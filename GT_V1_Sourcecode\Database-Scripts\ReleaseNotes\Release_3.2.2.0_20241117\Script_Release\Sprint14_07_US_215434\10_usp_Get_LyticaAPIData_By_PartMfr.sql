﻿
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


/* 
===========================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-208862]		Trung Pham			16-Jul-2024		Create			Manual Trigger: Refreshing Lytica API
[US-215434]		Phuc Hoang			06-Nov-2024		Update			Lytica Price should apply fuzzy logic for inserting & displaying
===========================================================================================
*/
CREATE OR ALTER  PROCEDURE [dbo].[usp_Get_LyticaAPIData_By_PartMfr]

    @partNo VARCHAR(256),
	@manufacturerCode VARCHAR(256),
    @manufacturerName VARCHAR(1024)

AS

BEGIN
	SELECT TOP 1 *
	FROM tbLyticaAPI
	WHERE OriginalPartSearched = @partNo 
		AND ISNULL(Inactive, 0) = 0
		AND (ISNULL(AveragePrice, 0) + ISNULL(TargetPrice, 0) + ISNULL(MarketLeading, 0)) > 0
		AND (
				Manufacturer = @manufacturerName 
				OR Manufacturer LIKE @manufacturerName + '%' 
				OR Manufacturer LIKE [dbo].[ufn_GetFirstWord](@manufacturerName) + '%'
			)
END

GO


