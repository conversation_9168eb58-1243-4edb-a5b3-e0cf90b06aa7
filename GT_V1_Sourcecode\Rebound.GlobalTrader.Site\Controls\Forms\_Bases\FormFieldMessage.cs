using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using System.Collections;

namespace Rebound.GlobalTrader.Site.Controls.Forms {

	/// <summary>
	/// Form field message
	/// </summary>
	public class FormFieldMessage : Message {
		public FormFieldMessage(MessageBox.MessageTypeList enmType, string strMessage) : base(enmType, strMessage) { }
	}

}