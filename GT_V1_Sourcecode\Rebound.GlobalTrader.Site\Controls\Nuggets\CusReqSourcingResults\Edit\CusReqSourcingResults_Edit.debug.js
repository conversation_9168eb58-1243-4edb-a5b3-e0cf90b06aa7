///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//Marker     Changed by      Date         Remarks
//[001]      Vinay           26/07/2012   Set the application to only accept qtys greater than 0:- ESMS Task-103
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.CusReqSourcingResults_Edit = function(element) { 
	Rebound.GlobalTrader.Site.Controls.Forms.CusReqSourcingResults_Edit.initializeBase(this, [element]);
	this._intCustomerRequirementID = -1;
	this._intSourcingResultID = -1;
	this._intSupplierId = -1;
	this._intCurrencyNo = -1;
	this._supplierAdvisoryNotes = "";
	this._mfrAdvisoryNotes = "";
};

Rebound.GlobalTrader.Site.Controls.Forms.CusReqSourcingResults_Edit.prototype = {

	get_intCustomerRequirementID: function() { return this._intCustomerRequirementID; }, 	set_intCustomerRequirementID: function(value) { if (this._intCustomerRequirementID !== value)  this._intCustomerRequirementID = value; }, 

	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.Forms.CusReqSourcingResults_Edit.callBaseMethod(this, "initialize");
		this.addShown(Function.createDelegate(this, this.formShown));
	},

	formShown: function() {
		if (this._blnFirstTimeShown) {
			this.addSave(Function.createDelegate(this, this.saveClicked));
		}
		this._intSupplierId = -1;
        this.showField("ctlPartWatchMatch", false);
		this.setFormFieldsToDefaults();
		this.getData();
		if ($find(this.getField("ctlSupplier").ControlID)) $find(this.getField("ctlSupplier").ControlID)._aut.addSelectionMadeEvent(Function.createDelegate(this, this.supplierChangeEvent));
		if ($find(this.getField("ctlManufacturer").ControlID)) $find(this.getField("ctlManufacturer").ControlID)._aut.addSelectionMadeEvent(Function.createDelegate(this, this.getMfrNotes));

		//if (this._supplierAdvisoryNotes) {
		//	$('#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlEdit_ctlDB_ctlSupplier_ctl03_aut_ctl03').parent().find('.advisory-notes').remove();
		//	$('#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlEdit_ctlDB_ctlSupplier_ctl03_aut_ctl03').append($R_FN.createAdvisoryNotesIcon(this._supplierAdvisoryNotes, 'margin-left-10'));
		//}
		//if (this._mfrAdvisoryNotes) {
		//	$('#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlEdit_ctlDB_ctlManufacturer_ctl03_aut_ctl03').parent().find('.advisory-notes').remove();
		//	$('#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlEdit_ctlDB_ctlManufacturer_ctl03_aut_ctl03').append($R_FN.createAdvisoryNotesIcon(this._mfrAdvisoryNotes, 'margin-left-10'));
		//}

		if ($find(this.getField("ctlManufacturer").ControlID)) $find(this.getField("ctlManufacturer").ControlID)._aut.addSelectionMadeEvent(Function.createDelegate(this, this.getMfrNotes));
		if ($find(this.getField("ctlSupplier").ControlID)) $find(this.getField("ctlSupplier").ControlID)._aut.addSelectionMadeEvent(Function.createDelegate(this, this.getSupplierNotes));

	},

	dispose: function() { 
		if (this.isDisposed) return;
		this._intCustomerRequirementID = null;
		this._intSourcingResultID = null;
		this._supplierAdvisoryNotes = null;
		this._mfrAdvisoryNotes = null;
		Rebound.GlobalTrader.Site.Controls.Forms.CusReqSourcingResults_Edit.callBaseMethod(this, "dispose");
	},
	
	getData: function() {
		this.showLoading(true);
		var obj = new Rebound.GlobalTrader.Site.Data();
		obj.set_PathToData("controls/Nuggets/CusReqSourcingResults");
		obj.set_DataObject("CusReqSourcingResults");
		obj.set_DataAction("GetItem");
		obj.addParameter("id", this._intSourcingResultID);
		obj.addDataOK(Function.createDelegate(this, this.getDataComplete));
		obj.addError(Function.createDelegate(this, this.finishShowForm));
		obj.addTimeout(Function.createDelegate(this, this.finishShowForm));
		$R_DQ.addToQueue(obj);
		$R_DQ.processQueue();
		obj = null;
	},

    getDataComplete: function (args) {
        
		var res = args._result;
		this.setFieldValue("ctlPartNo", $R_FN.setCleanTextValue(res.Part));
		this.setFieldValue("ctlDateCode", $R_FN.setCleanTextValue(res.DateCode));
		this.setFieldValue("ctlQuantity", res.Quantity);
		this.setFieldValue("ctlPrice", res.Price);
		//this.setFieldValue("ctlCurrency", res.CurrencyNo);
		//this.setFieldValue("ctlPackage", res.PackageNo);
        this.setFieldValue("ctlPackage", res.PackageNo, null, res.PackageDescription);
		this.setFieldValue("ctlProduct", res.ProductNo, null, res.ProductDescription);
		this.setFieldValue("ctlManufacturer", res.MfrNo, null, $R_FN.setCleanTextValue(res.Mfr));
		this.setFieldValue("ctlOfferStatus", res.OfferStatus);
		this.setFieldValue("ctlROHS", res.ROHS);
		this.setFieldValue("ctlNotes", res.Notes);
		this.setFieldValue("ctlSupplier", res.SupplierNo, null, $R_FN.setCleanTextValue(res.Supplier));
        this._intCurrencyNo = res.CurrencyNo;
        if (res.PartWatchMatch == true) {
            this.showField("ctlPartWatchMatch", true);
            this.setFieldValue("ctlPartWatchMatch", res.PartWatchMatch);
        }
        else {
            this.showField("ctlPartWatchMatch", false);
        }
           
		this.finishShowForm();
		this.setFieldValue("ctlMSL", res.MSLLevelNo);
	},
	
	finishShowForm: function() {
		this.showLoading(false);
		this.showInnerContent(true);
		this.getFieldDropDownData("ctlSupplier");
        this.getFieldDropDownData("ctlROHS");

		//this.getFieldDropDownData("ctlCurrency");
		//this.getFieldDropDownData("ctlPackage");
		//this.getFieldDropDownData("ctlProduct");
		this.getFieldDropDownData("ctlOfferStatus");
		this.getFieldDropDownData("ctlMSL");
	},

	saveClicked: function() {
		if (!this.validateForm()) return;
		var obj = new Rebound.GlobalTrader.Site.Data();
		obj.set_PathToData("controls/Nuggets/CusReqSourcingResults");
		obj.set_DataObject("CusReqSourcingResults");
		obj.set_DataAction("SaveEdit");
		obj.addParameter("id", this._intSourcingResultID);
		obj.addParameter("Quantity", this.getFieldValue("ctlQuantity"));
		obj.addParameter("Part", this.getFieldValue("ctlPartNo"));
		obj.addParameter("Manufacturer", this.getFieldValue("ctlManufacturer"));
		obj.addParameter("DateCode", this.getFieldValue("ctlDateCode"));
		obj.addParameter("Product", this.getFieldValue("ctlProduct"));
		obj.addParameter("Package", this.getFieldValue("ctlPackage"));
		obj.addParameter("Price", this.getFieldValue("ctlPrice"));
		obj.addParameter("Currency", this.getFieldValue("ctlCurrency"));
		obj.addParameter("Supplier", this.getFieldValue("ctlSupplier"));
		obj.addParameter("ROHS", this.getFieldValue("ctlROHS"));
		obj.addParameter("OfferStatus", this.getFieldValue("ctlOfferStatus"));
		obj.addParameter("Notes", this.getFieldValue("ctlNotes"));
        obj.addParameter("MSL", this.getFieldValue("ctlMSL"));
        obj.addParameter("PartWatchMatch", this.getFieldValue("ctlPartWatchMatch"));
		obj.addDataOK(Function.createDelegate(this, this.saveEditComplete));
		obj.addError(Function.createDelegate(this, this.saveEditError));
		obj.addTimeout(Function.createDelegate(this, this.saveEditError));
		$R_DQ.addToQueue(obj);
		$R_DQ.processQueue();
		obj = null;
	},

	saveEditError: function(args) {
		this._strErrorMessage = args._errorMessage;
		this.onSaveError();
	},
	
	saveEditComplete: function(args) {
		if (args._result.Result == true) {
			this.onSaveComplete();
		} else {
			this._strErrorMessage = args._errorMessage;
			this.onSaveError();
		}
	},	

	validateForm: function() {
		this.onValidate();
		var blnOK = this.autoValidateFields();
		return blnOK;
	},
	supplierChangeEvent: function () {
	    if (this._intSupplierId != this.getFieldValue("ctlSupplier")) {
	        this._intSupplierId = this.getFieldValue("ctlSupplier");
	        this.getPurchaseData();
	    }
	},
	getPurchaseData: function () {
	    // this.getData_Start();
	    var obj = new Rebound.GlobalTrader.Site.Data();
	    this._strPath = "controls/Nuggets/PurchaseRequestLineDetail";
	    this._strData = "PurchaseRequestLineDetail";
	    obj.set_PathToData(this._strPath);
	    obj.set_DataObject(this._strData);
	    obj.set_DataAction("GetDefaultPurchasingInfo");
	    obj.addParameter("id", this.getFieldValue("ctlSupplier"));
	    obj.addDataOK(Function.createDelegate(this, this.getPurchaseDataOK));
	    obj.addError(Function.createDelegate(this, this.getPurchaseDataError));
	    obj.addTimeout(Function.createDelegate(this, this.getPurchaseDataError));
	    $R_DQ.addToQueue(obj);
	    $R_DQ.processQueue();
	    obj = null;
	},
	getPurchaseDataOK: function (args) {
	    var res = args._result;
	    this._intGlobalCurrencyNo = res.GlobalCurrencyNo;
	    this._intPOCurrencyNo = res.CurrencyNo;
		$('#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlEdit_ctlDB_ctlSupplier_ctl04_aut_ctl03').parent().find('.advisory-notes').remove();
		$('#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlEdit_ctlDB_ctlSupplier_ctl04_aut_ctl03').append($R_FN.createAdvisoryNotesIcon(res.CompanyAdvisoryNotes, 'margin-left-10'))
	    this.bindCurrency();
	   
	},
	getPurchaseDataError: function (args) {
	    this.showError(true, args.get_ErrorMessage());
	},

	bindCurrency: function () {
	 
	    this.getFieldControl("ctlCurrency")._intGlobalCurrencyNo = this._intGlobalCurrencyNo;
	    this.getFieldDropDownData("ctlCurrency");
	   
	    this.setFieldValue("ctlCurrency", this._intCurrencyNo);
	},
	getMfrNotes: function () {
		var obj = new Rebound.GlobalTrader.Site.Data();
		this._strPath = "controls/Nuggets/ManufacturerMainInfo";
		this._strData = "ManufacturerMainInfo";
		obj.set_PathToData(this._strPath);
		obj.set_DataObject(this._strData);
		obj.set_DataAction("GetAdvisoryNotes");
		obj.addParameter("ID", this.getFieldValue("ctlManufacturer"));
		obj.addDataOK(Function.createDelegate(this, this.getMfrNotesOK));
		obj.addError(Function.createDelegate(this, this.getMfrNotesError));
		obj.addTimeout(Function.createDelegate(this, this.getMfrNotesError));
		$R_DQ.addToQueue(obj);
		$R_DQ.processQueue();
		obj = null;
	},
	getMfrNotesOK: function (args) {
		var res = args._result;
		var selectedMfrLabel = $find(this.getField("ctlManufacturer").ControlID)._aut._lblSelectedValue;
		$(selectedMfrLabel).parent().find('.advisory-notes').remove();
		$(selectedMfrLabel).append($R_FN.createAdvisoryNotesIcon(res.MfrAdvisoryNotes, 'margin-left-10'));
	},
	getMfrNotesError: function (args) {
		this.showError(true, args.get_ErrorMessage());
	},

	getSupplierNotes: function () {
		var obj = new Rebound.GlobalTrader.Site.Data();
		this._strPath = "controls/Nuggets/CompanyMainInfo";
		this._strData = "CompanyMainInfo";
		obj.set_PathToData(this._strPath);
		obj.set_DataObject(this._strData);
		obj.set_DataAction("GetAdvisoryNotes");
		obj.addParameter("ID", this.getFieldValue("ctlSupplier"));
		obj.addDataOK(Function.createDelegate(this, this.getSupplierNotesOK));
		obj.addError(Function.createDelegate(this, this.getSupplierNotesError));
		obj.addTimeout(Function.createDelegate(this, this.getSupplierNotesError));
		$R_DQ.addToQueue(obj);
		$R_DQ.processQueue();
		obj = null;
	},
	getSupplierNotesOK: function (args) {
		var res = args._result;
		var selectedSupplierLabel = $find(this.getField("ctlSupplier").ControlID)._aut._lblSelectedValue;
		$(selectedSupplierLabel).parent().find('.advisory-notes').remove();
		$(selectedSupplierLabel).append($R_FN.createAdvisoryNotesIcon(res.AdvisoryNotes, 'margin-left-10'));
	},
	getSupplierNotesError: function (args) {
		this.showError(true, args.get_ErrorMessage());
	}
};

Rebound.GlobalTrader.Site.Controls.Forms.CusReqSourcingResults_Edit.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.CusReqSourcingResults_Edit", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
