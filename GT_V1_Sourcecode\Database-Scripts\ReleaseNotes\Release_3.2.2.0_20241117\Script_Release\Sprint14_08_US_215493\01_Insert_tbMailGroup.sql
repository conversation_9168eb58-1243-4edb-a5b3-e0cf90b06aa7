﻿/*  
===========================================================================================  
TASK         UPDATED BY       DATE          ACTION    DESCRIPTION  
[US-215493]   Cuong DoX   6-Nov-2024  Create   Add mail group for all client  
===========================================================================================  
*/ 
IF NOT EXISTS (SELECT 1 FROM tbMailGroup WHERE Name = 'Finance Team')
BEGIN
INSERT INTO tbMailGroup (
    Name,
    UpdatedBy,
    DLUP,
    ClientNo,
    LoginNo,
    RefID109,
    RefLoginID109,
    RefIdHK,
    RefLoginIDHK,
    GroupDiscription
)
SELECT 
    'Finance Team' AS Name,           
    0 AS UpdatedBy,             
    GETDATE() AS DLUP,                
    c.ClientId AS ClientNo,            
    NULL AS LoginNo,                    
    NULL AS RefID109,                   
    NULL AS RefLoginID109,              
    NULL AS RefIdHK,                    
    NULL AS RefLoginIDHK,               
    'Notify when Supplier Invoice values total discrepancy' AS GroupDiscription 
FROM 
    tbClient c
WHERE 
    c.Inactive = 0;
END