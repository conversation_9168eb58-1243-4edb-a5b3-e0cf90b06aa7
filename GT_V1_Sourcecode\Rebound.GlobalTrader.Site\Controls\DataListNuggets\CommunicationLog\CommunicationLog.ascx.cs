using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site.Controls;

namespace Rebound.GlobalTrader.Site.Controls.DataListNuggets {
	public partial class CommunicationLog : Base {

		private IconButton _ibtnAdd;
		private IconButton _ibtnEdit;

		public bool ForContact { get; set; }
		public int CompanyID { get; set; }
		public int ContactID { get; set; }
		public CompanyListType CompanyListType { get; set; }

		private bool _blnCanAdd = true;
		public bool CanAdd {
			get { return _blnCanAdd; }
			set { _blnCanAdd = value; }
		}

		private bool _blnCanEdit = true;
		public bool CanEdit {
			get { return _blnCanEdit; }
			set { _blnCanEdit = value; }
		}

		#region Overrides

		/// <summary>
		/// OnInit
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
			SetDataListNuggetType("CommunicationLog");
			base.OnInit(e);
			WireUpControls();
			TitleText = Functions.GetGlobalResource("Nuggets", "CommunicationLog");
			AddScriptReference("Controls.DataListNuggets.CommunicationLog.CommunicationLog.js");
			SetupTable();
		}

		protected override void OnLoad(EventArgs e) {
			if (CompanyID < 1) CompanyID = _objQSManager.CompanyID;
			if (ContactID < 1) ContactID = _objQSManager.ContactID;
			SetupScriptDescriptors();
			base.OnLoad(e);
		}

		protected override void OnPreRender(EventArgs e) {
			ResetAllState();
			_ibtnAdd.Visible = _blnCanAdd;
			_ibtnEdit.Visible = _blnCanEdit;
            ((DropDowns.CommunicationLogType)this._ctlFilter.FindFieldControl_DropDown("ctlType").DropDownControl).IncludeNewSystemDocuments = true;
			base.OnPreRender(e);
		}

		#endregion

		private void SetupTable() {
			EnsureChildControls();
			_tbl.AllowSelection = true;
			_tbl.SortColumnDirection = Rebound.GlobalTrader.Site.Enumerations.SortColumnDirection.DESC;
			_tbl.PageSize = 10;
			_tbl.Columns.Add(new FlexiDataColumn("Date", "EnteredBy", WidthManager.GetWidth(WidthManager.ColumnWidth.DateAndTime), true));
			_tbl.Columns.Add(new FlexiDataColumn("ContactLogType", "Document", WidthManager.GetWidth(WidthManager.ColumnWidth.ListName), true));
			_tbl.Columns.Add(new FlexiDataColumn("ContactLogContactName", WidthManager.GetWidth(WidthManager.ColumnWidth.ContactName), true));
			_tbl.Columns.Add(new FlexiDataColumn("ContactLogDetails", Unit.Empty, true));
		}

		private void SetupScriptDescriptors() {
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.DataListNuggets.CommunicationLog", ctlDesignBase.ClientID);
			_scScriptControlDescriptor.AddProperty("intCompanyID", CompanyID);
			_scScriptControlDescriptor.AddProperty("intContactID", ContactID);
			_scScriptControlDescriptor.AddProperty("blnForContact", ForContact);
			_scScriptControlDescriptor.AddProperty("enmCompanyListType", CompanyListType);
			if (_blnCanAdd) _scScriptControlDescriptor.AddElementProperty("ibtnAdd", _ibtnAdd.ClientID);
			if (_blnCanEdit) _scScriptControlDescriptor.AddElementProperty("ibtnEdit", _ibtnEdit.ClientID);
		}

		private void WireUpControls() {
			_ibtnAdd = (IconButton)FindIconButton("ibtnAdd");
			_ibtnEdit = (IconButton)FindIconButton("ibtnEdit");
		}

	}
}