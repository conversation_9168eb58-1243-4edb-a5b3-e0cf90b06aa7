<%--
Marker     Changed by      Date         Remarks
[001]      Vinay           29/05/2012   This need to implement Incoterms field is requird.
--%>
<%@ Control Language="C#" CodeBehind="CRMAMainInfo_Edit.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Forms.CRMAMainInfo_Edit" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>

<ReboundUI_Form:DesignBase ID="ctlDB" runat="server" ShowRequiredFieldsExplanation="true" ShowQuickHelp="false">
	<Links>
		<ReboundUI:IconButton ID="ibtnSave" runat="server" IconGroup="Nugget" IconTitleResource="Save" IconCSSType="Save" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
		<ReboundUI:IconButton ID="ibtnCancel" runat="server" IconGroup="Nugget" IconTitleResource="Cancel" IconCSSType="Cancel" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
	</Links>

	<Explanation><%=Functions.GetGlobalResource("FormExplanations", "CRMAMainInfo_Edit")%></Explanation>

	<Content>
		<ReboundUI_Table:Form id="frm" runat="server">
			<ReboundUI_Form:FormField id="ctlCustomer" runat="server" FieldID="lblCustomer" ResourceTitle="Customer">
				<Field><asp:Label ID="lblCustomer" runat="server" /></Field>
			</ReboundUI_Form:FormField>
		
			<ReboundUI_Form:FormField id="ctlContact" runat="server" FieldID="lblContact" ResourceTitle="Contact">
				<Field><asp:Label ID="lblContact" runat="server" /></Field>
			</ReboundUI_Form:FormField>
		
			<ReboundUI_Form:FormField id="ctlDivision" runat="server" FieldID="ddlDivision" ResourceTitle="Division" IsRequiredField="true">
				<Field><ReboundDropDown:Division ID="ddlDivision" runat="server" /></Field>
			</ReboundUI_Form:FormField>
		
			<ReboundUI_Form:FormField id="ctlWarehouse" runat="server" FieldID="ddlWarehouse" ResourceTitle="Warehouse" IsRequiredField="true">
				<Field><ReboundDropDown:Warehouse ID="ddlWarehouse" runat="server" IncludeVirtual="false" /></Field>
			</ReboundUI_Form:FormField>
			
			<ReboundUI_Form:FormField id="ctlWarehouse_Label" runat="server" FieldID="lblWarehouse" ResourceTitle="Warehouse" DisplayRequiredFieldMarkerOnly="true">
				<Field><asp:Label ID="lblWarehouse" runat="server" /></Field>
			</ReboundUI_Form:FormField>
		
			<ReboundUI_Form:FormField id="ctlAuthorisedBy" runat="server" FieldID="ddlAuthorisedBy" ResourceTitle="AuthorisedBy" IsRequiredField="true">
				<Field><ReboundDropDown:Employee ID="ddlAuthorisedBy" runat="server" /></Field>
			</ReboundUI_Form:FormField>
			
			<ReboundUI_Form:FormField id="ctlRaisedByLbl" runat="server" FieldID="lblRaisedBy" ResourceTitle="AuthorisedBy" DisplayRequiredFieldMarkerOnly="true"  >
				<Field><asp:Label ID="lblRaisedBy" runat="server" /></Field>
			</ReboundUI_Form:FormField>

            <ReboundUI_Form:FormField id="ctlRMADate" runat="server" FieldID="txtRMADate" ResourceTitle="RMADate" IsRequiredField="true">
	            <Field>
		            <ReboundUI:ReboundTextBox ID="txtRMADate" runat="server" Width="150" />
		            <ReboundUI:Calendar ID="calRMADate" runat="server" RelatedTextBoxID="txtRMADate" />
	            </Field>
            </ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlInvoice" runat="server" FieldID="lblInvoice" ResourceTitle="InvoiceNo">
				<Field><asp:Label ID="lblInvoice" runat="server" /></Field>
			</ReboundUI_Form:FormField>
		
			<ReboundUI_Form:FormField id="ctlSalesOrder" runat="server" FieldID="lblSalesOrder" ResourceTitle="SalesOrderNo">
				<Field><asp:Label ID="lblSalesOrder" runat="server" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlShipVia" runat="server" FieldID="ddlShipVia" ResourceTitle="ShipVia" IsRequiredField="true">
				<Field><ReboundDropDown:SellShipMethod ID="ddlShipVia" runat="server" /></Field>
			</ReboundUI_Form:FormField>
			
			<ReboundUI_Form:FormField id="ctlShipViaLbl" runat="server" FieldID="lblShipVia" ResourceTitle="ShipVia" DisplayRequiredFieldMarkerOnly="true" >
				<Field><asp:Label ID="lblShipVia" runat="server" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlShippingAccount" runat="server" FieldID="txtShippingAccount" ResourceTitle="ShippingAccountNo">
				<Field><ReboundUI:ReboundTextBox ID="txtShippingAccount" runat="server" Width="200" /></Field>
			</ReboundUI_Form:FormField>

			  <%--[001] code start--%>
			<ReboundUI_Form:FormField id="ctlIncoterm" runat="server" FieldID="ddlIncoterm" ResourceTitle="Incoterm" IsRequiredField="true">
				<Field><ReboundDropDown:Incoterm ID="ddlIncoterm" runat="server" /></Field>
			</ReboundUI_Form:FormField>
			<%--[001] code end--%>
			
			<ReboundUI_Form:FormField id="ctlInstructions" runat="server" FieldID="txtInstructions" ResourceTitle="Instructions">
				<Field><ReboundUI:ReboundTextBox ID="txtInstructions" runat="server" Width="400" Rows="2" TextMode="multiLine" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlNotes" runat="server" FieldID="txtNotes" ResourceTitle="CustomerNotes">
				<Field><ReboundUI:ReboundTextBox ID="txtNotes" runat="server" Width="400" Rows="2" TextMode="multiLine" /></Field>
			</ReboundUI_Form:FormField>

             <ReboundUI_Form:FormField id="ctlCustomerRejectionNo" runat="server" FieldID="txtCRNo" ResourceTitle="CustomerRejectionNo">
				<Field><ReboundUI:ReboundTextBox ID="txtCRNo" MaxLength="20" runat="server" Width="150"   /></Field>
			</ReboundUI_Form:FormField>
		</ReboundUI_Table:Form>

	</Content>
	
</ReboundUI_Form:DesignBase>
