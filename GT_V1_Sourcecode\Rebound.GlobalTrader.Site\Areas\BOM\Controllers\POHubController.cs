﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Mvc;
using Rebound.GlobalTrader.BLL;
using System.Text;
using System.Data;
using System.Globalization;
using Rebound.GlobalTrader.BLL.BusinessLogic;
using Rebound.GlobalTrader.DAL.Common.Entities;
using Rebound.GlobalTrader.Site.Code.Common;
using Rebound.GlobalTrader.DAL;
using Rebound.GlobalTrader.Site.Areas.BOM.Models;
using Newtonsoft.Json;

namespace Rebound.GlobalTrader.Site.Areas.BOM.Controllers
{
    public class POHubController : Controller
    {
        // GET: BOM/POHub
        public ActionResult BOMManagerOrderPOHub(int BOM)
        {
            return View();
        }

        [HttpPost]
        public ActionResult GetBOMItem(int? BOMManagerId, int? ClientId, string partSearch)
        {
            int curPage = 1;
            if (Request["pq_curpage"] != null)
            {
                curPage = Convert.ToInt32(Request["pq_curpage"].ToString());
            }
            int Rpp = 5;
            if (Request["pq_rpp"] != null)
            {
                Rpp = Convert.ToInt32(Request["pq_rpp"].ToString());
            }
            try
            {
                int? cid = SessionManager.ClientID;
                List<BOMManagerContract> BOMList = BOMManagerContract.GetBOMListForCustomerRequirement(BOMManagerId, ClientId, curPage, Rpp, partSearch);
                if (BOMList.Count > 0)
                {
                    BOMList[0].curpage = curPage;
                }

                return Json(BOMList, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                new Errorlog().LogMessage("Inside POHubController class, Method name : GetBOMItem. Exception details:" + (ex.InnerException == null ? ex.Message : ex.InnerException.Message));
                return Json(null, JsonRequestBehavior.AllowGet);
            }
        }
        [HttpPost]
        public ActionResult GetUserMatchData(string CrossMatchType, int MatchFirstValue, bool ChkMatchBase, bool ChkMatchFirst, int BOMManagerID, int? CustomerRequirementID)
        {
            int curPage = 1;
            if (Request["pq_curpage"] != null)
            {
                curPage = Convert.ToInt32(Request["pq_curpage"].ToString());
            }
            int Rpp = 5;
            if (Request["pq_rpp"] != null)
            {
                Rpp = Convert.ToInt32(Request["pq_rpp"].ToString());
            }
            try
            {
                DateTime enddate = DateTime.Now.Date;
                DateTime startdate = DateTime.Now.AddMonths(-6);
                string MatchType = string.Empty;
                if (CrossMatchType == "rdInvoiceXMatch")
                {
                    MatchType = "InvoiceXMatch";
                    if (ChkMatchBase)
                        MatchType = "InvoiceXMatchBase";
                    else if (ChkMatchFirst)
                        MatchType = "InvoiceXMatchLike";
                }
                if (CrossMatchType == "rdRequirementsXMatch")
                {
                    MatchType = "ReqXMatch";
                    if (ChkMatchBase)
                        MatchType = "ReqXMatchBase";
                    else if (ChkMatchFirst)
                        MatchType = "ReqXMatchLike";
                }
                if (CrossMatchType == "rdQuoteXMatch")
                {
                    MatchType = "QuoteXMatch";
                    if (ChkMatchBase)
                        MatchType = "QuoteXMatchBase";
                    else if (ChkMatchFirst)
                        MatchType = "QuoteXMatchLike";
                }
                if (CrossMatchType == "rdOpenSOXMatch")
                {
                    MatchType = "OpenSOXMatch";
                    if (ChkMatchBase)
                        MatchType = "OpenSOXMatchBase";
                    else if (ChkMatchFirst)
                        MatchType = "OpenSOXMatchLike";
                }
                XMatch objXMatch = new XMatch();
                DataTable dtResult = objXMatch.GetXMatchMatchingDataBOMManager(startdate,
                                                                               enddate,
                                                                               MatchType,
                                                                               MatchFirstValue,
                                                                               SessionManager.LoginID ?? 0,
                                                                               SessionManager.ClientID ?? 0,
                                                                               BOMManagerID,
                                                                               CustomerRequirementID,
                                                                               curPage,
                                                                               Rpp);

                List<XMatchData> xMatchData = new List<XMatchData>();
                xMatchData = (from DataRow dr in dtResult.Rows
                              select new XMatchData()
                              {
                                  SalesXMatchID = Convert.ToInt32(dr["SalesXMatchID"]),
                                  BOMManagerNo = Convert.ToInt32(dr["BOMManagerNo"]),
                                  Part = dr["Part"].ToString(),
                                  InvoiceNumber = Convert.ToInt32(dr["InvoiceNumber"]),
                                  InvoiceSls = dr["InvoiceSls"].ToString(),
                                  InvoiceCustomer = dr["InvoiceCustomer"].ToString(),
                                  InvoiceDate = Convert.ToDateTime(dr["InvoiceDate"]),
                                  InvoiceQuantity = Convert.ToInt32(dr["InvoiceQuantity"]),
                                  UnitPrice = Convert.ToDecimal(dr["UnitPrice"]),
                                  POOrder = Convert.ToInt32(dr["POOrder"]),
                                  POCompany = dr["POCompany"].ToString(),
                                  POQuantity = Convert.ToInt32(dr["POQuantity"]),
                                  POPrice = Convert.ToDecimal(dr["POPrice"]),
                                  POCurrency = dr["POCurrency"].ToString(),
                                  CustomerRequirementId = Convert.ToInt32(dr["CustomerRequirementId"]),
                                  TotalCount = Convert.ToInt32(dr["TotalRecords"]),
                                  OfferAddFlag = Convert.ToBoolean(dr["OfferAddFlag"]),
                                  curpage = curPage
                              }
                              ).ToList();
                return Json(xMatchData, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                new Errorlog().LogMessage("Inside POHubController class, Method name : GetUserMatchData. Exception details:" + (ex.InnerException == null ? ex.Message : ex.InnerException.Message));
                return Json("Something went Wrong", JsonRequestBehavior.AllowGet);
            }
        }

        [HttpPost]
        public ActionResult IPOBOMAutoSource(string partSearch, int? BOM, int? customerRequirementId, int? CallType)
        {
            int curPage = Request["pq_curpage"] != null ? Convert.ToInt32(Request["pq_curpage"]) : 1;
            int Rpp = Request["pq_rpp"] != null ? Convert.ToInt32(Request["pq_rpp"]) : 5;
            bool IsServerLocal = true;
            DateTime? enddate = DateTime.Now.Date;
            DateTime? startdate = DateTime.Now.AddMonths(-6);
            DateTime? outDate;
            List<Offer> offerlist = new List<Offer>();

            try
            {
                offerlist = BOMManagerContract.IPOBOMAutoSource(
                    SessionManager.ClientID,
                    partSearch,
                    1,
                    startdate,
                    enddate,
                    out outDate,
                    IsServerLocal,
                    BOM,
                    CallType,
                    customerRequirementId,
                    curPage,
                    Rpp
                );

                if (offerlist.Count > 0)
                {
                    offerlist[0].curpage = curPage;
                }

                return Json(offerlist, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                string errorMessage = ex.InnerException == null ? ex.Message : ex.InnerException.Message;
                new Errorlog().LogMessage($"Inside POHubController class, Method name: IPOBOMAutoSource. Exception details: {errorMessage}");
                return Json(offerlist, JsonRequestBehavior.AllowGet);
            }
            finally
            {
                offerlist = null;
            }
        }

        [HttpGet]
        public ActionResult GetPackages(string search)
        {
            List<Package> lst = new List<Package>();

            try
            {
                lst = BLL.Package.AutoSearch(search, false);
                return Json(lst, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                new Errorlog().LogMessage("Inside POHubController class, Method name : GetPackages. Exception details:" + (ex.InnerException == null ? ex.Message : ex.InnerException.Message));
                return Json("Something went Wrong", JsonRequestBehavior.AllowGet);
            }
            finally
            {
                lst = null;
            }

        }
        [HttpPost]
        public ActionResult BOMManagerNewOffer(AutoSourcingModel offer)
        {
            int APIOfferID;
            try
            {
                APIOfferID = Offer.BOMManagerAPIOfferInsert(offer.Supplier,
                                                            offer.SupplierName,
                                                            offer.PartNo,
                                                            offer.ROHS,
                                                            offer.ManufacturerNo,
                                                            offer.ManufacturerName,
                                                            offer.DateCode,
                                                            offer.ProductNo,
                                                            offer.ProductName,
                                                            offer.PackageNo,
                                                            offer.PackageName,
                                                            offer.Quantity,
                                                            offer.Price,
                                                            offer.Currency,
                                                            offer.OfferStatus,
                                                            offer.SupplierTotalQSA,
                                                            offer.SupplierMOQ,
                                                            offer.SupplierLTB,
                                                            offer.MSLNo,
                                                            offer.SPQ?.ToString(),
                                                            offer.LeadTime,
                                                            offer.FactorySealed,
                                                            offer.ROHSStatus,
                                                            offer.Notes,
                                                            offer.BOMManagerId,
                                                            (int)SessionManager.ClientID,
                                                            offer.AlterCRNumber,
                                                            offer.SupplierWarranty,
                                                            offer.CountryOfOrigin,
                                                            offer.SellPrice,
                                                            offer.ShippingCost,
                                                            offer.Reason,
                                                            offer.Region,
                                                            offer.DeliveryDate,
                                                            offer.IsTestingRecommended);
                return Json(APIOfferID, JsonRequestBehavior.AllowGet);
            }

            catch (Exception ex)
            {
                new Errorlog().LogMessage("Inside POHubController class, Method name : BOMManagerNewOffer. Exception details:" + (ex.InnerException == null ? ex.Message : ex.InnerException.Message));
                return Json(0, JsonRequestBehavior.AllowGet);
            }
        }
        [HttpPost]
        public ActionResult GetAPIOffersData(int BOMManagerID, string Part, int? CustomerReqID, bool FirstLoad)
        {

            DataTable PartList;

            int curPage = 1;
            if (Request["pq_curpage"] != null)
            {
                curPage = Convert.ToInt32(Request["pq_curpage"].ToString());
            }
            int Rpp = 5;
            if (Request["pq_rpp"] != null)
            {
                Rpp = Convert.ToInt32(Request["pq_rpp"].ToString());
            }
            try
            {
                List<APIExternalLinksDetails> APIOffers = new List<APIExternalLinksDetails>();
                APIOffers = Offer.APIOffersBOMManager(BOMManagerID,
                                                      out PartList,
                                                      Part,
                                                      CustomerReqID,
                                                      curPage,
                                                      Rpp);


                if (FirstLoad)
                {
                    string PartArray = string.Empty;
                    for (int i = 0; i < PartList.Rows.Count; i++)
                    {
                        PartArray += '"' + PartList.Rows[i]["Part"].ToString() + '"';
                        PartArray += (i < PartList.Rows.Count - 1) ? "," : string.Empty;
                    }
                    List<APIExternalLinks> NewAPIOffers = new List<APIExternalLinks>();
                    NewAPIOffers.AddRange(APIIntegration.APIOfferIntegration(PartArray.TrimEnd(','), true, true));
                }


                if (APIOffers.Count > 0)
                {
                    APIOffers[0].curpage = curPage;
                }
                return Json(APIOffers, JsonRequestBehavior.AllowGet);

            }
            catch (Exception ex)
            {
                new Errorlog().LogMessage("Inside POHubController class, Method name : GetAPIOffersData. Exception details:" + (ex.InnerException == null ? ex.Message : ex.InnerException.Message));
                return Json("Something went Wrong", JsonRequestBehavior.AllowGet);
            }
        }
        public static int GlobalCounter = 0;
        [HttpPost]

        public ActionResult GetLyticaAPIData(int BOMManagerID, string Parts, int? CustomerReqID, bool FirstLoad)
        {

            DataTable PartList;

            int curPage = 1;
            if (Request["pq_curpage"] != null)
            {
                curPage = Convert.ToInt32(Request["pq_curpage"].ToString());
            }
            int Rpp = 5;
            if (Request["pq_rpp"] != null)
            {
                Rpp = Convert.ToInt32(Request["pq_rpp"].ToString());
            }
            try
            {

                DataTable dtPart = new DataTable();
                dtPart.Clear();
                dtPart.Columns.Add("Part");
                List<LyticaAPI> APIOffers = new List<LyticaAPI>();
                APIOffers = Offer.GetLyticaAPIData(BOMManagerID,
                                                      CustomerReqID,
                                                      Parts,
                                                      out dtPart,
                                                      curPage,
                                                      Rpp);

                string PartArray = string.Empty;
                string partJson = string.Empty;
                if (dtPart.Rows.Count > 0)
                {
                    PartArray += '"';
                    for (int i = 0; i < dtPart.Rows.Count; i++)
                    {
                        PartArray += dtPart.Rows[i]["Part"].ToString() + '|';
                        PartArray += (i < dtPart.Rows.Count - 1) ? "" : string.Empty;
                    }
                    PartArray += '"';
                    partJson = @"{""origMPN"": " + PartArray + "}";
                }
                if (partJson != "")
                {
                    APIIntegration.LyticaApiCall(partJson, SessionManager.LoginID);
                    APIOffers = Offer.GetLyticaAPIData(BOMManagerID,
                                                          CustomerReqID,
                                                          Parts,
                                                          out dtPart,
                                                          curPage,
                                                          Rpp);
                }
                return Json(APIOffers, JsonRequestBehavior.AllowGet);

            }
            catch (Exception ex)
            {
                new Errorlog().LogMessage("Inside POHubController class, Method name : GetLyticaAPIData. Exception details:" + (ex.InnerException == null ? ex.Message : ex.InnerException.Message));
                return Json("Something went Wrong", JsonRequestBehavior.AllowGet);
            }
        }
        [HttpPost]
        public ActionResult GetLyticaAPIAlternateData(string Parts)
        {

            //DataTable PartList;

            int curPage = 1;
            if (Request["pq_curpage"] != null)
            {
                curPage = Convert.ToInt32(Request["pq_curpage"].ToString());
            }
            int Rpp = 5;
            if (Request["pq_rpp"] != null)
            {
                Rpp = Convert.ToInt32(Request["pq_rpp"].ToString());
            }
            try
            {
                DataSet ds = Offer.GetLyticaAPIAlternateData(Parts,
                                                       curPage,
                                                       Rpp);
                LyticaAPI lyticaApi = new LyticaAPI();
                foreach (DataRow dr in ds.Tables[0].Rows)
                {
                    lyticaApi.Commodity = dr["Commodity"].ToString();
                    lyticaApi.OriginalPartSearched = dr["OriginalPartSearched"].ToString();
                    lyticaApi.Manufacturer = dr["Manufacturer"].ToString();
                    lyticaApi.AveragePrice = Convert.ToDouble(dr["AveragePrice"].ToString());
                    lyticaApi.TargetPrice = Convert.ToDouble(dr["TargetPrice"].ToString());
                    lyticaApi.MarketLeading = Convert.ToDouble(dr["MarketLeading"].ToString());
                    lyticaApi.LifeCycle = dr["LifeCycle"].ToString();
                    lyticaApi.lifeCycleStatus = dr["lifeCycleStatus"].ToString();
                    lyticaApi.OverAllRisk = dr["OverAllRisk"].ToString();
                    lyticaApi.PartBreadth = dr["PartBreadth"].ToString();
                    lyticaApi.ManufacturerBreadth = dr["ManufacturerBreadth"].ToString();
                    lyticaApi.DueDiligence = dr["DueDiligence"].ToString();
                    lyticaApi.PartConcentration = dr["PartConcentration"].ToString();
                    lyticaApi.OriginalEntryDate = Convert.ToDateTime(dr["OriginalEntryDate"].ToString());
                }

                List<LyticaAPIAlternatePart> lyticaApiAlternatePartDetail = new List<LyticaAPIAlternatePart>();
                foreach (DataRow dr in ds.Tables[1].Rows)
                {
                    LyticaAPIAlternatePart lyticaApiAlternatePart = new LyticaAPIAlternatePart();
                    lyticaApiAlternatePart.Manufacturer = dr["Manufacturer"].ToString();
                    lyticaApiAlternatePart.Part = dr["Part"].ToString();
                    lyticaApiAlternatePart.lifeCycleStatus = dr["lifeCycleStatus"].ToString();
                    lyticaApiAlternatePartDetail.Add(lyticaApiAlternatePart);
                }

                LyticalAlternatepartlist LyticaAPIList = new LyticalAlternatepartlist();
                LyticaAPIList.LyticaApiData = lyticaApi;
                LyticaAPIList.LyticaAPIAlternatePartData = lyticaApiAlternatePartDetail;


                return Json(LyticaAPIList, JsonRequestBehavior.AllowGet);


            }
            catch (Exception ex)
            {
                new Errorlog().LogMessage("Inside POHubController class, Method name : GetLyticaAPIAlternateData. Exception details:" + (ex.InnerException == null ? ex.Message : ex.InnerException.Message));
                return Json("Something went Wrong", JsonRequestBehavior.AllowGet);
            }
        }

        [HttpPost]
        public ActionResult RefreshLyticaAPIAfter3Days(int BOMManagerID, string Parts, string MfrCode, int? CustomerReqID, int MfrNo)
        {
            Parts = Functions.BeautifyPartNumber(Parts);
            int curPage = 1;
            if (Request["pq_curpage"] != null)
            {
                curPage = Convert.ToInt32(Request["pq_curpage"].ToString());
            }
            int Rpp = 5;
            if (Request["pq_rpp"] != null)
            {
                Rpp = Convert.ToInt32(Request["pq_rpp"].ToString());
            }
            try
            {
                DataTable dtPart = new DataTable();
                dtPart.Clear();
                dtPart.Columns.Add("Part");
                List<LyticaAPI> APIOffers = new List<LyticaAPI>();
                bool isUpdated = CustomerRequirement.RefreshLyticaDataAfter3days(Parts, MfrCode, MfrNo);
                if (isUpdated)
                {
                    Parts = '"' + Parts + '"';
                    List<LyticaAPI> LyticaData = new List<LyticaAPI>();
                    string partJson = @"{""origMPN"": " + Parts + "}";
                    LyticaData = APIIntegration.GetLyticaDataFromApi(partJson);
                    if (LyticaData.Count > 0)
                    {
                        var jsonData = JsonConvert.SerializeObject(LyticaData);
                        CustomerRequirement.UpsertLyticaAPI(jsonData, SessionManager.LoginID);
                    }
                }
                APIOffers = Offer.GetLyticaAPIData(BOMManagerID,
                                                      CustomerReqID,
                                                      Parts,
                                                      out dtPart,
                                                      curPage,
                                                      Rpp);
                return Json(APIOffers, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                new Errorlog().LogMessage("Inside POHubController class, Method name : RefreshLyticaAPIAfter3Days. Exception details:" + (ex.InnerException == null ? ex.Message : ex.InnerException.Message));
                return Json("Something went Wrong", JsonRequestBehavior.AllowGet);
            }
        }

        [HttpPost]
        public ActionResult GetCurrencyBOMManager(System.Int32 BOM)
        {

            try
            {
                JsonObject jsn = new JsonObject();
                JsonObject jsnList = new JsonObject(true);
                List<BLL.Currency> lst = BLL.Currency.DropDownForClientBOMManager(SessionManager.ClientID, BOM);
                for (int i = 0; i < lst.Count; i++)
                {
                    //CrossMatch.aspx     
                    JsonObject jsnItem = new JsonObject();
                    jsnItem.AddVariableNew("ID", lst[i].CurrencyId);
                    jsnItem.AddVariableNew("Name", String.Format("{1} - {0}", lst[i].CurrencyDescription, lst[i].CurrencyCode));
                    jsnItem.AddVariableNew("Code", lst[i].CurrencyCode);
                    jsnList.AddVariableNew(jsnItem);
                    jsnItem.Dispose(); jsnItem = null;
                }
                //lst.Clear(); lst = null;
                //jsn.AddVariableNew(jsnList);
                return Json(lst, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                new Errorlog().LogMessage("Inside POHubController class, Method name : GetCurrencyBOMManager. Exception details:" + (ex.InnerException == null ? ex.Message : ex.InnerException.Message));
                return Json("Something went Wrong", JsonRequestBehavior.AllowGet);
            }


        }
        [HttpPost]
        public ActionResult GetSupplierCurrencyDetails(System.Int32 SupplierId)
        {

            try
            {
                JsonObject jsn = new JsonObject();
                JsonObject jsnList = new JsonObject(true);
                Company cm = Company.GetDefaultPurchasingInfo(SupplierId);
                var currencylist = new
                {
                    GlobalCurrencyNo = cm.GlobalCurrencyNo,
                    CurrencyNo = cm.POCurrencyNo,
                    Currency = cm.POCurrencyCode,
                    UpliftPer = cm.UPLiftPrice.HasValue ? cm.UPLiftPrice.Value : 0,
                    ESTShippingCost = cm.ESTShippingCost,
                    NonPreferredCompany = cm.NonPreferredCompany,
                    SupplierWarranty = cm.SupplierWarranty
                };
                return Json(currencylist, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                new Errorlog().LogMessage("Inside POHubController class, Method name : GetSupplierCurrencyDetails. Exception details:" + (ex.InnerException == null ? ex.Message : ex.InnerException.Message));
                return Json("Something went Wrong", JsonRequestBehavior.AllowGet);
            }
        }
        [HttpPost]
        public ActionResult GetSupplierCurrency(System.Int32 GlobalCurrencyNo)
        {

            try
            {
                JsonObject jsn = new JsonObject();
                JsonObject jsnList = new JsonObject(true);
                List<BLL.Currency> lst = BLL.Currency.DropDownBuyForClientAndGlobal(SessionManager.ClientID, GlobalCurrencyNo, true);
                return Json(lst, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                new Errorlog().LogMessage("Inside POHubController class, Method name : GetSupplierCurrency. Exception details:" + (ex.InnerException == null ? ex.Message : ex.InnerException.Message));
                return Json("Something went Wrong", JsonRequestBehavior.AllowGet);
            }
        }
        [HttpPost]
        public ActionResult AutoSearchBomManager(System.String Part)
        {
            try
            {
                List<BLL.Part> lst = null;

                lst = BLL.Part.AutoSearchBomManager(SessionManager.ClientID, Functions.RemovePunctuationRetainingPercentSigns(Part) + "%");


                return Json(lst, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                new Errorlog().LogMessage("Inside POHubController class, Method name : AutoSearchBomManager. Exception details:" + (ex.InnerException == null ? ex.Message : ex.InnerException.Message));
                return Json("Something went Wrong", JsonRequestBehavior.AllowGet);
            }


        }

        [HttpPost]
        public ActionResult GetAutoSourcingResult(System.Int32 BOM, int? CustomerReqID)
        {
            int curPage = 1;
            if (Request["pq_curpage"] != null)
            {
                curPage = Convert.ToInt32(Request["pq_curpage"].ToString());
            }
            int Rpp = 5;
            if (Request["pq_rpp"] != null)
            {
                Rpp = Convert.ToInt32(Request["pq_rpp"].ToString());
            }
            List<Rebound.GlobalTrader.DAL.AutoSourcing> sourcinglist = new List<Rebound.GlobalTrader.DAL.AutoSourcing>();
            try
            {

                sourcinglist = BOMManagerContract.GetAutoSourcingResult(BOM, CustomerReqID, curPage, Rpp);
                if (sourcinglist.Count > 0)
                {
                    sourcinglist[0].curpage = curPage;
                }
                return Json(sourcinglist, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                new Errorlog().LogMessage("Inside POHubController class, Method name : GetAutoSourcingResult. Exception details:" + (ex.InnerException == null ? ex.Message : ex.InnerException.Message));
                return Json(sourcinglist, JsonRequestBehavior.AllowGet);
            }
            finally
            {
                sourcinglist = null;
            }
        }

        public class StatusMessage
        {
            public string status { get; set; }
            public string Message { get; set; }
        }
        protected override JsonResult Json(object data, string contentType, Encoding contentEncoding, JsonRequestBehavior behavior)
        {
            return new JsonResult()
            {
                Data = data,
                ContentType = contentType,
                ContentEncoding = contentEncoding,
                JsonRequestBehavior = behavior,
                MaxJsonLength = Int32.MaxValue
            };
        }

        [HttpPost]
        public ActionResult UpdateAutoSourcing(AutoSourcingModel sourcing)
        {
            StatusMessage stts = new StatusMessage();
            try
            {
                DataTable dt = BOMManagerContract.UpdateAutoSourcing(sourcing.BOMManagerId,
                                                                     sourcing.SourceId,
                                                                     sourcing.SPQ,
                                                                     sourcing.SellPrice,
                                                                     sourcing.Price,
                                                                     sourcing.ManufacturerNo,
                                                                     sourcing.ProductNo,
                                                                     sourcing.Reason,
                                                                     sourcing.PartNo,
                                                                     sourcing.ROHS,
                                                                     sourcing.CountryOfOrigin,
                                                                     sourcing.DateCode,
                                                                     sourcing.PackageNo,
                                                                     sourcing.Quantity,
                                                                     sourcing.OfferStatus,
                                                                     sourcing.FactorySealed,
                                                                     sourcing.MSLNo,
                                                                     sourcing.SupplierTotalQSA,
                                                                     sourcing.SupplierMOQ,
                                                                     sourcing.SupplierLTB,
                                                                     sourcing.Currency,
                                                                     sourcing.ShippingCost,
                                                                     sourcing.LeadTime,
                                                                     sourcing.Region,
                                                                     sourcing.DeliveryDate,
                                                                     sourcing.SupplierWarranty,
                                                                     sourcing.ROHSStatus,
                                                                     sourcing.Notes,
                                                                     sourcing.IsTestingRecommended,
                                                                     SessionManager.LoginID);

                foreach (DataRow dr in dt.Rows)
                {
                    stts.status = dr["Status"].ToString();
                    stts.Message = dr["Message"].ToString();
                }
                if (stts.status != "Success")
                {
                    new Errorlog().LogMessage("Inside POHubController class, Method name : UpdateAutoSourcing. Exception details:" + stts.Message);
                }
                return Json(stts, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                stts.status = "Fail";
                stts.Message = "Something went wrong.";
                new Errorlog().LogMessage("Inside POHubController class, Method name : UpdateAutoSourcing. Exception details:" + (ex.InnerException == null ? ex.Message : ex.InnerException.Message));
                return Json(stts, JsonRequestBehavior.AllowGet);
            }
            finally
            {
                stts = null;
            }
        }

        [HttpPost]
        public ActionResult ReplaceSourcingBOMManager(System.Int32 BOM, System.Int32 CustomerRequirementId, System.Int32 ReplaceSourceType)
        {
            StatusMessage stts = new StatusMessage();
            try
            {

                DataTable dt = BOMManagerContract.ReplaceSourcingBOMManager(BOM, CustomerRequirementId, (int)SessionManager.LoginID, ReplaceSourceType);

                foreach (DataRow dr in dt.Rows)
                {

                    stts.status = dr["Status"].ToString();
                    stts.Message = dr["Message"].ToString();
                }
                if (stts.status != "Success")
                {
                    new Errorlog().LogMessage("Inside POHubController class, Method name : ReplaceSourcingBOMManager. Exception details:" + stts.Message);
                }
                return Json(stts, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                stts.status = "Fail";
                stts.Message = "Something went wrong.";
                new Errorlog().LogMessage("Inside POHubController class, Method name : ReplaceSourcingBOMManager. Exception details:" + (ex.InnerException == null ? ex.Message : ex.InnerException.Message));
                return Json(stts, JsonRequestBehavior.AllowGet);
            }
            finally
            {
                stts = null;
            }
        }
        [HttpPost]
        public ActionResult RemoveOfferBOMManager(System.Int32 BOM, System.Int32 SourceId)
        {
            StatusMessage stts = new StatusMessage();
            try
            {

                DataTable dt = BOMManagerContract.RemoveOfferBOMManager(BOM, SourceId, (int)SessionManager.LoginID);

                foreach (DataRow dr in dt.Rows)
                {

                    stts.status = dr["Status"].ToString();
                    stts.Message = dr["Message"].ToString();
                }
                if (stts.status != "Success")
                {
                    new Errorlog().LogMessage("Inside POHubController class, Method name : RemoveOfferBOMManager. Exception details:" + stts.Message);
                }
                return Json(stts, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                stts.status = "Fail";
                stts.Message = "Something went wrong.";
                new Errorlog().LogMessage("Inside POHubController class, Method name : RemoveOfferBOMManager. Exception details:" + (ex.InnerException == null ? ex.Message : ex.InnerException.Message));
                return Json(stts, JsonRequestBehavior.AllowGet);
            }
            finally
            {
                stts = null;
            }
        }
        [HttpPost]
        public ActionResult SaveUserXmatchData(int? MOQ, int SPQ, int? StockQTY, string DateCode, string LeadTime, int BOMManagerID, int SalesXMatchID)
        {
            try
            {
                XMatch objXMatch = new XMatch();
                objXMatch.SaveUserXmatchData(MOQ,
                                             SPQ,
                                             StockQTY,
                                             DateCode,
                                             LeadTime,
                                             BOMManagerID,
                                             SalesXMatchID);
                return Json("1", JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                new Errorlog().LogMessage("Inside POHubController class, Method name : SaveUserXmatchData. Exception details:" + (ex.InnerException == null ? ex.Message : ex.InnerException.Message));
                return Json("0", JsonRequestBehavior.AllowGet);
            }
        }
        [HttpPost]
        public ActionResult GetBOMManagerStatus(System.Int32 BOM)
        {
            string GetBOMManagerStatus = string.Empty;
            string BOMManagerName = string.Empty;
            try
            {

                DataTable dt = BOMManagerContract.GetBOMManagerStatus(BOM);

                BOMManagerName = dt.Rows[0][0].ToString();
                GetBOMManagerStatus = dt.Rows[0][1].ToString();
                var jsonData = new
                {
                    BOMManagerName = BOMManagerName,
                    BOMStatus = GetBOMManagerStatus,
                    Status = dt.Rows[0][2].ToString(),
                    BOMManagerCode = dt.Rows[0][3].ToString(),
                    RequestToPOHubBy = dt.Rows[0][4].ToString(),
                    CompanyNo = dt.Rows[0][5].ToString(),
                    CompanyName = dt.Rows[0][6].ToString(),
                    SupportTeamMemberNo = dt.Rows[0][7].ToString(),
                    ItemsSourcedCount = dt.Rows[0][8].ToString(),
                    TotalItemsCount = dt.Rows[0][9].ToString(),
                    Salesperson = dt.Rows[0][13].ToString()
                };

                return Json(jsonData, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {

                new Errorlog().LogMessage("Inside POHubController class, Method name : ReplaceSourcingBOMManager. Exception details:" + (ex.InnerException == null ? ex.Message : ex.InnerException.Message));
                return Json(GetBOMManagerStatus, JsonRequestBehavior.AllowGet);
            }
            finally
            {
                GetBOMManagerStatus = null;
            }
        }
        [HttpPost]
        public ActionResult GetUserXMatchData(int BOMManagerID, int? SalesXMatchID)
        {
            try
            {
                XMatch objXMatch = new XMatch();
                DataTable dtResult = objXMatch.GetUserXMatchData(BOMManagerID,
                                                                 SalesXMatchID);
                List<XMatchData> xMatchData = new List<XMatchData>();
                xMatchData = (from DataRow dr in dtResult.Rows
                              select new XMatchData()
                              {
                                  SupplierMOQ = dr["SupplierMOQ"].ToString(),
                                  SPQ = dr["SPQ"].ToString(),
                                  DateCode = dr["DateCode"].ToString(),
                                  LeadTime = dr["LeadTime"].ToString()
                              }
                              ).ToList();
                return Json(xMatchData, JsonRequestBehavior.AllowGet);

            }
            catch (Exception ex)
            {
                new Errorlog().LogMessage("Inside POHubController class, Method name : GetUserMatchData. Exception details:" + (ex.InnerException == null ? ex.Message : ex.InnerException.Message));
                return Json("Something went Wrong", JsonRequestBehavior.AllowGet);
            }
        }
        public ActionResult GetAllSourcingResultBOMManager(int BOMManagerID, int reqStatus)
        {
            try
            {
                List<SourcingResult> lst = BOMManagerContract.GetListForBOMManagerReleaseAll(BOMManagerID, Convert.ToBoolean(SessionManager.IsPOHub), reqStatus);
                return Json(lst, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                new Errorlog().LogMessage("Inside POHubController class, Method name : GetAllSourcingResultBOMManager. Exception details:" + (ex.InnerException == null ? ex.Message : ex.InnerException.Message));
                return Json(false, JsonRequestBehavior.AllowGet);
            }
        }
        public ActionResult BOMReleaseRequirement(int BOMManagerID, string RequestToPOHubBy, string SupportTeamMemberNo, string UpdatedBy, string BOMManagerCode, string BOMManagerName,
            string BOMManagerCompanyNo, string BOMManagerCompanyName, string Cids,
            string ReqType, string NoBidNotes, string ASId, List<CustomerRReleaseNote> lstReleaseNotes, List<AutoSourcingModel> reasons)
        {
            try
            {
                int? reqtyepval = string.IsNullOrEmpty(ReqType) ? 0 : Convert.ToInt32(ReqType);
                DataTable dt = BOMManagerContract.BOMManagerReleaseRequirement(
                    BOMManagerID,
                    SessionManager.LoginID,
                    Cids,
                    reqtyepval,
                    NoBidNotes,
                    ASId
                );
                StatusMessage stts = new StatusMessage();
                foreach (DataRow dr in dt.Rows)
                {

                    stts.status = dr["Status"].ToString();
                    stts.Message = dr["Message"].ToString();
                }
                if (stts.status != "Success")
                {
                    new Errorlog().LogMessage("Inside POHubController class, Method name : BOMReleaseRequirement. Exception details:" + stts.Message);
                }
                
                if (stts.status == "Success")
                {
                    if (reasons != null)
                    {
                        foreach (var item in reasons)
                        {
                            DataTable test = BOMManagerContract.UpdateReasonForAutoSourcing(BOMManagerID, item.SourceId, item.Reason, SessionManager.LoginID);
                        }
                    }

                    //Update release notes Cuongdx -23-4-24
                    if (lstReleaseNotes != null)
                    {
                        List<DataTable> lstResultTable = BOMManagerContract.BOMManagerUpdateCustomerRequirementReleaseNotes(lstReleaseNotes);
                        StatusMessage sttReleaseNotes = new StatusMessage();
                        foreach (DataTable tableReleaseNote in lstResultTable)
                        {
                            foreach (DataRow dr in dt.Rows)
                            {

                                sttReleaseNotes.status = dr["Status"].ToString();
                                sttReleaseNotes.Message = dr["Message"].ToString();
                            }
                            if (sttReleaseNotes.status != "Success")
                            {
                                new Errorlog().LogMessage("Inside POHubController class, Method name : BOMReleaseRequirement -> Update Customer Requirement Release Notes in BOM. Exception details:" + sttReleaseNotes.Message);
                            }
                        }
                    }

                    WebServices servic = new WebServices();

                    if (reqtyepval == 1)
                    {
                        if (!string.IsNullOrEmpty(SupportTeamMemberNo))
                        {
                            servic.NotifyReleaseBomManager(UpdatedBy + "||" + RequestToPOHubBy + "||" + SupportTeamMemberNo, "", Functions.GetGlobalResource("Messages", "BOMManagerReleased"), BOMManagerID, BOMManagerCode, BOMManagerName, Convert.ToInt32(BOMManagerCompanyNo), BOMManagerCompanyName, true, Cids);
                        }
                        else
                        {
                            servic.NotifyReleaseBomManager(UpdatedBy + "||" + RequestToPOHubBy, "", Functions.GetGlobalResource("Messages", "BOMManagerReleased"), BOMManagerID, BOMManagerCode, BOMManagerName, Convert.ToInt32(BOMManagerCompanyNo), BOMManagerCompanyName, true, Cids);
                        }
                    }
                    if (reqtyepval == 2)
                    {
                        if (!string.IsNullOrEmpty(SupportTeamMemberNo))
                        {
                            servic.NotifyRecallBomManager(UpdatedBy + "||" + RequestToPOHubBy + "||" + SupportTeamMemberNo, "", Functions.GetGlobalResource("Messages", "BOMManagerRecall"), BOMManagerID, BOMManagerCode, BOMManagerName, Convert.ToInt32(BOMManagerCompanyNo), BOMManagerCompanyName, true, Cids);
                        }
                        else
                        {
                            servic.NotifyRecallBomManager(UpdatedBy + "||" + RequestToPOHubBy, "", Functions.GetGlobalResource("Messages", "BOMManagerRecall"), BOMManagerID, BOMManagerCode, BOMManagerName, Convert.ToInt32(BOMManagerCompanyNo), BOMManagerCompanyName, true, Cids);
                        }
                    }
                    if (reqtyepval == 3)
                    {
                        if (!string.IsNullOrEmpty(SupportTeamMemberNo))
                        {
                            servic.NotifyNoBidBomManager(UpdatedBy + "||" + RequestToPOHubBy + "||" + SupportTeamMemberNo, "", Functions.GetGlobalResource("Messages", "BOMManagerNoBid"), BOMManagerID, BOMManagerCode, BOMManagerName, Convert.ToInt32(BOMManagerCompanyNo), BOMManagerCompanyName, true, Cids);
                        }
                        else
                        {
                            servic.NotifyNoBidBomManager(UpdatedBy + "||" + RequestToPOHubBy, "", Functions.GetGlobalResource("Messages", "BOMManagerNoBid"), BOMManagerID, BOMManagerCode, BOMManagerName, Convert.ToInt32(BOMManagerCompanyNo), BOMManagerCompanyName, true, Cids);
                        }
                    }
                    if (reqtyepval == 4)
                    {
                        if (!string.IsNullOrEmpty(SupportTeamMemberNo))
                        {
                            servic.NotifyRecallNoBidBomManager(UpdatedBy + "||" + RequestToPOHubBy + "||" + SupportTeamMemberNo, "", Functions.GetGlobalResource("Messages", "BOMManagerRecallNoBid"), BOMManagerID, BOMManagerCode, BOMManagerName, Convert.ToInt32(BOMManagerCompanyNo), BOMManagerCompanyName, true, Cids);
                        }
                        else
                        {
                            servic.NotifyRecallNoBidBomManager(UpdatedBy + "||" + RequestToPOHubBy, "", Functions.GetGlobalResource("Messages", "BOMManagerRecallNoBid"), BOMManagerID, BOMManagerCode, BOMManagerName, Convert.ToInt32(BOMManagerCompanyNo), BOMManagerCompanyName, true, Cids);
                        }
                    }

                }

                return Json(stts, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                new Errorlog().LogMessage("Inside POHubController class, Method name : BOMReleaseRequirement. Exception details:" + (ex.InnerException == null ? ex.Message : ex.InnerException.Message));
                return Json(false, JsonRequestBehavior.AllowGet);
            }
        }
        public ActionResult GetUnsourcedParts(int BOMManagerID)
        {
            try
            {
                List<String> lst = BOMManagerContract.GetUnsourcedParts(BOMManagerID);
                return Json(lst, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                new Errorlog().LogMessage("Inside POHubController class, Method name : GetUnsourcedParts. Exception details:" + (ex.InnerException == null ? ex.Message : ex.InnerException.Message));
                return Json(new List<string>(), JsonRequestBehavior.AllowGet);
            }
        }
        public ActionResult CustReqPartsGRIDIHSAPIBOMManager(int BOMManagerID, int? CustomerReqID)
        {
            int curPage = 1;
            if (Request["pq_curpage"] != null)
            {
                curPage = Convert.ToInt32(Request["pq_curpage"].ToString());
            }
            int Rpp = 5;
            if (Request["pq_rpp"] != null)
            {
                Rpp = Convert.ToInt32(Request["pq_rpp"].ToString());
            }
            List<Part> lstDetailsMain = new List<Part>();
            try
            {
                DataTable dtCheckParts = new DataTable();
                dtCheckParts.Columns.Add("Rowid");
                dtCheckParts.Columns.Add("PartName");
                dtCheckParts.Columns.Add("ItemFound");
                //Getting existing data for the parts from IHS table in GT db
                List<BLL.Part> lst = BLL.Part.CustReqPartsGRIDIHSAPIBOMManager(SessionManager.ClientID, BOMManagerID, CustomerReqID, out dtCheckParts, curPage, Rpp);
                //calling IHS API if any part IHS data not found in GT db
                var partList = dtCheckParts.AsEnumerable().Select(p => p.Field<string>("PartName")).ToArray();
                //string partliststr = string.Join(",")
                if (dtCheckParts.Rows.Count > 0)
                {
                    Models.IHSAPICall IHSApi = new Models.IHSAPICall();

                    lstDetailsMain = IHSApi.GetDataGrid(partList);

                }
                List<Part> IHSPartList = new List<Part>();
                if (lstDetailsMain.Count > 0)
                {
                    IHSPartList = lst.Concat(lstDetailsMain).ToList();
                }
                else
                {
                    IHSPartList = lst;
                }
                return Json(IHSPartList, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                new Errorlog().LogMessage("Inside POHubController class, Method name : CustReqPartsGRIDIHSAPIBOMManager. Exception details:" + (ex.InnerException == null ? ex.Message : ex.InnerException.Message));
                return Json(new List<string>(), JsonRequestBehavior.AllowGet);
            }
        }

        [HttpPost]
        public ActionResult AddNewOffer(System.Int32 BOM, int sourceId, System.Int32 CustomerRequirementId, System.Int32 ReplaceSourceType, string OfferSource)
        {
            StatusMessage stts = new StatusMessage();
            try
            {
                DataTable dt = BOMManagerContract.AddNewOffer(BOM, sourceId, CustomerRequirementId, (int)SessionManager.LoginID, ReplaceSourceType, OfferSource);

                foreach (DataRow dr in dt.Rows)
                {
                    stts.status = dr["Status"].ToString();
                    stts.Message = dr["Message"].ToString();
                }
                if (stts.status != "Success")
                {
                    new Errorlog().LogMessage("Inside POHubController class, Method name : AddNewOffer. Exception details:" + stts.Message);
                }
                return Json(stts, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                stts.status = "Fail";
                stts.Message = "Something went wrong.";
                new Errorlog().LogMessage("Inside POHubController class, Method name : AddNewOffer. Exception details:" + (ex.InnerException == null ? ex.Message : ex.InnerException.Message));
                return Json(stts, JsonRequestBehavior.AllowGet);
            }
            finally
            {
                stts = null;
            }
        }

        [HttpPost]
        public ActionResult SaveBOMItemNoBid(int BOMManagerID, int CustomerRequirementId, string NoBidReason)
        {
            try
            {
                bool success = BOMManagerContract.SaveBOMItemNoBid(BOMManagerID, CustomerRequirementId, SessionManager.LoginID, NoBidReason);
                return Json(success, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                new Errorlog().LogMessage("InsidePOHUBController class, Method name : SaveBOMItemNoBid. Exception details:" + (ex.InnerException == null ? ex.Message : ex.InnerException.Message));
                return Json(null, JsonRequestBehavior.AllowGet);
            }
            finally
            {
            }
        }
        [HttpPost]
        public ActionResult RecallBOMItemNoBid(int CustomerRequirementId)
        {
            try
            {
                bool success = BOMManagerContract.RecallNoBidRequirement(CustomerRequirementId, SessionManager.LoginID);
                return Json(success, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                new Errorlog().LogMessage("InsidePOHUBController class, Method name : RecallBOMItemNoBid. Exception details:" + (ex.InnerException == null ? ex.Message : ex.InnerException.Message));
                return Json(null, JsonRequestBehavior.AllowGet);
            }
            finally
            {
            }
        }
        public ActionResult LoadMoreInformationWindow(int BOMID, int CRID)
        {
            return View("~/Areas/BOM/Views/BOMManagerGrids/BOMManagerGrids.cshtml");
        }
        [HttpPost]
        public ActionResult LoadEditBOMData(int CustReqID)
        {
            try
            {

                CustomerRequirement cReq = CustomerRequirement.Get(CustReqID);
                if (cReq.RFQClosingDate == DateTime.MinValue)
                {
                    cReq.RFQClosingDate = null;
                }
                if (cReq.CustomerDecisionDate == DateTime.MinValue)
                {
                    cReq.CustomerDecisionDate = null;
                }
                return Json(cReq, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {

                new Errorlog().LogMessage("Inside BOMManagerController class, Method name : LoadEditBOMData. Exception details:" + (ex.InnerException == null ? ex.Message : ex.InnerException.Message));
                return Json(null, JsonRequestBehavior.AllowGet);
            }
            finally
            {
            }
        }
        [HttpPost]
        public ActionResult PHSaveEditBOMItemData(int customerRequirementId, int manufacturerNo, int productNo, int BOMManagerID)
        {
            try
            {
                bool success = BOMManagerContract.PHSaveEditBOMItemData(customerRequirementId, manufacturerNo, productNo, BOMManagerID, SessionManager.LoginID);
                return Json(success, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                new Errorlog().LogMessage("Inside BOMManagerController class, Method name : SaveEditbOMItemData. Exception details:" + (ex.InnerException == null ? ex.Message : ex.InnerException.Message));
                return Json(null, JsonRequestBehavior.AllowGet);
            }
            finally
            {
            }
        }

        [HttpPost]
        public ActionResult GetLyticaAPILogCount(int BOMManagerID)
        {
            try
            {
                int LyticaAPILogCount = 0;
                LyticaAPILogCount = Offer.LyticaApiLog(BOMManagerID, SessionManager.LoginID);
                return Json(LyticaAPILogCount, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                new Errorlog().LogMessage("Inside BOMManagerController class, Method name : GetLyticaAPILogCount. Exception details:" + (ex.InnerException == null ? ex.Message : ex.InnerException.Message));
                return Json(0, JsonRequestBehavior.AllowGet);
            }
            finally
            {
            }
        }

        [HttpPost]
        public ActionResult GetBuyersList()
        {
            int intTeamNo = 0;
            int intDivisionNo = 0;
            int intExcludeLoginNo = 0;
            List<Login> lst = new List<Login>();
            try
            {
                string strOptions = CacheManager.SerializeOptions(new object[] { 114, "POHub" });

                lst = Login.DropDownForPurchaseHub(114, intTeamNo, intDivisionNo, intExcludeLoginNo);
                return Json(lst, JsonRequestBehavior.AllowGet);
            }

            catch (Exception ex)
            {
                new Errorlog().LogMessage("Inside POHubController class, Method name : GetBuyersList. Exception details:" + (ex.InnerException == null ? ex.Message : ex.InnerException.Message));
                return Json(lst, JsonRequestBehavior.AllowGet);
            }
        }

        public ActionResult GetLoginListForClient()
        {
            int intTeamNo = 0;
            int intDivisionNo = 0;
            int intExcludeLoginNo = 0;
            List<Login> lst = new List<Login>();
            try
            {
                lst = Login.DropDownForClient(SessionManager.ClientID, intTeamNo, intDivisionNo, intExcludeLoginNo);
                return Json(lst, JsonRequestBehavior.AllowGet);
            }

            catch (Exception ex)
            {
                new Errorlog().LogMessage("Inside BOMManagerController class, Method name : GetLoginListForClient. Exception details:" + (ex.InnerException == null ? ex.Message : ex.InnerException.Message));
                return Json(lst, JsonRequestBehavior.AllowGet);
            }
        }

        [HttpPost]
        public ActionResult GetMailToList(string searchString)
        {
            List<BLL.Login> lst = null;
            int? intGlobalLoginClientNo;
            intGlobalLoginClientNo = GetFormValue_NullableInt("GlobalLoginClientNo"); //SessionManager.GlobalClientNo;

            try
            {
                lst = BLL.Login.AutoSearchForMail((intGlobalLoginClientNo.HasValue && intGlobalLoginClientNo.Value > 0) ? intGlobalLoginClientNo.Value : SessionManager.ClientID, SessionManager.LoginID, searchString);
                //lst = BLL.Login.AutoSearchForMail(114, SessionManager.LoginID, searchString);
                return Json(lst, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                new Errorlog().LogMessage("Inside POHubController class, Method name : GetMailToList. Exception details:" + (ex.InnerException == null ? ex.Message : ex.InnerException.Message));
                return Json(lst, JsonRequestBehavior.AllowGet);
            }
            finally
            {
                lst = null;
            }
        }

        [HttpPost]
        public ActionResult SaveCommunicationNotesData(int BOMManagerID, string cusReqIds, string sendToGroup, string notes, string cCUserIds, int sendTo, string BOMManagerName, int companyNo, int contact2No, int requestToPOHubBy, int updateByPH)
        {
            try
            {
                bool success = SaveBOMExpediteNotes(BOMManagerID, cusReqIds, sendToGroup, notes, cCUserIds, sendTo, BOMManagerName, companyNo, contact2No, requestToPOHubBy, updateByPH);
                return Json(success, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                new Errorlog().LogMessage("Inside POHubController class, Method name : SaveCommunicationNotesData. Exception details:" + (ex.InnerException == null ? ex.Message : ex.InnerException.Message));
                return Json(null, JsonRequestBehavior.AllowGet);
            }
        }

        [HttpPost]
        public ActionResult GetComunicationNotes(int BOMManagerID, int CustomerReqNo)
        {
            try
            {
                List<Audit> audits = Audit.GetListExpediteForBOM(BOMManagerID, SessionManager.ClientID ?? 0, CustomerReqNo);
                List<CommunicationNotesModel> nodeLst = new List<CommunicationNotesModel>();
                int line = 1;
                foreach (var audit in audits)
                {
                    nodeLst.Add(new CommunicationNotesModel
                    {
                        ID = audit.AuditId,
                        Note = audit.Note,
                        CCUserID = audit.CCUserID,
                        DateTimeNote = Functions.FormatDate(audit.DLUP, false, true),
                        EmployeeName = audit.EmployeeName,
                        NoteTo = audit.To,
                        ReqNos = audit.ReqNos,
                        SendToGroup = audit.SendToGroup,
                        Line = line
                    });
                    line++;
                }

                return Json(nodeLst, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                new Errorlog().LogMessage("Inside POHubController class, Method name : GetExpediteHistory. Exception details:" + (ex.InnerException == null ? ex.Message : ex.InnerException.Message));
                return Json(null, JsonRequestBehavior.AllowGet);
            }
        }

        [HttpPost]
        public ActionResult GetGroupForCommunicationNotes()
        {
            List<CommunicationNoteGroupModel> list = new List<CommunicationNoteGroupModel>();
            try
            {
                if (SessionManager.ClientID == 101)
                {
                    list = new List<CommunicationNoteGroupModel>
                    {
                        new CommunicationNoteGroupModel()
                        {
                            Id = "Hub Sales",
                            Name = Functions.GetGlobalResource("HUBRFQSendToGrouping", "Hub Sales")
                        }
                    };
                }
                else
                {
                    list = new List<CommunicationNoteGroupModel>
                    {
                        new CommunicationNoteGroupModel()
                        {
                            Id = "Hub Only",
                            Name = Functions.GetGlobalResource("HUBRFQSendToGrouping", "Hub Only")
                        },
                        new CommunicationNoteGroupModel()
                        {
                            Id = "Hub Sales",
                            Name = Functions.GetGlobalResource("HUBRFQSendToGrouping", "Hub Sales")
                        }
                    };
                }

                return Json(list, JsonRequestBehavior.AllowGet);
            }

            catch (Exception ex)
            {
                new Errorlog().LogMessage("Inside POHubController class, Method name : GetCurrencyList. Exception details:" + (ex.InnerException == null ? ex.Message : ex.InnerException.Message));
                return Json(list, JsonRequestBehavior.AllowGet);
            }
        }

        private bool SaveBOMExpediteNotes(int BOMManagerID, string cusReqIds, string sendToGroup, string notes, string cCUserIds, int sendTo, string BOMManagerName, int companyNo, int contact2No, int requestToPOHubBy, int updateByPH)
        {            
            WebServices servic = new WebServices();
            string addrCC = string.Empty;
            string Subject = Functions.GetGlobalResource("Printing", "CommunicationNoteSubject");
            string SendToGroup = sendToGroup == "0" ? null : sendToGroup;
            //int CompanyNo = companyNo;
            //System.Int32 Contact2No = contact2No;
            servic.GetNameOfCCLoginIDHUBRFQ(cCUserIds, out addrCC);

            int expediteNoteId = CustomerRequirement.InsertBOMExpediteNote(
                 BOMManagerID,
                 notes,
                 SessionManager.LoginID,
                 cusReqIds,
                 sendTo,
                 addrCC,
                 SendToGroup
            );

            if (expediteNoteId > 0)
            {
                string FullPart = "";
                string ReqSalesman = "";
                string SendToMembersList = "";
                string SendToArrayList = "";
                
                List<CustomerRequirement> lst = CustomerRequirement.GetHUBRFQReqNos(cusReqIds, SessionManager.ClientID);
                foreach (CustomerRequirement objDetails in lst)
                {
                    FullPart = FullPart + "," + objDetails.FullPart;
                    if (SendToGroup == "Hub Sales")
                    {
                        ReqSalesman = ReqSalesman + "||" + Convert.ToString(objDetails.Salesman);
                    }                       
                }

                if (sendTo > 0)
                {
                    SendToArrayList = Convert.ToString(sendTo);
                }
                else
                {
                    List<SendToMemberList> list = new List<SendToMemberList>();
                    List<SendToMemberList> sglist = SendToMemberList.GetSecurityGroupList();

                    foreach (SendToMemberList objsg in sglist)
                    {
                        list = SendToMemberList.GetMemberList(Convert.ToInt32(objsg.SecurityGroupNo));
                    }

                    foreach (SendToMemberList objDetails in list)
                    {
                        SendToMembersList = SendToMembersList + "||" + Convert.ToString(objDetails.LoginNo);
                    }

                    SendToArrayList = SendToMembersList.StartsWith("||") ? SendToMembersList.Remove(0, 2) : SendToMembersList;

                    if (SendToGroup == "Hub Sales")
                    {
                        if (contact2No != 0)
                        {
                            ReqSalesman = Convert.ToString(contact2No) + ReqSalesman;
                        }
                        else
                        {
                            ReqSalesman = Convert.ToString(requestToPOHubBy) + ReqSalesman;
                        }
                    }
                }

                if (!string.IsNullOrEmpty(ReqSalesman))
                {
                    StringBuilder message = new StringBuilder();
                    string hyperLink = string.Format("Ord_BOMManagerDetail.aspx?BOM={0}&Note={1}", BOMManagerID, expediteNoteId);
                    string poref = string.Format("Reference BOM Manager Detail   : <a onclick=\"{0}\" target=\"_blank\" href=\"{1}\">{2}</a>", "javascript:void(0);", hyperLink, BOMManagerName);
                    message.Append("Message By  : " + SessionManager.LoginFullName + "<br />");
                    message.Append("Date & Time  : " + Functions.FormatDate(Functions.GetUKLocalTime()) + " " + Functions.FormatTime(Functions.GetUKLocalTime()) + "<br />");
                    message.Append("Part Nos  : " + FullPart.Remove(0, 1) + "<br />");
                    message.Append(poref + "<br /><br />");
                    message.Append("Communication Note  : " + notes + "<br />");
                    message.Append("<br /><br />Regards,<br />" + SessionManager.LoginFullName + "<br />");

                    servic.NotifyMessageExpediteNoteHUBRFQ(ReqSalesman, Subject, Convert.ToString(message), false, string.Empty, string.Empty);
                    message = null;
                }

                if (!string.IsNullOrEmpty(SendToArrayList))
                {
                    StringBuilder message = new StringBuilder();
                    string hyperLink = string.Format("Ord_BOMManagerSourcing.aspx?BOM={0}&Note={1}", BOMManagerID, expediteNoteId);
                    string poref = string.Format("Reference BOM Manager Sourcing   : <a onclick=\"{0}\" target=\"_blank\" href=\"{1}\">{2}</a>", "javascript:void(0);", hyperLink, BOMManagerName);
                    
                    message.Append("Message By  : " + SessionManager.LoginFullName + "<br />");
                    message.Append("Date & Time  : " + Functions.FormatDate(Functions.GetUKLocalTime()) + " " + Functions.FormatTime(Functions.GetUKLocalTime()) + "<br />");
                    message.Append("Part Nos  : " + FullPart.Remove(0, 1) + "<br />");
                    message.Append(poref + "<br /><br />");
                    message.Append("Communication Note  : " + notes + "<br />");
                    message.Append("<br /><br />Regards,<br />" + SessionManager.LoginFullName + "<br />");

                    servic.NotifyMessageExpediteNoteHUBRFQ(SendToArrayList, Subject, Convert.ToString(message), false, cCUserIds, SendToGroup);
                    message = null;
                }
                
                servic = null;

                return true;
            }
            else
            {
                return false;
            }
        }

        private int? GetFormValue_NullableInt(string strIndex)
        {
            return GetFormValue_NullableInt(strIndex, null);
        }
        private int? GetFormValue_NullableInt(string strIndex, object objValueIfNull)
        {
            object obj = GetFormValue(strIndex, objValueIfNull);
            if (obj == null)
            {
                return null;
            }
            else
            {
                CultureInfo ci = new CultureInfo(SessionManager.Culture);
                return (int?)Convert.ToInt32(obj, ci);
            }
        }

        private object GetFormValue(string strIndex, object objIfNull)
        {
            System.Web.HttpContext _context = System.Web.HttpContext.Current;


            object obj = objIfNull;
            if (_context.Request.Form[strIndex] != null) obj = Functions.FormatStringForDatabase(_context.Request.Form[strIndex]);
            return obj;
        }
    }
}
