/* 
===========================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-204858]	    CuongDox			4-Oct-2024		Update			Update Gross profit
===========================================================================================
*/
CREATE OR ALTER PROCEDURE KPI_GetSalesRevenueTargetByID
    @TeamTargetNo int,
    @YearNo INT
AS
BEGIN

    IF OBJECT_ID('tempdb..#tmp') IS NOT NULL
    begin
        DROP TABLE #tmp
    end

    IF OBJECT_ID('tempdb..#tmpRevenue') IS NOT NULL
    begin
        DROP TABLE #tmpRevenue
    end

    --  select isnull(sum(resale),0) from tbKPIRevenueSummary where TeamNo=100155 and invMonth=1 and clientno=101                       


    ;
    with CTE_SalesSummary
    as (select 1 as OrderBy,
               0 as SectionId,
               'Team Target' as SectionName,
               'TotalTarget' as SectionType,
               isnull(JanTarget, 0) JanTarget,
               isnull(FebTarget, 0) FebTarget,
               isnull(MarchTarget, 0) MarchTarget,
               isnull(AprTarget, 0) AprTarget,
               isnull(MayTarget, 0) MayTarget,
               isnull(JuneTarget, 0) JuneTarget,
               isnull(JulyTarget, 0) JulyTarget,
               isnull(AugTarget, 0) AugTarget,
               isnull(SepTarget, 0) SepTarget,
               isnull(OctTarget, 0) OctTarget,
               isnull(NovTarget, 0) NovTarget,
               isnull(DecTarget, 0) DecTarget,
               (
                   select isnull(sum(resale), 0)
                   from tbKPIRevenueSummary
                   where TeamNo = @TeamTargetNo
                         and invMonth = 1
                         and YearNo = @YearNo
               ) as JanRevenue,
               (
                   select isnull(sum(resale), 0)
                   from tbKPIRevenueSummary
                   where TeamNo = @TeamTargetNo
                         and invMonth = 2
                         and YearNo = @YearNo
               ) as FebRevenue,
               (
                   select isnull(sum(resale), 0)
                   from tbKPIRevenueSummary
                   where TeamNo = @TeamTargetNo
                         and invMonth = 3
                         and YearNo = @YearNo
               ) as MarchRevenue,
               (
                   select isnull(sum(resale), 0)
                   from tbKPIRevenueSummary
                   where TeamNo = @TeamTargetNo
                         and invMonth = 4
                         and YearNo = @YearNo
               ) as AprRevenue,
               (
                   select isnull(sum(resale), 0)
                   from tbKPIRevenueSummary
                   where TeamNo = @TeamTargetNo
                         and invMonth = 5
                         and YearNo = @YearNo
               ) as MayRevenue,
               (
                   select isnull(sum(resale), 0)
                   from tbKPIRevenueSummary
                   where TeamNo = @TeamTargetNo
                         and invMonth = 6
                         and YearNo = @YearNo
               ) as JuneRevenue,
               (
                   select isnull(sum(resale), 0)
                   from tbKPIRevenueSummary
                   where TeamNo = @TeamTargetNo
                         and invMonth = 7
                         and YearNo = @YearNo
               ) as JulyRevenue,
               (
                   select isnull(sum(resale), 0)
                   from tbKPIRevenueSummary
                   where TeamNo = @TeamTargetNo
                         and invMonth = 8
                         and YearNo = @YearNo
               ) as AugRevenue,
               (
                   select isnull(sum(resale), 0)
                   from tbKPIRevenueSummary
                   where TeamNo = @TeamTargetNo
                         and invMonth = 9
                         and YearNo = @YearNo
               ) as SepRevenue,
               (
                   select isnull(sum(resale), 0)
                   from tbKPIRevenueSummary
                   where TeamNo = @TeamTargetNo
                         and invMonth = 10
                         and YearNo = @YearNo
               ) as OctRevenue,
               (
                   select isnull(sum(resale), 0)
                   from tbKPIRevenueSummary
                   where TeamNo = @TeamTargetNo
                         and invMonth = 11
                         and YearNo = @YearNo
               ) as NovRevenue,
               (
                   select isnull(sum(resale), 0)
                   from tbKPIRevenueSummary
                   where TeamNo = @TeamTargetNo
                         and invMonth = 12
                         and YearNo = @YearNo
               ) as DecRevenue,
			   --calculate gross profit
			   (
                   select isnull(sum(GrossProfit), 0)
                   from tbKPIRevenueSummary
                   where TeamNo = @TeamTargetNo
                         and invMonth = 1
                         and YearNo = @YearNo
               ) as JanGrossProfit,
               (
                   select isnull(sum(GrossProfit), 0)
                   from tbKPIRevenueSummary
                   where TeamNo = @TeamTargetNo
                         and invMonth = 2
                         and YearNo = @YearNo
               ) as FebGrossProfit,
               (
                   select isnull(sum(GrossProfit), 0)
                   from tbKPIRevenueSummary
                   where TeamNo = @TeamTargetNo
                         and invMonth = 3
                         and YearNo = @YearNo
               ) as MarchGrossProfit,
               (
                   select isnull(sum(GrossProfit), 0)
                   from tbKPIRevenueSummary
                   where TeamNo = @TeamTargetNo
                         and invMonth = 4
                         and YearNo = @YearNo
               ) as AprGrossProfit,
               (
                   select isnull(sum(GrossProfit), 0)
                   from tbKPIRevenueSummary
                   where TeamNo = @TeamTargetNo
                         and invMonth = 5
                         and YearNo = @YearNo
               ) as MayGrossProfit,
               (
                   select isnull(sum(GrossProfit), 0)
                   from tbKPIRevenueSummary
                   where TeamNo = @TeamTargetNo
                         and invMonth = 6
                         and YearNo = @YearNo
               ) as JuneGrossProfit,
               (
                   select isnull(sum(GrossProfit), 0)
                   from tbKPIRevenueSummary
                   where TeamNo = @TeamTargetNo
                         and invMonth = 7
                         and YearNo = @YearNo
               ) as JulyGrossProfit,
               (
                   select isnull(sum(GrossProfit), 0)
                   from tbKPIRevenueSummary
                   where TeamNo = @TeamTargetNo
                         and invMonth = 8
                         and YearNo = @YearNo
               ) as AugGrossProfit,
               (
                   select isnull(sum(GrossProfit), 0)
                   from tbKPIRevenueSummary
                   where TeamNo = @TeamTargetNo
                         and invMonth = 9
                         and YearNo = @YearNo
               ) as SepGrossProfit,
               (
                   select isnull(sum(GrossProfit), 0)
                   from tbKPIRevenueSummary
                   where TeamNo = @TeamTargetNo
                         and invMonth = 10
                         and YearNo = @YearNo
               ) as OctGrossProfit,
               (
                   select isnull(sum(GrossProfit), 0)
                   from tbKPIRevenueSummary
                   where TeamNo = @TeamTargetNo
                         and invMonth = 11
                         and YearNo = @YearNo
               ) as NovGrossProfit,
               (
                   select isnull(sum(GrossProfit), 0)
                   from tbKPIRevenueSummary
                   where TeamNo = @TeamTargetNo
                         and invMonth = 12
                         and YearNo = @YearNo
               ) as DecGrossProfit,
			   -- end calculate gross profit
               cast(0 as float) as TotalTarget,
               0 as TotalRevenue,
			   0 as TotalGrossProfit,
               null as AllocatedPer,
               null as RevenuePer,
               0 as RowId
        from tbTeamTargetFinal
        where teamno = @TeamTargetNo
              and yearno = @YearNo
        union
        select 2 as OrderBy,
               0 as SectionId,
               'Allocated sales Target' as SectionName,
               'AllocatedTarget' as SectionType,
               0 as JanTarget,
               0 as FebTarget,
               0 as MarchTarget,
               0 as AprTarget,
               0 as MayTarget,
               0 as JuneTarget,
               0 as JulyTarget,
               0 as AugTarget,
               0 as SepTarget,
               0 as OctTarget,
               0 as NovTarget,
               0 as DecTarget,
               0 as JanRevenue,
               0 as FebRevenue,
               0 as MarchRevenue,
               0 as AprRevenue,
               0 as MayRevenue,
               0 as JuneRevenue,
               0 as JulyRevenue,
               0 as AugRevenue,
               0 as SepRevenue,
               0 as OctRevenue,
               0 as NovRevenue,
               0 as DecRevenue,
			   0 as JanGrossProfit,
               0 as FebGrossProfit,
               0 as MarchGrossProfit,
               0 as AprGrossProfit,
               0 as MayGrossProfit,
               0 as JuneGrossProfit,
               0 as JulyGrossProfit,
               0 as AugGrossProfit,
               0 as SepGrossProfit,
               0 as OctGrossProfit,
               0 as NovGrossProfit,
               0 as DecGrossProfit,
               cast(0 as float) as TotalTarget,
               0 as TotalRevenue,
			   0 as TotalGrossProfit,
               null as AllocatedPer,
               null as RevenuePer,
               0 as RowId
        union
        select 3 as OrderBy,
               0 as SectionId,
               ' Team Remaining Target' as SectionName,
               'RemainingTarget' as SectionType,
               0 as JanTarget,
               0 as FebTarget,
               0 as MarchTarget,
               0 as AprTarget,
               0 as MayTarget,
               0 as JuneTarget,
               0 as JulyTarget,
               0 as AugTarget,
               0 as SepTarget,
               0 as OctTarget,
               0 as NovTarget,
               0 as DecTarget,
               0 as JanRevenue,
               0 as FebRevenue,
               0 as MarchRevenue,
               0 as AprRevenue,
               0 as MayRevenue,
               0 as JuneRevenue,
               0 as JulyGrossProfit,
               0 as AugGrossProfit,
               0 as SepGrossProfit,
               0 as OctGrossProfit,
               0 as NovGrossProfit,
               0 as DecGrossProfit,
			   0 as JanGrossProfit,
               0 as FebGrossProfit,
               0 as MarchGrossProfit,
               0 as AprGrossProfit,
               0 as MayGrossProfit,
               0 as JuneGrossProfit,
               0 as JulyGrossProfit,
               0 as AugGrossProfit,
               0 as SepGrossProfit,
               0 as OctGrossProfit,
               0 as NovGrossProfit,
               0 as DecGrossProfit,
               cast(0 as float) as TotalTarget,
               0 as TotalRevenue,
			   0 as TotalGrossProfit,
               null as AllocatedPer,
               null as RevenuePer,
               0 as RowId
        union
        select *
        from
        (
            select 4 as OrderBy,
                   lg.LoginId as SectionId,
                   lg.EmployeeName as SectionName,
                   'Sales' as SectionType,
                   isnull(st.JanTarget, 0) as JanTarget,
                   isnull(st.FebTarget, 0) as FebTarget,
                   isnull(st.MarchTarget, 0) as MarchTarget,
                   isnull(st.AprTarget, 0) as AprTarget,
                   isnull(st.MayTarget, 0) as MayTarget,
                   isnull(st.JuneTarget, 0) as JuneTarget,
                   isnull(st.JulyTarget, 0) as JulyTarget,
                   isnull(st.AugTarget, 0) as AugTarget,
                   isnull(st.SepTarget, 0) as SepTarget,
                   isnull(st.OctTarget, 0) as OctTarget,
                   isnull(st.NovTarget, 0) as NovTarget,
                   isnull(st.DecTarget, 0) as DecTarget,
                   (
                       select isnull(sum(resale), 0)
                       from tbKPIRevenueSummary
                       where Salesman = st.SalesManNo
                             and invMonth = 1
                             and YearNo = @YearNo
                   ) as JanRevenue,
                   (
                       select isnull(sum(resale), 0)
                       from tbKPIRevenueSummary
                       where Salesman = st.SalesManNo
                             and invMonth = 2
                             and YearNo = @YearNo
                   ) as FebRevenue,
                   (
                       select isnull(sum(resale), 0)
                       from tbKPIRevenueSummary
                       where Salesman = st.SalesManNo
                             and invMonth = 3
                             and YearNo = @YearNo
                   ) as MarchRevenue,
                   (
                       select isnull(sum(resale), 0)
                       from tbKPIRevenueSummary
                       where Salesman = st.SalesManNo
                             and invMonth = 4
                             and YearNo = @YearNo
                   ) as AprRevenue,
                   (
                       select isnull(sum(resale), 0)
                       from tbKPIRevenueSummary
                       where Salesman = st.SalesManNo
                             and invMonth = 5
                             and YearNo = @YearNo
                   ) as MayRevenue,
                   (
                       select isnull(sum(resale), 0)
                       from tbKPIRevenueSummary
                       where Salesman = st.SalesManNo
                             and invMonth = 6
                             and YearNo = @YearNo
                   ) as JuneRevenue,
                   (
                       select isnull(sum(resale), 0)
                       from tbKPIRevenueSummary
                       where Salesman = st.SalesManNo
                             and invMonth = 7
                             and YearNo = @YearNo
                   ) as JulyRevenue,
                   (
                       select isnull(sum(resale), 0)
                       from tbKPIRevenueSummary
                       where Salesman = st.SalesManNo
                             and invMonth = 8
                             and YearNo = @YearNo
                   ) as AugRevenue,
                   (
                       select isnull(sum(resale), 0)
                       from tbKPIRevenueSummary
                       where Salesman = st.SalesManNo
                             and invMonth = 9
                             and YearNo = @YearNo
                   ) as SepRevenue,
                   (
                       select isnull(sum(resale), 0)
                       from tbKPIRevenueSummary
                       where Salesman = st.SalesManNo
                             and invMonth = 10
                             and YearNo = @YearNo
                   ) as OctRevenue,
                   (
                       select isnull(sum(resale), 0)
                       from tbKPIRevenueSummary
                       where Salesman = st.SalesManNo
                             and invMonth = 11
                             and YearNo = @YearNo
                   ) as NovRevenue,
                   (
                       select isnull(sum(resale), 0)
                       from tbKPIRevenueSummary
                       where Salesman = st.SalesManNo
                             and invMonth = 12
                             and YearNo = @YearNo
                   ) as DecRevenue,
				   --gross profit
				   (
                       select isnull(sum(GrossProfit), 0)
                       from tbKPIRevenueSummary
                       where Salesman = st.SalesManNo
                             and invMonth = 1
                             and YearNo = @YearNo
                   ) as JanGrossProfit,
                   (
                       select isnull(sum(GrossProfit), 0)
                       from tbKPIRevenueSummary
                       where Salesman = st.SalesManNo
                             and invMonth = 2
                             and YearNo = @YearNo
                   ) as FebGrossProfit,
                   (
                       select isnull(sum(GrossProfit), 0)
                       from tbKPIRevenueSummary
                       where Salesman = st.SalesManNo
                             and invMonth = 3
                             and YearNo = @YearNo
                   ) as MarchGrossProfit,
                   (
                       select isnull(sum(GrossProfit), 0)
                       from tbKPIRevenueSummary
                       where Salesman = st.SalesManNo
                             and invMonth = 4
                             and YearNo = @YearNo
                   ) as AprGrossProfit,
                   (
                       select isnull(sum(GrossProfit), 0)
                       from tbKPIRevenueSummary
                       where Salesman = st.SalesManNo
                             and invMonth = 5
                             and YearNo = @YearNo
                   ) as MayGrossProfit,
                   (
                       select isnull(sum(GrossProfit), 0)
                       from tbKPIRevenueSummary
                       where Salesman = st.SalesManNo
                             and invMonth = 6
                             and YearNo = @YearNo
                   ) as JuneGrossProfit,
                   (
                       select isnull(sum(GrossProfit), 0)
                       from tbKPIRevenueSummary
                       where Salesman = st.SalesManNo
                             and invMonth = 7
                             and YearNo = @YearNo
                   ) as JulyGrossProfit,
                   (
                       select isnull(sum(GrossProfit), 0)
                       from tbKPIRevenueSummary
                       where Salesman = st.SalesManNo
                             and invMonth = 8
                             and YearNo = @YearNo
                   ) as AugGrossProfit,
                   (
                       select isnull(sum(GrossProfit), 0)
                       from tbKPIRevenueSummary
                       where Salesman = st.SalesManNo
                             and invMonth = 9
                             and YearNo = @YearNo
                   ) as SepGrossProfit,
                   (
                       select isnull(sum(GrossProfit), 0)
                       from tbKPIRevenueSummary
                       where Salesman = st.SalesManNo
                             and invMonth = 10
                             and YearNo = @YearNo
                   ) as OctGrossProfit,
                   (
                       select isnull(sum(GrossProfit), 0)
                       from tbKPIRevenueSummary
                       where Salesman = st.SalesManNo
                             and invMonth = 11
                             and YearNo = @YearNo
                   ) as NovGrossProfit,
                   (
                       select isnull(sum(GrossProfit), 0)
                       from tbKPIRevenueSummary
                       where Salesman = st.SalesManNo
                             and invMonth = 12
                             and YearNo = @YearNo
                   ) as DecGrossProfit,
				   -- gross profit
                   cast(0 as float) as TotalTarget,
                   0 as TotalRevenue,
				   0 as TotalGrossProfit,
                   null as AllocatedPer,
                   null as RevenuePer,
                   st.SalesTargetId as RowId
            from tbLogin lg
                inner join tbsalestargetfinal st
                    on st.SalesManNo = lg.LoginId
            where lg.TeamNo = @TeamTargetNo -- and lg.Inactive=0                           
                  and isnull(st.YearNo, year(getdate())) = @yearNo
        ) as dt
       )
    select *
    from CTE_SalesSummary
    ORDER BY orderby,
             CASE
                 WHEN SectionId = 0 THEN
                     0
                 ELSE
                     1
             END,
             SectionName

End




