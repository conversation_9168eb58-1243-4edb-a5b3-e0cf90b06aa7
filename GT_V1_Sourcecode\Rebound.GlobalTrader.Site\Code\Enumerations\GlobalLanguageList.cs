/******************************************************************************************************************
 * RJP 22.06.2009
 * This is the Enumeration of all Languages, with a function to return a language code
 * Note that if the application using this sets multi-language options
 * in the database (for User preferences, for example) the table must define the same ID numbers as here
 * 
 * Changes
 * ----------------------------------------------------------------------------------------------------------------
 * RP 22.06.2009: Created
*******************************************************************************************************************/
using System;
using System.Collections.Generic;
using System.Text;

namespace Rebound.GlobalTrader.Site.Enumerations {

	public class GlobalLanguage {

		public enum List {
			English = 1,
			German = 2,
			Spanish = 3,
			Italian = 4,
			Thai = 5,
			Afrikaans = 6,
			Belarusian = 7,
			Bulgarian = 8,
			Czech = 10,
			Welsh = 11,
			Danish = 12,
			German_Austria = 13,
			German_Switzerland = 14,
			Greek = 15,
			English_Australia = 16,
			English_Canada = 17,
			English_Ireland = 18,
			English_NewZealand = 19,
			English_UnitedStates = 20,
			English_SouthAfrica = 21,
			English_Zimbabwe = 22,
			Spanish_Argentina = 23,
			Spanish_Bolivia = 24,
			Spanish_Chile = 25,
			Spanish_Colombia = 26,
			Spanish_CostaRica = 27,
			Spanish_DominicanRepublic = 28,
			Spanish_Ecuador = 29,
			Spanish_Guatemala = 30,
			Spanish_Honduras = 31,
			Spanish_Mexico = 32,
			Spanish_Nicaragua = 33,
			Spanish_Panama = 34,
			Spanish_Peru = 35,
			Spanish_PuertoRico = 36,
			Spanish_Paraguay = 37,
			Spanish_ElSalvador = 38,
			Spanish_Uruguay = 39,
			Spanish_Venezuela = 40,
			Estonian = 41,
			Basque = 42,
			Finnish = 43,
			French_Belgium = 44,
			French_Canada = 45,
			French_Switzerland = 46,
			French = 47,
			French_Luxembourg = 48,
			French_Monaco = 49,
			Gujarati = 51,
			Hebrew = 52,
			Hindi = 53,
			Hrvatski_BosnaiHercegovina = 54,
			Croatian = 55,
			Hungarian = 56,
			Armenian = 57,
			Indonesian = 58,
			Icelandic = 59,
			Italian_Switzerland = 60,
			Japanese = 61,
			Georgian = 62,
			Kazakh = 63,
			Kannada = 64,
			Korean = 66,
			Konkani = 67,
			Kyrgyz = 69,
			Lithuanian = 70,
			Latvian = 71,
			Macedonian = 72,
			Mongolian = 73,
			Marathi = 74,
			Malay = 75,
			Maltese = 76,
			Dutch_Belgium = 77,
			Dutch = 78,
			Norwegian = 79,
			Punjabi = 80,
			Polish = 81,
			Portuguese_Brazil = 82,
			Portuguese = 83,
			Romanian = 84,
			Russian = 85,
			Sanskrit = 86,
			Slovak = 87,
			Slovenian = 88,
			Albanian = 89,
			Swedish = 91,
			Kiswahili_Kenya = 92,
			Syriac = 93,
			Tamil = 94,
			Telugu = 95,
			Turkish = 96,
			Tatar = 97,
			Ukrainian = 98,
			Urdu = 99,
			Vietnamese = 101,
			Chinese = 102,
			Chinese_HongKong = 103,
			Chinese_Macau = 104,
			Chinese_Singapore = 105,
			Chinese_Taiwan = 106
		}

		public static string GetLanguageCode(int intLanguage) {
			return GetLanguageCode((List)intLanguage);
		}

		public static string GetLanguageCode(List enmLanguage) {
			string str = "";
			switch (enmLanguage) {
				case List.English: str = "en-GB"; break;
				case List.German: str = "de-DE"; break;
				case List.Spanish: str = "es-ES"; break;
				case List.Italian: str = "it-IT"; break;
				case List.Thai: str = "th-TH"; break;
				case List.Afrikaans: str = "af-ZA"; break;
				case List.Belarusian: str = "be-BY"; break;
				case List.Bulgarian: str = "bg-BG"; break;
				case List.Czech: str = "cs-CZ"; break;
				case List.Welsh: str = "cy-GB"; break;
				case List.Danish: str = "da-DK"; break;
				case List.German_Austria: str = "de-AT"; break;
				case List.German_Switzerland: str = "de-CH"; break;
				case List.Greek: str = "el-GR"; break;
				case List.English_Australia: str = "en-AU"; break;
				case List.English_Canada: str = "en-CA"; break;
				case List.English_Ireland: str = "en-IE"; break;
				case List.English_NewZealand: str = "en-NZ"; break;
				case List.English_UnitedStates: str = "en-US"; break;
				case List.English_SouthAfrica: str = "en-ZA"; break;
				case List.English_Zimbabwe: str = "en-ZW"; break;
				case List.Spanish_Argentina: str = "es-AR"; break;
				case List.Spanish_Bolivia: str = "es-BO"; break;
				case List.Spanish_Chile: str = "es-CL"; break;
				case List.Spanish_Colombia: str = "es-CO"; break;
				case List.Spanish_CostaRica: str = "es-CR"; break;
				case List.Spanish_DominicanRepublic: str = "es-DO"; break;
				case List.Spanish_Ecuador: str = "es-EC"; break;
				case List.Spanish_Guatemala: str = "es-GT"; break;
				case List.Spanish_Honduras: str = "es-HN"; break;
				case List.Spanish_Mexico: str = "es-MX"; break;
				case List.Spanish_Nicaragua: str = "es-NI"; break;
				case List.Spanish_Panama: str = "es-PA"; break;
				case List.Spanish_Peru: str = "es-PE"; break;
				case List.Spanish_PuertoRico: str = "es-PR"; break;
				case List.Spanish_Paraguay: str = "es-PY"; break;
				case List.Spanish_ElSalvador: str = "es-SV"; break;
				case List.Spanish_Uruguay: str = "es-UY"; break;
				case List.Spanish_Venezuela: str = "es-VE"; break;
				case List.Estonian: str = "et-EE"; break;
				case List.Basque: str = "eu-ES"; break;
				case List.Finnish: str = "fi-FI"; break;
				case List.French_Belgium: str = "fr-BE"; break;
				case List.French_Canada: str = "fr-CA"; break;
				case List.French_Switzerland: str = "fr-CH"; break;
				case List.French: str = "fr-FR"; break;
				case List.French_Luxembourg: str = "fr-LU"; break;
				case List.French_Monaco: str = "fr-MC"; break;
				case List.Gujarati: str = "gu-IN"; break;
				case List.Hebrew: str = "he-IL"; break;
				case List.Hindi: str = "hi-IN"; break;
				case List.Hrvatski_BosnaiHercegovina: str = "hr-BA"; break;
				case List.Croatian: str = "hr-HR"; break;
				case List.Hungarian: str = "hu-HU"; break;
				case List.Armenian: str = "hy-AM"; break;
				case List.Indonesian: str = "id-ID"; break;
				case List.Icelandic: str = "is-IS"; break;
				case List.Italian_Switzerland: str = "it-CH"; break;
				case List.Japanese: str = "ja-JP"; break;
				case List.Georgian: str = "ka-GE"; break;
				case List.Kazakh: str = "kk-KZ"; break;
				case List.Kannada: str = "kn-IN"; break;
				case List.Korean: str = "ko-KR"; break;
				case List.Konkani: str = "kok-IN"; break;
				case List.Kyrgyz: str = "ky-KG"; break;
				case List.Lithuanian: str = "lt-LT"; break;
				case List.Latvian: str = "lv-LV"; break;
				case List.Macedonian: str = "mk-MK"; break;
				case List.Mongolian: str = "mn-MN"; break;
				case List.Marathi: str = "mr-IN"; break;
				case List.Malay: str = "ms-MY"; break;
				case List.Maltese: str = "mt-MT"; break;
				case List.Dutch_Belgium: str = "nl-BE"; break;
				case List.Dutch: str = "nl-NL"; break;
				case List.Norwegian: str = "nn-NO"; break;
				case List.Punjabi: str = "pa-IN"; break;
				case List.Polish: str = "pl-PL"; break;
				case List.Portuguese_Brazil: str = "pt-BR"; break;
				case List.Portuguese: str = "pt-PT"; break;
				case List.Romanian: str = "ro-RO"; break;
				case List.Russian: str = "ru-RU"; break;
				case List.Sanskrit: str = "sa-IN"; break;
				case List.Slovak: str = "sk-SK"; break;
				case List.Slovenian: str = "sl-SI"; break;
				case List.Albanian: str = "sq-AL"; break;
				case List.Swedish: str = "sv-SE"; break;
				case List.Kiswahili_Kenya: str = "sw-KE"; break;
				case List.Syriac: str = "syr-SY"; break;
				case List.Tamil: str = "ta-IN"; break;
				case List.Telugu: str = "te-IN"; break;
				case List.Turkish: str = "tr-TR"; break;
				case List.Tatar: str = "tt-RU"; break;
				case List.Ukrainian: str = "uk-UA"; break;
				case List.Urdu: str = "ur-PK"; break;
				case List.Vietnamese: str = "vi-VN"; break;
				case List.Chinese: str = "zh-CN"; break;
				case List.Chinese_HongKong: str = "zh-HK"; break;
				case List.Chinese_Macau: str = "zh-MO"; break;
				case List.Chinese_Singapore: str = "zh-SG"; break;
				case List.Chinese_Taiwan: str = "zh-TW"; break;
			}
			return str;
		}
	}
}