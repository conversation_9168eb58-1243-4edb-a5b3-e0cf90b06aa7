SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

IF object_id('ufn_get_supplier_part_status', 'FN') IS NOT NULL
BEGIN
    DROP FUNCTION [dbo].[ufn_get_supplier_part_status]
END
GO
-- =============================================
-- Author:		An.TranTan
-- Create date: 06-May-2024
-- Description:	[US-201934]Compare import supplier part with customer requirement part to check if is valid
-- Return:		Y: valid, N: invalid
-- =============================================
CREATE FUNCTION [dbo].[ufn_get_supplier_part_status] 
(
	@SupplierPart nvarchar(30),
	@RequirementPart nvarchar(30)
)
RETURNS VARCHAR(3)
AS
BEGIN
	DECLARE @PartStatus VARCHAR(3) = 'N';
	DECLARE @SubPartLength TINYINT = 3;

	--Split the import supplier part into substrings of 3 consecutive characters.
	DECLARE @tempSplitPart TABLE(
		Position INT NULL,
		SubPart NVARCHAR(5) NULL
	);
	INSERT INTO @tempSplitPart 
	(
		Position, 
		SubPart
	)
	SELECT 
		number,
	    SUBSTRING(@SupplierPart, number, @SubPartLength)
	FROM
	    master.dbo.spt_values	--system table used for generating sequences of numbers
	WHERE 
	    type = 'P'
		AND number > 0
	    AND number <= LEN(@SupplierPart) - (@SubPartLength - 1)

	--The part is accepted if the requirement part contains at least 3 consecutive characters of import supplier part
	IF (EXISTS (SELECT TOP 1 1 FROM @tempSplitPart WHERE @RequirementPart LIKE '%' + SubPart + '%'))
	BEGIN
		SET @PartStatus = 'Y'
	END

	RETURN @PartStatus;
END
GO


