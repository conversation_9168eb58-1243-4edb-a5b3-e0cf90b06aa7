Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.CompanyGlobalSalesPDetails_Delete=function(n){Rebound.GlobalTrader.Site.Controls.Forms.CompanyGlobalSalesPDetails_Delete.initializeBase(this,[n]);this._intManufacturerLinkID=-1};Rebound.GlobalTrader.Site.Controls.Forms.CompanyGlobalSalesPDetails_Delete.prototype={get_intManufacturerLinkID:function(){return this._intManufacturerLinkID},set_intManufacturerLinkID:function(n){this._intManufacturerLinkID!==n&&(this._intManufacturerLinkID=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Forms.CompanyGlobalSalesPDetails_Delete.callBaseMethod(this,"initialize");this.addShown(Function.createDelegate(this,this.setupEvents))},dispose:function(){this.isDisposed||(this._intManufacturerLinkID=null,Rebound.GlobalTrader.Site.Controls.Forms.CompanyGlobalSalesPDetails_Delete.callBaseMethod(this,"dispose"))},setupEvents:function(){this._ctlConfirm=this.getFieldComponent("ctlConfirm");this._ctlConfirm.addClickYesEvent(Function.createDelegate(this,this.yesClicked));this._ctlConfirm.addClickNoEvent(Function.createDelegate(this,this.noClicked))},yesClicked:function(){var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/CompanyManufacturers");n.set_DataObject("CompanyManufacturers");n.set_DataAction("Delete");n.addParameter("id",this._intManufacturerLinkID);n.addDataOK(Function.createDelegate(this,this.saveDeleteComplete));n.addError(Function.createDelegate(this,this.saveDeleteError));n.addTimeout(Function.createDelegate(this,this.saveDeleteError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null;this.onSave()},noClicked:function(){this.showSaving(!1);this.onNotConfirmed()},saveDeleteError:function(n){this._strErrorMessage=n._errorMessage;this.onSaveError()},saveDeleteComplete:function(n){this.showSaving(!1);n._result.Result==!0?this.onSaveComplete():(this._strErrorMessage=n._errorMessage,this.onSaveError())}};Rebound.GlobalTrader.Site.Controls.Forms.CompanyGlobalSalesPDetails_Delete.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.CompanyGlobalSalesPDetails_Delete",Rebound.GlobalTrader.Site.Controls.Forms.Base,Sys.IDisposable);