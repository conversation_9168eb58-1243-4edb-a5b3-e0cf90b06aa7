﻿
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

/*     
========================================================================================================================    
TASK          UPDATED BY      DATE          ACTION    DESCRIPTION    
[US-215434]	  Phuc Hoang	  06-Nov-2024	Update	  Lytica Price should apply fuzzy logic for inserting & displaying
========================================================================================================================   
*/  

CREATE OR ALTER PROCEDURE [dbo].[usp_autosearch_LyticaManufacturer]              
(     
	@NameSearch nvarchar(50),
	@PartNo NVARCHAR(256) = NULL,
	@ShowInactive bit   
)
AS         
BEGIN     
	SELECT DISTINCT  
		lytica.LyticaAPIId AS ManufacturerId  
		,lytica.Manufacturer AS ManufacturerName 
	FROM dbo.tbLyticaAPI lytica

	WHERE lytica.OriginalPartSearched = @PartNo 
	AND lytica.Manufacturer LIKE @NameSearch
	AND (ISNULL(lytica.AveragePrice, 0) + ISNULL(lytica.TargetPrice, 0) + ISNULL(lytica.MarketLeading, 0)) > 0
	--ORDER BY ManufacturerName      

END   
GO
