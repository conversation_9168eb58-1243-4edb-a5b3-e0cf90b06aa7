///<reference name="MicrosoftAjax.js" />
///<reference path="~/Common/Data/Data.js" />
///<reference path="~/Common/Functions/Functions.js" />
///<reference path="~/Controls/IconButton/IconButton.js" />
//-----------------------------------------------------------------------------------------
// RP 16.03.2011:
// - change the way values are set, improving robustness (again)
//
// RP 17.03.2010:
// - change the way values are set, improving robustness
//
// RP 11.03.2010:
// - add isLoading function
//
// RP 06.01.2010:
// - add full dispose
//
// RP 26.10.2009:
// - Fix glitch with loading showing on static dropdowns
//
// RP 06.10.2009:
// - Copy approach from AutoSearches and allow for data call cancelling
//Marker     changed by     Date         Remarks
//[001]      Vinay         16/11/2016    Logout, if user change login from other tab
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");

Rebound.GlobalTrader.Site.Controls.DropDowns.Base = function(element) { 
	Rebound.GlobalTrader.Site.Controls.DropDowns.Base.initializeBase(this, [element]);
	this._strNoValue_Value = "";
	this._strNoValue_Text = "";
	this._varCurrentValue = null;
	this._intRefreshTimeout = 0;
	this._blnFirstTimeIn = true;
	this._strDataPathModification = "";
	this._aryExtraText = [];
	this._blnInDataCall = false;
	this._varOriginalInitialValue;
	this._objData;
};

Rebound.GlobalTrader.Site.Controls.DropDowns.Base.prototype = {

	get_lbx: function() { return this._lbx; }, 	set_lbx: function(value) { if (this._lbx !== value)  this._lbx = value; }, 
	get_strDataPathModification: function() {return this._strDataPathModification; }, 	set_strDataPathModification: function(value) { if (this._strDataPathModification !== value)  this._strDataPathModification = value; }, 
	get_hypAdd: function() { return this._hypAdd; }, 	set_hypAdd: function(value) { if (this._hypAdd !== value)  this._hypAdd = value; }, 
	get_pnlAddForm: function() { return this._pnlAddForm; }, 	set_pnlAddForm: function(value) { if (this._pnlAddForm !== value)  this._pnlAddForm = value; }, 
	get_txtAdd: function() { return this._txtAdd; }, 	set_txtAdd: function(value) { if (this._txtAdd !== value)  this._txtAdd = value; }, 
	get_ibtnAdd: function() { return this._ibtnAdd; }, 	set_ibtnAdd: function(value) { if (this._ibtnAdd !== value)  this._ibtnAdd = value; }, 
	get_ibtnCancel: function() { return this._ibtnCancel; }, 	set_ibtnCancel: function(value) { if (this._ibtnCancel !== value)  this._ibtnCancel = value; }, 
	get_blnCanAdd: function() { return this._blnCanAdd; }, 	set_blnCanAdd: function(value) { if (this._blnCanAdd !== value)  this._blnCanAdd = value; }, 
	get_blnCanRefresh: function() { return this._blnCanRefresh; }, 	set_blnCanRefresh: function(value) { if (this._blnCanRefresh !== value)  this._blnCanRefresh = value; }, 
	get_initialValue: function() { return this._initialValue; }, 	set_initialValue: function(value) { if (this._initialValue !== value)  this._initialValue = value; }, 		
	get_pnlFormInner: function() { return this._pnlFormInner; }, 	set_pnlFormInner: function(value) { if (this._pnlFormInner !== value)  this._pnlFormInner = value; }, 
	get_pnlAddError: function() { return this._pnlAddError; }, 	set_pnlAddError: function(value) { if (this._pnlAddError !== value)  this._pnlAddError = value; }, 
	get_pnlAddSaving: function() { return this._pnlAddSaving; }, 	set_pnlAddSaving: function(value) { if (this._pnlAddSaving !== value)  this._pnlAddSaving = value; }, 
	get_hypRefresh: function() { return this._hypRefresh; }, 	set_hypRefresh: function(value) { if (this._hypRefresh !== value)  this._hypRefresh = value; }, 
	get_blnIncludeNoValue: function() { return this._blnIncludeNoValue; }, 	set_blnIncludeNoValue: function(value) { if (this._blnIncludeNoValue !== value)  this._blnIncludeNoValue = value; }, 
	get_strNoValue_Value: function() { return this._strNoValue_Value; }, 	set_strNoValue_Value: function(value) { if (this._strNoValue_Value !== value)  this._strNoValue_Value = value; }, 
	get_strNoValue_Text: function() { return this._strNoValue_Text; }, 	set_strNoValue_Text: function(value) { if (this._strNoValue_Text !== value)  this._strNoValue_Text = value; }, 
	get_blnGetDataStraightAway: function() { return this._blnGetDataStraightAway; }, 	set_blnGetDataStraightAway: function(value) { if (this._blnGetDataStraightAway !== value)  this._blnGetDataStraightAway = value; }, 
	get_intDropDownID: function() { return this._intDropDownID; }, 	set_intDropDownID: function(value) { if (this._intDropDownID !== value)  this._intDropDownID = value; }, 
	get_varCurrentValue: function () { return this._varCurrentValue; }, set_varCurrentValue: function (value) { if (this._varCurrentValue !== value) this._varCurrentValue = value; },
    //[001] code start
	get_intCurrentLogin: function () { return this._intCurrentLogin; }, set_intCurrentLogin: function (value) { if (this._intCurrentLogin !== value) this._intCurrentLogin = value; },
    //[001] code end

	addSetupDataCall: function(handler) { this.get_events().addHandler("SetupDataCall", handler); },
	removeSetupDataCall: function(handler) { this.get_events().removeHandler("SetupDataCall", handler); },
	onSetupDataCall: function() { 
		var handler = this.get_events().getHandler("SetupDataCall");
		if (handler) handler(this, Sys.EventArgs.Empty); 
	},
	
	addGetDataOK: function(handler) { this.get_events().addHandler("GetDataOK", handler); },
	removeGetDataOK: function(handler) { this.get_events().removeHandler("GetDataOK", handler); },
	onGetDataOK: function() { 
		var handler = this.get_events().getHandler("GetDataOK");
		if (handler) handler(this, Sys.EventArgs.Empty); 
	},

	addChanged: function(handler) { this.get_events().addHandler("Changed", handler); },
	removeChanged: function(handler) { this.get_events().removeHandler("Changed", handler); },
	onChanged: function() { 
		var handler = this.get_events().getHandler("Changed");
		if (handler) handler(this, Sys.EventArgs.Empty); 
	},
		
	addShowAddForm: function(handler) { this.get_events().addHandler("ShowAddForm", handler); },
	removeShowAddForm: function(handler) { this.get_events().removeHandler("ShowAddForm", handler); },
	onShowAddForm: function() { 
		var handler = this.get_events().getHandler("ShowAddForm");
		if (handler) handler(this, Sys.EventArgs.Empty); 
	},
	
	addAdd: function(handler) { this.get_events().addHandler("Add", handler); },
	removeAdd: function(handler) { this.get_events().removeHandler("Add", handler); },
	onAdd: function() { 
		var handler = this.get_events().getHandler("Add");
		if (handler) handler(this, Sys.EventArgs.Empty); 
	},
	
	addRefresh: function(handler) { this.get_events().addHandler("Refresh", handler); },
	removeRefresh: function(handler) { this.get_events().removeHandler("Refresh", handler); },
	onRefresh: function() { 
		var handler = this.get_events().getHandler("Refresh");
		if (handler) handler(this, Sys.EventArgs.Empty); 
	},
	
	addGotDataComplete: function(handler) { this.get_events().addHandler("GotDataComplete", handler); },
	removeGotDataComplete: function(handler) { this.get_events().removeHandler("GotDataComplete", handler); },
	onGotDataComplete: function() { 
		var handler = this.get_events().getHandler("GotDataComplete");
		if (handler) handler(this, Sys.EventArgs.Empty); 
	},

    initialize: function () {
		if (this._blnCanRefresh) $addHandler(this._hypRefresh, "click", Function.createDelegate(this, this.refresh));
		if (this._blnCanAdd) {
			$addHandler(this._hypAdd, "click", Function.createDelegate(this, this.showAddForm));
			$R_IBTN.addClick(this._ibtnAdd, Function.createDelegate(this, this.onAdd));
			$R_IBTN.addClick(this._ibtnCancel, Function.createDelegate(this, this.cancelAdd));
		}
		$addHandler(this._lbx, "change", Function.createDelegate(this, this.changeDropDown));
		this._blnFirstTimeIn = true;
		this._varOriginalInitialValue = this._initialValue;
		this.addRefresh(Function.createDelegate(this, this.getData));
		if (this._blnGetDataStraightAway) this.getData();
		Rebound.GlobalTrader.Site.Controls.DropDowns.Base.callBaseMethod(this, "initialize");
	},

	dispose: function() { 
		if (this.isDisposed) return;
		if (this.get_element()) $clearHandlers(this.get_element());
		if (this._lbx) $clearHandlers(this._lbx);
		if (this._hypAdd) $clearHandlers(this._hypAdd);
		if (this._hypRefresh) $clearHandlers(this._hypRefresh);
		if (this._objData) this._objData.dispose();
		this._strNoValue_Value = null;
		this._strNoValue_Text = null;
		this._intRefreshTimeout = null;
		this._blnFirstTimeIn = null;
		this._strDataPathModification = null;
		this._aryExtraText = null;
		this._blnInDataCall = null;
		this._varOriginalInitialValue = null;
		this._objData = null;
		this._lbx = null;
		this._hypAdd = null;
		this._pnlAddForm = null;
		this._txtAdd = null;
		this._ibtnAdd = null;
		this._ibtnCancel = null;
		this._pnlFormInner = null;
		this._pnlAddError = null;
		this._pnlAddSaving = null;
		this._hypRefresh = null;
		this._objData = null;
		this._varCurrentValue = null;
		this._intCurrentLogin = null;
		Rebound.GlobalTrader.Site.Controls.DropDowns.Base.callBaseMethod(this, "dispose");
		this.isDisposed = true;
	},
	
	getData: function() {
		this.showLoading();
		
		//cancel any current data calls if there are any and set the flag to show we're in a data call
		this.cancelDataCall();
		this._blnInDataCall = true;
		
		//setup the data object
		this._objData = new Rebound.GlobalTrader.Site.Data();
		this._objData.addDataOK(Function.createDelegate(this, this.getDataOK));
		this._objData.addError(Function.createDelegate(this, this.getDataError));
		this._objData.addTimeout(Function.createDelegate(this, this.getDataError));					
		this._objData.addParameter("DDID", this._intDropDownID);
	    //[001] code start
        this._objData.addParameter("cLogIn", this._intCurrentLogin);
        var dateTimeMilliSec = new Date();
        this._objData.addParameter("DateTime", dateTimeMilliSec.toISOString());
	    //[001] code end

		//raise event for derived classes to add parameters etc
		this.onSetupDataCall();
		
		//submit the data action
		$R_DQ.addToQueue(this._objData, true);
		$R_DQ.processQueue();
	},
	
    getDataOK: function () {
		$R_FN.clearListBox(this._lbx);
		if (this._blnIncludeNoValue) this.addNoValueOption();
		
		//allow superclasses to add the data
		this.onGetDataOK();
		
		//set the correct value
		if (this._blnFirstTimeIn) {
			this.finishSetValue(this._initialValue);
			this._blnFirstTimeIn = false;
		} else {
			this.finishSetValue(this._varCurrentValue);
		}
		
		//check we're in a refresh event
		if (this._blnRefreshEvent) {
			this._blnRefreshEvent = false;
			this._lbx.selectedIndex = this._intIndexToSet;
			this.setValue(this._lbx.options[this._intIndexToSet].value);
		}
		
		//finish off
		this._blnInDataCall = false;
		this.onGotDataComplete();
	},
	
	getDataError: function(args) { 
		this.showError(args);
	},
	
	setValue: function(varValue, strText) {
		this.setInitialValue(varValue, strText);
		this._blnFirstTimeIn = false;
		this._varCurrentValue = varValue;
		if (this._lbx.options.length > 0 && !this._blnInDataCall) this.finishSetValue(varValue);
	},
	
	finishSetValue: function(varValue) {
		this._varCurrentValue = varValue;
		for (var i = 0, l = this._lbx.options.length; i < l; i++) {
			this._lbx.options[i].selected = (this._lbx.options[i].value == varValue);
		}
	},
	
	changeDropDown: function() {
		this._varCurrentValue = this._lbx.value;
		this.onChanged();
	},
	
	setValueFromText: function(strText) {
		for (var i = 0, l = this._lbx.options.length; i < l; i++) {
			this._lbx.options[i].selected = (this._lbx.options[i].text.toString().trim().toUpperCase() == strText.toString().trim().toUpperCase());
		}
	},
			
    setInitialValue: function (varValue, strText) {
		this._blnFirstTimeIn = true;
		if (varValue === "" || varValue == null) {
			if (!this._blnIncludeNoValue) {
				varValue = this._varOriginalInitialValue;
			} else {
				varValue = this._strNoValue_Value;
			}
		}
		this._initialValue = varValue;
		this._strInitialText = strText;
	},
	
	getValue: function() {
		if (this.isSetAsNoValue()) return null;
		if (typeof(this._lbx.value) == "undefined") return null;
		if (this.isLoading()) return null;
		return this._varCurrentValue;
	},
	
	getText: function() {
		if (this.isSetAsNoValue()) return "";
		return (this._lbx.options[this._lbx.selectedIndex].text);
	},
	
	getExtraText: function() {
		var str = this._aryExtraText[this._lbx.selectedIndex];
		if (typeof(str) == "undefined") str = "";
		return str.trim();
	},
		
	addNoValueOption: function(strDesc, strID, blnSelected) {
		if (!this._blnIncludeNoValue) return;
		$R_FN.addOptionToListBox(this._lbx, this._strNoValue_Text, this._strNoValue_Value, true);
		Array.add(this._aryExtraText, "");
	},
	
	addOption: function(strText, strID, strExtraText) {
		var blnSelected = false;
		if (this._strInitialText) blnSelected = (this._strInitialText.toString().trim().toUpperCase() == strText.toString().trim().toUpperCase());
		if (this._initialValue) blnSelected = (this._initialValue == strID);
		$R_FN.addOptionToListBox(this._lbx, $R_FN.setCleanTextValue(strText), strID, blnSelected);
		if (typeof(strExtraText) == "undefined" || strExtraText == null) strExtraText = "";
		Array.add(this._aryExtraText, strExtraText.toString());
	},

	showLoading: function() {
		$R_FN.clearListBox(this._lbx);
		Array.clear(this._aryExtraText);
		$R_FN.addOptionToListBox(this._lbx, String.format("< {0} >", $R_RES.Loading.toLowerCase()), "::loading::", true);
	},
	
	showAddForm: function() {
		if (this._blnFormShown) return;
		$R_FN.showElement(this._pnlAddForm, true);
		this.onShowAddForm();
		this._blnFormShown = true;
	},
	
	hideAddForm: function() {
		if (!this._blnFormShown) return;
		$R_FN.showElement(this._pnlAddForm, false);
		this._blnFormShown = false;
	},
	
	showError: function(args) { 
		var strMsg = $R_RES.Error;
		if (args != null && typeof(args) != "undefined") strMsg = args._errorMessage;
		$R_FN.clearListBox(this._lbx);
		$R_FN.addOptionToListBox(this._lbx, String.format("< {0} >", $R_RES.Error.toLowerCase()), strMsg, true);
		this._lbx.options[0].className = "error";
	},
	
	cancelAdd: function() {
		this.hideAddForm();
	},
	
	clearDropDown: function() {
		this._strInitialText = null;
		this._initialValue = null;
		Array.clear(this._aryExtraText);
		this.setToNoValue();
	},
	
	setToNoValue: function() {
		this.setValue(this._strNoValue_Value);
	},
	
	isSetAsNoValue: function() {
		return (this._lbx.value == this._strNoValue_Value);
	},
	
	isDataAlreadyGiven: function(strDesc) {
		var blnFound = false;
		for (var i = 0, l = this._lbx.options.length; i < l; i++) {
			if (strDesc.toString().trim() == this._lbx.options[i].text.toString().trim()) {
				blnFound = true;
				break;
			}
		}
		return blnFound;
	},
	
	isIDAlreadyGiven: function(strID) {
		var blnFound = false;
		for (var i = 0, l = this._lbx.options.length; i < l; i++) {
			if (strID.toString().trim() == this._lbx.options[i].value.toString().trim()) {
				blnFound = true;
				break;
			}
		}
		return blnFound;
	},
	
	showAddDataError: function(args) {
		$R_FN.showElement(this._pnlAddSaving, false);
		$R_FN.showElement(this._pnlAddError, true);
		$R_FN.showElement(this._pnlFormInner, false);
		$R_FN.setInnerHTML(this._pnlAddError, args._errorMessage);
	},
	
	refresh: function() {
		if (this._blnInDataCall) return;
		this._blnRefreshEvent = true;
		this._intIndexToSet = this._lbx.selectedIndex;
		this.showLoading();
		this.onRefresh();
	},
	
    reset: function () {
		this._lbx.value = this._initialValue;
	},
	
	countOptions: function() {
		var intCount = this._lbx.options.length;
		if (this._blnIncludeNoValue) intCount -= 1;
		return intCount;
	},
	
	cancelDataCall: function() {
		this._blnInDataCall = false;
		if (this._objData) this._objData.cancel();
	},
	
	isLoading: function() {
		return this._blnInDataCall;
	}

};

Rebound.GlobalTrader.Site.Controls.DropDowns.Base.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.Base", Sys.UI.Control, Sys.IDisposable);