﻿  
/*===========================================================================================    
TASK            UPDATED BY       DATE         ACTION      DESCRIPTION    
[US-209544]     CuongDox         17-Sep-2024  CREATE      Add table to store files uploaded  
===========================================================================================    
*/  
  
CREATE  OR ALTER PROC [dbo].[usp_insert_HUBOfferImportLargeFile] (  
 @original_file_name VARCHAR(255) ,  
    @generated_file_name VARCHAR(255) ,  
 @generated_error_file VARCHAR(255),  
    @status VARCHAR(50),  
 @ClientId INT,  
 @UpdatedBy INT,  
 @RecordCount INT OUTPUT  
)  
AS  
  
BEGIN  
 BEGIN TRANSACTION;  
  DECLARE @UtilityLogId INT  
  
  insert into BorisGlobalTraderImports.dbo.tbUtilityLog (FileName,UtilityType,Clientid,LoginNo,DLUP,iRowCount)        
  values (@original_file_name,9,@ClientId,@UpdatedBy,getdate(),0)   
  
  SET @UtilityLogId = SCOPE_IDENTITY();  
  
  INSERT INTO BorisGlobalTraderImports.dbo.tbHUBOfferImportLargeFile   
  (OriginalFileName,  
  GeneratedFileName ,  
  GeneratedErrorFile ,  
  UtilityLogId,  
  status,  
  ClientId,  
  UpdatedBy)  
  VALUES (@original_file_name,  
  @generated_file_name,  
  @generated_error_file,  
  @UtilityLogId,  
  @status,  
  @ClientId,  
  @UpdatedBy)  
  
  SET @RecordCount = @@ROWCOUNT  
 COMMIT TRANSACTION;  
END  
  