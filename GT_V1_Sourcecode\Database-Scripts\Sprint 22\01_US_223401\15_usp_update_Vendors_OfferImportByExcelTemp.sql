﻿
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

/*
=========================================================================================================================================================
TASK			UPDATED BY       DATE				ACTION		DESCRIPTION  
[US-223401]     Phuc Hoang		 20-Feb-2025		CREATE		IPO - Bulk Offer Import tool - Apply fuzzy matching and rationalize data before importing
=========================================================================================================================================================
*/
CREATE OR ALTER PROCEDURE [dbo].[usp_update_Vendors_OfferImportByExcelTemp] (
	@ImportFileId INT = 0,
    @IncorrectVendor NVARCHAR(MAX),
	@NewVendor NVARCHAR(MAX),
	@ClientNo INT = 0,
	@UpdatedBy INT,
	@RecordCount INT OUTPUT
)
AS
BEGIN
	IF (@IncorrectVendor = '_Blank_')
	BEGIN
		SET @IncorrectVendor = ''
	END

    UPDATE BorisGlobalTraderimports.dbo.tbOfferImportByExcelTemp
    SET
		UpdatedBy = @UpdatedBy,
		Vendor = @NewVendor
    WHERE HUBOfferImportLargeFileID = @ImportFileId AND ISNULL(Vendor, '') = @IncorrectVendor AND ClientNo = @ClientNo;

	SELECT @RecordCount = @@ROWCOUNT 
END
GO

