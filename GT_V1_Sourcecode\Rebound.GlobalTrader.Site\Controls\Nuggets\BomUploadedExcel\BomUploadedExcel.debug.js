///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 03.08.2011:
// - add permission to edit fields after authorisation
//  
// SK 05.05.2010:
// - add permission to edit shipping costs
//  
// RP 06.01.2010:
// - fully dispose everything
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Nuggets");

Rebound.GlobalTrader.Site.Controls.Nuggets.BomUploadedExcel = function (element) {
    Rebound.GlobalTrader.Site.Controls.Nuggets.BomUploadedExcel.initializeBase(this, [element]);
    this.intBomId = 0;
};

Rebound.GlobalTrader.Site.Controls.Nuggets.BomUploadedExcel.prototype = {
    get_pnlPDFDocuments: function () { return this._pnlPDFDocuments; }, set_pnlPDFDocuments: function (value) { if (this._pnlPDFDocuments !== value) this._pnlPDFDocuments = value; },
    get_strSectionName: function () { return this._strSectionName; }, set_strSectionName: function (value) { if (this._strSectionName !== value) this._strSectionName = value; },
    get_intBomId: function () { return this._intBomId; }, set_intBomId: function (value) { if (this._intBomId !== value) this._intBomId = value; },
    
    
    initialize: function () {
        Rebound.GlobalTrader.Site.Controls.Nuggets.BomUploadedExcel.callBaseMethod(this, "initialize");
        this.addRefreshEvent(Function.createDelegate(this, this.showPDFPanel));
    },
    dispose: function () {
        if (this.isDisposed) return;
        this._pnlPDFDocuments = null;
        this._intCountPDF = null;
        Rebound.GlobalTrader.Site.Controls.Nuggets.BomUploadedExcel.callBaseMethod(this, "dispose");
    },
    getData: function () {
        this.getData_Start();
        this._intCountPDF == 0;
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/BOMImport");
        obj.set_DataObject("BOMImport");
        obj.set_DataAction("GetBomUploadedFiles");
        obj.addParameter("BomId", this._intBomId);
        obj.addDataOK(Function.createDelegate(this, this.getDataOK));
        obj.addError(Function.createDelegate(this, this.getDataError));
        obj.addTimeout(Function.createDelegate(this, this.getDataError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },
    getDataOK: function (args) {
        var res = args._result;
        var result = args._result;
        $R_FN.setInnerHTML(this._pnlPDFDocuments, "");
        var iconpath = result.IconPath;
        var strPDF = "";
        if (result.Items) {
            for (var i = 0; i < result.Items.length; i++) {
                var row = result.Items[i];
                strPDF += "<div class=\"pdfDocument\">";
                //strPDF += String.format("<div class=\"pdfDocumentDelete\" onclick=\"$find('{0}').deletePDF({1},'{2}');\">&nbsp;</div>", this._element.id, row.ID, row.FileName);
                if (row.FilePath == "#")
                    strPDF += String.format("<a href=\"{0}\" target=\"_blank\"><img width=\"80px\" height=\"80px\" id=\"{1}_img{2}\" src=\"{3}\" border=\"0\" style=\"pointer-events: none;\"/></a>", row.FilePath, this._element.id, i, iconpath);
                else
                    //strPDF += String.format("<a href=\"{0}\" target=\"_blank\"><img width=\"80px\" height=\"80px\" id=\"{1}_img{2}\" src=\"{3}\" border=\"0\" /></a>", row.FilePath, this._element.id, i, iconpath);
                   strPDF += String.format("<a href=\"{0}\" ><img width=\"80px\" id=\"{1}_img{2}\" src=\"{3}\" border=\"0\" onclick=\"$find('{4}').OpenPDF('{5}','{6}');\" /></a>", "javascript:void(0);", this._element.id, i, iconpath, this._element.id, row.Section, row.GeneratedFileName);
                strPDF += "<div class=\"pdfDocumentCaption\">";
                if (row.Caption) strPDF += row.Caption + "<br />";
                strPDF += row.Date;
                if (row.By) strPDF += "<br />" + row.By;
                strPDF += "</div>";
                strPDF += "</div>";
                row = null;
            }
            this._intCountPDF = result.Items.length;
        }
        $R_FN.setInnerHTML(this._pnlPDFDocuments, strPDF);
        this.getDataOK_End();
        this.showNoData(this._intCountPDF == 0);
        this.showPanel(this._intCountPDF > 0)

    },
    getDataError: function (args) {
        this.showError(true, args.get_ErrorMessage());
    },
    deletePDF: function (intPDF, FileName) {
        this._frmDelete._intPDFDocumentID = intPDF;
        this._frmDelete._pdfFileName = FileName;
        this.showDeleteForm();
    },
    showAddForm: function () {
        this.showForm(this._frmAdd, true);
    },
    hideAddForm: function () {
        this.showForm(this._frmAdd, false);
        this.showNoData(this._intCountPDF == 0);
    },
    saveAddComplete: function () {
        this.hideAddForm();
        this.showSavedOK(true, $R_RES.ChangesSavedSuccessfully);
        this._IsPDFAvailable = true;
        this.getData();
    },

    saveAddError: function () {
        this.showError(true, this._frmAdd._strErrorMessage);
    },

    showDeleteForm: function () {
        this.showForm(this._frmDelete, true);
    },

    hideDeleteForm: function () {
        this.showForm(this._frmDelete, false);
        this.showNoData(this._intCountPDF == 0);
    },

    cancelDelete: function () {
        this.hideDeleteForm();
    },

    saveDeleteComplete: function () {
        this.hideDeleteForm();
        this.showSavedOK(true, $R_RES.ChangesSavedSuccessfully);
        this.getData();
    },

    showPDFPanel: function () {
        this._IsPDFAvailable = true;
        this.showPanel(true);
        //this.getMaxPDFDocument();
        this.getData();
    },

    showPanel: function (bln) {
        $R_FN.showElement(this._pnlPDFDocuments, bln);
    },

    pdfNotAvailable: function (bln) {
        if (bln) {
            $R_FN.setInnerHTML(this._pnlPDFDocuments, "");
            this.getDataOK_End();
            this._intCountPDF = 0;
            this.showNoData(true);

        }
    },
        OpenPDF: function (strsection, FileName) {
            var obj = new Rebound.GlobalTrader.Site.Data();
            obj.set_PathToData("controls/Nuggets/BOMImport");
            obj.set_DataObject("BOMImport");
            obj.set_DataAction("GetPDFAccessURL");
            obj.addParameter("section", strsection);
            obj.addParameter("filename", FileName);
            obj.addDataOK(Function.createDelegate(this, this.getOpenPDFOK));
            obj.addError(Function.createDelegate(this, this.getOpenPDFError));
            obj.addTimeout(Function.createDelegate(this, this.getOpenPDFError));
            $R_DQ.addToQueue(obj);
            $R_DQ.processQueue();
            obj = null;
        },
        getOpenPDFOK: function (args) {
            var res = args._result;
            var result = args._result;
            // alert(result.bothirl);
            window.open(this.setCleanTextBlobURL(result.bothirl), '_blank');

        },
        getOpenPDFError: function (args) {
            this.showError(true, args.get_ErrorMessage());
        },
        setCleanTextBlobURL: function (strIn, blnReplaceLineBreaks) {
            //
            if (typeof (strIn) == "undefined") strIn = "";
            strIn = (strIn + "").trim();
            strIn = strIn.replace(/(:PLUS:)/g, "+");
            strIn = strIn.replace(/(:AND:)/g, "&");
            strIn = strIn.replace(/[+]/g, "%2B");

            return strIn;
        }
    

};
Rebound.GlobalTrader.Site.Controls.Nuggets.BomUploadedExcel.registerClass("Rebound.GlobalTrader.Site.Controls.Nuggets.BomUploadedExcel", Rebound.GlobalTrader.Site.Controls.Nuggets.Base);
