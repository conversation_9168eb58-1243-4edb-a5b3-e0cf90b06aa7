<%@ Control Language="C#" CodeBehind="MyStatistics.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyStatistics" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Nugget:DesignBase id="ctlDB" runat="server" BoxType="allwhite">
	<Content>
		<div class="homepageNugget">
			<div style="border-bottom:solid 1px #cccccc;overflow: hidden;padding-bottom: 5px;margin-bottom: 5px;">
				<div class="floatLeft rightSpacing"><%=Functions.GetGlobalResource("Misc", "IncludeCredits")%></div>
				<div class="floatLeft rightSpacing"><ReboundUI:ImageCheckBox id="chkIncludeCredit" runat="server" Enabled="true" Checked="false" /></div>
			</div>
			<div style="border-bottom:solid 1px #cccccc;">
				<asp:Panel ID="pnlStatsTY" runat="server" CssClass="homepageNugget">
					<h5><%=DateTime.Now.ToString("yyyy")%></h5>
					<ReboundUI:SimpleDataTable ID="tblStatsTY" runat="server" AllowSelection="false" />
				</asp:Panel>
				<asp:Panel ID="pnlStatsTM" runat="server" CssClass="homepageNugget">
					<h5><%=Functions.GetFirstDayOfMonth(DateTime.Now).ToString("MMM yyyy")%></h5>
					<ReboundUI:SimpleDataTable ID="tblStatsTM" runat="server" AllowSelection="false" />
				</asp:Panel>
			</div>
			<div style="background-color:#eeeeee; padding-top:10px;">
				<asp:Panel ID="pnlStatsLM" runat="server" CssClass="homepageNugget">
					<h5><%=Functions.GetFirstDayOfMonth(DateTime.Now).AddMonths(-1).ToString("MMM yyyy")%></h5>
					<ReboundUI:SimpleDataTable ID="tblStatsLM" runat="server" AllowSelection="false" />
				</asp:Panel>
				<asp:Panel ID="pnlStatsNM" runat="server" CssClass="homepageNugget">
					<h5><%=Functions.GetFirstDayOfMonth(DateTime.Now).AddMonths(1).ToString("MMM yyyy")%></h5>
					<ReboundUI:SimpleDataTable ID="tblStatsNM" runat="server" AllowSelection="false" />
				</asp:Panel>
			</div>
			<div style="border-top:solid 1px #cccccc; padding-top:10px;">
				<asp:Panel ID="pnlStatsLY" runat="server" CssClass="homepageNugget">
					<h5><%=DateTime.Now.AddYears(-1).ToString("yyyy")%></h5>
					<ReboundUI:SimpleDataTable ID="tblStatsLY" runat="server" AllowSelection="false" />
				</asp:Panel>
			</div>
		</div>
	</Content>
</ReboundUI_Nugget:DesignBase>
