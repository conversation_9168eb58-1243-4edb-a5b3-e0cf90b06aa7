///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference name="~/Controls/IconButton/IconButton.js" />
///<reference name="~/Controls/ReboundTextBox/ReboundTextBox.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Nuggets");

Rebound.GlobalTrader.Site.Controls.Nuggets.ContactExtendedInfo = function(element) { 
	Rebound.GlobalTrader.Site.Controls.Nuggets.ContactExtendedInfo.initializeBase(this, [element]);
	this._intCompanyID = -1;
	this._intContactID = -1;
};

Rebound.GlobalTrader.Site.Controls.Nuggets.ContactExtendedInfo.prototype = {

	get_intCompanyID: function() { return this._intCompanyID; }, set_intCompanyID: function(value) { if (this._intCompanyID !== value) this._intCompanyID = value; },
	get_intContactID: function() { return this._intContactID; }, set_intContactID: function(value) { if (this._intContactID !== value) this._intContactID = value; },
	get_ibtnEdit: function() { return this._ibtnEdit; }, 	set_ibtnEdit: function(value) { if (this._ibtnEdit !== value)  this._ibtnEdit = value; }, 

	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.Nuggets.ContactExtendedInfo.callBaseMethod(this, "initialize");	
		this.addRefreshEvent(Function.createDelegate(this, this.getData));

		//setup forms and their events
		if (this._ibtnEdit) {
			$R_IBTN.addClick(this._ibtnEdit, Function.createDelegate(this, this.showEditForm));
			this._frmEdit = $find(this._aryFormIDs[0]);
			this._frmEdit.addCancel(Function.createDelegate(this, this.cancelEdit));
			this._frmEdit.addSaveComplete(Function.createDelegate(this, this.saveEditComplete));
		}

		if (!this._blnIsNoDataFound) {
			if (!this._blnHasInitialData) this.getData();
		}
	},

	dispose: function() { 
		if (this.isDisposed) return;
		if (this._ibtnEdit) $R_IBTN.clearHandlers(this._ibtnEdit);
		if (this._frmEdit) this._frmEdit.dispose();
		this._ibtnEdit = null;
		this._frmEdit = null;
		this._intCompanyID = null;
		this._intContactID = null;
		Rebound.GlobalTrader.Site.Controls.Nuggets.ContactExtendedInfo.callBaseMethod(this, "dispose");
	},

	getData: function() { 
		this.getData_Start();
		var obj = new Rebound.GlobalTrader.Site.Data();
		obj.set_PathToData("controls/Nuggets/ContactExtendedInfo");
		obj.set_DataObject("ContactExtendedInfo");
		obj.set_DataAction("GetData");
		obj.addParameter("id", this._intContactID);
		obj.addDataOK(Function.createDelegate(this, this.getDataOK));
		obj.addError(Function.createDelegate(this, this.getDataError));
		obj.addTimeout(Function.createDelegate(this, this.getDataError));
		$R_DQ.addToQueue(obj);
		$R_DQ.processQueue();
		obj = null;
	},

	getDataOK: function(args) { 
		var res = args._result;
		this.setFieldValue("ctlNotes", res.Notes);
		this.setFieldValue("ctlGender", res.Gender);
		this.setFieldValue("hidGenderID", res.GenderID);
		this.setFieldValue("ctlBirthday", res.Birthday);
		this.setFieldValue("ctlMaritalStatus", res.MaritalStatus);
		this.setFieldValue("hidMaritalStatusID", res.MaritalStatusID);
		this.setFieldValue("ctlPartner", res.PartnerName);
		this.setFieldValue("ctlPartnerBirthday", res.PartnerBirthday);
		this.setFieldValue("ctlAnniversary", res.Anniversary);
		this.setFieldValue("ctlNumberChildren", res.NumberChildren);
		for (var i = 1; i <= 3; i++) {
			this.setFieldValue("ctlChild" + i, res["Child" + i]);
			this.setFieldValue("hidChild" + i + "Name", res["Child" + i + "Name"]);
			this.setFieldValue("hidChild" + i + "Sex", res["Child" + i + "SexID"]);
			this.setFieldValue("hidChild" + i + "Birthday", res["Child" + i + "Birthday"]);
		}
		this.setFieldValue("ctlMobileTel", res.MobileTel);
		this.setFieldValue("ctlFavouriteSport", res.FavouriteSport);
		this.setFieldValue("ctlFavouriteTeam", res.FavouriteTeam);
		this.setFieldValue("ctlHobbies", res.Hobbies);
		this.setDLUP(res.DLUP);
		this.getDataOK_End();
	},

	getDataError: function(args) {
		this.showError(true, args.get_ErrorMessage());
	},
	
	showEditForm: function() {
		this._frmEdit.setFormFieldsToDefaults();
		this._frmEdit.setFieldValue("ctlGender", this.getFieldValue("hidGenderID"));
		this._frmEdit.setFieldValue("ctlBirthday", this.getFieldValue("ctlBirthday"));
		this._frmEdit.setFieldValue("ctlMaritalStatus", this.getFieldValue("hidMaritalStatusID"));
		this._frmEdit.setFieldValue("ctlPartner", this.getFieldValue("ctlPartner"));
		this._frmEdit.setFieldValue("ctlPartnerBirthday", this.getFieldValue("ctlPartnerBirthday"));
		this._frmEdit.setFieldValue("ctlAnniversary", this.getFieldValue("ctlAnniversary"));
		this._frmEdit.setFieldValue("ctlNumberChildren", this.getFieldValue("ctlNumberChildren"));
		for (var i = 1; i <= 3; i++) {
			this._frmEdit.setFieldValue("ctlChild" + i + "Name", this.getFieldValue("hidChild" + i + "Name"));
			this._frmEdit.setFieldValue("ctlChild" + i + "Sex", this.getFieldValue("hidChild" + i + "Sex"));
			this._frmEdit.setFieldValue("ctlChild" + i + "Birthday", this.getFieldValue("hidChild" + i + "Birthday"));
		}
		this._frmEdit.setFieldValue("ctlMobileTel", this.getFieldValue("ctlMobileTel"));
		this._frmEdit.setFieldValue("ctlFavouriteSport", this.getFieldValue("ctlFavouriteSport"));
		this._frmEdit.setFieldValue("ctlFavouriteTeam", this.getFieldValue("ctlFavouriteTeam"));
		this._frmEdit.setFieldValue("ctlHobbies", this.getFieldValue("ctlHobbies"));
		this._frmEdit.setFieldValue("ctlNotes", this.getFieldValue("ctlNotes"));
		this.showForm(this._frmEdit, true);
	},
	
	hideEditForm: function() {
		this.showForm(this._frmEdit, false);		
	},
	
	cancelEdit: function() {
		this.hideEditForm();
	},
	
	saveEditComplete: function() {
		this.getData();
		this.showSavedOK(true, $R_RES.ChangesSavedSuccessfully);
		this.hideEditForm();
	}
		
};

Rebound.GlobalTrader.Site.Controls.Nuggets.ContactExtendedInfo.registerClass("Rebound.GlobalTrader.Site.Controls.Nuggets.ContactExtendedInfo", Rebound.GlobalTrader.Site.Controls.Nuggets.Base);
