Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");Rebound.GlobalTrader.Site.Controls.DropDowns.ViewLevel_Stock=function(n){Rebound.GlobalTrader.Site.Controls.DropDowns.ViewLevel_Stock.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.DropDowns.ViewLevel_Stock.prototype={initialize:function(){Rebound.GlobalTrader.Site.Controls.DropDowns.ViewLevel_Stock.callBaseMethod(this,"initialize");this.addSetupDataCall(Function.createDelegate(this,this.setupDataCall));this.addGetDataOK(Function.createDelegate(this,this.dataCallOK))},dispose:function(){this.isDisposed||Rebound.GlobalTrader.Site.Controls.DropDowns.ViewLevel_Stock.callBaseMethod(this,"dispose")},setupDataCall:function(){this._objData.set_PathToData("controls/DropDowns/ViewLevel_Stock");this._objData.set_DataObject("ViewLevel_Stock");this._objData.set_DataAction("GetData")},dataCallOK:function(){var t=this._objData._result,n;if(t.Types)for(n=0;n<t.Types.length;n++)this.addOption(t.Types[n].Name,t.Types[n].ID)}};Rebound.GlobalTrader.Site.Controls.DropDowns.ViewLevel_Stock.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.ViewLevel_Stock",Rebound.GlobalTrader.Site.Controls.DropDowns.Base);