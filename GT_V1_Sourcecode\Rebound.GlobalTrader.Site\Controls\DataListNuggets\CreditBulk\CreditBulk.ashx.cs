
using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;
using Rebound.GlobalTrader.Site.Enumerations;

namespace Rebound.GlobalTrader.Site.Controls.DataListNuggets.Data {

	public class CreditBulk : Base {

		protected override void GetData() {
			//check view level
			Int32 viewLabel = 3;
			ViewLevelList enmViewLevel = (ViewLevelList)viewLabel;//(ViewLevelList)GetFormValue_Int("ViewLevel");
			int? SelectedclientNo = null;
			int? SessionClientNo = SessionManager.ClientID;
			bool? blnMakeYellow = false;
			if (SessionManager.IsGSA == true && SessionManager.IsGlobalUser == false)
			{
				SelectedclientNo = GetFormValue_NullableInt("Client");
				if (SelectedclientNo != null)
				{
					blnMakeYellow = true;
				}
				else
				{
					blnMakeYellow = false;
				}

			}
			else
			{
				blnMakeYellow = false;
			}
			
			//get data	
			List<CreditLine> lst = CreditLine.DataListNugget(
				SessionManager.ClientID
				, (enmViewLevel == ViewLevelList.Team) ? (int?)SessionManager.LoginTeamID : null
				, (enmViewLevel == ViewLevelList.Division) ? (int?)SessionManager.LoginDivisionID : null
				, (enmViewLevel == ViewLevelList.My) ? (int?)LoginID : null
				, GetFormValue_NullableInt("SortIndex")
				, GetFormValue_NullableInt("SortDir", SortColumnDirection.ASC)
				, GetFormValue_NullableInt("PageIndex", 0)
				, GetFormValue_NullableInt("PageSizeLimit", 50)
                //[0001] start code
                //, GetFormValue_StringForPartSearch("Part")
                  , GetFormValue_PartForLikeSearch("Part")
                //[0001] end code
                // , GetFormValue_StringForNameSearch("Contact")
                , GetFormValue_StringForNameSearchDecode("Contact")
                //, GetFormValue_StringForNameSearch("CMName")
                 , GetFormValue_StringForNameSearchDecode("CMName")
				, GetFormValue_NullableInt("Salesman")
				, GetFormValue_StringForSearch("CreditNotes")
				, GetFormValue_StringForSearch("CustPO")
				, GetFormValue_NullableInt("CreditNoLo")
				, GetFormValue_NullableInt("CreditNoHi")
				, GetFormValue_NullableInt("InvNoLo")
				, GetFormValue_NullableInt("InvNoHi")
				, GetFormValue_NullableInt("CRMANoLo")
				, GetFormValue_NullableInt("CRMANoHi")
				, GetFormValue_NullableDateTime("CreditDateFrom", null)
				, GetFormValue_NullableDateTime("CreditDateTo", null)
                 , SessionManager.IsPOHub == true ? GetFormValue_Boolean("PohubOnly") : false
                //[002] Start Code
                 , GetFormValue_NullableInt("ClientInvNoLo")
                , GetFormValue_NullableInt("ClientInvNoHi")
                , GetFormValue_Boolean("blnHubCredit")
				//[002] End Code
				//[003] Start Code
				, GetFormValue_Double("Total")
				, GetFormValue_String("CurrencyCode")
		        , GetFormValue_NullableInt("Client")
				, SessionManager.LoginID
			//[003] END Code
			);
			JsonObject jsn = new JsonObject();
			jsn.AddVariable("TotalRecords", (lst.Count > 0) ? lst[0].RowCnt : 0);
			JsonObject jsnRowsArray = new JsonObject(true);
			for (int i = 0; i < lst.Count; i++) {
				JsonObject jsnRow = new JsonObject();
				jsnRow.AddVariable("ID", lst[i].CreditId);
				jsnRow.AddVariable("No", lst[i].CreditNumber);
				jsnRow.AddVariable("Part", lst[i].Part);
				jsnRow.AddVariable("Date", Functions.FormatDate(lst[i].CreditDate));
				jsnRow.AddVariable("CM", lst[i].CompanyName);
				jsnRow.AddVariable("CMNo", lst[i].CompanyNo);
				jsnRow.AddVariable("CRMA", lst[i].CustomerRMANumber);
				jsnRow.AddVariable("CRMANo", lst[i].CustomerRMANo);
				jsnRow.AddVariable("CustPO", lst[i].CustomerPO);
				jsnRow.AddVariable("Invoice", lst[i].InvoiceNumber);
				jsnRow.AddVariable("InvNo", lst[i].InvoiceNo);
				jsnRow.AddVariable("Contact", lst[i].ContactName);
				jsnRow.AddVariable("ContactNo", lst[i].ContactNo);
				jsnRow.AddVariable("ROHS", lst[i].ROHS);
				jsnRow.AddVariable("MfrNo", lst[i].ManufacturerNo);
				jsnRow.AddVariable("Mfr", lst[i].ManufacturerCode);
                jsnRow.AddVariable("ClientInvoiceNumber", lst[i].ClientInvoiceNumber);
                jsnRow.AddVariable("ClientInvoiceNo", lst[i].ClientInvoiceNo);
				jsnRow.AddVariable("Total", Functions.FormatCurrency(lst[i].Total, lst[i].CurrencyCode, 2) );  // [003]
				jsnRow.AddVariable("blnMakeYellow", blnMakeYellow);
				jsnRowsArray.AddVariable(jsnRow);
				jsnRow.Dispose();
				jsnRow = null;
			}
			jsn.AddVariable("Results", jsnRowsArray);
			OutputResult(jsn);
			jsnRowsArray.Dispose();
			jsnRowsArray = null;
			jsn.Dispose();
			jsn = null;
			lst = null;
			base.GetData();
		}

		protected override void AddFilterStates() {
            //Prevent filter state for Tab
			//AddExplicitFilterState("ViewLevel", GetFormValue_Int("ViewLevel"));
			AddFilterState("Part");
			AddFilterState("Contact");
			AddFilterState("CMName");
			AddFilterState("Salesman");
			AddFilterState("CreditNotes");
			AddFilterState("CreditNo");
			AddFilterState("InvNo");
			AddFilterState("CRMANo");
			AddFilterState("CreditDateFrom");
			AddFilterState("CreditDateTo");
			AddFilterState("CustPO");
            AddFilterState("PohubOnly");
            AddFilterState("ClientInvNo");
			base.AddFilterStates();
		}

	}
}
