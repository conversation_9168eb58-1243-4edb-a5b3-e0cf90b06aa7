﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

/*   
===========================================================================================  
TASK        	UPDATED BY       DATE          ACTION    DESCRIPTION  
[BUG-238814]    Ngai To		04-Apr-2025			Update   Bug 238814: [PROD Bug] Invoice - Wrong Shipping Cost conversion across Client
===========================================================================================  
*/ 

CREATE OR ALTER PROCEDURE [dbo].[usp_update_Invoice]
	--********************************************************************************                    
	--* SK 12.04.2010:                    
	--* - exporting is now on a separate form so do not allow update of the column here                    
	--*                    
	--* SK 26.03.2010:                    
	--* - insert column for DivisionNo2 - this is not updated from the application but is required                     
	--*   for the divisonal reports                     
	--*   nota bene there is no edit facility on Salesman2 so it will never change on update                     
	--*                    
	--* SK 05.01.2010:                    
	--* - reinstate CurrencyNo to update list at request of JB/Rebound                    
	--*                    
	--* SK 05.11.2009:                    
	--* - add IncotermNo                    
	--*                    
	--* SK 18.10.2009:                    
	--* - remove CurrencyNo from update list                    
	--*                    
	--* RP 30.09.2009:                    
	--* - add CompanyBillTo and CompanyShipTo                    
	--*                    
	--* RP 18.09.2009:                    
	--* - add BillToAddressNo                    
	--*                    
	--* SK 16.09.2009:                    
	--* - add CofCNotes update                    
	--*                     
	--* SK 09.07.2009:                    
	--* - update CustomerPO      
	--* [001]  Devendra Singh Sikarwar    05-06-2023  RP-785 Log when shipment notification mails are sent to customers          
	--* [002]      Rahil Anjum Ansari    05-06-2023  RP-1830 logging invoice details at the time of creation  
	--* [003]      Rahil Anjum Ansari    10-01-2024  RP-2311 UPS Shipping Costs  
	--* [004]      Rahil Anjum Ansari    24-01-2024  RP-2881 UPS Shipping Charges 1:1 Issue 
	--********************************************************************************    
	@InvoiceId INT,
	@Notes NVARCHAR(max) = NULL,
	@Instructions NVARCHAR(max) = NULL,
	@ShippingNotes NVARCHAR(max) = NULL,
	@CofCNotes NVARCHAR(max) = NULL,
	--  , @Exported bit                    
	@InvoicePaid BIT,
	@Salesman2 INT = NULL,
	@Salesman2Percent FLOAT = NULL,
	@Boxes INT = NULL,
	@Weight FLOAT = NULL,
	@DimensionalWeight FLOAT = NULL,
	@WeightInPounds BIT,
	@AirWayBill NVARCHAR(30) = NULL,
	@ShippingCost FLOAT = NULL,
	@Freight FLOAT = NULL,
	@ShipViaNo INT = NULL,
	@CustomerPO NVARCHAR(30) = NULL,
	@Account NVARCHAR(50) = NULL,
	@CurrencyNo INT,
	@Salesman INT,
	@TermsNo INT,
	@TaxNo INT,
	@BillToAddressNo INT = NULL,
	@CompanyBillTo NVARCHAR(max) = NULL,
	@ShipToAddressNo INT = NULL,
	@CompanyShipTo NVARCHAR(max) = NULL,
	@DivisionNo INT,
	@IncotermNo INT = NULL,
	@UpdatedBy INT = NULL,
	@ExchangeRate FLOAT = NULL,
	@InvoicePaidDate DATETIME = NULL,
	@InvoiceShippedDate DATETIME = NULL,
	@IsAppShippingSurcharge BIT = 1,
	@DivisionHeaderNo INT = NULL,
	@RowsAffected INT = NULL OUTPUT
AS
BEGIN
	DECLARE @DivisionNo2 INT

	SET @DivisionNo2 = NULL

	IF @Salesman2 IS NOT NULL
	BEGIN
		SET @DivisionNo2 = (
				SELECT DivisionNo
				FROM tblogin
				WHERE LoginId = @Salesman2
				)
	END

	DECLARE @OldShippingSerCharge FLOAT
	DECLARE @ShipSurChargeValue FLOAT
	DECLARE @ShippingSurchargePercent FLOAT
	DECLARE @ExchangeRate1 FLOAT
	DECLARE @OldShipToAddressNo INT

	SELECT @OldShippingSerCharge = round(isnull(ShippingSurchargeValue, 0), 2),
		@ShippingSurchargePercent = ShippingSurchargePercent,
		@OldShipToAddressNo = ShipToAddressNo,
		@ExchangeRate1 = dbo.ufn_get_exchange_rate(CurrencyNo, isnull(InvoiceDate, GETDATE()))
	FROM tbInvoice
	WHERE InvoiceId = @InvoiceId

	--Update the shipping surcharge percentage and value in case invoice ship to address change                
	IF @OldShipToAddressNo <> @ShipToAddressNo
	BEGIN
		SELECT @ShippingSurchargePercent = c.ShippingSurchargePercent
		FROM tbAddress a
		JOIN tbCountry c ON a.CountryNo = c.CountryId
		WHERE a.AddressId = @ShipToAddressNo
	END

	--------------------------------for add log entry into Division header when update the record-----------------------------            
	DECLARE @DivisionHeaderName NVARCHAR(50) = NULL

	SELECT @DivisionHeaderName = iv.DivisionHeaderName
	FROM dbo.vwInvoice iv
	WHERE InvoiceId = @InvoiceId

	-- declare @SectionName varchar(50) = 'InvoiceDivisionHeader'            
	DECLARE @SectionName VARCHAR(50) = 'Invoice'
	DECLARE @SubSectionName VARCHAR(50) = 'Division Header'
	DECLARE @ActionName VARCHAR(10) = 'Print'
	DECLARE @DocumentNo INT = @InvoiceId
	DECLARE @Detail NVARCHAR(max) = 'Action¦¦' + ' Division Header changed from (' + @DivisionHeaderName + ')'
	DECLARE @PrintDocumentLogId INT = NULL

	-----------------------------------------------------------------------            
	EXEC [dbo].[usp_insert_PrintEmailLog] @SectionName,
		@SubSectionName,
		@ActionName,
		@DocumentNo,
		@Detail,
		@UpdatedBy,
		@PrintDocumentLogId

	-----------------------------------------            
	UPDATE dbo.tbInvoice
	SET Notes = @Notes,
		Instructions = @Instructions,
		ShippingNotes = @ShippingNotes,
		CofCNotes = @CofCNotes
		--          , Exported = @Exported                    
		,
		InvoicePaid = @InvoicePaid,
		Salesman2 = @Salesman2,
		Salesman2Percent = @Salesman2Percent,
		Boxes = @Boxes,
		Weight = @Weight,
		DimensionalWeight = @DimensionalWeight,
		WeightInPounds = @WeightInPounds,
		AirWayBill = @AirWayBill,
		ShippingCost = @ShippingCost
		--, ImportedShippingCost = @ShippingCost                    
		,
		Freight = @Freight,
		ShipViaNo = @ShipViaNo,
		CustomerPO = @CustomerPO,
		Account = @Account,
		CurrencyNo = @CurrencyNo,
		Salesman = @Salesman,
		TermsNo = @TermsNo,
		TaxNo = @TaxNo,
		BillToAddressNo = @BillToAddressNo,
		CompanyBillTo = @CompanyBillTo,
		ShipToAddressNo = @ShipToAddressNo,
		CompanyShipTo = @CompanyShipTo,
		DivisionNo = @DivisionNo,
		DivisionNo2 = @DivisionNo2,
		IncotermNo = @IncotermNo,
		UpdatedBy = @UpdatedBy,
		DLUP = CURRENT_TIMESTAMP,
		ExchangeRate = @ExchangeRate,
		InvoicePaidDate = @InvoicePaidDate,
		ShippingSurchargePercent = @ShippingSurchargePercent,
		InvoiceDate = ISNULL(@InvoiceShippedDate, InvoiceDate),
		IsAppShippingSurcharge = @IsAppShippingSurcharge,
		DivisionHeaderNo = @DivisionHeaderNo
	WHERE InvoiceId = @InvoiceId

	SELECT @RowsAffected = @@ROWCOUNT

	DECLARE @TotalValue FLOAT

	SELECT @TotalValue = dbo.ufn_calculate_InvoicePriceForSurcharge(@InvoiceId) / @ExchangeRate1

	SET @ShipSurChargeValue = round(((@TotalValue * isnull(@ShippingSurchargePercent, 0)) / 100), 2)
	
	IF @OldShippingSerCharge = 0
	BEGIN
		SET @OldShippingSerCharge=@ShipSurChargeValue
	END

	/*Change for [004]*/
	DECLARE @IsSameAsShipCost BIT

	SELECT @IsSameAsShipCost = IsSameAsShipCost
	FROM tbShipVia
	WHERE ShipViaId = @ShipViaNo

	/*Change for [004]*/
	--Apply shipping surcharge        
	IF @IsAppShippingSurcharge = 1
	BEGIN
		UPDATE tbInvoice
		SET ShippingSurchargeValue = @ShipSurChargeValue
		, ShippingCost = (ROUND(isnull(ShippingCost, 0), 2) - @OldShippingSerCharge) + @ShipSurChargeValue
		WHERE InvoiceId = @InvoiceId
	END
	ELSE
	BEGIN
		--SET @ShipSurChargeValue = 0

		/*Change for [004]*/
		IF @IsSameAsShipCost = 1
		BEGIN
			UPDATE tbInvoice
			SET ShippingSurchargeValue = 0--@ShipSurChargeValue
			--, ShippingCost = @ShippingCost
			WHERE InvoiceId = @InvoiceId
		END
		ELSE
		BEGIN
			UPDATE tbInvoice
			SET ShippingSurchargeValue = 0--@ShipSurChargeValue
			, ShippingCost = (ROUND(isnull(ShippingCost, 0), 2) - @OldShippingSerCharge) + @ShipSurChargeValue
			WHERE InvoiceId = @InvoiceId
		END
				--update tbInvoice set ShippingSurchargeValue =  @ShipSurChargeValue,ShippingCost = (ROUND( isnull(ShippingCost,0),2)-@OldShippingSerCharge)+@ShipSurChargeValue                
				--where InvoiceId = @InvoiceId
	END

	/*Changes start for [002]*/
	DECLARE @ShippingSurchargeValue FLOAT

	--Declare @IsAppShippingSurcharge float    
	SELECT @ShippingSurchargeValue = ShippingSurchargeValue,
		@IsAppShippingSurcharge = IsAppShippingSurcharge
	FROM tbInvoice
	WHERE InvoiceId = @InvoiceId

	IF @IsSameAsShipCost = 1
	BEGIN
		UPDATE tbInvoice
		SET Freight = ShippingCost --+ ShippingSurchargeValue  
		WHERE InvoiceId = @InvoiceId
	END

	INSERT INTO tbTempUPSInvoiceUpdateLog (
		Stage,
		DLUP,
		InvoiceNo,
		ClientNo,
		InvoiceNumber,
		Freight,
		ShippingCost,
		ShippingSurchargePercent,
		ShippingSurcharge,
		IsAppShippingSurcharge,
		ImportedShippingCost,
		CompanyNo,
		CompanyWaiveFreight,
		ShipViaNo,
		ShipVia,
		ShipViaFreightUplift,
		ShipViaAddSurcharge,
		ShipViaMatchFreightToShipCost,
		ActualUPSCost,
		SPCalled,
		CountryName,
		CountryShippingSurchargePercent
		)
	SELECT 'Invoice Update',
		GETDATE(),
		@InvoiceId,
		i.ClientNo,
		i.InvoiceNumber,
		@Freight,
		i.ShippingCost - isnull(ShippingSurchargeValue, 0.0),
		@ShippingSurchargePercent,
		ShippingSurchargeValue,
		@IsAppShippingSurcharge,
		NULL,
		i.CompanyNo,
		cy.ShippingCharge,
		@ShipViaNo,
		sv.ShipViaName,
		sv.FreightPercentage,
		sv.IncreaseFreight,
		sv.IsSameAsShipCost,
		NULL,
		'usp_insert_Invoice',
		cnt.CountryName,
		cnt.ShippingSurchargePercent
	FROM tbInvoice i WITH (NOLOCK)
	LEFT JOIN tbCompany cy WITH (NOLOCK) ON cy.CompanyId = i.CompanyNo
	LEFT JOIN tbShipVia sv WITH (NOLOCK) ON sv.ShipViaId = i.ShipViaNo
	LEFT JOIN tbAddress adr WITH (NOLOCK) ON i.ShipToAddressNo = adr.AddressId
	LEFT JOIN tbCountry cnt WITH (NOLOCK) ON cnt.CountryId = adr.CountryNo
	WHERE i.InvoiceId = @InvoiceId
		/*Changes End for [002]*/
END
GO


