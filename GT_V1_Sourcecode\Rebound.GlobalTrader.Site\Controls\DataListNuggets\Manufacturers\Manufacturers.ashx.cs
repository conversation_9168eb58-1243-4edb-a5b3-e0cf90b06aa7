using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using System.Text;
using System.Web.UI;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;
using Rebound.GlobalTrader.Site.Enumerations;
//using Microsoft.ApplicationInsights; //[001]

//Marker     Changed by      Date         Remarks
//[001]      Soorya          03/03/2023   RP-1048 Remove AI code
namespace Rebound.GlobalTrader.Site.Controls.DataListNuggets.Data
{

    public class Manufacturers : Base
    {

        /// <summary>
        /// Gets the main data
        /// </summary>
        protected override void GetData()
        {
            try
            {
                //[001]
                //var ai2 = new TelemetryClient();
                //ai2.TrackTrace("Manufacturers:GetData");

                List<Manufacturer> lst = Manufacturer.DataListNugget(
                    GetFormValue_NullableInt("SortIndex", 1)
                    , GetFormValue_NullableInt("SortDir", SortColumnDirection.ASC)
                    , GetFormValue_NullableInt("PageIndex", 0)
                    , GetFormValue_NullableInt("PageSize", 10)
                     //, GetFormValue_StringForNameSearch("Name")
                     , GetFormValue_StringForNameSearchDecode("Name")
                    , GetFormValue_StringForSearch("Code")
                    , GetFormValue_StringForSearch("GroupCode")
                    , GetFormValue_StringForSearch("GroupName")
                    , (int)SessionManager.ClientID
                );
                JsonObject jsn = new JsonObject();
                jsn.AddVariable("TotalRecords", (lst.Count > 0) ? lst[0].RowCnt : 0);
                JsonObject jsnRowsArray = new JsonObject(true);
                foreach (Manufacturer mf in lst)
                {
                    JsonObject jsnRow = new JsonObject();
                    jsnRow.AddVariable("ID", mf.ManufacturerId);
                    jsnRow.AddVariable("Name", mf.ManufacturerName);
                    jsnRow.AddVariable("Code", mf.ManufacturerCode);
                    jsnRow.AddVariable("Inactive", mf.Inactive);
                    jsnRow.AddVariable("URL", mf.URL);
                    jsnRow.AddVariable("ConflictResource", Functions.ReplaceLineBreaks(mf.ConflictResource));
                    jsnRow.AddVariable("GroupName", mf.GroupName);
                    jsnRow.AddVariable("GroupCode", mf.GroupCode);
                    jsnRow.AddVariable("SystemManufacturer", (mf.SystemManufacturer == 1) ? "YES" : "No");
                    //string companyNotes = Manufacturer.GetAdvisoryNotes(mf.ManufacturerId);
                    jsnRow.AddVariable("AdvisoryNotes", Functions.ReplaceLineBreaks(mf.AdvisoryNotes));
                    jsnRowsArray.AddVariable(jsnRow);
                    jsnRow.Dispose(); jsnRow = null;
                }
                jsn.AddVariable("Results", jsnRowsArray);
                OutputResult(jsn);
                jsnRowsArray.Dispose(); jsnRowsArray = null;
                jsn.Dispose(); jsn = null;
                base.GetData();
            }
            catch (Exception ex)
            {
                //[001]
                //var ai = new TelemetryClient();
                //ai.TrackTrace("Manufacturers: GetData");
                //ai.TrackException(ex);
                new Errorlog().LogMessage("Error at GetData in Manufacturers.ashx.cs : " + ex.Message);
                WriteError(ex);
            }
        }

        protected override void AddFilterStates()
        {
            AddFilterState("Name");
            AddFilterState("Code");
            base.AddFilterStates();
        }

    }
}
