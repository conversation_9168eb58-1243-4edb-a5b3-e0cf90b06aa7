<%@ Control Language="C#" CodeBehind="CommunicationLog_AddEdit.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Forms.CommunicationLog_AddEdit" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Form:DesignBase ID="ctlDB" runat="server" ShowRequiredFieldsExplanation="true" ShowQuickHelp="false">
	<Links>
		<ReboundUI:IconButton ID="ibtnSave" runat="server" IconGroup="Nugget" IconTitleResource="Save" IconCSSType="Save" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
		<ReboundUI:IconButton ID="ibtnCancel" runat="server" IconGroup="Nugget" IconTitleResource="Cancel" IconCSSType="Cancel" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
	</Links>
	<Explanation><%=Functions.GetGlobalResource("FormExplanations", "CommunicationLog_AddEdit")%></Explanation>
	<Content>
		<ReboundUI_Table:Form id="frm" runat="server">
			<ReboundUI_Form:FormField id="ctlContact" runat="server" FieldID="ddlContact" ResourceTitle="Contact" IsRequiredField="true">
				<Field><ReboundDropDown:ContactsForCompany ID="ddlContact" runat="server" /></Field>
			</ReboundUI_Form:FormField>
			<ReboundUI_Form:FormField id="ctlLogType" runat="server" FieldID="ddlLogType" ResourceTitle="LogType" IsRequiredField="true">
				<Field><ReboundDropDown:CommunicationLogType ID="ddlLogType" runat="server" /></Field>
			</ReboundUI_Form:FormField>
			<ReboundUI_Form:FormField id="ctlNotes" runat="server" FieldID="txtNotes" ResourceTitle="Notes">
				<Field><ReboundUI:ReboundTextBox ID="txtNotes" TextMode="multiline" runat="server" Width="400" Rows="2" /></Field>
			</ReboundUI_Form:FormField>
		</ReboundUI_Table:Form>
	</Content>
</ReboundUI_Form:DesignBase>
