﻿namespace Rebound.GlobalTrader.Site.Enumerations
{
    public class ClientList
    {
        public enum ClientNo
        {
            UK = 101,
            GMBH = 108,
            DMCC = 114
        }
        public static string GetClientDefaultCurrency(ClientNo client)
        {
            string currencyCode = "";
            switch (client)
            {
                case ClientNo.UK: currencyCode = "GBP"; break;
                case ClientNo.GMBH: currencyCode = "EUR"; break;
                case ClientNo.DMCC: currencyCode = "USD"; break;
            }
            return currencyCode;
        }
    }
}