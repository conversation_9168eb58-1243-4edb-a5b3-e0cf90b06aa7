
IF OBJECT_ID('usp_selectAll_BOMManager_for_Company', 'P') IS NOT NULL
DROP PROC usp_selectAll_BOMManager_for_Company
GO

/*
-- ==========================================================================================
-- TASK      		UPDATED BY     		DATE         ACTION 			DESCRIPTION                                    
-- US-200952 		Phuc.HoangDinh     	24-04-2024   Create				New SP for [RP-2999] BOM Manager Customer Contact Screen
-- ==========================================================================================
*/
CREATE PROCEDURE [dbo].[usp_selectAll_BOMManager_for_Company]
    @ClientId int 
  , @CompanyId int      
  , @IncludeClosed bit = 0 
AS       
    SELECT * FROM
		(
			SELECT 
			bom.BOMManagerId
			, bom.BOMManagerCode 
			, bom.BOMManagerName
			, (select top 1 (ReceivedDate) from tbCustomerRequirement where BOMManagerNo = BOMManagerId) as ReceivedDate
			, salesman.EmployeeName as SalesmanName
			, bom.[Status]
			, bom.TotalBOMManagerLinePrice
			, bom.DateRequestToPOHub
			, bom.DLUP
			, (SELECT POCurrencyNo FROM tbCompany WHERE ClientNo = @ClientId and IsPOHub = 1) as POCurrencyNo
			, bom.CompanyNo
			, bom.StatusValue
			FROM    dbo.vwBOMManager bom 
			LEFT JOIN dbo.tbLogin salesman ON bom.UpdatedBy = salesman.LoginId
		) t
			
    WHERE CompanyNo = @CompanyId
	    AND ReceivedDate >= DATEADD(MONTH,-24,GETDATE())
		AND StatusValue <> (CASE WHEN @IncludeClosed = 1 THEN 0 ELSE 7 END)
    ORDER BY BOMManagerId DESC
GO
