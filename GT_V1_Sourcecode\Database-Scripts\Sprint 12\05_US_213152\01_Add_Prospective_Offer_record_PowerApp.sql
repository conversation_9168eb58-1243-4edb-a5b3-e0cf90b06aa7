﻿GO
/*   
===========================================================================================  
TASK			UPDATED BY			DATE			ACTION		DESCRIPTION  
[US-213152]		Trung Pham Van		08-Oct-2024		CREATE		Insert new power automate flow
===========================================================================================  
*/
IF EXISTS (SELECT 1 FROM tbPowerApp_urls WHERE FlowName = 'Prospective_Offer_Outlook')
BEGIN
	DELETE FROM tbPowerApp_urls WHERE FlowName = 'Prospective_Offer_Outlook'
END

GO

    INSERT INTO [dbo].[tbPowerApp_urls]
        ([FlowName]
        ,[FlowUrl])
	VALUES
        ('Prospective_Offer_Outlook'
        ,'https://gt-uat2-webapp-001.azurewebsites.net')


