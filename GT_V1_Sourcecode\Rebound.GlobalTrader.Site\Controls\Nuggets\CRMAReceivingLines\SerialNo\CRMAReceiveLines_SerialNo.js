Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.CRMAReceiveLines_SerialNo=function(n){Rebound.GlobalTrader.Site.Controls.Forms.CRMAReceiveLines_SerialNo.initializeBase(this,[n]);this._intSOLineID=-1;this._intGIID=-1;this._intSerialID=-1;this._serialDetail=-1};Rebound.GlobalTrader.Site.Controls.Forms.CRMAReceiveLines_SerialNo.prototype={get_intSerialID:function(){return this._intSerialID},set_intSerialID:function(n){this._intSerialID!==n&&(this._intSerialID=n)},get_intGIID:function(){return this._intGIID},set_intGIID:function(n){this._intGIID!==n&&(this._intGIID=n)},get_intSOLineID:function(){return this._intSOLineID},set_intSOLineID:function(n){this._intSOLineID!==n&&(this._intSOLineID=n)},get_btnAdd:function(){return this._btnAdd},set_btnAdd:function(n){this._btnAdd!==n&&(this._btnAdd=n)},get_btnRefresh:function(){return this._btnRefresh},set_btnRefresh:function(n){this._btnRefresh!==n&&(this._btnRefresh=n)},get_lblDuplicateError:function(){return this._lblDuplicateError},set_lblDuplicateError:function(n){this._lblDuplicateError!==n&&(this._lblDuplicateError=n)},get_tblSerialNodetails:function(){return this._tblSerialNodetails},set_tblSerialNodetails:function(n){this._tblSerialNodetails!==n&&(this._tblSerialNodetails=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Forms.CRMAReceiveLines_SerialNo.callBaseMethod(this,"initialize");this.addShown(Function.createDelegate(this,this.formShown));this.addSave(Function.createDelegate(this,this.saveSerialClicked));$R_FN.showElement(this._btnAdd,!0);$R_FN.showElement(this._btnRefresh,!0);$R_FN.showElement(this._lblDuplicateError,!1);this.showField("ctlSerialNoDetail",!1);this.getFieldDropDownData("ctlSubGroup");this.setFieldValue("ctlSerailNo","");this._btnAdd&&$addHandler(this._btnAdd,"click",Function.createDelegate(this,this.saveClicked));this._btnRefresh&&$addHandler(this._btnRefresh,"click",Function.createDelegate(this,this.refreshClicked));this.LoadSerailNoGrid()},formShown:function(){this.showField("ctlSerialNoDetail",!1);this.setFieldValue("ctlSerailNo",0,null,"");$R_FN.showElement(this._lblDuplicateError,!1);$R_FN.showElement(this._btnUpdate,!1);$R_FN.showElement(this._btnAdd,!0);$R_FN.showElement(this._btnRefresh,!0);this._intSOLineID=this._intSOLineID;this.LoadSerailNoGrid();$find(this.getField("ctlSubGroup").ControlID).addChanged(Function.createDelegate(this,this.setGroup));this.getFieldDropDownData("ctlSubGroup")},dispose:function(){this.isDisposed||(this._intSerialID=-1,this._btnAdd=null,this._btnRefresh=null,this._btnUpdate=null,this._intGIID=-1,this._lblDuplicateError=null,this._tblSerialNodetails=null,this._intSOLineID=null,this._serialDetail=-1,Rebound.GlobalTrader.Site.Controls.Forms.CRMAReceiveLines_SerialNo.callBaseMethod(this,"dispose"))},setGroup:function(){$find(this.getField("ctlSerailNo").ControlID)._aut._txtGroup=this.getFieldValue("ctlSubGroup");this.setFieldValue("ctlSerailNo",0,null,"")},saveSerialClicked:function(){if(this._serialDetail<=0){this._strErrorMessage="No Record Exist";this.showError(!0,this._strErrorMessage);return}this.setFieldValue("ctlSubGroup","");this.setFieldValue("ctlSerailNo",0,null,"");this.onSaveComplete();var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/SOShippingLines");n.set_DataObject("SOShippingLines");n.set_DataAction("AddAllSerialNo");n.addDataOK(Function.createDelegate(this,this.saveSerialComplete));n.addError(Function.createDelegate(this,this.saveSerialError));n.addTimeout(Function.createDelegate(this,this.saveSerialError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},saveSerialError:function(n){this._strErrorMessage=n._errorMessage;this.onSaveError()},saveSerialComplete:function(n){n._result.Result==!0?(this.setFieldValue("ctlSubGroup",""),this.setFieldValue("ctlSerailNo",0,null,""),this.onSaveComplete()):(this._strErrorMessage=n._errorMessage,this.onSaveError())},saveClicked:function(){if(this.validateForm()){var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/SOShippingLines");n.set_DataObject("SOShippingLines");n.set_DataAction("AddSerialNo");n.addParameter("SubGroup",this.getFieldValue("ctlSubGroup"));n.addParameter("SerialNo",this.getFieldComboText("ctlSerailNo"));n.addParameter("SOLineNo",this._intSOLineID);n.addDataOK(Function.createDelegate(this,this.saveComplete));n.addError(Function.createDelegate(this,this.saveError));n.addTimeout(Function.createDelegate(this,this.saveError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null}},saveError:function(n){this._strErrorMessage=n._errorMessage;this.onSaveError()},saveComplete:function(n){if(n._result.Result==!0){this.showField("ctlSerialNoDetail",!0);this.setFieldValue("ctlSerailNo",0,null,"");this._strErrorMessage="";this.showError(!1,this._strErrorMessage);$R_FN.showElement(this._lblDuplicateError,!1);this.LoadSerailNoGrid();return}if(n._result.Result==!1&&n._result.ValidateMessage!=null){$R_FN.showElement(this._lblDuplicateError,!0);return}this._strErrorMessage=n._errorMessage;this.onSaveError()},LoadSerailNoGrid:function(){var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/SOShippingLines");n.set_DataObject("SOShippingLines");n.set_DataAction("GetDataGrid");n.addParameter("SOLineNo",this._intSOLineID);n.addDataOK(Function.createDelegate(this,this.getDataGrid));n.addError(Function.createDelegate(this,this.getDataGridError));n.addTimeout(Function.createDelegate(this,this.getDataGridError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getDataGridError:function(){},getDataGrid:function(n){var i;for(res=n._result,this._serialDetail=res.SerialNoDetails.length,this.showField("ctlSerialNoDetail",res.SerialNoDetails.length>0),this._tblSerialNodetails.clearTable(),i=0;i<res.SerialNoDetails.length;i++){var t=res.SerialNoDetails[i],r=[t.SubGroup,t.SerialNo],u={SerialNoId:t.SerialNoId,SubGroup:t.SubGroup,SerialNo:t.SerialNo};this._tblSerialNodetails.addRow(r,t.SerialNoId,!1,u);r=null;t=null}this._tblSerialNodetails.resizeColumns()},getSerialDetail:function(){$R_FN.showElement(this._lblDuplicateError,!1);this._strErrorMessage="";this.showError(!1,this._strErrorMessage);$R_FN.showElement(this._btnAdd,!0);this.setFieldValue("ctlSubGroup","");this.setFieldValue("ctlSerailNo",0,null,"");this.getValuesBySerial()},getValuesBySerial:function(){this._intSerialID=-1;var n=this._tblSerialNodetails.getSelectedExtraData();n&&(this.setFieldValue("ctlSubGroup",n.SubGroup),this.setFieldValue("ctlSerailNo",n.SerialNo),this._intSerialID=n.SerialNoId)},validateForm:function(){this.onValidate();return this.autoValidateFields()},refreshClicked:function(){this.LoadSerailNoGrid();this.getFieldDropDownData("ctlSubGroup");this.setFieldValue("ctlSubGroup","");this.setFieldValue("ctlSerailNo",0,null,"");this._strErrorMessage="";this.showError(!1,this._strErrorMessage);$R_FN.showElement(this._lblGroupError,!1)}};Rebound.GlobalTrader.Site.Controls.Forms.CRMAReceiveLines_SerialNo.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.CRMAReceiveLines_SerialNo",Rebound.GlobalTrader.Site.Controls.Forms.Base,Sys.IDisposable);