﻿/*     
=======================================================================================================================   
TASK         UPDATED BY       DATE          ACTION    DESCRIPTION    
[US-221203]  CuongDoX   7-Jan-2025  UPDATE   Update Lytica from selected 
=======================================================================================================================  
*/  
UPDATE tbReportColumn
SET SortOrder = CASE TitleResource
    WHEN 'RequirementNumber' THEN 1
    WHEN 'Company' THEN 2
    WHEN 'QuantityQuoted' THEN 3
    WHEN 'MPNQuoted' THEN 4
    WHEN 'ManufacturerName' THEN 5
    WHEN 'DateCode' THEN 6
    WHEN 'PackageType' THEN 7
    WHEN 'ProductType' THEN 8
    WHEN 'SPQ' THEN 9
    WHEN 'MOQ' THEN 10
    WHEN 'LeadTimeWks' THEN 11
    WHEN 'RohsYN' THEN 12
    WHEN 'TQSA' THEN 13
    WHEN 'LTB' THEN 14
    WHEN 'FactorySealed' THEN 15
    WHEN 'MSL' THEN 16
    WHEN 'UnitPrice' THEN 17
    WHEN 'SupplierNotes' THEN 18
    WHEN 'RequiredDate' THEN 19
    WHEN 'TargetPrice' THEN 20
    WHEN 'CRInstructionsNotes' THEN 21
	WHEN 'LyticaManufacture' THEN 22
    WHEN 'AveragePriceLytica' THEN 23
    WHEN 'TargetPriceLytica' THEN 24
    WHEN 'MarketLeadingLytica' THEN 25
    ELSE SortOrder 
END
WHERE ReportNo = 114