Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.BOMItems_Delete=function(n){Rebound.GlobalTrader.Site.Controls.Forms.BOMItems_Delete.initializeBase(this,[n]);this._intCustomerRequirementID=-1;this._intBOMID=-1};Rebound.GlobalTrader.Site.Controls.Forms.BOMItems_Delete.prototype={initialize:function(){Rebound.GlobalTrader.Site.Controls.Forms.BOMItems_Delete.callBaseMethod(this,"initialize");this.addShown(Function.createDelegate(this,this.formShown))},dispose:function(){this.isDisposed||(this._ctlDelete&&this._ctlDelete.dispose(),this._ctlDelete=null,this._intRequirementLineID=null,Rebound.GlobalTrader.Site.Controls.Forms.BOMItems_Delete.callBaseMethod(this,"dispose"))},formShown:function(){this._blnFirstTimeShown&&(this._ctlDelete=this.getFieldComponent("ctlDelete"),this._ctlDelete.addClickYesEvent(Function.createDelegate(this,this.yesClicked)),this._ctlDelete.addClickNoEvent(Function.createDelegate(this,this.noClicked)))},yesClicked:function(){this.showSaving(!0);var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("Controls/Nuggets/BOMItems");n.set_DataObject("BOMItems");n.set_DataAction("DeleteBomItem");n.addParameter("RequirementID",this._intCustomerRequirementID);n.addParameter("BomID",this._intBOMID);n.addDataOK(Function.createDelegate(this,this.saveComplete));n.addError(Function.createDelegate(this,this.saveError));n.addTimeout(Function.createDelegate(this,this.saveError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},noClicked:function(){this.showSaving(!1);this.onNotConfirmed()},saveError:function(n){this.showSaving(!1);this._strErrorMessage=n._errorMessage;this.onSaveError()},saveComplete:function(n){this.showSaving(!1);n._result.Result?this.onSaveComplete():(this._strErrorMessage=n._errorMessage,this.onSaveError())}};Rebound.GlobalTrader.Site.Controls.Forms.BOMItems_Delete.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.BOMItems_Delete",Rebound.GlobalTrader.Site.Controls.Forms.Base,Sys.IDisposable);