﻿/*===========================================================================================  
TASK			UPDATED BY       DATE				ACTION		DESCRIPTION  
[US-213080]     An.TranTan		 10-Oct-2024		CREATE		Init default value for 2 new permisisons
===========================================================================================  
*/
DECLARE @tbSecurityFunctions TABLE (SecurityFunctionId INT);
INSERT INTO @tbSecurityFunctions VALUES(20010025),(20010029);

DECLARE @tbHubSecurityGroups TABLE(SecurityGroupId INT, IsAdmin BIT);
INSERT INTO @tbHubSecurityGroups
SELECT 
	SecurityGroupId, 
	Administrator
FROM tbSecurityGroup
WHERE ClientNo = 114	--DMCC

--Clear all existing permissions
DELETE tbSecurityGroupSecurityFunctionPermission
WHERE SecurityFunctionNo IN (SELECT SecurityFunctionId FROM @tbSecurityFunctions)

;WITH cte AS(
	SELECT SecurityGroupId,
		SecurityFunctionId,
		IsAdmin
	FROM @tbHubSecurityGroups, @tbSecurityFunctions
)
INSERT INTO tbSecurityGroupSecurityFunctionPermission 
(  
	SecurityGroupNo  
    ,SecurityFunctionNo  
    ,IsAllowed  
    ,DLUP
) 
SELECT 
	SecurityGroupId,
	SecurityFunctionId,	
	IsAdmin, --default: allow for admin only
	GETDATE()
FROM cte 
