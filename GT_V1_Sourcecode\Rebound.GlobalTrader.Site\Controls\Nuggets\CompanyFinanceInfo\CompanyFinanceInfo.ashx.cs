//-------------------------------------------------------------------------------------------
// RP 12.01.2010:
// - convert values for YTD / Last year as they are returned in base currency
//
// RP 17.11.2009:
// - use specific query for sales info to cut down data going through pipes
// - get the YTD / last year values in one hit
//Marker     Changed by      Date         Remarks
//[001]      Vinay           11/06/2012   This need to Add Incoterms field in company section
//[002]      Shashi Keshar   21/01/2016  Need to add Insurance File No and Insured Amount
//[003]      Suhail          02/05/2018   Added Credit Limit2  
//-------------------------------------------------------------------------------------------
using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;

namespace Rebound.GlobalTrader.Site.Controls.Nuggets.Data
{
    [WebService(Namespace = "http://tempuri.org/")]
    [WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    public class CompanyFinanceInfo : Rebound.GlobalTrader.Site.Data.Base
    {

        public override void ProcessRequest(HttpContext context)
        {
            try
            {

                if (base.init(context))
                {
                    switch (Action)
                    {
                        case "GetData": GetData(); break;
                        case "loadLinkedCompanies": loadLinkedCompanies(); break;
                        case "SaveLinked": SaveLinked(); break;
                        case "CurrencyDropdownData": CurrencyDropdownData(); break;
                        case "GetLinkedAccountsCombinedInfo": GetLinkedAccountsCombinedInfo(); break;
                        case "GetCompanyDetailInactive": GetCompanyDetailInactive(); break;
                        default: WriteErrorActionNotFound(); break;
                    }
                }
            }
            catch (Exception ex) {
                var log = ex.Message;
            }
        }

        private void GetData()
        {
            List<Company> SelectedCompanies = new List<Company>();

            SelectedCompanies.Add(new Company { CompanyId = ID });
            SelectedCompanies.AddRange(Company.GetLinkedCompanies(SessionManager.ClientID, ID));

            if (SelectedCompanies.Count > 0)
            {
                for (int i = 0; i < SelectedCompanies.Count; i++)
                {
                    SelectedCompanies[i] = Company.GetSalesInfo(SelectedCompanies[i].CompanyId);
                }
            }

            JsonObject jsnList = null;
            JsonObject jsn = null;
            if (SelectedCompanies.Count > 0)
            {
                jsnList = new JsonObject(true);

                foreach (var cm in SelectedCompanies)
                {
                    jsn = new JsonObject();
                    jsn.AddVariable("Salesman", cm.SalesmanName);
                    jsn.AddVariable("SalesmanNo", cm.Salesman);
                    jsn.AddVariable("IsApproved", cm.SOApproved);
                    jsn.AddVariable("Currency", Functions.FormatCurrencyDescription(cm.SOCurrencyDescription, cm.SOCurrencyCode));
                    jsn.AddVariable("CurrencyNo", cm.SOCurrencyNo);
                    jsn.AddVariable("CurrencyCode", cm.SOCurrencyCode);
                    jsn.AddVariable("CustomerNo", cm.CustomerCode);
                    jsn.AddVariable("Rating", cm.SORating);
                    jsn.AddVariable("OnStop", cm.OnStop);
                    jsn.AddVariable("Terms", cm.SOTermsName);
                    jsn.AddVariable("TermsNo", cm.SOTermsNo);
                    jsn.AddVariable("Tax", cm.SOTaxName);
                    jsn.AddVariable("TaxNo", cm.SOTaxNo);
                    jsn.AddVariable("ContactName", cm.DefaultSOContactName);
                    jsn.AddVariable("ContactNo", cm.DefaultSOContactNo);
                    jsn.AddVariable("IsShippingWaived", cm.ShippingCharge);
                    jsn.AddVariable("ShipVia", cm.DefaultSalesShipViaName);
                    jsn.AddVariable("ShipViaNo", cm.DefaultSalesShipViaNo);
                    jsn.AddVariable("FreightVal", Functions.FormatCurrency(cm.DefaultSalesFreightCharge, 2));
                    jsn.AddVariable("ShippingCostVal", Functions.FormatCurrency(cm.DefaultSalesShippingCost, 2));
                    jsn.AddVariable("ShippingAccountNo", cm.DefaultSalesShipViaAccount);
                    string strBillToAddress = Functions.ReplaceLineBreaks(AddressManager.ToLongString(cm.DefaultBillingAddress));
                    jsn.AddVariable("BillToAddress", strBillToAddress);
                    jsn.AddVariable("ShipToAddressNo", cm.DefaultShippingAddress.AddressId);
                    jsn.AddVariable("CreditLimit", Functions.FormatConvertedCurrency(cm.CreditLimit, cm.SOCurrencyNo, cm.SOCurrencyCode, cm.CreditLimit / cm.ExchangeRate, SessionManager.ClientCurrencyID, SessionManager.ClientCurrencyCode, 2));
                    jsn.AddVariable("CreditLimitRaw", Functions.FormatCurrency(cm.CreditLimit, null, 2));
                    jsn.AddVariable("Current", Functions.FormatConvertedCurrency(cm.CurrentMonth, cm.SOCurrencyNo, cm.SOCurrencyCode, cm.CurrentMonth / cm.ExchangeRate, SessionManager.ClientCurrencyID, SessionManager.ClientCurrencyCode, 2));
                    jsn.AddVariable("Balance", Functions.FormatConvertedCurrency(cm.Balance, cm.SOCurrencyNo, cm.SOCurrencyCode, cm.Balance / cm.ExchangeRate, SessionManager.ClientCurrencyID, SessionManager.ClientCurrencyCode, 2));
                    jsn.AddVariable("Days30", Functions.FormatConvertedCurrency(cm.Days30, cm.SOCurrencyNo, cm.SOCurrencyCode, cm.Days30 / cm.ExchangeRate, SessionManager.ClientCurrencyID, SessionManager.ClientCurrencyCode, 2));
                    jsn.AddVariable("Days60", Functions.FormatConvertedCurrency(cm.Days60, cm.SOCurrencyNo, cm.SOCurrencyCode, cm.Days60 / cm.ExchangeRate, SessionManager.ClientCurrencyID, SessionManager.ClientCurrencyCode, 2));
                    jsn.AddVariable("Days90", Functions.FormatConvertedCurrency(cm.Days90, cm.SOCurrencyNo, cm.SOCurrencyCode, cm.Days90 / cm.ExchangeRate, SessionManager.ClientCurrencyID, SessionManager.ClientCurrencyCode, 2));
                    jsn.AddVariable("Days120", Functions.FormatConvertedCurrency(cm.Days120, cm.SOCurrencyNo, cm.SOCurrencyCode, cm.Days120 / cm.ExchangeRate, SessionManager.ClientCurrencyID, SessionManager.ClientCurrencyCode, 2));
                    jsn.AddVariable("DLUP", Functions.FormatDLUP(cm.DLUP, cm.UpdatedBy));
                    jsn.AddVariable("ApprovedByAndDate", Functions.FormatUPbyOn(cm.SOApprovedDate, cm.SOApprovedBy));
                    jsn.AddVariable("IsTraceability", cm.IsTraceability);
                    jsn.AddVariable("ApprovedByAndDate", Functions.FormatUPbyOn(cm.SOApprovedDate, cm.SOApprovedBy));

                    jsn.AddVariable("InsuranceFileNo", cm.InsuranceFileNo);
                    jsn.AddVariable("InsuredAmountRaw", Functions.FormatCurrency(cm.InsuredAmount, null, 2));
                    //jsn.AddVariable("InsuredAmount", Functions.FormatConvertedCurrency(cm.InsuredAmount, cm.SOCurrencyNo, cm.SOCurrencyCode, cm.InsuredAmount / cm.ExchangeRate, SessionManager.ClientCurrencyID, SessionManager.ClientCurrencyCode, 2));
                    jsn.AddVariable("InsuredAmount", Functions.FormatConvertedCurrency(cm.InsuredAmount, cm.InsuredAmountCurrencyNo, cm.InsuredAmountCurrencyCode, cm.InsuredAmount / cm.ExchangeRate, SessionManager.ClientCurrencyID, SessionManager.ClientCurrencyCode, 2));
                    jsn.AddVariable("StopStatus", cm.StopStatus);
                    jsn.AddVariable("GlobalCurNo", cm.GlobalCurrencyNo);
                    jsn.AddVariable("Days1", Functions.FormatConvertedCurrency(cm.Days1, cm.SOCurrencyNo, cm.SOCurrencyCode, cm.Days1 / cm.ExchangeRate, SessionManager.ClientCurrencyID, SessionManager.ClientCurrencyCode, 2));
                    jsn.AddVariable("WarehouseName", cm.WarehouseName);
                    jsn.AddVariable("WarehouseNo", cm.WarehouseNo);
                    //this year SO value to date
                    double? dblSalesOrderYTD = 0;
                    BLL.Company cmThisYear = BLL.Company.SummariseThisYearSalesOrderValue(cm.CompanyId);
                    if (cmThisYear != null) dblSalesOrderYTD = cmThisYear.SalesOrderValueYTDInBase;
                    jsn.AddVariable("ThisYearValue", Functions.FormatCurrency(dblSalesOrderYTD, cm.SOCurrencyCode, 2));
                    cmThisYear = null;

                    //last year SO value
                    double? dblValueLastYear = 0;
                    BLL.Company cmLastYear = BLL.Company.SummariseLastYearSalesOrderValue(cm.CompanyId);
                    if (cmLastYear != null) dblValueLastYear = cmLastYear.SalesOrderValueLastYearInBase;
                    jsn.AddVariable("LastYearValue", Functions.FormatCurrency(dblValueLastYear, cm.SOCurrencyCode, 2));
                    cmLastYear = null;
                    double dblOpenSOTotal = 0;
                    DateTime currencyDate;
                    List<SalesOrder> lst = SalesOrder.GetListOpenForCompany(cm.CompanyId);
                    for (int i = 0; i < lst.Count; i++)
                    {
                        SalesOrder SOTotal = SalesOrder.GetOpenLineSummaryValues(lst[i].SalesOrderId);
                        currencyDate = lst[i].CurrencyDate == null ? (DateTime)lst[i].DateOrdered : (DateTime)lst[i].CurrencyDate;
                        if (SOTotal.CurrencyNo != cm.SOCurrencyNo)
                        {
                            dblOpenSOTotal += BLL.Currency.ConvertValueBetweenTwoCurrencies(SOTotal.TotalValue, SOTotal.CurrencyNo, Convert.ToInt32(cm.SOCurrencyNo), currencyDate);
                        }
                        else
                        {
                            dblOpenSOTotal += Convert.ToDouble(SOTotal.TotalValue);
                        }
                        SOTotal = null;
                    }
                    lst = null;
                    double dblBalanceWithOpenOrders = Convert.ToDouble(cm.Balance) + dblOpenSOTotal;
                    jsn.AddVariable("BalanceWithOpenSalesOrders", Functions.FormatCurrency(dblBalanceWithOpenOrders, cm.SOCurrencyCode, 2));
                    jsn.AddVariable("BalanceWithOpenSalesOrdersVal", dblBalanceWithOpenOrders);
                    jsn.AddVariable("InvoiceNotExport", Functions.FormatCurrency(SalesOrder.GetInvoiceNotExported(cm.CompanyId), cm.SOCurrencyCode, 2));

                    jsn.AddVariable("NotesToInvoice", Functions.ReplaceLineBreaks(cm.NotesToInvoice));
                    // [002] Code Start
                    jsn.AddVariable("ActualCreditLimit", Functions.FormatConvertedCurrency(cm.ActualCreditLimit, cm.SOCurrencyNo, cm.SOCurrencyCode, cm.ActualCreditLimit / cm.ExchangeRate, SessionManager.ClientCurrencyID, SessionManager.ClientCurrencyCode, 2));
                    jsn.AddVariable("ActualCreditLimitRaw", Functions.FormatCurrency(cm.ActualCreditLimit, null, 2));
                    jsn.AddVariable("InsuredAmountCurrencyNo", cm.InsuredAmountCurrencyNo);
                    jsn.AddVariable("CompanyName", cm.CompanyName);
                    jsn.AddVariable("CompanyID", cm.CompanyId);
                    jsn.AddVariable("YearToDate", GetYearToDateNew(cm.CompanyId));
                    jsn.AddVariable("LastYear", GetLastYearNew(cm.CompanyId));
                    // [002] Code End
                    jsnList.AddVariable(jsn);
                    jsn.Dispose();
                    jsn = null;
                }

                JsonObject finaljsndata = new JsonObject();
                finaljsndata.AddVariable("SelectedCompanies", jsnList);
                OutputResult(finaljsndata);
                SelectedCompanies = null;
                finaljsndata.Dispose();
                finaljsndata = null;
            }
        }
        private void loadLinkedCompanies()
        {
            string VatNo = GetFormValue_String("VatNo");

            List<Company> SelectedCompanies = Company.GetLinkedCompanies(SessionManager.ClientID, ID);

            if (SelectedCompanies.Count > 0 & VatNo == null)
            {
                VatNo = SelectedCompanies[0].VatNo;
            }
            else
            {
                if (VatNo == null)
                {
                    VatNo = string.Empty;
                }
            }

            List<Company> AllCompanies = Company.GetCompanyByVatNo(SessionManager.ClientID, VatNo, ID);

            try
            {
                JsonObject jsn = new JsonObject();
                JsonObject jsnSelected = new JsonObject(true);
                List<int> lstSelected = new List<int>();

                foreach (var company in SelectedCompanies)
                {
                    JsonObject jsnItem = new JsonObject();
                    jsnItem.AddVariable("ID", company.CompanyId);
                    jsnItem.AddVariable("Name", company.CompanyName);
                    jsnItem.AddVariable("LinkCompanyID", company.LinkCompanyID);
                    if (VatNo == null || VatNo == string.Empty)
                    {
                        jsnItem.AddVariable("VatNo", company.VatNo);
                    }
                    else
                    {
                        jsnItem.AddVariable("VatNo", VatNo);
                    }
                    jsnSelected.AddVariable(jsnItem);
                    jsnItem.Dispose(); jsnItem = null;
                    lstSelected.Add(company.CompanyId);
                }
                jsn.AddVariable("Selected", jsnSelected);


                JsonObject jsnUnselected = new JsonObject(true);
                foreach (var company in AllCompanies)
                {
                    if (!lstSelected.Contains(company.CompanyId))
                    {
                        JsonObject jsnItem = new JsonObject();
                        jsnItem.AddVariable("ID", company.CompanyId);
                        jsnItem.AddVariable("Name", company.CompanyName);
                        jsnUnselected.AddVariable(jsnItem);
                        jsnItem.Dispose(); jsnItem = null;
                    }
                }

                jsn.AddVariable("Unselected", jsnUnselected);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }
        private void SaveLinked()
        {
            try
            {
                JsonObject jsn = new JsonObject();
                //Array aryUnselected = Functions.JavascriptStringToArray(GetFormValue_String("Unselected"));
                string selectedCompanines = GetFormValue_String("Selected");
                string VatNo = GetFormValue_String("VatNo");
                bool success = Company.UpdateLinkedCompanies(ID, VatNo, selectedCompanines + "||" + ID, Convert.ToInt32(SessionManager.LoginID));
                jsn.AddVariable("Result", true);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }
        private void CurrencyDropdownData()
        {
            JsonObject jsn = new JsonObject();
            JsonObject jsnList = new JsonObject(true);
            List<BLL.Currency> lst = BLL.Currency.DropDownSellForClient(SessionManager.ClientID);
            for (int i = 0; i < lst.Count; i++)
            {
                JsonObject jsnItem = new JsonObject();
                jsnItem.AddVariable("ID", lst[i].CurrencyId);
                jsnItem.AddVariable("Name", String.Format("{1} - {0}", lst[i].CurrencyDescription, lst[i].CurrencyCode));
                jsnItem.AddVariable("Code", lst[i].CurrencyCode);
                jsnList.AddVariable(jsnItem);
                jsnItem.Dispose(); jsnItem = null;
            }
            lst.Clear(); lst = null;
            jsn.AddVariable("Currencies", jsnList);
            jsnList.Dispose(); jsnList = null;
            OutputResult(jsn);
            jsn.Dispose(); jsn = null;
        }
        private void GetLinkedAccountsCombinedInfo()
        {
            JsonObject jsn = new JsonObject();
            int SelectedCurrency = GetFormValue_Int("SelectedCurrencyNo");
            string CurrencyCode = GetFormValue_String("CurrencyCode");
            if (SelectedCurrency !=0 )
            {
                List<Company> lst = Company.GetLinkedAccountsCombinedInfo(SessionManager.ClientID, ID);
                double ActualCreditLimit = 0, CreditLimit = 0, InsuredAmount = 0, ConversionRate=0;
                for (int i = 0; i < lst.Count; i++)
                {
                    //if (lst[i].SOCurrencyNo == null)
                    //{
                    //    lst[i].SOCurrencyNo = SessionManager.ClientCurrencyID;
                    //    lst[i].InsuredAmountCurrencyNo = SessionManager.ClientCurrencyID;
                    //}
                    ActualCreditLimit += Currency.ConvertValueBetweenTwoCurrencies(lst[i].ActualCreditLimit, (int)lst[i].SOCurrencyNo, SelectedCurrency, DateTime.Now);
                    CreditLimit += Currency.ConvertValueBetweenTwoCurrencies(lst[i].CreditLimit, (int)lst[i].SOCurrencyNo, SelectedCurrency, DateTime.Now);
                    InsuredAmount += Currency.ConvertValueBetweenTwoCurrencies(lst[i].InsuredAmount, (int)lst[i].InsuredAmountCurrencyNo, SelectedCurrency, DateTime.Now);
                    //var ExcahngeRate = Currency.GetCurrentRateAtDate
                }
                ConversionRate = Currency.ConvertValueBetweenTwoCurrencies(1, SelectedCurrency, (int) SessionManager.ClientCurrencyID, DateTime.Now);
                jsn.AddVariable("ActualCreditLimit", Functions.FormatCurrency(ActualCreditLimit, CurrencyCode,2,true));
                jsn.AddVariable("CreditLimit", Functions.FormatCurrency(CreditLimit, CurrencyCode,2,true));
                jsn.AddVariable("InsuredAmount", Functions.FormatCurrency(InsuredAmount, CurrencyCode,2,true));
                jsn.AddVariable("ToConversionRate", Functions.FormatCurrency(ConversionRate, SessionManager.ClientCurrencyCode));
                jsn.AddVariable("FromConversionRate", Functions.FormatCurrency(1, CurrencyCode));
            }
            OutputResult(jsn);
            jsn.Dispose(); jsn = null;
        }
        private string GetYearToDateNew(int CompanyID)
        {
            BLL.Company cm = BLL.Company.SummariseThisYearandLastYearSalesValue(CompanyID, true);
            string YearToDate = string.Empty;
            if (cm == null)
            {
                YearToDate= Functions.FormatCurrency(0, 2);
            }
            else
            {
                 YearToDate =  string.Format("{0} | {1} ({2})", Functions.FormatCurrency(cm.SalesResale, SessionManager.ClientCurrencyCode, 2), Functions.FormatCurrency(cm.SalesGrossProfit, SessionManager.ClientCurrencyCode, 2), Functions.FormatPercentage(cm.Margin, 2));
            }
            cm = null;
            return YearToDate;
        }
        private string GetLastYearNew(int CompanyID)
        {
            BLL.Company cm = BLL.Company.SummariseThisYearandLastYearSalesValue(CompanyID, false);
            string LastYearNew = string.Empty;
            if (cm == null)
            {
                LastYearNew = Functions.FormatCurrency(0, 2);
            }
            else
            {
                LastYearNew = string.Format("{0} | {1} ({2})", Functions.FormatCurrency(cm.SalesResale, SessionManager.ClientCurrencyCode, 2), Functions.FormatCurrency(cm.SalesGrossProfit, SessionManager.ClientCurrencyCode, 2), Functions.FormatPercentage(cm.Margin, 2));
            }
            cm = null;
            return LastYearNew;
        }

        private void GetCompanyDetailInactive()
        {
            Company cm = Company.GetCompanyDetailInactive(ID);
            if (cm == null)
            {
                WriteErrorDataNotFound();
            }
            else
            {
                JsonObject jsn = null;
                if (cm != null)
                {
                    jsn = new JsonObject();
                    jsn.AddVariable("CompanyId", cm.CompanyId);
                    jsn.AddVariable("Inactive", cm.Inactive);
                }
                cm = null;
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            cm = null;
        }
    }
}
