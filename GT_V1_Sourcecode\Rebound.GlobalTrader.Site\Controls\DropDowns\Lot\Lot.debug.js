///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//
// RP 06.10.2009:
// - Changes due to changes in base class
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");

Rebound.GlobalTrader.Site.Controls.DropDowns.Lot = function (element) {
    Rebound.GlobalTrader.Site.Controls.DropDowns.Lot.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.DropDowns.Lot.prototype = {

    get_intSourceLotToExclude: function () { return this._intSourceLotToExclude; }, set_intSourceLotToExclude: function (value) { if (this._intSourceLotToExclude !== value) this._intSourceLotToExclude = value; },
    get_intGlobalLoginClientNo: function () { return this._intGlobalLoginClientNo; }, set_intGlobalLoginClientNo: function (v) { if (this._intGlobalLoginClientNo !== v) this._intGlobalLoginClientNo = v; },

    initialize: function () {
        Rebound.GlobalTrader.Site.Controls.DropDowns.Lot.callBaseMethod(this, "initialize");
        this.addSetupDataCall(Function.createDelegate(this, this.setupDataCall));
        this.addGetDataOK(Function.createDelegate(this, this.dataCallOK));
    },

    dispose: function () {
        if (this.isDisposed) return;
        this._intSourceLotToExclude = null;
        this._intGlobalLoginClientNo = null;
        Rebound.GlobalTrader.Site.Controls.DropDowns.Lot.callBaseMethod(this, "dispose");
    },

    setupDataCall: function () {
        //alert(this._intGlobalLoginClientNo);
        this._objData.set_PathToData("controls/DropDowns/Lot");
        this._objData.set_DataObject("Lot");
        this._objData.set_DataAction("GetData");
        this._objData.addParameter("SourceLotToExclude", this._intSourceLotToExclude);
        this._objData.addParameter("GlobalLoginClientNo", this._intGlobalLoginClientNo);
    },

    dataCallOK: function () {
        var result = this._objData._result;
        if (result != null) {
            if (result.Lots) {
                for (var i = 0; i < result.Lots.length; i++) {
                    this.addOption(result.Lots[i].Name, result.Lots[i].ID);
                }
            }
        }
    }

};

Rebound.GlobalTrader.Site.Controls.DropDowns.Lot.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.Lot", Rebound.GlobalTrader.Site.Controls.DropDowns.Base);
