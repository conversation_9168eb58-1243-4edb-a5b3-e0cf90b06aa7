using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.BLL;
using Rebound.GlobalTrader.Site.Controls;

namespace Rebound.GlobalTrader.Site.Controls.Nuggets {
    public partial class BomUploadedExcel : Base
    {

		#region Locals
        protected Panel _pnlPDFDocuments;
        #endregion

		#region Properties
        private int _intBomId = -1;
		public int BomId {
            get { return _intBomId; }
            set { _intBomId = value; }
		}
        private string _strSectionName;
        public string SectionName
        {
            get { return _strSectionName; }
            set { _strSectionName = value; }
        }
        #endregion

		#region Overrides

		/// <summary>
		/// Oninit
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
			base.OnInit(e);
			WireUpControls();
            AddScriptReference("Controls.Nuggets.BomUploadedExcel.BomUploadedExcel.js");
            TitleText = Functions.GetGlobalResource("Nuggets", "UploadedExcelFiles");
            if (_objQSManager.ClientBOMID > 0) _intBomId = _objQSManager.ClientBOMID;
		}

		protected override void OnPreRender(EventArgs e) {
            SetupScriptDescriptors();
			base.OnPreRender(e);
		}

		#endregion

		/// <summary>
		/// Sets up AJAX script descriptors
		/// </summary>
		private void SetupScriptDescriptors() {
            _scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.Nuggets.BomUploadedExcel", ctlDesignBase.ClientID);
			_scScriptControlDescriptor.AddProperty("intBomId", _intBomId);
            _scScriptControlDescriptor.AddProperty("strSectionName", SectionName);
            _scScriptControlDescriptor.AddElementProperty("pnlPDFDocuments", _pnlPDFDocuments.ClientID);
			
		}

		/// <summary>
		/// Wire up controls from the ascx
		/// </summary>
		private void WireUpControls() {
            _pnlPDFDocuments = (Panel)FindContentControl("pnlPDFDocuments");
		}

	}
}
