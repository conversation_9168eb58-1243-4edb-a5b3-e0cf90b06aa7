/* Marker     changed by      date         Remarks
/* [0001]      A<PERSON><PERSON><PERSON>   14/07/2014  ESMS Ref:172 - Issue in Part Searching in whole GT */

using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;
using Rebound.GlobalTrader.Site.Enumerations;

namespace Rebound.GlobalTrader.Site.Controls.ItemSearch.Data {
	[WebService(Namespace = "http://tempuri.org/")]
	[WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    public class GITempLotNumber : Rebound.GlobalTrader.Site.Data.ItemSearch.Base
    {

		protected override void GetData() {
            List<BLL.GoodsInLine> lst = null;
			try {
                lst = GoodsInLine.GITempLotSearch(
                   SessionManager.ClientID,
                   GetFormValue_NullableInt("Order", 0),
                   GetFormValue_NullableInt("SortDir", SortColumnDirection.ASC),
                   GetFormValue_NullableInt("PageIndex", 0),
                   GetFormValue_NullableInt("PageSize", 10),
                   GetFormValue_NullableInt("GoodsInId"),
                   GetFormValue_NullableInt("GoodsInLineId"),
                   SessionManager.LoginID

                   );
                bool active = false;
				JsonObject jsn = new JsonObject();
				JsonObject jsnItems = new JsonObject(true);
				for (int i = 0; i < lst.Count; i++) {
					JsonObject jsnItem = new JsonObject();

                    active = (lst[i].InvoiceLineNo > 0) ? true : false;
                    jsnItem.AddVariable("ID", lst[i].LotNoId);
                    jsnItem.AddVariable("GoodsInNo", lst[i].GoodsInNo);
                    jsnItem.AddVariable("LotNo", lst[i].LotNumber);
                    jsnItem.AddVariable("SubGroup", lst[i].SubGroup);
                    jsnItem.AddVariable("Status", lst[i].Status);
                    jsnItem.AddVariable("InvoiceLineNo", lst[i].InvoiceLineNo);
                    jsnItem.AddVariable("Inactive", active);
                    jsnItem.AddVariable("GoodsNumber", lst[i].GoodsInNumber);
                    jsnItems.AddVariable(jsnItem);
                    jsnItem.Dispose();
                    jsnItem = null;
				}
				jsn.AddVariable("Count", (lst.Count > 0) ? lst[0].RowCnt : 0);
				jsn.AddVariable("Results", jsnItems);
				OutputResult(jsn);
				jsnItems.Dispose();
				jsnItems = null;
				jsn.Dispose();
				jsn = null;
			} catch (Exception ex) {
				WriteError(ex);
			} finally {
				lst = null;
			}
			base.GetData();
		}
	}
}
