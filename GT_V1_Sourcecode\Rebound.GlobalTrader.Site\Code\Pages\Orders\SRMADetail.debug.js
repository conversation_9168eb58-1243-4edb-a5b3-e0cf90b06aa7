///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference name="~/Controls/IconButton/IconButton.js" />
///<reference name="~/Controls/ReboundTextBox/ReboundTextBox.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
/*
Marker     Changed by      Date         Remarks
[001]      Vinay           07/05/2012   This need to upload pdf document for supplierRMA section
*/
//[002]      <PERSON><PERSON> Keshar   24/10/2016   Added New Link print document in Reference of Hub. IsHUB
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Pages.Orders");

Rebound.GlobalTrader.Site.Pages.Orders.SRMADetail = function(el) { 
	Rebound.GlobalTrader.Site.Pages.Orders.SRMADetail.initializeBase(this, [el]);
};

Rebound.GlobalTrader.Site.Pages.Orders.SRMADetail.prototype = {

	get_intSRMAID: function() { return this._intSRMAID; }, 	set_intSRMAID: function(v) { if (this._intSRMAID !== v)  this._intSRMAID = v; }, 
	get_ctlMainInfo: function() { return this._ctlMainInfo; }, 	set_ctlMainInfo: function(v) { if (this._ctlMainInfo !== v)  this._ctlMainInfo = v; }, 
	get_ctlLines: function() { return this._ctlLines; }, 	set_ctlLines: function(v) { if (this._ctlLines !== v)  this._ctlLines = v; },
	get_btnPrint: function() { return this._btnPrint; }, set_btnPrint: function(v) { if (this._btnPrint !== v) this._btnPrint = v; },
	//[001] code start
	get_ctlSRMADocuments: function() { return this._ctlSRMADocuments; }, set_ctlSRMADocuments: function(v) { if (this._ctlSRMADocuments !== v) this._ctlSRMADocuments = v; },
	//[001] code end
	get_ctlSRMAPDFDragDrop: function() { return this._ctlSRMAPDFDragDrop; }, set_ctlSRMAPDFDragDrop: function(v) { if (this._ctlSRMAPDFDragDrop !== v) this._ctlSRMAPDFDragDrop = v; },
	initialize: function() {
		Rebound.GlobalTrader.Site.Pages.Orders.SRMADetail.callBaseMethod(this, "initialize");
	},

	goInit: function() {
		if (this._btnPrint) this._btnPrint.addPrint(Function.createDelegate(this, this.printSRMA));
		if (this._btnPrint) this._btnPrint.addEmail(Function.createDelegate(this, this.emailSRMA));
		if (this._ctlMainInfo) this._ctlMainInfo.addGetDataComplete(Function.createDelegate(this, this.ctlMainInfo_GetDataComplete));
		if (this._ctlMainInfo) this.setLineFieldsFromHeader();
		//[001] code start
		if (this._ctlSRMADocuments) this._ctlSRMADocuments.getData();
		//[001] code end
		if (this._ctlSRMAPDFDragDrop) this._ctlSRMAPDFDragDrop.getData();
	    //[002]  Start Here
		if (this._btnPrint) this._btnPrint.addExtraButtonClick(Function.createDelegate(this, this.printOtherDocs));
	    //[002]  End Here
		Rebound.GlobalTrader.Site.Pages.Orders.SRMADetail.callBaseMethod(this, "goInit");
	},
	
	dispose: function() { 
		if (this.isDisposed) return;
		if (this._btnPrint) this._btnPrint.dispose();
		if (this._ctlMainInfo) this._ctlMainInfo.dispose();
		if (this._ctlLines) this._ctlLines.dispose();
		this._btnPrint = null;
		this._ctlMainInfo = null;
		this._ctlLines = null;
		this._intSRMAID = null;
		//[001] code start
		if (this._ctlSRMADocuments) this._ctlSRMADocuments.dispose();
		this._ctlSRMADocuments = null;
		//[001] code end
		Rebound.GlobalTrader.Site.Pages.Orders.SRMADetail.callBaseMethod(this, "dispose");
	},
	
	printSRMA: function() {
		$R_FN.openPrintWindow($R_ENUM$PrintObject.SupplierRMA, this._intSRMAID);
	},
	
	emailSRMA: function() {
		$R_FN.openPrintWindow($R_ENUM$PrintObject.SupplierRMA, this._intSRMAID, true);
	},
	
	ctlMainInfo_GetDataComplete: function() {
		this.setLineFieldsFromHeader();
	},
    //[002] Start Here
	printOtherDocs: function () {
	 
	    if (this._btnPrint._strExtraButtonClickCommand == "PrintHUBSRMA") $R_FN.openPrintWindow($R_ENUM$PrintObject.PrintHUBSRMA, this._intSRMAID);
	    if (this._btnPrint._strExtraButtonClickCommand == "EmailHUBSRMA") $R_FN.openPrintWindow($R_ENUM$PrintObject.PrintHUBSRMA, this._intSRMAID, true);
	    if (this._btnPrint._strExtraButtonClickCommand == "PrintLog") $R_FN.openPrintLogWindow($R_ENUM$PrintObject.PrintLog, this._intSRMAID, false, "SupplierRMA");
	   	},
    //[002]  End Here
	setLineFieldsFromHeader: function() {
		var strSupplierName = this._ctlMainInfo.getFieldValue("hidSupplier");
		var strSRMANumber = this._ctlMainInfo.getFieldValue("hidNo");
		if (this._ctlLines._frmAdd) this._ctlLines._frmAdd.setFieldsFromHeader(strSRMANumber, strSupplierName);
		if (this._ctlLines._frmEdit) this._ctlLines._frmEdit.setFieldsFromHeader(strSRMANumber, strSupplierName);
		if (this._ctlLines._frmDelete) this._ctlLines._frmDelete.setFieldsFromHeader(strSRMANumber, strSupplierName);
		if (this._ctlLines._frmAllocate) this._ctlLines._frmAllocate.setFieldsFromHeader(strSRMANumber, strSupplierName);
		if (this._ctlLines._frmDeallocate) this._ctlLines._frmDeallocate.setFieldsFromHeader(strSRMANumber, strSupplierName);
		if (this._ctlLines) this._ctlLines._blnSRMAAutoGenerated  =  this._ctlMainInfo.getFieldValue("hidIsSRMAAutoGen");
	}

};

Rebound.GlobalTrader.Site.Pages.Orders.SRMADetail.registerClass("Rebound.GlobalTrader.Site.Pages.Orders.SRMADetail", Rebound.GlobalTrader.Site.Pages.Content, Sys.IDisposable);
