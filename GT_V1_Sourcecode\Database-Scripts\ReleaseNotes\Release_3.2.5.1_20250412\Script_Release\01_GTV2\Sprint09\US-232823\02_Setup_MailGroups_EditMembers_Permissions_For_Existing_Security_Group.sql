﻿/*   
===========================================================================================  
TASK	        UPDATED BY	    DATE			ACTION		DESCRIPTION  
[US-225153]		Nam Nguyen		18-Feb-2025		Insert		Add a new permission to control the display of an Edit Members button
===========================================================================================  
*/

--Backup tbSecurityGroupSecurityFunctionPermission
DECLARE @BackupTableName NVARCHAR(100)
SET @BackupTableName = 'tbSecurityGroupSecurityFunctionPermission_bk_' 
    + FORMAT(GETDATE(), 'dd_MM_yy_HHmmss')

DECLARE @SQL NVARCHAR(MAX)
SET @SQL = 'SELECT * INTO ' + QUOTENAME(@BackupTableName) + 
           ' FROM dbo.tbSecurityGroupSecurityFunctionPermission'
EXEC sp_executesql @SQL


DECLARE @tbSecurityFunctions TABLE (SecurityFunctionId INT);

/* Get SecurityFunctionId of new security functions*/
INSERT INTO @tbSecurityFunctions (SecurityFunctionId)
SELECT SecurityFunctionId
FROM tbSecurityFunction
WHERE FunctionName IN (
	'Setup_CompanySettings_MailGroups_EditMembers'
)

/* Set default value of new functions for all groups: ADMIN: ACCESS, Other: NOT-ACCESS */
DECLARE @SecurityGroupId INT
DECLARE @SecurityFunctionId INT
DECLARE @IsAdmin BIT

DECLARE @dbcursor CURSOR SET @dbcursor = CURSOR
FOR
SELECT sg.SecurityGroupId,
	SecurityFunctionId,
	sg.Administrator AS IsAdmin
FROM tbSecurityGroup sg,
	@tbSecurityFunctions sf

OPEN @dbcursor

FETCH NEXT
FROM @dbcursor
INTO @SecurityGroupId,
	@SecurityFunctionId,
	@IsAdmin

WHILE @@FETCH_STATUS = 0
BEGIN
	IF NOT EXISTS (
			SELECT TOP 1 1
			FROM tbSecurityGroupSecurityFunctionPermission
			WHERE SecurityGroupNo = @SecurityGroupId
				AND SecurityFunctionNo = @SecurityFunctionId
			)
	BEGIN
		PRINT 'InSert ' + CAST(@SecurityGroupId AS VARCHAR(10)) + ' ' + CAST(@SecurityFunctionId AS VARCHAR(10)) + ' ' + CAST(@IsAdmin AS VARCHAR(10))

		INSERT INTO tbSecurityGroupSecurityFunctionPermission (
			SecurityGroupNo,
			SecurityFunctionNo,
			IsAllowed,
			DLUP,
			UpdatedBy
			)
		SELECT @SecurityGroupId,
			@SecurityFunctionId,
			@IsAdmin, --default: allow for admin only
			GETDATE(),
			1 --system administrator
	END
	ELSE
	BEGIN
		PRINT 'Existing ' + CAST(@SecurityGroupId AS VARCHAR(10)) + ' ' + CAST(@SecurityFunctionId AS VARCHAR(10)) + ' ' + CAST(@IsAdmin AS VARCHAR(10))
	END

	FETCH NEXT
	FROM @dbcursor
	INTO @SecurityGroupId,
		@SecurityFunctionId,
		@IsAdmin
END
/*============END===========*/