﻿using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;
using org.bouncycastle.asn1.ocsp;

namespace Rebound.GlobalTrader.Site.Controls.DropDowns {
	public partial class SalesOrderAuthorised : Base {

		public int InvoiceID { get; set; }

        protected override void OnInit(EventArgs e)
        {
            //var reqUrlFromV2 = 
            //InvoiceID = 
            base.OnInit(e);
        }

        protected override void OnLoad(EventArgs e) {
            SetDropDownType("SalesOrderAuthorised");
            AddScriptReference("Controls.DropDowns.SalesOrderAuthorised.SalesOrderAuthorised");
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.DropDowns.SalesOrderAuthorised", ClientID);
			_scScriptControlDescriptor.AddProperty("intInvoiceID", InvoiceID);
			base.OnLoad(e);
		}

	}
}