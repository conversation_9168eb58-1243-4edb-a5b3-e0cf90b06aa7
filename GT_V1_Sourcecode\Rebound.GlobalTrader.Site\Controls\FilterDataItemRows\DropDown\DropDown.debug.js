///<reference name="MicrosoftAjax.js" />
///<reference path="~/Common/Data/Data.js" />
///<reference path="~/Common/Functions/Functions.js" />
///<reference path="~/Controls/IconButton/IconButton.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - add full disposing event
//
// RP 21.12.2009:
// - add getExtraText function
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.FilterDataItemRows");

Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.DropDown = function(element) { 
	Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.DropDown.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.DropDown.prototype = {

	get_ddl: function() { return this._ddl; }, set_ddl: function(value) { if (this._ddl !== value)  this._ddl = value; }, 
	
	addDropDownChanged: function(handler) { this.get_events().addHandler("DropDownChanged", handler); },
	removeDropDownChanged: function(handler) { this.get_events().removeHandler("DropDownChanged", handler); },
	onDropDownChanged: function() {
		var handler = this.get_events().getHandler("DropDownChanged");
		if (handler) handler(this, Sys.EventArgs.Empty);
	},

	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.DropDown.callBaseMethod(this, "initialize");
		if (this._ddl) {
			this._ddl.addChanged(Function.createDelegate(this, this.onDropDownChanged));
			if (this._ddl._lbx)  {
				$addHandler(this._ddl._lbx, "focus", Function.createDelegate(this, this.onFocus));
				$addHandler(this._ddl._lbx, "blur", Function.createDelegate(this, this.onBlur));
			}
		}
	},

	dispose: function() { 
		if (this.isDisposed) return;
		if (this.get_element()) $clearHandlers(this.get_element());
		if (this._ddl) {
			if (this._ddl._lbx) $clearHandlers(this._ddl._lbx);
			this._ddl.dispose();
		}
		this._ddl = null;
		Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.DropDown.callBaseMethod(this, "dispose");
	},
	
	onFocus: function() {
		this.enableField(true);
	},
	
	onBlur: function() {
		this.enableField(this.isEntered());
	},
	
	getValue: function() {
		if (this._ddl) {
			return this._ddl.getValue();
		} else {
			return null;
		}
	},
	
	getText: function() {
		if (this._ddl) {
			return this._ddl.getText().trim();
		} else {
			return "";
		}
	},
	
	getExtraText: function() {
		if (this._ddl) {
			return this._ddl.getExtraText();
		} else {
			return "";
		}
	},

	setValue: function(v) {
		if (!this._ddl) return;
		if (typeof(v) == "undefined" || v == null) v = "";
		this._ddl.setInitialValue(v);
		this._ddl.setValue(v);
		this.enableField(v != "" && v != this._ddl._strNoValue_Value);
	},
	
	reset: function() {
		if (!this._ddl) return;
		this._ddl.reset();
		this.enableField(false);
	},
	
	getDropDownData: function() {
		if (!this._ddl) return;
		if (!this._blnGotDropDownData) this._ddl.onRefresh();
		this._blnGotDropDownData = true;
	},
	
	isEntered: function() {
		return !this._ddl.isSetAsNoValue();
	}	
	
};

Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.DropDown.registerClass("Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.DropDown", Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.Base, Sys.IDisposable);
