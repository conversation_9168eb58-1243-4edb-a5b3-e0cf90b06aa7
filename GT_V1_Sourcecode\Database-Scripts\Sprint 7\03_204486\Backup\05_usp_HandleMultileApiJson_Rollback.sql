﻿GO

IF OBJECT_ID('dbo.usp_HandleMultileApiJson', 'P') IS NOT NULL
BEGIN
    DROP PROCEDURE dbo.usp_HandleMultileApiJson;
END

GO
	/****** Object:  StoredProcedure [dbo].[usp_HandleMultileApiJson]    Script Date: 7/26/2024 11:42:06 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


		 CREATE procedure [dbo].[usp_HandleMultileApiJson]          
		 (          
			 @ResultJson nvarchar(max),          
			 --@ApiName varchar(200),          
			 @ClientId int  ,    
			 @PartSearch nvarchar(max),  
			 @ApiURLKeyId int   ,
			  @UserId int = null
			 )          
			 As          
			 Begin          
			  Declare @ApiFullName varchar(100);          
			   Set @ApiFullName=(Select ApiName from tbApiURLKey where ApiURLKeyId=@ApiURLKeyId)          
			  if(@ApiURLKeyId=1)          
				exec Usp_InsertSupplierAPIDataFE @ClientId,@ResultJson  ,@PartSearch,@ApiURLKeyId,@UserId       
  
			 End          

GO

