using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;
using Rebound.GlobalTrader.Site.Enumerations;

namespace Rebound.GlobalTrader.Site.Controls.ItemSearch.Data {
	[WebService(Namespace = "http://tempuri.org/")]
	[WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
	public class Service : Rebound.GlobalTrader.Site.Data.ItemSearch.Base {

		protected override void GetData() {
			List<BLL.Service> lst = null;
			try {
                int? intGlobalClientNo;
                intGlobalClientNo = GetFormValue_NullableInt("GlobalClientNo");
				lst = BLL.Service.ItemSearch(
                    (intGlobalClientNo.HasValue && intGlobalClientNo.Value>0)?intGlobalClientNo.Value:	SessionManager.ClientID,
					GetFormValue_NullableInt("Order", 0),
					GetFormValue_NullableInt("SortDir", SortColumnDirection.ASC),
					GetFormValue_NullableInt("PageIndex", 0),
					GetFormValue_NullableInt("PageSize", 10),
                   // GetFormValue_StringForNameSearch("Name"),
                    GetFormValue_StringForNameSearchDecode("Name"),   
					GetFormValue_String("Description"),
					GetFormValue_NullableInt("LotNo")
					);
				JsonObject jsn = new JsonObject();
				JsonObject jsnItems = new JsonObject(true);
				for (int i = 0; i < lst.Count; i++) {
					JsonObject jsnItem = new JsonObject();
					jsnItem.AddVariable("ID", lst[i].ServiceId);
					jsnItem.AddVariable("Name", lst[i].ServiceName);
					jsnItem.AddVariable("Desc", lst[i].ServiceDescription);
                    jsnItem.AddVariable("Cost", Functions.FormatCurrency(lst[i].Cost, lst[i].ClientBaseCurrencyCode));
                    jsnItem.AddVariable("Price", Functions.FormatCurrency(lst[i].Price, lst[i].ClientBaseCurrencyCode));
                    //jsnItem.AddVariable("Cost", Functions.FormatCurrency(lst[i].Cost, SessionManager.ClientCurrencyCode));
                    //jsnItem.AddVariable("Price", Functions.FormatCurrency(lst[i].Price, SessionManager.ClientCurrencyCode));
					jsnItems.AddVariable(jsnItem);
					jsnItem.Dispose(); jsnItem = null;
				}
				jsn.AddVariable("Count", (lst.Count > 0) ? lst[0].RowCnt : 0);
				jsn.AddVariable("Results", jsnItems);
				OutputResult(jsn);
				jsnItems.Dispose(); jsnItems = null;
				jsn.Dispose(); jsn = null;
			} catch (Exception ex) {
				WriteError(ex);
			} finally {
				lst = null;
			}
			base.GetData();
		}

	}
}
