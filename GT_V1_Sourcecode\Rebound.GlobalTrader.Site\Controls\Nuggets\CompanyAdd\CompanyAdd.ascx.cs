using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site.Controls;
using Rebound.GlobalTrader.Site;

namespace Rebound.GlobalTrader.Site.Controls.Nuggets {
	public partial class CompanyAdd : Base {

		#region Locals

		protected IconButton ibtnAdd;
		protected PlaceHolder plhAddNotAllowed;

		#endregion

		#region Properties

		private bool _blnCanAdd = true;
		public bool CanAdd {
			get { return _blnCanAdd; }
			set { _blnCanAdd = value; }
		}

		#endregion

		#region Overrides

		/// <summary>
		/// Oninit
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
			base.OnInit(e);
			AddScriptReference("Controls.Nuggets.CompanyAdd.CompanyAdd");
			TitleText = Functions.GetGlobalResource("Nuggets", "CompanyAdd");
			ibtnAdd = FindIconButton("ibtnAdd");
			plhAddNotAllowed = (PlaceHolder)FindContentControl("plhAddNotAllowed");
		}

		protected override void OnLoad(EventArgs e) {
			SetupScriptDescriptors();
			base.OnLoad(e);
		}

		protected override void OnPreRender(EventArgs e) {
			plhAddNotAllowed.Visible = !_blnCanAdd;
			ibtnAdd.Visible = _blnCanAdd;
			base.OnPreRender(e);
		}

		#endregion

		/// <summary>
		/// Sets up AJAX script descriptors
		/// </summary>
		private void SetupScriptDescriptors() {
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyAdd", ctlDesignBase.ClientID);
			if (_blnCanAdd) _scScriptControlDescriptor.AddElementProperty("ibtnAdd", FindIconButton("ibtnAdd").ClientID);
		}
	}
}
