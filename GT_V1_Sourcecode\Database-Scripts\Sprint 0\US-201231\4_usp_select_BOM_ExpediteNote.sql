IF OBJECT_ID('usp_select_BOM_ExpediteNote', 'P') IS NOT NULL
DROP PROC usp_select_BOM_ExpediteNote
GO
/*
-- ==========================================================================================
-- TASK      		UPDATED BY     		DATE         ACTION 			DESCRIPTION                                    
-- US-201231 		Phuc.HoangDinh     	22-04-2024   CREATE				Create for ticket US-201231 [RP-2609] BOM Manager Communication Notes Per Line
-- ==========================================================================================
*/
CREATE PROCEDURE [dbo].[usp_select_BOM_ExpediteNote]          
 @BOMId INT=0,    
 @ClientID INT=0    
 AS           
        
 SELECT MIN(BOMExpediteNotesId) AS BOMExpediteNotesId, MIN(ExpediteNotes) AS ExpediteNotes ,MIN(BOMNo) AS BOMNo,MIN(dbo.ufn_convert_BOM_Req(GroupID)) AS ReqNo,        
 GroupID, MIN(UpdatedBy) AS UpdatedBy,MIN(dlup) AS dlup,MIN(MailSendTo) AS MailSendTo, CCUserID,SendToGroup INTO #temp  FROM tbBOMExpediteNotes         
 WHERE BOMNo=@BOMId GROUP BY GroupID,CCUserID,SendToGroup        
        

		--select * from #temp
    
 SELECT        
   Ex.BOMExpediteNotesId        
  ,Ex.ExpediteNotes        
  ,Ex.[UpdatedBy]          
  ,Ex.[DLUP]          
  ,lg.EmployeeName  as EmployeeName        
  ,Ex.ReqNo        
  ,lgg.EmployeeName as AssignTo       
  ,Ex.CCUserID      
  ,Ex.SendToGroup      
      
 FROM #temp Ex          
 JOIN dbo.tbLogin lg ON Ex.UpdatedBy = lg.LoginId         
 LEFT JOIN  dbo.tbLogin lgg ON Ex.MailSendTo = lgg.LoginId         
 LEFT JOIN tbClient c on lg.ClientNo = c.ClientId 
 where isnull(Ex.MailSendTo, 0) <> 0 
	OR isnull(SendToGroup,'Hub Sales') in ( case when @ClientID = 114 then isnull(SendToGroup,'Hub Sales') else  'Hub Sales' end)
 --  WHERE 1 =    
 --(     
 --CASE WHEN    
 --@ClientID =101 and Ex.SendToGroup ='Hub Sales'      
 --THEN 1     
 --WHEN    
 -- @ClientID <>101     
 --THEN 1    
 --ELSE 0 END     
    
 --)        
 ORDER BY Ex.[DLUP] DESC        
 DROP table #temp 
GO
