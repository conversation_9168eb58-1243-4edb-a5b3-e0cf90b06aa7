IF OBJECT_ID('usp_ExcelUpload_Error_PriceQuote', 'P') IS NOT NULL
    DROP PROC dbo.usp_ExcelUpload_Error_PriceQuote
GO
/* 
===========================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-201934]		An.TranTan			06-May-2024		Update			Change the comparison logic between import supplier part and requirement part.
[US-205174]		An.TranTan			17-Jun-2024		Update			Re-format script + update logic:
																	- Specific client id for tbCustomerRequirement
===========================================================================================
*/
CREATE PROCEDURE [dbo].[usp_ExcelUpload_Error_PriceQuote]
    @UserId INT = 0,
    @ClientId INT = 0,
    @SelectedClientId int = 0
/*          
   SELECT * FROM BorisGlobalTraderImports.dbo.tbPriceQuoteImport_tempData      
   
   exec [usp_ExcelUpload_Error_PriceQuote_test] 5959,114,101

   */
WITH RECOMPILE
AS
BEGIN
    SELECT PriceQuoteImportId,
           (case
                when len(Column1) > 30 then
                    dbo.stripAlphahnumeric(SUBSTRING(Column1, 0, 30))
                else
                    dbo.stripAlphahnumeric(Column1)
            end
           ) as REQUIREMENTNo,
           (
               select top 1
                   c.CustomerRequirementNumber
               from dbo.tbCustomerRequirement c
               where c.CustomerRequirementNumber = Column1
                     and c.BOMNo IS NOT NULL
           ) as RequirementSNo,
           (case
                when len(Column6) > 100 then
                    dbo.stripAlphahtestingMfr(SUBSTRING(Column6, 0, 100))
                else
                    dbo.stripAlphahtestingMfr(Column6)
            end
           ) as ManufacturerName,
           (case
                when len(Column3) > 30 then
                    dbo.stripAlphahnumeric(SUBSTRING(Column3, 0, 30))
                else
                    dbo.stripAlphahnumeric(Column3)
            end
           ) as Part,
           FLOOR(dbo.stripNumeric(Column9)) as Quantity,
           (case
                when len(Column2) > 128 then
                    dbo.stripAlphahnumeric(SUBSTRING(Column2, 0, 128))
                else
                    dbo.stripAlphahnumeric(Column2)
            end
           ) as SupplierName,                              
           (case
                when len(Column7) > 30 then
                    dbo.stripAlphahnumeric2(SUBSTRING(Column7, 0, 30))
                else
                    dbo.stripAlphahnumeric2(Column7)
            end
           ) as DateCode,
           cast(dbo.stripNumeric(Column4) as FLOAT) as SupplierCost,
           (case
                when len(Column16) > 3 then
                    dbo.stripAlphahnumeric(SUBSTRING(Column16, 0, 3))
                else
                    dbo.stripAlphahnumeric(Column16)
            end
           ) as CurrencyCode,
                       
           1 as isValid,
           cast('' as varchar(max)) as ValidationMessage,
           [dbo].ufn_get_supplier_part_status(dbo.ufn_get_fullpart(column3), tbcust.FullPart) as PartStatus,
           ROW_NUMBER() OVER (partition by Column1, Column6, Column3, Column2 order by Column1) AS RowID
    into #tbPriceQuoteImport_tempData
    from BorisGlobalTraderImports.dbo.tbPriceQuoteImport_tempData tbtemp
        join dbo.tbCustomerRequirement tbcust
            on tbtemp.Column1 = tbcust.CustomerRequirementNumber
			and tbcust.ClientNo = @SelectedClientId
			and tbcust.BOMNo IS NOT NULL
    where tbtemp.ClientId = @ClientId
          and tbtemp.SelectedClientId = @SelectedClientId
          and tbtemp.CreatedBy = @UserId

    /*********** Updation Validation Query **************/

    --CurrencyCode No length 3  & mandotary--                                                            
    update TmpR
    set TmpR.ValidationMessage = case                                                             
                                     when len(TRl.Column16) < 3 then
                                         ISNULL(ValidationMessage, '') + 'Currency Code  accepts 3 characters.'
                                         + '<br/>'
                                     else
                                         TmpR.ValidationMessage
                                 end
    from #tbPriceQuoteImport_tempData TmpR
        inner Join BorisGlobalTraderImports.dbo.tbPriceQuoteImport_tempData TRl
            on Trl.PriceQuoteImportId = TmpR.PriceQuoteImportId

    --Requirement No length 10  & mandotary--                                                            
    update TmpR
    set TmpR.ValidationMessage = case
                                     when isnull(TRl.Column1, '') = '' then
                                         ISNULL(ValidationMessage, '') + 'Requirement is mandotary.' + '<br/>'
                                     when isnull(TmpR.RequirementSNo, 0) = 0 then
                                         ISNULL(ValidationMessage, '')
                                         + 'Requirement does not Exist or HUBRFQ does not exist for this Requirement'
                                         + '<br/>'                                                           
                                     else
                                         TmpR.ValidationMessage
                                 end
    from #tbPriceQuoteImport_tempData TmpR
        inner Join BorisGlobalTraderImports.dbo.tbPriceQuoteImport_tempData TRl
            on Trl.PriceQuoteImportId = TmpR.PriceQuoteImportId

    --Part length 30  & mandotary--                                                            
    update TmpR
    SET TmpR.ValidationMessage = CASE
           
                                     WHEN len(TRl.Column3) > 30 THEN
                                         ISNULL(ValidationMessage, '') + 'Supplier Part No only accepts 30 characters '
                                         + '<br/>'      
                                     ELSE
                                         TmpR.ValidationMessage
                                 END
    FROM #tbPriceQuoteImport_tempData TmpR
        inner Join BorisGlobalTraderImports.dbo.tbPriceQuoteImport_tempData TRl
            on Trl.PriceQuoteImportId = TmpR.PriceQuoteImportId
    --- DateCode length 5--                                                            
    update TmpR
    set TmpR.ValidationMessage = case
                                     when len(TRl.Column7) > 5
                                          and TmpR.DateCode <> TRl.Column7 then
                                         ISNULL(ValidationMessage, '')
                                         + 'DateCode Field only accepts 5 characters and AlphaNumeric values.'
                                         + '<br/>'                                                             
                                     when len(TRl.Column7) > 5 then
                                         ISNULL(ValidationMessage, '') + 'DateCode Field only accepts 5 characters.'
                                         + '<br/>'
                                     else
                                         TmpR.ValidationMessage
                                 end
    from #tbPriceQuoteImport_tempData TmpR
        inner Join BorisGlobalTraderImports.dbo.tbPriceQuoteImport_tempData TRl
            on Trl.PriceQuoteImportId = TmpR.PriceQuoteImportId

    update #tbPriceQuoteImport_tempData
    set ValidationMessage = isnull(ValidationMessage, '') + ' Requirment part does not match with supplier part.'
    where PartStatus = 'N'

    update #tbPriceQuoteImport_tempData
    set ValidationMessage = isnull(ValidationMessage, '')
                            + ' Duplicate record with same RequirementId, Part, Manufacturer and Supplier.'
    where rowid > 1
 
    /*********** Updation Validation Query  ENDs **************/
    UPDATE #tbPriceQuoteImport_tempData
    SET isvalid = 0
    WHERE ISNULL(ValidationMessage, '') <> ''

    /*********** Select Final Query  **************/
    SELECT ROW_NUMBER() OVER (ORDER BY RL.[PriceQuoteImportId] ASC) AS SNo,
           RL.[column1] AS 'REQUIREMENT',
           RL.[Column2] AS 'SUPPLIER NAME',
           RL.[Column3] AS 'SUPPLIER PART',
           RL.[Column4] AS 'SUPPLIER COST',
           RL.[Column5] AS 'ROHS',
           RL.[column6] AS 'MANUFACTURER',
           RL.[Column7] AS 'DateCode',
           RL.[Column8] AS 'PACKAGE',
           RL.[Column6] AS 'OFFERED QUANTITY',
           RL.[Column10] AS 'OFFER STATUS',
           RL.[Column11] AS 'SPQ',
           RL.[Column12] AS 'FACTORY SEALED',
           RL.[Column12] AS 'QTY IN STOCK',
           RL.[Column14] AS 'MOQ',
           RL.[Column15] AS 'LAST TIME BUY',
           RL.[Column16] AS 'CURRENCY',
           RL.[Column20] AS 'BUY PRICE',
           RL.[Column20] AS 'SELL PRICE',
           RL.[Column20] AS 'SHIPPING COST',
           RL.[Column20] AS 'LEAD TIME',
           RL.[Column21] AS 'REGION',
           RL.[Column22] AS 'Delivery Date',
           RL.[Column23] AS 'NOTES',
           RL.[Column24] AS 'MSL',
           IIF(TL.ValidationMessage = '', '', '<b>' + TL.ValidationMessage + '<b/>') as 'Reason for Excel Upload failed',
           RL.OriginalFilename
    from #tbPriceQuoteImport_tempData TL
        inner join BorisGlobalTraderImports.dbo.tbPriceQuoteImport_tempData RL
            on RL.PriceQuoteImportId = TL.PriceQuoteImportId
    where isValid <> 1
END
GO