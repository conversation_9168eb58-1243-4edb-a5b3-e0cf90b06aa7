﻿
GO
/* 
==========================================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-216601]	    Phuc Hoang			12-Dec-2024		Update			216601: Quote - New status matrix
==========================================================================================================
*/

IF NOT EXISTS (SELECT 1 FROM [dbo].[tbQuoteStatus] WHERE [Name] = 'New')
BEGIN
	INSERT INTO [dbo].[tbQuoteStatus](
		[QuoteStatusId]
		,[Name]
        ,[Inactive]
        ,[SortOrder])
     VALUES
        (5
        ,'New'
        ,0
        ,5) 
END

GO

IF NOT EXISTS (SELECT 1 FROM [dbo].[tbQuoteStatus] WHERE [Name] = 'Partially Offered')
BEGIN
	INSERT INTO [dbo].[tbQuoteStatus](
		[QuoteStatusId]
		,[Name]
        ,[Inactive]
        ,[SortOrder])
     VALUES
        (6
        ,'Partially Offered'
        ,0
        ,6) 
END

GO

IF NOT EXISTS (SELECT 1 FROM [dbo].[tbQuoteStatus] WHERE [Name] = 'Partially Accepted')
BEGIN
	INSERT INTO [dbo].[tbQuoteStatus](
		[QuoteStatusId]
		,[Name]
        ,[Inactive]
        ,[SortOrder])
     VALUES
        (7
        ,'Partially Accepted'
        ,0
        ,7) 
END

GO

IF NOT EXISTS (SELECT 1 FROM [dbo].[tbQuoteStatus] WHERE [Name] = 'Partially Declined')
BEGIN
	INSERT INTO [dbo].[tbQuoteStatus](
		[QuoteStatusId]
		,[Name]
        ,[Inactive]
        ,[SortOrder])
     VALUES
        (8
        ,'Partially Declined'
        ,0
        ,8) 
END

GO