///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Nuggets");

Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyAdd = function(element) { 
	Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyAdd.initializeBase(this, [element]);
	this._intLoginID = 0;
};

Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyAdd.prototype = {

	get_ibtnAdd: function() { return this._ibtnAdd; }, 	set_ibtnAdd: function(v) { if (this._ibtnAdd !== v)  this._ibtnAdd = v; }, 

	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyAdd.callBaseMethod(this, "initialize");	
		this.showRefresh(false);
		this.showLoading(false);
		this.showContentLoading(false);
		this.addRefreshEvent(Function.createDelegate(this, this.showAddForm));
		if (this._ibtnAdd) {
			$R_IBTN.addClick(this._ibtnAdd, Function.createDelegate(this, this.showAddForm));
			this._frmAdd = $find(this._aryFormIDs[0]);
			this._frmAdd.addSaveComplete(Function.createDelegate(this, this.saveAddComplete));
			this._frmAdd.addSaveError(Function.createDelegate(this, this.saveAddError));
		}
	},

	dispose: function() { 
		if (this.isDisposed) return;
		if (this._ibtnAdd) $R_IBTN.clearHandlers(this._ibtnAdd);
		if (this._frmAdd) this._frmAdd.dispose();
		this._ibtnAdd = null;
		this._frmAdd = null;
		Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyAdd.callBaseMethod(this, "dispose");
	},
	
	showAddForm: function() {
		if (this._ibtnAdd) {
			this._frmAdd.setFieldValue("ctlSalesman", this._intLoginID);
			this.showForm(this._frmAdd, true);
		} else {
			this.showContent(true);
		}
	},
	
	saveAddComplete: function() {
		location.href = $RGT_gotoURL_Company(this._frmAdd._intNewID);
	},
	
	saveAddError: function() {
		this.showError(true, this._frmAdd._strErrorMessage);
		$R_FN.showElement(this._pnlLinks, true);
	}
		
};

Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyAdd.registerClass("Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyAdd", Rebound.GlobalTrader.Site.Controls.Nuggets.Base);
