﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO
/* 
===========================================================================================
TASK      		UPDATED BY		DATE         	ACTION 		DESCRIPTION
[US-215481]		An.TranTan		01-Nov-2024		Update		Insert IsXML column
===========================================================================================
*/
CREATE OR ALTER PROCEDURE [dbo].[usp_insert_CreditEmail]               
 @CreditNos XML ,
 @IsXML BIT = 0,
 @UpdatedBy INT ,
 @IsFromExport BIT=0   
              
AS                
-- =============================================                
-- Author:  <Abhinav Saxena>                
-- Create date: <05 March 2024>                
-- Description: <Rebound- Credit Note bulk Emailer>                
-- =============================================                
BEGIN                
      SET ANSI_PADDING ON            
      SET ANSI_WARNINGS ON            
      SELECT x.CreditID.query('ID').value('.', 'int') as CreditID INTO #TEMP_Credit FROM @CreditNos.nodes('Credits/Credit') as x(CreditId)              
      SET ANSI_PADDING OFF            
      SET ANSI_WARNINGS OFF            
   --Insert into tbCreditEmail table            
      INSERT INTO tbCreditEmail(              
  ClientNo,              
  CreditNoteNo,                            
  ContactEmail,              
  SentStatus,              
  UpdatedBy,              
  EmailStatus,              
  DLUP ,    
  InACtive,
  IsFromExport,
  IsXML
      )                
      SELECT cr.ClientNo,              
      cr.CreditId,                            
      Con.EMail,0,              
      @UpdatedBy,'Pending',GETDATE(),0,@IsFromExport, @IsXML          
      FROM tbContact Con LEFT OUTER JOIN tbCredit cr  ON Con.CompanyNo=cr.CompanyNo         
      join #TEMP_Credit tmp on cr.CreditId=tmp.CreditID 
	  WHERE Con.FinanceContact=1 and con.Inactive=0               
           
          
SELECT * FROM (          
               Select           
                    cr.CreditNumber           
                   ,IsNull((Select COUNT(*) from tbContact           
                    where IsNull(FinanceContact,0)=1 and Inactive=0 and CompanyNo= cr.CompanyNo           
                    Group By CompanyNo),0) As 'CountFinanceContact'          
               from tbCredit cr          
               Inner Join #TEMP_Credit tmp on cr.CreditId=tmp.CreditID           
             ) as table1          
where CountFinanceContact=0          
          
        -- Drop the temp table            
      DROP TABLE #TEMP_Credit           
END             
GO


