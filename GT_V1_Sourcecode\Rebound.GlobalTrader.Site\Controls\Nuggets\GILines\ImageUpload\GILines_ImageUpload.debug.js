///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.GILines_ImageUpload = function (element) {
    Rebound.GlobalTrader.Site.Controls.Forms.GILines_ImageUpload.initializeBase(this, [element]);
    this._intLineID = "";
    //this._intLineID = -1;
    //[001] start
    this._files = [];
    this._dragobj = null;
    //[001] end
};

Rebound.GlobalTrader.Site.Controls.Forms.GILines_ImageUpload.prototype = {

    get_intLineID: function () { return this._intLineID; }, set_intLineID: function (v) { if (this._intLineID !== v) this._intLineID = v; },
    initialize: function () {

        Rebound.GlobalTrader.Site.Controls.Forms.GILines_ImageUpload.callBaseMethod(this, "initialize");
        this.addShown(Function.createDelegate(this, this.formShown));
    },
    formShown: function () {
        if (this._blnFirstTimeShown) {
            this.addSave(Function.createDelegate(this, this.saveClicked));
        }
    },

    dispose: function () {
        if (this.isDisposed) return;
        this._intLineID = null;
        Rebound.GlobalTrader.Site.Controls.Forms.GILines_ImageUpload.callBaseMethod(this, "dispose");
    },
    saveClicked: function () {
        if (!this.validateForm()) return;
        if (this._dragobj != undefined) {
            if (this._dragobj) this._dragobj.startUpload();
        }
        else
            this.saveGILineImage('', '');
    },
    saveGILineImage: function (originalFilename, generatedFilename) {
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/GILines");
        obj.set_DataObject("GILines");
        obj.set_DataAction("SaveGILineImage");
        obj.addParameter("ID", this._intLineID);
        obj.addParameter("Caption", this.getFieldValue("ctlCaption"));
        //obj.addParameter("OriginalFilename", originalFilename);
        obj.addParameter("TempFile", generatedFilename);
        obj.addDataOK(Function.createDelegate(this, this.saveAddComplete));
        obj.addError(Function.createDelegate(this, this.saveAddError));
        obj.addTimeout(Function.createDelegate(this, this.saveAddError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;

    },
    hideForm: function () {
        $(".ajax-file-upload-red").each(function (i, items) {
            $(this).click();
        });
    }
    ,
    saveAddComplete: function (args) {
        this.hideForm();
        if (args._result.Result > 0) {
            this.setFieldValue("ctlCaption", "");
            this.onSaveComplete();
        } else {
            if (args._result.Message) this._strErrorMessage = args._result.Message;
            this.onSaveError();
        }
    },
    saveAddError: function (args) {
        this._strErrorMessage = args._errorMessage;
        this.onSaveError();
    },
    validateForm: function () {
        this.onValidate();
        var blnOK = this.autoValidateFields();
        return blnOK;
    }

};

Rebound.GlobalTrader.Site.Controls.Forms.GILines_ImageUpload.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.GILines_ImageUpload", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
