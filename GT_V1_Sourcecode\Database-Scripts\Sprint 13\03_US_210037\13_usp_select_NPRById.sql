﻿
GO

/****** Object:  StoredProcedure [dbo].[usp_select_NPRById]    Script Date: 10/25/2024 2:32:42 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

/* 
===========================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-210037]		Phuc Hoang			25-Oct-2024		UPDATE			Update Advisory notes
===========================================================================================
*/


CREATE OR ALTER PROCEDURE [dbo].[usp_select_NPRById]                    
(                                
@NPRId int   ,      
@ClientNo int = null                             
)                                
AS                                
BEGIN                                
                                
SELECT                                
  npr.NPRId                                
 ,npr.NPRNo                                
 ,npr.RaisedBy                                
 ,npr.NPRdate                                
 ,npr.QLocation                                
 ,npr.GoodsInLineNo                                
 ,npr.GoodsInNo                                
 ,npr.SalesOrderNo                                
 ,npr.TotalRejectedValue                                
 ,npr.RejectionReason                                
 ,npr.SupplierRMANo                                
 ,npr.CorrectiveActionReport                                
 ,npr.SupplierShipVia                                
 ,npr.SupplierShipAccount                                
 ,npr.SupplierToCredit                                
 ,npr.SupplierRef                                
 ,npr.IncurredCostToSales_Scrap                                
 ,npr.Comments                                
 ,npr.StockLocation                                
 ,npr.IncurredCostToSales_Stock                                
 ,npr.OutworkerName                                
 ,npr.OutworkerPONo                                
 ,npr.SalesPersonNo                                
 ,npr.SalesAuthoriseDate                                
 ,npr.LogisticSRMANo                                
 ,npr.LogisticSRMADate                                
 ,npr.DebitNoteNo                                
 ,npr.DebitNoteDate                                
 ,npr.NPRCompletedByNo                                
 ,npr.NPRCompleteddATE                                
 ,npr.UpdatedBy                                
 ,npr.DLUP                               
 ,lo.EmployeeName   As ReceiverName                              
 ,npr.RejectedQty                            
 ,npr.CustomerNo                            
 ,npr.AdviceNote                          
 ,co.CompanyName                            
 ,npr.Part as PartNo                         
 ,cus.CompanyName   As CustomerName                         
 ,locompletedBy.EmployeeName   As CompletedByName                         
 ,ship.ShipViaName                        
 ,lg.EmployeeName        As BuyerName                          
 ,lg.LoginId as BuyerId                        
 ,gi.GoodsInNumber                  
 ,npr.UnitCost                
 ,npr.SalesAuthoriseBy                
 ,npr.SalesAuthoriseName                
 ,npr.IsAuthorise                 
 ,salesperson.EmployeeName as SalesPerson                
 ,npr.PurchaseOrderNumber          
 ,npr.QtyAdvised          
 ,npr.CurrencyCode      
 ,EightdCat.Prefix+EightdSubCat.Code as Reason1 
 ,EightdCat1.Prefix+EightdSubCat1.Code as Reason2
 ,npr.NPRComments  
 ,npr.SalesAction
 ,npr.Reason1 as Reason1Val
 ,npr.Reason2 as Reason2Val   
 , gi.ClientNo 
 , c.ClientName
 ,co.CompanyId AS NPRCompanyNo
FROM tbnpr npr                                
LEFT JOIN tbLogin lo on npr.RaisedBy=lo.LoginId                           
LEFT JOIN dbo.tbGoodsIn gi ON npr.GoodsInNo=gi.GoodsInId                        
LEFT JOIN dbo.tbPurchaseOrder po ON gi.PurchaseOrderNo = po.PurchaseOrderId                                       
LEFT JOIN dbo.tbLogin lg on po.Buyer = lg.LoginId                           
LEFT JOIN dbo.tbCompany co  ON npr.CompanyNo  = co.CompanyId                            
LEFT JOIN tbCompany cus on npr.CustomerNo=cus.CompanyId                          
LEFT JOIN tbLogin locompletedBy on npr.NPRCompletedByNo=locompletedBy.LoginId                          
LEFT JOIN tbLogin salesperson on npr.SalesPersonNo=salesperson.LoginId                  
LEFT JOIN  tbshipvia ship on npr.SupplierShipVia=ship.ShipViaId   
LEFT JOIN tbAnalysis8DSubCategory EightdSubCat on npr.Reason1=EightdSubCat.Analysis8DSubCategoryID
LEFT JOIN tbAnalysis8DSubCategory EightdSubCat1 on npr.Reason2=EightdSubCat1.Analysis8DSubCategoryID
Left join tbAnalysis8DCategory EightdCat on EightdSubCat.AnalysisNo=EightdCat.Analysis8DCategoryID
Left join tbAnalysis8DCategory EightdCat1 on EightdSubCat1.AnalysisNo=EightdCat1.Analysis8DCategoryID
left join tbClient c on gi.ClientNo = c.ClientId
--WHERE gi.ClientNo=@ClientNo and npr.NPRId=@NPRId and  ISNULL(npr.Inactive,0)=0                          
WHERE npr.NPRId=@NPRId and  ISNULL(npr.Inactive,0)=0                          
END


GO


