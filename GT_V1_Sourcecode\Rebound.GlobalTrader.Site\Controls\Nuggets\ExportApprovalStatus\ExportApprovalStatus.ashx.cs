﻿//Marker     Changed by         Date         Remarks
//[001]      Ab<PERSON><PERSON>     1/09/2021    Added hanlder for supplier approval.
//[002]      Ab<PERSON>av <PERSON>     22/09/2021   Added type no.
//[003]      Ab<PERSON>av <PERSON>     26-Oct-2021  Add columns for QualityApprovalDate & LineManagerApprovalDate.
//[004]      Abhinav <PERSON>     20-Jan-2022  Add new column IsEscalate.
//[005]      Ab<PERSON>av <PERSON>     27-Jan-2022  Add Line Manager Snapshot.
//[006]      Abhinav <PERSON>a     15-Mar-2022  Add permissions for approval.
using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;
using Rebound.GlobalTrader.Site.Code.Enumerations;
using System.Threading.Tasks;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Text.RegularExpressions;
using System.Linq;

namespace Rebound.GlobalTrader.Site.Controls.Nuggets.Data
{
    [WebService(Namespace = "http://tempuri.org/")]
    [WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    /// <summary>
    /// Summary description for POApprovals
    /// </summary>
    public class ExportApprovalStatus : Rebound.GlobalTrader.Site.Data.Base
    {

        public override void ProcessRequest(HttpContext context)
        {
            if (base.init(context))
            {
                switch (Action)
                {
                    case "GetData_All": GetData_All(); break;
                    case "GetData_Approved": GetData_Approved(); break;
                    case "GetData_Awaiting": GetData_Awaiting(); break;
                    case "GetExportApprovalData": GetExportApprovalData(); break;
                    case "ApproveExportRequest": ApproveExportRequest(); break;
                    case "RejectExportRequest": RejectExportRequest(); break;
                    case "SendExportApprovalRequest": SendExportApprovalRequest(); break;
                    case "GetEditApprovalData": GetEditApprovalData(); break;
                    case "SaveExportApprovalDetails": SaveExportApprovalDetails(); break;
                    case "SaveAllExportApprovalDetails": SaveAllExportApprovalDetails(); break;
                    case "ShowSONotifyerForExportApproval": ShowSONotifyerForExportApproval(); break;
                    default: WriteErrorActionNotFound(); break;
                }
            }
        }

        /// <summary>
        /// Get Data for the approval status tab.
        /// </summary>
        private void GetData_All()
        {
            JsonObject jsn = new JsonObject();
            jsn = ProcessSupplierApprovalList(SalesOrderLine.GetData_All(ID, SessionManager.ClientID, SessionManager.LoginID));
            OutputResult(jsn);
        }

        /// <summary>
        /// Get Data for the trade reference tab.
        /// /// </summary>
        private void GetData_Approved()
        {
            JsonObject jsn = new JsonObject();
            jsn = ProcessSupplierApprovalList(SalesOrderLine.GetData_Approved(ID,SessionManager.ClientID,SessionManager.LoginID));
            OutputResult(jsn);
        }

        /// <summary>
        /// Get Data for the supplier approval history tab.
        /// </summary>
        private void GetData_Awaiting()
        {
            JsonObject jsn = new JsonObject();
            jsn = ProcessSupplierApprovalList(SalesOrderLine.GetData_Awaiting(ID, SessionManager.ClientID, SessionManager.LoginID));
            OutputResult(jsn);
        }



        private JsonObject ProcessSupplierApprovalList(List<SalesOrderLine> lst)
        {
            var listMfrNotes = Manufacturer.GetAdvisoryNotes(lst.Select(x => x.ManufacturerNo).ToList(), (int)SessionManager.ClientID);

            JsonObject jsn = new JsonObject();
            JsonObject jsnItems = new JsonObject(true);
            jsn.AddVariable("Count", lst.Count);
            foreach (SalesOrderLine ln in lst)
            {
                string manufacturerCode = ln.RestrictedMfrNo > 0 && !Convert.ToBoolean(ln.RestrictedMfrInactive) 
                    ? string.Format(@"<span style=""color: red !important;"">{0}</span>", ln.ManufacturerCode) : ln.ManufacturerCode;
                string mfrNotes = !Functions.HasNumbericValue(ln.ManufacturerNo) ? "" : listMfrNotes.Find(x => x.ManufacturerId == (int)ln.ManufacturerNo).AdvisoryNotes;

                JsonObject jsnItem = new JsonObject();
                jsnItem.AddVariable("ExportApprovalId", ln.ExportApprovalId);
                jsnItem.AddVariable("LineID", ln.SalesOrderLineId);
                jsnItem.AddVariable("LineNo", ln.SOSerialNo);
                jsnItem.AddVariable("ROHS", ln.ROHS);
                jsnItem.AddVariable("CustomerPart", ln.CustomerPart);
                jsnItem.AddVariable("Part", ln.Part);
                jsnItem.AddVariable("Mfr", manufacturerCode);
                jsnItem.AddVariable("MfrNo", ln.ManufacturerNo);
                jsnItem.AddVariable("MfrAdvisoryNotes", Functions.ReplaceLineBreaks(mfrNotes));
                jsnItem.AddVariable("DC", ln.DateCode);
                jsnItem.AddVariable("ExportApprovalStatusId", ln.ExportApprovalStatusId);
                jsnItem.AddVariable("ExportApprovalStatus", ln.ExportApprovalStatus);
                jsnItem.AddVariable("OGELLicenseRequired", ln.OGELLicenseRequired);
                jsnItem.AddVariable("EUUFormRequired", ln.EUUFormRequired);
                jsnItem.AddVariable("Dated", Functions.FormatDate(ln.Dated));
                jsnItem.AddVariable("By", ln.By);
                jsnItem.AddVariable("Comment", Functions.ReplaceLineBreaks(ln.Comment));
                jsnItem.AddVariable("IsAllocationDone", ln.IsAllocationDone);
                jsnItem.AddVariable("IsExportDetailsFilled", ln.IsExportDetailsFilled);
                jsnItem.AddVariable("OgelRequiredOnSO", ln.OgelRequiredOnSO);
                jsnItem.AddVariable("IsExportControlHasPDF", ln.IsExportControlHasPDF);

                jsnItems.AddVariable(jsnItem);
                jsnItem.Dispose();
                jsnItem = null;
            }
            jsn.AddVariable("Lines", jsnItems);
            jsnItems.Dispose(); jsnItems = null;
            return jsn;
        }


        /// <summary>
        /// get a Export Approval data by Id
        /// </summary>
        private void GetExportApprovalData()
        {
            SalesOrderLine tr = SalesOrderLine.GetExportApprovalData(ID);
            if (tr == null)
            {
                WriteErrorDataNotFound();
            }
            else
            {
                bool? fromRecieve = false;
                fromRecieve = GetFormValue_NullableBoolean("FromRecieve", false);
                JsonObject jsn = new JsonObject();
                jsn.AddVariable("ExportApprovalId", tr.ExportApprovalId);
                jsn.AddVariable("SalesmanName", tr.SalesmanName);
                jsn.AddVariable("SalesOrderNo", tr.SalesOrderNumber);
                jsn.AddVariable("SOSerialNo", tr.SOSerialNo);
                jsn.AddVariable("CustomerName", tr.CustomerName);
                jsn.AddVariable("Part", tr.Part);
                jsn.AddVariable("DestinationCountry", tr.DestinationCountry);
                jsn.AddVariable("ISPdfAttached", tr.ISPdfAttached);
                jsn.AddVariable("MilitaryUseName", tr.MilitaryUseName);
                jsn.AddVariable("EndUser", Functions.ReplaceLineBreaks(tr.EndUserText));
                jsn.AddVariable("ECCN", tr.ECCN);
                jsn.AddVariable("PartApplication", Functions.ReplaceLineBreaks(tr.PartApplication));
                jsn.AddVariable("ExportControl", tr.ExportControl);
                jsn.AddVariable("AerospaceUse", tr.AerospaceUse);
                jsn.AddVariable("PartTested", Functions.ReplaceLineBreaks(tr.PartTested));
                jsn.AddVariable("CommodityCode", tr.CommodityCode);
                jsn.AddVariable("ClientNo", tr.ClientNo);
                jsn.AddVariable("ShipToCustomerCountry", tr.ShipToCustomerCountry);
                jsn.AddVariable("ShipToCustomerName", tr.ShipToCustomerName);
                jsn.AddVariable("ShipFromWarehouse", tr.ShipFromWarehouse);
                jsn.AddVariable("ShipFromCountry", tr.ShipFromCountry);
                jsn.AddVariable("CountryOfOrigin", tr.CountryOfOrigin);

                //---For notify Feature---//
                jsn.AddVariable("SubJect", String.Format(Functions.GetGlobalResource("Messages", "SendExportApproval"),tr.SalesOrderNumber));
                OutputResult(jsn);
                jsn.Dispose();
                jsn = null;
            }
            tr = null;
        }


        /// <summary>
        /// Export Data Approvals 
        /// </summary>
        public void ApproveExportRequest()
        {
            string OGELNumber = GetFormValue_String("OGELNumber");
            int? MilitaryUse = GetFormValue_NullableInt("MilitaryUse");
            string EndUser = GetFormValue_String("EndUser");
            string Comment = GetFormValue_String("Comment");
            bool? SendEmail = GetFormValue_NullableBoolean("SendEmail");
            try
            {
                JsonObject jsn = new JsonObject();
                bool blnResult = false;
                string strMessage = string.Empty;
                string LineManagerApprovalStatus = string.Empty;
                string strToLoginsArray = string.Empty;
                List<SalesOrderLine> lst = SalesOrderLine.ApproveExportRequest(ID, OGELNumber, MilitaryUse, EndUser,Convert.ToString(Comment),SessionManager.LoginID);

                foreach (SalesOrderLine ln in lst)
                {
                    blnResult =(bool) ln.Result;

                    if (blnResult)
                    {
                        WebServices servic = new WebServices();
                        if (SendEmail == true)
                        {
                            if (ln.Salesman > 0)
                            {
                                strToLoginsArray = string.Empty;
                                strToLoginsArray = ln.Salesman.ToString();
                                strMessage = string.Empty;
                                strMessage = "Your Export Approval request has been approved with below status: <br/><br/> <b>Approval Status:</b>" + ln.ExportApprovalStatus + "<br/><br/><b>Approval Comment:</b>"+ Convert.ToString(Comment)+"<br/>";
                                servic.NotifySalesPersonForExportApprovalData(strToLoginsArray, "", Functions.GetGlobalResource("Messages", "OGELApprovalStatus") + " (SO:" + ln.SalesOrderNumber.ToString() + ")", 0, ln.SalesmanName.ToString(), ln.SalesOrderNo.ToString(), ln.SalesOrderNumber.ToString(), strMessage,SessionManager.LoginID, SessionManager.LoginEmail, ln.SOSerialNo);
                               
                            }
                        }
                    }
                            
                }
                jsn.AddVariable("Result", blnResult);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }

        /// <summary>
        /// Export Data Rejection
        /// </summary>
        public void RejectExportRequest()
        {
            string OGELNumber = GetFormValue_String("OGELNumber");
            int? MilitaryUse = GetFormValue_NullableInt("MilitaryUse");
            string EndUser = GetFormValue_String("EndUser");
            string Comment = GetFormValue_String("Comment");
            bool? SendEmail = GetFormValue_NullableBoolean("SendEmail");
            try
            {
                JsonObject jsn = new JsonObject();
                bool blnResult = false;
                string strMessage = string.Empty;
                string LineManagerApprovalStatus = string.Empty;
                string strToLoginsArray = string.Empty;
                List<SalesOrderLine> lst = SalesOrderLine.RejectExportRequest(ID, OGELNumber, MilitaryUse, EndUser, Comment, SessionManager.LoginID);

                foreach (SalesOrderLine ln in lst)
                {
                    blnResult = (bool)ln.Result;

                    if (blnResult)
                    {
                        WebServices servic = new WebServices();
                        if (SendEmail == true)
                        {
                            if (ln.Salesman > 0)
                            {
                                strToLoginsArray = string.Empty;
                                strToLoginsArray = ln.Salesman.ToString();
                                strMessage = string.Empty;
                                strMessage = "Your Export Approval request has been <b>Rejected</b>. <br/><br/> <b>Rejection Comment:</b>" + Convert.ToString(Comment) + "<br/>";
                                servic.NotifySalesPersonForExportApprovalData(strToLoginsArray, "", Functions.GetGlobalResource("Messages", "OGELApprovalStatus") + " (SO:" + ln.SalesOrderNumber.ToString() + ")", 0, ln.SalesmanName.ToString(), ln.SalesOrderNo.ToString(), ln.SalesOrderNumber.ToString(), strMessage,SessionManager.LoginID, SessionManager.LoginEmail, ln.SOSerialNo);

                            }
                        }
                    }

                }
                jsn.AddVariable("Result", blnResult);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }
        /// <summary>
        /// Get Edit Approval Screen Data.
        /// </summary>
        public void GetEditApprovalData()
        {
            SalesOrderLine tr = SalesOrderLine.GetEditApprovalData(ID);
            if (tr == null)
            {
                WriteErrorDataNotFound();
            }
            else
            {
                bool? fromRecieve = false;
                fromRecieve = GetFormValue_NullableBoolean("FromRecieve", false);
                JsonObject jsn = new JsonObject();
                jsn.AddVariable("ExportApprovalId", tr.ExportApprovalId);
                jsn.AddVariable("SalesmanName", tr.SalesmanName);
                jsn.AddVariable("SalesOrderNo", tr.SalesOrderNumber);
                jsn.AddVariable("SOSerialNo", tr.SOSerialNo);
                jsn.AddVariable("CustomerName", tr.CustomerName);
                jsn.AddVariable("Part", tr.Part);
                jsn.AddVariable("DestinationCountryId", tr.DestinationCountryId);
                jsn.AddVariable("MilitaryUseId", tr.MilitaryUseId);
                jsn.AddVariable("EndUserText", Functions.ReplaceLineBreaks(tr.EndUserText));
                jsn.AddVariable("ExportApprovalStatusId", tr.ExportApprovalStatusId);
                jsn.AddVariable("ECCN", tr.ECCN);
                jsn.AddVariable("PartApplication", Functions.ReplaceLineBreaks(tr.PartApplication));
                jsn.AddVariable("ExportControl", tr.ExportControl);
                jsn.AddVariable("AerospaceUse", tr.AerospaceUse);
                jsn.AddVariable("PartTested", Functions.ReplaceLineBreaks(tr.PartTested));
                jsn.AddVariable("CommodityCode", tr.CommodityCode);
                jsn.AddVariable("ClientNo", tr.ClientNo);
                jsn.AddVariable("ShipToCustomerCountry", tr.ShipToCustomerCountry);
                jsn.AddVariable("ShipToCustomerName", tr.ShipToCustomerName);
                jsn.AddVariable("ShipFromWarehouse", tr.ShipFromWarehouse);
                jsn.AddVariable("ShipFromCountry", tr.ShipFromCountry);

                OutputResult(jsn);
                jsn.Dispose();
                jsn = null;
            }
            tr = null;
        }

        /// <summary>
        /// Update Export Approval details info.
        /// </summary>
        public void SaveExportApprovalDetails()
        {
            int? DestinationCountryId = GetFormValue_NullableInt("DestinationCountryNo");
            int? MilitaryUseid = GetFormValue_NullableInt("MilitaryuseNo");
            string EndUserText = GetFormValue_String("EndUserText");
            string partApplication = GetFormValue_String("PartApplication");
            int? exportControl = GetFormValue_NullableInt("ExportControl");
            int? aerospaceUse = GetFormValue_NullableInt("AerospaceUse");
            string partTested = GetFormValue_String("PartTested");
            try
            {
                JsonObject jsn = new JsonObject();
                bool? blnResult = false;
                List<SalesOrderLine> lst = SalesOrderLine.SaveExportApprovalDetails(ID, DestinationCountryId, MilitaryUseid,Convert.ToString(EndUserText), SessionManager.LoginID, 
                    partApplication, exportControl, aerospaceUse, partTested);

                blnResult = lst[0].Result;
                jsn.AddVariable("Result", blnResult);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }

        public void SaveAllExportApprovalDetails()
        {
            string ExportApprovalIds = GetFormValue_String("ExportApprovalIds");
            int? DestinationCountryId = GetFormValue_NullableInt("DestinationCountryNo");
            int? MilitaryUseid = GetFormValue_NullableInt("MilitaryuseNo");
            string EndUserText = GetFormValue_String("EndUserText");
            try
            {
                JsonObject jsn = new JsonObject();
                bool? blnResult = false;
                List<SalesOrderLine> lst = SalesOrderLine.SaveAllExportApprovalDetails(ExportApprovalIds, DestinationCountryId, MilitaryUseid, Convert.ToString(EndUserText), SessionManager.LoginID);

                blnResult = lst[0].Result;
                jsn.AddVariable("Result", blnResult);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }
        public void ShowSONotifyerForExportApproval()
        {
            SalesOrder so = SalesOrder.ShowSONotifyerForExportApproval(ID, SessionManager.LoginID);
            if (so == null)
            {
                WriteErrorDataNotFound();
            }
            else
            {
                JsonObject jsn = null;
                if (so != null)
                {
                    jsn = new JsonObject();
                    jsn.AddVariable("SoNotifyersName", so.SoNotifyersName);
                    jsn.AddVariable("IsAllowCheckSoNotify", so.IsAllowCheckSoNotify);
                }
                OutputResult(jsn);
            }
            so = null;
        }
        #region Sales Approval Request by Power App
        public void SendExportApprovalRequest()
        {
            string Subject = string.Empty;
            string Messgaeody = string.Empty;
            string ApproverIDs = string.Empty;
            string strEmailTo = string.Empty;
            System.Boolean IsNotifySO = false;
            List<int> lstToLoginID = new List<int>();
            Subject = GetFormValue_String("Subject");
            Messgaeody = GetFormValue_String("Message");
            ApproverIDs = GetFormValue_String("ApproverIds");
            IsNotifySO = GetFormValue_Boolean("IsNotifySales");
            Array aryToLogins = Functions.JavascriptStringToArray(ApproverIDs);
            //for (int i = 0; i < aryToLogins.Length; i++)
            //{
            //    int intLoginID = Convert.ToInt32(aryToLogins.GetValue(i));
            //    if (!lstToLoginID.Contains(intLoginID)) lstToLoginID.Add(intLoginID);
            //}

            List<SalesOrderLine> lstEmail = SalesOrderLine.GetLoginIds(SessionManager.ClientID);
            for (int i = 0; i < lstEmail.Count; i++)
                {
                    //LoginPreference objLoginPref = LoginPreference.GetByLoginForSendEmail(lstToLoginID[i]);
                    strEmailTo += lstEmail[i].Email + ";";
                }
            SalesOrderLine sor = SalesOrderLine.GetExportApprovalData(ID);   
            PowerAppToken JWTToken = PowerAppToken.GetTokenForPowerApp(SessionManager.LoginID, "Export Approval", IsNotifySO);

            if(lstEmail.Count>0)
            {
                SendSOApprovalToTeam(sor.SalesOrderNo,ID, RemoveHyperLink(Messgaeody), Subject, strEmailTo, "Bhooma Nand", sor.PowerExportUrl, Convert.ToInt32(SessionManager.LoginID), 
                    sor.EUUUPDFploadName, sor.EUUUPDFploadName, JWTToken.RequestId, JWTToken.TokenValue, IsNotifySO,sor.EUUFornName, sor.SalesOrderNumber, sor);
            }

        }
        public void SendSOApprovalToTeam(int? SalesOrderId,int? ApprovalId, string Message, string Subject, string toAddress, string username, string uri, 
            int UserId, string SOReportFileName, string POReportFileName, int RequestId, string TokenValue, System.Boolean? IsNotifySO,System.String EUUFornName, 
            int saleOrderNumber, SalesOrderLine sor)
        {
            try
            {
                string PORepoert = "";
                string SOReport = "";
                string SOReportAttachment = "";
                string euuFormUrl = "";
                Message = Message.Replace(saleOrderNumber.ToString(), "[" + saleOrderNumber.ToString() + "](" + string.Format(Convert.ToString(System.Configuration.ConfigurationManager.AppSettings["redirecturi"])) + "/Ord_SODetail.aspx?so=" + SalesOrderId + ")");

                if (EUUFornName!= "File Not Uploaded")
                {
                    SOReport = "[" + EUUFornName + "](" + string.Format(Convert.ToString(System.Configuration.ConfigurationManager.AppSettings["redirecturi"])) + "/PowerApp.ashx?RequestType=EUUForm&EUUFileName=" + SOReportFileName + ")";
                    euuFormUrl = string.Format(Convert.ToString(System.Configuration.ConfigurationManager.AppSettings["redirecturi"])) + "/PowerApp.ashx?RequestType=EUUForm&EUUFileName=" + SOReportFileName;
                }
                else
                {
                    SOReport = EUUFornName;
                }
                string soDetailsUrl = string.Format(Convert.ToString(System.Configuration.ConfigurationManager.AppSettings["redirecturi"])) + "/Ord_SODetail.aspx?so=" + SalesOrderId;
                
                HttpClient client = new HttpClient();
                client.BaseAddress = new Uri(uri);
                client.DefaultRequestHeaders.Accept.Clear();
                client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));

                HttpRequestMessage request = new HttpRequestMessage(HttpMethod.Post, client.BaseAddress);
                //var body = $"{{\"Email\": \"{toAddress}\",\"UserId\":\"{UserId}\",\"Subject\":\"{Subject}\",\"ExportApprovalId\":\"{ApprovalId}\",\"SOReportUrl\":\"{SOReport}\",\"POReportUrl\":\"{PORepoert}\",\"SOReportAttachment\":\"{SOReportAttachment}\",\"EUUFornName\":\"{EUUFornName}\",\"RequestId\":\"{RequestId}\",\"TokenValue\":\"{TokenValue}\",\"Message\":\"{Message}\",\"SODetailURL\":\"{soDetailsUrl}\"}}";
                var body = $"{{\"Email\": \"{toAddress}\",\"UserId\":\"{UserId}\",\"Subject\":\"{Subject}\",\"ExportApprovalId\":\"{ApprovalId}\",\"SOReportUrl\":\"{SOReport}\"," +
                    $"\"POReportUrl\":\"{PORepoert}\",\"SOReportAttachment\":\"{SOReportAttachment}\",\"EUUFornName\":\"{EUUFornName}\",\"RequestId\":\"{RequestId}\"," +
                    $"\"TokenValue\":\"{TokenValue}\",\"SalesPerson\":\"{sor.SalesmanName}\",\"SalesOrderNumber\":\"{saleOrderNumber}\",\"SOLineNumber\":\"{sor.SOSerialNo}\",\"Customer\":\"{sor.CustomerName}\"," +
                    $"\"PartNumber\":\"{sor.Part}\",\"ShipFromWarehouse\":\"{sor.ShipFromWarehouse}\",\"ShipFromCountry\":\"{sor.ShipFromCountry}\",\"ShipToCust\":\"{sor.ShipToCustomerName}\"," +
                    $"\"ShipToCustCountry\":\"{sor.ShipToCustomerCountry}\",\"CommodityCode\":\"{sor.CommodityCode}\",\"ECCN\":\"{sor.ECCN}\",\"DestinationCountry\":\"{sor.DestinationCountry}\"," +
                    $"\"MilitaryUse\":\"{sor.MilitaryUseName}\",\"EndUser\":\"{sor.EndUserText}\",\"PartApplication\":\"{sor.PartApplication}\",\"ExportControl\":\"{((sor.ExportControl == 0) ? "No" : "Yes")}\"," +
                    $"\"AerospaceUse\":\"{((sor.AerospaceUse == 0) ? "No" : "Yes")}\",\"PartsTest\":\"{sor.PartTested}\",\"SODetailURL\":\"{soDetailsUrl}\",\"EUUFormUrl\":\"{euuFormUrl}\",\"CountryOfOrigin\":\"{sor.CountryOfOrigin}\"}}";

                var content = new StringContent(body, Encoding.UTF8, "application/json");
                request.Content = content;
                //var response = await MakeRequestAsync(request, client);
                client.SendAsync(request).ConfigureAwait(false);

            }
            catch (Exception e)
            {
                Console.WriteLine(e.Message);
                throw new Exception();
            }
        }
        public async Task<string> MakeRequestAsync(HttpRequestMessage getRequest, HttpClient client)
        {
            var response = await client.SendAsync(getRequest).ConfigureAwait(false);
            var responseString = string.Empty;
            try
            {
                response.EnsureSuccessStatusCode();
                responseString = await response.Content.ReadAsStringAsync().ConfigureAwait(false);
            }
            catch (HttpRequestException)
            {
                // empty responseString
            }

            return responseString;
        }
        private void SaveSOReportForPowerApp(System.Int32? salesOrderId)
        {
            string strPdfPath = "";
            SalesOrder sor = SalesOrder.GetSOPowerAppApprovalData(salesOrderId);
            DateTime currencyDate = sor.CurrencyDate == null ? (DateTime)sor.DateOrdered : (DateTime)sor.CurrencyDate;
            double dblOpenSOTotal = 0;
            List<SalesOrder> lst = SalesOrder.GetListOpenForCompany(sor.CompanyNo);
            for (int n = 0; n < lst.Count; n++)
            {
                SalesOrder SOTotal = SalesOrder.GetOpenLineSummaryValues(lst[n].SalesOrderId);
                if (SOTotal.CurrencyNo != sor.SOCurrencyNo)
                {
                    dblOpenSOTotal += BLL.Currency.ConvertValueBetweenTwoCurrencies(SOTotal.TotalValue, SOTotal.CurrencyNo, sor.SOCurrencyNo, currencyDate);
                }
                else
                {
                    dblOpenSOTotal += Convert.ToDouble(SOTotal.TotalValue);
                }
                SOTotal = null;
            }
            Boolean IsAllowAuthorise = sor.CreditLimit >= (sor.Balance + dblOpenSOTotal) ? true : false;
            if (IsAllowAuthorise && !Convert.ToBoolean(sor.IsSoCompleted) && Convert.ToBoolean(sor.IsPODocExists))
            {
                GeneratePDF obj = new GeneratePDF();
                SalesOrder so = SalesOrder.GetForPrint(salesOrderId);
                if (so != null)
                {
                    obj.PrintSalesOrderReport(so, out strPdfPath);
                }
                if (!string.IsNullOrEmpty(strPdfPath))
                {
                    BLL.SalesOrder.Insert(salesOrderId, "Approval_SOR", strPdfPath, SessionManager.LoginID, "SORPDFNEW");
                }
                obj = null;
            }
            sor = null;
        }
        private string RemoveHyperLink(string strMessage)
        {
            return Regex.Replace(strMessage, "</?(a|A).*?>", "").Replace("\r\n", "<br/>");
        }
  
        #endregion
    }
}
