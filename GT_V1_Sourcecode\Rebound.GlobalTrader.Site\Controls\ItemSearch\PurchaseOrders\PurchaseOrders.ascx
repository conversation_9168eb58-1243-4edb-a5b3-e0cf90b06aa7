<%@ Control Language="C#" CodeBehind="PurchaseOrders.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.ItemSearch.PurchaseOrders" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_ItemSearch:DesignBase id="ctlDB" runat="server" InitialSortDirection="DESC">
	<FieldsLeft>
		<ReboundUI_FilterDataItemRow:Numerical id="ctlPurchaseOrderNo" runat="server" ResourceTitle="PurchaseOrderNo" TextBoxMaxLength="11" />
		<ReboundUI_FilterDataItemRow:TextBox id="ctlCompany" runat="server" ResourceTitle="Company" />
		<ReboundUI_FilterDataItemRow:TextBox id="ctlContact" runat="server" ResourceTitle="Contact" />
		<ReboundUI_FilterDataItemRow:CheckBox id="ctlIncludeClosed" runat="server" ResourceTitle="IncludeClosed" />
		<ReboundUI_FilterDataItemRow:DropDown id="ctlBuyer" runat="server" ResourceTitle="Buyer" DropDownType="Employee" DropDownAssembly="Rebound.GlobalTrader.Site" />
	</FieldsLeft>
	<FieldsRight>
		<ReboundUI_FilterDataItemRow:DateSelect id="ctlDateOrderedFrom" runat="server" ResourceTitle="DateOrderedFrom" />
		<ReboundUI_FilterDataItemRow:DateSelect id="ctlDateOrderedTo" runat="server" ResourceTitle="DateOrderedTo" />
		<ReboundUI_FilterDataItemRow:DateSelect id="ctlExpediteDateFrom" runat="server" ResourceTitle="ExpediteDateFrom" />
		<ReboundUI_FilterDataItemRow:DateSelect id="ctlExpediteDateTo" runat="server" ResourceTitle="ExpediteDateTo" />
		<ReboundUI_FilterDataItemRow:Numerical id="ctlInternalPurchaseOrderNo" runat="server" ResourceTitle="InternalPurchaseOrderNo" />
	</FieldsRight>
</ReboundUI_ItemSearch:DesignBase>
