﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Rebound.GlobalTrader.BLL.Entities;
using Rebound.GlobalTrader.DAL.SQLClient;

namespace Rebound.GlobalTrader.BLL.BusinessLogic
{
    public class XMatch
    {
        public int Login(XMatchLogin objXMatchLogin, out string ErrorMessage)
        {
            ErrorMessage = string.Empty;
            SQLXMatchProvider objSQLXMatchProvider = new SQLXMatchProvider();
            return objSQLXMatchProvider.Login(objXMatchLogin.UserName, objXMatchLogin.Password, out ErrorMessage);
        }

        public DataSet GetOfferAndExcess(int LoginId)
        {
            SQLXMatchProvider objSQLXMatchProvider = new SQLXMatchProvider();
            return objSQLXMatchProvider.GetOfferAndExcess(LoginId);
        }

        public DataTable GetXMatchMatchingData(DateTime FromDate, DateTime ToDate, string MatchType, int MatchFirstValue, bool ExcludeZero, int numberReturn, string supplierType, int LoginID, int ClinetId)
        {
            SQLXMatchProvider objSQLXMatchProvider = new SQLXMatchProvider();
            return objSQLXMatchProvider.GetXMatchMatchingData(FromDate, ToDate, MatchType, MatchFirstValue, ExcludeZero, numberReturn, supplierType, LoginID, ClinetId);
        }
        public DataTable GetXMatchMatchingDataBOMManager(DateTime FromDate, DateTime ToDate, string MatchType, int MatchFirstValue, int LoginID, int ClinetId, int BOMManagerID, int? CustomerRequirementID, int curPage = 1, int Rpp = 5)
        {
            SQLXMatchProvider objSQLXMatchProvider = new SQLXMatchProvider();
            return objSQLXMatchProvider.GetXMatchMatchingDataBOMManager(FromDate, ToDate, MatchType, MatchFirstValue, LoginID, ClinetId, BOMManagerID, CustomerRequirementID, curPage, Rpp);
        }
        public DataTable GetUserXMatchData(int BOMManagerID, int? SalesXMatchID)
        {
            SQLXMatchProvider objSQLXMatchProvider = new SQLXMatchProvider();
            return objSQLXMatchProvider.GetUserXMatchData(BOMManagerID, SalesXMatchID);
        }
        public void SaveUserXmatchData(int? MOQ, int SPQ, int? StockQTY, string DateCode, string LeadTime, int BOMManagerID, int SalesXMatchID)
        {
            SQLXMatchProvider objSQLXMatchProvider = new SQLXMatchProvider();
            objSQLXMatchProvider.SaveUserXmatchData(MOQ, SPQ, StockQTY, DateCode, LeadTime, BOMManagerID, SalesXMatchID);
        }
        public DataTable GetXMatchAdSearchData(DateTime FromDate, DateTime ToDate, string MatchType, string SearchText, int UserId, int ClientId)
        {
            SQLXMatchProvider objSQLXMatchProvider = new SQLXMatchProvider();
            return objSQLXMatchProvider.GetXMatchAdSearchData(FromDate, ToDate, MatchType, SearchText, UserId, ClientId);
        }
        public void SaveXMatchHeader(string columnList, string insertColumnList, System.Int32? clientId, int? loginID)
        {
            SQLXMatchProvider objSQLXMatchProvider = new SQLXMatchProvider();
            objSQLXMatchProvider.SaveXMatchHeader(columnList, insertColumnList, clientId, loginID);
        }
        public void saveXMatchImportData(DataTable tempXMatch, DataTable dtData, string originalFilename, string generatedFilename, System.Int32 userId, System.Int32 clientId)
        {
            SQLXMatchProvider objSQLXMatchProvider = new SQLXMatchProvider();
            objSQLXMatchProvider.saveXMatchImportData(tempXMatch, dtData, originalFilename, generatedFilename, userId, clientId);
        }
        public DataTable GetXMatchDetailFromTemp(int displayLength, int displayStart, int sortCol, string sortDir, string search, int userid, int ClientId)
        {
            SQLXMatchProvider objSQLXMatchProvider = new SQLXMatchProvider();
            return objSQLXMatchProvider.GetXMatchDetailFromTemp(displayLength, displayStart, sortCol, sortDir, search, userid, ClientId);
        }
        public DataTable GetXMatchHeader(System.Int32 clientStockId, System.Int32 userId)
        {
            SQLXMatchProvider objSQLXMatchProvider = new SQLXMatchProvider();
            return objSQLXMatchProvider.GetXMatchHeader(clientStockId, userId);
        }
        public DataTable GetExcelHeaderTemp(System.Int32 clientStockId, System.Int32 userId)
        {
            SQLXMatchProvider objSQLXMatchProvider = new SQLXMatchProvider();
            return objSQLXMatchProvider.GetExcelHeaderTemp(clientStockId, userId);
        }
        public DataTable GetXMatchGenrateData(int displayLength, int displayStart, int sortCol, string sortDir, string search, int userid, int ClientId, string ColumnList, string Column_Lable, string Column_Name)
        {
            SQLXMatchProvider objSQLXMatchProvider = new SQLXMatchProvider();
            return objSQLXMatchProvider.GetXMatchGenrateData(displayLength, displayStart, sortCol, sortDir, search, userid, ClientId, ColumnList, Column_Lable, Column_Name);
        }
        public int SaveXMatchImportData(int userId, int ClientId, string Column_Lable, string Column_Name, string insertDataList, string fileColName, out System.String errorMessage, bool IsDelExistingData)
        {
            SQLXMatchProvider objSQLXMatchProvider = new SQLXMatchProvider();
            return objSQLXMatchProvider.SaveXMatchImportData(userId, ClientId, Column_Lable, Column_Name, insertDataList, fileColName, out errorMessage, IsDelExistingData);
        }
        public int DeleteXMatchRecord(System.Int32 userId, int ClientId)
        {
            SQLXMatchProvider objSQLXMatchProvider = new SQLXMatchProvider();
            return objSQLXMatchProvider.DeleteXMatchRecord(userId, ClientId);
        }

        public DataTable GetXMatchExcessData(int LoginID, int ClientID)
        {
            SQLXMatchProvider objSQLXMatchProvider = new SQLXMatchProvider();
            return objSQLXMatchProvider.GetXMatchExcessData(LoginID, ClientID);
        }

        public int DeleteExcessRecord(int LoginId, int ClientId)
        {
            SQLXMatchProvider objSQLXMatchProvider = new SQLXMatchProvider();
            return objSQLXMatchProvider.DeleteExcessRecord(LoginId, ClientId);
        }

        public int GetExcessRecord(int UserId, int ClientId)
        {
            SQLXMatchProvider objSQLXMatchProvider = new SQLXMatchProvider();
            return objSQLXMatchProvider.GetExcessRecord(UserId, ClientId);
        }
        public DataTable GetXMatchClient(int ClientId)
        {
            SQLXMatchProvider objSQLXMatchProvider = new SQLXMatchProvider();
            return objSQLXMatchProvider.GetXMatchClient(ClientId);
        }
        public DataTable GetSupplierTrustedOfferXMatch(int ClientType, int ClientId)
        {
            SQLXMatchProvider objSQLXMatchProvider = new SQLXMatchProvider();
            return objSQLXMatchProvider.GetSupplierTrustedOfferXMatch(ClientType, ClientId);
        }

        public DataTable ProcessGTMatchData(string ExComp, string OfComp, string DocType, DateTime FromDate, int ClientID, int Months)
        {
            SQLXMatchProvider objSQLXMatchProvider = new SQLXMatchProvider();
            return objSQLXMatchProvider.ProcessGTMatchData(ExComp, OfComp, DocType, FromDate, ClientID, Months);
        }
    }
}
