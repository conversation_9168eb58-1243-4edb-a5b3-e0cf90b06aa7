Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");Rebound.GlobalTrader.Site.Controls.DropDowns.EIRequired=function(a){Rebound.GlobalTrader.Site.Controls.DropDowns.EIRequired.initializeBase(this,[a])};Rebound.GlobalTrader.Site.Controls.DropDowns.EIRequired.prototype={initialize:function(){Rebound.GlobalTrader.Site.Controls.DropDowns.EIRequired.callBaseMethod(this,"initialize");this.addSetupDataCall(Function.createDelegate(this,this.setupDataCall));this.addGetDataOK(Function.createDelegate(this,this.dataCallOK))},dispose:function(){if(this.isDisposed)return;Rebound.GlobalTrader.Site.Controls.DropDowns.EIRequired.callBaseMethod(this,"dispose")},setupDataCall:function(){this._objData.set_PathToData("controls/DropDowns/EIRequired");this._objData.set_DataObject("EIRequired");this._objData.set_DataAction("GetData")},dataCallOK:function(){var a=this._objData._result;if(a!=null)if(a.Types)for(var b=0;b<a.Types.length;b++)this.addOption(a.Types[b].Name,a.Types[b].ID)}};Rebound.GlobalTrader.Site.Controls.DropDowns.EIRequired.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.EIRequired",Rebound.GlobalTrader.Site.Controls.DropDowns.Base);