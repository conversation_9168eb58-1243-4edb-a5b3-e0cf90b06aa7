///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");
Rebound.GlobalTrader.Site.Controls.Forms.GILines_AddShortShipment = function (element) {
    Rebound.GlobalTrader.Site.Controls.Forms.GILines_AddShortShipment.initializeBase(this, [element]);
    this._intGIID = 0;
    this._intLineID = -1;
    this._intPOQuantityOrdered = 0;
    this._intGlobalClientNo = -1;
    this._intSupplierNo = -1;
    this._intBuyerNo = -1;
    this._intManufacturerNo = -1;
    this._intSalesPersonId = -1;
    this._intRaisedById = -1;
    this._IsPoHubLogin = 0;
    this._intInternalPurchaseOrderId = 0;
    this._intIPOSupplier = 0;
    this._IPOSupplierName = "";
    this._FirstLineCustomer = "";
    this._PurchaseOrderNo = -1;
    this._intSupportTeamMemberNo = -1;
};

Rebound.GlobalTrader.Site.Controls.Forms.GILines_AddShortShipment.prototype = {

    get_intGIID: function () { return this._intGIID; }, set_intGIID: function (v) { if (this._intGIID !== v) this._intGIID = v; },
    get_intPOQuantityOrdered: function () { return this._intPOQuantityOrdered; }, set_intPOQuantityOrdered: function (v) { if (this._intPOQuantityOrdered !== v) this._intPOQuantityOrdered = v; },

    initialize: function () {
        Rebound.GlobalTrader.Site.Controls.Forms.GILines_AddShortShipment.callBaseMethod(this, "initialize");
        this.addShown(Function.createDelegate(this, this.formShown));
        this.addSave(Function.createDelegate(this, this.saveClicked));

    },

    formShown: function () {
        this.storeOriginalFieldValues();
    },

    dispose: function () {
        if (this.isDisposed) return;
        this._intGIID = null;
        this._intPOQuantityOrdered = null;
        this._intSupplierNo = null;
        this._intBuyerNo = null;
        this._intManufacturerNo = null;
        this._intSalesPersonId = null;
        this._intRaisedById = null;

        this._IsPoHubLogin = null;
        this._intInternalPurchaseOrderId = null;
        this._intIPOSupplier = null;
        this._IPOSupplierName = null;
        Rebound.GlobalTrader.Site.Controls.Forms.GILines_AddShortShipment.callBaseMethod(this, "dispose");
    },

    saveClicked: function () {
        if (!this.validateForm()) return;
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/GILines");
        obj.set_DataObject("GILines");
        obj.set_DataAction("SaveShortShipment");
        obj.addParameter("id", this._intLineID);
        obj.addParameter("PurchaseOrderNo", this._PurchaseOrderNo);
        obj.addParameter("SalesContact", this._intSalesPersonId);
        obj.addParameter("Reference", this.getFieldValue("ctlReferenceDetail"));
        obj.addParameter("GoodsIn", this._intGIID);
        obj.addParameter("ReceivedDate", this.getFieldValue("ctlReceivedDate"));
        obj.addParameter("RaisedBy", this._intRaisedById);
        obj.addParameter("Buyer", this._intBuyerNo);
        obj.addParameter("PartNo", this.getFieldValue("ctlPartNo"));
        obj.addParameter("Manufacturer", this._intManufacturerNo);
        obj.addParameter("QuantityOrder", this.getFieldValue("ctlQuantityOrder"));
        obj.addParameter("QuantityAdvised", this.getFieldValue("ctlQuantityAdvised"));
        obj.addParameter("QuantityReceived", this.getFieldValue("ctlQuantityReceived"));
        obj.addParameter("ShortageQuantity", this.getFieldValue("ctlShortageQuantity"));
        obj.addParameter("ShortageValue", this.getFieldValue("ctlShortageValue"));
        obj.addParameter("GoodsInNo", this.getFieldValue("ctlGoodsIn"));
        obj.addParameter("PONumber", this.getFieldValue("ctlPurchaseOrder"));
        if ((this._IsPoHubLogin == 0 && this._intInternalPurchaseOrderId > 0)) {
            obj.addParameter("IsPoHub", true);
            obj.addParameter("Supplier", this._intIPOSupplier);
        }
        else {
            obj.addParameter("IsPoHub", false);
            obj.addParameter("Supplier", this._intSupplierNo);
        }
        obj.addParameter("Buyername", this.getFieldValue("ctlBuyer"));
        obj.addParameter("Suppliername", this.getFieldValue("ctlSupplier"));
        obj.addParameter("Customername", this._FirstLineCustomer);
        obj.addParameter("SupportTeamMemberNo", this._intSupportTeamMemberNo);
        obj.addDataOK(Function.createDelegate(this, this.saveShortShipmentComplete));
        obj.addError(Function.createDelegate(this, this.saveShortShipmentError));
        obj.addTimeout(Function.createDelegate(this, this.saveShortShipmentError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    saveShortShipmentError: function (args) {
        this._strErrorMessage = args._errorMessage;
        this.onSaveError();
    },

    saveShortShipmentComplete: function (args) {
        if (args._result.Result == true) {
            this.onSaveComplete();
        } else {
            var message = args._result.Message == "" ? args._errorMessage : args._result.Message;
            this.showMessage(true, message);
            return false;
            //this.onSaveError();
        }

    },

    validateForm: function () {
        this.onValidate();
        var blnOK = this.autoValidateFields();
        return blnOK;
    },
    showMessage: function (bln, strText) {
        $R_FN.showElement(this._pnlValidateError, bln);
        if (bln) {
            $R_FN.showElement(this._pnlSaving, false);
            $R_FN.showElement(this._pnlSavedOK, false);
            $R_FN.showElement(this._pnlLoading, false);
            $R_FN.showElement(this._pnlContentInner, true);
            $R_FN.showElement(this._pnlLinksHolder, true);
            $R_FN.showElement(this._pnlFooterLinksHolder, true);
            if (this._ctlRelatedNugget) {
                this._ctlRelatedNugget.control.showLoading(false);
                this._ctlRelatedNugget.control.showRefresh(true);
            }
            if (!strText) strText = "";
            this._pnlValidateErrorText.innerHTML = strText;
        }
    }

};

Rebound.GlobalTrader.Site.Controls.Forms.GILines_AddShortShipment.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.GILines_AddShortShipment", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
