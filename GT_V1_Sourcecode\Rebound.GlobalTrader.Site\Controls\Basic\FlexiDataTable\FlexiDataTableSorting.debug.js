///<reference name="MicrosoftAjax.js" />
///<reference path="~/Common/Functions/Functions.js" />
//-----------------------------------------------------------------------------------------
// RP 15.01.2010:
// - new script
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls");

Rebound.GlobalTrader.Site.Controls.FlexiDataTableSorting = function() { 
};

var $R_TABLESORT = Rebound.GlobalTrader.Site.Controls.FlexiDataTableSorting;
$R_TABLESORT._objSortData = {};

Rebound.GlobalTrader.Site.Controls.FlexiDataTableSorting.dispose = function() {
	if ($R_TABLESORT.isDisposed) return;
	$R_TABLESORT._objSortData = null;
	$R_TABLESORT.isDisposed = true;
};

Rebound.GlobalTrader.Site.Controls.FlexiDataTableSorting.registerTable = function(strID) {
	if (!$R_TABLESORT._objSortData) $R_TABLESORT._objSortData = [];
	$R_TABLESORT.resetSort(strID);
};

Rebound.GlobalTrader.Site.Controls.FlexiDataTableSorting.doSort = function(strID, intSortColumnIndex) {
	$R_TABLESORT.resetSort(strID);
	var tbl = $find(strID);
	if (!tbl) return;
	var obj = $R_TABLESORT._objSortData[strID];
	if (!obj) return;

	//store the data ready for sorting
	for (var i = 0, l = tbl._tbl.rows.length; i < l; i++) {
		Array.add(obj.SortData, {
			I: i 
			, V: $R_FN.splitDoubleCellValue( tbl._tbl.rows[i].cells[intSortColumnIndex].innerHTML)[0]
		});
		
		var aryData = [];
		for (var j = 0, l2 = tbl._tbl.rows[i].cells.length; j < l2; j++) {
			Array.add(aryData, tbl._tbl.rows[i].cells[j].innerHTML);
		}
		Array.add(obj.TableData, {
			D: aryData
			, ID: tbl._aryValues[i]
			, X: tbl._aryExtraData[i]
			, CSS: tbl._tbl.rows[i].cells[0].className.indexOf(" ") > 0 ? tbl._tbl.rows[i].cells[0].className.split(" ")[1] : ""
		});
		aryData = null;
	}

	//clear the table
	tbl.clearTable();
	
	//do the correct sort for the fieldtype
	switch (tbl._aryColumnClientSortFormat[intSortColumnIndex]) {
		case $R_ENUM$ClientSortFormat.Date:
			obj.SortData = obj.SortData.sort($R_TABLESORT.doSort_Date);
			break;
		case $R_ENUM$ClientSortFormat.DateAndTime:
			obj.SortData = obj.SortData.sort($R_TABLESORT.doSort_DateTime);
			break;
		case $R_ENUM$ClientSortFormat.Numeric:
		case $R_ENUM$ClientSortFormat.Currency:
			obj.SortData = obj.SortData.sort($R_TABLESORT.doSort_Numeric);
			break;
		case $R_ENUM$ClientSortFormat.Text:
		default:
			obj.SortData = obj.SortData.sort($R_TABLESORT.doSort_Text);
			break;
	}
	if (tbl._enmSortDirection == $R_ENUM$SortColumnDirection.DESC) obj.SortData.reverse();
	
	//asyncronously add back the data in the correct order
	obj.RowsAdded = 0;
	$R_TABLESORT.addRow(strID);
	
	//cleanup
	tbl = null;
	obj = null;
	fnAdd = null;
	fnFinish = null;
};

Rebound.GlobalTrader.Site.Controls.FlexiDataTableSorting.addRow = function(strID) {
	var obj = $R_TABLESORT._objSortData[strID];
	if (!obj) return;
	var tbl = $find(strID);
	if (!tbl) return;

	if (obj.RowsAdded < obj.SortData.length) {
		var row = obj.TableData[obj.SortData[obj.RowsAdded].I];
		obj.RowsAdded += 1;
		tbl.addRow(
			row.D
			, row.ID
			, false
			, row.X
			, row.CSS
		);
		setTimeout(function() { $R_TABLESORT.addRow(strID); }, 0);
	} else {
		tbl.resizeColumns();
		$R_TABLESORT.resetSort(strID);
	}
	tbl = null;
};

Rebound.GlobalTrader.Site.Controls.FlexiDataTableSorting.resetSort = function(strID) {
	$R_TABLESORT._objSortData[strID] = {
		SortData:[]
		, TableData: []
		, RowsAdded: 0
	};
};

Rebound.GlobalTrader.Site.Controls.FlexiDataTableSorting.doSort_Text = function(a, b) {
	var strA = a.V.toLowerCase();
	var strB = b.V.toLowerCase();
	if (strA < strB) { return -1; }
	if (strA == strB) { return 0; }
	if (strA > strB) { return 1; }
};

Rebound.GlobalTrader.Site.Controls.FlexiDataTableSorting.doSort_Numeric = function(a, b) {
	var re = new RegExp(String.format("[^0-9{0}]| ", Sys.CultureInfo.CurrentCulture.numberFormat.NumberDecimalSeparator), "gi");
	var varA = Number.parseLocale(a.V.replace(re, ""));
	var varB = Number.parseLocale(b.V.replace(re, ""));
	if (varA < varB) { return -1; }
	if (varA == varB) { return 0; }
	if (varA > varB) { return 1; }
};

Rebound.GlobalTrader.Site.Controls.FlexiDataTableSorting.doSort_Date = function(a, b) {
	var varA = Date.parseLocale(a.V);
	var varB = Date.parseLocale(b.V);
	if (varA < varB) { return -1; }
	if (varA == varB) { return 0; }
	if (varA > varB) { return 1; }
};
			
Rebound.GlobalTrader.Site.Controls.FlexiDataTableSorting.doSort_DateTime = function(a, b) {
	var objA = a.V.split(" ");
	var varA = $R_FN.getDateFromDateAndTime(objA[0], objA[1]);
	var objB = b.V.split(" ");
	var varB = $R_FN.getDateFromDateAndTime(objB[0], objB[1]);
	if (varA < varB) { return -1; }
	if (varA == varB) { return 0; }
	if (varA > varB) { return 1; }
};

Rebound.GlobalTrader.Site.Controls.FlexiDataTableSorting.registerClass("Rebound.GlobalTrader.Site.Controls.FlexiDataTableSorting");