using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site.Controls;
using Rebound.GlobalTrader.Site.Controls.DropDowns;

namespace Rebound.GlobalTrader.Site.Controls.DataListNuggets {
    public partial class CustomerRequirementsPrint : Base
    {

        Combo _cmb;
        ContactsForCompany _ddlContact;
        
        #region Overrides

        /// <summary>
        /// OnInit
        /// </summary>
        /// <param name="e"></param>
        protected override void OnInit(EventArgs e) {
            SetDataListNuggetType("CustomerRequirementsPrint");
            base.OnInit(e);
            WireUpControls();
            TitleText = Functions.GetGlobalResource("Nuggets", "CustomerRequirementsPrint");
            AddScriptReference("Controls.DataListNuggets.CustomerRequirementsPrint.CustomerRequirementsPrint.js");
            SetupTable();
        }

        protected override void OnLoad(EventArgs e) {
            _scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.DataListNuggets.CustomerRequirementsPrint", ctlDesignBase.ClientID);
            _scScriptControlDescriptor.AddProperty("intCompanyID", _objQSManager.CompanyID);
            _scScriptControlDescriptor.AddProperty("strCompanyName",HttpContext.Current.Server.UrlDecode(_objQSManager.CompanyName));
            _scScriptControlDescriptor.AddComponentProperty("cmbCustomer", _cmb.ClientID);
            _scScriptControlDescriptor.AddComponentProperty("ddlContact", _ddlContact.ClientID);
            _scScriptControlDescriptor.AddProperty("intContactID", _objQSManager.ContactID);

			base.OnLoad(e);
        }
      

        protected override void RenderAdditionalState()
        {
            //string strViewLevel = this.GetSavedStateValue("ViewLevel");
            //if (!string.IsNullOrEmpty(strViewLevel))
            //{
            //    ((Pages.Content)Page).CurrentTab = Convert.ToInt32(strViewLevel);
            //    _enmViewLevel = (ViewLevelList)Convert.ToInt32(strViewLevel);
            //    this.OnAskPageToChangeTab();
            //}
            base.RenderAdditionalState();
        }

        #endregion

        private void SetupTable() {
            _tbl.AllowSelection = true;
            _tbl.AllowMultipleSelection = true;
            _tbl.SortColumnDirection = Rebound.GlobalTrader.Site.Enumerations.SortColumnDirection.DESC;
            _tbl.Columns.Add(new FlexiDataColumn("Number", WidthManager.GetWidth(WidthManager.ColumnWidth.SystemDocumentNumber), true));
            _tbl.Columns.Add(new FlexiDataColumn("PartNo", "Manufacturer", WidthManager.GetWidth(WidthManager.ColumnWidth.PartNo), true));
            _tbl.Columns.Add(new FlexiDataColumn("Quantity", WidthManager.GetWidth(WidthManager.ColumnWidth.Quantity), true));
            _tbl.Columns.Add(new FlexiDataColumn("Company", "Contact", WidthManager.GetWidth(WidthManager.ColumnWidth.CompanyName), true));
            _tbl.Columns.Add(new FlexiDataColumn("Salesman", WidthManager.GetWidth(WidthManager.ColumnWidth.ContactName), true));
            _tbl.Columns.Add(new FlexiDataColumn("ReceivedDate", "DatePromised", Unit.Empty, true));
        }

        private void WireUpControls()
        {
            _cmb =(Combo) FindFilterControl("cmbCustomer");
            _ddlContact = (ContactsForCompany)FindFilterControl("ddlContact");

            
        }

    }
}