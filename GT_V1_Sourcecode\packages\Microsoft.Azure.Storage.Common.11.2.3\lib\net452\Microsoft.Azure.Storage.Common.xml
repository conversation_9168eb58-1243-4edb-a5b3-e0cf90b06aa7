<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.Azure.Storage.Common</name>
    </assembly>
    <members>
        <member name="T:Microsoft.Azure.Storage.Core.ByteCountingStream">
            <summary>
            This class provides a wrapper that will update the Ingress / Egress bytes of a given request result as the stream is used.
            Note this is not supported for Windows RT / .Net 4.5 as some Async methods may not be able to be intercepted.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.ByteCountingStream.#ctor(System.IO.Stream,Microsoft.Azure.Storage.RequestResult,System.Boolean)">
            <summary>
            Initializes a new instance of the ByteCountingStream class with an expandable capacity initialized to zero.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.ByteCountingStream.BeginRead(System.Byte[],System.Int32,System.Int32,System.AsyncCallback,System.Object)">
            <summary>
            Begins an asynchronous read operation.
            </summary>
            <param name="buffer">When this method returns, the buffer contains the specified byte array with the values between offset and (offset + count - 1) replaced by the bytes read from the current source.</param>
            <param name="offset">The zero-based byte offset in buffer at which to begin storing the data read from the current stream.</param>
            <param name="count">The maximum number of bytes to be read.</param>
            <param name="callback">An optional asynchronous callback, to be called when the read is complete.</param>
            <param name="state">A user-provided object that distinguishes this particular asynchronous read request from other requests.</param>
            <returns>An IAsyncResult that represents the asynchronous read, which could still be pending.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.ByteCountingStream.EndRead(System.IAsyncResult)">
            <summary>
            Waits for the pending asynchronous read to complete.
            </summary>
            <param name="asyncResult">The reference to the pending asynchronous request to finish.</param>
            <returns>The total number of bytes read into the buffer. This can be less than the number of bytes requested if that many bytes are not currently available, or zero if the end of the stream has been reached.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.ByteCountingStream.BeginWrite(System.Byte[],System.Int32,System.Int32,System.AsyncCallback,System.Object)">
            <summary>
            Begins an asynchronous write operation.
            </summary>
            <param name="buffer">The buffer to write data from.</param>
            <param name="offset">The zero-based byte offset in buffer at which to begin copying bytes to the current stream.</param>
            <param name="count">The number of bytes to write.</param>
            <param name="callback">An optional asynchronous callback, to be called when the write is complete.</param>
            <param name="state">A user-provided object that distinguishes this particular asynchronous write request from other requests.</param>
            <returns>An IAsyncResult that represents the asynchronous write, which could still be pending.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.ByteCountingStream.EndWrite(System.IAsyncResult)">
            <summary>
            Ends an asynchronous write operation.
            </summary>
            <param name="asyncResult">The reference to the pending asynchronous request to finish.</param>
        </member>
        <member name="T:Microsoft.Azure.Storage.Core.LengthLimitingStream">
            <summary>
            Stream that will be used for decrypting blob ranges. It is used to discard extra bytes from the beginning and end if required.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Logger.FormatLine(Microsoft.Azure.Storage.OperationContext,System.String,System.Object[])">
            <summary>
            Creates a well-formatted log entry so that logs can be easily parsed
            </summary>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param>
            <param name="format">A composite format string.</param>
            <param name="args">An object array that contains zero or more objects to format.</param>
            <returns>Log entry that contains common log prefix and a copy of format in which the format items have been replaced by the string representation of the corresponding objects in args.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Logger.ShouldLog(Microsoft.Azure.Storage.LogLevel,Microsoft.Azure.Storage.OperationContext)">
            <summary>
            Determines if the current operation context allows for a specific level of log entry.
            </summary>
            <param name="level">Level of the log entry.</param>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param>
            <returns><c>true</c> if the entry should be logged; otherwise <c>false</c>.</returns>
        </member>
        <member name="T:Microsoft.Azure.Storage.Core.TimeoutStream">
            <summary>
            Stream that will throw a <see cref="T:System.OperationCanceledException"/> if it has to wait longer than a configurable timeout to read or write more data
            </summary>
        </member>
        <member name="T:Microsoft.Azure.Storage.Core.Util.APMWithTimeout">
            <summary>
            Helper class to allow an APM Method to be executed with a given timeout in milliseconds
            </summary>
        </member>
        <member name="T:Microsoft.Azure.Storage.Core.Util.AsyncExtensions">
            <summary>
            Helper class to convert an APM method to a Task method.
            </summary>
        </member>
        <member name="T:Microsoft.Azure.Storage.Core.Util.AsyncSemaphore">
            <summary>
            This class provides asynchronous semaphore functionality (based on Stephen Toub's blog).
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Util.AsyncStreamCopier`1.#ctor(System.IO.Stream,System.IO.Stream,Microsoft.Azure.Storage.Core.Executor.ExecutionState{`0},Microsoft.Azure.Storage.IBufferManager,System.Nullable{System.Int32},Microsoft.Azure.Storage.Shared.Protocol.ChecksumRequested,Microsoft.Azure.Storage.Core.Util.StreamDescriptor)">
            <summary>
            Creates and initializes a new asynchronous copy operation.
            </summary>
            <param name="src">The source stream.</param>
            <param name="dest">The destination stream.</param>
            <param name="state">An ExecutionState used to coordinate copy operation.</param>
            <param name="bufferManager">IBufferManager instance to use.  May be null.</param>
            <param name="buffSize">Size of read and write buffers used to move data.  Overrides the default buffer size of bufferManager.</param>
            <param name="calculateChecksum">A value indicating whether the checksums should be calculated.</param>
            <param name="streamCopyState">An object that represents the state for the current operation.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Util.AsyncStreamCopier`1.StartCopyStream(System.Action{Microsoft.Azure.Storage.Core.Executor.ExecutionState{`0}},System.Nullable{System.Int64},System.Nullable{System.Int64},System.Threading.CancellationToken)">
            <summary>
            Begins a stream copy operation.
            
            This method wraps the StartCopyStreamAsync method, presenting a different API for it.
            As we update the library to be more task-based, callers should gradually move to StartCopyStreamAsync.
            </summary>
            <param name="completedDelegate">Callback delegate</param>
            <param name="copyLength">Number of bytes to copy from source stream to destination stream. Cannot pass in both copyLength and maxLength.</param>
            <param name="maxLength">Maximum length of the source stream. Cannot pass in both copyLength and maxLength.</param>
            <param name="cancellationToken">The cancellation token for the operation.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Util.AsyncStreamCopier`1.Dispose">
            <summary>
            Cleans up references. To end a copy operation, call Cancel() on the ExecutionState.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Util.AsyncStreamCopier`1.StartCopyStreamAsync(System.Nullable{System.Int64},System.Nullable{System.Int64},System.Threading.CancellationToken)">
             <summary>
             This method performs the stream copy in an asynchronous, task-based manner.
             
             To do the stream copy in a begin-with-callback style (the old style), use StartCopyStream, which wraps this method,
             does appropriate cancellation/exception handling, and calls the callback.
            
             This method sets up cancellation, and will cancel if either the timeout on the execution state expires, or cancel() is called 
             directly (on the ExecutionState).
             
             This method does not set the ExceptionRef on the ExecutionState, or abort the request.
             </summary>
             <param name="copyLength">Number of bytes to copy from source stream to destination stream. Cannot pass in both copyLength and maxLength.</param>
             <param name="maxLength">Maximum length of the source stream. Cannot pass in both copyLength and maxLength.</param>
             <param name="cancellationToken">The cancellation token for the operation.</param>
             <returns>A Task representing the asynchronous stream copy.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Util.AsyncStreamCopier`1.StartCopyStreamAsyncHelper(System.Nullable{System.Int64},System.Nullable{System.Int64},System.Threading.CancellationToken)">
            <summary>
            This method does the actual internal logic of copying one stream to another.
            </summary>
            <param name="copyLength">Number of bytes to copy from source stream to destination stream. Cannot pass in both copyLength and maxLength.</param>
            <param name="maxLength">Maximum length of the source stream. Cannot pass in both copyLength and maxLength.</param>
            <param name="token">Cancellation token.</param>
            <returns>A Task representing the asynchronous stream copy.</returns>
        </member>
        <member name="T:Microsoft.Azure.Storage.Core.Util.CancellableAsyncResultTaskWrapper">
            <summary>
            This class is designed to bridge the gap between async and APM.
            Specifically, if you have a Task-based async method and you want to wrap Begin() and End() methods around it, that's
            what this class is for.
            Usually, this is trivial with normal Tasks, but because we use our custom 'ICancellableAsyncResult' rather than 'IAsyncResult', 
            we need this custom class.
            
            
            Sample usage, assuming we already have an "DoThingAsync(CancellationToken token)" method that returns a Task:
            
            public virtual ICancellableAsyncResult BeginDoThing(AsyncCallback callback, object state)
            {
                return CancellableAsyncResultTaskWrapper.Create(token => DoThingAsync(token), callback, state);
            }
            
            public virtual void EndDoThing(IAsyncResult asyncResult)
            {
                ((CancellableAsyncResultTaskWrapper)asyncResult).Wait();
            }
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Util.CancellableAsyncResultTaskWrapper.#ctor(System.Func{System.Threading.CancellationToken,System.Threading.Tasks.Task},System.AsyncCallback,System.Object)">
            <summary>
            Creates a new ICancellableAsyncResult task wrapper object.
            </summary>
            <param name="generateTask">This is essentially the async method that does the actual work we want to wrap.</param>
            <param name="callback">An <see cref="T:System.AsyncCallback"/> delegate that will receive notification when the asynchronous operation completes.</param>
            <param name="state">A user-defined object that will be passed to the callback delegate.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Util.CancellableAsyncResultTaskWrapper.#ctor">
            <summary>
            Creates a new ICancellableAsyncResult task wrapper object.
            </summary>
        </member>
        <member name="T:Microsoft.Azure.Storage.Core.Util.CancellableAsyncResultTaskWrapper`1">
            <summary>
            This class is the same as CancellableAsyncResultTaskWrapper, except it's used to wrap operations that return a Task&lt;TResult&gt; (instead of just a Task).
            </summary>
            <typeparam name="TResult">The return type of the operation to wrap</typeparam>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Util.CancellableAsyncResultTaskWrapper`1.#ctor(System.Func{System.Threading.CancellationToken,System.Threading.Tasks.Task{`0}},System.AsyncCallback,System.Object)">
            <summary>
            Creates a new ICancellableAsyncResult Task&lt;TResult&gt; wrapper object.
            </summary>
            <param name="generateTask">This is essentially the async method that does the actual work we want to wrap.</param>
            <param name="callback">An <see cref="T:System.AsyncCallback"/> delegate that will receive notification when the asynchronous operation completes.</param>
            <param name="state">A user-defined object that will be passed to the callback delegate.</param>
        </member>
        <member name="T:Microsoft.Azure.Storage.Core.Util.CancellableOperationBase">
            <summary>
            Represents an operation that supports cancellation. Used by
            ICancellableAsyncResult implementations throughout the library.
            Also used by AsyncExtensions as a bridge between CancellationToken
            and the ICancellableAsyncResult returned by an APM method call.
            </summary>
        </member>
        <member name="T:Microsoft.Azure.Storage.Core.Util.NativeMD5">
            <summary>
            The class provides the helper functions to do FISMA compliant MD5.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Core.Util.NativeMD5.ProvRsaFull">
            <summary>
            Cryptographic service provider.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Core.Util.NativeMD5.CryptVerifyContext">
            <summary>
            Access to the private keys is not required and the user interface can be bypassed.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Core.Util.NativeMD5.CalgMD5">
            <summary>
            ALG_ID value that identifies the hash algorithm to use.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Core.Util.NativeMD5.HashVal">
            <summary>
            The hash value or message hash for the hash object specified by hashHandle. 
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Core.Util.NativeMD5.hashHandle">
            <summary>
            The address to which the function copies a handle to the new hash object. Has to be released by calling the CryptDestroyHash function after we are finished using the hash object.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Core.Util.NativeMD5.hashProv">
            <summary>
            A handle to a CSP created by a call to CryptAcquireContext.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Core.Util.NativeMD5.disposed">
            <summary>
            Whether this object has been torn down or not.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Util.NativeMD5.#ctor">
            <summary>
            Initializes a new instance of NativeMD5.
            </summary> 
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Util.NativeMD5.Finalize">
            <summary>
            Finalizes an instance of the NativeMD5 class, unhooking it from all events.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Util.NativeMD5.Initialize">
            <summary>
            Initializes an implementation of the NativeMD5 class.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Util.NativeMD5.HashCore(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Routes data written to the object into the hash algorithm for computing the hash.
            </summary>
            <param name="array">The input to compute the hash code for.</param>
            <param name="offset">The offset into the byte array from which to begin using data.</param>
            <param name="dataLen">The number of bytes in the byte array to use as data.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Util.NativeMD5.HashFinal">
            <summary>
            Finalizes the hash computation after the last data is processed by the cryptographic stream object.
            </summary>
            <returns>The computed hash code.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Util.NativeMD5.Dispose(System.Boolean)">
            <summary>
            Releases the unmanaged resources used by the NativeMD5.
            </summary>
            <param name="disposing">true to release both managed and unmanaged resources; false to release only unmanaged resources.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Util.NativeMD5.ValidateReturnCode(System.Boolean)">
            <summary>
            Validates the status returned by all the crypto functions and throws exception per the return code.
            </summary>
            <param name="status">The boolean status returned by the crypto functions.</param>
        </member>
        <member name="T:Microsoft.Azure.Storage.Core.Util.StorageAsyncResult`1">
            <summary>
            Represents the async result returned by operations that do not directly
            call into the Executor.
            </summary>
            <typeparam name="T">Async operation's result type</typeparam>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Util.StorageAsyncResult`1.#ctor(System.AsyncCallback,System.Object)">
            <summary>
            Initializes a new instance of the StorageAsyncResult class.
            </summary>
            <param name="callback">The callback method to be used on completion.</param>
            <param name="state">The state for the callback.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Util.StorageAsyncResult`1.OnComplete(System.Exception)">
            <summary>
            Called on completion of the async operation to notify the user
            </summary>
            <param name="exception">Exception that was caught by the caller.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Util.StorageAsyncResult`1.End">
            <summary>
            Blocks the calling thread until the async operation is completed and throws
            any stored exceptions.
            </summary>
        </member>
        <member name="T:Microsoft.Azure.Storage.Core.Util.StorageCommandAsyncResult">
            <summary>
            Represents the async result returned by a storage command.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Core.Util.StorageCommandAsyncResult.userCallback">
            <summary>
            The callback provided by the user.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Core.Util.StorageCommandAsyncResult.userState">
            <summary>
            The state for the callback.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Core.Util.StorageCommandAsyncResult.isCompleted">
            <summary>
            Indicates whether a task is completed.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Core.Util.StorageCommandAsyncResult.completedSynchronously">
            <summary>
            Indicates whether task completed synchronously.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Core.Util.StorageCommandAsyncResult.asyncWaitEvent">
            <summary>
            The event for blocking on this task's completion.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Util.StorageCommandAsyncResult.#ctor(System.AsyncCallback,System.Object)">
            <summary>
            Initializes a new instance of the StorageCommandAsyncResult class.
            </summary>
            <param name="callback">The callback method to be used on completion.</param>
            <param name="state">The state for the callback.</param>
        </member>
        <member name="P:Microsoft.Azure.Storage.Core.Util.StorageCommandAsyncResult.AsyncState">
            <summary>
            Gets A user-defined object that contains information about the asynchronous operation.
            </summary>
        </member>
        <member name="P:Microsoft.Azure.Storage.Core.Util.StorageCommandAsyncResult.AsyncWaitHandle">
            <summary>
             Gets a System.Threading.WaitHandle that is used to wait for an asynchronous operation to complete.
            </summary>
        </member>
        <member name="P:Microsoft.Azure.Storage.Core.Util.StorageCommandAsyncResult.CompletedSynchronously">
            <summary>
            Gets a value indicating whether the asynchronous operation completed synchronously.
            </summary>
        </member>
        <member name="P:Microsoft.Azure.Storage.Core.Util.StorageCommandAsyncResult.IsCompleted">
            <summary>
            Gets a value indicating whether the asynchronous operation has completed.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Util.StorageCommandAsyncResult.Dispose">
            <summary>
            We implement the dispose only to allow the explicit closing of the event.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Util.StorageCommandAsyncResult.Dispose(System.Boolean)">
            <summary>
            Releases unmanaged and - optionally - managed resources.
            </summary>
            <param name="disposing">Set to <c>true</c> to release both managed and unmanaged resources; <c>false</c> to release only unmanaged resources.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Util.StorageCommandAsyncResult.LazyCreateWaitHandle">
            <summary>
            Provides the lazy initialization of the WaitHandle (based on Joe Duffy's blog).
            </summary>
            <returns>The WaitHandle to use for waiting on completion.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Util.StorageCommandAsyncResult.OnComplete">
            <summary>
            Called on completion of the async operation to notify the user
            (Based on Joe Duffy's lockless design).
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Util.StorageCommandAsyncResult.End">
            <summary>
            Blocks the calling thread until the async operation is completed.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Util.StorageCommandAsyncResult.UpdateCompletedSynchronously(System.Boolean)">
            <summary>
            Updates the CompletedSynchronously flag with another asynchronous operation result.
            </summary>
            <param name="lastOperationCompletedSynchronously">Set to <c>true</c> if the last operation was completed synchronously; <c>false</c> if it was completed asynchronously.</param>
        </member>
        <member name="T:Microsoft.Azure.Storage.Core.Util.AsyncSemaphoreAsync">
            <summary>
            This class provides asynchronous semaphore functionality (based on Stephen Toub's blog https://blogs.msdn.microsoft.com/pfxteam/2012/02/12/building-async-coordination-primitives-part-5-asyncsemaphore/).
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Util.AuthenticationUtility.GetPreferredDateHeaderValue(System.Net.Http.HttpRequestMessage)">
            <summary>
            Gets the value of the x-ms-date or Date header.
            </summary>
            <param name="request">The request where the value is read from.</param>
            <returns>The value of the x-ms-date or Date header.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Util.AuthenticationUtility.AppendCanonicalizedContentLengthHeader(Microsoft.Azure.Storage.Core.CanonicalizedString,System.Net.Http.HttpRequestMessage)">
            <summary>
            Appends the value of the Content-Length header to the specified canonicalized string.
            </summary>
            <param name="canonicalizedString">The canonicalized string where the value is appended.</param>
            <param name="request">The request where the value is read from.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Util.AuthenticationUtility.AppendCanonicalizedDateHeader(Microsoft.Azure.Storage.Core.CanonicalizedString,System.Net.Http.HttpRequestMessage,System.Boolean)">
            <summary>
            Appends the value of the Date header (or, optionally, the x-ms-date header) to the specified canonicalized string.
            </summary>
            <param name="canonicalizedString">The canonicalized string where the value is appended.</param>
            <param name="request">The request where the value is read from.</param>
            <param name="allowMicrosoftDateHeader">true if the value of the x-ms-date header can be used and is preferred; otherwise, false.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Util.AuthenticationUtility.AppendCanonicalizedCustomHeaders(Microsoft.Azure.Storage.Core.CanonicalizedString,System.Net.Http.HttpRequestMessage)">
            <summary>
            Appends the values of the x-ms-* headers to the specified canonicalized string.
            </summary>
            <param name="canonicalizedString">The canonicalized string where the values are appended.</param>
            <param name="request">The request where the values are read from.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Util.AuthenticationUtility.GetCanonicalizedHeaderValue(System.Nullable{System.DateTimeOffset})">
            <summary>
            Gets the canonicalized header value to use for the specified date/time or <c>null</c> if it does not have a value.
            </summary>
            <param name="value">The date/time.</param>
            <returns>The canonicalized header value to use for the specified date/time or <c>null</c> if it does not have a value.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Util.AuthenticationUtility.GetAbsolutePathWithoutSecondarySuffix(System.Uri,System.String)">
            <summary>
            In case of path style, this method will strip off -secondary from absolute path and replace it with account name.
            </summary>
            <param name="uri">The resource URI.</param>
            <param name="accountName">The name of the storage account.</param>
            <returns>Absolute path with no -secondary suffix.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Util.AuthenticationUtility.GetCanonicalizedResourceString(System.Uri,System.String,System.Boolean)">
            <summary>
            Gets the canonicalized resource string for the specified URI.
            </summary>
            <param name="uri">The resource URI.</param>
            <param name="accountName">The name of the storage account.</param>
            <param name="isSharedKeyLiteOrTableService">true when using the Shared Key Lite authentication scheme or the Table service; otherwise, false.</param>
            <returns>The canonicalized resource string.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Util.CommonUtility.GetListingLocationMode(Microsoft.Azure.Storage.IContinuationToken)">
            <summary>
            Determines which location can the listing command target by looking at the
            continuation token.
            </summary>
            <param name="token">Continuation token</param>
            <returns>Location mode</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Util.CommonUtility.MaxTimeSpan(System.TimeSpan,System.TimeSpan)">
            <summary>
            Returns the larger of two time spans.
            </summary>
            <param name="val1">The first of two time spans to compare.</param>
            <param name="val2">The second of two time spans to compare.</param>
            <returns>Parameter <paramref name="val1"/> or <paramref name="val2"/>, whichever is larger.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Util.CommonUtility.GetFirstHeaderValue``1(System.Collections.Generic.IEnumerable{``0})">
            <summary>
            Gets the first header value or <c>null</c> if no header values exist.
            </summary>
            <typeparam name="T">The type of header objects contained in the enumerable.</typeparam>
            <param name="headerValues">An enumerable that contains header values.</param>
            <returns>The first header value or <c>null</c> if no header values exist.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Util.CommonUtility.AssertNotNullOrEmpty(System.String,System.String)">
            <summary>
            Throws an exception if the string is empty or <c>null</c>.
            </summary>
            <param name="paramName">The name of the parameter.</param>
            <param name="value">The value of the parameter.</param>
            <exception cref="T:System.ArgumentException">Thrown if value is empty.</exception>
            <exception cref="T:System.ArgumentNullException">Thrown if value is null.</exception>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Util.CommonUtility.AssertNotNull(System.String,System.Object)">
            <summary>
            Throw an exception if the value is null.
            </summary>
            <param name="paramName">The name of the parameter.</param>
            <param name="value">The value of the parameter.</param>
            <exception cref="T:System.ArgumentNullException">Thrown if value is null.</exception>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Util.CommonUtility.ArgumentOutOfRange(System.String,System.Object)">
            <summary>
            Throw an exception indicating argument is out of range.
            </summary>
            <param name="paramName">The name of the parameter.</param>
            <param name="value">The value of the parameter.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Util.CommonUtility.AssertInBounds``1(System.String,``0,``0,``0)">
            <summary>
            Throw an exception if the argument is out of bounds.
            </summary>
            <typeparam name="T">The type of the value.</typeparam>
            <param name="paramName">The name of the parameter.</param>
            <param name="val">The value of the parameter.</param>
            <param name="min">The minimum value for the parameter.</param>
            <param name="max">The maximum value for the parameter.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Util.CommonUtility.AssertInBounds``1(System.String,``0,``0)">
            <summary>
            Throw an exception if the argument is out of bounds.
            </summary>
            <typeparam name="T">The type of the value.</typeparam>
            <param name="paramName">The name of the parameter.</param>
            <param name="val">The value of the parameter.</param>
            <param name="min">The minimum value for the parameter.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Util.CommonUtility.CheckStringParameter(System.String,System.Boolean,System.String,System.Int32)">
            <summary>
            Combines AssertNotNullOrEmpty and AssertInBounds for convenience.
            </summary>
            <param name="paramName">The name of the parameter.</param>
            <param name="canBeNullOrEmpty">Turns on or off null/empty checking.</param>
            <param name="value">The value of the parameter.</param>
            <param name="maxSize">The maximum size of value.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Util.CommonUtility.RoundUpToSeconds(System.TimeSpan)">
            <summary>
            Rounds up to seconds.
            </summary>
            <param name="timeSpan">The time span.</param>
            <returns>The time rounded to seconds.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Util.CommonUtility.BinaryAppend(System.Byte[],System.Byte[])">
            <summary>
            Appends 2 byte arrays.
            </summary>
            <param name="arr1">First array.</param>
            <param name="arr2">Second array.</param>
            <returns>The result byte array.</returns>
        </member>
        <member name="F:Microsoft.Azure.Storage.Core.Util.CommonUtility.PathStylePorts">
            <summary>
            List of ports used for path style addressing.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Util.CommonUtility.UsePathStyleAddressing(System.Uri)">
            <summary>
            Determines if a URI requires path style addressing.
            </summary>
            <param name="uri">The URI to check.</param>
            <returns>Returns <c>true</c> if the Uri uses path style addressing; otherwise, <c>false</c>.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Util.CommonUtility.ReadElementAsString(System.String,System.Xml.XmlReader)">
            <summary>
            Read the value of an element in the XML.
            </summary>
            <param name="elementName">The name of the element whose value is retrieved.</param>
            <param name="reader">A reader that provides access to XML data.</param>
            <returns>A string representation of the element's value.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Util.CommonUtility.ReadElementAsStringAsync(System.String,System.Xml.XmlReader)">
            <summary>
            Read the value of an element in the XML.
            </summary>
            <param name="elementName">The name of the element whose value is retrieved.</param>
            <param name="reader">A reader that provides access to XML data.</param>
            <returns>A string representation of the element's value.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Util.CommonUtility.LazyEnumerable``1(System.Func{Microsoft.Azure.Storage.IContinuationToken,Microsoft.Azure.Storage.ResultSegment{``0}},System.Int64)">
            <summary>
            Returns an enumerable collection of results that is retrieved lazily.
            </summary>
            <typeparam name="T">The type of ResultSegment like Blob, Container, Queue and Table.</typeparam>
            <param name="segmentGenerator">The segment generator.</param>
            <param name="maxResults">>A non-negative integer value that indicates the maximum number of results to be returned 
            in the result segment, up to the per-operation limit of 5000.</param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Util.CounterEventAsync.Increment">
            <summary>
            Increments the counter by one and thus sets the state of the event to non-signaled, causing threads to block.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Util.CounterEventAsync.DecrementAsync">
            <summary>
            Decrements the counter by one. If the counter reaches zero, sets the state of the event to signaled, allowing one or more waiting threads to proceed.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Util.CounterEventAsync.WaitAsync">
            <summary>
            Blocks the current thread until the CounterEvent is set.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Util.Crc64.ComputeSlicedSafe(System.Byte[],System.Int32,System.UInt64)">
            <summary>
            Compute the CRC64 of the input data using the Azure Storage CRC64 polynomial.
            </summary>
            <param name="src">The source data on which to compute the CRC64.</param>
            <param name="size"></param>
            <param name="uCrc"></param>
            <returns></returns>
        </member>
        <member name="T:Microsoft.Azure.Storage.Core.Util.Crc64Wrapper">
            <summary>
            Wrapper class for CRC64.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Util.Crc64Wrapper.UpdateHash(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Calculates an on-going hash using the input byte array.
            </summary>
            <param name="input">The input array used for calculating the hash.</param>
            <param name="offset">The offset in the input buffer to calculate from.</param>
            <param name="count">The number of bytes to use from input.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Util.Crc64Wrapper.ComputeHash">
            <summary>
            Retrieves the string representation of the hash. (Completes the creation of the hash).
            </summary>
            <returns>String representation of the computed hash value.</returns>
        </member>
        <member name="T:Microsoft.Azure.Storage.Core.Util.CultureStringComparer">
            <summary>
            Represents a string comparison operation that uses specific case and culture-based rules.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Util.CultureStringComparer.#ctor(System.Globalization.CultureInfo,System.Boolean)">
            <summary>
            Creates a CultureStringComparer object that compares strings according to the rules of a specified culture.
            </summary>
            <param name="culture">A culture whose linguistic rules are used to perform a string comparison.</param>
            <param name="ignoreCase"><c>true</c> to specify that comparison operations be case-insensitive; <c>false</c> to specify that comparison operations be case-sensitive.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Util.CultureStringComparer.Compare(System.String,System.String)">
            <summary>
            Compares two strings and returns an indication of their relative sort order.
            </summary>
            <param name="x">A string to compare to y.</param>
            <param name="y">A string to compare to x.</param>
            <returns>A signed integer that indicates the relative values of x and y.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Util.CultureStringComparer.Equals(System.String,System.String)">
            <summary>
            Indicates whether two strings are equal.
            </summary>
            <param name="x">A string to compare to y.</param>
            <param name="y">A string to compare to x.</param>
            <returns><c>true</c> if x and y refer to the same object, or x and y are equal; otherwise, <c>false</c>.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Util.CultureStringComparer.GetHashCode(System.String)">
            <summary>
            Gets the hash code for the specified object.
            </summary>
            <param name="obj">An object.</param>
            <returns>A 32-bit signed hash code calculated from the value of the parameter.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Util.HttpResponseMessageUtils.GetHeaderSingleValueOrDefault(System.Net.Http.Headers.HttpHeaders,System.String)">
            <summary>
            Gets the first header value for a specified header or an empty string if it does not exist.
            </summary>
            <param name="headers">A collection of headers and their values.</param>
            <param name="name">The name of the header to return.</param>
            <returns>The first header value or an empty string if the header does not exist.</returns>
        </member>
        <member name="T:Microsoft.Azure.Storage.Core.Util.HttpWebUtility">
            <summary>
            Provides helper functions for http request/response processing. 
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Util.HttpWebUtility.ParseQueryString(System.String)">
            <summary>
            Parse the http query string.
            </summary>
            <param name="query">Http query string.</param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Util.HttpWebUtility.ConvertDateTimeToHttpString(System.DateTimeOffset)">
            <summary>
            Converts the DateTimeOffset object to an Http string of form: Mon, 28 Jan 2008 12:11:37 GMT.
            </summary>
            <param name="dateTime">The DateTimeOffset object to convert to an Http string.</param>
            <returns>String of form: Mon, 28 Jan 2008 12:11:37 GMT.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Util.HttpWebUtility.CombineHttpHeaderValues(System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            Combine all the header values in the IEnumerable to a single comma separated string.
            </summary>
            <param name="headerValues">An IEnumerable<string> object representing the header values.</string></param>
            <returns>A comma separated string of header values.</returns>
        </member>
        <member name="T:Microsoft.Azure.Storage.Core.Util.MD5Wrapper">
            <summary>
            Wrapper class for MD5.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Util.MD5Wrapper.UpdateHash(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Calculates an on-going hash using the input byte array.
            </summary>
            <param name="input">The input array used for calculating the hash.</param>
            <param name="offset">The offset in the input buffer to calculate from.</param>
            <param name="count">The number of bytes to use from input.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Util.MD5Wrapper.ComputeHash">
            <summary>
            Retrieves the string representation of the hash. (Completes the creation of the hash).
            </summary>
            <returns>String representation of the computed hash value.</returns>
        </member>
        <member name="T:Microsoft.Azure.Storage.Core.Util.NavigationHelper">
            <summary>
            Contains methods for dealing with navigation.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Core.Util.NavigationHelper.RootContainerName">
            <summary>
            The name of the root container.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Core.Util.NavigationHelper.Slash">
            <summary>
            Used in address parsing.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Core.Util.NavigationHelper.Dot">
            <summary>
            Used in address parsing.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Core.Util.NavigationHelper.SlashChar">
            <summary>
            Used in address parsing.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Core.Util.NavigationHelper.SlashAsSplitOptions">
            <summary>
            Used to split string on slash.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Core.Util.NavigationHelper.DotAsSplitOptions">
            <summary>
            Used to split hostname.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Util.NavigationHelper.GetContainerName(System.Uri,System.Nullable{System.Boolean})">
            <summary>
            Retrieves the container part of a storage Uri, or "$root" if the container is implicit.
            </summary>
            <param name="blobAddress">The blob address.</param>
            <param name="usePathStyleUris">If set to <c>true</c> use path style Uris.</param>
            <returns>Name of the container.</returns>
            <remarks>
            The trailing slash is always removed.
            <example>
            GetContainerName(new Uri("http://test.blob.core.windows.net/mycontainer/myfolder/myblob")) will return "mycontainer"
            GetContainerName(new Uri("http://test.blob.core.windows.net/mycontainer/")) will return "mycontainer"
            GetContainerName(new Uri("http://test.blob.core.windows.net/myblob")) will return "$root"
            GetContainerName(new Uri("http://test.blob.core.windows.net/")) will throw ArgumentException
            </example>
            </remarks>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Util.NavigationHelper.GetBlobName(System.Uri,System.Nullable{System.Boolean})">
            <summary>
            Retrieves the blob part of a storage Uri.
            </summary>
            <param name="blobAddress">The blob address.</param>
            <param name="usePathStyleUris">If set to <c>true</c> use path style Uris.</param>
            <returns>A string containing the name of the blob.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Util.NavigationHelper.GetShareName(System.Uri,System.Nullable{System.Boolean})">
            <summary>
            Retrieves the share part of a storage Uri, or "$root" if the share is implicit.
            </summary>
            <param name="fileAddress">The file address.</param>
            <param name="usePathStyleUris">If set to <c>true</c> use path style Uris.</param>
            <returns>Name of the share.</returns>
            <remarks>
            The trailing slash is always removed.
            <example>
            GetShareName(new Uri("http://test.file.core.windows.net/myshare/myfolder/myfile")) will return "myshare"
            GetShareName(new Uri("http://test.file.core.windows.net/myshare/")) will return "myshare"
            GetShareName(new Uri("http://test.file.core.windows.net/")) will throw ArgumentException
            </example>
            </remarks>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Util.NavigationHelper.GetFileName(System.Uri,System.Nullable{System.Boolean})">
            <summary>
            Retrieves the file part of a storage Uri.
            </summary>
            <param name="fileAddress">The file address.</param>
            <param name="usePathStyleUris">If set to <c>true</c> use path style Uris.</param>
            <returns>The name of the file.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Util.NavigationHelper.GetFileAndDirectoryName(System.Uri,System.Nullable{System.Boolean})">
            <summary>
            Retrieves the file and directory part of a storage Uri.
            </summary>
            <param name="fileAddress">The file address.</param>
            <param name="usePathStyleUris">If set to <c>true</c> use path style Uris.</param>
            <returns>The file name including directories.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Util.NavigationHelper.GetBlobParentNameAndAddress(Microsoft.Azure.Storage.StorageUri,System.String,System.Nullable{System.Boolean},System.String@,Microsoft.Azure.Storage.StorageUri@)">
            <summary>
            Retrieves the parent name from a storage Uri.
            </summary>
            <param name="blobAddress">The blob address.</param>
            <param name="delimiter">The delimiter.</param>
            <param name="usePathStyleUris">If set to <c>true</c> use path style Uris.</param>
            <param name="parentName">Name of the parent.</param>
            <param name="parentAddress">The parent URI.</param>
            <returns>The name of the parent.</returns>
            <remarks>
            Adds the trailing delimiter as the prefix returned by the storage REST api always contains the delimiter.
            </remarks>
            <example>
            GetBlobParentNameAndAddress(new Uri("http://test.blob.core.windows.net/mycontainer/myfolder/myblob", "/")) will return "/myfolder/"
            GetBlobParentNameAndAddress(new Uri("http://test.blob.core.windows.net/mycontainer/myfolder|myblob", "|") will return "/myfolder|"
            GetBlobParentNameAndAddress(new Uri("http://test.blob.core.windows.net/mycontainer/myblob", "/") will return ""
            GetBlobParentNameAndAddress(new Uri("http://test.blob.core.windows.net/mycontainer/", "/") will return ""
            </example>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Util.NavigationHelper.GetFileParentNameAndAddress(Microsoft.Azure.Storage.StorageUri,System.Nullable{System.Boolean},System.String@,Microsoft.Azure.Storage.StorageUri@)">
            <summary>
            Retrieves the parent name from a storage Uri.
            </summary>
            <param name="fileAddress">The file address.</param>
            <param name="usePathStyleUris">If set to <c>true</c> use path style Uris.</param>
            <param name="parentName">Name of the parent.</param>
            <param name="parentAddress">The parent URI.</param>
            <returns>The name of the parent.</returns>
            <remarks>
            Adds the trailing delimiter as the prefix returned by the storage REST api always contains the delimiter.
            </remarks>
            <example>
            GetFileParentNameAndAddress(new Uri("http://test.file.core.windows.net/myshare/myfolder/myfile", "/")) will return "myfolder"
            GetFileParentNameAndAddress(new Uri("http://test.file.core.windows.net/myshare/myfile", "/") will return ""
            GetFileParentNameAndAddress(new Uri("http://test.file.core.windows.net/myshare/", "/") will return ""
            </example>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Util.NavigationHelper.GetServiceClientBaseAddress(Microsoft.Azure.Storage.StorageUri,System.Nullable{System.Boolean})">
            <summary>
            Gets the service client base address.
            </summary>
            <param name="addressUri">The address Uri.</param>
            <param name="usePathStyleUris">The use path style Uris.</param>
            <returns>The base address of the client.</returns>
            <example>
            GetServiceClientBaseAddress("http://testaccount.blob.core.windows.net/testcontainer/blob1") 
            returns "http://testaccount.blob.core.windows.net"
            </example>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Util.NavigationHelper.GetServiceClientBaseAddress(System.Uri,System.Nullable{System.Boolean})">
            <summary>
            Gets the service client base address.
            </summary>
            <param name="addressUri">The address Uri.</param>
            <param name="usePathStyleUris">The use path style Uris.</param>
            <returns>The base address of the client.</returns>
            <example>
            GetServiceClientBaseAddress("http://testaccount.blob.core.windows.net/testcontainer/blob1") 
            returns "http://testaccount.blob.core.windows.net"
            </example>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Util.NavigationHelper.AppendPathToUri(Microsoft.Azure.Storage.StorageUri,System.String)">
            <summary>
            Appends a path to a list of URIs correctly using "/" as separator.
            </summary>
            <param name="uriList">The base URI.</param>
            <param name="relativeUri">The relative or absolute URI.</param>
            <returns>The list of appended URIs.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Util.NavigationHelper.AppendPathToUri(Microsoft.Azure.Storage.StorageUri,System.String,System.String)">
            <summary>
            Appends a path to a list of URIs correctly using "/" as separator.
            </summary>
            <param name="uriList">The base URI.</param>
            <param name="relativeUri">The relative or absolute URI.</param>
            <param name="sep">The separator.</param>
            <returns>The list of appended URIs.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Util.NavigationHelper.AppendPathToSingleUri(System.Uri,System.String)">
            <summary>
            Append a relative path to a URI, handling trailing slashes appropriately.
            </summary>
            <param name="uri">The base URI.</param>
            <param name="relativeUri">The relative or absolute URI.</param>
            <returns>The appended Uri.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Util.NavigationHelper.AppendPathToSingleUri(System.Uri,System.String,System.String)">
            <summary>
            Append a relative path to a URI, handling trailing slashes appropriately.
            </summary>
            <param name="uri">The base URI.</param>
            <param name="relativeUri">The relative or absolute URI.</param>
            <param name="sep">The separator.</param>
            <returns>The appended Uri.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Util.NavigationHelper.GetContainerNameFromContainerAddress(System.Uri,System.Nullable{System.Boolean})">
            <summary>
            Get container name from address for styles of paths
            Example: http://test.blob.core.windows.net/container/blob =&gt; container
            http://127.0.0.1:10000/test/container/blob =&gt; container.
            </summary>
            <param name="uri">The container Uri.</param>
            <param name="usePathStyleUris">If set to <c>true</c> use path style Uris.</param>
            <returns>The container name.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Util.NavigationHelper.GetQueueNameFromUri(System.Uri,System.Nullable{System.Boolean})">
            <summary>
            Similar to getting container name from Uri.
            </summary>
            <param name="uri">The queue Uri.</param>
            <param name="usePathStyleUris">If set to <c>true</c> use path style Uris.</param>
            <returns>The queue name.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Util.NavigationHelper.GetTableNameFromUri(System.Uri,System.Nullable{System.Boolean})">
            <summary>
            Extracts a table name from the table's Uri.
            </summary>
            <param name="uri">The queue Uri.</param>
            <param name="usePathStyleUris">If set to <c>true</c> use path style Uris.</param>
            <returns>The queue name.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Util.NavigationHelper.GetShareNameFromShareAddress(System.Uri,System.Nullable{System.Boolean})">
            <summary>
            Extracts a table name from the share's Uri.
            </summary>
            <param name="uri">The share Uri.</param>
            <param name="usePathStyleUris">If set to <c>true</c> use path style Uris.</param>
            <returns>The share name.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Util.NavigationHelper.GetContainerNameAndAddress(Microsoft.Azure.Storage.StorageUri,System.Nullable{System.Boolean},System.String@,Microsoft.Azure.Storage.StorageUri@)">
            <summary>
            Retrieve the container address and address.
            </summary>
            <param name="blobAddress">The blob address.</param>
            <param name="usePathStyleUris">True to use path style Uris.</param>
            <param name="containerName">Name of the container.</param>
            <param name="containerUri">The container URI.</param>
            <returns><c>true</c> when the container is an explicit container. <c>false</c>, otherwise.</returns> 
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Util.NavigationHelper.GetShareNameAndAddress(Microsoft.Azure.Storage.StorageUri,System.Nullable{System.Boolean},System.String@,Microsoft.Azure.Storage.StorageUri@)">
            <summary>
            Retrieve the share address and address.
            </summary>
            <param name="fileAddress">The file address.</param>
            <param name="usePathStyleUris">True to use path style Uris.</param>
            <param name="shareName">Name of the share.</param>
            <param name="shareUri">The share URI.</param>
            <returns><c>true</c> when the share is an explicit share. <c>false</c>, otherwise.</returns> 
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Util.NavigationHelper.GetContainerNameAndBlobName(System.Uri,System.Nullable{System.Boolean},System.String@,System.String@)">
            <summary>
            Retrieve the container name and the blob name from a blob address.
            </summary>
            <param name="blobAddress">The blob address.</param>
            <param name="usePathStyleUris">If set to <c>true</c> use path style Uris.</param>
            <param name="containerName">The resulting container name.</param>
            <param name="blobName">The resulting blob name.</param>
            <returns>A bool representing whether the blob is in an explicit container or not.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Util.NavigationHelper.GetShareNameAndFileName(System.Uri,System.Nullable{System.Boolean},System.String@,System.String@)">
            <summary>
            Retrieve the share name and the file or directory name from a file or directory address.
            </summary>
            <param name="fileAddress">The file address.</param>
            <param name="usePathStyleUris">If set to <c>true</c> use path style Uris.</param>
            <param name="shareName">The resulting share name.</param>
            <param name="fileName">The resulting file or directory name.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Util.NavigationHelper.ParseSnapshotTime(System.String)">
            <summary>
            Parses the snapshot time.
            </summary>
            <param name="snapshot">The snapshot time.</param>
            <returns>The parsed snapshot time.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Util.NavigationHelper.ParseBlobQueryAndVerify(Microsoft.Azure.Storage.StorageUri,Microsoft.Azure.Storage.Auth.StorageCredentials@,System.Nullable{System.DateTimeOffset}@)">
            <summary>
            Parse Uri for SAS (Shared access signature) information.
            </summary>
            <param name="address">The complete Uri.</param>
            <param name="parsedCredentials">The credentials to use.</param>
            <param name="parsedSnapshot">The parsed snapshot.</param>
            <returns>The blob URI without credentials or snapshot info</returns>
            <exception cref="T:System.ArgumentException">address</exception>
            <remarks>
            Validate that no other query parameters are passed in.
            Any SAS information will be recorded as corresponding credentials instance.
            If credentials is passed in and it does not match the SAS information found, an
            exception will be thrown.
            Otherwise a new client is created based on SAS information or as anonymous credentials.
            </remarks>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Util.NavigationHelper.ParseBlobQueryAndVerify(System.Uri,Microsoft.Azure.Storage.Auth.StorageCredentials@,System.Nullable{System.DateTimeOffset}@)">
            <summary>
            Parse Uri for SAS (Shared access signature) information.
            </summary>
            <param name="address">The complete Uri.</param>
            <param name="parsedCredentials">The credentials to use.</param>
            <param name="parsedSnapshot">The parsed snapshot.</param>
            <returns>The blob URI without credentials or snapshot info</returns>
            <exception cref="T:System.ArgumentException">address</exception>
            <remarks>
            Validate that no other query parameters are passed in.
            Any SAS information will be recorded as corresponding credentials instance.
            If credentials is passed in and it does not match the SAS information found, an
            exception will be thrown.
            Otherwise a new client is created based on SAS information or as anonymous credentials.
            </remarks>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Util.NavigationHelper.ParseFileQueryAndVerify(Microsoft.Azure.Storage.StorageUri,Microsoft.Azure.Storage.Auth.StorageCredentials@,System.Nullable{System.DateTimeOffset}@)">
            <summary>
            Parse Uri for SAS (Shared access signature) information.
            </summary>
            <param name="address">The complete Uri.</param>
            <param name="parsedCredentials">The credentials to use.</param>
            <param name="parsedShareSnapshot">The parsed share snapshot.</param>
            <returns>The file URI without credentials or snapshot info</returns>
            <exception cref="T:System.ArgumentException">address</exception>
            <remarks>
            Validate that no other query parameters are passed in.
            Any SAS information will be recorded as corresponding credentials instance.
            If credentials is passed in and it does not match the SAS information found, an
            exception will be thrown.
            Otherwise a new client is created based on SAS information or as anonymous credentials.
            </remarks>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Util.NavigationHelper.ParseFileQueryAndVerify(System.Uri,Microsoft.Azure.Storage.Auth.StorageCredentials@,System.Nullable{System.DateTimeOffset}@)">
            <summary>
            Parse Uri for SAS (Shared access signature) information.
            </summary>
            <param name="address">The complete Uri.</param>
            <param name="parsedCredentials">The credentials to use.</param>
            <param name="parsedShareSnapshot">The parsed share snapshot.</param>
            <returns>The file URI without credentials or snapshot info</returns>
            <exception cref="T:System.ArgumentException">address</exception>
            <remarks>
            Validate that no other query parameters are passed in.
            Any SAS information will be recorded as corresponding credentials instance.
            If credentials is passed in and it does not match the SAS information found, an
            exception will be thrown.
            Otherwise a new client is created based on SAS information or as anonymous credentials.
            </remarks>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Util.NavigationHelper.ParseQueueTableQueryAndVerify(Microsoft.Azure.Storage.StorageUri,Microsoft.Azure.Storage.Auth.StorageCredentials@)">
            <summary>
            Parse Uri for SAS (Shared access signature) information.
            </summary>
            <param name="address">The complete Uri.</param>
            <param name="parsedCredentials">The credentials to use.</param>
            <remarks>
            Validate that no other query parameters are passed in.
            Any SAS information will be recorded as corresponding credentials instance.
            If credentials is passed in and it does not match the SAS information found, an
            exception will be thrown.
            Otherwise a new client is created based on SAS information or as anonymous credentials.
            </remarks>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Util.NavigationHelper.ParseQueueTableQueryAndVerify(System.Uri,Microsoft.Azure.Storage.Auth.StorageCredentials@)">
            <summary>
            Parse Uri for SAS (Shared access signature) information.
            </summary>
            <param name="address">The complete Uri.</param>
            <param name="parsedCredentials">The credentials to use.</param>
            <remarks>
            Validate that no other query parameters are passed in.
            Any SAS information will be recorded as corresponding credentials instance.
            If credentials is passed in and it does not match the SAS information found, an
            exception will be thrown.
            Otherwise a new client is created based on SAS information or as anonymous credentials.
            </remarks>
        </member>
        <member name="T:Microsoft.Azure.Storage.Core.Util.PlatformAgnosticReflectionExtensions">
            <summary>
            Represents a canonicalized string used in authenticating a request against the azure service.
            </summary>
        </member>
        <member name="T:Microsoft.Azure.Storage.Core.Util.StorageProgress">
            <summary>
            Holds information about the progress data transfers for both request and response streams in a single operation.
            </summary>
            <remarks>
            ## Examples
            [!code-csharp[StorageProgress](~/azure-storage-net/Test/WindowsRuntime/Blob/BlobUploadDownloadTest.cs#sample_StorageProgress_NetCore "StorageProgress Sample")]
            </remarks>
        </member>
        <member name="P:Microsoft.Azure.Storage.Core.Util.StorageProgress.BytesTransferred">
            <summary>
            Progress in bytes of the request data transfer.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Util.StorageProgress.#ctor(System.Int64)">
            <summary>
            Creates a <see cref="T:Microsoft.Azure.Storage.Core.Util.StorageProgress"/> object.
            </summary>
            <param name="bytesTransferred">The progress value being reported.</param>
        </member>
        <member name="T:Microsoft.Azure.Storage.Core.Util.AggregatingProgressIncrementer">
            <summary>
            An accumulator for request and response data transfers.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Util.AggregatingProgressIncrementer.Report(System.Int64)">
            <summary>
            Increments the current value and reports it to the progress handler
            </summary>
            <param name="bytes"></param>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Util.AggregatingProgressIncrementer.Reset">
            <summary>
            Zeroes out the current accumulation, and reports it to the progress handler
            </summary>
        </member>
        <member name="P:Microsoft.Azure.Storage.Core.Util.AggregatingProgressIncrementer.None">
            <summary>
            Returns an instance that no-ops accumulation.
            </summary>
        </member>
        <member name="P:Microsoft.Azure.Storage.Core.Util.AggregatingProgressIncrementer.Current">
            <summary>
            Returns a StorageProgress instance representing the current progress value.
            </summary>
        </member>
        <member name="T:Microsoft.Azure.Storage.Core.Util.ProgressIncrementingStream">
            <summary>
            Wraps a stream, and reports position updates to a progress incrementer
            </summary>
        </member>
        <member name="T:Microsoft.Azure.Storage.Core.Util.StreamDescriptor">
            <summary>
            Provides properties to keep track of checksum hash / Length of a stream as it is being copied.
            </summary>
        </member>
        <member name="T:Microsoft.Azure.Storage.Core.Util.StreamExtensions">
            <summary>
            Provides stream helper methods that allow us to copy streams and measure the stream size.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Util.StreamExtensions.AsInputStream(System.IO.Stream)">
            <summary>
            Return input stream itself.
            </summary>
            <param name="stream">input stream</param>
            <returns>the input stream</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Util.StreamExtensions.Seek(System.IO.Stream,System.Int64)">
            <summary>
            Position the stream with offset from beginning.
            </summary>
            <param name="stream">input stream</param>
            <param name="offset">offset from beginning</param>
            <returns>stream position</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Util.StreamExtensions.WriteToSync``1(System.IO.Stream,System.IO.Stream,System.Nullable{System.Int64},System.Nullable{System.Int64},Microsoft.Azure.Storage.Shared.Protocol.ChecksumRequested,System.Boolean,Microsoft.Azure.Storage.Core.Executor.ExecutionState{``0},Microsoft.Azure.Storage.Core.Util.StreamDescriptor)">
            <summary>
            Reads synchronously the specified content of the stream and writes it to the given output stream.
            </summary>
            <param name="stream">The origin stream.</param>
            <param name="toStream">The destination stream.</param>    
            <param name="copyLength">Number of bytes to copy from source stream to destination stream. Cannot be passed with a value for maxLength.</param>
            <param name="maxLength">Maximum length of the stream to write.</param>        
            <param name="calculateChecksum">A value indicating whether the checksums should be calculated.</param>
            <param name="syncRead">A boolean indicating whether the write happens synchronously.</param>
            <param name="executionState">An object that stores state of the operation.</param>
            <param name="streamCopyState">State of the stream copy.</param>
            <exception cref="T:System.ArgumentOutOfRangeException">stream</exception>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Util.StreamExtensions.WrapWithByteCountingStream(System.IO.Stream,Microsoft.Azure.Storage.RequestResult,System.Boolean)">
            <summary>
            Associates a given stream to a given RequestResult such that the RequestResult byte counters are accurately updated as data is read or written.
            </summary>
            <param name="stream">A reference to the original stream</param>
            <param name="result">An object that represents the result of a physical request.</param>
            <param name="reverseCapture">A flag indicating that ingress/egress bytes should be capture in reverse.</param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Util.StreamExtensions.WriteToAsync``1(System.IO.Stream,System.IO.Stream,Microsoft.Azure.Storage.IBufferManager,System.Nullable{System.Int64},System.Nullable{System.Int64},Microsoft.Azure.Storage.Shared.Protocol.ChecksumRequested,Microsoft.Azure.Storage.Core.Executor.ExecutionState{``0},Microsoft.Azure.Storage.Core.Util.StreamDescriptor,System.Threading.CancellationToken,System.Action{Microsoft.Azure.Storage.Core.Executor.ExecutionState{``0}})">
            <summary>
            Asynchronously reads the entire content of the stream and writes it to the given output stream.
            </summary>
            <typeparam name="T">The result type of the ExecutionState</typeparam>
            <param name="stream">The origin stream.</param>
            <param name="toStream">The destination stream.</param>
            <param name="bufferManager">IBufferManager instance to use.</param>
            <param name="copyLength">Number of bytes to copy from source stream to destination stream. Cannot be passed with a value for maxLength.</param>
            <param name="maxLength">Maximum length of the source stream. Cannot be passed with a value for copyLength.</param>
            <param name="calculateChecksum">A value indicating whether the checksums should be calculated.</param>
            <param name="executionState">An object that stores state of the operation.</param>
            <param name="streamCopyState">State of the stream copy.</param>
            <param name="cancellationToken">The cancellation token for the operation.</param>
            <param name="completed">The action taken when the execution is completed.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Util.TaskExtensions.WithCancellation``1(System.Threading.Tasks.Task{``0},System.Threading.CancellationToken)">
            <summary>
             Extension method to add cancellation logic to non-cancellable operations.
            </summary>
            <param name="task">The <see cref="T:System.Threading.Tasks.Task"/> to enable cancellation on.</param>
            <param name="cancellationToken">the cancellation token which will be used to cancel the combined task </param>
            <remarks>Please refer to this post for more information: https://blogs.msdn.microsoft.com/pfxteam/2012/10/05/how-do-i-cancel-non-cancelable-async-operations/ </remarks>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Util.TaskExtensions.WithCancellation(System.Threading.Tasks.Task,System.Threading.CancellationToken)">
            <summary>
             Extension method to add cancellation logic to non-cancellable operations.
            </summary>
            <param name="task">The <see cref="T:System.Threading.Tasks.Task"/> to enable cancellation on.</param>
            <param name="cancellationToken">the cancellation token which will be used to cancel the combined task</param>
            <remarks>Please refer to this post for more information: https://blogs.msdn.microsoft.com/pfxteam/2012/10/05/how-do-i-cancel-non-cancelable-async-operations/ </remarks>
        </member>
        <member name="T:Microsoft.Azure.Storage.Core.Auth.ICanonicalizer">
            <summary>
            <para>Represents a canonicalizer that converts HTTP request data into a standard form appropriate for signing.</para>
            <para>For detailed information on how to authenticate a request, 
            see <see href="http://msdn.microsoft.com/en-us/library/Azure/dd179428.aspx">Authentication for the Microsoft Azure Storage Services</see>.</para>
            </summary>
        </member>
        <member name="P:Microsoft.Azure.Storage.Core.Auth.ICanonicalizer.AuthorizationScheme">
            <summary>
            Gets the authorization scheme used for canonicalization.
            </summary>
            <value>The authorization scheme used for canonicalization.</value>
            <seealso href="http://msdn.microsoft.com/en-us/library/Azure/dd179428.aspx">Authentication for the Microsoft Azure Storage Services</seealso>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Auth.ICanonicalizer.CanonicalizeHttpRequest(System.Net.Http.HttpRequestMessage,System.String)">
            <summary>
            Converts the specified HTTP request data into a standard form appropriate for signing.
            </summary>
            <param name="request">The HTTP request that needs to be signed.</param>
            <param name="accountName">The name of the storage account that the HTTP request will access.</param>
            <returns>The canonicalized string containing the HTTP request data in a standard form appropriate for signing.</returns>
            <seealso href="http://msdn.microsoft.com/en-us/library/Azure/dd179428.aspx">Authentication for the Microsoft Azure Storage Services</seealso>
        </member>
        <member name="T:Microsoft.Azure.Storage.Core.Auth.SharedAccessSignatureHelper">
            <summary>
            Contains helper methods for implementing shared access signatures.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Auth.SharedAccessSignatureHelper.GetDateTimeOrEmpty(System.Nullable{System.DateTimeOffset})">
            <summary>
            Converts the specified value to either a string representation or <see cref="F:System.String.Empty"/>.
            </summary>
            <param name="value">The value to convert.</param>
            <returns>A string representing the specified value.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Auth.SharedAccessSignatureHelper.GetDateTimeOrNull(System.Nullable{System.DateTimeOffset})">
            <summary>
            Converts the specified value to either a string representation or <c>null</c>.
            </summary>
            <param name="value">The value to convert.</param>
            <returns>A string representing the specified value.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Auth.SharedAccessSignatureHelper.GetProtocolString(System.Nullable{Microsoft.Azure.Storage.SharedAccessProtocol})">
            <summary>
            Converts the specified value to either a string representation or <c>null</c>.
            </summary>
            <param name="protocols">The protocols to convert</param>
            <returns>A string representing the specified value.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Auth.SharedAccessSignatureHelper.AddEscapedIfNotNull(Microsoft.Azure.Storage.Core.UriQueryBuilder,System.String,System.String)">
            <summary>
            Escapes and adds the specified name/value pair to the query builder if it is not null.
            </summary>
            <param name="builder">The builder to add the value to.</param>
            <param name="name">The name of the pair.</param>
            <param name="value">The value to be escaped.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Auth.SharedAccessSignatureHelper.ParseQuery(System.Collections.Generic.IDictionary{System.String,System.String})">
            <summary>
            Parses the query.
            </summary>
            <param name="queryParameters">The query parameters.</param>
        </member>
        <member name="T:Microsoft.Azure.Storage.Core.Auth.SharedKeyCanonicalizer">
            <summary>
            Represents a canonicalizer that converts HTTP request data into a standard form appropriate for signing via 
            the Shared Key authentication scheme for the Blob or Queue service.
            </summary>
            <seealso href="http://msdn.microsoft.com/en-us/library/Azure/dd179428.aspx">Authentication for the Microsoft Azure Storage Services</seealso>
        </member>
        <member name="P:Microsoft.Azure.Storage.Core.Auth.SharedKeyCanonicalizer.Instance">
            <summary>
            Gets a static instance of the <see cref="T:Microsoft.Azure.Storage.Core.Auth.SharedKeyCanonicalizer"/> object.
            </summary>
            <value>The static instance of the class.</value>
            <seealso href="http://msdn.microsoft.com/en-us/library/Azure/dd179428.aspx">Authentication for the Microsoft Azure Storage Services</seealso>
        </member>
        <member name="P:Microsoft.Azure.Storage.Core.Auth.SharedKeyCanonicalizer.AuthorizationScheme">
            <summary>
            Gets the authorization scheme used for canonicalization.
            </summary>
            <value>The authorization scheme used for canonicalization.</value>
            <seealso href="http://msdn.microsoft.com/en-us/library/Azure/dd179428.aspx">Authentication for the Microsoft Azure Storage Services</seealso>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Auth.SharedKeyCanonicalizer.CanonicalizeHttpRequest(System.Net.Http.HttpRequestMessage,System.String)">
            <summary>
            Converts the specified HTTP request data into a standard form appropriate for signing.
            </summary>
            <param name="request">The HTTP request that needs to be signed.</param>
            <param name="accountName">The name of the storage account that the HTTP request will access.</param>
            <returns>The canonicalized string containing the HTTP request data in a standard form appropriate for signing.</returns>
            <seealso href="http://msdn.microsoft.com/en-us/library/Azure/dd179428.aspx">Authentication for the Microsoft Azure Storage Services</seealso>
        </member>
        <member name="T:Microsoft.Azure.Storage.Core.Auth.SharedKeyLiteCanonicalizer">
            <summary>
            Represents a canonicalizer that converts HTTP request data into a standard form appropriate for signing via 
            the Shared Key Lite authentication scheme for the Blob or Queue service.
            </summary>
            <seealso href="http://msdn.microsoft.com/en-us/library/Azure/dd179428.aspx">Authentication for the Microsoft Azure Storage Services</seealso>
        </member>
        <member name="P:Microsoft.Azure.Storage.Core.Auth.SharedKeyLiteCanonicalizer.Instance">
            <summary>
            Gets a static instance of the <see cref="T:Microsoft.Azure.Storage.Core.Auth.SharedKeyLiteCanonicalizer"/> object.
            </summary>
            <value>The static instance of the class.</value>
            <seealso href="http://msdn.microsoft.com/en-us/library/Azure/dd179428.aspx">Authentication for the Microsoft Azure Storage Services</seealso>
        </member>
        <member name="P:Microsoft.Azure.Storage.Core.Auth.SharedKeyLiteCanonicalizer.AuthorizationScheme">
            <summary>
            Gets the authorization scheme used for canonicalization.
            </summary>
            <value>The authorization scheme used for canonicalization.</value>
            <seealso href="http://msdn.microsoft.com/en-us/library/Azure/dd179428.aspx">Authentication for the Microsoft Azure Storage Services</seealso>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Auth.SharedKeyLiteCanonicalizer.CanonicalizeHttpRequest(System.Net.Http.HttpRequestMessage,System.String)">
            <summary>
            Converts the specified HTTP request data into a standard form appropriate for signing.
            </summary>
            <param name="request">The HTTP request that needs to be signed.</param>
            <param name="accountName">The name of the storage account that the HTTP request will access.</param>
            <returns>The canonicalized string containing the HTTP request data in a standard form appropriate for signing.</returns>
            <seealso href="http://msdn.microsoft.com/en-us/library/Azure/dd179428.aspx">Authentication for the Microsoft Azure Storage Services</seealso>
        </member>
        <member name="T:Microsoft.Azure.Storage.Core.CanonicalizedString">
            <summary>
            Represents a canonicalized string used in authenticating a request against the azure service.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Core.CanonicalizedString.canonicalizedString">
            <summary>
            Stores the internal <see cref="T:System.Text.StringBuilder"/> that holds the canonicalized string.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.CanonicalizedString.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Azure.Storage.Core.CanonicalizedString"/> class.
            </summary>
            <param name="initialElement">The first canonicalized element to start the string with.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.CanonicalizedString.#ctor(System.String,System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Azure.Storage.Core.CanonicalizedString"/> class.
            </summary>
            <param name="initialElement">The first canonicalized element to start the string with.</param>
            <param name="capacity">The starting size of the string.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.CanonicalizedString.AppendCanonicalizedElement(System.String)">
            <summary>
            Append additional canonicalized element to the string.
            </summary>
            <param name="element">An additional canonicalized element to append to the string.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.CanonicalizedString.ToString">
            <summary>
            Converts the value of this instance to a string.
            </summary>
            <returns>A string whose value is the same as this instance.</returns>
        </member>
        <member name="T:Microsoft.Azure.Storage.Core.MultiBufferMemoryStream">
            <summary>
            Creates a multi-buffer stream whose backing store is memory.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Core.MultiBufferMemoryStream.DefaultSmallBufferSize">
            <summary>
            The default small buffer size.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Core.MultiBufferMemoryStream.bufferSize">
            <summary>
            The size of each buffer.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Core.MultiBufferMemoryStream.bufferBlocks">
            <summary>
            The underlying buffer blocks for the stream.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Core.MultiBufferMemoryStream.length">
            <summary>
            The currently used length.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Core.MultiBufferMemoryStream.capacity">
            <summary>
            The total capacity of the stream.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Core.MultiBufferMemoryStream.position">
            <summary>
            The current position.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Core.MultiBufferMemoryStream.bufferManager">
            <summary>
            A reference to the IBufferManager for the stream to use to acquire and return buffers.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.MultiBufferMemoryStream.#ctor(Microsoft.Azure.Storage.IBufferManager,System.Int32)">
            <summary>
             Initializes a new instance of the <see cref="T:Microsoft.Azure.Storage.Core.MultiBufferMemoryStream"/> class with the specified buffer manager.
            </summary>
            <param name="bufferManager">The <see cref="T:Microsoft.Azure.Storage.IBufferManager"/> to use to acquire and return buffers for the stream. May be <c>null</c>.</param>
            <param name="bufferSize">The buffer size to use for each block. The default size is 64 KB. Note that this parameter is disregarded when an <see cref="T:Microsoft.Azure.Storage.IBufferManager"/> is specified.</param>
        </member>
        <member name="P:Microsoft.Azure.Storage.Core.MultiBufferMemoryStream.CanRead">
            <summary>
            Gets a value indicating whether the current stream supports reading.
            </summary>
            <value><c>true</c> if the stream supports reading; otherwise, <c>false</c>.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.Core.MultiBufferMemoryStream.CanSeek">
            <summary>
            Gets a value indicating whether the current stream supports seeking.
            </summary>
            <value><c>true</c> if the stream supports seeking; otherwise, <c>false</c>.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.Core.MultiBufferMemoryStream.CanWrite">
            <summary>
            Gets a value indicating whether the current stream supports writing.
            </summary>
            <value><c>true</c> if the stream supports writing; otherwise, <c>false</c>.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.Core.MultiBufferMemoryStream.Length">
            <summary>
            Gets the length in bytes of the stream.
            </summary>
            <returns>A long value representing the length of the stream in bytes.</returns>
        </member>
        <member name="P:Microsoft.Azure.Storage.Core.MultiBufferMemoryStream.Position">
            <summary>
            Gets or sets the position within the current stream.
            </summary>
            <returns>The current position within the stream.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.MultiBufferMemoryStream.Read(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Reads a block of bytes from the current stream and writes the data to a buffer.
            </summary>
            <param name="buffer">When this method returns, the buffer contains the specified byte array with the values between offset and (offset + count - 1) replaced by the bytes read from the current source.</param>
            <param name="offset">The zero-based byte offset in buffer at which to begin storing the data read from the current stream.</param>
            <param name="count">The maximum number of bytes to be read.</param>
            <returns>The total number of bytes read into the buffer. This can be less than the number of bytes requested if that many bytes are not currently available, or zero if the end of the stream has been reached.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.MultiBufferMemoryStream.BeginRead(System.Byte[],System.Int32,System.Int32,System.AsyncCallback,System.Object)">
            <summary>
            Begins an asynchronous read operation.
            </summary>
            <param name="buffer">When this method returns, the buffer contains the specified byte array with the values between offset and (offset + count - 1) replaced by the bytes read from the current source.</param>
            <param name="offset">The zero-based byte offset in buffer at which to begin storing the data read from the current stream.</param>
            <param name="count">The maximum number of bytes to be read.</param>
            <param name="callback">An optional asynchronous callback, to be called when the read is complete.</param>
            <param name="state">A user-provided object that distinguishes this particular asynchronous read request from other requests.</param>
            <returns>An IAsyncResult that represents the asynchronous read, which could still be pending.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.MultiBufferMemoryStream.EndRead(System.IAsyncResult)">
            <summary>
            Waits for the pending asynchronous read to complete.
            </summary>
            <param name="asyncResult">The reference to the pending asynchronous request to finish.</param>
            <returns>The total number of bytes read into the buffer. This can be less than the number of bytes requested if that many bytes are not currently available, or zero if the end of the stream has been reached.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.MultiBufferMemoryStream.Seek(System.Int64,System.IO.SeekOrigin)">
            <summary>
            Sets the position within the current stream.
            </summary>
            <param name="offset">A byte offset relative to the origin parameter.</param>
            <param name="origin">A value of type System.IO.SeekOrigin indicating the reference point used to obtain the new position.</param>
            <returns>The new position within the current stream.</returns>
            <exception cref="T:System.ArgumentException">Thrown if <paramref name="offset"/> is invalid for SeekOrigin.</exception>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.MultiBufferMemoryStream.SetLength(System.Int64)">
            <summary>
            Sets the length of the current stream.
            </summary>
            <param name="value">The desired length of the current stream in bytes.</param>
            <exception cref="T:System.ArgumentOutOfRangeException">If the <paramref name="value"/> is negative.</exception>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.MultiBufferMemoryStream.Write(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Writes a block of bytes to the current stream using data read from a buffer.
            </summary>
            <param name="buffer">The buffer to write data from.</param>
            <param name="offset">The zero-based byte offset in buffer at which to begin copying bytes to the current stream.</param>
            <param name="count">The number of bytes to write. </param>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.MultiBufferMemoryStream.BeginWrite(System.Byte[],System.Int32,System.Int32,System.AsyncCallback,System.Object)">
            <summary>
            Begins an asynchronous write operation.
            </summary>
            <param name="buffer">The buffer to write data from.</param>
            <param name="offset">The zero-based byte offset in buffer at which to begin copying bytes to the current stream.</param>
            <param name="count">The number of bytes to write.</param>
            <param name="callback">An optional asynchronous callback, to be called when the write is complete.</param>
            <param name="state">A user-provided object that distinguishes this particular asynchronous write request from other requests.</param>
            <returns>An IAsyncResult that represents the asynchronous write, which could still be pending.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.MultiBufferMemoryStream.EndWrite(System.IAsyncResult)">
            <summary>
            Ends an asynchronous write operation.
            </summary>
            <param name="asyncResult">The reference to the pending asynchronous request to finish.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.MultiBufferMemoryStream.Flush">
            <summary>
            Does not perform any operation, as the stream is an in-memory stream.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.MultiBufferMemoryStream.FastCopyToAsync(System.IO.Stream,System.Nullable{System.DateTime},System.Threading.CancellationToken)">
            <summary>
            Reads the bytes from the current stream and writes them to another stream. This method writes directly to the destination stream, 
            rather than copying the data into a temporary buffer.
            </summary>
            <param name="destination">The stream to which the contents of the current stream will be copied.</param>
            <param name="expiryTime">A DateTime indicating the expiry time.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.MultiBufferMemoryStream.FastCopyTo(System.IO.Stream,System.Nullable{System.DateTime})">
            <summary>
            Reads the bytes from the current stream and writes them to another stream. This method writes directly to the destination stream, 
            rather than copying the data into a temporary buffer.
            </summary>
            <param name="destination">The stream to which the contents of the current stream will be copied.</param>
            <param name="expiryTime">A DateTime indicating the expiry time.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.MultiBufferMemoryStream.BeginFastCopyTo(System.IO.Stream,System.Nullable{System.DateTime},System.AsyncCallback,System.Object)">
            <summary>
            Begins an asynchronous fast-copy operation.
            </summary>
            <param name="destination">The stream to which the contents of the current stream will be copied.</param>
            <param name="expiryTime">DateTime indicating the expiry time.</param>
            <param name="callback">An optional asynchronous callback, to be called when the copy is complete.</param>
            <param name="state">A user-provided object that distinguishes this particular asynchronous copy request from other requests.</param>
            <returns>An IAsyncResult that represents the asynchronous copy, which could still be pending.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.MultiBufferMemoryStream.FastCopyToInternal(Microsoft.Azure.Storage.Core.Util.StorageAsyncResult{Microsoft.Azure.Storage.Core.NullType})">
            <summary>
            Initiates a write operation for the next buffer in line.
            </summary>
            <param name="result">Internal StorageAsyncResult that represents the asynchronous copy.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.MultiBufferMemoryStream.FastCopyToCallback(System.IAsyncResult)">
            <summary>
            Callback method to be called when the corresponding write operation completes.
            </summary>
            <param name="asyncResult">The result of the asynchronous operation.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.MultiBufferMemoryStream.EndFastCopyTo(System.IAsyncResult)">
            <summary>
            Ends an asynchronous copy operation.
            </summary>
            <param name="asyncResult">The reference to the pending asynchronous request to finish.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.MultiBufferMemoryStream.ComputeMD5Hash">
            <summary>
            Computes the hash value for this stream.
            </summary>
            <returns>String representation of the computed hash value.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.MultiBufferMemoryStream.ComputeCRC64Hash">
            <summary>
            Computes the hash value for this stream.
            </summary>
            <returns>String representation of the computed hash value.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.MultiBufferMemoryStream.Reserve(System.Int64)">
            <summary>
            Ensures that the amount of bufferBlocks is greater than or equal to the required size. 
            Does not trim the size.
            </summary>
            <param name="requiredSize">The required size.</param>
            <exception cref="T:System.ArgumentOutOfRangeException">If the <paramref name="requiredSize"/> is negative.</exception>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.MultiBufferMemoryStream.AddBlock">
            <summary>
            Adds another block to the underlying bufferBlocks.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.MultiBufferMemoryStream.ReadInternal(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Copies the specified amount of data from internal buffers to the buffer and advances the position.
            </summary>
            <param name="buffer">An array of bytes. When this method returns, the buffer contains the specified byte array with the values 
                                between offset and (offset + count - 1) replaced by the bytes read from the current source.</param>
            <param name="offset">The zero-based byte offset in buffer at which to begin storing the data read from the current stream.</param>
            <param name="count">The maximum number of bytes to be read from the current stream.</param>
            <returns> The total number of bytes read into the buffer. This can be less than the number of bytes requested 
            if that many bytes are not currently available, or zero (0) if the end of the stream has been reached.
            </returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.MultiBufferMemoryStream.WriteInternal(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Writes a sequence of bytes to the current stream and advances the current position within this stream by the number of bytes written.
            (Requires the stream to be of sufficient size for writing).
            </summary>
            <param name="buffer">An array of bytes. This method copies count bytes from buffer to the current stream.</param>
            <param name="offset">The zero-based byte offset in buffer at which to begin copying bytes to the current stream.</param>
            <param name="count">The number of bytes to be written to the current stream.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.MultiBufferMemoryStream.AdvancePosition(System.Int32@,System.Int32@,System.Int32)">
            <summary>
            Advances the current position of the stream and adjust the offset and remainder based on the amount completed.
            </summary>
            <param name="offset">The current offset in the external buffer.</param>
            <param name="leftToProcess">The amount of data left to process.</param>
            <param name="amountProcessed">The amount of data processed.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.MultiBufferMemoryStream.AdvancePosition(System.Int64@,System.Int32)">
            <summary>
            Advances the current position of the stream and adjust the remainder based on the amount completed.
            </summary>
            <param name="leftToProcess">The amount of data left to process.</param>
            <param name="amountProcessed">The amount of data processed.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.MultiBufferMemoryStream.GetCurrentBlock">
            <summary>
            Calculate the block for the current position.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.MultiBufferMemoryStream.Dispose(System.Boolean)">
            <summary>
            Releases all resources used by the <see cref="T:Microsoft.Azure.Storage.Core.MultiBufferMemoryStream"/>.
            </summary>
            <param name="disposing">true to release both managed and unmanaged resources; false to release only unmanaged resources.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.NonCloseableStream.#ctor(System.IO.Stream)">
            <summary>
            Initializes a new instance of the NonCloseableStream class This stream ensures that the user stream
            is not closed even when the enclosing crypto stream is closed in order to flush the final block of data.
            </summary>
            <param name="wrappedStream">The stream to wrap.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.NonCloseableStream.BeginRead(System.Byte[],System.Int32,System.Int32,System.AsyncCallback,System.Object)">
            <summary>
            Begins an asynchronous read operation.
            </summary>
            <param name="buffer">When this method returns, the buffer contains the specified byte array with the values between offset and (offset + count - 1) replaced by the bytes read from the current source.</param>
            <param name="offset">The zero-based byte offset in buffer at which to begin storing the data read from the current stream.</param>
            <param name="count">The maximum number of bytes to be read.</param>
            <param name="callback">An optional asynchronous callback, to be called when the read is complete.</param>
            <param name="state">A user-provided object that distinguishes this particular asynchronous read request from other requests.</param>
            <returns>An IAsyncResult that represents the asynchronous read, which could still be pending.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.NonCloseableStream.EndRead(System.IAsyncResult)">
            <summary>
            Waits for the pending asynchronous read to complete.
            </summary>
            <param name="asyncResult">The reference to the pending asynchronous request to finish.</param>
            <returns>The total number of bytes read into the buffer. This can be less than the number of bytes requested if that many bytes are not currently available, or zero if the end of the stream has been reached.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.NonCloseableStream.ReadAsync(System.Byte[],System.Int32,System.Int32,System.Threading.CancellationToken)">
            <summary>
            Asynchronously reads a sequence of bytes from the current stream and advances the position within the stream by the number of bytes read.
            </summary>
            <param name="buffer">The buffer to write the data into.</param>
            <param name="offset">The byte offset in buffer at which to begin writing data from the stream.</param>
            <param name="count">The maximum number of bytes to read.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A task that represents the asynchronous read operation.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.NonCloseableStream.BeginWrite(System.Byte[],System.Int32,System.Int32,System.AsyncCallback,System.Object)">
            <summary>
            Begins an asynchronous write operation.
            </summary>
            <param name="buffer">The buffer to write data from.</param>
            <param name="offset">The zero-based byte offset in buffer at which to begin copying bytes to the current stream.</param>
            <param name="count">The number of bytes to write.</param>
            <param name="callback">An optional asynchronous callback, to be called when the write is complete.</param>
            <param name="state">A user-provided object that distinguishes this particular asynchronous write request from other requests.</param>
            <returns>An IAsyncResult that represents the asynchronous write, which could still be pending.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.NonCloseableStream.EndWrite(System.IAsyncResult)">
            <summary>
            Ends an asynchronous write operation.
            </summary>
            <param name="asyncResult">The reference to the pending asynchronous request to finish.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.NonCloseableStream.WriteAsync(System.Byte[],System.Int32,System.Int32,System.Threading.CancellationToken)">
            <summary>
            Asynchronously writes a sequence of bytes to the current stream and advances the current position within this stream by the number of bytes written.
            </summary>
            <param name="buffer">The buffer to write data from.</param>
            <param name="offset">The zero-based byte offset in buffer from which to begin copying bytes to the stream.</param>
            <param name="count">The maximum number of bytes to write.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A task that represents the asynchronous write operation.</returns>
        </member>
        <member name="T:Microsoft.Azure.Storage.Core.NullType">
            <summary>
            A NullTaskReturn type.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Core.NullType.Value">
            <summary>
            Represents a no-return from a task.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Core.NullType.ValueTask">
            <summary>
            Represents a no-return from a task.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.NullType.#ctor">
            <summary>
            Prevents a default instance of the <see cref="T:Microsoft.Azure.Storage.Core.NullType"/> class from being created.
            </summary>
        </member>
        <member name="T:Microsoft.Azure.Storage.Core.SasQueryBuilder">
            <summary>
            A convenience class for constructing SAS-specific URI query strings.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.SasQueryBuilder.#ctor(System.String)">
            <summary>
            Public SasQueryBuilder constructor.
            </summary>
            <param name="sasToken">The ASA token used to authenticate request.</param>
        </member>
        <member name="P:Microsoft.Azure.Storage.Core.SasQueryBuilder.RequireHttps">
            <summary>
            Returns True if any of the parameters specifies https:.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.SasQueryBuilder.Add(System.String,System.String)">
            <summary>
            Add the query string value with URI escaping.
            </summary>
            <param name="name">The query string name.</param>
            <param name="value">The query string value.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.SasQueryBuilder.AddToUri(System.Uri)">
            <summary>
            Adds a query parameter to a URI.
            </summary>
            <param name="uri">A <see cref="T:System.Uri"/> object containing the original URI, including any existing query parameters.</param>
            <returns>A <see cref="T:System.Uri"/> object with the new query parameter appended.</returns>
        </member>
        <member name="T:Microsoft.Azure.Storage.Core.SR">
            <summary>
            Provides a standard set of errors that could be thrown from the client library.
            </summary>
        </member>
        <member name="T:Microsoft.Azure.Storage.Core.SyncMemoryStream">
            <summary>
            This class provides APM Read/Write overrides for memory stream to improve performance.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.SyncMemoryStream.#ctor">
            <summary>
            Initializes a new instance of the SyncMemoryStream class with an expandable capacity initialized to zero.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.SyncMemoryStream.#ctor(System.Byte[])">
            <summary>
            Initializes a new non-resizable instance of the SyncMemoryStream class based on the specified byte array. 
            </summary>
            <param name="buffer">The array of unsigned bytes from which to create the current stream.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.SyncMemoryStream.#ctor(System.Byte[],System.Int32)">
            <summary>
            Initializes a new non-resizable instance of the SyncMemoryStream class based on the specified region (index) of a byte array. 
            </summary>
            <param name="buffer">The array of unsigned bytes from which to create the current stream.</param>
            <param name="index">The index into buffer at which the stream begins.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.SyncMemoryStream.#ctor(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Initializes a new non-resizable instance of the SyncMemoryStream class based on the specified region (index) of a byte array. 
            </summary>
            <param name="buffer">The array of unsigned bytes from which to create the current stream.</param>
            <param name="index">The index into buffer at which the stream begins.</param>
            <param name="count">The length of the stream in bytes.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.SyncMemoryStream.BeginRead(System.Byte[],System.Int32,System.Int32,System.AsyncCallback,System.Object)">
            <summary>
            Begins an asynchronous read operation.
            </summary>
            <param name="buffer">When this method returns, the buffer contains the specified byte array with the values between offset and (offset + count - 1) replaced by the bytes read from the current source.</param>
            <param name="offset">The zero-based byte offset in buffer at which to begin storing the data read from the current stream.</param>
            <param name="count">The maximum number of bytes to be read.</param>
            <param name="callback">An optional asynchronous callback, to be called when the read is complete.</param>
            <param name="state">A user-provided object that distinguishes this particular asynchronous read request from other requests.</param>
            <returns>An IAsyncResult that represents the asynchronous read, which could still be pending.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.SyncMemoryStream.EndRead(System.IAsyncResult)">
            <summary>
            Waits for the pending asynchronous read to complete.
            </summary>
            <param name="asyncResult">The reference to the pending asynchronous request to finish.</param>
            <returns>The total number of bytes read into the buffer. This can be less than the number of bytes requested if that many bytes are not currently available, or zero if the end of the stream has been reached.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.SyncMemoryStream.BeginWrite(System.Byte[],System.Int32,System.Int32,System.AsyncCallback,System.Object)">
            <summary>
            Begins an asynchronous write operation.
            </summary>
            <param name="buffer">The buffer to write data from.</param>
            <param name="offset">The zero-based byte offset in buffer at which to begin copying bytes to the current stream.</param>
            <param name="count">The number of bytes to write.</param>
            <param name="callback">An optional asynchronous callback, to be called when the write is complete.</param>
            <param name="state">A user-provided object that distinguishes this particular asynchronous write request from other requests.</param>
            <returns>An IAsyncResult that represents the asynchronous write, which could still be pending.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.SyncMemoryStream.EndWrite(System.IAsyncResult)">
            <summary>
            Ends an asynchronous write operation.
            </summary>
            <param name="asyncResult">The reference to the pending asynchronous request to finish.</param>
        </member>
        <member name="T:Microsoft.Azure.Storage.Core.UriQueryBuilder">
            <summary>
            A convenience class for constructing URI query strings.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.UriQueryBuilder.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Azure.Storage.Core.UriQueryBuilder"/> class.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.UriQueryBuilder.#ctor(Microsoft.Azure.Storage.Core.UriQueryBuilder)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Azure.Storage.Core.UriQueryBuilder"/> class that contains elements copied from the specified <see cref="T:Microsoft.Azure.Storage.Core.UriQueryBuilder"/>.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.Azure.Storage.Core.UriQueryBuilder"/> whose elements are copied to the new <see cref="T:Microsoft.Azure.Storage.Core.UriQueryBuilder"/>.</param>
        </member>
        <member name="P:Microsoft.Azure.Storage.Core.UriQueryBuilder.Parameters">
            <summary>
            Stores the query parameters.
            </summary>
        </member>
        <member name="P:Microsoft.Azure.Storage.Core.UriQueryBuilder.Item(System.String)">
            <summary>
            Gets the query string value associated with the given name.
            </summary>
            <param name="name">The query string name.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.UriQueryBuilder.Add(System.String,System.String)">
            <summary>
            Add the query string value with URI escaping.
            </summary>
            <param name="name">The query string name.</param>
            <param name="value">The query string value.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.UriQueryBuilder.AddRange(System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{System.String,System.String}})">
            <summary>
            Add multiple query string values with URI escaping.
            </summary>
            <param name="parameters">The set of query string name/value pairs</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.UriQueryBuilder.ContainsQueryStringName(System.String)">
            <summary>
            Determines whether the query string name exists in the query string.
            </summary>
            <param name="name">The query string name</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.UriQueryBuilder.ToString">
            <summary>
            Returns a <see cref="T:System.String"/> containing the URI.
            </summary>
            <returns>
            A <see cref="T:System.String"/> containing the URI.
            </returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.UriQueryBuilder.AddToUri(Microsoft.Azure.Storage.StorageUri)">
            <summary>
            Adds a query parameter to a URI.
            </summary>
            <param name="storageUri">A <see cref="T:Microsoft.Azure.Storage.StorageUri"/> containing the original URI, including any existing query parameters.</param>
            <returns>A <see cref="T:Microsoft.Azure.Storage.StorageUri"/> object with the new query parameter appended.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.UriQueryBuilder.AddToUri(System.Uri)">
            <summary>
            Adds a query parameter to a URI.
            </summary>
            <param name="uri">A <see cref="T:System.Uri"/> object containing the original URI, including any existing query parameters.</param>
            <returns>A <see cref="T:System.Uri"/> object with the new query parameter appended.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.UriQueryBuilder.AddToUriCore(System.Uri)">
            <summary>
            Adds a query parameter to a URI.
            </summary>
            <param name="uri">A <see cref="T:System.Uri"/> object containing the original URI, including any existing query parameters.</param>
            <returns>A <see cref="T:System.Uri"/> object with the new query parameter appended.</returns>
        </member>
        <member name="T:Microsoft.Azure.Storage.ICancellableAsyncResult">
            <summary>
            Represents the status of an asynchronous operation and provides support for cancellation.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.ICancellableAsyncResult.Cancel">
            <summary>
            Cancels the asynchronous operation.
            </summary>
        </member>
        <member name="T:Microsoft.Azure.Storage.Shared.Protocol.StorageService">
            <summary>
            Represents a storage service.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.StorageService.Blob">
            <summary>
            Blob service.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.StorageService.Queue">
            <summary>
            Queue Service.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.StorageService.Table">
            <summary>
            Table Service.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.StorageService.File">
            <summary>
            File Service.
            </summary>
        </member>
        <member name="T:Microsoft.Azure.Storage.Shared.Protocol.AccessPolicyResponseBase`1">
            <summary>
            Parses the response XML from an operation to set the access policy for a cloud object.
            </summary>
            <typeparam name="T">The policy type to be filled.</typeparam>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.AccessPolicyResponseBase`1.reader">
            <summary>
            The reader used for parsing. This field is reserved and should not be used.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.Shared.Protocol.AccessPolicyResponseBase`1.#ctor(System.IO.Stream)">
            <summary>
            Initializes a new instance of the AccessPolicyResponseBase class.
            </summary>
            <param name="stream">The stream to be parsed.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.Shared.Protocol.AccessPolicyResponseBase`1.Dispose">
            <summary>
            Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources. 
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.Shared.Protocol.AccessPolicyResponseBase`1.Dispose(System.Boolean)">
            <summary>
            Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources, and optional
            managed resources.
            </summary>
            <param name="disposing"><c>True</c> to release both managed and unmanaged resources; otherwise, <c>false</c>.</param>
        </member>
        <member name="P:Microsoft.Azure.Storage.Shared.Protocol.AccessPolicyResponseBase`1.AccessIdentifiers">
            <summary>
            Gets an enumerable collection of container-level access policy identifiers.
            </summary>
            <value>An enumerable collection of container-level access policy identifiers.</value>
        </member>
        <member name="M:Microsoft.Azure.Storage.Shared.Protocol.AccessPolicyResponseBase`1.ParseElement(System.Xml.Linq.XElement)">
            <summary>
            Parses the current element.
            </summary>
            <param name="accessPolicyElement">The shared access policy element to parse.</param>
            <returns>The shared access policy.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Shared.Protocol.AccessPolicyResponseBase`1.ParseAsync">
            <summary>
            Parses the response XML from a Set Container ACL operation to retrieve container-level access policy data.
            </summary>
            <returns>A list of enumerable key-value pairs.</returns>
        </member>
        <member name="T:Microsoft.Azure.Storage.Shared.Protocol.AccountProperties">
            <summary>
            Class representing a set of properties pertaining to a cloud storage account.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.Shared.Protocol.AccountProperties.#ctor">
            <summary>
            Initializes a new instance of the ServiceProperties class.
            </summary>
        </member>
        <member name="P:Microsoft.Azure.Storage.Shared.Protocol.AccountProperties.SkuName">
            <summary>
            Gets the account SKU type based on GeoReplication state.
            </summary>
            <value>"Standard_LRS", "Standard_ZRS", "Standard_GRS", "Standard_RAGRS", "Premium_LRS", or "Premium_ZRS"</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.Shared.Protocol.AccountProperties.AccountKind">
            <summary>
            Gets the account kind.
            </summary>
            <value>"Storage", "StorageV2", or "BlobStorage"</value>
        </member>
        <member name="M:Microsoft.Azure.Storage.Shared.Protocol.AccountProperties.FromHttpResponseHeaders(System.Net.Http.Headers.HttpResponseHeaders)">
            <summary>
            Constructs an <c>AccountProperties</c> object from a HttpResponseHeaders object received from the service.
            </summary>
            <param name="httpResponseHeaders">The HttpResponseHeaders object.</param>
            <returns>An <c>AccountProperties</c> object containing the properties in the HttpResponseHeaders.</returns>
        </member>
        <member name="P:Microsoft.Azure.Storage.Shared.Protocol.ChecksumOptions.DisableContentMD5Validation">
             <summary>
             Gets or sets a value to indicate that MD5 validation will be disabled when downloading blobs.
             </summary>
             <value>Use <c>true</c> to disable MD5 validation; <c>false</c> to enable MD5 validation. Default is <c>false</c>.</value>
             <remarks>
             When downloading a blob, if the value already exists on the blob, the Storage service 
             will include the MD5 hash of the entire blob as a header. This option controls 
             whether or not the Storage Client will validate that MD5 hash on download.
             See <see cref="P:Microsoft.Azure.Storage.Shared.Protocol.ChecksumOptions.StoreContentMD5"/> for more details.
             
            ## Examples
            [!code-csharp[Disable_Content_MD5_Validation_Sample](~/azure-storage-net/Test/ClassLibraryCommon/Blob/MD5FlagsTest.cs#sample_ChecksumOptions_DisableContentMD5Validation "Disable Content MD5 Validation Sample")]        
             </remarks>
        </member>
        <member name="P:Microsoft.Azure.Storage.Shared.Protocol.ChecksumOptions.StoreContentMD5">
            <summary>
            Gets or sets a value to indicate that an MD5 hash will be calculated and stored when uploading a blob.
            </summary>
            <value>Use <c>true</c> to calculate and store an MD5 hash when uploading a blob; otherwise, <c>false</c>. Defaults to <c>false</c>.</value>
            <remarks>This property is not supported for the <see cref="!:CloudAppendBlob"/> Append* APIs.
            The StoreBlobContentMD5 request option instructs the Storage Client to calculate the MD5 hash 
            of the blob content during an upload operation. This value is then stored on the 
            blob object as the Content-MD5 header. This option applies only to upload operations. 
            This is useful for validating the integrity of the blob upon later download, and 
            compatible with the Content-MD5 header as defined in the HTTP spec. If using 
            the Storage Client for later download, if the Content-MD5 header is present, 
            the MD5 hash of the content will be validated, unless "DisableContentMD5Validation" is set.
            Note that this value is not validated on the Azure Storage service on either upload or download of data; 
            it is merely stored and returned.
             ## Examples
             [!code-csharp[Store_Blob_Content_MD5_Sample](~/azure-storage-net/Test/ClassLibraryCommon/Blob/MD5FlagsTest.cs#sample_ChecksumOptions_StoreBlobContentMD5 "Store Blob Content MD5 Sample")] 
            </remarks>
        </member>
        <member name="P:Microsoft.Azure.Storage.Shared.Protocol.ChecksumOptions.UseTransactionalMD5">
            <summary>
            Gets or sets a value to calculate and send/validate content MD5 for transactions.
            </summary>
            <value>Use <c>true</c> to calculate and send/validate content MD5 for transactions; otherwise, <c>false</c>. Default is <c>false</c>.</value>
            <remarks>
            The UseTransactionalMD5 option instructs the Storage Client to calculate and validate 
            the MD5 hash of individual Storage REST operations. For a given REST operation, 
            if this value is set, both the Storage Client and the Storage service will calculate
            the MD5 hash of the transferred data, and will fail if the values do not match.
            This value is not persisted on the service or the client.
            This option applies to both upload and download operations.
            Note that HTTPS does a similar check during transit. If you are using HTTPS, 
            we recommend this feature be off.
             ## Examples
             [!code-csharp[Use_Transactional_MD5_Sample](~/azure-storage-net/Test/ClassLibraryCommon/Blob/MD5FlagsTest.cs#sample_ChecksumOptions_UseTransactionalMD5 "Use Transactional MD5 Sample")] 
            </remarks>
        </member>
        <member name="P:Microsoft.Azure.Storage.Shared.Protocol.ChecksumOptions.DisableContentCRC64Validation">
            <summary>
            Gets or sets a value to indicate that CRC64 validation will be disabled when downloading blobs.
            </summary>
            <value>Use <c>true</c> to disable CRC64 validation; <c>false</c> to enable CRC64 validation. Default is <c>false</c>.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.Shared.Protocol.ChecksumOptions.StoreContentCRC64">
            <summary>
            Gets or sets a value to indicate that an CRC64 hash will be calculated and stored when uploading a blob.
            </summary>
            <value>Use <c>true</c> to calculate and store an CRC64 hash when uploading a blob; otherwise, <c>false</c>. Defaults to <c>false</c>.</value>
            <remarks>This property is not supported for the <see cref="!:CloudAppendBlob"/> Append* APIs.
            The StoreBlobContentCRC64 request option instructs the Storage Client to calculate the CRC64 hash 
            of the blob content during an upload operation. This value is then stored on the 
            blob object as the Content-CRC64 header. This option applies only to upload operations. 
            This is useful for validating the integrity of the blob upon later download, and 
            compatible with the Content-CRC64 header as defined in the HTTP spec. If using 
            the Storage Client for later download, if the Content-CRC64 header is present, 
            the CRC64 hash of the content will be validated, unless "DisableContentCRC64Validation" is set.
            Note that this value is not validated on the Azure Storage service on either upload or download of data; 
            it is merely stored and returned.
            </remarks>
        </member>
        <member name="P:Microsoft.Azure.Storage.Shared.Protocol.ChecksumOptions.UseTransactionalCRC64">
            <summary>
            Gets or sets a value to calculate and send/validate content CRC64 for transactions.
            </summary>
            <value>Use <c>true</c> to calculate and send/validate content CRC64 for transactions; otherwise, <c>false</c>. Default is <c>false</c>.</value>
            <remarks>
            The UseTransactionalCRC64 option instructs the Storage Client to calculate and validate 
            the CRC64 hash of individual Storage REST operations. For a given REST operation, 
            if this value is set, both the Storage Client and the Storage service will calculate
            the CRC64 hash of the transferred data, and will fail if the values do not match.
            This value is not persisted on the service or the client.
            This option applies to both upload and download operations.
            Note that HTTPS does a similar check during transit. If you are using HTTPS, 
            we recommend this feature be off.
            </remarks>
        </member>
        <member name="T:Microsoft.Azure.Storage.Shared.Protocol.Constants">
            <summary>
            Contains storage constants.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.MaxParallelOperationThreadCount">
            <summary>
            Constant for the max value of ParallelOperationThreadCount for Block Blobs.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.MaxSharedAccessPolicyIdentifiers">
            <summary>
            Maximum number of shared access policy identifiers supported by server.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.DefaultWriteBlockSizeBytes">
            <summary>
            Default Write Block Size used by Blob stream.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.DefaultSubStreamBufferSize">
            <summary>
            Default read buffer size used by the SubStream class for Large Block Blob uploads.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.DefaultParallelDownloadRangeSizeBytes">
            <summary>
            Default range size when downloading a blob in parallel.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.MaxSingleUploadBlobSize">
            <summary>
            The maximum size of a blob before it must be separated into blocks.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.MaxBlockSize">
            <summary>
            The maximum size of a single block for Block Blobs.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.MaxAppendBlockSize">
            <summary>
            The maximum size of a single block for Append Blobs.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.MaxIdleTimeMs">
            <summary>
            The maximum allowed time between write calls to the stream for parallel download streams.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.MaxRangeGetContentMD5Size">
            <summary>
            The maximum size of a range get operation that returns content MD5.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.MaxRangeGetContentCRC64Size">
            <summary>
            The maximum size of a range get operation that returns content CRC64.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.MaxBlockNumber">
            <summary>
            The maximum number of blocks.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.MaxBlobSize">
            <summary>
            The maximum size of a blob with blocks.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.MinLargeBlockSize">
            <summary>
            The minimum size of a block for the large block upload strategy to be employed.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.MaxRetainedVersionsPerBlob">
            <summary>
            The maximum number of retained versions per blob.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.MaxSubOperationPerBatch">
            <summary>
            The maximum number of sub operations that may be part of a batch.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.MaxMaximumExecutionTime">
            <summary>
            Constant for the max value of MaximumExecutionTime.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.DefaultClientSideTimeout">
            <summary>
            Default client side timeout for all service clients.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.MaximumRetryBackoff">
            <summary>
            Maximum Retry Policy back-off
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.MaximumAllowedTimeout">
            <summary>
            Maximum allowed timeout for any request.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.DefaultNetworkTimeout">
            <summary>
            Default timeout applied to an individual network operations.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.MaximumAllowedRetentionDays">
            <summary>
            Maximum allowed value for Delete Retention Days.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.DefaultBufferSize">
            <summary>
            Default size of buffer for unknown sized requests.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.LogSourceName">
            <summary>
            Common name to be used for all loggers.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.PageSize">
            <summary>
            The size of a page in a PageBlob.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.KB">
            <summary>
            A constant representing a kilo-byte (Non-SI version).
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.MB">
            <summary>
            A constant representing a megabyte (Non-SI version).
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.GB">
            <summary>
            A constant representing a gigabyte (Non-SI version).
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.AES256">
            <summary>
            A constant representing the encryption algorithm for CPK encryption
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.CommittedBlocksElement">
            <summary>
            XML element for committed blocks.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.UncommittedBlocksElement">
            <summary>
            XML element for uncommitted blocks.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.BlockElement">
            <summary>
            XML element for blocks.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.NameElement">
            <summary>
            XML element for names.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.SizeElement">
            <summary>
            XML element for sizes.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.BlockListElement">
            <summary>
            XML element for block lists.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.MessagesElement">
            <summary>
            XML element for queue message lists.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.MessageElement">
            <summary>
            XML element for queue messages.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.MessageIdElement">
            <summary>
            XML element for message IDs.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.InsertionTimeElement">
            <summary>
            XML element for insertion times.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.ExpirationTimeElement">
            <summary>
            XML element for expiration times.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.PopReceiptElement">
            <summary>
            XML element for pop receipts.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.TimeNextVisibleElement">
            <summary>
            XML element for the time next visible fields.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.MessageTextElement">
            <summary>
            XML element for message texts.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.DequeueCountElement">
            <summary>
            XML element for dequeue counts.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.PageRangeElement">
            <summary>
            XML element for page ranges.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.ClearRangeElement">
            <summary>
            XML element for clear ranges.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.PageListElement">
            <summary>
            XML element for page list elements.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.StartElement">
            <summary>
            XML element for page range start elements.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.EndElement">
            <summary>
            XML element for page range end elements.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HandleIdElement">
            <summary> 
            XML element for handle id elements. 
            </summary> 
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.PathElement">
            <summary> 
            XML element for handle file/directory name elements. 
            </summary> 
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.ClientIpElement">
            <summary> 
            XML element for handle client IP elements. 
            </summary> 
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.OpenTimeElement">
            <summary> 
            XML element for handle opened time elements. 
            </summary> 
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.LastReconnectTimeElement">
            <summary> 
            Last time this handle was reconnected. 
            </summary> 
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.FileIdElement">
            <summary> 
            XML element for handle file id elements. 
            </summary> 
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.ParentIdElement">
            <summary> 
            XML element for handle parent id elements. 
            </summary> 
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.SessionIdElement">
            <summary> 
            XML element for handle session id elements. 
            </summary> 
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.DelimiterElement">
            <summary>
            XML element for delimiters.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.BlobPrefixElement">
            <summary>
            XML element for blob prefixes.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.CacheControlElement">
            <summary>
            XML element for content type fields.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.ContentTypeElement">
            <summary>
            XML element for content type fields.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.ContentEncodingElement">
            <summary>
            XML element for content encoding fields.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.ContentLanguageElement">
            <summary>
            XML element for content language fields.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.ContentLengthElement">
            <summary>
            XML element for content length fields.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.ContentMD5Element">
            <summary>
            XML element for content MD5 fields.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.ContentCRC64Element">
            <summary>
            XML element for content CRC64 fields.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.EnumerationResultsElement">
            <summary>
            XML element for enumeration results.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.ServiceEndpointElement">
            <summary>
            XML element for service endpoint.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.ContainerNameElement">
            <summary>
            XML element for container name.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.ShareNameElement">
            <summary>
            XML element for share name.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.DirectoryPathElement">
            <summary>
            XML element for directory path.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.BlobsElement">
            <summary>
            XML element for blobs.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.PrefixElement">
            <summary>
            XML element for prefixes.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.MaxResultsElement">
            <summary>
            XML element for maximum results.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.MarkerElement">
            <summary>
            XML element for markers.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.NextMarkerElement">
            <summary>
            XML element for the next marker.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.EtagElement">
            <summary>
            XML element for the ETag.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.CreationTimeElement">
            <summary>
            XML element for the creation date.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.LastModifiedElement">
            <summary>
            XML element for the last modified date.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.ServerEncryptionElement">
            <summary>
            XML element for the server encryption status.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.UrlElement">
            <summary>
            XML element for the Url.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.BlobElement">
            <summary>
            XML element for blobs.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.CopyIdElement">
            <summary>
            XML element for copy ID.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.CopyStatusElement">
            <summary>
            XML element for copy status.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.CopySourceElement">
            <summary>
            XML element for copy source.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.CopyProgressElement">
            <summary>
            XML element for copy progress.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.CopyCompletionTimeElement">
            <summary>
            XML element for copy completion time.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.CopyStatusDescriptionElement">
            <summary>
            XML element for copy status description.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.IncrementalCopy">
            <summary>
            XML element for incremental copy.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.CopyDestinationSnapshotElement">
            <summary>
            XML element for destination snapshot time.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.DeletedElement">
            <summary>
            XML element for deleted flag indicating the retention policy on the blob.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.DeletedTimeElement">
            <summary>
            XML element for the time the retained blob was deleted.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.RemainingRetentionDaysElement">
            <summary>
            XML element for the remaining days before the retained blob will be permenantly deleted.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.EncryptionScopeElement">
            <summary>
            XML element for the encryption scope date.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.PageBlobValue">
            <summary>
            Constant signaling a page blob.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.BlockBlobValue">
            <summary>
            Constant signaling a block blob.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.AppendBlobValue">
            <summary>
            Constant signaling an append blob.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.LockedValue">
            <summary>
            Constant signaling the blob is locked.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.UnlockedValue">
            <summary>
            Constant signaling the blob is unlocked.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.LeaseAvailableValue">
            <summary>
            Constant signaling the resource is available for leasing.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.LeasedValue">
            <summary>
            Constant signaling the resource is leased.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.LeaseExpiredValue">
            <summary>
            Constant signaling the resource's lease has expired.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.LeaseBreakingValue">
            <summary>
            Constant signaling the resource's lease is breaking.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.LeaseBrokenValue">
            <summary>
            Constant signaling the resource's lease is broken.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.LeaseInfiniteValue">
            <summary>
            Constant signaling the resource's lease is infinite.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.LeaseFixedValue">
            <summary>
            Constant signaling the resource's lease is fixed (finite).
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.MinimumBreakLeasePeriod">
            <summary>
            Constant for the minimum period of time that a lease can be broken in. 
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.MaximumBreakLeasePeriod">
            <summary>
            Constant for the maximum period of time that a lease can be broken in.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.MinimumLeaseDuration">
            <summary>
            Constant for the minimum duration of a lease.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.MaximumLeaseDuration">
            <summary>
            Constant for the maximum non-infinite duration of a lease.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.CopyPendingValue">
            <summary>
            Constant for a pending copy.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.CopySuccessValue">
            <summary>
            Constant for a successful copy.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.CopyAbortedValue">
            <summary>
            Constant for an aborted copy.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.CopyFailedValue">
            <summary>
            Constant for a failed copy.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.RehydratePendingToHot">
            <summary>
            Constant for rehydrating an archived blob to hot storage.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.RehydratePendingToCool">
            <summary>
            Constant for rehydrating an archived blob to cool storage.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.GeoUnavailableValue">
            <summary>
            Constant for unavailable geo-replication status.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.GeoLiveValue">
            <summary>
            Constant for live geo-replication status.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.GeoBootstrapValue">
            <summary>
            Constant for bootstrap geo-replication status.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.AccessTierElement">
            <summary>
            Constant for the blob tier.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.AccessTierInferred">
            <summary>
            Constant for the access tier being inferred.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.AccessTierChangeTimeElement">
            <summary>
            Constant for the access tier change time.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.ArchiveStatusElement">
            <summary>
            Constant for the archive status.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.BlobTypeElement">
            <summary>
            XML element for blob types.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HasImmutabilityPolicyElement">
            <summary>
            XML element for immutability policy.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HasLegalHoldElement">
            <summary>
            XML element for legal hold.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.LeaseStatusElement">
            <summary>
            XML element for the lease status.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.LeaseStateElement">
            <summary>
            XML element for the lease status.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.LeaseDurationElement">
            <summary>
            XML element for the lease status.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.PublicAccessElement">
            <summary>
            XML element for the public access value.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.SnapshotElement">
            <summary>
            XML element for snapshots.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.ContainersElement">
            <summary>
            XML element for containers.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.ContainerElement">
            <summary>
            XML element for a container.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.SharesElement">
            <summary>
            XML element for shares.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.ShareElement">
            <summary>
            XML element for a share.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.QuotaElement">
            <summary>
            XML element for Share Quota.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.FileRangeElement">
            <summary>
            XML element for file ranges.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.FileRangeListElement">
            <summary>
            XML element for file list elements.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HandleElement">
            <summary> 
            XML element for file handles. 
            </summary> 
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.FileHandleListElement">
            <summary> 
            XML element for file list elements. 
            </summary> 
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.EntriesElement">
            <summary>
            XML element for files.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.FileElement">
            <summary>
            XML element for files.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.FileDirectoryElement">
            <summary>
            XML element for directory.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.QueuesElement">
            <summary>
            XML element for queues.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.QueueNameElement">
            <summary>
            Version 2 of the XML element for the queue name.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.QueueElement">
            <summary>
            XML element for the queue.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.PropertiesElement">
            <summary>
            XML element for properties.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.MetadataElement">
            <summary>
            XML element for the metadata.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.InvalidMetadataName">
            <summary>
            XML element for an invalid metadata name.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.MaxResults">
            <summary>
            XML element for maximum results.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.CommittedElement">
            <summary>
            XML element for committed blocks.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.UncommittedElement">
            <summary>
            XML element for uncommitted blocks.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.LatestElement">
            <summary>
            XML element for the latest.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.SignedIdentifiers">
            <summary>
            XML element for signed identifiers.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.SignedIdentifier">
            <summary>
            XML element for a signed identifier.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.AccessPolicy">
            <summary>
            XML element for access policies.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.Id">
            <summary>
            XML attribute for IDs.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.Start">
            <summary>
            XML element for the start time of an access policy.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.Expiry">
            <summary>
            XML element for the end of an access policy.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.KeyInfo">
            <summary>
            XML element for key information data.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.UserDelegationKey">
            <summary>
            XML elenent for user delegation key data.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.SignedOid">
            <summary>
            XML element for the OID of a user delegation key.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.SignedTid">
            <summary>
            XML element for the TID of a user delegation key.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.SignedStart">
            <summary>
            XML element for the start time of a user delegation key.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.SignedExpiry">
            <summary>
            XML element for the end of a user delegation key.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.SignedVersion">
            <summary>
            XML element for the REST API version used to create the delegation key.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.SignedService">
            <summary>
            XML element for the REST API version used to create the delegation key.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.Value">
            <summary>
            XML element for a generic value.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.DateTimeFormatter">
            <summary>
            DateTimeOffset format string for most operations.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.Permission">
            <summary>
            XML element for the permissions of an access policy.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.Messages">
            <summary>
            The URI path component to access the messages in a queue.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.ErrorException">
            <summary>
            XML element for exception details.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.ErrorRootElement">
            <summary>
            XML root element for errors.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.ErrorCode">
            <summary>
            XML element for error codes.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.ErrorCodePreview">
            <summary>
            XML element for error codes returned by the preview tenants.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.ErrorMessage">
            <summary>
            XML element for error messages.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.ErrorMessagePreview">
            <summary>
            XML element for error messages.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.ErrorExceptionMessage">
            <summary>
            XML element for exception messages.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.ErrorExceptionStackTrace">
            <summary>
            XML element for stack traces.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.EdmEntityTypeNamespaceName">
            <summary>
            Namespace of the entity container.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.EdmEntityTypeName">
            <summary>
            Name of the entity container.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.EntitySetName">
            <summary>
            Name of the entity set.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.Edm">
            <summary>
            Namespace name for primitive types.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.DefaultNamespaceName">
            <summary>
            Default namespace name for Tables. 
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.DefaultTableName">
            <summary>
            Default name for Tables. 
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.XMLAcceptHeaderValue">
            <summary>
            Header value to set Accept to XML.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.JsonLightAcceptHeaderValue">
            <summary>
            Header value to set Accept to JsonLight.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.JsonFullMetadataAcceptHeaderValue">
            <summary>
            Header value to set Accept to JsonFullMetadata.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.JsonNoMetadataAcceptHeaderValue">
            <summary>
            Header value to set Accept to JsonNoMetadata.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.NoMetadata">
            <summary>
            Header value argument to set JSON no metadata.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.JsonContentTypeHeaderValue">
            <summary>
            Header value to set Content-Type to JSON.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.ETagPrefix">
            <summary>
            The prefix used in all ETags.
            </summary>
        </member>
        <member name="T:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants">
            <summary>
            Constants for HTTP headers.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.UserAgent">
            <summary>
            Specifies the value to use for UserAgent header.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.UserAgentComment">
            <summary>
            Specifies the comment to use for UserAgent header.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.UserAgentProductName">
            <summary>
            Specifies the value to use for UserAgent header.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.UserAgentProductVersion">
            <summary>
            Specifies the value to use for UserAgent header.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.PrefixForStorageHeader">
            <summary>
            Master Microsoft Azure Storage header prefix.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.TrueHeader">
            <summary>
            True Header.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.FalseHeader">
            <summary>
            False Header.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.PrefixForStorageProperties">
            <summary>
            Header prefix for properties.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.PrefixForStorageMetadata">
            <summary>
            Header prefix for metadata.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.ContentDispositionResponseHeader">
            <summary>
            Header that specifies content disposition.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.ContentLengthHeader">
            <summary>
            Header that specifies content length.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.ContentLanguageHeader">
            <summary>
            Header that specifies content language.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.CreationTimeHeader">
            <summary>
            Header that specifies the creation time value for the resource.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.EtagHeader">
            <summary>
            Header that specifies the ETag value for the resource.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.HasImmutabilityPolicyHeader">
            <summary>
            Header that specifies the immutability policy value for the resource.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.HasLegalHoldHeader">
            <summary>
            Header that specifies the legal hold value for the resource.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.ServerEncrypted">
            <summary>
            Header that specifies if a resourse is fully encrypted server-side.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.EncryptionScopeHeader">
            <summary>
            Header that specifies encryption scope.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.DefaultEncryptionScopeHeader">
            <summary>
            Header that specifies default encryption scope for a container.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.PreventEncryptionScopeOverrideHeader">
            <summary>
            Header that specifies a value to indicate whether preventing request from specifying a different encryption scope than the scope set on the container.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.ServerRequestEncrypted">
            <summary>
            Header that acknowledges the data used for write operation is encrypted server-side.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.RangeHeader">
            <summary>
            Header for data ranges.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.RangeContentMD5Header">
            <summary>
            Header for range content MD5.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.RangeContentCRC64Header">
            <summary>
            Header for range content CRC64.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.ContentCrc64Header">
            <summary>
            Header for content CRC64.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.StorageVersionHeader">
            <summary>
            Header for storage version.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.CopySourceHeader">
            <summary>
            Header for copy source.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.RequiresSyncHeader">
            <summary>
            Header for copy sync.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.SourceRangeHeader">
            <summary>
            Header for source ranges.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.SourceIfMatchHeader">
            <summary>
            Header for the If-Match condition.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.SourceIfMatchCrcHeader">
            <summary>
            Header for the Source If Match CRC64 header.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.SourceIfModifiedSinceHeader">
            <summary>
            Header for the If-Modified-Since condition.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.SourceIfNoneMatchHeader">
            <summary>
            Header for the If-None-Match condition.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.SourceIfNoneMatchCrcHeader">
            <summary>
            Header for the Source If None Match CRC64 header.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.SourceIfUnmodifiedSinceHeader">
            <summary>
            Header for the If-Unmodified-Since condition.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.FileType">
            <summary>
            Header for the file type.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.FileCacheControlHeader">
            <summary>
            Header that specifies file caching control.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.FileContentDispositionRequestHeader">
            <summary>
            Request header that specifies the file content disposition.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.FileContentEncodingHeader">
            <summary>
            Header that specifies file content encoding.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.FileContentLanguageHeader">
            <summary>
            Header that specifies file content language.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.FileContentMD5Header">
            <summary>
            Header that specifies file content MD5.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.FileContentCRC64Header">
            <summary>
            Header that specifies file content CRC64.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.SourceContentMD5Header">
            <summary>
            Header that specifies source content MD5.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.SourceContentCRC64Header">
            <summary>
            Header that specifies source content CRC64.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.FileContentTypeHeader">
            <summary>
            Header that specifies file content type.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.FileContentLengthHeader">
            <summary>
            Header that specifies file content length.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.FileRangeWrite">
            <summary>
            Header that specifies the file write mode.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.BlobType">
            <summary>
            Header for the blob type.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.SnapshotHeader">
            <summary>
            Header for snapshots.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.DeleteSnapshotHeader">
            <summary>
            Header to delete snapshots.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.AccessTierHeader">
            <summary>
            Header for the blob tier.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.ArchiveStatusHeader">
            <summary>
            Header for the archive status.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.AccessTierInferredHeader">
            <summary>
            Header for the blob tier inferred.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.AccessTierChangeTimeHeader">
            <summary>
            Header for the last time the tier was modified.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.BlobCacheControlHeader">
            <summary>
            Header that specifies blob caching control.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.BlobContentDispositionRequestHeader">
            <summary>
            Request header that specifies the blob content disposition.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.BlobContentEncodingHeader">
            <summary>
            Header that specifies blob content encoding.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.BlobContentLanguageHeader">
            <summary>
            Header that specifies blob content language.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.BlobContentMD5Header">
            <summary>
            Header that specifies blob content MD5.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.BlobContentCRC64Header">
            <summary>
            Header that specifies blob content CRC64.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.BlobContentTypeHeader">
            <summary>
            Header that specifies blob content type.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.BlobContentLengthHeader">
            <summary>
            Header that specifies blob content length.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.BlobSequenceNumber">
            <summary>
            Header that specifies blob sequence number.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.SequenceNumberAction">
            <summary>
            Header that specifies sequence number action.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.BlobCommittedBlockCount">
            <summary>
            Header that specifies committed block count.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.BlobAppendOffset">
            <summary>
            Header that specifies the blob append offset.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.IfSequenceNumberLEHeader">
            <summary>
            Header for the If-Sequence-Number-LE condition.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.IfSequenceNumberLTHeader">
            <summary>
            Header for the If-Sequence-Number-LT condition.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.IfSequenceNumberEqHeader">
            <summary>
            Header for the If-Sequence-Number-EQ condition.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.IfMaxSizeLessThanOrEqualHeader">
            <summary>
            Header for the blob-condition-maxsize condition.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.IfAppendPositionEqualHeader">
            <summary>
            Header for the blob-condition-appendpos condition.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.LeaseIdHeader">
            <summary>
            Header that specifies lease ID.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.LeaseStatus">
            <summary>
            Header that specifies lease status.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.LeaseState">
            <summary>
            Header that specifies lease status.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.PageWrite">
            <summary>
            Header that specifies page write mode.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.ApproximateMessagesCount">
            <summary>
            Header that specifies approximate message count of a queue.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.Date">
            <summary>
            Header that specifies the date.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.RequestIdHeader">
            <summary>
            Header indicating the request ID.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.ClientRequestIdHeader">
            <summary>
            Header indicating the client request ID.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.BlobPublicAccess">
            <summary>
            Header that specifies public access to blobs.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.FilePermission">
            <summary>
            Header that specifies file permission.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.FilePermissionCopyMode">
            <summary>
            Header that specifies file permission copy mode.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.FileCopyFromSource">
            <summary>
            Header value that specifies copy file permissions, file system attributes, creation time or last write time from the source file to the destination file.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.FileCopyOverride">
            <summary>
            Header value that specifies override file permissions on the destination file.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.FilePermissionInherit">
            <summary>
            Default header value for file permission.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.FilePermissionKey">
            <summary>
            Header that specifies file permission key.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.FileAttributes">
            <summary>
            Header that specifies file attributes.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.FileCopySetArchive">
            <summary>
            Header with a boolean value that specifies whether the Archive attribute should be set.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.FileCopyInoreReadOnly">
            <summary>
            Header with a boolean value that specifies whether the ReadOnly attribute on a preexisting destination file should be respected. 
            If true, the copy will succeed, otherwise, a previous file at the destination with the ReadOnly attribute set will cause the copy to fail.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.FileAttributesNone">
            <summary>
            Default file attribute value for files.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.FileCreationTime">
            <summary>
            Header that specifies file creation time.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.FileLastWriteTime">
            <summary>
             Header that specifies file last write time.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.FileChangeTime">
            <summary>
             Header that specifies file change time.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.FileTimeNow">
            <summary>
            Default file creation and file last write time.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.Preserve">
            <summary>
            Default value for several SMB file headers.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.FileId">
            <summary>
            Header that specifies file id.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.FileParentId">
            <summary>
            Header that spcifies file parent id.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.RehydratePriorityHeader">
            <summary>
            Header for the blob rehydration priority.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.RangeHeaderFormat">
            <summary>
            Format string for specifying ranges.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.TargetStorageVersion">
            <summary>
            Current storage version header value.
            Every time this version changes, assembly version needs to be updated as well.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.File">
            <summary>
            Specifies the file type.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.AllFileHandles">
            <summary> 
            Specifies the wildcard for specifying all file handles. 
            </summary> 
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.HandleId">
            <summary> 
            Header that specifies what handle to close. 
            </summary> 
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.NumHandlesClosed">
            <summary> 
            Header that specifies how many SMB handles a close handles operation closed. 
            </summary> 
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.PageBlob">
            <summary>
            Specifies the page blob type.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.BlockBlob">
            <summary>
            Specifies the block blob type.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.AppendBlob">
            <summary>
            Specifies the append blob type.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.SnapshotsOnlyValue">
            <summary>
            Specifies only snapshots are to be included.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.IncludeSnapshotsValue">
            <summary>
            Specifies snapshots are to be included.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.PopReceipt">
            <summary>
            Header that specifies the pop receipt for a message.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.NextVisibleTime">
            <summary>
            Header that specifies the next visible time for a message.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.PeekOnly">
            <summary>
            Header that specifies whether to peek-only.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.ContainerPublicAccessType">
            <summary>
            Header that specifies whether data in the container may be accessed publicly and what level of access is to be allowed.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.LeaseActionHeader">
            <summary>
            Header that specifies the lease action to perform.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.ProposedLeaseIdHeader">
            <summary>
            Header that specifies the proposed lease ID for a leasing operation.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.LeaseDurationHeader">
            <summary>
            Header that specifies the duration of a lease.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.LeaseBreakPeriodHeader">
            <summary>
            Header that specifies the break period of a lease.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.LeaseTimeHeader">
            <summary>
            Header that specifies the remaining lease time.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.KeyNameHeader">
            <summary>
            Header that specifies the key name for explicit keys.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.CopyIdHeader">
            <summary>
            Header that specifies the copy ID.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.CopyCompletionTimeHeader">
            <summary>
            Header that specifies the conclusion time of the last attempted blob copy operation 
            where this blob was the destination blob.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.CopyStatusHeader">
            <summary>
            Header that specifies the copy status.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.CopyProgressHeader">
            <summary>
            Header that specifies the copy progress.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.CopyDescriptionHeader">
            <summary>
            Header that specifies a copy error message.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.CopyActionHeader">
            <summary>
            Header that specifies the copy action.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.CopyTypeHeader">
            <summary>
            Header that specifies the copy type.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.CopyActionAbort">
            <summary>
            The value of the copy action header that signifies an abort operation.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.IncrementalCopyHeader">
            <summary>
            Header that specifies an incremental copy.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.CopyDestinationSnapshotHeader">
            <summary>
            Header that specifies the snapshot time of the last successful incremental copy snapshot.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.ShareSize">
            <summary>
            Header that specifies the share size, in gigabytes.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.ShareQuota">
            <summary>
            Header that specifies the share quota, in gigabytes.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.Recursive">
            <summary> 
            Header that specifies whether the specified operation should recurse. 
            </summary> 
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.Marker">
            <summary> 
            Header that specifies a continuation token. 
            </summary> 
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.ClientProvidedEncryptionSuccess">
            <summary>
            The request server encrypted header for client-provided key encryption.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.ClientProvidedEncyptionKey">
            <summary>
            The key header for client-provided key encryption.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.ClientProvidedEncyptionKeyHash">
            <summary>
            The key hash header for client-provided key encryption
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.ClientProvidedEncyptionAlgorithm">
            <summary>
            The encryption algorithm header for client-provided key encryption.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.ClientProvidedEncyptionKeySource">
            <summary>
            The key header for client-provided key encryption.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.ClientProvidedEncyptionKeyHashSource">
            <summary>
            The key hash header for client-provided key encryption.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.ClientProvidedEncyptionKeyAlgorithmSource">
            <summary>
            The encryption algorithm header for client-provided key encryption.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.SkuNameName">
            <summary>
            The name of the SKU name header element.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.AccountKindName">
            <summary>
            The name of the account kind header element.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.PayloadAcceptHeader">
            <summary>
            Header that specifies the Accept type for the response payload.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.PayloadContentTypeHeader">
            <summary>
            Header that specifies the Content type for the request payload.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.AcceptCharset">
            <summary>
            OData Related
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.HeaderConstants.StorageErrorCodeHeader">
            <summary>
            Header that specifies the storage error code string in a failed response.
            </summary>
        </member>
        <member name="T:Microsoft.Azure.Storage.Shared.Protocol.Constants.QueryConstants">
            <summary>
            Constants for query strings.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.QueryConstants.Snapshot">
            <summary>
            Query component for snapshot time.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.QueryConstants.ShareSnapshot">
            <summary>
            Query component for share snapshot time.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.QueryConstants.SignedStart">
            <summary>
            Query component for the signed SAS start time.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.QueryConstants.SignedExpiry">
            <summary>
            Query component for the signed SAS expiry time.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.QueryConstants.SignedResource">
            <summary>
            Query component for the signed SAS resource.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.QueryConstants.BlobResourceType">
            <summary>
            Specifies a restype value for blobs.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.QueryConstants.BlobSnapshotResourceType">
            <summary>
            Specifies a restype value for blob snapshots.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.QueryConstants.ContainerResourceType">
            <summary>
            Specifies a restype value for containers.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.QueryConstants.SignedKeyOid">
            <summary>
            Query component for the signed object ID.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.QueryConstants.SignedKeyTid">
            <summary>
            Query component for the signed tenant ID.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.QueryConstants.SignedKeyStart">
            <summary>
            Query component for the signed key start time.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.QueryConstants.SignedKeyExpiry">
            <summary>
            Query compnent for the signed key expiration time.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.QueryConstants.SignedKeyService">
            <summary>
            Query compnent for the signed key service type.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.QueryConstants.SignedKeyVersion">
            <summary>
            Query component for the signed key version string.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.QueryConstants.SignedResourceTypes">
            <summary>
            Query component for the signed SAS resource types.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.QueryConstants.SignedServices">
            <summary>
            Query component for the signed SAS service.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.QueryConstants.SignedProtocols">
            <summary>
            Query component for the signed SAS protocol.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.QueryConstants.SignedIP">
            <summary>
            Query component for the signed SAS IP.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.QueryConstants.SasTableName">
            <summary>
            Query component for the SAS table name.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.QueryConstants.SignedPermissions">
            <summary>
            Query component for the signed SAS permissions.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.QueryConstants.StartPartitionKey">
            <summary>
            Query component for the SAS start partition key.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.QueryConstants.StartRowKey">
            <summary>
            Query component for the SAS start row key.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.QueryConstants.EndPartitionKey">
            <summary>
            Query component for the SAS end partition key.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.QueryConstants.EndRowKey">
            <summary>
            Query component for the SAS end row key.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.QueryConstants.SignedIdentifier">
            <summary>
            Query component for the signed SAS identifier.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.QueryConstants.SignedKey">
            <summary>
            Query component for the signing SAS key.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.QueryConstants.SignedVersion">
            <summary>
            Query component for the signed SAS version.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.QueryConstants.Signature">
            <summary>
            Query component for SAS signature.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.QueryConstants.CacheControl">
            <summary>
            Query component for SAS cache control.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.QueryConstants.ContentType">
            <summary>
            Query component for SAS content type.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.QueryConstants.ContentEncoding">
            <summary>
            Query component for SAS content encoding.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.QueryConstants.ContentLanguage">
            <summary>
            Query component for SAS content language.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.QueryConstants.ContentDisposition">
            <summary>
            Query component for SAS content disposition.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.QueryConstants.ApiVersion">
            <summary>
            Query component for SAS API version.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.QueryConstants.MessageTimeToLive">
            <summary>
            Query component for message time-to-live.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.QueryConstants.VisibilityTimeout">
            <summary>
            Query component for message visibility timeout.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.QueryConstants.NumOfMessages">
            <summary>
            Query component for the number of messages.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.QueryConstants.PopReceipt">
            <summary>
            Query component for message pop receipt.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.QueryConstants.ResourceType">
            <summary>
            Query component for resource type.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.QueryConstants.Component">
            <summary>
            Query component for the operation (component) to access.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.QueryConstants.CopyId">
            <summary>
            Query component for the copy ID.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.QueryConstants.Marker">
            <summary> 
            Query parameter for continuation token.
            </summary> 
        </member>
        <member name="T:Microsoft.Azure.Storage.Shared.Protocol.Constants.ContinuationConstants">
            <summary>
            Constants for Result Continuations
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.ContinuationConstants.ContinuationTopElement">
            <summary>
            Top Element for Continuation Tokens
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.ContinuationConstants.NextMarkerElement">
            <summary>
            XML element for the next marker.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.ContinuationConstants.NextPartitionKeyElement">
            <summary>
            XML element for the next partition key.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.ContinuationConstants.NextRowKeyElement">
            <summary>
            XML element for the next row key.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.ContinuationConstants.NextTableNameElement">
            <summary>
            XML element for the next table name.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.ContinuationConstants.TargetLocationElement">
            <summary>
            XML element for the target location.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.ContinuationConstants.VersionElement">
            <summary>
            XML element for the token version.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.ContinuationConstants.CurrentVersion">
            <summary>
            Stores the current token version value.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.ContinuationConstants.TypeElement">
            <summary>
            XML element for the token type.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.ContinuationConstants.BlobType">
            <summary>
            Specifies the blob continuation token type.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.ContinuationConstants.QueueType">
            <summary>
            Specifies the queue continuation token type.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.ContinuationConstants.TableType">
            <summary>
            Specifies the table continuation token type.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.ContinuationConstants.FileType">
            <summary>
            Specifies the file continuation token type.
            </summary>
        </member>
        <member name="T:Microsoft.Azure.Storage.Shared.Protocol.Constants.VersionConstants">
            <summary>
            Constants for version strings
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.VersionConstants.August2013">
            <summary>
            Constant for the 2013-08-15 version.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.VersionConstants.February2012">
            <summary>
            Constant for the 2012-02-12 version.
            </summary>
        </member>
        <member name="T:Microsoft.Azure.Storage.Shared.Protocol.Constants.AnalyticsConstants">
            <summary>
            Constants for analytics client
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.AnalyticsConstants.LogsContainer">
            <summary>
            Constant for the logs container.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.AnalyticsConstants.MetricsCapacityBlob">
            <summary>
            Constant for the blob capacity metrics table.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.AnalyticsConstants.MetricsHourPrimaryTransactionsBlob">
            <summary>
            Constant for the blob service primary location hourly metrics table.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.AnalyticsConstants.MetricsHourPrimaryTransactionsTable">
            <summary>
            Constant for the table service primary location hourly metrics table.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.AnalyticsConstants.MetricsHourPrimaryTransactionsQueue">
            <summary>
            Constant for the queue service primary location hourly metrics table.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.AnalyticsConstants.MetricsHourPrimaryTransactionsFile">
            <summary>
            Constant for the file service primary location hourly metrics table.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.AnalyticsConstants.MetricsMinutePrimaryTransactionsBlob">
            <summary>
            Constant for the blob service primary location minute metrics table.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.AnalyticsConstants.MetricsMinutePrimaryTransactionsTable">
            <summary>
            Constant for the table service primary location minute metrics table.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.AnalyticsConstants.MetricsMinutePrimaryTransactionsQueue">
            <summary>
            Constant for the queue service primary location minute metrics table.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.AnalyticsConstants.MetricsMinutePrimaryTransactionsFile">
            <summary>
            Constant for the file service primary location minute metrics table.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.AnalyticsConstants.MetricsHourSecondaryTransactionsBlob">
            <summary>
            Constant for the blob service secondary location hourly metrics table.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.AnalyticsConstants.MetricsHourSecondaryTransactionsTable">
            <summary>
            Constant for the table service secondary location hourly metrics table.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.AnalyticsConstants.MetricsHourSecondaryTransactionsQueue">
            <summary>
            Constant for the queue service secondary location hourly metrics table.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.AnalyticsConstants.MetricsHourSecondaryTransactionsFile">
            <summary>
            Constant for the file service secondary location hourly metrics table.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.AnalyticsConstants.MetricsMinuteSecondaryTransactionsBlob">
            <summary>
            Constant for the blob service secondary location minute metrics table.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.AnalyticsConstants.MetricsMinuteSecondaryTransactionsTable">
            <summary>
            Constant for the table service secondary location minute metrics table.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.AnalyticsConstants.MetricsMinuteSecondaryTransactionsQueue">
            <summary>
            Constant for the queue service secondary location minute metrics table.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.AnalyticsConstants.MetricsMinuteSecondaryTransactionsFile">
            <summary>
            Constant for the file service secondary location minute metrics table.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.AnalyticsConstants.LoggingVersionV1">
            <summary>
            Constant for default logging version.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.AnalyticsConstants.MetricsVersionV1">
            <summary>
            Constant for default metrics version.
            </summary>
        </member>
        <member name="T:Microsoft.Azure.Storage.Shared.Protocol.Constants.EncryptionConstants">
            <summary>
            Constants for client encryption.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.EncryptionConstants.EncryptionProtocolV1">
            <summary>
            Constant for the encryption protocol.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.EncryptionConstants.KeyWrappingIV">
            <summary>
            Encryption metadata key for key wrapping IV.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.EncryptionConstants.BlobEncryptionData">
            <summary>
            Metadata header to store encryption materials.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.EncryptionConstants.TableEncryptionKeyDetails">
            <summary>
            Property name to store the encryption metadata.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.EncryptionConstants.TableEncryptionPropertyDetails">
            <summary>
            Additional property name to store the encryption metadata.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.EncryptionConstants.AgentMetadataKey">
            <summary>
            Key for the encryption agent
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.Constants.EncryptionConstants.AgentMetadataValue">
            <summary>
            Value for the encryption agent
            </summary>
        </member>
        <member name="T:Microsoft.Azure.Storage.Shared.Protocol.CorsHttpMethods">
            <summary>
            HTTP methods that are supported by CORS.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.CorsHttpMethods.None">
            <summary>
            Represents no HTTP method in a CORS rule.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.CorsHttpMethods.Get">
            <summary>
            Represents the GET HTTP method in a CORS rule.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.CorsHttpMethods.Head">
            <summary>
            Represents the HEAD HTTP method in a CORS rule.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.CorsHttpMethods.Post">
            <summary>
            Represents the POST HTTP method in a CORS rule.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.CorsHttpMethods.Put">
            <summary>
            Represents the PUT HTTP method in a CORS rule.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.CorsHttpMethods.Delete">
            <summary>
            Represents the DELETE HTTP method in a CORS rule.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.CorsHttpMethods.Trace">
            <summary>
            Represents the TRACE HTTP method in a CORS rule.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.CorsHttpMethods.Options">
            <summary>
            Represents the OPTIONS HTTP method in a CORS rule.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.CorsHttpMethods.Connect">
            <summary>
            Represents the CONNECT HTTP method in a CORS rule.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.CorsHttpMethods.Merge">
            <summary>
            Represents the MERGE HTTP method in a CORS rule.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.CorsHttpMethods.Patch">
            <summary>
            Represents the PATCH HTTP method in a CORS rule.
            </summary>
        </member>
        <member name="T:Microsoft.Azure.Storage.Shared.Protocol.CorsProperties">
            <summary>
            Class representing the service properties pertaining to CORS.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.Shared.Protocol.CorsProperties.#ctor">
            <summary>
            Constructs a CORS Properties object.
            </summary>
        </member>
        <member name="P:Microsoft.Azure.Storage.Shared.Protocol.CorsProperties.CorsRules">
            <summary>
            Gets or sets CORS rules. The order of the list corresponds to precedence of rules. 
            </summary>
            <value>A collection containing CORS rules, limited to 5.</value>
        </member>
        <member name="T:Microsoft.Azure.Storage.Shared.Protocol.CorsRule">
            <summary>
            Class representing the service properties pertaining to CORS.
            </summary>
        </member>
        <member name="P:Microsoft.Azure.Storage.Shared.Protocol.CorsRule.AllowedOrigins">
            <summary>
            Gets or sets domain names allowed via CORS.
            </summary>
            <value>A collection of strings containing the allowed domain names, limited to 64.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.Shared.Protocol.CorsRule.ExposedHeaders">
            <summary>
            Gets or sets response headers that should be exposed to client via CORS.
            </summary>
            <value>A collection of strings containing exposed headers, limited to 64 defined headers and two prefixed headers.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.Shared.Protocol.CorsRule.AllowedHeaders">
            <summary>
            Gets or sets headers allowed to be part of the CORS request.
            </summary>
            <value>A collection of strings containing allowed headers, limited to 64 defined headers and two prefixed headers.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.Shared.Protocol.CorsRule.AllowedMethods">
            <summary>
            Gets or sets the HTTP methods permitted to execute for this origin.
            </summary>
            <value>The allowed HTTP methods.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.Shared.Protocol.CorsRule.MaxAgeInSeconds">
            <summary>
            Gets or sets the length of time in seconds that a preflight response should be cached by browser.
            </summary>
            <value>The maximum number of seconds to cache the response.</value>
        </member>
        <member name="T:Microsoft.Azure.Storage.Shared.Protocol.DeleteRetentionPolicy">
            <summary>
            Class representing the service properties pertaining to DeleteRetentionPolicy
            </summary>
        </member>
        <member name="P:Microsoft.Azure.Storage.Shared.Protocol.DeleteRetentionPolicy.Enabled">
            <summary>
            Gets or sets the Enabled flag of the DeleteRetentionPolicy.
            </summary>
            <value>Indicates whether DeleteRetentionPolicy is enabled for the Blob service. </value>
        </member>
        <member name="P:Microsoft.Azure.Storage.Shared.Protocol.DeleteRetentionPolicy.RetentionDays">
            <summary>
            Gets or Sets the number of days on the DeleteRetentionPolicy.
            </summary>
            <value>Indicates the number of days that the deleted blob should be retained. The minimum specified value can be 1 and the maximum value can be 365. </value>
        </member>
        <member name="T:Microsoft.Azure.Storage.Shared.Protocol.GeoReplicationStats">
            <summary>
            Class representing the geo-replication stats.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.GeoReplicationStats.StatusName">
            <summary>
            The name of the status XML element.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.GeoReplicationStats.LastSyncTimeName">
            <summary>
            The name of the last sync time XML element.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.Shared.Protocol.GeoReplicationStats.#ctor">
            <summary>
            Initializes a new instance of the GeoReplicationStats class.
            </summary>
        </member>
        <member name="P:Microsoft.Azure.Storage.Shared.Protocol.GeoReplicationStats.Status">
            <summary>
            Gets or sets the status of geo-replication.
            </summary>
            <value>The status of geo-replication.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.Shared.Protocol.GeoReplicationStats.LastSyncTime">
            <summary>
            Gets or sets the last synchronization time.
            </summary>
            <value>The last synchronization time.</value>
            <remarks>All primary writes preceding this value are guaranteed to be available for read operations. Primary writes following this point in time may or may not be available for reads.</remarks>
        </member>
        <member name="M:Microsoft.Azure.Storage.Shared.Protocol.GeoReplicationStats.GetGeoReplicationStatus(System.String)">
            <summary>
            Gets a <see cref="T:Microsoft.Azure.Storage.Shared.Protocol.GeoReplicationStatus"/> from a string.
            </summary>
            <param name="geoReplicationStatus">The geo-replication status string.</param>
            <returns>A <see cref="T:Microsoft.Azure.Storage.Shared.Protocol.GeoReplicationStatus"/> enumeration.</returns>
            <exception cref="T:System.ArgumentException">The string contains an unrecognized value.</exception>
        </member>
        <member name="M:Microsoft.Azure.Storage.Shared.Protocol.GeoReplicationStats.ReadGeoReplicationStatsFromXml(System.Xml.Linq.XElement)">
            <summary>
            Constructs a <c>GeoReplicationStats</c> object from an XML element.
            </summary>
            <param name="element">The XML element.</param>
            <returns>A <c>GeoReplicationStats</c> object containing the properties in the element.</returns>
        </member>
        <member name="T:Microsoft.Azure.Storage.Shared.Protocol.GeoReplicationStatus">
            <summary>
            Enumeration representing the state of geo-replication in a service.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.GeoReplicationStatus.Unavailable">
            <summary>
            Status of geo-replication is unavailable.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.GeoReplicationStatus.Live">
            <summary>
            Geo-replication is live.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.GeoReplicationStatus.Bootstrap">
            <summary>
            Data is being bootstrapped from primary to secondary.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.Shared.Protocol.HttpRequestMessageFactory.CreateRequestMessage(System.Net.Http.HttpMethod,System.Uri,System.Nullable{System.Int32},Microsoft.Azure.Storage.Core.UriQueryBuilder,System.Net.Http.HttpContent,Microsoft.Azure.Storage.OperationContext,Microsoft.Azure.Storage.Core.Auth.ICanonicalizer,Microsoft.Azure.Storage.Auth.StorageCredentials)">
            <summary>
            Creates the web request.
            </summary>
            <param name="uri">The request Uri.</param>
            <param name="timeout">The timeout.</param>
            <param name="builder">The builder.</param>
            <returns>A web request for performing the operation.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Shared.Protocol.HttpRequestMessageFactory.Create(System.Uri,System.Nullable{System.Int32},Microsoft.Azure.Storage.Core.UriQueryBuilder,System.Net.Http.HttpContent,Microsoft.Azure.Storage.OperationContext,Microsoft.Azure.Storage.Core.Auth.ICanonicalizer,Microsoft.Azure.Storage.Auth.StorageCredentials)">
            <summary>
            Creates the specified Uri.
            </summary>
            <param name="uri">The Uri to create.</param>
            <param name="timeout">The timeout.</param>
            <param name="builder">The builder.</param>
            <returns>A web request for performing the operation.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Shared.Protocol.HttpRequestMessageFactory.GetAcl(System.Uri,System.Nullable{System.Int32},Microsoft.Azure.Storage.Core.UriQueryBuilder,System.Net.Http.HttpContent,Microsoft.Azure.Storage.OperationContext,Microsoft.Azure.Storage.Core.Auth.ICanonicalizer,Microsoft.Azure.Storage.Auth.StorageCredentials)">
            <summary>
            Constructs a web request to return the ACL for a cloud resource.
            </summary>
            <param name="uri">The absolute URI to the resource.</param>
            <param name="timeout">The server timeout interval.</param>
            <param name="builder">An optional query builder to use.</param>
            <returns><returns>A web request to use to perform the operation.</returns></returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Shared.Protocol.HttpRequestMessageFactory.SetAcl(System.Uri,System.Nullable{System.Int32},Microsoft.Azure.Storage.Core.UriQueryBuilder,System.Net.Http.HttpContent,Microsoft.Azure.Storage.OperationContext,Microsoft.Azure.Storage.Core.Auth.ICanonicalizer,Microsoft.Azure.Storage.Auth.StorageCredentials)">
            <summary>
            Constructs a web request to set the ACL for a cloud resource.
            </summary>
            <param name="uri">The absolute URI to the resource.</param>
            <param name="timeout">The server timeout interval.</param>
            <param name="builder">An optional query builder to use.</param>
            <returns><returns>A web request to use to perform the operation.</returns></returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Shared.Protocol.HttpRequestMessageFactory.GetProperties(System.Uri,System.Nullable{System.Int32},Microsoft.Azure.Storage.Core.UriQueryBuilder,System.Net.Http.HttpContent,Microsoft.Azure.Storage.OperationContext,Microsoft.Azure.Storage.Core.Auth.ICanonicalizer,Microsoft.Azure.Storage.Auth.StorageCredentials)">
            <summary>
            Gets the properties.
            </summary>
            <param name="uri">The Uri to query.</param>
            <param name="timeout">The timeout.</param>
            <param name="builder">The builder.</param>
            <returns>A web request for performing the operation.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Shared.Protocol.HttpRequestMessageFactory.GetMetadata(System.Uri,System.Nullable{System.Int32},Microsoft.Azure.Storage.Core.UriQueryBuilder,System.Net.Http.HttpContent,Microsoft.Azure.Storage.OperationContext,Microsoft.Azure.Storage.Core.Auth.ICanonicalizer,Microsoft.Azure.Storage.Auth.StorageCredentials)">
            <summary>
            Gets the metadata.
            </summary>
            <param name="uri">The blob Uri.</param>
            <param name="timeout">The timeout.</param>
            <param name="builder">The builder.</param>
            <returns>A web request for performing the operation.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Shared.Protocol.HttpRequestMessageFactory.SetMetadata(System.Uri,System.Nullable{System.Int32},Microsoft.Azure.Storage.Core.UriQueryBuilder,System.Net.Http.HttpContent,Microsoft.Azure.Storage.OperationContext,Microsoft.Azure.Storage.Core.Auth.ICanonicalizer,Microsoft.Azure.Storage.Auth.StorageCredentials)">
            <summary>
            Sets the metadata.
            </summary>
            <param name="uri">The blob Uri.</param>
            <param name="timeout">The timeout.</param>
            <param name="builder">The builder.</param>
            <returns>A web request for performing the operation.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Shared.Protocol.HttpRequestMessageFactory.AddMetadata(Microsoft.Azure.Storage.Core.StorageRequestMessage,System.Collections.Generic.IDictionary{System.String,System.String})">
            <summary>
            Adds the metadata.
            </summary>
            <param name="request">The request.</param>
            <param name="metadata">The metadata.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.Shared.Protocol.HttpRequestMessageFactory.AddMetadata(Microsoft.Azure.Storage.Core.StorageRequestMessage,System.String,System.String)">
            <summary>
            Adds the metadata.
            </summary>
            <param name="request">The request.</param>
            <param name="name">The metadata name.</param>
            <param name="value">The metadata value.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.Shared.Protocol.HttpRequestMessageFactory.Delete(System.Uri,System.Nullable{System.Int32},Microsoft.Azure.Storage.Core.UriQueryBuilder,System.Net.Http.HttpContent,Microsoft.Azure.Storage.OperationContext,Microsoft.Azure.Storage.Core.Auth.ICanonicalizer,Microsoft.Azure.Storage.Auth.StorageCredentials)">
            <summary>
            Deletes the specified Uri.
            </summary>
            <param name="uri">The Uri to delete.</param>
            <param name="timeout">The timeout.</param>
            <param name="builder">The builder.</param>
            <returns>A web request for performing the operation.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Shared.Protocol.HttpRequestMessageFactory.Undelete(System.Uri,System.Nullable{System.Int32},Microsoft.Azure.Storage.Core.UriQueryBuilder,System.Net.Http.HttpContent,Microsoft.Azure.Storage.OperationContext,Microsoft.Azure.Storage.Core.Auth.ICanonicalizer,Microsoft.Azure.Storage.Auth.StorageCredentials)">
            <summary>
            Undeletes the specified URI.
            </summary>
            <param name="uri">The URI of the resource to delete.</param>
            <param name="timeout">The timeout.</param>
            <param name="builder">The builder.</param>
            <returns>A web request for performing the operation.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Shared.Protocol.HttpRequestMessageFactory.GetAccountProperties(System.Uri,Microsoft.Azure.Storage.Core.UriQueryBuilder,System.Nullable{System.Int32},System.Net.Http.HttpContent,Microsoft.Azure.Storage.OperationContext,Microsoft.Azure.Storage.Core.Auth.ICanonicalizer,Microsoft.Azure.Storage.Auth.StorageCredentials)">
            <summary>
            Creates a web request to get the properties of the account.
            </summary>
            <param name="uri">The absolute URI to the service.</param>
            <param name="builder">A <see cref="T:Microsoft.Azure.Storage.Core.UriQueryBuilder"/> object specifying additional parameters to add to the URI query string.</param>
            <param name="timeout">The server timeout interval.</param>
            <param name="useVersionHeader">A boolean value indicating whether to set the <i>x-ms-version</i> HTTP header.</param>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param>
            <returns>
            A web request to get the service properties.
            </returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Shared.Protocol.HttpRequestMessageFactory.GetServiceProperties(System.Uri,System.Nullable{System.Int32},Microsoft.Azure.Storage.OperationContext,Microsoft.Azure.Storage.Core.Auth.ICanonicalizer,Microsoft.Azure.Storage.Auth.StorageCredentials)">
            <summary>
            Creates a web request to get the properties of the service.
            </summary>
            <param name="uri">The absolute URI to the service.</param>
            <param name="timeout">The server timeout interval.</param>
            <returns>A web request to get the service properties.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Shared.Protocol.HttpRequestMessageFactory.SetServiceProperties(System.Uri,System.Nullable{System.Int32},System.Net.Http.HttpContent,Microsoft.Azure.Storage.OperationContext,Microsoft.Azure.Storage.Core.Auth.ICanonicalizer,Microsoft.Azure.Storage.Auth.StorageCredentials)">
            <summary>
            Creates a web request to set the properties of the service.
            </summary>
            <param name="uri">The absolute URI to the service.</param>
            <param name="timeout">The server timeout interval.</param>
            <returns>A web request to set the service properties.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Shared.Protocol.HttpRequestMessageFactory.GetServiceStats(System.Uri,System.Nullable{System.Int32},Microsoft.Azure.Storage.OperationContext,Microsoft.Azure.Storage.Core.Auth.ICanonicalizer,Microsoft.Azure.Storage.Auth.StorageCredentials)">
            <summary>
            Creates a web request to get the stats of the service.
            </summary>
            <param name="uri">The absolute URI to the service.</param>
            <param name="timeout">The server timeout interval.</param>
            <returns>A web request to get the service stats.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Shared.Protocol.HttpRequestMessageFactory.GetServiceUriQueryBuilder">
            <summary>
            Generates a query builder for building service requests.
            </summary>
            <returns>A <see cref="T:Microsoft.Azure.Storage.Core.UriQueryBuilder"/> for building service requests.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Shared.Protocol.HttpRequestParsers.GetContentType(System.Net.Http.HttpRequestMessage)">
            <summary>
            Gets an ETag from a request.
            </summary>
            <param name="request">The web request.</param>
            <returns>A quoted ETag string.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Shared.Protocol.HttpRequestParsers.GetContentRange(System.Net.Http.HttpRequestMessage)">
            <summary>
            Gets an ETag from a request.
            </summary>
            <param name="request">The web request.</param>
            <returns>A quoted ETag string.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Shared.Protocol.HttpRequestParsers.GetContentMD5(System.Net.Http.HttpRequestMessage)">
            <summary>
            Gets Content MD5 from a request.
            </summary>
            <param name="request">The web request.</param>
            <returns>A MD5 string.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Shared.Protocol.HttpRequestParsers.GetContentCRC64(System.Net.Http.HttpRequestMessage)">
            <summary>
            Gets Content CRC64 from a request.
            </summary>
            <param name="request">The web request.</param>
            <returns>A CRC64 string.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Shared.Protocol.HttpRequestParsers.GetContentLocation(System.Net.Http.HttpRequestMessage)">
            <summary>
            Gets an ETag from a request.
            </summary>
            <param name="request">The web request.</param>
            <returns>A quoted ETag string.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Shared.Protocol.HttpRequestParsers.GetContentLength(System.Net.Http.HttpRequestMessage)">
            <summary>
            Gets an ETag from a request.
            </summary>
            <param name="request">The web request.</param>
            <returns>A quoted ETag string.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Shared.Protocol.HttpRequestParsers.GetContentLanguage(System.Net.Http.HttpRequestMessage)">
            <summary>
            Gets an ETag from a request.
            </summary>
            <param name="request">The web request.</param>
            <returns>A quoted ETag string.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Shared.Protocol.HttpRequestParsers.GetContentEncoding(System.Net.Http.HttpRequestMessage)">
            <summary>
            Gets an ETag from a request.
            </summary>
            <param name="request">The web request.</param>
            <returns>A quoted ETag string.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Shared.Protocol.HttpRequestParsers.GetContentDisposition(System.Net.Http.HttpRequestMessage)">
            <summary>
            Gets an ETag from a request.
            </summary>
            <param name="request">The web request.</param>
            <returns>A quoted ETag string.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Shared.Protocol.HttpResponseParsers.ToUTCTime(System.String)">
            <summary>
            Converts a string to UTC time.
            </summary>
            <param name="str">The string to convert.</param>
            <returns>A UTC representation of the string.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Shared.Protocol.HttpResponseParsers.ReadAccountProperties(System.Net.Http.HttpResponseMessage)">
            <summary>
            Reads account properties from an HttpResponseMessage object.
            </summary>
            <param name="response">The response from which to read the account properties.</param>
            <returns>The account properties stored in the headers.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Shared.Protocol.HttpResponseParsers.ReadServicePropertiesAsync(System.IO.Stream,System.Threading.CancellationToken)">
            <summary>
            Reads service properties from a stream.
            </summary>
            <param name="inputStream">The stream from which to read the service properties.</param>
            <returns>The service properties stored in the stream.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Shared.Protocol.HttpResponseParsers.ReadServiceStatsAsync(System.IO.Stream,System.Threading.CancellationToken)">
            <summary>
            Reads service stats from a stream.
            </summary>
            <param name="inputStream">The stream from which to read the service stats.</param>
            <returns>The service stats stored in the stream.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Shared.Protocol.HttpResponseParsers.ParseServerRequestEncrypted(System.Net.Http.HttpResponseMessage)">
            <summary>
            Parses the server request encrypted response header.
            </summary>
            <param name="response">Response to be parsed.</param>
            <returns><c>true</c> if write content was encrypted by service or <c>false</c> if not.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Shared.Protocol.HttpResponseParsers.GetMetadataOrProperties(System.Net.Http.HttpResponseMessage,System.String)">
            <summary>
            Gets the metadata or properties.
            </summary>
            <param name="response">The response from server.</param>
            <param name="prefix">The prefix for all the headers.</param>
            <returns>A <see cref="!:IDictionary"/> of the headers with the prefix.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Shared.Protocol.HttpResponseParsers.GetContentType(System.Net.Http.HttpResponseMessage)">
            <summary>
            Gets an ETag from a response.
            </summary>
            <param name="response">The web response.</param>
            <returns>A quoted ETag string.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Shared.Protocol.HttpResponseParsers.GetContentRange(System.Net.Http.HttpResponseMessage)">
            <summary>
            Gets an ETag from a response.
            </summary>
            <param name="response">The web response.</param>
            <returns>A quoted ETag string.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Shared.Protocol.HttpResponseParsers.GetContentMD5(System.Net.Http.HttpResponseMessage)">
            <summary>
            Gets an ContentMD5 from a response.
            </summary>
            <param name="response">The web response.</param>
            <returns>A ContentMD5 string.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Shared.Protocol.HttpResponseParsers.GetContentCRC64(System.Net.Http.HttpResponseMessage)">
            <summary>
            Gets an ContentCRC64 from a response.
            </summary>
            <param name="response">The web response.</param>
            <returns>A ContentCRC64 string.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Shared.Protocol.HttpResponseParsers.GetContentLocation(System.Net.Http.HttpResponseMessage)">
            <summary>
            Gets an ETag from a response.
            </summary>
            <param name="response">The web response.</param>
            <returns>A quoted ETag string.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Shared.Protocol.HttpResponseParsers.GetContentLength(System.Net.Http.HttpResponseMessage)">
            <summary>
            Gets an ETag from a response.
            </summary>
            <param name="response">The web response.</param>
            <returns>A quoted ETag string.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Shared.Protocol.HttpResponseParsers.GetContentLanguage(System.Net.Http.HttpResponseMessage)">
            <summary>
            Gets an ETag from a response.
            </summary>
            <param name="response">The web response.</param>
            <returns>A quoted ETag string.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Shared.Protocol.HttpResponseParsers.GetContentEncoding(System.Net.Http.HttpResponseMessage)">
            <summary>
            Gets an ETag from a response.
            </summary>
            <param name="response">The web response.</param>
            <returns>A quoted ETag string.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Shared.Protocol.HttpResponseParsers.GetContentDisposition(System.Net.Http.HttpResponseMessage)">
            <summary>
            Gets an ETag from a response.
            </summary>
            <param name="response">The web response.</param>
            <returns>A quoted ETag string.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Shared.Protocol.HttpResponseParsers.GetLastModifiedTime(System.Net.Http.HttpResponseMessage)">
            <summary>
            Gets an ETag from a response.
            </summary>
            <param name="response">The web response.</param>
            <returns>A quoted ETag string.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Shared.Protocol.HttpResponseParsers.GetMetadata(System.Net.Http.HttpResponseMessage)">
            <summary>
            Gets the user-defined metadata.
            </summary>
            <param name="response">The response from server.</param>
            <returns>A <see cref="T:System.Collections.IDictionary"/> of the metadata.</returns>
        </member>
        <member name="T:Microsoft.Azure.Storage.Shared.Protocol.ListingContext">
            <summary>
            Represents the listing context for enumeration operations.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.ListingContext.maxResults">
            <summary>
            Stores the maximum number of results to list. Must be null or a value between 1 and 5000.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.Shared.Protocol.ListingContext.#ctor(System.String,System.Nullable{System.Int32})">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Azure.Storage.Shared.Protocol.ListingContext"/> class.
            </summary>
            <param name="prefix">The resource name prefix.</param>
            <param name="maxResults">The maximum number of resources to return in a single operation, up to the per-operation limit of 5000.</param>
        </member>
        <member name="P:Microsoft.Azure.Storage.Shared.Protocol.ListingContext.Prefix">
            <summary>
            Gets or sets the Prefix value.
            </summary>
            <value>The Prefix value.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.Shared.Protocol.ListingContext.MaxResults">
            <summary>
            Gets or sets the MaxResults value.
            </summary>
            <value>The MaxResults value.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.Shared.Protocol.ListingContext.Marker">
            <summary>
            Gets or sets the Marker value.
            </summary>
            <value>The Marker value.</value>
        </member>
        <member name="T:Microsoft.Azure.Storage.Shared.Protocol.LoggingOperations">
            <summary>
            Enumeration representing the state of logging in a service.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.LoggingOperations.None">
            <summary>
            Logging is disabled.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.LoggingOperations.Read">
            <summary>
            Log read operations.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.LoggingOperations.Write">
            <summary>
            Log write operations.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.LoggingOperations.Delete">
            <summary>
            Log delete operations.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.LoggingOperations.All">
            <summary>
            Log all operations.
            </summary>
        </member>
        <member name="T:Microsoft.Azure.Storage.Shared.Protocol.LoggingProperties">
            <summary>
            Class representing the service properties pertaining to logging.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.Shared.Protocol.LoggingProperties.#ctor">
            <summary>
            Initializes a new instance of the LoggingProperties class.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.Shared.Protocol.LoggingProperties.#ctor(System.String)">
            <summary>
            Initializes a new instance of the LoggingProperties class.
            </summary>
        </member>
        <member name="P:Microsoft.Azure.Storage.Shared.Protocol.LoggingProperties.Version">
            <summary>
            Gets or sets the version of the analytics service.
            </summary>
            <value>A string identifying the version of the service.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.Shared.Protocol.LoggingProperties.LoggingOperations">
            <summary>
            Gets or sets the state of logging.
            </summary>
            <value>A combination of <see cref="P:Microsoft.Azure.Storage.Shared.Protocol.LoggingProperties.LoggingOperations"/> flags describing the operations that are logged.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.Shared.Protocol.LoggingProperties.RetentionDays">
            <summary>
            Gets or sets the logging retention policy.
            </summary>
            <value>The number of days to retain the logs.</value>
        </member>
        <member name="T:Microsoft.Azure.Storage.Shared.Protocol.MetricsLevel">
            <summary>
            Enumeration representing the state of metrics collection in a service.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.MetricsLevel.None">
            <summary>
            Metrics collection is disabled.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.MetricsLevel.Service">
            <summary>
            Service-level metrics collection is enabled.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.MetricsLevel.ServiceAndApi">
            <summary>
            Service-level and API metrics collection are enabled.
            </summary>
        </member>
        <member name="T:Microsoft.Azure.Storage.Shared.Protocol.MetricsProperties">
            <summary>
            Class representing the service properties pertaining to metrics.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.Shared.Protocol.MetricsProperties.#ctor">
            <summary>
            Initializes a new instance of the MetricsProperties class.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.Shared.Protocol.MetricsProperties.#ctor(System.String)">
            <summary>
            Initializes a new instance of the MetricsProperties class.
            </summary>
        </member>
        <member name="P:Microsoft.Azure.Storage.Shared.Protocol.MetricsProperties.Version">
            <summary>
            Gets or sets the version of the analytics service.
            </summary>
            <value>A string identifying the version of the service.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.Shared.Protocol.MetricsProperties.MetricsLevel">
            <summary>
            Gets or sets the state of metrics collection.
            </summary>
            <value>A <see cref="P:Microsoft.Azure.Storage.Shared.Protocol.MetricsProperties.MetricsLevel"/> value indicating which metrics to collect, if any.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.Shared.Protocol.MetricsProperties.RetentionDays">
            <summary>
            Gets or sets the logging retention policy.
            </summary>
            <value>The number of days to retain the logs.</value>
        </member>
        <member name="M:Microsoft.Azure.Storage.Shared.Protocol.Request.ConvertDateTimeToSnapshotString(System.DateTimeOffset)">
            <summary>
            Converts the date time to snapshot string.
            </summary>
            <param name="dateTime">The date time.</param>
            <returns>The converted string.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Shared.Protocol.Request.WriteSharedAccessIdentifiers``1(System.Collections.Generic.IDictionary{System.String,``0},System.IO.Stream,System.Action{``0,System.Xml.XmlWriter})">
            <summary>
            Writes a collection of shared access policies to the specified stream in XML format.
            </summary>
            <param name="sharedAccessPolicies">A collection of shared access policies.</param>
            <param name="outputStream">An output stream.</param>
            <param name="writePolicyXml">A delegate that writes a policy to an XML writer.</param>
            <typeparam name="T">The type of policy to write.</typeparam>
        </member>
        <member name="M:Microsoft.Azure.Storage.Shared.Protocol.RequestMessageExtensions.AddLeaseId(Microsoft.Azure.Storage.Core.StorageRequestMessage,System.String)">
            <summary>
            Adds the lease id.
            </summary>
            <param name="request">The request.</param>
            <param name="leaseId">The lease id.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.Shared.Protocol.RequestMessageExtensions.AddOptionalHeader(Microsoft.Azure.Storage.Core.StorageRequestMessage,System.String,System.String)">
            <summary>
            Adds the optional header.
            </summary>
            <param name="request">The web request.</param>
            <param name="name">The metadata name.</param>
            <param name="value">The metadata value.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.Shared.Protocol.RequestMessageExtensions.AddOptionalHeader(Microsoft.Azure.Storage.Core.StorageRequestMessage,System.String,System.Nullable{System.Int32})">
            <summary>
            Adds an optional header to a request.
            </summary>
            <param name="request">The web request.</param>
            <param name="name">The header name.</param>
            <param name="value">The header value.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.Shared.Protocol.RequestMessageExtensions.AddOptionalHeader(Microsoft.Azure.Storage.Core.StorageRequestMessage,System.String,System.Nullable{System.Int64})">
            <summary>
            Adds an optional header to a request.
            </summary>
            <param name="request">The web request.</param>
            <param name="name">The header name.</param>
            <param name="value">The header value.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.Shared.Protocol.RequestMessageExtensions.ApplyLeaseId(Microsoft.Azure.Storage.Core.StorageRequestMessage,Microsoft.Azure.Storage.AccessCondition)">
            <summary>
            Applies the lease condition to the web request.
            </summary>
            <param name="request">The request to be modified.</param>
            <param name="accessCondition">Access condition to be added to the request.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.Shared.Protocol.RequestMessageExtensions.ApplySequenceNumberCondition(Microsoft.Azure.Storage.Core.StorageRequestMessage,Microsoft.Azure.Storage.AccessCondition)">
            <summary>
            Applies the sequence number condition to the web request.
            </summary>
            <param name="request">The request to be modified.</param>
            <param name="accessCondition">Access condition to be added to the request.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.Shared.Protocol.RequestMessageExtensions.ApplyAccessCondition(Microsoft.Azure.Storage.Core.StorageRequestMessage,Microsoft.Azure.Storage.AccessCondition)">
            <summary>
            Applies the condition to the web request.
            </summary>
            <param name="request">The request to be modified.</param>
            <param name="accessCondition">Access condition to be added to the request.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.Shared.Protocol.RequestMessageExtensions.ApplyAppendCondition(Microsoft.Azure.Storage.Core.StorageRequestMessage,Microsoft.Azure.Storage.AccessCondition)">
            <summary>
            Applies the append condition to the web request.
            </summary>
            <param name="request">The request to be modified.</param>
            <param name="accessCondition">Access condition to be added to the request.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.Shared.Protocol.RequestMessageExtensions.ApplyAccessConditionToSource(Microsoft.Azure.Storage.Core.StorageRequestMessage,Microsoft.Azure.Storage.AccessCondition)">
            <summary>
            Applies the condition for a source blob to the web request.
            </summary>
            <param name="request">The request to be modified.</param>
            <param name="accessCondition">Access condition to be added to the request.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.Shared.Protocol.Response.ReadSharedAccessIdentifiersAsync``1(System.Collections.Generic.IDictionary{System.String,``0},Microsoft.Azure.Storage.Shared.Protocol.AccessPolicyResponseBase{``0},System.Threading.CancellationToken)">
            <summary>
            Reads a collection of shared access policies from the specified <see cref="T:Microsoft.Azure.Storage.Shared.Protocol.AccessPolicyResponseBase`1"/> object.
            </summary>
            <param name="sharedAccessPolicies">A collection of shared access policies to be filled.</param>
            <param name="policyResponse">A policy response object for reading the stream.</param>
            <typeparam name="T">The type of policy to read.</typeparam>
        </member>
        <member name="M:Microsoft.Azure.Storage.Shared.Protocol.Response.ParseMetadata(System.Xml.XmlReader)">
            <summary>
            Parses the metadata.
            </summary>
            <param name="reader">The reader.</param>
            <returns>A <see cref="T:System.Collections.IDictionary"/> of metadata.</returns>
            <remarks>
            Precondition: reader at &lt;Metadata&gt;
            Postcondition: reader after &lt;/Metadata&gt; (&lt;Metadata/&gt; consumed)
            </remarks>
        </member>
        <member name="M:Microsoft.Azure.Storage.Shared.Protocol.Response.ParseMetadataAsync(System.Xml.XmlReader)">
            <summary>
            Parses the metadata.
            </summary>
            <param name="reader">The reader.</param>
            <returns>A <see cref="T:System.Collections.IDictionary"/> of metadata.</returns>
            <remarks>
            Precondition: reader at &lt;Metadata&gt;
            Postcondition: reader after &lt;/Metadata&gt; (&lt;Metadata/&gt; consumed)
            </remarks>
        </member>
        <member name="T:Microsoft.Azure.Storage.Shared.Protocol.ResponseParsingBase`1">
            <summary>
            Provides a base class that is used internally to parse XML streams from storage service operations.
            </summary>
            <typeparam name="T">The type to be parsed.</typeparam>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.ResponseParsingBase`1.allObjectsParsed">
            <summary>
            Indicates that all parsable objects have been consumed. This field is reserved and should not be used.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.ResponseParsingBase`1.outstandingObjectsToParse">
            <summary>
            Stores any objects that have not yet been parsed. This field is reserved and should not be used.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.ResponseParsingBase`1.reader">
            <summary>
            The reader used for parsing. This field is reserved and should not be used.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.ResponseParsingBase`1.parser">
            <summary>
            The IEnumerator over the parsed content.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.ResponseParsingBase`1.enumerableConsumed">
            <summary>
            Used to make sure that parsing is only done once, since a stream is not re-entrant.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.Shared.Protocol.ResponseParsingBase`1.#ctor(System.IO.Stream)">
            <summary>
            Initializes a new instance of the ResponseParsingBase class.
            </summary>
            <param name="stream">The stream to be parsed.</param>
        </member>
        <member name="P:Microsoft.Azure.Storage.Shared.Protocol.ResponseParsingBase`1.ObjectsToParse">
            <summary>
            Gets the parsable objects. This method is reserved and should not be used.
            </summary>
            <value>The objects to parse.</value>
        </member>
        <member name="M:Microsoft.Azure.Storage.Shared.Protocol.ResponseParsingBase`1.Dispose">
            <summary>
            Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources. 
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.Shared.Protocol.ResponseParsingBase`1.ParseXml">
            <summary>
            Parses the XML response. This method is reserved and should not be used.
            </summary>
            <returns>A collection of enumerable objects.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Shared.Protocol.ResponseParsingBase`1.Dispose(System.Boolean)">
            <summary>
            Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources, and optional
            managed resources.
            </summary>
            <param name="disposing"><c>True</c> to release both managed and unmanaged resources; otherwise, <c>false</c>.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.Shared.Protocol.ResponseParsingBase`1.Variable(System.Boolean@)">
            <summary>
            This method is reserved and should not be used.
            </summary>
            <param name="consumable"><c>True</c> when the object is consumable.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.Shared.Protocol.ResponseParsingBase`1.ParseXmlAndClose">
            <summary>
            Parses the XML and close.
            </summary>
            <returns>A list of parsed results.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Shared.Protocol.RetryableStreamContent.#ctor(System.IO.Stream)">
            <summary>
            Creates a new instance of the RetryableStreamContent class.
            </summary>
            <param name="content">The content used to initialize the RetryableStreamContent.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.Shared.Protocol.RetryableStreamContent.#ctor(System.IO.Stream,System.Int32)">
            <summary>
            Creates a new instance of the RetryableStreamContent class.
            </summary>
            <param name="content">The content used to initialize the RetryableStreamContent.</param>
            <param name="bufferSize">The size, in bytes, of the buffer for the RetryableStreamContent.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.Shared.Protocol.RetryableStreamContent.Dispose(System.Boolean)">
            <summary>
            Ignores the request and does not call base.Dispose.
            </summary>
            <param name="disposing">Not used.</param>
        </member>
        <member name="T:Microsoft.Azure.Storage.Shared.Protocol.ServiceProperties">
            <summary>
            Class representing a set of properties pertaining to a cloud storage service.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.ServiceProperties.StorageServicePropertiesName">
            <summary>
            The name of the root XML element.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.ServiceProperties.LoggingName">
            <summary>
            The name of the logging XML element.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.ServiceProperties.HourMetricsName">
            <summary>
            The name of the metrics XML element.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.ServiceProperties.CorsName">
            <summary>
            The name of the CORS XML element.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.ServiceProperties.MinuteMetricsName">
            <summary>
            The name of the minute metrics XML element.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.ServiceProperties.DeleteRetentionPolicyName">
            <summary>
            The name of the delete retention policy XML element.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.ServiceProperties.VersionName">
            <summary>
            The name of the version XML element.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.ServiceProperties.DeleteName">
            <summary>
            The name of the delete operation XML element.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.ServiceProperties.ReadName">
            <summary>
            The name of the read operation XML element.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.ServiceProperties.WriteName">
            <summary>
            The name of the write operation XML element.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.ServiceProperties.RetentionPolicyName">
            <summary>
            The name of the retention policy XML element.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.ServiceProperties.EnabledName">
            <summary>
            The name of the enabled XML element.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.ServiceProperties.DaysName">
            <summary>
            The name of the days XML element.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.ServiceProperties.IncludeApisName">
            <summary>
            The name of the include APIs XML element.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.ServiceProperties.DefaultServiceVersionName">
            <summary>
            The name of the default service version XML element.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.ServiceProperties.CorsRuleName">
            <summary>
            The name of the CORS Rule XML element.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.ServiceProperties.AllowedOriginsName">
            <summary>
            The name of the Allowed Origin XML element.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.ServiceProperties.AllowedMethodsName">
            <summary>
            The name of the Allowed Method XML element.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.ServiceProperties.MaxAgeInSecondsName">
            <summary>
            The name of the Maximum Age XML element.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.ServiceProperties.ExposedHeadersName">
            <summary>
            The name of the Exposed Headers XML element.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.ServiceProperties.AllowedHeadersName">
            <summary>
            The name of the Allowed Headers XML element.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.ServiceProperties.StaticWebsiteName">
            <summary>
            Name of the Static Website XML element that groups the static website-related properties.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.ServiceProperties.StaticWebsiteEnabledName">
            <summary>
            Name of the Enabled XML element under static website properties.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.ServiceProperties.StaticWebsiteIndexDocumentName">
            <summary>
            Name of the Index Document XML element under static website properties.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.ServiceProperties.StaticWebsiteErrorDocument404PathName">
            <summary>
            Name of the Error Document 404 Path XML element under static website properties.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.ServiceProperties.RetainedVersionsPerBlob">
            <summary>
            The name of the RetainedVersionsPerBlob XML element.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.Shared.Protocol.ServiceProperties.#ctor">
            <summary>
            Initializes a new instance of the ServiceProperties class.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.Shared.Protocol.ServiceProperties.#ctor(Microsoft.Azure.Storage.Shared.Protocol.LoggingProperties,Microsoft.Azure.Storage.Shared.Protocol.MetricsProperties,Microsoft.Azure.Storage.Shared.Protocol.MetricsProperties,Microsoft.Azure.Storage.Shared.Protocol.CorsProperties,Microsoft.Azure.Storage.Shared.Protocol.DeleteRetentionPolicy)">
            <summary>
            Initializes a new instance of the ServiceProperties class.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.Shared.Protocol.ServiceProperties.#ctor(Microsoft.Azure.Storage.Shared.Protocol.LoggingProperties,Microsoft.Azure.Storage.Shared.Protocol.MetricsProperties,Microsoft.Azure.Storage.Shared.Protocol.MetricsProperties,Microsoft.Azure.Storage.Shared.Protocol.CorsProperties,Microsoft.Azure.Storage.Shared.Protocol.DeleteRetentionPolicy,Microsoft.Azure.Storage.Shared.Protocol.StaticWebsiteProperties)">
            <summary>
            Initializes a new instance of the ServiceProperties class.
            </summary>
        </member>
        <member name="P:Microsoft.Azure.Storage.Shared.Protocol.ServiceProperties.Logging">
            <summary>
            Gets or sets the logging properties.
            </summary>
            <value>The logging properties.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.Shared.Protocol.ServiceProperties.HourMetrics">
            <summary>
            Gets or sets the hour metrics properties.
            </summary>
            <value>The metrics properties.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.Shared.Protocol.ServiceProperties.Cors">
            <summary>
            Gets or sets the Cross Origin Resource Sharing (CORS) properties.
            </summary>
            <value>The CORS properties.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.Shared.Protocol.ServiceProperties.MinuteMetrics">
            <summary>
            Gets or sets the minute metrics properties.
            </summary>
            <value>The minute metrics properties.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.Shared.Protocol.ServiceProperties.DefaultServiceVersion">
            <summary>
            Gets or sets the default service version.
            </summary>
            <value>The default service version identifier.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.Shared.Protocol.ServiceProperties.DeleteRetentionPolicy">
            <summary>
            Gets or sets the delete retention policy.
            </summary>
            <value>The delete retention policy.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.Shared.Protocol.ServiceProperties.StaticWebsite">
            <summary>
            Gets or sets the Static Website properties
            </summary>
            <value>The Static Website properties</value>
        </member>
        <member name="M:Microsoft.Azure.Storage.Shared.Protocol.ServiceProperties.FromServiceXml(System.Xml.Linq.XDocument)">
            <summary>
            Constructs a <c>ServiceProperties</c> object from an XML document received from the service.
            </summary>
            <param name="servicePropertiesDocument">The XML document.</param>
            <returns>A <c>ServiceProperties</c> object containing the properties in the XML document.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Shared.Protocol.ServiceProperties.ToServiceXml">
            <summary>
            Converts these properties into XML for communicating with the service.
            </summary>
            <returns>An XML document containing the service properties.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Shared.Protocol.ServiceProperties.GenerateRetentionPolicyXml(System.Nullable{System.Int32})">
            <summary>
            Generates XML representing the given retention policy.
            </summary>
            <param name="retentionDays">The number of days to retain, or <c>null</c> if the policy is disabled.</param>
            <returns>An XML retention policy element.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Shared.Protocol.ServiceProperties.GenerateMetricsXml(Microsoft.Azure.Storage.Shared.Protocol.MetricsProperties,System.String)">
            <summary>
            Generates XML representing the given metrics properties.
            </summary>
            <param name="metrics">The metrics properties.</param>
            <param name="metricsName">The XML name for these metrics.</param>
            <returns>An XML metrics element.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Shared.Protocol.ServiceProperties.GenerateLoggingXml(Microsoft.Azure.Storage.Shared.Protocol.LoggingProperties)">
            <summary>
            Generates XML representing the given logging properties.
            </summary>
            <param name="logging">The logging properties.</param>
            <returns>An XML logging element.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Shared.Protocol.ServiceProperties.GenerateCorsXml(Microsoft.Azure.Storage.Shared.Protocol.CorsProperties)">
            <summary>
            Generates XML representing the given CORS properties.
            </summary>
            <param name="cors">The CORS properties.</param>
            <returns>An XML logging element.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Shared.Protocol.ServiceProperties.GenerateDeleteRetentionPolicyXml(Microsoft.Azure.Storage.Shared.Protocol.DeleteRetentionPolicy)">
            <summary>
            Generates XML representing the given delete retention policy.
            </summary>
            <param name="deleteRetentionPolicy">The DeleteRetentionPolicy properties.</param>
            <returns>An XML logging element.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Shared.Protocol.ServiceProperties.GenerateStaticWebsitePropertiesXml(Microsoft.Azure.Storage.Shared.Protocol.StaticWebsiteProperties)">
            <summary>
            Generates XML representing the given static website properties.
            </summary>
            <param name="staticWebsiteProperties">The static website properties.</param>
            <returns>An XML element corresponding to the input properties.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Shared.Protocol.ServiceProperties.ReadLoggingPropertiesFromXml(System.Xml.Linq.XElement)">
            <summary>
            Constructs a <c>LoggingProperties</c> object from an XML element.
            </summary>
            <param name="element">The XML element.</param>
            <returns>A <c>LoggingProperties</c> object containing the properties in the element.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Shared.Protocol.ServiceProperties.ReadMetricsPropertiesFromXml(System.Xml.Linq.XElement)">
            <summary>
            Constructs a <c>MetricsProperties</c> object from an XML element.
            </summary>
            <param name="element">The XML element.</param>
            <returns>A <c>MetricsProperties</c> object containing the properties in the element.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Shared.Protocol.ServiceProperties.ReadRetentionPolicyFromXml(System.Xml.Linq.XElement)">
            <summary>
            Constructs a retention policy (number of days) from an XML element.
            </summary>
            <param name="element">The XML element.</param>
            <returns>The number of days to retain, or <c>null</c> if retention is disabled.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Shared.Protocol.ServiceProperties.ReadCorsPropertiesFromXml(System.Xml.Linq.XElement)">
            <summary>
            Constructs a <c>CorsProperties</c> object from an XML element.
            </summary>
            <param name="element">The XML element.</param>
            <returns>A <c>CorsProperties</c> object containing the properties in the element.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Shared.Protocol.ServiceProperties.ReadDeleteRetentionPolicyFromXml(System.Xml.Linq.XElement)">
            <summary>
            Constructs a <c>DeleteRetentionPolicy</c> object from an XML element.
            </summary>
            <param name="element">the XML element</param>
            <returns>A <c>DeleteRetentionPolicy</c> object containing the properties in the element</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Shared.Protocol.ServiceProperties.ReadStaticWebsitePropertiesFromXml(System.Xml.Linq.XElement)">
            <summary>
            Constructs a <c>StaticWebsiteProperties</c> object from an XML element.
            </summary>
            <param name="element">the XML element</param>
            <returns>A <c>StaticWebsiteProperties</c> object containing the properties in the element</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Shared.Protocol.ServiceProperties.WriteServiceProperties(System.IO.Stream)">
            <summary>
            Writes service properties to a stream, formatted in XML.
            </summary>
            <param name="outputStream">The stream to which the formatted properties are to be written.</param>
        </member>
        <member name="T:Microsoft.Azure.Storage.Shared.Protocol.ServiceStats">
            <summary>
            Class representing a set of stats pertaining to a cloud storage service.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.ServiceStats.StorageServiceStatsName">
            <summary>
            The name of the root XML element.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.ServiceStats.GeoReplicationName">
            <summary>
            The name of the geo-replication XML element.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.Shared.Protocol.ServiceStats.#ctor">
            <summary>
            Initializes a new instance of the ServiceStats class.
            </summary>
        </member>
        <member name="P:Microsoft.Azure.Storage.Shared.Protocol.ServiceStats.GeoReplication">
            <summary>
            Gets or sets the geo-replication stats.
            </summary>
            <value>The geo-replication stats.</value>
        </member>
        <member name="M:Microsoft.Azure.Storage.Shared.Protocol.ServiceStats.FromServiceXml(System.Xml.Linq.XDocument)">
            <summary>
            Constructs a <c>ServiceStats</c> object from an XML document received from the service.
            </summary>
            <param name="serviceStatsDocument">The XML document.</param>
            <returns>A <c>ServiceStats</c> object containing the properties in the XML document.</returns>
        </member>
        <member name="T:Microsoft.Azure.Storage.Shared.Protocol.StaticWebsiteProperties">
            <summary>
            Class representing the service properties pertaining to StaticWebsites
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.Shared.Protocol.StaticWebsiteProperties.#ctor">
            <summary>
            Initializes a new instance of the StaticWebsiteProperties class.
            </summary>
            <remarks>"Enabled" defaults to false.</remarks>
        </member>
        <member name="P:Microsoft.Azure.Storage.Shared.Protocol.StaticWebsiteProperties.Enabled">
            <summary>
            True if static websites should be enabled on the blob service for the corresponding Storage Account.
            </summary>
        </member>
        <member name="P:Microsoft.Azure.Storage.Shared.Protocol.StaticWebsiteProperties.IndexDocument">
            <summary>
            Gets or sets a string representing the name of the index document in each directory.
            </summary>
            <remarks>This is commonly "index.html".</remarks>
        </member>
        <member name="P:Microsoft.Azure.Storage.Shared.Protocol.StaticWebsiteProperties.ErrorDocument404Path">
            <summary>
            Gets or sets a string representing the path to the error document that should be shown when a 404 is issued
            (meaning, when a browser requests a page that does not exist.)
            </summary>
            <example>path/to/error/404.html</example>
        </member>
        <member name="T:Microsoft.Azure.Storage.Shared.Protocol.StorageErrorCodeStrings">
            <summary>
            Provides error code strings that are common to all storage services.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.StorageErrorCodeStrings.UnsupportedHttpVerb">
            <summary>
            The specified HTTP verb is not supported.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.StorageErrorCodeStrings.MissingContentLengthHeader">
            <summary>
            The Content-Length header is required for this request.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.StorageErrorCodeStrings.MissingRequiredHeader">
            <summary>
            A required header was missing.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.StorageErrorCodeStrings.MissingRequiredXmlNode">
            <summary>
            A required XML node was missing.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.StorageErrorCodeStrings.UnsupportedHeader">
            <summary>
            One or more header values are not supported.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.StorageErrorCodeStrings.UnsupportedXmlNode">
            <summary>
            One or more XML nodes are not supported.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.StorageErrorCodeStrings.InvalidHeaderValue">
            <summary>
            One or more header values are invalid.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.StorageErrorCodeStrings.InvalidXmlNodeValue">
            <summary>
            One or more XML node values are invalid.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.StorageErrorCodeStrings.MissingRequiredQueryParameter">
            <summary>
            A required query parameter is missing.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.StorageErrorCodeStrings.UnsupportedQueryParameter">
            <summary>
            One or more query parameters is not supported.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.StorageErrorCodeStrings.InvalidQueryParameterValue">
            <summary>
            One or more query parameters are invalid.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.StorageErrorCodeStrings.OutOfRangeQueryParameterValue">
            <summary>
            One or more query parameters are out of range.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.StorageErrorCodeStrings.InvalidUri">
            <summary>
            The URI is invalid.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.StorageErrorCodeStrings.InvalidHttpVerb">
            <summary>
            The HTTP verb is invalid.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.StorageErrorCodeStrings.EmptyMetadataKey">
            <summary>
            The metadata key is empty.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.StorageErrorCodeStrings.RequestBodyTooLarge">
            <summary>
            The request body is too large.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.StorageErrorCodeStrings.InvalidXmlDocument">
            <summary>
            The specified XML document is invalid.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.StorageErrorCodeStrings.InternalError">
            <summary>
            An internal error occurred.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.StorageErrorCodeStrings.AuthenticationFailed">
            <summary>
            Authentication failed.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.StorageErrorCodeStrings.Md5Mismatch">
            <summary>
            The specified MD5 hash does not match the server value.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.StorageErrorCodeStrings.Crc64Mismatch">
            <summary>
            The specified CRC64 hash does not match the server value.
            </summary>      
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.StorageErrorCodeStrings.InvalidMd5">
            <summary>
            The specified MD5 hash is invalid.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.StorageErrorCodeStrings.InvalidCrc64">
            <summary>
            The specified CRC64 hash is invalid.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.StorageErrorCodeStrings.OutOfRangeInput">
            <summary>
            The input is out of range.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.StorageErrorCodeStrings.InvalidInput">
            <summary>
            The input is invalid.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.StorageErrorCodeStrings.OperationTimedOut">
            <summary>
            The operation timed out.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.StorageErrorCodeStrings.ResourceNotFound">
            <summary>
            The specified resource was not found.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.StorageErrorCodeStrings.InvalidMetadata">
            <summary>
            The specified metadata is invalid.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.StorageErrorCodeStrings.MetadataTooLarge">
            <summary>
            The specified metadata is too large.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.StorageErrorCodeStrings.ConditionNotMet">
            <summary>
            The specified condition was not met.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.StorageErrorCodeStrings.InvalidRange">
            <summary>
            The specified range is invalid.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.StorageErrorCodeStrings.ContainerNotFound">
            <summary>
            The specified container was not found.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.StorageErrorCodeStrings.ContainerAlreadyExists">
            <summary>
            The specified container already exists.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.StorageErrorCodeStrings.ContainerDisabled">
            <summary>
            The specified container is disabled.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.StorageErrorCodeStrings.ContainerBeingDeleted">
            <summary>
            The specified container is being deleted.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.StorageErrorCodeStrings.ServerBusy">
            <summary>
            The server is busy.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.StorageErrorCodeStrings.RequestUrlFailedToParse">
            <summary>
            The url in the request could not be parsed.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.StorageErrorCodeStrings.InvalidAuthenticationInfo">
            <summary>
            The authentication information was not provided in the correct format. Verify the value of Authorization header.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.StorageErrorCodeStrings.InvalidResourceName">
            <summary>
            The specified resource name contains invalid characters.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.StorageErrorCodeStrings.ConditionHeadersNotSupported">
            <summary>
            Condition headers are not supported.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.StorageErrorCodeStrings.MultipleConditionHeadersNotSupported">
            <summary>
            Multiple condition headers are not supported.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.StorageErrorCodeStrings.InsufficientAccountPermissions">
            <summary>
            Read-access geo-redundant replication is not enabled for the account, write operations to the secondary location are not allowed, 
            or the account being accessed does not have sufficient permissions to execute this operation.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.StorageErrorCodeStrings.AccountIsDisabled">
            <summary>
            The specified account is disabled.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.StorageErrorCodeStrings.AccountAlreadyExists">
            <summary>
            The specified account already exists.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.StorageErrorCodeStrings.AccountBeingCreated">
            <summary>
            The specified account is in the process of being created.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.StorageErrorCodeStrings.ResourceAlreadyExists">
            <summary>
            The specified resource already exists.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.Shared.Protocol.StorageErrorCodeStrings.ResourceTypeMismatch">
            <summary>
            The specified resource type does not match the type of the existing resource.
            </summary>
        </member>
        <member name="T:Microsoft.Azure.Storage.AccessCondition">
            <summary>
            Represents a set of access conditions to be used for operations against the storage services. 
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.AccessCondition.ifModifiedSinceDateTime">
            <summary>
            Time for IfModifiedSince.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.AccessCondition.ifNotModifiedSinceDateTime">
            <summary>
            Time for IfUnmodifiedSince.
            </summary>
        </member>
        <member name="P:Microsoft.Azure.Storage.AccessCondition.IfMatchETag">
            <summary>
            Gets or sets an ETag value for a condition specifying that the given ETag must match the ETag of the specified resource.
            </summary>
            <value>A string containing an ETag value, or <c>"*"</c> to match any ETag. If <c>null</c>, no condition exists.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.AccessCondition.IfNoneMatchETag">
            <summary>
            Gets or sets an ETag value for a condition specifying that the given ETag must not match the ETag of the specified resource.
            </summary>
            <value>A string containing an ETag value, or <c>"*"</c> to match any ETag. If <c>null</c>, no condition exists.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.AccessCondition.IfMatchContentCrc">
            <summary>
            Gets or sets a CRC64 value for a condition speifying that the given CRC64 must match the CRC64 of the specified resource.
            </summary>
            <value>A string containing a CRC64 value.  If <c>null</c>, no condition exists.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.AccessCondition.IfNoneMatchContentCrc">
            <summary>
            Gets or sets a CRC64 value for a condition specifying that the given CRC64 must not match the CRC64 of the specified resource.
            </summary>
            <value>A string containing a CRC64 value.  If <c>null</c>, no condition exists.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.AccessCondition.IfModifiedSinceTime">
            <summary>
            Gets or sets a <see cref="T:System.DateTimeOffset"/> value for a condition specifying a time since which a resource has been modified.
            </summary>
            <value>A <see cref="T:System.DateTimeOffset"/> value specified in UTC, or <c>null</c> if no condition exists.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.AccessCondition.IfNotModifiedSinceTime">
            <summary>
            Gets or sets a <see cref="T:System.DateTimeOffset"/> value for a condition specifying a time since which a resource has not been modified.
            </summary>
            <value>A <see cref="T:System.DateTimeOffset"/> value specified in UTC, or <c>null</c> if no condition exists.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.AccessCondition.IfMaxSizeLessThanOrEqual">
            <summary>
            Gets or sets a value for a condition that specifies the maximum size allowed for an append blob when a new block is committed. The append
            will succeed only if the size of the blob after the append operation is less than or equal to the specified size.
            </summary>
            <value>The maximum size in bytes, or <c>null</c> if no value is set.</value>
            <remarks>This condition only applies to append blobs.</remarks>
        </member>
        <member name="P:Microsoft.Azure.Storage.AccessCondition.IfAppendPositionEqual">
            <summary>
            Gets or sets a value for a condition specifying the byte offset to check for when committing a block to an append blob.
            The append will succeed only if the end position is equal to this number. 
            </summary>
            <value>An append position number, or <c>null</c> if no value is set.</value>
            <remarks>This condition only applies to append blobs.</remarks>
        </member>
        <member name="P:Microsoft.Azure.Storage.AccessCondition.IfSequenceNumberLessThanOrEqual">
            <summary>
            Gets or sets a value for a condition specifying that the current sequence number must be less than or equal to the specified value.
            </summary>
            <value>A sequence number, or <c>null</c> if no condition exists.</value>
            <remarks>This condition only applies to page blobs.</remarks>
        </member>
        <member name="P:Microsoft.Azure.Storage.AccessCondition.IfSequenceNumberLessThan">
            <summary>
            Gets or sets a value for a condition specifying that the current sequence number must be less than the specified value.
            </summary>
            <value>A sequence number, or <c>null</c> if no condition exists.</value>
            <remarks>This condition only applies to page blobs.</remarks>
        </member>
        <member name="P:Microsoft.Azure.Storage.AccessCondition.IfSequenceNumberEqual">
            <summary>
            Gets or sets a value for a condition specifying that the current sequence number must be equal to the specified value.
            </summary>
            <value>A sequence number, or <c>null</c> if no condition exists.</value>
            <remarks>This condition only applies to page blobs.</remarks>
        </member>
        <member name="P:Microsoft.Azure.Storage.AccessCondition.LeaseId">
            <summary>
            Gets or sets a lease ID that must match the lease on a resource.
            </summary>
            <value>A string containing a lease ID, or <c>null</c> if no condition exists.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.AccessCondition.IsConditional">
            <summary>
            Determines whether the access condition is one of the four conditional headers.
            </summary>
            <value><c>true</c> if the access condition is a conditional header; otherwise, <c>false</c>.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.AccessCondition.IsIfNotExists">
            <summary>
            Determines whether the access condition is IfNotExists.
            </summary>
            <value><c>true</c> if the access condition is a IfNotExists; otherwise, <c>false</c>.</value>
        </member>
        <member name="M:Microsoft.Azure.Storage.AccessCondition.RemoveIsIfNotExistsCondition">
            <summary>
            Remove the IfNotExists condition.
            </summary>
            <value>The reference to the <see cref="T:Microsoft.Azure.Storage.AccessCondition"/> is returned, to allow chained usage</value>
        </member>
        <member name="M:Microsoft.Azure.Storage.AccessCondition.Clone">
            <summary>
            Provide a shallow copy of the current access condition
            </summary>
            <returns>A shallow copy of the <see cref="T:Microsoft.Azure.Storage.AccessCondition"/> object</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.AccessCondition.GenerateEmptyCondition">
            <summary>
            Constructs an empty access condition.
            </summary>
            <returns>An empty <see cref="T:Microsoft.Azure.Storage.AccessCondition"/> object.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.AccessCondition.GenerateIfNotExistsCondition">
            <summary>
            Constructs an access condition such that an operation will be performed only if the resource does not exist.
            </summary>
            <returns>An <see cref="T:Microsoft.Azure.Storage.AccessCondition"/> object that represents a condition where a resource does not exist.</returns>
            <remarks>Setting this access condition modifies the request to include the HTTP <i>If-None-Match</i> conditional header.</remarks>
        </member>
        <member name="M:Microsoft.Azure.Storage.AccessCondition.GenerateIfExistsCondition">
            <summary>
            Constructs an access condition such that an operation will be performed only if the resource exists.
            </summary>
            <returns>An <see cref="T:Microsoft.Azure.Storage.AccessCondition"/> object that represents a condition where a resource exists.</returns>
            <remarks>Setting this access condition modifies the request to include the HTTP <i>If-Match</i> conditional header.</remarks>
        </member>
        <member name="M:Microsoft.Azure.Storage.AccessCondition.GenerateIfMatchCondition(System.String)">
            <summary>
            Constructs an access condition such that an operation will be performed only if the resource's ETag value
            matches the specified ETag value.
            </summary>
            <param name="etag">The ETag value to check against the resource's ETag.</param>
            <returns>An <see cref="T:Microsoft.Azure.Storage.AccessCondition"/> object that represents the If-Match condition.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.AccessCondition.GenerateIfModifiedSinceCondition(System.DateTimeOffset)">
            <summary>
            Constructs an access condition such that an operation will be performed only if the resource has been
            modified since the specified time.
            </summary>
            <param name="modifiedTime">A <see cref="T:System.DateTimeOffset"/> value specifying the time since which the resource must have been modified.</param>
            <returns>An <see cref="T:Microsoft.Azure.Storage.AccessCondition"/> object that represents the If-Modified-Since condition.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.AccessCondition.GenerateIfNoneMatchCondition(System.String)">
            <summary>
            Constructs an access condition such that an operation will be performed only if the resource's ETag value
            does not match the specified ETag value.
            </summary>
            <param name="etag">The ETag value to check against the resource's ETag, or <c>"*"</c> to require that the resource does not exist.</param>
            <returns>An <see cref="T:Microsoft.Azure.Storage.AccessCondition"/> object that represents the If-None-Match condition.</returns>
            <remarks>
            If <c>"*"</c> is specified for the <paramref name="etag"/> parameter, then this condition requires that the resource does not exist.
            </remarks>        
        </member>
        <member name="M:Microsoft.Azure.Storage.AccessCondition.GenerateIfNotModifiedSinceCondition(System.DateTimeOffset)">
            <summary>
            Constructs an access condition such that an operation will be performed only if the resource has not been
            modified since the specified time.
            </summary>
            <param name="modifiedTime">A <see cref="T:System.DateTimeOffset"/> value specifying the time since which the resource must not have been modified.</param>
            <returns>An <see cref="T:Microsoft.Azure.Storage.AccessCondition"/> object that represents the If-Unmodified-Since condition.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.AccessCondition.GenerateIfMaxSizeLessThanOrEqualCondition(System.Int64)">
            <summary>
            Constructs an access condition such that an operation will be performed only if the size of the append blob after committing the block is less
            than or equal to the specified value.
            </summary>
            <param name="maxSize">An integer specifying the maximum allowed size of the blob, in bytes, when committing a new block.</param>
            <returns>An <see cref="T:Microsoft.Azure.Storage.AccessCondition"/> object that represents the maximum allowed size.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.AccessCondition.GenerateIfAppendPositionEqualCondition(System.Int64)">
            <summary>
            Constructs an access condition such that an operation will be performed only if the end position of the append blob is equal to the specified value.
            </summary>
            <param name="appendPosition">An integer specifying the offset to compare to the current end position of the blob.</param>
            <returns>An <see cref="T:Microsoft.Azure.Storage.AccessCondition"/> object that represents the offset to compare.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.AccessCondition.GenerateIfSequenceNumberLessThanOrEqualCondition(System.Int64)">
            <summary>
            Constructs an access condition such that an operation will be performed only if resource's current sequence
            number is less than or equal to the specified value.
            </summary>
            <param name="sequenceNumber">The value to compare to the current sequence number.</param>
            <returns>An <see cref="T:Microsoft.Azure.Storage.AccessCondition"/> object that represents the If-Sequence-Number-LE condition.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.AccessCondition.GenerateIfSequenceNumberLessThanCondition(System.Int64)">
            <summary>
            Constructs an access condition such that an operation will be performed only if resource's current sequence
            number is less than the specified value.
            </summary>
            <param name="sequenceNumber">The value to compare to the current sequence number.</param>
            <returns>An <see cref="T:Microsoft.Azure.Storage.AccessCondition"/> object that represents the If-Sequence-Number-LT condition.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.AccessCondition.GenerateIfSequenceNumberEqualCondition(System.Int64)">
            <summary>
            Constructs an access condition such that an operation will be performed only if resource's current sequence
            number is equal to the specified value.
            </summary>
            <param name="sequenceNumber">The value to compare to the current sequence number.</param>
            <returns>An <see cref="T:Microsoft.Azure.Storage.AccessCondition"/> object that represents the If-Sequence-Number-EQ condition.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.AccessCondition.GenerateLeaseCondition(System.String)">
            <summary>
            Constructs an access condition such that an operation will be performed only if the lease ID on the
            resource matches the specified lease ID.
            </summary>
            <param name="leaseId">The lease ID to compare to the lease ID of the resource.</param>
            <returns>An <see cref="T:Microsoft.Azure.Storage.AccessCondition"/> object that represents the lease condition.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.AccessCondition.CloneConditionWithETag(Microsoft.Azure.Storage.AccessCondition,System.String)">
            <summary>
            Constructs an access condition such that an operation will be performed only if the resource's ETag value
            matches the specified ETag value and the lease ID on the resource matches the lease ID specified in
            the given access condition.
            </summary>
            <param name="accessCondition">An <see cref="T:Microsoft.Azure.Storage.AccessCondition"/> object that represents the condition that must be met in order for the request to proceed.</param>
            <param name="etag">The ETag value that must be matched.</param>
            <returns>An <see cref="T:Microsoft.Azure.Storage.AccessCondition"/> object that represents the If-Match and the lease conditions.</returns>
        </member>
        <member name="T:Microsoft.Azure.Storage.AuthenticationScheme">
            <summary>
            Specifies the authentication scheme used to sign HTTP requests.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.AuthenticationScheme.SharedKeyLite">
            <summary>
            Signs HTTP requests using the Shared Key Lite authentication scheme.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.AuthenticationScheme.SharedKey">
            <summary>
            Signs HTTP requests using the Shared Key authentication scheme.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.AuthenticationScheme.Token">
            <summary>
            Signs HTTPS requests using the Bearer Token authentication scheme.
            </summary>
        </member>
        <member name="T:Microsoft.Azure.Storage.Auth.StorageCredentials">
            <summary>
            Represents a set of credentials used to authenticate access to a Microsoft Azure storage account.
            </summary>
            <remarks>
            ## Examples
            [!code-csharp[Storage_Credentials_Sample](~/azure-storage-net/Test/Common/Core/CloudStorageAccountTests.cs#sample_CloudStorageAccount_Constructor "Storage Credentials Sample")] 
            </remarks>
        </member>
        <member name="P:Microsoft.Azure.Storage.Auth.StorageCredentials.SASToken">
            <summary>
            Gets the associated shared access signature token for the credentials.
            </summary>
            <value>The shared access signature token.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.Auth.StorageCredentials.AccountName">
            <summary>
            Gets the associated account name for the credentials.
            </summary>
            <value>The account name.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.Auth.StorageCredentials.KeyName">
            <summary>
            Gets the associated key name for the credentials.
            </summary>
            <value>The key name.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.Auth.StorageCredentials.TokenCredential">
            <summary>
            Gets the associated OAuth Token for the credentials.
            </summary>
        </member>
        <member name="P:Microsoft.Azure.Storage.Auth.StorageCredentials.IsAnonymous">
            <summary>
            Gets a value indicating whether the credentials are for anonymous access.
            </summary>
            <value><c>true</c> if the credentials are for anonymous access; otherwise, <c>false</c>.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.Auth.StorageCredentials.IsSAS">
            <summary>
            Gets a value indicating whether the credentials are a shared access signature token.
            </summary>
            <value><c>true</c> if the credentials are a shared access signature token; otherwise, <c>false</c>.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.Auth.StorageCredentials.IsSharedKey">
            <summary>
            Gets a value indicating whether the credentials are a shared key.
            </summary>
            <value><c>true</c> if the credentials are a shared key; otherwise, <c>false</c>.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.Auth.StorageCredentials.IsToken">
            <summary>
            Gets a value indicating whether the credentials are a bearer token.
            </summary>
            <value><c>true</c> if the credentials are a bearer token; otherwise, <c>false</c>.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.Auth.StorageCredentials.SASSignature">
            <summary>
            Gets the value of the shared access signature token's <c>sig</c> parameter.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.Auth.StorageCredentials.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Azure.Storage.Auth.StorageCredentials"/> class.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.Auth.StorageCredentials.#ctor(System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Azure.Storage.Auth.StorageCredentials"/> class with the specified account name and key value.
            </summary>
            <param name="accountName">A string that represents the name of the storage account.</param>
            <param name="keyValue">A string that represents the Base64-encoded account access key.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.Auth.StorageCredentials.#ctor(System.String,System.Byte[])">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Azure.Storage.Auth.StorageCredentials"/> class with the specified account name and key value.
            </summary>
            <param name="accountName">A string that represents the name of the storage account.</param>
            <param name="keyValue">An array of bytes that represent the account access key.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.Auth.StorageCredentials.#ctor(System.String,System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Azure.Storage.Auth.StorageCredentials"/> class with the specified account name, key value, and key name.
            </summary>
            <param name="accountName">A string that represents the name of the storage account.</param>
            <param name="keyValue">A string that represents the Base64-encoded account access key.</param>
            <param name="keyName">A string that represents the name of the key.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.Auth.StorageCredentials.#ctor(System.String,System.Byte[],System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Azure.Storage.Auth.StorageCredentials"/> class with the specified account name, key value, and key name.
            </summary>
            <param name="accountName">A string that represents the name of the storage account.</param>
            <param name="keyValue">An array of bytes that represent the account access key.</param>
            <param name="keyName">A string that represents the name of the key.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.Auth.StorageCredentials.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Azure.Storage.Auth.StorageCredentials"/> class with the specified shared access signature token.
            </summary>
            <param name="sasToken">A string representing the shared access signature token.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.Auth.StorageCredentials.#ctor(Microsoft.Azure.Storage.Auth.TokenCredential)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Azure.Storage.Auth.StorageCredentials"/> class with the specified bearer token.
            </summary>
            <param name="tokenCredential">The authentication token.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.Auth.StorageCredentials.UpdateKey(System.String)">
            <summary>
            Updates the key value for the credentials.
            </summary>
            <param name="keyValue">The key value, as a Base64-encoded string, to update.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.Auth.StorageCredentials.UpdateKey(System.Byte[])">
            <summary>
            Updates the key value for the credentials.
            </summary>
            <param name="keyValue">The key value, as an array of bytes, to update.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.Auth.StorageCredentials.UpdateKey(System.String,System.String)">
            <summary>
            Updates the key value and key name for the credentials.
            </summary>
            <param name="keyValue">The key value, as a Base64-encoded string, to update.</param>
            <param name="keyName">The key name to update.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.Auth.StorageCredentials.UpdateKey(System.Byte[],System.String)">
            <summary>
            Updates the key value and key name for the credentials.
            </summary>
            <param name="keyValue">The key value, as an array of bytes, to update.</param>
            <param name="keyName">The key name to update.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.Auth.StorageCredentials.UpdateSASToken(System.String)">
            <summary>
            Updates the shared access signature (SAS) token value for storage credentials created with a shared access signature.
            </summary>
            <param name="sasToken">A string that specifies the SAS token value to update.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.Auth.StorageCredentials.ExportKey">
            <summary>
            Returns the account key for the credentials.
            </summary>
            <returns>An array of bytes that contains the key.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Auth.StorageCredentials.TransformUri(System.Uri)">
            <summary>
            Transforms a resource URI into a shared access signature URI, by appending a shared access token.
            </summary>
            <param name="resourceUri">A <see cref="T:System.Uri"/> object that represents the resource URI to be transformed.</param>
            <returns>A <see cref="T:System.Uri"/> object that represents the signature, including the resource URI and the shared access token.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Auth.StorageCredentials.TransformUri(Microsoft.Azure.Storage.StorageUri)">
            <summary>
            Transforms a resource URI into a shared access signature URI, by appending a shared access token.
            </summary>
            <param name="resourceUri">A <see cref="T:Microsoft.Azure.Storage.StorageUri"/> object that represents the resource URI to be transformed.</param>
            <returns>A <see cref="T:Microsoft.Azure.Storage.StorageUri"/> object that represents the signature, including the resource URI and the shared access token.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Auth.StorageCredentials.ExportBase64EncodedKey">
            <summary>
            Exports the value of the account access key to a Base64-encoded string.
            </summary>
            <returns>The account access key.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Auth.StorageCredentials.Equals(Microsoft.Azure.Storage.Auth.StorageCredentials)">
            <summary>
            Determines whether an other <see cref="T:Microsoft.Azure.Storage.Auth.StorageCredentials"/> object is equal to this one by comparing their SAS tokens, account names, key names, and key values.
            </summary>
            <param name="other">The <see cref="T:Microsoft.Azure.Storage.Auth.StorageCredentials"/> object to compare to this one.</param>
            <returns><c>true</c> if the two <see cref="T:Microsoft.Azure.Storage.Auth.StorageCredentials"/> objects are equal; otherwise, <c>false</c>.</returns>
        </member>
        <member name="T:Microsoft.Azure.Storage.Auth.NewTokenAndFrequency">
            <summary>
            This is the return type of <see cref="T:Microsoft.Azure.Storage.Auth.RenewTokenFuncAsync"/>.
            A new token and a new frequency is expected.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.Auth.NewTokenAndFrequency.#ctor(System.String,System.Nullable{System.TimeSpan})">
            <summary>
            Create a new instance of <see cref="T:Microsoft.Azure.Storage.Auth.NewTokenAndFrequency"/>.
            </summary>
            <param name="newToken">The new token credential.</param>
            <param name="newFrequency">The new frequency to wait before calling <see cref="T:Microsoft.Azure.Storage.Auth.RenewTokenFuncAsync"/> again.</param>
        </member>
        <member name="P:Microsoft.Azure.Storage.Auth.NewTokenAndFrequency.Token">
            <summary>
            The new token credential. 
            </summary>
        </member>
        <member name="P:Microsoft.Azure.Storage.Auth.NewTokenAndFrequency.Frequency">
            <summary>
            The new frequency to wait before calling <see cref="T:Microsoft.Azure.Storage.Auth.RenewTokenFuncAsync"/> again.
            </summary>
        </member>
        <member name="T:Microsoft.Azure.Storage.Auth.RenewTokenFuncAsync">
            <summary>
            This type of delegate is used to update the token credential periodically.
            </summary>
            <param name="state">A state object, which can be of any type.</param>
            <param name="cancellationToken">A cancellation token to receive the cancellation signal.</param>
            <returns></returns>
        </member>
        <member name="T:Microsoft.Azure.Storage.Auth.TokenCredential">
            <summary>
            Represents a token that is used to authorize HTTPS requests.
            </summary>
        </member>
        <member name="P:Microsoft.Azure.Storage.Auth.TokenCredential.Token">
            <summary>
            The authorization token. It can be set by the user at any point in a thread-safe way.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.Auth.TokenCredential.#ctor(System.String)">
            <summary>
            Create an instance of <see cref="T:Microsoft.Azure.Storage.Auth.TokenCredential"/>.
            </summary>
            <param name="initialToken">Initial value of the token credential.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.Auth.TokenCredential.#ctor(System.String,Microsoft.Azure.Storage.Auth.RenewTokenFuncAsync,System.Object,System.TimeSpan)">
            <summary>
            Create an instance of <see cref="T:Microsoft.Azure.Storage.Auth.TokenCredential"/>.
            </summary>
            <param name="initialToken">Initial value of the token credential.</param>
            <param name="periodicTokenRenewer">If given, this delegate is called periodically to renew the token credential.</param>
            <param name="state">A state object is passed to the periodicTokenRenewer every time it is called.</param>
            <param name="renewFrequency">If periodicTokenRenewer is given, user should define a frequency to call the periodicTokenRenewer.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.Auth.TokenCredential.Dispose">
            <summary>
            Calling Dispose stops the timer and periodicTokenRenewer.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.Auth.TokenCredential.RenewTokenAsync(System.Object)">
            <summary>
            This method is triggered by the timer. 
            It calls the renew function provided by the user, updates the token, and then restarts the timer.
            </summary>
            <param name="state"></param>
        </member>
        <member name="T:Microsoft.Azure.Storage.CloudStorageAccount">
            <summary>
            Represents a Microsoft Azure Storage account.
            </summary>
            <remarks>
             ## Examples
             [!code-csharp[Cloud_Storage_Account_Sample](~/azure-storage-net/Test/Common/Core/CloudStorageAccountTests.cs#sample_CloudStorageAccount_Constructor "Cloud Storage Account Sample")]
             </remarks>
        </member>
        <member name="F:Microsoft.Azure.Storage.CloudStorageAccount.version1MD5">
            <summary>
            The FISMA compliance default value.
            </summary>
        </member>
        <member name="P:Microsoft.Azure.Storage.CloudStorageAccount.UseV1MD5">
            <summary>
            Gets or sets a value indicating whether the FISMA MD5 setting will be used.
            </summary>
            <value><c>false</c> to use the FISMA MD5 setting; <c>true</c> to use the .NET default implementation.</value>
        </member>
        <member name="F:Microsoft.Azure.Storage.CloudStorageAccount.UseDevelopmentStorageSettingString">
            <summary>
            The setting name for using the development storage.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.CloudStorageAccount.DevelopmentStorageProxyUriSettingString">
            <summary>
            The setting name for specifying a development storage proxy Uri.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.CloudStorageAccount.DefaultEndpointsProtocolSettingString">
            <summary>
            The setting name for using the default storage endpoints with the specified protocol.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.CloudStorageAccount.AccountNameSettingString">
            <summary>
            The setting name for the account name.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.CloudStorageAccount.AccountKeyNameSettingString">
            <summary>
            The setting name for the account key name.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.CloudStorageAccount.AccountKeySettingString">
            <summary>
            The setting name for the account key.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.CloudStorageAccount.BlobEndpointSettingString">
            <summary>
            The setting name for a custom blob storage endpoint.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.CloudStorageAccount.QueueEndpointSettingString">
            <summary>
            The setting name for a custom queue endpoint.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.CloudStorageAccount.TableEndpointSettingString">
            <summary>
            The setting name for a custom table storage endpoint.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.CloudStorageAccount.FileEndpointSettingString">
            <summary>
            The setting name for a custom file storage endpoint.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.CloudStorageAccount.BlobSecondaryEndpointSettingString">
            <summary>
            The setting name for a custom blob storage secondary endpoint.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.CloudStorageAccount.QueueSecondaryEndpointSettingString">
            <summary>
            The setting name for a custom queue secondary endpoint.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.CloudStorageAccount.TableSecondaryEndpointSettingString">
            <summary>
            The setting name for a custom table storage secondary endpoint.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.CloudStorageAccount.FileSecondaryEndpointSettingString">
            <summary>
            The setting name for a custom file storage secondary endpoint.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.CloudStorageAccount.EndpointSuffixSettingString">
            <summary>
            The setting name for a custom storage endpoint suffix.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.CloudStorageAccount.SharedAccessSignatureSettingString">
            <summary>
            The setting name for a shared access key.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.CloudStorageAccount.DevstoreAccountName">
            <summary>
            The default account name for the development storage.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.CloudStorageAccount.DevstoreAccountKey">
            <summary>
            The default account key for the development storage.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.CloudStorageAccount.SecondaryLocationAccountSuffix">
            <summary>
            The suffix appended to account in order to access secondary location for read only access.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.CloudStorageAccount.DefaultEndpointSuffix">
            <summary>
            The default storage service hostname suffix.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.CloudStorageAccount.DefaultBlobHostnamePrefix">
            <summary>
            The default blob storage DNS hostname prefix.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.CloudStorageAccount.DefaultQueueHostnamePrefix">
            <summary>
            The root queue DNS name prefix.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.CloudStorageAccount.DefaultTableHostnamePrefix">
            <summary>
            The root table storage DNS name prefix.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.CloudStorageAccount.DefaultFileHostnamePrefix">
            <summary>
            The default file storage DNS hostname prefix.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.CloudStorageAccount.UseDevelopmentStorageSetting">
            <summary>
            Validator for the UseDevelopmentStorage setting. Must be "true".
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.CloudStorageAccount.DevelopmentStorageProxyUriSetting">
            <summary>
            Validator for the DevelopmentStorageProxyUri setting. Must be a valid Uri.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.CloudStorageAccount.DefaultEndpointsProtocolSetting">
            <summary>
            Validator for the DefaultEndpointsProtocol setting. Must be either "http" or "https".
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.CloudStorageAccount.AccountNameSetting">
            <summary>
            Validator for the AccountName setting. No restrictions.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.CloudStorageAccount.AccountKeyNameSetting">
            <summary>
            Validator for the AccountKey setting. No restrictions.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.CloudStorageAccount.AccountKeySetting">
            <summary>
            Validator for the AccountKey setting. Must be a valid base64 string.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.CloudStorageAccount.BlobEndpointSetting">
            <summary>
            Validator for the BlobEndpoint setting. Must be a valid Uri.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.CloudStorageAccount.QueueEndpointSetting">
            <summary>
            Validator for the QueueEndpoint setting. Must be a valid Uri.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.CloudStorageAccount.TableEndpointSetting">
            <summary>
            Validator for the TableEndpoint setting. Must be a valid Uri.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.CloudStorageAccount.FileEndpointSetting">
            <summary>
            Validator for the FileEndpoint setting. Must be a valid Uri.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.CloudStorageAccount.BlobSecondaryEndpointSetting">
            <summary>
            Validator for the BlobSecondaryEndpoint setting. Must be a valid Uri.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.CloudStorageAccount.QueueSecondaryEndpointSetting">
            <summary>
            Validator for the QueueSecondaryEndpoint setting. Must be a valid Uri.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.CloudStorageAccount.TableSecondaryEndpointSetting">
            <summary>
            Validator for the TableSecondaryEndpoint setting. Must be a valid Uri.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.CloudStorageAccount.FileSecondaryEndpointSetting">
            <summary>
            Validator for the FileSecondaryEndpoint setting. Must be a valid Uri.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.CloudStorageAccount.EndpointSuffixSetting">
            <summary>
            Validator for the EndpointSuffix setting. Must be a valid Uri.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.CloudStorageAccount.SharedAccessSignatureSetting">
            <summary>
            Validator for the SharedAccessSignature setting. No restrictions.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.CloudStorageAccount.devStoreAccount">
            <summary>
            Singleton instance for the development storage account.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.CloudStorageAccount.#ctor(Microsoft.Azure.Storage.Auth.StorageCredentials,System.Uri,System.Uri,System.Uri,System.Uri)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Azure.Storage.CloudStorageAccount"/> class using the specified
            credentials and service endpoints.
            </summary>
            <param name="storageCredentials">A <see cref="T:Microsoft.Azure.Storage.Auth.StorageCredentials"/> object.</param>
            <param name="blobEndpoint">A <see cref="T:System.Uri"/> specifying the primary Blob service endpoint.</param>
            <param name="queueEndpoint">A <see cref="T:System.Uri"/> specifying the primary Queue service endpoint.</param>
            <param name="tableEndpoint">A <see cref="T:System.Uri"/> specifying the primary Table service endpoint.</param>
            <param name="fileEndpoint">A <see cref="T:System.Uri"/> specifying the primary File service endpoint.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.CloudStorageAccount.#ctor(Microsoft.Azure.Storage.Auth.StorageCredentials,Microsoft.Azure.Storage.StorageUri,Microsoft.Azure.Storage.StorageUri,Microsoft.Azure.Storage.StorageUri,Microsoft.Azure.Storage.StorageUri)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Azure.Storage.CloudStorageAccount"/> class using the specified
            account credentials and service endpoints.
            </summary>
            <param name="storageCredentials">A <see cref="T:Microsoft.Azure.Storage.Auth.StorageCredentials"/> object.</param>
            <param name="blobStorageUri">A <see cref="T:Microsoft.Azure.Storage.StorageUri"/> specifying the Blob service endpoint or endpoints.</param>
            <param name="queueStorageUri">A <see cref="T:Microsoft.Azure.Storage.StorageUri"/> specifying the Queue service endpoint or endpoints.</param>
            <param name="tableStorageUri">A <see cref="T:Microsoft.Azure.Storage.StorageUri"/> specifying the Table service endpoint or endpoints.</param>
            <param name="fileStorageUri">A <see cref="T:Microsoft.Azure.Storage.StorageUri"/> specifying the File service endpoint or endpoints.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.CloudStorageAccount.#ctor(Microsoft.Azure.Storage.Auth.StorageCredentials,System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Azure.Storage.CloudStorageAccount"/> class using the specified
            credentials, and specifies whether to use HTTP or HTTPS to connect to the storage services. 
            </summary>
            <param name="storageCredentials">A <see cref="T:Microsoft.Azure.Storage.Auth.StorageCredentials"/> object.</param>
            <param name="useHttps"><c>true</c> to use HTTPS to connect to storage service endpoints; otherwise, <c>false</c>.</param>
            <remarks>Using HTTPS to connect to the storage services is recommended.</remarks>
        </member>
        <member name="M:Microsoft.Azure.Storage.CloudStorageAccount.#ctor(Microsoft.Azure.Storage.Auth.StorageCredentials,System.String,System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Azure.Storage.CloudStorageAccount"/> class using the specified
            credentials and endpoint suffix, and specifies whether to use HTTP or HTTPS to connect to the storage services.
            </summary>
            <param name="storageCredentials">A <see cref="T:Microsoft.Azure.Storage.Auth.StorageCredentials"/> object.</param>
            <param name="endpointSuffix">The DNS endpoint suffix for all storage services, e.g. "core.windows.net".</param>
            <param name="useHttps"><c>true</c> to use HTTPS to connect to storage service endpoints; otherwise, <c>false</c>.</param>
            <remarks>Using HTTPS to connect to the storage services is recommended.</remarks>
        </member>
        <member name="M:Microsoft.Azure.Storage.CloudStorageAccount.#ctor(Microsoft.Azure.Storage.Auth.StorageCredentials,System.String,System.String,System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Azure.Storage.CloudStorageAccount"/> class using the specified
            credentials and endpoint suffix, and specifies whether to use HTTP or HTTPS to connect to the storage services.
            </summary>
            <param name="storageCredentials">A <see cref="T:Microsoft.Azure.Storage.Auth.StorageCredentials"/> object.</param>
            <param name="accountName">The name of the account.</param>
            <param name="endpointSuffix">The DNS endpoint suffix for all storage services, e.g. "core.windows.net".</param>
            <param name="useHttps"><c>true</c> to use HTTPS to connect to storage service endpoints; otherwise, <c>false</c>.</param>
            <remarks>Using HTTPS to connect to the storage services is recommended.</remarks>
        </member>
        <member name="P:Microsoft.Azure.Storage.CloudStorageAccount.DevelopmentStorageAccount">
            <summary>
            Gets a <see cref="T:Microsoft.Azure.Storage.CloudStorageAccount"/> object that references the well-known development storage account.
            </summary>
            <value>A <see cref="T:Microsoft.Azure.Storage.CloudStorageAccount"/> object representing the development storage account.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.CloudStorageAccount.IsDevStoreAccount">
            <summary>
            Indicates whether this account is a development storage account.
            </summary>
        </member>
        <member name="P:Microsoft.Azure.Storage.CloudStorageAccount.EndpointSuffix">
            <summary>
            The storage service hostname suffix set by the user, if any.
            </summary>
        </member>
        <member name="P:Microsoft.Azure.Storage.CloudStorageAccount.Settings">
            <summary>
            The connection string parsed into settings.
            </summary>
        </member>
        <member name="P:Microsoft.Azure.Storage.CloudStorageAccount.DefaultEndpoints">
            <summary>
            True if the user used a constructor that auto-generates endpoints.
            </summary>
        </member>
        <member name="P:Microsoft.Azure.Storage.CloudStorageAccount.BlobEndpoint">
            <summary>
            Gets the primary endpoint for the Blob service, as configured for the storage account.
            </summary>
            <value>A <see cref="T:System.Uri"/> containing the primary Blob service endpoint.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.CloudStorageAccount.QueueEndpoint">
            <summary>
            Gets the primary endpoint for the Queue service, as configured for the storage account.
            </summary>
            <value>A <see cref="T:System.Uri"/> containing the primary Queue service endpoint.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.CloudStorageAccount.TableEndpoint">
            <summary>
            Gets the primary endpoint for the Table service, as configured for the storage account.
            </summary>
            <value>A <see cref="T:System.Uri"/> containing the primary Table service endpoint.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.CloudStorageAccount.FileEndpoint">
            <summary>
            Gets the primary endpoint for the File service, as configured for the storage account.
            </summary>
            <value>A <see cref="T:System.Uri"/> containing the primary File service endpoint.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.CloudStorageAccount.BlobStorageUri">
            <summary>
            Gets the endpoints for the Blob service at the primary and secondary location, as configured for the storage account.
            </summary>
            <value>A <see cref="T:Microsoft.Azure.Storage.StorageUri"/> containing the Blob service endpoints.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.CloudStorageAccount.QueueStorageUri">
            <summary>
            Gets the endpoints for the Queue service at the primary and secondary location, as configured for the storage account.
            </summary>
            <value>A <see cref="T:Microsoft.Azure.Storage.StorageUri"/> containing the Queue service endpoints.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.CloudStorageAccount.TableStorageUri">
            <summary>
            Gets the endpoints for the Table service at the primary and secondary location, as configured for the storage account.
            </summary>
            <value>A <see cref="T:Microsoft.Azure.Storage.StorageUri"/> containing the Table service endpoints.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.CloudStorageAccount.FileStorageUri">
            <summary>
            Gets the endpoints for the File service at the primary and secondary location, as configured for the storage account.
            </summary>
            <value>A <see cref="T:Microsoft.Azure.Storage.StorageUri"/> containing the File service endpoints.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.CloudStorageAccount.Credentials">
            <summary>
            Gets the credentials used to create this <see cref="T:Microsoft.Azure.Storage.CloudStorageAccount"/> object.
            </summary>
            <value>A <see cref="T:Microsoft.Azure.Storage.Auth.StorageCredentials"/> object.</value>
        </member>
        <member name="F:Microsoft.Azure.Storage.CloudStorageAccount.accountName">
            <summary>
            Private record of the account name for use in ToString(bool).
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.CloudStorageAccount.Parse(System.String)">
            <summary>
            Parses a connection string and returns a <see cref="T:Microsoft.Azure.Storage.CloudStorageAccount"/> created
            from the connection string.
            </summary>
            <param name="connectionString">A valid connection string.</param>
            <exception cref="T:System.ArgumentNullException">Thrown if <paramref name="connectionString"/> is null or empty.</exception>
            <exception cref="T:System.FormatException">Thrown if <paramref name="connectionString"/> is not a valid connection string.</exception>
            <exception cref="T:System.ArgumentException">Thrown if <paramref name="connectionString"/> cannot be parsed.</exception>
            <returns>A <see cref="T:Microsoft.Azure.Storage.CloudStorageAccount"/> object constructed from the values provided in the connection string.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.CloudStorageAccount.TryParse(System.String,Microsoft.Azure.Storage.CloudStorageAccount@)">
            <summary>
            Indicates whether a connection string can be parsed to return a <see cref="T:Microsoft.Azure.Storage.CloudStorageAccount"/> object.
            </summary>
            <param name="connectionString">The connection string to parse.</param>
            <param name="account">A <see cref="T:Microsoft.Azure.Storage.CloudStorageAccount"/> object to hold the instance returned if
            the connection string can be parsed.</param>
            <returns><b>true</b> if the connection string was successfully parsed; otherwise, <b>false</b>.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.CloudStorageAccount.GetSharedAccessSignature(Microsoft.Azure.Storage.SharedAccessAccountPolicy)">
            <summary>
            Returns a shared access signature for the account.
            </summary>
            <param name="policy">A <see cref="T:Microsoft.Azure.Storage.SharedAccessAccountPolicy"/> object specifying the access policy for the shared access signature.</param>
            <returns>A shared access signature, as a URI query string.</returns>
            <remarks>The query string returned includes the leading question mark.</remarks>
        </member>
        <member name="M:Microsoft.Azure.Storage.CloudStorageAccount.ToString">
            <summary>
            Returns a connection string for this storage account, without sensitive data.
            </summary>
            <returns>A connection string.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.CloudStorageAccount.ToString(System.Boolean)">
            <summary>
            Returns a connection string for the storage account, optionally with sensitive data.
            </summary>
            <param name="exportSecrets"><c>True</c> to include sensitive data in the string; otherwise, <c>false</c>.</param>
            <returns>A connection string.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.CloudStorageAccount.GetDevelopmentStorageAccount(System.Uri)">
            <summary>
            Returns a <see cref="T:Microsoft.Azure.Storage.CloudStorageAccount"/> with development storage credentials using the specified proxy Uri.
            </summary>
            <param name="proxyUri">The proxy endpoint to use.</param>
            <returns>The new <see cref="T:Microsoft.Azure.Storage.CloudStorageAccount"/>.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.CloudStorageAccount.ParseImpl(System.String,Microsoft.Azure.Storage.CloudStorageAccount@,System.Action{System.String})">
            <summary>
            Internal implementation of Parse/TryParse.
            </summary>
            <param name="connectionString">The string to parse.</param>
            <param name="accountInformation">The <see cref="T:Microsoft.Azure.Storage.CloudStorageAccount"/> to return.</param>
            <param name="error">A callback for reporting errors.</param>
            <returns>If true, the parse was successful. Otherwise, false.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.CloudStorageAccount.ParseStringIntoSettings(System.String,System.Action{System.String})">
            <summary>
            Tokenizes input and stores name value pairs.
            </summary>
            <param name="connectionString">The string to parse.</param>
            <param name="error">Error reporting delegate.</param>
            <returns>Tokenized collection.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.CloudStorageAccount.Setting(System.String,System.String[])">
            <summary>
            Encapsulates a validation rule for an enumeration based account setting.
            </summary>
            <param name="name">The name of the setting.</param>
            <param name="validValues">A list of valid values for the setting.</param>
            <returns>An <see cref="T:System.Collections.Generic.KeyValuePair`2"/> representing the enumeration constraint.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.CloudStorageAccount.Setting(System.String,System.Func{System.String,System.Boolean})">
            <summary>
            Encapsulates a validation rule using a func.
            </summary>
            <param name="name">The name of the setting.</param>
            <param name="isValid">A func that determines if the value is valid.</param>
            <returns>An <see cref="T:System.Collections.Generic.KeyValuePair`2"/> representing the constraint.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.CloudStorageAccount.IsValidBase64String(System.String)">
            <summary>
            Determines whether the specified setting value is a valid base64 string.
            </summary>
            <param name="settingValue">The setting value.</param>
            <returns><c>true</c> if the specified setting value is a valid base64 string; otherwise, <c>false</c>.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.CloudStorageAccount.IsValidUri(System.String)">
            <summary>
            Validation function that validates Uris.
            </summary>
            <param name="settingValue">Value to validate.</param>
            <returns><c>true</c> if the specified setting value is a valid Uri; otherwise, <c>false</c>.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.CloudStorageAccount.IsValidDomain(System.String)">
            <summary>
            Validation function that validates a domain name.
            </summary>
            <param name="settingValue">Value to validate.</param>
            <returns><c>true</c> if the specified setting value is a valid domain; otherwise, <c>false</c>.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.CloudStorageAccount.AllRequired(System.Collections.Generic.KeyValuePair{System.String,System.Func{System.String,System.Boolean}}[])">
            <summary>
            Settings filter that requires all specified settings be present and valid.
            </summary>
            <param name="requiredSettings">A list of settings that must be present.</param>
            <returns>The remaining settings or <c>null</c> if the filter's requirement is not satisfied.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.CloudStorageAccount.Optional(System.Collections.Generic.KeyValuePair{System.String,System.Func{System.String,System.Boolean}}[])">
            <summary>
            Settings filter that removes optional values.
            </summary>
            <param name="optionalSettings">A list of settings that are optional.</param>
            <returns>The remaining settings or <c>null</c> if the filter's requirement is not satisfied.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.CloudStorageAccount.AtLeastOne(System.Collections.Generic.KeyValuePair{System.String,System.Func{System.String,System.Boolean}}[])">
            <summary>
            Settings filter that ensures that at least one setting is present.
            </summary>
            <param name="atLeastOneSettings">A list of settings of which one must be present.</param>
            <returns>The remaining settings or <c>null</c> if the filter's requirement is not satisfied.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.CloudStorageAccount.None(System.Collections.Generic.KeyValuePair{System.String,System.Func{System.String,System.Boolean}}[])">
            <summary>
            Settings filter that ensures that none of the specified settings are present.
            </summary>
            <param name="atLeastOneSettings">A list of settings of which one must not be present.</param>
            <returns>The remaining settings or <c>null</c> if the filter's requirement is not satisfied.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.CloudStorageAccount.MatchesAll(System.Func{System.Collections.Generic.IDictionary{System.String,System.String},System.Collections.Generic.IDictionary{System.String,System.String}}[])">
            <summary>
            Settings filter that ensures that all of the specified filters match.
            </summary>
            <param name="filters">A list of filters of which all must match.</param>
            <returns>The remaining settings or <c>null</c> if the filter's requirement is not satisfied.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.CloudStorageAccount.MatchesOne(System.Func{System.Collections.Generic.IDictionary{System.String,System.String},System.Collections.Generic.IDictionary{System.String,System.String}}[])">
            <summary>
            Settings filter that ensures that exactly one filter matches.
            </summary>
            <param name="filters">A list of filters of which exactly one must match.</param>
            <returns>The remaining settings or <c>null</c> if the filter's requirement is not satisfied.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.CloudStorageAccount.MatchesExactly(System.Func{System.Collections.Generic.IDictionary{System.String,System.String},System.Collections.Generic.IDictionary{System.String,System.String}})">
            <summary>
            Settings filter that ensures that the specified filter is an exact match.
            </summary>
            <param name="filter">A list of filters of which ensures that the specified filter is an exact match.</param>
            <returns>The remaining settings or <c>null</c> if the filter's requirement is not satisfied.</returns>
        </member>
        <member name="F:Microsoft.Azure.Storage.CloudStorageAccount.ValidCredentials">
            <summary>
            Settings filter that ensures that a valid combination of credentials is present.
            </summary>
            <returns>The remaining settings or <c>null</c> if the filter's requirement is not satisfied.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.CloudStorageAccount.MatchesSpecification(System.Collections.Generic.IDictionary{System.String,System.String},System.Func{System.Collections.Generic.IDictionary{System.String,System.String},System.Collections.Generic.IDictionary{System.String,System.String}}[])">
            <summary>
            Tests to see if a given list of settings matches a set of filters exactly.
            </summary>
            <param name="settings">The settings to check.</param>
            <param name="constraints">A list of filters to check.</param>
            <returns>
            If any filter returns null, false.
            If there are any settings left over after all filters are processed, false.
            Otherwise true.
            </returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.CloudStorageAccount.GetCredentials(System.Collections.Generic.IDictionary{System.String,System.String})">
            <summary>
            Gets a StorageCredentials object corresponding to whatever credentials are supplied in the given settings.
            </summary>
            <param name="settings">The settings to check.</param>
            <returns>The StorageCredentials object specified in the settings.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.CloudStorageAccount.ConstructBlobEndpoint(System.Collections.Generic.IDictionary{System.String,System.String})">
            <summary>
            Gets the default blob endpoint using specified settings.
            </summary>
            <param name="settings">The settings to use.</param>
            <returns>The default blob endpoint.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.CloudStorageAccount.ConstructBlobEndpoint(System.String,System.String,System.String)">
            <summary>
            Gets the default blob endpoint using the specified protocol and account name.
            </summary>
            <param name="scheme">The protocol to use.</param>
            <param name="accountName">The name of the storage account.</param>
            <param name="endpointSuffix">The Endpoint DNS suffix; use <c>null</c> for default.</param>
            <returns>The default blob endpoint.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.CloudStorageAccount.ConstructFileEndpoint(System.Collections.Generic.IDictionary{System.String,System.String})">
            <summary>
            Gets the default file endpoint using specified settings.
            </summary>
            <param name="settings">The settings to use.</param>
            <returns>The default file endpoint.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.CloudStorageAccount.ConstructFileEndpoint(System.String,System.String,System.String)">
            <summary>
            Gets the default file endpoint using the specified protocol and account name.
            </summary>
            <param name="scheme">The protocol to use.</param>
            <param name="accountName">The name of the storage account.</param>
            <param name="endpointSuffix">The Endpoint DNS suffix; use <c>null</c> for default.</param>
            <returns>The default file endpoint.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.CloudStorageAccount.ConstructQueueEndpoint(System.Collections.Generic.IDictionary{System.String,System.String})">
            <summary>
            Gets the default queue endpoint using the specified settings.
            </summary>
            <param name="settings">The settings.</param>
            <returns>The default queue endpoint.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.CloudStorageAccount.ConstructQueueEndpoint(System.String,System.String,System.String)">
            <summary>
            Gets the default queue endpoint using the specified protocol and account name.
            </summary>
            <param name="scheme">The protocol to use.</param>
            <param name="accountName">The name of the storage account.</param>
            <param name="endpointSuffix">The Endpoint DNS suffix; use <c>null</c> for default.</param>
            <returns>The default queue endpoint.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.CloudStorageAccount.ConstructTableEndpoint(System.Collections.Generic.IDictionary{System.String,System.String})">
            <summary>
            Gets the default table endpoint using the specified settings.
            </summary>
            <param name="settings">The settings.</param>
            <returns>The default table endpoint.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.CloudStorageAccount.ConstructTableEndpoint(System.String,System.String,System.String)">
            <summary>
            Gets the default table endpoint using the specified protocol and account name.
            </summary>
            <param name="scheme">The protocol to use.</param>
            <param name="accountName">The name of the storage account.</param>
            <param name="endpointSuffix">The Endpoint DNS suffix; use <c>null</c> for default.</param>
            <returns>The default table endpoint.</returns>
        </member>
        <member name="T:Microsoft.Azure.Storage.DoesServiceRequestAttribute">
            <summary>
            Specifies that the method will make one or more requests to the storage service. 
            </summary>
        </member>
        <member name="T:Microsoft.Azure.Storage.EncryptionAgent">
            <summary>
            Represents the encryption agent stored on the service. It consists of the encryption protocol version and encryption algorithm used.
            </summary>
        </member>
        <member name="P:Microsoft.Azure.Storage.EncryptionAgent.Protocol">
            <summary>
            The protocol version used for encryption.
            </summary>
        </member>
        <member name="P:Microsoft.Azure.Storage.EncryptionAgent.EncryptionAlgorithm">
            <summary>
            The algorithm used for encryption.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.EncryptionAgent.#ctor(System.String,Microsoft.Azure.Storage.EncryptionAlgorithm)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Azure.Storage.EncryptionAgent"/> class using the specified protocol version and the algorithm. 
            </summary>
            <param name="protocol">The encryption protocol version.</param>
            <param name="algorithm">The encryption algorithm.</param>
        </member>
        <member name="T:Microsoft.Azure.Storage.EncryptionAlgorithm">
            <summary>
            Specifies the encryption algorithm used to encrypt a resource.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.EncryptionAlgorithm.AES_CBC_256">
            <summary>
            AES-CBC using a 256 bit key.
            </summary>
        </member>
        <member name="T:Microsoft.Azure.Storage.EncryptionData">
            <summary>
            Represents the encryption data that is stored on the service.
            </summary>
        </member>
        <member name="P:Microsoft.Azure.Storage.EncryptionData.WrappedContentKey">
            <summary>
            Gets or sets the wrapped key that is used to store the wrapping algorithm, key identifier and the encrypted key bytes.
            </summary>
            <value>A <see cref="P:Microsoft.Azure.Storage.EncryptionData.WrappedContentKey"/> object that stores the wrapping algorithm, key identifier and the encrypted key bytes.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.EncryptionData.EncryptionAgent">
            <summary>
            Gets or sets the encryption agent that is used to identify the encryption protocol version and encryption algorithm.
            </summary>
            <value>The encryption agent.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.EncryptionData.ContentEncryptionIV">
            <summary>
            Gets or sets the content encryption IV.
            </summary>
            <value>The content encryption IV.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.EncryptionData.KeyWrappingMetadata">
            <summary>
            Gets or sets the user-defined encryption metadata.
            </summary>
            <value>An <see cref="T:System.Collections.Generic.IDictionary`2"/> object containing the encryption metadata as a collection of name-value pairs.</value>
        </member>
        <member name="T:Microsoft.Azure.Storage.IBufferManager">
            <summary>
            An interface that allows clients to provide a buffer manager to a given service client. This interface is patterned after
            the <see href="http://msdn.microsoft.com/en-us/library/system.servicemodel.channels.buffermanager.aspx">System.ServiceModel.Channels.BufferManager</see> class.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.IBufferManager.ReturnBuffer(System.Byte[])">
            <summary>
            Returns a buffer to the pool.
            </summary>
            <param name="buffer">A byte array specifying the buffer to return to the pool.</param>
            <exception cref="T:System.ArgumentNullException">Buffer reference cannot be null.</exception>
            <exception cref="T:System.ArgumentException">Length of buffer does not match the pool's buffer length property.</exception>
        </member>
        <member name="M:Microsoft.Azure.Storage.IBufferManager.TakeBuffer(System.Int32)">
            <summary>
            Gets a buffer of the specified size or larger from the pool.
            </summary>
            <param name="bufferSize">The size, in bytes, of the requested buffer.</param>
            <exception cref="T:System.ArgumentOutOfRangeException">The value specified for <paramref name="bufferSize"/> cannot be less than zero.</exception>
            <returns>A byte array that is the requested size of the buffer.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.IBufferManager.GetDefaultBufferSize">
            <summary>
            Gets the size, in bytes, of the buffers managed by the given pool. Note that the buffer manager must return buffers of the exact size requested by the client.
            </summary>
            <returns>The size, in bytes, of the buffers managed by the given pool.</returns>
        </member>
        <member name="T:Microsoft.Azure.Storage.IContinuationToken">
            <summary>
            An interface required for continuation token types.
            </summary>
            <remarks>The <see cref="!:Microsoft.Azure.Storage.Blob.BlobContinuationToken"/>, 
            and <see cref="!:Microsoft.Azure.Storage.Queue.QueueContinuationToken"/> classes implement the <see cref="T:Microsoft.Azure.Storage.IContinuationToken"/> interface.</remarks>
        </member>
        <member name="P:Microsoft.Azure.Storage.IContinuationToken.TargetLocation">
            <summary>
            Gets the location that the token applies to.
            </summary>
            <value>A <see cref="T:Microsoft.Azure.Storage.StorageLocation"/> enumeration value.</value>
        </member>
        <member name="T:Microsoft.Azure.Storage.IPAddressOrRange">
            <summary>
            Specifies either a single IP Address or a single range of IP Addresses (a minimum and a maximum, inclusive.)
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.IPAddressOrRange.#ctor(System.String)">
            <summary>
            Initializes a new instance of the IPAddressOrRange class from a single IPAddress.
            </summary>
            <param name="address">The IP Address that the IPAddressOrRange object will represent.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.IPAddressOrRange.#ctor(System.String,System.String)">
            <summary>
            Initializes a new instance of the IPAddressOrRange class from two IPAddress objects, a minimum and a maximum.
            </summary>
            <param name="minimum">The minimum IP Address that the IPAddressOrRange object will use as a range boundary, inclusive.</param>
            <param name="maximum">The maximum IP Address that the IPAddressOrRange object will use as a range boundary, inclusive.</param>
        </member>
        <member name="P:Microsoft.Azure.Storage.IPAddressOrRange.Address">
            <summary>
            The IP Address.
            Returns null if this object represents a range of IP addresses.
            </summary>
        </member>
        <member name="P:Microsoft.Azure.Storage.IPAddressOrRange.MinimumAddress">
            <summary>
            The minimum IP Address for the range, inclusive.
            Returns null if this object represents a single IP address.
            </summary>
        </member>
        <member name="P:Microsoft.Azure.Storage.IPAddressOrRange.MaximumAddress">
            <summary>
            The maximum IP Address for the range, inclusive.
            Returns null if this object represents a single IP address.
            </summary>
        </member>
        <member name="P:Microsoft.Azure.Storage.IPAddressOrRange.IsSingleAddress">
            <summary>
            True if this object represents a single IP Address, false if it represents a range.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.IPAddressOrRange.ToString">
            <summary>
            Provides a string representation of this IPAddressOrRange object.
            </summary>
            <returns>The string representation of this IPAddressOrRange object.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.IPAddressOrRange.AssertIPv4(System.String)">
            <summary>
            Assert that an IP address is in IPv4 format.
            </summary>
            <param name="address">The IP address to assert.</param>
        </member>
        <member name="T:Microsoft.Azure.Storage.IRequestOptions">
            <summary>
            An interface required for request option types.
            </summary>
            <remarks>The <see cref="!:Microsoft.Azure.Storage.Queue.QueueRequestOptions"/>, <see cref="!:Microsoft.Azure.Storage.Blob.BlobRequestOptions"/>, and <see cref="!:Microsoft.Azure.Storage.Table.TableRequestOptions"/> classes implement the <see cref="T:Microsoft.Azure.Storage.IRequestOptions"/> interface.</remarks>
        </member>
        <member name="P:Microsoft.Azure.Storage.IRequestOptions.RetryPolicy">
            <summary>
            Gets or sets the retry policy for the request.
            </summary>
            <value>An object of type <see cref="T:Microsoft.Azure.Storage.RetryPolicies.IRetryPolicy"/>.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.IRequestOptions.LocationMode">
            <summary>
            Gets or sets the location mode of the request.
            </summary>
            <value>A <see cref="T:Microsoft.Azure.Storage.RetryPolicies.LocationMode"/> enumeration value.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.IRequestOptions.ServerTimeout">
            <summary>
            Gets or sets the default server timeout for the request.
            </summary>
            <value>A <see cref="T:System.TimeSpan"/> containing the server timeout interval.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.IRequestOptions.MaximumExecutionTime">
            <summary>
            Gets or sets the maximum execution time across all potential retries.
            </summary>
            <value>A <see cref="T:System.TimeSpan"/> containing the maximum execution time across all potential retries.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.IRequestOptions.NetworkTimeout">
            <summary>
            Gets or sets the timeout applied to an individual network operations.
            </summary>
            <value>A <see cref="T:System.TimeSpan"/> containing the timeout applied to an individual network operations.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.IRequestOptions.RequireEncryption">
            <summary>
            Gets or sets a value to indicate whether data written and read by the client library should be encrypted.
            </summary>
            <value>Use <c>true</c> to specify that data should be encrypted/decrypted for all transactions; otherwise, <c>false</c>.</value>
        </member>
        <member name="T:Microsoft.Azure.Storage.LogLevel">
            <summary>
            Specifies what messages to output to the log.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.LogLevel.Off">
            <summary>
            Output no tracing and debugging messages.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.LogLevel.Error">
            <summary>
            Output error-handling messages.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.LogLevel.Warning">
            <summary>
            Output warnings and error-handling messages.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.LogLevel.Informational">
            <summary>
            Output informational messages, warnings, and error-handling messages.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.LogLevel.Verbose">
            <summary>
            Output all debugging and tracing messages.
            </summary>
        </member>
        <member name="T:Microsoft.Azure.Storage.NameValidator">
            <summary>
            Provides helpers to validate resource names across the Microsoft Azure Storage Services.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.NameValidator.ValidateContainerName(System.String)">
            <summary>
            Checks if a container name is valid.
            </summary>
            <param name="containerName">A string representing the container name to validate.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.NameValidator.ValidateQueueName(System.String)">
            <summary>
            Checks if a queue name is valid.
            </summary>
            <param name="queueName">A string representing the queue name to validate.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.NameValidator.ValidateShareName(System.String)">
            <summary>
            Checks if a share name is valid.
            </summary>
            <param name="shareName">A string representing the share name to validate.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.NameValidator.ValidateBlobName(System.String)">
            <summary>
            Checks if a blob name is valid.
            </summary>
            <param name="blobName">A string representing the blob name to validate.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.NameValidator.ValidateFileName(System.String)">
            <summary>
            Checks if a file name is valid.
            </summary>
            <param name="fileName">A string representing the file name to validate.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.NameValidator.ValidateDirectoryName(System.String)">
            <summary>
            Checks if a directory name is valid.
            </summary>
            <param name="directoryName">A string representing the directory name to validate.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.NameValidator.ValidateTableName(System.String)">
            <summary>
            Checks if a table name is valid.
            </summary>
            <param name="tableName">A string representing the table name to validate.</param>
        </member>
        <member name="T:Microsoft.Azure.Storage.OperationContext">
            <summary>
            Represents the context for a request operation against the storage service, and provides additional runtime information about its execution.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.OperationContext.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Azure.Storage.OperationContext"/> class.
            </summary>
        </member>
        <member name="P:Microsoft.Azure.Storage.OperationContext.UserHeaders">
            <summary>
            Gets or sets additional headers on the request, for example, for proxy or logging information.
            </summary>
            <value>A <see cref="T:System.Collections.Generic.IDictionary`2"/> object containing additional header information.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.OperationContext.ClientRequestID">
            <summary>
            Gets or sets the client request ID.
            </summary>
            <value>A string containing the client request ID.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.OperationContext.CustomUserAgent">
            <summary>
            Gets or sets a custom UserAgent value to be prepended to the existing library UserAgent.
            </summary>
            <value>A string containing the specified UserAgent value.</value>
            <remarks>This value will be overridden if the UserAgent value is modified via SendingRequestEvent (per instance or global).</remarks>
        </member>
        <member name="P:Microsoft.Azure.Storage.OperationContext.DefaultLogLevel">
            <summary>
            Gets or sets the default logging level to be used for subsequently created instances of the <see cref="T:Microsoft.Azure.Storage.OperationContext"/> class.
            </summary>
            <value>A value of type <see cref="P:Microsoft.Azure.Storage.OperationContext.LogLevel"/> that specifies which events are logged by default by instances of the <see cref="T:Microsoft.Azure.Storage.OperationContext"/>.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.OperationContext.LogLevel">
            <summary>
            Gets or sets the logging level to be used for an instance of the <see cref="T:Microsoft.Azure.Storage.OperationContext"/> class.
            </summary>
            <value>A value of type <see cref="P:Microsoft.Azure.Storage.OperationContext.LogLevel"/> that specifies which events are logged by the <see cref="T:Microsoft.Azure.Storage.OperationContext"/>.</value>
        </member>
        <member name="E:Microsoft.Azure.Storage.OperationContext.GlobalSendingRequest">
            <summary>
            Occurs immediately before a request is signed.
            </summary>
        </member>
        <member name="E:Microsoft.Azure.Storage.OperationContext.GlobalResponseReceived">
            <summary>
            Occurs when a response is received from the server, before any processing or downloading.
            </summary>
        </member>
        <member name="E:Microsoft.Azure.Storage.OperationContext.GlobalRequestCompleted">
            <summary>
            Occurs after a response has been fully received and processed.
            </summary>
        </member>
        <member name="E:Microsoft.Azure.Storage.OperationContext.GlobalRetrying">
            <summary>
            Occurs before a request is retried
            </summary>
        </member>
        <member name="E:Microsoft.Azure.Storage.OperationContext.SendingRequest">
            <summary>
            Occurs immediately before a request is signed.
            </summary>
        </member>
        <member name="E:Microsoft.Azure.Storage.OperationContext.ResponseReceived">
            <summary>
            Occurs when a response is received from the service, before any processing or downloading.
            </summary>
        </member>
        <member name="E:Microsoft.Azure.Storage.OperationContext.RequestCompleted">
            <summary>
            Occurs after a response has been fully received and processed.
            </summary>
        </member>
        <member name="E:Microsoft.Azure.Storage.OperationContext.Retrying">
            <summary>
            Occurs before a request is retried
            </summary>
        </member>
        <member name="P:Microsoft.Azure.Storage.OperationContext.StartTime">
            <summary>
            Gets or sets the start time of the operation.
            </summary>
            <value>A <see cref="T:System.DateTime"/> value indicating the start time of the operation.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.OperationContext.EndTime">
            <summary>
            Gets or sets the end time of the operation.
            </summary>
            <value>A <see cref="T:System.DateTime"/> value indicating the end time of the operation.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.OperationContext.RequestResults">
            <summary>
            Gets or sets the set of request results that the current operation has created.
            </summary>
            <value>An <see cref="T:System.Collections.IList"/> object that contains <see cref="T:Microsoft.Azure.Storage.RequestResult"/> objects that represent the request results created by the current operation.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.OperationContext.LastResult">
            <summary>
            Gets the last request result encountered for the operation.
            </summary>
            <value>A <see cref="T:Microsoft.Azure.Storage.RequestResult"/> object that represents the last request result.</value>
        </member>
        <member name="T:Microsoft.Azure.Storage.RequestEventArgs">
            <summary>
            Provides information and event data that is associated with a request event.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.RequestEventArgs.#ctor(Microsoft.Azure.Storage.RequestResult)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Azure.Storage.RequestEventArgs"/> class by using the specified <see cref="T:Microsoft.Azure.Storage.RequestResult"/> parameter.
            </summary>
            <param name="res">The <see cref="T:Microsoft.Azure.Storage.RequestResult"/> object.</param>
        </member>
        <member name="P:Microsoft.Azure.Storage.RequestEventArgs.RequestInformation">
            <summary>
            Gets the request information associated with this event.
            </summary>
            <value>The request information associated with this event.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.RequestEventArgs.Request">
            <summary>
            Gets the HTTP request associated with this event.
            </summary>
            <value>The HTTP request associated with this event.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.RequestEventArgs.Response">
            <summary>
            Gets the HTTP response associated with this event.
            </summary>
            <value>The HTTP response associated with this event.</value>
        </member>
        <member name="T:Microsoft.Azure.Storage.RequestResult">
            <summary>
            Represents the result of a physical request.
            </summary>
        </member>
        <member name="P:Microsoft.Azure.Storage.RequestResult.HttpStatusCode">
            <summary>
            Gets or sets the HTTP status code for the request.
            </summary>
            <value>The HTTP status code for the request.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.RequestResult.HttpStatusMessage">
            <summary>
            Gets the HTTP status message for the request.
            </summary>
            <value>The HTTP status message for the request.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.RequestResult.ServiceRequestID">
            <summary>
            Gets the service request ID for this request.
            </summary>
            <value>The service request ID for this request.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.RequestResult.ContentMd5">
            <summary>
            Gets the content-MD5 value for the request. 
            </summary>
            <value>The content-MD5 value for the request.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.RequestResult.ContentCrc64">
            <summary>
            Gets the content-CRC64 value for the request. 
            </summary>
            <value>The content-CRC645 value for the request.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.RequestResult.Etag">
            <summary>
            Gets the ETag value of the request.
            </summary>
            <value>The ETag value of the request.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.RequestResult.IngressBytes">
            <summary>
            The number of bytes read from the response body for the given request
            </summary>
        </member>
        <member name="P:Microsoft.Azure.Storage.RequestResult.EgressBytes">
            <summary>        
            The number of bytes written to the request body for a given request
            </summary>
        </member>
        <member name="P:Microsoft.Azure.Storage.RequestResult.RequestDate">
            <summary>
            Gets the request date.
            </summary>
            <value>The request date.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.RequestResult.TargetLocation">
            <summary>
            Gets the location to which the request was sent.
            </summary>
            <value>A <see cref="T:Microsoft.Azure.Storage.StorageLocation"/> enumeration value.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.RequestResult.ExtendedErrorInformation">
            <summary>
            Gets the extended error information.
            </summary>
            <value>A <see cref="T:Microsoft.Azure.Storage.StorageExtendedErrorInformation"/> object.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.RequestResult.ErrorCode">
            <summary>
            Gets the storage service error code.
            </summary>
            <value>A string containing the storage service error code.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.RequestResult.IsRequestServerEncrypted">
            <summary>
            Gets whether or not the data for a write operation is encrypted server-side.
            </summary>
        </member>
        <member name="P:Microsoft.Azure.Storage.RequestResult.IsServiceEncrypted">
            <summary>
            Represents whether or not the data for a read operation is encrypted on the server-side.
            </summary>
        </member>
        <member name="P:Microsoft.Azure.Storage.RequestResult.EncryptionKeySHA256">
            <summary>
            Represents the hash for the key used to server-side encrypt with client-provided keys.
            </summary>
        </member>
        <member name="P:Microsoft.Azure.Storage.RequestResult.EncryptionScope">
            <summary>
            Represents encryption scope.
            </summary>
        </member>
        <member name="P:Microsoft.Azure.Storage.RequestResult.Exception">
            <summary>
            Gets or sets the exception.
            </summary>
            <value>An <see cref="T:System.Exception"/> object.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.RequestResult.StartTime">
            <summary>
            Gets the start time of the operation.
            </summary>
            <value>A <see cref="T:System.DateTime"/> value indicating the start time of the operation.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.RequestResult.EndTime">
            <summary>
            Gets the end time of the operation.
            </summary>
            <value>A <see cref="T:System.DateTime"/> value indicating the end time of the operation.</value>
        </member>
        <member name="M:Microsoft.Azure.Storage.RequestResult.TranslateFromExceptionMessage(System.String)">
            <summary>
            Translates the specified message into a <see cref="T:Microsoft.Azure.Storage.RequestResult"/> object.
            </summary>
            <param name="message">The message to translate.</param>
            <returns>The translated <see cref="T:Microsoft.Azure.Storage.RequestResult"/>.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.RequestResult.ReadXmlAsync(System.Xml.XmlReader)">
            <summary>
            Generates a serializable RequestResult from its XML representation.
            </summary>
            <param name="reader">The <see cref="T:System.Xml.XmlReader"/> stream from which the RequestResult is deserialized.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.RequestResult.WriteXml(System.Xml.XmlWriter)">
            <summary>
            Converts a serializable RequestResult into its XML representation.
            </summary>
            <param name="writer">The <see cref="T:System.Xml.XmlWriter"/> stream to which the RequestResult is serialized.</param>
        </member>
        <member name="T:Microsoft.Azure.Storage.ResultSegment`1">
            <summary>
            Represents a result segment that was retrieved from the total set of possible results.
            </summary>
            <typeparam name="TElement">The type of the element returned in the result segment.</typeparam>
        </member>
        <member name="F:Microsoft.Azure.Storage.ResultSegment`1.continuationToken">
            <summary>
            Stores the continuation token used to retrieve the next segment of results.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.ResultSegment`1.#ctor(System.Collections.Generic.List{`0})">
            <summary>
            Initializes a new instance of the ResultSegment class.
            </summary>
            <param name="result">The result.</param>
        </member>
        <member name="P:Microsoft.Azure.Storage.ResultSegment`1.Results">
            <summary>
            Gets an enumerable collection of results.
            </summary>
            <value>An enumerable collection of results of type <c>TElement</c>.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.ResultSegment`1.ContinuationToken">
            <summary>
            Gets a continuation token to use to retrieve the next set of results with a subsequent call to the operation.
            </summary>
            <value>An object of type <see cref="T:Microsoft.Azure.Storage.IContinuationToken"/>.</value>
        </member>
        <member name="T:Microsoft.Azure.Storage.RetryPolicies.ExponentialRetry">
            <summary>
            Represents a retry policy that performs a specified number of retries, using a randomized exponential back off scheme to determine the interval between retries. 
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.RetryPolicies.ExponentialRetry.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Azure.Storage.RetryPolicies.ExponentialRetry"/> class.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.RetryPolicies.ExponentialRetry.#ctor(System.TimeSpan,System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Azure.Storage.RetryPolicies.ExponentialRetry"/> class using the specified delta and maximum number of retries.
            </summary>
            <param name="deltaBackoff">A <see cref="T:System.TimeSpan"/> specifying the back-off interval between retries.</param>
            <param name="maxAttempts">An integer specifying the maximum number of retry attempts.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.RetryPolicies.ExponentialRetry.ShouldRetry(System.Int32,System.Int32,System.Exception,System.TimeSpan@,Microsoft.Azure.Storage.OperationContext)">
            <summary>
            Determines whether the operation should be retried and the interval until the next retry.
            </summary>
            <param name="currentRetryCount">An integer specifying the number of retries for the given operation. A value of zero signifies this is the first error encountered.</param>
            <param name="statusCode">An integer containing the status code for the last operation.</param>
            <param name="lastException">An <see cref="T:System.Exception"/> object that represents the last exception encountered.</param>
            <param name="retryInterval">A <see cref="T:System.TimeSpan"/> indicating the interval to wait until the next retry.</param>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param>
            <returns><c>true</c> if the operation should be retried; otherwise, <c>false</c>.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.RetryPolicies.ExponentialRetry.Evaluate(Microsoft.Azure.Storage.RetryPolicies.RetryContext,Microsoft.Azure.Storage.OperationContext)">
            <summary>
            Determines whether the operation should be retried and the interval until the next retry.
            </summary>
            <param name="retryContext">A <see cref="T:Microsoft.Azure.Storage.RetryPolicies.RetryContext"/> object that indicates the number of retries, the results of the last request, and whether the next retry should happen in the primary or secondary location, and specifies the location mode.</param>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param>
            <returns>A <see cref="T:Microsoft.Azure.Storage.RetryPolicies.RetryInfo"/> object that indicates the location mode, and whether the next retry should happen in the primary or secondary location. If <c>null</c>, the operation will not be retried.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.RetryPolicies.ExponentialRetry.CreateInstance">
            <summary>
            Generates a new retry policy for the current request attempt.
            </summary>
            <returns>An <see cref="T:Microsoft.Azure.Storage.RetryPolicies.IRetryPolicy"/> object that represents the retry policy for the current request attempt.</returns>
        </member>
        <member name="T:Microsoft.Azure.Storage.RetryPolicies.IExtendedRetryPolicy">
            <summary>
            Represents a retry policy.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.RetryPolicies.IExtendedRetryPolicy.Evaluate(Microsoft.Azure.Storage.RetryPolicies.RetryContext,Microsoft.Azure.Storage.OperationContext)">
            <summary>
            Determines whether the operation should be retried and the interval until the next retry.
            </summary>
            <param name="retryContext">A <see cref="T:Microsoft.Azure.Storage.RetryPolicies.RetryContext"/> object that indicates the number of retries, the results of the last request, and whether the next retry should happen in the primary or secondary location, and specifies the location mode.</param>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param>
            <returns>A <see cref="T:Microsoft.Azure.Storage.RetryPolicies.RetryInfo"/> object that indicates the location mode, and whether the next retry should happen in the primary or secondary location. If <c>null</c>, the operation will not be retried.</returns>
        </member>
        <member name="T:Microsoft.Azure.Storage.RetryPolicies.IRetryPolicy">
            <summary>
            Represents a retry policy.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.RetryPolicies.IRetryPolicy.CreateInstance">
            <summary>
            Generates a new retry policy for the current request attempt.
            </summary>
            <returns>An <see cref="T:Microsoft.Azure.Storage.RetryPolicies.IRetryPolicy"/> object that represents the retry policy for the current request attempt.</returns>        
        </member>
        <member name="M:Microsoft.Azure.Storage.RetryPolicies.IRetryPolicy.ShouldRetry(System.Int32,System.Int32,System.Exception,System.TimeSpan@,Microsoft.Azure.Storage.OperationContext)">
            <summary>
            Determines whether the operation should be retried and the interval until the next retry.
            </summary>
            <param name="currentRetryCount">An integer specifying the number of retries for the given operation. A value of zero signifies this is the first error encountered.</param>
            <param name="statusCode">An integer containing the status code for the last operation.</param>
            <param name="lastException">An <see cref="T:System.Exception"/> object that represents the last exception encountered.</param>
            <param name="retryInterval">A <see cref="T:System.TimeSpan"/> indicating the interval to wait until the next retry.</param>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param>
            <returns><c>true</c> if the operation should be retried; otherwise, <c>false</c>.</returns>
        </member>
        <member name="T:Microsoft.Azure.Storage.RetryPolicies.LinearRetry">
            <summary>
            Represents a retry policy that performs a specified number of retries, using a specified fixed time interval between retries.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.RetryPolicies.LinearRetry.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Azure.Storage.RetryPolicies.LinearRetry"/> class.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.RetryPolicies.LinearRetry.#ctor(System.TimeSpan,System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Azure.Storage.RetryPolicies.LinearRetry"/> class using the specified delta and maximum number of retries.
            </summary>
            <param name="deltaBackoff">A <see cref="T:System.TimeSpan"/> specifying the back-off interval between retries.</param>
            <param name="maxAttempts">An integer specifying the maximum number of retry attempts.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.RetryPolicies.LinearRetry.ShouldRetry(System.Int32,System.Int32,System.Exception,System.TimeSpan@,Microsoft.Azure.Storage.OperationContext)">
            <summary>
            Determines whether the operation should be retried and the interval until the next retry.
            </summary>
            <param name="currentRetryCount">An integer specifying the number of retries for the given operation. A value of zero signifies this is the first error encountered.</param>
            <param name="statusCode">An integer containing the status code for the last operation.</param>
            <param name="lastException">An <see cref="T:System.Exception"/> object that represents the last exception encountered.</param>
            <param name="retryInterval">A <see cref="T:System.TimeSpan"/> indicating the interval to wait until the next retry.</param>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param>
            <returns><c>true</c> if the operation should be retried; otherwise, <c>false</c>.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.RetryPolicies.LinearRetry.Evaluate(Microsoft.Azure.Storage.RetryPolicies.RetryContext,Microsoft.Azure.Storage.OperationContext)">
            <summary>
            Determines whether the operation should be retried and the interval until the next retry.
            </summary>
            <param name="retryContext">A <see cref="T:Microsoft.Azure.Storage.RetryPolicies.RetryContext"/> object that indicates the number of retries, the results of the last request, and whether the next retry should happen in the primary or secondary location, and specifies the location mode.</param>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param>
            <returns>A <see cref="T:Microsoft.Azure.Storage.RetryPolicies.RetryInfo"/> object that indicates the location mode, and whether the next retry should happen in the primary or secondary location. If <c>null</c>, the operation will not be retried.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.RetryPolicies.LinearRetry.CreateInstance">
            <summary>
            Generates a new retry policy for the current request attempt.
            </summary>
            <returns>An <see cref="T:Microsoft.Azure.Storage.RetryPolicies.IRetryPolicy"/> object that represents the retry policy for the current request attempt.</returns>        
        </member>
        <member name="T:Microsoft.Azure.Storage.RetryPolicies.LocationMode">
            <summary>
            Specifies the location mode to indicate which location should receive the request.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.RetryPolicies.LocationMode.PrimaryOnly">
            <summary>
            Requests are always sent to the primary location.
            </summary>
            <remarks>
            If this value is used for requests that only work against a secondary location
            (GetServiceStats, for example), the request will fail in the client.
            </remarks>
        </member>
        <member name="F:Microsoft.Azure.Storage.RetryPolicies.LocationMode.PrimaryThenSecondary">
            <summary>
            Requests are always sent to the primary location first. If a request fails, it is sent to the secondary location.
            </summary>
            <remarks>
            If this value is used for requests that are only valid against one location, the client will
            only target the allowed location.
            </remarks>
        </member>
        <member name="F:Microsoft.Azure.Storage.RetryPolicies.LocationMode.SecondaryOnly">
            <summary>
            Requests are always sent to the secondary location.
            </summary>
            <remarks>
            If this value is used for requests that only work against a primary location
            (create, modify, and delete APIs), the request will fail in the client.
            </remarks>
        </member>
        <member name="F:Microsoft.Azure.Storage.RetryPolicies.LocationMode.SecondaryThenPrimary">
            <summary>
            Requests are always sent to the secondary location first. If a request fails, it is sent to the primary location.
            </summary>
            <remarks>
            If this value is used for requests that are only valid against one location, the client will
            only target the allowed location.
            </remarks>
        </member>
        <member name="T:Microsoft.Azure.Storage.RetryPolicies.NoRetry">
            <summary>
            Represents a retry policy that performs no retries.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.RetryPolicies.NoRetry.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Azure.Storage.RetryPolicies.NoRetry"/> class.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.RetryPolicies.NoRetry.ShouldRetry(System.Int32,System.Int32,System.Exception,System.TimeSpan@,Microsoft.Azure.Storage.OperationContext)">
            <summary>
            Determines if the operation should be retried and how long to wait until the next retry. 
            </summary>
            <param name="currentRetryCount">An integer specifying the number of retries for the given operation. A value of zero signifies this is the first error encountered.</param>
            <param name="statusCode">An integer containing the status code for the last operation.</param>
            <param name="lastException">An <see cref="T:System.Exception"/> object that represents the last exception encountered.</param>
            <param name="retryInterval">A <see cref="T:System.TimeSpan"/> indicating the interval to wait until the next retry.</param>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param>
            <returns><c>true</c> if the operation should be retried; otherwise, <c>false</c>.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.RetryPolicies.NoRetry.CreateInstance">
            <summary>
            Generates a new retry policy for the current request attempt.
            </summary>
            <returns>An <see cref="T:Microsoft.Azure.Storage.RetryPolicies.IRetryPolicy"/> object that represents the retry policy for the current request attempt.</returns>
        </member>
        <member name="T:Microsoft.Azure.Storage.RetryPolicies.RetryContext">
            <summary>
            Represents the context for one or more retries of a request made against the Microsoft Azure storage services,
            including the number of retries made for the request, the results of the last request, and the storage location and location mode for subsequent retries.
            </summary>
        </member>
        <member name="P:Microsoft.Azure.Storage.RetryPolicies.RetryContext.NextLocation">
            <summary>
            Gets the target location for the next retry.
            </summary>
            <value>A <see cref="T:Microsoft.Azure.Storage.StorageLocation"/> enumeration value.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.RetryPolicies.RetryContext.LocationMode">
            <summary>
            Gets the location mode for subsequent retries.
            </summary>
            <value>A <see cref="T:Microsoft.Azure.Storage.RetryPolicies.LocationMode"/> enumeration value.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.RetryPolicies.RetryContext.CurrentRetryCount">
            <summary>
            Gets the number of retries for the given operation.
            </summary>
            <value>An integer specifying the number of retries for the given operation.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.RetryPolicies.RetryContext.LastRequestResult">
            <summary>
            Gets the results of the last request.
            </summary>
            <value>A <see cref="T:Microsoft.Azure.Storage.RequestResult"/> object.</value>
        </member>
        <member name="M:Microsoft.Azure.Storage.RetryPolicies.RetryContext.ToString">
            <summary>
            Returns a string that represents the current <see cref="T:Microsoft.Azure.Storage.RetryPolicies.RetryContext"/> instance.
            </summary>
            <returns>A string that represents the current <see cref="T:Microsoft.Azure.Storage.RetryPolicies.RetryContext"/> instance.</returns>
        </member>
        <member name="T:Microsoft.Azure.Storage.RetryPolicies.RetryInfo">
            <summary>
            Specifies parameters for the next retry of a request to be made against the Microsoft Azure storage services,
            including the target location and location mode for the next retry and the interval until the next retry.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.RetryPolicies.RetryInfo.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Azure.Storage.RetryPolicies.RetryInfo"/> class.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.RetryPolicies.RetryInfo.#ctor(Microsoft.Azure.Storage.RetryPolicies.RetryContext)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Azure.Storage.RetryPolicies.RetryInfo"/> class.
            </summary>
            <param name="retryContext">The <see cref="T:Microsoft.Azure.Storage.RetryPolicies.RetryContext"/> object that was passed in to the retry policy.</param>
        </member>
        <member name="P:Microsoft.Azure.Storage.RetryPolicies.RetryInfo.TargetLocation">
            <summary>
            Gets or sets the target location for the next retry.
            </summary>
            <value>A <see cref="T:Microsoft.Azure.Storage.StorageLocation"/> enumeration value.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.RetryPolicies.RetryInfo.UpdatedLocationMode">
            <summary>
            Gets or sets the location mode for subsequent retries.
            </summary>
            <value>A <see cref="T:Microsoft.Azure.Storage.RetryPolicies.LocationMode"/> enumeration value.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.RetryPolicies.RetryInfo.RetryInterval">
            <summary>
            Gets the interval until the next retry.
            </summary>
            <value>A <see cref="T:System.TimeSpan"/> object specifying the interval until the next retry.</value>
        </member>
        <member name="M:Microsoft.Azure.Storage.RetryPolicies.RetryInfo.ToString">
            <summary>
            Returns a string that represents the current <see cref="T:Microsoft.Azure.Storage.RetryPolicies.RetryInfo"/> instance.
            </summary>
            <returns>A string that represents the current <see cref="T:Microsoft.Azure.Storage.RetryPolicies.RetryInfo"/> instance.</returns>
        </member>
        <member name="T:Microsoft.Azure.Storage.SharedAccessAccountPermissions">
            <summary>
            Specifies the set of possible permissions for a shared access account policy.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.SharedAccessAccountPermissions.None">
            <summary>
            No shared access granted.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.SharedAccessAccountPermissions.Read">
            <summary>
            Permission to read resources and list queues and tables granted.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.SharedAccessAccountPermissions.Add">
            <summary>
            Permission to add messages, table entities, blobs, and files granted.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.SharedAccessAccountPermissions.Create">
            <summary>
            Permission to create containers, blobs, shares, directories, and files granted.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.SharedAccessAccountPermissions.Update">
            <summary>
            Permissions to update messages and table entities granted.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.SharedAccessAccountPermissions.ProcessMessages">
            <summary>
            Permission to get and delete messages granted.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.SharedAccessAccountPermissions.Write">
            <summary>
            Permission to write resources granted.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.SharedAccessAccountPermissions.Delete">
            <summary>
            Permission to delete resources granted.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.SharedAccessAccountPermissions.List">
            <summary>
            Permission to list blob containers, blobs, shares, directories, and files granted.
            </summary>
        </member>
        <member name="T:Microsoft.Azure.Storage.SharedAccessAccountPolicy">
            <summary>
            Represents a shared access policy for a account, which specifies the start time, expiry time, 
            permissions, signed service, signed resource type, signed protocol, and signed IP addresses for a shared access signature.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.SharedAccessAccountPolicy.#ctor">
            <summary>
            Initializes a new instance of the SharedAccessAccountPolicy class.
            </summary>
        </member>
        <member name="P:Microsoft.Azure.Storage.SharedAccessAccountPolicy.SharedAccessStartTime">
            <summary>
            Gets or sets the start time for a shared access signature associated with this shared access policy.
            </summary>
            <value>A <see cref="T:System.DateTimeOffset"/> specifying the shared access start time.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.SharedAccessAccountPolicy.SharedAccessExpiryTime">
            <summary>
            Gets or sets the expiry time for a shared access signature associated with this shared access policy.
            </summary>
            <value>A <see cref="T:System.DateTimeOffset"/> specifying the shared access expiry time.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.SharedAccessAccountPolicy.Permissions">
            <summary>
            Gets or sets the permissions for a shared access signature associated with this shared access policy.
            </summary>
            <value>A <see cref="T:Microsoft.Azure.Storage.SharedAccessAccountPermissions"/> object.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.SharedAccessAccountPolicy.Services">
            <summary>
            Gets or sets the services (blob, file, queue, table) for a shared access signature associated with this shared access policy.
            </summary>
        </member>
        <member name="P:Microsoft.Azure.Storage.SharedAccessAccountPolicy.ResourceTypes">
            <summary>
            Gets or sets the resource type for a shared access signature associated with this shared access policy.
            </summary>
        </member>
        <member name="P:Microsoft.Azure.Storage.SharedAccessAccountPolicy.Protocols">
            <summary>
            Gets or sets the allowed protocols for a shared access signature associated with this shared access policy.
            </summary>
        </member>
        <member name="P:Microsoft.Azure.Storage.SharedAccessAccountPolicy.IPAddressOrRange">
            <summary>
            Gets or sets the allowed IP address or IP address range for a shared access signature associated with this shared access policy.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.SharedAccessAccountPolicy.PermissionsToString(Microsoft.Azure.Storage.SharedAccessAccountPermissions)">
            <summary>
            Converts the permissions specified for the shared access policy to a string.
            </summary>
            <param name="permissions">A <see cref="T:Microsoft.Azure.Storage.SharedAccessAccountPermissions"/> object.</param>
            <returns>The shared access permissions in string format.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.SharedAccessAccountPolicy.ServicesToString(Microsoft.Azure.Storage.SharedAccessAccountServices)">
            <summary>
            Converts the services specified for the shared access policy to a string.
            </summary>
            <param name="services">A <see cref="T:Microsoft.Azure.Storage.SharedAccessAccountServices"/> object.</param>
            <returns>The shared access services in string format.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.SharedAccessAccountPolicy.ResourceTypesToString(Microsoft.Azure.Storage.SharedAccessAccountResourceTypes)">
            <summary>
            Converts the ResourceTypes specified for the shared access policy to a string.
            </summary>
            <param name="resourceTypes">A <see cref="T:Microsoft.Azure.Storage.SharedAccessAccountResourceTypes"/> object.</param>
            <returns>The shared access resource types in string format.</returns>
        </member>
        <member name="T:Microsoft.Azure.Storage.SharedAccessAccountResourceTypes">
            <summary>
            Specifies the set of possible signed resource types for a shared access account policy.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.SharedAccessAccountResourceTypes.None">
            <summary>
            No shared access granted.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.SharedAccessAccountResourceTypes.Service">
            <summary>
            Permission to access service level APIs granted.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.SharedAccessAccountResourceTypes.Container">
            <summary>
            Permission to access container level APIs (Blob Containers, Tables, Queues, File Shares) granted.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.SharedAccessAccountResourceTypes.Object">
            <summary>
            Permission to access object level APIs (Blobs, Table Entities, Queue Messages, Files) granted
            </summary>
        </member>
        <member name="T:Microsoft.Azure.Storage.SharedAccessAccountServices">
            <summary>
            Specifies the set of possible signed services for a shared access account policy.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.SharedAccessAccountServices.None">
            <summary>
            No shared access granted.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.SharedAccessAccountServices.Blob">
            <summary>
            Permission to access blob resources granted.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.SharedAccessAccountServices.File">
            <summary>
            Permission to access file resources granted.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.SharedAccessAccountServices.Queue">
            <summary>
            Permission to access queue resources granted.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.SharedAccessAccountServices.Table">
            <summary>
            Permission to access table resources granted.
            </summary>
        </member>
        <member name="T:Microsoft.Azure.Storage.SharedAccessProtocol">
            <summary>
            Specifies the set of possible signed protocols for a shared access account policy.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.SharedAccessProtocol.HttpsOnly">
            <summary>
            Permission to use SAS only through https granted.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.SharedAccessProtocol.HttpsOrHttp">
            <summary>
            Permission to use SAS through https or http granted. Equivalent to not specifying any permission at all.
            </summary>
        </member>
        <member name="T:Microsoft.Azure.Storage.StorageException">
            <summary>
            Represents an exception thrown by the Azure Storage service.
            </summary>
        </member>
        <member name="P:Microsoft.Azure.Storage.StorageException.RequestInformation">
            <summary>
            Gets the <see cref="T:Microsoft.Azure.Storage.RequestResult"/> object for this <see cref="T:Microsoft.Azure.Storage.StorageException"/> object.
            </summary>
            <value>The <see cref="T:Microsoft.Azure.Storage.RequestResult"/> object for this <see cref="T:Microsoft.Azure.Storage.StorageException"/> object.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.StorageException.IsRetryable">
            <summary>
            Indicates if exception is retryable.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.StorageException.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Azure.Storage.StorageException"/> class.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.StorageException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Azure.Storage.StorageException"/> class using the specified error message.
            </summary>
            <param name="message">The message that describes the error.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.StorageException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Azure.Storage.StorageException"/> class with a specified error message and a reference to the inner exception that generated this exception.
            </summary>
            <param name="message">The exception error message.</param>
            <param name="innerException">The inner exception.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.StorageException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Azure.Storage.StorageException"/> class with serialized data.
            </summary>
            <param name="info">The <see cref="T:System.Runtime.Serialization.SerializationInfo"/> object that holds serialized object data for the exception being thrown.</param>
            <param name="context">The <see cref="T:System.Runtime.Serialization.StreamingContext"/> that contains contextual information about the source or destination.</param>
            <remarks>This constructor is called during de-serialization to reconstitute the exception object transmitted over a stream.</remarks>
        </member>
        <member name="M:Microsoft.Azure.Storage.StorageException.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Populates a <see cref="T:System.Runtime.Serialization.SerializationInfo"/> object with the data needed to serialize the target object.
            </summary>
            <param name="info">The <see cref="T:System.Runtime.Serialization.SerializationInfo"/> object to populate with data.</param>
            <param name="context">The destination context for this serialization.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.StorageException.#ctor(Microsoft.Azure.Storage.RequestResult,System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Azure.Storage.StorageException"/> class by using the specified parameters.
            </summary>
            <param name="res">The request result.</param>
            <param name="message">The exception message.</param>
            <param name="inner">The inner exception.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.StorageException.TranslateExceptionAsync(System.Exception,Microsoft.Azure.Storage.RequestResult,System.Func{System.IO.Stream,System.Threading.CancellationToken,System.Threading.Tasks.Task{Microsoft.Azure.Storage.StorageExtendedErrorInformation}},System.Threading.CancellationToken,System.Net.Http.HttpResponseMessage)">
            <summary>
            Translates the specified exception into a storage exception.
            </summary>
            <param name="ex">The exception to translate.</param>
            <param name="reqResult">The request result.</param>
            <param name="parseErrorAsync">The delegate used to parse the error to get extended error information.</param>
            <param name="cancellationToken">cancellation token for the async operation</param>
            
            <returns>The storage exception.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.StorageException.TranslateExceptionWithPreBufferedStream(System.Exception,Microsoft.Azure.Storage.RequestResult,System.Func{System.IO.Stream,Microsoft.Azure.Storage.StorageExtendedErrorInformation},System.IO.Stream,System.Net.Http.HttpResponseMessage)">
            <summary>
            Translates the specified exception into a storage exception.
            </summary>
            <param name="ex">The exception to translate.</param>
            <param name="reqResult">The request result.</param>
            <param name="parseError">The delegate used to parse the error to get extended error information.</param>
            <param name="responseStream">The error stream that contains the error information.</param>
            <param name="response">HTTP response message</param>
            <returns>The storage exception.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.StorageException.CoreTranslate(System.Exception,Microsoft.Azure.Storage.RequestResult,System.Func{System.IO.Stream,Microsoft.Azure.Storage.StorageExtendedErrorInformation}@)">
            <summary>
            Tries to translate the specified exception into a storage exception.
            </summary>
            <param name="ex">The exception to translate.</param>
            <param name="reqResult">The request result.</param>
            <param name="parseError">The delegate used to parse the error to get extended error information.</param>
            <returns>The storage exception or <c>null</c>.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.StorageException.CoreTranslateAsync(System.Exception,Microsoft.Azure.Storage.RequestResult,System.Threading.CancellationToken)">
            <summary>
            Tries to translate the specified exception into a storage exception.
            Note: we can probably combine this with the above CoreTranslate, this doesn't need to be async.
            </summary>
            <param name="ex">The exception to translate.</param>
            <param name="reqResult">The request result.</param>
            <param name="parseError">The delegate used to parse the error to get extended error information.</param>
            <returns>The storage exception or <c>null</c>.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.StorageException.PopulateRequestResult(Microsoft.Azure.Storage.RequestResult,System.Net.Http.HttpResponseMessage)">
            <summary>
            Populate the RequestResult.
            </summary>
            <param name="reqResult">The request result.</param>
            <param name="response">The web response.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.StorageException.ToString">
            <summary>
            Represents an exception thrown by the Microsoft Azure storage client library. 
            </summary>
            <returns>A string that represents the exception.</returns>
        </member>
        <member name="T:Microsoft.Azure.Storage.StorageExtendedErrorInformation">
            <summary>
            Represents extended error information returned by the Microsoft Azure storage services.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.StorageExtendedErrorInformation.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Azure.Storage.StorageExtendedErrorInformation"/> class.
            </summary>
        </member>
        <member name="P:Microsoft.Azure.Storage.StorageExtendedErrorInformation.ErrorCode">
            <summary>
            Gets the storage service error code.
            </summary>
            <value>A string containing the storage service error code.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.StorageExtendedErrorInformation.ErrorMessage">
            <summary>
            Gets the storage service error message.
            </summary>
            <value>A string containing the storage service error message.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.StorageExtendedErrorInformation.AdditionalDetails">
            <summary>
            Gets additional error details from XML-formatted input stream.
            </summary>
            <value>An <see cref="T:System.Collections.Generic.IDictionary`2"/> containing the additional error details.</value>
        </member>
        <member name="M:Microsoft.Azure.Storage.StorageExtendedErrorInformation.ReadFromStreamAsync(System.IO.Stream)">
            <summary>
            Gets the error details from an XML-formatted error stream.
            </summary>
            <param name="inputStream">The input stream.</param>
            <returns>The error details.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.StorageExtendedErrorInformation.ReadFromStreamAsync(System.IO.Stream,System.Threading.CancellationToken)">
            <summary>
            Gets the error details from an XML-formatted error stream.
            </summary>
            <param name="inputStream">The input stream.</param>
            <returns>The error details.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.StorageExtendedErrorInformation.ReadXmlAsync(System.Xml.XmlReader,System.Threading.CancellationToken)">
            <summary>
            Generates a serializable <see cref="T:Microsoft.Azure.Storage.StorageExtendedErrorInformation"/> object from its XML representation.
            </summary>
            <param name="reader">The <see cref="T:System.Xml.XmlReader"/> stream from which the <see cref="T:Microsoft.Azure.Storage.StorageExtendedErrorInformation"/> object is deserialized.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.StorageExtendedErrorInformation.WriteXml(System.Xml.XmlWriter)">
            <summary>
            Converts a serializable <see cref="T:Microsoft.Azure.Storage.StorageExtendedErrorInformation"/> object into its XML representation.
            </summary>
            <param name="writer">The <see cref="T:System.Xml.XmlWriter"/> stream to which the <see cref="T:Microsoft.Azure.Storage.StorageExtendedErrorInformation"/> object is serialized.</param>
        </member>
        <member name="T:Microsoft.Azure.Storage.StorageLocation">
            <summary>
            Represents a storage service location.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.StorageLocation.Primary">
            <summary>
            Primary storage service location.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.StorageLocation.Secondary">
            <summary>
            Secondary storage service location.
            </summary>
        </member>
        <member name="T:Microsoft.Azure.Storage.StorageUri">
            <summary>
            Contains the URIs for both the primary and secondary locations of a Microsoft Azure Storage resource.
            </summary>
        </member>
        <member name="P:Microsoft.Azure.Storage.StorageUri.PrimaryUri">
            <summary>
            The endpoint for the primary location for the storage account.
            </summary>
            <value>The <see cref="T:System.Uri"/> for the primary endpoint.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.StorageUri.SecondaryUri">
            <summary>
            The endpoint for the secondary location for the storage account.
            </summary>
            <value>The <see cref="T:System.Uri"/> for the secondary endpoint.</value>
        </member>
        <member name="M:Microsoft.Azure.Storage.StorageUri.#ctor(System.Uri)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Azure.Storage.StorageUri"/> class using the primary endpoint for the storage account.
            </summary>
            <param name="primaryUri">The <see cref="T:System.Uri"/> for the primary endpoint.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.StorageUri.#ctor(System.Uri,System.Uri)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Azure.Storage.StorageUri"/> class using the primary and secondary endpoints for the storage account.
            </summary>
            <param name="primaryUri">The <see cref="T:System.Uri"/> for the primary endpoint.</param>
            <param name="secondaryUri">The <see cref="T:System.Uri"/> for the secondary endpoint.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.StorageUri.GetUri(Microsoft.Azure.Storage.StorageLocation)">
            <summary>
            Returns the URI for the storage account endpoint at the specified location.
            </summary>
            <param name="location">A <see cref="T:Microsoft.Azure.Storage.StorageLocation"/> enumeration value.</param>
            <returns>The <see cref="T:System.Uri"/> for the endpoint at the the specified location.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.StorageUri.ToString">
            <summary>
            Returns a <see cref="T:System.String"/> that represents this instance.
            </summary>
            <returns>
            A <see cref="T:System.String"/> that represents this instance.
            </returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.StorageUri.GetHashCode">
            <summary>
            Returns a hash code for this instance.
            </summary>
            <returns>
            A hash code for this instance, suitable for use in hashing algorithms and data structures like a hash table. 
            </returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.StorageUri.Equals(System.Object)">
            <summary>
            Determines whether the specified <see cref="T:System.Object"/> is equal to this instance.
            </summary>
            <param name="obj">The <see cref="T:System.Object"/> to compare with this instance.</param>
            <returns><c>true</c> if the specified <see cref="T:System.Object"/> is equal to this instance; otherwise, <c>false</c>.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.StorageUri.Equals(Microsoft.Azure.Storage.StorageUri)">
            <summary>
            Indicates whether the current object is equal to another object of the same type.
            </summary>
            <param name="other">An object to compare with this object.</param>
            <returns><c>true</c> if the current object is equal to the <paramref name="other"/> parameter; otherwise, <c>false</c>.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.StorageUri.op_Equality(Microsoft.Azure.Storage.StorageUri,Microsoft.Azure.Storage.StorageUri)">
            <summary>
            Compares two <see cref="T:Microsoft.Azure.Storage.StorageUri"/> objects for equivalency.
            </summary>
            <param name="uri1">The first <see cref="T:Microsoft.Azure.Storage.StorageUri"/> object to compare.</param>
            <param name="uri2">The second <see cref="T:Microsoft.Azure.Storage.StorageUri"/> object to compare.</param>
            <returns><c>true</c> if the <see cref="T:Microsoft.Azure.Storage.StorageUri"/> objects have equivalent values; otherwise, <c>false</c>.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.StorageUri.op_Inequality(Microsoft.Azure.Storage.StorageUri,Microsoft.Azure.Storage.StorageUri)">
            <summary>
            Compares two <see cref="T:Microsoft.Azure.Storage.StorageUri"/> objects for non-equivalency.
            </summary>
            <param name="uri1">The first <see cref="T:Microsoft.Azure.Storage.StorageUri"/> object to compare.</param>
            <param name="uri2">The second <see cref="T:Microsoft.Azure.Storage.StorageUri"/> object to compare.</param>
            <returns><c>true</c> if the <see cref="T:Microsoft.Azure.Storage.StorageUri"/> objects have non-equivalent values; otherwise, <c>false</c>.</returns>
        </member>
        <member name="T:Microsoft.Azure.Storage.UserDelegationKey">
            <summary>
            Represents a user delegation key, provided to the user by Azure Storage
            based on their Azure Active Directory access token.
            </summary>
        </member>
        <member name="P:Microsoft.Azure.Storage.UserDelegationKey.SignedOid">
            <summary>
            Object ID of this token.
            </summary>
            <value>A <see cref="T:System.Guid"/> representing the object ID.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.UserDelegationKey.SignedTid">
            <summary>
            Tenant ID of the tenant that issued this token.
            </summary>
            <value>A <see cref="T:System.Guid"/> representing the tenant ID.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.UserDelegationKey.SignedStart">
            <summary>
            The datetime this token becomes valid.
            </summary>
            <value>A <see cref="T:System.DateTimeOffset"/> representing the time.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.UserDelegationKey.SignedExpiry">
            <summary>
            The datetime this token expires.
            </summary>
            <value>A <see cref="T:System.DateTimeOffset"/> representing the time.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.UserDelegationKey.SignedService">
            <summary>
            What service this key is valid for.
            </summary>
            <value>The REST service's abbreviation of the service type.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.UserDelegationKey.SignedVersion">
            <summary>
            The version identifier of the REST service that created this token.
            </summary>
            <value>A <see cref="T:System.String"/> identifying the version.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.UserDelegationKey.Value">
            <summary>
            The user delegation key.
            </summary>
            <value>A <see cref="T:System.String"/> representing the key in base 64.</value>
        </member>
        <member name="T:Microsoft.Azure.Storage.WrappedKey">
            <summary>
            Represents the envelope key details stored on the service.
            </summary>
        </member>
        <member name="P:Microsoft.Azure.Storage.WrappedKey.KeyId">
            <summary>
            Gets or sets the key identifier. This identifier is used to identify the key that is used to wrap/unwrap the content encryption key.
            </summary>
            <value>The key identifier string.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.WrappedKey.EncryptedKey">
            <summary>
            Gets or sets the encrypted content encryption key.
            </summary>
            <value>The encrypted content encryption key.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.WrappedKey.Algorithm">
            <summary>
            The algorithm used for wrapping.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.WrappedKey.#ctor(System.String,System.Byte[],System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Azure.Storage.WrappedKey"/> class using the specified key id, encrypted key and the algorithm.
            </summary>
            <param name="keyId">The key identifier string.</param>
            <param name="encryptedKey">The encrypted content encryption key.</param>
            <param name="algorithm">The algorithm used for wrapping.</param>
        </member>
    </members>
</doc>
