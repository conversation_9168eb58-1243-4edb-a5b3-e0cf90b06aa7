///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.Invoices_Confirm = function (element) {
    Rebound.GlobalTrader.Site.Controls.Forms.Invoices_Confirm.initializeBase(this, [element]);
    this._strInvoices = "";
    this._blnCOC = false;
    this._blnPackagingSlip = false;
    this._ClientNo = "";
    this._AllowGenerateXml = false;
};

Rebound.GlobalTrader.Site.Controls.Forms.Invoices_Confirm.prototype = {
    initialize: function () {
        Rebound.GlobalTrader.Site.Controls.Forms.Invoices_Confirm.callBaseMethod(this, "initialize");
        this.addShown(Function.createDelegate(this, this.formShown));
    },

    dispose: function () {
        if (this.isDisposed) return;
        if (this._ctlConfirm) this._ctlConfirm.dispose();
        this._ctlConfirm = null;
        this._strInvoices = null;
        this._blnCOC = null;
        this._ClientNo = null;
        this._AllowGenerateXml = null;
        Rebound.GlobalTrader.Site.Controls.Forms.Invoices_Confirm.callBaseMethod(this, "dispose");
    },

    formShown: function () {
        if (this._blnFirstTimeShown) {
            this._ctlConfirm = this.getFieldComponent("ctlConfirm");
            this._ctlConfirm.addClickYesEvent(Function.createDelegate(this, this.yesClicked));
            this._ctlConfirm.addClickNoEvent(Function.createDelegate(this, this.noClicked));
        }
        this.showFormatOptions();
    },

    yesClicked: function () {
        this.showSaving(true);
        var isXmlFormat = (this._AllowGenerateXml && document.querySelector('.optionXML input[type="radio"]').checked) ? true : false;
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/InvoiceMainInfo");
        obj.set_DataObject("InvoiceMainInfo");
        obj.set_DataAction("SaveInvoiceEmail");
        obj.addParameter("Invoices", this._strInvoices);
        obj.addParameter("coc", this._blnCOC);
        obj.addParameter("packagingSlip", this._blnPackagingSlip);
        obj.addParameter("isXmlFormat", isXmlFormat);
        obj.addDataOK(Function.createDelegate(this, this.saveComplete));
        obj.addError(Function.createDelegate(this, this.saveError));
        obj.addTimeout(Function.createDelegate(this, this.saveError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
        this.onSave();
    },

    noClicked: function () {
        this.showSaving(false);
        this.onNotConfirmed();
    },

    saveError: function (args) {
        this.showSaving(false);
        this._strErrorMessage = args._errorMessage;
        this.onSaveError();
    },

    saveComplete: function (args) {
        this.showSaving(false);
        if (args._result.Result >= 1) {
            this.onSaveComplete();
            if (args._result.Result == 1) {
                alert($R_RES.FinanceContactNotFoundMessage + ' ' + args._result.Invoices);
            }
            else if (args._result.Result == 2) {
                alert($R_RES.InvoiceProgressMessage + '\n\n' + $R_RES.FinanceContactNotFoundMessage + ' ' + args._result.Invoices);
            }
            else {
                alert($R_RES.InvoiceProgressMessage);
            }
        } else {
            this._strErrorMessage = args._errorMessage;
            this.onSaveError();
        }
    },
    showFormatOptions: function () {
        if (!this._AllowGenerateXml) {
            document.querySelector('.optionXML').classList.add('invisible');
            document.querySelector('.optionPDF input[type="radio"]').checked = true;
            return;
        }
        document.querySelector('.optionXML').classList.remove('invisible');
        if (this._ClientNo == '108') {
            document.querySelector('.optionXML input[type="radio"]').checked = true;
        } else {
            document.querySelector('.optionPDF input[type="radio"]').checked = true;
        }
    }
};

Rebound.GlobalTrader.Site.Controls.Forms.Invoices_Confirm.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.Invoices_Confirm", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
