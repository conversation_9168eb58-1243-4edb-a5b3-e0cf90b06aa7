using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site;
using Rebound.GlobalTrader.Site.Controls;

namespace Rebound.GlobalTrader.Site.Controls.Forms {
	public partial class GILines_Inspect : Base {

		#region Locals

		#endregion

		#region Properties

        private int _intGIID = -1;
        public int GIID
        {
            get { return _intGIID; }
            set { _intGIID = value; }
        }

		private int _intLineID;
		public int LineID {
			get { return _intLineID; }
			set { _intLineID = value; }
		}

		#endregion

		#region Overrides

		/// <summary>
		/// OnInit
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
			base.OnInit(e);
            TitleText = Functions.GetGlobalResource("FormTitles", "GILines_Inspect");
            AddScriptReference("Controls.Nuggets.GILines.Inspect.GILines_Inspect.js");
            if (_objQSManager.GoodsInID > 0) _intGIID = _objQSManager.GoodsInID;
            WireUpControls();
        }

		/// <summary>
		/// OnPreRender
		/// </summary>
		/// <param name="e"></param>
		protected override void OnPreRender(EventArgs e) {
			SetupScriptDescriptors();
			base.OnPreRender(e);
		}

		#endregion

        /// <summary>
        /// Wire up controls to the ascx
        /// </summary>
        private void WireUpControls()
        {
        }

		/// <summary>
		/// Setup Script Descriptors
		/// </summary>
		private void SetupScriptDescriptors() {
            _scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.Forms.GILines_Inspect", ctlDesignBase.ClientID);
			_scScriptControlDescriptor.AddProperty("intLineID", _intLineID);
            _scScriptControlDescriptor.AddProperty("intGIID", _intGIID);
        }
	}
}