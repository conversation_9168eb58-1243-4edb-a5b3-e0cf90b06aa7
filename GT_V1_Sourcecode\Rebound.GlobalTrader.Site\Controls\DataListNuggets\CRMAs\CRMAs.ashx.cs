//----------------------------------------------------------------------------------------------
// RP 06.09.2010:
// - fix Include<PERSON><PERSON>eived not working
//----------------------------------------------------------------------------------------------
/* Marker     changed by      date         Remarks
/* [0001]      Abhinav <PERSON>   14/07/2014  ESMS Ref:172 - Issue in Part Searching in whole GT */

using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;
using Rebound.GlobalTrader.Site.Enumerations;

namespace Rebound.GlobalTrader.Site.Controls.DataListNuggets.Data {

	public class CRMAs : Base {

		protected override void GetData() {
			JsonObject jsn = new JsonObject();

			//check view level
			ViewLevelList enmViewLevel = (ViewLevelList)GetFormValue_Int("ViewLevel");

			//get data	
			List<CustomerRmaLine> lst = CustomerRmaLine.DataListNugget(
				SessionManager.ClientID
				, (enmViewLevel == ViewLevelList.Team) ? (int?)SessionManager.LoginTeamID : null
				, (enmViewLevel == ViewLevelList.Division) ? (int?)SessionManager.LoginDivisionID : null
				, (enmViewLevel == ViewLevelList.My) ? (int?)LoginID : null
				, GetFormValue_NullableInt("PageIndex", 0)
				, GetFormValue_NullableInt("PageSize", 10)
				, GetFormValue_NullableInt("SortIndex")
				, GetFormValue_NullableInt("SortDir", SortColumnDirection.ASC)
                //[0001] start code
                //, GetFormValue_StringForPartSearch("Part")
                  , GetFormValue_PartForLikeSearch("Part")
                //[0001] end code
               // , GetFormValue_StringForNameSearch("Contact")
                , GetFormValue_StringForNameSearchDecode("Contact")
                //, GetFormValue_StringForNameSearch("CMName")
                 , GetFormValue_StringForNameSearchDecode("CMName")
				, GetFormValue_NullableInt("SalesmanName")
				, GetFormValue_StringForSearch("CRMANotes")
				, GetFormValue_NullableInt("InvNoLo")
				, GetFormValue_NullableInt("InvNoHi")
				, GetFormValue_NullableInt("CRMANoLo")
				, GetFormValue_NullableInt("CRMANoHi")
				, GetFormValue_NullableDateTime("CRMADateFrom", null)
				, GetFormValue_NullableDateTime("CRMADateTo", null)
				, GetFormValue_Boolean("IncludeReceived")
                , GetFormValue_Boolean("RecentOnly")
                 , SessionManager.IsPOHub == true ? GetFormValue_Boolean("PohubOnly") : false
                 ,null
                 ,false
           );

			//check counts
			jsn.AddVariable("TotalRecords", (lst.Count > 0) ? lst[0].RowCnt : 0);

			//format data
			JsonObject jsnRowsArray = new JsonObject(true);
			for (int i = 0; i < lst.Count; i++) {
				JsonObject jsnRow = new JsonObject();
				jsnRow.AddVariable("ID", lst[i].CustomerRMAId);
				jsnRow.AddVariable("No", lst[i].CustomerRMANumber);
				jsnRow.AddVariable("Part", lst[i].Part);
				jsnRow.AddVariable("Quantity", Functions.FormatNumeric(lst[i].Quantity));
				jsnRow.AddVariable("Date", Functions.FormatDate(lst[i].CustomerRMADate));
				jsnRow.AddVariable("CM", lst[i].CompanyName);
				jsnRow.AddVariable("CMNo", lst[i].CompanyNo);
				jsnRow.AddVariable("Invoice", lst[i].InvoiceNumber);
				jsnRow.AddVariable("InvNo", lst[i].InvoiceNo);
				jsnRow.AddVariable("Contact", lst[i].ContactName);
				jsnRow.AddVariable("ContactNo", lst[i].ContactNo);
				jsnRow.AddVariable("Mfr", lst[i].ManufacturerCode);
				jsnRow.AddVariable("MfrNo", lst[i].ManufacturerNo);
				jsnRow.AddVariable("ROHS", lst[i].ROHS);
				jsnRowsArray.AddVariable(jsnRow);
				jsnRow.Dispose();
				jsnRow = null;
			}
			jsn.AddVariable("Results", jsnRowsArray);
			OutputResult(jsn);
			jsnRowsArray.Dispose();
			jsnRowsArray = null;
			jsn.Dispose();
			jsn = null;
			base.GetData();
		}

		protected override void AddFilterStates() {
            //Prevent filter state for Tab
			//AddExplicitFilterState("ViewLevel", GetFormValue_Int("ViewLevel"));
			AddFilterState("Part");
			AddFilterState("Contact");
			AddFilterState("CMName");
			AddFilterState("SalesmanName");
			AddFilterState("CRMANotes");
			AddFilterState("InvNo");
			AddFilterState("CRMANo");
			AddFilterState("CRMADateFrom");
			AddFilterState("CRMADateTo");
            AddFilterState("ctlIncludeReceived");
            AddFilterState("RecentOnly");
            AddFilterState("PohubOnly");
            base.AddFilterStates();
		}
	}
}