<%--
 Marker     changed by      date           Remarks
[001]       <PERSON><PERSON>   12/09/2018    Add Header, Detail radio export Csv button
--%>

<%@ Control Language="C#" CodeBehind="BOMManagerLinesAssign.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.DataListNuggets.BOMManagerLinesAssign" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Nugget:DesignBase ID="ctlDB" runat="server" BoxType="List">
    <%--[001] code start--%>
    <Links>
        <ReboundUI:IconButton  ID="ibtnExportCSV" runat="server" Style="margin-left:8px; display:none;" IconButtonMode="hyperlink" IconGroup="Nugget" IconTitleResource="ExportToExcel" />
     <span style="margin-left:15%;">
    <%--<label style="font-weight: bold;font-size: 11px;color: #009900;">Assign User</label>--%>
     <label style="font-weight: bold;font-size: 11px;color: #009900;"></label>
    <input type="checkbox" id="chkAssign" name="AssignmentType"  title="User" value="User" />
         <br />
         <div style="height:15px; padding-top:5px;">
          <ReboundUI:MultiSelectionCount ID="ctlMultiSelectionCount" runat="server" />
             
         <div id="divAssign" style="display:none;">
         
            
         <Field><ReboundDropDown:PoHubBuyer ID="ddlSalesperson" runat="server" /></Field>
             <ReboundUI:IconButton  ID="ibtnAssign" runat="server" Style="margin-left:8px; " IconButtonMode="hyperlink" IconGroup="Nugget" IconTitleResource="AssignLead" />
         <%--<ReboundUI_Form:FormField id="ctlSalesperson" runat="server" FieldID="ddlSalesperson" ResourceTitle="Buyer" IsRequiredField="true">
				<Field><ReboundDropDown:PoHubBuyer ID="ddlSalesperson" runat="server" /></Field>
			</ReboundUI_Form:FormField>--%>
             </div></div>
    </span>
    </Links>
    <%--[001] code end--%>

    <Filters>
        <ReboundUI_DataListNugget:Filter ID="ctlFilter" runat="server">

            <FieldsLeft>
                <%--[001] code start--%>
                <ReboundUI_Form:FormField runat="server">
                    <%--<Field>
                        <asp:RadioButtonList ID="radHeaderDetail" name="radioList" runat="server" RepeatDirection="Horizontal">
                            <asp:ListItem Text="Header" Value="Header" />
                            <asp:ListItem Text="Detail" Value="Detail" />
                        </asp:RadioButtonList>
                    </Field>--%>
                </ReboundUI_Form:FormField>
                <%--[001] code end--%>

                <ReboundUI_FilterDataItemRow:TextBox ID="ctlCode" runat="server" ResourceTitle="Code"
                    FilterField="Code" />
                <ReboundUI_FilterDataItemRow:DropDown ID="ctlStatus" runat="server" DropDownType="BomManagerStatus"
                    ResourceTitle="Status" FilterField="BomManagerStatus" DropDownAssembly="Rebound.GlobalTrader.Site" />
                 
                <ReboundUI_FilterDataItemRow:DropDown ID="ctlSalesperson" runat="server" DropDownAssembly="Rebound.GlobalTrader.Site" DropDownType="PoHubBuyer" ResourceTitle="AssignedUserLead" FilterField="PoHubBuyer" />
                <%--<ReboundUI_FilterDataItemRow:DropDown ID="ddlSalesperson" runat="server" FieldID="ddlSalesperson" ResourceTitle="Buyer"/>	--%>
                <ReboundUI_FilterDataItemRow:DropDown ID="ctlClient" runat="server" DropDownType="Client"
                    ResourceTitle="Client" FilterField="Client" DropDownAssembly="Rebound.GlobalTrader.Site" />
                <ReboundUI_FilterDataItemRow:DropDown ID="ctlClientSalesperson" runat="server" DropDownAssembly="Rebound.GlobalTrader.Site"
                    DropDownType="Employee" ResourceTitle="SalesPerson" FilterField="SalesPerson" />
                 
            </FieldsLeft>
            <FieldsRight>
          
                <ReboundUI_FilterDataItemRow:TextBox ID="ctlName" runat="server" ResourceTitle="Name"
                    FilterField="Name" />
                <%--<ReboundUI_FilterDataItemRow:TextBox id="ctlCompanyName" runat="server" ResourceTitle="CompanyName" FilterField="CMName"  />--%>
                <ReboundUI_FilterDataItemRow:TextBox ID="ctlManufacturer" runat="server" ResourceTitle="Manufacturer"
                    FilterField="Manufacturer" />
                <ReboundUI_FilterDataItemRow:TextBox ID="ctlPartNumber" runat="server" ResourceTitle="PartNo"
                    FilterField="Part" />

                <%--[001] code start--%>
                    <ReboundUI_FilterDataItemRow:DropDown ID="ctlDivision" runat="server" DropDownType="Division"
                    ResourceTitle="Division" FilterField="Division" DropDownAssembly="Rebound.GlobalTrader.Site" />
                
               <ReboundUI_FilterDataItemRow:DateSelect ID="ctlStartDate" runat="server" ResourceTitle="ReceivedStartDate" FilterField="StartDate" />
                <ReboundUI_FilterDataItemRow:DateSelect ID="ctlEndDate" runat="server" ResourceTitle="ReceivedEndDate" FilterField="EndDate" />
                <%--[001] code end--%>
                
            </FieldsRight>
        </ReboundUI_DataListNugget:Filter>
          <style type="text/css">

        #ctl00_cphMain_ctlBOMManagerResults_ctlDB_pnlBoxInner .boxHeader{ height:89px !important;}
       #ctl00_cphMain_ctlBOMManagerResults_ctlDB_pnlBoxInner .boxHeaderInner{ height:89px !important;}

         
    </style>

    </Filters>
    <Forms>
		<%--<ReboundForm:BOMAssign_Confirm ID="frmConfirm" runat="server" />--%>
	</Forms>
    <Content>
        <script type="text/javascript">
            $(document).ready(function () {
                //alert('check');
                $("#chkAssign").prop('checked', true).hide();
                $('#divAssign').show();
                setTimeout(function () {
                    $("#ctl00_cphMain_ctlBOMManagerResults_ctlDB_ctl12_ddlSalesperson_ctl02").trigger('click');
                }, 1000);
            });
            $('#chkAssign').click(function () {
                if ($("#chkAssign").prop('checked') == true) {
                    $('#divAssign').show();
                    setTimeout(function () {
                        $("#ctl00_cphMain_ctlBOMManagerResults_ctlDB_ctl12_ddlSalesperson_ctl02").trigger('click');
                    }, 1000);
                }
                else {
                    $('#divAssign').hide();
                }

                
            });
        </script>
    </Content>
    <%--<Content>
          <asp:Panel ID="pnlCustReq" runat="server">
               <ReboundUI:FlexiDataTable ID="Table" runat="server" PanelHeight="250" />
                    <ReboundUI:FlexiDataTable ID="tblCustReq" runat="server" PanelHeight="250" />
          </asp:Panel>
         </Content>--%>
</ReboundUI_Nugget:DesignBase>
