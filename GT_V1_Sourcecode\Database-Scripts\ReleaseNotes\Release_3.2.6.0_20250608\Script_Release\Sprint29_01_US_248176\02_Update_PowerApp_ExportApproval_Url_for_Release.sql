/* 
===========================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-248176]		Trung Pham			4-June-2025		CREATE			Insert/update powerapp URL for Export Approval
===========================================================================================
*/
DECLARE @ExportApprovalURL NVARCHAR(MAX) = N'https://gt-uat-webapp-001.azurewebsites.net'

IF EXISTS(SELECT TOP 1 1 FROM tbPowerApp_urls WITH(NOLOCK) WHERE FlowName = 'Export Approval')
BEGIN
	UPDATE tbPowerApp_urls SET FlowUrl = @ExportApprovalURL WHERE FlowName = 'Export Approval'
END
ELSE BEGIN
	INSERT INTO tbPowerApp_urls
	(
		FlowName,
		FlowUrl
	)
	VALUES
	(
		'Export Approval'
		,@ExportApprovalURL
	)
END
GO