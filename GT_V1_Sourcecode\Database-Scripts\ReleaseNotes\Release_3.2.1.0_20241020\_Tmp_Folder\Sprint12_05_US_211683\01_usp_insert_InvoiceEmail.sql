﻿CREATE OR ALTER PROCEDURE [dbo].[usp_insert_InvoiceEmail]               
 @InvoiceNos xml ,                
 @UpdatedBy int ,    
 @AttachCOC bit=0,  
 @AttachPackagingSlip bit=0,
 @XmlFormat bit=0
AS                
-- =============================================                
-- Author:  <Vin<PERSON>>                
-- Create date: <13 july 2012>                
-- Description: <Rebound- Invoice bulk Emailer>
-- Update:
--		An.TranTan - 12-Sep-2024: Add @XmlFormat
--		An.TranTan - 07-Oct-2024: Set QUOTED_IDENTIFIER when invoke XML data type methods.
-- =============================================                
BEGIN                
      SET ANSI_PADDING ON            
      SET ANSI_WARNINGS ON
	  SET QUOTED_IDENTIFIER ON
      SELECT x.InvoiceID.query('ID').value('.', 'int') as InvoiceID INTO #TEMP_Invoice FROM @InvoiceNos.nodes('Invoices/Invoice') as x(InvoiceID)              
      SET ANSI_PADDING OFF            
      SET ANSI_WARNINGS OFF
   --Insert into tbInvoiceEmail table            
      INSERT INTO tbInvoiceEmail(              
  ClientNo,              
  InvoiceNo,              
  ContactNo,              
  ContactEmail,              
  SentStatus,              
  UpdatedBy,              
  EmailStatus,              
  DLUP ,    
  AttachCOC ,  
  AttachPackagingSlip,
  IsXmlFormat
      )                
      SELECT Inv.ClientNo,              
      Inv.InvoiceId,              
      Con.ContactId,              
      Con.EMail,0,              
      @UpdatedBy,'Pending',GETDATE(),@AttachCOC  ,   @AttachPackagingSlip, @XmlFormat        
      FROM tbContact Con LEFT OUTER JOIN tbInvoice Inv  ON Con.CompanyNo=Inv.CompanyNo         
      join #TEMP_Invoice tmp on inv.InvoiceId=tmp.InvoiceID where Con.FinanceContact=1 and con.Inactive=0                
           
   -- Popolate           
      --SELECT  distinct             
      --Inv.InvoiceNumber          
      --FROM tbContact Con LEFT OUTER JOIN tbInvoice Inv                
      --ON Con.CompanyNo=Inv.CompanyNo join               
      --#TEMP_Invoice tmp on inv.InvoiceId=tmp.InvoiceID where Con.FinanceContact=0          
Select * from (          
               Select           
                    inv.InvoiceNumber           
                   ,IsNull((Select COUNT(*) from tbContact           
                    where IsNull(FinanceContact,0)=1 and Inactive=0 and CompanyNo= inv.CompanyNo           
                    Group By CompanyNo),0) As 'CountFinanceContact'          
               from tbInvoice inv          
               Inner Join #TEMP_Invoice tmp on inv.InvoiceId=tmp.InvoiceID           
             ) as table1          
where CountFinanceContact=0          
          
        -- Drop the temp table            
      DROP TABLE #TEMP_Invoice            
END             
    
  
  
  