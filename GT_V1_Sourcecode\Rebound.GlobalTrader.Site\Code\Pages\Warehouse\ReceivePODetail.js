Type.registerNamespace("Rebound.GlobalTrader.Site.Pages.Warehouse");Rebound.GlobalTrader.Site.Pages.Warehouse.ReceivePODetail=function(n){Rebound.GlobalTrader.Site.Pages.Warehouse.ReceivePODetail.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Pages.Warehouse.ReceivePODetail.prototype={get_intPOID:function(){return this._intPOID},set_intPOID:function(n){this._intPOID!==n&&(this._intPOID=n)},get_ctlLines:function(){return this._ctlLines},set_ctlLines:function(n){this._ctlLines!==n&&(this._ctlLines=n)},get_ctlMainInfo:function(){return this._ctlMainInfo},set_ctlMainInfo:function(n){this._ctlMainInfo!==n&&(this._ctlMainInfo=n)},get_pnlStatus:function(){return this._pnlStatus},set_pnlStatus:function(n){this._pnlStatus!==n&&(this._pnlStatus=n)},get_lblStatus:function(){return this._lblStatus},set_lblStatus:function(n){this._lblStatus!==n&&(this._lblStatus=n)},get_IsGlobalLogin:function(){return this._IsGlobalLogin},set_IsGlobalLogin:function(n){this._IsGlobalLogin!==n&&(this._IsGlobalLogin=n)},initialize:function(){Rebound.GlobalTrader.Site.Pages.Warehouse.ReceivePODetail.callBaseMethod(this,"initialize")},goInit:function(){this._ctlMainInfo&&this._ctlMainInfo.addGetDataComplete(Function.createDelegate(this,this.ctlMainInfo_GetDataComplete));this._ctlLines&&this._ctlLines.addSaveEditComplete(Function.createDelegate(this,this.ctlLines_SaveEditComplete));Rebound.GlobalTrader.Site.Pages.Warehouse.ReceivePODetail.callBaseMethod(this,"goInit")},dispose:function(){this.isDisposed||(this._ctlLines&&this._ctlLines.dispose(),this._ctlMainInfo&&this._ctlMainInfo.dispose(),this._intPOID=null,this._ctlLines=null,this._ctlMainInfo=null,this._pnlStatus=null,this._lblStatus=null,this._IsGlobalLogin=null,Rebound.GlobalTrader.Site.Pages.Warehouse.ReceivePODetail.callBaseMethod(this,"dispose"))},ctlMainInfo_GetDataComplete:function(){this._ctlLines._strSupplierName=this._ctlMainInfo.getFieldValue("hidSupplierName");this._ctlLines._intSupplierRating=this._ctlMainInfo.getFieldValue("hidCompanyPORating");$R_FN.setInnerHTML(this._lblStatus,this._ctlMainInfo.getFieldValue("hidStatus"));var n=Number.parseInvariant(this._ctlMainInfo.getFieldValue("hidStatusNo").toString());this._ctlLines._blnDisableAllButtons=n==$R_ENUM$PurchaseOrderStatus.Received||n==$R_ENUM$PurchaseOrderStatus.Complete;this._ctlLines._intPONumber=this._ctlMainInfo._intPONumber;this._ctlLines._blnIPOApproved=Boolean.parse(this._ctlMainInfo.getFieldValue("hidIsIPOApprove"));this._ctlLines._IsRelatedToIPO=Boolean.parse(this._ctlMainInfo.getFieldValue("hidIsIPO"));this._ctlLines.enableEditButtons(!0);this._ctlLines._intGlobalClientNo=this._IsGlobalLogin==!0?this._ctlMainInfo.getFieldValue("hidGlobalClientNo"):null;this._ctlMainInfo._IsGlobalLogin=this._IsGlobalLogin},ctlLines_SaveEditComplete:function(){this._ctlMainInfo.getData()}};Rebound.GlobalTrader.Site.Pages.Warehouse.ReceivePODetail.registerClass("Rebound.GlobalTrader.Site.Pages.Warehouse.ReceivePODetail",Rebound.GlobalTrader.Site.Pages.Content,Sys.IDisposable);