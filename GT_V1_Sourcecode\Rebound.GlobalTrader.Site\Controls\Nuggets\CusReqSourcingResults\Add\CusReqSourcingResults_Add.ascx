<%--
Marker     Changed by      Date         Remarks
[001]      Vinay           21/11/2012   Please make Rosh as a compulsory field on the following:- Requirements,Quotes,PO,SO
--%>
<%@ Control Language="C#" CodeBehind="CusReqSourcingResults_Add.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Forms.CusReqSourcingResults_Add" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<%@ Register TagPrefix="cc1" Namespace="Rebound.GlobalTrader.Site" Assembly="Rebound.GlobalTrader.Site" %>
<ReboundUI_Form:DesignBase ID="ctlDB" runat="server" ShowRequiredFieldsExplanation="true" ShowQuickHelp="false">
	<Links>
		<ReboundUI:IconButton ID="ibtnSave" runat="server" IconGroup="Nugget" IconTitleResource="Save" IconCSSType="Save" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
		<ReboundUI:IconButton ID="ibtnCancel" runat="server" IconGroup="Nugget" IconTitleResource="Cancel" IconCSSType="Cancel" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
	</Links>

	<Explanation><%=Functions.GetGlobalResource("FormExplanations", "CusReqSourcingResults_Add")%></Explanation>

	<Content>
		<ReboundUI_Table:Form id="frm" runat="server">
			<ReboundUI_Form:FormField id="ctlSupplier" runat="server" FieldID="cmbSupplier" ResourceTitle="Supplier" IsRequiredField="true">
				<Field><ReboundUI:Combo ID="cmbSupplier" runat="server" AutoSearchControlAssembly="Rebound.GlobalTrader.Site" AutoSearchControlType="AllCompanies" PanelWidth="250" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlPartNo" runat="server" FieldID="txtPartNo" ResourceTitle="PartNo" IsRequiredField="true">
				<Field>
					<ReboundUI:ReboundTextBox ID="txtPartNo" runat="server" Width="250" UppercaseOnly="true" />
					<ReboundAutoSearch:PartSearch ID="autPartNo" runat="server" RelatedTextBoxID="txtPartNo" ResultsHeight="150" Width="250" ResultsActionType="RaiseEvent" TriggerByButton="true" />
				</Field>
			</ReboundUI_Form:FormField>
           <%--[001] code start--%>
			<ReboundUI_Form:FormField id="ctlROHS" runat="server" FieldID="ddlROHS" ResourceTitle="ROHS"  >
				<Field><ReboundDropDown:ROHSStatus ID="ddlROHS" runat="server"  NoValue_Value="" InitialValue=""  /></Field>
			</ReboundUI_Form:FormField>
           <%--[001] code end--%>
			<ReboundUI_Form:FormField id="ctlManufacturer" runat="server" FieldID="cmbManufacturer" ResourceTitle="Manufacturer">
				<Field><ReboundUI:Combo ID="cmbManufacturer" runat="server" AutoSearchControlAssembly="Rebound.GlobalTrader.Site" AutoSearchControlType="Manufacturers" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlDateCode" runat="server" FieldID="txtDateCode" ResourceTitle="DateCode">
				<Field><ReboundUI:ReboundTextBox ID="txtDateCode" runat="server" Width="60" MaxLength="5" UppercaseOnly="true" /></Field>			
			</ReboundUI_Form:FormField>

            <ReboundUI_Form:FormField id="ctlProduct" runat="server" FieldID="cmbProducts" ResourceTitle="Product" >
				<Field><ReboundUI:Combo ID="cmbProducts" runat="server" AutoSearchControlAssembly="Rebound.GlobalTrader.Site" PanelWidth="450" PanelHeight="250"  AutoSearchControlType="Products" /></Field>
			</ReboundUI_Form:FormField>

			<%--<ReboundUI_Form:FormField id="ctlProduct" runat="server" FieldID="ddlProduct" ResourceTitle="Product">
				<Field><ReboundDropDown:Product ID="ddlProduct" runat="server" /></Field>
			</ReboundUI_Form:FormField>--%>

			<%--<ReboundUI_Form:FormField id="ctlPackage" runat="server" FieldID="cmbPackage" ResourceTitle="Package">
				<Field>
                    <ReboundDropDown:Package ID="ddlPackage" runat="server" />
				</Field>
			</ReboundUI_Form:FormField>--%>
            <ReboundUI_Form:FormField id="ctlPackage" runat="server" FieldID="cmbPackage" ResourceTitle="Package">
				<Field>
                    <ReboundUI:Combo ID="cmbPackage" runat="server" AutoSearchControlAssembly="Rebound.GlobalTrader.Site"   AutoSearchControlType="Packages" />
				</Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlQuantity" runat="server" FieldID="txtQuantity" ResourceTitle="Quantity" IsRequiredField="true">
				<Field><ReboundUI:ReboundTextBox ID="txtQuantity" runat="server" TextBoxMode="Numeric" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlPrice" runat="server" FieldID="txtPrice" ResourceTitle="Price" IsRequiredField="true">
				<Field><ReboundUI:ReboundTextBox ID="txtPrice" runat="server" TextBoxMode="currency" FormatDecimalPlaces="true" Width="100" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlBuyCurrency" runat="server" FieldID="ddlCurrency" ResourceTitle="Currency" DisplayRequiredFieldMarkerOnly="true">
				<Field><ReboundDropDown:BuyCurrency ID="ddlCurrency" runat="server" /></Field>
			</ReboundUI_Form:FormField>

             <ReboundUI_Form:FormField id="ctlCurrency" runat="server" FieldID="ddlGlobalCurrency" ResourceTitle="Currency" DisplayRequiredFieldMarkerOnly="true" >
				<Field><ReboundDropDown:BuyCurrencyByGlobalNo ID="ddlGlobalCurrency" runat="server" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlOfferStatus" runat="server" FieldID="ddlOfferStatus" ResourceTitle="OfferStatus">
				<Field><ReboundDropDown:OfferStatus ID="ddlOfferStatus" runat="server" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlNotes" runat="server" FieldID="txtNotes" ResourceTitle="Notes">
				<Field><ReboundUI:ReboundTextBox ID="txtNotes" runat="server" Width="400" MaxLength="128" /></Field>			
			</ReboundUI_Form:FormField>

               <ReboundUI_Form:FormField id="ctlMSL" runat="server" FieldID="ddlMsl" ResourceTitle="MSL" IsRequiredField="true">
				<Field><ReboundDropDown:MSLLevelNo ID="ddlMsl" runat="server" /></Field>
			</ReboundUI_Form:FormField>

		</ReboundUI_Table:Form>
	</Content>
</ReboundUI_Form:DesignBase>
