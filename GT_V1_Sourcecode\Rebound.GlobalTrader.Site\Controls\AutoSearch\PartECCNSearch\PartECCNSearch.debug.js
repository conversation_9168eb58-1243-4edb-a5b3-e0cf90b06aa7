///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.AutoSearch");

Rebound.GlobalTrader.Site.Controls.AutoSearch.PartECCNSearch = function(element){
    Rebound.GlobalTrader.Site.Controls.AutoSearch.PartECCNSearch.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.AutoSearch.PartECCNSearch.prototype = {

get_ShowInactive: function() { return this._ShowInactive; }, 	set_ShowInactive: function(value) { if (this._ShowInactive !== value)  this._ShowInactive = value; }, 

	initialize: function(){
        Rebound.GlobalTrader.Site.Controls.AutoSearch.PartECCNSearch.callBaseMethod(this, "initialize");
		this.addDataReturnedEvent(Function.createDelegate(this, this.dataReturned));
    	//this.addSetupParametersEvent(Function.createDelegate(this, this.setupParameters));
		this.addSelectionMadeEvent(Function.createDelegate(this, this.selectionMade));
	
        this.setupDataObject("PartECCNSearch");
	},
	
	dispose: function(){
		if (this.isDisposed) return;
		this._ShowInactive=null;
        Rebound.GlobalTrader.Site.Controls.AutoSearch.PartECCNSearch.callBaseMethod(this, "dispose");
	},
	
//	setupParameters: function() {
//	alert("hi");
//	//this.addParameter("blnShowInactive", this._ShowInactive);
//		this.addDataParameter("blnShowInactive", this._ShowInactive);
//	},
	selectionMade: function () {
		var extar = this._varSelectedExtraData;
		var strAlt = "";
		if (typeof extar === "undefined") { }
		else {
			var ECCNObj = extar.split(':');
			var ECCNStatus = ECCNObj[0];
			var ECCNWarning = ECCNObj[1];
			if (ECCNStatus) {
				$('#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_cmbPartEccnMappedaut_ctl03').addClass('ihspartstatusdoc');
				$('#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_cmbPartEccnMappedaut_ctl03').prop("title", ECCNWarning.replace(/<br\s*\/?>/gi, ''));
				$('#ctl00_cphMain_ctlAdd_ctlDB_ctl14_ctlAdd_ctlDB_cmbPartEccnMappedaut_ctl03').addClass('ihspartstatusdoc');
				$('#ctl00_cphMain_ctlAdd_ctlDB_ctl14_ctlAdd_ctlDB_cmbPartEccnMappedaut_ctl03').prop("title", ECCNWarning.replace(/<br\s*\/?>/gi, ''));
				$('#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmAdd_ctlDB_ctlECCNCode_ctl03_aut_ctl03').addClass('ihspartstatusdoc');
				$('#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmAdd_ctlDB_ctlECCNCode_ctl03_aut_ctl03').prop("title", ECCNWarning.replace(/<br\s*\/?>/gi, ''));
				$('#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_ctlECCNCode_ctl03_aut_ctl03').addClass('ihspartstatusdoc');
				$('#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_ctlECCNCode_ctl03_aut_ctl03').prop("title", ECCNWarning.replace(/<br\s*\/?>/gi, ''));
			}
			else {
				$('#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_cmbPartEccnMappedaut_ctl03').removeClass('ihspartstatusdoc');
				$('#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_cmbPartEccnMappedaut_ctl03').prop("title", "");
				$('#ctl00_cphMain_ctlAdd_ctlDB_ctl14_ctlAdd_ctlDB_cmbPartEccnMappedaut_ctl03').removeClass('ihspartstatusdoc');
				$('#ctl00_cphMain_ctlAdd_ctlDB_ctl14_ctlAdd_ctlDB_cmbPartEccnMappedaut_ctl03').prop("title", "");
				$('#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmAdd_ctlDB_ctlECCNCode_ctl03_aut_ctl03').removeClass('ihspartstatusdoc');
				$('#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmAdd_ctlDB_ctlECCNCode_ctl03_aut_ctl03').prop("title", "");
				$('#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_ctlECCNCode_ctl03_aut_ctl03').removeClass('ihspartstatusdoc');
				$('#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_ctlECCNCode_ctl03_aut_ctl03').prop("title", "");
			}
		}

	},
	dataReturned: function () {
		if (!this._result) return;
		if (this._result.TotalRecords > 0) {
			for (var i = 0, l = this._result.Results.length; i < l; i++) {
				var res = this._result.Results[i];
				var strHTML = "";
				if (this._enmResultsActionType == $R_ENUM$AutoSearchResultsActionType.Navigate) {
                    strHTML = $RGT_nubButton_Package(res.ID, res.Name);
				} else {
					strHTML = $R_FN.setCleanTextValue(res.Name);
				}
				this.addResultItem(strHTML, $R_FN.setCleanTextValue(res.Name), res.ID, res.ECCNStatus + ":" + $R_FN.setCleanTextValue(res.ECCNWarning));
				strHTML = null;
				res = null;
			}
		}
	}
};
Rebound.GlobalTrader.Site.Controls.AutoSearch.PartECCNSearch.registerClass("Rebound.GlobalTrader.Site.Controls.AutoSearch.PartECCNSearch", Rebound.GlobalTrader.Site.Controls.AutoSearch.Base, Sys.IDisposable);
