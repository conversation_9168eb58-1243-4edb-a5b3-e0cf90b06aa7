Type.registerNamespace("Rebound.GlobalTrader.Site.Controls");Rebound.GlobalTrader.Site.Controls.TopMenu=function(n){Rebound.GlobalTrader.Site.Controls.TopMenu.initializeBase(this,[n]);this._aryListItemIDs=[];this._aryHoverPanelClientIDs=[];this._intTimeout=-1;this._intCurrentRollover=-1;this._blnRolloverShown=!1};Rebound.GlobalTrader.Site.Controls.TopMenu.prototype={get_aryListItemIDs:function(){return this._aryListItemIDs},set_aryListItemIDs:function(n){this._aryListItemIDs!==n&&(this._aryListItemIDs=n)},get_pnlRollovers:function(){return this._pnlRollovers},set_pnlRollovers:function(n){this._pnlRollovers!==n&&(this._pnlRollovers=n)},get_aryHoverPanelClientIDs:function(){return this._aryHoverPanelClientIDs},set_aryHoverPanelClientIDs:function(n){this._aryHoverPanelClientIDs!==n&&(this._aryHoverPanelClientIDs=n)},initialize:function(){var n,u,t,i,r;for(Rebound.GlobalTrader.Site.Controls.TopMenu.callBaseMethod(this,"initialize"),n=0,u=this._aryListItemIDs.length;n<u;n++)t=this._aryHoverPanelClientIDs[n]==null?null:$get(this._aryHoverPanelClientIDs[n]),i=this._aryListItemIDs[n]==null?null:$get(this._aryListItemIDs[n]),i&&(i.setAttribute("bui_topMenuID",n),$addHandler(i,"mouseover",Function.createDelegate(this,this.rolloverElement)),$addHandler(i,"mouseout",Function.createDelegate(this,this.rolloutElement)),t&&($addHandler(t,"mouseover",Function.createDelegate(this,this.rolloverElement)),$addHandler(t,"mouseout",Function.createDelegate(this,this.rolloutElement)),r=$get(t.id+"_hyp"),r&&$addHandler(i,"mouseover",Function.createDelegate(this,this.rolloverElement)),r=null)),t=null},dispose:function(){var n,u,t,i,r;if(!this.isDisposed){for(this.get_element()&&$clearHandlers(this.get_element()),this.clearTimeout(),n=0,u=this._aryListItemIDs.length;n<u;n++)t=this._aryHoverPanelClientIDs[n]==null?null:$get(this._aryHoverPanelClientIDs[n]),i=this._aryListItemIDs[n]==null?null:$get(this._aryListItemIDs[n]),i&&$clearHandlers(i),t&&($clearHandlers(t),r=$get(t.id+"_hyp"),r&&$clearHandlers(r),r=null),t=null,i=null;this._aryHoverPanelClientIDs=null;this._aryListItemIDs=null;this._pnlRollovers=null;this._intTimeout=null;this._intCurrentRollover=null;this._blnRolloverShown=null;Rebound.GlobalTrader.Site.Controls.TopMenu.callBaseMethod(this,"dispose");this.isDisposed=!0}},rolloverElement:function(n){var i,f,t,c,e,o,h,s;if((this.clearTimeout(),i=$R_FN.findParentElementOfType(n.target,"LI"),i)&&(Sys.UI.DomElement.addCssClass(i,"hover"),f=Number.parseInvariant(i.getAttribute("bui_topMenuID")),!this._blnRolloverShown||f!=this._intCurrentRollover)){for(this._blnRolloverShown=!0,this._intCurrentRollover=f,t=0,c=this._aryHoverPanelClientIDs.length;t<c;t++)e=this._aryHoverPanelClientIDs[t]==null?null:$get(this._aryHoverPanelClientIDs[t]),e&&$R_FN.showElement(e,t==this._intCurrentRollover),o=this._aryListItemIDs[t]==null?null:$get(this._aryListItemIDs[t]),o&&Sys.UI.DomElement.removeCssClass(o,"hover"),e=null,o=null;if(this._aryHoverPanelClientIDs[this._intCurrentRollover]){$R_FN.showElement(this._pnlRollovers,!0);$R_FN.setElementOpacity(this._pnlRollovers,100);this._pnlRollovers.style.width="";var l=Sys.UI.DomElement.getBounds(document.body),r=Sys.UI.DomElement.getBounds(i),u=Sys.UI.DomElement.getBounds(this._pnlRollovers);u.width<r.width&&(u.width=r.width,this._pnlRollovers.style.width=u.width+"px");h=29;s=r.x;u.width>r.width&&(s-=Math.round((u.width-r.width)/2));Sys.UI.DomElement.setLocation(this._pnlRollovers,s,h);l=null;r=null;u=null;s=null;h=null}else $R_FN.showElement(this._pnlRollovers,!1);i=null;f=null}},rolloutElement:function(){this._intTimeout=setTimeout(Function.createDelegate(this,this.hideHoverPanel),100)},hideHoverPanel:function(){var n=this._aryListItemIDs[this._intCurrentRollover]==null?null:$get(this._aryListItemIDs[this._intCurrentRollover]);n&&Sys.UI.DomElement.removeCssClass(n,"hover");n=null;$R_FN.showElement(this._pnlRollovers,!1);this._intCurrentRollover=-1;this._blnRolloverShown=!1},cancelHideHoverPanel:function(){clearTimeout(this._intTimeout)},clearTimeout:function(){this._intTimeout!=-1&&clearTimeout(this._intTimeout)}};Rebound.GlobalTrader.Site.Controls.TopMenu.registerClass("Rebound.GlobalTrader.Site.Controls.TopMenu",Sys.UI.Control,Sys.IDisposable);