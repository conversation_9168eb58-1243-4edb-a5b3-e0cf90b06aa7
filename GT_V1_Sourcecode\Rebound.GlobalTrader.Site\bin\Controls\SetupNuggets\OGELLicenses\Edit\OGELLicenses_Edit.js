Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.OGELLicenses_Edit=function(n){Rebound.GlobalTrader.Site.Controls.Forms.OGELLicenses_Edit.initializeBase(this,[n]);this._intItemID=0};Rebound.GlobalTrader.Site.Controls.Forms.OGELLicenses_Edit.prototype={initialize:function(){Rebound.GlobalTrader.Site.Controls.Forms.OGELLicenses_Edit.callBaseMethod(this,"initialize");this.addShown(Function.createDelegate(this,this.formShown))},dispose:function(){this.isDisposed||(this._intItemID=null,Rebound.GlobalTrader.Site.Controls.Forms.OGELLicenses_Edit.callBaseMethod(this,"dispose"))},formShown:function(){this._blnFirstTimeShown&&(this.addSave(Function.createDelegate(this,this.saveClicked)),this._strPathToData="controls/SetupNuggets/OGELLicenses",this._strDataObject="OGELLicenses")},clearNewItemValues:function(){this.setFormFieldsToDefaults()},saveClicked:function(){this.resetFormFields();this.validateForm()&&this.saveEdit()},validateForm:function(){return this.autoValidateFields()},saveEdit:function(){var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData(this._strPathToData);n.set_DataObject(this._strDataObject);n.set_DataAction("SaveEdit");n.addParameter("ID",this._intItemID);n.addParameter("OgelNumber",this.getFieldValue("ctlOgelNumber"));n.addParameter("Description",this.getFieldValue("ctlDescription"));n.addParameter("InActive",this.getFieldValue("ctlInActive"));n.addDataOK(Function.createDelegate(this,this.saveEditOK));n.addError(Function.createDelegate(this,this.saveEditError));n.addTimeout(Function.createDelegate(this,this.saveEditError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},saveEditError:function(n){this._strErrorMessage=n._errorMessage;this.onSaveError()},saveEditOK:function(n){if(n._result.Result==-1){this.showError(!0,"'OGEL Number' is already exists.");return}this.onSaveComplete()}};Rebound.GlobalTrader.Site.Controls.Forms.OGELLicenses_Edit.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.OGELLicenses_Edit",Rebound.GlobalTrader.Site.Controls.Forms.Base,Sys.IDisposable);