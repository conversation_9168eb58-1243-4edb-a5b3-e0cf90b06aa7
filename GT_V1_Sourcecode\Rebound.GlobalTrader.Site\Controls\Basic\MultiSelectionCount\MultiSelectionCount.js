Type.registerNamespace("Rebound.GlobalTrader.Site.Controls");Rebound.GlobalTrader.Site.Controls.MultiSelectionCount=function(n){Rebound.GlobalTrader.Site.Controls.MultiSelectionCount.initializeBase(this,[n]);this._intCount=0;this._tbl=null};Rebound.GlobalTrader.Site.Controls.MultiSelectionCount.prototype={get_lblNumber:function(){return this._lblNumber},set_lblNumber:function(n){this._lblNumber!==n&&(this._lblNumber=n)},get_hypSelectAll:function(){return this._hypSelectAll},set_hypSelectAll:function(n){this._hypSelectAll!==n&&(this._hypSelectAll=n)},get_hypClearAll:function(){return this._hypClearAll},set_hypClearAll:function(n){this._hypClearAll!==n&&(this._hypClearAll=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.MultiSelectionCount.callBaseMethod(this,"initialize");this.updateCount(0)},dispose:function(){this.isDisposed||(this._element&&$clearHandlers(this._element),this._hypSelectAll&&$clearHandlers(this._hypSelectAll),this._hypClearAll&&$clearHandlers(this._hypClearAll),this._tbl&&this._tbl.dispose(),this._lblNumber=null,this._hypSelectAll=null,this._hypClearAll=null,this._tbl=null,Rebound.GlobalTrader.Site.Controls.MultiSelectionCount.callBaseMethod(this,"dispose"),this.isDisposed=!0)},updateCount:function(n){$R_FN.setInnerHTML(this._lblNumber,n);$R_FN.showElement(this._hypSelectAll,n<1);$R_FN.showElement(this._hypClearAll,n>0)},registerTable:function(n){this._tbl&&eval(String.format("MultiSelectionCount_AlreadyHasTable_ID_{0}()",this._element.id));this._tbl=n;this._tbl.addMultipleSelectionChanged(Function.createDelegate(this,this.tbl_MultipleSelectionIndexChanged));$addHandler(this._hypSelectAll,"click",Function.createDelegate(this,this.selectAll));$addHandler(this._hypClearAll,"click",Function.createDelegate(this,this.clearAll))},changeTableRegistration:function(n){this.clearTableRegistration();this.registerTable(n)},clearTableRegistration:function(){this._tbl&&(this._tbl.removeMultipleSelectionChanged(Function.createDelegate(this,this.tbl_MultipleSelectionIndexChanged)),this._tbl=null,$clearHandlers(this._hypSelectAll),$clearHandlers(this._hypClearAll))},tbl_MultipleSelectionIndexChanged:function(){this.updateCount(this._tbl._intCountSelected)},selectAll:function(){this._tbl.selectAllRows(!0)},clearAll:function(){this._tbl.clearSelection(!0)},show:function(n){$R_FN.showElement(this._element,n)},resetCount:function(){this.updateCount(0)}};Rebound.GlobalTrader.Site.Controls.MultiSelectionCount.registerClass("Rebound.GlobalTrader.Site.Controls.MultiSelectionCount",Sys.UI.Control,Sys.IDisposable);