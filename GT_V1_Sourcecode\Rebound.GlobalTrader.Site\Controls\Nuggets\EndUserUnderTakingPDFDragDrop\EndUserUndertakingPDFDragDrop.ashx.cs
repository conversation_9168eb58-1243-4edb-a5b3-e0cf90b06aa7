﻿/*
Marker     changed by      date         Remarks
[001]      Vinay           07/05/2012   This need to upload pdf document for different section
[002]      Vinay           12/10/2012   Upload PDF document for invoices
*/
using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;
using System.IO;
using System.Drawing;
using System.Drawing.Imaging;
//[001] add namespace for Azure Blob storage
using Microsoft.Azure;
using Microsoft.Azure.Storage;
using Microsoft.Azure.Storage.Blob;
using Microsoft.Azure.KeyVault;
using Microsoft.Azure.Storage.Shared;
using Microsoft.Azure.Storage.Auth;
using Microsoft.Azure.Storage.Auth.Protocol;
using Microsoft.Azure.Storage.RetryPolicies;
using Microsoft.Azure.KeyVault.Core;
using System.Net;
using System.Text;
//[001]
using System.Configuration;
//[002]


namespace Rebound.GlobalTrader.Site.Controls.Nuggets.Data
{
    [WebService(Namespace = "http://tempuri.org/")]
    [WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    public class EndUserUndertakingPDFDragDrop : Rebound.GlobalTrader.Site.Data.Base
    {
        public string Section { get; private set; }

        public override void ProcessRequest(HttpContext context)
        {

            if (base.init(context))
            {
                switch (Action)
                {
                    case "GetData": GetData(context); break;
                    case "AddNew": AddNew(); break;
                    case "Delete": Delete(context); break;
                    case "MaxPDFDoc": MaxPDFDoc(); break;
                    case "GetPDFAccessURL":GetPDFAccessURL();break;




                    default: WriteErrorActionNotFound(); break;
                }
            }
        }

        /// <summary>
        /// Set maximum pdf document upload value
        /// </summary>
        private void MaxPDFDoc()
        {
            try
            {
                JsonObject jsn = new JsonObject();
                //jsn.AddVariable("MaxPDFDocument", SettingsManager.GetSetting_Int(SettingItem.List.MaxPDFDocuments));
                jsn.AddVariable("MaxPDFDocument", 1);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }
        private void GetPDFAccessURL()
        {
            try
            {
                JsonObject jsn = new JsonObject();
                string sasURL = AzureBlobSA.GetSasUrl("gtdocmgmt");
               

                string bothirl = AzureBlobSA.GetSasBlobUrl("gtdocmgmt", GetFormValue_String("filename"), sasURL, GetFormValue_String("section").ToUpper());
                jsn.AddVariable("bothirl", bothirl);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }

       
        private void GetData(HttpContext context)
        {
            try
            {

                this.Section = GetFormValue_String("section").Split('_')[0];
                //[003]
                //string PDFPath = string.Format(Convert.ToString(System.Configuration.ConfigurationManager.AppSettings["PDFDocumentVirtualURL"]) + "{0}\\", this.Section);
                string PDFPath = string.Format(Convert.ToString(System.Configuration.ConfigurationManager.AppSettings["PDFDocumentPhysicalAzureURL"]) + "{0}/", this.Section.ToUpper());
                //[003]

                List<PDFDocument> lstPDFDocument = null;
                switch (this.Section.ToUpper())
                {
                    case "EUU":
                        lstPDFDocument = BLL.SalesOrder.GetPDFListForEndUserUndertaking(ID, "EUUPDF");
                        break;
                    default:
                        WriteError(Functions.GetGlobalResource("NotFound", "PDFDocument"));
                        break;
                }

                JsonObject jsn = new JsonObject();
                JsonObject jsnItems = new JsonObject(true);
                if (lstPDFDocument != null)
                {
                    foreach (BLL.PDFDocument pdfDoc in lstPDFDocument)
                    {
                       

                        JsonObject jsnItem = new JsonObject();
                        jsnItem.AddVariable("ID", pdfDoc.PDFDocumentId);
                        jsnItem.AddVariable("Date", Functions.FormatDate(pdfDoc.DLUP, false, true));
                        jsnItem.AddVariable("By", pdfDoc.UpdatedByName);
                        jsnItem.AddVariable("Caption", Functions.ReplaceLineBreaks(pdfDoc.Caption));
                        jsnItem.AddVariable("FilePath", PDFPath + pdfDoc.FileName);
                        jsnItem.AddVariable("FileName", pdfDoc.FileName);
                        jsnItem.AddVariable("Section", this.Section);
                        jsnItem.AddVariable("CanDeleteDocument", pdfDoc.AuthorisedBy.Equals(null) ? true : false);
                        jsnItems.AddVariable(jsnItem);
                        jsnItem.Dispose();
                        jsnItem = null;
                        //sasURL = null;
                    }
                }


                jsn.AddVariable("Items", jsnItems);
                jsn.AddVariable("IconPath", "app_themes/original/images/IconButton/pdficon.jpg");
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;

            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }

        /// <summary>
        /// Add new pdf document
        /// </summary>
        private void AddNew()
        {
            int intDocumentID = -1;
            string caption = GetFormValue_String("Caption");
            string tempFile = GetFormValue_String("TempFile");
            bool result = true;

            try
            {
                this.Section = GetFormValue_String("Section").Split('_')[0];
                switch (this.Section.ToUpper())
                {
                    case "EUU":
                        intDocumentID = BLL.SalesOrder.InsertEUUPDF(ID, caption, tempFile, LoginID, "EUUPDF");
                        break;
                    default:
                        result = false;
                        break;
                }

                JsonObject jsn = new JsonObject();
                jsn.AddVariable("Result", result);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;

            }
            catch (Exception e)
            {
                WriteError(e);
            }

        }

        /// <summary>
        /// Delete pdf document
        /// </summary>
        /// <param name="context"></param>
        //private void Delete(HttpContext context)
        //{
        //    try
        //    {
        //        if (!string.IsNullOrEmpty(GetFormValue_String("pdffilename")))
        //        {
        //            bool result = false;
        //            this.Section = GetFormValue_String("Section").Split('_')[0];
        //            switch (this.Section.ToUpper())
        //            {
        //                case "SO":
        //                    result = BLL.SalesOrder.DeleteSalesOrderPDF(ID);
        //                    break;
        //                case "PO":
        //                    result = BLL.PurchaseOrder.DeletePurchaseOrderPDF(ID);
        //                    break;
        //                case "GI":
        //                    result = BLL.GoodsIn.DeleteGoodsInPDF(ID);
        //                    break;
        //                case "STK":
        //                    result = BLL.Stock.DeleteStockPDF(ID);
        //                    break;
        //                case "CRMA":
        //                    result = BLL.CustomerRma.DeleteCustomerRMAPDF(ID);
        //                    break;
        //                case "SRMA":
        //                    result = BLL.SupplierRma.DeleteSupplierRMAPDF(ID);
        //                    break;
        //                case "COMPANY":
        //                    result = BLL.Company.DeleteCompanyPDF(ID);
        //                    break;
        //                //[002] code start
        //                case "INVOICE":
        //                    result = BLL.Invoice.DeleteInvoicePDF(ID);
        //                    break;
        //                //[002] code end
        //                //[003] code start
        //                case "MFG":
        //                    result = BLL.Manufacturer.DeleteManufacturerPDF(ID);
        //                    break;
        //                //[003] code end
        //                default:
        //                    result = false;
        //                    break;
        //            }
        //            File.Delete(FileUploadManager.GetPDFUploadFilePath(this.Section) + GetFormValue_String("pdffilename"));
        //            JsonObject jsn = new JsonObject();
        //            jsn.AddVariable("Result", result);
        //            OutputResult(jsn);
        //            jsn.Dispose(); jsn = null;

        //        }
        //    }
        //    catch (Exception e)
        //    {
        //        WriteError(e);
        //    }
        //}
        private void Delete(HttpContext context)
        {
            try
            {
                if (!string.IsNullOrEmpty(GetFormValue_String("pdffilename")))
                {
                    bool result = false;
                    this.Section = GetFormValue_String("Section").Split('_')[0];
                    switch (this.Section.ToUpper())
                    {
                        case "EUU":
                            result = BLL.SalesOrder.DeleteEndUserUndertakingPDF(ID);
                            break;
                        default:
                            result = false;
                            break;
                    }
                    string accountname = ConfigurationManager.AppSettings.Get("StorageName");
                    string accesskey = ConfigurationManager.AppSettings.Get("StorageKey1");
                    StorageCredentials creden = new StorageCredentials(accountname, accesskey);
                    CloudStorageAccount acc = new CloudStorageAccount(creden, useHttps: true);
                    CloudBlobClient client = acc.CreateCloudBlobClient();
                    CloudBlobContainer cont = client.GetContainerReference("gtdocmgmt");
                    if (cont.Exists())
                    {
                        CloudBlobDirectory directory = cont.GetDirectoryReference(this.Section.ToUpper());
                        CloudBlockBlob cblob = directory.GetBlockBlobReference(GetFormValue_String("pdffilename"));
                        if (cblob.Exists())
                        {
                            cblob.DeleteIfExists();
                        }
                    }
                    //[003]

                    JsonObject jsn = new JsonObject();
                    jsn.AddVariable("Result", result);
                    OutputResult(jsn);
                    jsn.Dispose(); jsn = null;

                }
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }


    }
}
