﻿///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP-2727 10.01.2024:
////-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");

Rebound.GlobalTrader.Site.Controls.DropDowns.MailGroups = function (element) {
	Rebound.GlobalTrader.Site.Controls.DropDowns.MailGroups.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.DropDowns.MailGroups.prototype = {

	get_blnIncludeNewSystemDocuments: function () { return this._blnIncludeNewSystemDocuments; }, set_blnIncludeNewSystemDocuments: function (value) { if (this._blnIncludeNewSystemDocuments !== value) this._blnIncludeNewSystemDocuments = value; },

	initialize: function () {
		Rebound.GlobalTrader.Site.Controls.DropDowns.MailGroups.callBaseMethod(this, "initialize");
		this.addSetupDataCall(Function.createDelegate(this, this.setupDataCall));
		this.addGetDataOK(Function.createDelegate(this, this.dataCallOK));
	},

	dispose: function () {
		if (this.isDisposed) return;
		this._blnIncludeNewSystemDocuments = null;
		Rebound.GlobalTrader.Site.Controls.DropDowns.MailGroups.callBaseMethod(this, "dispose");
	},

	setupDataCall: function () {
		this._objData.set_PathToData("controls/DropDowns/MailGroups");
		this._objData.set_DataObject("MailGroups");
		this._objData.set_DataAction("GetData");
		this._objData.addParameter("IncludeNewSystemDocuments", this._blnIncludeNewSystemDocuments);
	},

	dataCallOK: function () {
		var result = this._objData._result;
		if (result.Types) {
			var obj = result.Types;
			for (var i = 0; i < obj.length; i++) {
				var row = obj[i];
				if (this._blnIncludeNewSystemDocuments) {
					this.addOption(row.Name, row.ID, row.Type);
				} else {
					this.addOption(row.Name, row.ID);
				}
				row = null;
			}
			obj = null;
		}
	}

};

Rebound.GlobalTrader.Site.Controls.DropDowns.MailGroups.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.MailGroups", Rebound.GlobalTrader.Site.Controls.DropDowns.Base);
