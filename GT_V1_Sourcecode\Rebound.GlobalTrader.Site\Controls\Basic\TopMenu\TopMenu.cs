using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using System.Collections;
using System.Resources;

namespace Rebound.GlobalTrader.Site.Controls {
	[DefaultProperty("")]
	[ToolboxData("<{0}:TopMenu runat=server></{0}:TopMenu>")]
	public class TopMenu : Panel, INamingContainer, IScriptControl {

		#region Locals

		private int _intCountItems = 0;
		protected PlaceHolder _plhItems;
		protected HtmlControl _ulTopMenu;
		protected Panel _pnlRolloversInner;
		protected Panel _pnlRollovers;
		protected List<string> _lstHoverPanelClientIDs = new List<string>();
		protected List<TopMenuLink[]> _lstHoverLinks = new List<TopMenuLink[]>();

		#endregion

		#region Properties

		/// <summary>
		/// Which top menu item is selected
		/// </summary>
		private int _intSelectedSiteSectionID;
		public int SelectedSiteSectionID {
			get { return _intSelectedSiteSectionID; }
			set { _intSelectedSiteSectionID = value; }
		}

		/// <summary>
		/// Array of items in the menu
		/// </summary>
		private List<string> _lstItemIDs = new List<string>();
		public List<string> ItemIDs {
			get { return _lstItemIDs; }
			set { _lstItemIDs = value; }
		}
		//private List<TopMenuHover> _lstHoverItems = new List<TopMenuHover>();
		//public List<TopMenuHover> HoverItems {
		//    get { return _lstHoverItems; }
		//    set { _lstHoverItems = value; }
		//}

		#endregion

		#region Overrides

		protected override void OnInit(EventArgs e) {
			((Pages.Base)Page).AddCSSFile("TopMenu.css");
			base.OnInit(e);
		}

		/// <summary>
		/// create controls
		/// </summary>
		protected override void CreateChildControls() {
			this.CssClass = "topMenu";
			_ulTopMenu = ControlBuilders.CreateHtmlGenericControlInsideParent(this, "ul", "topMenu");
			_plhItems = ControlBuilders.CreatePlaceHolderInsideParent(_ulTopMenu);
			_pnlRollovers = ControlBuilders.CreatePanelInsideParent(this, "topMenuRollovers invisible");
			_pnlRolloversInner = ControlBuilders.CreatePanelInsideParent(_pnlRollovers, "topMenuRolloversContent");
			_pnlRolloversInner.ID = "pnlRollovers";
			ControlBuilders.CreateLiteralInsideParent(ControlBuilders.CreatePanelInsideParent(_pnlRollovers, "curveBL"), "&nbsp;");
			ControlBuilders.CreateLiteralInsideParent(ControlBuilders.CreatePanelInsideParent(_pnlRollovers, "curveBR"), "&nbsp;");
			base.CreateChildControls();
		}

		/// <summary>
		/// OnPreRender
		/// </summary>
		/// <param name="e"></param>
		protected override void OnPreRender(EventArgs e) {
			AddHovers();
			if (!this.DesignMode) {
				ScriptManager sm = ScriptManager.GetCurrent(Page);
				if (sm == null) throw new HttpException("A ScriptManager control must exist on the current page.");
				sm.RegisterScriptControl(this);
				sm = null;
			}
			base.OnPreRender(e);
		}

		protected override void Render(HtmlTextWriter writer) {
			if (!this.DesignMode) ScriptManager.GetCurrent(Page).RegisterScriptDescriptors(this);
			base.Render(writer);
		}

		#endregion

		/// <summary>
		/// Adds a new item to the menu
		/// </summary>
		public void AddItem(TopMenuLink objLink) {
			AddItem(objLink, null);
		}
		public void AddItem(TopMenuLink objLink, TopMenuLink[] aryHoverItems) {
			EnsureChildControls();
			HtmlGenericControl li = ControlBuilders.CreateHtmlGenericControl("li");
			li.ID = string.Format("li{0}", _intCountItems);
			li.Controls.Add(ControlBuilders.CreateHtmlGenericControl("div", "innerShadowL"));
			li.Controls.Add(ControlBuilders.CreateHtmlGenericControl("div", "innerShadowR"));
			HyperLink hyp = ControlBuilders.CreateHyperLinkInsideParent(li);
			hyp.ID = "hyp";
			if (objLink.ID == _intSelectedSiteSectionID) li.Attributes["class"] = "selected";
			hyp.Text = objLink.Title;
			hyp.NavigateUrl = objLink.Href;
          
			_plhItems.Controls.Add(li);
			_lstHoverLinks.Add(aryHoverItems);
			_lstItemIDs.Add(li.ClientID);
			_intCountItems += 1;
		}

		private void AddSeparator() {
			HtmlGenericControl li = ControlBuilders.CreateHtmlGenericControl("li");
			li.Attributes["class"] = "sep";
			ControlBuilders.CreateImageInsideParent(li, "", "~/images/x.gif", 5, 29);
			_plhItems.Controls.Add(li);
		}

		private void AddHovers() {
			//add hovers for each section if they exist
			for (int i = 0; i < _lstItemIDs.Count; i++) {
				bool blnHasHoverLinks = (_lstHoverLinks[i] != null) ? _lstHoverLinks[i].Length > 0 : false;
				if (blnHasHoverLinks) {
					Panel pnl = ControlBuilders.CreatePanel("topMenuRolloverLinks invisible");
					Panel pnlLastLink = new Panel();
					pnl.ID = "pnlRollover" + i;
					_pnlRolloversInner.Controls.Add(pnl);
					int intCountHoverLinks = 0;
					for (int j = 0; j < _lstHoverLinks[i].Length; j++) {
						if (!String.IsNullOrEmpty(_lstHoverLinks[i][j].Title)) {
							Panel pnlLink = ControlBuilders.CreatePanelInsideParent(pnl, "topMenuRolloverLink");
							HyperLink hyp = ControlBuilders.CreateHyperLinkInsideParent(pnlLink, "", _lstHoverLinks[i][j].Href, _lstHoverLinks[i][j].Title);

                           
                            if (_lstHoverLinks[i][j].Title == "Sales (BI)")
                                hyp.Target = "_blank";
							pnlLastLink = pnlLink;
							intCountHoverLinks += 1;
						}
					}
					if (intCountHoverLinks > 0) {
						pnlLastLink.CssClass += " topMenuRolloverLink_Last";
						_lstHoverPanelClientIDs.Add(pnl.ClientID);
					} else {
						_lstHoverPanelClientIDs.Add(null);
					}
				} else {
					_lstHoverPanelClientIDs.Add(null);
				}
			}
		}


		#region IScriptControl Members

		public IEnumerable<ScriptDescriptor> GetScriptDescriptors() {
			ScriptControlDescriptor descriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.TopMenu", this.ClientID);
			descriptor.AddProperty("aryListItemIDs", _lstItemIDs);
			descriptor.AddElementProperty("pnlRollovers", _pnlRollovers.ClientID);
			descriptor.AddProperty("aryHoverPanelClientIDs", _lstHoverPanelClientIDs);
			return new ScriptDescriptor[] { descriptor };
		}

		public IEnumerable<ScriptReference> GetScriptReferences() {
			bool blnDebug = false;
#if (DEBUG)
			blnDebug = true;
#endif
			return new ScriptReference[] { Functions.GetScriptReference(blnDebug, "Rebound.GlobalTrader.Site", "Controls.Basic.TopMenu.TopMenu", true) };
		}

		IEnumerable<ScriptReference> IScriptControl.GetScriptReferences() {
			return GetScriptReferences();
		}

		IEnumerable<ScriptDescriptor> IScriptControl.GetScriptDescriptors() {
			return GetScriptDescriptors();
		}

		#endregion
	}

	#region TopMenuLink Structure

	public struct TopMenuLink {
		public int ID;
		public string Title;
		public string Href;

		public TopMenuLink(int intID, string strTitle, string strHref) {
			ID = intID;
			Title = strTitle;
			Href = strHref;
		}

		public TopMenuLink(SiteSection objSiteSection) {
			ID = objSiteSection.ID;
			Title = Functions.GetGlobalResource("SectionTitles", objSiteSection.Name);
			Href = objSiteSection.Url;
		}
	}

	#endregion
}