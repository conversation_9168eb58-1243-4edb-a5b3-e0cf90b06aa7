﻿
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


/* 
=====================================================================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-208862]		Trung Pham			15-Jul-2024		CREATE			Refresh lytica data when selected/opened requirement after 3 days
[US-215434]		Phuc Hoang			25-Oct-2024		UPDATE			[PROD Bug] The existing Lytica Price is not shown in HUBRFQ
=====================================================================================================================================
*/

CREATE OR ALTER PROCEDURE [dbo].[usp_Get_LyticaAPIData_ByKey]
(
@partNo NVARCHAR(256),
@mfrCode NVARCHAR(100),
@mfrNo INT = 0
)
AS
BEGIN
	DECLARE @mfrName NVARCHAR(256) = NULL;

	SELECT TOP 1 @mfrName=ManufacturerName FROM tbManufacturer WHERE ManufacturerId = @mfrNo;

	IF NOT EXISTS (SELECT 1 FROM tbLyticaAPI WHERE OriginalPartSearched = @partNo AND Manufacturer = @mfrName)
	BEGIN
		SELECT 1
	END

	ELSE
	BEGIN
		SELECT 1 
		FROM tbLyticaAPI
		WHERE OriginalPartSearched = @partNo AND Manufacturer = @mfrName AND GETDATE() >= CAST(DATEADD(DAY, 3, DLUP) AS DATE)
		ORDER BY DLUP DESC
	END
END
GO


