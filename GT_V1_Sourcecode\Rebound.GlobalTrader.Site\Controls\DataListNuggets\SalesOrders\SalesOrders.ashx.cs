/* Marker     changed by      date         Remarks  
   [001]      V<PERSON><PERSON> kumar     22/11/2011  ESMS Ref:21 - Add Country search option in SO */
/* [0002]     <PERSON><PERSON><PERSON><PERSON>  14/07/2014    ESMS Ref:172 - Issue in Part Searching in whole GT 
   [003]      <PERSON><PERSON><PERSON>     17-Aug-2018  Provision to add Global Security in Sales Order
	[004]     Ravi           19-09-2023      RP-2338  AS6081 Search/Filter functionality on different pages 
 */

using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;
using System.Configuration;
using Rebound.GlobalTrader.Site.Enumerations;
using Rebound.GlobalTrader.Site.Code.Common;

namespace Rebound.GlobalTrader.Site.Controls.DataListNuggets.Data
{

    public class SalesOrders : Base
    {

        public override void ProcessRequest(HttpContext context)
        {
            base.ProcessRequest(context);
            if (Action == "ExportToCSV") ExportToCSV();
        }

        protected override void GetData()
        {

            //check view level
            ViewLevelList enmViewLevel = (ViewLevelList)GetFormValue_Int("ViewLevel");
            string AS6081 = GetFormValue_String("AS6081"); //[004]
            int? SelectedclientNo = null;
            int? SessionClientNo = SessionManager.ClientID;
            bool? blnMakeYellow = false;
            if (SessionManager.IsGSA == true && SessionManager.IsGlobalUser == false)
            {
                SelectedclientNo = GetFormValue_NullableInt("Client");
                if (SelectedclientNo != null)
                {
                    blnMakeYellow = true;
                }
                else
                {
                    blnMakeYellow = false;
                }

            }
            else
            {
                blnMakeYellow = false;
            }
            //get data
            List<SalesOrderLine> lst = SalesOrderLine.DataListNugget(
                SessionManager.ClientID
                , (enmViewLevel == ViewLevelList.Team) ? (int?)SessionManager.LoginTeamID : null
                , (enmViewLevel == ViewLevelList.Division) ? (int?)SessionManager.LoginDivisionID : null
                , (enmViewLevel == ViewLevelList.My) ? (int?)LoginID : null
                , GetFormValue_NullableInt("SortIndex")
                , GetFormValue_NullableInt("SortDir", SortColumnDirection.ASC)
                , GetFormValue_NullableInt("PageIndex", 0)
                , GetFormValue_NullableInt("PageSize", 10)
                  //[0002] start code
                  //, GetFormValue_StringForPartSearch("Part")
                  , GetFormValue_PartForLikeSearch("Part")
                 //[0002] end code
                 //, GetFormValue_StringForNameSearch("Contact")
                 , GetFormValue_StringForNameSearchDecode("Contact")
                //[001]Code Start
                , GetFormValue_NullableInt("Country")
                 //[001]Code End
                 //, GetFormValue_StringForNameSearch("CMName")
                 , GetFormValue_StringForNameSearchDecode("CMName")
                , GetFormValue_NullableInt("Salesman")
                , GetFormValue_ForLikeSearchWithSpecialChar("CustPO")
                , GetFormValue_Boolean("RecentOnly")
                , GetFormValue_Boolean("IncludeClosed")
                , GetFormValue_NullableInt("SONoLo")
                , GetFormValue_NullableInt("SONoHi")
                , GetFormValue_NullableDateTime("DateOrderedFrom")
                , GetFormValue_NullableDateTime("DateOrderedTo")
                , GetFormValue_NullableDateTime("DatePromisedFrom")
                , GetFormValue_NullableDateTime("DatePromisedTo")
                //, GetFormValue_Boolean("UnauthorisedOnly")
                , null
                , GetFormValue_NullableInt("IncludeOrderSent")
                //, GetFormValue_PartForLikeSearch("ContractNo")
                , GetFormValue_StringForLikeSearch("ContractNo")
                //[003] start
                , GetFormValue_Boolean("IsGlobalLogin")
                , GetFormValue_NullableInt("Client")
                //[003] end
                , GetFormValue_NullableInt("SOCheckedStatus")
                , GetFormValue_NullableInt("SalesOrderStatus")
                , (AS6081 == "1" ? true : (AS6081 == "2" ? false : (bool?)null)) //[004]
                , SessionManager.LoginID
            );

            JsonObject jsn = new JsonObject();
            JsonObject jsnRowsArray = new JsonObject(true);

            //check counts
            jsn.AddVariable("TotalRecords", (lst.Count > 0) ? lst[0].RowCnt : 0);

            //format json
            for (int i = 0; i < lst.Count; i++)
            {
                if (i < lst.Count)
                {
                    JsonObject jsnRow = new JsonObject();
                    jsnRow.AddVariable("ID", lst[i].SalesOrderNo);
                    jsnRow.AddVariable("No", lst[i].SalesOrderNumber);
                    jsnRow.AddVariable("Part", lst[i].Part);
                    jsnRow.AddVariable("Price", Functions.FormatCurrency(lst[i].Price, lst[i].CurrencyCode));
                    jsnRow.AddVariable("Quantity", Functions.FormatNumeric(lst[i].Quantity));
                    jsnRow.AddVariable("QuantityShipped", Functions.FormatNumeric(lst[i].QuantityShipped));
                    jsnRow.AddVariable("DateOrdered", Functions.FormatDate(lst[i].DateOrdered));
                    jsnRow.AddVariable("DatePromised", Functions.FormatDate(lst[i].DatePromised));
                    jsnRow.AddVariable("RequiredDate", Functions.FormatDate(lst[i].RequiredDate));
                    jsnRow.AddVariable("CM", lst[i].CompanyName);
                    jsnRow.AddVariable("CMNo", lst[i].CompanyNo);
                    jsnRow.AddVariable("CustPONO", lst[i].CustomerPO);
                    jsnRow.AddVariable("Contact", lst[i].ContactName);
                    jsnRow.AddVariable("ContactNo", lst[i].ContactNo);
                    jsnRow.AddVariable("ROHS", lst[i].ROHS);
                    jsnRow.AddVariable("Mfr", lst[i].ManufacturerCode);
                    jsnRow.AddVariable("MfrNo", lst[i].ManufacturerNo);
                    jsnRow.AddVariable("Status", Functions.GetGlobalResource("Status", (BLL.SalesOrderStatus.List)lst[i].Status));
                    jsnRow.AddVariable("QuantityInStock", Functions.FormatNumeric(lst[i].QuantityInStock));
                    jsnRow.AddVariable("ContractNo", lst[i].ContractNo);
                    //if (lst[i].Quantity == lst[i].QuantityInStock)
                    //{
                    //jsnRow.AddVariable("DatePromisedStatus ", "Green");
                    //}
                    //else
                    //{
                    jsnRow.AddVariable("DatePromisedStatus ", lst[i].DatePromisedStatus);
                    //}
                    jsnRow.AddVariable("AS6081", lst[i].AS6081); //[004]
                    jsnRow.AddVariable("blnMakeYellow", blnMakeYellow);
                    jsnRow.AddVariable("TaskCount", lst[i].TaskCount);
                    jsnRow.AddVariable("HasUnFinishedTask", lst[i].HasUnFinishedTask);
                    jsnRowsArray.AddVariable(jsnRow);
                    jsnRow.Dispose();
                    jsnRow = null;
                }
            }
            jsn.AddVariable("Results", jsnRowsArray);
            jsn.AddVariable("SortIndex", GetFormValue_NullableInt("SortIndex", 0));
            jsn.AddVariable("SortDir", GetFormValue_NullableInt("SortDir", SortColumnDirection.ASC));
            jsn.AddVariable("PageIndex", GetFormValue_NullableInt("PageIndex", 0));
            jsn.AddVariable("PageSize", GetFormValue_NullableInt("PageSize", 10));
            OutputResult(jsn);
            jsnRowsArray.Dispose(); jsnRowsArray = null;
            jsn.Dispose(); jsn = null;
            lst = null;
            base.GetData();
        }


        protected override void AddFilterStates()
        {
            //Prevent filter state for Tab
            //AddExplicitFilterState("ViewLevel", GetFormValue_Int("ViewLevel"));
            AddFilterState("Part");
            AddFilterState("Contact");
            AddFilterState("CMName");
            AddFilterState("Salesman");
            AddFilterState("CustPO");
            AddFilterState("RecentOnly");
            AddFilterState("IncludeClosed");
            AddFilterState("SONo");
            AddFilterState("DateOrderedFrom");
            AddFilterState("DateOrderedTo");
            AddFilterState("DatePromisedFrom");
            AddFilterState("DatePromisedTo");
            AddFilterState("UnauthorisedOnly");
            AddFilterState("IncludeOrderSent");
            AddFilterState("SOCheckedStatus");
            AddFilterState("SalesOrderStatus");
            AddFilterState("AS6081"); //[004]
            base.AddFilterStates();
        }

        public void ExportToCSV()
        {
            JsonObject jsn = new JsonObject();
            try
            {
                ViewLevelList enmViewLevel = (ViewLevelList)GetFormValue_Int("ViewLevel");
                string filePath = string.Empty;
                string strFilename = String.Format("report_u{0}r{1}.xlsx", LoginID, (int)DataListNuggetExport.SalesOrder);
                string AS6081 = GetFormValue_String("AS6081");

                DataTable dtResult = SalesOrderLine.DataListNugget_Export(
                    SessionManager.ClientID
                    , (enmViewLevel == ViewLevelList.Team) ? (int?)SessionManager.LoginTeamID : null
                    , (enmViewLevel == ViewLevelList.Division) ? (int?)SessionManager.LoginDivisionID : null
                    , (enmViewLevel == ViewLevelList.My) ? (int?)LoginID : null
                    , GetFormValue_NullableInt("SortIndex")
                    , GetFormValue_NullableInt("SortDir", SortColumnDirection.ASC)
                    , GetFormValue_NullableInt("PageIndex", 0)
                    , GetFormValue_NullableInt("PageSize", 10)
                    , GetFormValue_PartForLikeSearch("Part")
                    , GetFormValue_StringForNameSearchDecode("Contact")
                    , GetFormValue_NullableInt("Country")
                    , GetFormValue_StringForNameSearchDecode("CMName")
                    , GetFormValue_NullableInt("Salesman")
                    , GetFormValue_ForLikeSearchWithSpecialChar("CustPO")
                    , GetFormValue_Boolean("RecentOnly")
                    , GetFormValue_Boolean("IncludeClosed")
                    , GetFormValue_NullableInt("SONoLo")
                    , GetFormValue_NullableInt("SONoHi")
                    , GetFormValue_NullableDateTime("DateOrderedFrom")
                    , GetFormValue_NullableDateTime("DateOrderedTo")
                    , GetFormValue_NullableDateTime("DatePromisedFrom")
                    , GetFormValue_NullableDateTime("DatePromisedTo")
                    , null
                    , GetFormValue_NullableInt("IncludeOrderSent")
                    , GetFormValue_StringForLikeSearch("ContractNo")
                    , GetFormValue_Boolean("IsGlobalLogin")
                    , GetFormValue_NullableInt("Client")
                    , GetFormValue_NullableInt("SOCheckedStatus")
                    , GetFormValue_NullableInt("SalesOrderStatus")
                    , (AS6081 == "1" ? true : (AS6081 == "2" ? false : (bool?)null))
                    , SessionManager.LoginID
                );

                filePath = (new EPPlusExportUtility()).ExportDataTableToCSV(dtResult, strFilename, "SalesOrderResult");
                jsn.AddVariable("Filename", filePath);
                OutputResult(jsn);
            }
            catch (Exception e)
            {
                WriteError(e);
            }
            finally
            {
                jsn.Dispose();
            }
        }
    }
}