Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.HomeNuggets");Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyGIQueries=function(n){Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyGIQueries.initializeBase(this,[n]);this._tblMyGIQueries=null};Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyGIQueries.prototype={get_tblMyGIQueries:function(){return this._tblMyGIQueries},set_tblMyGIQueries:function(n){this._tblMyGIQueries!==n&&(this._tblMyGIQueries=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyGIQueries.callBaseMethod(this,"initialize");this.addRefreshEvent(Function.createDelegate(this,this.getData))},dispose:function(){this.isDisposed||(this._tblMyGIQueries&&this._tblMyGIQueries.dispose(),this._tblMyGIQueries=null,Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyGIQueries.callBaseMethod(this,"dispose"))},setupLoadingState:function(){this._tblMyGIQueries.show(!1);Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyGIQueries.callBaseMethod(this,"setupLoadingState")},getData:function(){this.setupLoadingState();this._tblMyGIQueries.clearTable();var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/HomeNuggets/MyGIQueries");n.set_DataObject("MyGIQueries");n.set_DataAction("GetData");n._intTimeOutMiliseconds=1;n.addParameter("rowcount",this._intRowCount);n.addDataOK(Function.createDelegate(this,this.getDataComplete));n.addError(Function.createDelegate(this,this.homeNuggetDataError));n.addTimeout(Function.createDelegate(this,this.homeNuggetDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getDataComplete:function(n){var i=n._result,r,t,u;for(this.showNoneFoundOrContent(i.Count),r=0;r<i.Items.length;r++)t=i.Items[r],u=[$RGT_nubButton_QueryGILine(t.GoodsInId,t.GoodsInLineId,t.GINumber,!0),$RGT_nubButton_SalesOrder(t.SalesOrderNo,t.SalesOrderNumber),$R_FN.setCleanTextValue(t.DateSent),$R_FN.setCleanTextValue(t.Approvers),$R_FN.setCleanTextValue(t.Status)],this._tblMyGIQueries.addRow(u,null),t=null;this._tblMyGIQueries.show(i.Count>0);this.hideLoading()}};Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyGIQueries.registerClass("Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyGIQueries",Rebound.GlobalTrader.Site.Controls.HomeNuggets.Base,Sys.IDisposable);