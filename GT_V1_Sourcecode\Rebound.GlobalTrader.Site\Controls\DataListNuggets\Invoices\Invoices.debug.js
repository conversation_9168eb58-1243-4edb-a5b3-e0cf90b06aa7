///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//
// RP 23.12.2009:
// - render state on server
//
// RP 16.10.2009:
// - Changes due to changes in base class
//Marker     Changed by      Date         Remarks
//[001]      Vinay           12/07/2012   This need for Rebound- Invoice bulk Emailer
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DataListNuggets");

Rebound.GlobalTrader.Site.Controls.DataListNuggets.Invoices = function(element) {
    Rebound.GlobalTrader.Site.Controls.DataListNuggets.Invoices.initializeBase(this, [element]);
    this._frmConfirm = null;
    this._InvoiceFormat = 'PDF';
};

Rebound.GlobalTrader.Site.Controls.DataListNuggets.Invoices.prototype = {
    get_blnPageLoad: function() { return this._blnPageLoad; }, set_blnPageLoad: function(value) { if (this._blnPageLoad !== value) this._blnPageLoad = value; },
    get_ClientNo: function () { return this._ClientNo; }, set_ClientNo: function (value) { if (this._ClientNo !== value) this._ClientNo = value; },
    get_AllowGenerateXml: function () { return this._AllowGenerateXml; }, set_AllowGenerateXml: function (value) { if (this._AllowGenerateXml !== value) this._AllowGenerateXml = value; },

    initialize: function() {
        //
        this.addSetupDataCallEvent(Function.createDelegate(this, this.setupDataCall));
        this.addGetDataOKEvent(Function.createDelegate(this, this.getDataOK));
        this._ibtnPrint = $get(this._aryButtonIDs[0]);
        //[001] code start
        this._ibtnEmail = $get(this._aryButtonIDs[1]);
        this._frmConfirm = $find(this._aryFormIDs[0]);
        this._frmConfirm.addCancel(Function.createDelegate(this, this.hideConfirmForm));
        this._frmConfirm.addSaveComplete(Function.createDelegate(this, this.saveCeaseComplete));
        this._frmConfirm.addNotConfirmed(Function.createDelegate(this, this.hideConfirmForm));
        if (this._ibtnEmail) $R_IBTN.addClick(this._ibtnEmail, Function.createDelegate(this, this.showConfirmForm));
        //[001] code end
        //if (this._ibtnPrint) $R_IBTN.addClick(this._ibtnPrint, Function.createDelegate(this, this.printInvoices));
        if (this._ibtnPrint) $R_IBTN.addClick(this._ibtnPrint, Function.createDelegate(this, this.openFormatModal));
        document.getElementById("btnFormatAction").addEventListener("click", Function.createDelegate(this, this.selectInvoicePrintFormat));
        document.getElementById("btnFormatCancel").addEventListener("click", Function.createDelegate(this, this.closePopup));

        this.enableBulkButtons(false);
        this._table.addMultipleSelectionChanged(Function.createDelegate(this, this.selectionMade));
        this._strPathToData = "controls/DataListNuggets/Invoices";
        this._strDataObject = "Invoices";
        this.getData();
        this._strCK = "lns";
        this._strCKExp = 1;
        this._lnsSeperator = "|";
        if (this.getFilterField("ctlNotExported") && this.getFilterField("ctlExportedOnly")) {
            $find(this.getFilterField("ctlNotExported").get_id())._element.setAttribute("onclick", String.format("$find(\"{0}\").checkNotExport()", this._element.id));
            $find(this.getFilterField("ctlExportedOnly").get_id())._element.setAttribute("onclick", String.format("$find(\"{0}\").f()", this._element.id));
        }
        //if (this.getFilterField("ctlCOC")) {
        //    $find(this.getFilterField("ctlCOC").get_id())._element.setAttribute("onclick", String.format("$find(\"{0}\").COCChecked()", this._element.id));
        //}
        //if (this.getFilterField("ctlPackaginSlip")) {
        //    $find(this.getFilterField("ctlPackaginSlip").get_id())._element.setAttribute("onclick", String.format("$find(\"{0}\").PackagingChecked()", this._element.id));
        //}

        Rebound.GlobalTrader.Site.Controls.DataListNuggets.Invoices.callBaseMethod(this, "initialize");
    },

    dispose: function() {
        if (this.isDisposed) return;
        //[001] code start
        if (this._ibtnEmail) this._ibtnEmail = null;
        if (this._frmConfirm) this._frmConfirm.dispose();
        this._frmConfirm = null;
        this._blnPageLoad = null;
        this._InvoiceFormat = null;
        //[001] code end
        this.closePopup();
        Rebound.GlobalTrader.Site.Controls.DataListNuggets.Invoices.callBaseMethod(this, "dispose");
    },

    enableBulkButtons: function(bln) {
        if (this._ibtnPrint) $R_IBTN.enableButton(this._ibtnPrint, bln);
        //[001] code start
        if (this._ibtnEmail) $R_IBTN.enableButton(this._ibtnEmail, bln);
        //[001] code end
    },

    printInvoices: function () {
        var coc = Boolean.parse(this.getFilterField("ctlCOC").getValue().toString());
        var packaging = Boolean.parse(this.getFilterField("ctlPackaginSlip").getValue().toString());
        var strIDs = $R_FN.arrayToSingleString(this._table._aryCurrentValues, this._lnsSeperator);
        $R_FN.setCookie(this._strCK, strIDs, this._strCKExp); strIDs = "";

        if (this._InvoiceFormat == 'XML') {
            $R_FN.openPrintWindowWithMultiples($R_ENUM$PrintObject.XmlInvoice, strIDs);
        }
        else if (coc && packaging) {
            //var strIDs = $R_FN.arrayToSingleString(this._table._aryCurrentValues, this._lnsSeperator);
            //$R_FN.setCookie(this._strCK, strIDs, this._strCKExp); strIDs = "";
            $R_FN.setCookie("coc", this.getFilterField("ctlCOC").getValue(), this._strCKExp);
            $R_FN.setCookie("Packaging", this.getFilterField("ctlPackaginSlip").getValue(), this._strCKExp);
            $R_FN.openPrintWindowWithMultiples($R_ENUM$PrintObject.InvoiceIncludeCOCPackaging, strIDs);
            //strIDs = null;
        }
       else  if (Boolean.parse(this.getFilterField("ctlCOC").getValue().toString())) {
            //var strIDs = $R_FN.arrayToSingleString(this._table._aryCurrentValues, this._lnsSeperator);
            //$R_FN.setCookie(this._strCK, strIDs, this._strCKExp); strIDs = "";
            $R_FN.setCookie("coc", this.getFilterField("ctlCOC").getValue(), this._strCKExp);
            $R_FN.openPrintWindowWithMultiples($R_ENUM$PrintObject.Invoice, strIDs);
            //strIDs = null;
        }
       else if (Boolean.parse(this.getFilterField("ctlPackaginSlip").getValue().toString())) {
           //var strIDs = $R_FN.arrayToSingleString(this._table._aryCurrentValues, this._lnsSeperator);
           //$R_FN.setCookie(this._strCK, strIDs, this._strCKExp); strIDs = "";
           $R_FN.setCookie("Packaging", this.getFilterField("ctlPackaginSlip").getValue(), this._strCKExp);
           $R_FN.openPrintWindowWithMultiples($R_ENUM$PrintObject.InvoiceIncludePackaging, strIDs);
           //strIDs = null;
       }
       else {
           //var strIDs = $R_FN.arrayToSingleString(this._table._aryCurrentValues, this._lnsSeperator);
           //$R_FN.setCookie(this._strCK, strIDs, this._strCKExp); strIDs = "";
           $R_FN.setCookie("coc", this.getFilterField("ctlCOC").getValue(), this._strCKExp);
           $R_FN.openPrintWindowWithMultiples($R_ENUM$PrintObject.Invoice, strIDs);
           //strIDs = null;
           
                //var strIDs = $R_FN.arrayToSingleString(this._table._aryCurrentValues, this._lnsSeperator);
                //$r_fn.setcookie(this._strck, strids, this._strckexp); strids = "";
                //var strurl = string.format("print.aspx?{0}={1}", $r_qs_printobject, $r_enum$printobject.invoice);
                //if (strids.length > 0) strurl += string.format("&{0}={1}", $r_qs_lineids, strids);
                //var strwin = window.open(strurl);
                //strwin.print();
        }
        strIDs = null;
    },
    selectionMade: function() {
        this.enableBulkButtons(this._table._arySelectedIndexes.length > 0);
    },

    setupDataCall: function() {
        if (this._blnPageLoad) {
            this._objData.addParameter("ExportedOnly", "true");
            this._objData.addParameter("RecentOnly", "true");
        }
        this.enableBulkButtons(false);
        this._blnPageLoad = false;
    },

    getDataOK: function(args) {
        for (var i = 0, l = this._objResult.Results.length; i < l; i++) {
            var row = this._objResult.Results[i];
            var aryData = [
				$RGT_nubButton_Invoice(row.ID, row.No)
				, $R_FN.writeDoubleCellValue($RGT_nubButton_Company(row.CMNo, row.CM), $R_FN.setCleanTextValue(row.CustPONO))
				, $RGT_nubButton_SalesOrder(row.SONo, row.SO)
				, $R_FN.setCleanTextValue(row.Date)
				, row.Value
			];
            this._table.addRow(aryData, row.ID, false);
            aryData = null; row = null;
        }
    },
    //[001] code start
    showConfirmForm: function() {
        this._frmConfirm._strInvoices = this._table._aryCurrentValues;
        this._frmConfirm._blnCOC = this.getFilterField("ctlCOC").getValue();
        this._frmConfirm._blnPackagingSlip = this.getFilterField("ctlPackaginSlip").getValue();
        this._frmConfirm._ClientNo = this._ClientNo;
        this._frmConfirm._AllowGenerateXml = this._AllowGenerateXml;
        this.showForm(this._frmConfirm, true);
    },
    hideConfirmForm: function() {
        this.showForm(this._frmConfirm, false);
    },
    saveCeaseComplete: function() {
        this.hideConfirmForm();
    },
    checkExportOnly: function() {
        if (Boolean.parse(this.getFilterField("ctlNotExported").getValue().toString()) == true) {
            this.getFilterField("ctlNotExported").enableField(false);
            this.getFilterField("ctlExportedOnly").enableField(true);
        }
    },
    checkNotExport: function() {
        if (Boolean.parse(this.getFilterField("ctlExportedOnly").getValue().toString()) == true) {
            this.getFilterField("ctlNotExported").enableField(true);
            this.getFilterField("ctlExportedOnly").enableField(false);
        }
    },
    COCChecked: function () {
        if(Boolean.parse(this.getFilterField("ctlPackaginSlip").getValue().toString())== true)
            this.setFilterFieldValue("ctlPackaginSlip", !Boolean.parse(this.getFilterField("ctlCOC").getValue().toString()))
        
    },
    PackagingChecked: function () {
        if (Boolean.parse(this.getFilterField("ctlCOC").getValue().toString()) == true)
        this.setFilterFieldValue("ctlCOC", !Boolean.parse(this.getFilterField("ctlPackaginSlip").getValue().toString()))
    },

    openFormatModal: function () {
        $("#overlay").css("display", "block");
        let selectedOption = this._AllowGenerateXml && this._ClientNo == '108' ? 'XML' : 'PDF';
        $('input:radio[name=rdInvoiceFormat]').val([selectedOption]);
        $('#optionXML').css("display", this._AllowGenerateXml ? 'block' : 'none');
        $("#formatModal").dialog("open");
    },

    selectInvoicePrintFormat: function () {
        if (!this._AllowGenerateXml) {
            this._InvoiceFormat = 'PDF';
        } else {
            this._InvoiceFormat = $('input[name="rdInvoiceFormat"]:checked').val();
        }
        this.printInvoices();
        this.closePopup();
    },

    closePopup: function () {
        $("#overlay").css("display", "none");
        $("#formatModal").dialog("close");
    },
};
Rebound.GlobalTrader.Site.Controls.DataListNuggets.Invoices.registerClass("Rebound.GlobalTrader.Site.Controls.DataListNuggets.Invoices", Rebound.GlobalTrader.Site.Controls.DataListNuggets.Base, Sys.IDisposable);
