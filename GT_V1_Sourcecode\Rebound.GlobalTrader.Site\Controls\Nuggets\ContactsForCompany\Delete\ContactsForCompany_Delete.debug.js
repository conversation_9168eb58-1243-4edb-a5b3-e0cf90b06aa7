///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.ContactsForCompany_Delete = function(element) { 
	Rebound.GlobalTrader.Site.Controls.Forms.ContactsForCompany_Delete.initializeBase(this, [element]);
	this._intContactID = -1;
	this._strContactName = "";
};

Rebound.GlobalTrader.Site.Controls.Forms.ContactsForCompany_Delete.prototype = {

	get_intContactID: function() { return this._intContactID; }, 	set_intContactID: function(value) { if (this._intContactID !== value)  this._intContactID = value; }, 

	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.Forms.ContactsForCompany_Delete.callBaseMethod(this, "initialize");
		this.addShown(Function.createDelegate(this, this.formShown));
	},

	dispose: function() { 
		if (this.isDisposed) return;
		if (this._ctlConfirm) this._ctlConfirm.dispose();
		this._intContactID = null;
		this._strContactName = null;
		this._ctlConfirm = null;
		Rebound.GlobalTrader.Site.Controls.Forms.ContactsForCompany_Delete.callBaseMethod(this, "dispose");
	},
	
	formShown: function() {
		if (this._blnFirstTimeShown) {
			this._ctlConfirm = this.getFieldComponent("ctlConfirm");
			this._ctlConfirm.addClickYesEvent(Function.createDelegate(this, this.yesClicked));
			this._ctlConfirm.addClickNoEvent(Function.createDelegate(this, this.noClicked));
		}
	},

	yesClicked: function() {
		this.showSaving(true);
		var obj = new Rebound.GlobalTrader.Site.Data();
		obj.set_PathToData("controls/Nuggets/ContactsForCompany");
		obj.set_DataObject("ContactsForCompany");
		obj.set_DataAction("Delete");
		obj.addParameter("id", this._intContactID);
		obj.addDataOK(Function.createDelegate(this, this.saveDeleteComplete));
		obj.addError(Function.createDelegate(this, this.saveDeleteError));
		obj.addTimeout(Function.createDelegate(this, this.saveDeleteError));
		$R_DQ.addToQueue(obj);
		$R_DQ.processQueue();
		obj = null;
	},
	
	noClicked: function() {
		this.onNotConfirmed();
	},

	saveDeleteError: function(args) {
		this._strErrorMessage = args._errorMessage;
		this.onSaveError();
	},
	
	saveDeleteComplete: function(args) {
		this.showSaving(false);
		if (args._result.Result == true) {
			this.onSaveComplete();
		} else {
			this._strErrorMessage = args._errorMessage;
			this.onSaveError();
		}
	}
	
};

Rebound.GlobalTrader.Site.Controls.Forms.ContactsForCompany_Delete.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.ContactsForCompany_Delete", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
