﻿/*  
===========================================================================================  
TASK			UPDATED BY      DATE			ACTION		DESCRIPTION  
[US-242815]		Trung Pham		05-May-2025		UPDATE		Add more fields  
===========================================================================================  
*/
CREATE OR ALTER PROCEDURE usp_ExportApproveDetailsInsert          
@ExportApprovalId   INT,          
@DestinationCountryNo   INT=0,          
@MilitaryUseNo    INT=0,          
@EndUserText    NVARCHAR(MAX)=NULL,                
@LoginId     INT,
@PartApplication NVARCHAR(100)=NULL,
@ExportControl INT=0,
@AerospaceUse INT=0,
@PartTested NVARCHAR(200)=NULL
AS          
/*          
 *[001]  Created   Abhinav Saxena  18-04-2023  Add new Proc to add/update the Export Approval details.          
 */          
BEGIN          
SET NOCOUNT ON           
Declare @IsApproved     BIT=0           
CREATE TABLE #tempExportApprovalDetails         
(                 
IsApproved    BIT          
)          
BEGIN TRY          
-----Insert Export Approval Details---          
IF((SELECT ISNULL(COUNT(1),0) FROM tbSO_ExportApprovalDetails WHERE ExportApprovalNo=@ExportApprovalId)=0)          
BEGIN          
INSERT INTO tbSO_ExportApprovalDetails(  
ExportApprovalNo,  
EndDestinationCountryNo,     
MilitaryUseNo,  
ENDUserText,  
IsInformationFilled,  
UpdatedBy,  
DLUP,
PartApplication,
ExportControl,
AerospaceUse,
PartTested
)  
VALUES(  
@ExportApprovalId,  
@DestinationCountryNo,  
@MilitaryUseNo,  
@EndUserText,  
CAST(1 AS BIT),  
@LoginId,  
GetDATE(),
@PartApplication,
@ExportControl,
@AerospaceUse,
@PartTested
)               
END          
ELSE        
BEGIN          
UPDATE tbSO_ExportApprovalDetails SET          
EndDestinationCountryNo=@DestinationCountryNo,          
MilitaryUseNo=@MilitaryUseNo,          
ENDUserText=@EndUserText,          
IsInformationFilled=1,         
UpdatedBy=@LoginId,          
DLUP=GETDATE(),
PartApplication=@PartApplication,
ExportControl=@ExportControl,
AerospaceUse=@AerospaceUse,
PartTested=@PartTested
WHERE ExportApprovalNo=@ExportApprovalId          
END          
SET @IsApproved=1;          
END TRY          
BEGIN CATCH          
SET @IsApproved=0;          
END CATCH          
          
          
IF(@IsApproved=1)          
BEGIN          
INSERT INTO #tempExportApprovalDetails VALUES(1)          
END          
ELSE          
BEGIN          
INSERT INTO #tempExportApprovalDetails VALUES(0)         
END          
SELECT * FROm #tempExportApprovalDetails          
DROP TABLE #tempExportApprovalDetails          
--------------END-------------------          
SET NOCOUNT OFF          
END  