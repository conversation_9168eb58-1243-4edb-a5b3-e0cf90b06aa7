Type.registerNamespace("Rebound.GlobalTrader.Site.Pages.Contact");Rebound.GlobalTrader.Site.Pages.Contact.ContactDetail=function(n){Rebound.GlobalTrader.Site.Pages.Contact.ContactDetail.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Pages.Contact.ContactDetail.prototype={get_ctlPageTitle:function(){return this._ctlPageTitle},set_ctlPageTitle:function(n){this._ctlPageTitle!==n&&(this._ctlPageTitle=n)},get_ctlLeft_RecentlyViewed:function(){return this._ctlLeft_RecentlyViewed},set_ctlLeft_RecentlyViewed:function(n){this._ctlLeft_RecentlyViewed!==n&&(this._ctlLeft_RecentlyViewed=n)},get_ctlContactList:function(){return this._ctlContactList},set_ctlContactList:function(n){this._ctlContactList!==n&&(this._ctlContactList=n)},get_ctlContactInfoMain:function(){return this._ctlContactInfoMain},set_ctlContactInfoMain:function(n){this._ctlContactInfoMain!==n&&(this._ctlContactInfoMain=n)},get_ctlContactExtendedInfo:function(){return this._ctlContactExtendedInfo},set_ctlContactExtendedInfo:function(n){this._ctlContactExtendedInfo!==n&&(this._ctlContactExtendedInfo=n)},get_ctlContactLog:function(){return this._ctlContactLog},set_ctlContactLog:function(n){this._ctlContactLog!==n&&(this._ctlContactLog=n)},get_intContactID:function(){return this._intContactID},set_intContactID:function(n){this._intContactID!==n&&(this._intContactID=n)},get_btnTransactions:function(){return this._btnTransactions},set_btnTransactions:function(n){this._btnTransactions!==n&&(this._btnTransactions=n)},initialize:function(){Rebound.GlobalTrader.Site.Pages.Contact.ContactDetail.callBaseMethod(this,"initialize")},goInit:function(){this._ctlContactList&&this._ctlContactList.addSelectContact(Function.createDelegate(this,this.ctlContactList_SelectContact));this._ctlContactInfoMain&&this._ctlContactInfoMain.addSaveEditComplete(Function.createDelegate(this,this.ctlContactInfoMain_SaveEditComplete));Rebound.GlobalTrader.Site.Pages.Contact.ContactDetail.callBaseMethod(this,"goInit")},dispose:function(){this.isDisposed||(this._ctlPageTitle&&this._ctlPageTitle.dispose(),this._ctlLeft_RecentlyViewed&&this._ctlLeft_RecentlyViewed.dispose(),this._ctlContactList&&this._ctlContactList.dispose(),this._ctlContactInfoMain&&this._ctlContactInfoMain.dispose(),this._ctlContactExtendedInfo&&this._ctlContactExtendedInfo.dispose(),this._ctlContactLog&&this._ctlContactLog.dispose(),this._btnTransactions&&this._btnTransactions.dispose(),this._ctlPageTitle=null,this._ctlLeft_RecentlyViewed=null,this._ctlContactList=null,this._ctlContactInfoMain=null,this._ctlContactExtendedInfo=null,this._ctlContactLog=null,this._btnTransactions=null,Rebound.GlobalTrader.Site.Pages.Contact.ContactDetail.callBaseMethod(this,"dispose"))},ctlContactList_SelectContact:function(){this._intContactID=this._ctlContactList._intContactID;var n=String.format($R_RES.ContactsForCompany,this._ctlContactList._strCompanyName);this._intContactID>0&&(n=String.format("{0} ({1})",this._ctlContactList._strContactName,this._ctlContactList._strCompanyName),this._ctlContactInfoMain._intContactID=this._intContactID,this._ctlContactInfoMain.refresh(),this._ctlContactLog._intContactID=this._intContactID,this._ctlContactLog.refresh(),this._ctlLeft_RecentlyViewed.addItem($RGT_gotoURL_Contact(this._intContactID),n));this._ctlPageTitle.updateTitle(n);this._ctlContactInfoMain.show(this._intContactID>0);this._ctlContactLog.show(this._intContactID>0);this._btnTransactions&&this._btnTransactions.changeContact(this._intContactID,this._ctlContactList._strContactName)},ctlContactInfoMain_SaveEditComplete:function(){this._ctlContactList.refresh()}};Rebound.GlobalTrader.Site.Pages.Contact.ContactDetail.registerClass("Rebound.GlobalTrader.Site.Pages.Contact.ContactDetail",Rebound.GlobalTrader.Site.Pages.Content,Sys.IDisposable);