using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using System.Collections;
using System.Globalization;
using System.Threading;

namespace Rebound.GlobalTrader.Site.Controls {
	[DefaultProperty("")]
	[ToolboxData("<{0}:TimeSelect runat=server></{0}:TimeSelect>")]
	public class TimeSelect : Panel, IScriptControl, INamingContainer {

		#region Locals

		private DropDownList _ddlHour = new DropDownList();
		private DropDownList _ddlMinutes = new DropDownList();
		protected ScriptManager _sm;

		#endregion

		#region Properties

		private DateTime? _dtmInitialValue = null;
		/// <summary>
		/// The initial value of the control
		/// </summary>
		public DateTime? InitialValue {
			get { return _dtmInitialValue; }
			set { _dtmInitialValue = value; }
		}

		private bool _blnShowFull60Minutes = false;
		/// <summary>
		/// Should the minutes dropdown show 60 minutes or just every 15 minutes? (default is false)
		/// </summary>
		public bool ShowFull60Minutes {
			get { return _blnShowFull60Minutes; }
			set { _blnShowFull60Minutes = value; }
		}

		#endregion

		/// <summary>
		/// create controls
		/// </summary>
		protected override void CreateChildControls() {
			//hours dropdown
			for (int i = 0; i < 24; i++) {
				ListItem li = new ListItem();
				li.Text = String.Format("{0}{1}", (i < 10) ? "0" : "", i);
				li.Value = li.Text;
				_ddlHour.Items.Add(li);
			}
			//minutes dropdown
			for (int i = 0; i < 60; i += (_blnShowFull60Minutes) ? 1 : 15) {
				ListItem li = new ListItem();
				li.Text = String.Format("{0}{1}", (i < 10) ? "0" : "", i);
				li.Value = li.Text;
				_ddlMinutes.Items.Add(li);
			}
			Controls.Add(_ddlHour);
			ControlBuilders.CreateLiteralInsideParent(this, "&nbsp;:&nbsp;");
			Controls.Add(_ddlMinutes);
			base.CreateChildControls();
		}

		/// <summary>
		/// OnPreRender
		/// </summary>
		/// <param name="e"></param>
		protected override void OnPreRender(EventArgs e) {
			if (!this.DesignMode) {
				_sm = ScriptManager.GetCurrent(Page);
				if (_sm == null) throw new HttpException("A ScriptManager control must exist on the current page.");
				_sm.RegisterScriptControl(this);
			}
			base.OnPreRender(e);
		}

		/// <summary>
		/// Render
		/// </summary>
		/// <param name="writer"></param>
		protected override void Render(HtmlTextWriter writer) {
			if (!this.DesignMode) _sm.RegisterScriptDescriptors(this);
			base.Render(writer);
		}

		#region IScriptControl Members

		protected virtual IEnumerable<ScriptReference> GetScriptReferences() {
			bool blnDebug = false;
#if (DEBUG)
			blnDebug = true;
#endif
			return new ScriptReference[] { Functions.GetScriptReference(blnDebug, "Rebound.GlobalTrader.Site", "Controls.Basic.TimeSelect.TimeSelect", true) };
		}

		protected virtual IEnumerable<ScriptDescriptor> GetScriptDescriptors() {
			ScriptControlDescriptor descriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.TimeSelect", this.ClientID);
			descriptor.AddElementProperty("ddlHour", _ddlHour.ClientID);
			descriptor.AddElementProperty("ddlMinutes", _ddlMinutes.ClientID);
			return new ScriptDescriptor[] { descriptor };
		}

		IEnumerable<ScriptReference> IScriptControl.GetScriptReferences() {
			return GetScriptReferences();
		}

		IEnumerable<ScriptDescriptor> IScriptControl.GetScriptDescriptors() {
			return GetScriptDescriptors();
		}

		#endregion

	}

}