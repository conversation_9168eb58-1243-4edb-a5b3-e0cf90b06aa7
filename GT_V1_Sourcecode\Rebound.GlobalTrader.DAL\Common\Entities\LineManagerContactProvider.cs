﻿//Marker     Changed by         Date         Remarks
//[001]      <PERSON><PERSON><PERSON><PERSON>     28/01/2022    Added class for Line Manager Contact.
using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;
using System.Data.Common;

namespace Rebound.GlobalTrader.DAL
{
    public abstract class LineManagerContactProvider : DataAccess
    {
        static private LineManagerContactProvider _instance = null;
        /// <summary>
        /// Returns an instance of the provider type specified in the config file
        /// </summary>       
        static public LineManagerContactProvider Instance
        {
            get
            {
                if (_instance == null) _instance = (LineManagerContactProvider)Activator.CreateInstance(Type.GetType(Globals.Settings.LineManagerContacts.ProviderType));
                return _instance;
            }
        }
        public LineManagerContactProvider()
        {
            this.ConnectionString = Globals.Settings.LineManagerContacts.ConnectionString;
        }


        /// <summary>
        /// DropDown
        /// Calls [usp_dropdown_LineManagerContacts]
        /// </summary>
        public abstract List<LineManagerContactDetails> DropDown(System.Int32? BuyerId, System.Int32? ClientNo, System.Boolean? UpdateManager);
    }
}
