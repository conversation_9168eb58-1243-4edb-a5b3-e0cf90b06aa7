///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.FormFieldCollections");

Rebound.GlobalTrader.Site.Controls.FormFieldCollections.ToDo = function (element) {
    Rebound.GlobalTrader.Site.Controls.FormFieldCollections.ToDo.initializeBase(this, [element]);
    this._intToDoID = null;
    this._intMessageID = null;
    this._intCategoryID = null;
    this._blnCreateReminder = false;
    this._blnFirstTimeReminderShown = true;
    this._quoteMinReminderDate = null;
    this._quoteStatus = null;
};

Rebound.GlobalTrader.Site.Controls.FormFieldCollections.ToDo.prototype = {

    initialize: function () {
        Rebound.GlobalTrader.Site.Controls.FormFieldCollections.ToDo.callBaseMethod(this, "initialize");
    },

    dispose: function () {
        if (this.isDisposed) return;
        this._intToDoID = null;
        this._intMessageID = null;
        this._blnCreateReminder = null;
        this._blnFirstTimeReminderShown = null;
        this._intCategoryID = null;
        this._quoteStatus = null;
        Rebound.GlobalTrader.Site.Controls.FormFieldCollections.ToDo.callBaseMethod(this, "dispose");
    },

    setupReminderClick: function () {
        this._ctlRelatedForm.getFieldControl("ctlReminder").addClick(Function.createDelegate(this, this.selectReminder));
    },

    selectReminder: function () {
        this._blnCreateReminder = this._ctlRelatedForm.getFieldValue("ctlReminder");
        this._ctlRelatedForm.showField("ctlReminderDate", this._blnCreateReminder);
        this._ctlRelatedForm.showField("ctlReminderTime", this._blnCreateReminder);
        this._ctlRelatedForm.showField("ctlReminderText", this._blnCreateReminder);
        this._ctlRelatedForm.showField("ctlDailyReminder", (this._blnCreateReminder && this._intCategoryID == 2 && (this._quoteStatus == 'Offered' || this._quoteStatus == 'Partially Offered')));
        if (this._blnCreateReminder && this._blnFirstTimeReminderShown && this._intCategoryID != 2) {
            this._ctlRelatedForm.setFieldValue("ctlReminderDate", this._ctlRelatedForm.getFieldValue("ctlDueDate"));
            this._ctlRelatedForm.setFieldValue("ctlReminderTime", this._ctlRelatedForm.getFieldValue("ctlDueTime"));
            this._ctlRelatedForm.setFieldValue("ctlReminderText", this._ctlRelatedForm.getFieldValue("ctlText"));
            this._blnFirstTimeReminderShown = false;
        }
    },

    validateFields: function () {
        var bln = true;
        var dtm;
        var dtmToday = new Date();
        if (!this._ctlRelatedForm.checkFieldEntered("ctlSubject")) bln = false;
        if (!this._ctlRelatedForm.checkFieldEntered("ctlText")) bln = false;
        if (this._ctlRelatedForm.getFieldValue("ctlDueDate").length > 0) {
            dtm = $R_FN.getDateFromDateAndTimeFields("ctlDueDate", "ctlDueTime", this._ctlRelatedForm);
            if (dtm < dtmToday) {
                this._ctlRelatedForm.setFieldInError("ctlDueDate", true, $R_RES.DateTimeMustBeInFuture);
                this._ctlRelatedForm.setFieldInError("ctlDueTime", true, $R_RES.DateTimeMustBeInFuture);
                bln = false;
            }
        }
        if (this._blnCreateReminder) {
            if (!this._ctlRelatedForm.checkFieldEntered("ctlReminderDate")) bln = false;
            if (!this._ctlRelatedForm.checkFieldEntered("ctlReminderText")) bln = false;
            if (this._ctlRelatedForm.getFieldValue("ctlReminderDate").length > 0) {
                dtm = $R_FN.getDateFromDateAndTimeFields("ctlReminderDate", "ctlReminderTime", this._ctlRelatedForm);
                //validate quote reminder date if exists
                //if (this._intCategoryID == 2 && this._quoteMinReminderDate) {
                //    var dq = $R_FN.parseDateFromUKFormat(this._quoteMinReminderDate);
                //    if (dtm < dq) {
                //        this._ctlRelatedForm.setFieldInError("ctlReminderDate", true, $R_RES.QuoteMinReminderDate);
                //        this._ctlRelatedForm.setFieldInError("ctlReminderTime", true, $R_RES.QuoteMinReminderDate);
                //        bln = false;
                //    }
                //} else if (dtm < dtmToday) {
                //    this._ctlRelatedForm.setFieldInError("ctlReminderDate", true, $R_RES.DateTimeMustBeInFuture);
                //    this._ctlRelatedForm.setFieldInError("ctlReminderTime", true, $R_RES.DateTimeMustBeInFuture);
                //    bln = false;
                //}
                if (dtm < dtmToday) {
                    this._ctlRelatedForm.setFieldInError("ctlReminderDate", true, $R_RES.DateTimeMustBeInFuture);
                    this._ctlRelatedForm.setFieldInError("ctlReminderTime", true, $R_RES.DateTimeMustBeInFuture);
                    bln = false;
                }
            }
        }
        return bln;
    },

    addFieldsToDataObject: function (obj) {
        obj.addParameter("ID", this._intToDoID);
        obj.addParameter("Subject", this._ctlRelatedForm.getFieldValue("ctlSubject"));
        obj.addParameter("DueDate", $R_FN.getDateFromDateAndTimeFields("ctlDueDate", "ctlDueTime", this._ctlRelatedForm, true));
        obj.addParameter("Text", this._ctlRelatedForm.getFieldValue("ctlText"));
        obj.addParameter("MessageID", this._intMessageID);
        obj.addParameter("HasReminder", this._blnCreateReminder);
        obj.addParameter("HasReview", this._ctlRelatedForm.getFieldValue("ctlReview"));
        obj.addParameter("ToDoListTypeId", this._ctlRelatedForm.getFieldValue("ctlToDoListType"));
        if (this._blnCreateReminder) {
            obj.addParameter("ReminderDate", $R_FN.getDateFromDateAndTimeFields("ctlReminderDate", "ctlReminderTime", this._ctlRelatedForm, true));
            obj.addParameter("DailyReminder", this._ctlRelatedForm.getFieldValue("ctlDailyReminder"));
            obj.addParameter("ReminderText", this._ctlRelatedForm.getFieldValue("ctlReminderText"));
        }
    }

};

Rebound.GlobalTrader.Site.Controls.FormFieldCollections.ToDo.registerClass("Rebound.GlobalTrader.Site.Controls.FormFieldCollections.ToDo", Rebound.GlobalTrader.Site.Controls.Forms.LabelFormField, Sys.IDisposable);
