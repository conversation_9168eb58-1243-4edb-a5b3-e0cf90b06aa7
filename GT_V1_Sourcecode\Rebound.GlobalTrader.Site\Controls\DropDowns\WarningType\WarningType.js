Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");Rebound.GlobalTrader.Site.Controls.DropDowns.WarningType=function(n){Rebound.GlobalTrader.Site.Controls.DropDowns.WarningType.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.DropDowns.WarningType.prototype={initialize:function(){Rebound.GlobalTrader.Site.Controls.DropDowns.WarningType.callBaseMethod(this,"initialize");this.addSetupDataCall(Function.createDelegate(this,this.setupDataCall));this.addGetDataOK(Function.createDelegate(this,this.dataCallOK))},dispose:function(){this.isDisposed||Rebound.GlobalTrader.Site.Controls.DropDowns.WarningType.callBaseMethod(this,"dispose")},setupDataCall:function(){this._objData.set_PathToData("controls/DropDowns/WarningType");this._objData.set_DataObject("WarningType");this._objData.set_DataAction("GetData")},dataCallOK:function(){var t=this._objData._result,n;if(t.Types)for(n=0;n<t.Types.length;n++)this.addOption(t.Types[n].Name,t.Types[n].ID)}};Rebound.GlobalTrader.Site.Controls.DropDowns.WarningType.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.WarningType",Rebound.GlobalTrader.Site.Controls.DropDowns.Base);