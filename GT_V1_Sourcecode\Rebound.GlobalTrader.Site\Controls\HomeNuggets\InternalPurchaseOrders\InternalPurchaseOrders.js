Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.HomeNuggets");Rebound.GlobalTrader.Site.Controls.HomeNuggets.AllPurchaseOrdersDueIn=function(n){Rebound.GlobalTrader.Site.Controls.HomeNuggets.AllPurchaseOrdersDueIn.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.HomeNuggets.AllPurchaseOrdersDueIn.prototype={get_pnlDueIn:function(){return this._pnlDueIn},set_pnlDueIn:function(n){this._pnlDueIn!==n&&(this._pnlDueIn=n)},get_tblDueIn:function(){return this._tblDueIn},set_tblDueIn:function(n){this._tblDueIn!==n&&(this._tblDueIn=n)},get_pnlMore:function(){return this._pnlMore},set_pnlMore:function(n){this._pnlMore!==n&&(this._pnlMore=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.HomeNuggets.AllPurchaseOrdersDueIn.callBaseMethod(this,"initialize");this.addRefreshEvent(Function.createDelegate(this,this.getData))},dispose:function(){this.isDisposed||(this._tblDueIn&&this._tblDueIn.dispose(),this._pnlDueIn=null,this._tblDueIn=null,this._pnlMore=null,Rebound.GlobalTrader.Site.Controls.HomeNuggets.AllPurchaseOrdersDueIn.callBaseMethod(this,"dispose"))},setupLoadingState:function(){$R_FN.showElement(this._pnlMore,!1);$R_FN.showElement(this._pnlDueIn,!1);Rebound.GlobalTrader.Site.Controls.HomeNuggets.AllPurchaseOrdersDueIn.callBaseMethod(this,"setupLoadingState")},showNoData:function(n){this.showContent(!0);$R_FN.showElement(this._pnlNoData,n)},getData:function(){this.setupLoadingState();var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/HomeNuggets/AllPurchaseOrdersDueIn");n.set_DataObject("AllPurchaseOrdersDueIn");n.set_DataAction("GetData");n.addParameter("rowcount",this._intRowCount);n.addDataOK(Function.createDelegate(this,this.getDataComplete));n.addError(Function.createDelegate(this,this.homeNuggetDataError));n.addTimeout(Function.createDelegate(this,this.homeNuggetDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getDataComplete:function(n){var i,r,t,u;for(this.showNoData(n._result.Count==0),$R_FN.showElement(this._pnlMore,!0),i=n._result,this._tblDueIn.clearTable(),r=0;r<i.DueInPO.length;r++)t=i.DueInPO[r],u=[$RGT_nubButton_ReceivePurchaseOrder(t.ID,t.No),$RGT_nubButton_Company(t.CMNo,t.CM),t.Due],this._tblDueIn.addRow(u,null);$R_FN.showElement(this._pnlDueIn,i.DueInPO.length>0);this.hideLoading()}};Rebound.GlobalTrader.Site.Controls.HomeNuggets.AllPurchaseOrdersDueIn.registerClass("Rebound.GlobalTrader.Site.Controls.HomeNuggets.AllPurchaseOrdersDueIn",Rebound.GlobalTrader.Site.Controls.HomeNuggets.Base,Sys.IDisposable);