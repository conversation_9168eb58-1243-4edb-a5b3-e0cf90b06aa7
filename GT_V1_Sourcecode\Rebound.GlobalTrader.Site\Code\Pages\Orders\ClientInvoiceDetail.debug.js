///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference name="~/Controls/IconButton/IconButton.js" />
///<reference name="~/Controls/ReboundTextBox/ReboundTextBox.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//Marker     Changed by      Date         Remarks
//[002]      Prakash           13/04/2016   Upload PDF document for Clientinvoices
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Pages.Orders");

Rebound.GlobalTrader.Site.Pages.Orders.ClientInvoiceDetail = function(el) { 
	Rebound.GlobalTrader.Site.Pages.Orders.ClientInvoiceDetail.initializeBase(this, [el]);
};

Rebound.GlobalTrader.Site.Pages.Orders.ClientInvoiceDetail.prototype = {
    get_intClientInvoiceID: function() { return this._intClientInvoiceID; }, set_intClientInvoiceID: function(v) { if (this._intClientInvoiceID !== v) this._intClientInvoiceID = v; },
    get_btnPrint: function() { return this._btnPrint; }, set_btnPrint: function(v) { if (this._btnPrint !== v) this._btnPrint = v; },
    get_ctlMainInfo: function() { return this._ctlMainInfo; }, set_ctlMainInfo: function(v) { if (this._ctlMainInfo !== v) this._ctlMainInfo = v; },
    get_ctlLines: function() { return this._ctlLines; }, set_ctlLines: function(v) { if (this._ctlLines !== v) this._ctlLines = v; },
    get_ctlLinesDeleted: function() { return this._ctlLinesDeleted; }, set_ctlLinesDeleted: function(v) { if (this._ctlLinesDeleted !== v) this._ctlLinesDeleted = v; },
    get_lblStatus: function() { return this._lblStatus; }, set_lblStatus: function(v) { if (this._lblStatus !== v) this._lblStatus = v; },
    get_pnlStatus: function() { return this._pnlStatus; }, set_pnlStatus: function(v) { if (this._pnlStatus !== v) this._pnlStatus = v; },
    // [001] code start
    get_ctlInvoicePDF: function() { return this._ctlInvoicePDF; }, set_ctlInvoicePDF: function(v) { if (this._ctlInvoicePDF !== v) this._ctlInvoicePDF = v; },
    // [001] code end
    get_ctlInvoicePDFDragDrop: function() { return this._ctlInvoicePDFDragDrop; }, set_ctlInvoicePDFDragDrop: function(v) { if (this._ctlInvoicePDFDragDrop !== v) this._ctlInvoicePDFDragDrop = v; },

    initialize: function() {
        Rebound.GlobalTrader.Site.Pages.Orders.ClientInvoiceDetail.callBaseMethod(this, "initialize");
    },

    goInit: function() {
        if (this._btnPrint) this._btnPrint.addPrint(Function.createDelegate(this, this.printInvoice));
         if (this._btnPrint) this._btnPrint.addEmail(Function.createDelegate(this, this.emailInvoice));
         if (this._btnPrint) this._btnPrint.addExtraButtonClick(Function.createDelegate(this, this.printOtherDocs));
        if (this._ctlMainInfo) this._ctlMainInfo.addGetDataComplete(Function.createDelegate(this, this.ctlMainInfo_GetDataComplete));
        //if (this._ctlLines) this._ctlLines.addGetDeletedData(Function.createDelegate(this, this.ctlLines_GetDeletedData));
        // [001] code start
        if (this._ctlInvoicePDF) this._ctlInvoicePDF.getData();
        // [001] code end
        if (this._ctlInvoicePDFDragDrop) this._ctlInvoicePDFDragDrop.getData();
        Rebound.GlobalTrader.Site.Pages.Orders.ClientInvoiceDetail.callBaseMethod(this, "goInit");
    },

    dispose: function() {
        if (this.isDisposed) return;
        if (this._ctlMainInfo) this._ctlMainInfo.dispose();
        if (this._ctlLines) this._ctlLines.dispose();
        if (this._ctlLinesDeleted) this._ctlLinesDeleted.dispose();
        if (this._btnPrint) this._btnPrint.dispose();
        this._btnPrint = null;
        this._ctlMainInfo = null;
        this._ctlLines = null;
        this._ctlLinesDeleted = null;
        this._pnlStatus = null;
        this._lblStatus = null;
        this._intClientInvoiceID = null;
        //[001] code start
        this._ctlInvoicePDF = null;
        //[001] code end
        Rebound.GlobalTrader.Site.Pages.Orders.ClientInvoiceDetail.callBaseMethod(this, "dispose");
    },

    printInvoice: function() {
        //alert(this._intClientInvoiceID);
    $R_FN.openPrintWindow($R_ENUM$PrintObject.ClientInvoice, this._intClientInvoiceID);
    },

    emailInvoice: function() {
    $R_FN.openPrintWindow($R_ENUM$PrintObject.ClientInvoice, this._intClientInvoiceID, true);
    },


    printOtherDocs: function() {
        if (this._btnPrint._strExtraButtonClickCommand == "PrintLog") $R_FN.openPrintLogWindow($R_ENUM$PrintObject.PrintLog, this._intClientInvoiceID, false, "ClientInvoice");
    },

    ctlMainInfo_GetDataComplete: function() {
        //$R_FN.setInnerHTML(this._lblStatus, this._ctlMainInfo.getFieldValue("hidStatus"));
        this.setLineFieldsFromHeader();
        this._ctlLines.getData();
        //this._ctlLinesDeleted.getData();
    },

    ctlLines_GetDeletedData: function() {
        this._ctlLinesDeleted.getData();
    },

    setLineFieldsFromHeader: function() {
        //var strInvoiceNumber = this._ctlMainInfo.getFieldValue("hidNo");
        //var strCustomer = this._ctlMainInfo.getFieldValue("hidCompany");
        //var strCurrency = this._ctlMainInfo.getFieldValue("hidCurrencyCode");
        //var intCurrency = this._ctlMainInfo.getFieldValue("hidCurrencyNo");
        //var strDate = this._ctlMainInfo.getFieldValue("ctlDateShipped");
        if (this._ctlLines) {
            this._ctlLines._blnExported = this._ctlMainInfo._blnExported;
            this._ctlLines._intInvoiceClientNo = this._ctlMainInfo._intInvoiceClientNo;
           // alert(this._intInvoiceClientNo);
            //if (this._ctlLines._frmAdd) this._ctlLines._frmAdd.setFieldsFromHeader(strInvoiceNumber, strCustomer, strCurrency, intCurrency, strDate);
        }
    }

};
Rebound.GlobalTrader.Site.Pages.Orders.ClientInvoiceDetail.registerClass("Rebound.GlobalTrader.Site.Pages.Orders.ClientInvoiceDetail", Rebound.GlobalTrader.Site.Pages.Content, Sys.IDisposable);
