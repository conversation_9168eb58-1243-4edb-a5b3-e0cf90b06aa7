///<reference name="MicrosoftAjax.js" />
///<reference path="~/Common/Data/Data.js" />
///<reference path="~/Common/Functions/Functions.js" />
///<reference path="~/Controls/IconButton/IconButton.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - use new function to hide calendar when field is disabled to avoid recursion
//
// RP 06.01.2010:
// - add full disposing and hide calendar on disabling field
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.FilterDataItemRows");

Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.DateSelect = function(element) { 
	Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.DateSelect.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.DateSelect.prototype = {

	get_txt: function() { return this._txt; }, set_txt: function(value) { if (this._txt !== value)  this._txt = value; }, 
	get_cal: function() { return this._cal; }, set_cal: function(value) { if (this._cal !== value)  this._cal = value; }, 
	get_enmComparison: function() { return this._enmComparison; }, set_enmComparison: function(value) { if (this._enmComparison !== value)  this._enmComparison = value; }, 

	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.DateSelect.callBaseMethod(this, "initialize");
		$addHandler(this._txt, "focus", Function.createDelegate(this, this.dateSelectFocus));
		$addHandler(this._txt, "blur", Function.createDelegate(this, this.dateSelectBlur));
		this._cal.addShowCalendarEvent(Function.createDelegate(this, this.dateSelectFocus));
		this._cal.addCloseCalendarEvent(Function.createDelegate(this, this.dateSelectBlur));
		this._cal.addDateSelectEvent(Function.createDelegate(this, this.dateSelectBlur));
		this.addFieldDisabledEvent(Function.createDelegate(this, this.hideCalendarAfterDeselect));
	},

	dispose: function() { 
		if (this.isDisposed) return;
		if (this.get_element()) $clearHandlers(this.get_element());
		if (this._txt) $clearHandlers(this._txt);
		this._txt = null;
		if (this._cal) this._cal.dispose();
		this._cal = null;
		Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.DateSelect.callBaseMethod(this, "dispose");
	},
	
	dateSelectFocus: function() {
		if (this.getValue() == "") {
			this.setValue($R_FN.shortDate());
		} else {
			this.enableField(true);
		}
	},
	
	dateSelectBlur: function() {
		this.enableField($R_FN.isEntered(this._txt.value));
	},
	
	hideCalendarAfterDeselect: function() {
		this._cal.showCalendar(false, true);
	},
	
	getValue: function() {
		return this._txt.value;
	},

	setValue: function(v) {
		if (typeof(v) == "undefined" || v == null) v = "";
		if (v == "") {
			this.enableField(false);
		} else {
			switch (v.toUpperCase()) {
				case "TODAY": v = $R_FN.shortDate(); break;
				case "FIRSTDAYOFMONTH": v = $R_FN.firstDayOfMonth(); break;
				case "LASTDAYOFMONTH": v = $R_FN.lastDayOfMonth(); break;
				case "ONEWEEKAGO": v = $R_FN.oneWeekAgo(); break;				
			}
			this._txt.value = v;
			this.enableField(true);
		}
	},

	reset: function() {
		this._txt.value = "";
		this.enableField(false);
	}
	
};

Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.DateSelect.registerClass("Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.DateSelect", Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.Base, Sys.IDisposable);
