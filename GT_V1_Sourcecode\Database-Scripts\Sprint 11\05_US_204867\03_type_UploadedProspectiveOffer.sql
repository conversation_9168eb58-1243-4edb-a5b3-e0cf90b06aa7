﻿/*   
===========================================================================================  
TASK			UPDATED BY      DATE			ACTION		DESCRIPTION  
[US-204867]		An.TranTan		23-Sep-2024		Create		Create type for store temp data
===========================================================================================  
*/ 
IF TYPE_ID(N'dbo.UploadedProspectiveOffer') IS NOT NULL
BEGIN
	--drop depend SP
	IF OBJECT_ID('dbo.usp_saveProsOffer_tempData', 'P') IS NOT NULL
		DROP PROCEDURE dbo.usp_saveProsOffer_tempData
	DROP TYPE dbo.UploadedProspectiveOffer;
END
GO
CREATE TYPE dbo.UploadedProspectiveOffer AS TABLE(
	[Column1] [nvarchar](max) NULL DEFAULT (NULL),
	[Column2] [nvarchar](max) NULL DEFAULT (NULL),
	[Column3] [nvarchar](max) NULL DEFAULT (NULL),
	[Column4] [nvarchar](max) NULL DEFAULT (NULL),
	[Column5] [nvarchar](max) NULL DEFAULT (NULL),
	[Column6] [nvarchar](max) NULL DEFAULT (NULL),
	[Column7] [nvarchar](max) NULL DEFAULT (NULL),
	[Column8] [nvarchar](max) NULL DEFAULT (NULL),
	[Column9] [nvarchar](max) NULL DEFAULT (NULL),
	[Column10] [nvarchar](max) NULL DEFAULT (NULL),
	[Column11] [nvarchar](max) NULL DEFAULT (NULL),
	[LineNumber] [int] NULL
)
GO


