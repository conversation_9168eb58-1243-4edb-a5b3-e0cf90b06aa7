///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");

Rebound.GlobalTrader.Site.Controls.DropDowns.Certificate = function(element) {
    Rebound.GlobalTrader.Site.Controls.DropDowns.Certificate.initializeBase(this, [element]);
    this._intCategoryID = 0;
};

Rebound.GlobalTrader.Site.Controls.DropDowns.Certificate.prototype = {

    initialize: function() {
        Rebound.GlobalTrader.Site.Controls.DropDowns.Certificate.callBaseMethod(this, "initialize");
        this.addSetupDataCall(Function.createDelegate(this, this.setupDataCall));
        this.addGetDataOK(Function.createDelegate(this, this.dataCallOK));
    },

    dispose: function() {
        if (this.isDisposed) return;
        this._intCategoryID = null;
        Rebound.GlobalTrader.Site.Controls.DropDowns.Certificate.callBaseMethod(this, "dispose");
    },

    setupDataCall: function() {
        this._objData.set_PathToData("controls/DropDowns/Certificate");
        this._objData.set_DataObject("Certificate");
        this._objData.set_DataAction("GetData");
        this._objData.addParameter("CategoryID", this._intCategoryID);
    },

    dataCallOK: function() {
        var result = this._objData._result;
        if (result.Certificates) {
            for (var i = 0; i < result.Certificates.length; i++) {
                this.addOption(result.Certificates[i].Name, result.Certificates[i].ID);
            }
        }
    }

};

Rebound.GlobalTrader.Site.Controls.DropDowns.Certificate.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.Certificate", Rebound.GlobalTrader.Site.Controls.DropDowns.Base);
