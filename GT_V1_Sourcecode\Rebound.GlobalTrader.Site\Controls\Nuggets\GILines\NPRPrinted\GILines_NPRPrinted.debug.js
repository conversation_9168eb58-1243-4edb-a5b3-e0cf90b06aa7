///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//
// RP 12.10.2009:
// - retrofitting changes from v3.0.34 - allow selection of whether to update Stock
//   and Shipments too (task 322)
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.GILines_NPRPrinted = function(element) {
Rebound.GlobalTrader.Site.Controls.Forms.GILines_NPRPrinted.initializeBase(this, [element]);
this._intLineID = -1;
};

Rebound.GlobalTrader.Site.Controls.Forms.GILines_NPRPrinted.prototype = {

    initialize: function() {
        Rebound.GlobalTrader.Site.Controls.Forms.GILines_NPRPrinted.callBaseMethod(this, "initialize");
        this.addShown(Function.createDelegate(this, this.formShown));
        this.addSave(Function.createDelegate(this, this.saveClicked));
    },

    formShown: function() {
    
    },

    dispose: function() {
        if (this.isDisposed) return;
        this._intLineID = null;
        Rebound.GlobalTrader.Site.Controls.Forms.GILines_NPRPrinted.callBaseMethod(this, "dispose");
    },

    saveClicked: function() {
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/GILines");
        obj.set_DataObject("GILines");
        obj.set_DataAction("NPRPrinted");
        obj.addParameter("id", this._intLineID);
        obj.addParameter("NPRPrintStatus", this.getFieldValue("ctlNPRPrinted"));
        obj.addDataOK(Function.createDelegate(this, this.saveEditComplete));
        obj.addError(Function.createDelegate(this, this.saveEditError));
        obj.addTimeout(Function.createDelegate(this, this.saveEditError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    saveEditError: function(args) {
        this._strErrorMessage = args._errorMessage;
        this.onSaveError();
    },

    saveEditComplete: function(args) {
        if (args._result.Result == true) {
            this.onSaveComplete();
        } else {
            this._strErrorMessage = args._errorMessage;
            this.onSaveError();
        }
    }

};

Rebound.GlobalTrader.Site.Controls.Forms.GILines_NPRPrinted.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.GILines_NPRPrinted", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
