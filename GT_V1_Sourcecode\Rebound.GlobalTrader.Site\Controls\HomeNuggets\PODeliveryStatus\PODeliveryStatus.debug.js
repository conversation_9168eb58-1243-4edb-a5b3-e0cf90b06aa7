///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
//Marker  Date          Changed By     Remarks
//[001]   26-Sep-2018   A<PERSON><PERSON>    REB-13083: Change request PO - delivery status
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.HomeNuggets");

Rebound.GlobalTrader.Site.Controls.HomeNuggets.PODeliveryStatus = function (element) {
    Rebound.GlobalTrader.Site.Controls.HomeNuggets.PODeliveryStatus.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.HomeNuggets.PODeliveryStatus.prototype = {

    get_tblPODeliveryStatus: function () { return this._tblPODeliveryStatus; }, set_tblPODeliveryStatus: function (value) { if (this._tblPODeliveryStatus !== value) this._tblPODeliveryStatus = value; },

	initialize: function() {
	    Rebound.GlobalTrader.Site.Controls.HomeNuggets.PODeliveryStatus.callBaseMethod(this, "initialize");
		this.addRefreshEvent(Function.createDelegate(this, this.getData));
	},

	dispose: function() { 
		if (this.isDisposed) return;
		if (this._tblPODeliveryStatus) this._tblPODeliveryStatus.dispose();
		this._tblPODeliveryStatus = null;
		Rebound.GlobalTrader.Site.Controls.HomeNuggets.PODeliveryStatus.callBaseMethod(this, "dispose");
	},
	
	setupLoadingState: function() {
	    this._tblPODeliveryStatus.show(false);
	    Rebound.GlobalTrader.Site.Controls.HomeNuggets.PODeliveryStatus.callBaseMethod(this, "setupLoadingState");
	},
	
	getData: function () {
	    this.setupLoadingState();
		var obj = new Rebound.GlobalTrader.Site.Data();
		obj.set_PathToData('controls/HomeNuggets/PODeliveryStatus');
		obj.set_DataObject('PODeliveryStatus');
		obj.set_DataAction('GetData');
		obj.addDataOK(Function.createDelegate(this, this.getDataComplete));
		obj.addError(Function.createDelegate(this, this.homeNuggetDataError));
		obj.addTimeout(Function.createDelegate(this, this.homeNuggetDataError));
		$R_DQ.addToQueue(obj);
		$R_DQ.processQueue();
		obj = null;
	},
	
	getDataComplete: function (args) {
	    this.showNoneFoundOrContent(args._result.Count);
	    var result = args._result;
	    var IsPOHub = args._result.IsPOHub;
		this._tblPODeliveryStatus.clearTable();
		this._tblPODeliveryStatus.show(result.PODeliveryDetail.length > 0);
		for (var i = 0; i < result.PODeliveryDetail.length; i++) {
		    var row = result.PODeliveryDetail[i];
			var aryData = [
				    $RGT_nubButton_Company(row.CompanyId, row.CompanyName),
                    !IsPOHub && row.InternalPurchaseOrderId > 0 ? $RGT_nubButton_InternalPurchaseOrder(row.InternalPurchaseOrderId, row.InternalPurchaseOrderNumber) : $RGT_nubButton_PurchaseOrder(row.PurchaseOrderId, row.PurchaseOrderNumber),
                    row.Part,
                    row.PromiseDate
				];
			this._tblPODeliveryStatus.addRow(aryData, null);
			this._tblPODeliveryStatus.RowColor(i + 1, row.RowCSS);
        }
		this.hideLoading();
	}
};

Rebound.GlobalTrader.Site.Controls.HomeNuggets.PODeliveryStatus.registerClass("Rebound.GlobalTrader.Site.Controls.HomeNuggets.PODeliveryStatus", Rebound.GlobalTrader.Site.Controls.HomeNuggets.Base, Sys.IDisposable);
