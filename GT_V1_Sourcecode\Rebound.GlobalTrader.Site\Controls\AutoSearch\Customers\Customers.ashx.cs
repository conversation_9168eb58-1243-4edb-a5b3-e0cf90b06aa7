using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;

namespace Rebound.GlobalTrader.Site.Controls.AutoSearch.Data {

	[WebService(Namespace = "http://tempuri.org/")]
	[WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
	public class Customers : Rebound.GlobalTrader.Site.Controls.AutoSearch.Data.Base {

		protected override void GetData() {
			List<Company> lst = null;
			try {
                lst = Company.AutoSearchForCustomers(SessionManager.ClientID, GetFormValue_StringForNameSearch("search"));
				OutputCompanyList(lst);
			} catch (Exception e) {
				WriteError(e);
			} finally {
				lst = null;
			}
			base.GetData();
		}

	}
}