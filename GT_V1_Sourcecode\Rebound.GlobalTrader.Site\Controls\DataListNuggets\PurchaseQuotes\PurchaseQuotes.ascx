<%@ Control Language="C#" CodeBehind="PurchaseQuotes.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.DataListNuggets.PurchaseQuotes" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Nugget:DesignBase ID="ctlDB" runat="server" BoxType="List">
	<Filters>
		<ReboundUI_DataListNugget:Filter ID="ctlFilter" runat="server">
			<FieldsLeft>
				<ReboundUI_FilterDataItemRow:Numerical id="ctlPOQuoteNo" runat="server" ResourceTitle="POQuoteNo" FilterField="POQuoteNo" />
				<ReboundUI_FilterDataItemRow:TextBox id="ctlPart" runat="server" ResourceTitle="PartNo" FilterField="Part" />
		        <ReboundUI_FilterDataItemRow:CheckBox id="ctlRecentOnly" runat="server" ResourceTitle="RecentOnly" FilterField="RecentOnly" DefaultValue="true" />
		        <%--<ReboundUI_FilterDataItemRow:CheckBox id="ctlIncludeClosed" runat="server" ResourceTitle="IncludeClosed" FilterField="IncludeClosed" />--%>
				
			</FieldsLeft>
			<FieldsRight>
				<%--<ReboundUI_FilterDataItemRow:TextBox id="ctlContactName" runat="server" ResourceTitle="ContactName" FilterField="Contact" />--%>
				<ReboundUI_FilterDataItemRow:TextBox id="ctlCompanyName" runat="server" ResourceTitle="CompanyName" FilterField="CMName" />
				<ReboundUI_FilterDataItemRow:DateSelect id="ctlDatePOQuotedFrom" runat="server" ResourceTitle="DateFrom" FilterField="DatePOQuotedFrom" />
				<ReboundUI_FilterDataItemRow:DateSelect id="ctlDatePOQuotedTo" runat="server" ResourceTitle="DateTo" FilterField="DatePOQuotedTo" />
				<ReboundUI_FilterDataItemRow:DropDown id="ctlSalesmanName" runat="server" ResourceTitle="Buyer" DropDownType="PoHubBuyer" DropDownAssembly="Rebound.GlobalTrader.Site" FilterField="Salesman" />
			</FieldsRight>
		</ReboundUI_DataListNugget:Filter>
	</Filters>
</ReboundUI_Nugget:DesignBase>
