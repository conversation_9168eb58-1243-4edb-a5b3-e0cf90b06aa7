﻿
GO

/****** Object:  StoredProcedure [dbo].[usp_select_Company_SalesInfo]    Script Date: 1/8/2025 7:26:00 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO



    
CREATE OR ALTER PROCEDURE  [dbo].[usp_select_Company_SalesInfo]           
--******************************************************************************************                  
--* Gets the Sales info for a company, checking that tax and currency are valid                  
--* (through table joins)                  
--*                   
--* RP 12.01.2010:                  
--* - get exchange rate                  
--*                   
--* RP 17.11.2009:                  
--* - new proc         
--*[001] Modify By Suhail on 02/05/2018   Added Credit Limit2                     
--******************************************************************************************                  
    @CompanyId int                  
AS                   
    SELECT  co.CompanyId                  
          , co.Salesman                  
          , lg.EmployeeName AS SalesmanName                  
          , ISNULL(co.SOApproved,0)   AS SOApproved               
          , cu.CurrencyId AS SOCurrencyNo                  
          , cu.CurrencyCode AS SOCurrencyCode                  
          , cu.CurrencyDescription AS SOCurrencyDescription                  
          , co.CustomerCode                  
          , co.SORating                  
          , co.OnStop                  
          , tx.TaxId AS SOTaxNo                  
          , tx.TaxName AS SOTaxName                  
          , tm.TermsId AS SOTermsNo                  
          , tm.TermsName AS SOTermsName                  
          , co.ShippingCharge                  
          , sv.ShipViaId AS DefaultSalesShipViaNo                  
          , sv.ShipViaName AS DefaultSalesShipViaName                  
          , isnull(sv.Cost, 0) AS DefaultSalesShippingCost                  
          , isnull(sv.Charge, 0) AS DefaultSalesFreightCharge                  
          , co.DefaultSalesShipViaAccount                  
          , co.CreditLimit                  
          , co.CurrentMonth                  
          , co.Balance                  
          , co.Days30                  
          , co.Days60                  
          , co.Days90                  
          , co.Days120                  
          , cn.ContactId AS DefaultSOContactNo                  
          , cn.ContactName AS DefaultSOContactName                  
          , co.DLUP                  
          , dbo.ufn_get_exchange_rate(cu.CurrencyId, getdate()) AS ExchangeRate               
          , co.CompanyName             
          , co.UpdatedBy            
          , co.SOApprovedBy            
          , co.SOApprovedDate         
          ,  ct.IsTraceability           
    , co.InsuranceFileNo         
    , co.InsuredAmount       
     , co.CreditStatus      
  , cu.GlobalCurrencyNo     
  ,co.NotesToInvoice    
   --[001] Start Code    
  , co.ActualCreditLimit         
   --[001] End Code      
   , co.Days1     
   ,ISNULL(W.WarehouseName,'') AS    WarehouseName     
   ,ISNULL(W.WarehouseId,0) AS    WarehouseId    
   , cur.CurrencyId AS InsuredAmountCurrencyNo  
   , cur.CurrencyCode AS InsuredAmountCurrencyCode   
   , cur.CurrencyDescription AS InsuredAmountCurrencyDescription   
    FROM    tbCompany co                  
    LEFT JOIN dbo.tbCurrency cu ON co.SOCurrencyNo = cu.CurrencyId                  
                                   AND cu.Sell = 1                  
                                   AND NOT cu.Inactive = 1                  
    LEFT JOIN dbo.tbShipVia sv ON co.DefaultSalesShipViaNo = sv.ShipViaId                  
    LEFT JOIN dbo.tbTax tx ON co.SOTaxNo = tx.TaxId                  
                              AND NOT tx.Inactive = 1                  
    LEFT JOIN dbo.tbTerms tm ON co.SOTermsNo = tm.TermsId                  
                                AND NOT tm.Inactive = 1                  
    LEFT JOIN dbo.tbContact cn ON co.DefaultSOContactNo = cn.ContactId                  
                                  AND NOT cn.Inactive = 1                  
    LEFT JOIN dbo.tbLogin lg ON co.Salesman = lg.LoginId      
    Left JOIN dbo.tbCompanyType ct on ct.CompanyTypeId=co.TypeNo      
    LEFT JOIN dbo.tbWarehouse w ON W.WarehouseId = co.WarehouseNo   
 LEFT JOIN dbo.tbCurrency cur ON co.InsuredAmountCurrencyNo = cur.CurrencyId                  
                                   AND cur.Sell = 1                  
                                   AND NOT cur.Inactive = 1            
    WHERE   CompanyId = @CompanyId     
    


GO


