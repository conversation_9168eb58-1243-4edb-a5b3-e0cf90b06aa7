﻿
GO

CREATE OR ALTER PROCEDURE [dbo].[usp_SaveXMatchImportData] 
@UserId int,            
@ClientId int,            
@list_Label_name nvarchar(max),            
@list_column_name nvarchar(max),            
@insertDataList nvarchar(max),            
@fileColName nvarchar(100),            
            
            
@RecordCount int OUTPUT,            
@ErrorMessage varchar(max) OUTPUT,            
@IsDelExistingData bit            
AS            
BEGIN            
  SET NOCOUNT ON;            
  SET @ErrorMessage = NULL            
  SET @RecordCount = 0     
       
  declare @counterVar int  = 1    
       
  --[001]    
  truncate table tbTmpUtilityTable     
            
  BEGIN TRY       
           
    BEGIN TRANSACTION [tranSaveXMatchImportData]     
            
      -------------------Update--tbTempXMatchDa--Command----------Start-----------------------------------------------------------                                                                        
      DECLARE @Label_name varchar(max),            
              @column_name varchar(max)            
      DECLARE cursor_SaveXMatchData CURSOR FOR            
      WITH T1 AS (SELECT  val AS Label_name,ROW_NUMBER() OVER (ORDER BY (SELECT 1)) AS ID            
      FROM BorisGlobalTrader.dbo.[SplitString](@list_Label_name, ',')),            
      T2 AS (SELECT val AS Column_name,ROW_NUMBER() OVER (ORDER BY (SELECT 1)) AS ID            
      FROM BorisGlobalTrader.dbo.[SplitString](@list_column_name, ','))            
             
      SELECT            
        T1.Label_name,            
        T2.Column_name            
      FROM T1            
      FULL JOIN T2 ON (T1.ID = T2.ID)     
           
      OPEN cursor_SaveXMatchData;            
      FETCH NEXT FROM cursor_SaveXMatchData INTO            
      @Label_name, @column_name     
           
      WHILE @@FETCH_STATUS = 0            
      BEGIN      
       
  insert into tbTmpUtilityTable(Value1,Value2,rwStatus,lineNumber,insertedby,clientid,utilityname)                    
  select @Label_name,@column_name,0,@counterVar,@UserId,@ClientId,'XMatch'    
         --print 'start'    
    
        DECLARE @strScript nvarchar(max)            
        SET @strScript = ''            
        IF @Label_name = 'XPN'            
        BEGIN            
          SET @strScript = 'update BorisGlobalTraderimports.dbo.tbTempXMatchData set ' + @column_name + '=' + '' +            
          '(case    when len( ' + @column_name + ')>50 then dbo.stripAlphahnumeric(SUBSTRING(' + @column_name + ',0, 50))                                                     
              else dbo.stripAlphahnumeric( ' + @column_name + ' )  end)' + '' + ' WHERE  ClientId= ' + CONVERT(nvarchar(10), @ClientId)            
          + '  and CreatedBy= ' + CONVERT(nvarchar(10), @UserId) + ''            
          EXEC (@strScript)          
    --Print '1'      
        END            
        IF @Label_name = 'XQty'            
        BEGIN            
 --         SET @strScript = 'update BorisGlobalTraderimports.dbo.tbTempXMatchData set ' + @column_name + '=' + '' +            
 --         '(case    when len( ' + @column_name + ')>30 then dbo.stripAlphahnumeric(SUBSTRING(' + @column_name + ',0, 30))                                                     
 --else dbo.stripAlphahnumeric( ' + @column_name + ' )  end)' + '' + ' WHERE  ClientId= ' + CONVERT(nvarchar(10), @ClientId)            
 --         + '  and CreatedBy= ' + CONVERT(nvarchar(10), @UserId) + ''     
    -- [001]    
    SET @strScript = 'update BorisGlobalTraderimports.dbo.tbTempXMatchData set ' + @column_name + '=' + '' +            
          'cast(' + @column_name + ' as int)' + '' + ' WHERE  ClientId= ' + CONVERT(nvarchar(10), @ClientId)            
          + '  and CreatedBy= ' + CONVERT(nvarchar(10), @UserId) + ''       
               
          EXEC (@strScript)     
    --Print '2'            
        END            
        IF @Label_name = 'XDC'            
    BEGIN            
          SET @strScript = 'update BorisGlobalTraderimports.dbo.tbTempXMatchData set ' + @column_name + '=' + '' +            
          '(case    when len( ' + @column_name + ')>30 then dbo.stripAlphahnumeric(SUBSTRING(' + @column_name + ',0, 30))                                                     
 else dbo.stripAlphahnumeric( ' + @column_name + ' )  end)' + '' + ' WHERE  ClientId= ' + CONVERT(nvarchar(10), @ClientId)            
          + '  and CreatedBy= ' + CONVERT(nvarchar(10), @UserId) + ''            
          EXEC (@strScript)     
    --Print '3'            
        END            
            
  ---[001]    
  update tbTmpUtilityTable set rwStatus = 1 where Value1 = @Label_name and Value2 = @column_name and clientid = @ClientId and insertedby = @UserId        
  and utilityname = 'XMatch'           
          
  ---[001] increment @counterVar to +1 to set the line number to match with the row data of excel uploaded          
  SET @counterVar = @counterVar + 1;     
            
        FETCH NEXT FROM cursor_SaveXMatchData INTO            
        @Label_name, @column_name            
      END;            
      CLOSE cursor_SaveXMatchData;            
      DEALLOCATE cursor_SaveXMatchData;    
       
   --Print '4'             
      -------------------Update--tbTempXMatchDa--Command----------end-----------------------------------------------------------                      
      IF (@IsDelExistingData = 1)            
      BEGIN            
        DELETE FROM BorisGlobalTraderimports.dbo.XMatchExcess            
        WHERE UserId = @UserId            
          AND ClientId = @ClientId            
      END                         
      
      DECLARE @DynamicQuery nvarchar(max)            
      SET @DynamicQuery = 'insert  into BorisGlobalTraderimports.dbo.XMatchExcess (UserId,ClientId,' +            
      @list_Label_name + ') ' + ' select ' + CONVERT(nvarchar(10), @UserId) + ',' + CONVERT(nvarchar(10), @ClientId) + ',' +            
  @insertDataList + ' ' + ' from BorisGlobalTraderimports.dbo.tbTempXMatchData ' + 'WHERE  ClientId=' + CONVERT(nvarchar(10),            
      @ClientId) + '  and CreatedBy=' + CONVERT(nvarchar(10), @UserId)            
          
   --Print '5'     
   --print @DynamicQuery    
   EXEC (@DynamicQuery)     
       
   --Print '6'            
      SET @RecordCount = @@ROWCOUNT            
            
      UPDATE BorisGlobalTraderimports.dbo.XMatchExcess            
      SET FullPN = (select [dbo].[ufn_get_fullpart] (XPN)),            
         BASEPN = dbo.ufn_get_basepart(XPN) --RIGHT(XPN, case when PATINDEX('%[^0-9]%', REVERSE(XPN))>0 then  PATINDEX('%[^0-9]%', REVERSE(XPN)) - 1 else '' end)          
      WHERE UserId = @UserId            
      AND ClientId = @ClientId       
         
   --Print '7'     
   -------------------------Import History------------------------------------       
 declare  @RowCount int = 0             
 declare  @OriginalFilename nvarchar(200)= null     
             
      select  @RowCount=count(*),@OriginalFilename=OriginalFilename from  BorisGlobalTraderimports.dbo.tbTempXMatchData       
      WHERE CreatedBy = @UserId  AND ClientId = @ClientId  group by OriginalFilename          
      insert into BorisGlobalTraderImports.dbo.tbUtilityLog (FileName,UtilityType,Clientid,LoginNo,DLUP,iRowCount)       
      values (@OriginalFilename,3,@ClientId,@UserID,getdate(),@RowCount)            
     --------------------------Import History End--------------------------------     
      
  --print '8'    
      
 truncate table tbTmpUtilityTable;              
    COMMIT TRANSACTION [tranSaveXMatchImportData]            
  END TRY            
  BEGIN CATCH            
    --SET @RecordCount = 0            
    --SELECT @ErrorMessage = ERROR_MESSAGE();      
 declare @curStatus smallint    
 SET @curStatus = Cursor_Status('global', 'cursor_SaveXMatchData'); --set it to LOCAL above, if using global above change here too    
 IF @curStatus >= 0    
 BEGIN    
   CLOSE cursor_SaveXMatchData;    
   DEALLOCATE cursor_SaveXMatchData;    
 END    
 ELSE IF @curStatus = -1 --may have been closed already so just deallocate    
 BEGIN    
   DEALLOCATE cursor_SaveXMatchData;    
 END;    
    
 print ERROR_MESSAGE()     
 DECLARE @LabelText1 varchar(100);                     
 DECLARE @ColumnText1 varchar(100);                     
          
 Select @LabelText1 = Value1,@ColumnText1 = Value2 from tbTmpUtilityTable where RwID = (Select min(RwID) from tbTmpUtilityTable where rwStatus = 0 and insertedby = @UserId and utilityname = 'XMatch');                                  
                    
 DECLARE @msg NVARCHAR(2048) = Replace(FORMATMESSAGE('Please check column %s for data format issues.', @LabelText1),'.',',');                       
        
 --truncate table tbTmpUtilityTable;                              
 Rollback TRANSACTION tranSaveXMatchImportData;                     
        
 SET @RecordCount = 0            
    SELECT @ErrorMessage = @msg;     
    
 --THROW 60000, @msg, 1;     
         
    --ROLLBACK TRANSACTION [tranSaveXMatchImportData]            
  END CATCH            
END 
GO


