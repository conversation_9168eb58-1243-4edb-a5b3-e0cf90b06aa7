///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Pages.Setup");

Rebound.GlobalTrader.Site.Pages.Setup.CD_Product = function(el) { 
	Rebound.GlobalTrader.Site.Pages.Setup.CD_Product.initializeBase(this, [el]);
};

Rebound.GlobalTrader.Site.Pages.Setup.CD_Product.prototype = {

	get_ctlProduct: function() { return this._ctlProduct; }, 	set_ctlProduct: function(v) { if (this._ctlProduct !== v)  this._ctlProduct = v; }, 
	get_ctlRateHistory: function() { return this._ctlRateHistory; }, 	set_ctlRateHistory: function(v) { if (this._ctlRateHistory !== v)  this._ctlRateHistory = v; }, 

	initialize: function() {
		Rebound.GlobalTrader.Site.Pages.Setup.CD_Product.callBaseMethod(this, "initialize");
	},
	
	goInit: function() {
		this._ctlProduct.addSelectProduct(Function.createDelegate(this, this.ctlProduct_SelectProduct));
		Rebound.GlobalTrader.Site.Pages.Setup.CD_Product.callBaseMethod(this, "goInit");
	},

	dispose: function() { 
		if (this.isDisposed) return;
		if (this._ctlProduct) this._ctlProduct.dispose();
		if (this._ctlRateHistory) this._ctlRateHistory.dispose();
		this._ctlProduct = null;
		this._ctlRateHistory = null;
		Rebound.GlobalTrader.Site.Pages.Setup.CD_Product.callBaseMethod(this, "dispose");
	},
	
	ctlProduct_SelectProduct: function() {
		this._ctlRateHistory._intProductID = this._ctlProduct._intProductID;
		this._ctlRateHistory.show(true);
		this._ctlProduct._tbl.resizeColumns();
		this._ctlRateHistory.refresh();
	}
	
};

Rebound.GlobalTrader.Site.Pages.Setup.CD_Product.registerClass("Rebound.GlobalTrader.Site.Pages.Setup.CD_Product", Rebound.GlobalTrader.Site.Pages.Content, Sys.IDisposable);
