using System;
using System.Text;
using System.Collections.Generic;

namespace Rebound.GlobalTrader.Site {
	public class JsonObject : IDisposable {

		private List<JsonVariable> _lstVariables;
		public List<JsonVariable> Variables {
			get { return _lstVariables; }
		}

		/// <summary>
		/// String result of variables
		/// </summary>
		public string Result {
			get {
				StringBuilder sb = new StringBuilder();
				sb.Append((_blnIsArray) ? "[" : "{");
				for (int i = 0; i < _lstVariables.Count; i++) {
					if (i > 0) sb.Append(",");
					if (_blnIsArray) {
						sb.Append(_lstVariables[i].Value);
					} else {
						sb.AppendFormat("{0}:{1}", _lstVariables[i].VariableName, _lstVariables[i].Value);
					}
				}
				sb.Append((_blnIsArray) ? "]" : "}");
				return sb.ToString();
			}
		}

		private bool _blnIsArray = false;
		public bool IsArray {
			get { return _blnIsArray; }
			set { _blnIsArray = value; }
		}

		public JsonObject() : this(false) { }

		public JsonObject(bool blnIsArray) {
			_lstVariables = new List<JsonVariable>();
			IsArray = blnIsArray;
		}

		public void AddVariable(object obj) { AddVariable("", obj); }
        public void AddVariableNew(object obj) { AddVariableNew("", obj); }

        public void AddVariable(string strName, object obj) {
			if (obj is string) AddVariable(strName, obj, "");
			if (obj is int) AddVariable(strName, obj, 0);
			if (obj is float) AddVariable(strName, obj, 0);
			if (obj is decimal) AddVariable(strName, obj, 0);
			if (obj is double) AddVariable(strName, obj, 0);
			if (obj is byte) AddVariable(strName, obj, 0);
			if (obj is JsonObject) AddVariable(strName, obj, null);
			if (obj is DateTime) AddVariable(strName, obj, null);
			if (obj is bool) AddVariable(strName, obj, false);
		}

        public void AddVariableNew(string strName, object obj)
        {
            if (obj is string) AddVariableNew(strName, obj, "");
            if (obj is int) AddVariableNew(strName, obj, 0);
            if (obj is float) AddVariableNew(strName, obj, 0);
            if (obj is decimal) AddVariableNew(strName, obj, 0);
            if (obj is double) AddVariableNew(strName, obj, 0);
            if (obj is byte) AddVariableNew(strName, obj, 0);
            if (obj is JsonObject) AddVariableNew(strName, obj, null);
            if (obj is DateTime) AddVariableNew(strName, obj, null);
            if (obj is bool) AddVariableNew(strName, obj, false);
        }
        public void AddVariableBackSlash(string strName, object obj)
        {
            if (obj is string) AddVariableBackSlash(strName, obj, "");
        }
        public void AddVariableBackSlash(string strName, object obj, object objValueIfNull)
        {
            string strValue = "";
            if (obj == null && objValueIfNull != null) strValue = objValueIfNull.ToString();
            if (obj is string) strValue = string.Format(@"""{0}""", FormatStringForJsonBackSlash(obj.ToString()));
            _lstVariables.Add(new JsonVariable(strName, obj, strValue));
        }


		public void AddVariable(string strName, object obj, object objValueIfNull) {
			string strValue = "";
			if (obj == null && objValueIfNull != null) strValue = objValueIfNull.ToString();
			if (obj is string) strValue = string.Format(@"""{0}""", FormatStringForJson(obj.ToString()));
			if (obj is int) strValue = obj.ToString();
			if (obj is float) strValue = obj.ToString();
			if (obj is decimal) strValue = obj.ToString();
			if (obj is double) strValue = obj.ToString();
			if (obj is byte) strValue = obj.ToString();
			if (obj is JsonObject) strValue = ((JsonObject)obj).Result;
			if (obj is DateTime) {
				long lngTicks = (((DateTime)obj).Ticks - DateTime.Parse("01/01/1970 01:00:00").Ticks) / 10000;
				strValue = string.Format(@"""\/Date({0})\/""", lngTicks);
			}
			if (obj is bool) strValue = ((bool)obj).ToString().ToLower();
			_lstVariables.Add(new JsonVariable(strName, obj, strValue));
		}

        public void AddVariableNew(string strName, object obj, object objValueIfNull)
        {
            string strValue = "";
            if (obj == null && objValueIfNull != null) strValue = objValueIfNull.ToString();
            if (obj is string) strValue = string.Format(@"""{0}""", FormatStringForJson(obj.ToString()));
            if (obj is int) strValue = obj.ToString();
            if (obj is float) strValue = obj.ToString();
            if (obj is decimal) strValue = obj.ToString();
            if (obj is double) strValue = obj.ToString();
            if (obj is byte) strValue = obj.ToString();
            if (obj is JsonObject) strValue = ((JsonObject)obj).Result;
            if (obj is DateTime)
            {
                long lngTicks = (((DateTime)obj).Ticks - DateTime.Parse("01/01/1970 01:00:00").Ticks) / 10000;
                strValue = string.Format(@"""\/Date({0})\/""", lngTicks);
            }
            if (obj is bool) strValue = ((bool)obj).ToString().ToLower();
            strName = string.Format(@"""{0}""", strName);
            _lstVariables.Add(new JsonVariable(strName, obj, strValue));
        }

        /// <summary>
        /// Formats a string for JSON output
        /// </summary>
        /// <param name="str"></param>
        /// <returns></returns>
        private string FormatStringForJson(string str) {
			str = str.Replace("\r\n", "<br>");
			str = str.Replace(@"""", @":QUOTE:");
			str = str.Replace(@"+", @":PLUS:");
			str = str.Replace(@"&", @":AND:");
			str = str.Replace(@"\", @"/");
			return str;
		}
        private string FormatStringForJsonBackSlash(string str)
        {
            str = str.Replace("\r\n", "<br>");
            str = str.Replace(@"""", @":QUOTE:");
            str = str.Replace(@"+", @":PLUS:");
            str = str.Replace(@"&", @":AND:");
            str = str.Replace(@"\", @":BACKSLASH:");
            return str;
        }


		public JsonVariable GetVariable(string strVarName) {
			JsonVariable objOut = null;
			foreach (JsonVariable jsnVar in _lstVariables) {
				if (jsnVar.VariableName.ToUpper() == strVarName.ToUpper()) {
					objOut = jsnVar;
					break;
				}
			}
			return objOut;
		}

		public int GetVariableInt(string strVarName) {
			JsonVariable obj = GetVariable(strVarName);
			if (obj == null) {
				return 0;
			} else {
				return Convert.ToInt32(obj.RawValue);
			}
		}
		public int? GetVariableNullableInt(string strVarName) {
			JsonVariable obj = GetVariable(strVarName);
			if (obj == null) {
				return null;
			} else {
				return (int?)(obj.RawValue);
			}
		}

		public double GetVariableDouble(string strVarName) {
			JsonVariable obj = GetVariable(strVarName);
			if (obj == null) {
				return 0;
			} else {
				return Convert.ToDouble(obj.RawValue);
			}
		}

		public double? GetVariableNullableDouble(string strVarName) {
			JsonVariable obj = GetVariable(strVarName);
			if (obj == null) {
				return null;
			} else {
				return (double?)(obj.RawValue);
			}
		}

		public string GetVariableString(string strVarName) {
			JsonVariable obj = GetVariable(strVarName);
			if (obj == null) {
				return null;
			} else {
				return obj.RawValue.ToString();
			}
		}

		public bool VariableHasIntValueNotZero(string strVariableName) {
			bool blnReturn = false;
			JsonVariable jsnVar = GetVariable(strVariableName);
			if (jsnVar != null) {
				int intValue = 0;
				if (int.TryParse(jsnVar.RawValue.ToString(), out intValue)) {
					blnReturn = intValue > 0;
				}
			}
			return blnReturn;
		}

		public JsonObject GetJsonObjectVariable(string strVarName) {
			JsonObject objOut = null;
			foreach (JsonVariable jsnVar in _lstVariables) {
				if (jsnVar.VariableName.ToUpper() == strVarName.ToUpper() && (jsnVar.RawValue is JsonObject)) {
					objOut = (JsonObject)jsnVar.RawValue;
					break;
				}
			}
			return objOut;
		}

		#region IDisposable Members

		bool blnIsDisposed = false;
		protected void Dispose(bool blnDisposing) {
			if (!blnIsDisposed) {
				if (blnDisposing) {
					_lstVariables.Clear();
					_lstVariables = null;
				}
			}
			blnIsDisposed = true;
		}

		public void Dispose() {
			Dispose(true);
			GC.SuppressFinalize(this);
		}

		~JsonObject() {
			Dispose(false);
		}
		#endregion
	}

	public class JsonVariable {
		private string _strValue;
		public string Value {
			get { return _strValue; }
			set { _strValue = value; }
		}
		private object _objRawValue;
		public object RawValue {
			get { return _objRawValue; }
			set { _objRawValue = value; }
		}
		private string _strVariableName;
		public string VariableName {
			get { return _strVariableName; }
			set { _strVariableName = value; }
		}
		public JsonVariable(string strName, object objRawValue, string strValue) {
			_strVariableName = strName;
			_objRawValue = objRawValue;
			_strValue = strValue;
		}
	}
}