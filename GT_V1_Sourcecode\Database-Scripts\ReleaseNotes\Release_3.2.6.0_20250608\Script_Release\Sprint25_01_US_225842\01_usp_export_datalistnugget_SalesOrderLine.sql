﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

/* 
===========================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-225842]		An.TranTan			31-Mar-2025		CREATE			Export to Excel in SalesOrder List screen
===========================================================================================
*/
CREATE OR ALTER PROCEDURE [dbo].[usp_export_datalistnugget_SalesOrderLine] 
	@ClientId INT
	,@TeamId INT = NULL
	,@DivisionId INT = NULL
	,@LoginId INT = NULL
	,@OrderBy INT = 1
	,@SortDir INT = 1
	--, @PageIndex int = 0                                
	--, @PageSize int = 10                                
	,@PartSearch NVARCHAR(50) = NULL
	,@ContactSearch NVARCHAR(50) = NULL
	,@CountrySearch INT = NULL
	,@CMSearch NVARCHAR(50) = NULL
	,@SalesmanNo INT = NULL
	,@CustomerPOSearch NVARCHAR(50) = NULL
	,@RecentOnly BIT = 1
	,@IncludeClosed BIT = 0
	,@SalesOrderNoLo INT = NULL
	,@SalesOrderNoHi INT = NULL
	,@DateOrderedFrom DATETIME = NULL
	,@DateOrderedTo DATETIME = NULL
	,@DatePromisedFrom DATETIME = NULL
	,@DatePromisedTo DATETIME = NULL
	--,@UnauthorisedOnly BIT = 0
	--, @IncludeSentOrder bit = 0                              
	,@IncludeSentOrder INT = NULL
	,@ContractNo NVARCHAR(100) = NULL
	,@IsGlobalLogin BIT = 0
	,@ClientSearch INT = NULL
	,@SOCheckedStatus INT = NULL
	,@SOStatus INT = NULL
	,@AS6081 BIT = NULL --[002]        
	,@SelectedLoginId INT = NULL
AS
BEGIN

	DECLARE @RecentDate DATETIME
		,@StartPage INT
		,@EndPage INT
	
	SET @RecentDate = dbo.ufn_get_date_from_datetime(dateadd(mm, - 3, getdate()))
	                              
	IF (NOT @DateOrderedFrom IS NULL)
		SET @DateOrderedFrom = dbo.ufn_get_start_of_day_for_date(@DateOrderedFrom)
	
	IF (NOT @DateOrderedTo IS NULL)
		SET @DateOrderedTo = dbo.ufn_get_end_of_day_for_date(@DateOrderedTo)
	
	IF (NOT @DatePromisedFrom IS NULL)
		SET @DatePromisedFrom = dbo.ufn_get_start_of_day_for_date(@DatePromisedFrom)
	
	IF (NOT @DatePromisedTo IS NULL)
		SET @DatePromisedTo = dbo.ufn_get_end_of_day_for_date(@DatePromisedTo)
	
	IF (NOT @LoginId IS NULL)
		SET @SalesmanNo = NULL
	
	DECLARE @DynamicSQL NVARCHAR(MAX) = N'', 
		@OrderClause NVARCHAR(100) = N'';
	
	SET @OrderClause = CASE WHEN @OrderBy = 1 THEN 'so.SalesOrderNumber'--'so.SalesOrderNumber'
						 WHEN @OrderBy = 2 THEN 'sol.FullPart'
						 WHEN @OrderBy = 3 THEN 'sol.Quantity'
						 WHEN @OrderBy = 4 THEN 'co.CompanyName'
						 WHEN @OrderBy = 5 THEN 'so.DateOrdered'
						 WHEN @OrderBy = 6 THEN 'sol.DatePromised'
						 WHEN @OrderBy = 7 THEN 'sol.ContractNo'
						END;
	SET @OrderClause = @OrderClause + ' ' + IIF(@SortDir = 2, ' DESC', 'ASC');
	
	SET @DynamicSQL = N'
		SELECT
			sol.SalesOrderLineId
			,so.SalesOrderNumber
			,sol.Part
			,so.CustomerPO
			,sol.Quantity AS QuantityOrdered
			,IsNull((
					SELECT Sum(IsNull(ila.Quantity, 0))
					FROM dbo.tbInvoicelineAllocation ila
					WHERE sol.SalesOrderLineId = ila.SalesOrderLineNo
			), 0) AS QuantityShipped
			,(
				SELECT sum(isnull(QuantityInStock, 0))
				FROM tbStock stk
				JOIN dbo.tbAllocation al ON al.StockNo = stk.StockId
				WHERE sol.SalesOrderLineId = al.SalesOrderLineNo
			) AS QuantityInStock
			,co.CompanyName
			,cn.ContactName
			,so.DateOrdered
			,sol.DatePromised
			,dbo.ufn_get_salesOrder_statusNo(so.SalesOrderId) AS SalesOrderStatusNo
			,sol.ContractNo
			,ROW_NUMBER() OVER (ORDER BY ' + @OrderClause + ') AS RowNum
		FROM dbo.tbSalesOrder so WITH(NOLOCK)
		LEFT JOIN dbo.tbSalesOrderLine sol WITH(NOLOCK) ON sol.SalesOrderNo = so.SalesOrderId
		LEFT JOIN dbo.tbCompany co WITH(NOLOCK) ON so.CompanyNo = co.CompanyId
		LEFT JOIN dbo.tbContact cn WITH(NOLOCK) ON so.ContactNo = cn.ContactId
		LEFT JOIN dbo.tbLogin lg WITH(NOLOCK) ON lg.LoginId = so.Salesman
		LEFT JOIN dbo.tbManufacturer mf WITH(NOLOCK) ON sol.ManufacturerNo = mf.ManufacturerId
		JOIN dbo.tbAddress ad WITH(NOLOCK) ON so.ShipToAddressNo = ad.AddressID
		LEFT OUTER JOIN tbStock stk WITH(NOLOCK) ON stk.StockId = sol.StockNo
		WHERE so.Closed IN (0, @IncludeClosed) 
			AND ISNULL(sol.Closed,0) IN (0, @IncludeClosed)
			AND ISNULL(sol.Inactive,0) = 0';
	
	IF(@IsGlobalLogin = 0 AND @ClientSearch IS NULL)
	BEGIN
		SET @DynamicSQL = @DynamicSQL + N' AND so.ClientNo = @ClientId';
	END
	ELSE IF(@IsGlobalLogin = 0 AND @ClientSearch IS NOT NULL)
	BEGIN
		SET @DynamicSQL = @DynamicSQL + N' AND so.ClientNo = @ClientSearch';
	END
	IF(@TeamId IS NOT NULL)
	BEGIN
		SET @DynamicSQL = @DynamicSQL + N' AND lg.TeamNo = @TeamId';
	END
	IF(@DivisionId IS NOT NULL)
	BEGIN
		SET @DynamicSQL = @DynamicSQL + N' AND lg.DivisionNo = @DivisionId';
	END
	IF(@LoginId IS NOT NULL)
	BEGIN
		SET @DynamicSQL = @DynamicSQL + N' AND (so.Salesman = @LoginId OR (so.Salesman2 = @LoginId AND so.Salesman2Percent > 0))';
	END
	IF(@RecentOnly = 1)
	BEGIN
		SET @DynamicSQL = @DynamicSQL + N' AND so.DateOrdered >= @RecentDate';
	END
	IF(@SalesOrderNoLo IS NOT NULL)
	BEGIN
		SET @DynamicSQL = @DynamicSQL + N' AND SalesOrderNumber >= @SalesOrderNoLo';
	END
	IF(@SalesOrderNoHi IS NOT NULL)
	BEGIN
		SET @DynamicSQL = @DynamicSQL + N' AND SalesOrderNumber <= @SalesOrderNoHi';
	END
	IF(@DateOrderedFrom IS NOT NULL)
	BEGIN
		SET @DynamicSQL = @DynamicSQL + N' AND DateOrdered >= @DateOrderedFrom';
	END
	IF(@DateOrderedTo IS NOT NULL)
	BEGIN
		SET @DynamicSQL = @DynamicSQL + N' AND DateOrdered <= @DateOrderedTo';
	END
	IF(@DatePromisedFrom IS NOT NULL)
	BEGIN
		SET @DynamicSQL = @DynamicSQL + N' AND DatePromised >= @DatePromisedFrom';
	END
	IF(@DatePromisedTo IS NOT NULL)
	BEGIN
		SET @DynamicSQL = @DynamicSQL + N' AND DatePromised <= @DatePromisedTo';
	END
	IF(@ContactSearch IS NOT NULL)
	BEGIN
		SET @DynamicSQL = @DynamicSQL + N' AND ContactName LIKE @ContactSearch';
	END
	IF(@CountrySearch IS NOT NULL)
	BEGIN
		SET @DynamicSQL = @DynamicSQL + N' AND ad.CountryNo = @CountrySearch';
	END
	IF(@CMSearch IS NOT NULL)
	BEGIN
		SET @DynamicSQL = @DynamicSQL + N' AND co.FullName LIKE @CMSearch';
	END
	IF(@SalesmanNo IS NOT NULL)
	BEGIN
		SET @DynamicSQL = @DynamicSQL + N' AND (so.Salesman = @SalesmanNo OR (so.Salesman2 = @SalesmanNo AND so.Salesman2Percent > 0))';
	END
	IF(@CustomerPOSearch IS NOT NULL)
	BEGIN
		SET @DynamicSQL = @DynamicSQL + N' AND CustomerPO LIKE @CustomerPOSearch';
	END
	IF(@PartSearch IS NOT NULL)
	BEGIN
		SET @DynamicSQL = @DynamicSQL + N' AND (sol.FullPart LIKE @PartSearch OR FullCustomerPart LIKE @PartSearch)';
	END
	IF(ISNULL(@IncludeSentOrder,0) = 1)
	BEGIN
		SET @DynamicSQL = @DynamicSQL + N' AND so.SentOrdertoCust IS NOT NULL';
	END
	ELSE IF(ISNULL(@IncludeSentOrder,0) = 2)
	BEGIN
		SET @DynamicSQL = @DynamicSQL + N' AND so.SentOrdertoCust IS NULL';
	END
	IF(@ContractNo IS NOT NULL)
	BEGIN
		SET @DynamicSQL = @DynamicSQL + N' AND sol.ContractNo LIKE @ContractNo';
	END
	IF(@ClientSearch IS NOT NULL)
	BEGIN
		SET @DynamicSQL = @DynamicSQL + N' AND so.ClientNo = @ClientSearch';
	END
	IF(@ClientSearch IS NOT NULL AND @IsGlobalLogin <> 1)
	BEGIN
		SET @DynamicSQL = @DynamicSQL + N' AND so.companyNo IN (
					SELECT CompanyNo FROM dbo.ufn_GSA_GetMyCompnayIds(@SelectedLoginID, @ClientId)
			)';
	END
	IF(ISNULL(@SOCheckedStatus,0) = 1)
	BEGIN
		SET @DynamicSQL = @DynamicSQL + N' AND so.DateAuthorised IS NOT NULL';
	END
	ELSE IF(ISNULL(@SOCheckedStatus,0) = 2)
	BEGIN
		SET @DynamicSQL = @DynamicSQL + N' AND so.DateAuthorised IS NULL';
	END
	IF(@SOStatus IS NOT NULL)
	BEGIN
		SET @DynamicSQL = @DynamicSQL + N' AND dbo.ufn_get_salesOrder_statusNo(SalesOrderNo) = @SOStatus';
	END
	IF(@AS6081 = 0)
	BEGIN
		SET @DynamicSQL = @DynamicSQL + N' AND (so.AS6081 IS NULL OR so.AS6081 = 0)';
	END
	ELSE IF(@AS6081 = 1)
	BEGIN
		SET @DynamicSQL = @DynamicSQL + N' AND so.AS6081 = 1';
	END

	SET @DynamicSQL = N'
		;WITH cteSearch AS(' + @DynamicSQL + ')
		SELECT
			SalesOrderNumber AS ''Sales Order No''
			,Part AS ''Part No''
			,CustomerPO AS ''Customer PO No''
			,ISNULL(QuantityOrdered,0) AS ''Qty Ordered''
			,ISNULL(QuantityShipped,0) AS ''Qty Shipped''
			,ISNULL(QuantityInStock,0) AS ''Qty In Stock''
			,CompanyName AS Company
			,ContactName AS Contact
			,FORMAT(DateOrdered, ''dd/MM/yyyy'') AS Ordered
			,FORMAT(DatePromised, ''dd/MM/yyyy'') AS Promised
			,ss.[Description] AS Status
			,ContractNo AS ''Contract No''
		FROM cteSearch
		LEFT JOIN tbSalesOrderStatus ss WITH(NOLOCK) on ss.SalesOrderStatusId = cteSearch.SalesOrderStatusNo
		ORDER BY RowNum';

	-- Execute Dynamic SQL
	PRINT(@DynamicSQL)
	--PRINT(@ParamDefinition);
	--RETURN
	DECLARE @ParamDefinition NVARCHAR(MAX) = N'
		@IncludeClosed BIT
		,@ClientId INT
		,@TeamId INT
		,@DivisionId INT
		,@LoginId INT
		,@RecentDate DATETIME
		,@SalesOrderNoLo INT
		,@SalesOrderNoHi INT
		,@DateOrderedFrom DATETIME
		,@DateOrderedTo DATETIME
		,@DatePromisedFrom DATETIME
		,@DatePromisedTo DATETIME
		,@ContactSearch NVARCHAR(50)
		,@CountrySearch INT
		,@CMSearch NVARCHAR(50)
		,@SalesmanNo INT
		,@CustomerPOSearch NVARCHAR(50)
		,@PartSearch NVARCHAR(50)
		,@ContractNo NVARCHAR(100)
		,@SOStatus INT
		,@ClientSearch INT
		,@SelectedLoginID INT';

	EXEC sp_executesql @DynamicSQL
						,@ParamDefinition
						,@IncludeClosed
						,@ClientId
						,@TeamId
						,@DivisionId
						,@LoginId
						,@RecentDate
						,@SalesOrderNoLo
						,@SalesOrderNoHi
						,@DateOrderedFrom
						,@DateOrderedTo
						,@DatePromisedFrom
						,@DatePromisedTo
						,@ContactSearch
						,@CountrySearch
						,@CMSearch
						,@SalesmanNo
						,@CustomerPOSearch
						,@PartSearch
						,@ContractNo
						,@SOStatus
						,@ClientSearch
						,@SelectedLoginID;
END
GO
/*
EXEC [dbo].[usp_export_datalistnugget_SalesOrderLine] 
	@ClientId = 101
	,@TeamId  = 222
	,@DivisionId  = 333
	,@LoginId  = 6670
	,@OrderBy  = 1
	,@SortDir  = 1
	--, @PageIndex int = 0                                
	--, @PageSize int = 10                                
	,@PartSearch  = 'part here%'
	,@ContactSearch  = 'contact here%'
	,@CountrySearch  = 69
	,@CMSearch  = 'company name%'
	,@SalesmanNo = 111
	,@CustomerPOSearch  = 'cuspo%'
	,@RecentOnly = 1
	,@IncludeClosed  = 1
	,@SalesOrderNoLo  = 123
	,@SalesOrderNoHi  = 456
	,@DateOrderedFrom  = '2025-04-12'
	,@DateOrderedTo  = '2025-04-29'
	,@DatePromisedFrom  = '2025-05-12'
	,@DatePromisedTo  = '2025-05-29'
	,@IncludeSentOrder  = 1
	,@ContractNo  = 123213
	,@IsGlobalLogin = 0
	,@ClientSearch = NULL
	,@SOCheckedStatus = 1
	,@SOStatus = 3
	,@AS6081 = 1       
	,@SelectedLoginId = NULL

*/
