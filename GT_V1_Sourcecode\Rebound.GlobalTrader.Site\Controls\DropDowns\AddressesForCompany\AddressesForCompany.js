Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");Rebound.GlobalTrader.Site.Controls.DropDowns.AddressesForCompany=function(n){Rebound.GlobalTrader.Site.Controls.DropDowns.AddressesForCompany.initializeBase(this,[n]);this._intCompanyID=null};Rebound.GlobalTrader.Site.Controls.DropDowns.AddressesForCompany.prototype={get_intCompanyID:function(){return this._intCompanyID},set_intCompanyID:function(n){this._intCompanyID!==n&&(this._intCompanyID=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.DropDowns.AddressesForCompany.callBaseMethod(this,"initialize");this.addSetupDataCall(Function.createDelegate(this,this.setupDataCall));this.addGetDataOK(Function.createDelegate(this,this.dataCallOK))},dispose:function(){this.isDisposed||(this._intCompanyID=null,Rebound.GlobalTrader.Site.Controls.DropDowns.AddressesForCompany.callBaseMethod(this,"dispose"))},setupDataCall:function(){this._objData.set_PathToData("controls/DropDowns/AddressesForCompany");this._objData.set_DataObject("AddressesForCompany");this._objData.set_DataAction("GetData");this._objData.addParameter("id",this._intCompanyID)},dataCallOK:function(){var t=this._objData._result,n;if(t.Addresses)for(n=0;n<t.Addresses.length;n++)this.addOption(t.Addresses[n].Address,t.Addresses[n].ID)}};Rebound.GlobalTrader.Site.Controls.DropDowns.AddressesForCompany.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.AddressesForCompany",Rebound.GlobalTrader.Site.Controls.DropDowns.Base);