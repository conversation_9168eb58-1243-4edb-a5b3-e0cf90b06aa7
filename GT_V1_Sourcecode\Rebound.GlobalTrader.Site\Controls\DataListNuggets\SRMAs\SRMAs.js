Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DataListNuggets");Rebound.GlobalTrader.Site.Controls.DataListNuggets.SRMAs=function(n){Rebound.GlobalTrader.Site.Controls.DataListNuggets.SRMAs.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.DataListNuggets.SRMAs.prototype={get_blnPOHub:function(){return this._blnPOHub},set_blnPOHub:function(n){this._blnPOHub!==n&&(this._blnPOHub=n)},initialize:function(){this.addSetupDataCallEvent(Function.createDelegate(this,this.setupDataCall));this.addGetDataOKEvent(Function.createDelegate(this,this.getDataOK));this.addInitCompleteEvent(Function.createDelegate(this,this.initAfterBaseIsReady));this._strPathToData="controls/DataListNuggets/SRMAs";this._strDataObject="SRMAs";Rebound.GlobalTrader.Site.Controls.DataListNuggets.SRMAs.callBaseMethod(this,"initialize")},initAfterBaseIsReady:function(){this.addPageTabChangedEvent(Function.createDelegate(this,this.pageTabChanged));this.updateFilterVisibility();this.getData()},dispose:function(){this.isDisposed||Rebound.GlobalTrader.Site.Controls.DataListNuggets.SRMAs.callBaseMethod(this,"dispose")},pageTabChanged:function(){this._table._intCurrentPage=1;this._enmViewLevel=this._intCurrentTab;this.updateFilterVisibility();this.getData()},setupDataCall:function(){this._objData.addParameter("ViewLevel",this._enmViewLevel)},getDataOK:function(){for(var n,i,t=0,r=this._objResult.Results.length;t<r;t++)n=this._objResult.Results[t],i=[$RGT_nubButton_SRMA(n.ID,n.No),$R_FN.writeDoubleCellValue($R_FN.writePartNo(n.Part,n.ROHS),$RGT_nubButton_Manufacturer(n.MfrNo,n.Mfr)),n.Quantity,$R_FN.writeDoubleCellValue($RGT_nubButton_Company(n.CMNo,n.CM),$RGT_nubButton_Contact(n.ContactNo,n.Contact)),$R_FN.setCleanTextValue(n.Date),$RGT_nubButton_PurchaseOrder(n.PONo,n.PO)],this._table.addRow(i,n.ID,!1),i=null,n=null},updateFilterVisibility:function(){this.getFilterField("ctlBuyerName").show(this._enmViewLevel!=0);this.getFilterField("ctlPohubOnly").show(this._blnPOHub);this.getFilterField("ctlPohubOnly").enableField(this._blnPOHub)}};Rebound.GlobalTrader.Site.Controls.DataListNuggets.SRMAs.registerClass("Rebound.GlobalTrader.Site.Controls.DataListNuggets.SRMAs",Rebound.GlobalTrader.Site.Controls.DataListNuggets.Base,Sys.IDisposable);