﻿using System;
using System.Web.UI;

namespace Rebound.GlobalTrader.Site.Controls.DataListNuggets
{
    public partial class ProspectiveOffers : Base
    {
        protected override void OnInit(EventArgs e)
        {
            SetDataListNuggetType("ProspectiveOffers");
            base.OnInit(e);
            TitleText = Functions.GetGlobalResource("Nuggets", "Orders_ProspectiveCrossSelling");
            AddScriptReference("Controls.DataListNuggets.ProspectiveOffers.ProspectiveOffers");
            SetupTable();
        }

        protected override void OnLoad(EventArgs e)
        {
            _scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.DataListNuggets.ProspectiveOffers", ctlDesignBase.ClientID);
            base.OnLoad(e);
        }

        protected override void RenderAdditionalState()
        {
            string strViewLevel = this.GetSavedStateValue("ViewLevel");
            if (!string.IsNullOrEmpty(strViewLevel))
            {
                ((Pages.Content)Page).CurrentTab = Convert.ToInt32(strViewLevel);
                _enmViewLevel = (ViewLevelList)Convert.ToInt32(strViewLevel);
                this.OnAskPageToChangeTab();
            }
            base.RenderAdditionalState();
        }


        private void SetupTable()
        {
            _tbl.AllowSelection = false;
            _tbl.SortColumnDirection = Enumerations.SortColumnDirection.DESC;
            _tbl.Columns.Add(new FlexiDataColumn("Number", WidthManager.GetWidth(WidthManager.ColumnWidth.SystemDocumentNumber), true));
            _tbl.Columns.Add(new FlexiDataColumn("ImportDate", WidthManager.GetWidth(WidthManager.ColumnWidth.Date), true));
            _tbl.Columns.Add(new FlexiDataColumn("ProspectiveOfferSource", WidthManager.GetWidth(WidthManager.ColumnWidth.CompanyName), true));
            _tbl.Columns.Add(new FlexiDataColumn("RowCount", WidthManager.GetWidth(WidthManager.ColumnWidth.ImportDate), true));
            _tbl.Columns.Add(new FlexiDataColumn("ImportStatus", WidthManager.GetWidth(WidthManager.ColumnWidth.CompanyName), true));
            _tbl.Columns.Add(new FlexiDataColumn("Supplier", WidthManager.GetWidth(WidthManager.ColumnWidth.Supplier), true));
            _tbl.Columns.Add(new FlexiDataColumn("ImportedBy", WidthManager.GetWidth(WidthManager.ColumnWidth.CompanyName), true));
        }
    }
}