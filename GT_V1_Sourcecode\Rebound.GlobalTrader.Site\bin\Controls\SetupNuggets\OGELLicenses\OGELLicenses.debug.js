///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
//Marker     changed by      date         Remarks
//[001]      nagiTo           10/04/2024   Add/edit OGELLicenses in setup screen
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.SetupNuggets");


Rebound.GlobalTrader.Site.Controls.SetupNuggets.OGELLicenses = function(element) {
    Rebound.GlobalTrader.Site.Controls.SetupNuggets.OGELLicenses.initializeBase(this, [element]);
	this._intItemID = -1;
};

Rebound.GlobalTrader.Site.Controls.SetupNuggets.OGELLicenses.prototype = {

    get_intItemID: function() { return this._intItemID; }, set_intItemID: function(v) { if (this._intItemID !== v) this._intItemID = v; },
    get_ibtnAdd: function() { return this._ibtnAdd; }, set_ibtnAdd: function(v) { if (this._ibtnAdd !== v) this._ibtnAdd = v; },
    get_ibtnEdit: function() { return this._ibtnEdit; }, set_ibtnEdit: function(v) { if (this._ibtnEdit !== v) this._ibtnEdit = v; },
    get_tbl: function() { return this._tbl; }, set_tbl: function(v) { if (this._tbl !== v) this._tbl = v; },

    initialize: function() {
        Rebound.GlobalTrader.Site.Controls.SetupNuggets.OGELLicenses.callBaseMethod(this, "initialize");

        //data
        this._strDataPath = "controls/SetupNuggets/OGELLicenses";
        this._strDataObject = "OGELLicenses";

        //nugget events
        this.addRefreshEvent(Function.createDelegate(this, this.getData));

        //other controls
        this._tbl.addSelectedIndexChanged(Function.createDelegate(this, this.tbl_SelectedIndexChanged));

        //edit form
        if (this._ibtnEdit) {
            $R_IBTN.addClick(this._ibtnEdit, Function.createDelegate(this, this.showEditForm));
            this._frmEdit = $find(this._aryFormIDs[1]);
            this._frmEdit.addCancel(Function.createDelegate(this, this.hideEditForm));
            this._frmEdit.addSaveComplete(Function.createDelegate(this, this.saveEditComplete));
        }

        //add form
        if (this._ibtnAdd) {
            $R_IBTN.addClick(this._ibtnAdd, Function.createDelegate(this, this.showAddForm));
            this._frmAdd = $find(this._aryFormIDs[0]);
            this._frmAdd.addCancel(Function.createDelegate(this, this.hideAddForm));
            this._frmAdd.addSaveComplete(Function.createDelegate(this, this.saveAddComplete));
        }

        this.getData();
    },

    dispose: function() {
        if (this.isDisposed) return;
        if (this._ibtnAdd) $R_IBTN.clearHandlers(this._ibtnAdd);
        if (this._ibtnEdit) $R_IBTN.clearHandlers(this._ibtnEdit);
        if (this._frmEdit) this._frmEdit.dispose();
        if (this._frmAdd) this._frmAdd.dispose();
        if (this._tbl) this._tbl.dispose();
        this._frmEdit = null;
        this._frmAdd = null;
        this._ibtnAdd = null;
        this._ibtnEdit = null;
        this._tbl = null;
        this._intItemID = null;
        Rebound.GlobalTrader.Site.Controls.SetupNuggets.OGELLicenses.callBaseMethod(this, "dispose");
    },

    enableEditButtons: function(bln) {
        if (this._ibtnEdit) $R_IBTN.enableButton(this._ibtnEdit, bln);
    },

    tbl_SelectedIndexChanged: function() {
        this.enableEditButtons(true);
        this._intItemID = this._tbl._varSelectedValue;
    },

    getData: function() {
        this.showLoading(true);
        this.showContentLoading(true);
        this.enableEditButtons(false);
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData(this._strDataPath);
        obj.set_DataObject(this._strDataObject);
        obj.set_DataAction("GetData");
        obj.addDataOK(Function.createDelegate(this, this.getDataOK));
        obj.addError(Function.createDelegate(this, this.getDataError));
        obj.addTimeout(Function.createDelegate(this, this.getDataError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },
    getDataOK: function(args) {
        this.showLoading(false);
        var blnHasData = false;
        this._tbl.clearTable();
        var result = args._result;
        if (result.Items) {
            for (var i = 0; i < result.Items.length; i++) {
                var row = result.Items[i];
                var isactive = row.Inactive === false ? "Yes" : "No";

                var aryData = [
                    $R_FN.writeDoubleCellValue(row.ID)
                    , $R_FN.setCleanTextValue(row.Name)
                    , $R_FN.setCleanTextValue(row.Description)
                    , $R_FN.setCleanTextValue(isactive)
                    , row.UpdatedDate
                    , $R_FN.setCleanTextValue(row.UpdatedBy)
					];
                var xtraData = {
                    Inactive: row.Inactive
                };
                var strCSS = (row.Inactive) ? "ceased" : "";
                this._tbl.addRow(aryData, row.ID, (row.ID == this._intItemID), xtraData, strCSS);
                row = null;
                blnHasData = true;
            }
        }
        this._tbl.resizeColumns();
        this.showContent(true);
        this.showContentLoading(false);
        this.showNoData(!blnHasData);
    },

    getDataError: function(args) {
        this.showError(true, args.get_ErrorMessage());
    },

    showEditForm: function() {
        this._frmEdit._intItemID = this._intItemID;
        this._frmEdit.setFieldValue("ctlOgelNumber", this._tbl.getSelectedCellValue(1));
        this._frmEdit.setFieldValue("ctlDescription", this._tbl.getSelectedCellValue(2));
        this._frmEdit.setFieldValue("ctlInActive", this._tbl.getSelectedExtraData().Inactive);
        this.showForm(this._frmEdit, true);
    },

    hideEditForm: function() {
        this.showForm(this._frmEdit, false);
    },

    saveEditComplete: function() {
        this.hideEditForm();
        this.showSavedOK(true, $R_RES.ChangesSavedSuccessfully);
        this.getData();
    },

    showAddForm: function() {
        this.showForm(this._frmAdd, true);
    },

    hideAddForm: function() {
        this.showForm(this._frmAdd, false);
    },

    saveAddComplete: function() {
        this.hideAddForm();
        this._intItemID = this._frmAdd._intItemID;
        this.getData();
        this.showSavedOK(true, $R_RES.ChangesSavedSuccessfully);
    }

};

Rebound.GlobalTrader.Site.Controls.SetupNuggets.OGELLicenses.registerClass("Rebound.GlobalTrader.Site.Controls.SetupNuggets.OGELLicenses", Rebound.GlobalTrader.Site.Controls.Nuggets.Base, Sys.IDisposable);
