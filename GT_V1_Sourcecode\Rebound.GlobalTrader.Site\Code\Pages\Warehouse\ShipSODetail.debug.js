///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Pages.Warehouse");

Rebound.GlobalTrader.Site.Pages.Warehouse.ShipSODetail = function(el) { 
	Rebound.GlobalTrader.Site.Pages.Warehouse.ShipSODetail.initializeBase(this, [el]);
};

Rebound.GlobalTrader.Site.Pages.Warehouse.ShipSODetail.prototype = {

	get_ctlMainInfo: function() { return this._ctlMainInfo; }, 	set_ctlMainInfo: function(v) { if (this._ctlMainInfo !== v)  this._ctlMainInfo = v; }, 
	get_ctlLines: function() { return this._ctlLines; }, 	set_ctlLines: function(v) { if (this._ctlLines !== v)  this._ctlLines = v; }, 
	get_intSOID: function() { return this._intSOID; }, 	set_intSOID: function(v) { if (this._intSOID !== v)  this._intSOID = v; }, 
	get_lblStatus: function() { return this._lblStatus; }, 	set_lblStatus: function(v) { if (this._lblStatus !== v)  this._lblStatus = v; }, 
	get_pnlStatus: function() { return this._pnlStatus; }, 	set_pnlStatus: function(v) { if (this._pnlStatus !== v)  this._pnlStatus = v; }, 
	get_IsGlobalLogin: function () { return this._IsGlobalLogin; }, set_IsGlobalLogin: function (v) { if (this._IsGlobalLogin !== v) this._IsGlobalLogin = v; },

	initialize: function() {
		Rebound.GlobalTrader.Site.Pages.Warehouse.ShipSODetail.callBaseMethod(this, "initialize");
	},
	
	goInit: function() {
		if (this._ctlMainInfo) this._ctlMainInfo.addGetDataComplete(Function.createDelegate(this, this.ctlMainInfo_GetDataComplete));
		if (this._ctlLines) this._ctlLines.addSaveShipComplete(Function.createDelegate(this, this.ctlLines_SaveShipComplete));
		Rebound.GlobalTrader.Site.Pages.Warehouse.ShipSODetail.callBaseMethod(this, "goInit");
	},

	dispose: function() { 
		if (this.isDisposed) return;
		if (this._ctlLines) this._ctlLines.dispose();
		if (this._ctlMainInfo) this._ctlMainInfo.dispose();
		this._ctlMainInfo = null;
		this._ctlLines = null;
		this._intSOID = null;
		this._lblStatus = null;
		this._pnlStatus = null;
		this._IsGlobalLogin=null;
		Rebound.GlobalTrader.Site.Pages.Warehouse.ShipSODetail.callBaseMethod(this, "dispose");
	},
	
	ctlMainInfo_GetDataComplete: function() {
		var intStatus = Number.parseInvariant(this._ctlMainInfo.getFieldValue("hidStatusNo").toString());
		this._ctlLines._blnDisableAllButtons = (intStatus == $R_ENUM$SalesOrderStatus.Complete);
		this._ctlLines.enableButtons(true);
		$R_FN.setInnerHTML(this._lblStatus, this._ctlMainInfo.getFieldValue("hidStatus"));
		this._ctlLines._intGlobalClientNo = this._IsGlobalLogin == true ? this._ctlMainInfo.getFieldValue("hidGlobalClientNo") : null;
		this._ctlMainInfo._IsGlobalLogin = this._IsGlobalLogin;
	},
	
	ctlLines_SaveShipComplete: function() {
		this._ctlMainInfo.getData();
	}

};
Rebound.GlobalTrader.Site.Pages.Warehouse.ShipSODetail.registerClass("Rebound.GlobalTrader.Site.Pages.Warehouse.ShipSODetail", Rebound.GlobalTrader.Site.Pages.Content, Sys.IDisposable);
