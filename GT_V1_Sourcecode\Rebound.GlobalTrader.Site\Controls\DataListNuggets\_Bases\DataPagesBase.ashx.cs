using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;
using Rebound.GlobalTrader.Site;
using Rebound.GlobalTrader.Site.Enumerations;

namespace Rebound.GlobalTrader.Site.Controls.DataListNuggets.Data
{

    public class Base : Rebound.GlobalTrader.Site.Data.Base
    {

        protected DataListNugget _objDataListNugget;
        protected DataListNuggetState _objState = new DataListNuggetState();

        public override void ProcessRequest(HttpContext context)
        {
            if (base.init(context))
            {
                if (Action != "ExportToCSV")
                {
                    _objDataListNugget = _objSite.GetDataListNugget(GetFormValue_Int("DLNID"));
                }
                switch (Action)
                {
                    case "GetData": GetData(); break;
                    case "SaveState": SaveState(); break;
                }
            }
        }

        protected virtual void GetData()
        {
            SaveState();
        }

        protected void SaveState()
        {
            if (GetFormValue_Boolean("SaveState"))
            {
                AddFilterStates();
                _objState.SortIndex = (int)GetFormValue_NullableInt("SortIndex", 1) - 1;
                _objState.SortDirection = (int)GetFormValue_NullableInt("SortDir", SortColumnDirection.ASC);
                _objState.Page = (int)GetFormValue_NullableInt("PageIndex", 0) + 1;
                if (GetFormValue_NullableInt("PageSizeForState") == null)
                {
                    _objState.PageSize = (int)GetFormValue_NullableInt("PageSize", 10);
                }
                else
                {
                    _objState.PageSize = (int)GetFormValue_NullableInt("PageSizeForState");
                }
                DoSaveState();
            }
        }

        protected virtual void AddFilterStates()
        {
            //Let subclasses add their own filter states
        }

        protected void DoSaveState()
        {
            DataListNuggetStateManager.SaveState(_objDataListNugget.ID, GetFormValue_String("DLNSubType"), _objState.ToString());
        }

        protected void AddFilterState(string strField)
        {
            string strValue = "";
            Rebound.GlobalTrader.Site.Controls.DropDowns.NumericalComparison.NumericalComparisonType enmComparison = (Rebound.GlobalTrader.Site.Controls.DropDowns.NumericalComparison.NumericalComparisonType)GetFormValue_Int(String.Format("{0}_Comparison", strField));
            Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.Type enmType = (Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.Type)GetFormValue_Int(String.Format("{0}_Type", strField));

            switch (enmType)
            {
                case Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.Type.CheckBox:
                case Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.Type.DropDown:
                case Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.Type.DateSelect:
                case Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.Type.TextBox:
                    strValue = GetFormValue_String(strField);
                    break;
                case Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.Type.Numerical:
                case Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.Type.StarRating:
                    switch (enmComparison)
                    {
                        case Rebound.GlobalTrader.Site.Controls.DropDowns.NumericalComparison.NumericalComparisonType.EqualTo: strValue = GetFormValue_String(String.Format("{0}Lo", strField)); break;
                        case Rebound.GlobalTrader.Site.Controls.DropDowns.NumericalComparison.NumericalComparisonType.GreaterThan: strValue = (GetFormValue_NullableInt(String.Format("{0}Lo", strField), 0) - 1).ToString(); break;
                        case Rebound.GlobalTrader.Site.Controls.DropDowns.NumericalComparison.NumericalComparisonType.LessThan: strValue = (GetFormValue_NullableInt(String.Format("{0}Hi", strField), 0) + 1).ToString(); break;
                        case Rebound.GlobalTrader.Site.Controls.DropDowns.NumericalComparison.NumericalComparisonType.GreaterThanOrEqualTo: strValue = GetFormValue_String(String.Format("{0}Lo", strField)); break;
                        case Rebound.GlobalTrader.Site.Controls.DropDowns.NumericalComparison.NumericalComparisonType.LessThanOrEqualTo: strValue = GetFormValue_String(String.Format("{0}Hi", strField)); break;
                    }
                    break;
            }

            _objState.AddFilterState(
                strField
                , enmType
                , GetFormValue_Boolean(String.Format("{0}_IsShown", strField))
                , GetFormValue_Boolean(String.Format("{0}_IsOn", strField))
                , enmComparison
                , strValue
            );
        }

        protected void AddExplicitFilterState(string strField, object objValue)
        {
            if (objValue == null) objValue = "";
            _objState.AddFilterState(
                strField
                , Controls.FilterDataItemRows.Type.None
                , (objValue.ToString() != "")
                , (objValue.ToString() != "")
                , Controls.DropDowns.NumericalComparison.NumericalComparisonType.EqualTo
                , objValue.ToString()
            );
        }

        protected void SetDataListNuggetType(string str)
        {
            _objDataListNugget = _objSite.GetDataListNugget(str);
        }
        protected void AddFilterStateWithHttpEncode(string strField)
        {
            string strValue = "";
            Rebound.GlobalTrader.Site.Controls.DropDowns.NumericalComparison.NumericalComparisonType enmComparison = (Rebound.GlobalTrader.Site.Controls.DropDowns.NumericalComparison.NumericalComparisonType)GetFormValue_Int(String.Format("{0}_Comparison", strField));
            Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.Type enmType = (Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.Type)GetFormValue_Int(String.Format("{0}_Type", strField));

            switch (enmType)
            {
                case Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.Type.CheckBox:
                case Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.Type.DropDown:
                case Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.Type.DateSelect:
                case Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.Type.TextBox:
                    strValue = GetFormValue_String(strField);
                    break;
                case Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.Type.Numerical:
                case Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.Type.StarRating:
                    switch (enmComparison)
                    {
                        case Rebound.GlobalTrader.Site.Controls.DropDowns.NumericalComparison.NumericalComparisonType.EqualTo: strValue = GetFormValue_String(String.Format("{0}Lo", strField)); break;
                        case Rebound.GlobalTrader.Site.Controls.DropDowns.NumericalComparison.NumericalComparisonType.GreaterThan: strValue = (GetFormValue_NullableInt(String.Format("{0}Lo", strField), 0) - 1).ToString(); break;
                        case Rebound.GlobalTrader.Site.Controls.DropDowns.NumericalComparison.NumericalComparisonType.LessThan: strValue = (GetFormValue_NullableInt(String.Format("{0}Hi", strField), 0) + 1).ToString(); break;
                        case Rebound.GlobalTrader.Site.Controls.DropDowns.NumericalComparison.NumericalComparisonType.GreaterThanOrEqualTo: strValue = GetFormValue_String(String.Format("{0}Lo", strField)); break;
                        case Rebound.GlobalTrader.Site.Controls.DropDowns.NumericalComparison.NumericalComparisonType.LessThanOrEqualTo: strValue = GetFormValue_String(String.Format("{0}Hi", strField)); break;
                    }
                    break;
            }

            _objState.AddFilterState(
                strField
                , enmType
                , GetFormValue_Boolean(String.Format("{0}_IsShown", strField))
                , GetFormValue_Boolean(String.Format("{0}_IsOn", strField))
                , enmComparison
                , HttpUtility.UrlEncode(strValue)
            );
        }
    }
}
