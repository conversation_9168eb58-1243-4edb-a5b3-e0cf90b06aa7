﻿//Marker     changed by      date          Remarks
//[001]      Vinay           18/09/2012    Ref:## - Display Purchase Country
//[002]      <PERSON><PERSON><PERSON>         27/02/2014    Ref:## - Supplier RMA-ISCRMA
//[003]      A<PERSON>u          11/June/2018    Added code to print So and so detail in report.
//[004]      <PERSON><PERSON><PERSON>    25/06/2018      Show supplier warranty for po line detail.
//[006]      <PERSON><PERSON><PERSON>     27-Sep-2018   REB-13083 Change request PO - delivery status
//[007]      <PERSON>     17/08/2021   (GTDP-198) Controlling specific product groups for purchasing
//[007]      A<PERSON><PERSON>av <PERSON>   28-Jul-2021   Add new property for repeat order
//[008]      <PERSON>     11/01/2022   (GTDP-240)  Add function to add warning message to show on all screens REQ - Quote - SO - PO related to ECCN field
//[009]      Ab<PERSON>av <PERSON>   21-Jan-2022   Addnew property SupplierApprovalStatus 
//[010]      Ravi             18-09-2023    RP-2339  AS6081 (Show AS6081 on detail screens)
//[RP-881]   <PERSON>    13-11-2023      Include auto quarantine function for stock received from a sanctioned supplier
using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;

namespace Rebound.GlobalTrader.DAL {
	
	public class PurchaseOrderLineDetails {
		
		#region Constructors
		
		public PurchaseOrderLineDetails() { }
		
		#endregion
		
		#region Properties
	
		/// <summary>
		/// PurchaseOrderLineId (from Table)
		/// </summary>
		public System.Int32 PurchaseOrderLineId { get; set; }
		/// <summary>
		/// WarningMessage
		/// </summary>
		public System.String WarningMessage { get; set; }
		public System.Boolean? IsCountryOnHighRisk { get; set; }
		public System.String CountryOfOrigin { get; set; }
        public System.String IHSProduct { get; set; }
        public System.String ECCNCode { get; set; }
        public System.Int32? ECCNId { get; set; }
        public System.String ECCNCodeEdit { get; set; }
        public System.String ECCNCodeSOLine { get; set; }
        public System.Int32? ECCNIdSOLine { get; set; }



        /// <summary>
        /// LifeCycleStage
        /// </summary>
        public System.String LifeCycleStage { get; set; }
        /// <summary>
        /// AveragePrice
        /// </summary>
        public System.Double AveragePrice { get; set; }
        /// <summary>
        /// HTSCode
        /// </summary>
        public System.String HTSCode { get; set; }

        /// <summary>
        /// Packaging
        /// </summary>
        public System.String Packaging { get; set; }
        /// <summary>
        /// PackagingSize
        /// </summary>
        public System.String PackagingSize { get; set; }
        public System.String Descriptions { get; set; }
		/// <summary>
		/// PurchaseOrderNo (from Table)
		/// </summary>
		public System.Int32 PurchaseOrderNo { get; set; }
		/// <summary>
		/// FullPart (from Table)
		/// </summary>
		public System.String FullPart { get; set; }
        /// <summary>
        /// SupportTeamMemberNo
        /// </summary>
        public System.Int32? SupportTeamMemberNo { get; set; }
        /// <summary>
        /// SupportTeamMemberName
        /// </summary>
        public System.String SupportTeamMemberName { get; set; }
		/// <summary>
		/// Part (from Table)
		/// </summary>
		public System.String Part { get; set; }
		/// <summary>
		/// ManufacturerNo (from Table)
		/// </summary>
		public System.Int32? ManufacturerNo { get; set; }
		/// <summary>
		/// DateCode (from Table)
		/// </summary>
		public System.String DateCode { get; set; }
		/// <summary>
		/// PackageNo (from Table)
		/// </summary>
		public System.Int32? PackageNo { get; set; }
		/// <summary>
		/// Quantity (from Table)
		/// </summary>
		public System.Int32 Quantity { get; set; }
		/// <summary>
		/// Price (from Table)
		/// </summary>
		public System.Double Price { get; set; }
		/// <summary>
		/// DeliveryDate (from Table)
		/// </summary>
		public System.DateTime DeliveryDate { get; set; }
		public System.DateTime? DeliveryDate2 { get; set; }


		/// <summary>
		/// ReceivingNotes (from Table)
		/// </summary>
		public System.String ReceivingNotes { get; set; }
		/// <summary>
		/// Taxable (from Table)
		/// </summary>
		public System.Boolean Taxable { get; set; }
        /// <summary>
        /// RepeatOrder (from Table)
        /// </summary>
        public System.Boolean RepeatOrder { get; set; }
        /// <summary>
        /// ProductNo (from Table)
        /// </summary>
        public System.Int32? ProductNo { get; set; }
		/// <summary>
		/// Posted (from Table)
		/// </summary>
		public System.Boolean Posted { get; set; }
		/// <summary>
		/// ShipInCost (from Table)
		/// </summary>
		public System.Double? ShipInCost { get; set; }
		/// <summary>
		/// SupplierPart (from Table)
		/// </summary>
		public System.String SupplierPart { get; set; }
		/// <summary>
		/// Inactive (from Table)
		/// </summary>
		public System.Boolean Inactive { get; set; }
		/// <summary>
		/// Closed (from Table)
		/// </summary>
		public System.Boolean Closed { get; set; }
		/// <summary>
		/// ROHS (from Table)
		/// </summary>
		public System.Byte? ROHS { get; set; }
		/// <summary>
		/// UpdatedBy (from Table)
		/// </summary>
		public System.Int32? UpdatedBy { get; set; }
		/// <summary>
		/// DLUP (from Table)
		/// </summary>
		public System.DateTime DLUP { get; set; }
		/// <summary>
		/// Notes (from Table)
		/// </summary>
		public System.String Notes { get; set; }
		/// <summary>
		/// FullSupplierPart (from Table)
		/// </summary>
		public System.String FullSupplierPart { get; set; }
		/// <summary>
		/// PurchaseOrderId (from usp_datalistnugget_PurchaseOrderLine)
		/// </summary>
		public System.Int32 PurchaseOrderId { get; set; }
		/// <summary>
		/// PurchaseOrderNumber (from usp_datalistnugget_PurchaseOrderLine)
		/// </summary>
		public System.Int32 PurchaseOrderNumber { get; set; }
		/// <summary>
		/// CurrencyCode (from usp_datalistnugget_PurchaseOrderLine)
		/// </summary>
		public System.String CurrencyCode { get; set; }
		/// <summary>
		/// QuantityOrdered (from usp_datalistnugget_PurchaseOrderLine)
		/// </summary>
		public System.Int32 QuantityOrdered { get; set; }
		/// <summary>
		/// DateOrdered (from usp_datalistnugget_PurchaseOrderLine)
		/// </summary>
		public System.DateTime DateOrdered { get; set; }
               
		/// <summary>
		/// CompanyName (from usp_datalistnugget_PurchaseOrderLine)
		/// </summary>
		public System.String CompanyName { get; set; }
		/// <summary>
		/// CompanyNo (from usp_datalistnugget_PurchaseOrderLine)
		/// </summary>
		public System.Int32 CompanyNo { get; set; }
		/// <summary>
		/// ContactName (from usp_datalistnugget_PurchaseOrderLine)
		/// </summary>
		public System.String ContactName { get; set; }
		/// <summary>
		/// ContactNo (from usp_datalistnugget_PurchaseOrderLine)
		/// </summary>
		public System.Int32 ContactNo { get; set; }
		/// <summary>
		/// ManufacturerCode (from usp_datalistnugget_PurchaseOrderLine)
		/// </summary>
		public System.String ManufacturerCode { get; set; }
		/// <summary>
		/// RowNum (from usp_datalistnugget_PurchaseOrderLine)
		/// </summary>
		public System.Int64? RowNum { get; set; }
		/// <summary>
		/// RowCnt (from usp_datalistnugget_PurchaseOrderLine)
		/// </summary>
		public System.Int32? RowCnt { get; set; }
		/// <summary>
		/// Status (from usp_datalistnugget_PurchaseOrderLine)
		/// </summary>
		public System.Int32? Status { get; set; }
		/// <summary>
		/// QuantityOutstanding (from usp_datalistnugget_PurchaseOrderLine)
		/// </summary>
		public System.Int32? QuantityOutstanding { get; set; }
		/// <summary>
		/// EarliestShipDate (from usp_datalistnugget_PurchaseOrderLine_AllForReceiving)
		/// </summary>
		public System.DateTime? EarliestShipDate { get; set; }
		/// <summary>
		/// PurchaseOrderValue (from usp_itemsearch_PurchaseOrderLine)
		/// </summary>
		public System.Double? PurchaseOrderValue { get; set; }
		/// <summary>
		/// LineNotes (from usp_select_PurchaseOrderLine)
		/// </summary>
		public System.String LineNotes { get; set; }
		/// <summary>
		/// QuantityReceived (from usp_select_PurchaseOrderLine)
		/// </summary>
		public System.Int32 QuantityReceived { get; set; }
		/// <summary>
		/// QuantityAllocated (from usp_select_PurchaseOrderLine)
		/// </summary>
		public System.Int32 QuantityAllocated { get; set; }
		/// <summary>
		/// GIShipInCost (from usp_select_PurchaseOrderLine)
		/// </summary>
		public System.Double GIShipInCost { get; set; }
		/// <summary>
		/// ProductName (from usp_select_PurchaseOrderLine)
		/// </summary>
		public System.String ProductName { get; set; }
		/// <summary>
		/// ProductDescription (from usp_select_PurchaseOrderLine)
		/// </summary>
		public System.String ProductDescription { get; set; }
		/// <summary>
		/// ProductDutyCode (from usp_select_PurchaseOrderLine)
		/// </summary>
		public System.String ProductDutyCode { get; set; }
		/// <summary>
		/// PackageName (from usp_select_PurchaseOrderLine)
		/// </summary>
		public System.String PackageName { get; set; }
		/// <summary>
		/// PackageDescription (from usp_select_PurchaseOrderLine)
		/// </summary>
		public System.String PackageDescription { get; set; }
		/// <summary>
		/// ImportCountryShippingCost (from usp_select_PurchaseOrderLine)
		/// </summary>
		public System.Double? ImportCountryShippingCost { get; set; }
		/// <summary>
		/// CurrencyNo (from usp_select_PurchaseOrderLine)
		/// </summary>
		public System.Int32 CurrencyNo { get; set; }
		/// <summary>
		/// CurrencyDescription (from usp_select_PurchaseOrderLine)
		/// </summary>
		public System.String CurrencyDescription { get; set; }
		/// <summary>
		/// ManufacturerName (from usp_select_PurchaseOrderLine)
		/// </summary>
		public System.String ManufacturerName { get; set; }
		/// <summary>
		/// TaxRate (from usp_select_PurchaseOrderLine)
		/// </summary>
		public System.Double? TaxRate { get; set; }
		/// <summary>
		/// ClientNo (from usp_select_PurchaseOrderLine)
		/// </summary>
		public System.Int32 ClientNo { get; set; }
		/// <summary>
		/// QuantityToReturn (from usp_select_PurchaseOrderLine_for_SupplierRMALine)
		/// </summary>
		public System.Int32? QuantityToReturn { get; set; }
		/// <summary>
		/// ExpediteDate (from usp_selectAll_PurchaseOrderLine_Receiving_for_PurchaseOrder)
		/// </summary>
		public System.DateTime? ExpediteDate { get; set; }
		/// <summary>
		/// Buyer (from usp_selectAll_PurchaseOrderLine_Receiving_for_PurchaseOrder)
		/// </summary>
		public System.Int32 Buyer { get; set; }
		/// <summary>
		/// BuyerName (from usp_selectAll_PurchaseOrderLine_Receiving_for_PurchaseOrder)
		/// </summary>
		public System.String BuyerName { get; set; }
		/// <summary>
		/// DivisionNo (from usp_selectAll_PurchaseOrderLine_Receiving_for_PurchaseOrder)
		/// </summary>
		public System.Int32 DivisionNo { get; set; }
		/// <summary>
		/// TeamNo (from usp_selectAll_PurchaseOrderLine_Receiving_for_PurchaseOrder)
		/// </summary>
		public System.Int32? TeamNo { get; set; }
		/// <summary>
		/// FullName (from usp_selectAll_PurchaseOrderLine_Receiving_for_PurchaseOrder)
		/// </summary>
		public System.String FullName { get; set; }
		/// <summary>
		/// CreditLimit (from usp_selectAll_PurchaseOrderLine_Receiving_for_PurchaseOrder)
		/// </summary>
		public System.Double? CreditLimit { get; set; }
		/// <summary>
		/// Balance (from usp_selectAll_PurchaseOrderLine_Receiving_for_PurchaseOrder)
		/// </summary>
		public System.Double? Balance { get; set; }
		/// <summary>
		/// LineValue (from usp_selectAll_PurchaseOrderLine_Receiving_for_PurchaseOrder)
		/// </summary>
		public System.Double LineValue { get; set; }
		/// <summary>
		/// TermsNo (from usp_selectAll_PurchaseOrderLine_Receiving_for_PurchaseOrder)
		/// </summary>
		public System.Int32 TermsNo { get; set; }
		/// <summary>
		/// TermsName (from usp_selectAll_PurchaseOrderLine_Receiving_for_PurchaseOrder)
		/// </summary>
		public System.String TermsName { get; set; }
		/// <summary>
		/// InAdvance (from usp_selectAll_PurchaseOrderLine_Receiving_for_PurchaseOrder)
		/// </summary>
		public System.Boolean InAdvance { get; set; }
		/// <summary>
		/// DatePromised (from usp_selectAll_PurchaseOrderLine_Receiving_for_PurchaseOrder)
		/// </summary>
		public System.DateTime DatePromised { get; set; }
		/// <summary>
		/// ClientName (from usp_source_PurchaseOrderLine)
		/// </summary>
		public System.String ClientName { get; set; }
		/// <summary>
		/// ClientDataVisibleToOthers (from usp_source_PurchaseOrderLine)
		/// </summary>
		public System.Boolean? ClientDataVisibleToOthers { get; set; }
        //[001] code start
        /// <summary>
        /// ImportCountryNo
        /// </summary>
        public System.Int32? ImportCountryNo { get; set; }
        //[001] code end
        /// </summary>
        public System.DateTime PromiseDate { get; set; }

		public System.DateTime? PromiseDate2 { get; set; }
		/// <summary>
		/// Location
		/// </summary>
		public System.String Location { get; set; }
        /// <summary>
        /// IsNprExist
        /// </summary>
        public System.Boolean? IsNprExist { get; set; }
        /// <summary>
        /// blnSRMA
        /// </summary>
        public System.Boolean? blnSRMA { get; set; }
        /// <summary>
        /// POSerialNo
        /// </summary>
        public System.Int16? POSerialNo { get; set; }
        //[002] Code Start
        /// <summary>
        /// IsCRMA
        /// </summary>
        public System.Boolean? IsCRMA { get; set; }
        //[002] Code End
        /// <summary>
        /// InternalPurchaseOrderId (from Table)
        /// </summary>
        public System.Int32? InternalPurchaseOrderNo { get; set; } 
        /// <summary> 
        /// InternalPurchaseOrderNo (from Table)
        /// </summary>
        public System.Int32 InternalPurchaseOrderNumber { get; set; }
        /// <summary>
        /// InternalPurchaseOrderNumber (from Table)
        /// </summary>

        public int? PurchaseQuoteId { get; set; }
        public int? PurchaseQuoteNumber { get; set; }
        public DateTime? PurchaseRequestDate { get; set; }
        public int? BomNo { get; set; }
        public string BOMName { get; set; }
        /// <summary>
        /// IsIPO (from Table)
        /// </summary>
        public System.Boolean IsIPO { get; set; }

        public System.Double? ClientPrice { get; set; }
        public System.Int32? ClientCurrencyNo { get; set; }
        public System.String ClientCurrencyCode { get; set; }
        public System.Double? ClientShipInCost { get; set; }
        public System.Int32? IPOClientNo { get; set; }
        public System.String CTName { get; set; }
        public System.Double? SOPrice { get; set; }
        public System.String SOCurrencyCode { get; set; }
        public System.Int32? SOCurrencyNo { get; set; }
        public DateTime? SODateOrdered { get; set; }
        
        public System.String ClientCode { get; set; }
        public System.Int32? HubCurrencyNo { get; set; }
        public System.DateTime? CurrencyDate { get; set; }
        public System.Int32? DefaultClientLotNo { get; set; }
        public System.Int32? IPOBuyer { get; set; }
        public System.String IPOBuyerName { get; set; }
        public System.Int32? MailGroupId { get; set; }
        public System.String PoClientName { get; set; }
        public System.Boolean? ProductInactive { get; set; }       
        public System.Double? DutyRate { get; set; }
        /// Taxable (from Table)
        /// </summary>
        public System.Boolean? ReqSerialNo { get; set; }
        /// <summary>
        /// 
        public System.String MSLevel { get; set; }

        /// <summary> 
        /// SONumberDetail
        /// </summary>
        public System.String SONumberDetail { get; set; }
        //[004] start
        /// <summary>
        /// SupplierWarranty
        /// </summary>
        public System.Int32? SupplierWarranty { get; set; }
        //[004] end
        /// 
        public System.Boolean? IsProdHazardous { get; set; }
        public System.Boolean? PrintHazardous { get; set; }
        //[005] start
        public System.String EPRIds { get; set; }
        public System.Boolean IsReleased { get; set; }
        public System.Boolean IsAuthorised { get; set; }
        //[005] end
        //[006] start
        public System.Int32? StatusId { get; set; }
        public System.DateTime? OriginalDeliveryDate { get; set; }
        public System.String DeliveryStatus { get; set; }
        public System.String RowCSS { get; set; }
        //[006] end
        public System.Boolean isIncludeAltPart { get; set; }
        public System.Int32? TotalCount { get; set; }
		public System.Boolean TempLogFlag { get; set; }
		
		public System.Int32? POApprovedBy { get; set; }
        public System.Double? IpoLineTotal { get; set; }
        public System.Double? LineProfit { get; set; }
        public System.Double? LineProfitPercentage { get; set; }
        //[007] code start
        public System.Boolean? IsOrderViaIPOonly { get; set; }
		//[007] code end
		public System.String SupplierMessage { get; set; }
        public System.String ReceivePOStatus { get; set; }
        //[008] code start
        public System.String IHSECCNCodeDefination { get; set; }
        //[008] code end
        
        //[007] code end
        //[008] code start
        public System.Int32? SupplierApprovalStatus { get; set; }
		//[009] code end
		public System.String SupplierApprovalStatusMessage { get; set; }
		/// <summary>
		/// IsRestrictedProduct
		/// </summary>
		public System.Boolean? IsRestrictedProduct { get; set; }
        /// <summary>
        /// ECCNNotify
        /// </summary>
        public System.Boolean? ECCNNotify { get; set; }
        /// <summary>
        /// EccnSubject
        /// </summary>
        public System.String EccnSubject { get; set; }
        /// <summary>
        /// EccnMessage
        /// </summary>
        public System.String EccnMessage { get; set; }
        public System.String EccnWarningMessage { get; set; }

        public System.Boolean AS6081 { get; set; } //[010]
		

		public System.Boolean IsSanctioned { get; set; } //[RP-881]
		public System.Boolean? isPOLogFlag { get; set; } //[010]
		public System.Int32? IPOSupportTeamMemberNo { get; set; }
		/// <summary>
		/// SupportTeamMemberName
		/// </summary>
		public System.String IPOSupportTeamMemberName { get; set; }
        public System.String Remarks { get; set; }
        public System.String ApprovedByName { get; set; }
		public System.String ApprovalStatus { get; set; }
        public System.DateTime? DateApproved { get; set; }
        public System.String VendorName { get; set; }
        public System.String VendorType { get; set; }
        public System.String CustomerName { get; set; }
        public System.String CustomerPO { get; set; }
        public System.String LastPOPlaced { get; set; }
        public System.String QuoteLT { get; set; }
        public System.String MouserLT { get; set; }
        public System.DateTime? CustomerDate { get; set; }
        public System.String DigikeyLT { get; set; }
        public System.Int32? SourcingResultNo { get; set; }
        public System.Int32? SalesOrderNo { get; set; }
        public System.String SalesOrderNumber { get; set; }
        public System.Int32? SalesOrderLineNo { get; set; }

        public System.Double? GPValue { get; set; }
        public System.Double? GPPercent { get; set; }
        public System.Double? VendorBuyPrice { get; set; }
        public System.String Traceability { get; set; }
        public System.String LeadTime { get; set; }
        public System.String PartsGoingToTest { get; set; }
        public System.String Capacitor { get; set; }
		public System.Int32? AvailableStock { get; set; }        
        public bool IsServiceCharge { get; set; }
        public double? ServiceChargeAmount { get; set; }
        public System.Int32? SOLineRank { get; set; }
        public System.Int32? POLineRank { get; set; }
		public double? ShipInCostInBaseClient { get; set; }
        #endregion



	}
}
