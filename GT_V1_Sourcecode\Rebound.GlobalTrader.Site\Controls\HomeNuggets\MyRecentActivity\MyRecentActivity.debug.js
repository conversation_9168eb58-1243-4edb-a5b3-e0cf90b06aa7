///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.HomeNuggets");

Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyRecentActivity = function(element) { 
	Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyRecentActivity.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyRecentActivity.prototype = {

	get_pnlCR: function() { return this._pnlCR; }, 	set_pnlCR: function(v) { if (this._pnlCR !== v)  this._pnlCR = v; }, 
	get_pnlGI: function() { return this._pnlGI; }, 	set_pnlGI: function(v) { if (this._pnlGI !== v)  this._pnlGI = v; }, 
	get_pnlQU: function() { return this._pnlQU; }, 	set_pnlQU: function(v) { if (this._pnlQU !== v)  this._pnlQU = v; }, 
	get_pnlPO: function() { return this._pnlPO; }, 	set_pnlPO: function(v) { if (this._pnlPO !== v)  this._pnlPO = v; }, 
	get_pnlSO: function() { return this._pnlSO; }, 	set_pnlSO: function(v) { if (this._pnlSO !== v)  this._pnlSO = v; }, 
	get_pnlCRMA: function() { return this._pnlCRMA; }, set_pnlCRMA: function(v) { if (this._pnlCRMA !== v)  this._pnlCRMA = v; }, 
	get_pnlSRMA: function() { return this._pnlSRMA; }, set_pnlSRMA: function(v) { if (this._pnlSRMA !== v)  this._pnlSRMA = v; }, 
	get_pnlCredit: function() { return this._pnlCredit; }, set_pnlCredit: function(v) { if (this._pnlCredit !== v)  this._pnlCredit = v; }, 
	get_pnlDebit: function() { return this._pnlDebit; }, set_pnlDebit: function(v) { if (this._pnlDebit !== v)  this._pnlDebit = v; }, 
	get_pnlInv: function() { return this._pnlInv; }, set_pnlInv: function(v) { if (this._pnlInv !== v)  this._pnlInv = v; }, 
    get_pnlOsPoApproval: function () { return this._pnlOsPoApproval; }, set_pnlOsPoApproval: function (v) { if (this._pnlOsPoApproval !== v) this._pnlOsPoApproval = v; }, 
	get_tblCR: function() { return this._tblCR; }, 	set_tblCR: function(v) { if (this._tblCR !== v)  this._tblCR = v; }, 
	get_tblGI: function() { return this._tblGI; }, 	set_tblGI: function(v) { if (this._tblGI !== v)  this._tblGI = v; }, 
	get_tblQU: function() { return this._tblQU; }, 	set_tblQU: function(v) { if (this._tblQU !== v)  this._tblQU = v; }, 
	get_tblPO: function() { return this._tblPO; }, set_tblPO: function(v) { if (this._tblPO !== v)  this._tblPO = v; }, 
	get_tblSO: function() { return this._tblSO; }, set_tblSO: function(v) { if (this._tblSO !== v)  this._tblSO = v; }, 
	get_tblCRMA: function() { return this._tblCRMA; }, set_tblCRMA: function(v) { if (this._tblCRMA !== v)  this._tblCRMA = v; }, 
	get_tblSRMA: function() { return this._tblSRMA; }, set_tblSRMA: function(v) { if (this._tblSRMA !== v)  this._tblSRMA = v; }, 
	get_tblCredit: function() { return this._tblCredit; }, set_tblCredit: function(v) { if (this._tblCredit !== v)  this._tblCredit = v; }, 
	get_tblDebit: function() { return this._tblDebit; }, set_tblDebit: function(v) { if (this._tblDebit !== v)  this._tblDebit = v; }, 
	get_tblInv: function() { return this._tblInv; }, set_tblInv: function(v) { if (this._tblInv !== v)  this._tblInv = v; }, 
    get_tblOsPoApproval: function () { return this._tblOsPoApproval; }, set_tblOsPoApproval: function (v) { if (this._tblOsPoApproval !== v) this._tblOsPoApproval = v; }, 

	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyRecentActivity.callBaseMethod(this, "initialize");
		this.addRefreshEvent(Function.createDelegate(this, this.getData));
	},

	dispose: function() { 
		if (this.isDisposed) return;
		if (this._tblCR) this._tblCR.dispose();
		if (this._tblGI) this._tblGI.dispose();
		if (this._tblQU) this._tblQU.dispose();
		if (this._tblPO) this._tblPO.dispose();
		if (this._tblSO) this._tblSO.dispose();
		if (this._tblCRMA) this._tblCRMA.dispose();
		if (this._tblSRMA) this._tblSRMA.dispose();
		if (this._tblCredit) this._tblCredit.dispose();
		if (this._tblDebit) this._tblDebit.dispose();
		if (this._tblInv) this._tblInv.dispose();
        if (this._tblOsPoApproval) this._tblOsPoApproval.dispose();
		this._pnlCR = null;
		this._pnlGI = null;
		this._pnlQU = null;
		this._pnlPO = null;
		this._pnlSO = null;
		this._pnlCRMA = null;
		this._pnlSRMA = null;
		this._pnlCredit = null;
		this._pnlDebit = null;
		this._pnlInv = null;
        this._pnlOsPoApproval = null;
		this._tblCR = null;
		this._tblGI = null;
		this._tblQU = null;
		this._tblPO = null;
		this._tblSO = null;
		this._tblCRMA = null;
		this._tblSRMA = null;
		this._tblCredit = null;
		this._tblDebit = null;
		this._tblInv = null;
        this._tblOsPoApproval = null;
		Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyRecentActivity.callBaseMethod(this, "dispose");
	},
		
	setupLoadingState: function() {
		$R_FN.showElement(this._pnlCR, false);
		$R_FN.showElement(this._pnlGI, false);
		$R_FN.showElement(this._pnlQU, false);
		$R_FN.showElement(this._pnlPO, false);
		$R_FN.showElement(this._pnlSO, false);
		$R_FN.showElement(this._pnlCRMA, false);
		$R_FN.showElement(this._pnlSRMA, false);
		$R_FN.showElement(this._pnlCredit, false);
		$R_FN.showElement(this._pnlDebit, false);
		$R_FN.showElement(this._pnlInv, false);
        $R_FN.showElement(this._pnlOsPoApproval, false);
		Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyRecentActivity.callBaseMethod(this, "setupLoadingState");
	},

	getData: function() {
		this.setupLoadingState();
		var obj = new Rebound.GlobalTrader.Site.Data();
		obj.set_PathToData("controls/HomeNuggets/MyRecentActivity");
		obj.set_DataObject("MyRecentActivity");
		obj.set_DataAction("GetData");
		obj.addParameter("rowcount", this._intRowCount);
		obj.addParameter("OtherLoginID", this._intLoginID_Other);
		obj.addDataOK(Function.createDelegate(this, this.getDataComplete));
		obj.addError(Function.createDelegate(this, this.homeNuggetDataError));
		obj.addTimeout(Function.createDelegate(this, this.homeNuggetDataError));
		$R_DQ.addToQueue(obj);
		$R_DQ.processQueue();
		obj = null;
	},
	
    getDataComplete: function (args) {
		var result = args._result;
		var aryData, row;
		this.showNoneFoundOrContent(result.Count);
		//CR
		this._tblCR.clearTable();
		for (var i = 0; i < result.CRActivity.length; i++) {
			row = result.CRActivity[i];
			aryData = [
				$RGT_nubButton_CustomerRequirement(row.ID, row.No),
				$RGT_nubButton_Company(row.CMNo, row.CM),
				row.Date
				];
			this._tblCR.addRow(aryData, null);
			row = null;
		}
		$R_FN.showElement(this._pnlCR, result.CRActivity.length > 0);
		
		//GI
		this._tblGI.clearTable();
		for (i = 0; i < result.GIActivity.length; i++) {
			row = result.GIActivity[i];
			aryData = [
				$RGT_nubButton_GoodsIn(row.ID, row.No),
				$RGT_nubButton_Company(row.CMNo, row.CM),
				row.Date
				];
			this._tblGI.addRow(aryData, null);
			row = null;
		}
		$R_FN.showElement(this._pnlGI, result.GIActivity.length > 0);
		
		//QU
		this._tblQU.clearTable();
		for (i = 0; i < result.QUActivity.length; i++) {
			row = result.QUActivity[i];
			aryData = [
				$RGT_nubButton_Quote(row.ID, row.No),
				$RGT_nubButton_Company(row.CMNo, row.CM),
				row.Date
				];
			this._tblQU.addRow(aryData, null);
			row = null;
		}
		$R_FN.showElement(this._pnlQU, result.QUActivity.length > 0);
		
		//PO
		this._tblPO.clearTable();
		for (i = 0; i < result.POActivity.length; i++) {
			row = result.POActivity[i];
			aryData = [
				$RGT_nubButton_PurchaseOrder(row.ID, row.No),
				$RGT_nubButton_Company(row.CMNo, row.CM),
				row.Date
				];
			this._tblPO.addRow(aryData, null);
			row = null;
		}
		$R_FN.showElement(this._pnlPO, result.POActivity.length > 0);
		
		//SO
		this._tblSO.clearTable();
		for (i = 0; i < result.SOActivity.length; i++) {
			row = result.SOActivity[i];
			aryData = [
				$RGT_nubButton_SalesOrder(row.ID, row.No),
				$RGT_nubButton_Company(row.CMNo, row.CM),
				row.Date
				];
			this._tblSO.addRow(aryData, null);
			row = null;
		}
		$R_FN.showElement(this._pnlSO, result.SOActivity.length > 0);

		//CRMA
		this._tblCRMA.clearTable();
		for (i = 0; i < result.CRMAActivity.length; i++) {
			row = result.CRMAActivity[i];
			aryData = [
				$RGT_nubButton_CRMA(row.ID, row.No),
				$RGT_nubButton_Company(row.CMNo, row.CM),
				row.Date
				];
			this._tblCRMA.addRow(aryData, null);
			row = null;
		}
		$R_FN.showElement(this._pnlCRMA, result.CRMAActivity.length > 0);

		//SRMA
		this._tblSRMA.clearTable();
		for (i = 0; i < result.SRMAActivity.length; i++) {
			row = result.SRMAActivity[i];
			aryData = [
				$RGT_nubButton_SRMA(row.ID, row.No),
				$RGT_nubButton_Company(row.CMNo, row.CM),
				row.Date
				];
			this._tblSRMA.addRow(aryData, null);
			row = null;
		}
		$R_FN.showElement(this._pnlSRMA, result.SRMAActivity.length > 0);

		//Credit
		this._tblCredit.clearTable();
		for (i = 0; i < result.CreditActivity.length; i++) {
			row = result.CreditActivity[i];
			aryData = [
				$RGT_nubButton_CreditNote(row.ID, row.No),
				$RGT_nubButton_Company(row.CMNo, row.CM),
				row.Date
				];
			this._tblCredit.addRow(aryData, null);
			row = null;
		}
		$R_FN.showElement(this._pnlCredit, result.CreditActivity.length > 0);

		//Debit
		this._tblDebit.clearTable();
		for (i = 0; i < result.DebitActivity.length; i++) {
			row = result.DebitActivity[i];
			aryData = [
				$RGT_nubButton_DebitNote(row.ID, row.No),
				$RGT_nubButton_Company(row.CMNo, row.CM),
				row.Date
				];
			this._tblDebit.addRow(aryData, null);
			row = null;
		}
		$R_FN.showElement(this._pnlDebit, result.DebitActivity.length > 0);
		
		//Invoice
		this._tblInv.clearTable();
		for (i = 0; i < result.InvoiceActivity.length; i++) {
			row = result.InvoiceActivity[i];
			aryData = [
				$RGT_nubButton_Invoice(row.ID, row.No),
				$RGT_nubButton_Company(row.CMNo, row.CM),
				row.Date
				];
			this._tblInv.addRow(aryData, null);
			row = null;
		}
		$R_FN.showElement(this._pnlInv, result.InvoiceActivity.length > 0);
        
        //Open Supplier / PO Approvals
 
        this._tblOsPoApproval.clearTable();
        for (i = 0; i < result.POSupplierApprovalActivity.length; i++) {
            row = result.POSupplierApprovalActivity[i];
            aryData = [
                $RGT_nubButton_PurchaseOrder(row.ID, row.No),
                $RGT_nubButton_Company(row.CMNo, row.CM),
                row.Date
            ];
            this._tblOsPoApproval.addRow(aryData, null);
            row = null;
        }
        $R_FN.showElement(this._pnlOsPoApproval, result.POSupplierApprovalActivity.length > 0);

		this.hideLoading();
    }
     
};

Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyRecentActivity.registerClass("Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyRecentActivity", Rebound.GlobalTrader.Site.Controls.HomeNuggets.Base, Sys.IDisposable);
