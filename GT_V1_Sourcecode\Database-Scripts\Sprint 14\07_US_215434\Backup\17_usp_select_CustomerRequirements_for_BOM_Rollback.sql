﻿
GO


SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].[usp_select_CustomerRequirements_for_BOM] --178,101,115            
@BOMNo int ,                
@ClientID int            
,@CompanyNo int=0            
--,            
--@Note varchar(500) OUT            
AS                    
SELECT            
--CustomerRequirementId AS RequirementNumber,            
CustomerRequirementNumber AS RequirementNumber,
@CompanyNo AS Company,             
Quantity AS QuantityQuoted,            
(part + CASE WHEN ISNULL(AlternateStatus,0) =0  THEN '' ELSE ' ('+          
  CASE WHEN   AlternateStatus = 1 THEN 'Alternate' WHEN   AlternateStatus = 2 THEN 'Possible Alternate'      
  WHEN   AlternateStatus = 2 THEN 'Firm Alternate' END      
       +')' END) AS MPNQuoted,            
mf.ManufacturerName,            
DateCode,            
pk.Packagename AS PackageType,            
pr.ProductName AS ProductType,            
--New columns Added-Start            
'' AS SPQ,            
'' AS MOQ,            
'' AS LeadTimeWks,            
'' AS RohsYN,            
'' AS TQSA,            
'' AS LTB,            
'' AS FactorySealed,            
'' AS MSL,            
--New columns Added-End              
 UnitPrice = 0.00,            
'' AS SupplierNotes            
--,bom.Notes      
,convert(varchar(10),cr.DatePromised,103) AS RequiredDate   
 ,cast(dbo.ufn_convert_currency_value(cr.Price, cr.CurrencyNo, bom.CurrencyNo, bom.DLUP)as varchar(30))+' '+cur.CurrencyCode AS   TargetPrice   
 ,cr.Instructions CRInstructionsNotes              
FROM  dbo.tbCustomerRequirement cr             
LEFT JOIN dbo.tbProduct pr ON cr.ProductNo = pr.ProductId                      
LEFT JOIN dbo.tbPackage pk ON cr.PackageNo = pk.PackageId                      
LEFT JOIN dbo.tbManufacturer mf ON cr.ManufacturerNo = mf.ManufacturerId             
LEFT JOIN dbo.tbBom bom on cr.BomNo=bom.BomId            
LEFT JOIN dbo.tbCurrency Cur ON  bom.CurrencyNo=Cur.CurrencyId                     
where cr.BOMNo = @BOMNo  order by CustomerRequirementId               
--and cr.ClientNo = @ClientID             
            
--set @Note=(Select Notes from tbbom where BomId=@BOMNo)


GO


