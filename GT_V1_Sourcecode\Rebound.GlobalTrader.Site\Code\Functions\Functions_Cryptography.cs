using System;
using System.Collections.Generic;
using System.Text;
using System.Security.Cryptography;

namespace Rebound.GlobalTrader.Site {
	public static partial class Functions {

		public static string MD5Hash(string strIn) {
			MD5CryptoServiceProvider md5 = new System.Security.Cryptography.MD5CryptoServiceProvider();
			string strOut = "";
			byte[] bytData = md5.ComputeHash(System.Text.Encoding.ASCII.GetBytes(strIn));
			for (int i = 0; i < bytData.Length; i++) {
				strOut += bytData[i].ToString("x2");
			}
			md5 = null;
			bytData = null;
			return strOut.ToLower();
		}

		public static string EncryptString(string strMessage, string strPassphrase) {
			byte[] Results;
			System.Text.UTF8Encoding UTF8 = new System.Text.UTF8Encoding();

			// Step 1. We hash the passphrase using MD5
			// We use the MD5 hash generator as the result is a 128 bit byte array
			// which is a valid length for the TripleDES encoder we use below
			MD5CryptoServiceProvider HashProvider = new MD5CryptoServiceProvider();
			byte[] TDESKey = HashProvider.ComputeHash(UTF8.GetBytes(strPassphrase));

			// Step 2. Create a new TripleDESCryptoServiceProvider object
			TripleDESCryptoServiceProvider TDESAlgorithm = new TripleDESCryptoServiceProvider();

			// Step 3. Setup the encoder
			TDESAlgorithm.Key = TDESKey;
			TDESAlgorithm.Mode = CipherMode.ECB;
			TDESAlgorithm.Padding = PaddingMode.PKCS7;

			// Step 4. Convert the input string to a byte[]
			byte[] DataToEncrypt = UTF8.GetBytes(strMessage);

			// Step 5. Attempt to encrypt the string
			try {
				ICryptoTransform Encryptor = TDESAlgorithm.CreateEncryptor();
				Results = Encryptor.TransformFinalBlock(DataToEncrypt, 0, DataToEncrypt.Length);
			} finally {
				// Clear the TripleDes and Hashprovider services of any sensitive information
				TDESAlgorithm.Clear();
				HashProvider.Clear();
			}

			// Step 6. Return the encrypted string as a base64 encoded string
			return Convert.ToBase64String(Results);
		}

		public static string DecryptString(string strMessage, string strPassphrase) {
			if (String.IsNullOrEmpty(strMessage)) return "";
			byte[] Results;
			System.Text.UTF8Encoding UTF8 = new System.Text.UTF8Encoding();

			// Step 1. We hash the passphrase using MD5
			// We use the MD5 hash generator as the result is a 128 bit byte array
			// which is a valid length for the TripleDES encoder we use below
			MD5CryptoServiceProvider HashProvider = new MD5CryptoServiceProvider();
			byte[] TDESKey = HashProvider.ComputeHash(UTF8.GetBytes(strPassphrase));

			// Step 2. Create a new TripleDESCryptoServiceProvider object
			TripleDESCryptoServiceProvider TDESAlgorithm = new TripleDESCryptoServiceProvider();

			// Step 3. Setup the decoder
			TDESAlgorithm.Key = TDESKey;
			TDESAlgorithm.Mode = CipherMode.ECB;
			TDESAlgorithm.Padding = PaddingMode.PKCS7;

			// Step 4. Convert the input string to a byte[]
			byte[] DataToDecrypt = Convert.FromBase64String(strMessage);

			// Step 5. Attempt to decrypt the string
			try {
				ICryptoTransform Decryptor = TDESAlgorithm.CreateDecryptor();
				Results = Decryptor.TransformFinalBlock(DataToDecrypt, 0, DataToDecrypt.Length);
			} finally {
				// Clear the TripleDes and Hashprovider services of any sensitive information
				TDESAlgorithm.Clear();
				HashProvider.Clear();
			}

			// Step 6. Return the decrypted string in UTF8 format
			return UTF8.GetString(Results);
		}
	}
}
