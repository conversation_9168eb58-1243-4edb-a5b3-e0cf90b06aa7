Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.BOMCusReqSourcingResults_Delete=function(n){Rebound.GlobalTrader.Site.Controls.Forms.BOMCusReqSourcingResults_Delete.initializeBase(this,[n]);this._intSourcingResultID=-1;this._blnIsPOHub=!1};Rebound.GlobalTrader.Site.Controls.Forms.BOMCusReqSourcingResults_Delete.prototype={initialize:function(){Rebound.GlobalTrader.Site.Controls.Forms.BOMCusReqSourcingResults_Delete.callBaseMethod(this,"initialize");this.addShown(Function.createDelegate(this,this.formShown))},dispose:function(){this.isDisposed||(this._intSourcingResultID=null,this._ctlDelete=null,Rebound.GlobalTrader.Site.Controls.Forms.BOMCusReqSourcingResults_Delete.callBaseMethod(this,"dispose"))},formShown:function(){this._blnFirstTimeShown&&(this._ctlDelete=this.getFieldComponent("ctlDelete"),this._ctlDelete.addClickYesEvent(Function.createDelegate(this,this.yesClicked)),this._ctlDelete.addClickNoEvent(Function.createDelegate(this,this.noClicked)))},yesClicked:function(){this.showSaving(!0);var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/BOMCusReqSourcingResults");n.set_DataObject("BOMCusReqSourcingResults");n.set_DataAction("DeleteItem");n.addParameter("id",this._intSourcingResultID);n.addParameter("IsPOHub",this._blnIsPOHub);n.addParameter("ListOfIds",this._aryCurrentValues);n.addDataOK(Function.createDelegate(this,this.saveComplete));n.addError(Function.createDelegate(this,this.saveError));n.addTimeout(Function.createDelegate(this,this.saveError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},noClicked:function(){this.showSaving(!1);this.onNotConfirmed()},saveError:function(n){this.showSaving(!1);this._strErrorMessage=n._errorMessage;this.onSaveError()},saveComplete:function(n){this.showSaving(!1);n._result.Result?this.onSaveComplete():(this._strErrorMessage=n._errorMessage,this.onSaveError())}};Rebound.GlobalTrader.Site.Controls.Forms.BOMCusReqSourcingResults_Delete.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.BOMCusReqSourcingResults_Delete",Rebound.GlobalTrader.Site.Controls.Forms.Base,Sys.IDisposable);