﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE OR ALTER PROCEDURE [dbo].[usp_insert_ImportColumnMapping_PriceQuoteImport]                    
@strInsertMappings nvarchar(500)                    
AS                      
BEGIN                      
DECLARE @sql NVARCHAR(max)                      
SET @sql = 'insert into BorisGlobalTraderimports.dbo.tbImportMapping_PriceQuote (      
    [SupplierNo], [RequirementColumn],[AlternatePartColumn], [ManufacturerColumn], [PartColumn], [QuantityColumn], [PriceColumn], [DateCodeColumn],      
    [ProductColumn], [PackageColumn], [CurrencyColumn], [FixedCurrencyColumn],      
    [FileType], [DescriptionColumn], [SupplierNameColumn], [SupplierPartColumn], [ROHSColumn],      
    [SPQColumn], [MOQColumn], [QtyInStockColumn], [LeadTimeColumn], [OfferStatusColumn], [FactorySealedColumn], [Region]  
 ,[Last_Time_Buy],[Buy_Price],[Sell_PRICE],[Shipping_Cost],[DeliveryDateColumn],[MSLColumn], [ClientID]      
      
) values (' + @strInsertMappings+')'                      
EXEC sp_executesql  @sql, N'@strInsertMappings nvarchar(500)',                      
 @strInsertMappings = @strInsertMappings                      
END
GO

