Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");Rebound.GlobalTrader.Site.Controls.DropDowns.Group=function(n){Rebound.GlobalTrader.Site.Controls.DropDowns.Group.initializeBase(this,[n]);this._intGoodsInLineNo=-1;this._intInvoiceLineNo=-1};Rebound.GlobalTrader.Site.Controls.DropDowns.Group.prototype={get_intGoodsInLineNo:function(){return this._intGoodsInLineNo},set_intGoodsInLineNo:function(n){this._intGoodsInLineNo!==n&&(this._intGoodsInLineNo=n)},get_intInvoiceLineNo:function(){return this._intInvoiceLineNo},set_intInvoiceLineNo:function(n){this._intInvoiceLineNo!==n&&(this._intInvoiceLineNo=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.DropDowns.Group.callBaseMethod(this,"initialize");this.addSetupDataCall(Function.createDelegate(this,this.setupDataCall));this.addGetDataOK(Function.createDelegate(this,this.dataCallOK))},dispose:function(){this.isDisposed||(this._intGoodsInLineNo=null,this._intInvoiceLineNo=null,Rebound.GlobalTrader.Site.Controls.DropDowns.Group.callBaseMethod(this,"dispose"))},setupDataCall:function(){this._objData.set_PathToData("controls/DropDowns/Group");this._objData.set_DataObject("Group");this._objData.set_DataAction("GetData");this._objData.addParameter("GoodsInLineNo",this._intGoodsInLineNo);this._objData.addParameter("InvoiceLineNo",this._intInvoiceLineNo)},dataCallOK:function(){var t=this._objData._result,n;if(this._objData._result.Results.length>0)for(n=0;n<this._objData._result.Results.length;n++)this.addOption(this._objData._result.Results[n].Group+"   ("+this._objData._result.Results[n].RemainSerialNo+")",this._objData._result.Results[n].Group)}};Rebound.GlobalTrader.Site.Controls.DropDowns.Group.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.Group",Rebound.GlobalTrader.Site.Controls.DropDowns.Base);