///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference name="~/Controls/IconButton/IconButton.js" />
///<reference name="~/Controls/ReboundTextBox/ReboundTextBox.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Pages.Orders");

Rebound.GlobalTrader.Site.Pages.Orders.CreditDetail = function (el) {
    Rebound.GlobalTrader.Site.Pages.Orders.CreditDetail.initializeBase(this, [el]);
};

Rebound.GlobalTrader.Site.Pages.Orders.CreditDetail.prototype = {

    get_intCreditID: function () { return this._intCreditID; }, set_intCreditID: function (v) { if (this._intCreditID !== v) this._intCreditID = v; },
    get_ctlMainInfo: function () { return this._ctlMainInfo; }, set_ctlMainInfo: function (v) { if (this._ctlMainInfo !== v) this._ctlMainInfo = v; },
    get_ctlLines: function () { return this._ctlLines; }, set_ctlLines: function (v) { if (this._ctlLines !== v) this._ctlLines = v; },
    get_btnPrint: function () { return this._btnPrint; }, set_btnPrint: function (v) { if (this._btnPrint !== v) this._btnPrint = v; },

    initialize: function () {
        Rebound.GlobalTrader.Site.Pages.Orders.CreditDetail.callBaseMethod(this, "initialize");
    },

    goInit: function () {
        if (this._btnPrint) this._btnPrint.addPrint(Function.createDelegate(this, this.printCreditNote));
        if (this._btnPrint) this._btnPrint.addEmail(Function.createDelegate(this, this.emailCreditNote));
        if (this._ctlMainInfo) this._ctlMainInfo.addGetDataComplete(Function.createDelegate(this, this.ctlMainInfo_GetDataComplete));
        // Added Button Email Credit (HTML Format)  - Suhail
        if (this._btnPrint) this._btnPrint.addExtraButtonClick(Function.createDelegate(this, this.printOtherDocs));  
        Rebound.GlobalTrader.Site.Pages.Orders.CreditDetail.callBaseMethod(this, "goInit");
    },

    dispose: function () {
        if (this.isDisposed) return;
        if (this._ctlMainInfo) this._ctlMainInfo.dispose();
        if (this._ctlLines) this._ctlLines.dispose();
        if (this._btnPrint) this._btnPrint.dispose();
        this._btnPrint = null;
        this._ctlMainInfo = null;
        this._ctlLines = null;
        this._intCreditID = null;
        Rebound.GlobalTrader.Site.Pages.Orders.CreditDetail.callBaseMethod(this, "dispose");
    },

    printCreditNote: function () {
        $R_FN.openPrintWindow($R_ENUM$PrintObject.CreditNote, this._intCreditID);
    },

    emailCreditNote: function () {
        $R_FN.openPrintWindow($R_ENUM$PrintObject.CreditNote, this._intCreditID, true);
    },

    // Added functionality of Email Credit (HTML Format) to invoke method CREmail  - Suhail
    printOtherDocs: function () {
        if (this._btnPrint._strExtraButtonClickCommand == "EmailCRHTML") $R_FN.openPrintWindow($R_ENUM$PrintObject.CREmail, this._intCreditID, true);
        if (this._btnPrint._strExtraButtonClickCommand == "PrintLog") $R_FN.openPrintLogWindow($R_ENUM$PrintObject.PrintLog, this._intCreditID, false, "CreditNote");
        if (this._btnPrint._strExtraButtonClickCommand == "PrintCreditXML") $R_FN.openPrintWindow($R_ENUM$PrintObject.XmlCredit, this._intCreditID);
        if (this._btnPrint._strExtraButtonClickCommand == "EmailCreditXML") $R_FN.openPrintWindow($R_ENUM$PrintObject.XmlCredit, this._intCreditID, true);

    }, 

    ctlMainInfo_GetDataComplete: function () {
        this._ctlLines.getData();
        this.setLineFieldsFromHeader();
    },

    setLineFieldsFromHeader: function () {
        var strCustomerName = this._ctlMainInfo.getFieldValue("hidCustomerName");
        var strCreditNumber = this._ctlMainInfo.getFieldValue("hidCreditNumber");
        var strCurrencyCode = this._ctlMainInfo.getFieldValue("hidCurrencyCode");
        var intCurrencyNo = this._ctlMainInfo.getFieldValue("hidCurrencyNo");
        var strCreditDate = this._ctlMainInfo.getFieldValue("ctlCreditDate");
        var strReferenceDate = this._ctlMainInfo.getFieldValue("ctlReferenceDate");
        if (this._ctlLines._frmEdit) this._ctlLines._frmEdit.setCurrency(this._ctlMainInfo.getFieldValue("hidCurrencyCode"));
        if (this._ctlLines._frmAdd) this._ctlLines._frmAdd.setFieldsFromHeader(strCreditNumber, strCustomerName, intCurrencyNo, strCurrencyCode, strCreditDate, this._ctlMainInfo.getFieldValue("hidInvoiceNumber"), this._ctlMainInfo.getFieldValue("hidCRMANumber"), strReferenceDate, this._ctlMainInfo.getFieldValue("hidIsClientInvoice"), this._ctlMainInfo._InvoiceNo, this._ctlMainInfo.getFieldValue("hidClientInvoiceLineNo"));
        if (this._ctlLines) this._ctlLines._blnFronClientInvoice = this._ctlMainInfo.getFieldValue("hidIsClientInvoice");
        if (this._ctlLines) this._ctlLines._blnHubLogin = this._ctlMainInfo.getFieldValue("hidHubLogin");
      
        if (this._ctlLines) this._ctlLines._blnExported = this._ctlMainInfo._blnExported;

        strCustomerName = null;
        strCreditNumber = null;
        strCurrencyCode = null;
        intCurrencyNo = null;
        strCreditDate = null;
        strReferenceDate = null;
    }

};

Rebound.GlobalTrader.Site.Pages.Orders.CreditDetail.registerClass("Rebound.GlobalTrader.Site.Pages.Orders.CreditDetail", Rebound.GlobalTrader.Site.Pages.Content, Sys.IDisposable);
