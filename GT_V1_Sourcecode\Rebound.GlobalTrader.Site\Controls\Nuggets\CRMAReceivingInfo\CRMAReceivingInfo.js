Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Nuggets");Rebound.GlobalTrader.Site.Controls.Nuggets.CRMAReceivingInfo=function(n){Rebound.GlobalTrader.Site.Controls.Nuggets.CRMAReceivingInfo.initializeBase(this,[n]);this._intCRMAID=-1;this._intDataCalls=0};Rebound.GlobalTrader.Site.Controls.Nuggets.CRMAReceivingInfo.prototype={get_intCRMAID:function(){return this._intCRMAID},set_intCRMAID:function(n){this._intCRMAID!==n&&(this._intCRMAID=n)},get_fldReceived:function(){return this._fldReceived},set_fldReceived:function(n){this._fldReceived!==n&&(this._fldReceived=n)},get_tblReceived:function(){return this._tblReceived},set_tblReceived:function(n){this._tblReceived!==n&&(this._tblReceived=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Nuggets.CRMAReceivingInfo.callBaseMethod(this,"initialize");this.addRefreshEvent(Function.createDelegate(this,this.getData));this._fldReceived.addShown(Function.createDelegate(this,this.onShownReceived));this._fldReceived.addRefresh(Function.createDelegate(this,this.onRefreshReceived));this.getData()},dispose:function(){this.isDisposed||(this._fldReceived&&this._fldReceived.dispose(),this._tblReceived&&this._tblReceived.dispose(),this._intDataCalls=null,this._intCRMAID=null,this._fldReceived=null,this._tblReceived=null,Rebound.GlobalTrader.Site.Controls.Nuggets.CRMAReceivingInfo.callBaseMethod(this,"dispose"))},getData:function(){this.getData_Start();this._intDataCalls+=1;var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/CRMAMainInfo");n.set_DataObject("CRMAMainInfo");n.set_DataAction("GetData");n.addParameter("id",this._intCRMAID);n.addDataOK(Function.createDelegate(this,this.getDataOK));n.addError(Function.createDelegate(this,this.getDataError));n.addTimeout(Function.createDelegate(this,this.getDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();this.getReceived();n=null},getDataOK:function(n){var t=n._result;this.setFieldValue("hidNo",t.CRMANumber);this.setFieldValue("ctlCustomerName",$RGT_nubButton_Company(t.CustomerNo,t.Customer,null,null,null,t.CustomerAdvisoryNotes));this.setFieldValue("hidCustomer",$R_FN.setCleanTextValue(t.Customer));this.setFieldValue("hidCustomerNo",t.CustomerNo);this.setFieldValue("ctlWarehouse",$R_FN.setCleanTextValue(t.Warehouse));this.setFieldValue("hidWarehouseNo",t.WarehouseNo);this.setFieldValue("ctlContact",$RGT_nubButton_Contact(t.ContactNo,t.Contact));this.setFieldValue("hidContactNo",t.ContactNo);this.setFieldValue("ctlAuthoriser",$R_FN.setCleanTextValue(t.Authoriser));this.setFieldValue("hidAuthorisedBy",t.AuthorisedBy);this.setFieldValue("ctlDivision",$R_FN.setCleanTextValue(t.Division));this.setFieldValue("hidDivisionNo",t.DivisionNo);this.setFieldValue("ctlRMADate",t.RMADate);this.setFieldValue("ctlInvoice",t.Invoice,$RGT_nubButton_Invoice(t.InvoiceNo,t.Invoice));this.setFieldValue("hidInvoiceNo",t.InvoiceNo);this.setFieldValue("ctlSalesOrder",$RGT_nubButton_SalesOrder(t.SalesOrderNo,t.SalesOrder));this.setFieldValue("hidSalesOrderNo",t.SalesOrderNo);this.setFieldValue("ctlShipVia",$R_FN.setCleanTextValue(t.ShipVia));this.setFieldValue("hidShipViaNo",t.ShipViaNo);this.setFieldValue("ctlShippingAccountNo",$R_FN.setCleanTextValue(t.ShippingAccountNo));this.setFieldValue("ctlNotes",$R_FN.setCleanTextValue(t.Notes));this.setFieldValue("ctlInstructions",$R_FN.setCleanTextValue(t.Instructions));this.setFieldValue("ctlCurrency",$R_FN.setCleanTextValue(t.Currency));this.setFieldValue("hidCurrencyNo",t.CurrencyNo);this.setFieldValue("hidGlobalClientNo",t.ClientNo);this.setDLUP(t.DLUP);this.getDataOK_End();this.finishDataCall()},getDataError:function(n){this.showError(!0,n.get_ErrorMessage());this.finishDataCall()},getReceived:function(){this._intDataCalls+=1;this.showLoading(!0);this._fldReceived.showLoading(!0);var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/CRMAMainInfo");n.set_DataObject("CRMAMainInfo");n.set_DataAction("GetReceived");n.addParameter("id",this._intCRMAID);n.addDataOK(Function.createDelegate(this,this.getReceivedOK));n.addError(Function.createDelegate(this,this.getReceivedError));n.addTimeout(Function.createDelegate(this,this.getReceivedError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getReceivedError:function(n){this._fldReceived.showError(!0,n.get_ErrorMessage());this.finishDataCall()},getReceivedOK:function(n){var i,r,t,u;if(this._intDataCalls-=1,this._intDataCalls<1&&this.showLoading(!1),i=n._result,this._fldReceived.showContent(!0),this._fldReceived.resetCount(),this._tblReceived.clearTable(),this._fldReceived.updateCount(i.Count),i.Received)for(r=0;r<i.Received.length;r++)t=i.Received[r],u=[$RGT_nubButton_GoodsIn(t.GoodsInNo,t.GoodsInNumber),$R_FN.writeDoubleCellValue($RGT_nubButton_Stock(t.StockNo,t.PartNo,t.ROHS),$R_FN.setCleanTextValue(t.SupplierPart)),$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue($RGT_nubButton_Manufacturer(t.MfrNo,t.Mfr,t.MfrAdvisoryNotes)),$R_FN.setCleanTextValue(t.DC)),$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue($R_FN.setCleanTextValue(t.Product)),$R_FN.setCleanTextValue(t.Package)),$R_FN.writeDoubleCellValue(t.Quantity,$R_FN.setCleanTextValue(t.Location)),$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(t.Receiver),$R_FN.setCleanTextValue(t.ReceivedDate))],this._tblReceived.addRow(u,t.GoodsInNo,!1),t=null;this._tblReceived.resizeColumns();this.finishDataCall()},onShownReceived:function(){this._tblReceived.resizeColumns()},onRefreshReceived:function(){this.getReceived()},finishDataCall:function(){this._intDataCalls-=1;this._intDataCalls<1&&this.showLoading(!1)}};Rebound.GlobalTrader.Site.Controls.Nuggets.CRMAReceivingInfo.registerClass("Rebound.GlobalTrader.Site.Controls.Nuggets.CRMAReceivingInfo",Rebound.GlobalTrader.Site.Controls.Nuggets.Base);