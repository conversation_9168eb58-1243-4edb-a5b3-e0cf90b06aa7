///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.CreditLines_Edit = function(element) { 
	Rebound.GlobalTrader.Site.Controls.Forms.CreditLines_Edit.initializeBase(this, [element]);
	this._intLineID = -1;
	this._blnLineIsService = false;
	this._dblCurrencyRateToBase = 1;
	this._strCurrencyCode = "";
};

Rebound.GlobalTrader.Site.Controls.Forms.CreditLines_Edit.prototype = {

	get_lblCurrency: function() { return this._lblCurrency; }, set_lblCurrency: function(v) { if (this._lblCurrency !== v)  this._lblCurrency = v; }, 
	get_lblLandedCost: function() { return this._lblLandedCost; }, 	set_lblLandedCost: function(v) { if (this._lblLandedCost !== v)  this._lblLandedCost = v; }, 

	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.Forms.CreditLines_Edit.callBaseMethod(this, "initialize");
		this.addShown(Function.createDelegate(this, this.formShown));
	},

	formShown: function() {
		if (this._blnFirstTimeShown) {
			this.addSave(Function.createDelegate(this, this.saveClicked));
			$addHandler(this.getFieldControl("ctlLandedCost"), "change", Function.createDelegate(this, this.landedCostChanged));
		}
		this.showFields(true);
		this.landedCostChanged();
	},

	dispose: function() { 
		if (this.isDisposed) return;
		if (this.getFieldControl("ctlLandedCost")) $clearHandlers(this.getFieldControl("ctlLandedCost"));
		this._lblCurrency = null;
		this._lblLandedCost = null;
		Rebound.GlobalTrader.Site.Controls.Forms.CreditLines_Edit.callBaseMethod(this, "dispose");
	},

	landedCostChanged: function() {
		this.setLandedCostInCurrency($R_FN.formatCurrency(this.getFieldValue("ctlLandedCost") * this._dblCurrencyRateToBase, this._strCurrencyCode));
	},
	
	setLandedCostInCurrency: function(strCost) {
		$R_FN.setInnerHTML(this._lblLandedCost, String.format("({0})", strCost));
	},
	
	saveClicked: function() {
		if (!this.validateForm()) return;
		var obj = new Rebound.GlobalTrader.Site.Data();
		obj.set_PathToData("controls/Nuggets/CreditLines");
		obj.set_DataObject("CreditLines");
		obj.set_DataAction("SaveEdit");
		obj.addParameter("id", this._intLineID);
		obj.addParameter("Quantity", this.getFieldValue("ctlQuantity"));
		obj.addParameter("Price", this.getFieldValue("ctlPrice"));
		obj.addParameter("LandedCost", this.getFieldValue("ctlLandedCost"));
		obj.addParameter("LineIsService", this._blnLineIsService);
		obj.addParameter("LineNotes", this.getFieldValue("ctlLineNotes"));
		if (this._blnLineIsService) {
			obj.addParameter("Service", this.getFieldValue("ctlService"));
			obj.addParameter("ServiceDescription", this.getFieldValue("ctlServiceDescription"));
		}
		obj.addDataOK(Function.createDelegate(this, this.saveEditComplete));
		obj.addError(Function.createDelegate(this, this.saveEditError));
		obj.addTimeout(Function.createDelegate(this, this.saveEditError));
		$R_DQ.addToQueue(obj);
		$R_DQ.processQueue();
		obj = null;
	},

	saveEditError: function(args) {
		this._strErrorMessage = args._errorMessage;
		this.onSaveError();
	},
	
	saveEditComplete: function(args) {
		if (args._result.Result == true) {
			this.onSaveComplete();
		} else {
			this._strErrorMessage = args._errorMessage;
			this.onSaveError();
		}
	},	

	validateForm: function() {
		this.onValidate();
		var blnOK = true;
		if (this._blnLineIsService) {
			if (!this.checkFieldEntered("ctlService")) blnOK = false;
		}
		if (!this.checkFieldEntered("ctlQuantity")) blnOK = false;
		if (!this.checkFieldNumeric("ctlQuantity")) blnOK = false;
		if (!this.checkFieldEntered("ctlPrice")) blnOK = false;
		if (!this.checkFieldNumeric("ctlPrice")) blnOK = false;
		if (!blnOK) this.showError(true);
		return blnOK;
	},
	
	setCurrency: function(strCode) {
		$R_FN.setInnerHTML(this._lblCurrency, strCode);
	},
	
	showFields: function(bln) {
		this.showField("ctlQuantity", bln);
		this.showField("ctlPrice", bln);
		this.showField("ctlLandedCost", bln);
		this.showField("ctlPartNo", bln && !this._blnLineIsService);
		this.showField("ctlManufacturer", bln && !this._blnLineIsService);
		this.showField("ctlDateCode", bln && !this._blnLineIsService);
		this.showField("ctlPackage", bln && !this._blnLineIsService);
		this.showField("ctlProduct", bln && !this._blnLineIsService);
		this.showField("ctlService", bln && this._blnLineIsService);
		this.showField("ctlServiceDescription", bln && this._blnLineIsService);
	}
	
};

Rebound.GlobalTrader.Site.Controls.Forms.CreditLines_Edit.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.CreditLines_Edit", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
