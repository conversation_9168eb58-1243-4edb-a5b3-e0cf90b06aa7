﻿
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
ALTER PROCEDURE  [dbo].[usp_selectAll_InvoiceLine_for_Invoice]                         
                          
@InvoiceId int                          
                          
AS                          
--*******************************************************************************************                              
--*  [RP-2341] Ravi  17-10-2023  AS6081 Document printing      
--*******************************************************************************************                          
 DECLARE @Space NVARCHAR(100)='                                                   '                            
SELECT         
   DISTINCT        
   ivl.*                          
,  isnull((SELECT  Top 1(gc.GlobalCountryName)                          
    FROM    dbo.tbInvoiceLineAllocation ila                          
    LEFT                           
    JOIN dbo.tbGlobalCountryList gc                           
     ON ila.CountryOfManufactureNo = gc.GlobalCountryId                          
     WHERE   ila.InvoiceLineNo = ivl.InvoiceLineId),'') AS CountryOfManufactureName_old  ,                        
  ivl.CountryOfOrigin     AS CountryOfManufactureName ,              
     sol.SOSerialNo                       
  , dbo.ufn_get_serialno_byInvoiceLine(InvoiceLineId) as SerialLineNos                     
  , convert(varchar,sol.ContractNo) as ContractNo                  
  , so.AS9120                 
  ,  sol.ProductSource             
  ,sol.SalesOrderNo as SoSalesOrderNo               
  ,so.CustomerPO as SoCustomerPO            
  ,so.SalesOrderNumber as SoSalesOrderNumber            
  ,(case when es.OGELNumber>0 and so.OGEL_Required=1 then (select ct.OGELNumber from tbClient ct where  ct.ClientId=so.ClientNO) else '' end) as OGELNumber          
  -- [RP-2341] start      
  , case WHEN ISNULL(sr.TypeOfSupplierNo,0)>0 THEN  
   CONCAT(@Space+'Type of supplier', replicate(char(32), 90-len(@Space+'Type of supplier')-1), ': ', isnull(tos.Name,' ')              
  , CHAR(13),char(10) -- new line              
  ,@Space+'Reason for supplier choice', replicate(char(32), 83-len(@Space+'Reason for supplier choice')-1), ': ', isnull(rcs.Name,' ')               
  , CHAR(13),char(10) -- new line              
  ,@Space+'Risk of Supplier', replicate(char(32), 90.9-len(@Space+'Risk of Supplier')-1), ': ', isnull(ros.Name,' ')           
  , CHAR(13),char(10) -- new line              
  ,@Space+'Location',replicate(char(32), 94-len(@Space+'Location')-1), ': ', ISNULL(cou.CountryName , ' ' )            
  ) ELSE '' END AS AS6081PrintDetails        
  -- [RP-2341] end      
FROM vwInvoiceLine ivl                          
LEFT JOIN tbSalesOrderLine sol on isnull(ivl.SalesOrderLineNo,0)=isnull(sol.SalesOrderLineId,0)                      
left join tbSalesOrder so on so.SalesOrderId= sol.SalesOrderNo           
--RP-1560          
LEFT JOIN tbSO_ExportApprovalStatusOGEL es on isnull(ivl.SalesOrderLineNo,0) = isnull(es.SalesOrderLineNo,0)                
--end RP-1560         
-- [RP-2341] start      
  LEFT JOIN tbSourcingResult sr on sr.SourcingResultId = sol.SourcingResultNo      
  LEFT JOIN tbAS6081_TypeOfSupplier tos on tos.TypeOfSupplierId = sr.TypeOfSupplierNo        
        LEFT JOIN tbAS6081_ReasonForChosenSupplier rcs on rcs.ReasonForChosenSupplierId = sr.ReasonForSupplierNo        
        LEFT JOIN tbAS6081_RiskOfSupplier ros on ros.RiskOfSupplierId = sr.RiskOfSupplierNo        
        LEFT JOIN tbCountry cou on cou.CountryId = sr.CountryNo        
-- [RP-2341] end      
WHERE ivl.InvoiceNo = @InvoiceId                          
AND  ivl.Inactive = 0                          
ORDER BY ivl.InvoiceLineId 
