﻿//Marker     Changed by      Date         Remarks
//[001]      Vinay           04/09/2012   Print Label
//[002]      Vinay           08/07/2013    Ref:## -32 Nice Label Printing
//[003]      Vinay           28/08/2013    NPR Print
//[004]      <PERSON><PERSON><PERSON>         27/02/2014    GoodsIn - Po serial No.
//[005]     <PERSON><PERSON><PERSON>      26-Oct-2018    Issue - with GI line edit at a time in two tabs.
//[006]      <PERSON>     17/08/2021   (GTDP-198) Controlling specific product groups for purchasing

//[007]     Bhooma & Sunil      29-July-2021    To get All Infromation Of PO Variance.
//[008]     Abhinav <PERSON>xena     10/11/2021       work on the GI lines query messages.
//[009]     Abhinav <PERSON>xena     10/11/2021       New changes in GI query message screen.
//[010]     Abhinav <PERSON>a     22/12/2021       Add no reply email setting.
//[011]     Abhinav <PERSON>     29/12/2021       Add new properties and columns
//[012]     Ab<PERSON>av <PERSON>     13/01/2022       Add new property for approver add/remove logic.
//[013]     Ab<PERSON>av <PERSON>     19/01/2022       Add ability to CC any GT Users.
//[014]     Ab<PERSON>av <PERSON>     09/02/2022       Add new QueryHicStatus function.
//[006]     Bhooma & Sunil  29-July-2021    To get All Infromation Of PO Variance.
//[007]     Bhooma          03-Feb-2022    Change Short Shipment button as look like NPR.
// [008]    Ravi            21-03-2023      [RP-968] Barcode radio buttons and barcode
//[009]     Ravi            15-05-2023      [RP-616]
//[015]     Ravi            08-06-2023      [RP-1758]
//[016]     Ravi            19-09-2023      RP-2338  AS6081 Search/Filter functionality on different pages 
//[017]     Ravi            22/09/2023   RP-2339 AS6081 GT Documents - Show AS6081 on detail screens
//[RP-2546] Ravi            01-11-2023   GI Phase 2 Re-Open/ Released Issue
//[RP-881]   Ravi Bhushan    22-11-2023   New CR by client to show banner on GI Screen
//Code merge for GI Line
using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;
using Rebound.GlobalTrader.DAL;
using Rebound.GlobalTrader.BLL;

namespace Rebound.GlobalTrader.BLL
{
    public partial class GoodsInLine : BizObject
    {

        #region Properties

        protected static DAL.GoodsInLineElement Settings
        {
            get { return Globals.Settings.GoodsInLines; }
        }
        /// <summary>
        /// GoodsInLineId
        /// </summary>
        public System.Boolean? IsSTOGi { get; set; }
        /// <summary>
        /// GoodsInLineId
        /// </summary>
        public System.Int32 GoodsInLineId { get; set; }
        /// <summary>
        /// GoodsInNo
        /// </summary>
        public System.Int32 GoodsInNo { get; set; }
        /// <summary>
        /// PurchaseOrderLineNo
        /// </summary>
        public System.Int32? PurchaseOrderLineNo { get; set; }
        /// <summary>
        /// FullPart
        /// </summary>
        public System.String FullPart { get; set; }
        /// <summary>
        /// Part
        /// </summary>
        public System.String Part { get; set; }
        /// <summary>
        /// supplierType
        /// </summary>
        public System.String supplierType { get; set; }
        /// <summary>
        /// ManufacturerNo
        /// </summary>
        public System.Int32? ManufacturerNo { get; set; }
        /// <summary>
        /// DateCode
        /// </summary>
        public System.String DateCode { get; set; }
        /// <summary>
        /// PackageNo
        /// </summary>
        public System.Int32? PackageNo { get; set; }
        /// <summary>
        /// Quantity
        /// </summary>
        public System.Int32 Quantity { get; set; }
        /// <summary>
        /// Price
        /// </summary>
        public System.Double Price { get; set; }
        /// <summary>
        /// ShipInCost
        /// </summary>
        public System.Double? ShipInCost { get; set; }
        /// <summary>
        /// QualityControlNotes
        /// </summary>
        public System.String QualityControlNotes { get; set; }
        /// <summary>
        /// Location
        /// </summary>
        public System.String Location { get; set; }
        /// <summary>
        /// ProductNo
        /// </summary>
        public System.Int32? ProductNo { get; set; }
        /// <summary>
        /// LandedCost
        /// </summary>
        public System.Double LandedCost { get; set; }
        /// <summary>
        /// CustomerRMALineNo
        /// </summary>
        public System.Int32? CustomerRMALineNo { get; set; }
        /// <summary>
        /// SupplierPart
        /// </summary>
        public System.String SupplierPart { get; set; }
        /// <summary>
        /// ROHS
        /// </summary>
        public System.Byte? ROHS { get; set; }
        /// <summary>
        /// CountryOfManufacture
        /// </summary>
        public System.Int32? CountryOfManufacture { get; set; }
        /// <summary>
        /// UpdatedBy
        /// </summary>
        public System.Int32? UpdatedBy { get; set; }
        /// <summary>
        /// NoReplyId (from Table)
        /// </summary>
        public System.Int32? NoReplyId { get; set; }
        /// <summary>
        /// NoReplyId (from Table)
        /// </summary>
        public System.String NoReplyEmail { get; set; }
        /// <summary>
        /// DLUP
        /// </summary>
        public System.DateTime DLUP { get; set; }
        /// <summary>
        /// InspectedBy
        /// </summary>
        public System.Int32? InspectedBy { get; set; }
        /// <summary>
        /// DateInspected
        /// </summary>
        public System.DateTime? DateInspected { get; set; }
        /// <summary>
        /// CountingMethodNo
        /// </summary>
        public System.Int32? CountingMethodNo { get; set; }
        /// <summary>
        /// SerialNosRecorded
        /// </summary>
        public System.Boolean? SerialNosRecorded { get; set; }
        /// <summary>
        /// Unavailable
        /// </summary>
        public System.Boolean? Unavailable { get; set; }
        /// <summary>
		/// IsPDFAvailable
		/// </summary>
		public System.Boolean? IsPDFAvailable { get; set; }

        /// <summary>
        /// LotNo
        /// </summary>
        public System.Int32? LotNo { get; set; }
        /// <summary>
        /// Notes
        /// </summary>
        public System.String Notes { get; set; }
        /// <summary>
        /// PartMarkings
        /// </summary>
        public System.String PartMarkings { get; set; }
        /// <summary>
        /// FullSupplierPart
        /// </summary>
        public System.String FullSupplierPart { get; set; }
        /// <summary>
        /// GoodsInId
        /// </summary>
        public System.Int32 GoodsInId { get; set; }
        /// <summary>
        /// GoodsInNumber
        /// </summary>
        public System.Int32 GoodsInNumber { get; set; }
        /// <summary>
        /// ManufacturerCode
        /// </summary>
        public System.String ManufacturerCode { get; set; }
        /// <summary>
        /// DateReceived
        /// </summary>
        public System.DateTime DateReceived { get; set; }
        /// <summary>
        /// ReceiverName
        /// </summary>
        public System.String ReceiverName { get; set; }
        /// <summary>
        /// PurchaseOrderNo
        /// </summary>
        public System.Int32? PurchaseOrderNo { get; set; }
        /// <summary>
        /// DeliveryDate
        /// </summary>
        public System.DateTime? DeliveryDate { get; set; }
        /// <summary>
        /// PurchaseOrderNumber
        /// </summary>
        public System.Int32? PurchaseOrderNumber { get; set; }
        /// <summary>
        /// CompanyNo
        /// </summary>
        public System.Int32 CompanyNo { get; set; }
        /// <summary>
        /// CompanyName
        /// </summary>
        public System.String CompanyName { get; set; }
        /// <summary>
        /// AirWayBill
        /// </summary>
        public System.String AirWayBill { get; set; }
        /// <summary>
        /// RowNum
        /// </summary>
        public System.Int64? RowNum { get; set; }
        /// <summary>
        /// RowCnt
        /// </summary>
        public System.Int32? RowCnt { get; set; }
        /// <summary>
        /// QuantityOrdered
        /// </summary>
        public System.Int32 QuantityOrdered { get; set; }
        /// <summary>
        /// ContactNo
        /// </summary>
        public System.Int32 ContactNo { get; set; }
        /// <summary>
        /// ContactName
        /// </summary>
        public System.String ContactName { get; set; }
        /// <summary>
        /// SupplierInvoice
        /// </summary>
        public System.String SupplierInvoice { get; set; }
        /// <summary>
        /// InvoiceAmount
        /// </summary>
        public System.Double? InvoiceAmount { get; set; }
        /// <summary>
        /// CurrencyNo
        /// </summary>
        public System.Int32? CurrencyNo { get; set; }
        /// <summary>
        /// CurrencyCode
        /// </summary>
        public System.String CurrencyCode { get; set; }
        /// <summary>
        /// CountingMethodDescription
        /// </summary>
        public System.String CountingMethodDescription { get; set; }
        /// <summary>
        /// LineNotes
        /// </summary>
        public System.String LineNotes { get; set; }
        /// <summary>
        /// ClientNo
        /// </summary>
        public System.Int32 ClientNo { get; set; }
        /// <summary>
        /// PackageName
        /// </summary>
        public System.String PackageName { get; set; }
        /// <summary>
        /// PackageDescription
        /// </summary>
        public System.String PackageDescription { get; set; }
        /// <summary>
        /// ProductName
        /// </summary>
        public System.String ProductName { get; set; }
        /// <summary>
        /// ProductDescription
        /// </summary>
        public System.String ProductDescription { get; set; }
        /// <summary>
        /// ProductDutyCode
        /// </summary>
        public System.String ProductDutyCode { get; set; }
        /// <summary>
        /// ManufacturerName
        /// </summary>
        public System.String ManufacturerName { get; set; }
        /// <summary>
        /// CountryOfManufactureName
        /// </summary>
        public System.String CountryOfManufactureName { get; set; }
        /// <summary>
        /// InspectorName
        /// </summary>
        public System.String InspectorName { get; set; }
        /// <summary>
        /// CustomerRMANo
        /// </summary>
        public System.Int32? CustomerRMANo { get; set; }
        /// <summary>
        /// CustomerRMANumber
        /// </summary>
        public System.Int32? CustomerRMANumber { get; set; }
        /// <summary>
        /// CurrencyDescription
        /// </summary>
        public System.String CurrencyDescription { get; set; }
        /// <summary>
        /// ReceivedBy
        /// </summary>
        public System.Int32 ReceivedBy { get; set; }
        /// <summary>
        /// DivisionNo
        /// </summary>
        public System.Int32? DivisionNo { get; set; }
        /// <summary>
        /// TeamNo
        /// </summary>
        public System.Int32? TeamNo { get; set; }
        /// <summary>
        /// StockNo
        /// </summary>
        public System.Int32? StockNo { get; set; }
        /// <summary>
        /// Reference
        /// </summary>
        public System.String Reference { get; set; }
        /// <summary>
        /// LotName
        /// </summary>
        public System.String LotName { get; set; }
        /// <summary>
        /// PurchaseOrderLineShipInCost
        /// </summary>
        public System.Double PurchaseOrderLineShipInCost { get; set; }
        /// <summary>
        /// ChangedFields
        /// </summary>
        public System.String ChangedFields { get; set; }
        /// <summary>
        /// UpdateStock
        /// </summary>
        public System.Boolean? UpdateStock { get; set; }
        /// <summary>
        /// UpdateShipments
        /// </summary>
        public System.Boolean? UpdateShipments { get; set; }
        //[001] code start
        /// <summary>
        /// CustomerPart
        /// </summary>
        public System.String CustomerPart { get; set; }
        /// <summary>
        /// SalesOrderNumber
        /// </summary>
        public System.Int32? SalesOrderNumber { get; set; }
        /// <summary>
        /// DatePicked
        /// </summary>
        public System.DateTime DatePicked { get; set; }
        /// <summary>
        /// InspectedByUser
        /// </summary>
        public System.String InspectedByUser { get; set; }
        //[001] code end
        /// <summary>
        /// HasAllocationOutward
        /// </summary>
        public System.Boolean? HasAllocationOutward { get; set; }
        /// <summary>
        /// NPRPrinted
        /// </summary>
        public System.Boolean? NPRPrinted { get; set; }

        //[002] code start
        /// <summary>
        /// InspectorNameLabel
        /// </summary>
        public System.String InspectorNameLabel { get; set; }
        //[002] code end
        //[003] code start
        /// <summary>
        /// NPRIds
        /// </summary>
        public System.String NPRIds { get; set; }
        /// <summary>
        /// NPRNos
        /// </summary>
        public System.String NPRNos { get; set; }
        //[003] code end

        /// SupplierInvoiceExists
        /// </summary>
        public System.Boolean HasSupplierInvoiceExists { get; set; }

        /// HasStocksplit
        /// </summary>
        public System.Boolean HasStocksplit { get; set; }
        /// <summary>
        /// blnStockProvision
        /// </summary>
        public System.Boolean blnStockProvision { get; set; }
        /// <summary>
        /// PhysicalInspectedBy
        /// </summary>
        public System.Int32? PhysicalInspectedBy { get; set; }
        /// <summary>
        /// DatePhysicalInspected
        /// </summary>
        public System.DateTime? DatePhysicalInspected { get; set; }
        /// <summary>
        /// LotCode
        /// </summary>
        public System.String LotCode { get; set; }
        //[004] code start
        /// <summary>
        /// POSerialNo
        /// </summary>
        public System.Int16? POSerialNo { get; set; }

        public System.String ECCNCode { get; set; }

        //IHS code start
        //IHS End
        /// <summary>
        /// IHSPartsId
        /// </summary>
        public System.Int32? IHSPartsId { get; set; }
        /// <summary>
        /// OriginalEntryDate
        /// </summary>
        public System.DateTime? OriginalEntryDate { get; set; }
        /// <summary>
        /// Descriptions
        /// </summary>
        public System.String Descriptions { get; set; }
        public System.String IHSProduct { get; set; }
        /// <summary>
        /// CountryOfOrigin
        /// </summary>
        public System.String CountryOfOrigin { get; set; }
        /// <summary>
        /// CountryOfOriginNo
        /// </summary>
        public System.Int32? CountryOfOriginNo { get; set; }
        /// <summary>
        /// LifeCycleStage
        /// </summary>
        public System.String LifeCycleStage { get; set; }

        /// <summary>
        /// MSL
        /// </summary>
        public System.String MSL { get; set; }
        /// <summary>
        /// MSLNo
        /// </summary>
        public System.Int32? MSLNo { get; set; }
        /// <summary>
        /// AveragePrice
        /// </summary>
        public System.Double AveragePrice { get; set; }
        /// <summary>
        /// HTSCode
        /// </summary>
        public System.String HTSCode { get; set; }

        /// <summary>
        /// Packaging
        /// </summary>
        public System.String Packaging { get; set; }
        /// <summary>
        /// PackagingSize
        /// </summary>
        public System.String PackagingSize { get; set; }
        public System.String IHSCurrencyCode { get; set; }
        //[004] code End
        /// <summary>
        /// MSLName
        /// </summary>
        public System.String MSLName { get; set; }

        public int? InternalPurchaseOrderNumber { get; set; }
        public int? InternalPurchaseOrderId { get; set; }
        public int? IPOSupplier { get; set; }
        public string IPOSupplierName { get; set; }
        /// <summary>
        /// ClientPrice (from Table)
        /// </summary>
        public System.Double ClientPrice { get; set; }

        public string ClientName { get; set; }
        public System.Boolean? Quarantined { get; set; }

        public System.Int32? SalesGroupId { get; set; }
        public System.String SalesGroupName { get; set; }

        public System.Int32? PurchasingGroupId { get; set; }
        public System.String PurchasingGroupName { get; set; }

        public System.String ProcessPurchaseApproverName { get; set; }
        public System.String ProcessSalesApproverName { get; set; }
        public System.String ProcessQualityApproverName { get; set; }

        /// <summary>
        /// ClientLandedCost
        /// </summary>
        public System.Double ClientLandedCost { get; set; }
        public System.Int32? ClientCurrencyNo { get; set; }
        public System.String ClientCurrencyCode { get; set; }
        public System.Int32? IPOClientNo { get; set; }
        public System.Double? POBankFee { get; set; }
        /// <summary>
        /// CustomerPO
        /// </summary>
        public System.String CustomerPO { get; set; }
        public System.DateTime? StockDate { get; set; }
        public System.Double? DutyRate { get; set; }

        public System.String SerialNo { get; set; }
        public System.Int32? SerialNoId { get; set; }
        public System.String SubGroup { get; set; }
        public System.Boolean? ReqSerialNo { get; set; }
        public System.Int32? SerialNoCount { get; set; }
        public System.String Status { get; set; }
        public System.String Approvers { get; set; }
        public System.Int32? InvoiceLineNo { get; set; }
        public System.String MSLLevel { get; set; }
        public System.String ReasonDate { get; set; }
        public System.String ReasonCode { get; set; }
        public System.Boolean? IsProdHazardous { get; set; }
        public System.Boolean? PrintHazardous { get; set; }
        public System.Int32 GIShipCostHistoryId { get; set; }
        //[005] start
        public string StringDLUP { get; set; }
        //[005] end
        public System.Int32? ParentGoodsInLineId { get; set; }
        public System.Double? TotalShipCost { get; set; }
        public System.Int32? GIStatus { get; set; }
        public System.String MessageAuther { get; set; }
        public System.Int32? HICId { get; set; }
        public System.String HICStatus { get; set; }
        public System.Int32? QueryHICId { get; set; }
        public System.String QueryHICStatus { get; set; }

        public System.Boolean? IsFullQtyRecieved { get; set; }
        public System.Boolean? IsPartNoCorrect { get; set; }
        public System.String CorrectPartNo { get; set; }
        public System.Boolean? IsManufacturerCorrect { get; set; }
        public System.Int32? CorrectManufacturer { get; set; }
        public System.Boolean? IsDateCodeCorrect { get; set; }
        public System.String CorrectDateCode { get; set; }
        public System.Boolean? IsDateCodeRequired { get; set; }
        public System.Boolean? IsPackageTypeCorrect { get; set; }
        public System.Int32? CorrectPackageType { get; set; }
        public System.Boolean? IsMSLLevelCorrect { get; set; }
        public System.String CorrectMSLLevel { get; set; }
        public System.Int32? HIC_Status { get; set; }
        public System.Boolean? IsHICStatusCorrect { get; set; }
        public System.String CorrectHICStatus { get; set; }
        public System.String PKGBreakdownMismatch { get; set; }
        public System.Boolean? IsROHSStatusCorrect { get; set; }
        public System.Int32? CorrectROHSStatus { get; set; }
        public System.Boolean? IsLotCodesReq { get; set; }
        public System.Int32? BakingLevelAdded { get; set; }
        public System.Boolean? EnhancedInspectionReq { get; set; }
        public System.String GeneralInspectionNotes { get; set; }
        public System.Boolean? IsInspectionConducted { get; set; }
        public System.String CompanyType { get; set; }
        public System.String UpdateType { get; set; }
        public System.String CorrectManufacturerName { get; set; }
        public System.String CorrectPackageName { get; set; }
        public System.String GIQuery { get; set; }
        public System.Boolean IsSalesNotify { get; set; }
        public System.Boolean IsQualityNotify { get; set; }
        public System.Boolean IsPurchaseNotify { get; set; }
        public System.Int32? ID { get; set; }
        public System.String Name { get; set; }
        public System.String SalesQueryReply { get; set; }
        public System.String PurchaseQueryReply { get; set; }
        public System.String QualityQueryReply { get; set; }
        public System.Int32? SalesApprovalStatus { get; set; }
        public System.Int32? PurchaseApprovalStatus { get; set; }
        public System.Int32? QualityApprovalStatus { get; set; }
        public System.Boolean IsPDFReportRequired { get; set; }
        public System.Boolean IsQuarantineProduct { get; set; }
        public System.String GIQueryNumber { get; set; }
        public System.Boolean? InDraftMode { get; set; }
        public System.Int32? Gi_QueryId { get; set; }
        public System.String WarehouseRemark { get; set; }
        public System.Boolean? ISSalesPermission { get; set; }
        public System.Boolean? ISPurchasingPermission { get; set; }
        public System.Boolean? ISQualityPermission { get; set; }
        //[011] code start
        public System.Int32? ParentSalesApprovalStatus { get; set; }
        public System.Int32? ParentPurchaseApprovalStatus { get; set; }
        public System.Int32? ParentQualityApprovalStatus { get; set; }
        public System.String CurrentPurchasingApprover { get; set; }
        public System.String CurrentSalesApprover { get; set; }
        public System.String CCUsersName { get; set; }
        public System.String CurrentSalesApprovalStatus { get; set; }
        public System.String CurrentPurchasingApprovalStatus { get; set; }
        public System.String CurrentQualityApprovalStatus { get; set; }
        //[011] code end
        /// <summary>
        /// LotNoId
        /// </summary>
        public System.Int32? LotNoId { get; set; }
        public System.String LotNumber { get; set; }
        public System.Boolean? ReqLotNo { get; set; }
        public System.Int32? LotNoCount { get; set; }

        public System.Int32? SalesPersonId { get; set; }
        public System.String SalesPersonName { get; set; }
        //[006] code start
        public System.Boolean? IsOrderViaIPOonly { get; set; }
        //[006] code end
        public System.String SupplierMessage { get; set; }
        public System.Int32? SupportTeamMemberNo { get; set; }
        //[012] code start
        public System.Int32? PurchasingApproverId { get; set; }

        /// <summary>
        /// CCUSerId (from Table)
        /// </summary>
        public System.Int32? CCUSerId { get; set; }
        public System.Int32? SalesApproverId { get; set; }
        public System.String PreviousPurchasingApproverName { get; set; }
        public System.String PreviousSalesApproverName { get; set; }
        public System.Int32? PreviousPurchasingApproverId { get; set; }
        public System.Int32? PreviousSalesApproverId { get; set; }
        //[012] code end
        public System.String QueryMessage { get; set; }
        public System.String QueryMessageApproval { get; set; }
        public System.Boolean? NotifyToSales { get; set; }
        public System.Boolean? NotifyToQuality { get; set; }
        public System.Boolean? NotifyToPurchasing { get; set; }
        public System.Boolean? IsSendMail { get; set; }
        public System.Boolean? MyMessage { get; set; }
        public System.Boolean? ISInitialNotifyToSales { get; set; }
        public System.Boolean? ISInitialNotifyToQuality { get; set; }
        public System.Boolean? ISInitialNotifyToPurchase { get; set; }
        public System.Boolean? IsInitialMessage { get; set; }
        public System.Int32? Result { get; set; }
        public System.String RaisedBy { get; set; }
        public System.String Department { get; set; }
        public System.String ApprovalName { get; set; }
        public System.DateTime? ApprovedDate { get; set; }
        public System.String ApprovalStatus { get; set; }
        public System.String GoodInLineMessage { get; set; }
        /// <summary>
        ///  POPart
        /// </summary>
        public System.String POPart { get; set; }
        /// <summary>
        /// POManufacturerName
        /// </summary>
        public System.String POManufacturerName { get; set; }
        /// <summary>
        /// PODateCode
        /// </summary>
        public System.String PODateCode { get; set; }
        /// <summary>
        /// POPackageType
        /// </summary>
        public System.String POPackageType { get; set; }
        /// <summary>
        /// POMSLLevel
        /// </summary>
        public System.String POMSLLevel { get; set; }
        /// <summary>
        /// POROHSStatus
        /// </summary>
        public System.String POROHSStatus { get; set; }
        public System.Int32? POManufacturerNo { get; set; }
        public System.Int32? POPackageNo { get; set; }
        public System.Int32? POROHS { get; set; }
        public System.Int32? POQuantity { get; set; }
        public System.String ReleseStockDisbaleReason { get; set; }
        public System.Boolean? QueryRaised { get; set; }
        public System.Int32? ActeoneTestStatus { get; set; }
        public System.Int32? IsopropryleStatus { get; set; }
        public System.String ActeoneTest { get; set; }
        public System.String Isopropryle { get; set; }
        public System.String HICStatusName { get; set; }
        public System.Boolean? ReleseStockDisbaleStatus { get; set; }
        public System.String QueryBakingLevel { get; set; }
        public System.Int32? SalesOrderNo { get; set; }
        public System.Int32? EnhancedInspectionStatusId { get; set; }
        public System.Boolean? IsSendQuery { get; set; }
        public System.String DraftQueryMessage { get; set; }

        public System.Boolean? IsShortShipmentEnable { get; set; }
        public System.Int32? ShortShipmentId { get; set; }
        /// <summary>
        /// ShortShipmentIds
        /// </summary>
        public System.String ShortShipmentIds { get; set; }
        /// <summary>
        /// ShortShipmentNos
        /// </summary>
        public System.String ShortShipmentNos { get; set; }
        public System.String PrintDateCode { get; set; }
        public System.String SalesApproverIds { get; set; }
        public System.Boolean? IsCCMailGroupId { get; set; }
        public System.String MailType { get; set; }
        public System.String PIBy { get; set; }
        public System.String QueryRaisedBy { get; set; }
        public System.Int32? AllocatedSalesOrderNumber { get; set; }
        public System.Boolean? IsNotGBLPermissionForSales { get; set; }
        public System.Boolean? IsNotGBLPermissionForPurch { get; set; }
        public System.Boolean? IsNotGBLPermissionForQaulity { get; set; }

        public System.String AttachmentFileName { get; set; }
        public System.String AttachmentType { get; set; }
        //[008] start
        public System.Int32? HasBarcodeScan { get; set; }
        public System.String BarcodeScanRemarks { get; set; }
        public System.Boolean? IsStartInspection { get; set; }
        public System.Boolean? ISCloseInspection { get; set; }
        public System.Int32? InspectionLogId { get; set; }
        public System.String ActionTaken { get; set; }
        public System.String CommentText { get; set; }

        public System.Int32? GIBarcodeScanStatusId { get; set; }

        public System.String GIBarcodesScanStatusName { get; set; }

        public string PartNoQuery { get; set; }
        public string ManufacturerQuery { get; set; }
        public string PackagingTypeQuery { get; set; }
        public string MslQuery { get; set; }
        public string RohsQuery { get; set; }
        public System.Boolean? IsEditInspection { get; set; }
        public System.Boolean? IsAlreadyClosed { get; set; }

        public System.String PartNumberFilled { get; set; }
        public System.Boolean? PartNumberFilledChkEnable { get; set; }
        public System.String PackageBreakdownComplete { get; set; }
        public System.Boolean? PackageBreakdownCompleteChkEnable { get; set; }
        public System.String PhotosAttached { get; set; }
        public System.Boolean? PhotosAttachedChkEnable { get; set; }
        public System.String BarcodeScannedTicked { get; set; }
        public System.Boolean? BarcodeScannedTickedChkEnable { get; set; }
        public System.String QueryFormatUse { get; set; }
        public System.Boolean? QueryFormatUseChkEnable { get; set; }
        public System.Boolean? C1{ get; set; }
        public System.Boolean? C2{ get; set; }
        public System.Boolean? C3{ get; set; }
        public System.Boolean? C4{ get; set; }
        public System.Boolean? C5{ get; set; }
        public System.Boolean? C6{ get; set; }
        public System.Boolean? C7{ get; set; }
        public System.Boolean? C8{ get; set; }
        public System.Boolean? C9 { get; set; }
        public System.Boolean? C10 { get; set; }
        public System.Boolean? ISReleaseStock { get; set; }
        //[008]
        public System.Boolean? IsQueryColumn { get; set; }
        public System.Boolean? InvoiceExported { get; set; } //[009]

        public System.Boolean? GeneralInspectionQuery { get; set; }
        public System.Boolean? IsEditAfterStartInspection { get; set; }

        public System.Boolean AS6081 { get; set; } //[016]

        public System.String AlertMessage { get; set; } //[RP-2546]

        public System.Int32 InspectionStatus { get; set; } //[RP-2546]

        public System.Boolean IsSanctioned { get; set; } //[RP-881]

        public System.Int32? SerialId { get; set; }
        public System.String Box { get; set; }
        public System.String SerialNumber { get; set; }

        public System.String PartStatus { get; set; }

        public System.Boolean? APIImportedData { get; set; }
        #endregion

        #region Methods

        /// <summary>
        /// CountForPurchaseOrderLine
        /// Calls [usp_count_GoodsInLine_for_PurchaseOrderLine]
        /// </summary>
        public static Int32 CountForPurchaseOrderLine(System.Int32? purchaseOrderLineId)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.GoodsInLine.CountForPurchaseOrderLine(purchaseOrderLineId);
        }       
        /// <summary>
                /// DataListNugget
                /// Calls [usp_datalistnugget_GoodsInLine]
                /// </summary>
        public static List<GoodsInLine> DataListNugget(System.Int32? clientId, System.Int32? orderBy, System.Int32? sortDir, System.Int32? pageIndex, System.Int32? pageSize, System.String partSearch, System.String cmSearch, System.Int32? receivedBy, System.String airWayBill, System.Boolean? includeInvoiced, System.Int32? purchaseOrderNoLo, System.Int32? purchaseOrderNoHi, System.Int32? goodsInNoLo, System.Int32? goodsInNoHi, System.DateTime? dateReceivedFrom, System.DateTime? dateReceivedTo, System.String supplierInvoice, System.String reference, System.Boolean? recentOnly, System.Boolean? uninspectedOnly, System.Int32? clientSearch, int? IsPoHub, Boolean IsGlobalLogin, int? warehouse, System.Boolean? IsQueriedTab, System.Int32? QueryProgressStatus, System.Boolean? AS6081)
        {
            List<GoodsInLineDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.GoodsInLine.DataListNugget(clientId, orderBy, sortDir, pageIndex, pageSize, partSearch, cmSearch, receivedBy, airWayBill, includeInvoiced, purchaseOrderNoLo, purchaseOrderNoHi, goodsInNoLo, goodsInNoHi, dateReceivedFrom, dateReceivedTo, supplierInvoice, reference, recentOnly, uninspectedOnly, clientSearch, IsPoHub, IsGlobalLogin, warehouse, IsQueriedTab, QueryProgressStatus, AS6081); //[016]
            if (lstDetails == null)
            {
                return new List<GoodsInLine>();
            }
            else
            {
                List<GoodsInLine> lst = new List<GoodsInLine>();
                foreach (GoodsInLineDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.GoodsInLine obj = new Rebound.GlobalTrader.BLL.GoodsInLine();
                    obj.GoodsInId = objDetails.GoodsInId;
                    obj.GoodsInNumber = objDetails.GoodsInNumber;
                    obj.Part = objDetails.Part;
                    obj.ManufacturerNo = objDetails.ManufacturerNo;
                    obj.ManufacturerCode = objDetails.ManufacturerCode;
                    obj.Quantity = objDetails.Quantity;
                    obj.DateReceived = objDetails.DateReceived;
                    obj.ReceiverName = objDetails.ReceiverName;
                    obj.PurchaseOrderNo = objDetails.PurchaseOrderNo;
                    obj.DeliveryDate = objDetails.DeliveryDate;
                    obj.PurchaseOrderNumber = objDetails.PurchaseOrderNumber;
                    obj.ROHS = objDetails.ROHS;
                    obj.CompanyNo = objDetails.CompanyNo;
                    obj.CompanyName = objDetails.CompanyName;
                    obj.AirWayBill = objDetails.AirWayBill;
                    obj.RowNum = objDetails.RowNum;
                    obj.RowCnt = objDetails.RowCnt;
                    obj.InternalPurchaseOrderId = objDetails.InternalPurchaseOrderId;
                    obj.InternalPurchaseOrderNumber = objDetails.InternalPurchaseOrderNumber;
                    obj.IPOSupplier = objDetails.IPOSupplier;
                    obj.IPOSupplierName = objDetails.IPOSupplierName;
                    obj.ClientNo = objDetails.ClientNo;
                    obj.ClientName = objDetails.ClientName;
                    obj.GIStatus = objDetails.GIStatus;
                    obj.SupplierMessage = objDetails.SupplierMessage;
                    obj.GoodInLineMessage = objDetails.GoodInLineMessage;
                    obj.QueryRaised = objDetails.QueryRaised;
                    obj.GoodsInLineId = objDetails.GoodsInLineId;
                    obj.AS6081 = objDetails.AS6081; //[016]
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }


        /// DataListNugget IHS
        /// Calls [usp_datalistnugget_IHSCatalogue]
        /// </summary>
        public static List<GoodsInLine> DataListNuggetIHS(System.Int32? clientId, System.Int32? orderBy, System.Int32? sortDir, System.Int32? pageIndex, System.Int32? pageSize, System.String partSearch, System.String MfrSearch, System.Int32? countryOforigin, System.String MSL, System.String HtcCode, System.String Description, System.Boolean? recentOnly, int? IsPoHub)
        {
            List<GoodsInLineDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.GoodsInLine.DataListNuggetIHS(clientId, orderBy, sortDir, pageIndex, pageSize, partSearch, MfrSearch, countryOforigin, MSL, HtcCode, Description, recentOnly, IsPoHub);
            if (lstDetails == null)
            {
                return new List<GoodsInLine>();
            }
            else
            {
                List<GoodsInLine> lst = new List<GoodsInLine>();
                foreach (GoodsInLineDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.GoodsInLine obj = new Rebound.GlobalTrader.BLL.GoodsInLine();
                    obj.IHSPartsId = objDetails.IHSPartsId;
                    obj.Part = objDetails.Part;
                    obj.ManufacturerName = objDetails.ManufacturerName;
                    obj.ManufacturerNo = objDetails.ManufacturerNo;
                    obj.ManufacturerCode = objDetails.ManufacturerCode;
                    obj.Descriptions = objDetails.Descriptions;
                    obj.CountryOfOrigin = objDetails.CountryOfOrigin;
                    obj.CountryOfOriginNo = objDetails.CountryOfOriginNo;
                    obj.LifeCycleStage = objDetails.LifeCycleStage;
                    obj.MSL = objDetails.MSL;
                    obj.MSLNo = objDetails.MSLNo;
                    obj.HTSCode = objDetails.HTSCode;
                    obj.AveragePrice = objDetails.AveragePrice;
                    obj.Packaging = objDetails.Packaging;
                    obj.PackagingSize = objDetails.PackagingSize;
                    obj.MSLName = objDetails.MSLName;
                    obj.RowNum = objDetails.RowNum;
                    obj.RowCnt = objDetails.RowCnt;
                    obj.OriginalEntryDate = objDetails.OriginalEntryDate;
                    obj.IHSCurrencyCode = objDetails.IHSCurrencyCode;
                    obj.ECCNCode = objDetails.ECCNCode;
                    obj.IsPDFAvailable = objDetails.IsPDFAvailable;
                    obj.APIImportedData = objDetails.APIImportedData;

                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }

        /// <summary>
        /// DataListNuggetAsReceivedPO
        /// Calls [usp_datalistnugget_GoodsInLine_AsReceivedPO]
        /// </summary>
        public static List<GoodsInLine> DataListNuggetAsReceivedPO(System.Int32? clientId, System.Int32? teamId, System.Int32? divisionId, System.Int32? loginId, System.Int32? pageIndex, System.Int32? pageSize, System.Int32? orderBy, System.Int32? sortDir, System.Int32? purchaseOrderNoLo, System.Int32? purchaseOrderNoHi, System.String partSearch, System.Boolean? recentOnly, System.String cmSearch, System.String contactSearch, System.Int32? buyerSearch, System.DateTime? receivedDateFrom, System.DateTime? receivedDateTo, System.String airWayBill, System.String supplierPartSearch, System.String reference, bool? isPoHub)
        {
            List<GoodsInLineDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.GoodsInLine.DataListNuggetAsReceivedPO(clientId, teamId, divisionId, loginId, pageIndex, pageSize, orderBy, sortDir, purchaseOrderNoLo, purchaseOrderNoHi, partSearch, recentOnly, cmSearch, contactSearch, buyerSearch, receivedDateFrom, receivedDateTo, airWayBill, supplierPartSearch, reference, isPoHub);
            if (lstDetails == null)
            {
                return new List<GoodsInLine>();
            }
            else
            {
                List<GoodsInLine> lst = new List<GoodsInLine>();
                foreach (GoodsInLineDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.GoodsInLine obj = new Rebound.GlobalTrader.BLL.GoodsInLine();
                    obj.PurchaseOrderNo = objDetails.PurchaseOrderNo;
                    obj.PurchaseOrderNumber = objDetails.PurchaseOrderNumber;
                    obj.QuantityOrdered = objDetails.QuantityOrdered;
                    obj.GoodsInNo = objDetails.GoodsInNo;
                    obj.GoodsInNumber = objDetails.GoodsInNumber;
                    obj.DateReceived = objDetails.DateReceived;
                    obj.CompanyNo = objDetails.CompanyNo;
                    obj.CompanyName = objDetails.CompanyName;
                    obj.ContactNo = objDetails.ContactNo;
                    obj.ContactName = objDetails.ContactName;
                    obj.Quantity = objDetails.Quantity;
                    obj.Part = objDetails.Part;
                    obj.ROHS = objDetails.ROHS;
                    obj.ManufacturerNo = objDetails.ManufacturerNo;
                    obj.ManufacturerCode = objDetails.ManufacturerCode;
                    obj.SupplierPart = objDetails.SupplierPart;
                    obj.SupplierInvoice = objDetails.SupplierInvoice;
                    obj.AirWayBill = objDetails.AirWayBill;
                    obj.InvoiceAmount = objDetails.InvoiceAmount;
                    obj.CurrencyNo = objDetails.CurrencyNo;
                    obj.CurrencyCode = objDetails.CurrencyCode;
                    obj.RowNum = objDetails.RowNum;
                    obj.RowCnt = objDetails.RowCnt;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }
        /// <summary>
        /// Delete
        /// Calls [usp_delete_GoodsInLine]
        /// </summary>
        public static bool Delete(System.Int32? goodsInLineId, System.Int32? updatedBy)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.GoodsInLine.Delete(goodsInLineId, updatedBy);
        }
        /// <summary>
        /// Insert
        /// Calls [usp_insert_GoodsInLine]
        /// </summary>
        public static Int32 Insert(System.Int32? goodsInNo, System.Int32? purchaseOrderLineNo, System.String part, System.Int32? manufacturerNo, System.String dateCode, System.Int32? packageNo, System.Int32? quantity, System.Double? price, System.Double? shipInCost, System.String qualityControlNotes, System.String location, System.Int32? lotNo, System.Int32? productNo, System.Int32? currencyNo, System.Int32? customerRmaLineNo, System.String supplierPart, System.Byte? rohs, System.Int32? countryOfManufacture, System.Boolean? unavailable, System.String notes, System.String changedFields, System.Int32? countingMethodNo, System.Boolean? serialNosRecorded, System.String partMarkings, System.Int32? updatedBy, System.Double? clientPrice, System.Boolean? reqSerialNo, out System.String strMessage)
        {
            Int32 objReturn = Rebound.GlobalTrader.DAL.SiteProvider.GoodsInLine.Insert(goodsInNo, purchaseOrderLineNo, part, manufacturerNo, dateCode, packageNo, quantity, price, shipInCost, qualityControlNotes, location, lotNo, productNo, currencyNo, customerRmaLineNo, supplierPart, rohs, countryOfManufacture, unavailable, notes, changedFields, countingMethodNo, serialNosRecorded, partMarkings, updatedBy, clientPrice, reqSerialNo, out strMessage);
            return objReturn;
        }
        /// <summary>
        /// Insert (without parameters)
        /// Calls [usp_insert_GoodsInLine]
        /// </summary>
        public Int32 Insert()
        {
            string strMessage = "";
            return Rebound.GlobalTrader.DAL.SiteProvider.GoodsInLine.Insert(GoodsInNo, PurchaseOrderLineNo, Part, ManufacturerNo, DateCode, PackageNo, Quantity, Price, ShipInCost, QualityControlNotes, Location, LotNo, ProductNo, CurrencyNo, CustomerRMALineNo, SupplierPart, ROHS, CountryOfManufacture, Unavailable, Notes, ChangedFields, CountingMethodNo, SerialNosRecorded, PartMarkings, UpdatedBy, ClientPrice, ReqSerialNo, out strMessage);
        }
        public Int32 InsertIHS()
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.GoodsInLine.InsertIHS(Part, PartStatus, ManufacturerNo, OriginalEntryDate, PackageNo, Descriptions, HTSCode, CountryOfManufacture, PackageName, UpdatedBy, MSLLevel, ECCNCode);
        }

        /// <summary>
        /// Get
        /// Calls [usp_select_GoodsInLine]
        /// </summary>
        public static GoodsInLine Get(System.Int32? goodsInLineId, System.Int32? LoginId = 0, System.Int32? ClientId = 0)
        {
            Rebound.GlobalTrader.DAL.GoodsInLineDetails objDetails = Rebound.GlobalTrader.DAL.SiteProvider.GoodsInLine.Get(goodsInLineId, LoginId, ClientId);
            if (objDetails == null)
            {
                return null;
            }
            else
            {
                GoodsInLine obj = new GoodsInLine();
                obj.GoodsInLineId = objDetails.GoodsInLineId;
                obj.GoodsInNo = objDetails.GoodsInNo;
                obj.PurchaseOrderLineNo = objDetails.PurchaseOrderLineNo;
                obj.FullPart = objDetails.FullPart;
                obj.Part = objDetails.Part;
                obj.ManufacturerNo = objDetails.ManufacturerNo;
                obj.DateCode = objDetails.DateCode;
                obj.PackageNo = objDetails.PackageNo;
                obj.Quantity = objDetails.Quantity;
                obj.Price = objDetails.Price;
                obj.ShipInCost = objDetails.ShipInCost;
                obj.QualityControlNotes = objDetails.QualityControlNotes;
                obj.Location = objDetails.Location;
                obj.ProductNo = objDetails.ProductNo;
                obj.LandedCost = objDetails.LandedCost;
                obj.CustomerRMALineNo = objDetails.CustomerRMALineNo;
                obj.SupplierPart = objDetails.SupplierPart;
                obj.ROHS = objDetails.ROHS;
                obj.CountryOfManufacture = objDetails.CountryOfManufacture;
                obj.InspectedBy = objDetails.InspectedBy;
                obj.DateInspected = objDetails.DateInspected;
                obj.CountingMethodNo = objDetails.CountingMethodNo;
                obj.CountingMethodDescription = objDetails.CountingMethodDescription;
                obj.SerialNosRecorded = objDetails.SerialNosRecorded;
                obj.PartMarkings = objDetails.PartMarkings;
                obj.Unavailable = objDetails.Unavailable;
                obj.LotNo = objDetails.LotNo;
                obj.LineNotes = objDetails.LineNotes;
                obj.UpdatedBy = objDetails.UpdatedBy;
                obj.DLUP = objDetails.DLUP;
                obj.GoodsInNumber = objDetails.GoodsInNumber;
                obj.ClientNo = objDetails.ClientNo;
                obj.AirWayBill = objDetails.AirWayBill;
                obj.PackageName = objDetails.PackageName;
                obj.CorrectPackageName = objDetails.CorrectPackageName;
                obj.PackageDescription = objDetails.PackageDescription;
                obj.ProductName = objDetails.ProductName;
                obj.ProductDescription = objDetails.ProductDescription;
                obj.ProductDutyCode = objDetails.ProductDutyCode;
                obj.ManufacturerName = objDetails.ManufacturerName;
                obj.CorrectManufacturerName = objDetails.CorrectManufacturerName;
                obj.ManufacturerCode = objDetails.ManufacturerCode;
                obj.CountryOfManufactureName = objDetails.CountryOfManufactureName;
                obj.DateReceived = objDetails.DateReceived;
                obj.InspectorName = objDetails.InspectorName;
                obj.PurchaseOrderNo = objDetails.PurchaseOrderNo;
                obj.PurchaseOrderNumber = objDetails.PurchaseOrderNumber;
                obj.CustomerRMANo = objDetails.CustomerRMANo;
                obj.CustomerRMANumber = objDetails.CustomerRMANumber;
                obj.CurrencyNo = objDetails.CurrencyNo;
                obj.CurrencyCode = objDetails.CurrencyCode;
                obj.CurrencyDescription = objDetails.CurrencyDescription;
                obj.ReceivedBy = objDetails.ReceivedBy;
                obj.ReceiverName = objDetails.ReceiverName;
                obj.DivisionNo = objDetails.DivisionNo;
                obj.TeamNo = objDetails.TeamNo;
                obj.CompanyNo = objDetails.CompanyNo;
                obj.CompanyName = objDetails.CompanyName;
                obj.StockNo = objDetails.StockNo;
                obj.SupplierInvoice = objDetails.SupplierInvoice;
                obj.Reference = objDetails.Reference;
                obj.LotName = objDetails.LotName;
                obj.QuantityOrdered = objDetails.QuantityOrdered;
                obj.PurchaseOrderLineShipInCost = objDetails.PurchaseOrderLineShipInCost;
                obj.ChangedFields = objDetails.ChangedFields;
                obj.UpdateStock = objDetails.UpdateStock;
                obj.UpdateShipments = objDetails.UpdateShipments;
                obj.HasAllocationOutward = objDetails.HasAllocationOutward;
                obj.CustomerPart = objDetails.CustomerPart;
                obj.SalesOrderNumber = objDetails.SalesOrderNumber;
                obj.NPRPrinted = objDetails.NPRPrinted;
                //[002] code start
                obj.InspectorNameLabel = objDetails.InspectorNameLabel;
                //[002] code end

                //[003] code start
                obj.NPRIds = objDetails.NPRIds;
                obj.NPRNos = objDetails.NPRNos;
                //[003] code end
                obj.HasStocksplit = objDetails.HasStocksplit;
                obj.HasSupplierInvoiceExists = objDetails.HasSupplierInvoiceExists;
                obj.blnStockProvision = objDetails.blnStockProvision;
                obj.LotCode = objDetails.LotCode;
                obj.ClientLandedCost = objDetails.ClientLandedCost;
                obj.ClientPrice = objDetails.ClientPrice;
                obj.InternalPurchaseOrderId = objDetails.InternalPurchaseOrderId;
                obj.ClientCurrencyNo = objDetails.ClientCurrencyNo;
                obj.ClientCurrencyCode = objDetails.ClientCurrencyCode;
                obj.IPOClientNo = objDetails.IPOClientNo;
                obj.POBankFee = objDetails.POBankFee;
                obj.CustomerPO = objDetails.CustomerPO;
                obj.StockDate = objDetails.StockDate;
                obj.ProductInactive = objDetails.ProductInactive;
                obj.DutyRate = objDetails.DutyRate;
                obj.ReqSerialNo = objDetails.ReqSerialNo;
                obj.SerialNoCount = objDetails.SerialNoCount;
                obj.MSLLevel = objDetails.MSLLevel;
                obj.IsProdHazardous = objDetails.IsProdHazardous;
                obj.PrintHazardous = objDetails.PrintHazardous;
                //[005] start
                obj.StringDLUP = objDetails.StringDLUP;
                //[005] end
                obj.ParentGoodsInLineId = objDetails.ParentGoodsInLineId;
                obj.TotalShipCost = objDetails.TotalShipCost;
                //ihs code start
                obj.CountryOfOrigin = objDetails.CountryOfOrigin;
                obj.LifeCycleStage = objDetails.LifeCycleStage;
                obj.HTSCode = objDetails.HTSCode;
                obj.AveragePrice = objDetails.AveragePrice;
                obj.Packaging = objDetails.Packaging;
                obj.PackagingSize = objDetails.PackagingSize;
                obj.Descriptions = objDetails.Descriptions;
                obj.IHSProduct = objDetails.IHSProduct;
                obj.ECCNCode = objDetails.ECCNCode;
                obj.SalesPersonId = objDetails.SalesPersonId;
                obj.SalesPersonName = objDetails.SalesPersonName;
                //[006] code start
                obj.IsOrderViaIPOonly = objDetails.IsOrderViaIPOonly;
                //[006] code end

                obj.IsFullQtyRecieved = objDetails.IsFullQtyRecieved;
                obj.IsPartNoCorrect = objDetails.IsPartNoCorrect;
                obj.CorrectPartNo = objDetails.CorrectPartNo;
                obj.IsManufacturerCorrect = objDetails.IsManufacturerCorrect;
                obj.CorrectManufacturer = objDetails.CorrectManufacturer;
                obj.IsDateCodeCorrect = objDetails.IsDateCodeCorrect;
                obj.CorrectDateCode = objDetails.CorrectDateCode;
                obj.IsDateCodeRequired = objDetails.IsDateCodeRequired;
                obj.IsPackageTypeCorrect = objDetails.IsPackageTypeCorrect;
                obj.CorrectPackageType = objDetails.CorrectPackageType;
                obj.IsMSLLevelCorrect = objDetails.IsMSLLevelCorrect;
                obj.CorrectMSLLevel = objDetails.CorrectMSLLevel;
                obj.HIC_Status = objDetails.HIC_Status;
                obj.IsHICStatusCorrect = objDetails.IsHICStatusCorrect;
                obj.CorrectHICStatus = objDetails.CorrectHICStatus;
                obj.PKGBreakdownMismatch = objDetails.PKGBreakdownMismatch;
                obj.IsROHSStatusCorrect = objDetails.IsROHSStatusCorrect;
                obj.CorrectROHSStatus = objDetails.CorrectROHSStatus;
                obj.IsLotCodesReq = objDetails.IsLotCodesReq;
                obj.BakingLevelAdded = objDetails.BakingLevelAdded;
                obj.EnhancedInspectionReq = objDetails.EnhancedInspectionReq;
                obj.GeneralInspectionNotes = objDetails.GeneralInspectionNotes;
                obj.IsInspectionConducted = objDetails.IsInspectionConducted;
                obj.CompanyType = objDetails.CompanyType;

                obj.ReqLotNo = objDetails.ReqLotNo;
                obj.LotNoCount = objDetails.LotNoCount;
                obj.SupportTeamMemberNo = objDetails.SupportTeamMemberNo;

                obj.POPart = objDetails.POPart;
                obj.POManufacturerNo = objDetails.POManufacturerNo;
                obj.POManufacturerName = objDetails.POManufacturerName;
                obj.PODateCode = objDetails.PODateCode;
                obj.POPackageNo = objDetails.POPackageNo;
                obj.POPackageType = objDetails.POPackageType;
                obj.POMSLLevel = objDetails.POMSLLevel;
                obj.POROHS = objDetails.POROHS;
                obj.POROHSStatus = objDetails.POROHSStatus;
                obj.ReleseStockDisbaleReason = objDetails.ReleseStockDisbaleReason;
                obj.ActeoneTestStatus = objDetails.ActeoneTestStatus;
                obj.IsopropryleStatus = objDetails.IsopropryleStatus;
                obj.ActeoneTest = objDetails.ActeoneTest;
                obj.Isopropryle = objDetails.Isopropryle;
                obj.HICStatusName = objDetails.HICStatusName;
                obj.ReleseStockDisbaleStatus = objDetails.ReleseStockDisbaleStatus;
                obj.UpdateType = objDetails.UpdateType;
                obj.QueryBakingLevel = objDetails.QueryBakingLevel;
                obj.EnhancedInspectionStatusId = objDetails.EnhancedInspectionStatusId;
                obj.IsSendQuery = objDetails.IsSendQuery;
                obj.IsShortShipmentEnable = objDetails.IsShortShipmentEnable;
                obj.ShortShipmentId = objDetails.ShortShipmentId;
                obj.SupportTeamMemberNo = objDetails.SupportTeamMemberNo;
                //[007] code start
                obj.ShortShipmentIds = objDetails.ShortShipmentIds;
                obj.ShortShipmentNos = objDetails.ShortShipmentNos;
                //[007] code end
                obj.PrintDateCode = objDetails.PrintDateCode;
                obj.SalesApproverIds = objDetails.SalesApproverIds;
                obj.AllocatedSalesOrderNumber = objDetails.AllocatedSalesOrderNumber;
                //[008] start
                obj.HasBarcodeScan = objDetails.HasBarcodeScan;
                obj.BarcodeScanRemarks = objDetails.BarcodeScanRemarks;
                //[008] end

                obj.IsStartInspection = objDetails.IsStartInspection;
                obj.ISCloseInspection = objDetails.ISCloseInspection;

                obj.PartNoQuery = objDetails.PartNoQuery;
                obj.ManufacturerQuery = objDetails.ManufacturerQuery;
                obj.PackagingTypeQuery = objDetails.PackagingTypeQuery;
                obj.MslQuery = objDetails.MslQuery;
                obj.RohsQuery = objDetails.RohsQuery;
                obj.IsEditInspection = objDetails.IsEditInspection;
                obj.IsAlreadyClosed = objDetails.IsAlreadyClosed;
                obj.InvoiceExported = objDetails.GiInvoiceExported;
                obj.GeneralInspectionQuery = objDetails.GeneralInspectionQuery;
                obj.IsEditAfterStartInspection = objDetails.IsEditAfterStartInspection;
                obj.AS6081 = objDetails.AS6081; //[017]
                obj.IsSanctioned = objDetails.IsSanctioned; //[RP-881]
                obj.IsSTOGi = objDetails.IsSTOGi;
                objDetails = null;
                return obj;
            }
        }

        /// <summary>
        /// Get
        /// Calls [usp_select_GoodsInLine]
        /// </summary>
        public static GoodsInLine GetTemp(System.Int32? goodsInLineId, System.Int32? LoginId = 0, System.Int32? ClientId = 0)
        {
            Rebound.GlobalTrader.DAL.GoodsInLineDetails objDetails = Rebound.GlobalTrader.DAL.SiteProvider.GoodsInLine.GetTemp(goodsInLineId, LoginId, ClientId);
            if (objDetails == null)
            {
                return null;
            }
            else
            {
                GoodsInLine obj = new GoodsInLine();
                obj.GoodsInLineId = objDetails.GoodsInLineId;
                obj.GoodsInNo = objDetails.GoodsInNo;
                obj.PurchaseOrderLineNo = objDetails.PurchaseOrderLineNo;
                obj.FullPart = objDetails.FullPart;
                obj.Part = objDetails.Part;
                obj.ManufacturerNo = objDetails.ManufacturerNo;
                obj.DateCode = objDetails.DateCode;
                obj.PackageNo = objDetails.PackageNo;
                obj.Quantity = objDetails.Quantity;
                obj.Price = objDetails.Price;
                obj.ShipInCost = objDetails.ShipInCost;
                obj.QualityControlNotes = objDetails.QualityControlNotes;
                obj.Location = objDetails.Location;
                obj.ProductNo = objDetails.ProductNo;
                obj.LandedCost = objDetails.LandedCost;
                obj.CustomerRMALineNo = objDetails.CustomerRMALineNo;
                obj.SupplierPart = objDetails.SupplierPart;
                obj.ROHS = objDetails.ROHS;
                obj.CountryOfManufacture = objDetails.CountryOfManufacture;
                obj.InspectedBy = objDetails.InspectedBy;
                obj.DateInspected = objDetails.DateInspected;
                obj.CountingMethodNo = objDetails.CountingMethodNo;
                obj.CountingMethodDescription = objDetails.CountingMethodDescription;
                obj.SerialNosRecorded = objDetails.SerialNosRecorded;
                obj.PartMarkings = objDetails.PartMarkings;
                obj.Unavailable = objDetails.Unavailable;
                obj.LotNo = objDetails.LotNo;
                obj.LineNotes = objDetails.LineNotes;
                obj.UpdatedBy = objDetails.UpdatedBy;
                obj.DLUP = objDetails.DLUP;
                obj.GoodsInNumber = objDetails.GoodsInNumber;
                obj.ClientNo = objDetails.ClientNo;
                obj.AirWayBill = objDetails.AirWayBill;
                obj.PackageName = objDetails.PackageName;
                obj.CorrectPackageName = objDetails.CorrectPackageName;
                obj.PackageDescription = objDetails.PackageDescription;
                obj.ProductName = objDetails.ProductName;
                obj.ProductDescription = objDetails.ProductDescription;
                obj.ProductDutyCode = objDetails.ProductDutyCode;
                obj.ManufacturerName = objDetails.ManufacturerName;
                obj.CorrectManufacturerName = objDetails.CorrectManufacturerName;
                obj.ManufacturerCode = objDetails.ManufacturerCode;
                obj.CountryOfManufactureName = objDetails.CountryOfManufactureName;
                obj.DateReceived = objDetails.DateReceived;
                obj.InspectorName = objDetails.InspectorName;
                obj.PurchaseOrderNo = objDetails.PurchaseOrderNo;
                obj.PurchaseOrderNumber = objDetails.PurchaseOrderNumber;
                obj.CustomerRMANo = objDetails.CustomerRMANo;
                obj.CustomerRMANumber = objDetails.CustomerRMANumber;
                obj.CurrencyNo = objDetails.CurrencyNo;
                obj.CurrencyCode = objDetails.CurrencyCode;
                obj.CurrencyDescription = objDetails.CurrencyDescription;
                obj.ReceivedBy = objDetails.ReceivedBy;
                obj.ReceiverName = objDetails.ReceiverName;
                obj.DivisionNo = objDetails.DivisionNo;
                obj.TeamNo = objDetails.TeamNo;
                obj.CompanyNo = objDetails.CompanyNo;
                obj.CompanyName = objDetails.CompanyName;
                obj.StockNo = objDetails.StockNo;
                obj.SupplierInvoice = objDetails.SupplierInvoice;
                obj.Reference = objDetails.Reference;
                obj.LotName = objDetails.LotName;
                obj.QuantityOrdered = objDetails.QuantityOrdered;
                obj.PurchaseOrderLineShipInCost = objDetails.PurchaseOrderLineShipInCost;
                obj.ChangedFields = objDetails.ChangedFields;
                obj.UpdateStock = objDetails.UpdateStock;
                obj.UpdateShipments = objDetails.UpdateShipments;
                obj.HasAllocationOutward = objDetails.HasAllocationOutward;
                obj.CustomerPart = objDetails.CustomerPart;
                obj.SalesOrderNumber = objDetails.SalesOrderNumber;
                obj.NPRPrinted = objDetails.NPRPrinted;
                //[002] code start
                obj.InspectorNameLabel = objDetails.InspectorNameLabel;
                //[002] code end

                //[003] code start
                obj.NPRIds = objDetails.NPRIds;
                obj.NPRNos = objDetails.NPRNos;
                //[003] code end
                obj.HasStocksplit = objDetails.HasStocksplit;
                obj.HasSupplierInvoiceExists = objDetails.HasSupplierInvoiceExists;
                obj.blnStockProvision = objDetails.blnStockProvision;
                obj.LotCode = objDetails.LotCode;
                obj.ClientLandedCost = objDetails.ClientLandedCost;
                obj.ClientPrice = objDetails.ClientPrice;
                obj.InternalPurchaseOrderId = objDetails.InternalPurchaseOrderId;
                obj.ClientCurrencyNo = objDetails.ClientCurrencyNo;
                obj.ClientCurrencyCode = objDetails.ClientCurrencyCode;
                obj.IPOClientNo = objDetails.IPOClientNo;
                obj.POBankFee = objDetails.POBankFee;
                obj.CustomerPO = objDetails.CustomerPO;
                obj.StockDate = objDetails.StockDate;
                obj.ProductInactive = objDetails.ProductInactive;
                obj.DutyRate = objDetails.DutyRate;
                obj.ReqSerialNo = objDetails.ReqSerialNo;
                obj.SerialNoCount = objDetails.SerialNoCount;
                obj.MSLLevel = objDetails.MSLLevel;
                obj.IsProdHazardous = objDetails.IsProdHazardous;
                obj.PrintHazardous = objDetails.PrintHazardous;
                //[005] start
                obj.StringDLUP = objDetails.StringDLUP;
                //[005] end
                obj.ParentGoodsInLineId = objDetails.ParentGoodsInLineId;
                obj.TotalShipCost = objDetails.TotalShipCost;
                //ihs code start
                obj.CountryOfOrigin = objDetails.CountryOfOrigin;
                obj.LifeCycleStage = objDetails.LifeCycleStage;
                obj.HTSCode = objDetails.HTSCode;
                obj.AveragePrice = objDetails.AveragePrice;
                obj.Packaging = objDetails.Packaging;
                obj.PackagingSize = objDetails.PackagingSize;
                obj.Descriptions = objDetails.Descriptions;
                obj.IHSProduct = objDetails.IHSProduct;
                obj.ECCNCode = objDetails.ECCNCode;
                obj.SalesPersonId = objDetails.SalesPersonId;
                obj.SalesPersonName = objDetails.SalesPersonName;
                //[006] code start
                obj.IsOrderViaIPOonly = objDetails.IsOrderViaIPOonly;
                //[006] code end

                obj.IsFullQtyRecieved = objDetails.IsFullQtyRecieved;
                obj.IsPartNoCorrect = objDetails.IsPartNoCorrect;
                obj.CorrectPartNo = objDetails.CorrectPartNo;
                obj.IsManufacturerCorrect = objDetails.IsManufacturerCorrect;
                obj.CorrectManufacturer = objDetails.CorrectManufacturer;
                obj.IsDateCodeCorrect = objDetails.IsDateCodeCorrect;
                obj.CorrectDateCode = objDetails.CorrectDateCode;
                obj.IsDateCodeRequired = objDetails.IsDateCodeRequired;
                obj.IsPackageTypeCorrect = objDetails.IsPackageTypeCorrect;
                obj.CorrectPackageType = objDetails.CorrectPackageType;
                obj.IsMSLLevelCorrect = objDetails.IsMSLLevelCorrect;
                obj.CorrectMSLLevel = objDetails.CorrectMSLLevel;
                obj.HIC_Status = objDetails.HIC_Status;
                obj.IsHICStatusCorrect = objDetails.IsHICStatusCorrect;
                obj.CorrectHICStatus = objDetails.CorrectHICStatus;
                obj.PKGBreakdownMismatch = objDetails.PKGBreakdownMismatch;
                obj.IsROHSStatusCorrect = objDetails.IsROHSStatusCorrect;
                obj.CorrectROHSStatus = objDetails.CorrectROHSStatus;
                obj.IsLotCodesReq = objDetails.IsLotCodesReq;
                obj.BakingLevelAdded = objDetails.BakingLevelAdded;
                obj.EnhancedInspectionReq = objDetails.EnhancedInspectionReq;
                obj.GeneralInspectionNotes = objDetails.GeneralInspectionNotes;
                obj.IsInspectionConducted = objDetails.IsInspectionConducted;
                obj.CompanyType = objDetails.CompanyType;

                obj.ReqLotNo = objDetails.ReqLotNo;
                obj.LotNoCount = objDetails.LotNoCount;
                obj.SupportTeamMemberNo = objDetails.SupportTeamMemberNo;

                obj.POPart = objDetails.POPart;
                obj.POManufacturerNo = objDetails.POManufacturerNo;
                obj.POManufacturerName = objDetails.POManufacturerName;
                obj.PODateCode = objDetails.PODateCode;
                obj.POPackageNo = objDetails.POPackageNo;
                obj.POPackageType = objDetails.POPackageType;
                obj.POMSLLevel = objDetails.POMSLLevel;
                obj.POROHS = objDetails.POROHS;
                obj.POROHSStatus = objDetails.POROHSStatus;
                obj.ReleseStockDisbaleReason = objDetails.ReleseStockDisbaleReason;
                obj.ActeoneTestStatus = objDetails.ActeoneTestStatus;
                obj.IsopropryleStatus = objDetails.IsopropryleStatus;
                obj.ActeoneTest = objDetails.ActeoneTest;
                obj.Isopropryle = objDetails.Isopropryle;
                obj.HICStatusName = objDetails.HICStatusName;
                obj.ReleseStockDisbaleStatus = objDetails.ReleseStockDisbaleStatus;
                obj.UpdateType = objDetails.UpdateType;
                obj.QueryBakingLevel = objDetails.QueryBakingLevel;
                obj.EnhancedInspectionStatusId = objDetails.EnhancedInspectionStatusId;
                obj.IsSendQuery = objDetails.IsSendQuery;
                obj.IsShortShipmentEnable = objDetails.IsShortShipmentEnable;
                obj.ShortShipmentId = objDetails.ShortShipmentId;
                obj.SupportTeamMemberNo = objDetails.SupportTeamMemberNo;
                //[007] code start
                obj.ShortShipmentIds = objDetails.ShortShipmentIds;
                obj.ShortShipmentNos = objDetails.ShortShipmentNos;
                //[007] code end
                obj.PrintDateCode = objDetails.PrintDateCode;
                obj.SalesApproverIds = objDetails.SalesApproverIds;
                obj.AllocatedSalesOrderNumber = objDetails.AllocatedSalesOrderNumber;
                //[008] start
                obj.HasBarcodeScan = objDetails.HasBarcodeScan;
                obj.BarcodeScanRemarks = objDetails.BarcodeScanRemarks;
                //[008] end

                obj.IsStartInspection = objDetails.IsStartInspection;
                obj.ISCloseInspection = objDetails.ISCloseInspection;

                obj.PartNoQuery = objDetails.PartNoQuery;
                obj.ManufacturerQuery = objDetails.ManufacturerQuery;
                obj.PackagingTypeQuery = objDetails.PackagingTypeQuery;
                obj.MslQuery = objDetails.MslQuery;
                obj.RohsQuery = objDetails.RohsQuery;
                obj.IsEditInspection = objDetails.IsEditInspection;
                obj.IsAlreadyClosed = objDetails.IsAlreadyClosed;
                obj.InvoiceExported = objDetails.GiInvoiceExported;
                obj.GeneralInspectionQuery = objDetails.GeneralInspectionQuery;
                objDetails = null;
                return obj;
            }
        }


        /// <summary>
        /// GetListForCustomerRMA
        /// Calls [usp_selectAll_GoodsInLine_for_CustomerRMA]
        /// </summary>
        public static List<GoodsInLine> GetListForCustomerRMA(System.Int32? customerRmaId)
        {
            List<GoodsInLineDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.GoodsInLine.GetListForCustomerRMA(customerRmaId);
            if (lstDetails == null)
            {
                return new List<GoodsInLine>();
            }
            else
            {
                List<GoodsInLine> lst = new List<GoodsInLine>();
                foreach (GoodsInLineDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.GoodsInLine obj = new Rebound.GlobalTrader.BLL.GoodsInLine();
                    obj.GoodsInLineId = objDetails.GoodsInLineId;
                    obj.GoodsInNo = objDetails.GoodsInNo;
                    obj.PurchaseOrderLineNo = objDetails.PurchaseOrderLineNo;
                    obj.FullPart = objDetails.FullPart;
                    obj.Part = objDetails.Part;
                    obj.ManufacturerNo = objDetails.ManufacturerNo;
                    obj.DateCode = objDetails.DateCode;
                    obj.PackageNo = objDetails.PackageNo;
                    obj.Quantity = objDetails.Quantity;
                    obj.Price = objDetails.Price;
                    obj.ShipInCost = objDetails.ShipInCost;
                    obj.QualityControlNotes = objDetails.QualityControlNotes;
                    obj.Location = objDetails.Location;
                    obj.ProductNo = objDetails.ProductNo;
                    obj.LandedCost = objDetails.LandedCost;
                    obj.CustomerRMALineNo = objDetails.CustomerRMALineNo;
                    obj.SupplierPart = objDetails.SupplierPart;
                    obj.ROHS = objDetails.ROHS;
                    obj.CountryOfManufacture = objDetails.CountryOfManufacture;
                    obj.InspectedBy = objDetails.InspectedBy;
                    obj.DateInspected = objDetails.DateInspected;
                    obj.CountingMethodNo = objDetails.CountingMethodNo;
                    obj.CountingMethodDescription = objDetails.CountingMethodDescription;
                    obj.SerialNosRecorded = objDetails.SerialNosRecorded;
                    obj.PartMarkings = objDetails.PartMarkings;
                    obj.Unavailable = objDetails.Unavailable;
                    obj.LotNo = objDetails.LotNo;
                    obj.LineNotes = objDetails.LineNotes;
                    obj.UpdatedBy = objDetails.UpdatedBy;
                    obj.DLUP = objDetails.DLUP;
                    obj.GoodsInNumber = objDetails.GoodsInNumber;
                    obj.ClientNo = objDetails.ClientNo;
                    obj.AirWayBill = objDetails.AirWayBill;
                    obj.PackageName = objDetails.PackageName;
                    obj.PackageDescription = objDetails.PackageDescription;
                    obj.ProductName = objDetails.ProductName;
                    obj.ProductDescription = objDetails.ProductDescription;
                    obj.ProductDutyCode = objDetails.ProductDutyCode;
                    obj.ManufacturerName = objDetails.ManufacturerName;
                    obj.ManufacturerCode = objDetails.ManufacturerCode;
                    obj.CountryOfManufactureName = objDetails.CountryOfManufactureName;
                    obj.DateReceived = objDetails.DateReceived;
                    obj.InspectorName = objDetails.InspectorName;
                    obj.PurchaseOrderNo = objDetails.PurchaseOrderNo;
                    obj.PurchaseOrderNumber = objDetails.PurchaseOrderNumber;
                    obj.CustomerRMANo = objDetails.CustomerRMANo;
                    obj.CustomerRMANumber = objDetails.CustomerRMANumber;
                    obj.CurrencyNo = objDetails.CurrencyNo;
                    obj.CurrencyCode = objDetails.CurrencyCode;
                    obj.CurrencyDescription = objDetails.CurrencyDescription;
                    obj.ReceivedBy = objDetails.ReceivedBy;
                    obj.ReceiverName = objDetails.ReceiverName;
                    obj.DivisionNo = objDetails.DivisionNo;
                    obj.TeamNo = objDetails.TeamNo;
                    obj.CompanyNo = objDetails.CompanyNo;
                    obj.CompanyName = objDetails.CompanyName;
                    obj.StockNo = objDetails.StockNo;
                    obj.SupplierInvoice = objDetails.SupplierInvoice;
                    obj.Reference = objDetails.Reference;
                    obj.LotName = objDetails.LotName;
                    obj.QuantityOrdered = objDetails.QuantityOrdered;
                    obj.PurchaseOrderLineShipInCost = objDetails.PurchaseOrderLineShipInCost;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }
        /// <summary>
        /// GetListForCustomerRMALine
        /// Calls [usp_selectAll_GoodsInLine_for_CustomerRMALine]
        /// </summary>
        public static List<GoodsInLine> GetListForCustomerRMALine(System.Int32? customerRmaLineId)
        {
            List<GoodsInLineDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.GoodsInLine.GetListForCustomerRMALine(customerRmaLineId);
            if (lstDetails == null)
            {
                return new List<GoodsInLine>();
            }
            else
            {
                List<GoodsInLine> lst = new List<GoodsInLine>();
                foreach (GoodsInLineDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.GoodsInLine obj = new Rebound.GlobalTrader.BLL.GoodsInLine();
                    obj.GoodsInLineId = objDetails.GoodsInLineId;
                    obj.GoodsInNo = objDetails.GoodsInNo;
                    obj.PurchaseOrderLineNo = objDetails.PurchaseOrderLineNo;
                    obj.FullPart = objDetails.FullPart;
                    obj.Part = objDetails.Part;
                    obj.ManufacturerNo = objDetails.ManufacturerNo;
                    obj.DateCode = objDetails.DateCode;
                    obj.PackageNo = objDetails.PackageNo;
                    obj.Quantity = objDetails.Quantity;
                    obj.Price = objDetails.Price;
                    obj.ShipInCost = objDetails.ShipInCost;
                    obj.QualityControlNotes = objDetails.QualityControlNotes;
                    obj.Location = objDetails.Location;
                    obj.ProductNo = objDetails.ProductNo;
                    obj.LandedCost = objDetails.LandedCost;
                    obj.CustomerRMALineNo = objDetails.CustomerRMALineNo;
                    obj.SupplierPart = objDetails.SupplierPart;
                    obj.ROHS = objDetails.ROHS;
                    obj.CountryOfManufacture = objDetails.CountryOfManufacture;
                    obj.InspectedBy = objDetails.InspectedBy;
                    obj.DateInspected = objDetails.DateInspected;
                    obj.CountingMethodNo = objDetails.CountingMethodNo;
                    obj.CountingMethodDescription = objDetails.CountingMethodDescription;
                    obj.SerialNosRecorded = objDetails.SerialNosRecorded;
                    obj.PartMarkings = objDetails.PartMarkings;
                    obj.Unavailable = objDetails.Unavailable;
                    obj.LotNo = objDetails.LotNo;
                    obj.LineNotes = objDetails.LineNotes;
                    obj.UpdatedBy = objDetails.UpdatedBy;
                    obj.DLUP = objDetails.DLUP;
                    obj.GoodsInNumber = objDetails.GoodsInNumber;
                    obj.ClientNo = objDetails.ClientNo;
                    obj.AirWayBill = objDetails.AirWayBill;
                    obj.PackageName = objDetails.PackageName;
                    obj.PackageDescription = objDetails.PackageDescription;
                    obj.ProductName = objDetails.ProductName;
                    obj.ProductDescription = objDetails.ProductDescription;
                    obj.ProductDutyCode = objDetails.ProductDutyCode;
                    obj.ManufacturerName = objDetails.ManufacturerName;
                    obj.ManufacturerCode = objDetails.ManufacturerCode;
                    obj.CountryOfManufactureName = objDetails.CountryOfManufactureName;
                    obj.DateReceived = objDetails.DateReceived;
                    obj.InspectorName = objDetails.InspectorName;
                    obj.PurchaseOrderNo = objDetails.PurchaseOrderNo;
                    obj.PurchaseOrderNumber = objDetails.PurchaseOrderNumber;
                    obj.CustomerRMANo = objDetails.CustomerRMANo;
                    obj.CustomerRMANumber = objDetails.CustomerRMANumber;
                    obj.CurrencyNo = objDetails.CurrencyNo;
                    obj.CurrencyCode = objDetails.CurrencyCode;
                    obj.CurrencyDescription = objDetails.CurrencyDescription;
                    obj.ReceivedBy = objDetails.ReceivedBy;
                    obj.ReceiverName = objDetails.ReceiverName;
                    obj.DivisionNo = objDetails.DivisionNo;
                    obj.TeamNo = objDetails.TeamNo;
                    obj.CompanyNo = objDetails.CompanyNo;
                    obj.CompanyName = objDetails.CompanyName;
                    obj.StockNo = objDetails.StockNo;
                    obj.SupplierInvoice = objDetails.SupplierInvoice;
                    obj.Reference = objDetails.Reference;
                    obj.LotName = objDetails.LotName;
                    obj.QuantityOrdered = objDetails.QuantityOrdered;
                    obj.PurchaseOrderLineShipInCost = objDetails.PurchaseOrderLineShipInCost;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }
        /// <summary>
        /// GetListForGoodsIn
        /// Calls [usp_selectAll_GoodsInLine_for_GoodsIn]
        /// </summary>
        public static List<GoodsInLine> GetListForGoodsIn(System.Int32? goodsInId, System.Int32? pageIndex, System.Int32? pageSize)
        {
            List<GoodsInLineDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.GoodsInLine.GetListForGoodsIn(goodsInId, pageIndex, pageSize);
            if (lstDetails == null)
            {
                return new List<GoodsInLine>();
            }
            else
            {
                List<GoodsInLine> lst = new List<GoodsInLine>();
                foreach (GoodsInLineDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.GoodsInLine obj = new Rebound.GlobalTrader.BLL.GoodsInLine();
                    obj.GoodsInLineId = objDetails.GoodsInLineId;
                    obj.GoodsInNo = objDetails.GoodsInNo;
                    obj.PurchaseOrderLineNo = objDetails.PurchaseOrderLineNo;
                    obj.FullPart = objDetails.FullPart;
                    obj.Part = objDetails.Part;
                    obj.ManufacturerNo = objDetails.ManufacturerNo;
                    obj.DateCode = objDetails.DateCode;
                    obj.PackageNo = objDetails.PackageNo;
                    obj.Quantity = objDetails.Quantity;
                    obj.Price = objDetails.Price;
                    obj.ShipInCost = objDetails.ShipInCost;
                    obj.QualityControlNotes = objDetails.QualityControlNotes;
                    obj.Location = objDetails.Location;
                    obj.ProductNo = objDetails.ProductNo;
                    obj.LandedCost = objDetails.LandedCost;
                    obj.CustomerRMALineNo = objDetails.CustomerRMALineNo;
                    obj.SupplierPart = objDetails.SupplierPart;
                    obj.ROHS = objDetails.ROHS;
                    obj.CountryOfManufacture = objDetails.CountryOfManufacture;
                    obj.InspectedBy = objDetails.InspectedBy;
                    obj.DateInspected = objDetails.DateInspected;
                    obj.CountingMethodNo = objDetails.CountingMethodNo;
                    obj.CountingMethodDescription = objDetails.CountingMethodDescription;
                    obj.SerialNosRecorded = objDetails.SerialNosRecorded;
                    obj.PartMarkings = objDetails.PartMarkings;
                    obj.Unavailable = objDetails.Unavailable;
                    obj.LotNo = objDetails.LotNo;
                    obj.LineNotes = objDetails.LineNotes;
                    obj.UpdatedBy = objDetails.UpdatedBy;
                    obj.DLUP = objDetails.DLUP;
                    obj.GoodsInNumber = objDetails.GoodsInNumber;
                    obj.ClientNo = objDetails.ClientNo;
                    obj.AirWayBill = objDetails.AirWayBill;
                    obj.PackageName = objDetails.PackageName;
                    obj.PackageDescription = objDetails.PackageDescription;
                    obj.ProductName = objDetails.ProductName;
                    obj.ProductDescription = objDetails.ProductDescription;
                    obj.ProductDutyCode = objDetails.ProductDutyCode;
                    obj.ManufacturerName = objDetails.ManufacturerName;
                    obj.ManufacturerCode = objDetails.ManufacturerCode;
                    obj.CountryOfManufactureName = objDetails.CountryOfManufactureName;
                    obj.DateReceived = objDetails.DateReceived;
                    obj.InspectorName = objDetails.InspectorName;
                    obj.PurchaseOrderNo = objDetails.PurchaseOrderNo;
                    obj.PurchaseOrderNumber = objDetails.PurchaseOrderNumber;
                    obj.CustomerRMANo = objDetails.CustomerRMANo;
                    obj.CustomerRMANumber = objDetails.CustomerRMANumber;
                    obj.CurrencyNo = objDetails.CurrencyNo;
                    obj.CurrencyCode = objDetails.CurrencyCode;
                    obj.CurrencyDescription = objDetails.CurrencyDescription;
                    obj.ReceivedBy = objDetails.ReceivedBy;
                    obj.ReceiverName = objDetails.ReceiverName;
                    obj.DivisionNo = objDetails.DivisionNo;
                    obj.TeamNo = objDetails.TeamNo;
                    obj.CompanyNo = objDetails.CompanyNo;
                    obj.CompanyName = objDetails.CompanyName;
                    obj.StockNo = objDetails.StockNo;
                    obj.SupplierInvoice = objDetails.SupplierInvoice;
                    obj.Reference = objDetails.Reference;
                    obj.LotName = objDetails.LotName;
                    obj.QuantityOrdered = objDetails.QuantityOrdered;
                    obj.PurchaseOrderLineShipInCost = objDetails.PurchaseOrderLineShipInCost;
                    obj.RowNum = objDetails.RowNum;
                    obj.RowCnt = objDetails.RowCnt;
                    obj.PhysicalInspectedBy = objDetails.PhysicalInspectedBy;
                    obj.DatePhysicalInspected = objDetails.DatePhysicalInspected;
                    //[004] code start
                    obj.POSerialNo = objDetails.POSerialNo;
                    //[004] code End
                    obj.ClientLandedCost = objDetails.ClientLandedCost;
                    obj.ClientPrice = objDetails.ClientPrice;
                    obj.InternalPurchaseOrderId = objDetails.InternalPurchaseOrderId;
                    obj.ParentGoodsInLineId = objDetails.ParentGoodsInLineId;
                    obj.QueryRaised = objDetails.QueryRaised;
                    obj.PrintDateCode = objDetails.PrintDateCode;
                    obj.Status = objDetails.Status;
                    obj.AS6081 = objDetails.AS6081; //[017]
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }
        /// <summary>
        /// GetListForPurchaseOrder
        /// Calls [usp_selectAll_GoodsInLine_for_PurchaseOrder]
        /// </summary>
        public static List<GoodsInLine> GetListForPurchaseOrder(System.Int32? purchaseOrderId)
        {
            List<GoodsInLineDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.GoodsInLine.GetListForPurchaseOrder(purchaseOrderId);
            if (lstDetails == null)
            {
                return new List<GoodsInLine>();
            }
            else
            {
                List<GoodsInLine> lst = new List<GoodsInLine>();
                foreach (GoodsInLineDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.GoodsInLine obj = new Rebound.GlobalTrader.BLL.GoodsInLine();
                    obj.GoodsInLineId = objDetails.GoodsInLineId;
                    obj.GoodsInNo = objDetails.GoodsInNo;
                    obj.PurchaseOrderLineNo = objDetails.PurchaseOrderLineNo;
                    obj.FullPart = objDetails.FullPart;
                    obj.Part = objDetails.Part;
                    obj.ManufacturerNo = objDetails.ManufacturerNo;
                    obj.DateCode = objDetails.DateCode;
                    obj.PackageNo = objDetails.PackageNo;
                    obj.Quantity = objDetails.Quantity;
                    obj.Price = objDetails.Price;
                    obj.ShipInCost = objDetails.ShipInCost;
                    obj.QualityControlNotes = objDetails.QualityControlNotes;
                    obj.Location = objDetails.Location;
                    obj.ProductNo = objDetails.ProductNo;
                    obj.LandedCost = objDetails.LandedCost;
                    obj.CustomerRMALineNo = objDetails.CustomerRMALineNo;
                    obj.SupplierPart = objDetails.SupplierPart;
                    obj.ROHS = objDetails.ROHS;
                    obj.CountryOfManufacture = objDetails.CountryOfManufacture;
                    obj.InspectedBy = objDetails.InspectedBy;
                    obj.DateInspected = objDetails.DateInspected;
                    obj.CountingMethodNo = objDetails.CountingMethodNo;
                    obj.CountingMethodDescription = objDetails.CountingMethodDescription;
                    obj.SerialNosRecorded = objDetails.SerialNosRecorded;
                    obj.PartMarkings = objDetails.PartMarkings;
                    obj.Unavailable = objDetails.Unavailable;
                    obj.LotNo = objDetails.LotNo;
                    obj.LineNotes = objDetails.LineNotes;
                    obj.UpdatedBy = objDetails.UpdatedBy;
                    obj.DLUP = objDetails.DLUP;
                    obj.GoodsInNumber = objDetails.GoodsInNumber;
                    obj.ClientNo = objDetails.ClientNo;
                    obj.AirWayBill = objDetails.AirWayBill;
                    obj.PackageName = objDetails.PackageName;
                    obj.PackageDescription = objDetails.PackageDescription;
                    obj.ProductName = objDetails.ProductName;
                    obj.ProductDescription = objDetails.ProductDescription;
                    obj.ProductDutyCode = objDetails.ProductDutyCode;
                    obj.ManufacturerName = objDetails.ManufacturerName;
                    obj.ManufacturerCode = objDetails.ManufacturerCode;
                    obj.CountryOfManufactureName = objDetails.CountryOfManufactureName;
                    obj.DateReceived = objDetails.DateReceived;
                    obj.InspectorName = objDetails.InspectorName;
                    obj.PurchaseOrderNo = objDetails.PurchaseOrderNo;
                    obj.PurchaseOrderNumber = objDetails.PurchaseOrderNumber;
                    obj.CustomerRMANo = objDetails.CustomerRMANo;
                    obj.CustomerRMANumber = objDetails.CustomerRMANumber;
                    obj.CurrencyNo = objDetails.CurrencyNo;
                    obj.CurrencyCode = objDetails.CurrencyCode;
                    obj.CurrencyDescription = objDetails.CurrencyDescription;
                    obj.ReceivedBy = objDetails.ReceivedBy;
                    obj.ReceiverName = objDetails.ReceiverName;
                    obj.DivisionNo = objDetails.DivisionNo;
                    obj.TeamNo = objDetails.TeamNo;
                    obj.CompanyNo = objDetails.CompanyNo;
                    obj.CompanyName = objDetails.CompanyName;
                    obj.StockNo = objDetails.StockNo;
                    obj.SupplierInvoice = objDetails.SupplierInvoice;
                    obj.Reference = objDetails.Reference;
                    obj.LotName = objDetails.LotName;
                    obj.QuantityOrdered = objDetails.QuantityOrdered;
                    obj.PurchaseOrderLineShipInCost = objDetails.PurchaseOrderLineShipInCost;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }
        /// <summary>
        /// GetListForPurchaseOrderLine
        /// Calls [usp_selectAll_GoodsInLine_for_PurchaseOrderLine]
        /// </summary>
        public static List<GoodsInLine> GetListForPurchaseOrderLine(System.Int32? purchaseOrderLineId)
        {
            List<GoodsInLineDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.GoodsInLine.GetListForPurchaseOrderLine(purchaseOrderLineId);
            if (lstDetails == null)
            {
                return new List<GoodsInLine>();
            }
            else
            {
                List<GoodsInLine> lst = new List<GoodsInLine>();
                foreach (GoodsInLineDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.GoodsInLine obj = new Rebound.GlobalTrader.BLL.GoodsInLine();
                    obj.GoodsInLineId = objDetails.GoodsInLineId;
                    obj.GoodsInNumber = objDetails.GoodsInNumber;
                    obj.GoodsInNo = objDetails.GoodsInNo;
                    obj.Part = objDetails.Part;
                    obj.ROHS = objDetails.ROHS;
                    obj.SupplierPart = objDetails.SupplierPart;
                    obj.DateCode = objDetails.DateCode;
                    obj.ProductName = objDetails.ProductName;
                    obj.ProductNo = objDetails.ProductNo;
                    obj.PackageNo = objDetails.PackageNo;
                    obj.PackageName = objDetails.PackageName;
                    obj.Quantity = objDetails.Quantity;
                    obj.Price = objDetails.Price;
                    obj.Location = objDetails.Location;
                    obj.DateReceived = objDetails.DateReceived;
                    obj.StockNo = objDetails.StockNo;
                    obj.ReceiverName = objDetails.ReceiverName;
                    obj.LandedCost = objDetails.LandedCost;
                    obj.CompanyNo = objDetails.CompanyNo;
                    obj.CompanyName = objDetails.CompanyName;
                    obj.ManufacturerNo = objDetails.ManufacturerNo;
                    obj.ManufacturerCode = objDetails.ManufacturerCode;
                    obj.ManufacturerName = objDetails.ManufacturerName;
                    obj.ClientLandedCost = objDetails.ClientLandedCost;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }
        /// <summary>
        /// Update
        /// Calls [usp_update_GoodsInLine]
        /// </summary>
        //[005] start
        public static bool Update(System.Int32? goodsInLineId, System.String part, System.Int32? manufacturerNo, System.String dateCode, System.Int32? packageNo, System.Int32? quantity, System.Double? price, System.Double? shipInCost, System.String qualityControlNotes, System.String location, System.Int32? lotNo, System.Int32? productNo, System.String supplierPart, System.Byte? rohs, System.Int32? countryOfManufacture, System.Int32? currencyNo, System.Boolean? unavailable, System.String changedFields, System.String notes, System.Int32? countingMethodNo, System.Boolean? serialNosRecorded, System.String partMarkings, System.Boolean? updateStock, System.Boolean? updateShipments, System.Int32? updatedBy, System.Double? clientPrice, System.Boolean? reqSerialNo, System.String mslLevel, System.Boolean? printHazardous, System.String previousDLUP, ref string message)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.GoodsInLine.Update(goodsInLineId, part, manufacturerNo, dateCode, packageNo, quantity, price, shipInCost, qualityControlNotes, location, lotNo, productNo, supplierPart, rohs, countryOfManufacture, currencyNo, unavailable, changedFields, notes, countingMethodNo, serialNosRecorded, partMarkings, updateStock, updateShipments, updatedBy, clientPrice, reqSerialNo, mslLevel, printHazardous, previousDLUP, ref message);
        }
        /// <summary>
        /// Update (without parameters)
        /// Calls [usp_update_GoodsInLine]
        /// </summary>
        public bool Update()
        {
            string message = string.Empty;
            return Rebound.GlobalTrader.DAL.SiteProvider.GoodsInLine.Update(GoodsInLineId, Part, ManufacturerNo, DateCode, PackageNo, Quantity, Price, ShipInCost, QualityControlNotes, Location, LotNo, ProductNo, SupplierPart, ROHS, CountryOfManufacture, CurrencyNo, Unavailable, ChangedFields, Notes, CountingMethodNo, SerialNosRecorded, PartMarkings, UpdateStock, UpdateShipments, UpdatedBy, ClientPrice, ReqSerialNo, MSLLevel, PrintHazardous, null, ref message);
        }
        /// <summary>
        /// UpdateInspect
        /// Calls [usp_update_GoodsInLine_Inspect]
        /// </summary>
        public static bool UpdateInspect(System.Int32? goodsInLineId, System.Int32? inspectedBy)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.GoodsInLine.UpdateInspect(goodsInLineId, inspectedBy);
        }
        /// <summary>
        /// calls [usp_update_GoodsInLine_PhysicalInspect]
        /// </summary>
        /// <param name="goodsInLineId"></param>
        /// <param name="inspectedBy"></param>
        /// <returns></returns>
        public static bool UpdatePhysicalInspect(System.Int32? goodsInLineId, System.Int32? inspectedBy)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.GoodsInLine.UpdatePhysicalInspect(goodsInLineId, inspectedBy);
        }

        private static GoodsInLine PopulateFromDBDetailsObject(GoodsInLineDetails obj)
        {
            GoodsInLine objNew = new GoodsInLine();
            objNew.GoodsInLineId = obj.GoodsInLineId;
            objNew.GoodsInNo = obj.GoodsInNo;
            objNew.PurchaseOrderLineNo = obj.PurchaseOrderLineNo;
            objNew.FullPart = obj.FullPart;
            objNew.Part = obj.Part;
            objNew.ManufacturerNo = obj.ManufacturerNo;
            objNew.DateCode = obj.DateCode;
            objNew.PackageNo = obj.PackageNo;
            objNew.Quantity = obj.Quantity;
            objNew.Price = obj.Price;
            objNew.ShipInCost = obj.ShipInCost;
            objNew.QualityControlNotes = obj.QualityControlNotes;
            objNew.Location = obj.Location;
            objNew.ProductNo = obj.ProductNo;
            objNew.LandedCost = obj.LandedCost;
            objNew.CustomerRMALineNo = obj.CustomerRMALineNo;
            objNew.SupplierPart = obj.SupplierPart;
            objNew.ROHS = obj.ROHS;
            objNew.CountryOfManufacture = obj.CountryOfManufacture;
            objNew.UpdatedBy = obj.UpdatedBy;
            objNew.DLUP = obj.DLUP;
            objNew.InspectedBy = obj.InspectedBy;
            objNew.DateInspected = obj.DateInspected;
            objNew.CountingMethodNo = obj.CountingMethodNo;
            objNew.SerialNosRecorded = obj.SerialNosRecorded;
            objNew.Unavailable = obj.Unavailable;
            objNew.LotNo = obj.LotNo;
            objNew.Notes = obj.Notes;
            objNew.PartMarkings = obj.PartMarkings;
            objNew.FullSupplierPart = obj.FullSupplierPart;
            objNew.GoodsInId = obj.GoodsInId;
            objNew.GoodsInNumber = obj.GoodsInNumber;
            objNew.ManufacturerCode = obj.ManufacturerCode;
            objNew.DateReceived = obj.DateReceived;
            objNew.ReceiverName = obj.ReceiverName;
            objNew.PurchaseOrderNo = obj.PurchaseOrderNo;
            objNew.DeliveryDate = obj.DeliveryDate;
            objNew.PurchaseOrderNumber = obj.PurchaseOrderNumber;
            objNew.CompanyNo = obj.CompanyNo;
            objNew.CompanyName = obj.CompanyName;
            objNew.AirWayBill = obj.AirWayBill;
            objNew.RowNum = obj.RowNum;
            objNew.RowCnt = obj.RowCnt;
            objNew.QuantityOrdered = obj.QuantityOrdered;
            objNew.ContactNo = obj.ContactNo;
            objNew.ContactName = obj.ContactName;
            objNew.SupplierInvoice = obj.SupplierInvoice;
            objNew.InvoiceAmount = obj.InvoiceAmount;
            objNew.CurrencyNo = obj.CurrencyNo;
            objNew.CurrencyCode = obj.CurrencyCode;
            objNew.CountingMethodDescription = obj.CountingMethodDescription;
            objNew.LineNotes = obj.LineNotes;
            objNew.ClientNo = obj.ClientNo;
            objNew.PackageName = obj.PackageName;
            objNew.PackageDescription = obj.PackageDescription;
            objNew.ProductName = obj.ProductName;
            objNew.ProductDescription = obj.ProductDescription;
            objNew.ProductDutyCode = obj.ProductDutyCode;
            objNew.ManufacturerName = obj.ManufacturerName;
            objNew.CountryOfManufactureName = obj.CountryOfManufactureName;
            objNew.InspectorName = obj.InspectorName;
            objNew.CustomerRMANo = obj.CustomerRMANo;
            objNew.CustomerRMANumber = obj.CustomerRMANumber;
            objNew.CurrencyDescription = obj.CurrencyDescription;
            objNew.ReceivedBy = obj.ReceivedBy;
            objNew.DivisionNo = obj.DivisionNo;
            objNew.TeamNo = obj.TeamNo;
            objNew.StockNo = obj.StockNo;
            objNew.Reference = obj.Reference;
            objNew.LotName = obj.LotName;
            objNew.PurchaseOrderLineShipInCost = obj.PurchaseOrderLineShipInCost;
            objNew.ChangedFields = obj.ChangedFields;
            objNew.UpdateStock = obj.UpdateStock;
            objNew.UpdateShipments = obj.UpdateShipments;
            return objNew;
        }
        //[001] code start
        /// <summary>
        /// GetDetailsPrintNiceLabelGoodsInLine
        /// Calls [usp_GetDetails_to_PrintNiceLabel_for_GoodsInLine]
        /// </summary>
        public static GoodsInLine GetDetailsPrintNiceLabelGoodsInLine(System.Int32? goodsInLineId)
        {
            Rebound.GlobalTrader.DAL.GoodsInLineDetails objDetails = Rebound.GlobalTrader.DAL.SiteProvider.GoodsInLine.GetDetailsPrintNiceLabelGoodsInLine(goodsInLineId);
            if (objDetails == null)
            {
                return null;
            }
            else
            {
                GoodsInLine obj = new GoodsInLine();
                obj.GoodsInLineId = objDetails.GoodsInLineId;
                obj.Part = objDetails.Part;
                obj.DateCode = objDetails.DateCode;
                obj.Quantity = objDetails.Quantity;
                obj.InspectedByUser = objDetails.InspectedByUser;
                obj.ManufacturerName = objDetails.ManufacturerName;
                obj.CompanyName = objDetails.CompanyName;
                obj.CustomerPart = objDetails.CustomerPart;
                obj.SalesOrderNumber = objDetails.SalesOrderNumber;
                obj.DatePicked = objDetails.DatePicked;
                obj.GoodsInNumber = objDetails.GoodsInNumber;
                objDetails = null;
                return obj;
            }
        }
        //[001] code end
        /// <summary>
        /// usp_update_GoodsInLine_NPRStatus
        /// </summary>
        /// <param name="goodsInLineId"></param>
        /// <param name="nprPrintStatus"></param>
        /// <returns></returns>
        public static bool UpdateNPRStatus(System.Int32? goodsInLineId, System.Boolean? nprPrintStatus)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.GoodsInLine.UpdateNPRStatus(goodsInLineId, nprPrintStatus);
        }


        /// <summary>
        /// Insert
        /// Calls [usp_insert_SerialNo]
        /// </summary>
        public static Int32 InsertSerialNo(System.String subGroup, System.String serialNo, System.Int32? goodsInId, System.Int32? goodsInLineId, System.Int32? updatedBy, out System.String validateMessage, out System.Int32 totalSerial)
        {
            Int32 objReturn = Rebound.GlobalTrader.DAL.SiteProvider.GoodsInLine.InsertSerialNo(subGroup, serialNo, goodsInId, goodsInLineId, updatedBy, out validateMessage, out totalSerial);
            return objReturn;
        }

        public static Int32 UpdateSerialNo(System.Int32? serialId, System.String subGroup, System.String serialNo, System.Int32? goodsInId, System.String status, System.Int32? goodsInLineId, System.Int32? updatedBy, out System.String validateMessage)
        {
            Int32 objReturn = Rebound.GlobalTrader.DAL.SiteProvider.GoodsInLine.UpdateSerialNo(serialId, subGroup, serialNo, goodsInId, status, goodsInLineId, updatedBy, out validateMessage);
            return objReturn;
        }


        public static List<GoodsInLine> GetDataGrid(System.Int32? goodsInId, System.Int32? goodsInLineId, System.Int32? updatedBy)
        {
            List<GoodsInLineDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.GoodsInLine.GetDataGrid(goodsInId, goodsInLineId, updatedBy);
            if (lstDetails == null)
            {
                return new List<GoodsInLine>();
            }
            else
            {
                List<GoodsInLine> lst = new List<GoodsInLine>();
                foreach (GoodsInLineDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.GoodsInLine obj = new Rebound.GlobalTrader.BLL.GoodsInLine();
                    obj.SerialNoId = objDetails.SerialNoId;
                    obj.GoodsInNo = objDetails.GoodsInNo;
                    obj.SerialNo = objDetails.SerialNo;
                    obj.SubGroup = objDetails.SubGroup;
                    obj.Status = objDetails.Status;
                    obj.InvoiceLineNo = objDetails.InvoiceLineNo;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }

        public static Int32 InsertAllSerialNo(System.Int32? goodsInId, System.Int32? goodsInLineId, System.Int32? updatedBy, out System.String validateMessage)
        {
            Int32 objReturn = Rebound.GlobalTrader.DAL.SiteProvider.GoodsInLine.InsertAllSerialNo(goodsInId, goodsInLineId, updatedBy, out validateMessage);
            return objReturn;
        }

        public static List<GoodsInLine> GetSerial(System.Int32? goodsInLineId)
        {
            List<GoodsInLineDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.GoodsInLine.GetSerial(goodsInLineId);
            if (lstDetails == null)
            {
                return new List<GoodsInLine>();
            }
            else
            {
                List<GoodsInLine> lst = new List<GoodsInLine>();
                foreach (GoodsInLineDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.GoodsInLine obj = new Rebound.GlobalTrader.BLL.GoodsInLine();
                    obj.SerialNoId = objDetails.SerialNoId;
                    obj.SerialNo = objDetails.SerialNo;
                    obj.SubGroup = objDetails.SubGroup;
                    obj.GoodsInNo = objDetails.GoodsInNo;

                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }

        //public static List<GoodsInLine> AutoSearch(System.String nameSearch, System.String groupName)
        //{
        //    List<GoodsInLineDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.GoodsInLine.AutoSearch(nameSearch, groupName);
        //    if (lstDetails == null)
        //    {
        //        return new List<GoodsInLine>();
        //    }
        //    else
        //    {
        //        List<GoodsInLine> lst = new List<GoodsInLine>();
        //        foreach (GoodsInLineDetails objDetails in lstDetails)
        //        {
        //            Rebound.GlobalTrader.BLL.GoodsInLine obj = new Rebound.GlobalTrader.BLL.GoodsInLine();
        //            obj.SerialNoId = objDetails.SerialNoId;
        //            obj.SerialNo = objDetails.SerialNo;
        //            lst.Add(obj);
        //            obj = null;
        //        }
        //        lstDetails = null;
        //        return lst;
        //    }
        //}

        public static List<GoodsInLine> AutoSearch(System.String nameSearch, System.String groupName)
        {
            List<GoodsInLineDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.GoodsInLine.AutoSearch(nameSearch, groupName);
            if (lstDetails == null)
            {
                return new List<GoodsInLine>();
            }
            else
            {
                List<GoodsInLine> lst = new List<GoodsInLine>();
                foreach (GoodsInLineDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.GoodsInLine obj = new Rebound.GlobalTrader.BLL.GoodsInLine();
                    obj.SerialNoId = objDetails.SerialNoId;
                    obj.SerialNo = objDetails.SerialNo;

                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="goodsInLineNo"></param>
        /// <returns></returns>
        public static List<GoodsInLine> DropDown(System.Int32? goodsInLineNo, System.Int32? invoiceLineNo)
        {
            List<GoodsInLine> lst = new List<GoodsInLine>();
            try
            {
                List<GoodsInLineDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.GoodsInLine.DropDown(goodsInLineNo, invoiceLineNo);
                if (lstDetails == null)
                {
                    return new List<GoodsInLine>();
                }
                else
                {

                    foreach (GoodsInLineDetails objDetails in lstDetails)
                    {
                        Rebound.GlobalTrader.BLL.GoodsInLine obj = new Rebound.GlobalTrader.BLL.GoodsInLine();
                        obj.SubGroup = objDetails.SubGroup;
                        obj.SerialNo = objDetails.SerialNo;
                        lst.Add(obj);
                        obj = null;
                    }
                    lstDetails = null;

                }
            }
            catch (Exception ex)
            {
                throw new Exception("Failed to get GoodsInLine DropDown", ex);
            }
            return lst;
        }


        public static List<GoodsInLine> AutoSearchGroup(System.String nameSearch)
        {
            List<GoodsInLineDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.GoodsInLine.AutoSearchGroup(nameSearch);
            if (lstDetails == null)
            {
                return new List<GoodsInLine>();
            }
            else
            {
                List<GoodsInLine> lst = new List<GoodsInLine>();
                foreach (GoodsInLineDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.GoodsInLine obj = new Rebound.GlobalTrader.BLL.GoodsInLine();
                    obj.SerialNoId = objDetails.SerialNoId;
                    obj.SubGroup = objDetails.SubGroup;

                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }

        public static Int32 DeleteSerialNo(System.Int32? serialNoId, System.String status, System.Int32? goodsInId, System.Int32? goodsInLineId, System.Int32? updatedBy)
        {
            Int32 objReturn = Rebound.GlobalTrader.DAL.SiteProvider.GoodsInLine.DeleteSerialNo(serialNoId, status, goodsInId, goodsInLineId, updatedBy);
            return objReturn;
        }

        /// <summary>
        /// ItemSearch
        /// Calls [usp_itemsearch_GoodsInSerialNo]
        /// </summary>
        public static List<GoodsInLine> GISerialSearch(System.Int32? clientId, System.Int32? orderBy, System.Int32? sortDir, System.Int32? pageIndex, System.Int32? pageSize, System.String giSerialGroup, System.String serialNoLo, System.Int32? serialNoHi, System.DateTime? dateReceivedFrom, System.DateTime? dateReceivedTo, System.Int32? goodsInLineNo, System.Int32? invoiceLineNo)
        {
            List<GoodsInLineDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.GoodsInLine.GISerialSearch(clientId, orderBy, sortDir, pageIndex, pageSize, giSerialGroup, serialNoLo, serialNoHi, dateReceivedFrom, dateReceivedTo, goodsInLineNo, invoiceLineNo);
            if (lstDetails == null)
            {
                return new List<GoodsInLine>();
            }
            else
            {
                List<GoodsInLine> lst = new List<GoodsInLine>();
                foreach (GoodsInLineDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.GoodsInLine obj = new Rebound.GlobalTrader.BLL.GoodsInLine();
                    obj.SerialNoId = objDetails.SerialNoId;
                    obj.SerialNo = objDetails.SerialNo;
                    obj.SubGroup = objDetails.SubGroup;
                    obj.RowNum = objDetails.RowNum;
                    obj.RowCnt = objDetails.RowCnt;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }

        /// <summary>
        /// usp_get_AttachedSerialNo
        /// </summary>
        public static List<GoodsInLine> GetAttachedSerial(System.Int32? clientId, System.Int32? orderBy, System.Int32? sortDir, System.Int32? pageIndex, System.Int32? pageSize, System.Int32? goodsInLineNO, System.Int32? salesOrderLineNo, System.DateTime? dateReceivedFrom, System.DateTime? dateReceivedTo, System.Int32? allocationNo)
        {
            List<GoodsInLineDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.GoodsInLine.GetAttachedSerial(clientId, orderBy, sortDir, pageIndex, pageSize, goodsInLineNO, salesOrderLineNo, dateReceivedFrom, dateReceivedTo, allocationNo);
            if (lstDetails == null)
            {
                return new List<GoodsInLine>();
            }
            else
            {
                List<GoodsInLine> lst = new List<GoodsInLine>();
                foreach (GoodsInLineDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.GoodsInLine obj = new Rebound.GlobalTrader.BLL.GoodsInLine();
                    obj.SerialNoId = objDetails.SerialNoId;
                    obj.SerialNo = objDetails.SerialNo;
                    obj.SubGroup = objDetails.SubGroup;
                    obj.RowNum = objDetails.RowNum;
                    obj.InvoiceLineNo = objDetails.InvoiceLineNo;
                    obj.RowCnt = objDetails.RowCnt;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }

        /// <summary>
        /// usp_delete_AttachedSerial
        /// </summary>
        public static Int32 DeleteAttachedSerial(System.Int32? serialId, System.Int32? goodsInLineId, System.Int32? soLineId, System.Int32? updatedBy, System.Int32? allocationNo)
        {
            Int32 objReturn = Rebound.GlobalTrader.DAL.SiteProvider.GoodsInLine.DeleteAttachedSerial(serialId, goodsInLineId, soLineId, updatedBy, allocationNo);
            return objReturn;
        }

        /// <summary>
        /// usp_update_SerialBySO
        /// </summary>
        public static Int32 UpdateSerialBySO(System.String subGroup, System.Int32? goodsInLineId, System.Int32? soLineId, System.Int32? qtyToShpped, System.Int32? allocatedId, out System.String validateMessage)
        {
            Int32 objReturn = Rebound.GlobalTrader.DAL.SiteProvider.GoodsInLine.UpdateSerialBySO(subGroup, goodsInLineId, soLineId, qtyToShpped, allocatedId, out validateMessage);
            return objReturn;
        }

        /// <summary>
        /// usp_delete_SerialBySO
        /// </summary>
        public static Int32 DeleteSerialBySO(System.Int32? goodsInLineId, System.Int32? soLineId, System.Int32? AllocatedId)
        {
            Int32 objReturn = Rebound.GlobalTrader.DAL.SiteProvider.GoodsInLine.DeleteSerialBySO(goodsInLineId, soLineId, AllocatedId);
            return objReturn;
        }


        public static List<GoodsInLine> GetReasonDetailByPart(System.String part)
        {
            List<GoodsInLineDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.GoodsInLine.GetReasonDetailByPart(part);
            if (lstDetails == null)
            {
                return new List<GoodsInLine>();
            }
            else
            {
                List<GoodsInLine> lst = new List<GoodsInLine>();
                foreach (GoodsInLineDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.GoodsInLine obj = new Rebound.GlobalTrader.BLL.GoodsInLine();

                    obj.ReasonCode = objDetails.ReasonCode;
                    obj.ReasonDate = objDetails.ReasonDate;

                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="goodsInLineNo"></param>
        /// <returns></returns>
        public static List<GoodsInLine> GetShipCostHistory(System.Int32? goodsInLineNo)
        {
            List<GoodsInLineDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.GoodsInLine.GetShipCostHistory(goodsInLineNo);
            if (lstDetails == null)
            {
                return new List<GoodsInLine>();
            }
            else
            {
                List<GoodsInLine> lst = new List<GoodsInLine>();
                foreach (GoodsInLineDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.GoodsInLine obj = new Rebound.GlobalTrader.BLL.GoodsInLine();
                    obj.GIShipCostHistoryId = objDetails.GIShipCostHistoryId;
                    obj.ShipInCost = objDetails.ShipInCost;
                    obj.DLUP = objDetails.DLUP;
                    obj.ReceiverName = objDetails.ReceiverName;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }

        /// <summary>
        /// ItemSearch
        /// Calls [usp_itemsearch_GoodsInTempSerialNo]
        /// </summary>
        public static List<GoodsInLine> GITempSerialSearch(System.Int32? clientId, System.Int32? orderBy, System.Int32? sortDir, System.Int32? pageIndex, System.Int32? pageSize, System.Int32? goodsInNo, System.Int32? goodsInLineNo, System.Int32? loginNo)
        {
            List<GoodsInLineDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.GoodsInLine.GITempSerialSearch(clientId, orderBy, sortDir, pageIndex, pageSize, goodsInNo, goodsInLineNo, loginNo);
            if (lstDetails == null)
            {
                return new List<GoodsInLine>();
            }
            else
            {
                List<GoodsInLine> lst = new List<GoodsInLine>();
                foreach (GoodsInLineDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.GoodsInLine obj = new Rebound.GlobalTrader.BLL.GoodsInLine();
                    obj.SerialNoId = objDetails.SerialNoId;
                    obj.SerialNo = objDetails.SerialNo;
                    obj.GoodsInNo = objDetails.GoodsInNo;
                    obj.SubGroup = objDetails.SubGroup;
                    obj.Status = objDetails.Status;
                    obj.InvoiceLineNo = objDetails.InvoiceLineNo;
                    obj.GoodsInNumber = objDetails.GoodsInNumber;
                    obj.RowNum = objDetails.RowNum;
                    obj.RowCnt = objDetails.RowCnt;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }
        public static List<GoodsInLine> GetAttachedSerial(System.Int32? orderBy, System.Int32? sortDir, System.Int32? pageIndex, System.Int32? pageSize, System.Int32? customerRMANo, System.Int32? customerRMALineNo)
        {
            List<GoodsInLineDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.GoodsInLine.GetAttachedSerial(orderBy, sortDir, pageIndex, pageSize, customerRMANo, customerRMALineNo);
            if (lstDetails == null)
            {
                return new List<GoodsInLine>();
            }
            else
            {
                List<GoodsInLine> lst = new List<GoodsInLine>();
                foreach (GoodsInLineDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.GoodsInLine obj = new Rebound.GlobalTrader.BLL.GoodsInLine();
                    obj.SerialNoId = objDetails.SerialNoId;
                    obj.SerialNo = objDetails.SerialNo;
                    obj.SubGroup = objDetails.SubGroup;
                    obj.RowNum = objDetails.RowNum;
                    obj.InvoiceLineNo = objDetails.InvoiceLineNo;
                    obj.RowCnt = objDetails.RowCnt;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }
        public static Int32 DeleteAttachedSerial(System.Int32? serialId, System.Int32? customerRMANo, System.Int32? customerRMALineNo, System.Int32? updatedBy)
        {
            Int32 objReturn = Rebound.GlobalTrader.DAL.SiteProvider.GoodsInLine.DeleteAttachedSerial(serialId, customerRMANo, customerRMALineNo, updatedBy);
            return objReturn;
        }
        public static Int32 DeattachCRMASerial(System.Int32? invoiceLineNo)
        {
            Int32 objReturn = Rebound.GlobalTrader.DAL.SiteProvider.GoodsInLine.DeattachCRMASerial(invoiceLineNo);
            return objReturn;
        }
        public static Int32 AttachSerialByCRMA(System.String subGroup, System.Int32? invoiceLineNo, System.Int32? customerRMANo, System.Int32? customerRMALineNo, out System.String validateMessage)
        {
            Int32 objReturn = Rebound.GlobalTrader.DAL.SiteProvider.GoodsInLine.AttachSerialByCRMA(subGroup, invoiceLineNo, customerRMANo, customerRMALineNo, out validateMessage);
            return objReturn;
        }
        /// <summary>
		/// Update
		/// Calls [usp_split_GoodsInLine]
		/// </summary>
        //[005] start
        public static bool SplitGI(System.Int32? goodsInLineId, System.String part, System.Int32? manufacturerNo, 
            System.String dateCode, System.Int32? packageNo, System.Int32? quantity, System.Double? price, System.Double? shipInCost, System.String qualityControlNotes, System.String location, System.Int32? lotNo, System.Int32? productNo, System.String supplierPart, System.Byte? rohs, System.Int32? countryOfManufacture, System.Int32? currencyNo, System.Boolean? unavailable, System.String changedFields, System.String notes, System.Int32? countingMethodNo, System.Boolean? serialNosRecorded, System.String partMarkings, System.Boolean? updateStock, System.Boolean? updateShipments, System.Int32? updatedBy, System.Double? clientPrice, System.Boolean? reqSerialNo, System.String mslLevel, 
            System.Boolean? printHazardous, System.String previousDLUP,System.String SerialNo, ref string message)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.GoodsInLine.SplitGI(goodsInLineId, part, manufacturerNo, dateCode, packageNo, quantity, price, shipInCost, qualityControlNotes, location, lotNo, productNo, supplierPart, rohs, countryOfManufacture, currencyNo, unavailable, changedFields, notes, countingMethodNo, serialNosRecorded, partMarkings, updateStock, updateShipments, updatedBy, clientPrice, reqSerialNo, mslLevel, printHazardous, previousDLUP, SerialNo, ref message);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="HICStatus"></param>
        /// <returns></returns>
        public static List<GoodsInLine> GetHICStatus()
        {
            List<GoodsInLine> lst = new List<GoodsInLine>();
            try
            {
                List<GoodsInLineDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.GoodsInLine.GetHICStatus();
                if (lstDetails == null)
                {
                    return new List<GoodsInLine>();
                }
                else
                {

                    foreach (GoodsInLineDetails objDetails in lstDetails)
                    {
                        Rebound.GlobalTrader.BLL.GoodsInLine obj = new Rebound.GlobalTrader.BLL.GoodsInLine();
                        obj.HICId = objDetails.HICId;
                        obj.HICStatus = objDetails.HICStatus;
                        lst.Add(obj);
                        obj = null;
                    }
                    lstDetails = null;

                }
            }
            catch (Exception ex)
            {
                throw new Exception("Failed to get GoodsInLine DropDown", ex);
            }
            return lst;
        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="QueryHICStatus"></param>
        /// <returns></returns>
        public static List<GoodsInLine> GetQueryHICStatus()
        {
            List<GoodsInLine> lst = new List<GoodsInLine>();
            try
            {
                List<GoodsInLineDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.GoodsInLine.GetQueryHICStatus();
                if (lstDetails == null)
                {
                    return new List<GoodsInLine>();
                }
                else
                {

                    foreach (GoodsInLineDetails objDetails in lstDetails)
                    {
                        Rebound.GlobalTrader.BLL.GoodsInLine obj = new Rebound.GlobalTrader.BLL.GoodsInLine();
                        obj.QueryHICId = objDetails.QueryHICId;
                        obj.QueryHICStatus = objDetails.QueryHICStatus;
                        lst.Add(obj);
                        obj = null;
                    }
                    lstDetails = null;

                }
            }
            catch (Exception ex)
            {
                throw new Exception("Failed to get Query HIC Status DropDown", ex);
            }
            return lst;
        }
        /// <summary>
        /// calls[usp_update_GoodsInLineEdit]
        /// </summary>
        /// <param name="ID"></param>
        /// <param name="actShipInCost"></param>
        /// <param name="QCNotes"></param>
        /// <param name="Location"></param>
        /// <param name="LotNo"></param>
        /// <param name="blnUpdateStock"></param>
        /// <param name="blnUpdateShipments"></param>
        /// <param name="loginID"></param>
        /// <param name="IsFullQuantityReceived"></param>
        /// <param name="QuantityReceived"></param>
        /// <param name="IsPartNumberCorrect"></param>
        /// <param name="CorrectPartNo"></param>
        /// <param name="IsManufacturerCorrect"></param>
        /// <param name="CorrectManufacturerNo"></param>
        /// <param name="IsDateCodeCorrect"></param>
        /// <param name="CorrectDateCode"></param>
        /// <param name="IsPackageCorrect"></param>
        /// <param name="CorrectPackageNo"></param>
        /// <param name="IsMSLCorrect"></param>
        /// <param name="CorrectMslNo"></param>
        /// <param name="IsHICCorrect"></param>
        /// <param name="CorrectHIC"></param>
        /// <param name="IsRohsStatusCorrect"></param>
        /// <param name="CorrectStatusNo"></param>
        /// <param name="CountryOfManufacture"></param>
        /// <param name="CountingMethodNo"></param>
        /// <param name="IsSerialNosRecorded"></param>
        /// <param name="IsLotCodeReq"></param>
        /// <param name="IsEnhancedInpection"></param>
        /// <param name="GeneralInspectionNotes"></param>
        /// <param name="IsBakingYes"></param>
        /// <param name="IsBakingNo"></param>
        /// <param name="IsBakingNA"></param>
        /// <param name="IsInspectionConducted"></param>
        /// <param name="SupplierPart"></param>
        /// <param name="ProductNo"></param>
        /// <param name="Price"></param>
        /// <param name="ClientPrice"></param>
        /// <param name="Unavailable"></param>
        /// <param name="ChangedFields"></param>
        /// <param name="CurrencyNo"></param>
        /// <param name="ReqSerailNo"></param>
        /// <param name="PartMarkings"></param>
        /// <param name="LineNotes"></param>
        /// <param name="PrintHazWar"></param>
        /// <param name="PreviousDLUP"></param>
        /// <param name="message"></param>
        /// <returns></returns>

        //[008] added two parameters [System.Int16? HasBarCode, System.String BarcodeRemarks] in UpdateGILineEdit function
        public static bool UpdateGILineEdit(System.Int32? ID, System.Double? actShipInCost, System.String QCNotes, System.String Location, System.Int32? LotNo, System.Boolean? blnUpdateStock, System.Boolean? blnUpdateShipments, System.Int32? loginID, System.Boolean? IsFullQuantityReceived, System.Int32? QuantityReceived, System.Boolean? IsPartNumberCorrect, System.String CorrectPartNo, System.Boolean? IsManufacturerCorrect, System.Int32? CorrectManufacturerNo, System.Boolean? IsDateCodeCorrect, System.String CorrectDateCode, System.Boolean? IsPackageCorrect, System.Int32? CorrectPackageNo, System.Boolean? IsMSLCorrect, System.String CorrectMslNo, System.Boolean? IsHICCorrect, System.String CorrectHIC, System.Boolean? IsRohsStatusCorrect, System.Int32? CorrectStatusNo, System.Int32? CountryOfManufacture, System.Int32? CountingMethodNo, System.Boolean? IsSerialNosRecorded, System.Boolean? IsLotCodeReq, System.Boolean? IsEnhancedInpection, System.String GeneralInspectionNotes, System.Boolean? IsBakingYes, System.Boolean? IsBakingNo, System.Boolean? IsBakingNA, System.Boolean? IsInspectionConducted, System.String SupplierPart, System.Int32? ProductNo, System.Double Price, System.Double? ClientPrice, System.Boolean? Unavailable, System.String ChangedFields, System.Int32? CurrencyNo, System.Boolean? ReqSerailNo, System.String PartMarkings, System.String LineNotes, System.Boolean? PrintHazWar, System.String PreviousDLUP, System.Double shipInCost, System.Int32? quantity, ref System.String message, System.Boolean? IsDateCodeRequired, System.String PackageBreakdownInfo, System.Int32? HICStatus, System.String PackBreakDownJSON, System.Boolean? IsBySendQueryBtn, System.Int32? ActeoneTestStatus, System.Int32? IsopropryleStatus, System.String ActeoneTest, System.String Isopropryle, System.String QueryBakingLevel, System.Int32? EnhInpectionReqId, System.String PrintDateCode, System.Boolean? IsPackageBreakdownChnaged,
            System.Int32? HasBarCode, System.String BarcodeRemarks, System.String PartNoQuery, System.String ManufacturerQuery, System.String PackagingTypeQuery, System.String MslQuery, System.String RohsQuery,System.Boolean? ReaiseGeneralQuery)
        {
            //[008] added two parameters [System.Int16? HasBarCode, System.String BarcodeRemarks] in UpdateGILineEdit function
            return Rebound.GlobalTrader.DAL.SiteProvider.GoodsInLine.UpdateGILineEdit(ID, actShipInCost, QCNotes, Location, LotNo, blnUpdateStock, blnUpdateShipments, loginID, IsFullQuantityReceived, QuantityReceived, IsPartNumberCorrect, CorrectPartNo, IsManufacturerCorrect, CorrectManufacturerNo, IsDateCodeCorrect, CorrectDateCode, IsPackageCorrect, CorrectPackageNo, IsMSLCorrect, CorrectMslNo, IsHICCorrect, CorrectHIC, IsRohsStatusCorrect, CorrectStatusNo, CountryOfManufacture, CountingMethodNo, IsSerialNosRecorded, IsLotCodeReq, IsEnhancedInpection, GeneralInspectionNotes, IsBakingYes, IsBakingNo, IsBakingNA, IsInspectionConducted, SupplierPart, ProductNo, Price, ClientPrice, Unavailable, ChangedFields, CurrencyNo, ReqSerailNo, PartMarkings, LineNotes, PrintHazWar, PreviousDLUP, shipInCost, quantity, ref message, IsDateCodeRequired, PackageBreakdownInfo, HICStatus, PackBreakDownJSON, IsBySendQueryBtn, ActeoneTestStatus, IsopropryleStatus, ActeoneTest, Isopropryle, QueryBakingLevel, EnhInpectionReqId, PrintDateCode, IsPackageBreakdownChnaged, HasBarCode, BarcodeRemarks, PartNoQuery, ManufacturerQuery, PackagingTypeQuery, MslQuery, RohsQuery, ReaiseGeneralQuery);
        }
        /// <summary>
        /// calls [usp_select_PackagingBreakdown]
        /// </summary>
        /// <param name="goodsInLineId"></param>
        /// <returns></returns>
        public static List<PackagingBreakdown> GetPackagingBreakdownList(System.Int32 goodsInLineId)
        {
            List<Rebound.GlobalTrader.DAL.PackagingBreakdown> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.GoodsInLine.GetPackagingBreakdownList(goodsInLineId);
            if (lstDetails == null)
            {
                return new List<PackagingBreakdown>();
            }
            else
            {
                List<PackagingBreakdown> lst = new List<PackagingBreakdown>();
                foreach (Rebound.GlobalTrader.DAL.PackagingBreakdown objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.PackagingBreakdown obj = new Rebound.GlobalTrader.BLL.PackagingBreakdown();
                    obj.FactorySealed = objDetails.FactorySealed;
                    obj.NumberOfPacks = objDetails.NumberOfPacks;
                    obj.PackSize = objDetails.PackSize;
                    obj.DateCode = objDetails.DateCode;
                    obj.BatchCode = objDetails.BatchCode;
                    obj.PackagingTypeId = objDetails.PackagingTypeId;
                    obj.TotalPackSize = objDetails.TotalPackSize;
                    obj.MFRLabelId = objDetails.MFRLabelId;

                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }
        public static List<DateCode> GetDateCodeList(System.Int32 goodsInLineId)
        {
            List<Rebound.GlobalTrader.DAL.DateCode> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.GoodsInLine.GetDateCodeList(goodsInLineId);
            if (lstDetails == null)
            {
                return new List<DateCode>();
            }
            else
            {
                List<DateCode> lst = new List<DateCode>();
                foreach (Rebound.GlobalTrader.DAL.DateCode objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.DateCode obj = new Rebound.GlobalTrader.BLL.DateCode();
                    obj.ID = objDetails.ID;
                    obj.DateCodes = objDetails.DateCodes;
                    obj.Quantity = objDetails.Quantity;
                    obj.QuantityReceived = objDetails.QuantityReceived;

                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }
        /// <summary>
        /// Calls[usp_select_GoodsInVariance_Query]
        /// </summary>
        /// <param name="goodsInLineId"></param>
        /// <returns></returns>
        public static GoodsInLine GetGIQueryData(System.Int32? goodsInLineId)
        {
            Rebound.GlobalTrader.DAL.GoodsInLineDetails objDetails = Rebound.GlobalTrader.DAL.SiteProvider.GoodsInLine.GetGIQueryData(goodsInLineId);
            if (objDetails == null)
            {
                return null;
            }
            else
            {
                GoodsInLine obj = new GoodsInLine();
                obj.GIQuery = objDetails.GIQuery;
                objDetails = null;
                return obj;
            }
        }
        /// <summary>
        /// Calls [usp_select_GIUpdatedLine]
        /// </summary>
        /// <param name="goodsInLineId"></param>
        /// <returns></returns>
        public static GoodsInLine GetGILineData(System.Int32? goodsInLineId)
        {
            Rebound.GlobalTrader.DAL.GoodsInLineDetails objDetails = Rebound.GlobalTrader.DAL.SiteProvider.GoodsInLine.GetGILineData(goodsInLineId);
            if (objDetails == null)
            {
                return null;
            }
            else
            {
                GoodsInLine obj = new GoodsInLine();
                obj.GoodsInLineId = objDetails.GoodsInLineId;
                obj.GoodsInNo = objDetails.GoodsInNo;
                obj.PurchaseOrderLineNo = objDetails.PurchaseOrderLineNo;
                obj.FullPart = objDetails.FullPart;
                obj.Part = objDetails.Part;
                obj.ManufacturerNo = objDetails.ManufacturerNo;
                obj.DateCode = objDetails.DateCode;
                obj.PackageNo = objDetails.PackageNo;
                obj.Quantity = objDetails.Quantity;
                obj.Price = objDetails.Price;
                obj.ShipInCost = objDetails.ShipInCost;
                obj.QualityControlNotes = objDetails.QualityControlNotes;
                obj.Location = objDetails.Location;
                obj.ProductNo = objDetails.ProductNo;
                obj.LandedCost = objDetails.LandedCost;
                obj.CustomerRMALineNo = objDetails.CustomerRMALineNo;
                obj.SupplierPart = objDetails.SupplierPart;
                obj.ROHS = objDetails.ROHS;
                obj.CountryOfManufacture = objDetails.CountryOfManufacture;
                obj.InspectedBy = objDetails.InspectedBy;
                obj.DateInspected = objDetails.DateInspected;
                obj.CountingMethodNo = objDetails.CountingMethodNo;
                obj.CountingMethodDescription = objDetails.CountingMethodDescription;
                obj.SerialNosRecorded = objDetails.SerialNosRecorded;
                obj.PartMarkings = objDetails.PartMarkings;
                obj.Unavailable = objDetails.Unavailable;
                obj.LotNo = objDetails.LotNo;
                obj.LineNotes = objDetails.LineNotes;
                obj.UpdatedBy = objDetails.UpdatedBy;
                obj.DLUP = objDetails.DLUP;
                obj.GoodsInNumber = objDetails.GoodsInNumber;
                obj.ClientNo = objDetails.ClientNo;
                obj.AirWayBill = objDetails.AirWayBill;
                obj.PackageName = objDetails.PackageName;
                obj.CorrectPackageName = objDetails.CorrectPackageName;
                obj.PackageDescription = objDetails.PackageDescription;
                obj.ProductName = objDetails.ProductName;
                obj.ProductDescription = objDetails.ProductDescription;
                obj.ProductDutyCode = objDetails.ProductDutyCode;
                obj.ManufacturerName = objDetails.ManufacturerName;
                obj.CorrectManufacturerName = objDetails.CorrectManufacturerName;
                obj.ManufacturerCode = objDetails.ManufacturerCode;
                obj.CountryOfManufactureName = objDetails.CountryOfManufactureName;
                obj.DateReceived = objDetails.DateReceived;
                obj.InspectorName = objDetails.InspectorName;
                obj.PurchaseOrderNo = objDetails.PurchaseOrderNo;
                obj.PurchaseOrderNumber = objDetails.PurchaseOrderNumber;
                obj.CustomerRMANo = objDetails.CustomerRMANo;
                obj.CustomerRMANumber = objDetails.CustomerRMANumber;
                obj.CurrencyNo = objDetails.CurrencyNo;
                obj.CurrencyCode = objDetails.CurrencyCode;
                obj.CurrencyDescription = objDetails.CurrencyDescription;
                obj.ReceivedBy = objDetails.ReceivedBy;
                obj.ReceiverName = objDetails.ReceiverName;
                obj.DivisionNo = objDetails.DivisionNo;
                obj.TeamNo = objDetails.TeamNo;
                obj.CompanyNo = objDetails.CompanyNo;
                obj.CompanyName = objDetails.CompanyName;
                obj.StockNo = objDetails.StockNo;
                obj.SupplierInvoice = objDetails.SupplierInvoice;
                obj.Reference = objDetails.Reference;
                obj.LotName = objDetails.LotName;
                obj.QuantityOrdered = objDetails.QuantityOrdered;
                obj.PurchaseOrderLineShipInCost = objDetails.PurchaseOrderLineShipInCost;
                obj.ChangedFields = objDetails.ChangedFields;
                obj.UpdateStock = objDetails.UpdateStock;
                obj.UpdateShipments = objDetails.UpdateShipments;
                obj.HasAllocationOutward = objDetails.HasAllocationOutward;
                obj.CustomerPart = objDetails.CustomerPart;
                obj.SalesOrderNumber = objDetails.SalesOrderNumber;
                obj.NPRPrinted = objDetails.NPRPrinted;
                //[002] code start
                obj.InspectorNameLabel = objDetails.InspectorNameLabel;
                //[002] code end

                //[003] code start
                obj.NPRIds = objDetails.NPRIds;
                obj.NPRNos = objDetails.NPRNos;
                //[003] code end
                obj.HasStocksplit = objDetails.HasStocksplit;
                obj.HasSupplierInvoiceExists = objDetails.HasSupplierInvoiceExists;
                obj.blnStockProvision = objDetails.blnStockProvision;
                obj.LotCode = objDetails.LotCode;
                obj.ClientLandedCost = objDetails.ClientLandedCost;
                obj.ClientPrice = objDetails.ClientPrice;
                obj.InternalPurchaseOrderId = objDetails.InternalPurchaseOrderId;
                obj.ClientCurrencyNo = objDetails.ClientCurrencyNo;
                obj.ClientCurrencyCode = objDetails.ClientCurrencyCode;
                obj.IPOClientNo = objDetails.IPOClientNo;
                obj.POBankFee = objDetails.POBankFee;
                obj.CustomerPO = objDetails.CustomerPO;
                obj.StockDate = objDetails.StockDate;
                obj.ProductInactive = objDetails.ProductInactive;
                obj.DutyRate = objDetails.DutyRate;
                obj.ReqSerialNo = objDetails.ReqSerialNo;
                obj.SerialNoCount = objDetails.SerialNoCount;
                obj.MSLLevel = objDetails.MSLLevel;
                obj.IsProdHazardous = objDetails.IsProdHazardous;
                obj.PrintHazardous = objDetails.PrintHazardous;
                //[005] start
                obj.StringDLUP = objDetails.StringDLUP;
                //[005] end
                obj.ParentGoodsInLineId = objDetails.ParentGoodsInLineId;
                obj.TotalShipCost = objDetails.TotalShipCost;
                //ihs code start
                obj.CountryOfOrigin = objDetails.CountryOfOrigin;
                obj.LifeCycleStage = objDetails.LifeCycleStage;
                obj.HTSCode = objDetails.HTSCode;
                obj.AveragePrice = objDetails.AveragePrice;
                obj.Packaging = objDetails.Packaging;
                obj.PackagingSize = objDetails.PackagingSize;
                obj.Descriptions = objDetails.Descriptions;
                obj.IHSProduct = objDetails.IHSProduct;
                obj.ECCNCode = objDetails.ECCNCode;
                //ihs code end

                obj.IsFullQtyRecieved = objDetails.IsFullQtyRecieved;
                obj.IsPartNoCorrect = objDetails.IsPartNoCorrect;
                obj.CorrectPartNo = objDetails.CorrectPartNo;
                obj.IsManufacturerCorrect = objDetails.IsManufacturerCorrect;
                obj.CorrectManufacturer = objDetails.CorrectManufacturer;
                obj.IsDateCodeCorrect = objDetails.IsDateCodeCorrect;
                obj.CorrectDateCode = objDetails.CorrectDateCode;
                obj.IsDateCodeRequired = objDetails.IsDateCodeRequired;
                obj.IsPackageTypeCorrect = objDetails.IsPackageTypeCorrect;
                obj.CorrectPackageType = objDetails.CorrectPackageType;
                obj.IsMSLLevelCorrect = objDetails.IsMSLLevelCorrect;
                obj.CorrectMSLLevel = objDetails.CorrectMSLLevel;
                obj.HIC_Status = objDetails.HIC_Status;
                obj.IsHICStatusCorrect = objDetails.IsHICStatusCorrect;
                obj.CorrectHICStatus = objDetails.CorrectHICStatus;
                obj.PKGBreakdownMismatch = objDetails.PKGBreakdownMismatch;
                obj.IsROHSStatusCorrect = objDetails.IsROHSStatusCorrect;
                obj.CorrectROHSStatus = objDetails.CorrectROHSStatus;
                obj.IsLotCodesReq = objDetails.IsLotCodesReq;
                obj.BakingLevelAdded = objDetails.BakingLevelAdded;
                obj.EnhancedInspectionReq = objDetails.EnhancedInspectionReq;
                obj.GeneralInspectionNotes = objDetails.GeneralInspectionNotes;
                obj.IsInspectionConducted = objDetails.IsInspectionConducted;
                obj.CompanyType = objDetails.CompanyType;
                obj.IsSalesNotify = objDetails.IsSalesNotify;
                obj.IsQualityNotify = objDetails.IsQualityNotify;
                obj.IsPurchaseNotify = objDetails.IsPurchaseNotify;

                obj.SalesQueryReply = objDetails.SalesQueryReply;
                obj.PurchaseQueryReply = objDetails.PurchaseQueryReply;
                obj.QualityQueryReply = objDetails.QualityQueryReply;
                obj.SalesApprovalStatus = objDetails.SalesApprovalStatus;
                obj.PurchaseApprovalStatus = objDetails.PurchaseApprovalStatus;
                obj.QualityApprovalStatus = objDetails.QualityApprovalStatus;
                obj.IsPDFReportRequired = objDetails.IsPDFReportRequired;
                obj.IsQuarantineProduct = objDetails.IsQuarantineProduct;
                obj.GoodsInId = objDetails.GoodsInId;
                obj.GIQuery = objDetails.GIQuery;
                obj.ReqLotNo = objDetails.ReqLotNo;
                obj.LotNoCount = objDetails.LotNoCount;
                obj.ReleseStockDisbaleStatus = objDetails.ReleseStockDisbaleStatus;

                //[007] code start
                obj.ShortShipmentIds = objDetails.ShortShipmentIds;
                obj.ShortShipmentNos = objDetails.ShortShipmentNos;
                //[007] code end

                objDetails = null;
                return obj;
            }
        }
        /// <summary>
        /// call [usp_get_GILine_Quarantine_Status]
        /// </summary>
        /// <param name="iD"></param>
        /// <returns></returns>
        public static bool GetQuarantineStatus(int GILineId)
        {
            bool objReturn = Rebound.GlobalTrader.DAL.SiteProvider.GoodsInLine.GetQuarantineStatus(GILineId);
            return objReturn;
        }

        /// <summary>
        /// Calls [usp_update_GIQuery]
        /// </summary>
        /// <param name="IsSales"></param>
        /// <param name="IsPurchasing"></param>
        /// <param name="IsQualityApproval"></param>
        /// <param name="LoginID"></param>
        /// <param name="GoodsInLineId"></param>
        /// <returns></returns>
        public static bool UpdateGiQuery(System.Boolean IsSalesNotify, System.Boolean IsPurchaseNotify, System.Boolean IsQualityNotify, System.Int32? LoginID, System.Int32 GoodsInLineId, System.Int32 GoodsInId, System.String Query)
        {
            bool objReturn = Rebound.GlobalTrader.DAL.SiteProvider.GoodsInLine.UpdateGiQuery(IsSalesNotify, IsPurchaseNotify, IsQualityNotify, LoginID, GoodsInLineId, GoodsInId, Query);
            return objReturn;
        }
        /// <summary>
        /// Calls [usp_update_NotifyQuery]
        /// </summary>
        /// <param name="ID"></param>
        /// <param name="GILineQueryStatus"></param>
        /// <param name="IsPDFReportRequired"></param>
        /// <param name="IsQuarantineProduct"></param>
        /// <param name="QueryReply"></param>
        /// <param name="LoginId"></param>
        /// <returns></returns>
        public static bool NotifyQuery(System.Int32 ID, System.Int32? QueryApprovedStatusSales, System.Int32? QueryApprovedStatusPurchase, System.Int32? QueryApprovedStatusQuality, System.Boolean IsPDFReportRequired, System.Boolean IsQuarantineProduct, System.String QueryReply, System.Int32? LoginId, System.Int32 LoginType)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.GoodsInLine.NotifyQuery(ID, QueryApprovedStatusQuality, QueryApprovedStatusPurchase, QueryApprovedStatusQuality, IsPDFReportRequired, IsQuarantineProduct, QueryReply, LoginId, LoginType);
        }

        public static List<GoodsInLine> GIQueryStatusDropDown(System.Int32? IsPartialGIQueryStatus)
        {
            List<GoodsInLine> lst = new List<GoodsInLine>();
            try
            {
                List<GoodsInLineDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.GoodsInLine.GIQueryStatusDropDown(IsPartialGIQueryStatus);
                if (lstDetails == null)
                {
                    return new List<GoodsInLine>();
                }
                else
                {

                    foreach (GoodsInLineDetails objDetails in lstDetails)
                    {
                        Rebound.GlobalTrader.BLL.GoodsInLine obj = new Rebound.GlobalTrader.BLL.GoodsInLine();
                        obj.ID = objDetails.ID;
                        obj.Name = objDetails.Name;
                        lst.Add(obj);
                        obj = null;
                    }
                    lstDetails = null;

                }
            }
            catch (Exception ex)
            {
                throw new Exception("Failed to get GIQueryStatusDropDown DropDown", ex);
            }
            return lst;
        }

        public static bool UpdateGILineQuarantine(int iD, int loginID, bool Quarantine, int? ClientID, ref string message)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.GoodsInLine.UpdateGILineQuarantine(iD, loginID, Quarantine,ClientID, ref message);
        }
        /// <summary>
        /// Get sale email from GILine
        /// </summary>
        /// <param name="GILineID"></param>
        /// <returns></returns>
        public static int GetSaleEmail(int GILineID, out int StockId,out string partNo, out string atrSOLineNumbers)
        {
            StockId = 0;
            atrSOLineNumbers = "";
            return Rebound.GlobalTrader.DAL.SiteProvider.GoodsInLine.GetSaleEmail(GILineID,out StockId,out partNo, out atrSOLineNumbers);
        }
        public static Int32 InsertImage(System.Int32? GILineID, System.String caption, System.String GILineImageName, System.Int32? updatedBy)
        {
            Int32 objReturn = Rebound.GlobalTrader.DAL.SiteProvider.GoodsInLine.InsertImage(GILineID, caption, GILineImageName, updatedBy);
            return objReturn;
        }

        public static Int32 InsertPDF(System.Int32? GILineID, System.String caption, System.String GILinePdfName, System.Int32? updatedBy)
        {
            Int32 objReturn = Rebound.GlobalTrader.DAL.SiteProvider.GoodsInLine.InsertPDF(GILineID, caption, GILinePdfName, updatedBy);
            return objReturn;
        }
        public static List<StockImage> GetGILineImageList(System.Int32? GILineId, System.String fileType, System.Int32? GIId)
        {
            List<StockImageDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.GoodsInLine.GetGILineImageList(GILineId, fileType, GIId);
            if (lstDetails == null)
            {
                return new List<StockImage>();
            }
            else
            {
                List<StockImage> lst = new List<StockImage>();
                foreach (StockImageDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.StockImage obj = new Rebound.GlobalTrader.BLL.StockImage();
                    obj.ImageId = objDetails.ImageId;
                    obj.Caption = objDetails.Caption;
                    obj.ImageName = objDetails.ImageName;
                    obj.UpdatedBy = objDetails.UpdatedBy;
                    obj.By = objDetails.UpdatedByName;
                    obj.DLUP = objDetails.DLUP;
                    obj.ImageDocumentRefNo = objDetails.ImageDocumentRefNo;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }
        public static Int32 InsertGILineImage(System.Int32? resultNo, System.String caption, System.String ImageName, System.Int32? updatedBy, System.Int32? GIId)
        {
            Int32 objReturn = Rebound.GlobalTrader.DAL.SiteProvider.GoodsInLine.InsertGILineImage(resultNo, caption, ImageName, updatedBy, GIId);
            return objReturn;
        }
        public static bool DeleteGILineImage(System.Int32? ImageNo)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.GoodsInLine.DeleteGILineImage(ImageNo);
        }
        #region Add/Edit/Get Lot Code for GI Edit 
        /// <summary>
        ///  Calls [usp_itemsearch_GoodsInTempLotNo]
        /// </summary>
        /// <param name="clientId"></param>
        /// <param name="orderBy"></param>
        /// <param name="sortDir"></param>
        /// <param name="pageIndex"></param>
        /// <param name="pageSize"></param>
        /// <param name="goodsInNo"></param>
        /// <param name="goodsInLineNo"></param>
        /// <param name="loginNo"></param>
        /// <returns></returns>
        public static List<GoodsInLine> GITempLotSearch(System.Int32? clientId, System.Int32? orderBy, System.Int32? sortDir, System.Int32? pageIndex, System.Int32? pageSize, System.Int32? goodsInNo, System.Int32? goodsInLineNo, System.Int32? loginNo)
        {
            List<GoodsInLineDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.GoodsInLine.GITempLotSearch(clientId, orderBy, sortDir, pageIndex, pageSize, goodsInNo, goodsInLineNo, loginNo);
            if (lstDetails == null)
            {
                return new List<GoodsInLine>();
            }
            else
            {
                List<GoodsInLine> lst = new List<GoodsInLine>();
                foreach (GoodsInLineDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.GoodsInLine obj = new Rebound.GlobalTrader.BLL.GoodsInLine();
                    obj.LotNoId = objDetails.LotNoId;
                    obj.LotNumber = objDetails.LotNumber;
                    obj.GoodsInNo = objDetails.GoodsInNo;
                    obj.SubGroup = objDetails.SubGroup;
                    obj.Status = objDetails.Status;
                    obj.InvoiceLineNo = objDetails.InvoiceLineNo;
                    obj.GoodsInNumber = objDetails.GoodsInNumber;
                    obj.RowNum = objDetails.RowNum;
                    obj.RowCnt = objDetails.RowCnt;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }
        /// <summary>
        /// Calls [usp_insert_LotNo]
        /// </summary>
        /// <param name="subGroup"></param>
        /// <param name="lotNo"></param>
        /// <param name="goodsInId"></param>
        /// <param name="goodsInLineId"></param>
        /// <param name="updatedBy"></param>
        /// <param name="validateMessage"></param>
        /// <param name="totalLot"></param>
        /// <returns></returns>
        public static Int32 InsertLotNo(System.String subGroup, System.String lotNo, System.Int32? goodsInId, System.Int32? goodsInLineId, System.Int32? updatedBy, out System.String validateMessage, out System.Int32 totalLot)
        {
            Int32 objReturn = Rebound.GlobalTrader.DAL.SiteProvider.GoodsInLine.InsertLotNo(subGroup, lotNo, goodsInId, goodsInLineId, updatedBy, out validateMessage, out totalLot);
            return objReturn;
        }

        /// <summary>
        /// Calls [usp_update_LotNo]
        /// </summary>
        /// <param name="lotId"></param>
        /// <param name="subGroup"></param>
        /// <param name="lotNo"></param>
        /// <param name="goodsInId"></param>
        /// <param name="status"></param>
        /// <param name="goodsInLineId"></param>
        /// <param name="updatedBy"></param>
        /// <param name="validateMessage"></param>
        /// <returns></returns>
        public static Int32 UpdateLotNo(System.Int32? lotId, System.String subGroup, System.String lotNo, System.Int32? goodsInId, System.String status, System.Int32? goodsInLineId, System.Int32? updatedBy, out System.String validateMessage)
        {
            Int32 objReturn = Rebound.GlobalTrader.DAL.SiteProvider.GoodsInLine.UpdateLotNo(lotId, subGroup, lotNo, goodsInId, status, goodsInLineId, updatedBy, out validateMessage);
            return objReturn;
        }
        /// <summary>
        /// Calls [usp_insert_AllLotNo]
        /// </summary>
        /// <param name="goodsInId"></param>
        /// <param name="goodsInLineId"></param>
        /// <param name="updatedBy"></param>
        /// <param name="validateMessage"></param>
        /// <returns></returns>
        public static Int32 InsertAllLotNo(System.Int32? goodsInId, System.Int32? goodsInLineId, System.Int32? updatedBy, out System.String validateMessage)
        {
            Int32 objReturn = Rebound.GlobalTrader.DAL.SiteProvider.GoodsInLine.InsertAllLotNo(goodsInId, goodsInLineId, updatedBy, out validateMessage);
            return objReturn;
        }
        /// <summary>
        /// Calls [usp_select_AllLotNo]
        /// </summary>
        /// <param name="goodsInLineId"></param>
        /// <returns></returns>
        public static List<GoodsInLine> GetLot(System.Int32? goodsInLineId)
        {
            List<GoodsInLineDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.GoodsInLine.GetLot(goodsInLineId);
            if (lstDetails == null)
            {
                return new List<GoodsInLine>();
            }
            else
            {
                List<GoodsInLine> lst = new List<GoodsInLine>();
                foreach (GoodsInLineDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.GoodsInLine obj = new Rebound.GlobalTrader.BLL.GoodsInLine();
                    obj.LotNoId = objDetails.LotNoId;
                    obj.LotNumber = objDetails.LotNumber;
                    obj.SubGroup = objDetails.SubGroup;
                    obj.GoodsInNo = objDetails.GoodsInNo;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }
        /// <summary>
        /// Calls [usp_delete_LotNo]
        /// </summary>
        /// <param name="serialNoId"></param>
        /// <param name="status"></param>
        /// <param name="goodsInId"></param>
        /// <param name="goodsInLineId"></param>
        /// <param name="updatedBy"></param>
        /// <returns></returns>
        public static Int32 DeleteLotNo(System.Int32? serialNoId, System.String status, System.Int32? goodsInId, System.Int32? goodsInLineId, System.Int32? updatedBy)
        {
            Int32 objReturn = Rebound.GlobalTrader.DAL.SiteProvider.GoodsInLine.DeleteLotNo(serialNoId, status, goodsInId, goodsInLineId, updatedBy);
            return objReturn;
        }
        #endregion
        /// <summary>
        /// SaveShortShipment
        /// Calls [usp_insert_ShortShipment]
        /// </summary>
        /// <param name="ID"></param>
        /// <param name="Supplier"></param>
        /// <param name="SalesContact"></param>
        /// <param name="AirWayBill"></param>
        /// <param name="GoodsIn"></param>
        /// <param name="ReceivedDate"></param>
        /// <param name="RaisedBy"></param>
        /// <param name="Buyer"></param>
        /// <param name="PartNo"></param>
        /// <param name="Manufacturer"></param>
        /// <param name="QuantityOrder"></param>
        /// <param name="QuantityAdvised"></param>
        /// <param name="QuantityReceived"></param>
        /// <param name="ShortageQuantity"></param>
        /// <param name="ShortageValue"></param>
        /// <param name="loginID"></param>
        /// <param name="clientID"></param>
        /// <param name="message"></param>
        /// <returns></returns>
        public static bool SaveShortShipment(System.Int32 ID, System.Int32? Supplier, System.Int32? PurchaseOrderNo, System.Int32? SalesContact, System.String Reference, System.Int32 GoodsIn, System.DateTime? ReceivedDate, System.Int32? RaisedBy, System.Int32? Buyer, System.String PartNo, System.Int32? Manufacturer, System.Int32? QuantityOrder, System.String QuantityAdvised, System.Int32 QuantityReceived, System.Int32 ShortageQuantity, System.Double? ShortageValue, System.Int32 loginID, System.Int32? clientID, ref System.String message, ref System.Int32 ShortShipmentId, System.Boolean? IsPOHub, System.Int32? SupportTeamMemberNo, ref System.String NoReplyEmail, ref System.Int32 NoReplyId)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.GoodsInLine.SaveShortShipment(ID, Supplier, PurchaseOrderNo, SalesContact, Reference, GoodsIn, ReceivedDate, RaisedBy, Buyer, PartNo, Manufacturer, QuantityOrder, QuantityAdvised, QuantityReceived, ShortageQuantity, ShortageValue, loginID, clientID, ref message, ref ShortShipmentId, IsPOHub, SupportTeamMemberNo, ref NoReplyEmail, ref NoReplyId);
        }

        #endregion

        #region Methods for new GI Lines Query Message
        /// <summary>
        /// Get
        /// Calls [usp_SelectAll_GILines_QueryMessages]
        /// </summary>
        public static List<GoodsInLine> GetGILineQueryMessage(System.Int32? goodsInId, System.Int32? GiLineNo, System.Int32? LoginId, System.Int32? ClientId)
        {
            List<GoodsInLineDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.GoodsInLine.GetGILineQueryMessage(goodsInId, GiLineNo, LoginId, ClientId);
            if (lstDetails == null)
            {
                return new List<GoodsInLine>();
            }
            else
            {
                List<GoodsInLine> lst = new List<GoodsInLine>();
                foreach (GoodsInLineDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.GoodsInLine obj = new Rebound.GlobalTrader.BLL.GoodsInLine();
                    obj.QueryMessage = objDetails.QueryMessage;
                    obj.QueryMessageApproval = objDetails.QueryMessageApproval;
                    obj.NotifyToSales = objDetails.NotifyToSales;
                    obj.NotifyToQuality = objDetails.NotifyToQuality;
                    obj.NotifyToPurchasing = objDetails.NotifyToPurchasing;
                    obj.IsSendMail = objDetails.IsSendMail;
                    obj.MyMessage = objDetails.MyMessage;
                    obj.UpdatedBy = objDetails.UpdatedBy;
                    obj.DLUP = objDetails.DLUP;
                    obj.ISInitialNotifyToSales = objDetails.ISInitialNotifyToSales;
                    obj.ISInitialNotifyToQuality = objDetails.ISInitialNotifyToQuality;
                    obj.ISInitialNotifyToPurchase = objDetails.ISInitialNotifyToPurchase;
                    obj.IsInitialMessage = objDetails.IsInitialMessage;
                    obj.SalesApprovalStatus = objDetails.SalesApprovalStatus;
                    obj.QualityApprovalStatus = objDetails.QualityApprovalStatus;
                    obj.PurchaseApprovalStatus = objDetails.PurchaseApprovalStatus;
                    obj.GIQueryNumber = objDetails.GIQueryNumber;
                    obj.InDraftMode = objDetails.InDraftMode;
                    obj.Gi_QueryId = objDetails.Gi_QueryId;
                    obj.WarehouseRemark = objDetails.WarehouseRemark;
                    obj.ISSalesPermission = objDetails.ISSalesPermission;
                    obj.ISPurchasingPermission = objDetails.ISPurchasingPermission;
                    obj.ISQualityPermission = objDetails.ISQualityPermission;
                    //[011] code start
                    obj.ParentSalesApprovalStatus = objDetails.ParentSalesApprovalStatus;
                    obj.ParentQualityApprovalStatus = objDetails.ParentQualityApprovalStatus;
                    obj.ParentPurchaseApprovalStatus = objDetails.ParentPurchaseApprovalStatus;
                    obj.CurrentPurchasingApprover = objDetails.CurrentPurchasingApprover;
                    obj.CurrentSalesApprover = objDetails.CurrentSalesApprover;
                    obj.CCUsersName = objDetails.CCUsersName;
                    obj.DraftQueryMessage = objDetails.DraftQueryMessage;
                    obj.QueryRaisedBy = objDetails.QueryRaisedBy;

                    obj.IsNotGBLPermissionForSales = objDetails.IsNotGBLPermissionForSales;
                    obj.IsNotGBLPermissionForPurch = objDetails.IsNotGBLPermissionForPurch;
                    obj.IsNotGBLPermissionForQaulity = objDetails.IsNotGBLPermissionForQaulity;
                    obj.ClientName = objDetails.ClientName;

                    obj.Quarantined = objDetails.Quarantined;
                    obj.SalesGroupId = objDetails.SalesGroupId;
                    obj.SalesGroupName = objDetails.SalesGroupName;

                    obj.PurchasingGroupId = objDetails.PurchasingGroupId;
                    obj.PurchasingGroupName = objDetails.PurchasingGroupName;

                    obj.ProcessPurchaseApproverName = objDetails.ProcessPurchaseApproverName;
                    obj.ProcessSalesApproverName = objDetails.ProcessSalesApproverName;
                    obj.ProcessQualityApproverName = objDetails.ProcessQualityApproverName;
                    //[011]  coded end
                    obj.C1 = objDetails.C1;
                    obj.C2 = objDetails.C2;
                    obj.C3 = objDetails.C3;
                    obj.C4 = objDetails.C4;
                    obj.C5 = objDetails.C5;
                    obj.C6 = objDetails.C6;
                    obj.C7 = objDetails.C7;
                    obj.C8 = objDetails.C8;
                    obj.C9 = objDetails.C9;
                    obj.C10 = objDetails.C10;
                    obj.ISReleaseStock = objDetails.ISReleaseStock;
                    obj.IsQueryColumn = objDetails.IsQueryColumn;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }

        /// <summary>
        /// Get
        /// Calls [usp_Insert_GILineQueryMessage]
        /// </summary>
        public static List<BLL.GoodsInLine> AddGILineQueryMessage(System.Int32? GI_QueryId, System.Int32? goodsInId, System.Int32? GiLineNo, System.Int32? LoginId, System.String QueryMessage, System.Boolean? IsSalesNotify, System.Boolean? IsQualityNotify, System.Boolean? IsPurchasingNotify, System.Int32? ClientNo, System.String CCUserId, System.String CCGroupIDs)
        {
            List<GoodsInLineDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.GoodsInLine.AddGILineQueryMessage(GI_QueryId, goodsInId, GiLineNo, LoginId, QueryMessage, IsSalesNotify, IsQualityNotify, IsPurchasingNotify, ClientNo, CCUserId, CCGroupIDs);
            if (lstDetails == null)
            {
                return new List<GoodsInLine>();
            }
            else
            {
                List<GoodsInLine> lst = new List<GoodsInLine>();
                foreach (GoodsInLineDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.GoodsInLine obj = new Rebound.GlobalTrader.BLL.GoodsInLine();
                    obj.Result = objDetails.Result;
                    obj.ISInitialNotifyToSales = objDetails.ISInitialNotifyToSales;
                    obj.ISInitialNotifyToQuality = objDetails.ISInitialNotifyToQuality;
                    obj.ISInitialNotifyToPurchase = objDetails.ISInitialNotifyToPurchase;
                    obj.UpdatedBy = objDetails.UpdatedBy;
                    obj.NoReplyId = objDetails.NoReplyId;
                    obj.NoReplyEmail = objDetails.NoReplyEmail;
                    obj.QueryMessage = objDetails.QueryMessage;
                    obj.ClientName = objDetails.ClientName;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }

        }

        /// <summary>
        /// Get
        /// Calls [usp_Select_GILines_Approvals]
        /// </summary>
        public static List<GoodsInLine> GetGILineApprovals(System.Int32? goodsInId, System.Int32? GiLineNo)
        {
            List<GoodsInLineDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.GoodsInLine.GetGILineApprovals(goodsInId, GiLineNo);
            if (lstDetails == null)
            {
                return new List<GoodsInLine>();
            }
            else
            {
                List<GoodsInLine> lst = new List<GoodsInLine>();
                foreach (GoodsInLineDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.GoodsInLine obj = new Rebound.GlobalTrader.BLL.GoodsInLine();
                    obj.RaisedBy = objDetails.RaisedBy;
                    obj.Department = objDetails.Department;
                    obj.ApprovalName = objDetails.ApprovalName;
                    obj.ApprovedDate = objDetails.ApprovedDate;
                    obj.ApprovalStatus = objDetails.ApprovalStatus;
                    obj.CurrentPurchasingApprover = objDetails.CurrentPurchasingApprover;
                    obj.CurrentSalesApprover = objDetails.CurrentSalesApprover;
                    obj.CurrentSalesApprovalStatus = objDetails.CurrentSalesApprovalStatus;
                    obj.CurrentPurchasingApprovalStatus = objDetails.CurrentPurchasingApprovalStatus;
                    obj.CurrentQualityApprovalStatus = objDetails.CurrentQualityApprovalStatus;
                    obj.ParentSalesApprovalStatus = objDetails.ParentSalesApprovalStatus;
                    obj.ParentPurchaseApprovalStatus = objDetails.ParentPurchaseApprovalStatus;
                    obj.ParentQualityApprovalStatus = objDetails.ParentQualityApprovalStatus;
                    obj.Gi_QueryId = objDetails.Gi_QueryId;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }

        // [001] code start
        /// <summary>
        /// GetPDFListForGoodsInLine
        /// Calls [usp_selectAll_PDF_for_GoodsInLine]
        /// </summary>
        public static List<PDFDocument> GetPDFListForGoodsInLine(System.Int32? GoodsInLineId)
        {
            List<PDFDocumentDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.GoodsInLine.GetPDFListForGoodsInLine(GoodsInLineId);
            if (lstDetails == null)
            {
                return new List<PDFDocument>();
            }
            else
            {
                List<PDFDocument> lst = new List<PDFDocument>();
                foreach (PDFDocumentDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.PDFDocument obj = new Rebound.GlobalTrader.BLL.PDFDocument();
                    obj.PDFDocumentId = objDetails.PDFDocumentId;
                    obj.PDFDocumentRefNo = objDetails.PDFDocumentRefNo;
                    obj.Caption = objDetails.Caption;
                    obj.FullCaption = objDetails.FullCaption;
                    obj.FileName = objDetails.FileName;
                    obj.UpdatedBy = objDetails.UpdatedBy;
                    obj.DLUP = objDetails.DLUP;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }

        public static List<SupplierPoApproval> GetImageData(System.Int32? GoodInLineId)
        {
            List<SupplierPoApprovalDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.GoodsInLine.GetImageData(GoodInLineId);
            if (lstDetails == null)
            {
                return new List<SupplierPoApproval>();
            }
            else
            {
                List<SupplierPoApproval> lst = new List<SupplierPoApproval>();
                foreach (SupplierPoApprovalDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.SupplierPoApproval obj = new Rebound.GlobalTrader.BLL.SupplierPoApproval();
                    obj.ImageId = objDetails.ImageId;
                    obj.Caption = objDetails.Caption;
                    obj.ImageName = objDetails.ImageName;
                    obj.UpdatedBy = objDetails.UpdatedBy;
                    obj.DLUP = objDetails.DLUP;
                    obj.FullCaption = objDetails.FullCaption;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }

        /// <summary>
        /// Get
        /// Calls [usp_Insert_GILineQueryApprovalResponce]
        /// </summary>
        public static List<BLL.GoodsInLine> AddGILineQueryApprovalResponce(System.Int32? GI_QueryId, System.Int32? goodsInId, System.Int32? GiLineNo, System.Int32? LoginId, System.String QueryMessage, System.Int32? SalesApprovalStatus, System.Int32? QualityApprovalStatus, System.Int32? PurchasingApprovalStatus, System.String CCUserId, System.Int32? ClientNo, System.String CCGroupIDs, System.String ApproverHtml,System.Int32? TotalCheckBoxcount,System.Int32? CheckedTotalCheckBoxcount,System.String GetEnableCheckBoxIds)
        {
            List<GoodsInLineDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.GoodsInLine.AddGILineQueryApprovalResponce(GI_QueryId, goodsInId, GiLineNo, LoginId, QueryMessage, SalesApprovalStatus, QualityApprovalStatus, PurchasingApprovalStatus, CCUserId, ClientNo, CCGroupIDs, ApproverHtml, TotalCheckBoxcount, CheckedTotalCheckBoxcount, GetEnableCheckBoxIds);
            if (lstDetails == null)
            {
                return new List<GoodsInLine>();
            }
            else
            {
                List<GoodsInLine> lst = new List<GoodsInLine>();
                foreach (GoodsInLineDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.GoodsInLine obj = new Rebound.GlobalTrader.BLL.GoodsInLine();
                    obj.Result = objDetails.Result;
                    obj.ISInitialNotifyToSales = objDetails.ISInitialNotifyToSales;
                    obj.ISInitialNotifyToQuality = objDetails.ISInitialNotifyToQuality;
                    obj.ISInitialNotifyToPurchase = objDetails.ISInitialNotifyToPurchase;
                    obj.UpdatedBy = objDetails.UpdatedBy;
                    obj.NoReplyId = objDetails.NoReplyId;
                    obj.NoReplyEmail = objDetails.NoReplyEmail;
                    obj.QueryMessage = objDetails.QueryMessage;
                    obj.SalesApproverId = objDetails.SalesApproverId;
                    obj.PurchasingApproverId = objDetails.PurchasingApproverId;
                    obj.ClientName = objDetails.ClientName;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }

        }

        /// <summary>
        /// Get
        /// Calls [usp_Insert_ChangeApprover]
        /// </summary>
        public static List<BLL.GoodsInLine> ChangeApprover(System.Int32? GI_QueryId, System.Int32? NewSalesApprover, System.Int32? NewPurchasingApprover, System.Int32? LoginId, System.Int32? ClientNo)
        {
            List<GoodsInLineDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.GoodsInLine.ChangeApprover(GI_QueryId, NewSalesApprover, NewPurchasingApprover, LoginId, ClientNo);
            if (lstDetails == null)
            {
                return new List<GoodsInLine>();
            }
            else
            {
                List<GoodsInLine> lst = new List<GoodsInLine>();
                foreach (GoodsInLineDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.GoodsInLine obj = new Rebound.GlobalTrader.BLL.GoodsInLine();
                    obj.PreviousSalesApproverId = objDetails.PreviousSalesApproverId;
                    obj.PreviousPurchasingApproverId = objDetails.PreviousPurchasingApproverId;
                    obj.PreviousSalesApproverName = objDetails.PreviousSalesApproverName;
                    obj.PreviousPurchasingApproverName = objDetails.PreviousPurchasingApproverName;
                    obj.CurrentSalesApprover = objDetails.CurrentSalesApprover;
                    obj.CurrentPurchasingApprover = objDetails.CurrentPurchasingApprover;
                    obj.NoReplyId = objDetails.NoReplyId;
                    obj.NoReplyEmail = objDetails.NoReplyEmail;
                    obj.PurchaseOrderNo = objDetails.PurchaseOrderNo;
                    obj.GoodsInNo = objDetails.GoodsInNo;
                    obj.GoodsInId = objDetails.GoodsInId;
                    obj.GoodsInLineId = objDetails.GoodsInLineId;
                    obj.GIQueryNumber = objDetails.GIQueryNumber;
                    obj.QueryMessage = objDetails.QueryMessage;
                    obj.ClientName = objDetails.ClientName;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }

        }
        /// <summary>
        /// calls [usp_select_MyGIQueries]
        /// </summary>
        /// <param name="loginId"></param>
        /// <param name="clientId"></param>
        /// <returns></returns>
        public static List<GoodsInLine> GetMyGIQueries(System.Int32? loginId, System.Int32? clientId)
        {
            List<GoodsInLineDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.GoodsInLine.GetMyGIQueries(loginId, clientId);
            if (lstDetails == null)
            {
                return new List<GoodsInLine>();
            }
            else
            {
                List<GoodsInLine> lst = new List<GoodsInLine>();
                foreach (GoodsInLineDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.GoodsInLine obj = new Rebound.GlobalTrader.BLL.GoodsInLine();
                    obj.GoodsInId = objDetails.GoodsInId;
                    obj.GoodsInNo = objDetails.GoodsInNo;
                    obj.GoodsInLineId = objDetails.GoodsInLineId;
                    obj.SalesOrderNumber = objDetails.SalesOrderNumber;
                    obj.DeliveryDate = objDetails.DeliveryDate;
                    obj.SalesOrderNo = objDetails.SalesOrderNo;
                    obj.Approvers = objDetails.Approvers;
                    obj.Status = objDetails.Status;

                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }
        /// <summary>
        /// calls [GetEnhancedInspection]
        /// </summary>
        /// <returns></returns>
        public static List<GoodsInLine> GetEnhancedInspection()
        {
            List<GoodsInLine> lst = new List<GoodsInLine>();
            try
            {
                List<GoodsInLineDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.GoodsInLine.GetEnhancedInspection();
                if (lstDetails == null)
                {
                    return new List<GoodsInLine>();
                }
                else
                {

                    foreach (GoodsInLineDetails objDetails in lstDetails)
                    {
                        Rebound.GlobalTrader.BLL.GoodsInLine obj = new Rebound.GlobalTrader.BLL.GoodsInLine();
                        obj.ID = objDetails.ID;
                        obj.Name = objDetails.Name;
                        lst.Add(obj);
                        obj = null;
                    }
                    lstDetails = null;

                }
            }
            catch (Exception ex)
            {
                throw new Exception("Failed to get GetEnhancedInspection DropDown", ex);
            }
            return lst;
        }

        /// <summary>
        /// Get
        /// Calls [usp_GetQueryMessagesCCUsers]
        /// </summary>
        public static List<BLL.GoodsInLine> GetCCUserFoEmail(System.Int32? GI_QueryId)
        {
            List<GoodsInLineDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.GoodsInLine.GetCCUserFoEmail(GI_QueryId);
            if (lstDetails == null)
            {
                return new List<GoodsInLine>();
            }
            else
            {
                List<GoodsInLine> lst = new List<GoodsInLine>();
                foreach (GoodsInLineDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.GoodsInLine obj = new Rebound.GlobalTrader.BLL.GoodsInLine();
                    obj.CCUSerId = objDetails.CCUSerId;
                    obj.IsCCMailGroupId = objDetails.IsCCMailGroupId;
                    obj.MailType = objDetails.MailType;
                    obj.SalesGroupName = objDetails.SalesGroupName;
                    obj.PurchasingGroupName = objDetails.PurchasingGroupName;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }

        }
        public static List<GoodsInLine> GetMFRLabelListJSON()
        {
            List<GoodsInLineDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.GoodsInLine.GetMFRLabelListJSON();
            if (lstDetails == null)
            {
                return new List<GoodsInLine>();
            }
            else
            {
                List<GoodsInLine> lst = new List<GoodsInLine>();
                foreach (GoodsInLineDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.GoodsInLine obj = new Rebound.GlobalTrader.BLL.GoodsInLine();
                    obj.ID = objDetails.ID;
                    obj.Name = objDetails.Name;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }
        public static Int32 DraftGILineQueryMessage(System.Int32? GI_QueryId, System.Int32 GoodsInId, System.Int32? GoodsInLineId, System.Int32? LoginID, System.String QueryMessage)
        {
            Int32 objReturn = Rebound.GlobalTrader.DAL.SiteProvider.GoodsInLine.DraftGILineQueryMessage(GI_QueryId, GoodsInId, GoodsInLineId, LoginID, QueryMessage);
            return objReturn;
        }
        public static bool DeleteGILine_PDF(int PDFDocId)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.GoodsInLine.DeleteGILine_PDF(PDFDocId);
        }
        public static bool DeleteGILine_Image(int ImageId)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.GoodsInLine.DeleteGILine_Image(ImageId);
        }

        public static Int32 RenameCaption(System.Int32? AttachmentId, System.String Caption, System.String Type, System.Int32? LoginId)
        {
            Int32 objReturn = Rebound.GlobalTrader.DAL.SiteProvider.GoodsInLine.RenameCaption(AttachmentId, Caption, Type, LoginId);
            return objReturn;
        }
        public static List<BLL.GoodsInLine> BulkAttachmentDelete(System.String ImageAttachments, System.String PdfAttachments)
        {
            List<GoodsInLineDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.GoodsInLine.BulkAttachmentDelete(ImageAttachments, PdfAttachments);
            if (lstDetails == null)
            {
                return new List<GoodsInLine>();
            }
            else
            {
                List<GoodsInLine> lst = new List<GoodsInLine>();
                foreach (GoodsInLineDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.GoodsInLine obj = new Rebound.GlobalTrader.BLL.GoodsInLine();
                    obj.AttachmentFileName = objDetails.AttachmentFileName;
                    obj.AttachmentType = objDetails.AttachmentType;

                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }

        //[015] start
        public static int StockAttachmentFileCountByFileName(System.String FileName, System.String FileType)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.GoodsInLine.StockAttachmentFileCountByFileName(FileName, FileType);
        }
        //[015] End

        ///<summary>
        ///
        /// 
        ///</summary>
        public static bool CheckDeleteAttcmntPermission(int? ClientNo, int? LoginUser)
        {
            bool blnReturn = Rebound.GlobalTrader.DAL.SiteProvider.GoodsInLine.CheckDeleteAttcmntPermission(ClientNo, LoginUser);
            return blnReturn;
        }

        /// <summary>
        /// Get
        /// Calls [usp_Previous_GILines_QueryMessages]
        /// </summary>
        public static List<GoodsInLine> GetPreviousChatOnGILine(System.Int32? GI_QueryId)
        {
            List<GoodsInLineDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.GoodsInLine.GetPreviousChatOnGILine(GI_QueryId);
            if (lstDetails == null)
            {
                return new List<GoodsInLine>();
            }
            else
            {
                List<GoodsInLine> lst = new List<GoodsInLine>();
                foreach (GoodsInLineDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.GoodsInLine obj = new Rebound.GlobalTrader.BLL.GoodsInLine();
                    obj.MessageAuther = objDetails.MessageAuther;
                    obj.QueryMessage = objDetails.QueryMessage;
                    obj.DLUP = objDetails.DLUP;
                    obj.Status = objDetails.Status;

                    //[011]  coded end
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }
        /// <summary>
        /// calls [usp_select_MyQualityGIQueries]
        /// </summary>
        /// <param name="loginId"></param>
        /// <param name="clientId"></param>
        /// <returns></returns>
        public static List<GoodsInLine> MyQualityGIQueries(System.Int32? loginId, System.Int32? clientId)
        {
            List<GoodsInLineDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.GoodsInLine.MyQualityGIQueries(loginId, clientId);
            if (lstDetails == null)
            {
                return new List<GoodsInLine>();
            }
            else
            {
                List<GoodsInLine> lst = new List<GoodsInLine>();
                foreach (GoodsInLineDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.GoodsInLine obj = new Rebound.GlobalTrader.BLL.GoodsInLine();
                    obj.GoodsInId = objDetails.GoodsInId;
                    obj.GoodsInNo = objDetails.GoodsInNo;
                    obj.GoodsInLineId = objDetails.GoodsInLineId;
                    obj.SalesOrderNumber = objDetails.SalesOrderNumber;
                    obj.DeliveryDate = objDetails.DeliveryDate;
                    obj.SalesOrderNo = objDetails.SalesOrderNo;
                    obj.Status = objDetails.Status;

                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }

        /// <summary>
        /// calls [usp_GILine_StartOrCloseInspection]
        /// </summary>
        /// <param name="iD"></param>
        /// <param name="Comment"></param>
        /// <param name="LoginId"></param>
        /// <returns></returns>
        public static bool StartInspection(int iD, string Comment, System.Int32? LoginId)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.GoodsInLine.StartOrCloseInspection(iD, Comment, LoginId, 1);
        }
        /// <summary>
        /// calls [usp_GILine_StartOrCloseInspection]
        /// </summary>
        /// <param name="iD"></param>
        /// <param name="Comment"></param>
        /// <param name="LoginId"></param>
        /// <returns></returns>
        public static bool CloseInspection(int iD, string Comment, System.Int32? LoginId)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.GoodsInLine.StartOrCloseInspection(iD, Comment, LoginId, 2);
        }
        /// <summary>
        /// usp_GI_InspectionsLog
        /// </summary>
        public static List<GoodsInLine> GetInspectionHistory(System.Int32? goodsInLineNo)
        {
            List<GoodsInLineDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.GoodsInLine.GetInspectionHistory(goodsInLineNo);
            if (lstDetails == null)
            {
                return new List<GoodsInLine>();
            }
            else
            {
                List<GoodsInLine> lst = new List<GoodsInLine>();
                foreach (GoodsInLineDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.GoodsInLine obj = new Rebound.GlobalTrader.BLL.GoodsInLine();
                    obj.InspectionLogId = objDetails.InspectionLogId;
                    obj.ActionTaken = objDetails.ActionTaken;
                    obj.DLUP = objDetails.DLUP;
                    obj.InspectedByUser = objDetails.InspectedByUser;
                    obj.CommentText = objDetails.CommentText;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }

        /// <summary>
        /// GetCloseInspectionData
        /// Calls [usp_select_CloseInspectionData]
        /// </summary>
        public static GoodsInLine GetCloseInspectionData(System.Int32? goodsInLineId)
        {
            Rebound.GlobalTrader.DAL.GoodsInLineDetails objDetails = Rebound.GlobalTrader.DAL.SiteProvider.GoodsInLine.GetCloseInspectionData(goodsInLineId);
            if (objDetails == null)
            {
                return null;
            }
            else
            {
                GoodsInLine obj = new GoodsInLine();
                obj.PartNumberFilled = objDetails.PartNumberFilled;
                obj.PartNumberFilledChkEnable = objDetails.PartNumberFilledChkEnable;
                obj.PackageBreakdownComplete = objDetails.PackageBreakdownComplete;
                obj.PackageBreakdownCompleteChkEnable = objDetails.PackageBreakdownCompleteChkEnable;
                obj.PhotosAttached = objDetails.PhotosAttached;
                obj.PhotosAttachedChkEnable = objDetails.PhotosAttachedChkEnable;
                obj.BarcodeScannedTicked = objDetails.BarcodeScannedTicked;
                obj.BarcodeScannedTickedChkEnable = objDetails.BarcodeScannedTickedChkEnable;
                obj.QueryFormatUse = objDetails.QueryFormatUse;
                obj.QueryFormatUseChkEnable = objDetails.QueryFormatUseChkEnable;

                objDetails = null;
                return obj;
            }
        }


        #endregion

        #region Related Receipt
        /// <summary>
        /// Get
        /// Calls [usp_SelectAll_GILines_QueryMessages]
        /// </summary>
        public static List<GoodsInLine> GetRelatedReceiptLines(System.Int32? GiLineNo, System.Int32? goodsInId, System.Int32? LoginId)
        {
            List<GoodsInLineDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.GoodsInLine.GetRelatedReceiptLines(GiLineNo, goodsInId, LoginId);
            if (lstDetails == null)
            {
                return new List<GoodsInLine>();
            }
            else
            {
                List<GoodsInLine> lst = new List<GoodsInLine>();
                foreach (GoodsInLineDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.GoodsInLine obj = new Rebound.GlobalTrader.BLL.GoodsInLine();
                    obj.Part = objDetails.Part;
                    obj.ROHS = objDetails.ROHS;
                    obj.supplierType = objDetails.supplierType;
                    obj.GoodsInNo = objDetails.GoodsInNo;
                    obj.DateReceived = objDetails.DateReceived;
                    obj.PIBy = objDetails.PIBy;

                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }
        public static string GetPackagingBreakdownHtml(System.Int32 goodsInLineId, System.Boolean? IsPDF)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.GoodsInLine.GetPackagingBreakdownHtml(goodsInLineId, IsPDF);
        }
        #endregion

        #region [008] Get Scanned Barcode status codes from tbGI_BarcodeScanStatus table
        //[008] start
        public static List<GoodsInLine> GetGILineBarcodeStatusDropdown(System.Int32? loginId, System.Int32? clientId)
        {
            List<GoodsInLineDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.GoodsInLine.GetBarcodeStatusList(loginId: loginId, clientId: clientId);
            if (lstDetails == null)
            {
                return new List<GoodsInLine>();
            }
            else
            {
                List<GoodsInLine> lst = new List<GoodsInLine>();
                foreach (GoodsInLineDetails obj in lstDetails)
                {
                    GoodsInLine o = new GoodsInLine()
                    {
                        GIBarcodeScanStatusId = obj.GIBarcodeScanStatusId,
                        GIBarcodesScanStatusName = obj.GIBarcodesScanStatusName
                    };
                    lst.Add(o);
                    o = null;
                }
                lstDetails = null;
                return lst;
            }
        }
        //[008] end




        #endregion

        #region RP-2546
        public static GoodsInLine GetGiLineReleaseStatus(int GoodsInLineId)
        {
            Rebound.GlobalTrader.DAL.GoodsInLineDetails objDetails = Rebound.GlobalTrader.DAL.SiteProvider.GoodsInLine.GetGiLineReleaseStatus(GoodsInLineId);
            if (objDetails == null)
            {
                return null;
            }
            else
            {
                GoodsInLine obj = new GoodsInLine();
                obj.InspectedBy = objDetails.InspectedBy;
                obj.DateInspected = objDetails.DateInspected;
                obj.AlertMessage = objDetails.AlertMessage;
                obj.ISCloseInspection = objDetails.ISCloseInspection;
                objDetails = null;
                return obj;
            }
        }

        public static GoodsInLine GetGiLineInspectionStatus(int GoodsInLineId)
        {
            Rebound.GlobalTrader.DAL.GoodsInLineDetails objDetails = Rebound.GlobalTrader.DAL.SiteProvider.GoodsInLine.GetGiLineInspectionStatus(GoodsInLineId);
            if (objDetails == null)
            {
                return null;
            }
            else
            {
                GoodsInLine obj = new GoodsInLine();
                obj.InspectionStatus = objDetails.InspectionStatus;
                obj.InspectedBy = objDetails.InspectedBy;
                obj.DateInspected = objDetails.DateInspected;
                obj.AlertMessage = objDetails.AlertMessage;
                obj.ISCloseInspection = objDetails.ISCloseInspection;

                objDetails = null;
                return obj;
            }
        }

        /// <summary>
        /// Get
        /// Calls [usp_select_GoodsInLineSerialNo]
        /// </summary>
        public static List<GoodsInLine> GetSerialNoForSplit(System.Int32? goodsInLineId)
        {
            List<GoodsInLineDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.GoodsInLine.GetSerialNoForSplit(goodsInLineId);
            if (lstDetails == null)
            {
                return new List<GoodsInLine>();
            }
            else
            {
                List<GoodsInLine> lst = new List<GoodsInLine>();
                foreach (GoodsInLineDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.GoodsInLine obj = new Rebound.GlobalTrader.BLL.GoodsInLine();
                    obj.SerialId = objDetails.SerialId;
                    obj.Box = objDetails.Box;
                    obj.SerialNumber = objDetails.SerialNumber;

                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }

        #endregion

    }
    public class PackagingBreakdown
    {
        public System.Boolean? FactorySealed { get; set; }
        public System.Int32? NumberOfPacks { get; set; }
        public System.Double? PackSize { get; set; }
        public System.String DateCode { get; set; }
        public System.String BatchCode { get; set; }
        public System.Double? TotalPackSize { get; set; }
        public System.Int32? PackagingTypeId { get; set; }
        public System.Int32? MFRLabelId { get; set; }
    }
    public class DateCode
    {
        public System.Int32 ID { get; set; }
        public System.String DateCodes { get; set; }
        public System.Int32? Quantity { get; set; }
        public System.Int32? QuantityReceived { get; set; }
    }


}
