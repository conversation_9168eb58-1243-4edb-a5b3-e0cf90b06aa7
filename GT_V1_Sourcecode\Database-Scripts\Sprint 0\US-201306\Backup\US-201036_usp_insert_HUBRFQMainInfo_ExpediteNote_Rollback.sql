GO

/****** Object:  StoredProcedure [dbo].[usp_insert_HUBRFQMainInfo_ExpediteNote]    Script Date: 4/23/2024 5:36:28 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO



ALTER PROCEDURE [dbo].[usp_insert_HUBRFQMainInfo_ExpediteNote]        
/********************************************************************************************        
    Created by      Date         Remarks    
  Shashi Keshar   19/07/2017   Save Expedite Notes    
--********************************************************************************************/        
    @HUBRFQId INT        
  , @ExpediteNotes   nvarchar(MAX) = Null     
  , @UpdatedBy INT 
  , @EmailSendTo INT = 0
  , @NewId INT = NULL OUTPUT        
AS         
BEGIN     
 Declare @ID int , @Group int= 0 , @RequestedToPOHUB int

      -- select top 1 @RequestedToPOHUB=UpdateByPH from tbBOM where BOMId= @HUBRFQId                         
   BEGIN                 
   
    INSERT  INTO dbo.tbHUBRFQExpediteNotes (        
            HUBRFQNo    
          , ExpediteNotes              
          , UpdatedBy    
          , DLUP  
		 ,MailSendTo
		  
          )        
    VALUES  (        
              @HUBRFQId        
            , @ExpediteNotes       
            , @UpdatedBy        
            , CURRENT_TIMESTAMP
			, @EmailSendTo 
            )  
			
			 if(@Group=0)
			begin
			 set @Group=  SCOPE_IDENTITY()
			 end
			  update tbHUBRFQExpediteNotes set GroupID=@Group  where [HUBRFQExpediteNotesId]=SCOPE_IDENTITY()
			  update tbCustomerRequirement set ExpediteDate= CURRENT_TIMESTAMP where CustomerRequirementId=@ID
      
   END 
                             
           
         
         
   
          
    SET @NewId = SCOPE_IDENTITY()      
                
  	SELECT  @NewId 
         
END 



GO


