Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.BOMMainInfo_Edit=function(n){Rebound.GlobalTrader.Site.Controls.Forms.BOMMainInfo_Edit.initializeBase(this,[n]);this._intBOMID=-1;this._blnRequestedToPoHub=!1};Rebound.GlobalTrader.Site.Controls.Forms.BOMMainInfo_Edit.prototype={get_intBOMID:function(){return this._intBOMID},set_intBOMID:function(n){this._intBOMID!==n&&(this._intBOMID=n)},get_BomCode:function(){return this._BomCode},set_BomCode:function(n){this._BomCode!==n&&(this._BomCode=n)},get_BomName:function(){return this._BomName},set_BomName:function(n){this._BomName!==n&&(this._BomName=n)},get_BomCompanyName:function(){return this._BomCompanyName},set_BomCompanyName:function(n){this._BomCompanyName!==n&&(this._BomCompanyName=n)},get_BomCompanyNo:function(){return this._BomCompanyNo},set_BomCompanyNo:function(n){this._BomCompanyNo!==n&&(this._BomCompanyNo=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Forms.BOMMainInfo_Edit.callBaseMethod(this,"initialize");this.addShown(Function.createDelegate(this,this.formShown))},formShown:function(){this._blnFirstTimeShown&&this.addSave(Function.createDelegate(this,this.saveClicked));this._blnRequestedToPoHub&&(this.showField("ctlCode",!1),this.showField("ctlName",!1),this.showField("ctlCompany",!1),this.showField("ctlContact",!1),this.showField("ctlInActive",!1),this.showField("ctlNotes",!1),this.showField("ctlCurrency",!1),this.showField("ctlCurrentSupplier",!1),this.showField("ctlQuoteRequired",!1),this.showField("ctlAS9120",!1))},dispose:function(){this.isDisposed||(this._intBOMID=null,Rebound.GlobalTrader.Site.Controls.Forms.BOMMainInfo_Edit.callBaseMethod(this,"dispose"))},saveClicked:function(){if(this._blnRequestedToPoHub!=!1||this.validateForm()){var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/BOMMainInfo");n.set_DataObject("BOMMainInfo");n.set_DataAction("SaveEdit");n.addParameter("id",this._intBOMID);n.addParameter("Code",this.getFieldValue("ctlCode"));n.addParameter("Name",this.getFieldValue("ctlName"));n.addParameter("Company",this.getFieldValue("ctlCompany"));n.addParameter("Contact",this.getFieldValue("ctlContact"));n.addParameter("InActive",this.getFieldValue("ctlInActive"));n.addParameter("Notes",this.getFieldValue("ctlNotes"));n.addParameter("Currency",this.getFieldValue("ctlCurrency"));n.addParameter("CurrentSupplier",this.getFieldValue("ctlCurrentSupplier"));n.addParameter("QuoteRequired",this.getFieldValue("ctlQuoteRequired"));n.addParameter("AS9120",this.getFieldValue("ctlAS9120"));n.addParameter("Contact2No",this.getFieldValue("ctlSalesman"));n.addDataOK(Function.createDelegate(this,this.saveEditComplete));n.addError(Function.createDelegate(this,this.saveEditError));n.addTimeout(Function.createDelegate(this,this.saveEditError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null}},saveEditError:function(n){this._strErrorMessage=n._errorMessage;this.onSaveError()},saveEditComplete:function(n){n._result.Result==!0?(this.showSavedOK(!0),this.onSaveComplete(),$("#ctl00_cphMain_ctlMainInfo_ctlDB_BuyAndPriceMergine").hide()):(this._strErrorMessage=n._errorMessage,this.onSaveError())},validateForm:function(){this.onValidate();return this.autoValidateFields()},showFieldsLoading:function(n){this.showFieldLoading("ctlCompany",n)}};Rebound.GlobalTrader.Site.Controls.Forms.BOMMainInfo_Edit.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.BOMMainInfo_Edit",Rebound.GlobalTrader.Site.Controls.Forms.Base,Sys.IDisposable);