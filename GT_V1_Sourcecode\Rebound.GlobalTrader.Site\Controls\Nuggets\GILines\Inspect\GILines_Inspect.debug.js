///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.GILines_Inspect = function(element) { 
	Rebound.GlobalTrader.Site.Controls.Forms.GILines_Inspect.initializeBase(this, [element]);
	this._intLineID = -1;
	this._intGIID = 0;
    this._ctlConfirm = null;
    this._ShipInCost = 0;
    this._PurchasePrice = 0;
	this._ClientPurchasePrice = 0;
	this._inspectedBy = 0;
	this._alertMessage = "";
	this._inspectionStatus = 0;
	this._isCloseInspection = 0;
};

Rebound.GlobalTrader.Site.Controls.Forms.GILines_Inspect.prototype = {

	get_intGIID: function() { return this._intGIID; }, set_intGIID: function(v) { if (this._intGIID !== v)  this._intGIID = v; }, 
	get_intLineID: function() { return this._intLineID; }, set_intLineID: function(value) { if (this._intLineID !== value)  this._intLineID = value; }, 

	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.Forms.GILines_Inspect.callBaseMethod(this, "initialize");
		this.addShown(Function.createDelegate(this, this.formShown));
	    this.getGoodsIn();
	},

	dispose: function() { 
		if (this.isDisposed) return;
		if (this._ctlConfirm) this._ctlConfirm.dispose();
		this._ctlConfirm = null;
		this._intLineID = null;
		this._intGIID = null;
		Rebound.GlobalTrader.Site.Controls.Forms.GILines_Inspect.callBaseMethod(this, "dispose");
	},

	formShown: function () {
		this.checkGiLineInspectionStatus();
		if (this._blnFirstTimeShown) {
			this._ctlConfirm = this.getFieldComponent("ctlConfirm");
			this._ctlConfirm.addClickYesEvent(Function.createDelegate(this, this.yesClicked));
			this._ctlConfirm.addClickNoEvent(Function.createDelegate(this, this.noClicked));
		}
	},

	getGoodsIn: function() {
		$R_FN.showElement(this._pnlLines, false);
		var obj = new Rebound.GlobalTrader.Site.Data();
		obj.set_PathToData("controls/Nuggets/GIMainInfo");
		obj.set_DataObject("GIMainInfo");
		obj.set_DataAction("GetData");
		obj.addParameter("ID", this._intGIID);
		obj.addDataOK(Function.createDelegate(this, this.getGoodsInOK));
		obj.addError(Function.createDelegate(this, this.getGoodsInError));
		obj.addTimeout(Function.createDelegate(this, this.getGoodsInError));
		$R_DQ.addToQueue(obj);
		$R_DQ.processQueue();
		obj = null;
	},
	
	getGoodsInOK: function(args) {
		var res = args._result;
		this.setFieldsFromGoodsIn(res);
	},
	
	setFieldsFromGoodsIn: function(res) {
		if (!res) return;
		this.setFieldValue("ctlGoodsIn", res.GoodsInNumber);
		this.setFieldValue("ctlSupplier", res.SupplierName);
	},

	getGoodsInError: function(args) {
		this._strErrorMessage = args._errorMessage;
		this.onSaveError();
	},

	yesClicked: function () {
		debugger;
		if (this._isCloseInspection == true) {
			var allowSaving = true;
			var zeroValue = false;
			var errorMessage = "";
			if (this._PurchasePrice == 0) {
				this._PurchasePrice = this._ClientPurchasePrice;
			}

			if ((this._ShipInCost == 0)) {
				zeroValue = true;
				errorMessage = 'Ship In Cost value is 0. Are you sure want to release stock?'
			} else if (this._PurchasePrice == 0) {
				zeroValue = true;
				errorMessage = 'Purchase Price is 0. Are you sure want to release stock?'
			} else {
				zeroValue = false;
				errorMessage = "";
			}
			var isConfirm = true;
			if (zeroValue == true) {
				if (confirm(errorMessage) == true) {
					isConfirm = true;
				} else {
					isConfirm = false;
				}
			}
			if (isConfirm == true) {

				this.showSaving(true);
				var obj = new Rebound.GlobalTrader.Site.Data();
				obj.set_PathToData("Controls/Nuggets/GILines");
				obj.set_DataObject("GILines");
				obj.set_DataAction("Inspect");
				obj.addParameter("id", this._intLineID);

				obj.addDataOK(Function.createDelegate(this, this.saveComplete));
				obj.addError(Function.createDelegate(this, this.saveError));
				obj.addTimeout(Function.createDelegate(this, this.saveError));
				$R_DQ.addToQueue(obj);
				$R_DQ.processQueue();
				obj = null;
				this.onSave();
			}
		}
		else {
			alert(this._alertMessage);
        }
        
        
	},

	noClicked: function() {
		this.showSaving(false);
		this.onNotConfirmed();
	},

	saveError: function(args) {
		this.showSaving(false);
		this._strErrorMessage = args._errorMessage;
		this.onSaveError();
	},

	saveComplete: function(args) {
		this.showSaving(false);
		if (args._result.Result == true) {
			this.onSaveComplete();
		} else {
			this._strErrorMessage = args._errorMessage;
			this.onSaveError();
		}
	}

	, checkGiLineInspectionStatus: function () {
		var obj = new Rebound.GlobalTrader.Site.Data();
		obj.set_PathToData("controls/Nuggets/GILines");
		obj.set_DataObject("GILines");
		obj.set_DataAction("GetGiInspectionStatus");
		obj.addParameter("ID", this._intLineID);
		obj.addDataOK(Function.createDelegate(this, (args) => {
			var res = args._result;
			this._isCloseInspection = res.ISCloseInspection;
			this._inspectionStatus = res.InspectionStatus;
			this._inspectedBy = res.InspectedBy;
			this._alertMessage = res.AlertMessage;

		}));
		obj.addError(Function.createDelegate(this, (args) => {
			this._strErrorMessage = args._errorMessage;
			this.onSaveError();

		}));
		obj.addTimeout(Function.createDelegate(this, (args) => {
			this._strErrorMessage = args._errorMessage;
			this.onSaveError();

		}));
		$R_DQ.addToQueue(obj);
		$R_DQ.processQueue();
		obj = null;
	}
};

Rebound.GlobalTrader.Site.Controls.Forms.GILines_Inspect.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.GILines_Inspect", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
