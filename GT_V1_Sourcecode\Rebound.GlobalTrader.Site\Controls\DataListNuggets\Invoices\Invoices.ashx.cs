//Marker     Changed by      Date         Remarks
//[001]      Vinay           21/09/2012   Add expoted only filter
//[002]      Vinay           22/11/2012   Add Failed only  filter
//[003]     <PERSON>     07/02/2020    Add WareHouse Search filter
using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;
using Rebound.GlobalTrader.Site.Enumerations;

namespace Rebound.GlobalTrader.Site.Controls.DataListNuggets.Data {

	public class Invoices : Base {

		/// <summary>
		/// Gets the main data
		/// </summary>
		protected override void GetData() {
			JsonObject jsn = new JsonObject();

			//get data	
			List<Invoice> lst = Invoice.DataListNugget(
				SessionManager.ClientID
				, GetFormValue_NullableInt("PageIndex", 0)
                , GetFormValue_NullableInt("ResultsLimit", 50)
				, GetFormValue_NullableInt("SortIndex")
				, GetFormValue_NullableInt("SortDir", SortColumnDirection.ASC)
                , GetFormValue_StringForNameSearch("CMName")
				, GetFormValue_NullableInt("Salesman")
				, GetFormValue_StringForSearch("CustPO")
				, GetFormValue_Boolean("IncludePaid")
				, GetFormValue_NullableInt("InvoiceNoLo")
				, GetFormValue_NullableInt("InvoiceNoHi")
				, GetFormValue_NullableInt("SONoLo")
				, GetFormValue_NullableInt("SONoHi")
				, GetFormValue_NullableDateTime("InvoiceDateFrom")
				, GetFormValue_NullableDateTime("InvoiceDateTo")
				, GetFormValue_Boolean("RecentOnly")
                //[001] code start
                , GetFormValue_Boolean("ExportedOnly")
                //[001] code end
                //[002] code start
                , GetFormValue_Boolean("FailedOnly")
                //[002] code end
                , GetFormValue_Boolean("NotExported")
                //[003]code start | Add wharehouse Search [Warehouse]
                 , GetFormValue_NullableInt("Warehouse")
				  //[003] cod end
				  , GetFormValue_NullableInt("CountryNo")
				  , GetFormValue_Boolean("InvoiceHold")
            );

			//check counts
			jsn.AddVariable("TotalRecords", (lst.Count > 0) ? lst[0].RowCnt : 0);

			//format data
			JsonObject jsnRowsArray = new JsonObject(true);
			for (int i = 0; i < lst.Count; i++) {
				JsonObject jsnRow = new JsonObject();
				jsnRow.AddVariable("ID", lst[i].InvoiceId);
				jsnRow.AddVariable("No", lst[i].InvoiceNumber);
				jsnRow.AddVariable("CMNo", lst[i].CompanyNo);
				jsnRow.AddVariable("CM", lst[i].CompanyName);
				jsnRow.AddVariable("CustPONO", lst[i].CustomerPO);
				jsnRow.AddVariable("Value", Functions.FormatCurrency(lst[i].InvoiceValue, lst[i].CurrencyCode));
				jsnRow.AddVariable("SONo", lst[i].SalesOrderNo);
				jsnRow.AddVariable("SO", lst[i].SalesOrderNumber);
				jsnRow.AddVariable("Date", Functions.FormatDate(lst[i].InvoiceDate));
				jsnRowsArray.AddVariable(jsnRow);
				jsnRow.Dispose();
				jsnRow = null;
			}
			jsn.AddVariable("Results", jsnRowsArray);
			OutputResult(jsn);
			jsnRowsArray.Dispose();
			jsnRowsArray = null;
			jsn.Dispose();
			jsn = null;
			base.GetData();
		}
       
       
	}
}
