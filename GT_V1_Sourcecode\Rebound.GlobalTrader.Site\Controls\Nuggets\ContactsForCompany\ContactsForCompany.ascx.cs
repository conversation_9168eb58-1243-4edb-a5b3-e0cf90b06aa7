//Marker     Changed by      Date         Remarks
//[001]      Vinay           09/07/2012   This need for Rebound- Invoice bulk Emailer
//[002]      Vinay           04/10/2012   Degete Ref:#26#  - Add two more columns contact to identify Default Purchase ledger and Default Sales ledger
using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;
using System.Text;
using Rebound.GlobalTrader.Site.Controls;
using Rebound.GlobalTrader.Site;

namespace Rebound.GlobalTrader.Site.Controls.Nuggets {
	public partial class ContactsForCompany : Base {

		#region Controls

		protected IconButton _ibtnAdd;
		protected IconButton _ibtnDelete;
		protected IconButton _ibtnMakeDefaultSO;
		protected IconButton _ibtnMakeDefaultPO;
		protected FlexiDataTable _tblContacts;
        //[002] code start
        protected IconButton _ibtnDefaultPOLedger;
        protected IconButton _ibtnDefaultSOLedger;
        //[002] code end

		#endregion

		#region Properties

		private int _intCompanyID = -1;
		public int CompanyID {
			get { return _intCompanyID; }
			set { _intCompanyID = value; }
		}

		private string _strCompanyName;
		public string CompanyName {
			get { return _strCompanyName; }
			set { _strCompanyName = value; }
		}

		private int _intContactID = -1;
		public int ContactID {
			get { return _intContactID; }
			set { _intContactID = value; }
		}

		private string _strContactName;
		public string ContactName {
			get { return _strContactName; }
			set { _strContactName = value; }
		}

		private bool _blnCanAdd = true;
		public bool CanAdd {
			get { return _blnCanAdd; }
			set { _blnCanAdd = value; }
		}

		private bool _blnCanDelete = true;
		public bool CanDelete {
			get { return _blnCanDelete; }
			set { _blnCanDelete = value; }
		}

		private bool _blnCanMakeDefaultForSOs = true;
		public bool CanMakeDefaultForSOs {
			get { return _blnCanMakeDefaultForSOs; }
			set { _blnCanMakeDefaultForSOs = value; }
		}

		private bool _blnCanMakeDefaultForPOs = true;
		public bool CanMakeDefaultForPOs {
			get { return _blnCanMakeDefaultForPOs; }
			set { _blnCanMakeDefaultForPOs = value; }
		}

        //[002] code start
        private bool _blnCanDefaultPOLedger = true;
        public bool CanDefaultPOLedger
        {
            get { return _blnCanDefaultPOLedger; }
            set { _blnCanDefaultPOLedger = value; }
        }

        private bool _blnCanDefaultSOLedger = true;
        public bool CanDefaultSOLedger
        {
            get { return _blnCanDefaultSOLedger; }
            set { _blnCanDefaultSOLedger = value; }
        }
        //[002] code end


		#endregion

		#region Overrides

		/// <summary>
		/// Oninit
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
			base.OnInit(e);
			WireUpControls();
			AddScriptReference("Controls.Nuggets.ContactsForCompany.ContactsForCompany");
			if (_intCompanyID == -1) _intCompanyID = _objQSManager.CompanyID;
			if (_intContactID == -1) _intContactID = _objQSManager.ContactID;
			SetupTable();
		}

		protected override void OnLoad(EventArgs e) {
			SetupScriptDescriptors();
			TitleText = String.Format(Functions.GetGlobalResource("Nuggets", "ContactsForCompany"), _strCompanyName);
			base.OnLoad(e);
		}

		protected override void OnPreRender(EventArgs e) {
			_ibtnAdd.Visible = _blnCanAdd;
			_ibtnDelete.Visible = _blnCanDelete;
			_ibtnMakeDefaultSO.Visible = _blnCanMakeDefaultForSOs;
			_ibtnMakeDefaultPO.Visible = _blnCanMakeDefaultForPOs;
            //[002] code start
            _ibtnDefaultPOLedger.Visible = _blnCanDefaultPOLedger;
            _ibtnDefaultSOLedger.Visible = _blnCanDefaultSOLedger;
            //[002] code end
			base.OnPreRender(e);
		}

		#endregion

		/// <summary>
		/// sets up the list of contacts table
		/// </summary>
		private void SetupTable() {
			_tblContacts.Columns.Add(new FlexiDataColumn("ContactName", WidthManager.GetWidth(WidthManager.ColumnWidth.ContactName)));
			_tblContacts.Columns.Add(new FlexiDataColumn("Tel", WidthManager.GetWidth(WidthManager.ColumnWidth.TelNo)));
			_tblContacts.Columns.Add(new FlexiDataColumn("Email", WidthManager.GetWidth(WidthManager.ColumnWidth.EmailAddress)));
            _tblContacts.Columns.Add(new FlexiDataColumn("IsDefaultSO", WidthManager.GetWidth(WidthManager.ColumnWidth.DefaultSOPO)));
            _tblContacts.Columns.Add(new FlexiDataColumn("IsDefaultPO", WidthManager.GetWidth(WidthManager.ColumnWidth.DefaultSOPO)));
            //[001] code start
            _tblContacts.Columns.Add(new FlexiDataColumn("IsFinanceContact", WidthManager.GetWidth(WidthManager.ColumnWidth.FinanceContact)));
            //[001] code end
            //[002] code start
            _tblContacts.Columns.Add(new FlexiDataColumn("IsDefaultPOLedger"));
            _tblContacts.Columns.Add(new FlexiDataColumn("IsDefaultSOLedger"));
            //[002] code end
		}

		/// <summary>
		/// Sets up AJAX script descriptors
		/// </summary>
		private void SetupScriptDescriptors() {
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.Nuggets.ContactsForCompany", ctlDesignBase.ClientID);
			if (_blnCanAdd) _scScriptControlDescriptor.AddElementProperty("ibtnAdd", _ibtnAdd.ClientID);
			if (_blnCanDelete) _scScriptControlDescriptor.AddElementProperty("ibtnDelete", _ibtnDelete.ClientID);
			if (_blnCanMakeDefaultForSOs) _scScriptControlDescriptor.AddElementProperty("ibtnMakeDefaultSO", _ibtnMakeDefaultSO.ClientID);
			if (_blnCanMakeDefaultForPOs) _scScriptControlDescriptor.AddElementProperty("ibtnMakeDefaultPO", _ibtnMakeDefaultPO.ClientID);
            //[002] code start
            if (_blnCanDefaultPOLedger) _scScriptControlDescriptor.AddElementProperty("ibtnDefaultPOLedger", _ibtnDefaultPOLedger.ClientID);
            if (_blnCanDefaultSOLedger) _scScriptControlDescriptor.AddElementProperty("ibtnDefaultSOLedger", _ibtnDefaultSOLedger.ClientID);
            //[002] code end
			_scScriptControlDescriptor.AddProperty("intCompanyID", _intCompanyID);
			_scScriptControlDescriptor.AddProperty("strCompanyName", _strCompanyName);
			_scScriptControlDescriptor.AddProperty("intContactID", _intContactID);
			_scScriptControlDescriptor.AddProperty("strContactName", _strContactName);
			_scScriptControlDescriptor.AddComponentProperty("tbl", _tblContacts.ClientID);
		}

		/// <summary>
		/// Wire up controls from the ascx
		/// </summary>
		private void WireUpControls() {
			_ibtnAdd = FindIconButton("ibtnAdd");
			_ibtnDelete = FindIconButton("ibtnDelete");
			_ibtnMakeDefaultSO = FindIconButton("ibtnMakeDefaultSO");
			_ibtnMakeDefaultPO = FindIconButton("ibtnMakeDefaultPO");
            //[002] code start
            _ibtnDefaultPOLedger = FindIconButton("ibtnDefaultPOLedger");
            _ibtnDefaultSOLedger = FindIconButton("ibtnDefaultSOLedger");
            //[002] code end
			_tblContacts = (FlexiDataTable)ctlDesignBase.FindContentControl("tblContacts");
			//_ctlContactsForCompany_Edit = (Forms.ContactsForCompany_Edit)ctlDesignBase.FindFormControl("ctlContactsForCompany_Edit");
		}
	}
}
