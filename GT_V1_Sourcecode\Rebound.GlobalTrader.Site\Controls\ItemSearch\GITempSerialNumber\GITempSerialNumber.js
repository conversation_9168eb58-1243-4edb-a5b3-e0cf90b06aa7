Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.ItemSearch");Rebound.GlobalTrader.Site.Controls.ItemSearch.GITempSerialNumber=function(n){Rebound.GlobalTrader.Site.Controls.ItemSearch.GITempSerialNumber.initializeBase(this,[n]);this._intGoodsInLineNo=-1;this._intGoodsInNo=-1;this._elementId=""};Rebound.GlobalTrader.Site.Controls.ItemSearch.GITempSerialNumber.prototype={addPotentialStatusChange:function(n){this.get_events().addHandler("PotentialStatusChange",n)},removePotentialStatusChange:function(n){this.get_events().removeHandler("PotentialStatusChange",n)},onPotentialStatusChange:function(){var n=this.get_events().getHandler("PotentialStatusChange");n&&n(this,Sys.EventArgs.Empty)},initialize:function(){Rebound.GlobalTrader.Site.Controls.ItemSearch.GITempSerialNumber.callBaseMethod(this,"initialize");this.addSetupData(Function.createDelegate(this,this.doSetupData));this.addGetDataComplete(Function.createDelegate(this,this.doGetDataComplete));this.addSearched(Function.createDelegate(this,this.doSearched))},dispose:function(){this.isDisposed||(this._intGoodsInLineNo=null,this._intGoodsInNo=null,this._elementId=null,Rebound.GlobalTrader.Site.Controls.ItemSearch.GITempSerialNumber.callBaseMethod(this,"dispose"))},refereshGroup:function(){$find("ctl00_cphMain_ctlShippingLines_ctlDB_ctl14_frmShip_ctlDB_ctlGiSerialNumber_ctlDB_ctlGroup_ddl")._intGoodsInLineNo=this._intGoodsInLineNo;$find("ctl00_cphMain_ctlShippingLines_ctlDB_ctl14_frmShip_ctlDB_ctlGiSerialNumber_ctlDB_ctlGroup_ddl").getData()},doSetupData:function(){this._objData.set_PathToData("controls/ItemSearch/GITempSerialNumber");this._objData.set_DataObject("GITempSerialNumber");this._objData.set_DataAction("GetData");this._objData.addParameter("GoodsInId",this._intGoodsInNo);this._objData.addParameter("GoodsInLineId",this._intGoodsInLineNo)},getGroupValue:function(){return this.getFieldValue("ctlGroup")},doGetDataComplete:function(){var t,r,n,i,u,f;for(this._invoiceExist=!1,t=0,r=this._objResult.Results.length;t<r;t++)n=this._objResult.Results[t],this._invoiceExist=n.InvoiceLineNo>0?!0:!1,i="",i=this._invoiceExist==!1?[n.SubGroup,n.SerialNo,n.GoodsNumber,String.format("<a href=\"javascript:void(0);\" Style='color:#006600' class='Delete'  onclick=\"$find('{0}').removeItem({1},'{2}');\" class=\"quickSearchReselect\">Delete<\/a>",this._elementId,n.ID,n.Status)]:[n.SubGroup,n.SerialNo,n.GoodsNumber,String.format('<a href="javascript:void(0);" Style=\'color: #006600\' Hidden=\'true\'  onclick="return false;" class="quickSearchReselect">Delete<\/a>',this._elementId,n.ID)],u={SerialNoId:n.ID,SubGroup:n.SubGroup,SerialNo:n.SerialNo,GoodsInNo:n.GoodsInNo,Status:n.Status,InvoiceLineNo:n.InvoiceLineNo},f=n.Inactive?"ceased":"",this._tblResults.addRow(i,n.ID,!1,u,f),i=null,n=null},doSearched:function(){this.onPotentialStatusChange()}};Rebound.GlobalTrader.Site.Controls.ItemSearch.GITempSerialNumber.registerClass("Rebound.GlobalTrader.Site.Controls.ItemSearch.GITempSerialNumber",Rebound.GlobalTrader.Site.Controls.ItemSearch.Base,Sys.IDisposable);