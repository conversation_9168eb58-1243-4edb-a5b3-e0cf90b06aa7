using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site;
using Rebound.GlobalTrader.Site.Controls;

namespace Rebound.GlobalTrader.Site.Controls.Forms {
    public partial class BOMItems_ConfirmRemovePartwatch : Base
    {

        #region Locals
        protected FlexiDataTable _tblAllReleasedetails;
        protected Panel _pnlLoadingAllRelease;
        protected Panel _pnlAllReleaseError;
        protected Panel _pnlAllRelease;
        #endregion

        #region Overrides

        /// <summary>
        /// OnInit
        /// </summary>
        /// <param name="e"></param>
        protected override void OnInit(EventArgs e) {
			base.OnInit(e);
            TitleText = Functions.GetGlobalResource("FormTitles", "BOMItems_SaveRemoveApplyPartwatch");
            AddScriptReference("Controls.Nuggets.BOMItems.ConfirmRemovePartwatch.BOMItems_ConfirmRemovePartwatch");
            WireUpControls();
            SetupTable();

        }

		/// <summary>
		/// OnPreRender
		/// </summary>
		/// <param name="e"></param>
		protected override void OnPreRender(EventArgs e) {
			SetupScriptDescriptors();
			base.OnPreRender(e);
		}

        private void WireUpControls()
        {

           // _tblAllReleasedetails = (FlexiDataTable)ctlDesignBase.FindContentControl("tblAllReleasedetails");
            

        }
        private void SetupTable()
        {
            //_tblAllReleasedetails.AllowSelection = false;
            
            //_tblAllReleasedetails.Columns.Add(new FlexiDataColumn("Supplier", "PartNo", WidthManager.GetWidth(WidthManager.ColumnWidth.PartNo)));
            //_tblAllReleasedetails.Columns.Add(new FlexiDataColumn("BuyPrice", WidthManager.GetWidth(WidthManager.ColumnWidth.CurrencyValue)));
            //_tblAllReleasedetails.Columns.Add(new FlexiDataColumn("UnitSellPrice", WidthManager.GetWidth(WidthManager.ColumnWidth.CurrencyValue)));
            //_tblAllReleasedetails.Columns.Add(new FlexiDataColumn("SetWarningText"));

        }
        #endregion

        /// <summary>
        /// Setup Script Descriptors
        /// </summary>
        private void SetupScriptDescriptors() {
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.Forms.BOMItems_ConfirmRemovePartwatch", ctlDesignBase.ClientID);
            //_scScriptControlDescriptor.AddComponentProperty("tblAllReleasedetails", FindFieldControl("ctlSerialNoDetail", "tblAllReleasedetails").ClientID);

        }


	}
}