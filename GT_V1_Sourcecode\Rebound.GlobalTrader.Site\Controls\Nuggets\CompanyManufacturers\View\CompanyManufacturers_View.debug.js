///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.CompanyManufacturers_View = function (element) {
    Rebound.GlobalTrader.Site.Controls.Forms.CompanyManufacturers_View.initializeBase(this, [element]);
    this._intManufacturerLinkID = -1;
};

Rebound.GlobalTrader.Site.Controls.Forms.CompanyManufacturers_View.prototype = {
    initialize: function () {
        Rebound.GlobalTrader.Site.Controls.Forms.CompanyManufacturers_View.callBaseMethod(this, "initialize");
        this.addShown(Function.createDelegate(this, this.formShown));
        $('#ctl00_cphMain_ctlManufacturersSupplied_ctlDB_ctl14_frmView_ctlDB_ctlRating_ctl03_ctlStarRating').css('pointer-events', 'none');
        $("#ctl00_cphMain_ctlManufacturersSupplied_ctlDB_ctl14_frmView_ctlDB_ctlRating_ctl03_ctlStarRating_star5").remove();
    },

    formShown: function () {
        if (this._blnFirstTimeShown) {
            this.addSave(Function.createDelegate(this, this.saveClicked));
        }
    },

    saveClicked: function () {
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/CompanyManufacturers");
        obj.set_DataObject("CompanyManufacturers");
        obj.set_DataAction("SaveFranchise");
        obj.addParameter("ID", this._intManufacturerLinkID);
        obj.addParameter("IsFranchised", this.getFieldValue('ctlFranchise'));
        obj.addDataOK(Function.createDelegate(this, this.saveEditComplete));
        obj.addError(Function.createDelegate(this, this.saveEditError));
        obj.addTimeout(Function.createDelegate(this, this.saveEditError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    saveEditError: function (args) {
        this._strErrorMessage = args._errorMessage;
        this.onSaveError();
    },

    saveEditComplete: function (args) {
        if (args._result.Result == true) {
            this.onSaveComplete();
        } else {
            this._strErrorMessage = args._errorMessage;
            this.onSaveError();
        }
    },

    dispose: function () {
        if (this.isDisposed) return;
        if (this._ctlConfirm) this._ctlConfirm.dispose();
        this._ctlConfirm = null;
        this._intManufacturerLinkID = null;
        this._intCommunicationLogID = null;
        Rebound.GlobalTrader.Site.Controls.Forms.CompanyManufacturers_View.callBaseMethod(this, "dispose");
    },
};

Rebound.GlobalTrader.Site.Controls.Forms.CompanyManufacturers_View.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.CompanyManufacturers_View", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
