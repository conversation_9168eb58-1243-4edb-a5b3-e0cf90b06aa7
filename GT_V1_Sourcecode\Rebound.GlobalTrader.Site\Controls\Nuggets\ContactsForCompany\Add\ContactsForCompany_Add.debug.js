///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//Marker     Changed by      Date         Remarks
//[001]      Vinay           09/07/2012   This need for Rebound- Invoice bulk Emailer
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.ContactsForCompany_Add = function(element) { 
	Rebound.GlobalTrader.Site.Controls.Forms.ContactsForCompany_Add.initializeBase(this, [element]);
	this._intCompanyID = -1;
	this._intNewID = -1;
	this._ctlAddress = null;
};

Rebound.GlobalTrader.Site.Controls.Forms.ContactsForCompany_Add.prototype = {

    get_intCompanyID: function() { return this._intCompanyID; }, set_intCompanyID: function(value) { if (this._intCompanyID !== value) this._intCompanyID = value; },

    initialize: function() {
        Rebound.GlobalTrader.Site.Controls.Forms.ContactsForCompany_Add.callBaseMethod(this, "initialize");
        this.addShown(Function.createDelegate(this, this.formShown));
    },

    dispose: function() {
        if (this.isDisposed) return;
        if (this._ctlAddress) this._ctlAddress.dispose();
        this._ctlAddress = null;
        this._intCompanyID = null;
        this._intNewID = null;
        Rebound.GlobalTrader.Site.Controls.Forms.ContactsForCompany_Add.callBaseMethod(this, "dispose");
    },

    formShown: function() {
        if (this._blnFirstTimeShown) {
            var fnSave = Function.createDelegate(this, this.saveClicked);
            $R_IBTN.addClick(this._ibtnSave, fnSave);
            $R_IBTN.addClick(this._ibtnSave_Footer, fnSave);
            var fnCancel = Function.createDelegate(this, this.cancelClicked);
            $R_IBTN.addClick(this._ibtnCancel, fnCancel);
            $R_IBTN.addClick(this._ibtnCancel_Footer, fnCancel);
            //this._ctlAddress = $find(this.getField("ctlAddress").ID);
            //this._ctlAddress._ctlRelatedForm = this;
        }
        this.setFormFieldsToDefaults();
        //this.getFieldDropDownData("ctlCountry");
        this.getFieldControl("ctlCompanyAddress")._intCompanyID = this._intCompanyID;
        this.getFieldDropDownData("ctlCompanyAddress");
    },

    saveClicked: function() {
        if (!this.validateForm()) return;
        if (!this.validateEmail()) return;
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/ContactsForCompany");
        obj.set_DataObject("ContactsForCompany");
        obj.set_DataAction("AddNew");
        obj.addParameter("id", this._intCompanyID);
        obj.addParameter("FirstName", this.getFieldValue("ctlFirstName"));
        obj.addParameter("Surname", this.getFieldValue("ctlSurname"));
        obj.addParameter("JobTitle", this.getFieldValue("ctlJobTitle"));
        obj.addParameter("Tel", this.getFieldValue("ctlTel"));
        obj.addParameter("Fax", this.getFieldValue("ctlFax"));
        obj.addParameter("Extension", this.getFieldValue("ctlExtension"));
        obj.addParameter("HomeTel", this.getFieldValue("ctlHomeTel"));
        obj.addParameter("MobileTel", this.getFieldValue("ctlMobileTel"));
        obj.addParameter("Email", this.getFieldValue("ctlEmail"));
        obj.addParameter("TextOnlyEmail", this.getFieldValue("ctlTextOnlyEmail"));
        obj.addParameter("Nickname", this.getFieldValue("ctlNickname"));
        //obj.addParameter("HasAddress", false);// this._ctlAddress.addressHasBeenEntered());
        //obj.addParameter("AddressID",null);// this._ctlAddress._intAddressID);
        //obj.addParameter("AddressName",null);// this.getFieldValue("ctlAddressName"));
        //obj.addParameter("Address1", null);//this.getFieldValue("ctlLine1"));
        //obj.addParameter("Address2",null);// this.getFieldValue("ctlLine2"));
        //obj.addParameter("Address3", null);// this.getFieldValue("ctlLine3"));
        //obj.addParameter("Town", null);//this.getFieldValue("ctlTown"));
        //obj.addParameter("County", null);//this.getFieldValue("ctlCounty"));
        //obj.addParameter("Country", null);//this.getFieldValue("ctlCountry"));
        //obj.addParameter("Postcode",null);// this.getFieldValue("ctlPostcode"));
        obj.addParameter("CompanyAddress", this.getFieldValue("ctlCompanyAddress"));
        //[001] code start
        obj.addParameter("FinanceContact", this.getFieldValue("ctlFinanceContact"));
        //[001] code end
        obj.addParameter("IsSendShipmentNotification", this.getFieldValue("ctlSendShipmentNotification"));
        obj.addDataOK(Function.createDelegate(this, this.saveEditComplete));
        obj.addError(Function.createDelegate(this, this.saveEditError));
        obj.addTimeout(Function.createDelegate(this, this.saveEditError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
        this.onSave();
    },

    saveEditError: function(args) {
        this._strErrorMessage = args._errorMessage;
        this.onSaveError();
    },

    saveEditComplete: function(args) {
        if (args._result.Result == true) {
            this._intNewID = args._result.NewID;
            this.onSaveComplete();
        } else {
            this._strErrorMessage = args._errorMessage;
            this.onSaveError();
        }
    },

    validateForm: function() {
        this.onValidate();
        var blnOK = this.autoValidateFields();
        //if (!this._ctlAddress.validateFields()) blnOK = false;
        if (!blnOK) this.showError(true);
        return blnOK;
    },
    //[001] code start
    validateEmail: function() {
        if (this.getFieldValue("ctlFinanceContact") == true && this.checkFieldEntered("ctlEmail") == false) {
            this.showError(true, $R_RES.ContactEmailMessage);
            return false;
        }
        else
            return true;
    },
    //[001] code end
    cancelClicked: function() {
        this.onCancel();
    }

};

Rebound.GlobalTrader.Site.Controls.Forms.ContactsForCompany_Add.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.ContactsForCompany_Add", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
