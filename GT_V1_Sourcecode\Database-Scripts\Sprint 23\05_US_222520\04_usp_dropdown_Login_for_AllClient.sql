﻿
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO
/*
--============================================================================================================================
TASK			UPDATED BY       DATE				ACTION		DESCRIPTION  
[US-222520]     Phuc Hoang		 10-Mar-2025		UPDATE		IPO - Quote's Task Reminder on HUBRFQ with Sales Team in GT
==============================================================================================================================  
*/

CREATE OR ALTER PROCEDURE [dbo].[usp_dropdown_Login_for_AllClient] (      
  @ClientNo int = NULL  
  , @TeamNo int = 0 
  , @DivisionNo int = 0  
  , @ExcludeLoginNo int = 0  
) 
AS
BEGIN
    SELECT  LoginId
          , ISNULL(lg.EmployeeName, '') + ' [' + LTRIM(REPLACE(REPLACE(ISNULL(cl.ClientCode, ''), 'RET ', ''), 'RE', '')) + ']'  AS EmployeeName
    FROM    dbo.tbLogin lg  WITH(NOLOCK)
	JOIN dbo.tbClient cl WITH(NOLOCK) ON cl.ClientId = lg.ClientNo 
    WHERE  ISNULL(lg.Inactive, 0) = 0 AND ISNULL(cl.Inactive, 0) = 0
    ORDER BY EmployeeName  

END
