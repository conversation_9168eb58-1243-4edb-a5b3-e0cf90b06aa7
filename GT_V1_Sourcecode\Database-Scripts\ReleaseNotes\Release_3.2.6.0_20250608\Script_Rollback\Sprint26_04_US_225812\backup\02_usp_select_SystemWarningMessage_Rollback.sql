﻿  
  
  
CREATE OR ALTER PROCEDURE usp_select_SystemWarningMessage    
    @SystemWarningMessageNo int    
AS     
    SELECT    
  swm.SystemWarningMessageId,  
  swm.WarningText,  
  wm.WarningName AS WarningKey,  
  CASE WHEN swm.ApplyToCatagoryNo=1 THEN 'Manufacturer'  
       WHEN swm.ApplyToCatagoryNo=2 THEN 'Product'  
    WHEN swm.ApplyToCatagoryNo=3 THEN 'Vendor'  
    ELSE '' END AS ApplyToCatagory,  
    CASE WHEN swm.ApplyTo=0 THEN 'ALL' ELSE CASE WHEN swm.ApplyToCatagoryNo=1 THEN mn.ManufacturerName  
           WHEN swm.ApplyToCatagoryNo=2 THEN pd.ProductName  
     WHEN swm.ApplyToCatagoryNo=3 THEN cm.CompanyName ELSE '' END END  
     AS [Applyto],  
    swm.UpdatedBy,  
    swm.DLUP,  
    swm.InActive  
    FROM    dbo.tbSystemWarningMessage swm    
 INNER JOIN dbo.tbWarningMessage wm ON swm.WarningNo=wm.WarningId  
  LEFT JOIN dbo.tbManufacturer mn ON swm.ApplyTo=mn.ManufacturerId  
  LEFT JOIN dbo.tbProduct pd ON swm.ApplyTo=pd.ProductId  
  LEFT JOIN dbo.tbCompany cm ON swm.ApplyTo=cm.CompanyId  
    WHERE    SystemWarningMessageId= @SystemWarningMessageNo   
  
  
  