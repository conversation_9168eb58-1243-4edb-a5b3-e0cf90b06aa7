<%--
Marker     changed by      date         Remarks
[001]      <PERSON><PERSON>   15/03/1989   Records Processed and Remaining
--%>
<%@ Control Language="C#" CodeBehind="ClientImportBOMMainInfo.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Nuggets.ClientImportBOMMainInfo" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Nugget:DesignBase ID="ctlDB" runat="server" BoxType="Standard">
    <Links>
        <ReboundUI:IconButton ID="ibtnEdit" runat="server" IconButtonMode="hyperlink" IconGroup="Nugget"
            IconTitleResource="Edit" IconCSSType="Edit" />
     
            <ReboundUI:IconButton ID="ibtnExportPurchaseHUB" runat="server" IconButtonMode="hyperlink"  IconGroup="Nugget" ToolTip="Save as HUBRFQ"  IconTitleResource="SaveasHUBRFQ"  /> 
        <ReboundUI:IconButton ID="ibtnComplete" runat="server" IconButtonMode="hyperlink" IconGroup="Nugget" IconTitleResource="MarkComplete" IconCSSType="Release"  />
         </Links>
    <Content>
        <table class="threeCols">
            <tr>
                <td class="col1">
                    <table cellpadding="0" cellspacing="0" border="0" class="dataItems">
                        <ReboundUI:DataItemRow ID="ctlCode" runat="server" ResourceTitle="BOMCode" />
                        <ReboundUI:DataItemRow ID="ctlName" runat="server" ResourceTitle="BOMName" />
                        <ReboundUI:DataItemRow id="ctlNotes" runat="server" ResourceTitle="Notes" />
                        <ReboundUI:DataItemRow ID="ctlRecordsRemain" runat="server" ResourceTitle="RecordsRemaining" />   <%--[001]  --%>
                        <ReboundUI:DataItemRow ID="ctlRecordsProc" runat="server"  ResourceTitle="RecordsProcessed" /> <%--[001]  --%>
                        <ReboundUI:DataItemRow id="hidCurrency" runat="server" FieldType="Hidden" />
                        <ReboundUI:DataItemRow id="hidCompanyNo" runat="server" FieldType="Hidden" />
						<ReboundUI:DataItemRow id="hidContactNo" runat="server" FieldType="Hidden" /> 
						<ReboundUI:DataItemRow id="hidDisplayStatus" runat="server" FieldType="Hidden"/>
                        <ReboundUI:DataItemRow id="hidContact2No" runat="server" FieldType="Hidden" /> 
                    </table>
                </td>
                <td class="col1">
                    <table cellpadding="0" cellspacing="0" border="0" class="dataItems"> 
                         <ReboundUI:DataItemRow id="ctlCompanyName" runat="server" ResourceTitle="CompanyName"  /> 
                         <ReboundUI:DataItemRow id="ctlContactName" runat="server" ResourceTitle="ContactName"  /> 
                         <ReboundUI:DataItemRow id="ctlCurrencyCode" runat="server" ResourceTitle="Currency"  /> 
                         <ReboundUI:DataItemRow id="ctlHUBRFQName" runat="server" ResourceTitle="HUBRFQName"  /> 
                    </table>
                </td>  
                 <td class="col2">
                    <table cellpadding="0" cellspacing="0" border="0" class="dataItems">  
                         <ReboundUI:DataItemRow id="hidSalesmanId" runat="server" FieldType="Hidden" /> 
                         <ReboundUI:DataItemRow id="ctlSalespersion" runat="server" ResourceTitle="Salesman" /> 
                         <ReboundUI:DataItemRow ID="ctlInActive" runat="server" FieldType="CheckBox" ResourceTitle="IsInactive" />
                        <ReboundUI:DataItemRow ID="ctlMarkComplete" runat="server" FieldType="CheckBox" ResourceTitle="MarkComplete" />
                        
                         </table>
                </td>                 
            </tr>
        </table>
    </Content>
    <Forms>
        <ReboundForm:ClientImportBOMMainInfo_Edit ID="ctlEdit" runat="server" />
        <ReboundForm:ClientBomConfirm ID="ctlConfirm" runat="server" />
       <ReboundForm:SaveAsHUBRFQ ID="cltSaveAsHUBRFQ" runat="server" />
    </Forms>
</ReboundUI_Nugget:DesignBase>
