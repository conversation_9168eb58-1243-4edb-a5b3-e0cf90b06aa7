Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.ClientBOMItems_AddToHUBRFQ=function(n){Rebound.GlobalTrader.Site.Controls.Forms.ClientBOMItems_AddToHUBRFQ.initializeBase(this,[n]);this._intLineID=0;this._intRequirementLineID=0;this._strCompanyName="";this._intBOMID=0;this._intClientCompanyId=-1;this._CustReqNo=-1};Rebound.GlobalTrader.Site.Controls.Forms.ClientBOMItems_AddToHUBRFQ.prototype={get_intBOMID:function(){return this._intBOMID},set_intBOMID:function(n){this._intBOMID!==n&&(this._intBOMID=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Forms.ClientBOMItems_AddToHUBRFQ.callBaseMethod(this,"initialize");this.addShown(Function.createDelegate(this,this.formShown))},dispose:function(){this.isDisposed||(this._trSourceFromRequirement=null,Rebound.GlobalTrader.Site.Controls.Forms.ClientBOMItems_AddToHUBRFQ.callBaseMethod(this,"dispose"))},formShown:function(){this._blnFirstTimeShown&&(this.addSave(Function.createDelegate(this,this.saveAdd)),this._strPathToData="controls/Nuggets/ClientBOMItems",this._strDataObject="ClientBOMItems");this.getFieldControl("ctlBOMHeader")._intCompanyID=this._intClientCompanyId;this.getFieldDropDownData("ctlBOMHeader")},loadDropDowns:function(){this.getFieldDropDownData("ctlROHS")},selectRequirementItem:function(){this.continueClicked()},validateForm:function(){this.onValidate();var n=!0;return this.getFieldValue("ctlBOMHeader")==null&&(n=!1,this.showError(!0,"Please select HUBRFQ for this Requirement.")),n||this.showError(!0),n},saveAdd:function(){if(this.validateForm()){var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData(this._strPathToData);n.set_DataObject(this._strDataObject);n.set_DataAction("UpdateCustomerRequirementWithHUBRFQ");n.addParameter("BomId",this.getFieldValue("ctlBOMHeader"));n.addParameter("CompanyNo",this._intClientCompanyId);n.addParameter("CustomerRequirementNumber",this._CustReqNo);n.addDataOK(Function.createDelegate(this,this.saveAddOK));n.addError(Function.createDelegate(this,this.saveAddError));n.addTimeout(Function.createDelegate(this,this.saveAddError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null}},saveAddError:function(n){this._strErrorMessage=n._errorMessage;this.onSaveError()},saveAddOK:function(n){n._result.Result==!0?(this._intLineID=n._result.NewID,this.onSaveComplete()):this.saveEditError(n)}};Rebound.GlobalTrader.Site.Controls.Forms.ClientBOMItems_AddToHUBRFQ.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.ClientBOMItems_AddToHUBRFQ",Rebound.GlobalTrader.Site.Controls.Forms.Base,Sys.IDisposable);