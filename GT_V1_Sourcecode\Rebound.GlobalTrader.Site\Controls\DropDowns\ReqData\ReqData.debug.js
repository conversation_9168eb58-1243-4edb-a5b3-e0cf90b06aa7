///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//
// RP 06.10.2009:
// - Changes due to changes in base class
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");

Rebound.GlobalTrader.Site.Controls.DropDowns.ReqData = function(element) { 
	Rebound.GlobalTrader.Site.Controls.DropDowns.ReqData.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.DropDowns.ReqData.prototype = {
get_SType: function() { return this._SType; }, set_SType: function(v) { if (this._SType !== v) this._SType = v; },

	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.DropDowns.ReqData.callBaseMethod(this, "initialize");
		this.addSetupDataCall(Function.createDelegate(this, this.setupDataCall));
		this.addGetDataOK(Function.createDelegate(this, this.dataCallOK));
	},
	
	dispose: function() { 
		if (this.isDisposed) return;
		Rebound.GlobalTrader.Site.Controls.DropDowns.ReqData.callBaseMethod(this, "dispose");
	},
	
	setupDataCall: function() { 
		this._objData.set_PathToData("controls/DropDowns/ReqData");
		this._objData.set_DataObject("ReqData");
		this._objData.set_DataAction("GetData");
		this._objData.addParameter("SType", this._SType);
	},
	
	dataCallOK: function() { 
		var result = this._objData._result;
		if (result.Types) {
			for (var i = 0; i < result.Types.length; i++) {
				this.addOption(result.Types[i].Name, result.Types[i].ID);
			}
		}
	}

};

Rebound.GlobalTrader.Site.Controls.DropDowns.ReqData.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.ReqData", Rebound.GlobalTrader.Site.Controls.DropDowns.Base);
