using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using System.Collections;

namespace Rebound.GlobalTrader.Site.Controls {
	[DefaultProperty("")]
	[ToolboxData("<{0}:MultiStep runat=server></{0}:MultiStep>")]
	public class MultiStep : WebControlWithScript, IScriptControl, INamingContainer {

		#region Locals

		private Panel _pnlMultiStep;
		private List<MultiStepItem> _lstItems = new List<MultiStepItem>();
		private List<string> _lstItemIDs = new List<string>();
		private List<string> _lstItemLinkIDs = new List<string>();
		private List<TableCell> _lstSeparators = new List<TableCell>();
		private List<string> _lstSeparatorIDs = new List<string>();
		private List<string> _lstRelatedContentFormIDs = new List<string>();
		private List<string> _lstExplainLabelIDs = new List<string>();

		#endregion

		#region Properties

		/// <summary>
		/// Tabs container
		/// </summary>
		private ITemplate _tmpItems = null;
		[PersistenceMode(PersistenceMode.InnerProperty)]
		[TemplateContainer(typeof(Container))]
		public ITemplate Items {
			get { return _tmpItems; }
			set { _tmpItems = value; }
		}

		/// <summary>
		/// index of selected item
		/// </summary>
		private int _intCurrentStep;
		public int CurrentStep {
			get { return _intCurrentStep; }
			set { _intCurrentStep = value; }
		}

		/// <summary>
		/// Pattern for finding form table objects
		/// </summary>
		private string _strMultiStepFormNamePattern = "frmStep{0}";
		public string MultiStepFormNamePattern {
			get { return _strMultiStepFormNamePattern; }
			set { _strMultiStepFormNamePattern = value; }
		}

		/// <summary>
		/// Related Form object
		/// </summary>
		private Forms.DesignBase _ctlRelatedForm;
		public Forms.DesignBase RelatedForm {
			get { return _ctlRelatedForm; }
			set { _ctlRelatedForm = value; }
		}

		#endregion

		#region Overrides

		/// <summary>
		/// OnLoad
		/// </summary>
		/// <param name="e"></param>
		protected override void OnLoad(EventArgs e) {
			((Pages.Base)Page).AddCSSFile("MultiStep.css");
			EnsureChildControls();
			base.OnLoad(e);
		}

		/// <summary>
		/// create controls
		/// </summary>
		protected override void CreateChildControls() {
			_pnlMultiStep = ControlBuilders.CreatePanelInsideParent(this, "multistep");
			_pnlMultiStep.ID = "pnlMultiStep";

			//items container
			if (_tmpItems != null) {
				Container cnt = new Container();
				_tmpItems.InstantiateIn(cnt);
				int intStepNo = 0;
				Table tbl = ControlBuilders.CreateTableInsideParent(_pnlMultiStep);
				TableRow tr = new TableRow();
				tbl.Rows.Add(tr);
				for (int i = 0; i < cnt.Controls.Count; i++) {
					Control ctl = cnt.Controls[i];
					if (ctl is MultiStepItem) {
						intStepNo += 1;
						MultiStepItem ms = (MultiStepItem)ctl;
						ms.ParentMultiStep = this;
						ms.StepNumber = intStepNo;
						if (ms.IsSelected) _intCurrentStep = intStepNo;
						TableCell tdSep = new TableCell();
						tdSep.ID = String.Format("tdSep{0}", intStepNo);
						ControlBuilders.CreateImageInsideParent(tdSep, "", "~/images/x.gif", 10, 19);
						tr.Controls.Add(ms);
						tr.Controls.Add(tdSep);
						ms.MakeChildControls();
						_lstItemLinkIDs.Add(ms.LinkID);
						_lstSeparators.Add(tdSep);
						_lstItemIDs.Add(ms.ClientID);
						_lstItems.Add(ms);
					}
				}

				//Explanations
				Panel pnlExplain = ControlBuilders.CreatePanelInsideParent(this);
				for (int i = 0; i < _lstItems.Count; i++) {
					Label lbl = ControlBuilders.CreateLabelInsideParent(pnlExplain, "invisible", Functions.GetGlobalResource("FormExplanations", _lstItems[i].ResourceTitle));
					_lstExplainLabelIDs.Add(lbl.ClientID);
				}
				cnt.Dispose(); cnt = null;
			}
			base.CreateChildControls();
		}

		/// <summary>
		/// Pre render
		/// </summary>
		protected override void OnPreRender(EventArgs e) {
			///sort out css classes for everything
			for (int i = 1; i <= _lstItemLinkIDs.Count; i++) {
				TableCell tdSep = _lstSeparators[i - 1];
				tdSep.CssClass = string.Format("sep sep_{0}_{1}", CurrentStepTimeString(i), NextStepTimeString(i));
				_lstSeparatorIDs.Add(tdSep.ClientID);
				MultiStepItem ms = _lstItems[i - 1];
				ms.RelatedContentForm = (Tables.Form)_ctlRelatedForm.FindContentControl(String.Format(_strMultiStepFormNamePattern, i));
				if (ms.RelatedContentForm != null) {
					Functions.SetCSSVisibility(ms.RelatedContentForm, ms.IsSelected);
					_lstRelatedContentFormIDs.Add(ms.RelatedContentForm.ClientID);
				}
			}
			base.OnPreRender(e);
		}

		#endregion

		/// <summary>
		/// Get string of current step time - Current, Future or Past
		/// </summary>
		internal string CurrentStepTimeString(int i) {
			string strOut = "Future";
			if (i == _intCurrentStep) strOut = "Current";
			if (i < _intCurrentStep) strOut = "Past";
			return strOut;
		}

		/// <summary>
		/// Get string of next step time - Current, Future or Past
		/// </summary>
		internal string NextStepTimeString(int i) {
			i += 1;
			return (i > _lstItemLinkIDs.Count) ? "End" : CurrentStepTimeString(i);
		}

		/// <summary>
		/// Allows EnsureChildControls to be called from outside
		/// </summary>
		internal void CreateControls() {
			EnsureChildControls();
		}

		/// <summary>
		/// Finds an item control within this control
		/// </summary>
		internal MultiStepItem FindItem(string strID) {
			return (MultiStepItem)Functions.FindControlRecursive(_pnlMultiStep, strID);
		}

		#region IScriptControl Members

		protected new virtual IEnumerable<ScriptReference> GetScriptReferences() {
			bool blnDebug = false;
#if (DEBUG)
			blnDebug = true;
#endif
			return new ScriptReference[] { Functions.GetScriptReference(blnDebug, "Rebound.GlobalTrader.Site", "Controls.Basic.MultiStep.MultiStep", true) };
		}

		protected new virtual IEnumerable<ScriptDescriptor> GetScriptDescriptors() {
			ScriptControlDescriptor descriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.MultiStep", this.ClientID);
			descriptor.AddProperty("aryItemIDs", _lstItemIDs);
			descriptor.AddProperty("aryItemLinkIDs", _lstItemLinkIDs);
			descriptor.AddProperty("arySeparatorIDs", _lstSeparatorIDs);
			descriptor.AddProperty("aryContentFormIDs", _lstRelatedContentFormIDs);
			descriptor.AddProperty("aryExplainLabelIDs", _lstExplainLabelIDs);
			descriptor.AddProperty("intCurrentStep", _intCurrentStep);
			descriptor.AddElementProperty("pnlMultiStep", _pnlMultiStep.ClientID);
			return new ScriptDescriptor[] { descriptor };
		}

		IEnumerable<ScriptReference> IScriptControl.GetScriptReferences() {
			return GetScriptReferences();
		}

		IEnumerable<ScriptDescriptor> IScriptControl.GetScriptDescriptors() {
			return GetScriptDescriptors();
		}

		#endregion
	}
}