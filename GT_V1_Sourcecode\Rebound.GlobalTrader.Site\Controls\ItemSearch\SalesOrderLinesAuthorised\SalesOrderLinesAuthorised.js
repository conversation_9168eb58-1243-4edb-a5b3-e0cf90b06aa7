Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.ItemSearch");Rebound.GlobalTrader.Site.Controls.ItemSearch.SalesOrderLinesAuthorised=function(n){Rebound.GlobalTrader.Site.Controls.ItemSearch.SalesOrderLinesAuthorised.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.ItemSearch.SalesOrderLinesAuthorised.prototype={initialize:function(){Rebound.GlobalTrader.Site.Controls.ItemSearch.SalesOrderLinesAuthorised.callBaseMethod(this,"initialize");this.addSetupData(Function.createDelegate(this,this.doSetupData));this.addGetDataComplete(Function.createDelegate(this,this.doGetDataComplete))},dispose:function(){this.isDisposed||Rebound.GlobalTrader.Site.Controls.ItemSearch.SalesOrderLinesAuthorised.callBaseMethod(this,"dispose")},doSetupData:function(){this._objData.set_PathToData("controls/ItemSearch/SalesOrderLinesAuthorised");this._objData.set_DataObject("SalesOrderLinesAuthorised");this._objData.set_DataAction("GetData");this._objData.addParameter("Part",this.getFieldValue("ctlPartNo"));this._objData.addParameter("CMName",this.getFieldValue("ctlCompany"));this._objData.addParameter("IncludeClosed",this.getFieldValue("ctlIncludeClosed"));this._objData.addParameter("CustomerPO",this.getFieldValue("ctlCustomerPO"));this._objData.addParameter("SalesOrderNo",this.getFieldValue("ctlSalesOrderNo"));this._objData.addParameter("DateOrderedFrom",this.getFieldValue("ctlDateOrderedFrom"));this._objData.addParameter("DateOrderedTo",this.getFieldValue("ctlDateOrderedTo"));this._objData.addParameter("DatePromisedFrom",this.getFieldValue("ctlDatePromisedFrom"));this._objData.addParameter("DatePromisedTo",this.getFieldValue("ctlDatePromisedTo"));this._objData.addParameter("Salesman",this.getFieldValue("ctlSalesman"));var n=window.location.href,t=new URL(n),i=t.searchParams.get("inv");this._objData.addParameter("InvoiceNo",i)},doGetDataComplete:function(){for(var n,i,t=0,r=this._objResult.Results.length;t<r;t++)n=this._objResult.Results[t],i=[n.No,$R_FN.setCleanTextValue(n.CMName),$R_FN.writePartNo(n.Part,n.ROHS),$R_FN.setCleanTextValue(n.Date),n.Price,n.Quantity,$R_FN.setCleanTextValue(n.Salesman),$R_FN.setCleanTextValue(n.CustomerPO),n.Cost,],this._tblResults.addRow(i,n.ID,!1),i=null,n=null},GetParameterValues:function(n){for(var i,r=window.location.href.slice(window.location.href.indexOf("?")+1).split("&"),t=0;t<r.length;t++)if(i=r[t].split("="),i[0]==n)return i[1]}};Rebound.GlobalTrader.Site.Controls.ItemSearch.SalesOrderLinesAuthorised.registerClass("Rebound.GlobalTrader.Site.Controls.ItemSearch.SalesOrderLinesAuthorised",Rebound.GlobalTrader.Site.Controls.ItemSearch.Base,Sys.IDisposable);