using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using System.Collections;

namespace Rebound.GlobalTrader.Site.Controls.AutoSearch {
	[DefaultProperty("")]
	[ToolboxData("<{0}:PartECCNSearch runat=server></{0}:PartECCNSearch>")]
	public class PartECCNSearch : Base {
		#region Properties
		#endregion

		#region Overrides
		/// <summary>
		/// OnInit
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
			base.OnInit(e);
			AddScriptReference("Controls.AutoSearch.PartECCNSearch.PartECCNSearch.js");
			SetAutoSearchType("PartECCNSearch");
		}

		protected override void OnLoad(EventArgs e) {
			CharactersToEnterBeforeSearch = 2;
            //IncludeInactive = false;
			base.OnLoad(e);
		}

		/// <summary>
		/// OnPreRender
		/// </summary>
		/// <param name="e"></param>
		protected override void OnPreRender(EventArgs e) {
			SetupScriptDescriptors();
			base.OnPreRender(e);
		}
		#endregion

		/// <summary>
		/// SetupScriptDescriptors
		/// </summary>
		private void SetupScriptDescriptors() {
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.AutoSearch.PartECCNSearch", ClientID);
            //_scScriptControlDescriptor.AddProperty("ShowInactive", IncludeInactive);
		}
	}
}