<%@ Control Language="C#" CodeBehind="ContactExtendedInfo.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Nuggets.ContactExtendedInfo" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Nugget:DesignBase ID="ctlDB" runat="server" BoxType="Standard">
	<Links>
		<ReboundUI:IconButton ID="ibtnEdit" runat="server" IconGroup="Nugget" IconTitleResource="Edit" IconCSSType="Edit" IconButtonMode="Hyperlink" Href="javascript:void(0);" />
	</Links>
	
	<Content>
		<table class="twoCols">
			<tr>
				<td class="col1">
					<table cellpadding="0" cellspacing="0" border="0" class="dataItems">
						<ReboundUI:DataItemRow id="ctlGender" runat="server" ResourceTitle="Gender" />
						<ReboundUI:DataItemRow id="hidGenderID" runat="server" FieldType="Hidden" />
						<ReboundUI:DataItemRow id="ctlBirthday" runat="server" ResourceTitle="Birthday" />
						<ReboundUI:DataItemRow id="ctlMaritalStatus" runat="server" ResourceTitle="MaritalStatus" />
						<ReboundUI:DataItemRow id="hidMaritalStatusID" runat="server" FieldType="Hidden" />
						<ReboundUI:DataItemRow id="ctlPartner" runat="server" ResourceTitle="Partner" />
						<ReboundUI:DataItemRow id="ctlPartnerBirthday" runat="server" ResourceTitle="PartnerBirthday" />
						<ReboundUI:DataItemRow id="ctlAnniversary" runat="server" ResourceTitle="Anniversary" />
						<ReboundUI:DataItemRow id="ctlNumberChildren" runat="server" ResourceTitle="NumberChildren" />
						<ReboundUI:DataItemRow id="ctlChild1" runat="server" ResourceTitle="Child1" />
						<ReboundUI:DataItemRow id="hidChild1Name" runat="server" FieldType="Hidden" />
						<ReboundUI:DataItemRow id="hidChild1Sex" runat="server" FieldType="Hidden" />
						<ReboundUI:DataItemRow id="hidChild1Birthday" runat="server" FieldType="Hidden" />
						<ReboundUI:DataItemRow id="ctlChild2" runat="server" ResourceTitle="Child2" />
						<ReboundUI:DataItemRow id="hidChild2Name" runat="server" FieldType="Hidden" />
						<ReboundUI:DataItemRow id="hidChild2Sex" runat="server" FieldType="Hidden" />
						<ReboundUI:DataItemRow id="hidChild2Birthday" runat="server" FieldType="Hidden" />
						<ReboundUI:DataItemRow id="ctlChild3" runat="server" ResourceTitle="Child3" />
						<ReboundUI:DataItemRow id="hidChild3Name" runat="server" FieldType="Hidden" />
						<ReboundUI:DataItemRow id="hidChild3Sex" runat="server" FieldType="Hidden" />
						<ReboundUI:DataItemRow id="hidChild3Birthday" runat="server" FieldType="Hidden" />
					</table>
				</td>
				<td class="col2">
					<table cellpadding="0" cellspacing="0" border="0" class="dataItems">
						<ReboundUI:DataItemRow id="ctlMobileTel" runat="server" ResourceTitle="MobileTel" />
						<ReboundUI:DataItemRow id="ctlFavouriteSport" runat="server" ResourceTitle="FavouriteSport" />
						<ReboundUI:DataItemRow id="ctlFavouriteTeam" runat="server" ResourceTitle="FavouriteTeam" />
						<ReboundUI:DataItemRow id="ctlHobbies" runat="server" ResourceTitle="Hobbies" />
						<ReboundUI:DataItemRow id="ctlNotes" runat="server" ResourceTitle="Notes" />
					</table>
				</td>
			</tr>
		</table>
	</Content>
	
	<Forms>
		<ReboundForm:ContactExtendedInfo_Edit ID="ctlEdit" runat="server" />
	</Forms>
	
</ReboundUI_Nugget:DesignBase>
