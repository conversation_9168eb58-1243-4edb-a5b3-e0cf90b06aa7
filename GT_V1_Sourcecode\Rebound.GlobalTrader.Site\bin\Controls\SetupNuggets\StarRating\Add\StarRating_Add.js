Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.StarRating_Add=function(n){Rebound.GlobalTrader.Site.Controls.Forms.StarRating_Add.initializeBase(this,[n]);this._intItemID=-1};Rebound.GlobalTrader.Site.Controls.Forms.StarRating_Add.prototype={initialize:function(){Rebound.GlobalTrader.Site.Controls.Forms.StarRating_Add.callBaseMethod(this,"initialize");this.addShown(Function.createDelegate(this,this.formShown))},dispose:function(){this.isDisposed||(this._intItemID=null,Rebound.GlobalTrader.Site.Controls.Forms.StarRating_Add.callBaseMethod(this,"dispose"))},formShown:function(){this._blnFirstTimeShown&&(this.addSave(Function.createDelegate(this,this.saveClicked)),this._strPathToData="controls/SetupNuggets/StarRating",this._strDataObject="StarRating");this.setFormFieldsToDefaults()},saveClicked:function(){this.resetFormFields();this.validateForm()&&this.saveEdit()},validateForm:function(){return this.autoValidateFields()},saveEdit:function(){if(this.validateForm())if(this.getFieldValue("ctlNumOfPO")==0)this.setFieldInError(null,!0,"Please input number greater than 0!",this.getField("ctlNumOfPO")),this.showError(!0);else{this.showError(!1);var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData(this._strPathToData);n.set_DataObject(this._strDataObject);n.set_DataAction("AddNew");n.addParameter("NumOfPO",this.getFieldValue("ctlNumOfPO"));n.addDataOK(Function.createDelegate(this,this.saveEditOK));n.addError(Function.createDelegate(this,this.saveEditError));n.addTimeout(Function.createDelegate(this,this.saveEditError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null}},saveEditOK:function(){this.onSaveComplete()},saveEditError:function(n){this._strErrorMessage=n._errorMessage;this.onSaveError()}};Rebound.GlobalTrader.Site.Controls.Forms.StarRating_Add.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.StarRating_Add",Rebound.GlobalTrader.Site.Controls.Forms.Base,Sys.IDisposable);