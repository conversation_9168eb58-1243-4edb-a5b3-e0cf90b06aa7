Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.ItemSearch");Rebound.GlobalTrader.Site.Controls.ItemSearch.PurchaseOrderLines=function(n){Rebound.GlobalTrader.Site.Controls.ItemSearch.PurchaseOrderLines.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.ItemSearch.PurchaseOrderLines.prototype={initialize:function(){Rebound.GlobalTrader.Site.Controls.ItemSearch.PurchaseOrderLines.callBaseMethod(this,"initialize");this.addSetupData(Function.createDelegate(this,this.doSetupData));this.addGetDataComplete(Function.createDelegate(this,this.doGetDataComplete))},dispose:function(){this.isDisposed||Rebound.GlobalTrader.Site.Controls.ItemSearch.PurchaseOrderLines.callBaseMethod(this,"dispose")},doSetupData:function(){this._objData.set_PathToData("controls/ItemSearch/PurchaseOrderLines");this._objData.set_DataObject("PurchaseOrderLines");this._objData.set_DataAction("GetData");this._objData.addParameter("Part",this.getFieldValue("ctlPartNo"));this._objData.addParameter("CMName",this.getFieldValue("ctlCompany"));this._objData.addParameter("IncludeClosed",this.getFieldValue("ctlIncludeClosed"));this._objData.addParameter("PurchaseOrderNoLo",this.getFieldValue_Min("ctlPurchaseOrderNo"));this._objData.addParameter("PurchaseOrderNoHi",this.getFieldValue_Max("ctlPurchaseOrderNo"));this._objData.addParameter("DateOrderedFrom",this.getFieldValue("ctlDateOrderedFrom"));this._objData.addParameter("DateOrderedTo",this.getFieldValue("ctlDateOrderedTo"));this._objData.addParameter("DateDeliveredFrom",this.getFieldValue("ctlDateDeliveredFrom"));this._objData.addParameter("DateDeliveredTo",this.getFieldValue("ctlDateDeliveredTo"))},doGetDataComplete:function(){for(var n,i,t=0,r=this._objResult.Results.length;t<r;t++)n=this._objResult.Results[t],i=[n.No,$R_FN.setCleanTextValue(n.CMName),$R_FN.writePartNo(n.Part,n.ROHS),$R_FN.setCleanTextValue(n.DateOrdered),n.Price,$R_FN.setCleanTextValue(n.DeliveryDate)],this._tblResults.addRow(i,n.ID,!1),i=null,n=null}};Rebound.GlobalTrader.Site.Controls.ItemSearch.PurchaseOrderLines.registerClass("Rebound.GlobalTrader.Site.Controls.ItemSearch.PurchaseOrderLines",Rebound.GlobalTrader.Site.Controls.ItemSearch.Base,Sys.IDisposable);