Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.ItemSearch");Rebound.GlobalTrader.Site.Controls.ItemSearch.Base=function(n){Rebound.GlobalTrader.Site.Controls.ItemSearch.Base.initializeBase(this,[n]);this._blnFirstCall=!0;this._blnIsSearching=!1};Rebound.GlobalTrader.Site.Controls.ItemSearch.Base.prototype={get_intItemSearchID:function(){return this._intItemSearchID},set_intItemSearchID:function(n){this._intItemSearchID!==n&&(this._intItemSearchID=n)},get_pnlLoading:function(){return this._pnlLoading},set_pnlLoading:function(n){this._pnlLoading!==n&&(this._pnlLoading=n)},get_pnlError:function(){return this._pnlError},set_pnlError:function(n){this._pnlError!==n&&(this._pnlError=n)},get_lblError:function(){return this._lblError},set_lblError:function(n){this._lblError!==n&&(this._lblError=n)},get_tblResults:function(){return this._tblResults},set_tblResults:function(n){this._tblResults!==n&&(this._tblResults=n)},get_pnlContent:function(){return this._pnlContent},set_pnlContent:function(n){this._pnlContent!==n&&(this._pnlContent=n)},get_pnlNoneFound:function(){return this._pnlNoneFound},set_pnlNoneFound:function(n){this._pnlNoneFound!==n&&(this._pnlNoneFound=n)},get_ctlPagingButtons:function(){return this._ctlPagingButtons},set_ctlPagingButtons:function(n){this._ctlPagingButtons!==n&&(this._ctlPagingButtons=n)},get_ibtnSearch:function(){return this._ibtnSearch},set_ibtnSearch:function(n){this._ibtnSearch!==n&&(this._ibtnSearch=n)},get_objFieldIDs:function(){return this._objFieldIDs},set_objFieldIDs:function(n){this._objFieldIDs!==n&&(this._objFieldIDs=n)},get_aryFieldIDs:function(){return this._aryFieldIDs},set_aryFieldIDs:function(n){this._aryFieldIDs!==n&&(this._aryFieldIDs=n)},get_enmInitialSortDirection:function(){return this._enmInitialSortDirection},set_enmInitialSortDirection:function(n){this._enmInitialSortDirection!==n&&(this._enmInitialSortDirection=n)},addSetupData:function(n){this.get_events().addHandler("SetupData",n)},removeSetupData:function(n){this.get_events().removeHandler("SetupData",n)},onSetupData:function(){var n=this.get_events().getHandler("SetupData");n&&n(this,Sys.EventArgs.Empty)},addGetData:function(n){this.get_events().addHandler("GetData",n)},removeGetData:function(n){this.get_events().removeHandler("GetData",n)},onGetData:function(){var n=this.get_events().getHandler("GetData");n&&n(this,Sys.EventArgs.Empty)},addGetDataComplete:function(n){this.get_events().addHandler("GetDataComplete",n)},removeGetDataComplete:function(n){this.get_events().removeHandler("GetDataComplete",n)},onGetDataComplete:function(){var n=this.get_events().getHandler("GetDataComplete");n&&n(this,Sys.EventArgs.Empty)},addSearched:function(n){this.get_events().addHandler("Searched",n)},removeSearched:function(n){this.get_events().removeHandler("Searched",n)},onSearched:function(){var n=this.get_events().getHandler("Searched");n&&n(this,Sys.EventArgs.Empty)},addItemSelected:function(n){this.get_events().addHandler("ItemSelected",n)},removeItemSelected:function(n){this.get_events().removeHandler("ItemSelected",n)},onItemSelected:function(){var n=this.get_events().getHandler("ItemSelected");n&&n(this,Sys.EventArgs.Empty)},initialize:function(){Rebound.GlobalTrader.Site.Controls.ItemSearch.Base.callBaseMethod(this,"initialize");$R_IBTN.addClick(this._ibtnSearch,Function.createDelegate(this,this.searchClicked));this._ctlPagingButtons&&(this._ctlPagingButtons.show(!0),this._ctlPagingButtons.addPageSizeClickEvent(Function.createDelegate(this,this.pageSizeClick)),this._ctlPagingButtons.addPageChangedEvent(Function.createDelegate(this,this.pageNumberChanged)));this._tblResults.addSortDataEvent(Function.createDelegate(this,this.getData));this._tblResults.addFilterDataEvent(Function.createDelegate(this,this.getData));this._tblResults.addSelectedIndexChanged(Function.createDelegate(this,this.tableItemClicked));this._tblResults.setInitialSortDirection(this._enmInitialSortDirection);this._tblResults._enmSortDirection=this._enmInitialSortDirection;this._blnFirstCall=null;this._blnIsSearching=null;this.setFilterFieldEnterPressedEvents();this.getDropDownsData()},dispose:function(){this.isDisposed||(this.get_element()&&$clearHandlers(this.get_element()),this._ibtnSearch&&$R_IBTN.clearHandlers(this._ibtnSearch),this._tblResults&&this._tblResults.dispose(),this._ctlPagingButtons&&this._ctlPagingButtons.dispose(),this._pnlLoading=null,this._pnlError=null,this._lblError=null,this._tblResults=null,this._pnlContent=null,this._pnlNoneFound=null,this._ctlPagingButtons=null,this._ibtnSearch=null,this._objFieldIDs=null,this._aryFieldIDs=null,Rebound.GlobalTrader.Site.Controls.ItemSearch.Base.callBaseMethod(this,"dispose"),this.isDisposed=!0)},searchClicked:function(n){this._blnIsSearching||(n||(n=null),this._intInitialID=n,this.resetToFirstPage(),this.getData())},getField:function(n){var t=eval("this._objFieldIDs."+n);return t||eval(String.format("FormFieldNotFound_{0}()",n)),$find(t)},setFieldValue:function(n,t){if(typeof t!="undefined"){var i=this.getField(n);i&&i.setValue(t);i=null}},getFieldValue:function(n){var t=this.getField(n);if(t)return t._blnOn?t.getValue():void 0},getFieldValue_Min:function(n){var t=this.getField(n);if(t)return t._blnOn?t.getMinValue():void 0},getFieldValue_Max:function(n){var t=this.getField(n);if(t)return t._blnOn?t.getMaxValue():void 0},showError:function(n,t){$R_FN.showElement(this._pnlError,n);n&&(t==""&&(t=$R_RES.DatabaseError),$R_FN.setInnerHTML(this._lblError,t),$R_FN.showElement(this._pnlLoading,!1),$R_FN.showElement(this._pnlContent,!1),$R_FN.showElement(this._pnlNoneFound,!1))},showLoading:function(n,t){$R_FN.showElement(this._pnlLoading,n);n&&($R_FN.showElement(this._pnlError,!1),t?$R_FN.showElement(this._pnlContent,!1):Sys.UI.DomElement.addCssClass(this._pnlContent,"itemSearchTableLoading"),$R_FN.showElement(this._pnlNoneFound,!1))},showResults:function(n){$R_FN.showElement(this._pnlContent,!0);Sys.UI.DomElement.removeCssClass(this._pnlContent,"itemSearchTableLoading");n&&($R_FN.showElement(this._pnlError,!1),$R_FN.showElement(this._pnlLoading,!1),$R_FN.showElement(this._pnlNoneFound,!1))},showNoneFound:function(n){$R_FN.showElement(this._pnlNoneFound,!0);n&&($R_FN.showElement(this._pnlContent,!1),$R_FN.showElement(this._pnlError,!1),$R_FN.showElement(this._pnlLoading,!1))},getData:function(){this.checkSomeFieldsEntered()&&(this.showLoading(!0,this._blnFirstCall),this._objData=new Rebound.GlobalTrader.Site.Data,this.onSetupData(),this.setIsSearching(!0),this._objData.addParameter("ISID",this._intItemSearchID),this._objData.addParameter("Order",this._tblResults._intSortColumnIndex+1),this._objData.addParameter("SortDir",this._tblResults._enmSortDirection),this._objData.addParameter("PageIndex",this._tblResults._intCurrentPage-1),this._objData.addParameter("PageSize",this._tblResults._intCurrentPageSize),this._objData.addDataOK(Function.createDelegate(this,this.getDataComplete)),this._objData.addError(Function.createDelegate(this,this.getDataError)),this._objData.addTimeout(Function.createDelegate(this,this.getDataError)),$R_DQ.addToQueue(this._objData),$R_DQ.processQueue(),this._objData=null,this.onGetData(),this._blnFirstCall=!1)},getDataComplete:function(n){this.setIsSearching(!1);this._objResult=n._result;this._objResult.Results.length>0?(this._tblResults.clearTable(),this._tblResults._intTotalRecords=this._objResult.Count,this._tblResults.calculatePages(),this._tblResults.calculateStartAndEndRow(),this.updatePaging(),this.onGetDataComplete(),this.showResults(!0),this._tblResults.resizeColumns()):this.showNoneFound(!0);this.onSearched()},getDataError:function(n){this.setIsSearching(!1);this.showError(!0,n.get_ErrorMessage())},checkSomeFieldsEntered:function(){for(var t,n=0,i=this._aryFieldIDs.length;n<i;n++){if(t=$find(this._aryFieldIDs[n]),t._blnOn)return t=null,!0;t=null}return!1},pageSizeClick:function(){this._blnIsSearching||this._ctlPagingButtons&&this._ctlPagingButtons._intCurrentPageSize!=this._tblResults._intCurrentPageSize&&(this._tblResults._intCurrentPage=1,this._ctlPagingButtons._intCurrentPage=1,this._tblResults._intCurrentPageSize=this._ctlPagingButtons._intCurrentPageSize,this.getData())},pageNumberChanged:function(){this._blnIsSearching||this._ctlPagingButtons&&this._ctlPagingButtons._intCurrentPage!=this._tblResults._intCurrentPage&&(this._tblResults.changePage(this._ctlPagingButtons._intCurrentPage),this.getData())},updatePaging:function(){this._ctlPagingButtons&&(this._ctlPagingButtons._intTotalResults=this._tblResults._intTotalRecords,this._ctlPagingButtons._intTotalPages=this._tblResults._intTotalPages,this._ctlPagingButtons._intCurrentPage=this._tblResults._intCurrentPage,this._ctlPagingButtons.updatePageDisplay())},tableItemClicked:function(){this._blnIsSearching||this.onItemSelected()},getSelectedID:function(){return this._tblResults._varSelectedValue},resetToFirstPage:function(){this._ctlPagingButtons&&(this._ctlPagingButtons._intCurrentPage=1);this._tblResults.changePage(1)},resizeColumns:function(){this._tblResults&&this._tblResults.resizeColumns()},getDropDownsData:function(){var t,i,n;for(this._intCheckDropDownTimer!=0&&clearTimeout(this._intCheckDropDownTimer),t=0,i=this._aryFieldIDs.length;t<i;t++)n=$find(this._aryFieldIDs[t]),n&&Object.getTypeName(n)=="Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.DropDown"&&(n.get_isInitialized()?n.getDropDownData():this._intCheckDropDownTimer=setTimeout(Function.createDelegate(this,this.getDropDownsData),5))},setIsSearching:function(n){this._blnIsSearching=n;this._ctlPagingButtons&&this._ctlPagingButtons.enable(!n);this._tblResults&&this._tblResults.enable(!n)},setFilterFieldEnterPressedEvents:function(){for(var t,n=0,i=this._aryFieldIDs.length;n<i;n++)t=$find(this._aryFieldIDs[n]),(Object.getTypeName(t)=="Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.TextBox"||Object.getTypeName(t)=="Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.Numerical")&&t.addEnterPressed(Function.createDelegate(this,this.searchClicked))}};Rebound.GlobalTrader.Site.Controls.ItemSearch.Base.registerClass("Rebound.GlobalTrader.Site.Controls.ItemSearch.Base",Sys.UI.Control,Sys.IDisposable);