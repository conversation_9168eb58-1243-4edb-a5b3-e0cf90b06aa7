
/****** Object:  StoredProcedure [dbo].[usp_GPDetail_MTD_GP_for_Client]    Script Date: 5/23/2024 2:25:37 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

ALTER PROCEDURE [dbo].[usp_GPDetail_MTD_GP_for_Client] 
--********************************************************************************************
--* SK 29.07.2010:
--* - Services are allocated by definition without existing on tbAllocation
--*   BackOrderQuantity - despite being on tbBackOrder - will therefore be zero
--* 
--* SK 26.04.2010:
--* - redo process for open orders to reflect reporting process elsewhere
--* - 
--* SK 22.04.2010:
--* - distinct count of orders to remove lines
--* - 
--* SK 12.04.2010:
--* - do not add shipping or freight into cost and resale for shipped sales as that is 
--*   performed in the report
--* - 
--* SK 06.04.2010:
--* - correct retrieval of duty for landed cost calculation 
--* - 
--* SK 04.11.2009:
--* - use Price from tbInvoiceLine rather than tbSalesOrderLine 
--*   which allows us to get the price of service items correctly
--* 
--* SK 15.07.2009:
--* - open orders do not allow for salesman2 at this time - and do not include shipping  
--* - 
--* SK 14.07.2009:
--* - recalculate LandedCost as at today's date  
--* - 
--* SK 25.06.2009:
--* - allow for currency date on SO  
--* - 
--* SK 17.06.2009:
--* - 
--* - include shipping in landed cost 
--* - ensure calculations for open(and shipped) as per reports
    --* SK 22.05.2024:
      --* - 
      --* - include credit 
      --* - add new shipped cost that includes credit
      --* - cuongdox
--********************************************************************************************
@ClientId	int

AS 

DECLARE @FromDate	datetime
DECLARE @ToDate		datetime

SET		@FromDate	= dateadd(mm, datediff(mm, 0, getdate()), 0) 
SET		@ToDate		= GetDate() 

CREATE	
TABLE	#tmpGP
(
 		SalesOrderNumber		int
,		OpenShippingCost		float
,		OpenFreightCharge		float
,		OpenLandedCost			float
,		OpenSalesValue			float
,		InvoiceNumber			int		
,		ShippedShippingCost		float
,		ShippedFreightCharge	float
,		ShippedLandedCost		float
,		ShippedSalesValue		float
)

CREATE	
TABLE	#tmpOpenGP
(
 		SalesOrderNumber		int
,		OpenShippingCost		float
,		OpenFreightCharge		float
,		LandedCost				float
,		BuyPrice				float
,		Allocation				int
,		PONumber				int
,		GILineNo				int
,		LandedCostExAllocation	float
,		OpenSalesValue			float
,		InStock					int   -- GA 03/12/2012
)

INSERT  
INTO	#tmpOpenGP
SELECT  SalesOrderNumber																													
,		ShippingCost
,		Freight 
,		LandedCost
,		BuyPriceInBase
,		QuantityAllocated  
,		PurchaseOrderNumber
,		GoodsInLineNo
,		LandedCost
,		GoodsSales								
,		QuantityInStock
FROM    dbo.tmpOpenOrders o
WHERE   dbo.ufn_get_date_from_datetime(DatePromised) BETWEEN @FromDate AND @ToDate
		AND	ClientNo				= @ClientId	

----if manual stock (i.e. PO does not exist) take landed cost from Allocation 
--UPDATE	#tmpOpenGP
--SET		LandedCost	= IsNull(LandedCostExAllocation,0)
--WHERE	InStock > 0								-- GA 03/12/2012


INSERT  
INTO	#tmpGP
SELECT  tmp.SalesOrderNumber																													
,		tmp.OpenShippingCost 
,		tmp.OpenFreightCharge
,		SUM(ISNULL(IsNull(tmp.LandedCost, tmp.BuyPrice) * tmp.Allocation,0))
,		SUM(ISNULL(tmp.OpenSalesValue,0))								
,		0
,		0
,		0
,		0
,		0
FROM    #tmpOpenGP tmp
Group BY SalesOrderNumber
		,OpenShippingCost
		,OpenFreightCharge

INSERT  
INTO	#tmpGP
SELECT	0
,		0
,		0
,		0
,		0
,		InvoiceNumber
,		ShippingCost
,		Freight 
,		Sum(GoodsCost)
,		Sum(GoodsSales)
FROM    dbo.tmpShippedOrders a
WHERE   a.InvoiceDate		BETWEEN @FromDate AND @ToDate
AND		a.ClientNo			= @ClientId
GROUP BY  a.InvoiceNumber
,		a.ShippingCost
,		a.Freight
,		a.CurrencyNo
,		a.InvoiceDate

DELETE
FROM	dbo.tbGPDetail
WHERE	ClientNo	= @ClientId
AND		Period		= 'MD'

INSERT
INTO	dbo.tbGPDetail
SELECT	@ClientId
,		null
,		null
,		null
,		'MD'  
,		isnull(sum(OpenShippingCost)	, 0)		
,		isnull(sum(OpenFreightCharge)	, 0)		
,		isnull(sum(OpenLandedCost)		, 0)			
,		isnull(sum(OpenSalesValue)		, 0)			
,		(SELECT count(DISTINCT SalesOrderNumber) from #tmpGP WHERE SalesOrderNumber > 0) 			
,		isnull(sum(ShippedShippingCost)	, 0)		
,		isnull(sum(ShippedFreightCharge), 0)	
,		IsNull(sum(ShippedLandedCost)	, 0)		
,		IsNull(sum(ShippedSalesValue)	, 0)		
,		(SELECT count(DISTINCT InvoiceNumber) from #tmpGP WHERE InvoiceNumber > 0) 	
,0
,0		
FROM    #tmpGP

DROP	TABLE	#tmpGP
DROP	TABLE	#tmpopenGP
