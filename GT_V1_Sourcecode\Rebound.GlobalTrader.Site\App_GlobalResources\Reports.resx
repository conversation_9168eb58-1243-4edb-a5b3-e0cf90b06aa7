﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="ActiveVendors" xml:space="preserve">
    <value>Active Vendors</value>
  </data>
  <data name="AirWayBill" xml:space="preserve">
    <value>Air Way Bill</value>
  </data>
  <data name="AllocatedCost" xml:space="preserve">
    <value>Allocated Cost</value>
  </data>
  <data name="AllocatedResale" xml:space="preserve">
    <value>Allocated Resale</value>
  </data>
  <data name="ApprovedCustomersOnStop" xml:space="preserve">
    <value>Approved Customers On Stop</value>
  </data>
  <data name="AuthorisedBy" xml:space="preserve">
    <value>Authorised By</value>
  </data>
  <data name="AutoEnteredSuppliers_Unedited" xml:space="preserve">
    <value>Auto-Entered Suppliers (unedited)</value>
  </data>
  <data name="Average" xml:space="preserve">
    <value>Average</value>
  </data>
  <data name="BackOrderQuantity" xml:space="preserve">
    <value>Back Order Quantity</value>
  </data>
  <data name="BackOrderValue" xml:space="preserve">
    <value>Back Order Value</value>
  </data>
  <data name="Balance" xml:space="preserve">
    <value>Balance</value>
  </data>
  <data name="BaseValue" xml:space="preserve">
    <value>Base Value</value>
  </data>
  <data name="BookedSales" xml:space="preserve">
    <value>Booked Sales</value>
  </data>
  <data name="Buyer" xml:space="preserve">
    <value>Buyer</value>
  </data>
  <data name="BuyInCost" xml:space="preserve">
    <value>Buy In Cost</value>
  </data>
  <data name="BuyInPrice" xml:space="preserve">
    <value>Buy In Price</value>
  </data>
  <data name="BuyInValue" xml:space="preserve">
    <value>Buy In Value</value>
  </data>
  <data name="City" xml:space="preserve">
    <value>City</value>
  </data>
  <data name="ClosedRequirements" xml:space="preserve">
    <value>Closed Requirements</value>
  </data>
  <data name="ClosedRequirementsReasons" xml:space="preserve">
    <value>Closed Requirements Reasons</value>
  </data>
  <data name="CommunicationLogActivityforaUser" xml:space="preserve">
    <value>Communication Log Activity for a User</value>
  </data>
  <data name="CompaniesApprovedToPurchaseFrom" xml:space="preserve">
    <value>Companies Approved To Purchase From</value>
  </data>
  <data name="CompaniesNotContacted" xml:space="preserve">
    <value>Companies Not Contacted</value>
  </data>
  <data name="Company" xml:space="preserve">
    <value>Company</value>
  </data>
  <data name="CompanyGrouping" xml:space="preserve">
    <value>Grouping?</value>
  </data>
  <data name="CompanyName" xml:space="preserve">
    <value>Company Name</value>
  </data>
  <data name="CompanyTax" xml:space="preserve">
    <value>Company Tax</value>
  </data>
  <data name="CompanyType" xml:space="preserve">
    <value>Company Type</value>
  </data>
  <data name="Confirmed" xml:space="preserve">
    <value>Confirmed</value>
  </data>
  <data name="Contact" xml:space="preserve">
    <value>Contact</value>
  </data>
  <data name="ContactEmailList" xml:space="preserve">
    <value>Contact Email List</value>
  </data>
  <data name="ContactFax" xml:space="preserve">
    <value>Contact Fax</value>
  </data>
  <data name="ContactName" xml:space="preserve">
    <value>Contact Name</value>
  </data>
  <data name="ContactsNotContacted" xml:space="preserve">
    <value>Contacts Not Contacted</value>
  </data>
  <data name="ContactTelephone" xml:space="preserve">
    <value>Contact Telephone</value>
  </data>
  <data name="ContactType" xml:space="preserve">
    <value>Contact Type</value>
  </data>
  <data name="Cost" xml:space="preserve">
    <value>Cost</value>
  </data>
  <data name="CostInBase" xml:space="preserve">
    <value>Cost In Base</value>
  </data>
  <data name="Count" xml:space="preserve">
    <value>Count</value>
  </data>
  <data name="Country" xml:space="preserve">
    <value>Country</value>
  </data>
  <data name="CountryOfImport" xml:space="preserve">
    <value>Country Of Import </value>
  </data>
  <data name="CountryOfManufacture" xml:space="preserve">
    <value>Country Of Manufacture</value>
  </data>
  <data name="County" xml:space="preserve">
    <value>County</value>
  </data>
  <data name="CreditDate" xml:space="preserve">
    <value>Credit Date</value>
  </data>
  <data name="CreditLimit" xml:space="preserve">
    <value>Credit Limit</value>
  </data>
  <data name="CreditNotes" xml:space="preserve">
    <value>Credit Notes</value>
  </data>
  <data name="CreditNotesforaCustomer" xml:space="preserve">
    <value>Credit Notes for a Customer</value>
  </data>
  <data name="CreditNotesforaSalesperson" xml:space="preserve">
    <value>Credit Notes for a Salesperson</value>
  </data>
  <data name="CreditNumber" xml:space="preserve">
    <value>Credit Number</value>
  </data>
  <data name="Currency" xml:space="preserve">
    <value>Currency</value>
  </data>
  <data name="CurrencyRate" xml:space="preserve">
    <value>Currency Rate</value>
  </data>
  <data name="CurrencyValue" xml:space="preserve">
    <value>Currency Value</value>
  </data>
  <data name="Customer" xml:space="preserve">
    <value>Customer</value>
  </data>
  <data name="CustomerCode" xml:space="preserve">
    <value>Customer Code</value>
  </data>
  <data name="CustomerListforSalesperson" xml:space="preserve">
    <value>Customer List for Salesperson</value>
  </data>
  <data name="CustomerOnTimeDeliveryReport" xml:space="preserve">
    <value>Customer On Time Delivery Report</value>
  </data>
  <data name="CustomerPart" xml:space="preserve">
    <value>Customer Part</value>
  </data>
  <data name="CustomerPO" xml:space="preserve">
    <value>Customer PO</value>
  </data>
  <data name="CustomerReturnValue" xml:space="preserve">
    <value>Customer Return Value</value>
  </data>
  <data name="CustomerRMA" xml:space="preserve">
    <value>Customer RMA</value>
  </data>
  <data name="CustomerRMADate" xml:space="preserve">
    <value>Customer RMA Date</value>
  </data>
  <data name="CustomerRMANumber" xml:space="preserve">
    <value>Customer RMA Number</value>
  </data>
  <data name="CustomerRMAs" xml:space="preserve">
    <value>Customer RMAs</value>
  </data>
  <data name="CustomerStatement" xml:space="preserve">
    <value>Customer Statement</value>
  </data>
  <data name="CutOffDate" xml:space="preserve">
    <value>Cut Off Date</value>
  </data>
  <data name="DailyCustomerRequirementsbySalesperson_Detailed" xml:space="preserve">
    <value>Daily Customer Requirements by Salesperson (Detailed)</value>
  </data>
  <data name="DailyCustomerRequirementsbySalesperson_Summary" xml:space="preserve">
    <value>Daily Customer Requirements by Salesperson (Summary)</value>
  </data>
  <data name="DailyCustomerRequirementsbySalesperson_Totals" xml:space="preserve">
    <value>Daily Customer Requirements by Salesperson (Totals)</value>
  </data>
  <data name="DailyImports" xml:space="preserve">
    <value>Daily Imports</value>
  </data>
  <data name="DailyImportsBySource" xml:space="preserve">
    <value>Daily Imports By Source</value>
  </data>
  <data name="DatabaseManagement" xml:space="preserve">
    <value>Database Management</value>
  </data>
  <data name="DateAndLogin" xml:space="preserve">
    <value>{0} by {1}</value>
  </data>
  <data name="DateCode" xml:space="preserve">
    <value>Date Code</value>
  </data>
  <data name="DateOrdered" xml:space="preserve">
    <value>Date Ordered</value>
  </data>
  <data name="DatePromised" xml:space="preserve">
    <value>Date Promised</value>
  </data>
  <data name="DateReceived" xml:space="preserve">
    <value>Date Received</value>
  </data>
  <data name="DaysSinceInvoice" xml:space="preserve">
    <value>Days Since Invoice</value>
  </data>
  <data name="DaysSinceLastContact" xml:space="preserve">
    <value>Days Since Last Contact</value>
  </data>
  <data name="DaysSinceLastInvoicebyContact" xml:space="preserve">
    <value>Days Since Last Invoice by Contact</value>
  </data>
  <data name="DaysSinceLastInvoicebyCustomer" xml:space="preserve">
    <value>Days Since Last Invoice by Customer</value>
  </data>
  <data name="DeliveryDate" xml:space="preserve">
    <value>Delivery Date</value>
  </data>
  <data name="Destination" xml:space="preserve">
    <value>Destination</value>
  </data>
  <data name="Division" xml:space="preserve">
    <value>Division</value>
  </data>
  <data name="DivisionName" xml:space="preserve">
    <value>Division Name</value>
  </data>
  <data name="Document" xml:space="preserve">
    <value>Document</value>
  </data>
  <data name="DueDate" xml:space="preserve">
    <value>Due Date</value>
  </data>
  <data name="Duration" xml:space="preserve">
    <value>Duration</value>
  </data>
  <data name="DutyCode" xml:space="preserve">
    <value>Duty Code</value>
  </data>
  <data name="EMail" xml:space="preserve">
    <value>EMail</value>
  </data>
  <data name="Employee" xml:space="preserve">
    <value>Employee</value>
  </data>
  <data name="EmployeeName" xml:space="preserve">
    <value>Employee Name</value>
  </data>
  <data name="EndDate" xml:space="preserve">
    <value>End Date</value>
  </data>
  <data name="EndTime" xml:space="preserve">
    <value>End Time</value>
  </data>
  <data name="ExcludeCustomersOnStop" xml:space="preserve">
    <value>Exclude Customers On Stop?</value>
  </data>
  <data name="FirstName" xml:space="preserve">
    <value>First Name</value>
  </data>
  <data name="Freight" xml:space="preserve">
    <value>Freight</value>
  </data>
  <data name="General" xml:space="preserve">
    <value>General</value>
  </data>
  <data name="GINumber" xml:space="preserve">
    <value>Goods In Number</value>
  </data>
  <data name="GoodsReceived" xml:space="preserve">
    <value>Goods Received</value>
  </data>
  <data name="GoodsReceivedNotInvoiced" xml:space="preserve">
    <value>Goods Received Not Invoiced</value>
  </data>
  <data name="GoodsReceivedShipmentDetails" xml:space="preserve">
    <value>Goods Received Shipment Details</value>
  </data>
  <data name="GoodsValuation" xml:space="preserve">
    <value>Goods Valuation</value>
  </data>
  <data name="GPRange" xml:space="preserve">
    <value>Gross Profit Range</value>
  </data>
  <data name="GrossProfit" xml:space="preserve">
    <value>Gross Profit</value>
  </data>
  <data name="GrossProfitBreakdown" xml:space="preserve">
    <value>Gross Profit Breakdown</value>
  </data>
  <data name="GrossProfitPercentage" xml:space="preserve">
    <value>Gross Profit %</value>
  </data>
  <data name="HarmonisedCode" xml:space="preserve">
    <value>Harmonised Code</value>
  </data>
  <data name="ImportDate" xml:space="preserve">
    <value>Import Date</value>
  </data>
  <data name="ImportName" xml:space="preserve">
    <value>Import Name</value>
  </data>
  <data name="InAdvance" xml:space="preserve">
    <value>In Advance</value>
  </data>
  <data name="IncludeCredits" xml:space="preserve">
    <value>Include Credits?</value>
  </data>
  <data name="IncludeLotsOnHold" xml:space="preserve">
    <value>Include Lots On Hold?</value>
  </data>
  <data name="IncludeNever" xml:space="preserve">
    <value>Include Never?</value>
  </data>
  <data name="IncludeOnOrder" xml:space="preserve">
    <value>Include On Order?</value>
  </data>
  <data name="IncludeShipping" xml:space="preserve">
    <value>Include Shipping?</value>
  </data>
  <data name="IncludeUnconfirmed" xml:space="preserve">
    <value>Include Unconfirmed?</value>
  </data>
  <data name="IncludeUnpaid" xml:space="preserve">
    <value>Include Unpaid?</value>
  </data>
  <data name="InStock" xml:space="preserve">
    <value>In Stock</value>
  </data>
  <data name="IntrastatReportforEECArrivals_CustomerRMAs" xml:space="preserve">
    <value>Intrastat Report for EEC Arrivals (Customer RMAs)</value>
  </data>
  <data name="IntrastatReportforEECArrivals_Purchases" xml:space="preserve">
    <value>Intrastat Report for EEC Arrivals (Purchases)</value>
  </data>
  <data name="IntrastatReportforEECDispatches_Sales" xml:space="preserve">
    <value>Intrastat Report for EEC Dispatches (Sales)</value>
  </data>
  <data name="IntrastatReportforEECDispatches_SupplierRMAs" xml:space="preserve">
    <value>Intrastat Report for EEC Dispatches (Supplier RMAs)</value>
  </data>
  <data name="InventoryLocationReport" xml:space="preserve">
    <value>Inventory Location Report</value>
  </data>
  <data name="InventoryLocationReportforLot" xml:space="preserve">
    <value>Inventory Location Report for Lot</value>
  </data>
  <data name="InvoiceCost" xml:space="preserve">
    <value>Invoice Cost</value>
  </data>
  <data name="InvoiceDate" xml:space="preserve">
    <value>Invoice Date</value>
  </data>
  <data name="InvoiceLineAllocationName" xml:space="preserve">
    <value>InvoiceLine Allocation Name</value>
  </data>
  <data name="InvoiceNumber" xml:space="preserve">
    <value>Invoice Number</value>
  </data>
  <data name="Invoices" xml:space="preserve">
    <value>Invoices</value>
  </data>
  <data name="InvoicesSortedbyInvoiceNumber" xml:space="preserve">
    <value>Invoices Sorted by Invoice Number</value>
  </data>
  <data name="InvoicesSortedbyInvoiceNumberforaCustomer" xml:space="preserve">
    <value>Invoices Sorted by Invoice Number for a Customer</value>
  </data>
  <data name="InvoicesSortedbyInvoiceNumberforaSalesperson" xml:space="preserve">
    <value>Invoices Sorted by Invoice Number for a Salesperson</value>
  </data>
  <data name="InvoiceValuation" xml:space="preserve">
    <value>Invoice Valuation</value>
  </data>
  <data name="InvoiceValue" xml:space="preserve">
    <value>Invoice Value</value>
  </data>
  <data name="Invoicing" xml:space="preserve">
    <value>Invoicing</value>
  </data>
  <data name="IPAddress" xml:space="preserve">
    <value>IP Address</value>
  </data>
  <data name="LandedCost" xml:space="preserve">
    <value>Landed Cost</value>
  </data>
  <data name="LastContactDate" xml:space="preserve">
    <value>Last Contact Date</value>
  </data>
  <data name="LastName" xml:space="preserve">
    <value>Last Name</value>
  </data>
  <data name="Late1DayPct" xml:space="preserve">
    <value>Late 1 Day %</value>
  </data>
  <data name="Late2To4DaysPct" xml:space="preserve">
    <value>Late 2 to 4 Days %</value>
  </data>
  <data name="Late5DaysOrMorePct" xml:space="preserve">
    <value>Late 5 Days Or More %</value>
  </data>
  <data name="LateOnly" xml:space="preserve">
    <value>Late Only</value>
  </data>
  <data name="Location" xml:space="preserve">
    <value>Location</value>
  </data>
  <data name="Login" xml:space="preserve">
    <value>Login</value>
  </data>
  <data name="LoginName" xml:space="preserve">
    <value>Login Name</value>
  </data>
  <data name="LoginStatistics" xml:space="preserve">
    <value>Login Statistics</value>
  </data>
  <data name="LoginStatisticsbyName" xml:space="preserve">
    <value>Login Statistics by Name</value>
  </data>
  <data name="Lot" xml:space="preserve">
    <value>Lot</value>
  </data>
  <data name="LotCode" xml:space="preserve">
    <value>Lot Code</value>
  </data>
  <data name="Management" xml:space="preserve">
    <value>Management</value>
  </data>
  <data name="Manufacturer" xml:space="preserve">
    <value>Manufacturer</value>
  </data>
  <data name="Margin" xml:space="preserve">
    <value>Margin</value>
  </data>
  <data name="Never" xml:space="preserve">
    <value />
    <comment>**BLANK**</comment>
  </data>
  <data name="NoOfAccounts" xml:space="preserve">
    <value>No of Accounts</value>
  </data>
  <data name="NoOfCredits" xml:space="preserve">
    <value>No of Credits</value>
  </data>
  <data name="NoOfCustomers" xml:space="preserve">
    <value>No of Customers</value>
  </data>
  <data name="NoOfGoodsIn" xml:space="preserve">
    <value>No of GoodsIn</value>
  </data>
  <data name="NoOfInvoices" xml:space="preserve">
    <value>No of Invoices</value>
  </data>
  <data name="NoOfItems" xml:space="preserve">
    <value>No of Items</value>
  </data>
  <data name="NoOfOffers" xml:space="preserve">
    <value>No of Offers</value>
  </data>
  <data name="NoOfOffersHistory" xml:space="preserve">
    <value>No of Offers History</value>
  </data>
  <data name="NoOfOrders" xml:space="preserve">
    <value>No of Orders</value>
  </data>
  <data name="NoOfRequirements" xml:space="preserve">
    <value>No of Requirements</value>
  </data>
  <data name="Notes" xml:space="preserve">
    <value>Notes</value>
  </data>
  <data name="NumberofAccountsbySalesperson" xml:space="preserve">
    <value>Number of Accounts by Salesperson</value>
  </data>
  <data name="NumberofOffersbyVendor" xml:space="preserve">
    <value>Number of Offers by Vendor</value>
  </data>
  <data name="NumberofOffersHistorybyVendor" xml:space="preserve">
    <value>Number of Offers History by Vendor</value>
  </data>
  <data name="NumberofRequirementsbyVendor" xml:space="preserve">
    <value>Number of Requirements by Vendor</value>
  </data>
  <data name="Offers" xml:space="preserve">
    <value>Offers</value>
  </data>
  <data name="OnStop" xml:space="preserve">
    <value>On Stop</value>
  </data>
  <data name="OnTimePct" xml:space="preserve">
    <value>On Time %</value>
  </data>
  <data name="OpenCustomerRMAs" xml:space="preserve">
    <value>Open Customer RMAs</value>
  </data>
  <data name="OpenCustomerRMAsforaCustomer" xml:space="preserve">
    <value>Open Customer RMAs for a Customer</value>
  </data>
  <data name="OpenCustomerRMAsforaCustomerwithReasons" xml:space="preserve">
    <value>Open Customer RMAs for a Customer with Reasons</value>
  </data>
  <data name="OpenCustomerRMAsforaSaleperson" xml:space="preserve">
    <value>Open Customer RMAs for a Saleperson</value>
  </data>
  <data name="OpenCustomerRMAsforaSalepersonwithReasons" xml:space="preserve">
    <value>Open Customer RMAs for a Saleperson with Reasons</value>
  </data>
  <data name="OpenCustomerRMAswithReasons" xml:space="preserve">
    <value>Open Customer RMAs with Reasons</value>
  </data>
  <data name="OpenPurchaseOrders" xml:space="preserve">
    <value>Open Purchase Orders</value>
  </data>
  <data name="OpenRequirementsbyCustomer" xml:space="preserve">
    <value>Open Requirements by Customer</value>
  </data>
  <data name="OpenRequirementsReportBySalesperson" xml:space="preserve">
    <value>Open Requirements Report By Salesperson</value>
  </data>
  <data name="OpenSales" xml:space="preserve">
    <value>Open Sales</value>
  </data>
  <data name="OpenSalesOrders" xml:space="preserve">
    <value>Open Sales Orders</value>
  </data>
  <data name="OpenSalesOrdersforSalesperson" xml:space="preserve">
    <value>Open Sales Orders for Salesperson</value>
  </data>
  <data name="OpenSupplierRMAs" xml:space="preserve">
    <value>Open Supplier RMAs</value>
  </data>
  <data name="OpenSupplierRMAsforaBuyer" xml:space="preserve">
    <value>Open Supplier RMAs for a Buyer</value>
  </data>
  <data name="OpenSupplierRMAsforaBuyerwithReasons" xml:space="preserve">
    <value>Open Supplier RMAs for a Buyer with Reasons</value>
  </data>
  <data name="OpenSupplierRMAsforaSupplier" xml:space="preserve">
    <value>Open Supplier RMAs for a Supplier</value>
  </data>
  <data name="OpenSupplierRMAsforaSupplierwithReasons" xml:space="preserve">
    <value>Open Supplier RMAs for a Supplier with Reasons</value>
  </data>
  <data name="OpenSupplierRMAswithReasons" xml:space="preserve">
    <value>Open Supplier RMAs with Reasons</value>
  </data>
  <data name="OrdersPercentage" xml:space="preserve">
    <value>Orders %</value>
  </data>
  <data name="OrdersToBeShipped" xml:space="preserve">
    <value>Orders To Be Shipped</value>
  </data>
  <data name="OrdersToBeShippedBySalesperson" xml:space="preserve">
    <value>Orders To Be Shipped By Salesperson</value>
  </data>
  <data name="OustandingCustomerInvoices" xml:space="preserve">
    <value>Oustanding Customer Invoices</value>
  </data>
  <data name="Package" xml:space="preserve">
    <value>Package</value>
  </data>
  <data name="PackageUnit" xml:space="preserve">
    <value>Package Unit</value>
  </data>
  <data name="Paid" xml:space="preserve">
    <value>Paid</value>
  </data>
  <data name="Part" xml:space="preserve">
    <value>Part</value>
  </data>
  <data name="PartOrdered" xml:space="preserve">
    <value>Part Ordered</value>
  </data>
  <data name="PhysicalCount" xml:space="preserve">
    <value>Physical Count</value>
  </data>
  <data name="PickSheetSalesOrdersBasic" xml:space="preserve">
    <value>Pick Sheet - Sales Orders Basic</value>
  </data>
  <data name="PickSheetSalesOrdersDetailed" xml:space="preserve">
    <value>Pick Sheet - Sales Orders Detailed</value>
  </data>
  <data name="PickSheetSupplierRMAs" xml:space="preserve">
    <value>Pick Sheet - Supplier RMAs</value>
  </data>
  <data name="PickUp" xml:space="preserve">
    <value>Pick Up Time</value>
  </data>
  <data name="POApproved" xml:space="preserve">
    <value>PO Approved</value>
  </data>
  <data name="POConfirmed" xml:space="preserve">
    <value>Confirmed?</value>
  </data>
  <data name="PONumber" xml:space="preserve">
    <value>Purchase Order Number</value>
  </data>
  <data name="PORating" xml:space="preserve">
    <value>PO Rating</value>
  </data>
  <data name="PostedOnly" xml:space="preserve">
    <value>Posted Lines Only?</value>
  </data>
  <data name="Price" xml:space="preserve">
    <value>Price</value>
  </data>
  <data name="Product" xml:space="preserve">
    <value>Product</value>
  </data>
  <data name="ProductDescription" xml:space="preserve">
    <value>Product Description</value>
  </data>
  <data name="PurchaseOrdersDueIn" xml:space="preserve">
    <value>Purchase Orders Due In</value>
  </data>
  <data name="PurchaseOrdersDueInforBuyer" xml:space="preserve">
    <value>Purchase Orders Due In for Buyer</value>
  </data>
  <data name="PurchaseOrdersDueInforSalesperson" xml:space="preserve">
    <value>Purchase Orders Due In for Salesperson</value>
  </data>
  <data name="PurchaseRequisitions" xml:space="preserve">
    <value>Purchase Requisitions</value>
  </data>
  <data name="PurchaseRequisitionsforaCustomer" xml:space="preserve">
    <value>Purchase Requisitions for a Customer</value>
  </data>
  <data name="PurchaseRequisitionsforaSalesPerson" xml:space="preserve">
    <value>Purchase Requisitions for a Sales Person</value>
  </data>
  <data name="PurchaseValue" xml:space="preserve">
    <value>Purchase Value</value>
  </data>
  <data name="Purchasing" xml:space="preserve">
    <value>Purchasing</value>
  </data>
  <data name="QualityControlNotes" xml:space="preserve">
    <value>Quality Control Notes</value>
  </data>
  <data name="Quantity" xml:space="preserve">
    <value>Quantity</value>
  </data>
  <data name="QuantityAllocated" xml:space="preserve">
    <value>Quantity Allocated</value>
  </data>
  <data name="QuantityInStock" xml:space="preserve">
    <value>Quantity In Stock</value>
  </data>
  <data name="QuantityOnOrder" xml:space="preserve">
    <value>Quantity On Order</value>
  </data>
  <data name="QuantityOrdered" xml:space="preserve">
    <value>Quantity Ordered</value>
  </data>
  <data name="RealPrice" xml:space="preserve">
    <value>Real Price</value>
  </data>
  <data name="Reason" xml:space="preserve">
    <value>Reason</value>
  </data>
  <data name="ReceivedCustomerRMAs" xml:space="preserve">
    <value>Received Customer RMAs</value>
  </data>
  <data name="ReceivedCustomerRMAsforaCustomer" xml:space="preserve">
    <value>Received Customer RMAs for a Customer</value>
  </data>
  <data name="ReceivedCustomerRMAsforaCustomerwithReasons" xml:space="preserve">
    <value>Received Customer RMAs for a Customer with Reasons</value>
  </data>
  <data name="ReceivedCustomerRMAsforaSaleperson" xml:space="preserve">
    <value>Received Customer RMAs for a Saleperson</value>
  </data>
  <data name="ReceivedCustomerRMAsforaSalepersonwithReasons" xml:space="preserve">
    <value>Received Customer RMAs for a Saleperson with Reasons</value>
  </data>
  <data name="ReceivedCustomerRMAswithReasons" xml:space="preserve">
    <value>Received Customer RMAs with Reasons</value>
  </data>
  <data name="ReceivedDate" xml:space="preserve">
    <value>Received Date</value>
  </data>
  <data name="ReceivedGoodsValuationbyCountry" xml:space="preserve">
    <value>Received Goods Valuation by Country</value>
  </data>
  <data name="Receiving" xml:space="preserve">
    <value>Receiving</value>
  </data>
  <data name="ReceivingNotes" xml:space="preserve">
    <value>Receiving Notes</value>
  </data>
  <data name="Requirements" xml:space="preserve">
    <value>Requirements</value>
  </data>
  <data name="Requisitions" xml:space="preserve">
    <value>Requisitions</value>
  </data>
  <data name="Resale" xml:space="preserve">
    <value>Resale</value>
  </data>
  <data name="ResaleInBase" xml:space="preserve">
    <value>Resale In Base</value>
  </data>
  <data name="ResaleValue" xml:space="preserve">
    <value>Resale Value</value>
  </data>
  <data name="ReturnCost" xml:space="preserve">
    <value>Return Cost</value>
  </data>
  <data name="ReturnDate" xml:space="preserve">
    <value>Return Date</value>
  </data>
  <data name="ReturnPrice" xml:space="preserve">
    <value>Return Price</value>
  </data>
  <data name="ReturnValue" xml:space="preserve">
    <value>Return Value</value>
  </data>
  <data name="ROHSCompliant" xml:space="preserve">
    <value>ROHS Compliant</value>
  </data>
  <data name="RowsAffected" xml:space="preserve">
    <value>Rows Affected</value>
  </data>
  <data name="Sale" xml:space="preserve">
    <value>Sale</value>
  </data>
  <data name="Sales" xml:space="preserve">
    <value>Sales</value>
  </data>
  <data name="SalesHistory" xml:space="preserve">
    <value>Sales History</value>
  </data>
  <data name="Salesman" xml:space="preserve">
    <value>Salesperson</value>
  </data>
  <data name="SalesmanGP" xml:space="preserve">
    <value>Salesperson GP</value>
  </data>
  <data name="SalesmanPercent" xml:space="preserve">
    <value>Salesperson %</value>
  </data>
  <data name="SalesOrderLineBaseValue" xml:space="preserve">
    <value>Sales Price (Base)</value>
  </data>
  <data name="SalesOrderLineQuantity" xml:space="preserve">
    <value>Sales Order Line Quantity</value>
  </data>
  <data name="SalesOrderLineValue" xml:space="preserve">
    <value>Sales Order Line Value</value>
  </data>
  <data name="ShipASAP" xml:space="preserve">
    <value>Ship ASAP</value>
  </data>
  <data name="ShippedGoodsValuationbyCountry" xml:space="preserve">
    <value>Shipped Goods Valuation by Country</value>
  </data>
  <data name="ShippedOrdersSortedbyInvoiceNumber" xml:space="preserve">
    <value>Shipped Orders Sorted by Invoice Number</value>
  </data>
  <data name="ShippedOrdersSortedbyInvoiceNumberforaCustomer" xml:space="preserve">
    <value>Shipped Orders Sorted by Invoice Number for a Customer</value>
  </data>
  <data name="ShippedOrdersSortedbyInvoiceNumberforaSalesperson" xml:space="preserve">
    <value>Shipped Orders Sorted by Invoice Number for a Salesperson</value>
  </data>
  <data name="ShippedSales" xml:space="preserve">
    <value>Shipped Sales</value>
  </data>
  <data name="ShippedSalesforLot" xml:space="preserve">
    <value>Shipped Sales for Lot</value>
  </data>
  <data name="ShippedSupplierRMAs" xml:space="preserve">
    <value>Shipped Supplier RMAs</value>
  </data>
  <data name="ShippedSupplierRMAsforaBuyer" xml:space="preserve">
    <value>Shipped Supplier RMAs for a Buyer</value>
  </data>
  <data name="ShippedSupplierRMAsforaBuyerwithReasons" xml:space="preserve">
    <value>Shipped Supplier RMAs for a Buyer with Reasons</value>
  </data>
  <data name="ShippedSupplierRMAsforaSupplier" xml:space="preserve">
    <value>Shipped Supplier RMAs for a Supplier</value>
  </data>
  <data name="ShippedSupplierRMAsforaSupplierwithReasons" xml:space="preserve">
    <value>Shipped Supplier RMAs for a Supplier with Reasons</value>
  </data>
  <data name="ShippedSupplierRMAswithReasons" xml:space="preserve">
    <value>Shipped Supplier RMAs with Reasons</value>
  </data>
  <data name="Shipping" xml:space="preserve">
    <value>Shipping</value>
  </data>
  <data name="ShippingCharge" xml:space="preserve">
    <value>Shipping Charge</value>
  </data>
  <data name="ShippingCost" xml:space="preserve">
    <value>Shipping Cost</value>
  </data>
  <data name="ShipVia" xml:space="preserve">
    <value>Ship Via</value>
  </data>
  <data name="SOApproved" xml:space="preserve">
    <value>SO Approved</value>
  </data>
  <data name="SONumber" xml:space="preserve">
    <value>Sales Order Number</value>
  </data>
  <data name="SORating" xml:space="preserve">
    <value>SO Rating</value>
  </data>
  <data name="StartDate" xml:space="preserve">
    <value>Start Date</value>
  </data>
  <data name="StartTime" xml:space="preserve">
    <value>Start Time</value>
  </data>
  <data name="State" xml:space="preserve">
    <value>State</value>
  </data>
  <data name="Stock" xml:space="preserve">
    <value>Stock</value>
  </data>
  <data name="StockCount" xml:space="preserve">
    <value>Stock Count</value>
  </data>
  <data name="StockDate" xml:space="preserve">
    <value>Stock Date</value>
  </data>
  <data name="StockKeepingUnit" xml:space="preserve">
    <value>Stock Keeping Unit</value>
  </data>
  <data name="StockList" xml:space="preserve">
    <value>Stock List</value>
  </data>
  <data name="StockManagement" xml:space="preserve">
    <value>Stock Management</value>
  </data>
  <data name="StockName" xml:space="preserve">
    <value>Stock Name</value>
  </data>
  <data name="StockPart" xml:space="preserve">
    <value>Stock Part</value>
  </data>
  <data name="StockValuation" xml:space="preserve">
    <value>Stock Valuation</value>
  </data>
  <data name="Sum" xml:space="preserve">
    <value>Sum</value>
  </data>
  <data name="Sum_ShippingCost" xml:space="preserve">
    <value>Shipping Cost</value>
  </data>
  <data name="Summary" xml:space="preserve">
    <value>Summary</value>
  </data>
  <data name="SummaryBookedOrdersbyCustomer" xml:space="preserve">
    <value>Summary Booked Orders by Customer</value>
  </data>
  <data name="SummaryBookedOrdersbyDivision" xml:space="preserve">
    <value>Summary Booked Orders by Division</value>
  </data>
  <data name="SummaryBookedOrdersbySalesperson" xml:space="preserve">
    <value>Summary Booked Orders by Salesperson</value>
  </data>
  <data name="SummaryOpenOrdersbyCustomer" xml:space="preserve">
    <value>Summary Open Orders by Customer</value>
  </data>
  <data name="SummaryOpenOrdersbyDivision" xml:space="preserve">
    <value>Summary Open Orders by Division</value>
  </data>
  <data name="SummaryOpenOrdersbySalesperson" xml:space="preserve">
    <value>Summary Open Orders by Salesperson</value>
  </data>
  <data name="SummaryShippedSalesbyCustomer" xml:space="preserve">
    <value>Summary Shipped Sales by Customer</value>
  </data>
  <data name="SummaryShippedSalesbyDivision" xml:space="preserve">
    <value>Summary Shipped Sales by Division</value>
  </data>
  <data name="SummaryShippedSalesbySalesperson" xml:space="preserve">
    <value>Summary Shipped Sales by Salesperson</value>
  </data>
  <data name="Supplier" xml:space="preserve">
    <value>Supplier</value>
  </data>
  <data name="SupplierFax" xml:space="preserve">
    <value>Supplier Fax</value>
  </data>
  <data name="SupplierOnTimeDeliveryReport" xml:space="preserve">
    <value>Supplier On Time Delivery Report</value>
  </data>
  <data name="SupplierPart" xml:space="preserve">
    <value>Supplier Part</value>
  </data>
  <data name="SupplierReturnValue" xml:space="preserve">
    <value>Supplier Return Value</value>
  </data>
  <data name="SupplierRMA" xml:space="preserve">
    <value>Supplier RMA</value>
  </data>
  <data name="SupplierRMADate" xml:space="preserve">
    <value>Supplier RMA Date</value>
  </data>
  <data name="SupplierRMANumber" xml:space="preserve">
    <value>Supplier RMA Number</value>
  </data>
  <data name="SupplierRMAs" xml:space="preserve">
    <value>Supplier RMAs</value>
  </data>
  <data name="SupplierTelephone" xml:space="preserve">
    <value>Supplier Telephone</value>
  </data>
  <data name="System" xml:space="preserve">
    <value>System</value>
  </data>
  <data name="Target" xml:space="preserve">
    <value>Target</value>
  </data>
  <data name="Tax" xml:space="preserve">
    <value>Tax</value>
  </data>
  <data name="TaxRate" xml:space="preserve">
    <value>Tax Rate</value>
  </data>
  <data name="TeamName" xml:space="preserve">
    <value>Team Name</value>
  </data>
  <data name="Telephone" xml:space="preserve">
    <value>Telephone</value>
  </data>
  <data name="Terms" xml:space="preserve">
    <value>Terms</value>
  </data>
  <data name="TermsDate" xml:space="preserve">
    <value>Terms Date</value>
  </data>
  <data name="Title" xml:space="preserve">
    <value>Title</value>
  </data>
  <data name="TotalCost" xml:space="preserve">
    <value>Total Cost</value>
  </data>
  <data name="TotalLandedCost" xml:space="preserve">
    <value>Total Landed Cost</value>
  </data>
  <data name="TotalResale" xml:space="preserve">
    <value>Total Resale</value>
  </data>
  <data name="TotalSales" xml:space="preserve">
    <value>Total Sales</value>
  </data>
  <data name="TotalShippingCost" xml:space="preserve">
    <value>Total Shipping Cost</value>
  </data>
  <data name="TotalTax" xml:space="preserve">
    <value>Total Tax</value>
  </data>
  <data name="TotalValue" xml:space="preserve">
    <value>Total Value</value>
  </data>
  <data name="UnallocatedOnly" xml:space="preserve">
    <value>Unallocated Only?</value>
  </data>
  <data name="UnitPrice" xml:space="preserve">
    <value>Unit Price</value>
  </data>
  <data name="URL" xml:space="preserve">
    <value>URL</value>
  </data>
  <data name="UserList" xml:space="preserve">
    <value>User List</value>
  </data>
  <data name="Users" xml:space="preserve">
    <value>Users</value>
  </data>
  <data name="Value" xml:space="preserve">
    <value>Value</value>
  </data>
  <data name="ValueStockOnly" xml:space="preserve">
    <value>Valued Stock Only?</value>
  </data>
  <data name="Vendor" xml:space="preserve">
    <value>Vendor</value>
  </data>
  <data name="Warehouse" xml:space="preserve">
    <value>Warehouse</value>
  </data>
  <data name="WeightInKgs" xml:space="preserve">
    <value>Weight In Kgs</value>
  </data>
  <data name="ZeroValueOnly" xml:space="preserve">
    <value>Zero Value Only?</value>
  </data>
  <data name="TotalPOs" xml:space="preserve">
    <value>Total POs</value>
  </data>
  <data name="TotalSRMAs" xml:space="preserve">
    <value>Total SRMAs</value>
  </data>
  <data name="String1" xml:space="preserve">
    <value>Late Only</value>
  </data>
  <data name="OpenQuotes" xml:space="preserve">
    <value>Open Quotes</value>
  </data>
  <data name="Quotes" xml:space="preserve">
    <value>Quotes</value>
  </data>
  <data name="QuoteDate" xml:space="preserve">
    <value>Quote Date</value>
  </data>
  <data name="QuoteNumber" xml:space="preserve">
    <value>Quote Number</value>
  </data>
  <data name="QuantityShipped" xml:space="preserve">
    <value>Quantity Shipped</value>
  </data>
  <data name="ExpediteDate" xml:space="preserve">
    <value>Expedite Date</value>
  </data>
  <data name="ExpediteNotes" xml:space="preserve">
    <value>Expedite Notes</value>
  </data>
  <data name="OpenPurchaseOrdersbyCompanyType" xml:space="preserve">
    <value>Open Purchase Orders by Company Type</value>
  </data>
  <data name="OpenPurchaseOrdersbySupplier" xml:space="preserve">
    <value>Open Purchase Orders by Supplier</value>
  </data>
  <data name="ContactProblems" xml:space="preserve">
    <value>Contact Problems</value>
  </data>
  <data name="CurrencyProblems" xml:space="preserve">
    <value>Currency Problems</value>
  </data>
  <data name="InvalidCompanyPurchasingInfo" xml:space="preserve">
    <value>Invalid Purchasing Information</value>
  </data>
  <data name="InvalidCompanySalesInfo" xml:space="preserve">
    <value>Invalid Sales Information</value>
  </data>
  <data name="InvalidOnly" xml:space="preserve">
    <value>Invalid Only?</value>
  </data>
  <data name="TaxProblems" xml:space="preserve">
    <value>Tax Problems</value>
  </data>
  <data name="TermsProblems" xml:space="preserve">
    <value>Terms Problems</value>
  </data>
  <data name="RandomStockCheck" xml:space="preserve">
    <value>Random Stock Check</value>
  </data>
  <data name="NumberOfItems" xml:space="preserve">
    <value>Number of Items</value>
  </data>
  <data name="Comments" xml:space="preserve">
    <value>Comments</value>
  </data>
  <data name="InvoiceQuantity" xml:space="preserve">
    <value>Invoice Quantity</value>
  </data>
  <data name="QuantityReceived" xml:space="preserve">
    <value>Quantity Received</value>
  </data>
  <data name="ZipCode" xml:space="preserve">
    <value>Zip Code</value>
  </data>
  <data name="Approved" xml:space="preserve">
    <value>Approved</value>
  </data>
  <data name="ReceivedBy" xml:space="preserve">
    <value>Received By</value>
  </data>
  <data name="ShippedBy" xml:space="preserve">
    <value>Shipped By</value>
  </data>
  <data name="BulkEmailInvoiceStatus" xml:space="preserve">
    <value>Bulk Email Invoice Status</value>
  </data>
  <data name="ContactEmail" xml:space="preserve">
    <value>Contact Email</value>
  </data>
  <data name="emailStatus" xml:space="preserve">
    <value>Status</value>
  </data>
  <data name="InvoiceEmailId" xml:space="preserve">
    <value>No</value>
  </data>
  <data name="InvoiceNo" xml:space="preserve">
    <value>Invoice No</value>
  </data>
  <data name="InvoicePrice" xml:space="preserve">
    <value>Invoice Price</value>
  </data>
  <data name="Name" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="Sentdate" xml:space="preserve">
    <value>Sent Date</value>
  </data>
  <data name="InvoiceEmailStatus" xml:space="preserve">
    <value>Invoice Email Status</value>
  </data>
  <data name="Incoterms" xml:space="preserve">
    <value>Incoterms</value>
  </data>
  <data name="FailedOnly" xml:space="preserve">
    <value>Failed Only</value>
  </data>
  <data name="BankFee" xml:space="preserve">
    <value>Bank Fee</value>
  </data>
  <data name="BankFeeInBase" xml:space="preserve">
    <value>Bank Fee In Base</value>
  </data>
  <data name="CurrencyDescription" xml:space="preserve">
    <value>Currency Description</value>
  </data>
  <data name="ExchangeRate" xml:space="preserve">
    <value>Exchange Rate</value>
  </data>
  <data name="CurrentLandedCost" xml:space="preserve">
    <value>Current Landed Cost</value>
  </data>
  <data name="OriginalLandedCost" xml:space="preserve">
    <value>Original Landed Cost</value>
  </data>
  <data name="StockId" xml:space="preserve">
    <value>Stock Id</value>
  </data>
  <data name="NewLandedCost" xml:space="preserve">
    <value>New Landed Cost</value>
  </data>
  <data name="LotStockProvision" xml:space="preserve">
    <value>Lot Stock Provision</value>
  </data>
  <data name="POLineNo" xml:space="preserve">
    <value>PO Line No</value>
  </data>
  <data name="SOLineNo" xml:space="preserve">
    <value>SO Line No</value>
  </data>
  <data name="Checked" xml:space="preserve">
    <value>Checked</value>
  </data>
  <data name="DateRequired" xml:space="preserve">
    <value>Date Required</value>
  </data>
  <data name="GIReceivedDate" xml:space="preserve">
    <value>GI Received Date</value>
  </data>
  <data name="Released" xml:space="preserve">
    <value>Released</value>
  </data>
  <data name="CustomerRequirementNumber" xml:space="preserve">
    <value>Customer Requirement Number</value>
  </data>
  <data name="SalesManName" xml:space="preserve">
    <value>SalesMan Name</value>
  </data>
  <data name="PurchaseQuoteLineId" xml:space="preserve">
    <value>PurchaseQuoteLineId</value>
  </data>
  <data name="BOMNumber" xml:space="preserve">
    <value>HUBRFQ Number</value>
  </data>
  <data name="RequirementNumber" xml:space="preserve">
    <value>Requirement Number</value>
  </data>
  <data name="ManufacturerName" xml:space="preserve">
    <value>Manufacturer Name</value>
  </data>
  <data name="Packagename" xml:space="preserve">
    <value>Package Name</value>
  </data>
  <data name="PQId" xml:space="preserve">
    <value>PQ Id</value>
  </data>
  <data name="ProductName" xml:space="preserve">
    <value>Product Name</value>
  </data>
  <data name="CurrencyCode" xml:space="preserve">
    <value>Currency</value>
  </data>
  <data name="FactorySealed" xml:space="preserve">
    <value>Factory Sealed</value>
  </data>
  <data name="LeadTime" xml:space="preserve">
    <value>Lead time</value>
  </data>
  <data name="MSL" xml:space="preserve">
    <value>MSL</value>
  </data>
  <data name="ROHSStatus" xml:space="preserve">
    <value>ROHS Status</value>
  </data>
  <data name="SPQ" xml:space="preserve">
    <value>SPQ</value>
  </data>
  <data name="LeadTimeWks" xml:space="preserve">
    <value>Lead time (wks)</value>
  </data>
  <data name="LTB" xml:space="preserve">
    <value>LTB</value>
  </data>
  <data name="MOQ" xml:space="preserve">
    <value>MOQ</value>
  </data>
  <data name="MPNQuoted" xml:space="preserve">
    <value>MPN Quoted</value>
  </data>
  <data name="PackageType" xml:space="preserve">
    <value>Package Type</value>
  </data>
  <data name="SupplierNotes" xml:space="preserve">
    <value>Notes</value>
  </data>
  <data name="ProductType" xml:space="preserve">
    <value>Product Type</value>
  </data>
  <data name="QuantityQuoted" xml:space="preserve">
    <value>Quantity Quoted</value>
  </data>
  <data name="RohsYN" xml:space="preserve">
    <value>Rohs (Y/N)</value>
  </data>
  <data name="TQSA" xml:space="preserve">
    <value>TQSA</value>
  </data>
  <data name="Salesman2" xml:space="preserve">
    <value>Salesman2</value>
  </data>
  <data name="CurrencyNo" xml:space="preserve">
    <value>Currency No</value>
  </data>
  <data name="Adjsutment" xml:space="preserve">
    <value>Adjsutment</value>
  </data>
  <data name="DutyRate" xml:space="preserve">
    <value>Duty Rate</value>
  </data>
  <data name="HUBBuyer" xml:space="preserve">
    <value>HUB Buyer</value>
  </data>
  <data name="HUBPONumber" xml:space="preserve">
    <value>HUB PO Number</value>
  </data>
  <data name="Avoidable" xml:space="preserve">
    <value>Avoidable</value>
  </data>
  <data name="DateConfirmed" xml:space="preserve">
    <value>SO Line Confirmed Date</value>
  </data>
  <data name="ShipStatus" xml:space="preserve">
    <value>Ship Status</value>
  </data>
  <data name="PODeliveryStatus" xml:space="preserve">
    <value>PO Delivery Status</value>
  </data>
  <data name="QtyUnallocatedAll" xml:space="preserve">
    <value>Qty Unallocated (In Stock/On Order)</value>
  </data>
  <data name="QtyUnallocatedOnOrder" xml:space="preserve">
    <value>Qty Unallocated (On Order)</value>
  </data>
  <data name="QtyUnallocatedStock" xml:space="preserve">
    <value>Qty Unallocated (In Stock)</value>
  </data>
  <data name="CRInstructionsNotes" xml:space="preserve">
    <value>Internal Notes</value>
  </data>
  <data name="RequiredDate" xml:space="preserve">
    <value>HUBRFQ Required Date</value>
  </data>
  <data name="TargetPrice" xml:space="preserve">
    <value>Target Price</value>
  </data>
  <data name="DailyReportLog" xml:space="preserve">
    <value>Daily Report Log</value>
  </data>
  <data name="Action" xml:space="preserve">
    <value>Parameter Details</value>
  </data>
  <data name="CreatedBy" xml:space="preserve">
    <value>By</value>
  </data>
  <data name="LogDate" xml:space="preserve">
    <value>Date</value>
  </data>
  <data name="ReportType" xml:space="preserve">
    <value>Report Type</value>
  </data>
  <data name="ReportName" xml:space="preserve">
    <value>Report Name</value>
  </data>
  <data name="iRowCount" xml:space="preserve">
    <value>Number of Row</value>
  </data>
  <data name="ECCN" xml:space="preserve">
    <value>ECCN</value>
  </data>
  <data name="AveragePriceLytica" xml:space="preserve">
    <value>Avg. Price (50th Percentile)</value>
  </data>
  <data name="MarketLeadingLytica" xml:space="preserve">
    <value>Market Leading (Lytica)</value>
  </data>
  <data name="TargetPriceLytica" xml:space="preserve">
    <value>Target Price (70th Percentile)</value>
  </data>
  <data name="CompanyNo" xml:space="preserve">
    <value>Company No</value>
  </data>
  <data name="CustomerNo" xml:space="preserve">
    <value>Customer No</value>
  </data>
  <data name="SOSentToCustomer" xml:space="preserve">
    <value>SO Sent To Customer</value>
  </data>
  <data name="SupplierNo" xml:space="preserve">
    <value>Supplier No</value>
  </data>
  <data name="LyticaManufacture" xml:space="preserve">
    <value>Lytica Manufacture</value>
  </data>
</root>