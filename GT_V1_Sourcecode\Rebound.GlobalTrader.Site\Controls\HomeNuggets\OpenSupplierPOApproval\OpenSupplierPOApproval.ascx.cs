﻿using System;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site.Controls;

namespace Rebound.GlobalTrader.Site.Controls.HomeNuggets
{
    public partial class OpenSupplierPOApproval : Base
    {

        protected SimpleDataTable _tblOpenSupplierPOApproval;
        protected Panel _pnlOpenSupplierPOApproval;

        protected override void OnInit(EventArgs e)
        {
            this.HomePageNuggetType = "OpenSupplierPOApproval";
            base.OnInit(e);
            WireUpControls();
            SetupTables();
            AddScriptReference("Controls.HomeNuggets.OpenSupplierPOApproval.OpenSupplierPOApproval.js");
        }

        protected override void OnLoad(EventArgs e)
        {
            _scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.HomeNuggets.OpenSupplierPOApproval", this.ctlDesignBase.ClientID);
            _scScriptControlDescriptor.AddElementProperty("pnlOpenSupplierPOApproval", _pnlOpenSupplierPOApproval.ClientID);
            _scScriptControlDescriptor.AddComponentProperty("tblOpenSupplierPOApproval", _tblOpenSupplierPOApproval.ClientID);
            base.OnLoad(e);
        }

        private void SetupTables()
        {
            _tblOpenSupplierPOApproval.Columns.Add(new SimpleDataColumn("PurchaseOrder", Unit.Pixel(50)));
            _tblOpenSupplierPOApproval.Columns.Add(new SimpleDataColumn("LineMgrApprovalStatus", Unit.Pixel(50)));
            _tblOpenSupplierPOApproval.Columns.Add(new SimpleDataColumn("QualityApprovalStatus", Unit.Pixel(50)));
            _tblOpenSupplierPOApproval.Columns.Add(new SimpleDataColumn("Date", Unit.Pixel(50)));
            _tblOpenSupplierPOApproval.Columns.Add(new SimpleDataColumn("SaDateOrdered", Unit.Pixel(50)));
        }

        private void WireUpControls()
        {
            _pnlOpenSupplierPOApproval = (Panel)FindContentControl("pnlOpenSupplierPOApproval");
            _tblOpenSupplierPOApproval = (SimpleDataTable)FindContentControl("tblOpenSupplierPOApproval");
        }

    }
}