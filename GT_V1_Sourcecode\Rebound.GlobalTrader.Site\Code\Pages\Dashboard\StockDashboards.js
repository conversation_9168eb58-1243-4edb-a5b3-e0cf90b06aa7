Type.registerNamespace("Rebound.GlobalTrader.Site.Pages.Dashboard");Rebound.GlobalTrader.Site.Pages.Dashboard.StockDashboards=function(n){Rebound.GlobalTrader.Site.Pages.Dashboard.StockDashboards.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Pages.Dashboard.StockDashboards.prototype={get_strStockurl:function(){return this._strStockurl},set_strStockurl:function(n){this._strStockurl!==n&&(this._strStockurl=n)},initialize:function(){Rebound.GlobalTrader.Site.Pages.Dashboard.StockDashboards.callBaseMethod(this,"initialize")},goInit:function(){document.getElementById("ifrmQlikStock").src=this._strStockurl;Rebound.GlobalTrader.Site.Pages.Dashboard.StockDashboards.callBaseMethod(this,"goInit")},dispose:function(){this.isDisposed||(this._strStockurl=null,Rebound.GlobalTrader.Site.Pages.Dashboard.StockDashboards.callBaseMethod(this,"dispose"))}};Rebound.GlobalTrader.Site.Pages.Dashboard.StockDashboards.registerClass("Rebound.GlobalTrader.Site.Pages.Dashboard.StockDashboards",Rebound.GlobalTrader.Site.Pages.Content,Sys.IDisposable);