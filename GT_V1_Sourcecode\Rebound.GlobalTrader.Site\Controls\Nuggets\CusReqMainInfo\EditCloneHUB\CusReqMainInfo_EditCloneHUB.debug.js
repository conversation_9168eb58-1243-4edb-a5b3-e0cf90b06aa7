///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//Marker     Changed by      Date         Remarks
//[001]      <PERSON>     18/05/2021    Clone and Send to HUBRFQ
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.CusReqMainInfo_EditCloneHUB = function (element) {
    Rebound.GlobalTrader.Site.Controls.Forms.CusReqMainInfo_EditCloneHUB.initializeBase(this, [element]);
    this._intCustomerRequirementID = -1;
    this._intCompanyID = 0;
    this._strSourceSelected = "";
    this._RadChecked = false;
    this._radioCheck = false;
    this._isPoRequest = false;
    this._intBOMID = -1;
    this._radObsoleteChk = false;
    this._radLastTimeBuyChk = false;
    this._radRefirbsAcceptableChk = false;
    this._radTestingRequiredChk = false;
    this._BOMHeaderDisplayStatus = false;
    this._blnCurInSameFaimly = true;
    this._intCurrencyNo = 0;
    this._radAlternativesAcceptedChK = false;
    this._radRepeatBusinessChk = false;
    this._hidCountryOfOrigin = "";
    this._hidCountryOfOriginNo = 0;
    this._hidLifeCycleStage = "";
    this._hidHTSCode = "";
    this._hidAveragePrice = 0;
    this._hidPackaging = "";
    this._hidPackagingSize = "";
    this._hidDescriptions = "";
    //ihs variable start
    this._ctlCompany = "";
   // this._hidCompanyName = "";
    //this._hidCompanyID = 0;
    this._ctlContact = -1;//res.ContactNo, res.Contact
    this._hidContactID = -1;
    this._ctlQuantity = 0;
    this._ctlPartNo = "";
    this._ctlCustomerPart = "";
   // this._ctlManufacturer = "";//res.ManufacturerNo, res.Manufacturer
    this._hidManufacturer = "";
    this._hidManufacturerNo = "";
    this._ctlDateCode = "";
    this._ctlProduct = "";
    this._ctlProductDis = false;//(res.Product, res.IsProdHaz)
   // this._ctlPrdDutyCodeRate = "";
    this._hidProductID = 0;
    //this._ctlPackage = "";
    this._hidPackageID = -1;
    //this._ctlTargetPrice = 0;
    this._hidPrice = 0;
    //this._ctlCurrency = "";
    this._hidCurrencyID = 0;
    this._ctlDateRequired = "";
    //this._ctlUsage = "";
    this._hidUsageID = -1;
    this._ctlNotes = "";
    this._ctlInstructions = "";
    this._ctlROHS = -1;
    //this._hidROHS = 0;
   // this._ctlClosed = "";
   // this._ctlClosedReason = "";
    //this._hidDisplayStatus = "";
    this._ctlPartWatch = "";
    this._ctlBOM = false;
    this._ctlBOMName = "";
    this._ctlBOMHeader = "";//res.BOMId, $R_FN.setCleanTextValue(res.BOMHeader
    this._hidBOMID = 0;
    //this._hidBOMHeaderDisplayStatus = false;
    this._ctlMSL = -1;
   // this._ctlFactorySealed = false;
    this._ctlPQA = false;
    //this._ctlObsolete = false;
    //this._ctlLastTimeBuy = false;
    //this._ctlRefirbsAcceptable = false;
    //this._ctlTestingRequired =false;
   // this._ctlTargetSellPrice = 0;
    //this._ctlCompetitorBestoffer = 0;
    this._ctlCustomerDecisionDate = "";
    this._ctlRFQClosingDate = "";
    this._ctlQuoteValidityRequiredHid = "";
   // this._ctlQuoteValidityRequired = "";
    this._ctlTypeHid = -1;
    //this._ctlType = "";
    this._ctlOrderToPlace = false;
   // this._ctlRequirementforTraceability = "";
    this._ctlRequirementforTraceabilityHid = "";
    this._ctlTargetSellPriceHidden = 0;
    this._ctlCompetitorBestofferHidden = 0;
    this._ctlEAU = "";
    this._hidCustGCNo = null;
    //this._ctlAlternativesAccepted = false;
    //this._ctlRepeatBusiness = false;
    //this._blnProdInactive =false;
    //this._strhidMSL = 0;
    this._ctlSalespersion = null;
    this._hidSalesPersion = null;

    this._IHSProductNo = 0;
    this._IHSProduct = "";
    this._IHSHTSCode = "";
    this._IHSDutyCode = 0;
    this._AlternateStatus = 0;
    this.chkAlternatives = false;
    this._ECCNCode = "";
    this._ctlPackage = "";
    //ihs variable end
    this._isRestrictedManufacturer = 0;
    this._RestrictedMFRMessage = "";
    this._PartEditStatus = 0;
    this._searchType = "";
    var strsearchtype = "";
    this._enmSearchType = 0;
    
};

Rebound.GlobalTrader.Site.Controls.Forms.CusReqMainInfo_EditCloneHUB.prototype = {

    get_intCustomerRequirementID: function () { return this._intCustomerRequirementID; }, set_intCustomerRequirementID: function (value) { if (this._intCustomerRequirementID !== value) this._intCustomerRequirementID = value; },
    //get_tblPartdetails: function () { return this._tblPartdetails; }, set_tblPartdetails: function (v) { if (this._tblPartdetails !== v) this._tblPartdetails = v; },
    get_btn1: function () { return this._btn1; }, set_btn1: function (v) { if (this._btn1 !== v) this._btn1 = v; },
    get_btn2: function () { return this._btn2; }, set_btn2: function (v) { if (this._btn2 !== v) this._btn2 = v; },
    get_lblError: function () { return this._lblError; }, set_lblError: function (v) { if (this._lblError !== v) this._lblError = v; },
    get_pnlPartDetail: function () { return this._pnlPartDetail; }, set_pnlPartDetail: function (value) { if (this._pnlPartDetail !== value) this._pnlPartDetail = value; },
    get_ctltblPartdetails: function () { return this._ctltblPartdetails; }, set_ctltblPartdetails: function (v) { if (this._ctltblPartdetails !== v) this._ctltblPartdetails = v; },

    initialize: function () {
        Rebound.GlobalTrader.Site.Controls.Forms.CusReqMainInfo_EditCloneHUB.callBaseMethod(this, "initialize");
        this.addShown(Function.createDelegate(this, this.formShown));
        //this._tblPartdetails.addSelectedIndexChanged(Function.createDelegate(this, this.getParSearch));
        this._ctltblPartdetails.addItemSelected(Function.createDelegate(this, this.getIHSDataSelected));
        this._ctlMail = $find(this.getField("ctlSendMailMessage").ID);
        this._ctlMail._ctlRelatedForm = this;
    
    },
    getFormControlID: function (ParentId, controlID) {

        var str = "";
        str = String.format("{0}_{1}", ParentId, controlID);
        return str;
    },
    formShown: function () {
        if (this._PartEditStatus == 1) {
            $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_showhidepartnolableHUB").hide();
            $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_showhidePartnotextHUB").show();
            this.ClearIhsDataOnLoad();
            document.getElementById("ParttypeSearch2").addEventListener("click", Function.createDelegate(this, this.changeSearchType));
            if (this._enmSearchType == 0) {
                $("#ParttypeSearch2").removeClass("searchType_StartsWith");
                $("#ParttypeSearch2").removeClass("searchType_ExactWith");
                $("#ParttypeSearch2").addClass("searchType_Contains");
                $("#ParttypeSearch2").attr("title", "Contains");
                $("#ParttypeSearch2").attr("alt", "Contains");
                $find(this.getFormControlID(this._element.id, 'cmbIHS'))._aut._txtGroup = "contains";

            }
            if ($find(this.getFormControlID(this._element.id, 'cmbIHS'))) $find(this.getFormControlID(this._element.id, 'cmbIHS'))._aut.addSelectionMadeEvent(Function.createDelegate(this, this.getIHSPartDetails));
            document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_btnClear").addEventListener("click", Function.createDelegate(this, this.ClearIhsData));
            //edit part
            document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_cmbIHS12txt").value = this._ctlPartNo;
            //code end
            document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_btn1").addEventListener("click", Function.createDelegate(this, this.SearchTypePopup));
            document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_btn3").addEventListener("click", Function.createDelegate(this, this.Toggle1));
            document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_btn2").addEventListener("click", Function.createDelegate(this, this.Toggle2));
            document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_btn5").addEventListener("click", Function.createDelegate(this, this.Canceltop));
            document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_btn6").addEventListener("click", Function.createDelegate(this, this.Cancelbottom));
            $addHandler(document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_btnOK_hyp"), "click", Function.createDelegate(this, this.getValuesByPartsOnClick));

        }

        else {
            $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_showhidePartnotextHUB").hide();
            $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_showhidepartnolableHUB").show();
           // $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_cmbPartEccnMappedaut_ctl04").hide();


        }
        $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_txtPartNo").hide();
        $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_lblError").hide();


        // part edit status end
        //////////////////////////////////////////////////////
        $("#lblRsMFRClone").hide();
        $("#spanmfrClone").text("");
        //quickSearchReselect
        //$find('ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_ctlSendMailMessage_ctlTo').reselect();
        //$find('ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_ctlSendMailMessage').trigger('click');
        $find(this.getFormControlID(this._element.id, 'BOMPoHubBuyer')).setValue(0);
        $find(this.getFormControlID(this._element.id, 'ddlSalesmanContact2')).setValue(0);
        $get(this.getFormControlID(this._element.id, 'txtQuoteRequired')).value = "";
        
        $("#lbleditClonehubtxtInstructions").text(2000 - this._ctlInstructions.length);
        $("#lbleditClonehubtxtNotes").text(2000 - this._ctlNotes.length);

        $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_lblDateRequired2").text("");
        $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_lblRFQClosingDate2").text("");
        $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_lblCustomerDecisionDate2").text("");
        $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_chkAlternatives_ctl00").addClass("off");
        if (this._AlternateStatus != 'undefined' && this._AlternateStatus > 0) {
           $find(this.getFormControlID(this._element.id, 'chkAlternatives')).enableButton(false);
        }
        else {
           $find(this.getFormControlID(this._element.id, 'chkAlternatives')).enableButton(true);
        }
    
       //[005] ihs set value
        $find(this.getFormControlID(this._element.id, 'ddlSalesman')).getData();
        $find(this.getFormControlID(this._element.id, 'ddlROHS')).getData();
        $find(this.getFormControlID(this._element.id, 'ddlType')).getData();
        $find(this.getFormControlID(this._element.id, 'ddlUsage')).getData();
        //$find(this.getFormControlID(this._element.id, 'ddlPackage')).getData();

        $find(this.getFormControlID(this._element.id, 'BOMPoHubBuyer')).getData();
        $find(this.getFormControlID(this._element.id, 'ddlSalesmanContact2')).getData();
        
        $find(this.getFormControlID(this._element.id, 'ddlMsl')).getData();
        $find(this.getFormControlID(this._element.id, 'ddlQuoteValidityRequired')).getData();
        $find(this.getFormControlID(this._element.id, 'ddlRequirementforTraceability')).getData();
        //Dropdown value bind
        $find(this.getFormControlID(this._element.id, 'ddlSalesman')).setValue(this._salesmanNo);
       // alert(this.setControlValue(this.getFormControlID(this._element.id, 'ddlSalesman'), 'DropDown'));
        $find(this.getFormControlID(this._element.id, 'ddlROHS')).setValue(this._ctlROHS);
        $find(this.getFormControlID(this._element.id, 'ddlType')).setValue(this._ctlTypeHid);
        $find(this.getFormControlID(this._element.id, 'ddlUsage')).setValue(this._hidUsageID);
        //$find(this.getFormControlID(this._element.id, 'ddlPackage')).setValue(this._hidPackageID);
        $find(this.getFormControlID(this._element.id, 'ddlMsl')).setValue(this._ctlMSL);
        $find(this.getFormControlID(this._element.id, 'ddlQuoteValidityRequired')).setValue(this._ctlQuoteValidityRequiredHid);
        $find(this.getFormControlID(this._element.id, 'ddlRequirementforTraceability')).setValue(this._ctlRequirementforTraceabilityHid);
        //Lable Value bind
        $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_lblCustomer").text(this._ctlCompany);
        $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_lblContact").text(this._ctlContact);
       
               
        $find(this.getFormControlID(this._element.id, 'cmbManufacturer')).setValue(this._hidManufacturer, $R_FN.setCleanTextValue(this._hidManufacturerNo));
        $find(this.getFormControlID(this._element.id, 'cmbProducts')).setValue(this._hidProductID, this._ctlProduct);
        $find(this.getFormControlID(this._element.id, 'cmbPackage')).setValue(this._hidPackageID, this._ctlPackage);
        $find(this.getFormControlID(this._element.id, 'cmbSalespersion')).setValue(this._hidSalesPersion, this._ctlSalespersion);
        
        
        //TextBox value bind
        $get(this.getFormControlID(this._element.id, 'txtCustomerPartNo')).value = this._ctlCustomerPart;
        $get(this.getFormControlID(this._element.id, 'txtQuantity')).value = this._ctlQuantity;
        //$get(this.getFormControlID(this._element.id, 'txtPartNo')).value = this._ctlPartNo;
        $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_lblPartNo").text(this._ctlPartNo);
        $get(this.getFormControlID(this._element.id, 'txtNotes')).value = this._ctlNotes;
        $get(this.getFormControlID(this._element.id, 'txtInstructions')).value = this._ctlInstructions;
        $get(this.getFormControlID(this._element.id, 'txtDateCode')).value = this._ctlDateCode;
        $get(this.getFormControlID(this._element.id, 'txtBOMName')).value = this._ctlBOMName;
        $get(this.getFormControlID(this._element.id, 'txtTargetPrice')).value = this._hidPrice;
        $get(this.getFormControlID(this._element.id, 'txtTargetSellPrice')).value = this._ctlTargetSellPriceHidden;
        //$get(this.getFormControlID(this._element.id, 'txtDateRequired')).value = $R_FN.shortDate();
        
        
        
        $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_lblDateRequired2").text(this._ctlDateRequired);
        $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_lblRFQClosingDate2").text(this._ctlRFQClosingDate);
        $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_lblCustomerDecisionDate2").text(this._ctlCustomerDecisionDate);
        //$get(this.getFormControlID(this._element.id, 'txtRFQClosingDate')).value = this._ctlRFQClosingDate;
        //$get(this.getFormControlID(this._element.id, 'txtDateRequired')).value = this._ctlDateRequired;
        //$get(this.getFormControlID(this._element.id, 'txtCustomerDecisionDate')).value = this._ctlCustomerDecisionDate;
        $get(this.getFormControlID(this._element.id, 'txtEau')).value = this._ctlEAU;
        $get(this.getFormControlID(this._element.id, 'txtCompetitorBestoffer')).value = this._ctlCompetitorBestofferHidden;
               
        //Checkbox Value Bind
        $find(this.getFormControlID(this._element.id, 'chkPartWatch')).setChecked(this._ctlPartWatch);
        $find(this.getFormControlID(this._element.id, 'chkPQA')).setChecked(this._ctlPQA);
        $find(this.getFormControlID(this._element.id, 'chkBOM')).setChecked(this._ctlBOM);
        $find(this.getFormControlID(this._element.id, 'chkOrderToPlace')).setChecked(this._ctlOrderToPlace);

        $find(this.getFormControlID(this._element.id, 'chkAlternativesAccepted')).setChecked(this._radAlternativesAcceptedChK);
        $find(this.getFormControlID(this._element.id, 'chkTestingRequired')).setChecked(this._radTestingRequiredChk);
        $find(this.getFormControlID(this._element.id, 'chkRefirbsAcceptable')).setChecked(this._radRefirbsAcceptableChk);
        $find(this.getFormControlID(this._element.id, 'chkFactorySealedSource')).setChecked(this._radioCheck);
        $find(this.getFormControlID(this._element.id, 'chkRepeatBusiness')).setChecked(this._radRepeatBusinessChk);
        $find(this.getFormControlID(this._element.id, 'chkObsolete')).setChecked(this._radObsoleteChk);
        $find(this.getFormControlID(this._element.id, 'chkLastTimeBuy')).setChecked(this._radLastTimeBuyChk);
        
        $find(this.getFormControlID(this._element.id, 'ddlCurrency'))._intGlobalCurrencyNo = this._hidCustGCNo;
        $find(this.getFormControlID(this._element.id, 'ddlCurrency'))._blnIsBuy = false;
        $find(this.getFormControlID(this._element.id, 'ddlCurrency')).setValue(this._hidCurrencyID);
        $find(this.getFormControlID(this._element.id, 'ddlCurrency')).getData();
        
        
        
        //[005] ihs set value end
        //this.showField("ctlPartNo", !this._isPoRequest);
        $('#divBlockBox2').hide();
        if (this._blnFirstTimeShown) {
            this.addSave(Function.createDelegate(this, this.saveClicked));
        }

       
       
        document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_parterror").style.backgroundColor = "";
        document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_parterror1").style.backgroundColor = "";
        document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_QuantityError").style.backgroundColor = "";
        document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_QuantityError1").style.backgroundColor = "";
        document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_CustomerTargetPriceError").style.backgroundColor = "";
        document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_CustomerTargetPriceError1").style.backgroundColor = "";
        document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_ManufacturerError").style.backgroundColor = "";
        document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_ManufacturerError1").style.backgroundColor = "";
        document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_ManufacturerError").style.backgroundColor = "";
        document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_ManufacturerError1").style.backgroundColor = "";
        document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_ProductError").style.backgroundColor = "";
        document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_ProductError1").style.backgroundColor = "";
        document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_TraceabilityError").style.backgroundColor = "";
        document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_TraceabilityError1").style.backgroundColor = "";
        document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_ddlTypeError").style.backgroundColor = "";
        document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_ddlTypeError1").style.backgroundColor = "";
        document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_SalespersonError").style.backgroundColor = "";
        document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_SalespersonError1").style.backgroundColor = "";
        document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_CurrencyError").style.backgroundColor = "";
        document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_CurrencyError1").style.backgroundColor = "";
        document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_DateRequiredError").style.backgroundColor = "";
        document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_DateRequiredError1").style.backgroundColor = "";

        document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_BuyerError").style.backgroundColor = "";
        document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_BuyerError1").style.backgroundColor = "";
        document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_QuoteRequiredError").style.backgroundColor = "";
        document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_QuoteRequiredError1").style.backgroundColor = "";
        //document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_Contact2Error").style.backgroundColor = "";
        //document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_Contact2Error1").style.backgroundColor = "";
       
        //end
      
        $find(this.getFormControlID(this._element.id, 'ddlBOM'))._intCompanyID = this._intCompanyID;
        $find(this.getFormControlID(this._element.id, 'ddlBOM')).getData();
        $find(this.getFormControlID(this._element.id, 'ddlBOM')).setValue(this._intCompanyID);
        this.allowEditingCurrency(this._blnCurInSameFaimly);

        
        if (this._IHSProductNo != 0 && this._hidProductID != 0) {
            
            document.getElementById("lblihsproduct2").style.color = 'yellow'
            //document.getElementById("lblMatchproduct").style.color = 'yellow'
            $("#spnIHSProduct2").text(this._IHSProduct);
        }
        else if (this._IHSProduct != 'undefined' && this._IHSProduct != '') {
            document.getElementById("lblihsproduct2").style.color = 'yellow'
            $("#spnIHSProduct2").text(this._IHSProduct);
            document.getElementById("lblMatchproduct2").style.color = ''

        }
        else {
            document.getElementById("lblihsproduct2").style.color = ''
            $("#spnIHSProduct2").text(" ( N/A ) ");
            document.getElementById("spnIHSProduct2").style.color = 'yellow'
            document.getElementById("lblMatchproduct2").style.color = ''

        }
        
        if (this._IHSHTSCode != 'undefined' && this._IHSHTSCode != '') {
            document.getElementById("lblihsHTSCode2").style.color = 'yellow'
             $("#spnHTSCode2").text(this._hidHTSCode);
            
        }
        else if (this._hidHTSCode != 'undefined' && this._hidHTSCode != '') {
            document.getElementById("lblihsHTSCode2").style.color = 'yellow'
            $("#spnHTSCode2").text(this._hidHTSCode);//text(this._hidHTSCode);
        }
        else {

            document.getElementById("lblihsHTSCode2").style.color = ''
            $("#spnHTSCode2").text(" ( N/A ) ");
            document.getElementById("spnHTSCode2").style.color = 'yellow'
        }
        if (this._IHSDutyCode != "") {
            $("#spnIHSDutyCode2").text(this._IHSDutyCode);
            document.getElementById("lblduty2").style.color = 'yellow'
            //alert(this._IHSDutyCode);
            
        }
        else
        {
            document.getElementById("lblduty2").style.color = ''
            //$("#spnIHSDutyCode").text(this.IHSDutyCode);
            $("#spnIHSDutyCode2").text(" ( N/A ) ");
            document.getElementById("spnIHSDutyCode2").style.color = 'yellow'

        }
        if (this._hidCountryOfOriginNo != 'undefined' && this._hidCountryOfOriginNo != 0) {
            document.getElementById("lblCoo2").style.color = 'yellow'
            $("#spnIHSCountryOfOrigin2").text(this._hidCountryOfOrigin);
        }
        else {
            document.getElementById("lblCoo2").style.color = ''
            $("#spnIHSCountryOfOrigin2").text(" ( " + "N/A" + " ) ");
            document.getElementById("spnIHSCountryOfOrigin2").style.color = 'yellow'

        }
        if (this._hidLifeCycleStage != 'undefined' && this._hidLifeCycleStage != '') {
            document.getElementById("lblpartstaus2").style.color = 'yellow'
            $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_LableLifeStatus").text(this._hidLifeCycleStage);
        }
        else {
            document.getElementById("lblpartstaus2").style.color = ''
            $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_LableLifeStatus").text(" ( " + "N/A" + " ) ");
            document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_LableLifeStatus").style.color = 'yellow'

        }

        //code for ECCN Code
        //if (this._ECCNCode != 'undefined' && this._ECCNCode != '') {
        //    document.getElementById("lblECCNCode2").style.color = 'yellow'
        //    $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_LableECCNCode").text(this._ECCNCode);
        //}
        //else {
        //    document.getElementById("lblECCNCode2").style.color = ''
        //    $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_LableECCNCode").text(" ( " + "N/A" + " ) ");
        //    document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_LableECCNCode").style.color = 'yellow'

        //}
        //code for ECCN Code
        if (this._ECCNCode != 'undefined' && this._ECCNCode != '') {
            document.getElementById("lblECCNCode2").style.color = 'yellow'
            this.SelectIHSEccnCode(this._ECCNCode);
        }
        else {
            document.getElementById("lblECCNCode2").style.color = ''
            $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_LableECCNCode").text(" ( " + "N/A" + " ) ");
            document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_LableECCNCode").style.color = 'yellow'

        }
        //code end for ECCN Code
        //code end for ECCN Code
        //document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_btn3").addEventListener("click", Function.createDelegate(this, this.Toggle1));
        //document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_btn2").addEventListener("click", Function.createDelegate(this, this.Toggle2));
        //document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_btn5").addEventListener("click", Function.createDelegate(this, this.Canceltop));
        //document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_btn6").addEventListener("click", Function.createDelegate(this, this.Cancelbottom));
        //document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_btn4").addEventListener("click", Function.createDelegate(this, this.getValuesByPartsOnClick));
        document.getElementById("myplasegrde2").addEventListener("click", Function.createDelegate(this, this.getPartDetaildata));
        document.getElementById("closePoppartdetails2").addEventListener("click", Function.createDelegate(this, this.hidpartdetaildive));
        
        $R_FN.showElement(this._pnlPartDetail, false);
        $('.dropDownRefresh').hide();
        if ($find(this.getFormControlID(this._element.id, 'cmbManufacturer'))) $find(this.getFormControlID(this._element.id, 'cmbManufacturer'))._aut.addSelectionMadeEvent(Function.createDelegate(this, this.RsMfrChanged));
        document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_cmbManufactureraut_ctl04").addEventListener("click", Function.createDelegate(this, this.ResetMFR));
    },
    getIHSPartDetails: function () {
        var id = $find(this.getFormControlID(this._element.id, 'cmbIHS'))._aut._varSelectedValue;
        var arr = $find(this.getFormControlID(this._element.id, 'cmbIHS'))._aut._varSelectedValue.split("->");
        $find(this.getFormControlID(this._element.id, 'cmbIHS'))._txt.value = arr[0];
        this.SaveIHSPartDetail($find(this.getFormControlID(this._element.id, 'cmbIHS'))._aut._varSelectedExtraData);

    },
    SaveIHSPartDetail: function (IHSResult) {
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/CusReqAdd");
        obj.set_DataObject("CusReqAdd");
        obj.set_DataAction("SaveIHSPartDetail");
        obj.addParameter("IHSResult", IHSResult);
        obj.addDataOK(Function.createDelegate(this, this.setIHSPartDetail));
        obj.addError(Function.createDelegate(this, this.IHSPartDetailError));
        obj.addTimeout(Function.createDelegate(this, this.IHSPartDetailError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;


    },
    setIHSPartDetail: function (args) {
        var obj = args._result.Result
        if (obj[0].IHSPartsId > 0) {
            $("#lblihsproduct2").show();
            $("#lblihsHTSCode2").show();
            $("#lblduty2").show();
            $("#lblCoo2").show();
            var PartName = obj[0].ID;
            if (PartName != 'undefined ' && PartName.length > 0) {
                //$get(this.getFormControlID(this._element.id, 'txtPartNo')).value = $R_FN.setCleanTextValue(PartName);
                $get(this.getFormControlID(this._element.id, 'cmbIHS12txt')).value = $R_FN.setCleanTextValue(PartName);
                document.getElementById("lblparts2").style.color = 'yellow'
            }
            else {
                document.getElementById("lblparts2").style.color = ''
            }
            if (obj[0].ROHSName != 'undefined ' && obj[0].ROHSName != '' && obj[0].ROHSName.length > 0) {
                // $("#spnMSL").show();
                $("#spnMSL2").text(" ( " + obj[0].ROHSName + " ) ");
                $find(this.getFormControlID(this._element.id, 'ddlMsl')).setValue(obj[0].ROHSName);
                document.getElementById("lblmls2").style.color = 'yellow';
                if (obj[0].ROHSName == 'N/A')
                    document.getElementById("spnMSL2").style.color = 'yellow';
                else
                    document.getElementById("spnMSL2").style.color = '';
            }
            else {
                $("#spnMSL2").text(" ( " + "N/A" + " ) ");
                $find(this.getFormControlID(this._element.id, 'ddlMsl')).setValue("N/A");
                document.getElementById("spnMSL2").style.color = 'yellow';
                document.getElementById("lblmls2").style.color = 'yellow';





            }
            if (obj[0].Manufacturer != 'undefined ' && obj[0].Manufacturer != '' && obj[0].Manufacturer.length > 0) {
                if (obj[0].ManufacturerNo != 0) {
                    $find(this.getFormControlID(this._element.id, 'cmbManufacturer')).setValue(obj[0].ManufacturerNo, $R_FN.setCleanTextValue(obj[0].Manufacturer));
                    $("#spnManufacturer").hide();
                    document.getElementById("lblmrf2").style.color = 'yellow'
                }
                else {

                    $find(this.getFormControlID(this._element.id, 'cmbManufacturer')).setValue("", 0);
                    $("#spnManufacturer").show();
                    $("#spnManufacturer").text(" (" + obj[0].ManufacturerFullName + " ) ");
                    document.getElementById("lblmrf2").style.color = ''
                }

            }
            else {

                $("#spnManufacturer").hide();

            }

            if (obj[0].ProdDesc != 'undefined ' && obj[0].IHSProdDesc.length > 0) {

                if (obj[0].ProdNo != 0) {
                    $find(this.getFormControlID(this._element.id, 'cmbProducts')).setValue(obj[0].ProdNo, $R_FN.setCleanTextValue(obj[0].ProdDesc));
                    //$("#spnManufacturer").hide();
                    document.getElementById("lblMatchproduct2").style.color = 'yellow';
                    $("#spnIHSProduct2").text($R_FN.setCleanTextValue(obj[0].IHSProdDesc));
                    document.getElementById("lblihsproduct2").style.color = 'yellow';
                }
                else {
                    $find(this.getFormControlID(this._element.id, 'cmbProducts')).setValue("", 0);
                    $("#spnIHSProduct2").show();
                    $("#spnIHSProduct2").text($R_FN.setCleanTextValue(obj[0].IHSProdDesc));
                    document.getElementById("lblihsproduct2").style.color = 'yellow';
                }

            }
            else {
                if (obj[0].HTSCode != '' || obj[0].IHSProdDes != '') {
                    $("#spnIHSProduct2").show();
                    $("#spnIHSProduct2").text(" ( " + "N/A" + " ) ");
                    document.getElementById("lblihsproduct2").style.color = 'yellow';
                    document.getElementById("spnIHSProduct2").style.color = 'yellow';
                } else {
                    document.getElementById("lblihsproduct2").style.color = '';
                }
            }

            if (obj[0].PackageId > 0) {
                $("#lblECCNCode2").show();
                // $find(this.getFormControlID(this._element.id, 'ddlPackage')).setValue(obj[0].PackageId);
                $find(this.getFormControlID(this._element.id, 'cmbPackage')).setValue(obj[0].PackageId, $R_FN.setCleanTextValue(obj[0].PackageDescription));
                $("#spnPackage2").text(" ( " + obj[0].Packaging + " ) ");
                document.getElementById('lblPackage2').style.color = "yellow";
                document.getElementById("spnPackage2").style.color = "";


            }
            else {
                $("#lblECCNCode2").show();
                $("#spnPackage2").text("  ( " + "N/A" + " ) ");
                document.getElementById('lblPackage2').style.color = "";
                document.getElementById("spnPackage2").style.color = 'yellow'
                // $find(this.getFormControlID(this._element.id, 'ddlPackage')).setValue(0);
                $find(this.getFormControlID(this._element.id, 'cmbPackage')).setValue("", 0);

            }

            if (obj[0].HTSCode != 'undefined ' && obj[0].HTSCode != '' && obj[0].HTSCode.length > 0) {
                $("#spnHTSCode2").show();
                $("#spnHTSCode2").text(obj[0].HTSCode);
                document.getElementById("lblihsHTSCode2").style.color = 'yellow'
            }
            else {
                if (obj[0].HTSCode != '' || obj[0].IHSProdDes != '') {
                    $("#spnHTSCode2").show();
                    $("#spnHTSCode2").text(" ( " + "N/A" + " ) ");
                    document.getElementById("lblihsHTSCode2").style.color = 'yellow'
                    document.getElementById("spnHTSCode2").style.color = 'yellow'
                } else {
                    document.getElementById("lblihsHTSCode2").style.color = ''
                    document.getElementById("spnHTSCode2").style.color = ''
                }

            }

            if (obj[0].IHSDutyCode != 'undefined ' && obj[0].IHSDutyCode != '' && obj[0].IHSDutyCode.length > 0) {
                $("#spnIHSDutyCode2").show();
                $("#spnIHSDutyCode2").text(" ( " + obj[0].IHSDutyCode + " ) ");
                document.getElementById("lblduty2").style.color = 'yellow'
                document.getElementById("spnIHSDutyCode2").style.color = ''

            }
            else {
                $("#spnIHSDutyCode2").text("( N/A )");
                document.getElementById("lblduty").style.color = ''
                document.getElementById("spnIHSDutyCode2").style.color = 'yellow'
            }
            if (obj[0].CountryOfOrigin != 'undefined ' && obj[0].CountryOfOrigin != '' && obj[0].CountryOfOrigin.length > 0) {
                $("#spnIHSCountryOfOrigin2").show();
                $("#spnIHSCountryOfOrigin2").text(obj[0].CountryOfOrigin);
                document.getElementById("lblCoo2").style.color = 'yellow'
                document.getElementById("spnIHSCountryOfOrigin2").style.color = ''
            }
            else {
                $("#spnIHSCountryOfOrigin2").text(" ( " + "N/A" + " ) ");
                document.getElementById("lblCoo2").style.color = ''
                document.getElementById("spnIHSCountryOfOrigin2").style.color = 'yellow'
            }

            if (obj[0].PartStatus != 'undefined ' && obj[0].PartStatus != '' && obj[0].PartStatus.length > 0) {
                //$get(this.getFormControlID(this._element.id, 'txtLifeStatus')).value = $R_FN.setCleanTextValue(obj[0].PartStatus);
                $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_LableLifeStatus").text($R_FN.setCleanTextValue(obj[0].PartStatus));
                document.getElementById("lblpartstaus2").style.color = 'yellow'
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_LableLifeStatus").style.color = ''
            }
            else {
                //$get(this.getFormControlID(this._element.id, 'txtLifeStatus')).value = "";
                $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_LableLifeStatus").text(" ( " + "N/A" + " ) ");
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_LableLifeStatus").style.color = 'yellow'
                document.getElementById("lblpartstaus2").style.color = ''
            }
            //ECCN Code start
            if (obj[0].ECCNCode != 'undefined ' && obj[0].ECCNCode != '' && obj[0].ECCNCode.length > 0) {
                $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_LableECCNCode").text($R_FN.setCleanTextValue(obj[0].ECCNCode));
                document.getElementById("lblECCNCode2").style.color = 'yellow'
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_LableECCNCode").style.color = ''
            }
            else {
                $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_LableECCNCode").text(" ( " + "N/A" + " ) ");
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_LableECCNCode").style.color = 'yellow'
                document.getElementById("lblECCNCode2").style.color = ''
            }
            //ECCN Code End

            if (obj[0].Packaging != 'undefined ' && obj[0].Packaging != '') {
                this._hidPackaging = obj[0].Packaging;
            }
            else {
                this._hidPackaging = "";
            }
            if (obj[0].PackagingSize != 'undefined ' && obj[0].PackagingSize != '') {
                this._hidPackagingSize = obj[0].PackagingSize;
            }
            else {
                this._hidPackagingSize = "";
            }
            this._hidCountryOfOrigin = obj[0].CountryOfOrigin;
            this._hidCountryOfOriginNo = obj[0].CountryOfOriginNo;
            this._hidLifeCycleStage = obj[0].PartStatus;
            this._hidHTSCode = obj[0].HTSCode;
            this._hidAveragePrice = obj[0].AveragePrice;
            this._hidDescriptions = obj[0].Descriptions;
            this._IHSPartsId = obj[0].IHSPartsId;
            this._ihsCurrencyCode = obj[0].ihsCurrencyCode;
            this._hidIHSProduct = obj[0].IHSProduct;
            this._hidECCNCode = obj[0].ECCNCode;

        }

    },
    IHSPartDetailError: function (args) {
    },
    //Part Eccn Mapping strat code
    SelectIHSPartEccnMapped: function (IHSEccnCode, PartName) {
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/CusReqAdd");
        obj.set_DataObject("CusReqAdd");
        obj.set_DataAction("GetIHSPartEccnDetail");
        obj.addParameter("PartEccnCode", IHSEccnCode);
        obj.addParameter("PartNo", PartName);
        obj.addDataOK(Function.createDelegate(this, this.setIHSPartEccnDetail));
        obj.addError(Function.createDelegate(this, this.IHSPartECCNDetailError));
        obj.addTimeout(Function.createDelegate(this, this.IHSPartECCNDetailError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;


    },
    setIHSPartEccnDetail: function (args) {
        var obj = args._result.Result
        if (obj[0].PartEccnMappedId > 0) {
            //ECCN Code start
            if (obj[0].ECCNCode != 'undefined' && obj[0].ECCNCode != '' && obj[0].ECCNCode.length > 0) {

                $find(this.getFormControlID(this._element.id, 'cmbPartEccnMapped')).setValue(obj[0].ECCNNo, $R_FN.setCleanTextValue(obj[0].ECCNCode));
                this._hidECCNCode = obj[0].ECCNCode;

                $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_cmbPartEccnMappedaut").show();
                $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_cmbPartEccnMappedtxt").show();
                document.getElementById("lblECCNCode2").style.color = 'yellow'

            }


        }
        else if (this._hidECCNCode != '') {

            this.SelectIHSEccnCode(this._hidECCNCode);
            $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_cmbPartEccnMappedaut").show();
            $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_cmbPartEccnMappedtxt").show();

        }
        else {
            // alert(3);
            $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_LableECCNCode").text(" ( " + "N/A" + " ) ");
            document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_LableECCNCode").style.color = 'yellow'
            document.getElementById("lblECCNCode2").style.color = ''
            this._hidECCNCode = "";
            $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_cmbPartEccnMappedaut").show();
            $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_cmbPartEccnMappedtxt").show();
        }

    },
    IHSPartECCNDetailError: function (args) {
    },
    //code end


    //Bind Eccn Code from TbECCN Table strat code
    SelectIHSEccnCode: function (sEccnCode) {
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/CusReqAdd");
        obj.set_DataObject("CusReqAdd");
        obj.set_DataAction("GetIHSEccnCodeDetail");
        obj.addParameter("ECCNCode", sEccnCode);
        obj.addDataOK(Function.createDelegate(this, this.setIHSEccnCodeDetail));
        obj.addError(Function.createDelegate(this, this.IHSECCNCodeDetailError));
        obj.addTimeout(Function.createDelegate(this, this.IHSECCNCodeDetailError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;


    },
    setIHSEccnCodeDetail: function (args) {

        var obj = args._result.Result
        if (obj[0].ECCNNo > 0) {
            //ECCN Code start
            if (obj[0].ECCNCode != '') {

                $find(this.getFormControlID(this._element.id, 'cmbPartEccnMapped')).setValue(obj[0].ECCNNo, $R_FN.setCleanTextValue(obj[0].ECCNCode));
                document.getElementById("lblECCNCode").style.color = 'yellow'

                this._hidECCNCode = obj[0].ECCNCode;
                $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_cmbPartEccnMappedaut").show();
                $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_cmbPartEccnMappedtxt").show();
            }
            else {
                $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_LableECCNCode").text(" ( " + "N/A" + " ) ");
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_LableECCNCode").style.color = 'yellow'
                document.getElementById("lblECCNCode2").style.color = ''
                this._hidECCNCode = "";
                $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_cmbPartEccnMappedaut").show();
                $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_cmbPartEccnMappedtxt").show();

            }
        }
        else {
            //$find('ctl00_cphMain_ctlNotify_ctlDB_ctl14_ctlNotify_ctlDB_ctlSalespersion_ctl03_aut').reselect();
            $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_LableECCNCode").text(" ( " + "N/A" + " ) ");
            document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_LableECCNCode").style.color = 'yellow'
            document.getElementById("lblECCNCode2").style.color = ''
            this._hidECCNCode = "";
            $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_cmbPartEccnMappedaut").show();
            $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_cmbPartEccnMappedtxt").show();
        }

    },
    IHSECCNCodeDetailError: function (args) {
    },
    //code end

    ClearIhsData: function () {
        $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_cmbPartEccnMappedaut").hide();
        $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_cmbPartEccnMappedtxt").hide();
        $("#spanmfrEdit").text("");
        $("#spanmfr").text("");
        document.getElementById("lblMatchproduct2").style.color = ''
        $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_cmbIHS12aut_hypClose")[0].click();
        $("#lblIhsServiceMessage2").text("");
        $("#lblservicemsgerror2").text("");
        $get(this.getFormControlID(this._element.id, 'cmbIHS12txt')).value = "";
        $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_LableLifeStatus").text("");
        document.getElementById("lblparts2").style.color = '';
        document.getElementById("lblmls2").style.color = '';
        $find(this.getFormControlID(this._element.id, 'ddlMsl')).setValue(0);
        $find(this.getFormControlID(this._element.id, 'cmbManufacturer')).setValue("", 0);
        document.getElementById("lblmrf2").style.color = '';
        $find(this.getFormControlID(this._element.id, 'cmbProducts')).setValue("", 0);
        document.getElementById("lblihsproduct2").style.color = '';
        document.getElementById('lblPackage2').style.color = "";
        document.getElementById("lblihsproduct2").style.color = '';
        document.getElementById("lblduty2").style.color = '';
        $("#spnMSL2").text("");
        $("#spnManufacturer1").text("");
        $("#spnIHSProduct2").text("");
        $("#spnPackage2").text("");
        $("#spnHTSCode2").text("");
        $("#spnIHSDutyCode2").text("");
        $("#spnIHSCountryOfOrigin2").text("");
        document.getElementById("lblduty2").style.color = '';
        document.getElementById("lblpartstaus2").style.color = '';
        document.getElementById("lblCoo2").style.color = '';
        document.getElementById("lblihsHTSCode2").style.color = '';

        document.getElementById("spnMSL2").style.color = ''
        document.getElementById("spnIHSProduct2").style.color = '';
        document.getElementById("spnPackage2").style.color = ''
        document.getElementById("spnHTSCode2").style.color = ''
        document.getElementById("spnIHSDutyCode2").style.color = ''
        document.getElementById("spnIHSCountryOfOrigin2").style.color = ''
        document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_LableECCNCode").style.color = '';
        document.getElementById("lblECCNCode2").style.color = '';
        document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_LableLifeStatus").style.color = ''
        $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_LableECCNCode").text("");
        //lblCoo
        //lblihsHTSCode
        this._hidPackaging = "";
        this._hidPackagingSize = "";
        this._hidCountryOfOrigin = "";
        this._hidCountryOfOriginNo = ""
        this._hidLifeCycleStage = "";
        this._hidHTSCode = "";
        this._hidAveragePrice = "";
        this._hidDescriptions = "";
        this._IHSPartsId = "";
        this._ihsCurrencyCode = "";
        this._hidIHSProduct = "";
        //$("#lblihsproduct").hide();
        //$("#lblihsHTSCode").hide();
        //$("#lblduty").hide();
        //$("#lblCoo").hide();
        //$("#lblECCNCode").hide();
        this._hidECCNCode = "";
        $find(this.getFormControlID(this._element.id, 'cmbPackage')).setValue("", 0);
        //this.onSaveComplete();

    },

    //Part Eccn Mapping strat code
    SelectIHSPartEccnMappedFormLoad: function (IHSEccnCode, PartName) {
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/CusReqAdd");
        obj.set_DataObject("CusReqAdd");
        obj.set_DataAction("GetIHSPartEccnDetail");
        obj.addParameter("PartEccnCode", IHSEccnCode);
        obj.addParameter("PartNo", PartName);
        obj.addDataOK(Function.createDelegate(this, this.setIHSPartEccnDetailFormLoad));
        obj.addError(Function.createDelegate(this, this.IHSPartECCNDetailErrorFormLoad));
        obj.addTimeout(Function.createDelegate(this, this.IHSPartECCNDetailErrorFormLoad));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;


    },
    setIHSPartEccnDetailFormLoad: function (args) {
        var obj = args._result.Result
        if (obj[0].PartEccnMappedId > 0) {
            //ECCN Code start
            if (obj[0].ECCNCode != 'undefined' && obj[0].ECCNCode != '' && obj[0].ECCNCode.length > 0) {

                $find(this.getFormControlID(this._element.id, 'cmbPartEccnMapped')).setValue(obj[0].ECCNNo, $R_FN.setCleanTextValue(obj[0].ECCNCode));
                this._hidECCNCode = obj[0].ECCNCode;

                $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_cmbPartEccnMappedaut").show();
                $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_cmbPartEccnMappedtxt").show();
                document.getElementById("lblECCNCode2").style.color = 'yellow'

            }


        }
        else if (this._ECCNCode != '') {

            this.SelectIHSEccnCodeFormLoad(this._ECCNCode);
            $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_cmbPartEccnMappedaut").show();
            $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_cmbPartEccnMappedtxt").show();

        }
        else {
            // alert(3);
            $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_LableECCNCode").text(" ( " + "N/A" + " ) ");
            document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_LableECCNCode").style.color = 'yellow'
            document.getElementById("lblECCNCode2").style.color = ''
            this._hidECCNCode = "";
            $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_cmbPartEccnMappedaut").show();
            $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_cmbPartEccnMappedtxt").show();
        }

    },
    IHSPartECCNDetailErrorFormLoad: function (args) {
    },
    //code end


    //Bind Eccn Code from TbECCN Table strat code
    SelectIHSEccnCodeFormLoad: function (sEccnCode) {
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/CusReqAdd");
        obj.set_DataObject("CusReqAdd");
        obj.set_DataAction("GetIHSEccnCodeDetail");
        obj.addParameter("ECCNCode", sEccnCode);
        obj.addDataOK(Function.createDelegate(this, this.setIHSEccnCodeDetailFormLoad));
        obj.addError(Function.createDelegate(this, this.IHSECCNCodeDetailErrorFormLoad));
        obj.addTimeout(Function.createDelegate(this, this.IHSECCNCodeDetailErrorFormLoad));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;


    },
    setIHSEccnCodeDetailFormLoad: function (args) {

        var obj = args._result.Result
        if (obj[0].ECCNNo > 0) {
            //ECCN Code start
            if (obj[0].ECCNCode != '') {

                $find(this.getFormControlID(this._element.id, 'cmbPartEccnMapped')).setValue(obj[0].ECCNNo, $R_FN.setCleanTextValue(obj[0].ECCNCode));
                document.getElementById("lblECCNCode2").style.color = 'yellow'

                this._hidECCNCode = obj[0].ECCNCode;
                $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_cmbPartEccnMappedaut").show();
                $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_cmbPartEccnMappedtxt").show();
            }
            else {
                $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_LableECCNCode").text(" ( " + "N/A" + " ) ");
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_LableECCNCode").style.color = 'yellow'
                document.getElementById("lblECCNCode2").style.color = ''
                this._hidECCNCode = "";
                $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_cmbPartEccnMappedaut").show();
                $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_cmbPartEccnMappedtxt").show();

            }
        }
        else {
            //$find('ctl00_cphMain_ctlNotify_ctlDB_ctl14_ctlNotify_ctlDB_ctlSalespersion_ctl03_aut').reselect();
            $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_LableECCNCode").text(" ( " + "N/A" + " ) ");
            document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_LableECCNCode").style.color = 'yellow'
            document.getElementById("lblECCNCode2").style.color = ''
            this._hidECCNCode = "";
            $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_cmbPartEccnMappedaut").show();
            $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_cmbPartEccnMappedtxt").show();
        }

    },
    IHSECCNCodeDetailErrorFormLoad: function (args) {
    },
    //code end



    ClearIhsDataOnLoad: function () {
        document.getElementById("lblMatchproduct2").style.color = ''
        document.getElementById("lblparts2").style.color = '';
        document.getElementById("lblmls2").style.color = '';
        document.getElementById("lblmrf2").style.color = '';
        document.getElementById('lblPackage2').style.color = "";
        $("#spnMSL2").text("");
        $("#spnManufacturer1").text("");
        $("#spnIHSProduct2").text("");
        $("#spnPackage2").text("");

    },
    changeSearchType: function () {
        this.setSearchType(this._enmSearchType + 1);
    },

    setSearchType: function (i) {
        this._enmSearchType = i;
        if (this._enmSearchType > 2) this._enmSearchType = 0;
        this.showSearchType();
    },

    showSearchType: function () {
        if (this._enmSearchType == 0) {
            $("#ParttypeSearch2").removeClass("searchType_StartsWith");
            $("#ParttypeSearch2").removeClass("searchType_ExactWith");
            $("#ParttypeSearch2").addClass("searchType_Contains");
            $("#ParttypeSearch2").attr("title", "Contains");
            $("#ParttypeSearch2").attr("alt", "Contains");
            $find(this.getFormControlID(this._element.id, 'cmbIHS'))._aut._txtGroup = "contains";

        }
        if (this._enmSearchType == 1) {
            $("#ParttypeSearch2").removeClass("searchType_Contains");
            $("#ParttypeSearch2").removeClass("searchType_ExactWith");
            $("#ParttypeSearch2").addClass("searchType_StartsWith");
            $("#ParttypeSearch2").attr("title", "Startswith");
            $("#ParttypeSearch2").attr("alt", "Startswith");
            $find(this.getFormControlID(this._element.id, 'cmbIHS'))._aut._txtGroup = "startswith";

        }


        if (this._enmSearchType == 2) {
            $("#ParttypeSearch2").removeClass("searchType_StartsWith");
            $("#ParttypeSearch2").removeClass("searchType_Contains");
            $("#ParttypeSearch2").addClass("searchType_ExactWith");
            $("#ParttypeSearch2").attr("title", "Exact");
            $("#ParttypeSearch2").attr("alt", "Exact");
            $find(this.getFormControlID(this._element.id, 'cmbIHS'))._aut._txtGroup = "exact";

        }


    },
    ResetMFR: function () {
        $("#spanmfrClone").text("");
    },
    RsMfrChanged: function () {
        this._intManufacturerNo = this.getControlValue(this.getFormControlID(this._element.id, 'cmbManufacturer'), 'Combo')
        this.getRsMfr(this._intManufacturerNo);

    },
    getRsMfr: function (RsManufacturerNo) {
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/CusReqAdd");
        obj.set_DataObject("CusReqAdd");
        obj.set_DataAction("GetRestrictedManufacturer");
        obj.addParameter("RsManufacturerNo", RsManufacturerNo);
        obj.addDataOK(Function.createDelegate(this, this.getRsMfrOK));
        obj.addError(Function.createDelegate(this, this.getRsMfrError));
        obj.addTimeout(Function.createDelegate(this, this.getRsMfrError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },
    getRsMfrOK: function (args) {
        var res = args._result;
        this._RestrictedMFRMessage = res.RestrictedMFRMessage;
        this._isRestrictedManufacturer = res.isRestrictedManufacturer;
        if (this._isRestrictedManufacturer > 0 && this._isRestrictedManufacturer != "undefined") {
            $("#lblRsMFRClone").show();
            $("#spanmfrClone").text(this._RestrictedMFRMessage);

        } else { $("#lblRsMFRClone").hide(); }


    },
    getRsMfrError: function () {
        //this.setFieldValue($find(this.getFormControlID(this._element.id, 'cmbManufacturer')), "");
        // this.showShipViaFieldsLoading(false);
    },
    SearchTypePopup: function () {
        $('input:radio[name="searchType"][value="contains"]').prop('checked', true);
        $R_FN.showElement(this._pnlPartDetail, false);
        this._ctltblPartdetails._tblResults.clearTable();
        $("#myModal2").show();
        var strPart = $R_FN.setCleanTextValue($get(this.getFormControlID(this._element.id, 'cmbIHS12txt')).value);
        $get(this.getFormControlID(this._element.id, 'SearchtxtPartNo2')).value = strPart;
        //this.loadIHSGrid();
        this.Toggle1();

        document.getElementsByClassName("dataFilter")[1].style.display = "none";
        if (document.getElementsByClassName("itemSearchGo")[1])
            document.getElementsByClassName("itemSearchGo")[1].style.display = "none";

        // $R_FN.showElement(document.getElementById("ctl00_cphMain_ctlAdd_ctlDB_ctl14_ctlAdd_ctlDB_btnOK_hyp"), false);
        $R_FN.showElement(document.getElementById("okbtn"), false);
        $('.okbtn').hide();


    },
    loadIHSGrid: function () {
        $("#lblihserror2").text("");
        this._ctltblPartdetails._tblResults.clearTable();
        this._ctltblPartdetails._tblResults.resizeColumns();
        this._ctltblPartdetails.showNoneFound(true);
        this._ctltblPartdetails.resetToFirstPage();
        $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_ctltblPartdetails_ctlDB_tblOuter").hide();
        $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_ctltblPartdetails_ctlDB_ctl03_hyp").hide();

        this._ctltblPartdetails.setFieldValue("ctlSearchtxtPartNo", $R_FN.setCleanTextValue($get(this.getFormControlID(this._element.id, 'SearchtxtPartNo2')).value));
        this._ctltblPartdetails._strpart = $R_FN.setCleanTextValue($get(this.getFormControlID(this._element.id, 'SearchtxtPartNo2')).value);
        this._ctltblPartdetails._searchType = $("input[name='searchType']:checked").val();
        this._ctltblPartdetails.getData();
        //$R_FN.showElement(document.getElementById("ctl00_cphMain_ctlAdd_ctlDB_ctl14_ctlAdd_ctlDB_btnOK_hyp"), true);
        //$('.okbtn').show();

    },
    Canceltop: function () {
        $("#myModal2").hide();
        this.Toggle3();
    },
    Cancelbottom: function () {
        $("#myModal2").hide();
        this.Toggle3();

    },
    hidpartdetaildive: function () {
        this.showDetailDiv(false);


    },

    Toggle3: function () {
        // this._tblPartdetails.clearTable();
        // this.showField("ctlPartDetail", false);
        //this.showDetailDiv(false);

    },
    dispose: function () {
        if (this.isDisposed) return;
        this._intCustomerRequirementID = null;
        this._arySources = null;
        this._radFactorySealedSource = null;
        this._radObsolete = null;
        this._radLastTimeBuy = null;
        this._radRefirbsAcceptable = null;
        this._radTestingRequired = null;
        this._blnCurInSameFaimly = true;
        this._intCurrencyNo = null;
        this._radRepeatBusiness = null;
        this._radAlternativesAccepted = null;
        this._tblPartdetails = null;
        this._btn1 = null;
        this._btn2 = null;
        this._lblError = null;
        this._hidCountryOfOrigin = null;
        this._hidCountryOfOriginNo = null;
        this._hidLifeCycleStage = null;
        this._hidHTSCode = null;
        this._hidAveragePrice = null;
        this._hidPackaging = null;
        this._hidPackagingSize = null;
        this._hidDescriptions = null;
        this._pnlPartDetail = null;
        this._hidBOMID = null;
        this._IHSProductNo = null;
        this._IHSProduct = null;
        this._HTSCode = null;
        this._CountryOfOrigin = null;
        this._IHSDutyCode = null;
        this._AlternateStatus = null;
        this.chkAlternatives = null;
        this._ECCNCode = null;
        if (this._ctlMail) this._ctlMail.dispose();
        Rebound.GlobalTrader.Site.Controls.Forms.CusReqMainInfo_EditCloneHUB.callBaseMethod(this, "dispose");
    },
    findWhichTypeSelected: function (radChk) {
        for (var i = 0; i < this._arySources.length; i++) {
            var rad = $get(String.format("{0}_{1}", radChk.id, i));

            if (rad.checked) {
                return this._arySources[i];
            }
            else if (i == this._arySources.length - 1) {
                return "false";
            }

        }
    },
    Toggle1: function () {
        $("#lblIhsServiceMessage2").text("");
        $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_ctltblPartdetails_ctlDB_ctl08").hide();
        if ($("input[name='searchType']:checked").val() == "exact".trim()) {
            var strexact = $get(this.getFormControlID(this._element.id, 'SearchtxtPartNo2')).value;
            if (strexact.length > 2) {
                $("#lblihserror2").text("");
                $('#divLoader2').show();
                $R_FN.showElement(this._pnlPartDetail, true);
                this.showProductLoading(true);
                var obj = new Rebound.GlobalTrader.Site.Data();
                obj.set_PathToData("controls/Nuggets/CusReqAdd");
                obj.set_DataObject("CusReqAdd");
                obj.set_DataAction("GetDataGrid");
                obj.addParameter("partsearch", $get(this.getFormControlID(this._element.id, 'SearchtxtPartNo2')).value);
                obj.addParameter("searchType", $("input[name='searchType']:checked").val());
                obj.addDataOK(Function.createDelegate(this, this.getDataGrid));
                obj.addError(Function.createDelegate(this, this.getDataGridError));
                obj.addTimeout(Function.createDelegate(this, this.getDataGridError));
                $R_DQ.addToQueue(obj);
                $R_DQ.processQueue();
                obj = null;

            }
            else {
                $("#lblihserror2").text("The search term must contain at least 3 alphanumeric characters. Examples are '2n222', 'F245', etc.");
                $R_FN.showElement(this._lblError, true);
                $R_FN.showElement(this._pnlPartDetail, false);
                this._ctltblPartdetails._tblResults.clearTable();
                this._ctltblPartdetails._tblResults.resizeColumns();
                this._ctltblPartdetails.showNoneFound(true);
                return;

            }
        }
        if ($("input[name='searchType']:checked").val() == "startswith".trim()) {
            var strexact = $get(this.getFormControlID(this._element.id, 'SearchtxtPartNo2')).value;
            if (strexact.length > 2) {
                $("#lblihserror2").text("");
                $('#divLoader2').show();
                $R_FN.showElement(this._pnlPartDetail, true);
                this.showProductLoading(true);
                var obj = new Rebound.GlobalTrader.Site.Data();
                obj.set_PathToData("controls/Nuggets/CusReqAdd");
                obj.set_DataObject("CusReqAdd");
                obj.set_DataAction("GetDataGrid");
                obj.addParameter("partsearch", $get(this.getFormControlID(this._element.id, 'SearchtxtPartNo2')).value);
                obj.addParameter("searchType", $("input[name='searchType']:checked").val());
                obj.addDataOK(Function.createDelegate(this, this.getDataGrid));
                obj.addError(Function.createDelegate(this, this.getDataGridError));
                obj.addTimeout(Function.createDelegate(this, this.getDataGridError));
                $R_DQ.addToQueue(obj);
                $R_DQ.processQueue();
                obj = null;

            }
            else {
                $("#lblihserror2").text("The search term must contain at least 3 alphanumeric characters. Examples are '2n222', 'F245', etc.");
                $R_FN.showElement(this._lblError, true);
                $R_FN.showElement(this._pnlPartDetail, false);
                this._ctltblPartdetails._tblResults.clearTable();
                this._ctltblPartdetails._tblResults.resizeColumns();
                this._ctltblPartdetails.showNoneFound(true);
                return;

            }
        }
        if ($("input[name='searchType']:checked").val() == "contains".trim()) {
            var strexact = $get(this.getFormControlID(this._element.id, 'SearchtxtPartNo2')).value;
            if (strexact.length > 3) {
                $("#lblihserror2").text("");
                $('#divLoader2').show();
                $R_FN.showElement(this._pnlPartDetail, true);
                this.showProductLoading(true);
                var obj = new Rebound.GlobalTrader.Site.Data();
                obj.set_PathToData("controls/Nuggets/CusReqAdd");
                obj.set_DataObject("CusReqAdd");
                obj.set_DataAction("GetDataGrid");
                obj.addParameter("partsearch", $get(this.getFormControlID(this._element.id, 'SearchtxtPartNo2')).value);
                obj.addParameter("searchType", $("input[name='searchType']:checked").val());
                obj.addDataOK(Function.createDelegate(this, this.getDataGrid));
                obj.addError(Function.createDelegate(this, this.getDataGridError));
                obj.addTimeout(Function.createDelegate(this, this.getDataGridError));
                $R_DQ.addToQueue(obj);
                $R_DQ.processQueue();
                obj = null;

            }
            else {
                $("#lblihserror2").text("The search term must contain at least 4 alphanumeric characters. Examples are '2n222', 'F245', etc.");
                $R_FN.showElement(this._lblError, true);
                $R_FN.showElement(this._pnlPartDetail, false);
                this._ctltblPartdetails._tblResults.clearTable();
                this._ctltblPartdetails._tblResults.resizeColumns();
                this._ctltblPartdetails.showNoneFound(true);
                return;

            }
        }

    },

    getIHSDataSelected: function () {
        $('.okbtn').show();
    },
    getValuesByPartsOnClick: function () {
        var PartNo = this._ctltblPartdetails._tblResults._varSelectedValue;
        if (PartNo.length > 0) {
            this.getValuesByParts();

        }
        // this.Canceltop();
    },
    getValuesByParts: function () {
        var obj = this._ctltblPartdetails._tblResults.getSelectedExtraData(); if (!obj) return;
        this.getIHSValuesByParts(obj.IHSPartsId, obj.ROHSName, obj.ROHSNo, obj.Manufacturer, obj.IHSProdDesc, obj.Packaging, obj.HTSCode, obj.IHSDutyCode, obj.CountryOfOrigin, obj.CountryOfOriginNo, obj.PackagingSize);

    },
    getIHSValuesByParts: function (IHSPartsId, MSLName, MSLNo, Manufacturer, IHSProdDesc, Packaging, HTSCode, IHSDutyCode, CountryOfOrigin, CountryOfOriginNo, PackagingSize) {
        $('#divLoader2').show();
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/CusReqAdd");
        obj.set_DataObject("CusReqAdd");
        obj.set_DataAction("GetIHSPartDetails");
        obj.addParameter("IHSPartsId", IHSPartsId);
        obj.addParameter("MSLName", MSLName);
        obj.addParameter("MSLNo", MSLNo);
        obj.addParameter("Manufacturer", Manufacturer);
        obj.addParameter("IHSProdDesc", IHSProdDesc);
        obj.addParameter("Packaging", Packaging);
        obj.addParameter("HTSCode", HTSCode);
        obj.addParameter("IHSDutyCode", IHSDutyCode);
        obj.addParameter("CountryOfOrigin", CountryOfOrigin);
        obj.addParameter("CountryOfOriginNo", CountryOfOriginNo);
        obj.addParameter("PackagingSize", PackagingSize);
        obj.addDataOK(Function.createDelegate(this, this.getIHSDataGrid));
        obj.addError(Function.createDelegate(this, this.getIHSDataError));
        obj.addTimeout(Function.createDelegate(this, this.getIHSDataGridError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },
    getIHSDataError: function (args) {
        $('#divLoader2').hide();
    },
    getIHSDataGrid: function (args) {
        var obj = args._result.Result
        if (obj[0].IHSPartsId > 0) {

            $("#lblihsproduct2").show();
            $("#lblihsHTSCode2").show();
            $("#lblduty2").show();
            $("#lblCoo2").show();

            var PartName = obj[0].ID;
            if (PartName != 'undefined ' && PartName.length > 0) {
                // $get(this.getFormControlID(this._element.id, 'txtPartNo')).value = $R_FN.setCleanTextValue(PartName);
                $get(this.getFormControlID(this._element.id, 'cmbIHS12txt')).value = $R_FN.setCleanTextValue(PartName);
                document.getElementById("lblparts2").style.color = 'yellow'
            }
            else {
                document.getElementById("lblparts2").style.color = ''
            }
            if (obj[0].ROHSName != 'undefined ' && obj[0].ROHSName != '' && obj[0].ROHSName.length > 0) {
                // $("#spnMSL").show();
                $("#spnMSL2").text(" ( " + obj[0].ROHSName + " ) ");
                $find(this.getFormControlID(this._element.id, 'ddlMsl')).setValue(obj[0].ROHSName);
                document.getElementById("lblmls2").style.color = 'yellow'
                if (obj[0].ROHSName == 'N/A')
                    document.getElementById("spnMSL2").style.color = 'yellow';
                else
                    document.getElementById("spnMSL2").style.color = '';

            }
            else {

                $("#spnMSL2").text(" ( " + "N/A" + " ) ");
                document.getElementById("lblmls2").style.color = 'yellow'
                document.getElementById("spnMSL2").style.color = 'yellow'
                $find(this.getFormControlID(this._element.id, 'ddlMsl')).setValue("N/A");


            }


            if (obj[0].Manufacturer != 'undefined ' && obj[0].Manufacturer != '' && obj[0].Manufacturer.length > 0) {
                if (obj[0].ManufacturerNo != 0) {
                    $find(this.getFormControlID(this._element.id, 'cmbManufacturer')).setValue(obj[0].ManufacturerNo, $R_FN.setCleanTextValue(obj[0].Manufacturer));
                    $("#spnManufacturer").hide();
                    document.getElementById("lblmrf2").style.color = 'yellow'

                }
                else {

                    $find(this.getFormControlID(this._element.id, 'cmbManufacturer')).setValue("", 0);
                    $("#spnManufacturer").show();
                    $("#spnManufacturer").text(" ( " + obj[0].ManufacturerFullName + " ) ");
                    document.getElementById("lblmrf2").style.color = ''
                }

            }
            else {

                $("#spnManufacturer").hide();

            }

            if (obj[0].ProdDesc != 'undefined ' && obj[0].IHSProdDesc.length > 0) {

                if (obj[0].ProdNo != 0) {
                    $find(this.getFormControlID(this._element.id, 'cmbProducts')).setValue(obj[0].ProdNo, $R_FN.setCleanTextValue(obj[0].ProdDesc));
                    // $("#spnManufacturer").hide();
                    document.getElementById("lblMatchproduct2").style.color = 'yellow'
                    $("#spnIHSProduct2").text($R_FN.setCleanTextValue(obj[0].IHSProdDesc));
                    document.getElementById("lblihsproduct2").style.color = 'yellow'
                }
                else {
                    $find(this.getFormControlID(this._element.id, 'cmbProducts')).setValue("", 0);
                    $("#spnIHSProduct2").show();
                    $("#spnIHSProduct2").text($R_FN.setCleanTextValue(obj[0].IHSProdDesc));
                    document.getElementById("lblihsproduct2").style.color = 'yellow'
                }

            }
            else {
                if (obj[0].HTSCode != '' || obj[0].IHSProdDes != '') {
                    $("#spnIHSProduct2").show();
                    $("#spnIHSProduct2").text(" ( " + "N/A" + " ) ");
                    document.getElementById("spnIHSProduct2").style.color = 'yellow'
                    document.getElementById("lblihsproduct2").style.color = 'yellow'
                } else {
                    document.getElementById("lblihsproduct2").style.color = ''
                    document.getElementById("spnIHSProduct2").style.color = ''
                }
            }

            //if (obj[0].PackageId > 0) {

            //    //$("#spnPackaging").show();
            //    //$("#spnPackaging").text(" (" + obj[0].Packaging + " ) ");
            //   // $find(this.getFormControlID(this._element.id, 'ddlPackage')).setValue(obj[0].PackageId);
            //    //document.getElementById("lblpackage").style.color = 'yellow'
            //    $("#spnPackaging").text(" ( " + obj[0].Packaging + " ) ");
            //   // document.getElementById("spnPackaging").style.color = ''
            //}
            //else {
            //    // $("#spnPackaging").hide();
            //      $("#spnPackaging").text(" ( " + "N/A" + " ) ");
            //     // document.getElementById("spnPackaging").style.color = 'yellow'
            //   // document.getElementById("lblpackage").style.color = ''
            //    //$find(this.getFormControlID(this._element.id, 'ddlPackage')).setValue(0);

            //}

            if (obj[0].PackageId > 0) {
                $("#lblECCNCode2").show();
                $("#spnPackage2").text(" ( " + obj[0].Packaging + " ) ");
                document.getElementById('lblPackage2').style.color = "yellow";
                document.getElementById("spnPackage2").style.color = "";
                $find(this.getFormControlID(this._element.id, 'cmbPackage')).setValue(obj[0].PackageId, $R_FN.setCleanTextValue(obj[0].PackageDescription));

            }
            else {
                $("#lblECCNCode2").show();
                $("#spnPackage2").text(" ( " + "N/A" + " ) ");
                document.getElementById('lblPackage2').style.color = "";
                document.getElementById("spnPackage2").style.color = 'yellow'
                $find(this.getFormControlID(this._element.id, 'cmbPackage')).setValue("", 0);

            }


            if (obj[0].HTSCode != 'undefined ' && obj[0].HTSCode != '' && obj[0].HTSCode.length > 0) {
                $("#spnHTSCode2").show();
                $("#spnHTSCode2").text(obj[0].HTSCode);
                document.getElementById("lblihsHTSCode2").style.color = 'yellow'
                document.getElementById("spnHTSCode2").style.color = ''
            }
            else {
                if (obj[0].HTSCode != '' || obj[0].IHSProdDes != '') {
                    $("#spnHTSCode2").show();
                    $("#spnHTSCode2").text(" ( " + "N/A" + " ) ");
                    document.getElementById("lblihsHTSCode2").style.color = 'yellow'
                    document.getElementById("spnHTSCode2").style.color = 'yellow'
                } else {
                    document.getElementById("lblihsHTSCode2").style.color = ''
                    document.getElementById("spnHTSCode2").style.color = ''
                }
            }

            //if (obj.ProdDesc != 'undefined ' && obj.IHSProdDesc.length > 0) {
            //    $("#spnIHSProduct").show();
            //    //$("#spnIHSProduct").text("(" + obj.ProdDesc + " ) ");
            //    $("#spnIHSProduct").text(obj.IHSProdDesc);
            //}
            //else {
            //    $find(this.getFormControlID(this._element.id, 'cmbProducts')).setValue(obj.ProdNo, obj.ProdDesc);
            //    $("#spnIHSProduct").hide();
            //}
            if (obj[0].IHSDutyCode != 'undefined ' && obj[0].IHSDutyCode != '' && obj[0].IHSDutyCode.length > 0) {
                $("#spnIHSDutyCode2").show();
                $("#spnIHSDutyCode2").text(" ( " + obj[0].IHSDutyCode + " ) ");
                document.getElementById("lblduty2").style.color = 'yellow'
                document.getElementById("spnIHSDutyCode2").style.color = ''

            }
            else {

                $("#spnIHSDutyCode2").text("( N/A )")
                document.getElementById("spnIHSDutyCode2").style.color = 'yellow'
                document.getElementById("lblduty2").style.color = ''
            }
            if (obj[0].CountryOfOrigin != 'undefined ' && obj[0].CountryOfOrigin != '' && obj[0].CountryOfOrigin.length > 0) {
                $("#spnIHSCountryOfOrigin2").show();
                $("#spnIHSCountryOfOrigin2").text(obj[0].CountryOfOrigin);
                document.getElementById("lblCoo2").style.color = 'yellow'
            }
            else {

                $("#spnIHSCountryOfOrigin2").text(" ( " + "N/A" + " ) ");
                document.getElementById("lblCoo2").style.color = ''
                document.getElementById("spnIHSCountryOfOrigin2").style.color = 'yellow'
            }

            if (obj[0].PartStatus != 'undefined ' && obj[0].PartStatus != '' && obj[0].PartStatus.length > 0) {
                $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_LableLifeStatus").text($R_FN.setCleanTextValue(obj[0].PartStatus));
                document.getElementById("lblpartstaus2").style.color = 'yellow'
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_LableLifeStatus").style.color = ''
            }
            else {
                $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_LableLifeStatus").text(" ( " + "N/A" + " ) ");
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_LableLifeStatus").style.color = 'yellow'
                document.getElementById("lblpartstaus2").style.color = ''
            }
            //ECCN Code start
            if (obj[0].ECCNCode != 'undefined ' && obj[0].ECCNCode != '' && obj[0].ECCNCode.length > 0) {
                $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_LableECCNCode").text($R_FN.setCleanTextValue(obj[0].ECCNCode));
                document.getElementById("lblECCNCode2").style.color = 'yellow'
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_LableECCNCode").style.color = ''
            }
            else {
                $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_LableECCNCode").text(" ( " + "N/A" + " ) ");
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_LableECCNCode").style.color = 'yellow'
                document.getElementById("lblECCNCode2").style.color = ''
            }
            //ECCN Code End

            if (obj[0].Packaging != 'undefined ' && obj[0].Packaging != '') {
                this._hidPackaging = obj[0].Packaging;
            }
            else {
                this._hidPackaging = "";
            }
            if (obj[0].PackagingSize != 'undefined ' && obj[0].PackagingSize != '') {
                this._hidPackagingSize = obj[0].PackagingSize;
            }
            else {
                this._hidPackagingSize = "";
            }


            this._hidCountryOfOrigin = obj[0].CountryOfOrigin;
            this._hidCountryOfOriginNo = obj[0].CountryOfOriginNo;
            this._hidLifeCycleStage = obj[0].PartStatus;
            this._hidHTSCode = obj[0].HTSCode;
            this._hidAveragePrice = obj[0].AveragePrice;
            this._hidDescriptions = obj[0].Descriptions;
            this._IHSPartsId = obj[0].IHSPartsId;
            this._ihsCurrencyCode = obj[0].ihsCurrencyCode;
            this._hidIHSProduct = obj[0].IHSProduct;
            this._hidECCNCode = obj[0].ECCNCode;

        }
        $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_cmbIHS12aut_hypClose")[0].click();
        $('#divLoader2').hide();
        $("#myModal2").hide();
    },
    getIHSDataGridError: function (args) {
        $('#divLoader2').hide();
        $("#myModal2").hide();
    },
    getDataGridError: function (args) {
        $('#divLoader2').hide();
        $("#myModal2").hide();
    },
    getDataGrid: function (args) {

        $('#divLoader2').hide();
        var ihsmsg = args._result.ServiceStatus;
        if (ihsmsg == false) {
            $("#lblIhsServiceMessage2").text("Sorry, the IHS part lookup service is not currently available.");
            $R_FN.showElement(this._lblError, true);
            $R_FN.showElement(this._pnlPartDetail, false);
            this._ctltblPartdetails._tblResults.clearTable();
            this._ctltblPartdetails._tblResults.resizeColumns();
        }
        else {
            if (args._result.Result) {
                this.loadIHSGrid();
                $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_ctltblPartdetails_ctlDB_ctl08").show();
            }
            else {
                this.loadIHSGrid();
            }
        }
    },
    Toggle2: function () {
        $("#lblIhsServiceMessage2").text("");
        $("#lblservicemsgerror2").text("");
        $('input:radio[name="searchType"][value="contains"]').prop('checked', true);
        $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_ctltblPartdetails_ctlDB_ctl08").hide();
        $get(this.getFormControlID(this._element.id, 'SearchtxtPartNo2')).value = "";
        $("#lblihserror2").text("");
        $R_FN.showElement(this._pnlPartDetail, false);
        this._ctltblPartdetails._tblResults.clearTable();
        this._ctltblPartdetails._tblResults.resizeColumns();
        this._ctltblPartdetails.showNoneFound(true);

    },
    getParSearch: function () {

    },

    getPartDetaildata: function () {
        //if (this._ctlMultiStep._intCurrentStep == 2) {
        if (this._PartEditStatus == 1) {
            if ($get(this.getFormControlID(this._element.id, 'cmbIHS12txt')).value.length > 0) {
                this.getPartDetail($get(this.getFormControlID(this._element.id, 'cmbIHS12txt')).value);
                this.showDetailDiv(true);
            }
            else {
                if (!this.validateForm2()) return;
                this.showDetailDiv(false);
            }
        }
        else {
            if ($("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_lblPartNo").text().length > 0) {
                this.getPartDetail($("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_lblPartNo").text());
                this.showDetailDiv(true);
            }
            else {
                if (!this.validateForm2()) return;
                this.showDetailDiv(false);
            }
        }
        //}

    },
    validateForm2: function () {
        this.onValidate();
        var blnOK = true;
        //if (!this.checkControlAddEntered(this.getFormControlID(this._element.id, 'txtPartNo'), 'TextBox')) blnOK = false;
        if (this._PartEditStatus == 1) {
            if (!this.checkControlEditEntered(this.getFormControlID(this._element.id, 'cmbIHS12txt'), 'TextBox')) blnOK = false;
        }
        //if (!this.checkControlAddEntered(this.getFormControlID(this._element.id, 'cmbIHS'), 'Combo')) blnOK = false;
        if (!blnOK) this.showError(true);
        return blnOK;
    },
   
    showDetailDiv: function (visible) {
        if (visible) {
            $("#mydiv2").show();
            $("#tbpartdet2").show();
        }
        else {
            $("#mydiv2").hide();
            $("#tbpartdet2").hide();
        }
    },
    saveClicked: function () {
        if (!this.validateForm()) return;
        if (this._isRestrictedManufacturer > 0 && this._isRestrictedManufacturer != "undefined") {
            this.showError(true, this._RestrictedMFRMessage);

        }
        else {
            var obj = new Rebound.GlobalTrader.Site.Data();
            obj.set_PathToData("controls/Nuggets/CusReqMainInfo");
            obj.set_DataObject("CusReqMainInfo");
            obj.set_DataAction("CloneRequirementDataHUB");
            obj.addParameter("CustReqNo", this._intCustomerRequirementID);
            obj.addParameter("CMNo", this._intCompanyID)
            obj.addParameter("Name", $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_lblCustomer").text());
            obj.addParameter("ContactNo", 0);
            obj.addParameter("Quantity", $get(this.getFormControlID(this._element.id, 'txtQuantity')).value);
            //obj.addParameter("PartNo", $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_lblPartNo").text());
            if (this._PartEditStatus == 1) {
                obj.addParameter("PartNo", $get(this.getFormControlID(this._element.id, 'cmbIHS12txt')).value);
            }
            else {
                obj.addParameter("PartNo", $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_lblPartNo").text());

            }
            obj.addParameter("CustomerPart", $get(this.getFormControlID(this._element.id, 'txtCustomerPartNo')).value);
            obj.addParameter("Mfr", this.getControlValue(this.getFormControlID(this._element.id, 'cmbManufacturer'), 'Combo'));
            obj.addParameter("DateCD", $get(this.getFormControlID(this._element.id, 'txtDateCode')).value);
            obj.addParameter("Product", this.getControlValue(this.getFormControlID(this._element.id, 'cmbProducts'), 'Combo'));
            obj.addParameter("Package", this.getControlValue(this.getFormControlID(this._element.id, 'cmbPackage'), 'Combo'));
            obj.addParameter("Price", $get(this.getFormControlID(this._element.id, 'txtTargetPrice')).value);
            if (this._blnCurInSameFaimly == false) {
                obj.addParameter("CurrencyNo", this._intCurrencyNo);
            }
            else {
                obj.addParameter("CurrencyNo", $find(this.getFormControlID(this._element.id, 'ddlCurrency')).getValue());//this.getFieldValue("ctlCurrency"));
            }
            obj.addParameter("DateRequired", $get(this.getFormControlID(this._element.id, 'txtDateRequired')).value);
            obj.addParameter("Usage", $find(this.getFormControlID(this._element.id, 'ddlUsage')).getValue());
            obj.addParameter("Notes", $get(this.getFormControlID(this._element.id, 'txtNotes')).value);
            obj.addParameter("Instructions", $get(this.getFormControlID(this._element.id, 'txtInstructions')).value);
            obj.addParameter("ROHS", $find(this.getFormControlID(this._element.id, 'ddlROHS')).getValue());
            obj.addParameter("PartWatch", this.getControlValue(this.getFormControlID(this._element.id, 'chkPartWatch'), 'CheckBox'));
            obj.addParameter("BOM", this.getControlValue(this.getFormControlID(this._element.id, 'chkBOM'), 'CheckBox'));
            obj.addParameter("BOMName", $get(this.getFormControlID(this._element.id, 'txtBOMName')).value);
            obj.addParameter("BOMNo", $find(this.getFormControlID(this._element.id, 'ddlBOM')).getValue());
            obj.addParameter("FactorySealed", this.getControlValue(this.getFormControlID(this._element.id, 'chkFactorySealedSource'), 'CheckBox'));//this._RadChecked);
            obj.addParameter("MSL", $find(this.getFormControlID(this._element.id, 'ddlMsl')).getValue());
            obj.addParameter("PQA", this.getControlValue(this.getFormControlID(this._element.id, 'chkPQA'), 'CheckBox'));
            obj.addParameter("ObsoleteChk", this.getControlValue(this.getFormControlID(this._element.id, 'chkObsolete'), 'CheckBox'));//this._radObsoleteChk);
            obj.addParameter("LastTimeBuyChk", this.getControlValue(this.getFormControlID(this._element.id, 'chkLastTimeBuy'), 'CheckBox'));//this._radLastTimeBuyChk);
            obj.addParameter("RefirbsAcceptableChk", this.getControlValue(this.getFormControlID(this._element.id, 'chkRefirbsAcceptable'), 'CheckBox'));//this._radRefirbsAcceptableChk);
            obj.addParameter("TestingRequiredChk", this.getControlValue(this.getFormControlID(this._element.id, 'chkTestingRequired'), 'CheckBox'));//this._radTestingRequiredChk);
            obj.addParameter("TargetSellPrice", $get(this.getFormControlID(this._element.id, 'txtTargetSellPrice')).value);
            obj.addParameter("CompetitorBestOffer", $get(this.getFormControlID(this._element.id, 'txtCompetitorBestoffer')).value);
            obj.addParameter("CustomerDecisionDate", $get(this.getFormControlID(this._element.id, 'txtCustomerDecisionDate')).value);
            obj.addParameter("RFQClosingDate", $get(this.getFormControlID(this._element.id, 'txtRFQClosingDate')).value);
            obj.addParameter("QuoteValidityRequired", $find(this.getFormControlID(this._element.id, 'ddlQuoteValidityRequired')).getValue());
            obj.addParameter("Type", $find(this.getFormControlID(this._element.id, 'ddlType')).getValue());
            obj.addParameter("OrderToPlace", this.getControlValue(this.getFormControlID(this._element.id, 'chkOrderToPlace'), 'CheckBox'));
            obj.addParameter("RequirementforTraceability", $find(this.getFormControlID(this._element.id, 'ddlRequirementforTraceability')).getValue());
            obj.addParameter("EAU", $get(this.getFormControlID(this._element.id, 'txtEau')).value);
            obj.addParameter("AlternativesAccepted", this.getControlValue(this.getFormControlID(this._element.id, 'chkAlternativesAccepted'), 'CheckBox')); //this.getRadioSeclectedValue(this._radAlternativesAccepted));
            obj.addParameter("RepeatBusiness", this.getControlValue(this.getFormControlID(this._element.id, 'chkRepeatBusiness'), 'CheckBox'));//this.getRadioSeclectedValue(this._radRepeatBusiness));
            obj.addParameter("SalesPersion", this.getControlValue(this.getFormControlID(this._element.id, 'cmbSalespersion'), 'Combo'));
            obj.addParameter("SalesmanNo", $find(this.getFormControlID(this._element.id, 'ddlSalesman')).getValue());
            obj.addParameter("Alternatives", this.getControlValue(this.getFormControlID(this._element.id, 'chkAlternatives'), 'CheckBox'));

            //for  send to hub code start
            obj.addParameter("AssignUserNo", $find(this.getFormControlID(this._element.id, 'BOMPoHubBuyer')).getValue());
            obj.addParameter("DateQuoteRequired", $get(this.getFormControlID(this._element.id, 'txtQuoteRequired')).value);
            //[002] Start Code
            obj.addParameter("aryRecipientLoginIDs", $R_FN.arrayToSingleString(this._ctlMail._aryRecipientLoginIDs));
            //obj.addParameter("aryRecipientLoginIDs", $R_FN.arrayToSingleString(28));
            //[002] End Code
            obj.addParameter("Contact2No", $find(this.getFormControlID(this._element.id, 'ddlSalesmanContact2')).getValue());
            //cod end
            //ihs code end
            //ihs part edit code start
            obj.addParameter("CountryOfOrigin", this._hidCountryOfOrigin);
            obj.addParameter("CountryOfOriginNo", this._hidCountryOfOriginNo);
            obj.addParameter("LifeCycleStage", this._hidLifeCycleStage);
            obj.addParameter("HTSCode", this._hidHTSCode);
            obj.addParameter("AveragePrice", this._hidAveragePrice);
            obj.addParameter("Packaging", this._hidPackaging);
            obj.addParameter("PackagingSize", this._hidPackagingSize);
            obj.addParameter("Descriptions", this._hidDescriptions);
            obj.addParameter("IHSPartsId", this._IHSPartsId);
            obj.addParameter("ihsCurrencyCode", this._ihsCurrencyCode);
            obj.addParameter("IHSProduct", this._hidIHSProduct);
            //obj.addParameter("ECCNCode", this._hidECCNCode);
            obj.addParameter("ECCNCode", $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_cmbPartEccnMappedaut_ctl03").text());
            obj.addParameter("ECCNNo", this.getControlValue(this.getFormControlID(this._element.id, 'cmbPartEccnMapped'), 'Combo'));
            //code end
            obj.addDataOK(Function.createDelegate(this, this.saveEditCloneHUBComplete));
            obj.addError(Function.createDelegate(this, this.saveEditError));
            obj.addTimeout(Function.createDelegate(this, this.saveEditError));
            $R_DQ.addToQueue(obj);
            $R_DQ.processQueue();
            obj = null;
        }
    },

    saveEditError: function (args) {
        this.showSaving(false);
        this._strErrorMessage = args._errorMessage;
        this.onSaveError();
    },

    saveEditCloneHUBComplete: function (args) {
      
        var res = args._result;
        if (args._result.Result > 0) {
            this.showSavedOK(true);
            location.href = $RGT_gotoURL_CustomerRequirement(args._result.Result);
            
        } else {
            this.showError(true, args._result.ValidationMessage);
        }
    },

    getRadioSeclectedValue: function (radchk) {
        var value = false;
        var rad = $get(String.format("{0}_{1}", radchk.id, 0));
        //var rad1 = $get(String.format("{0}_{1}", radchk.id, 1));
        if (rad.checked) {
            value = true;
        }
        return value;

    },
   

    validateForm: function () {
        this.onValidate();
        var strQuantity = "";
        //var blnOK = this.autoValidateFields();
        var blnOK = true;
        if (this._PartEditStatus == 1) {
            if (!this.checkControlEditEntered(this.getFormControlID(this._element.id, 'cmbIHS12txt'), 'TextBox')) blnOK = false;
        }
        if (!this.checkControlEditEntered(this.getFormControlID(this._element.id, 'txtQuantity'), 'TextBox')) blnOK = false;
        if (!this.checkControlEditEntered(this.getFormControlID(this._element.id, 'cmbManufacturer'), 'Combo')) blnOK = false;
        if (!this.checkControlEditEntered(this.getFormControlID(this._element.id, 'cmbManufacturertxt'), 'TextBox')) blnOK = false;
        if (!this.checkControlEditEntered(this.getFormControlID(this._element.id, 'cmbProducts'), 'Combo')) blnOK = false;
        if (!this.checkControlEditEntered(this.getFormControlID(this._element.id, 'cmbProductstxt'), 'TextBox')) blnOK = false;
        if (this._blnCurInSameFaimly == false) {
            if (this._intCurrencyNo <= 0) blnOK = false;
        }
        else {
            if (!this.checkControlEditEntered(this.getFormControlID(this._element.id, 'ddlCurrency'), 'DropDown')) blnOK = false;
        }

        if (!this.checkControlEditEntered(this.getFormControlID(this._element.id, 'txtDateRequired'), 'TextBox')) blnOK = false;
        if (!this.checkControlEditEntered(this.getFormControlID(this._element.id, 'ddlRequirementforTraceability'), 'DropDown')) blnOK = false;
        if (!this.checkControlEditEntered(this.getFormControlID(this._element.id, 'ddlSalesman'), 'DropDown')) blnOK = false;
        if (!this.checkControlEditEntered(this.getFormControlID(this._element.id, 'ddlType'), 'DropDown')) blnOK = false;
        if (!this.checkControlEditEntered(this.getFormControlID(this._element.id, 'BOMPoHubBuyer'), 'DropDown')) blnOK = false;
        if (!this.checkControlEditEntered(this.getFormControlID(this._element.id, 'txtQuoteRequired'), 'TextBox')) blnOK = false;
        //if (!this.checkControlEditEntered(this.getFormControlID(this._element.id, 'ddlSalesmanContact2'), 'DropDown')) blnOK = false;
        if (!blnOK) this.showError(true);
        return blnOK;
    },

    //for Edit req validation
    checkControlEditEntered: function (strControlID, strControlType) {
        var blnEntered = true;
        switch (strControlType) {
            case "TextBox": blnEntered = $R_FN.isEntered($get(strControlID).value); break;
            case  "DropDown": blnEntered = !$find(strControlID).isSetAsNoValue(); break;
            case  "FileUpload": blnEntered = $find(strControlID).checkEntered(); break;
            case "Combo": blnEntered = $find(strControlID).checkEntered(); break;
        }
        if (!blnEntered) {
            this.setControleditInError(strControlID, true, $R_RES.RequiredFieldMissingMessage);
        }
        else {
            document.getElementById(strControlID).style.border = '';
            if (this._PartEditStatus == 1) {
                if (strControlID == "ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_cmbIHS12txt" && blnEntered == true) {
                    document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_parterror").style.backgroundColor = "";
                    document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_parterror1").style.backgroundColor = "";
                }
            }
            if (strControlID == "ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_txtQuantity" && blnEntered == true) {
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_QuantityError").style.backgroundColor = "";
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_QuantityError1").style.backgroundColor = "";
            }
            if (strControlID == "ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_txtTargetPrice" && blnEntered == true) {
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_CustomerTargetPriceError").style.backgroundColor = "";
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_CustomerTargetPriceError1").style.backgroundColor = "";
            }
            if (strControlID == "ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_cmbManufacturer" && blnEntered == true) {
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_ManufacturerError").style.backgroundColor = "";
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_ManufacturerError1").style.backgroundColor = "";
            }
            if (strControlID == "ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUBRFQ_ctlDB_cmbManufacturertxt" && blnEntered == true) {
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUBRFQ_ctlDB_ManufacturerError").style.backgroundColor = "";
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUBRFQ_ctlDB_ManufacturerError1").style.backgroundColor = "";
            }
            if (strControlID == "ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_cmbProducts" && blnEntered == true) {
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_ProductError").style.backgroundColor = "";
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_ProductError1").style.backgroundColor = "";
            }
            if (strControlID == "ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_cmbProductstxt" && blnEntered == true) {
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_ProductError").style.backgroundColor = "";
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_ProductError1").style.backgroundColor = "";
            }
            if (strControlID == "ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_ddlRequirementforTraceability" && blnEntered == true) {
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_TraceabilityError").style.backgroundColor = "";
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_TraceabilityError1").style.backgroundColor = "";
            }
            if (strControlID == "ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_ddlType" && blnEntered == true) {
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_ddlTypeError").style.backgroundColor = "";
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_ddlTypeError1").style.backgroundColor = "";
            }

            if (strControlID == "ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_ddlSalesman" && blnEntered == true) {
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_SalespersonError").style.backgroundColor = "";
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_SalespersonError1").style.backgroundColor = "";
            }

            if (strControlID == "ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_ddlCurrency" && blnEntered == true) {
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_CurrencyError").style.backgroundColor = "";
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_CurrencyError1").style.backgroundColor = "";
            }

            if (strControlID == "ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_txtDateRequired" && blnEntered == true) {
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_DateRequiredError").style.backgroundColor = "";
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_DateRequiredError1").style.backgroundColor = "";
            }
            //code for send to hub
            if (strControlID == "ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_BOMPoHubBuyer" && blnEntered == true) {
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_BuyerError").style.backgroundColor = "";
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_BuyerError1").style.backgroundColor = "";
            }

            if (strControlID == "ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_txtQuoteRequired" && blnEntered == true) {
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_QuoteRequiredError").style.backgroundColor = "";
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_QuoteRequiredError1").style.backgroundColor = "";
            }

            //if (strControlID == "ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_ddlSalesmanContact2" && blnEntered == true) {
                //document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_Contact2Error").style.backgroundColor = "";
               // document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_Contact2Error1").style.backgroundColor = "";
            //}
            
        }
       
        return blnEntered;
    },
    setControleditInError: function (strControlID, blnInError, strMessage) {
        if (blnInError) {
           
            document.getElementById(strControlID).focus();
           
            if (this._PartEditStatus == 1) {
                if (strControlID == "ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_cmbIHS12txt" && blnInError == true) {
                    document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_parterror").style.backgroundColor = "#990000";
                    document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_parterror1").style.backgroundColor = "#990000";
                }
            }
            if (strControlID == "ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_txtQuantity" && blnInError == true) {
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_QuantityError").style.backgroundColor = "#990000";
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_QuantityError1").style.backgroundColor = "#990000";
            }
            if (strControlID == "ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_txtTargetPrice" && blnInError == true) {
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_CustomerTargetPriceError").style.backgroundColor = "#990000";
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_CustomerTargetPriceError1").style.backgroundColor = "#990000";
            }
            if (strControlID == "ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_cmbManufacturer" && blnInError == true) {
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_ManufacturerError").style.backgroundColor = "#990000";
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_ManufacturerError1").style.backgroundColor = "#990000";
            }
            if (strControlID == "ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_cmbManufacturertxt" && blnInError == true) {
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_ManufacturerError").style.backgroundColor = "#990000";
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_ManufacturerError1").style.backgroundColor = "#990000";
            }
            if (strControlID == "ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_cmbProducts" && blnInError == true) {
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_ProductError").style.backgroundColor = "#990000";
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_ProductError1").style.backgroundColor = "#990000";
            }
            if (strControlID == "ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_cmbProductstxt" && blnInError == true) {
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_ProductError").style.backgroundColor = "#990000";
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_ProductError1").style.backgroundColor = "#990000";
            }
            if (strControlID == "ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_ddlRequirementforTraceability" && blnInError == true) {
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_TraceabilityError").style.backgroundColor = "#990000";
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_TraceabilityError1").style.backgroundColor = "#990000";
            }
            if (strControlID == "ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_ddlType" && blnInError == true) {
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_ddlTypeError").style.backgroundColor = "#990000";
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_ddlTypeError1").style.backgroundColor = "#990000";
            }

            if (strControlID == "ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_ddlSalesman" && blnInError == true) {
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_SalespersonError").style.backgroundColor = "#990000";
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_SalespersonError1").style.backgroundColor = "#990000";
            }

            if (strControlID == "ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUBRFQ_ctlDB_ddlCurrency" && blnInError == true) {
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUBRFQ_ctlDB_CurrencyError").style.backgroundColor = "#990000";
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUBRFQ_ctlDB_CurrencyError1").style.backgroundColor = "#990000";
            }
            if (strControlID == "ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_txtDateRequired" && blnInError == true) {
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_DateRequiredError").style.backgroundColor = "#990000";
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_DateRequiredError1").style.backgroundColor = "#990000";
            }

            //code for send to hub
            if (strControlID == "ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_BOMPoHubBuyer" && blnInError == true) {
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_BuyerError").style.backgroundColor = "#990000";
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_BuyerError1").style.backgroundColor = "#990000";
            }

            if (strControlID == "ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_txtQuoteRequired" && blnInError == true) {
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_QuoteRequiredError").style.backgroundColor = "#990000";
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_QuoteRequiredError1").style.backgroundColor = "#990000";
            }

           // if (strControlID == "ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_ddlSalesmanContact2" && blnInError == true) {
               // document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_Contact2Error").style.backgroundColor = "#990000";
               // document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_Contact2Error1").style.backgroundColor = "#990000";
           // }
           
           
        } else {
            document.getElementById(strControlID).style.border = '';
            
        }
       
    }
    ,
    allowEditingCurrency: function (bln) {
        //this.showField("ctlEditCurrency", bln);
        //this.showField("ctlCurrency", bln);
    },
    showProductLoading: function (bln) {
        //this.showFieldLoading("ctlPartNo", bln);
    },
    getPartDetail: function (partNo) {
        $('#divBlockBox2').show();
            var obj = new Rebound.GlobalTrader.Site.Data();
            obj.set_PathToData("controls/Nuggets/CusReqAdd");
            obj.set_DataObject("CusReqAdd");
            obj.set_DataAction("GetPartDetail");
            obj.addParameter("partNo", partNo);
            obj.addParameter("CompanyNo", this._intCompanyID);
            obj.addDataOK(Function.createDelegate(this, this.setPartDetail));
            obj.addError(Function.createDelegate(this, this.getPartDetailError));
            obj.addTimeout(Function.createDelegate(this, this.getPartDetailError));
            $R_DQ.addToQueue(obj);
            $R_DQ.processQueue();
            obj = null;
        

    },
    setPartDetail: function (args) {
        res = args._result;

        for (var i = 0; i < res.LastPriceCustDetails.length; i++) {
            var row = res.LastPriceCustDetails[i];

            $("#spnpartname2").text($("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_lblPartNo").text());
            $("#spnLastSoldPrice2").text(row.LastPricePaidByCust);
            $("#spnsoldtocuston2").text($R_FN.setCleanTextValue(row.LastSoldtoCustomer));
            $("#spnAvgPrice2").text(row.LastAverageReboundPriceSold);
            $("#spnlastsoldon2").text(row.LastSoldOn);

            $("#spnLastQuantity2").text(row.LastQuantity);
            $("#spnLastSupplierType2").text($R_FN.setCleanTextValue(row.LastSupplierType));
            $("#spnLastDatecode2").text($R_FN.setCleanTextValue(row.LastDatecode));
            $("#spnLastDatePurchased2").text(row.LastDatePurchased);
            $("#spnLastCustomerRegion2").text($R_FN.setCleanTextValue(row.LastCustomerRegion));

            $("#spnCustLastSoldPrice2").text(row.CustLastPricePaidByCust);
            $("#spnCurrentCust2").text(this._strCompanyName);
            // $("#spnCustAvgPrice").text(row.CustLastAvgPriceSold);
            $("#spnCustlastsoldon2").text(row.CustLastSoldOn);

            $("#spnCustQuantity2").text(row.CustQuantity);
            $("#spnCustSupplierType2").text($R_FN.setCleanTextValue(row.CustSupplierType));
            $("#spnCustDatecode2").text($R_FN.setCleanTextValue(row.CustDatecode));
            $("#spnCustDatePurchased2").text(row.CustDatePurchased);
            $("#spnCustomerRegion2").text($R_FN.setCleanTextValue(row.CustomerRegion));
            $("#spnLastPricePaid122").text(row.BestLastPricePaid12);
            $("#spnCleintBestPricePaid122").text(row.CleintBestPricePaid12);

        }
        $('#divBlockBox2').hide();

        
    },
    getPartDetailError: function (args) {
    }

};

Rebound.GlobalTrader.Site.Controls.Forms.CusReqMainInfo_EditCloneHUB.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.CusReqMainInfo_EditCloneHUB", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
