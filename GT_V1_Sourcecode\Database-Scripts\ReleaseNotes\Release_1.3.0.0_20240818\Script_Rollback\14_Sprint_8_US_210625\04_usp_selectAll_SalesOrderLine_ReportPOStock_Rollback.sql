﻿
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

/*
===========================================================================================
TASK      			UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-210625]			Phuc Hoang			09-Aug-2024		CREATE          Sanctioned manufacturers need to be highlighted in red on the SOR PDF and SO screens.
===========================================================================================
*/

CREATE OR ALTER PROCEDURE [dbo].[usp_selectAll_SalesOrderLine_ReportPOStock]                  
--******************************************************************************************                    
--* RP 07.07.2010:                    
--* - fix CurrencyNo coming from PO but CurrencyCode from GI                    
--*                      
--* RP 15.02.2010:                    
--* - add ROHS                    
--*                      
--* SK 04.11.2009:                    
--* - include SupplierPart                    
--*                      
--* SK 01.10.2009:                    
--* - include SupplierType                    
--******************************************************************************************                    
    @SalesOrderLineId int                    
AS                     
    SELECT  sol.SalesOrderLineId                    
          , sol.SalesOrderNo                    
          , al.StockNo                    
          , al.QuantityAllocated                    
          , gil.GoodsInLineId AS GoodsInLineNo                    
          , gil.GoodsInNo                    
          , sk.Part                    
          , sk.ROHS                    
          , sk.ManufacturerNo                    
          , mf.ManufacturerName                    
          , mf.ManufacturerCode                    
          , sk.DateCode                    
          , pol.PurchaseOrderLineId AS PurchaseOrderLineNo                    
          , isnull(ipol.Price, pol.Price) AS PurchasePrice                    
          , sk.ProductNo                    
          , pr.ProductName     
    , pr.ProductDescription                   
          , pol.PurchaseOrderNo                    
          , isnull(ipo.InternalPurchaseOrderNumber, po.PurchaseOrderNumber ) as PurchaseOrderNumber          
          , isnull(ipo.CurrencyNo,  po.CurrencyNo)  as  CurrencyNo                 
          , isnull(ipo.CurrencyNo, po.CurrencyNo) as CurrencyNo          
          , isnull(cup.CurrencyCode, cu.CurrencyCode) as CurrencyCode               
          , gi.CompanyNo AS SupplierNo                    
          , cm.CompanyName AS SupplierName                    
         -- , cn.CountryName           
          , case when ipo.InternalPurchaseOrderId  is null then cn.CountryName else '' end as CountryName                   
        -- , ISNULL(rg.RegionName , cn.CountryName ) as CountryName                    
          , isnull(cnipo.Duty, cn.Duty) as Duty                    
          , case isnull(cnipo.Duty, cn.Duty)                    
              WHEN 1 THEN dbo.ufn_get_productdutyrate(sk.ProductNo, gi.DateReceived)                    
              ELSE 0                    
            END AS DutyRate                    
          , isnull((gil.ShipInCost / gil.Quantity * al.QuantityAllocated), 0) AS ShipInCost                    
          , gi.DateReceived                    
          , po.TermsNo                    
          , tm.TermsName                    
          , cast(pol.Taxable AS char(1)) AS Taxable                    
          , isnull(sk.ClientLandedCost, sk.LandedCost) as LandedCost          
          , isnull(sk.ClientLandedCost, sk.LandedCost) as ClientLandedCost                    
          , ct.Name AS SupplierType                    
          , pol.SupplierPart                    
          , pol.POSerialNo                 
          , isnull(po.InternalPurchaseOrderNo,0) as InternalPurchaseOrderNo                
--          ,(select                     
--        co.CompanyName                 
--       from tbInternalPurchaseOrderLine ipol                
--      Left Join tbSalesOrderLine sol on sol.SalesOrderLineId=ipol.SalesOrderLineNo                
--left join tbInternalPurchaseOrder ipo on ipo.InternalPurchaseOrderId=ipol.InternalPurchaseOrderNo                  
--left join tbCompany   co ON co.CompanyId = ipo.CompanyNo where sol.SalesOrderLineId=@SalesOrderLineId ) as IPOSupplierName            
     , cop.CompanyName as   IPOSupplierName            
--,(select                     
--        ct.Name                
--       from tbInternalPurchaseOrderLine ipol                 
--      Left Join tbSalesOrderLine sol on sol.SalesOrderLineId=ipol.SalesOrderLineNo                
--left join tbInternalPurchaseOrder ipo on ipo.InternalPurchaseOrderId=ipol.InternalPurchaseOrderNo                  
--left join tbCompany   co ON co.CompanyId = ipo.CompanyNo                
-- LEFT JOIN dbo.tbCompanyType ct ON co.TypeNo = ct.CompanyTypeId                 
-- where sol.SalesOrderLineId=@SalesOrderLineId )  as IPOSupplierType           
 --,  ctp.Name as  IPOSupplierType          
 ,  ct.Name as  IPOSupplierType             
 , isnull(po.DateOrdered,GETDATE()) as PODateOrdered      
  , isnull(pr.IsHazardous,0) as IsProdHazardous          
 ,isnull(pr.IsOrderViaIPOonly,0) as IsOrderViaIPOonly  
 ,sol.ECCNCode  
 ,isnull((select top 1 m.ECCNStatus from dbo.tbECCN m   where m.ECCNCode= sol.ECCNCode ),0)  as IsECCNWarning         
    FROM    tbAllocation al                    
    JOIN    dbo.tbSalesOrderLine sol ON sol.SalesOrderLineId = al.SalesOrderLineNo                    
    JOIN    dbo.tbStock sk ON sk.StockId = al.StockNo             
 LEFT JOIN dbo.tbPurchaseOrderLine pol ON pol.PurchaseOrderLineId = sk.PurchaseOrderLineNo           
    LEFT JOIN dbo.tbPurchaseOrder po ON po.PurchaseOrderId = pol.PurchaseOrderNo                    
    LEFT JOIN dbo.tbGoodsInLine gil ON gil.GoodsInLineId = sk.GoodsInLineNo                    
    JOIN    tbGoodsIn gi ON gi.GoodsInId = gil.GoodsInNo                    
    LEFT JOIN dbo.tbProduct pr ON pr.ProductId = sk.ProductNo                    
    LEFT JOIN dbo.tbCurrency cu ON cu.CurrencyId = po.CurrencyNo                    
    --LEFT JOIN dbo.tbCompany cm ON cm.CompanyId = gi.CompanyNo            
    --Espire: Change 5th Dec 2018,                 
    LEFT JOIN dbo.tbCompany cm ON cm.CompanyId = po.CompanyNo                    
    LEFT JOIN dbo.tbCountry cn ON cn.CountryId = po.ImportCountryNo                    
    LEFT JOIN dbo.tbTerms tm ON tm.TermsId = po.TermsNo                    
    LEFT JOIN dbo.tbManufacturer mf ON mf.ManufacturerId = gil.ManufacturerNo                    
    LEFT JOIN dbo.tbCompanyType ct ON cm.TypeNo = ct.CompanyTypeId             
 LEFT JOIN dbo.tbInternalPurchaseOrderLine  ipol ON ipol.PurchaseOrderLineNo = sk.PurchaseOrderLineNo                           
 LEFT JOIN dbo.tbInternalPurchaseOrder  ipo ON ipol.InternalPurchaseOrderNo = ipo.InternalPurchaseOrderId             
 LEFT JOIN dbo.tbCountry cnipo ON cnipo.CountryId = ipo.ImportCountryNo               
 LEFT JOIN tbRegion rg on ipo.RegionNo = rg.RegionId            
 LEFT JOIN dbo.tbCurrency cup ON cup.CurrencyId = ipo.CurrencyNo               
 left join tbCompany   cop ON cop.CompanyId = ipo.CompanyNo               
-- LEFT JOIN dbo.tbCompanyType ctp ON cop.TypeNo = ctp.CompanyTypeId             
    WHERE   sol.SalesOrderLineId = @SalesOrderLineId 

GO
