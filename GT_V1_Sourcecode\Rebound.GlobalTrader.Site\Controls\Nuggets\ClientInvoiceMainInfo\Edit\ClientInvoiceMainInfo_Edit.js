Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.ClientInvoiceMainInfo_Edit=function(n){Rebound.GlobalTrader.Site.Controls.Forms.ClientInvoiceMainInfo_Edit.initializeBase(this,[n]);this._intClientInvoiceID=-1;this._CurrencyCode="";this._TaxRate=0;this._TaxNo=0};Rebound.GlobalTrader.Site.Controls.Forms.ClientInvoiceMainInfo_Edit.prototype={get_intClientInvoiceID:function(){return this._intClientInvoiceID},set_intClientInvoiceID:function(n){this._intClientInvoiceID!==n&&(this._intClientInvoiceID=n)},get_lblCurrency_InvoiceAmount:function(){return this._lblCurrency_InvoiceAmount},set_lblCurrency_InvoiceAmount:function(n){this._lblCurrency_InvoiceAmount!==n&&(this._lblCurrency_InvoiceAmount=n)},get_lblCurrency_GoodsInValue:function(){return this._lblCurrency_GoodsInValue},set_lblCurrency_GoodsInValue:function(n){this._lblCurrency_GoodsInValue!==n&&(this._lblCurrency_GoodsInValue=n)},get_lblCurrency_Tax:function(){return this._lblCurrency_Tax},set_lblCurrency_Tax:function(n){this._lblCurrency_Tax!==n&&(this._lblCurrency_Tax=n)},get_lblCurrency_DeliveryCharge:function(){return this._lblCurrency_DeliveryCharge},set_lblCurrency_DeliveryCharge:function(n){this._lblCurrency_DeliveryCharge!==n&&(this._lblCurrency_DeliveryCharge=n)},get_lblCurrency_BankFee:function(){return this._lblCurrency_BankFee},set_lblCurrency_BankFee:function(n){this._lblCurrency_BankFee!==n&&(this._lblCurrency_BankFee=n)},get_lblCurrency_CreditCardFee:function(){return this._lblCurrency_CreditCardFee},set_lblCurrency_CreditCardFee:function(n){this._lblCurrency_CreditCardFee!==n&&(this._lblCurrency_CreditCardFee=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Forms.ClientInvoiceMainInfo_Edit.callBaseMethod(this,"initialize");this.addShown(Function.createDelegate(this,this.formShown));this.addSave(Function.createDelegate(this,this.saveClicked))},formShown:function(){this._blnFirstTimeShown&&$find(this.getField("ctlCurrency").ControlID).addChanged(Function.createDelegate(this,this.updateCurrency));this.getFieldDropDownData("ctlCurrency");this.getFieldDropDownData("ctlddlTax")},dispose:function(){this.isDisposed||(this._intClientInvoiceID=null,this._CurrencyCode=null,this._TaxRate=null,this._TaxNo=null,Rebound.GlobalTrader.Site.Controls.Forms.ClientInvoiceMainInfo_Edit.callBaseMethod(this,"dispose"))},updateCurrency:function(){this._CurrencyCode=this.getFieldDropDownExtraText("ctlCurrency");$R_FN.setInnerHTML(this._lblCurrency_InvoiceAmount,this._CurrencyCode);$R_FN.setInnerHTML(this._lblCurrency_GoodsInValue,this._CurrencyCode);$R_FN.setInnerHTML(this._lblCurrency_Tax,this._CurrencyCode);$R_FN.setInnerHTML(this._lblCurrency_DeliveryCharge,this._CurrencyCode);$R_FN.setInnerHTML(this._lblCurrency_BankFee,this._CurrencyCode);$R_FN.setInnerHTML(this._lblCurrency_CreditCardFee,this._CurrencyCode)},saveClicked:function(){if(this.validateForm()){var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/ClientInvoiceMainInfo");n.set_DataObject("ClientInvoiceMainInfo");n.addParameter("id",this._intClientInvoiceID);n.set_DataAction("SaveEdit");n.addParameter("ClientInvoiceDate",this.getFieldValue("ctlInvoiceDate"));n.addParameter("CurrencyNo",this.getFieldValue("ctlCurrency"));n.addParameter("Amount",this.getFieldValue("ctlInvoiceAmount"));n.addParameter("GoodsValue",this.getFieldValue("ctlGoodsValue"));n.addParameter("Tax",this.getFieldValue("ctlTax"));n.addParameter("DeliveryCharge",this.getFieldValue("ctlDeliveryCharge"));n.addParameter("BankFee",this.getFieldValue("ctlBankFee"));n.addParameter("CreditCardFee",this.getFieldValue("ctlCreditCardFee"));n.addParameter("Notes",this.getFieldValue("ctlNotes"));n.addParameter("TaxNo",this.getFieldValue("ctlddlTax"));n.addParameter("TaxCode",this.getFieldDropDownExtraText("ctlddlTax"));n.addParameter("CurrencyCode",this._CurrencyCode);n.addParameter("SecondRef",$R_FN.setCleanTextValue(this.getFieldValue("ctlSecondRef")));n.addParameter("Narrative",$R_FN.setCleanTextValue(this.getFieldValue("ctlNarrative")));n.addDataOK(Function.createDelegate(this,this.saveEditComplete));n.addError(Function.createDelegate(this,this.saveEditError));n.addTimeout(Function.createDelegate(this,this.saveEditError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null}},saveEditError:function(n){this._strErrorMessage=n._errorMessage;this.onSaveError()},saveEditComplete:function(n){n._result.Result==!0?(this.showSavedOK(!0),this.onSaveComplete()):(this._strErrorMessage=n._errorMessage,this.onSaveError())},validateForm:function(){this.onValidate();var n=!0;return(this.checkFieldEntered("ctlInvoiceDate")||(n=!1),this.checkFieldEntered("ctlCurrency")||(n=!1),this.getFieldValue("ctlSecondRef").length>16)?(this.showError(!0,$R_RES.SecondRefMessage),!1):this.getFieldValue("ctlNarrative").length>41?(this.showError(!0,$R_RES.NarrativeMessage),!1):(n||this.showError(!0),n)},updateTaxRate:function(){this.getTaxRate()},getTaxRate:function(){this.getFieldValue("ctlddlTax")&&(this._TaxNo=this.getFieldValue("ctlddlTax"));var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/ClientInvoiceMainInfo");n.set_DataObject("ClientInvoiceMainInfo");n.set_DataAction("GetTaxRate");n.addParameter("TaxNo",this._TaxNo);n.addDataOK(Function.createDelegate(this,this.getTaxRateComplete));n.addError(Function.createDelegate(this,this.getTaxRateError));n.addTimeout(Function.createDelegate(this,this.getTaxRateError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getTaxRateError:function(n){this._strErrorMessage=n._errorMessage;this.onSaveError()},getTaxRateComplete:function(n){n._result&&(this._TaxRate=n._result.Rate)},validateTaxRate:function(){var n=!0;return parseFloat(this._TaxRate)<=0?(n=parseFloat(this.getFieldValue("ctlTax"))==0,n||(n=confirm($R_RES.TaxValueMessage))):(n=parseFloat(this.getFieldValue("ctlTax"))>0,n||(n=confirm($R_RES.TaxValueMessage))),n||this.showError(!0),n}};Rebound.GlobalTrader.Site.Controls.Forms.ClientInvoiceMainInfo_Edit.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.ClientInvoiceMainInfo_Edit",Rebound.GlobalTrader.Site.Controls.Forms.Base,Sys.IDisposable);