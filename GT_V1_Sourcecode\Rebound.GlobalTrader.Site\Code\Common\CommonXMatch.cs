﻿using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Text;
using System.Web;

namespace Rebound.GlobalTrader.Site.XMatchTool.Common
{
    public class CommonXMatch
    {
        public static string SaveDataTableToExcel(DataTable dtResult, string savePath, string fileName)
        {
            string strFilename = string.Empty;
            //open file
            //StreamWriter wr = new StreamWriter(savePath, false, Encoding.Unicode);

            //try
            //{
            //    for (int i = 0; i < dtResult.Columns.Count; i++)
            //    {
            //        wr.Write(dtResult.Columns[i].ToString().ToUpper() + "\t");
            //    }

            //    wr.WriteLine();

            //    //write rows to excel file
            //    for (int i = 0; i < (dtResult.Rows.Count); i++)
            //    {
            //        for (int j = 0; j < dtResult.Columns.Count; j++)
            //        {
            //            if (dtResult.Rows[i][j] != null)
            //            {
            //                wr.Write("=\"" + Convert.ToString(dtResult.Rows[i][j]) + "\"" + "\t");
            //            }
            //            else
            //            {
            //                wr.Write("\t");
            //            }
            //        }
            //        //go to next line
            //        wr.WriteLine();
            //    }
            //    //close file
            //    wr.Close();
            //}
            //catch (Exception ex)
            //{

            //    return false;
            //}
            var lines = new List<string>();
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < dtResult.Columns.Count; i++)
            {
                sb.Append(dtResult.Columns[i]);
                if (i < dtResult.Columns.Count - 1)
                {
                    sb.Append(",");
                }
            }
            lines.Add(sb.ToString());
            sb = new StringBuilder();
            foreach (DataRow dr in dtResult.Rows)
            {
                for (int i = 0; i < dtResult.Columns.Count; i++)
                {
                    if (!Convert.IsDBNull(dr[i]))
                    {
                        string value = dr[i].ToString();
                        if (value.Contains(','))
                        {
                            value = String.Format("\"{0}\"", value);
                            sb.Append(value);
                        }
                        else
                        {
                            sb.Append(dr[i].ToString());
                        }
                    }
                    if (i < dtResult.Columns.Count - 1)
                    {
                        sb.Append(",");
                    }
                }
                lines.Add(sb.ToString());
                sb = new StringBuilder();
            }
            strFilename = String.Format("{0}{1}{2}.csv", fileName, SessionManager.LoginID, SessionManager.ClientID);
            File.WriteAllLines(String.Format("{0}/{1}", savePath, strFilename), lines.ToArray(), Encoding.UTF8);
            return strFilename;
        }
    }
}