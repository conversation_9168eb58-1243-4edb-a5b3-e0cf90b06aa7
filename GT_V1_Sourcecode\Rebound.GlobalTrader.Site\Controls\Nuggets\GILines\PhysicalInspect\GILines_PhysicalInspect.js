Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.GILines_PhysicalInspect=function(n){Rebound.GlobalTrader.Site.Controls.Forms.GILines_PhysicalInspect.initializeBase(this,[n]);this._intLineID=-1;this._intGIID=0;this._ctlConfirm=null};Rebound.GlobalTrader.Site.Controls.Forms.GILines_PhysicalInspect.prototype={get_intGIID:function(){return this._intGIID},set_intGIID:function(n){this._intGIID!==n&&(this._intGIID=n)},get_intLineID:function(){return this._intLineID},set_intLineID:function(n){this._intLineID!==n&&(this._intLineID=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Forms.GILines_PhysicalInspect.callBaseMethod(this,"initialize");this.addShown(Function.createDelegate(this,this.formShown));this.getGoodsIn()},dispose:function(){this.isDisposed||(this._ctlConfirm&&this._ctlConfirm.dispose(),this._ctlConfirm=null,this._intLineID=null,this._intGIID=null,Rebound.GlobalTrader.Site.Controls.Forms.GILines_PhysicalInspect.callBaseMethod(this,"dispose"))},formShown:function(){this._blnFirstTimeShown&&(this._ctlConfirm=this.getFieldComponent("ctlConfirm"),this._ctlConfirm.addClickYesEvent(Function.createDelegate(this,this.yesClicked)),this._ctlConfirm.addClickNoEvent(Function.createDelegate(this,this.noClicked)))},getGoodsIn:function(){$R_FN.showElement(this._pnlLines,!1);var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/GIMainInfo");n.set_DataObject("GIMainInfo");n.set_DataAction("GetData");n.addParameter("ID",this._intGIID);n.addDataOK(Function.createDelegate(this,this.getGoodsInOK));n.addError(Function.createDelegate(this,this.getGoodsInError));n.addTimeout(Function.createDelegate(this,this.getGoodsInError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getGoodsInOK:function(n){var t=n._result;this.setFieldsFromGoodsIn(t)},setFieldsFromGoodsIn:function(n){n&&(this.setFieldValue("ctlGoodsIn",n.GoodsInNumber),this.setFieldValue("ctlSupplier",n.SupplierName))},getGoodsInError:function(n){this._strErrorMessage=n._errorMessage;this.onSaveError()},yesClicked:function(){this.showSaving(!0);var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("Controls/Nuggets/GILines");n.set_DataObject("GILines");n.set_DataAction("PhysicalInspect");n.addParameter("id",this._intLineID);n.addDataOK(Function.createDelegate(this,this.saveComplete));n.addError(Function.createDelegate(this,this.saveError));n.addTimeout(Function.createDelegate(this,this.saveError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null;this.onSave()},noClicked:function(){this.showSaving(!1);this.onNotConfirmed()},saveError:function(n){this.showSaving(!1);this._strErrorMessage=n._errorMessage;this.onSaveError()},saveComplete:function(n){this.showSaving(!1);n._result.Result==!0?this.onSaveComplete():(this._strErrorMessage=n._errorMessage,this.onSaveError())}};Rebound.GlobalTrader.Site.Controls.Forms.GILines_PhysicalInspect.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.GILines_PhysicalInspect",Rebound.GlobalTrader.Site.Controls.Forms.Base,Sys.IDisposable);