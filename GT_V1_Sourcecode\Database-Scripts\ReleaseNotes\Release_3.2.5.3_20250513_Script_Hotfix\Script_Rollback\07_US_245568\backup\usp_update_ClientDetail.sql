﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE OR ALTER PROCEDURE [dbo].[usp_update_ClientDetail]            
    @ClientId INT,      
    @ClientBillTo NVARCHAR(120) = NULL,      
    @CustomerCode NVARCHAR(60) = NULL,      
    @TaxNo INT = NULL,      
    @TermsNo INT = NULL,      
    @ShipViaNo INT = NULL,      
    @AddressName NVARCHAR(50) = NULL,      
    @Line1 NVARCHAR(50) = NULL,      
    @Line2 NVARCHAR(50) = NULL,      
    @Line3 NVARCHAR(50) = NULL,      
    @County NVARCHAR(50) = NULL,      
    @City NVARCHAR(50) = NULL,      
    @State NVARCHAR(3) = NULL,      
    @CountryNo INT = NULL,      
    @ZIP NVARCHAR(10) = NULL,      
    @UpdatedBy INT = NULL,      
    @RowsAffected INT = NULL OUTPUT      
AS            
BEGIN      
    BEGIN TRANSACTION; -- Start a transaction      
      
    DECLARE @Count INT;     
 DECLARE @ClientAddressNo INT;    
 declare @ClientAddressid int;    
      
    -- Check if the client exists      
    --SELECT @Count = COUNT(*)      
    --FROM dbo.tbClient      
    --WHERE ClientId = @ClientId;      
     SELECT @Count = COUNT(*),@ClientAddressNo=isnull(ClientAddressNo,0)      
    FROM dbo.tbClient      
    WHERE ClientId = @ClientId group by ClientAddressNo    
      
    IF @Count > 0      
    BEGIN      
        -- Update the client's details      
        UPDATE dbo.tbClient      
        SET ClientBillTo = @ClientBillTo,      
            CustomerCode = @CustomerCode,      
            TaxNo = @TaxNo,      
            TermsNo = @TermsNo,      
            ShipViaNo = @ShipViaNo,      
            UpdatedBy = @UpdatedBy,      
            DLUP = GETDATE() -- Use GETDATE() instead of CURRENT_TIMESTAMP for SQL Server compatibility      
        WHERE ClientId = @ClientId;      
    END;      
 --if not EXISTS (SELECT ClientAddressNo FROM dbo.tbClient WHERE ClientId = @ClientId)      
     
 if (@Count>0 and @ClientAddressNo=0)     
    BEGIN      
        -- Insert a new address record      
        INSERT INTO dbo.tbClientAddress (AddressName, Line1, Line2, Line3, County, City, State, CountryNo, ZIP,UpdatedBy)      
        VALUES (@AddressName, @Line1, @Line2, @Line3, @County, @City, @State, @CountryNo, @ZIP,@UpdatedBy);     
    
   SET @ClientAddressid = scope_identity()      
    update tbClient set ClientAddressNo=@ClientAddressid where ClientId = @ClientId    
    END;      
    -- Update or insert the client's address      
   else      
    BEGIN      
        -- Update the existing address record      
        UPDATE dbo.tbClientAddress      
        SET AddressName = @AddressName,      
            Line1 = @Line1,      
            Line2 = @Line2,      
            Line3 = @Line3,      
            County = @County,      
            City = @City,      
            State = @State,      
            CountryNo = @CountryNo,      
            ZIP = @ZIP      
   ,UpdatedBy=@UpdatedBy    
        WHERE AddressId = @ClientAddressNo      
    END      
          
      
    -- Commit the transaction if everything succeeded      
    IF @@ERROR = 0      
    BEGIN      
        COMMIT TRANSACTION;      
        SET @RowsAffected = @@ROWCOUNT;      
    END      
    ELSE      
    BEGIN      
        ROLLBACK TRANSACTION; -- Rollback the transaction if an error occurred      
    END;      
END; 
GO


