//-----------------------------------------------------------------------------------------
// RP 05.01.2010:
// - add links back to related Quotes
//Marker     changed by      date         Remarks
//[001]      Aashu           07/06/2018   Added supplier warranty field
//[002]      Aashu          14/08/2018     REB-12322 : A tick box to recomond test the parts from HUB side.
//-----------------------------------------------------------------------------------------
using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;
using System.Linq;

namespace Rebound.GlobalTrader.Site.Controls.Nuggets.Data {
	[WebService(Namespace = "http://tempuri.org/")]
	[WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
	public class BOMCustomerRequirementSourcingResults : Rebound.GlobalTrader.Site.Data.Base {
		public override void ProcessRequest(HttpContext context) {
			if (base.init(context)) {
                switch (Action)
                {
                    case "GetData": GetData(); break;
                    case "GetItem": GetItem(); break;
                    case "AddNew": AddNew(); break;
                    case "SaveEdit": SaveEdit(); break;
                    case "DeleteItem": DeleteItem(); break;
                    case "UpdateCurrency": UpdateCurrency(); break;
                    case "ReleaseSourcing": ReleaseSourcing(); break;
                    case "Approval": Approval(); break;
                    case "DeletePartWatchMatch": DeletePartWatchMatch();break;
                    case "GetCountry": GetCountry(); break;

                    default: WriteErrorActionNotFound(); break;
                }
			}
		}

		public override bool IsReusable {
			get { return false; }
		}

        /// <summary>
        /// Release customer requirement for client user
        /// </summary>
        public void ReleaseSourcing()
        {
            try
            {
                int? Bomid = GetFormValue_Int("BomId");
                string BOMCode = GetFormValue_String("BOMCode");
                string BOMName = GetFormValue_String("BOMName");
                string BomCompanyName = GetFormValue_String("BomCompanyName");
                int? BomCompanyNo = GetFormValue_Int("BomCompanyNo");
               // string SalesManName = GetFormValue_String("SalesManName");
                int? SalesManNo = GetFormValue_Int("SalesManNo");
                int? CustReqNo = GetFormValue_Int("CustReqNo");
                int? SID = GetFormValue_Int("SID");
                string ReqSalesPerson = GetFormValue_String("Reqsalesman");
                string SupportTeamMemberNo = GetFormValue_String("SupportTeamMemberNo");
                
                bool blnResult = SourcingResult.ReleaseSourcing(
                    SID,
                    LoginID
                     );

                if (blnResult)
                {
                    WebServices servic = new WebServices();
                    //BLL.CustomerRequirement bomr = BLL.CustomerRequirement.Get(ID);
                    if (!string.IsNullOrEmpty(SupportTeamMemberNo))
                    {
                        servic.NotifyReleaseBom((SalesManNo ?? 0).ToString() + "||" + ReqSalesPerson + "||" + SupportTeamMemberNo, "", Functions.GetGlobalResource("Messages", "BOMReleased"), (Bomid ?? 0), BOMCode, BOMName, BomCompanyNo, BomCompanyName, false, CustReqNo);
                    }
                    else
                    {
                        servic.NotifyReleaseBom((SalesManNo ?? 0).ToString() + "||" + ReqSalesPerson, "", Functions.GetGlobalResource("Messages", "BOMReleased"), (Bomid ?? 0), BOMCode, BOMName, BomCompanyNo, BomCompanyName, false, CustReqNo);
                    }
                }
                JsonObject jsn = new JsonObject();
                jsn.AddVariable("Result", blnResult);
                OutputResult(jsn);
                jsn.Dispose();
                jsn = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }

        public void Approval()
        {
            try
            {
                int? Bomid = GetFormValue_Int("BomId");
                string BOMCode = GetFormValue_String("BOMCode");
                string BOMName = GetFormValue_String("BOMName");
                string BomCompanyName = GetFormValue_String("BomCompanyName");
                int? BomCompanyNo = GetFormValue_Int("BomCompanyNo");
                // string SalesManName = GetFormValue_String("SalesManName");
                int? SalesManNo = GetFormValue_Int("SalesManNo");
                int? CustReqNo = GetFormValue_Int("CustReqNo");
                int? intSourcingResultNo = GetFormValue_Int("SID");
                string ReqSalesPerson = GetFormValue_String("Reqsalesman");
                
                bool blnResult = SourcingResult.ApproveSourcing(
                   intSourcingResultNo,
                    LoginID
                     );

                if (blnResult)
                {
                    WebServices servic = new WebServices();
                    //BLL.CustomerRequirement bomr = BLL.CustomerRequirement.Get(ID);
                    servic.NotifyApproval((SalesManNo ?? 0).ToString() + "||" + ReqSalesPerson, "", Functions.GetGlobalResource("Messages", "HUBRFQApproval"), (Bomid ?? 0), BOMCode, BOMName, BomCompanyNo, BomCompanyName, false, CustReqNo, intSourcingResultNo);
                }
                JsonObject jsn = new JsonObject();
                jsn.AddVariable("Result", blnResult);
                OutputResult(jsn);
                jsn.Dispose();
                jsn = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }
        private void GetData() {
            List<SourcingResult> lst = SourcingResult.GetListForBOMCustomerRequirement(ID, Convert.ToBoolean(SessionManager.IsPOHub));
			if (lst == null) {
				WriteErrorDataNotFound();
			} else {
				try {
					JsonObject jsn = new JsonObject();
					JsonObject jsnItems = new JsonObject(true);

                    bool blnAllHasDeliveryDate = true;
                    bool blnAllHasProduct = true;
                    bool IsAssignedToMe = false;
                    var listMfrNotes = Manufacturer.GetAdvisoryNotes(lst.Select(x=>x.ManufacturerNo).ToList(), (int)SessionManager.ClientID);

                    foreach (SourcingResult ln in lst) {

                        if (blnAllHasDeliveryDate==true && !ln.DeliveryDate.HasValue)
                            blnAllHasDeliveryDate = false;

                        if (blnAllHasProduct == true && (!ln.ProductNo.HasValue || ln.ProductNo==0))
                        {
                            blnAllHasProduct = false;
                        }

						JsonObject jsnItem = new JsonObject();
						jsnItem.AddVariable("ID", ln.SourcingResultId);
                        jsnItem.AddVariable("IsClosed", ln.IsClosed);
                        if (ln.SourcingTable == "PQ" || ln.SourcingTable == "OFPH" || ln.SourcingTable == "EXPH"|| ln.SourcingTable == "HUBSTK" || ln.SourcingTable == "EPPH" || ln.SourcingTable == "RLPH")
                        {
                            //jsnItem.AddVariable("SupplierNo", Convert.ToBoolean(SessionManager.IsPOHub) ? ln.POHubCompanyNo : ln.ClientCompanyNo);
                            //jsnItem.AddVariable("Supplier", Convert.ToBoolean(SessionManager.IsPOHub) ? ln.POHubSupplierName : ln.ClientSupplierName);
                            if (Convert.ToBoolean(SessionManager.IsPOHub))
                            {
                                jsnItem.AddVariable("SupplierNo", ln.POHubCompanyNo);
                                jsnItem.AddVariable("Supplier", ln.POHubSupplierName);
                                //only display notes in DMCC since in Company name hidden in Client side.
                                string supplierNotes = Company.GetAdvisoryNotes(ln.POHubCompanyNo ?? 0);
                                jsnItem.AddVariable("SupplierAdvisoryNotes", Functions.ReplaceLineBreaks(supplierNotes));
                            }
                            else
                            {
                                jsnItem.AddVariable("SupplierNo", ln.ClientCompanyNo);
                                jsnItem.AddVariable("Supplier", ln.ClientSupplierName);
                                string supplierNotes = Company.GetAdvisoryNotes(ln.ClientCompanyNo ?? 0);
                                jsnItem.AddVariable("SupplierAdvisoryNotes", Functions.ReplaceLineBreaks(supplierNotes));
                            }


                            if (ln.ClientCurrencyNo != SessionManager.ClientCurrencyID)
                            {
                                if (!SessionManager.IsPOHub.Value)
                                {
                                    jsnItem.AddVariable("PriceInBase", Functions.FormatCurrency(BLL.Currency.ConvertValueToBaseCurrency(ln.Price, ln.ClientCurrencyNo, DateTime.Now), SessionManager.ClientCurrencyCode));
                                    jsnItem.AddVariable("EstimatedShippingCostInBase", Functions.FormatCurrency(BLL.Currency.ConvertValueToBaseCurrency(ln.EstimatedShippingCost, ln.ClientCurrencyNo, DateTime.Now), SessionManager.ClientCurrencyCode));

                                }
                                else
                                {
                                    jsnItem.AddVariable("PriceInBase", Functions.FormatCurrency(BLL.Currency.ConvertValueToBaseCurrency(ln.Price, ln.CurrencyNo, DateTime.Now), SessionManager.ClientCurrencyCode));
                                }

                            }
                        }
                        else
                        {
                            jsnItem.AddVariable("SupplierNo", ln.SupplierNo);
                            jsnItem.AddVariable("Supplier",  ln.SupplierName);
                            string supplierNotes = Company.GetAdvisoryNotes(ln.SupplierNo ?? 0);
                            jsnItem.AddVariable("SupplierAdvisoryNotes", Functions.ReplaceLineBreaks(supplierNotes));
                            if (ln.CurrencyNo != SessionManager.ClientCurrencyID)
                            {
                                if (!SessionManager.IsPOHub.Value)
                                {
                                    jsnItem.AddVariable("PriceInBase", Functions.FormatCurrency(BLL.Currency.ConvertValueToBaseCurrency(ln.Price, ln.CurrencyNo, DateTime.Now), SessionManager.ClientCurrencyCode));
                                    jsnItem.AddVariable("EstimatedShippingCostInBase", Functions.FormatCurrency(BLL.Currency.ConvertValueToBaseCurrency(ln.EstimatedShippingCost, ln.CurrencyNo, DateTime.Now), SessionManager.ClientCurrencyCode));

                                }

                            }
                        }


                        jsnItem.AddVariable("ManufacturerName", ln.ManufacturerName);
                        jsnItem.AddVariable("PFPQ", ln.SourcingTable == "PQ" ? "YES" : "-");
						jsnItem.AddVariable("Part", ln.Part);
						jsnItem.AddVariable("Product", ln.ProductName);
						//jsnItem.AddVariable("Package", ln.PackageName); // soorya
						jsnItem.AddVariable("Package", ln.SupplierPackageType);
						jsnItem.AddVariable("Qty", ln.Quantity);
						jsnItem.AddVariable("MfrNo", ln.ManufacturerNo);
						jsnItem.AddVariable("Mfr", ln.ManufacturerCode);
                        string mfrNotes = !Functions.HasNumbericValue(ln.ManufacturerNo) ? "" : listMfrNotes.Find(x => x.ManufacturerId == (int)ln.ManufacturerNo).AdvisoryNotes;
                        jsnItem.AddVariable("MfrAdvisoryNotes", Functions.ReplaceLineBreaks(mfrNotes));
                        jsnItem.AddVariable("DC", ln.DateCode);
						jsnItem.AddVariable("Date", Functions.FormatDate(ln.OfferStatusChangeDate));
						//jsnItem.AddVariable("By", (String.IsNullOrEmpty(ln.SalesmanName)) ? ln.OfferStatusChangeEmployeeName : ln.SalesmanName);
                        jsnItem.AddVariable("By", ln.OfferStatusChangeEmployeeName);
                        jsnItem.AddVariable("Notes", Functions.ReplaceLineBreaks(ln.Notes));
                        jsnItem.AddVariable("MslSpqFactorySealed", ln.MslSpqFactorySealed);
                        jsnItem.AddVariable("ROHSDescription", ln.ROHSDescription);
                        //jsnItem.AddVariable("SupplierManufacturerName", ln.ManufacturerName);
                        //jsnItem.AddVariable("SupplierDateCode", String.IsNullOrEmpty(ln.SupplierDateCode)?ln.DateCode:ln.SupplierDateCode);
                        //jsnItem.AddVariable("SupplierPackageType", String.IsNullOrEmpty(ln.SupplierPackageType)?ln.PackageName:ln.SupplierPackageType);
                        //jsnItem.AddVariable("SupplierProductType", String.IsNullOrEmpty(ln.SupplierProductType)?ln.ProductName:ln.SupplierProductType);
                        //[001] start
                        jsnItem.AddVariable("SupplierWarranty", ln.SupplierWarranty);
                        //[001] end
                        jsnItem.AddVariable("SupplierManufacturerName", ln.SupplierManufacturerName);
                        jsnItem.AddVariable("SupplierDateCode", ln.SupplierDateCode);
                        jsnItem.AddVariable("SupplierPackageType", ln.SupplierPackageType);
                        jsnItem.AddVariable("SupplierProductType", ln.SupplierProductType);
                        
                        jsnItem.AddVariable("SupplierMOQ", ln.SupplierMOQ);
                        jsnItem.AddVariable("SupplierTotalQSA", ln.SupplierTotalQSA);
                        jsnItem.AddVariable("SupplierLTB", ln.SupplierLTB);
                        jsnItem.AddVariable("SupplierNotes", Functions.ReplaceLineBreaks(ln.SupplierNotes));

                        jsnItem.AddVariable("SupplierType", ln.SupplierType);
                        jsnItem.AddVariable("DeliveryDate", Functions.FormatDate(ln.DeliveryDate));
                        jsnItem.AddVariable("SourcRelease", ln.SourcingRelease);
                        //jsnItem.AddVariable("AllHasDelDate", blnAllHasDeliveryDate);

                        
                        //get price in base currency too if it's not already
                        jsnItem.AddVariable("Price", Functions.FormatCurrency(ln.Price, ln.CurrencyCode));
                        jsnItem.AddVariable("EstimatedShippingCost", Functions.FormatCurrency(ln.EstimatedShippingCost, ln.CurrencyCode));
                        jsnItem.AddVariable("BuyPrice", Functions.FormatCurrency(ln.OriginalPrice, ln.ActualCurrencyCode));
                        jsnItem.AddVariable("ActualPrice", Functions.FormatCurrency(ln.ActualPrice, SessionManager.ClientCurrencyCode));
                        jsnItem.AddVariable("SupplierPercentage", ln.SupplierPercentage+ Functions.GetGlobalResource("Misc","PercentSymbol"));
                        jsnItem.AddVariable("SPQ", ln.SPQ);
                        jsnItem.AddVariable("LeadTime", ln.LeadTime);
                        jsnItem.AddVariable("ROHSStatus", ln.ROHSStatus);
                        jsnItem.AddVariable("FactorySealed", ln.FactorySealed);
                        jsnItem.AddVariable("MSL", ln.MSL);
                        jsnItem.AddVariable("ClientNo", ln.ClientNo);
                        jsnItem.AddVariable("IsClosed", ln.IsClosed);
                        jsnItem.AddVariable("IsSoCreated", ln.IsSoCreated);
                       // jsnItem.AddVariable("TermsName", ln.IsApplyPOBankFee==true?ln.TermsName:"");
                        jsnItem.AddVariable("TermsName",  ln.TermsName );
                        jsnItem.AddVariable("ActBuyCurrencyNo", ln.ActualCurrencyNo);
                        jsnItem.AddVariable("MSLLevelNo", ln.MSLLevelNo);
                        jsnItem.AddVariable("MSLLevelText", ln.MSLLevelText);                       

                        jsnItem.AddVariable("IsSourcingReleasedCount", ln.SourcingReleasedCount > 0);
                        if (ln.SourceRef == "Q" || ln.SourceRef == "S")
                        {
                            jsnItem.AddVariable("SourceRef", false);
                        }
                        else
                        {
                            jsnItem.AddVariable("SourceRef", true);
                        }
                        
                        //else
                        //{
                        //    jsnItem.AddVariable("PriceInBase", Functions.FormatCurrency(ln.Price, SessionManager.ClientCurrencyCode));
                        //}
						//status
						string strStatus = "";
						if (ln.OfferStatusNo != null) strStatus = Functions.GetGlobalResource("OfferStatus", (OfferStatus.List)ln.OfferStatusNo);
						jsnItem.AddVariable("Status", strStatus);

						//related quotes
						JsonObject jsnQuotes = new JsonObject(true);
						foreach (BLL.Quote qt in Quote.GetListForSourcingResult(ln.SourcingResultId)) {
							JsonObject jsnQuote = new JsonObject();
							jsnQuote.AddVariable("ID", qt.QuoteId);
							jsnQuote.AddVariable("No", qt.QuoteNumber);
							jsnQuote.AddVariable("QuoteStatus", qt.QuoteStatusName);
							jsnQuote.AddVariable("CustomerName", qt.CompanyName);
                            jsnQuote.AddVariable("TaskCount", qt.TaskCount);
                            jsnQuote.AddVariable("HasUnFinishedTask", qt.HasUnFinishedTask);
                            jsnQuotes.AddVariable(jsnQuote);
							jsnQuote.Dispose(); jsnQuote = null;
						}
						jsnItem.AddVariable("Quotes", jsnQuotes);
                        jsnItem.AddVariable("RegionName", ln.RegionName);
                        //[002] start
                        jsnItem.AddVariable("IsTestingRecommended", ln.IsTestingRecommended);
                        jsnItem.AddVariable("IsImageAvail", ln.IsImageAvailable);
                        jsnItem.AddVariable("PriorityId", ln.PriorityNo);
                        jsnItem.AddVariable("IHSCountryOfOriginNo", ln.IHSCountryOfOriginNo);
                        jsnItem.AddVariable("IHSCountryOfOriginName", ln.IHSCountryOfOriginName);
                        jsnItem.AddVariable("CountryOfOriginNo", ln.CountryOfOriginNo);
                        jsnItem.AddVariable("CountryOfOriginName", ln.CountryOfOriginName);
                        jsnItem.AddVariable("UnitSellPrice", ln.Price);
                        jsnItem.AddVariable("UnitBuyPrice", ln.OriginalPrice);

                        jsnItem.AddVariable("PartWatchMatchHUBIPO", ln.PartWatchMatchHUBIPO);
                        jsnItem.AddVariable("IsPartWatchMatchClient", ln.IsPartWatchMatchClient);
                        //[002] end
                        jsnItem.AddVariable("SourceClient", ln.SourceClient );
                        jsnItem.AddVariable("SourceClientNo", ln.SourceClientNo);
                        jsnItem.AddVariable("ISAS6081Required", ln.ISAS6081Required);
                        jsnItem.AddVariable("TypeOfSupplier", ln.TypeOfSupplierName);
                        jsnItem.AddVariable("ReasonForSupplier", ln.ReasonForSupplierName);
                        jsnItem.AddVariable("RiskOfSupplier", ln.RiskOfSupplierName);

                        string[] Ids = ln.AssigneeId.Split(',');
                        for(int i = 0; i < Ids.Length; i++)
                        {
                            if(Convert.ToInt32(Ids[i]) == SessionManager.LoginID)
                            {
                                IsAssignedToMe = true;
                            }
                            else
                            {
                                IsAssignedToMe = false;
                            }
                            if (IsAssignedToMe == true)
                            {
                                break;
                            }
                        }
                        jsnItem.AddVariable("IsAssignToMe", IsAssignedToMe);
                        jsnQuotes.Dispose(); jsnQuotes = null;

						jsnItems.AddVariable(jsnItem);
						jsnItem.Dispose(); jsnItem = null;
					}
					jsn.AddVariable("Results", jsnItems);
                    jsn.AddVariable("AllHasDelDate", blnAllHasDeliveryDate);
                    jsn.AddVariable("AllHasProduct", blnAllHasProduct);
                    OutputResult(jsn);
					jsn.Dispose(); jsn = null;
				} catch (Exception e) {
					WriteError(e);
				}
			}
		}

		private void GetItem() {
			SourcingResult sr = SourcingResult.Get(ID);
			if (sr != null) {
				JsonObject jsn = new JsonObject();
				jsn.AddVariable("Part", sr.Part);
				jsn.AddVariable("DateCode", sr.DateCode);
				jsn.AddVariable("Quantity", sr.Quantity);
				jsn.AddVariable("Price", Functions.FormatCurrency(sr.Price));
				jsn.AddVariable("CurrencyNo", sr.CurrencyNo);
				jsn.AddVariable("PackageNo", sr.PackageNo);
				jsn.AddVariable("ProductNo", sr.ProductNo);
				jsn.AddVariable("Mfr", sr.ManufacturerName);
				jsn.AddVariable("MfrNo", sr.ManufacturerNo);
				jsn.AddVariable("OfferStatus", sr.OfferStatusNo);
				jsn.AddVariable("ROHS", sr.ROHS);
				//jsn.AddVariable("Supplier", sr.SupplierName);
				//jsn.AddVariable("SupplierNo", sr.SupplierNo);
				jsn.AddVariable("Notes", Functions.ReplaceLineBreaks(sr.Notes));
               // jsn.AddVariable("SupPrice", Functions.FormatCurrency(sr.SupplierPrice));               
                jsn.AddVariable("SupPrice", Functions.FormatCurrency(sr.OriginalPrice));               
                jsn.AddVariable("EstimatedShippingCostValue", sr.EstimatedShippingCost);
                jsn.AddVariable("DeliveryDate", Functions.FormatDate(sr.DeliveryDate));
                jsn.AddVariable("SPQ", sr.SPQ);
                jsn.AddVariable("LeadTime", sr.LeadTime);
                jsn.AddVariable("ROHSStatus", sr.ROHSStatus);
                jsn.AddVariable("FactorySealed", sr.FactorySealed);
                jsn.AddVariable("MSL", sr.MSL);
                jsn.AddVariable("SupplierLTB", sr.SupplierLTB);
                jsn.AddVariable("SupplierMOQ", sr.SupplierMOQ);
                jsn.AddVariable("SupplierTotalQSA", sr.SupplierTotalQSA);
                jsn.AddVariable("SupplierNotes", Functions.ReplaceLineBreaks(sr.SupplierNotes));
                if (sr.SourcingTable == "PQ" || sr.SourcingTable == "OFPH" || sr.SourcingTable == "EXPH" || sr.SourcingTable == "HUBSTK" || sr.SourcingTable == "EPPH" || sr.SourcingTable == "RLPH")
                {
                    jsn.AddVariable("SupplierNo", Convert.ToBoolean(SessionManager.IsPOHub) ? sr.POHubCompanyNo : sr.ClientCompanyNo);
                    jsn.AddVariable("Supplier", Convert.ToBoolean(SessionManager.IsPOHub) ? sr.POHubSupplierName : sr.ClientSupplierName);
                }
                else
                {
                    jsn.AddVariable("SupplierNo", sr.SupplierNo);
                    jsn.AddVariable("Supplier", sr.SupplierName);
                }
                string notes = Company.GetAdvisoryNotes(sr.SupplierNo??0);
                jsn.AddVariable("SupplierAdvisoryNotes", Functions.ReplaceLineBreaks(notes));

                jsn.AddVariable("ClientNo", sr.ClientNo);
               // jsn.AddVariable("POHubSupplier", sr.POHubSupplierName);
               // jsn.AddVariable("POHubSupplierNo", sr.POHubCompanyNo);
                jsn.AddVariable("UPLiftPrice", sr.UPLiftPrice);
                jsn.AddVariable("RegionNo", sr.RegionNo);
                jsn.AddVariable("CurrencyCode", sr.CurrencyCode);
                jsn.AddVariable("CurrCodeForSupPrice", string.Format("{0} ( {1} )", sr.ActualCurrencyCode, Functions.FormatCurrency(sr.SupplierPrice, SessionManager.ClientCurrencyCode)));
                jsn.AddVariable("UpliftPriceCode", string.Format("{0} ( {1} % )", sr.CurrencyCode, sr.UPLiftPrice.HasValue ? sr.UPLiftPrice.Value : 0));
                 jsn.AddVariable("BuySupPrice", Functions.FormatCurrency(sr.SupplierPrice));
                 jsn.AddVariable("ActCurCode", sr.ActualCurrencyCode);
                 jsn.AddVariable("ProductDescription", sr.ProductDescription);
                 jsn.AddVariable("ProdInc", Convert.ToBoolean(sr.ProductInactive));
                 jsn.AddVariable("MSLLevelNo", sr.MSLLevelNo);
                //[001] start
                 jsn.AddVariable("SupplierWarranty", sr.SupplierWarranty);
                 jsn.AddVariable("NonPreferredCompany", sr.NonPreferredCompany);
                 jsn.AddVariable("PriorityId", sr.PriorityNo);
                 jsn.AddVariable("IHSCountryOfOriginNo", sr.IHSCountryOfOriginNo);
                 jsn.AddVariable("IHSCountryOfOriginName", sr.IHSCountryOfOriginName);
                 jsn.AddVariable("CountryOfOriginNo", sr.CountryOfOriginNo);
                jsn.AddVariable("PackageDescription", sr.PackageDescription);
                jsn.AddVariable("PartWatchMatchHUBIPO", sr.PartWatchMatchHUBIPO);

                jsn.AddVariable("TypeOfSupplierNo", sr.TypeOfSupplierNo);
                jsn.AddVariable("ReasonForSupplierNo", sr.ReasonForSupplierNo);
                jsn.AddVariable("RiskOfSupplierNo", sr.RiskOfSupplierNo);
                jsn.AddVariable("IsCountryFound", sr.IsCountryFound);
                jsn.AddVariable("CountryName", sr.CountryName);
                jsn.AddVariable("CountryNo", sr.CountryNo);
                jsn.AddVariable("SellPriceLessReason", sr.SellPriceLessReason);
                jsn.AddVariable("IsTestingRecommended", sr.IsTestingRecommended);
                //[001] end
                OutputResult(jsn);
				jsn.Dispose(); jsn = null;
			} else {
				WriteErrorDataNotFound();
			}
		}

		public void SaveEdit() {
			try {
				//update sourcing result
				//(proc also updates related Offer, where appropriate)
                double? EstimatedShippingCost=GetFormValue_NullableDouble("EstimatedShippingCost");
                DateTime? DeliveryDate = GetFormValue_NullableDateTime("DeliveryDate");
				bool blnOK = SourcingResult.UpdatePOHub(
					ID
					, GetFormValue_String("Part")
					, GetFormValue_NullableInt("Manufacturer")
					, GetFormValue_String("DateCode")
					, GetFormValue_NullableInt("Product")
					, GetFormValue_NullableInt("Package")
					, GetFormValue_NullableInt("Quantity")
					, GetFormValue_NullableDouble("Price")
					, 0
					, GetFormValue_NullableInt("OfferStatus")
					, GetFormValue_NullableInt("Supplier")
					, GetFormValue_NullableByte("ROHS",0)
					, GetFormValue_String("Notes")
					, LoginID
                    , GetFormValue_NullableDouble("SUPPrice")
                    , EstimatedShippingCost
                    , DeliveryDate
                    , SessionManager.IsPOHub??false
                    , GetFormValue_String("SPQ")
                    , GetFormValue_String("LeadTime")
                    , GetFormValue_String("ROHSStatus")
                    , GetFormValue_String("FactorySealed")
                    //, GetFormValue_String("MSL")
                    , ""
                    , GetFormValue_String("SupplierTotalQSA")
                    , GetFormValue_String("SupplierMOQ")
                    , GetFormValue_String("SupplierLTB")
                    , GetFormValue_NullableInt("Region")
                    , GetFormValue_NullableInt("Currency")
                    , GetFormValue_NullableInt("LinkCurrencyNo")  
                     , GetFormValue_NullableInt("MSL")  
                    , GetFormValue_NullableInt("SupplierWarranty")  
                    //[002] start
                    , GetFormValue_Boolean("TestingRecommended")
                    , GetFormValue_NullableInt("Priority")
                    , GetFormValue_NullableInt("CountryOfOriginNo")
                    , GetFormValue_String("ChangedFields")
                    , GetFormValue_Boolean("PartWatchMatch")
                    , GetFormValue_String("SellPriceLessReason")
                    , GetFormValue_NullableInt("TypeOfSupplier")
                    , GetFormValue_NullableInt("ReasonForSupplier")
                    , GetFormValue_NullableInt("RiskOfSupplier")
                    , GetFormValue_NullableInt("CountryNo")

                //[002] end
                );
				JsonObject jsn = new JsonObject();
				jsn.AddVariable("Result", blnOK);
				OutputResult(jsn);
				jsn.Dispose(); jsn = null;
			} catch (Exception e) {
				WriteError(e);
			}
		}

		private void AddNew() {
			try {
                double? EstimatedShippingCost=GetFormValue_NullableDouble("EstimatedShippingCost");
                DateTime? DeliveryDate = GetFormValue_NullableDateTime("DeliveryDate");
            string strLinkMessage ="";
                //add the new sourcing result
                int intNewID = BLL.SourcingResult.InsertSourcingResult(
					GetFormValue_Int("CustomerRequirementID")
					, null
					, GetFormValue_String("Notes")
					, GetFormValue_String("Part")
                    , GetFormValue_NullableInt("Manufacturer")
                    , GetFormValue_String("DateCode")
                    , GetFormValue_NullableInt("Product")
                    , GetFormValue_NullableInt("Package")
                    , GetFormValue_NullableInt("Quantity")
                    , GetFormValue_NullableDouble("Price")
                    , DateTime.Now
                    , LoginID
                    , GetFormValue_NullableInt("OfferStatus")
                    , GetFormValue_NullableInt("SupplierNo")
                    , GetFormValue_NullableByte("ROHS")
                    , SessionManager.ClientID
                    , LoginID
                    , GetFormValue_NullableDouble("SUPPrice")
                    , EstimatedShippingCost
                    , DeliveryDate
                    , SessionManager.IsPOHub ?? false
                    , GetFormValue_String("SPQ")
                    , GetFormValue_String("LeadTime")
                    , GetFormValue_String("ROHSStatus")
                    , GetFormValue_String("FactorySealed")
                    //, GetFormValue_String("MSL")
                    ,""
                    , GetFormValue_String("SupplierTotalQSA")
                    , GetFormValue_String("SupplierMOQ")
                    , GetFormValue_String("SupplierLTB")
                    , GetFormValue_NullableInt("Region")
                    , GetFormValue_NullableInt("Currency")
                    , GetFormValue_NullableInt("MSL")  
                    //[001] start
                    , GetFormValue_NullableInt("SupplierWarranty")
                    //[001] end
                    //[002] start
                    , GetFormValue_Boolean("TestingRecommended")
                    
                    //[002] end
                   , out strLinkMessage
                   , GetFormValue_NullableInt("CountryOfOriginNo")
                   , GetFormValue_String("SellPriceLessReason")
                   , GetFormValue_NullableInt("TypeOfSupplier")
                   , GetFormValue_NullableInt("ReasonForSupplier")
                   , GetFormValue_NullableInt("RiskOfSupplier")
                   , GetFormValue_NullableInt("CountryNo")
                );

				JsonObject jsn = new JsonObject();
				jsn.AddVariable("Result", intNewID > 0);
				jsn.AddVariable("NewID", intNewID);
                jsn.AddVariable("Msg", strLinkMessage);
				OutputResult(jsn);
				jsn.Dispose(); jsn = null;
			} catch (Exception e) {
				WriteError(e);
			}
		}

        /// <summary>
        /// DeleteItem
        /// </summary>
        public void DeleteItem()
        {
            bool IsDeleted = SourcingResult.Delete(GetFormValue_String("ListOfIds"),GetFormValue_Boolean("IsPOHub"));
            JsonObject jsn = new JsonObject();
            jsn.AddVariable("Result", IsDeleted);
            OutputResult(jsn);
            jsn.Dispose();
            jsn = null;
        }

        private void UpdateCurrency()
        {
            string strActualCurCode = GetFormValue_String("ActualCurCode");
            SourcingResult sr = SourcingResult.ConvertPriceToDifferentCurrency(
               null// GetFormValue_NullableInt("OldCurrencyNo")
               , ID
              , null //, GetFormValue_NullableDouble("UpliftPrice")
              , null //, GetFormValue_NullableDouble("SupplierPrice")
               , GetFormValue_NullableInt("SourcingResultNo")
               );
            if (sr != null)
            {
                JsonObject jsn = new JsonObject();
                jsn.AddVariable("NewCurCode", string.Format("{0} ( {1} % )", sr.CurrencyCode, sr.SupplierPercentage.HasValue ? sr.SupplierPercentage.Value : 0));
                jsn.AddVariable("UPLiftPrice", Functions.FormatCurrency(sr.UPLiftPrice));
                jsn.AddVariable("SupPriceInBase", Functions.FormatCurrency(sr.SupplierPrice));
                jsn.AddVariable("ShipCost", Functions.FormatCurrency(sr.EstimatedShippingCost));
                jsn.AddVariable("CurrCodeForSupPrice", string.Format("{0} ( {1} )", strActualCurCode, Functions.FormatCurrency(sr.SupplierPrice, SessionManager.ClientCurrencyCode)));
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            else
            {
                WriteErrorDataNotFound();
            }
        }
        private void DeletePartWatchMatch()
        {
            try
            {
                int Result = BLL.SourcingResult.DeleteHubPartWatchMatch(
                    GetFormValue_String("SourcingResultIDs")
                );

                JsonObject jsn = new JsonObject();
                jsn.AddVariable("Result", Result > 0);
                jsn.AddVariable("NewID", Result);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            catch (Exception e)
            {
                new Errorlog().LogMessage("Error at DeletePartWatchMatch() in CusReqSourcingResults.ashx.cs : " + e.Message);

                WriteError(e);
            }
        }

        private void GetCountry()
        {
            AS6081 cntry = AS6081.GetCountry(GetFormValue_NullableInt("SupplierNo"));
            if (cntry != null)
            {
                JsonObject jsn = new JsonObject();
                jsn.AddVariable("IsCountryFound", cntry.IsCountryFound);
                jsn.AddVariable("CountryName", cntry.CountryName);
                jsn.AddVariable("CountryNo", cntry.CountryNo);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            else
            {
                WriteErrorDataNotFound();
            }

        }

    }
}
