Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.HomeNuggets");Rebound.GlobalTrader.Site.Controls.HomeNuggets.ReceivedOrders=function(n){Rebound.GlobalTrader.Site.Controls.HomeNuggets.ReceivedOrders.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.HomeNuggets.ReceivedOrders.prototype={get_tblReceived:function(){return this._tblReceived},set_tblReceived:function(n){this._tblReceived!==n&&(this._tblReceived=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.HomeNuggets.ReceivedOrders.callBaseMethod(this,"initialize");this.addRefreshEvent(Function.createDelegate(this,this.getData))},dispose:function(){this.isDisposed||(this._tblReceived&&this._tblReceived.dispose(),this._tblReceived=null,Rebound.GlobalTrader.Site.Controls.HomeNuggets.ReceivedOrders.callBaseMethod(this,"dispose"))},setupLoadingState:function(){this._tblReceived.show(!1);Rebound.GlobalTrader.Site.Controls.HomeNuggets.ReceivedOrders.callBaseMethod(this,"setupLoadingState")},getData:function(){this.setupLoadingState();var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/HomeNuggets/ReceivedOrders");n.set_DataObject("ReceivedOrders");n.set_DataAction("GetData");n.addParameter("rowcount",this._intRowCount);n.addDataOK(Function.createDelegate(this,this.getDataComplete));n.addError(Function.createDelegate(this,this.homeNuggetDataError));n.addTimeout(Function.createDelegate(this,this.homeNuggetDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getDataComplete:function(n){var i,r,t,u;for(this.showNoneFoundOrContent(n._result.Count),i=n._result,this._tblReceived.clearTable(),r=0;r<i.Received.length;r++)t=i.Received[r],u=[$RGT_nubButton_PurchaseOrder(t.ID,t.No),$RGT_nubButton_Company(t.CMNo,t.CM),t.Received],this._tblReceived.addRow(u,null);this._tblReceived.show(i.Received.length>0);this.hideLoading()}};Rebound.GlobalTrader.Site.Controls.HomeNuggets.ReceivedOrders.registerClass("Rebound.GlobalTrader.Site.Controls.HomeNuggets.ReceivedOrders",Rebound.GlobalTrader.Site.Controls.HomeNuggets.Base,Sys.IDisposable);