///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference name="~/Controls/IconButton/IconButton.js" />
///<reference name="~/Controls/ReboundTextBox/ReboundTextBox.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 09.09.2010:
// - pass Closed flag from MainInfo to the other nuggets
//
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Pages.Orders");

Rebound.GlobalTrader.Site.Pages.Orders.CustomerRequirementDetail = function(el) { 
	Rebound.GlobalTrader.Site.Pages.Orders.CustomerRequirementDetail.initializeBase(this, [el]);
};

Rebound.GlobalTrader.Site.Pages.Orders.CustomerRequirementDetail.prototype = {

    get_intCustomerRequirementID: function() { return this._intCustomerRequirementID; }, set_intCustomerRequirementID: function(v) { if (this._intCustomerRequirementID !== v) this._intCustomerRequirementID = v; },
    get_ctlMainInfo: function() { return this._ctlMainInfo; }, set_ctlMainInfo: function(v) { if (this._ctlMainInfo !== v) this._ctlMainInfo = v; },
    get_ctlSourcingResults: function() { return this._ctlSourcingResults; }, set_ctlSourcingResults: function(v) { if (this._ctlSourcingResults !== v) this._ctlSourcingResults = v; },
    get_ctlSourcing: function() { return this._ctlSourcing; }, set_ctlSourcing: function(v) { if (this._ctlSourcing !== v) this._ctlSourcing = v; },
    get_lblStatus: function() { return this._lblStatus; }, set_lblStatus: function(v) { if (this._lblStatus !== v) this._lblStatus = v; },
    get_pnlStatus: function() { return this._pnlStatus; }, set_pnlStatus: function(v) { if (this._pnlStatus !== v) this._pnlStatus = v; },

    initialize: function() {
        Rebound.GlobalTrader.Site.Pages.Orders.CustomerRequirementDetail.callBaseMethod(this, "initialize");
    },

    goInit: function() {
        if (this._ctlMainInfo) this._ctlMainInfo.addPartSelected(Function.createDelegate(this, this.selectPart));
        if (this._ctlSourcing) this._ctlSourcing.addSourcingResultAdded(Function.createDelegate(this, this.ctlSourcing_SourcingResultAdded));
        if (this._ctlMainInfo) this._ctlMainInfo.addStartGetData(Function.createDelegate(this, this.mainInfoStartGetData));
        if (this._ctlMainInfo) this._ctlMainInfo.addGotDataOK(Function.createDelegate(this, this.mainInfoGotDataOK));
        if (this._ctlSourcingResults) this._ctlSourcingResults.addAddFormShown(Function.createDelegate(this, this.ctlSourcingResults_AddFormShown));
        Rebound.GlobalTrader.Site.Pages.Orders.CustomerRequirementDetail.callBaseMethod(this, "goInit");
    },

    dispose: function() {
        if (this.isDisposed) return;
        if (this._ctlMainInfo) this._ctlMainInfo.dispose();
        if (this._ctlSourcingResults) this._ctlSourcingResults.dispose();
        if (this._ctlSourcing) this._ctlSourcing.dispose();
        this._ctlMainInfo = null;
        this._ctlSourcingResults = null;
        this._ctlSourcing = null;
        this._lblStatus = null;
        this._pnlStatus = null;
        Rebound.GlobalTrader.Site.Pages.Orders.CustomerRequirementDetail.callBaseMethod(this, "dispose");
    },

    selectPart: function() {
        this._intCustomerRequirementID = this._ctlMainInfo._intCustomerRequirementID;
        this._ctlSourcingResults._blnRequirementClosed = this._ctlMainInfo._blnRequirementClosed;
        this._ctlSourcingResults._intCustomerRequirementID = this._intCustomerRequirementID;
        this._ctlSourcingResults._strPartNo = this._ctlMainInfo.getSelectedPartNo();
        this._ctlSourcingResults.getData();
        if (this._ctlSourcing) this._ctlSourcing._intCustomerRequirementID = this._intCustomerRequirementID;
        if (this._ctlSourcing) this._ctlSourcing._blnRequirementClosed = this._ctlMainInfo._blnRequirementClosed;
        if (this._ctlSourcing) this._ctlSourcing.selectPart(this._ctlMainInfo.getSelectedPartNo());
    },

    ctlSourcing_SourcingResultAdded: function() {
        this._ctlSourcingResults.getData();
        if (this._ctlSourcing) this._ctlSourcing.hideAddForm();
    },

    mainInfoStartGetData: function() {
        if (this._ctlSourcing) this._ctlSourcing.show(false);
        this._ctlSourcingResults.show(false);
    },

    mainInfoGotDataOK: function() {
        if (this._ctlSourcing) this._ctlSourcing._frmRFQ._strPartNo = this._ctlMainInfo.getFieldValue("ctlPartNo");
        if (this._ctlSourcing) this._ctlSourcing._frmRFQ._intROHSNo = this._ctlMainInfo.getFieldValue("hidROHS");
        if (this._ctlSourcing) this._ctlSourcing._frmRFQ._intQuantity = this._ctlMainInfo.getFieldValue("ctlQuantity");
        if (this._ctlSourcing) this._ctlSourcing._frmRFQ._strManufacturer = this._ctlMainInfo.getFieldValue("hidManufacturer");
        if (this._ctlSourcing) this._ctlSourcing._frmRFQ._intManufacturerNo = this._ctlMainInfo.getFieldValue("hidManufacturerNo");
        if (this._ctlSourcing) this._ctlSourcing._frmRFQ._strProduct = this._ctlMainInfo.getFieldValue("ctlProduct");
        if (this._ctlSourcing) this._ctlSourcing._frmRFQ._intProductNo = this._ctlMainInfo.getFieldValue("hidProductID");
        if (this._ctlSourcing) this._ctlSourcing._frmRFQ._strPackage = this._ctlMainInfo.getFieldValue("ctlPackage");
        if (this._ctlSourcing) this._ctlSourcing._frmRFQ._intPackageNo = this._ctlMainInfo.getFieldValue("hidPackageID");
        if (this._ctlSourcing) this._ctlSourcing._frmRFQ._strDateCode = this._ctlMainInfo.getFieldValue("ctlDateCode");
        if (this._ctlSourcing) this._ctlSourcing._frmRFQ._dtmRequired = this._ctlMainInfo.getFieldValue("ctlDateRequired");
        this._ctlSourcingResults._intCompanyID = this._ctlMainInfo.getFieldValue("hidCompanyID");
        if (this._ctlSourcing) this._ctlSourcing.show(true);
        this._ctlSourcingResults.show(true);
        $R_FN.setInnerHTML(this._lblStatus, this._ctlMainInfo.getFieldValue("hidDisplayStatus"));
    },

    ctlSourcingResults_AddFormShown: function() {
        this._ctlSourcingResults._frmAdd.setFieldValue("ctlManufacturer", this._ctlMainInfo.getFieldValue("hidManufacturerNo"), null, this._ctlMainInfo.getFieldValue("hidManufacturer"));
        this._ctlSourcingResults._frmAdd.setFieldValue("ctlDateCode", this._ctlMainInfo.getFieldValue("ctlDateCode"));
        this._ctlSourcingResults._frmAdd.setFieldValue("ctlProduct", this._ctlMainInfo.getFieldValue("hidProductID"), null, this._ctlMainInfo.getFieldValue("ctlProduct"));
        this._ctlSourcingResults._frmAdd.setFieldValue("ctlPackage", this._ctlMainInfo.getFieldValue("hidPackageID"), null, this._ctlMainInfo._ctlPackage);
        this._ctlSourcingResults._frmAdd.setFieldValue("ctlQuantity", this._ctlMainInfo.getFieldValue("ctlQuantity"));
        this._ctlSourcingResults._frmAdd.setFieldValue("ctlCurrency", this._ctlMainInfo.getFieldValue("hidCurrencyID"));
        this._ctlSourcingResults._frmAdd.setFieldValue("ctlROHS", this._ctlMainInfo.getFieldValue("hidROHS"));
        this._ctlSourcingResults._frmAdd.setFieldValue("ctlMSL", this._ctlMainInfo.getFieldValue("hidMSL"));
    }

};
Rebound.GlobalTrader.Site.Pages.Orders.CustomerRequirementDetail.registerClass("Rebound.GlobalTrader.Site.Pages.Orders.CustomerRequirementDetail", Rebound.GlobalTrader.Site.Pages.Content, Sys.IDisposable);
