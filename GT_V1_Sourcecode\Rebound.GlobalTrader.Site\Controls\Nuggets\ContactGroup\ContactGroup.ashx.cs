﻿using Rebound.GlobalTrader.BLL;
using Rebound.GlobalTrader.Site.Enumerations;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Web;
using System.Web.Script.Serialization;
using System.Web.Services;

namespace Rebound.GlobalTrader.Site.Controls.Nuggets.Data
{
    [WebService(Namespace = "http://tempuri.org/")]
    [WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    /// <summary>
    /// Summary description for ContactGroup
    /// </summary>
    public class ContactGroup : Rebound.GlobalTrader.Site.Data.Base
    {
        public override void ProcessRequest(HttpContext context)
        {
            if (base.init(context))
            {
                if (context.Request.QueryString["action"] != null)
                    Action = context.Request.QueryString["action"];
                switch (Action)
                {
                    case "GetData": GetData(context); break;
                    case "SaveData": SaveData(context); break;
                    case "AutoSearch": AutoSearch(context); break;
                    case "GetClientLot": GetClientLot(context); break;
                    case "GetLotData": GetLotData(context); break;
                    default: WriteErrorActionNotFound(); break;
                }
            }
        }

        private void GetData(HttpContext context)
        {
            List<Rebound.GlobalTrader.BLL.ContactGroup> lst = Manufacturer.GetDataByNameorCode(
                context.Request.QueryString["Name"]
                , context.Request.QueryString["Code"]
                , context.Request.QueryString["Type"]
            );
            //JsonObject jsn = new JsonObject();
            ////jsn.AddVariable("TotalRecords", (lst.Count > 0) ? lst[0].RowCnt : 0);
            //JsonObject jsnRowsArray = new JsonObject(true);
            List<Rebound.GlobalTrader.BLL.ContactGroup> lstContacts = new List<Rebound.GlobalTrader.BLL.ContactGroup>(0);
            foreach (Rebound.GlobalTrader.BLL.ContactGroup mf in lst)
            {
                Rebound.GlobalTrader.BLL.ContactGroup contact = new Rebound.GlobalTrader.BLL.ContactGroup();
                contact.Id = mf.Id;
                contact.ContactName = mf.ContactName;
                contact.Code = mf.Code;
                lstContacts.Add(contact);
                contact = null;
            }
            //jsn.AddVariable("Results", jsnRowsArray);
            var serializer = new JavaScriptSerializer();
            var serializedResult = serializer.Serialize(lstContacts);
            context.Response.Write(serializedResult);
            //OutputResult(jsn);
            //jsnRowsArray.Dispose(); jsnRowsArray = null;
            //jsn.Dispose(); jsn = null;
        }

        private void SaveData(HttpContext context)
        {
            Rebound.GlobalTrader.DAL.InsertedData data = new Rebound.GlobalTrader.DAL.InsertedData();
            try
            {
                int output = Manufacturer.SaveGroupData(
                    context.Request.QueryString["GrpName"]
                    , context.Request.QueryString["GrpCode"]
                    , context.Request.QueryString["ContactGrpType"]
                    , context.Request.QueryString["SelManf"]
                );
                data.Id = output;
                data.ErrorMsg = "";
                if (output == -1)
                {
                    data.ErrorMsg = "Group name or group code already exists.";
                }
                else if (output < 1)
                {
                    data.ErrorMsg = "Error in inserting";
                }
            }
            catch (Exception ex)
            {
                data.ErrorMsg = ex.Message;
            }
            var serializer = new JavaScriptSerializer();
            var serializedResult = serializer.Serialize(data);
            context.Response.Write(serializedResult);
            data = null;

        }

        private void AutoSearch(HttpContext context)
        {
            System.Data.DataTable dt = Manufacturer.AutoSearch(context.Request.QueryString["GroupName"], context.Request.QueryString["GroupType"]);
            List<string> customers = new List<string>();
            foreach (DataRow dr in dt.Rows)
            {
                customers.Add(dr[0].ToString());
            }
            //string jsonresult = ConvertDataTableToJSON(dt);
            //context.Response.Write(jsonresult);
            var serializer = new JavaScriptSerializer();
            var serializedResult = serializer.Serialize(customers.ToArray());
            context.Response.Write(serializedResult);
            //context.Response.Write(customers.ToArray());
        }

        private string ConvertDataTableToJSON(DataTable dt)
        {
            System.Web.Script.Serialization.JavaScriptSerializer serializer = new System.Web.Script.Serialization.JavaScriptSerializer();
            List<Dictionary<string, object>> rows = new List<Dictionary<string, object>>();
            Dictionary<string, object> row;
            foreach (DataRow dr in dt.Rows)
            {
                row = new Dictionary<string, object>();
                foreach (DataColumn col in dt.Columns)
                {
                    row.Add(col.ColumnName, dr[col]);
                }
                rows.Add(row);
            }
            return serializer.Serialize(rows);
        }

        private void GetLotData(HttpContext context)
        {
            int LotNo = string.IsNullOrEmpty(context.Request.QueryString["LotNo"]) ? 0 : int.Parse(context.Request.QueryString["LotNo"]);
            int ClientNo = SessionManager.ClientID ?? 0;
            List<Rebound.GlobalTrader.BLL.ContactGroup> lst = Manufacturer.GetDataByLot(LotNo, ClientNo );
            List<Rebound.GlobalTrader.BLL.ContactGroup> lstLots = new List<Rebound.GlobalTrader.BLL.ContactGroup>(0);
            foreach (Rebound.GlobalTrader.BLL.ContactGroup stk in lst)
            {
                Rebound.GlobalTrader.BLL.ContactGroup LotDetails = new Rebound.GlobalTrader.BLL.ContactGroup();
                LotDetails.ClientNo =          stk.ClientNo;
                LotDetails.StockId =            stk.StockId;
                LotDetails.Part =               stk.Part;
                LotDetails.SupplierPart =       stk.SupplierPart;
                LotDetails.QuantityInStock =    stk.QuantityInStock;
                LotDetails.QuantityOnOrder =    stk.QuantityOnOrder;
                LotDetails.QuantityAvailable =  stk.QuantityAvailable;
                LotDetails.QuantityAllocated =  stk.QuantityAllocated;
                LotDetails.LandedCost =         stk.LandedCost;
                LotDetails.Unavailable =        stk.Unavailable;
                LotDetails.SupplierName =       stk.SupplierName;
                LotDetails.PODeliveryDate =     stk.PODeliveryDate;
                LotDetails.PurchaseOrderNumber =stk.PurchaseOrderNumber;
                LotDetails.CustomerRMANumber =  stk.CustomerRMANumber;
                LotDetails.CustomerRMADate =    stk.CustomerRMADate;
                LotDetails.WarehouseName =      stk.WarehouseName;
                LotDetails.Location = stk.Location;
                LotDetails.POSerialNo = stk.POSerialNo;
                LotDetails.LotCode = stk.LotCode;
                LotDetails.LotName = stk.LotName;
                LotDetails.ManufacturerCode = stk.ManufacturerCode;
                LotDetails.ManufacturerName = stk.ManufacturerName;
                LotDetails.ProductDescription = stk.ProductDescription;
                LotDetails.BookedLotQuoteNo = stk.BookedLotQuoteNo;
                LotDetails.QuoteNumber = stk.QuoteNumber;
                LotDetails.IsBookedLotQuote = stk.IsBookedLotQuote;
                //lot by SO line
                LotDetails.IsBookedLotSO = stk.IsBookedLotSO;
                LotDetails.SONumber = stk.SONumber;
                LotDetails.IsBookedLotSO = stk.IsBookedLotSO;



                lstLots.Add(LotDetails);
                LotDetails = null;
            }
          
            var serializer = new JavaScriptSerializer() { MaxJsonLength = 86753090 };
            var serializedResult = serializer.Serialize(lstLots);
            context.Response.Write(serializedResult);
           
        }
        public void GetClientLot(HttpContext context)
        {
            try
            {
                int clientType_con = 0;
                int ClientNo = SessionManager.ClientID ?? 0;
                DataTable dtClient = BLL.Stock.GetClientLot(ClientNo, clientType_con);
                context.Response.Write(ConvertDataTableToJSON(dtClient));

            }
            catch (Exception ex)
            {
                WriteError(ex);
            }
        }
    }
}