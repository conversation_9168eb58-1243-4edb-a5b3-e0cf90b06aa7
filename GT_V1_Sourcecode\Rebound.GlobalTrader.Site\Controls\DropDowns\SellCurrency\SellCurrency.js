Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");Rebound.GlobalTrader.Site.Controls.DropDowns.SellCurrency=function(n){Rebound.GlobalTrader.Site.Controls.DropDowns.SellCurrency.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.DropDowns.SellCurrency.prototype={get_intGlobalLoginClientNo:function(){return this._intGlobalLoginClientNo},set_intGlobalLoginClientNo:function(n){this._intGlobalLoginClientNo!==n&&(this._intGlobalLoginClientNo=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.DropDowns.SellCurrency.callBaseMethod(this,"initialize");this.addSetupDataCall(Function.createDelegate(this,this.setupDataCall));this.addGetDataOK(Function.createDelegate(this,this.dataCallOK))},dispose:function(){this.isDisposed||(this._intGlobalLoginClientNo=null,Rebound.GlobalTrader.Site.Controls.DropDowns.SellCurrency.callBaseMethod(this,"dispose"))},setupDataCall:function(){this._objData.set_PathToData("controls/DropDowns/SellCurrency");this._objData.set_DataObject("SellCurrency");this._objData.set_DataAction("GetData");this._objData.addParameter("GlobalLoginClientNo",this._intGlobalLoginClientNo)},dataCallOK:function(){var t=this._objData._result,n;if(t.Currencies)for(n=0;n<t.Currencies.length;n++)this.addOption(t.Currencies[n].Name,t.Currencies[n].ID,t.Currencies[n].Code)}};Rebound.GlobalTrader.Site.Controls.DropDowns.SellCurrency.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.SellCurrency",Rebound.GlobalTrader.Site.Controls.DropDowns.Base);