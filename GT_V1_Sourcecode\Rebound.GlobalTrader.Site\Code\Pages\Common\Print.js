Type.registerNamespace("Rebound.GlobalTrader.Site.Pages");Rebound.GlobalTrader.Site.Pages.Print=function(){Rebound.GlobalTrader.Site.Pages.Print.initializeBase(this);this._frmEmail=null;this._pnlEmailLinks=null;this._strInitialToEmail="";this._strInitialToContactName="";this._strInitialSubject=""};Rebound.GlobalTrader.Site.Pages.Print.prototype={get_frmEmail:function(){return this._frmEmail},set_frmEmail:function(n){this._frmEmail!==n&&(this._frmEmail=n)},get_pnlEmailLinks:function(){return this._pnlEmailLinks},set_pnlEmailLinks:function(n){this._pnlEmailLinks!==n&&(this._pnlEmailLinks=n)},get_strInitialToEmail:function(){return this._strInitialToEmail},set_strInitialToEmail:function(n){this._strInitialToEmail!==n&&(this._strInitialToEmail=n)},get_strInitialToContactName:function(){return this._strInitialToContactName},set_strInitialToContactName:function(n){this._strInitialToContactName!==n&&(this._strInitialToContactName=n)},get_strInitialSubject:function(){return this._strInitialSubject},set_strInitialSubject:function(n){this._strInitialSubject!==n&&(this._strInitialSubject=n)},initialize:function(){Rebound.GlobalTrader.Site.WebServices.CheckLoggedIn(Function.createDelegate(this,this.loginOK),Function.createDelegate(this,this.closeWindow));this._frmEmail&&(this._frmEmail.addCancel(Function.createDelegate(this,this.closeWindow)),this._frmEmail.addSaveComplete(Function.createDelegate(this,this.closeWindow)),this.showForm(!0),this._frmEmail._ctlTo.clearItems(),this._frmEmail._ctlTo.addItem(null,null,$R_RES.EmailTo,this._strInitialToEmail.trim(),"",String.format("({0})",this._strInitialToContactName)),this._frmEmail.setFieldValue("ctlSubject",this._strInitialSubject))},dispose:function(){this.isDisposed||(this._frmEmail&&this._frmEmail.dispose(),this._frmEmail=null,this._pnlEmailLinks=null,this._strInitialToEmail=null,this._strInitialToContactName=null,this._strInitialSubject=null,this.isDisposed=!0)},loginOK:function(n){n||window.close()},closeWindow:function(){window.close()},showForm:function(n){this._frmEmail.show(n);$R_FN.showElement(this._pnlEmailLinks,n)}};Rebound.GlobalTrader.Site.Pages.Print.registerClass("Rebound.GlobalTrader.Site.Pages.Print",null,Sys.IDisposable);