Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.ItemSearch");Rebound.GlobalTrader.Site.Controls.ItemSearch.PReqs=function(n){Rebound.GlobalTrader.Site.Controls.ItemSearch.PReqs.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.ItemSearch.PReqs.prototype={initialize:function(){Rebound.GlobalTrader.Site.Controls.ItemSearch.PReqs.callBaseMethod(this,"initialize");this.addSetupData(Function.createDelegate(this,this.doSetupData));this.addGetDataComplete(Function.createDelegate(this,this.doGetDataComplete))},dispose:function(){this.isDisposed||Rebound.GlobalTrader.Site.Controls.ItemSearch.PReqs.callBaseMethod(this,"dispose")},doSetupData:function(){this._objData.set_PathToData("controls/ItemSearch/PReqs");this._objData.set_DataObject("PReqs");this._objData.set_DataAction("GetData");this._objData.addParameter("SalesOrderNoLo",this.getFieldValue_Min("ctlSalesOrderNo"));this._objData.addParameter("SalesOrderNoHi",this.getFieldValue_Max("ctlSalesOrderNo"));this._objData.addParameter("Part",this.getFieldValue("ctlPartNo"));this._objData.addParameter("CM",this.getFieldValue("ctlCompany"));this._objData.addParameter("DateOrderedFrom",this.getFieldValue("ctlDateOrderedFrom"));this._objData.addParameter("DateOrderedTo",this.getFieldValue("ctlDateOrderedTo"))},doGetDataComplete:function(){for(var n,i,t=0,r=this._objResult.Results.length;t<r;t++)n=this._objResult.Results[t],i=[n.No+String.format(" ({0})",n.LineNo),$R_FN.setCleanTextValue(n.CMName),$R_FN.writePartNo(n.Part,n.ROHS),n.Price,n.Quantity,$R_FN.setCleanTextValue(n.Date),$R_FN.setCleanTextValue(n.DatePromised)],this._tblResults.addRow(i,n.LineID,!1),i=null,n=null}};Rebound.GlobalTrader.Site.Controls.ItemSearch.PReqs.registerClass("Rebound.GlobalTrader.Site.Controls.ItemSearch.PReqs",Rebound.GlobalTrader.Site.Controls.ItemSearch.Base,Sys.IDisposable);