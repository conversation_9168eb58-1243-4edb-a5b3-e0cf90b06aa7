///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//
// RP 30.10.2009:
// - fix glitch with textbox restricted to numerics on first try for Sourcing
//Marker     changed by      date         Remarks
//[001]      Vinay           10/07/2013   ESMS Ref:23 - GT having some .14 ASP.Net warnings
//[002]      Abhinav         25/04/2014   ESMS #111 Quick Jump for NPR
//[003]      Aashu Singh      30/05/2018  Quick Jump in Global Warehouse
//[004]      Aashu Singh      28-Nov-2018 Show customer requirement all info in tree view. 
//
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.LeftNuggets");

Rebound.GlobalTrader.Site.Controls.LeftNuggets.QuickJump = function (element) {
    Rebound.GlobalTrader.Site.Controls.LeftNuggets.QuickJump.initializeBase(this, [element]);
    this._intID = -1;
    this._intMaxRadioButtons = 15;
};

Rebound.GlobalTrader.Site.Controls.LeftNuggets.QuickJump.prototype = {

    get_ibtnGo: function () { return this._ibtnGo; }, set_ibtnGo: function (v) { if (this._ibtnGo !== v) this._ibtnGo = v; },
    get_txt: function () { return this._txt; }, set_txt: function (v) { if (this._txt !== v) this._txt = v; },
    get_rad: function () { return this._rad; }, set_rad: function (v) { if (this._rad !== v) this._rad = v; },
    get_pnlNotFound: function () { return this._pnlNotFound; }, set_pnlNotFound: function (v) { if (this._pnlNotFound !== v) this._pnlNotFound = v; },
    get_enmSection: function () { return this._enmSection; }, set_enmSection: function (v) { if (this._enmSection !== v) this._enmSection = v; },
    get_enmSelectedType: function () { return this._enmSelectedType; }, set_enmSelectedType: function (v) { if (this._enmSelectedType !== v) this._enmSelectedType = v; },

    //[003] start
    get_pnlQJSearchResultTooltip: function () { return this._pnlQJSearchResultTooltip; }, set_pnlQJSearchResultTooltip: function (v) { if (this._pnlQJSearchResultTooltip !== v) this._pnlQJSearchResultTooltip = v; },
    get_pnlQJSearchResult: function () { return this._pnlQJSearchResult; }, set_pnlQJSearchResult: function (v) { if (this._pnlQJSearchResult !== v) this._pnlQJSearchResult = v; },
    //[003] end
    //[004] start
    get_ibtnShowRelDoc: function () { return this._ibtnShowRelDoc; }, set_ibtnShowRelDoc: function (v) { if (this._ibtnShowRelDoc !== v) this._ibtnShowRelDoc = v; },
    //[004] end

    initialize: function () {
        Rebound.GlobalTrader.Site.Controls.LeftNuggets.QuickJump.callBaseMethod(this, "initialize");
        $R_IBTN.addClick(this._ibtnGo, Function.createDelegate(this, this.goClick));
        $R_TXTBOX.addEnterPressedEvent(this._txt, Function.createDelegate(this, this.goClick));
        $R_TXTBOX.removeInitialSettings(this._txt);
        for (var i = 0; i < this._intMaxRadioButtons; i++) {
            var rad = $get(String.format("{0}_{1}", this._rad.id, i));
            if (rad) $addHandler(rad, "click", Function.createDelegate(this, this.changedSelection));
            rad = null;
        }
        //[004] start
        if (this._ibtnShowRelDoc) {
            $R_IBTN.addClick(this._ibtnShowRelDoc, Function.createDelegate(this, this.showRelatedDocument));
        }
        //[004] end
        this.changedSelection();
    },

    dispose: function () {
        if (this.isDisposed) return;
        if (this._ibtnGo) $R_IBTN.clearHandlers(this._ibtnGo);
        for (var i = 0; i < this._intMaxRadioButtons; i++) {
            var rad = $get(String.format("{0}_{1}", this._rad.id, i));
            if (rad) $clearHandlers(rad);
            rad = null;
        }
        if (this._txt) $R_TXTBOX.clearEvents(this._txt);
        this._txt = null;
        this._rad = null;
        this._pnlNotFound = null;
        this._ibtnGo = null;
        //[003] start
        this._pnlQJSearchResultTooltip = null;
        this._pnlQJSearchResult = null;
        //[003] end
        Rebound.GlobalTrader.Site.Controls.LeftNuggets.QuickJump.callBaseMethod(this, "dispose");
    },

    changedSelection: function () {
        this.getSelectedItem();
      
        // alert(this._enmSection);
        // alert($R_ENUM$QuickJumpType.HUBRFQ);
        if ((this._enmSection == $R_ENUM$QuickJumpSection.Warehouse && this._enmSelectedType == $R_ENUM$QuickJumpType.Stock)
        || (this._enmSection == $R_ENUM$QuickJumpSection.Warehouse && this._enmSelectedType == $R_ENUM$QuickJumpType.StockNumber)
        || (this._enmSection == $R_ENUM$QuickJumpSection.Orders && this._enmSelectedType == $R_ENUM$QuickJumpType.Sourcing)
            || (this._enmSection == $R_ENUM$QuickJumpSection.Orders && this._enmSelectedType == $R_ENUM$QuickJumpType.HUBRFQ)) {
            $R_TXTBOX.setMode(this._txt, $R_ENUM$TextBoxMode.Normal);
            $R_TXTBOX.setUpperCaseOnly(this._txt, true);
            this._txt.value = "";
            this._txt.setAttribute("Maxlength", 40);
        } else if (this._enmSection == $R_ENUM$QuickJumpSection.Contact) {
            $R_TXTBOX.setMode(this._txt, $R_ENUM$TextBoxMode.Normal);
            $R_TXTBOX.setUpperCaseOnly(this._txt, false);
            this._txt.setAttribute("Maxlength", 40);
        }
            //[002] start
        else if (this._enmSection == $R_ENUM$QuickJumpSection.Warehouse && this._enmSelectedType == $R_ENUM$QuickJumpType.NPR) {
            $R_TXTBOX.setMode(this._txt, $R_ENUM$TextBoxMode.NumericDash);
            this._txt.setAttribute("Maxlength", 30);
        }

            //[002] end
        else {
            if (isNaN(parseInt(this._txt.value, 10))) this._txt.value = "";
            $R_TXTBOX.setMode(this._txt, $R_ENUM$TextBoxMode.Numeric);
            $R_TXTBOX.setUpperCaseOnly(this._txt, false);
            this._txt.setAttribute("Maxlength", 10);
        }
    },
    //[001] code start
    goClick: function () {

        this.showNotFound(false);
        if (this._txt.value.trim().length < 1) return;
        this._txt.value = this._txt.value.trim();
        switch (this._enmSection) {
            case $R_ENUM$QuickJumpSection.Warehouse:
                switch (this._enmSelectedType) {
                    case $R_ENUM$QuickJumpType.ReceivePurchaseOrder: if (!$R_FN.checkNumeric(this._txt)) { this.showMessage(true); return; } this.doDataCall("GetPurchaseOrderID", Function.createDelegate(this, this.goPOComplete)); break;
                    case $R_ENUM$QuickJumpType.ShipSalesOrder: if (!$R_FN.checkNumeric(this._txt)) { this.showMessage(true); return; } this.doDataCall("GetSalesOrderID", Function.createDelegate(this, this.goSOComplete)); break;
                    case $R_ENUM$QuickJumpType.ReceiveCustomerRMA: if (!$R_FN.checkNumeric(this._txt)) { this.showMessage(true); return; } this.doDataCall("GetCRMAID", Function.createDelegate(this, this.goCRMAComplete)); break;
                    case $R_ENUM$QuickJumpType.ShipSupplierRMA: if (!$R_FN.checkNumeric(this._txt)) { this.showMessage(true); return; } this.doDataCall("GetSRMAID", Function.createDelegate(this, this.goSRMAComplete)); break;
                    case $R_ENUM$QuickJumpType.GoodsIn: if (!$R_FN.checkNumeric(this._txt)) { this.showMessage(true); return; } this.doDataCall("GetGoodsInID", Function.createDelegate(this, this.goGoodsInComplete)); break;
                    case $R_ENUM$QuickJumpType.Stock: location.href = $RGT_gotoURL_StockBrowse(this._txt.value.toUpperCase()); break;
                        //[002] start       
                    case $R_ENUM$QuickJumpType.NPR: this.getNPRIDLine(this._txt.value); break; //
                        //[002] end        
                   // case $R_ENUM$QuickJumpType.StockNumber: location.href = $RGT_gotoURL_Stock(this._txt.value.toUpperCase()); break;
                    case $R_ENUM$QuickJumpType.StockNumber: if (!$R_FN.checkNumeric(this._txt)) { this.showMessage(true); return; } this.doDataCall("GetStockID", Function.createDelegate(this, this.goStockComplete)); break;
                }
                break;

            case $R_ENUM$QuickJumpSection.Orders:
                switch (this._enmSelectedType) {
                    case $R_ENUM$QuickJumpType.CustomerRequirement: if (!$R_FN.checkNumeric(this._txt)) { this.showMessage(true); return; } this.doDataCall("GetRequirementID", Function.createDelegate(this, this.goRequirementComplete)); break;
                    case $R_ENUM$QuickJumpType.Quote: if (!$R_FN.checkNumeric(this._txt)) { this.showMessage(true); return; } this.doDataCall("GetQuoteID", Function.createDelegate(this, this.goQuoteComplete)); break;
                    case $R_ENUM$QuickJumpType.PurchaseOrder: if (!$R_FN.checkNumeric(this._txt)) { this.showMessage(true); return; } this.doDataCall("GetPurchaseOrderID", Function.createDelegate(this, this.goPOComplete)); break;
                    case $R_ENUM$QuickJumpType.SalesOrder: if (!$R_FN.checkNumeric(this._txt)) { this.showMessage(true); return; } this.doDataCall("GetSalesOrderID", Function.createDelegate(this, this.goSOComplete)); break;
                    case $R_ENUM$QuickJumpType.Invoice: if (!$R_FN.checkNumeric(this._txt)) { this.showMessage(true); return; } this.doDataCall("GetInvoiceID", Function.createDelegate(this, this.goInvoiceComplete)); break;
                    case $R_ENUM$QuickJumpType.CustomerRMA: if (!$R_FN.checkNumeric(this._txt)) { this.showMessage(true); return; } this.doDataCall("GetCRMAID", Function.createDelegate(this, this.goCRMAComplete)); break;
                    case $R_ENUM$QuickJumpType.SupplierRMA: if (!$R_FN.checkNumeric(this._txt)) { this.showMessage(true); return; } this.doDataCall("GetSRMAID", Function.createDelegate(this, this.goSRMAComplete)); break;
                    case $R_ENUM$QuickJumpType.CreditNote: if (!$R_FN.checkNumeric(this._txt)) { this.showMessage(true); return; } this.doDataCall("GetCreditID", Function.createDelegate(this, this.goCreditComplete)); break;
                    case $R_ENUM$QuickJumpType.DebitNote: if (!$R_FN.checkNumeric(this._txt)) { this.showMessage(true); return; } this.doDataCall("GetDebitID", Function.createDelegate(this, this.goDebitComplete)); break;

                    case $R_ENUM$QuickJumpType.InternalPurchaseOrder: if (!$R_FN.checkNumeric(this._txt)) { this.showMessage(true); return; } this.doDataCall("GetInternalPurchaseOrderID", Function.createDelegate(this, this.goIPOComplete)); break;
                    case $R_ENUM$QuickJumpType.HUBRFQ: this.doDataCall("GetHUBRFQID", Function.createDelegate(this, this.goHUBRFQComplete)); break;

                    case $R_ENUM$QuickJumpType.Sourcing: location.href = $RGT_gotoURL_Sourcing(this._txt.value.toUpperCase()); break;
                }
                break;

            case $R_ENUM$QuickJumpSection.Contact:
                switch (this._enmSelectedType) {
                    case $R_ENUM$QuickJumpType.Company: location.href = $RGT_gotoURL_CompanyBrowse($R_ENUM$CompanyListType.AllCompanies, this._txt.value); break;
                    case $R_ENUM$QuickJumpType.Customer: location.href = $RGT_gotoURL_CompanyBrowse($R_ENUM$CompanyListType.Customers, this._txt.value); break;
                    case $R_ENUM$QuickJumpType.Manufacturer: location.href = $RGT_gotoURL_ManufacturerBrowse(this._txt.value); break;
                    case $R_ENUM$QuickJumpType.Supplier: location.href = $RGT_gotoURL_CompanyBrowse($R_ENUM$CompanyListType.Suppliers, this._txt.value); break;
                    case $R_ENUM$QuickJumpType.Prospect: location.href = $RGT_gotoURL_CompanyBrowse($R_ENUM$CompanyListType.Prospects, this._txt.value); break;
                    case $R_ENUM$QuickJumpType.Contact: location.href = $RGT_gotoURL_ContactBrowse(this._txt.value); break;
                }
                break;

            case $R_ENUM$QuickJumpSection.Accounts:
                switch (this._enmSelectedType) {
                    case $R_ENUM$QuickJumpType.ReceivedPurchaseOrder: if (!$R_FN.checkNumeric(this._txt)) { this.showMessage(true); return; } this.doDataCall("GetPurchaseOrderID", Function.createDelegate(this, this.goPOComplete)); break;
                    case $R_ENUM$QuickJumpType.ReceivedCustomerRMA: if (!$R_FN.checkNumeric(this._txt)) { this.showMessage(true); return; } this.doDataCall("GetCRMAID", Function.createDelegate(this, this.goCRMAComplete)); break;
                }
                break;
        }
    },
    //[004] start
    showRelatedDocument: function () {
        var actionType = '';
        switch (this._enmSelectedType) {
            case $R_ENUM$QuickJumpType.CustomerRequirement: actionType = 'REQ'; break;
            case $R_ENUM$QuickJumpType.HUBRFQ: actionType = 'BOM'; break;
            case $R_ENUM$QuickJumpType.Quote: actionType = 'Q'; break;
            case $R_ENUM$QuickJumpType.SalesOrder: actionType = 'SO'; break;
            case $R_ENUM$QuickJumpType.Invoice: actionType = 'INV'; break;
            case $R_ENUM$QuickJumpType.PurchaseOrder: actionType = 'PO'; break;
            case $R_ENUM$QuickJumpType.GoodsIn: actionType = 'GI'; break;
            case $R_ENUM$QuickJumpType.Stock: actionType = 'STK'; break;
            case $R_ENUM$QuickJumpType.CustomerRMA: actionType = 'CRMA'; break;
            case $R_ENUM$QuickJumpType.SupplierRMA: actionType = 'SRMA'; break;
            case $R_ENUM$QuickJumpType.CreditNote: actionType = 'CRD'; break;
            case $R_ENUM$QuickJumpType.DebitNote: actionType = 'DBT'; break;
            case $R_ENUM$QuickJumpType.StockNumber: actionType = 'STK'; break;
        }
        if (actionType == '') return;
        if (this._txt.value.trim().length < 1) return;
        location.href = "AllDocumentInformation.aspx?DocNo=" + this._txt.value.trim() + "&ActionType=" + actionType;
        
    }
    ,
    //[004] end
    //[001] code end

    doDataCall: function (strAction, fnOK) {

        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/LeftNuggets/QuickJump");
        obj.set_DataObject("QuickJump");
        obj.set_DataAction(strAction);

        obj.addParameter("No", this._txt.value);

        obj.addDataOK(fnOK);
        obj.addError(Function.createDelegate(this, this.getDataError));
        obj.addTimeout(Function.createDelegate(this, this.getDataError));
        obj.addTimeout(Function.createDelegate(this, this.getDataError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
    },

    getSelectedItem: function () {
        for (var i = 0; i < this._intMaxRadioButtons; i++) {
            var rad = $get(String.format("{0}_{1}", this._rad.id, i));
            if (rad) {
                if (rad.checked) {
                    this._enmSelectedType = Number.parseInvariant(rad.value.toString());
                    return;
                }
            }
        }
    },

    getDataError: function (args) {
        this.showNotFound(true);
    },

    goRequirementComplete: function (args) {
        if (this.checkResult(args._result)) location.href = $RGT_gotoURL_CustomerRequirement(this._intID);
    },

    goQuoteComplete: function (args) {
        if (this.checkResult(args._result)) location.href = $RGT_gotoURL_Quote(this._intID);
    },

    goIPOComplete: function (args) {
        if (this.checkResult(args._result)) location.href = $RGT_gotoURL_InternalPurchaseOrder(this._intID);
    },
    goHUBRFQComplete: function (args) {
        if (this.checkResult(args._result)) location.href = $RGT_gotoURL_BOM(this._intID);
    },
    goPOComplete: function (args) {
        if (this.checkResult(args._result)) {
            var strURL = "";
            //[003] start
            if (this._enmSection == $R_ENUM$QuickJumpSection.Warehouse) {
                if (args._result.QJSearchResult.length == 1) {
                    strURL = $RGT_gotoURL_ReceivePurchaseOrder(this._intID);
                }
                else
                    this.showTopIcons(args._result);
            }

            if (this._enmSection == $R_ENUM$QuickJumpSection.Orders) {
                if (args._result.QJSearchResult.length == 1) {
                    strURL = $RGT_gotoURL_PurchaseOrder(this._intID)
                }
                else
                    this.showTopIcons(args._result);
            }
            //[003] end
            if (this._enmSection == $R_ENUM$QuickJumpSection.Accounts) strURL = $RGT_gotoURL_ReceivedPurchaseOrder(this._intID);
            if (strURL) location.href = strURL;
            strURL = null;
        }

    },
    ////////////////////////////////stock number search//////////////////////////////////////
    //goStockComplete: function (args) {
    //    if (this.checkResult(args._result)) {
    //        var strURL = "";
    //        //[003] start
    //        if (this._enmSection == $R_ENUM$QuickJumpSection.Warehouse) {
    //            if (args._result.QJSearchResult.length == 1) {
    //                strURL = $RGT_gotoURL_Stock(this._intID);
    //            }
    //            else
    //                this.showTopIcons(args._result);
    //        }

            
    //        if (this._enmSection == $R_ENUM$QuickJumpSection.Accounts) strURL = $RGT_gotoURL_Stock(this._intID);
    //        if (strURL) location.href = strURL;
    //        strURL = null;
    //    }

    //},

    goStockComplete: function (args) {
        if (this.checkResult(args._result)) location.href = $RGT_gotoURL_Stock(this._intID);
    },
    ////////////////////////////////end/////////////////////////////////////

    goSOComplete: function (args) {
        if (this.checkResult(args._result)) {
            //[003] start
            if (args._result.QJSearchResult.length == 1) {
                location.href = (this._enmSection == $R_ENUM$QuickJumpSection.Warehouse) ? $RGT_gotoURL_ShipSalesOrder(this._intID) : $RGT_gotoURL_SalesOrder(this._intID);
            }
            else
                this.showTopIcons(args._result);
            //[003] end

        }
        //if (this.checkResult(args._result)) location.href = (this._enmSection == $R_ENUM$QuickJumpSection.Warehouse) ? $RGT_gotoURL_ShipSalesOrder(this._intID) : $RGT_gotoURL_SalesOrder(this._intID);
    },

    goInvoiceComplete: function (args) {
        if (this.checkResult(args._result)) {
            //[003] start
            if (args._result.QJSearchResult.length == 1) {
                location.href = $RGT_gotoURL_Invoice(this._intID)
            }
            else
                this.showTopIcons(args._result);
            //[003] end

        }
    },

    goCRMAComplete: function (args) {
        if (this.checkResult(args._result)) {
            var strURL = "";
            //[003] start
            if (this._enmSection == $R_ENUM$QuickJumpSection.Warehouse) {
                if (args._result.QJSearchResult.length == 1) {
                    strURL = $RGT_gotoURL_ReceiveCRMA(this._intID)
                }
                else
                    this.showTopIcons(args._result);



            };
            //[003] end
            if (this._enmSection == $R_ENUM$QuickJumpSection.Orders) strURL = $RGT_gotoURL_CRMA(this._intID);
            if (this._enmSection == $R_ENUM$QuickJumpSection.Accounts) strURL = $RGT_gotoURL_ReceivedCRMA(this._intID);
            if (strURL) location.href = strURL;
            strURL = null;
        }
    },

    goSRMAComplete: function (args) {
        if (this.checkResult(args._result)) {
            //[003] start
            if (args._result.QJSearchResult.length == 1) {
                location.href = (this._enmSection == $R_ENUM$QuickJumpSection.Warehouse) ? $RGT_gotoURL_ShipSRMA(this._intID) : $RGT_gotoURL_SRMA(this._intID);
            }
            else
                this.showTopIcons(args._result);
            //[003] end

        }
        //if (this.checkResult(args._result)) location.href = (this._enmSection == $R_ENUM$QuickJumpSection.Warehouse) ? $RGT_gotoURL_ShipSRMA(this._intID) : $RGT_gotoURL_SRMA(this._intID);
    },

    goCreditComplete: function (args) {
        if (this.checkResult(args._result)) location.href = $RGT_gotoURL_CreditNote(this._intID);
    },

    goDebitComplete: function (args) {
        if (this.checkResult(args._result)) {
            //[003] start
            if (args._result.QJSearchResult.length == 1) {
                location.href = $RGT_gotoURL_DebitNote(this._intID);
            }
            else
                this.showTopIcons(args._result);
            //[003] end

        }
        //if (this.checkResult(args._result)) location.href = $RGT_gotoURL_DebitNote(this._intID);
    },

    goGoodsInComplete: function (args) {
        //[003] start
        if (this.checkResult(args._result)) {
            if (args._result.QJSearchResult.length == 1) {
                location.href = $RGT_gotoURL_GoodsIn(this._intID);
            }
            else
                this.showTopIcons(args._result);
        }
        //[003] end
    },

    //[002] start
    goNPRGILineComplete: function (args) {
        if (this.checkResult(args._result))
            $RGT_openNPRWindow(args._result.ID, args._result.NPRID);
    },
    //[002] end
    showNotFound: function (bln) {
        $R_FN.showElement(this._pnlNotFound, bln);
        //[001] code start
        $R_FN.setInnerHTML(this._pnlNotFound, $R_RES.Document);
        //[001] code end
    },

    //[001] code start
    showMessage: function (bln) {
        $R_FN.showElement(this._pnlNotFound, bln);
        $R_FN.setInnerHTML(this._pnlNotFound, $R_RES.NumericFieldError);
    },
    //[001] code end

    //[002] start
    getNPRIDLine: function (strValue) {
        var tempID = strValue.split("-");
        if (tempID == null || tempID.length <= 0 || tempID.length > 3) {
            this.showNotFound(true);
            return false;
        }
        this.doDataCall("GetNPRGILineID", Function.createDelegate(this, this.goNPRGILineComplete));
    }, //[002] end

    checkResult: function (res) {
        var blnOK = false;
        //[003] start
        if (res) {
            if (!res.Error) {
                if (res.QJSearchResult !== undefined) {
                    if (res.QJSearchResult.length > 0) { blnOK = true; this._intID = res.QJSearchResult[0].ID; }
                }
                else {
                    if (res.ID > 0) { blnOK = true; this._intID = res.ID; }
                }
            }
        }
        //[003] end
        this.showNotFound(!blnOK);
        return blnOK;
    },
    //[003] start
    setPOResult: function (result) {
        var strOut = "<div class='topMenuRolloverLink'>";
        var strlink = "";
        var url = "";
        for (var i = 0; i < result.QJSearchResult.length; i++) {
            var obj = result.QJSearchResult[i];
            url = $RGT_gotoURL_PurchaseOrder(obj.ID);
            strlink = strlink + "<a href='" + url + "' >" + obj.ItemDescription + "</a><br/>";
        }
        strOut = strOut + strlink + "</div>"
        $R_FN.setInnerHTML(this._pnlQJSearchResult, "");
        $R_FN.setInnerHTML(this._pnlQJSearchResult, strOut);

    },
    setInvoiceResult: function (result) {
        var strOut = "<div class='topMenuRolloverLink'>";
        var strlink = "";
        var url = "";
        for (var i = 0; i < result.QJSearchResult.length; i++) {
            var obj = result.QJSearchResult[i];
            url = $RGT_gotoURL_Invoice(obj.ID);
            strlink = strlink + "<a href='" + url + "' >" + obj.ItemDescription + "</a><br/>";
        }
        strOut = strOut + strlink + "</div>"
        $R_FN.setInnerHTML(this._pnlQJSearchResult, "");
        $R_FN.setInnerHTML(this._pnlQJSearchResult, strOut);

    },
    setDebitNotesResult: function (result) {
        var strOut = "<div class='topMenuRolloverLink'>";
        var strlink = "";
        var url = "";
        for (var i = 0; i < result.QJSearchResult.length; i++) {
            var obj = result.QJSearchResult[i];
            url = $RGT_gotoURL_DebitNote(obj.ID);
            strlink = strlink + "<a href='" + url + "' >" + obj.ItemDescription + "</a><br/>";
        }
        strOut = strOut + strlink + "</div>"
        $R_FN.setInnerHTML(this._pnlQJSearchResult, "");
        $R_FN.setInnerHTML(this._pnlQJSearchResult, strOut);

    },
    setSalesOrderResult: function (result) {
        var strOut = "<div class='topMenuRolloverLink'>";
        var strlink = "";
        var url = "";
        for (var i = 0; i < result.QJSearchResult.length; i++) {
            var obj = result.QJSearchResult[i];
            url = (this._enmSection == $R_ENUM$QuickJumpSection.Warehouse) ? $RGT_gotoURL_ShipSalesOrder(obj.ID) : $RGT_gotoURL_SalesOrder(obj.ID)
            strlink = strlink + "<a href='" + url + "' >" + obj.ItemDescription + "</a><br/>";
        }
        strOut = strOut + strlink + "</div>"
        $R_FN.setInnerHTML(this._pnlQJSearchResult, "");
        $R_FN.setInnerHTML(this._pnlQJSearchResult, strOut);

    },
    setRecieveCRMAResult: function (result) {
        var strOut = "<div class='topMenuRolloverLink'>";
        var strlink = "";
        var url = "";
        for (var i = 0; i < result.QJSearchResult.length; i++) {
            var obj = result.QJSearchResult[i];
            url = $RGT_gotoURL_ReceiveCRMA(obj.ID)
            strlink = strlink + "<a href='" + url + "' >" + obj.ItemDescription + "</a><br/>";
        }
        strOut = strOut + strlink + "</div>"
        $R_FN.setInnerHTML(this._pnlQJSearchResult, "");
        $R_FN.setInnerHTML(this._pnlQJSearchResult, strOut);

    },
    setShipSRMAResult: function (result) {
        var strOut = "<div class='topMenuRolloverLink'>";
        var strlink = "";
        var url = "";
        for (var i = 0; i < result.QJSearchResult.length; i++) {
            var obj = result.QJSearchResult[i];
            url = (this._enmSection == $R_ENUM$QuickJumpSection.Warehouse) ? $RGT_gotoURL_ShipSRMA(obj.ID) : $RGT_gotoURL_SRMA(obj.ID);
            strlink = strlink + "<a href='" + url + "' >" + obj.ItemDescription + "</a><br/>";
        }
        strOut = strOut + strlink + "</div>"
        $R_FN.setInnerHTML(this._pnlQJSearchResult, "");
        $R_FN.setInnerHTML(this._pnlQJSearchResult, strOut);

    },
    setGoodsInResult: function (result) {
        var strOut = "<div class='topMenuRolloverLink'>";
        var strlink = "";
        var url = "";
        for (var i = 0; i < result.QJSearchResult.length; i++) {
            var obj = result.QJSearchResult[i];
            url = $RGT_gotoURL_GoodsIn(obj.ID);
            strlink = strlink + "<a href='" + url + "' >" + obj.ItemDescription + "</a><br/>";
        }
        strOut = strOut + strlink + "</div>"
        $R_FN.setInnerHTML(this._pnlQJSearchResult, "");
        $R_FN.setInnerHTML(this._pnlQJSearchResult, strOut);

    },
    setRecievePOResult: function (result) {
        var strOut = "<div class='topMenuRolloverLink'>";
        var strlink = "";
        var url = "";
        for (var i = 0; i < result.QJSearchResult.length; i++) {
            var obj = result.QJSearchResult[i];
            url = $RGT_gotoURL_ReceivePurchaseOrder(obj.ID);
            strlink = strlink + "<a href='" + url + "' >" + obj.ItemDescription + "</a><br/>";
        }
        strOut = strOut + strlink + "</div>"
        $R_FN.setInnerHTML(this._pnlQJSearchResult, "");
        $R_FN.setInnerHTML(this._pnlQJSearchResult, strOut);

    },


    showTopIcons: function (result) {
        if (this._enmSection == $R_ENUM$QuickJumpSection.Orders) {
            if (this._enmSelectedType == $R_ENUM$QuickJumpType.PurchaseOrder)
                this.setPOResult(result);
            else if (this._enmSelectedType == $R_ENUM$QuickJumpType.Invoice)
                this.setInvoiceResult(result);
            else if (this._enmSelectedType == $R_ENUM$QuickJumpType.DebitNote)
                this.setDebitNotesResult(result);
            else if (this._enmSelectedType == $R_ENUM$QuickJumpType.SalesOrder)
                this.setSalesOrderResult(result);
        }
        else if (this._enmSection == $R_ENUM$QuickJumpSection.Warehouse) {
            if (this._enmSelectedType == $R_ENUM$QuickJumpType.ReceivePurchaseOrder)
                this.setRecievePOResult(result);
            else if (this._enmSelectedType == $R_ENUM$QuickJumpType.ShipSalesOrder)
                this.setSalesOrderResult(result);
            else if (this._enmSelectedType == $R_ENUM$QuickJumpType.ReceiveCustomerRMA)
                this.setRecieveCRMAResult(result);
            else if (this._enmSelectedType == $R_ENUM$QuickJumpType.ShipSupplierRMA)
                this.setShipSRMAResult(result);
            else if (this._enmSelectedType == $R_ENUM$QuickJumpType.GoodsIn)
                this.setGoodsInResult(result);
        }
        clearTimeout(this._intTimeout);
        $R_FN.showElement(this._pnlQJSearchResultTooltip, true);
        this.setToolTipLocation();
    },
    setToolTipLocation: function () {
        if (this._pnlNPRTootTip) {
            if (this._ibtnNPRLabel) this._pnlNPRTootTip.style.top = String.format("{0}px", (Sys.UI.DomElement.getBounds(this._ibtnNPRLabel).y - Sys.UI.DomElement.getBounds(this._pnlNPRTootTip).y) + 15);
            if (this._ibtnNPRLabel) this._pnlNPRTootTip.style.left = String.format("{0}px", (Sys.UI.DomElement.getBounds(this._ibtnNPRLabel).x - Sys.UI.DomElement.getBounds(this._pnlNPRTootTip).x));
        }
    }
    //[003] end

};

Rebound.GlobalTrader.Site.Controls.LeftNuggets.QuickJump.registerClass("Rebound.GlobalTrader.Site.Controls.LeftNuggets.QuickJump", Rebound.GlobalTrader.Site.Controls.LeftNuggets.Base);
