using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;

namespace Rebound.GlobalTrader.Site.Controls.DropDowns.Data {
	[WebService(Namespace = "http://tempuri.org/")]
	[WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
	public class GroupCodeForCompany : Rebound.GlobalTrader.Site.Data.DropDowns.Base {

		public override void ProcessRequest(HttpContext context) {
			SetDropDownType("GroupCodeForCompany");
			base.ProcessRequest(context);
		}

		protected override void GetData() {
			JsonObject jsn = new JsonObject();
			JsonObject jsnList = new JsonObject(true);
			List<ContactGroup> lst = ContactGroup.GetAllGroupCodeForCompany("customer", true);
			for (int i = 0; i < lst.Count; i++) {
				JsonObject jsnItem = new JsonObject();
				jsnItem.AddVariable("ID", lst[i].Id);
				jsnItem.AddVariable("Name", lst[i].ContactName);
				jsnList.AddVariable(jsnItem);
				jsnItem.Dispose(); jsnItem = null;
			}
			lst.Clear(); lst = null;
			jsn.AddVariable("Items", jsnList);
			jsnList.Dispose(); jsnList = null;
			OutputResult(jsn);
			jsn.Dispose(); jsn = null;
		}
	}
}
