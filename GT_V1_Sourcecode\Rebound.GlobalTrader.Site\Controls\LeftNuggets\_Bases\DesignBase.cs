using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.ComponentModel;
using Rebound.GlobalTrader.Site;

namespace Rebound.GlobalTrader.Site.Controls.LeftNuggets {

	public class DesignBase : WebControl, INamingContainer {

		#region Locals

		public Panel pnlOuter;
		public Panel pnlHeader;
		public PlaceHolder plhContent;
		public Panel pnlContent;
		public Literal litTitle;
		public Image imgShow;
		public Image imgHide;
		public Image imgIconOn;
		public Image imgIconOff;
		public HyperLink ancShowHideClick;
		public Image imgShowHideClick;

		#endregion

		#region properties

		private LeftNugget _objLeftNuggetType;
		public LeftNugget LeftNuggetType {
			get { return _objLeftNuggetType; }
			set { _objLeftNuggetType = value; }
		}


		/// <summary>
		/// Is this the first left nugget in the left side (so it can have the shadow on it)
		/// </summary>
		private Boolean _blnIsFirstInLeftside = false;
		public Boolean IsFirstInLeftside {
			get { return _blnIsFirstInLeftside; }
			set { _blnIsFirstInLeftside = value; }
		}

		/// <summary>
		/// Is this initially expanded
		/// </summary>
		private Boolean _blnIsInitiallyExpanded = false;
		public Boolean IsInitiallyExpanded {
			get { return _blnIsInitiallyExpanded; }
			set { _blnIsInitiallyExpanded = value; }
		}

		/// <summary>
		/// Title Resource (from LeftNuggets.resx)
		/// </summary>
		private string _strTitleResource = "";
		public string TitleResource {
			get { return _strTitleResource; }
			set { _strTitleResource = value; }
		}

		/// <summary>
		/// Content container
		/// </summary>
		private ITemplate _tmpContentTemplate = null;
		[PersistenceMode(PersistenceMode.InnerProperty)]
		[TemplateContainer(typeof(Container))]
		public ITemplate Content {
			get { return _tmpContentTemplate; }
			set { _tmpContentTemplate = value; }
		}

		/// <summary>
		/// The type of the Icon corresponding to a CSS class
		/// </summary>
		private string _strIconCssType = "";
		public string IconCssType {
			get { return _strIconCssType; }
			set { _strIconCssType = value; }
		}

		#endregion

		/// <summary>
		/// create controls
		/// </summary>
		protected override void CreateChildControls() {

			//outer
			pnlOuter = ControlBuilders.CreatePanel("leftNugget");

			//header
			pnlHeader = ControlBuilders.CreatePanelInsideParent(pnlOuter, "leftNuggetHeader");

			//show hide click button (javascript event on here)
			ancShowHideClick = ControlBuilders.CreateHyperLinkInsideParent(pnlHeader, "showHideClick", "javascript:void(0);");
			imgShowHideClick = ControlBuilders.CreateImageInsideParent(ancShowHideClick, "", "~/images/x.gif", 210, 29);

			//icon
			HtmlGenericControl divIcon = ControlBuilders.CreateHtmlGenericControlInsideParent(pnlHeader, "div", "icon");
			imgIconOn = ControlBuilders.CreateImageInsideParent(divIcon, "leftNuggetIconOn", "~/images/x.gif");
			imgIconOff = ControlBuilders.CreateImageInsideParent(divIcon, "leftNuggetIconOff", "~/images/x.gif");

			//heading
			HtmlGenericControl h3 = ControlBuilders.CreateHtmlGenericControlInsideParent(pnlHeader, "h3");
			litTitle = ControlBuilders.CreateLiteralInsideParent(h3);

			//show/hide image
			HtmlGenericControl divShowHide = ControlBuilders.CreateHtmlGenericControlInsideParent(pnlHeader, "div", "showHide");
			imgShow = ControlBuilders.CreateImageInsideParent(divShowHide, "leftNuggetShow", "~/images/x.gif");
			imgHide = ControlBuilders.CreateImageInsideParent(divShowHide, "leftNuggetHide", "~/images/x.gif");

			//content
			pnlContent = ControlBuilders.CreatePanelInsideParent(pnlOuter, "leftNuggetContent");
			plhContent = ControlBuilders.CreatePlaceHolderInsideParent(pnlContent);

			//footer
			ControlBuilders.CreateHtmlGenericControlInsideParent(pnlOuter, "div", "leftNuggetFooter");

			//populate containers
			if (_tmpContentTemplate != null) {
				Container cnt = new Container();
				_tmpContentTemplate.InstantiateIn(cnt);
				plhContent.Controls.Add(cnt);
			}

			Controls.Add(pnlOuter);

			base.CreateChildControls();

		}

		/// <summary>
		/// pre render
		/// </summary>
		/// <param name="e"></param>
		protected override void OnPreRender(EventArgs e) {
			if (!_blnIsInitiallyExpanded) pnlOuter.CssClass += " leftNuggetCollapsed";
			if (_blnIsFirstInLeftside) pnlOuter.CssClass += " leftNuggetFirst";
			if (_strIconCssType == "") _strIconCssType = _objLeftNuggetType.Name;
			imgIconOn.CssClass = String.Format("leftNuggetIconOn leftNuggetIconOn_{0}", _strIconCssType);
			imgIconOff.CssClass = String.Format("leftNuggetIconOff leftNuggetIconOff_{0}", _strIconCssType);
			litTitle.Text = Functions.GetGlobalResource("LeftNuggets", (_strTitleResource == "") ? _objLeftNuggetType.Name : _strTitleResource);
			base.OnPreRender(e);
		}

		/// <summary>
		/// Find control in content placeholder
		/// </summary>
		/// <param name="strControlName">Control ID to find</param>
		/// <returns>The found Control or null</returns>
		public object FindContentControl(string strControlName) {
			EnsureChildControls();
			return (Functions.FindControlRecursive(plhContent, strControlName));
		}

		internal void MakeChildControls() {
			EnsureChildControls();
		}
	}

}