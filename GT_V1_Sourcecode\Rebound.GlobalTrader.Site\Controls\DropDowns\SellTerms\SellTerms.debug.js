///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
////[001]      A<PERSON><PERSON>     20-Aug-2018  Provision to add Global Security in Sales Order 
// RP 06.10.2009:
// - Changes due to changes in base class
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");

Rebound.GlobalTrader.Site.Controls.DropDowns.SellTerms = function(element) { 
	Rebound.GlobalTrader.Site.Controls.DropDowns.SellTerms.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.DropDowns.SellTerms.prototype = {
    //[001] start
    get_intGlobalLoginClientNo: function () { return this._intGlobalLoginClientNo; }, set_intGlobalLoginClientNo: function (v) { if (this._intGlobalLoginClientNo !== v) this._intGlobalLoginClientNo = v; },
    //[001] end
	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.DropDowns.SellTerms.callBaseMethod(this, "initialize");
		this.addSetupDataCall(Function.createDelegate(this, this.setupDataCall));
		this.addGetDataOK(Function.createDelegate(this, this.dataCallOK));
	},
	
	dispose: function() { 
	    if (this.isDisposed) return;
	    //[001] start
	    this._intGlobalLoginClientNo = null;
	    //[001] end
		Rebound.GlobalTrader.Site.Controls.DropDowns.SellTerms.callBaseMethod(this, "dispose");
	},
	
	setupDataCall: function() { 		
		this._objData.set_PathToData("controls/DropDowns/SellTerms");
		this._objData.set_DataObject("SellTerms");
		this._objData.set_DataAction("GetData");
	    //[001] start
		this._objData.addParameter("GlobalLoginClientNo", this._intGlobalLoginClientNo);
	    //[001] end
	},
	
	dataCallOK: function() { 
		var result = this._objData._result;
		if (result.Terms){
			for (var i = 0; i < result.Terms.length; i++) {
				this.addOption(result.Terms[i].Name, result.Terms[i].ID);
			}
		}
	}
		
};

Rebound.GlobalTrader.Site.Controls.DropDowns.SellTerms.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.SellTerms", Rebound.GlobalTrader.Site.Controls.DropDowns.Base);
