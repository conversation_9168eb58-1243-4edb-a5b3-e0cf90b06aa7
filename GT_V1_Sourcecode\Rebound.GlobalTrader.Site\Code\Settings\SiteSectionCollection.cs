using System;
using System.Data;
using System.Configuration;
using System.Web.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;

namespace Rebound.GlobalTrader.Site.Settings {

	public class SiteSectionCollection : ConfigurationElementCollection {

		[ConfigurationProperty("siteSection")]
		public SiteSectionElement this[int i] {
			get { return base.BaseGet(i) as SiteSectionElement; }
			set {
				if (base.BaseGet(i) != null) base.BaseRemoveAt(i);
				this.BaseAdd(i, value);
			}
		}
		protected override System.Configuration.ConfigurationElement CreateNewElement() {
			return new SiteSectionElement();
		}

		protected override object GetElementKey(
			System.Configuration.ConfigurationElement el) {
			return ((SiteSectionElement)el).ID;
		}

	}
}
