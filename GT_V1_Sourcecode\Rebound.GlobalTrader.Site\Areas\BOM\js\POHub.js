﻿var poHubControllerUrl = window.location.origin + "/BOM/POHub/";
var dropdownUrl = window.location.origin + "/Controls/Nuggets/CrossMatch/CrossMatch.ashx";
var defaultOption = '<option value="-1">&lt; Select &gt;</option>';
var refershOption = '<option selected="true" value="Loading">loading</option>';
var optionStart = "<option value='";
var optionEnd = "</option>";

function EditSourceRow(rowIndx, grid, row) {
    grid.addClass({ rowIndx: rowIndx, cls: 'pq-row-delete' });
    var sourcingId = grid.getRecId({ rowIndx: rowIndx });
    $('#hdnSourceId').val(sourcingId);
    var supplierId = row.rowData.SupplierNo;
    var supplierName = row.rowData.VendorName;
    var partNo = row.rowData.Part;
    var rohs = row.rowData.ROHS;
    var manufacturerId = row.rowData.ManufacturerNo;
    var manufacturer = row.rowData.ManufacturerName;
    var countryOfOrigin = row.rowData.CountryOfOriginNo;
    var dateCode = row.rowData.DateCode;
    var productId = row.rowData.ProductNo;
    var productName = row.rowData.ProductName;
    var packageId = row.rowData.PackageNo;
    var packageName = row.rowData.PackageName;
    var quantity = row.rowData.ADJQty;
    var buyPrice = row.rowData.Cost;
    var sellPrice = row.rowData.Resale;
    var shippingCost = row.rowData.ShippingCost;
    var currencyId = row.rowData.CurrencyNo;
    var offerStatusId = row.rowData.OfferStatusId;
    var totalQSA = row.rowData.SupplierTotalQSA;
    var moq = row.rowData.MOQ;
    var lastTimeBuy = row.rowData.SupplierLTB;
    var msl = row.rowData.MSLLevelNo;
    var spq = row.rowData.SPQ;
    var leadTime = row.rowData.LT;
    var regionId = row.rowData.RegionNo;
    var deliveryDate = row.rowData.DeliveryDate;
    var factorySealed = row.rowData.FactorySealed;
    var rohsStatus = row.rowData.ROHSStatus;
    var supplierWarranty = row.rowData.SupplierWarranty;
    var notes = row.rowData.Notes;
    var isTestingRecommended = row.rowData.IsTestingRecommended;
    var sellPriceLessReason = row.rowData.Reason;


    $('#lblSupplierName').text(supplierName);
    if (partNo) {
        $('#textEditPartNo').val(partNo);
        $('#search-part').css("display", "none");
        $('#display-part').css("display", "block");
        $('#hiddenEditPartNo').val(partNo);
        $('#lblEditPart').text(partNo);
    } else {
        $('#search-part').css("display", "block");
        $('#display-part').css("display", "none");
        $('#hiddenEditPartNo').val("");
        $('#textEditPartNo').val("");
        $('#lblEditPart').text("");
    }
    BindROHSStatus('ddlEditROHS', rohs >= 0 ? rohs : -1);

    if (manufacturerId && manufacturer.trim() != "") {
        $('#search-manufacturer').css("display", "none");
        $('#display-manufacturer').css("display", "block");
        $('#hiddenEditManufacturer').val(manufacturerId);
        $('#lblEditManufacturer').text(manufacturer);
    } else {
        $('#search-manufacturer').css("display", "block");
        $('#display-manufacturer').css("display", "none");
        $('#hiddenEditManufacturer').val("");
        $('#textEditManufacturer').val("");
        $('#lblEditManufacturer').text("");
    }

    BindCountryOfOrigin('ddlEditCOO', countryOfOrigin > 0 ? countryOfOrigin : -1);
    $('#textEditDateCode').val(dateCode);

    if (productId && productName.trim() != "") {
        $('#search-product').css("display", "none");
        $('#display-product').css("display", "block");
        $('#hiddenEditProductId').val(productId);
        $('#lblEditProduct').text(productName);
    } else {
        $('#search-product').css("display", "block");
        $('#display-product').css("display", "none");
        $('#hiddenEditProductId').val("");
        $('#textEditProduct').val("");
        $('#lblEditProduct').text("");
    }

    if (packageId && packageName.trim() != "") {
        $('#search-package').css("display", "none");
        $('#display-package').css("display", "block");
        $('#hiddenEditPackageId').val(packageId);
        $('#lblEditPackage').text(packageName);
    } else {
        $('#search-package').css("display", "block");
        $('#display-package').css("display", "none");
        $('#hiddenEditPackageId').val("");
        $('#textEdit>Package').val("");
        $('#lblEditPackage').text("");
    }

    $('#textEditQuantity').val(quantity);
    $('#textEditBuyPrice').val(parseFloat(buyPrice).toFixed(4));
    $('#textEditSellPrice').val(parseFloat(sellPrice).toFixed(4));
    $('#textEditShippingCost').val(parseFloat(shippingCost).toFixed(4));
    if (buyPrice > sellPrice) {
        $('#RowEditSellPriceLess').css("display", "block");
        $('#RowEditSellPriceLessReason').css("display", "block");
        $('#textEditSellPriceLessReason').val(SetCleanText(sellPriceLessReason))
    } else {
        $('#RowEditSellPriceLess').css("display", "none");
        $('#RowEditSellPriceLessReason').css("display", "none");
        $('#textEditSellPriceLessReason').val("")
    }
    getSupplierCurrencyDetails(supplierId, currencyId, false);
    BindOfferStatus('ddlEditOfferStatus', offerStatusId > 0 ? offerStatusId : -1);
    $('#textEditTotalQSA').val(totalQSA);
    $('#textEditMOQ').val(moq);
    $('#textEditLTB').val(lastTimeBuy);
    BindMSL('ddlEditMSL', msl > 0 ? msl : -1);
    $('#textEditSPQ').val(spq);
    $('#textEditLeadTime').val(leadTime);
    BindRegion('ddlEditRegion', regionId > 0 ? regionId : -1);
    $('#textEditDeliveryDate').val(getCustomDate(deliveryDate));
    $('#textEditFactorySealed').val(factorySealed);
    $('#textEditROHSStatus').val(rohsStatus);
    $('#textEditSupplierWarranty').val(supplierWarranty);
    $('#textEditNotes').val(SetCleanText(notes));
    $('#chkEditTestingRecommended').prop('checked', isTestingRecommended);

    var modal = document.getElementById("EditSourceModal");
    modal.style.display = "block"
    $('html, body', window.parent.document).animate({ scrollTop: 240 }, "fast");
}

$("#ReselectEditPartNo").click(function () {
    $('#search-part').css("display", "block");
    $('#display-part').css("display", "none");
    $('#hiddenEditPartNo').val("");
    $('#textEditPartNo').val("");
    $('#lblEditPart').text("");
});
$("#textEditPartNo").autocomplete({
    minLength: 4,
    source: function (request, response) {
        // Fetch data
        $.ajax({
            processData: true,
            contentType: 'application/json',
            type: 'POST',
            url: poHubControllerUrl + 'AutoSearchBomManager?Part=' + $("#textEditPartNo").val(),
            dataType: "json",
            success: function (data) {
                autocompleteCount = data.length;
                response(data);
            },
            error: function (err) {
            }
        });
    },
    open: function (event, ui) {
        $("ul").prepend('<li class ="AutoCompleteHeader""><div>' + autocompleteCount + ' result(s)</div></li>');
        autocompleteCount = 0;
    },
    select: function (event, ui) {
        $('#search-part').css("display", "none");
        $('#display-part').css("display", "block");
        $('#hiddenEditPartNo').val(ui.item.PartName);
        $('#lblEditPart').text(SetCleanText(ui.item.PartName, true));
        if (ui.item.ProductNo) {
            $('#search-product').css("display", "none");
            $('#display-product').css("display", "block");
            $('#hiddenEditProductId').val(ui.item.ProductNo);
            $('#lblEditProduct').text(SetCleanText(ui.item.ProductDescription, true));
        }
        if (ui.item.ManufacturerNo) {
            $('#search-manufacturer').css("display", "none");
            $('#display-manufacturer').css("display", "block");
            $('#hiddenEditManufacturer').val(ui.item.ManufacturerNo);
            $('#lblEditManufacturer').text(SetCleanText(ui.item.ManufacturerName, true));
        }
    }
}).autocomplete("instance")._renderItem = function (ul, item) {
    return $("<li>")
        .append("<div>" + SetCleanText(item.PartName, true) + "</div>")
        .appendTo(ul);
};

$('#part-search-icon').click(function () {
    $("#textEditPartNo").autocomplete("enable");
    if ($("#textEditPartNo").val().length > 3) {
        $("#textEditPartNo").autocomplete('search', $("#textEditPartNo").val());
    }
});

$("#ReselectEditManufacturer").click(function () {
    $('#search-manufacturer').css("display", "block");
    $('#display-manufacturer').css("display", "none");
    $('#hiddenEditManufacturer').val("");
    $('#textEditManufacturer').val("");
    $('#lblEditManufacturer').text("");
});
$("#textEditManufacturer").autocomplete({
    minLength: 2,
    source: function (request, response) {
        // Fetch data
        $.ajax({
            processData: true,
            contentType: 'application/json',
            type: 'GET',
            url: dropdownUrl + '?action=GetManufacturer&MrfSearch=' + $("#textEditManufacturer").val(),
            dataType: "json",
            success: function (data) {
                autocompleteCount = data.length;
                response(data);
            },
            error: function (err) {
                console.log("error when fetch manufacturer", err);
            }
        });
    },
    open: function (event, ui) {
        $("ul").prepend('<li class ="AutoCompleteHeader""><div>' + autocompleteCount + ' result(s)</div></li>');
        autocompleteCount = 0;
    },
    select: function (event, ui) {
        $('#search-manufacturer').css("display", "none");
        $('#display-manufacturer').css("display", "block");
        $('#hiddenEditManufacturer').val(ui.item.value);
        $('#lblEditManufacturer').text(SetCleanText(ui.item.label, true));
    }
}).autocomplete("instance")._renderItem = function (ul, item) {
    return $("<li>")
        //.append("<div>" + item.label + "<br>" + item.value + "</div>")
        .append("<div>" + SetCleanText(item.label, true) + "</div>")
        .appendTo(ul);
};


$("#ReselectEditProduct").click(function () {
    $('#search-product').css("display", "block");
    $('#display-product').css("display", "none");
    $('#hiddenEditProductId').val("");
    $('#textEditProduct').val("");
    $('#lblEditProduct').text("");
});
$("#textEditProduct").autocomplete({
    minLength: 2,
    source: function (request, response) {
        // Fetch data
        $.ajax({
            processData: true,
            contentType: 'application/json',
            type: 'GET',
            url: dropdownUrl + '?action=GetProduct&ProdSearch=' + $("#textEditProduct").val(),
            dataType: "json",
            success: function (data) {
                autocompleteCount = data.length;
                response(data);
            },
            error: function (err) {
                console.log("error when fetch product", err);
            }
        });
    },
    open: function (event, ui) {
        $("ul").prepend('<li class ="AutoCompleteHeader""><div>' + autocompleteCount + ' result(s)</div></li>');
        autocompleteCount = 0;
    },
    select: function (event, ui) {
        $('#search-product').css("display", "none");
        $('#display-product').css("display", "block");
        $('#hiddenEditProductId').val(ui.item.value);
        $('#lblEditProduct').text(SetCleanText(ui.item.label, true));
    }
})
    .autocomplete("instance")._renderItem = function (ul, item) {
        return $("<li>")
            .append("<div>" + SetCleanText(item.label, true) + "</div>")
            .appendTo(ul);
    };

$("#ReselectEditPackage").click(function () {
    $('#search-package').css("display", "block");
    $('#display-package').css("display", "none");
    $('#hiddenEditPackageId').val("");
    $('#textEditPackage').val("");
    $('#lblEditPackage').text("");
});
$("#textEditPackage").autocomplete({
    minLength: 2,
    source: function (request, response) {
        // Fetch data
        $.ajax({
            processData: true,
            contentType: 'application/json',
            type: 'GET',
            url: poHubControllerUrl + 'GetPackages?search=' + $("#textEditPackage").val() + '%',
            dataType: "json",
            success: function (data) {
                autocompleteCount = data.length;
                response($.map(data, function (item) {
                    return { label: item.PackageDescription, value: item.PackageId };
                }))


            },
            error: function (err) {
                console.log("error when fetching package", err);
            }
        });
    },
    open: function (event, ui) {
        $("ul").prepend('<li class ="AutoCompleteHeader""><div>' + autocompleteCount + ' result(s)</div></li>');
        autocompleteCount = 0;
    },
    select: function (event, ui) {
        $('#search-package').css("display", "none");
        $('#display-package').css("display", "block");
        $('#hiddenEditPackageId').val(ui.item.value);
        $('#lblEditPackage').text(SetCleanText(ui.item.label, true));
    }
})
    .autocomplete("instance")._renderItem = function (ul, item) {
        return $("<li>")
            .append("<div>" + SetCleanText(item.label, true) + "</div>")
            .appendTo(ul);
    };

function displayReasonSellPriceLess() {
    var buyPrice = parseFloat($('#textEditBuyPrice').val());
    var sellPrice = parseFloat($('#textEditSellPrice').val());

    if (!buyPrice || !sellPrice) return;

    //show reason field
    if (sellPrice < buyPrice) {
        $("#RowEditSellPriceLess").css("display", "block");
        $("#RowEditSellPriceLessReason").css("display", "block");
    } else {
        //clear reason field and hide
        $('#textEditSellPriceLessReason').val('');
        $("#RowEditSellPriceLess").css("display", "none");
        $("#RowEditSellPriceLessReason").css("display", "none");
        $("#RowEditSellPriceLessReasonError").css("display", "none");
    }
};


function validateEditSourceForm() {
    var isValid = true;
    if (!$('#hiddenEditPartNo').val() || $('#lblEditPart').text() == "") {
        $("#RowEditPartNo").css("background-color", "#990000");
        $("#RowEditPartNoError").css("display", "block");
        isValid = false;
    } else {
        $("#RowEditPartNo").css("background-color", "#56954E");
        $("#RowEditPartNoError").css("display", "none");
    }

    if (!$('#hiddenEditManufacturer').val() || $('#lblEditManufacturer').text() == "") {
        $("#RowEditManufacturer").css("background-color", "#990000");
        $("#RowEditManufacturerError").css("display", "block");
        isValid = false;
    } else {
        $("#RowEditManufacturer").css("background-color", "#56954E");
        $("#RowEditManufacturerError").css("display", "none");
    }

    if (!$('#hiddenEditProductId').val() || $('#lblEditProduct').text() == "") {
        $("#RowEditProduct").css("background-color", "#990000");
        $("#RowEditProductError").css("display", "block");
        isValid = false;
    } else {
        $("#RowEditProduct").css("background-color", "#56954E");
        $("#RowEditProductError").css("display", "none");
    }
    var quantity = Number($('#textEditQuantity').val());
    if (isNaN(quantity) || quantity <= 0) {
        $("#RowEditQuantity").css("background-color", "#990000");
        $("#RowEditQuantityError").css("display", "block");
        isValid = false;
    } else {
        $("#RowEditQuantity").css("background-color", "#56954E");
        $("#RowEditQuantityError").css("display", "none");
    }

    var buyPrice = parseFloat($("#textEditBuyPrice").val());
    if (isNaN(buyPrice)) {
        $("#RowEditBuyPrice").css("background-color", "#990000");
        $("#RowEditBuyPriceError").css("display", "block");
        isValid = false;
    } else {
        $("#RowEditBuyPrice").css("background-color", "#56954E");
        $("#RowEditBuyPriceError").css("display", "none");
    }

    var sellPrice = parseFloat($("#textEditSellPrice").val());
    if (isNaN(sellPrice)) {
        $("#RowEditSellPrice").css("background-color", "#990000");
        $("#RowEditSellPriceError").css("display", "block");
        isValid = false;
    } else {
        $("#RowEditSellPrice").css("background-color", "#56954E");
        $("#RowEditSellPriceError").css("display", "none");
    }

    //validate reason sell price less then buy price
    if (!isNaN(sellPrice) && !isNaN(buyPrice)) {
        if (sellPrice < buyPrice && $("#textEditSellPriceLessReason").val().trim() == "") {
            $("#RowEditSellPriceLessReason").css("background-color", "#990000");
            $("#RowEditSellPriceLessReasonError").css("display", "block");
            isValid = false;
        }
        else {
            $("#RowEditSellPriceLessReason").css("background-color", "#56954E");
            $("#RowEditSellPriceLessReasonError").css("display", "none");
        }
    }
    else {
        $("#RowEditSellPriceLess").css("display", "none");
        $("#RowEditSellPriceLessReason").css("display", "none");
        $("#RowEditSellPriceLessReason").css("background-color", "#56954E");
        $("#RowEditSellPriceLessReasonError").css("display", "none");
    }

    if (Number($('#ddlEditCurrency').val() <= 0)) {
        $("#RowEditCurrency").css("background-color", "#990000");
        $("#RowEditCurrencyError").css("display", "block");
        isValid = false;
    } else {
        $("#RowEditCurrency").css("background-color", "#56954E");
        $("#RowEditCurrencyError").css("display", "none");
    }

    return isValid;
}

$('.SaveSource').click(function () {
    if (validateEditSourceForm()) {
        saveEditSourcing();
    }
    else {
        console.log("edit sourcing form is not valid");
        $("#EditSourcingValidationError").css("display", "block");
    }
});

function saveEditSourcing() {
    var qrystr = new URLSearchParams(window.location.search);
    var BomManagerId = qrystr.get("BOM");
    var sourcing = {
        BOMManagerId: BomManagerId,
        SourceId: $('#hdnSourceId').val(),
        PartNo: $('#textEditPartNo').val(),
        ROHS: $("#ddlEditROHS").val(),
        ManufacturerNo: $("#hiddenEditManufacturer").val(),
        CountryOfOrigin: $("#ddlEditCOO").val(),
        DateCode: $('#textEditDateCode').val(),
        ProductNo: $('#hiddenEditProductId').val(),
        PackageNo: $('#hiddenEditPackageId').val(),
        Quantity: $('#textEditQuantity').val(),
        Price: $('#textEditBuyPrice').val(),
        SellPrice: $('#textEditSellPrice').val(),
        ShippingCost: $('#textEditShippingCost').val(),
        Currency: $("#ddlEditCurrency").val(),
        OfferStatus: $('#ddlEditOfferStatus').val(),
        SupplierTotalQSA: $('#textEditTotalQSA').val(),
        SupplierMOQ: $('#textEditMOQ').val(),
        SupplierLTB: $('#textEditLTB').val(),
        MSLNo: $('#ddlEditMSL').val(),
        SPQ: $('#textEditSPQ').val(),
        LeadTime: $('#textEditLeadTime').val(),
        Region: $('#ddlEditRegion').val(),
        DeliveryDate: $('#textEditDeliveryDate').val(),
        FactorySealed: $('#textEditFactorySealed').val(),
        ROHSStatus: $('#textEditROHSStatus').val(),
        SupplierWarranty: $('#textEditSupplierWarranty').val(),
        Notes: $('#textEditNotes').val(),
        Reason: $('#textEditSellPriceLessReason').val(),
        IsTestingRecommended: $('#chkEditTestingRecommended').is(":checked"),
    }

    $.ajax({
        //processData: true,
        contentType: 'application/json',
        type: 'POST',
        url: poHubControllerUrl + 'UpdateAutoSourcing',
        dataType: "json",
        async: true,
        data: JSON.stringify({ sourcing: sourcing }),
        success: function (data) {
            editsource = true;
            LoadAutoSource($('#hdnBomPartId').val(), true);
            editsource = false;
        },
        error: function (err) {
            /*alert(err);*/
            console.log('Error in editsourcing');
        },
        complete: function () {
            resetEditSourcingForm();
            var modal = document.getElementById("EditSourceModal");
            modal.style.display = "none"
            $('html, body', window.parent.document).animate({ scrollTop: 240 }, "fast");
        }
    });
}

function resetEditSourcingForm() {
    //clear form value
    $('#lblSupplierName').text("");
    $('#textEditPartNo').val("");
    $('#hiddenEditPartNo').val("");
    $('#lblEditPart').text("");
    $("#ddlEditROHS").empty().append("--Select--");
    $('#textEditManufacturer').val("");
    $('#hiddenEditManufacturer').val("");
    $('#lblEditManufacturer').text("");
    $("#ddlEditCOO").empty().append("--Select--");
    $('#textEditDateCode').val("");
    $('#textEditProduct').val("");
    $('#hiddenEditProductId').val("");
    $('#lblEditProduct').text("");
    $('#textEditPackage').val("");
    $('#hiddenEditPackageId').val("");
    $('#lblEditPackage').text("");
    $('#textEditQuantity').val("");
    $('#textEditBuyPrice').val("");
    $('#textEditSellPrice').val("");
    $('#textEditShippingCost').val("");
    $("#ddlEditCurrency").empty().append("--Select--");
    $("#ddlEditOfferStatus").empty().append("--Select--");
    $('#textEditTotalQSA').val("");
    $('#textEditMOQ').val("");
    $('#textEditLTB').val("");
    $("#ddlEditMSL").empty().append("--Select--");
    $('#textEditSPQ').val("");
    $('#textEditLeadTime').val("");
    $("#ddlEditRegion").empty().append("--Select--");
    $('#textEditDeliveryDate').val("");
    $('#textEditFactorySealed').val("");
    $('#textEditROHSStatus').val("");
    $('#textEditSupplierWarranty').val("");
    $('#textEditNotes').val("");
    $('#chkEditTestingRecommended').prop('checked', false);
    $('#textEditSellPriceLessReason').val("");

    //clear form validation error
    $("#EditSourcingValidationError").css("display", "none");
    $("#RowEditPartNo").css("background-color", "#56954E");
    $("#RowEditPartNoError").css("display", "none");
    $("#RowEditManufacturer").css("background-color", "#56954E");
    $("#RowEditManufacturerError").css("display", "none");
    $("#RowEditProduct").css("background-color", "#56954E");
    $("#RowEditProductError").css("display", "none");
    $("#RowEditQuantity").css("background-color", "#56954E");
    $("#RowEditQuantityError").css("display", "none");
    $("#RowEditBuyPrice").css("background-color", "#56954E");
    $("#RowEditBuyPriceError").css("display", "none");
    $("#RowEditSellPrice").css("background-color", "#56954E");
    $("#RowEditSellPriceError").css("display", "none");
    $("#RowEditCurrency").css("background-color", "#56954E");
    $("#RowEditCurrencyError").css("display", "none");
    $("#RowEditSellPriceLess").css("display", "none");
    $("#RowEditSellPriceLessReason").css("background-color", "#56954E");
    $("#RowEditSellPriceLessReason").css("display", "none");
    $("#RowEditSellPriceLessReasonError").css("display", "none");
}

$('.CancelSource').click(function () {
    var modal = document.getElementById("EditSourceModal");
    resetEditSourcingForm();
    modal.style.display = "none"
    $('html, body', window.parent.document).animate({ scrollTop: 240 }, "fast");
});