<%@ Control Language="C#" CodeBehind="Service.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.ItemSearch.Service" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_ItemSearch:DesignBase id="ctlDB" runat="server" InitialSortDirection="DESC">
	<FieldsLeft>
		<ReboundUI_FilterDataItemRow:TextBox id="ctlName" runat="server" ResourceTitle="Name" />
	</FieldsLeft>
</ReboundUI_ItemSearch:DesignBase>
