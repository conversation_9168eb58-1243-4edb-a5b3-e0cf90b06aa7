using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;
using System.Text;
using Rebound.GlobalTrader.Site.Controls;
using Rebound.GlobalTrader.Site;

namespace Rebound.GlobalTrader.Site.Controls.Nuggets {
	public partial class CompanyAddresses : Base {

		#region Locals

		protected FlexiDataTable _tblAddresses;
		protected IconButton _ibtnEdit;
		protected IconButton _ibtnAdd;
		protected IconButton _ibtnCease;
		protected IconButton _ibtnDefaultBill;
		protected IconButton _ibtnDefaultShip;
		protected Panel _pnlLoadingAddressInfo;
		protected Panel _pnlAddressError;
		protected Panel _pnlAddressInfo;
		protected Label _lblAddressName;

		#endregion

		#region Properties

		private int _intCompanyID = -1;
		public int CompanyID {
			get { return _intCompanyID; }
			set { _intCompanyID = value; }
		}

		private int _intCompanyAddressID = -1;
		public int CompanyAddressID {
			get { return _intCompanyAddressID; }
			set { _intCompanyAddressID = value; }
		}

		private string _strAddressRowType;
		public string AddressRowType {
			get { return _strAddressRowType; }
			set { _strAddressRowType = value; }
		}

		private string _strCompanyName = "";
		public string CompanyName {
			get { return _strCompanyName; }
			set { _strCompanyName = value; }
		}

		private bool _blnCanAdd = true;
		public bool CanAdd {
			get { return _blnCanAdd; }
			set { _blnCanAdd = value; }
		}

		private bool _blnCanEdit = true;
		public bool CanEdit {
			get { return _blnCanEdit; }
			set { _blnCanEdit = value; }
		}

		private bool _blnCanCease = true;
		public bool CanCease {
			get { return _blnCanCease; }
			set { _blnCanCease = value; }
		}

		private bool _blnCanMakeDefault = true;
		public bool CanMakeDefault {
			get { return _blnCanMakeDefault; }
			set { _blnCanMakeDefault = value; }
		}

        private bool _blnCanEditTax = false;
        public bool CanEditTax
        {
            get { return _blnCanEditTax; }
            set { _blnCanEditTax = value; }
        }
	

		#endregion

		#region Overrides

		/// <summary>
		/// Oninit
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
			base.OnInit(e);
			WireUpControls();
			AddScriptReference("Controls/Nuggets/CompanyAddresses/CompanyAddresses");
			TitleText = Functions.GetGlobalResource("Nuggets", "Addresses");
			SetupAddressesTable();
		}

		protected override void OnLoad(EventArgs e) {
			if (_intCompanyID == -1) _intCompanyID = _objQSManager.CompanyID;
			SetupScriptDescriptors();
			base.OnLoad(e);
		}

		protected override void OnPreRender(EventArgs e) {
			_ibtnAdd.Visible = _blnCanAdd;
			_ibtnEdit.Visible = _blnCanEdit;
			_ibtnDefaultShip.Visible = _blnCanMakeDefault;
			_ibtnDefaultBill.Visible = _blnCanMakeDefault;
			_ibtnCease.Visible = _blnCanCease;
			base.OnPreRender(e);
		}
		#endregion

		private void SetupAddressesTable() {
			_tblAddresses.AllowSelection = true;
			_tblAddresses.Columns.Add(new FlexiDataColumn("AddressName", WidthManager.GetWidth(WidthManager.ColumnWidth.ListName)));
			_tblAddresses.Columns.Add(new FlexiDataColumn("Address"));
			_tblAddresses.Columns.Add(new FlexiDataColumn("IsDefaultBill", WidthManager.GetWidth(WidthManager.ColumnWidth.IsDefault), false));
			_tblAddresses.Columns.Add(new FlexiDataColumn("IsDefaultShip", WidthManager.GetWidth(WidthManager.ColumnWidth.IsDefault), false));
		}

		private void SetupScriptDescriptors() {
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyAddresses", ctlDesignBase.ClientID);
			_scScriptControlDescriptor.AddProperty("intCompanyID", _intCompanyID);
			_scScriptControlDescriptor.AddProperty("intCompanyAddressID", _intCompanyAddressID);
			_scScriptControlDescriptor.AddProperty("strAddressType", _strAddressRowType);
			_scScriptControlDescriptor.AddComponentProperty("tblAddresses", _tblAddresses.ClientID);
			if (_blnCanEdit) _scScriptControlDescriptor.AddElementProperty("ibtnEdit", _ibtnEdit.ClientID);
			if (_blnCanAdd) _scScriptControlDescriptor.AddElementProperty("ibtnAdd", _ibtnAdd.ClientID);
			if (_blnCanCease) _scScriptControlDescriptor.AddElementProperty("ibtnCease", _ibtnCease.ClientID);
			if (_blnCanMakeDefault) _scScriptControlDescriptor.AddElementProperty("ibtnDefaultBill", _ibtnDefaultBill.ClientID);
			if (_blnCanMakeDefault) _scScriptControlDescriptor.AddElementProperty("ibtnDefaultShip", _ibtnDefaultShip.ClientID);
			_scScriptControlDescriptor.AddElementProperty("pnlLoadingAddressInfo", _pnlLoadingAddressInfo.ClientID);
			_scScriptControlDescriptor.AddElementProperty("pnlAddressError", _pnlAddressError.ClientID);
			_scScriptControlDescriptor.AddElementProperty("pnlAddressInfo", _pnlAddressInfo.ClientID);
			_scScriptControlDescriptor.AddElementProperty("lblAddressName", _lblAddressName.ClientID);
			_scScriptControlDescriptor.AddProperty("strCompanyName", _strCompanyName);
            _scScriptControlDescriptor.AddProperty("blnCanEditTax", _blnCanEditTax);
		}

		/// <summary>
		/// Wire up controls from the ascx
		/// </summary>
		private void WireUpControls() {
			_tblAddresses = (FlexiDataTable)ctlDesignBase.FindContentControl("tblAddresses");
			_ibtnEdit = (IconButton)FindIconButton("ibtnEdit");
			_ibtnAdd = (IconButton)FindIconButton("ibtnAdd");
			_ibtnCease = (IconButton)FindIconButton("ibtnCease");
			_ibtnDefaultBill = (IconButton)FindIconButton("ibtnDefaultBill");
			_ibtnDefaultShip = (IconButton)FindIconButton("ibtnDefaultShip");
			_pnlLoadingAddressInfo = (Panel)ctlDesignBase.FindContentControl("pnlLoadingAddressInfo");
			_pnlAddressError = (Panel)ctlDesignBase.FindContentControl("pnlAddressError");
			_pnlAddressInfo = (Panel)ctlDesignBase.FindContentControl("pnlAddressInfo");
			_lblAddressName = (Label)ctlDesignBase.FindContentControl("lblAddressName");
		}


	}
}
