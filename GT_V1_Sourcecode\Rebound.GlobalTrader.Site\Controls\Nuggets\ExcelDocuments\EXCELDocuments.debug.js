﻿


Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Nuggets");
Rebound.GlobalTrader.Site.Controls.Nuggets.EXCELDocuments = function(element) {
Rebound.GlobalTrader.Site.Controls.Nuggets.EXCELDocuments.initializeBase(this, [element]);
    this._sectionID = 0;
    this._intCountPDF == 0;
    
};
Rebound.GlobalTrader.Site.Controls.Nuggets.EXCELDocuments.prototype = {

    get_sectionID: function() { return this._sectionID; }, set_sectionID: function(value) { if (this._sectionID !== value) this._sectionID = value; },
    get_pnlEXCELDocuments: function() { return this._pnlEXCELDocuments; }, set_pnlEXCELDocuments: function(value) { if (this._pnlEXCELDocuments !== value) this._pnlEXCELDocuments = value; },
    get_ibtnAdd: function() { return this._ibtnAdd; }, set_ibtnAdd: function(value) { if (this._ibtnAdd !== value) this._ibtnAdd = value; },
    get_blnCanDelete: function() { return this._blnCanDelete; }, set_blnCanDelete: function(value) { if (this._blnCanDelete !== value) this._blnCanDelete = value; },
    get_intMaxPDFDocuments: function() { return this._intMaxPDFDocuments; }, set_intMaxPDFDocuments: function(value) { if (this._intMaxPDFDocuments !== value) this._intMaxPDFDocuments = value; },
    get_blnCanAdd: function() { return this._blnCanAdd; }, set_blnCanAdd: function(value) { if (this._blnCanAdd !== value) this._blnCanAdd = value; },
    get_IsPDFAvailable: function() { return this._IsPDFAvailable; }, set_IsPDFAvailable: function(value) { if (this._IsPDFAvailable !== value) this._IsPDFAvailable = value; },
    get_strSectionName: function() { return this._strSectionName; }, set_strSectionName: function(value) { if (this._strSectionName !== value) this._strSectionName = value; },
    get_strExcel: function() { return this._strExcel; }, set_strExcel: function(value) { if (this._strExcel !== value) this._strExcel = value; },
    initialize: function() {

        Rebound.GlobalTrader.Site.Controls.Nuggets.EXCELDocuments.callBaseMethod(this, "initialize");
        this.addRefreshEvent(Function.createDelegate(this, this.showPDFPanel));
        if (this._ibtnAdd) {

            $R_IBTN.addClick(this._ibtnAdd, Function.createDelegate(this, this.showAddForm));
            this._frmAdd = $find(this._aryFormIDs[0]);
            this._frmAdd._strSectionName = this._strSectionName;
            this._frmAdd._intSectionID = this._sectionID;
            this._frmAdd._strExcel = this._strExcel;
            this._frmAdd.addCancel(Function.createDelegate(this, this.hideAddForm));
            this._frmAdd.addSaveComplete(Function.createDelegate(this, this.saveAddComplete));
            this._frmAdd.addSaveError(Function.createDelegate(this, this.saveAddError));

        }

        if (this._blnCanDelete) {
            this._frmDelete = $find(this._aryFormIDs[1]);
            this._frmDelete._strSectionName = this._strSectionName;
            this._frmDelete.addNotConfirmed(Function.createDelegate(this, this.cancelDelete));
            this._frmDelete.addSaveComplete(Function.createDelegate(this, this.saveDeleteComplete));
        }
        this.getData();
    },
    dispose: function() {
        if (this.isDisposed) return;
        if (this._ibtnAdd) $R_IBTN.clearHandlers(this._ibtnAdd);
        if (this._frmAdd) this._frmAdd.dispose();
        if (this._frmDelete) this._frmDelete.dispose();
        this._frmAdd = null;
        this._frmDelete = null;
        this._ibtnAdd = null;
        this._pnlEXCELDocuments = null;
        this._sectionID = null;
        this._blnCanAdd = null;
        this._IsPDFAvailable = null;
        this._intMaxPDFDocuments = null;
        this._blnCanDelete = null;
        this._intCountPDF = null;
        this._strSectionName = null;
        Rebound.GlobalTrader.Site.Controls.Nuggets.EXCELDocuments.callBaseMethod(this, "dispose");
    },
    getData: function() {

        if (!this._IsPDFAvailable) { this.pdfNotAvailable(true); return; }
        this.getData_Start();
        this._intCountPDF == 0;
        this.enableButtons(false);
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/EXCELDocuments");
        obj.set_DataObject("EXCELDocuments");
        obj.set_DataAction("GetData");
        obj.addParameter("id", this._sectionID);
        obj.addParameter("section", this._strSectionName);
        //obj.addParameter("excel", this._strExcel);
        obj.addDataOK(Function.createDelegate(this, this.getDataOK));
        obj.addError(Function.createDelegate(this, this.getDataError));
        obj.addTimeout(Function.createDelegate(this, this.getDataError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },
    getMaxPDFDocument: function() {
        this.getData_Start();
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/EXCELDocuments");
        obj.set_DataObject("EXCELDocuments");
        obj.set_DataAction("MaxPDFDoc");
        obj.addDataOK(Function.createDelegate(this, this.getMaxPDFDocumentOK));
        obj.addError(Function.createDelegate(this, this.getDataError));
        obj.addTimeout(Function.createDelegate(this, this.getDataError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },
    getMaxPDFDocumentOK: function(args) {
        var result = args._result;
        if (result.MaxPDFDocument > 0)
            this._intMaxPDFDocuments = result.MaxPDFDocument;
    },
    getDataOK: function(args) {
        var res = args._result;
        var result = args._result;
        $R_FN.setInnerHTML(this._pnlEXCELDocuments, "");
        var iconpath = result.IconPath;
       
        var strPDF = "";
        if (result.Items) {
            for (var i = 0; i < result.Items.length; i++) {
                var row = result.Items[i];
                strPDF += "<div class=\"pdfDocument\">";
                strPDF += String.format("<div class=\"pdfDocumentDelete\" onclick=\"$find('{0}').deletePDF({1},'{2}');\">&nbsp;</div>", this._element.id, row.ID, row.FileName);
                strPDF += String.format("<a href=\"{0}\" target=\"_blank\"><img width=\"80px\" id=\"{1}_img{2}\" src=\"{3}\" border=\"0\" /></a>", "Print.aspx?pro=25&filename=" + row.FileName, this._element.id, i, iconpath);
                strPDF += "<div class=\"pdfDocumentCaption\">";
                if (row.Caption) strPDF += row.Caption + "<br />";
                strPDF += row.Date;
                if (row.By) strPDF += "<br />" + row.By;
                strPDF += "</div>";
                strPDF += "</div>";
                row = null;
            }
            this._intCountPDF = result.Items.length;
        }
        $R_FN.setInnerHTML(this._pnlEXCELDocuments, strPDF);
        this.getDataOK_End();
        this.showNoData(this._intCountPDF == 0);
        this.enableButtons(this._blnCanAdd);
        this.showPanel(this._intCountPDF > 0)

    },
    getDataError: function(args) {
        this.showError(true, args.get_ErrorMessage());
    },
    getDataCount: function() {
        this.getData_Start();
        this._intCountPDF == 0;
        this.enableButtons(false);
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/EXCELDocuments");
        obj.set_DataObject("EXCELDocuments");
        obj.set_DataAction("GetData");
        obj.addParameter("id", this._sectionID);
        obj.addParameter("section", this._strSectionName);
        obj.addParameter("excel", this._strExcel);
        obj.addDataOK(Function.createDelegate(this, this.getDataCountOK));
        obj.addError(Function.createDelegate(this, this.getDataError));
        obj.addTimeout(Function.createDelegate(this, this.getDataError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },
    getDataCountOK: function(args) {
        var res = args._result;
        var result = args._result;
        $R_FN.setInnerHTML(this._pnlEXCELDocuments, "");
        var strPDF = "";
        if (result.Items) {
            this._intCountPDF = result.Items.length;
            strPDF += "<div style=\"height:50px;\"></div>";
        }
        $R_FN.setInnerHTML(this._pnlEXCELDocuments, strPDF);
        this.getDataOK_End();
        this.showNoData(this._intCountPDF == 0);
        this.enableButtons(this._blnCanAdd);
        this.showPanel(this._intCountPDF > 0)

    },
    enableButtons: function(bln) {
        if (bln) {
            if (this._ibtnAdd) $R_IBTN.enableButton(this._ibtnAdd, ((this._intCountPDF == 0) || (this._intCountPDF < this._intMaxPDFDocuments)));
        } else {
            if (this._ibtnAdd) $R_IBTN.enableButton(this._ibtnAdd, false);
        }
    },
    deletePDF: function(intPDF, FileName) {
        this._frmDelete._intPDFDocumentID = intPDF;
        this._frmDelete._pdfFileName = FileName;
        this.showDeleteForm();
    },
    showAddForm: function() {
        this.showForm(this._frmAdd, true);
    },
    hideAddForm: function() {
        this.showForm(this._frmAdd, false);
        this.showNoData(this._intCountPDF == 0);
    },
    saveAddComplete: function() {
        this.hideAddForm();
        this.showSavedOK(true, $R_RES.ChangesSavedSuccessfully);
        this._IsPDFAvailable = true;
        this.getData();
    },

    saveAddError: function() {
        this.showError(true, this._frmAdd._strErrorMessage);
    },

    showDeleteForm: function() {
        this.showForm(this._frmDelete, true);
    },

    hideDeleteForm: function() {
        this.showForm(this._frmDelete, false);
        this.showNoData(this._intCountPDF == 0);
    },

    cancelDelete: function() {
        this.hideDeleteForm();
    },

    saveDeleteComplete: function() {
        this.hideDeleteForm();
        this.showSavedOK(true, $R_RES.ChangesSavedSuccessfully);
        this.getData();
    },

    showPDFPanel: function() {
        this._IsPDFAvailable = true;
        this.showPanel(true);
        this.getMaxPDFDocument();
        this.getData();
    },

    showPanel: function(bln) {
        $R_FN.showElement(this._pnlEXCELDocuments, bln);
    },

    pdfNotAvailable: function(bln) {
        if (bln) {
            $R_FN.setInnerHTML(this._pnlEXCELDocuments, "");
            this.getDataOK_End();
            this._intCountPDF = 0;
            this.showNoData(true);
            this.enableButtons(this._blnCanAdd);
        }
    }

};
Rebound.GlobalTrader.Site.Controls.Nuggets.EXCELDocuments.registerClass("Rebound.GlobalTrader.Site.Controls.Nuggets.EXCELDocuments", Rebound.GlobalTrader.Site.Controls.Nuggets.Base);
