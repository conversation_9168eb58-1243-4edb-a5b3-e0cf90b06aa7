﻿/* 
===========================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-209429]	    CuongDox			23-Oct-2024		Update			search for sanctioned company with fuzzy
===========================================================================================
*/
CREATE OR ALTER PROCEDURE usp_Select_CSVSantionedEUList_Fuzzy
(
    @Name NVARCHAR(MAX) = NULL,
    @Type NVARCHAR(MAX) = NULL,
    @Address NVARCHAR(MAX) = NULL,
    @Country NVARCHAR(MAX) = NULL
)
AS
BEGIN
	Declare @NameShort NVARCHAR(MAX)
	SET @NameShort = dbo.GetFirstNWords(@Name, 3) 
    SELECT 
		csl.EntityRemark,
		CSL.EntityRegulationProgramme,
		csl.IdentificationNumber,
        csl.EntitySubjectTypeClassificationCode,
        csl.NameAliasWholeName,
        CONCAT(AddressStreet, ' ', AddressCity, ' ', AddressRegion, ' ', AddressPoBox, ' ', AddressZipCode, ' ', AddressPlace) AS Address,
        csl.BirthDateBirthDate,
        csl.BirthDatePlace,
        csl.BirthDateCountryDescription 
    FROM tbCSVImportEU csl
    WHERE 
        -- Check if @Name is not NULL and not an empty string, otherwise skip this condition
        (@Name IS NULL OR @Name = '' OR csl.NameAliasWholeName LIKE '%' + @NameShort + '%')
        
        -- Check if @Type is not NULL and not an empty string, otherwise skip this condition
        AND (@Type IS NULL OR @Type = '' OR csl.EntitySubjectType = @Type)
        
        -- Check if @Address is not NULL and not an empty string, otherwise skip this condition
        AND (@Address IS NULL OR @Address = '' OR CONCAT(AddressStreet, ' ', AddressCity, ' ', AddressRegion, ' ', AddressPoBox, ' ', AddressZipCode, ' ', AddressPlace) LIKE '%' + @Address + '%')
        
        -- Check if @Country is not NULL and not an empty string, otherwise skip this condition
        AND (@Country IS NULL OR @Country = '' OR csl.BirthDateCountryDescription LIKE @Country)
END;