﻿using System;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace Rebound.GlobalTrader.Site.Controls.ItemSearch
{
    public partial class SIDebitNotes : Base
    {
        protected override void OnInit(EventArgs e)
        {
            base.OnInit(e);
            SetItemSearchType("SIDebitNotes");
            AddScriptReference("Controls.ItemSearch.SIDebitNotes.SIDebitNotes.js");
        }

        protected override void OnLoad(EventArgs e)
        {
            _scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.ItemSearch.SIDebitNotes", ctlDesignBase.ClientID);
            base.OnLoad(e);
        }

        protected override void OnPreRender(EventArgs e)
        {
            ctlDesignBase.MakeChildControls();
            ctlDesignBase.tblResults.AllowSelection = false;
            ctlDesignBase.tblResults.AllowMultipleSelection = false;
            ctlDesignBase.tblResults.PanelHeight = 250;
            ctlDesignBase.tblResults.InsideDataListNugget = true;

            ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("Select", Unit.Percentage(3), false, HorizontalAlign.Center));
            ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("DebitNoteNumber", Unit.Percentage(10), false));
            ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("DebitAmount", Unit.Percentage(7), false));
            ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("Date", Unit.Percentage(7), false));
            ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("PurchaseOrderNumber", Unit.Percentage(10), false));
            ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("IPONumber", Unit.Percentage(10), false));
            ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("SupplierRMANo", Unit.Percentage(10), false));
            ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("SupplierNotes", Unit.Percentage(43), false));
            base.OnPreRender(e);
        }
    }
}