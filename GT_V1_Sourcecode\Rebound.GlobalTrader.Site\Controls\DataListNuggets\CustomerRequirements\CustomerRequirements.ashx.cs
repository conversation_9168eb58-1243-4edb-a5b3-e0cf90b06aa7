/* Marker     changed by      date         Remarks
/* [0001]      A<PERSON><PERSON><PERSON>   14/07/2014  ESMS Ref:172 - Issue in Part Searching in whole GT */
/* [0002]      <PERSON>    19/09/2023  AS6081 Search/Filter functionality on different pages  */

using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;
using Rebound.GlobalTrader.Site.Enumerations;

namespace Rebound.GlobalTrader.Site.Controls.DataListNuggets.Data {

	public class CustomerRequirements : Base {

        /// <summary>
        /// Gets the main data
        /// </summary>
        protected override void GetData() {
            JsonObject jsn = new JsonObject();

            //check view level
            ViewLevelList enmViewLevel = (ViewLevelList)GetFormValue_Int("ViewLevel");

            int? SelectedclientNo = null;
            int? SessionClientNo = SessionManager.ClientID;
            bool? blnMakeYellow = false;
            if (SessionManager.IsGSA == true && SessionManager.IsGlobalUser == false)
            {
                SelectedclientNo = GetFormValue_NullableInt("Client");
                if (SelectedclientNo != null)
                {
                    blnMakeYellow = true;
                }
                else
                {
                    blnMakeYellow = false;
                }

            }
            else
            {
                blnMakeYellow = false;
            }
            string AS6081 = GetFormValue_String("AS6081", "");
            //get data
            List<CustomerRequirement> lst = CustomerRequirement.DataListNugget(
                SessionManager.ClientID
                , (enmViewLevel == ViewLevelList.Team) ? (int?)SessionManager.LoginTeamID : null
                , (enmViewLevel == ViewLevelList.Division) ? (int?)SessionManager.LoginDivisionID : null
                , (enmViewLevel == ViewLevelList.My) ? (int?)LoginID : null
                , GetFormValue_NullableInt("SortIndex", 0)
                , GetFormValue_NullableInt("SortDir", SortColumnDirection.ASC)
                , GetFormValue_NullableInt("PageIndex", 0)
                , GetFormValue_NullableInt("PageSize", 10)
                //[0001] start code
                //, GetFormValue_StringForPartSearch("Part")
                  , GetFormValue_PartForLikeSearch("Part")
                //[0001] end code
                //, GetFormValue_StringForNameSearch("Contact")
                , GetFormValue_StringForNameSearchDecode("Contact")
                //, GetFormValue_StringForNameSearch("CMName")
                , GetFormValue_StringForNameSearchDecode("CMName")
                , GetFormValue_NullableInt("Salesman")
                , GetFormValue_Boolean("RecentOnly")
                , GetFormValue_Boolean("IncludeClosed")
                , GetFormValue_NullableInt("CReqNoLo")
                , GetFormValue_NullableInt("CReqNoHi")
                , GetFormValue_NullableDateTime("ReceivedDateFrom")
                , GetFormValue_NullableDateTime("ReceivedDateTo")
                , GetFormValue_NullableDateTime("DatePromisedFrom")
                , GetFormValue_NullableDateTime("DatePromisedTo")
                , GetFormValue_Boolean("PartWatch")               
                , GetFormValue_StringForNameSearchNew("BOMName")
               // , GetFormValue_StringForNameSearchDecode("BOMCode")
               , GetFormValue_StringForLikeSearch("BOMCode", true)
               , GetFormValue_NullableDouble("TotalLo")
                , GetFormValue_NullableDouble("TotalHi")
                , GetFormValue_NullableInt("StatusREQ")
                //<% --Code start[001]-- %>
                , GetFormValue_NullableInt("IndustryType")
            //<% --Code End[001]-- %>

                , (AS6081 == "1" ? true : (AS6081 == "2" ? false : (bool?)null))  //[002]
                , GetFormValue_NullableInt("Client")
                , SessionManager.LoginID
            );

            //check counts
            jsn.AddVariable("TotalRecords", (lst.Count > 0) ? lst[0].RowCnt : 0);

            //format data
            JsonObject jsnRowsArray = new JsonObject(true);
            for (int i = 0; i < lst.Count; i++) {
                if (i < lst.Count) {
                    JsonObject jsnRow = new JsonObject();
                    jsnRow.AddVariable("ID", lst[i].CustomerRequirementId);
                    jsnRow.AddVariable("No", lst[i].CustomerRequirementNumber);
                    jsnRow.AddVariable("Part", lst[i].Part);
                    jsnRow.AddVariable("ROHS", lst[i].ROHS);
                    jsnRow.AddVariable("MfrNo", lst[i].ManufacturerNo);
                    jsnRow.AddVariable("Mfr", lst[i].ManufacturerCode);
                    jsnRow.AddVariable("Quantity", lst[i].Quantity);
                    jsnRow.AddVariable("CM", lst[i].CompanyName);
                    jsnRow.AddVariable("CMNo", lst[i].CompanyNo);
                    jsnRow.AddVariable("Contact", lst[i].ContactName);
                    jsnRow.AddVariable("ContactNo", lst[i].ContactNo);
                    jsnRow.AddVariable("Salesman", lst[i].SalesmanName);
                    jsnRow.AddVariable("Received", Functions.FormatDate(lst[i].ReceivedDate));
                    jsnRow.AddVariable("Promised", Functions.FormatDate(lst[i].DatePromised));
                    jsnRow.AddVariable("BOMCode", lst[i].BOMCode);
                    jsnRow.AddVariable("BOMNo", lst[i].BOMNo);
                    jsnRow.AddVariable("BOMName", lst[i].BOMName);
                    jsnRow.AddVariable("TotalBase", Functions.FormatCurrency(lst[i].TotalInBase, SessionManager.ClientCurrencyCode, 2));
                    jsnRow.AddVariable("TotalValue", Functions.FormatCurrency(lst[i].TotalValue, lst[i].CurrencyCode, 2));
                    jsnRow.AddVariable("REQStatusName", lst[i].REQStatusName);
                    jsnRow.AddVariable("BomStatus", lst[i].BomStatus);
                    //<% --Code start[002]-- %>
                    jsnRow.AddVariable("IndustryName", lst[i].IndustryName);
                    //<% --Code End[002]-- %>
                    jsnRow.AddVariable("AS6081", lst[i].AS6081); //[002]
                    jsnRow.AddVariable("blnMakeYellow", blnMakeYellow);
                    jsnRowsArray.AddVariable(jsnRow);
                    jsnRow.Dispose();
                    jsnRow = null;
                }
            }
            jsn.AddVariable("Results", jsnRowsArray);
            OutputResult(jsn);
            jsnRowsArray.Dispose();
            jsnRowsArray = null;
            jsn.Dispose();
            jsn = null;
            base.GetData();
        }

        protected override void AddFilterStates() {
            //Prevent filter state for Tab
            //AddExplicitFilterState("ViewLevel", GetFormValue_Int("ViewLevel"));
            AddFilterState("Part");
            AddFilterState("Contact");
            AddFilterState("CMName");
            AddFilterState("Salesman");
            AddFilterState("RecentOnly");
            AddFilterState("IncludeClosed");
            AddFilterState("CReqNo");
            AddFilterState("ReceivedDateFrom");
            AddFilterState("ReceivedDateTo");
            AddFilterState("DatePromisedFrom");
            AddFilterState("DatePromisedTo");
            AddFilterState("BOMName");
            AddFilterState("PartWatch");
            AddFilterState("StatusREQ");
            //AddFilterState("BOMCode");
            AddFilterState("AS6081"); //[0002] 
            base.AddFilterStates();
        }
    }
}
