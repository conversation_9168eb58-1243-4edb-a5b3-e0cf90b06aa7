Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.BOMItems_NoBidConfirm=function(n){Rebound.GlobalTrader.Site.Controls.Forms.BOMItems_NoBidConfirm.initializeBase(this,[n]);this._intRequirementLineID=-1;this._intBOMID=-1;this._CustReqNo=-1;this._RecallNoBid=!1};Rebound.GlobalTrader.Site.Controls.Forms.BOMItems_NoBidConfirm.prototype={get_BomCode:function(){return this._BomCode},set_BomCode:function(n){this._BomCode!==n&&(this._BomCode=n)},get_BomName:function(){return this._BomName},set_BomName:function(n){this._BomName!==n&&(this._BomName=n)},get_BomCompanyName:function(){return this._BomCompanyName},set_BomCompanyName:function(n){this._BomCompanyName!==n&&(this._BomCompanyName=n)},get_BomCompanyNo:function(){return this._BomCompanyNo},set_BomCompanyNo:function(n){this._BomCompanyNo!==n&&(this._BomCompanyNo=n)},get_SalesManNo:function(){return this._SalesManNo},set_SalesManNo:function(n){this._SalesManNo!==n&&(this._SalesManNo=n)},get_SalesManName:function(){return this._SalesManName},set_SalesManName:function(n){this._SalesManName!==n&&(this._SalesManName=n)},get_strTitle_NoBid:function(){return this._strTitle_NoBid},set_strTitle_NoBid:function(n){this._strTitle_NoBid!==n&&(this._strTitle_NoBid=n)},get_strTitle_RecallNoBid:function(){return this._strTitle_RecallNoBid},set_strTitle_RecallNoBid:function(n){this._strTitle_RecallNoBid!==n&&(this._strTitle_RecallNoBid=n)},get_lblExplainNoBid:function(){return this._lblExplainNoBid},set_lblExplainNoBid:function(n){this._lblExplainNoBid!==n&&(this._lblExplainNoBid=n)},get_lblExplainRecallNoBid:function(){return this._lblExplainRecallNoBid},set_lblExplainRecallNoBid:function(n){this._lblExplainRecallNoBid!==n&&(this._lblExplainRecallNoBid=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Forms.BOMItems_NoBidConfirm.callBaseMethod(this,"initialize");this.addShown(Function.createDelegate(this,this.formShown))},dispose:function(){this.isDisposed||(this._ctlConfirm&&this._ctlConfirm.dispose(),this._ctlConfirm=null,this._intRequirementLineID=null,this._strTitle_NoBid=null,this._strTitle_RecallNoBid=null,this._lblExplainNoBid=null,this._lblExplainRecallNoBid=null,Rebound.GlobalTrader.Site.Controls.Forms.BOMItems_NoBidConfirm.callBaseMethod(this,"dispose"))},formShown:function(){this._blnFirstTimeShown&&(this._ctlConfirm=this.getFieldComponent("ctlConfirm"),this._ctlConfirm.addClickYesEvent(Function.createDelegate(this,this.yesClicked)),this._ctlConfirm.addClickNoEvent(Function.createDelegate(this,this.noClicked)));this.checkMode()},yesClicked:function(){this.showSaving(!0);var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("Controls/Nuggets/BOMItems");n.set_DataObject("BOMItems");this._RecallNoBid==!0?n.set_DataAction("RecallNoBidRequirement"):n.set_DataAction("NoBidRequirement");n.addParameter("id",this._intRequirementLineID);n.addParameter("BomId",this._intBOMID);n.addParameter("BomCode",this._BomCode);n.addParameter("BomName",this._BomName);n.addParameter("BomCompanyName",this._BomCompanyName);n.addParameter("BomCompanyNo",this._BomCompanyNo);n.addParameter("SalesManName",this._SalesManName);n.addParameter("SalesManNo",this._SalesManNo);n.addParameter("CustReqNo",this._CustReqNo);n.addParameter("Notes",this.getFieldValue("ctlNotes"));n.addDataOK(Function.createDelegate(this,this.saveComplete));n.addError(Function.createDelegate(this,this.saveError));n.addTimeout(Function.createDelegate(this,this.saveError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},noClicked:function(){this.showSaving(!1);this.onNotConfirmed()},saveError:function(n){this.showSaving(!1);this._strErrorMessage=n._errorMessage;this.onSaveError()},saveComplete:function(n){this.showSaving(!1);n._result.Result?this.onSaveComplete():(this._strErrorMessage=n._errorMessage,this.onSaveError())},checkMode:function(){switch(this._mode){case"NoBid":this.changeTitle(this._strTitle_NoBid);break;case"RecallNoBid":this.changeTitle(this._strTitle_RecallNoBid)}$R_FN.showElement(this._lblExplainNoBid,this._mode=="NoBid");$R_FN.showElement(this._lblExplainRecallNoBid,this._mode=="RecallNoBid");this.showField("ctlNotes",this._mode!="RecallNoBid")}};Rebound.GlobalTrader.Site.Controls.Forms.BOMItems_NoBidConfirm.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.BOMItems_NoBidConfirm",Rebound.GlobalTrader.Site.Controls.Forms.Base,Sys.IDisposable);