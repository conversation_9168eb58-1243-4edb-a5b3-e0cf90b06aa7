using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;

using Rebound.GlobalTrader.Site.Controls;
using Rebound.GlobalTrader.Site;


namespace Rebound.GlobalTrader.Site.Controls.Forms {
	public partial class CompanyMainInfo_Edit : Base {


		#region Overrides

		/// <summary>
		/// OnInit
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
			base.OnInit(e);
			TitleText = Functions.GetGlobalResource("FormTitles", "CompanyMainInfo_Edit");
			AddScriptReference("Controls.Nuggets.CompanyMainInfo.Edit.CompanyMainInfo_Edit.js");
		}

		/// <summary>
		/// OnPreRender
		/// </summary>
		/// <param name="e"></param>
		protected override void OnPreRender(EventArgs e) {
			SetupScriptDescriptors();
			base.OnPreRender(e);
		}

		#endregion


		/// <summary>
		/// Setup Script Descriptors
		/// </summary>
		private void SetupScriptDescriptors() {
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.Forms.CompanyMainInfo_Edit", ctlDesignBase.ClientID);
			_scScriptControlDescriptor.AddProperty("intCompanyID", _objQSManager.CompanyID);
            _scScriptControlDescriptor.AddElementProperty("lblLastReviewDate", FindFieldControl("ctlReviewDate", "lblLastReviewDate").ClientID);
			_scScriptControlDescriptor.AddProperty("blnPOHUB", Convert.ToBoolean(SessionManager.IsPOHub));
		}

	}
}