﻿/* 
===========================================================================================
TASK      			UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[BUG-207483]		Ngai To				02-Jul-2024		FIX				Bug 207483: [PROD Bug] CPU Spike - DB deadlock in the existing KUB Assistance feature
===========================================================================================
*/

CREATE UNIQUE CLUSTERED INDEX [PK_tbKUB_GPCalculationDetails] ON [dbo].[tbKUB_GPCalculationDetails] ([DetailsId] ASC)
	WITH (
			PAD_INDEX = OFF,
			STATISTICS_NORECOMPUTE = OFF,
			SORT_IN_TEMPDB = OFF,
			IGNORE_DUP_KEY = OFF,
			DROP_EXISTING = OFF,
			ONLINE = OFF,
			ALLOW_ROW_LOCKS = ON,
			ALLOW_PAGE_LOCKS = ON
			)
GO

SET ANSI_PADDING ON
GO

CREATE NONCLUSTERED INDEX [IX_tbKUB_GPCalculationDetails_Part] ON [dbo].[tbKUB_GPCalculationDetails] ([Part] ASC)
	WITH (
			PAD_INDEX = OFF,
			STATISTICS_NORECOMPUTE = OFF,
			SORT_IN_TEMPDB = OFF,
			DROP_EXISTING = OFF,
			ONLINE = OFF,
			ALLOW_ROW_LOCKS = ON,
			ALLOW_PAGE_LOCKS = ON
			)
GO