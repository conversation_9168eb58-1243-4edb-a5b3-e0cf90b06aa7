Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");Rebound.GlobalTrader.Site.Controls.DropDowns.BuyCurrencyByGlobalNo=function(n){Rebound.GlobalTrader.Site.Controls.DropDowns.BuyCurrencyByGlobalNo.initializeBase(this,[n]);this._blnIsBuy=!0};Rebound.GlobalTrader.Site.Controls.DropDowns.BuyCurrencyByGlobalNo.prototype={get_intGlobalCurrencyNo:function(){return this._intGlobalCurrencyNo},set_intGlobalCurrencyNo:function(n){this._intGlobalCurrencyNo!==n&&(this._intGlobalCurrencyNo=n)},get_blnIsBuy:function(){return this._blnIsBuy},set_blnIsBuy:function(n){this._blnIsBuy!==n&&(this._blnIsBuy=n)},get_intGlobalLoginClientNo:function(){return this._intGlobalLoginClientNo},set_intGlobalLoginClientNo:function(n){this._intGlobalLoginClientNo!==n&&(this._intGlobalLoginClientNo=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.DropDowns.BuyCurrencyByGlobalNo.callBaseMethod(this,"initialize");this.addSetupDataCall(Function.createDelegate(this,this.setupDataCall));this.addGetDataOK(Function.createDelegate(this,this.dataCallOK))},dispose:function(){this.isDisposed||(this._intGlobalCurrencyNo=null,this._blnIsBuy=null,this._intGlobalLoginClientNo=null,Rebound.GlobalTrader.Site.Controls.DropDowns.BuyCurrencyByGlobalNo.callBaseMethod(this,"dispose"))},setupDataCall:function(){this._objData.set_PathToData("controls/DropDowns/BuyCurrencyByGlobalNo");this._objData.set_DataObject("BuyCurrencyByGlobalNo");this._objData.set_DataAction("GetData");this._objData.addParameter("GlobalNo",this._intGlobalCurrencyNo);this._objData.addParameter("blnBuy",this._blnIsBuy);this._objData.addParameter("GlobalLoginClientNo",this._intGlobalLoginClientNo)},dataCallOK:function(){var t=this._objData._result,n;if(t.Currencies)for(n=0;n<t.Currencies.length;n++)this.addOption(t.Currencies[n].Name,t.Currencies[n].ID,t.Currencies[n].Code)}};Rebound.GlobalTrader.Site.Controls.DropDowns.BuyCurrencyByGlobalNo.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.BuyCurrencyByGlobalNo",Rebound.GlobalTrader.Site.Controls.DropDowns.Base);