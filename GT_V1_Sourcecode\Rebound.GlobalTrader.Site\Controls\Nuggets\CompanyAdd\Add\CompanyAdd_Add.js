Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.CompanyAdd_Add=function(n){Rebound.GlobalTrader.Site.Controls.Forms.CompanyAdd_Add.initializeBase(this,[n]);this._intNewID=0;this._txtCompanyName=null};Rebound.GlobalTrader.Site.Controls.Forms.CompanyAdd_Add.prototype={initialize:function(){Rebound.GlobalTrader.Site.Controls.Forms.CompanyAdd_Add.callBaseMethod(this,"initialize");this.addCancel(Function.createDelegate(this,this.cancelClicked));this.addSave(Function.createDelegate(this,this.saveClicked));this.addShown(Function.createDelegate(this,this.formShown))},dispose:function(){this.isDisposed||(this._ctlAddress&&this._ctlAddress.dispose(),this._txtCompanyName&&$clearHandlers(this._txtCompanyName),this._ctlAddress=null,this._intNewID=null,this._txtCompanyName=null,Rebound.GlobalTrader.Site.Controls.Forms.CompanyAdd_Add.callBaseMethod(this,"dispose"))},formShown:function(){this._blnFirstTimeShown&&(this._ctlAddress=$find(this.getField("ctlAddress").ID),this._ctlAddress._ctlRelatedForm=this,this._txtCompanyName=$get(this.getField("ctlCompanyName").ControlID),$addHandler(this._txtCompanyName,"change",Function.createDelegate(this,this.updateAddressName)));this.getFieldDropDownData("ctlSalesman");this.getFieldDropDownData("ctlCountry")},cancelClicked:function(){$R_FN.navigateBack()},saveClicked:function(){if(this.validateForm()){this.showSaving(!0);var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/CompanyAdd");n.set_DataObject("CompanyAdd");n.set_DataAction("AddNew");n.addParameter("CompanyName",this.getFieldValue("ctlCompanyName"));n.addParameter("Salesman",this.getFieldValue("ctlSalesman"));n.addParameter("Telephone",this.getFieldValue("ctlTelephone"));n.addParameter("Telephone800",this.getFieldValue("ctlTelephone800"));n.addParameter("Fax",this.getFieldValue("ctlFax"));n.addParameter("EMail",this.getFieldValue("ctlEMail"));n.addParameter("URL",this.getFieldValue("ctlURL"));n.addParameter("VatNo",this.getFieldValue("ctlVATNumber"));n.addParameter("Notes",this.getFieldValue("ctlNotes"));n.addParameter("AddressEntered",this._ctlAddress.addressHasBeenEntered());n.addParameter("AddressName",this.getFieldValue("ctlAddressName"));n.addParameter("AddressLine1",this.getFieldValue("ctlLine1"));n.addParameter("AddressLine2",this.getFieldValue("ctlLine2"));n.addParameter("AddressLine3",this.getFieldValue("ctlLine3"));n.addParameter("Town",this.getFieldValue("ctlTown"));n.addParameter("County",this.getFieldValue("ctlCounty"));n.addParameter("State",this.getFieldValue("ctlState"));n.addParameter("Country",this.getFieldValue("ctlCountry"));n.addParameter("Postcode",this.getFieldValue("ctlPostcode"));n.addParameter("FirstName",this.getFieldValue("ctlFirstName"));n.addParameter("LastName",this.getFieldValue("ctlLastName"));n.addParameter("CompRegNo",this.getFieldValue("ctlCmpRegNO"));n.addParameter("CertificateNotes",this.getFieldValue("ctlCertificateNotes"));n.addParameter("QualityNotes",this.getFieldValue("ctlqualityNotes"));n.addParameter("EORINumber",this.getFieldValue("ctlEORINumber"));n.addDataOK(Function.createDelegate(this,this.saveEditComplete));n.addError(Function.createDelegate(this,this.saveEditError));n.addTimeout(Function.createDelegate(this,this.saveEditError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null}},saveEditError:function(n){this._strErrorMessage=n._errorMessage;this.onSaveError()},saveEditComplete:function(n){n._result.NewID>0?(this._intNewID=n._result.NewID,this.showSavedOK(!0),this.onSaveComplete()):(this._strErrorMessage=n._errorMessage,this.onSaveError())},validateForm:function(){this.onValidate();var n=this.autoValidateFields();return this._ctlAddress.validateFields()||(n=!1),n||this.showError(!0),n},updateAddressName:function(){this.setFieldValue("ctlAddressName",this.getFieldValue("ctlCompanyName"))}};Rebound.GlobalTrader.Site.Controls.Forms.CompanyAdd_Add.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.CompanyAdd_Add",Rebound.GlobalTrader.Site.Controls.Forms.Base,Sys.IDisposable);