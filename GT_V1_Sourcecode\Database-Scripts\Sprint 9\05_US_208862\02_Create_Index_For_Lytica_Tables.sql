﻿IF (EXISTS (SELECT 1 FROM sys.indexes WHERE name='Part-MFR' AND object_id = OBJECT_ID('tbLyticaAPI')))
BEGIN
	DROP INDEX [Part-MFR] ON [dbo].[tbLyticaAPI]
END
GO

IF (EXISTS (SELECT 1 FROM sys.indexes WHERE name='Part-MFR-LCS-LyticaNo' AND object_id = OBJECT_ID('tbLyticaAlternatePart')))
BEGIN
	DROP INDEX [Part-MFR-LCS-LyticaNo] ON [dbo].[tbLyticaAlternatePart]
END
GO

SET ANSI_PADDING ON
GO

CREATE NONCLUSTERED INDEX [Part-MFR] ON [dbo].[tbLyticaAPI]
(
	[OriginalPartSearched] ASC,
	[Manufacturer] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO

CREATE NONCLUSTERED INDEX [Part-MFR-LCS-LyticaNo] ON [dbo].[tbLyticaAlternatePart]
(
	[LyticaAPINo] ASC,
	[Part] ASC,
	[Manufacturer] ASC,
	[LifeCycleStatus] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO