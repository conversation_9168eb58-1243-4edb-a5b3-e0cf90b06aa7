﻿-- Drop the procedure if it already exists
IF OBJECT_ID('dbo.usp_Insert_GILineQueryApprovalResponce', 'P') IS NOT NULL
BEGIN
    DROP PROCEDURE dbo.usp_Insert_GILineQueryApprovalResponce;
END
GO
/* 
===========================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[BUG-204859]    cuongdx			   20-JUL-2024		 Alter	        fix bug when run in quarantine
===========================================================================================
*/
-- Create the new procedure
CREATE PROCEDURE usp_Insert_GILineQueryApprovalResponce
    @GI_QueryId INT,
    @GoodInNo INT,
    @GILineNo INT,
    @LoginId INT,
    @QueryMessage NVARCHAR(3000),
    @SalesApprovalStatus INT = 0,
    @QualityApprovalStatus INT = 0,
    @PurchasingApprovalStatus INT = 0,
    @CCUserId NVARCHAR(MAX) = NULL,
    @CCGroupIDs NVARCHAR(1500) = NULL,
    @ClientNo INT = 0,
    @ApproverHtml NVARCHAR(MAX) = NULL,
    @TotalCheckBoxcount INT = 0,
    @CheckedTotalCheckBoxcount INT = 0,
    @GetEnableCheckBoxIds NVARCHAR(1000) = ''
/*                                                                 
 *                          
 * [006] Action: Altered         Action By: Abhinav Saxena      Date: 28-03-2022                                      
 * Comment: Add Client name in subject of mail.                            
 *                        
 * [007] Action: Altered         Action By: Abhinav Saxena      Date: 30-03-2022                                      
 * Comment: Add multiple users in CC.            
 *          
 * [008] Action: Altered         Action By: Abhinav Saxena      Date: 05-05-2023                                      
 * Comment: Add multiple users in CC.          
 *                            
 */
AS
BEGIN
    DECLARE @ISInitialNotifyToSales BIT = 0
    DECLARE @ISInitialNotifyToQuality BIT = 0
    DECLARE @ISInitialNotifyToPurchase BIT = 0
    DECLARE @Result INT
    DECLARE @UpdatedBy INT
    DECLARE @GI_QueryNumber NVARCHAR(100) = ''
    DECLARE @NoReplyId INT
    DECLARE @NoReplyEmail NVARCHAR(MAX)
    SELECT @NoReplyId = LoginId,
           @NoReplyEmail = EMail
    FROM tbLogin
    where EmployeeName LIKE '%noreply%'
    DECLARE @QueryMessageOut NVARCHAR(MAX)
    DECLARE @WareHouseID INT = 0
    DECLARE @PurchasingApproverId INT = 0
    DECLARE @SalesApproverId INT = 0
    DECLARE @SalesSupportApproverId INT = 0
    --[006] code start                             
    DECLARE @ClientName NVARCHAR(100) = ''
    SELECT @ClientName = ltrim(replace(ClientCode, 'RE', ''))
    FROM tbclient
    WHERE ClientId = @ClientNo
    --[006] code end                            
    SELECT @PurchasingApproverId = PurchaseApproverId,
           @SalesApproverId = SalesApproverId,
           @SalesSupportApproverId = SalesSupportApproverId
    FROM tbGiLineQueryApprover
    where GI_QueryNo = @GI_QueryId
    SET @QueryMessageOut = ''
    IF (
       (
           SELECT COUNT(1) FROM tbGI_QueryFixedColumn WHERE GI_QueryNo = @GI_QueryId
       ) > 0
       )
    BEGIN
        ------------------Approval Response Changes--------    
        CREATE TABLE #tblResponses
        (
            ID INT IDENTITY(1, 1),
            ResId INT
        )
        IF (
               (@TotalCheckBoxcount != @CheckedTotalCheckBoxcount)
               AND (@SalesApprovalStatus = 1)
           )
        BEGIN
            SET @SalesApprovalStatus = 3;
        END
        IF (
               (@TotalCheckBoxcount != @CheckedTotalCheckBoxcount)
               AND (@PurchasingApprovalStatus = 1)
           )
        BEGIN
            SET @PurchasingApprovalStatus = 3;
        END
        IF (
               (@TotalCheckBoxcount != @CheckedTotalCheckBoxcount)
               AND (@QualityApprovalStatus = 1)
           )
        BEGIN
            SET @QualityApprovalStatus = 3;
        END

        INSERT INTO #tblResponses
        (
            ResId
        )
        SELECT *
        FROM SplitString(@GetEnableCheckBoxIds, ',');
        DELETE FROM tbGI_QueryApprovalResponse
        WHERE GI_QueryNo = @GI_QueryId
              AND UserId = @LoginId
        IF (@SalesApprovalStatus > 0)
        BEGIN
            INSERT INTO tbGI_QueryApprovalResponse
            (
                GI_QueryNo,
                GoodsInNo,
                GoodsInLineNo,
                UserId,
                ApprovalStatus,
                ApproveDept
            )
            VALUES
            (@GI_QueryId, @GoodInNo, @GILineNo, @LoginId, @SalesApprovalStatus, 'S')
        END
        IF (@PurchasingApprovalStatus > 0)
        BEGIN
            INSERT INTO tbGI_QueryApprovalResponse
            (
                GI_QueryNo,
                GoodsInNo,
                GoodsInLineNo,
                UserId,
                ApprovalStatus,
                ApproveDept
            )
            VALUES
            (@GI_QueryId, @GoodInNo, @GILineNo, @LoginId, @PurchasingApprovalStatus, 'P')
        END
        IF (@QualityApprovalStatus > 0)
        BEGIN
            INSERT INTO tbGI_QueryApprovalResponse
            (
                GI_QueryNo,
                GoodsInNo,
                GoodsInLineNo,
                UserId,
                ApprovalStatus,
                ApproveDept
            )
            VALUES
            (@GI_QueryId, @GoodInNo, @GILineNo, @LoginId, @QualityApprovalStatus, 'Q')
        END
        IF ((SELECT COUNT(ResId) FROM #tblResponses WHERE ResId = 1) > 0)
        BEGIN
            UPDATE tbGI_QueryApprovalResponse
            SET C1 = 1
            WHERE GI_QueryNo = @GI_QueryId
                  AND UserId = @LoginId
        END
        IF ((SELECT COUNT(ResId) FROM #tblResponses WHERE ResId = 2) > 0)
        BEGIN
            UPDATE tbGI_QueryApprovalResponse
            SET C2 = 1
            WHERE GI_QueryNo = @GI_QueryId
                  AND UserId = @LoginId
        END
        IF ((SELECT COUNT(ResId) FROM #tblResponses WHERE ResId = 3) > 0)
        BEGIN
            UPDATE tbGI_QueryApprovalResponse
            SET C3 = 1
            WHERE GI_QueryNo = @GI_QueryId
                  AND UserId = @LoginId
        END
        IF ((SELECT COUNT(ResId) FROM #tblResponses WHERE ResId = 4) > 0)
        BEGIN
            UPDATE tbGI_QueryApprovalResponse
            SET C4 = 1
            WHERE GI_QueryNo = @GI_QueryId
                  AND UserId = @LoginId
        END
        IF ((SELECT COUNT(ResId) FROM #tblResponses WHERE ResId = 5) > 0)
        BEGIN
            UPDATE tbGI_QueryApprovalResponse
            SET C5 = 1
            WHERE GI_QueryNo = @GI_QueryId
                  AND UserId = @LoginId
        END
        IF ((SELECT COUNT(ResId) FROM #tblResponses WHERE ResId = 6) > 0)
        BEGIN
            UPDATE tbGI_QueryApprovalResponse
            SET C6 = 1
            WHERE GI_QueryNo = @GI_QueryId
                  AND UserId = @LoginId
        END
        IF ((SELECT COUNT(ResId) FROM #tblResponses WHERE ResId = 7) > 0)
        BEGIN
            UPDATE tbGI_QueryApprovalResponse
            SET C7 = 1
            WHERE GI_QueryNo = @GI_QueryId
                  AND UserId = @LoginId
        END
        IF ((SELECT COUNT(ResId) FROM #tblResponses WHERE ResId = 8) > 0)
        BEGIN
            UPDATE tbGI_QueryApprovalResponse
            SET C8 = 1
            WHERE GI_QueryNo = @GI_QueryId
                  AND UserId = @LoginId
        END
        IF ((SELECT COUNT(ResId) FROM #tblResponses WHERE ResId = 9) > 0)
        BEGIN
            UPDATE tbGI_QueryApprovalResponse
            SET C9 = 1
            WHERE GI_QueryNo = @GI_QueryId
                  AND UserId = @LoginId
        END
        IF ((SELECT COUNT(ResId) FROM #tblResponses WHERE ResId = 10) > 0)
        BEGIN
            UPDATE tbGI_QueryApprovalResponse
            SET C10 = 1
            WHERE GI_QueryNo = @GI_QueryId
                  AND UserId = @LoginId
        END
    ----------------------END--------------------------        
    END
    SELECT @ISInitialNotifyToSales = ISNULL(NotifyToSales, 0),
           @ISInitialNotifyToQuality = ISNULL(NotifyToQuality, 0),
           @ISInitialNotifyToPurchase = ISNULL(NotifyToPurchasing, 0),
           @UpdatedBy = UpdatedBy,
           @GI_QueryNumber = GI_QueryNumber
    FROM tbGiQueryMessage
    WHERE GI_QueryId = @GI_QueryId

    INSERT INTO tbGiQueryMessage
    (
        GoodInNo,
        GILineNo,
        QueryMessage,
        NotifyToSales,
        IsSalesApproved,
        NotifyToPurchasing,
        IsPurchasingApproved,
        NotifyToQuality,
        IsQualityApproved,
        IsSendMail,
        UpdatedBy,
        DLUP,
        INActive,
        IsInitialMessage,
        GI_QueryNumber,
        ParentQueryNo,
        QueryMessageApprover
    )
    VALUES
    (   @GoodInNo,
        @GILineNo,
        @QueryMessage,
        CASE
            WHEN @SalesApprovalStatus = 1 THEN
                1
            WHEN @SalesApprovalStatus = 2 THEN
                1
            WHEN @SalesApprovalStatus = 3 THEN
                1
            ELSE
                NULL
        END,
        @SalesApprovalStatus,
        CASE
            WHEN @PurchasingApprovalStatus = 1 THEN
                1
            WHEN @PurchasingApprovalStatus = 2 THEN
                1
            WHEN @PurchasingApprovalStatus = 3 THEN
                1
            ELSE
                NULL
        END,
        @PurchasingApprovalStatus,
        CASE
            WHEN @QualityApprovalStatus = 1 THEN
                1
            WHEN @QualityApprovalStatus = 2 THEN
                1
            WHEN @QualityApprovalStatus = 3 THEN
                1
            ELSE
                NULL
        END,
        @QualityApprovalStatus,
        1,
        @LoginId,
        CURRENT_TIMESTAMP,
        0,
        0,
        @GI_QueryNumber,
        @GI_QueryId,
        @ApproverHtml
    )
    SET @Result = @@IDENTITY;

    ------Start----        
    DECLARE @HtmlExtraPrint NVARCHAR(MAX) = '';
    DECLARE @QueryApproverMessage NVARCHAR(MAX) = '';
    DECLARE @GeneralInspectionMessage NVARCHAR(MAX) = ''

    SELECT @HtmlExtraPrint = ISNULL(HtmlPackBreakDownInfo, '')
    from tbGiQueryMessage
    where GI_QueryId = @GI_QueryId

    SELECT @QueryApproverMessage = QueryMessageApprover
    from tbGiQueryMessage
    where GI_QueryId = @Result
    SELECT @GeneralInspectionMessage
        = '<tr><td colspan="4"><b>General Inspection Notes:</b>' + ISNULL(GeneralInspectionNotes, '')
          + '</b></td></td></tr>'
    FROM tbGoodsInLine
    WHERE GoodsInLineId = @GILineNo

    SET @QueryApproverMessage
        = Replace(@QueryApproverMessage, '</tbody></table></div>', @GeneralInspectionMessage + '</tbody></table></div>')
    ----END--------        

    SET @QueryMessageOut = @QueryApproverMessage + @HtmlExtraPrint;

    UPDATE tbGiQueryMessage
    SET DraftQueryMessage = ''
    WHERE GI_QueryId = @GI_QueryId
    ---Sales Approved Script---                                          
    IF (@SalesApprovalStatus != 0)
    BEGIN
        UPDATE tbGI_QueryMessageApprovals
        SET SalesApprovalStatus = @SalesApprovalStatus,
            SalesApprovedBy = CASE
                                  WHEN @SalesApprovalStatus != 0 THEN
                                      @LoginId
                                  ELSE
                                      NULL
                              END,
            SalesApprovedDate = CASE
                                    WHEN @SalesApprovalStatus != 0 THEN
                                        CURRENT_TIMESTAMP
                                    ELSE
                                        NULL
                                END
        WHERE GI_QueryId = @GI_QueryId

        --[003] code start--   
        SELECT TOP 1
            @WareHouseID = RaisedBy
        FROM tbGI_QueryApprovalsLog
        WHERE GILineNo = @GILineNo
              AND GI_QueryId = @GI_QueryId
        ORDER BY Approval_logId ASC

        INSERT INTO tbGI_QueryApprovalsLog
        (
            [GILineNo],
            [GI_QueryId],
            [RaisedBy],
            [SentTo],
            [Department],
            [DLUP],
            [Status],
            [InActive]
        )
        VALUES
        (   @GILineNo,
            @GI_QueryId,
            @LoginId,
            ISNULL(@WareHouseID, 0),
            'Sales',
            CURRENT_TIMESTAMP,
            CASE
                WHEN @SalesApprovalStatus = 1 THEN
                    'Request Approved'
                WHEN @SalesApprovalStatus = 3 THEN
                    'Partial Approved'
                ELSE
                    'Request Declined'
            END,
            0
        )
    --[003] code end                                      
    END
    ---Quality Approved Script---                                          
    IF (@QualityApprovalStatus != 0)
    BEGIN
        --[003] code start--                                    
        SELECT TOP 1
            @WareHouseID = RaisedBy
        FROM tbGI_QueryApprovalsLog
        WHERE GILineNo = @GILineNo
              AND GI_QueryId = @GI_QueryId
        ORDER BY Approval_logId ASC

        INSERT INTO tbGI_QueryApprovalsLog
        (
            [GILineNo],
            [GI_QueryId],
            [RaisedBy],
            [SentTo],
            [Department],
            [DLUP],
            [Status],
            [InActive]
        )
        VALUES
        (   @GILineNo,
            @GI_QueryId,
            @LoginId,
            ISNULL(@WareHouseID, 0),
            'Quality',
            CURRENT_TIMESTAMP,
            CASE
                WHEN @QualityApprovalStatus = 1 THEN
                    'Request Approved'
                WHEN @QualityApprovalStatus = 3 THEN
                    'Partial Approved'
                ELSE
                    'Request Declined'
            END,
            0
        )
        --[003] code end                                            
        UPDATE tbGI_QueryMessageApprovals
        SET QualityApprovalStatus = @QualityApprovalStatus,
            QualityApprovedBy = CASE
                                    WHEN @QualityApprovalStatus != 0 THEN
                                        @LoginId
                                    ELSE
                                        NULL
                                END,
            QualityApprovedDate = CASE
                                      WHEN @QualityApprovalStatus != 0 THEN
                                          CURRENT_TIMESTAMP
                                      ELSE
                                          NULL
                                  END
        WHERE GI_QueryId = @GI_QueryId
    END
    ---Purchasing Approved Script---                                          
    IF (@PurchasingApprovalStatus != 0)
    BEGIN
        --[003] code start--                                    
        SELECT TOP 1
            @WareHouseID = RaisedBy
        FROM tbGI_QueryApprovalsLog
        WHERE GILineNo = @GILineNo
              AND GI_QueryId = @GI_QueryId
        ORDER BY Approval_logId ASC

        INSERT INTO tbGI_QueryApprovalsLog
        (
            [GILineNo],
            [GI_QueryId],
            [RaisedBy],
            [SentTo],
            [Department],
            [DLUP],
            [Status],
            [InActive]
        )
        VALUES
        (   @GILineNo,
            @GI_QueryId,
            @LoginId,
            ISNULL(@WareHouseID, 0),
            'Purchasing',
            CURRENT_TIMESTAMP,
            CASE
                WHEN @PurchasingApprovalStatus = 1 THEN
                    'Request Approved'
                WHEN @PurchasingApprovalStatus = 3 THEN
                    'Partial Approved'
                ELSE
                    'Request Declined'
            END,
            0
        )
        --[003] code end                                          
        UPDATE tbGI_QueryMessageApprovals
        SET PurchasingApprovalStatus = @PurchasingApprovalStatus,
            PurchasingApprovedBy = CASE
                                       WHEN @PurchasingApprovalStatus != 0 THEN
                                           @LoginId
                                       ELSE
                                           NULL
                                   END,
            PurchasingApprovedDate = CASE
                                         WHEN @PurchasingApprovalStatus != 0 THEN
                                             CURRENT_TIMESTAMP
                                         ELSE
                                             NULL
                                     END
        WHERE GI_QueryId = @GI_QueryId
    END

    IF (LEN(@CCUserId) > 0)
    BEGIN
        SET @CCUserId = REPLACE(@CCUserId, '||', ',')
        DELETE FROM tbGI_QueryCCUsers
        WHERE GI_QueryNo = @GI_QueryId
              AND GILineNo = @GILineNo
              AND UserId IN (
                                SELECT VALUE FROM STRING_SPLIT(@CCUserId, ',')
                            )
        INSERT INTO tbGI_QueryCCUsers
        (
            GI_QueryNo,
            GILineNo,
            UserId,
            UpdatedBy,
            DLUP,
            IsCCMailGroupId
        )
        SELECT @GI_QueryId,
               @GILineNo,
               [value],
               @LoginId,
               CURRENT_TIMESTAMP,
               0
        FROM STRING_SPLIT(@CCUserId, ',')
    END
    IF (LEN(@CCGroupIDs) > 0)
    BEGIN
        SET @CCGroupIDs = REPLACE(@CCGroupIDs, '||', ',')
        DELETE FROM tbGI_QueryCCUsers
        WHERE GI_QueryNo = @GI_QueryId
              AND GILineNo = @GILineNo
              AND ISNULL(IsCCMailGroupId, 0) = 1
              AND UserId IN (
                                SELECT VALUE FROM STRING_SPLIT(@CCGroupIDs, ',')
                            )
        INSERT INTO tbGI_QueryCCUsers
        (
            GI_QueryNo,
            GILineNo,
            UserId,
            UpdatedBy,
            DLUP,
            IsCCMailGroupId
        )
        SELECT @GI_QueryId,
               @GILineNo,
               [value],
               @LoginId,
               CURRENT_TIMESTAMP,
               1
        FROM STRING_SPLIT(@CCGroupIDs, ',')
    END

    IF (@SalesSupportApproverId = @LoginId)
    BEGIN
        SET @SalesApproverId = @SalesSupportApproverId
    END

    Delete from tbGIQueryDrafMessage
    where UpdatedBy = @LoginId
          and GI_QueryId = @GI_QueryId

    BEGIN TRY
        EXEC usp_AutoApproveGIQuery @GI_QueryId
    END TRY
    BEGIN CATCH

    END CATCH
    SELECT @ISInitialNotifyToSales AS ISInitialNotifyToSales,
           @ISInitialNotifyToQuality AS ISInitialNotifyToQuality,
           @ISInitialNotifyToPurchase AS ISInitialNotifyToPurchase,
           @Result AS Result,
           @UpdatedBy AS UpdatedBy,
           @NoReplyId AS NoReplyId,
           @NoReplyEmail AS NoReplyEmail,
           @QueryMessageOut as QueryMessageOut,
           ISNULL(@PurchasingApproverId, 0) AS PurchasingApproverId,
           ISNULL(@SalesApproverId, 0) AS SalesApproverId,
           ISNULL(@ClientName, '') AS ClientName

	
	IF OBJECT_ID('tempdb..#tblResponses') IS NOT NULL
	BEGIN
		DROP TABLE #tblResponses;
	END
END