Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DataListNuggets");Rebound.GlobalTrader.Site.Controls.DataListNuggets.InternalPurchaseOrders=function(n){Rebound.GlobalTrader.Site.Controls.DataListNuggets.InternalPurchaseOrders.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.DataListNuggets.InternalPurchaseOrders.prototype={get_intBuyerID:function(){return this._intBuyerID},set_intBuyerID:function(n){this._intBuyerID!==n&&(this._intBuyerID=n)},get_blnPOHub:function(){return this._blnPOHub},set_blnPOHub:function(n){this._blnPOHub!==n&&(this._blnPOHub=n)},initialize:function(){this.addSetupDataCallEvent(Function.createDelegate(this,this.setupDataCall));this.addGetDataOKEvent(Function.createDelegate(this,this.getDataOK));this.addInitCompleteEvent(Function.createDelegate(this,this.initAfterBaseIsReady));this._strPathToData="controls/DataListNuggets/InternalPurchaseOrders";this._strDataObject="InternalPurchaseOrders";Rebound.GlobalTrader.Site.Controls.DataListNuggets.InternalPurchaseOrders.callBaseMethod(this,"initialize")},initAfterBaseIsReady:function(){this.addPageTabChangedEvent(Function.createDelegate(this,this.pageTabChanged));this.applyBuyerFilter();this.updateFilterVisibility();this.getData()},dispose:function(){this.isDisposed||(this._intBuyerID=null,Rebound.GlobalTrader.Site.Controls.DataListNuggets.InternalPurchaseOrders.callBaseMethod(this,"dispose"))},pageTabChanged:function(){this._table._intCurrentPage=1;this._enmViewLevel=this._intCurrentTab;this.updateFilterVisibility();this.getData()},setupDataCall:function(){this._objData.addParameter("ViewLevel",this._enmViewLevel)},getDataOK:function(){for(var n,i,t=0,r=this._objResult.Results.length;t<r;t++)n=this._objResult.Results[t],i=[$RGT_nubButton_InternalPurchaseOrder(n.ID,n.No),$R_FN.writeDoubleCellValue($R_FN.writePartNo(n.Part,n.ROHS),$RGT_nubButton_Manufacturer(n.MfrNo,n.Mfr)),n.Price,$R_FN.writeDoubleCellValue(n.Quantity,n.QuantityOS),n.isPoHUB==!0?$R_FN.setCleanTextValue(n.ClientName):$RGT_nubButton_Company(n.CMNo,n.CM),$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(n.DeliveryDate),n.RequireASAP),$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(n.IPOStatus),$R_FN.setCellValueWithBackground(n.DeliveryStatus,n.RowCSS)),$R_FN.setCleanTextValue(n.Status)],this._table.addRow(i,n.ID,!1),i=null,n=null},updateFilterVisibility:function(){this.getFilterField("ctlBuyerName").show(this._enmViewLevel!=0);this.getFilterField("ctlClientName").show(this._blnPOHub)},applyBuyerFilter:function(){this._intBuyerID&&this._intBuyerID>0&&this.getFilterField("ctlBuyerName").setValue(this._intBuyerID)}};Rebound.GlobalTrader.Site.Controls.DataListNuggets.InternalPurchaseOrders.registerClass("Rebound.GlobalTrader.Site.Controls.DataListNuggets.InternalPurchaseOrders",Rebound.GlobalTrader.Site.Controls.DataListNuggets.Base,Sys.IDisposable);