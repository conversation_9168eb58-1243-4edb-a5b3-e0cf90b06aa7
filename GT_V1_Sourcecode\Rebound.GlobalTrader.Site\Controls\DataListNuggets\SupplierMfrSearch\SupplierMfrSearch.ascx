<%@ Control Language="C#" CodeBehind="SupplierMfrSearch.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.DataListNuggets.SupplierMfrSearch" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Nugget:DesignBase ID="ctlDB" runat="server" BoxType="List">
	<Filters>
		<ReboundUI_DataListNugget:Filter ID="ctlFilter" runat="server">
			<FieldsLeft>
				<ReboundUI_FilterDataItemRow:TextBox id="ctlPart" runat="server" ResourceTitle="PartNo" FilterField="Part" />
		        <ReboundUI_FilterDataItemRow:CheckBox id="ctlRecentOnly" runat="server" ResourceTitle="RecentOnly" FilterField="RecentOnly" DefaultValue="true" />
		        <ReboundUI_FilterDataItemRow:CheckBox id="ctlIncludeInvoiced" runat="server" ResourceTitle="IncludeInvoiced" FilterField="IncludeInvoiced" />
				<ReboundUI_FilterDataItemRow:TextBox id="ctlCompanyName" runat="server" ResourceTitle="CompanyName" FilterField="CMName" />
				<ReboundUI_FilterDataItemRow:DropDown id="ctlReceivedBy" runat="server" ResourceTitle="ReceivedBy" DropDownType="Employee" DropDownAssembly="Rebound.GlobalTrader.Site" FilterField="ReceivedBy" />
				<ReboundUI_FilterDataItemRow:TextBox id="ctlAirWayBill" runat="server" ResourceTitle="AirWayBill" FilterField="AirWayBill" />
				<%--[001]Code Start--%>
				<ReboundUI_FilterDataItemRow:DropDown id="ctlClientName" runat="server" ResourceTitle="ClientName" DropDownType="Client" DropDownAssembly="Rebound.GlobalTrader.Site" FilterField="Client"  />				
				<%--[001]Code End--%>
			</FieldsLeft>
			<FieldsRight>
				<ReboundUI_FilterDataItemRow:Numerical id="ctlGINo" runat="server" ResourceTitle="GINo" FilterField="GINo" />
				<ReboundUI_FilterDataItemRow:Numerical id="ctlPONo" runat="server" ResourceTitle="PurchaseOrderNo" FilterField="PONo"  TextBoxMaxLength="10" />
				<ReboundUI_FilterDataItemRow:TextBox id="ctlSupplierInvoice" runat="server" ResourceTitle="SupplierInvoice" FilterField="SupplierInvoice" />
				<ReboundUI_FilterDataItemRow:TextBox id="ctlReference" runat="server" ResourceTitle="Reference" FilterField="Reference" />
				<ReboundUI_FilterDataItemRow:DateSelect id="ctlDateReceivedFrom" runat="server" ResourceTitle="DateReceivedFrom" FilterField="DateReceivedFrom" />
				<ReboundUI_FilterDataItemRow:DateSelect id="ctlDateReceivedTo" runat="server" ResourceTitle="DateReceivedTo" FilterField="DateReceivedTo" />
                <ReboundUI_FilterDataItemRow:DropDown id="ctlWarehouse" runat="server" DropDownType="Warehouse" ResourceTitle="Warehouse" DropDownAssembly="Rebound.GlobalTrader.Site" FilterField="Warehouse" />
			</FieldsRight>
		</ReboundUI_DataListNugget:Filter>
	</Filters>
</ReboundUI_Nugget:DesignBase>
