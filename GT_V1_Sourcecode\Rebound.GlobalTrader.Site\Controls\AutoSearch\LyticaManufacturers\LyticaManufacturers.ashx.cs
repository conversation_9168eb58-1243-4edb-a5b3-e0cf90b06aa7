using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;

namespace Rebound.GlobalTrader.Site.Controls.AutoSearch.Data {

	[WebService(Namespace = "http://tempuri.org/")]
	[WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
	public class LyticaManufacturers : Rebound.GlobalTrader.Site.Controls.AutoSearch.Data.Base {

		protected override void GetData() {
			List<Manufacturer> lst = null;
			try 
			{
				var partNumber = GetFormValue_String("LyticaPartNo");
				partNumber = Functions.BeautifyPartNumber(partNumber);
				var nameSearch = GetFormValue_StringForNameSearch("search");
				//GetFormValue_NullableBoolean("blnShowInactive")  GetFormValue_Boolean("ShowInactive")

				lst = Manufacturer.LyticaAutoSearch(partNumber, nameSearch, false);
				JsonObject jsn = new JsonObject();
				jsn.AddVariable("TotalRecords", lst.Count);
				JsonObject jsnRows = new JsonObject(true);
				for (int i = 0; i < lst.Count; i++) {
					if (i < lst.Count) {
						JsonObject jsnRow = new JsonObject();
						jsnRow.AddVariable("ID", lst[i].ManufacturerId);
						jsnRow.AddVariable("Name", lst[i].ManufacturerName);
						jsnRows.AddVariable(jsnRow);
						jsnRow.Dispose();
						jsnRow = null;
					}
				}
				jsn.AddVariable("Results", jsnRows);
				OutputResult(jsn);
				jsnRows.Dispose(); jsnRows = null;
				jsn.Dispose(); jsn = null;
			} catch (Exception e) {
				WriteError(e);
			} finally {
				lst = null;
			}
			base.GetData();
		}
	}
}