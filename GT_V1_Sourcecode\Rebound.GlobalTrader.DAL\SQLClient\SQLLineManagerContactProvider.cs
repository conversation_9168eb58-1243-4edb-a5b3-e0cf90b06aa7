﻿//Marker     Changed by         Date          Remarks
//[001]      <PERSON><PERSON><PERSON><PERSON>     28/01/2022    Added new class for Line Manager Contact.
using System;
using System.Data;
using System.Data.SqlClient;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;
using System.Web.Caching;
using System.Data.Common;

namespace Rebound.GlobalTrader.DAL.SqlClient
{
    public class SqlLineManagerContactProvider : LineManagerContactProvider
    {
        /// <summary>
        /// DropDown 
        /// Calls [usp_dropdown_LineManagerContacts]
        /// </summary>
        public override List<LineManagerContactDetails> DropDown(System.Int32? BuyerId, System.Int32? ClientNo, System.Boolean? UpdateManager)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_dropdown_LineManagerContacts", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 90;
                cmd.Parameters.Add("@BuyerId", SqlDbType.Int).Value = BuyerId;
                cmd.Parameters.Add("@ClientNo", SqlDbType.Int).Value = ClientNo;
                cmd.Parameters.Add("@UpdateManager", SqlDbType.Bit).Value = UpdateManager;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<LineManagerContactDetails> lst = new List<LineManagerContactDetails>();
                while (reader.Read())
                {
                    LineManagerContactDetails obj = new LineManagerContactDetails();
                    obj.LineManagerContactId = GetReaderValue_Int32(reader, "LineManagerContactId", 0);
                    obj.LineManagerContactName = GetReaderValue_String(reader, "LineManagerContactName", "");
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get RohsStatuss", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
    }
}
