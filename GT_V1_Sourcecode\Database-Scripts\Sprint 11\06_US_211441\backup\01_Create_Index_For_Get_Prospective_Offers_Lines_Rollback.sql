﻿-- DROP INDEX on tbProspectiveOfferLines if it exists
IF EXISTS (SELECT 1 FROM sys.indexes WHERE name = 'IX_tbProspectiveOfferLines_ProspectiveOfferNo_Part_ProspectiveOfferLineId' AND object_id = OBJECT_ID('tbProspectiveOfferLines'))
BEGIN
    DROP INDEX IX_tbProspectiveOfferLines_ProspectiveOfferNo_Part_ProspectiveOfferLineId ON tbProspectiveOfferLines;
END
IF EXISTS (SELECT 1 FROM sys.indexes WHERE name = 'IX_tbProspectiveOfferLines_ProspectiveOfferNo_Part_ManufacturerNo' AND object_id = OBJECT_ID('tbProspectiveOfferLines'))
BEGIN
    CREATE INDEX IX_tbProspectiveOfferLines_ProspectiveOfferNo_Part_ManufacturerNo ON tbProspectiveOfferLines;
END

-- DROP INDEX on tbClient if it exists
IF EXISTS (SELECT 1 FROM sys.indexes WHERE name = 'IX_tbClient_ClientId' AND object_id = OBJECT_ID('tbClient'))
BEGIN
    DROP INDEX IX_tbClient_ClientId ON tbClient;
END

-- DROP INDEX on tbManufacturer if it exists
IF EXISTS (SELECT 1 FROM sys.indexes WHERE name = 'IX_tbManufacturer_ManufacturerId' AND object_id = OBJECT_ID('tbManufacturer'))
BEGIN
    DROP INDEX IX_tbManufacturer_ManufacturerId ON tbManufacturer;
END

-- DROP INDEX on tbLyticaAPI if it exists
IF EXISTS (SELECT 1 FROM sys.indexes WHERE name = 'IX_tbLyticaAPI_OriginalPartSearched_Manufacturer' AND object_id = OBJECT_ID('tbLyticaAPI'))
BEGIN
    DROP INDEX IX_tbLyticaAPI_OriginalPartSearched_Manufacturer ON tbLyticaAPI;
END

-- DROP INDEX on tbIHSparts if it exists
IF EXISTS (SELECT 1 FROM sys.indexes WHERE name = 'IX_tbIHSparts_FullPart_ManufacturerFullName' AND object_id = OBJECT_ID('tbIHSparts'))
BEGIN
    CREATE INDEX IX_tbIHSparts_FullPart_ManufacturerFullName ON tbIHSparts;
END