﻿/*
============================================================================================================================ 
TASK			UPDATED BY       DATE				ACTION		DESCRIPTION  
[US-235854]     CuongDox		 12-Mar-2025		CREATE		IPO- Simplified HUBRFQ Creation - Addition of PPV/ Bom Qualification at creation stage (Client Side)
============================================================================================================================  
*/
CREATE OR ALTER PROCEDURE [dbo].[usp_selectAll_Temp_PVVBOM_Question]                                             
@BomIdGenerated NVARCHAR(100) null,                                                      
@IsPoHub bit  = 0                                                                       
AS                                                                
begin     
  
 DECLARE  @Count  integer        
SELECT   @Count = COUNT (*)          
FROM    dbo.tbHUBRFQPVVAnswerTemp           
WHERE    BomNoGenerated = @BomIdGenerated   
   
  
 IF  @Count  = 0          
 BEGIN    
SELECT                                                                          
  PVVQuestionId    
 ,PVVQuestionName    
 ,0 as  PVVAnswerId  
 ,null as PVVAnswerName  
 ,null as BomNo  
 ,null HUBRFQNo  
 ,DLUP    
 ,Inactive    
 ,ClientNo    
 ,UpdatedBy    
FROM  tbHUBRFQPVVQuestion where Inactive=0               
ORDER BY PVVQuestionId            
end  
else  
begin  
SELECT                
      PVA.PVVAnswerId  
     ,PVA.PVVAnswerId as PVVQuestionId  
 --,PVA.PVVQuestionNo as PVVQuestionId  
 ,PVQ.PVVQuestionName  
 ,PVA.BomNo  
 ,PVA.HUBRFQNo  
 ,PVA.PVVAnswerName  
 ,PVA.DLUP  
 ,PVA.Inactive  
 ,PVA.ClientNo  
 ,PVA.UpdatedBy  
FROM  tbHUBRFQPVVAnswerTemp PVA   
left join tbHUBRFQPVVQuestion PVQ on PVA.PVVQuestionNo=PVQ.PVVQuestionId  
where PVA.BomNoGenerated=@BomIdGenerated and PVA.Inactive=0    
end  
end   
  