///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//
// RP 23.12.2009:
// - render state on server
//
// RP 30.11.2009:
// - allow passing of initial Name search
//
// RP 16.10.2009:
// - Changes due to changes in base class
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DataListNuggets");

Rebound.GlobalTrader.Site.Controls.DataListNuggets.Manufacturers = function(element) { 
	Rebound.GlobalTrader.Site.Controls.DataListNuggets.Manufacturers.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.DataListNuggets.Manufacturers.prototype = {

	get_ibtnAdd: function () { return this._ibtnAdd; }, set_ibtnAdd: function (v) { if (this._ibtnAdd !== v) this._ibtnAdd = v; },

	initialize: function() {
		this.addInitCompleteEvent(Function.createDelegate(this, this.initAfterBaseIsReady));
		this.addGetDataOKEvent(Function.createDelegate(this, this.getDataOK));
		this._strPathToData = "controls/DataListNuggets/Manufacturers";
		this._strDataObject = "Manufacturers";

		//if (this._ibtnAdd) {
		//	$R_IBTN.addClick(this._ibtnAdd, Function.createDelegate(this, this.showAddForm));
		//	this._frmAdd = $find(this._aryFormIDs[0]);
		//	//this._frmAdd.addSaveComplete(Function.createDelegate(this, this.saveAddComplete));
		//	//this._frmAdd.addSaveError(Function.createDelegate(this, this.saveAddError));
		//}

		Rebound.GlobalTrader.Site.Controls.DataListNuggets.Manufacturers.callBaseMethod(this, "initialize");
	},

	showAddForm: function () {
		if (this._ibtnAdd) {
			//this._frmAdd.setFieldValue("ctlSalesman", this._intLoginID);
			this.showForm(this._frmAdd, true);
		} else {
			this.showContent(true);
		}
	},
	
	initAfterBaseIsReady: function() {
		this.getData();
	},

	dispose: function() { 
		if (this.isDisposed) return;
		Rebound.GlobalTrader.Site.Controls.DataListNuggets.Manufacturers.callBaseMethod(this, "dispose");
	},

	getDataOK: function() {
		for (var i = 0, l = this._objResult.Results.length; i < l; i++) {
			var row = this._objResult.Results[i];
			//alert(row.Inactive);
			var strCSS = (row.Inactive) ? "ceased" : "";
			var aryData = [
				$RGT_nubButton_Manufacturer(row.ID, row.Code, row.AdvisoryNotes)
				, $R_FN.setCleanTextValue(row.Name)
				, (row.URL) ? String.format("<a href=\"{0}\" target=\"_blank\" class=\"nubButton nubButtonAlignLeft\">{0}</a>", $R_FN.formatURL($R_FN.setCleanTextValue(row.URL))) : ""
				, $R_FN.setCleanTextValue(row.ConflictResource)
				, $R_FN.setCleanTextValue(row.GroupName)
				, $R_FN.setCleanTextValue(row.GroupCode)
				, $R_FN.setCleanTextValue(row.SystemManufacturer)
			];
			// alert(strCSS);
           //  this._tbl.addRow(aryData, row.ID, (row.ID == this._intItemID), xtraData, strCSS);
			this._table.addRow(aryData, row.ID, false,null,strCSS);
			aryData = null; row = null;strCSS=null;
		}
	}
	
};
Rebound.GlobalTrader.Site.Controls.DataListNuggets.Manufacturers.registerClass("Rebound.GlobalTrader.Site.Controls.DataListNuggets.Manufacturers", Rebound.GlobalTrader.Site.Controls.DataListNuggets.Base, Sys.IDisposable);
