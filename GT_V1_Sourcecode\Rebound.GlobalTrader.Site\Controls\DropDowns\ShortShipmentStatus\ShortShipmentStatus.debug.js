///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//
// RP 06.10.2009:
// - Changes due to changes in base class
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");

Rebound.GlobalTrader.Site.Controls.DropDowns.ShortShipmentStatus = function(element) { 
	Rebound.GlobalTrader.Site.Controls.DropDowns.ShortShipmentStatus.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.DropDowns.ShortShipmentStatus.prototype = {
    get_IsPartialShortShipmentStatus: function () { return this._IsPartialShortShipmentStatus; }, set_IsPartialShortShipmentStatus: function (v) { if (this._IsPartialShortShipmentStatus !== v) this._IsPartialShortShipmentStatus = v; },
   // get_IsShipPartialOrHoldForRemain: function () { return this._IsShipPartialOrHoldForRemain; }, set_IsShipPartialOrHoldForRemain: function (v) { if (this._IsShipPartialOrHoldForRemain !== v) this._IsShipPartialOrHoldForRemain = v; },

	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.DropDowns.ShortShipmentStatus.callBaseMethod(this, "initialize");
		this.addSetupDataCall(Function.createDelegate(this, this.setupDataCall));
		this.addGetDataOK(Function.createDelegate(this, this.dataCallOK));
	},
	
	dispose: function() { 
        if (this.isDisposed) return;
        this._IsPartialShortShipmentStatus = null;
        this._IsPartialShortShipmentStatus24 = null;
        this._IsPartialShortShipmentStatus25 = null;
		Rebound.GlobalTrader.Site.Controls.DropDowns.ShortShipmentStatus.callBaseMethod(this, "dispose");
	},
	
    setupDataCall: function () { 
		this._objData.set_PathToData("controls/DropDowns/ShortShipmentStatus");
		this._objData.set_DataObject("ShortShipmentStatus");
		this._objData.set_DataAction("GetData");
        this._objData.addParameter("IsPartialShortShipmentStatus", this._IsPartialShortShipmentStatus);
        this._objData.addParameter("_IsPartialShortShipmentStatus24", this._IsPartialShortShipmentStatus24);
        this._objData.addParameter("_IsPartialShortShipmentStatus25", this._IsPartialShortShipmentStatus25);
	},
	
	dataCallOK: function() { 
		var result = this._objData._result;
		if (result.Types) {
			for (var i = 0; i < result.Types.length; i++) {
				this.addOption(result.Types[i].Name, result.Types[i].ID);
			}
		}
	}
	
};

Rebound.GlobalTrader.Site.Controls.DropDowns.ShortShipmentStatus.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.ShortShipmentStatus", Rebound.GlobalTrader.Site.Controls.DropDowns.Base);
