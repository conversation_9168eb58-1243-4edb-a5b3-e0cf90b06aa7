<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="CommunicationLogType_Edit" xml:space="preserve">
    <value>Redigieren Sie Hauptinformationen </value>
  </data>
  <data name="CompanyAddresses_Add" xml:space="preserve">
    <value>Addieren Sie neue Adresse </value>
  </data>
  <data name="CompanyAddresses_Cease" xml:space="preserve">
    <value>Hören Sie Adresse auf</value>
  </data>
  <data name="CompanyAddresses_DefaultBill" xml:space="preserve">
    <value>Halten Sie Rückstellungs-Gebührenzählungs-Rede</value>
  </data>
  <data name="CompanyAddresses_Edit" xml:space="preserve">
    <value>Redigieren Sie Adresse</value>
  </data>
  <data name="CompanyContactLog_Add" xml:space="preserve">
    <value>Addieren Sie Kontakt-Maschinenbordbuch-Einzelteil </value>
  </data>
  <data name="CompanyContactLog_Delete" xml:space="preserve">
    <value>Löschung-Kontakt-Maschinenbordbuch-Einzelteil </value>
  </data>
  <data name="CompanyContactLog_Edit" xml:space="preserve">
    <value>Redigieren Sie Einzelteil</value>
  </data>
  <data name="CompanyMainInfo_Edit" xml:space="preserve">
    <value>Redigieren Sie Hauptinformationen </value>
  </data>
  <data name="CompanyManufacturers_Add" xml:space="preserve">
    <value>Addieren Sie den gelieferten Hersteller </value>
  </data>
  <data name="CompanyManufacturers_Delete" xml:space="preserve">
    <value>Löschung-Hersteller geliefert </value>
  </data>
  <data name="CompanyManufacturers_Edit" xml:space="preserve">
    <value>Redigieren Sie Hauptinformationen </value>
  </data>
  <data name="CompanyPurchasingInfo_Edit" xml:space="preserve">
    <value>Redigieren Sie Einzelteil</value>
  </data>
  <data name="CompanySalesInfo_Edit" xml:space="preserve">
    <value>Redigieren Sie Verkaufs-Informationen </value>
  </data>
  <data name="CompanyType_Add" xml:space="preserve">
    <value>Addieren Sie neue Firma Art</value>
  </data>
  <data name="CompanyType_Edit" xml:space="preserve">
    <value>Redigieren Sie Hauptinformationen </value>
  </data>
  <data name="ContactExtendedInfo_Edit" xml:space="preserve">
    <value>Redigieren Sie Einzelteil</value>
  </data>
  <data name="ContactMainInfo_Edit" xml:space="preserve">
    <value>Redigieren Sie Hauptinformationen </value>
  </data>
  <data name="ContactsForCompany_Add" xml:space="preserve">
    <value>Addieren Sie neuen Kontakt</value>
  </data>
  <data name="ContactsForCompany_Delete" xml:space="preserve">
    <value>Löschung-Kontakt </value>
  </data>
  <data name="Country_Add" xml:space="preserve">
    <value>Addieren Sie neues Land</value>
  </data>
  <data name="Country_Edit" xml:space="preserve">
    <value>Redigieren Sie Einzelteil</value>
  </data>
  <data name="Currency_Add" xml:space="preserve">
    <value>Addieren Sie neue Währung</value>
  </data>
  <data name="Currency_Edit" xml:space="preserve">
    <value>Redigieren Sie Hauptinformationen </value>
  </data>
  <data name="CustomerRequirementAdd_Add" xml:space="preserve">
    <value>Addieren Sie neue Anforderung</value>
  </data>
  <data name="Division_Add" xml:space="preserve">
    <value>Addieren Sie neue Abteilung </value>
  </data>
  <data name="Division_Edit" xml:space="preserve">
    <value>Redigieren Sie Einzelteil</value>
  </data>
  <data name="ManufacturerMainInfo_Edit" xml:space="preserve">
    <value>Redigieren Sie Hauptinformationen </value>
  </data>
  <data name="GlobalCountryList_Edit" xml:space="preserve">
    <value>Redigieren Sie Einzelteil</value>
  </data>
  <data name="GlobalCurrencyList_Edit" xml:space="preserve">
    <value>Redigieren Sie Einzelteil</value>
  </data>
  <data name="IndustryType_Add" xml:space="preserve">
    <value>Addieren Sie neue Industrie-Art </value>
  </data>
  <data name="IndustryType_Edit" xml:space="preserve">
    <value>Redigieren Sie Einzelteil</value>
  </data>
  <data name="Package_Add" xml:space="preserve">
    <value>Addieren Sie neues Paket</value>
  </data>
  <data name="Package_Edit" xml:space="preserve">
    <value>Redigieren Sie Einzelteil</value>
  </data>
  <data name="POAdd_Add" xml:space="preserve">
    <value>Addieren Sie neuen Kaufauftrag</value>
  </data>
  <data name="Product_Add" xml:space="preserve">
    <value>Addieren Sie neues Produkt</value>
  </data>
  <data name="Product_Edit" xml:space="preserve">
    <value>Redigieren Sie Einzelteil</value>
  </data>
  <data name="QuoteAdd_Add" xml:space="preserve">
    <value>Addieren Sie neuen Preisangabe</value>
  </data>
  <data name="ShipVia_Add" xml:space="preserve">
    <value>Addieren Sie neue Verschiffen-Methode</value>
  </data>
  <data name="ShipVia_Edit" xml:space="preserve">
    <value>Redigieren Sie Einzelteile</value>
  </data>
  <data name="SOAdd_Add" xml:space="preserve">
    <value>Addieren Sie neuen Verkaufs-Auftrag</value>
  </data>
  <data name="SOLines_Add" xml:space="preserve">
    <value>Addieren Sie neue Linie</value>
  </data>
  <data name="SOLines_Allocate" xml:space="preserve">
    <value>Teilen Sie Linie zu</value>
  </data>
  <data name="SOLines_Deallocate" xml:space="preserve">
    <value>Geben Sie Linie frei</value>
  </data>
  <data name="SOLines_Delete" xml:space="preserve">
    <value>Löschung-Linie </value>
  </data>
  <data name="SOLines_Post" xml:space="preserve">
    <value>Pfosten-Linie</value>
  </data>
  <data name="SOLines_Unpost" xml:space="preserve">
    <value>Unpost Linie</value>
  </data>
  <data name="SOMainInfo_Edit" xml:space="preserve">
    <value>Redigieren Sie Hauptinformationen </value>
  </data>
  <data name="Sourcing_AddToReq" xml:space="preserve">
    <value>Fügen Sie Anforderung hinzu</value>
  </data>
  <data name="Sourcing_EditOffer" xml:space="preserve">
    <value>Redigieren Sie Einzelteil</value>
  </data>
  <data name="Sourcing_RFQ" xml:space="preserve">
    <value>Ersuchen um Preisangabe</value>
  </data>
  <data name="StockLogReason_Add" xml:space="preserve">
    <value>Addieren Sie neuen auf lagermaschinenbordbuch-Grund</value>
  </data>
  <data name="StockLogReason_Edit" xml:space="preserve">
    <value>Redigieren Sie Einzelteil</value>
  </data>
  <data name="Tax_Add" xml:space="preserve">
    <value>Addieren Sie neue Steuer</value>
  </data>
  <data name="Tax_Edit" xml:space="preserve">
    <value>Redigieren Sie Einzelteile</value>
  </data>
  <data name="Team_Add" xml:space="preserve">
    <value>Addieren Sie neue Mannschaft</value>
  </data>
  <data name="Team_Edit" xml:space="preserve">
    <value>Redigieren Sie Einzelteile</value>
  </data>
  <data name="Terms_Add" xml:space="preserve">
    <value>Addieren Sie neue Ausdrücke</value>
  </data>
  <data name="Terms_Edit" xml:space="preserve">
    <value>Redigieren Sie Einzelteile</value>
  </data>
  <data name="User_Add" xml:space="preserve">
    <value>Addieren Sie neuen Benutzer</value>
  </data>
  <data name="User_Edit" xml:space="preserve">
    <value>Redigieren Sie Einzelteile</value>
  </data>
  <data name="Warehouse_Add" xml:space="preserve">
    <value>Addieren Sie neues Lager</value>
  </data>
  <data name="Warehouse_Edit" xml:space="preserve">
    <value>Redigieren Sie Einzelteile</value>
  </data>
  <data name="ManufacturerSuppliers_Add" xml:space="preserve">
    <value>Addieren Sie Lieferanten, der sich verteilt</value>
  </data>
  <data name="ManufacturerSuppliers_Delete" xml:space="preserve">
    <value>Löschung-Lieferant, der sich verteilt </value>
  </data>
  <data name="ManufacturerSuppliers_Edit" xml:space="preserve">
    <value>Redigieren Sie Einzelteil</value>
  </data>
  <data name="Reason_Add" xml:space="preserve">
    <value>Addieren Sie neuen Grund</value>
  </data>
  <data name="Reason_Edit" xml:space="preserve">
    <value>Redigieren Sie Einzelteil</value>
  </data>
  <data name="MailMessages_DeleteFolder" xml:space="preserve">
    <value>Löschung-Faltblatt </value>
  </data>
  <data name="MailMessages_DeleteMessage" xml:space="preserve">
    <value>Löschung-Mitteilung </value>
  </data>
  <data name="MailMessages_EditFolder" xml:space="preserve">
    <value>Redigieren Sie Einzelteil</value>
  </data>
  <data name="MailMessages_MoveMessage" xml:space="preserve">
    <value>Verschieben Sie Mitteilung</value>
  </data>
  <data name="MailMessages_NewFolder" xml:space="preserve">
    <value>Neues Faltblatt</value>
  </data>
  <data name="MailMessages_NewMessage" xml:space="preserve">
    <value>Neue Mitteilung</value>
  </data>
  <data name="ManufacturerCompanies_Add" xml:space="preserve">
    <value>Addieren Sie in Verbindung stehendes Firma</value>
  </data>
  <data name="ManufacturerCompanies_Delete" xml:space="preserve">
    <value>Entfernen Sie in Verbindung stehendes Firma</value>
  </data>
  <data name="QuoteMainInfo_Edit" xml:space="preserve">
    <value>Redigieren Sie Hauptinformationen </value>
  </data>
  <data name="CompanyAdd_Add" xml:space="preserve">
    <value>Addieren Sie neue Firma</value>
  </data>
  <data name="ManufacturerAdd_Add" xml:space="preserve">
    <value>Addieren Sie neuen Hersteller </value>
  </data>
  <data name="QuoteLines_Add" xml:space="preserve">
    <value>Redigieren Sie Linie </value>
  </data>
  <data name="QuoteLines_Close" xml:space="preserve">
    <value>Nahe Linie</value>
  </data>
  <data name="MailMessages_MarkAsToDo" xml:space="preserve">
    <value>Verursachen Sie, um Einzelteil zu tun</value>
  </data>
  <data name="ToDo_Delete" xml:space="preserve">
    <value>Löschung, zum der Einzelteile zu tun </value>
  </data>
  <data name="ToDo_Edit" xml:space="preserve">
    <value>Redigieren Sie Einzelteil</value>
  </data>
  <data name="ToDo_MarkComplete" xml:space="preserve">
    <value>Markieren Sie die kompletten Einzelteile</value>
  </data>
  <data name="ToDo_MarkIncomplete" xml:space="preserve">
    <value>Markieren Sie die unvollständigen Einzelteile</value>
  </data>
  <data name="CommunicationLogType_Add" xml:space="preserve">
    <value>Addieren Sie neue CommunicationLog Art </value>
  </data>
  <data name="UserPreferences_Edit" xml:space="preserve">
    <value>Redigieren Sie Einzelteil</value>
  </data>
  <data name="UserProfile_Edit" xml:space="preserve">
    <value>Redigieren Sie Einzelteil</value>
  </data>
  <data name="UserProfile_ChangePassword" xml:space="preserve">
    <value>Ändern Sie Kennwort</value>
  </data>
  <data name="CusReqSourcingResults_Add" xml:space="preserve">
    <value>Addieren Sie neues Auftreten-Resultat</value>
  </data>
  <data name="CusReqSourcingResults_Edit" xml:space="preserve">
    <value>Redigieren Sie Einzelteil</value>
  </data>
  <data name="CustomerRequirementMainInfo_Edit" xml:space="preserve">
    <value>Redigieren Sie Hauptinformationen </value>
  </data>
  <data name="CustomerRequirementMainInfo_AddAlternate" xml:space="preserve">
    <value>Addieren Sie wechselndes Teil </value>
  </data>
  <data name="QuoteLines_Edit" xml:space="preserve">
    <value>Redigieren Sie Linie </value>
  </data>
  <data name="CRMALines_Add" xml:space="preserve">
    <value>Addieren Sie Linie des Kunden-RMA </value>
  </data>
  <data name="CRMALines_Delete" xml:space="preserve">
    <value>Löschung-Linie </value>
  </data>
  <data name="CRMALines_Edit" xml:space="preserve">
    <value>Redigieren Sie Linie </value>
  </data>
  <data name="SRMALines_Add" xml:space="preserve">
    <value>Addieren Sie neue Linie</value>
  </data>
  <data name="SRMALines_Delete" xml:space="preserve">
    <value>Löschung-Linie </value>
  </data>
  <data name="SRMALines_Edit" xml:space="preserve">
    <value>Redigieren Sie Linie </value>
  </data>
  <data name="SRMAMainInfo_Edit" xml:space="preserve">
    <value>Redigieren Sie Hauptinformationen </value>
  </data>
  <data name="SRMAAdd_Add" xml:space="preserve">
    <value>Addieren Sie neuen Lieferanten RMA</value>
  </data>
  <data name="CRMAAdd_Add" xml:space="preserve">
    <value>Addieren Sie neuen Kunden RMA</value>
  </data>
  <data name="Feedback_Add" xml:space="preserve">
    <value>Senden Sie Rückgespräch</value>
  </data>
  <data name="SecurityGroupMembers_EditMembers" xml:space="preserve">
    <value>Redigieren Sie Einzelteil</value>
  </data>
  <data name="SecurityGroupPermissionsGeneral_Edit" xml:space="preserve">
    <value>Redigieren Sie Einzelteil</value>
  </data>
  <data name="SecurityGroupPermissionsReports_Edit" xml:space="preserve">
    <value>Redigieren Sie Einzelteile</value>
  </data>
  <data name="SecurityGroups_Add" xml:space="preserve">
    <value>Addieren Sie neue Sicherheits-Gruppe</value>
  </data>
  <data name="SecurityGroups_Delete" xml:space="preserve">
    <value>Löschung-Sicherheits-Gruppe</value>
  </data>
  <data name="SecurityGroups_Edit" xml:space="preserve">
    <value>Redigieren Sie Einzelteil</value>
  </data>
  <data name="CRMAReceivingLines_Receive" xml:space="preserve">
    <value>Empfangen Sie Linie des Kunden-RMA</value>
  </data>
  <data name="POReceivingLines_Receive" xml:space="preserve">
    <value>Empfangen Sie Kaufauftrag-Linie</value>
  </data>
  <data name="InvoiceLines_Add" xml:space="preserve">
    <value>Addieren Sie neue Linie</value>
  </data>
  <data name="InvoiceLines_Edit" xml:space="preserve">
    <value>Redigieren Sie Linie </value>
  </data>
  <data name="InvoiceLines_EditAllocation" xml:space="preserve">
    <value>Redigieren Sie Einzelteil</value>
  </data>
  <data name="InvoiceMainInfo_Edit" xml:space="preserve">
    <value>Redigieren Sie Hauptinformationen </value>
  </data>
  <data name="SecurityUserGroups_EditMembers" xml:space="preserve">
    <value>Redigieren Sie Einzelteile</value>
  </data>
  <data name="SecurityUsers_Add" xml:space="preserve">
    <value>Addieren Sie neuen Benutzer</value>
  </data>
  <data name="SecurityUsers_Disable" xml:space="preserve">
    <value>Sperrungs-Benutzer</value>
  </data>
  <data name="SecurityUsers_Edit" xml:space="preserve">
    <value>Redigieren Sie Einzelteil</value>
  </data>
  <data name="SecurityUsers_Enable" xml:space="preserve">
    <value>Ermöglichen Sie Benutzer</value>
  </data>
  <data name="SourcingLinks_Add" xml:space="preserve">
    <value>Addieren Sie neue Auftreten-Verbindung</value>
  </data>
  <data name="SourcingLinks_Delete" xml:space="preserve">
    <value>Löschung-Auftreten-Verbindung </value>
  </data>
  <data name="SourcingLinks_Edit" xml:space="preserve">
    <value>Redigieren Sie Einzelteil</value>
  </data>
  <data name="UserProfile_ResetPassword" xml:space="preserve">
    <value>Stellen Sie Kennwort zurück</value>
  </data>
  <data name="GlobalCurrencyList_Add" xml:space="preserve">
    <value>Addieren Sie neue Vorlagenwährung</value>
  </data>
  <data name="POMainInfo_Edit" xml:space="preserve">
    <value>Redigieren Sie Hauptinformationen </value>
  </data>
  <data name="POLines_Add" xml:space="preserve">
    <value>Addieren Sie neue Linie</value>
  </data>
  <data name="POLines_Allocate" xml:space="preserve">
    <value>Teilen Sie Linie zu</value>
  </data>
  <data name="POLines_Deallocate" xml:space="preserve">
    <value>Geben Sie Linie frei</value>
  </data>
  <data name="POLines_Delete" xml:space="preserve">
    <value>Löschung-Linie </value>
  </data>
  <data name="POLines_Post" xml:space="preserve">
    <value>Pfosten-Linie</value>
  </data>
  <data name="POLines_Unpost" xml:space="preserve">
    <value>Unpost Linie</value>
  </data>
  <data name="POLines_Edit" xml:space="preserve">
    <value>Redigieren Sie Linie </value>
  </data>
  <data name="SOLines_Edit" xml:space="preserve">
    <value>Redigieren Sie Linie </value>
  </data>
  <data name="GIAdd_Add" xml:space="preserve">
    <value>Fügen Sie neue Waren in der Anmerkung hinzu </value>
  </data>
  <data name="InvoiceAdd_Add" xml:space="preserve">
    <value>Addieren Sie neue Rechnung </value>
  </data>
  <data name="ContactsForCompany_MakeDefaultPO" xml:space="preserve">
    <value>Bilden Sie Rückstellung für POs</value>
  </data>
  <data name="ContactsForCompany_MakeDefaultSO" xml:space="preserve">
    <value>Bilden Sie Rückstellung für SOs</value>
  </data>
  <data name="SOShippingLines_Ship" xml:space="preserve">
    <value>Schiffs-Verkaufs-Auftrags-Linie</value>
  </data>
  <data name="Currency_EditRates" xml:space="preserve">
    <value>Redigieren Sie Einzelteile</value>
  </data>
  <data name="StockAllocations_Deallocate" xml:space="preserve">
    <value>Geben Sie Vorrat frei</value>
  </data>
  <data name="StockMainInfo_Edit" xml:space="preserve">
    <value>Redigieren Sie Hauptinformationen </value>
  </data>
  <data name="StockMainInfo_Quarantine" xml:space="preserve">
    <value>Quarantäne-Vorrat</value>
  </data>
  <data name="StockMainInfo_Split" xml:space="preserve">
    <value>Aufgeteilter Vorrat</value>
  </data>
  <data name="StockMainInfo_MakeAvailable" xml:space="preserve">
    <value>Stellen Sie Vorrat zur Verfügung</value>
  </data>
  <data name="CRMAMainInfo_Edit" xml:space="preserve">
    <value>Redigieren Sie Hauptinformationen </value>
  </data>
  <data name="Tax_EditRates" xml:space="preserve">
    <value>Redigieren Sie Einzelteile</value>
  </data>
  <data name="LotMainInfo_Edit" xml:space="preserve">
    <value>Redigieren Sie Hauptinformationen </value>
  </data>
  <data name="CreditLines_Add" xml:space="preserve">
    <value>Addieren Sie Kreditverwaltung </value>
  </data>
  <data name="CreditLines_Delete" xml:space="preserve">
    <value>Löschung-Linie </value>
  </data>
  <data name="CreditLines_Edit" xml:space="preserve">
    <value>Redigieren Sie Linie </value>
  </data>
  <data name="CreditMainInfo_Edit" xml:space="preserve">
    <value>Redigieren Sie Hauptinformationen </value>
  </data>
  <data name="LotAdd_Add" xml:space="preserve">
    <value>Addieren Sie neues Los </value>
  </data>
  <data name="CreditAdd_Add" xml:space="preserve">
    <value>Addieren Sie neue Kreditnote</value>
  </data>
  <data name="ServiceAdd_Add" xml:space="preserve">
    <value>Addieren Sie neuen Service</value>
  </data>
  <data name="StockAdd_Add" xml:space="preserve">
    <value>Addieren Sie neues auf lagereinzelteil</value>
  </data>
  <data name="DebitAdd_Add" xml:space="preserve">
    <value>Addieren Sie neue Belastungsanzeige </value>
  </data>
  <data name="DebitLines_Add" xml:space="preserve">
    <value>Addieren Sie neue Linie</value>
  </data>
  <data name="DebitLines_Delete" xml:space="preserve">
    <value>Löschung-Linie </value>
  </data>
  <data name="DebitLines_Edit" xml:space="preserve">
    <value>Redigieren Sie Linie </value>
  </data>
  <data name="DebitMainInfo_Edit" xml:space="preserve">
    <value>Redigieren Sie Hauptinformationen </value>
  </data>
  <data name="MailMessageGroupMembers_EditMembers" xml:space="preserve">
    <value>Redigieren Sie Einzelteile</value>
  </data>
  <data name="MailMessageGroups_Add" xml:space="preserve">
    <value>Addieren Sie neue Post-Gruppe </value>
  </data>
  <data name="MailMessageGroups_Delete" xml:space="preserve">
    <value>Löschung-Post-Gruppe </value>
  </data>
  <data name="MailMessageGroups_Edit" xml:space="preserve">
    <value>Redigieren Sie Einzelteil</value>
  </data>
  <data name="Sourcing_AddToReq_SelectCusReq" xml:space="preserve">
    <value>Fügen Sie Kunden-Anforderung hinzu</value>
  </data>
  <data name="GILines_Delete" xml:space="preserve">
    <value>Löschung-Linie </value>
  </data>
  <data name="GILines_Edit" xml:space="preserve">
    <value>Redigieren Sie Linie </value>
  </data>
  <data name="GIMainInfo_Edit" xml:space="preserve">
    <value>Redigieren Sie Hauptinformationen </value>
  </data>
  <data name="SRMAShippingLines_Ship" xml:space="preserve">
    <value>Linie des Schiffs-Lieferanten-RMA</value>
  </data>
  <data name="SOMainInfo_Authorise" xml:space="preserve">
    <value>Autorisieren Sie Verkaufs-Auftrag</value>
  </data>
  <data name="SOMainInfo_Deauthorise" xml:space="preserve">
    <value>De-authorise Verkaufs-Auftrag</value>
  </data>
  <data name="CompanyAddresses_DefaultShip" xml:space="preserve">
    <value>Halten Sie Rückstellungs-Verschiffen-Rede</value>
  </data>
  <data name="StockImages_Add" xml:space="preserve">
    <value>Addieren Sie ein neues Bild </value>
  </data>
  <data name="LotItems_Delete_Service" xml:space="preserve">
    <value>Löschung nicht zugewiesene Dienstleistungen </value>
  </data>
  <data name="LotItems_Delete_Stock" xml:space="preserve">
    <value>Löschung nicht zugewiesener Vorrat </value>
  </data>
  <data name="LotItems_Transfer_Service" xml:space="preserve">
    <value>Übergangsdienstleistungen</value>
  </data>
  <data name="LotItems_Transfer_Stock" xml:space="preserve">
    <value>Übergangsvorrat</value>
  </data>
  <data name="LotMainInfo_Delete" xml:space="preserve">
    <value>Löschung-Los</value>
  </data>
  <data name="ServiceMainInfo_Edit" xml:space="preserve">
    <value>Redigieren Sie Hauptinformationen </value>
  </data>
  <data name="StockImages_Delete" xml:space="preserve">
    <value>Löschung-Bild </value>
  </data>
  <data name="DocHeaderImage_Add" xml:space="preserve">
    <value>Addieren Sie Dokumenten-Überschrift-Bild </value>
  </data>
  <data name="DocHeaderImage_Delete" xml:space="preserve">
    <value>Löschung-Dokumenten-Überschrift-Bild </value>
  </data>
  <data name="DocFooters_Edit" xml:space="preserve">
    <value>Redigieren Sie Einzelteil</value>
  </data>
  <data name="GILines_Inspect" xml:space="preserve">
    <value>Beschauen Sie Waren in der Linie</value>
  </data>
  <data name="SRMALines_Allocate" xml:space="preserve">
    <value>Teilen Sie Linie zu</value>
  </data>
  <data name="SRMALines_Deallocate" xml:space="preserve">
    <value>Geben Sie Linie frei</value>
  </data>
  <data name="ServiceMainInfo_Delete" xml:space="preserve">
    <value>Löschung-Service </value>
  </data>
  <data name="EmailDocument" xml:space="preserve">
    <value>Senden Sie eMail</value>
  </data>
  <data name="Sequencer_Edit" xml:space="preserve">
    <value>Redigieren Sie Einzelteil</value>
  </data>
  <data name="SOAuthorisation_Authorise" xml:space="preserve">
    <value>Autorisieren Sie Verkaufs-Auftrag</value>
  </data>
  <data name="SOAuthorisation_Deauthorise" xml:space="preserve">
    <value>De-authorise Verkaufs-Auftrag</value>
  </data>
  <data name="SOMainInfo_Notify" xml:space="preserve">
    <value>Verkaufs-Auftrags-Mitteilung </value>
  </data>
  <data name="DocHeaderImage_Edit" xml:space="preserve">
    <value>Redigieren Sie Einzelteil</value>
  </data>
  <data name="GIMainInfo_Notify" xml:space="preserve">
    <value>Waren in der Mitteilung </value>
  </data>
  <data name="POLines_PostAll" xml:space="preserve">
    <value>Geben Sie alle Linien bekannt</value>
  </data>
  <data name="POLines_UnpostAll" xml:space="preserve">
    <value>Unpost alle Linien</value>
  </data>
  <data name="InvoiceLines_Delete" xml:space="preserve">
    <value>Löschung-Linie </value>
  </data>
  <data name="CurrencyRateHistory_Delete" xml:space="preserve">
    <value>Löschung-Währungsstabilität </value>
  </data>
  <data name="CurrencyRateHistory_Edit" xml:space="preserve">
    <value>Redigieren Sie Einzelteil</value>
  </data>
  <data name="CusReqMainInfo_Close" xml:space="preserve">
    <value>Nahe Kunden-Anforderung</value>
  </data>
  <data name="SOLines_PostAll" xml:space="preserve">
    <value>Geben Sie alle Linien bekannt</value>
  </data>
  <data name="SOLines_UnpostAll" xml:space="preserve">
    <value>Unpost alle Linien</value>
  </data>
  <data name="CountingMethod_Add" xml:space="preserve">
    <value>Addieren Sie neue Zählung-Methode</value>
  </data>
  <data name="CountingMethod_Edit" xml:space="preserve">
    <value>Redigieren Sie Hauptinformationen </value>
  </data>
  <data name="POMainInfo_Close" xml:space="preserve">
    <value>Naher Kaufauftrag</value>
  </data>
  <data name="SOMainInfo_Close" xml:space="preserve">
    <value>Naher Verkaufs-Auftrag</value>
  </data>
  <data name="POLines_Close" xml:space="preserve">
    <value>Nahe Linie</value>
  </data>
  <data name="SOLines_Close" xml:space="preserve">
    <value>Nahe Linie</value>
  </data>
  <data name="CRMALines_Deallocate" xml:space="preserve">
    <value>Geben Sie Linie frei</value>
  </data>
  <data name="Inconterm_Add" xml:space="preserve">
    <value>Addieren Sie neue Incoterm</value>
  </data>
  <data name="Incoterm_Edit" xml:space="preserve">
    <value>Redigieren Sie Einzelteil</value>
  </data>
</root>