Type.registerNamespace("Rebound.GlobalTrader.Site.Pages.Login");Rebound.GlobalTrader.Site.Pages.Login=function(){this._intPauseTime=250};Rebound.GlobalTrader.Site.Pages.Login.prototype={initialize:function(){this._blnCheckForDatabase&&this.checkDatabaseConnection()},dispose:function(){this.isDisposed||(this._pnlDBCheck=null,this._pnlCheckingDatabase=null,this._pnlNoDatabase=null,this._pnlDatabaseOK=null,this._pnlLogin=null,this.isDisposed=!0)},checkDatabaseConnection:function(){$R_FN.showElement(this._pnlDBCheck,!0);$R_FN.showElement(this._pnlCheckingDatabase,!0);$R_FN.showElement(this._pnlNoDatabase,!1);$R_FN.showElement(this._pnlDatabaseOK,!1);$R_FN.showElement(this._pnlLogin,!1);setTimeout(Function.createDelegate(this,this.doDatabaseCheck),this._intPauseTime)},doDatabaseCheck:function(){Rebound.GlobalTrader.Site.WebServices.CheckDatabaseConnection(Function.createDelegate(this,this.databaseOK),Function.createDelegate(this,this.databaseError))},databaseOK:function(n){$R_FN.showElement(this._pnlCheckingDatabase,!1);n.length==0?(setTimeout(Function.createDelegate(this,this.showLogin),this._intPauseTime),$R_FN.showElement(this._pnlNoDatabase,!1),$R_FN.showElement(this._pnlDatabaseOK,!0)):this.databaseError()},databaseError:function(){$R_FN.showElement(this._pnlCheckingDatabase,!1);$R_FN.showElement(this._pnlNoDatabase,!0);$R_FN.showElement(this._pnlLogin,!1)},showLogin:function(){$R_FN.showElement(this._pnlLogin,!0);$R_FN.showElement(this._pnlDBCheck,!1)},doReboundClientChoice:function(n,t){Rebound.GlobalTrader.Site.WebServices.DoReboundClientChoice(n,t,Function.createDelegate(this,this.doReboundClientChoiceOK))},doReboundClientChoiceOK:function(n){n.length>0&&(location.href=n)}};Rebound.GlobalTrader.Site.Pages.Login.registerClass("Rebound.GlobalTrader.Site.Pages.Login",null,Sys.IDisposable);