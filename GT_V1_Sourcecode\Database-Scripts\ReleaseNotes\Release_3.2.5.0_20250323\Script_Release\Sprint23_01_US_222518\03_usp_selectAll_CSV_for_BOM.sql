﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

/* 
===========================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-222518]		An.TranTan			06-Mar-2025		UPDATE			Get csv files type CreateHUBRFQ/UpdateHUBRFQ: these files uploaded during create HUBRFQ action 
																	and accessed by both DMCC and client.
[US-222518]		An.TranTan			13-Mar-2025		UPDATE			Get file section: 
																	- BOM: for existing file
																	- UTILITYBOM: for file upload during Create/Update HUBRFQ			
===========================================================================================
*/
CREATE OR ALTER PROCEDURE [dbo].[usp_selectAll_CSV_for_BOM]           
    @BOMNo INT ,
	@Type varchar(20)=null       
    AS           
    SELECT  BOMCSVId        
          , BOMNo        
          , Caption         
          , [FileName]         
          , UpdatedBy          
          , DLUP
		  , 'BOM' AS Section
    FROM    dbo.tbBOMCSV        
    WHERE   BOMNo = @BOMNo  and ImportType =  @Type 
	UNION
	--[US-222518]
	SELECT  BOMCSVId        
          , BOMNo        
          , Caption         
          , [FileName]         
          , UpdatedBy          
          , DLUP
		  , 'UTILITYBOM' AS Section
    FROM    dbo.tbBOMCSV        
    WHERE   BOMNo = @BOMNo  and (ImportType =  'CreateHUBRFQ' OR ImportType = 'UpdateHUBRFQ')
GO


