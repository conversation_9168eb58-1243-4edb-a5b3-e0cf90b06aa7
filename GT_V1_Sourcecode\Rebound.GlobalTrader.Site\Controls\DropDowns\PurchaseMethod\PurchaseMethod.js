Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");Rebound.GlobalTrader.Site.Controls.DropDowns.PurchaseMethod=function(n){Rebound.GlobalTrader.Site.Controls.DropDowns.PurchaseMethod.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.DropDowns.PurchaseMethod.prototype={initialize:function(){Rebound.GlobalTrader.Site.Controls.DropDowns.PurchaseMethod.callBaseMethod(this,"initialize");this.addSetupDataCall(Function.createDelegate(this,this.setupDataCall));this.addGetDataOK(Function.createDelegate(this,this.dataCallOK))},dispose:function(){this.isDisposed||Rebound.GlobalTrader.Site.Controls.DropDowns.PurchaseMethod.callBaseMethod(this,"dispose")},setupDataCall:function(){this._objData.set_PathToData("controls/DropDowns/PurchaseMethod");this._objData.set_DataObject("PurchaseMethod");this._objData.set_DataAction("GetData")},dataCallOK:function(){var t=this._objData._result,n;if(t.Types)for(n=0;n<t.Types.length;n++)this.addOption(t.Types[n].Name,t.Types[n].ID)}};Rebound.GlobalTrader.Site.Controls.DropDowns.PurchaseMethod.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.PurchaseMethod",Rebound.GlobalTrader.Site.Controls.DropDowns.Base);