Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists");Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Stock=function(n){Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Stock.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Stock.prototype={get_strQuantityAt:function(){return this._strQuantityAt},set_strQuantityAt:function(n){this._strQuantityAt!==n&&(this._strQuantityAt=n)},get_strAvailable:function(){return this._strAvailable},set_strAvailable:function(n){this._strAvailable!==n&&(this._strAvailable=n)},initialize:function(){this.addGetDataOKEvent(Function.createDelegate(this,this.getDataOK));this.addSetupDataCallEvent(Function.createDelegate(this,this.setupDataCall));this._strPathToData="controls/DataListNuggets/AllStock";this._strDataObject="AllStock";Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Stock.callBaseMethod(this,"initialize")},dispose:function(){this.isDisposed||(this._strQuantityAt=null,this._strAvailable=null,Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Stock.callBaseMethod(this,"dispose"))},setupDataCall:function(){var n="GetData_",t=this.getFilterValue("ViewLevel");(t==null||typeof t=="undefined")&&(t=0);switch(Number.parseInvariant(t.toString())){case 0:n+="All";break;case 1:n+="Available";break;case 2:n+="Quarantined"}this._objData.set_DataAction(n)},getDataOK:function(){for(var n,t,i=0,r=this._objResult.Results.length;i<r;i++)n=this._objResult.Results[i],t=String.format('<a href="{0}">{1}<br />',$RGT_gotoURL_Stock(n.ID),$R_FN.writePartNo(n.Part,n.ROHS)),t+=n.Location.length>0?String.format(this._strQuantityAt,n.Available,n.Location):String.format(this._strAvailable,n.Available),t+="<\/a>",this._tbl.addRow([t],n.ID,!1),t=null,n=null}};Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Stock.registerClass("Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Stock",Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Base,Sys.IDisposable);