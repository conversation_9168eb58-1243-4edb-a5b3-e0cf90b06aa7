///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.HomeNuggets");

Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyOpenQuotes = function(element) { 
	Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyOpenQuotes.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyOpenQuotes.prototype = {

	get_pnlOpen: function() { return this._pnlOpen; }, 	set_pnlOpen: function(value) { if (this._pnlOpen !== value)  this._pnlOpen = value; }, 
	get_pnlRecent: function() { return this._pnlRecent; }, 	set_pnlRecent: function(value) { if (this._pnlRecent !== value)  this._pnlRecent = value; }, 
	get_tblOpen: function() { return this._tblOpen; }, 	set_tblOpen: function(value) { if (this._tblOpen !== value)  this._tblOpen = value; }, 
	get_tblRecent: function() { return this._tblRecent; }, 	set_tblRecent: function(value) { if (this._tblRecent !== value)  this._tblRecent = value; }, 
	get_pnlMore: function() { return this._pnlMore; }, 	set_pnlMore: function(value) { if (this._pnlMore !== value)  this._pnlMore = value; },
	get_lnkMore: function() { return this._lnkMore; }, set_lnkMore: function(value) { if (this._lnkMore !== value) this._lnkMore = value; },

	get_myLoginID: function() { return this.myLoginID; }, set_myLoginID: function(value) { if (this.myLoginID !== value) this.myLoginID = value; },



	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyOpenQuotes.callBaseMethod(this, "initialize");
		this.addRefreshEvent(Function.createDelegate(this, this.getData));
	},

	dispose: function() { 
		if (this.isDisposed) return;
		if (this._tblOpen) this._tblOpen.dispose();
		if (this._tblRecent) this._tblRecent.dispose();
		this._pnlOpen = null;
		this._pnlRecent = null;
		this._tblOpen = null;
		this._tblRecent = null;		
		this._pnlMore = null;
		Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyOpenQuotes.callBaseMethod(this, "dispose");
	},
	
	setupLoadingState: function() {
		$R_FN.showElement(this._pnlMore, false);
		$R_FN.showElement(this._pnlOpen, false);
		$R_FN.showElement(this._pnlRecent, false);
		Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyOpenQuotes.callBaseMethod(this, "setupLoadingState");
	},
	
	showNoData: function(bln) {
		this.showContent(true);
		$R_FN.showElement(this._pnlNoData, bln);
	},
	
	getData: function() {
	this.setupLoadingState();

	if (this._intLoginID_Other > 0) {
	    this._lnkMore.href = $RGT_gotoURL_QuoteBrowse(this._intLoginID_Other);
	    //"Ord_SOBrowse.aspx?bss=" + true + "&sp=" + this._intLoginID_Other;
	}
	else {
	    this._lnkMore.href = $RGT_gotoURL_QuoteBrowse(this.myLoginID);
	}
		var obj = new Rebound.GlobalTrader.Site.Data();
		obj.set_PathToData("controls/HomeNuggets/MyOpenQuotes");
		obj.set_DataObject("MyOpenQuotes");
		obj.set_DataAction("GetData");
		obj.addParameter("rowcount", this._intRowCount);
		obj.addParameter("OtherLoginID", this._intLoginID_Other);
		obj.addDataOK(Function.createDelegate(this, this.getDataComplete));
		obj.addError(Function.createDelegate(this, this.homeNuggetDataError));
		obj.addTimeout(Function.createDelegate(this, this.homeNuggetDataError));
		$R_DQ.addToQueue(obj);
		$R_DQ.processQueue();
		obj = null;
	},
	
	getDataComplete: function(args) {
		this.showNoData(args._result.Count == 0);
		$R_FN.showElement(this._pnlMore, true);
		var result = args._result;
		var aryData, row;
		//open
		this._tblOpen.clearTable();
		var RowColor = '';
		for (var i = 0; i < result.OpenQ.length; i++) {
			row = result.OpenQ[i];
			aryData = [
                $RGT_nubButton_QuoteImp(row.ID, row.No, row.IsImportant),
				//$RGT_nubButton_Quote(row.ID, row.Nonull),
				$RGT_nubButton_Company(row.CMNo, row.CM),
				row.Quote
				];
		    //this._tblOpen.addRow(aryData, null);#cccc00
			RowColor = row.IsImportant == true ? 'yellow-background' : ''
			this._tblOpen.addRowRowColor(aryData, row.ID, false, null, null, row.IsImportant, RowColor);
		}
		$R_FN.showElement(this._pnlOpen, result.OpenQ.length > 0);
		
		//recent
		this._tblRecent.clearTable();
		for (i = 0; i < result.RecentQ.length; i++) {
			row = result.RecentQ[i];
			aryData = [
				$RGT_nubButton_Quote(row.ID, row.No),
				$RGT_nubButton_Company(row.CMNo, row.CM),
				row.Quote
				];
			this._tblRecent.addRow(aryData, null);
		}
		$R_FN.showElement(this._pnlRecent, result.RecentQ.length > 0);
		this.hideLoading();
	}
	
};

Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyOpenQuotes.registerClass("Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyOpenQuotes", Rebound.GlobalTrader.Site.Controls.HomeNuggets.Base, Sys.IDisposable);
