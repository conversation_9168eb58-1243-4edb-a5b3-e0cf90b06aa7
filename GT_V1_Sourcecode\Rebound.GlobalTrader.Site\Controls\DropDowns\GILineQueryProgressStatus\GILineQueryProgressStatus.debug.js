///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//
// RP 06.10.2009:
// - Changes due to changes in base class
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");

Rebound.GlobalTrader.Site.Controls.DropDowns.GILineQueryProgressStatus = function (element) {
    Rebound.GlobalTrader.Site.Controls.DropDowns.GILineQueryProgressStatus.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.DropDowns.GILineQueryProgressStatus.prototype = {
    get_IsPartialGIQueryStatus: function () { return this._IsPartialGIQueryStatus; }, set_IsPartialGIQueryStatus: function (v) { if (this._IsPartialGIQueryStatus !== v) this._IsPartialGIQueryStatus = v; },

    initialize: function () {
        Rebound.GlobalTrader.Site.Controls.DropDowns.GILineQueryProgressStatus.callBaseMethod(this, "initialize");
        this.addSetupDataCall(Function.createDelegate(this, this.setupDataCall));
        this.addGetDataOK(Function.createDelegate(this, this.dataCallOK));
    },

    dispose: function () {
        if (this.isDisposed) return;
        this._IsPartialGIQueryStatus = null;
        Rebound.GlobalTrader.Site.Controls.DropDowns.GILineQueryProgressStatus.callBaseMethod(this, "dispose");
    },

    setupDataCall: function () {
        this._objData.set_PathToData("controls/DropDowns/GILineQueryProgressStatus");
        this._objData.set_DataObject("GILineQueryProgressStatus");
        this._objData.set_DataAction("GetData");
    },

    dataCallOK: function () {
        var result = this._objData._result;
        if (result != null) {
            if (result.Types) {
                for (var i = 0; i < result.Types.length; i++) {
                    this.addOption(result.Types[i].Name, result.Types[i].ID);
                }
            }
        }

    }

};

Rebound.GlobalTrader.Site.Controls.DropDowns.GILineQueryProgressStatus.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.GILineQueryProgressStatus", Rebound.GlobalTrader.Site.Controls.DropDowns.Base);
