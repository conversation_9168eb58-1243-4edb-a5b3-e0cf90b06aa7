//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Resources {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option or rebuild the Visual Studio project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.VisualStudio.Web.Application.StronglyTypedResourceProxyBuilder", "16.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class MultiStepTitles {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal MultiStepTitles() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Resources.MultiStepTitles", global::System.Reflection.Assembly.Load("App_GlobalResources"));
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter Detail.
        /// </summary>
        internal static string CountryAdd_EnterDetail {
            get {
                return ResourceManager.GetString("CountryAdd_EnterDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select Source.
        /// </summary>
        internal static string CountryAdd_SelectSource {
            get {
                return ResourceManager.GetString("CountryAdd_SelectSource", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter Details.
        /// </summary>
        internal static string CreditAdd_EnterDetail {
            get {
                return ResourceManager.GetString("CreditAdd_EnterDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Notify.
        /// </summary>
        internal static string CreditAdd_Notify {
            get {
                return ResourceManager.GetString("CreditAdd_Notify", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select Customer RMA.
        /// </summary>
        internal static string CreditAdd_SelectCRMA {
            get {
                return ResourceManager.GetString("CreditAdd_SelectCRMA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select Invoice.
        /// </summary>
        internal static string CreditAdd_SelectInvoice {
            get {
                return ResourceManager.GetString("CreditAdd_SelectInvoice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select Item.
        /// </summary>
        internal static string CreditAdd_SelectItem {
            get {
                return ResourceManager.GetString("CreditAdd_SelectItem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select Source.
        /// </summary>
        internal static string CreditAdd_SelectSource {
            get {
                return ResourceManager.GetString("CreditAdd_SelectSource", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Details.
        /// </summary>
        internal static string CreditLine_Add_EditDetails {
            get {
                return ResourceManager.GetString("CreditLine_Add_EditDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select Item.
        /// </summary>
        internal static string CreditLine_Add_SelectItem {
            get {
                return ResourceManager.GetString("CreditLine_Add_SelectItem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select Source.
        /// </summary>
        internal static string CreditLine_Add_SelectSource {
            get {
                return ResourceManager.GetString("CreditLine_Add_SelectSource", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter Details.
        /// </summary>
        internal static string CRMAAdd_EnterDetail {
            get {
                return ResourceManager.GetString("CRMAAdd_EnterDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Notify.
        /// </summary>
        internal static string CRMAAdd_Notify {
            get {
                return ResourceManager.GetString("CRMAAdd_Notify", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select Invoice.
        /// </summary>
        internal static string CRMAAdd_SelectInvoice {
            get {
                return ResourceManager.GetString("CRMAAdd_SelectInvoice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Details.
        /// </summary>
        internal static string CRMALines_Add_EnterDetail {
            get {
                return ResourceManager.GetString("CRMALines_Add_EnterDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select Invoice Line.
        /// </summary>
        internal static string CRMALines_Add_SelectItem {
            get {
                return ResourceManager.GetString("CRMALines_Add_SelectItem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter Details.
        /// </summary>
        internal static string CRMAReceivingLines_Receive_Detail {
            get {
                return ResourceManager.GetString("CRMAReceivingLines_Receive_Detail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Header Details.
        /// </summary>
        internal static string CRMAReceivingLines_Receive_Header {
            get {
                return ResourceManager.GetString("CRMAReceivingLines_Receive_Header", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Notify.
        /// </summary>
        internal static string CRMAReceivingLines_Receive_Notify {
            get {
                return ResourceManager.GetString("CRMAReceivingLines_Receive_Notify", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select Item.
        /// </summary>
        internal static string CRMAReceivingLines_Receive_SelectItem {
            get {
                return ResourceManager.GetString("CRMAReceivingLines_Receive_SelectItem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select Source.
        /// </summary>
        internal static string CRMAReceivingLines_Receive_Source {
            get {
                return ResourceManager.GetString("CRMAReceivingLines_Receive_Source", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter Details.
        /// </summary>
        internal static string CurrencyAdd_EnterDetail {
            get {
                return ResourceManager.GetString("CurrencyAdd_EnterDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select Source.
        /// </summary>
        internal static string CurrencyAdd_SelectSource {
            get {
                return ResourceManager.GetString("CurrencyAdd_SelectSource", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter Details.
        /// </summary>
        internal static string CusReqAdd_EnterDetail {
            get {
                return ResourceManager.GetString("CusReqAdd_EnterDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter Details Step 1.
        /// </summary>
        internal static string CusReqAdd_EnterDetail1 {
            get {
                return ResourceManager.GetString("CusReqAdd_EnterDetail1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter Details Step 2.
        /// </summary>
        internal static string CusReqAdd_EnterDetail2 {
            get {
                return ResourceManager.GetString("CusReqAdd_EnterDetail2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Notify.
        /// </summary>
        internal static string CusReqAdd_Notify {
            get {
                return ResourceManager.GetString("CusReqAdd_Notify", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select Company.
        /// </summary>
        internal static string CusReqAdd_SelectCompany {
            get {
                return ResourceManager.GetString("CusReqAdd_SelectCompany", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter Details.
        /// </summary>
        internal static string DebitAdd_EnterDetail {
            get {
                return ResourceManager.GetString("DebitAdd_EnterDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Notify.
        /// </summary>
        internal static string DebitAdd_Notify {
            get {
                return ResourceManager.GetString("DebitAdd_Notify", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select Purchase Order.
        /// </summary>
        internal static string DebitAdd_SelectPO {
            get {
                return ResourceManager.GetString("DebitAdd_SelectPO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Details.
        /// </summary>
        internal static string DebitLine_Add_EditDetails {
            get {
                return ResourceManager.GetString("DebitLine_Add_EditDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select Item.
        /// </summary>
        internal static string DebitLine_Add_SelectItem {
            get {
                return ResourceManager.GetString("DebitLine_Add_SelectItem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select Source.
        /// </summary>
        internal static string DebitLine_Add_SelectSource {
            get {
                return ResourceManager.GetString("DebitLine_Add_SelectSource", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select Booking Period.
        /// </summary>
        internal static string EnhanceInspection_Add_BookingPeriod {
            get {
                return ResourceManager.GetString("EnhanceInspection_Add_BookingPeriod", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select Close Date.
        /// </summary>
        internal static string EnhanceInspection_Add_EnterDetail {
            get {
                return ResourceManager.GetString("EnhanceInspection_Add_EnterDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter Details .
        /// </summary>
        internal static string GIAdd_EnterDetail {
            get {
                return ResourceManager.GetString("GIAdd_EnterDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Notify.
        /// </summary>
        internal static string GIAdd_Notify {
            get {
                return ResourceManager.GetString("GIAdd_Notify", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select Purchase Order.
        /// </summary>
        internal static string GIAdd_SelectPO {
            get {
                return ResourceManager.GetString("GIAdd_SelectPO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Details.
        /// </summary>
        internal static string GILines_Edit {
            get {
                return ResourceManager.GetString("GILines_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Notify.
        /// </summary>
        internal static string GILines_Notify {
            get {
                return ResourceManager.GetString("GILines_Notify", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter Details .
        /// </summary>
        internal static string InvoiceAdd_EnterDetail {
            get {
                return ResourceManager.GetString("InvoiceAdd_EnterDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Notify.
        /// </summary>
        internal static string InvoiceAdd_Notify {
            get {
                return ResourceManager.GetString("InvoiceAdd_Notify", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select Sales Order.
        /// </summary>
        internal static string InvoiceAdd_SelectSO {
            get {
                return ResourceManager.GetString("InvoiceAdd_SelectSO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select Closed SO Line.
        /// </summary>
        internal static string InvoiceLines_Add_CloseSO {
            get {
                return ResourceManager.GetString("InvoiceLines_Add_CloseSO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Confirm Details.
        /// </summary>
        internal static string InvoiceLines_Add_EnterDetail {
            get {
                return ResourceManager.GetString("InvoiceLines_Add_EnterDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select Service Line.
        /// </summary>
        internal static string InvoiceLines_Add_SelectItem {
            get {
                return ResourceManager.GetString("InvoiceLines_Add_SelectItem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select Source.
        /// </summary>
        internal static string InvoiceLines_Add_SelectSource {
            get {
                return ResourceManager.GetString("InvoiceLines_Add_SelectSource", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Notify.
        /// </summary>
        internal static string Notify {
            get {
                return ResourceManager.GetString("Notify", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter Details.
        /// </summary>
        internal static string POAdd_EnterDetail {
            get {
                return ResourceManager.GetString("POAdd_EnterDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Notify.
        /// </summary>
        internal static string POAdd_Notify {
            get {
                return ResourceManager.GetString("POAdd_Notify", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select Company.
        /// </summary>
        internal static string POAdd_SelectCompany {
            get {
                return ResourceManager.GetString("POAdd_SelectCompany", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Details.
        /// </summary>
        internal static string POLine_Add_EditDetails {
            get {
                return ResourceManager.GetString("POLine_Add_EditDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select Item.
        /// </summary>
        internal static string POLine_Add_SelectItem {
            get {
                return ResourceManager.GetString("POLine_Add_SelectItem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select Source.
        /// </summary>
        internal static string POLine_Add_SelectSource {
            get {
                return ResourceManager.GetString("POLine_Add_SelectSource", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter Line Details.
        /// </summary>
        internal static string POReceivingLines_Receive_Detail {
            get {
                return ResourceManager.GetString("POReceivingLines_Receive_Detail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Header.
        /// </summary>
        internal static string POReceivingLines_Receive_Header {
            get {
                return ResourceManager.GetString("POReceivingLines_Receive_Header", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select New or Existing Header.
        /// </summary>
        internal static string POReceivingLines_Receive_NewOrExisting {
            get {
                return ResourceManager.GetString("POReceivingLines_Receive_NewOrExisting", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Notify.
        /// </summary>
        internal static string POReceivingLines_Receive_Notify {
            get {
                return ResourceManager.GetString("POReceivingLines_Receive_Notify", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select Target.
        /// </summary>
        internal static string POReceivingLines_Receive_Target {
            get {
                return ResourceManager.GetString("POReceivingLines_Receive_Target", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter Details.
        /// </summary>
        internal static string QuoteAdd_EnterDetail {
            get {
                return ResourceManager.GetString("QuoteAdd_EnterDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Notify.
        /// </summary>
        internal static string QuoteAdd_Notify {
            get {
                return ResourceManager.GetString("QuoteAdd_Notify", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select Item.
        /// </summary>
        internal static string QuoteAdd_SelectItem {
            get {
                return ResourceManager.GetString("QuoteAdd_SelectItem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select Source.
        /// </summary>
        internal static string QuoteAdd_SelectSource {
            get {
                return ResourceManager.GetString("QuoteAdd_SelectSource", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter Details.
        /// </summary>
        internal static string QuoteLine_Add_EditDetails {
            get {
                return ResourceManager.GetString("QuoteLine_Add_EditDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lot Details.
        /// </summary>
        internal static string QuoteLine_Add_SearchLotDetails {
            get {
                return ResourceManager.GetString("QuoteLine_Add_SearchLotDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select Item.
        /// </summary>
        internal static string QuoteLine_Add_SelectItem {
            get {
                return ResourceManager.GetString("QuoteLine_Add_SelectItem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select Source.
        /// </summary>
        internal static string QuoteLine_Add_SelectSource {
            get {
                return ResourceManager.GetString("QuoteLine_Add_SelectSource", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter Details.
        /// </summary>
        internal static string SATrade_EnterDetail {
            get {
                return ResourceManager.GetString("SATrade_EnterDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Notify.
        /// </summary>
        internal static string SATrade_Notify {
            get {
                return ResourceManager.GetString("SATrade_Notify", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter Details.
        /// </summary>
        internal static string SOAdd_EnterDetail {
            get {
                return ResourceManager.GetString("SOAdd_EnterDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Notify.
        /// </summary>
        internal static string SOAdd_Notify {
            get {
                return ResourceManager.GetString("SOAdd_Notify", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select Company.
        /// </summary>
        internal static string SOAdd_SelectCompany {
            get {
                return ResourceManager.GetString("SOAdd_SelectCompany", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lot Details.
        /// </summary>
        internal static string SOeLine_Add_SearchLotDetails {
            get {
                return ResourceManager.GetString("SOeLine_Add_SearchLotDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Details.
        /// </summary>
        internal static string SOLine_Add_EditDetails {
            get {
                return ResourceManager.GetString("SOLine_Add_EditDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select Item.
        /// </summary>
        internal static string SOLine_Add_SelectItem {
            get {
                return ResourceManager.GetString("SOLine_Add_SelectItem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select Source.
        /// </summary>
        internal static string SOLine_Add_SelectSource {
            get {
                return ResourceManager.GetString("SOLine_Add_SelectSource", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select Stock Item.
        /// </summary>
        internal static string SOLine_Allocate_SelectStock {
            get {
                return ResourceManager.GetString("SOLine_Allocate_SelectStock", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Details.
        /// </summary>
        internal static string SOLine_Alocate_EditDetails {
            get {
                return ResourceManager.GetString("SOLine_Alocate_EditDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter Line Details.
        /// </summary>
        internal static string SOShippingLines_Ship_Detail {
            get {
                return ResourceManager.GetString("SOShippingLines_Ship_Detail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Header Details.
        /// </summary>
        internal static string SOShippingLines_Ship_Header {
            get {
                return ResourceManager.GetString("SOShippingLines_Ship_Header", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Notify.
        /// </summary>
        internal static string SOShippingLines_Ship_Notify {
            get {
                return ResourceManager.GetString("SOShippingLines_Ship_Notify", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select Target.
        /// </summary>
        internal static string SOShippingLines_Ship_Target {
            get {
                return ResourceManager.GetString("SOShippingLines_Ship_Target", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Confirm.
        /// </summary>
        internal static string Sourcing_AddToReq_Confirm {
            get {
                return ResourceManager.GetString("Sourcing_AddToReq_Confirm", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  Customer Requirement.
        /// </summary>
        internal static string Sourcing_AddToReq_IPOBOMConfirm {
            get {
                return ResourceManager.GetString("Sourcing_AddToReq_IPOBOMConfirm", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select Customer Requirement.
        /// </summary>
        internal static string Sourcing_AddToReq_SelectCusReq {
            get {
                return ResourceManager.GetString("Sourcing_AddToReq_SelectCusReq", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter Details.
        /// </summary>
        internal static string SRMAAdd_EnterDetail {
            get {
                return ResourceManager.GetString("SRMAAdd_EnterDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Notify.
        /// </summary>
        internal static string SRMAAdd_Notify {
            get {
                return ResourceManager.GetString("SRMAAdd_Notify", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select Purchase Order.
        /// </summary>
        internal static string SRMAAdd_SelectPO {
            get {
                return ResourceManager.GetString("SRMAAdd_SelectPO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Details.
        /// </summary>
        internal static string SRMALines_Add_EnterDetail {
            get {
                return ResourceManager.GetString("SRMALines_Add_EnterDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select Purchase Order Line.
        /// </summary>
        internal static string SRMALines_Add_SelectItem {
            get {
                return ResourceManager.GetString("SRMALines_Add_SelectItem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Details.
        /// </summary>
        internal static string SRMALine_Allocate_EditDetails {
            get {
                return ResourceManager.GetString("SRMALine_Allocate_EditDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select Stock.
        /// </summary>
        internal static string SRMALine_Allocate_SelectStock {
            get {
                return ResourceManager.GetString("SRMALine_Allocate_SelectStock", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter Line Details.
        /// </summary>
        internal static string SRMAShippingLines_Ship_Detail {
            get {
                return ResourceManager.GetString("SRMAShippingLines_Ship_Detail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Header Details.
        /// </summary>
        internal static string SRMAShippingLines_Ship_Header {
            get {
                return ResourceManager.GetString("SRMAShippingLines_Ship_Header", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Notify.
        /// </summary>
        internal static string SRMAShippingLines_Ship_Notify {
            get {
                return ResourceManager.GetString("SRMAShippingLines_Ship_Notify", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select Source.
        /// </summary>
        internal static string SRMAShippingLines_Ship_SelectSource {
            get {
                return ResourceManager.GetString("SRMAShippingLines_Ship_SelectSource", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter Details.
        /// </summary>
        internal static string SupplierInvoiceAdd_EnterDetail {
            get {
                return ResourceManager.GetString("SupplierInvoiceAdd_EnterDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Notify.
        /// </summary>
        internal static string SupplierInvoiceAdd_Notify {
            get {
                return ResourceManager.GetString("SupplierInvoiceAdd_Notify", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select Company.
        /// </summary>
        internal static string SupplierInvoiceAdd_SelectCompany {
            get {
                return ResourceManager.GetString("SupplierInvoiceAdd_SelectCompany", resourceCulture);
            }
        }
    }
}
