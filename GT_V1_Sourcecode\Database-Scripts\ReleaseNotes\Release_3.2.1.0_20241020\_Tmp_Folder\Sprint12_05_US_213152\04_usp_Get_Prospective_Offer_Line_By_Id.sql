﻿SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
/*
===========================================================================================  
TASK			UPDATED BY			DATE			ACTION		DESCRIPTION  
[US-213152]		Trung Pham Van		04-Oct-2024		CREATE		Get Prospective Offer Line By Id
===========================================================================================  
*/
CREATE OR ALTER PROCEDURE usp_Get_Prospective_Offer_Line_By_Id
	@LineId INT = NULL
AS
BEGIN
	SELECT l.Part, l.<PERSON><PERSON><PERSON>, l.<PERSON><PERSON><PERSON>, l.<PERSON><PERSON>, l.PackageId,
		l.Quant<PERSON>, l.<PERSON>, l.<PERSON><PERSON>, p.SupplierNo, l.ROHS, l.ROHSStatus
	FROM tbProspectiveOfferLines l
	INNER JOIN tbProspectiveOffers p ON p.ProspectiveOfferId = l.ProspectiveOfferNo
	WHERE ProspectiveOfferLineId = @LineId
END
GO