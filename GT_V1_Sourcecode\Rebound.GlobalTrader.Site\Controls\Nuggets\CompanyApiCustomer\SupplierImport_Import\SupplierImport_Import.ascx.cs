using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site;
using Rebound.GlobalTrader.Site.Controls;

namespace Rebound.GlobalTrader.Site.Controls.Forms {
    public partial class SupplierImport_Import : Base
    {

		#region Locals
        protected IconButton _ibtnSend;
        protected IconButton _ibtnSend_Footer;
		#endregion

		#region Properties

		#endregion

		#region Overrides

		/// <summary>
		/// OnInit
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
			base.OnInit(e);
            //TitleText = Functions.GetGlobalResource("FormTitles", "SupplierImport_Import");
            AddScriptReference("Controls.Nuggets.CompanyApiCustomer.SupplierImport_Import.SupplierImport_Import.js");
		}

		/// <summary>
		/// OnPreRender
		/// </summary>
		/// <param name="e"></param>
		protected override void OnPreRender(EventArgs e) {
			WireUpButtons();
			SetupScriptDescriptors();
			base.OnPreRender(e);
		}

		#endregion

		private void WireUpButtons() {
            //_ibtnSend = FindIconButton("ibtnSend");
            //_ibtnSend_Footer = (IconButton)FindFooterIconButton("ibtnSend");
		}

		/// <summary>
		/// Setup Script Descriptors
		/// </summary>
		private void SetupScriptDescriptors() {
            _scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.Forms.SupplierImport_Import", ctlDesignBase.ClientID);
			//_scScriptControlDescriptor.AddProperty("intNPRID", _objQSManager.NPRID);
            //_scScriptControlDescriptor.AddElementProperty("ibtnSend", _ibtnSend.ClientID);
            //if (FooterLinksHolder != null) _scScriptControlDescriptor.AddElementProperty("ibtnSend_Footer", _ibtnSend_Footer.ClientID);

            //string strOut = string.Empty; 
            //BLL.ReportNPR npr = BLL.ReportNPR.Get(_objQSManager.NPRID,SessionManager.ClientID);
            //if (npr != null)
            //{
            //    strOut = MailTemplateManager.GetMessage_NotifyNPR(npr);
            //    _scScriptControlDescriptor.AddProperty("strMessageText", strOut);
            //    _scScriptControlDescriptor.AddProperty("strNPRNo", npr.NPRNo);
            //    _scScriptControlDescriptor.AddProperty("intGoodsIn", npr.GoodsInNo);
            //    _scScriptControlDescriptor.AddProperty("intBuyerId", npr.BuyerId);
            //    _scScriptControlDescriptor.AddProperty("strBuyerName", npr.BuyerName);
            //    _scScriptControlDescriptor.AddProperty("intGoodsInLineId", npr.GoodsInLineId);
            //    _scScriptControlDescriptor.AddProperty("intClientNo", npr.ClientNo);
            //}
            //else
            //{
            //    HttpContext.Current.Response.Redirect(_objSite.GetPage("NotFound").Url, true);
            //}
           
		}

	}
}
