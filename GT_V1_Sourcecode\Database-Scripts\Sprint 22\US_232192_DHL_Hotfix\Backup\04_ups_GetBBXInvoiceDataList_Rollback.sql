﻿
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

/* 
================================================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-210540]		Phuc Hoang			16-Aug-2024		UPDATE			[PROD Bug] DHL Interface - Label Print Issue
================================================================================================================
*/

CREATE OR ALTER PROCEDURE [dbo].[ups_GetBBXInvoiceDataList]                                             
(                 
  @ClientId int,                
  @LoginId int,                
  @ParentId varchar(500)              
)                
As                
 Begin                
   select ts.*,(
   select upsCt.CountryCode 
   from dbo.UPSCountryList upsCt
   JOIN tbGlobalCountryList glb on upsCt.GTCountryID = glb.GlobalCountryId
   where upsCt.GTCountryID = ts.CountryOfOrigin --AND glb.GlobalCountryName NOT LIKE '%EMBARGOED DESTINATION%'

   ) as CountryCode, 
   caw.Weight

   from ChildInvoiceAWBDHL caw            
   Left join tbSaveBBXInvoiceLineItems ts on caw.InvoiceNo=ts.InvoiceNo  
   --caw.AWBNo=ts.AWBNo and           
   where caw.SendStatus=1 and caw.ClientId=@ClientId and caw.ParentID=@ParentId and ts.isactive=1              
   --and LoginId=@LoginId                
 End 
 
GO


