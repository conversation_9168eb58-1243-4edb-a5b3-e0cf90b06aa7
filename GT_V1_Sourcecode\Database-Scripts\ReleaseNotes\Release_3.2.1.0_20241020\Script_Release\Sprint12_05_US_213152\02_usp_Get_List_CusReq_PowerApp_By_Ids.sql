﻿SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
/*   
===========================================================================================  
TASK			UPDATED BY			DATE			ACTION		DESCRIPTION  
[US-213152]		Trung Pham Van		03-Oct-2024		CREATE		Get Power App Config by CustomerRequirements Ids
===========================================================================================  
*/
CREATE OR ALTER PROCEDURE [dbo].[usp_Get_List_CusReq_PowerApp_By_Ids]
	@CustomerRequirementIds VARCHAR(MAX) = NULL,
	@FlowName VARCHAR(MAX) = NULL
AS
BEGIN
	DECLARE @PowerPrOUrl VARCHAR(MAX)
	SELECT @PowerPrOUrl = FlowUrl FROM tbPowerApp_urls WHERE FlowName = @FlowName
	SELECT DISTINCT l.EMail, @PowerPrOUrl AS PowerAppUri, co.Salesman
	FROM tbCustomerRequirement cr
	JOIN tbBOM bom ON cr.BomNo = bom.BOMId
	JOIN tbCompany co ON co.CompanyId = bom.CompanyNo
	JOIN tbLogin l ON l.LoginId = co.Salesman
	WHERE cr.CustomerRequirementId IN (SELECT CAST(value AS INT) FROM STRING_SPLIT(@CustomerRequirementIds, ','))
END