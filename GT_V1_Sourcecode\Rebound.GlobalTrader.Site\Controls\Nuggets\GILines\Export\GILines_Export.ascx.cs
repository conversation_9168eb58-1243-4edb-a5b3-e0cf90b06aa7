using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site;
using Rebound.GlobalTrader.Site.Controls;

namespace Rebound.GlobalTrader.Site.Controls.Forms {
	public partial class GILines_Export : Base {

        #region Locals

        protected Panel _pnlImages;
        #endregion

        #region Properties
        #endregion

        #region Overrides

        /// <summary>
        /// Oninit
        /// </summary>
        /// <param name="e"></param>
        protected override void OnInit(EventArgs e)
        {
            base.OnInit(e);
            WireUpControls();
            AddScriptReference("Controls.Nuggets.GILines.Export.GILines_Export.js");
            TitleText = Functions.GetGlobalResource("FormTitles", "GILines_Export");
            
        }

        protected override void OnPreRender(EventArgs e)
        {
            //_ibtnAdd.Visible = _blnCanAdd;
            SetupScriptDescriptors();
            base.OnPreRender(e);
        }

        #endregion

        /// <summary>
        /// Sets up AJAX script descriptors
        /// </summary>
        private void SetupScriptDescriptors()
        {
            _scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.Forms.GILines_Export", ctlDesignBase.ClientID);
            _scScriptControlDescriptor.AddElementProperty("pnlImages", _pnlImages.ClientID);
            //_scScriptControlDescriptor.AddProperty("strThumbnail", Functions.GetGlobalResource("Misc", "Thumbnail"));       // [001]
            _scScriptControlDescriptor.AddProperty("strMedium", Functions.GetGlobalResource("Misc", "Medium"));
            _scScriptControlDescriptor.AddProperty("strLarge", Functions.GetGlobalResource("Misc", "Large"));                                      // [001]
            
        }

        /// <summary>
        /// Wire up controls from the ascx
        /// </summary>
        private void WireUpControls()
        {
            _pnlImages = (Panel)FindContentControl("pnlImages");
        }

    }
}