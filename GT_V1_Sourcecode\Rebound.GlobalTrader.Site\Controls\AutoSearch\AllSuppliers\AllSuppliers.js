Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.AutoSearch");Rebound.GlobalTrader.Site.Controls.AutoSearch.AllSuppliers=function(n){Rebound.GlobalTrader.Site.Controls.AutoSearch.AllSuppliers.initializeBase(this,[n]);this._blnUseSupplierNo=!1};Rebound.GlobalTrader.Site.Controls.AutoSearch.AllSuppliers.prototype={get_blnUseSupplierNo:function(){return this._blnUseSupplierNo},set_blnUseSupplierNo:function(n){this._blnUseSupplierNo!==n&&(this._blnUseSupplierNo=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.AutoSearch.AllSuppliers.callBaseMethod(this,"initialize");this.addDataReturnedEvent(Function.createDelegate(this,this.dataReturned));this.setupDataObject("AllSuppliers")},dispose:function(){this.isDisposed||(this._blnUseSupplierNo=null,Rebound.GlobalTrader.Site.Controls.AutoSearch.AllSuppliers.callBaseMethod(this,"dispose"))},dataReturned:function(){var t,r,n,i;if(this._result&&this._result.TotalRecords>0)for(t=0,r=this._result.Results.length;t<r;t++)n=this._result.Results[t],i="",i=this._enmResultsActionType==$R_ENUM$AutoSearchResultsActionType.Navigate?$RGT_nubButton_Company(n.ID,n.Name):n.Name,this.addResultItem(i,n.Name,this._blnUseSupplierNo?n.SupNo:n.ID,n.Email),i=null,n=null}};Rebound.GlobalTrader.Site.Controls.AutoSearch.AllSuppliers.registerClass("Rebound.GlobalTrader.Site.Controls.AutoSearch.AllSuppliers",Rebound.GlobalTrader.Site.Controls.AutoSearch.Base,Sys.IDisposable);