///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Pages.Setup");

Rebound.GlobalTrader.Site.Pages.Setup.CD_GlobalTax = function(el) { 
	Rebound.GlobalTrader.Site.Pages.Setup.CD_GlobalTax.initializeBase(this, [el]);
};

Rebound.GlobalTrader.Site.Pages.Setup.CD_GlobalTax.prototype = {

	get_ctlRates: function() { return this._ctlRates; }, 	set_ctlRates: function(v) { if (this._ctlRates !== v)  this._ctlRates = v; }, 
	get_ctlTax: function() { return this._ctlTax; }, 	set_ctlTax: function(v) { if (this._ctlTax !== v)  this._ctlTax = v; }, 

	initialize: function() {
		Rebound.GlobalTrader.Site.Pages.Setup.CD_GlobalTax.callBaseMethod(this, "initialize");
	},
	
	goInit: function() {
		if (this._ctlTax) this._ctlTax.addSelectTax(Function.createDelegate(this, this.ctlTax_SelectTax));
		Rebound.GlobalTrader.Site.Pages.Setup.CD_GlobalTax.callBaseMethod(this, "goInit");
	},

	dispose: function() { 
		if (this.isDisposed) return;
		if (this._ctlRates) this._ctlRates.dispose();
		if (this._ctlTax) this._ctlTax.dispose();
		this._ctlRates = null;
		this._ctlTax = null;
		Rebound.GlobalTrader.Site.Pages.Setup.CD_GlobalTax.callBaseMethod(this, "dispose");
	},
	
	ctlTax_SelectTax: function() {
		this._ctlRates._intTaxID = this._ctlTax._intTaxID;
		this._ctlRates.show(true);
		this._ctlTax._tbl.resizeColumns();
		this._ctlRates.refresh();
	}
	
};

Rebound.GlobalTrader.Site.Pages.Setup.CD_GlobalTax.registerClass("Rebound.GlobalTrader.Site.Pages.Setup.CD_GlobalTax", Rebound.GlobalTrader.Site.Pages.Content, Sys.IDisposable);
