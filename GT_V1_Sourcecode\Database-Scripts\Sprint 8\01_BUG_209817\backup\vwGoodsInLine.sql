CREATE VIEW [dbo].[vwGoodsInLine]
/*****************************************************************************************                                                                                                                                                                    
  
  
  
  
         
*Marker     Changed by      Date          Remarks                                                                                                                                                                                 
*[006]      Abhinav         10/01/2021   Add new clientCOmpanyType Column                              
*[005]      Bhooma          17/01/2022   Get QueryBakeLevel                     
*[006]      Abhinav <PERSON>  09/02/2022   make changes for correct HIC status.               
*[007]      <PERSON>    21-03-2023   adding two new columns HasBarcodeScan and BarcodeScanRemarks            
*[008]      Abhinav <PERSON>a  09-05-2023   adding 5 new columns for query text.      
*[009]      Abhinav <PERSON>  14-07-2023   Re-Compile Re-Open Process for RP-1944.  
*[010]      <PERSON>    22/09/2023   RP-2339 AS6081 GT Documents - Show AS6081 on detail screens  
*/
AS
SELECT distinct
    gil.GoodsInLineId,
    gil.GoodsInNo,
    ISNULL(gil.PurchaseOrderLineNo, pol.PurchaseOrderLineId) AS PurchaseOrderLineNo,
    gil.FullPart,
    gil.Part,
    gil.ManufacturerNo,
    gil.DateCode,
    gil.PackageNo,
    gil.Quantity,
    gil.Price,
    gil.ShipInCost,
    gil.QualityControlNotes,
    gil.Location,
    gil.ProductNo,
    gil.LandedCost,
    gil.CustomerRMALineNo,
    gil.SupplierPart,
    gil.ROHS,
    gil.CountryOfManufacture,
    gil.InspectedBy,
    gil.DateInspected,
    gil.CountingMethodNo,
    cm.CountingMethodDescription,
    gil.SerialNosRecorded,
    gil.PartMarkings,
    gil.Unavailable,
    gil.LotNo,
    ISNULL(gil.Notes, '') AS LineNotes,
    gil.UpdatedBy,
    gil.DLUP,
    gi.GoodsInNumber,
    gi.ClientNo,
    gi.AirWayBill,
    pk.PackageName,
    pk.PackageDescription,
    pr.ProductName,
    pr.ProductDescription,
    pr.DutyCode AS ProductDutyCode,
    mf.ManufacturerName,
    mf.ManufacturerCode,
    gc.GlobalCountryName AS CountryOfManufactureName,
    gi.DateReceived,
    lgi.EmployeeName AS InspectorName,
    ISNULL(lgi.FirstName, '') + ' ' + SUBSTRING(ISNULL(lgi.LastName, ''), 0, 2) AS InspectorNameLabel,
    ISNULL(gi.PurchaseOrderNo, po.PurchaseOrderId) AS PurchaseOrderNo,
    po.PurchaseOrderNumber,
    cln.CustomerRMANo,
    crma.CustomerRMANumber,
    gi.CurrencyNo,
    cu.CurrencyCode,
    cu.CurrencyDescription,
    gi.ReceivedBy,
    lgr.EmployeeName AS ReceiverName,
    lgr.DivisionNo,
    lgr.TeamNo,
    gi.CompanyNo,
    co.CompanyName,
    ctyp.[Name] AS CompanyType,
    sk.StockId AS StockNo,
    gi.SupplierInvoice,
    gi.Reference,
    lt.LotName,
    ISNULL(pol.Quantity, 0) AS QuantityOrdered,
    ISNULL(pol.ShipInCost, 0) AS PurchaseOrderLineShipInCost,
    ISNULL(gil.NPRPrinted, 0) AS NPRPrinted,
    ISNULL(gil.Invoiced, 0) AS Invoiced,
    CASE ISNULL(sk.FirstStockProvisionDate, 0)
        WHEN 0 THEN
            0
        ELSE
            1
    END AS blnStockProvision,
    gil.PhysicalInspectedBy,
    gil.DatePhysicalInspected,
    lt.LotCode,
    pol.POSerialNo,
    gil.ClientLandedCost,
    gil.ClientPrice,
    gil.PurchaseOrderLineNo AS GIPurchaseOrderLineNo,
    gil.POBankFee,
    gi.PurchaseOrderNo AS GIPurchaseOrderNo,
    ISNULL(pr.Inactive, 0) AS ProductInactive,
    gil.ReqSerialNo,
    gil.SerialNoCount,
                                    --[004] code Start                                                                        
    gil.MSLLevel,
                                    --[004] code end                                                                        
    ISNULL(pr.IsHazardous, 0) AS IsProdHazardous,
    gil.PrintHazardous,
    gil.ParentGILineNo,
    col.GlobalCountryName AS CountryOfOrigin,
    gil.LifeCycleStage,
    gil.HTSCode,
    gil.AveragePrice,
    gil.Packing,
    gil.PackagingSize,
    col.GlobalCountryName AS IHSCountryOfOrigin,
    gil.Descriptions,
    gil.IHSProduct,
    gil.ECCNCode,
    gil.IsFullQtyRecieved,
    gil.IsPartNoCorrect,
    gil.CorrectPartNo,
    gil.IsManufacturerCorrect,
    gil.CorrectManufacturer,
    gil.IsDateCodeCorrect,
    gil.CorrectDateCode,
    gil.IsDateCodeRequired,
    gil.IsPackageTypeCorrect,
    gil.CorrectPackageType,
    gil.IsMSLLevelCorrect,
    gil.CorrectMSLLevel,
    gil.HICStatus,
    gil.IsHICStatusCorrect,
    gil.CorrectHICStatus,
    hic.HICStatus AS CorrectHICStatusName,
    gil.PKGBreakdownMismatch,
    gil.IsROHSStatusCorrect,
    gil.CorrectROHSStatus,
    gil.IsLotCodesReq,
    gil.BakingLevelAdded,
    gil.EnhancedInspectionReq,
    gil.GeneralInspectionNotes,
    gil.IsInspectionConducted,
    isnull(pr.IsOrderViaIPOonly, 0) as IsOrderViaIPOonly,
    gil.IsSalesNotify,
    gil.IsQualityNotify,
    gil.IsPurchaseNotify,
    gil.SalesQueryReply,
    gil.PurchaseQueryReply,
    gil.QualityQueryReply,
    gil.SalesApprovalStatus,
    gil.PurchaseApprovalStatus,
    gil.QualityApprovalStatus,
    gil.IsPDFReportRequired,
    gil.IsQuarantineProduct,
    gi.GoodsInId,
    gil.IsLotCodesReq as ReqLotNo,
    gil.LotNoCount,
    pol.Part as POPart,
    pol.ManufacturerNo as POManufacturerNo,
    pol.DateCode as PODateCode,
    pol.PackageNo as POPackageNo,
    pol.MSLLevel as POMSLLevel,
    pol.ROHS as POROHS,
    pol.Quantity as POQuantity,
    gil.ActeoneTestStatus,
    gil.IsopropryleStatus,
    gil.ActeoneTest,
    gil.Isopropryle,
    Clntctyp.[Name] AS ClientCompanyType,
    gil.QueryBakeLevel,
    gil.EnhInpectionReqId,
    gil.PrintableDC,
    co.ClientNo AS [SupplierClient],
    clnt.clientName AS [SupplierClientName],
                                    --[007] start              
    gil.HasBarCodeScan,
    gil.BarCodeScanRemarks,
                                    --[007] end            
                                    --[008] Start           
    ISNULL(gil.IsStartInspection, 0) AS IsStartInspection,
    ISNULL(gil.ISCloseInspection, 0) AS ISCloseInspection,
    ISNULL(gil.PartNoQuery, '') AS PartNoQuery,
    ISNULL(gil.ManufacturerQuery, '') AS ManufacturerQuery,
    ISNULL(gil.PackagingTypeQuery, '') AS PackagingTypeQuery,
    ISNULL(gil.MslQuery, '') AS MslQuery,
    ISNULL(gil.RohsQuery, '') AS RohsQuery,
    CASE
        WHEN gil.InspectionStatus = 0 THEN
            ''
        WHEN gil.InspectionStatus = 1 THEN
            'Inspection in Progress'
        WHEN gil.InspectionStatus = 2 THEN
            'Parts inspection completed'
    END AS [Status],
    ISNULL(gil.ISReaiseGeneralQuery, 0) AS ISReaiseGeneralQuery,
                                    --[008] End     
    CAST(ISNULL(gil.ISReOpenInspection, 0) AS BIT) AS ISReOpenInspection,
    ISNULL(gil.AS6081, 0) AS AS6081 -- [010]  
FROM dbo.tbGoodsInLine AS gil
    INNER JOIN dbo.tbGoodsIn AS gi
        ON gil.GoodsInNo = gi.GoodsInId
    LEFT OUTER JOIN dbo.tbProduct AS pr
        ON gil.ProductNo = pr.ProductId
    LEFT OUTER JOIN dbo.tbPackage AS pk
        ON gil.PackageNo = pk.PackageId
    LEFT OUTER JOIN dbo.tbManufacturer AS mf
        ON gil.ManufacturerNo = mf.ManufacturerId
    LEFT OUTER JOIN dbo.tbCountingMethod AS cm
        ON gil.CountingMethodNo = cm.CountingMethodId
    LEFT OUTER JOIN dbo.tbGlobalCountryList AS gc
        ON gil.CountryOfManufacture = gc.GlobalCountryId
    LEFT OUTER JOIN dbo.tbCurrency AS cu
        ON gi.CurrencyNo = cu.CurrencyId
    LEFT OUTER JOIN dbo.tbLogin AS lgr
        ON gi.ReceivedBy = lgr.LoginId
    INNER JOIN dbo.tbCompany AS co
        ON gi.CompanyNo = co.CompanyId
    LEFT JOIN dbo.tbCompanyType ctyp
        ON co.TypeNo = ctyp.CompanyTypeId
    LEFT OUTER JOIN dbo.tbLogin AS lgi
        ON gil.InspectedBy = lgi.LoginId
    LEFT OUTER JOIN dbo.tbCustomerRMALine AS cln
        ON gil.CustomerRMALineNo = cln.CustomerRMALineId
    LEFT OUTER JOIN dbo.tbCustomerRMA AS crma
        ON cln.CustomerRMANo = crma.CustomerRMAId
    LEFT OUTER JOIN dbo.tbStock AS sk
        ON gil.GoodsInLineId = sk.GoodsInLineNo
    LEFT OUTER JOIN dbo.tbLot AS lt
        ON gil.LotNo = lt.LotId
    LEFT OUTER JOIN dbo.tbPurchaseOrderLine AS pol
        ON sk.PurchaseOrderLineNo = pol.PurchaseOrderLineId
    LEFT OUTER JOIN dbo.tbPurchaseOrder AS po
        ON pol.PurchaseOrderNo = po.PurchaseOrderId
    LEFT OUTER JOIN dbo.tbGlobalCountryList col
        ON gil.CountryOfOriginNo = col.GlobalCountryId
    LEFT OUTER JOIN dbo.tbCompany AS coclnt
        ON gi.ClientCompanyNo = coclnt.CompanyId
    LEFT JOIN dbo.tbCompanyType Clntctyp
        ON coclnt.TypeNo = Clntctyp.CompanyTypeId
    LEFT OUTER JOIN tbHICStatus hic
        ON gil.CorrectHICStatus = hic.HICId
           AND hic.IsQueryHICStatus = 1
    LEFT OUTER JOIN tbClient clnt
        ON clnt.ClientId = co.ClientNo