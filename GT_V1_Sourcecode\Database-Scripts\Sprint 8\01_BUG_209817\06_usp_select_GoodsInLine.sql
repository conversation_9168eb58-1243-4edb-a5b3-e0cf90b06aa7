ALTER PROCEDURE [dbo].[usp_select_GoodsInLine]
    --**********************************************************************************************                                                                                                                                                             
    --*Marker     Changed by      Date          Remarks                                                                                                                                                                                                          
    --*[010]      Bhooma          03/Feb/2022   get ShortShipmentNos                                    
    --*[011]      Ravi B<PERSON>han   15-Mar-2023   adding a column -- unavailable which is used for GI Quarantine                                  
    --*[012]      <PERSON> B<PERSON>han   23-Mar-2023   adding two new columns (HasBarcodeScan and BarcodeScanRemarks)                             
    --*[013]      Abhinav <PERSON>xena  09-May-2023   adding 5 new columns for query text.                            
    --*[014]      Abhinav <PERSON>xena  11-May-2023   adding security permission.                                
    --*[015]      Ravi Bhushan    12-May-2023   Adding Exported flag from tbInvoice table (RP-616 => Prevent splitting lines in GI after invoice has been set to export)                            
    --[RP-881]   <PERSON>    22-11-2023   New CR by client to show banner on GI Screen        
    --**********************************************************************************************                                                                                                                                                              
    /* 
    ===========================================================================================
    TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
    [BUG-209817]    cuongdx			   24-JUL-2024		 Alter	        Add more release button tooltip
    ===========================================================================================
    */

    @GoodsInLineId INT,
    @LoginId INT = 0,
    @ClientId INT = 0
AS

-----EI Token Check in SO----                                     
--IF((SELECT COUNT(t3.SOLineNo)                    
--FROM tbstock t1                    
--    LEFT OUTER JOIN tbAllocation t2 on t1.StockId=t2.StockNo                    
--    LEFT OUTER JOIN tbEI_BookingDetailsInfo t3 on t2.SalesOrderLineNo=t3.SOLineNo                    
--WHERE t1.GoodsInLineNo=@GoodsInLineId)>0)                                     
--BEGIN                    
--    UPDATE tbGoodsInLine set EnhancedInspectionReq=1,EnhInpectionReqId=1 where GoodsInLineId=@GoodsInLineId                    
--END                    
-----------END--------------                                                
--For Print Label                                                                                                                                         
DECLARE @CUSTOMERPARTNO NVARCHAR(60),
        @CUSTOMERPO NVARCHAR(60)
DECLARE @SALESORDERNO INT
DECLARE @ReleseStockDisbaleReason NVARCHAR(1000) = '';
DECLARE @ReleseStockDisbaleStatus bit;
DECLARE @IsShortShipmentEnable bit;
DECLARE @ShortShipmentId int = null;
DECLARE @GINOTE NVARCHAR(MAX) = '';
DECLARE @SalesApproverIds VARCHAR(MAX);
DECLARE @SalesOrderNumber INT = NULL
SELECT @SalesApproverIds = COALESCE(@SalesApproverIds + '#', '') + CAST(SalesApproverId AS VARCHAR(100))
FROM tbGiLineQueryApprover
where GI_LineNo = @GoodsInLineId

SELECT @GINOTE = RejectionReason
FROM tbnpr
where GoodsInLineNo = @GoodsInLineId
      and Inactive = 0
--SET @IsShortShipmentEnable= (Select top 1 CASE when IsCancel=1 THEN 0  ELSE 1 END from tbShortShipment where GoodsInLineNo=@GoodsInLineId and ISNULL(IsCancel,0)=1 )                                                                                         

SET @ShortShipmentId =
(
    SELECT TOP 1
        ShortShipmentId
    FROM tbShortShipment
    where GoodsInLineNo = @GoodsInLineId
    order by ShortShipmentId desc
)
IF (@ShortShipmentId > 0)
BEGIN
    SET @IsShortShipmentEnable =
    (
        Select top 1
            CASE
                when [Status] = 1 THEN
                    0
                ELSE
                    1
            END
        from tbShortShipment
        where GoodsInLineNo = @GoodsInLineId
        order by ShortShipmentId desc
    )
END
ELSE
BEGIN
    SET @IsShortShipmentEnable = 1
END
--*[005] start                                      
CREATE TABLE #GILineQueryStatus
(
    GILineNo INT,
    SalesApprovalStatus INT,
    QualityApprovalStatus INT,
    PurchasingApprovalStatus INT
)
INSERT INTO #GILineQueryStatus
EXEC usp_Select_All_GILines_QueryApprovals_Status @GoodsInLineId

SELECT @ReleseStockDisbaleReason += CASE
                                        WHEN SalesApprovalStatus = 0 THEN
                                            'Query Sales Approval Pending,'
                                        WHEN SalesApprovalStatus = 2 THEN
                                            'Query Sales Approval Declined,'
                                        WHEN SalesApprovalStatus = 3 THEN
                                            'Query Sales Partial Approved,'
                                        ELSE
                                            ''
                                    END,
       @ReleseStockDisbaleReason += CASE
                                        WHEN QualityApprovalStatus = 0 THEN
                                            'Query Quality Approval Pending,'
                                        WHEN QualityApprovalStatus = 2 THEN
                                            'Query Quality Approval Declined,'
                                        WHEN QualityApprovalStatus = 3 THEN
                                            'Query Quality Partial Approved,'
                                        ELSE
                                            ''
                                    END,
       @ReleseStockDisbaleReason += CASE
                                        WHEN PurchasingApprovalStatus = 0 THEN
                                            'Query Purchasing Approval Pending,'
                                        WHEN PurchasingApprovalStatus = 2 THEN
                                            'Query Purchasing Approval Declined,'
                                        WHEN PurchasingApprovalStatus = 3 THEN
                                            'Query Purchasing Partial Approved,'
                                        ELSE
                                            ''
                                    END,

		---------------------------------------------------------------------------

       --[007] Start  Fetching data for  Enable Disable (Save And Release) button of GI line Edit                                                                                          
       @ReleseStockDisbaleStatus = CASE
                                       WHEN SalesApprovalStatus = 1
                                            AND QualityApprovalStatus = 1
                                            AND PurchasingApprovalStatus = 1 THEN
                                           1
                                       ELSE
                                           0
                                   END
--[007] End                                                                                               
FROM #GILineQueryStatus

-- [209817] cuongdx add more message to show quarantine status
SELECT @ReleseStockDisbaleReason += CASE
                                        WHEN Unavailable = 1 THEN
                                            'This GI line is released due to Quarantine'
                                        ELSE
                                            ''
                                    END
FROM tbGoodsInline WHERE GoodsInLineId = @GoodsInLineId

--*[005] end                                                                    
SET @ReleseStockDisbaleReason
    = @ReleseStockDisbaleReason + ISNULL(
                                  (
                                      select [dbo].[ufn_get_reason_disableGILine](@GoodsInLineId)
                                  ),
                                  ''
                                        )
SELECT TOP 1
    @CUSTOMERPARTNO = CustomerPart,
    @SALESORDERNO = SalesOrderNo,
    @CUSTOMERPO = CustomerPO,
    @SalesOrderNumber = SalesOrderNumber
FROM vwAllocation_Outward
WHERE GoodsInLineNo = @GoodsInLineId
--end                                                                                      
if (ISNULL(@SalesOrderNumber, 0) <= 0)
BEGIN
    SET @SalesOrderNumber =
    (
        select top 1
            tbSalesOrder.SalesOrderNumber
        from tbInvoiceLineAllocation
            join tbSalesOrderLine
                on tbSalesOrderLine.SalesOrderLineId = tbInvoiceLineAllocation.SalesOrderLineNo
            join tbSalesOrder
                on tbSalesOrder.SalesOrderId = tbSalesOrderLine.SalesOrderNo
        where tbInvoiceLineAllocation.GoodsInLineNo = @GoodsInLineId
    )
END
--[001] code start                                                                        
Declare @vNPRIds Varchar(1000)
Declare @vNPRNos Varchar(1000)
Execute usp_select_NPRNos_By_GoodsInLineId @GoodsInLineId,
                                           @vNPRIds Out,
                                           @vNPRNos Out
--[001] code end                   
--[010] code start                                                              
Declare @vShortShipmentIds Varchar(1000)
Declare @vShortShipmentNos Varchar(1000)
Execute usp_select_ShortShipmentNos_By_GoodsInLineId @GoodsInLineId,
                                                     @vShortShipmentIds Out,
                                                     @vShortShipmentNos Out
--[010] code end                                                                                                                           

declare @SupplierInvoiceExists bit

if exists
(
    select SupplierInvoiceLineID
    from tbSupplierInvoiceLine
    where goodsinlineno = @GoodsInLineId
)
    set @SupplierInvoiceExists = 1
else
    set @SupplierInvoiceExists = 0

Declare @Stocksplit bit
declare @StockDate datetime

Select @Stocksplit = IsNull(HasSplit, 0),
       @StockDate = StockDate
From tbStock
where GoodsInLineNo = @GoodsInLineId

declare @TotalChildShipCost float

select @TotalChildShipCost = isnull(sum(ShipInCost), 0)
from tbGoodsInLine b
where b.ParentGILineNo = @GoodsInLineId

DECLARE @SupportTeamMemberNo int = NULL
DECLARE @SalesmanId int = NULL
DECLARE @SalesPersonName VARCHAR(100) = NULL

SELECT top 1
    @SupportTeamMemberNo = SupportTeamMemberNo,
    @SalesmanId = Salesman,
    @SalesPersonName = EmployeeName
from vwAllocation_Outward
where GoodsInLineNo = @GoodsInLineId
DECLARE @IsSendQuery bit = 0
SET @IsSendQuery =
(
    select CASE
               WHEN COUNT(*) > 0 THEN
                   1
               ELSE
                   0
           END
    from tbGI_QueryApprovalsLog
    where GILineNo = @GoodsInLineId
)

--IF EXISTS(SELECT StockLogId From tbStockLog where StockLogTypeNo = 12 AND GoodsInLineNo = @GoodsInLineId)                                                                                                                                                   





--SET @Stocksplit = 1                                                                                                                                                           
--ELSE                                                                                                                            
--   SET @Stocksplit = 0                                                                                                                                   
----  DECLARE @PriceForClient FLOAT                                                                                                                            
----    SELECT                                                                                                                                                    
----@PriceForClient = case when ipl.InternalPurchaseOrderLineId  IS NULL THEN z.Price else ipl.Price end                                                                                                                                                       

----FROM dbo.tbGoodsInLine z                                                                           
----left JOIN tbPurchaseOrderLine pol ON z.PurchaseOrderLineNo = pol.PurchaseOrderLineId                                                                                               
---- left JOIN tbInternalPurchaseOrderLine ipl on pol.PurchaseOrderLineId = ipl.PurchaseOrderLineNo                                                                 
----WHERE z.GoodsInLineId = @GoodsInLineId                                                                                                                                     


--[015] RP-616 (check either the invoice is exported or not)                          

declare @isExported bit = 0;
select @isExported = si.CanbeExported
from tbGoodsInLine gil
    join tbSupplierInvoiceLine sil
        on gil.GoodsInLineId = sil.GoodsInLineNo
    join tbSupplierInvoice si
        on si.supplierInvoiceId = sil.SupplierInvoiceNo
where gil.GoodsInLineId = @GoodsInLineId
--[015] end                          
SELECT v.GoodsInLineId,
       v.GoodsInNo,
       v.PurchaseOrderLineNo,
       v.FullPart,
       v.Part,
       v.ManufacturerNo,
       v.DateCode,
       v.PackageNo,
       v.Quantity,
       v.Price,
       v.ShipInCost,
       v.QualityControlNotes,
       v.Location,
       v.ProductNo,
       v.LandedCost,
       v.CustomerRMALineNo,
       v.SupplierPart,
       v.ROHS,
       v.CountryOfManufacture,
       v.InspectedBy,
       v.DateInspected,
       v.CountingMethodNo,
       v.CountingMethodDescription,
       v.SerialNosRecorded,
       v.PartMarkings,
       v.Unavailable,
       v.LotNo,
       v.LineNotes,
       v.UpdatedBy,
       v.DLUP,
       v.GoodsInNumber,
       v.ClientNo,
       v.AirWayBill,
       v.PackageName,
       v.PackageDescription,
       v.ProductName,
       v.ProductDescription,
       v.ProductDutyCode,
       v.ManufacturerName,
       v.ManufacturerCode,
       v.CountryOfManufactureName,
       v.DateReceived,
       v.InspectorName,
       v.InspectorNameLabel,
       v.PurchaseOrderNo,
       v.PurchaseOrderNumber,
       v.CustomerRMANo,
       v.CustomerRMANumber,
       v.CurrencyNo,
       v.CurrencyCode,
       v.CurrencyDescription,
       v.ReceivedBy,
       v.ReceiverName,
       v.DivisionNo,
       v.TeamNo,
       v.CompanyNo,
       v.CompanyName,
       v.CompanyType,
       v.StockNo,
       v.SupplierInvoice,
       v.Reference,
       v.LotName,
       v.QuantityOrdered,
       v.PurchaseOrderLineShipInCost,
       v.NPRPrinted,
       v.Invoiced,
       v.blnStockProvision,
       v.PhysicalInspectedBy,
       v.DatePhysicalInspected,
       v.LotCode,
       v.POSerialNo,
       ipl.InternalPurchaseOrderNo,
       v.ClientLandedCost,
       v.ClientPrice,
       '' AS ChangedFields,                                                    --dummy to make instance Update work                                                
       cast(0 AS bit) AS UpdateStock,                                          --dummy to make instance Update work                                    
       cast(0 AS bit) AS UpdateShipments,                                      --dummy to make instance Update work                                                                                            
       CASE
       (
           SELECT COUNT(GoodsInLineNo)
           FROM vwAllocation_Outward
           WHERE GoodsInLineNo = @GoodsInLineId
       )
           WHEN 0 THEN
               CONVERT(BIT, 0)
           ELSE
               CONVERT(BIT, 1)
       END AS HasAllocationOutward,
       @CUSTOMERPARTNO AS CustomerPartNo,
       @SALESORDERNO as SalesOrderNo,
                                                                               --[001] code start                      
       @vNPRIds as 'NPRIds',
       @vNPRNos as 'NPRNos',
                                                                               --[001] code end                                                                                                                                               
       @SupplierInvoiceExists as 'SupplierInvoiceExists',
       @Stocksplit as 'Stocksplit',
       ipo.CurrencyNo as ClientCurrencyNo,
       ipo.ClientNo as IPOClientNo,
       cu.CurrencyCode as ClientCurrencyCode,
       v.POBankFee,
       @CUSTOMERPO AS CustomerPO,
       @StockDate as StockDate,
       v.ProductInactive,
       dbo.ufn_get_productdutyrate(v.ProductNo, getdate()) as ProductDutyRate, --- to get Rate %  on form Sales Order                                                                                       
       v.ReqSerialNo,
       v.SerialNoCount,
                                                                               --[002] code Start                                         
       v.MSLLevel,
                                                                               --[002] code end                                                
       v.IsProdHazardous,
       v.PrintHazardous,
       v.ParentGILineNo,
       v.CountryOfOrigin,
       v.LifeCycleStage,
       v.HTSCode,
       v.AveragePrice,
       v.Packing,
       v.PackagingSize,
       v.IHSCountryOfOrigin,
       v.Descriptions,
       v.IHSProduct,
       v.ECCNCode,
       (isnull(v.ShipInCost, 0) + @TotalChildShipCost) as TotalShipInCost,
       ISNULL(v.IsFullQtyRecieved, 0) as IsFullQtyRecieved,
       ISNULL(v.IsPartNoCorrect, 0) AS IsPartNoCorrect,
       v.CorrectPartNo,
       ISNULL(v.IsManufacturerCorrect, 0) AS IsManufacturerCorrect,
       v.CorrectManufacturer,
       mf.ManufacturerName as CorrectManufacturerName,
       ISNULL(v.IsDateCodeCorrect, 0) AS IsDateCodeCorrect,
       v.CorrectDateCode,
       ISNULL(v.IsDateCodeRequired, 0) AS IsDateCodeRequired,
       ISNULL(v.IsPackageTypeCorrect, 0) AS IsPackageTypeCorrect,
       v.CorrectPackageType,
       pk.PackageName as CorrectPackageName,
       ISNULL(v.IsMSLLevelCorrect, 0) AS IsMSLLevelCorrect,
       v.CorrectMSLLevel,
       v.HICStatus,
       ISNULL(v.IsHICStatusCorrect, 0) AS IsHICStatusCorrect,
       v.CorrectHICStatus,
       v.PKGBreakdownMismatch,
       ISNULL(v.IsROHSStatusCorrect, 0) AS IsROHSStatusCorrect,
       v.CorrectROHSStatus,
       ISNULL(v.IsLotCodesReq, 0) AS IsLotCodesReq,
       ISNULL(v.BakingLevelAdded, -1) as BakingLevelAdded,
       ISNULL(v.EnhancedInspectionReq, 0) AS EnhancedInspectionReq,
                                                                               --, Case WHEN v.EnhancedInspectionReq IS NULL AND (v.CompanyType like '%PRIOR APPROVAL%') THEN CAST(1 as BIT) ELSE ISNULL(v.EnhancedInspectionReq, 0) END as EnhancedInspectionReq                                                                   
                                                                               --,v.GeneralInspectionNotes                                                          
       (v.GeneralInspectionNotes) AS GeneralInspectionNotes,
       ISNULL(v.IsInspectionConducted, 0) AS IsInspectionConducted,
       @SalesmanId as SalesPersonId,
       @SalesPersonName as SalesPersonName,
                                                                               --[004] Code start                     
       v.IsOrderViaIPOonly,
       v.ReqLotNo,
       v.LotNoCount,
       @SupportTeamMemberNo as SupportTeamMemberNo,
       v.POPart,
       v.POManufacturerNo,
       mf1.ManufacturerName as POManufacturerName,
                                                                               --v.PODateCode,                                                
       v.DateCode AS PODateCode,
       v.POPackageNo,
       pk1.PackageDescription as POPackageType,
       v.POMSLLevel,
       CAST(v.POROHS as int) as POROHS,
       ROHSSts.[Description] as POROHSStatus,
       v.POQuantity,
                                                                               --[004] Code end                                                                             
                                                                               --[005] Code Start                                                                                                         
       gqs.SalesApprovalStatus AS SalesApprovalStatus,
       gqs.QualityApprovalStatus AS QualityApprovalStatus,
       gqs.PurchasingApprovalStatus AS PurchasingApprovalStatus,
       @ReleseStockDisbaleReason as ReleseStockDisbaleReason,
       ISNULL(v.ActeoneTestStatus, -1) as ActeoneTestStatus,
       ISNULL(v.IsopropryleStatus, -1) as IsopropryleStatus,
       v.ActeoneTest,
       v.Isopropryle,
       hics.HICStatus as HICStatusName,
       @ReleseStockDisbaleStatus as ReleseStockDisbaleStatus,
       @IsShortShipmentEnable as IsShortShipmentEnable,
       @ShortShipmentId AS ShortShipmentId,
       CompanyType as ClientCompanyType,
       'Receipt, linked stock and shipment' AS UpdateType,
       v.QueryBakeLevel as QueryBakingLevel,
       v.EnhInpectionReqId,
                                                                               --[005] Code end                                                                        
                                                                               --[005] code start                                                                                                                          
       @vShortShipmentIds as 'ShortShipmentIds',
       @vShortShipmentNos as 'ShortShipmentNos',
                                                                               --[005] code end                                                    
       @IsSendQuery as IsSendQuery,
       v.PrintableDC,
       @SalesApproverIds as SalesApproverIds,
       @SalesOrderNumber as AllocatedSalesOrderNumber,
                                                                               --[011] start                                   
       v.unavailable as UnAvailable,
                                                                               -- [011] end                                   
                                                                               --[012] start                                 
       v.HasBarCodeScan,
       v.BarCodeScanRemarks,
       CAST(v.IsStartInspection AS BIT) AS IsStartInspection,
       CAST(v.ISCloseInspection AS BIT) AS ISCloseInspection,
                                                                               --[012] end                               
                                                                               --[013] Start                             
       ISNULL(v.PartNoQuery, '') AS PartNoQuery,
       ISNULL(v.ManufacturerQuery, '') AS ManufacturerQuery,
       ISNULL(v.PackagingTypeQuery, '') AS PackagingTypeQuery,
       ISNULL(v.MslQuery, '') AS MslQuery,
       ISNULL(v.RohsQuery, '') AS RohsQuery,
       CAST(CASE
                WHEN v.ISReOpenInspection = 1 THEN
                    1
                ELSE
                    dbo.ufn_GetSecurityPermissions(@LoginId, @ClientId, 20010047)
            END AS BIT) AS IsEditInspection,
       CASE
           WHEN
           (
               SELECT ISNULL(COUNT(1), 0)
               FROM tbGoodsInLineInspectionLog
               WHERE GoodsInLineNo = @GoodsInLineId
                     AND InspectionAction = 'Complete Inspection'
           ) > 0 THEN
               CAST(1 AS BIT)
           ELSE
               CAST(0 AS BIT)
       END AS IsAlreadyClosed,
                                                                               --[013] End                               
       isnull(cast(@isExported as bit), 0) as GiInvoiceExported,
       CAST(v.ISReaiseGeneralQuery AS BIT) AS ISReaiseGeneralQuery,
       CAST(dbo.ufn_GetSecurityPermissions(@LoginId, @ClientId, 30005029) AS BIT) AS IsEditAfterStartInspection,
                                                                               -- [015]          
       ISNULL(v.AS6081, 0) 'AS6081',
       ISNULL(co.IsSanctioned, 0) 'IsSanctioned',                              -- [RP-881]  
       CASE
           WHEN ((
                 (
                     SELECT COUNT(1)
                     FROM dbo.tbstock ST
                         INNER JOIN dbo.tbSourcingresult Sr
                             on ST.ParentStockNo = SR.SourcingTableItemNo
                     where SR.SourcingTable = 'HUBSTK'
                           and ST.GoodsInLineNo = @GoodsInLineId
                 ) > 0
                 )
                ) THEN
               1
           ELSE
               0
       END AS IsSTOGi
FROM dbo.vwGoodsInLine v
    LEFT JOIN tbInternalPurchaseOrderLine ipl
        on v.GIPurchaseOrderLineNo = ipl.PurchaseOrderLineNo
    LEFT JOIN tbInternalPurchaseOrder ipo
        on ipl.InternalPurchaseOrderNo = ipo.InternalPurchaseOrderId
    LEFT JOIN tbCurrency cu
        on ipo.CurrencyNo = cu.CurrencyId
    LEFT JOIN dbo.tbManufacturer AS mf
        ON v.CorrectManufacturer = mf.ManufacturerId
    LEFT JOIN dbo.tbManufacturer AS mf1
        ON v.POManufacturerNo = mf1.ManufacturerId
    LEFT JOIN dbo.tbPackage AS pk
        ON v.CorrectPackageType = pk.PackageId
    LEFT JOIN dbo.tbPackage AS pk1
        ON v.POPackageNo = pk1.PackageId
    LEFT JOIN dbo.tbROHSStatus ROHSSts
        ON v.POROHS = ROHSSts.ROHSStatusId
    LEFT OUTER JOIN #GILineQueryStatus gqs
        ON v.GoodsInLineId = gqs.GILineNo
    LEFT JOIN dbo.tbhicstatus as hics
        on hics.HICId = v.HICStatus
    LEFT JOIN tbCompany co
        on v.CompanyNo = co.CompanyId -- RP-881           
WHERE v.GoodsInLineId = @GoodsInLineId

DROP TABLE #GILineQueryStatus