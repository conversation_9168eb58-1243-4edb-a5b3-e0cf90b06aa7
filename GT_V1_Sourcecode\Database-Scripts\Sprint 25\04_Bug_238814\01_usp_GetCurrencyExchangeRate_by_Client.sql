﻿/*   
===========================================================================================  
TASK        	UPDATED BY       DATE          ACTION    DESCRIPTION  
[BUG-238814]    Ngai To		03-Apr-2025			Create   Bug 238814: [PROD Bug] Invoice - Wrong Shipping Cost conversion across Client
===========================================================================================  
*/  
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE OR ALTER PROCEDURE [dbo].[usp_GetCurrencyExchangeRate_by_Client] @ClientNo INT,
	@CurrencyCode NVARCHAR(10)
AS
BEGIN
	DECLARE @ExchangeRate FLOAT
	SELECT TOP 1 @ExchangeRate =c.CurrentExchangeRate
	FROM tbCurrency c
	INNER JOIN tbClient cl ON c.ClientNo = cl.ClientId
	WHERE ClientNo = @ClientNo
		AND Sell = 1
		AND c.Inactive = 0
		AND upper(CurrencyCode) = upper(@CurrencyCode)
	ORDER BY CurrencyId
	SELECT isnull(@ExchangeRate, 1)
END
GO