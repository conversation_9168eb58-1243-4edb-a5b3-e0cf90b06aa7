///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 09.09.2010:
// - Fix passing of VirtualWarehouse to data call
//
// RP 06.01.2010:
// - fully dispose everything
//
// RP 22.12.2009:
// - Allow for Virtual Warehouses
//
// RP 06.10.2009:
// - Changes due to changes in base class
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");

Rebound.GlobalTrader.Site.Controls.DropDowns.Warehouse = function (element) {
    this._includeAll = 0;
	Rebound.GlobalTrader.Site.Controls.DropDowns.Warehouse.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.DropDowns.Warehouse.prototype = {

    get_blnIncludeVirtual: function() { return this._blnIncludeVirtual; }, set_blnIncludeVirtual: function(v) { if (this._blnIncludeVirtual !== v) this._blnIncludeVirtual = v; },
    get_intPOHubClientNo: function() { return this._intPOHubClientNo; }, set_intPOHubClientNo: function(v) { if (this._intPOHubClientNo !== v) this._intPOHubClientNo = v; },
    get_intGlobalLoginClientNo: function () { return this._intGlobalLoginClientNo; }, set_intGlobalLoginClientNo: function (v) { if (this._intGlobalLoginClientNo !== v) this._intGlobalLoginClientNo = v; },
    get_includeAll: function () { return this._includeAll; }, set_includeAll: function (v) { if (this._includeAll !== v) this._includeAll = v; },

    initialize: function () {
        Rebound.GlobalTrader.Site.Controls.DropDowns.Warehouse.callBaseMethod(this, "initialize");
        this.addSetupDataCall(Function.createDelegate(this, this.setupDataCall));
        this.addGetDataOK(Function.createDelegate(this, this.dataCallOK));
    },

    dispose: function() {
        if (this.isDisposed) return;
        this._blnIncludeVirtual = null;
        this._intPOHubClientNo = null;
        this._intGlobalLoginClientNo = null;
        this._includeAll = null;
        Rebound.GlobalTrader.Site.Controls.DropDowns.Warehouse.callBaseMethod(this, "dispose");
    },

    setupDataCall: function () {
        this._objData.set_PathToData("controls/DropDowns/Warehouse");
        this._objData.set_DataObject("Warehouse");
        this._objData.set_DataAction("GetData");
        this._objData.addParameter("IncludeVirtual", this._blnIncludeVirtual);
        this._objData.addParameter("POHubClientNo", this._intPOHubClientNo);
        this._objData.addParameter("GlobalLoginClientNo", this._intGlobalLoginClientNo);
        this._objData.addParameter("IncludeAll", this._includeAll);
    },

    dataCallOK: function() {
        var result = this._objData._result;
        if (result.Warehouses) {
            for (var i = 0; i < result.Warehouses.length; i++) {
                this.addOption(result.Warehouses[i].Name, result.Warehouses[i].ID);
            }
        }
    }

};

Rebound.GlobalTrader.Site.Controls.DropDowns.Warehouse.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.Warehouse", Rebound.GlobalTrader.Site.Controls.DropDowns.Base);
