Type.registerNamespace("Rebound.GlobalTrader.Site.Pages.Orders");Rebound.GlobalTrader.Site.Pages.Orders.CreditDetail=function(n){Rebound.GlobalTrader.Site.Pages.Orders.CreditDetail.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Pages.Orders.CreditDetail.prototype={get_intCreditID:function(){return this._intCreditID},set_intCreditID:function(n){this._intCreditID!==n&&(this._intCreditID=n)},get_ctlMainInfo:function(){return this._ctlMainInfo},set_ctlMainInfo:function(n){this._ctlMainInfo!==n&&(this._ctlMainInfo=n)},get_ctlLines:function(){return this._ctlLines},set_ctlLines:function(n){this._ctlLines!==n&&(this._ctlLines=n)},get_btnPrint:function(){return this._btnPrint},set_btnPrint:function(n){this._btnPrint!==n&&(this._btnPrint=n)},initialize:function(){Rebound.GlobalTrader.Site.Pages.Orders.CreditDetail.callBaseMethod(this,"initialize")},goInit:function(){this._btnPrint&&this._btnPrint.addPrint(Function.createDelegate(this,this.printCreditNote));this._btnPrint&&this._btnPrint.addEmail(Function.createDelegate(this,this.emailCreditNote));this._ctlMainInfo&&this._ctlMainInfo.addGetDataComplete(Function.createDelegate(this,this.ctlMainInfo_GetDataComplete));this._btnPrint&&this._btnPrint.addExtraButtonClick(Function.createDelegate(this,this.printOtherDocs));Rebound.GlobalTrader.Site.Pages.Orders.CreditDetail.callBaseMethod(this,"goInit")},dispose:function(){this.isDisposed||(this._ctlMainInfo&&this._ctlMainInfo.dispose(),this._ctlLines&&this._ctlLines.dispose(),this._btnPrint&&this._btnPrint.dispose(),this._btnPrint=null,this._ctlMainInfo=null,this._ctlLines=null,this._intCreditID=null,Rebound.GlobalTrader.Site.Pages.Orders.CreditDetail.callBaseMethod(this,"dispose"))},printCreditNote:function(){$R_FN.openPrintWindow($R_ENUM$PrintObject.CreditNote,this._intCreditID)},emailCreditNote:function(){$R_FN.openPrintWindow($R_ENUM$PrintObject.CreditNote,this._intCreditID,!0)},printOtherDocs:function(){this._btnPrint._strExtraButtonClickCommand=="EmailCRHTML"&&$R_FN.openPrintWindow($R_ENUM$PrintObject.CREmail,this._intCreditID,!0);this._btnPrint._strExtraButtonClickCommand=="PrintLog"&&$R_FN.openPrintLogWindow($R_ENUM$PrintObject.PrintLog,this._intCreditID,!1,"CreditNote");this._btnPrint._strExtraButtonClickCommand=="PrintCreditXML"&&$R_FN.openPrintWindow($R_ENUM$PrintObject.XmlCredit,this._intCreditID);this._btnPrint._strExtraButtonClickCommand=="EmailCreditXML"&&$R_FN.openPrintWindow($R_ENUM$PrintObject.XmlCredit,this._intCreditID,!0)},ctlMainInfo_GetDataComplete:function(){this._ctlLines.getData();this.setLineFieldsFromHeader()},setLineFieldsFromHeader:function(){var n=this._ctlMainInfo.getFieldValue("hidCustomerName"),t=this._ctlMainInfo.getFieldValue("hidCreditNumber"),i=this._ctlMainInfo.getFieldValue("hidCurrencyCode"),r=this._ctlMainInfo.getFieldValue("hidCurrencyNo"),u=this._ctlMainInfo.getFieldValue("ctlCreditDate"),f=this._ctlMainInfo.getFieldValue("ctlReferenceDate");this._ctlLines._frmEdit&&this._ctlLines._frmEdit.setCurrency(this._ctlMainInfo.getFieldValue("hidCurrencyCode"));this._ctlLines._frmAdd&&this._ctlLines._frmAdd.setFieldsFromHeader(t,n,r,i,u,this._ctlMainInfo.getFieldValue("hidInvoiceNumber"),this._ctlMainInfo.getFieldValue("hidCRMANumber"),f,this._ctlMainInfo.getFieldValue("hidIsClientInvoice"),this._ctlMainInfo._InvoiceNo,this._ctlMainInfo.getFieldValue("hidClientInvoiceLineNo"));this._ctlLines&&(this._ctlLines._blnFronClientInvoice=this._ctlMainInfo.getFieldValue("hidIsClientInvoice"));this._ctlLines&&(this._ctlLines._blnHubLogin=this._ctlMainInfo.getFieldValue("hidHubLogin"));this._ctlLines&&(this._ctlLines._blnExported=this._ctlMainInfo._blnExported);n=null;t=null;i=null;r=null;u=null;f=null}};Rebound.GlobalTrader.Site.Pages.Orders.CreditDetail.registerClass("Rebound.GlobalTrader.Site.Pages.Orders.CreditDetail",Rebound.GlobalTrader.Site.Pages.Content,Sys.IDisposable);