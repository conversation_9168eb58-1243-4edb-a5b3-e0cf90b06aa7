﻿//Marker     Changed by         Date         Remarks
//[001]      <PERSON><PERSON><PERSON><PERSON>     20/09/2021    Added class for Supplier contact.
using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;
using Rebound.GlobalTrader.DAL;
using Rebound.GlobalTrader.BLL;

namespace Rebound.GlobalTrader.BLL
{
    public partial class SupplierContact : BizObject
    {
        #region Properties

        protected static DAL.RohsStatusElement Settings
        {
            get { return Globals.Settings.RohsStatuss; }
        }

        /// <summary>
        /// ROHSStatusId
        /// </summary>
        public System.String SupplierContactEmail { get; set; }
        /// <summary>
        /// Name
        /// </summary>
        public System.String Name { get; set; }

        #endregion

        #region Methods

        /// <summary>
        /// DropDown
        /// Calls [usp_dropdown_SupplierContact]
        /// </summary>
        public static List<SupplierContact> DropDown(System.Int32 SupplierId)
        {
            List<SupplierContactDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.SupplierContact.DropDown(SupplierId);
            if (lstDetails == null)
            {
                return new List<SupplierContact>();
            }
            else
            {
                List<SupplierContact> lst = new List<SupplierContact>();
                foreach (SupplierContactDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.SupplierContact obj = new Rebound.GlobalTrader.BLL.SupplierContact();
                    obj.SupplierContactEmail = objDetails.SupplierContactEmail;
                    obj.Name = objDetails.Name;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }





        #endregion
    }
}
