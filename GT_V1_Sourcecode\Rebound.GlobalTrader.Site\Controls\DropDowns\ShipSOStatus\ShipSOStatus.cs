﻿using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;

namespace Rebound.GlobalTrader.Site.Controls.DropDowns {
	public partial class ShipSOStatus : Base {

		public bool LimitToCurrentUsersTeam { get; set; }
		public bool LimitToCurrentUsersDivision { get; set; }
		public bool ExcludeCurrentUser { get; set; }

		protected override void OnLoad(EventArgs e) {
            SetDropDownType("ShipSOStatus");
            AddScriptReference("Controls.DropDowns.ShipSOStatus.ShipSOStatus");
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.DropDowns.ShipSOStatus", ClientID);
            _scScriptControlDescriptor.AddProperty("blnLimitToCurrentUsersDivision", LimitToCurrentUsersDivision);
			_scScriptControlDescriptor.AddProperty("blnLimitToCurrentUsersTeam", LimitToCurrentUsersTeam);
			_scScriptControlDescriptor.AddProperty("blnExcludeCurrentUser", ExcludeCurrentUser);
			base.OnLoad(e);
		}

	}
}