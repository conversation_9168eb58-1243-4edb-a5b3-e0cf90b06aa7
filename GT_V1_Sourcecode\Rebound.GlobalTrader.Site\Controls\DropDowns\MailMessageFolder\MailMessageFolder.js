Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");Rebound.GlobalTrader.Site.Controls.DropDowns.MailMessageFolder=function(n){Rebound.GlobalTrader.Site.Controls.DropDowns.MailMessageFolder.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.DropDowns.MailMessageFolder.prototype={initialize:function(){Rebound.GlobalTrader.Site.Controls.DropDowns.MailMessageFolder.callBaseMethod(this,"initialize");this.addSetupDataCall(Function.createDelegate(this,this.setupDataCall));this.addGetDataOK(Function.createDelegate(this,this.dataCallOK))},dispose:function(){this.isDisposed||Rebound.GlobalTrader.Site.Controls.DropDowns.MailMessageFolder.callBaseMethod(this,"dispose")},setupDataCall:function(){this._objData.set_PathToData("controls/DropDowns/MailMessageFolder");this._objData.set_DataObject("MailMessageFolder");this._objData.set_DataAction("GetData")},dataCallOK:function(){var t=this._objData._result,n;if(t.Types)for(this.addOption($R_RES.Inbox,0),n=0;n<t.Types.length;n++)this.addOption(t.Types[n].Name,t.Types[n].ID)}};Rebound.GlobalTrader.Site.Controls.DropDowns.MailMessageFolder.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.MailMessageFolder",Rebound.GlobalTrader.Site.Controls.DropDowns.Base);