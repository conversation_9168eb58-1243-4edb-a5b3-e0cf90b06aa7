Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");Rebound.GlobalTrader.Site.Controls.DropDowns.Warehouse=function(n){this._includeAll=0;Rebound.GlobalTrader.Site.Controls.DropDowns.Warehouse.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.DropDowns.Warehouse.prototype={get_blnIncludeVirtual:function(){return this._blnIncludeVirtual},set_blnIncludeVirtual:function(n){this._blnIncludeVirtual!==n&&(this._blnIncludeVirtual=n)},get_intPOHubClientNo:function(){return this._intPOHubClientNo},set_intPOHubClientNo:function(n){this._intPOHubClientNo!==n&&(this._intPOHubClientNo=n)},get_intGlobalLoginClientNo:function(){return this._intGlobalLoginClientNo},set_intGlobalLoginClientNo:function(n){this._intGlobalLoginClientNo!==n&&(this._intGlobalLoginClientNo=n)},get_includeAll:function(){return this._includeAll},set_includeAll:function(n){this._includeAll!==n&&(this._includeAll=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.DropDowns.Warehouse.callBaseMethod(this,"initialize");this.addSetupDataCall(Function.createDelegate(this,this.setupDataCall));this.addGetDataOK(Function.createDelegate(this,this.dataCallOK))},dispose:function(){this.isDisposed||(this._blnIncludeVirtual=null,this._intPOHubClientNo=null,this._intGlobalLoginClientNo=null,this._includeAll=null,Rebound.GlobalTrader.Site.Controls.DropDowns.Warehouse.callBaseMethod(this,"dispose"))},setupDataCall:function(){this._objData.set_PathToData("controls/DropDowns/Warehouse");this._objData.set_DataObject("Warehouse");this._objData.set_DataAction("GetData");this._objData.addParameter("IncludeVirtual",this._blnIncludeVirtual);this._objData.addParameter("POHubClientNo",this._intPOHubClientNo);this._objData.addParameter("GlobalLoginClientNo",this._intGlobalLoginClientNo);this._objData.addParameter("IncludeAll",this._includeAll)},dataCallOK:function(){var t=this._objData._result,n;if(t.Warehouses)for(n=0;n<t.Warehouses.length;n++)this.addOption(t.Warehouses[n].Name,t.Warehouses[n].ID)}};Rebound.GlobalTrader.Site.Controls.DropDowns.Warehouse.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.Warehouse",Rebound.GlobalTrader.Site.Controls.DropDowns.Base);