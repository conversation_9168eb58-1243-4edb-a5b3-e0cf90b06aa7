﻿//Marker     Changed by         Date         Remarks
//[001]      A<PERSON><PERSON><PERSON>     20/09/2021    Added class for supplier contact.
using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;
using System.Data.Common;

namespace Rebound.GlobalTrader.DAL
{
    public abstract class SupplierContactProvider : DataAccess
    {
        static private SupplierContactProvider _instance = null;
        /// <summary>
        /// Returns an instance of the provider type specified in the config file
        /// </summary>       
        static public SupplierContactProvider Instance
        {
            get
            {
                if (_instance == null) _instance = (SupplierContactProvider)Activator.CreateInstance(Type.GetType(Globals.Settings.SupplierContacts.ProviderType));
                return _instance;
            }
        }
        public SupplierContactProvider()
        {
            this.ConnectionString = Globals.Settings.RohsStatuss.ConnectionString;
        }

        #region Method Registrations


        /// <summary>
        /// DropDown
        /// Calls [usp_dropdown_SupplierContact]
        /// </summary>
        public abstract List<SupplierContactDetails> DropDown(System.Int32 SupplierId);





        #endregion



    }
}
