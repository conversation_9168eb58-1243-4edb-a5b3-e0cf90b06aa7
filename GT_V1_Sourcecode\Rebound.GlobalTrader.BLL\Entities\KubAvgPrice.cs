﻿using Rebound.GlobalTrader.DAL;
using Rebound.GlobalTrader.DAL.SQLClient;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Rebound.GlobalTrader.BLL
{
    //Anuj
    public partial class KubAvgPrice : BizObject 
    {
      
        #region Properties
        public System.String AveragePriceOfPartLast12Months { get; set; }
        public System.String LastUpdatedDate { get; set; }
        #endregion

        #region Methods
        /// <summary>
        /// get kub price Details
        /// Calls [sp_KubPrice]
        /// </summary>
        public static KubAvgPrice GetKubAvgPriceDetails(System.String PartNo, System.Int32 ClientID)
        {
            KubAvgPrice obj = new KubAvgPrice();
            KubAvgPriceDetails objDetails = new KubAvgPriceDetails();
            SQLKubProvider objSQLKubProvider = new SQLKubProvider();
            try
            {
                objDetails = objSQLKubProvider.FetchKubAvgPriceDetails(PartNo, ClientID);
                if (objDetails == null)
                {
                    return null;
                }
                else
                {
                    obj.AveragePriceOfPartLast12Months = objDetails.AveragePriceOfPartLast12Months;
                    obj.LastUpdatedDate = objDetails.LastUpdatedDate;
                }
                return obj;
            }
            catch(Exception ex)
            {
                throw ex; 
            } 
            finally
            {
                obj = null;
            }
        }
        #endregion
    }
}
