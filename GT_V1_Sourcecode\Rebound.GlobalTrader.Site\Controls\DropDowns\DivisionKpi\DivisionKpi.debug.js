///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");

Rebound.GlobalTrader.Site.Controls.DropDowns.DivisionKpi = function(element) { 
	Rebound.GlobalTrader.Site.Controls.DropDowns.DivisionKpi.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.DropDowns.DivisionKpi.prototype = {
    get_intSelClientNo: function () { return this._intSelClientNo; }, set_intSelClientNo: function (v) { if (this._intSelClientNo !== v) this._intSelClientNo = v; },
    get_intGlobalLoginClientNo: function () { return this._intGlobalLoginClientNo; }, set_intGlobalLoginClientNo: function (v) { if (this._intGlobalLoginClientNo !== v) this._intGlobalLoginClientNo = v; },
	initialize: function() {
		this.addSetupDataCall(Function.createDelegate(this, this.setupDataCall));
		this.addGetDataOK(Function.createDelegate(this, this.dataCallOK));
		Rebound.GlobalTrader.Site.Controls.DropDowns.DivisionKpi.callBaseMethod(this, "initialize");
	},
	
	dispose: function() { 
	    if (this.isDisposed) return;
	    this._intSelClientNo = null;
	    this._intGlobalLoginClientNo = null;
		Rebound.GlobalTrader.Site.Controls.DropDowns.DivisionKpi.callBaseMethod(this, "dispose");
	},
	
	setupDataCall: function() { 
		this._objData.set_PathToData("controls/DropDowns/DivisionKpi");
		this._objData.set_DataObject("DivisionKpi");
		this._objData.set_DataAction("GetData");
		this._objData.addParameter("intSelClientNo", this._intSelClientNo);
		this._objData.addParameter("GlobalLoginClientNo", this._intGlobalLoginClientNo);
	},
	
	dataCallOK: function() { 
		var result = this._objData._result;
        if (result.DivisionKpis) {
            for (var i = 0; i < result.DivisionKpis.length; i++) {
                this.addOption(result.DivisionKpis[i].Name, result.DivisionKpis[i].ID);
			}
		}
	}
	
};

Rebound.GlobalTrader.Site.Controls.DropDowns.DivisionKpi.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.DivisionKpi", Rebound.GlobalTrader.Site.Controls.DropDowns.Base);
