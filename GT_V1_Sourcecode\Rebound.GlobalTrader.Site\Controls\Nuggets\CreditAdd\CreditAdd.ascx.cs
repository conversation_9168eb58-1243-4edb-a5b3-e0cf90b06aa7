//---------------------------------------------------------------------------------------------------------
// RP 04.12.2009:
// - allow sources for new lines to be set on permissions
//---------------------------------------------------------------------------------------------------------
using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site;
using Rebound.GlobalTrader.Site.Controls;

namespace Rebound.GlobalTrader.Site.Controls.Nuggets {
	public partial class CreditAdd : Base {

		#region Locals

		protected PlaceHolder plhAddNotAllowed;
		protected IconButton ibtnAdd;
		protected Forms.CreditAdd_Add frmAdd;

		#endregion

		#region Properties

		private bool _blnCanAdd = true;
		public bool CanAdd {
			get { return _blnCanAdd; }
			set { _blnCanAdd = value; }
		}

		private bool _blnCanAddFromInvoice = true;
		public bool CanAddFromInvoice {
			get { return _blnCanAddFromInvoice; }
			set { _blnCanAddFromInvoice = value; }
		}

		private bool _blnCanAddFromCRMA = true;
		public bool CanAddFromCRMA {
			get { return _blnCanAddFromCRMA; }
			set { _blnCanAddFromCRMA = value; }
		}

		#endregion

		#region Overrides

		/// <summary>
		/// Oninit
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
			base.OnInit(e);
			AddScriptReference("Controls.Nuggets.CreditAdd.CreditAdd.js");
			TitleText = Functions.GetGlobalResource("Nuggets", "CreditAdd");
			ibtnAdd = FindIconButton("ibtnAdd");
			plhAddNotAllowed = (PlaceHolder)FindContentControl("plhAddNotAllowed");
			frmAdd = (Forms.CreditAdd_Add)FindFormControl("ctlAdd");
		}

		protected override void OnLoad(EventArgs e) {
			SetupScriptDescriptors();
			base.OnLoad(e);
			frmAdd.CanAddFromCRMA = _blnCanAddFromCRMA;
			frmAdd.CanAddFromInvoice = _blnCanAddFromInvoice;
		}

		protected override void OnPreRender(EventArgs e) {
			plhAddNotAllowed.Visible = !_blnCanAdd;
			ibtnAdd.Visible = _blnCanAdd;
			base.OnPreRender(e);
		}

		#endregion

		/// <summary>
		/// Sets up AJAX script descriptors
		/// </summary>
		private void SetupScriptDescriptors() {
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.Nuggets.CreditAdd", ctlDesignBase.ClientID);
			_scScriptControlDescriptor.AddElementProperty("ibtnAdd", ibtnAdd.ClientID);
		}


	}
}
