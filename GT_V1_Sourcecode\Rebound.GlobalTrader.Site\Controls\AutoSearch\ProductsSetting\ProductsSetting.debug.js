///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//Marker     Changed by      Date         Remarks
//[001]      <PERSON>     17/08/2021   (GTDP-198) Controlling specific product groups for purchasing
//[002]      <PERSON>     25/10/2021   Product Warning message from copany setup.

//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.AutoSearch");

Rebound.GlobalTrader.Site.Controls.AutoSearch.ProductsSetting = function (element) {
    Rebound.GlobalTrader.Site.Controls.AutoSearch.ProductsSetting.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.AutoSearch.ProductsSetting.prototype = {

get_ShowInactive: function() { return this._ShowInactive; }, set_ShowInactive: function(value) { if (this._ShowInactive !== value)  this._ShowInactive = value; }, 
//get_intPOHubClientNo: function () { return this._intPOHubClientNo; }, set_intPOHubClientNo: function (v) { if (this._intPOHubClientNo !== v) this._intPOHubClientNo = v; },

	initialize: function(){
	    Rebound.GlobalTrader.Site.Controls.AutoSearch.ProductsSetting.callBaseMethod(this, "initialize");
		this.addDataReturnedEvent(Function.createDelegate(this, this.dataReturned));
		this.addSelectionMadeEvent(Function.createDelegate(this, this.selectionMade));
	
		this.setupDataObject("ProductsSetting");
	},
	
	dispose: function(){
		if (this.isDisposed) return;
		this._ShowInactive = null;
		//this._intPOHubClientNo = null;
		Rebound.GlobalTrader.Site.Controls.AutoSearch.ProductsSetting.callBaseMethod(this, "dispose");
	},

    //[002] code start
    selectionMade: function () {
        //var extar = this._varSelectedExtraData;
        //var strAlt = "";
        //if (typeof extar === "undefined") { }
        //else {
        //    var nameArr = extar.split(':');
        //    var IsHaz = nameArr[0];
        //    var IsViaIPO = nameArr[1];
        //    var productMsg = nameArr[2];
        //    var IsRHProd = nameArr[3];
        //    strAlt = productMsg.replaceAll("&#013;","\n\n");
           
        //}
        //setTimeout(function () { if (IsHaz == 'true' || IsViaIPO == 'true' || IsRHProd == 'true') { alert(strAlt); } }, 300);

    },
    //[002] code end

	
	
	dataReturned: function(){
	
		if (!this._result) return;
		if (this._result.TotalRecords > 0) {
			for (var i = 0, l = this._result.Results.length; i < l; i++) {
				var res = this._result.Results[i];
				var strHTML = "";
				if (this._enmResultsActionType == $R_ENUM$AutoSearchResultsActionType.Navigate) {
					strHTML = $RGT_nubButton_Manufacturer(res.ID, res.Name);
				} else {
					strHTML = $R_FN.setCleanTextValue(res.Name);
				}
                //this.addResultItem(strHTML, $R_FN.setCleanTextValue(res.Name), res.ID, res.IsHa + ":" + res.IsOrdViaIPO);
                //this.addResultItem(strHTML, $R_FN.setCleanTextValue(res.Name), res.ID, res.IsHa + ":" + res.IsOrdViaIPO + ":" + $R_FN.setCleanTextValue(res.ProductMessage));
                this.addResultItem(strHTML, $R_FN.setCleanTextValue(res.Name), res.ID, res.IsHa + ":" + res.IsOrdViaIPO + ":" + $R_FN.setCleanTextValue(res.ProductMessage) + ":" + res.IsRestrictedProd);
				strHTML = null;
				res = null;
			}
		}
	}
};
Rebound.GlobalTrader.Site.Controls.AutoSearch.ProductsSetting.registerClass("Rebound.GlobalTrader.Site.Controls.AutoSearch.ProductsSetting", Rebound.GlobalTrader.Site.Controls.AutoSearch.Base, Sys.IDisposable);
