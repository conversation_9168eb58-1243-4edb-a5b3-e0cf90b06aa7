///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//----------------------------------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//----------------------------------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.ItemSearch");

Rebound.GlobalTrader.Site.Controls.ItemSearch.SRMALines = function(element) { 
	Rebound.GlobalTrader.Site.Controls.ItemSearch.SRMALines.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.ItemSearch.SRMALines.prototype = {

	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.ItemSearch.SRMALines.callBaseMethod(this, "initialize");
		this.addSetupData(Function.createDelegate(this, this.doSetupData));
		this.addGetDataComplete(Function.createDelegate(this, this.doGetDataComplete));
	},

	dispose: function() { 
		if (this.isDisposed) return;
		Rebound.GlobalTrader.Site.Controls.ItemSearch.SRMALines.callBaseMethod(this, "dispose");
	},
	
	doSetupData: function() {
		this._objData.set_PathToData("controls/ItemSearch/SRMALines");
		this._objData.set_DataObject("SRMALines");
		this._objData.set_DataAction("GetData");
		this._objData.addParameter("SRMANoLo", this.getFieldValue_Min("ctlSRMANo"));
		this._objData.addParameter("SRMANoHi", this.getFieldValue_Max("ctlSRMANo"));
		this._objData.addParameter("Part", this.getFieldValue("ctlPart"));
		this._objData.addParameter("Contact", this.getFieldValue("ctlContact"));
		this._objData.addParameter("CMName", this.getFieldValue("ctlCompany"));
		this._objData.addParameter("Buyer", this.getFieldValue("ctlBuyer"));
		this._objData.addParameter("Notes", this.getFieldValue("ctlSRMANotes"));
		this._objData.addParameter("PONoLo", this.getFieldValue_Min("ctlPurchaseOrderNo"));
		this._objData.addParameter("PONoHi", this.getFieldValue_Max("ctlPurchaseOrderNo"));
		this._objData.addParameter("SRMADateFrom", this.getFieldValue("ctlSRMADateFrom"));
		this._objData.addParameter("SRMADateTo", this.getFieldValue("ctlSRMADateTo"));
	},

	doGetDataComplete: function() {
		for (var i = 0, l = this._objResult.Results.length; i < l; i++) {
			var row = this._objResult.Results[i];
			var aryData = [
				row.No,
				$R_FN.setCleanTextValue(row.CMName),
				$R_FN.writePartNo(row.Part, row.ROHS),
				$R_FN.setCleanTextValue(row.Date),
				row.Quantity,
				$R_FN.setCleanTextValue(row.Buyer),
				$R_FN.setCleanTextValue(row.PurchaseOrderNo)
			];
			this._tblResults.addRow(aryData, row.ID, false);
			aryData = null; row = null;
		}
	}
};

Rebound.GlobalTrader.Site.Controls.ItemSearch.SRMALines.registerClass("Rebound.GlobalTrader.Site.Controls.ItemSearch.SRMALines", Rebound.GlobalTrader.Site.Controls.ItemSearch.Base, Sys.IDisposable);
