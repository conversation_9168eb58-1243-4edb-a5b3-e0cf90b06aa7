<%@ Control Language="C#" CodeBehind="DebitLines.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Nuggets.DebitLines" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>

<ReboundUI_Nugget:DesignBase ID="ctlDB" runat="server" TitleText="Lines" BoxType="Standard" >

	<Links>
		<ReboundUI:IconButton ID="ibtnAdd" runat="server" IconButtonMode="hyperlink" IconGroup="Nugget" IconTitleResource="Add" IconCSSType="Add" />
		<ReboundUI:IconButton ID="ibtnEdit" runat="server" IconButtonMode="hyperlink" IconGroup="Nugget" IconTitleResource="Edit" IconCSSType="Edit" IsInitiallyEnabled="false" />
		<ReboundUI:IconButton ID="ibtnDelete" runat="server" IconButtonMode="hyperlink" IconGroup="Nugget" IconTitleResource="Delete" IconCSSType="Delete" IsInitiallyEnabled="false" />
	</Links>
	
	<Content>
		<ReboundUI:FlexiDataTable ID="tbl" runat="server" AllowSelection="true" PanelHeight="150" />
		<asp:Panel ID="pnlSummary" runat="server" CssClass="dataTableSummary">
			<div class="dataTableSummaryField">
				<div class="title"><%=Functions.GetGlobalResource("FormFields", "SubTotal")%></div>
				<div class="item"><asp:Label ID="lblSubTotal" runat="server" /></div>
			</div>
			<div class="dataTableSummaryField">
				<div class="title"><%=Functions.GetGlobalResource("FormFields", "Freight")%></div>
				<div class="item"><asp:Label ID="lblFreight" runat="server" /></div>
			</div>
			<div class="dataTableSummaryField">
				<div class="title"><%=Functions.GetGlobalResource("FormFields", "Tax")%></div>
				<div class="item"><asp:Label ID="lblTax" runat="server" /></div>
			</div>
			<div class="dataTableSummaryField">
				<div class="title"><%=Functions.GetGlobalResource("FormFields", "Total")%></div>
				<div class="item"><asp:Label ID="lblTotal" runat="server" /></div>
			</div>
		</asp:Panel>
		<br />
		<asp:Panel ID="pnlLoadingLineDetail" runat="server" CssClass="subInfoLoading invisible"><%=Functions.GetGlobalResource("Misc", "Loading")%></asp:Panel>
		<asp:Panel ID="pnlLineDetailError" runat="server" CssClass="error invisible" />
		<asp:Panel id="pnlLineDetail" runat="server" CssClass="invisible">
             <input type="button" id="hypShowHideDbn"  class="showHideDbn"  value="-" />  
            <label style="margin-left:-2px;margin: 0px 0px 5px 0px; font-size: 12px;font-weight: bold;"><%=Functions.GetGlobalResource("FormFields", "LineDetails")%></label>
            <div class="steps" id="divDbn">
			<h4 class="extraTopMargin"><asp:HyperLink ID="hypPrev" runat="server" NavigateUrl="javascript:void(0);" CssClass="linePagingNextPrev linePagingPrev">&laquo;</asp:HyperLink><asp:Label ID="lblLineNumber" runat="server" /><asp:HyperLink ID="hypNext" runat="server" NavigateUrl="javascript:void(0);" CssClass="linePagingNextPrev linePagingNext">&raquo;</asp:HyperLink></h4>
			<table class="twoCols">
				<tr>
					<td class="col1">
						<table cellpadding="0" cellspacing="0" border="0" class="dataItems">
							<ReboundUI:DataItemRow id="ctlPartNo" runat="server" ResourceTitle="PartNo" />
						    <ReboundUI:DataItemRow id="hidPartNo" runat="server" FieldType="Hidden" />
							<ReboundUI:DataItemRow id="ctlService" runat="server" ResourceTitle="Service" />
						    <ReboundUI:DataItemRow id="hidService" runat="server" FieldType="Hidden" />
							<ReboundUI:DataItemRow id="ctlServiceDescription" runat="server" ResourceTitle="Description" />
							<ReboundUI:DataItemRow id="ctlROHS" runat="server" ResourceTitle="ROHS" />
							<ReboundUI:DataItemRow id="hidROHS" runat="server" FieldType="Hidden" />
                            <ReboundUI:DataItemRow id="ctlDateCode" runat="server" ResourceTitle="DateCode" />
							<ReboundUI:DataItemRow id="ctlSupplierPart" runat="server" ResourceTitle="SupplierPart" />
							<ReboundUI:DataItemRow id="ctlManufacturer" runat="server" ResourceTitle="Manufacturer" />
							<ReboundUI:DataItemRow id="hidManufacturer" runat="server" FieldType="Hidden" />
							<ReboundUI:DataItemRow id="hidManufacturerNo" runat="server" FieldType="Hidden" />
							<ReboundUI:DataItemRow id="ctlTaxable" runat="server" FieldType="CheckBox" ResourceTitle="IsTaxable" />
						</table>
					</td>
					<td class="col2">
						<table cellpadding="0" cellspacing="0" border="0" class="dataItems">
							<ReboundUI:DataItemRow id="ctlPackage" runat="server" ResourceTitle="Package" />
							<ReboundUI:DataItemRow id="hidPackageNo" runat="server" FieldType="Hidden" />
							<%--<ReboundUI:DataItemRow id="ctlProduct" runat="server" ResourceTitle="Product" />--%>
                             <ReboundUI:DataItemRow id="ctlProduct" runat="server" FieldType="Hidden"  />
                             <ReboundUI:DataItemRow id="ctlProductDis" runat="server" ResourceTitle="Product" />
							<ReboundUI:DataItemRow id="hidProductNo" runat="server" FieldType="Hidden" />
						    <ReboundUI:DataItemRow id="ctlQuantity" runat="server" ResourceTitle="Quantity" />
							<ReboundUI:DataItemRow id="ctlPrice" runat="server" ResourceTitle="Price" />
							<ReboundUI:DataItemRow id="hidPriceRaw" runat="server" FieldType="Hidden" />
							<%--<ReboundUI:DataItemRow id="ctlLandedCost" runat="server" ResourceTitle="LandedCost" />--%>
							<ReboundUI:DataItemRow id="ctlLineNotes" runat="server" ResourceTitle="Notes" />
                            <ReboundUI:DataItemRow id="hidPrintHaza" runat="server" FieldType="Hidden" />
                            <ReboundUI:DataItemRow id="hidProductHazar" runat="server" FieldType="Hidden" />
						</table>
					</td>
				</tr>
			</table>
                 </div>
               <script>
            $(document).ready(function () {
            $("#divDbn").show();
            $("#hypShowHideDbn").val("-");
            $("#hypShowHideDbn").click(function () {
            if ($(this).val() == "+") {
                $("#divDbn").show();
                $(this).val("-");} else {
            $("#divDbn").hide();
            $(this).val("+");
            }});});
                </script>
		</asp:Panel>
		
	</Content>
	
	<Forms>
		<ReboundForm:DebitLines_Add id="frmAdd" runat="server" />
		<ReboundForm:DebitLines_Edit id="frmEdit" runat="server" />
		<ReboundForm:DebitLines_Delete id="frmDelete" runat="server" />
	</Forms>
	
</ReboundUI_Nugget:DesignBase>
