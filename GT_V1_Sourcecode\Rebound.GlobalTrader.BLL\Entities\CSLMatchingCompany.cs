﻿//Marker     changed by      date         Remarks
//[001]      Arpit           03/03/2023   RP-257

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Rebound.GlobalTrader.DAL;
using System.Threading.Tasks;

namespace Rebound.GlobalTrader.BLL
{
    public partial class CSLMatchingCompany : BizObject
    {
        #region Properties

        public int CompanyId { get; set; }
        public string CustomerCode { get; set; }
        public int ClientNo { get; set; }
        public string ClientName { get; set; }
        public string CompanyName { get; set; }
        public string GT_Company_Address { get; set; }
        public string CSL_Name { get; set; }
        public string CSL_Address { get; set; }
        public string CSL_ALT_Name { get; set; }
        public string CSL_DateInserted { get; set; }
        public string Notes { get; set; }
        public string ImportantNotes { get; set; }

        #endregion

        protected static DAL.CSLMatchingCompanyElement Settings
        {
            get { return Globals.Settings.CSLMatchingCompanys; }
        }

        //[001]
        public static List<CSLMatchingCompany> GetListCSLMatchingCompany(string dateInput, string type)
        {
            try
            {
                List<CSLMatchingCompanyDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.CSLMatchingCompany.GetListCSLMatchingCompany(dateInput, type);
                if (lstDetails == null)
                {
                    return new List<CSLMatchingCompany>();
                }
                else
                {
                    List<CSLMatchingCompany> lst = new List<CSLMatchingCompany>();
                    foreach (CSLMatchingCompanyDetails objDetails in lstDetails)
                    {
                        Rebound.GlobalTrader.BLL.CSLMatchingCompany obj = new Rebound.GlobalTrader.BLL.CSLMatchingCompany();
                        obj.CompanyId = objDetails.CompanyId;
                        obj.CustomerCode = objDetails.CustomerCode;
                        obj.ClientNo = objDetails.ClientNo;
                        obj.ClientName = objDetails.ClientName;
                        obj.CompanyName = objDetails.CompanyName;
                        obj.GT_Company_Address = objDetails.GT_Company_Address;
                        obj.CSL_Name = objDetails.CSL_Name;
                        obj.CSL_Address = objDetails.CSL_Address;
                        obj.CSL_ALT_Name = objDetails.CSL_ALT_Name;
                        obj.Notes = objDetails.Notes;
                        obj.ImportantNotes = objDetails.ImportantNotes;

                        lst.Add(obj);
                        obj = null;
                    }
                    lstDetails = null;
                    return lst;
                }
            }
            catch(Exception ex)
            {
                throw new Exception(ex.Message, ex.InnerException);
            }
        }
        //[001]
        public static List<CSLMatchingCompany> GetListCSVImportedDate(string type)
        {
            try
            {
                List<CSLMatchingCompanyDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.CSLMatchingCompany.GetCSVImportedDate(type);
                if (lstDetails == null)
                {
                    return new List<CSLMatchingCompany>();
                }
                else
                {
                    List<CSLMatchingCompany> lst = new List<CSLMatchingCompany>();
                    foreach (CSLMatchingCompanyDetails objDetails in lstDetails)
                    {
                        Rebound.GlobalTrader.BLL.CSLMatchingCompany obj = new Rebound.GlobalTrader.BLL.CSLMatchingCompany();
                        obj.CSL_DateInserted = objDetails.CSL_DateInserted;

                        lst.Add(obj);
                        obj = null;
                    }
                    lstDetails = null;
                    return lst;
                }
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message, ex.InnerException);
            }
        }

    }
}