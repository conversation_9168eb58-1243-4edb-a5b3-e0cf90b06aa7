﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Rebound.GlobalTrader.DAL
{
    public abstract class SourcingAuditLogProvider: DataAccess
    {
		private static SourcingAuditLogProvider _instance = null;
		/// <summary>
		/// Returns an instance of the provider type specified in the config file
		/// </summary>       
		static public SourcingAuditLogProvider Instance
		{
			get
			{
				if (_instance == null) _instance = (SourcingAuditLogProvider)Activator.CreateInstance(Type.GetType(Globals.Settings.SourcingAuditLog.ProviderType));
				return _instance;
			}
		}
		public SourcingAuditLogProvider()
		{
			this.ConnectionString = Globals.Settings.SourcingAuditLog.ConnectionString;
		}

		#region methods
		public abstract List<SourcingAuditLogDetails> GetBulkEditLog(int? order,
                                                               int? sortDir,
                                                               int? pageIndex,
                                                               int? pageSize,
                                                               string sourcingType,
                                                               string partSearch,
                                                               int? userId,
                                                               DateTime? updatedDateFrom,
                                                               DateTime? updatedDateTo);
        #endregion
    }
}
