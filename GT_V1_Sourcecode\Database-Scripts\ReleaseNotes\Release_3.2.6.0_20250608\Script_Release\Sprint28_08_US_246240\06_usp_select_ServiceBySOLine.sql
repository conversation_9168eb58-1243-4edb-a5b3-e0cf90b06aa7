﻿
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

/*  
========================================================================================================================================   
TASK           UPDATED BY      DATE            ACTION        DESCRIPTION  
[US-246240]    Phuc Hoang      15-May-2025     CREATE        Invoice - Line from 'Service' source Function on Client/ DMCC side (Part 2)
========================================================================================================================================  
*/ 

CREATE OR ALTER PROCEDURE [dbo].[usp_select_ServiceBySOLine] (
	@SalesOrderLineId int 
)
AS
BEGIN

	SELECT ser.*, ISNULL(sol.ServiceCostRef, ser.Cost) AS SoLineCost, sol.Price AS SoLinePrice, sol.Quantity AS SoQuantity, sol.SalesOrderNo, sol.Notes AS SoNotes 
	FROM dbo.tbSalesOrderLine sol
	LEFT JOIN dbo.vwService ser ON sol.ServiceNo = ser.ServiceId
	WHERE sol.SalesOrderLineId = @SalesOrderLineId

END

GO


