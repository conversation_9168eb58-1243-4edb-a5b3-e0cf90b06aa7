using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;

namespace Rebound.GlobalTrader.Site.Controls.HomeNuggets.Data
{
    [WebService(Namespace = "http://tempuri.org/")]
    [WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    public class MyStatistics : Rebound.GlobalTrader.Site.Data.Base
    {

        public override void ProcessRequest(HttpContext context)
        {
            if (base.init(context))
            {
                switch (Action)
                {
                    case "GetData": GetData(); break;
                    default: WriteErrorActionNotFound(); break;
                }
            }
        }

        /// <summary>
        /// Gets the main data
        /// </summary>
        private void GetData()
        {
            try
            {
                if (SettingsManager.GetSetting_Boolean(SettingItem.List.IncludeShippingOnHomepageStats))
                {
                    GetDataWithShipping();
                }
                else
                {
                    GetDataWithoutShipping();
                }
            }
            catch (Exception ex)
            {
                Errorlog obj = new Errorlog();
                obj.LogMessage(Convert.ToString("Exception- " + ex.Message + " " + ex.StackTrace));
            }
        }

        /// <summary>
        /// Gets the main data without any shipping charges or freight
        /// </summary>
        private void GetDataWithoutShipping()
        {
            int intLoginID = LoginID;
            bool isIncludeCredit = false;
            if (GetFormValue_Int("OtherLoginID") > 0) intLoginID = GetFormValue_Int("OtherLoginID");
            isIncludeCredit = GetFormValue_Boolean("includeCredit");

            Login gpThisYear = Login.GetThisYearGP(intLoginID);
            Login gpThisMonth = Login.GetThisMonthGP(intLoginID);
            Login gpNextMonth = Login.GetNextMonthGP(intLoginID);
            Login gpLastYear = Login.GetLastYearGP(intLoginID);
            Login gpLastMonth = Login.GetLastMonthGP(intLoginID);

            JsonObject jsn = new JsonObject();
            JsonObject jsnItems = new JsonObject(true);

            if (gpThisYear != null)
            {
                //this year
                double openCost = (double)gpThisYear.OpenLandedCost;
                double openSalesValue = (double)gpThisYear.OpenSalesValue;
                double shippedCost = (double)gpThisYear.ShippedLandedCost - (isIncludeCredit ? (double)gpThisYear.ShippedCostCredit : 0);
                double shippedSalesValue = (double)gpThisYear.ShippedSalesValue - (isIncludeCredit ? (double)gpThisYear.ShippedCostCredit : 0);
                double openGPValue = openSalesValue - openCost;
                double shippedGPValue = shippedSalesValue - shippedCost;
                double totalSalesValue = openSalesValue + shippedSalesValue;
                double totalCost = openCost + shippedCost;
                double totalGPValue = openGPValue + shippedGPValue;
                double openGPPercentage;
                if (openGPValue > 0)
                {
                    openGPPercentage = (openGPValue / openSalesValue) * 100;
                }
                else
                {
                    openGPPercentage = 0;
                }
                double shippedGPPercentage;
                if (shippedGPValue > 0)
                {
                    shippedGPPercentage = (shippedGPValue / shippedSalesValue) * 100;
                }
                else
                {
                    shippedGPPercentage = 0;
                }
                double totalGPPercentage;
                if (totalGPValue > 0)
                {
                    totalGPPercentage = (totalGPValue / totalSalesValue) * 100;
                }
                else
                {
                    totalGPPercentage = 0;
                }
                JsonObject jsnItem = new JsonObject();
                jsnItem.AddVariable("GPDetail", "Open");
                jsnItem.AddVariable("SalesValue", Functions.FormatCurrency(openSalesValue, 2));
                jsnItem.AddVariable("Cost", Functions.FormatCurrency(openCost, 2));
                string s = String.Format("{0} ({1})", Functions.FormatCurrency(openGPValue, 2), Functions.FormatPercentage(openGPPercentage));
                jsnItem.AddVariable("GP", s);
                jsnItems.AddVariable(jsnItem);
                jsnItem.Dispose();
                jsnItem = null;
                jsnItem = new JsonObject();
                jsnItem.AddVariable("GPDetail", "Shipped");
                jsnItem.AddVariable("SalesValue", Functions.FormatCurrency(shippedSalesValue, 2));
                jsnItem.AddVariable("Cost", Functions.FormatCurrency(shippedCost, 2));
                s = String.Format("{0} ({1})", Functions.FormatCurrency(shippedGPValue, 2), Functions.FormatPercentage(shippedGPPercentage));
                jsnItem.AddVariable("GP", s);
                jsnItems.AddVariable(jsnItem);
                jsnItem.Dispose();
                jsnItem = null;
                jsnItem = new JsonObject();
                jsnItem.AddVariable("GPDetail", "Total");
                jsnItem.AddVariable("SalesValue", Functions.FormatCurrency(totalSalesValue, 2));
                jsnItem.AddVariable("Cost", Functions.FormatCurrency(totalCost, 2));
                s = String.Format("{0} ({1})", Functions.FormatCurrency(totalGPValue, 2), Functions.FormatPercentage(totalGPPercentage));
                jsnItem.AddVariable("GP", s);
                jsnItems.AddVariable(jsnItem);
                jsnItem.Dispose();
                jsnItem = null;
            }
            jsn.AddVariable("StatsTY", jsnItems);
            jsnItems.Dispose();
            jsnItems = null;

            jsnItems = new JsonObject(true);
            if (gpThisMonth != null)
            {
                //this month
                double openCost = (double)gpThisMonth.OpenLandedCost;
                double openSalesValue = (double)gpThisMonth.OpenSalesValue;
                double shippedCost = (double)gpThisMonth.ShippedLandedCost - (isIncludeCredit ? (double)gpThisMonth.ShippedCostCredit : 0);
                double shippedSalesValue = (double)gpThisMonth.ShippedSalesValue - (isIncludeCredit ? (double)gpThisMonth.ShippedSalesValueCredit : 0);
                double openGPValue = openSalesValue - openCost;
                double shippedGPValue = shippedSalesValue - shippedCost;
                double totalSalesValue = openSalesValue + shippedSalesValue;
                double totalCost = openCost + shippedCost;
                double totalGPValue = openGPValue + shippedGPValue;
                double openGPPercentage;
                if (openGPValue > 0)
                {
                    openGPPercentage = (openGPValue / openSalesValue) * 100;
                }
                else
                {
                    openGPPercentage = 0;
                }
                double shippedGPPercentage;
                if (shippedGPValue > 0)
                {
                    shippedGPPercentage = (shippedGPValue / shippedSalesValue) * 100;
                }
                else
                {
                    shippedGPPercentage = 0;
                }
                double totalGPPercentage;
                if (totalGPValue > 0)
                {
                    totalGPPercentage = (totalGPValue / totalSalesValue) * 100;
                }
                else
                {
                    totalGPPercentage = 0;
                }
                JsonObject jsnItem = new JsonObject();
                jsnItem.AddVariable("GPDetail", "Open");
                jsnItem.AddVariable("SalesValue", Functions.FormatCurrency(openSalesValue, 2));
                jsnItem.AddVariable("Cost", Functions.FormatCurrency(openCost, 2));
                string s = String.Format("{0} ({1})", Functions.FormatCurrency(openGPValue, 2), Functions.FormatPercentage(openGPPercentage));
                jsnItem.AddVariable("GP", s);
                jsnItems.AddVariable(jsnItem);
                jsnItem.Dispose();
                jsnItem = null;
                jsnItem = new JsonObject();
                jsnItem.AddVariable("GPDetail", "Shipped");
                jsnItem.AddVariable("SalesValue", Functions.FormatCurrency(shippedSalesValue, 2));
                jsnItem.AddVariable("Cost", Functions.FormatCurrency(shippedCost, 2));
                s = String.Format("{0} ({1})", Functions.FormatCurrency(shippedGPValue, 2), Functions.FormatPercentage(shippedGPPercentage));
                jsnItem.AddVariable("GP", s);
                jsnItems.AddVariable(jsnItem);
                jsnItem.Dispose();
                jsnItem = null;
                jsnItem = new JsonObject();
                jsnItem.AddVariable("GPDetail", "Total");
                jsnItem.AddVariable("SalesValue", Functions.FormatCurrency(totalSalesValue, 2));
                jsnItem.AddVariable("Cost", Functions.FormatCurrency(totalCost, 2));
                s = String.Format("{0} ({1})", Functions.FormatCurrency(totalGPValue, 2), Functions.FormatPercentage(totalGPPercentage));
                jsnItem.AddVariable("GP", s);
                jsnItems.AddVariable(jsnItem);
                jsnItem.Dispose();
                jsnItem = null;
            }
            else
            {
                jsnItems.AddVariable(ZeroJSONItem("GPDetail", "Open", "SalesValue", "Cost", "GP"));
                jsnItems.AddVariable(ZeroJSONItem("GPDetail", "Shipped", "SalesValue", "Cost", "GP"));
                jsnItems.AddVariable(ZeroJSONItem("GPDetail", "Total", "SalesValue", "Cost", "GP"));
            }
            jsn.AddVariable("StatsTM", jsnItems);
            jsnItems.Dispose();
            jsnItems = null;

            jsnItems = new JsonObject(true);
            if (gpNextMonth != null)
            {
                //next month
                double openCost = (double)gpNextMonth.OpenLandedCost;
                double openSalesValue = (double)gpNextMonth.OpenSalesValue;
                double shippedCost = (double)gpNextMonth.ShippedLandedCost - (isIncludeCredit ? (double)gpNextMonth.ShippedCostCredit : 0);
                double shippedSalesValue = (double)gpNextMonth.ShippedSalesValue - (isIncludeCredit ? (double)gpNextMonth.ShippedSalesValueCredit : 0);
                double openGPValue = openSalesValue - openCost;
                double shippedGPValue = shippedSalesValue - shippedCost;
                double totalSalesValue = openSalesValue + shippedSalesValue;
                double totalCost = openCost + shippedCost;
                double totalGPValue = openGPValue + shippedGPValue;
                double openGPPercentage;
                if (openGPValue > 0)
                {
                    openGPPercentage = (openGPValue / openSalesValue) * 100;
                }
                else
                {
                    openGPPercentage = 0;
                }
                double shippedGPPercentage;
                if (shippedGPValue > 0)
                {
                    shippedGPPercentage = (shippedGPValue / shippedSalesValue) * 100;
                }
                else
                {
                    shippedGPPercentage = 0;
                }
                double totalGPPercentage;
                if (totalGPValue > 0)
                {
                    totalGPPercentage = (totalGPValue / totalSalesValue) * 100;
                }
                else
                {
                    totalGPPercentage = 0;
                }
                JsonObject jsnItem = new JsonObject();
                jsnItem.AddVariable("GPDetail", "Open");
                jsnItem.AddVariable("SalesValue", Functions.FormatCurrency(openSalesValue, 2));
                jsnItem.AddVariable("Cost", Functions.FormatCurrency(openCost, 2));
                string s = String.Format("{0} ({1})", Functions.FormatCurrency(openGPValue, 2), Functions.FormatPercentage(openGPPercentage));
                jsnItem.AddVariable("GP", s);
                jsnItems.AddVariable(jsnItem);
                jsnItem.Dispose();
                jsnItem = null;
                jsnItem = new JsonObject();
                jsnItem.AddVariable("GPDetail", "Shipped");
                jsnItem.AddVariable("SalesValue", Functions.FormatCurrency(shippedSalesValue, 2));
                jsnItem.AddVariable("Cost", Functions.FormatCurrency(shippedCost, 2));
                s = String.Format("{0} ({1})", Functions.FormatCurrency(shippedGPValue, 2), Functions.FormatPercentage(shippedGPPercentage));
                jsnItem.AddVariable("GP", s);
                jsnItems.AddVariable(jsnItem);
                jsnItem.Dispose();
                jsnItem = null;
                jsnItem = new JsonObject();
                jsnItem.AddVariable("GPDetail", "Total");
                jsnItem.AddVariable("SalesValue", Functions.FormatCurrency(totalSalesValue, 2));
                jsnItem.AddVariable("Cost", Functions.FormatCurrency(totalCost, 2));
                s = String.Format("{0} ({1})", Functions.FormatCurrency(totalGPValue, 2), Functions.FormatPercentage(totalGPPercentage));
                jsnItem.AddVariable("GP", s);
                jsnItems.AddVariable(jsnItem);
                jsnItem.Dispose();
                jsnItem = null;
            }
            else
            {
                jsnItems.AddVariable(ZeroJSONItem("GPDetail", "Open", "SalesValue", "Cost", "GP"));
                jsnItems.AddVariable(ZeroJSONItem("GPDetail", "Shipped", "SalesValue", "Cost", "GP"));
                jsnItems.AddVariable(ZeroJSONItem("GPDetail", "Total", "SalesValue", "Cost", "GP"));
            }
            jsn.AddVariable("StatsNM", jsnItems);
            jsnItems.Dispose();
            jsnItems = null;

            jsnItems = new JsonObject(true);
            if (gpLastYear != null)
            {
                //last year
                double openCost = (double)gpLastYear.OpenLandedCost;
                double openSalesValue = (double)gpLastYear.OpenSalesValue;
                double shippedCost = (double)gpLastYear.ShippedLandedCost - (isIncludeCredit ? (double)gpLastYear.ShippedCostCredit : 0);
                double shippedSalesValue = (double)gpLastYear.ShippedSalesValue - (isIncludeCredit ? (double)gpLastYear.ShippedSalesValueCredit : 0);
                double openGPValue = openSalesValue - openCost;
                double shippedGPValue = shippedSalesValue - shippedCost;
                double totalSalesValue = openSalesValue + shippedSalesValue;
                double totalCost = openCost + shippedCost;
                double totalGPValue = openGPValue + shippedGPValue;
                double openGPPercentage;
                if (openGPValue > 0)
                {
                    openGPPercentage = (openGPValue / openSalesValue) * 100;
                }
                else
                {
                    openGPPercentage = 0;
                }
                double shippedGPPercentage;
                if (shippedGPValue > 0)
                {
                    shippedGPPercentage = (shippedGPValue / shippedSalesValue) * 100;
                }
                else
                {
                    shippedGPPercentage = 0;
                }
                double totalGPPercentage;
                if (totalGPValue > 0)
                {
                    totalGPPercentage = (totalGPValue / totalSalesValue) * 100;
                }
                else
                {
                    totalGPPercentage = 0;
                }
                JsonObject jsnItem = new JsonObject();
                jsnItem.AddVariable("GPDetail", "Open");
                jsnItem.AddVariable("SalesValue", Functions.FormatCurrency(openSalesValue, 2));
                jsnItem.AddVariable("Cost", Functions.FormatCurrency(openCost, 2));
                string s = String.Format("{0} ({1})", Functions.FormatCurrency(openGPValue, 2), Functions.FormatPercentage(openGPPercentage));
                jsnItem.AddVariable("GP", s);
                jsnItems.AddVariable(jsnItem);
                jsnItem.Dispose();
                jsnItem = null;
                jsnItem = new JsonObject();
                jsnItem.AddVariable("GPDetail", "Shipped");
                jsnItem.AddVariable("SalesValue", Functions.FormatCurrency(shippedSalesValue, 2));
                jsnItem.AddVariable("Cost", Functions.FormatCurrency(shippedCost, 2));
                s = String.Format("{0} ({1})", Functions.FormatCurrency(shippedGPValue, 2), Functions.FormatPercentage(shippedGPPercentage));
                jsnItem.AddVariable("GP", s);
                jsnItems.AddVariable(jsnItem);
                jsnItem.Dispose();
                jsnItem = null;
                jsnItem = new JsonObject();
                jsnItem.AddVariable("GPDetail", "Total");
                jsnItem.AddVariable("SalesValue", Functions.FormatCurrency(totalSalesValue, 2));
                jsnItem.AddVariable("Cost", Functions.FormatCurrency(totalCost, 2));
                s = String.Format("{0} ({1})", Functions.FormatCurrency(totalGPValue, 2), Functions.FormatPercentage(totalGPPercentage));
                jsnItem.AddVariable("GP", s);
                jsnItems.AddVariable(jsnItem);
                jsnItem.Dispose();
                jsnItem = null;
            }
            else
            {
                jsnItems.AddVariable(ZeroJSONItem("GPDetail", "Open", "SalesValue", "Cost", "GP"));
                jsnItems.AddVariable(ZeroJSONItem("GPDetail", "Shipped", "SalesValue", "Cost", "GP"));
                jsnItems.AddVariable(ZeroJSONItem("GPDetail", "Total", "SalesValue", "Cost", "GP"));
            }
            jsn.AddVariable("StatsLY", jsnItems);
            jsnItems.Dispose();
            jsnItems = null;

            jsnItems = new JsonObject(true);
            if (gpLastMonth != null)
            {
                //last month
                double openCost = (double)gpLastMonth.OpenLandedCost;
                double openSalesValue = (double)gpLastMonth.OpenSalesValue;
                double shippedCost = (double)gpLastMonth.ShippedLandedCost - (isIncludeCredit ? (double)gpLastMonth.ShippedCostCredit : 0);
                double shippedSalesValue = (double)gpLastMonth.ShippedSalesValue - (isIncludeCredit ? (double)gpLastMonth.ShippedSalesValueCredit : 0);
                double openGPValue = openSalesValue - openCost;
                double shippedGPValue = shippedSalesValue - shippedCost;
                double totalSalesValue = openSalesValue + shippedSalesValue;
                double totalCost = openCost + shippedCost;
                double totalGPValue = openGPValue + shippedGPValue;
                double openGPPercentage;
                if (openGPValue > 0)
                {
                    openGPPercentage = (openGPValue / openSalesValue) * 100;
                }
                else
                {
                    openGPPercentage = 0;
                }
                double shippedGPPercentage;
                if (shippedGPValue > 0)
                {
                    shippedGPPercentage = (shippedGPValue / shippedSalesValue) * 100;
                }
                else
                {
                    shippedGPPercentage = 0;
                }
                double totalGPPercentage;
                if (totalGPValue > 0)
                {
                    totalGPPercentage = (totalGPValue / totalSalesValue) * 100;
                }
                else
                {
                    totalGPPercentage = 0;
                }
                JsonObject jsnItem = new JsonObject();
                jsnItem.AddVariable("GPDetail", "Open");
                jsnItem.AddVariable("SalesValue", Functions.FormatCurrency(openSalesValue, 2));
                jsnItem.AddVariable("Cost", Functions.FormatCurrency(openCost, 2));
                string s = String.Format("{0} ({1})", Functions.FormatCurrency(openGPValue, 2), Functions.FormatPercentage(openGPPercentage));
                jsnItem.AddVariable("GP", s);
                jsnItems.AddVariable(jsnItem);
                jsnItem.Dispose();
                jsnItem = null;
                jsnItem = new JsonObject();
                jsnItem.AddVariable("GPDetail", "Shipped");
                jsnItem.AddVariable("SalesValue", Functions.FormatCurrency(shippedSalesValue, 2));
                jsnItem.AddVariable("Cost", Functions.FormatCurrency(shippedCost, 2));
                s = String.Format("{0} ({1})", Functions.FormatCurrency(shippedGPValue, 2), Functions.FormatPercentage(shippedGPPercentage));
                jsnItem.AddVariable("GP", s);
                jsnItems.AddVariable(jsnItem);
                jsnItem.Dispose();
                jsnItem = null;
                jsnItem = new JsonObject();
                jsnItem.AddVariable("GPDetail", "Total");
                jsnItem.AddVariable("SalesValue", Functions.FormatCurrency(totalSalesValue, 2));
                jsnItem.AddVariable("Cost", Functions.FormatCurrency(totalCost, 2));
                s = String.Format("{0} ({1})", Functions.FormatCurrency(totalGPValue, 2), Functions.FormatPercentage(totalGPPercentage));
                jsnItem.AddVariable("GP", s);
                jsnItems.AddVariable(jsnItem);
                jsnItem.Dispose();
                jsnItem = null;
            }
            else
            {
                jsnItems.AddVariable(ZeroJSONItem("GPDetail", "Open", "SalesValue", "Cost", "GP"));
                jsnItems.AddVariable(ZeroJSONItem("GPDetail", "Shipped", "SalesValue", "Cost", "GP"));
                jsnItems.AddVariable(ZeroJSONItem("GPDetail", "Total", "SalesValue", "Cost", "GP"));
            }
            jsn.AddVariable("StatsLM", jsnItems);
            jsnItems.Dispose();
            jsnItems = null;

            OutputResult(jsn);
            jsn.Dispose();
            jsn = null;
            //}

            gpThisYear = null;
            gpThisMonth = null;
            gpNextMonth = null;
            gpLastYear = null;
            gpLastMonth = null;
        }

        /// <summary>
        /// Gets the main data with shipping charges and freight
        /// </summary>
        private void GetDataWithShipping()
        {
            int intLoginID = LoginID;
            bool isIncludeCredit = false;
            if (GetFormValue_Int("OtherLoginID") > 0) intLoginID = GetFormValue_Int("OtherLoginID");
            isIncludeCredit = GetFormValue_Boolean("includeCredit");

            Login gpThisYear = Login.GetThisYearGP(intLoginID);
            Login gpThisMonth = Login.GetThisMonthGP(intLoginID);
            Login gpNextMonth = Login.GetNextMonthGP(intLoginID);
            Login gpLastYear = Login.GetLastYearGP(intLoginID);
            Login gpLastMonth = Login.GetLastMonthGP(intLoginID);
            //if (gpThisYear == null && gpThisMonth == null && gpNextMonth == null && gpLastYear == null && gpLastMonth == null) {
            //  WriteErrorDataNotFound();
            //} else {
            JsonObject jsn = new JsonObject();
            JsonObject jsnItems = new JsonObject(true);

            if (gpThisYear != null)
            {
                //this year
                double openCost = (double)gpThisYear.OpenLandedCost + (double)gpThisYear.OpenShippingCost;
                double openSalesValue = (double)gpThisYear.OpenSalesValue + (double)gpThisYear.OpenFreightCharge;
                double shippedCost = (double)gpThisYear.ShippedLandedCost + (double)gpThisYear.ShippedShippingCost - (isIncludeCredit ? (double)gpThisYear.ShippedCostCredit : 0);
                double shippedSalesValue = (double)gpThisYear.ShippedSalesValue + (double)gpThisYear.ShippedFreightCharge - (isIncludeCredit ? (double)gpThisYear.ShippedSalesValueCredit : 0);
                double openGPValue = openSalesValue - openCost;
                double shippedGPValue = shippedSalesValue - shippedCost;
                double totalSalesValue = openSalesValue + shippedSalesValue;
                double totalCost = openCost + shippedCost;
                double totalGPValue = openGPValue + shippedGPValue;
                double openGPPercentage;
                if (openGPValue > 0)
                {
                    openGPPercentage = (openGPValue / openSalesValue) * 100;
                }
                else
                {
                    openGPPercentage = 0;
                }
                double shippedGPPercentage;
                if (shippedGPValue > 0)
                {
                    shippedGPPercentage = (shippedGPValue / shippedSalesValue) * 100;
                }
                else
                {
                    shippedGPPercentage = 0;
                }
                double totalGPPercentage;
                if (totalGPValue > 0)
                {
                    totalGPPercentage = (totalGPValue / totalSalesValue) * 100;
                }
                else
                {
                    totalGPPercentage = 0;
                }
                JsonObject jsnItem = new JsonObject();
                jsnItem.AddVariable("GPDetail", "Open");
                jsnItem.AddVariable("SalesValue", Functions.FormatCurrency(openSalesValue, 2));
                jsnItem.AddVariable("Cost", Functions.FormatCurrency(openCost, 2));
                string s = String.Format("{0} ({1})", Functions.FormatCurrency(openGPValue, 2), Functions.FormatPercentage(openGPPercentage));
                jsnItem.AddVariable("GP", s);
                jsnItems.AddVariable(jsnItem);
                jsnItem.Dispose();
                jsnItem = null;
                jsnItem = new JsonObject();
                jsnItem.AddVariable("GPDetail", "Shipped");
                jsnItem.AddVariable("SalesValue", Functions.FormatCurrency(shippedSalesValue, 2));
                jsnItem.AddVariable("Cost", Functions.FormatCurrency(shippedCost, 2));
                s = String.Format("{0} ({1})", Functions.FormatCurrency(shippedGPValue, 2), Functions.FormatPercentage(shippedGPPercentage));
                jsnItem.AddVariable("GP", s);
                jsnItems.AddVariable(jsnItem);
                jsnItem.Dispose();
                jsnItem = null;
                jsnItem = new JsonObject();
                jsnItem.AddVariable("GPDetail", "Total");
                jsnItem.AddVariable("SalesValue", Functions.FormatCurrency(totalSalesValue, 2));
                jsnItem.AddVariable("Cost", Functions.FormatCurrency(totalCost, 2));
                s = String.Format("{0} ({1})", Functions.FormatCurrency(totalGPValue, 2), Functions.FormatPercentage(totalGPPercentage));
                jsnItem.AddVariable("GP", s);
                jsnItems.AddVariable(jsnItem);
                jsnItem.Dispose();
                jsnItem = null;
            }
            jsn.AddVariable("StatsTY", jsnItems);
            jsnItems.Dispose();
            jsnItems = null;

            jsnItems = new JsonObject(true);
            if (gpThisMonth != null)
            {
                //this month
                double openCost = (double)gpThisMonth.OpenLandedCost + (double)gpThisMonth.OpenShippingCost;
                double openSalesValue = (double)gpThisMonth.OpenSalesValue + (double)gpThisMonth.OpenFreightCharge;
                double shippedCost = (double)gpThisMonth.ShippedLandedCost + (double)gpThisMonth.ShippedShippingCost - (isIncludeCredit ? (double)gpThisMonth.ShippedCostCredit : 0);
                double shippedSalesValue = (double)gpThisMonth.ShippedSalesValue + (double)gpThisMonth.ShippedFreightCharge - (isIncludeCredit ? (double)gpThisMonth.ShippedSalesValueCredit : 0);
                double openGPValue = openSalesValue - openCost;
                double shippedGPValue = shippedSalesValue - shippedCost;
                double totalSalesValue = openSalesValue + shippedSalesValue;
                double totalCost = openCost + shippedCost;
                double totalGPValue = openGPValue + shippedGPValue;
                double openGPPercentage;
                if (openGPValue > 0)
                {
                    openGPPercentage = (openGPValue / openSalesValue) * 100;
                }
                else
                {
                    openGPPercentage = 0;
                }
                double shippedGPPercentage;
                if (shippedGPValue > 0)
                {
                    shippedGPPercentage = (shippedGPValue / shippedSalesValue) * 100;
                }
                else
                {
                    shippedGPPercentage = 0;
                }
                double totalGPPercentage;
                if (totalGPValue > 0)
                {
                    totalGPPercentage = (totalGPValue / totalSalesValue) * 100;
                }
                else
                {
                    totalGPPercentage = 0;
                }
                JsonObject jsnItem = new JsonObject();
                jsnItem.AddVariable("GPDetail", "Open");
                jsnItem.AddVariable("SalesValue", Functions.FormatCurrency(openSalesValue, 2));
                jsnItem.AddVariable("Cost", Functions.FormatCurrency(openCost, 2));
                string s = String.Format("{0} ({1})", Functions.FormatCurrency(openGPValue, 2), Functions.FormatPercentage(openGPPercentage));
                jsnItem.AddVariable("GP", s);
                jsnItems.AddVariable(jsnItem);
                jsnItem.Dispose();
                jsnItem = null;
                jsnItem = new JsonObject();
                jsnItem.AddVariable("GPDetail", "Shipped");
                jsnItem.AddVariable("SalesValue", Functions.FormatCurrency(shippedSalesValue, 2));
                jsnItem.AddVariable("Cost", Functions.FormatCurrency(shippedCost, 2));
                s = String.Format("{0} ({1})", Functions.FormatCurrency(shippedGPValue, 2), Functions.FormatPercentage(shippedGPPercentage));
                jsnItem.AddVariable("GP", s);
                jsnItems.AddVariable(jsnItem);
                jsnItem.Dispose();
                jsnItem = null;
                jsnItem = new JsonObject();
                jsnItem.AddVariable("GPDetail", "Total");
                jsnItem.AddVariable("SalesValue", Functions.FormatCurrency(totalSalesValue, 2));
                jsnItem.AddVariable("Cost", Functions.FormatCurrency(totalCost, 2));
                s = String.Format("{0} ({1})", Functions.FormatCurrency(totalGPValue, 2), Functions.FormatPercentage(totalGPPercentage));
                jsnItem.AddVariable("GP", s);
                jsnItems.AddVariable(jsnItem);
                jsnItem.Dispose();
                jsnItem = null;
            }
            else
            {
                jsnItems.AddVariable(ZeroJSONItem("GPDetail", "Open", "SalesValue", "Cost", "GP"));
                jsnItems.AddVariable(ZeroJSONItem("GPDetail", "Shipped", "SalesValue", "Cost", "GP"));
                jsnItems.AddVariable(ZeroJSONItem("GPDetail", "Total", "SalesValue", "Cost", "GP"));
            }
            jsn.AddVariable("StatsTM", jsnItems);
            jsnItems.Dispose();
            jsnItems = null;

            jsnItems = new JsonObject(true);
            if (gpNextMonth != null)
            {
                //next month
                double openCost = (double)gpNextMonth.OpenLandedCost + (double)gpNextMonth.OpenShippingCost;
                double openSalesValue = (double)gpNextMonth.OpenSalesValue + (double)gpNextMonth.OpenFreightCharge;
                double shippedCost = (double)gpNextMonth.ShippedLandedCost + (double)gpNextMonth.ShippedShippingCost - (isIncludeCredit ? (double)gpNextMonth.ShippedCostCredit : 0);
                double shippedSalesValue = (double)gpNextMonth.ShippedSalesValue + (double)gpNextMonth.ShippedFreightCharge - (isIncludeCredit ? (double)gpNextMonth.ShippedSalesValueCredit : 0);
                double openGPValue = openSalesValue - openCost;
                double shippedGPValue = shippedSalesValue - shippedCost;
                double totalSalesValue = openSalesValue + shippedSalesValue;
                double totalCost = openCost + shippedCost;
                double totalGPValue = openGPValue + shippedGPValue;
                double openGPPercentage;
                if (openGPValue > 0)
                {
                    openGPPercentage = (openGPValue / openSalesValue) * 100;
                }
                else
                {
                    openGPPercentage = 0;
                }
                double shippedGPPercentage;
                if (shippedGPValue > 0)
                {
                    shippedGPPercentage = (shippedGPValue / shippedSalesValue) * 100;
                }
                else
                {
                    shippedGPPercentage = 0;
                }
                double totalGPPercentage;
                if (totalGPValue > 0)
                {
                    totalGPPercentage = (totalGPValue / totalSalesValue) * 100;
                }
                else
                {
                    totalGPPercentage = 0;
                }
                JsonObject jsnItem = new JsonObject();
                jsnItem.AddVariable("GPDetail", "Open");
                jsnItem.AddVariable("SalesValue", Functions.FormatCurrency(openSalesValue, 2));
                jsnItem.AddVariable("Cost", Functions.FormatCurrency(openCost, 2));
                string s = String.Format("{0} ({1})", Functions.FormatCurrency(openGPValue, 2), Functions.FormatPercentage(openGPPercentage));
                jsnItem.AddVariable("GP", s);
                jsnItems.AddVariable(jsnItem);
                jsnItem.Dispose();
                jsnItem = null;
                jsnItem = new JsonObject();
                jsnItem.AddVariable("GPDetail", "Shipped");
                jsnItem.AddVariable("SalesValue", Functions.FormatCurrency(shippedSalesValue, 2));
                jsnItem.AddVariable("Cost", Functions.FormatCurrency(shippedCost, 2));
                s = String.Format("{0} ({1})", Functions.FormatCurrency(shippedGPValue, 2), Functions.FormatPercentage(shippedGPPercentage));
                jsnItem.AddVariable("GP", s);
                jsnItems.AddVariable(jsnItem);
                jsnItem.Dispose();
                jsnItem = null;
                jsnItem = new JsonObject();
                jsnItem.AddVariable("GPDetail", "Total");
                jsnItem.AddVariable("SalesValue", Functions.FormatCurrency(totalSalesValue, 2));
                jsnItem.AddVariable("Cost", Functions.FormatCurrency(totalCost, 2));
                s = String.Format("{0} ({1})", Functions.FormatCurrency(totalGPValue, 2), Functions.FormatPercentage(totalGPPercentage));
                jsnItem.AddVariable("GP", s);
                jsnItems.AddVariable(jsnItem);
                jsnItem.Dispose();
                jsnItem = null;
            }
            else
            {
                jsnItems.AddVariable(ZeroJSONItem("GPDetail", "Open", "SalesValue", "Cost", "GP"));
                jsnItems.AddVariable(ZeroJSONItem("GPDetail", "Shipped", "SalesValue", "Cost", "GP"));
                jsnItems.AddVariable(ZeroJSONItem("GPDetail", "Total", "SalesValue", "Cost", "GP"));
            }
            jsn.AddVariable("StatsNM", jsnItems);
            jsnItems.Dispose();
            jsnItems = null;

            jsnItems = new JsonObject(true);
            if (gpLastYear != null)
            {
                //last year
                double openCost = (double)gpLastYear.OpenLandedCost + (double)gpLastYear.OpenShippingCost;
                double openSalesValue = (double)gpLastYear.OpenSalesValue + (double)gpLastYear.OpenFreightCharge;
                double shippedCost = (double)gpLastYear.ShippedLandedCost + (double)gpLastYear.ShippedShippingCost - (isIncludeCredit ? (double)gpLastYear.ShippedCostCredit : 0);
                double shippedSalesValue = (double)gpLastYear.ShippedSalesValue + (double)gpLastYear.ShippedFreightCharge - (isIncludeCredit ? (double)gpLastYear.ShippedSalesValueCredit : 0);
                double openGPValue = openSalesValue - openCost;
                double shippedGPValue = shippedSalesValue - shippedCost;
                double totalSalesValue = openSalesValue + shippedSalesValue;
                double totalCost = openCost + shippedCost;
                double totalGPValue = openGPValue + shippedGPValue;
                double openGPPercentage;
                if (openGPValue > 0)
                {
                    openGPPercentage = (openGPValue / openSalesValue) * 100;
                }
                else
                {
                    openGPPercentage = 0;
                }
                double shippedGPPercentage;
                if (shippedGPValue > 0)
                {
                    shippedGPPercentage = (shippedGPValue / shippedSalesValue) * 100;
                }
                else
                {
                    shippedGPPercentage = 0;
                }
                double totalGPPercentage;
                if (totalGPValue > 0)
                {
                    totalGPPercentage = (totalGPValue / totalSalesValue) * 100;
                }
                else
                {
                    totalGPPercentage = 0;
                }
                JsonObject jsnItem = new JsonObject();
                jsnItem.AddVariable("GPDetail", "Open");
                jsnItem.AddVariable("SalesValue", Functions.FormatCurrency(openSalesValue, 2));
                jsnItem.AddVariable("Cost", Functions.FormatCurrency(openCost, 2));
                string s = String.Format("{0} ({1})", Functions.FormatCurrency(openGPValue, 2), Functions.FormatPercentage(openGPPercentage));
                jsnItem.AddVariable("GP", s);
                jsnItems.AddVariable(jsnItem);
                jsnItem.Dispose();
                jsnItem = null;
                jsnItem = new JsonObject();
                jsnItem.AddVariable("GPDetail", "Shipped");
                jsnItem.AddVariable("SalesValue", Functions.FormatCurrency(shippedSalesValue, 2));
                jsnItem.AddVariable("Cost", Functions.FormatCurrency(shippedCost, 2));
                s = String.Format("{0} ({1})", Functions.FormatCurrency(shippedGPValue, 2), Functions.FormatPercentage(shippedGPPercentage));
                jsnItem.AddVariable("GP", s);
                jsnItems.AddVariable(jsnItem);
                jsnItem.Dispose();
                jsnItem = null;
                jsnItem = new JsonObject();
                jsnItem.AddVariable("GPDetail", "Total");
                jsnItem.AddVariable("SalesValue", Functions.FormatCurrency(totalSalesValue, 2));
                jsnItem.AddVariable("Cost", Functions.FormatCurrency(totalCost, 2));
                s = String.Format("{0} ({1})", Functions.FormatCurrency(totalGPValue, 2), Functions.FormatPercentage(totalGPPercentage));
                jsnItem.AddVariable("GP", s);
                jsnItems.AddVariable(jsnItem);
                jsnItem.Dispose();
                jsnItem = null;
            }
            else
            {
                jsnItems.AddVariable(ZeroJSONItem("GPDetail", "Open", "SalesValue", "Cost", "GP"));
                jsnItems.AddVariable(ZeroJSONItem("GPDetail", "Shipped", "SalesValue", "Cost", "GP"));
                jsnItems.AddVariable(ZeroJSONItem("GPDetail", "Total", "SalesValue", "Cost", "GP"));
            }
            jsn.AddVariable("StatsLY", jsnItems);
            jsnItems.Dispose();
            jsnItems = null;

            jsnItems = new JsonObject(true);
            if (gpLastMonth != null)
            {
                //last month
                double openCost = (double)gpLastMonth.OpenLandedCost + (double)gpLastMonth.OpenShippingCost;
                double openSalesValue = (double)gpLastMonth.OpenSalesValue + (double)gpLastMonth.OpenFreightCharge;
                double shippedCost = (double)gpLastMonth.ShippedLandedCost + (double)gpLastMonth.ShippedShippingCost - (isIncludeCredit ? (double)gpLastMonth.ShippedCostCredit : 0);
                double shippedSalesValue = (double)gpLastMonth.ShippedSalesValue + (double)gpLastMonth.ShippedFreightCharge - (isIncludeCredit ? (double)gpLastMonth.ShippedSalesValueCredit : 0);
                double openGPValue = openSalesValue - openCost;
                double shippedGPValue = shippedSalesValue - shippedCost;
                double totalSalesValue = openSalesValue + shippedSalesValue;
                double totalCost = openCost + shippedCost;
                double totalGPValue = openGPValue + shippedGPValue;
                double openGPPercentage;
                if (openGPValue > 0)
                {
                    openGPPercentage = (openGPValue / openSalesValue) * 100;
                }
                else
                {
                    openGPPercentage = 0;
                }
                double shippedGPPercentage;
                if (shippedGPValue > 0)
                {
                    shippedGPPercentage = (shippedGPValue / shippedSalesValue) * 100;
                }
                else
                {
                    shippedGPPercentage = 0;
                }
                double totalGPPercentage;
                if (totalGPValue > 0)
                {
                    totalGPPercentage = (totalGPValue / totalSalesValue) * 100;
                }
                else
                {
                    totalGPPercentage = 0;
                }
                JsonObject jsnItem = new JsonObject();
                jsnItem.AddVariable("GPDetail", "Open");
                jsnItem.AddVariable("SalesValue", Functions.FormatCurrency(openSalesValue, 2));
                jsnItem.AddVariable("Cost", Functions.FormatCurrency(openCost, 2));
                string s = String.Format("{0} ({1})", Functions.FormatCurrency(openGPValue, 2), Functions.FormatPercentage(openGPPercentage));
                jsnItem.AddVariable("GP", s);
                jsnItems.AddVariable(jsnItem);
                jsnItem.Dispose();
                jsnItem = null;
                jsnItem = new JsonObject();
                jsnItem.AddVariable("GPDetail", "Shipped");
                jsnItem.AddVariable("SalesValue", Functions.FormatCurrency(shippedSalesValue, 2));
                jsnItem.AddVariable("Cost", Functions.FormatCurrency(shippedCost, 2));
                s = String.Format("{0} ({1})", Functions.FormatCurrency(shippedGPValue, 2), Functions.FormatPercentage(shippedGPPercentage));
                jsnItem.AddVariable("GP", s);
                jsnItems.AddVariable(jsnItem);
                jsnItem.Dispose();
                jsnItem = null;
                jsnItem = new JsonObject();
                jsnItem.AddVariable("GPDetail", "Total");
                jsnItem.AddVariable("SalesValue", Functions.FormatCurrency(totalSalesValue, 2));
                jsnItem.AddVariable("Cost", Functions.FormatCurrency(totalCost, 2));
                s = String.Format("{0} ({1})", Functions.FormatCurrency(totalGPValue, 2), Functions.FormatPercentage(totalGPPercentage));
                jsnItem.AddVariable("GP", s);
                jsnItems.AddVariable(jsnItem);
                jsnItem.Dispose();
                jsnItem = null;
            }
            else
            {
                jsnItems.AddVariable(ZeroJSONItem("GPDetail", "Open", "SalesValue", "Cost", "GP"));
                jsnItems.AddVariable(ZeroJSONItem("GPDetail", "Shipped", "SalesValue", "Cost", "GP"));
                jsnItems.AddVariable(ZeroJSONItem("GPDetail", "Total", "SalesValue", "Cost", "GP"));
            }
            jsn.AddVariable("StatsLM", jsnItems);
            jsnItems.Dispose();
            jsnItems = null;

            OutputResult(jsn);
            jsn.Dispose();
            jsn = null;
            //}

            gpThisYear = null;
            gpThisMonth = null;
            gpNextMonth = null;
            gpLastYear = null;
            gpLastMonth = null;
        }

        private JsonObject ZeroJSONItem(string strVar1, string strValue1, string strVar2, string strVar3, string strVar4)
        {
            JsonObject jsnItem = new JsonObject();
            jsnItem.AddVariable(strVar1, strValue1);
            jsnItem.AddVariable(strVar2, Functions.FormatCurrency(0, 2));
            jsnItem.AddVariable(strVar3, Functions.FormatCurrency(0, 2));
            jsnItem.AddVariable(strVar4, String.Format("{0} ({1})", Functions.FormatCurrency(0, 2), Functions.FormatPercentage(0)));
            return jsnItem;
        }

    }
}
