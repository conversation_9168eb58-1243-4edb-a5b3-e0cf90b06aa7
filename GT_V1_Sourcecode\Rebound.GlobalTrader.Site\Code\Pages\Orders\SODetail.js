Type.registerNamespace("Rebound.GlobalTrader.Site.Pages.Orders");Rebound.GlobalTrader.Site.Pages.Orders.SODetail=function(n){Rebound.GlobalTrader.Site.Pages.Orders.SODetail.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Pages.Orders.SODetail.prototype={get_intSOID:function(){return this._intSOID},set_intSOID:function(n){this._intSOID!==n&&(this._intSOID=n)},get_ctlMainInfo:function(){return this._ctlMainInfo},set_ctlMainInfo:function(n){this._ctlMainInfo!==n&&(this._ctlMainInfo=n)},get_ctlAuthorisation:function(){return this._ctlAuthorisation},set_ctlAuthorisation:function(n){this._ctlAuthorisation!==n&&(this._ctlAuthorisation=n)},get_ctlLines:function(){return this._ctlLines},set_ctlLines:function(n){this._ctlLines!==n&&(this._ctlLines=n)},get_btnPrint:function(){return this._btnPrint},set_btnPrint:function(n){this._btnPrint!==n&&(this._btnPrint=n)},get_pnlStatus:function(){return this._pnlStatus},set_pnlStatus:function(n){this._pnlStatus!==n&&(this._pnlStatus=n)},get_lblStatus:function(){return this._lblStatus},set_lblStatus:function(n){this._lblStatus!==n&&(this._lblStatus=n)},get_ctlSODDocuments:function(){return this._ctlSODDocuments},set_ctlSODDocuments:function(n){this._ctlSODDocuments!==n&&(this._ctlSODDocuments=n)},get_ctlSORPDFDocuments:function(){return this._ctlSORPDFDocuments},set_ctlSORPDFDocuments:function(n){this._ctlSORPDFDocuments!==n&&(this._ctlSORPDFDocuments=n)},get_ctlSOPDFDragDrop:function(){return this._ctlSOPDFDragDrop},set_ctlSOPDFDragDrop:function(n){this._ctlSOPDFDragDrop!==n&&(this._ctlSOPDFDragDrop=n)},get_ctlSORPDFDocsDragDrop:function(){return this._ctlSORPDFDocsDragDrop},set_ctlSORPDFDocsDragDrop:function(n){this._ctlSORPDFDocsDragDrop!==n&&(this._ctlSORPDFDocsDragDrop=n)},get_ctlDragDropForSOR:function(){return this._ctlDragDropForSOR},set_ctlDragDropForSOR:function(n){this._ctlDragDropForSOR!==n&&(this._ctlDragDropForSOR=n)},get_lblSoStatus:function(){return this._lblSoStatus},set_lblSoStatus:function(n){this._lblSoStatus!==n&&(this._lblSoStatus=n)},get_pnlConsolidateStatus:function(){return this._pnlConsolidateStatus},set_pnlConsolidateStatus:function(n){this._pnlConsolidateStatus!==n&&(this._pnlConsolidateStatus=n)},get_IsGlobalLogin:function(){return this._IsGlobalLogin},set_IsGlobalLogin:function(n){this._IsGlobalLogin!==n&&(this._IsGlobalLogin=n)},get_ctlSOShowPaymentFiles:function(){return this._ctlSOShowPaymentFiles},set_ctlSOShowPaymentFiles:function(n){this._ctlSOShowPaymentFiles!==n&&(this._ctlSOShowPaymentFiles=n)},get_ctlSOEXCELDocFileDragDrop:function(){return this._ctlSOEXCELDocFileDragDrop},set_ctlSOEXCELDocFileDragDrop:function(n){this._ctlSOEXCELDocFileDragDrop!==n&&(this._ctlSOEXCELDocFileDragDrop=n)},initialize:function(){Rebound.GlobalTrader.Site.Pages.Orders.SODetail.callBaseMethod(this,"initialize")},goInit:function(){this._btnPrint&&(this._btnPrint.addPrint(Function.createDelegate(this,this.printSO)),this._btnPrint.addEmail(Function.createDelegate(this,this.emailSO)),this._btnPrint.addExtraButtonClick(Function.createDelegate(this,this.printOtherDocs)));this._ctlAuthorisation&&this._ctlAuthorisation.addPotentialStatusChange(Function.createDelegate(this,this.ctlAuthorisation_PotentialStatusChange));this._ctlLines&&(this._ctlLines.addPotentialStatusChange(Function.createDelegate(this,this.ctlLines_PotentialStatusChange)),this._ctlLines.addRefreshChange(Function.createDelegate(this,this.ctlLines_RefreshChange)));this._ctlMainInfo&&(this._ctlMainInfo.addGetDataComplete(Function.createDelegate(this,this.ctlMainInfo_GetDataComplete)),this._ctlMainInfo.addSaveEditComplete(Function.createDelegate(this,this.ctlMainInfo_SaveEditComplete)),this._ctlMainInfo.addPotentialStatusChange(Function.createDelegate(this,this.ctlMainInfo_PotentialStatusChange)),this._ctlMainInfo.addPotentialStatus(Function.createDelegate(this,this.ctlMainInfo_ValidateChange)),this._ctlMainInfo.addPotentialStatusCon(Function.createDelegate(this,this.ctlMainInfo_UpdateConsolidate)),this.setLineDetailsFromMainInfo());this._ctlSODDocuments&&this._ctlSODDocuments.getData();this._ctlSORPDFDocuments&&this._ctlSORPDFDocuments.getData();this._ctlSOPDFDragDrop&&this._ctlSOPDFDragDrop.getData();this._ctlDragDropForSOR&&this._ctlDragDropForSOR.getData();this._ctlMainInfo._frmEdit&&this._ctlAuthorisation&&this._ctlMainInfo._frmEdit.addSave(Function.createDelegate(this,this.frmEditSaveClicked));this._ctlAuthorisation&&this._ctlAuthorisation.addGetDataComplete(Function.createDelegate(this,this.ctlAuthorisation_GetDataComplete));this._ctlSORPDFDocsDragDrop&&this._ctlSORPDFDocsDragDrop.addGetDataComplete(Function.createDelegate(this,this.ctlSORPDFDocsDragDrop_GetDataComplete));this._ctlSORPDFDocsDragDrop&&this._ctlSORPDFDocsDragDrop.getData();this._ctlSOShowPaymentFiles&&this._ctlSOShowPaymentFiles.getData();this._ctlSOEXCELDocFileDragDrop&&this._ctlSOEXCELDocFileDragDrop.getData();Rebound.GlobalTrader.Site.Pages.Orders.SODetail.callBaseMethod(this,"goInit")},dispose:function(){this.isDisposed||(this._btnPrint&&this._btnPrint.dispose(),this._ctlMainInfo&&this._ctlMainInfo.dispose(),this._ctlAuthorisation&&this._ctlAuthorisation.dispose(),this._ctlLines&&this._ctlLines.dispose(),this._ctlMainInfo=null,this._ctlAuthorisation=null,this._ctlLines=null,this._lblStatus=null,this._pnlStatus=null,this._intSOID=null,this._ctlSODDocuments&&this._ctlSODDocuments.dispose(),this._ctlSODDocuments=null,this._ctlSORPDFDocuments&&this._ctlSORPDFDocuments.dispose(),this._ctlSORPDFDocuments=null,this._ctlSORPDFDocsDragDrop&&this._ctlSORPDFDocsDragDrop.dispose(),this._ctlSORPDFDocsDragDrop=null,this._ctlDragDropForSOR&&this._ctlDragDropForSOR.dispose(),this._ctlDragDropForSOR=null,this._pnlConsolidateStatus=null,this._lblSoStatus=null,this._IsGlobalLogin=null,this._ctlSOShowPaymentFiles=null,this._ctlSOEXCELDocFileDragDrop&&this._ctlSOEXCELDocFileDragDrop.dispose(),this._ctlSOEXCELDocFileDragDrop=null,Rebound.GlobalTrader.Site.Pages.Orders.SODetail.callBaseMethod(this,"dispose"))},ConsolidatePrintSO:function(){this._ctlMainInfo.showConfirmForm(0)},printSO:function(){this._ctlMainInfo._Consolidated=="0"?$R_FN.openPrintWindow($R_ENUM$PrintObject.SalesOrder,this._intSOID):this._ctlMainInfo._Consolidated=="1"?$R_FN.openPrintWindow($R_ENUM$PrintObject.ConsolidatePrintSO,this._intSOID):this.ConsolidatePrintSO();this._ctlMainInfo._ibtnSentOrder&&$R_IBTN.enableButton(this._ctlMainInfo._ibtnSentOrder,!1);this._ctlMainInfo.set_ctlIncludeSentOrder(!0)},ConsolidateEmailSO:function(){this._ctlMainInfo.showConfirmForm(1)},emailSO:function(){this._ctlMainInfo._Consolidated=="0"?$R_FN.openPrintWindow($R_ENUM$PrintObject.SalesOrder,this._intSOID,!0):this._ctlMainInfo._Consolidated=="1"?$R_FN.openPrintWindow($R_ENUM$PrintObject.ConsolidateEmailSO,this._intSOID,!0):this.ConsolidateEmailSO()},printOtherDocs:function(){this._btnPrint._strExtraButtonClickCommand=="PrintProFormaInvoice"&&(this._ctlMainInfo._Consolidated=="0"?$R_FN.openPrintWindow($R_ENUM$PrintObject.ProFormaInvoice,this._intSOID):this._ctlMainInfo._Consolidated=="1"?$R_FN.openPrintWindow($R_ENUM$PrintObject.ProFormaInvoiceCon,this._intSOID):this._ctlMainInfo.showConfirmForm(2),this._ctlMainInfo._ibtnSentOrder&&$R_IBTN.enableButton(this._ctlMainInfo._ibtnSentOrder,!1),this._ctlMainInfo.set_ctlIncludeSentOrder(!0));this._btnPrint._strExtraButtonClickCommand=="EmailProFormaInvoice"&&(this._ctlMainInfo._Consolidated=="0"?$R_FN.openPrintWindow($R_ENUM$PrintObject.ProFormaInvoice,this._intSOID,!0):this._ctlMainInfo._Consolidated=="1"?$R_FN.openPrintWindow($R_ENUM$PrintObject.ProFormaInvoiceCon,this._intSOID,!0):this._ctlMainInfo.showConfirmForm(3));this._btnPrint._strExtraButtonClickCommand=="PrintSOReport"&&($R_FN.openPrintWindow($R_ENUM$PrintObject.SOReport,this._intSOID),this._ctlMainInfo._ibtnSentOrder&&$R_IBTN.enableButton(this._ctlMainInfo._ibtnSentOrder,!1),this._ctlMainInfo.set_ctlIncludeSentOrder(!0));this._btnPrint._strExtraButtonClickCommand=="ConsolidatePrintSO"&&($R_FN.openPrintWindow($R_ENUM$PrintObject.ConsolidatePrintSO,this._intSOID),this._ctlMainInfo._ibtnSentOrder&&$R_IBTN.enableButton(this._ctlMainInfo._ibtnSentOrder,!1),this._ctlMainInfo.set_ctlIncludeSentOrder(!0));this._btnPrint._strExtraButtonClickCommand=="ConsolidateEmailSO"&&($R_FN.openPrintWindow($R_ENUM$PrintObject.ConsolidateEmailSO,this._intSOID,!0),this._ctlMainInfo._ibtnSentOrder&&$R_IBTN.enableButton(this._ctlMainInfo._ibtnSentOrder,!1),this._ctlMainInfo.set_ctlIncludeSentOrder(!0));this._btnPrint._strExtraButtonClickCommand=="PrintLog"&&$R_FN.openPrintLogWindow($R_ENUM$PrintObject.PrintLog,this._intSOID,!1,"SalesOrder");this._btnPrint._strExtraButtonClickCommand=="ExportToExcel"&&this.exportClicked();this._btnPrint._strExtraButtonClickCommand=="CustomTemplate"&&this.customTemplate()},customTemplate:function(){var n=this._ctlMainInfo.getFieldValue("hidFirstQuoteId");$R_FN.openCustomTemplateWindow($R_ENUM$PrintObject.Quote,n)},ctlMainInfo_GetDataComplete:function(){$R_FN.setInnerHTML(this._lblStatus,this._ctlMainInfo.getFieldValue("hidStatus"));this._ctlLines._intGlobalClientNo=this._IsGlobalLogin==!0?this._ctlMainInfo.getFieldValue("hidGlobalClientNo"):null;this._ctlMainInfo._IsGlobalLogin=this._IsGlobalLogin;this._ctlSOShowPaymentFiles.getData();this.setLineDetailsFromMainInfo()},ctlMainInfo_SaveEditComplete:function(){this._ctlLines._blnAS9120=this._ctlMainInfo.getFieldValue("ctlAS9120");$R_IBTN.enableButton(this._ctlLines._ibtnLineWarehouse,!1);this._ctlLines.getTabData()},setLineDetailsFromMainInfo:function(){var n=this._ctlMainInfo.getFieldValue("hidNo"),e=this._ctlMainInfo.getFieldValue("hidCustomerNo"),t=this._ctlMainInfo.getFieldValue("hidCustomer"),i=this._ctlMainInfo.getFieldValue("hidCurrencyCode"),r=this._ctlMainInfo.getFieldValue("hidCurrencyNo"),u=this._ctlMainInfo.getFieldValue("ctlDateOrdered"),f;this._ctlAuthorisation&&(this._ctlAuthorisation._blnCompanyOnStop=this._ctlMainInfo._blnCompanyOnStop,this._ctlAuthorisation._blnSOComplete=this._ctlMainInfo._blnSOComplete,this._ctlAuthorisation.enableAuthoriseButtons(!0));f=Number.parseInvariant(this._ctlMainInfo.getFieldValue("hidStatusNo").toString());this._ctlLines&&(this._ctlLines._blnSOComplete=f==$R_ENUM$SalesOrderStatus.Complete,this._ctlLines._blnSOAuthorised=this._ctlMainInfo._blnIsAuthorised,this._ctlLines.enableEditButtons(!0),this._ctlLines._frmEdit&&this._ctlLines._frmEdit.setFieldsFromSalesOrder(n,t,i),this._ctlLines._frmAdd&&this._ctlLines._frmAdd.setFieldsFromSalesOrder(n,t,i,r,u),this._ctlLines._frmPost&&this._ctlLines._frmPost.setFieldsFromSalesOrder(n,t),this._ctlLines._frmDelete&&this._ctlLines._frmDelete.setFieldsFromSalesOrder(n,t),this._ctlLines._frmDeallocate&&this._ctlLines._frmDeallocate.setFieldsFromSalesOrder(n,t),this._ctlLines._frmAllocate&&this._ctlLines._frmAllocate.setFieldsFromSalesOrder(i,r,u),this._ctlLines._frmClose&&this._ctlLines._frmClose.setFieldsFromSalesOrder(n,t),this._ctlLines._frmAdd&&this._ctlLines._frmAdd.setIncludeLockLotCustNo(e),this._ctlLines._frmEditAll&&this._ctlLines._frmEditAll.setFieldsFromSalesOrder(n,t,i));this._ctlLines._blnAS9120=this._ctlMainInfo.getFieldValue("ctlAS9120")},ctlMainInfo_PotentialStatusChange:function(){this._ctlLines._blnAS9120=this._ctlMainInfo.getFieldValue("ctlAS9120");this._ctlLines.getTabData();this._ctlAuthorisation.getData();this.getCreditBalance()},ctlMainInfo_ValidateChange:function(){this._ctlMainInfo._intValid=this._ctlLines.validateTotalPrice()?!0:!1},ctlAuthorisation_PotentialStatusChange:function(){this._ctlMainInfo.getData();this._ctlLines.getTabData();this.getCreditBalance();this._ctlSORPDFDocsDragDrop&&(this._ctlSORPDFDocsDragDrop._blnIsAuthorise=this._ctlAuthorisation._blnIsAuthorised)},ctlLines_PotentialStatusChange:function(){this._ctlMainInfo.getData();this._ctlAuthorisation.getData();this.getCreditBalance();this.setMainInfoFromLine()},frmEditSaveClicked:function(){this._ctlMainInfo._frmEdit._IsFrieght=!0;this.validateFrieght()},validateFrieght:function(){var n=this.calculateFreight();n>=parseFloat(this._ctlAuthorisation.getFieldValue("hidCreditBalance"))+(this._ctlLines._anyLinePosted?parseFloat(this._ctlLines._totalFreight):0)&&(this._ctlMainInfo._frmEdit._IsFrieght=!1)},ctlAuthorisation_GetDataComplete:function(){this.getCreditBalance();this._ctlDragDropForSOR&&this._ctlAuthorisation._blnIsAuthorised&&this._ctlDragDropForSOR.getData();this._ctlSORPDFDocsDragDrop&&(this._ctlSORPDFDocsDragDrop._blnIsAuthorise=this._ctlAuthorisation._blnIsAuthorised);this._ctlLines.showHideAdd()},getCreditBalance:function(){this._ctlLines&&this._ctlAuthorisation&&(this._ctlLines.setCreditBalance(this._ctlAuthorisation.getFieldValue("hidCreditBalance")<=0?0:this._ctlAuthorisation.getFieldValue("hidCreditBalance")),this._ctlMainInfo.setCreditBalance(this._ctlAuthorisation.getFieldValue("hidCreditBalance")<=0?0:this._ctlAuthorisation.getFieldValue("hidCreditBalance")))},calculateFreight:function(){return parseFloat(this._ctlMainInfo._frmEdit.getFieldValue("ctlFreight"))+parseFloat(this._ctlMainInfo._frmEdit.getFieldValue("ctlFreight"))*parseFloat(this._ctlLines._taxRate)},setMainInfoFromLine:function(){this._ctlMainInfo&&(this._ctlMainInfo._blnLineContainData=this._ctlLines._blnContainLine)},ctlLines_RefreshChange:function(){this.setMainInfoFromLine();this._ctlMainInfo.disableCreateIPO(this._ctlLines._blnAllIPOCreated)},ctlSORPDFDocsDragDrop_GetDataComplete:function(){this._ctlAuthorisation&&this._ctlAuthorisation.getData()},ctlMainInfo_UpdateConsolidate:function(){this._ctlMainInfo._Consolidated=="1"?$R_FN.setInnerHTML(this._lblSoStatus,"Consolidate lines"):this._ctlMainInfo._Consolidated=="0"?$R_FN.setInnerHTML(this._lblSoStatus,"Actual lines"):$R_FN.setInnerHTML(this._lblSoStatus,"None")},exportClicked:function(){var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/SOMainInfo");n.set_DataObject("SOMainInfo");n.set_DataAction("ExportSalesOrderReport");n.addParameter("id",this._intSOID);n._intTimeoutMilliseconds=9e4;n.addDataOK(Function.createDelegate(this,this.exportComplete));n.addError(Function.createDelegate(this,this.exportError));n.addTimeout(Function.createDelegate(this,this.exportError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},exportError:function(n){this.showError(!0,n.get_ErrorMessage())},exportComplete:function(n){if(n._result.Result==1){var t=new Date,i=!1;i=this.UrlExists(window.location.origin+"/"+n._result.FileURL);i==!0&&(location.href=String.format("{0}?t={1}",n._result.FileURL,t.getTime()));t=null;this._ctlMainInfo.getData()}},UrlExists:function(n){var t=new XMLHttpRequest;t.open("HEAD",n,!1);try{t.send()}catch(i){}return t.status!=404}};Rebound.GlobalTrader.Site.Pages.Orders.SODetail.registerClass("Rebound.GlobalTrader.Site.Pages.Orders.SODetail",Rebound.GlobalTrader.Site.Pages.Content,Sys.IDisposable);