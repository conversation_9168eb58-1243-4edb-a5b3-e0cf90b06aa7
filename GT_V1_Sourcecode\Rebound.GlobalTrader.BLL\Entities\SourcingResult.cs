﻿//Marker     changed by      date         Remarks
//[001]      Aashu          07/06/2018     Added supplier warranty field
//[002]      Aashu          20/06/2018     [REB-11754]: MSL level
//[003]      <PERSON><PERSON><PERSON>    16/08/2018     REB-12322 : A tick box to recomond test the parts from HUB side.
//[004]      Ahinav Saxena  14/07/2021     Add new property for the partwatchmatch
//[005]      Bhooma&Sunil   11/08/2021     Added method for delete req partwatch match
//[006]      Abhinav <PERSON>a 07-09-2021     Add notification logic for the manual partwatch adding.
//[007]      Abhinav <PERSON>xena 10-09-2021     Remove partwatch from manual adding sourcing result
//[008]      Abhinav <PERSON>xena 04-10-2021      Add new flag for different client
//[009]      Soorya Vyas    14-04-2023   [RP-1421] add revers logistic similar to strategic offer on HUBRFQ page
//[010]      Ravi Bhushan   12-09-2023   RP-2340 AS6081
//[011]      <PERSON>   27-09-2023   RP-2427 - AS6081 Adding new line items for Sales Order, Purchase Orders, Quote, Invoice and Stock using multiple sources
//-----------------------------------------------------------------------------------------
using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;
using Rebound.GlobalTrader.DAL;
using Rebound.GlobalTrader.BLL;

namespace Rebound.GlobalTrader.BLL
{
    public partial class SourcingResult : BizObject
    {

        #region Properties

        protected static DAL.SourcingResultElement Settings
        {
            get { return Globals.Settings.SourcingResults; }
        }
        public System.String ChangeNotes { get; set; }
        /// <summary>
        /// SourcingResultId
        /// </summary>
        public System.Int32 SourcingResultId { get; set; }
        /// <summary>
        /// CustomerRequirementNo
        /// </summary>
        public System.Int32 CustomerRequirementNo { get; set; }
        /// <summary>
        /// SourcingTable
        /// </summary>
        public System.String SourcingTable { get; set; }
        /// <summary>
        /// SourcingTableItemNo
        /// </summary>
        public System.Int32? SourcingTableItemNo { get; set; }
        /// <summary>
        /// TypeName
        /// </summary>
        public System.String TypeName { get; set; }
        /// <summary>
        /// FullPart
        /// </summary>
        public System.String FullPart { get; set; }
        /// <summary>
        /// Part
        /// </summary>
        public System.String Part { get; set; }
        /// <summary>
        /// ManufacturerNo
        /// </summary>
        public System.Int32? ManufacturerNo { get; set; }
        /// <summary>
        /// DateCode
        /// </summary>
        public System.String DateCode { get; set; }
        /// <summary>
        /// ProductNo
        /// </summary>
        public System.Int32? ProductNo { get; set; }
        /// <summary>
        /// PackageNo
        /// </summary>
        public System.Int32? PackageNo { get; set; }
        /// <summary>
        /// Quantity
        /// </summary>
        public System.Int32 Quantity { get; set; }
        /// <summary>
        /// Price
        /// </summary>
        //public System.Double Price { get; set; }
        public double? Price { get; set; }

        /// <summary>
        /// CurrencyNo
        /// </summary>
        public System.Int32 CurrencyNo { get; set; }
        /// <summary>
        /// OriginalEntryDate
        /// </summary>
        public System.DateTime? OriginalEntryDate { get; set; }
        /// <summary>
        /// Salesman
        /// </summary>
        public System.Int32 Salesman { get; set; }
        /// <summary>
        /// SupplierNo
        /// </summary>
        public System.Int32? SupplierNo { get; set; }
        /// <summary>
        /// UpdatedBy
        /// </summary>
        public System.Int32? UpdatedBy { get; set; }
        /// <summary>
        /// DLUP
        /// </summary>
        public System.DateTime DLUP { get; set; }
        /// <summary>
        /// ROHS
        /// </summary>
        public System.Byte? ROHS { get; set; }
        /// <summary>
        /// OfferStatusNo
        /// </summary>
        public System.Int32? OfferStatusNo { get; set; }
        /// <summary>
        /// OfferStatusChangeDate
        /// </summary>
        public System.DateTime? OfferStatusChangeDate { get; set; }
        /// <summary>
        /// OfferStatusChangeLoginNo
        /// </summary>
        public System.Int32? OfferStatusChangeLoginNo { get; set; }
        /// <summary>
        /// Notes
        /// </summary>
        public System.String Notes { get; set; }
        /// <summary>
        /// CurrencyCode
        /// </summary>
        public System.String CurrencyCode { get; set; }
        /// <summary>
        /// CustomerRequirementId
        /// </summary>
        public System.Int32? CustomerRequirementId { get; set; }
        /// <summary>
        /// CustomerRequirementNumber
        /// </summary>
        public System.Int32? CustomerRequirementNumber { get; set; }
        /// <summary>
        /// ClientNo
        /// </summary>
        public System.Int32? ClientNo { get; set; }
        /// <summary>
        /// CompanyNo
        /// </summary>
        public System.Int32? CompanyNo { get; set; }
        /// <summary>
        /// CompanyName
        /// </summary>
        public System.String CompanyName { get; set; }
        /// <summary>
        /// SupplierName
        /// </summary>
        public System.String SupplierName { get; set; }
        /// <summary>
        /// ManufacturerCode
        /// </summary>
        public System.String ManufacturerCode { get; set; }
        /// <summary>
        /// ProductName
        /// </summary>
        public System.String ProductName { get; set; }
        /// <summary>
        /// PackageName
        /// </summary>
        public System.String PackageName { get; set; }
        /// <summary>
        /// OfferStatusChangeEmployeeName
        /// </summary>
        public System.String OfferStatusChangeEmployeeName { get; set; }
        /// <summary>
        /// RowNum
        /// </summary>
        public System.Int64? RowNum { get; set; }
        /// <summary>
        /// RowCnt
        /// </summary>
        public System.Int32? RowCnt { get; set; }
        /// <summary>
        /// ManufacturerName
        /// </summary>
        public System.String ManufacturerName { get; set; }
        /// <summary>
        /// CustomerPart
        /// </summary>
        public System.String CustomerPart { get; set; }
        /// <summary>
        /// ProductDescription
        /// </summary>
        public System.String ProductDescription { get; set; }
        /// <summary>
        /// PackageDescription
        /// </summary>
        public System.String PackageDescription { get; set; }
        /// <summary>
        /// SalesmanName
        /// </summary>
        public System.String SalesmanName { get; set; }
        public System.Double? SupplierPrice { get; set; }
        public System.String POHubSupplierName { get; set; }
        public System.Int32? POHubCompanyNo { get; set; }
        public System.String IsPoHub { get; set; }
        public System.Int32? POHubReleaseBy { get; set; }

        public System.String ClientSupplierName { get; set; }
        public System.Int32? ClientCompanyNo { get; set; }
        /// <summary>
        /// UPLiftPrice
        /// </summary>
        public System.Double? UPLiftPrice { get; set; }
        public System.Int32 ClientCurrencyNo { get; set; }
        public System.String ClientCurrencyCode { get; set; }
        public System.Double? ConvertedSourcingPrice { get; set; }
        public System.String MslSpqFactorySealed { get; set; }
        public double? EstimatedShippingCost { get; set; }
        public double? ActualPrice { get; set; }
        public double? SupplierPercentage { get; set; }

        public string SupplierManufacturerName { get; set; }
        public string SupplierDateCode { get; set; }
        public string SupplierPackageType { get; set; }
        public string SupplierProductType { get; set; }
        public string SupplierMOQ { get; set; }
        public string SupplierTotalQSA { get; set; }
        public string SupplierLTB { get; set; }
        public string SupplierNotes { get; set; }
        public string SupplierType { get; set; }
        public DateTime? DeliveryDate { get; set; }
        public System.Boolean? SourcingRelease { get; set; }
        public bool IsClosed { get; set; }
        public string SPQ { get; set; }
        public string LeadTime { get; set; }
        public string ROHSStatus { get; set; }
        public string FactorySealed { get; set; }
        public string MSL { get; set; }
        public string RegionName { get; set; }
        public System.Int32? RegionNo { get; set; }

        public string HubRFQName { get; set; }
        public System.Int32? HubRFQNo { get; set; }
        public bool IsSoCreated { get; set; }
        public string TermsName { get; set; }
        public bool IsApplyPOBankFee { get; set; }
        public string SourceRef { get; set; }
        public bool IsReleased { get; set; }
        public bool Recalled { get; set; }
        public string SourcingNotes { get; set; }
        public double? OriginalPrice { get; set; }
        public System.Int32? ActualCurrencyNo { get; set; }
        public System.String ActualCurrencyCode { get; set; }
        public System.Int32 SourcingReleasedCount { get; set; }
        public System.Int32? MSLLevelNo { get; set; }
        public System.String MSLLevelText { get; set; }

        //[001] start
        /// <summary>
        /// SupplierWarranty
        /// </summary>
        public System.Int32? SupplierWarranty { get; set; }
        /// <summary>
        /// NonPreferredCompany
        /// </summary>
        public System.Boolean? NonPreferredCompany { get; set; }
        //[001] end
        //[003] start
        public System.Boolean IsTestingRecommended { get; set; }
        //[003] end
        public System.Boolean? IsImageAvailable { get; set; }
        /// <summary>
        /// PriorityId (from Table)
        /// </summary>
        public System.Int32 PriorityId { get; set; }
        /// <summary>
        /// PriorityNo 
        /// </summary>
        public System.Int32 PriorityNo { get; set; }
        public System.Int32 IHSCountryOfOriginNo { get; set; }
        public System.String IHSCountryOfOriginName { get; set; }
        public System.Int32 CountryOfOriginNo { get; set; }
        public System.String CountryOfOriginName { get; set; }
        public System.Int32? ReReleased { get; set; }
        public System.Boolean? PartWatchMatch { get; set; }
        public System.Boolean? DiffrentClientOffer { get; set; }

        public System.String ClientCode { get; set; }
        public System.String ROHSDescription { get; set; }
        public System.Boolean? PartWatchMatchHUBIPO { get; set; }
        public System.Boolean? IsPartWatchMatchClient { get; set; }
        public System.String SourceClient { get; set; }
        public System.Int32? SourceClientNo { get; set; }
        public System.Boolean? Status1 { get; set; }
        public System.Boolean? Status2 { get; set; }
        public System.Boolean? HeaderFlag { get; set; }
        public System.Int32 LineNumber { get; set; }
        public System.Boolean? ISAS6081Required { get; set; }
        public System.Int32? TypeOfSupplierNo { get; set; }
        public System.Int32? ReasonForSupplierNo { get; set; }
        public System.Int32? RiskOfSupplierNo { get; set; }
        public System.String TypeOfSupplierName { get; set; }
        public System.String ReasonForSupplierName { get; set; }
        public System.String RiskOfSupplierName { get; set; }
        public System.String AssigneeId { get; set; }
        public System.Boolean? IsCountryFound { get; set; }
        public System.String CountryName { get; set; }
        public System.Int32 CountryNo { get; set; }
        public System.Int32 SourceId { get; set; }
        public System.String Reason { get; set; }
        public System.Boolean? MissingRequiredFeilds { get; set; }


        public System.Int32 BomId { get; set; }
        public System.Int32 PVVQuestionId { get; set; }
        public System.String PVVQuestionName { get; set; }
        public System.Int32 PVVAnswerId { get; set; }
        public System.String PVVAnswerName { get; set; }
        public System.Int32 PVVQuestionNo { get; set; }
        public System.String HUBRFQNo { get; set; }
        public System.String ReleaseNote { get; set; }
        public System.String SellPriceLessReason { get; set; }

        #endregion

        #region Methods

        /// <summary>
        /// Delete
        /// Calls [usp_delete_SourcingResult]
        /// </summary>
        public static bool Delete(System.String sourcingResultIds, System.Boolean? IsPOHub)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.SourcingResult.Delete(sourcingResultIds, IsPOHub);
        }
        /// <summary>
        /// Insert
        /// Calls [usp_insert_SourcingResult]
        /// </summary>
        public static Int32 Insert(System.Int32? customerRequirementNo, System.String typeName, System.String notes, System.String part, System.Int32? manufacturerNo, System.String dateCode, System.Int32? productNo, System.Int32? packageNo, System.Int32? quantity, System.Double? price, System.Int32? currencyNo, System.DateTime? originalEntryDate, System.Int32? salesman, System.Int32? offerStatusNo, System.Int32? supplierNo, System.Byte? rohs, System.Int32? clientNo, System.Int32? updatedBy, System.Int32? mslLevelNo, System.Boolean? blnSupHasCurrency, out System.Int32? offerID)
        {
            offerID = -1;
            Int32 objReturn = Rebound.GlobalTrader.DAL.SiteProvider.SourcingResult.Insert(customerRequirementNo, typeName, notes, part, manufacturerNo, dateCode, productNo, packageNo, quantity, price, currencyNo, originalEntryDate, salesman, offerStatusNo, supplierNo, rohs, clientNo, updatedBy, mslLevelNo, blnSupHasCurrency, out offerID);
            return objReturn;
        }

        /// <summary>
        /// Insert
        /// Calls [usp_insert_ProspectiveOffer_SourcingResult]
        /// </summary>
        public static Int32 InsertProspectiveSourcingResult(System.Int32? customerRequirementNo, System.Int32 offerNo, System.String part, System.Int32? manufacturerNo, System.String dateCode, System.Int32? productNo, System.Int32? packageNo, System.Int32? quantity,
             System.Int32? salesman, System.Int32? supplierNo, System.Int32? poCurrencyNo, System.Int32? poHubCompanyNo, System.Double? offerPriceFromProspective, out System.Int32? offerID)
        {
            offerID = -1;
            Int32 objReturn = Rebound.GlobalTrader.DAL.SiteProvider.SourcingResult.InsertProspectiveSourcingResult(customerRequirementNo, offerNo,  part, manufacturerNo, dateCode, productNo, packageNo, quantity,
             salesman, supplierNo, poCurrencyNo, poHubCompanyNo, offerPriceFromProspective, out offerID);
            return objReturn;
        }
        /// <summary>
        /// Insert (without parameters)
        /// Calls [usp_insert_SourcingResult]
        /// </summary>
        public Int32 Insert()
        {
            System.Int32? offerID = -1;
            return Rebound.GlobalTrader.DAL.SiteProvider.SourcingResult.Insert(CustomerRequirementNo, TypeName, Notes, Part, ManufacturerNo, DateCode, ProductNo, PackageNo, Quantity, Price, CurrencyNo, OriginalEntryDate, Salesman, OfferStatusNo, SupplierNo, ROHS, ClientNo, UpdatedBy, null, true, out offerID);
        }
        /// <summary>
        /// InsertFromHistory
        /// Calls [usp_insert_SourcingResult_From_History]
        /// </summary>
        public static Int32 InsertFromHistory(System.Int32? customerRequirementNo, System.Int32? historyNo, System.Int32? clientNo, System.Int32? updatedBy)
        {
            Int32 objReturn = Rebound.GlobalTrader.DAL.SiteProvider.SourcingResult.InsertFromHistory(customerRequirementNo, historyNo, clientNo, updatedBy);
            return objReturn;
        }
        /// <summary>
        /// InsertFromOffer
        /// Calls [usp_insert_SourcingResult_From_Offer]
        /// </summary>
        public static Int32 InsertFromOffer(System.Int32? customerRequirementNo, System.Int32? offerNo, System.Int32? updatedBy, System.Boolean isPOHub, out System.String strLinkMessage)
        {
            Int32 objReturn = Rebound.GlobalTrader.DAL.SiteProvider.SourcingResult.InsertFromOffer(customerRequirementNo, offerNo, updatedBy, isPOHub, out strLinkMessage);
            return objReturn;
        }
        
        /// <summary>
        /// InsertFromOffer
        /// Calls [usp_insert_SourcingResult_From_AltPart]
        /// </summary>
        public static Int32 InsertFromAltPart(System.Int32? customerRequirementNo, System.Int32? AltPartNo, System.Int32? updatedBy, System.Boolean isPOHub, out System.String strLinkMessage)
        {
            Int32 objReturn = Rebound.GlobalTrader.DAL.SiteProvider.SourcingResult.InsertFromAltPart(customerRequirementNo, AltPartNo, updatedBy, isPOHub, out strLinkMessage);
            return objReturn;
        }



        /// <summary>
        /// InsertFromOffer
        /// Calls [usp_insert_SourcingResult_From_Stock]
        /// </summary>
        public static Int32 InsertFromStock(System.Int32? customerRequirementNo, System.Int32? StockNo, System.Int32? updatedBy, System.Boolean isPOHub, out System.String strLinkMessage)
        {
            Int32 objReturn = Rebound.GlobalTrader.DAL.SiteProvider.SourcingResult.InsertFromStock(customerRequirementNo, StockNo, updatedBy, isPOHub, out strLinkMessage);
            return objReturn;
        }

        //EPO START
        /// <summary>
        /// InsertFromOffer
        /// Calls [usp_insert_SourcingResult_From_EpoPH]
        /// </summary>
        public static Int32 InsertFromEpo(System.Int32? customerRequirementNo, System.Int32? EpoNo, System.Int32? updatedBy, System.Boolean isPOHub, out System.String strLinkMessage)
        {
            Int32 objReturn = Rebound.GlobalTrader.DAL.SiteProvider.SourcingResult.InsertFromEpo(customerRequirementNo, EpoNo, updatedBy, isPOHub, out strLinkMessage);
            return objReturn;
        }
        //EPO ENd


        //ReverseLogistic START
        /// <summary>
        /// //[009] 
        /// InsertFromOffer
        /// Calls [usp_insert_SourcingResult_From_ReverseLogisticPH]
        /// </summary>
        public static Int32 InsertFromReverseLogistic(System.Int32? customerRequirementNo, System.Int32? ReverseLogisticNo, System.Int32? updatedBy, System.Boolean isPOHub, out System.String strLinkMessage)
        {
            Int32 objReturn = Rebound.GlobalTrader.DAL.SiteProvider.SourcingResult.InsertFromReverseLogistic(customerRequirementNo, ReverseLogisticNo, updatedBy, isPOHub, out strLinkMessage);
            return objReturn;
        }
        //ReverseLogistic End

        /// <summary>
        /// InsertOffer From CrossMatch
        /// Calls [usp_insert_SourcingResult_From_CrossMatch_Offer]
        /// </summary>
        public static Int32 InsertFromOfferCrossMatch(System.Int32? customerRequirementNo, System.Int32? offerNo, System.Int32? updatedBy, System.Boolean isPOHub, out System.String strLinkMessage)
        {
            Int32 objReturn = Rebound.GlobalTrader.DAL.SiteProvider.SourcingResult.InsertFromOfferCrossMatch(customerRequirementNo, offerNo, updatedBy, isPOHub, out strLinkMessage);
            return objReturn;
        }
        /// <summary>
        /// InsertFromTrusted
        /// Calls [usp_insert_SourcingResult_From_Trusted]
        /// </summary>
        public static Int32 InsertFromTrusted(System.Int32? customerRequirementNo, System.Int32? excessNo, System.Int32? updatedBy, System.Boolean isPOHub, out System.String strLinkMessage)
        {
            Int32 objReturn = Rebound.GlobalTrader.DAL.SiteProvider.SourcingResult.InsertFromTrusted(customerRequirementNo, excessNo, updatedBy, isPOHub, out strLinkMessage);
            return objReturn;
        }

        //code aded by anand cross march trusted
        /// <summary>
        /// InsertFromTrusted
        /// Calls [[usp_insert_SourcingResult_From_Trusted_CrossMatch]]
        /// </summary>
        public static Int32 InsertFromTrustedCrossMatch(System.Int32? customerRequirementNo, System.Int32? excessNo, System.Int32? updatedBy, System.Boolean isPOHub, out System.String strLinkMessage)
        {
            Int32 objReturn = Rebound.GlobalTrader.DAL.SiteProvider.SourcingResult.InsertFromTrustedCrossMatch(customerRequirementNo, excessNo, updatedBy, isPOHub, out strLinkMessage);
            return objReturn;
        }
        //end
        /// <summary>
        /// Release 
        /// Calls [usp_update_Sourcing_Release]
        /// </summary>
        public static bool ReleaseSourcing(System.Int32? sourcingResultID, System.Int32? updatedBy)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.SourcingResult.ReleaseSourcing(sourcingResultID, updatedBy);
        }


        /// <summary>
        /// Release 
        /// Calls [usp_update_Sourcing_Approval]
        /// </summary>
        public static bool ApproveSourcing(System.Int32? sourcingResultID, System.Int32? updatedBy)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.SourcingResult.ApproveSourcing(sourcingResultID, updatedBy);
        }


        /// <summary>
        /// InsertFromPurchaseQuote
        /// Calls [[usp_insert_SourcingResult_From_PurchaseRequest]]
        /// </summary>
        public static Int32 InsertFromPOQuote(System.Int32? customerRequirementNo, System.Int32? poQuoteLineNo, System.Int32? updatedBy, out System.String strLinkMessage)
        {
            Int32 objReturn = Rebound.GlobalTrader.DAL.SiteProvider.SourcingResult.InsertFromPOQuote(customerRequirementNo, poQuoteLineNo, updatedBy, out strLinkMessage);
            return objReturn;
        }
        //code added by anand for price request
        /// <summary>
        /// InsertFromPurchaseQuote Cross Match
        /// Calls [usp_insert_SourcingResult_From_PurchaseRequest_CrossMatch]
        /// </summary>
        public static Int32 InsertFromPOQuoteCrossMatch(System.Int32? customerRequirementNo, System.Int32? poQuoteLineNo, System.Int32? updatedBy, out System.String strLinkMessage)
        {
            Int32 objReturn = Rebound.GlobalTrader.DAL.SiteProvider.SourcingResult.InsertFromPOQuoteCrossMatch(customerRequirementNo, poQuoteLineNo, updatedBy, out strLinkMessage);
            return objReturn;
        }
        //code end
        /// <summary>
        /// ItemSearch
        /// Calls [usp_itemsearch_SourcingResult]
        /// </summary>
        public static List<SourcingResult> ItemSearch(System.Int32? clientId, System.Int32? orderBy, System.Int32? sortDir, System.Int32? pageIndex, System.Int32? pageSize, System.String partSearch, System.String cmSearch, System.Int32? customerRequirementNoLo, System.Int32? customerRequirementNoHi, System.String supplierSearch, System.Boolean? isPoHub, System.Int32? intQuoteID, System.String bom)
        {
            List<SourcingResultDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.SourcingResult.ItemSearch(clientId, orderBy, sortDir, pageIndex, pageSize, partSearch, cmSearch, customerRequirementNoLo, customerRequirementNoHi, supplierSearch, isPoHub, intQuoteID, bom);
            if (lstDetails == null)
            {
                return new List<SourcingResult>();
            }
            else
            {
                List<SourcingResult> lst = new List<SourcingResult>();
                foreach (SourcingResultDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.SourcingResult obj = new Rebound.GlobalTrader.BLL.SourcingResult();
                    obj.SourcingResultId = objDetails.SourcingResultId;
                    obj.Part = objDetails.Part;
                    obj.Price = objDetails.Price;
                    obj.Quantity = objDetails.Quantity;
                    obj.CurrencyCode = objDetails.CurrencyCode;
                    obj.CustomerRequirementId = objDetails.CustomerRequirementId;
                    obj.CustomerRequirementNumber = objDetails.CustomerRequirementNumber;
                    obj.ClientNo = objDetails.ClientNo;
                    obj.CompanyNo = objDetails.CompanyNo;
                    obj.CompanyName = objDetails.CompanyName;
                    obj.SupplierNo = objDetails.SupplierNo;
                    obj.SupplierName = objDetails.SupplierName;
                    obj.ManufacturerCode = objDetails.ManufacturerCode;
                    obj.ProductName = objDetails.ProductName;
                    obj.PackageName = objDetails.PackageName;
                    obj.OfferStatusChangeDate = objDetails.OfferStatusChangeDate;
                    obj.OfferStatusChangeEmployeeName = objDetails.OfferStatusChangeEmployeeName;
                    obj.DateCode = objDetails.DateCode;
                    obj.RowNum = objDetails.RowNum;
                    obj.RowCnt = objDetails.RowCnt;
                    obj.IsPoHub = objDetails.IsPoHub;
                    obj.ClientCompanyNo = objDetails.ClientCompanyNo;
                    obj.ClientSupplierName = objDetails.ClientSupplierName;
                    obj.ManufacturerNo = objDetails.ManufacturerNo;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }
        /// <summary>
        /// Get
        /// Calls [usp_select_SourcingResult]
        /// </summary>
        public static SourcingResult Get(System.Int32? sourcingResultId)
        {
            Rebound.GlobalTrader.DAL.SourcingResultDetails objDetails = Rebound.GlobalTrader.DAL.SiteProvider.SourcingResult.Get(sourcingResultId);
            if (objDetails == null)
            {
                return null;
            }
            else
            {
                SourcingResult obj = new SourcingResult();
                obj.SourcingResultId = objDetails.SourcingResultId;
                obj.CustomerRequirementNo = objDetails.CustomerRequirementNo;
                obj.SourcingTable = objDetails.SourcingTable;
                obj.SourcingTableItemNo = objDetails.SourcingTableItemNo;
                obj.TypeName = objDetails.TypeName;
                obj.FullPart = objDetails.FullPart;
                obj.Part = objDetails.Part;
                obj.ManufacturerNo = objDetails.ManufacturerNo;
                obj.DateCode = objDetails.DateCode;
                obj.ProductNo = objDetails.ProductNo;
                obj.PackageNo = objDetails.PackageNo;
                obj.Quantity = objDetails.Quantity;
                obj.Price = objDetails.Price;
                obj.CurrencyNo = objDetails.CurrencyNo;
                obj.OriginalEntryDate = objDetails.OriginalEntryDate;
                obj.Salesman = objDetails.Salesman;
                obj.SupplierNo = objDetails.SupplierNo;
                obj.UpdatedBy = objDetails.UpdatedBy;
                obj.DLUP = objDetails.DLUP;
                obj.ROHS = objDetails.ROHS;
                obj.OfferStatusNo = objDetails.OfferStatusNo;
                obj.OfferStatusChangeDate = objDetails.OfferStatusChangeDate;
                obj.OfferStatusChangeLoginNo = objDetails.OfferStatusChangeLoginNo;
                obj.Notes = objDetails.Notes;
                obj.SupplierName = objDetails.SupplierName;
                obj.ManufacturerName = objDetails.ManufacturerName;
                obj.ManufacturerCode = objDetails.ManufacturerCode;
                obj.CustomerPart = objDetails.CustomerPart;
                obj.SupplierPrice = objDetails.SupplierPrice;
                obj.POHubSupplierName = objDetails.POHubSupplierName;
                obj.POHubCompanyNo = objDetails.POHubCompanyNo;
                obj.UPLiftPrice = objDetails.UPLiftPrice;
                obj.ClientCompanyNo = objDetails.ClientCompanyNo;
                obj.ClientSupplierName = objDetails.ClientSupplierName;
                obj.EstimatedShippingCost = objDetails.EstimatedShippingCost;
                obj.ClientCurrencyNo = objDetails.ClientCurrencyNo;
                obj.ClientCurrencyCode = objDetails.ClientCurrencyCode;
                obj.ActualPrice = objDetails.ActualPrice;
                obj.SupplierPercentage = objDetails.SupplierPercentage;
                obj.DeliveryDate = objDetails.DeliveryDate;

                obj.SPQ = objDetails.SPQ;
                obj.LeadTime = objDetails.LeadTime;
                obj.ROHSStatus = objDetails.ROHSStatus;
                obj.FactorySealed = objDetails.FactorySealed;
                obj.MSL = objDetails.MSL;
                obj.SupplierMOQ = objDetails.SupplierMOQ;
                obj.SupplierTotalQSA = objDetails.SupplierTotalQSA;
                obj.SupplierLTB = objDetails.SupplierLTB;
                obj.SupplierNotes = objDetails.SupplierNotes;
                obj.ClientNo = objDetails.ClientNo;
                obj.RegionNo = objDetails.RegionNo;
                obj.SourcingNotes = objDetails.SourcingNotes;

                obj.CurrencyCode = objDetails.CurrencyCode;
                obj.ActualCurrencyCode = objDetails.ActualCurrencyCode;
                obj.ActualCurrencyNo = objDetails.ActualCurrencyNo;
                obj.OriginalPrice = objDetails.OriginalPrice;
                obj.ProductDescription = objDetails.ProductDescription;
                obj.ProductInactive = objDetails.ProductInactive;
                //[001] start
                obj.SupplierWarranty = objDetails.SupplierWarranty;
                obj.NonPreferredCompany = objDetails.NonPreferredCompany;
                //[001] end
                obj.MSLLevelNo = objDetails.MSLLevelNo;
                obj.MSLLevelText = objDetails.MSLLevelText;
                obj.PriorityNo = objDetails.PriorityNo;
                obj.IHSCountryOfOriginNo = objDetails.IHSCountryOfOriginNo;
                obj.IHSCountryOfOriginName = objDetails.IHSCountryOfOriginName;
                obj.CountryOfOriginNo = objDetails.CountryOfOriginNo;
                obj.PackageDescription = objDetails.PackageDescription;
                obj.PartWatchMatch = objDetails.PartWatchMatch;
                obj.PartWatchMatchHUBIPO = objDetails.PartWatchMatchHUBIPO;
                obj.TypeOfSupplierNo = objDetails.TypeOfSupplierNo;
                obj.ReasonForSupplierNo = objDetails.ReasonForSupplierNo;
                obj.RiskOfSupplierNo = objDetails.RiskOfSupplierNo;
                obj.ISAS6081Required = objDetails.ISAS6081Required; //[010]
                obj.IsCountryFound = objDetails.IsCountryFound;
                obj.CountryName = objDetails.CountryName;
                obj.CountryNo = objDetails.CountryNo;
                obj.SellPriceLessReason = objDetails.SellPriceLessReason;
                obj.IsTestingRecommended = objDetails.IsTestingRecommended;
                objDetails = null;
                return obj;
            }
        }
        /// <summary>
        /// GetListForCustomerRequirement
        /// Calls [usp_selectAll_SourcingResult_for_CustomerRequirement]
        /// </summary>
        public static List<SourcingResult> GetListForCustomerRequirement(System.Int32? customerRequirementId)
        {
            List<SourcingResultDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.SourcingResult.GetListForCustomerRequirement(customerRequirementId);
            if (lstDetails == null)
            {
                return new List<SourcingResult>();
            }
            else
            {
                List<SourcingResult> lst = new List<SourcingResult>();
                foreach (SourcingResultDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.SourcingResult obj = new Rebound.GlobalTrader.BLL.SourcingResult();
                    obj.SourcingResultId = objDetails.SourcingResultId;
                    obj.CustomerRequirementNo = objDetails.CustomerRequirementNo;
                    obj.SourcingTable = objDetails.SourcingTable;
                    obj.SourcingTableItemNo = objDetails.SourcingTableItemNo;
                    obj.TypeName = objDetails.TypeName;
                    obj.FullPart = objDetails.FullPart;
                    obj.Part = objDetails.Part;
                    obj.ManufacturerNo = objDetails.ManufacturerNo;
                    obj.DateCode = objDetails.DateCode;
                    obj.ProductNo = objDetails.ProductNo;
                    obj.PackageNo = objDetails.PackageNo;
                    obj.Quantity = objDetails.Quantity;
                    obj.Price = objDetails.Price;
                    obj.CurrencyNo = objDetails.CurrencyNo;
                    obj.OriginalEntryDate = objDetails.OriginalEntryDate;
                    obj.Salesman = objDetails.Salesman;
                    obj.SupplierNo = objDetails.SupplierNo;
                    obj.UpdatedBy = objDetails.UpdatedBy;
                    obj.DLUP = objDetails.DLUP;
                    obj.ROHS = objDetails.ROHS;
                    obj.OfferStatusNo = objDetails.OfferStatusNo;
                    obj.OfferStatusChangeDate = objDetails.OfferStatusChangeDate;
                    obj.OfferStatusChangeLoginNo = objDetails.OfferStatusChangeLoginNo;
                    obj.Notes = objDetails.Notes;
                    obj.ManufacturerName = objDetails.ManufacturerName;
                    obj.ManufacturerCode = objDetails.ManufacturerCode;
                    obj.CurrencyCode = objDetails.CurrencyCode;
                    obj.SupplierName = objDetails.SupplierName;
                    obj.ProductName = objDetails.ProductName;
                    obj.ProductDescription = objDetails.ProductDescription;
                    obj.PackageName = objDetails.PackageName;
                    obj.PackageDescription = objDetails.PackageDescription;
                    obj.SalesmanName = objDetails.SalesmanName;
                    obj.OfferStatusChangeEmployeeName = objDetails.OfferStatusChangeEmployeeName;
                    obj.POHubCompanyNo = objDetails.POHubCompanyNo;
                    obj.POHubSupplierName = objDetails.POHubSupplierName;
                    obj.POHubReleaseBy = objDetails.POHubReleaseBy;
                    obj.ClientCompanyNo = objDetails.ClientCompanyNo;
                    obj.ClientSupplierName = objDetails.ClientSupplierName;
                    obj.ClientCurrencyNo = objDetails.ClientCurrencyNo;
                    obj.IsSoCreated = objDetails.IsSoCreated;
                    obj.IsClosed = objDetails.IsClosed;
                    obj.RegionName = objDetails.RegionName;
                    obj.EstimatedShippingCost = objDetails.EstimatedShippingCost;
                    obj.TermsName = objDetails.TermsName;
                    obj.SupplierType = objDetails.SupplierType;
                    obj.MSL = objDetails.MSL;
                    obj.PartWatchMatch = objDetails.PartWatchMatch;
                    obj.DiffrentClientOffer = objDetails.DiffrentClientOffer;
                    obj.ClientCode = objDetails.ClientCode;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }

        /// <summary>
        /// GetListForCustomerRequirement
        /// Calls [usp_select_SourcingResult_for_CustomerRequirement]
        /// </summary>
        public static List<SourcingResult> GetListForCustomerRequirementCopy(System.Int32? customerRequirementId)
        {
            List<SourcingResultDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.SourcingResult.GetListForCustomerRequirementCopy(customerRequirementId);
            if (lstDetails == null)
            {
                return new List<SourcingResult>();
            }
            else
            {
                List<SourcingResult> lst = new List<SourcingResult>();
                foreach (SourcingResultDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.SourcingResult obj = new Rebound.GlobalTrader.BLL.SourcingResult();
                    obj.SourcingResultId = objDetails.SourcingResultId;
                    obj.CustomerRequirementNo = objDetails.CustomerRequirementNo;
                    obj.SourcingTable = objDetails.SourcingTable;
                    obj.SourcingTableItemNo = objDetails.SourcingTableItemNo;
                    obj.TypeName = objDetails.TypeName;
                    obj.FullPart = objDetails.FullPart;
                    obj.Part = objDetails.Part;
                    obj.ManufacturerNo = objDetails.ManufacturerNo;
                    obj.DateCode = objDetails.DateCode;
                    obj.ProductNo = objDetails.ProductNo;
                    obj.PackageNo = objDetails.PackageNo;
                    obj.Quantity = objDetails.Quantity;
                    obj.Price = objDetails.Price;
                    obj.CurrencyNo = objDetails.CurrencyNo;
                    obj.OriginalEntryDate = objDetails.OriginalEntryDate;
                    obj.Salesman = objDetails.Salesman;
                    obj.SupplierNo = objDetails.SupplierNo;
                    obj.UpdatedBy = objDetails.UpdatedBy;
                    obj.DLUP = objDetails.DLUP;
                    obj.ROHS = objDetails.ROHS;
                    obj.OfferStatusNo = objDetails.OfferStatusNo;
                    obj.OfferStatusChangeDate = objDetails.OfferStatusChangeDate;
                    obj.OfferStatusChangeLoginNo = objDetails.OfferStatusChangeLoginNo;
                    obj.Notes = objDetails.Notes;
                    obj.ManufacturerName = objDetails.ManufacturerName;
                    obj.ManufacturerCode = objDetails.ManufacturerCode;
                    obj.CurrencyCode = objDetails.CurrencyCode;
                    obj.SupplierName = objDetails.SupplierName;
                    obj.ProductName = objDetails.ProductName;
                    obj.ProductDescription = objDetails.ProductDescription;
                    obj.PackageName = objDetails.PackageName;
                    obj.PackageDescription = objDetails.PackageDescription;
                    obj.SalesmanName = objDetails.SalesmanName;
                    obj.OfferStatusChangeEmployeeName = objDetails.OfferStatusChangeEmployeeName;
                    obj.POHubCompanyNo = objDetails.POHubCompanyNo;
                    obj.POHubSupplierName = objDetails.POHubSupplierName;
                    obj.POHubReleaseBy = objDetails.POHubReleaseBy;
                    obj.ClientCompanyNo = objDetails.ClientCompanyNo;
                    obj.ClientSupplierName = objDetails.ClientSupplierName;
                    obj.ClientCurrencyNo = objDetails.ClientCurrencyNo;
                    obj.IsSoCreated = objDetails.IsSoCreated;
                    obj.IsClosed = objDetails.IsClosed;
                    obj.RegionName = objDetails.RegionName;
                    obj.EstimatedShippingCost = objDetails.EstimatedShippingCost;
                    obj.TermsName = objDetails.TermsName;
                    obj.SupplierType = objDetails.SupplierType;

                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }

        /// <summary>
        /// GetListForCustomerRequirement
        /// Calls [usp_SourcingResult_for_CustomerRequirement]
        /// </summary>
        public static List<SourcingResult> GetListForSourcing(System.Int32? customerRequirementId, System.Boolean? isFromQuote)
        {
            List<SourcingResultDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.SourcingResult.GetListForSourcing(customerRequirementId, isFromQuote);
            if (lstDetails == null)
            {
                return new List<SourcingResult>();
            }
            else
            {
                List<SourcingResult> lst = new List<SourcingResult>();
                foreach (SourcingResultDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.SourcingResult obj = new Rebound.GlobalTrader.BLL.SourcingResult();
                    obj.SourcingResultId = objDetails.SourcingResultId;
                    obj.CustomerRequirementNo = objDetails.CustomerRequirementNo;
                    obj.SourcingTable = objDetails.SourcingTable;
                    obj.SourcingTableItemNo = objDetails.SourcingTableItemNo;
                    obj.TypeName = objDetails.TypeName;
                    obj.FullPart = objDetails.FullPart;
                    obj.Part = objDetails.Part;
                    obj.ManufacturerNo = objDetails.ManufacturerNo;
                    obj.DateCode = objDetails.DateCode;
                    obj.ProductNo = objDetails.ProductNo;
                    obj.PackageNo = objDetails.PackageNo;
                    obj.Quantity = objDetails.Quantity;
                    obj.Price = objDetails.Price;
                    obj.CurrencyNo = objDetails.CurrencyNo;
                    obj.OriginalEntryDate = objDetails.OriginalEntryDate;
                    obj.Salesman = objDetails.Salesman;
                    obj.SupplierNo = objDetails.SupplierNo;
                    obj.UpdatedBy = objDetails.UpdatedBy;
                    obj.DLUP = objDetails.DLUP;
                    obj.ROHS = objDetails.ROHS;
                    obj.OfferStatusNo = objDetails.OfferStatusNo;
                    obj.OfferStatusChangeDate = objDetails.OfferStatusChangeDate;
                    obj.OfferStatusChangeLoginNo = objDetails.OfferStatusChangeLoginNo;
                    obj.Notes = objDetails.Notes;
                    obj.ManufacturerName = objDetails.ManufacturerName;
                    obj.ManufacturerCode = objDetails.ManufacturerCode;
                    obj.CurrencyCode = objDetails.CurrencyCode;
                    obj.SupplierName = objDetails.SupplierName;
                    obj.ProductName = objDetails.ProductName;
                    obj.ProductDescription = objDetails.ProductDescription;
                    obj.PackageName = objDetails.PackageName;
                    obj.PackageDescription = objDetails.PackageDescription;
                    obj.SalesmanName = objDetails.SalesmanName;
                    obj.OfferStatusChangeEmployeeName = objDetails.OfferStatusChangeEmployeeName;
                    obj.POHubCompanyNo = objDetails.POHubCompanyNo;
                    obj.POHubSupplierName = objDetails.POHubSupplierName;
                    obj.POHubReleaseBy = objDetails.POHubReleaseBy;
                    obj.ClientCompanyNo = objDetails.ClientCompanyNo;
                    obj.ClientSupplierName = objDetails.ClientSupplierName;
                    obj.ClientCurrencyNo = objDetails.ClientCurrencyNo;
                    obj.IsReleased = objDetails.IsReleased;
                    obj.Recalled = objDetails.Recalled;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }


        /// <summary>
        /// GetListForQuoteLine
        /// Calls [usp_selectAll_SourcingResult_for_QuoteLine]
        /// </summary>
        public static List<SourcingResult> GetListForQuoteLine(System.Int32? quoteLineId)
        {
            List<SourcingResultDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.SourcingResult.GetListForQuoteLine(quoteLineId);
            if (lstDetails == null)
            {
                return new List<SourcingResult>();
            }
            else
            {
                List<SourcingResult> lst = new List<SourcingResult>();
                foreach (SourcingResultDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.SourcingResult obj = new Rebound.GlobalTrader.BLL.SourcingResult();
                    obj.SourcingResultId = objDetails.SourcingResultId;
                    obj.CustomerRequirementNo = objDetails.CustomerRequirementNo;
                    obj.CustomerRequirementNumber = objDetails.CustomerRequirementNumber;
                    obj.SourcingTable = objDetails.SourcingTable;
                    obj.SourcingTableItemNo = objDetails.SourcingTableItemNo;
                    obj.TypeName = objDetails.TypeName;
                    obj.FullPart = objDetails.FullPart;
                    obj.Part = objDetails.Part;
                    obj.ManufacturerNo = objDetails.ManufacturerNo;
                    obj.DateCode = objDetails.DateCode;
                    obj.ProductNo = objDetails.ProductNo;
                    obj.PackageNo = objDetails.PackageNo;
                    obj.Quantity = objDetails.Quantity;
                    obj.Price = objDetails.Price;
                    obj.CurrencyNo = objDetails.CurrencyNo;
                    obj.OriginalEntryDate = objDetails.OriginalEntryDate;
                    obj.Salesman = objDetails.Salesman;
                    obj.SupplierNo = objDetails.SupplierNo;
                    obj.UpdatedBy = objDetails.UpdatedBy;
                    obj.DLUP = objDetails.DLUP;
                    obj.ROHS = objDetails.ROHS;
                    obj.OfferStatusNo = objDetails.OfferStatusNo;
                    obj.OfferStatusChangeDate = objDetails.OfferStatusChangeDate;
                    obj.OfferStatusChangeLoginNo = objDetails.OfferStatusChangeLoginNo;
                    obj.Notes = objDetails.Notes;
                    obj.ManufacturerName = objDetails.ManufacturerName;
                    obj.ManufacturerCode = objDetails.ManufacturerCode;
                    obj.CurrencyCode = objDetails.CurrencyCode;
                    obj.SupplierName = objDetails.SupplierName;
                    obj.ProductName = objDetails.ProductName;
                    obj.ProductDescription = objDetails.ProductDescription;
                    obj.PackageName = objDetails.PackageName;
                    obj.PackageDescription = objDetails.PackageDescription;
                    obj.SalesmanName = objDetails.SalesmanName;
                    obj.OfferStatusChangeEmployeeName = objDetails.OfferStatusChangeEmployeeName;
                    obj.POHubCompanyNo = objDetails.POHubCompanyNo;
                    obj.POHubSupplierName = objDetails.POHubSupplierName;
                    obj.POHubReleaseBy = objDetails.POHubReleaseBy;
                    obj.ClientCompanyNo = objDetails.ClientCompanyNo;
                    obj.ClientSupplierName = objDetails.ClientSupplierName;
                    obj.ClientCurrencyNo = objDetails.ClientCurrencyNo;
                    obj.ClientCurrencyCode = objDetails.ClientCurrencyCode;
                    obj.ConvertedSourcingPrice = objDetails.ConvertedSourcingPrice;
                    obj.SupplierType = objDetails.SupplierType;
                    obj.DeliveryDate = objDetails.DeliveryDate;
                    obj.EstimatedShippingCost = objDetails.EstimatedShippingCost;
                    obj.RegionName = objDetails.RegionName;
                    obj.HubRFQName = objDetails.HubRFQName;
                    obj.HubRFQNo = objDetails.HubRFQNo;
                    obj.TermsName = objDetails.TermsName;
                    obj.IsApplyPOBankFee = objDetails.IsApplyPOBankFee;

                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }
        /// <summary>
        /// Update
        /// Calls [usp_update_SourcingResult]
        /// </summary>
        public static bool Update(System.Int32? sourcingResultId, System.String part, System.Int32? manufacturerNo, System.String dateCode, System.Int32? productNo, System.Int32? packageNo, System.Int32? quantity, System.Double? price, System.Int32? currencyNo, System.Int32? offerStatusNo, System.Int32? supplierNo, System.Byte? rohs, System.String notes, System.Int32? updatedBy, System.Int32? mslLevelNo, System.Boolean? PartWatchMatch)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.SourcingResult.Update(sourcingResultId, part, manufacturerNo, dateCode, productNo, packageNo, quantity, price, currencyNo, offerStatusNo, supplierNo, rohs, notes, updatedBy, mslLevelNo, PartWatchMatch);
        }
        /// <summary>
        /// Update
        /// Calls [usp_update_POHubSourcingResult]
        /// </summary>
        //[003] start
        public static bool UpdatePOHub(System.Int32? sourcingResultId,
                                       System.String part,
                                       System.Int32? manufacturerNo,
                                       System.String dateCode,
                                       System.Int32? productNo,
                                       System.Int32? packageNo,
                                       System.Int32? quantity,
                                       System.Double? price,
                                       System.Int32? currencyNo,
                                       System.Int32? offerStatusNo,
                                       System.Int32? supplierNo,
                                       System.Byte? rohs,
                                       System.String notes,
                                       System.Int32? updatedBy,
                                       System.Double? suplierPrice,
                                       double? estimatedShippingCost,
                                       DateTime? deliveryDate,
                                       bool IsPoHub,
                                       System.String SPQ,
                                       System.String leadTime,
                                       System.String rohsStatus,
                                       System.String factorySealed,
                                       System.String MSL,
                                       System.String supplierTotalQSA,
                                       System.String supplierMOQ,
                                       System.String supplierLTB,
                                       System.Int32? regionNo,
                                       System.Int32? hubcurrencyNo,
                                       System.Int32? linkMultiCurrencyNo,
                                       System.Int32? mslLevelNo,
                                       System.Int32? supplierWarranty,
                                       System.Boolean? isTestingRecommended,
                                       System.Int32? PriorityNo,
                                       System.Int32? CountryOfOriginNo,
                                       System.String ChangedFields,
                                       System.Boolean? PartWatchMatch,
                                       string sellPriceLessReason,
                                       System.Int32? TypeOfSupplier = 0,
                                       System.Int32? ReasonForSupplier = 0,
                                       System.Int32? RiskOfSupplier = 0,
                                       System.Int32? CountryNo = 0)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.SourcingResult.UpdatePOHub(sourcingResultId,
                                                                                    part,
                                                                                    manufacturerNo,
                                                                                    dateCode,
                                                                                    productNo,
                                                                                    packageNo,
                                                                                    quantity,
                                                                                    price,
                                                                                    currencyNo,
                                                                                    offerStatusNo,
                                                                                    supplierNo,
                                                                                    rohs,
                                                                                    notes,
                                                                                    updatedBy,
                                                                                    suplierPrice,
                                                                                    estimatedShippingCost,
                                                                                    deliveryDate,
                                                                                    IsPoHub,
                                                                                    SPQ,
                                                                                    leadTime,
                                                                                    rohsStatus,
                                                                                    factorySealed,
                                                                                    MSL,
                                                                                    supplierTotalQSA,
                                                                                    supplierMOQ,
                                                                                    supplierLTB,
                                                                                    regionNo,
                                                                                    hubcurrencyNo,
                                                                                    linkMultiCurrencyNo,
                                                                                    mslLevelNo,
                                                                                    supplierWarranty,
                                                                                    isTestingRecommended,
                                                                                    PriorityNo,
                                                                                    CountryOfOriginNo,
                                                                                    ChangedFields,
                                                                                    PartWatchMatch,
                                                                                    sellPriceLessReason,
                                                                                    TypeOfSupplier,
                                                                                    ReasonForSupplier,
                                                                                    RiskOfSupplier,
                                                                                    CountryNo);
        }
        //[001] end
        /// <summary>
        /// Update (without parameters)
        /// Calls [usp_update_SourcingResult]
        /// </summary>
        public bool Update()
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.SourcingResult.Update(SourcingResultId, Part, ManufacturerNo, DateCode, ProductNo, PackageNo, Quantity, Price, CurrencyNo, OfferStatusNo, SupplierNo, ROHS, Notes, UpdatedBy, null, null);
        }

        /// <summary>
        /// GetListForBOMCustomerRequirement
        /// Calls [usp_selectAll_SourcingResult_for_BOMCustomerRequirement]
        /// </summary>
        public static List<SourcingResult> GetListForBOMCustomerRequirement(System.Int32? customerRequirementId, System.Boolean isPOHub)
        {
            List<SourcingResultDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.SourcingResult.GetListForBOMCustomerRequirement(customerRequirementId, isPOHub);
            if (lstDetails == null)
            {
                return new List<SourcingResult>();
            }
            else
            {
                List<SourcingResult> lst = new List<SourcingResult>();
                foreach (SourcingResultDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.SourcingResult obj = new Rebound.GlobalTrader.BLL.SourcingResult();
                    obj.SourcingResultId = objDetails.SourcingResultId;
                    obj.CustomerRequirementNo = objDetails.CustomerRequirementNo;
                    obj.SourcingTable = objDetails.SourcingTable;
                    obj.SourcingTableItemNo = objDetails.SourcingTableItemNo;
                    obj.TypeName = objDetails.TypeName;
                    obj.FullPart = objDetails.FullPart;
                    obj.Part = objDetails.Part;
                    obj.ManufacturerNo = objDetails.ManufacturerNo;
                    obj.DateCode = objDetails.DateCode;
                    obj.ProductNo = objDetails.ProductNo;
                    obj.PackageNo = objDetails.PackageNo;
                    obj.Quantity = objDetails.Quantity;
                    obj.Price = objDetails.Price;
                    obj.CurrencyNo = objDetails.CurrencyNo;
                    obj.OriginalEntryDate = objDetails.OriginalEntryDate;
                    obj.Salesman = objDetails.Salesman;
                    obj.SupplierNo = objDetails.SupplierNo;
                    obj.UpdatedBy = objDetails.UpdatedBy;
                    obj.DLUP = objDetails.DLUP;
                    obj.ROHS = objDetails.ROHS;
                    obj.OfferStatusNo = objDetails.OfferStatusNo;
                    obj.OfferStatusChangeDate = objDetails.OfferStatusChangeDate;
                    obj.OfferStatusChangeLoginNo = objDetails.OfferStatusChangeLoginNo;
                    obj.Notes = objDetails.Notes;
                    obj.ManufacturerName = objDetails.ManufacturerName;
                    obj.ManufacturerCode = objDetails.ManufacturerCode;
                    obj.CurrencyCode = objDetails.CurrencyCode;
                    obj.SupplierName = objDetails.SupplierName;
                    obj.ProductName = objDetails.ProductName;
                    obj.ProductDescription = objDetails.ProductDescription;
                    obj.PackageName = objDetails.PackageName;
                    obj.PackageDescription = objDetails.PackageDescription;
                    obj.SalesmanName = objDetails.SalesmanName;
                    obj.OfferStatusChangeEmployeeName = objDetails.OfferStatusChangeEmployeeName;
                    obj.SupplierPrice = objDetails.SupplierPrice;
                    obj.POHubCompanyNo = objDetails.POHubCompanyNo;
                    obj.POHubSupplierName = objDetails.POHubSupplierName;
                    obj.POHubReleaseBy = objDetails.POHubReleaseBy;
                    obj.ClientCompanyNo = objDetails.ClientCompanyNo;
                    obj.ClientSupplierName = objDetails.ClientSupplierName;
                    obj.UPLiftPrice = objDetails.UPLiftPrice;
                    obj.ClientCurrencyNo = objDetails.ClientCurrencyNo;
                    obj.ClientCurrencyCode = objDetails.ClientCurrencyCode;
                    obj.ConvertedSourcingPrice = objDetails.ConvertedSourcingPrice;
                    obj.MslSpqFactorySealed = objDetails.MslSpqFactorySealed;
                    obj.EstimatedShippingCost = objDetails.EstimatedShippingCost;

                    obj.SupplierType = objDetails.SupplierType;
                    obj.DeliveryDate = objDetails.DeliveryDate;


                    obj.ActualPrice = objDetails.ActualPrice;
                    obj.SupplierPercentage = objDetails.SupplierPercentage;

                    obj.SupplierManufacturerName = objDetails.SupplierManufacturerName;
                    obj.SupplierDateCode = objDetails.SupplierDateCode;
                    obj.SupplierPackageType = objDetails.SupplierPackageType;
                    obj.SupplierProductType = objDetails.SupplierProductType;
                    obj.SupplierMOQ = objDetails.SupplierMOQ;
                    obj.SupplierTotalQSA = objDetails.SupplierTotalQSA;
                    obj.SupplierLTB = objDetails.SupplierLTB;
                    obj.SupplierNotes = objDetails.SupplierNotes;
                    obj.SourcingRelease = objDetails.SourcingRelease;
                    obj.SPQ = objDetails.SPQ;
                    obj.LeadTime = objDetails.LeadTime;
                    obj.ROHSStatus = objDetails.ROHSStatus;
                    obj.FactorySealed = objDetails.FactorySealed;
                    obj.MSL = objDetails.MSL;
                    obj.IsClosed = objDetails.IsClosed;
                    obj.ClientNo = objDetails.ClientNo;
                    obj.RegionName = objDetails.RegionName;
                    obj.IsClosed = objDetails.IsClosed;
                    obj.IsSoCreated = objDetails.IsSoCreated;
                    obj.TermsName = objDetails.TermsName;
                    obj.IsApplyPOBankFee = objDetails.IsApplyPOBankFee;
                    obj.SourceRef = objDetails.SourceRef;

                    obj.OriginalPrice = objDetails.OriginalPrice;
                    obj.ActualCurrencyNo = objDetails.ActualCurrencyNo;
                    obj.ActualCurrencyCode = objDetails.ActualCurrencyCode;

                    obj.SourcingReleasedCount = objDetails.SourcingReleasedCount;
                    //[001] start
                    //obj.NonPreferredCompany = objDetails.NonPreferredCompany;
                    obj.SupplierWarranty = objDetails.SupplierWarranty;
                    //[001] end
                    obj.MSLLevelNo = objDetails.MSLLevelNo;
                    obj.MSLLevelText = objDetails.MSLLevelText;
                    //[003] start
                    obj.IsTestingRecommended = objDetails.IsTestingRecommended;
                    //[003] end
                    obj.IsImageAvailable = objDetails.IsImageAvailable;
                    obj.PriorityNo = objDetails.PriorityNo;
                    obj.IHSCountryOfOriginNo = objDetails.IHSCountryOfOriginNo;
                    obj.IHSCountryOfOriginName = objDetails.IHSCountryOfOriginName;
                    obj.CountryOfOriginNo = objDetails.CountryOfOriginNo;
                    obj.CountryOfOriginName = objDetails.CountryOfOriginName;
                    obj.ReReleased = objDetails.ReReleased;
                    obj.ROHSDescription = objDetails.ROHSDescription;
                    obj.PartWatchMatchHUBIPO = objDetails.PartWatchMatchHUBIPO;
                    obj.IsPartWatchMatchClient = objDetails.IsPartWatchMatchClient;
                    obj.SourceClient = objDetails.SourceClient;
                    obj.SourceClientNo = objDetails.SourceClientNo;
                    obj.ISAS6081Required = objDetails.ISAS6081Required;
                    obj.TypeOfSupplierName = objDetails.TypeOfSupplierName;
                    obj.ReasonForSupplierName = objDetails.ReasonForSupplierName;
                    obj.RiskOfSupplierName = objDetails.RiskOfSupplierName;
                    obj.AssigneeId = objDetails.AssigneeId;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }


        /// <summary>
        /// GetListForBOMCustomerRequirement
        /// Calls [usp_selectAll_SourcingResult_for_LogDetail]
        /// </summary>
        public static List<SourcingResult> GetListForBOMCustomerRequirementLogDetail(System.Int32? customerRequirementId, System.Boolean isPOHub)
        {
            List<SourcingResultDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.SourcingResult.GetListForBOMCustomerRequirementLogDetail(customerRequirementId, isPOHub);
            if (lstDetails == null)
            {
                return new List<SourcingResult>();
            }
            else
            {
                List<SourcingResult> lst = new List<SourcingResult>();
                foreach (SourcingResultDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.SourcingResult obj = new Rebound.GlobalTrader.BLL.SourcingResult();
                    obj.SourcingResultId = objDetails.SourcingResultId;
                    obj.CustomerRequirementNo = objDetails.CustomerRequirementNo;
                    obj.ChangeNotes = objDetails.ChangeNotes;


                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }

        public static SourcingResult GetSourcingLog(System.Int32? sourcingResultId)
        {
            Rebound.GlobalTrader.DAL.SourcingResultDetails objDetails = Rebound.GlobalTrader.DAL.SiteProvider.SourcingResult.GetSourcingLog(sourcingResultId);
            if (objDetails == null)
            {
                return null;
            }
            else
            {
                SourcingResult obj = new SourcingResult();
                obj.SourcingResultId = objDetails.SourcingResultId;
                obj.CustomerRequirementNo = objDetails.CustomerRequirementNo;
                obj.ChangeNotes = objDetails.ChangeNotes;
                objDetails = null;
                return obj;
            }
        }


        /// <summary>
        /// GetListForBOMCustomerRequirement
        /// Calls [usp_selectAll_SourcingResult_for_BOME]
        /// </summary>
        public static List<SourcingResult> GetListForBOMSourcingResult(System.Int32? BomId, System.Boolean isPOHub)
        {
            List<SourcingResultDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.SourcingResult.GetListForBOMSourcingResult(BomId, isPOHub);
            if (lstDetails == null)
            {
                return new List<SourcingResult>();
            }
            else
            {
                List<SourcingResult> lst = new List<SourcingResult>();
                foreach (SourcingResultDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.SourcingResult obj = new Rebound.GlobalTrader.BLL.SourcingResult();
                    obj.SourcingResultId = objDetails.SourcingResultId;
                    obj.CustomerRequirementNo = objDetails.CustomerRequirementNo;
                    obj.SourcingTable = objDetails.SourcingTable;
                    obj.SourcingTableItemNo = objDetails.SourcingTableItemNo;
                    obj.TypeName = objDetails.TypeName;
                    obj.FullPart = objDetails.FullPart;
                    obj.Part = objDetails.Part;
                    obj.ManufacturerNo = objDetails.ManufacturerNo;
                    obj.DateCode = objDetails.DateCode;
                    obj.ProductNo = objDetails.ProductNo;
                    obj.PackageNo = objDetails.PackageNo;
                    obj.Quantity = objDetails.Quantity;
                    obj.Price = objDetails.Price;
                    obj.CurrencyNo = objDetails.CurrencyNo;
                    obj.OriginalEntryDate = objDetails.OriginalEntryDate;
                    obj.Salesman = objDetails.Salesman;
                    obj.SupplierNo = objDetails.SupplierNo;
                    obj.UpdatedBy = objDetails.UpdatedBy;
                    obj.DLUP = objDetails.DLUP;
                    obj.ROHS = objDetails.ROHS;
                    obj.OfferStatusNo = objDetails.OfferStatusNo;
                    obj.OfferStatusChangeDate = objDetails.OfferStatusChangeDate;
                    obj.OfferStatusChangeLoginNo = objDetails.OfferStatusChangeLoginNo;
                    obj.Notes = objDetails.Notes;
                    obj.ManufacturerName = objDetails.ManufacturerName;
                    obj.ManufacturerCode = objDetails.ManufacturerCode;
                    obj.CurrencyCode = objDetails.CurrencyCode;
                    obj.SupplierName = objDetails.SupplierName;
                    obj.ProductName = objDetails.ProductName;
                    obj.ProductDescription = objDetails.ProductDescription;
                    obj.PackageName = objDetails.PackageName;
                    obj.PackageDescription = objDetails.PackageDescription;
                    obj.SalesmanName = objDetails.SalesmanName;
                    obj.OfferStatusChangeEmployeeName = objDetails.OfferStatusChangeEmployeeName;
                    obj.SupplierPrice = objDetails.SupplierPrice;
                    obj.POHubCompanyNo = objDetails.POHubCompanyNo;
                    obj.POHubSupplierName = objDetails.POHubSupplierName;
                    obj.POHubReleaseBy = objDetails.POHubReleaseBy;
                    obj.ClientCompanyNo = objDetails.ClientCompanyNo;
                    obj.ClientSupplierName = objDetails.ClientSupplierName;
                    obj.UPLiftPrice = objDetails.UPLiftPrice;
                    obj.ClientCurrencyNo = objDetails.ClientCurrencyNo;
                    obj.ClientCurrencyCode = objDetails.ClientCurrencyCode;
                    obj.ConvertedSourcingPrice = objDetails.ConvertedSourcingPrice;
                    obj.MslSpqFactorySealed = objDetails.MslSpqFactorySealed;
                    obj.EstimatedShippingCost = objDetails.EstimatedShippingCost;

                    obj.SupplierType = objDetails.SupplierType;
                    obj.DeliveryDate = objDetails.DeliveryDate;


                    obj.ActualPrice = objDetails.ActualPrice;
                    obj.SupplierPercentage = objDetails.SupplierPercentage;

                    obj.SupplierManufacturerName = objDetails.SupplierManufacturerName;
                    obj.SupplierDateCode = objDetails.SupplierDateCode;
                    obj.SupplierPackageType = objDetails.SupplierPackageType;
                    obj.SupplierProductType = objDetails.SupplierProductType;
                    obj.SupplierMOQ = objDetails.SupplierMOQ;
                    obj.SupplierTotalQSA = objDetails.SupplierTotalQSA;
                    obj.SupplierLTB = objDetails.SupplierLTB;
                    obj.SupplierNotes = objDetails.SupplierNotes;
                    obj.SourcingRelease = objDetails.SourcingRelease;
                    obj.SPQ = objDetails.SPQ;
                    obj.LeadTime = objDetails.LeadTime;
                    obj.ROHSStatus = objDetails.ROHSStatus;
                    obj.FactorySealed = objDetails.FactorySealed;
                    obj.MSL = objDetails.MSL;
                    obj.IsClosed = objDetails.IsClosed;
                    obj.ClientNo = objDetails.ClientNo;
                    obj.RegionName = objDetails.RegionName;
                    obj.IsClosed = objDetails.IsClosed;
                    obj.IsSoCreated = objDetails.IsSoCreated;
                    obj.TermsName = objDetails.TermsName;
                    obj.IsApplyPOBankFee = objDetails.IsApplyPOBankFee;
                    obj.SourceRef = objDetails.SourceRef;

                    obj.OriginalPrice = objDetails.OriginalPrice;
                    obj.ActualCurrencyNo = objDetails.ActualCurrencyNo;
                    obj.ActualCurrencyCode = objDetails.ActualCurrencyCode;

                    obj.SourcingReleasedCount = objDetails.SourcingReleasedCount;
                    //[001] start
                    //obj.NonPreferredCompany = objDetails.NonPreferredCompany;
                    obj.SupplierWarranty = objDetails.SupplierWarranty;
                    //[001] end
                    obj.MSLLevelNo = objDetails.MSLLevelNo;
                    obj.MSLLevelText = objDetails.MSLLevelText;
                    //[003] start
                    obj.IsTestingRecommended = objDetails.IsTestingRecommended;
                    //[003] end
                    obj.IsImageAvailable = objDetails.IsImageAvailable;
                    obj.PriorityNo = objDetails.PriorityNo;
                    obj.IHSCountryOfOriginNo = objDetails.IHSCountryOfOriginNo;
                    obj.IHSCountryOfOriginName = objDetails.IHSCountryOfOriginName;
                    obj.CountryOfOriginNo = objDetails.CountryOfOriginNo;
                    obj.CountryOfOriginName = objDetails.CountryOfOriginName;
                    obj.ReReleased = objDetails.ReReleased;

                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }

        //code add by anand
        /// <summary>
        /// GetListForBOMCustomerRequirement Req List Bind Cross Match
        /// Calls [usp_selectAll_SourcingResult_for_BOMCustomerRequirement_List]
        /// </summary>
        public static List<SourcingResult> GetListForBOMCustomerRequirement_List(System.Int32? customerRequirementId, System.Boolean isPOHub)
        {
            List<SourcingResultDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.SourcingResult.GetListForBOMCustomerRequirement_List(customerRequirementId, isPOHub);
            if (lstDetails == null)
            {
                return new List<SourcingResult>();
            }
            else
            {
                List<SourcingResult> lst = new List<SourcingResult>();
                foreach (SourcingResultDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.SourcingResult obj = new Rebound.GlobalTrader.BLL.SourcingResult();
                    obj.SourcingResultId = objDetails.SourcingResultId;
                    obj.CustomerRequirementNo = objDetails.CustomerRequirementNo;
                    obj.SourcingTable = objDetails.SourcingTable;
                    obj.SourcingTableItemNo = objDetails.SourcingTableItemNo;
                    obj.TypeName = objDetails.TypeName;
                    obj.FullPart = objDetails.FullPart;
                    obj.Part = objDetails.Part;
                    obj.ManufacturerNo = objDetails.ManufacturerNo;
                    obj.DateCode = objDetails.DateCode;
                    obj.ProductNo = objDetails.ProductNo;
                    obj.PackageNo = objDetails.PackageNo;
                    obj.Quantity = objDetails.Quantity;
                    obj.Price = objDetails.Price;
                    obj.CurrencyNo = objDetails.CurrencyNo;
                    obj.OriginalEntryDate = objDetails.OriginalEntryDate;
                    obj.Salesman = objDetails.Salesman;
                    obj.SupplierNo = objDetails.SupplierNo;
                    obj.UpdatedBy = objDetails.UpdatedBy;
                    obj.DLUP = objDetails.DLUP;
                    obj.ROHS = objDetails.ROHS;
                    obj.OfferStatusNo = objDetails.OfferStatusNo;
                    obj.OfferStatusChangeDate = objDetails.OfferStatusChangeDate;
                    obj.OfferStatusChangeLoginNo = objDetails.OfferStatusChangeLoginNo;
                    obj.Notes = objDetails.Notes;
                    obj.ManufacturerName = objDetails.ManufacturerName;
                    obj.ManufacturerCode = objDetails.ManufacturerCode;
                    obj.CurrencyCode = objDetails.CurrencyCode;
                    obj.SupplierName = objDetails.SupplierName;
                    obj.ProductName = objDetails.ProductName;
                    obj.ProductDescription = objDetails.ProductDescription;
                    obj.PackageName = objDetails.PackageName;
                    obj.PackageDescription = objDetails.PackageDescription;
                    obj.SalesmanName = objDetails.SalesmanName;
                    obj.OfferStatusChangeEmployeeName = objDetails.OfferStatusChangeEmployeeName;
                    obj.SupplierPrice = objDetails.SupplierPrice;
                    obj.POHubCompanyNo = objDetails.POHubCompanyNo;
                    obj.POHubSupplierName = objDetails.POHubSupplierName;
                    obj.POHubReleaseBy = objDetails.POHubReleaseBy;
                    obj.ClientCompanyNo = objDetails.ClientCompanyNo;
                    obj.ClientSupplierName = objDetails.ClientSupplierName;
                    obj.UPLiftPrice = objDetails.UPLiftPrice;
                    obj.ClientCurrencyNo = objDetails.ClientCurrencyNo;
                    obj.ClientCurrencyCode = objDetails.ClientCurrencyCode;
                    obj.ConvertedSourcingPrice = objDetails.ConvertedSourcingPrice;
                    obj.MslSpqFactorySealed = objDetails.MslSpqFactorySealed;
                    obj.EstimatedShippingCost = objDetails.EstimatedShippingCost;

                    obj.SupplierType = objDetails.SupplierType;
                    obj.DeliveryDate = objDetails.DeliveryDate;


                    obj.ActualPrice = objDetails.ActualPrice;
                    obj.SupplierPercentage = objDetails.SupplierPercentage;

                    obj.SupplierManufacturerName = objDetails.SupplierManufacturerName;
                    obj.SupplierDateCode = objDetails.SupplierDateCode;
                    obj.SupplierPackageType = objDetails.SupplierPackageType;
                    obj.SupplierProductType = objDetails.SupplierProductType;
                    obj.SupplierMOQ = objDetails.SupplierMOQ;
                    obj.SupplierTotalQSA = objDetails.SupplierTotalQSA;
                    obj.SupplierLTB = objDetails.SupplierLTB;
                    obj.SupplierNotes = objDetails.SupplierNotes;
                    obj.SourcingRelease = objDetails.SourcingRelease;
                    obj.SPQ = objDetails.SPQ;
                    obj.LeadTime = objDetails.LeadTime;
                    obj.ROHSStatus = objDetails.ROHSStatus;
                    obj.FactorySealed = objDetails.FactorySealed;
                    obj.MSL = objDetails.MSL;
                    obj.IsClosed = objDetails.IsClosed;
                    obj.ClientNo = objDetails.ClientNo;
                    obj.RegionName = objDetails.RegionName;
                    obj.IsClosed = objDetails.IsClosed;
                    obj.IsSoCreated = objDetails.IsSoCreated;
                    obj.TermsName = objDetails.TermsName;
                    obj.IsApplyPOBankFee = objDetails.IsApplyPOBankFee;
                    obj.SourceRef = objDetails.SourceRef;

                    obj.OriginalPrice = objDetails.OriginalPrice;
                    obj.ActualCurrencyNo = objDetails.ActualCurrencyNo;
                    obj.ActualCurrencyCode = objDetails.ActualCurrencyCode;

                    obj.SourcingReleasedCount = objDetails.SourcingReleasedCount;
                    //[001] start
                    //obj.NonPreferredCompany = objDetails.NonPreferredCompany;
                    obj.SupplierWarranty = objDetails.SupplierWarranty;
                    //[001] end
                    obj.MSLLevelNo = objDetails.MSLLevelNo;
                    obj.MSLLevelText = objDetails.MSLLevelText;
                    //[003] start
                    obj.IsTestingRecommended = objDetails.IsTestingRecommended;
                    //[003] end
                    obj.IsImageAvailable = objDetails.IsImageAvailable;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }
        //code end by anand

        /// <summary>
        /// ConvertPriceToDifferentCurrency
        /// Calls [usp_Convert_Price_To_Different_Currency]
        /// </summary>
        public static SourcingResult ConvertPriceToDifferentCurrency(System.Int32? intFromCurrency, System.Int32? intToCurrency, System.Double? upliftPrice, System.Double? hubBuyPrice, System.Int32? sourcingResultNo)
        {
            Rebound.GlobalTrader.DAL.SourcingResultDetails objDetails = Rebound.GlobalTrader.DAL.SiteProvider.SourcingResult.ConvertPriceToDifferentCurrency(intFromCurrency, intToCurrency, upliftPrice, hubBuyPrice, sourcingResultNo);
            if (objDetails == null)
            {
                return null;
            }
            else
            {
                SourcingResult obj = new SourcingResult();
                obj.CurrencyCode = objDetails.CurrencyCode;
                obj.UPLiftPrice = objDetails.UPLiftPrice;
                obj.SupplierPrice = objDetails.SupplierPrice;
                obj.EstimatedShippingCost = objDetails.EstimatedShippingCost;
                obj.SupplierPercentage = objDetails.SupplierPercentage;
                objDetails = null;
                return obj;
            }
        }

        private static SourcingResult PopulateFromDBDetailsObject(SourcingResultDetails obj)
        {
            SourcingResult objNew = new SourcingResult();
            objNew.SourcingResultId = obj.SourcingResultId;
            objNew.CustomerRequirementNo = obj.CustomerRequirementNo;
            objNew.SourcingTable = obj.SourcingTable;
            objNew.SourcingTableItemNo = obj.SourcingTableItemNo;
            objNew.TypeName = obj.TypeName;
            objNew.FullPart = obj.FullPart;
            objNew.Part = obj.Part;
            objNew.ManufacturerNo = obj.ManufacturerNo;
            objNew.DateCode = obj.DateCode;
            objNew.ProductNo = obj.ProductNo;
            objNew.PackageNo = obj.PackageNo;
            objNew.Quantity = obj.Quantity;
            objNew.Price = obj.Price;
            objNew.CurrencyNo = obj.CurrencyNo;
            objNew.OriginalEntryDate = obj.OriginalEntryDate;
            objNew.Salesman = obj.Salesman;
            objNew.SupplierNo = obj.SupplierNo;
            objNew.UpdatedBy = obj.UpdatedBy;
            objNew.DLUP = obj.DLUP;
            objNew.ROHS = obj.ROHS;
            objNew.OfferStatusNo = obj.OfferStatusNo;
            objNew.OfferStatusChangeDate = obj.OfferStatusChangeDate;
            objNew.OfferStatusChangeLoginNo = obj.OfferStatusChangeLoginNo;
            objNew.Notes = obj.Notes;
            objNew.CurrencyCode = obj.CurrencyCode;
            objNew.CustomerRequirementId = obj.CustomerRequirementId;
            objNew.CustomerRequirementNumber = obj.CustomerRequirementNumber;
            objNew.ClientNo = obj.ClientNo;
            objNew.CompanyNo = obj.CompanyNo;
            objNew.CompanyName = obj.CompanyName;
            objNew.SupplierName = obj.SupplierName;
            objNew.ManufacturerCode = obj.ManufacturerCode;
            objNew.ProductName = obj.ProductName;
            objNew.PackageName = obj.PackageName;
            objNew.OfferStatusChangeEmployeeName = obj.OfferStatusChangeEmployeeName;
            objNew.RowNum = obj.RowNum;
            objNew.RowCnt = obj.RowCnt;
            objNew.ManufacturerName = obj.ManufacturerName;
            objNew.CustomerPart = obj.CustomerPart;
            objNew.ProductDescription = obj.ProductDescription;
            objNew.PackageDescription = obj.PackageDescription;
            objNew.SalesmanName = obj.SalesmanName;
            return objNew;
        }


        /// <summary>
        /// Insert
        /// Calls [usp_insert_SourcingResult]
        /// </summary>
        //[001],[003] start
        public static Int32 InsertSourcingResult(System.Int32? customerRequirementNo,
                                                 System.String typeName,
                                                 System.String notes,
                                                 System.String part,
                                                 System.Int32? manufacturerNo,
                                                 System.String dateCode,
                                                 System.Int32? productNo,
                                                 System.Int32? packageNo,
                                                 System.Int32? quantity,
                                                 System.Double? price,
                                                 System.DateTime? originalEntryDate,
                                                 System.Int32? salesman,
                                                 System.Int32? offerStatusNo,
                                                 System.Int32? supplierNo,
                                                 System.Byte? rohs,
                                                 System.Int32? clientNo,
                                                 System.Int32? updatedBy,
                                                 System.Double? suplierPrice,
                                                 double? estimatedShippingCost,
                                                 DateTime? deliveryDate,
                                                 bool isPoHub,
                                                 System.String SPQ,
                                                 System.String leadTime,
                                                 System.String rohsStatus,
                                                 System.String factorySealed,
                                                 System.String MSL,
                                                 System.String supplierTotalQSA,
                                                 System.String supplierMOQ,
                                                 System.String supplierLTB,
                                                 System.Int32? regionNo,
                                                 System.Int32? hubcurrencyNo,
                                                 System.Int32? mslLevel,
                                                 System.Int32? supplierWarranty,
                                                 System.Boolean? isTestingRecommended,
                                                 out System.String strLinkMessage,
                                                 System.Int32? IHSCountryOfOriginNo,
                                                 string sellPriceLessReason,
                                                 System.Int32? TypeOfSupplier = 0,
                                                 System.Int32? ReasonForSupplier = 0,
                                                 System.Int32? RiskOfSupplier = 0,
                                                 System.Int32? CountryNo = 0)
        {
            Int32 objReturn = Rebound.GlobalTrader.DAL.SiteProvider.SourcingResult.InsertSourcingResult(customerRequirementNo,
                                                                                                        typeName,
                                                                                                        notes,
                                                                                                        part,
                                                                                                        manufacturerNo,
                                                                                                        dateCode,
                                                                                                        productNo,
                                                                                                        packageNo,
                                                                                                        quantity,
                                                                                                        price,
                                                                                                        originalEntryDate,
                                                                                                        salesman,
                                                                                                        offerStatusNo,
                                                                                                        supplierNo,
                                                                                                        rohs,
                                                                                                        clientNo,
                                                                                                        updatedBy,
                                                                                                        suplierPrice,
                                                                                                        estimatedShippingCost,
                                                                                                        deliveryDate,
                                                                                                        isPoHub,
                                                                                                        SPQ,
                                                                                                        leadTime,
                                                                                                        rohsStatus,
                                                                                                        factorySealed,
                                                                                                        MSL,
                                                                                                        supplierTotalQSA,
                                                                                                        supplierMOQ,
                                                                                                        supplierLTB,
                                                                                                        regionNo,
                                                                                                        hubcurrencyNo,
                                                                                                        mslLevel,
                                                                                                        supplierWarranty,
                                                                                                        isTestingRecommended,
                                                                                                        out strLinkMessage,
                                                                                                        IHSCountryOfOriginNo,
                                                                                                        sellPriceLessReason,
                                                                                                        TypeOfSupplier,
                                                                                                        ReasonForSupplier,
                                                                                                        RiskOfSupplier,
                                                                                                        CountryNo);
            return objReturn;

        }
        /// <summary>
        /// Calls [usp_Delete_ReqPartWatchMatch]
        /// </summary>
        /// <param name="sourcingResultIds"></param>
        /// <returns></returns>

        public static Int32 DeletePartWatchMatch(System.String sourcingResultIds)
        {
            Int32 objReturn = Rebound.GlobalTrader.DAL.SiteProvider.SourcingResult.DeletePartWatchMatch(sourcingResultIds);
            return objReturn;
        }
        /// <summary>
        /// Calls [usp_Delete_HubPartWatchMatch]
        /// </summary>
        /// <param name="sourcingResultIds"></param>
        /// <returns></returns>

        public static Int32 DeleteHubPartWatchMatch(System.String sourcingResultIds)
        {
            Int32 objReturn = Rebound.GlobalTrader.DAL.SiteProvider.SourcingResult.DeleteHubPartWatchMatch(sourcingResultIds);
            return objReturn;
        }
        /// <summary>
        /// Calls [usp_Delete_ReqPartWatchMatch]
        /// </summary>
        /// <param name="sourcingResultIds"></param>
        /// <returns></returns>

        public static Int32 DeletePartWatchMatchHUBIPO(System.String sourcingResultIds, System.Int32? updatedBy)
        {
            Int32 objReturn = Rebound.GlobalTrader.DAL.SiteProvider.SourcingResult.DeletePartWatchMatchHUBIPO(sourcingResultIds, updatedBy);
            return objReturn;
        }

        /// <summary>
        /// Calls [usp_selectAll_SourcingResult_for_AllRelease]
        /// </summary>
        /// <param name="customerRequirementId"></param>
        /// <returns></returns>
        public static List<SourcingResult> GetListForBOMReleaseAll(System.Int32? customerRequirementId, System.Boolean isPOHub)
        {
            List<SourcingResultDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.SourcingResult.GetListForBOMReleaseAll(customerRequirementId, isPOHub);
            if (lstDetails == null)
            {
                return new List<SourcingResult>();
            }
            else
            {
                List<SourcingResult> lst = new List<SourcingResult>();
                foreach (SourcingResultDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.SourcingResult obj = new Rebound.GlobalTrader.BLL.SourcingResult();
                    obj.SourcingResultId = objDetails.SourcingResultId;
                    obj.CustomerRequirementNo = objDetails.CustomerRequirementNo;
                    obj.FullPart = objDetails.FullPart;
                    obj.Part = objDetails.Part;
                    obj.Price = objDetails.Price;
                    obj.CurrencyCode = objDetails.CurrencyCode;
                    obj.DeliveryDate = objDetails.DeliveryDate;
                    obj.OriginalPrice = objDetails.OriginalPrice;
                    obj.ActualCurrencyCode = objDetails.ActualCurrencyCode;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }


        /// <summary>
        /// Calls [usp_selectAll_PVVBOM_Question]
        /// </summary>
        /// <param name="BomId"></param>
        /// <returns></returns>
        public static List<SourcingResult> GetListPVVQuestion(System.Int32? BomId, System.Boolean isPOHub)
        {
            List<SourcingResultDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.SourcingResult.GetListPVVQuestion(BomId, isPOHub);
            if (lstDetails == null)
            {
                return new List<SourcingResult>();
            }
            else
            {
                List<SourcingResult> lst = new List<SourcingResult>();
                foreach (SourcingResultDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.SourcingResult obj = new Rebound.GlobalTrader.BLL.SourcingResult();
                    obj.PVVQuestionId = objDetails.PVVQuestionId;
                    obj.PVVQuestionName = objDetails.PVVQuestionName;
                    //edit
                    obj.PVVAnswerId = objDetails.PVVAnswerId;
                    obj.PVVAnswerName = objDetails.PVVAnswerName;
                    obj.HUBRFQNo = objDetails.HUBRFQNo;
                    obj.BomId = objDetails.BomId;

                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }
        /// <summary>
        /// Calls [usp_selectAll_PVVBOM_Question]
        /// </summary>
        /// <param name="BomId"></param>
        /// <returns></returns>
        public static List<SourcingResult> GetListPVVQuestionTemp(string BomId, System.Boolean isPOHub)
        {
            List<SourcingResultDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.SourcingResult.GetListPVVQuestionTemp(BomId, isPOHub);
            if (lstDetails == null)
            {
                return new List<SourcingResult>();
            }
            else
            {
                List<SourcingResult> lst = new List<SourcingResult>();
                foreach (SourcingResultDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.SourcingResult obj = new Rebound.GlobalTrader.BLL.SourcingResult();
                    obj.PVVQuestionId = objDetails.PVVQuestionId;
                    obj.PVVQuestionName = objDetails.PVVQuestionName;
                    //edit
                    obj.PVVAnswerId = objDetails.PVVAnswerId;
                    obj.PVVAnswerName = objDetails.PVVAnswerName;
                    obj.HUBRFQNo = objDetails.HUBRFQNo;
                    obj.BomId = objDetails.BomId;

                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }
        /// <summary>
        /// Calls [usp_selectAll_PVVBOM_Question]
        /// </summary>
        /// <param name="BomId"></param>
        /// <returns></returns>
        public static List<SourcingResult> GetListCOunt(System.Int32? BomId, System.Boolean isPOHub)
        {
            List<SourcingResultDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.SourcingResult.GetListCOunt(BomId, isPOHub);
            if (lstDetails == null)
            {
                return new List<SourcingResult>();
            }
            else
            {
                List<SourcingResult> lst = new List<SourcingResult>();
                foreach (SourcingResultDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.SourcingResult obj = new Rebound.GlobalTrader.BLL.SourcingResult();
                    obj.PVVQuestionId = objDetails.PVVQuestionId;
                    obj.PVVQuestionName = objDetails.PVVQuestionName;
                    //edit
                    obj.PVVAnswerId = objDetails.PVVAnswerId;
                    obj.PVVAnswerName = objDetails.PVVAnswerName;
                    obj.HUBRFQNo = objDetails.HUBRFQNo;
                    obj.BomId = objDetails.BomId;

                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }
        /// <summary>
        /// Calls [usp_selectAll_PVVBOM_Question]
        /// </summary>
        /// <param name="BomId"></param>
        /// <returns></returns>
        public static List<SourcingResult> GetDataPVV(System.Int32? BomId)
        {
            List<SourcingResultDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.SourcingResult.GetDataPVV(BomId);
            if (lstDetails == null)
            {
                return new List<SourcingResult>();
            }
            else
            {
                List<SourcingResult> lst = new List<SourcingResult>();
                foreach (SourcingResultDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.SourcingResult obj = new Rebound.GlobalTrader.BLL.SourcingResult();
                    obj.PVVQuestionId = objDetails.PVVQuestionId;
                    obj.PVVQuestionName = objDetails.PVVQuestionName;
                    //edit
                    obj.PVVAnswerId = objDetails.PVVAnswerId;
                    obj.PVVAnswerName = objDetails.PVVAnswerName;
                    obj.HUBRFQNo = objDetails.HUBRFQNo;
                    obj.BomId = objDetails.BomId;

                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }
        /// <summary>
        /// Calls [usp_selectAll_PVVBOM_Question]
        /// </summary>
        /// <param name="BomId"></param>
        /// <returns></returns>
        public static List<SourcingResult> GetDataPVVTemp(string BomIdGenerated)
        {
            List<SourcingResultDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.SourcingResult.GetDataPVVTemp(BomIdGenerated);
            if (lstDetails == null)
            {
                return new List<SourcingResult>();
            }
            else
            {
                List<SourcingResult> lst = new List<SourcingResult>();
                foreach (SourcingResultDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.SourcingResult obj = new Rebound.GlobalTrader.BLL.SourcingResult();
                    obj.PVVQuestionId = objDetails.PVVQuestionId;
                    obj.PVVQuestionName = objDetails.PVVQuestionName;
                    //edit
                    obj.PVVAnswerId = objDetails.PVVAnswerId;
                    obj.PVVAnswerName = objDetails.PVVAnswerName;
                    obj.HUBRFQNo = objDetails.HUBRFQNo;
                    obj.BomId = objDetails.BomId;

                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }
        #endregion

    }
}
