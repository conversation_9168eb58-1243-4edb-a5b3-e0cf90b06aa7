Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Nuggets");

Rebound.GlobalTrader.Site.Controls.Nuggets.BOMMainInfo = function (element) {
    Rebound.GlobalTrader.Site.Controls.Nuggets.BOMMainInfo.initializeBase(this, [
        element,
    ]);
    this._intBOMID = -1;
    this._blnHasRequirement = false;
    this._blnRequestedToPoHub = false;
    this._blnRelease = false;
    this._isAddButtonEnable = true;
    this._isPurchaseHub = false;
    this._intCurrencyNo = -1;
    this._BomCode = "";
    this._BomName = "";
    this._BomCompanyName = "";
    this._BomCompanyNo = 0;
    this._intContact2No = -1;
    this._stringCurrency = null;
    this._inActive = false;
    this._BomContactname = "";
    this._BomContactNo = 0;
    // this._blnPOHub = false;
    this._CurrentSupplier = "";
    this._QuoteRequired = "";
    this._blnAllHasDelDate = false;
    this._blnAllHasProduct = false;
    this._blnCanReleaseAll = false;
    this._blnAllItemHasSourcing = false;
    this.BOMStatus = "";
    this._isClosed = false;

    this._UpdatedBy = null;
    this._blnCanNoBidAll = true;
    this._isNoBidCount = false;
    this._RequestToPOHubBy = -1;
    this._UpdateByPH = -1;
    this._blnReqInValid = false;
    this._ValidMessage = "";
    this._BomClientNo = -1;
    this._blnMerginCanReleaseAll = false;
    this._UnitBuyPrice = null;
    this._UnitSellPrice = null;
    this._IsAssignToMe = false;
    this._blnPVVBOMValidateMessage = "";
    this._PVVBOMCountValid = false;
};

Rebound.GlobalTrader.Site.Controls.Nuggets.BOMMainInfo.prototype = {
    get_intBOMID: function () {
        return this._intBOMID;
    },
    set_intBOMID: function (value) {
        if (this._intBOMID !== value) this._intBOMID = value;
    },
    get_ibtnEdit: function () {
        return this._ibtnEdit;
    },
    set_ibtnEdit: function (value) {
        if (this._ibtnEdit !== value) this._ibtnEdit = value;
    },
    get_ibtnDelete: function () {
        return this._ibtnDelete;
    },
    set_ibtnDelete: function (value) {
        if (this._ibtnDelete !== value) this._ibtnDelete = value;
    },
    get_ibtnExportCSV: function () {
        return this._ibtnExportCSV;
    },
    set_ibtnExportCSV: function (value) {
        if (this._ibtnExportCSV !== value) this._ibtnExportCSV = value;
    },
    get_ibtnExportPurchaseHUB: function () {
        return this._ibtnExportPurchaseHUB;
    },
    set_ibtnExportPurchaseHUB: function (value) {
        if (this._ibtnExportPurchaseHUB !== value)
            this._ibtnExportPurchaseHUB = value;
    },
    get_ibtnNotify: function () {
        return this._ibtnNotify;
    },
    set_ibtnNotify: function (value) {
        if (this._ibtnNotify !== value) this._ibtnNotify = value;
    },
    get_ibtnRelease: function () {
        return this._ibtnRelease;
    },
    set_ibtnRelease: function (value) {
        if (this._ibtnRelease !== value) this._ibtnRelease = value;
    },
    get_blnPOHub: function () {
        return this._blnPOHub;
    },
    set_blnPOHub: function (value) {
        if (this._blnPOHub !== value) this._blnPOHub = value;
    },
    get_ibtnClose: function () {
        return this._ibtnClose;
    },
    set_ibtnClose: function (value) {
        if (this._ibtnClose !== value) this._ibtnClose = value;
    },
    get_ibtnNoBid: function () {
        return this._ibtnNoBid;
    },
    set_ibtnNoBid: function (value) {
        if (this._ibtnNoBid !== value) this._ibtnNoBid = value;
    },
    get_ibtnNote: function () {
        return this._ibtnNote;
    },
    set_ibtnNote: function (value) {
        if (this._ibtnNote !== value) this._ibtnNote = value;
    },
    //[002] start
    get_ibtnViewTree: function () {
        return this._ibtnViewTree;
    },
    set_ibtnViewTree: function (value) {
        if (this._ibtnViewTree !== value) this._ibtnViewTree = value;
    },
    //[002] end
    get_ibtnCrossMatch: function () {
        return this._ibtnCrossMatch;
    },
    set_ibtnCrossMatch: function (value) {
        if (this._ibtnCrossMatch !== value) this._ibtnCrossMatch = value;
    },
    get_IsDiffrentClient: function () {
        return this._IsDiffrentClient;
    },
    set_IsDiffrentClient: function (value) {
        if (this._IsDiffrentClient !== value) this._IsDiffrentClient = value;
    },
    get_IsGSAEditPermission: function () {
        return this._IsGSAEditPermission;
    },
    set_IsGSAEditPermission: function (value) {
        if (this._IsGSAEditPermission !== value) this._IsGSAEditPermission = value;
    },
    get_IsGSA: function () {
        return this._IsGSA;
    },
    set_IsGSA: function (value) {
        if (this._IsGSA !== value) this._IsGSA = value;
    },
    get_ClientId: function () {
        return this._ClientId;
    },
    set_ClientId: function (value) {
        if (this._ClientId !== value) this._ClientId = value;
    },

    addGotData: function (handler) {
        this.get_events().addHandler("GotData", handler);
    },
    removeGotData: function (handler) {
        this.get_events().removeHandler("GotData", handler);
    },
    onGotData: function () {
        var handler = this.get_events().getHandler("GotData");
        if (handler) handler(this, Sys.EventArgs.Empty);
    },

    addCallBeforeRelease: function (handler) {
        this.get_events().addHandler("CallBeforeRelease", handler);
    },
    removeCallBeforeRelease: function (handler) {
        this.get_events().removeHandler("CallBeforeRelease", handler);
    },
    onCallBeforeRelease: function () {
        var handler = this.get_events().getHandler("CallBeforeRelease");
        if (handler) handler(this, Sys.EventArgs.Empty);
    },

    initialize: function () {
        Rebound.GlobalTrader.Site.Controls.Nuggets.BOMMainInfo.callBaseMethod(
            this,
            "initialize"
        );
        this.addRefreshEvent(Function.createDelegate(this, this.getData));
        if (this._ibtnExportCSV)
            $R_IBTN.addClick(
                this._ibtnExportCSV,
                Function.createDelegate(this, this.showExportCSV)
            );
        //   if (this._ibtnExportPurchaseHUB) $R_IBTN.addClick(this._ibtnExportPurchaseHUB, Function.createDelegate(this, this.savePurchaseHUBData));
        //edit form
        if (this._ClientId != 114) {
            if (this._IsDiffrentClient == true) {
                if (this._IsGSA == true) {
                    if (this._IsGSAEditPermission == true) {
                        $("#ctl00_cphMain_ctlLines_ctlDB_pnlLinks").show();
                        $("#ctl00_cphMain_POApprovals_ctlDB_pnlLinks").show();
                        $("#ctl00_cphMain_ctlPOPDFDragDrop_ctlDB_pnlLinks").show();
                        $("#ctl00_cphMain_ctlBOMItems_ctlDB_pnlLinks").show();
                        $("#ctl00_cphMain_ctlBomCSV_ctlDB_pnlLinks").show();
                        $("#ctl00_cphMain_ctlSourcingResults_ctlDB_pnlLinks").show();
                        $("#ctl00_cphMain_ctlPOHubSourcing_ctlDB_ctl13_ibtnSearch").show();

                        $(
                            "#ctl00_cphMain_ctlPOHubSourcing_ctlDB_ctl13_ibtnAddStockInfo"
                        ).show();
                        $(
                            "#ctl00_cphMain_ctlPOHubSourcing_ctlDB_ctl13_ibtnAddOffer"
                        ).show();
                        $(
                            "#ctl00_cphMain_ctlPOHubSourcing_ctlDB_ctl13_ibtnAddTrusted"
                        ).show();
                    } else {
                        $("#ctl00_cphMain_ctlLines_ctlDB_pnlLinks").hide();
                        $("#ctl00_cphMain_POApprovals_ctlDB_pnlLinks").hide();
                        $("#ctl00_cphMain_ctlPOPDFDragDrop_ctlDB_pnlLinks").hide();
                        $("#ctl00_cphMain_ctlBOMItems_ctlDB_pnlLinks").hide();
                        $("#ctl00_cphMain_ctlBomCSV_ctlDB_pnlLinks").hide();
                        $("#ctl00_cphMain_ctlSourcingResults_ctlDB_pnlLinks").hide();
                        $("#ctl00_cphMain_ctlPOHubSourcing_ctlDB_ctl13_ibtnSearch").hide();

                        $(
                            "#ctl00_cphMain_ctlPOHubSourcing_ctlDB_ctl13_ibtnAddStockInfo"
                        ).hide();
                        $(
                            "#ctl00_cphMain_ctlPOHubSourcing_ctlDB_ctl13_ibtnAddOffer"
                        ).hide();
                        $(
                            "#ctl00_cphMain_ctlPOHubSourcing_ctlDB_ctl13_ibtnAddTrusted"
                        ).hide();
                    }
                } else {
                    $("#ctl00_cphMain_ctlLines_ctlDB_pnlLinks").show();
                    $("#ctl00_cphMain_POApprovals_ctlDB_pnlLinks").show();
                    $("#ctl00_cphMain_ctlPOPDFDragDrop_ctlDB_pnlLinks").show();
                    $("#ctl00_cphMain_ctlBOMItems_ctlDB_pnlLinks").show();
                    $("#ctl00_cphMain_ctlBomCSV_ctlDB_pnlLinks").show();
                    $("#ctl00_cphMain_ctlSourcingResults_ctlDB_pnlLinks").show();
                    $("#ctl00_cphMain_ctlPOHubSourcing_ctlDB_ctl13_ibtnSearch").show();

                    $(
                        "#ctl00_cphMain_ctlPOHubSourcing_ctlDB_ctl13_ibtnAddStockInfo"
                    ).show();
                    $("#ctl00_cphMain_ctlPOHubSourcing_ctlDB_ctl13_ibtnAddOffer").show();
                    $(
                        "#ctl00_cphMain_ctlPOHubSourcing_ctlDB_ctl13_ibtnAddTrusted"
                    ).show();
                }
            } else {
                $("#ctl00_cphMain_ctlLines_ctlDB_pnlLinks").show();
                $("#ctl00_cphMain_POApprovals_ctlDB_pnlLinks").show();
                $("#ctl00_cphMain_ctlPOPDFDragDrop_ctlDB_pnlLinks").show();
                $("#ctl00_cphMain_ctlBOMItems_ctlDB_pnlLinks").show();
                $("#ctl00_cphMain_ctlBomCSV_ctlDB_pnlLinks").show();
                $("#ctl00_cphMain_ctlSourcingResults_ctlDB_pnlLinks").show();
                $("#ctl00_cphMain_ctlPOHubSourcing_ctlDB_ctl13_ibtnSearch").show();

                $(
                    "#ctl00_cphMain_ctlPOHubSourcing_ctlDB_ctl13_ibtnAddStockInfo"
                ).show();
                $("#ctl00_cphMain_ctlPOHubSourcing_ctlDB_ctl13_ibtnAddOffer").show();
                $("#ctl00_cphMain_ctlPOHubSourcing_ctlDB_ctl13_ibtnAddTrusted").show();
            }
        }

        if (this._ibtnEdit) {
            $R_IBTN.addClick(
                this._ibtnEdit,
                Function.createDelegate(this, this.showEditForm)
            );
            this._frmEdit = $find(this._aryFormIDs[0]);
            this._frmEdit._intBOMID = this._intBOMID;
            this._frmEdit._BomCode = this._BomCode;
            this._frmEdit._BomName = this._BomName;
            this._frmEdit._BomCompanyName = this._BomCompanyName;
            this._frmEdit._BomCompanyNo = this._BomCompanyNo;
            this._frmEdit.addCancel(Function.createDelegate(this, this.cancelEdit));
            this._frmEdit.addSaveComplete(
                Function.createDelegate(this, this.saveEditComplete)
            );
        }
        //Delete
        if (this._ibtnDelete) {
            $R_IBTN.addClick(
                this._ibtnDelete,
                Function.createDelegate(this, this.showDeleteForm)
            );
            this._frmDelete = $find(this._aryFormIDs[1]);
            this._frmDelete._intBOMID = this._intBOMID;
            this._frmDelete._BomCode = this._BomCode;
            this._frmDelete._BomName = this._BomName;
            this._frmDelete._BomCompanyName = this._BomCompanyName;
            this._frmDelete._BomCompanyNo = this._BomCompanyNo;
            this._frmDelete.addNotConfirmed(
                Function.createDelegate(this, this.cancelDelete)
            );
            this._frmDelete.addSaveComplete(
                Function.createDelegate(this, this.saveDeleteComplete)
            );
        }
        //notify form
        if (this._ibtnNotify) {
            $R_IBTN.addClick(
                this._ibtnNotify,
                Function.createDelegate(this, this.showNotifyForm)
            );
            this._frmNotify = $find(this._aryFormIDs[2]);
            this._frmNotify._BomCode = this._BomCode;
            this._frmNotify._BomName = this._BomName;
            this._frmNotify._BomCompanyName = this._BomCompanyName;
            this._frmNotify._BomCompanyNo = this._BomCompanyNo;
            this._frmNotify.addCancel(
                Function.createDelegate(this, this.cancelNotifyForm)
            );
            this._frmNotify.addSaveComplete(
                Function.createDelegate(this, this.saveNotifyComplete)
            );
            this._frmNotify.addNotConfirmed(
                Function.createDelegate(this, this.hideNotifyForm)
            );
        }
        if (this._ibtnExportPurchaseHUB) {
            //getValidation
            //$R_IBTN.addClick(this._ibtnExportPurchaseHUB, Function.createDelegate(this, this.showConfirmForm));
            $R_IBTN.addClick(
                this._ibtnExportPurchaseHUB,
                Function.createDelegate(this, this.getValidation)
            );
            this._frmConfirm = $find(this._aryFormIDs[3]);
            this._frmConfirm._intBOMID = this._intBOMID;
            this._frmConfirm._BomCode = this._BomCode;
            this._frmConfirm._BomName = this._BomName;
            this._frmConfirm._BomCompanyName = this._BomCompanyName;
            this._frmConfirm._BomCompanyNo = this._BomCompanyNo;
            this._frmConfirm._intContact2No = this._intContact2No;
            this._frmConfirm.addNotConfirmed(
                Function.createDelegate(this, this.cancelConfirm)
            );
            this._frmConfirm.addSaveComplete(
                Function.createDelegate(this, this.saveConfirmComplete)
            );
        }

        if (this._ibtnRelease) {
            $R_IBTN.addClick(
                this._ibtnRelease,
                Function.createDelegate(this, this.showReleaseForm)
            );
            this._frmRelease = $find(this._aryFormIDs[4]);
            this._frmRelease._intBOMID = this._intBOMID;
            this._frmRelease._BomCode = this._BomCode;
            this._frmRelease._BomName = this._BomName;
            this._frmRelease._BomCompanyName = this._BomCompanyName;
            this._frmRelease._BomCompanyNo = this._BomCompanyNo;
            this._frmRelease._UpdatedBy = this._UpdatedBy;
            this._frmRelease.addNotConfirmed(
                Function.createDelegate(this, this.cancelRelease)
            );
            this._frmRelease.addSaveComplete(
                Function.createDelegate(this, this.saveReleaseComplete)
            );
        }

        if (this._ibtnClose) {
            $R_IBTN.addClick(
                this._ibtnClose,
                Function.createDelegate(this, this.showConfirmCloseForm)
            );

            this._frmConfirmClose = $find(this._aryFormIDs[5]);
            this._frmConfirmClose._intBOMID = this._intBOMID;
            this._frmConfirmClose.addNotConfirmed(
                Function.createDelegate(this, this.cancelConfirmClose)
            );
            this._frmConfirmClose.addSaveComplete(
                Function.createDelegate(this, this.saveConfirmCloseComplete)
            );
        }

        if (this._ibtnNoBid) {
            $R_IBTN.addClick(
                this._ibtnNoBid,
                Function.createDelegate(this, this.showNoBidForm)
            );
            this._frmNoBid = $find(this._aryFormIDs[6]);
            this._frmNoBid._intBOMID = this._intBOMID;
            this._frmNoBid._BomCode = this._BomCode;
            this._frmNoBid._BomName = this._BomName;
            this._frmNoBid._BomCompanyName = this._BomCompanyName;
            this._frmNoBid._BomCompanyNo = this._BomCompanyNo;
            this._frmNoBid._UpdatedBy = this._UpdatedBy;
            this._frmNoBid.addNotConfirmed(
                Function.createDelegate(this, this.cancelNoBid)
            );
            this._frmNoBid.addSaveComplete(
                Function.createDelegate(this, this.saveNoBidComplete)
            );
        }
        if (this._ibtnNote) {
            $R_IBTN.addClick(
                this._ibtnNote,
                Function.createDelegate(this, this.showExpediteNoteForm)
            );

            this._frmAddExpediteNote = $find(this._aryFormIDs[7]);
            this._frmAddExpediteNote.addCancel(
                Function.createDelegate(this, this.cancelAddExpediteNoteForm)
            );
            this._frmAddExpediteNote.addSaveComplete(
                Function.createDelegate(this, this.saveAddExpediteNoteComplete)
            );
        }

        // alert($find(this._aryFormIDs[8]))

        //[002] start
        if (this._ibtnViewTree)
            $R_IBTN.addClick(
                this._ibtnViewTree,
                Function.createDelegate(this, this.OpenDocTree)
            );
        //[002] code end
        if (this._ibtnCrossMatch)
            $R_IBTN.addClick(
                this._ibtnCrossMatch,
                Function.createDelegate(this, this.OpenCrossMatch)
            );

        this.getData();
    },

    dispose: function () {
        if (this.isDisposed) return;
        if (this._ibtnEdit) $R_IBTN.clearHandlers(this._ibtnEdit);
        if (this._ibtnNotify) $R_IBTN.clearHandlers(this._ibtnNotify);
        if (this._ibtnDelete) $R_IBTN.clearHandlers(this._ibtnDelete);
        if (this._ibtnClose) $R_IBTN.clearHandlers(this._ibtnClose);
        if (this._frmEdit) this._frmEdit.dispose();
        if (this._frmDelete) this._frmDelete.dispose();
        this._ibtnNotify = null;
        this._intBOMID = null;
        this._ibtnEdit = null;
        this._ibtnDelete = null;
        this._frmEdit = null;
        this._frmDelete = null;
        this._ibtnExportCSV = null;
        this._IsDiffrentClient = null;
        this._ClientId = null;
        this._IsGSAEditPermission = null;
        this._IsGSA = null;
        this._blnHasRequirement = null;
        this._blnPOHub = null;
        this._blnRequestedToPoHub = null;
        this._blnRelease = null;
        this._intCurrencyNo = null;
        this._blnAllHasDelDate = null;
        this._blnAllHasProduct = null;
        this._blnCanReleaseAll = null;
        this._blnAllItemHasSourcing = null;
        this._ibtnClose = null;
        this._ibtnNoBid = null;
        this._ibtnNote = null;
        this._ValidMessage = null;
        this._intContact2No = null;
        this._ibtnCrossMatch = null;
        this._blnMerginCanReleaseAll = null;
        this._UnitBuyPrice = null;
        this._UnitSellPrice = null;
        this._blnPVVBOMValidateMessage = null;
        this._PVVBOMCountValid = null;
        Rebound.GlobalTrader.Site.Controls.Nuggets.BOMMainInfo.callBaseMethod(
            this,
            "dispose"
        );
    },

    getData: function () {
        //$("#ctl00_cphMain_ctlMainInfo_ctlDB_BuyAndPriceMerginesourcing").hide();
        $("#ctl00_cphMain_ctlMainInfo_ctlDB_BuyAndPriceMergine").hide();
        this._blnRequestedToPoHub = false;
        this.getData_Start();
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/BOMMainInfo");
        obj.set_DataObject("BOMMainInfo");
        obj.set_DataAction("GetData");
        obj.addParameter("id", this._intBOMID);
        obj.addDataOK(Function.createDelegate(this, this.getDataOK));
        obj.addError(Function.createDelegate(this, this.getDataError));
        obj.addTimeout(Function.createDelegate(this, this.getDataError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
        //this.onSave();
    },

    getDataOK: function (args) {
        var res = args._result;
        this.BOMStatus = res.BOMStatus;

        if (res.IsPoHub) {
            this._isPurchaseHub = res.IsPoHub;
            this.disableNotifyAndExportButton(!res.IsPoHub);
        }
        this._BomCode = res.Code;
        this._BomName = res.Name;
        this._BomCompanyName = res.Company;
        this._BomCompanyNo = res.CompanyNo;
        this.setFieldValue("ctlCode", $R_FN.setCleanTextValue(res.Code));
        this.setFieldValue("ctlName", $R_FN.setCleanTextValue(res.Name));
        this.setFieldValue("ctlNotes", $R_FN.setCleanTextValue(res.Notes, true));
        this.setFieldValue("ctlAS6081", $R_FN.setCleanTextValue(res.AS6081));
        if (res.AS6081 == "Yes") {
            $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl13_ctlAS6081_lbl").css(
                "background-color",
                "yellow"
            );
        } else {
            $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl13_ctlAS6081_lbl").css(
                "background-color",
                "white"
            );
        }
        this.setFieldValue("ctlInActive", res.InActive);
        this.setFieldValue(
            "ctlCompany",
            $R_FN.setCleanTextValue(res.Company) +
            " (" +
            $R_FN.setCleanTextValue(res.CompanyType) +
            ")" +
            $R_FN.createAdvisoryNotesIcon(res.CompanyAdvisoryNotes)
        );
        this.setFieldValue("ctlContact", $R_FN.setCleanTextValue(res.Contact));
        this.setFieldValue("hidCompanyNo", res.CompanyNo);
        this.setFieldValue("hidContactNo", res.ContactNo);
        this._blnRequestedToPoHub = res.blnReqToPoHub;
        this._blnRelease = res.blnRelease;
        // alert(this._blnRelease);
        if (this._blnRelease == true) {
            $("#ctl00_cphMain_ctlBOMItems_ctlDB_ctl12_ibtnImportSrcReslt_hyp")
                .prop("disabled", true)
                .css("opacity", 0.5);
            $(
                "#ctl00_cphMain_ctlBOMItems_ctlDB_ctl12_ibtnImportSrcReslt_hyp"
            ).addClass("disable-click");
        }
        if (this._blnRelease == false) {
            $("#ctl00_cphMain_ctlBOMItems_ctlDB_ctl12_ibtnImportSrcReslt_hyp")
                .prop("disabled", false)
                .css("opacity", 5.5);
            $(
                "#ctl00_cphMain_ctlBOMItems_ctlDB_ctl12_ibtnImportSrcReslt_hyp"
            ).removeClass("disable-click");
        }
        if (res.IsPoHub == true)
            $("#ctl00_cphMain_ctlBOMItems_ctlDB_ctl12_ibtnImportSrcReslt_hyp").show();
        else if (res.IsPoHub == false)
            $("#ctl00_cphMain_ctlBOMItems_ctlDB_ctl12_ibtnImportSrcReslt_hyp").hide();
        this.setFieldValue(
            "hidDisplayStatus",
            $R_FN.setCleanTextValue(res.BOMStatus)
        );
        this.setFieldValue(
            "ctlCurrency",
            $R_FN.setCleanTextValue(res.CurrencyCode)
        );
        this.setFieldValue("ctlAS9120", res.AS9120);
        if (res.UploadedBy != "") {
            this.setFieldValue("ctlIsFromPrOffer", res.IsFromProspectiveOffer);
            this.setFieldValue("ctlPrOUploadedBy", res.UploadedBy);
        } else {
            $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl13_ctlIsFromPrOffer").hide();
            $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl13_ctlPrOUploadedBy").hide();
        }
        this._intCurrencyNo = res.CurrencyNo;
        //this.setFieldValue("ctl_Currency", $R_FN.setCleanTextValue(res.Currency_Code));
        this._stringCurrency = res.Currency_Code;
        this._intCurrencyNo = res.CurrencyNo;
        this.setDLUP(res.DLUP);
        this._inActive = res.InActive;
        this._CurrentSupplier = res.CurrentSupplier;
        this._QuoteRequired = res.QuoteRequired;
        this._blnAllItemHasSourcing = res.AllItemHasSourcing;

        this._UpdatedBy = res.UpdatedBy;
        this._isNoBidCount = res.isNoBidCount;
        this.setFieldValue(
            "ctlCurrentSupplier",
            $R_FN.setCleanTextValue(res.CurrentSupplier)
        );
        this.setFieldValue(
            "ctlQuoteRequired",
            $R_FN.setCleanTextValue(res.QuoteRequired)
        );
        //New field added on 7-6-2016(Releasedby,Requestedby)
        this.setFieldValue(
            "ctlRequestedby",
            $R_FN.setCleanTextValue(res.Requestedby)
        );
        this.setFieldValue(
            "ctlReleasedby",
            $R_FN.setCleanTextValue(res.Releasedby)
        );
        this._isClosed = res.IsClosed;
        this.setFieldValue(
            "ctlAssignTo",
            $R_FN.setCleanTextValue(res.AssignedUser)
        );
        this._RequestToPOHubBy = res.RequestToPOHubBy;
        this._UpdateByPH = res.UpdateByPH;
        this._blnReqInValid = res.IsReqInValid;
        this._ValidMessage = res.ValidMessage;
        //PVVBOM Validation
        this._blnPVVBOMValidateMessage = res.PVVBOMValidateMessage;
        this._PVVBOMCountValid = res.PVVBOMCountValid;
        //end
        this.setFieldValue(
            "ctlContact2",
            $R_FN.setCleanTextValue(res.Contact2Name)
        );
        this.setFieldValue("hidContact2No", res.Contact2Id);
        this._intContact2No = res.Contact2Id;
        this.setFieldValue("hidReqSalesperson", res.ReqSalesPerson);
        this.setFieldValue("hidSupportTeamMemberNo", res.SupportTeamMemberNo);
        if (res.PurchasingNotes != null && res.PurchasingNotes != "") {
            this.setFieldValue(
                "ctlPurchasingNotes",
                $R_FN.setCleanTextValue($R_FN.showYellowText(res.PurchasingNotes))
            );
        } else {
            this.setFieldValue(
                "ctlPurchasingNotes",
                $R_FN.setCleanTextValue(res.PurchasingNotes)
            );
        }
        //        this.setFieldValue("ctlCurrentSupplier", $R_FN.setCleanTextValue(res.CurrentSupplier));
        //        this.setFieldValue("ctlQuoteRequired", $R_FN.setCleanTextValue(res.QuoteRequired));

        //        if ((res.blnBomCount == 0) || (res.blnBomCount == '0')) {
        //            $R_IBTN.enableButton(this._ibtnRelease, false);
        //        }
        this._isAddButtonEnable = !res.blnReqToPoHub && !res.InActive;
        this._BomClientNo = res.ClientNo;
        this._IsAssignToMe = res.IsAssignToMe;
        this.getDataOK_End();
        this.onGotData();
        //alert(!res.blnReqToPoHub && !res.InActive);

        this.enableButtons(!res.InActive);
    },

    getDataError: function (args) {
        this.showError(true, args.get_ErrorMessage());
    },

    enableButtons: function (bln) {
        if (bln) {
            // if (this._ibtnEdit) $R_IBTN.enableButton(this._ibtnEdit, !this._blnRequestedToPoHub && !this._isClosed);
            if (this._ibtnEdit)
                $R_IBTN.enableButton(
                    this._ibtnEdit,
                    !this._isClosed && !this._blnPOHub && this._IsAssignToMe == true
                );
            //code start for Add a link to send directly to supplier on HUBRFQ from Hub Side
            if (this._isPurchaseHub != true) {
                if (this._ibtnNotify)
                    $R_IBTN.enableButton(
                        this._ibtnNotify,
                        this._blnHasRequirement &&
                        !this._blnRequestedToPoHub &&
                        !this._inActive &&
                        !this._isClosed &&
                        this._IsAssignToMe == true
                    );
            }
            //end
            else {
                if (this._ibtnNotify)
                    $R_IBTN.enableButton(
                        this._ibtnNotify,
                        this._blnHasRequirement &&
                        this._blnPOHub &&
                        this._blnRequestedToPoHub &&
                        !this._inActive &&
                        !this._isClosed &&
                        this._IsAssignToMe == true
                    );
            }
            if (this._ibtnExportPurchaseHUB)
                $R_IBTN.enableButton(
                    this._ibtnExportPurchaseHUB,
                    this._blnHasRequirement &&
                    !this._blnPOHub &&
                    !this._blnRequestedToPoHub &&
                    !this._inActive &&
                    !this._isClosed &&
                    this._IsAssignToMe == true
                );
            if (this._ibtnRelease)
                $R_IBTN.enableButton(
                    this._ibtnRelease,
                    this._blnHasRequirement &&
                    this._blnPOHub &&
                    !this._blnRelease &&
                    !this._inActive &&
                    this._blnAllItemHasSourcing &&
                    !this._isClosed &&
                    this._IsAssignToMe == true
                );
            if (this._ibtnExportCSV)
                $R_IBTN.enableButton(this._ibtnExportCSV, this._IsAssignToMe == true);
            if (this._ibtnClose)
                $R_IBTN.enableButton(
                    this._ibtnClose,
                    !this._isClosed && this._IsAssignToMe == true
                );
            if (this._ibtnNoBid)
                $R_IBTN.enableButton(
                    this._ibtnNoBid,
                    this._blnHasRequirement &&
                    this._blnPOHub &&
                    !this._inActive &&
                    this._isNoBidCount &&
                    !this._isClosed &&
                    this._IsAssignToMe == true
                );
            if (this._ibtnNote)
                $R_IBTN.enableButton(this._ibtnNote, this._IsAssignToMe == true);
        } else {
            //alert(this._blnRelease);
            if (this._ibtnEdit) $R_IBTN.enableButton(this._ibtnEdit, false);
            if (this._ibtnNotify) $R_IBTN.enableButton(this._ibtnNotify, false);
            if (this._ibtnExportPurchaseHUB)
                $R_IBTN.enableButton(this._ibtnExportPurchaseHUB, false);
            if (this._ibtnRelease) $R_IBTN.enableButton(this._ibtnRelease, false);
            //if (this._ibtnExportCSV) $R_IBTN.enableButton(this._ibtnExportCSV, false);
            if (this._ibtnClose)
                $R_IBTN.enableButton(this._ibtnClose, this._isClosed);
            //  if (this._ibtnNote) $R_IBTN.enableButton(this._ibtnNote, false);
            // $("#ctl00_cphMain_ctlBOMItems_ctlDB_ctl12_ibtnImportSrcReslt_hyp").prop('disabled', this._blnRelease).css('opacity', 0.5);
        }
    },

    showEditForm: function () {
        if (
            (this._BomName.includes(".xlsx") &&
                this._BomName.split("-").pop() != this._BomClientNo) ||
            (this._BomName.includes(".xls") &&
                this._BomName.split("-").pop() != this._BomClientNo) ||
            (this._BomName.includes(".csv") &&
                this._BomName.split("-").pop() != this._BomClientNo)
        ) {
            this._frmEdit.setFieldValue("ctlName", this._BomName);
        } else if (this._BomName.split("-").pop() == this._BomClientNo) {
            var lastIndex = this._BomName.lastIndexOf("-");
            this._frmEdit._BomName = this._BomName.substring(0, lastIndex);
            this._frmEdit.setFieldValue(
                "ctlName",
                this._BomName.substring(0, lastIndex)
            );
        } else {
            this._frmEdit._BomName = this._BomName;
            this._frmEdit.setFieldValue("ctlName", this._BomName);
        }
        var lastIndex = this._BomName.lastIndexOf("-");
        this._frmEdit._intBOMID = this._intBOMID;
        this._frmEdit._BomCode = this._BomCode;
        this._frmEdit._BomCompanyName = this._BomCompanyName;
        this._frmEdit._BomCompanyNo = this._BomCompanyNo;
        this._frmEdit.setFieldValue("ctlCode", this.getFieldValue("ctlCode"));
        //this._frmEdit.setFieldValue("ctlName", this.getFieldValue("ctlName").substring(0, lastIndex));
        //this._frmEdit.setFieldValue("ctlName", this._BomName.substring(0, lastIndex));
        //this._frmEdit.setFieldValue("ctlName", this.getFieldValue("ctlName"));
        this._frmEdit.setFieldValue("ctlNotes", this.getFieldValue("ctlNotes"));
        this._frmEdit.setFieldValue(
            "ctlInActive",
            this.getFieldValue("ctlInActive")
        );
        this._frmEdit.getFieldDropDownData("ctlCurrency");
        this._frmEdit.setFieldValue("ctlCurrency", this._intCurrencyNo);
        this._frmEdit.setFieldValue("ctlCompany", this.getFieldValue("ctlCompany"));
        this._frmEdit.getFieldControl("ctlContact")._intCompanyID =
            this.getFieldValue("hidCompanyNo");
        this._frmEdit.getFieldDropDownData("ctlContact");
        this._frmEdit.setFieldValue(
            "ctlContact",
            this.getFieldValue("hidContactNo")
        );
        this._frmEdit.setFieldValue("ctlCurrentSupplier", this._CurrentSupplier);
        this._frmEdit.setFieldValue("ctlQuoteRequired", this._QuoteRequired);
        this._frmEdit.setFieldValue("ctlAS9120", this.getFieldValue("ctlAS9120"));
        this._frmEdit.getFieldDropDownData("ctlSalesman");
        this._frmEdit.setFieldValue("ctlSalesman", this._intContact2No);
        this._frmEdit._blnRequestedToPoHub = this._blnRequestedToPoHub;
        this.showForm(this._frmEdit, true);
    },

    hideEditForm: function () {
        this.showForm(this._frmEdit, false);
    },

    cancelEdit: function () {
        this.hideEditForm();
    },

    saveEditComplete: function () {
        this.hideEditForm();
        this.showSavedOK(true, $R_RES.ChangesSavedSuccessfully);
        this.getData();
    },
    saveConfirmComplete: function () {
        this.hideConfirmForm();
        //this.showSavedOK(true, $R_RES.ChangesSavedSuccessfully);
        this.showSavedOK(
            true,
            "HUBRFQ has been sent for Price Request successfully."
        );
        this.getData();
    },

    saveConfirmCloseComplete: function () {
        this.hideConfirmCloseForm();
        //this.showSavedOK(true, $R_RES.ChangesSavedSuccessfully);
        this.showSavedOK(true, "HUBRFQ has been closed successfully.");
        this.getData();
    },

    showDeleteForm: function () {
        this._frmDelete._intBOMID = this._intBOMID;
        this._frmDelete._BomCode = this._BomCode;
        this._frmDelete._BomName = this._BomName;
        this._frmDelete._BomCompanyName = this._BomCompanyName;
        this._frmDelete._BomCompanyNo = this._BomCompanyNo;
        this._frmDelete.setFieldValue("ctlName", this.getFieldValue("ctlName"));
        this.showForm(this._frmDelete, true);
    },
    showConfirmForm: function () {
        this._frmConfirm._intBOMID = this._intBOMID;
        this._frmConfirm._BomCode = this._BomCode;
        this._frmConfirm._BomName = this._BomName;
        this._frmConfirm._BomCompanyName = this._BomCompanyName;
        this._frmConfirm._BomCompanyNo = this._BomCompanyNo;
        this._frmConfirm._blnReqInValid = this._blnReqInValid;
        this._frmConfirm._ValidMessage = this._ValidMessage;
        this._frmConfirm._intContact2No = this._intContact2No;
        this._frmConfirm._PVVBOMCountValid = this._PVVBOMCountValid;
        this._frmConfirm._blnPVVBOMValidateMessage = this._blnPVVBOMValidateMessage;
        this.showForm(this._frmConfirm, true);
    },
    hideConfirmForm: function () {
        this.showForm(this._frmConfirm, false);
    },

    cancelConfirm: function () {
        this.hideConfirmForm();
    },
    hideDeleteForm: function () {
        this.showForm(this._frmDelete, false);
    },

    showReleaseForm: function () {
        this.onCallBeforeRelease();
        //alert(this._blnCanReleaseAll);
        if (this._blnCanReleaseAll == true) {
            this.getSerialDetail();
            this._frmRelease._intBOMID = this._intBOMID;
            this._frmRelease._BomCode = this._BomCode;
            this._frmRelease._BomName = this._BomName;
            this._frmRelease._BomCompanyName = this._BomCompanyName;
            this._frmRelease._BomCompanyNo = this._BomCompanyNo;
            this._frmRelease._UpdatedBy = this._UpdatedBy;
            this._frmRelease._RequestToPOHubBy = this._RequestToPOHubBy;
            this._frmRelease._reqSalespeson = this.getFieldValue("hidReqSalesperson");
            this._frmRelease._SupportTeamMemberNo = this.getFieldValue(
                "hidSupportTeamMemberNo"
            );
            this.showForm(this._frmRelease, true);
        } else {
            $("#ctl00_cphMain_ctlMainInfo_ctlDB_BuyAndPriceMergine").hide();
        }
    },
    //code added for check buy and price diff
    getSerialDetail: function () {
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/BOMMainInfo");
        obj.set_DataObject("BOMMainInfo");
        obj.set_DataAction("GetAllSourcingResult");
        obj.addParameter("Bomid", this._intBOMID);
        obj.addDataOK(Function.createDelegate(this, this.getAllReleaseOK));
        obj.addError(Function.createDelegate(this, this.getAllReleaseError));
        obj.addTimeout(Function.createDelegate(this, this.getAllReleaseError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    getAllReleaseOK: function (args) {
        this._UnitBuyPrice = null;
        this._UnitSellPrice = null;
        res = args._result;
        var intCount = 0;
        if (res.Results.length > 0) {
            var aryData = null;
            var row = null;
            this._blnMerginCanReleaseAll = false;
            for (var i = 0; i < res.Results.length; i++) {
                row = res.Results[i];
                this._UnitBuyPrice = row.UnitBuyPrice;
                this._UnitSellPrice = row.UnitSellPrice;
                if (this._UnitBuyPrice != null && this._UnitSellPrice != null) {
                    if (this._UnitBuyPrice >= this._UnitSellPrice) {
                        this._blnMerginCanReleaseAll = true;
                    }
                }
                row = null;
                aryData = null;
            }
            if (this._blnMerginCanReleaseAll == false) {
                $("#ctl00_cphMain_ctlMainInfo_ctlDB_BuyAndPriceMergine").hide();
                this._frmRelease._intBOMID = this._intBOMID;
                this._frmRelease._BomCode = this._BomCode;
                this._frmRelease._BomName = this._BomName;
                this._frmRelease._BomCompanyName = this._BomCompanyName;
                this._frmRelease._BomCompanyNo = this._BomCompanyNo;
                this._frmRelease._UpdatedBy = this._UpdatedBy;
                this._frmRelease._RequestToPOHubBy = this._RequestToPOHubBy;
                this._frmRelease._reqSalespeson =
                    this.getFieldValue("hidReqSalesperson");
                this._frmRelease._SupportTeamMemberNo = this.getFieldValue(
                    "hidSupportTeamMemberNo"
                );
                this.showForm(this._frmRelease, true);
            } else {
                $("#ctl00_cphMain_ctlBOMItems_ctlDB_imgRefresh").trigger("click");
                $("#ctl00_cphMain_ctlMainInfo_ctlDB_BuyAndPriceMergine").show();
                this._frmRelease._intBOMID = this._intBOMID;
                this._frmRelease._BomCode = this._BomCode;
                this._frmRelease._BomName = this._BomName;
                this._frmRelease._BomCompanyName = this._BomCompanyName;
                this._frmRelease._BomCompanyNo = this._BomCompanyNo;
                this._frmRelease._UpdatedBy = this._UpdatedBy;
                this._frmRelease._RequestToPOHubBy = this._RequestToPOHubBy;
                this._frmRelease._reqSalespeson =
                    this.getFieldValue("hidReqSalesperson");
                this._frmRelease._SupportTeamMemberNo = this.getFieldValue(
                    "hidSupportTeamMemberNo"
                );
                this.showForm(this._frmRelease, true);
                //$("#ctl00_cphMain_ctlBOMItems_ctlDB_imgRefresh").trigger('click');
            }
        } else {
            $("#ctl00_cphMain_ctlMainInfo_ctlDB_BuyAndPriceMergine").hide();
        }
    },

    getAllReleaseError: function (args) { },

    showLoadingAllRelease: function (blnShow) { },

    showAllReleaseError: function (blnShow, strMessage) { },
    //code end

    showNoBidForm: function () {
        // this.onCallBeforeRelease();
        if (this._blnCanNoBidAll) {
            this._frmNoBid._intBOMID = this._intBOMID;
            this._frmNoBid._BomCode = this._BomCode;
            this._frmNoBid._BomName = this._BomName;
            this._frmNoBid._BomCompanyName = this._BomCompanyName;
            this._frmNoBid._BomCompanyNo = this._BomCompanyNo;
            this._frmNoBid._UpdatedBy = this._UpdatedBy;
            this._frmNoBid.setFieldValue("ctlNotes", "");
            //rp-225 salesman no passed to nobid function
            this._frmNoBid._SalesmanNo = this.getFieldValue("hidReqSalesperson");
            //alert(this._frmNoBid._SalesmanNo);
            this.showForm(this._frmNoBid, true);
        }
    },

    saveNoBidComplete: function () {
        this.hideNoBidForm();
        this.showSavedOK(true, $R_RES.ChangesSavedSuccessfully);
        this.getData();
    },
    hideNoBidForm: function () {
        this.showForm(this._frmNoBid, false);
    },

    cancelNoBid: function () {
        this.hideNoBidForm();
    },
    showConfirmCloseForm: function () {
        this._frmConfirmClose._intBOMID = this._intBOMID;
        this.showForm(this._frmConfirmClose, true);
    },
    hideConfirmCloseForm: function () {
        this.showForm(this._frmConfirmClose, false);
    },

    cancelConfirmClose: function () {
        this.hideConfirmCloseForm();
    },

    saveReleaseComplete: function () {
        this.hideReleaseForm();
        this.showSavedOK(true, $R_RES.ChangesSavedSuccessfully);
        this.getData();
        //if (this._ibtnRelease) $R_IBTN.enableButton(this._ibtnRelease, false);
    },
    hideReleaseForm: function () {
        this.showForm(this._frmRelease, false);
    },
    cancelRelease: function () {
        this.hideReleaseForm();
    },
    cancelDelete: function () {
        this.hideDeleteForm();
    },
    showExportCSV: function () {
        ////        alert(this._stringCurrency);
        this.getData_Start();
        //this.showSaving(true);
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/BOMItems");
        obj.set_DataObject("BOMItems");
        obj.set_DataAction("ExportToCSV");
        obj.addParameter("id", this._intBOMID);
        obj.addParameter("currency_Code", this._stringCurrency);
        //alert(this.getFieldValue("ctlCurrency"));
        obj.addDataOK(Function.createDelegate(this, this.exportCSV_OK));
        obj.addError(Function.createDelegate(this, this.exportCSV_Error));
        obj.addTimeout(Function.createDelegate(this, this.exportCSV_Error));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
        //this.onSave();
    },
    exportCSV_OK: function (args) {
        var res = args._result;
        if (res.Filename) {
            //add the date to the file to force the latest version to be returned (not from cache)
            var dt = new Date();
            location.href = String.format("{0}?t={1}", res.Filename, dt.getTime());
            dt = null;
        }
        this.getDataOK_End();
    },

    exportCSV_Error: function (args) {
        this.showError(true, args.get_ErrorMessage());
    },

    saveDeleteComplete: function () {
        $R_FN.navigateBack();
    },

    savePurchaseHUBData: function () {
        this.getData_Start();
        //this.showSaving(true);
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/BOMMainInfo");
        obj.set_DataObject("BOMMainInfo");
        obj.set_DataAction("savePurchaseHUBData");
        obj.addParameter("id", this._intBOMID);
        obj.addParameter("BomCode", this._BomCode);
        obj.addParameter("BomName", this._BomName);
        obj.addParameter("BomCompanyName", this._BomCompanyName);
        obj.addParameter("BomCompanyNo", this._BomCompanyNo);

        //        obj.addParameter("BomContactname", this._BomContactname);
        //        obj.addParameter("BomContactNo", this._BomContactNo);

        obj.addDataOK(Function.createDelegate(this, this.getDataOK));
        obj.addError(Function.createDelegate(this, this.getDataError));
        obj.addTimeout(Function.createDelegate(this, this.getDataError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
        // this.onSave();
    },

    showNotifyForm: function () {
        this._frmNotify._intBOMID = this._intBOMID;
        this._frmNotify._BomCode = this._BomCode;
        this._frmNotify._BomName = this._BomName;
        this._frmNotify._BomCompanyName = this._BomCompanyName;
        this._frmNotify._BomCompanyNo = this._BomCompanyNo;
        this._frmNotify._intCompanyID = this.getFieldValue("hidCompanyNo");
        this._frmNotify._stringCurrency = this._stringCurrency;
        this.showForm(this._frmNotify, true);
    },

    hideNotifyForm: function () {
        this.showForm(this._frmNotify, false);
    },

    cancelNotifyForm: function () {
        this.showForm(this._frmNotify, false);
        this.showContent(true);
    },

    saveNotifyComplete: function () {
        this.showForm(this._frmNotify, false);
        this.showSavedOK(true, "BOM notification sent successfully");
    },
    disableNotifyAndExportButton: function (isDisabled) {
        //code start for Add a link to send directly to supplier on HUBRFQ from Hub Side / comment for show into hub side
        // if (this._ibtnNotify) $R_IBTN.showButton(this._ibtnNotify, isDisabled);
        //end
    },

    showExpediteNoteForm: function () {
        this._frmAddExpediteNote._intBOMID = this._intBOMID;
        this._frmAddExpediteNote._HUBRFQName = this.getFieldValue("ctlName");
        this._frmAddExpediteNote._intRequestedby = this._RequestToPOHubBy;
        this._frmAddExpediteNote._intUpdateByPH = this._UpdateByPH;
        this._frmAddExpediteNote._HubrfqCode = this.getFieldValue("ctlCode");
        this._frmAddExpediteNote._CompanyNo = this.getFieldValue("hidCompanyNo");
        this._frmAddExpediteNote.setFieldValue("ctlExpediteNotes", "");
        this._frmAddExpediteNote._intContact2No = this._intContact2No;
        //[001] Code Start
        this._frmAddExpediteNote._companyname = this.getFieldValue("ctlCompany");
        this._frmAddExpediteNote._contactname = this.getFieldValue("ctlContact");
        this._frmAddExpediteNote._reqSalespeson =
            this.getFieldValue("hidReqSalesperson");
        //[001] Code End
        this.showForm(this._frmAddExpediteNote, true);
    },
    saveAddExpediteNoteComplete: function () {
        this.showForm(this._frmAddExpediteNote, false);
        this.showSavedOK(true, $R_RES.ChangesSavedSuccessfully);
        this.getData();
    },
    cancelAddExpediteNoteForm: function () {
        this.showForm(this._frmAddExpediteNote, false);
        this.showContent(true);
    },
    enableDisableReleaseButton: function (isDisabled) {
        // alert(this._blnHasRequirement && this._blnPOHub && !this._blnRelease && isDisabled);
        // if (this._ibtnRelease) $R_IBTN.enableButton(this._ibtnRelease, this._blnHasRequirement && this._blnPOHub && !this._blnRelease && isDisabled);
    },
    getValidation: function () {
        this.getData_Start();
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/BOMMainInfo");
        obj.set_DataObject("BOMMainInfo");
        obj.set_DataAction("GetData");
        obj.addParameter("id", this._intBOMID);
        obj.addDataOK(Function.createDelegate(this, this.getValidationOK));
        obj.addError(Function.createDelegate(this, this.getValidationError));
        obj.addTimeout(Function.createDelegate(this, this.getValidationError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
        //this.onSave();
    },

    getValidationOK: function (args) {
        var res = args._result;

        this._blnReqInValid = res.IsReqInValid;
        this._ValidMessage = res.ValidMessage;
        this._blnPVVBOMValidateMessage = res.PVVBOMValidateMessage;
        this._PVVBOMCountValid = res.PVVBOMCountValid;
        this.getDataOK_End();
        this.onGotData();
        this.showConfirmForm();
    },

    getValidationError: function (args) {
        this.showError(true, args.get_ErrorMessage());
    },
    //[002] code start
    OpenDocTree: function () {
        //$R_FN.openDocumentTree(this._BomName, "BOM");
        $R_FN.openDocumentTree(this._intBOMID, "BOM", this._BomName);
    },
    //[002] code end
    OpenCrossMatch: function () {
        //$R_FN.openDocumentTree(this._BomName, "BOM");
        $R_FN.openCrossMatch(this._intBOMID, this._BomCode);
    },
};

Rebound.GlobalTrader.Site.Controls.Nuggets.BOMMainInfo.registerClass(
    "Rebound.GlobalTrader.Site.Controls.Nuggets.BOMMainInfo",
    Rebound.GlobalTrader.Site.Controls.Nuggets.Base
);
