Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.ItemSearch");Rebound.GlobalTrader.Site.Controls.ItemSearch.SourcingBulkEditLog=function(n){Rebound.GlobalTrader.Site.Controls.ItemSearch.SourcingBulkEditLog.initializeBase(this,[n]);this._sourcingType="";this._partSearch=""};Rebound.GlobalTrader.Site.Controls.ItemSearch.SourcingBulkEditLog.prototype={initialize:function(){Rebound.GlobalTrader.Site.Controls.ItemSearch.SourcingBulkEditLog.callBaseMethod(this,"initialize");this.addSetupData(Function.createDelegate(this,this.doSetupData));this.addGetDataComplete(Function.createDelegate(this,this.doGetDataComplete))},dispose:function(){this.isDisposed||(this._sourcingType=null,this._partSearch=null,Rebound.GlobalTrader.Site.Controls.ItemSearch.SourcingBulkEditLog.callBaseMethod(this,"dispose"))},doSetupData:function(){this._objData.set_PathToData("controls/ItemSearch/SourcingBulkEditLog");this._objData.set_DataObject("SourcingBulkEditLog");this._objData.set_DataAction("GetData");this._objData.addParameter("SourcingType",this._sourcingType);this._objData.addParameter("PartNo",this.getFieldValue("ctlPartNo"));this._objData.addParameter("EditedBy",this.getFieldValue("ctlEditedBy"));this._objData.addParameter("EditedDateFrom",this.getFieldValue("ctlEditedDateFrom"));this._objData.addParameter("EditedDateTo",this.getFieldValue("ctlEditedDateTo"))},doGetDataComplete:function(){for(var n,i,t=0,r=this._objResult.Results.length;t<r;t++)n=this._objResult.Results[t],i=[n.BatchNo,$R_FN.setCleanTextValue(n.PartNo),n.Action,n.OldValue,$R_FN.setCleanTextValue(n.UpdatedBy),$R_FN.setCleanTextValue(n.UpdatedDate)],this._tblResults.addRow(i,n.ID,!1),i=null,n=null},clearFilterInput:function(){this.setFieldValue("ctlPartNo","");this.setFieldValue("ctlEditedBy",0);this.setFieldValue("ctlEditedDateFrom",null);this.setFieldValue("ctlEditedDateTo",null);this.getField("ctlEditedDateFrom")._txt.value="";this.getField("ctlEditedDateTo")._txt.value=""}};Rebound.GlobalTrader.Site.Controls.ItemSearch.SourcingBulkEditLog.registerClass("Rebound.GlobalTrader.Site.Controls.ItemSearch.SourcingBulkEditLog",Rebound.GlobalTrader.Site.Controls.ItemSearch.Base,Sys.IDisposable);