<%@ Control Language="C#" CodeBehind="CRMALines_Delete.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Forms.CRMALines_Delete" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>

<ReboundUI_Form:DesignBase ID="ctlDB" runat="server" ShowRequiredFieldsExplanation="false" AllowQuickHelp="false">
	<Explanation><%=Functions.GetGlobalResource("FormExplanations", "CRMALines_Delete")%></Explanation>
	
	<Content>
	
		<ReboundUI_Table:Form id="frm" runat="server">
		
			<ReboundUI_Form:FormField id="ctlCustomerRMA" runat="server" FieldID="lblCustomerRMA" ResourceTitle="CustomerRMANo" >
				<Field><asp:Label ID="lblCustomerRMA" runat="server" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlCustomer" runat="server" FieldID="lblCustomer" ResourceTitle="Customer" >
				<Field><asp:Label ID="lblCustomer" runat="server" /></Field>
			</ReboundUI_Form:FormField>
			
			<ReboundUI_Form:FormField id="ctlPartNo" runat="server" FieldID="lblPartNo" ResourceTitle="PartNo">
	            <Field><asp:Label ID="lblPartNo" runat="server" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlQuantity" runat="server" FieldID="lblQuantity" ResourceTitle="Quantity">
				<Field><asp:Label ID="lblQuantity" runat="server" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlReason" runat="server" FieldID="lblReason" ResourceTitle="Reason">
				<Field><asp:Label ID="lblReason" runat="server" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlConfirm" runat="server" FieldID="ctlConfirmation" ResourceTitle="Confirm">
				<Field><ReboundUI:Confirmation ID="ctlConfirmation" runat="server" /></Field>
			</ReboundUI_Form:FormField>
			
		</ReboundUI_Table:Form>
		
	</Content>
	
</ReboundUI_Form:DesignBase>
