Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");Rebound.GlobalTrader.Site.Controls.DropDowns.GlobalCountryList=function(n){Rebound.GlobalTrader.Site.Controls.DropDowns.GlobalCountryList.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.DropDowns.GlobalCountryList.prototype={get_blnIncludeSelected:function(){return this._blnIncludeSelected},set_blnIncludeSelected:function(n){this._blnIncludeSelected!==n&&(this._blnIncludeSelected=n)},get_intGlobalLoginClientNo:function(){return this._intGlobalLoginClientNo},set_intGlobalLoginClientNo:function(n){this._intGlobalLoginClientNo!==n&&(this._intGlobalLoginClientNo=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.DropDowns.GlobalCountryList.callBaseMethod(this,"initialize");this.addSetupDataCall(Function.createDelegate(this,this.setupDataCall));this.addGetDataOK(Function.createDelegate(this,this.dataCallOK))},dispose:function(){this.isDisposed||(this._blnIncludeSelected=null,this._intGlobalLoginClientNo=null,Rebound.GlobalTrader.Site.Controls.DropDowns.GlobalCountryList.callBaseMethod(this,"dispose"))},setupDataCall:function(){this._objData.set_PathToData("controls/DropDowns/GlobalCountryList");this._objData.set_DataObject("GlobalCountryList");this._objData.set_DataAction("GetData");this._objData.addParameter("IncludeSelected",this._blnIncludeSelected);this._objData.addParameter("GlobalLoginClientNo",this._intGlobalLoginClientNo)},dataCallOK:function(){var t=this._objData._result,n;if(t.GlobalCountryLists)for(n=0;n<t.GlobalCountryLists.length;n++)this.addOption(t.GlobalCountryLists[n].Name,t.GlobalCountryLists[n].ID,t.GlobalCountryLists[n].HighRiskContent)}};Rebound.GlobalTrader.Site.Controls.DropDowns.GlobalCountryList.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.GlobalCountryList",Rebound.GlobalTrader.Site.Controls.DropDowns.Base);