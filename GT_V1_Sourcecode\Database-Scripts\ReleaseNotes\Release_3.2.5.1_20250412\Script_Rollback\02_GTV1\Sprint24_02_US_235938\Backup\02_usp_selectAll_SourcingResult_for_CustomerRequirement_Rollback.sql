﻿
GO

/****** Object:  StoredProcedure [dbo].[usp_selectAll_SourcingResult_for_CustomerRequirement]    Script Date: 3/21/2025 4:02:57 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE OR ALTER PROCEDURE [dbo].[usp_selectAll_SourcingResult_for_CustomerRequirement]                                            
@CustomerRequirementId int        
/*      
 *SN:001 Action: Altered  Action By: Ab<PERSON>av <PERSON>a Dated:27-12-2021 Comment: Change order by condition.     
 -- [001]  Soorya Vyas   RP-2440/RP-2513  13/10/2023 Customer Requirement Sourcing Results nugget limited to 6 months
 */                                                 
AS                
DECLARE @ClientNo int            
SET @ClientNo=(SELECT Clientno from tbCustomerRequirement where CustomerRequirementId=@CustomerRequirementId)      
    
DECLARE @FROMDATE DATETIME                                
DECLARE @ENDDATE DATETIME        
SET @FROMDATE=dbo.ufn_get_date_from_datetime(DATEADD(month,-6,getdate()))                                
SET @ENDDATE =dbo.ufn_get_date_from_datetime(getdate())         
    
    
SELECT  sr.SourcingResultId                                                  
 , sr.CustomerRequirementNo                                                  
 , sr.SourcingTable                                                  
 , sr.SourcingTableItemNo                                                  
 , sr.TypeName                                                  
 , sr.FullPart                                                  
 , sr.Part                                                  
 , sr.ManufacturerNo                                                  
 , sr.DateCode                                                  
 , sr.ProductNo                                                  
 , sr.PackageNo                                                  
 , sr.Quantity                                                  
 , CASE WHEN sr.PartWatchMatch=1 AND sr.POHubCompanyNo IS not NULL AND @ClientNo!=114 THEN 0 ELSE sr.Price  END Price                                         
 , sr.CurrencyNo                                                  
 , sr.OriginalEntryDate                                                  
 , sr.Salesman                                                  
 , sr.SupplierNo                                                  
 , sr.UpdatedBy                                                  
 , sr.DLUP                                                  
 , sr.ROHS                                                  
 , sr.OfferStatusNo                                                  
 , sr.OfferStatusChangeDate                                                  
 , sr.OfferStatusChangeLoginNo                                                  
 , sr.Notes                                                  
 , mf.ManufacturerName                                                  
 , mf.ManufacturerCode                                                  
 , cu.CurrencyCode                                                  
 --, co.CompanyName AS SupplierName  
 ,CASE WHEN coc.CompanyName IS NOT NULL and @ClientNo!=114  THEN coc.CompanyName ELSE  co.CompanyName end AS SupplierName   -- soorya     
 , pr.ProductName                                                  
 , pr.ProductDescription                                                  
 , pk.PackageName                                                  
 , pk.PackageDescription                                                  
 , lg.EmployeeName AS SalesmanName                                                  
 , l2.EmployeeName AS OfferStatusChangeEmployeeName                                                  
 , sr.POHubCompanyNo                                                  
 , cop.CompanyName AS POHubSupplierName                                                     
 , cr.POHubReleaseBy                                              
 , sr.ClientCompanyNo                                                
 , coc.CompanyName AS ClientSupplierName                                          
 , cop.UPLiftPrice                                         
 , sr.ClientCurrencyNo                                       
 , sr.IsSoCreated                                
 ,sr.Closed                       
 ,sr.IsSoCreated                                 
  ,tm.TermsName                                
  ,tm.IsApplyPOBankFee                                
  ,sr.SourceRef                                  
  , sr.ActualPrice as BuyPrice                                
  , cub.CurrencyCode as BuyCurrencyCode                        
  , sr.ActualCurrencyNo                               
  , sr.EstimatedShippingCost                               
  , ct.Name AS SupplierType                             
  , sr.SupplierPrice                             
   , Region.RegionName                             
   , ml.MSLLevel                         
   ,sr.PartWatchMatch                      
  , CASE WHEN sr.PartWatchMatch=1 AND sr.clientNo IS NOT NULL AND sr.ClientNo !=@ClientNo THEN 1 ELSE 0 END AS DiffrentClientOffer        
  , tc.ClientCode                   
  ,case when sr.OfferStatusChangeDate is null then OriginalEntryDate else sr.OfferStatusChangeDate end as OfferDate      
FROM    dbo.tbSourcingResult sr                                                  
LEFT                                
JOIN dbo.tbManufacturer mf                                                   
 ON sr.ManufacturerNo   = mf.ManufacturerID                                                  
LEFT                                                   
JOIN dbo.tbCurrency cu                         
 ON sr.CurrencyNo    = cu.CurrencyID                                                  
LEFT                                                   
JOIN dbo.tbCompany co                                                   
 ON sr.SupplierNo    = co.CompanyId                                                  
LEFT                                                   
JOIN dbo.tbProduct pr                                                   
 ON sr.ProductNo    = pr.ProductId                                                  
LEFT                                                   
JOIN dbo.tbPackage pk                                                   
 ON sr.PackageNo    = pk.PackageId                            
LEFT                                                   
JOIN dbo.tbLogin lg                                                   
 ON sr.Salesman     = lg.LoginId                                        
LEFT                                                   
JOIN dbo.tbLogin l2                                                   
 ON sr.OfferStatusChangeLoginNo = l2.LoginId                                                  
LEFT                                                       
JOIN dbo.tbCompany cop                                                       
 ON sr.POHubCompanyNo    = cop.CompanyId                                             
LEFT                                                     
JOIN dbo.tbCompany coc                                                     
 ON sr.ClientCompanyNo    = coc.CompanyId                              
LEFT JOIN  dbo.tbCustomerRequirement cr on cr.CustomerRequirementId = sr.CustomerRequirementNo                               
LEFT JOIN tbclient tc on sr.ClientNo=tc.ClientId                                           
LEFT JOIN tbCompanyType ct on ct.CompanyTypeId = cop.TypeNo                            
Left Join tbregion Region on Region.regionId=sr.regionNo                                    
left Join tbCompany c on sr.POHubCompanyNo=  c.CompanyId                                  
left join tbTerms tm on c.POTermsNo=tm.TermsId                                
LEFT JOIN tbCurrency cub on isnull(sr.ActualCurrencyNo,0) = cub.CurrencyId                             
left join tbMSLLevel ml on sr.MSLLevelNo= ml.MSLLevelId                                        
WHERE   sr.CustomerRequirementNo = @CustomerRequirementId                              
AND ISNULL(sr.IsSoftDelete,0)=0                        
--AND (sr.Closed IS NULL OR sr.Closed=0)                   
--and isnull(sr.IsSoCreated,0)=0                                              
AND(sr.PartWatchMatch=1 OR (sr.POHubCompanyNo IS NULL or (sr.POHubCompanyNo IS not NULL and isnull(sr.IsReleased,0)=1))   )                                      
--ORDER BY sr.SourcingResultId       
--Code start 001      
--AND (dbo.ufn_get_date_from_datetime(ISNULL(sr.OfferStatusChangeDate, sr.DLUP)) between @FROMDATE   AND  @ENDDATE)  -- [001]                           
--ORDER BY ISNULL(sr.PartWatchMatch,0) ASC      
Order by OfferDate  desc  --COde end 001
GO


