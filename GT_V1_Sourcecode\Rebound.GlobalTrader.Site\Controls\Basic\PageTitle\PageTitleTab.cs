using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using System.Collections;

namespace Rebound.GlobalTrader.Site.Controls {

	/// <summary>
	/// Tab for the page title bar
	/// </summary>
	[DefaultProperty("")]
	[ToolboxData("<{0}:PageTitleTab runat=server></{0}:PageTitleTab>")]
	public class PageTitleTab : WebControl, INamingContainer {

		#region Locals
		private Panel _pnlTab;
		private HyperLink _hypLink;
		private Panel _pnlTabContent;
		#endregion

		#region Properties

		/// <summary>
		/// Title for the tab
		/// </summary>
		private String _title = "";
		public String Title {
			get { return _title; }
			set { _title = value; }
		}

		/// <summary>
		/// Link Href
		/// </summary>
		private String _href = "";
		public String Href {
			get { return _href; }
			set { _href = value; }
		}

		/// <summary>
		/// Is this one selected?
		/// </summary>
		private Boolean _isSelected = false;
		public Boolean IsSelected {
			get { return _isSelected; }
			set { _isSelected = value; }
		}

		/// <summary>
		/// Position offset
		/// </summary>
		private int _positionOffset = 0;
		public int PositionOffset {
			get { return _positionOffset; }
			set { _positionOffset = value; }
		}

		/// <summary>
		/// Return reference to the hyperlink
		/// </summary>
		private string _strHyperLinkClientID;
		public string HyperLinkClientID {
			get { return _strHyperLinkClientID; }
			set { _strHyperLinkClientID = value; }
		}

		/// <summary>
		/// Client ID of panel
		/// </summary>
		private string _strPanelClientID;
		public string PanelClientID {
			get { return _strPanelClientID; }
			set { _strPanelClientID = value; }
		}
	

		/// <summary>
		/// Is this initially invisible?
		/// </summary>
		private bool _blnIsInitiallyInvisible = false;
		public bool IsInitiallyInvisible {
			get { return _blnIsInitiallyInvisible; }
			set { _blnIsInitiallyInvisible = value; }
		}

		private string _strOnClientClick = "";
		public string OnClientClick {
			get { return _strOnClientClick; }
			set { _strOnClientClick = value; }
		}
	

		#endregion

		/// <summary>
		/// on init
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
			EnsureChildControls();
			base.OnInit(e);
		}

		/// <summary>
		/// create controls
		/// </summary>
		protected override void CreateChildControls() {
			_pnlTab = ControlBuilders.CreatePanel("pnlTab");
			_pnlTab.ID = "tab";
			Controls.Add(_pnlTab);
			_pnlTab.Controls.Add(ControlBuilders.CreatePanel("tabLeft"));
			_pnlTabContent = ControlBuilders.CreatePanelInsideParent(_pnlTab, "tabContent");
			_hypLink = ControlBuilders.CreateHyperLinkInsideParent(_pnlTabContent);
			_hypLink.ID = "hyp";
			_pnlTab.Controls.Add(ControlBuilders.CreatePanel("tabRight"));
			_strHyperLinkClientID = _hypLink.ClientID;
			_strPanelClientID = _pnlTab.ClientID;
			base.CreateChildControls();
		}

		/// <summary>
		/// render contents
		/// </summary>
		/// <param name="writer"></param>
		protected override void Render(HtmlTextWriter writer) {
			_hypLink.Text = this.Title;
			_hypLink.NavigateUrl = this.Href;
			if (_strOnClientClick != "") _hypLink.Attributes.Add("onclick", _strOnClientClick);
			_pnlTab.CssClass = (this.IsSelected) ? "tabSelected" : "tab";
			if (_blnIsInitiallyInvisible) _pnlTab.CssClass += " invisible";
			_pnlTab.Attributes.Add("style", string.Format("position:relative; right:{0}px;", this.PositionOffset));
			base.Render(writer);
		}

	}
}