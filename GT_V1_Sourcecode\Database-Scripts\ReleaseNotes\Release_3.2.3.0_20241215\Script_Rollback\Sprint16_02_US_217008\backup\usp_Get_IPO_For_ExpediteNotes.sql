﻿  
  
    
    
CREATE OR ALTER PROCEDURE usp_Get_IPO_For_ExpediteNotes     
@POLineNos nvarchar(max)          
AS     
BEGIN    
    
SELECT      
        pol.PurchaseOrderNo            
      , po.PurchaseOrderNumber       
   , pol.POSerialNo    
   , pol.Part    
   , ipo.InternalPurchaseOrderNumber     
   , ipo.InternalPurchaseOrderId             
      , po.Buyer as <PERSON><PERSON><PERSON>uyer<PERSON>o    
   , ipo.Buyer as <PERSON>OBuyerNo           
      , lg.EmployeeName AS BuyerName          
   , lg1.EmployeeName AS IPOBuyerName              
      , po.CompanyNo          
      , poco.CompanyName      
   , (select top 1 MailGroupId from tbMailGroup where Name='IPO Purchasing' and ClientNo=ipo.ClientNo) as   MailGroupId    
   , pol.Quantity    
   , pol.DeliveryDate    
   ,po.SupportTeamMemberNo   
FROM   tbPurchaseOrderLine pol    
LEFT JOIN tbPurchaseOrder po ON pol.PurchaseOrderNo = po.PurchaseOrderId            
LEFT JOIN tbLogin lg ON lg.LoginId = po.Buyer            
left JOIN tbInternalPurchaseOrderLine ipol ON pol.PurchaseOrderLineId = ipol.PurchaseOrderLineNo           
left JOIN tbInternalPurchaseOrder ipo ON po.PurchaseOrderId = ipo.PurchaseOrderNo          
LEFT JOIN tbLogin lg1 ON lg1.LoginId = ipo.Buyer            
LEFT JOIN tbCompany co ON co.CompanyId = ipo.CompanyNo      
LEFT JOIN tbCompany poco ON poco.CompanyId = po.CompanyNo              
LEFT JOIN tbContact ct on ct.ContactId = po.ContactNo    
WHERE pol.PurchaseOrderLineId  IN (SELECT String FROM dbo.ufn_splitString(@POLineNos,','))    
and ipo.InternalPurchaseOrderId is not null    
    
    
END    
    
  
  