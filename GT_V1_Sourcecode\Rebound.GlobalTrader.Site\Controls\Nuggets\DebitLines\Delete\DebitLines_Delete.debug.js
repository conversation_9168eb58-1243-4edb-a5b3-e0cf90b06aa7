///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.DebitLines_Delete = function(element) { 
	Rebound.GlobalTrader.Site.Controls.Forms.DebitLines_Delete.initializeBase(this, [element]);
	this._intLineID = -1;
	this._blnLineIsService = false;
};

Rebound.GlobalTrader.Site.Controls.Forms.DebitLines_Delete.prototype = {

	get_intLineID: function() { return this._intLineID; }, 	set_intLineID: function(value) { if (this._intLineID !== value)  this._intLineID = value; }, 

	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.Forms.DebitLines_Delete.callBaseMethod(this, "initialize");
		this.addShown(Function.createDelegate(this, this.formShown));
	},

	dispose: function() { 
		if (this.isDisposed) return;
		if (this._ctlConfirm) this._ctlConfirm.dispose();
		this._ctlConfirm = null;
		this._intLineID = null;
		this._blnLineIsService = null;
		Rebound.GlobalTrader.Site.Controls.Forms.DebitLines_Delete.callBaseMethod(this, "dispose");
	},

	formShown: function() {
		if (this._blnFirstTimeShown) {
			this._ctlConfirm = this.getFieldComponent("ctlConfirm");
			this._ctlConfirm.addClickYesEvent(Function.createDelegate(this, this.yesClicked));
			this._ctlConfirm.addClickNoEvent(Function.createDelegate(this, this.noClicked));
		}
		this.showField("ctlPartNo", !this._blnLineIsService);
		this.showField("ctlManufacturer", !this._blnLineIsService);
		this.showField("ctlDateCode", !this._blnLineIsService);
		this.showField("ctlPackage", !this._blnLineIsService);
		this.showField("ctlProduct", !this._blnLineIsService);
		this.showField("ctlService", this._blnLineIsService);
		this.showField("ctlServiceDescription", this._blnLineIsService);
	},

	yesClicked: function() {
		this.showSaving(true);
		var obj = new Rebound.GlobalTrader.Site.Data();
		obj.set_PathToData("Controls/Nuggets/DebitLines");
		obj.set_DataObject("DebitLines");
		obj.set_DataAction("Delete");
		obj.addParameter("id", this._intLineID);
		obj.addDataOK(Function.createDelegate(this, this.saveComplete));
		obj.addError(Function.createDelegate(this, this.saveError));
		obj.addTimeout(Function.createDelegate(this, this.saveError));
		$R_DQ.addToQueue(obj);
		$R_DQ.processQueue();
		obj = null;
		this.onSave();
	},

	noClicked: function() {
		this.showSaving(false);
		this.onNotConfirmed();
	},

	saveError: function(args) {
		this.showSaving(false);
		this._strErrorMessage = args._errorMessage;
		this.onSaveError();
	},

	saveComplete: function(args) {
		this.showSaving(false);
		if (args._result.Result == true) {
			this.onSaveComplete();
		} else {
			this._strErrorMessage = args._errorMessage;
			this.onSaveError();
		}
	}
	
};

Rebound.GlobalTrader.Site.Controls.Forms.DebitLines_Delete.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.DebitLines_Delete", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
