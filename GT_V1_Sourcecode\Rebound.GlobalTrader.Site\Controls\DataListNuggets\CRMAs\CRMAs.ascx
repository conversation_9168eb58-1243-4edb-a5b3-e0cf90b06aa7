<%@ Control Language="C#" CodeBehind="CRMAs.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.DataListNuggets.CRMAs" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Nugget:DesignBase ID="ctlDB" runat="server" BoxType="List">
	<Filters>
		<ReboundUI_DataListNugget:Filter ID="ctlFilter" runat="server">
			<FieldsLeft>
				<ReboundUI_FilterDataItemRow:Numerical id="ctlCRMANo" runat="server" ResourceTitle="CRMANo" FilterField="CRMANo" />
				<ReboundUI_FilterDataItemRow:TextBox id="ctlPart" runat="server" ResourceTitle="PartNo" FilterField="Part" />
		        <ReboundUI_FilterDataItemRow:CheckBox id="ctlRecentOnly" runat="server" ResourceTitle="RecentOnly" FilterField="RecentOnly" DefaultValue="true" />
		       <ReboundUI_FilterDataItemRow:CheckBox id="ctlPohubOnly" runat="server" ResourceTitle="PohubOnly" FilterField="PohubOnly" DefaultValue="true"  />
		        <ReboundUI_FilterDataItemRow:CheckBox id="ctlIncludeReceived" runat="server" ResourceTitle="IncludeReceived" FilterField="IncludeReceived" />
				<ReboundUI_FilterDataItemRow:TextBox id="ctlCompanyName" runat="server" ResourceTitle="CompanyName" FilterField="CMName" />
				<ReboundUI_FilterDataItemRow:TextBox id="ctlContactName" runat="server" ResourceTitle="ContactName" FilterField="Contact" />
			</FieldsLeft>
			<FieldsRight>
				<ReboundUI_FilterDataItemRow:Numerical id="ctlInvNo" runat="server" ResourceTitle="InvoiceNo" FilterField="InvNo" />
				<ReboundUI_FilterDataItemRow:TextBox id="ctlCRMANotes" runat="server" ResourceTitle="Notes" InitialSearchType="Contains" FilterField="CRMANotes" />
				<ReboundUI_FilterDataItemRow:DateSelect id="ctlCRMADateFrom" runat="server" ResourceTitle="CustomerRMADateFrom" FilterField="CRMADateFrom" />
				<ReboundUI_FilterDataItemRow:DateSelect id="ctlCRMADateTo" runat="server" ResourceTitle="CustomerRMADateTo" FilterField="CRMADateTo" />
				<ReboundUI_FilterDataItemRow:DropDown id="ctlSalesmanName" runat="server" ResourceTitle="SalesmanName" DropDownType="Employee" DropDownAssembly="Rebound.GlobalTrader.Site" FilterField="SalesmanName" />
			</FieldsRight>
		</ReboundUI_DataListNugget:Filter>
	</Filters>
</ReboundUI_Nugget:DesignBase>
