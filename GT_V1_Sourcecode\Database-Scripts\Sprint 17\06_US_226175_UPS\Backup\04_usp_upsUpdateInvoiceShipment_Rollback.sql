﻿
GO

/****** Object:  StoredProcedure [dbo].[usp_upsUpdateInvoiceShipment]    Script Date: 11/20/2024 12:04:22 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].[usp_upsUpdateInvoiceShipment]                                            
(                                                  
  @vInvoiceIDs Varchar(Max),                                                  
  @vAirwayBillNo Varchar(Max)=Null,                                        
  @vShippingCost float=Null,                                        
  @vBoxes int=null,                                        
  @vWeight float=null,                                        
  @vDimensionalWeight float=null,                                 
  @vIntials varchar(max)=null,                              
  @vClientNo int=null,                                       
  @vRetMsg Varchar(100) Out,                                                  
  @vIsSuccess bit Out  ,                      
  @UPSAccountNo VARCHAR(40) = NULL                                                
)                                                  
/*                                                  
CREATED BY : PANKAJ KUMAR                                                      
CREATED ON : 24-11-2011                                                      
PURPOSE    : To update invoice shipment status                       
Modify by Vinay: 16 March 2015: Add billing currency           
Modify by Rahil: 16 Feb 2023: Modifying shippingcost and freight logic          
===========================================================================================
TASK      			UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[BUG-203597]		Trung Pham			29-Jul-2024		UPDATE			Add ShippingSurchargeValue = null as suggestion in Bug
[BUG-203597]		Trung Pham			30-Aug-2024		UPDATE			Comment ShippingSurchargeValue = null as suggestion in Bug
===========================================================================================
*/                                                  
AS                                                          
BEGIN                                                        
                                                 
 BEGIN TRY                                                
                                     
   Declare @vLowestInvoice int                                    
   Declare @vCountInvoice Int                                    
                                     
   SET @vLowestInvoice = (Select Min(Cast(RTrim(LTrim(String)) As Int)) From dbo.[ufn_splitString](@vInvoiceIDs,','))                                    
   SET @vCountInvoice = (Select COUNT(*) From dbo.[ufn_splitString](@vInvoiceIDs,','))                
   
   print @vLowestInvoice
   print @vCountInvoice
  INSERT INTO tbTempUPSInvoiceUpdateLog (              
    Stage,              
    DLUP,              
    InvoiceNo,              
    ClientNo,              
    InvoiceNumber,              
    Freight,              
    ShippingCost,              
    ShippingSurchargePercent,              
    ShippingSurcharge,              
    IsAppShippingSurcharge,              
    ImportedShippingCost,              
    CompanyNo,              
    CompanyWaiveFreight,              
    ShipViaNo,              
    ShipVia,              
    ShipViaFreightUplift,              
    ShipViaAddSurcharge,              
    ShipViaMatchFreightToShipCost,              
    ActualUPSCost,              
    SPCalled,            
    CountryName,            
    CountryShippingSurchargePercent              
  )              
  SELECT              
    'ups SP Start'      AS Stage,              
    getdate()      AS DLUP,              
    i.InvoiceId      AS InvoiceNo,              
    i.ClientNo      AS ClientNo,              
    i.InvoiceNumber     AS InvoiceNumber,              
    i.Freight      AS Freight,              
    i.ShippingCost     AS ShippingCost,              
    i.ShippingSurchargePercent  AS ShippingSurchargePercent,              
    i.ShippingSurchargeValue  AS ShippingSurcharge,              
    i.IsAppShippingSurcharge  AS IsAppShippingSurcharge,              
    i.ImportedShippingCost   AS ImportedShippingCost,              
    i.CompanyNo      AS CompanyNo,              
    cy.ShippingCharge    AS CompanyWaiveFreight,              
    i.ShipViaNo      AS ShipViaNo,              
    sv.ShipViaName     AS ShipVia,              
    sv.FreightPercentage   AS ShipViaFreightUplift,              
    sv.IncreaseFreight    AS ShipViaAddSurcharge,              
    sv.IsSameAsShipCost    AS ShipViaMatchFreightToShipCost,                  @vShippingCost     AS ActualUPSCost,              
    'usp_upsUpdateInvoiceShipment_Test' as SPCalled,            
 cnt.CountryName as CountryName,            
    round(isnull(cnt.ShippingSurchargePercent,0),2) as ShippingSurchargePercent                         
  FROM  tbInvoice i  WITH (NoLock)              
  LEFT JOIN tbCompany cy WITH (NoLock) ON cy.CompanyId = i.CompanyNo              
  LEFT JOIN tbShipVia sv WITH (NoLock) ON sv.ShipViaId = i.ShipViaNo            
  LEFT join tbAddress adr with (nolock) on i.ShipToAddressNo = adr.AddressId            
  LEFT join tbCountry cnt with (nolock) on cnt.CountryId = adr.CountryNo            
               
  WHERE i.ClientNo = @vClientNo              
  AND  i.InvoiceNumber = @vLowestInvoice                                  
    
	-----------------------------------------------------------------------------

   IF ISNULL(@vDimensionalWeight,0)=0                                  
      SET @vDimensionalWeight = NULL                                
                                    
   --Calculate shipping cost price in base currency                              
 DECLARE @ShippingCost float                                
 DECLARE @ExchangeRate float                               
 DECLARE @CurrencyNo INT                          
 DECLARE @BillingCurrency VARCHAR(30)                      
                        
  --Get Billing currect from UPS account table                      
 SELECT @BillingCurrency = BillingCurrency                      
 FROM tbupsShippingAccount  WHERE UPPER(UPSAccount) = UPPER(@UPSAccountNo)                      
                                
  --select @CurrencyNo=CurrencyNo from tbClient where ClientId=@vClientNo                                
                              
 IF @BillingCurrency IS NULL                      
 BEGIN                      
   select top 1 @CurrencyNo=CurrencyId from tbCurrency where ClientNo = @vClientNo                            
   and Sell=1 and Inactive =0 and upper(CurrencyCode) = 'GBP' order by CurrencyId                             
 END                      
 ELSE                      
 BEGIN                      
   select top 1 @CurrencyNo=CurrencyId from tbCurrency where ClientNo = @vClientNo                            
   and Sell=1 and Inactive =0 and upper(CurrencyCode) = @BillingCurrency order by CurrencyId                       
 END                            
                                
 SET @ExchangeRate= dbo.ufn_get_exchange_rate(@CurrencyNo, GETDATE())                                
 SET @ShippingCost=(ISNULL(@vShippingCost,0)/ISNULL(@ExchangeRate,1))                
           
 Declare @InvoiceShippingCost float --           
 Declare @InvoiceFreight float --           
 Declare @InvoiceShipSurChargeValue float --          
 Declare @SalesOrderShippingCost float --          
 Declare @SalesOrderFreight float --          
 --Declare @ShipViaShippingCost float          
 DECLARE @IsSameAsShipping BIT --          
 Declare @UpliftPer float   --                   
 --Declare @shippingCost float          
 Declare @TotalShippingCost float          
 Declare @ShippingUplift float          
 Declare @ShippingUpliftValue float          
 Declare @CountryShippingSurchargePer float --          
 Declare @CountryShippingSurchargeValue float --          
 Declare @TotalFreight float          
 Declare @SetupShipViaCharge float --          
 Declare @SetupShipViaCost float --          
 Declare @CustWave bit = 0 --          
 declare @CompanyNo int --          
 declare @InvoiceCurrency int --          
 declare @invoicedate datetime   --           
 declare @CalculateShippingCost float          
 declare @invSubTotalCost  float --          
 Declare @ImportShippingCost float              
           
 set @ImportShippingCost = @ShippingCost          
           
 --print '@ImportShippingCost : ' + cast(@ImportShippingCost as nvarchar(max))          
  SELECT @InvoiceShippingCost = isnull(ShippingCost,0),                
     @InvoiceFreight= round(isnull(Freight,0),2),                
     @CompanyNo = CompanyNo,                      
     @InvoiceShipSurChargeValue = round(isnull(ShippingSurchargeValue,0),2),                      
     @InvoiceCurrency= CurrencyNo,               
     @invoicedate= isnull(InvoiceDate,getdate())                       
 FROM tbInvoice Where InvoiceNumber = @vLowestInvoice and ClientNo=@vClientNo            
          
  Set @InvoiceShippingCost = @InvoiceShippingCost-@InvoiceShipSurChargeValue        
        
 select @CustWave = isnull(ShippingCharge,0) from tbCompany where CompanyId = @CompanyNo               
           
           
  SELECT  @IsSameAsShipping = ISNULL(IsSameAsShipCost,0),              
   @UpliftPer= isnull(FreightPercentage,0) ,            
   @SetupShipViaCost =round(isnull(Cost,0),2),               
   @SetupShipViaCharge = round(isnull(Charge,0),2)                      
 FROM tbShipVia  a join tbInvoice b on a.ShipViaId = b.ShipViaNo Where b.InvoiceNumber = @vLowestInvoice and b.ClientNo=@vClientNo               
          
           
  SELECT @SalesOrderFreight =  round(a.Freight,2) ,          
  @SalesOrderShippingCost =  round(a.ShippingCost,2)          
 FROM tbSalesOrder a join tbInvoice b on a.SalesOrderId = b.SalesOrderNo          
 Where b.InvoiceNumber = @vLowestInvoice and b.ClientNo=@vClientNo           
          
  Select @CountryShippingSurchargePer = round(isnull(inv.ShippingSurchargePercent,0),2)        --- Country surcharge percentage            
 from tbCountry cnt with (nolock)            
 inner join tbAddress adr with (nolock) on cnt.CountryId = adr.CountryNo            
 inner join tbInvoice inv with (nolock) on inv.ShipToAddressNo = adr.AddressId            
 where inv.InvoiceNumber = @vLowestInvoice and inv.ClientNo = @vClientNo             
           
           
          
SELECT @CalculateShippingCost =Max(v) FROM (VALUES (isnull(@SalesOrderShippingCost,0)), (isnull(@InvoiceShippingCost,0)),          
(isnull(@SetupShipViaCost,0)), (isnull(@ShippingCost,0))) AS value(v)          
--print '@CalculateShippingCost : ' + cast(@CalculateShippingCost as nvarchar(max))          
          
-- select @ShippingUpliftValue = (isnull(@CalculateShippingCost,0) + isnull(@ShippingUplift,0))/ 100          
select @ShippingUpliftValue = (isnull(@CalculateShippingCost,0) * isnull(@UpliftPer,0))/ 100          
          
Select @invSubTotalCost = Sum(B.PriceCal) from (            
 Select *            
 from (            
  Select invLine.invoiceno,inv.InvoiceDate,inv.ClientNo,inv.TaxNo,            
  invlineAloc.Quantity, invLine.Price, (invlineAloc.Quantity * invLine.Price) PriceCal            
  from             
  tbInvoiceLine invLine with (nolock)            
  inner join tbInvoiceLineAllocation invlineAloc with (nolock) on invLine.InvoiceLineId = invlineAloc.InvoiceLineNo            
  inner join tbinvoice inv with (nolock) on inv.invoiceid = invLine.invoiceno            
  where inv.InvoiceNumber = @vLowestInvoice and inv.ClientNo = @vClientNo            
 )            
 A            
 )             
 B Group by B.InvoiceNo           
          
 select @CountryShippingSurchargeValue =  (isnull(@invSubTotalCost,0)* @CountryShippingSurchargePer) / 100   
          
 select @TotalShippingCost = @CalculateShippingCost + @ShippingUpliftValue + @CountryShippingSurchargeValue          
 --print '@Shipp ingUpliftValue : ' + cast(@ShippingUpliftValue as nvarchar(max))          
 --print '@CountryShippingSurchargeValue : ' + cast(@CountryShippingSurchargeValue as nvarchar(max))          
 --print '@TotalShippingCost : ' + cast(@TotalShippingCost as nvarchar(max))          
 -- Freight calculation :          
 SELECT @TotalFreight =Max(v) FROM (VALUES (isnull(@SalesOrderFreight ,0)), (isnull(@InvoiceFreight,0)),(isnull(@SetupShipViaCharge,0))) AS value(v)          
           

--print '@TotalFreight : ' + cast(@TotalFreight as nvarchar(max))          
          
 IF @IsSameAsShipping = 1           
 BEGIN          
 /*Changes done for RP-2890*/
 --set @TotalFreight = isnull(@TotalShippingCost,0)  
 set @TotalShippingCost =  isnull(@CalculateShippingCost,0)
 set @TotalFreight = isnull(@CalculateShippingCost,0)
 
 END  
 
  --print '@IsSameAsShipping : ' + cast(@IsSameAsShipping as nvarchar(max))          
  --print '@TotalFreight : ' + cast(@TotalFreight as nvarchar(max))          
 IF @CustWave = 1          
 BEGIN          
 set @TotalFreight = 0          
 END          
   --print '@CustWave : ' + cast(@CustWave as nvarchar(max))          
  --print '@TotalFreight : ' + cast(@TotalFreight as nvarchar(max))          
          
       
   UPDATE tbInvoice Set                                     
          [AirWayBill] = @vAirwayBillNo,                                         
          [ImportedShippingCost]= @ImportShippingCost ,                      
          [ShippingCost] = @TotalShippingCost, ---+ ISNULL(ShippingSurchargeValue,0), -- SG 30.04.2019 - Uncommented out temporarily until requirements revised.    
		  ShippingSurchargeValue = case when @IsSameAsShipping = 1 then 0 else  @CountryShippingSurchargeValue end,
          [Boxes] = @vBoxes,                                         
          [Weight] = @vWeight,                                         
          [DimensionalWeight] = @vDimensionalWeight,                                         
          [IsUpsInvoiceExported] = 1   ,                                
          [Notes]= isnull([Notes],'') + ' ' + '<br>|#Packed By:'+ @vIntials  + '#|'  ,                         
    Freight= @TotalFreight           
   Where InvoiceNumber = @vLowestInvoice                           
   and ClientNo=@vClientNo                     
                                    
   IF @vCountInvoice > 1                                    
   BEGIN                                                 
  Update tbInvoice Set                                         
   [AirWayBill] = @vAirwayBillNo,                      
   -- setting all other invoice values as nullother than lower invoice to prevent double charge from cusotmer
   [ShippingCost] = null,
   --------
   --ShippingSurchargeValue = null,
   ----------------------------------------------------------                      
   [IsUpsInvoiceExported] = 1                                                  
  Where ClientNo=@vClientNo  and InvoiceNumber In                      
  (            
   Select String From dbo.[ufn_splitString](@vInvoiceIDs,',') Where String Not In(@vLowestInvoice)            
  )                         
   END                              
                                                 
 IF @@ERROR > 0                                    
    BEGIN                                                  
   Set @vRetMsg = 'Shipment Updation Failed!'                                                
   Set @vIsSuccess = 0                                                  
    END                                   
 ELSE                                              
    BEGIN                                                  
   Set @vRetMsg = 'Shipment Updated Successfully!'                                   
   Set @vIsSuccess = 1                                                  
    END                 
               
  INSERT INTO tbTempUPSInvoiceUpdateLog (              
    Stage,              
    DLUP,              
    InvoiceNo,              
    ClientNo,              
    InvoiceNumber,              
    Freight,              
    ShippingCost,              
    ShippingSurchargePercent,              
    ShippingSurcharge,              
    IsAppShippingSurcharge,              
    ImportedShippingCost,              
    CompanyNo,              
    CompanyWaiveFreight,              
    ShipViaNo,              
    ShipVia,              
    ShipViaFreightUplift,              
    ShipViaAddSurcharge,              
    ShipViaMatchFreightToShipCost,              
    ActualUPSCost,              
    SPCalled,            
    CountryName,            
    CountryShippingSurchargePercent            
  )              
  SELECT              
    'ups SP End'      AS Stage,              
    getdate()      AS DLUP,              
    i.InvoiceId      AS InvoiceNo,              
    i.ClientNo      AS ClientNo,              
    i.InvoiceNumber     AS InvoiceNumber,              
    i.Freight      AS Freight,      
    i.ShippingCost     AS ShippingCost,              
    i.ShippingSurchargePercent  AS ShippingSurchargePercent,              
    i.ShippingSurchargeValue  AS ShippingSurcharge,              
    i.IsAppShippingSurcharge  AS IsAppShippingSurcharge,              
    i.ImportedShippingCost   AS ImportedShippingCost,              
    i.CompanyNo      AS CompanyNo,              
    cy.ShippingCharge    AS CompanyWaiveFreight,              
    i.ShipViaNo      AS ShipViaNo,              
    sv.ShipViaName     AS ShipVia,              
    sv.FreightPercentage   AS ShipViaFreightUplift,              
    sv.IncreaseFreight    AS ShipViaAddSurcharge,              
    sv.IsSameAsShipCost    AS ShipViaMatchFreightToSurcharge,              
    @vShippingCost     AS ActualUPSCost,              
    'usp_upsUpdateInvoiceShipment' as SPCalled,            
    cnt.CountryName as CountryName,            
    round(isnull(cnt.ShippingSurchargePercent,0),2) as ShippingSurchargePercent                         
  FROM  tbInvoice i  WITH (NoLock)             
  LEFT JOIN tbCompany cy WITH (NoLock) ON cy.CompanyId = i.CompanyNo              
  LEFT JOIN tbShipVia sv WITH (NoLock) ON sv.ShipViaId = i.ShipViaNo            
  LEFT join tbAddress adr with (nolock) on i.ShipToAddressNo = adr.AddressId            
  LEFT join tbCountry cnt with (nolock) on cnt.CountryId = adr.CountryNo              
  WHERE i.ClientNo = @vClientNo              
  AND  i.InvoiceNumber = @vLowestInvoice              
                                             
 END TRY                                                 
 BEGIN CATCH                                      
    Set @vRetMsg = ERROR_MESSAGE()                          
    Set @vIsSuccess = 0                                                           
 END CATCH                                                
                                                                                           
END           

GO


