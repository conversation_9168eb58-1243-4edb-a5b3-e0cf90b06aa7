///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Pages.Warehouse");

Rebound.GlobalTrader.Site.Pages.Warehouse.Warehouse = function(el) { 
	Rebound.GlobalTrader.Site.Pages.Warehouse.Warehouse.initializeBase(this, [el]);
};

Rebound.GlobalTrader.Site.Pages.Warehouse.Warehouse.prototype = {

    get_ctlEllipses_ReceivePOs: function() { return this._ctlEllipses_ReceivePOs; }, set_ctlEllipses_ReceivePOs: function(v) { if (this._ctlEllipses_ReceivePOs !== v) this._ctlEllipses_ReceivePOs = v; },
    get_txtReceivePO: function() { return this._txtReceivePO; }, set_txtReceivePO: function(v) { if (this._txtReceivePO !== v) this._txtReceivePO = v; },
    get_ibtnReceivePOs: function() { return this._ibtnReceivePOs; }, set_ibtnReceivePOs: function(v) { if (this._ibtnReceivePOs !== v) this._ibtnReceivePOs = v; },
    get_ctlEllipses_ShipSOs: function() { return this._ctlEllipses_ShipSOs; }, set_ctlEllipses_ShipSOs: function(v) { if (this._ctlEllipses_ShipSOs !== v) this._ctlEllipses_ShipSOs = v; },
    get_txtShipSO: function() { return this._txtShipSO; }, set_txtShipSO: function(v) { if (this._txtShipSO !== v) this._txtShipSO = v; },
    get_ibtnShipSOs: function() { return this._ibtnShipSOs; }, set_ibtnShipSOs: function(v) { if (this._ibtnShipSOs !== v) this._ibtnShipSOs = v; },
    get_ctlEllipses_ReceiveCRMAs: function() { return this._ctlEllipses_ReceiveCRMAs; }, set_ctlEllipses_ReceiveCRMAs: function(v) { if (this._ctlEllipses_ReceiveCRMAs !== v) this._ctlEllipses_ReceiveCRMAs = v; },
    get_txtReceiveCRMA: function() { return this._txtReceiveCRMA; }, set_txtReceiveCRMA: function(v) { if (this._txtReceiveCRMA !== v) this._txtReceiveCRMA = v; },
    get_ibtnReceiveCRMAs: function() { return this._ibtnReceiveCRMAs; }, set_ibtnReceiveCRMAs: function(v) { if (this._ibtnReceiveCRMAs !== v) this._ibtnReceiveCRMAs = v; },
    get_ctlEllipses_ShipSRMAs: function() { return this._ctlEllipses_ShipSRMAs; }, set_ctlEllipses_ShipSRMAs: function(v) { if (this._ctlEllipses_ShipSRMAs !== v) this._ctlEllipses_ShipSRMAs = v; },
    get_txtShipSRMA: function() { return this._txtShipSRMA; }, set_txtShipSRMA: function(v) { if (this._txtShipSRMA !== v) this._txtShipSRMA = v; },
    get_ibtnShipSRMAs: function() { return this._ibtnShipSRMAs; }, set_ibtnShipSRMAs: function(v) { if (this._ibtnShipSRMAs !== v) this._ibtnShipSRMAs = v; },
    get_ctlEllipses_AllStock: function() { return this._ctlEllipses_AllStock; }, set_ctlEllipses_AllStock: function(v) { if (this._ctlEllipses_AllStock !== v) this._ctlEllipses_AllStock = v; },
    get_txtSearchStock: function() { return this._txtSearchStock; }, set_txtSearchStock: function(v) { if (this._txtSearchStock !== v) this._txtSearchStock = v; },
    get_ctlEllipses_Services: function() { return this._ctlEllipses_Services; }, set_ctlEllipses_Services: function(v) { if (this._ctlEllipses_Services !== v) this._ctlEllipses_Services = v; },
    get_txtSearchService: function() { return this._txtSearchService; }, set_txtSearchService: function(v) { if (this._txtSearchService !== v) this._txtSearchService = v; },
    get_ctlEllipses_Lots: function() { return this._ctlEllipses_Lots; }, set_ctlEllipses_Lots: function(v) { if (this._ctlEllipses_Lots !== v) this._ctlEllipses_Lots = v; },
    get_ctlEllipses_GoodsIn: function() { return this._ctlEllipses_GoodsIn; }, set_ctlEllipses_GoodsIn: function(v) { if (this._ctlEllipses_GoodsIn !== v) this._ctlEllipses_GoodsIn = v; },
    get_txtGoodsIn: function() { return this._txtGoodsIn; }, set_txtGoodsIn: function(v) { if (this._txtGoodsIn !== v) this._txtGoodsIn = v; },
    get_ibtnGoodsIn: function() { return this._ibtnGoodsIn; }, set_ibtnGoodsIn: function(v) { if (this._ibtnGoodsIn !== v) this._ibtnGoodsIn = v; },


    initialize: function() {
        Rebound.GlobalTrader.Site.Pages.Warehouse.Warehouse.callBaseMethod(this, "initialize");
    },

    goInit: function() {
        this._strPathToData = "Code/Pages/Warehouse";
        this._strDataObject = "Warehouse";
        this._strPathToData_Go = "controls/LeftNuggets/QuickJump";
        this._strDataObject_Go = "QuickJump";
        if (this._ibtnShipSOs) $R_IBTN.addClick(this._ibtnShipSOs, Function.createDelegate(this, this.goShipSO));
        if (this._ibtnReceivePOs) $R_IBTN.addClick(this._ibtnReceivePOs, Function.createDelegate(this, this.goReceivePO));
        if (this._ibtnReceiveCRMAs) $R_IBTN.addClick(this._ibtnReceiveCRMAs, Function.createDelegate(this, this.goReceiveCRMA));
        if (this._ibtnShipSRMAs) $R_IBTN.addClick(this._ibtnShipSRMAs, Function.createDelegate(this, this.goShipSRMA));
        if (this._ibtnGoodsIn) $R_IBTN.addClick(this._ibtnGoodsIn, Function.createDelegate(this, this.goGoodsIn));
        $R_TXTBOX.addEnterPressedEvent(this._txtReceivePO, Function.createDelegate(this, this.goReceivePO));
        $R_TXTBOX.addEnterPressedEvent(this._txtShipSO, Function.createDelegate(this, this.goShipSO));
        $R_TXTBOX.addEnterPressedEvent(this._txtReceiveCRMA, Function.createDelegate(this, this.goReceiveCRMA));
        $R_TXTBOX.addEnterPressedEvent(this._txtShipSRMA, Function.createDelegate(this, this.goShipSRMA));
        $R_TXTBOX.addEnterPressedEvent(this._txtGoodsIn, Function.createDelegate(this, this.goGoodsIn));
        Rebound.GlobalTrader.Site.Pages.Warehouse.Warehouse.callBaseMethod(this, "goInit");
    },

    dispose: function() {
        if (this.isDisposed) return;
        if (this._ctlEllipses_ReceivePOs) this._ctlEllipses_ReceivePOs.dispose();
        if (this._ctlEllipses_ShipSOs) this._ctlEllipses_ShipSOs.dispose();
        if (this._ctlEllipses_ReceiveCRMAs) this._ctlEllipses_ReceiveCRMAs.dispose();
        if (this._ctlEllipses_ShipSRMAs) this._ctlEllipses_ShipSRMAs.dispose();
        if (this._ctlEllipses_AllStock) this._ctlEllipses_AllStock.dispose();
        if (this._ctlEllipses_Services) this._ctlEllipses_Services.dispose();
        if (this._ctlEllipses_Lots) this._ctlEllipses_Lots.dispose();
        if (this._ctlEllipses_GoodsIn) this._ctlEllipses_GoodsIn.dispose();
        if (this._ibtnShipSOs) $R_IBTN.clearHandlers(this._ibtnShipSOs);
        if (this._ibtnReceivePOs) $R_IBTN.clearHandlers(this._ibtnReceivePOs);
        if (this._ibtnReceiveCRMAs) $R_IBTN.clearHandlers(this._ibtnReceiveCRMAs);
        if (this._ibtnShipSRMAs) $R_IBTN.clearHandlers(this._ibtnShipSRMAs);
        if (this._txtReceivePO) $R_TXTBOX.clearEvents(this._txtReceivePO);
        if (this._txtShipSO) $R_TXTBOX.clearEvents(this._txtShipSO);
        if (this._txtReceiveCRMA) $R_TXTBOX.clearEvents(this._txtReceiveCRMA);
        if (this._txtShipSRMA) $R_TXTBOX.clearEvents(this._txtShipSRMA);
        if (this._txtGoodsIn) $R_TXTBOX.clearEvents(this._txtGoodsIn);
        this._ctlEllipses_ReceivePOs = null;
        this._txtReceivePO = null;
        this._ibtnReceivePOs = null;
        this._ctlEllipses_ShipSOs = null;
        this._txtShipSO = null;
        this._ibtnShipSOs = null;
        this._ctlEllipses_ReceiveCRMAs = null;
        this._txtReceiveCRMA = null;
        this._ibtnReceiveCRMAs = null;
        this._ctlEllipses_ShipSRMAs = null;
        this._txtShipSRMA = null;
        this._ibtnShipSRMAs = null;
        this._ctlEllipses_AllStock = null;
        this._txtSearchStock = null;
        this._ctlEllipses_Services = null;
        this._txtSearchService = null;
        this._ctlEllipses_Lots = null;
        this._ctlEllipses_GoodsIn = null;
        this._txtGoodsIn = null;
        this._ibtnGoodsIn = null;

        Rebound.GlobalTrader.Site.Pages.Warehouse.Warehouse.callBaseMethod(this, "dispose");
    },

    countReceivePOs: function() {
        var obj = this._ctlEllipses_ReceivePOs._objData;
        obj.set_PathToData(this._strPathToData);
        obj.set_DataObject(this._strDataObject);
        obj.set_DataAction("CountReceivePOs");
    },

    countShipSOs: function() {
        var obj = this._ctlEllipses_ShipSOs._objData;
        obj.set_PathToData(this._strPathToData);
        obj.set_DataObject(this._strDataObject);
        obj.set_DataAction("CountShipSOs");
    },

    countReceiveCRMAs: function() {
        var obj = this._ctlEllipses_ReceiveCRMAs._objData;
        obj.set_PathToData(this._strPathToData);
        obj.set_DataObject(this._strDataObject);
        obj.set_DataAction("CountReceiveCRMAs");
    },

    countShipSRMAs: function() {
        var obj = this._ctlEllipses_ShipSRMAs._objData;
        obj.set_PathToData(this._strPathToData);
        obj.set_DataObject(this._strDataObject);
        obj.set_DataAction("CountShipSRMAs");
    },

    countStock: function() {
        var obj = this._ctlEllipses_AllStock._objData;
        obj.set_PathToData(this._strPathToData);
        obj.set_DataObject(this._strDataObject);
        obj.set_DataAction("CountStock");
    },

    countServices: function() {
        var obj = this._ctlEllipses_Services._objData;
        obj.set_PathToData(this._strPathToData);
        obj.set_DataObject(this._strDataObject);
        obj.set_DataAction("CountServices");
    },

    countLots: function() {
        var obj = this._ctlEllipses_Lots._objData;
        obj.set_PathToData(this._strPathToData);
        obj.set_DataObject(this._strDataObject);
        obj.set_DataAction("CountLots");
    },

    countGoodsIn: function() {
        var obj = this._ctlEllipses_GoodsIn._objData;
        obj.set_PathToData(this._strPathToData);
        obj.set_DataObject(this._strDataObject);
        obj.set_DataAction("CountGIs");
    },


    goReceivePO: function() {
        if (this._txtReceivePO.value.trim().length < 1) return;
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData(this._strPathToData_Go);
        obj.set_DataObject(this._strDataObject_Go);
        obj.set_DataAction("GetPurchaseOrderID");
        obj.addParameter("No", this._txtReceivePO.value.trim());
        obj.addDataOK(Function.createDelegate(this, this.goReceivePOComplete));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
    },

    goReceivePOComplete: function(args) {
        if (args._result.ID) location.href = $RGT_gotoURL_ReceivePurchaseOrder(args._result.ID);
    },

    goShipSO: function() {
        if (this._txtShipSO.value.trim().length < 1) return;
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData(this._strPathToData_Go);
        obj.set_DataObject(this._strDataObject_Go);
        obj.set_DataAction("GetSalesOrderID");
        obj.addParameter("No", this._txtShipSO.value.trim());
        obj.addDataOK(Function.createDelegate(this, this.goShipSOComplete));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
    },

    goShipSOComplete: function(args) {
        if (args._result.ID) location.href = $RGT_gotoURL_ShipSalesOrder(args._result.ID);
    },

    goReceiveCRMA: function() {
        if (this._txtReceiveCRMA.value.trim().length < 1) return;
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData(this._strPathToData_Go);
        obj.set_DataObject(this._strDataObject_Go);
        obj.set_DataAction("GetCRMAID");
        obj.addParameter("No", this._txtReceiveCRMA.value.trim());
        obj.addDataOK(Function.createDelegate(this, this.goReceiveCRMAComplete));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
    },

    goReceiveCRMAComplete: function(args) {
        if (args._result.ID) location.href = $RGT_gotoURL_ReceiveCRMA(args._result.ID);
    },

    goShipSRMA: function() {
        if (this._txtShipSRMA.value.trim().length < 1) return;
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData(this._strPathToData_Go);
        obj.set_DataObject(this._strDataObject_Go);
        obj.set_DataAction("GetSRMAID");
        obj.addParameter("No", this._txtShipSRMA.value.trim());
        obj.addDataOK(Function.createDelegate(this, this.goShipSRMAComplete));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
    },

    goShipSRMAComplete: function(args) {
        if (args._result.ID) location.href = $RGT_gotoURL_ShipSRMA(args._result.ID);
    },

    goGoodsIn: function() {
        if (this._txtGoodsIn.value.trim().length < 1) return;
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData(this._strPathToData_Go);
        obj.set_DataObject(this._strDataObject_Go);
        obj.set_DataAction("GetGoodsInID");
        obj.addParameter("No", this._txtGoodsIn.value.trim());
        obj.addDataOK(Function.createDelegate(this, this.goGoodsInComplete));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
    },

    goGoodsInComplete: function(args) {
        if (args._result.ID) location.href = $RGT_gotoURL_GoodsIn(args._result.ID);
    }

};
Rebound.GlobalTrader.Site.Pages.Warehouse.Warehouse.registerClass("Rebound.GlobalTrader.Site.Pages.Warehouse.Warehouse", Rebound.GlobalTrader.Site.Pages.Content, Sys.IDisposable);
