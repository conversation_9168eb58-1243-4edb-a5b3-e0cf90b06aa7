using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using System.Collections;
using Rebound.GlobalTrader.Site.Controls.Forms;
using Rebound.GlobalTrader.Site.Controls;
using Rebound.GlobalTrader.Site;

namespace Rebound.GlobalTrader.Site.Controls.FormFieldCollections {
    [DefaultProperty("")]
    [ToolboxData("<{0}:LandedCostCalculator runat=server></{0}:LandedCostCalculator>")]
    public class LandedCostCalculator : Base, INamingContainer {

        #region Locals

        private Table _tbl;
        private FormField _ctlQuantity;
        private FormField _ctlCost;
        private Label _lblCurrency;
        private FormField _ctlFunds;
        private FormField _ctlDate;
        private FormField _ctlProduct;
        private FormField _ctlShippingCost;
        private FormField _ctlApplyDuty;
        private FormField _ctlLandedCost;
        private Label _lblCurrency2;
        private IconButton _ibtnCalculate;
        private IconButton _ibtnApply;
        private IconButton _ibtnCancel;
        private TableRow _trLoading;
        private TableRow _trError;
        private TableRow _trApply;
        private TableRow _trCalculate;

        #endregion

        protected override void OnInit(EventArgs e) {
            base.OnInit(e);
            AddScriptReference("Controls.FormFieldCollections.LandedCostCalculator.LandedCostCalculator");
            RemoveCSSClass = true;
        }

        protected override void OnLoad(EventArgs e) {
            Panel pnl = ControlBuilders.CreatePanel("landedCostCalculator");
            _tbl = ControlBuilders.CreateTable();
            _tbl.Width = Unit.Percentage(100);
            pnl.Controls.Add(_tbl);

            //Title
            TableRow trTitle = new TableRow();
            TableCell tdTitle = new TableCell();
            tdTitle.ColumnSpan = 2;
            HtmlControl h4 = ControlBuilders.CreateHtmlGenericControlInsideParent(tdTitle, "h4");
            ControlBuilders.CreateLiteralInsideParent(h4, Functions.GetGlobalResource("Misc", "LandedCostCalculator"));
            trTitle.Cells.Add(tdTitle);
            _tbl.Rows.Add(trTitle);

            //Quantity
            _ctlQuantity = new FormField();
            _ctlQuantity.ID = "ctlLCC_Quantity";
            _ctlQuantity.ResourceTitle = "Quantity";
            _ctlQuantity.FieldID = "txtQuantity";
            _ctlQuantity.IsRequiredField = true;
            ReboundTextBox txtQuantity = new ReboundTextBox();
            txtQuantity.ID = "txtQuantity";
            txtQuantity.Width = 60;
            txtQuantity.TextBoxMode = ReboundTextBox.TextBoxModeList.Numeric;
            _ctlQuantity.AddFieldControl(txtQuantity);
            _tbl.Rows.Add(_ctlQuantity);

            //Cost
            _ctlCost = new FormField();
            _ctlCost.ID = "ctlLCC_Cost";
            _ctlCost.ResourceTitle = "Cost";
            _ctlCost.FieldID = "txtCost";
            _ctlCost.IsRequiredField = true;
            ReboundTextBox txtCost = new ReboundTextBox();
            txtCost.ID = "txtCost";
            txtCost.Width = 80;
            txtCost.TextBoxMode = ReboundTextBox.TextBoxModeList.Currency;
			txtCost.FormatDecimalPlaces = true;
            _ctlCost.AddFieldControl(txtCost);
            _tbl.Rows.Add(_ctlCost);

            //Funds
            _ctlFunds = new FormField();
            _ctlFunds.ID = "ctlLCC_Funds";
            _ctlFunds.ResourceTitle = "Currency";
            _ctlFunds.FieldID = "ddlFunds";
            _ctlFunds.IsRequiredField = true;
            Controls.DropDowns.Base ddlFunds = DropDownManager.GetDropDown("Rebound.GlobalTrader.Site", "BuyCurrency");
            ddlFunds.ID = "ddlFunds";
            _ctlFunds.AddFieldControl(ddlFunds);
            _tbl.Rows.Add(_ctlFunds);

            //Date
            _ctlDate = new FormField();
            _ctlDate.ID = "ctlLCC_Date";
            _ctlDate.ResourceTitle = "Date";
            _ctlDate.FieldID = "txtDate";
            _ctlDate.IsRequiredField = true;
            ReboundTextBox txtDate = new ReboundTextBox();
            txtDate.ID = "txtDate";
            _ctlDate.AddFieldControl(txtDate);
            Controls.Calendar calDate = new Controls.Calendar();
            calDate.ID = "calDate";
            calDate.RelatedTextBoxID = "txtDate";
            _ctlDate.AddFieldControl(calDate);
            _tbl.Rows.Add(_ctlDate);

            //ApplyDuty
            _ctlApplyDuty = new FormField();
            _ctlApplyDuty.ID = "ctlLCC_ApplyDuty";
            _ctlApplyDuty.ResourceTitle = "ApplyDuty";
            _ctlApplyDuty.FieldID = "chkApplyDuty";
            ImageCheckBox chkApplyDuty = new ImageCheckBox();
            chkApplyDuty.ID = "chkApplyDuty";
            chkApplyDuty.Enabled = true;
            _ctlApplyDuty.AddFieldControl(chkApplyDuty);
            _tbl.Rows.Add(_ctlApplyDuty);

            //Product
            _ctlProduct = new FormField();
            _ctlProduct.ID = "ctlLCC_Product";
            _ctlProduct.ResourceTitle = "Product";
            _ctlProduct.FieldID = "ddlProduct";
            _ctlProduct.IsRequiredField = true;
            Controls.DropDowns.Base ddlProduct = DropDownManager.GetDropDown("Rebound.GlobalTrader.Site", "Product");
            ddlProduct.ID = "ddlProduct";
            _ctlProduct.AddFieldControl(ddlProduct);
            _tbl.Rows.Add(_ctlProduct);

            //ShippingCost
            _ctlShippingCost = new FormField();
            _ctlShippingCost.ID = "ctlLCC_ShippingCost";
            _ctlShippingCost.ResourceTitle = "ShippingCost";
            _ctlShippingCost.FieldID = "txtShippingCost";
            _ctlShippingCost.IsRequiredField = true;
            ReboundTextBox txtShippingCost = new ReboundTextBox();
            txtShippingCost.ID = "txtShippingCost";
            txtShippingCost.Width = 80;
            txtShippingCost.TextBoxMode = ReboundTextBox.TextBoxModeList.Currency;
			txtShippingCost.FormatDecimalPlaces = true;
			txtShippingCost.DecimalPlaces = 2;
            _ctlShippingCost.AddFieldControl(txtShippingCost);
            _ctlShippingCost.AddFieldControl(ControlBuilders.CreateLiteral("&nbsp;"));
            _lblCurrency = new Label();
            _lblCurrency.ID = "lblCurrency";
            _ctlShippingCost.AddFieldControl(_lblCurrency);
            _tbl.Rows.Add(_ctlShippingCost);

            //Calculate and Cancel Buttons
            _trCalculate = new TableRow();
            _trCalculate.CssClass = "calculateButton";
            _trCalculate.Cells.Add(new TableCell());
            ControlBuilders.CreateLiteralInsideParent(_trCalculate.Cells[0], "&nbsp;");
            TableCell tdCalculate = new TableCell();
            _ibtnCalculate = new IconButton();
            _ibtnCalculate.ID = "ibtnCalculate";
            _ibtnCalculate.IconGroup = IconButton.IconGroupList.FormBody;
            _ibtnCalculate.IconTitleResource = "Calculate";
            _ibtnCalculate.IconButtonMode = IconButton.IconButtonModeList.Hyperlink;
            _ibtnCalculate.Href = "javascript:void(0);";
            tdCalculate.Controls.Add(_ibtnCalculate);
            _ibtnCancel = new IconButton();
            _ibtnCancel.ID = "ibtnCancel";
            _ibtnCancel.IconGroup = IconButton.IconGroupList.FormBody;
            _ibtnCancel.IconTitleResource = "Cancel";
            _ibtnCancel.IconButtonMode = IconButton.IconButtonModeList.Hyperlink;
            _ibtnCancel.Href = "javascript:void(0);";
            tdCalculate.Controls.Add(_ibtnCancel);
            _trCalculate.Cells.Add(tdCalculate);
            _tbl.Rows.Add(_trCalculate);

            //LandedCost
            _ctlLandedCost = new FormField();
            _ctlLandedCost.ID = "ctlLCC_LandedCost";
            _ctlLandedCost.ResourceTitle = "LandedCost";
            _ctlLandedCost.FieldID = "lblLandedCost";
            Label lblLandedCost = ControlBuilders.CreateLabel();
            lblLandedCost.ID = "lblLandedCost";
            _ctlLandedCost.AddFieldControl(lblLandedCost);
            _ctlLandedCost.AddFieldControl(ControlBuilders.CreateLiteral("&nbsp;"));
            _lblCurrency2 = new Label();
            _lblCurrency2.ID = "lblCurrency2";
            _ctlLandedCost.AddFieldControl(_lblCurrency2);
            _tbl.Rows.Add(_ctlLandedCost);

            //Loading
            _trLoading = new TableRow();
            _trLoading.ID = "trLoading";
            _trLoading.Cells.Add(new TableCell());
            ControlBuilders.CreateLiteralInsideParent(_trLoading.Cells[0], "&nbsp;");
            TableCell tdLoading = new TableCell();
            Panel pnlLoading = ControlBuilders.CreatePanel("loading");
            ControlBuilders.CreateLiteralInsideParent(pnlLoading, Functions.GetGlobalResource("Misc", "Loading"));
            tdLoading.Controls.Add(pnlLoading);
            _trLoading.Cells.Add(tdLoading);
            _tbl.Rows.Add(_trLoading);
            Functions.SetCSSVisibility(_trLoading, false);

            //Error
            _trError = new TableRow();
            _trError.ID = "trError";
            _trError.Cells.Add(new TableCell());
            ControlBuilders.CreateLiteralInsideParent(_trError.Cells[0], "&nbsp;");
            TableCell tdError = new TableCell();
            Panel pnlError = ControlBuilders.CreatePanel("error");
            ControlBuilders.CreateLiteralInsideParent(pnlError, Functions.GetGlobalResource("Messages", "LandedCostCalculationError"));
            tdError.Controls.Add(pnlError);
            _trError.Cells.Add(tdError);
            _tbl.Rows.Add(_trError);
            Functions.SetCSSVisibility(_trError, false);

            //Apply Button
            _trApply = new TableRow();
            _trApply.Cells.Add(new TableCell());
            ControlBuilders.CreateLiteralInsideParent(_trApply.Cells[0], "&nbsp;");
            TableCell tdApply = new TableCell();
            _ibtnApply = new IconButton();
            _ibtnApply.ID = "ibtnApply";
            _ibtnApply.IconGroup = IconButton.IconGroupList.FormBody;
            _ibtnApply.IconTitleResource = "Apply";
            _ibtnApply.IconButtonMode = IconButton.IconButtonModeList.Hyperlink;
            _ibtnApply.Href = "javascript:void(0);";
            tdApply.Controls.Add(_ibtnApply);
            _trApply.Cells.Add(tdApply);
            _tbl.Rows.Add(_trApply);

            AddControl(pnl);
            SetupScriptDescriptors();
            base.OnLoad(e);
        }

        private void SetupScriptDescriptors() {
            _scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.FormFieldCollections.LandedCostCalculator", this.ClientID);
            _scScriptControlDescriptor.AddElementProperty("lblCurrency", _lblCurrency.ClientID);
            _scScriptControlDescriptor.AddElementProperty("lblCurrency2", _lblCurrency2.ClientID);
            _scScriptControlDescriptor.AddElementProperty("ibtnCalculate", _ibtnCalculate.ClientID);
            _scScriptControlDescriptor.AddElementProperty("ibtnApply", _ibtnApply.ClientID);
            _scScriptControlDescriptor.AddElementProperty("ibtnCancel", _ibtnCancel.ClientID);
            _scScriptControlDescriptor.AddElementProperty("trLoading", _trLoading.ClientID);
            _scScriptControlDescriptor.AddElementProperty("trError", _trError.ClientID);
            _scScriptControlDescriptor.AddElementProperty("trApply", _trApply.ClientID);
            _scScriptControlDescriptor.AddElementProperty("trCalculate", _trCalculate.ClientID);
        }

    }
}
