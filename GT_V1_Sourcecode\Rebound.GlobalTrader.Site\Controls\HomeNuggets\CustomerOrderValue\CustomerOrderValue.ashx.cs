//-----------------------------------------------------------------------------------------
//Marker  Date          Changed By     Remarks
//[001]   03/07/2018    A<PERSON><PERSON>    Add customer order value nugget on broker and sales tab
//-----------------------------------------------------------------------------------------
using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;

namespace Rebound.GlobalTrader.Site.Controls.HomeNuggets.Data {
	[WebService(Namespace = "http://tempuri.org/")]
	[WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    public class CustomerOrderValue : Rebound.GlobalTrader.Site.Data.Base
    {

		public override void ProcessRequest(HttpContext context) {
			if (base.init(context)) {
				switch (Action) {
					case "GetData": GetData(); break;
					default: WriteErrorActionNotFound(); break;
				}
			}
		}

		/// <summary>
		/// Gets the main data
		/// </summary>
		private void GetData() {
            try
            {
                int? intLoginID = LoginID;//sales Person id
                if (GetFormValue_Int("OtherLoginID") > 0) intLoginID = GetFormValue_Int("OtherLoginID");
                int intClientId = SessionManager.ClientID ?? 0;
                List<SalesOrder> lstCustomerOrder = SalesOrder.GetListCustomerOrderValue(intLoginID, intClientId);
                if (lstCustomerOrder == null)
                {
                    WriteErrorDataNotFound();
                }
                else
                {
                    JsonObject jsn = new JsonObject();
                    JsonObject jsnItems = new JsonObject(true);
                    for (int i = 0; i < lstCustomerOrder.Count; i++)
                    {
                        JsonObject jsnItem = new JsonObject();
                        jsnItem.AddVariable("CompanyName", lstCustomerOrder[i].CompanyName);
                        jsnItem.AddVariable("TotalValue", Functions.FormatCurrency(lstCustomerOrder[i].TotalValue, 2));
                        jsnItem.AddVariable("AvailCredit", Functions.FormatCurrency(lstCustomerOrder[i].AvailableCreditLimit, 2));
                        jsnItem.AddVariable("CompanyNo", lstCustomerOrder[i].CompanyNo);
                        jsnItem.AddVariable("RowCSS", lstCustomerOrder[i].RowCSS);
                        jsnItems.AddVariable(jsnItem);
                        jsnItem.Dispose();
                        jsnItem = null;
                    }
                    jsn.AddVariable("CustomerOrder", jsnItems);
                    jsn.AddVariable("Count", lstCustomerOrder.Count);
                    jsnItems.Dispose();
                    jsnItems = null;

                    OutputResult(jsn);
                    jsn.Dispose();
                    jsn = null;
                }
                lstCustomerOrder = null;
            }
            catch (Exception ex)
            {
                Errorlog obj = new Errorlog();
                obj.LogMessage(Convert.ToString("Exception- " + ex.Message + " " + ex.StackTrace));
            }
        }


	}
}
