﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO
/*
--============================================================================================================================
TASK			UPDATED BY       DATE				ACTION		DESCRIPTION  
[US-229094]     An.TranTan		 21-Jan-2025		UPDATE		Get quote number
[US-229094]     An.TranTan		 24-Jan-2025		UPDATE		Get quote customer name
==============================================================================================================================  
*/
CREATE OR ALTER PROCEDURE [dbo].[usp_select_ToDo]
@ToDoId int
AS
BEGIN
	select vw.*, q.QuoteNumber, c.CompanyName as CustomerName
	from vwToDo vw
	left join tbQuote q WITH(NOLOCK) on q.QuoteId = vw.QuoteNo
	left join tbCompany c WITH(NOLOCK) on c.CompanyId = q.CompanyNo
	where vw.ToDoId = @ToDoId
END
GO


