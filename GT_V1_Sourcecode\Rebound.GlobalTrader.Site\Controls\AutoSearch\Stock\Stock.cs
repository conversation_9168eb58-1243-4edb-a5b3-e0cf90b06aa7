using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using System.Collections;

namespace Rebound.GlobalTrader.Site.Controls.AutoSearch {
	[DefaultProperty("")]
	[ToolboxData("<{0}:Stock runat=server></{0}:Stock>")]
	public class Stock : Base {

		#region Properties

		private string _searchType = "FULL";

		public string SearchType {
			get {
				return _searchType;
			}
			set {
				_searchType = value;
			}
		}

		#endregion

		#region Overrides

		/// <summary>
		/// OnInit
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
			base.OnInit(e);
			AddScriptReference("Controls.AutoSearch.Stock.Stock.js");
			SetAutoSearchType("Stock");
		}
		/// <summary>
		/// OnLoad
		/// </summary>
		/// <param name="e"></param>
		protected override void OnLoad(EventArgs e) {
			CharactersToEnterBeforeSearch = 3;
			SetupScriptDescriptors();
			base.OnLoad(e);
		}

		#endregion

		/// <summary>
		/// SetupScriptDescriptors
		/// </summary>
		private void SetupScriptDescriptors() {
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.AutoSearch.Stock", ClientID);
			_scScriptControlDescriptor.AddProperty("searchType", SearchType.ToUpper());
		}
	}
}