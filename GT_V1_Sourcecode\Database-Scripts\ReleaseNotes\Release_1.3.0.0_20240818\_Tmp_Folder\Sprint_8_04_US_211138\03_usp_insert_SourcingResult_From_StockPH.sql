﻿
GO

/*
===========================================================================================
TASK      			UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-211138]			Phuc Hoang			14-Aug-2024		UPDATE          [PROD Bug] Error when adding an Offer without Currency in HUBRFQ
===========================================================================================
*/

CREATE OR ALTER PROCEDURE [dbo].[usp_insert_SourcingResult_From_StockPH]
    @CustomerRequirementNo int,
    @StcokNo int,
    @UpdatedBy int,
    @SourcingResultId int OUTPUT,
    @LinkCurrencyMsg varchar(150) = null OUTPUT
AS
BEGIN

    --HUBSTK issue  start Soorya                
    declare @ImportSupplierNo int = 0
    set @ImportSupplierNo = case
                                when
                                (
                                    select ImportSupplierNo from tbstock where stockid = @StcokNo
                                ) is null then
                                (
                                    select po.companyno
                                    from tbstock st
                                        join tbpurchaseorder po
                                            on po.purchaseorderid = st.purchaseorderno
                                    where stockid = @StcokNo
                                )
                                else
                            (
                                select ImportSupplierNo from tbstock where stockid = @StcokNo
                            )
                            end
    --HUBSTK issue  end Soorya                


    declare @POCurrencyNo int = 0
    declare @SupplierWarranty int = 0
    select @POCurrencyNo = cmp.POCurrencyNo,
           @SupplierWarranty = cmp.SupplierWarranty
    from tbcompany cmp
    where cmp.companyid = @ImportSupplierNo


    --IF (@CustomerRequirementNo is not null)                                  
    --begin                                    
    --UPDATE tbCustomerRequirement set REQStatus = 3 where CustomerRequirementId = @CustomerRequirementNo                                        
    --end                      
    DECLARE @CurrencyClientNo INT = 0;
    SELECT @CurrencyClientNo = CurrencyNo
    FROm tbCLient
    WHERE CLientid = 114
    IF (@CustomerRequirementNo <= 0)
    BEGIN
        return
    end
    SET @LinkCurrencyMsg = ''
    --check if we have a row already for this sourcing result                                              
    --IF (SELECT  count(*)                                              
    --    FROM    dbo.tbSourcingResult                                              
    --    WHERE   CustomerRequirementNo = @CustomerRequirementNo                                              
    --            AND SourcingTable = 'HUBSTK'                                              
    --            AND SourcingTableItemNo = @StcokNo                                              
    --   ) = 0                                               
    --    BEGIN                                              
    DECLARE @ClientCompanyNo INT
    DECLARE @CompanyNo INT
    DECLARE @ClientNo INT
    -- DECLARE @UPLiftPrice FLOAT                                                
    -- DECLARE @Price FLOAT                                                
    DECLARE @ClientCurrencyNo INT
    -- DECLARE @ClientLinkCurrencyNo INT                                         
    DECLARE @HubCurrencyNo INT
    declare @LinkMultiCurrencyNo int

    declare @ManufacturerNo int
    declare @ProductNo int
    declare @PackageNo int
    DECLARE @OfferCurrencyNo INT
    DECLARE @BuyExchangeRate float
    DECLARE @HubExchangeRate float
    DECLARE @GlobalProductNo int
    DECLARE @ClientProductNo int
    DECLARE @HubCurrencyName varchar(20)
    DECLARE @ClientCode varchar(20)
    DECLARE @supplierDMCC int
    DECLARE @ReqQuantity int
    declare @StockRemainingQYT int
    declare @StockQuantityAllocated int
    declare @StockQuantityInStock int
    declare @StockQuantityAvilable int

    declare @isAllocationAvilable int = 0



    SELECT @ClientNo = ClientNo,
           @ManufacturerNo = IsNULL(ManufacturerNo, 0),
           @ProductNo = IsNULL(ProductNo, 0),
           @PackageNo = IsNULL(PackageNo, 0),
           @ReqQuantity = Quantity
    FROM tbCustomerRequirement
    where CustomerRequirementId = @CustomerRequirementNo
    SELECT TOP 1
        @ClientCompanyNo = CompanyId
    FROM tbCompany
    WHERE ClientNo = @ClientNo
          AND IsPOHub = 1 --and POApproved=1                                               

    -- SELECT top 1 @HubCurrencyNo = CurrencyNo FROM tbClient where isnull(IsPOHub,0) = 1                                      
    -- SELECT HubCurrencyNo = dbo.ufn_get_HUB_DefaultCurrencyNo(244,101,114)                                      



    --IF EXISTS(SELECT 1 FROM [BorisGlobalTraderImports].dbo.tbOffer WHERE OfferId =  @OfferNo)                                    
    --BEGIN                                              
    --SELECT @OfferCurrencyNo = CurrencyNo , @GlobalProductNo = isnull(p.GlobalProductNo,0) FROM [BorisGlobalTraderImports].dbo.tbOffer o left join tbProduct p on o.ProductNo = p.ProductId                                        
    --WHERE OfferId = @OfferNo                                   

    --select top 1 @supplierDMCC=CompanyId from tbCompany where ClientNo=@ClientNo and CompanyName like '%Rebound Dubai DMCC%' and POApproved=1                                

    SELECT @LinkMultiCurrencyNo = l.LinkMultiCurrencyId,
           @ClientCurrencyNo = l.SupplierCurrencyNo
    from tbCurrency c
        left join tbLinkMultiCurrency l
            on l.GlobalCurrencyNo = c.GlobalCurrencyNo
    WHERE l.ClientNo = @ClientNo
          and c.ClientNo = 114
    --AND c.CurrencyId = @OfferCurrencyNo                                      

    SELECT @HubCurrencyNo = dbo.ufn_get_HUB_DefaultCurrencyNo(@OfferCurrencyNo, @ClientNo, 114)
    --Get the buy exchange rate .66                                      
    SELECT @BuyExchangeRate = dbo.ufn_get_exchange_rate(ISNULL(@OfferCurrencyNo, 0), GETDATE())
    --Get Hub Exchange rate                                                 
    SELECT @HubExchangeRate = dbo.ufn_get_exchange_rate(ISNULL(@HubCurrencyNo, 0), GETDATE())

    select top 1
        @ClientProductNo = ProductId
    from tbProduct
    where GlobalProductNo = @GlobalProductNo
          and ClientNo = @ClientNo
          and isnull(Inactive, 0) = 0

    IF @LinkMultiCurrencyNo IS NULL
    BEGIN
        SELECT @ClientCode = ClientCode
        FROM tbclient
        where clientid = @ClientNo
        select @HubCurrencyName = CurrencyCode
        from tbCurrency
        where CurrencyId = @OfferCurrencyNo
        SET @LinkCurrencyMsg
            = 'Cannot use ' + ISNULL(@HubCurrencyName, '') + ' currency for the ' + ISNULL(@ClientCode, '')
              + ' client. Kindly contact administrator.';
        SET @SourcingResultId = 0
        RETURN
    END

	/***remove allocation because it is not used anymore - cuongdox 14-6-24 bug 203943***/
	/*
    select @isAllocationAvilable = count(*)
    from tbAllocation
    where stockno = @StcokNo
    IF (
       (
           select st.QuantityInStock from tbstock st where st.stockid = @StcokNo
       )   >= @ReqQuantity
       and @isAllocationAvilable = 0
       )
    BEGIN
        --update tbstock set QuantityInStock=(QuantityInStock-@ReqQuantity)  where stockid=@StcokNo    
        -------------------Start-----------Insert into tbAllocation-------------------------------------------------    
        INSERT INTO dbo.tbAllocation
        (
            StockNo,
            SalesOrderLineNo,
            QuantityAllocated,
            UpdatedBy,
            DLUP
        )
        SELECT Stockid,
               0,
               @ReqQuantity,
               UpdatedBy,
               GETDATE()
        FROM dbo.tbStock
        WHERE StockId = @StcokNo
    ---------------end---------------Insert into tbAllocation-------------------------------------------------    
    END
    else
    begin

        select @StockQuantityAllocated = sum(QuantityAllocated)
        from tbAllocation
        where stockno = @StcokNo
        select @StockQuantityInStock = st.QuantityInStock
        from tbstock st
        where st.stockid = @StcokNo
        set @StockQuantityAvilable = (@StockQuantityInStock - @StockQuantityAllocated)

        IF (@StockQuantityAvilable >= @ReqQuantity)
        BEGIN
            --update tbstock set QuantityInStock=(QuantityInStock-@ReqQuantity)  where stockid=@StcokNo    
            -------------------Start-----------Insert into tbAllocation-------------------------------------------------    
            INSERT INTO dbo.tbAllocation
            (
                StockNo,
                SalesOrderLineNo,
                QuantityAllocated,
                UpdatedBy,
                DLUP
            )
            SELECT Stockid,
                   0,
                   @ReqQuantity,
                   UpdatedBy,
                   GETDATE()
            FROM dbo.tbStock
            WHERE StockId = @StcokNo
        ---------------end---------------Insert into tbAllocation-------------------------------------------------    
        END
        --IF( @ReqQuantity<(select st.QuantityInStock from tbstock st where st.stockid=@StcokNo)) 
        IF (@StockQuantityAvilable < @ReqQuantity)
        begin

            INSERT INTO dbo.tbAllocation
            (
                StockNo,
                SalesOrderLineNo,
                QuantityAllocated,
                UpdatedBy,
                DLUP
            )
            SELECT Stockid,
                   0,
                   @StockQuantityAvilable,
                   UpdatedBy,
                   GETDATE()
            FROM dbo.tbStock
            WHERE StockId = @StcokNo

            set @ReqQuantity = @StockQuantityAvilable
        ---------------end---------------Insert into tbAllocation-------------------------------------------------  
        end

    end

	*/
	/*end removing for cuongdox 14-6-24 bug 203943*/
    --calculate quantity on stock to get as source quantity
    select @StockQuantityInStock = st.QuantityInStock
        from tbstock st
        where st.stockid = @StcokNo
        
    INSERT INTO dbo.tbSourcingResult
    (
        CustomerRequirementNo,
        SourcingTable,
        SourcingTableItemNo,
        FullPart,
        Part,
        ManufacturerNo,
        DateCode,
        ProductNo,
        PackageNo,
        Quantity,
        Price,
        CurrencyNo,
        OriginalEntryDate,
        Salesman,
        OfferStatusNo,
        OfferStatusChangeDate,
        OfferStatusChangeLoginNo,
        SupplierNo,
        UpdatedBy,
        DLUP,
        TypeName,
        Notes,
        ROHS,
        POHubCompanyNo,
        SupplierPrice,
        ClientCompanyNo,
        EstimatedShippingCost,
        ClientCurrencyNo,
        SupplierManufacturerName,
        SupplierDateCode,
        SupplierPackageType,
        SupplierProductType,
        SupplierMOQ,
        SupplierTotalQSA,
        SupplierLTB,
        SupplierNotes,
        SPQ,
        LeadTime,
        ROHSStatus,
        FactorySealed,
        MSL,
        Buyer,
        ActualPrice,
        ActualCurrencyNo,
        ExchangeRate,
        LinkMultiCurrencyNo,
        MSLLevelNo,
        SupplierWarranty
    )
    SELECT @CustomerRequirementNo,
           'HUBSTK',
           @StcokNo,
           st.fullpart,
           st.Part,
           isnull(mfr.ManufacturerId, 0),
           st.DateCode,
           isnull(st.ProductNo, @ProductNo),
           isnull(st.PackageNo, @PackageNo),
                                                                           --,  isnull(st.QuantityInStock, 0)   --discuss with sir   @ReqQuantity        
           CASE WHEN @ReqQuantity > @StockQuantityInStock THEN @StockQuantityInStock ELSE @ReqQuantity END
           ,
                                                                           --------------------------------------------------    



                                                                           --------------------------------------------------    
                                                                           --, isnull(st.clientUpLiftprice,0)                     
                                                                           --, dbo.ufn_convert_currency_value(isnull(st.clientUpLiftprice,0),@CurrencyClientNo,dbo.ufn_get_HUB_DefaultCurrencyNo(isnull(company.POCurrencyNo,0),@ClientNo,114),GETDATE())                           
                                                                           --, dbo.ufn_get_HUB_DefaultCurrencyNo(isnull(company.POCurrencyNo,0),@ClientNo,114)                 
           dbo.ufn_convert_currency_value(
                                             isnull(st.clientUpLiftprice, 0),
                                             @CurrencyClientNo,
                                             dbo.ufn_get_HUB_DefaultCurrencyNo(isnull(@POCurrencyNo, 0), @ClientNo, 114),
                                             GETDATE()
                                         ),
           dbo.ufn_get_HUB_DefaultCurrencyNo(isnull(@POCurrencyNo, 0), @ClientNo, 114),
           st.DLUP,
           0,
           null,
           null,
           null,
           @ClientCompanyNo,
           @UpdatedBy,
           getdate(),
           '',
           st.LotImportLogNotes,
           ROHS.ROHSStatusId,
                                                                           --, st.ImportSupplierNo                          
           @ImportSupplierNo,
           ISNULL(st.ResalePrice, 0),
           @ClientCompanyNo,                                               --need to discuss                   
           0,
                                                                           /*                  
                  
  , (isnull(country.ShippingCost,0))* @HubExchangeRate      --need to discuss with Abinav S                     
  */
           @ClientCurrencyNo,                                              --need to discuss                      
           mfr.ManufacturerName,
           st.DateCode,
           pk.PackageName,
           pr.ProductDescription,
           null,
           0,
           null,
           null,
           null,
           null,
           ROHS.Description,
           null,
           msl.MSLLevel,
           @UpdatedBy,
                                                                           --, dbo.ufn_convert_currency_value(isnull(st.ResalePrice,0),@CurrencyClientNo,dbo.ufn_get_HUB_DefaultCurrencyNo(isnull(company.POCurrencyNo,0),@ClientNo,114),GETDATE())             
           dbo.ufn_convert_currency_value(
                                             isnull(st.ResalePrice, 0),
                                             @CurrencyClientNo,
                                             dbo.ufn_get_HUB_DefaultCurrencyNo(isnull(@POCurrencyNo, 0), @ClientNo, 114),
                                             GETDATE()
                                         ),
           isnull(@POCurrencyNo, 0),
                                                                           --, isnull(company.POCurrencyNo,0)                                
                                                                           --, dbo.ufn_get_exchange_rate(isnull(company.POCurrencyNo,0),GETDATE())     --need to discuss                                 
           dbo.ufn_get_exchange_rate(isnull(@POCurrencyNo, 0), GETDATE()), --need to discuss                  
           @LinkMultiCurrencyNo,                                           --need to discuss                                     
           msl.MSLLevelId,
                                                                           --, company.SupplierWarranty     --need to discuss                                               
           @SupplierWarranty
    FROM tbStock st
        LEFT JOIN dbo.tbCompany company
            on st.ImportSupplierNo = company.CompanyId
        LEFT JOIN dbo.tbCountry country
            on country.CountryId = company.DefaultPOShipCountryNo
               AND company.ClientNo = country.ClientNo
        LEFT JOIN dbo.tbManufacturer mfr
            ON mfr.ManufacturerId = st.ManufacturerNo
        LEFT JOIN dbo.tbProduct pr
            ON pr.ProductId = st.ProductNo
        LEFT JOIN dbo.tbPackage pk
            ON pk.PackageId = st.PackageNo
        LEFT JOIN dbo.tbMSLLevel msl
            ON st.MSLLevel = msl.MSLLevel
        LEFT JOIN dbo.tbROHSStatus rohs
            ON st.ROHS = ROHS.ROHSStatusId
    WHERE st.StockId = @StcokNo


    SET @SourcingResultId = scope_identity();

    --Update customer requirement                                         
    UPDATE tbCustomerRequirement
    set HasHubSourcingResult = 1
    where CustomerRequirementId = @CustomerRequirementNo
--        END                                              
--    ELSE                                               
--BEGIN                                          
--            SET @SourcingResultId = 1 ; --spoof an OK result                                              
--        END                                              


END
GO


