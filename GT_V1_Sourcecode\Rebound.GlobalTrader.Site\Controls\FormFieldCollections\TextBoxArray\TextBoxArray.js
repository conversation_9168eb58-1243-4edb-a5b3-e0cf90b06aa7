Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.FormFieldCollections");Rebound.GlobalTrader.Site.Controls.FormFieldCollections.TextBoxArray=function(n){Rebound.GlobalTrader.Site.Controls.FormFieldCollections.TextBoxArray.initializeBase(this,[n]);this._aryComponents=[];this._aryExtraData=[];this._blnAllowAddAndDelete=!0;this._blnAllowRecordSerialNo=!0;this._intNextIndex=0;this._intValidateMinValue=null;this._blnValidateGreaterThanOrEqualTo=!1;this._intValidateMaxValue=null;this._blnValidateLessThanOrEqualTo=!1;this._frmSOShip=null;this._intLineID=-1;this._intAllocationID=-1};Rebound.GlobalTrader.Site.Controls.FormFieldCollections.TextBoxArray.prototype={get_tbl:function(){return this._tbl},set_tbl:function(n){this._tbl!==n&&(this._tbl=n)},get_intTextBoxWidth:function(){return this._intTextBoxWidth},set_intTextBoxWidth:function(n){this._intTextBoxWidth!==n&&(this._intTextBoxWidth=n)},get_enmTextBoxMode:function(){return this._enmTextBoxMode},set_enmTextBoxMode:function(n){this._enmTextBoxMode!==n&&(this._enmTextBoxMode=n)},get_blnUppercaseOnly:function(){return this._blnUppercaseOnly},set_blnUppercaseOnly:function(n){this._blnUppercaseOnly!==n&&(this._blnUppercaseOnly=n)},get_blnFormatDecimalPlaces:function(){return this._blnFormatDecimalPlaces},set_blnFormatDecimalPlaces:function(n){this._blnFormatDecimalPlaces!==n&&(this._blnFormatDecimalPlaces=n)},get_intDecimalPlaces:function(){return this._intDecimalPlaces},set_intDecimalPlaces:function(n){this._intDecimalPlaces!==n&&(this._intDecimalPlaces=n)},get_aryExtraData:function(){return this._aryExtraData},set_aryExtraData:function(n){this._aryExtraData!==n&&(this._aryExtraData=n)},get_blnAllowAddAndDelete:function(){return this._blnAllowAddAndDelete},set_blnAllowAddAndDelete:function(n){this._blnAllowAddAndDelete!==n&&(this._blnAllowAddAndDelete=n)},get_blnAllowRecordSerialNo:function(){return this._blnAllowRecordSerialNo},set_blnAllowRecordSerialNo:function(n){this._blnAllowRecordSerialNo!==n&&(this._blnAllowRecordSerialNo=n)},get_blnRequiredField:function(){return this._blnRequiredField},set_blnRequiredField:function(n){this._blnRequiredField!==n&&(this._blnRequiredField=n)},get_blnValidatePasteChar:function(){return this._blnValidatePasteChar},set_blnValidatePasteChar:function(n){this._blnValidatePasteChar!==n&&(this._blnValidatePasteChar=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.FormFieldCollections.TextBoxArray.callBaseMethod(this,"initialize")},dispose:function(){this.isDisposed||(this.get_element()&&$clearHandlers(this.get_element()),this._tbl=null,this._aryComponents=null,this._aryExtraData=null,this._enmTextBoxMode=null,this._blnUppercaseOnly=null,this._blnFormatDecimalPlaces=null,this._intDecimalPlaces=null,this._intTextBoxWidth=null,this._blnAllowAddAndDelete=null,this._blnAllowRecordSerialNo=null,this._intValidateMinValue=null,this._blnValidateGreaterThanOrEqualTo=null,this._intValidateMaxValue=null,this._blnValidateLessThanOrEqualTo=null,this._blnRequiredField=null,this._blnValidatePasteChar=null,this._intLineID=null,this._intAllocationID=null,Rebound.GlobalTrader.Site.Controls.FormFieldCollections.TextBoxArray.callBaseMethod(this,"dispose"))},show:function(n){$R_FN.showElement(this._element,n)},addItem:function(n,t,i,r,u,f,e){var s,o;n||(n=this._intNextIndex);t||(t=this._intNextIndex);r||(r="");u||(u="");f||(f="");e||(e=null);s=this._tbl.insertRow(-1);s.id=this.getControlID("tr",n);o=[];o[0]=document.createElement("td");o[0].className="title";o[0].innerHTML=String.format("{0}{1}",$R_FN.setCleanTextValue(i),this._blnRequiredField?'<span class="requiredField">*<\/span>':"");s.appendChild(o[0]);o[1]=document.createElement("td");o[1].className="item";var c=this.getControlID("txt",n),l=this.getControlID("btn",n),h=u;h+=String.format(' <input id="{0}" type="text" style="width: {1}px;" value="{2}" onfocus="$R_TXTBOX.initTextBox(this, {3}, false, {4}, false, {5}, {6},{7});" />',c,this._intTextBoxWidth==0?"200":this._intTextBoxWidth,r,this._enmTextBoxMode,this._blnUppercaseOnly,this._blnFormatDecimalPlaces,this._intDecimalPlaces,this._blnValidatePasteChar);h+=String.format("&nbsp; {0}",f);this._blnAllowAddAndDelete&&(h+=String.format('<span id="{0}"><\/span>',this.getControlID("spnButtons",n)));h+=String.format('<div id="{0}" class="invisible"><\/div>',this.getControlID("divError",n));o[1].innerHTML=h;h=null;s.appendChild(o[1]);Array.add(this._aryComponents,{ID:t,TextBoxID:c,RowID:s.id,ButtonsID:this.getControlID("spnButtons",n),ErrorID:this.getControlID("divError",n)});Array.add(this._aryExtraData,e);s=null;o=null;this._blnAllowAddAndDelete&&this.displayButtons();this._intNextIndex+=1},addSerialItem:function(n,t,i,r,u,f,e,o,s,h,c){var v,l;n||(n=this._intNextIndex);t||(t=this._intNextIndex);r||(r="");u||(u="");f||(f="");e||(e=null);this._intLineID=e;v=this._tbl.insertRow(-1);v.id=this.getControlID("tr",n);l=[];l[0]=document.createElement("td");l[0].className="title";l[0].innerHTML=String.format("{0}{1}",$R_FN.setCleanTextValue(i),this._blnRequiredField?'<span class="requiredField">*<\/span>':"");v.appendChild(l[0]);l[1]=document.createElement("td");l[1].className="item";var y=this.getControlID("txt",n),p=this.getControlID("btn",n),a=u;a+=h?String.format(' <input id="{0}" type="text" style="width: {1}px;" value="{2}" onfocus="$R_TXTBOX.initTextBox(this, {3}, false, {4}, false, {5}, {6},{7});" onkeyup="$find(\'{8}\').refereshSerial({9});" />',y,this._intTextBoxWidth==0?"200":this._intTextBoxWidth,r,this._enmTextBoxMode,this._blnUppercaseOnly,this._blnFormatDecimalPlaces,this._intDecimalPlaces,this._blnValidatePasteChar,this._element.id,t):String.format(' <input id="{0}" type="text" style="width: {1}px;" value="{2}" onfocus="$R_TXTBOX.initTextBox(this, {3}, false, {4}, false, {5}, {6},{7});"  />',y,this._intTextBoxWidth==0?"200":this._intTextBoxWidth,r,this._enmTextBoxMode,this._blnUppercaseOnly,this._blnFormatDecimalPlaces,this._intDecimalPlaces,this._blnValidatePasteChar);a+=String.format("&nbsp; {0}",f);h&&(a+=String.format('<span id="{0}"><\/span>',this.getControlID("spanButtons",n)));a+=String.format('<div id="{0}" class="invisible"><\/div>',this.getControlID("divError",n));this._blnAllowAddAndDelete&&(a+=String.format('<span id="{0}"><\/span>',this.getControlID("spnButtons",n)));a+=String.format('<div id="{0}" class="invisible"><\/div>',this.getControlID("divError",n));l[1].innerHTML=a;a=null;v.appendChild(l[1]);Array.add(this._aryComponents,{ID:t,TextBoxID:y,RowID:v.id,ButtonID:this.getControlID("spanButtons",n),ButtonsID:this.getControlID("spnButtons",n),ErrorID:this.getControlID("divError",n),GoodsInLineNo:s,SOLineNo:e,PartNo:i,QtyAllocated:r,AllocationNo:c});Array.add(this._aryExtraData,e);v=null;l=null;h&&this.displaySerialButtons();this._blnAllowAddAndDelete&&this.displayButtons();this._intNextIndex+=1;this._frmSOShip=o},removeItem:function(n){var i=this.findIndexFromID(n),t=$get(this._aryComponents[i].RowID);t&&(t.parentNode.removeChild(t),Array.removeAt(this._aryComponents,i),Array.removeAt(this._aryExtraData,i),this.displayButtons());t=null},recordSerailNo:function(n){var t=this.findIndexFromID(n),i=$get(this._aryComponents[t].RowID),r;i&&(r=$get(this._aryComponents[t].TextBoxID),this._frmSOShip.OpenSerialForm(this._aryComponents[t].SOLineNo,this._aryComponents[t].GoodsInLineNo,r.value,this._aryComponents[t].PartNo,this._aryComponents[t].AllocationNo));i=null},refereshSerial:function(n){this.recordSerailNo(n)},addHeading:function(n,t){var r=this._tbl.insertRow(-1),i=document.createElement("td");i.colSpan=2;i.innerHTML=String.format("<h{0}{2}>{1}<\/h{0}>",n,t,this._tbl.rows.length==1?' class="first"':"");r.appendChild(i);r=null;i=null},displayButtons:function(){var n,i,t,r;if(this._blnAllowAddAndDelete)for(n=0,i=this._aryComponents.length;n<i;n++)t="",n>0&&i>1&&(t+=String.format('<a href="javascript:void(0);" onclick="$find(\'{0}\').removeItem({1});" class="quickSearchReselect">[x]<\/a>',this._element.id,this._aryComponents[n].ID)),n==i-1&&(t+=String.format('<a href="javascript:void(0);" onclick="$find(\'{0}\').addItem();" class="quickSearchReselect">[+]<\/a>',this._element.id)),r=$get(this._aryComponents[n].ButtonsID),r&&(r.innerHTML=t),r=null,t=null},displaySerialButtons:function(){for(var t,i,n=0,r=this._aryComponents.length;n<r;n++)t="",t+=String.format('&nbsp;&nbsp;<a href="javascript:void(0);" onclick="$find(\'{0}\').recordSerailNo({1},{2});" style="color:yellow;font-size: 13px">Select Serial No. to ship<\/a>',this._element.id,this._aryComponents[n].ID,this._aryComponents[n].GoodsInLineNo),i=$get(this._aryComponents[n].ButtonID),i&&(i.innerHTML=t),i=null,t=null},findIndexFromID:function(n){for(var i=-1,t=0,r=this._aryComponents.length;t<r;t++)if(this._aryComponents[t].ID==n){i=t;break}return i},getControlID:function(n,t){return String.format("{0}_{1}{2}",this._element.id,n,t)},clearItems:function(){for(var n=this._tbl.rows.length-1;n>=0;n--)this._tbl.deleteRow(n);Array.clear(this._aryComponents);Array.clear(this._aryExtraData);this._intNextIndex=0},getIDs:function(){for(var t=[],n=0,i=this._aryComponents.length;n<i;n++)Array.add(t,this._aryComponents[n].ID);return t},getIDsAsString:function(){return $R_FN.arrayToSingleString(this.getIDs())},getValues:function(){for(var t,i=[],n=0,r=this._aryComponents.length;n<r;n++)t=$get(this._aryComponents[n].TextBoxID),Array.add(i,t.value.trim()),t=null;return i},updateValues:function(n){for(var i,t=0,r=this._aryComponents.length;t<r;t++)i=$get(this._aryComponents[t].TextBoxID),i&&(i.value=n[t]),i=null},updateValue:function(n,t){var i=$get(this._aryComponents[n].TextBoxID);i&&(i.value=t);i=null},getValuesAsString:function(){return $R_FN.arrayToSingleString(this.getValues())},getSelectedExtraData:function(n){return this._aryExtraData[n]},getValue:function(n){var t=$get(this._aryComponents[n].TextBoxID);if(t)return t.value;t=null},setMinAndMaxForNumericValidation:function(n,t,i,r){this._intValidateMinValue=typeof n=="undefined"||n==null?null:n;this._intValidateMaxValue=typeof t=="undefined"||t==null?null:t;this._blnValidateLessThanOrEqualTo=!1;this._blnValidateGreaterThanOrEqualTo=!1;typeof r!="undefined"&&r!=null&&(this._blnValidateLessThanOrEqualTo=r);typeof i!="undefined"&&i!=null&&(this._blnValidateGreaterThanOrEqualTo=i)},validateAllFields:function(){for(var t,i=!0,n=0,r=this._aryComponents.length;n<r;n++)t=$get(this._aryComponents[n].TextBoxID),this.clearFieldError(n),this._enmTextBoxMode==$R_ENUM$TextBoxMode.Numeric||this._enmTextBoxMode==$R_ENUM$TextBoxMode.Currency?$R_FN.checkNumeric(t)?this._blnRequiredField&&!$R_FN.isEntered(t.value)?(this.showFieldError(n,$R_RES.EnterFieldGeneric),i=!1):this._intValidateMinValue!=null?this._blnValidateGreaterThanOrEqualTo?Number.parseLocale(t.value.toString())<this._intValidateMinValue&&(this.showFieldError(n,String.format($R_RES.NumericFieldGreaterThanOrEqualToError,this._intValidateMinValue)),i=!1):Number.parseLocale(t.value.toString())<=this._intValidateMinValue&&(this.showFieldError(n,String.format($R_RES.NumericFieldGreaterThanError,this._intValidateMinValue)),i=!1):this._intValidateMaxValue!=null&&(this._blnValidateLessThanOrEqualTo?Number.parseLocale(t.value.toString())>this._intValidateMaxValue&&(this.showFieldError(n,String.format($R_RES.NumericFieldLessThanOrEqualToError,this._intValidateMaxValue)),i=!1):Number.parseLocale(t.value.toString())>=this._intValidateMaxValue&&(this.showFieldError(n,String.format($R_RES.NumericFieldLessThanError,this._intValidateMaxValue)),i=!1)):(this.showFieldError(n,$R_RES.NumericFieldError),i=!1):this._blnRequiredField&&!$R_FN.isEntered(t.value)&&(this.showFieldError(n,$R_RES.EnterFieldGeneric),i=!1),t=null;return i},showFieldError:function(n,t){var i=$get(this._aryComponents[n].ErrorID),r=$get(this._aryComponents[n].RowID);i&&r&&(i.innerHTML=t,r.className="formRowError",$R_FN.showElement(i,!0),i=null,r=null)},clearFieldError:function(n){var t=$get(this._aryComponents[n].ErrorID),i=$get(this._aryComponents[n].RowID);t&&i&&(t.innerHTML="",i.className="formRow",$R_FN.showElement(t,!1),t=null,i=null)}};Rebound.GlobalTrader.Site.Controls.FormFieldCollections.TextBoxArray.registerClass("Rebound.GlobalTrader.Site.Controls.FormFieldCollections.TextBoxArray",Rebound.GlobalTrader.Site.Controls.Forms.LabelFormField,Sys.IDisposable);