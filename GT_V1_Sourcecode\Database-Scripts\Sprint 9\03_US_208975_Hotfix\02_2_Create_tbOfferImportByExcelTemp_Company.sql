﻿/* 
===========================================================================================
TASK      		UPDATED BY     	DATE         ACTION		DESCRIPTION
[US-208975]		Phuc Hoang		23-Aug-2024  UPDATE		Large HUB Offers Import File be imported by BACKEND/ SCRIPT supporting
===========================================================================================
*/

CREATE TABLE [dbo].[tbOfferImportByExcelTemp_Company](
	[OfferTempId] [int] IDENTITY(1,1) NOT NULL,
	[MPN] [nvarchar](MAX) NULL,
	[MFR] [nvarchar](MAX) NULL,
	[COST] [nvarchar](MAX) NULL,
	[LeadTime] [nvarchar](MAX) NULL,
	[SPQ] [nvarchar](MAX) NULL,
	[MOQ] [nvarchar](MAX) NULL,
	[Remarks] [nvarchar](MAX) NULL,
	[OfferedDate] [datetime] NULL,
	[Vendor] [nvarchar](MAX) NULL,
	[DLUP] [datetime] NOT NULL,
 CONSTRAINT [PK_tbOfferImportByExcelTemp_Company] PRIMARY KEY CLUSTERED 
(
	[OfferTempId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[tbOfferImportByExcelTemp_Company] ADD  CONSTRAINT [DF_tbOfferImportByExcelTemp_Company_DLUP]  DEFAULT (getdate()) FOR [DLUP]
GO