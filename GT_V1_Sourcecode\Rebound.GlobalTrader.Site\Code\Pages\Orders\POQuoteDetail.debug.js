///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference name="~/Controls/IconButton/IconButton.js" />
///<reference name="~/Controls/ReboundTextBox/ReboundTextBox.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Pages.Orders");

Rebound.GlobalTrader.Site.Pages.Orders.POQuoteDetail = function(el) {
    Rebound.GlobalTrader.Site.Pages.Orders.POQuoteDetail.initializeBase(this, [el]);
};

Rebound.GlobalTrader.Site.Pages.Orders.POQuoteDetail.prototype = {

    get_ctlPOQuoteMainInfo: function() { return this._ctlPOQuoteMainInfo; }, set_ctlPOQuoteMainInfo: function(v) { if (this._ctlPOQuoteMainInfo !== v) this._ctlPOQuoteMainInfo = v; },
    get_ctlPOQuoteLines: function() { return this._ctlPOQuoteLines; }, set_ctlPOQuoteLines: function(v) { if (this._ctlPOQuoteLines !== v) this._ctlPOQuoteLines = v; },
    get_ctlPurchaseRequestLineDetail: function() { return this._ctlPurchaseRequestLineDetail; }, set_ctlPurchaseRequestLineDetail: function(v) { if (this._ctlPurchaseRequestLineDetail !== v) this._ctlPurchaseRequestLineDetail = v; },
    get_ctlPurchaseCSV: function() { return this._ctlPurchaseCSV; }, set_ctlPurchaseCSV: function(v) { if (this._ctlPurchaseCSV !== v) this._ctlPurchaseCSV = v; },
    get_ctlCsvExportHistory: function() { return this._ctlCsvExportHistory; }, set_ctlCsvExportHistory: function(v) { if (this._ctlCsvExportHistory !== v) this._ctlCsvExportHistory = v; },

    initialize: function() {
        Rebound.GlobalTrader.Site.Pages.Orders.POQuoteDetail.callBaseMethod(this, "initialize");
    },

    goInit: function() {
        if (this._ctlPOQuoteMainInfo) this._ctlPOQuoteMainInfo.addSaveEditComplete(Function.createDelegate(this, this.ctlPOQuoteMainInfo_SaveEditComplete));
        if (this._ctlPOQuoteLines) this._ctlPOQuoteLines.addPotentialStatusChange(Function.createDelegate(this, this.ctlLines_PotentialStatusChange));
        if (this._ctlPOQuoteLines) this._ctlPOQuoteLines.addPurchaseRequestLineSelect(Function.createDelegate(this, this.ctlLines_PurchaseRequestLineSelect));
         if (this._ctlPOQuoteMainInfo) this._ctlPOQuoteMainInfo.addRefreshLog(Function.createDelegate(this, this.ctlPOQuoteMainInfo_addRefreshLog));
        Rebound.GlobalTrader.Site.Pages.Orders.POQuoteDetail.callBaseMethod(this, "goInit");

    },

    dispose: function() {
        if (this.isDisposed) return;
        if (this._ctlPOQuoteMainInfo) this._ctlPOQuoteMainInfo.dispose();
        if (this._ctlPOQuoteLines) this._ctlPOQuoteLines.dispose();
        this._ctlCsvExportHistory = null;
        this._ctlPOQuoteMainInfo = null;
        this._ctlPOQuoteLines = null;
        this._ctlPurchaseCSV = null;
        Rebound.GlobalTrader.Site.Pages.Orders.POQuoteDetail.callBaseMethod(this, "dispose");
    },

    ctlPOQuoteMainInfo_GetDataComplete: function() {

    },

ctlPOQuoteMainInfo_addRefreshLog: function(){
this._ctlCsvExportHistory.getCreditHistory();
//this._ctlCsvExportHistory.getData();

},
    ctlPOQuoteMainInfo_SaveEditComplete: function() {
        if (this._ctlPOQuoteLines) this._ctlPOQuoteLines.getData();
       // this.ctlPurchaseRequestLineDetail_PotentialStatusChange();
    },

    ctlLines_PotentialStatusChange: function() {
        this._ctlPOQuoteMainInfo.enableExportAndNotifyButton(this._ctlPOQuoteLines._enableExportAndNotifyButton);
        this._ctlPurchaseRequestLineDetail._intPurchaseRequestLineNo = 0;
        this._ctlPurchaseRequestLineDetail.getData();
        this._ctlPurchaseRequestLineDetail.enableDisableAddButton(false);
        this._ctlPurchaseRequestLineDetail.enableDisableEditButton(false);
    }
    , ctlLines_PurchaseRequestLineSelect: function() {
        if (this._ctlPOQuoteLines._intPurchaseRequestLineId > 0) {
           //var strCurrency= this._ctlPOQuoteMainInfo.getFieldValue("ctlCurrency");
            this._ctlPurchaseRequestLineDetail._intPurchaseRequestLineNo = this._ctlPOQuoteLines._intPurchaseRequestLineId;
            this._ctlPurchaseRequestLineDetail.getData();
           // this._ctlPurchaseRequestLineDetail._ctlCurrency=strCurrency;
             this._ctlPurchaseRequestLineDetail._ctlPart=this._ctlPOQuoteLines._ctlPart;
            // this._ctlPurchaseRequestLineDetail._ctlCurrencyCode=this._ctlPOQuoteLines._ctlCurrencyCode;
            this._ctlPurchaseRequestLineDetail.enableDisableAddButton(true);
            this._ctlPurchaseRequestLineDetail.enableDisableEditButton(false);
        } else {
            this._ctlPurchaseRequestLineDetail.enableDisableAddButton(false);
        }
    },
    printQuote: function() {
    }

};
Rebound.GlobalTrader.Site.Pages.Orders.POQuoteDetail.registerClass("Rebound.GlobalTrader.Site.Pages.Orders.POQuoteDetail", Rebound.GlobalTrader.Site.Pages.Content, Sys.IDisposable);
