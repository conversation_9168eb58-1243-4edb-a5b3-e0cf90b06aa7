﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Afrikaans" xml:space="preserve">
    <value>Afrikaans</value>
  </data>
  <data name="Albanian" xml:space="preserve">
    <value>Albanian</value>
  </data>
  <data name="Armenian" xml:space="preserve">
    <value>Armenian</value>
  </data>
  <data name="Basque" xml:space="preserve">
    <value>Basque</value>
  </data>
  <data name="Belarusian" xml:space="preserve">
    <value>Belarusian</value>
  </data>
  <data name="Bulgarian" xml:space="preserve">
    <value>Bulgarian</value>
  </data>
  <data name="Chinese" xml:space="preserve">
    <value>Chinese</value>
  </data>
  <data name="Chinese_HongKong" xml:space="preserve">
    <value>Chinese (Hong Kong)</value>
  </data>
  <data name="Chinese_Macau" xml:space="preserve">
    <value>Chinese (Macau)</value>
  </data>
  <data name="Chinese_Singapore" xml:space="preserve">
    <value>Chinese (Singapore)</value>
  </data>
  <data name="Chinese_Taiwan" xml:space="preserve">
    <value>Chinese (Taiwan)</value>
  </data>
  <data name="Croatian" xml:space="preserve">
    <value>Croatian</value>
  </data>
  <data name="Czech" xml:space="preserve">
    <value>Czech</value>
  </data>
  <data name="Danish" xml:space="preserve">
    <value>Danish</value>
  </data>
  <data name="Dutch" xml:space="preserve">
    <value>Dutch</value>
  </data>
  <data name="Dutch_Belgium" xml:space="preserve">
    <value>Dutch (Belgium)</value>
  </data>
  <data name="English" xml:space="preserve">
    <value>English</value>
  </data>
  <data name="English_Australia" xml:space="preserve">
    <value>English (Australia)</value>
  </data>
  <data name="English_Canada" xml:space="preserve">
    <value>English (Canada)</value>
  </data>
  <data name="English_Ireland" xml:space="preserve">
    <value>English (Ireland)</value>
  </data>
  <data name="English_NewZealand" xml:space="preserve">
    <value>English (NewZealand)</value>
  </data>
  <data name="English_SouthAfrica" xml:space="preserve">
    <value>English (SouthAfrica)</value>
  </data>
  <data name="English_UnitedStates" xml:space="preserve">
    <value>English (UnitedStates)</value>
  </data>
  <data name="English_Zimbabwe" xml:space="preserve">
    <value>English (Zimbabwe)</value>
  </data>
  <data name="Estonian" xml:space="preserve">
    <value>Estonian</value>
  </data>
  <data name="Finnish" xml:space="preserve">
    <value>Finnish</value>
  </data>
  <data name="French" xml:space="preserve">
    <value>French</value>
  </data>
  <data name="French_Belgium" xml:space="preserve">
    <value>French (Belgium)</value>
  </data>
  <data name="French_Canada" xml:space="preserve">
    <value>French (Canada)</value>
  </data>
  <data name="French_Luxembourg" xml:space="preserve">
    <value>French (Luxembourg)</value>
  </data>
  <data name="French_Monaco" xml:space="preserve">
    <value>French (Principality of Monaco)</value>
  </data>
  <data name="French_Switzerland" xml:space="preserve">
    <value>French (Switzerland)</value>
  </data>
  <data name="Georgian" xml:space="preserve">
    <value>Georgian</value>
  </data>
  <data name="German" xml:space="preserve">
    <value>German</value>
  </data>
  <data name="German_Austria" xml:space="preserve">
    <value>German (Austria)</value>
  </data>
  <data name="German_Switzerland" xml:space="preserve">
    <value>German (Switzerland)</value>
  </data>
  <data name="Greek" xml:space="preserve">
    <value>Greek</value>
  </data>
  <data name="Gujarati" xml:space="preserve">
    <value>Gujarati</value>
  </data>
  <data name="Hebrew" xml:space="preserve">
    <value>Hebrew</value>
  </data>
  <data name="Hindi" xml:space="preserve">
    <value>Hindi</value>
  </data>
  <data name="hrvatski_BosnaiHercegovina" xml:space="preserve">
    <value>hrvatski</value>
  </data>
  <data name="Hungarian" xml:space="preserve">
    <value>Hungarian</value>
  </data>
  <data name="Icelandic" xml:space="preserve">
    <value>Icelandic</value>
  </data>
  <data name="Indonesian" xml:space="preserve">
    <value>Indonesian</value>
  </data>
  <data name="Italian" xml:space="preserve">
    <value>Italian</value>
  </data>
  <data name="Italian_Switzerland" xml:space="preserve">
    <value>Italian (Switzerland)</value>
  </data>
  <data name="Japanese" xml:space="preserve">
    <value>Japanese</value>
  </data>
  <data name="Kannada" xml:space="preserve">
    <value>Kannada</value>
  </data>
  <data name="Kazakh" xml:space="preserve">
    <value>Kazakh</value>
  </data>
  <data name="Kiswahili_Kenya" xml:space="preserve">
    <value>Kiswahili</value>
  </data>
  <data name="Konkani" xml:space="preserve">
    <value>Konkani</value>
  </data>
  <data name="Korean" xml:space="preserve">
    <value>Korean</value>
  </data>
  <data name="Kyrgyz" xml:space="preserve">
    <value>Kyrgyz</value>
  </data>
  <data name="Latvian" xml:space="preserve">
    <value>Latvian</value>
  </data>
  <data name="Lithuanian" xml:space="preserve">
    <value>Lithuanian</value>
  </data>
  <data name="Macedonian" xml:space="preserve">
    <value>Macedonian</value>
  </data>
  <data name="Malay" xml:space="preserve">
    <value>Malay</value>
  </data>
  <data name="Maltese" xml:space="preserve">
    <value>Maltese</value>
  </data>
  <data name="Marathi" xml:space="preserve">
    <value>Marathi</value>
  </data>
  <data name="Mongolian" xml:space="preserve">
    <value>Mongolian</value>
  </data>
  <data name="Norwegian" xml:space="preserve">
    <value>Norwegian</value>
  </data>
  <data name="Polish" xml:space="preserve">
    <value>Polish</value>
  </data>
  <data name="Portuguese" xml:space="preserve">
    <value>Portuguese</value>
  </data>
  <data name="Portuguese_Brazil" xml:space="preserve">
    <value>Portuguese (Brazil)</value>
  </data>
  <data name="Punjabi" xml:space="preserve">
    <value>Punjabi</value>
  </data>
  <data name="Romanian" xml:space="preserve">
    <value>Romanian</value>
  </data>
  <data name="Russian" xml:space="preserve">
    <value>Russian</value>
  </data>
  <data name="Sanskrit" xml:space="preserve">
    <value>Sanskrit</value>
  </data>
  <data name="Slovak" xml:space="preserve">
    <value>Slovak</value>
  </data>
  <data name="Slovenian" xml:space="preserve">
    <value>Slovenian</value>
  </data>
  <data name="Spanish" xml:space="preserve">
    <value>Spanish</value>
  </data>
  <data name="Spanish_Argentina" xml:space="preserve">
    <value>Spanish (Argentina)</value>
  </data>
  <data name="Spanish_Bolivia" xml:space="preserve">
    <value>Spanish (Bolivia)</value>
  </data>
  <data name="Spanish_Chile" xml:space="preserve">
    <value>Spanish (Chile)</value>
  </data>
  <data name="Spanish_Colombia" xml:space="preserve">
    <value>Spanish (Colombia)</value>
  </data>
  <data name="Spanish_CostaRica" xml:space="preserve">
    <value>Spanish (CostaRica)</value>
  </data>
  <data name="Spanish_DominicanRepublic" xml:space="preserve">
    <value>Spanish (DominicanRepublic)</value>
  </data>
  <data name="Spanish_Ecuador" xml:space="preserve">
    <value>Spanish (Ecuador)</value>
  </data>
  <data name="Spanish_ElSalvador" xml:space="preserve">
    <value>Spanish (ElSalvador)</value>
  </data>
  <data name="Spanish_Guatemala" xml:space="preserve">
    <value>Spanish (Guatemala)</value>
  </data>
  <data name="Spanish_Honduras" xml:space="preserve">
    <value>Spanish (Honduras)</value>
  </data>
  <data name="Spanish_Mexico" xml:space="preserve">
    <value>Spanish (Mexico)</value>
  </data>
  <data name="Spanish_Nicaragua" xml:space="preserve">
    <value>Spanish (Nicaragua)</value>
  </data>
  <data name="Spanish_Panama" xml:space="preserve">
    <value>Spanish (Panama)</value>
  </data>
  <data name="Spanish_Paraguay" xml:space="preserve">
    <value>Spanish (Paraguay)</value>
  </data>
  <data name="Spanish_Peru" xml:space="preserve">
    <value>Spanish (Peru)</value>
  </data>
  <data name="Spanish_PuertoRico" xml:space="preserve">
    <value>Spanish (PuertoRico)</value>
  </data>
  <data name="Spanish_Uruguay" xml:space="preserve">
    <value>Spanish (Uruguay)</value>
  </data>
  <data name="Spanish_Venezuela" xml:space="preserve">
    <value>Spanish (Venezuela)</value>
  </data>
  <data name="Swedish" xml:space="preserve">
    <value>Swedish</value>
  </data>
  <data name="Syriac" xml:space="preserve">
    <value>Syriac</value>
  </data>
  <data name="Tamil" xml:space="preserve">
    <value>Tamil</value>
  </data>
  <data name="Tatar" xml:space="preserve">
    <value>Tatar</value>
  </data>
  <data name="Telugu" xml:space="preserve">
    <value>Telugu</value>
  </data>
  <data name="Thai" xml:space="preserve">
    <value>Thai</value>
  </data>
  <data name="Turkish" xml:space="preserve">
    <value>Turkish</value>
  </data>
  <data name="Ukrainian" xml:space="preserve">
    <value>Ukrainian</value>
  </data>
  <data name="Urdu" xml:space="preserve">
    <value>Urdu</value>
  </data>
  <data name="Vietnamese" xml:space="preserve">
    <value>Vietnamese</value>
  </data>
  <data name="Welsh" xml:space="preserve">
    <value>Welsh</value>
  </data>
</root>