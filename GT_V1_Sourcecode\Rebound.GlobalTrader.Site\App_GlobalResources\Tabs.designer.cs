//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Resources {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option or rebuild the Visual Studio project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.VisualStudio.Web.Application.StronglyTypedResourceProxyBuilder", "16.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class Tabs {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Tabs() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Resources.Tabs", global::System.Reflection.Assembly.Load("App_GlobalResources"));
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Accounts.
        /// </summary>
        internal static string Accounts {
            get {
                return ResourceManager.GetString("Accounts", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to All.
        /// </summary>
        internal static string All {
            get {
                return ResourceManager.GetString("All", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to All Orders.
        /// </summary>
        internal static string AllOrders {
            get {
                return ResourceManager.GetString("AllOrders", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to All Unshipped.
        /// </summary>
        internal static string AllUnshipped {
            get {
                return ResourceManager.GetString("AllUnshipped", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Approval Status.
        /// </summary>
        internal static string ApprovalSatus {
            get {
                return ResourceManager.GetString("ApprovalSatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Approved.
        /// </summary>
        internal static string Approved {
            get {
                return ResourceManager.GetString("Approved", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to AS6081.
        /// </summary>
        internal static string AS6081 {
            get {
                return ResourceManager.GetString("AS6081", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Available.
        /// </summary>
        internal static string Available {
            get {
                return ResourceManager.GetString("Available", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Awaiting.
        /// </summary>
        internal static string Awaiting {
            get {
                return ResourceManager.GetString("Awaiting", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Awaiting Receipt.
        /// </summary>
        internal static string AwaitingReceipt {
            get {
                return ResourceManager.GetString("AwaitingReceipt", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to BOMs.
        /// </summary>
        internal static string BOMs {
            get {
                return ResourceManager.GetString("BOMs", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Broker.
        /// </summary>
        internal static string Broker {
            get {
                return ResourceManager.GetString("Broker", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Calls.
        /// </summary>
        internal static string Calls {
            get {
                return ResourceManager.GetString("Calls", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to On Hold.
        /// </summary>
        internal static string CanNotBeExported {
            get {
                return ResourceManager.GetString("CanNotBeExported", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Certificate.
        /// </summary>
        internal static string Certificate {
            get {
                return ResourceManager.GetString("Certificate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Closed.
        /// </summary>
        internal static string Closed {
            get {
                return ResourceManager.GetString("Closed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Credits.
        /// </summary>
        internal static string CreditNotes {
            get {
                return ResourceManager.GetString("CreditNotes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Credits.
        /// </summary>
        internal static string Credits {
            get {
                return ResourceManager.GetString("Credits", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to CRMAs.
        /// </summary>
        internal static string CRMAs {
            get {
                return ResourceManager.GetString("CRMAs", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer API.
        /// </summary>
        internal static string CustomerAPI {
            get {
                return ResourceManager.GetString("CustomerAPI", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to CRMAs.
        /// </summary>
        internal static string CustomerRMAs {
            get {
                return ResourceManager.GetString("CustomerRMAs", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer Requirements.
        /// </summary>
        internal static string CustReq {
            get {
                return ResourceManager.GetString("CustReq", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Debits.
        /// </summary>
        internal static string DebitNotes {
            get {
                return ResourceManager.GetString("DebitNotes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Debits.
        /// </summary>
        internal static string Debits {
            get {
                return ResourceManager.GetString("Debits", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Detail.
        /// </summary>
        internal static string Detail {
            get {
                return ResourceManager.GetString("Detail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Queried.
        /// </summary>
        internal static string GI_Queried {
            get {
                return ResourceManager.GetString("GI_Queried", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Goods In.
        /// </summary>
        internal static string GoodsIn {
            get {
                return ResourceManager.GetString("GoodsIn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invoices.
        /// </summary>
        internal static string Invoices {
            get {
                return ResourceManager.GetString("Invoices", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Log.
        /// </summary>
        internal static string Log {
            get {
                return ResourceManager.GetString("Log", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Stock Provision.
        /// </summary>
        internal static string LotStockProvision {
            get {
                return ResourceManager.GetString("LotStockProvision", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Main.
        /// </summary>
        internal static string Main {
            get {
                return ResourceManager.GetString("Main", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to None.
        /// </summary>
        internal static string None {
            get {
                return ResourceManager.GetString("None", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Non Zero Stock.
        /// </summary>
        internal static string NonZeroStock {
            get {
                return ResourceManager.GetString("NonZeroStock", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Old.
        /// </summary>
        internal static string Old {
            get {
                return ResourceManager.GetString("Old", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open.
        /// </summary>
        internal static string Open {
            get {
                return ResourceManager.GetString("Open", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to POs.
        /// </summary>
        internal static string POs {
            get {
                return ResourceManager.GetString("POs", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sales BI.
        /// </summary>
        internal static string PowerBIDash {
            get {
                return ResourceManager.GetString("PowerBIDash", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to POs.
        /// </summary>
        internal static string PurchaseOrders {
            get {
                return ResourceManager.GetString("PurchaseOrders", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Purchasing.
        /// </summary>
        internal static string Purchasing {
            get {
                return ResourceManager.GetString("Purchasing", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quarantined.
        /// </summary>
        internal static string Quarantined {
            get {
                return ResourceManager.GetString("Quarantined", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quotes.
        /// </summary>
        internal static string Quotations {
            get {
                return ResourceManager.GetString("Quotations", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quotes.
        /// </summary>
        internal static string Quotes {
            get {
                return ResourceManager.GetString("Quotes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ready to Receive.
        /// </summary>
        internal static string ReadyToReceive {
            get {
                return ResourceManager.GetString("ReadyToReceive", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ready to Ship.
        /// </summary>
        internal static string ReadyToShip {
            get {
                return ResourceManager.GetString("ReadyToShip", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Received.
        /// </summary>
        internal static string Received {
            get {
                return ResourceManager.GetString("Received", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to By CRMA Line.
        /// </summary>
        internal static string RelatedByCRMALine {
            get {
                return ResourceManager.GetString("RelatedByCRMALine", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to By Part.
        /// </summary>
        internal static string RelatedByPart {
            get {
                return ResourceManager.GetString("RelatedByPart", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to By PO Line.
        /// </summary>
        internal static string RelatedByPOLine {
            get {
                return ResourceManager.GetString("RelatedByPOLine", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reqs.
        /// </summary>
        internal static string Reqs {
            get {
                return ResourceManager.GetString("Reqs", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reqs.
        /// </summary>
        internal static string Requirements {
            get {
                return ResourceManager.GetString("Requirements", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sales.
        /// </summary>
        internal static string Sales {
            get {
                return ResourceManager.GetString("Sales", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SOs.
        /// </summary>
        internal static string SalesOrders {
            get {
                return ResourceManager.GetString("SalesOrders", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Services.
        /// </summary>
        internal static string Services {
            get {
                return ResourceManager.GetString("Services", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shipped.
        /// </summary>
        internal static string Shipped {
            get {
                return ResourceManager.GetString("Shipped", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SOs.
        /// </summary>
        internal static string SOs {
            get {
                return ResourceManager.GetString("SOs", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SRMAs.
        /// </summary>
        internal static string SRMAs {
            get {
                return ResourceManager.GetString("SRMAs", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Stock.
        /// </summary>
        internal static string Stock {
            get {
                return ResourceManager.GetString("Stock", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier Approval History.
        /// </summary>
        internal static string SupplierApprovalHistory {
            get {
                return ResourceManager.GetString("SupplierApprovalHistory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SRMAs.
        /// </summary>
        internal static string SupplierRMAs {
            get {
                return ResourceManager.GetString("SupplierRMAs", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Today.
        /// </summary>
        internal static string Today {
            get {
                return ResourceManager.GetString("Today", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier Information.
        /// </summary>
        internal static string TradeRefrences {
            get {
                return ResourceManager.GetString("TradeRefrences", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unreleased.
        /// </summary>
        internal static string Uninspected {
            get {
                return ResourceManager.GetString("Uninspected", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Warehouse.
        /// </summary>
        internal static string Warehouse {
            get {
                return ResourceManager.GetString("Warehouse", resourceCulture);
            }
        }
    }
}
