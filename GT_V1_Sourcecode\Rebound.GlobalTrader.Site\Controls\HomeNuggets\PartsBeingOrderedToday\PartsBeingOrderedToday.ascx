<%@ Control Language="C#" CodeBehind="PartsBeingOrderedToday.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.HomeNuggets.PartsBeingOrderedToday" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Nugget:DesignBase id="ctlDB" runat="server">
	<Content>
		<div class="homepageNugget">
		    <asp:Panel ID="pnlPartsOrdered" runat="server">
			    <ReboundUI:SimpleDataTable ID="tblPartsOrdered" runat="server" AllowSelection="false" />
		    </asp:Panel>
		    <asp:Panel ID="pnlMore" runat="server" CssClass="homeNuggetMoreLink">
				<ReboundUI:PageHyperLink id="lnkMore" runat="server" PageType="Orders_PurchaseOrderBrowse" OverrideTextResource="MoreOpenPurchaseOrders" CssClass="nubButton nubButtonAlignLeft" />
			</asp:Panel>
		</div>
	</Content>
</ReboundUI_Nugget:DesignBase>
