
<%--
	[002]  Ravi          19-09-2023      RP-2338  AS6081 Search/Filter functionality on different pages 
--%>
<%@ Control Language="C#" CodeBehind="AllStock.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.DataListNuggets.AllStock" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Nugget:DesignBase ID="ctlDB" runat="server" BoxType="List">
	<Filters>
		<ReboundUI_DataListNugget:Filter ID="ctlFilter" runat="server">
			<FieldsLeft>
				<ReboundUI_FilterDataItemRow:TextBox id="ctlPart" runat="server" ResourceTitle="PartNo" FilterField="Part" />
				<ReboundUI_FilterDataItemRow:DropDown id="ctlLot" runat="server" DropDownType="Lot" ResourceTitle="Lot" DropDownAssembly="Rebound.GlobalTrader.Site" FilterField="Lot" />
				<ReboundUI_FilterDataItemRow:DropDown id="ctlWarehouse" runat="server" DropDownType="Warehouse" ResourceTitle="Warehouse" DropDownAssembly="Rebound.GlobalTrader.Site" FilterField="Warehouse" />
				<ReboundUI_FilterDataItemRow:CheckBox id="ctlRecentOnly" runat="server" ResourceTitle="RecentOnly" FilterField="RecentOnly" DefaultValue="true" />
				<ReboundUI_FilterDataItemRow:CheckBox id="ctlIncludeZeroStock" runat="server" ResourceTitle="IncludeZeroStock" FilterField="IncludeZeroStock" />
				<%--[001]Code Start--%>
				<ReboundUI_FilterDataItemRow:DropDown id="ctlClientName" runat="server" ResourceTitle="ClientName" DropDownType="Client" DropDownAssembly="Rebound.GlobalTrader.Site" FilterField="Client"  />				
				<%--[001]Code End--%>
			</FieldsLeft>
			<FieldsRight>
				<ReboundUI_FilterDataItemRow:TextBox id="ctlLocation" runat="server" ResourceTitle="Location" FilterField="Location" />
				<ReboundUI_FilterDataItemRow:Numerical id="ctlPONo" runat="server" ResourceTitle="PurchaseOrderNo" FilterField="PONo"  TextBoxMaxLength="10"  />
				<ReboundUI_FilterDataItemRow:Numerical id="ctlCRMANo" runat="server" ResourceTitle="CustomerRMANo" FilterField="CRMANo" />
				<ReboundUI_FilterDataItemRow:TextBox id="ctlSupplierName" runat="server" ResourceTitle="SupplierName" FilterField="SupplierName" />
				<ReboundUI_FilterDataItemRow:TextBox id="ctlSupplierPart" runat="server" ResourceTitle="SupplierPartNo" FilterField="SupplierPart" />
                <ReboundUI_FilterDataItemRow:Numerical id="Numerical1" runat="server" ResourceTitle="StockNo" FilterField="StockNo"  TextBoxMaxLength="10"  />
				<ReboundUI_FilterDataItemRow:DropDown id="ctlAS6081" runat="server" ResourceTitle="AS6081Filter" DropDownType="CounterfeitElectronicParts" DropDownAssembly="Rebound.GlobalTrader.Site" FilterField="AS6081" /> <%--[002]--%>
			</FieldsRight>

		</ReboundUI_DataListNugget:Filter>
	</Filters>
</ReboundUI_Nugget:DesignBase>
