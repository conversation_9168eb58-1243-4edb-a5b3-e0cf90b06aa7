﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="CommunicationLogType_Add" xml:space="preserve">
    <value>Enter the changed details for the CommunicationLog Type and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="CommunicationLogType_Edit" xml:space="preserve">
    <value>Enter all the details of the new CommunicationLog Type and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="CompanyType_Add" xml:space="preserve">
    <value>Enter the changed details for the Company Type and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="CompanyType_Edit" xml:space="preserve">
    <value>Enter all the details of the new Company Type and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="CountryAdd_EnterDetail" xml:space="preserve">
    <value>Enter all the details of the new Country and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="Country_Edit" xml:space="preserve">
    <value>Enter the changed details for the Country and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="Currency_Add" xml:space="preserve">
    <value>Enter all the details of the new Currency and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="Currency_Edit" xml:space="preserve">
    <value>Enter the changed details for the Currency and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="Division_Add" xml:space="preserve">
    <value>Enter all the details of the new Division and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="Division_Edit" xml:space="preserve">
    <value>Enter the changed details for the Division and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="EnterDetailsAndPressSave" xml:space="preserve">
    <value>Enter all the details and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="ManufacturerMainInfo_Edit" xml:space="preserve">
    <value>Enter the details for the Manufacturer and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="GlobalCountryList_Edit" xml:space="preserve">
    <value>Amend the details of the Master Country and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="GlobalCurrencyList_Edit" xml:space="preserve">
    <value>Amend the details of the Master Currency and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="IndustryType_Add" xml:space="preserve">
    <value>Enter the changed details for the Industry Type and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="IndustryType_Edit" xml:space="preserve">
    <value>Enter all the details of the new Industry Type and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="Package_Add" xml:space="preserve">
    <value>Enter the changed details for the Package and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="Package_Edit" xml:space="preserve">
    <value>Enter all the details of the new Package and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="POAdd_EnterDetail" xml:space="preserve">
    <value>Enter the details of the new Purchase Order and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="Product_Add" xml:space="preserve">
    <value>Enter all the details of the new Product and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="Product_Edit" xml:space="preserve">
    <value>Enter the changed details for the Product and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="QuoteAdd_EnterDetail" xml:space="preserve">
    <value>Enter the details of the new Quote and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="QuoteAdd_SelectCompany" xml:space="preserve">
    <value>Select the Company for which you would like to add this Quote</value>
  </data>
  <data name="QuoteAdd_SelectLines" xml:space="preserve">
    <value>Select the lines from the Requirement you would like to carry into the new quote and press &lt;b&gt;Continue&lt;/b&gt;</value>
  </data>
  <data name="QuoteAdd_SelectRequirement" xml:space="preserve">
    <value>Select the Requirement and Sourcing Results on which you would like to base this new Quote and press &lt;b&gt;Continue&lt;/b&gt;</value>
  </data>
  <data name="QuoteAdd_SelectSource" xml:space="preserve">
    <value>Select the source for the new Quote and press &lt;b&gt;Continue&lt;/b&gt;</value>
  </data>
  <data name="ShipVia_Add" xml:space="preserve">
    <value>Enter all the details of the new Shipping Method and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="ShipVia_Edit" xml:space="preserve">
    <value>Enter the changed details for the Shipping Method and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="SOAdd_EnterDetail" xml:space="preserve">
    <value>Enter the details of the new Sales Order and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="SOAdd_SelectCompany" xml:space="preserve">
    <value>Select the Company for which you would like to add this Sales Order</value>
  </data>
  <data name="SOLine_EditDetails" xml:space="preserve">
    <value>Edit the details of the new line and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="SOLine_SelectItem" xml:space="preserve">
    <value>Search for and select the item you would like to use as the source for the new line and press &lt;b&gt;Continue&lt;/b&gt;</value>
  </data>
  <data name="SOLine_SelectSource" xml:space="preserve">
    <value>Select the source of the new line and press &lt;b&gt;Continue&lt;/b&gt;</value>
  </data>
  <data name="SOLines_Deallocate" xml:space="preserve">
    <value>Are you sure you would like to remove the selected allocation(s)?</value>
  </data>
  <data name="SOLines_Delete" xml:space="preserve">
    <value>Are you sure you would like to delete this Sales Order Line?</value>
  </data>
  <data name="StockLogReason_Add" xml:space="preserve">
    <value>Enter all the details of the new StockLog Reason and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="StockLogReason_Edit" xml:space="preserve">
    <value>Enter the changed details for the StockLog Reason and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="Tax_Add" xml:space="preserve">
    <value>Enter all the details of the new Tax and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="Tax_Edit" xml:space="preserve">
    <value>Enter the changed details for the Tax and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="Team_Add" xml:space="preserve">
    <value>Enter all the details of the new Team and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="Team_Edit" xml:space="preserve">
    <value>Enter the changed details for the Team and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="Terms_Add" xml:space="preserve">
    <value>Enter all the details of the new Terms and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="Terms_Edit" xml:space="preserve">
    <value>Enter the changed details for the Terms and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="User_Add" xml:space="preserve">
    <value>Enter all the details of the new User and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="User_Edit" xml:space="preserve">
    <value>Enter the changed details for the User and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="Warehouse_Add" xml:space="preserve">
    <value>Enter all the details of the new Warehouse and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="CompanyManufacturers_Delete" xml:space="preserve">
    <value>Are you sure you would like to remove this Manufacturer?</value>
  </data>
  <data name="ManufacturerSuppliers_Delete" xml:space="preserve">
    <value>Are you sure you would like to remove this Supplier?</value>
  </data>
  <data name="Reason_Add" xml:space="preserve">
    <value>Enter all the details of the new Reason and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="Reason_Edit" xml:space="preserve">
    <value>Enter the changed details for the Reason and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="MailMessages_DeleteFolder" xml:space="preserve">
    <value>Are you sure you would like to delete this folder?</value>
  </data>
  <data name="MailMessages_DeleteMessage" xml:space="preserve">
    <value>Are you sure you would like to delete this/these message(s)?</value>
  </data>
  <data name="MailMessages_EditFolder" xml:space="preserve">
    <value>Enter a new name for this folder and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="MailMessages_MoveMessage" xml:space="preserve">
    <value>Select a destination folder for this/these message(s) and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="MailMessages_NewFolder" xml:space="preserve">
    <value>Enter a name for the new folder and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="MailMessages_NewMessage" xml:space="preserve">
    <value>Enter the details of the new message and press &lt;b&gt;Send&lt;/b&gt;</value>
  </data>
  <data name="ManufacturerCompanies_Add" xml:space="preserve">
    <value>Select a new company and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="ManufacturerCompanies_Delete" xml:space="preserve">
    <value>Are you sure you would like to remove this related Company?</value>
  </data>
  <data name="Sourcing_AddToReq" xml:space="preserve">
    <value>Are you sure you would like to add the selected item(s) to the requirement?</value>
  </data>
  <data name="Sourcing_EditOffer" xml:space="preserve">
    <value>Edit the details of the offer and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="QuoteMainInfo_Edit" xml:space="preserve">
    <value>Amend the details and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="CompanyAdd_Add" xml:space="preserve">
    <value>Enter the details of the new Company and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="CompanyAddresses_Add" xml:space="preserve">
    <value>Enter the details of the new Address and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="CompanyAddresses_Cease" xml:space="preserve">
    <value>Are you sure you would like to cease this address?</value>
  </data>
  <data name="CompanyAddresses_DefaultBill" xml:space="preserve">
    <value>Are you sure you would like to make this address the default Billing Address for this Company?</value>
  </data>
  <data name="CompanyAddresses_Edit" xml:space="preserve">
    <value>Amend the details and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="CusReqAdd_EnterDetail" xml:space="preserve">
    <value>Enter the detail of the new Requirement</value>
  </data>
  <data name="CusReqAdd_Notify" xml:space="preserve">
    <value>Notify others of the new Requirement</value>
  </data>
  <data name="CusReqAdd_SelectCompany" xml:space="preserve">
    <value>Select the Company for the new Requirement</value>
  </data>
  <data name="ManufacturerAdd_Add" xml:space="preserve">
    <value>Enter the details of the new Manufacturer and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="ManufacturerSuppliers_AddEdit" xml:space="preserve">
    <value>Enter the details and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="QuoteLine_Add_EditDetails" xml:space="preserve">
    <value>Enter the details of the new Quote Line and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="QuoteLine_Add_SelectItem" xml:space="preserve">
    <value>Search for and select the item you would like to use as the source for the new Quote Line and press &lt;b&gt;Continue&lt;/b&gt;</value>
  </data>
  <data name="QuoteLine_Add_SelectSource" xml:space="preserve">
    <value>Select the source for the new Line</value>
  </data>
  <data name="QuoteLines_Close" xml:space="preserve">
    <value>Are you sure you would like to close this Quote Line?</value>
  </data>
  <data name="QuoteLines_Edit" xml:space="preserve">
    <value>Edit the details of the Quote Line and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="SOLine_Add_EditDetails" xml:space="preserve">
    <value>Enter the details of the new Line and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="SOLine_Add_SelectItem" xml:space="preserve">
    <value>Search for and select the item you would like to use as the source for the new Line and press &lt;b&gt;Continue&lt;/b&gt;</value>
  </data>
  <data name="SOLine_Add_SelectSource" xml:space="preserve">
    <value>Select the source for the new Line</value>
  </data>
  <data name="SOLine_Allocate_SelectStock" xml:space="preserve">
    <value>Select the stock item to allocate to this Line and press &lt;b&gt;Continue&lt;/b&gt;</value>
  </data>
  <data name="SOLine_Alocate_EditDetails" xml:space="preserve">
    <value>Edit the details of the allocation and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="MailMessages_MarkAsToDo" xml:space="preserve">
    <value>Enter the details of the new To Do item and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="ToDo_Delete" xml:space="preserve">
    <value>Are you sure you would like to delete the selected To Do item(s)?</value>
  </data>
  <data name="ToDo_Edit" xml:space="preserve">
    <value>Enter all of the details and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="ToDo_MarkComplete" xml:space="preserve">
    <value>Are you sure you would like to mark the selected To Do item(s) as complete?</value>
  </data>
  <data name="ToDo_MarkIncomplete" xml:space="preserve">
    <value>Are you sure you would like to mark the selected To Do item(s) as incomplete?</value>
  </data>
  <data name="UserPreferences_Edit" xml:space="preserve">
    <value>Edit the details and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="UserProfile_Edit" xml:space="preserve">
    <value>Edit the details and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="Warehouse_Edit" xml:space="preserve">
    <value>Enter the changed details for the Warehouse and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="UserProfile_ChangePassword" xml:space="preserve">
    <value>Enter your new and old passwords and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="CusReqMainInfo_Edit" xml:space="preserve">
    <value>Amend the details of the Requirement and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="CusReqMainInfo_AddAlternate" xml:space="preserve">
    <value>Enter the details of the Alternate Part Requirement and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="CusReqSourcingResults_Add" xml:space="preserve">
    <value>Enter the details of the Sourcing Result and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="CusReqSourcingResults_Edit" xml:space="preserve">
    <value>Amend the details of the Sourcing Result and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="QuoteAdd_Notify" xml:space="preserve">
    <value>Notify others of the new Quote</value>
  </data>
  <data name="CRMALines_Add" xml:space="preserve">
    <value>Enter all the details of the new Customer RMA Line and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="CRMALines_Delete" xml:space="preserve">
    <value>Are you sure you would like to delete this Customer RMA Line?</value>
  </data>
  <data name="CRMALines_Edit" xml:space="preserve">
    <value>Edit the details of the Customer RMA Line and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="SRMALines_Add" xml:space="preserve">
    <value>Enter all the details of the new Supplier RMA Line and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="SRMALines_Delete" xml:space="preserve">
    <value>Are you sure you would like to delete this Supplier RMA Line?</value>
  </data>
  <data name="SRMALines_Edit" xml:space="preserve">
    <value>Edit the details of the Supplier RMA Line and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="SRMAMainInfo_Edit" xml:space="preserve">
    <value>Amend the details and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="SRMA_Add" xml:space="preserve">
    <value>Enter all the details of the new Supplier RMA and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="SRMAAdd_EnterDetail" xml:space="preserve">
    <value>Enter all the details of the new Supplier RMA and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="SRMAAdd_Notify" xml:space="preserve">
    <value>Notify others of the new Supplier RMA</value>
  </data>
  <data name="SRMAAdd_SelectPO" xml:space="preserve">
    <value>Select the Purchase Order on which you would like to base this new Supplier RMA and press &lt;b&gt;Continue&lt;/b&gt;</value>
  </data>
  <data name="SRMALines_Add_EnterDetail" xml:space="preserve">
    <value>Edit the details of the new line and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="SRMALines_Add_SelectItem" xml:space="preserve">
    <value>Select the Purchase Order line you would like to use as the source for the new Line</value>
  </data>
  <data name="CRMA_Add" xml:space="preserve">
    <value>Enter all the details of the new Customer RMA and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="CRMAAdd_EnterDetail" xml:space="preserve">
    <value>Enter all the details of the new Customer RMA and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="CRMAAdd_Notify" xml:space="preserve">
    <value>Notify others of the new Customer RMA</value>
  </data>
  <data name="CRMAAdd_SelectInvoice" xml:space="preserve">
    <value>Select the Invoice on which you would like to base this new Customer RMA and press &lt;b&gt;Continue&lt;/b&gt;</value>
  </data>
  <data name="CRMALines_Add_EnterDetail" xml:space="preserve">
    <value>Edit the details of the new line and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="CRMALines_Add_SelectItem" xml:space="preserve">
    <value>Search for and select the invoice line you would like to use as the source for the new Line and press &lt;b&gt;Continue&lt;/b&gt;</value>
  </data>
  <data name="CRMAMainInfo_Edit" xml:space="preserve">
    <value>Amend the details and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="Feedback_Add" xml:space="preserve">
    <value>Please send us your thoughts and comments about &lt;b&gt;Rebound Global:Trader&lt;/b&gt;
&lt;br /&gt;We are committed to its continual improvement and would love to hear any suggestions you have. 
&lt;br /&gt;Thank you.</value>
  </data>
  <data name="SecurityGroupMembers_EditMembers" xml:space="preserve">
    <value>Select the Members for this Security Group and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="SecurityGroupPermissionsGeneral_Edit" xml:space="preserve">
    <value>Edit the permissions and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="SecurityGroupPermissionsReports_Edit" xml:space="preserve">
    <value>Edit the permissions and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="SecurityGroups_Add" xml:space="preserve">
    <value>Enter the details of the new Security Group and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="SecurityGroups_Delete" xml:space="preserve">
    <value>Are you sure you would like to delete this Security Group?</value>
  </data>
  <data name="SecurityGroups_Edit" xml:space="preserve">
    <value>Amend the details of the Security Group and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="CRMAReceivingLines_Receive" xml:space="preserve">
    <value>Confirm the receipt of the Customer RMA Line and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="CompanyMainInfo_Edit" xml:space="preserve">
    <value>Amend the details of the Company and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="POReceivingLines_Receive" xml:space="preserve">
    <value>Confirm the receipt of the Purchase Order Line and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="POReceivingLines_Receive_NewOrExisting" xml:space="preserve">
    <value>Enter the received Purchase Order Line against a new Goods In Note or an existing one and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="POReceivingLines_Receive_Detail" xml:space="preserve">
    <value>Enter details of the new Goods In Note line and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="POReceivingLines_Receive_Header" xml:space="preserve">
    <value>Enter details of the new Goods In Note</value>
  </data>
  <data name="POReceivingLines_Receive_Notify" xml:space="preserve">
    <value>Notify others of the new Goods In Note detail</value>
  </data>
  <data name="InvoiceLines_Add" xml:space="preserve">
    <value>Enter all the details of the new Invoice Line and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="InvoiceLines_Edit" xml:space="preserve">
    <value>Edit the details of the Invoice Line and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="InvoiceLines_EditAllocation" xml:space="preserve">
    <value>Edit the details of the Invoice Line Allocation and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="InvoiceMainInfo_Edit" xml:space="preserve">
    <value>Edit the details of the Invoice and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="SecurityUserGroups_EditMembers" xml:space="preserve">
    <value>Select the members of this Group and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="SecurityUsers_Add" xml:space="preserve">
    <value>Enter the details of the new Security User and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="SecurityUsers_Disable" xml:space="preserve">
    <value>Are you sure you would like to disable this Security Group?</value>
  </data>
  <data name="SecurityUsers_Edit" xml:space="preserve">
    <value>Edit the details of the Security User and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="SecurityUsers_Enable" xml:space="preserve">
    <value>Are you sure you would like to enable this Security Group?</value>
  </data>
  <data name="SourcingLinks_Add" xml:space="preserve">
    <value>Enter the details of the new Sourcing Link and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="SourcingLinks_Delete" xml:space="preserve">
    <value>Are you sure you would like to delete this Sourcing Link?</value>
  </data>
  <data name="SourcingLinks_Edit" xml:space="preserve">
    <value>Edit the details of the Sourcing Link and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="UserProfile_ResetPassword" xml:space="preserve">
    <value>Are you sure you would like to reset the password? It will be set to the username.</value>
  </data>
  <data name="GlobalCurrencyList_Add" xml:space="preserve">
    <value>Enter the details of the new Currency and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="CurrencyAdd_EnterDetail" xml:space="preserve">
    <value>Enter the details of the Currency and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="CurrencyAdd_SelectSource" xml:space="preserve">
    <value>Select the source of the Currency from the Master Currency List and press &lt;b&gt;Continue&lt;/b&gt;</value>
  </data>
  <data name="POMainInfo_Edit" xml:space="preserve">
    <value>Amend the details and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="POLines_Edit" xml:space="preserve">
    <value>Edit the details of the Purchase Order line and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="POLine_Add_EditDetails" xml:space="preserve">
    <value>Enter the details of the new Line and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="POLine_Add_SelectItem" xml:space="preserve">
    <value>Search for and select the item you would like to use as the source for the new Line and press &lt;b&gt;Continue&lt;/b&gt;</value>
  </data>
  <data name="POLine_Add_SelectSource" xml:space="preserve">
    <value>Select the source for the new Line</value>
  </data>
  <data name="POLines_Delete" xml:space="preserve">
    <value>Are you sure you would like to delete this Purchase Order Line?</value>
  </data>
  <data name="POAdd_Notify" xml:space="preserve">
    <value>Notify others of the new Purchase Order</value>
  </data>
  <data name="SOAdd_Notify" xml:space="preserve">
    <value>Notify others of the new Sales Order</value>
  </data>
  <data name="GIAdd_EnterDetail" xml:space="preserve">
    <value>Edit the details of the new Goods In Note and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="GIAdd_Notify" xml:space="preserve">
    <value>Notify others of the new Goods In Note</value>
  </data>
  <data name="GIAdd_SelectPO" xml:space="preserve">
    <value>Select the Purchase Order on which you would like to base this new Goods In Note and press &lt;b&gt;Continue&lt;/b&gt;</value>
  </data>
  <data name="InvoiceAdd_EnterDetail" xml:space="preserve">
    <value>Edit the details of the new Invoice and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="InvoiceAdd_Notify" xml:space="preserve">
    <value>Notify others of the new Invoice</value>
  </data>
  <data name="InvoiceAdd_SelectSO" xml:space="preserve">
    <value>Select the Sales Order on which you would like to base this new Invoice and press &lt;b&gt;Continue&lt;/b&gt;</value>
  </data>
  <data name="ContactsForCompany_MakeDefaultPO" xml:space="preserve">
    <value>Set as Company's Default Contact for Purchase Orders</value>
  </data>
  <data name="ContactsForCompany_MakeDefaultSO" xml:space="preserve">
    <value>Set as Company's Default Contact for Sales Orders</value>
  </data>
  <data name="SOShippingLines_Ship" xml:space="preserve">
    <value>Confirm the shipment of the Sales Order Line and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="SOShippingLines_Ship_Detail" xml:space="preserve">
    <value>Enter details of the new Invoice line and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="SOShippingLines_Ship_NewHeader" xml:space="preserve">
    <value>Enter details of the new Invoice</value>
  </data>
  <data name="SOShippingLines_Ship_Notify" xml:space="preserve">
    <value>Notify others of the new Invoice detail</value>
  </data>
  <data name="Currency_EditRates" xml:space="preserve">
    <value>Edit the current Currency Rates and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="CountryAdd_SelectSource" xml:space="preserve">
    <value>Select the source for the new Country and press &lt;b&gt;Continue&lt;/b&gt;</value>
  </data>
  <data name="SOMainInfo_Edit" xml:space="preserve">
    <value>Amend the details and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="StockAllocations_Deallocate" xml:space="preserve">
    <value>Are you sure you would like to Deallocate this Stock?</value>
  </data>
  <data name="StockMainInfo_Edit" xml:space="preserve">
    <value>Edit the details of the Stock and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="StockMainInfo_Quarantine" xml:space="preserve">
    <value>Are you sure you would like to quarantine this Stock?</value>
  </data>
  <data name="StockMainInfo_Split" xml:space="preserve">
    <value>Edit the details of the split press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="CompanyPurchaseInfo_Edit" xml:space="preserve">
    <value>Amend the purchasing details of the Company and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="CompanySalesInfo_Edit" xml:space="preserve">
    <value>Amend the sales details of the Company and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="StockMainInfo_MakeAvailable" xml:space="preserve">
    <value>Are you sure you would like to release this Stock?</value>
  </data>
  <data name="CommunicationLog_AddEdit" xml:space="preserve">
    <value>Enter the details of the Contact Log item and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="Tax_EditRates" xml:space="preserve">
    <value>Amend all the Tax Rates and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="LotItems_Delete_Service" xml:space="preserve">
    <value>Are you sure you would like to delete the unallocated Services for this Lot?</value>
  </data>
  <data name="LotItems_Delete_Stock" xml:space="preserve">
    <value>Are you sure you would like to delete the unallocated Stock for this Lot?</value>
  </data>
  <data name="LotItems_Transfer_Service" xml:space="preserve">
    <value>Select the new Lot to transfer the Services to and confirm you are sure</value>
  </data>
  <data name="LotItems_Transfer_Stock" xml:space="preserve">
    <value>Select the new Lot to transfer the Stock to and confirm you are sure</value>
  </data>
  <data name="LotMainInfo_Edit" xml:space="preserve">
    <value>Edit the details and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="ContactExtendedInfo_Edit" xml:space="preserve">
    <value>Enter the extended information for the Contact and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="CreditLines_Add" xml:space="preserve">
    <value>Enter all the details of the new Credit Line and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="CreditLines_Delete" xml:space="preserve">
    <value>Are you sure you would like to delete this Credit Line?</value>
  </data>
  <data name="CreditLines_Edit" xml:space="preserve">
    <value>Edit the details of the Credit Line and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="CreditMainInfo_Edit" xml:space="preserve">
    <value>Amend the details and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="CreditLine_Add_EditDetails" xml:space="preserve">
    <value>Edit the details of the new line and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="CreditLine_Add_SelectItem" xml:space="preserve">
    <value>Search for and select the item you would like to use as the source for the new line and press &lt;b&gt;Continue&lt;/b&gt;</value>
  </data>
  <data name="CreditLine_Add_SelectSource" xml:space="preserve">
    <value>Select the source for the new Line</value>
  </data>
  <data name="CompanyManufacturers_AddEdit" xml:space="preserve">
    <value>Specify the details of the Manufacturer and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="LotAdd_Add" xml:space="preserve">
    <value>Enter the details of the new Lot and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="ServiceAdd_Add" xml:space="preserve">
    <value>Enter the details of the new Service and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="SOLines_Edit" xml:space="preserve">
    <value>Edit the details and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="Credit_Add" xml:space="preserve">
    <value>Enter all the details of the new Credit and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="CreditAdd_EnterDetail" xml:space="preserve">
    <value>Enter all the details of the new Credit and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="CreditAdd_Notify" xml:space="preserve">
    <value>Notify others of the new Credit </value>
  </data>
  <data name="CreditAdd_SelectSource" xml:space="preserve">
    <value>Select the source of this new Credit and press &lt;b&gt;Continue&lt;/b&gt;</value>
  </data>
  <data name="StockAdd_Add" xml:space="preserve">
    <value>Enter the details of the new Stock and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="Debit_Add" xml:space="preserve">
    <value>Enter all the details of the new Debit and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="DebitAdd_EnterDetail" xml:space="preserve">
    <value>Enter all the details of the new Debit and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="DebitAdd_Notify" xml:space="preserve">
    <value>Notify others of the new Debit </value>
  </data>
  <data name="DebitAdd_SelectPO" xml:space="preserve">
    <value>Select the Purchase Order on which you would like to base this new Debit and press &lt;b&gt;Continue&lt;/b&gt;</value>
  </data>
  <data name="DebitLine_Add_EditDetails" xml:space="preserve">
    <value>Edit the details of the new line and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="DebitLine_Add_SelectSource" xml:space="preserve">
    <value>Select the source for the new Line</value>
  </data>
  <data name="DebitLines_Add" xml:space="preserve">
    <value>Enter all the details of the new Debit Line and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="DebitLines_Delete" xml:space="preserve">
    <value>Are you sure you would like to delete this Debit Line?</value>
  </data>
  <data name="DebitLines_Edit" xml:space="preserve">
    <value>Edit the details of the Debit Line and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="DebitMainInfo_Edit" xml:space="preserve">
    <value>Amend the details and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="CRMAReceivingLines_Receive_Detail" xml:space="preserve">
    <value>Enter details of the new Goods In Note line and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="CRMAReceivingLines_Receive_Notify" xml:space="preserve">
    <value>Notify others of the new Goods In Note detail</value>
  </data>
  <data name="MailMessageGroupMembers_EditMembers" xml:space="preserve">
    <value>Edit the members of the Mail Group and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="MailMessageGroups_Add" xml:space="preserve">
    <value>Enter the details of the new Mail Group and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="MailMessageGroups_Delete" xml:space="preserve">
    <value>Are you sure you would like to delete the Mail Group?</value>
  </data>
  <data name="MailMessageGroups_Edit" xml:space="preserve">
    <value>Amend the details of the Mail Group and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="Sourcing_AddToReq_Confirm" xml:space="preserve">
    <value>Are you sure you would like to add this/these items to the Customer Requirement?</value>
  </data>
  <data name="Sourcing_AddToReq_SelectCusReq" xml:space="preserve">
    <value>Select a Customer Requirement</value>
  </data>
  <data name="Sourcing_RFQ" xml:space="preserve">
    <value>Enter the details of the message(s) and press &lt;b&gt;Send&lt;/b&gt;</value>
  </data>
  <data name="CreditAdd_SelectItem" xml:space="preserve">
    <value>Select the item on which you would like to base this new Credit Note and press &lt;b&gt;Continue&lt;/b&gt;</value>
  </data>
  <data name="CreditAdd_SelectCRMA" xml:space="preserve">
    <value>Select the Customer RMA on which you would like to base this new Credit Note and press &lt;b&gt;Continue&lt;/b&gt;</value>
  </data>
  <data name="CreditAdd_SelectInvoice" xml:space="preserve">
    <value>Select the Invoice on which you would like to base this new Credit Note and press &lt;b&gt;Continue&lt;/b&gt;</value>
  </data>
  <data name="GILines_Delete" xml:space="preserve">
    <value>Are you sure you would like to delete this Goods In Line?</value>
  </data>
  <data name="GILines_Edit" xml:space="preserve">
    <value>Edit the details and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="GIMainInfo_Edit" xml:space="preserve">
    <value>Amend the details and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="ContactMainInfo_Edit" xml:space="preserve">
    <value>Edit the details and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="CompanyAddresses_DefaultShip" xml:space="preserve">
    <value>Are you sure you would like to make this address the default Shipping Address for this Company?</value>
  </data>
  <data name="StockImages_Add" xml:space="preserve">
    <value>Select a new Image (maximum size {0}mb) and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="LotMainInfo_Delete" xml:space="preserve">
    <value>Are you sure you would like to delete this Lot?</value>
  </data>
  <data name="ServiceMainInfo_Edit" xml:space="preserve">
    <value>Edit the details of the Service and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="StockImages_Delete" xml:space="preserve">
    <value>Are you sure you would like to delete this Image?</value>
  </data>
  <data name="DocHeaderImage_Add" xml:space="preserve">
    <value>Select a new image for the Document Headers, 710 pixels wide and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="DocHeaderImage_Delete" xml:space="preserve">
    <value>Are you sure you would like to delete the Document Header Image?</value>
  </data>
  <data name="DocFooters_Edit" xml:space="preserve">
    <value>Edit the text of the Document Footer and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="GILines_Inspect" xml:space="preserve">
    <value>Confirm the inspection details and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="CompanyPurchasingInfo_Edit" xml:space="preserve">
    <value>Edit the Purchasing Information and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="ContactsForCompany_Add" xml:space="preserve">
    <value>Enter the details of the new Contact and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="ContactsForCompany_Delete" xml:space="preserve">
    <value>Are you sure you would like to delete this Contact?</value>
  </data>
  <data name="POLines_Deallocate" xml:space="preserve">
    <value>Are you sure you would like to deallocate this Purchase Order Line?</value>
  </data>
  <data name="SRMALine_Allocate_SelectStock" xml:space="preserve">
    <value>Choose the Stock Item to allocate to this SRMA</value>
  </data>
  <data name="SRMALine_Alocate_EditDetails" xml:space="preserve">
    <value>Edit the details of the SRMA Line and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="SRMALines_Deallocate" xml:space="preserve">
    <value>Are you sure you want to remove the selected Allocation(s)?</value>
  </data>
  <data name="ServiceMainInfo_Delete" xml:space="preserve">
    <value>Are you sure you would like to delete this Service?</value>
  </data>
  <data name="POReceivingLines_Receive_Header_ExistingGI" xml:space="preserve">
    <value>Select an existing Goods In Note</value>
  </data>
  <data name="POReceivingLines_Receive_Header_NewGI" xml:space="preserve">
    <value>Enter details of the new Goods In Note and press &lt;b&gt;Continue&lt;/b&gt;</value>
  </data>
  <data name="POReceivingLines_Receive_Target" xml:space="preserve">
    <value>Select the target for the new receipt and press &lt;b&gt;Continue&lt;/b&gt;</value>
  </data>
  <data name="SOShippingLines_Ship_SelectInvoice" xml:space="preserve">
    <value>Select the Invoice against which to add the shipped Sales Order Line(s)</value>
  </data>
  <data name="SOShippingLines_Ship_Target" xml:space="preserve">
    <value>Select whether the shipped Sales Order Line(s) should be entered against a new Invoice or an existing one and press &lt;b&gt;Continue&lt;/b&gt;</value>
  </data>
  <data name="EmailDocument" xml:space="preserve">
    <value>Enter the details of the email and press &lt;b&gt;Send&lt;/b&gt;</value>
  </data>
  <data name="SRMALine_Allocate_EditDetails" xml:space="preserve">
    <value>Edit the details of the allocation and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="POAdd_SelectCompany" xml:space="preserve">
    <value>Select the Company from which you are buying</value>
  </data>
  <data name="Sequencer_Edit" xml:space="preserve">
    <value>Edit the Sequence Numbers and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="DebitLine_Add_SelectPOLine" xml:space="preserve">
    <value>Select the PO Line you would like to use as the source for the new line</value>
  </data>
  <data name="DebitLine_Add_SelectService" xml:space="preserve">
    <value>Search for and select the Service you would like to use as the source for the new line and press &lt;b&gt;Continue&lt;/b&gt;</value>
  </data>
  <data name="CRMAReceivingLines_Receive_Header" xml:space="preserve">
    <value>Enter details of the new Goods In Note and press &lt;b&gt;Continue&lt;/b&gt;</value>
  </data>
  <data name="CRMAReceivingLines_Receive_SelectItem" xml:space="preserve">
    <value>Select the Invoice Line Allocation you would like to use for the Goods In Line</value>
  </data>
  <data name="CRMAReceivingLines_Receive_Source" xml:space="preserve">
    <value>Select the source for the new Customer RMA Line and press &lt;b&gt;Continue&lt;/b&gt;</value>
  </data>
  <data name="SRMAShippingLines_Ship_SelectSource" xml:space="preserve">
    <value>Select the source for the new Debit Note and press &lt;b&gt;Continue&lt;/b&gt;</value>
  </data>
  <data name="SRMAShippingLines_Ship_Detail" xml:space="preserve">
    <value>Enter details of the new Debit Line and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="SRMAShippingLines_Ship_Header" xml:space="preserve">
    <value>Enter details of the new Debit Note press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="SRMAShippingLines_Ship_Notify" xml:space="preserve">
    <value>Notify others of the new Debit Note?</value>
  </data>
  <data name="SOMainInfo_Notify" xml:space="preserve">
    <value>Notify others of this Sales Order</value>
  </data>
  <data name="InvoiceLines_Add_SelectItem" xml:space="preserve">
    <value>Search for and select the service line you would like to use as the source for the new Line and press &lt;b&gt;Continue&lt;/b&gt;</value>
  </data>
  <data name="InvoiceLines_Add_EnterDetail" xml:space="preserve">
    <value>Confirm the details of the new line and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="DocHeaderImage_Edit" xml:space="preserve">
    <value>Edit the details of the Document Header Image and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="GIMainInfo_Notify" xml:space="preserve">
    <value>Notify others about this Goods In Note</value>
  </data>
  <data name="InvoiceLines_Delete" xml:space="preserve">
    <value>Are you sure you would like to delete this Invoice Line?</value>
  </data>
  <data name="CurrencyRateHistory_Delete" xml:space="preserve">
    <value>Are you sure you would like to delete this Currency Rate?</value>
  </data>
  <data name="CurrencyRateHistory_Edit" xml:space="preserve">
    <value>Amend the details of the Currency Rate and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="CusReqMainInfo_Close" xml:space="preserve">
    <value>Are you sure you would like to close this Customer Requirement?</value>
  </data>
  <data name="SOLines_PostAll" xml:space="preserve">
    <value>Are you sure you would like to post all unposted lines?</value>
  </data>
  <data name="SOLines_UnpostAll" xml:space="preserve">
    <value>Are you sure you would like to unpost all posted lines?</value>
  </data>
  <data name="ConfirmPost" xml:space="preserve">
    <value>Are you sure you would like to post this line?</value>
  </data>
  <data name="ConfirmPostAll" xml:space="preserve">
    <value>Are you sure you would like to post all the unposted lines?</value>
  </data>
  <data name="ConfirmUnpost" xml:space="preserve">
    <value>Are you sure you would like to unpost this line?</value>
  </data>
  <data name="ConfirmUnpostAll" xml:space="preserve">
    <value>Are you sure you would like to unpost all the posted and unallocated lines?</value>
  </data>
  <data name="InvoiceLines_Add_SelectSource" xml:space="preserve">
    <value>Select the source for the new Line</value>
  </data>
  <data name="CountingMethod_Add" xml:space="preserve">
    <value>Enter the changed details for the Counting Method and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="CountingMethod_Edit" xml:space="preserve">
    <value>Enter all the details of the new Counting Method and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="POMainInfo_Close" xml:space="preserve">
    <value>Are you sure you would like to close this Purchase Order?</value>
  </data>
  <data name="SOMainInfo_Close" xml:space="preserve">
    <value>Are you sure you would like to close this Sales Order?</value>
  </data>
  <data name="POLines_Close" xml:space="preserve">
    <value>Are you sure you would like to close this Purchase Order Line?</value>
  </data>
  <data name="SOLines_Close" xml:space="preserve">
    <value>Are you sure you would like to close this Sales Order Line?</value>
  </data>
  <data name="CRMALines_Deallocate" xml:space="preserve">
    <value>Are you sure you want to remove the selected Allocation(s)?</value>
  </data>
  <data name="ServiceAllocations_Deallocate" xml:space="preserve">
    <value>Are you sure you would like to remove this allocation?</value>
  </data>
  <data name="Incoterm_Add" xml:space="preserve">
    <value>Enter the changed details for the Incoterm and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="Incoterm_Edit" xml:space="preserve">
    <value>Enter all the details of the new Incoterm and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="SecurityGroups_Clone" xml:space="preserve">
    <value>Are you sure you would like to make a copy of this Security Group and its permissions?</value>
  </data>
  <data name="AppSettings_Edit" xml:space="preserve">
    <value>Settings at the Company Level will override any Global settings, otherwise the defaults will be used. Edit the details and press &lt;b&gt;Save&lt;/b&gt;. Clearing a field will use the default value.</value>
  </data>
  <data name="SecurityUsers_Transfer" xml:space="preserve">
    <value>Select the user you would like to transfer sales accounts to and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="GlobalCountryList_Add" xml:space="preserve">
    <value>Enter the details of the new Master Country and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="POMainInfo_Notify" xml:space="preserve">
    <value>Notify others of this Purchase Order</value>
  </data>
  <data name="Warehouse_ClearDefault" xml:space="preserve">
    <value>Are you sure you would like to clear the default Warehouse?</value>
  </data>
  <data name="Warehouse_MakeDefault" xml:space="preserve">
    <value>Are you sure you would like to set this Warehouse as default for new POs and CRMAs?</value>
  </data>
  <data name="TaxRateHistory_Delete" xml:space="preserve">
    <value>Are you sure you would like to delete this future tax rate?</value>
  </data>
  <data name="CRMALines_Close" xml:space="preserve">
    <value>Are you sure you would like to close this Customer RMA Line?</value>
  </data>
  <data name="GILines_PrintLabel" xml:space="preserve">
    <value>Print Goods In Label</value>
  </data>
  <data name="PDF_Add" xml:space="preserve">
    <value>Select a new PDF (maximum size {0}mb) and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="PDF_Delete" xml:space="preserve">
    <value>Are you sure you would like to delete this PDF?</value>
  </data>
  <data name="EmailComposer_Edit" xml:space="preserve">
    <value>Enter message for invoice bulk email</value>
  </data>
  <data name="Invoice_Email" xml:space="preserve">
    <value>Are you sure you would like to send invoice email?</value>
  </data>
  <data name="Incoterm_Disable" xml:space="preserve">
    <value>Are you sure you would like to disable this Incoterm?</value>
  </data>
  <data name="Incoterm_Enable" xml:space="preserve">
    <value>Are you sure you would like to enable this Incoterm?</value>
  </data>
  <data name="ContactsForCompany_MakeDefaultPOLedger" xml:space="preserve">
    <value>Set as Company's Default Contact for Purchase Orders Ledger</value>
  </data>
  <data name="ContactsForCompany_MakeDefaultSOLedger" xml:space="preserve">
    <value>Set as Company's Default Contact for Sales Orders Ledger</value>
  </data>
  <data name="InvoiceLines_EditBankFee" xml:space="preserve">
    <value>Edit the Bank Charge Fee and press Save</value>
  </data>
  <data name="Sourcing_AddStockInfo" xml:space="preserve">
    <value>Enter all the details of the new sourcing info and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="Sourcing_EditStockInfo" xml:space="preserve">
    <value>Edit the details of the sourcing info and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="Sourcing_AddOffer" xml:space="preserve">
    <value>Enter all the details of the offer and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="Sourcing_AddTrusted" xml:space="preserve">
    <value>Enter all the details of the trusted and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="TabSecurity_Edit" xml:space="preserve">
    <value>Edit the permissions and press Save</value>
  </data>
  <data name="LocalCurrency_Add" xml:space="preserve">
    <value>Enter the details of the local currency and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="LocalCurrency_Edit" xml:space="preserve">
    <value>Enter all the details of  local currency and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="GILines_NPRPrinted" xml:space="preserve">
    <value>Edit the details and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="SupplierInvoiceAdd_EnterDetail" xml:space="preserve">
    <value>Enter the details of the new Supplier Invoice and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="SupplierInvoiceAdd_Notify" xml:space="preserve">
    <value>Notify others of the new Supplier Invoice</value>
  </data>
  <data name="SupplierInvoiceAdd_SelectCompany" xml:space="preserve">
    <value>Select the Supplier Company</value>
  </data>
  <data name="SupplierInvoiceMainInfo_Edit" xml:space="preserve">
    <value>Edit the details and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="SupplierInvoiceLines_Add" xml:space="preserve">
    <value>Edit the details and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="SupplierInvoiceLines_Delete" xml:space="preserve">
    <value>Are you sure you would like to delete the selected Supplier Invoice Line?</value>
  </data>
  <data name="SupplierInvoiceMainInfo_Notify" xml:space="preserve">
    <value>Notify others about this Supplier Invoice</value>
  </data>
  <data name="NPRNotify_Notify" xml:space="preserve">
    <value>Notify others about this NPR</value>
  </data>
  <data name="Printer_Add" xml:space="preserve">
    <value>Enter all the details of the new Printer and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="Printer_Edit" xml:space="preserve">
    <value>Enter the changed details for the Printer and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="StockMainInfo_StockProvision" xml:space="preserve">
    <value>Edit the details of the stock provision and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="GILines_PhysicalInspect" xml:space="preserve">
    <value>Confirm the inspection details and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="CertificateCategory_Add" xml:space="preserve">
    <value>Enter the details of the new certificate category and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="CertificateCategory_Edit" xml:space="preserve">
    <value>Edit the details and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="Certificate_Add" xml:space="preserve">
    <value>Enter all the details of the new Certificate and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="Certificate_Edit" xml:space="preserve">
    <value>Edit the details and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="CompanyCertificate_Add" xml:space="preserve">
    <value>Enter all the details of the new Company Certificate and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="CompanyCertificate_Edit" xml:space="preserve">
    <value>Edit the details and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="EightDCode_Add" xml:space="preserve">
    <value>Enter the details of the new Root Cause category and press Save</value>
  </data>
  <data name="EightDCode_Edit" xml:space="preserve">
    <value>Enter All of the details and press &lt;b&gt; Save &lt;/b&gt;</value>
  </data>
  <data name="EightDSubCategory_Add" xml:space="preserve">
    <value>Enter all the details of the new Root Cause sub category and press &lt;b&gt; Save&lt;/b&gt;</value>
  </data>
  <data name="EigthDSubCategory_Edit" xml:space="preserve">
    <value>Edit the details and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="LotItems_StockProvision" xml:space="preserve">
    <value>Edit the details of the stock provision and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="LotItems_LotStockProvision" xml:space="preserve">
    <value>Edit the details of the stock provision and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="LotItems_LotSave" xml:space="preserve">
    <value>Are you sure you would like to save stock provision</value>
  </data>
  <data name="LabelFullPath_Add" xml:space="preserve">
    <value>Edit the details and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="LabelFullPath_Edit" xml:space="preserve">
    <value>Edit the details and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="InvoiceMainInfo_EditShippingInfo" xml:space="preserve">
    <value>Edit the details and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="EPRNotify_Notify" xml:space="preserve">
    <value>Notify others about this EPR</value>
  </data>
  <data name="EXCEL_Add" xml:space="preserve">
    <value>Select a new Excel (maximum size {0}mb) and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="EXCEL_Delete" xml:space="preserve">
    <value>Are you sure you would like to delete this Excel?</value>
  </data>
  <data name="POMainInfo_AddExpedite" xml:space="preserve">
    <value>Please add Expedite Note</value>
  </data>
  <data name="UserProfile_SSRS" xml:space="preserve">
    <value>GT SSRS Report Access</value>
  </data>
  <data name="PDFDragDrop_Add" xml:space="preserve">
    <value>Enter the details and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="EXCELDragDrop_Add" xml:space="preserve">
    <value>Enter the details and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="StockImagesDragDrop_Add" xml:space="preserve">
    <value>Enter the details and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="BOMAdd_Add" xml:space="preserve">
    <value>Enter all the details of the new HUBRFQ and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="BOMMainInfo_Delete" xml:space="preserve">
    <value>Are you sure you would like to delete this HUBRFQ?</value>
  </data>
  <data name="BOMMainInfo_Edit" xml:space="preserve">
    <value>Edit the details and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="SOMainInfo_Warehouse" xml:space="preserve">
    <value>Warehouse</value>
  </data>
  <data name="BOMMainInfo_Notify" xml:space="preserve">
    <value>Notify others of this HUBRFQ</value>
  </data>
  <data name="POQuoteMainInfo_Notify" xml:space="preserve">
    <value>Notify others of this Price Request</value>
  </data>
  <data name="Confirm" xml:space="preserve">
    <value>Are you sure you would like to send this to Purchase Hub?</value>
  </data>
  <data name="BOMItem_Release" xml:space="preserve">
    <value>Are you sure you would like to release this HUBRFQ?</value>
  </data>
  <data name="POQuoteMainInfo_Edit" xml:space="preserve">
    <value>Edit the details and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="POQuoteLines_Close" xml:space="preserve">
    <value>Are you sure you would like to delete the selected Price Request ?</value>
  </data>
  <data name="CSV_Add" xml:space="preserve">
    <value>CSV File</value>
  </data>
  <data name="Sourcing_AddToReq_IPOBOMConfirm" xml:space="preserve">
    <value>Are you sure you would like to add this/these items as sourcing result to the HUBRFQ?</value>
  </data>
  <data name="BomItems_Delete" xml:space="preserve">
    <value>Are you sure you would like to delete the selected  item?</value>
  </data>
  <data name="BOMItem_PartialRelease" xml:space="preserve">
    <value>Are you sure you would like to partially release this HUBRFQ?</value>
  </data>
  <data name="String1" xml:space="preserve">
    <value />
  </data>
  <data name="ClientInvoiceMainInfo_Edit" xml:space="preserve">
    <value>Edit the details and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="ClientInvoiceLines_Add" xml:space="preserve">
    <value>Edit the details and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="InvoiceSetting_Add" xml:space="preserve">
    <value>Invoice Setting</value>
  </data>
  <data name="InvoiceSetting_Edit" xml:space="preserve">
    <value>Schedule invoice</value>
  </data>
  <data name="BomItems_UnRelease" xml:space="preserve">
    <value>Are you sure you would like to Revoke the selected  item?</value>
  </data>
  <data name="BOM_AssignToMe" xml:space="preserve">
    <value>Are you sure you would like to Assign these HUBRFQ?</value>
  </data>
  <data name="BOMCusReqSourcingResults_Delete" xml:space="preserve">
    <value>Are you sure you would like to delete selected item?</value>
  </data>
  <data name="InternalPOLines_Edit" xml:space="preserve">
    <value>Edit Internal Purchase Order Line</value>
  </data>
  <data name="Sourcing_EditTrust" xml:space="preserve">
    <value>Edit the details of the trust and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="ConfirmClose" xml:space="preserve">
    <value>Are you sure you would like to close this HUBRFQ?</value>
  </data>
  <data name="InternalPOMainInfo_Edit" xml:space="preserve">
    <value>Edit the details and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="ClientInvoiceLines_Delete" xml:space="preserve">
    <value>Are you sure you would like to delete the selected Client Invoice Line?</value>
  </data>
  <data name="BOMItem_NoBid" xml:space="preserve">
    <value>Are you sure you would like to No-Bid this HUBRFQ?</value>
  </data>
  <data name="BOMItem_PartialNoBid" xml:space="preserve">
    <value>Are you sure you would like to No-Bid this HUBRFQ Line?</value>
  </data>
  <data name="GILines_PrintLabelCRX" xml:space="preserve">
    <value>Print Goods In Label CRX B2B</value>
  </data>
  <data name="GILines_PrintLabelRejected" xml:space="preserve">
    <value>Print Goods In Label Rejected</value>
  </data>
  <data name="GILines_PrintLabelStock" xml:space="preserve">
    <value>Print Goods In Label Stock</value>
  </data>
  <data name="StockMainInfo_PrintLabel" xml:space="preserve">
    <value>Print Stock Label</value>
  </data>
  <data name="StockMainInfo_PrintLabelCRX" xml:space="preserve">
    <value>Print Stock Label CRX B2B</value>
  </data>
  <data name="StockMainInfo_PrintLabelRejected" xml:space="preserve">
    <value>Print Stock Label Rejected</value>
  </data>
  <data name="StockMainInfo_PrintLabelStock" xml:space="preserve">
    <value>Print Label Stock</value>
  </data>
  <data name="CusReqAdd_EnterDetail1" xml:space="preserve">
    <value>Enter the detail of the new Requirement Step 1</value>
  </data>
  <data name="CusReqAdd_EnterDetail2" xml:space="preserve">
    <value>Enter the detail of the new Requirement Step 2</value>
  </data>
  <data name="SoMainInfo_Confirm" xml:space="preserve">
    <value>Would you like to consolidate similar sales order lines ? &lt;br/&gt; (Consolidation is based on same Part, Customer Part, MFR, Product, Package, DC, RoHS, Product Source, Date Promised &amp; Unit Price)</value>
  </data>
  <data name="SOMainInfo_Consolidate" xml:space="preserve">
    <value>Are you sure you would like to reset print option?</value>
  </data>
  <data name="BOMItems_AddExpedite" xml:space="preserve">
    <value>Add Communication Note</value>
  </data>
  <data name="GlobalProduct_Add" xml:space="preserve">
    <value>Enter all the details of the new Global Product and press Save</value>
  </data>
  <data name="GlobalProduct_Edit" xml:space="preserve">
    <value>Enter the changed details for the Global Product and press Save</value>
  </data>
  <data name="GlobalSecurityGroupMembers_EditMembers" xml:space="preserve">
    <value>Select the Members for this Security Group and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="GlobalSeGroupPermissionsGeneral_Edit" xml:space="preserve">
    <value>Edit the permissions and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="GlobalProductDutyRateHistory_Edit" xml:space="preserve">
    <value>Enter the changed details for the Client Product and press Save</value>
  </data>
  <data name="StockMainInfo_PrintExcessLabelStock" xml:space="preserve">
    <value>Print Excess Label</value>
  </data>
  <data name="GlobalProductName_Add" xml:space="preserve">
    <value>Enter all the details of the new Global Product Name and press Save</value>
  </data>
  <data name="GlobalProductName_Edit" xml:space="preserve">
    <value>Edit the changed details of the new Global Product Name and press Save</value>
  </data>
  <data name="AddEditSerialNo" xml:space="preserve">
    <value>Add/Edit Serial No</value>
  </data>
  <data name="SOMainInfo_SentOrder" xml:space="preserve">
    <value>Please confirm sales order acknowledgement has been sent to the customer</value>
  </data>
  <data name="SoSHippingLines_Confirm" xml:space="preserve">
    <value>Would you also like to de-allocate selected serial Nos</value>
  </data>
  <data name="GTUpdate_Add" xml:space="preserve">
    <value>Add GT Application Update Information</value>
  </data>
  <data name="GTUpdate_Edit" xml:space="preserve">
    <value>Enter the changed details for the GT Notifications and press Save</value>
  </data>
  <data name="CRMAMainInfo_AddExpedite" xml:space="preserve">
    <value>Add Internal Log</value>
  </data>
  <data name="InternalPOMainInfo_AddExpedite" xml:space="preserve">
    <value>Please add Expedite Note</value>
  </data>
  <data name="SOLines_Confirm" xml:space="preserve">
    <value>Are you sure you would like to confirm?</value>
  </data>
  <data name="SOMainInfo_PayByCreditCard" xml:space="preserve">
    <value>Pay By Credit Card</value>
  </data>
  <data name="UploadImagesDragDrop_Add" xml:space="preserve">
    <value>Upload images</value>
  </data>
  <data name="CustReqAllInfo" xml:space="preserve">
    <value>Customer requirement all information</value>
  </data>
  <data name="LabelSetupItem_Add" xml:space="preserve">
    <value>Enter all the details of the new Master Status item and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="LabelSetupItem_Edit" xml:space="preserve">
    <value>Edit the details of the Master Status item and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="ClientBOMAdd_Add" xml:space="preserve">
    <value>Enter all the details of the new BOM and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="MarkasCompleteConfirm" xml:space="preserve">
    <value>Are you sure you would like to mark client bom as complete?</value>
  </data>
  <data name="SaveAsHUBRFQ" xml:space="preserve">
    <value>Once converted to HUBRFQ you cannot make any changes to current Bom&lt;br/&gt;
Are you sure you would like to save this as HUBRFQ?</value>
  </data>
  <data name="CRMAReceiveLines_Confirm" xml:space="preserve">
    <value>Would you also like to de-allocate selected serial Nos</value>
  </data>
  <data name="Sourcing_StockImportTool" xml:space="preserve">
    <value />
  </data>
  <data name="GILines_SplitGI" xml:space="preserve">
    <value>Edit the details and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="Client_Add" xml:space="preserve">
    <value>Enter the changed details for the client and press Save</value>
  </data>
  <data name="Sourcing_EpoStockImportTool" xml:space="preserve">
    <value>Office Import EPO Tool</value>
  </data>
  <data name="Sourcing_StockImportEpoTool" xml:space="preserve">
    <value>Upload Offers Import HUB Tool</value>
  </data>
  <data name="Sourcing_EpoTool" xml:space="preserve">
    <value>Upload Stock Epo FIle</value>
  </data>
  <data name="Sourcing_EditEpo" xml:space="preserve">
    <value>Edit the details of the Strategic Offers and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="Sourcing_EditAltPart" xml:space="preserve">
    <value>Edit Alternative Part and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="AppSettingsEdit_InvAutoExport" xml:space="preserve">
    <value>Activate auto invoice export</value>
  </data>
  <data name="CusReqMainInfo_Delete" xml:space="preserve">
    <value>Are you sure you would like to delete these Alternate Part(s)?</value>
  </data>
  <data name="MasterLogin_Add" xml:space="preserve">
    <value>Enter the changed details for the master login and press Save</value>
  </data>
  <data name="editinvoiceinfoafterrelease" xml:space="preserve">
    <value>EDIT INVOICE INFORMATION AFTER RELEASE</value>
  </data>
  <data name="UtilityOffer_Import" xml:space="preserve">
    <value />
  </data>
  <data name="UtilityBOM_Import" xml:space="preserve">
    <value>Utility BOM Import</value>
    <comment>Utility BOM Import</comment>
  </data>
  <data name="CloneHUBRFQ" xml:space="preserve">
    <value>Clone requirement and add HUBRFQ</value>
  </data>
  <data name="CloneHUB" xml:space="preserve">
    <value>Clone requirement and send to HUB</value>
  </data>
  <data name="GILines_AddShortShipment" xml:space="preserve">
    <value>Add the details and press Save</value>
  </data>
  <data name="ShortShipmentNotify_Notify" xml:space="preserve">
    <value>Add the details and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="DeletePartWatch" xml:space="preserve">
    <value>Are you sure you would like to delete selected item?</value>
  </data>
  <data name="ConfirmSAApprove" xml:space="preserve">
    <value>Are you sure you would like to approve supplier?</value>
  </data>
  <data name="ConfirmSADecline" xml:space="preserve">
    <value>Are you sure you would like to decline supplier?</value>
  </data>
  <data name="Supplier_Approval_Edit" xml:space="preserve">
    <value>Amend the details and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="GILines_Notify" xml:space="preserve">
    <value>GILines Notify</value>
  </data>
  <data name="GILineNotify_Notify" xml:space="preserve">
    <value>Edit the details and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="GILines_ImageUpload" xml:space="preserve">
    <value>Upload images and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="GILines_Quarantine" xml:space="preserve">
    <value>Are you sure you would like to quarantine this GI line? Once you quarantine, the system will deallocate the associated Sales Order lines, close all open queries related to this GI line, and set this GI line status as Released.</value>
  </data>
  <data name="GILInes_UploadPDF" xml:space="preserve">
    <value>Select a new PDF and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="AddEditLotNo" xml:space="preserve">
    <value>Add/Edit Lot Code</value>
  </data>
  <data name="GILines_Export" xml:space="preserve">
    <value>Select images and press &lt;b&gt;export&lt;/b&gt; </value>
  </data>
  <data name="RestrictedManufacture_Add" xml:space="preserve">
    <value>Enter all the details of the new Restricted Manufacturer and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="RestrictedManufacture_Edit" xml:space="preserve">
    <value>Enter the changed details for the Restricted Manufacturer and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="SSMainInfo_Cancel" xml:space="preserve">
    <value>Are you sure you would like to cancel the Short Shipment?</value>
  </data>
  <data name="SSMainInfo_Close" xml:space="preserve">
    <value>Are you sure you would like to close the Short Shipment?</value>
  </data>
  <data name="WarningMessage_Edit" xml:space="preserve">
    <value>Edit the text of the Warning Message and press Save</value>
  </data>
  <data name="WarningMessage_Add" xml:space="preserve">
    <value>Enter all the details of the Warning Message and press Save</value>
  </data>
  <data name="EXCEL_DOC_Delete" xml:space="preserve">
    <value>Are you sure you would like to delete this Excel or Doc?</value>
  </data>
  <data name="CusReqSourcingResults_Delete" xml:space="preserve">
    <value>Are you sure you would like to delete selected item?</value>
  </data>
  <data name="ECCN_Add" xml:space="preserve">
    <value>Enter all the details of the new ECCN and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="ECCN_Edit" xml:space="preserve">
    <value>Enter the changed details for the ECCN and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="SSMainInfo_Confirm" xml:space="preserve">
    <value>Are you sure you would like to update the Short Shipment?</value>
  </data>
  <data name="SATrade_EnterDetail" xml:space="preserve">
    <value>Amend the details and press &lt;b&gt;Save and Send&lt;/b&gt;</value>
  </data>
  <data name="SATrade_Notify" xml:space="preserve">
    <value>Notify for the supplier Approval</value>
  </data>
  <data name="ConfirmSAEscalate" xml:space="preserve">
    <value>Are you sure you would like to escalate supplier approval?</value>
  </data>
  <data name="UtilityStock_Import" xml:space="preserve">
    <value>Utility Stock Import</value>
  </data>
  <data name="CompanyProspects_Edit" xml:space="preserve">
    <value />
  </data>
  <data name="ToDoListType_Add" xml:space="preserve">
    <value>Enter the changed details for the Package and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="GT" xml:space="preserve">
    <value>GT</value>
  </data>
  <data name="SORequestApproval" xml:space="preserve">
    <value>Request Approval to this Sales Order</value>
  </data>
  <data name="UtilityLog" xml:space="preserve">
    <value>Utility Log</value>
  </data>
  <data name="GlobalProductMainCategory_Map" xml:space="preserve">
    <value>Map product to category</value>
  </data>
  <data name="GlobalCategoryName_Edit" xml:space="preserve">
    <value>Edit the changed details of the new Global Product Main Category and press Save</value>
  </data>
  <data name="GlobalProductMainCategory_Add" xml:space="preserve">
    <value>Enter all the details of the new Global Product Category Name and press Save</value>
  </data>
  <data name="EnhanceInspection_Add" xml:space="preserve">
    <value>Enter the details of the new Test and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="EnhanceInspectionTest_Delete" xml:space="preserve">
    <value>Delete Enhanced Inspection Test</value>
  </data>
  <data name="SalesBOM_Import" xml:space="preserve">
    <value>Sales BOM Import</value>
    <comment>Sales BOM Import</comment>
  </data>
  <data name="QuoteLine_Add_SearchLotDetails" xml:space="preserve">
    <value>Search  the Lot details for the new Quote Line and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="QuoteLines_Delete" xml:space="preserve">
    <value>Are you sure you would like to delete this Quote Line?</value>
  </data>
  <data name="UtilityBOMManager_Import" xml:space="preserve">
    <value>BOM Manager Import</value>
  </data>
  <data name="BOMItem_ApplyPartwatch" xml:space="preserve">
    <value>Are you sure you would like to Apply Partwatch?</value>
  </data>
  <data name="BOMItem_ApplyRemovePartwatch" xml:space="preserve">
    <value>Are you sure you would like to Remove Partwatch?</value>
  </data>
  <data name="OGELApproveExplntn" xml:space="preserve">
    <value>Are you sure would like to authorise this export?</value>
  </data>
  <data name="SendExportApprovalRequest" xml:space="preserve">
    <value>Request Approval to this Sales Order Line</value>
  </data>
  <data name="SOeLine_Add_SearchLotDetails" xml:space="preserve">
    <value>Search  the Lot details for the new SO Line and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="Sourcing_EditReverseLogistic" xml:space="preserve">
    <value>Edit the details of the Reverse Logistic and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="ExportApproval_Edit" xml:space="preserve">
    <value>Amend the details and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="ECCN_EditMap" xml:space="preserve">
    <value>Edit the ECCN Cdoe  of the Warning Message and press Save</value>
  </data>
  <data name="Sourcing_EditReverseLogistics" xml:space="preserve">
    <value>Edit the details of the Reverse Logistic Offers and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="GILines_CloseInspection" xml:space="preserve">
    <value>Are you sure you would like to complete inspection of this GI Line?</value>
  </data>
  <data name="GILines_StartInspection" xml:space="preserve">
    <value>Are you sure you would like to start inspection of this GI Line?</value>
  </data>
  <data name="SOURCING_SEARCHOFFER" xml:space="preserve">
    <value>Enter all the required Sourcing fields and click SEARCH</value>
  </data>
  <data name="CompanyApiCustomer_Add" xml:space="preserve">
    <value>Enter details of customer/supplier</value>
  </data>
  <data name="CompanyApiCustomer_Edit" xml:space="preserve">
    <value>Edit the details of customer/supplier</value>
  </data>
  <data name="UtilityPriceQuote_Import" xml:space="preserve">
    <value>Utility Price Quote Import</value>
  </data>
  <data name="AS6_6081_RCS_Add" xml:space="preserve">
    <value>Enter the changed details for the reason for chosen supplier and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="AS6_6081_RCS_Edit" xml:space="preserve">
    <value>Enter the changed details for the reason for chosen supplier and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="AS6_6081_ROS_Add" xml:space="preserve">
    <value>Enter all the details of the new reason for chosen supplier and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="AS6_6081_ROS_Edit" xml:space="preserve">
    <value>Enter the changed details for the risk of supplier and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="AS6_6081_TOS_Add" xml:space="preserve">
    <value>Enter all the details of the new type of supplier and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="AS6_6081_TOS_Edit" xml:space="preserve">
    <value>Enter the changed details for the type of supplier and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="AS6081_RCS_Delete" xml:space="preserve">
    <value>Are you sure you would like to delete this reason for chosen supplier?</value>
  </data>
  <data name="AS6081_ROS_Delete" xml:space="preserve">
    <value>Are you sure you would like to delete this risk of supplier?</value>
  </data>
  <data name="AS6081_TOS_Delete" xml:space="preserve">
    <value>Are you sure you would like to delete this type of suppliers?</value>
  </data>
  <data name="Sourcing_EditStock" xml:space="preserve">
    <value>Edit the details of the Stock info and press Save</value>
  </data>
  <data name="PDFDocumentFileSize_Add" xml:space="preserve">
    <value>Enter the changed details for the Document File Size and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="PDFDocumentFileSize_Edit" xml:space="preserve">
    <value>Enter all the details of the new Document File Size and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="EntertainmentType_Add" xml:space="preserve">
    <value>Enter the changed details for the Entertainment Type and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="EntertainmentType_Edit" xml:space="preserve">
    <value>Enter the changed details for the Entertainment Type and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="GSA_EditMembers" xml:space="preserve">
    <value>Select the Members for this GSA Group and press Save</value>
  </data>
  <data name="ClientInvoiceHeader_Add" xml:space="preserve">
    <value>Enter all the details of the new Division and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="ClientInvoiceHeader_Edit" xml:space="preserve">
    <value>Edit the text of the Document Footer and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="ClientInvoiceHeaderImage_Add" xml:space="preserve">
    <value>Select a new image for the Client Invoice Headers, 710 pixels wide and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="ClientInvoiceHeaderImage_Delete" xml:space="preserve">
    <value>Are you sure you would like to delete the Client Invoice Header Image?</value>
  </data>
  <data name="ClientInvoiceHeaderImage_Edit" xml:space="preserve">
    <value>Edit the details of the Client Invoice Header Image and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="CompanyInsuranceCertificate_Add" xml:space="preserve">
    <value>Enter all the details of the Company Insurance Certificate and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="CompanyInsuranceCertificate_Edit" xml:space="preserve">
    <value>Enter the Edit details of the Company Insurance Certificate and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="lblExplainReadyToShip" xml:space="preserve">
    <value>This functionality allows a stopped company's sales order to ship, Are you sure?</value>
  </data>
  <data name="CompanyFinanceInfo_Link" xml:space="preserve">
    <value>Select the Accounts with matching VAT Number which you wish to link</value>
  </data>
  <data name="Country_ManageHeader" xml:space="preserve">
    <value>Enter the details for the &lt;span style="
color: red;
 background-color: yellow;
 font-weight: bold;
"&gt;Header&lt;/span&gt; and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="Country_DeleteHeader" xml:space="preserve">
    <value>Are you sure you would like to remove the country Header Image?</value>
  </data>
  <data name="PPVBOM_Add" xml:space="preserve">
    <value>Enter all the details of the new PPV/ BOM Qualification and press Save</value>
  </data>
  <data name="PPVBOM_Edit" xml:space="preserve">
    <value>Enter the changed details for the PPV/ BOM Qualification and press Save</value>
  </data>
  <data name="PVVBOM_Delete" xml:space="preserve">
    <value>Are you sure you would like to delete this PPV/ BOM Qualification Question &amp; Answer?</value>
  </data>
  <data name="Credit_Email" xml:space="preserve">
    <value>Are you sure you would like to send credit email?</value>
  </data>
  <data name="Debit_Email" xml:space="preserve">
    <value>Are you sure you would like to send debit email?</value>
  </data>
  <data name="OGELLicenses_Add" xml:space="preserve">
    <value>Enter all the details of the new OGEL License and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="OGELLicenses_Edit" xml:space="preserve">
    <value>Enter the changed details for the OGEL License and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="StarRating_Add" xml:space="preserve">
    <value>Enter all the details of the new Star Rating and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="ManufacturerSuppliers_View" xml:space="preserve">
    <value>View the details below</value>
  </data>
  <data name="GlobalTax_Edit" xml:space="preserve">
    <value>Enter the changed details for the Master Tax and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="GlobalTax_EditRates" xml:space="preserve">
    <value>Amend all the Master Tax Rates and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="IHSAdd_Add" xml:space="preserve">
    <value>Enter the details of the new Part and press &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
</root>