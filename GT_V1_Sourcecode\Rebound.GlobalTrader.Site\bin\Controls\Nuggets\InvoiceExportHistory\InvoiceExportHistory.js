Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Nuggets");Rebound.GlobalTrader.Site.Controls.Nuggets.InvoiceExportHistory=function(n){Rebound.GlobalTrader.Site.Controls.Nuggets.InvoiceExportHistory.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.Nuggets.InvoiceExportHistory.prototype={get_sectionID:function(){return this._sectionID},set_sectionID:function(n){this._sectionID!==n&&(this._sectionID=n)},get_pnlPDFDocuments:function(){return this._pnlPDFDocuments},set_pnlPDFDocuments:function(n){this._pnlPDFDocuments!==n&&(this._pnlPDFDocuments=n)},get_IsPDFAvailable:function(){return this._IsPDFAvailable},set_IsPDFAvailable:function(n){this._IsPDFAvailable!==n&&(this._IsPDFAvailable=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Nuggets.InvoiceExportHistory.callBaseMethod(this,"initialize");this.addRefreshEvent(Function.createDelegate(this,this.showPDFPanel));this.getData()},dispose:function(){this.isDisposed||(this._sectionID=null,this._pnlPDFDocuments=null,this._IsPDFAvailable=null,Rebound.GlobalTrader.Site.Controls.Nuggets.InvoiceExportHistory.callBaseMethod(this,"dispose"))},getData:function(){if(!this._IsPDFAvailable){this.pdfNotAvailable(!0);return}this.getData_Start();this._intCountPDF==0;var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/InvoiceExportHistory");n.set_DataObject("InvoiceExportHistory");n.set_DataAction("GetData");n.addParameter("id",this._sectionID);n.addDataOK(Function.createDelegate(this,this.getDataOK));n.addError(Function.createDelegate(this,this.getDataError));n.addTimeout(Function.createDelegate(this,this.getDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getDataOK:function(n){var r=n._result,f,i,u,t;if($R_FN.setInnerHTML(this._pnlPDFDocuments,""),f=r.IconPath,i="",r.Items){for(u=0;u<r.Items.length;u++)t=r.Items[u],i+='<div class="pdfDocument">',i+=String.format('<a href="{0}" target="_blank"><img width="80px" id="{1}_img{2}" src="{3}" border="0" /><\/a><p class="pdfInvoiceExportDocuments">{4}<\/p>',$R_FN.setCleanTextValue(t.FilePath),this._element.id,u,t.FileType==="pdf"?f+"pdficon.jpg":f+"xmlicon.jpg",t.ActionType),i+='<div class="pdfDocumentCaption">',t.PrefixAction&&(i+="["+t.PrefixAction+"] "+t.ActionType+"."+t.FileType+"<br />"),i+=t.Date,t.By&&(i+="<br />"+t.By),i+="<\/div>",i+="<\/div>",t=null;this._intCountPDF=r.Items.length}$R_FN.setInnerHTML(this._pnlPDFDocuments,i);this.getDataOK_End();this.showNoData(!this._IsPDFAvailable);this.showPanel(this._IsPDFAvailable)},getDataError:function(n){this.showError(!0,n.get_ErrorMessage())},pdfNotAvailable:function(n){n&&($R_FN.setInnerHTML(this._pnlPDFDocuments,""),this.getDataOK_End(),this.showNoData(!0))},showPDFPanel:function(){this._IsPDFAvailable=!0;this.showPanel(!0);this.getData()},showPanel:function(n){$R_FN.showElement(this._pnlPDFDocuments,n)}};Rebound.GlobalTrader.Site.Controls.Nuggets.InvoiceExportHistory.registerClass("Rebound.GlobalTrader.Site.Controls.Nuggets.InvoiceExportHistory",Rebound.GlobalTrader.Site.Controls.Nuggets.Base);