Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");Rebound.GlobalTrader.Site.Controls.DropDowns.Manufacturer=function(n){Rebound.GlobalTrader.Site.Controls.DropDowns.Manufacturer.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.DropDowns.Manufacturer.prototype={initialize:function(){Rebound.GlobalTrader.Site.Controls.DropDowns.Manufacturer.callBaseMethod(this,"initialize");this.addSetupDataCall(Function.createDelegate(this,this.setupDataCall));this.addGetDataOK(Function.createDelegate(this,this.dataCallOK))},dispose:function(){this.isDisposed||Rebound.GlobalTrader.Site.Controls.DropDowns.Manufacturer.callBaseMethod(this,"dispose")},setupDataCall:function(){this._objData.set_PathToData("controls/DropDowns/Manufacturer");this._objData.set_DataObject("Manufacturer");this._objData.set_DataAction("GetData")},dataCallOK:function(){var t=this._objData._result,n;if(t.Manufacturers)for(n=0;n<t.Manufacturers.length;n++)this.addOption(t.Manufacturers[n].Name,t.Manufacturers[n].ID)}};Rebound.GlobalTrader.Site.Controls.DropDowns.Manufacturer.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.Manufacturer",Rebound.GlobalTrader.Site.Controls.DropDowns.Base);