﻿/*
Marker     Changed by      Date          Remarks
[001]      <PERSON><PERSON><PERSON>          13/09/2014   NPR Search
 */
using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;
using Rebound.GlobalTrader.Site.Enumerations;

namespace Rebound.GlobalTrader.Site.Controls.DataListNuggets.Data
{

    public class NPR : Base
    {



        protected override void GetData()
        {



            List<ReportNPR> lst = ReportNPR.DataListNugget(
                SessionManager.ClientID
                , GetFormValue_NullableInt("SortIndex")
                , GetFormValue_NullableInt("SortDir", SortColumnDirection.ASC)
                , GetFormValue_NullableInt("PageIndex", 0)
                , GetFormValue_NullableInt("PageSize", 10)
                , GetFormValue_PartForLikeSearchNpr("Npr")
                //, GetFormValue_StringForNameSearch("CMName")
                , GetFormValue_StringForNameSearchDecode("CMName")
                , GetFormValue_String("ActionBy")
                , GetFormValue_NullableInt("IsCompleted")
                , GetFormValue_NullableInt("PONoNPRLo")
                 , GetFormValue_NullableInt("PONoNPRHi")
                , GetFormValue_NullableInt("GINoNprLo")               
                , GetFormValue_NullableInt("GINoNprHi")
                , GetFormValue_NullableDateTime("NprRaisedDateFrom")
                , GetFormValue_NullableDateTime("NprRaisedDateTo")
                , GetFormValue_NullableInt("IsAuthorised")

            );
            JsonObject jsn = new JsonObject();
            jsn.AddVariable("TotalRecords", (lst.Count > 0) ? lst[0].RowCnt : 0);
            JsonObject jsnRowsArray = new JsonObject(true);
            for (int i = 0; i < lst.Count; i++)
            {
                if (i < lst.Count)
                {
                    JsonObject jsnRow = new JsonObject();
                    jsnRow.AddVariable("ID", lst[i].NprId);
                    jsnRow.AddVariable("Part", lst[i].Part);
                    jsnRow.AddVariable("Location", lst[i].Location);
                    jsnRow.AddVariable("Quantity", lst[i].Quantity);
                    jsnRow.AddVariable("CM", lst[i].CompanyName);                    
                    jsnRow.AddVariable("UPrice", lst[i].UnitCost);
                    jsnRow.AddVariable("AuthBy", lst[i].AuthorisedBy);
                    jsnRow.AddVariable("CompBy", lst[i].CompletedBy);
                    jsnRow.AddVariable("NprDate", Functions.FormatDate(lst[i].NprRaisedDate));
                    jsnRow.AddVariable("NprNo", lst[i].NprNo);
                    jsnRow.AddVariable("CMNo", lst[i].CompanyNo);
                    jsnRow.AddVariable("Actn", lst[i].Action);
                    jsnRow.AddVariable("PONo", lst[i].PurchaseOrderNumber);
                    jsnRow.AddVariable("POID", lst[i].POId);
                    jsnRow.AddVariable("GILIneNo", lst[i].GoodsInLineId);
                    jsnRowsArray.AddVariable(jsnRow);
                    jsnRow.Dispose();
                    jsnRow = null;
                }
            }
            jsn.AddVariable("Results", jsnRowsArray);
            OutputResult(jsn);
            jsnRowsArray.Dispose();
            jsnRowsArray = null;
            jsn.Dispose();
            jsn = null;
            lst = null;
            base.GetData();
        }

        protected override void AddFilterStates()
        {
            AddFilterState("Npr");
            AddFilterState("IsCompleted");
            AddFilterState("IsAuthorised");
            AddFilterState("CMName");
            AddFilterState("Action");
            AddFilterState("GINoNpr");
            AddFilterState("PONoNPR");
            AddFilterState("NprRaisedDateFrom");
            AddFilterState("NprRaisedDateTo");
            base.AddFilterStates();
        }

    }
}
