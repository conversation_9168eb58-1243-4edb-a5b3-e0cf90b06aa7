<%@ Control Language="C#" CodeBehind="ToDoList_Edit.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Forms.ToDoList_Edit" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Form:DesignBase ID="ctlDB" runat="server" ShowRequiredFieldsExplanation="false" AllowQuickHelp="false">
	<Links>
		<ReboundUI:IconButton ID="ibtnSave" runat="server" IconGroup="Nugget" IconTitleResource="Save" IconCSSType="Save" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
		<ReboundUI:IconButton ID="ibtnCancel" runat="server" IconGroup="Nugget" IconTitleResource="Cancel" IconCSSType="Cancel" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
	</Links>
	<Explanation><%=Functions.GetGlobalResource("FormExplanations", "ToDoList_Edit")%></Explanation>
	<Content>
		<ReboundUI_Table:Form id="frm" runat="server">
			<ReboundFormFieldCollection:ToDo id="ctlToDo" runat="server" />
		</ReboundUI_Table:Form>
	</Content>
</ReboundUI_Form:DesignBase>
