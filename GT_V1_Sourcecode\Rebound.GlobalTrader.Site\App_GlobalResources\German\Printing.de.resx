﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Account" xml:space="preserve">
    <value>Konto</value>
  </data>
  <data name="AdviceNote" xml:space="preserve">
    <value>Benachrichtigungsschreiben</value>
  </data>
  <data name="AirwayBill" xml:space="preserve">
    <value>Flugliniebill-Nr.</value>
  </data>
  <data name="Allocation" xml:space="preserve">
    <value>Verteilung</value>
  </data>
  <data name="Amount" xml:space="preserve">
    <value>Menge</value>
  </data>
  <data name="Attention" xml:space="preserve">
    <value>Aufmerksamkeit</value>
  </data>
  <data name="AuthorisedBy" xml:space="preserve">
    <value>Vorbei autorisiert</value>
  </data>
  <data name="Boxes" xml:space="preserve">
    <value>Kästen</value>
  </data>
  <data name="BuyBase" xml:space="preserve">
    <value>Kaufen Sie Unterseite</value>
  </data>
  <data name="BuyCurrency" xml:space="preserve">
    <value>Kaufen Sie Währung</value>
  </data>
  <data name="Buyer" xml:space="preserve">
    <value>Kaufer</value>
  </data>
  <data name="BuyTerms" xml:space="preserve">
    <value>Kaufen Sie Ausdrücke</value>
  </data>
  <data name="CertificateOfConformance" xml:space="preserve">
    <value>Bescheinigung der Übereinstimmung</value>
  </data>
  <data name="Contact" xml:space="preserve">
    <value>Kontakt</value>
  </data>
  <data name="CountingMethod" xml:space="preserve">
    <value>Zählung-Methode</value>
  </data>
  <data name="Country" xml:space="preserve">
    <value>Land</value>
  </data>
  <data name="CountryOfOrigin" xml:space="preserve">
    <value>Ursprungsland</value>
  </data>
  <data name="CreditNote" xml:space="preserve">
    <value>Kreditnote</value>
  </data>
  <data name="CustNo" xml:space="preserve">
    <value>Kunden-Nr.</value>
  </data>
  <data name="Customer" xml:space="preserve">
    <value>Kunde</value>
  </data>
  <data name="CustomerNo" xml:space="preserve">
    <value>Kunden-Nr.</value>
  </data>
  <data name="CustomerPartNo" xml:space="preserve">
    <value>Kunden-Teilenummer</value>
  </data>
  <data name="CustomerPONumber" xml:space="preserve">
    <value>Kunden-PO Nr.</value>
  </data>
  <data name="CustomerRMA" xml:space="preserve">
    <value>Kunde RMA</value>
  </data>
  <data name="Date" xml:space="preserve">
    <value>Datum</value>
  </data>
  <data name="DateCode" xml:space="preserve">
    <value>DC</value>
  </data>
  <data name="DateCodeReceived" xml:space="preserve">
    <value>Datum-Code empfing</value>
  </data>
  <data name="DateCodeRequired" xml:space="preserve">
    <value>Datum-Code erforderte</value>
  </data>
  <data name="DateInspected" xml:space="preserve">
    <value>Datum Kontrollierte</value>
  </data>
  <data name="DateIssued" xml:space="preserve">
    <value>Datum Herausgegeben</value>
  </data>
  <data name="DateOrdered" xml:space="preserve">
    <value>Datum Bestellte</value>
  </data>
  <data name="DatePromised" xml:space="preserve">
    <value>Datum Versprochen</value>
  </data>
  <data name="DateQuoted" xml:space="preserve">
    <value>Datum Preisangabe</value>
  </data>
  <data name="DateRaised" xml:space="preserve">
    <value>Datum Hob an</value>
  </data>
  <data name="DateReceived" xml:space="preserve">
    <value>Datum Empfing</value>
  </data>
  <data name="DateShipped" xml:space="preserve">
    <value>Datum Versendete</value>
  </data>
  <data name="DC" xml:space="preserve">
    <value>DC</value>
  </data>
  <data name="DebitNote" xml:space="preserve">
    <value>Belastungsanzeige</value>
  </data>
  <data name="DeliveryDate" xml:space="preserve">
    <value>Lieferfrist</value>
  </data>
  <data name="Description" xml:space="preserve">
    <value>Beschreibung</value>
  </data>
  <data name="DimensionalWeight" xml:space="preserve">
    <value>Maßgewicht</value>
  </data>
  <data name="DueDate" xml:space="preserve">
    <value>Abgabefrist</value>
  </data>
  <data name="Duty" xml:space="preserve">
    <value>Zoll</value>
  </data>
  <data name="EarliestShipmentDate" xml:space="preserve">
    <value>Frühestes Versand-Datum</value>
  </data>
  <data name="ETA" xml:space="preserve">
    <value>ETA</value>
  </data>
  <data name="Fax" xml:space="preserve">
    <value>Fax</value>
  </data>
  <data name="FreeOnBoard" xml:space="preserve">
    <value>F.O.B.</value>
  </data>
  <data name="Freight" xml:space="preserve">
    <value>Fracht</value>
  </data>
  <data name="From" xml:space="preserve">
    <value>Von</value>
  </data>
  <data name="GoodsInNote" xml:space="preserve">
    <value>Goods In Note</value>
  </data>
  <data name="GoodsReceivingItem" xml:space="preserve">
    <value>Goods Receiving Item</value>
  </data>
  <data name="GP" xml:space="preserve">
    <value>GP</value>
  </data>
  <data name="GPPercent" xml:space="preserve">
    <value>GP %</value>
  </data>
  <data name="InspectedBy" xml:space="preserve">
    <value>Inspected By</value>
  </data>
  <data name="Invoice" xml:space="preserve">
    <value>Rechnung</value>
  </data>
  <data name="InvoiceDate" xml:space="preserve">
    <value>Rechnungs-Datum</value>
  </data>
  <data name="LandedCost" xml:space="preserve">
    <value>Gelandete Kosten</value>
  </data>
  <data name="LineHeader_Amount" xml:space="preserve">
    <value>Menge</value>
    <comment>Max 12 chars</comment>
  </data>
  <data name="LineHeader_Buy" xml:space="preserve">
    <value>Kauf</value>
  </data>
  <data name="LineHeader_BuyCurrency" xml:space="preserve">
    <value>Kaufen Sie Währung</value>
  </data>
  <data name="LineHeader_BuyTerms" xml:space="preserve">
    <value>Kaufen Sie Ausdrücke</value>
  </data>
  <data name="LineHeader_Country" xml:space="preserve">
    <value>Land</value>
  </data>
  <data name="LineHeader_CountryOfOrigin" xml:space="preserve">
    <value>Ursprungsland</value>
  </data>
  <data name="LineHeader_CustomerPartNo" xml:space="preserve">
    <value>Kunden-Teil</value>
    <comment>Max 30 chars</comment>
  </data>
  <data name="LineHeader_Date" xml:space="preserve">
    <value>Datum</value>
  </data>
  <data name="LineHeader_DateCode" xml:space="preserve">
    <value>DC</value>
    <comment>Max 5 chars</comment>
  </data>
  <data name="LineHeader_DateShipped" xml:space="preserve">
    <value>Datum Versendete</value>
    <comment>Max 12 chars</comment>
  </data>
  <data name="LineHeader_DeliveryDate" xml:space="preserve">
    <value>Lieferfrist</value>
    <comment>Max 12 chars</comment>
  </data>
  <data name="LineHeader_DueDate" xml:space="preserve">
    <value>Abgabefrist</value>
    <comment>Max 12 chars</comment>
  </data>
  <data name="LineHeader_Duty" xml:space="preserve">
    <value>Zoll</value>
  </data>
  <data name="LineHeader_ETA" xml:space="preserve">
    <value>ETA</value>
  </data>
  <data name="LineHeader_LandedCost" xml:space="preserve">
    <value>Gelandete Kosten</value>
  </data>
  <data name="LineHeader_Manufacturer" xml:space="preserve">
    <value>Hersteller</value>
    <comment>Max 5 chars</comment>
  </data>
  <data name="LineHeader_Notes" xml:space="preserve">
    <value>Anmerkungen</value>
  </data>
  <data name="LineHeader_Package" xml:space="preserve">
    <value>Satz</value>
    <comment>Max 5 chars</comment>
  </data>
  <data name="LineHeader_PartNo" xml:space="preserve">
    <value>Teilenummer</value>
    <comment>Max 30 chars</comment>
  </data>
  <data name="LineHeader_Photos" xml:space="preserve">
    <value>Fotos</value>
  </data>
  <data name="LineHeader_PO" xml:space="preserve">
    <value>PO</value>
  </data>
  <data name="LineHeader_Price" xml:space="preserve">
    <value>Preis</value>
    <comment>Max 12 chars</comment>
  </data>
  <data name="LineHeader_Product" xml:space="preserve">
    <value>Produkt</value>
  </data>
  <data name="LineHeader_ProductDutyCode" xml:space="preserve">
    <value>Aufgaben-Code</value>
  </data>
  <data name="LineHeader_Quantity" xml:space="preserve">
    <value>Quantität </value>
    <comment>Max 10 chars</comment>
  </data>
  <data name="LineHeader_QuantityCredited" xml:space="preserve">
    <value>Qtt Schrieb</value>
    <comment>Max 10 chars</comment>
  </data>
  <data name="LineHeader_QuantityOrdered" xml:space="preserve">
    <value>Qtt Bstllt</value>
    <comment>Max 10 chars</comment>
  </data>
  <data name="LineHeader_QuantityShipped" xml:space="preserve">
    <value>Qtt Versnd</value>
    <comment>Max 10 chars</comment>
  </data>
  <data name="LineHeader_Reason" xml:space="preserve">
    <value>Grund</value>
    <comment>Max 30 chars</comment>
  </data>
  <data name="LineHeader_Reference" xml:space="preserve">
    <value>Hinweis</value>
  </data>
  <data name="LineHeader_ReturnDate" xml:space="preserve">
    <value>Rückholdatum</value>
    <comment>Max 12 chars</comment>
  </data>
  <data name="LineHeader_ROHS" xml:space="preserve">
    <value>ROHS</value>
  </data>
  <data name="LineHeader_Sell" xml:space="preserve">
    <value>Verkauf</value>
  </data>
  <data name="LineHeader_SellCurrency" xml:space="preserve">
    <value>Verkaufs-Währung</value>
  </data>
  <data name="LineHeader_SellTerms" xml:space="preserve">
    <value>Verkaufs-Ausdrücke</value>
  </data>
  <data name="LineHeader_ShipCost" xml:space="preserve">
    <value>Schiffs-Kosten</value>
  </data>
  <data name="LineHeader_Supplier" xml:space="preserve">
    <value>Lieferant </value>
  </data>
  <data name="LineHeader_SupplierPartNo" xml:space="preserve">
    <value>Lieferanten-Teil </value>
    <comment>Max 30 chars</comment>
  </data>
  <data name="LineHeader_Taxable" xml:space="preserve">
    <value>Steuerpflichtig? </value>
  </data>
  <data name="LineHeader_TotalBuy" xml:space="preserve">
    <value>Gesamtkauf </value>
  </data>
  <data name="LineHeader_TotalPrice" xml:space="preserve">
    <value>Gesamtpreis </value>
    <comment>Max 12 chars</comment>
  </data>
  <data name="LineHeader_TotalSell" xml:space="preserve">
    <value>Gesamtverkauf </value>
  </data>
  <data name="LineHeader_UnitPrice" xml:space="preserve">
    <value>Stückpreis </value>
    <comment>Max 12 chars</comment>
  </data>
  <data name="Location" xml:space="preserve">
    <value>Position</value>
  </data>
  <data name="Manufacturer" xml:space="preserve">
    <value>Hersteller</value>
  </data>
  <data name="ManufacturerReceived" xml:space="preserve">
    <value>Hersteller empfangen</value>
  </data>
  <data name="ManufacturerRequired" xml:space="preserve">
    <value>Hersteller erforderte</value>
  </data>
  <data name="MFG" xml:space="preserve">
    <value>Hersteller</value>
  </data>
  <data name="No" xml:space="preserve">
    <value>Nein</value>
  </data>
  <data name="Package" xml:space="preserve">
    <value>Paket</value>
  </data>
  <data name="PackageReceived" xml:space="preserve">
    <value>Paket empfangen</value>
  </data>
  <data name="PackageRequired" xml:space="preserve">
    <value>Paket erforderte</value>
  </data>
  <data name="PackingSlip" xml:space="preserve">
    <value>Verpackungs-Beleg</value>
  </data>
  <data name="PartMarking" xml:space="preserve">
    <value>Teil-Markierung</value>
  </data>
  <data name="PartNo" xml:space="preserve">
    <value>Teilenummer</value>
  </data>
  <data name="PartNoReceived" xml:space="preserve">
    <value>Teilenummer empfangen</value>
  </data>
  <data name="PartNoRequired" xml:space="preserve">
    <value>Teilenummer erforderte</value>
  </data>
  <data name="Photos" xml:space="preserve">
    <value>Fotos</value>
  </data>
  <data name="PO" xml:space="preserve">
    <value>PO</value>
  </data>
  <data name="POCRMA" xml:space="preserve">
    <value>PO / CRMA</value>
  </data>
  <data name="Price" xml:space="preserve">
    <value>Preis</value>
  </data>
  <data name="Printed" xml:space="preserve">
    <value>Gedruckt</value>
  </data>
  <data name="Product" xml:space="preserve">
    <value>Produkt</value>
  </data>
  <data name="ProFormaInvoice" xml:space="preserve">
    <value>Proformarechnung</value>
  </data>
  <data name="PurchaseOrder" xml:space="preserve">
    <value>Kaufauftrag</value>
  </data>
  <data name="Quantity" xml:space="preserve">
    <value>Quantität</value>
  </data>
  <data name="QuantityCredited" xml:space="preserve">
    <value>Quantität Schrieb gut</value>
  </data>
  <data name="QuantityOrdered" xml:space="preserve">
    <value>Quantität Bestellt</value>
  </data>
  <data name="QuantityReceived" xml:space="preserve">
    <value>Quantität Empfangen</value>
  </data>
  <data name="QuantityRequired" xml:space="preserve">
    <value>Quantität Erforderlich</value>
  </data>
  <data name="QuantityShipped" xml:space="preserve">
    <value>Quantität Versendete</value>
  </data>
  <data name="Quote" xml:space="preserve">
    <value>Preisangabe</value>
  </data>
  <data name="RaisedBy" xml:space="preserve">
    <value>Vorbei angehoben</value>
  </data>
  <data name="Reason" xml:space="preserve">
    <value>Grund</value>
  </data>
  <data name="Received" xml:space="preserve">
    <value>Empfangen</value>
  </data>
  <data name="ReceivedBy" xml:space="preserve">
    <value>Vorbei empfangen</value>
  </data>
  <data name="Reference" xml:space="preserve">
    <value>Hinweis</value>
  </data>
  <data name="RefInvoice" xml:space="preserve">
    <value>Bezugsrechnung</value>
  </data>
  <data name="RefPurchaseOrder" xml:space="preserve">
    <value>Bezugskaufauftrag</value>
  </data>
  <data name="Required" xml:space="preserve">
    <value>Erforderlich</value>
  </data>
  <data name="ReturnAddress" xml:space="preserve">
    <value>Rücksprungadresse</value>
  </data>
  <data name="ReturnDate" xml:space="preserve">
    <value>Erwartete Rückkehr</value>
  </data>
  <data name="RMA" xml:space="preserve">
    <value>RMA</value>
  </data>
  <data name="ROHS" xml:space="preserve">
    <value>ROHS Befolgung</value>
  </data>
  <data name="ROHSReceived" xml:space="preserve">
    <value>ROHS empfangen</value>
  </data>
  <data name="ROHSRequired" xml:space="preserve">
    <value>ROHS erfordert</value>
  </data>
  <data name="SalesOrder" xml:space="preserve">
    <value>Verkaufs-Auftrag</value>
  </data>
  <data name="Salesperson" xml:space="preserve">
    <value>Verkäufer</value>
  </data>
  <data name="SellBase" xml:space="preserve">
    <value>Verkaufs-Unterseite</value>
  </data>
  <data name="SellCurrency" xml:space="preserve">
    <value>Verkaufs-Währung</value>
  </data>
  <data name="SellTerms" xml:space="preserve">
    <value>Verkaufs-Ausdrücke</value>
  </data>
  <data name="SerialNosRecorded" xml:space="preserve">
    <value>Seriennr. notiert</value>
  </data>
  <data name="ShipASAP" xml:space="preserve">
    <value>Schiff so bald wie möglich </value>
  </data>
  <data name="ShipCost" xml:space="preserve">
    <value>Schiffs-Kosten</value>
  </data>
  <data name="ShippedBy" xml:space="preserve">
    <value>Vorbei versendet</value>
  </data>
  <data name="ShippedVia" xml:space="preserve">
    <value>Versendet über</value>
  </data>
  <data name="Shipping" xml:space="preserve">
    <value>Verschiffen-Kosten </value>
  </data>
  <data name="ShippingCharge" xml:space="preserve">
    <value>Verschiffen-Gebühr </value>
  </data>
  <data name="ShippingCost" xml:space="preserve">
    <value>Verschiffen-Kosten </value>
  </data>
  <data name="ShipTo" xml:space="preserve">
    <value>Schiff zu</value>
  </data>
  <data name="ShipVia" xml:space="preserve">
    <value>Schiff über</value>
  </data>
  <data name="SignedPurchashing" xml:space="preserve">
    <value>Unterzeichneter Kauf </value>
  </data>
  <data name="SignedSales" xml:space="preserve">
    <value>Unterzeichnete Verkäufe </value>
  </data>
  <data name="SoldTo" xml:space="preserve">
    <value>Verkauft an </value>
  </data>
  <data name="SOReport" xml:space="preserve">
    <value>Verkaufs-Auftrags-Report für SO</value>
  </data>
  <data name="SubTotal" xml:space="preserve">
    <value>Teilsumme </value>
  </data>
  <data name="Supplier" xml:space="preserve">
    <value>Lieferant </value>
  </data>
  <data name="SupplierInvoice" xml:space="preserve">
    <value>Lieferanten-Rechnung </value>
  </data>
  <data name="SupplierPartNo" xml:space="preserve">
    <value>Lieferanten-Teile-Nr.</value>
  </data>
  <data name="SupplierRMA" xml:space="preserve">
    <value>Lieferant RMA </value>
  </data>
  <data name="Symbol_Weight_KG" xml:space="preserve">
    <value>kg</value>
  </data>
  <data name="Symbol_Weight_Pounds" xml:space="preserve">
    <value>lb</value>
  </data>
  <data name="Tax" xml:space="preserve">
    <value>Steuer </value>
  </data>
  <data name="Taxable" xml:space="preserve">
    <value>Steuerpflichtig</value>
  </data>
  <data name="TaxName" xml:space="preserve">
    <value>Steuer-Name </value>
  </data>
  <data name="Tel" xml:space="preserve">
    <value>Tel</value>
  </data>
  <data name="Terms" xml:space="preserve">
    <value>Ausdrücke </value>
  </data>
  <data name="To" xml:space="preserve">
    <value>Zu </value>
  </data>
  <data name="Total" xml:space="preserve">
    <value>Gesamtmenge </value>
  </data>
  <data name="TotalAllocSell" xml:space="preserve">
    <value>Gesamtmenge zugeteilter Verkauf </value>
  </data>
  <data name="TotalBuy" xml:space="preserve">
    <value>Gesamtkauf </value>
  </data>
  <data name="TotalPrice" xml:space="preserve">
    <value>Gesamtpreis </value>
  </data>
  <data name="TotalSell" xml:space="preserve">
    <value>Gesamtverkauf </value>
  </data>
  <data name="TotalWeight" xml:space="preserve">
    <value>Gesamtgewicht </value>
  </data>
  <data name="UnitPrice" xml:space="preserve">
    <value>Stückpreis </value>
  </data>
  <data name="VATNo" xml:space="preserve">
    <value>Vat-Nr. </value>
  </data>
  <data name="Yes" xml:space="preserve">
    <value>Ja</value>
  </data>
  <data name="YourCreditNote" xml:space="preserve">
    <value>Ihre Kreditnote</value>
  </data>
  <data name="YourDebitNote" xml:space="preserve">
    <value>Ihre Belastungsanzeige</value>
  </data>
  <data name="YourInvoice" xml:space="preserve">
    <value>Ihre Rechnung</value>
  </data>
  <data name="YourPONumber" xml:space="preserve">
    <value>Ihr Kaufauftrag </value>
  </data>
  <data name="YourReturnNote" xml:space="preserve">
    <value>Ihre Rückholanmerkung </value>
  </data>
  <data name="Incoterms" xml:space="preserve">
    <value>Incoterms</value>
  </data>
</root>