///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//
// RP 06.10.2009:
// - Changes due to changes in base class
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");

Rebound.GlobalTrader.Site.Controls.DropDowns.Group = function(element) { 
    Rebound.GlobalTrader.Site.Controls.DropDowns.Group.initializeBase(this, [element]);
    this._intGoodsInLineNo = -1;
    this._intInvoiceLineNo = -1;
};

Rebound.GlobalTrader.Site.Controls.DropDowns.Group.prototype = {
    get_intGoodsInLineNo: function () { return this._intGoodsInLineNo; }, set_intGoodsInLineNo: function (v) { if (this._intGoodsInLineNo !== v) this._intGoodsInLineNo = v; },
    get_intInvoiceLineNo: function () { return this._intInvoiceLineNo; }, set_intInvoiceLineNo: function (v) { if (this._intInvoiceLineNo !== v) this._intInvoiceLineNo = v; },

	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.DropDowns.Group.callBaseMethod(this, "initialize");
		this.addSetupDataCall(Function.createDelegate(this, this.setupDataCall));
		this.addGetDataOK(Function.createDelegate(this, this.dataCallOK));
	},
	
	dispose: function() { 
	    if (this.isDisposed) return;
	    this._intGoodsInLineNo = null;
	    this._intInvoiceLineNo = null;
		Rebound.GlobalTrader.Site.Controls.DropDowns.Group.callBaseMethod(this, "dispose");
	},
	
	setupDataCall: function () {
	    //alert(this._intGoodsInLineNo);
		this._objData.set_PathToData("controls/DropDowns/Group");
		this._objData.set_DataObject("Group");
		this._objData.set_DataAction("GetData");
		this._objData.addParameter("GoodsInLineNo", this._intGoodsInLineNo);
		this._objData.addParameter("InvoiceLineNo", this._intInvoiceLineNo);
	},
	
	dataCallOK: function() { 
		var result = this._objData._result;
		//if (result.Types) {
		//	for (var i = 0; i < result.Types.length; i++) {
		//	    this.addOption(result.Types[i].Group, result.Types[i].Group);
		//	}
	    //}
		if (this._objData._result.Results.length > 0) {
		    for (var i = 0; i < this._objData._result.Results.length; i++) {
		        this.addOption(this._objData._result.Results[i].Group + '   (' + this._objData._result.Results[i].RemainSerialNo + ')', this._objData._result.Results[i].Group);
		    }
		}
	}
	
};

Rebound.GlobalTrader.Site.Controls.DropDowns.Group.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.Group", Rebound.GlobalTrader.Site.Controls.DropDowns.Base);
