Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Nuggets");Rebound.GlobalTrader.Site.Controls.Nuggets.GILineNotify=function(n){Rebound.GlobalTrader.Site.Controls.Nuggets.GILineNotify.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.Nuggets.GILineNotify.prototype={get_ibtnAdd:function(){return this._ibtnAdd},set_ibtnAdd:function(n){this._ibtnAdd!==n&&(this._ibtnAdd=n)},get_blnCanEditShipInCost:function(){return this._blnCanEditShipInCost},set_blnCanEditShipInCost:function(n){this._blnCanEditShipInCost!==n&&(this._blnCanEditShipInCost=n)},get_blnCanEditPurchasePrice:function(){return this._blnCanEditPurchasePrice},set_blnCanEditPurchasePrice:function(n){this._blnCanEditPurchasePrice!==n&&(this._blnCanEditPurchasePrice=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Nuggets.GILineNotify.callBaseMethod(this,"initialize");this.showRefresh(!1);this.showLoading(!1);this.showContentLoading(!1);this.addRefreshEvent(Function.createDelegate(this,this.showAddForm));this._ibtnAdd&&($R_IBTN.addClick(this._ibtnAdd,Function.createDelegate(this,this.showAddForm)),this._frmAdd=$find(this._aryFormIDs[0]),this._frmAdd.addSaveComplete(Function.createDelegate(this,this.saveAddComplete)),this._frmAdd.addSaveError(Function.createDelegate(this,this.saveAddError)));this._frmGILineNotify=$find(this._aryFormIDs[0])},dispose:function(){this.isDisposed||(this._ibtnAdd&&$R_IBTN.clearHandlers(this._ibtnAdd),this._frmAdd&&(this._frmAdd=null),this._ibtnAdd=null,this._frmAdd=null,Rebound.GlobalTrader.Site.Controls.Nuggets.GILineNotify.callBaseMethod(this,"dispose"))},showAddForm:function(){this._ibtnAdd?(this._frmGILineNotify._blnCanEditShipInCost=this._blnCanEditShipInCost,this._frmGILineNotify._blnCanEditPurchasePrice=this._blnCanEditPurchasePrice,this.showForm(this._frmAdd,!0)):this.showContent(!0)},saveAddComplete:function(){},saveAddError:function(){this.showError(!0,this._frmAdd._strErrorMessage);$R_FN.showElement(this._pnlLinks,!0)}};Rebound.GlobalTrader.Site.Controls.Nuggets.GILineNotify.registerClass("Rebound.GlobalTrader.Site.Controls.Nuggets.GILineNotify",Rebound.GlobalTrader.Site.Controls.Nuggets.Base);