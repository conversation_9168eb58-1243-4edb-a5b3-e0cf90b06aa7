<%@ Control Language="C#" CodeBehind="CompanyManufacturers.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyManufacturers" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Nugget:DesignBase ID="ctlDB" runat="server" BoxType="Standard">
	<Links>
		<ReboundUI:IconButton ID="ibtnAdd" runat="server" IconGroup="Nugget" IconTitleResource="Add" IconCSSType="Add" IconButtonMode="Hyperlink"></ReboundUI:IconButton>
		<ReboundUI:IconButton ID="ibtnView" runat="server" IconGroup="Nugget" IconTitleResource="Edit" IconCSSType="Edit" IconButtonMode="Hyperlink" IsInitiallyEnabled="false"></ReboundUI:IconButton>
		<ReboundUI:IconButton ID="ibtnDelete" runat="server" IconGroup="Nugget" IconTitleResource="Delete" IconCSSType="Delete" IconButtonMode="Hyperlink" IsInitiallyEnabled="false"></ReboundUI:IconButton>
	</Links>
	<Content>
		<style>
			#ctl00_cphMain_ctlManufacturersSupplied_ctlDB_ctl13_ibtnMarkFranchised {
				margin-left: 20px;
			}

			#ctl00_cphMain_ctlManufacturersSupplied_ctlDB_ctl13_ddlMfrGroupBySupplier_ddl {
				margin-right: 6px;
			}
			.btn-franchised {
				margin-left: 25px;
				background-color: #d7d7d7;
			}
			.btn-franchised:disabled {
				margin-left: 25px;
				background-color: #d7d7d7;
				cursor: default;
			}
        </style>
		<script type="text/javascript">
            function callMarkFranchised()
            {
                $find("<%=this.ClientID%>").markFranchised();
            }
                    
        </script>
		<div style="margin: 0px 0px 10px 0px;">
			<asp:Label runat="server">Group Name: </asp:Label>
			<ReboundDropDown:MfrGroupBySupplier ID="ddlMfrGroupBySupplier" runat="server" />
			<input type="button" id="ibtnMarkFranchised" onclick="callMarkFranchised()" value="Mark Franchised" class="btn btn-franchised" style="margin-left: 25px;" disabled/>
		</div>
		<ReboundUI:FlexiDataTable ID="tbl" runat="server" Width="100%" PanelHeight="100" />
	</Content>
	<Forms>
		<ReboundForm:CompanyManufacturers_AddEdit id="frmAddEdit" runat="server" />
		<ReboundForm:CompanyManufacturers_Delete id="frmDelete" runat="server" />
		<ReboundForm:CompanyManufacturers_View id="frmView" runat="server" />
	</Forms>
</ReboundUI_Nugget:DesignBase>
