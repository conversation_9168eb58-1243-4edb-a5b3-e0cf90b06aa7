///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.HomeNuggets");

Rebound.GlobalTrader.Site.Controls.HomeNuggets.AllStatistics = function(element) { 
	Rebound.GlobalTrader.Site.Controls.HomeNuggets.AllStatistics.initializeBase(this, [element]);
	this._blnCanViewAllStatistics = true;
};

Rebound.GlobalTrader.Site.Controls.HomeNuggets.AllStatistics.prototype = {

	get_pnlStatsTY: function() { return this._pnlStatsTY; }, set_pnlStatsTY: function(v) { if (this._pnlStatsTY !== v)  this._pnlStatsTY = v; }, 
	get_tblStatsTY: function() { return this._tblStatsTY; }, set_tblStatsTY: function(v) { if (this._tblStatsTY !== v)  this._tblStatsTY = v; }, 
	get_pnlStatsTM: function() { return this._pnlStatsTM; }, set_pnlStatsTM: function(v) { if (this._pnlStatsTM !== v)  this._pnlStatsTM = v; }, 
	get_tblStatsTM: function() { return this._tblStatsTM; }, set_tblStatsTM: function(v) { if (this._tblStatsTM !== v)  this._tblStatsTM = v; }, 
	get_pnlStatsNM: function() { return this._pnlStatsNM; }, set_pnlStatsNM: function(v) { if (this._pnlStatsNM !== v)  this._pnlStatsNM = v; }, 
	get_tblStatsNM: function() { return this._tblStatsNM; }, set_tblStatsNM: function(v) { if (this._tblStatsNM !== v)  this._tblStatsNM = v; }, 
	get_pnlStatsLY: function() { return this._pnlStatsLY; }, set_pnlStatsLY: function(v) { if (this._pnlStatsLY !== v)  this._pnlStatsLY = v; }, 
	get_tblStatsLY: function() { return this._tblStatsLY; }, set_tblStatsLY: function(v) { if (this._tblStatsLY !== v)  this._tblStatsLY = v; }, 
	get_pnlStatsLM: function() { return this._pnlStatsLM; }, set_pnlStatsLM: function(v) { if (this._pnlStatsLM !== v)  this._pnlStatsLM = v; }, 
	get_tblStatsLM: function() { return this._tblStatsLM; }, set_tblStatsLM: function(v) { if (this._tblStatsLM !== v)  this._tblStatsLM = v; }, 
	get_blnCanViewAllStatistics: function() { return this._blnCanViewAllStatistics; }, set_blnCanViewAllStatistics: function(value) { if (this._blnCanViewAllStatistics !== value)  this._blnCanViewAllStatistics = value; }, 

	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.HomeNuggets.AllStatistics.callBaseMethod(this, "initialize");
		this.addRefreshEvent(Function.createDelegate(this, this.getData));
	},

	dispose: function() { 
		if (this.isDisposed) return;
		if (this._tblStatsTY) this._tblStatsTY.dispose();
		if (this._tblStatsTM) this._tblStatsTM.dispose();
		if (this._tblStatsNY) this._tblStatsNY.dispose();
		if (this._tblStatsNM) this._tblStatsNM.dispose();
		if (this._tblStatsLY) this._tblStatsLY.dispose();
		if (this._tblStatsLM) this._tblStatsLM.dispose();
		this._pnlStatsTY = null;
		this._tblStatsTY = null;
		this._pnlStatsTM = null;
		this._tblStatsTM = null;
		this._pnlStatsNM = null;
		this._tblStatsNM = null;
		this._pnlStatsLY = null;
		this._tblStatsLY = null;
		this._pnlStatsLM = null;
		this._tblStatsLM = null;
		this._blnCanViewAllStatistics = null;
		Rebound.GlobalTrader.Site.Controls.HomeNuggets.AllStatistics.callBaseMethod(this, "dispose");
	},
	
	setupLoadingState: function() {
		$R_FN.showElement(this._pnlStatsTY, false);
		$R_FN.showElement(this._pnlStatsTM, false);
		$R_FN.showElement(this._pnlStatsNM, false);
		$R_FN.showElement(this._pnlStatsLY, false);
		$R_FN.showElement(this._pnlStatsLM, false);
		Rebound.GlobalTrader.Site.Controls.HomeNuggets.AllStatistics.callBaseMethod(this, "setupLoadingState");
	},
	
	getData: function() {
		if (this._blnCanViewAllStatistics) {
		    this.setupLoadingState();
		    var obj = new Rebound.GlobalTrader.Site.Data();
		    obj.set_PathToData("controls/HomeNuggets/AllStatistics");
		    obj.set_DataObject("AllStatistics");
		    obj.set_DataAction("GetData");
		    obj.addParameter("rowcount", this._intRowCount);
		    obj.addDataOK(Function.createDelegate(this, this.getDataComplete));
		    obj.addError(Function.createDelegate(this, this.homeNuggetDataError));
		    obj.addTimeout(Function.createDelegate(this, this.homeNuggetDataError));
		    $R_DQ.addToQueue(obj);
		    $R_DQ.processQueue();
		    obj = null;
		} else {
		    this.hideLoading();
		    this.showHomeNuggetContent(false);
		}
	},

	getDataComplete: function(args) {
		this.showHomeNuggetContent(true);
		var result = args._result;
		var aryData, row;
		//this year
		this._tblStatsTY.clearTable();
		for (var i = 0; i < result.StatsTY.length; i++) {
			row = result.StatsTY[i];
			aryData = [
				row.GPDetail,
				row.SalesValue,
				row.Cost,
				row.GP 
				];
			this._tblStatsTY.addRow(aryData, null);
		}
		$R_FN.showElement(this._pnlStatsTY, true);
		
		//this month
		this._tblStatsTM.clearTable();
		for (i = 0; i < result.StatsTM.length; i++) {
			row = result.StatsTM[i];
			aryData = [
				row.GPDetail,
				row.SalesValue,
				row.Cost,
				row.GP 
				];
			this._tblStatsTM.addRow(aryData, null);
		}
		$R_FN.showElement(this._pnlStatsTM, true);
		
		//next month
		this._tblStatsNM.clearTable();
		for (i = 0; i < result.StatsNM.length; i++) {
			row = result.StatsNM[i];
			aryData = [
				row.GPDetail,
				row.SalesValue,
				row.Cost,
				row.GP 
				];
			this._tblStatsNM.addRow(aryData, null);
		}
		$R_FN.showElement(this._pnlStatsNM, true);
		
		//last year
		this._tblStatsLY.clearTable();
		for (i = 0; i < result.StatsLY.length; i++) {
			row = result.StatsLY[i];
			aryData = [
				row.GPDetail,
				row.SalesValue,
				row.Cost,
				row.GP 
				];
			this._tblStatsLY.addRow(aryData, null);
		}
		$R_FN.showElement(this._pnlStatsLY, true);
		
		//last month
		this._tblStatsLM.clearTable();
		for (i = 0; i < result.StatsLM.length; i++) {
			row = result.StatsLM[i];
			aryData = [
				row.GPDetail,
				row.SalesValue,
				row.Cost,
				row.GP 
				];
			this._tblStatsLM.addRow(aryData, null);
		}
		$R_FN.showElement(this._pnlStatsLM, true);
		
		this.hideLoading();
	}
	
};

Rebound.GlobalTrader.Site.Controls.HomeNuggets.AllStatistics.registerClass("Rebound.GlobalTrader.Site.Controls.HomeNuggets.AllStatistics", Rebound.GlobalTrader.Site.Controls.HomeNuggets.Base, Sys.IDisposable);
