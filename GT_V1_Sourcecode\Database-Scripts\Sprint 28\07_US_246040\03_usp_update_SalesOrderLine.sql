﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO
/* 
==========================================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-246040]		Trung Pham			20-May-2025		Update			Add new field
==========================================================================================================
*/
CREATE OR ALTER PROCEDURE [dbo].[usp_update_SalesOrderLine]                                                       
--******************************************************************************************                                                                          
/*                                                      
Marker     changed by      date         Remarks                                                      
[001]      Vinay           21/01/2014   CR:- Add AS9120 Requirement in GT application                                   
[002]      Abhinav Saxena  24/05/2022   Add new fields for EI p-1                                                      
*/                                                      
--******************************************************************************************                                                      
    @SalesOrderLineId int                                                      
  , @Part nvarchar(30)                                                      
  , @ManufacturerNo int = NULL                                                      
  , @DateCode nvarchar(5) = NULL                                                      
  , @PackageNo int = NULL                                                      
  , @Quantity int                                                      
  , @Price float                                                      
  , @DatePromised datetime                                                      
  , @RequiredDate datetime                                                      
  , @Instructions nvarchar(max) = NULL                                                      
  , @ProductNo int = NULL                                                      
  , @Taxable nvarchar(1)                                                      
  , @CustomerPart nvarchar(30) = NULL                                                      
  , @ShipASAP bit                                                      
  , @Inactive bit                                                      
  , @ROHS tinyint = NULL                                                    
  , @Notes nvarchar(2000) = NULL                                                      
  , @UpdatedBy int = NULL                                                   
  --[002] code start                                                    
  , @ProductSource tinyint = NULL                                                        
  --[002] code end                                                      
  , @PODelDate datetime = null                                                    
  , @SOSerialNo int = 0                                                  
  , @PrintHazardous bit = 0                                                  
  , @MSLLevel nvarchar(100) = NULL                                                   
  , @ContractNo nvarchar(100) = NULL                                             
  , @IsFormChanged bit=0                                           
  , @IsReasonChanged bit = 0                                          
  , @PromiseReasonNo int = null                                          
  , @ECCNCode nvarchar(100) = NULL                                    
  , @ECCNNo int = null             
  , @ServiceCostRef float = null
  , @RowsAffected int = NULL OUTPUT                                   
  --[004]  code start                                                                                          
  --, @EI_Required    INT=0                                
  --, @EvidenceNotes  NVARCHAR(MAX)=NULL                                
  --, @TestingType    INT    =NULL                              
--[004]  code end                                                  
AS         
BEGIN                                      
  --14-Feb-2019                                        
  DECLARE @OldPromiseDate DATETIME                                            
  SELECT  @OldPromiseDate = DatePromised FROM dbo.tbSalesOrderLine          
  WHERE   SalesOrderLineId = @SalesOrderLineId               
                
  if(@ECCNCode = '[Blank]')    
  begin    
 set @ECCNCode = null    
 set @ECCNNo = null    
  end    
  -------------------------------------Log entery for ECCN Log----------------------------16-09-2022-----RP-30------------------------------                
declare @CHKEccnCode nvarchar(50)=null                
declare @SectionName varchar(50) = 'SOLineECCN'                          
    declare @SubSectionName varchar(50) = 'ECCN'                          
    declare @ActionName   varchar(10) = 'Print'                          
    declare @DocumentNo     int     = @SalesOrderLineId                        
    declare @Detail     nvarchar(max) =null                       
    declare @PrintDocumentLogId  int =NULL                
                   
                
 select @CHKEccnCode=ECCNCode from tbSalesOrderLine                             
  WHERE   SalesOrderLineId = @SalesOrderLineId  and ECCNCode is not null                 
              
                
                
  -------------------------------------ECCN Log Entery End----------------------------------------------------                        
   if( @ECCNCode is not null)                          
   begin                        
  if(@CHKEccnCode!= @ECCNCode)                        
  begin                        
 set @Detail   = 'Action��' +' ECCN CODE CHANGED FROM  ( ' + @CHKEccnCode + ' )'                         
 EXEC [dbo].[usp_insert_PrintEmailLog]                                 
   @SectionName                               
       , @SubSectionName                              
       , @ActionName                                 
       , @DocumentNo                                 
       , @Detail                                   
       , @UpdatedBy                                
       , @PrintDocumentLogId                             
  end                        
  --else                        
  --if(@CHKEccnCode is not null)                        
  --begin                        
  --set @Detail  = 'Action��' +' ECCN Code changed from  ( ' + @ECCNCode + ' )'                         
  --EXEC [dbo].[usp_insert_PrintEmailLog]                                 
  -- @SectionName                               
  --     , @SubSectionName                              
  --     , @ActionName                                 
  --     , @DocumentNo                                 
  --     , @Detail                                   
  --     , @UpdatedBy                                
  --     , @PrintDocumentLogId                             
  -- -----------------------------------------------------------------------                              
  --end                        
  else                        
  if(@CHKEccnCode is  null)                        
  begin                        
  set @Detail  = 'Action��' +' ECCN CODE ADDED WITH  ( ' + @ECCNCode + ' )'                         
  EXEC [dbo].[usp_insert_PrintEmailLog]                                 
   @SectionName                               
       , @SubSectionName                              
       , @ActionName                                 
       , @DocumentNo                  , @Detail                                   
       , @UpdatedBy                                
       , @PrintDocumentLogId                             
   -----------------------------------------------------------------------                              
  end                    
  end                         
    if(@ECCNCode is  null)                     
 begin                       
  if(@CHKEccnCode is not null)                        
  begin                        
  set @Detail  = 'Action��' +' ECCN CODE CHANGED FROM  ( ' + @CHKEccnCode + ' )'                         
  EXEC [dbo].[usp_insert_PrintEmailLog]                                 
   @SectionName                               
       , @SubSectionName                              
       , @ActionName                                 
       , @DocumentNo                                 
       , @Detail                                   
       , @UpdatedBy                                
       , @PrintDocumentLogId                             
   -----------------------------------------------------------------------                              
  end                      
  end                    
                         
                            
-------------------------------------ECCN Log Entery End----------------------------------------------------              
               
  --14-Feb-2019 END                                                 
 --SET DateConfirmed to null                                                
 IF(@IsFormChanged=1)                                               
 BEGIN                                                
  UPDATE  dbo.tbSalesOrderLine                                                      
  SET    DateConfirmed = NULL   ,                                        
   DateChanged = GETDATE()                                               
  WHERE   SalesOrderLineId = @SalesOrderLineId                         
 end                                                
                    
                  
                
-------Code end for ECCN Log Entery------------------------------------------------------------------------                   
                                                
    UPDATE  dbo.tbSalesOrderLine                                                      
    SET     FullPart = dbo.ufn_get_fullpart(@Part)                                                      
          , Part = @Part                                                      
          , ManufacturerNo = @ManufacturerNo                                                      
      , DateCode = @DateCode                                                      
       , PackageNo = @PackageNo                                                  
         , Quantity = @Quantity                                                      
          , Price = @Price                                                      
          , DatePromised = @DatePromised                                                      
          , RequiredDate = @RequiredDate                                              
   , Instructions = @Instructions                                                      
          , ProductNo = @ProductNo                                                      
          , Taxable = @Taxable                                     
          , CustomerPart = @CustomerPart                                                      
          , ShipASAP = @ShipASAP                                                      
          , Inactive = @Inactive                                                      
          , ROHS = @ROHS                                                      
          , Notes = @Notes                                                      
          , UpdatedBy = @UpdatedBy                                                      
        , DLUP = CURRENT_TIMESTAMP                                                      
          , FullCustomerPart = dbo.ufn_get_fullpart(@CustomerPart)                                                  
--[002] code start                                                    
          , ProductSource=@ProductSource                                                      
          --[002] code end                                                  
    , PODeliveryDate = @PODelDate                                                  
    , PrintHazardous = @PrintHazardous                                               
    , MSLLevel = @MSLLevel                                                  
    ,ContractNo=@ContractNo                                   
 ,ECCNCode=@ECCNCode                                               
 --[004]  code start                            
--, EI_Required=@EI_Required                            
--, EvidenceNotes=@EvidenceNotes                            
--, TestingType=@TestingType                            
--[004]  code end                                                       
,ServiceCostRef = @ServiceCostRef
    WHERE   SalesOrderLineId = @SalesOrderLineId                                                      
 --Espire: 30 Jan 18                                                  
 and SOSerialNo = @SOSerialNo                                
-- IF(@EI_Required=1 AND @TestingType=1)                          
-- BEGIN                        
-- --// Get the testing Labs with warehouse//--                    
--DECLARE @WarehouseNo  INT=0                    
--DECLARE @TestingLabNo  INT                    
                    
--SELECT @WarehouseNo=WarehouseNo FROM tbStock WHERE StockId                     
--IN(SELECT StockNo FROM tbAllocation WHERE SalesOrderLineNo=@SalesOrderLineId)                    
----SELECT @TestingLabNo=TestingLabNo FROM tbWarehouse WHERE WarehouseId=@WarehouseNo                    
                  
--SELECT @TestingLabNo AS TestingLabNo                  
-----------------// END // ---------------------                    
                    
-- DECLARE @EISONo INT=0;                       
-- DECLARE @TokenSalesOrderLineId INT=0;                         
-- SELECT @EISONo=SalesOrderNo FROm tbSalesOrderLine WHERE SalesOrderLineId=@SalesOrderLineId                          
-- --IF((SELECT COUNT(1) FROM tbEI_BookingDetailsInfo WHERE SalesOrderNo=@EISONo AND SOLineNo=@SalesOrderLineId)=0)                          
-- --BEGIN                          
-- --EXEC usp_insert_EI_SoToken @EISONo,@Quantity,@Price,@DatePromised,@RequiredDate,@PODelDate,@Instructions,                      
-- --@Notes,@UpdatedBy,@ProductSource,NULL,@SalesOrderLineId,NULL,1,NULL,@ShipASAP,@ProductNo, @TokenSalesOrderLineId out                      
-- --INSERT INTO tbEI_BookingDetailsInfo(SalesOrderNo,SOLineNo,EITokenLineNo)VALUES(@EISONo,@SalesOrderLineId,@TokenSalesOrderLineId)                      
-- --INSERT INTO tbEI_BookedTest SELECT (SELECT BookingDetailId from tbEI_BookingDetailsInfo WHERE SOLineNo=@SalesOrderLineId),TestingItemId,@UpdatedBy,Current_timestamp,0                    
-- --FROM tbEI_TestingItem WHERE TestingLabNo=@TestingLabNo AND ISAddedDefault=1                    
-- --END                          
-- end                         
-- ELSE                          
-- BEGIN                        
-- DECLARE @EITokenLineNo INT=0                        
-- SELECT @EITokenLineNo=EITokenLineNo FROM tbEI_BookingDetailsInfo WHERE SOLineNo=@SalesOrderLineId                        
-- DELETE FROM tbEI_BookingDetailsInfo WHERE SOLineNo=@SalesOrderLineId                        
-- DELETE FROM tbSalesOrderLine WHERE SalesOrderLineId=@EITokenLineNo       DELETE FROM tbEI_BookedTest  WHERE BookingDetailNo=(SELECT BookingDetailId from tbEI_BookingDetailsInfo WHERE SOLineNo=@SalesOrderLineId)                    
-- DELETE FROM tbStock WHERE StockId IN(SELECT StockNo FROM tbAllocation WHERE SalesOrderLineNo=@EITokenLineNo)                  
-- DELETE FROM tbAllocation WHERE SalesOrderLineNo=@EITokenLineNo                  
-- END                                            
-----------INSERT INTO Promise Reason Log---------------                                          
IF(@IsReasonChanged = 1)                                          
BEGIN                                          
 INSERT INTO tblSOLinePromiseReasonLog(SOLineNo, PromiseReasonNo, UpdatedBy, UpdatedDate,DatePromiseOld)                                          
 SELECT   @SalesOrderLineId,@PromiseReasonNo,@UpdatedBy,GetDate(),@OldPromiseDate                                            
                                           
END                                         
                                        
UPDATE  dbo.tbSalesOrderLine                                                      
SET     PromiseReasonNo = @PromiseReasonNo                                        
WHERE   SalesOrderLineId = @SalesOrderLineId                            
                                           
-----------END---------------------------------------                                  
                                
--for Eccn code insert cammand                                    
if(@ECCNNo is not null and @ECCNCode is not null)                                        
begin         
if(@ECCNCode!='')                                      
begin          
declare @checkdublicate int=0                                             
set @checkdublicate=(select count(*) from tbPartEccnMapped where  Part=@Part )                                                         
if(@checkdublicate=0)                                                
begin                            
INSERT INTO dbo.tbPartEccnMapped                                                                      
 ( Part                                                                      
 , ECCNNo                                                                      
 , ECCNCode                                            
 , ClientNo            
 , UpdatedBy                                                                      
                                                                    
 )                                                                      
VALUES                                                                      
 ( @Part                                                                      
 , @ECCNNo                                                                      
 , @ECCNCode                 
 , 0                                                                      
 , @UpdatedBy                                      
)                                                      
end                                    
end         
end        
--code end                                     
                                        
SELECT  @RowsAffected = @@ROWCOUNT       
-------------------------------------[005] Starts here ---------------------------------------------------------------------        
      
DECLARE @CheckOgelRequiredFlag bit = 0        
DECLARE @DefaultApprovalStatusId int = 3;        
DECLARE @SalesOrderNo INT=0;      
SELECT @SalesOrderNo=SalesOrderNo FROM tbSalesOrderLine WHERE SalesOrderLineId=@SalesOrderLineId        
IF((select count(1) from tbSO_ExportApprovalStatusOGEL WHERE SalesOrderLineNo = @SalesOrderLineId)=0)  -- check if SalesOrderLineNo already exists in table or not        
BEGIN      
SELECT @CheckOgelRequiredFlag  = isnull(OGEL_Required,0)             
FROM tbSalesOrder t WHERE t.SalesOrderId = @SalesOrderNo         
        
IF (@CheckOgelRequiredFlag = 1)           
 BEGIN        
  INSERT INTO tbSO_ExportApprovalStatusOGEL (        
  SalesOrderNo        
  , SalesOrderLineNo        
  , ApprovalStatusId        
  --, MilitaryUses        
  , UpdatedBy        
  , DLUP)        
  VALUES (        
  @SalesOrderNo        
  , @SalesOrderLineId        
  , @DefaultApprovalStatusId        
  --, @Ogel_MilitaryUse        
  , @UpdatedBy        
  , getdate())        
 END        
END        
        
-----------------------------------[005] ends here --------------------------------       
END
GO


