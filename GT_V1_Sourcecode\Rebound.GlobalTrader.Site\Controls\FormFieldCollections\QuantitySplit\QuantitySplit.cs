using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using System.Collections;
using Rebound.GlobalTrader.Site.Controls.Forms;
using Rebound.GlobalTrader.Site;

namespace Rebound.GlobalTrader.Site.Controls.FormFieldCollections {
	[DefaultProperty("")]
	[ToolboxData("<{0}:QuantitySplit runat=server></{0}:QuantitySplit>")]
	public class QuantitySplit : Base, INamingContainer {

		#region Locals

		private HyperLink _hypItemSplit;
		private HyperLink _hypItemUnsplit;
		private Panel _pnlItems;

		#endregion

		#region Properties

		private int _intTotalQuantity;
		public int TotalQuantity {
			get { return _intTotalQuantity; }
			set { _intTotalQuantity = value; }
		}

		private int _intTotalWidth = 300;
		public int TotalWidth {
			get { return _intTotalWidth; }
			set { _intTotalWidth = value; }
		}

		private int _intMaxSplits = 5;
		public int MaxSplits {
			get { return _intMaxSplits; }
			set { _intMaxSplits = value; }
		}

		#endregion

		protected override void OnInit(EventArgs e) {
			base.OnInit(e);
			AddScriptReference("Controls.FormFieldCollections.QuantitySplit.QuantitySplit");
			RemoveCSSClass = true;
		}

		protected override void OnLoad(EventArgs e) {

			//start table
			HtmlTable tbl = new HtmlTable();
			tbl.Border = 0;
			tbl.CellPadding = 0;
			tbl.CellSpacing = 0;
			HtmlTableRow tr = new HtmlTableRow();
			tbl.Rows.Add(tr);

			//left cell
			HtmlTableCell tdLeft = new HtmlTableCell();
			tdLeft.Attributes["class"] = "title";
			ControlBuilders.CreateLiteralInsideParent(tdLeft, Functions.GetGlobalResource("FormFields", "SplitQuantities"));
			tr.Cells.Add(tdLeft);

			//right cell
			HtmlTableCell tdRight = new HtmlTableCell();
			tdRight.Attributes["class"] = "item";
			tr.Cells.Add(tdRight);

			//outer
			Panel pnlOuter = ControlBuilders.CreatePanelInsideParent(tdRight, "splitStock");

			//buttons
			Panel pnlButtons = ControlBuilders.CreatePanelInsideParent(pnlOuter, "splitStockButtons");
			_hypItemSplit = ControlBuilders.CreateHyperLinkInsideParent(pnlButtons, "", "javascript:void(0);", "[ + ]");
			_hypItemSplit.ID = "hypItemSplit";
			_hypItemUnsplit = ControlBuilders.CreateHyperLinkInsideParent(pnlButtons, "", "javascript:void(0);", "[ - ]");
			_hypItemUnsplit.ID = "hypItemUnsplit";

			//items
			_pnlItems = ControlBuilders.CreatePanelInsideParent(pnlOuter, "splitStockItems");
			_pnlItems.ID = "pnlItems";

			AddControl(tbl);
			SetupScriptDescriptors();
			base.OnLoad(e);
		}

		private void SetupScriptDescriptors() {
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.FormFieldCollections.QuantitySplit", this.ClientID);
			_scScriptControlDescriptor.AddElementProperty("hypItemSplit", _hypItemSplit.ClientID);
			_scScriptControlDescriptor.AddElementProperty("hypItemUnsplit", _hypItemUnsplit.ClientID);
			_scScriptControlDescriptor.AddElementProperty("pnlItems", _pnlItems.ClientID);
			_scScriptControlDescriptor.AddProperty("intTotalQuantity", _intTotalQuantity);
			_scScriptControlDescriptor.AddProperty("intTotalWidth", _intTotalWidth);
			_scScriptControlDescriptor.AddProperty("intMaxSplits", _intMaxSplits);
		}

	}
}
