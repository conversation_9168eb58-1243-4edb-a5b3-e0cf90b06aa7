﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

/*
===========================================================================================================================================================  
TASK			UPDATED BY       DATE				ACTION		DESCRIPTION  
[US-225812]     Trung Pham		 20-Apr-2025		CREATE		Get country by Name
===========================================================================================================================================================  
*/

CREATE OR ALTER PROCEDURE [dbo].[usp_autosearch_Country]
	@NameSearch NVARCHAR(50),
	@ClientId INT
AS

SELECT CountryId, CountryName
FROM tbCountry
WHERE ISNULL(Inactive,0) = 0
AND ClientNo = @ClientId
AND CountryName like @NameSearch
ORDER BY IsPriorityForLists desc, CountryName  