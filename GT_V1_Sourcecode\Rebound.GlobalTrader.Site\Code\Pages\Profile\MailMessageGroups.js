Type.registerNamespace("Rebound.GlobalTrader.Site.Pages.Profile");Rebound.GlobalTrader.Site.Pages.Profile.MailMessageGroups=function(n){Rebound.GlobalTrader.Site.Pages.Profile.MailMessageGroups.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Pages.Profile.MailMessageGroups.prototype={get_ctlGroups:function(){return this._ctlGroups},set_ctlGroups:function(n){this._ctlGroups!==n&&(this._ctlGroups=n)},get_ctlMembers:function(){return this._ctlMembers},set_ctlMembers:function(n){this._ctlMembers!==n&&(this._ctlMembers=n)},initialize:function(){Rebound.GlobalTrader.Site.Pages.Profile.MailMessageGroups.callBaseMethod(this,"initialize")},goInit:function(){this._ctlGroups&&this._ctlGroups.addSelectGroup(Function.createDelegate(this,this.ctlSecurityGroups_SelectGroup));this._ctlMembers&&this._ctlMembers.addSaveEditComplete(Function.createDelegate(this,this.ctlSecurityGroupMembers_SaveEditComplete));this._ctlGroups&&this._ctlGroups.addDeleteComplete(Function.createDelegate(this,this.ctlSecurityGroupMembers_DeleteComplete));Rebound.GlobalTrader.Site.Pages.Profile.MailMessageGroups.callBaseMethod(this,"goInit")},dispose:function(){this.isDisposed||(this._ctlGroups&&this._ctlGroups.dispose(),this._ctlMembers&&this._ctlMembers.dispose(),this._ctlGroups=null,this._ctlMembers=null,Rebound.GlobalTrader.Site.Pages.Profile.MailMessageGroups.callBaseMethod(this,"dispose"))},ctlSecurityGroups_SelectGroup:function(){this._ctlMembers._intMailGroupID=this._ctlGroups._intMailGroupID;this._ctlMembers.refresh();this._ctlGroups._tbl.resizeColumns();this.showNuggets(!0)},ctlSecurityGroupMembers_SaveEditComplete:function(){this._ctlGroups.refresh()},ctlSecurityGroupMembers_DeleteComplete:function(){this.showNuggets(!1)},showNuggets:function(n){this._ctlMembers.show(n)}};Rebound.GlobalTrader.Site.Pages.Profile.MailMessageGroups.registerClass("Rebound.GlobalTrader.Site.Pages.Profile.MailMessageGroups",Rebound.GlobalTrader.Site.Pages.Content,Sys.IDisposable);