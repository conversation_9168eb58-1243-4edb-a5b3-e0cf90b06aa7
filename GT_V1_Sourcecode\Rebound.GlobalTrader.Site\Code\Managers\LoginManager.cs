//-----------------------------------------------------------------------------------------
// RP 07.01.2010:
// - set Session Timeout from User Preferences
//-----------------------------------------------------------------------------------------
using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.BLL;
using System.Text;
using System.Collections.Generic;
using Rebound.GlobalTrader.Site;
using System.Net.Mail;

// [001] <PERSON>rya Vyas RP-2326/RP-1709   both strat offers and reverse logistics import tools are not showing on DMCC on live (added LoginId parameter)

namespace Rebound.GlobalTrader.Site {
	public class LoginManager {

        internal static Rebound.GlobalTrader.BLL.Login LogUserIntoSystem(string strUserName, string strPassword, string strUserHostAddress, string strSessionID, out int intReturnCode, string strServerIP, string strADLogin,System.Boolean? enableADAuth)
        {
			BLL.Login lg = null;
            if (!enableADAuth.Value)
                intReturnCode = BLL.Login.DoLogin(strUserName, strPassword, strUserHostAddress, strSessionID, strServerIP, strADLogin);
            else
                intReturnCode = BLL.Login.DoADLogin(strUserName, strPassword, strUserHostAddress, strSessionID, strServerIP, strADLogin);

            if (intReturnCode == 0) {
                if (!enableADAuth.Value)
                    lg = BLL.Login.GetByName(strUserName);
                else
                    lg = BLL.Login.GetByADName(strUserName);

                //store things in session
                SessionManager.StoreGeneralLoginItems(lg);
				SessionManager.StoreClientLoginItems(lg);

				//store general Site-level and Section-level permissions
				SecurityManager.StoreGeneralLevelPermissions(lg.LoginId);
				SecurityManager.StoreSectionLevelPermissions(lg.LoginId);

				//get user preferences
				StoreUserPreferences(lg.LoginId);
                //store warehouse permission
                SecurityManager.StoreWarehousePermissions();
                SecurityManager.StoreUtilityPermissions(lg.LoginId); //[001]
                SecurityManager.StoreOrdersPermission(lg.LoginId);
                //clean up some old stuff
                CleanUp();
			}
			return lg;
		}

        internal static Rebound.GlobalTrader.BLL.Login LogMasterUserIntoSystem(string strAdUserName, string strUserHostAddress, string strSessionID, out int intReturnCode, string strServerIP)
        {
            BLL.Login lg = null;
            string strGTLogin = "";
            intReturnCode = BLL.Login.DoMasterLogin(strAdUserName, strUserHostAddress, strSessionID, strServerIP, out strGTLogin);
            if (intReturnCode == 0)
            {
                //HttpContext.Current.Session["testLog"] = "UK";
                lg = BLL.Login.GetByName(strGTLogin);

                //store things in session
                SessionManager.StoreGeneralLoginItems(lg);
                SessionManager.StoreClientLoginItems(lg);

                //store general Site-level and Section-level permissions
                SecurityManager.StoreGeneralLevelPermissions(lg.LoginId);
                SecurityManager.StoreSectionLevelPermissions(lg.LoginId);

                //get user preferences
                StoreUserPreferences(lg.LoginId);
                //store warehouse permission
                SecurityManager.StoreWarehousePermissions();
                SecurityManager.StoreUtilityPermissions(lg.LoginId); //[001]
                SecurityManager.StoreOrdersPermission(lg.LoginId);
                //clean up some old stuff
                CleanUp();
            }
            return lg;
        }

        internal static void CleanUp() {
			//remove old files from the temp upload folder and old CSVs
			FileUploadManager.ClearOldTemporaryFiles();
			FileUploadManager.ClearLoginsOldCSVs();
		}

		internal static void LogUserOut() {
			CleanUp();
			//complete the logout on the DB
			if (SessionManager.LoginID != null) BLL.Login.Logout(SessionManager.LoginID, HttpContext.Current.Session.SessionID);

			//end the ASP.Net Session
			SessionManager.EndSession();
		}

		internal static string GetName(int intLoginID) {
			string str = "";
			BLL.Login lg = BLL.Login.GetName(intLoginID);
			if (lg != null) str = lg.EmployeeName;
			lg = null;
			return str;
		}

		internal static bool CheckReboundAdminLogin(string strUser, string strPassword) {
			bool blnReboundLogin = false;
			if (strUser.ToUpper() == "REBOUNDADMIN") {
				if (Functions.MD5Hash(strPassword) == "9cd94671034df1806f368dd559ec191f") {
					blnReboundLogin = true;
					BLL.Login lg = BLL.Login.Get(0);

					//store things in session
					SessionManager.StoreGeneralLoginItems(lg);

					//store general Site-level and Section-level permissions
					SecurityManager.StoreGeneralLevelPermissions(lg.LoginId);
					SecurityManager.StoreSectionLevelPermissions(lg.LoginId);

					//get user preferences
					StoreUserPreferences(lg.LoginId);

					lg = null;
				}
			}
			return blnReboundLogin;
		}

        internal static void ForgotPassword(string strUserName, out int intReturnCode, out string strPassword,out string strEmail)
        {
            //BLL.Login lg = null;
            intReturnCode = BLL.Login.ForgotPassword(strUserName, out strPassword, out strEmail);
            if (intReturnCode == 0)
            {
                string strEmailFrom = strEmail;
                MailAddressCollection adrTo = null;
                string strMessage = "";
                strMessage = string.Format(Functions.GetGlobalResource("Messages", "ForgotMessage"),strPassword);

                adrTo = new MailAddressCollection();
                adrTo.Add(strEmail);
                System.Net.Mail.MailAddress adrFrom = new MailAddress(strEmailFrom);
                System.Net.Mail.MailAddress adrReplyTo = new MailAddress(strEmailFrom);
                OutgoingMailManager.SendUserMail(Functions.GetGlobalResource("Messages", "ForgotPwdSubject"), strMessage, adrTo, adrFrom, null, null, adrReplyTo, true);
            }
            //return lg;
        }

        internal static void ForgotUserName(System.Int32? clientNo,  System.String email, out int intReturnCode)
        {
            //BLL.Login lg = null;
            List<BLL.Login> lst = BLL.Login.ForgotUserName(clientNo, email);
            intReturnCode = (lst != null && lst.Count > 0) ? 0 : 99;
            if (intReturnCode == 0)
            {
                //string strEmailFrom = email;
                MailAddressCollection adrTo = null;
                string strMessage = "";

                StringBuilder sb = new StringBuilder();
                sb.Append("<table width=\"100%\" border=\"1\" cellpadding=\"2\" cellspacing=\"0\" >");
                sb.Append("<tr>");
                sb.Append("<td align=\"center\" style=\"background-color: #e0e0e0;\" colspan=\"2\">" + lst[0].ClientName + "</td>");
                sb.Append("</tr>");
                sb.Append("<tr>");
                sb.Append("<td style=\"background-color: #e0e0e0;\">User name </td>");
                sb.Append("<td style=\"background-color: #e0e0e0;\">Password</td>");
                sb.Append("</tr>");
                foreach (BLL.Login lg in lst)
                {
                    sb.Append("<tr>");
                    sb.Append("<td>" + lg.LoginName + "</td>");
                    sb.Append("<td>" + lg.LastName + "</td>");
                    sb.Append("</tr>");
                }
                
                sb.Append("</table>");


                strMessage = string.Format(Functions.GetGlobalResource("Messages", "ForgotUserName"), sb.ToString());
                strMessage = strMessage.Replace("\n", "<br/>");

                sb = null;

                adrTo = new MailAddressCollection();
                adrTo.Add(email);
                System.Net.Mail.MailAddress adrFrom = new MailAddress(email);
                System.Net.Mail.MailAddress adrReplyTo = new MailAddress(email);
                OutgoingMailManager.SendUserMail(Functions.GetGlobalResource("Messages", "ForgotUserSubject"), strMessage, adrTo, adrFrom, null, null, adrReplyTo, true);
            }
            //return lg;
        }

        internal static Rebound.GlobalTrader.BLL.Login LogMasterNoIntoSystem(System.Int32? masterLoginNo, System.Int32? clientNo, System.String ipAddress, System.String sessionId, System.String serverIP)
        {
            BLL.Login lg = null;
            lg = BLL.Login.GetLoginByMasterLoginNo(masterLoginNo, clientNo, ipAddress, sessionId, serverIP);
            if (lg != null)
            {
                //store things in session
                SessionManager.StoreGeneralLoginItems(lg);
                SessionManager.StoreClientLoginItems(lg);

                HttpContext.Current.Session["ClientNoForDropdown"] = lg.DefaultClientNo;

                //store general Site-level and Section-level permissions
                SecurityManager.StoreGeneralLevelPermissions(lg.LoginId);
                SecurityManager.StoreSectionLevelPermissions(lg.LoginId);

                //get user preferences
                StoreUserPreferences(lg.LoginId);
                //store warehouse permission
                SecurityManager.StoreWarehousePermissions();
                SecurityManager.StoreUtilityPermissions(lg.LoginId); //[001]
                SecurityManager.StoreOrdersPermission(lg.LoginId);
                //clean up some old stuff
                CleanUp();
            }
            return lg;
        }

        private static void StoreUserPreferences(int intLoginID) {
			BLL.LoginPreference pref = BLL.LoginPreference.GetByLogin(intLoginID);
			if (pref != null) SessionManager.StoreLoginPreferences(pref);
            HttpContext.Current.Session.Timeout = pref.LoginTimeout;
			pref = null;
		}
	}

}