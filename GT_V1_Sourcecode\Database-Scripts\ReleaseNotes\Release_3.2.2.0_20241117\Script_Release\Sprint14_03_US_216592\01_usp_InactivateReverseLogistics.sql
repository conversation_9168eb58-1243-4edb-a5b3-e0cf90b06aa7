﻿
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO
/* 
========================================================================================================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-216592]		Phuc Hoang			03-Nov-2024		UPDATE			Strategic/ RL Import Utility - Allow deactivating all Stock(s) except the ones attached in Sourcing
========================================================================================================================================================================
*/

CREATE OR ALTER PROCEDURE [dbo].[usp_InactivateReverseLogistics]                                                                                                                
  @UserId INT=0 ,                                                        
  @ClientId INT=0,                                                        
  @SelectedImportId INT=0  
AS
BEGIN 
DECLARE @Message NVARCHAR(500) = '';
  
    IF EXISTS (SELECT 1 FROM  tbSourcingResult a join BorisGlobalTraderImports..tbReverseLogistic b ON a.SourcingTableItemNo = b.ReverseLogisticId
		WHERE a.SourcingTable = 'RLPH' and b.ImportId=@SelectedImportId
	)
	BEGIN
		UPDATE relgt 
		SET relgt.Inactive=1, relgt.UpdatedBy = @UserId, relgt.InactiveDate=GETDATE() 
		FROM BorisGlobalTraderImports.dbo.tbReverseLogistic relgt
		LEFT JOIN dbo.tbSourcingResult sr ON sr.SourcingTableItemNo = relgt.ReverseLogisticId AND sr.SourcingTable = 'RLPH'
		WHERE reLgt.ImportId = @SelectedImportId AND ISNULL(sr.SourcingResultId, 0) = 0 --and UpdatedBy = @UserId

		SET @Message = 'Please note some lines on the sheet you have imported have been attached to sourcing results and therefore will not be removed from the sourcing results.'
	END
	
	ELSE
	BEGIN
		UPDATE BorisGlobalTraderImports.dbo.tbReverseLogistic SET Inactive=1 , UpdatedBy =@UserId, InactiveDate=GETDATE() WHERE ImportId = @SelectedImportId  --and UpdatedBy = @UserId
		UPDATE BorisGlobalTraderimports.dbo.tbReverseLogistic_ImportHistory SET InactiveDate=GETDATE() WHERE ImportId = @SelectedImportId  --and UpdatedBy = @UserId

		SET @Message = 'File items deactivated.'
	END	

	SELECT @Message AS 'Message', convert(BIT,1) AS 'Status' 

END

GO


