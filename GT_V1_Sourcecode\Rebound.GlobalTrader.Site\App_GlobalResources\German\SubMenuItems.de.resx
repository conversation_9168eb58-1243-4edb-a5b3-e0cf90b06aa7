<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Address" xml:space="preserve">
    <value>Adresse</value>
  </data>
  <data name="Company" xml:space="preserve">
    <value>Firma</value>
  </data>
  <data name="CompanyLetters" xml:space="preserve">
    <value>Firma-Buchstaben</value>
  </data>
  <data name="CompanyProfile" xml:space="preserve">
    <value>Firma-Profil</value>
  </data>
  <data name="Contact" xml:space="preserve">
    <value>Kontakt</value>
  </data>
  <data name="ContactLetters" xml:space="preserve">
    <value>Kontakt-Buchstaben</value>
  </data>
  <data name="CreditNote" xml:space="preserve">
    <value>Kreditnote</value>
  </data>
  <data name="CreditNotes" xml:space="preserve">
    <value>Kreditnoten</value>
  </data>
  <data name="CRMA" xml:space="preserve">
    <value>Kunde RMA</value>
  </data>
  <data name="CustomerRMA" xml:space="preserve">
    <value>Kunde RMA</value>
  </data>
  <data name="CustomerRMAs" xml:space="preserve">
    <value>Kunde RMAs</value>
  </data>
  <data name="DataEntry" xml:space="preserve">
    <value>Dateneingabe</value>
  </data>
  <data name="DebitNote" xml:space="preserve">
    <value>Belastungsanzeige</value>
  </data>
  <data name="DebitNotes" xml:space="preserve">
    <value>Belastungsanzeigen</value>
  </data>
  <data name="Email" xml:space="preserve">
    <value>EMail</value>
  </data>
  <data name="EmailCertificateOfConformance" xml:space="preserve">
    <value>EMail-Bescheinigung der Übereinstimmung</value>
  </data>
  <data name="EmailCreditNote" xml:space="preserve">
    <value>EMail-Gutschrift</value>
  </data>
  <data name="EmailCRMA" xml:space="preserve">
    <value>EMail-Kunde RMA</value>
  </data>
  <data name="EmailDebitNote" xml:space="preserve">
    <value>EMail-Schuldposten</value>
  </data>
  <data name="EmailInvoice" xml:space="preserve">
    <value>EMail-Rechnung</value>
  </data>
  <data name="EmailPackingSlip" xml:space="preserve">
    <value>EMail-Verpackungs-Beleg</value>
  </data>
  <data name="EmailPO" xml:space="preserve">
    <value>EMail-PO</value>
  </data>
  <data name="EmailProFormaInvoice" xml:space="preserve">
    <value>EMail Proforma</value>
  </data>
  <data name="EmailQuote" xml:space="preserve">
    <value>EMail-Anführungsstrich</value>
  </data>
  <data name="EmailSO" xml:space="preserve">
    <value>EMail-SO</value>
  </data>
  <data name="EmailSRMA" xml:space="preserve">
    <value>EMail-Lieferant RMA</value>
  </data>
  <data name="GoodsIN" xml:space="preserve">
    <value>Waren innen</value>
  </data>
  <data name="Invoice" xml:space="preserve">
    <value>Rechnung</value>
  </data>
  <data name="Invoices" xml:space="preserve">
    <value>Rechnungen</value>
  </data>
  <data name="Letter" xml:space="preserve">
    <value>Buchstabe</value>
  </data>
  <data name="Lot" xml:space="preserve">
    <value>Los</value>
  </data>
  <data name="Manufacturer" xml:space="preserve">
    <value>Hersteller</value>
  </data>
  <data name="PackingSlip" xml:space="preserve">
    <value>Verpackungs-Beleg</value>
  </data>
  <data name="Print" xml:space="preserve">
    <value>Druck</value>
  </data>
  <data name="PrintCertificateOfConformance" xml:space="preserve">
    <value>Druck-Bescheinigung der Übereinstimmung</value>
  </data>
  <data name="PrintCreditNote" xml:space="preserve">
    <value>Druck-Gutschrift</value>
  </data>
  <data name="PrintCRMA" xml:space="preserve">
    <value>Druck-Kunde RMA</value>
  </data>
  <data name="PrintDebitNote" xml:space="preserve">
    <value>Druckl-Schuldposten</value>
  </data>
  <data name="PrintInvoice" xml:space="preserve">
    <value>Druck-Rechnung</value>
  </data>
  <data name="PrintPackingSlip" xml:space="preserve">
    <value>Druck-Verpackungs-Beleg</value>
  </data>
  <data name="PrintPO" xml:space="preserve">
    <value>Druck-PO</value>
  </data>
  <data name="PrintProFormaInvoice" xml:space="preserve">
    <value>Druck Proforma</value>
  </data>
  <data name="PrintQuote" xml:space="preserve">
    <value>ruck-Preisangabe</value>
  </data>
  <data name="PrintSO" xml:space="preserve">
    <value>Druck-SO</value>
  </data>
  <data name="PrintSOReport" xml:space="preserve">
    <value>Druck-SO Report</value>
  </data>
  <data name="PrintSRMA" xml:space="preserve">
    <value>EMail-Lieferant RMA</value>
  </data>
  <data name="PurchaseOrder" xml:space="preserve">
    <value>Kaufauftrag</value>
  </data>
  <data name="PurchaseOrders" xml:space="preserve">
    <value>Kaufauftragen</value>
  </data>
  <data name="PurchaseRequisitions" xml:space="preserve">
    <value>Kauf-Forderungen</value>
  </data>
  <data name="Quotation" xml:space="preserve">
    <value>Preisangabe</value>
  </data>
  <data name="Quotations" xml:space="preserve">
    <value>Preisangaben</value>
  </data>
  <data name="ReceivedCustomerRMAs" xml:space="preserve">
    <value>Empfangener Kunde RMAs</value>
  </data>
  <data name="ReceivedPurchaseOrders" xml:space="preserve">
    <value>Empfangene Kaufaufträge</value>
  </data>
  <data name="Requirement" xml:space="preserve">
    <value>Anforderung</value>
  </data>
  <data name="Requirements" xml:space="preserve">
    <value>Anforderungen</value>
  </data>
  <data name="SalesOrder" xml:space="preserve">
    <value>Verkaufs-Auftrag</value>
  </data>
  <data name="SalesOrders" xml:space="preserve">
    <value>Verkaufs-Aufträge</value>
  </data>
  <data name="ScheduleCall" xml:space="preserve">
    <value>Zeitplan-Anruf</value>
  </data>
  <data name="ScheduledCalls" xml:space="preserve">
    <value>Zeitlich geplante Anrufe</value>
  </data>
  <data name="Service" xml:space="preserve">
    <value>Service</value>
  </data>
  <data name="ShippedSupplierRMAs" xml:space="preserve">
    <value>Versendeter Lieferant RMAs</value>
  </data>
  <data name="SRMA" xml:space="preserve">
    <value>Lieferant RMA</value>
  </data>
  <data name="Stock" xml:space="preserve">
    <value>Vorrat</value>
  </data>
  <data name="StockItem" xml:space="preserve">
    <value>Stock Item</value>
  </data>
  <data name="SupplierRMA" xml:space="preserve">
    <value>Lieferant RMA</value>
  </data>
  <data name="SupplierRMAs" xml:space="preserve">
    <value>Lieferant RMAs</value>
  </data>
  <data name="TransferData" xml:space="preserve">
    <value>Übergangsdaten</value>
  </data>
</root>