﻿/*
 * Action: Created          By:<PERSON><PERSON><PERSON><PERSON>       Dated:09-02-2022    Comment: Add new dropdown for query HIC Staus.
 */ 
using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;

namespace Rebound.GlobalTrader.Site.Controls.DropDowns {
	public partial class QueryHICStatus : Base {

		protected override void OnLoad(EventArgs e) {
            SetDropDownType("QueryHICStatus");
            AddScriptReference("Controls.DropDowns.QueryHICStatus.QueryHICStatus");
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.DropDowns.QueryHICStatus", ClientID);
			base.OnLoad(e);
		}

	}
}