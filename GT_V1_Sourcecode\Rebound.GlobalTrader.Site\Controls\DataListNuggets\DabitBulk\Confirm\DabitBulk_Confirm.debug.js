///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.DabitBulk_Confirm = function(element) {
    Rebound.GlobalTrader.Site.Controls.Forms.DabitBulk_Confirm.initializeBase(this, [element]);
    this._strDebits = "";
    this._blnCOC = false;
    this._blnPackagingSlip = false;
};

Rebound.GlobalTrader.Site.Controls.Forms.DabitBulk_Confirm.prototype = {
    initialize: function() {
        Rebound.GlobalTrader.Site.Controls.Forms.DabitBulk_Confirm.callBaseMethod(this, "initialize");
        this.addShown(Function.createDelegate(this, this.formShown));
    },

    dispose: function() {
        if (this.isDisposed) return;
        if (this._ctlConfirm) this._ctlConfirm.dispose();
        this._ctlConfirm = null;
        this._strCredits = null;
        this._blnCOC = null;
        Rebound.GlobalTrader.Site.Controls.Forms.DabitBulk_Confirm.callBaseMethod(this, "dispose");
    },

    formShown: function () {
        if (this._blnFirstTimeShown) {
            this._ctlConfirm = this.getFieldComponent("ctlConfirm");
            this._ctlConfirm.addClickYesEvent(Function.createDelegate(this, this.yesClicked));
            this._ctlConfirm.addClickNoEvent(Function.createDelegate(this, this.noClicked));
        }
    },

    yesClicked: function () {
        debugger;
        this.showSaving(true);
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/DebitMainInfo");
        obj.set_DataObject("DebitMainInfo");
        obj.set_DataAction("SaveDebitEmail");
        obj.addParameter("Debits", this._strDebits);
        obj.addDataOK(Function.createDelegate(this, this.saveComplete));
        obj.addError(Function.createDelegate(this, this.saveError));
        obj.addTimeout(Function.createDelegate(this, this.saveError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
        this.onSave();
    },

    noClicked: function() {
        this.showSaving(false);
        this.onNotConfirmed();
    },

    saveError: function(args) {
        this.showSaving(false);
        this._strErrorMessage = args._errorMessage;
        this.onSaveError();
    },

    saveComplete: function(args) {
        this.showSaving(false);
        if (args._result.Result >= 1) {
            this.onSaveComplete();
            if (args._result.Result == 1) {
                alert($R_RES.DebitFinanceContactNotFoundMessage + ' ' + args._result.Credits);
            }
            else if (args._result.Result == 2) {
                alert($R_RES.DebitProgressMessage + '\n\n' + $R_RES.DebitFinanceContactNotFoundMessage + ' ' + args._result.Credits);
            }
            else {
                alert($R_RES.DebitProgressMessage);
            }
        } else {
            this._strErrorMessage = args._errorMessage;
            this.onSaveError();
        }
    }

};

Rebound.GlobalTrader.Site.Controls.Forms.DabitBulk_Confirm.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.DabitBulk_Confirm", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
