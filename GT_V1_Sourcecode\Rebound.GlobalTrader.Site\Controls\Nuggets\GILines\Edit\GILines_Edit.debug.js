///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// RP 12.10.2009:
// - retrofitting changes from v3.0.34 - allow selection of whether to update Stock
//   and Shipments too (task 322)
// [001]     Aashu Singh      26-Oct-2018    Issue - with GI line edit at a time in two tabs.
// [002]     Ravi Bhushan     21-03-2023     [RP-968] Barcode scan fields
// [003]     Devendra         29-12-2023     [RP-2544] Recieved Part No 30 char max validation
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");
Rebound.GlobalTrader.Site.Controls.Forms.GILines_Edit = function (element) {
    Rebound.GlobalTrader.Site.Controls.Forms.GILines_Edit.initializeBase(this, [element]);
    this._intGIID = 0;
    this._intLineID = -1;
    this._intCurrencyID = -1;
    this._strCurrencyCode = "";
    this._dblPOLineShipInCost = 0;
    this._intPOQuantityOrdered = 0;
    this._blnCanEditShipInCost = false;
    this._blnCanEditPurchasePrice = false;
    this._strPartNoQuery = "";
    this._strManufacturerQuery = "";
    this._strPackagingTypeQuery = "";
    this._strMslQuery = "";
    this._strRohsQuery = "";
    this._blnRelatedToIPO = false;
    this._intIPOClientNo = -1;
    this._poBankFee = 0;
    this._intGlobalClientNo = -1;
    this._intSerialNoCount = 0;
    this._blnSerNoRecorded = false;
    this._blnProductHaza = false;
    this._blnGISplited = false;

    this._intPOManufacturerId = -1;
    this._intPOPackagingNo = -1;
    this._intPOROHSNo = -1;
    this._POMSL = "";
    //this._PackagingBreakdown = [];
    //this._DateCode = [];
    this._FieldChanges = {};
    this._intLotNoCount = 0;
    this._blnLotNoRecorded = false;
    this._intGoodsInNumber = -1;
    //[002] start
    this._tempBarcodeRemark = "";
    this._blnBarcodeRemarkMandatory = false;
    this._blnFirstTimePageLoad = true;
    this._ddl_val_zero = true;
    this._blnQAIncludeApproverHtml = false;
    this._blnSalesDataIncludeApproverHtml = false; 
    this._blnPurchaseDataIncludeApproverHtml = false; 
    this.GIQueryId = 0;
    this._blnC1 = false;
    this._blnC2 = false;
    this._blnC3 = false;
    this._blnC4 = false;
    this._blnC5 = false;
    this._blnC6 = false;
    this._blnC7 = false;
    this._blnC8 = false;
    this._blnC9 = false;
    this._blnC10 = false;
    this._IsSTOGi = false;
    this._IsSTOKHub = false;
    //[002] end

    document.getElementById("btnSendQuery").addEventListener("click", Function.createDelegate(this, this.SendQueryMessage));
    document.getElementById("btnSendResponce").addEventListener("click", Function.createDelegate(this, this.SendApprovalResponce));
    document.getElementById("btnSaveNewApprover").addEventListener("click", Function.createDelegate(this, this.ChangeApprover));
    document.getElementById("btnSaveRename").addEventListener("click", Function.createDelegate(this, this.RenameCaption));
    document.getElementById("btnDraftQueryMessage").addEventListener("click", Function.createDelegate(this, this.DraftQueryMessage));
    document.getElementById("btnResponseDraftQueryMessage").addEventListener("click", Function.createDelegate(this, this.DraftQueryMessage));
    document.getElementById("btnBulkAttacheDelete").addEventListener("click", Function.createDelegate(this, this.BulkAttachmentDelete));
    //document.getElementById("btnResponse").addEventListener("click", Function.createDelegate(this, this.GetApprovalTabledata));
    //[003] start code
    $("#CorrectPartNoError1").append(`<span style="float: left; display: inline - block; clear: both; "> Max length: (30 chrs max)</span>`);
     //[003] end code
    this._PackBreakDownJSON = [];
    this._blnCanViewApproval = false;
    this._blnCanViewAttachment = false;
    this._isSendClicked = false;
    this._GI_QueryId = 0;
    this._NavigateFromGIInfo = false;
    this._blnCanManageApproverEdit = false;
    this._RefreshFieldChanges = {};
    this._PackagingListJSON = [];
    this._MFRLabelListJSON = [];
    this._intEnhancedInspectionStatusId = -1;
    this._intGIlineBarcodesStatusId = -1;//[002]
    this._IsQueryRaised = false;
    var ManageApprover;
    this._IsViewModeOnly = false;
    this._blnCanEdit = true;
    this._IsQueryMessage = false;
    this._InspectedBy = 0;
    this._intAttachmentId = 0;
    this._strAttachmentType = "";
    this._blnDeletePermission = false;
    this.allowSaving = true;
};

Rebound.GlobalTrader.Site.Controls.Forms.GILines_Edit.prototype = {
    get_intGIID: function () { return this._intGIID; }, set_intGIID: function (v) { if (this._intGIID !== v) this._intGIID = v; },
    get_dblPOLineShipInCost: function () { return this._dblPOLineShipInCost; }, set_dblPOLineShipInCost: function (v) { if (this._dblPOLineShipInCost !== v) this._dblPOLineShipInCost = v; },
    get_intPOQuantityOrdered: function () { return this._intPOQuantityOrdered; }, set_intPOQuantityOrdered: function (v) { if (this._intPOQuantityOrdered !== v) this._intPOQuantityOrdered = v; },
    get_IsPOHub: function () { return this._IsPOHub; }, set_IsPOHub: function (v) { if (this._IsPOHub !== v) this._IsPOHub = v; },
    get_ibtnSendQuery: function () { return this._ibtnSendQuery; }, set_ibtnSendQuery: function (value) { if (this._ibtnSendQuery !== value) this._ibtnSendQuery = value; },
    get_ibtnSaveRelease: function () { return this._ibtnSaveRelease; }, set_ibtnSaveRelease: function (value) { if (this._ibtnSaveRelease !== value) this._ibtnSaveRelease = value; },
    initialize: function () {
        Rebound.GlobalTrader.Site.Controls.Forms.GILines_Edit.callBaseMethod(this, "initialize");
        this.addShown(Function.createDelegate(this, this.formShown));
        this.addSave(Function.createDelegate(this, this.saveClicked));
        var fnContinue = Function.createDelegate(this, this.finishedForm);
        document.getElementById("aAttachments").addEventListener("click", Function.createDelegate(this, this.getAttachementsData));
        document.getElementById("ctl00_cphMain_ctlLines_ctlDB_ctl17_ibtnSendQuery_hyp").addEventListener("click", Function.createDelegate(this, this.SaveAndSendQuery));
        document.getElementById("ctl00_cphMain_ctlLines_ctlDB_ctl18_ibtnSendQuery_hyp").addEventListener("click", Function.createDelegate(this, this.SaveAndSendQuery));
        $("#btnBulkAttacheDelete").hide();
        document.getElementById("ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_ddlQualityApproved_ddl").addEventListener("change", Function.createDelegate(this, this.MadeChangeInQAddl));
        document.getElementById("ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_ddlSalesApproved_ddl").addEventListener("change", Function.createDelegate(this, this.MadeChangeInSalesddl));
        document.getElementById("ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_ddlPurchasingApproved_ddl").addEventListener("change", Function.createDelegate(this, this.MadeChangeInPurchaseddl));
        //[002] event for character count
        document.getElementById("ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_ddlGILineBarcodesStatus_ddl").addEventListener("change", Function.createDelegate(this, this.BarcodeScannedChanged));
        
        
        //document.getElementById("ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_txtBarcodeRemarks").addEventListener("keyup", Function.createDelegate(this, this.characterCounter));
    },
    goClick: function () {
        //alert('Demo');
    },
    formShown: function () {
        
        $R_IBTN.enableButton(this._ibtnSave, false);
        $R_IBTN.enableButton(this._ibtnSave_Footer, false);

        $R_IBTN.enableButton(this._ibtnCancel, false);
        $R_IBTN.enableButton(this._ibtnCancel_Footer, false);

        SetDefaultUploadValue("1");
       
        $("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_txtQueryPartNo").val(this._strPartNoQuery.trim().replace(/<br\s*[\/]?>/gi, "\n"));
        $("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_txtQueryManfacturer").val(this._strManufacturerQuery.trim().replace(/<br\s*[\/]?>/gi, "\n"));
        $("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_txtPackageTypeQuery").val(this._strPackagingTypeQuery.trim().replace(/<br\s*[\/]?>/gi, "\n"));
        $("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_txtMslQuery").val(this._strMslQuery.trim().replace(/<br\s*[\/]?>/gi, "\n"));
        $("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_txtRohsQuery").val(this._strRohsQuery.trim().replace(/<br\s*[\/]?>/gi, "\n"));
        //[002] start (below code will be executed when the modal window is loaded)

        // in below code getting the class name of the element (imageCheckbox in this case). if class is 'off' i.e. it is not checked

        // below checking either 'yes' check box's class is 'on' or 'off'. if it is 'off' i.e. 'yes' check box is not checked
        // in this case disable the textbox for remarks
        //var cssClassOfElement = $("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_radioBarCodeYes_ctl00").attr("class");
        //if (cssClassOfElement == "off") {
        //    $("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_txtBarcodeRemarks").prop("disabled", true);
        //    $("#BarcodeMandatory").hide();
        //}
        //else {
        //    $("#BarcodeMandatory").show();
        //    this._blnBarcodeRemarkMandatory = true;
        //}
        //$("#barcodeRemarError").removeClass("error"); // remove the erro class at the time of first load of the page.

        //[002] end

        $("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_txtAccountNotes").attr('maxlength', '128');
        $("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_txtGeneralInspectionNotes").attr('maxlength', '1500');
        $("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_ddlCCUsersRequest_tdLabel").prop("colspan", "6");
        $("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_ddlCCUsers_tdLabel").prop("colspan", "6");
        $('#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_ctl02_ctlMultiStep_ctlItem1_ctl00').click();

        $("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_txtQueryPartNo").attr('maxlength', '150');
        $("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_txtQueryManfacturer").attr('maxlength', '150');
        $("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_txtPackageTypeQuery").attr('maxlength', '150');
        $("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_txtMslQuery").attr('maxlength', '150');
        $("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_txtRohsQuery").attr('maxlength', '150');

        $("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_txtActeoneTest").attr('maxlength', '150');
        $("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_txtIsopropryle").attr('maxlength', '150');

        $("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_txtQueryBakingLevel").attr('maxlength', '50');

        $("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_txtBarcodeRemarks").attr('maxlength', '1000');

        $find(this.getFormControlID(this._element.id, 'ddlSalesApproved')).getData();
        $find(this.getFormControlID(this._element.id, 'ddlQualityApproved')).getData();
        $find(this.getFormControlID(this._element.id, 'ddlPurchasingApproved')).getData();
        $find(this.getFormControlID(this._element.id, 'ddlNewSalesApprover')).getData();
        $find(this.getFormControlID(this._element.id, 'ddlNewPurchasingApprover')).getData();

        //$find(this.getFormControlID(this._element.id, 'ddlCCUsers')).getData();
        this._ctlMail = $find(this.getField("ddlCCUsers").ID);
        this._ctlMail._ctlRelatedForm = this;

        this._ctlMailRequest = $find(this.getField("ddlCCUsersRequest").ID);
        this._ctlMailRequest._ctlRelatedForm = this;

         

        this.storeOriginalFieldValues();
     
        if (this._intGlobalClientNo > 0) {
            $find(this.getFormControlID(this._element.id, 'cmbProducts'))._aut._intGlobalLoginClientNo = this._intGlobalClientNo;
        } else {
            $find(this.getFormControlID(this._element.id, 'cmbProducts'))._aut._intPOHubClientNo = this._intIPOClientNo;
        }
        if (!this._IsPOHub) {
            $('#' + (this.getFormControlID(this._element.id, 'lblShipInCost'))).css("display", (this._blnCanEditShipInCost /*&& !this._blnGISplited*/) == true ? "block" : "none");
            $('#' + (this.getFormControlID(this._element.id, 'lblShipInCost'))).css("display", !(this._blnCanEditShipInCost /*&& !this._blnGISplited*/) == true ? "block" : "none");
        }
        else {
            $('#' + (this.getFormControlID(this._element.id, 'lblShipInCost'))).css("display", "none");
            $('#' + (this.getFormControlID(this._element.id, 'lblShipInCost'))).css("display", "none");

        }
            if (this._blnRelatedToIPO) {
                $('#' + (this.getFormControlID(this._element.id, 'txtPrice'))).css("display", (this._blnCanEditPurchasePrice && this._IsPOHub) == true ? "" : "none");
                $('#' + (this.getFormControlID(this._element.id, 'lblCurrency_Price'))).css("display", (this._blnCanEditPurchasePrice && this._IsPOHub) == true ? "block" : "none");
                $('#' + (this.getFormControlID(this._element.id, 'lblPrice'))).css("display", (!this._blnCanEditPurchasePrice) == true ? "block" : "none");
                $('#' + (this.getFormControlID(this._element.id, 'lblCurrency_PriceLabel'))).css("display", (!this._blnCanEditPurchasePrice) == true ? "block" : "none");
            }
            else {
                $('#' + (this.getFormControlID(this._element.id, 'txtPrice'))).css("display", (this._blnCanEditPurchasePrice) == true ? "" : "none");
                $('#' + (this.getFormControlID(this._element.id, 'lblCurrency_Price'))).css("display", (this._blnCanEditPurchasePrice) == true ? "block" : "none");
                $('#' + (this.getFormControlID(this._element.id, 'lblPrice'))).css("display", (!this._blnCanEditPurchasePrice) == true ? "block" : "none");
                $('#' + (this.getFormControlID(this._element.id, 'lblCurrency_PriceLabel'))).css("display", (!this._blnCanEditPurchasePrice) == true ? "block" : "none");
            }
        
            $('#' + (this.getFormControlID(this._element.id, 'txtPrice_IPO'))).css("display", (this._blnCanEditPurchasePrice && this._blnRelatedToIPO && !this._IsPOHub) == true ? "" : "none");
            $('#' + (this.getFormControlID(this._element.id, 'lblCurrency_Price_IPO'))).css("display", (this._blnCanEditPurchasePrice && this._blnRelatedToIPO && !this._IsPOHub) == true ? "block" : "none");
            $('#' + (this.getFormControlID(this._element.id, 'lblPrice_IPO'))).css("display", (!this._blnCanEditPurchasePrice) == true ? "block" : "none");
            $('#' + (this.getFormControlID(this._element.id, 'lblCurrency_PriceLabel_IPO'))).css("display", (!this._blnCanEditPurchasePrice) == true ? "block" : "none");


        if (this._IsSTOGi == true) {
            $('#' + (this.getFormControlID(this._element.id, 'txtPrice'))).prop('disabled', true);
            $('#' + (this.getFormControlID(this._element.id, 'txtPrice_IPO'))).prop('disabled', true);
            $("#imgPriceinfo").show();
        }
        else {
            $('#' + (this.getFormControlID(this._element.id, 'txtPrice'))).prop('disabled', false);
            $('#' + (this.getFormControlID(this._element.id, 'txtPrice_IPO'))).prop('disabled', false);
            $("#imgPriceinfo").hide();
        }
        $find((this.getFormControlID(this._element.id, 'chkReqSerailNo'))).enableButton(!(this._intSerialNoCount > 0));
        $find((this.getFormControlID(this._element.id, 'chkLotCodeReq'))).enableButton(!(this._intLotNoCount > 0));
        if (this._blnGISplited == false)
            this.showHideQuantity(this._blnSerNoRecorded);
        else
            this.showHideQuantity(this._blnGISplited);
        document.getElementById("GoodsInUpdateTypeError").style.backgroundColor = "";
        document.getElementById("GoodsInUpdateTypeError1").style.backgroundColor = "";
        document.getElementById("LocationError").style.backgroundColor = "";
        document.getElementById("LocationError1").style.backgroundColor = "";
        document.getElementById('ProductsError').style.backgroundColor = "";
        document.getElementById('ProductsError1').style.backgroundColor = "";
        document.getElementById('ShipInCostError').style.backgroundColor = "";
        document.getElementById('ShipInCostError1').style.backgroundColor = "";
        document.getElementById('tcQuantity').style.backgroundColor = "";

        document.getElementById('QuantityError').style.backgroundColor = "";
        document.getElementById('QuantityError1').style.backgroundColor = "";

        
        

        //Enable Disable checkbox -- Start
        this._chkPartNumberCorrect = $find(this.getFormControlID(this._element.id, 'chkPartNumberCorrect'));
        this._chkPartNumberCorrect.addClick(Function.createDelegate(this, this.CorrectPartNo));
        this._chkManufacturerCorrect = $find(this.getFormControlID(this._element.id, 'chkManufacturerCorrect'));
        this._chkManufacturerCorrect.addClick(Function.createDelegate(this, this.ManufacturerCorrect));
        this._chkPackageCorrect = $find(this.getFormControlID(this._element.id, 'chkPackageCorrect'));
        this._chkPackageCorrect.addClick(Function.createDelegate(this, this.PackageCorrect));
        this._chkMSLCorrect = $find(this.getFormControlID(this._element.id, 'chkMSLCorrect'));
        this._chkMSLCorrect.addClick(Function.createDelegate(this, this.MSLCorrect));
        this._chkRohsStatusCorrect = $find(this.getFormControlID(this._element.id, 'chkRohsStatusCorrect'));
        this._chkRohsStatusCorrect.addClick(Function.createDelegate(this, this.RohsStatusCorrect));
        //Enable Disable checkbox -- End

        this._chkbakingYes = $find(this.getFormControlID(this._element.id, 'chkbakingYes'));
        this._chkbakingYes.addClick(Function.createDelegate(this, this.BakingLabelAdded));
        this._chkbakingNo = $find(this.getFormControlID(this._element.id, 'chkbakingNo'));
        this._chkbakingNo.addClick(Function.createDelegate(this, this.BakingLabelAddedNo));
        this._chkbakingNA = $find(this.getFormControlID(this._element.id, 'chkbakingNA'));
        this._chkbakingNA.addClick(Function.createDelegate(this, this.BakingLabelAddedNA));



        this._chkActeonePass = $find(this.getFormControlID(this._element.id, 'chkActeoneTestPass'));
        this._chkActeonePass.addClick(Function.createDelegate(this, this.ActeoneTestPass));
        this._chkActeoneFail = $find(this.getFormControlID(this._element.id, 'chkActeoneTestFail'));
        this._chkActeoneFail.addClick(Function.createDelegate(this, this.ActeoneTestFail));
        this._chkActeoneNA = $find(this.getFormControlID(this._element.id, 'chkActeoneTestNA'));
        this._chkActeoneNA.addClick(Function.createDelegate(this, this.ActeoneTestNA));

        this._chkIsoproprylePass = $find(this.getFormControlID(this._element.id, 'chkIsoproprylePass'));
        this._chkIsoproprylePass.addClick(Function.createDelegate(this, this.IsopropryleActeoneTestPass));
        this._chkIsopropryleFail = $find(this.getFormControlID(this._element.id, 'chkIsopropryleFail'));
        this._chkIsopropryleFail.addClick(Function.createDelegate(this, this.IsopropryleActeoneTestFail));
        this._chkIsopropryleNA = $find(this.getFormControlID(this._element.id, 'chkIsopropryleNA'));
        this._chkIsopropryleNA.addClick(Function.createDelegate(this, this.IsopropryleActeoneTestNA));

        //this._chkEnhancedInpectionRequired = $find(this.getFormControlID(this._element.id, 'chkEnhancedInpectionRequired'));
        //this._chkEnhancedInpectionRequired.addClick(Function.createDelegate(this, this.EnhancedInpectionRequired));

        if (this._blnCanViewApproval == false) {
            $('#aApprovals').hide();
        }
        if (this._blnCanViewAttachment == false) {
            $('#aAttachments').hide();
        }
        this.bindPackagingBreakdownData();
         //[003] start code
        $("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_txtCorrectPartNo").attr("maxlength", 30);
         //[003] end code
        $find('ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_ddlGILineBarcodesStatus').addChanged(Function.createDelegate(this, this.BarcodeScannedChanged)); //[002]

        $find('ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_ddlEnhancedInspection').addChanged(Function.createDelegate(this, this.findEnhancedInspectionStatus));
        PackagingDropDownJSON = this._PackagingListJSON;
        MFRLabelListJSON = this._MFRLabelListJSON;
        $find(this.getFormControlID(this._element.id, 'cmbCountryOfManufacture'))._aut._blnIncludeSelected = true;
        IsEditClick = false;
        this.stepChanged();
        $("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_ddlCCUsers_ctlTo_tdTitle").css('color', '#fffff4');
        IsEditPermission = this._blnCanEdit;
        IsQueryMessage = this._IsQueryMessage;
        InspectedBy = this._InspectedBy;
        setTimeout(function () {
            $("#ctl00_cphMain_ctlLines_ctlDB_ctl17_ibtnSave_lblDisabled").hide();
            $("#ctl00_cphMain_ctlLines_ctlDB_ctl17_ibtnSave_hyp").removeClass("iconButton iconButton_Nugget iconButton_Nugget_Save iconButton_alignLeft invisible");
            $("#ctl00_cphMain_ctlLines_ctlDB_ctl17_ibtnSave_hyp").addClass("iconButton iconButton_Nugget iconButton_Nugget_Save iconButton_alignLeft");

            $("#ctl00_cphMain_ctlLines_ctlDB_ctl18_ibtnSave_lblDisabled").hide();
            $("#ctl00_cphMain_ctlLines_ctlDB_ctl18_ibtnSave_hyp").removeClass("iconButton iconButton_Nugget iconButton_Nugget_Save iconButton_alignLeft invisible");
            $("#ctl00_cphMain_ctlLines_ctlDB_ctl18_ibtnSave_hyp").addClass("iconButton iconButton_Nugget iconButton_Nugget_Save iconButton_alignLeft");

            $("#ctl00_cphMain_ctlLines_ctlDB_ctl17_ibtnCancel_lblDisabled").hide();
            $("#ctl00_cphMain_ctlLines_ctlDB_ctl17_ibtnCancel_hyp").removeClass("iconButton iconButton_Nugget iconButton_Nugget_Save iconButton_alignLeft invisible");
            $("#ctl00_cphMain_ctlLines_ctlDB_ctl17_ibtnCancel_hyp").addClass("iconButton iconButton_Nugget iconButton_Nugget_Save iconButton_alignLeft");

            $("#ctl00_cphMain_ctlLines_ctlDB_ctl18_ibtnCancel_lblDisabled").hide();
            $("#ctl00_cphMain_ctlLines_ctlDB_ctl18_ibtnCancel_hyp").removeClass("iconButton iconButton_Nugget iconButton_Nugget_Save iconButton_alignLeft invisible");
            $("#ctl00_cphMain_ctlLines_ctlDB_ctl18_ibtnCancel_hyp").addClass("iconButton iconButton_Nugget iconButton_Nugget_Save iconButton_alignLeft");
        }, 10000);
        
        this._blnFirstTimePageLoad = true; //[002]
        this._ddl_val_zero = true;
        this.BarcodeScannedChanged(); // [002]

    },
    dispose: function () {
        if (this.isDisposed) return;
        this._intGIID = null;
        this._lblCurrency_Price = null;
        this._intLineID = null;
        this._intCurrencyID = null;
        this._strCurrencyCode = null;
        this._dblPOLineShipInCost = null;
        this._intPOQuantityOrdered = null;
        this._blnCanEditShipInCost = null;
        this._blnCanEditPurchasePrice = null;
        this._lblCurrency_PriceLabel = null;
        this._lblCurrency_Price_IPO = null;
        this._lblCurrency_PriceLabel_IPO = null;
        this._IsPOHub = null;
        this._blnRelatedToIPO = null;
        this._poBankFee = null;
        this._intGlobalClientNo = null;
        this._blnSerNoRecorded = null;
        this._blnProductHaza = null;
        if (this._ibtnSendQuery) $R_IBTN.clearHandlers(this._ibtnSendQuery);
        this._ibtnSendQuery = null;
        this._intEnhancedInspectionStatusId = null;
        this._intGIlineBarcodesStatusId = null; //[002]
        this._IsQueryRaised = null;
        this._blnCanEdit = null;
        this._InspectedBy = null;
        this._intAttachmentId = null;
        this._strAttachmentType = null;
        if (this._ibtnSave) $R_IBTN.clearHandlers(this._ibtnSave);
        this._ibtnSave = null;
        if (this._ibtnSave_Footer) $R_IBTN.clearHandlers(this._ibtnSave_Footer);
        this._ibtnSave_Footer = null;
        Rebound.GlobalTrader.Site.Controls.Forms.GILines_Edit.callBaseMethod(this, "dispose");
    },
    //[002] start
    //characterCounter: function () {
        //var currentlength = $("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_txtBarcodeRemarks").val().length;
        //var remaining = 1000 - currentlength;
        ///*$("#textWritten").html("("+currentlength + " written / ");*/
        //$("#textWritten").html("Character Count(1000 chrs max) : ")
        //$("#txtRemaining").html(remaining);
    //},
    //[002] end
    setCurrency: function (intID, strCode) {
        this._intCurrencyID = intID;
        this._strCurrencyCode = strCode;
        $R_FN.setInnerHTML(this._lblCurrency_Price, strCode);
        $R_FN.setInnerHTML(this._lblCurrency_PriceLabel, strCode);
        $R_FN.setInnerHTML(this._lblCurrency_Price_IPO, strCode);
        $R_FN.setInnerHTML(this._lblCurrency_PriceLabel_IPO, strCode);
    },

    setCurrencyIPO: function (strCode) {
        this._strCurrencyCode = strCode;
        $R_FN.setInnerHTML(this._lblCurrency_Price_IPO, strCode);
        $R_FN.setInnerHTML(this._lblCurrency_PriceLabel_IPO, strCode);
    },
    saveClicked: function () {
        this.allowSaving = true;
        var zeroValue = false;
        var errorMessage = "";
        var ShipInCost = 0, PurchasePrice = 0;
        ShipInCost = $get(this.getFormControlID(this._element.id, 'txtShipInCost')).value;
        PurchasePrice = $get(this.getFormControlID(this._element.id, 'txtPrice')).value;
        if (PurchasePrice == 0) {
            PurchasePrice = $get(this.getFormControlID(this._element.id, 'txtPrice_IPO')).value;
        }
        if ((ShipInCost == 0)) {
            zeroValue = true;
            errorMessage += 'Ship In Cost value is 0. Are you sure want to update?'
        }
        if (PurchasePrice == 0) {
            zeroValue = true;
            if (errorMessage.length > 1) {
                errorMessage += '\nPurchase Price is 0. Are you sure want to update?'
            } else {
                errorMessage += 'Purchase Price is 0. Are you sure want to update?'
            }

        } 

        if (!this.validateForm()) return;

        if (zeroValue == true) {
            if (confirm(errorMessage)) {
                this.allowSaving = true;
            }
            else {
                this.allowSaving = false;
            }
        }

        if (this.allowSaving == true) {

            var strChangedFields = this.filedChangesList("Save");
            this.refreshFieldChanged();

            var obj = new Rebound.GlobalTrader.Site.Data();
            obj.set_PathToData("controls/Nuggets/GILines");
            obj.set_DataObject("GILines");
            obj.set_DataAction("SaveEdit");
            obj.addParameter("id", this._intLineID);
            obj.addParameter("UpdateType", 3);
            obj.addParameter("Location", $get(this.getFormControlID(this._element.id, 'txtLocation')).value);
            obj.addParameter("LotNo", $find(this.getFormControlID(this._element.id, 'ddlLot')).getValue());
            obj.addParameter("QCNotes", $get(this.getFormControlID(this._element.id, 'txtQualityControlNotes')).value);
            obj.addParameter("Quantity", $get(this.getFormControlID(this._element.id, 'txtQuantity')).value);
            obj.addParameter("IsPartNumberCorrect", this.getControlValue(this.getFormControlID(this._element.id, 'chkPartNumberCorrect'), 'CheckBox'));
            obj.addParameter("CorrectPartNo", $get(this.getFormControlID(this._element.id, 'txtCorrectPartNo')).value);
            obj.addParameter("IsManufacturerCorrect", this.getControlValue(this.getFormControlID(this._element.id, 'chkManufacturerCorrect'), 'CheckBox'));
            obj.addParameter("CorrectManufacturerNo", this.getControlValue(this.getFormControlID(this._element.id, 'cmbManufacturer'), 'Combo'));

            obj.addParameter("IsPackageCorrect", this.getControlValue(this.getFormControlID(this._element.id, 'chkPackageCorrect'), 'CheckBox'));
            obj.addParameter("CorrectPackageNo", this.getControlValue(this.getFormControlID(this._element.id, 'cmbPackage'), 'Combo'));
            obj.addParameter("IsMSLCorrect", this.getControlValue(this.getFormControlID(this._element.id, 'chkMSLCorrect'), 'CheckBox'));
            obj.addParameter("CorrectMslNo", $find(this.getFormControlID(this._element.id, 'ddlMsl')).getValue());
            obj.addParameter("HICStatus", $find(this.getFormControlID(this._element.id, 'HICStatus')).getValue());
            obj.addParameter("QueryHICStatus", $find(this.getFormControlID(this._element.id, 'QueryHICStatus')).getValue());

            obj.addParameter("IsRohsStatusCorrect", this.getControlValue(this.getFormControlID(this._element.id, 'chkRohsStatusCorrect'), 'CheckBox'));
            obj.addParameter("CorrectStatusNo", $find(this.getFormControlID(this._element.id, 'ddlROHSStatus')).getValue());
            obj.addParameter("CountryOfManufacture", this.getControlValue(this.getFormControlID(this._element.id, 'cmbCountryOfManufacture'), 'Combo'));
            obj.addParameter("CountingMethodNo", $find(this.getFormControlID(this._element.id, 'ddlCountingMethod')).getValue());
            obj.addParameter("ReqSerailNo", this.getControlValue(this.getFormControlID(this._element.id, 'chkReqSerailNo'), 'CheckBox'));
            obj.addParameter("IsLotCodeReq", this.getControlValue(this.getFormControlID(this._element.id, 'chkLotCodeReq'), 'CheckBox'));

            obj.addParameter("IsEnhancedInpection", $("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_lblHeaderEnhancedInpection").text().toLowerCase() == 'Yes'.toLowerCase() ? true : false); //$find(this.getFormControlID(this._element.id, 'ddlEnhancedInspection')).getValue() > 0 ? true : false); // By Default false due to client need (GR2-131)
            obj.addParameter("EnhInpectionReqId", $find(this.getFormControlID(this._element.id, 'ddlEnhancedInspection')).getValue());
            obj.addParameter("GeneralInspectionNotes", $get(this.getFormControlID(this._element.id, 'txtGeneralInspectionNotes')).value);
            obj.addParameter("IsBakingYes", this.getControlValue(this.getFormControlID(this._element.id, 'chkbakingYes'), 'CheckBox'));
            obj.addParameter("IsBakingNo", this.getControlValue(this.getFormControlID(this._element.id, 'chkbakingNo'), 'CheckBox'));
            obj.addParameter("IsBakingNA", this.getControlValue(this.getFormControlID(this._element.id, 'chkbakingNA'), 'CheckBox'));
            obj.addParameter("IsInspectionConducted", this.getControlValue(this.getFormControlID(this._element.id, 'chkInspectionConducted'), 'CheckBox'));
            obj.addParameter("SupplierPart", $get(this.getFormControlID(this._element.id, 'txtSupplierPart')).value);
            obj.addParameter("ProductNo", this.getControlValue(this.getFormControlID(this._element.id, 'cmbProducts'), 'Combo'));
            obj.addParameter("Price", $get(this.getFormControlID(this._element.id, 'txtPrice')).value);
            obj.addParameter("ShipInCost", $get(this.getFormControlID(this._element.id, 'txtShipInCost')).value);
            obj.addParameter("ClientPrice", $get(this.getFormControlID(this._element.id, 'txtPrice_IPO')).value);
            obj.addParameter("ChangedFields", strChangedFields);
            obj.addParameter("CurrencyNo", this._intCurrencyID);
            obj.addParameter("PartMarkings", $get(this.getFormControlID(this._element.id, 'txtPartMarkings')).value);
            obj.addParameter("AccountNotes", $get(this.getFormControlID(this._element.id, 'txtAccountNotes')).value);
            obj.addParameter("POBankFee", this._poBankFee);
            //[001] start
            obj.addParameter("PreviousDLUP", this._StringDLUP);

            var PackagingBreakdownData = this.getPackagingBreakdownData();
            obj.addParameter("PackBreakDownJSON", JSON.stringify(PackagingBreakdownData));
            obj.addParameter("IsBySendQueryBtn", this._isSendClicked);
            obj.addParameter("IsActeoneTestPass", this.getControlValue(this.getFormControlID(this._element.id, 'chkActeoneTestPass'), 'CheckBox'));
            obj.addParameter("IsActeoneTestFail", this.getControlValue(this.getFormControlID(this._element.id, 'chkActeoneTestFail'), 'CheckBox'));
            obj.addParameter("IsActeoneTestNA", this.getControlValue(this.getFormControlID(this._element.id, 'chkActeoneTestNA'), 'CheckBox'));
            obj.addParameter("ActeoneTest", $get(this.getFormControlID(this._element.id, 'txtActeoneTest')).value);
            obj.addParameter("IsIsoproprylePass", this.getControlValue(this.getFormControlID(this._element.id, 'chkIsoproprylePass'), 'CheckBox'));
            obj.addParameter("IsIsopropryleFail", this.getControlValue(this.getFormControlID(this._element.id, 'chkIsopropryleFail'), 'CheckBox'));
            obj.addParameter("IsIsopropryleNA", this.getControlValue(this.getFormControlID(this._element.id, 'chkIsopropryleNA'), 'CheckBox'));
            obj.addParameter("Isopropryle", $get(this.getFormControlID(this._element.id, 'txtIsopropryle')).value);

            obj.addParameter("QueryBakingLevel", $get(this.getFormControlID(this._element.id, 'txtQueryBakingLevel')).value);
            obj.addParameter("PrintDateCode", $get(this.getFormControlID(this._element.id, 'txtPrintDateCode')).value);
            obj.addParameter("IsPackageBreakDownChanged", GetPackageBreakDownChnage());

            obj.addParameter("HasBarcodeStatusId", $find(this.getFormControlID(this._element.id, 'ddlGILineBarcodesStatus')).getValue());
            obj.addParameter("BarcodeRemarks", $("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_txtBarcodeRemarks").val());
            obj.addParameter("PartNoQuery", $("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_txtQueryPartNo").val());
            obj.addParameter("ManufacturerQuery", $("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_txtQueryManfacturer").val());
            obj.addParameter("PackagingTypeQuery", $("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_txtPackageTypeQuery").val());
            obj.addParameter("MslQuery", $("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_txtMslQuery").val());
            obj.addParameter("RohsQuery", $("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_txtRohsQuery").val());

            obj.addParameter("GeneralInspectionQuery", this.getControlValue(this.getFormControlID(this._element.id, 'ChkGeneralInspectionNote'), 'CheckBox'));

            obj.addDataOK(Function.createDelegate(this, this.saveEditComplete));
            obj.addError(Function.createDelegate(this, this.saveEditError));
            obj.addTimeout(Function.createDelegate(this, this.saveEditError));
            $R_DQ.addToQueue(obj);
            $R_DQ.processQueue();
            obj = null;
        }
        else {
            this.showError(true);
            $("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_pnlValidateError").hide();
        }
        
    },

    saveEditError: function (args) {
        this._strErrorMessage = args._errorMessage;
        this.onSaveError();
    },

    saveEditComplete: function (args) {
        if (args._result.Result == true) {
            this.showSaving(false);
            if (IsEditClick == true) {
                this.showSavedOK(true);
                this.onSaveComplete();
            }
            else {
                this.RefreshAllQueryMessage();
            }
            this._FieldChanges = this._RefreshFieldChanges;
            $R_IBTN.enableButton(this._ibtnSave, false);
            $R_IBTN.enableButton(this._ibtnSave_Footer, false);
            $R_IBTN.enableButton(this._ibtnSendQuery, false);
            $("#ctl00_cphMain_ctlLines_ctlDB_ctl18_ibtnSendQuery_lblDisabled").removeClass();
            $("#ctl00_cphMain_ctlLines_ctlDB_ctl18_ibtnSendQuery_hyp").removeClass();
            $("#ctl00_cphMain_ctlLines_ctlDB_ctl18_ibtnSendQuery_lblDisabled").addClass("iconButton iconButton_Nugget_Disabled iconButton_Nugget_Save_Disabled iconButton_alignLeft");
            $("#ctl00_cphMain_ctlLines_ctlDB_ctl18_ibtnSendQuery_hyp").addClass("iconButton iconButton_Nugget iconButton_Nugget_Save iconButton_alignLeft invisible");
            SetSaveValue(true);
        } else {
            this._strErrorMessage = args._errorMessage;
            this.onSaveError();
        }
        SetPackageBreakDownChnage();
    },

    validateForm: function () {
        $("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_pnlValidateError").show();
        this.onValidate();
        var blnOK = true;
        if (!this.checkControlEditEntered(this.getFormControlID(this._element.id, 'txtLocation'), 'TextBox')) { blnOK = false; }
        else if (!this.checkControlEditEntered(this.getFormControlID(this._element.id, 'cmbProducts'), 'Combo')) { blnOK = false; }
        else if (!this.checkControlEditEntered(this.getFormControlID(this._element.id, 'txtShipInCost'), 'TextBox')) { blnOK = false; }
        else if (!this.checkControlEditEntered(this.getFormControlID(this._element.id, 'txtQuantity'), 'TextBox')) { blnOK = false; }
        else if (this.getControlValue(this.getFormControlID(this._element.id, 'chkInspectionConducted'), 'CheckBox') == false) {
            blnOK = false;
            $("#EnhancedInspectiontd1").css("background-color", "darkred");
            $("#EnhancedInspectiontd2").css("background-color", "darkred");
        }
        else {
            blnOK = true;
            $("#EnhancedInspectiontd1").css("background-color", "#6aa363");
            $("#EnhancedInspectiontd2").css("background-color", "#6aa363");
        }
        //[002] start calling ValidateBarscan function 
        if (!this.ValidateBarscan()) {
            blnOK = false;
        }
        //[002] end
        if (!this.ValidatePackageBreakDown()) blnOK = false;
        if (!blnOK) this.showError(true);
        if (!blnOK) $('#aGIScreenInfo').click();
        return blnOK;
    },
    //[002] start (validation for Barcode Scan on GI Edit page)
    ValidateBarscan: function () {
       
        this.BarcodeScannedChanged(); //[002] check existing status of barcodes scanned dropdown;
 
        var selectedBarCodeVal = $("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_ddlGILineBarcodesStatus_ddl option:selected").val();
        //if (selectedBarCodeVal == 0) {
        //    $("#barcodeRemarError").addClass("error-highlight");
        //    return false;
        //}
        if (this._blnBarcodeRemarkMandatory) {
 
            if ($("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_txtBarcodeRemarks").val().trim().length <= 0) {
                $("#barcodeRemarError").addClass("error-highlight");
                return false;
            }
            else {
                $("#barcodeRemarError").removeClass("error-highlight");
                return true;
            }
        }
        $("#barcodeRemarError").removeClass("error-highlight");
        return true;

    },
    //[002] end
    checkControlEditEntered: function (strControlID, strControlType) {
        var blnEntered = true;

        switch (strControlType) {
            case "TextBox": blnEntered = $R_FN.isEntered($get(strControlID).value); break;
            case "DropDown": blnEntered = !$find(strControlID).isSetAsNoValue(); break;
            case "FileUpload": blnEntered = $find(strControlID).checkEntered(); break;
            case "Combo": blnEntered = $find(strControlID).checkEntered(); break;
        }
        if (strControlID == (this.getFormControlID(this._element.id, 'txtShipInCost')) && $get(this.getFormControlID(this._element.id, 'txtShipInCost')).value < '0.00000') {
            blnEntered = false
        }
        if (!blnEntered) {
            this.setControleditInError(strControlID, true, $R_RES.RequiredFieldMissingMessage);
        }
        else {
            document.getElementById(strControlID).style.border = '';
            if (strControlID == "ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_ddlUpdateType" && blnEntered == true) {
                document.getElementById("GoodsInUpdateTypeError").style.backgroundColor = "";
                document.getElementById("GoodsInUpdateTypeError1").style.backgroundColor = "";
            }
            if (strControlID == "ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_txtLocation" && blnEntered == true) {
                document.getElementById("LocationError").style.backgroundColor = "";
                document.getElementById("LocationError1").style.backgroundColor = "";
            }
            if (strControlID == (this.getFormControlID(this._element.id, 'cmbProducts')) && blnEntered == true) {
                document.getElementById('ProductsError').style.backgroundColor = "";
                document.getElementById('ProductsError1').style.backgroundColor = "";
            }
            if (strControlID == (this.getFormControlID(this._element.id, 'txtShipInCost')) && blnEntered == true) {
                document.getElementById('ShipInCostError').style.backgroundColor = "";
                document.getElementById('ShipInCostError1').style.backgroundColor = "";
            }
            //if (strControlID == (this.getFormControlID(this._element.id, 'txtCorrectPartNo')) && blnEntered == true) {
            //    document.getElementById('CorrectPartNoError').style.backgroundColor = "";
            //    document.getElementById('CorrectPartNoError1').style.backgroundColor = "";
            //}
            //if (strControlID == (this.getFormControlID(this._element.id, 'ddlMsl')) && blnEntered == true) {
            //    document.getElementById('CorrectMSLError').style.backgroundColor = "";
            //    document.getElementById('CorrectMSLError1').style.backgroundColor = "";
            //}
            if (strControlID == (this.getFormControlID(this._element.id, 'txtQuantity')) && blnEntered == true) {
                document.getElementById('QuantityError').style.backgroundColor = "";
                document.getElementById('QuantityError1').style.backgroundColor = "";
            }
        }

        return blnEntered;
    },
    setControleditInError: function (strControlID, blnInError, strMessage) {
        if (blnInError) {

            document.getElementById(strControlID).focus();
            if (strControlID == "ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_ddlUpdateType" && blnInError == true) {
                document.getElementById("GoodsInUpdateTypeError").style.backgroundColor = "#990000";
                document.getElementById("GoodsInUpdateTypeError1").style.backgroundColor = "#990000";
            }
            if (strControlID == "ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_txtLocation" && blnInError == true) {
                document.getElementById("LocationError").style.backgroundColor = "#990000";
                document.getElementById("LocationError1").style.backgroundColor = "#990000";
            }
            if (strControlID == (this.getFormControlID(this._element.id, 'cmbProducts')) && blnInError == true) {
                document.getElementById('ProductsError').style.backgroundColor = "#990000";
                document.getElementById('ProductsError1').style.backgroundColor = "#990000";
            }
            if (strControlID == (this.getFormControlID(this._element.id, 'txtShipInCost')) && blnInError == true) {
                document.getElementById('ShipInCostError').style.backgroundColor = "#990000";
                document.getElementById('ShipInCostError1').style.backgroundColor = "#990000";
            }
            //if (strControlID == (this.getFormControlID(this._element.id, 'txtCorrectPartNo')) && blnInError == true) {
            //    document.getElementById('CorrectPartNoError').style.backgroundColor = "#990000";
            //    document.getElementById('CorrectPartNoError1').style.backgroundColor = "#990000";
            //}
            //if (strControlID == (this.getFormControlID(this._element.id, 'ddlMsl')) && blnInError == true) {
            //    document.getElementById('CorrectMSLError').style.backgroundColor = "#990000";
            //    document.getElementById('CorrectMSLError1').style.backgroundColor = "#990000";
            //}
            if (strControlID == (this.getFormControlID(this._element.id, 'txtQuantity')) && blnInError == true) {
                document.getElementById('QuantityError').style.backgroundColor = "#990000";
                document.getElementById('QuantityError1').style.backgroundColor = "#990000";
            }

        } else {
            document.getElementById(strControlID).style.border = '';

        }

    },
    showHideQuantity: function (bln) {

    },
    productChange: function () {
        $('#' + (this.getFormControlID(this._element.id, 'chkPrintHazWar'))).prop('checked', false);
        $find((this.getFormControlID(this._element.id, 'chkPrintHazWar'))).enableButton($find(this.getFormControlID(this._element.id, 'cmbProducts'))._aut._varSelectedExtraData);
    },
    showMessage: function (bln, strText) {
        $R_FN.showElement(this._pnlValidateError, bln);
        if (bln) {
            $R_FN.showElement(this._pnlSaving, false);
            $R_FN.showElement(this._pnlSavedOK, false);
            $R_FN.showElement(this._pnlLoading, false);
            $R_FN.showElement(this._pnlContentInner, true);
            $R_FN.showElement(this._pnlLinksHolder, true);
            $R_FN.showElement(this._pnlFooterLinksHolder, true);
            if (this._ctlRelatedNugget) {
                this._ctlRelatedNugget.control.showLoading(false);
                this._ctlRelatedNugget.control.showRefresh(true);
            }
            if (!strText) strText = "";
            this._pnlValidateErrorText.innerHTML = strText;
        }
    },
    stepChanged: function () {

    },
    chooseIfSendMail: function () {
        this.showMailButtons();
    },
    showMailButtons: function () {
        var bln = this.getFieldValue("ctlSendMail");

        if (bln == false) {
            $('.mailbody').hide();
        }
        else {
            $('.mailbody').show();
        }
    },
    getFormControlID: function (ParentId, controlID) {

        var str = "";
        str = String.format("{0}_{1}", ParentId, controlID);
        return str;
    },

    CorrectPartNo: function () {
        var chkPartNumberCorrect = this.getControlValue(this.getFormControlID(this._element.id, 'chkPartNumberCorrect'), 'CheckBox');

        if (chkPartNumberCorrect == true) {
            $get(this.getFormControlID(this._element.id, 'txtCorrectPartNo')).value = $('#' + (this.getFormControlID(this._element.id, 'lblPartNo'))).text();
            $('#' + (this.getFormControlID(this._element.id, 'txtCorrectPartNo'))).prop('disabled', true);
        }
        else {
            $('#' + (this.getFormControlID(this._element.id, 'txtCorrectPartNo'))).prop('disabled', false);
            $get(this.getFormControlID(this._element.id, 'txtCorrectPartNo')).value = "";
        }
    },
    ManufacturerCorrect: function () {
        var chkManufacturerCorrect = this.getControlValue(this.getFormControlID(this._element.id, 'chkManufacturerCorrect'), 'CheckBox');
        if (chkManufacturerCorrect == true) {
            $find(this.getFormControlID(this._element.id, 'cmbManufacturer')).setValue(this._intPOManufacturerId, $('#' + (this.getFormControlID(this._element.id, 'lblManufacturer'))).text());
            $('#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_cmbManufactureraut_ctl04').css("display", "none");
        }
        else {
            $find(this.getFormControlID(this._element.id, 'cmbManufacturer')).setValue(0, "");
            $('#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_cmbManufactureraut_ctl04').css("display", "");
            $('#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_cmbManufacturertxt').removeAttr("disabled")
        }
    },

    PackageCorrect: function () {
        var chkPackageCorrect = this.getControlValue(this.getFormControlID(this._element.id, 'chkPackageCorrect'), 'CheckBox');
        if (chkPackageCorrect == true) {
            $find(this.getFormControlID(this._element.id, 'cmbPackage')).setValue(this._intPOPackagingNo, $('#' + (this.getFormControlID(this._element.id, 'lblPackage'))).text());
            $('#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_cmbPackageaut_ctl04').css("display", "none");
        }
        else {
            $find(this.getFormControlID(this._element.id, 'cmbPackage')).setValue(0, "");
            $('#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_cmbPackageaut_ctl04').css("display", "block");
            $('#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_cmbPackagetxt').removeAttr("disabled")
        }
    },
    MSLCorrect: function () {
        var chkMSLCorrect = this.getControlValue(this.getFormControlID(this._element.id, 'chkMSLCorrect'), 'CheckBox');
        if (chkMSLCorrect == true) {
            $find(this.getFormControlID(this._element.id, 'ddlMsl')).setValue(this._POMSL);
            $('#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_ddlMsl_ddl').prop('disabled', true);
            $('#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_ddlMsl_ctl02').css("display", "none");
        }
        else {
            $find(this.getFormControlID(this._element.id, 'ddlMsl')).setValue(-1);
            $('#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_ddlMsl_ddl').prop('disabled', false);
            $('#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_ddlMsl_ctl02').css("display", "");
        }
    },
    HICCorrect: function () {
        var chkHICCorrect = this.getControlValue(this.getFormControlID(this._element.id, 'chkHICCorrect'), 'CheckBox');
        if (chkHICCorrect == true) {
            // $('#' + (this.getFormControlID(this._element.id, 'txtCorrectHIC'))).prop('disabled', true);
            $('#' + (this.getFormControlID(this._element.id, 'QueryHICStatus'))).prop('disabled', true);
        }
        else {
            // $('#' + (this.getFormControlID(this._element.id, 'txtCorrectHIC'))).prop('disabled', false);
            $('#' + (this.getFormControlID(this._element.id, 'QueryHICStatus'))).prop('disabled', false);
        }
    },
    RohsStatusCorrect: function () {
        var chkRohsStatusCorrect = this.getControlValue(this.getFormControlID(this._element.id, 'chkRohsStatusCorrect'), 'CheckBox');
        if (chkRohsStatusCorrect == true) {
            $find(this.getFormControlID(this._element.id, 'ddlROHSStatus')).setValue(this._intPOROHSNo);
            $('#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_ddlROHSStatus_ddl').prop('disabled', true);
            $('#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_ddlROHSStatus_ctl02').css("display", "none");
        }
        else {
            $find(this.getFormControlID(this._element.id, 'ddlROHSStatus')).setValue(-1);
            $('#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_ddlROHSStatus_ddl').prop('disabled', false);
            $('#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_ddlROHSStatus_ctl02').css("display", "");
        }
    },
    BakingLabelAdded: function () {
        var chkbakingYes = this.getControlValue(this.getFormControlID(this._element.id, 'chkbakingYes'), 'CheckBox');
        if (chkbakingYes == true) {
            $find(this.getFormControlID(this._element.id, 'chkbakingNo')).setChecked(false);
            $find(this.getFormControlID(this._element.id, 'chkbakingNA')).setChecked(false);
        }

    },
    BakingLabelAddedNo: function () {
        var chkbakingYes = this.getControlValue(this.getFormControlID(this._element.id, 'chkbakingYes'), 'CheckBox');
        var chkbakingNo = this.getControlValue(this.getFormControlID(this._element.id, 'chkbakingNo'), 'CheckBox');
        if (chkbakingNo == true) {
            $find(this.getFormControlID(this._element.id, 'chkbakingYes')).setChecked(false);
            $find(this.getFormControlID(this._element.id, 'chkbakingNA')).setChecked(false);
        }

    },
    BakingLabelAddedNA: function () {
        var chkbakingNA = this.getControlValue(this.getFormControlID(this._element.id, 'chkbakingNA'), 'CheckBox');
        if (chkbakingNA == true) {
            $find(this.getFormControlID(this._element.id, 'chkbakingYes')).setChecked(false);
            $find(this.getFormControlID(this._element.id, 'chkbakingNo')).setChecked(false);
        }
    },

    //[002] start
    BarcodeScannedChanged: function () {
        var result = 0;
        var barcodedata = $("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_txtBarcodeRemarks").val();


        if (this._blnFirstTimePageLoad == true) {
            this._blnFirstTimePageLoad = false;

            result = $("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_ddlGILineBarcodesStatus_ddl option:selected").val();
            
            var intervalforBarcodeDropdown = setInterval(function () {
                var counter = $("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_ddlGILineBarcodesStatus_ddl option").length;
                
                        if (result.indexOf('loading') !== -1 || counter <=1) {
                            result = $("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_ddlGILineBarcodesStatus_ddl option:selected").val();
                            this._tempBarcodeRemark = $("#ctl00_cphMain_ctlLines_ctlDB_ctl13_ctlBarcodeScanRemark_lbl").html().replace(/<br\s*[\/]?>/gi, "\n");
                            $("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_txtBarcodeRemarks").attr("readonly", "readonly");
                            $("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_txtBarcodeRemarks").val('Loading...');
                            //console.log('timer running');
                            //console.log(result);
                        }

                        else {
                            //console.log('timer finieshed')
                            //console.log(result);
                             
                                clearInterval(intervalforBarcodeDropdown);
                                result = $("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_ddlGILineBarcodesStatus_ddl option:selected").val();
                                $("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_txtBarcodeRemarks").removeAttr("readonly", "readonly")
                                //console.log('after time out in else condition');
                                //console.log(result);
                                if (result == 2) {

                                    this._blnBarcodeRemarkMandatory = true;
                                    $("#BarcodeMandatory").removeClass("invisible");


                                    $("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_txtBarcodeRemarks").val(barcodedata);

                                    if ($("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_ddlGILineBarcodesStatus_ddl").attr('disabled') == 'disabled') {
                                        $("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_txtBarcodeRemarks").addProp("disabled");
                                    }
                                    else {
                                        $("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_txtBarcodeRemarks").removeProp("disabled");
                                    }
                                    $("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_txtBarcodeRemarks").prop("placeholder", "Remarks field is mandatory!");
                                }
                                else {
                                    this._blnBarcodeRemarkMandatory = false;

                                    $("#BarcodeMandatory").addClass("invisible");
                                    //this._tempBarcodeRemark = $("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_txtBarcodeRemarks").val();

                                    $("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_txtBarcodeRemarks").val("");
                                    $("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_txtBarcodeRemarks").prop("disabled", true);
                                    $("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_txtBarcodeRemarks").prop("placeholder", "");
                                } 
                            
                        }
                    }, 4000);
        } else {
            result = $("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_ddlGILineBarcodesStatus_ddl option:selected").val();
            var resultText = $("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_ddlGILineBarcodesStatus_ddl option:selected").text();

            if (result == 2) {
                this._blnBarcodeRemarkMandatory = true;
                this._tempBarcodeRemark = $("#ctl00_cphMain_ctlLines_ctlDB_ctl13_ctlBarcodeScanRemark_lbl").html().replace(/<br\s*[\/]?>/gi, "\n");
                
                $("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_txtBarcodeRemarks").val(barcodedata);
                $("#BarcodeMandatory").removeClass("invisible");
                $("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_txtBarcodeRemarks").removeProp("disabled");
                $("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_txtBarcodeRemarks").prop("placeholder", "Remarks field is mandatory!");
            }
            else {
                
                this._blnBarcodeRemarkMandatory = false;
                $("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_txtBarcodeRemarks").val("");
                $("#BarcodeMandatory").addClass("invisible");
                $("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_txtBarcodeRemarks").prop("disabled", true);
                $("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_txtBarcodeRemarks").prop("placeholder", "");
            }
        }

    },
    //[002] end


    finishedForm: function () {
        this._ctlMultiStep.showExplainLabel(false);
        this._ctlMultiStep.showSteps(false);
        $R_IBTN.showButton(this._ibtnSave, false);
        $R_IBTN.showButton(this._ibtnSave_Footer, false);
        this.showSavedOK(true);
        this.onSaveComplete();
    },
    getQueryData: function () {
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/GILines");
        obj.set_DataObject("GILines");
        obj.set_DataAction("GetGIQueryData");
        obj.addParameter("GoodsInLineId", this._intLineID);
        obj.addDataOK(Function.createDelegate(this, this.getQueryDataOK));
        obj.addError(Function.createDelegate(this, this.getQueryDataError));
        obj.addTimeout(Function.createDelegate(this, this.getQueryDataError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },
    getQueryDataOK: function (args) {
        var res = args._result;
        $("#dvGIAllQueries").html($R_FN.setCleanTextValue(res.GIQuery));
    },
    getQueryDataError: function (args) {
        this.showError(true, args.get_ErrorMessage());
    },
    sendClicked: function () {
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/GILines");
        obj.set_DataObject("GILines");
        obj.set_DataAction("NotifyGIQuery");
        obj.addParameter("GoodsInLineId", this._intLineID);
        obj.addParameter("IsSales", this.getControlValue(this.getFormControlID(this._element.id, 'chkSales'), 'CheckBox'));
        obj.addParameter("IsPurchasing", this.getControlValue(this.getFormControlID(this._element.id, 'chkPurchasing'), 'CheckBox'));
        obj.addParameter("IsQualityApproval", this.getControlValue(this.getFormControlID(this._element.id, 'chkQualityApproval'), 'CheckBox'));
        obj.addParameter("Query", document.getElementById("dvGIAllQueries").innerHTML);
        obj.addParameter("GoodsInId", this._intGIID);
        obj.addParameter("GoodsInNumber", this._intGoodsInNumber);
        obj.addDataOK(Function.createDelegate(this, this.sendComplete));
        obj.addError(Function.createDelegate(this, this.sendError));
        obj.addTimeout(Function.createDelegate(this, this.sendError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },
    sendError: function (args) {
        this._strErrorMessage = args._errorMessage;
        this.onSaveError();
    },
    sendComplete: function (args) {
        if (args._result.Result == true) {
            this.showSavedOK(true);
            this.onSaveComplete();

        } else {
            this._strErrorMessage = args._errorMessage;
            this.onSaveError();
        }

    },
    filedChangesList: function (Type) {
        var IsLandedCostUpdated = false;
        var strChangedFields = "";
        if (eval("this._FieldChanges.txtCorrectPartNo") != $get(this.getFormControlID(this._element.id, 'txtCorrectPartNo')).value) {
            if (strChangedFields != "") strChangedFields += "||";
            strChangedFields += "CorrectPartNo";
        }
        if (eval("this._FieldChanges.ddlROHSStatus") == '0' && $find(this.getFormControlID(this._element.id, 'ddlROHSStatus')).getValue() != '0')
            this._FieldChanges["ddlROHSStatus"] = '';
        if (eval("this._FieldChanges.ddlROHSStatus") != ($find(this.getFormControlID(this._element.id, 'ddlROHSStatus')).getValue() == null ? "" : $find(this.getFormControlID(this._element.id, 'ddlROHSStatus')).getValue())) {
            if (strChangedFields != "") strChangedFields += "||";
            strChangedFields += "CorrectRohsStatus";
        }
        if (eval("this._FieldChanges.txtLocation") != $get(this.getFormControlID(this._element.id, 'txtLocation')).value) {
            if (strChangedFields != "") strChangedFields += "||";
            strChangedFields += "Location";
        }
        if (eval("this._FieldChanges.ddlLot") == '0')
            this._FieldChanges["ddlLot"] = '';
        if (eval("this._FieldChanges.ddlLot") != ($find(this.getFormControlID(this._element.id, 'ddlLot')).getValue() == null ? "" : $find(this.getFormControlID(this._element.id, 'ddlLot')).getValue())) {
            if (strChangedFields != "") strChangedFields += "||";
            strChangedFields += "Lot";
        }
        if (eval("this._FieldChanges.txtSupplierPart") != $get(this.getFormControlID(this._element.id, 'txtSupplierPart')).value) {
            if (strChangedFields != "") strChangedFields += "||";
            strChangedFields += "SupplierPartNo";
        }
        if (eval("this._FieldChanges.cmbProducts") == '0')
            this._FieldChanges["cmbProducts"] = '';
        if (eval("this._FieldChanges.cmbProducts") != (this.getControlValue(this.getFormControlID(this._element.id, 'cmbProducts'), 'Combo') == null ? '' : this.getControlValue(this.getFormControlID(this._element.id, 'cmbProducts'), 'Combo'))) {
            if (strChangedFields != "") strChangedFields += "||";
            strChangedFields += "Product";
            IsLandedCostUpdated = true;
        }
        if (eval("this._FieldChanges.cmbPackage") == '0')
            this._FieldChanges["cmbPackage"] = '';
        if (eval("this._FieldChanges.cmbPackage") != (this.getControlValue(this.getFormControlID(this._element.id, 'cmbPackage'), 'Combo') == null ? '' : this.getControlValue(this.getFormControlID(this._element.id, 'cmbPackage'), 'Combo'))) {
            if (strChangedFields != "") strChangedFields += "||";
            strChangedFields += "CorrectPackage";
        }
        if (eval("this._FieldChanges.cmbManufacturer") == '0')
            this._FieldChanges["cmbManufacturer"] = '';
        if (eval("this._FieldChanges.cmbManufacturer") != (this.getControlValue(this.getFormControlID(this._element.id, 'cmbManufacturer'), 'Combo') == null ? '' : this.getControlValue(this.getFormControlID(this._element.id, 'cmbManufacturer'), 'Combo'))) {
            if (strChangedFields != "") strChangedFields += "||";
            strChangedFields += "CorrectManufacturer";
        }
        if (eval("this._FieldChanges.cmbCountryOfManufacture") == '0')
            this._FieldChanges["cmbCountryOfManufacture"] = '';
        if (eval("this._FieldChanges.cmbCountryOfManufacture") != (this.getControlValue(this.getFormControlID(this._element.id, 'cmbCountryOfManufacture'), 'Combo') == null ? '' : this.getControlValue(this.getFormControlID(this._element.id, 'cmbCountryOfManufacture'), 'Combo'))) {
            if (strChangedFields != "") strChangedFields += "||";
            strChangedFields += "countryOforigin";
        }
        if (eval("this._FieldChanges.txtShipInCost") != $get(this.getFormControlID(this._element.id, 'txtShipInCost')).value) {
            if (strChangedFields != "") strChangedFields += "||";
            strChangedFields += "ShipInCost";
            IsLandedCostUpdated = true;
        }
        this._FieldChanges["txtQualityControlNotes"] = eval("this._FieldChanges.txtQualityControlNotes").replace(/<br\s*[\/]?>/gi, "\n");
        if (eval("this._FieldChanges.txtQualityControlNotes") != $get(this.getFormControlID(this._element.id, 'txtQualityControlNotes')).value) {
            if (strChangedFields != "") strChangedFields += "||";
            strChangedFields += "InsToQualityControlNotes";
        }
        if (eval("this._FieldChanges.ddlCountingMethod") == '0')
            this._FieldChanges["ddlCountingMethod"] = '';
        if (eval("this._FieldChanges.ddlCountingMethod") != ($find(this.getFormControlID(this._element.id, 'ddlCountingMethod')).getValue() == null ? "" : $find(this.getFormControlID(this._element.id, 'ddlCountingMethod')).getValue())) {
            if (strChangedFields != "") strChangedFields += "||";
            strChangedFields += "CountingMethod";
        }
        if (eval("this._FieldChanges.txtPartMarkings") != $get(this.getFormControlID(this._element.id, 'txtPartMarkings')).value) {
            if (strChangedFields != "") strChangedFields += "||";
            strChangedFields += "BatchReference";
        }

        if (eval("this._FieldChanges.txtPrice") != $get(this.getFormControlID(this._element.id, 'txtPrice')).value) {
            if (strChangedFields != "") strChangedFields += "||";
            strChangedFields += "PurchasePrice";
            IsLandedCostUpdated = true;
        }
        if (eval("this._FieldChanges.txtPrice_IPO") != $get(this.getFormControlID(this._element.id, 'txtPrice_IPO')).value) {
            if (strChangedFields != "") strChangedFields += "||";
            strChangedFields += "PurchasePrice";
            IsLandedCostUpdated = true;
        }
        if (eval("this._FieldChanges.ddlMsl") == '0')
            this._FieldChanges["ddlMsl"] = '';
        if (eval("this._FieldChanges.ddlMsl") != ($find(this.getFormControlID(this._element.id, 'ddlMsl')).getValue() == null ? "" : $find(this.getFormControlID(this._element.id, 'ddlMsl')).getValue())) {
            if (strChangedFields != "") strChangedFields += "||";
            strChangedFields += "CorrectMSL";
        }
        if (eval("this._FieldChanges.QueryHICStatus") != (($find(this.getFormControlID(this._element.id, 'QueryHICStatus')).getValue() == null ? "" : $find(this.getFormControlID(this._element.id, 'QueryHICStatus')).getValue()))) {
            if (strChangedFields != "") strChangedFields += "||";
            strChangedFields += "CorrectHIC";
        }
        //if (eval("this._FieldChanges.txtPackageBreakdownInfo") != $get(this.getFormControlID(this._element.id, 'txtPackageBreakdownInfo')).value) {
        //    if (strChangedFields != "") strChangedFields += "||";
        //    strChangedFields += "PackageBreakdownInfo";
        //}
        if (eval("this._FieldChanges.txtQuantity") != $get(this.getFormControlID(this._element.id, 'txtQuantity')).value) {
            if (strChangedFields != "") strChangedFields += "||";
            strChangedFields += "Quantity";
        }
        if (IsLandedCostUpdated == true) {
            strChangedFields += "||LandedCost";
        }
        if (Type == "SendQuery") {
            if (eval("this._FieldChanges.txtActeoneTest") != $get(this.getFormControlID(this._element.id, 'txtActeoneTest')).value) {
                if (strChangedFields != "") strChangedFields += "||";
                strChangedFields += "ActeoneTest";
            }
            if (eval("this._FieldChanges.txtIsopropryle") != $get(this.getFormControlID(this._element.id, 'txtIsopropryle')).value) {
                if (strChangedFields != "") strChangedFields += "||";
                strChangedFields += "Isopropryle";
            }
            if ($R_FN.setCleanTextValue(eval("this._FieldChanges.txtGeneralInspectionNotes")).trim().replace(/<br\s*[\/]?>/gi, "\n") != ($R_FN.setCleanTextValue($get(this.getFormControlID(this._element.id, 'txtGeneralInspectionNotes')).value)).trim().replace(/<br\s*[\/]?>/gi, "\n")) {
                if (strChangedFields != "") strChangedFields += "||";
                strChangedFields += "GeneralInspectionNotes";
            }
            //if (eval("this._FieldChanges.chkEnhancedInpectionRequired") != (this.getControlValue(this.getFormControlID(this._element.id, 'chkEnhancedInpectionRequired'), 'CheckBox') == true ? "true" : "false")) {
            //    if (strChangedFields != "") strChangedFields += "||";
            //    strChangedFields += "EnhancedInpectionRequired";
            //}
            if (eval("this._FieldChanges.ddlEnhancedInspection") == '0')
                this._FieldChanges["ddlEnhancedInspection"] = '';
            if (eval("this._FieldChanges.ddlEnhancedInspection") != ($find(this.getFormControlID(this._element.id, 'ddlEnhancedInspection')).getValue() == null ? "" : $find(this.getFormControlID(this._element.id, 'ddlEnhancedInspection')).getValue())) {
                if (strChangedFields != "") strChangedFields += "||";
                strChangedFields += "EnhancedInspection";
            }
            if (eval("this._FieldChanges.chkLotCodeReq") != (this.getControlValue(this.getFormControlID(this._element.id, 'chkLotCodeReq'), 'CheckBox') == true ? "true" : "false")) {
                if (strChangedFields != "") strChangedFields += "||";
                strChangedFields += "LotCodeReq";
            }
            if (eval("this._FieldChanges.chkReqSerailNo") != (this.getControlValue(this.getFormControlID(this._element.id, 'chkReqSerailNo'), 'CheckBox') == true ? true : false)) {
                if (strChangedFields != "") strChangedFields += "||";
                strChangedFields += "ReqSerailNo";
            }
            if (eval("this._FieldChanges.chkInspectionConducted") != (this.getControlValue(this.getFormControlID(this._element.id, 'chkInspectionConducted'), 'CheckBox') == true ? "true" : "false")) {
                if (strChangedFields != "") strChangedFields += "||";
                strChangedFields += "InspectionConducted";
            }
            if (eval("this._FieldChanges.HICStatus") != ($find(this.getFormControlID(this._element.id, 'HICStatus')).getValue() == null ? "" : $find(this.getFormControlID(this._element.id, 'HICStatus')).getValue())) {
                if (strChangedFields != "") strChangedFields += "||";
                strChangedFields += "HICStatus";
            }
            if (eval("this._FieldChanges.chkbaking") != (this.getControlValue(this.getFormControlID(this._element.id, 'chkbakingYes'), 'CheckBox') == true ? "1" : this.getControlValue(this.getFormControlID(this._element.id, 'chkbakingNo'), 'CheckBox') == true ? "2" : this.getControlValue(this.getFormControlID(this._element.id, 'chkbakingNA'), 'CheckBox') == true ? "3" : '-1')) {
                if (strChangedFields != "") strChangedFields += "||";
                strChangedFields += "chkbaking";
            }
            if (eval("this._FieldChanges.ActeoneTestStatus") != (this.getControlValue(this.getFormControlID(this._element.id, 'chkActeoneTestPass'), 'CheckBox') == true ? "1" : this.getControlValue(this.getFormControlID(this._element.id, 'chkActeoneTestFail'), 'CheckBox') == true ? "2" : this.getControlValue(this.getFormControlID(this._element.id, 'chkActeoneTestNA'), 'CheckBox') == true ? "3" : "-1")) {
                if (strChangedFields != "") strChangedFields += "||";
                strChangedFields += "ActeoneTest";
            }
            if (eval("this._FieldChanges.IsopropryleStatus") != (this.getControlValue(this.getFormControlID(this._element.id, 'chkIsoproprylePass'), 'CheckBox') == true ? "1" : this.getControlValue(this.getFormControlID(this._element.id, 'chkIsopropryleFail'), 'CheckBox') == true ? "2" : this.getControlValue(this.getFormControlID(this._element.id, 'chkIsopropryleNA'), 'CheckBox') == true ? "3" : "-1")) {
                if (strChangedFields != "") strChangedFields += "||";
                strChangedFields += "Isopropryle";
            }
            var PackagingBreakdownData = this.validatePackagingBreakdownData();
            var PackageJSON = (JSON.stringify(PackagingBreakdownData) == '[]' ? undefined : JSON.stringify(PackagingBreakdownData));
            var OldJSON = eval("this._FieldChanges.PackBreakDownJSON");
            if (OldJSON == '[]') {
                OldJSON = undefined;
            }
            if (OldJSON != PackageJSON) {
                if (strChangedFields != "") strChangedFields += "||";
                strChangedFields += "PackingBreakDown";
            }
            if (eval("this._FieldChanges.txtQueryBakingLevel") != $get(this.getFormControlID(this._element.id, 'txtQueryBakingLevel')).value) {
                if (strChangedFields != "") strChangedFields += "||";
                strChangedFields += "QueryBakingLevel";
            }
            if (eval("this._FieldChanges.txtPrintDateCode") != $get(this.getFormControlID(this._element.id, 'txtPrintDateCode')).value) {
                if (strChangedFields != "") strChangedFields += "||";
                strChangedFields += "PrintDateCode";
            }
            if (eval("this._FieldChanges.txtQuantity") != $get(this.getFormControlID(this._element.id, 'txtQuantity')).value) {
                if (strChangedFields != "") strChangedFields += "||";
                strChangedFields += "Quantity";
            }
            if (eval("this._FieldChanges.txtAccountNotes") != $get(this.getFormControlID(this._element.id, 'txtAccountNotes')).value) {
                if (strChangedFields != "") strChangedFields += "||";
                strChangedFields += "AccountNotes";
            }
            //[002] start
            if (eval("this._FieldChanges.ddlGILineBarcodesStatus") != $get(this.getFormControlID(this._element.id, 'ddlGILineBarcodesStatus')).value) {
                if (strChangedFields != "") strChangedFields += "||";
                strChangedFields += "GILineBarcodesStatus";
            }
            //[002] end
        }
        return strChangedFields;
    },
    RefreshAllQueryMessage: function () {
        var obj = new Rebound.GlobalTrader.Site.Data();

        obj.set_PathToData("controls/Nuggets/GILines");
        obj.set_DataObject("GILines");
        obj.set_DataAction("GetGILineQueryMessage");
        obj.addParameter("GoodsInId", this._intGIID);
        obj.addParameter("GoodsInLineId", this._intLineID);
        obj.addDataOK(Function.createDelegate(this, this.RefreshAllQueryMessageOK));
        obj.addError(Function.createDelegate(this, this.RefreshAllQueryMessageOK));
        obj.addTimeout(Function.createDelegate(this, this.RefreshAllQueryMessageError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },
    RefreshAllQueryMessageError: function (args) {

    },
    RefreshAllQueryMessageOK: function (args) {
        this._invoiceExist = false;
        this._countLotNo = 0;
        res = args._result;
        var InitailQueryMessageDetails = "";
        var ApprovalQueryMessageDetails = "";
        var MessageBoxData = "";
        $(".discussionbox").html('');
        var title = "GI Line";
        var SendTo = "";
        var ApprovalStatus = "";
        var SendFrom = "";
        var DraftQueryMessage = "";
        var actionButton = "";
        if (res.QueryMessageDetails.length > 0) {
            
            if (res.QueryMessageDetails[0].SalesGroupId > 0) {
                this._ctlMailRequest.addNewLoginRecipient(res.QueryMessageDetails[0].SalesGroupId, res.QueryMessageDetails[0].SalesGroupName);
            }
            if (res.QueryMessageDetails[0].PurchasingGroupId > 0) {
                this._ctlMailRequest.addNewLoginRecipient(res.QueryMessageDetails[0].PurchasingGroupId, res.QueryMessageDetails[0].PurchasingGroupName);
            }
        }
        for (var i = 0; i < res.QueryMessageDetails.length; i++) {
            var row = res.QueryMessageDetails[i];

            row.ParentSalesApprovalStatus = row.ParentSalesApprovalStatus == 3 ? 2 : row.ParentSalesApprovalStatus;
            row.ParentPurchaseApprovalStatus = row.ParentPurchaseApprovalStatus == 3 ? 2 : row.ParentPurchaseApprovalStatus;
            row.ParentQualityApprovalStatus = row.ParentQualityApprovalStatus == 3 ? 2 : row.ParentQualityApprovalStatus;


            $("#lblSapproverName").text(row.ProcessSalesApproverName);
            $("#lblPapproverName").text(row.ProcessPurchaseApproverName);
            $("#lblQapproverName").text(row.ProcessQualityApproverName);
            if (row.ProcessSalesApproverName == "No Approver Found") {
                $("#lblSapproverName").addClass("GIWarninglbl");
                $("#tdSApproverName").addClass("GIWarningtd");
                IsSApproverNotFound = 1;
            } else {
                $("#lblSapproverName").removeClass("GIWarninglbl");
                $("#tdSApproverName").removeClass("GIWarningtd");
                IsSApproverNotFound = 0;
            }
            if (row.ProcessPurchaseApproverName == "No Approver Found") {
                $("#lblPapproverName").addClass("GIWarninglbl");
                $("#tdPApproverName").addClass("GIWarningtd");
                IsPApproverNotFound = 1;
            } else {
                $("#lblPapproverName").removeClass("GIWarninglbl");
                $("#tdPApproverName").removeClass("GIWarningtd");
                IsPApproverNotFound = 0;
            }
            if (row.ProcessQualityApproverName == "No Approver Found") {
                $("#lblQapproverName").addClass("GIWarninglbl");
                $("#tdQApproverName").addClass("GIWarningtd");
                IsQApproverNotFound = 1;
            } else {
                $("#lblQapproverName").removeClass("GIWarninglbl");
                $("#tdQApproverName").removeClass("GIWarningtd");
                IsQApproverNotFound = 0;
            }
            title = "";
            
            if ((row.Quarantined == true || row.ISReleaseStock == true) && row.IsInitialMessage == true) {
                var StatusMsg = "";
                if (row.Quarantined == true) {
                    StatusMsg +=" <b style='color:red;font-size: 12px;background: yellow;font-style: italic;'>(This Stock is Quarantined)</b>";
                }
                if (row.ISReleaseStock == true) {
                    StatusMsg += " <b style='color:red;font-size: 12px;background: yellow;font-style: italic;'>(This Stock is Released)</b>";
                }
                title += " Query Number: " + row.GIQueryNumber + StatusMsg;
            }
            else {
                title += " Query Number: " + row.GIQueryNumber;
            }

            SendTo = "";
            SendTo += row.NotifyToSales == true ? row.CurrentSalesApprover + "" : "";
            SendTo += row.NotifyToQuality == true ? SendTo.length > 0 ? ", QUALITY" : "QUALITY" : "";
            SendTo += row.NotifyToPurchasing == true ? SendTo.length > 0 ? ", " + row.CurrentPurchasingApprover + "" : row.CurrentPurchasingApprover + "" : "";
            SendTo += row.CCUsersName.length > 0 ? SendTo.length > 0 ? ", " + row.CCUsersName + "" : row.CCUsersName + "" : "";

            SendTo += SendTo.length == 0 ? "Not Send" : "";

            ApprovalStatus = "";
            ApprovalStatus += row.SalesApprovalStatus == 1 ? "<b>Sales:</b> Approved " : row.SalesApprovalStatus == 2 ? "<b>Sales:</b> Declined " : row.SalesApprovalStatus == 3 ? "<b>Sales:</b> Partial Approved" : "";
            ApprovalStatus += row.QualityApprovalStatus == 1 ? "<b>Quality:</b> Approved " : row.QualityApprovalStatus == 2 ? "<b>Quality:</b> Declined " : row.QualityApprovalStatus == 3 ? "<b>Quality:</b> Partial Approved" : "";
            ApprovalStatus += row.PurchaseApprovalStatus == 1 ? "<b>Purchasing:</b> Approved " : row.PurchaseApprovalStatus == 2 ? "<b>Purchasing:</b> Declined" : row.PurchaseApprovalStatus == 3 ? "<b>Purchasing:</b> Partial Approved"  : "";

           
            var ConfigureButton = this._blnCanManageApproverEdit == true ? (row.InDraftMode == false ? String.format("<a class='configicon' title='This screen allows you to change the Approver for a GI Query, but the original Approver will still receive all the messages' onClick=\"ConfigureApprover(" + row.Gi_QueryId + ",'" + row.CurrentPurchasingApprover + "','" + row.CurrentSalesApprover + "'" + "," + row.ParentPurchaseApprovalStatus + "," + row.ParentSalesApprovalStatus + ") \">Manage Approvers<img src='../../../../images/ConfigureGi.png'></a>") : "") : "";

            //if ((row.Quarantined == true) || (row.ISReleaseStock==true)) {
               
            //    actionButton = row.InDraftMode == true ? "<a class='iconButton iconButton_Nugget_PostAll' style='color:white;margin-left: 45px;pointer-events: none;' onClick='RequestForApproval(" + row.Gi_QueryId + "," + IsSApproverNotFound + "," + IsPApproverNotFound + "," + IsQApproverNotFound + ")'><img src='../../../../images/sendbtn.png'>Send Query</a>" : "<a class='iconButton iconButton_Nugget_PostAll' id='btnRespondQ' style='color:white;margin-left: 20px;pointer-events: none;' onClick='ResponceForApproval(" + row.Gi_QueryId + "," + row.NotifyToSales + "," + row.NotifyToQuality + "," + row.NotifyToPurchasing + "," + row.ISSalesPermission + "," + row.ISQualityPermission + "," + row.ISPurchasingPermission + "," + row.ParentSalesApprovalStatus + "," + row.ParentPurchaseApprovalStatus + "," + row.ParentQualityApprovalStatus + "," + row.IsNotGBLPermissionForSales + "," + row.IsNotGBLPermissionForPurch + "," + row.IsNotGBLPermissionForQaulity + "," + row.C1 + "," + row.C2 + "," + row.C3 + "," + row.C4 + "," + row.C5 + "," + row.C6 + "," + row.C7 + "," + row.C8 + "," + row.C9 + "," + row.C10  + ")'><img src='../../../../images/sendbtn.png'>Respond to query</a>";
            //    }
            //    else {
            actionButton = row.InDraftMode == true ? "<a class='iconButton iconButton_Nugget_PostAll' style='color:white;margin-left: 45px' onClick='RequestForApproval(" + row.Gi_QueryId + "," + IsSApproverNotFound + "," + IsPApproverNotFound + "," + IsQApproverNotFound + ")'><img src='../../../../images/sendbtn.png'>Send Query</a>" : "<a class='iconButton iconButton_Nugget_PostAll' id='btnRespondQ' style='color:white;margin-left: 20px' onClick='ResponceForApproval(" + row.Gi_QueryId + "," + row.NotifyToSales + "," + row.NotifyToQuality + "," + row.NotifyToPurchasing + "," + row.ISSalesPermission + "," + row.ISQualityPermission + "," + row.ISPurchasingPermission + "," + row.ParentSalesApprovalStatus + "," + row.ParentPurchaseApprovalStatus + "," + row.ParentQualityApprovalStatus + "," + row.IsNotGBLPermissionForSales + "," + row.IsNotGBLPermissionForPurch + "," + row.IsNotGBLPermissionForQaulity + "," + row.C1 + "," + row.C2 + "," + row.C3 + "," + row.C4 + "," + row.C5 + "," + row.C6 + "," + row.C7 + "," + row.C8 + "," + row.C9 + "," + row.C10 + "," + row.IsQueryColumn +")'><img src='../../../../images/sendbtn.png'>Respond to query</a>";
                    //}
            if (row.IsInitialMessage == true) {
                
                var htmldata = $R_FN.setCleanTextValue(this.ChangeApproverResponse($R_FN.setCleanTextValue(row.QueryMessageApproval), row.C1, row.C2, row.C3, row.C4, row.C5, row.C6, row.C7, row.C8, row.C9, row.C10));
                $("#QueryMessageP").html("<div style='background-color:#c1e5bc;border:1px #c1e5bc solid;' class='contentarea'>" + htmldata + "</div>");

            }
            SendFrom = "";
            SendFrom += row.NotifyToSales == true ? "SALES" : "";
            SendFrom += row.NotifyToQuality == true ? "<br/>QUALITY" : "";
            SendFrom += row.NotifyToPurchasing == true ? "<br/>PURCHASING" : "";
            GIClientName = row.ClientName;
            if (row.MyMessage == true) {
                if (row.IsInitialMessage == true) {
                    InitailQueryMessageDetails += "<div class='rightcontentbox'><div class='leftimgblock'><img src='../../../../images/profilepic.png'><br/><span class=''>" + row.QueryRaisedBy + "" + "</span></div>";
                    InitailQueryMessageDetails += "<div class='contentarea'><span class='title'>" + title + "<span class='ManageApprovers'>" + ConfigureButton + "</span>" + "</span><span class='datetime'>" + (SendTo == 'Not Send' ? "" : row.DLUP) + (SendTo != 'Not Send' ? (" | <b>Sent To: </b>" + SendTo) : "") + "</span><p>" + $R_FN.setCleanTextValue(row.QueryMessage) + "<p class='remarkcol' > <b>WAREHOUSE REMARKS: </b>" + row.WarehouseRemark + "</p></span></div>";
                    InitailQueryMessageDetails += "<div  class='rlybtn'>" + actionButton + "</div></div>";
                }
                else {
                    ApprovalQueryMessageDetails += "<div class='rightcontentbox'><div class='leftimgblock'><img src='../../../../images/profilepic.png'><br/><span class=''>" + row.QueryRaisedBy + "</span></div>";
                    ApprovalQueryMessageDetails += "<div class='contentarea'><span class='title'>RE:" + title + "</span><span class='datetime'>" + row.DLUP + " | <b>Sent To: </b>" + "WAREHOUSE" + " | " + ApprovalStatus + "</span><p>" + $R_FN.setCleanTextValue(row.QueryMessage) + "</span></div>" + $R_FN.setCleanTextValue(row.QueryMessageApproval) +"</div>";
                }
            }
            else {
                if (row.IsInitialMessage == true) {
                    InitailQueryMessageDetails += "<div class='leftcontentbox'><div class='leftimgblock'><img src='../../../../images/profilepic2.png'><br/><span class=''>" + row.QueryRaisedBy + "" + "</span></div>";
                    InitailQueryMessageDetails += "<div class='contentarea'><span class='title'>" + title + "<span class='ManageApprovers'>" + ConfigureButton + "</span>" + "</span><span class='datetime'>" + (SendTo == 'Not Send' ? "" : row.DLUP) + (SendTo != 'Not Send' ? (" | <b>Sent To: </b>" + SendTo) : "") + "</span><p>" + $R_FN.setCleanTextValue(row.QueryMessage) + "<p class='remarkcol' > <b>WAREHOUSE REMARKS: </b>" + row.WarehouseRemark + "</p> </span></div>";
                    InitailQueryMessageDetails += "<div class='rlybtn'>" + actionButton + "</div></div>";
                }
                else {
                    ApprovalQueryMessageDetails += "<div class='leftcontentbox'><div class='leftimgblock'><img src='../../../../images/profilepic2.png'><br/><span class=''>" + row.QueryRaisedBy + "</span></div>";
                    ApprovalQueryMessageDetails += "<div class='contentarea'><span class='title'>RE:" + title + "</span><span class='datetime'>" + row.DLUP + " | <b>Sent To: </b>" + "WAREHOUSE" + " | " + ApprovalStatus + "</span><p>" + $R_FN.setCleanTextValue(row.QueryMessage) + " </span></div>" + $R_FN.setCleanTextValue(row.QueryMessageApproval) +"</div>";
                }
            }
            if (row.DraftQueryMessage != '') {
                DraftQueryMessage = row.DraftQueryMessage;
            }
            row = null;
        }
        $("#IsInitailMessage").append(InitailQueryMessageDetails);
        $("#IsApprovalMessage").append(ApprovalQueryMessageDetails);
        InitailQueryMessageDetails = "";
        ApprovalQueryMessageDetails = "";
        $get(this.getFormControlID(this._element.id, 'TxtMessageBox')).value = DraftQueryMessage;
        $get(this.getFormControlID(this._element.id, 'txtResponceBox')).value = DraftQueryMessage;
    },
    SendQueryMessage: function () {
        var IsSApproverNotFound = getSApproverNotFound();
        var IsPApproverNotFound = getPApproverNotFound();
        var IsQApproverNotFound = getQApproverNotFound();
        var msgConfirm = "";
        if ((IsSApproverNotFound == 1) && (this.getControlValue(this.getFormControlID(this._element.id, 'chkSales'), 'CheckBox') == true)) {
            msgConfirm = "Sales Approver not found, You still want to raise query kindly confirm?";
        }
        if ((IsPApproverNotFound == 1) && (this.getControlValue(this.getFormControlID(this._element.id, 'chkPurchasing'), 'CheckBox') == true)) {
            msgConfirm = "Purchase Approver not found, You still want to raise query kindly confirm?";
        }
        if ((IsQApproverNotFound == 1) && (this.getControlValue(this.getFormControlID(this._element.id, 'chkQualityApproval'), 'CheckBox') == true)) {
            msgConfirm = "Quality Approver not found, You still want to raise query kindly confirm?";
        }
        if ($get(this.getFormControlID(this._element.id, 'TxtMessageBox')).value.length > 0) {
            if ((this.getControlValue(this.getFormControlID(this._element.id, 'chkSales'), 'CheckBox') == true) || (this.getControlValue(this.getFormControlID(this._element.id, 'chkPurchasing'), 'CheckBox') == true) || (this.getControlValue(this.getFormControlID(this._element.id, 'chkQualityApproval'), 'CheckBox') == true)) {
                if (((IsSApproverNotFound == 1) && (this.getControlValue(this.getFormControlID(this._element.id, 'chkSales'), 'CheckBox') == true)) ||
                    ((IsPApproverNotFound == 1) && (this.getControlValue(this.getFormControlID(this._element.id, 'chkPurchasing'), 'CheckBox') == true)) ||
                    ((IsQApproverNotFound == 1) && (this.getControlValue(this.getFormControlID(this._element.id, 'chkQualityApproval'), 'CheckBox') == true))) {
                    if (confirm(msgConfirm) == true) {
                        $('#btnSendQuery').attr("disabled", "disabled");
                        var obj = new Rebound.GlobalTrader.Site.Data();
                        obj.set_PathToData("controls/Nuggets/GILines");
                        obj.set_DataObject("GILines");
                        obj.set_DataAction("AddGILineQueryMessage");
                        obj.addParameter("GoodsInId", this._intGIID);
                        obj.addParameter("GoodsInLineId", this._intLineID);
                        obj.addParameter("QueryMessage", $get(this.getFormControlID(this._element.id, 'TxtMessageBox')).value);
                        obj.addParameter("IsSalesNotify", this.getControlValue(this.getFormControlID(this._element.id, 'chkSales'), 'CheckBox'));
                        obj.addParameter("IsPurchasingNotify", this.getControlValue(this.getFormControlID(this._element.id, 'chkPurchasing'), 'CheckBox'));
                        obj.addParameter("IsQulityNotify", this.getControlValue(this.getFormControlID(this._element.id, 'chkQualityApproval'), 'CheckBox'));
                        obj.addParameter("GI_QueryId", Get_GIQueryId());
                        obj.addParameter("PONumber", $('#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_lblPONumber').text());
                        obj.addParameter("GoodsInNumber", this._intGoodsInNumber);
                        obj.addParameter("EnhInspectionStatusId", this._intEnhancedInspectionStatusId);
                        obj.addParameter("CCUserId", $R_FN.arrayToSingleString(this._ctlMailRequest._aryRecipientLoginIDs));
                        obj.addParameter("CCGroupIDs", $R_FN.arrayToSingleString(this._ctlMailRequest._aryRecipientGroupIDs));
                        obj.addDataOK(Function.createDelegate(this, this.SendQueryMessageOK));
                        obj.addError(Function.createDelegate(this, this.SendQueryMessageError));
                        obj.addTimeout(Function.createDelegate(this, this.SendQueryMessageError));
                        $R_DQ.addToQueue(obj);
                        $R_DQ.processQueue();
                        obj = null;
                    }
                } else {
                    $('#btnSendQuery').attr("disabled", "disabled");
                    var obj = new Rebound.GlobalTrader.Site.Data();
                    obj.set_PathToData("controls/Nuggets/GILines");
                    obj.set_DataObject("GILines");
                    obj.set_DataAction("AddGILineQueryMessage");
                    obj.addParameter("GoodsInId", this._intGIID);
                    obj.addParameter("GoodsInLineId", this._intLineID);
                    obj.addParameter("QueryMessage", $get(this.getFormControlID(this._element.id, 'TxtMessageBox')).value);
                    obj.addParameter("IsSalesNotify", this.getControlValue(this.getFormControlID(this._element.id, 'chkSales'), 'CheckBox'));
                    obj.addParameter("IsPurchasingNotify", this.getControlValue(this.getFormControlID(this._element.id, 'chkPurchasing'), 'CheckBox'));
                    obj.addParameter("IsQulityNotify", this.getControlValue(this.getFormControlID(this._element.id, 'chkQualityApproval'), 'CheckBox'));
                    obj.addParameter("GI_QueryId", Get_GIQueryId());
                    obj.addParameter("PONumber", $('#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_lblPONumber').text());
                    obj.addParameter("GoodsInNumber", this._intGoodsInNumber);
                    obj.addParameter("EnhInspectionStatusId", this._intEnhancedInspectionStatusId);
                    obj.addParameter("CCUserId", $R_FN.arrayToSingleString(this._ctlMailRequest._aryRecipientLoginIDs));
                    obj.addParameter("CCGroupIDs", $R_FN.arrayToSingleString(this._ctlMailRequest._aryRecipientGroupIDs));
                    obj.addDataOK(Function.createDelegate(this, this.SendQueryMessageOK));
                    obj.addError(Function.createDelegate(this, this.SendQueryMessageError));
                    obj.addTimeout(Function.createDelegate(this, this.SendQueryMessageError));
                    $R_DQ.addToQueue(obj);
                    $R_DQ.processQueue();
                    obj = null;
                }  
            }
            else {
                alert("Please choose at least one option from send to section.");
            }

        }
        else {
            alert("Please write something in the discussion box.");
        }
    },
    SendQueryMessageError: function (args) {
        $('#btnSendQuery').removeAttr("disabled");
    },
    SendQueryMessageOK: function (args) {
        this.RefreshAllQueryMessage();
        $get(this.getFormControlID(this._element.id, 'TxtMessageBox')).value = "";

        var chkSales = this.getFormControlID(this._element.id, 'chkSales');
        this.setControlValue("CheckBox", chkSales, false, "");

        var chkPurchasing = this.getFormControlID(this._element.id, 'chkPurchasing');
        this.setControlValue("CheckBox", chkPurchasing, false, "");

        var chkQualityApproval = this.getFormControlID(this._element.id, 'chkQualityApproval');
        this.setControlValue("CheckBox", chkQualityApproval, false, "");

        var Requestmodal = document.getElementById("RequestApprovalModel");
        Requestmodal.style.display = "none";
        $R_IBTN.enableButton(this._ibtnSaveRelease, false);
        $R_IBTN.enableButton(this._ibtnSendQuery, false);
        $("#ctl00_cphMain_ctlLines_ctlDB_ctl18_ibtnSendQuery_lblDisabled").removeClass();
        $("#ctl00_cphMain_ctlLines_ctlDB_ctl18_ibtnSendQuery_hyp").removeClass();
        $("#ctl00_cphMain_ctlLines_ctlDB_ctl18_ibtnSendQuery_lblDisabled").addClass("iconButton iconButton_Nugget_Disabled iconButton_Nugget_Save_Disabled iconButton_alignLeft");
        $("#ctl00_cphMain_ctlLines_ctlDB_ctl18_ibtnSendQuery_hyp").addClass("iconButton iconButton_Nugget iconButton_Nugget_Save iconButton_alignLeft invisible");
        this._IsQueryRaised = true;
        $R_IBTN.enableButton(this._ibtnSave, false);
        $R_IBTN.enableButton(this._ibtnSave_Footer, false);
        $('#btnSendQuery').removeAttr("disabled")
    },
    getPackagingBreakdownData: function () {
        var data = [];
        $('tr.data-Breakdown-GI').each(function () {
            var FactorySealed = $(this).find('.f-FactorySealed01').is(":checked");
            var NumberofPacks = $(this).find('.n-NumberofPacks01').val();
            var PackSize = $(this).find('.p-PackSize01').val();
            var DateCode = $(this).find('.d-DateCode01').val();
            var BatchCode = $(this).find('.d-BatchCode01').val();
            var PackagingTypeId = $(this).find('.d-PackagingType01').val();
            var MFRLabelId = $(this).find('.d-MFRLabel01').val();
            var Total = $(this).find('.t-Total01').val();
            var alldata = {
                'FactorySealed': FactorySealed,
                'NumberofPacks': NumberofPacks,
                'PackSize': PackSize,
                'DateCode': DateCode,
                'BatchCode': BatchCode,
                'PackagingTypeId': PackagingTypeId,
                'MFRLabelId': MFRLabelId,
                'Total': Total
            }
            if (NumberofPacks > 0 || PackSize > 0)
                data.push(alldata);
        });
        return data;
    },
    bindPackagingBreakdownData: function () {
        $('#tbBreakdownTable').empty();
        if (this._PackBreakDownJSON != undefined) {
            if (this._PackBreakDownJSON.length > 0) {
                var SumOfTotal = 0;
                var PackDropDownOption = "<option value ='0'>--Select--</option>";
                for (var i = 0; i < this._PackagingListJSON.length; i++) {
                    PackDropDownOption += "<option value = '" + this._PackagingListJSON[i].ID + "'>" + this._PackagingListJSON[i].Name + " </option>";
                }
                var MFRLabelDropDownOption = "<option value ='0'>--Select--</option>";
                for (var i = 0; i < this._MFRLabelListJSON.length; i++) {
                    MFRLabelDropDownOption += "<option value = '" + this._MFRLabelListJSON[i].ID + "'>" + this._MFRLabelListJSON[i].Name + " </option>";
                }
                for (var i = 0; i < this._PackBreakDownJSON.length; i++) {
                    var row = this._PackBreakDownJSON[i];

                    var counter = $('.data-Breakdown-GI').length + 1;
                    $('<tr id="tablerow' + counter + '" class="data-Breakdown-GI"><td id="tdFactorySealed' + counter + '">' +
                        '<input type="checkbox" name="FactorySealed' + counter + '"class="form-control f-FactorySealed01" id=chkFactorySealed' + counter + (row.FactorySealed == true ? ' checked="checked"' : '') + '>' +
                        '</td>' +
                        '<td id="tdNumberofPacks' + counter + '">' +
                        '<input type="text" name="NumberofPacks' + counter + '"class="form-control n-NumberofPacks01" id=txtNumberofPacks' + counter + ' onkeypress="return (event.charCode !=8 && event.charCode ==0 || (event.charCode >= 48 && event.charCode <= 57))" onKeyUp="return BindPackagingBreakdown(this.id)" value=' + row.NumberofPacks + '>' +
                        '</td>' +
                        '<td id="tdPackSize' + counter + '">' +
                        '<input type="text" name="PackSize' + counter + '"class="form-control p-PackSize01" id=txtPackSize' + counter + ' onkeypress="return (event.charCode !=8 && event.charCode ==0 || (event.charCode >= 48 && event.charCode <= 57))" onKeyUp="return BindPackagingBreakdown(this.id)" value=' + row.PackSize + '>' +
                        '</td>' +
                        '<td id="tdDateCode' + counter + '">' +
                        '<input type="text" name="DateCode' + counter + '"class="form-control d-DateCode01" id=txtDateCode' + counter + '  value=' + row.DateCode + '>' +
                        '</td>' +
                        '<td id="tdPackagingType' + counter + '">' +
                        '<select name="PackagingType' + counter + '"class="form-control d-PackagingType01" id=ddlPackagingType' + counter + ' /select>' +
                        //'<ReboundUI:ComboNew runat="server" AutoSearchControlAssembly="Rebound.GlobalTrader.Site" AutoSearchControlType="Packages" ParentControlID="cmbPackage" id=cmbNewPackage' + counter + ' />' +
                        '</td>' +
                        '<td id="tdBatchCode' + counter + '">' +
                        '<input type="text" style="width: 61px !important;" name="BatchCode' + counter + '"class="form-control d-BatchCode01" id=txtBatchCode' + counter + ' value=' + row.BatchCode + '>' +
                        '</td>' +
                        '<td id="tdMFRLabel' + counter + '">' +
                        '<select name="MFRLabel' + counter + '"class="form-control d-MFRLabel01" id=ddlMFRLabel' + counter + ' /select>' +
                        '</td>' +
                        '<td id="tdTotal' + counter + '">' +
                        '<input type="text" name="Total' + counter + '"class="form-control t-Total01" id=txtTotal' + counter + ' value=' + row.Total + ' readonly="readonly">' +
                        '</td>' +
                        '<td>' +
                        '<button class="btn btn-primary" onclick="removeTr(' + counter + ');"><i class="fa fa-close"></i></button>' +
                        '</td>' +
                        '</tr>').appendTo('#BreakdownTable');
                    $(("#ddlPackagingType" + counter)).append(PackDropDownOption);
                    $(('#ddlPackagingType' + counter) + ' option[value="' + row.PackagingTypeId + '"]').attr("selected", "selected");
                    $(('#ddlPackagingType' + counter)).chosen({ allow_single_deselect: true });
                    $(("#ddlMFRLabel" + counter)).append(MFRLabelDropDownOption);
                    $(('#ddlMFRLabel' + counter) + ' option[value="' + row.MFRLabelId + '"]').attr("selected", "selected");
                    $(('#ddlPackagingType' + counter) + '_chosen').css("width", "200px");
                    counter++;
                    SumOfTotal = SumOfTotal + parseFloat(row.Total);
                }
                $('#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_lblPackageBreakTotal').text(SumOfTotal);
                //if ((SumOfTotal != this._intPOQuantityOrdered)) {
                //    document.getElementById("ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_lblQuantity").style.backgroundColor = "Red";
                //    document.getElementById("ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_lblQuantity").style.background = "Yellow";
                //    document.getElementById('ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_lblQuantity').innerHTML = SumOfTotal + ' Parts Received ' + this._intPOQuantityOrdered + ' Adviced';
                //}
                //else {
                //    document.getElementById("ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_lblQuantity").style.backgroundColor = "";
                //    document.getElementById("ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_lblQuantity").style.background = "";
                //    document.getElementById('ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_lblQuantity').innerHTML = "";

                //}
            }
        }
    },
    GILineUploadPDF: function (originalFilename, generatedFilename) {
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/GILines");
        obj.set_DataObject("GILines");
        obj.set_DataAction("SaveGILineUploadPDF");
        obj.addParameter("ID", this._intLineID);
        obj.addParameter("Caption", originalFilename.split('.').slice(0, -1).join('.'));
        obj.addParameter("OriginalFilename", originalFilename);
        obj.addParameter("TempFile", generatedFilename);
        obj.addDataOK(Function.createDelegate(this, this.saveAddComplete));
        obj.addError(Function.createDelegate(this, this.saveAddError));
        obj.addTimeout(Function.createDelegate(this, this.saveAddError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;

    },
    saveAddComplete: function (args) {
        if (args._result.Result == true) {
            this.getAttachementsData();

        } else {
            alert('No Stock Found.');
            //if (args._result.Message) this._strErrorMessage = "No Stock found.";
            //this.onSaveError();
        }
    },
    saveAddError: function (args) {
        this._strErrorMessage = args._errorMessage;
        this.onSaveError();
    },
    getPDFData: function () {
        this._intCountPDF == 0;
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/GILines");
        obj.set_DataObject("GILines");
        obj.set_DataAction("GetPDFData");
        obj.addParameter("id", this._intLineID);
        obj.addDataOK(Function.createDelegate(this, this.getPDFDataOK));
        obj.addError(Function.createDelegate(this, this.getPDFDataError));
        obj.addTimeout(Function.createDelegate(this, this.getPDFDataError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },
    getPDFDataOK: function (args) {
        var res = args._result;
        var result = args._result;
        $R_FN.setInnerHTML(this._pnlPDFDocuments, "");
        var iconpath = result.IconPath;
        var strPDF = "";
        var MarginLeft ="margin-left: 57%;";
        if (result.Items) {
            for (var i = 0; i < result.Items.length; i++) {
               
                var row = result.Items[i];

                if (row.Caption.length == 1) {
                    MarginLeft = "margin-left:15%;"
                }
                if (row.Caption.length == 2) {
                    MarginLeft = "margin-left:21%;"
                }
                if (row.Caption.length == 3) {
                    MarginLeft = "margin-left:23%;"
                }
                if (row.Caption.length == 4) {
                    MarginLeft = "margin-left:25%;"
                }
                if (row.Caption.length == 5) {
                    MarginLeft = "margin-left:26%;"
                }
                if (row.Caption.length == 6) {
                    MarginLeft = "margin-left:29%;"
                }
                if (row.Caption.length == 7) {
                    MarginLeft = "margin-left:33%;"
                }
                if (row.Caption.length == 8) {
                    MarginLeft = "margin-left:35%;"
                }
                if (row.Caption.length == 9) {
                    MarginLeft = "margin-left:37%;"
                }
                if (row.Caption.length == 10) {
                    MarginLeft = "margin-left:40%;"
                }
                if (row.Caption.length == 11) {
                    MarginLeft = "margin-left:43%;"
                }
                if (row.Caption.length == 12) {
                    MarginLeft = "margin-left:46%;"
                }
                if (row.Caption.length == 13) {
                    MarginLeft = "margin-left:49%;"
                }
                if (row.Caption.length == 14) {
                    MarginLeft = "margin-left:53%;"
                }
                if (row.Caption.length == 15) {
                    MarginLeft = "margin-left:56%;"
                }
                strPDF += String.format("<span class='thumbimgbox'>");
                if (row.IsDeletePermission) {
                    strPDF += String.format("<div class=\"GIDeleteCheckBox\" \"><input type='checkbox' class='chkhideshow' style='margin-top: 3px;margin-left: 25px;' onclick='AddPdfAttachmentIds(" + row.ID + ")' id='BulkAttachmentDelete" + row.ID + "' /></div>");
                }
                strPDF += String.format("<a href=\"{0}\" ><img  id =\"{1}_img{2}\" src=\"{3}\" border=\"0\" onclick=\"$find('{4}').OpenPDF('{5}','{6}');\" /></a>", "javascript:void(0);", this._element.id, i, iconpath, this._element.id, "", row.FileName);
                strPDF += "<span class='docdetails'>";
                strPDF += String.format("<span class='doctitle' style='color:yellow;cursor: pointer;text-decoration: underline;' title='" + row.FullCaption + "' onclick=\"$find('{0}').RenameImage('{1}','{2}','{3}');\">" + row.Caption + "<span style='text-decoration: underline;margin-top: -15px;"+MarginLeft+"'><img src='../App_Themes/Original/images/IconButton/nuggets/edit.gif'></span></span>", this._element.id, row.ID, row.FullCaption, "PDF");
                strPDF += "<span class='docdate'>" + row.Date + "</span>";
                strPDF += "<span class='docuploadby'>" + row.By + "</span>";
                strPDF += "</span>";
                if (row.IsDeletePermission) {
                    //strPDF += String.format("<input type='checkbox' class='chkhideshow' style='margin-top: 10px;' onclick='AddPdfAttachmentIds(" + row.ID + ")' id='BulkAttachmentDelete" + row.ID + "' />");
                }
                strPDF += "</span>";
                if ((i + 1) % 4 == 0) { strPDF += ""; }
                row = null;
            }
            this._intCountPDF = result.Items.length;
        }
        $("#pdfAttachments").html(strPDF);

    },
    getPDFDataError: function (args) {
        this.showError(true, args.get_ErrorMessage());
    },
    OpenPDF: function (strsection, FileName) {
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/GILines");
        obj.set_DataObject("GILines");
        obj.set_DataAction("GetPDFAccessURL");
        obj.addParameter("section", strsection);
        obj.addParameter("filename", FileName);
        obj.addDataOK(Function.createDelegate(this, this.getOpenPDFOK));
        obj.addError(Function.createDelegate(this, this.getOpenPDFError));
        obj.addTimeout(Function.createDelegate(this, this.getOpenPDFError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },
    getOpenPDFOK: function (args) {
        var res = args._result;
        var result = args._result;
        window.open(this.setCleanTextBlobURL(result.bothirl), '_blank');

    },
    getOpenPDFError: function (args) {
        this.showError(true, args.get_ErrorMessage());
    },
    setCleanTextBlobURL: function (strIn, blnReplaceLineBreaks) {
        if (typeof (strIn) == "undefined") strIn = "";
        strIn = (strIn + "").trim();
        strIn = strIn.replace(/(:PLUS:)/g, "+");
        strIn = strIn.replace(/(:AND:)/g, "&");
        strIn = strIn.replace(/[+]/g, "%2B");

        return strIn;
    },
    saveGILineImage: function (originalFilename, generatedFilename) {
        let p = document.getElementById("uploadImages").childNodes[3];
        var len = p.childNodes.length;
        for (var i = 0; i < len; i++) {
            p.removeChild(p.firstElementChild)
        }
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/GILines");
        obj.set_DataObject("GILines");
        obj.set_DataAction("SaveGILineImage");
        obj.addParameter("ID", this._intLineID);
        obj.addParameter("Caption", originalFilename.split('.').slice(0, -1).join('.'));
        obj.addParameter("TempFile", generatedFilename);
        obj.addDataOK(Function.createDelegate(this, this.saveAddComplete));
        obj.addError(Function.createDelegate(this, this.saveAddError));
        obj.addTimeout(Function.createDelegate(this, this.saveAddError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;

    },
    getImageData: function () {
        this._intCountImages == 0;
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/GILines");
        obj.set_DataObject("GILines");
        obj.set_DataAction("GetImageData");
        obj.addParameter("ID", this._intLineID);
        obj.addDataOK(Function.createDelegate(this, this.getImageDataOK));
        obj.addError(Function.createDelegate(this, this.getImageDataError));
        obj.addTimeout(Function.createDelegate(this, this.getImageDataError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },
    getImageDataOK: function (args) {
        var res = args._result;
        var result = args._result;
        $R_FN.setInnerHTML(this._pnlImagesDragDrop, "");
        var strImages = "";
        var MarginLeft = "margin-left: 57%;";
        if (result.Items) {
            this.intCountImages = result.Items.length;
            for (var i = 0; i < result.Items.length; i++) {

                var row = result.Items[i];
                if (row.Caption.length == 1) {
                    MarginLeft = "margin-left:15%;"
                }
                if (row.Caption.length == 2) {
                    MarginLeft = "margin-left:21%;"
                }
                if (row.Caption.length == 3) {
                    MarginLeft = "margin-left:23%;"
                }
                if (row.Caption.length == 4) {
                    MarginLeft = "margin-left:25%;"
                }
                if (row.Caption.length == 5) {
                    MarginLeft = "margin-left:26%;"
                }
                if (row.Caption.length == 6) {
                    MarginLeft = "margin-left:29%;"
                }
                if (row.Caption.length == 7) {
                    MarginLeft = "margin-left:33%;"
                }
                if (row.Caption.length == 8) {
                    MarginLeft = "margin-left:35%;"
                }
                if (row.Caption.length == 9) {
                    MarginLeft = "margin-left:37%;"
                }
                if (row.Caption.length == 10) {
                    MarginLeft = "margin-left:40%;"
                }
                if (row.Caption.length == 11) {
                    MarginLeft = "margin-left:43%;"
                }
                if (row.Caption.length == 12) {
                    MarginLeft = "margin-left:46%;"
                }
                if (row.Caption.length == 13) {
                    MarginLeft = "margin-left:49%;"
                }
                if (row.Caption.length == 14) {
                    MarginLeft = "margin-left:53%;"
                }
                if (row.Caption.length == 15) {
                    MarginLeft = "margin-left:56%;"
                }
                strImages += "<span class='thumbimgbox'>";
                if (row.IsDeletePermission) {
                    //strImages += String.format("<div class=\"GIpdfDelete\" onclick=\"$find('{0}').deleteImage({1},'{2}');\">&nbsp;</div>", this._element.id, row.ID, row.ImageName);
                    strImages += String.format("<div class=\"GIDeleteCheckBox\" \"><input type='checkbox' class='chkhideshow' style='margin-top: 3px;margin-left: 25px;' onclick='AddImgAttachmentIds(" + row.ID + ")' id='BulkImageDelete" + row.ID + "' /></div>");
                }
                strImages += String.format("<img id =\"{0}_img{1}\" src=\"{2}\" border=\"0\"  />", this._element.id, i, this.getImageSource(row.ID, "t", '' + row.ImageName + '', "SOURCEIMAGE"));
                strImages += "<span class='docdetails'>";
                strImages += String.format("<span class='doctitle' style='color:yellow; cursor: pointer;text-decoration:underline;' title='" + row.FullCaption + "' onclick=\"$find('{0}').RenameImage('{1}','{2}','{3}');\">" + row.Caption + "<span style='margin-top:-16px;" +MarginLeft+"'><img src='../App_Themes/Original/images/IconButton/nuggets/edit.gif'></span></span>", this._element.id, row.ID, row.FullCaption, "IMAGE");
                strImages += "<span class='docdate'>" + row.Date + "</span>";
                strImages += "<span class='docuploadby'>" + row.By; +"</span>";
                strImages += String.format("<br /><a href=\"javascript:void(0);\" onclick=\"$find('{0}').popupImage({1}, 'm','{4}','STOCKIMGFORSAN');\">{2}</a> | <a href=\"javascript:void(0);\" onclick=\"$find('{0}').popupImage({1}, 'f','{4}','STOCKIMGFORSAN');\">{3}</a>", this._element.id, row.ID, "Medium", "Large", row.ImageName);
                strImages += "</span></span>";
                if (row.IsDeletePermission) {
                    //strImages += String.format("<input type='checkbox' class='chkhideshow' style='margin-top: 10px;' onclick='AddImgAttachmentIds(" + row.ID + ")' id='BulkImageDelete" + row.ID + "' />");
                }
                strImages += "</span>";
                if ((i + 1) % 4 == 0) {
                    //strImages += "<hr>";
                }
                row = null;
            }

        }
        $("#imageAttachements").html(strImages);
    },
    getImageDataError: function (args) {
        this.showError(true, args.get_ErrorMessage());
    },
    getImageSource: function (intID, strType, strImageName, strstockimage) {
        return String.format("StockImage.ashx?img={0}&typ={1}&imagename={2}&imagesourcefrom={3}", intID, strType, strImageName, strstockimage);
    },
    popupImage: function (intID, strType, strFileName, strstockimage) {
        if (strType == 'm')
            window.open(this.getImageSource(intID, strType, strFileName, strstockimage), "", "left=300,top=250,width=654,height=488,toolbar=no,resizable=yes,status=no,directories=no,location=no,menubar=no,scrollbars=yes");
        else
            window.open(this.getImageSource(intID, strType, strFileName, strstockimage), "", "left=300,top=170,width=802,height=602,toolbar=no,resizable=yes,status=no,directories=no,location=no,menubar=no,scrollbars=yes");
    },
    getAttachementsData: function () {
        //this.CheckDeleteAttcmntPermission();
        SetDefaultUpload();
        $(".spanBorder").css("display", "initial");
        $R_IBTN.enableButton(this._ibtnSave, false);
        $R_IBTN.enableButton(this._ibtnSave_Footer, false);
        if (this._NavigateFromGIInfo == true) {
            var result = this.filedChangesList("SendQuery");
            if (result.length > 0)
                alert("Please make sure all data has been saved by clicking Save and exit or Send query button before leaving this tab.");
        }
        $R_IBTN.enableButton(this._ibtnSendQuery, false);
        $("#ctl00_cphMain_ctlLines_ctlDB_ctl18_ibtnSendQuery_lblDisabled").removeClass();
        $("#ctl00_cphMain_ctlLines_ctlDB_ctl18_ibtnSendQuery_hyp").removeClass();
        $("#ctl00_cphMain_ctlLines_ctlDB_ctl18_ibtnSendQuery_lblDisabled").addClass("iconButton iconButton_Nugget_Disabled iconButton_Nugget_Save_Disabled iconButton_alignLeft");
        $("#ctl00_cphMain_ctlLines_ctlDB_ctl18_ibtnSendQuery_hyp").addClass("iconButton iconButton_Nugget iconButton_Nugget_Save iconButton_alignLeft invisible");
        this.getPDFData();
        this.getImageData();

        this._NavigateFromGIInfo = false;
        if (this._IsViewModeOnly == true) {
            $('#ddlUpload').attr("disabled", "disabled");
            $('#uploadPdf').hide();
        }

    },
    SaveAndSendQuery: function () {
        this._isSendClicked = true;
        var PackagingBreakdownData = this.validatePackagingBreakdownData();
        var isDuplicate = false;
        for (var i = 0; i < PackagingBreakdownData.length; i++) {
            for (var j = 0; j < PackagingBreakdownData.length; j++) {
                if (i != j) {
                    if (PackagingBreakdownData[i].BatchCode == PackagingBreakdownData[j].BatchCode
                        && PackagingBreakdownData[i].DateCode == PackagingBreakdownData[j].DateCode
                        && PackagingBreakdownData[i].FactorySealed == PackagingBreakdownData[j].FactorySealed
                        && PackagingBreakdownData[i].MFRLabelId == PackagingBreakdownData[j].MFRLabelId
                        && PackagingBreakdownData[i].NumberofPacks == PackagingBreakdownData[j].NumberofPacks
                        && PackagingBreakdownData[i].PackSize == PackagingBreakdownData[j].PackSize
                        && PackagingBreakdownData[i].PackagingTypeId == PackagingBreakdownData[j].PackagingTypeId) {
                        isDuplicate = true;
                        break;
                    }
                }

            }
        }

        //if (isDuplicate == true)
            //alert("Duplicate rows found in the Packaging Breakdown. The system will remove the duplicate record automatically");


        this.saveClicked();
        if (this.allowSaving == true) {
            $('#aQueryMessages').click();
        }
    },
    ValidatePackageBreakDown: function () {
        var isvalid = true;
        var table = document.getElementById('tbBreakdownTable');
        var rowLength = table.rows.length;


        $('#tbBreakdownTable tr').each(function () {
            var tbIds = this.id.split(/([0-9]+)/);
            var i = tbIds[1];

        })
        return isvalid;
    },
    SendApprovalResponce: function () {
        if ($get(this.getFormControlID(this._element.id, 'txtResponceBox')).value.length > 0) {
            if (($find(this.getFormControlID(this._element.id, 'ddlQualityApproved')).getValue() != 0) || ($find(this.getFormControlID(this._element.id, 'ddlSalesApproved')).getValue() != 0) || ($find(this.getFormControlID(this._element.id, 'ddlPurchasingApproved')).getValue() != 0)) {
                if ($find(this.getFormControlID(this._element.id, 'ddlQualityApproved')).getValue() > 0) {
                    if (Get_ParentQualityApprovalStatus() == 1) {
                        alert("This query is already approved by the quality you can not take another action.");
                        return false;
                    }
                }
                if ($find(this.getFormControlID(this._element.id, 'ddlPurchasingApproved')).getValue() > 0) {
                    if (Get_ParentPurchasingApprovalStatus() == 1) {
                        alert("This query is already approved by the purchasing you can not take another action.");
                        return false;
                    }
                }
                if ($find(this.getFormControlID(this._element.id, 'ddlSalesApproved')).getValue() > 0) {
                    if (Get_ParentSalesApprovalStatus() == 1) {
                        alert("This query is already approved by the sales you can not take another action.");
                        return false;
                    }
                }
                $('#btnSendResponce').attr("disabled", "disabled");
                var obj = new Rebound.GlobalTrader.Site.Data();
                obj.set_PathToData("controls/Nuggets/GILines");
                obj.set_DataObject("GILines");
                obj.set_DataAction("AddGILineQueryApprovalResponce");
                obj.addParameter("GoodsInId", this._intGIID);
                obj.addParameter("GoodsInLineId", this._intLineID);
                obj.addParameter("QueryMessage", $get(this.getFormControlID(this._element.id, 'txtResponceBox')).value);
                obj.addParameter("SalesApprovalStatus", $find(this.getFormControlID(this._element.id, 'ddlSalesApproved')).getValue());
                obj.addParameter("PurchasingApprovalStatus", $find(this.getFormControlID(this._element.id, 'ddlPurchasingApproved')).getValue());
                obj.addParameter("QualityApprovalStatus", $find(this.getFormControlID(this._element.id, 'ddlQualityApproved')).getValue());
                obj.addParameter("GI_QueryId", Get_GIQueryId());
                obj.addParameter("PONumber", $('#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_lblPONumber').text());
                obj.addParameter("GoodsInNumber", this._intGoodsInNumber);
                obj.addParameter("CCUserId", $R_FN.arrayToSingleString(this._ctlMail._aryRecipientLoginIDs));
                obj.addParameter("CCGroupIDs", $R_FN.arrayToSingleString(this._ctlMail._aryRecipientGroupIDs));
                obj.addParameter("EnhInspectionStatusId", this._intEnhancedInspectionStatusId);
                if ((this._blnQAIncludeApproverHtml == true) || (this._blnSalesDataIncludeApproverHtml == true) || (this._blnPurchaseDataIncludeApproverHtml == true)) {
                    obj.addParameter("HtmlApproval", replaceExtra($("#QueryMessageP").html()));
                }
                else {
                    obj.addParameter("HtmlApproval","");
                }
                obj.addParameter("TotalCheckBoxcount", TotalCheckBoxcount());
                obj.addParameter("CheckedTotalCheckBoxcount", CheckedTotalCheckBoxcount());
                obj.addParameter("GetEnableCheckBoxIds", GetEnableCheckBoxIds());

                obj.addDataOK(Function.createDelegate(this, this.SendApprovalResponceOK));
                obj.addError(Function.createDelegate(this, this.SendApprovalResponceError));
                obj.addTimeout(Function.createDelegate(this, this.SendApprovalResponceError));
                $R_DQ.addToQueue(obj);
                $R_DQ.processQueue();
                obj = null;
            }
            else {
                alert("Please Select appropriate Approval option in the dropdown.");
            }

        }
        else {
            alert("Please write something in the discussion box.");
        }
    },
    SendApprovalResponceError: function (args) {
        $('#btnSendResponce').removeAttr("disabled");
    },
    SendApprovalResponceOK: function (args) {
        this.RefreshAllQueryMessage();
        $get(this.getFormControlID(this._element.id, 'txtResponceBox')).value = "";
        this.setControlValue("DropDown", this.getFormControlID(this._element.id, 'ddlSalesApproved'), "", "");
        this.setControlValue("DropDown", this.getFormControlID(this._element.id, 'ddlQualityApproved'), "", "");
        this.setControlValue("DropDown", this.getFormControlID(this._element.id, 'ddlPurchasingApproved'), "", "");
        //this.setControlValue("DropDown", this.getFormControlID(this._element.id, 'ddlCCUsers'), "", "");
        var ResponceModal = document.getElementById("ResponceApprovalModel");
        ResponceModal.style.display = "none";
        $('#btnSendResponce').removeAttr("disabled");
        this._blnPurchaseDataIncludeApproverHtml = false; 
        this._blnSalesDataIncludeApproverHtml = false;
        this._blnQAIncludeApproverHtml = false;
    },

    GetApprovalTabledata: function () {
        
                var obj = new Rebound.GlobalTrader.Site.Data();
                obj.set_PathToData("controls/Nuggets/GILines");
                obj.set_DataObject("GILines");
                obj.set_DataAction("GetApprovalTabledata");
                obj.addParameter("GoodsInId", this._intGIID);
                obj.addParameter("GoodsInLineId", this._intLineID);
               
                $R_DQ.addToQueue(obj);
                $R_DQ.processQueue();
                obj = null;
    },
    GetApprovalTabledataError: function (args) {
        //$('#btnSendResponce').removeAttr("disabled");
    },
    GetApprovalTabledataOK: function (args) {
        //this.RefreshAllQueryMessage();
        //$get(this.getFormControlID(this._element.id, 'txtResponceBox')).value = "";
        //this.setControlValue("DropDown", this.getFormControlID(this._element.id, 'ddlSalesApproved'), "", "");
        //this.setControlValue("DropDown", this.getFormControlID(this._element.id, 'ddlQualityApproved'), "", "");
        //this.setControlValue("DropDown", this.getFormControlID(this._element.id, 'ddlPurchasingApproved'), "", "");
        ////this.setControlValue("DropDown", this.getFormControlID(this._element.id, 'ddlCCUsers'), "", "");
        //var ResponceModal = document.getElementById("ResponceApprovalModel");
        //ResponceModal.style.display = "none";
        //$('#btnSendResponce').removeAttr("disabled");
    },
    ActeoneTestPass: function () {
        var chkActeoneTestPass = this.getControlValue(this.getFormControlID(this._element.id, 'chkActeoneTestPass'), 'CheckBox');
        if (chkActeoneTestPass == true) {
            $find(this.getFormControlID(this._element.id, 'chkActeoneTestFail')).setChecked(false);
            $find(this.getFormControlID(this._element.id, 'chkActeoneTestNA')).setChecked(false);
        }

    },
    ActeoneTestFail: function () {
        var chkActeoneTestFail = this.getControlValue(this.getFormControlID(this._element.id, 'chkActeoneTestFail'), 'CheckBox');
        if (chkActeoneTestFail == true) {
            $find(this.getFormControlID(this._element.id, 'chkActeoneTestPass')).setChecked(false);
            $find(this.getFormControlID(this._element.id, 'chkActeoneTestNA')).setChecked(false);
        }

    },
    ActeoneTestNA: function () {
        var chkActeoneTestNA = this.getControlValue(this.getFormControlID(this._element.id, 'chkActeoneTestNA'), 'CheckBox');
        if (chkActeoneTestNA == true) {
            $find(this.getFormControlID(this._element.id, 'chkActeoneTestPass')).setChecked(false);
            $find(this.getFormControlID(this._element.id, 'chkActeoneTestFail')).setChecked(false);
        }
    },
    IsopropryleActeoneTestPass: function () {
        var chkIsoproprylePass = this.getControlValue(this.getFormControlID(this._element.id, 'chkIsoproprylePass'), 'CheckBox');
        if (chkIsoproprylePass == true) {
            $find(this.getFormControlID(this._element.id, 'chkIsopropryleFail')).setChecked(false);
            $find(this.getFormControlID(this._element.id, 'chkIsopropryleNA')).setChecked(false);
        }

    },
    IsopropryleActeoneTestFail: function () {
        var chkIsopropryleFail = this.getControlValue(this.getFormControlID(this._element.id, 'chkIsopropryleFail'), 'CheckBox');
        if (chkIsopropryleFail == true) {
            $find(this.getFormControlID(this._element.id, 'chkIsoproprylePass')).setChecked(false);
            $find(this.getFormControlID(this._element.id, 'chkIsopropryleNA')).setChecked(false);
        }

    },
    IsopropryleActeoneTestNA: function () {
        var chkIsopropryleNA = this.getControlValue(this.getFormControlID(this._element.id, 'chkIsopropryleNA'), 'CheckBox');
        if (chkIsopropryleNA == true) {
            $find(this.getFormControlID(this._element.id, 'chkIsoproprylePass')).setChecked(false);
            $find(this.getFormControlID(this._element.id, 'chkIsopropryleFail')).setChecked(false);
        }
    },
    validatePackagingBreakdownData: function () {
        var data = [];
        $('tr.data-Breakdown-GI').each(function () {
            var FactorySealed = $(this).find('.f-FactorySealed01').is(":checked");
            var NumberofPacks = $(this).find('.n-NumberofPacks01').val();
            var PackSize = $(this).find('.p-PackSize01').val();
            var DateCode = $(this).find('.d-DateCode01').val();
            var BatchCode = $(this).find('.d-BatchCode01').val();
            var PackagingTypeId = $(this).find('.d-PackagingType01').val();
            var MFRLabelId = $(this).find('.d-MFRLabel01').val();
            var Total = $(this).find('.t-Total01').val();
            var alldata = {
                'FactorySealed': FactorySealed,
                'NumberofPacks': parseInt(NumberofPacks),
                'PackSize': parseInt(PackSize),
                'DateCode': DateCode,
                'BatchCode': BatchCode,
                'PackagingTypeId': parseInt(PackagingTypeId),
                'MFRLabelId': parseInt(MFRLabelId),
                'Total': parseInt(Total)
            }
            if (NumberofPacks > 0 || PackSize > 0)
                data.push(alldata);
        });
        return data;
    },
    refreshFieldChanged: function () {
       
        this._RefreshFieldChanges["txtCorrectPartNo"] = $R_FN.setCleanTextValue($get(this.getFormControlID(this._element.id, 'txtCorrectPartNo')).value);
        this._RefreshFieldChanges["ddlROHSStatus"] = $find(this.getFormControlID(this._element.id, 'ddlROHSStatus')).getValue();
        this._RefreshFieldChanges["txtLocation"] = $get(this.getFormControlID(this._element.id, 'txtLocation')).value;
        this._RefreshFieldChanges["ddlLot"] = $find(this.getFormControlID(this._element.id, 'ddlLot')).getValue();
        this._RefreshFieldChanges["txtSupplierPart"] = $get(this.getFormControlID(this._element.id, 'txtSupplierPart')).value;
        this._RefreshFieldChanges["cmbProducts"] = this.getControlValue(this.getFormControlID(this._element.id, 'cmbProducts'), 'Combo');
        this._RefreshFieldChanges["cmbPackage"] = this.getControlValue(this.getFormControlID(this._element.id, 'cmbPackage'), 'Combo');
        this._RefreshFieldChanges["cmbManufacturer"] = this.getControlValue(this.getFormControlID(this._element.id, 'cmbManufacturer'), 'Combo');
        this._RefreshFieldChanges["cmbCountryOfManufacture"] = this.getControlValue(this.getFormControlID(this._element.id, 'cmbCountryOfManufacture'), 'Combo');
        this._RefreshFieldChanges["txtShipInCost"] = $R_FN.setCleanTextValue($get(this.getFormControlID(this._element.id, 'txtShipInCost')).value);
        this._RefreshFieldChanges["txtQualityControlNotes"] = $get(this.getFormControlID(this._element.id, 'txtQualityControlNotes')).value;
        this._RefreshFieldChanges["ddlCountingMethod"] = $find(this.getFormControlID(this._element.id, 'ddlCountingMethod')).getValue();
        this._RefreshFieldChanges["txtPartMarkings"] = $get(this.getFormControlID(this._element.id, 'txtPartMarkings')).value;
        this._RefreshFieldChanges["txtAccountNotes"] = $get(this.getFormControlID(this._element.id, 'txtAccountNotes')).value;
        this._RefreshFieldChanges["txtPrice"] = $get(this.getFormControlID(this._element.id, 'txtPrice')).value;
        this._RefreshFieldChanges["txtPrice_IPO"] = $get(this.getFormControlID(this._element.id, 'txtPrice_IPO')).value;
        this._RefreshFieldChanges["ddlMsl"] = $find(this.getFormControlID(this._element.id, 'ddlMsl')).getValue();
        this._RefreshFieldChanges["QueryHICStatus"] = $find(this.getFormControlID(this._element.id, 'QueryHICStatus')).getValue();

        this._RefreshFieldChanges["ActeoneTestStatus"] = (this.getControlValue(this.getFormControlID(this._element.id, 'chkActeoneTestPass'), 'CheckBox') == true ? "1" : this.getControlValue(this.getFormControlID(this._element.id, 'chkActeoneTestFail'), 'CheckBox') == true ? "2" : this.getControlValue(this.getFormControlID(this._element.id, 'chkActeoneTestNA'), 'CheckBox') == true ? "3" : "-1");
        this._RefreshFieldChanges["txtActeoneTest"] = $get(this.getFormControlID(this._element.id, 'txtActeoneTest')).value;
        this._RefreshFieldChanges["IsopropryleStatus"] = (this.getControlValue(this.getFormControlID(this._element.id, 'chkIsoproprylePass'), 'CheckBox') == true ? "1" : this.getControlValue(this.getFormControlID(this._element.id, 'chkIsopropryleFail'), 'CheckBox') == true ? "2" : this.getControlValue(this.getFormControlID(this._element.id, 'chkIsopropryleNA'), 'CheckBox') == true ? "3" : "-1");
        this._RefreshFieldChanges["txtIsopropryle"] = $get(this.getFormControlID(this._element.id, 'txtIsopropryle')).value;
        this._RefreshFieldChanges["chkReqSerailNo"] = this.getControlValue(this.getFormControlID(this._element.id, 'chkReqSerailNo'), 'CheckBox');
        this._RefreshFieldChanges["chkLotCodeReq"] = (this.getControlValue(this.getFormControlID(this._element.id, 'chkLotCodeReq'), 'CheckBox')).toString();

        this._RefreshFieldChanges["ddlEnhancedInspection"] = $find(this.getFormControlID(this._element.id, 'ddlEnhancedInspection')).getValue();
        this._RefreshFieldChanges["txtGeneralInspectionNotes"] = $R_FN.setCleanTextValue($get(this.getFormControlID(this._element.id, 'txtGeneralInspectionNotes')).value);
        this._RefreshFieldChanges["chkbaking"] = (this.getControlValue(this.getFormControlID(this._element.id, 'chkbakingYes'), 'CheckBox') == true ? "1" : this.getControlValue(this.getFormControlID(this._element.id, 'chkbakingNo'), 'CheckBox') == true ? "2" : this.getControlValue(this.getFormControlID(this._element.id, 'chkbakingNA'), 'CheckBox') == true ? "3" : '-1');
        this._RefreshFieldChanges["chkInspectionConducted"] = (this.getControlValue(this.getFormControlID(this._element.id, 'chkInspectionConducted'), 'CheckBox')).toString();
        this._RefreshFieldChanges["HICStatus"] = $find(this.getFormControlID(this._element.id, 'HICStatus')).getValue();
        var PackagingBreakdownData = this.validatePackagingBreakdownData();
        this._RefreshFieldChanges["PackBreakDownJSON"] = JSON.stringify(PackagingBreakdownData);
        this._RefreshFieldChanges["txtQueryBakingLevel"] = $get(this.getFormControlID(this._element.id, 'txtQueryBakingLevel')).value;
        this._RefreshFieldChanges["txtPrintDateCode"] = $get(this.getFormControlID(this._element.id, 'txtPrintDateCode')).value;
        this._RefreshFieldChanges["txtQuantity"] = $get(this.getFormControlID(this._element.id, 'txtQuantity')).value;

        this._RefreshFieldChanges = JSON.parse(JSON.stringify(this._RefreshFieldChanges).replace(/\:null/gi, "\:\"\""));
        this._intEnhancedInspectionStatusId = $find(this.getFormControlID(this._element.id, 'ddlEnhancedInspection')).getValue();

        this._intGIlineBarcodesStatusId = $find(this.getFormControlID(this._element.id, 'ddlGILineBarcodesStatus')).getValue();

    },
    ChangeApprover: function () {
        if ((($find(this.getFormControlID(this._element.id, 'ddlNewSalesApprover')).getValue()) == null) && ($find(this.getFormControlID(this._element.id, 'ddlNewPurchasingApprover')).getValue()) == null) {
            alert("Please select new approver");
            return false;
        }
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/GILines");
        obj.set_DataObject("GILines");
        obj.set_DataAction("ChangeApprover");
        obj.addParameter("GI_QueryId", Get_GIQueryId());
        obj.addParameter("NewSalesApproverId", $find(this.getFormControlID(this._element.id, 'ddlNewSalesApprover')).getValue());
        obj.addParameter("NewPurchaseApproverId", $find(this.getFormControlID(this._element.id, 'ddlNewPurchasingApprover')).getValue());
        obj.addDataOK(Function.createDelegate(this, this.ChangeApproverOK));
        obj.addError(Function.createDelegate(this, this.ChangeApproverError));
        obj.addTimeout(Function.createDelegate(this, this.ChangeApproverError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },
    ChangeApproverError: function (args) {

    },
    ChangeApproverOK: function (args) {
        this.RefreshAllQueryMessage();
        this.RefreshAllApprovals();
        this.setControlValue("DropDown", this.getFormControlID(this._element.id, 'ddlNewSalesApprover'), "", "");
        this.setControlValue("DropDown", this.getFormControlID(this._element.id, 'ddlNewPurchasingApprover'), "", "");

        var ConfigureeModal = document.getElementById("ConfigureApproverModel");
        ConfigureeModal.style.display = "none";
    },
    findEnhancedInspectionStatus: function () {
        var result = $("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_ddlEnhancedInspection_ddl option:selected").val();
        var resultText = $("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_ddlEnhancedInspection_ddl option:selected").text();
        if (result > 0 && (resultText.toLowerCase() == ('Enhanced Inspection').toLowerCase() || resultText.toLowerCase() == ('Send for Outwork').toLowerCase() || resultText.toLowerCase() == ('EI And Outwork').toLowerCase())) {
            $('#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_lblHeaderEnhancedInpection').text("Yes");
        }
        else {
            $('#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_lblHeaderEnhancedInpection').text("No");
        }
    },
    RefreshAllApprovals: function () {
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/GILines");
        obj.set_DataObject("GILines");
        obj.set_DataAction("GetGIApprovals");
        obj.addParameter("GoodsInId", this._intGIID);
        obj.addParameter("GoodsInLineId", this._intLineID);
        obj.addDataOK(Function.createDelegate(this, this.RefreshAllApprovalsOK));
        obj.addError(Function.createDelegate(this, this.RefreshAllApprovalsError));
        obj.addTimeout(Function.createDelegate(this, this.RefreshAllApprovalsError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },
    RefreshAllApprovalsError: function (args) {

    },
    RefreshAllApprovalsOK: function (args) {
        res = args._result;
        ManageApprover = res;
        var ApprovalsDetails = "";
        var MessageBoxData = "";
        $("#ApprovalBody").html('');

        var ApprovalStatus = "";
        if (res.GIApprovals.length > 0) {
            $("#btnApprovalsChange").click(function () {
                ConfigureApprover(ManageApprover.GIApprovals[0].Gi_QueryId, ManageApprover.GIApprovals[0].CurrentPurchasingApprover, ManageApprover.GIApprovals[0].CurrentSalesApprover, ManageApprover.GIApprovals[0].ParentPurchaseApprovalStatus, ManageApprover.GIApprovals[0].ParentSalesApprovalStatus);
            });
            $("#spnApprovalsChange").show();
            for (var i = 0; i < res.GIApprovals.length; i++) {
                var row = res.GIApprovals[i];
                $("#lblSalesStatus").text(row.SalesApprovalStatus);
                $("#lblPurchasingStatus").text(row.PurchasingApprovalStatus);
                $("#lblQualityStatus").text(row.QualityApprovalStatus);
                if (row.QualityApprovalStatus.toUpperCase() == "N/A") {
                    $("#lblQualityUser").text("Approval not sent");
                }
                else {
                    $("#lblQualityUser").text("Quality inbox");
                }
                if (row.PurchasingApprovalStatus.toUpperCase() == "N/A") {
                    $("#lblPurchasingUser").text("Approval not sent");
                }
                else {
                    $("#lblPurchasingUser").text(row.CurrentPurchasingApprover);
                }
                if (row.SalesApprovalStatus.toUpperCase() == "N/A") {
                    $("#lblSalesUser").text("Approval not sent");
                }
                else {
                    $("#lblSalesUser").text(row.CurrentSalesApprover);
                }
                ApprovalsDetails += "<tr>";
                ApprovalsDetails += "<td>" + row.RaisedBy + "</td>";
                ApprovalsDetails += "<td>" + row.ApprovalName + "</td>";
                ApprovalsDetails += row.Department == "Final Messase" ? "<td></td>" : "<td>" + row.ApprovedDate + "</td>";
                ApprovalsDetails += row.Status == "Request Declined" ? "<td><span class='redtext'>" + row.Status + "</span>" : row.Status == "Request Approved" ? "<td><span class='greentext'>" + row.Status + "</span>" : row.Status == "Partial Approved" ? "<td><span class='greentext'>" + row.Status + "</span>": "<td><span class=>" + row.Status + "</span>" + "</td>";
                ApprovalsDetails += "</tr>";
                row = null;
            }
        }
        else {
            ApprovalsDetails += "<p> No Data found.</p>"
            $("#lblSalesUser").text("Not Set");
            $("#lblPurchasingUser").text("Not Set");
            $("#lblQualityUser").text("Not Set");
            $("#spnApprovalsChange").hide();
            $("#lblSalesStatus").text("N/A");
            $("#lblPurchasingStatus").text("N/A");
            $("#lblQualityStatus").text("N/A");
        }

        $("#ApprovalBody").append(ApprovalsDetails);
        QueryMessageDetails = "";
    },
    DraftQueryMessage: function () {
        var Message = '';
        if ($get(this.getFormControlID(this._element.id, 'TxtMessageBox')).value.length > 0) {
            Message = $get(this.getFormControlID(this._element.id, 'TxtMessageBox')).value;
        }
        else if ($get(this.getFormControlID(this._element.id, 'txtResponceBox')).value.length > 0) {
            Message = $get(this.getFormControlID(this._element.id, 'txtResponceBox')).value;
        }
        if (Message.length > 0) {
            var obj = new Rebound.GlobalTrader.Site.Data();
            obj.set_PathToData("controls/Nuggets/GILines");
            obj.set_DataObject("GILines");
            obj.set_DataAction("DraftGILineQueryMessage");
            obj.addParameter("GoodsInId", this._intGIID);
            obj.addParameter("GoodsInLineId", this._intLineID);
            obj.addParameter("QueryMessage", Message);
            obj.addParameter("GI_QueryId", Get_GIQueryId());
            obj.addDataOK(Function.createDelegate(this, this.DraftQueryMessageOK));
            obj.addError(Function.createDelegate(this, this.DraftQueryMessageError));
            obj.addTimeout(Function.createDelegate(this, this.DraftQueryMessageError));
            $R_DQ.addToQueue(obj);
            $R_DQ.processQueue();
            obj = null;
        }
        else {
            alert("Please write something in the discussion box.");
        }
    },
    DraftQueryMessageError: function (args) {

    },
    DraftQueryMessageOK: function (args) {
        alert('Saved to draft');
    },
    deletePDF: function (intPDF, FileName) {
        if (confirm("Do you want to delete PDF.") == true) {
            var obj = new Rebound.GlobalTrader.Site.Data();
            obj.set_PathToData("Controls/Nuggets/GILines");
            obj.set_DataObject("GILines");
            obj.set_DataAction("DeletePDF");
            obj.addParameter("id", intPDF);
            obj.addParameter("pdffilename", FileName);
            obj.addDataOK(Function.createDelegate(this, this.deletePDFOK));
            obj.addError(Function.createDelegate(this, this.deletePDFError));
            obj.addTimeout(Function.createDelegate(this, this.deletePDFError));
            $R_DQ.addToQueue(obj);
            $R_DQ.processQueue();
            obj = null;
        }
    },
    deletePDFError: function (args) {

    },
    deletePDFOK: function (args) {
        this.getAttachementsData();
    },
    deleteImage: function (intImage, FileName) {
        if (confirm("Do you want to delete Image.") == true) {
            var obj = new Rebound.GlobalTrader.Site.Data();
            obj.set_PathToData("Controls/Nuggets/GILines");
            obj.set_DataObject("GILines");
            obj.set_DataAction("DeleteImage");
            obj.addParameter("id", intImage);
            obj.addParameter("ImageFileName", FileName);
            obj.addDataOK(Function.createDelegate(this, this.deleteImageOK));
            obj.addError(Function.createDelegate(this, this.deleteImageError));
            obj.addTimeout(Function.createDelegate(this, this.deleteImageError));
            $R_DQ.addToQueue(obj);
            $R_DQ.processQueue();
            obj = null;
        }
    },
    deleteImageError: function (args) {

    },
    deleteImageOK: function (args) {
        this.getAttachementsData();
    },
    RenameImage: function (id, CaptionName, type) {
        $get(this.getFormControlID(this._element.id, 'txtRenameCaption')).value = CaptionName;
        ShowRenamePopUp(id, CaptionName);
        this._intAttachmentId = id;
        this._strAttachmentType = type;
    },
    RenameCaption: function () {
        if ($get(this.getFormControlID(this._element.id, 'txtRenameCaption')).value.length == 0) {
            alert("Please write proper caption.");
            return false;
        }
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/GILines");
        obj.set_DataObject("GILines");
        obj.set_DataAction("RenameCaption");
        obj.addParameter("AttachmentId", this._intAttachmentId);
        obj.addParameter("Caption", $get(this.getFormControlID(this._element.id, 'txtRenameCaption')).value);
        obj.addParameter("AttachmentType", this._strAttachmentType);
        obj.addDataOK(Function.createDelegate(this, this.RenameCaptionOK));
        obj.addError(Function.createDelegate(this, this.RenameCaptionError));
        obj.addTimeout(Function.createDelegate(this, this.RenameCaptionError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },
    RenameCaptionError: function (args) {

    },
    RenameCaptionOK: function (args) {
        this.getAttachementsData();

        var RenameModel = document.getElementById("RenameModel");
        RenameModel.style.display = "none";
    },

    BulkAttachmentDelete: function () {
        if (confirm("Are you sure to delete selected attachments.") == true) {
            $('#divLoader').show();
            var obj = new Rebound.GlobalTrader.Site.Data();
            obj.set_PathToData("controls/Nuggets/GILines");
            obj.set_DataObject("GILines");
            obj.set_DataAction("BulkAttachmentDelete");
            obj.addParameter("PdfAttachments", GetPdfIds());
            obj.addParameter("ImageAttachments", GetImageIds());
            obj.addDataOK(Function.createDelegate(this, this.BulkAttachmentDeleteOK));
            obj.addError(Function.createDelegate(this, this.BulkAttachmentDeleteError));
            obj.addTimeout(Function.createDelegate(this, this.BulkAttachmentDeleteError));
            $R_DQ.addToQueue(obj);
            $R_DQ.processQueue();
            obj = null;
        }
    },
    BulkAttachmentDeleteError: function (args) {

    },
    BulkAttachmentDeleteOK: function (args) {
        this.getAttachementsData();
        ResetAttachmentData();
        $("#btnBulkAttacheDelete").hide();
        $('#divLoader').hide();
    },
    MadeChangeInQAddl: function () {
        QAData = $find(this.getFormControlID(this._element.id, 'ddlQualityApproved')).getValue();
        if (QAData == null) {
            this._blnQAIncludeApproverHtml = false;
        }
        else {
            this._blnQAIncludeApproverHtml = true;
        }
        
    },
    MadeChangeInSalesddl: function () {
        SalesData = $find(this.getFormControlID(this._element.id, 'ddlSalesApproved')).getValue();
        if (SalesData == null) {
            this._blnSalesDataIncludeApproverHtml = false;
        }
        else {
            this._blnSalesDataIncludeApproverHtml = true;
        }

    },
    MadeChangeInPurchaseddl: function () {
        PurchaseData = $find(this.getFormControlID(this._element.id, 'ddlPurchasingApproved')).getValue();
        if (PurchaseData == null) {
            this._blnPurchaseDataIncludeApproverHtml = false;
        }
        else {
            this._blnPurchaseDataIncludeApproverHtml = true;
        }

    },
  

    ChangeApproverResponse: function (htmlData1, C1, C2, C3, C4, C5, C6, C7, C8, C9, C10) {
        htmlData = $R_FN.setCleanTextValue(htmlData1);
        if (C1 == true) {
            htmlData = htmlData.replace('id="C1"', 'id="C1" checked="checked"');
            SetEnableCheckBoxIds("1");
        }
        if (C2 == true) {
            htmlData = htmlData.replace('id="C2"', 'id="C2" checked="checked"');
            SetEnableCheckBoxIds("2");
        }
        if (C3 == true) {
            htmlData = htmlData.replace('id="C3"', 'id="C3" checked="checked"');
            SetEnableCheckBoxIds("3");
        }
        if (C4 == true) {
            htmlData = htmlData.replace('id="C4"', 'id="C4" checked="checked"');
            SetEnableCheckBoxIds("4");
        }
        if (C5 == true) {
            htmlData = htmlData.replace('id="C5"', 'id="C5" checked="checked"');
            SetEnableCheckBoxIds("5");
        }
        if (C6 == true) {
            htmlData = htmlData.replace('id="C6"', 'id="C6" checked="checked"');
            SetEnableCheckBoxIds("6");
        }
        if (C7 == true) {
            htmlData = htmlData.replace('id="C7"', 'id="C7" checked="checked"');
            SetEnableCheckBoxIds("7");
        }
        if (C8 == true) {
            htmlData = htmlData.replace('id="C8"', 'id="C8" checked="checked"');
            SetEnableCheckBoxIds("8");
        }
        if (C9 == true) {
            htmlData = htmlData.replace('id="C9"', 'id="C9" checked="checked"');
            SetEnableCheckBoxIds("9");
        }
        if (C10 == true) {
            htmlData = htmlData.replace('id="C10"', 'id="C10" checked="checked"');
            SetEnableCheckBoxIds("10");
        }
        return htmlData;
    }
};

Rebound.GlobalTrader.Site.Controls.Forms.GILines_Edit.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.GILines_Edit", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
