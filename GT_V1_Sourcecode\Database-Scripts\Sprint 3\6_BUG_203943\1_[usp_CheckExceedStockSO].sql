SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
-- =============================================
-- Task:        BUG 203943
-- Author:		Cuong Do
-- Create date: 14 June 24
-- Description:	Check if there is a source exceeds stock
-- =============================================

GO

IF OBJECT_ID('[usp_CheckExceedStockSO]', 'P') IS NOT NULL
	DROP PROC [dbo].[usp_CheckExceedStockSO]
GO


CREATE PROCEDURE [dbo].[usp_CheckExceedStockSO]
	-- Add the parameters for the stored procedure here
	@SoId INT,
	@Result INT OUTPUT
AS
BEGIN

	select @Result = COUNT(*) FROM
	(
		SELECT SUM(sol.Quantity) AS totalLineQuality,
		   st.QuantityInStock
		FROM dbo.tbSalesOrderLine sol
			JOIN tbSourcingResult sr
				ON sr.SourcingResultId = sol.SourcingResultNo
			JOIN tbstock st
				ON sr.sourcingtable = 'HUBSTK'
				   AND sr.sourcingtableitemno = st.stockid
		WHERE sol.SalesOrderNo = @SoId
			  AND sr.IsReleased = 1
			  AND (sol.IsChecked = 1)
			  AND (sol.IsIPO = 1)
		GROUP BY sourcingtableitemno,
				 QuantityInStock
		HAVING SUM(sol.Quantity) > QuantityInStock
	) t
END
GO
