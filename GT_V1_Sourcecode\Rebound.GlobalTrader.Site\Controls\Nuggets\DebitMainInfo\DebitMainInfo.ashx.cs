//Marker     Changed by      Date         Remarks
//[002]     Shashi Keshar    07/10/2016   Lock update from Client
using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;
using System.Linq;

namespace Rebound.GlobalTrader.Site.Controls.Nuggets.Data {
	[WebService(Namespace = "http://tempuri.org/")]
	[WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
	public class DebitMainInfo : Rebound.GlobalTrader.Site.Data.Base {
		public override void ProcessRequest(HttpContext context) {
			if (base.init(context)) {
				switch (Action) {
					case "GetData": GetData(); break;
					case "SaveEdit": SaveEdit(); break;
                    case "ExportRelease": ExportRelease(); break;
					case "SaveDebitEmail": SaveDebitEmail(); break;
					case "SaveDebitPrint": SaveDebitPrint(); break;
					default: WriteErrorActionNotFound(); break;
				}
			}
		}
		/// <summary>
		/// get specific debit by key
		/// </summary>
		public JsonObject GetData(Debit debit) {
			JsonObject jsn = null;
			if (debit != null) {
				jsn = new JsonObject();
				jsn.AddVariable("DebitNumber", debit.DebitNumber);

				string companyNotes;
                if (debit.InternalPurchaseOrderNo.HasValue && debit.InternalPurchaseOrderNo.Value > 0)
                {
                    if (SessionManager.IsPOHub.Value)
                    {
						jsn.AddVariable("SupplierNo", debit.CompanyNo);
						jsn.AddVariable("SupplierName", debit.CompanyName);
						companyNotes = Company.GetAdvisoryNotes(debit.CompanyNo);
                    }
                    else
                    {
						jsn.AddVariable("SupplierNo", debit.POHubCompanyNo);
						jsn.AddVariable("SupplierName", debit.POHubCompanyName);
						companyNotes = Company.GetAdvisoryNotes(debit.POHubCompanyNo ?? 0);
					}
				}
                else
                {
                    jsn.AddVariable("SupplierNo", debit.CompanyNo);
                    jsn.AddVariable("SupplierName", debit.CompanyName);
					companyNotes = Company.GetAdvisoryNotes(debit.CompanyNo);
				}
				jsn.AddVariable("SupplierAdvisoryNotes", Functions.ReplaceLineBreaks(companyNotes));
				jsn.AddVariable("Contact", debit.ContactName);
				jsn.AddVariable("ContactNo", debit.ContactNo);
				jsn.AddVariable("BuyerNo", debit.Buyer);
				jsn.AddVariable("Buyer", debit.BuyerName);
				jsn.AddVariable("RaisedBy", debit.RaisedBy);
				jsn.AddVariable("Raiser", debit.RaiserName);
				jsn.AddVariable("Division", debit.DivisionName);
				jsn.AddVariable("PurchaseOrder", debit.PurchaseOrderNumber);
				jsn.AddVariable("PurchaseOrderNo", debit.PurchaseOrderNo);
				jsn.AddVariable("SupplierRMA", debit.SupplierRMANumber);
				jsn.AddVariable("SupplierRMANo", debit.SupplierRMANo);
				jsn.AddVariable("DebitDate", Functions.FormatDate(debit.DebitDate));
				jsn.AddVariable("ReferenceDate", Functions.FormatDate(debit.ReferenceDate));
				jsn.AddVariable("Currency", Functions.FormatCurrencyDescription(debit.CurrencyDescription, debit.CurrencyCode));
				jsn.AddVariable("CurrencyNo", debit.CurrencyNo);
				jsn.AddVariable("CurrencyCode", debit.CurrencyCode);
				jsn.AddVariable("DLUP", Functions.FormatDLUP(debit.DLUP, debit.UpdatedBy));
				jsn.AddVariable("SupplierInvoice", debit.SupplierInvoice);
				jsn.AddVariable("SupplierCredit", debit.SupplierCredit);
				jsn.AddVariable("SupplierReturn", debit.SupplierRMA);
				string strFreight = Functions.FormatCurrency(debit.Freight, debit.CurrencyCode, 2,true);
				if (debit.CurrencyNo != SessionManager.ClientCurrencyID) strFreight = string.Format("{0} ({1})", strFreight, Functions.FormatCurrency(BLL.Currency.ConvertValueToBaseCurrency(debit.Freight, debit.CurrencyNo, debit.DebitDate), SessionManager.ClientCurrencyCode, 2,true));
				jsn.AddVariable("Freight", strFreight);
				jsn.AddVariable("FreightVal", Functions.FormatCurrency(debit.Freight, null, true));
				jsn.AddVariable("TaxNo", debit.TaxNo);
				jsn.AddVariable("Tax", debit.TaxName);
				jsn.AddVariable("Notes", Functions.ReplaceLineBreaks(debit.Notes));
				jsn.AddVariable("Instructions", Functions.ReplaceLineBreaks(debit.Instructions));
				jsn.AddVariable("DivisionNo", debit.DivisionNo);
                //[001] code start
                jsn.AddVariable("Incoterm", debit.IncotermsName);
                jsn.AddVariable("IncotermNo", debit.IncotermsNo);

                jsn.AddVariable("InternalPurchaseOrderNo", debit.InternalPurchaseOrderNo);
                jsn.AddVariable("InternalPurchaseOrderNumber", debit.InternalPurchaseOrderNumber);
                jsn.AddVariable("IsPoHub", SessionManager.IsPOHub == true ? false : true);
                jsn.AddVariable("RefNumber", Functions.FormatNumeric(debit.RefNumber));
              
                //[001] code end
                //[002] Start Here
                jsn.AddVariable("isAutoGenerated", debit.DebitParentId > 0 ? true : false);
                jsn.AddVariable("ishublocked", debit.ishublocked);
                jsn.AddVariable("DebClientNo", debit.ClientNo);
                //[002] End Here
                jsn.AddVariable("isExport", debit.isExport);
				
				jsn.AddVariable("DateExported", Functions.FormatDate(debit.DateExported));
				jsn.AddVariable("CanBeExported", debit.CanBeExported);
				jsn.AddVariable("URNNumber", debit.URNNumber);
				


				Company objCmp = Company.GetSupplierStatusMessage(debit.CompanyNo);
				if (objCmp != null)
					jsn.AddVariable("SuppMessage", Functions.ReplaceLineBreaks(objCmp.SupplierMessage));
				else
					jsn.AddVariable("SuppMessage", "");


			}
			debit = null;
			return jsn;
		}
		public void GetData() {
			Debit debit = Debit.Get(ID);
			OutputResult(GetData(debit));
			debit = null;
		}

		/// <summary>
		/// Update an existing debit
		/// </summary>
		public void SaveEdit() {
            try
            {    //[002] Start Here
                   bool? isLockUpdateClient = false;
                isLockUpdateClient = GetFormValue_NullableBoolean("LockUpdateClient");
				//[002] End Here
				
				bool blnResult = Debit.Update(
					ID
					, GetFormValue_String("SupplierInvoice")
					, GetFormValue_String("SupplierReturn")
					, GetFormValue_String("SupplierCredit")
					, GetFormValue_Double("Freight")
					, GetFormValue_String("Notes")
					, GetFormValue_String("Instructions")
					, GetFormValue_Int("DivisionNo")
					, GetFormValue_Int("Buyer")
					, GetFormValue_Int("RaisedBy")
					, GetFormValue_DateTime("DebitDate")
					, GetFormValue_DateTime("ReferenceDate")
					, GetFormValue_Int("TaxNo")
					, GetFormValue_Int("CurrencyNo")
					, LoginID
                     //[001] code start
					
				   , GetFormValue_NullableInt("IncotermNo")
                    //[001] code end
                    , isLockUpdateClient
					 , GetFormValue_Int("URNNumber")
				);
				JsonObject jsn = new JsonObject();
				jsn.AddVariable("Result", blnResult);
				OutputResult(jsn);
				jsn.Dispose();
				jsn = null;
			} catch (Exception e) {
				WriteError(e);
			}
		}

        /// <summary>
        /// Export or Release an existing Debit Note
        /// </summary>
        public void ExportRelease()
        {
            try
            {
                bool blnResult = Debit.UpdateExport(
                    ID,
                    LoginID,
                    GetFormValue_Boolean("Exported")
                );
                JsonObject jsn = new JsonObject();
                jsn.AddVariable("Result", blnResult);
                OutputResult(jsn);
                jsn.Dispose();
                jsn = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }

		private void SaveDebitEmail()
		{
			try
			{
				JsonObject jsn = new JsonObject();
				int CreditsCount = GetFormValue_String("Debits").Split(',').Count();
				List<string> lstResult = BLL.Debit.InsertIntoDebitEmail(GetFormValue_String("Debits"), SessionManager.LoginID);
				if ((CreditsCount == lstResult.Count) && lstResult.Count > 0)
				{
					jsn.AddVariable("Result", 1);
					jsn.AddVariable("Credits", lstResult.Aggregate((a, b) => (a + "," + b)));
				}
				else if ((CreditsCount != lstResult.Count) && lstResult.Count != 0)
				{
					jsn.AddVariable("Result", 2);
					jsn.AddVariable("Credits", lstResult.Aggregate((a, b) => (a + "," + b)));
				}
				else
				{
					jsn.AddVariable("Result", 3);
				}
				OutputResult(jsn);
				jsn.Dispose(); jsn = null;
			}
			catch (Exception e)
			{
				WriteError(e);
			}
		}

		private void SaveDebitPrint()
		{
			try
			{
				JsonObject jsn = new JsonObject();
				//int CreditsCount = GetFormValue_String("Debits").Split(',').Count();
				int? intResult = BLL.Debit.InsertIntoDebitPrint(GetFormValue_String("DebitsPrintId"), SessionManager.LoginID);
				
				jsn.AddVariable("Result", intResult);
				jsn.AddVariable("Url", Convert.ToString(System.Configuration.ConfigurationManager.AppSettings["redirecturi"]));

				OutputResult(jsn);
				jsn.Dispose(); jsn = null;
			}
			catch (Exception e)
			{
				WriteError(e);
			}
		}
	}
}
