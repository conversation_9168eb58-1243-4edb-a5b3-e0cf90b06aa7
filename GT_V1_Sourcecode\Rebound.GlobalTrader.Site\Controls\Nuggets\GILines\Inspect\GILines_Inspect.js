Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.GILines_Inspect=function(n){Rebound.GlobalTrader.Site.Controls.Forms.GILines_Inspect.initializeBase(this,[n]);this._intLineID=-1;this._intGIID=0;this._ctlConfirm=null;this._ShipInCost=0;this._PurchasePrice=0;this._ClientPurchasePrice=0;this._inspectedBy=0;this._alertMessage="";this._inspectionStatus=0;this._isCloseInspection=0};Rebound.GlobalTrader.Site.Controls.Forms.GILines_Inspect.prototype={get_intGIID:function(){return this._intGIID},set_intGIID:function(n){this._intGIID!==n&&(this._intGIID=n)},get_intLineID:function(){return this._intLineID},set_intLineID:function(n){this._intLineID!==n&&(this._intLineID=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Forms.GILines_Inspect.callBaseMethod(this,"initialize");this.addShown(Function.createDelegate(this,this.formShown));this.getGoodsIn()},dispose:function(){this.isDisposed||(this._ctlConfirm&&this._ctlConfirm.dispose(),this._ctlConfirm=null,this._intLineID=null,this._intGIID=null,Rebound.GlobalTrader.Site.Controls.Forms.GILines_Inspect.callBaseMethod(this,"dispose"))},formShown:function(){this.checkGiLineInspectionStatus();this._blnFirstTimeShown&&(this._ctlConfirm=this.getFieldComponent("ctlConfirm"),this._ctlConfirm.addClickYesEvent(Function.createDelegate(this,this.yesClicked)),this._ctlConfirm.addClickNoEvent(Function.createDelegate(this,this.noClicked)))},getGoodsIn:function(){$R_FN.showElement(this._pnlLines,!1);var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/GIMainInfo");n.set_DataObject("GIMainInfo");n.set_DataAction("GetData");n.addParameter("ID",this._intGIID);n.addDataOK(Function.createDelegate(this,this.getGoodsInOK));n.addError(Function.createDelegate(this,this.getGoodsInError));n.addTimeout(Function.createDelegate(this,this.getGoodsInError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getGoodsInOK:function(n){var t=n._result;this.setFieldsFromGoodsIn(t)},setFieldsFromGoodsIn:function(n){n&&(this.setFieldValue("ctlGoodsIn",n.GoodsInNumber),this.setFieldValue("ctlSupplier",n.SupplierName))},getGoodsInError:function(n){this._strErrorMessage=n._errorMessage;this.onSaveError()},yesClicked:function(){var r,n;if(this._isCloseInspection==!0){var t=!1,i="";this._PurchasePrice==0&&(this._PurchasePrice=this._ClientPurchasePrice);this._ShipInCost==0?(t=!0,i="Ship In Cost value is 0. Are you sure want to release stock?"):this._PurchasePrice==0?(t=!0,i="Purchase Price is 0. Are you sure want to release stock?"):(t=!1,i="");r=!0;t==!0&&(r=confirm(i)==!0?!0:!1);r==!0&&(this.showSaving(!0),n=new Rebound.GlobalTrader.Site.Data,n.set_PathToData("Controls/Nuggets/GILines"),n.set_DataObject("GILines"),n.set_DataAction("Inspect"),n.addParameter("id",this._intLineID),n.addDataOK(Function.createDelegate(this,this.saveComplete)),n.addError(Function.createDelegate(this,this.saveError)),n.addTimeout(Function.createDelegate(this,this.saveError)),$R_DQ.addToQueue(n),$R_DQ.processQueue(),n=null,this.onSave())}else alert(this._alertMessage)},noClicked:function(){this.showSaving(!1);this.onNotConfirmed()},saveError:function(n){this.showSaving(!1);this._strErrorMessage=n._errorMessage;this.onSaveError()},saveComplete:function(n){this.showSaving(!1);n._result.Result==!0?this.onSaveComplete():(this._strErrorMessage=n._errorMessage,this.onSaveError())},checkGiLineInspectionStatus:function(){var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/GILines");n.set_DataObject("GILines");n.set_DataAction("GetGiInspectionStatus");n.addParameter("ID",this._intLineID);n.addDataOK(Function.createDelegate(this,n=>{var t=n._result;this._isCloseInspection=t.ISCloseInspection;this._inspectionStatus=t.InspectionStatus;this._inspectedBy=t.InspectedBy;this._alertMessage=t.AlertMessage}));n.addError(Function.createDelegate(this,n=>{this._strErrorMessage=n._errorMessage,this.onSaveError()}));n.addTimeout(Function.createDelegate(this,n=>{this._strErrorMessage=n._errorMessage,this.onSaveError()}));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null}};Rebound.GlobalTrader.Site.Controls.Forms.GILines_Inspect.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.GILines_Inspect",Rebound.GlobalTrader.Site.Controls.Forms.Base,Sys.IDisposable);