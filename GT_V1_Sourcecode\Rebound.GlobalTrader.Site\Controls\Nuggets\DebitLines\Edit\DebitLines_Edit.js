Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.DebitLines_Edit=function(n){Rebound.GlobalTrader.Site.Controls.Forms.DebitLines_Edit.initializeBase(this,[n]);this._intLineID=-1;this._blnLineIsService=!1;this._blnProductHaza=!1};Rebound.GlobalTrader.Site.Controls.Forms.DebitLines_Edit.prototype={get_lblCurrency:function(){return this._lblCurrency},set_lblCurrency:function(n){this._lblCurrency!==n&&(this._lblCurrency=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Forms.DebitLines_Edit.callBaseMethod(this,"initialize");this.addShown(Function.createDelegate(this,this.formShown))},formShown:function(){this._blnFirstTimeShown&&this.addSave(Function.createDelegate(this,this.saveClicked));this.showField("ctlPartNo",!this._blnLineIsService);this.showField("ctlManufacturer",!this._blnLineIsService);this.showField("ctlDateCode",!this._blnLineIsService);this.showField("ctlPackage",!this._blnLineIsService);this.showField("ctlProduct",!this._blnLineIsService);this.showField("ctlService",this._blnLineIsService);this.showField("ctlServiceDescription",this._blnLineIsService);this.enableFieldCheckBox("ctlPrintHazWar",this._blnProductHaza)},dispose:function(){this.isDisposed||(this._lblCurrency=null,this._intLineID=null,this._blnLineIsService=null,this._blnProductHaza=null,Rebound.GlobalTrader.Site.Controls.Forms.DebitLines_Edit.callBaseMethod(this,"dispose"))},saveClicked:function(){if(this.validateForm()){var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/DebitLines");n.set_DataObject("DebitLines");n.set_DataAction("SaveEdit");n.addParameter("id",this._intLineID);n.addParameter("Quantity",this.getFieldValue("ctlQuantity"));n.addParameter("Price",this.getFieldValue("ctlPrice"));n.addParameter("LineIsService",this._blnLineIsService);n.addParameter("LineNotes",this.getFieldValue("ctlLineNotes"));this._blnLineIsService&&(n.addParameter("Service",this.getFieldValue("ctlService")),n.addParameter("ServiceDescription",this.getFieldValue("ctlServiceDescription")));n.addParameter("PrintHazWar",this.getFieldValue("ctlPrintHazWar"));n.addDataOK(Function.createDelegate(this,this.saveEditComplete));n.addError(Function.createDelegate(this,this.saveEditError));n.addTimeout(Function.createDelegate(this,this.saveEditError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null}},saveEditError:function(n){this._strErrorMessage=n._errorMessage;this.onSaveError()},saveEditComplete:function(n){n._result.Result==!0?this.onSaveComplete():(this._strErrorMessage=n._errorMessage,this.onSaveError())},validateForm:function(){this.onValidate();var n=!0;return this._blnLineIsService&&(this.checkFieldEntered("ctlService")||(n=!1)),this.checkFieldEntered("ctlQuantity")||(n=!1),this.checkFieldNumeric("ctlQuantity")||(n=!1),this.checkFieldEntered("ctlPrice")||(n=!1),this.checkFieldNumeric("ctlPrice")||(n=!1),n||this.showError(!0),n},setCurrency:function(n){$R_FN.setInnerHTML(this._lblCurrency,n)}};Rebound.GlobalTrader.Site.Controls.Forms.DebitLines_Edit.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.DebitLines_Edit",Rebound.GlobalTrader.Site.Controls.Forms.Base,Sys.IDisposable);