Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DataListNuggets");Rebound.GlobalTrader.Site.Controls.DataListNuggets.Debits=function(n){Rebound.GlobalTrader.Site.Controls.DataListNuggets.Debits.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.DataListNuggets.Debits.prototype={get_blnPOHub:function(){return this._blnPOHub},set_blnPOHub:function(n){this._blnPOHub!==n&&(this._blnPOHub=n)},get_IsGlobalLogin:function(){return this._IsGlobalLogin},set_IsGlobalLogin:function(n){this._IsGlobalLogin!==n&&(this._IsGlobalLogin=n)},get_IsGSA:function(){return this._IsGSA},set_IsGSA:function(n){this._IsGSA!==n&&(this._IsGSA=n)},initialize:function(){this.addSetupDataCallEvent(Function.createDelegate(this,this.setupDataCall));this.addGetDataOKEvent(Function.createDelegate(this,this.getDataOK));this.addInitCompleteEvent(Function.createDelegate(this,this.initAfterBaseIsReady));this._strPathToData="controls/DataListNuggets/Debits";this._strDataObject="Debits";Rebound.GlobalTrader.Site.Controls.DataListNuggets.Debits.callBaseMethod(this,"initialize")},initAfterBaseIsReady:function(){this.addPageTabChangedEvent(Function.createDelegate(this,this.pageTabChanged));this.updateFilterVisibility();this.getData()},dispose:function(){this.isDisposed||(this._IsGlobalLogin=null,this._IsGSA=null,Rebound.GlobalTrader.Site.Controls.DataListNuggets.Debits.callBaseMethod(this,"dispose"))},pageTabChanged:function(){this._table._intCurrentPage=1;this._enmViewLevel=this._intCurrentTab;this.updateFilterVisibility();this.getData()},setupDataCall:function(){this._objData.addParameter("ViewLevel",this._enmViewLevel);this._objData.addParameter("IsGlobalLogin",this._IsGlobalLogin)},getDataOK:function(){for(var n,i,t=0,r=this._objResult.Results.length;t<r;t++)n=this._objResult.Results[t],i=this._IsGlobalLogin?[$RGT_nubButton_DebitNote(n.ID,n.No),$R_FN.writeDoubleCellValue($R_FN.writePartNo(n.Part,n.ROHS),$RGT_nubButton_Manufacturer(n.MfrNo,n.Mfr)),$R_FN.writeDoubleCellValue($R_FN.showSupplierMessage(n.blnMakeYellow==!0?'<span style="background-color:yellow;">'+$RGT_nubButton_Company(n.CMNo,n.CM)+"<\/span>":$RGT_nubButton_Company(n.CMNo,n.CM),$R_FN.setCleanTextValue(n.SuppMessage)),$RGT_nubButton_Contact(n.ContactNo,n.Contact)),$R_FN.setCleanTextValue(n.Date),$RGT_nubButton_PurchaseOrder(n.PONo,n.PurchaseOrder),$R_FN.setCleanTextValue(n.SupplierInvoice),$R_FN.setCleanTextValue(n.ClientName)]:[$RGT_nubButton_DebitNote(n.ID,n.No),$R_FN.writeDoubleCellValue($R_FN.writePartNo(n.Part,n.ROHS),$RGT_nubButton_Manufacturer(n.MfrNo,n.Mfr)),$R_FN.writeDoubleCellValue($R_FN.showSupplierMessage(n.blnMakeYellow==!0?'<span style="background-color:yellow;">'+$RGT_nubButton_Company(n.CMNo,n.CM)+"<\/span>":$RGT_nubButton_Company(n.CMNo,n.CM),$R_FN.setCleanTextValue(n.SuppMessage)),$RGT_nubButton_Contact(n.ContactNo,n.Contact)),$R_FN.setCleanTextValue(n.Date),$RGT_nubButton_PurchaseOrder(n.PONo,n.PurchaseOrder),$R_FN.setCleanTextValue(n.SupplierInvoice)],this._table.addRow(i,n.ID,!1),i=null,n=null},updateFilterVisibility:function(){this.getFilterField("ctlBuyerName").show(this._enmViewLevel!=0);this.getFilterField("ctlPohubOnly").show(this._blnPOHub);this.getFilterField("ctlPohubOnly").enableField(this._blnPOHub);this.getFilterField("ctlClientName").show(this._IsGlobalLogin||this._IsGSA)}};Rebound.GlobalTrader.Site.Controls.DataListNuggets.Debits.registerClass("Rebound.GlobalTrader.Site.Controls.DataListNuggets.Debits",Rebound.GlobalTrader.Site.Controls.DataListNuggets.Base,Sys.IDisposable);