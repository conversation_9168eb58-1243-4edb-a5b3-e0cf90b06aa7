///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//
// RP 06.10.2009:
// - Changes due to changes in base class
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");

Rebound.GlobalTrader.Site.Controls.DropDowns.ShipSOReadyStatus = function(element) { 
    Rebound.GlobalTrader.Site.Controls.DropDowns.ShipSOReadyStatus.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.DropDowns.ShipSOReadyStatus.prototype = {
    get_blnAllTab: function () { return this._blnAllTab; }, set__blnAllTab: function (value) { if (this._blnAllTab !== value) this._blnAllTab = value; }, 
	get_blnLimitToCurrentUsersDivision: function() { return this._blnLimitToCurrentUsersDivision; }, set_blnLimitToCurrentUsersDivision: function(v) { if (this._blnLimitToCurrentUsersDivision !== v) this._blnLimitToCurrentUsersDivision = v; },
	get_blnLimitToCurrentUsersTeam: function() { return this._blnLimitToCurrentUsersTeam; }, set_blnLimitToCurrentUsersTeam: function(v) { if (this._blnLimitToCurrentUsersTeam !== v) this._blnLimitToCurrentUsersTeam = v; },
	get_blnExcludeCurrentUser: function() { return this._blnExcludeCurrentUser; }, set_blnExcludeCurrentUser: function(v) { if (this._blnExcludeCurrentUser !== v) this._blnExcludeCurrentUser = v; },
	get_intGlobalLoginClientNo: function () { return this._intGlobalLoginClientNo; }, set_intGlobalLoginClientNo: function (v) { if (this._intGlobalLoginClientNo !== v) this._intGlobalLoginClientNo = v; },

	initialize: function() {
		this.addSetupDataCall(Function.createDelegate(this, this.setupDataCall));
		this.addGetDataOK(Function.createDelegate(this, this.dataCallOK));
        Rebound.GlobalTrader.Site.Controls.DropDowns.ShipSOReadyStatus.callBaseMethod(this, "initialize");
	},
	
	dispose: function() { 
		if (this.isDisposed) return;
		this._blnLimitToCurrentUsersDivision = null;
		this._blnLimitToCurrentUsersTeam = null;
		this._blnExcludeCurrentUser = null;
        this._intGlobalLoginClientNo = null;
        this._blnAllTab = null;
		Rebound.GlobalTrader.Site.Controls.DropDowns.Employee.callBaseMethod(this, "dispose");
	},
	
	setupDataCall: function() { 
        this._objData.set_PathToData("controls/DropDowns/ShipSOReadyStatus");
        this._objData.set_DataObject("ShipSOReadyStatus");
        this._objData.set_DataAction("GetData");
        this._objData.addParameter("AllTab", this._blnAllTab);
		this._objData.addParameter("LimitToCurrentUsersTeam", this._blnLimitToCurrentUsersTeam);
		this._objData.addParameter("LimitToCurrentUsersDivision", this._blnLimitToCurrentUsersDivision);
		this._objData.addParameter("ExcludeCurrentUser", this._blnExcludeCurrentUser);
		this._objData.addParameter("GlobalLoginClientNo", this._intGlobalLoginClientNo);
	},
	
	dataCallOK: function() { 
		var result = this._objData._result;
        if (result.ShipSOReadyStatus) {
            for (var i = 0; i < result.ShipSOReadyStatus.length; i++) {
                this.addOption(result.ShipSOReadyStatus[i].Name, result.ShipSOReadyStatus[i].ID);
			}
		}
	}
	
};

Rebound.GlobalTrader.Site.Controls.DropDowns.ShipSOReadyStatus.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.ShipSOReadyStatus", Rebound.GlobalTrader.Site.Controls.DropDowns.Base);
