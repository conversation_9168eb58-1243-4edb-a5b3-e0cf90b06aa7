﻿
using System;
using System.Data;
using System.Data.SqlClient;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;
using System.Web.Caching;
using System.Data.Common;

namespace Rebound.GlobalTrader.DAL.SqlClient
{
    public class SqlShortShipmentProvider : ShortShipmentProvider
    {
        /// <summary>
        /// DataListNugget 
        /// Calls [usp_datalistnugget_ShortShipment]
        /// </summary>
        public override List<ShortShipmentDetails> DataListNugget(System.Int32? ClientID, System.Int32? OrderBy, System.Int32? SortDir, System.Int32? PageIndex, System.Int32? PageSize, System.String Supplier, System.Int32? PurchaseOrderNoLo, System.Int32? PurchaseOrder<PERSON>o<PERSON>i, System.DateTime? DateReceived, System.Int32? QuantityOrderedLo, System.Int32? QuantityOrderedHi, System.Int32? QuantityReceivedLo, System.Int32? QuantityReceivedHi, System.Int32? ShortageQuantityLo, System.Int32? ShortageQuantityHi, System.Double? ShortageValueLo, System.Double? ShortageValueHi, System.Boolean? IsShortageRefundIssue, System.Int32? Status, System.Int32? GINumberLo, System.Int32? GINumberHi)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_selectall_shortshipment", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;
                cmd.Parameters.Add("@ClientID", SqlDbType.Int).Value = ClientID;
                cmd.Parameters.Add("@OrderBy", SqlDbType.Int).Value = OrderBy;
                cmd.Parameters.Add("@SortDir", SqlDbType.Int).Value = SortDir;
                cmd.Parameters.Add("@PageIndex", SqlDbType.Int).Value = PageIndex;
                cmd.Parameters.Add("@PageSize", SqlDbType.Int).Value = PageSize;
                cmd.Parameters.Add("@Supplier", SqlDbType.NVarChar).Value = Supplier;
                cmd.Parameters.Add("@PurchaseOrderNoLo", SqlDbType.Int).Value = PurchaseOrderNoLo;
                cmd.Parameters.Add("@PurchaseOrderNoHi", SqlDbType.Int).Value = PurchaseOrderNoHi;
                cmd.Parameters.Add("@DateReceived", SqlDbType.DateTime).Value = DateReceived;
                cmd.Parameters.Add("@QuantityOrderedLo", SqlDbType.Int).Value = QuantityOrderedLo;
                cmd.Parameters.Add("@QuantityOrderedHi", SqlDbType.Int).Value = QuantityOrderedHi;
                cmd.Parameters.Add("@QuantityReceivedLo", SqlDbType.Int).Value = QuantityReceivedLo;
                cmd.Parameters.Add("@QuantityReceivedHi", SqlDbType.Int).Value = QuantityReceivedHi;
                cmd.Parameters.Add("@ShortageQuantityLo", SqlDbType.Int).Value = ShortageQuantityLo;
                cmd.Parameters.Add("@ShortageQuantityHi", SqlDbType.Int).Value = ShortageQuantityHi;
                cmd.Parameters.Add("@ShortageValueLo", SqlDbType.Float).Value = ShortageValueLo;
                cmd.Parameters.Add("@ShortageValueHi", SqlDbType.Float).Value = ShortageValueHi;
                cmd.Parameters.Add("@IsShortageRefundIssue", SqlDbType.Bit).Value = IsShortageRefundIssue;
                cmd.Parameters.Add("@Status", SqlDbType.Int).Value = Status;
                cmd.Parameters.Add("@GINumberLo", SqlDbType.Int).Value = GINumberLo;
                cmd.Parameters.Add("@GINumberHi", SqlDbType.Int).Value = GINumberHi;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<ShortShipmentDetails> lst = new List<ShortShipmentDetails>();
                while (reader.Read())
                {
                    ShortShipmentDetails obj = new ShortShipmentDetails();
                    obj.ShortShipmentId = GetReaderValue_Int32(reader, "ShortShipmentId", 0);
                    obj.Supplier = GetReaderValue_String(reader, "Supplier", "");
                    obj.PurchaseOrderNo = GetReaderValue_NullableInt32(reader, "PurchaseOrderNo", null);
                    obj.IPONo = GetReaderValue_NullableInt32(reader, "InternalPurchaseOrderNo", null);
                    obj.Salesman = GetReaderValue_String(reader, "Salesman", "");
                    obj.Reference = GetReaderValue_String(reader, "Reference", "");
                    obj.GoodsInNo = GetReaderValue_NullableInt32(reader, "GoodsInNo", null);
                    obj.DateReceived = GetReaderValue_DateTime(reader, "DateReceived", DateTime.MinValue);
                    obj.Raisedby = GetReaderValue_String(reader, "Raisedby", "");
                    obj.Buyer = GetReaderValue_String(reader, "Buyer", "");
                    obj.PartNo = GetReaderValue_String(reader, "PartNo", "");
                    obj.ManufacturerName = GetReaderValue_String(reader, "ManufacturerName", "");
                    obj.QuantityOrdered = GetReaderValue_NullableInt32(reader, "QuantityOrdered", null);
                    obj.QuantityAdvised = GetReaderValue_NullableInt32(reader, "QuantityAdvised", null);
                    obj.QuantityReceived = GetReaderValue_NullableInt32(reader, "QuantityReceived", null);
                    obj.ShortageQuantity = GetReaderValue_NullableInt32(reader, "ShortageQuantity", null);
                    obj.ShortageValue = GetReaderValue_NullableDouble(reader, "ShortageValue", null);
                    obj.IsShortageRefundIssue = GetReaderValue_NullableBoolean(reader, "IsShortageRefundIssue", null);
                    obj.ShortageRefundIssue = GetReaderValue_String(reader, "ShortageRefundIssue", "");
                    obj.RowCnt = GetReaderValue_NullableInt32(reader, "RowCnt", null);
                    obj.Completedby = GetReaderValue_String(reader, "Completedby", "");
                    obj.CompletedDate = GetReaderValue_DateTime(reader, "CompletedDate", DateTime.MinValue);
                    obj.Status = GetReaderValue_String(reader, "Status", "");
                    obj.GoodsInId = GetReaderValue_NullableInt32(reader, "GoodsInId", null);
                    //obj.ClientNo = GetReaderValue_NullableInt32(reader, "ClientNo", null)??0;
                    //[001]Code End
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get ShortShipments", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// DataListNugget 
        /// Calls [usp_datalistnugget_ShortShipment]
        /// </summary>
        public override Int32 Insert(System.Int32? Supplier, System.Int32? PurchaseOrderNo, System.Int32? Salesman, System.String Reference, System.Int32? GoodsInNo, System.DateTime? DateReceived, System.Int32? Raisedby, System.Int32? Buyer, System.String PartNo, System.Int32? ManufacturerNo, System.Int32? QuantityOrdered, System.Int32? QuantityAdvised, System.Int32? QuantityReceived, System.Int32? ShortageQuantity, System.Int32? ShortValue, System.Int32? Status)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_insert_shortshipment", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;
                cmd.Parameters.Add("@Supplier", SqlDbType.Int).Value = Supplier;
                cmd.Parameters.Add("@PurchaseOrderNo", SqlDbType.Int).Value = PurchaseOrderNo;
                cmd.Parameters.Add("@Salesman", SqlDbType.Int).Value = Salesman;
                cmd.Parameters.Add("@Reference", SqlDbType.NVarChar).Value = Reference;
                cmd.Parameters.Add("@GoodsInNo", SqlDbType.Int).Value = GoodsInNo;
                cmd.Parameters.Add("@DateReceived", SqlDbType.DateTime).Value = DateReceived;
                cmd.Parameters.Add("@Raisedby", SqlDbType.Int).Value = Raisedby;
                cmd.Parameters.Add("@Buyer", SqlDbType.Int).Value = Buyer;
                cmd.Parameters.Add("@PartNo", SqlDbType.NVarChar).Value = PartNo;
                cmd.Parameters.Add("@ManufacturerNo", SqlDbType.Int).Value = ManufacturerNo;
                cmd.Parameters.Add("@QuantityOrdered", SqlDbType.Int).Value = QuantityOrdered;
                cmd.Parameters.Add("@QuantityAdvised", SqlDbType.Int).Value = QuantityAdvised;
                cmd.Parameters.Add("@QuantityReceived", SqlDbType.Int).Value = QuantityReceived;
                cmd.Parameters.Add("@ShortageQuantity", SqlDbType.Int).Value = ShortageQuantity;
                cmd.Parameters.Add("@ShortValue", SqlDbType.Int).Value = ShortValue;
                cmd.Parameters.Add("@Status", SqlDbType.Int).Value = Status;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (Int32)cmd.Parameters["@ShortShipmentId"].Value;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get ShortShipments", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// Get 
        /// Calls [usp_select_GoodsInLine]
        /// </summary>
        public override ShortShipmentDetails GetShortShipmentById(System.Int32 ShortShipmentId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_select_shortshipment", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@ShortShipmentId", SqlDbType.Int).Value = ShortShipmentId;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd, CommandBehavior.SingleRow);
                if (reader.Read())
                {
                    ShortShipmentDetails obj = new ShortShipmentDetails();
                    obj.ShortShipmentId = GetReaderValue_Int32(reader, "ShortShipmentId", 0);
                    obj.Supplier = GetReaderValue_String(reader, "Supplier", "");
                    obj.PurchaseOrderNo = GetReaderValue_NullableInt32(reader, "PurchaseOrderNo", null);
                    obj.Salesman = GetReaderValue_String(reader, "Salesman", "");
                    obj.SalesmanId = GetReaderValue_NullableInt32(reader, "SalesmanId", null);
                    obj.Reference = GetReaderValue_String(reader, "Reference", "");
                    obj.GoodsInNo = GetReaderValue_NullableInt32(reader, "GoodsInNo", null);
                    obj.DateReceived = GetReaderValue_DateTime(reader, "DateReceived", DateTime.MinValue);
                    obj.Raisedby = GetReaderValue_String(reader, "Raisedby", "");
                    obj.RaisedbyId = GetReaderValue_NullableInt32(reader, "RaisedbyId", null);
                    obj.Buyer = GetReaderValue_String(reader, "Buyer", "");
                    obj.BuyerId = GetReaderValue_NullableInt32(reader, "BuyerId", null);
                    obj.PartNo = GetReaderValue_String(reader, "PartNo", "");
                    obj.ManufacturerName = GetReaderValue_String(reader, "ManufacturerName", "");
                    obj.QuantityOrdered = GetReaderValue_NullableInt32(reader, "QuantityOrdered", null);
                    obj.QuantityAdvised = GetReaderValue_NullableInt32(reader, "QuantityAdvised", null);
                    obj.QuantityReceived = GetReaderValue_NullableInt32(reader, "QuantityReceived", null);
                    obj.ShortageQuantity = GetReaderValue_NullableInt32(reader, "ShortageQuantity", null);
                    obj.ShortageValue = GetReaderValue_NullableDouble(reader, "ShortageValue", null);
                    obj.IsShortageRefundIssue = GetReaderValue_NullableBoolean(reader, "IsShortageRefundIssue", null);
                    obj.ShortageRefundIssue = GetReaderValue_String(reader, "ShortageRefundIssue", "");
                    obj.IsStageTwoUpdated = GetReaderValue_Boolean(reader, "IsStageTwoUpdated", false);
                    obj.IsCancel = GetReaderValue_Boolean(reader, "IsCancel", false);
                    obj.IsDebitNoteExists = GetReaderValue_Boolean(reader, "IsDebitNoteExists", false);
                    obj.IsPOHub = GetReaderValue_NullableBoolean(reader, "IsPOHub", false);
                    obj.IsClosed = GetReaderValue_Boolean(reader, "IsClosed", false);

                    obj.ManufacturerId = GetReaderValue_NullableInt32(reader, "ManufacturerId", null);
                    obj.GoodsInId = GetReaderValue_NullableInt32(reader, "GoodsInId", null);
                    obj.PurchaseOrderId = GetReaderValue_NullableInt32(reader, "PurchaseOrderId", null);
                    obj.DebitNoteNo = GetReaderValue_NullableInt32(reader, "DebitNoteNo", null);
                    obj.DebitNumber = GetReaderValue_NullableInt32(reader, "DebitNumber", null);
                    obj.ClientNo = GetReaderValue_NullableInt32(reader, "ClientNo", null);
                    obj.SupportTeamMemberNo = GetReaderValue_NullableInt32(reader, "SupportTeamMemberNo", null);
                    obj.StatusId = GetReaderValue_NullableInt32(reader, "StatusId", 0);
                    obj.Status = GetReaderValue_String(reader, "Status", "");
                    obj.CurrencyNo = GetReaderValue_NullableInt32(reader, "CurrencyNo", null);
                    obj.DebitIds = GetReaderValue_String(reader, "DebitIds", "");
                    obj.DebitNumbers = GetReaderValue_String(reader, "DebitNumbers", "");

                    obj.InternalPurchaseOrderId = GetReaderValue_NullableInt32(reader, "InternalPurchaseOrderId", null);
                    obj.InternalPurchaseOrderNo = GetReaderValue_NullableInt32(reader, "InternalPurchaseOrderNumber", null);
                    obj.ClientCurrencyCode = GetReaderValue_String(reader, "ClientCurrencyCode", "");
                    obj.CurrencyCode = GetReaderValue_String(reader, "CurrencyCode", "");
                    obj.CompanyAdvisoryNotes = GetReaderValue_String(reader, "CompanyAdvisoryNotes", "");
                    obj.MfrAdvisoryNotes = GetReaderValue_String(reader, "MfrAdvisoryNotes", "");

                    return obj;
                }
                else
                {
                    return null;
                }
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get ShortShipment", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// Update Short Shipment
        /// Calls [usp_update_shortshipment]
        /// </summary>
        public override bool UpdateShortShipment(System.Int32 ShortShipmentId, System.Boolean IsShortageRefundIssue, System.String ShortageRefundIssue, System.Int32 loginId, System.Int32? clientId, ref string message, ref int result, System.Int32? ShortShipmentStatusId, ref System.Int32 NoReplyId, ref System.String NoReplyEmail)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_update_shortshipment", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@ShortShipmentId", SqlDbType.Int).Value = ShortShipmentId;
                cmd.Parameters.Add("@IsShortageRefundIssue", SqlDbType.Bit).Value = IsShortageRefundIssue;
                cmd.Parameters.Add("@ShortageRefundIssue", SqlDbType.NVarChar, 500).Value = ShortageRefundIssue;
                cmd.Parameters.Add("@LoginId", SqlDbType.Int).Value = loginId;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@ShortShipmentStatusId", SqlDbType.Int).Value = ShortShipmentStatusId;
                cmd.Parameters.Add("@ErrorMessage", SqlDbType.VarChar, 200).Direction = ParameterDirection.Output;
                cmd.Parameters.Add("@Result", SqlDbType.Int).Direction = ParameterDirection.Output;
                cmd.Parameters.Add("@NoReplyId", SqlDbType.Int).Direction = ParameterDirection.Output;
                cmd.Parameters.Add("@NoReplyEmail", SqlDbType.VarChar, 100).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                message = cmd.Parameters["@ErrorMessage"].Value.ToString();
                result = Convert.ToInt16(cmd.Parameters["@Result"].Value);
                NoReplyEmail = cmd.Parameters["@NoReplyEmail"].Value.ToString();
                NoReplyId = Convert.ToInt16(cmd.Parameters["@NoReplyId"].Value);
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                LogExceptionAzure(sqlex);
                throw new Exception("Failed to update UpdateShortShipmentStage2", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// Delete GoodsInLine
        /// Calls [usp_delete_GoodsInLine]
        /// </summary>
        public override bool Delete(System.Int32? ShortShipmentId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_delete_shortshipment", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@GoodsInLineId", SqlDbType.Int).Value = ShortShipmentId;
                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to delete GoodsInLine", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override List<ShortShipmentDetails> DropDown(System.Int32? IsPartialShortShipmentStatus, System.String strquery, System.Int32? LoginId, System.Int32? ClientId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_select_shortshipmentstatus", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@IsPartialShortShipmentStatus", SqlDbType.Int).Value = IsPartialShortShipmentStatus;                
                cmd.Parameters.Add("@ShipPartialOrHoldForRemain", SqlDbType.NVarChar, 200).Value = strquery;
                cmd.Parameters.Add("@LoginNo", SqlDbType.Int).Value = LoginId;
                cmd.Parameters.Add("@ClientNo", SqlDbType.Int).Value = ClientId;
                cmd.CommandTimeout = 60;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<ShortShipmentDetails> lst = new List<ShortShipmentDetails>();
                while (reader.Read())
                {
                    ShortShipmentDetails obj = new ShortShipmentDetails();
                    obj.StatusId = GetReaderValue_NullableInt32(reader, "StatusId", null);
                    obj.Status = GetReaderValue_String(reader, "Status", null);
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get ShortShipments", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        // <summary>
        /// calls[usp_insert_Email_ShortShipment_Log]
        /// </summary>
        /// <returns></returns>
        public override Int32 InsertEmailShortShipmentLog(System.Int32? shortShipmentId, System.String strShortShipmentEmailLog, System.Int32? loginID)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_insert_Email_ShortShipment_Log", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@ShortShipmentId", SqlDbType.Int).Value = shortShipmentId;
                cmd.Parameters.Add("@ShortShipmentEmailLog", SqlDbType.NVarChar).Value = strShortShipmentEmailLog;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = loginID;
                cmd.Parameters.Add("@ShortShipmentLog", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (Int32)cmd.Parameters["@ShortShipmentLog"].Value;

            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to insert Short Shipment Email Log", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        /// <summary>
        /// DataListNugget 
        /// Calls [usp_datalistnugget_ShortShipment]
        /// </summary>
        public override List<ShortShipmentDetails> DataListNugget(System.Int32? ClientID, System.Int32? OrderBy, System.Int32? SortDir, System.Int32? PageIndex, System.Int32? PageSize, System.String Supplier, System.Int32? PurchaseOrderNoLo, System.Int32? PurchaseOrderNoHi, System.DateTime? DateReceived, System.Int32? QuantityOrderedLo, System.Int32? QuantityOrderedHi, System.Int32? QuantityReceivedLo, System.Int32? QuantityReceivedHi, System.Int32? ShortageQuantityLo, System.Int32? ShortageQuantityHi, System.Double? ShortageValueLo, System.Double? ShortageValueHi, System.Boolean? IsShortageRefundIssue, System.Int32? Status, System.Int32? GINumberLo, System.Int32? GINumberHi, System.Int32? Buyer, System.Boolean IsRecentOnly)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_selectall_shortshipment", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;
                cmd.Parameters.Add("@ClientID", SqlDbType.Int).Value = ClientID;
                cmd.Parameters.Add("@OrderBy", SqlDbType.Int).Value = OrderBy;
                cmd.Parameters.Add("@SortDir", SqlDbType.Int).Value = SortDir;
                cmd.Parameters.Add("@PageIndex", SqlDbType.Int).Value = PageIndex;
                cmd.Parameters.Add("@PageSize", SqlDbType.Int).Value = PageSize;
                cmd.Parameters.Add("@Supplier", SqlDbType.NVarChar).Value = Supplier;
                cmd.Parameters.Add("@PurchaseOrderNoLo", SqlDbType.Int).Value = PurchaseOrderNoLo;
                cmd.Parameters.Add("@PurchaseOrderNoHi", SqlDbType.Int).Value = PurchaseOrderNoHi;
                cmd.Parameters.Add("@DateReceived", SqlDbType.DateTime).Value = DateReceived;
                cmd.Parameters.Add("@QuantityOrderedLo", SqlDbType.Int).Value = QuantityOrderedLo;
                cmd.Parameters.Add("@QuantityOrderedHi", SqlDbType.Int).Value = QuantityOrderedHi;
                cmd.Parameters.Add("@QuantityReceivedLo", SqlDbType.Int).Value = QuantityReceivedLo;
                cmd.Parameters.Add("@QuantityReceivedHi", SqlDbType.Int).Value = QuantityReceivedHi;
                cmd.Parameters.Add("@ShortageQuantityLo", SqlDbType.Int).Value = ShortageQuantityLo;
                cmd.Parameters.Add("@ShortageQuantityHi", SqlDbType.Int).Value = ShortageQuantityHi;
                cmd.Parameters.Add("@ShortageValueLo", SqlDbType.Float).Value = ShortageValueLo;
                cmd.Parameters.Add("@ShortageValueHi", SqlDbType.Float).Value = ShortageValueHi;
                cmd.Parameters.Add("@IsShortageRefundIssue", SqlDbType.Bit).Value = IsShortageRefundIssue;
                cmd.Parameters.Add("@Status", SqlDbType.Int).Value = Status;
                cmd.Parameters.Add("@GINumberLo", SqlDbType.Int).Value = GINumberLo;
                cmd.Parameters.Add("@GINumberHi", SqlDbType.Int).Value = GINumberHi;
                cmd.Parameters.Add("@Buyer", SqlDbType.Int).Value = Buyer;
                cmd.Parameters.Add("@IsRecentOnly", SqlDbType.Bit).Value = IsRecentOnly;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<ShortShipmentDetails> lst = new List<ShortShipmentDetails>();
                while (reader.Read())
                {
                    ShortShipmentDetails obj = new ShortShipmentDetails();
                    obj.ShortShipmentId = GetReaderValue_Int32(reader, "ShortShipmentId", 0);
                    obj.Supplier = GetReaderValue_String(reader, "Supplier", "");
                    obj.PurchaseOrderNo = GetReaderValue_NullableInt32(reader, "PurchaseOrderNo", null);
                    obj.IPONo = GetReaderValue_NullableInt32(reader, "InternalPurchaseOrderNo", null);
                    obj.Salesman = GetReaderValue_String(reader, "Salesman", "");
                    obj.Reference = GetReaderValue_String(reader, "Reference", "");
                    obj.GoodsInNo = GetReaderValue_NullableInt32(reader, "GoodsInNo", null);
                    obj.DateReceived = GetReaderValue_DateTime(reader, "DateReceived", DateTime.MinValue);
                    obj.Raisedby = GetReaderValue_String(reader, "Raisedby", "");
                    obj.Buyer = GetReaderValue_String(reader, "Buyer", "");
                    obj.PartNo = GetReaderValue_String(reader, "PartNo", "");
                    obj.ManufacturerName = GetReaderValue_String(reader, "ManufacturerName", "");
                    obj.QuantityOrdered = GetReaderValue_NullableInt32(reader, "QuantityOrdered", null);
                    obj.QuantityAdvised = GetReaderValue_NullableInt32(reader, "QuantityAdvised", null);
                    obj.QuantityReceived = GetReaderValue_NullableInt32(reader, "QuantityReceived", null);
                    obj.ShortageQuantity = GetReaderValue_NullableInt32(reader, "ShortageQuantity", null);
                    obj.ShortageValue = GetReaderValue_NullableDouble(reader, "ShortageValue", null);
                    obj.IsShortageRefundIssue = GetReaderValue_NullableBoolean(reader, "IsShortageRefundIssue", null);
                    obj.ShortageRefundIssue = GetReaderValue_String(reader, "ShortageRefundIssue", "");
                    obj.RowCnt = GetReaderValue_NullableInt32(reader, "RowCnt", null);
                    obj.Completedby = GetReaderValue_String(reader, "Completedby", "");
                    obj.CompletedDate = GetReaderValue_DateTime(reader, "CompletedDate", DateTime.MinValue);
                    obj.Status = GetReaderValue_String(reader, "Status", "");
                    obj.GoodsInId = GetReaderValue_NullableInt32(reader, "GoodsInId", null);
                    obj.DebitNoteNo = GetReaderValue_NullableInt32(reader, "DebitNoteNo", null);
                    obj.DebitNumber = GetReaderValue_NullableInt32(reader, "DebitNumber", null);
                    obj.IsCancel = GetReaderValue_Boolean(reader, "IsCancel", false);
                    obj.IsClosed = GetReaderValue_Boolean(reader, "IsClosed", false);
                    //obj.ClientNo = GetReaderValue_NullableInt32(reader, "ClientNo", null)??0;
                    //[001]Code End
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get ShortShipments", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        /// <summary>
        /// Calls [usp_datalistnugget_ShortShipment_Export]
        /// </summary>
        /// <param name="clientID"></param>
        /// <param name="SortIndex"></param>
        /// <param name="SortDir"></param>
        /// <param name="Supplier"></param>
        /// <param name="PurchaseOrderNoLo"></param>
        /// <param name="PurchaseOrderNoHi"></param>
        /// <param name="DateReceived"></param>
        /// <param name="QuantityOrderedLo"></param>
        /// <param name="QuantityOrderedHi"></param>
        /// <param name="QuantityReceivedLo"></param>
        /// <param name="QuantityReceivedHi"></param>
        /// <param name="ShortageQuantityLo"></param>
        /// <param name="ShortageQuantityHi"></param>
        /// <param name="ShortageValueLo"></param>
        /// <param name="ShortageValueHi"></param>
        /// <param name="IsShortageRefundIssue"></param>
        /// <param name="ShortShipmentStatus"></param>
        /// <param name="GINumberLo"></param>
        /// <param name="GINumberHi"></param>
        /// <param name="IsPOHub"></param>
        /// <param name="Buyer"></param>
        /// <returns></returns>
        public override DataTable DataListNugget_Export(System.Int32? clientID, System.Int32? SortIndex, System.Int32? SortDir, System.String Supplier, System.Int32? PurchaseOrderNoLo, System.Int32? PurchaseOrderNoHi, DateTime? DateReceived, System.Int32? QuantityOrderedLo, System.Int32? QuantityOrderedHi, System.Int32? QuantityReceivedLo, System.Int32? QuantityReceivedHi, System.Int32? ShortageQuantityLo, System.Int32? ShortageQuantityHi, System.Double? ShortageValueLo, System.Double? ShortageValueHi, System.Boolean? IsShortageRefundIssue, System.Int32? ShortShipmentStatus, System.Int32? GINumberLo, System.Int32? GINumberHi, System.Boolean? IsPOHub, System.Int32? Buyer, System.Boolean IsRecentOnly)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_datalistnugget_ShortShipment_Export", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;
                cmd.Parameters.Add("@ClientID", SqlDbType.Int).Value = clientID;
                cmd.Parameters.Add("@OrderBy", SqlDbType.Int).Value = SortIndex;
                cmd.Parameters.Add("@SortDir", SqlDbType.Int).Value = SortDir;
                cmd.Parameters.Add("@Supplier", SqlDbType.NVarChar).Value = Supplier;
                cmd.Parameters.Add("@PurchaseOrderNoLo", SqlDbType.Int).Value = PurchaseOrderNoLo;
                cmd.Parameters.Add("@PurchaseOrderNoHi", SqlDbType.Int).Value = PurchaseOrderNoHi;
                cmd.Parameters.Add("@DateReceived", SqlDbType.DateTime).Value = DateReceived;
                cmd.Parameters.Add("@QuantityOrderedLo", SqlDbType.Int).Value = QuantityOrderedLo;
                cmd.Parameters.Add("@QuantityOrderedHi", SqlDbType.Int).Value = QuantityOrderedHi;
                cmd.Parameters.Add("@QuantityReceivedLo", SqlDbType.Int).Value = QuantityReceivedLo;
                cmd.Parameters.Add("@QuantityReceivedHi", SqlDbType.Int).Value = QuantityReceivedHi;
                cmd.Parameters.Add("@ShortageQuantityLo", SqlDbType.Int).Value = ShortageQuantityLo;
                cmd.Parameters.Add("@ShortageQuantityHi", SqlDbType.Int).Value = ShortageQuantityHi;
                cmd.Parameters.Add("@ShortageValueLo", SqlDbType.Float).Value = ShortageValueLo;
                cmd.Parameters.Add("@ShortageValueHi", SqlDbType.Float).Value = ShortageValueHi;
                cmd.Parameters.Add("@IsShortageRefundIssue", SqlDbType.Bit).Value = IsShortageRefundIssue;
                cmd.Parameters.Add("@Status", SqlDbType.Int).Value = ShortShipmentStatus;
                cmd.Parameters.Add("@GINumberLo", SqlDbType.Int).Value = GINumberLo;
                cmd.Parameters.Add("@GINumberHi", SqlDbType.Int).Value = GINumberHi;
                cmd.Parameters.Add("@IsPOHub", SqlDbType.Bit).Value = IsPOHub;
                cmd.Parameters.Add("@Buyer", SqlDbType.Int).Value = Buyer;
                cmd.Parameters.Add("@IsRecentOnly", SqlDbType.Bit).Value = IsRecentOnly;
                cn.Open();
                DataSet ds = new DataSet();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);
                return ds.Tables[0];
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Short Shipment Export Data", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override bool CancelShortShipment(System.Int32? shortShipmentId, System.Int32? loginId, System.Int32? clientId, ref System.Int32 NoReplyId, ref System.String NoReplyEmail)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_cancel_SSMainInfo", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@ShortShipmentId", SqlDbType.Int).Value = shortShipmentId;
                cmd.Parameters.Add("@LoginId", SqlDbType.Int).Value = loginId;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cmd.Parameters.Add("@NoReplyId", SqlDbType.Int).Direction = ParameterDirection.Output;
                cmd.Parameters.Add("@NoReplyEmail", SqlDbType.VarChar, 100).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                NoReplyId = (Int32)cmd.Parameters["@NoReplyId"].Value;
                NoReplyEmail = (string)cmd.Parameters["@NoReplyEmail"].Value;
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to Cancel Short Shipment", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override bool CloseShortShipment(System.Int32? shortShipmentId, System.Int32? loginId, System.Int32? clientId, ref System.Int32 NoReplyId, ref System.String NoReplyEmail)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_close_SSMainInfo", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@ShortShipmentId", SqlDbType.Int).Value = shortShipmentId;
                cmd.Parameters.Add("@LoginId", SqlDbType.Int).Value = loginId;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cmd.Parameters.Add("@NoReplyId", SqlDbType.Int).Direction = ParameterDirection.Output;
                cmd.Parameters.Add("@NoReplyEmail", SqlDbType.VarChar, 100).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                NoReplyId = (Int32)cmd.Parameters["@NoReplyId"].Value;
                NoReplyEmail = (string)cmd.Parameters["@NoReplyEmail"].Value;
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to Close Short Shipment", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
    }
}
