﻿//Marker     Changed by         Date         Remarks
//[001]      <PERSON><PERSON><PERSON><PERSON>     25/10/2021    Added new class for Warning Type.
using System;
using System.Data;
using System.Data.SqlClient;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;
using System.Web.Caching;
using System.Data.Common;

namespace Rebound.GlobalTrader.DAL.SqlClient
{
    public class SqlWarningTypeProvider: WarningTypeProvider
    {
        /// <summary>
        /// DropDown 
        /// Calls [usp_dropdown_ROHSStatus]
        /// </summary>
        public override List<WarningTypeDetails> DropDown()
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_dropdown_WarningType", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<WarningTypeDetails> lst = new List<WarningTypeDetails>();
                while (reader.Read())
                {
                    WarningTypeDetails obj = new WarningTypeDetails();
                    obj.WarningId = GetReaderValue_Int32(reader, "WarningId", 0);
                    obj.WarningName = GetReaderValue_String(reader, "WarningName", "");
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get WarningType", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// DropDown 
        /// Calls [usp_dropdown_LabelType]
        /// </summary>
        public override List<WarningTypeDetails> LableTypeDropDown()
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_dropdown_LabelType", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<WarningTypeDetails> lst = new List<WarningTypeDetails>();
                while (reader.Read())
                {
                    WarningTypeDetails obj = new WarningTypeDetails();
                    obj.LableTypeId = GetReaderValue_Int32(reader, "LableTypeId", 0);
                    obj.LableTypeName = GetReaderValue_String(reader, "LabelTypeName", "");
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get LableType", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
    }
}
