//------------------------------------------------------------------------------------------
// RP 14.06.2010:
// - Remove excess database calls (getting a whole sales order for each group of lines!)
// - ensure CustomerPart is updated for Services (it is the "Description" field)
//
// RP 14.01.2010:
// - Replace Price with LandedCost on Line Allocations
//Marker     changed by      date         Remarks
//[001]      Vinay         21/08/2012     ESMS Ref:54 - If SO line created from Quote line then create hyperlink from sales order to quote
//[002]      Vinay         30/08/2012     Add purchase order link in sales order allocation lines
//[003]      Vinay           04/02/2014   CR:- Add AS9120 Requirement in GT application
//[004]      Vinay           29/07/2015   EMS Ticket No:	254
//[005]      Vinay           11/04/2018   [REB-11304]: CHG-570795 Hazarders product type
//[006]      A<PERSON><PERSON>     18/07/2018   REB-12614 :Sales order Confirmation requirement
//[007]      <PERSON><PERSON><PERSON>    03-Dec-2018   [REB-13584]: Link Requirement to SO Line.
//[008]      Aashu Singh    12-Feb-2019   [REB-13395]: Log reasons for changes to Date Promised 
//[009]      Umendra Gupta  05-Apr-2019   Send Email to Status Reason Email
//[010]      Anand Gupta     17/08/2021   (GTDP-198) Controlling specific product groups for purchasing
//[011]      Anand Gupta     11/01/2022   (GTDP-240)  Add function to add warning message to show on all screens REQ - Quote - SO - PO related to ECCN field
//------------------------------------------------------------------------------------------
using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;
using System.Linq;
using System.Text;
using System.Net.Mail;
using System.Text.RegularExpressions;
using System.Collections.Concurrent;

namespace Rebound.GlobalTrader.Site.Controls.Nuggets.Data
{
    [WebService(Namespace = "http://tempuri.org/")]
    [WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    public class EndUserUndertakingForm : Rebound.GlobalTrader.Site.Data.Base
    {

        public override void ProcessRequest(HttpContext context)
        {
            if (base.init(context))
            {
                switch (Action)
                {
                    case "GetLines_Open": GetLines_Open(); break;
                    case "GetLines_Closed": GetLines_Closed(); break;
                    case "GetLines_All": GetLines_All(); break;
                    case "GetData": GetData(); break;
                    case "AddNew": AddNew(); break;
                    case "SaveEdit": SaveEdit(); break;
                    case "Delete": Delete(); break;
                    case "PostUnpost": PostUnpost(); break;
                    case "PostAll": PostAll(); break;
                    case "UnpostAll": UnpostAll(); break;
                    case "GetQuoteLineForNew": GetQuoteLineForNew(); break;
                    case "GetRequirementLineForNew": GetRequirementLineForNew(); break;
                    case "GetStockLineForNew": GetStockLineForNew(); break;
                    case "GetServiceLineForNew": GetServiceLineForNew(); break;
                    case "GetSalesOrderLineForNew": GetSalesOrderLineForNew(); break;
                    case "GetLineShippingLines": GetLineShippingLines(); break;
                    case "GetLineAllocations": GetLineAllocations(); break;
                    case "AddNewAllocation": AddNewAllocation(); break;
                    case "DeleteAllocation": DeleteAllocation(); break;
                    case "GetStockForAllocation": GetStockForAllocation(); break;
                    case "GetStockForIpoAllocation": GetStockForIpoAllocation(); break;
                    case "AddNewIpoAllocation": AddNewIpoAllocation(); break;
                    case "GetServiceLineForInvoice": GetServiceLineForInvoice(); break;
                    case "Close": Close(); break;
                    case "CreateCloneSOLine": CreateCloneSOLine(); break;
                    case "GetSalesOrder": GetSalesOrder(); break;
                    //[006] start
                    case "Confirm": SaveConfirmation(); break;
                    //[006] end
                    //[008] start
                    case "GetPromiseReasonLog": GetPromiseReasonLog(); break;
                    //[008] end
                    case "SaveEditAll": SaveEditAll(); break;
                    case "GetSOIHSEccnDetail": GetSOIHSEccnDetail(); break;
                    default: WriteErrorActionNotFound(); break;
                }
            }
        }

        /// <summary>
        /// get open SalesOrderLines for specified salesOrder
        /// /// </summary>
        private void GetLines_Open()
        {
            ProcessLines("LinesOpen", SalesOrderLine.GetListOpenForSalesOrder(ID));
        }

        /// <summary>
        /// get closed SalesOrderLines for specified salesOrder
        /// </summary>
        private void GetLines_Closed()
        {
            ProcessLines("LinesClosed", SalesOrderLine.GetListClosedForSalesOrder(ID));
        }

        /// <summary>
        /// get all SalesOrderLines for specified salesOrder
        /// </summary>
        private void GetLines_All()
        {
            ProcessLines("LinesAll", SalesOrderLine.GetListForEUUSalesOrder(ID, true));
        }



        /// <summary>
        /// Processes a list of sales order lines
        /// </summary>
        private void ProcessLines(string strListVarName, List<SalesOrderLine> lst)
        {
            try
            {
                JsonObject jsn = new JsonObject();
                JsonObject jsnItems = new JsonObject(true);


                //get details for calculations
                SalesOrder so = SalesOrder.GetDetailsForLineCalculations(ID);
                bool isConfirmedAll = true;
                if (so == null)
                {
                    WriteErrorDataNotFound();
                }
                else
                {
                    jsn.AddVariable("Count", lst.Count);
                    double dblSubTotal = 0;
                    double dblSubTotalRaw = 0;
                    double dblTax = 0;
                    bool IsAnyLinePosted = false;
                    foreach (SalesOrderLine ln in lst)
                    {
                        double dblLineTotal = (double)ln.Quantity * (double)ln.Price;
                        double dblLineTax = ln.IsLineTaxable ? dblLineTotal * ((double)so.TaxRate / 100) : 0;
                        JsonObject jsnItem = new JsonObject();
                        jsnItem.AddVariable("IsSourcingResultExist", ln.IsSourcingResultExist);
                        jsnItem.AddVariable("LineID", ln.SalesOrderLineId);
                        jsnItem.AddVariable("ROHS", ln.ROHS);
                        jsnItem.AddVariable("CustomerPart", ln.CustomerPart);
                        jsnItem.AddVariable("Part", ln.Part);
                        jsnItem.AddVariable("Mfr", ln.ManufacturerCode);
                        jsnItem.AddVariable("MfrNo", ln.ManufacturerNo);
                        jsnItem.AddVariable("DC", ln.DateCode);
                        //jsnItem.AddVariable("Product", ln.ProductName);
                        jsnItem.AddVariable("Product", ln.ProductDescription);
                        jsn.AddVariable("DutyCodeAndRate", string.Format("{0} ({1})", ln.DutyCode, Functions.FormatNumeric(ln.DutyRate, 5)));
                        jsnItem.AddVariable("Package", ln.PackageName);
                        jsnItem.AddVariable("Quantity", Functions.FormatNumeric(ln.Quantity));
                        jsnItem.AddVariable("ECCNCode", ln.ECCNCode);
                        jsnItem.AddVariable("ECCNCodeNo", ln.ECCNCodeNo);
                        if (ln.IsService)
                        {
                            jsnItem.AddVariable("Shipped", (ln.ServiceShipped) ? Functions.FormatNumeric(ln.Quantity) : "0");
                            jsnItem.AddVariable("Allocated", Functions.FormatNumeric(ln.Quantity));
                            jsnItem.AddVariable("BackOrder", 0);
                        }
                        else
                        {
                            jsnItem.AddVariable("Shipped", Functions.FormatNumeric(ln.QuantityShipped));
                            jsnItem.AddVariable("Allocated", Functions.FormatNumeric(ln.QuantityAllocated));
                            jsnItem.AddVariable("BackOrder", Functions.FormatNumeric(ln.BackOrderQuantity));
                        }
                        jsnItem.AddVariable("PartialAllocated", (ln.QuantityAllocated < ln.Quantity && ln.QuantityAllocated > 0));
                        jsnItem.AddVariable("ServiceNo", ln.ServiceNo);
                        jsnItem.AddVariable("IsIPO", ln.IsIPO);
                        jsnItem.AddVariable("IsChecked", ln.IsChecked);
                        jsnItem.AddVariable("Closed", ln.Closed);
                        jsnItem.AddVariable("IsPosted", ln.Posted);
                        jsnItem.AddVariable("IsAllocated", ln.IsAllocated);
                        jsnItem.AddVariable("IsShipped", ln.IsShipped);
                        jsnItem.AddVariable("Inactive", ln.Inactive);
                        jsnItem.AddVariable("Price", Functions.FormatCurrency(ln.Price, so.CurrencyCode));
                        jsnItem.AddVariable("Total", Functions.FormatCurrency(dblLineTotal, so.CurrencyCode, 2));
                        jsnItem.AddVariable("TotalRaw", dblLineTotal);
                        jsnItem.AddVariable("Tax", Functions.FormatCurrency(dblLineTax, so.CurrencyCode, 2));
                        jsnItem.AddVariable("TaxRaw", dblLineTax);
                        jsnItem.AddVariable("CanBePosted", (so.Paid || (so.CreditLimit >= (so.Balance + dblLineTotal + dblLineTax))));
                        jsnItem.AddVariable("LineNo", Functions.FormatNumeric(ln.SerialNo));
                        jsnItem.AddVariable("SourcingResultNo", ln.SourcingResultNo);
                        jsnItem.AddVariable("IsFromIPO", ln.IsFromIPO);
                        jsnItem.AddVariable("IsIPOCreatedFromCurrentLine", ln.IsIPOCreatedForCurrentLine);
                        jsnItem.AddVariable("IsIPOHeaderCreated", ln.IsIPOHeaderCreated);
                        jsnItem.AddVariable("InternalPurchaseOrderNumber ", ln.InternalPurchaseOrderNumber);
                        jsnItem.AddVariable("IsClone", ln.CloneID.HasValue ? true : false);//need get from database
                        //[006] start
                        jsnItem.AddVariable("IsConfirmed", ln.IsConfirmed);
                        jsnItem.AddVariable("DateConfirmed", Functions.FormatDate(ln.DateConfirmed));
                        if (isConfirmedAll && !ln.IsConfirmed)
                            isConfirmedAll = false;
                        //[006] end
                        //[007] start
                        jsnItem.AddVariable("PromiseReasonNo", ln.PromiseReasonNo);
                        jsnItem.AddVariable("PromiseReason", ln.PromiseReason);
                        //[007] end
                        jsnItem.AddVariable("WarehouseNo", ln.WarehouseNo);
                        jsnItem.AddVariable("WarehouseName", ln.WarehouseName);

                        // Check post enable condition
                        bool CanBePosted = (so.Paid || (so.CreditLimit >= (so.Balance + dblLineTotal + dblLineTax)));
                        if (CanBePosted == false)
                        {
                            ln.PostDisableReason = string.IsNullOrEmpty(ln.PostDisableReason) ? " Not paid or low balance" : ln.PostDisableReason + ",Not paid or low balance";
                        }
                        //bool blnAutoAuthoriseSO = SettingsManager.GetSetting_Boolean(SettingItem.List.AutoApproveSO);
                        //if (blnAutoAuthoriseSO == false)
                        //{
                        //    ln.PostDisableReason = string.IsNullOrEmpty(ln.PostDisableReason) ? " Not auto authorise SO" : ln.PostDisableReason + ",Not auto authorise SO";
                        //}
                        jsnItem.AddVariable("PostDisableReason", ln.PostDisableReason);
                        jsnItem.AddVariable("EUUFormRequired", ln.IsECCNWarning);
                        jsnItem.AddVariable("IsEUUPDFAvailable", ln.IsEUUPDFAvailable);
                        jsnItem.AddVariable("EUUPDFUploadDate", Functions.FormatDate(ln.EUUPDFUploadDate));
                        jsnItem.AddVariable("EUUUPDFploadName", ln.EUUUPDFploadName);

                        jsnItems.AddVariable(jsnItem);
                        jsnItem.Dispose(); jsnItem = null;
                        dblSubTotalRaw += dblLineTotal + dblLineTax;
                        if (ln.Posted)
                        {
                            dblSubTotal += dblLineTotal;
                            dblTax += dblLineTax;
                        }
                        if (ln.Posted)
                            IsAnyLinePosted = true;

                    }
                    jsn.AddVariable(strListVarName, jsnItems);

                    //summary calculations
                    dblTax += so.Freight * ((double)so.TaxRate / 100);
                    jsn.AddVariable("CanBePosted", (so.CreditLimit >= (so.Balance + dblSubTotalRaw)) || so.Paid);
                    jsn.AddVariable("SubTotal", Functions.FormatCurrency(dblSubTotal, so.CurrencyCode, 2));
                    jsn.AddVariable("Freight", Functions.FormatCurrency(so.Freight, so.CurrencyCode, 2));
                    jsn.AddVariable("Tax", Functions.FormatCurrency(dblTax, so.CurrencyCode, 2));
                    jsn.AddVariable("Total", Functions.FormatCurrency(dblSubTotal + dblTax + so.Freight, so.CurrencyCode, 2));
                    jsn.AddVariable("TotalVal", dblSubTotal + dblTax + so.Freight);
                    jsn.AddVariable("AnyLinePosted", IsAnyLinePosted);
                    jsn.AddVariable("TotalFreight", so.Freight + so.Freight * ((double)so.TaxRate / 100));
                    jsn.AddVariable("TaxRate", ((double)so.TaxRate / 100));
                    jsn.AddVariable("IsConfirmedAll", isConfirmedAll);

                    OutputResult(jsn);
                }
                jsn.Dispose(); jsn = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }

        /// <summary>
        /// get a salesOrderLine by key
        /// </summary>
        private void GetData()
        {
            //System.Threading.Thread.Sleep(3000);
            SalesOrderLine ln = null;
            try
            {
                ln = SalesOrderLine.Get(ID);
                if (ln == null)
                {
                    WriteErrorDataNotFound();
                }
                else
                {
                    JsonObject jsn = new JsonObject();
                    jsn.AddVariable("IsSourcingExist", BLL.Warehouse.IsSoLineExistInSourcingResult(ln.SalesOrderNo, ln.SalesOrderLineId));
                    jsn.AddVariable("SONo", ln.SalesOrderNo);
                    jsn.AddVariable("Part", ln.Part);
                    jsn.AddVariable("Mfr", ln.ManufacturerName);
                    jsn.AddVariable("MfrNo", ln.ManufacturerNo);
                    jsn.AddVariable("DateCd", ln.DateCode);
                    jsn.AddVariable("PackageName", ln.PackageName);
                    jsn.AddVariable("Package", ln.PackageDescription);
                    jsn.AddVariable("PackageNo", ln.PackageNo);
                    jsn.AddVariable("Quantity", Functions.FormatNumeric(ln.Quantity));
                    jsn.AddVariable("Price", Functions.FormatCurrency(ln.Price, ln.CurrencyCode));
                    jsn.AddVariable("PriceVal", Functions.FormatCurrency(ln.Price));
                    jsn.AddVariable("CurrencyCode", ln.CurrencyCode);
                    if (ln.IsService)
                    {
                        jsn.AddVariable("Allocated", Functions.FormatNumeric(ln.Quantity));
                        jsn.AddVariable("Shipped", (ln.ServiceShipped) ? Functions.FormatNumeric(ln.Quantity) : "0");
                        jsn.AddVariable("BackOrder", 0);
                    }
                    else
                    {
                        jsn.AddVariable("Allocated", Functions.FormatNumeric(ln.QuantityAllocated));
                        jsn.AddVariable("Shipped", Functions.FormatNumeric(ln.QuantityShipped));
                        jsn.AddVariable("BackOrder", Functions.FormatNumeric(ln.BackOrderQuantity));
                    }
                    jsn.AddVariable("DatePromised", Functions.FormatDate(ln.DatePromised));
                    jsn.AddVariable("RequiredDate", Functions.FormatDate(ln.RequiredDate));
                    jsn.AddVariable("DeliveryDate", Functions.FormatDate(ln.DeliveryDate));
                    jsn.AddVariable("Instructions", Functions.ReplaceLineBreaks(ln.Instructions));
                    jsn.AddVariable("ProductName", ln.ProductName);
                    jsn.AddVariable("Product", ln.ProductDescription);
                    jsn.AddVariable("ProductNo", ln.ProductNo);
                    jsn.AddVariable("CustomerPart", ln.CustomerPart);
                    jsn.AddVariable("Closed", ln.Closed);
                    jsn.AddVariable("Posted", ln.Posted);
                    jsn.AddVariable("ShipASAP", ln.ShipASAP);
                    jsn.AddVariable("IsAllocated", ln.IsAllocated);
                    jsn.AddVariable("IsShipped", ln.IsShipped);
                    jsn.AddVariable("Inactive", ln.Inactive);
                    jsn.AddVariable("LineValue", Functions.FormatCurrency(ln.LineValue, ln.CurrencyCode));
                    jsn.AddVariable("StockNo", ln.StockNo);
                    jsn.AddVariable("ServiceNo", ln.ServiceNo);
                    jsn.AddVariable("ROHS", ln.ROHS);
                    jsn.AddVariable("PurchaseOrderNo", ln.PurchaseOrderId);
                    jsn.AddVariable("PurchaseOrderNumber", ln.PurchaseOrderNumber);
                    jsn.AddVariable("PurchaseOrderNo", ln.PurchaseOrderId);
                    jsn.AddVariable("WarehouseNo", ln.WarehouseNo);
                    jsn.AddVariable("Warehouse", ln.WarehouseName);
                    jsn.AddVariable("AllocationId", ln.AllocationId);
                    jsn.AddVariable("AllocatedQuantity", ln.AllocatedQuantity);
                    jsn.AddVariable("SupplierNo", ln.SupplierNo);
                    jsn.AddVariable("Supplier", ln.SupplierName);
                    jsn.AddVariable("Status", (ln.QuantityShipped < ln.Quantity) ? Functions.GetGlobalResource("Status", "Open") : Functions.GetGlobalResource("Status", "Closed"));
                    jsn.AddVariable("LineNotes", Functions.ReplaceLineBreaks(ln.LineNotes));
                    //[004] code start
                    jsn.AddVariable("CompanyNo", ln.CompanyNo);
                    //[004] code end
                    //[001] code start
                    jsn.AddVariable("isIPOApproved", ln.IPOApprovedBy > 0 ? false : true);
                    jsn.AddVariable("CompanyName", ln.CompanyName);
                    jsn.AddVariable("InternalPurchaseOrderNo", ln.InternalPurchaseOrderNo);
                    jsn.AddVariable("IsIPOExist", Convert.ToBoolean(ln.IsIPOExist));
                    jsn.AddVariable("IsIPOOpen", Convert.ToBoolean(ln.IsIPOAndPOOpen));
                    jsn.AddVariable("IsIPO", ln.IsIPO);
                    jsn.AddVariable("PODelDate", Functions.FormatDate(ln.PODeliveryDate));
                    jsn.AddVariable("SourcingResultUsedByOther", Convert.ToBoolean(ln.SourcingResultUsedByOther));
                    jsn.AddVariable("CloneSerialNo", Functions.FormatNumeric(ln.CloneSerialNo));
                    jsn.AddVariable("SOSerialNo", Functions.FormatNumeric(ln.SerialNo));
                    if (ln.QuoteLineNo > 0)
                    {
                        QuoteLine qln = null;
                        qln = QuoteLine.Get(ln.QuoteLineNo);
                        if (qln != null)
                        {
                            jsn.AddVariable("QuoteId", qln.QuoteNo);
                            jsn.AddVariable("QuoteLineNo", qln.QuoteLineId);
                            jsn.AddVariable("QuoteNumber", qln.QuoteNumber);
                        }
                    }
                    else
                    {
                        jsn.AddVariable("QuoteId", 0);
                        jsn.AddVariable("QuoteLineNo", 0);
                        jsn.AddVariable("QuoteNumber", 0);
                    }
                    //[001] code end
                    //[003] code start
                    jsn.AddVariable("ProductSource", ln.ProductSource);
                    jsn.AddVariable("ProductSourceName", Functions.ReplaceLineBreaks(ln.ProductSourceName));
                    jsn.AddVariable("ProdInactive", Convert.ToBoolean(ln.ProductInactive));
                    jsn.AddVariable("DutyCodeAndRate", string.Format("{0} ({1})", ln.DutyCode, Functions.FormatNumeric(ln.DutyRate, 5)));//ln.DutyRate
                    //[003] code end
                    jsn.AddVariable("MSLLevel", ln.MSLLevel);
                    jsn.AddVariable("ContractNo", ln.ContractNo);
                    jsn.AddVariable("IsProdHaz", Convert.ToBoolean(ln.IsProdHazardous));
                    jsn.AddVariable("IsPrintHaz", Convert.ToBoolean(ln.PrintHazardous));
                    //[006] start
                    jsn.AddVariable("DateConfirmed", Functions.FormatDate(ln.DateConfirmed));
                    //[006] end
                    //[007] start
                    jsn.AddVariable("CustomerRequirementId", ln.CustomerRequirementId);
                    jsn.AddVariable("CustomerRequirementNumber", ln.CustomerRequirementNumber);
                    //ihs code start
                    jsn.AddVariable("CountryOfOrigin", ln.CountryOfOrigin);
                    jsn.AddVariable("LifeCycleStage", ln.LifeCycleStage);
                    jsn.AddVariable("HTSCode", ln.HTSCode);
                    jsn.AddVariable("AveragePrice", ln.AveragePrice);
                    jsn.AddVariable("Packaging", ln.Packaging);
                    jsn.AddVariable("PackagingSize", ln.PackagingSize);
                    jsn.AddVariable("Descriptions", Functions.ReplaceLineBreaks(ln.Descriptions));
                    if (string.IsNullOrEmpty(ln.Descriptions))
                        jsn.AddVariable("DescShort", Functions.ReplaceLineBreaks(ln.Descriptions));
                    else if (ln.Descriptions.Length <= 10)
                        jsn.AddVariable("DescShort", Functions.ReplaceLineBreaks(ln.Descriptions));
                    else
                        jsn.AddVariable("DescShort", Functions.ReplaceLineBreaks(ln.Descriptions.Substring(0, 10)));
                    //ihs code end
                    jsn.AddVariable("IHSProduct", ln.IHSProduct);
                    jsn.AddVariable("IsPOHub", Convert.ToBoolean(SessionManager.IsPOHub));
                    jsn.AddVariable("ECCNCode", ln.ECCNCode);
                    //[007] end
                    //[010] code start
                    jsn.AddVariable("IsOrderViaIPOonly", Convert.ToBoolean(ln.IsOrderViaIPOonly));
                    //[010] code end
                    jsn.AddVariable("IsRestrictedProduct", Convert.ToBoolean(ln.IsRestrictedProduct));
                    Product objcReq = Product.GetProductStatusMessage(ln.ProductNo, Convert.ToBoolean(ln.IsProdHazardous), Convert.ToBoolean(ln.IsOrderViaIPOonly), ln.ClientNo, Convert.ToBoolean(ln.IsRestrictedProduct));
                    if (objcReq != null)
                    {
                        jsn.AddVariable("ProductMessage", Functions.ReplaceLineBreaks(objcReq.ProductMessage));
                        jsn.AddVariable("MsgHazardous", Functions.ReplaceLineBreaks(objcReq.hazardousMessage));
                        jsn.AddVariable("MsgIPO", Functions.ReplaceLineBreaks(objcReq.IPOMessage));
                        jsn.AddVariable("MsgRestricted", Functions.ReplaceLineBreaks(objcReq.RestrictedMessage));
                    }

                    else
                    {
                        jsn.AddVariable("ProductMessage", "");
                        jsn.AddVariable("MsgHazardous", "");
                        jsn.AddVariable("MsgIPO", "");
                        jsn.AddVariable("MsgRestricted", "");

                    }
                    //[011] code start
                    string IHSECCNSCodeDefination = string.Empty;
                    if (!string.IsNullOrEmpty(ln.IHSECCNCodeDefination))
                    {
                        IHSECCNSCodeDefination = ln.IHSECCNCodeDefination;
                        jsn.AddVariable("IHSECCNSCodeDefination", Functions.ReplaceLineBreaks(IHSECCNSCodeDefination));
                    }
                    //[011] code end
                    string StrStockAvailableDetail = string.Empty;
                    if (!string.IsNullOrEmpty(ln.StockAvailableDetail))
                    {
                        StrStockAvailableDetail = ln.StockAvailableDetail;
                        jsn.AddVariable("StockAvailableDetail", Functions.ReplaceLineBreaks(StrStockAvailableDetail));
                    }
                    OutputResult(jsn);
                    jsn.Dispose();
                    jsn = null;
                }
            }
            catch (Exception e)
            {
                WriteError(e);
            }
            finally
            {
                ln = null;
            }
        }

        /// <summary>
        /// Add new salesOrderLine
        /// </summary>
        public void AddNew()
        {
            try
            {
                bool blnIsFromStock = GetFormValue_Boolean("IsFromStock");
                // Boolean isClone = GetFormValue_Boolean("isClone");

                int intNewSalesOrderLineID = SalesOrderLine.Insert(
                        ID
                        , GetFormValue_String("Part")
                        , GetFormValue_NullableInt("MfrNo")
                        , GetFormValue_String("DateCd")
                        , GetFormValue_NullableInt("PackageNo")
                        , GetFormValue_NullableInt("Quantity")
                        , GetFormValue_NullableDouble("Price")
                        , GetFormValue_DateTime("DatePromised")
                        , GetFormValue_DateTime("RequiredDate")
                        , GetFormValue_String("Instructions")
                        , GetFormValue_NullableInt("ProductNo")
                        , "Y"
                        , GetFormValue_String("CustomerPart")
                        , blnIsFromStock
                        , GetFormValue_Boolean("ShipASAP")
                        , GetFormValue_NullableInt("ServiceNo")
                        , GetFormValue_NullableInt("StockNo")
                        , GetFormValue_NullableByte("ROHS")
                        , GetFormValue_String("LineNotes")
                        , LoginID
                        //[001] code start
                        , GetFormValue_NullableInt("QuoteLineID")
                        //[001] code end

                        //[003] code start
                        , GetFormValue_NullableByte("ProductSource")
                        //[003] code end
                        , GetFormValue_NullableInt("SourcingResultNo")
                        , GetFormValue_NullableDateTime("PODelDate", null)
                        , GetFormValue_String("MSLLevel")

                        , GetFormValue_String("ContractNo")
                        //[003] code start
                        , GetFormValue_NullableBoolean("PrintHazWar", 0)
                        //[003] code end
                        //[007] code start
                        , GetFormValue_NullableInt("DocNo", 0)
                        , GetFormValue_String("DocType", "")
                        //[007] code end
                        , GetFormValue_String("ECCNCode", "")
                        , GetFormValue_Int("ECCNNo")
                        , null
                        );

                //update the quote line's status to "Sold" if this line was based on a quote line
                if (GetFormValue_NullableInt("QuoteLineID") > 0)
                {
                    QuoteLine.UpdateClose(
                        GetFormValue_NullableInt("QuoteLineID")
                        , 1 //"Sold"
                        , LoginID
                        , ""   //  Added as per client requirement 14-3-2018 
                    );
                }
                if (intNewSalesOrderLineID > 0)
                {
                    NotifyECCNSales(ID, LoginID, intNewSalesOrderLineID);
                }

                JsonObject jsn = new JsonObject();
                jsn.AddVariable("Result", intNewSalesOrderLineID > 0);
                jsn.AddVariable("NewID", intNewSalesOrderLineID);
                OutputResult(jsn);
                jsn.Dispose();
                jsn = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }
        private void NotifyECCNSales(int? salesOrderNo, int? LoginId, int? SalesOrderLineID)
        {
            int? intMailGroupNo = null;
            SalesOrder so = SalesOrder.Get(salesOrderNo);
            if (so != null)
            {
                intMailGroupNo = BLL.MailGroup.GetECCNRestrictedUsenotifcationMailGroupNo(SessionManager.ClientID);
                Rebound.GlobalTrader.Site.WebServices service = new WebServices();
                // string message = string.Format(Functions.GetGlobalResource("MailTemplates", "SOAuthorized"), so.SalesOrderNumber, LoginFullName, so.CompanyName);
                if (so.SupportTeamMemberNo > 0)
                {
                    service.NotifyECCNMessageSO(Convert.ToString(LoginId) + "||" + Convert.ToString(so.SupportTeamMemberNo), Convert.ToString(intMailGroupNo), "", "", null, LoginId, so.SalesOrderNumber, SalesOrderLineID, so.SupportTeamMemberNo, salesOrderNo);
                }
                else
                {
                    service.NotifyECCNMessageSO(Convert.ToString(LoginId), Convert.ToString(intMailGroupNo), "", "", null, LoginId, so.SalesOrderNumber, SalesOrderLineID, so.SupportTeamMemberNo, salesOrderNo);
                    //service.PowerAppNotifyMessageSO(Convert.ToString(so.Salesman), "", string.Format(Functions.GetGlobalResource("Messages", "SOAuthorise"), so.SalesOrderNumber), message, null, LoginId, LoginEmailFrom);
                }
                service = null;
            }
        }

        /// <summary>
        /// Add new salesOrderLine
        /// </summary>
        public void CreateCloneSOLine()
        {
            try
            {
                bool blnIsFromStock = GetFormValue_Boolean("IsFromStock");
                Boolean IsIPO = GetFormValue_Boolean("IsIPO");
                string Message = "";
                int intNewSalesOrderLineID = SalesOrderLine.CreateCloneSOLine(
                        ID

                        , GetFormValue_NullableInt("Quantity")
                        , GetFormValue_NullableDouble("Price")
                        , GetFormValue_DateTime("DatePromised")
                        , GetFormValue_DateTime("RequiredDate")
                         , GetFormValue_DateTime("PoDeleveryDate")
                         , SessionManager.LoginID
                        , GetFormValue_NullableInt("SourcingResultNo")
                        , GetFormValue_NullableInt("SalesOrderLineID")
                        , IsIPO == true ? true : false
                        , GetFormValue_NullableInt("InternalIPO")
                        , GetFormValue_String("LineNotes")
                        , GetFormValue_String("Instructions")
                        , GetFormValue_NullableInt("Flag")
                        , GetFormValue_NullableByte("ProductSource")
                         , GetFormValue_Boolean("ShipASAP")
                         , GetFormValue_NullableInt("ProductNo")

                        , out Message
                        );

                //update the quote line's status to "Sold" if this line was based on a quote line
                if (GetFormValue_NullableInt("QuoteLineID") > 0)
                {
                    QuoteLine.UpdateClose(
                        GetFormValue_NullableInt("QuoteLineID")
                        , 1 //"Sold"
                        , LoginID
                        , ""  //Added as per client requirement 14-3-2018
                    );
                }

                JsonObject jsn = new JsonObject();
                jsn.AddVariable("Result", intNewSalesOrderLineID > 0);
                jsn.AddVariable("NewID", intNewSalesOrderLineID);
                OutputResult(jsn);
                jsn.Dispose();
                jsn = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }
        /// <summary>
        /// Update an existing salesOrderLine
        /// </summary>
        public void SaveEdit()
        {
            try
            {
                bool blnResult = false;
                string strECCNCodeUI = string.Empty;
                string strECCNCodedb = string.Empty;
                System.Int32 intSalesOrderNo = 0;
                SalesOrderLine sl = SalesOrderLine.Get(ID);
                if (sl != null)
                {
                    string oldPromiseDate = sl.DatePromised.ToShortDateString();
                    strECCNCodedb = sl.ECCNCode;
                    if (!sl.IsService)
                    {
                        sl.Part = GetFormValue_String("Part");
                        sl.ROHS = GetFormValue_NullableByte("ROHS");
                        sl.DateCode = GetFormValue_String("DateCd");
                        sl.ProductNo = GetFormValue_NullableInt("ProductNo");
                        sl.PackageNo = GetFormValue_NullableInt("PackageNo");
                        sl.ManufacturerNo = GetFormValue_NullableInt("ManufacturerNo");
                        sl.ShipASAP = GetFormValue_Boolean("ShipASAP");
                        sl.Instructions = GetFormValue_String("Instructions");
                        sl.Notes = GetFormValue_String("LineNotes");
                    }
                    sl.CustomerPart = GetFormValue_String("CustomerPart");
                    sl.Quantity = GetFormValue_Int("Quantity");
                    sl.Price = GetFormValue_Double("Price");
                    sl.DatePromised = GetFormValue_DateTime("DatePromised");
                    sl.RequiredDate = GetFormValue_DateTime("RequiredDate");
                    sl.UpdatedBy = LoginID;
                    //[003] code start
                    sl.ProductSource = GetFormValue_NullableByte("ProductSource");
                    sl.PODeliveryDate = GetFormValue_NullableDateTime("PODelDate", null);
                    sl.SerialNo = Convert.ToInt16(GetFormValue_NullableInt("SOSerial", 0));
                    sl.MSLLevel = GetFormValue_String("MSLLevel");
                    sl.ContractNo = GetFormValue_String("ContractNo");
                    sl.ECCNCode = GetFormValue_String("ECCNCode");
                    sl.ECCNCodeNo = GetFormValue_Int("ECCNNo");
                    sl.PrintHazardous = GetFormValue_NullableBoolean("PrintHazWar", 0);
                    sl.Email = GetFormValue_String("Email");
                    strECCNCodeUI = GetFormValue_String("ECCNCode");
                    intSalesOrderNo = sl.SalesOrderNo;
                    //start [009]
                    string strEmailTo = "";
                    MailAddressCollection adrTo = null;
                    string strEmailFrom = SessionManager.LoginEmail;
                    string strSubject = "SO {0} Promised Date Changed";
                    if (!string.IsNullOrEmpty(sl.Email) && oldPromiseDate != sl.DatePromised.ToShortDateString())
                    {
                        strSubject = string.Format(strSubject, sl.SalesOrderNumber.ToString());
                        strEmailTo = sl.Email;
                        adrTo = new MailAddressCollection();
                        adrTo.Add(strEmailTo);
                        System.Net.Mail.MailAddress adrFrom = new MailAddress(strEmailFrom);
                        System.Net.Mail.MailAddress adrReplyTo = new MailAddress(strEmailFrom);

                        string messageBodyContent = string.Format(Functions.GetGlobalResource("MailTemplates", "PromiseReasonEmail"),
                                                                sl.SalesOrderNumber.ToString(),
                                                                sl.DatePromised.ToShortDateString(),
                                                                GetFormValue_String("Reason"),
                                                                SessionManager.LoginFullName);
                        OutgoingMailManager.SendUserMail(strSubject, RemoveHyperLink(messageBodyContent), adrTo, adrFrom, null, null, adrReplyTo, true);
                    }
                    //end [009]
                    blnResult = sl.Update(GetFormValue_Boolean("IsFormChanged"),
                        GetFormValue_Boolean("IsReasonChanged"),
                        GetFormValue_Int("PromiseReasonNo")
                        );
                }
                sl = null;
                //[003] code end
                JsonObject jsn = new JsonObject();
                jsn.AddVariable("Result", blnResult);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;

                if (strECCNCodeUI != null)
                {
                    if (strECCNCodeUI != strECCNCodedb)
                    {
                        if (ID > 0)
                        {
                            NotifyECCNSales(intSalesOrderNo, LoginID, ID);
                        }
                    }
                }
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }
        //[008] start
        private string RemoveHyperLink(string strMessage)
        {
            return Regex.Replace(strMessage, "</?(a|A).*?>", "").Replace("\r\n", "<br/>");
        }
        //[008] end
        /// <summary>
        /// Delete
        /// </summary>
        public void Delete()
        {
            try
            {
                SalesOrderLine sol = SalesOrderLine.Get(ID);
                bool blnCanDelete = true;
                if (!sol.IsService)
                {
                    if (sol.QuantityAllocated > 0) blnCanDelete = false;
                    if (sol.QuantityShipped > 0) blnCanDelete = false;
                }
                if (blnCanDelete)
                {
                    SalesOrderLine.Delete(ID);
                }
                sol = null;
                JsonObject jsn = new JsonObject();
                jsn.AddVariable("Result", true);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }

        /// <summary>
        /// post/unpost an existing salesOrderLine
        /// </summary>
        public void PostUnpost()
        {
            try
            {
                bool blnResult = SalesOrderLine.UpdatePostOrUnpost(
                    ID,
                    GetFormValue_Boolean("Posted"),
                    LoginID
                );
                JsonObject jsn = new JsonObject();
                jsn.AddVariable("Result", blnResult);
                OutputResult(jsn);
                jsn.Dispose();
                jsn = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }

        /// <summary>
        /// Post All passed Line IDs
        /// </summary>
        public void PostAll()
        {
            try
            {
                bool blnResult = true;
                Array aryUnpostedLineIDs = Functions.JavascriptStringToArray(GetFormValue_String("IDs"));
                for (int i = 0; i < aryUnpostedLineIDs.Length; i++)
                {
                    int intLineID = Convert.ToInt32(aryUnpostedLineIDs.GetValue(i));
                    blnResult = blnResult && SalesOrderLine.UpdatePostOrUnpost(
                        intLineID
                        , true
                        , LoginID
                    );
                }
                JsonObject jsn = new JsonObject();
                jsn.AddVariable("Result", blnResult);
                OutputResult(jsn);
                jsn.Dispose();
                jsn = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }

        /// <summary>
        /// Unpost All passed Line IDs
        /// </summary>
        public void UnpostAll()
        {
            try
            {
                bool blnResult = true;
                Array aryPostedLineIDs = Functions.JavascriptStringToArray(GetFormValue_String("IDs"));
                for (int i = 0; i < aryPostedLineIDs.Length; i++)
                {
                    int intLineID = Convert.ToInt32(aryPostedLineIDs.GetValue(i));
                    blnResult = blnResult && SalesOrderLine.UpdatePostOrUnpost(
                        intLineID
                        , false
                        , LoginID
                    );
                }
                JsonObject jsn = new JsonObject();
                jsn.AddVariable("Result", blnResult);
                OutputResult(jsn);
                jsn.Dispose();
                jsn = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }

        /// <summary>
        /// Get Allocations for a line
        /// </summary>
        public void GetLineAllocations()
        {
            try
            {
                JsonObject jsn = new JsonObject();
                JsonObject jsnItems = new JsonObject(true);
                int intCount = 0;
                foreach (Allocation ln in Allocation.GetListForSalesOrderLine(GetFormValue_Int("LineID")))
                {
                    JsonObject jsnItem = new JsonObject();
                    jsnItem.AddVariable("AllocationID", ln.AllocationId);
                    jsnItem.AddVariable("SupplierNo", ln.CompanyNo);
                    jsnItem.AddVariable("Supplier", ln.CompanyName);
                    jsnItem.AddVariable("Part", ln.Part);
                    jsnItem.AddVariable("ROHS", ln.ROHS);
                    jsnItem.AddVariable("Quantity", Functions.FormatNumeric(ln.QuantityAllocated));
                    jsnItem.AddVariable("StockNo", ln.StockNo);
                    jsnItem.AddVariable("PurchaseOrderNo", ln.PurchaseOrderNo);
                    jsnItem.AddVariable("PurchaseOrderNumber", ln.PurchaseOrderNumber);
                    jsnItem.AddVariable("PurchaseOrderLineNo", ln.PurchaseOrderLineId);
                    jsnItem.AddVariable("LandedCost", Functions.FormatCurrency(SessionManager.IsPOHub == true ? ln.LandedCost : ln.ClientLandedCost, SessionManager.ClientCurrencyCode));
                    jsnItem.AddVariable("ExpediteDate", Functions.FormatDate(ln.ExpediteDate));
                    jsnItem.AddVariable("SupplierPart", ln.SupplierPart);
                    jsnItem.AddVariable("CRMANo", ln.CustomerRMANo);
                    jsnItem.AddVariable("CRMANumber", ln.CustomerRMANumber);
                    jsnItem.AddVariable("QuantityInStock", Functions.FormatNumeric(ln.QuantityInStock));
                    jsnItem.AddVariable("QuantityOnOrder", Functions.FormatNumeric(ln.QuantityOnOrder));
                    jsnItem.AddVariable("ReturnDate", Functions.FormatDate(ln.ReturnDate));
                    //[002] code start
                    jsnItem.AddVariable("PurchaseOrderLine", ln.PurchaseOrderLineNo);
                    //[002] code end
                    jsnItem.AddVariable("LineNo", Functions.FormatNumeric(ln.POSerialNo));

                    jsnItem.AddVariable("InternalPurchaseOrderId", ln.InternalPurchaseOrderId);
                    jsnItem.AddVariable("InternalPurchaseOrderNumber", ln.InternalPurchaseOrderNo);
                    jsnItem.AddVariable("IPOSupplier", ln.IPOSupplier);
                    jsnItem.AddVariable("IPOSupplierName", ln.IPOSupplierName);

                    jsnItems.AddVariable(jsnItem);
                    jsnItem.Dispose(); jsnItem = null;
                    intCount += 1;
                }
                jsn.AddVariable("Count", intCount);
                jsn.AddVariable("Lines", jsnItems);
                OutputResult(jsn);
                jsnItems.Dispose(); jsnItems = null;
                jsn.Dispose(); jsn = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }

        /// <summary>
        /// Get InvoiceLineAllocations for a line
        /// </summary>
        public void GetLineShippingLines()
        {
            try
            {
                JsonObject jsn = new JsonObject();
                JsonObject jsnItems = new JsonObject(true);
                int intCount = 0;
                foreach (InvoiceLine il in InvoiceLine.GetListForSalesOrderLine(ID))
                {
                    foreach (InvoiceLineAllocation al in InvoiceLineAllocation.GetListForInvoiceLine(il.InvoiceLineId))
                    {
                        double dblLineTotal = ((double)il.Price * (double)al.Quantity);
                        JsonObject jsnItem = new JsonObject();
                        jsnItem.AddVariable("ID", al.InvoiceLineAllocationId);
                        jsnItem.AddVariable("InvoiceNo", il.InvoiceNo);
                        jsnItem.AddVariable("InvoiceNumber", il.InvoiceNumber);
                        jsnItem.AddVariable("Part", il.Part);
                        jsnItem.AddVariable("StockNo", al.StockNo);
                        jsnItem.AddVariable("CustomerPart", il.CustomerPart);
                        jsnItem.AddVariable("ROHS", il.ROHS);
                        jsnItem.AddVariable("Mfr", il.ManufacturerCode);
                        jsnItem.AddVariable("MfrNo", il.ManufacturerNo);
                        jsnItem.AddVariable("Product", il.ProductName);
                        jsnItem.AddVariable("Package", il.PackageName);
                        jsnItem.AddVariable("DateCode", il.DateCode);
                        jsnItem.AddVariable("Location", al.Location);
                        jsnItem.AddVariable("InvoiceDate", Functions.FormatDate(il.InvoiceDate));
                        jsnItem.AddVariable("Quantity", Functions.FormatNumeric(al.Quantity));
                        jsnItem.AddVariable("ShippedBy", il.ShippedByName);
                        jsnItem.AddVariable("Price", Functions.FormatCurrency(il.Price, il.CurrencyCode));
                        jsnItem.AddVariable("Total", Functions.FormatCurrency(dblLineTotal, il.CurrencyCode));
                        jsnItems.AddVariable(jsnItem);
                        jsnItem.Dispose(); jsnItem = null;
                        intCount += 1;
                    }
                }
                jsn.AddVariable("Count", intCount);
                jsn.AddVariable("Items", jsnItems);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }

        /// <summary>
        /// Gets data for a new Line based on a Quote Line
        /// </summary>
        public void GetQuoteLineForNew()
        {
            try
            {
                QuoteLine ql = QuoteLine.Get(ID);
                JsonObject jsn = new JsonObject();
                jsn.AddVariable("QuoteLineID", ql.QuoteLineId);
                jsn.AddVariable("Qty", Functions.FormatNumeric(ql.Quantity));
                jsn.AddVariable("Part", ql.Part);
                jsn.AddVariable("CustomerPart", ql.CustomerPart);
                int intSOCurrencyID = GetFormValue_Int("SOCurrencyNo");
                if (intSOCurrencyID != ql.CurrencyNo) ql.Price = BLL.Currency.ConvertValueBetweenTwoCurrencies(ql.Price, (int)ql.CurrencyNo, intSOCurrencyID, GetFormValue_DateTime("SODate"));
                jsn.AddVariable("Price", Functions.FormatCurrency(ql.Price));
                jsn.AddVariable("DC", ql.DateCode);
                jsn.AddVariable("MfrNo", ql.ManufacturerNo);
                jsn.AddVariable("Mfr", ql.ManufacturerName);
                jsn.AddVariable("ProductNo", ql.ProductNo);
                jsn.AddVariable("PackageNo", ql.PackageNo);
                jsn.AddVariable("ROHS", ql.ROHS);
                //[003] code start
                jsn.AddVariable("ProductSource", ql.ProductSource);
                jsn.AddVariable("SourcingResultNo", ql.SourcingResultNo);
                if (ql.SourcingTable == "PQ" || ql.SourcingTable == "OFPH" || ql.SourcingTable == "EXPH")
                    jsn.AddVariable("IsIPO", true);
                else
                    jsn.AddVariable("IsIPO", false);
                jsn.AddVariable("PODelDate", Functions.FormatDate(ql.DeliveryDate));
                jsn.AddVariable("IsIPOExist", ql.IsIPOCreated);
                //[003] code end
                jsn.AddVariable("QuoteNotes", Functions.ReplaceLineBreaks(ql.QuoteNotes));
                jsn.AddVariable("ProductDescription", ql.ProductDescription);

                jsn.AddVariable("MSLLevel", ql.MSLLevel);
                jsn.AddVariable("PackageDescription", ql.PackageDescription);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
                ql = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }

        /// <summary>
        /// Gets data for a new Line based on a stock item
        /// </summary>
        public void GetStockLineForNew()
        {
            try
            {
                int intSOCurrencyID = GetFormValue_Int("SOCurrencyNo");
                string strSOCurrencyCode = GetFormValue_String("SOCurrencyCode");
                DateTime dtmSODate = GetFormValue_DateTime("SODate");
                Stock stk = Stock.Get(ID);
                JsonObject jsn = new JsonObject();
                jsn.AddVariable("Qty", Functions.FormatNumeric(stk.QuantityAvailable));
                jsn.AddVariable("Part", stk.Part);
                jsn.AddVariable("SupplierPart", stk.SupplierPart);
                jsn.AddVariable("DC", stk.DateCode);
                jsn.AddVariable("LandedCost", Functions.FormatCurrency(stk.LandedCost));
                jsn.AddVariable("MfrNo", stk.ManufacturerNo);
                jsn.AddVariable("Mfr", stk.ManufacturerName);
                jsn.AddVariable("ProductNo", stk.ProductNo);
                jsn.AddVariable("PackageNo", stk.PackageNo);
                jsn.AddVariable("ROHS", stk.ROHS);
                jsn.AddVariable("SourcingResultNo", 0);
                string strPrice = Functions.FormatCurrency(stk.ResalePrice);
                string strLandedCost = Functions.FormatCurrency(stk.LandedCost, SessionManager.ClientCurrencyCode);
                if (intSOCurrencyID != SessionManager.ClientCurrencyID)
                {
                    strPrice = Functions.FormatCurrency(BLL.Currency.ConvertValueFromBaseCurrency(stk.ResalePrice, intSOCurrencyID, dtmSODate));
                    strLandedCost = string.Format("{0} ({1})", strLandedCost, Functions.FormatCurrency(BLL.Currency.ConvertValueFromBaseCurrency(stk.LandedCost, intSOCurrencyID, dtmSODate), strSOCurrencyCode));
                }
                jsn.AddVariable("Price", strPrice);
                jsn.AddVariable("LandedCost", strLandedCost);
                jsn.AddVariable("ProductDescription", stk.ProductDescription);
                jsn.AddVariable("MSLLevel", stk.MSLLevel);
                jsn.AddVariable("PackageDescription", stk.PackageDescription);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
                stk = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }

        /// <summary>
        /// Gets data for a new Line based on a service item
        /// </summary>
        public void GetServiceLineForNew()
        {
            try
            {
                Service svc = Service.Get(ID);
                JsonObject jsn = new JsonObject();
                jsn.AddVariable("Qty", 1);
                jsn.AddVariable("ServiceName", svc.ServiceName);
                jsn.AddVariable("ServiceDescription", svc.ServiceDescription);
                jsn.AddVariable("SourcingResultNo", 0);
                //convert price to SO currency
                int intSOCurrencyID = GetFormValue_Int("SOCurrencyNo");
                if (intSOCurrencyID != SessionManager.ClientCurrencyID) svc.Price = BLL.Currency.ConvertValueFromBaseCurrency(svc.Price, intSOCurrencyID, GetFormValue_DateTime("SODate"));
                jsn.AddVariable("Price", Functions.FormatCurrency(svc.Price));
                jsn.AddVariable("MSLLevel", "N/A");

                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
                svc = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }

        /// <summary>
        /// Gets data for a new Line based on a Requirement
        /// </summary>
        public void GetRequirementLineForNew()
        {
            try
            {
                //SourcingResult sr = SourcingResult.Get(ID);
                //SK 28.1.2009 - was based on a sourcing result - now based purely on a customer requirement
                CustomerRequirement cr = CustomerRequirement.Get(ID);
                JsonObject jsn = new JsonObject();
                jsn.AddVariable("Qty", Functions.FormatNumeric(cr.Quantity));
                jsn.AddVariable("Part", cr.Part);
                jsn.AddVariable("SourcingResultNo", cr.SourcingResultNo);
                int intSOCurrencyID = GetFormValue_Int("SOCurrencyNo");
                string strPrice = Functions.FormatCurrency(cr.Price);
                if (intSOCurrencyID != cr.CurrencyNo) strPrice = Functions.FormatCurrency(BLL.Currency.ConvertValueBetweenTwoCurrencies(cr.Price, (int)cr.CurrencyNo, intSOCurrencyID, GetFormValue_DateTime("SODate")));
                jsn.AddVariable("Price", strPrice);
                jsn.AddVariable("DC", cr.DateCode);
                jsn.AddVariable("MfrNo", cr.ManufacturerNo);
                jsn.AddVariable("Mfr", cr.ManufacturerName);
                jsn.AddVariable("ProductNo", cr.ProductNo);
                jsn.AddVariable("PackageNo", cr.PackageNo);
                jsn.AddVariable("ROHS", cr.ROHS);
                jsn.AddVariable("ReqNotes", Functions.ReplaceLineBreaks(cr.ReqNotes));
                jsn.AddVariable("ProductDescription", cr.ProductDescription);
                jsn.AddVariable("MSLLevel", cr.MSL);
                jsn.AddVariable("PackageDescription", cr.PackageDescription);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
                cr = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }

        /// <summary>
        /// Gets data for a new Line based on a Sales Order Line
        /// </summary>
        public void GetSalesOrderLineForNew()
        {
            try
            {
                SalesOrderLine sol = SalesOrderLine.Get(ID);
                JsonObject jsn = new JsonObject();
                jsn.AddVariable("Qty", Functions.FormatNumeric(sol.Quantity));
                jsn.AddVariable("Part", sol.Part);
                jsn.AddVariable("CustomerPart", sol.CustomerPart);
                jsn.AddVariable("SourcingResultNo", sol.SourcingResultNo);
                int intSOCurrencyID = GetFormValue_Int("SOCurrencyNo");
                string strPrice = Functions.FormatCurrency(sol.Price);
                if (intSOCurrencyID != sol.CurrencyNo) strPrice = Functions.FormatCurrency(BLL.Currency.ConvertValueBetweenTwoCurrencies(sol.Price, (int)sol.CurrencyNo, intSOCurrencyID, GetFormValue_DateTime("SODate")));
                jsn.AddVariable("Price", strPrice);
                jsn.AddVariable("DC", sol.DateCode);
                jsn.AddVariable("MfrNo", sol.ManufacturerNo);
                jsn.AddVariable("Mfr", sol.ManufacturerName);
                jsn.AddVariable("ProductNo", sol.ProductNo);
                jsn.AddVariable("PackageNo", sol.PackageNo);
                jsn.AddVariable("ROHS", sol.ROHS);
                //[003] code start
                jsn.AddVariable("ProductSource", sol.ProductSource);

                //[003] code end
                jsn.AddVariable("Instructions", Functions.ReplaceLineBreaks(sol.Instructions));
                jsn.AddVariable("LineNotes", Functions.ReplaceLineBreaks(sol.LineNotes));
                jsn.AddVariable("PODelDate", Functions.FormatDate(sol.PODeliveryDate));
                jsn.AddVariable("IsIPO", sol.IsIPO);
                jsn.AddVariable("IsIPOExist", sol.IsIPOExist);
                jsn.AddVariable("ProductDescription", sol.ProductDescription);
                jsn.AddVariable("MSLLevel", sol.MSLLevel);
                jsn.AddVariable("PackageDescription", sol.PackageDescription);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
                sol = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }

        /// <summary>
        /// Gets data for a new Line based on a Sales Order Line
        /// </summary>
        public void GetSalesOrder()
        {
            try
            {
                SalesOrderLine sol = SalesOrderLine.Get(ID);
                JsonObject jsn = new JsonObject();
                jsn.AddVariable("Qty", Functions.FormatNumeric(sol.Quantity));
                jsn.AddVariable("Part", sol.Part);
                jsn.AddVariable("CustomerPart", sol.CustomerPart);
                jsn.AddVariable("SourcingResultNo", sol.SourcingResultNo);
                int intSOCurrencyID = GetFormValue_Int("SOCurrencyNo");
                //string strPrice = Functions.FormatCurrency(sol.Price);
                // if (intSOCurrencyID != sol.CurrencyNo) strPrice = Functions.FormatCurrency(BLL.Currency.ConvertValueBetweenTwoCurrencies(sol.Price, (int)sol.CurrencyNo, intSOCurrencyID, GetFormValue_DateTime("SODate")));
                jsn.AddVariable("Price", Functions.FormatCurrency(sol.Price));
                jsn.AddVariable("DC", sol.DateCode);
                jsn.AddVariable("MfrNo", sol.ManufacturerNo);
                jsn.AddVariable("Mfr", sol.ManufacturerName);
                jsn.AddVariable("ProductNo", sol.ProductNo);
                jsn.AddVariable("PackageNo", sol.PackageNo);
                jsn.AddVariable("ROHS", sol.ROHS);
                //[003] code start
                jsn.AddVariable("ProductSource", sol.ProductSource);

                //[003] code end
                jsn.AddVariable("Instructions", Functions.ReplaceLineBreaks(sol.Instructions));
                jsn.AddVariable("LineNotes", Functions.ReplaceLineBreaks(sol.LineNotes));
                jsn.AddVariable("PODelDate", Functions.FormatDate(sol.PODeliveryDate));
                jsn.AddVariable("IsIPO", sol.IsIPO);
                jsn.AddVariable("IsIPOExist", sol.IsIPOExist);
                jsn.AddVariable("ProdInactive", Convert.ToBoolean(sol.ProductInactive));
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
                sol = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }
        //[008] start
        public void GetPromiseReasonLog()
        {
            try
            {
                int solineid = GetFormValue_NullableInt("SOLineId") ?? 0;
                List<SalesOrderLine> lstsol = SalesOrderLine.GetPromiseReasonLog(solineid);

                JsonObject jsn = new JsonObject();
                JsonObject jsnItems = new JsonObject(true);
                int intCount = 0;
                foreach (SalesOrderLine sol in lstsol)
                {
                    JsonObject jsnItem = new JsonObject();
                    jsnItem.AddVariable("PromiseReason", sol.PromiseReason);
                    jsnItem.AddVariable("UpdatedBy", sol.EmployeeName);
                    jsnItem.AddVariable("UpdatedDate", Functions.FormatDate(sol.UpdatedDate));
                    jsnItem.AddVariable("PromiseDateOld", Functions.FormatDate(sol.PromiseDateOld));
                    //jsnItem.AddVariable("PromiseDateNew", Functions.FormatDate(sol.PromiseDateNew));
                    jsnItems.AddVariable(jsnItem);
                    jsnItem.Dispose(); jsnItem = null;
                    intCount += 1;
                }
                jsn.AddVariable("Count", intCount);
                jsn.AddVariable("Items", jsnItems);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;

            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }
        //[008] end

        private void AddNewAllocation()
        {
            try
            {
                int intNewID = Allocation.Insert(
                      GetFormValue_NullableInt("StockID")
                    , GetFormValue_NullableInt("LineID")
                    , GetFormValue_NullableInt("Quantity")
                    , null //supplierRMALineNo - was ID - ? 
                    , LoginID
                    , GetFormValue_Boolean("SONonIPO")
                );
                JsonObject jsn = new JsonObject();
                jsn.AddVariable("Result", intNewID > 0);
                jsn.AddVariable("ID", intNewID);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }

        private void AddNewIpoAllocation()
        {
            try
            {
                int intNewID = Allocation.InsertIPO(
                      GetFormValue_NullableInt("StockID")
                    , GetFormValue_NullableInt("LineID")
                    , GetFormValue_NullableInt("Quantity")
                    , null //supplierRMALineNo - was ID - ? 
                    , LoginID
                    , GetFormValue_Boolean("SONonIPO")
                );
                JsonObject jsn = new JsonObject();
                jsn.AddVariable("Result", intNewID > 0);
                jsn.AddVariable("ID", intNewID);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }

        /// <summary>
        /// Delete allocation
        /// </summary>
        private void DeleteAllocation()
        {
            try
            {
                Boolean? CallFromPO = false;
                CallFromPO = GetFormValue_NullableBoolean("CallFromPO");
                Array ary = Functions.JavascriptStringToArray(GetFormValue_String("LineIDs"));
                if (CallFromPO == true)
                {
                    string part = GetFormValue_String("Part");
                    int InternalPurchaseOrderNumber = GetFormValue_Int("InternalPurchaseOrderNumber");
                    int Quantity = GetFormValue_Int("Quantity");
                    int PoLine = GetFormValue_Int("PoLine");
                    int IntPOLineID = GetFormValue_Int("intPOLineID");
                    //int? SalesMan = 0;
                    int MailGroupId = GetFormValue_Int("MailGroupId");
                    int CompanyNo = 0;

                    if (SessionManager.IsPOHub == true && InternalPurchaseOrderNumber > 0)
                    {
                        StringBuilder message = new StringBuilder();
                        WebServices servic = new WebServices();
                        message.Append("IPO No : " + InternalPurchaseOrderNumber + ", Line : " + PoLine + ", Part : " + part + ", Qty : " + Quantity + "<br />");
                        message.Append("Deallocated from the following Sales order(s) By  : " + SessionManager.LoginFullName + "<br />");
                        //  message.Append("Date & Time  : " + Functions.FormatDate(System.DateTime.Now) + " " + Functions.FormatTime(System.DateTime.Now) + "<br />");

                        string Subject = "IPO No : " + InternalPurchaseOrderNumber + ", Line : " + PoLine + " Deallocation Notification from HUB";
                        List<Allocation> lst = Allocation.GetListForPurchaseOrderLine(IntPOLineID);
                        List<int> ids = new List<int>();
                        for (int i = 0; i < ary.Length; i++)
                        {
                            ids.Add(Convert.ToInt32(ary.GetValue(i)));
                        }
                        // = GetFormValue_String("LineIDs").Split('||').Select(i => int.Parse(i)).ToList();
                        //   lst.Add(new Allocation() { AllocationId = 7655 });
                        //   lst.Add(new Allocation() { AllocationId = 7575 });
                        List<Allocation> filteredList = lst.Where(x => ids.Contains(x.AllocationId)).ToList();
                        string strSalesMan = "";
                        if (filteredList.Count > 0)
                        {
                            strSalesMan = filteredList.Select(x => x.Salesman.ToString()).Distinct().Aggregate((a, b) => a.ToString() + "||" + b.ToString()).ToString();
                        }

                        foreach (Allocation listFiltered in filteredList)
                        {
                            // SalesMan = listFiltered.Salesman;
                            CompanyNo = listFiltered.CompanyNo;
                            message.Append("SO : " + listFiltered.SalesOrderNumber + ", Line : " + listFiltered.SOSerialNo + ", Qty : " + Functions.FormatNumeric(listFiltered.QuantityAllocated) + "<br />");
                        }
                        message.Append("<br /><br />Regards,<br />" + SessionManager.LoginFullName + "<br />");

                        servic.NotifyMessageExpediteNote(Convert.ToString(strSalesMan), Convert.ToString(MailGroupId), Subject, Convert.ToString(message), CompanyNo, false);
                        message = null;
                        servic = null;
                    }
                }




                bool blnResult = true;
                for (int i = 0; i < ary.Length; i++)
                {
                    blnResult = blnResult && Allocation.Delete(Convert.ToInt32(ary.GetValue(i)), LoginID);
                }
                JsonObject jsn = new JsonObject();
                jsn.AddVariable("Result", blnResult);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }

        /// <summary>
        /// Get Stock item for Allocation
        /// </summary>
        private void GetStockForIpoAllocation()
        {
            try
            {
                JsonObject jsn = new JsonObject();
                Stock stk = Stock.Get(ID);
                jsn.AddVariable("StockNo", stk.StockId);
                jsn.AddVariable("Part", stk.Part);
                jsn.AddVariable("SupplierPart", stk.SupplierPart);
                jsn.AddVariable("ROHS", stk.ROHS);
                jsn.AddVariable("DateCode", stk.DateCode);
                jsn.AddVariable("ManufacturerNo", stk.ManufacturerNo);
                jsn.AddVariable("Manufacturer", stk.ManufacturerName);
                jsn.AddVariable("ProductNo", stk.ProductNo);
                jsn.AddVariable("ProductName", stk.ProductName);
                jsn.AddVariable("Product", stk.ProductDescription);
                jsn.AddVariable("PackageNo", stk.PackageNo);
                jsn.AddVariable("PackageName", stk.PackageName);
                jsn.AddVariable("Package", stk.PackageDescription);
                jsn.AddVariable("WarehouseNo", stk.WarehouseNo);
                jsn.AddVariable("Warehouse", stk.WarehouseName);
                jsn.AddVariable("QCNotes", Functions.ReplaceLineBreaks(stk.QualityControlNotes));
                jsn.AddVariable("QtyStock", Functions.FormatNumeric(stk.QuantityInStock));
                jsn.AddVariable("QtyOrder", Functions.FormatNumeric(stk.QuantityOnOrder));
                jsn.AddVariable("QtyAllocated", Functions.FormatNumeric(stk.QuantityAllocated));
                jsn.AddVariable("QtyAvailable", Functions.FormatNumeric(stk.QuantityAvailable));
                jsn.AddVariable("Unavailable", stk.Unavailable);
                jsn.AddVariable("Supplier", stk.SupplierName);
                jsn.AddVariable("IPOSupplier", stk.IPOSupplierName);
                jsn.AddVariable("IPONo", stk.IPONo);
                jsn.AddVariable("IsIPOStock", Convert.ToBoolean((stk.IPONo.HasValue && stk.IPONo.Value > 0)));
                //convert price and cost to SO currency
                int intSOCurrencyID = GetFormValue_Int("SOCurrencyNo");
                string strSOCurrencyCode = GetFormValue_String("SOCurrencyCode");
                DateTime dtmSODate = GetFormValue_DateTime("SODate");
                string strResalePrice = Functions.FormatCurrency(stk.ResalePrice, SessionManager.ClientCurrencyCode);
                string strLandedCost = Functions.FormatCurrency(stk.LandedCost, SessionManager.ClientCurrencyCode);
                if (intSOCurrencyID != SessionManager.ClientCurrencyID)
                {
                    strResalePrice = String.Format("{0} ({1})", Functions.FormatCurrency(BLL.Currency.ConvertValueFromBaseCurrency(stk.ResalePrice, intSOCurrencyID, dtmSODate), strSOCurrencyCode), strResalePrice);
                    strLandedCost = String.Format("{0} ({1})", strLandedCost, Functions.FormatCurrency(BLL.Currency.ConvertValueFromBaseCurrency(stk.LandedCost, intSOCurrencyID, dtmSODate), strSOCurrencyCode));
                }
                jsn.AddVariable("ResalePrice", strResalePrice);
                jsn.AddVariable("LandedCost", strLandedCost);

                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
                stk = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }


        /// <summary>
        /// Get Stock item for Allocation
        /// </summary>
        private void GetStockForAllocation()
        {
            try
            {
                JsonObject jsn = new JsonObject();
                Stock stk = Stock.Get(ID);
                jsn.AddVariable("StockNo", stk.StockId);
                jsn.AddVariable("Part", stk.Part);
                jsn.AddVariable("SupplierPart", stk.SupplierPart);
                jsn.AddVariable("ROHS", stk.ROHS);
                jsn.AddVariable("DateCode", stk.DateCode);
                jsn.AddVariable("ManufacturerNo", stk.ManufacturerNo);
                jsn.AddVariable("Manufacturer", stk.ManufacturerName);
                jsn.AddVariable("ProductNo", stk.ProductNo);
                jsn.AddVariable("ProductName", stk.ProductName);
                jsn.AddVariable("Product", stk.ProductDescription);
                jsn.AddVariable("PackageNo", stk.PackageNo);
                jsn.AddVariable("PackageName", stk.PackageName);
                jsn.AddVariable("Package", stk.PackageDescription);
                jsn.AddVariable("WarehouseNo", stk.WarehouseNo);
                jsn.AddVariable("Warehouse", stk.WarehouseName);
                jsn.AddVariable("QCNotes", Functions.ReplaceLineBreaks(stk.QualityControlNotes));
                jsn.AddVariable("QtyStock", Functions.FormatNumeric(stk.QuantityInStock));
                jsn.AddVariable("QtyOrder", Functions.FormatNumeric(stk.QuantityOnOrder));
                jsn.AddVariable("QtyAllocated", Functions.FormatNumeric(stk.QuantityAllocated));
                jsn.AddVariable("QtyAvailable", Functions.FormatNumeric(stk.QuantityAvailable));
                jsn.AddVariable("Unavailable", stk.Unavailable);
                jsn.AddVariable("Supplier", stk.SupplierName);
                jsn.AddVariable("IsIPOStock", Convert.ToBoolean((stk.IPONo.HasValue && stk.IPONo.Value > 0)));

                //convert price and cost to SO currency
                int intSOCurrencyID = GetFormValue_Int("SOCurrencyNo");
                string strSOCurrencyCode = GetFormValue_String("SOCurrencyCode");
                DateTime dtmSODate = GetFormValue_DateTime("SODate");
                string strResalePrice = Functions.FormatCurrency(stk.ResalePrice, SessionManager.ClientCurrencyCode);
                string strLandedCost = Functions.FormatCurrency(stk.LandedCost, SessionManager.ClientCurrencyCode);
                if (intSOCurrencyID != SessionManager.ClientCurrencyID)
                {
                    strResalePrice = String.Format("{0} ({1})", Functions.FormatCurrency(BLL.Currency.ConvertValueFromBaseCurrency(stk.ResalePrice, intSOCurrencyID, dtmSODate), strSOCurrencyCode), strResalePrice);
                    strLandedCost = String.Format("{0} ({1})", strLandedCost, Functions.FormatCurrency(BLL.Currency.ConvertValueFromBaseCurrency(stk.LandedCost, intSOCurrencyID, dtmSODate), strSOCurrencyCode));
                }
                jsn.AddVariable("ResalePrice", strResalePrice);
                jsn.AddVariable("LandedCost", strLandedCost);

                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
                stk = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }

        /// <summary>
        /// Gets data for a service-based salesOrderLine
        /// </summary>
        public void GetServiceLineForInvoice()
        {
            try
            {
                JsonObject jsn = new JsonObject();
                JsonObject jsnItems = new JsonObject(true);
                int intCount = 0;
                foreach (SalesOrderLine sol in SalesOrderLine.GetListServiceForInvoice(ID))
                {
                    JsonObject jsnItem = new JsonObject();
                    jsnItem.AddVariable("ID", sol.SalesOrderLineId);
                    jsnItem.AddVariable("Service", sol.Part);
                    jsnItem.AddVariable("Quantity", Functions.FormatNumeric(sol.Quantity));
                    jsnItem.AddVariable("Price", Functions.FormatCurrency(sol.Price, sol.CurrencyCode));
                    jsnItems.AddVariable(jsnItem);
                    jsnItem.Dispose(); jsnItem = null;
                    intCount += 1;
                }
                jsn.AddVariable("Count", intCount);
                jsn.AddVariable("Items", jsnItems);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }

        /// <summary>
        /// Close
        /// </summary>
        public void Close()
        {
            try
            {
                bool blnResult = SalesOrderLine.UpdateClose(
                    ID,
                    GetFormValue_Boolean("ResetQuantity"),
                    LoginID
                );
                JsonObject jsn = new JsonObject();
                jsn.AddVariable("Result", blnResult);
                OutputResult(jsn);
                jsn.Dispose();
                jsn = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }

        private int GetSerialNumber(ConcurrentDictionary<int, int> dic, int intkey)
        {
            int intValue = 0;
            if (dic != null)
            {

                dic.TryGetValue(intkey, out intValue);
            }
            return intValue;
        }
        //[006] start
        private void SaveConfirmation()
        {
            try
            {

                int intNewID = (new SalesOrderLine()).SaveConfirmation(ID, GetFormValue_Int("intSalesOrderID"), GetFormValue_Int("intClickedButton"));
                JsonObject jsn = new JsonObject();
                jsn.AddVariable("Result", intNewID > 0);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }
        //[006] end

        public void SaveEditAll()
        {
            try
            {
                bool blnResult = false;

                // string oldPromiseDate = sl.DatePromised.ToShortDateString();

                // sl.DatePromised = ;
                // sl.UpdatedBy = LoginID;
                //[003] code start
                //sl.Email = GetFormValue_String("Email");
                ////start [009]
                //string strEmailTo = "";
                //MailAddressCollection adrTo = null;
                //string strEmailFrom = SessionManager.LoginEmail;
                //string strSubject = "SO {0} Promised Date Changed";
                //if (!string.IsNullOrEmpty(sl.Email) && oldPromiseDate != sl.DatePromised.ToShortDateString())
                //{
                //    strSubject = string.Format(strSubject, sl.SalesOrderNumber.ToString());
                //    strEmailTo = sl.Email;
                //    adrTo = new MailAddressCollection();
                //    adrTo.Add(strEmailTo);
                //    System.Net.Mail.MailAddress adrFrom = new MailAddress(strEmailFrom);
                //    System.Net.Mail.MailAddress adrReplyTo = new MailAddress(strEmailFrom);

                //    string messageBodyContent = string.Format(Functions.GetGlobalResource("MailTemplates", "PromiseReasonEmail"),
                //                                            sl.SalesOrderNumber.ToString(),
                //                                            sl.DatePromised.ToShortDateString(),
                //                                            GetFormValue_String("Reason"),
                //                                            SessionManager.LoginFullName);
                //    OutgoingMailManager.SendUserMail(strSubject, RemoveHyperLink(messageBodyContent), adrTo, adrFrom, null, null, adrReplyTo, true);
                //}
                //end [009]
                blnResult = SalesOrderLine.UpdateAll(ID, GetFormValue_DateTime("DatePromised"), null, LoginID, GetFormValue_Boolean("IsFormChanged"),
                    GetFormValue_Boolean("IsReasonChanged"),
                    GetFormValue_Int("PromiseReasonNo")
                    );

                JsonObject jsn = new JsonObject();
                jsn.AddVariable("Result", blnResult);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }

        private void GetSOIHSEccnDetail()
        {
            int intloginid = SessionManager.LoginID ?? 0;
            List<BLL.Part> lst = null;
            try
            {
                lst = BLL.Part.SOIHSEccnDetial(
                      SessionManager.ClientID,
                       GetFormValue_String("Part"),
                       intloginid


                    );

                JsonObject jsn = new JsonObject();
                JsonObject jsnRows = new JsonObject(true);
                for (int i = 0; i < lst.Count; i++)
                {
                    if (i < lst.Count)
                    {
                        JsonObject jsnRow = new JsonObject();
                        jsnRow.AddVariable("ECCNNo", lst[i].ECCNNo);
                        jsnRow.AddVariable("ECCNCode", Functions.ReplaceLineBreaks(lst[i].ECCNCode));
                        jsnRows.AddVariable(jsnRow);
                        jsnRow.Dispose();
                        jsnRow = null;
                    }
                }
                if (lst.Count == 0)
                {
                    JsonObject jsnRow = new JsonObject();
                    jsnRow.AddVariable("ECCNNo", 0);
                    jsnRow.AddVariable("ECCNCode", "");
                    jsnRows.AddVariable(jsnRow);
                    jsnRow.Dispose();
                    jsnRow = null;
                }

                jsn.AddVariable("Result", jsnRows);
                OutputResult(jsn);
                jsnRows.Dispose();
                jsnRows = null;
                jsn.Dispose();
                jsn = null;
                lst = null;

            }
            catch (Exception e)
            {
                WriteError(e);
            }
            finally
            {
                lst = null;
            }
        }


    }

}
