Type.registerNamespace("Rebound.GlobalTrader.Site.Pages.Default");Rebound.GlobalTrader.Site.Pages.Default=function(n){Rebound.GlobalTrader.Site.Pages.Default.initializeBase(this,[n]);this._intDifferentUserID=0;var t=this};Rebound.GlobalTrader.Site.Pages.Default.prototype={get_ctlPageTitle:function(){return this._ctlPageTitle},set_ctlPageTitle:function(n){this._ctlPageTitle!==n&&(this._ctlPageTitle=n)},get_ctlTopLinks:function(){return this._ctlTopLinks},set_ctlTopLinks:function(n){this._ctlTopLinks!==n&&(this._ctlTopLinks=n)},get_chkMyself:function(){return this._chkMyself},set_chkMyself:function(n){this._chkMyself!==n&&(this._chkMyself=n)},get_chkIncludeCredit:function(){return this._chkIncludeCredit},set_chkIncludeCredit:function(n){this._chkIncludeCredit!==n&&(this._chkIncludeCredit=n)},get_pnlChooseUser:function(){return this._pnlChooseUser},set_pnlChooseUser:function(n){this._pnlChooseUser!==n&&(this._pnlChooseUser=n)},get_ddlEmployee:function(){return this._ddlEmployee},set_ddlEmployee:function(n){this._ddlEmployee!==n&&(this._ddlEmployee=n)},get_intCurrentTab:function(){return this._intCurrentTab},set_intCurrentTab:function(n){this._intCurrentTab!==n&&(this._intCurrentTab=n)},get_pnlToday:function(){return this._pnlToday},set_pnlToday:function(n){this._pnlToday!==n&&(this._pnlToday=n)},get_pnlAccounts:function(){return this._pnlAccounts},set_pnlAccounts:function(n){this._pnlAccounts!==n&&(this._pnlAccounts=n)},get_pnlBroker:function(){return this._pnlBroker},set_pnlBroker:function(n){this._pnlBroker!==n&&(this._pnlBroker=n)},get_pnlPurchasing:function(){return this._pnlPurchasing},set_pnlPurchasing:function(n){this._pnlPurchasing!==n&&(this._pnlPurchasing=n)},get_pnlSales:function(){return this._pnlSales},set_pnlSales:function(n){this._pnlSales!==n&&(this._pnlSales=n)},get_pnlWarehouse:function(){return this._pnlWarehouse},set_pnlWarehouse:function(n){this._pnlWarehouse!==n&&(this._pnlWarehouse=n)},get_PnlBI:function(){return this._PnlBI},set_PnlBI:function(n){this._PnlBI!==n&&(this._PnlBI=n)},get_ctlToday_TopSalespersons:function(){return this._ctlToday_TopSalespersons},set_ctlToday_TopSalespersons:function(n){this._ctlToday_TopSalespersons!==n&&(this._ctlToday_TopSalespersons=n)},get_ctlToday_MyRecentActivity:function(){return this._ctlToday_MyRecentActivity},set_ctlToday_MyRecentActivity:function(n){this._ctlToday_MyRecentActivity!==n&&(this._ctlToday_MyRecentActivity=n)},get_ctlToday_PartsOrdered:function(){return this._ctlToday_PartsOrdered},set_ctlToday_PartsOrdered:function(n){this._ctlToday_PartsOrdered!==n&&(this._ctlToday_PartsOrdered=n)},get_ctlToday_MyToDoList:function(){return this._ctlToday_MyToDoList},set_ctlToday_MyToDoList:function(n){this._ctlToday_MyToDoList!==n&&(this._ctlToday_MyToDoList=n)},get_ctlToday_MyMessages:function(){return this._ctlToday_MyMessages},set_ctlToday_MyMessages:function(n){this._ctlToday_MyMessages!==n&&(this._ctlToday_MyMessages=n)},get_ctlAccounts_AllStatistics:function(){return this._ctlAccounts_AllStatistics},set_ctlAccounts_AllStatistics:function(n){this._ctlAccounts_AllStatistics!==n&&(this._ctlAccounts_AllStatistics=n)},get_ctlAccounts_MyToDoList:function(){return this._ctlAccounts_MyToDoList},set_ctlAccounts_MyToDoList:function(n){this._ctlAccounts_MyToDoList!==n&&(this._ctlAccounts_MyToDoList=n)},get_ctlAccounts_ApprovedCustomersOnStop:function(){return this._ctlAccounts_ApprovedCustomersOnStop},set_ctlAccounts_ApprovedCustomersOnStop:function(n){this._ctlAccounts_ApprovedCustomersOnStop!==n&&(this._ctlAccounts_ApprovedCustomersOnStop=n)},get_ctlAccounts_TodaysShippedOrders:function(){return this._ctlAccounts_TodaysShippedOrders},set_ctlAccounts_TodaysShippedOrders:function(n){this._ctlAccounts_TodaysShippedOrders!==n&&(this._ctlAccounts_TodaysShippedOrders=n)},get_ctlAccounts_ReceivedOrders:function(){return this._ctlAccounts_ReceivedOrders},set_ctlAccounts_ReceivedOrders:function(n){this._ctlAccounts_ReceivedOrders!==n&&(this._ctlAccounts_ReceivedOrders=n)},get_ctlBroker_MyStatistics:function(){return this._ctlBroker_MyStatistics},set_ctlBroker_MyStatistics:function(n){this._ctlBroker_MyStatistics!==n&&(this._ctlBroker_MyStatistics=n)},get_ctlBroker_MyApprovedCustomersOnStop:function(){return this._ctlBroker_MyApprovedCustomersOnStop},set_ctlBroker_MyApprovedCustomersOnStop:function(n){this._ctlBroker_MyApprovedCustomersOnStop!==n&&(this._ctlBroker_MyApprovedCustomersOnStop=n)},get_ctlBroker_ReceivedOrders:function(){return this._ctlBroker_ReceivedOrders},set_ctlBroker_ReceivedOrders:function(n){this._ctlBroker_ReceivedOrders!==n&&(this._ctlBroker_ReceivedOrders=n)},get_ctlBroker_MyRecentlyShippedOrders:function(){return this._ctlBroker_MyRecentlyShippedOrders},set_ctlBroker_MyRecentlyShippedOrders:function(n){this._ctlBroker_MyRecentlyShippedOrders!==n&&(this._ctlBroker_MyRecentlyShippedOrders=n)},get_ctlBroker_MyOpenSalesOrders:function(){return this._ctlBroker_MyOpenSalesOrders},set_ctlBroker_MyOpenSalesOrders:function(n){this._ctlBroker_MyOpenSalesOrders!==n&&(this._ctlBroker_MyOpenSalesOrders=n)},get_ctlBroker_MyOpenPurchaseOrders:function(){return this._ctlBroker_MyOpenPurchaseOrders},set_ctlBroker_MyOpenPurchaseOrders:function(n){this._ctlBroker_MyOpenPurchaseOrders!==n&&(this._ctlBroker_MyOpenPurchaseOrders=n)},get_ctlBroker_MyOpenQuotes:function(){return this._ctlBroker_MyOpenQuotes},set_ctlBroker_MyOpenQuotes:function(n){this._ctlBroker_MyOpenQuotes!==n&&(this._ctlBroker_MyOpenQuotes=n)},get_ctlBroker_MyOpenRequirements:function(){return this._ctlBroker_MyOpenRequirements},set_ctlBroker_MyOpenRequirements:function(n){this._ctlBroker_MyOpenRequirements!==n&&(this._ctlBroker_MyOpenRequirements=n)},get_ctlBroker_MyToDoList:function(){return this._ctlBroker_MyToDoList},set_ctlBroker_MyToDoList:function(n){this._ctlBroker_MyToDoList!==n&&(this._ctlBroker_MyToDoList=n)},get_ctlPurchasing_ReceivedOrders:function(){return this._ctlPurchasing_ReceivedOrders},set_ctlPurchasing_ReceivedOrders:function(n){this._ctlPurchasing_ReceivedOrders!==n&&(this._ctlPurchasing_ReceivedOrders=n)},get_ctlPurchasing_MyToDoList:function(){return this._ctlPurchasing_MyToDoList},set_ctlPurchasing_MyToDoList:function(n){this._ctlPurchasing_MyToDoList!==n&&(this._ctlPurchasing_MyToDoList=n)},get_ctlPurchasing_MyOpenPurchaseOrders:function(){return this._ctlPurchasing_MyOpenPurchaseOrders},set_ctlPurchasing_MyOpenPurchaseOrders:function(n){this._ctlPurchasing_MyOpenPurchaseOrders!==n&&(this._ctlPurchasing_MyOpenPurchaseOrders=n)},get_ctlSales_MyStatistics:function(){return this._ctlSales_MyStatistics},set_ctlSales_MyStatistics:function(n){this._ctlSales_MyStatistics!==n&&(this._ctlSales_MyStatistics=n)},get_ctlSales_MyToDoList:function(){return this._ctlSales_MyToDoList},set_ctlSales_MyToDoList:function(n){this._ctlSales_MyToDoList!==n&&(this._ctlSales_MyToDoList=n)},get_ctlSales_MyApprovedCustomersOnStop:function(){return this._ctlSales_MyApprovedCustomersOnStop},set_ctlSales_MyApprovedCustomersOnStop:function(n){this._ctlSales_MyApprovedCustomersOnStop!==n&&(this._ctlSales_MyApprovedCustomersOnStop=n)},get_ctlSales_MyRecentlyShippedOrders:function(){return this._ctlSales_MyRecentlyShippedOrders},set_ctlSales_MyRecentlyShippedOrders:function(n){this._ctlSales_MyRecentlyShippedOrders!==n&&(this._ctlSales_MyRecentlyShippedOrders=n)},get_ctlSales_MyOpenSalesOrders:function(){return this._ctlSales_MyOpenSalesOrders},set_ctlSales_MyOpenSalesOrders:function(n){this._ctlSales_MyOpenSalesOrders!==n&&(this._ctlSales_MyOpenSalesOrders=n)},get_ctlSales_MyOpenQuotes:function(){return this._ctlSales_MyOpenQuotes},set_ctlSales_MyOpenQuotes:function(n){this._ctlSales_MyOpenQuotes!==n&&(this._ctlSales_MyOpenQuotes=n)},get_ctlSales_MyOpenRequirements:function(){return this._ctlSales_MyOpenRequirements},set_ctlSales_MyOpenRequirements:function(n){this._ctlSales_MyOpenRequirements!==n&&(this._ctlSales_MyOpenRequirements=n)},get_ctlWarehouse_SalesOrdersReadyToShip:function(){return this._ctlWarehouse_SalesOrdersReadyToShip},set_ctlWarehouse_SalesOrdersReadyToShip:function(n){this._ctlWarehouse_SalesOrdersReadyToShip!==n&&(this._ctlWarehouse_SalesOrdersReadyToShip=n)},get_ctlWarehouse_TodaysShippedOrders:function(){return this._ctlWarehouse_TodaysShippedOrders},set_ctlWarehouse_TodaysShippedOrders:function(n){this._ctlWarehouse_TodaysShippedOrders!==n&&(this._ctlWarehouse_TodaysShippedOrders=n)},get_ctlWarehouse_MyToDoList:function(){return this._ctlWarehouse_MyToDoList},set_ctlWarehouse_MyToDoList:function(n){this._ctlWarehouse_MyToDoList!==n&&(this._ctlWarehouse_MyToDoList=n)},get_ctlWarehouse_AllPurchaseOrdersDueIn:function(){return this._ctlWarehouse_AllPurchaseOrdersDueIn},set_ctlWarehouse_AllPurchaseOrdersDueIn:function(n){this._ctlWarehouse_AllPurchaseOrdersDueIn!==n&&(this._ctlWarehouse_AllPurchaseOrdersDueIn=n)},get_ctlWarehouse_MyApprovedCustomersOnStop:function(){return this._ctlWarehouse_MyApprovedCustomersOnStop},set_ctlWarehouse_MyApprovedCustomersOnStop:function(n){this._ctlWarehouse_MyApprovedCustomersOnStop!==n&&(this._ctlWarehouse_MyApprovedCustomersOnStop=n)},get_ctlToday_Bom:function(){return this._ctlToday_Bom},set_ctlToday_Bom:function(n){this._ctlToday_Bom!==n&&(this._ctlToday_Bom=n)},get_ctlToday_BomManager:function(){return this._ctlToday_BomManager},set_ctlToday_BomManager:function(n){this._ctlToday_BomManager!==n&&(this._ctlToday_BomManager=n)},get_ctlToday_TodayOpenPurchaseOrders:function(){return this._ctlToday_TodayOpenPurchaseOrders},set_ctlToday_TodayOpenPurchaseOrders:function(n){this._ctlToday_TodayOpenPurchaseOrders!==n&&(this._ctlToday_TodayOpenPurchaseOrders=n)},get_ctlToday_UnProcessSalesOrders:function(){return this._ctlToday_UnProcessSalesOrders},set_ctlToday_UnProcessSalesOrders:function(n){this._ctlToday_UnProcessSalesOrders!==n&&(this._ctlToday_UnProcessSalesOrders=n)},get_ctlToday_UncheckedIPO:function(){return this._ctlToday_UncheckedIPO},set_ctlToday_UncheckedIPO:function(n){this._ctlToday_UncheckedIPO!==n&&(this._ctlToday_UncheckedIPO=n)},get_ctlToday_OpenSupplierPOApproval:function(){return this._ctlToday_OpenSupplierPOApproval},set_ctlToday_OpenSupplierPOApproval:function(n){this._ctlToday_OpenSupplierPOApproval!==n&&(this._ctlToday_OpenSupplierPOApproval=n)},get_ctlBroker_CustomerOrderValue:function(){return this._ctlBroker_CustomerOrderValue},set_ctlBroker_CustomerOrderValue:function(n){this._ctlBroker_CustomerOrderValue!==n&&(this._ctlBroker_CustomerOrderValue=n)},get_ctlSales_CustomerOrderValue:function(){return this._ctlSales_CustomerOrderValue},set_ctlSales_CustomerOrderValue:function(n){this._ctlSales_CustomerOrderValue!==n&&(this._ctlSales_CustomerOrderValue=n)},get_ctlPurchasing_PODeliveryStatus:function(){return this._ctlPurchasing_PODeliveryStatus},set_ctlPurchasing_PODeliveryStatus:function(n){this._ctlPurchasing_PODeliveryStatus!==n&&(this._ctlPurchasing_PODeliveryStatus=n)},get_ctlToday_MyGIQueries:function(){return this._ctlToday_MyGIQueries},set_ctlToday_MyGIQueries:function(n){this._ctlToday_MyGIQueries!==n&&(this._ctlToday_MyGIQueries=n)},get_ctlToday_MyQualityGIQueries:function(){return this._ctlToday_MyQualityGIQueries},set_ctlToday_MyQualityGIQueries:function(n){this._ctlToday_MyQualityGIQueries!==n&&(this._ctlToday_MyQualityGIQueries=n)},get_ctlToday_PowerBIActivity:function(){return this._ctlToday_PowerBIActivity},set_ctlToday_PowerBIActivity:function(n){this._ctlToday_PowerBIActivity!==n&&(this._ctlToday_PowerBIActivity=n)},get_ctlToday_PowerBiSalesDashboard:function(){return this._ctlToday_PowerBiSalesDashboard},set_ctlToday_PowerBiSalesDashboard:function(n){this._ctlToday_PowerBiSalesDashboard!==n&&(this._ctlToday_PowerBiSalesDashboard=n)},initialize:function(){Rebound.GlobalTrader.Site.Pages.Default.callBaseMethod(this,"initialize")},goInit:function(){this._ctlTopLinks&&(this._chkMyself.addClick(Function.createDelegate(this,this.selectMyselfOrOther)),this._chkIncludeCredit.addClick(Function.createDelegate(this,this.selectIncludeCredit)),this._ddlEmployee.addChanged(Function.createDelegate(this,this.selectLogin)));this.changeTab(this._intCurrentTab)},dispose:function(){this.isDisposed||(this._ctlTopLinks&&this._ctlTopLinks.dispose(),this._ctlPageTitle&&this._ctlPageTitle.dispose(),this._chkMyself&&this._chkMyself.dispose(),this._chkIncludeCredit&&this._chkIncludeCredit.dispose(),this._ctlToday_TopSalespersons&&this._ctlToday_TopSalespersons.dispose(),this._ctlToday_MyRecentActivity&&this._ctlToday_MyRecentActivity.dispose(),this._ctlToday_PartsOrdered&&this._ctlToday_PartsOrdered.dispose(),this._ctlToday_MyToDoList&&this._ctlToday_MyToDoList.dispose(),this._ctlToday_MyMessages&&this._ctlToday_MyMessages.dispose(),this._ctlAccounts_AllStatistics&&this._ctlAccounts_AllStatistics.dispose(),this._ctlAccounts_MyToDoList&&this._ctlAccounts_MyToDoList.dispose(),this._ctlAccounts_ApprovedCustomersOnStop&&this._ctlAccounts_ApprovedCustomersOnStop.dispose(),this._ctlAccounts_TodaysShippedOrders&&this._ctlAccounts_TodaysShippedOrders.dispose(),this._ctlAccounts_ReceivedOrders&&this._ctlAccounts_ReceivedOrders.dispose(),this._ctlBroker_MyStatistics&&this._ctlBroker_MyStatistics.dispose(),this._ctlBroker_MyApprovedCustomersOnStop&&this._ctlBroker_MyApprovedCustomersOnStop.dispose(),this._ctlBroker_ReceivedOrders&&this._ctlBroker_ReceivedOrders.dispose(),this._ctlBroker_MyRecentlyShippedOrders&&this._ctlBroker_MyRecentlyShippedOrders.dispose(),this._ctlBroker_MyOpenSalesOrders&&this._ctlBroker_MyOpenSalesOrders.dispose(),this._ctlBroker_MyOpenPurchaseOrders&&this._ctlBroker_MyOpenPurchaseOrders.dispose(),this._ctlBroker_MyOpenQuotes&&this._ctlBroker_MyOpenQuotes.dispose(),this._ctlBroker_MyToDoList&&this._ctlBroker_MyToDoList.dispose(),this._ctlBroker_MyOpenRequirements&&this._ctlBroker_MyOpenRequirements.dispose(),this._ctlPurchasing_ReceivedOrders&&this._ctlPurchasing_ReceivedOrders.dispose(),this._ctlPurchasing_MyToDoList&&this._ctlPurchasing_MyToDoList.dispose(),this._ctlPurchasing_MyOpenPurchaseOrders&&this._ctlPurchasing_MyOpenPurchaseOrders.dispose(),this._ctlSales_MyStatistics&&this._ctlSales_MyStatistics.dispose(),this._ctlSales_MyToDoList&&this._ctlSales_MyToDoList.dispose(),this._ctlSales_MyApprovedCustomersOnStop&&this._ctlSales_MyApprovedCustomersOnStop.dispose(),this._ctlSales_MyRecentlyShippedOrders&&this._ctlSales_MyRecentlyShippedOrders.dispose(),this._ctlSales_MyOpenSalesOrders&&this._ctlSales_MyOpenSalesOrders.dispose(),this._ctlSales_MyOpenQuotes&&this._ctlSales_MyOpenQuotes.dispose(),this._ctlSales_MyOpenRequirements&&this._ctlSales_MyOpenRequirements.dispose(),this._ctlWarehouse_SalesOrdersReadyToShip&&this._ctlWarehouse_SalesOrdersReadyToShip.dispose(),this._ctlWarehouse_TodaysShippedOrders&&this._ctlWarehouse_TodaysShippedOrders.dispose(),this._ctlWarehouse_MyToDoList&&this._ctlWarehouse_MyToDoList.dispose(),this._ctlWarehouse_AllPurchaseOrdersDueIn&&this._ctlWarehouse_AllPurchaseOrdersDueIn.dispose(),this._ctlWarehouse_MyApprovedCustomersOnStop&&this._ctlWarehouse_MyApprovedCustomersOnStop.dispose(),this._ctlToday_Bom&&this._ctlToday_Bom.dispose(),this._ctlToday_BomManager&&this._ctlToday_BomManager.dispose(),this._ctlToday_TodayOpenPurchaseOrders&&this._ctlToday_TodayOpenPurchaseOrders.dispose(),this._ctlToday_UnProcessSalesOrders&&this._ctlToday_UnProcessSalesOrders.dispose(),this._ctlToday_UncheckedIPO&&this._ctlToday_UncheckedIPO.dispose(),this._ctlToday_OpenSupplierPOApproval&&this._ctlToday_OpenSupplierPOApproval.dispose(),this._ctlTopLinks=null,this._ctlPageTitle=null,this._chkMyself=null,this._chkIncludeCredit=null,this._pnlChooseUser=null,this._ddlEmployee=null,this._pnlToday=null,this._pnlAccounts=null,this._pnlBroker=null,this._pnlPurchasing=null,this._pnlSales=null,this._pnlWarehouse=null,this._ctlToday_TopSalespersons=null,this._ctlToday_MyRecentActivity=null,this._ctlToday_PartsOrdered=null,this._ctlToday_MyToDoList=null,this._ctlToday_MyMessages=null,this._ctlAccounts_AllStatistics=null,this._ctlAccounts_MyToDoList=null,this._ctlAccounts_ApprovedCustomersOnStop=null,this._ctlAccounts_TodaysShippedOrders=null,this._ctlAccounts_ReceivedOrders=null,this._ctlBroker_MyStatistics=null,this._ctlBroker_MyApprovedCustomersOnStop=null,this._ctlBroker_ReceivedOrders=null,this._ctlBroker_MyRecentlyShippedOrders=null,this._ctlBroker_MyOpenSalesOrders=null,this._ctlBroker_MyOpenPurchaseOrders=null,this._ctlBroker_MyOpenQuotes=null,this._ctlBroker_MyToDoList=null,this.ctlToday_PowerBIActivity=null,this._ctlBroker_MyOpenRequirements=null,this._ctlPurchasing_ReceivedOrders=null,this._ctlPurchasing_MyToDoList=null,this._ctlPurchasing_MyOpenPurchaseOrders=null,this._ctlSales_MyStatistics=null,this._ctlSales_MyToDoList=null,this._ctlSales_MyApprovedCustomersOnStop=null,this._ctlSales_MyRecentlyShippedOrders=null,this._ctlSales_MyOpenSalesOrders=null,this._ctlSales_MyOpenQuotes=null,this._ctlSales_MyOpenRequirements=null,this._ctlWarehouse_SalesOrdersReadyToShip=null,this._ctlWarehouse_TodaysShippedOrders=null,this._ctlWarehouse_MyToDoList=null,this._ctlWarehouse_AllPurchaseOrdersDueIn=null,this._ctlWarehouse_MyApprovedCustomersOnStop=null,this._ctlToday_Bom=null,this._ctlToday_BomManager=null,this._ctlToday_TodayOpenPurchaseOrders=null,this._ctlToday_UnProcessSalesOrders=null,this._ctlBroker_CustomerOrderValue&&this._ctlBroker_CustomerOrderValue.dispose(),this._ctlBroker_CustomerOrderValue=null,this._ctlSales_CustomerOrderValue&&this._ctlSales_CustomerOrderValue.dispose(),this._ctlSales_CustomerOrderValue=null,this._ctlPurchasing_PODeliveryStatus&&this._ctlPurchasing_PODeliveryStatus.dispose(),this._ctlPurchasing_PODeliveryStatus=null,this._ctlToday_UncheckedIPO=null,this._ctlToday_OpenSupplierPOApproval=null,this._ddlClientByMaster=null,this._ctlToday_MyGIQueries&&this._ctlToday_MyGIQueries.dispose(),this._ctlToday_MyGIQueries=null,this._ctlToday_MyQualityGIQueries&&this._ctlToday_MyQualityGIQueries.dispose(),this._ctlToday_MyQualityGIQueries=null,this._ctlToday_PowerBIActivity&&this._ctlToday_PowerBIActivity.dispose(),this._ctlToday_PowerBIActivity=null,this.get_ctlToday_PowerBiSalesDashboard&&this._ctlToday_PowerBiSalesDashboard.dispose(),this._ctlToday_PowerBiSalesDashboard=null,Rebound.GlobalTrader.Site.Pages.Default.callBaseMethod(this,"dispose"))},changeTab:function(n){this._intCurrentTab=n;this._ctlPageTitle.selectTab(n);this._ctlTopLinks&&this._ctlTopLinks.show(n!=$R_ENUM$HomeTabList.Accounts);$R_FN.showElement(this._pnlToday,n==$R_ENUM$HomeTabList.Today);$R_FN.showElement(this._pnlAccounts,n==$R_ENUM$HomeTabList.Accounts);$R_FN.showElement(this._pnlBroker,n==$R_ENUM$HomeTabList.Broker);$R_FN.showElement(this._pnlPurchasing,n==$R_ENUM$HomeTabList.Purchasing);$R_FN.showElement(this._pnlSales,n==$R_ENUM$HomeTabList.Sales);$R_FN.showElement(this._pnlWarehouse,n==$R_ENUM$HomeTabList.Warehouse);$R_FN.showElement(this._PnlBI,n==$R_ENUM$HomeTabList.PowerBIDash);switch(this._intCurrentTab){case $R_ENUM$HomeTabList.Today:this._ctlToday_MyRecentActivity&&this._ctlToday_MyRecentActivity.getData();this._ctlToday_TopSalespersons&&this._ctlToday_TopSalespersons.getData();this._ctlToday_PartsOrdered&&this._ctlToday_PartsOrdered.getData();this._ctlToday_MyToDoList&&this._ctlToday_MyToDoList.getData();this._ctlToday_MyMessages&&this._ctlToday_MyMessages.getData();this._ctlToday_Bom&&this._ctlToday_Bom.getData();this._ctlToday_BomManager&&this._ctlToday_BomManager.getData();this._ctlToday_TodayOpenPurchaseOrders&&this._ctlToday_TodayOpenPurchaseOrders.getData();this._ctlToday_UnProcessSalesOrders&&this._ctlToday_UnProcessSalesOrders.getData();this._ctlToday_UncheckedIPO&&this._ctlToday_UncheckedIPO.getData();this._ctlToday_OpenSupplierPOApproval&&this._ctlToday_OpenSupplierPOApproval.getData();this._ctlToday_MyGIQueries&&this._ctlToday_MyGIQueries.getData();this._ctlToday_PowerBIActivity&&this._ctlToday_PowerBIActivity.getData();this._ctlToday_MyQualityGIQueries&&this._ctlToday_MyQualityGIQueries.getData();break;case $R_ENUM$HomeTabList.Accounts:this._ctlAccounts_AllStatistics&&this._ctlAccounts_AllStatistics.getData();this._ctlAccounts_MyToDoList&&this._ctlAccounts_MyToDoList.getData();this._ctlAccounts_ApprovedCustomersOnStop&&this._ctlAccounts_ApprovedCustomersOnStop.getData();this._ctlAccounts_TodaysShippedOrders&&this._ctlAccounts_TodaysShippedOrders.getData();this._ctlAccounts_ReceivedOrders&&this._ctlAccounts_ReceivedOrders.getData();break;case $R_ENUM$HomeTabList.Broker:this._ctlBroker_MyStatistics&&this._ctlBroker_MyStatistics.getData();this._ctlBroker_MyToDoList&&this._ctlBroker_MyToDoList.getData();this._ctlBroker_MyApprovedCustomersOnStop&&this._ctlBroker_MyApprovedCustomersOnStop.getData();this._ctlBroker_ReceivedOrders&&this._ctlBroker_ReceivedOrders.getData();this._ctlBroker_MyRecentlyShippedOrders&&this._ctlBroker_MyRecentlyShippedOrders.getData();this._ctlBroker_MyOpenSalesOrders&&this._ctlBroker_MyOpenSalesOrders.getData();this._ctlBroker_MyOpenPurchaseOrders&&this._ctlBroker_MyOpenPurchaseOrders.getData();this._ctlBroker_MyOpenQuotes&&this._ctlBroker_MyOpenQuotes.getData();this._ctlBroker_MyOpenRequirements&&this._ctlBroker_MyOpenRequirements.getData();this._ctlBroker_CustomerOrderValue&&this._ctlBroker_CustomerOrderValue.getData();break;case $R_ENUM$HomeTabList.Purchasing:this._ctlPurchasing_ReceivedOrders&&this._ctlPurchasing_ReceivedOrders.getData();this._ctlPurchasing_MyToDoList&&this._ctlPurchasing_MyToDoList.getData();this._ctlPurchasing_MyOpenPurchaseOrders&&this._ctlPurchasing_MyOpenPurchaseOrders.getData();this._ctlPurchasing_PODeliveryStatus&&this._ctlPurchasing_PODeliveryStatus.getData();break;case $R_ENUM$HomeTabList.Sales:this._ctlSales_MyStatistics&&this._ctlSales_MyStatistics.getData();this._ctlSales_MyToDoList&&this._ctlSales_MyToDoList.getData();this._ctlSales_MyApprovedCustomersOnStop&&this._ctlSales_MyApprovedCustomersOnStop.getData();this._ctlSales_MyRecentlyShippedOrders&&this._ctlSales_MyRecentlyShippedOrders.getData();this._ctlSales_MyOpenSalesOrders&&this._ctlSales_MyOpenSalesOrders.getData();this._ctlSales_MyOpenQuotes&&this._ctlSales_MyOpenQuotes.getData();this._ctlSales_MyOpenRequirements&&this._ctlSales_MyOpenRequirements.getData();this._ctlSales_CustomerOrderValue&&this._ctlSales_CustomerOrderValue.getData();break;case $R_ENUM$HomeTabList.Warehouse:this._ctlWarehouse_SalesOrdersReadyToShip&&this._ctlWarehouse_SalesOrdersReadyToShip.getData();this._ctlWarehouse_TodaysShippedOrders&&this._ctlWarehouse_TodaysShippedOrders.getData();this._ctlWarehouse_MyToDoList&&this._ctlWarehouse_MyToDoList.getData();this._ctlWarehouse_AllPurchaseOrdersDueIn&&this._ctlWarehouse_AllPurchaseOrdersDueIn.getData();this._ctlWarehouse_MyApprovedCustomersOnStop&&this._ctlWarehouse_MyApprovedCustomersOnStop.getData();break;case $R_ENUM$HomeTabList.PowerBIDash:getToken().call(this)}},selectMyselfOrOther:function(){if(this._ctlTopLinks)if($R_FN.showElement(this._pnlChooseUser,!this._chkMyself._blnChecked),this._chkMyself._blnChecked){if(this._intDifferentUserID>0){this._ctlToday_MyRecentActivity&&this._ctlToday_MyRecentActivity.revertUserToCurrentLogin();this._ctlToday_PowerBIActivity&&this._ctlToday_PowerBIActivity.revertUserToCurrentLogin();this._ctlBroker_MyStatistics&&this._ctlBroker_MyStatistics.revertUserToCurrentLogin();this._ctlBroker_MyApprovedCustomersOnStop&&this._ctlBroker_MyApprovedCustomersOnStop.revertUserToCurrentLogin();this._ctlBroker_MyOpenSalesOrders&&this._ctlBroker_MyOpenSalesOrders.revertUserToCurrentLogin();this._ctlBroker_MyOpenPurchaseOrders&&this._ctlBroker_MyOpenPurchaseOrders.revertUserToCurrentLogin();this._ctlBroker_MyRecentlyShippedOrders&&this._ctlBroker_MyRecentlyShippedOrders.revertUserToCurrentLogin();this._ctlBroker_MyOpenQuotes&&this._ctlBroker_MyOpenQuotes.revertUserToCurrentLogin();this._ctlBroker_MyOpenRequirements&&this._ctlBroker_MyOpenRequirements.revertUserToCurrentLogin();this._ctlSales_MyStatistics&&this._ctlSales_MyStatistics.revertUserToCurrentLogin();this._ctlSales_MyApprovedCustomersOnStop&&this._ctlSales_MyApprovedCustomersOnStop.revertUserToCurrentLogin();this._ctlSales_MyOpenSalesOrders&&this._ctlSales_MyOpenSalesOrders.revertUserToCurrentLogin();this._ctlSales_MyRecentlyShippedOrders&&this._ctlSales_MyRecentlyShippedOrders.revertUserToCurrentLogin();this._ctlSales_MyOpenQuotes&&this._ctlSales_MyOpenQuotes.revertUserToCurrentLogin();this._ctlSales_MyOpenRequirements&&this._ctlSales_MyOpenRequirements.revertUserToCurrentLogin();this._ctlWarehouse_MyApprovedCustomersOnStop&&this._ctlWarehouse_MyApprovedCustomersOnStop.revertUserToCurrentLogin();this._ctlPurchasing_MyOpenPurchaseOrders&&this._ctlPurchasing_MyOpenPurchaseOrders.revertUserToCurrentLogin();this._ctlBroker_CustomerOrderValue&&this._ctlBroker_CustomerOrderValue.revertUserToCurrentLogin();this._ctlSales_CustomerOrderValue&&this._ctlSales_CustomerOrderValue.revertUserToCurrentLogin();switch(this._intCurrentTab){case $R_ENUM$HomeTabList.Today:this._ctlToday_MyRecentActivity&&this._ctlToday_MyRecentActivity.getData();this._ctlToday_PowerBIActivity&&this._ctlToday_PowerBIActivity.getData();break;case $R_ENUM$HomeTabList.Broker:this._ctlBroker_MyStatistics&&this._ctlBroker_MyStatistics.getData();this._ctlBroker_MyApprovedCustomersOnStop&&this._ctlBroker_MyApprovedCustomersOnStop.getData();this._ctlBroker_MyOpenSalesOrders&&this._ctlBroker_MyOpenSalesOrders.getData();this._ctlBroker_MyRecentlyShippedOrders&&this._ctlBroker_MyRecentlyShippedOrders.getData();this._ctlBroker_MyOpenPurchaseOrders&&this._ctlBroker_MyOpenPurchaseOrders.getData();this._ctlBroker_MyOpenQuotes&&this._ctlBroker_MyOpenQuotes.getData();this._ctlBroker_MyOpenRequirements&&this._ctlBroker_MyOpenRequirements.getData();this._ctlBroker_CustomerOrderValue&&this._ctlBroker_CustomerOrderValue.getData();break;case $R_ENUM$HomeTabList.Sales:this._ctlSales_MyStatistics&&this._ctlSales_MyStatistics.getData();this._ctlSales_MyApprovedCustomersOnStop&&this._ctlSales_MyApprovedCustomersOnStop.getData();this._ctlSales_MyOpenSalesOrders&&this._ctlSales_MyOpenSalesOrders.getData();this._ctlSales_MyRecentlyShippedOrders&&this._ctlSales_MyRecentlyShippedOrders.getData();this._ctlSales_MyOpenRequirements&&this._ctlSales_MyOpenRequirements.getData();this._ctlSales_MyOpenQuotes&&this._ctlSales_MyOpenQuotes.getData();this._ctlSales_CustomerOrderValue&&this._ctlSales_CustomerOrderValue.getData();break;case $R_ENUM$HomeTabList.Warehouse:this._ctlWarehouse_MyApprovedCustomersOnStop&&this._ctlWarehouse_MyApprovedCustomersOnStop.getData();break;case $R_ENUM$HomeTabList.Purchasing:this._ctlPurchasing_MyOpenPurchaseOrders&&this._ctlPurchasing_MyOpenPurchaseOrders.getData()}}this._intDifferentUserID=0}else this._ddlEmployee.isSetAsNoValue()||this.selectLogin()},LoginWithOtherClient:function(){location.href=String.format("{0}?ret={1}&CID={2}","LoginWithOtherClient.aspx",escape(location.href),this._ddlClientByMaster.getValue())},selectLogin:function(){if(!this._ddlEmployee.isSetAsNoValue()&&!this._ddlEmployee._blnInDataCall){var t=this._ddlEmployee.getText(),n=this._ddlEmployee.getValue(),i=this._chkIncludeCredit._blnChecked;this._intDifferentUserID=n;this._ctlToday_MyRecentActivity&&this._ctlToday_MyRecentActivity.changeUser(n,t);this._ctlToday_PowerBIActivity&&this._ctlToday_PowerBIActivity.changeUser(n,t);this._ctlBroker_MyStatistics&&this._ctlBroker_MyStatistics.changeUser(n,t,i);this._ctlBroker_MyApprovedCustomersOnStop&&this._ctlBroker_MyApprovedCustomersOnStop.changeUser(n,t);this._ctlBroker_MyOpenSalesOrders&&this._ctlBroker_MyOpenSalesOrders.changeUser(n,t);this._ctlBroker_MyOpenPurchaseOrders&&this._ctlBroker_MyOpenPurchaseOrders.changeUser(n,t);this._ctlBroker_MyRecentlyShippedOrders&&this._ctlBroker_MyRecentlyShippedOrders.changeUser(n,t);this._ctlBroker_MyOpenQuotes&&this._ctlBroker_MyOpenQuotes.changeUser(n,t);this._ctlBroker_MyOpenRequirements&&this._ctlBroker_MyOpenRequirements.changeUser(n,t);this._ctlSales_MyStatistics&&this._ctlSales_MyStatistics.changeUser(n,t);this._ctlSales_MyApprovedCustomersOnStop&&this._ctlSales_MyApprovedCustomersOnStop.changeUser(n,t);this._ctlSales_MyOpenSalesOrders&&this._ctlSales_MyOpenSalesOrders.changeUser(n,t);this._ctlSales_MyRecentlyShippedOrders&&this._ctlSales_MyRecentlyShippedOrders.changeUser(n,t);this._ctlSales_MyOpenQuotes&&this._ctlSales_MyOpenQuotes.changeUser(n,t);this._ctlSales_MyOpenRequirements&&this._ctlSales_MyOpenRequirements.changeUser(n,t);this._ctlWarehouse_MyApprovedCustomersOnStop&&this._ctlWarehouse_MyApprovedCustomersOnStop.changeUser(n,t);this._ctlPurchasing_MyOpenPurchaseOrders&&this._ctlPurchasing_MyOpenPurchaseOrders.changeUser(n,t);this._ctlBroker_CustomerOrderValue&&this._ctlBroker_CustomerOrderValue.changeUser(n,t);this._ctlSales_CustomerOrderValue&&this._ctlSales_CustomerOrderValue.changeUser(n,t);switch(this._intCurrentTab){case $R_ENUM$HomeTabList.Today:this._ctlToday_MyRecentActivity&&this._ctlToday_MyRecentActivity.getData();this._ctlToday_PowerBIActivity&&this._ctlToday_PowerBIActivity.getData();break;case $R_ENUM$HomeTabList.Broker:this._ctlBroker_MyStatistics&&this._ctlBroker_MyStatistics.getData();this._ctlBroker_MyApprovedCustomersOnStop&&this._ctlBroker_MyApprovedCustomersOnStop.getData();this._ctlBroker_MyOpenSalesOrders&&this._ctlBroker_MyOpenSalesOrders.getData();this._ctlBroker_MyRecentlyShippedOrders&&this._ctlBroker_MyRecentlyShippedOrders.getData();this._ctlBroker_MyOpenPurchaseOrders&&this._ctlBroker_MyOpenPurchaseOrders.getData();this._ctlBroker_MyOpenQuotes&&this._ctlBroker_MyOpenQuotes.getData();this._ctlBroker_MyOpenRequirements&&this._ctlBroker_MyOpenRequirements.getData();this._ctlBroker_CustomerOrderValue&&this._ctlBroker_CustomerOrderValue.getData();break;case $R_ENUM$HomeTabList.Sales:this._ctlSales_MyStatistics&&this._ctlSales_MyStatistics.getData();this._ctlSales_MyApprovedCustomersOnStop&&this._ctlSales_MyApprovedCustomersOnStop.getData();this._ctlSales_MyOpenSalesOrders&&this._ctlSales_MyOpenSalesOrders.getData();this._ctlSales_MyRecentlyShippedOrders&&this._ctlSales_MyRecentlyShippedOrders.getData();this._ctlSales_MyOpenRequirements&&this._ctlSales_MyOpenRequirements.getData();this._ctlSales_MyOpenQuotes&&this._ctlSales_MyOpenQuotes.getData();this._ctlSales_CustomerOrderValue&&this._ctlSales_CustomerOrderValue.getData();break;case $R_ENUM$HomeTabList.Warehouse:this._ctlWarehouse_MyApprovedCustomersOnStop&&this._ctlWarehouse_MyApprovedCustomersOnStop.getData();this._ctlPurchasing_MyOpenPurchaseOrders&&this._ctlPurchasing_MyOpenPurchaseOrders.getData()}}},selectIncludeCredit:function(){var t;if(this._chkMyself._blnChecked)if(t=this._chkIncludeCredit._blnChecked,this._intDifferentUserID>0){this._ctlSales_MyStatistics&&this._ctlSales_MyStatistics.revertUserToCurrentLogin();switch(this._intCurrentTab){case $R_ENUM$HomeTabList.Broker:this._ctlBroker_MyStatistics&&this._ctlBroker_MyStatistics.getData()}this._intDifferentUserID=0}else switch(this._intCurrentTab){case $R_ENUM$HomeTabList.Broker:this._ctlBroker_MyStatistics&&this._ctlBroker_MyStatistics.getData()}else{if(this._ddlEmployee.isSetAsNoValue())return;if(this._ddlEmployee._blnInDataCall)return;var i=this._ddlEmployee.getText(),n=this._ddlEmployee.getValue(),t=this._chkIncludeCredit._blnChecked;this._intDifferentUserID=n;this._ctlBroker_MyStatistics&&this._ctlBroker_MyStatistics.changeUser(n,i,t);switch(this._intCurrentTab){case $R_ENUM$HomeTabList.Broker:this._ctlBroker_MyStatistics&&this._ctlBroker_MyStatistics.getData()}}}};Rebound.GlobalTrader.Site.Pages.Default.registerClass("Rebound.GlobalTrader.Site.Pages.Default",Rebound.GlobalTrader.Site.Pages.Content,Sys.IDisposable);