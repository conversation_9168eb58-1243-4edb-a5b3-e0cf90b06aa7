/*
Marker      Date        Changed By       Remarks
[001]       26/06/2018  <PERSON><PERSON><PERSON>      Save expedite notes for CRMA.
[002]      <PERSON><PERSON>   21-Jan-2019  Add View Tree Button.
 */
using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.BLL;
using Rebound.GlobalTrader.Site.Controls;
using Rebound.GlobalTrader.Site;

namespace Rebound.GlobalTrader.Site.Controls.Nuggets {
	public partial class CRMAMainInfo : Base
    {
        #region Controls

        protected IconButton _ibtnEdit;
        //[001] start
        protected IconButton _ibtnAdd;

        protected FlexiDataTable _tblExpHist;
        protected Panel _pnlLoadingExpHist;
        protected Panel _pnlExpHistError;
        protected Panel _pnlExpHist;
        //[001] end
        protected IconButton _ibtnViewTree;//[002]
		#endregion

		#region Properties

		private int _intCRMAID = -1;
		public int CRMAID {
			get { return _intCRMAID; }
			set { _intCRMAID = value; }
		}

		private bool _blnCanEdit = true;
		public bool CanEdit {
			get { return _blnCanEdit; }
			set { _blnCanEdit = value; }
		}

		#endregion

		#region Overrides

		/// <summary>
		/// Oninit
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
			base.OnInit(e);
			WireUpControls();
			AddScriptReference("Controls.Nuggets.CRMAMainInfo.CRMAMainInfo.js");
			TitleText = Functions.GetGlobalResource("Nuggets", "CRMAMainInfo");
			if (_objQSManager.CRMAID > 0) _intCRMAID = _objQSManager.CRMAID;
            //[001] start
            SetupTable();
		}

		protected override void OnPreRender(EventArgs e) {
			_ibtnEdit.Visible = _blnCanEdit;
			SetupScriptDescriptors();
			base.OnPreRender(e);
		}

		#endregion

		/// <summary>
		/// Sets up AJAX script descriptors
		/// </summary>
		private void SetupScriptDescriptors() {
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.Nuggets.CRMAMainInfo", ctlDesignBase.ClientID);
			if (_blnCanEdit) _scScriptControlDescriptor.AddElementProperty("ibtnEdit", _ibtnEdit.ClientID);
			_scScriptControlDescriptor.AddProperty("intCRMAID", _intCRMAID);
            //[001] start
            _scScriptControlDescriptor.AddElementProperty("ibtnAdd", _ibtnAdd.ClientID);
            _scScriptControlDescriptor.AddElementProperty("pnlLoadingExpHist", _pnlLoadingExpHist.ClientID);
            _scScriptControlDescriptor.AddElementProperty("pnlExpHistError", _pnlExpHistError.ClientID);
            _scScriptControlDescriptor.AddElementProperty("pnlExpHist", _pnlExpHist.ClientID);
            _scScriptControlDescriptor.AddComponentProperty("tblExpHist", _tblExpHist.ClientID);
            //[001] end
            _scScriptControlDescriptor.AddElementProperty("ibtnViewTree", _ibtnViewTree.ClientID);//[002]
		}
        private void SetupTable()
        {
            //[001] code start
            _tblExpHist.AllowSelection = false;
            _tblExpHist.Columns.Add(new FlexiDataColumn("Notes", Unit.Pixel(230)));
            _tblExpHist.Columns.Add(new FlexiDataColumn("Date",WidthManager.GetWidth(WidthManager.ColumnWidth.DateAndTime)));
            _tblExpHist.Columns.Add(new FlexiDataColumn("By"));

        }

		/// <summary>
		/// Wire up controls from the ascx
		/// </summary>
		private void WireUpControls() {
			_ibtnEdit = FindIconButton("ibtnEdit");
            //[001] start
            _ibtnAdd = FindIconButton("ibtnAdd");
            _tblExpHist = (FlexiDataTable)ctlDesignBase.FindContentControl("tblExpHist");
            _pnlLoadingExpHist = (Panel)ctlDesignBase.FindContentControl("pnlLoadingExpHist");
            _pnlExpHistError = (Panel)ctlDesignBase.FindContentControl("pnlExpHistError");
            _pnlExpHist = (Panel)ctlDesignBase.FindContentControl("pnlExpHist");
            //[001] end
            _ibtnViewTree = FindIconButton("ibtnViewTree");//[002]
		}

	}
}
