using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site;
using Rebound.GlobalTrader.Site.Controls;

namespace Rebound.GlobalTrader.Site.Controls.Forms
{
    public partial class ExportApprovalStatus_Approvals : Base
    {

        #region Locals

        private string _strQualityApprove;
        private string _strTitle_QualityDecline;
        private string _strTitle_ExportApprove;
        private string _strTitle_LineManagerDecline;
        private string _strTitle_LineManagerIndepndtTest;
        private string _strTitle_QualityEscalate;
        private Label _lblExplainQualityApprove;
        private Label _lblExplainQualityDecline;
        private Label _lblExplainExportApprove;
        private Label _lblExplainLineManagerDecline;
        private Label _lblExplainLineManagerIndpdt;
        private Label _lblExplainQualityEscalate;

        #endregion

        #region Properties

        private int _intExportApprovalID = -1;
        public int ExportApprovalID
        {
            get { return _intExportApprovalID; }
            set { _intExportApprovalID = value; }
        }

        private int _intLineID;
        public int LineID
        {
            get { return _intLineID; }
            set { _intLineID = value; }
        }

        #endregion

        #region Overrides

        /// <summary>
        /// OnInit
        /// </summary>
        /// <param name="e"></param>
        protected override void OnInit(EventArgs e)
        {
            base.OnInit(e);
            _strTitle_ExportApprove = Functions.GetGlobalResource("FormTitles", "OGELApprovetitle");
            AddScriptReference("Controls.Nuggets.ExportApprovalStatus.Approvals.ExportApprovalStatus_Approvals.js");
            if (_objQSManager.SalesOrderID > 0) _intExportApprovalID = _objQSManager.SalesOrderID;
            WireUpControls();
        }

        /// <summary>
        /// OnPreRender
        /// </summary>
        /// <param name="e"></param>
        protected override void OnPreRender(EventArgs e)
        {
            SetupScriptDescriptors();
            base.OnPreRender(e);
        }

        #endregion

        /// <summary>
        /// Wire up controls to the ascx
        /// </summary>
        private void WireUpControls()
        {
            _lblExplainExportApprove = (Label)ctlDesignBase.FindExplanationControl("lblExplainExportApprove");
        }

        /// <summary>
        /// Setup Script Descriptors
        /// </summary>
        private void SetupScriptDescriptors()
        {
            _scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.Forms.ExportApprovalStatus_Approvals", ctlDesignBase.ClientID);
            _scScriptControlDescriptor.AddProperty("intExportApprovalID", _intExportApprovalID);
            _scScriptControlDescriptor.AddProperty("intLineID", _intLineID);
            _scScriptControlDescriptor.AddProperty("strTitle_ExportApprove", _strTitle_ExportApprove);
            
           
            _scScriptControlDescriptor.AddElementProperty("lblExplainExportApprove", _lblExplainExportApprove.ClientID);
            
        }

    }
}