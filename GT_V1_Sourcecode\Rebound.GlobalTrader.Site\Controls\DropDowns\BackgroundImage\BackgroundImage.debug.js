///<reference name="MicrosoftAjax.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//
// RP 06.10.2009:
// - Changes due to changes in base class
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");

Rebound.GlobalTrader.Site.Controls.DropDowns.BackgroundImage = function(element) { 
	Rebound.GlobalTrader.Site.Controls.DropDowns.BackgroundImage.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.DropDowns.BackgroundImage.prototype = {

	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.DropDowns.BackgroundImage.callBaseMethod(this, "initialize");
		this.addSetupDataCall(Function.createDelegate(this, this.setupDataCall));
		this.addGetDataOK(Function.createDelegate(this, this.dataCallOK));
	},
	
	dispose: function() { 
		if (this.isDisposed) return;
		Rebound.GlobalTrader.Site.Controls.DropDowns.BackgroundImage.callBaseMethod(this, "dispose");
	},
	
	setupDataCall: function() { 
		this._objData.set_PathToData("controls/DropDowns/BackgroundImage");
		this._objData.set_DataObject("BackgroundImage");
		this._objData.set_DataAction("GetData");
	},
	
	dataCallOK: function() { 
		var result = this._objData._result;
		if (result.Types) {
			for (var i = 0; i < result.Types.length; i++) {
				this.addOption(result.Types[i].Name, result.Types[i].ID);
			}
		}
	}
	
};

Rebound.GlobalTrader.Site.Controls.DropDowns.BackgroundImage.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.BackgroundImage", Rebound.GlobalTrader.Site.Controls.DropDowns.Base);
