﻿/* Marker     changed by      date         Remarks  
   [001]      <PERSON><PERSON><PERSON> kumar     22/11/2011  ESMS Ref:21 - Add Country search option in PO 
   [002]      Vinay           18/09/2012    Ref:## - Display Purchase Country
   [003]      A<PERSON><PERSON><PERSON>   05/02/2013    Ref:## - Add Promise Date in PO Line
   [004]      <PERSON><PERSON><PERSON>         27/02/2014    Ref:## - Supplier RMA-ISCRMA
   [005]      Aashu           11/June/2018  Added code to print SO and so promise date 
   [006]      <PERSON><PERSON><PERSON>     25/06/2018    show supplier warranty for po line detail
   [007]      <PERSON><PERSON><PERSON>     06-Aug-2018   REB-12084:Lock PO lines when EPR is authorised
   [008]      <PERSON><PERSON><PERSON>     27-Sep-2018   REB-13083 Change request PO - delivery status
   [009]      <PERSON>     17/08/2021   (GTDP-198) Controlling specific product groups for purchasing
   [009]      A<PERSON><PERSON>av <PERSON>  28-Jul-2021   Add repeat order field.
   [010]      <PERSON>     11/01/2022   (GTDP-240)  Add function to add warning message to show on all screens REQ - Quote - SO - PO related to ECCN field
   [011]      A<PERSON><PERSON><PERSON>a  21-Jan-2021   Add new Column SupplierApprovalStatus.
   [012]      <PERSON>    13-Sep-2023   RP-2340 (Insert AS6081 value to the table tbPurchaseOrderLine)
   [013]      Ravi Bhushan    18-09-2023      RP-2339  AS6081 (Show AS6081 on detail screens)
   [014]      Ravi Bhushan    19-09-2023      RP-2338  AS6081 Search/Filter functionality on different pages
    [RP-881]   Ravi Bhushan    13-11-2023      Include auto quarantine function for stock received from a sanctioned supplier   
 
 */

using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;
using Rebound.GlobalTrader.DAL;
using Rebound.GlobalTrader.BLL;

namespace Rebound.GlobalTrader.BLL
{
    public partial class PurchaseOrderLine : BizObject
    {

        #region Properties

        protected static DAL.PurchaseOrderLineElement Settings
        {
            get { return Globals.Settings.PurchaseOrderLines; }
        }
        public System.String CountryOfOrigin { get; set; }
        public System.String IHSProduct { get; set; }
        public System.String ECCNCode { get; set; }
        public System.Int32? ECCNId { get; set; }
        public System.String ECCNCodeEdit { get; set; }
        public System.String ECCNCodeSOLine { get; set; }
        public System.Int32? ECCNIdSOLine { get; set; }

        public System.Int32? POApprovedBy { get; set; }
        /// <summary>
        /// LifeCycleStage
        /// </summary>
        public System.String LifeCycleStage { get; set; }
        /// <summary>
        /// AveragePrice
        /// </summary>
        public System.Double AveragePrice { get; set; }
        /// <summary>
        /// HTSCode
        /// </summary>
        public System.String HTSCode { get; set; }

        /// <summary>
        /// Packaging
        /// </summary>
        public System.String Packaging { get; set; }
        /// <summary>
        /// PackagingSize
        /// </summary>
        public System.String PackagingSize { get; set; }
        public System.String Descriptions { get; set; }
        /// <summary>
        /// PurchaseOrderLineId
        /// </summary>
        public System.Int32 PurchaseOrderLineId { get; set; }
        /// <summary>
        /// PurchaseOrderNo
        /// </summary>
        public System.Int32 PurchaseOrderNo { get; set; }
        /// <summary>
        /// WarningMessage
        /// </summary>
        public System.String WarningMessage { get; set; }
        /// <summary>
        /// IsCountryOnHighRisk
        /// </summary>
        public System.Boolean? IsCountryOnHighRisk { get; set; }


        /// <summary>
        /// FullPart
        /// </summary>
        public System.String FullPart { get; set; }
        /// <summary>
        /// Part
        /// </summary>
        public System.String Part { get; set; }
        /// <summary>
        /// ManufacturerNo
        /// </summary>
        public System.Int32? ManufacturerNo { get; set; }
        /// <summary>
        /// DateCode
        /// </summary>
        public System.String DateCode { get; set; }
        /// <summary>
        /// PackageNo
        /// </summary>
        public System.Int32? PackageNo { get; set; }
        /// <summary>
        /// Quantity
        /// </summary>
        public System.Int32 Quantity { get; set; }
        /// <summary>
        /// Price
        /// </summary>
        public System.Double Price { get; set; }
        /// <summary>
        /// DeliveryDate
        /// </summary>
        public System.DateTime DeliveryDate { get; set; }
        public System.DateTime? DeliveryDate2 { get; set; }
        /// <summary>
        /// ReceivingNotes
        /// </summary>
        public System.String ReceivingNotes { get; set; }
        /// <summary>
        /// Taxable
        /// </summary>
        public System.Boolean Taxable { get; set; }

        //public System.Boolean Eprflag { get; set; }

        /// <summary>
        /// RepeatOrder
        /// </summary>
        public System.Boolean RepeatOrder { get; set; }
        /// <summary>
        /// ProductNo
        /// </summary>
        public System.Int32? ProductNo { get; set; }
        /// <summary>
        /// Posted
        /// </summary>
        public System.Boolean Posted { get; set; }
        /// <summary>
        /// ShipInCost
        /// </summary>
        public System.Double? ShipInCost { get; set; }
        /// <summary>
        /// SupplierPart
        /// </summary>
        public System.String SupplierPart { get; set; }
        /// <summary>
        /// Inactive
        /// </summary>
        public System.Boolean Inactive { get; set; }
        /// <summary>
        /// Closed
        /// </summary>
        public System.Boolean Closed { get; set; }
        /// <summary>
        /// ROHS
        /// </summary>
        public System.Byte? ROHS { get; set; }
        /// <summary>
        /// UpdatedBy
        /// </summary>
        public System.Int32? UpdatedBy { get; set; }
        /// <summary>
        /// DLUP
        /// </summary>
        public System.DateTime DLUP { get; set; }
        /// <summary>
        /// Notes
        /// </summary>
        public System.String Notes { get; set; }
        /// <summary>
        /// FullSupplierPart
        /// </summary>
        public System.String FullSupplierPart { get; set; }
        /// <summary>
        /// PurchaseOrderId
        /// </summary>
        public System.Int32 PurchaseOrderId { get; set; }
        /// <summary>
        /// PurchaseOrderNumber
        /// </summary>
        public System.Int32 PurchaseOrderNumber { get; set; }
        /// <summary>
        /// CurrencyCode
        /// </summary>
        public System.String CurrencyCode { get; set; }
        /// <summary>
        /// QuantityOrdered
        /// </summary>
        public System.Int32 QuantityOrdered { get; set; }
        /// <summary>
        /// DateOrdered
        /// </summary>
        public System.DateTime DateOrdered { get; set; }
        /// <summary>
        /// CompanyName
        /// </summary>
        public System.String CompanyName { get; set; }
        /// <summary>
        /// CompanyNo
        /// </summary>
        public System.Int32 CompanyNo { get; set; }
        /// <summary>
        /// ContactName
        /// </summary>
        public System.String ContactName { get; set; }
        /// <summary>
        /// ContactNo
        /// </summary>
        public System.Int32 ContactNo { get; set; }
        /// <summary>
        /// ManufacturerCode
        /// </summary>
        public System.String ManufacturerCode { get; set; }
        /// <summary>
        /// RowNum
        /// </summary>
        public System.Int64? RowNum { get; set; }
        /// <summary>
        /// RowCnt
        /// </summary>
        public System.Int32? RowCnt { get; set; }
        /// <summary>
        /// Status
        /// </summary>
        public System.Int32? Status { get; set; }
        /// <summary>
        /// QuantityOutstanding
        /// </summary>
        public System.Int32? QuantityOutstanding { get; set; }
        /// <summary>
        /// EarliestShipDate
        /// </summary>
        public System.DateTime? EarliestShipDate { get; set; }
        /// <summary>
        /// PurchaseOrderValue
        /// </summary>
        public System.Double? PurchaseOrderValue { get; set; }
        /// <summary>
        /// LineNotes
        /// </summary>
        public System.String LineNotes { get; set; }
        /// <summary>
        /// QuantityReceived
        /// </summary>
        public System.Int32 QuantityReceived { get; set; }
        /// <summary>
        /// QuantityAllocated
        /// </summary>
        public System.Int32 QuantityAllocated { get; set; }
        /// <summary>
        /// GIShipInCost
        /// </summary>
        public System.Double GIShipInCost { get; set; }
        /// <summary>
        /// ProductName
        /// </summary>
        public System.String ProductName { get; set; }
        /// <summary>
        /// ProductDescription
        /// </summary>
        public System.String ProductDescription { get; set; }
        /// <summary>
        /// ProductDutyCode
        /// </summary>
        public System.String ProductDutyCode { get; set; }
        /// <summary>
        /// PackageName
        /// </summary>
        public System.String PackageName { get; set; }
        /// <summary>
        /// PackageDescription
        /// </summary>
        public System.String PackageDescription { get; set; }
        /// <summary>
        /// ImportCountryShippingCost
        /// </summary>
        public System.Double? ImportCountryShippingCost { get; set; }
        /// <summary>
        /// CurrencyNo
        /// </summary>
        public System.Int32 CurrencyNo { get; set; }
        /// <summary>
        /// CurrencyDescription
        /// </summary>
        public System.String CurrencyDescription { get; set; }
        /// <summary>
        /// ManufacturerName
        /// </summary>
        public System.String ManufacturerName { get; set; }
        /// <summary>
        /// TaxRate
        /// </summary>
        public System.Double? TaxRate { get; set; }
        /// <summary>
        /// ClientNo
        /// </summary>
        public System.Int32 ClientNo { get; set; }
        /// <summary>
        /// QuantityToReturn
        /// </summary>
        public System.Int32? QuantityToReturn { get; set; }
        /// <summary>
        /// ExpediteDate
        /// </summary>
        public System.DateTime? ExpediteDate { get; set; }
        /// <summary>
        /// Buyer
        /// </summary>
        public System.Int32 Buyer { get; set; }
        /// <summary>
        /// BuyerName
        /// </summary>
        public System.String BuyerName { get; set; }
        /// <summary>
        /// DivisionNo
        /// </summary>
        public System.Int32 DivisionNo { get; set; }
        /// <summary>
        /// TeamNo
        /// </summary>
        public System.Int32? TeamNo { get; set; }
        /// <summary>
        /// FullName
        /// </summary>
        public System.String FullName { get; set; }
        /// <summary>
        /// CreditLimit
        /// </summary>
        public System.Double? CreditLimit { get; set; }
        /// <summary>
        /// Balance
        /// </summary>
        public System.Double? Balance { get; set; }
        /// <summary>
        /// LineValue
        /// </summary>
        public System.Double LineValue { get; set; }
        /// <summary>
        /// TermsNo
        /// </summary>
        public System.Int32 TermsNo { get; set; }
        /// <summary>
        /// TermsName
        /// </summary>
        public System.String TermsName { get; set; }
        /// <summary>
        /// InAdvance
        /// </summary>
        public System.Boolean InAdvance { get; set; }
        /// <summary>
        /// DatePromised
        /// </summary>
        public System.DateTime DatePromised { get; set; }
        /// <summary>
        /// ClientName
        /// </summary>
        public System.String ClientName { get; set; }
        /// <summary>
        /// ClientDataVisibleToOthers
        /// </summary>
        public System.Boolean? ClientDataVisibleToOthers { get; set; }
        //[002] code start
        /// <summary>
        /// ImportCountryNo
        /// </summary>
        public System.Int32? ImportCountryNo { get; set; }
        //[002] code end
        //[003] code end
        /// </summary>
        public System.DateTime PromiseDate { get; set; }
        public System.DateTime? PromiseDate2 { get; set; }
        /// <summary>
        //[003] code end
        /// <summary>
        /// Location
        /// </summary>
        public System.String Location { get; set; }
        /// <summary>
        /// IsNprExist
        /// </summary>
        public System.Boolean? IsNprExist { get; set; }
        /// <summary>
        /// blnSRMA
        /// </summary>
        public System.Boolean? blnSRMA { get; set; }
        /// <summary>
        /// POSerialNo
        /// </summary>
        public System.Int16? POSerialNo { get; set; }

        //[004]Code Start
        /// <summary>
        /// IsCRMA
        /// </summary>
        public System.Boolean? IsCRMA { get; set; }
        /// <summary>
        /// SupportTeamMemberNo
        /// </summary>
        public System.Int32? SupportTeamMemberNo { get; set; }
        /// <summary>
        /// SupportTeamMemberName
        /// </summary>
        public System.String SupportTeamMemberName { get; set; }
        public System.Int32? IPOSupportTeamMemberNo { get; set; }
        /// <summary>
        /// SupportTeamMemberName
        /// </summary>
        public System.String IPOSupportTeamMemberName { get; set; }
        //[004]Code Start

        /// <summary>
        /// InternalPurchaseOrderNo
        /// </summary>
        public System.Int32? InternalPurchaseOrderNo { get; set; }
        public System.Boolean isIncludeAltPart { get; set; }
        /// <summary>
        /// InternalPurchaseOrderNo
        /// </summary>
        public System.Int32 InternalPurchaseOrderNumber { get; set; }
        public int? PurchaseQuoteId { get; set; }
        public int? PurchaseQuoteNumber { get; set; }
        public DateTime? PurchaseRequestDate { get; set; }
        public int? BomNo { get; set; }
        public string BOMName { get; set; }
        /// <summary>
        /// IsIPO
        /// </summary>
        public System.Boolean IsIPO { get; set; }
        /// <summary>
        /// SupplierNo
        /// </summary>
        public System.Int32? SupplierNo { get; set; }

        public System.Double? ClientPrice { get; set; }
        public System.Int32? ClientCurrencyNo { get; set; }
        public System.String ClientCurrencyCode { get; set; }
        public System.Double? ClientShipInCost { get; set; }
        public System.Int32? IPOClientNo { get; set; }
        public System.String CTName { get; set; }
        public System.Double? SOPrice { get; set; }
        public System.String SOCurrencyCode { get; set; }
        public System.Int32? SOCurrencyNo { get; set; }
        public DateTime? SODateOrdered { get; set; }

        public System.String ClientCode { get; set; }

        public System.Int32? HubCurrencyNo { get; set; }
        public System.DateTime? CurrencyDate { get; set; }
        public System.Int32? DefaultClientLotNo { get; set; }
        public System.Int32? IPOBuyer { get; set; }
        public System.String IPOBuyerName { get; set; }
        public System.Int32? MailGroupId { get; set; }
        public System.String PoClientName { get; set; }
        public System.Double? DutyRate { get; set; }
        /// <summary>
        /// Taxable
        /// </summary>
        public System.Boolean? ReqSerialNo { get; set; }
        public System.String MSLevel { get; set; }
        //[005] start
        /// <summary>
        /// SupplierNo
        /// </summary>
        public System.String SONumberDetail { get; set; }
        //[005] end
        //[006] start
        /// <summary>
        /// SupplierWarranty
        /// </summary>
        public System.Int32? SupplierWarranty { get; set; }
        //[006] end
        public System.Boolean? IsProdHazardous { get; set; }
        public System.Boolean? PrintHazardous { get; set; }
        //[007] start
        public System.String EPRIds { get; set; }
        public System.Boolean IsReleased { get; set; }
        public System.Boolean IsAuthorised { get; set; }
        public System.Int32? SupplierApprovalStatus { get; set; }
        //[007] end
        public System.String SupplierApprovalStatusMessage { get; set; }
        //[008] start
        public System.Int32? StatusId { get; set; }
        public System.DateTime? OriginalDeliveryDate { get; set; }
        public System.String DeliveryStatus { get; set; }
        public System.String RowCSS { get; set; }
        //[008] end

        public System.Int32? TotalCount { get; set; }
        public System.Double? IpoLineTotal { get; set; }
        public System.Double? LineProfit { get; set; }
        public System.Double? LineProfitPercentage { get; set; }
        //[009] code start
        public System.Boolean? IsOrderViaIPOonly { get; set; }
        //[009] code end
        public System.String SupplierMessage { get; set; }
        /// <summary>
        /// ReceivePOStatusId
        /// </summary>
        public System.String ReceivePOStatus { get; set; }
        //[010] code start
        public System.String IHSECCNCodeDefination { get; set; }
        //[010] code end
        /// <summary>
        /// IsRestrictedProduct
        /// </summary>
        public System.Boolean? IsRestrictedProduct { get; set; }
        /// <summary>
        /// ECCNNotify
        /// </summary>
        public System.Boolean? ECCNNotify { get; set; }
        /// <summary>
        /// EccnSubject
        /// </summary>
        public System.String EccnSubject { get; set; }
        /// <summary>
        /// EccnMessage
        /// </summary>
        public System.String EccnMessage { get; set; }

        public System.String EccnWarningMessage { get; set; }

        /// <summary>
        /// ReceivePOStatusId
        /// </summary>
        //[011] code start
        //public System.Int32? SupplierApprovalStatus { get; set; }
        //[011]  code end 
        public System.Boolean? AS6081 { get; set; }
        public System.Boolean IsSanctioned { get; set; } //[RP-881]
        public System.Boolean? Eprflag { get; set; }
        public System.Boolean? isPOLogFlag { get; set; } //[010]
        public System.String Remarks { get; set; }
        public System.String ApprovedByName { get; set; }
        public System.String ApprovalStatus { get; set; }
        public System.DateTime? DateApproved { get; set; }
        public System.String VendorName { get; set; }
        public System.String VendorType { get; set; }
        public System.String CustomerName { get; set; }
        public System.String CustomerPO { get; set; }
        public System.String LastPOPlaced { get; set; }
        public System.String QuoteLT { get; set; }
        public System.String MouserLT { get; set; }
        public System.DateTime CustomerDate { get; set; }
        public System.String DigikeyLT { get; set; }
        public System.Int32? SourcingResultNo { get; set; }
        public System.Int32? SalesOrderNo { get; set; }
        public System.Int32? SalesOrderLineNo { get; set; }
        public System.String SalesOrderNumber { get; set; }
        public System.Double? GPValue { get; set; }
        public System.Double? GPPercent { get; set; }
        public System.Double? VendorBuyPrice { get; set; }
        public System.String Traceability { get; set; }
        public System.String LeadTime { get; set; }
        public System.String PartsGoingToTest { get; set; }
        public System.String Capacitor { get; set; }
        public System.Int32? AvailableStock { get; set; }
        public System.Int32? SOLineRank { get; set; }
        public System.Int32? POLineRank { get; set; }
        public System.Boolean POApproved { get; set; }
        public double? ShipInCostInBaseClient { get; set; }

        #endregion

        #region Methods

        /// <summary>
        /// DataListNugget
        /// Calls [usp_datalistnugget_PurchaseOrderLine]
        /// </summary>
        //[001]Code Start
        public static List<PurchaseOrderLine> DataListNugget(System.Int32? clientId, System.Int32? teamId, System.Int32? divisionId, System.Int32? loginId, System.Int32? pageIndex, System.Int32? pageSize, System.Int32? orderBy, System.Int32? sortDir, System.String partSearch, System.String contactSearch, System.String cmSearch, System.Int32? buyerSearch, System.Int32? CountrySearch, System.Boolean? includeClosed, System.Int32? purchaseOrderNoLo, System.Int32? purchaseOrderNoHi, System.DateTime? dateOrderedFrom, System.DateTime? dateOrderedTo, System.DateTime? expediteDateFrom, System.DateTime? expediteDateTo, System.DateTime? deliveryDateFrom, System.DateTime? deliveryDateTo, System.Boolean? recentOnly, System.Int32? internalPurchaseOrderNoLo, System.Int32? internalPurchaseOrderNoHi, System.Int32? clientSearch, int? IsPoHub, System.Boolean? PohubOnly, Boolean IsGlobalLogin, System.Int32? SOCheckedStatus, System.Int32? SOStatus, System.Int32? SupplierApprovalStatus, System.Boolean? AS6081, System.Int32? SelectedLoginId = null)
        {
            List<PurchaseOrderLineDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.PurchaseOrderLine.DataListNugget(clientId, teamId, divisionId, loginId, pageIndex, pageSize, orderBy, sortDir, partSearch, contactSearch, cmSearch, buyerSearch, CountrySearch, includeClosed, purchaseOrderNoLo, purchaseOrderNoHi, dateOrderedFrom, dateOrderedTo, expediteDateFrom, expediteDateTo, deliveryDateFrom, deliveryDateTo, recentOnly, internalPurchaseOrderNoLo, internalPurchaseOrderNoHi, clientSearch, IsPoHub, PohubOnly, IsGlobalLogin, SOCheckedStatus, SOStatus, SupplierApprovalStatus, AS6081, SelectedLoginId); //[014]
            //[001]Code End
            if (lstDetails == null)
            {
                return new List<PurchaseOrderLine>();
            }
            else
            {
                List<PurchaseOrderLine> lst = new List<PurchaseOrderLine>();
                foreach (PurchaseOrderLineDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.PurchaseOrderLine obj = new Rebound.GlobalTrader.BLL.PurchaseOrderLine();
                    obj.PurchaseOrderId = objDetails.PurchaseOrderId;
                    obj.PurchaseOrderLineId = objDetails.PurchaseOrderLineId;
                    obj.PurchaseOrderNumber = objDetails.PurchaseOrderNumber;
                    obj.Part = objDetails.Part;
                    obj.Price = objDetails.Price;
                    obj.CurrencyCode = objDetails.CurrencyCode;
                    obj.QuantityOrdered = objDetails.QuantityOrdered;
                    obj.DateOrdered = objDetails.DateOrdered;
                    obj.DeliveryDate = objDetails.DeliveryDate;
                    obj.CompanyName = objDetails.CompanyName;
                    obj.CompanyNo = objDetails.CompanyNo;
                    obj.ContactName = objDetails.ContactName;
                    obj.ContactNo = objDetails.ContactNo;
                    obj.ROHS = objDetails.ROHS;
                    obj.ManufacturerNo = objDetails.ManufacturerNo;
                    obj.ManufacturerCode = objDetails.ManufacturerCode;
                    obj.RowNum = objDetails.RowNum;
                    obj.RowCnt = objDetails.RowCnt;
                    obj.Status = objDetails.Status;
                    obj.QuantityOutstanding = objDetails.QuantityOutstanding;
                    obj.ClientName = objDetails.ClientName;
                    obj.InternalPurchaseOrderNo = objDetails.InternalPurchaseOrderNo;
                    obj.InternalPurchaseOrderNumber = objDetails.InternalPurchaseOrderNumber;
                    obj.PoClientName = objDetails.PoClientName;
                    //[008] start
                    obj.DeliveryStatus = objDetails.DeliveryStatus;
                    obj.RowCSS = objDetails.RowCSS;
                    obj.SupplierMessage = objDetails.SupplierMessage;
                    //[008] end
                    obj.AS6081 = objDetails.AS6081; //[014]
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }
        /// <summary>
        /// DataListNuggetAllForReceiving
        /// Calls [usp_datalistnugget_PurchaseOrderLine_AllForReceiving]
        /// </summary>
        public static List<PurchaseOrderLine> DataListNuggetAllForReceiving(System.Int32? clientId, System.Int32? orderBy, System.Int32? sortDir, System.Int32? pageIndex, System.Int32? pageSize, System.String partSearch, System.String contactSearch, System.String cmSearch, System.Int32? buyerSearch, System.Boolean? recentOnly, System.Boolean? includeClosed, System.Int32? purchaseOrderNoLo, System.Int32? purchaseOrderNoHi, System.DateTime? dateOrderedFrom, System.DateTime? dateOrderedTo, System.DateTime? expediteDateFrom, System.DateTime? expediteDateTo, System.DateTime? deliveryDateFrom, System.DateTime? deliveryDateTo, System.Int32? internalPurchaseOrderNoLo, System.Int32? internalPurchaseOrderNoHi, bool? isPoHub, System.Int32? clientNo, System.Boolean? globalUser)
        {
            List<PurchaseOrderLineDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.PurchaseOrderLine.DataListNuggetAllForReceiving(clientId, orderBy, sortDir, pageIndex, pageSize, partSearch, contactSearch, cmSearch, buyerSearch, recentOnly, includeClosed, purchaseOrderNoLo, purchaseOrderNoHi, dateOrderedFrom, dateOrderedTo, expediteDateFrom, expediteDateTo, deliveryDateFrom, deliveryDateTo, internalPurchaseOrderNoLo, internalPurchaseOrderNoHi, isPoHub, clientNo, globalUser);
            if (lstDetails == null)
            {
                return new List<PurchaseOrderLine>();
            }
            else
            {
                List<PurchaseOrderLine> lst = new List<PurchaseOrderLine>();
                foreach (PurchaseOrderLineDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.PurchaseOrderLine obj = new Rebound.GlobalTrader.BLL.PurchaseOrderLine();
                    obj.PurchaseOrderNo = objDetails.PurchaseOrderNo;
                    obj.PurchaseOrderNumber = objDetails.PurchaseOrderNumber;
                    obj.Part = objDetails.Part;
                    obj.ROHS = objDetails.ROHS;
                    obj.ManufacturerNo = objDetails.ManufacturerNo;
                    obj.ManufacturerCode = objDetails.ManufacturerCode;
                    obj.CompanyNo = objDetails.CompanyNo;
                    obj.CompanyName = objDetails.CompanyName;
                    obj.ContactNo = objDetails.ContactNo;
                    obj.ContactName = objDetails.ContactName;
                    obj.DeliveryDate = objDetails.DeliveryDate;
                    obj.PurchaseOrderLineId = objDetails.PurchaseOrderLineId;
                    obj.QuantityOrdered = objDetails.QuantityOrdered;
                    obj.QuantityOutstanding = objDetails.QuantityOutstanding;
                    obj.EarliestShipDate = objDetails.EarliestShipDate;
                    obj.InternalPurchaseOrderNo = objDetails.InternalPurchaseOrderNo;
                    obj.InternalPurchaseOrderNumber = objDetails.InternalPurchaseOrderNumber;
                    obj.RowNum = objDetails.RowNum;
                    obj.RowCnt = objDetails.RowCnt;
                    obj.ClientName = objDetails.ClientName;
                    obj.PoClientName = objDetails.PoClientName;
                    obj.ReceivePOStatus = objDetails.ReceivePOStatus;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }
        /// <summary>
        /// DataListNuggetReadyToReceive
        /// Calls [usp_datalistnugget_PurchaseOrderLine_ReadyToReceive]
        /// </summary>
        public static List<PurchaseOrderLine> DataListNuggetReadyToReceive(System.Int32? clientId, System.Int32? orderBy, System.Int32? sortDir, System.Int32? pageIndex, System.Int32? pageSize, System.String partSearch, System.String contactSearch, System.String cmSearch, System.Int32? buyerSearch, System.Int32? purchaseOrderNoLo, System.Int32? purchaseOrderNoHi, System.DateTime? dateOrderedFrom, System.DateTime? dateOrderedTo, System.DateTime? expediteDateFrom, System.DateTime? expediteDateTo, System.DateTime? deliveryDateFrom, System.DateTime? deliveryDateTo, System.Int32? internalPurchaseOrderNoLo, System.Int32? internalPurchaseOrderNoHi, System.Int32? clientNo, System.Boolean? globalUser)
        {
            List<PurchaseOrderLineDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.PurchaseOrderLine.DataListNuggetReadyToReceive(clientId, orderBy, sortDir, pageIndex, pageSize, partSearch, contactSearch, cmSearch, buyerSearch, purchaseOrderNoLo, purchaseOrderNoHi, dateOrderedFrom, dateOrderedTo, expediteDateFrom, expediteDateTo, deliveryDateFrom, deliveryDateTo, internalPurchaseOrderNoLo, internalPurchaseOrderNoHi, clientNo, globalUser);
            if (lstDetails == null)
            {
                return new List<PurchaseOrderLine>();
            }
            else
            {
                List<PurchaseOrderLine> lst = new List<PurchaseOrderLine>();
                foreach (PurchaseOrderLineDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.PurchaseOrderLine obj = new Rebound.GlobalTrader.BLL.PurchaseOrderLine();
                    obj.PurchaseOrderNo = objDetails.PurchaseOrderNo;
                    obj.PurchaseOrderNumber = objDetails.PurchaseOrderNumber;
                    obj.Part = objDetails.Part;
                    obj.ROHS = objDetails.ROHS;
                    obj.ManufacturerNo = objDetails.ManufacturerNo;
                    obj.ManufacturerCode = objDetails.ManufacturerCode;
                    obj.CompanyNo = objDetails.CompanyNo;
                    obj.CompanyName = objDetails.CompanyName;
                    obj.ContactNo = objDetails.ContactNo;
                    obj.ContactName = objDetails.ContactName;
                    obj.DeliveryDate = objDetails.DeliveryDate;
                    obj.PurchaseOrderLineId = objDetails.PurchaseOrderLineId;
                    obj.QuantityOrdered = objDetails.QuantityOrdered;
                    obj.QuantityOutstanding = objDetails.QuantityOutstanding;
                    obj.EarliestShipDate = objDetails.EarliestShipDate;
                    obj.InternalPurchaseOrderNo = objDetails.InternalPurchaseOrderNo;
                    obj.InternalPurchaseOrderNumber = objDetails.InternalPurchaseOrderNumber;
                    obj.RowNum = objDetails.RowNum;
                    obj.RowCnt = objDetails.RowCnt;
                    obj.ClientName = objDetails.ClientName;
                    obj.PoClientName = objDetails.PoClientName;
                    obj.ReceivePOStatus = objDetails.ReceivePOStatus;

                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }
        /// <summary>
        /// Delete
        /// Calls [usp_delete_PurchaseOrderLine]
        /// </summary>
        public static bool Delete(System.Int32? purchaseOrderLineId)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.PurchaseOrderLine.Delete(purchaseOrderLineId);
        }
        /// <summary>
        /// Insert
        /// Calls [usp_insert_PurchaseOrderLine]
        /// </summary>
        public static Int32 Insert(System.Int32? purchaseOrderNo,
                                   System.String part,
                                   System.Int32? manufacturerNo,
                                   System.String dateCode,
                                   System.Int32? packageNo,
                                   System.Int32? quantity,
                                   System.Double? price,
                                   System.DateTime? deliveryDate,
                                   System.String receivingNotes,
                                   System.Boolean? taxable,
                                   System.Int32? productNo,
                                   System.Boolean? posted,
                                   System.Double? shipInCost,
                                   System.String supplierPart,
                                   System.Byte? rohs,
                                   System.String notes,
                                   System.DateTime? PromiseDate,
                                   System.Int32? updatedBy,
                                   System.Boolean? reqSerialNo,
                                   System.String mslLevel,
                                   System.Boolean? printHazardous,
                                   System.Boolean? RepeatOrder,
                                   System.Int32? ECCNId,
                                   System.Boolean? AS6081 = false)
        {
            Int32 objReturn = SiteProvider.PurchaseOrderLine.Insert(purchaseOrderNo,
                                                                    part,
                                                                    manufacturerNo,
                                                                    dateCode,
                                                                    packageNo,
                                                                    quantity,
                                                                    price,
                                                                    deliveryDate,
                                                                    receivingNotes,
                                                                    taxable,
                                                                    productNo,
                                                                    posted,
                                                                    shipInCost,
                                                                    supplierPart,
                                                                    rohs,
                                                                    notes,
                                                                    PromiseDate,
                                                                    updatedBy,
                                                                    reqSerialNo,
                                                                    mslLevel,
                                                                    printHazardous,
                                                                    RepeatOrder,
                                                                    ECCNId,
                                                                    AS6081);
            return objReturn;
        }
        /// <summary>
        /// Insert (without parameters)
        /// Calls [usp_insert_PurchaseOrderLine]
        /// </summary>
        public Int32 Insert()
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.PurchaseOrderLine.Insert(PurchaseOrderNo, Part, ManufacturerNo, DateCode, PackageNo, Quantity, Price, DeliveryDate, ReceivingNotes, Taxable, ProductNo, Posted, ShipInCost, SupplierPart, ROHS, Notes, PromiseDate, UpdatedBy, ReqSerialNo, MSLevel, PrintHazardous, RepeatOrder,0, AS6081 = false);
        }
        /// <summary>
        /// InsertFromSalesOrderLine
        /// Calls [usp_insert_PurchaseOrderLine_from_SalesOrderLine]
        /// </summary>
        public static Int32 InsertFromSalesOrderLine(System.Int32? salesOrderLineId, System.Int32? purchaseOrderNo, System.Int32? updatedBy)
        {
            Int32 objReturn = Rebound.GlobalTrader.DAL.SiteProvider.PurchaseOrderLine.InsertFromSalesOrderLine(salesOrderLineId, purchaseOrderNo, updatedBy);
            return objReturn;
        }
        /// <summary>
        /// ItemSearch
        /// Calls [usp_itemsearch_PurchaseOrderLine]
        /// </summary>
        public static List<PurchaseOrderLine> ItemSearch(System.Int32? clientId, System.Int32? orderBy, System.Int32? sortDir, System.Int32? pageIndex, System.Int32? pageSize, System.String partSearch, System.String cmSearch, System.Boolean? includeClosed, System.Int32? purchaseOrderNoLo, System.Int32? purchaseOrderNoHi, System.DateTime? dateOrderedFrom, System.DateTime? dateOrderedTo, System.DateTime? deliveryDateFrom, System.DateTime? deliveryDateTo)
        {
            List<PurchaseOrderLineDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.PurchaseOrderLine.ItemSearch(clientId, orderBy, sortDir, pageIndex, pageSize, partSearch, cmSearch, includeClosed, purchaseOrderNoLo, purchaseOrderNoHi, dateOrderedFrom, dateOrderedTo, deliveryDateFrom, deliveryDateTo);
            if (lstDetails == null)
            {
                return new List<PurchaseOrderLine>();
            }
            else
            {
                List<PurchaseOrderLine> lst = new List<PurchaseOrderLine>();
                foreach (PurchaseOrderLineDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.PurchaseOrderLine obj = new Rebound.GlobalTrader.BLL.PurchaseOrderLine();
                    obj.PurchaseOrderLineId = objDetails.PurchaseOrderLineId;
                    obj.PurchaseOrderNumber = objDetails.PurchaseOrderNumber;
                    obj.PurchaseOrderValue = objDetails.PurchaseOrderValue;
                    obj.CurrencyCode = objDetails.CurrencyCode;
                    obj.Price = objDetails.Price;
                    obj.CompanyNo = objDetails.CompanyNo;
                    obj.CompanyName = objDetails.CompanyName;
                    obj.DateOrdered = objDetails.DateOrdered;
                    obj.DeliveryDate = objDetails.DeliveryDate;
                    obj.Part = objDetails.Part;
                    obj.ROHS = objDetails.ROHS;
                    obj.RowNum = objDetails.RowNum;
                    obj.RowCnt = objDetails.RowCnt;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }
        /// <summary>
        /// GetWarningMessage
        /// Calls [usp_selectWarningMessage_Country_for_Client]
        /// </summary>
        public static PurchaseOrderLine GetWarningMessage(System.Int32? clientId, System.Int32? countryId)
        {
            Rebound.GlobalTrader.DAL.PurchaseOrderLineDetails objDetails = Rebound.GlobalTrader.DAL.SiteProvider.PurchaseOrderLine.GetWarningMessage(clientId, countryId);
            if (objDetails == null)
            {
                return null;
            }
            else
            {
                PurchaseOrderLine obj = new PurchaseOrderLine();
                obj.WarningMessage = objDetails.WarningMessage;
                objDetails = null;
                return obj;
            }
        }
        /// <summary>
        /// GetWarningMessage
        /// Calls [usp_SanctionedPOWarningMessage_Country_for_Client]
        /// </summary>
        public static PurchaseOrderLine GetPOSantionWarningMessage(System.Int32? clientId, System.Int32? countryId)
        {
            Rebound.GlobalTrader.DAL.PurchaseOrderLineDetails objDetails = Rebound.GlobalTrader.DAL.SiteProvider.PurchaseOrderLine.GetPOSantionWarningMessage(clientId, countryId);
            if (objDetails == null)
            {
                return null;
            }
            else
            {
                PurchaseOrderLine obj = new PurchaseOrderLine();
                obj.WarningMessage = objDetails.WarningMessage;
                obj.IsCountryOnHighRisk = objDetails.IsCountryOnHighRisk;
                objDetails = null;
                return obj;
            }
        }
        public static PurchaseOrderLine Get(System.Int32? purchaseOrderLineId)
        {
            Rebound.GlobalTrader.DAL.PurchaseOrderLineDetails objDetails = Rebound.GlobalTrader.DAL.SiteProvider.PurchaseOrderLine.Get(purchaseOrderLineId);
            if (objDetails == null)
            {
                return null;
            }
            else
            {
                PurchaseOrderLine obj = new PurchaseOrderLine();
                obj.PurchaseOrderLineId = objDetails.PurchaseOrderLineId;
                obj.PurchaseOrderNo = objDetails.PurchaseOrderNo;
                obj.FullPart = objDetails.FullPart;
                obj.Part = objDetails.Part;
                obj.ManufacturerNo = objDetails.ManufacturerNo;
                obj.DateCode = objDetails.DateCode;
                obj.PackageNo = objDetails.PackageNo;
                obj.Quantity = objDetails.Quantity;
                obj.Price = objDetails.Price;
                obj.DeliveryDate = objDetails.DeliveryDate;
                obj.ReceivingNotes = objDetails.ReceivingNotes;
                obj.Taxable = objDetails.Taxable;
                obj.RepeatOrder = objDetails.RepeatOrder;
                obj.ProductNo = objDetails.ProductNo;
                obj.Posted = objDetails.Posted;
                obj.ShipInCost = objDetails.ShipInCost;
                obj.SupplierPart = objDetails.SupplierPart;
                obj.Inactive = objDetails.Inactive;
                obj.Closed = objDetails.Closed;
                obj.ROHS = objDetails.ROHS;
                obj.LineNotes = objDetails.LineNotes;
                obj.UpdatedBy = objDetails.UpdatedBy;
                obj.DLUP = objDetails.DLUP;
                obj.QuantityReceived = objDetails.QuantityReceived;
                obj.QuantityAllocated = objDetails.QuantityAllocated;
                obj.GIShipInCost = objDetails.GIShipInCost;
                obj.ProductName = objDetails.ProductName;
                obj.ProductDescription = objDetails.ProductDescription;
                obj.ProductDutyCode = objDetails.ProductDutyCode;
                obj.PackageName = objDetails.PackageName;
                obj.PackageDescription = objDetails.PackageDescription;
                obj.ImportCountryShippingCost = objDetails.ImportCountryShippingCost;
                obj.PurchaseOrderNumber = objDetails.PurchaseOrderNumber;
                obj.CurrencyCode = objDetails.CurrencyCode;
                obj.CurrencyNo = objDetails.CurrencyNo;
                obj.CurrencyDescription = objDetails.CurrencyDescription;
                obj.ManufacturerName = objDetails.ManufacturerName;
                obj.ManufacturerCode = objDetails.ManufacturerCode;
                obj.CompanyNo = objDetails.CompanyNo;
                obj.CompanyName = objDetails.CompanyName;
                obj.DateOrdered = objDetails.DateOrdered;
                obj.TaxRate = objDetails.TaxRate;
                obj.ClientNo = objDetails.ClientNo;
                //[002] code start
                obj.ImportCountryNo = objDetails.ImportCountryNo;
                //[002] code end
                obj.PromiseDate = objDetails.PromiseDate;

                obj.PurchaseQuoteId = objDetails.PurchaseQuoteId;
                obj.PurchaseQuoteNumber = objDetails.PurchaseQuoteNumber;
                obj.PurchaseRequestDate = objDetails.PurchaseRequestDate;
                obj.BomNo = objDetails.BomNo;
                obj.BOMName = objDetails.BOMName;
                obj.ClientPrice = objDetails.ClientPrice;
                obj.InternalPurchaseOrderNo = objDetails.InternalPurchaseOrderNo;
                obj.ClientCurrencyNo = objDetails.ClientCurrencyNo;
                obj.ClientCurrencyCode = objDetails.ClientCurrencyCode;
                obj.ClientShipInCost = objDetails.ClientShipInCost;
                obj.IPOClientNo = objDetails.IPOClientNo;
                obj.DefaultClientLotNo = objDetails.DefaultClientLotNo;
                obj.ProductInactive = objDetails.ProductInactive;
                obj.DutyRate = objDetails.DutyRate;
                obj.ReqSerialNo = objDetails.ReqSerialNo;
                obj.MSLevel = objDetails.MSLevel;
                //[006] start
                obj.SupplierWarranty = objDetails.SupplierWarranty;
                //[006] end
                //[007] start
                obj.EPRIds = objDetails.EPRIds;
                obj.IsReleased = objDetails.IsReleased;
                obj.IsAuthorised = objDetails.IsAuthorised;
                //[007] end
                //ihs code start
                obj.CountryOfOrigin = objDetails.CountryOfOrigin;
                obj.LifeCycleStage = objDetails.LifeCycleStage;
                obj.HTSCode = objDetails.HTSCode;
                obj.AveragePrice = objDetails.AveragePrice;
                obj.Packaging = objDetails.Packaging;
                obj.PackagingSize = objDetails.PackagingSize;
                obj.Descriptions = objDetails.Descriptions;
                obj.IHSProduct = objDetails.IHSProduct;
                obj.ECCNCode = objDetails.ECCNCode;
                //ihs code end
                //[008] start
                obj.OriginalDeliveryDate = objDetails.OriginalDeliveryDate;
                //[008] end
                obj.SOPrice = objDetails.SOPrice;
                obj.IpoLineTotal = objDetails.IpoLineTotal;
                obj.LineProfit = objDetails.LineProfit;
                obj.LineProfitPercentage = objDetails.LineProfitPercentage;
                //[009] code start
                obj.PrintHazardous = objDetails.PrintHazardous;
                obj.IsProdHazardous = objDetails.IsProdHazardous;
                obj.IsOrderViaIPOonly = objDetails.IsOrderViaIPOonly;
                //[009] code end
                obj.HubCurrencyNo = objDetails.HubCurrencyNo;
                obj.CurrencyDate = objDetails.CurrencyDate;
                //[010] code start
                obj.IHSECCNCodeDefination = objDetails.IHSECCNCodeDefination;
                //[010] code end
                obj.IsRestrictedProduct = objDetails.IsRestrictedProduct;
                obj.ECCNNotify = objDetails.ECCNNotify;
                obj.EccnSubject = objDetails.EccnSubject;
                obj.EccnMessage = objDetails.EccnMessage;
                obj.AS6081 = objDetails.AS6081; //[013]
                obj.IsSanctioned = objDetails.IsSanctioned; //[RP-881]
                obj.isPOLogFlag = objDetails.isPOLogFlag; //[RP-881]
                obj.ECCNId = objDetails.ECCNId;
                obj.ECCNCodeEdit = objDetails.ECCNCodeEdit;

                obj.ECCNCodeSOLine = objDetails.ECCNCodeSOLine;
                obj.ECCNIdSOLine = objDetails.ECCNIdSOLine;
                obj.SalesOrderNo = objDetails.SalesOrderNo;
                obj.SalesOrderNumber = objDetails.SalesOrderNumber;
                obj.SalesOrderLineNo = objDetails.SalesOrderLineNo;
                obj.SOLineRank = objDetails.SOLineRank;
                obj.POLineRank = objDetails.POLineRank;
                obj.ShipInCostInBaseClient = objDetails.ShipInCostInBaseClient;
                objDetails = null;
                return obj;
            }
        }
        public static PurchaseOrderLine Get2(System.Int32? purchaseOrderLineId)
        {
            Rebound.GlobalTrader.DAL.PurchaseOrderLineDetails objDetails = Rebound.GlobalTrader.DAL.SiteProvider.PurchaseOrderLine.Get2(purchaseOrderLineId);
            if (objDetails == null)
            {
                return null;
            }
            else
            {
                PurchaseOrderLine obj = new PurchaseOrderLine();
                obj.PurchaseOrderLineId = objDetails.PurchaseOrderLineId;
                obj.PurchaseOrderNo = objDetails.PurchaseOrderNo;
                obj.FullPart = objDetails.FullPart;
                obj.Part = objDetails.Part;
                obj.ManufacturerNo = objDetails.ManufacturerNo;
                obj.DateCode = objDetails.DateCode;
                obj.PackageNo = objDetails.PackageNo;
                obj.Quantity = objDetails.Quantity;
                obj.Price = objDetails.Price;
                obj.DeliveryDate = objDetails.DeliveryDate;
                obj.DeliveryDate2 = objDetails.DeliveryDate2;
                obj.ReceivingNotes = objDetails.ReceivingNotes;
                obj.Taxable = objDetails.Taxable;
                obj.RepeatOrder = objDetails.RepeatOrder;
                obj.ProductNo = objDetails.ProductNo;
                obj.Posted = objDetails.Posted;
                obj.ShipInCost = objDetails.ShipInCost;
                obj.SupplierPart = objDetails.SupplierPart;
                obj.Inactive = objDetails.Inactive;
                obj.Closed = objDetails.Closed;
                obj.ROHS = objDetails.ROHS;
                obj.LineNotes = objDetails.LineNotes;
                obj.UpdatedBy = objDetails.UpdatedBy;
                obj.DLUP = objDetails.DLUP;
                obj.QuantityReceived = objDetails.QuantityReceived;
                obj.QuantityAllocated = objDetails.QuantityAllocated;
                obj.GIShipInCost = objDetails.GIShipInCost;
                obj.ProductName = objDetails.ProductName;
                obj.ProductDescription = objDetails.ProductDescription;
                obj.ProductDutyCode = objDetails.ProductDutyCode;
                obj.PackageName = objDetails.PackageName;
                obj.PackageDescription = objDetails.PackageDescription;
                obj.ImportCountryShippingCost = objDetails.ImportCountryShippingCost;
                obj.PurchaseOrderNumber = objDetails.PurchaseOrderNumber;
                obj.CurrencyCode = objDetails.CurrencyCode;
                obj.CurrencyNo = objDetails.CurrencyNo;
                obj.CurrencyDescription = objDetails.CurrencyDescription;
                obj.ManufacturerName = objDetails.ManufacturerName;
                obj.ManufacturerCode = objDetails.ManufacturerCode;
                obj.CompanyNo = objDetails.CompanyNo;
                obj.CompanyName = objDetails.CompanyName;
                obj.DateOrdered = objDetails.DateOrdered;
                obj.TaxRate = objDetails.TaxRate;
                obj.ClientNo = objDetails.ClientNo;
                //[002] code start
                obj.ImportCountryNo = objDetails.ImportCountryNo;
                //[002] code end
                obj.PromiseDate = objDetails.PromiseDate;
                obj.PromiseDate2 = objDetails.PromiseDate2;

                obj.PurchaseQuoteId = objDetails.PurchaseQuoteId;
                obj.PurchaseQuoteNumber = objDetails.PurchaseQuoteNumber;
                obj.PurchaseRequestDate = objDetails.PurchaseRequestDate;
                obj.BomNo = objDetails.BomNo;
                obj.BOMName = objDetails.BOMName;
                obj.ClientPrice = objDetails.ClientPrice;
                obj.InternalPurchaseOrderNo = objDetails.InternalPurchaseOrderNo;
                obj.ClientCurrencyNo = objDetails.ClientCurrencyNo;
                obj.ClientCurrencyCode = objDetails.ClientCurrencyCode;
                obj.ClientShipInCost = objDetails.ClientShipInCost;
                obj.IPOClientNo = objDetails.IPOClientNo;
                obj.DefaultClientLotNo = objDetails.DefaultClientLotNo;
                obj.ProductInactive = objDetails.ProductInactive;
                obj.DutyRate = objDetails.DutyRate;
                obj.ReqSerialNo = objDetails.ReqSerialNo;
                obj.MSLevel = objDetails.MSLevel;
                //[006] start
                obj.SupplierWarranty = objDetails.SupplierWarranty;
                //[006] end
                //[007] start
                obj.EPRIds = objDetails.EPRIds;
                obj.IsReleased = objDetails.IsReleased;
                obj.IsAuthorised = objDetails.IsAuthorised;
                //[007] end
                //ihs code start
                obj.CountryOfOrigin = objDetails.CountryOfOrigin;
                obj.LifeCycleStage = objDetails.LifeCycleStage;
                obj.HTSCode = objDetails.HTSCode;
                obj.AveragePrice = objDetails.AveragePrice;
                obj.Packaging = objDetails.Packaging;
                obj.PackagingSize = objDetails.PackagingSize;
                obj.Descriptions = objDetails.Descriptions;
                obj.IHSProduct = objDetails.IHSProduct;
                obj.ECCNCode = objDetails.ECCNCode;
                //ihs code end
                //[008] start
                obj.OriginalDeliveryDate = objDetails.OriginalDeliveryDate;
                //[008] end
                obj.SOPrice = objDetails.SOPrice;
                obj.IpoLineTotal = objDetails.IpoLineTotal;
                obj.LineProfit = objDetails.LineProfit;
                obj.LineProfitPercentage = objDetails.LineProfitPercentage;
                //[009] code start
                obj.PrintHazardous = objDetails.PrintHazardous;
                obj.IsProdHazardous = objDetails.IsProdHazardous;
                obj.IsOrderViaIPOonly = objDetails.IsOrderViaIPOonly;
                //[009] code end
                obj.HubCurrencyNo = objDetails.HubCurrencyNo;
                obj.CurrencyDate = objDetails.CurrencyDate;
                //[010] code start
                obj.IHSECCNCodeDefination = objDetails.IHSECCNCodeDefination;
                //[010] code end
                obj.IsRestrictedProduct = objDetails.IsRestrictedProduct;
                obj.ECCNNotify = objDetails.ECCNNotify;
                obj.EccnSubject = objDetails.EccnSubject;
                obj.EccnMessage = objDetails.EccnMessage;
                obj.AS6081 = objDetails.AS6081; //[013]
                obj.IsSanctioned = objDetails.IsSanctioned; //[RP-881]
                objDetails = null;
                return obj;
            }
        }

        public static PurchaseOrderLine Get3(System.Int32? purchaseOrderLineId)
        {
            Rebound.GlobalTrader.DAL.PurchaseOrderLineDetails objDetails = Rebound.GlobalTrader.DAL.SiteProvider.PurchaseOrderLine.Get3(purchaseOrderLineId);
            if (objDetails == null)
            {
                return null;
            }
            else
            {
                PurchaseOrderLine obj = new PurchaseOrderLine();
                obj.PurchaseOrderLineId = objDetails.PurchaseOrderLineId;
                obj.PurchaseOrderNo = objDetails.PurchaseOrderNo;
                obj.FullPart = objDetails.FullPart;
                obj.Part = objDetails.Part;
                obj.ManufacturerNo = objDetails.ManufacturerNo;
                obj.DateCode = objDetails.DateCode;
                obj.PackageNo = objDetails.PackageNo;
                obj.Quantity = objDetails.Quantity;
                obj.Price = objDetails.Price;
                obj.DeliveryDate = objDetails.DeliveryDate;
                obj.DeliveryDate2 = objDetails.DeliveryDate2;
                obj.ReceivingNotes = objDetails.ReceivingNotes;
                obj.Taxable = objDetails.Taxable;
                obj.RepeatOrder = objDetails.RepeatOrder;
                obj.ProductNo = objDetails.ProductNo;
                obj.Posted = objDetails.Posted;
                obj.ShipInCost = objDetails.ShipInCost;
                obj.SupplierPart = objDetails.SupplierPart;
                obj.Inactive = objDetails.Inactive;
                obj.Closed = objDetails.Closed;
                obj.ROHS = objDetails.ROHS;
                obj.LineNotes = objDetails.LineNotes;
                obj.UpdatedBy = objDetails.UpdatedBy;
                obj.DLUP = objDetails.DLUP;
                obj.QuantityReceived = objDetails.QuantityReceived;
                obj.QuantityAllocated = objDetails.QuantityAllocated;
                obj.GIShipInCost = objDetails.GIShipInCost;
                obj.ProductName = objDetails.ProductName;
                obj.ProductDescription = objDetails.ProductDescription;
                obj.ProductDutyCode = objDetails.ProductDutyCode;
                obj.PackageName = objDetails.PackageName;
                obj.PackageDescription = objDetails.PackageDescription;
                obj.ImportCountryShippingCost = objDetails.ImportCountryShippingCost;
                obj.PurchaseOrderNumber = objDetails.PurchaseOrderNumber;
                obj.CurrencyCode = objDetails.CurrencyCode;
                obj.CurrencyNo = objDetails.CurrencyNo;
                obj.CurrencyDescription = objDetails.CurrencyDescription;
                obj.ManufacturerName = objDetails.ManufacturerName;
                obj.ManufacturerCode = objDetails.ManufacturerCode;
                obj.CompanyNo = objDetails.CompanyNo;
                obj.CompanyName = objDetails.CompanyName;
                obj.DateOrdered = objDetails.DateOrdered;
                obj.TaxRate = objDetails.TaxRate;
                obj.ClientNo = objDetails.ClientNo;
                //[002] code start
                obj.ImportCountryNo = objDetails.ImportCountryNo;
                //[002] code end
                obj.PromiseDate = objDetails.PromiseDate;
                obj.PromiseDate2 = objDetails.PromiseDate2;

                obj.PurchaseQuoteId = objDetails.PurchaseQuoteId;
                obj.PurchaseQuoteNumber = objDetails.PurchaseQuoteNumber;
                obj.PurchaseRequestDate = objDetails.PurchaseRequestDate;
                obj.BomNo = objDetails.BomNo;
                obj.BOMName = objDetails.BOMName;
                obj.ClientPrice = objDetails.ClientPrice;
                obj.InternalPurchaseOrderNo = objDetails.InternalPurchaseOrderNo;
                obj.ClientCurrencyNo = objDetails.ClientCurrencyNo;
                obj.ClientCurrencyCode = objDetails.ClientCurrencyCode;
                obj.ClientShipInCost = objDetails.ClientShipInCost;
                obj.IPOClientNo = objDetails.IPOClientNo;
                obj.DefaultClientLotNo = objDetails.DefaultClientLotNo;
                obj.ProductInactive = objDetails.ProductInactive;
                obj.DutyRate = objDetails.DutyRate;
                obj.ReqSerialNo = objDetails.ReqSerialNo;
                obj.MSLevel = objDetails.MSLevel;
                //[006] start
                obj.SupplierWarranty = objDetails.SupplierWarranty;
                //[006] end
                //[007] start
                obj.EPRIds = objDetails.EPRIds;
                obj.IsReleased = objDetails.IsReleased;
                obj.IsAuthorised = objDetails.IsAuthorised;
                //[007] end
                //ihs code start
                obj.CountryOfOrigin = objDetails.CountryOfOrigin;
                obj.LifeCycleStage = objDetails.LifeCycleStage;
                obj.HTSCode = objDetails.HTSCode;
                obj.AveragePrice = objDetails.AveragePrice;
                obj.Packaging = objDetails.Packaging;
                obj.PackagingSize = objDetails.PackagingSize;
                obj.Descriptions = objDetails.Descriptions;
                obj.IHSProduct = objDetails.IHSProduct;
                obj.ECCNCode = objDetails.ECCNCode;
                //ihs code end
                //[008] start
                obj.OriginalDeliveryDate = objDetails.OriginalDeliveryDate;
                //[008] end
                obj.SOPrice = objDetails.SOPrice;
                obj.IpoLineTotal = objDetails.IpoLineTotal;
                obj.LineProfit = objDetails.LineProfit;
                obj.LineProfitPercentage = objDetails.LineProfitPercentage;
                //[009] code start
                obj.PrintHazardous = objDetails.PrintHazardous;
                obj.IsProdHazardous = objDetails.IsProdHazardous;
                obj.IsOrderViaIPOonly = objDetails.IsOrderViaIPOonly;
                //[009] code end
                obj.HubCurrencyNo = objDetails.HubCurrencyNo;
                obj.CurrencyDate = objDetails.CurrencyDate;
                //[010] code start
                obj.IHSECCNCodeDefination = objDetails.IHSECCNCodeDefination;
                //[010] code end
                obj.IsRestrictedProduct = objDetails.IsRestrictedProduct;
                obj.ECCNNotify = objDetails.ECCNNotify;
                obj.EccnSubject = objDetails.EccnSubject;
                obj.EccnMessage = objDetails.EccnMessage;
                obj.AS6081 = objDetails.AS6081; //[013]
                obj.IsSanctioned = objDetails.IsSanctioned; //[RP-881]
                objDetails = null;
                return obj;
            }
        }
        /// <summary>
        /// GetForSupplierRMALine
        /// Calls [usp_select_PurchaseOrderLine_for_SupplierRMALine]
        /// </summary>
        public static PurchaseOrderLine GetForSupplierRMALine(System.Int32? purchaseOrderLineId)
        {
            Rebound.GlobalTrader.DAL.PurchaseOrderLineDetails objDetails = Rebound.GlobalTrader.DAL.SiteProvider.PurchaseOrderLine.GetForSupplierRMALine(purchaseOrderLineId);
            if (objDetails == null)
            {
                return null;
            }
            else
            {
                PurchaseOrderLine obj = new PurchaseOrderLine();
                obj.PurchaseOrderLineId = objDetails.PurchaseOrderLineId;
                obj.PurchaseOrderNo = objDetails.PurchaseOrderNo;
                obj.FullPart = objDetails.FullPart;
                obj.Part = objDetails.Part;
                obj.ManufacturerNo = objDetails.ManufacturerNo;
                obj.DateCode = objDetails.DateCode;
                obj.PackageNo = objDetails.PackageNo;
                obj.Quantity = objDetails.Quantity;
                obj.Price = objDetails.Price;
                obj.DeliveryDate = objDetails.DeliveryDate;
                obj.ReceivingNotes = objDetails.ReceivingNotes;
                obj.Taxable = objDetails.Taxable;
                obj.ProductNo = objDetails.ProductNo;
                obj.Posted = objDetails.Posted;
                obj.ShipInCost = objDetails.ShipInCost;
                obj.SupplierPart = objDetails.SupplierPart;
                obj.Inactive = objDetails.Inactive;
                obj.Closed = objDetails.Closed;
                obj.ROHS = objDetails.ROHS;
                obj.LineNotes = objDetails.LineNotes;
                obj.UpdatedBy = objDetails.UpdatedBy;
                obj.DLUP = objDetails.DLUP;
                obj.QuantityReceived = objDetails.QuantityReceived;
                obj.QuantityAllocated = objDetails.QuantityAllocated;
                obj.GIShipInCost = objDetails.GIShipInCost;
                obj.ProductName = objDetails.ProductName;
                obj.ProductDescription = objDetails.ProductDescription;
                obj.ProductDutyCode = objDetails.ProductDutyCode;
                obj.PackageName = objDetails.PackageName;
                obj.PackageDescription = objDetails.PackageDescription;
                obj.ImportCountryShippingCost = objDetails.ImportCountryShippingCost;
                obj.PurchaseOrderNumber = objDetails.PurchaseOrderNumber;
                obj.CurrencyCode = objDetails.CurrencyCode;
                obj.CurrencyNo = objDetails.CurrencyNo;
                obj.CurrencyDescription = objDetails.CurrencyDescription;
                obj.ManufacturerName = objDetails.ManufacturerName;
                obj.ManufacturerCode = objDetails.ManufacturerCode;
                obj.CompanyNo = objDetails.CompanyNo;
                obj.CompanyName = objDetails.CompanyName;
                obj.DateOrdered = objDetails.DateOrdered;
                obj.TaxRate = objDetails.TaxRate;
                obj.ClientNo = objDetails.ClientNo;
                obj.QuantityToReturn = objDetails.QuantityToReturn;
                objDetails = null;
                return obj;
            }
        }
        /// <summary>
        /// GetListCandidatesForSupplierRMA
        /// Calls [usp_selectAll_PurchaseOrderLine_candidates_for_SupplierRMA]
        /// </summary>
        public static List<PurchaseOrderLine> GetListCandidatesForSupplierRMA(System.Int32? supplierRmaId)
        {
            List<PurchaseOrderLineDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.PurchaseOrderLine.GetListCandidatesForSupplierRMA(supplierRmaId);
            if (lstDetails == null)
            {
                return new List<PurchaseOrderLine>();
            }
            else
            {
                List<PurchaseOrderLine> lst = new List<PurchaseOrderLine>();
                foreach (PurchaseOrderLineDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.PurchaseOrderLine obj = new Rebound.GlobalTrader.BLL.PurchaseOrderLine();
                    obj.PurchaseOrderLineId = objDetails.PurchaseOrderLineId;
                    obj.PurchaseOrderNo = objDetails.PurchaseOrderNo;
                    obj.FullPart = objDetails.FullPart;
                    obj.Part = objDetails.Part;
                    obj.ManufacturerNo = objDetails.ManufacturerNo;
                    obj.DateCode = objDetails.DateCode;
                    obj.PackageNo = objDetails.PackageNo;
                    obj.Quantity = objDetails.Quantity;
                    obj.Price = objDetails.Price;
                    obj.DeliveryDate = objDetails.DeliveryDate;
                    obj.ReceivingNotes = objDetails.ReceivingNotes;
                    obj.Taxable = objDetails.Taxable;
                    obj.ProductNo = objDetails.ProductNo;
                    obj.Posted = objDetails.Posted;
                    obj.ShipInCost = objDetails.ShipInCost;
                    obj.SupplierPart = objDetails.SupplierPart;
                    obj.Inactive = objDetails.Inactive;
                    obj.Closed = objDetails.Closed;
                    obj.ROHS = objDetails.ROHS;
                    obj.LineNotes = objDetails.LineNotes;
                    obj.UpdatedBy = objDetails.UpdatedBy;
                    obj.DLUP = objDetails.DLUP;
                    obj.QuantityReceived = objDetails.QuantityReceived;
                    obj.QuantityAllocated = objDetails.QuantityAllocated;
                    obj.GIShipInCost = objDetails.GIShipInCost;
                    obj.ProductName = objDetails.ProductName;
                    obj.ProductDescription = objDetails.ProductDescription;
                    obj.ProductDutyCode = objDetails.ProductDutyCode;
                    obj.PackageName = objDetails.PackageName;
                    obj.PackageDescription = objDetails.PackageDescription;
                    obj.ImportCountryShippingCost = objDetails.ImportCountryShippingCost;
                    obj.PurchaseOrderNumber = objDetails.PurchaseOrderNumber;
                    obj.CurrencyCode = objDetails.CurrencyCode;
                    obj.CurrencyNo = objDetails.CurrencyNo;
                    obj.CurrencyDescription = objDetails.CurrencyDescription;
                    obj.ManufacturerName = objDetails.ManufacturerName;
                    obj.ManufacturerCode = objDetails.ManufacturerCode;
                    obj.CompanyNo = objDetails.CompanyNo;
                    obj.CompanyName = objDetails.CompanyName;
                    obj.DateOrdered = objDetails.DateOrdered;
                    obj.TaxRate = objDetails.TaxRate;
                    obj.ClientNo = objDetails.ClientNo;
                    obj.QuantityToReturn = objDetails.QuantityToReturn;
                    obj.Location = objDetails.Location;
                    obj.IsNprExist = objDetails.IsNprExist;
                    //[004]Code Start
                    obj.IsCRMA = objDetails.IsCRMA;
                    //[004]Code End
                    obj.POSerialNo = objDetails.POSerialNo;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }
        /// <summary>
        /// GetListClosedForPurchaseOrder
        /// Calls [usp_selectAll_PurchaseOrderLine_closed_for_PurchaseOrder]
        /// </summary>
        public static List<PurchaseOrderLine> GetListClosedForPurchaseOrder(System.Int32? purchaseOrderId)
        {
            List<PurchaseOrderLineDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.PurchaseOrderLine.GetListClosedForPurchaseOrder(purchaseOrderId);
            if (lstDetails == null)
            {
                return new List<PurchaseOrderLine>();
            }
            else
            {
                List<PurchaseOrderLine> lst = new List<PurchaseOrderLine>();
                foreach (PurchaseOrderLineDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.PurchaseOrderLine obj = new Rebound.GlobalTrader.BLL.PurchaseOrderLine();
                    obj.PurchaseOrderLineId = objDetails.PurchaseOrderLineId;
                    obj.PurchaseOrderNo = objDetails.PurchaseOrderNo;
                    obj.FullPart = objDetails.FullPart;
                    obj.Part = objDetails.Part;
                    obj.ManufacturerNo = objDetails.ManufacturerNo;
                    obj.DateCode = objDetails.DateCode;
                    obj.PackageNo = objDetails.PackageNo;
                    obj.Quantity = objDetails.Quantity;
                    obj.Price = objDetails.Price;
                    obj.DeliveryDate = objDetails.DeliveryDate;
                    obj.ReceivingNotes = objDetails.ReceivingNotes;
                    obj.Taxable = objDetails.Taxable;
                    obj.ProductNo = objDetails.ProductNo;
                    obj.Posted = objDetails.Posted;
                    obj.ShipInCost = objDetails.ShipInCost;
                    obj.SupplierPart = objDetails.SupplierPart;
                    obj.Inactive = objDetails.Inactive;
                    obj.Closed = objDetails.Closed;
                    obj.ROHS = objDetails.ROHS;
                    obj.LineNotes = objDetails.LineNotes;
                    obj.UpdatedBy = objDetails.UpdatedBy;
                    obj.DLUP = objDetails.DLUP;
                    obj.QuantityReceived = objDetails.QuantityReceived;
                    obj.QuantityAllocated = objDetails.QuantityAllocated;
                    obj.GIShipInCost = objDetails.GIShipInCost;
                    obj.ProductName = objDetails.ProductName;
                    obj.ProductDescription = objDetails.ProductDescription;
                    obj.ProductDutyCode = objDetails.ProductDutyCode;
                    obj.PackageName = objDetails.PackageName;
                    obj.PackageDescription = objDetails.PackageDescription;
                    obj.ImportCountryShippingCost = objDetails.ImportCountryShippingCost;
                    obj.PurchaseOrderNumber = objDetails.PurchaseOrderNumber;
                    obj.CurrencyCode = objDetails.CurrencyCode;
                    obj.CurrencyNo = objDetails.CurrencyNo;
                    obj.CurrencyDescription = objDetails.CurrencyDescription;
                    obj.ManufacturerName = objDetails.ManufacturerName;
                    obj.ManufacturerCode = objDetails.ManufacturerCode;
                    obj.CompanyNo = objDetails.CompanyNo;
                    obj.CompanyName = objDetails.CompanyName;
                    obj.DateOrdered = objDetails.DateOrdered;
                    obj.TaxRate = objDetails.TaxRate;
                    obj.ClientNo = objDetails.ClientNo;
                    obj.POSerialNo = objDetails.POSerialNo;
                    obj.InternalPurchaseOrderNo = objDetails.InternalPurchaseOrderNo;
                    obj.InternalPurchaseOrderNumber = objDetails.InternalPurchaseOrderNumber;
                    //[007] start
                    obj.IsReleased = objDetails.IsReleased;
                    obj.IsAuthorised = objDetails.IsAuthorised;
                    //[007] end
                    //[011] code start
                    obj.SupplierApprovalStatus = objDetails.SupplierApprovalStatus;
                    //[011] code end
                    obj.SupplierApprovalStatusMessage = objDetails.SupplierApprovalStatusMessage;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }
        /// <summary>
        /// GetListForPurchaseOrder
        /// Calls [usp_selectAll_PurchaseOrderLine_for_PurchaseOrder]
        /// ECCNCode
        /// </summary>
        public static List<PurchaseOrderLine> GetListForPurchaseOrder(System.Int32? purchaseOrderId)
        {
            List<PurchaseOrderLineDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.PurchaseOrderLine.GetListForPurchaseOrder(purchaseOrderId);
            if (lstDetails == null)
            {
                return new List<PurchaseOrderLine>();
            }
            else
            {
                List<PurchaseOrderLine> lst = new List<PurchaseOrderLine>();
                foreach (PurchaseOrderLineDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.PurchaseOrderLine obj = new Rebound.GlobalTrader.BLL.PurchaseOrderLine();
                    obj.PurchaseOrderLineId = objDetails.PurchaseOrderLineId;
                    obj.PurchaseOrderNo = objDetails.PurchaseOrderNo;
                    obj.FullPart = objDetails.FullPart;
                    obj.Part = objDetails.Part;
                    obj.ManufacturerNo = objDetails.ManufacturerNo;
                    obj.DateCode = objDetails.DateCode;
                    obj.PackageNo = objDetails.PackageNo;
                    obj.Quantity = objDetails.Quantity;
                    obj.Price = objDetails.Price;
                    obj.DeliveryDate = objDetails.DeliveryDate;
                    obj.ReceivingNotes = objDetails.ReceivingNotes;
                    obj.Taxable = objDetails.Taxable;
                    obj.ProductNo = objDetails.ProductNo;
                    obj.Posted = objDetails.Posted;
                    obj.ShipInCost = objDetails.ShipInCost;
                    obj.SupplierPart = objDetails.SupplierPart;
                    obj.Inactive = objDetails.Inactive;
                    obj.Closed = objDetails.Closed;
                    obj.ROHS = objDetails.ROHS;
                    obj.LineNotes = objDetails.LineNotes;
                    obj.UpdatedBy = objDetails.UpdatedBy;
                    obj.DLUP = objDetails.DLUP;
                    obj.QuantityReceived = objDetails.QuantityReceived;
                    obj.QuantityAllocated = objDetails.QuantityAllocated;
                    obj.GIShipInCost = objDetails.GIShipInCost;
                    obj.ProductName = objDetails.ProductName;
                    obj.ProductDescription = objDetails.ProductDescription;
                    obj.ProductDutyCode = objDetails.ProductDutyCode;
                    obj.PackageName = objDetails.PackageName;
                    obj.PackageDescription = objDetails.PackageDescription;
                    obj.ImportCountryShippingCost = objDetails.ImportCountryShippingCost;
                    obj.PurchaseOrderNumber = objDetails.PurchaseOrderNumber;
                    obj.CurrencyCode = objDetails.CurrencyCode;
                    obj.CurrencyNo = objDetails.CurrencyNo;
                    obj.CurrencyDescription = objDetails.CurrencyDescription;
                    obj.ManufacturerName = objDetails.ManufacturerName;
                    obj.ManufacturerCode = objDetails.ManufacturerCode;
                    obj.CompanyNo = objDetails.CompanyNo;
                    obj.CompanyName = objDetails.CompanyName;
                    obj.DateOrdered = objDetails.DateOrdered;
                    obj.TaxRate = objDetails.TaxRate;
                    obj.ClientNo = objDetails.ClientNo;
                    obj.POSerialNo = objDetails.POSerialNo;
                    obj.InternalPurchaseOrderNo = objDetails.InternalPurchaseOrderNo;
                    obj.ClientPrice = objDetails.ClientPrice;
                    obj.InternalPurchaseOrderNumber = objDetails.InternalPurchaseOrderNumber;
                    obj.MSLevel = objDetails.MSLevel;
                    obj.PrintHazardous = objDetails.PrintHazardous;
                    //[007] start
                    obj.IsReleased = objDetails.IsReleased;
                    obj.IsAuthorised = objDetails.IsAuthorised;
                    //[007] end
                    //[006] code start
                    obj.IsOrderViaIPOonly = objDetails.IsOrderViaIPOonly;
                    obj.ECCNCode = objDetails.ECCNCode;
                    //[006] code end
                    //[011] code start
                    obj.SupplierApprovalStatus = objDetails.SupplierApprovalStatus;
                    //[011] code end
                    obj.ECCNNotify = objDetails.ECCNNotify;
                    obj.EccnSubject = objDetails.EccnSubject;
                    obj.EccnMessage = objDetails.EccnMessage;
                    obj.EccnWarningMessage = objDetails.EccnWarningMessage;
                    obj.AS6081 = objDetails.AS6081; //[013]
                    obj.SupplierApprovalStatusMessage = objDetails.SupplierApprovalStatusMessage;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }
        /// <summary>
        /// GetListForSupplierRMA
        /// Calls [usp_selectAll_PurchaseOrderLine_for_SupplierRMA]
        /// </summary>
        public static List<PurchaseOrderLine> GetListForSupplierRMA(System.Int32? supplierRmaId)
        {
            List<PurchaseOrderLineDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.PurchaseOrderLine.GetListForSupplierRMA(supplierRmaId);
            if (lstDetails == null)
            {
                return new List<PurchaseOrderLine>();
            }
            else
            {
                List<PurchaseOrderLine> lst = new List<PurchaseOrderLine>();
                foreach (PurchaseOrderLineDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.PurchaseOrderLine obj = new Rebound.GlobalTrader.BLL.PurchaseOrderLine();
                    obj.PurchaseOrderLineId = objDetails.PurchaseOrderLineId;
                    obj.PurchaseOrderNo = objDetails.PurchaseOrderNo;
                    obj.FullPart = objDetails.FullPart;
                    obj.Part = objDetails.Part;
                    obj.ManufacturerNo = objDetails.ManufacturerNo;
                    obj.DateCode = objDetails.DateCode;
                    obj.PackageNo = objDetails.PackageNo;
                    obj.Quantity = objDetails.Quantity;
                    obj.Price = objDetails.Price;
                    obj.DeliveryDate = objDetails.DeliveryDate;
                    obj.ReceivingNotes = objDetails.ReceivingNotes;
                    obj.Taxable = objDetails.Taxable;
                    obj.ProductNo = objDetails.ProductNo;
                    obj.Posted = objDetails.Posted;
                    obj.ShipInCost = objDetails.ShipInCost;
                    obj.SupplierPart = objDetails.SupplierPart;
                    obj.Inactive = objDetails.Inactive;
                    obj.Closed = objDetails.Closed;
                    obj.ROHS = objDetails.ROHS;
                    obj.LineNotes = objDetails.LineNotes;
                    obj.UpdatedBy = objDetails.UpdatedBy;
                    obj.DLUP = objDetails.DLUP;
                    obj.QuantityReceived = objDetails.QuantityReceived;
                    obj.QuantityAllocated = objDetails.QuantityAllocated;
                    obj.GIShipInCost = objDetails.GIShipInCost;
                    obj.ProductName = objDetails.ProductName;
                    obj.ProductDescription = objDetails.ProductDescription;
                    obj.ProductDutyCode = objDetails.ProductDutyCode;
                    obj.PackageName = objDetails.PackageName;
                    obj.PackageDescription = objDetails.PackageDescription;
                    obj.ImportCountryShippingCost = objDetails.ImportCountryShippingCost;
                    obj.PurchaseOrderNumber = objDetails.PurchaseOrderNumber;
                    obj.CurrencyCode = objDetails.CurrencyCode;
                    obj.CurrencyNo = objDetails.CurrencyNo;
                    obj.CurrencyDescription = objDetails.CurrencyDescription;
                    obj.ManufacturerName = objDetails.ManufacturerName;
                    obj.ManufacturerCode = objDetails.ManufacturerCode;
                    obj.CompanyNo = objDetails.CompanyNo;
                    obj.CompanyName = objDetails.CompanyName;
                    obj.DateOrdered = objDetails.DateOrdered;
                    obj.TaxRate = objDetails.TaxRate;
                    obj.ClientNo = objDetails.ClientNo;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }
        /// <summary>
        /// GetListOpenForPurchaseOrder
        /// Calls [usp_selectAll_PurchaseOrderLine_open_for_PurchaseOrder]
        /// </summary>
        public static List<PurchaseOrderLine> GetListOpenForPurchaseOrder(System.Int32? purchaseOrderId)
        {
            List<PurchaseOrderLineDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.PurchaseOrderLine.GetListOpenForPurchaseOrder(purchaseOrderId);
            if (lstDetails == null)
            {
                return new List<PurchaseOrderLine>();
            }
            else
            {
                List<PurchaseOrderLine> lst = new List<PurchaseOrderLine>();
                foreach (PurchaseOrderLineDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.PurchaseOrderLine obj = new Rebound.GlobalTrader.BLL.PurchaseOrderLine();
                    obj.PurchaseOrderLineId = objDetails.PurchaseOrderLineId;
                    obj.PurchaseOrderNo = objDetails.PurchaseOrderNo;
                    obj.FullPart = objDetails.FullPart;
                    obj.Part = objDetails.Part;
                    obj.ManufacturerNo = objDetails.ManufacturerNo;
                    obj.DateCode = objDetails.DateCode;
                    obj.PackageNo = objDetails.PackageNo;
                    obj.Quantity = objDetails.Quantity;
                    obj.Price = objDetails.Price;
                    obj.DeliveryDate = objDetails.DeliveryDate;
                    obj.ReceivingNotes = objDetails.ReceivingNotes;
                    obj.Taxable = objDetails.Taxable;
                    obj.ProductNo = objDetails.ProductNo;
                    obj.Posted = objDetails.Posted;
                    obj.ShipInCost = objDetails.ShipInCost;
                    obj.SupplierPart = objDetails.SupplierPart;
                    obj.Inactive = objDetails.Inactive;
                    obj.Closed = objDetails.Closed;
                    obj.ROHS = objDetails.ROHS;
                    obj.LineNotes = objDetails.LineNotes;
                    obj.UpdatedBy = objDetails.UpdatedBy;
                    obj.DLUP = objDetails.DLUP;
                    obj.QuantityReceived = objDetails.QuantityReceived;
                    obj.QuantityAllocated = objDetails.QuantityAllocated;
                    obj.GIShipInCost = objDetails.GIShipInCost;
                    obj.ProductName = objDetails.ProductName;
                    obj.ProductDescription = objDetails.ProductDescription;
                    obj.ProductDutyCode = objDetails.ProductDutyCode;
                    obj.PackageName = objDetails.PackageName;
                    obj.PackageDescription = objDetails.PackageDescription;
                    obj.ImportCountryShippingCost = objDetails.ImportCountryShippingCost;
                    obj.PurchaseOrderNumber = objDetails.PurchaseOrderNumber;
                    obj.CurrencyCode = objDetails.CurrencyCode;
                    obj.CurrencyNo = objDetails.CurrencyNo;
                    obj.CurrencyDescription = objDetails.CurrencyDescription;
                    obj.ManufacturerName = objDetails.ManufacturerName;
                    obj.ManufacturerCode = objDetails.ManufacturerCode;
                    obj.CompanyNo = objDetails.CompanyNo;
                    obj.CompanyName = objDetails.CompanyName;
                    obj.DateOrdered = objDetails.DateOrdered;
                    obj.TaxRate = objDetails.TaxRate;
                    obj.ClientNo = objDetails.ClientNo;
                    //[001] code start
                    obj.PromiseDate = objDetails.PromiseDate;
                    //[001] code end
                    obj.POSerialNo = objDetails.POSerialNo;
                    obj.InternalPurchaseOrderNo = objDetails.InternalPurchaseOrderNo;
                    obj.InternalPurchaseOrderNumber = objDetails.InternalPurchaseOrderNumber;
                    //[007] start
                    obj.IsReleased = objDetails.IsReleased;
                    obj.IsAuthorised = objDetails.IsAuthorised;
                    //[007] end

                    //[011] code start
                    obj.SupplierApprovalStatus = objDetails.SupplierApprovalStatus;
                    //[011] code end
                    obj.AS6081 = objDetails.AS6081;
                    obj.SupplierApprovalStatusMessage = objDetails.SupplierApprovalStatusMessage;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }
        /// <summary>
        /// GetListReceivingForPurchaseOrder
        /// Calls [usp_selectAll_PurchaseOrderLine_Receiving_for_PurchaseOrder]
        /// </summary>
        public static List<PurchaseOrderLine> GetListReceivingForPurchaseOrder(System.Int32? purchaseOrderId)
        {
            List<PurchaseOrderLineDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.PurchaseOrderLine.GetListReceivingForPurchaseOrder(purchaseOrderId);
            if (lstDetails == null)
            {
                return new List<PurchaseOrderLine>();
            }
            else
            {
                List<PurchaseOrderLine> lst = new List<PurchaseOrderLine>();
                foreach (PurchaseOrderLineDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.PurchaseOrderLine obj = new Rebound.GlobalTrader.BLL.PurchaseOrderLine();
                    obj.ClientNo = objDetails.ClientNo;
                    obj.PurchaseOrderId = objDetails.PurchaseOrderId;
                    obj.PurchaseOrderNumber = objDetails.PurchaseOrderNumber;
                    obj.PurchaseOrderLineId = objDetails.PurchaseOrderLineId;
                    obj.DateOrdered = objDetails.DateOrdered;
                    obj.ExpediteDate = objDetails.ExpediteDate;
                    obj.DeliveryDate = objDetails.DeliveryDate;
                    obj.Buyer = objDetails.Buyer;
                    obj.BuyerName = objDetails.BuyerName;
                    obj.DivisionNo = objDetails.DivisionNo;
                    obj.TeamNo = objDetails.TeamNo;
                    obj.CompanyNo = objDetails.CompanyNo;
                    obj.CompanyName = objDetails.CompanyName;
                    obj.FullName = objDetails.FullName;
                    obj.CreditLimit = objDetails.CreditLimit;
                    obj.Balance = objDetails.Balance;
                    obj.ContactNo = objDetails.ContactNo;
                    obj.ContactName = objDetails.ContactName;
                    obj.Price = objDetails.Price;
                    obj.Quantity = objDetails.Quantity;
                    obj.LineValue = objDetails.LineValue;
                    obj.QuantityOrdered = objDetails.QuantityOrdered;
                    obj.QuantityReceived = objDetails.QuantityReceived;
                    obj.QuantityOutstanding = objDetails.QuantityOutstanding;
                    obj.QuantityAllocated = objDetails.QuantityAllocated;
                    obj.ShipInCost = objDetails.ShipInCost;
                    obj.ReceivingNotes = objDetails.ReceivingNotes;
                    obj.ProductNo = objDetails.ProductNo;
                    obj.ProductName = objDetails.ProductName;
                    obj.ProductDescription = objDetails.ProductDescription;
                    obj.FullPart = objDetails.FullPart;
                    obj.Part = objDetails.Part;
                    obj.ROHS = objDetails.ROHS;
                    obj.Posted = objDetails.Posted;
                    obj.SupplierPart = objDetails.SupplierPart;
                    obj.FullSupplierPart = objDetails.FullSupplierPart;
                    obj.ManufacturerNo = objDetails.ManufacturerNo;
                    obj.ManufacturerName = objDetails.ManufacturerName;
                    obj.ManufacturerCode = objDetails.ManufacturerCode;
                    obj.DateCode = objDetails.DateCode;
                    obj.PackageNo = objDetails.PackageNo;
                    obj.PackageName = objDetails.PackageName;
                    obj.PackageDescription = objDetails.PackageDescription;
                    obj.Closed = objDetails.Closed;
                    obj.TermsNo = objDetails.TermsNo;
                    obj.TermsName = objDetails.TermsName;
                    obj.InAdvance = objDetails.InAdvance;
                    obj.CurrencyNo = objDetails.CurrencyNo;
                    obj.CurrencyCode = objDetails.CurrencyCode;
                    obj.CurrencyDescription = objDetails.CurrencyDescription;
                    obj.PurchaseOrderValue = objDetails.PurchaseOrderValue;
                    obj.DatePromised = objDetails.DatePromised;
                    obj.InternalPurchaseOrderNo = objDetails.InternalPurchaseOrderNo;
                    obj.ClientPrice = objDetails.ClientPrice;
                    obj.PromiseDate = objDetails.PromiseDate;
                    //[009] code start
                    obj.IsProdHazardous = objDetails.IsProdHazardous;
                    obj.IsOrderViaIPOonly = objDetails.IsOrderViaIPOonly;
                    obj.IsRestrictedProduct = objDetails.IsRestrictedProduct;
                    //[009] code end
                    obj.IsSanctioned = objDetails.IsSanctioned; //[RP-881]
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }
        /// <summary>
        /// GetListTodayForClient
        /// Calls [usp_selectAll_PurchaseOrderLine_today_for_Client]
        /// </summary>
        public static List<PurchaseOrderLine> GetListTodayForClient(System.Int32? clientId, System.Int32? loginId, System.Int32? topToSelect)
        {
            List<PurchaseOrderLineDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.PurchaseOrderLine.GetListTodayForClient(clientId, loginId, topToSelect);
            if (lstDetails == null)
            {
                return new List<PurchaseOrderLine>();
            }
            else
            {
                List<PurchaseOrderLine> lst = new List<PurchaseOrderLine>();
                foreach (PurchaseOrderLineDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.PurchaseOrderLine obj = new Rebound.GlobalTrader.BLL.PurchaseOrderLine();
                    obj.PurchaseOrderId = objDetails.PurchaseOrderId;
                    obj.PurchaseOrderNumber = objDetails.PurchaseOrderNumber;
                    obj.FullPart = objDetails.FullPart;
                    obj.BuyerName = objDetails.BuyerName;
                    obj.Quantity = objDetails.Quantity;
                    obj.Price = objDetails.Price;
                    obj.CurrencyCode = objDetails.CurrencyCode;
                    obj.POApprovedBy = objDetails.POApprovedBy;

                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }
        /// <summary>
        /// Source
        /// Calls [usp_source_PurchaseOrderLine]
        /// </summary>
        public static List<PurchaseOrderLine> Source(System.Int32? clientId, System.String partSearch, System.Int32? index, DateTime? maxDate, out DateTime? outDate, System.Boolean? blnReferesh, bool IsServerLocal, System.Boolean? isPoHub)
        {
            DateTime? StartDate = null;
            DateTime? EndDate = null;
            if (index == 2 && maxDate.HasValue)
            {
                StartDate = (!blnReferesh.Value) ? maxDate.Value.AddMonths(-6) : maxDate.Value.AddMonths(-12);
                EndDate = maxDate.Value;
            }
            else if (index == 3 && maxDate.HasValue)
            {
                StartDate = DateTime.Parse("1900-01-01 00:00:00.000");
                EndDate = maxDate.Value;
            }
            List<PurchaseOrderLineDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.PurchaseOrderLine.Source(clientId, partSearch, index, StartDate, EndDate, out outDate, IsServerLocal, isPoHub);
            if (lstDetails == null)
            {
                return new List<PurchaseOrderLine>();
            }
            else
            {
                List<PurchaseOrderLine> lst = new List<PurchaseOrderLine>();
                foreach (PurchaseOrderLineDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.PurchaseOrderLine obj = new Rebound.GlobalTrader.BLL.PurchaseOrderLine();
                    obj.PurchaseOrderLineId = objDetails.PurchaseOrderLineId;
                    obj.PurchaseOrderNo = objDetails.PurchaseOrderNo;
                    obj.PurchaseOrderNumber = objDetails.PurchaseOrderNumber;
                    obj.Part = objDetails.Part;
                    obj.Quantity = objDetails.Quantity;
                    obj.Price = objDetails.Price;
                    obj.ROHS = objDetails.ROHS;
                    obj.ManufacturerNo = objDetails.ManufacturerNo;
                    obj.ManufacturerCode = objDetails.ManufacturerCode;
                    obj.CompanyNo = objDetails.CompanyNo;
                    obj.CompanyName = objDetails.CompanyName;
                    obj.DateOrdered = objDetails.DateOrdered;
                    obj.CurrencyCode = objDetails.CurrencyCode;
                    obj.DateCode = objDetails.DateCode;
                    obj.SupplierPart = objDetails.SupplierPart;
                    obj.ProductName = objDetails.ProductName;
                    obj.PackageName = objDetails.PackageName;
                    obj.ClientNo = objDetails.ClientNo;
                    obj.ClientName = objDetails.ClientName;
                    obj.ClientDataVisibleToOthers = objDetails.ClientDataVisibleToOthers;
                    obj.Buyer = objDetails.Buyer;
                    obj.BuyerName = objDetails.BuyerName;
                    obj.blnSRMA = objDetails.blnSRMA;
                    obj.ClientCode = objDetails.ClientCode;
                    obj.InternalPurchaseOrderNo = objDetails.InternalPurchaseOrderNo;
                    obj.InternalPurchaseOrderNumber = objDetails.InternalPurchaseOrderNumber;
                    obj.IPOClientNo = objDetails.IPOClientNo;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }

        //code added by anand for cross Match PurchaseOrderLine
        /// <summary>
        /// Source
        /// Calls [usp_CrossMatch_PurchaseOrderLine]
        /// </summary>
        public static List<PurchaseOrderLine> SourcePurchaseOrderLine(System.Int32? clientId, System.Int32? pageIndex, System.Int32? pageSize, System.Int32? orderBy, System.String sortDir, System.String partSearch, System.String PartMatch, System.String months, System.Int32? monthTime, System.Int32? vendorNo, System.Int32? currencyNo, System.Boolean? isManufaurer, System.Int32? NoOfTopRecord, bool IsServerLocal, System.Boolean? isPohub, System.Int32? BomID, System.Boolean? IncludeAltPart, System.Int32? ReqId)
        {

            List<PurchaseOrderLineDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.PurchaseOrderLine.SourcePurchaseOrderLine(clientId, pageIndex, pageSize, orderBy, sortDir, partSearch, PartMatch, months, monthTime, vendorNo, currencyNo, isManufaurer, NoOfTopRecord, IsServerLocal, isPohub, BomID, IncludeAltPart, ReqId);
            if (lstDetails == null)
            {
                return new List<PurchaseOrderLine>();
            }
            else
            {
                List<PurchaseOrderLine> lst = new List<PurchaseOrderLine>();
                foreach (PurchaseOrderLineDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.PurchaseOrderLine obj = new Rebound.GlobalTrader.BLL.PurchaseOrderLine();
                    obj.PurchaseOrderLineId = objDetails.PurchaseOrderLineId;
                    obj.PurchaseOrderNo = objDetails.PurchaseOrderNo;
                    obj.PurchaseOrderNumber = objDetails.PurchaseOrderNumber;
                    obj.Part = objDetails.Part;
                    obj.Quantity = objDetails.Quantity;
                    obj.Price = objDetails.Price;
                    obj.ROHS = objDetails.ROHS;
                    obj.ManufacturerNo = objDetails.ManufacturerNo;
                    obj.ManufacturerCode = objDetails.ManufacturerCode;
                    obj.CompanyNo = objDetails.CompanyNo;
                    obj.CompanyName = objDetails.CompanyName;
                    obj.DateOrdered = objDetails.DateOrdered;
                    obj.CurrencyCode = objDetails.CurrencyCode;
                    obj.DateCode = objDetails.DateCode;
                    obj.SupplierPart = objDetails.SupplierPart;
                    obj.ProductName = objDetails.ProductName;
                    obj.PackageName = objDetails.PackageName;
                    obj.ClientNo = objDetails.ClientNo;
                    obj.ClientName = objDetails.ClientName;
                    obj.ClientDataVisibleToOthers = objDetails.ClientDataVisibleToOthers;
                    obj.Buyer = objDetails.Buyer;
                    obj.BuyerName = objDetails.BuyerName;
                    obj.blnSRMA = objDetails.blnSRMA;
                    obj.ClientCode = objDetails.ClientCode;
                    obj.InternalPurchaseOrderNo = objDetails.InternalPurchaseOrderNo;
                    obj.InternalPurchaseOrderNumber = objDetails.InternalPurchaseOrderNumber;
                    obj.IPOClientNo = objDetails.IPOClientNo;
                    obj.isIncludeAltPart = objDetails.isIncludeAltPart;
                    obj.RowNum = objDetails.RowNum;
                    obj.TotalCount = objDetails.TotalCount;

                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }
        //code end
        /// <summary>
        /// Update
        /// Calls [usp_update_PurchaseOrderLine]
        /// </summary>
        public static bool Update(System.Int32? purchaseOrderLineId, System.String part, System.Int32? manufacturerNo, System.String dateCode, System.Int32? packageNo, System.Int32? quantity, System.Double? price, System.DateTime? deliveryDate, System.String receivingNotes, System.Boolean? taxable, System.Int32? productNo, System.Double? shipInCost, System.String supplierPart, System.Boolean? inactive, System.Byte? rohs, System.String notes, System.DateTime? PromiseDate, System.Int32? updatedBy, System.String msLevel, System.Boolean? printHazardous, System.Int32? ECCNId, System.Boolean? RepeatOrder, System.Boolean? Eprflag)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.PurchaseOrderLine.Update(purchaseOrderLineId, part, manufacturerNo, dateCode, packageNo, quantity, price, deliveryDate, receivingNotes, taxable, productNo, shipInCost, supplierPart, inactive, rohs, notes, PromiseDate, updatedBy, false, msLevel, printHazardous, null, RepeatOrder, Eprflag, ECCNId);
        }
        /// <summary>
        /// Update (without parameters)
        /// Calls [usp_update_PurchaseOrderLine]
        /// </summary>
        public bool Update()
        {
            return SiteProvider.PurchaseOrderLine.Update(PurchaseOrderLineId,
                                                         Part,
                                                         ManufacturerNo,
                                                         DateCode,
                                                         PackageNo,
                                                         Quantity,
                                                         Price,
                                                         DeliveryDate,
                                                         ReceivingNotes,
                                                         Taxable,
                                                         ProductNo,
                                                         ShipInCost,
                                                         SupplierPart,
                                                         Inactive,
                                                         ROHS,
                                                         Notes,
                                                         PromiseDate,
                                                         UpdatedBy,
                                                         ReqSerialNo,
                                                         MSLevel,
                                                         PrintHazardous,
                                                         StatusId,
                                                         RepeatOrder,
                                                         Eprflag,
                                                         ECCNId

                                                         );
        }
        /// <summary>
        /// UpdateClose
        /// Calls [usp_update_PurchaseOrderLine_Close]
        /// </summary>
        public static bool UpdateClose(System.Int32? purchaseOrderLineId, System.Int32? updatedBy, out System.Int32? SalesPersonNo, out System.Int32? SalesOrderNo,
            out System.Int32? SalesOrderLineNo, out System.Int32? SalesOrderNumber, out System.String SalesPersonName,
            out System.String SalesPersonEmail, out System.String Part, out System.Int32? PurchaseOrderNumber, out System.Int32? POLineNumber)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.PurchaseOrderLine.UpdateClose(purchaseOrderLineId, updatedBy, out SalesPersonNo, out SalesOrderNo, out SalesOrderLineNo, out SalesOrderNumber,
                out SalesPersonName, out SalesPersonEmail, out Part, out PurchaseOrderNumber, out POLineNumber);
        }
        /// <summary>
        /// UpdateClosedStatus
        /// Calls [usp_update_PurchaseOrderLine_Closed_Status]
        /// </summary>
        public static bool UpdateClosedStatus(System.Int32? purchaseOrderLineNo)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.PurchaseOrderLine.UpdateClosedStatus(purchaseOrderLineNo);
        }
        /// <summary>
        /// UpdatePostOrUnpost
        /// Calls [usp_update_PurchaseOrderLine_Post_or_Unpost]
        /// </summary>
        public static bool UpdatePostOrUnpost(System.Int32? purchaseOrderLineId, System.Boolean? posted, System.Int32? updatedBy)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.PurchaseOrderLine.UpdatePostOrUnpost(purchaseOrderLineId, posted, updatedBy);
        }
        /// <summary>
        /// UpdateClose
        /// Calls [usp_chk_SupplierApprovalStatus]
        /// </summary>
        public static int ChkSupplierValidation(System.Int32? purchaseOrderLineId)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.PurchaseOrderLine.ChkSupplierValidation(purchaseOrderLineId);
        }
        /// <summary>
        /// UpdateClose
        /// Calls [usp_Sa_IsQualityGroupUser]
        /// </summary>
        public static bool IsQualityGroupUser(System.Int32? UserId, System.Int32? ClientId)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.PurchaseOrderLine.IsQualityGroupUser(UserId, ClientId);
        }
        private static PurchaseOrderLine PopulateFromDBDetailsObject(PurchaseOrderLineDetails obj)
        {
            PurchaseOrderLine objNew = new PurchaseOrderLine();
            objNew.PurchaseOrderLineId = obj.PurchaseOrderLineId;
            objNew.PurchaseOrderNo = obj.PurchaseOrderNo;
            objNew.FullPart = obj.FullPart;
            objNew.Part = obj.Part;
            objNew.ManufacturerNo = obj.ManufacturerNo;
            objNew.DateCode = obj.DateCode;
            objNew.PackageNo = obj.PackageNo;
            objNew.Quantity = obj.Quantity;
            objNew.Price = obj.Price;
            objNew.DeliveryDate = obj.DeliveryDate;
            objNew.ReceivingNotes = obj.ReceivingNotes;
            objNew.Taxable = obj.Taxable;
            objNew.ProductNo = obj.ProductNo;
            objNew.Posted = obj.Posted;
            objNew.ShipInCost = obj.ShipInCost;
            objNew.SupplierPart = obj.SupplierPart;
            objNew.Inactive = obj.Inactive;
            objNew.Closed = obj.Closed;
            objNew.ROHS = obj.ROHS;
            objNew.UpdatedBy = obj.UpdatedBy;
            objNew.DLUP = obj.DLUP;
            objNew.Notes = obj.Notes;
            objNew.FullSupplierPart = obj.FullSupplierPart;
            objNew.PurchaseOrderId = obj.PurchaseOrderId;
            objNew.PurchaseOrderNumber = obj.PurchaseOrderNumber;
            objNew.CurrencyCode = obj.CurrencyCode;
            objNew.QuantityOrdered = obj.QuantityOrdered;
            objNew.DateOrdered = obj.DateOrdered;
            objNew.CompanyName = obj.CompanyName;
            objNew.CompanyNo = obj.CompanyNo;
            objNew.ContactName = obj.ContactName;
            objNew.ContactNo = obj.ContactNo;
            objNew.ManufacturerCode = obj.ManufacturerCode;
            objNew.RowNum = obj.RowNum;
            objNew.RowCnt = obj.RowCnt;
            objNew.Status = obj.Status;
            objNew.QuantityOutstanding = obj.QuantityOutstanding;
            objNew.EarliestShipDate = obj.EarliestShipDate;
            objNew.PurchaseOrderValue = obj.PurchaseOrderValue;
            objNew.LineNotes = obj.LineNotes;
            objNew.QuantityReceived = obj.QuantityReceived;
            objNew.QuantityAllocated = obj.QuantityAllocated;
            objNew.GIShipInCost = obj.GIShipInCost;
            objNew.ProductName = obj.ProductName;
            objNew.ProductDescription = obj.ProductDescription;
            objNew.ProductDutyCode = obj.ProductDutyCode;
            objNew.PackageName = obj.PackageName;
            objNew.PackageDescription = obj.PackageDescription;
            objNew.ImportCountryShippingCost = obj.ImportCountryShippingCost;
            objNew.CurrencyNo = obj.CurrencyNo;
            objNew.CurrencyDescription = obj.CurrencyDescription;
            objNew.ManufacturerName = obj.ManufacturerName;
            objNew.TaxRate = obj.TaxRate;
            objNew.ClientNo = obj.ClientNo;
            objNew.QuantityToReturn = obj.QuantityToReturn;
            objNew.ExpediteDate = obj.ExpediteDate;
            objNew.Buyer = obj.Buyer;
            objNew.BuyerName = obj.BuyerName;
            objNew.DivisionNo = obj.DivisionNo;
            objNew.TeamNo = obj.TeamNo;
            objNew.FullName = obj.FullName;
            objNew.CreditLimit = obj.CreditLimit;
            objNew.Balance = obj.Balance;
            objNew.LineValue = obj.LineValue;
            objNew.TermsNo = obj.TermsNo;
            objNew.TermsName = obj.TermsName;
            objNew.InAdvance = obj.InAdvance;
            objNew.DatePromised = obj.DatePromised;
            objNew.ClientName = obj.ClientName;
            objNew.ClientDataVisibleToOthers = obj.ClientDataVisibleToOthers;
            return objNew;
        }


        /// <summary>
        /// Report115
        /// Calls [usp_select_PurchaseRequestLineDetails]
        /// </summary>
        public static List<List<object>> GetPurchaseQuoteLineList(System.Int32? BOMNo, System.Int32? clientID, System.Int32? companyNo)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.QuoteLine.GetPQLineList(BOMNo, clientID, companyNo);
        }


        /// <summary>
        /// GetListForPurchaseOrder
        /// Calls [usp_selectAll_PurchaseOrderLine_for_PurchaseOrderReport]
        /// </summary>
        public static List<PurchaseOrderLine> GetListForPurchaseOrderReport(System.Int32? purchaseOrderId)
        {
            List<PurchaseOrderLineDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.PurchaseOrderLine.GetListForPurchaseOrderReport(purchaseOrderId);
            if (lstDetails == null)
            {
                return new List<PurchaseOrderLine>();
            }
            else
            {
                List<PurchaseOrderLine> lst = new List<PurchaseOrderLine>();
                foreach (PurchaseOrderLineDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.PurchaseOrderLine obj = new Rebound.GlobalTrader.BLL.PurchaseOrderLine();
                    obj.PurchaseOrderLineId = objDetails.PurchaseOrderLineId;
                    obj.PurchaseOrderNo = objDetails.PurchaseOrderNo;
                    obj.FullPart = objDetails.FullPart;
                    obj.Part = objDetails.Part;
                    obj.ManufacturerNo = objDetails.ManufacturerNo;
                    obj.DateCode = objDetails.DateCode;
                    obj.PackageNo = objDetails.PackageNo;
                    obj.Quantity = objDetails.Quantity;
                    obj.Price = objDetails.Price;
                    obj.DeliveryDate = objDetails.DeliveryDate;
                    obj.ReceivingNotes = objDetails.ReceivingNotes;
                    obj.Taxable = objDetails.Taxable;
                    obj.ProductNo = objDetails.ProductNo;
                    obj.Posted = objDetails.Posted;
                    obj.ShipInCost = objDetails.ShipInCost;
                    obj.SupplierPart = objDetails.SupplierPart;
                    obj.Inactive = objDetails.Inactive;
                    obj.Closed = objDetails.Closed;
                    obj.ROHS = objDetails.ROHS;
                    obj.LineNotes = objDetails.LineNotes;
                    obj.UpdatedBy = objDetails.UpdatedBy;
                    obj.DLUP = objDetails.DLUP;
                    obj.QuantityReceived = objDetails.QuantityReceived;
                    obj.QuantityAllocated = objDetails.QuantityAllocated;
                    obj.GIShipInCost = objDetails.GIShipInCost;
                    obj.ProductName = objDetails.ProductName;
                    obj.ProductDescription = objDetails.ProductDescription;
                    obj.ProductDutyCode = objDetails.ProductDutyCode;
                    obj.PackageName = objDetails.PackageName;
                    obj.PackageDescription = objDetails.PackageDescription;
                    obj.ImportCountryShippingCost = objDetails.ImportCountryShippingCost;
                    obj.PurchaseOrderNumber = objDetails.PurchaseOrderNumber;
                    obj.CurrencyCode = objDetails.CurrencyCode;
                    obj.CurrencyNo = objDetails.CurrencyNo;
                    obj.CurrencyDescription = objDetails.CurrencyDescription;
                    obj.ManufacturerName = objDetails.ManufacturerName;
                    obj.ManufacturerCode = objDetails.ManufacturerCode;
                    obj.CompanyNo = objDetails.CompanyNo;
                    obj.CompanyName = objDetails.CompanyName;
                    obj.DateOrdered = objDetails.DateOrdered;
                    obj.TaxRate = objDetails.TaxRate;
                    obj.ClientNo = objDetails.ClientNo;
                    obj.POSerialNo = objDetails.POSerialNo;
                    obj.InternalPurchaseOrderNo = objDetails.InternalPurchaseOrderNo;
                    obj.BuyerName = objDetails.BuyerName;
                    obj.ContactName = objDetails.ContactName;
                    obj.PromiseDate = objDetails.PromiseDate;
                    obj.ClientPrice = objDetails.ClientPrice;
                    obj.CTName = objDetails.CTName;
                    obj.SOPrice = objDetails.SOPrice;
                    obj.SOCurrencyCode = objDetails.SOCurrencyCode;
                    obj.SOCurrencyNo = objDetails.SOCurrencyNo;
                    obj.SODateOrdered = objDetails.SODateOrdered;
                    obj.ClientCurrencyNo = objDetails.ClientCurrencyNo;
                    obj.ClientCurrencyCode = objDetails.ClientCurrencyCode;
                    obj.HubCurrencyNo = objDetails.HubCurrencyNo;
                    obj.CurrencyDate = objDetails.CurrencyDate;
                    //[005] start
                    obj.SONumberDetail = objDetails.SONumberDetail;
                    //[005] end
                    obj.IpoLineTotal = objDetails.IpoLineTotal;
                    obj.LineProfit = objDetails.LineProfit;
                    obj.LineProfitPercentage = objDetails.LineProfitPercentage;

                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }

        /// <summary>
        /// Get
        /// Calls [usp_Validate_PurchaseOrderLine_Receiving]
        /// </summary>
        public static PurchaseOrderLine ValidatePOLineReceiving(System.Int32? purchaseOrderLineId)
        {
            Rebound.GlobalTrader.DAL.PurchaseOrderLineDetails objDetails = Rebound.GlobalTrader.DAL.SiteProvider.PurchaseOrderLine.ValidatePOLineReceiving(purchaseOrderLineId);
            if (objDetails == null)
            {
                return null;
            }
            else
            {
                PurchaseOrderLine obj = new PurchaseOrderLine();
                obj.Quantity = objDetails.Quantity;
                //obj.QuantityReceived = objDetails.QuantityReceived;
                obj.Closed = objDetails.Closed;
                objDetails = null;
                return obj;
            }
        }

        /// <summary>
        /// usp_Get_IPO_For_ExpediteNotes
        /// </summary>
        /// <param name="salesOrderNo"></param>
        /// <returns></returns>
        public static List<PurchaseOrderLine> GetListIPOMessage(System.String poLineIds)
        {
            List<PurchaseOrderLineDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.PurchaseOrderLine.GetListIPOMessage(poLineIds);
            if (lstDetails == null)
            {
                return new List<PurchaseOrderLine>();
            }
            else
            {
                List<PurchaseOrderLine> lst = new List<PurchaseOrderLine>();
                foreach (PurchaseOrderLineDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.PurchaseOrderLine obj = new Rebound.GlobalTrader.BLL.PurchaseOrderLine();
                    obj.PurchaseOrderId = objDetails.PurchaseOrderId;
                    obj.PurchaseOrderNumber = objDetails.PurchaseOrderNumber;
                    obj.POSerialNo = objDetails.POSerialNo;
                    obj.Part = objDetails.Part;
                    obj.InternalPurchaseOrderNumber = objDetails.InternalPurchaseOrderNumber;
                    obj.InternalPurchaseOrderNo = objDetails.InternalPurchaseOrderNo;
                    obj.IPOBuyer = objDetails.IPOBuyer;
                    obj.IPOBuyerName = objDetails.IPOBuyerName;
                    obj.CompanyNo = objDetails.CompanyNo;
                    obj.MailGroupId = objDetails.MailGroupId;
                    obj.Buyer = objDetails.Buyer;
                    obj.Quantity = objDetails.Quantity;
                    obj.DeliveryDate = objDetails.DeliveryDate;
                    obj.SupportTeamMemberNo = objDetails.SupportTeamMemberNo;
                    obj.IPOSupportTeamMemberNo = objDetails.IPOSupportTeamMemberNo;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }
        //[007] start
        /// <summary>
        /// usp_Release_UnRelease_POLines
        /// </summary>
        /// <param name="salesOrderNo"></param>
        /// <returns></returns>
        public static int Release_POLines(string poLineIDs, int updatedBy, out string message)
        {
            Int32 objReturn = Rebound.GlobalTrader.DAL.SiteProvider.PurchaseOrderLine.Release_POLines(poLineIDs, updatedBy, out message);
            return objReturn;
        }
        //[007] end
        #endregion

    }
}
