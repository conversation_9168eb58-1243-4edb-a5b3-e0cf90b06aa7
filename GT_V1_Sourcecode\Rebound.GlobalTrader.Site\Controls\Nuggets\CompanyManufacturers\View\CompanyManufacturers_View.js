Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.CompanyManufacturers_View=function(n){Rebound.GlobalTrader.Site.Controls.Forms.CompanyManufacturers_View.initializeBase(this,[n]);this._intManufacturerLinkID=-1};Rebound.GlobalTrader.Site.Controls.Forms.CompanyManufacturers_View.prototype={initialize:function(){Rebound.GlobalTrader.Site.Controls.Forms.CompanyManufacturers_View.callBaseMethod(this,"initialize");this.addShown(Function.createDelegate(this,this.formShown));$("#ctl00_cphMain_ctlManufacturersSupplied_ctlDB_ctl14_frmView_ctlDB_ctlRating_ctl03_ctlStarRating").css("pointer-events","none");$("#ctl00_cphMain_ctlManufacturersSupplied_ctlDB_ctl14_frmView_ctlDB_ctlRating_ctl03_ctlStarRating_star5").remove()},formShown:function(){this._blnFirstTimeShown&&this.addSave(Function.createDelegate(this,this.saveClicked))},saveClicked:function(){var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/CompanyManufacturers");n.set_DataObject("CompanyManufacturers");n.set_DataAction("SaveFranchise");n.addParameter("ID",this._intManufacturerLinkID);n.addParameter("IsFranchised",this.getFieldValue("ctlFranchise"));n.addDataOK(Function.createDelegate(this,this.saveEditComplete));n.addError(Function.createDelegate(this,this.saveEditError));n.addTimeout(Function.createDelegate(this,this.saveEditError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},saveEditError:function(n){this._strErrorMessage=n._errorMessage;this.onSaveError()},saveEditComplete:function(n){n._result.Result==!0?this.onSaveComplete():(this._strErrorMessage=n._errorMessage,this.onSaveError())},dispose:function(){this.isDisposed||(this._ctlConfirm&&this._ctlConfirm.dispose(),this._ctlConfirm=null,this._intManufacturerLinkID=null,this._intCommunicationLogID=null,Rebound.GlobalTrader.Site.Controls.Forms.CompanyManufacturers_View.callBaseMethod(this,"dispose"))}};Rebound.GlobalTrader.Site.Controls.Forms.CompanyManufacturers_View.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.CompanyManufacturers_View",Rebound.GlobalTrader.Site.Controls.Forms.Base,Sys.IDisposable);