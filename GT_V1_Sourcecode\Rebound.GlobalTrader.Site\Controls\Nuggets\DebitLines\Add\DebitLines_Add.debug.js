 ///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 30.03.2010:
// - remove check for ROHS being entered
//
// RP 06.01.2010:
// - fully dispose everything
//
// RP 04.12.2009:
// - allow sources for new lines to be set on permissions
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.DebitLines_Add = function(element) { 
	Rebound.GlobalTrader.Site.Controls.Forms.DebitLines_Add.initializeBase(this, [element]);
	this._intDebitID = 0;
	this._intLineID = 0;
	this._intSupplierRMALineID = null;
	this._intPurchaseOrderID = null;
	this._intPurchaseOrderLineID = null;
	this._intServiceID = null;
	this._strSupplierName = "";
	this._intCurrencyNo = null;
	this._intGlobalClientNo = -1;
	this._blnGlobalUser = false;
};

Rebound.GlobalTrader.Site.Controls.Forms.DebitLines_Add.prototype = {

	get_strSupplierName: function() { return this._strSupplierName; }, set_strSupplierName: function(v) { if (this._strSupplierName !== v) this._strSupplierName = v; }, 
	get_intDebitID: function() { return this._intDebitID; }, 	set_intDebitID: function(v) { if (this._intDebitID !== v)  this._intDebitID = v; }, 
	get_ibtnContinue: function() { return this._ibtnContinue; }, 	set_ibtnContinue: function(v) { if (this._ibtnContinue !== v)  this._ibtnContinue = v; }, 
	get_ibtnContinue_Footer: function() { return this._ibtnContinue_Footer; }, 	set_ibtnContinue_Footer: function(v) { if (this._ibtnContinue_Footer !== v)  this._ibtnContinue_Footer = v; }, 
	get_radSelectSource: function() { return this._radSelectSource; }, 	set_radSelectSource: function(v) { if (this._radSelectSource !== v)  this._radSelectSource = v; }, 
	get_trSelectPOLine: function() { return this._trSelectPOLine; }, 	set_trSelectPOLine: function(v) { if (this._trSelectPOLine !== v)  this._trSelectPOLine = v; }, 
	get_trSelectService: function() { return this._trSelectService; }, 	set_trSelectService: function(v) { if (this._trSelectService !== v)  this._trSelectService = v; }, 
	get_ctlSelectService: function() { return this._ctlSelectService; }, 	set_ctlSelectService: function(v) { if (this._ctlSelectService !== v)  this._ctlSelectService = v; }, 
	get_pnlLines: function() { return this._pnlLines; }, 	set_pnlLines: function(v) { if (this._pnlLines !== v)  this._pnlLines = v; }, 
	get_tblLines: function() { return this._tblLines; }, 	set_tblLines: function(v) { if (this._tblLines !== v)  this._tblLines = v; }, 
	get_pnlLinesError: function() { return this._pnlLinesError; }, 	set_pnlLinesError: function(v) { if (this._pnlLinesError !== v)  this._pnlLinesError = v; }, 
	get_lblLinesError: function() { return this._lblLinesError; }, 	set_lblLinesError: function(v) { if (this._lblLinesError !== v)  this._lblLinesError = v; }, 
	get_pnlLinesLoading: function() { return this._pnlLinesLoading; }, 	set_pnlLinesLoading: function(v) { if (this._pnlLinesLoading !== v)  this._pnlLinesLoading = v; }, 
	get_pnlLinesNoneFound: function() { return this._pnlLinesNoneFound; }, 	set_pnlLinesNoneFound: function(v) { if (this._pnlLinesNoneFound !== v)  this._pnlLinesNoneFound = v; }, 
	get_pnlLinesNotAvailable: function() { return this._pnlLinesNotAvailable; }, 	set_pnlLinesNotAvailable: function(v) { if (this._pnlLinesNotAvailable !== v)  this._pnlLinesNotAvailable = v; }, 
	get_lblCurrency_Price: function() { return this._lblCurrency_Price; }, 	set_lblCurrency_Price: function(v) { if (this._lblCurrency_Price !== v)  this._lblCurrency_Price = v; }, 
	get_lblExplain_POLine: function() { return this._lblExplain_POLine; }, 	set_lblExplain_POLine: function(v) { if (this._lblExplain_POLine !== v)  this._lblExplain_POLine = v; }, 
	get_lblExplain_Service: function() { return this._lblExplain_Service; }, 	set_lblExplain_Service: function(v) { if (this._lblExplain_Service !== v)  this._lblExplain_Service = v; }, 
	get_arySources: function() { return this._arySources; }, set_arySources: function(v) { if (this._arySources !== v) this._arySources = v; }, 

	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.Forms.DebitLines_Add.callBaseMethod(this, "initialize");
		this.addShown(Function.createDelegate(this, this.formShown));
	},

	dispose: function() { 
		if (this.isDisposed) return;
		if (this._ibtnContinue) $R_IBTN.clearHandlers(this._ibtnContinue);
		if (this._ibtnContinue_Footer) $R_IBTN.clearHandlers(this._ibtnContinue_Footer);
		if (this._ctlSelectService) this._ctlSelectService.dispose();
		if (this._tblLines) this._tblLines.dispose();
		this._strSupplierName = null;
		this._intDebitID = null;
		this._ibtnContinue = null;
		this._ibtnContinue_Footer = null;
		this._radSelectSource = null;
		this._trSelectPOLine = null;
		this._trSelectService = null;
		this._ctlSelectService = null;
		this._pnlLines = null;
		this._tblLines = null;
		this._pnlLinesError = null;
		this._lblLinesError = null;
		this._pnlLinesLoading = null;
		this._pnlLinesNoneFound = null;
		this._pnlLinesNotAvailable = null;
		this._lblCurrency_Price = null;
		this._lblExplain_POLine = null;
		this._lblExplain_Service = null;
		this._arySources = null;
		this._intLineID = null;
		this._intSupplierRMALineID = null;
		this._intPurchaseOrderID = null;
		this._intPurchaseOrderLineID = null;
		this._intServiceID = null;
		this._strSupplierName = null;
		this._intCurrencyNo = null;
		this._intGlobalClientNo = null;
		this._blnGlobalUser = null;
		Rebound.GlobalTrader.Site.Controls.Forms.DebitLines_Add.callBaseMethod(this, "dispose");
	},
		
	formShown: function() {
		if (this._blnFirstTimeShown) {
			//form events
			this.addSave(Function.createDelegate(this, this.saveClicked));
			
			//buttons
			var fnContinue = Function.createDelegate(this, this.continueClicked);
			$R_IBTN.addClick(this._ibtnContinue, fnContinue);
			$R_IBTN.addClick(this._ibtnContinue_Footer, fnContinue);
			
			//other controls
			this._ctlMultiStep.addStepChanged(Function.createDelegate(this, this.stepChanged));
			this._tblLines.addSelectedIndexChanged(Function.createDelegate(this, this.selectPOLine));
			this._ctlSelectService.addItemSelected(Function.createDelegate(this, this.selectService));
			
			//data
			this._strPathToData = "controls/Nuggets/DebitLines";
			this._strDataObject = "DebitLines";
		}
		this.resetSteps();
		this.setFormFieldsToDefaults();
		$find(this.getField("ctlProduct").ControlID)._aut.addSelectionMadeEvent(Function.createDelegate(this, this.productChange));
		this.setFieldValue("ctlPrintHazWar", false);
		this.enableFieldCheckBox("ctlPrintHazWar", false);
	},
	
	setFieldsFromHeader: function(intCurrencyNo, strCurrencyCode, strDebitNumber, strSupplierName, intPONo, dtmDebitDate) {
		this.setFieldValue("ctlDebit", strDebitNumber);
		this.setFieldValue("ctlSupplier", strSupplierName);
		this._intPurchaseOrderID = intPONo;
		this._strSupplierName = strSupplierName;
		this._intCurrencyNo = intCurrencyNo;
		$R_FN.setInnerHTML(this._lblCurrency_Price, strCurrencyCode);
		this._dtmDebitDate = dtmDebitDate;
	},

	continueClicked: function() {
		this.nextStep(); 
	},
	
	findWhichTypeSelected: function() {
	    for (var i = 0; i < this._arySources.length; i++) {
	        var rad = $get(String.format("{0}_{1}", this._radSelectSource.id, i));
	        if (rad.checked) return this._arySources[i];
	    }
	},
	
	stepChanged: function() {
		this._strSourceSelected = this.findWhichTypeSelected();
		var intStep = this._ctlMultiStep._intCurrentStep;
		var blnShowContinue = intStep == 1 || (intStep == 1 && this._strSourceSelected != "NEW");
		$R_IBTN.showButton(this._ibtnContinue, blnShowContinue);
		$R_IBTN.showButton(this._ibtnContinue_Footer, blnShowContinue);
		$R_IBTN.enableButton(this._ibtnSave, intStep == 3);
		$R_IBTN.enableButton(this._ibtnSave_Footer, intStep == 3);
		this._ctlMultiStep.showExplainLabel(intStep != 2);
		$R_FN.showElement(this._lblExplain_POLine, intStep == 2 && this._strSourceSelected == "PO");
		$R_FN.showElement(this._lblExplain_Service, intStep == 2 && this._strSourceSelected == "SERVICE");
		if (intStep == 2) { 
			if (this._strSourceSelected == "PO") {
				this._tblLines.resizeColumns();
				this.getPOLines();
			}
			if (this._strSourceSelected == "SERVICE") {
				this._ctlSelectService.resizeColumns();
				this._ctlSelectService.setFieldValue("ctlName", "%");
				this._intGlobalClientNo = (this._blnGlobalUser == true) ? this._intGlobalClientNo : null;
				this._ctlSelectService._GlobalClientNo = this._intGlobalClientNo;
				this._ctlSelectService.getData();
			}
			$R_FN.showElement(this._trSelectPOLine, this._strSourceSelected == "PO");
			$R_FN.showElement(this._trSelectService, this._strSourceSelected == "SERVICE");
			this.showField("ctlService", this._strSourceSelected == "SERVICE");
			this.showField("ctlServiceDescription", this._strSourceSelected == "SERVICE");
			this.showField("ctlPartNo", this._strSourceSelected == "PO");
			this.showField("ctlProduct", this._strSourceSelected == "PO");
			this.showField("ctlPackage", this._strSourceSelected == "PO");
			this.showField("ctlROHSStatus", this._strSourceSelected == "PO");
			this.showField("ctlManufacturer", this._strSourceSelected == "PO");
			this.showField("ctlSupplierPart", this._strSourceSelected == "PO");
			this.showField("ctlDateCode", this._strSourceSelected == "PO");
		}
	},

	getPOLines: function() {
	    this._tblLines.clearTable();
		$R_FN.showElement(this._pnlLines, true);
		var obj = new Rebound.GlobalTrader.Site.Data();
		obj.set_PathToData(this._strPathToData);
		obj.set_DataObject(this._strDataObject);
		obj.set_DataAction("GetPOLines");
		obj.addParameter("ID", this._intPurchaseOrderID);
		obj.addDataOK(Function.createDelegate(this, this.getPOLinesOK));
		obj.addError(Function.createDelegate(this, this.getPOLinesError));
		obj.addTimeout(Function.createDelegate(this, this.getPOLinesError));
		$R_DQ.addToQueue(obj);
		$R_DQ.processQueue();
		obj = null;
	},
	
	getPOLinesOK: function(args) {
		var result = args._result;
		if (result.Lines.length == 0) {
			$R_FN.showElement(this._pnlLines, false);
			$R_FN.showElement(this._pnlLinesNotAvailable, true);
		} else {
			for (var i = 0, l = result.Lines.length; i < l; i++) {
				var row = result.Lines[i];
				var aryData = [
					row.Part
					, row.Mfr
					, row.Product
					, row.QuantityOrdered
					, row.Price
					, row.ShipInCost
				];
				this._tblLines.addRow(aryData, row.ID, false);
				aryData = null; row = null;
			}
		}
	},

	getPOLinesError: function(args) {
		this._strErrorMessage = args._errorMessage;
		this.onSaveError();
	},
		
	selectPOLine: function() {
		this._intPurchaseOrderLineID = this._tblLines._varSelectedValue;
		this.fillData_POLine();
		this.nextStep(); 
	},
	
	selectService: function() {
		this._intServiceID = this._ctlSelectService.getSelectedID();
		this.fillData_Service();
		this.nextStep(); 
	},
	
	clearNewItemValues: function() {
		this.setFormFieldsToDefaults();
	},
	
	resetNewItemValues: function() {
		this.setFormFieldsToDefaults();
	},
	
	fillData: function() {
		switch (this._strSourceSelected) {
			case "PO": this.fillData_POLine(); break;
			case "SERVICE": this.fillData_Service(); break;
		}
	},
	
	fillDataError: function(args) {
		this._strErrorMessage = args._errorMessage;
		this.onSaveError();
	},
	
	fillData_POLine: function() {
		this.resetNewItemValues();
		var obj = new Rebound.GlobalTrader.Site.Data();
		obj.set_PathToData(this._strPathToData);
		obj.set_DataObject(this._strDataObject);
		obj.set_DataAction("GetPOLineForNew");
		obj.addParameter("id", this._intPurchaseOrderLineID);
		obj.addParameter("DebitCurrencyNo", this._intCurrencyNo);
		obj.addParameter("DebitDate", this._dtmDebitDate);
		obj.addDataOK(Function.createDelegate(this, this.fillData_POLineOK));
		obj.addError(Function.createDelegate(this, this.fillDataError));
		obj.addTimeout(Function.createDelegate(this, this.fillDataError));
		$R_DQ.addToQueue(obj);
		$R_DQ.processQueue();
		obj = null;
	},
	
	fillData_POLineOK: function(args) {
		var res = args._result;
		this.setFieldValue("ctlPartNo", res.Part);
		this.setFieldValue("ctlDateCode", res.DateCode);
		this.setFieldValue("ctlSupplierPart", res.SupplierPart);
		this.setFieldValue("ctlPrice", res.Price);
		this.setFieldValue("ctlQuantity", res.Quantity);
		this.setFieldValue("ctlManufacturer", res.MfrNo, null, res.Mfr);
		this.setFieldValue("ctlProduct", res.ProductNo, null, res.ProductDescription);
		//this.setFieldValue("ctlPackage", res.PackageNo);
        this.setFieldValue("ctlPackage", res.PackageNo, null, res.PackageDescription);
		this.setFieldValue("ctlROHSStatus", res.ROHS);
		this.getDropDownsData();
	},
	
	fillData_Service: function() {
		this.resetNewItemValues();
		var obj = new Rebound.GlobalTrader.Site.Data();
		obj.set_PathToData(this._strPathToData);
		obj.set_DataObject(this._strDataObject);
		obj.set_DataAction("GetServiceForNew");
		obj.addParameter("id", this._intServiceID);
		obj.addParameter("DebitCurrencyNo", this._intCurrencyNo);
		obj.addParameter("DebitDate", this._dtmDebitDate);
		obj.addDataOK(Function.createDelegate(this, this.fillData_ServiceOK));
		obj.addError(Function.createDelegate(this, this.fillDataError));
		obj.addTimeout(Function.createDelegate(this, this.fillDataError));
		$R_DQ.addToQueue(obj);
		$R_DQ.processQueue();
		obj = null;
	},
	
	fillData_ServiceOK: function(args) {
		var res = args._result;
		this.setFieldValue("ctlService", res.ServiceName);
		this.setFieldValue("ctlServiceDescription", res.ServiceDescription);
		this.setFieldValue("ctlPrice", res.Price);
		this.setFieldValue("ctlQuantity", 1);
		this.getDropDownsData();
	},
	
	getDropDownsData: function() {
	//	this.getFieldDropDownData("ctlProduct");
		//this.getFieldDropDownData("ctlPackage");
		this.getFieldDropDownData("ctlROHSStatus");
	},

	saveClicked: function() {
		this.resetFormFields();
		if (this.validateForm()) this.saveEdit();
	},
	
	validateForm: function() {
		var blnOK = true;
		if (this._strSourceSelected == "SERVICE") {
			if (!this.checkFieldEntered("ctlService")) blnOK = false;
		} else {
			if (!this.checkFieldEntered("ctlPartNo")) blnOK = false;
		}
		if (!this.checkFieldEntered("ctlQuantity")) blnOK = false;
		if (!this.checkFieldNumeric("ctlQuantity")) blnOK = false;
		if (!this.checkFieldEntered("ctlPrice")) blnOK = false;
		if (!this.checkFieldNumeric("ctlPrice")) blnOK = false;
		if (!blnOK) this.showError(true);
		return blnOK;
	},
	
	saveEdit: function() {
		var obj = new Rebound.GlobalTrader.Site.Data();
		obj.set_PathToData(this._strPathToData);
		obj.set_DataObject(this._strDataObject);
		obj.set_DataAction("AddNew");
		obj.addParameter("id", this._intDebitID);
		obj.addParameter("LineIsService", this._strSourceSelected == "SERVICE");
		if (this._strSourceSelected == "SERVICE") {
			obj.addParameter("ServiceNo", this._intServiceID);
			obj.addParameter("Service", this.getFieldValue("ctlService"));
			obj.addParameter("ServiceDescription", this.getFieldValue("ctlServiceDescription"));
		} else {
			obj.addParameter("PurchaseOrderLineNo", this._intPurchaseOrderLineID);
			obj.addParameter("Part", this.getFieldValue("ctlPartNo"));
			obj.addParameter("MfrNo", this.getFieldValue("ctlManufacturer"));
			obj.addParameter("DateCd", this.getFieldValue("ctlDateCode"));
			obj.addParameter("PackageNo", this.getFieldValue("ctlPackage"));
			obj.addParameter("ProductNo", this.getFieldValue("ctlProduct"));
			obj.addParameter("SupplierPart", this.getFieldValue("ctlSupplierPart"));
			obj.addParameter("ROHS", this.getFieldValue("ctlROHSStatus"));
		}
		obj.addParameter("SupplierRMALineNo", this._intSupplierRMALineID);
		obj.addParameter("Quantity", this.getFieldValue("ctlQuantity"));
		obj.addParameter("Price", this.getFieldValue("ctlPrice"));
		obj.addParameter("Taxable", this.getFieldValue("ctlTaxable"));
		obj.addParameter("LineNotes", this.getFieldValue("ctlLineNotes"));
		obj.addParameter("PrintHazWar", this.getFieldValue("ctlPrintHazWar"));
		obj.addDataOK(Function.createDelegate(this, this.saveEditOK));
		obj.addError(Function.createDelegate(this, this.saveEditError));
		obj.addTimeout(Function.createDelegate(this, this.saveEditError));
		$R_DQ.addToQueue(obj);
		$R_DQ.processQueue();
		obj = null;
	},
		
	saveEditError: function(args) {
		this._strErrorMessage = args._errorMessage;
		this.onSaveError();
	},
	
	saveEditOK: function(args) {
		if (args._result.Result == true) {
			this._intLineID = args._result.NewID;
			this.onSaveComplete();
		} else {
			this.saveEditError(args);
		}
	},
	productChange: function () {
	   // this.setFieldValue("ctlPrintHazWar", false);
	    //this.enableFieldCheckBox("ctlPrintHazWar", $find(this.getField("ctlProduct").ControlID)._aut._varSelectedExtraData);
        var isHazProduct = "";
        this.setFieldValue("ctlPrintHazWar", false);
        isHazProduct = $find(this.getField("ctlProduct").ControlID)._aut._varSelectedExtraData;
        if (typeof isHazProduct === "undefined") { }
        else {
            var nameArr = isHazProduct.split(':');
            var IsHaz = nameArr[0];
            this.enableFieldCheckBox("ctlPrintHazWar", Boolean.parse(IsHaz));
        }
	}

};

Rebound.GlobalTrader.Site.Controls.Forms.DebitLines_Add.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.DebitLines_Add", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
