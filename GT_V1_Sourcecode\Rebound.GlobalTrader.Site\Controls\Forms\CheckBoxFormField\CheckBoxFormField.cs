using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using System.Collections;

namespace Rebound.GlobalTrader.Site.Controls.Forms {
	[DefaultProperty("")]
	[ToolboxData("<{0}:CheckBoxFormField runat=server></{0}:CheckBoxFormField>")]
	public class CheckBoxFormField : TableRow, INamingContainer, IScriptControl {

		#region Locals

		protected TableCell _tdLeft;
		protected TableCell _tdRight;
		protected ImageCheckBox _chk;
		protected ScriptManager _sm;
		protected ScriptReference[] _srScriptReference = new ScriptReference[5];
		private int _intNumberOfScriptReferences = 0;
		protected ScriptControlDescriptor _scScriptControlDescriptor;

		#endregion

		#region Properties	

		#endregion

		#region Overrides

		/// <summary>
		/// OnInit
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
			EnsureChildControls();
			bool blnDebug = false;
#if (DEBUG)
			blnDebug = true;
#endif
			AddScriptReference(Functions.GetScriptReference(blnDebug, "Rebound.GlobalTrader.Site", "Controls.Forms.CheckBoxFormField.CheckBoxFormField.js", true));
			base.OnInit(e);
		}

		/// <summary>
		/// create controls
		/// </summary>
		protected override void CreateChildControls() {
			//left cell
			_tdLeft = new TableCell();
			_tdLeft.CssClass = "checkBoxLabel";
			Cells.Add(_tdLeft);

			//right cell
			_tdRight = new TableCell();
			_chk = new ImageCheckBox();
			_chk.ID = "chk";
			_chk.Enabled = true;

			_tdRight.Controls.Add(_chk);
			Cells.Add(_tdRight);

			base.CreateChildControls();
		}

		/// <summary>
		/// OnPreRender
		/// </summary>
		/// <param name="e"></param>
		protected override void OnPreRender(EventArgs e) {
			if (!this.DesignMode) {
				_sm = ScriptManager.GetCurrent(Page);
				if (_sm == null) throw new HttpException("A ScriptManager control must exist on the current page.");
				_sm.RegisterScriptControl(this);
			}
			base.OnPreRender(e);
		}

		/// <summary>
		/// Render
		/// </summary>
		/// <param name="writer"></param>
		protected override void Render(HtmlTextWriter writer) {
			if (!this.DesignMode) _sm.RegisterScriptDescriptors(this);
			base.Render(writer);
		}

		#endregion

		/// <summary>
		/// Add AJAX script reference
		/// </summary>
		/// <param name="sr"></param>
		protected void AddScriptReference(ScriptReference sr) {
			_srScriptReference[_intNumberOfScriptReferences] = sr;
			_intNumberOfScriptReferences += 1;
		}

		#region IScriptControl Members

		protected virtual IEnumerable<ScriptReference> GetScriptReferences() {
			return _srScriptReference;
		}

		protected virtual IEnumerable<ScriptDescriptor> GetScriptDescriptors() {
			if (_scScriptControlDescriptor == null) _scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.Forms.CheckBoxFormField", this.ClientID);
			_scScriptControlDescriptor.AddElementProperty("tdLeft", _tdLeft.ClientID);
			_scScriptControlDescriptor.AddComponentProperty("chk", _chk.ClientID);
			return new ScriptDescriptor[] { _scScriptControlDescriptor };
		}

		IEnumerable<ScriptReference> IScriptControl.GetScriptReferences() { return GetScriptReferences(); }
		IEnumerable<ScriptDescriptor> IScriptControl.GetScriptDescriptors() { return GetScriptDescriptors(); }

		#endregion

	}
}