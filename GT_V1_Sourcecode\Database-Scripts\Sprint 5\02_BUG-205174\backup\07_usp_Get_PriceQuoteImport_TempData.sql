﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

IF OBJECT_ID('dbo.usp_Get_PriceQuoteImport_TempData', 'P') IS NOT NULL
BEGIN
    DROP PROCEDURE dbo.usp_Get_PriceQuoteImport_TempData
END
GO

CREATE PROCEDURE [dbo].[usp_Get_PriceQuoteImport_TempData]                                                      
@DisplayLength int,                                                      
@DisplayStart int,                                                      
@SortCol int,                                                      
@SortDir nvarchar(10),                                                      
@Search nvarchar(255) = NULL,                                                    
@UserId INT,                                                  
@ClientId INT  ,                                              
@SelectedClientId INT      
 /*    
usp_Get_PriceQuoteImport_TempData   10,0,0,'asc','',4763,114,114       
*/    
                                                    
AS                                                      
BEGIN                                          
SET nocount off;                                                    
    Declare @FirstRec int, @LastRec int                                                      
    Set @FirstRec = @DisplayStart;                                                      
    Set @LastRec = @DisplayStart + @DisplayLength;                                                      
                                                         
    With CTE_Stock as                                                      
    (                                                      
         Select ROW_NUMBER() over (order by                                                      
                                                              
         case when (@SortCol = 0 and @SortDir='asc')                                                      
             then SelectedClientId                                                      
         end asc                                                     
   )                                                      
         as RowNum,                     
                   
    COUNT(*) over() as TotalCount,              
 /*          
  REPLACE(REQUIREMENTNo, '\', '\\')as REQUIREMENTNo                 
 ,REPLACE( ManufacturerName , '\', '\\')as ManufacturerName               
  ,REPLACE(Part, '\', '\\')as Part                 
  ,REPLACE(Quantity ,'\', '\\' )as Quantity             
  ,REPLACE(SupplierName ,'\', '\\' )as SupplierName              
  ,REPLACE(SupplierPart ,'\', '\\' )as SupplierPart             
  ,REPLACE(PackageName ,'\', '\\' )as PackageName                         
  ,REPLACE( DateCode ,'\', '\\' )as DateCode              
   ,REPLACE(SupplierCost ,'\', '\\' )as SupplierCost            
   ,REPLACE(CurrencyCode ,'\', '\\' )as CurrencyCode            
  ,REPLACE(SPQ ,'\', '\\' )as SPQ                        
  ,REPLACE(SupplierMOQ ,'\', '\\' )as SupplierMOQ                  
    ,REPLACE(Qty_in_Stock ,'\', '\\' )as Qty_in_Stock            
   ,REPLACE( LeadTime ,'\', '\\' )as LeadTime             
    ,REPLACE( OfferStatus ,'\', '\\' )as OfferStatus             
 ,REPLACE( ROHS ,'\', '\\' )as ROHS             
  ,REPLACE(FactorySealed ,'\', '\\' )as FactorySealed               
   ,REPLACE(Region ,'\', '\\' )as Region              
  ,REPLACE(DescriptionNotes ,'\', '\\' )as DescriptionNotes          
  */        
  /*    
  REPLACE(Column1, '\', '\\')as RequirementNo                 
 ,REPLACE( Column2 , '\', '\\')as ManufacturerName               
  ,REPLACE(Column3, '\', '\\')as Part                 
  ,REPLACE(Column4 ,'\', '\\' )as Quantity             
  ,REPLACE(Column5 ,'\', '\\' )as SupplierName              
  ,REPLACE(Column6 ,'\', '\\' )as SupplierPart             
  ,REPLACE(Column7 ,'\', '\\' )as PackageName                         
  ,REPLACE( Column8 ,'\', '\\' )as DateCode              
   ,REPLACE(Column9 ,'\', '\\' )as SupplierCost            
   ,REPLACE(Column10 ,'\', '\\' )as CurrencyCode            
  ,REPLACE(Column11 ,'\', '\\' )as SPQ        
  ,REPLACE(Column12 ,'\', '\\' )as SupplierMOQ                  
    ,REPLACE(Column13 ,'\', '\\' )as Qty_in_Stock            
  ,REPLACE( Column14 ,'\', '\\' )as LeadTime             
    ,REPLACE( Column15 ,'\', '\\' )as OfferStatus             
 ,REPLACE( Column16 ,'\', '\\' )as ROHS             
  ,REPLACE(Column17 ,'\', '\\' )as FactorySealed               
   ,REPLACE(Column18 ,'\', '\\' )as Region              
  ,REPLACE(Column19 ,'\', '\\' )as DescriptionNotes          
   ,REPLACE(Column20 ,'\', '\\' )as LastTimeBuy        
   ,REPLACE(Column21 ,'\', '\\' )as DeliveryDate        
   ,REPLACE(Column22 ,'\', '\\' )as SellPrice    
   */    
  REPLACE(Column1, '\', '\\')as RequirementNo                 
 ,REPLACE(Column2 , '\', '\\')as Supplier_Name               
 ,REPLACE(Column3, '\', '\\')as Supplier_Part_No                
 ,REPLACE(Column4 ,'\', '\\' )as Supplier_Cost    
 ,REPLACE(Column5 ,'\', '\\' )as Rohs   
 ,REPLACE(Column6 ,'\', '\\' )as Manufacturer              
 ,REPLACE(Column7 ,'\', '\\' )as DateCode             
 ,REPLACE(Column8 ,'\', '\\' )as PackageName                         
 ,REPLACE(Column9 ,'\', '\\' )as OfferedQty              
 ,REPLACE(Column10 ,'\', '\\' )as OfferStatus            
 ,REPLACE(Column11 ,'\', '\\' )as SPQ            
 ,REPLACE(Column12 ,'\', '\\' )as FactorySealed                        
 ,REPLACE(Column13 ,'\', '\\' )as  Qty_in_Stock               
 ,REPLACE(Column14 ,'\', '\\' )as MOQ            
 ,REPLACE(Column15 ,'\', '\\' )as Last_Time_Buy             
 ,REPLACE(Column16 ,'\', '\\' )as CurrencyCode             
 ,REPLACE(Column17 ,'\', '\\' )as BuyPrice             
 ,REPLACE(Column18 ,'\', '\\' )as SellPrice               
 ,REPLACE(Column19 ,'\', '\\' )as ShippingCost              
 ,REPLACE(Column20 ,'\', '\\' )as LeadTime          
 ,REPLACE(Column21 ,'\', '\\' )as Region        
 ,REPLACE(Column22 ,'\', '\\' )as DeliveryDate        
 ,REPLACE(Column23 ,'\', '\\' )as Notes
 ,REPLACE(Column24 ,'\', '\\' )as MSL 
 ,PriceQuoteImportId  ,ImportDate,ClientId,SelectedClientId , OriginalFilename                    
  FROM BorisGlobalTraderimports.dbo.tbPriceQuoteImport_tempData                                                      
  WHERE (ClientId = @ClientId and SelectedClientId=@SelectedClientId and CreatedBy = @UserId  ))                                                      
    SELECT RowNum,TotalCount,              
        RequirementNo as Column1,            
          Supplier_Name as Column2,                                               
          SUPPLIER_PART_NO as Column3,             
          Supplier_Cost  as Column4,    
    Rohs  as Column5,  
         Manufacturer as Column6  ,            
         DateCode as Column7 ,            
         PackageName as Column8,            
         OfferedQty as Column9,            
         OfferStatus as Column10,            
         SPQ as Column11,                
         FactorySealed as Column12,             
         Qty_in_Stock as Column13,             
         MOQ as Column14,            
         Last_Time_Buy as Column15,            
         CurrencyCode  as Column16,            
         BuyPrice as Column17,            
         SellPrice as Column18,            
         ShippingCost as Column19,            
         LeadTime as Column20 ,     
          Region as Column21,    
         DeliveryDate as Column22,    
         Notes as Column23,
		  MSL as Column24, 
 PriceQuoteImportId ,ImportDate,ClientId,SelectedClientId , OriginalFilename                                      
    from CTE_Stock                                                      
    where RowNum > @FirstRec and RowNum <= @LastRec                                                     
    ORDER BY ImportDate desc                           
END   
GO


