﻿
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.EXCELDocuments_Add = function(element) {
Rebound.GlobalTrader.Site.Controls.Forms.EXCELDocuments_Add.initializeBase(this, [element]);
    this._intSectionID = -1;
    this._ctlFileUpload = null;
    this._strSectionName = null;
    this._strExcel = null;
};

Rebound.GlobalTrader.Site.Controls.Forms.EXCELDocuments_Add.prototype = {

    initialize: function() {

        Rebound.GlobalTrader.Site.Controls.Forms.EXCELDocuments_Add.callBaseMethod(this, "initialize");
        this.addShown(Function.createDelegate(this, this.formShown));
    },

    dispose: function() {
        if (this.isDisposed) return;
        if (this._ctlFileUpload) this._ctlFileUpload.dispose();
        this._intSectionID = -1;
        this._ctlFileUpload = null;
        this._strSectionName = null;
        Rebound.GlobalTrader.Site.Controls.Forms.EXCELDocuments_Add.callBaseMethod(this, "dispose");
    },

    formShown: function() {
        if (this._blnFirstTimeShown) {

            this.addSave(Function.createDelegate(this, this.saveClicked));
            this._ctlFileUpload = $find(this.getField("ctlFile").ControlID);
            this._ctlFileUpload._intTimeoutSeconds = 60;
            this._ctlFileUpload._strSectionName = this._strSectionName;
            this._ctlFileUpload._strExcel = this._strExcel;

            this._ctlFileUpload.addUploadComplete(Function.createDelegate(this, this.uploadComplete));
            this._ctlFileUpload.addUploadFailed(Function.createDelegate(this, this.uploadFatalError));
            this._ctlFileUpload.addTimeout(Function.createDelegate(this, this.uploadFatalError));
            this._ctlFileUpload.addFileTooBig(Function.createDelegate(this, this.uploadError));
            this._ctlFileUpload.addFileNotAllowedType(Function.createDelegate(this, this.uploadError));
        }
        this.setFormFieldsToDefaults();
        this._ctlFileUpload.reset();
        this.showInnerContent(true);
    },

    saveClicked: function() {
        
        if (!this.validateForm()) return;
        this.showSaving(true);
        this._ctlFileUpload.doUpload();

    },

    uploadError: function() {
        this.showError(true);
        this.showInnerContent(true);
        this.setFieldInError("ctlFile", true, this._ctlFileUpload._strErrorMessage);
    },

    uploadFatalError: function() {
        this.showNuggetError(true, this._ctlFileUpload._strErrorMessage);
        this.showInnerContent(true);
        this._ctlFileUpload.reset();
    },

    uploadComplete: function() {
        this.saveEdit();
    },

    saveEdit: function() {
        
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/EXCELDocuments");
        obj.set_DataObject("EXCELDocuments");
        obj.set_DataAction("AddNew");
        obj._intTimeoutMilliseconds = 30000;
        obj.addParameter("id", this._intSectionID);
        obj.addParameter("Caption", this.getFieldValue("ctlCaption"));
        obj.addParameter("TempFile", this._ctlFileUpload._strUploadedFilename);
        obj.addParameter("Section", this._strSectionName);
        obj.addDataOK(Function.createDelegate(this, this.saveEditComplete));
        obj.addError(Function.createDelegate(this, this.saveEditError));
        obj.addTimeout(Function.createDelegate(this, this.saveEditError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    saveEditError: function(args) {
        this._strErrorMessage = args._errorMessage;
        this.showNuggetError(true, this._strErrorMessage);
        this.onSaveError();
    },

    saveEditComplete: function(args) {
        if (args._result.Result == true) {
            this.onSaveComplete();
        } else {
            if (args._result.Message) this._strErrorMessage = args._result.Message;
            this.onSaveError();
        }
    },

    validateForm: function() {
        this.onValidate();
        var blnOK = this.autoValidateFields();
        return blnOK;
    }

};

Rebound.GlobalTrader.Site.Controls.Forms.EXCELDocuments_Add.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.EXCELDocuments_Add", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
