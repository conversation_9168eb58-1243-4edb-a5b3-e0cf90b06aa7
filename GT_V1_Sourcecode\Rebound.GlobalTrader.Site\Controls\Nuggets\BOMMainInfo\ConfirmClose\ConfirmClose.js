Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.ConfirmClose=function(n){Rebound.GlobalTrader.Site.Controls.Forms.ConfirmClose.initializeBase(this,[n]);this._intBOMID=-1};Rebound.GlobalTrader.Site.Controls.Forms.ConfirmClose.prototype={get_intBOMID:function(){return this._intBOMID},set_intBOMID:function(n){this._intBOMID!==n&&(this._intBOMID=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Forms.ConfirmClose.callBaseMethod(this,"initialize");this.addShown(Function.createDelegate(this,this.formShown))},dispose:function(){this.isDisposed||(this._intBOMID=null,Rebound.GlobalTrader.Site.Controls.Forms.ConfirmClose.callBaseMethod(this,"dispose"))},formShown:function(){this._blnFirstTimeShown&&(this._ctlConfirm=this.getFieldComponent("ctlConfirmClose"),this._ctlConfirm.addClickYesEvent(Function.createDelegate(this,this.yesClicked)),this._ctlConfirm.addClickNoEvent(Function.createDelegate(this,this.noClicked)))},yesClicked:function(){this.showSaving(!0);var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("Controls/Nuggets/BOMMainInfo");n.set_DataObject("BOMMainInfo");n.set_DataAction("updateBOMStatusToClosed");n.addParameter("id",this._intBOMID);n.addDataOK(Function.createDelegate(this,this.saveConfirmComplete));n.addError(Function.createDelegate(this,this.saveError));n.addTimeout(Function.createDelegate(this,this.saveError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null;this.onSave()},noClicked:function(){this.showSaving(!1);this.onNotConfirmed()},saveError:function(n){this.showSaving(!1);this._strErrorMessage=n._errorMessage;this.onSaveError()},saveConfirmComplete:function(n){n._result.Result==!0?(this.showSavedOK(!0),this.onSaveComplete()):(this._strErrorMessage=n._errorMessage,this.onSaveError())}};Rebound.GlobalTrader.Site.Controls.Forms.ConfirmClose.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.ConfirmClose",Rebound.GlobalTrader.Site.Controls.Forms.Base,Sys.IDisposable);