using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site;
using Rebound.GlobalTrader.Site.Controls;

namespace Rebound.GlobalTrader.Site.Controls.Forms {
	public partial class CompanyAddresses_AddEdit : Base {

		#region Locals

		#endregion

		#region Properties

		/// <summary>
		/// CompanyID
		/// </summary>
		private int _intCompanyID;
		public int CompanyID {
			get { return _intCompanyID; }
			set { _intCompanyID = value; }
		}

		private int _intCompanyAddressID;
		public int CompanyAddressID {
			get { return _intCompanyAddressID; }
			set { _intCompanyAddressID = value; }
		}

		private string _strTitleEdit = "";
		public string TitleEdit {
			get { return _strTitleEdit; }
			set { _strTitleEdit = value; }
		}

		private string _strTitleAdd = "";
		public string TitleAdd {
			get { return _strTitleAdd; }
			set { _strTitleAdd = value; }
		}

		private string _strExplanationEdit = "";
		public string ExplanationEdit {
			get { return _strExplanationEdit; }
			set { _strExplanationEdit = value; }
		}

		private string _strExplanationAdd = "";
		public string ExplanationAdd {
			get { return _strExplanationAdd; }
			set { _strExplanationAdd = value; }
		}

		#endregion

		#region Overrides

		/// <summary>
		/// OnInit
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
			base.OnInit(e);
			_strTitleEdit = Functions.GetGlobalResource("FormTitles", "CompanyAddresses_Edit");
			_strTitleAdd = Functions.GetGlobalResource("FormTitles", "CompanyAddresses_Add");
			_strExplanationEdit = Functions.GetGlobalResource("FormExplanations", "CompanyAddresses_Edit");
			_strExplanationAdd = Functions.GetGlobalResource("FormExplanations", "CompanyAddresses_Add");
			TitleText = _strTitleAdd;
			AddScriptReference("Controls/Nuggets/CompanyAddresses/AddEdit/CompanyAddresses_AddEdit");
		}

		/// <summary>
		/// OnPreRender
		/// </summary>
		/// <param name="e"></param>
		protected override void OnPreRender(EventArgs e) {
			WireUpControls();
			SetupScriptDescriptors();
			base.OnPreRender(e);
		}

		#endregion

		private void SetupScriptDescriptors() {
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.Forms.CompanyAddresses_AddEdit", ctlDesignBase.ClientID);
			_scScriptControlDescriptor.AddProperty("intCompanyID", _intCompanyID);
			_scScriptControlDescriptor.AddProperty("intCompanyAddressID", _intCompanyAddressID);
			_scScriptControlDescriptor.AddProperty("strTitleAdd", _strTitleAdd);
			_scScriptControlDescriptor.AddProperty("strTitleEdit", _strTitleEdit);
			_scScriptControlDescriptor.AddProperty("strExplanationAdd", _strExplanationAdd);
			_scScriptControlDescriptor.AddProperty("strExplanationEdit", _strExplanationEdit);
			if (_ibtnSave != null) _scScriptControlDescriptor.AddElementProperty("ibtnSave", _ibtnSave.ClientID);
			if (_ibtnCancel != null) _scScriptControlDescriptor.AddElementProperty("ibtnCancel", _ibtnCancel.ClientID);
		}

		/// <summary>
		/// Wire up controls to the ascx
		/// </summary>
		private void WireUpControls() {
			_ibtnSave = FindIconButton("ibtnSave");
			_ibtnCancel = FindIconButton("ibtnCancel");
		}

	}
}
