//Marker     Changed by      Date         Remarks
//[001]      Soorya          03/03/2023   RP-1048 Remove AI code

//using Microsoft.ApplicationInsights;  //[001]
using System;
using System.Collections.Generic;
using System.Text;
using System.Web;

namespace Rebound.GlobalTrader.Site.Data.DropDowns {
	public class Base : Rebound.GlobalTrader.Site.Data.Base {

		protected DropDown _objDropDown;

       
		public override void ProcessRequest(HttpContext context) {
			if (base.init(context)) {
                try
                {
                    _objDropDown = _objSite.GetDropDown(GetFormValue_Int("DDID"));
               
                    int _intID = GetFormValue_Int("cLogIn");
                //Log out, if user login from diferent login in other tab
                //Comment the code due to logout from system
                if (GetFormValue_Int("DDID") != 90)
                {
                    if (_intID != SessionManager.LoginID)
                    {
                        WriteErrorNoSession();
                        //return false;
                    }
                }
               
                    switch (Action)
                    {
                        case "GetData": GetData(); break;
                        default: WriteErrorActionNotFound(); break;
                    }
                }
                catch(Exception ex)
                {

                    //var ai = new TelemetryClient(); // or re-use an existing instance

                    //ai.TrackTrace("Page URL:" + HttpContext.Current.Request.UrlReferrer + ",DropdownName: " + _objDropDown.Name);
                    //ai.TrackException(ex);
                    new Errorlog().LogMessage("Error at ProcessRequest in DataPageBase.ashx.cs : " + ex.Message);
                    WriteError(ex);

                }
            }
		}

		protected virtual void GetData() { }

		protected void SetDropDownType(string strName) {
			_objDropDown = _objSite.GetDropDown(strName);
		}

	}
}
