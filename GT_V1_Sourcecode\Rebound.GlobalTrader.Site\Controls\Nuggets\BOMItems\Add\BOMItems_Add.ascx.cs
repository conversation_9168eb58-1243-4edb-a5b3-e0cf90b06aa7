//---------------------------------------------------------------------------------------------------------
// RP 04.12.2009:
// - allow sources for new lines to be set on permissions
//---------------------------------------------------------------------------------------------------------
using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site;
using Rebound.GlobalTrader.Site.Controls;
using System.Collections.Generic;

namespace Rebound.GlobalTrader.Site.Controls.Forms {
    public partial class BOMItems_Add : Base
    {

        #region Locals

        protected TableRow _trSourceFromRequirement;
        protected ItemSearch.RequiredBomItem _ctlReqsWithBOM;

        #endregion

        #region Properties

        private int _intQuoteID = -1;
        public int QuoteID {
            get { return _intQuoteID; }
            set { _intQuoteID = value; }
        }

        private bool _blnCanAddNewLine = true;
        public bool CanAddNewLine {
            get { return _blnCanAddNewLine; }
            set { _blnCanAddNewLine = value; }
        }

       
        #endregion

        #region Overrides

        /// <summary>
        /// OnInit
        /// </summary>
        /// <param name="e"></param>
        protected override void OnInit(EventArgs e) {
            base.OnInit(e);
            TitleText = Functions.GetGlobalResource("FormTitles", "BOMItems_Add");
            AddScriptReference("Controls.Nuggets.BOMItems.Add.BOMItems_Add.js");
           // if (_objQSManager.QuoteID > 0) _intQuoteID = _objQSManager.QuoteID;
            WireUpControls();
        }

        /// <summary>
        /// OnPreRender
        /// </summary>
        /// <param name="e"></param>
        protected override void OnPreRender(EventArgs e) {
            //WireUpButtons();
            //SetupSelectSourceScreen();
            SetupScriptDescriptors();
            base.OnPreRender(e);
        }

        #endregion

       

        /// <summary>
        /// Wire up controls to the ascx
        /// </summary>
        private void WireUpControls() {
            _ctlReqsWithBOM = (ItemSearch.RequiredBomItem)FindContentControl("ctlReqsWithBOM");
        }

        /// <summary>
        /// Setup Script Descriptors
        /// </summary>
        private void SetupScriptDescriptors() {
            _scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.Forms.BOMItems_Add", ctlDesignBase.ClientID);
          //  _scScriptControlDescriptor.AddProperty("intPOQuoteID", _objQSManager.POQuoteID);
            _scScriptControlDescriptor.AddComponentProperty("ctlReqsWithBOM", _ctlReqsWithBOM.ctlDesignBase.ClientID);
        }
    }
}
