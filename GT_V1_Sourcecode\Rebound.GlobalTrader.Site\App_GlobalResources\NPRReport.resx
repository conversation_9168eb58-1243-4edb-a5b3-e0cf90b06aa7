﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="ActionRequired" xml:space="preserve">
    <value>4. Action Required (Sales to complete). Please complete either 4a,4b,4c or 4d</value>
  </data>
  <data name="AdviceNote" xml:space="preserve">
    <value>Advice Note</value>
  </data>
  <data name="Allcustomers" xml:space="preserve">
    <value>All customers unauthorized return should be recorded in Section 3: Non All customers unauthorized return should be recorded in Section 3: Non Conformance details.</value>
  </data>
  <data name="Authoriseby" xml:space="preserve">
    <value>Authorise by/signature</value>
  </data>
  <data name="AuthoriseName" xml:space="preserve">
    <value>Authorise Name</value>
  </data>
  <data name="Closure" xml:space="preserve">
    <value>6. Closure (Logistics)</value>
  </data>
  <data name="Comments" xml:space="preserve">
    <value>Comments</value>
  </data>
  <data name="CompletedBy" xml:space="preserve">
    <value>Completed By</value>
  </data>
  <data name="Customer" xml:space="preserve">
    <value>Customer</value>
  </data>
  <data name="CustomerRMA" xml:space="preserve">
    <value>Customer RMA</value>
  </data>
  <data name="Date" xml:space="preserve">
    <value>Date</value>
  </data>
  <data name="Dateraised" xml:space="preserve">
    <value>Date raised:</value>
  </data>
  <data name="Debitnoteno" xml:space="preserve">
    <value>Debit note no:</value>
  </data>
  <data name="Incurredcosttosales" xml:space="preserve">
    <value>Incurred cost to sales. Yes/No</value>
  </data>
  <data name="MoveintoStock" xml:space="preserve">
    <value>4c: Move into Stock</value>
  </data>
  <data name="NonConformance" xml:space="preserve">
    <value>3. Non Conformance Details. Reason for rejection (Warehouse to complete)</value>
  </data>
  <data name="NPR" xml:space="preserve">
    <value>Nonconforming Product Report</value>
  </data>
  <data name="NPRNo" xml:space="preserve">
    <value>NPR No.</value>
  </data>
  <data name="OrderDetails" xml:space="preserve">
    <value>2. Order Details (Warehouse to complete)</value>
  </data>
  <data name="Originator" xml:space="preserve">
    <value>1. Originator (Warehouse to complete)</value>
  </data>
  <data name="OutworkerName" xml:space="preserve">
    <value>Outworker Name</value>
  </data>
  <data name="OutworkerPO" xml:space="preserve">
    <value>Outworker P/o No</value>
  </data>
  <data name="PartNo" xml:space="preserve">
    <value>Part No</value>
  </data>
  <data name="PONO" xml:space="preserve">
    <value>PO NO</value>
  </data>
  <data name="QLocation" xml:space="preserve">
    <value>Q Location</value>
  </data>
  <data name="RaisedBy" xml:space="preserve">
    <value>Raised By</value>
  </data>
  <data name="RejectedQty" xml:space="preserve">
    <value>Rejected Qty</value>
  </data>
  <data name="Requiredoutwork" xml:space="preserve">
    <value>4d: Required outwork</value>
  </data>
  <data name="Returntosupplier" xml:space="preserve">
    <value>4a : Return to supplier</value>
  </data>
  <data name="Salesdetail" xml:space="preserve">
    <value>5. Sales detail &amp; Authorisation</value>
  </data>
  <data name="SalesPerson" xml:space="preserve">
    <value>Sales Person</value>
  </data>
  <data name="Scrap" xml:space="preserve">
    <value>4b : Scrap</value>
  </data>
  <data name="SONo" xml:space="preserve">
    <value>SO No</value>
  </data>
  <data name="SRMANo" xml:space="preserve">
    <value>SRMA No</value>
  </data>
  <data name="Stocklocation" xml:space="preserve">
    <value>Stock location. 
(EPO/Consignment department only)</value>
  </data>
  <data name="Supplier" xml:space="preserve">
    <value>Supplier</value>
  </data>
  <data name="SupplierReference" xml:space="preserve">
    <value>Supplier Reference</value>
  </data>
  <data name="SupplierRMA" xml:space="preserve">
    <value>Supplier RMA</value>
  </data>
  <data name="SupplierRMANo" xml:space="preserve">
    <value>Supplier RMA No</value>
  </data>
  <data name="SupplierShipVia" xml:space="preserve">
    <value>Supplier Ship Via</value>
  </data>
  <data name="SupplierShipviaacno" xml:space="preserve">
    <value>Supplier Ship via a/c no:</value>
  </data>
  <data name="Suppliertocredit" xml:space="preserve">
    <value>Supplier to credit</value>
  </data>
  <data name="TotalRejectedvalue" xml:space="preserve">
    <value>Total Rejected value</value>
  </data>
  <data name="UnitCost" xml:space="preserve">
    <value>Unit Cost</value>
  </data>
</root>