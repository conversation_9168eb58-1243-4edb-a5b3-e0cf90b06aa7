<%--
Marker     Changed by      Date         Remarks
[001]      Vinay           30/07/2012   Add compulsory incoterms field when create Credit and debit note. :ESMS No:- 105
[002]      Umendra         21/01/2019   Adding View Tree Button
--%>
<%@ Control Language="C#" CodeBehind="DebitMainInfo.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Nuggets.DebitMainInfo" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>

<ReboundUI_Nugget:DesignBase ID="ctlDB" runat="server" TitleText="Main Information" BoxType="Standard">

	<Links>
		<ReboundUI:IconButton ID="ibtnEdit" runat="server" IconButtonMode="hyperlink" IconGroup="Nugget" IconTitleResource="Edit" IconCSSType="Edit" />
        <ReboundUI:IconButton ID="ibtnExport" runat="server" IconButtonMode="hyperlink" IconGroup="Nugget" IconTitleResource="Export" IconCSSType="Export" />
		<ReboundUI:IconButton ID="ibtnRelease" runat="server" IconButtonMode="hyperlink" IconGroup="Nugget" IconTitleResource="Release" IconCSSType="Release" />
	    <%--[002] code Start--%>
        <ReboundUI:IconButton ID="ibtnViewTree" runat="server" IconButtonMode="hyperlink" IconGroup="Nugget" IconTitleResource="ViewTree" IconCSSType="Add" />
    	<%--[002] code End--%>
    </Links>
	
	<Content>
		<table class="twoCols">
			<tr>
				<td class="col1">
					<table cellpadding="0" cellspacing="0" border="0" class="dataItems">
						<ReboundUI:DataItemRow id="ctlSupplier" runat="server" ResourceTitle="Supplier" />
						<ReboundUI:DataItemRow id="ctlContact" runat="server" ResourceTitle="Contact" />
						<ReboundUI:DataItemRow id="ctlDivision" runat="server" ResourceTitle="Division" />
						<ReboundUI:DataItemRow id="ctlBuyer" runat="server" ResourceTitle="Buyer" />
						<ReboundUI:DataItemRow id="ctlRaiser" runat="server" ResourceTitle="RaisedBy" />

						<ReboundUI:DataItemRow id="ctlSep1" runat="server" FieldType="SeparatorWithLine" />
						<ReboundUI:DataItemRow id="ctlDebitDate" runat="server" ResourceTitle="DebitDate" />
						<ReboundUI:DataItemRow id="ctlReferenceDate" runat="server" ResourceTitle="ReferenceDate" />

						<ReboundUI:DataItemRow id="ctlSep3" runat="server" FieldType="SeparatorWithLine" />
						<ReboundUI:DataItemRow id="ctlSupplierInvoice" runat="server" ResourceTitle="SupplierInvoice" />
						<ReboundUI:DataItemRow id="ctlSupplierReturn" runat="server" ResourceTitle="SupplierReturn" />
						<ReboundUI:DataItemRow id="ctlSupplierCredit" runat="server" ResourceTitle="SupplierCredit" />

						<ReboundUI:DataItemRow id="hidDebitNumber" runat="server" FieldType="Hidden" />
						<ReboundUI:DataItemRow id="hidPO" runat="server" FieldType="Hidden" />
						<ReboundUI:DataItemRow id="hidPONo" runat="server" FieldType="Hidden" />
						<ReboundUI:DataItemRow id="hidSupplierName" runat="server" FieldType="Hidden" />
						<ReboundUI:DataItemRow id="hidCurrencyCode" runat="server" FieldType="Hidden" />
						<ReboundUI:DataItemRow id="hidCurrencyNo" runat="server" FieldType="Hidden" />
						<ReboundUI:DataItemRow id="hidFreight" runat="server" FieldType="Hidden" />
						<ReboundUI:DataItemRow id="hidSRMA" runat="server" FieldType="Hidden" />
						<ReboundUI:DataItemRow id="hidSRMANo" runat="server" FieldType="Hidden" />
						<ReboundUI:DataItemRow id="hidRaisedByNo" runat="server" FieldType="Hidden" />
						<ReboundUI:DataItemRow id="hidBuyerNo" runat="server" FieldType="Hidden" />
						<ReboundUI:DataItemRow id="hidTaxNo" runat="server" FieldType="Hidden" />
						<ReboundUI:DataItemRow id="hidContactName" runat="server" FieldType="Hidden" />
						<ReboundUI:DataItemRow id="hidDivisionNo" runat="server" FieldType="Hidden" />
					</table>
				</td>
				<td class="col2">
					<table cellpadding="0" cellspacing="0" border="0" class="dataItems">
						<ReboundUI:DataItemRow id="ctlPurchaseOrder" runat="server" ResourceTitle="PurchaseOrderNo" />
						<ReboundUI:DataItemRow id="ctlInternalPurchaseOrder" runat="server" ResourceTitle="InternalPurchaseOrderNo" />
						<ReboundUI:DataItemRow id="ctlSupplierRMA" runat="server" ResourceTitle="SupplierRMANo" />

						<ReboundUI:DataItemRow id="ctlSep4" runat="server" FieldType="SeparatorWithLine" />
						<ReboundUI:DataItemRow id="ctlTax" runat="server" ResourceTitle="Tax" />
						<ReboundUI:DataItemRow id="ctlCurrency" runat="server" ResourceTitle="Currency" />
						<ReboundUI:DataItemRow id="ctlFreight" runat="server" ResourceTitle="Freight" />

						<ReboundUI:DataItemRow id="ctlSep5" runat="server" FieldType="SeparatorWithLine" />
						<ReboundUI:DataItemRow id="ctlNotes" runat="server" ResourceTitle="SupplierNotes" />
						<ReboundUI:DataItemRow id="ctlInstructions" runat="server" ResourceTitle="Instructions" />
						<%--[001] code start--%>
						<ReboundUI:DataItemRow id="ctlIncotermName" runat="server" ResourceTitle="Incoterm" />
						<ReboundUI:DataItemRow id="hidIncotermNo" runat="server" FieldType="Hidden" />
						<%--[001] code end--%>
						<ReboundUI:DataItemRow id="ctlRefNo" runat="server" ResourceTitle="RefNo" />
                        <ReboundUI:DataItemRow id="ctlLockUpdateClient" runat="server" ResourceTitle="LockUpdateClient" FieldType="CheckBox" />
						<ReboundUI:DataItemRow id="ctlExported" runat="server" ResourceTitle="Exported?" FieldType="CheckBox" />
						<ReboundUI:DataItemRow id="ctlExportedDate" runat="server" ResourceTitle="ExportedDate" />
						<ReboundUI:DataItemRow id="ctlApprovedforExport" runat="server" ResourceTitle="ApprovedforExport" FieldType="CheckBox"/>
						<ReboundUI:DataItemRow id="ctlURNnumber" runat="server" ResourceTitle="URNNumber" />
                        <ReboundUI:DataItemRow id="hidGlobalClientNo" runat="server" FieldType="Hidden" />
					</table>
				</td>
			</tr>
		</table>
	</Content>
	
	<Forms>
		<ReboundForm:DebitMainInfo_Edit ID="ctlDebitEdit" runat="server" />
        <ReboundForm:DebitMainInfo_Export ID="frmDebitExport" runat="server" />
	</Forms>

</ReboundUI_Nugget:DesignBase>
