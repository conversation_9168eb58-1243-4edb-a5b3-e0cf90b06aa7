Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.DebitMainInfo_Edit=function(n){Rebound.GlobalTrader.Site.Controls.Forms.DebitMainInfo_Edit.initializeBase(this,[n]);this._intDebitID=-1;this._hidRaisedByNo=0;this._IsPOHub=!1;this._RaisedBy=-1;this._intGlobalClientNo=-1};Rebound.GlobalTrader.Site.Controls.Forms.DebitMainInfo_Edit.prototype={get_intDebitID:function(){return this._intDebitID},set_intDebitID:function(n){this._intDebitID!==n&&(this._intDebitID=n)},get_lblCurrency_Freight:function(){return this._lblCurrency_Freight},set_lblCurrency_Freight:function(n){this._lblCurrency_Freight!==n&&(this._lblCurrency_Freight=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Forms.DebitMainInfo_Edit.callBaseMethod(this,"initialize");this.addShown(Function.createDelegate(this,this.formShown))},formShown:function(){this._blnFirstTimeShown&&(this.addSave(Function.createDelegate(this,this.saveClicked)),$find(this.getField("ctlCurrency").ControlID).addChanged(Function.createDelegate(this,this.currencyChanged)));this.getFieldControl("ctlDivision")._intGlobalLoginClientNo=this._intGlobalClientNo;this.getFieldDropDownData("ctlDivision");this.getFieldControl("ctlBuyer")._intGlobalLoginClientNo=this._intGlobalClientNo;this.getFieldDropDownData("ctlBuyer");this.getFieldControl("ctlRaisedBy")._intGlobalLoginClientNo=this._intGlobalClientNo;this.getFieldDropDownData("ctlRaisedBy");this.getFieldControl("ctlTax")._intGlobalLoginClientNo=this._intGlobalClientNo;this.getFieldDropDownData("ctlTax");this.getFieldControl("ctlCurrency")._intGlobalLoginClientNo=this._intGlobalClientNo;this.getFieldDropDownData("ctlCurrency");this.getFieldDropDownData("ctlIncoterm")},dispose:function(){this.isDisposed||(this._intDebitID=null,this._lblCurrency_Freight=null,Rebound.GlobalTrader.Site.Controls.Forms.DebitMainInfo_Edit.callBaseMethod(this,"dispose"))},saveClicked:function(){var t,r,i,n;if(this._RaisedBy=this.getFieldValue("ctlRaisedBy"),this._RaisedBy<=0&&(this._RaisedBy=this._hidRaisedByNo),!this._IsPOHub)for(t=0,r=this._aryFields.length;t<r;t++)i=this._aryFields[t],i.ControlID=="ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlDebitEdit_ctlDB_ctlRaisedBy_ctl04_ddlRaisedBy"&&(i.Required="false");this.validateForm()&&(n=new Rebound.GlobalTrader.Site.Data,n.set_PathToData("controls/Nuggets/DebitMainInfo"),n.set_DataObject("DebitMainInfo"),n.set_DataAction("SaveEdit"),n.addParameter("id",this._intDebitID),n.addParameter("SupplierInvoice",this.getFieldValue("ctlSupplierInvoice")),n.addParameter("SupplierReturn",this.getFieldValue("ctlSupplierReturn")),n.addParameter("SupplierCredit",this.getFieldValue("ctlSupplierCredit")),n.addParameter("Freight",this.getFieldValue("ctlFreight")),n.addParameter("Notes",this.getFieldValue("ctlNotes")),n.addParameter("Instructions",this.getFieldValue("ctlInstructions")),n.addParameter("DivisionNo",this.getFieldValue("ctlDivision")),n.addParameter("Buyer",this.getFieldValue("ctlBuyer")),n.addParameter("RaisedBy",this._IsPOHub==!1?this._hidRaisedByNo:this.getFieldValue("ctlRaisedBy")),n.addParameter("DebitDate",this.getFieldValue("ctlDebitDate")),n.addParameter("ReferenceDate",this.getFieldValue("ctlReferenceDate")),n.addParameter("TaxNo",this.getFieldValue("ctlTax")),n.addParameter("CurrencyNo",this.getFieldValue("ctlCurrency")),n.addParameter("IncotermNo",this.getFieldValue("ctlIncoterm")),n.addParameter("URNNumber",this.getFieldValue("ctlURNNumber")),n.addParameter("LockUpdateClient",this.getFieldValue("ctlLockUpdateClient")),n.addDataOK(Function.createDelegate(this,this.saveEditComplete)),n.addError(Function.createDelegate(this,this.saveEditError)),n.addTimeout(Function.createDelegate(this,this.saveEditError)),$R_DQ.addToQueue(n),$R_DQ.processQueue(),n=null)},saveEditError:function(n){this._strErrorMessage=n._errorMessage;this.onSaveError()},saveEditComplete:function(n){n._result.Result==!0?this.onSaveComplete():(this._strErrorMessage=n._errorMessage,this.onSaveError())},validateForm:function(){this.onValidate();return this.autoValidateFields()},setCurrency:function(n){$R_FN.setInnerHTML(this._lblCurrency_Freight,n)},currencyChanged:function(){this.setCurrency(this.getFieldDropDownExtraText("ctlCurrency"))}};Rebound.GlobalTrader.Site.Controls.Forms.DebitMainInfo_Edit.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.DebitMainInfo_Edit",Rebound.GlobalTrader.Site.Controls.Forms.Base,Sys.IDisposable);