﻿
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

/*
--===============================================================================================================================================
TASK			UPDATED BY       DATE				ACTION		DESCRIPTION  
[US-236903]     Phuc Hoang		 25-Mar-2025		CREATE		The system doesn't keep selected client when switched from v2 to v1 or vice versa
=================================================================================================================================================
*/

CREATE OR ALTER PROCEDURE [dbo].[usp_login_byMasterLogin]
   @AdLoginName VARCHAR(150)
  , @IPAddress nvarchar(15)      
  , @SessionId nvarchar(20)   
  , @ServerIP nvarchar(15) = null    
  , @Result int OUTPUT    
  , @LoginName varchar(100)='' OUTPUT  
AS
BEGIN
DECLARE @LoginId int ;  
		--clear old sessions      
			EXEC usp_update_Session_ClearOldSessions      
            
			set @LoginName ='NotFoundAnyUser'
			 -- check if loginName is valid      
    IF EXISTS ( SELECT MasterLoginId from tbMasterLogin WHERE ADLoginName = @AdLoginName and isnull(inactive,0)=0)       
        BEGIN      
  -- get loginName     
            DECLARE @MasterLoginNo INT  
			DECLARE @DefaultClientNo INT  
			DECLARE @LastClientNo int  

			SELECT top 1  @MasterLoginNo = MasterLoginId
			FROM tbMasterLogin ml
		    WHERE ADLoginName = @AdLoginName

		    SELECT top 1 @DefaultClientNo = LastClientNo,@LastClientNo = LastClientNo 
			FROM tbMasterLogin ml
			INNER JOIN tbClient cli ON ml.LastClientNo=cli.ClientId 
		    WHERE ADLoginName = @AdLoginName and ISNULL(cli.Inactive, 0)=0
		    
			IF ISNULL(@DefaultClientNo, 0) = 0 And @MasterLoginNo is not null  
		    BEGIN  
				
				SELECT top 1 @LastClientNo = ClientNo,@DefaultClientNo = ClientNo FROM tbLogin lg 
				INNER JOIN tbClient cli ON lg.ClientNo=cli.ClientId 
				WHERE MasterLoginNo=@MasterLoginNo and LoginId<>0 and ISNULL(cli.Inactive, 0)=0 order by ClientNo asc  
				UPDATE tbMasterLogin SET LastClientNo=@LastClientNo where MasterLoginId=@MasterLoginNo  
		    END   
			
			SELECT top 1 @LoginId = LoginId   , @LoginName = LoginName   
                            FROM    dbo.tbLogin     
                            WHERE   MasterLoginNo = @MasterLoginNo and ClientNo= @DefaultClientNo 

							if @LoginId is null
							begin
							  SET @Result = 99      
                               RETURN 99   
							end
     
        END      
     ELSE       
        BEGIN      
            SET @Result = 99      
            RETURN 99      
        END

		 IF (SELECT  count(*)      
        FROM    dbo.tbSession      
        WHERE   LoginNo = @LoginId      
       ) > 0       
        BEGIN      
            UPDATE  dbo.tbSession      
            SET     SessionTimestamp = CURRENT_TIMESTAMP      
                  , SessionName = @SessionId      
                  , IPAddress = @IPAddress     
                  , ServerIP = @ServerIP   
            WHERE   LoginNo = @LoginId       
        END      
    ELSE       
        BEGIN      
            INSERT  INTO dbo.tbSession      
                    (LoginNo      
                   , SessionName      
                   , SessionTimestamp      
                   , StartTime      
                   , IPAddress  
                   , ServerIP)      
            VALUES  (      
                     @LoginId      
                   , @SessionId      
                   , CURRENT_TIMESTAMP      
                   , CURRENT_TIMESTAMP      
                   , @IPAddress  
                   , @ServerIP)        
        END   
		SET @Result = 0      
    RETURN @Result   
END


GO


