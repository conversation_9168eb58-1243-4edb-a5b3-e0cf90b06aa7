Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.HomeNuggets");Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyOpenQuotes=function(n){Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyOpenQuotes.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyOpenQuotes.prototype={get_pnlOpen:function(){return this._pnlOpen},set_pnlOpen:function(n){this._pnlOpen!==n&&(this._pnlOpen=n)},get_pnlRecent:function(){return this._pnlRecent},set_pnlRecent:function(n){this._pnlRecent!==n&&(this._pnlRecent=n)},get_tblOpen:function(){return this._tblOpen},set_tblOpen:function(n){this._tblOpen!==n&&(this._tblOpen=n)},get_tblRecent:function(){return this._tblRecent},set_tblRecent:function(n){this._tblRecent!==n&&(this._tblRecent=n)},get_pnlMore:function(){return this._pnlMore},set_pnlMore:function(n){this._pnlMore!==n&&(this._pnlMore=n)},get_lnkMore:function(){return this._lnkMore},set_lnkMore:function(n){this._lnkMore!==n&&(this._lnkMore=n)},get_myLoginID:function(){return this.myLoginID},set_myLoginID:function(n){this.myLoginID!==n&&(this.myLoginID=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyOpenQuotes.callBaseMethod(this,"initialize");this.addRefreshEvent(Function.createDelegate(this,this.getData))},dispose:function(){this.isDisposed||(this._tblOpen&&this._tblOpen.dispose(),this._tblRecent&&this._tblRecent.dispose(),this._pnlOpen=null,this._pnlRecent=null,this._tblOpen=null,this._tblRecent=null,this._pnlMore=null,Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyOpenQuotes.callBaseMethod(this,"dispose"))},setupLoadingState:function(){$R_FN.showElement(this._pnlMore,!1);$R_FN.showElement(this._pnlOpen,!1);$R_FN.showElement(this._pnlRecent,!1);Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyOpenQuotes.callBaseMethod(this,"setupLoadingState")},showNoData:function(n){this.showContent(!0);$R_FN.showElement(this._pnlNoData,n)},getData:function(){this.setupLoadingState();this._lnkMore.href=this._intLoginID_Other>0?$RGT_gotoURL_QuoteBrowse(this._intLoginID_Other):$RGT_gotoURL_QuoteBrowse(this.myLoginID);var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/HomeNuggets/MyOpenQuotes");n.set_DataObject("MyOpenQuotes");n.set_DataAction("GetData");n.addParameter("rowcount",this._intRowCount);n.addParameter("OtherLoginID",this._intLoginID_Other);n.addDataOK(Function.createDelegate(this,this.getDataComplete));n.addError(Function.createDelegate(this,this.homeNuggetDataError));n.addTimeout(Function.createDelegate(this,this.homeNuggetDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getDataComplete:function(n){var r,u,t,f,i;for(this.showNoData(n._result.Count==0),$R_FN.showElement(this._pnlMore,!0),r=n._result,this._tblOpen.clearTable(),f="",i=0;i<r.OpenQ.length;i++)t=r.OpenQ[i],u=[$RGT_nubButton_QuoteImp(t.ID,t.No,t.IsImportant),$RGT_nubButton_Company(t.CMNo,t.CM),t.Quote],f=t.IsImportant==!0?"yellow-background":"",this._tblOpen.addRowRowColor(u,t.ID,!1,null,null,t.IsImportant,f);for($R_FN.showElement(this._pnlOpen,r.OpenQ.length>0),this._tblRecent.clearTable(),i=0;i<r.RecentQ.length;i++)t=r.RecentQ[i],u=[$RGT_nubButton_Quote(t.ID,t.No),$RGT_nubButton_Company(t.CMNo,t.CM),t.Quote],this._tblRecent.addRow(u,null);$R_FN.showElement(this._pnlRecent,r.RecentQ.length>0);this.hideLoading()}};Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyOpenQuotes.registerClass("Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyOpenQuotes",Rebound.GlobalTrader.Site.Controls.HomeNuggets.Base,Sys.IDisposable);