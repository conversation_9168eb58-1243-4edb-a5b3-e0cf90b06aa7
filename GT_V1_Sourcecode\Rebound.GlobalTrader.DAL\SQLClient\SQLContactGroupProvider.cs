﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Data.SqlClient;

namespace Rebound.GlobalTrader.DAL.SqlClient
{
    public class SqlContactGroupProvider : ContactGroupProvider
    {
        public override List<ContactGroup> GetDataByNameOrCode(string nameSearch, string codeSearch, string codeType)
        {
            var contactGroups = new List<ContactGroup>();

            using (var cn = new SqlConnection(ConnectionString))
            using (var cmd = new SqlCommand("usp_nugget_GetDetailsByNameOrCodeCompany", cn))
            {
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;
                cmd.Parameters.Add("@NameSearch", SqlDbType.NVarChar).Value = nameSearch ?? (object)DBNull.Value;
                cmd.Parameters.Add("@CodeSearch", SqlDbType.NVarChar).Value = codeSearch ?? (object)DBNull.Value;
                cmd.Parameters.Add("@CodeType", SqlDbType.NVarChar).Value = codeType ?? (object)DBNull.Value;

                try
                {
                    cn.Open();
                    using (var reader = cmd.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            var contactGroup = new ContactGroup
                            {
                                Id = reader.GetInt32(reader.GetOrdinal("Id")),
                                ContactName = reader.GetString(reader.GetOrdinal("Name")),
                                Code = reader.GetString(reader.GetOrdinal("Code")),
                                Inactive = GetReaderValue_Int32(reader, "Inactive", 0) == 0 ? "No" : "Yes"
                            };
                            contactGroups.Add(contactGroup);
                        }
                    }
                }
                catch (SqlException sqlex)
                {
                    throw new Exception("Failed to get contact groups", sqlex);
                }
            }

            return contactGroups;
        }

        public override int SaveGroupData(string groupName, string groupCode, string grpType, int updatedBy)
        {
            using (var cn = new SqlConnection(ConnectionString))
            using (var cmd = new SqlCommand("usp_GroupCodeContactGroup_Insert", cn))
            {
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;
                cmd.Parameters.Add("@ContactName", SqlDbType.NVarChar).Value = groupName ?? (object)DBNull.Value;
                cmd.Parameters.Add("@Code", SqlDbType.NVarChar).Value = groupCode ?? (object)DBNull.Value;
                cmd.Parameters.Add("@ContactGroupType", SqlDbType.NVarChar).Value = grpType ?? (object)DBNull.Value;
                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;

                try
                {
                    cn.Open();
                    cmd.ExecuteNonQuery();
                    return (int)cmd.Parameters["@RowsAffected"].Value;
                }
                catch (SqlException sqlex)
                {
                    throw new Exception("Failed to insert group data", sqlex);
                }
            }

        }
        public override int EditGroupData(int groupId, string groupName, string groupCode, string grpType, int updatedBy)
        {
            using (var cn = new SqlConnection(ConnectionString))
            using (var cmd = new SqlCommand("usp_GroupCodeContactGroup_Edit", cn))
            {
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;
                cmd.Parameters.Add("@ItemId", SqlDbType.Int).Value = groupId;
                cmd.Parameters.Add("@ContactName", SqlDbType.NVarChar).Value = groupName ?? (object)DBNull.Value;
                cmd.Parameters.Add("@Code", SqlDbType.NVarChar).Value = groupCode ?? (object)DBNull.Value;
                cmd.Parameters.Add("@ContactGroupType", SqlDbType.NVarChar).Value = grpType ?? (object)DBNull.Value;
                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;

                try
                {
                    cn.Open();
                    cmd.ExecuteNonQuery();
                    return (int)cmd.Parameters["@RowsAffected"].Value;
                }
                catch (SqlException sqlex)
                {
                    throw new Exception("Failed to insert group data", sqlex);
                }
            }
        }

        public override List<ContactGroup> GetAllGroupCodeForCompany(string groupType, bool isForDropdown)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_get_all_ContactGroup_by_GroupType", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;
                cmd.Parameters.Add("@ContactGroupType", SqlDbType.NVarChar).Value = groupType;
                cmd.Parameters.Add("@IsForDropdown", SqlDbType.Bit).Value = isForDropdown;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<ContactGroup> lst = new List<ContactGroup>();
                while (reader.Read())
                {
                    ContactGroup obj = new ContactGroup();
                    obj.Id = GetReaderValue_Int32(reader, "ItemId", 0);
                    obj.ContactName = GetReaderValue_String(reader, "ContactName", "");
                    obj.Code = GetReaderValue_String(reader, "Code", "");
                    obj.Inactive = GetReaderValue_Int32(reader, "Inactive", 0) == 0 ? "No" : "Yes";
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to get Companys", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override int DeleteCustomerGroupCodeByID(int groupId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_Delete_GroupCode_by_Id", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;
                cmd.Parameters.Add("@GroupCodeID", SqlDbType.Int).Value = groupId;
                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                cmd.ExecuteNonQuery();
                return (int)cmd.Parameters["@RowsAffected"].Value;

            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to Delete Customer Group Code By ID ", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override int InactiveCustomerGroupCodeByID(int groupId, int inactive, int updatedBy)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_Inactive_GroupCode_by_Id", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;
                cmd.Parameters.Add("@GroupCodeID", SqlDbType.Int).Value = groupId;
                cmd.Parameters.Add("@Inactive", SqlDbType.Int).Value = inactive;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                cmd.ExecuteNonQuery();
                return (int)cmd.Parameters["@RowsAffected"].Value;

            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to Active/Inactive Customer Group Code By ID ", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override List<ContactGroup> AutoSearchGroupCode(string strSearch, string groupType, bool isDropDown)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_Get_AutoSearch_ContactGroup", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;
                cmd.Parameters.Add("@ContactGroupType", SqlDbType.NVarChar).Value = groupType;
                cmd.Parameters.Add("@AutoSearch", SqlDbType.NVarChar).Value = strSearch;
                cmd.Parameters.Add("@IsForDropdown", SqlDbType.Bit).Value = isDropDown;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<ContactGroup> lst = new List<ContactGroup>();
                while (reader.Read())
                {
                    ContactGroup obj = new ContactGroup();
                    obj.Id = GetReaderValue_Int32(reader, "ItemId", 0);
                    obj.ContactName = GetReaderValue_String(reader, "ContactName", "");
                    obj.Code = GetReaderValue_String(reader, "Code", "");
                    obj.Inactive = GetReaderValue_Int32(reader, "Inactive", 0) == 0 ? "No" : "Yes";
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to get Companys", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override DataTable GetManufacturerGroupByMfr(int? MfrId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_GetManufacturerGroup_By_Mfr", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@ManufacturerId", SqlDbType.Int).Value = MfrId;
                cmd.CommandTimeout = 900;

                cn.Open();
                DataSet ds = new DataSet();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);
                return ds.Tables[0];
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get ManufacturerGroupByMfr", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override DataTable GetSupplierTypeByMfrGroup(System.Int32? ContactGroupId, System.Int32? ClientNo)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_GetSupplierType_By_MfrGroup", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@ContactGroupId", SqlDbType.Int).Value = ContactGroupId;
                cmd.Parameters.Add("@ClientNo", SqlDbType.Int).Value = ClientNo;
                cmd.CommandTimeout = 900;

                cn.Open();
                DataSet ds = new DataSet();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);
                return ds.Tables[0];
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get SupplierTypeByMfrGroup", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override List<ContactGroup> GetManufacturerGroupBySupplier(int? supplierId, int? clientNo, System.Boolean? inactive)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_Get_ManufactuerGroupNameBySupplierId", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;
                cmd.Parameters.Add("@SupplierId", SqlDbType.Int).Value = supplierId;
                cmd.Parameters.Add("@ClientNo", SqlDbType.Int).Value = clientNo;
                cmd.Parameters.Add("@Inactive", SqlDbType.Bit).Value = inactive;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<ContactGroup> lst = new List<ContactGroup>();
                while (reader.Read())
                {
                    ContactGroup obj = new ContactGroup();
                    obj.Id = GetReaderValue_Int32(reader, "ContactGroupId", 0);
                    obj.ContactName = GetReaderValue_String(reader, "ContactName", "");
                    obj.Code = GetReaderValue_String(reader, "Code", "");
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to get Companys", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
    }
}
