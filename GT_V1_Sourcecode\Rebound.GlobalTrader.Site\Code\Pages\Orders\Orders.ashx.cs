using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site.Controls.DropDowns.Data;
using Rebound.GlobalTrader.BLL;

namespace Rebound.GlobalTrader.Site.Pages.Data.Orders {
    /// <summary>
    /// Summary description for $codebehindclassname$
    /// </summary>
    [WebService(Namespace = "http://tempuri.org/")]
    [WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
	public class Orders : Rebound.GlobalTrader.Site.Data.Base {

        public override void ProcessRequest(HttpContext context) {
            if (base.init(context)) {
                switch (Action) {
                    case "CountRequirements": CountRequirements(); break;
                    case "CountSOs": CountSOs(); break;
                    case "CountInvoices": CountInvoices(); break;
                    case "CountQuotes": CountQuotes(); break;
                    case "CountPOs": CountPOs(); break;
                    case "CountCRMAs": CountCRMAs(); break;
                    case "CountSRMAs": CountSRMAs(); break;
                    case "CountPurReqs": CountPurReqs(); break;
                    case "CountCredits": CountCredits(); break;
                    case "CountDebits": CountDebits(); break;
                }
            }
        }

        private void CountRequirements() {
            OutputCount(CustomerRequirement.CountForClient(SessionManager.ClientID));
        }

        private void CountSOs() {
            OutputCount(SalesOrder.CountForClient(SessionManager.ClientID));
        }

        private void CountInvoices() {
            OutputCount(Invoice.CountForClient(SessionManager.ClientID));
        }

        private void CountQuotes() {
            OutputCount(Quote.CountForClient(SessionManager.ClientID));
        }

        private void CountPOs() {
            OutputCount(PurchaseOrder.CountForClient(SessionManager.ClientID));
        }

        private void CountCRMAs() {
            OutputCount(CustomerRma.CountForClient(SessionManager.ClientID));
        }

        private void CountSRMAs() {
            OutputCount(SupplierRma.CountForClient(SessionManager.ClientID));
        }

        private void CountPurReqs() {
            OutputCount(SalesOrderLine.CountAsPurchaseRequisitionForClient(SessionManager.ClientID));
        }

        private void CountCredits() {
            OutputCount(CreditLine.CountForClient(SessionManager.ClientID));
        }

        private void CountDebits() {
            OutputCount(DebitLine.CountForClient(SessionManager.ClientID));
        }
    }
}
