///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//
// RP 23.10.2009:
// - new control
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists");

Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Companies = function(element) { 
	Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Companies.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Companies.prototype = {

	get_enmCompanyListType: function() { return this._enmCompanyListType; }, set_enmCompanyListType: function(value) { if (this._enmCompanyListType !== value)  this._enmCompanyListType = value; }, 

	initialize: function() {
		this.addGetDataOKEvent(Function.createDelegate(this, this.getDataOK));
		this.addSetupDataCallEvent(Function.createDelegate(this, this.setupDataCall));
		this._strPathToData = "controls/DataListNuggets/Companies";
		this._strDataObject = "Companies";
		Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Companies.callBaseMethod(this, "initialize");
	},
	
	dispose: function() { 
		if (this.isDisposed) return;
		this._enmCompanyListType = null;
		Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Companies.callBaseMethod(this, "dispose");
	},
	
	setupDataCall: function() {
		var strAction = "GetData_";
		switch (this._enmCompanyListType) {
			case $R_ENUM$CompanyListType.AllCompanies: strAction += "Companies"; break;
			case $R_ENUM$CompanyListType.Customers: strAction += "Customers"; break;
			case $R_ENUM$CompanyListType.Suppliers: strAction += "Suppliers"; break;
			case $R_ENUM$CompanyListType.Prospects: strAction += "Prospects"; break;
		}
		this._objData.set_DataAction(strAction);
		this._objData.addParameter("CallType", this._enmCompanyListType);
	},

	getDataOK: function() {
		for (var i = 0, l = this._objResult.Results.length; i < l; i++) {
			var row = this._objResult.Results[i];
			this._tbl.addRow([ String.format("<a href=\"{0}\">{1}</a>", $RGT_gotoURL_Company(row.ID, null, this._enmCompanyListType), $R_FN.setCleanTextValue(row.Name)) ]);
			strData = null; row = null;
		}
	}

};
Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Companies.registerClass("Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Companies", Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Base, Sys.IDisposable);
