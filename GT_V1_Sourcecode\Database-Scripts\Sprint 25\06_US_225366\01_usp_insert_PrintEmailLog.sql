﻿
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

/*   
===========================================================================================  
TASK        	UPDATED BY       DATE          ACTION    DESCRIPTION  
[US-225366]    Ngai To		09-Apr-2025			Update   US 225366: Sales Order - Add more triggers for set 'SO sent to customer' info
===========================================================================================  
*/
CREATE OR ALTER PROCEDURE [dbo].[usp_insert_PrintEmailLog]
	--
	@SectionName VARCHAR(50) = NULL,
	@SubSectionName VARCHAR(50) = NULL,
	@ActionName VARCHAR(10) = NULL,
	@DocumentNo INT,
	@Detail NVARCHAR(max) = NULL,
	@UpdatedBy INT = NULL,
	@PrintDocumentLogId INT OUTPUT
AS
BEGIN
	IF @SubSectionName = 'ReceivingDocket'
		OR @SubSectionName = 'ProFormaReceivingDocket'
	BEGIN
		SELECT @DocumentNo = GoodsInNo
		FROM tbGoodsInLine
		WHERE GoodsInLineId = @DocumentNo
	END

	IF @SectionName = 'SalesOrder'
		AND (
			@SubSectionName = 'SalesOrder'
			OR @SubSectionName = 'ConsolidatePrintSO'
			OR @SubSectionName = 'ConsolidateEmailSO'
			OR @SubSectionName = 'ProFormaInvoice'
			OR @SubSectionName = 'ProFormaInvoiceCon'
			OR @SubSectionName = 'SOReport'
			)
		AND (
			@ActionName = 'Print'
			OR @ActionName = 'Email'
			)
	BEGIN
		UPDATE dbo.tbSalesOrder
		SET SentOrdertoCust = @UpdatedBy,
			SentOrderDate = current_timestamp
		WHERE SalesOrderId = @DocumentNo
	END

	INSERT INTO dbo.tbPrintDocumentLog (
		SectionName,
		SubSectionName,
		ActionName,
		DocumentNo,
		Detail,
		UpdatedBy,
		DLUP
		)
	VALUES (
		@SectionName,
		@SubSectionName,
		@ActionName,
		@DocumentNo,
		@Detail,
		@UpdatedBy,
		GETDATE()
		)

	SET @PrintDocumentLogId = scope_identity()
END
GO


