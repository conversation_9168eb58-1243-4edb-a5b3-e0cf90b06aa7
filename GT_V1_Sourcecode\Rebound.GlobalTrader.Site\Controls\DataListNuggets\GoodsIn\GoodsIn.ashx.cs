/* Marker     changed by      date         Remarks
/* [0001]      <PERSON><PERSON><PERSON><PERSON>   14/07/2014  ESMS Ref:172 - Issue in Part Searching in whole GT */
/* [0002]      <PERSON>    19-09-2023      RP-2338  AS6081 Search/Filter functionality on different pages */

using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;
using Rebound.GlobalTrader.Site.Enumerations;

namespace Rebound.GlobalTrader.Site.Controls.DataListNuggets.Data {

	public class GoodsIn : Base {

		private string _strCallType;
       // private Boolean IsGlobalLogin = true;

		protected override void GetData() {

			_strCallType = "ALL";
			if (GetFormValue_Boolean("UninspectedOnly")) _strCallType = "UNINSPECTED";
            if (GetFormValue_Boolean("IsQueriedTab")) _strCallType = "GI_Queried";
            Boolean IsGlobalLogin = GetFormValue_Boolean("IsGlobalLogin");
            string AS6081 = GetFormValue_String("AS6081"); //[0003]
            List<GoodsInLine> lst = GoodsInLine.DataListNugget(
				SessionManager.ClientID
				, GetFormValue_NullableInt("SortIndex")
				, GetFormValue_NullableInt("SortDir", SortColumnDirection.ASC)
				, GetFormValue_NullableInt("PageIndex", 0)
				, GetFormValue_NullableInt("PageSize", 10)
                , GetFormValue_PartForLikeSearch("Part")
                , GetFormValue_StringForNameSearchDecode("CMName")
				, GetFormValue_NullableInt("ReceivedBy")
                , GetFormValue_StringForLikeSearch("AirWayBill")
				, GetFormValue_Boolean("IncludeInvoiced")
				, GetFormValue_NullableInt("PONoLo")
				, GetFormValue_NullableInt("PONoHi")
				, GetFormValue_NullableInt("GINoLo")
				, GetFormValue_NullableInt("GINoHi")
				, GetFormValue_NullableDateTime("DateReceivedFrom")
				, GetFormValue_NullableDateTime("DateReceivedTo")
				, GetFormValue_StringForSearch("SupplierInvoice")
				, GetFormValue_StringForSearch("Reference")
				, GetFormValue_Boolean("RecentOnly")
				, GetFormValue_Boolean("UninspectedOnly")
                , GetFormValue_NullableInt("Client")
                , SessionManager.IsPOHub == true ? 1 : 0
                , GetFormValue_Boolean("IsGlobalLogin")
                , GetFormValue_NullableInt("Warehouse")
                , GetFormValue_Boolean("IsQueriedTab")
                , GetFormValue_NullableInt("GILineQueryProgressStatus")
				, (AS6081 == "1" ? true : (AS6081 == "2" ? false : (bool?)null)) //[0002]
            );
			JsonObject jsn = new JsonObject();
			jsn.AddVariable("TotalRecords", (lst.Count > 0) ? lst[0].RowCnt : 0);
			JsonObject jsnRowsArray = new JsonObject(true);
			for (int i = 0; i < lst.Count; i++) {
				if (i < lst.Count) {
					JsonObject jsnRow = new JsonObject();
					jsnRow.AddVariable("ID", lst[i].GoodsInId);
					jsnRow.AddVariable("No", lst[i].GoodsInNumber);
					jsnRow.AddVariable("Part", lst[i].Part);
					jsnRow.AddVariable("StockNo", lst[i].StockNo);
					jsnRow.AddVariable("Quantity", lst[i].Quantity);

                    if (lst[i].InternalPurchaseOrderId.HasValue && lst[i].InternalPurchaseOrderId.Value > 0)
                    {
                        jsnRow.AddVariable("CM", SessionManager.IsPOHub.Value ? lst[i].CompanyName : lst[i].IPOSupplierName);
                        jsnRow.AddVariable("CMNo", SessionManager.IsPOHub.Value ? lst[i].CompanyNo : lst[i].IPOSupplier);
                    }
                    else
                    {
                        jsnRow.AddVariable("CM", lst[i].CompanyName);
                        jsnRow.AddVariable("CMNo", lst[i].CompanyNo);
                    }

					jsnRow.AddVariable("PO", lst[i].PurchaseOrderNumber);
					jsnRow.AddVariable("PONo", lst[i].PurchaseOrderNo);
					jsnRow.AddVariable("Receiver", lst[i].ReceiverName);
					jsnRow.AddVariable("Date", Functions.FormatDate(lst[i].DateReceived));
					jsnRow.AddVariable("ROHS", lst[i].ROHS);
					jsnRow.AddVariable("DelivDate", Functions.FormatDate(lst[i].DeliveryDate));
					jsnRow.AddVariable("Mfr", lst[i].ManufacturerCode);
					jsnRow.AddVariable("MfrNo", lst[i].ManufacturerNo);
					jsnRow.AddVariable("AWB", lst[i].AirWayBill);
                    jsnRow.AddVariable("IPO", lst[i].InternalPurchaseOrderId);
                    jsnRow.AddVariable("IPONo", lst[i].InternalPurchaseOrderNumber);

                    jsnRow.AddVariable("ClientName", SessionManager.IsPOHub == true || IsGlobalLogin ==true? lst[i].ClientName : "");
                    jsnRow.AddVariable("ClientNo", lst[i].ClientNo);

                    jsnRow.AddVariable("IPOSupplier",  lst[i].IPOSupplier);
                    jsnRow.AddVariable("IPOSupplierName", lst[i].IPOSupplierName);
                    jsnRow.AddVariable("IsPoHub", SessionManager.IsPOHub);
                    jsnRow.AddVariable("Status", Functions.GetGlobalResource("Status", (BLL.StockStatus.List)lst[i].GIStatus));
					jsnRow.AddVariable("SuppMessage", Functions.ReplaceLineBreaks(lst[i].SupplierMessage));
                    jsnRow.AddVariable("GoodInLineMessage", Functions.ReplaceLineBreaks(lst[i].GoodInLineMessage));
                    jsnRow.AddVariable("QueryRaised", lst[i].QueryRaised);
                    jsnRow.AddVariable("GoodsInLineId", lst[i].GoodsInLineId);
                    jsnRow.AddVariable("AS6081", lst[i].AS6081); //[0002]
                    jsnRowsArray.AddVariable(jsnRow);
					jsnRow.Dispose();
					jsnRow = null;
				}
			}
			jsn.AddVariable("Results", jsnRowsArray);
			OutputResult(jsn);
			jsnRowsArray.Dispose();
			jsnRowsArray = null;
			jsn.Dispose();
			jsn = null;
			lst = null;
			base.GetData();
		}

		protected override void AddFilterStates() {
			AddExplicitFilterState("CallType", _strCallType);
			AddFilterState("Part");
			AddFilterState("CMName");
			AddFilterState("ReceivedBy");
			AddFilterState("AirWayBill");
			AddFilterState("IncludeInvoiced");
			AddFilterState("PONo");
			AddFilterState("GINo");
			AddFilterState("DateReceivedFrom");
			AddFilterState("DateReceivedTo");
			AddFilterState("SupplierInvoice");
			AddFilterState("Reference");
			AddFilterState("RecentOnly");
			AddFilterState("UninspectedOnly");
            AddFilterState("GILineQueryProgressStatus");
			AddFilterState("AS6081"); //[0002]
			base.AddFilterStates();
		}

	}
}
