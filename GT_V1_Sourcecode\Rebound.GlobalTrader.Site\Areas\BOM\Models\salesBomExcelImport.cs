﻿//using System;
//using System.Collections.Generic;
//using System.ComponentModel.DataAnnotations;
//using System.Linq;
//using System.Web;



//namespace Rebound.GlobalTrader.Site.Areas.BOM.Models
//{
//    public class salesBomExcelImport
//    {
//        public string FilePath { get; set; }
//    }



//    public class Client
//    {
//        public int ClientId { get; set; }
//        public string ClientName { get; set; }
//        public string ClientCode { get; set; }
//    }

//    public class RequiremenTraceability
//    {
//        public int Id { get; set; }
//        public string RequiremenTraceabilityName { get; set; }
//    }

//    public class UploadType
//    {
//        public int Id { get; set; }
//        public string DataType { get; set; }
//    }

//    public class Currency
//    {
//        public int CurrencyId { get; set; }
//        public string CurrencyDescription { get; set; }
//        public string CurrencyCode { get; set; }
//    }



//    public class SalesBomSheetModel : ErrorMessage
//    {
//        [Required]
//        public int ClientId { get; set; }
//        [Required]
//        public int DigitalCurrency { get; set; }
//        [Required]
//        public bool Currencyforalllines { get; set; }
//        [Required]
//        [StringLength(200)]
//        public string Company { get; set; }
//        [Required]
//        [StringLength(100)]
//        public string Salesperson { get; set; }

//        public bool Defaultcurrency { get; set; } = true;
//        [Required]
//        public bool AllRequirements { get; set; }
//        [Required]
//        public int Contact { get; set; }
//        [Required]
//        [StringLength(100)]
//        public string BOMName { get; set; }

//        public bool ApplyPartwatch { get; set; }
//        public bool Filecolumnheader { get; set; } = true;
//        [Required]
//        public string StockCode { get; set; }
//        [Required]
//        public int Description { get; set; }
//        [Required]
//        public int Part { get; set; }
//        [Required]
//        public int RFQ { get; set; }
//        [Required]
//        public int UnitPrice { get; set; }
//        [Required]
//        public int LineTotal { get; set; }
//        public string ExcelBOMFileName { get; set; }
//        public int RequiremenTraceability { get; set; }
//        public int Type { get; set; }
//        public DateTime CurrentDateTime { get; set; } = System.DateTime.Now;
//        public List<string> ExcelBOMFileColumns { get; set; }
//        public List<Client> clients { get; set; }
//        public List<Currency> currency { get; set; }
//        public List<ExcelFileModel> excelFileModel { get; set; }
//        //public List<ExcelFileColumnsModel> excelFileColumnsModel { get; set; }
//    }



//    public class ExcelFileModel
//    {
//        public string StockCode { get; set; }
//        public string Description { get; set; }
//        public string Part { get; set; }
//        public string RFQ { get; set; }
//        public string UnitPrice { get; set; }
//        public string LineTotal { get; set; }
//        //public List<ExcelFileColumnsModel> excelFileColumnsModel { get; set; }
//    }



//    public class ExcelFileColumnsModel
//    {
//        public int Id { get; set; }
//        public string ColumnsName { get; set; }
//    }



//    public class ErrorMessage
//    {
//        public string ErrorId { get; set; }
//        public string ErrorMessages { get; set; }
//        public string ErrorCode { get; set; }
//    }


//    public class ImportDataModel
//    {
//        public string Company    { get; set; }
//        public string Contact { get; set; }
//        public string Currency { get; set; }
//        public string Notes { get; set; }
//        public string Manufacturer { get; set; }
//        public string Part { get; set; }
//        public string Quantity { get; set; }
//        public string Price { get; set; }
//    }

//}