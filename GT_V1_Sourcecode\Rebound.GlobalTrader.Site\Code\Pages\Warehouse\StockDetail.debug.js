///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
/*
Marker     Changed by      Date         Remarks
[001]      Vinay           07/05/2012   This need to upload pdf document for stock detail section
*/
//-----------------------------------------------------------------------------------------
//Code Merge for GI Screen
Type.registerNamespace("Rebound.GlobalTrader.Site.Pages.Warehouse");

Rebound.GlobalTrader.Site.Pages.Warehouse.StockDetail = function(el) { 
	Rebound.GlobalTrader.Site.Pages.Warehouse.StockDetail.initializeBase(this, [el]);
};

Rebound.GlobalTrader.Site.Pages.Warehouse.StockDetail.prototype = {

    get_ctlPageTitle: function() { return this._ctlPageTitle; }, set_ctlPageTitle: function(v) { if (this._ctlPageTitle !== v) this._ctlPageTitle = v; },
    get_ctlMainInfo: function() { return this._ctlMainInfo; }, set_ctlMainInfo: function(v) { if (this._ctlMainInfo !== v) this._ctlMainInfo = v; },
    get_ctlAllocations: function() { return this._ctlAllocations; }, set_ctlAllocations: function(v) { if (this._ctlAllocations !== v) this._ctlAllocations = v; },
    get_ctlStockLog: function() { return this._ctlStockLog; }, set_ctlStockLog: function(v) { if (this._ctlStockLog !== v) this._ctlStockLog = v; },
    get_ctlStockImages: function() { return this._ctlStockImages; }, set_ctlStockImages: function(v) { if (this._ctlStockImages !== v) this._ctlStockImages = v; },
    //[001] code start
    get_ctlStockDocuments: function() { return this._ctlStockDocuments }, set_ctlStockDocuments: function(a) { if (this._ctlStockDocuments !== a) { this._ctlStockDocuments = a } },
    //[001] code end
    get_ctlRelatedStock: function() { return this._ctlRelatedStock; }, set_ctlRelatedStock: function(v) { if (this._ctlRelatedStock !== v) this._ctlRelatedStock = v; },
    get_pnlQuantities: function() { return this._pnlQuantities; }, set_pnlQuantities: function(v) { if (this._pnlQuantities !== v) this._pnlQuantities = v; },
    get_pnlQuantityInStock: function() { return this._pnlQuantityInStock; }, set_pnlQuantityInStock: function(v) { if (this._pnlQuantityInStock !== v) this._pnlQuantityInStock = v; },
    get_lblQuantityInStock: function() { return this._lblQuantityInStock; }, set_lblQuantityInStock: function(v) { if (this._lblQuantityInStock !== v) this._lblQuantityInStock = v; },
    get_pnlQuantityOnOrder: function() { return this._pnlQuantityOnOrder; }, set_pnlQuantityOnOrder: function(v) { if (this._pnlQuantityOnOrder !== v) this._pnlQuantityOnOrder = v; },
    get_lblQuantityOnOrder: function() { return this._lblQuantityOnOrder; }, set_lblQuantityOnOrder: function(v) { if (this._lblQuantityOnOrder !== v) this._lblQuantityOnOrder = v; },
    get_pnlQuantityAllocated: function() { return this._pnlQuantityAllocated; }, set_pnlQuantityAllocated: function(v) { if (this._pnlQuantityAllocated !== v) this._pnlQuantityAllocated = v; },
    get_lblQuantityAllocated: function() { return this._lblQuantityAllocated; }, set_lblQuantityAllocated: function(v) { if (this._lblQuantityAllocated !== v) this._lblQuantityAllocated = v; },
    get_lblQuantityAvailable: function() { return this._lblQuantityAvailable; }, set_lblQuantityAvailable: function(v) { if (this._lblQuantityAvailable !== v) this._lblQuantityAvailable = v; },
    get_pnlQuarantined: function() { return this._pnlQuarantined; }, set_pnlQuarantined: function(v) { if (this._pnlQuarantined !== v) this._pnlQuarantined = v; },
    get_pnlStatus: function() { return this._pnlStatus; }, set_pnlStatus: function(v) { if (this._pnlStatus !== v) this._pnlStatus = v; },
    get_lblStatus: function() { return this._lblStatus; }, set_lblStatus: function(v) { if (this._lblStatus !== v) this._lblStatus = v; },
    get_ctlStockPDFDragDrop: function() { return this._ctlStockPDFDragDrop; }, set_ctlStockPDFDragDrop: function(v) { if (this._ctlStockPDFDragDrop !== v) this._ctlStockPDFDragDrop = v; },
    get_ctlStockImagesDragDrop: function() { return this._ctlStockImagesDragDrop; }, set_ctlStockImagesDragDrop: function(v) { if (this._ctlStockImagesDragDrop !== v) this._ctlStockImagesDragDrop = v; },
    get_IsGlobalLogin: function () { return this._IsGlobalLogin; }, set_IsGlobalLogin: function (v) { if (this._IsGlobalLogin !== v) this._IsGlobalLogin = v; },

    initialize: function() {
        Rebound.GlobalTrader.Site.Pages.Warehouse.StockDetail.callBaseMethod(this, "initialize");
    },

    goInit: function() {
        if (this._ctlMainInfo) this._ctlMainInfo.addGotData(Function.createDelegate(this, this.ctlStockMainInfo_GotData));
        if (this._ctlAllocations) this._ctlAllocations.addSaveEditComplete(Function.createDelegate(this, this.ctlAllocations_SaveEditComplete));
        Rebound.GlobalTrader.Site.Pages.Warehouse.StockDetail.callBaseMethod(this, "goInit");
    },

    dispose: function() {
        if (this.isDisposed) return;
        if (this._ctlMainInfo) this._ctlMainInfo.dispose();
        if (this._ctlPageTitle) this._ctlPageTitle.dispose();
        if (this._ctlAllocations) this._ctlAllocations.dispose();
        if (this._ctlStockLog) this._ctlStockLog.dispose();
        if (this._ctlStockImages) this._ctlStockImages.dispose();
        if (this._ctlRelatedStock) this._ctlRelatedStock.dispose();
        //[001] code start
        if (this._ctlStockDocuments) { this._ctlStockDocuments.dispose(); }
        //[001] code end
        if (this._ctlStockPDFDragDrop) { this._ctlStockPDFDragDrop.dispose(); }
        if (this._ctlStockImagesDragDrop) { this._ctlStockImagesDragDrop.dispose(); }

        this._ctlPageTitle = null;
        this._ctlMainInfo = null;
        this._ctlAllocations = null;
        this._ctlStockLog = null;
        this._ctlStockImages = null;
        this._ctlRelatedStock = null;
        this._pnlQuantities = null;
        this._pnlQuantityInStock = null;
        this._lblQuantityInStock = null;
        this._pnlQuantityOnOrder = null;
        this._lblQuantityOnOrder = null;
        this._pnlQuantityAllocated = null;
        this._lblQuantityAllocated = null;
        this._lblQuantityAvailable = null;
        this._pnlQuarantined = null;
        this._pnlStatus = null;
        this._lblStatus = null;
        this._ctlStockImagesDragDrop = null;
        this._IsGlobalLogin = null;
        Rebound.GlobalTrader.Site.Pages.Warehouse.StockDetail.callBaseMethod(this, "dispose");
    },

    ctlStockMainInfo_GotData: function() {
        this.updateQuantities(this._ctlMainInfo._intQuantityInStock, this._ctlMainInfo._intQuantityOnOrder, this._ctlMainInfo._intQuantityAllocated, this._ctlMainInfo._intQuantityAvailable);
        this.updateQuarantined(this._ctlMainInfo._blnQuarantined);
        this.updateStatus();
        this.updatePartNoTitle();
        if (this._ctlRelatedStock) this._ctlRelatedStock.refresh();
        if (this._ctlStockLog) this._ctlStockLog.refresh();
        if (this._ctlAllocations) this._ctlAllocations.refresh();
        // alert("test");
        if (this._ctlStockImages) this._ctlStockImages.refresh();
        //[001] code start
        if (this._ctlStockDocuments) { this._ctlStockDocuments.getData(); }
        //[001] code end
        // alert(this._ctlStockPDFDragDrop);
        if (this._ctlStockPDFDragDrop) { this._ctlStockPDFDragDrop.getData(); }
        if (this._ctlStockImagesDragDrop) this._ctlStockImagesDragDrop.refresh();
        this._ctlMainInfo._IsGlobalLogin = this._IsGlobalLogin;
    },

    updateQuantities: function(intInStock, intOnOrder, intAllocated, intAvailable) {
        $R_FN.setInnerHTML(this._lblQuantityInStock, (intInStock > 0) ? intInStock : 0);
        $R_FN.setInnerHTML(this._lblQuantityOnOrder, (intOnOrder > 0) ? intOnOrder : 0);
        $R_FN.setInnerHTML(this._lblQuantityAllocated, (intAllocated > 0) ? intAllocated : 0);
        $R_FN.setInnerHTML(this._lblQuantityAvailable, (intAvailable > 0) ? intAvailable : 0);
        var intWidth = 250;
        var intMax = Math.max(intInStock + intOnOrder, intAllocated);
        var dblFactor = (intMax > 0) ? intWidth / intMax : 1;
        var objWidths = { InStock: Math.min(intWidth, Math.round(intInStock * dblFactor))
			, OnOrder: Math.min(intWidth, Math.round(intOnOrder * dblFactor))
			, Allocated: Math.min(intWidth, Math.round(intAllocated * dblFactor))
			, Available: Math.min(intWidth, Math.round(intAvailable * dblFactor))
        };
        this._pnlQuantityInStock.style.width = String.format("{0}px", objWidths.InStock);
        this._pnlQuantityOnOrder.style.width = String.format("{0}px", objWidths.OnOrder + objWidths.InStock);
        this._pnlQuantityAllocated.style.width = String.format("{0}px", objWidths.Allocated);
    },

    ctlAllocations_SaveEditComplete: function() {
        if (this._ctlMainInfo) this._ctlMainInfo.refresh();
        if (this._ctlStockLog) this._ctlStockLog.refresh();
    },

    updateQuarantined: function(bln) {
        $R_FN.showElement(this._pnlQuarantined, bln);
    },

    updateStatus: function() {
        var strStatus = this._ctlMainInfo.getFieldValue("hidStatus");
        $R_FN.setInnerHTML(this._lblStatus, strStatus);
        $R_FN.showElement(this._pnlStatus, strStatus.length > 0);
        strStatus = null;
    },

    updatePartNoTitle: function() {
        this._ctlPageTitle.updateTitle(this._ctlMainInfo.getFieldValue("ctlPartNo"));
    }

};

Rebound.GlobalTrader.Site.Pages.Warehouse.StockDetail.registerClass("Rebound.GlobalTrader.Site.Pages.Warehouse.StockDetail", Rebound.GlobalTrader.Site.Pages.Content, Sys.IDisposable);
