using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
//using Rebound.GlobalTrader.Site.Controls.DropDowns.Data;
using Rebound.GlobalTrader.BLL;

namespace Rebound.GlobalTrader.Site.Pages.Data.Contact {
	[WebService(Namespace = "http://tempuri.org/")]
	[WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
	public class Contact : Rebound.GlobalTrader.Site.Data.Base {

		public override void ProcessRequest(HttpContext context) {
			if (base.init(context)) {
				switch (Action) {
					case "CountAllCompanies": CountAllCompanies(); break;
					case "CountManufacturers": CountManufacturers(); break;
					case "CountProspects": CountProspects(); break;
					case "CountCustomers": CountCustomers(); break;
					case "CountSuppliers": CountSuppliers(); break;
					case "CountContacts": CountContacts(); break;
				}
			}
		}

		private void CountAllCompanies() {
			OutputCount(Company.CountByClient(SessionManager.ClientID));
		}

		private void CountManufacturers() {
			OutputCount(BLL.Manufacturer.Count());
		}

		private void CountProspects() {
			OutputCount(Company.CountAsProspectsByClient(SessionManager.ClientID));
		}

		private void CountCustomers() {
			OutputCount(Company.CountAsCustomersByClient(SessionManager.ClientID));
		}

		private void CountSuppliers() {
			OutputCount(Company.CountAsSuppliersByClient(SessionManager.ClientID));
		}

		private void CountContacts() {
			OutputCount(BLL.Contact.CountForClient(SessionManager.ClientID));
		}

	}
}
