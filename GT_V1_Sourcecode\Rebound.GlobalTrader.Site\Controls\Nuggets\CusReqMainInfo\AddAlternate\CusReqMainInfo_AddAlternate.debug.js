/*
Marker     Changed by      Date         Remarks
[001]      Bhooma          01-Dec-2021  Replace screen with same like add requirement screen.
[002]      Bhooma          20-Dec-2021  Removing Manufacture Product Required Field.
*/

Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.CustomerRequirementMainInfo_AddAlternate = function (element) {
    Rebound.GlobalTrader.Site.Controls.Forms.CustomerRequirementMainInfo_AddAlternate.initializeBase(this, [element]);
    this._intCustomerRequirementID = -1;
    this._intOriginalCustomerRequirementID = -1;
    this._intCompanyID = 0;
    this._strSourceSelected = "";
    this._RadChecked = false;
    this._radioCheck = false;
    this._isPoRequest = false;
    this._intBOMID = -1;
    this._radObsoleteChk = false;
    this._radLastTimeBuyChk = false;
    this._radRefirbsAcceptableChk = false;
    this._radTestingRequiredChk = false;
    this._BOMHeaderDisplayStatus = false;
    this._blnCurInSameFaimly = true;
    this._intCurrencyNo = 0;
    this._radAlternativesAcceptedChK = false;
    this._radRepeatBusinessChk = false;
    this._hidCountryOfOrigin = "";
    this._hidCountryOfOriginNo = 0;
    this._hidLifeCycleStage = "";
    this._hidHTSCode = "";
    this._hidAveragePrice = 0;
    this._hidPackaging = "";
    this._hidPackagingSize = "";
    this._hidDescriptions = "";
    //ihs variable start
    this._ctlCompany = "";
    // this._hidCompanyName = "";
    //this._hidCompanyID = 0;
    this._ctlContact = -1;//res.ContactNo, res.Contact
    // this._hidContactID = -1;
    this._ctlQuantity = 0;
    this._ctlPartNo = "";
    this._ctlCustomerPart = "";
    // this._ctlManufacturer = "";//res.ManufacturerNo, res.Manufacturer
    this._hidManufacturer = "";
    this._hidManufacturerNo = "";
    this._ctlDateCode = "";
    this._ctlProduct = "";
    this._ctlProductDis = false;//(res.Product, res.IsProdHaz)
    // this._ctlPrdDutyCodeRate = "";
    this._hidProductID = 0;
    //this._ctlPackage = "";
    this._hidPackageID = -1;
    //this._ctlTargetPrice = 0;
    this._hidPrice = 0;
    //this._ctlCurrency = "";
    this._hidCurrencyID = 0;
    this._ctlDateRequired = "";
    //this._ctlUsage = "";
    this._hidUsageID = -1;
    this._ctlNotes = "";
    this._ctlInstructions = "";
    this._ctlROHS = -1;
    //this._hidROHS = 0;
    // this._ctlClosed = "";
    // this._ctlClosedReason = "";
    //this._hidDisplayStatus = "";
    this._ctlPartWatch = "";
    this._ctlBOM = false;
    this._ctlBOMName = "";
    this._ctlBOMHeader = "";//res.BOMId, $R_FN.setCleanTextValue(res.BOMHeader
    this._hidBOMID = 0;
    //this._hidBOMHeaderDisplayStatus = false;
    this._ctlMSL = -1;
    // this._ctlFactorySealed = false;
    this._ctlPQA = false;
    //this._ctlObsolete = false;
    //this._ctlLastTimeBuy = false;
    //this._ctlRefirbsAcceptable = false;
    //this._ctlTestingRequired =false;
    // this._ctlTargetSellPrice = 0;
    //this._ctlCompetitorBestoffer = 0;
    this._ctlCustomerDecisionDate = "";
    this._ctlRFQClosingDate = "";
    this._ctlQuoteValidityRequiredHid = "";
    // this._ctlQuoteValidityRequired = "";
    this._ctlTypeHid = -1;
    //this._ctlType = "";
    this._ctlOrderToPlace = false;
    // this._ctlRequirementforTraceability = "";
    this._ctlRequirementforTraceabilityHid = "";
    this._ctlTargetSellPriceHidden = 0;
    this._ctlCompetitorBestofferHidden = 0;
    this._ctlEAU = "";
    this._hidCustGCNo = null;
    //this._ctlAlternativesAccepted = false;
    //this._ctlRepeatBusiness = false;
    //this._blnProdInactive =false;
    //this._strhidMSL = 0;
    this._ctlSalespersion = null;
    this._hidSalesPersion = null;

    this._IHSProductNo = 0;
    this._IHSProduct = "";
    this._IHSHTSCode = "";
    this._IHSDutyCode = 0;
    this._AlternateStatus = 0;
    this.chkAlternatives = false;
    this._ECCNCode = "";
    this._ctlPackage = "";
    //ihs variable end
    this._isRestrictedManufacturer = 0;
    this._RestrictedMFRMessage = "";
    this._PartEditStatus = 1;
    this._searchType = "";
    var strsearchtype = "";
    this._enmSearchType = 0;

};

Rebound.GlobalTrader.Site.Controls.Forms.CustomerRequirementMainInfo_AddAlternate.prototype = {

    get_intCustomerRequirementID: function () { return this._intCustomerRequirementID; }, set_intCustomerRequirementID: function (value) { if (this._intCustomerRequirementID !== value) this._intCustomerRequirementID = value; },
    get_btn1: function () { return this._btn1; }, set_btn1: function (v) { if (this._btn1 !== v) this._btn1 = v; },
    get_btn2: function () { return this._btn2; }, set_btn2: function (v) { if (this._btn2 !== v) this._btn2 = v; },
    get_lblError: function () { return this._lblError; }, set_lblError: function (v) { if (this._lblError !== v) this._lblError = v; },
    get_pnlPartDetail: function () { return this._pnlPartDetail; }, set_pnlPartDetail: function (value) { if (this._pnlPartDetail !== value) this._pnlPartDetail = value; },
    get_ctltblPartdetails: function () { return this._ctltblPartdetails; }, set_ctltblPartdetails: function (v) { if (this._ctltblPartdetails !== v) this._ctltblPartdetails = v; },

    initialize: function () {
        Rebound.GlobalTrader.Site.Controls.Forms.CustomerRequirementMainInfo_AddAlternate.callBaseMethod(this, "initialize");
        this.addShown(Function.createDelegate(this, this.formShown));
        this._ctltblPartdetails.addItemSelected(Function.createDelegate(this, this.getIHSDataSelected));
    },
    getFormControlID: function (ParentId, controlID) {

        var str = "";
        str = String.format("{0}_{1}", ParentId, controlID);
        return str;
    },
    formShown: function () {
        //code start for part edit
        //alert(this._PartEditStatus);
        if (this._PartEditStatus == 1) {
            $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_showhidepartnolable").hide();
            $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_showhidePartnotext").show();
            this.ClearIhsDataOnLoad();
            document.getElementById("ParttypeSearch1Alternate").addEventListener("click", Function.createDelegate(this, this.changeSearchType));
            if (this._enmSearchType == 0) {
                $("#ParttypeSearch1Alternate").removeClass("searchType_StartsWith");
                $("#ParttypeSearch1Alternate").removeClass("searchType_ExactWith");
                $("#ParttypeSearch1Alternate").addClass("searchType_Contains");
                $("#ParttypeSearch1Alternate").attr("title", "Contains");
                $("#ParttypeSearch1Alternate").attr("alt", "Contains");
                $find(this.getFormControlID(this._element.id, 'cmbIHS'))._aut._txtGroup = "contains";

            }
            if ($find(this.getFormControlID(this._element.id, 'cmbIHS'))) $find(this.getFormControlID(this._element.id, 'cmbIHS'))._aut.addSelectionMadeEvent(Function.createDelegate(this, this.getIHSPartDetails));
            document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_btnClear").addEventListener("click", Function.createDelegate(this, this.ClearIhsData));
            //edit part
            document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_cmbIHS12txt").value = this._ctlPartNo;
            //code end
            document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_btn1").addEventListener("click", Function.createDelegate(this, this.SearchTypePopup));
            document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_btn3").addEventListener("click", Function.createDelegate(this, this.Toggle1));
            document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_btn2").addEventListener("click", Function.createDelegate(this, this.Toggle2));
            document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_btn5").addEventListener("click", Function.createDelegate(this, this.Canceltop));
            document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_btn6").addEventListener("click", Function.createDelegate(this, this.Cancelbottom));
            $addHandler(document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_btnOK_hyp"), "click", Function.createDelegate(this, this.getValuesByPartsOnClick));

        }

        else {
            $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_showhidePartnotext").hide();
            $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_showhidepartnolable").show();
            //$("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_cmbPartEccnMappedaut_ctl04").hide();


        }
        $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_txtPartNo").hide();
        $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_lblError").hide();


        // part edit status end

        $("#lblRsMFREditAlternate").hide();
        $("#spanmfrEditAlternate").text("");
        $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_chkAlternatives_ctl00").addClass("off");
        if (this._AlternateStatus != 'undefined' && this._AlternateStatus > 0) {
            $find(this.getFormControlID(this._element.id, 'chkAlternatives')).enableButton(false);
        }
        else {
            $find(this.getFormControlID(this._element.id, 'chkAlternatives')).enableButton(true);
        }
        $("#lbledittxtInstructionsAlternate").text(2000 - this._ctlInstructions.length);
        $("#lbledittxtNotesAlternate").text(2000 - this._ctlNotes.length);
        //[005] ihs set value
        $find(this.getFormControlID(this._element.id, 'ddlSalesman')).getData();
        $find(this.getFormControlID(this._element.id, 'ddlROHS')).getData();
        $find(this.getFormControlID(this._element.id, 'ddlType')).getData();
        $find(this.getFormControlID(this._element.id, 'ddlUsage')).getData();
        //$find(this.getFormControlID(this._element.id, 'ddlPackage')).getData();

        $find(this.getFormControlID(this._element.id, 'ddlMsl')).getData();
        $find(this.getFormControlID(this._element.id, 'ddlQuoteValidityRequired')).getData();
        $find(this.getFormControlID(this._element.id, 'ddlRequirementforTraceability')).getData();
        //Dropdown value bind
        $find(this.getFormControlID(this._element.id, 'ddlSalesman')).setValue(this._salesmanNo);
        // alert(this.setControlValue(this.getFormControlID(this._element.id, 'ddlSalesman'), 'DropDown'));
        $find(this.getFormControlID(this._element.id, 'ddlROHS')).setValue(this._ctlROHS);
        $find(this.getFormControlID(this._element.id, 'ddlType')).setValue(this._ctlTypeHid);
        $find(this.getFormControlID(this._element.id, 'ddlUsage')).setValue(this._hidUsageID);
        //$find(this.getFormControlID(this._element.id, 'ddlPackage')).setValue(this._hidPackageID);
        $find(this.getFormControlID(this._element.id, 'ddlMsl')).setValue(this._ctlMSL);
        $find(this.getFormControlID(this._element.id, 'ddlQuoteValidityRequired')).setValue(this._ctlQuoteValidityRequiredHid);
        $find(this.getFormControlID(this._element.id, 'ddlRequirementforTraceability')).setValue(this._ctlRequirementforTraceabilityHid);
        //Lable Value bind
        $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_lblCustomer").text(this._ctlCompany);
        $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_lblContact").text(this._ctlContact);


        $find(this.getFormControlID(this._element.id, 'cmbManufacturer')).setValue(this._hidManufacturer, $R_FN.setCleanTextValue(this._hidManufacturerNo));
        $find(this.getFormControlID(this._element.id, 'cmbProducts')).setValue(this._hidProductID, this._ctlProduct);
        $find(this.getFormControlID(this._element.id, 'cmbPackage')).setValue(this._hidPackageID, this._ctlPackage);
        $find(this.getFormControlID(this._element.id, 'cmbSalespersion')).setValue(this._hidSalesPersion, this._ctlSalespersion);


        //TextBox value bind
        $get(this.getFormControlID(this._element.id, 'txtCustomerPartNo')).value = this._ctlCustomerPart;
        $get(this.getFormControlID(this._element.id, 'txtQuantity')).value = this._ctlQuantity;
        $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_lblPartNo").text(this._ctlPartNo);



        $get(this.getFormControlID(this._element.id, 'txtNotes')).value = this._ctlNotes;
        $get(this.getFormControlID(this._element.id, 'txtInstructions')).value = this._ctlInstructions;
        $get(this.getFormControlID(this._element.id, 'txtDateCode')).value = this._ctlDateCode;
        $get(this.getFormControlID(this._element.id, 'txtBOMName')).value = this._ctlBOMName;
        $get(this.getFormControlID(this._element.id, 'txtTargetPrice')).value = this._hidPrice;
        $get(this.getFormControlID(this._element.id, 'txtTargetSellPrice')).value = this._ctlTargetSellPriceHidden;
        $get(this.getFormControlID(this._element.id, 'txtRFQClosingDate')).value = this._ctlRFQClosingDate;
        $get(this.getFormControlID(this._element.id, 'txtDateRequired')).value = this._ctlDateRequired;
        $get(this.getFormControlID(this._element.id, 'txtCustomerDecisionDate')).value = this._ctlCustomerDecisionDate;
        $get(this.getFormControlID(this._element.id, 'txtEau')).value = this._ctlEAU;
        $get(this.getFormControlID(this._element.id, 'txtCompetitorBestoffer')).value = this._ctlCompetitorBestofferHidden;

        //Checkbox Value Bind
        $find(this.getFormControlID(this._element.id, 'chkPartWatch')).setChecked(this._ctlPartWatch);
        $find(this.getFormControlID(this._element.id, 'chkPQA')).setChecked(this._ctlPQA);
        $find(this.getFormControlID(this._element.id, 'chkBOM')).setChecked(this._ctlBOM);
        $find(this.getFormControlID(this._element.id, 'chkOrderToPlace')).setChecked(this._ctlOrderToPlace);

        $find(this.getFormControlID(this._element.id, 'chkAlternativesAccepted')).setChecked(this._radAlternativesAcceptedChK);
        $find(this.getFormControlID(this._element.id, 'chkTestingRequired')).setChecked(this._radTestingRequiredChk);
        $find(this.getFormControlID(this._element.id, 'chkRefirbsAcceptable')).setChecked(this._radRefirbsAcceptableChk);
        $find(this.getFormControlID(this._element.id, 'chkFactorySealedSource')).setChecked(this._radioCheck);
        $find(this.getFormControlID(this._element.id, 'chkRepeatBusiness')).setChecked(this._radRepeatBusinessChk);
        $find(this.getFormControlID(this._element.id, 'chkObsolete')).setChecked(this._radObsoleteChk);
        $find(this.getFormControlID(this._element.id, 'chkLastTimeBuy')).setChecked(this._radLastTimeBuyChk);

        $find(this.getFormControlID(this._element.id, 'ddlCurrency'))._intGlobalCurrencyNo = this._hidCustGCNo;
        $find(this.getFormControlID(this._element.id, 'ddlCurrency'))._blnIsBuy = false;
        $find(this.getFormControlID(this._element.id, 'ddlCurrency')).setValue(this._hidCurrencyID);
        $find(this.getFormControlID(this._element.id, 'ddlCurrency')).getData();
        //[005] ihs set value end
        //this.showField("ctlPartNo", !this._isPoRequest);
        $('#divBlockBoxAlternate').hide();
        if (this._blnFirstTimeShown) {
            this.addSave(Function.createDelegate(this, this.saveClicked));
        }
        //creat error style color

        document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_parterror").style.backgroundColor = "";
        document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_parterror1").style.backgroundColor = "";
        document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_QuantityError").style.backgroundColor = "";
        document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_QuantityError1").style.backgroundColor = "";
        //document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_CustomerTargetPriceError").style.backgroundColor = "";
        //document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_CustomerTargetPriceError1").style.backgroundColor = "";
        document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_ManufacturerError").style.backgroundColor = "";
        document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_ManufacturerError1").style.backgroundColor = "";
        document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_ManufacturerError").style.backgroundColor = "";
        document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_ManufacturerError1").style.backgroundColor = "";
        //document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_ProductError").style.backgroundColor = "";
        //document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_ProductError1").style.backgroundColor = "";
        document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_TraceabilityError").style.backgroundColor = "";
        document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_TraceabilityError1").style.backgroundColor = "";
        document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_ddlTypeError").style.backgroundColor = "";
        document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_ddlTypeError1").style.backgroundColor = "";
        document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_SalespersonError").style.backgroundColor = "";
        document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_SalespersonError1").style.backgroundColor = "";
        document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_CurrencyError").style.backgroundColor = "";
        document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_CurrencyError1").style.backgroundColor = "";
        document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_DateRequiredError").style.backgroundColor = "";
        document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_DateRequiredError1").style.backgroundColor = "";

        //end

        $find(this.getFormControlID(this._element.id, 'ddlBOM'))._intCompanyID = this._intCompanyID;
        $find(this.getFormControlID(this._element.id, 'ddlBOM')).getData();
        $find(this.getFormControlID(this._element.id, 'ddlBOM')).setValue(this._intCompanyID);
        this.allowEditingCurrency(this._blnCurInSameFaimly);


        if (this._IHSProductNo != 0 && this._hidProductID != 0) {

            document.getElementById("lblihsproductAlternate").style.color = 'yellow'
            //document.getElementById("lblMatchproductAlternate").style.color = 'yellow'
            $("#spnIHSProductAlternate").text(this._IHSProduct);
        }
        else if (this._IHSProduct != 'undefined' && this._IHSProduct != '') {
            document.getElementById("lblihsproductAlternate").style.color = 'yellow'
            $("#spnIHSProductAlternate").text(this._IHSProduct);
            document.getElementById("lblMatchproductAlternate").style.color = ''

        }
        else {
            document.getElementById("lblihsproductAlternate").style.color = ''
            $("#spnIHSProductAlternate").text(" ( N/A ) ");
            document.getElementById("spnIHSProductAlternate").style.color = 'yellow'
            document.getElementById("lblMatchproductAlternate").style.color = ''

        }

        if (this._IHSHTSCode != 'undefined' && this._IHSHTSCode != '') {
            document.getElementById("lblihsHTSCodeAlternate").style.color = 'yellow'
            $("#spnHTSCodeAlternate").text(this._hidHTSCode);

        }
        else if (this._hidHTSCode != 'undefined' && this._hidHTSCode != '') {
            document.getElementById("lblihsHTSCodeAlternate").style.color = 'yellow'
            $("#spnHTSCodeAlternate").text(this._hidHTSCode);//text(this._hidHTSCode);
        }
        else {

            document.getElementById("lblihsHTSCodeAlternate").style.color = ''
            $("#spnHTSCodeAlternate").text(" ( N/A ) ");
            document.getElementById("spnHTSCodeAlternate").style.color = 'yellow'
        }
        if (this._IHSDutyCode != "") {
            $("#spnIHSDutyCodeAlternate").text(this._IHSDutyCode);
            document.getElementById("lbldutyAlternate").style.color = 'yellow'
            //alert(this._IHSDutyCode);

        }
        else {
            document.getElementById("lbldutyAlternate").style.color = ''
            //$("#spnIHSDutyCodeAlternate").text(this.IHSDutyCode);
            $("#spnIHSDutyCodeAlternate").text(" ( N/A ) ");
            document.getElementById("spnIHSDutyCodeAlternate").style.color = 'yellow'

        }
        if (this._hidCountryOfOriginNo != 'undefined' && this._hidCountryOfOriginNo != 0) {
            document.getElementById("lblCooAlternate").style.color = 'yellow'
            $("#spnIHSCountryOfOriginAlternate").text(this._hidCountryOfOrigin);
        }
        else {
            document.getElementById("lblCooAlternate").style.color = ''
            $("#spnIHSCountryOfOriginAlternate").text(" ( " + "N/A" + " ) ");
            document.getElementById("spnIHSCountryOfOriginAlternate").style.color = 'yellow'

        }
        if (this._hidLifeCycleStage != 'undefined' && this._hidLifeCycleStage != '') {
            document.getElementById("lblpartstausAlternate").style.color = 'yellow'
            $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_LableLifeStatus").text(this._hidLifeCycleStage);
        }
        else {
            document.getElementById("lblpartstausAlternate").style.color = ''
            $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_LableLifeStatus").text(" ( " + "N/A" + " ) ");
            document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_LableLifeStatus").style.color = 'yellow'

        }

        ////code for ECCN Code
        //if (this._ECCNCode != 'undefined' && this._ECCNCode != '') {
        //    document.getElementById("lblECCNCodeAlternate").style.color = 'yellow'
        //    $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_LableECCNCode").text(this._ECCNCode);
        //}
        //else {
        //    document.getElementById("lblECCNCodeAlternate").style.color = ''
        //    $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_LableECCNCode").text(" ( " + "N/A" + " ) ");
        //    document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_LableECCNCode").style.color = 'yellow'

        //}
        ////code end for ECCN Code
        //code for ECCN Code
        if (this._ECCNCode != 'undefined' && this._ECCNCode != '') {
            document.getElementById("lblECCNCodeAlternate").style.color = 'yellow'
            //$("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_LableECCNCode").text(this._ECCNCode);
            //this.SelectIHSPartEccnMappedFormLoad(this._ECCNCode, this._ctlPartNo);
            this.SelectIHSEccnCode(this._ECCNCode);
        }
        else {
            document.getElementById("lblECCNCodeAlternate").style.color = ''
            $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_LableECCNCode").text(" ( " + "N/A" + " ) ");
            document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_LableECCNCode").style.color = 'yellow'

        }
        //code end for ECCN Code

        //this._ECCNCode


        document.getElementById("myplasegrdeAlternate").addEventListener("click", Function.createDelegate(this, this.getPartDetaildata));
        document.getElementById("closePoppartdetailsAlternate").addEventListener("click", Function.createDelegate(this, this.hidpartdetaildive));

        $R_FN.showElement(this._pnlPartDetail, false);
        $('.dropDownRefresh').hide();
        if ($find(this.getFormControlID(this._element.id, 'cmbManufacturer'))) $find(this.getFormControlID(this._element.id, 'cmbManufacturer'))._aut.addSelectionMadeEvent(Function.createDelegate(this, this.RsMfrChanged));
        document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_cmbManufactureraut_ctl04").addEventListener("click", Function.createDelegate(this, this.ResetMFR));
    },
    getIHSPartDetails: function () {
        var id = $find(this.getFormControlID(this._element.id, 'cmbIHS'))._aut._varSelectedValue;
        var arr = $find(this.getFormControlID(this._element.id, 'cmbIHS'))._aut._varSelectedValue.split("->");
        $find(this.getFormControlID(this._element.id, 'cmbIHS'))._txt.value = arr[0];
        this.SaveIHSPartDetail($find(this.getFormControlID(this._element.id, 'cmbIHS'))._aut._varSelectedExtraData);

    },
    SaveIHSPartDetail: function (IHSResult) {
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/CusReqAdd");
        obj.set_DataObject("CusReqAdd");
        obj.set_DataAction("SaveIHSPartDetail");
        obj.addParameter("IHSResult", IHSResult);
        obj.addDataOK(Function.createDelegate(this, this.setIHSPartDetail));
        obj.addError(Function.createDelegate(this, this.IHSPartDetailError));
        obj.addTimeout(Function.createDelegate(this, this.IHSPartDetailError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;


    },
    setIHSPartDetail: function (args) {
        var obj = args._result.Result
        if (obj[0].IHSPartsId > 0) {
            $("#lblihsproductAlternate").show();
            $("#lblihsHTSCodeAlternate").show();
            $("#lbldutyAlternate").show();
            $("#lblCooAlternate").show();
            var PartName = obj[0].ID;
            if (PartName != 'undefined ' && PartName.length > 0) {
                //$get(this.getFormControlID(this._element.id, 'txtPartNo')).value = $R_FN.setCleanTextValue(PartName);
                $get(this.getFormControlID(this._element.id, 'cmbIHS12txt')).value = $R_FN.setCleanTextValue(PartName);
                document.getElementById("ParttypeSearch1Alternate").style.color = 'yellow'
            }
            else {
                document.getElementById("ParttypeSearch1Alternate").style.color = ''
            }
            if (obj[0].ROHSName != 'undefined ' && obj[0].ROHSName != '' && obj[0].ROHSName.length > 0) {
                // $("#spnMSLAlternate").show();
                $("#spnMSLAlternate").text(" ( " + obj[0].ROHSName + " ) ");
                $find(this.getFormControlID(this._element.id, 'ddlMsl')).setValue(obj[0].ROHSName);
                document.getElementById("lblmlsAlternate").style.color = 'yellow';
                if (obj[0].ROHSName == 'N/A')
                    document.getElementById("spnMSLAlternate").style.color = 'yellow';
                else
                    document.getElementById("spnMSLAlternate").style.color = '';
            }
            else {
                $("#spnMSLAlternate").text(" ( " + "N/A" + " ) ");
                $find(this.getFormControlID(this._element.id, 'ddlMsl')).setValue("N/A");
                document.getElementById("spnMSLAlternate").style.color = 'yellow';
                document.getElementById("lblmlsAlternate").style.color = 'yellow';





            }
            if (obj[0].Manufacturer != 'undefined ' && obj[0].Manufacturer != '' && obj[0].Manufacturer.length > 0) {
                if (obj[0].ManufacturerNo != 0) {
                    $find(this.getFormControlID(this._element.id, 'cmbManufacturer')).setValue(obj[0].ManufacturerNo, $R_FN.setCleanTextValue(obj[0].Manufacturer));
                    $("#spnManufacturerAlternate").hide();
                    document.getElementById("lblmrfAlternate").style.color = 'yellow'
                }
                else {

                    $find(this.getFormControlID(this._element.id, 'cmbManufacturer')).setValue("", 0);
                    $("#spnManufacturerAlternate").show();
                    $("#spnManufacturerAlternate").text(" (" + obj[0].ManufacturerFullName + " ) ");
                    document.getElementById("lblmrfAlternate").style.color = ''
                }

            }
            else {

                $("#spnManufacturerAlternate").hide();

            }

            if (obj[0].ProdDesc != 'undefined ' && obj[0].IHSProdDesc.length > 0) {

                if (obj[0].ProdNo != 0) {
                    $find(this.getFormControlID(this._element.id, 'cmbProducts')).setValue(obj[0].ProdNo, $R_FN.setCleanTextValue(obj[0].ProdDesc));
                    //$("#spnManufacturerAlternate").hide();
                    document.getElementById("lblMatchproductAlternate").style.color = 'yellow';
                    $("#spnIHSProductAlternate").text($R_FN.setCleanTextValue(obj[0].IHSProdDesc));
                    document.getElementById("lblihsproductAlternate").style.color = 'yellow';
                }
                else {
                    $find(this.getFormControlID(this._element.id, 'cmbProducts')).setValue("", 0);
                    $("#spnIHSProductAlternate").show();
                    $("#spnIHSProductAlternate").text($R_FN.setCleanTextValue(obj[0].IHSProdDesc));
                    document.getElementById("lblihsproductAlternate").style.color = 'yellow';
                }

            }
            else {
                if (obj[0].HTSCode != '' || obj[0].IHSProdDes != '') {
                    $("#spnIHSProductAlternate").show();
                    $("#spnIHSProductAlternate").text(" ( " + "N/A" + " ) ");
                    document.getElementById("lblihsproductAlternate").style.color = 'yellow';
                    document.getElementById("spnIHSProductAlternate").style.color = 'yellow';
                } else {
                    document.getElementById("lblihsproductAlternate").style.color = '';
                }
            }

            if (obj[0].PackageId > 0) {
                $("#lblECCNCodeAlternate").show();
                // $find(this.getFormControlID(this._element.id, 'ddlPackage')).setValue(obj[0].PackageId);
                $find(this.getFormControlID(this._element.id, 'cmbPackage')).setValue(obj[0].PackageId, $R_FN.setCleanTextValue(obj[0].PackageDescription));
                $("#spnPackageAlternate").text(" ( " + obj[0].Packaging + " ) ");
                document.getElementById('lblPackageAlternate').style.color = "yellow";
                document.getElementById("spnPackageAlternate").style.color = "";


            }
            else {
                $("#lblECCNCodeAlternate").show();
                $("#spnPackageAlternate").text("  ( " + "N/A" + " ) ");
                document.getElementById('lblPackageAlternate').style.color = "";
                document.getElementById("spnPackageAlternate").style.color = 'yellow'
                // $find(this.getFormControlID(this._element.id, 'ddlPackage')).setValue(0);
                $find(this.getFormControlID(this._element.id, 'cmbPackage')).setValue("", 0);

            }

            if (obj[0].HTSCode != 'undefined ' && obj[0].HTSCode != '' && obj[0].HTSCode.length > 0) {
                $("#spnHTSCodeAlternate").show();
                $("#spnHTSCodeAlternate").text(obj[0].HTSCode);
                document.getElementById("lblihsHTSCodeAlternate").style.color = 'yellow'
            }
            else {
                if (obj[0].HTSCode != '' || obj[0].IHSProdDes != '') {
                    $("#spnHTSCodeAlternate").show();
                    $("#spnHTSCodeAlternate").text(" ( " + "N/A" + " ) ");
                    document.getElementById("lblihsHTSCodeAlternate").style.color = 'yellow'
                    document.getElementById("spnHTSCodeAlternate").style.color = 'yellow'
                } else {
                    document.getElementById("lblihsHTSCodeAlternate").style.color = ''
                    document.getElementById("spnHTSCodeAlternate").style.color = ''
                }

            }

            if (obj[0].IHSDutyCode != 'undefined ' && obj[0].IHSDutyCode != '' && obj[0].IHSDutyCode.length > 0) {
                $("#spnIHSDutyCodeAlternate").show();
                $("#spnIHSDutyCodeAlternate").text(" ( " + obj[0].IHSDutyCode + " ) ");
                document.getElementById("lbldutyAlternate").style.color = 'yellow'
                document.getElementById("spnIHSDutyCodeAlternate").style.color = ''

            }
            else {
                $("#spnIHSDutyCodeAlternate").text("( N/A )");
                document.getElementById("lbldutyAlternate").style.color = ''
                document.getElementById("spnIHSDutyCodeAlternate").style.color = 'yellow'
            }
            if (obj[0].CountryOfOrigin != 'undefined ' && obj[0].CountryOfOrigin != '' && obj[0].CountryOfOrigin.length > 0) {
                $("#spnIHSCountryOfOriginAlternate").show();
                $("#spnIHSCountryOfOriginAlternate").text(obj[0].CountryOfOrigin);
                document.getElementById("lblCooAlternate").style.color = 'yellow'
                document.getElementById("spnIHSCountryOfOriginAlternate").style.color = ''
            }
            else {
                $("#spnIHSCountryOfOriginAlternate").text(" ( " + "N/A" + " ) ");
                document.getElementById("lblCooAlternate").style.color = ''
                document.getElementById("spnIHSCountryOfOriginAlternate").style.color = 'yellow'
            }

            if (obj[0].PartStatus != 'undefined ' && obj[0].PartStatus != '' && obj[0].PartStatus.length > 0) {
                //$get(this.getFormControlID(this._element.id, 'txtLifeStatus')).value = $R_FN.setCleanTextValue(obj[0].PartStatus);
                $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_LableLifeStatus").text($R_FN.setCleanTextValue(obj[0].PartStatus));
                document.getElementById("lblpartstausAlternate").style.color = 'yellow'
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_LableLifeStatus").style.color = ''
            }
            else {
                //$get(this.getFormControlID(this._element.id, 'txtLifeStatus')).value = "";
                $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_LableLifeStatus").text(" ( " + "N/A" + " ) ");
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_LableLifeStatus").style.color = 'yellow'
                document.getElementById("lblpartstausAlternate").style.color = ''
            }
            //ECCN Code start
            if (obj[0].ECCNCode != 'undefined ' && obj[0].ECCNCode != '' && obj[0].ECCNCode.length > 0) {
                $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_LableECCNCode").text($R_FN.setCleanTextValue(obj[0].ECCNCode));
                document.getElementById("lblECCNCodeAlternate").style.color = 'yellow'
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_LableECCNCode").style.color = ''
            }
            else {
                $("#ctl00_cphMain_ctlAdd_ctlDB_ctl14_ctlAdd_ctlDB_LableECCNCode").text(" ( " + "N/A" + " ) ");
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_LableECCNCode").style.color = 'yellow'
                document.getElementById("lblECCNCodeAlternate").style.color = ''
            }
            //ECCN Code End

            if (obj[0].Packaging != 'undefined ' && obj[0].Packaging != '') {
                this._hidPackaging = obj[0].Packaging;
            }
            else {
                this._hidPackaging = "";
            }
            if (obj[0].PackagingSize != 'undefined ' && obj[0].PackagingSize != '') {
                this._hidPackagingSize = obj[0].PackagingSize;
            }
            else {
                this._hidPackagingSize = "";
            }
            this._hidCountryOfOrigin = obj[0].CountryOfOrigin;
            this._hidCountryOfOriginNo = obj[0].CountryOfOriginNo;
            this._hidLifeCycleStage = obj[0].PartStatus;
            this._hidHTSCode = obj[0].HTSCode;
            this._hidAveragePrice = obj[0].AveragePrice;
            this._hidDescriptions = obj[0].Descriptions;
            this._IHSPartsId = obj[0].IHSPartsId;
            this._ihsCurrencyCode = obj[0].ihsCurrencyCode;
            this._hidIHSProduct = obj[0].IHSProduct;
            this._hidECCNCode = obj[0].ECCNCode;

        }

    },
    IHSPartDetailError: function (args) {
    },

    //Part Eccn Mapping strat code
    SelectIHSPartEccnMapped: function (IHSEccnCode, PartName) {
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/CusReqAdd");
        obj.set_DataObject("CusReqAdd");
        obj.set_DataAction("GetIHSPartEccnDetail");
        obj.addParameter("PartEccnCode", IHSEccnCode);
        obj.addParameter("PartNo", PartName);
        obj.addDataOK(Function.createDelegate(this, this.setIHSPartEccnDetail));
        obj.addError(Function.createDelegate(this, this.IHSPartECCNDetailError));
        obj.addTimeout(Function.createDelegate(this, this.IHSPartECCNDetailError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;


    },
    setIHSPartEccnDetail: function (args) {
        var obj = args._result.Result
        if (obj[0].PartEccnMappedId > 0) {
            //ECCN Code start
            if (obj[0].ECCNCode != 'undefined' && obj[0].ECCNCode != '' && obj[0].ECCNCode.length > 0) {

                $find(this.getFormControlID(this._element.id, 'cmbPartEccnMapped')).setValue(obj[0].ECCNNo, $R_FN.setCleanTextValue(obj[0].ECCNCode));
                this._hidECCNCode = obj[0].ECCNCode;

                $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_cmbPartEccnMappedaut").show();
                $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_cmbPartEccnMappedtxt").show();
                document.getElementById("lblECCNCodeAlternate").style.color = 'yellow'

            }


        }
        else if (this._hidECCNCode != '') {

            this.SelectIHSEccnCode(this._hidECCNCode);
            $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_cmbPartEccnMappedaut").show();
            $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_cmbPartEccnMappedtxt").show();

        }
        else {
            // alert(3);
            $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_LableECCNCode").text(" ( " + "N/A" + " ) ");
            document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_LableECCNCode").style.color = 'yellow'
            document.getElementById("lblECCNCodeAlternate").style.color = ''
            this._hidECCNCode = "";
            $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_cmbPartEccnMappedaut").show();
            $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_cmbPartEccnMappedtxt").show();
        }

    },
    IHSPartECCNDetailError: function (args) {
    },
    //code end


    //Bind Eccn Code from TbECCN Table strat code
    SelectIHSEccnCode: function (sEccnCode) {
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/CusReqAdd");
        obj.set_DataObject("CusReqAdd");
        obj.set_DataAction("GetIHSEccnCodeDetail");
        obj.addParameter("ECCNCode", sEccnCode);
        obj.addDataOK(Function.createDelegate(this, this.setIHSEccnCodeDetail));
        obj.addError(Function.createDelegate(this, this.IHSECCNCodeDetailError));
        obj.addTimeout(Function.createDelegate(this, this.IHSECCNCodeDetailError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;


    },
    setIHSEccnCodeDetail: function (args) {

        var obj = args._result.Result
        if (obj[0].ECCNNo > 0) {
            //ECCN Code start
            if (obj[0].ECCNCode != '') {

                $find(this.getFormControlID(this._element.id, 'cmbPartEccnMapped')).setValue(obj[0].ECCNNo, $R_FN.setCleanTextValue(obj[0].ECCNCode));
                document.getElementById("lblECCNCodeAlternate").style.color = 'yellow'

                this._hidECCNCode = obj[0].ECCNCode;
                $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_cmbPartEccnMappedaut").show();
                $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_cmbPartEccnMappedtxt").show();
            }
            else {
                $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_LableECCNCode").text(" ( " + "N/A" + " ) ");
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_LableECCNCode").style.color = 'yellow'
                document.getElementById("lblECCNCodeAlternate").style.color = ''
                this._hidECCNCode = "";
                $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_cmbPartEccnMappedaut").show();
                $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_cmbPartEccnMappedtxt").show();

            }
        }
        else {
            //$find('ctl00_cphMain_ctlNotify_ctlDB_ctl14_ctlNotify_ctlDB_ctlSalespersion_ctl03_aut').reselect();
            $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_LableECCNCode").text(" ( " + "N/A" + " ) ");
            document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_LableECCNCode").style.color = 'yellow'
            document.getElementById("lblECCNCodeAlternate").style.color = ''
            this._hidECCNCode = "";
            $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_cmbPartEccnMappedaut").show();
            $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_cmbPartEccnMappedtxt").show();
        }

    },
    IHSECCNCodeDetailError: function (args) {
    },

    //code end
    //Part Eccn Mapping strat code
    SelectIHSPartEccnMappedFormLoad: function (IHSEccnCode, PartName) {
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/CusReqAdd");
        obj.set_DataObject("CusReqAdd");
        obj.set_DataAction("GetIHSPartEccnDetail");
        obj.addParameter("PartEccnCode", IHSEccnCode);
        obj.addParameter("PartNo", PartName);
        obj.addDataOK(Function.createDelegate(this, this.setIHSPartEccnDetailFormLoad));
        obj.addError(Function.createDelegate(this, this.IHSPartECCNDetailErrorFormLoad));
        obj.addTimeout(Function.createDelegate(this, this.IHSPartECCNDetailErrorFormLoad));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;


    },
    setIHSPartEccnDetailFormLoad: function (args) {
        var obj = args._result.Result
        if (obj[0].PartEccnMappedId > 0) {
            //ECCN Code start
            if (obj[0].ECCNCode != 'undefined' && obj[0].ECCNCode != '' && obj[0].ECCNCode.length > 0) {

                $find(this.getFormControlID(this._element.id, 'cmbPartEccnMapped')).setValue(obj[0].ECCNNo, $R_FN.setCleanTextValue(obj[0].ECCNCode));
                this._hidECCNCode = obj[0].ECCNCode;

                $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_cmbPartEccnMappedaut").show();
                $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_cmbPartEccnMappedtxt").show();
                document.getElementById("lblECCNCodeAlternate").style.color = 'yellow'

            }


        }
        else if (this._ECCNCode != '') {

            this.SelectIHSEccnCodeFormLoad(this._ECCNCode);
            $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_cmbPartEccnMappedaut").show();
            $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_cmbPartEccnMappedtxt").show();

        }
        else {
            // alert(3);
            $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_LableECCNCode").text(" ( " + "N/A" + " ) ");
            document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_LableECCNCode").style.color = 'yellow'
            document.getElementById("lblECCNCodeAlternate").style.color = ''
            this._hidECCNCode = "";
            $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_cmbPartEccnMappedaut").show();
            $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_cmbPartEccnMappedtxt").show();
        }

    },
    IHSPartECCNDetailErrorFormLoad: function (args) {
    },
    //code end


    //Bind Eccn Code from TbECCN Table strat code
    SelectIHSEccnCodeFormLoad: function (sEccnCode) {
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/CusReqAdd");
        obj.set_DataObject("CusReqAdd");
        obj.set_DataAction("GetIHSEccnCodeDetail");
        obj.addParameter("ECCNCode", sEccnCode);
        obj.addDataOK(Function.createDelegate(this, this.setIHSEccnCodeDetailFormLoad));
        obj.addError(Function.createDelegate(this, this.IHSECCNCodeDetailErrorFormLoad));
        obj.addTimeout(Function.createDelegate(this, this.IHSECCNCodeDetailErrorFormLoad));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;


    },
    setIHSEccnCodeDetailFormLoad: function (args) {

        var obj = args._result.Result
        if (obj[0].ECCNNo > 0) {
            //ECCN Code start
            if (obj[0].ECCNCode != '') {

                $find(this.getFormControlID(this._element.id, 'cmbPartEccnMapped')).setValue(obj[0].ECCNNo, $R_FN.setCleanTextValue(obj[0].ECCNCode));
                document.getElementById("lblECCNCodeAlternate").style.color = 'yellow'

                this._hidECCNCode = obj[0].ECCNCode;
                $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_cmbPartEccnMappedaut").show();
                $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_cmbPartEccnMappedtxt").show();
            }
            else {
                $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_LableECCNCode").text(" ( " + "N/A" + " ) ");
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_LableECCNCode").style.color = 'yellow'
                document.getElementById("lblECCNCodeAlternate").style.color = ''
                this._hidECCNCode = "";
                $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_cmbPartEccnMappedaut").show();
                $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_cmbPartEccnMappedtxt").show();

            }
        }
        else {
            //$find('ctl00_cphMain_ctlNotify_ctlDB_ctl14_ctlNotify_ctlDB_ctlSalespersion_ctl03_aut').reselect();
            $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_LableECCNCode").text(" ( " + "N/A" + " ) ");
            document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_LableECCNCode").style.color = 'yellow'
            document.getElementById("lblECCNCodeAlternate").style.color = ''
            this._hidECCNCode = "";
            $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_cmbPartEccnMappedaut").show();
            $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_cmbPartEccnMappedtxt").show();
        }

    },
    IHSECCNCodeDetailErrorFormLoad: function (args) {
    },
    //code end

    ClearIhsData: function () {
        $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_cmbPartEccnMappedaut").hide();
        $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_cmbPartEccnMappedtxt").hide();
        $("#spanmfrEditAlternate").text("");
        //$("#spanmfr").text("");
        document.getElementById("lblMatchproductAlternate").style.color = ''
        $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_cmbIHS12aut_hypClose")[0].click();
        $("#lblIhsServiceMessageAlternate").text("");
        $("#lblservicemsgerrorAlternate").text("");
        $get(this.getFormControlID(this._element.id, 'cmbIHS12txt')).value = "";
        $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_LableLifeStatus").text("");
        document.getElementById("ParttypeSearch1Alternate").style.color = '';
        document.getElementById("lblmlsAlternate").style.color = '';
        $find(this.getFormControlID(this._element.id, 'ddlMsl')).setValue(0);
        $find(this.getFormControlID(this._element.id, 'cmbManufacturer')).setValue("", 0);
        document.getElementById("lblmrfAlternate").style.color = '';
        $find(this.getFormControlID(this._element.id, 'cmbProducts')).setValue("", 0);
        document.getElementById("lblihsproductAlternate").style.color = '';
        document.getElementById('lblPackageAlternate').style.color = "";
        document.getElementById("lblihsproductAlternate").style.color = '';
        document.getElementById("lbldutyAlternate").style.color = '';
        $("#spnMSLAlternate").text("");
        $("#spnManufacturerAlternate").text("");
        $("#spnIHSProductAlternate").text("");
        $("#spnPackageAlternate").text("");
        $("#spnHTSCodeAlternate").text("");
        $("#spnIHSDutyCodeAlternate").text("");
        $("#spnIHSCountryOfOriginAlternate").text("");
        document.getElementById("lbldutyAlternate").style.color = '';
        document.getElementById("lblpartstausAlternate").style.color = '';
        document.getElementById("lblCooAlternate").style.color = '';
        document.getElementById("lblihsHTSCodeAlternate").style.color = '';

        document.getElementById("spnMSLAlternate").style.color = ''
        document.getElementById("spnIHSProductAlternate").style.color = '';
        document.getElementById("spnPackageAlternate").style.color = ''
        document.getElementById("spnHTSCodeAlternate").style.color = ''
        document.getElementById("spnIHSDutyCodeAlternate").style.color = ''
        document.getElementById("spnIHSCountryOfOriginAlternate").style.color = ''
        document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_LableECCNCode").style.color = '';
        document.getElementById("lblECCNCodeAlternate").style.color = '';
        document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_LableLifeStatus").style.color = ''
        $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_LableECCNCode").text("");
        //lblCooAlternate
        //lblihsHTSCodeAlternate
        this._hidPackaging = "";
        this._hidPackagingSize = "";
        this._hidCountryOfOrigin = "";
        this._hidCountryOfOriginNo = ""
        this._hidLifeCycleStage = "";
        this._hidHTSCode = "";
        this._hidAveragePrice = "";
        this._hidDescriptions = "";
        this._IHSPartsId = "";
        this._ihsCurrencyCode = "";
        this._hidIHSProduct = "";
        this._hidECCNCode = "";
        $find(this.getFormControlID(this._element.id, 'cmbPackage')).setValue("", 0);
        //this.onSaveComplete();

    },
    ClearIhsDataOnLoad: function () {
        document.getElementById("lblMatchproductAlternate").style.color = ''
        document.getElementById("ParttypeSearch1Alternate").style.color = '';
        document.getElementById("lblmlsAlternate").style.color = '';
        document.getElementById("lblmrfAlternate").style.color = '';
        document.getElementById('lblPackageAlternate').style.color = "";
        $("#spnMSLAlternate").text("");
        $("#spnManufacturerAlternate").text("");
        $("#spnIHSProductAlternate").text("");
        $("#spnPackageAlternate").text("");

    },
    changeSearchType: function () {
        this.setSearchType(this._enmSearchType + 1);
    },
    setSearchType: function (i) {
        this._enmSearchType = i;
        if (this._enmSearchType > 2) this._enmSearchType = 0;
        this.showSearchType();
    },
    showSearchType: function () {
        if (this._enmSearchType == 0) {
            $("#ParttypeSearch1Alternate").removeClass("searchType_StartsWith");
            $("#ParttypeSearch1Alternate").removeClass("searchType_ExactWith");
            $("#ParttypeSearch1Alternate").addClass("searchType_Contains");
            $("#ParttypeSearch1Alternate").attr("title", "Contains");
            $("#ParttypeSearch1Alternate").attr("alt", "Contains");
            $find(this.getFormControlID(this._element.id, 'cmbIHS'))._aut._txtGroup = "contains";

        }
        if (this._enmSearchType == 1) {
            $("#ParttypeSearch1Alternate").removeClass("searchType_Contains");
            $("#ParttypeSearch1Alternate").removeClass("searchType_ExactWith");
            $("#ParttypeSearch1Alternate").addClass("searchType_StartsWith");
            $("#ParttypeSearch1Alternate").attr("title", "Startswith");
            $("#ParttypeSearch1Alternate").attr("alt", "Startswith");
            $find(this.getFormControlID(this._element.id, 'cmbIHS'))._aut._txtGroup = "startswith";

        }


        if (this._enmSearchType == 2) {
            $("#ParttypeSearch1Alternate").removeClass("searchType_StartsWith");
            $("#ParttypeSearch1Alternate").removeClass("searchType_Contains");
            $("#ParttypeSearch1Alternate").addClass("searchType_ExactWith");
            $("#ParttypeSearch1Alternate").attr("title", "Exact");
            $("#ParttypeSearch1Alternate").attr("alt", "Exact");
            $find(this.getFormControlID(this._element.id, 'cmbIHS'))._aut._txtGroup = "exact";

        }


    },
    ResetMFR: function () {
        $("#spanmfrEditAlternate").text("");
    },
    RsMfrChanged: function () {
        this._intManufacturerNo = this.getControlValue(this.getFormControlID(this._element.id, 'cmbManufacturer'), 'Combo')
        this.getRsMfr(this._intManufacturerNo);

    },
    getRsMfr: function (RsManufacturerNo) {
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/CusReqAdd");
        obj.set_DataObject("CusReqAdd");
        obj.set_DataAction("GetRestrictedManufacturer");
        obj.addParameter("RsManufacturerNo", RsManufacturerNo);
        obj.addDataOK(Function.createDelegate(this, this.getRsMfrOK));
        obj.addError(Function.createDelegate(this, this.getRsMfrError));
        obj.addTimeout(Function.createDelegate(this, this.getRsMfrError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },
    getRsMfrOK: function (args) {
        var res = args._result;
        this._RestrictedMFRMessage = res.RestrictedMFRMessage;
        this._isRestrictedManufacturer = res.isRestrictedManufacturer;
        if (this._isRestrictedManufacturer > 0 && this._isRestrictedManufacturer != "undefined") {
            $("#lblRsMFREditAlternate").show();
            $("#spanmfrEditAlternate").text(this._RestrictedMFRMessage);

        } else { $("#lblRsMFREditAlternate").hide(); }


    },
    getRsMfrError: function () {
        //this.setFieldValue($find(this.getFormControlID(this._element.id, 'cmbManufacturer')), "");
        // this.showShipViaFieldsLoading(false);
    },
    SearchTypePopup: function () {
        $('input:radio[name="searchType"][value="contains"]').prop('checked', true);
        $R_FN.showElement(this._pnlPartDetail, false);
        this._ctltblPartdetails._tblResults.clearTable();
        $("#myModalAlternate").show();
        var strPart = $R_FN.setCleanTextValue($get(this.getFormControlID(this._element.id, 'cmbIHS12txt')).value);
        $get(this.getFormControlID(this._element.id, 'SearchtxtPartNo')).value = strPart;
        //this.loadIHSGrid();
        this.Toggle1();

        document.getElementsByClassName("dataFilter")[1].style.display = "none";
        if (document.getElementsByClassName("itemSearchGo")[1])
            document.getElementsByClassName("itemSearchGo")[1].style.display = "none";

        // $R_FN.showElement(document.getElementById("ctl00_cphMain_ctlAdd_ctlDB_ctl14_ctlAdd_ctlDB_btnOK_hyp"), false);
        $R_FN.showElement(document.getElementById("okbtnAlternate"), false);
        $('.okbtnAlternate').hide();


    },
    loadIHSGrid: function () {
        $("#lblihserrorAlternate").text("");
        this._ctltblPartdetails._tblResults.clearTable();
        this._ctltblPartdetails._tblResults.resizeColumns();
        this._ctltblPartdetails.showNoneFound(true);
        this._ctltblPartdetails.resetToFirstPage();
        $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_ctltblPartdetails_ctlDB_tblOuter").hide();
        $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_ctltblPartdetails_ctlDB_ctl03_hyp").hide();

        this._ctltblPartdetails.setFieldValue("ctlSearchtxtPartNo", $R_FN.setCleanTextValue($get(this.getFormControlID(this._element.id, 'SearchtxtPartNo')).value));
        this._ctltblPartdetails._strpart = $R_FN.setCleanTextValue($get(this.getFormControlID(this._element.id, 'SearchtxtPartNo')).value);
        this._ctltblPartdetails._searchType = $("input[name='searchType']:checked").val();
        this._ctltblPartdetails.getData();
        //$R_FN.showElement(document.getElementById("ctl00_cphMain_ctlAdd_ctlDB_ctl14_ctlAdd_ctlDB_btnOK_hyp"), true);
        //$('.okbtnAlternate').show();

    },
    Canceltop: function () {
        $("#myModalAlternate").hide();
        this.Toggle3();
    },
    Cancelbottom: function () {
        $("#myModalAlternate").hide();
        this.Toggle3();

    },
    hidpartdetaildive: function () {
        this.showDetailDiv(false);


    },
    Toggle3: function () {
        // this._tblPartdetails.clearTable();
        // this.showField("ctlPartDetail", false);
        //this.showDetailDiv(false);

    },
    dispose: function () {
        if (this.isDisposed) return;
        this._intCustomerRequirementID = null;
        this._arySources = null;
        this._radFactorySealedSource = null;
        this._radObsolete = null;
        this._radLastTimeBuy = null;
        this._radRefirbsAcceptable = null;
        this._radTestingRequired = null;
        this._blnCurInSameFaimly = true;
        this._intCurrencyNo = null;
        this._radRepeatBusiness = null;
        this._radAlternativesAccepted = null;
        //this._tblPartdetails = null;
        this._btn1 = null;
        this._btn2 = null;
        this._lblError = null;
        this._hidCountryOfOrigin = null;
        this._hidCountryOfOriginNo = null;
        this._hidLifeCycleStage = null;
        this._hidHTSCode = null;
        this._hidAveragePrice = null;
        this._hidPackaging = null;
        this._hidPackagingSize = null;
        this._hidDescriptions = null;
        this._pnlPartDetail = null;
        this._hidBOMID = null;
        this._IHSProductNo = null;
        this._IHSProduct = null;
        this._HTSCode = null;
        this._CountryOfOrigin = null;
        this._IHSDutyCode = null;
        this._AlternateStatus = null;
        this.chkAlternatives = null;
        this._ECCNCode = null;
        this._ctltblPartdetails = null;
        this._strpart = null;
        this._searchType = null;
        this._enmSearchType = 0;
        Rebound.GlobalTrader.Site.Controls.Forms.CustomerRequirementMainInfo_Edit.callBaseMethod(this, "dispose");
    },
    findWhichTypeSelected: function (radChk) {
        for (var i = 0; i < this._arySources.length; i++) {
            var rad = $get(String.format("{0}_{1}", radChk.id, i));

            if (rad.checked) {
                return this._arySources[i];
            }
            else if (i == this._arySources.length - 1) {
                return "false";
            }

        }
    },
    Toggle1: function () {
        $("#lblIhsServiceMessageAlternate").text("");
        $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_ctltblPartdetails_ctlDB_ctl08").hide();
        if ($("input[name='searchType']:checked").val() == "exact".trim()) {
            var strexact = $get(this.getFormControlID(this._element.id, 'SearchtxtPartNo')).value;
            if (strexact.length > 2) {
                $("#lblihserrorAlternate").text("");
                $('#divLoaderAlternate').show();
                $R_FN.showElement(this._pnlPartDetail, true);
                this.showProductLoading(true);
                var obj = new Rebound.GlobalTrader.Site.Data();
                obj.set_PathToData("controls/Nuggets/CusReqAdd");
                obj.set_DataObject("CusReqAdd");
                obj.set_DataAction("GetDataGrid");
                obj.addParameter("partsearch", $get(this.getFormControlID(this._element.id, 'SearchtxtPartNo')).value);
                obj.addParameter("searchType", $("input[name='searchType']:checked").val());
                obj.addDataOK(Function.createDelegate(this, this.getDataGrid));
                obj.addError(Function.createDelegate(this, this.getDataGridError));
                obj.addTimeout(Function.createDelegate(this, this.getDataGridError));
                $R_DQ.addToQueue(obj);
                $R_DQ.processQueue();
                obj = null;

            }
            else {
                $("#lblihserrorAlternate").text("The search term must contain at least 3 alphanumeric characters. Examples are '2n222', 'F245', etc.");
                $R_FN.showElement(this._lblError, true);
                $R_FN.showElement(this._pnlPartDetail, false);
                this._ctltblPartdetails._tblResults.clearTable();
                this._ctltblPartdetails._tblResults.resizeColumns();
                this._ctltblPartdetails.showNoneFound(true);
                return;

            }
        }
        if ($("input[name='searchType']:checked").val() == "startswith".trim()) {
            var strexact = $get(this.getFormControlID(this._element.id, 'SearchtxtPartNo')).value;
            if (strexact.length > 2) {
                $("#lblihserrorAlternate").text("");
                $('#divLoaderAlternate').show();
                $R_FN.showElement(this._pnlPartDetail, true);
                this.showProductLoading(true);
                var obj = new Rebound.GlobalTrader.Site.Data();
                obj.set_PathToData("controls/Nuggets/CusReqAdd");
                obj.set_DataObject("CusReqAdd");
                obj.set_DataAction("GetDataGrid");
                obj.addParameter("partsearch", $get(this.getFormControlID(this._element.id, 'SearchtxtPartNo')).value);
                obj.addParameter("searchType", $("input[name='searchType']:checked").val());
                obj.addDataOK(Function.createDelegate(this, this.getDataGrid));
                obj.addError(Function.createDelegate(this, this.getDataGridError));
                obj.addTimeout(Function.createDelegate(this, this.getDataGridError));
                $R_DQ.addToQueue(obj);
                $R_DQ.processQueue();
                obj = null;

            }
            else {
                $("#lblihserrorAlternate").text("The search term must contain at least 3 alphanumeric characters. Examples are '2n222', 'F245', etc.");
                $R_FN.showElement(this._lblError, true);
                $R_FN.showElement(this._pnlPartDetail, false);
                this._ctltblPartdetails._tblResults.clearTable();
                this._ctltblPartdetails._tblResults.resizeColumns();
                this._ctltblPartdetails.showNoneFound(true);
                return;

            }
        }
        if ($("input[name='searchType']:checked").val() == "contains".trim()) {
            var strexact = $get(this.getFormControlID(this._element.id, 'SearchtxtPartNo')).value;
            if (strexact.length > 3) {
                $("#lblihserrorAlternate").text("");
                $('#divLoaderAlternate').show();
                $R_FN.showElement(this._pnlPartDetail, true);
                this.showProductLoading(true);
                var obj = new Rebound.GlobalTrader.Site.Data();
                obj.set_PathToData("controls/Nuggets/CusReqAdd");
                obj.set_DataObject("CusReqAdd");
                obj.set_DataAction("GetDataGrid");
                obj.addParameter("partsearch", $get(this.getFormControlID(this._element.id, 'SearchtxtPartNo')).value);
                obj.addParameter("searchType", $("input[name='searchType']:checked").val());
                obj.addDataOK(Function.createDelegate(this, this.getDataGrid));
                obj.addError(Function.createDelegate(this, this.getDataGridError));
                obj.addTimeout(Function.createDelegate(this, this.getDataGridError));
                $R_DQ.addToQueue(obj);
                $R_DQ.processQueue();
                obj = null;

            }
            else {
                $("#lblihserrorAlternate").text("The search term must contain at least 4 alphanumeric characters. Examples are '2n222', 'F245', etc.");
                $R_FN.showElement(this._lblError, true);
                $R_FN.showElement(this._pnlPartDetail, false);
                this._ctltblPartdetails._tblResults.clearTable();
                this._ctltblPartdetails._tblResults.resizeColumns();
                this._ctltblPartdetails.showNoneFound(true);
                return;

            }
        }

    },
    getIHSDataSelected: function () {
        $('.okbtnAlternate').show();
    },
    getValuesByPartsOnClick: function () {
        var PartNo = this._ctltblPartdetails._tblResults._varSelectedValue;
        if (PartNo.length > 0) {
            this.getValuesByParts();

        }
        // this.Canceltop();
    },
    getValuesByParts: function () {
        var obj = this._ctltblPartdetails._tblResults.getSelectedExtraData(); if (!obj) return;
        this.getIHSValuesByParts(obj.IHSPartsId, obj.ROHSName, obj.ROHSNo, obj.Manufacturer, obj.IHSProdDesc, obj.Packaging, obj.HTSCode, obj.IHSDutyCode, obj.CountryOfOrigin, obj.CountryOfOriginNo, obj.PackagingSize);

    },
    getIHSValuesByParts: function (IHSPartsId, MSLName, MSLNo, Manufacturer, IHSProdDesc, Packaging, HTSCode, IHSDutyCode, CountryOfOrigin, CountryOfOriginNo, PackagingSize) {
        $('#divLoaderAlternate').show();
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/CusReqAdd");
        obj.set_DataObject("CusReqAdd");
        obj.set_DataAction("GetIHSPartDetails");
        obj.addParameter("IHSPartsId", IHSPartsId);
        obj.addParameter("MSLName", MSLName);
        obj.addParameter("MSLNo", MSLNo);
        obj.addParameter("Manufacturer", Manufacturer);
        obj.addParameter("IHSProdDesc", IHSProdDesc);
        obj.addParameter("Packaging", Packaging);
        obj.addParameter("HTSCode", HTSCode);
        obj.addParameter("IHSDutyCode", IHSDutyCode);
        obj.addParameter("CountryOfOrigin", CountryOfOrigin);
        obj.addParameter("CountryOfOriginNo", CountryOfOriginNo);
        obj.addParameter("PackagingSize", PackagingSize);
        obj.addDataOK(Function.createDelegate(this, this.getIHSDataGrid));
        obj.addError(Function.createDelegate(this, this.getIHSDataError));
        obj.addTimeout(Function.createDelegate(this, this.getIHSDataGridError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },
    getIHSDataError: function (args) {
        $('#divLoaderAlternate').hide();
    },
    getIHSDataGrid: function (args) {
        var obj = args._result.Result
        if (obj[0].IHSPartsId > 0) {

            $("#lblihsproductAlternate").show();
            $("#lblihsHTSCodeAlternate").show();
            $("#lbldutyAlternate").show();
            $("#lblCooAlternate").show();

            var PartName = obj[0].ID;
            if (PartName != 'undefined ' && PartName.length > 0) {
                // $get(this.getFormControlID(this._element.id, 'txtPartNo')).value = $R_FN.setCleanTextValue(PartName);
                $get(this.getFormControlID(this._element.id, 'cmbIHS12txt')).value = $R_FN.setCleanTextValue(PartName);
                document.getElementById("ParttypeSearch1Alternate").style.color = 'yellow'
            }
            else {
                document.getElementById("ParttypeSearch1Alternate").style.color = ''
            }
            if (obj[0].ROHSName != 'undefined ' && obj[0].ROHSName != '' && obj[0].ROHSName.length > 0) {
                // $("#spnMSLAlternate").show();
                $("#spnMSLAlternate").text(" ( " + obj[0].ROHSName + " ) ");
                $find(this.getFormControlID(this._element.id, 'ddlMsl')).setValue(obj[0].ROHSName);
                document.getElementById("lblmlsAlternate").style.color = 'yellow'
                if (obj[0].ROHSName == 'N/A')
                    document.getElementById("spnMSLAlternate").style.color = 'yellow';
                else
                    document.getElementById("spnMSLAlternate").style.color = '';

            }
            else {

                $("#spnMSLAlternate").text(" ( " + "N/A" + " ) ");
                document.getElementById("lblmlsAlternate").style.color = 'yellow'
                document.getElementById("spnMSLAlternate").style.color = 'yellow'
                $find(this.getFormControlID(this._element.id, 'ddlMsl')).setValue("N/A");


            }


            if (obj[0].Manufacturer != 'undefined ' && obj[0].Manufacturer != '' && obj[0].Manufacturer.length > 0) {
                if (obj[0].ManufacturerNo != 0) {
                    $find(this.getFormControlID(this._element.id, 'cmbManufacturer')).setValue(obj[0].ManufacturerNo, $R_FN.setCleanTextValue(obj[0].Manufacturer));
                    $("#spnManufacturerAlternate").hide();
                    document.getElementById("lblmrfAlternate").style.color = 'yellow'

                }
                else {

                    $find(this.getFormControlID(this._element.id, 'cmbManufacturer')).setValue("", 0);
                    $("#spnManufacturerAlternate").show();
                    $("#spnManufacturerAlternate").text(" ( " + obj[0].ManufacturerFullName + " ) ");
                    document.getElementById("lblmrfAlternate").style.color = ''
                }

            }
            else {

                $("#spnManufacturerAlternate").hide();

            }

            if (obj[0].ProdDesc != 'undefined ' && obj[0].IHSProdDesc.length > 0) {

                if (obj[0].ProdNo != 0) {
                    $find(this.getFormControlID(this._element.id, 'cmbProducts')).setValue(obj[0].ProdNo, $R_FN.setCleanTextValue(obj[0].ProdDesc));
                    // $("#spnManufacturerAlternate").hide();
                    document.getElementById("lblMatchproductAlternate").style.color = 'yellow'
                    $("#spnIHSProductAlternate").text($R_FN.setCleanTextValue(obj[0].IHSProdDesc));
                    document.getElementById("lblihsproductAlternate").style.color = 'yellow'
                }
                else {
                    $find(this.getFormControlID(this._element.id, 'cmbProducts')).setValue("", 0);
                    $("#spnIHSProductAlternate").show();
                    $("#spnIHSProductAlternate").text($R_FN.setCleanTextValue(obj[0].IHSProdDesc));
                    document.getElementById("lblihsproductAlternate").style.color = 'yellow'
                }

            }
            else {
                if (obj[0].HTSCode != '' || obj[0].IHSProdDes != '') {
                    $("#spnIHSProductAlternate").show();
                    $("#spnIHSProductAlternate").text(" ( " + "N/A" + " ) ");
                    document.getElementById("spnIHSProductAlternate").style.color = 'yellow'
                    document.getElementById("lblihsproductAlternate").style.color = 'yellow'
                } else {
                    document.getElementById("lblihsproductAlternate").style.color = ''
                    document.getElementById("spnIHSProductAlternate").style.color = ''
                }
            }

            if (obj[0].PackageId > 0) {
                $("#lblECCNCodeAlternate").show();
                $("#spnPackageAlternate").text(" ( " + obj[0].Packaging + " ) ");
                document.getElementById('lblPackageAlternate').style.color = "yellow";
                document.getElementById("spnPackageAlternate").style.color = "";
                $find(this.getFormControlID(this._element.id, 'cmbPackage')).setValue(obj[0].PackageId, $R_FN.setCleanTextValue(obj[0].PackageDescription));

            }
            else {
                $("#lblECCNCodeAlternate").show();
                $("#spnPackageAlternate").text(" ( " + "N/A" + " ) ");
                document.getElementById('lblPackageAlternate').style.color = "";
                document.getElementById("spnPackageAlternate").style.color = 'yellow'
                $find(this.getFormControlID(this._element.id, 'cmbPackage')).setValue("", 0);

            }


            if (obj[0].HTSCode != 'undefined ' && obj[0].HTSCode != '' && obj[0].HTSCode.length > 0) {
                $("#spnHTSCodeAlternate").show();
                $("#spnHTSCodeAlternate").text(obj[0].HTSCode);
                document.getElementById("lblihsHTSCodeAlternate").style.color = 'yellow'
                document.getElementById("spnHTSCodeAlternate").style.color = ''
            }
            else {
                if (obj[0].HTSCode != '' || obj[0].IHSProdDes != '') {
                    $("#spnHTSCodeAlternate").show();
                    $("#spnHTSCodeAlternate").text(" ( " + "N/A" + " ) ");
                    document.getElementById("lblihsHTSCodeAlternate").style.color = 'yellow'
                    document.getElementById("spnHTSCodeAlternate").style.color = 'yellow'
                } else {
                    document.getElementById("lblihsHTSCodeAlternate").style.color = ''
                    document.getElementById("spnHTSCodeAlternate").style.color = ''
                }
            }
            if (obj[0].IHSDutyCode != 'undefined ' && obj[0].IHSDutyCode != '' && obj[0].IHSDutyCode.length > 0) {
                $("#spnIHSDutyCodeAlternate").show();
                $("#spnIHSDutyCodeAlternate").text(" ( " + obj[0].IHSDutyCode + " ) ");
                document.getElementById("lbldutyAlternate").style.color = 'yellow'
                document.getElementById("spnIHSDutyCodeAlternate").style.color = ''

            }
            else {

                $("#spnIHSDutyCodeAlternate").text("( N/A )")
                document.getElementById("spnIHSDutyCodeAlternate").style.color = 'yellow'
                document.getElementById("lbldutyAlternate").style.color = ''
            }
            if (obj[0].CountryOfOrigin != 'undefined ' && obj[0].CountryOfOrigin != '' && obj[0].CountryOfOrigin.length > 0) {
                $("#spnIHSCountryOfOriginAlternate").show();
                $("#spnIHSCountryOfOriginAlternate").text(obj[0].CountryOfOrigin);
                document.getElementById("lblCooAlternate").style.color = 'yellow'
            }
            else {

                $("#spnIHSCountryOfOriginAlternate").text(" ( " + "N/A" + " ) ");
                document.getElementById("lblCooAlternate").style.color = ''
                document.getElementById("spnIHSCountryOfOriginAlternate").style.color = 'yellow'
            }

            if (obj[0].PartStatus != 'undefined ' && obj[0].PartStatus != '' && obj[0].PartStatus.length > 0) {
                $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_LableLifeStatus").text($R_FN.setCleanTextValue(obj[0].PartStatus));
                document.getElementById("lblpartstausAlternate").style.color = 'yellow'
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_LableLifeStatus").style.color = ''
            }
            else {
                $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_LableLifeStatus").text(" ( " + "N/A" + " ) ");
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_LableLifeStatus").style.color = 'yellow'
                document.getElementById("lblpartstausAlternate").style.color = ''
            }
            //ECCN Code start
            if (obj[0].ECCNCode != 'undefined ' && obj[0].ECCNCode != '' && obj[0].ECCNCode.length > 0) {
                $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_LableECCNCode").text($R_FN.setCleanTextValue(obj[0].ECCNCode));
                document.getElementById("lblECCNCodeAlternate").style.color = 'yellow'
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_LableECCNCode").style.color = ''
            }
            else {
                $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_LableECCNCode").text(" ( " + "N/A" + " ) ");
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_LableECCNCode").style.color = 'yellow'
                document.getElementById("lblECCNCodeAlternate").style.color = ''
            }
            //ECCN Code End

            if (obj[0].Packaging != 'undefined ' && obj[0].Packaging != '') {
                this._hidPackaging = obj[0].Packaging;
            }
            else {
                this._hidPackaging = "";
            }
            if (obj[0].PackagingSize != 'undefined ' && obj[0].PackagingSize != '') {
                this._hidPackagingSize = obj[0].PackagingSize;
            }
            else {
                this._hidPackagingSize = "";
            }


            this._hidCountryOfOrigin = obj[0].CountryOfOrigin;
            this._hidCountryOfOriginNo = obj[0].CountryOfOriginNo;
            this._hidLifeCycleStage = obj[0].PartStatus;
            this._hidHTSCode = obj[0].HTSCode;
            this._hidAveragePrice = obj[0].AveragePrice;
            this._hidDescriptions = obj[0].Descriptions;
            this._IHSPartsId = obj[0].IHSPartsId;
            this._ihsCurrencyCode = obj[0].ihsCurrencyCode;
            this._hidIHSProduct = obj[0].IHSProduct;
            this._hidECCNCode = obj[0].ECCNCode;

        }
        $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_cmbIHS12aut_hypClose")[0].click();
        $('#divLoaderAlternate').hide();
        $("#myModalAlternate").hide();
    },
    getIHSDataGridError: function (args) {
        $('#divLoaderAlternate').hide();
        $("#myModalAlternate").hide();
    },
    getDataGridError: function (args) {
        $('#divLoaderAlternate').hide();
        $("#myModalAlternate").hide();
    },
    getDataGrid: function (args) {

        $('#divLoaderAlternate').hide();
        var ihsmsg = args._result.ServiceStatus;
        if (ihsmsg == false) {
            $("#lblIhsServiceMessageAlternate").text("Sorry, the IHS part lookup service is not currently available.");
            $R_FN.showElement(this._lblError, true);
            $R_FN.showElement(this._pnlPartDetail, false);
            this._ctltblPartdetails._tblResults.clearTable();
            this._ctltblPartdetails._tblResults.resizeColumns();
        }
        else {
            if (args._result.Result) {
                this.loadIHSGrid();
                $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_ctltblPartdetails_ctlDB_ctl08").show();
            }
            else {
                this.loadIHSGrid();
            }
        }
    },
    Toggle2: function () {
        $("#lblIhsServiceMessageAlternate").text("");
        $("#lblservicemsgerrorAlternate").text("");
        $('input:radio[name="searchType"][value="contains"]').prop('checked', true);
        $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_ctltblPartdetails_ctlDB_ctl08").hide();
        $get(this.getFormControlID(this._element.id, 'SearchtxtPartNo')).value = "";
        $("#lblihserrorAlternate").text("");
        $R_FN.showElement(this._pnlPartDetail, false);
        this._ctltblPartdetails._tblResults.clearTable();
        this._ctltblPartdetails._tblResults.resizeColumns();
        this._ctltblPartdetails.showNoneFound(true);

    },
    getParSearch: function () {

    },
    getPartDetaildata: function () {
        //if (this._ctlMultiStep._intCurrentStep == 2) {
        if (this._PartEditStatus == 1) {
            if ($get(this.getFormControlID(this._element.id, 'cmbIHS12txt')).value.length > 0) {
                this.getPartDetail($get(this.getFormControlID(this._element.id, 'cmbIHS12txt')).value);
                this.showDetailDiv(true);
            }
            else {
                if (!this.validateForm2()) return;
                this.showDetailDiv(false);
            }
        }
        else {
            if ($("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_lblPartNo").text().length > 0) {
                this.getPartDetail($("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_lblPartNo").text());
                this.showDetailDiv(true);
            }
            else {
                if (!this.validateForm2()) return;
                this.showDetailDiv(false);
            }
        }
        //}

    },
    validateForm2: function () {
        this.onValidate();
        var blnOK = true;
        //if (!this.checkControlAddEntered(this.getFormControlID(this._element.id, 'txtPartNo'), 'TextBox')) blnOK = false;
        if (this._PartEditStatus == 1) {
            if (!this.checkControlEditEntered(this.getFormControlID(this._element.id, 'cmbIHS12txt'), 'TextBox')) blnOK = false;
        }
        //if (!this.checkControlAddEntered(this.getFormControlID(this._element.id, 'cmbIHS'), 'Combo')) blnOK = false;
        if (!blnOK) this.showError(true);
        return blnOK;
    },
    showDetailDiv: function (visible) {
        if (visible) {
            $("#mydivAlternate").show();
            $("#tbpartdetAlternate").show();
        }
        else {
            $("#mydivAlternate").hide();
            $("#tbpartdetAlternate").hide();
        }
    },
    saveClicked: function () {
        if (!this.validateForm()) return;
        if (this._isRestrictedManufacturer > 0 && this._isRestrictedManufacturer != "undefined") {
            this.showError(true, this._RestrictedMFRMessage);

        }
        else {
            var obj = new Rebound.GlobalTrader.Site.Data();

            //ihs changes start
            obj.set_PathToData("controls/Nuggets/CusReqMainInfo");
            obj.set_DataObject("CusReqMainInfo");
            obj.set_DataAction("AddAlternate");
            obj.addParameter("id", this._intCustomerRequirementID);
            obj.addParameter("OriginalCusReqID", this._intOriginalCustomerRequirementID);
            obj.addParameter("Quantity", $get(this.getFormControlID(this._element.id, 'txtQuantity')).value);
            // obj.addParameter("PartNo", $get(this.getFormControlID(this._element.id, 'txtPartNo')).value);
            if (this._PartEditStatus == 1) {
                obj.addParameter("PartNo", $get(this.getFormControlID(this._element.id, 'cmbIHS12txt')).value);
            }
            else {
                obj.addParameter("PartNo", $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_lblPartNo").text());

            }

            obj.addParameter("CustomerPartNo", $get(this.getFormControlID(this._element.id, 'txtCustomerPartNo')).value);
            obj.addParameter("Manufacturer", this.getControlValue(this.getFormControlID(this._element.id, 'cmbManufacturer'), 'Combo'));
            obj.addParameter("DateCode", $get(this.getFormControlID(this._element.id, 'txtDateCode')).value);
            obj.addParameter("Product", this.getControlValue(this.getFormControlID(this._element.id, 'cmbProducts'), 'Combo'));
            obj.addParameter("Package", this.getControlValue(this.getFormControlID(this._element.id, 'cmbPackage'), 'Combo'));
            //obj.addParameter("Package", $find(this.getFormControlID(this._element.id, 'ddlPackage')).getValue());
            obj.addParameter("TargetPrice", $get(this.getFormControlID(this._element.id, 'txtTargetPrice')).value);
            obj.addParameter("DateRequired", $get(this.getFormControlID(this._element.id, 'txtDateRequired')).value);
            obj.addParameter("Usage", $find(this.getFormControlID(this._element.id, 'ddlUsage')).getValue());
            obj.addParameter("Notes", $get(this.getFormControlID(this._element.id, 'txtNotes')).value);
            obj.addParameter("Instructions", $get(this.getFormControlID(this._element.id, 'txtInstructions')).value);
            obj.addParameter("ROHS", $find(this.getFormControlID(this._element.id, 'ddlROHS')).getValue());
            obj.addParameter("PartWatch", this.getControlValue(this.getFormControlID(this._element.id, 'chkPartWatch'), 'CheckBox'));
            obj.addParameter("BOM", this.getControlValue(this.getFormControlID(this._element.id, 'chkBOM'), 'CheckBox'));
            obj.addParameter("BOMName", $get(this.getFormControlID(this._element.id, 'txtBOMName')).value);
            //obj.addParameter("BOMNo", this._BOMHeaderDisplayStatus == false ? this._intBOMID : this._ctlBOMHeader);
            obj.addParameter("BOMNo", this._BOMHeaderDisplayStatus == false ? this._intBOMID : this._hidBOMID);
            obj.addParameter("FactorySealed", this.getControlValue(this.getFormControlID(this._element.id, 'chkFactorySealedSource'), 'CheckBox'));//this._RadChecked);
            //  obj.addParameter("MSL", this.getFieldValue("ctlMsl"));
            //[003] code start
            obj.addParameter("MSL", $find(this.getFormControlID(this._element.id, 'ddlMsl')).getValue());
            //[003] code end

            obj.addParameter("BOMID", this._intBOMID);
            obj.addParameter("PQA", this.getControlValue(this.getFormControlID(this._element.id, 'chkPQA'), 'CheckBox'));
            // obj.addParameter("PQA", $get(this.getFormControlID(this._element.id, 'chkPQA')).value);
            obj.addParameter("ObsoleteChk", this.getControlValue(this.getFormControlID(this._element.id, 'chkObsolete'), 'CheckBox'));//this._radObsoleteChk);
            obj.addParameter("LastTimeBuyChk", this.getControlValue(this.getFormControlID(this._element.id, 'chkLastTimeBuy'), 'CheckBox'));//this._radLastTimeBuyChk);
            obj.addParameter("RefirbsAcceptableChk", this.getControlValue(this.getFormControlID(this._element.id, 'chkRefirbsAcceptable'), 'CheckBox'));//this._radRefirbsAcceptableChk);
            obj.addParameter("TestingRequiredChk", this.getControlValue(this.getFormControlID(this._element.id, 'chkTestingRequired'), 'CheckBox'));//this._radTestingRequiredChk);
            obj.addParameter("TargetSellPrice", $get(this.getFormControlID(this._element.id, 'txtTargetSellPrice')).value);
            obj.addParameter("CompetitorBestOffer", $get(this.getFormControlID(this._element.id, 'txtCompetitorBestoffer')).value);
            obj.addParameter("CustomerDecisionDate", $get(this.getFormControlID(this._element.id, 'txtCustomerDecisionDate')).value);
            obj.addParameter("RFQClosingDate", $get(this.getFormControlID(this._element.id, 'txtRFQClosingDate')).value);
            obj.addParameter("QuoteValidityRequired", $find(this.getFormControlID(this._element.id, 'ddlQuoteValidityRequired')).getValue());
            obj.addParameter("Type", $find(this.getFormControlID(this._element.id, 'ddlType')).getValue());
            obj.addParameter("OrderToPlace", this.getControlValue(this.getFormControlID(this._element.id, 'chkOrderToPlace'), 'CheckBox'));
            obj.addParameter("RequirementforTraceability", $find(this.getFormControlID(this._element.id, 'ddlRequirementforTraceability')).getValue());
            obj.addParameter("EAU", $get(this.getFormControlID(this._element.id, 'txtEau')).value);
            obj.addParameter("AlternativesAccepted", this.getControlValue(this.getFormControlID(this._element.id, 'chkAlternativesAccepted'), 'CheckBox')); //this.getRadioSeclectedValue(this._radAlternativesAccepted));
            obj.addParameter("RepeatBusiness", this.getControlValue(this.getFormControlID(this._element.id, 'chkRepeatBusiness'), 'CheckBox'));//this.getRadioSeclectedValue(this._radRepeatBusiness));
            obj.addParameter("SalesPersion", this.getControlValue(this.getFormControlID(this._element.id, 'cmbSalespersion'), 'Combo'));

            if (this._blnCurInSameFaimly == false) {
                obj.addParameter("Currency", this._intCurrencyNo);
            }
            else {
                obj.addParameter("Currency", $find(this.getFormControlID(this._element.id, 'ddlCurrency')).getValue());//this.getFieldValue("ctlCurrency"));
            }
            obj.addParameter("SalesmanNo", $find(this.getFormControlID(this._element.id, 'ddlSalesman')).getValue());
            obj.addParameter("Alternatives", this.getControlValue(this.getFormControlID(this._element.id, 'chkAlternatives'), 'CheckBox'));
            //ihs code end
            //ihs part edit code start
            obj.addParameter("CountryOfOrigin", this._hidCountryOfOrigin);
            obj.addParameter("CountryOfOriginNo", this._hidCountryOfOriginNo);
            obj.addParameter("LifeCycleStage", this._hidLifeCycleStage);
            obj.addParameter("HTSCode", this._hidHTSCode);
            obj.addParameter("AveragePrice", this._hidAveragePrice);
            obj.addParameter("Packaging", this._hidPackaging);
            obj.addParameter("PackagingSize", this._hidPackagingSize);
            obj.addParameter("Descriptions", this._hidDescriptions);
            obj.addParameter("IHSPartsId", this._IHSPartsId);
            obj.addParameter("ihsCurrencyCode", this._ihsCurrencyCode);
            obj.addParameter("IHSProduct", this._hidIHSProduct);
            //obj.addParameter("ECCNCode", this._hidECCNCode);
            obj.addParameter("ECCNCode", $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_cmbPartEccnMappedaut_ctl03").text());
            obj.addParameter("ECCNNo", this.getControlValue(this.getFormControlID(this._element.id, 'cmbPartEccnMapped'), 'Combo'));
            //code end
            obj.addDataOK(Function.createDelegate(this, this.saveEditComplete));
            obj.addError(Function.createDelegate(this, this.saveEditError));
            obj.addTimeout(Function.createDelegate(this, this.saveEditError));
            $R_DQ.addToQueue(obj);
            $R_DQ.processQueue();
            obj = null;
        }
    },
    saveEditError: function (args) {
        this._strErrorMessage = args._errorMessage;
        this.onSaveError();
    },
    saveEditComplete: function (args) {
        if (args._result.NewID > 0) {
            this._intNewID = args._result.NewID;
            this.onSaveComplete();
        } else {
            this._strErrorMessage = args._errorMessage;
            this.onSaveError();
        }
    },	
    getRadioSeclectedValue: function (radchk) {
        var value = false;
        var rad = $get(String.format("{0}_{1}", radchk.id, 0));
        //var rad1 = $get(String.format("{0}_{1}", radchk.id, 1));
        if (rad.checked) {
            value = true;
        }
        return value;

    },
    validateForm: function () {
        console.log('validation starting')
        this.onValidate();
        var strQuantity = "";
        //var blnOK = this.autoValidateFields();
        var blnOK = true;
        //when edit part code start
        if (this._PartEditStatus == 1) {
            if (!this.checkControlEditEntered(this.getFormControlID(this._element.id, 'cmbIHS12txt'), 'TextBox')) blnOK = false;
        }
        //code end
        if (!this.checkControlEditEntered(this.getFormControlID(this._element.id, 'txtQuantity'), 'TextBox')) blnOK = false;
        if (!this.checkControlEditEntered(this.getFormControlID(this._element.id, 'cmbManufacturer'), 'Combo')) blnOK = false;
        if (!this.checkControlEditEntered(this.getFormControlID(this._element.id, 'cmbManufacturertxt'), 'TextBox')) blnOK = false;
        //if (!this.checkControlEntered(this.getFormControlID(this._element.id, 'txtPartNo'), 'TextBox')) blnOK = false;
        //if (!this.checkControlEditEntered(this.getFormControlID(this._element.id, 'cmbProducts'), 'Combo')) blnOK = false;
        //if (!this.checkControlEditEntered(this.getFormControlID(this._element.id, 'cmbProductstxt'), 'TextBox')) blnOK = false;
        //if (!this.checkControlEditEntered(this.getFormControlID(this._element.id, 'cmbPackage'), 'Combo')) blnOK = false;
        //if (!this.checkControlEditEntered(this.getFormControlID(this._element.id, 'cmbPackagetxt'), 'TextBox')) blnOK = false;


        // if (!this.checkFieldEntered("ctlPartNo")) blnOK = false;
        // if (!this.checkFieldEntered("ctlManufacturer")) blnOK = false;
        //if (!this.checkFieldEntered("ctlProduct")) blnOK = false; // TO make product mandatory on Edit (Suhail)
        if (this._blnCurInSameFaimly == false) {
            if (this._intCurrencyNo <= 0) blnOK = false;
        }
        else {
            // if (!this.checkFieldEntered("ctlCurrency")) blnOK = false;
            if (!this.checkControlEditEntered(this.getFormControlID(this._element.id, 'ddlCurrency'), 'DropDown')) blnOK = false;
        }

        if (!this.checkControlEditEntered(this.getFormControlID(this._element.id, 'txtDateRequired'), 'TextBox')) blnOK = false;
        if (!this.checkControlEditEntered(this.getFormControlID(this._element.id, 'ddlRequirementforTraceability'), 'DropDown')) blnOK = false;
        if (!this.checkControlEditEntered(this.getFormControlID(this._element.id, 'ddlSalesman'), 'DropDown')) blnOK = false;
        // if (!this.checkFieldEntered("ctlDateRequired")) blnOK = false;
        // if (!this.checkFieldEntered("ctlRequirementforTraceability")) blnOK = false;
        // if (!this.checkFieldEntered("ctlSalesman")) blnOK = false;

        //  [003] code start
        //if (!this.checkFieldEntered("ctlMsl")) blnOK = false;
        //  [003] code start
        //alert(blnOK);

        //[004] start
        //if (!this.checkFieldEntered("ctlType")) blnOK = false;
        if (!this.checkControlEditEntered(this.getFormControlID(this._element.id, 'ddlType'), 'DropDown')) blnOK = false;
        //[004] end
        if (!blnOK) this.showError(true);
        return blnOK;
    },
    checkControlEditEntered: function (strControlID, strControlType) {
        var blnEntered = true;
        switch (strControlType) {
            case "TextBox": blnEntered = $R_FN.isEntered($get(strControlID).value); break;
            case "DropDown": blnEntered = !$find(strControlID).isSetAsNoValue(); break;
            case "FileUpload": blnEntered = $find(strControlID).checkEntered(); break;
            case "Combo": blnEntered = $find(strControlID).checkEntered(); break;
        }
        if (!blnEntered) {
            this.setControleditInError(strControlID, true, $R_RES.RequiredFieldMissingMessage);
        }
        else {
            document.getElementById(strControlID).style.border = '';
            if (this._PartEditStatus == 1) {
                if (strControlID == "ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_cmbIHS12txt" && blnEntered == true) {
                    document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_parterror").style.backgroundColor = "";
                    document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_parterror1").style.backgroundColor = "";
                }
            }
            if (strControlID == "ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_txtQuantity" && blnEntered == true) {
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_QuantityError").style.backgroundColor = "";
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_QuantityError1").style.backgroundColor = "";
            }
            //if (strControlID == "ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_txtTargetPrice" && blnEntered == true) {
            //    document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_CustomerTargetPriceError").style.backgroundColor = "";
            //    document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_CustomerTargetPriceError1").style.backgroundColor = "";
            //}
            if (strControlID == "ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_cmbManufacturer" && blnEntered == true) {
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_ManufacturerError").style.backgroundColor = "";
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_ManufacturerError1").style.backgroundColor = "";
            }
            if (strControlID == "ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_cmbManufacturertxt" && blnEntered == true) {
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_ManufacturerError").style.backgroundColor = "";
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_ManufacturerError1").style.backgroundColor = "";
            }
            //if (strControlID == "ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_cmbProducts" && blnEntered == true) {
            //    document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_ProductError").style.backgroundColor = "";
            //    document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_ProductError1").style.backgroundColor = "";
            //}
            //if (strControlID == "ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_cmbProductstxt" && blnEntered == true) {
            //    document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_ProductError").style.backgroundColor = "";
            //    document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_ProductError1").style.backgroundColor = "";
            //}
            if (strControlID == "ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_ddlRequirementforTraceability" && blnEntered == true) {
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_TraceabilityError").style.backgroundColor = "";
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_TraceabilityError1").style.backgroundColor = "";
            }
            if (strControlID == "ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_ddlType" && blnEntered == true) {
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_ddlTypeError").style.backgroundColor = "";
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_ddlTypeError1").style.backgroundColor = "";
            }

            if (strControlID == "ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_ddlSalesman" && blnEntered == true) {
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_SalespersonError").style.backgroundColor = "";
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_SalespersonError1").style.backgroundColor = "";
            }

            if (strControlID == "ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_ddlCurrency" && blnEntered == true) {
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_CurrencyError").style.backgroundColor = "";
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_CurrencyError1").style.backgroundColor = "";
            }

            if (strControlID == "ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_txtDateRequired" && blnEntered == true) {
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_DateRequiredError").style.backgroundColor = "";
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_DateRequiredError1").style.backgroundColor = "";
            }

        }

        return blnEntered;
    },
    setControleditInError: function (strControlID, blnInError, strMessage) {
        if (blnInError) {

            document.getElementById(strControlID).focus();
            // document.getElementById(strControlID).style.borderColor = "red";
            // document.getElementById(strControlID).style.border = '2px solid red';
            //alert(strControlID + " " + blnInError + " " + strMessage);
            if (this._PartEditStatus == 1) {
                if (strControlID == "ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_cmbIHS12txt" && blnInError == true) {
                    document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_parterror").style.backgroundColor = "#990000";
                    document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_parterror1").style.backgroundColor = "#990000";
                }
            }
            if (strControlID == "ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_txtQuantity" && blnInError == true) {
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_QuantityError").style.backgroundColor = "#990000";
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_QuantityError1").style.backgroundColor = "#990000";
            }
            //if (strControlID == "ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_txtTargetPrice" && blnInError == true) {
            //    document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_CustomerTargetPriceError").style.backgroundColor = "#990000";
            //    document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_CustomerTargetPriceError1").style.backgroundColor = "#990000";
            //}
            if (strControlID == "ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_cmbManufacturer" && blnInError == true) {
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_ManufacturerError").style.backgroundColor = "#990000";
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_ManufacturerError1").style.backgroundColor = "#990000";
            }
            if (strControlID == "ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_cmbManufacturertxt" && blnInError == true) {
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_ManufacturerError").style.backgroundColor = "#990000";
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_ManufacturerError1").style.backgroundColor = "#990000";
            }
            //if (strControlID == "ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_cmbProducts" && blnInError == true) {
            //    document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_ProductError").style.backgroundColor = "#990000";
            //    document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_ProductError1").style.backgroundColor = "#990000";
            //}
            //if (strControlID == "ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_cmbProductstxt" && blnInError == true) {
            //    document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_ProductError").style.backgroundColor = "#990000";
            //    document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_ProductError1").style.backgroundColor = "#990000";
            //}
            if (strControlID == "ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_ddlRequirementforTraceability" && blnInError == true) {
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_TraceabilityError").style.backgroundColor = "#990000";
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_TraceabilityError1").style.backgroundColor = "#990000";
            }
            if (strControlID == "ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_ddlType" && blnInError == true) {
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_ddlTypeError").style.backgroundColor = "#990000";
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_ddlTypeError1").style.backgroundColor = "#990000";
            }

            if (strControlID == "ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_ddlSalesman" && blnInError == true) {
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_SalespersonError").style.backgroundColor = "#990000";
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_SalespersonError1").style.backgroundColor = "#990000";
            }

            if (strControlID == "ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_ddlCurrency" && blnInError == true) {
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_CurrencyError").style.backgroundColor = "#990000";
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_CurrencyError1").style.backgroundColor = "#990000";
            }
            if (strControlID == "ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_txtDateRequired" && blnInError == true) {
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_DateRequiredError").style.backgroundColor = "#990000";
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_DateRequiredError1").style.backgroundColor = "#990000";
            }

        } else {
            document.getElementById(strControlID).style.border = '';

        }

    },
    allowEditingCurrency: function (bln) {
        //this.showField("ctlEditCurrency", bln);
        //this.showField("ctlCurrency", bln);
    },
    showProductLoading: function (bln) {
        //this.showFieldLoading("ctlPartNo", bln);
    },
    getPartDetail: function (partNo) {
        $('#divBlockBoxAlternate').show();
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/CusReqAdd");
        obj.set_DataObject("CusReqAdd");
        obj.set_DataAction("GetPartDetail");
        obj.addParameter("partNo", partNo);
        obj.addParameter("CompanyNo", this._intCompanyID);
        obj.addDataOK(Function.createDelegate(this, this.setPartDetail));
        obj.addError(Function.createDelegate(this, this.getPartDetailError));
        obj.addTimeout(Function.createDelegate(this, this.getPartDetailError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;


    },
    setPartDetail: function (args) {
        res = args._result;

        for (var i = 0; i < res.LastPriceCustDetails.length; i++) {
            var row = res.LastPriceCustDetails[i];
            $("#spnpartnameAlternate").text($("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_lblPartNo").text());
            $("#spnLastSoldPriceAlternate").text(row.LastPricePaidByCust);
            $("#spnsoldtocustonAlternate").text($R_FN.setCleanTextValue(row.LastSoldtoCustomer));
            $("#spnAvgPriceAlternate").text(row.LastAverageReboundPriceSold);
            $("#spnlastsoldonAlternate").text(row.LastSoldOn);

            $("#spnLastQuantityAlternate").text(row.LastQuantity);
            $("#spnLastSupplierTypeAlternate").text($R_FN.setCleanTextValue(row.LastSupplierType));
            $("#spnLastDatecodeAlternate").text($R_FN.setCleanTextValue(row.LastDatecode));
            $("#spnLastDatePurchasedAlternate").text(row.LastDatePurchased);
            $("#spnLastCustomerRegionAlternate").text($R_FN.setCleanTextValue(row.LastCustomerRegion));

            $("#spnCustLastSoldPriceAlternate").text(row.CustLastPricePaidByCust);
            $("#spnCurrentCustAlternate").text(this._strCompanyName);
            // $("#spnCustAvgPrice").text(row.CustLastAvgPriceSold);
            $("#spnCustlastsoldonAlternate").text(row.CustLastSoldOn);

            $("#spnCustQuantityAlternate").text(row.CustQuantity);
            $("#spnCustSupplierTypeAlternate").text($R_FN.setCleanTextValue(row.CustSupplierType));
            $("#spnCustDatecodeAlternate").text($R_FN.setCleanTextValue(row.CustDatecode));
            $("#spnCustDatePurchasedAlternate").text(row.CustDatePurchased);
            $("#spnCustomerRegionAlternate").text($R_FN.setCleanTextValue(row.CustomerRegion));
            $("#spnLastPricePaid12Alternate").text(row.BestLastPricePaid12);
            $("#spnCleintBestPricePaid12Alternate").text(row.CleintBestPricePaid12);

        }
        $('#divBlockBoxAlternate').hide();
    },
    getPartDetailError: function (args) {
    }
};

Rebound.GlobalTrader.Site.Controls.Forms.CustomerRequirementMainInfo_AddAlternate.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.CustomerRequirementMainInfo_AddAlternate", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
