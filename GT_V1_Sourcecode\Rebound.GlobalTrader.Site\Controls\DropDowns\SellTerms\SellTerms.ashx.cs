//-----------------------------------------------------------------------------------------------
// RP 11.03.2010:
// - cache data
//[001]      A<PERSON><PERSON>     20-Aug-2018  Provision to add Global Security in Sales Order 
//-----------------------------------------------------------------------------------------------
using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;

namespace Rebound.GlobalTrader.Site.Controls.DropDowns.Data {
    [WebService(Namespace = "http://tempuri.org/")]
    [WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    public class SellTerms : Rebound.GlobalTrader.Site.Data.DropDowns.Base {

        public override void ProcessRequest(HttpContext context) {
            SetDropDownType("SellTerms");
            base.ProcessRequest(context);
        }

        protected override void GetData() {
            //[001] start
            int? clientId = GetFormValue_NullableInt("GlobalLoginClientNo") > 0 ? GetFormValue_Int("GlobalLoginClientNo") : SessionManager.ClientID;
            //[001] end
            string strCacheOptions = CacheManager.SerializeOptions(new object[] { clientId });
            string strCachedData = CacheManager.GetDropDownData(_objDropDown.ID, strCacheOptions);
            if (string.IsNullOrEmpty(strCachedData)) {
                JsonObject jsn = new JsonObject();
                JsonObject jsnList = new JsonObject(true);
                List<BLL.Terms> lst = BLL.Terms.DropDownSellForClient(clientId);
                for (int i = 0; i < lst.Count; i++) {
                    JsonObject jsnItem = new JsonObject();
                    jsnItem.AddVariable("ID", lst[i].TermsId);
                    jsnItem.AddVariable("Name", lst[i].TermsName);
                    jsnList.AddVariable(jsnItem);
                    jsnItem.Dispose(); jsnItem = null;
                }
                lst.Clear(); lst = null;
                jsn.AddVariable("Terms", jsnList);
                jsnList.Dispose(); jsnList = null;
                CacheManager.StoreDropDown(_objDropDown.ID, strCacheOptions, jsn.Result, CacheManager.CacheExpiryType.OneWorkingDay);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            } else {
                _context.Response.Write(strCachedData);
            }
            strCachedData = null;
        }
    }
}
