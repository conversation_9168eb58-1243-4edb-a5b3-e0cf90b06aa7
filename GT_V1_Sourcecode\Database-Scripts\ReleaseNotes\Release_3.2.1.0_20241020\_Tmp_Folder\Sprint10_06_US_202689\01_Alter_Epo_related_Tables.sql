﻿/*
===========================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-202689]		Trung Pham			12-SEP-2024		UPDATE			UPDATE SCHEMA
[US-202689]		Trung Pham			14-OCT-2024		UPDATE			UPDATE EACH COLUMNS IN SCHEMA
===========================================================================================

*/
--tbImportActivity_Epo
 IF (NOT EXISTS (SELECT 1 FROM BorisGlobalTraderimports.INFORMATION_SCHEMA.COLUMNS
	WHERE TABLE_NAME = 'tbImportActivity_Epo'
	AND COLUMN_NAME = 'Inactive'))
	BEGIN
		ALTER TABLE BorisGlobalTraderimports.dbo.tbImportActivity_Epo ADD Inactive BIT;
	END
 IF (NOT EXISTS (SELECT 1 FROM BorisGlobalTraderimports.INFORMATION_SCHEMA.COLUMNS
	WHERE TABLE_NAME = 'tbImportActivity_Epo'
	AND COLUMN_NAME ='InactiveBy'))
	BEGIN
		ALTER TABLE BorisGlobalTraderimports.dbo.tbImportActivity_Epo ADD InactiveBy NVARCHAR(MAX);
	END
 IF (NOT EXISTS (SELECT 1 FROM BorisGlobalTraderimports.INFORMATION_SCHEMA.COLUMNS
	WHERE TABLE_NAME = 'tbImportActivity_Epo'
	AND COLUMN_NAME = 'InactiveDate'))
	BEGIN
		ALTER TABLE BorisGlobalTraderimports.dbo.tbImportActivity_Epo ADD InactiveDate DATETIME;
	END
 IF (NOT EXISTS (SELECT 1 FROM BorisGlobalTraderimports.INFORMATION_SCHEMA.COLUMNS
	WHERE TABLE_NAME = 'tbImportActivity_Epo'
	AND COLUMN_NAME = 'ImportId'))
	BEGIN
		ALTER TABLE BorisGlobalTraderimports.dbo.tbImportActivity_Epo ADD ImportId INT Identity(1, 1);
	END
	
	
--tbEpo
 IF (NOT EXISTS (SELECT 1 FROM BorisGlobalTraderimports.INFORMATION_SCHEMA.COLUMNS
	WHERE TABLE_NAME = 'tbEpo'
	AND COLUMN_NAME = 'Inactive'))
	BEGIN
		ALTER TABLE BorisGlobalTraderimports.dbo.tbEpo ADD Inactive BIT;
	END
  IF (NOT EXISTS (SELECT 1 FROM BorisGlobalTraderimports.INFORMATION_SCHEMA.COLUMNS
	WHERE TABLE_NAME = 'tbEpo'
	AND COLUMN_NAME = 'InactiveBy'))
	BEGIN
		ALTER TABLE BorisGlobalTraderimports.dbo.tbEpo ADD InactiveBy NVARCHAR(MAX);
	END
  IF (NOT EXISTS (SELECT 1 FROM BorisGlobalTraderimports.INFORMATION_SCHEMA.COLUMNS
	WHERE TABLE_NAME = 'tbEpo'
	AND COLUMN_NAME = 'InactiveDate'))
	BEGIN
		ALTER TABLE BorisGlobalTraderimports.dbo.tbEpo ADD InactiveDate DATETIME;
	END
  IF (NOT EXISTS (SELECT 1 FROM BorisGlobalTraderimports.INFORMATION_SCHEMA.COLUMNS
	WHERE TABLE_NAME = 'tbEpo'
	AND COLUMN_NAME = 'ImportId'))
	BEGIN
		ALTER TABLE BorisGlobalTraderimports.dbo.tbEpo ADD ImportId INT;
	END