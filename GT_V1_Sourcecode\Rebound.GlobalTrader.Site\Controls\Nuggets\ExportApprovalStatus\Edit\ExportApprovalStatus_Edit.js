Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.ExportApprovalStatus_Edit=function(n){Rebound.GlobalTrader.Site.Controls.Forms.ExportApprovalStatus_Edit.initializeBase(this,[n]);this._intPurchaseOrderID=0;this._intLineID=-1;this._blnPartReceivedEdit=!1;this._blnRestrictedEdit=!1;this._intLineReceived=0;this._intIPOClientNo=-1;this._blnClientPO=!1;this._intGlobalClientNo=-1;this._blnProductHaza=!1;this._blnCanEditQty=!0;this._blnCanEditPrice=!0;this._TypeNo="";this._IsSendToLineManager=!1;this._IsSendToQuality=!1};Rebound.GlobalTrader.Site.Controls.Forms.ExportApprovalStatus_Edit.prototype={get_intPurchaseOrderID:function(){return this._intPurchaseOrderID},set_intPurchaseOrderID:function(n){this._intPurchaseOrderID!==n&&(this._intPurchaseOrderID=n)},get_strTitleMessage:function(){return this._strTitleMessage},set_strTitleMessage:function(n){this._strTitleMessage!==n&&(this._strTitleMessage=n)},get_lblCurrency:function(){return this._lblCurrency},set_lblCurrency:function(n){this._lblCurrency!==n&&(this._lblCurrency=n)},get_lblTotalShipInCost:function(){return this._lblTotalShipInCost},set_lblTotalShipInCost:function(n){this._lblTotalShipInCost!==n&&(this._lblTotalShipInCost=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Forms.ExportApprovalStatus_Edit.callBaseMethod(this,"initialize");$R_IBTN.showButton(this._ibtnNotify,!1);$R_IBTN.showButton(this._ibtnNotify_Footer,!1);this.addShown(Function.createDelegate(this,this.formShown))},dispose:function(){this.isDisposed||(this._intPurchaseOrderID=null,this._strTitleMessage=null,this._ctlMail&&this._ctlMail.dispose(),this._intLineID=null,this._IsSendToLineManager=null,this._IsSendToQuality=null,this._blnPartReceivedEdit=null,this._blnRestrictedEdit=null,this._intLineReceived=null,this._lblCurrency=null,this._lblTotalShipInCost=null,this._intIPOClientNo=null,this._blnClientPO=null,Rebound.GlobalTrader.Site.Controls.Forms.ExportApprovalStatus_Edit.callBaseMethod(this,"dispose"))},getEditApprovalData:function(){this.showLoading(!0);var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/ExportApprovalStatus");n.set_DataObject("ExportApprovalStatus");n.set_DataAction("GetEditApprovalData");n.addParameter("id",this._intLineID);n.addDataOK(Function.createDelegate(this,this.getEditApprovalDataOK));n.addError(Function.createDelegate(this,this.getEditApprovalDataError));n.addTimeout(Function.createDelegate(this,this.getEditApprovalDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getEditApprovalDataError:function(){this.showInnerContent(!0);this.showLoading(!1)},getEditApprovalDataOK:function(n){var t=n._result;this.setFieldValue("ctlSalesPerson",t.SalesmanName);this.setFieldValue("ctlSalesOrder",t.SalesOrderNo);this.setFieldValue("ctlSOLineNo",t.SOSerialNo);this.setFieldValue("ctlCustomer",t.CustomerName);this.setFieldValue("ctlPartNumber",t.Part);this.setFieldValue("ctlDestinationCountry",t.DestinationCountryId);this.setFieldValue("ctlMilitaryuse",t.MilitaryUseId);this.setFieldValue("ctlEndUser",t.EndUserText);this.setFieldValue("ctlWarehouse",t.ShipFromWarehouse);this.setFieldValue("ctlCountry",t.ShipFromCountry);this.setFieldValue("ctlCustomerName",t.ShipToCustomerName);this.setFieldValue("ctlCustomerCountry",t.ShipToCustomerCountry);this.setFieldValue("ctlCommodityCode",t.CommodityCode);this.setFieldValue("ctlECCN",t.ECCN);this.setFieldValue("ctlPartApplication",t.PartApplication);$get("ctl00_cphMain_ctlExportApprovalStatus_ctlDB_ctl14_frmEdit_ctlDB_ctlExportControl_ctl04_ddlExportControl").value=t.ExportControl;$get("ctl00_cphMain_ctlExportApprovalStatus_ctlDB_ctl14_frmEdit_ctlDB_ctlAerospaceUse_ctl04_ddlAerospaceUse").value=t.AerospaceUse;this.setFieldValue("ctlPartTested",t.PartTested);t.ExportApprovalStatusId==3||t.ExportApprovalStatusId==4||t.ExportApprovalStatusId==5||t.ExportApprovalStatusId==7?($R_IBTN.enableButton(this._ibtnSave,!0),$R_IBTN.enableButton(this._ibtnSave_Footer,!0)):($R_IBTN.enableButton(this._ibtnSave,!1),$R_IBTN.enableButton(this._ibtnSave_Footer,!1));this.showInnerContent(!0);this.showLoading(!1)},formShown:function(){if(this._blnFirstTimeShown){var n=$get("ctl00_cphMain_ctlExportApprovalStatus_ctlDB_ctl14_frmEdit_ctlDB_ctlExportControl_ctl04_ddlExportControl");n&&n.addEventListener("change",function(){n.value==="1"&&alert("Kindly upload the respective document under PDF Document on the SO Detail page")});this.addSave(Function.createDelegate(this,this.saveClicked))}this.getEditApprovalData();this._blnClientPO;this.getFieldDropDownData("ctlDestinationCountry");this.getFieldDropDownData("ctlMilitaryuse");this._intGlobalClientNo>0;this._blnCanEditPrice==!0;this._blnCanEditQty==!0},createPDF:function(){var n=window.location.href,t=new URL(n);$R_FN.openPrintWindow($R_ENUM$PrintObject.PrintTermCondtionPurchase,0)},ShowPopupWeblinkEvidence:function(){$RGT_openSupplierApprovalDoc(this._intLineID,"EVDNS",1)},ShowPopupTROne:function(){$RGT_openSupplierApprovalDoc(this._intLineID,"TR1",1)},ShowPopupTRTwo:function(){$RGT_openSupplierApprovalDoc(this._intLineID,"TR2",1)},ShowPopupTRThree:function(){$RGT_openSupplierApprovalDoc(this._intLineID,"TR3",1)},ShowPopupDevicePictures:function(){$RGT_openSupplierApprovalImages(this._intLineID,"DVCPCTR",1)},ShowPopupManufacturersPictures:function(){$RGT_openSupplierApprovalImages(this._intLineID,"MNFCTRPCTR",1)},ShowPopupTraceabilityPictures:function(){$RGT_openSupplierApprovalImages(this._intLineID,"TRCBLTYPCTR",1)},ShowMailWizard:function(){!1},saveClicked:function(){if(this.validateForm()){var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/ExportApprovalStatus");n.set_DataObject("ExportApprovalStatus");n.set_DataAction("SaveExportApprovalDetails");n.addParameter("id",this._intLineID);n.addParameter("DestinationCountryNo",this.getFieldValue("ctlDestinationCountry"));n.addParameter("MilitaryuseNo",this.getFieldValue("ctlMilitaryuse"));n.addParameter("EndUserText",this.getFieldValue("ctlEndUser"));n.addParameter("PartApplication",this.getFieldValue("ctlPartApplication"));n.addParameter("ExportControl",this.getFieldValue("ctlExportControl"));n.addParameter("AerospaceUse",this.getFieldValue("ctlAerospaceUse"));n.addParameter("PartTested",this.getFieldValue("ctlPartTested"));n.addDataOK(Function.createDelegate(this,this.saveEditOK));n.addError(Function.createDelegate(this,this.saveEditError));n.addTimeout(Function.createDelegate(this,this.saveEditError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null}},saveEditError:function(n){this._strErrorMessage=n._errorMessage;this.onSaveError()},saveEditOK:function(n){n._result.Result==!0?(this.onSaveComplete(),$("#AllocationErrorMsg").hide(),$("#dvtxt").html("")):(this._strErrorMessage=n._errorMessage,this.onSaveError())},SaveInDraftMode:function(){var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/POApprovals");n.set_DataObject("POApprovals");n.set_DataAction("SaveInDraftMode");n.addParameter("id",this._intLineID);n.addParameter("PrecogsSupplierNo",this.getFieldValue("ctlDestinationCountry"));n.addParameter("To",this.getFieldValue("ctlTo"));n.addParameter("Subject",this._ctlMail.getValue_Subject());n.addParameter("Message",this._ctlMail.getValue_Body());n.addParameter("Comment",this.getFieldValue("ctlComment"));n.addDataOK(Function.createDelegate(this,this.SaveInDraftModeOK));n.addError(Function.createDelegate(this,this.SaveInDraftModeError));n.addTimeout(Function.createDelegate(this,this.SaveInDraftModeError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},SaveInDraftModeError:function(n){this._strErrorMessage=n._errorMessage;this.onSaveError()},SaveInDraftModeOK:function(n){n._result.Result==!0?this.onSaveComplete():(this._strErrorMessage=n._errorMessage,this.onSaveError())},validateForm:function(){this.onValidate();var n=!0;return this.checkFieldEntered("ctlDestinationCountry")||(n=!1),this.checkFieldEntered("ctlMilitaryuse")||(n=!1),this.checkFieldEntered("ctlEndUser")||(n=!1),this.checkFieldEntered("ctlPartApplication")||(n=!1),this.checkFieldEntered("ctlPartTested")||(n=!1),n||this.showError(!0),n},setFieldsFromHeader:function(n,t,i,r){$R_FN.setInnerHTML(this._lblTotalShipInCost,r);$R_FN.setInnerHTML(this._lblCurrency,i);this.setFieldValue("ctlPurchaseOrder",n);this.setFieldValue("ctlSalesOrder",t)},productChange:function(){},stepChanged:function(){var n=this._ctlMultiStep._intCurrentStep;$("#spnexplntn").hide();$R_IBTN.showButton(this._ibtnSave,n!=2);$R_IBTN.showButton(this._ibtnSave_Footer,n!=2);$R_IBTN.showButton(this._ibtnCancel,n!=2);$R_IBTN.showButton(this._ibtnCancel_Footer,n!=2);this.setFieldValue("ctlSendMailSupplier",!1);this.setFieldValue("ctlSendMailLineManager",!1);this.setFieldValue("ctlSendMailQuality",!1);this.setFieldValue("ctlUpDateLineManager",!1)},EnableNotifyButton:function(){var r=this.getFieldValue("ctlSendMailSupplier"),n=this.getFieldValue("ctlSendMailLineManager"),t=this.getFieldValue("ctlSendMailQuality"),i=this.getFieldValue("ctlUpDateLineManager");r==!0||n==!0||t==!0||i==!0;n==!0?(this.showField("ctlLineManager",!0),this.showField("ctlSendMailQuality",!1)):(this.showField("ctlLineManager",!1),this._IsSendToLineManager==!1&&this.showField("ctlSendMailQuality",!0));t==!0?(this.showField("ctlSendMailLineManager",!1),alert("Now Line Manager Approval is not required for this supplier approval.")):this._IsSendToQuality==!1&&this.showField("ctlSendMailLineManager",!0);i==!0?this.showField("ctlUpdateLineManagerlist",!0):this.showField("ctlUpdateLineManagerlist",!1)},ExitForm:function(){this.onSaveComplete()},NotifySUpplierApproval:function(){var n;this.getFieldValue("ctlSendMailLineManager")==!0?this.getFieldValue("ctlLineManager")!=null?(n=new Rebound.GlobalTrader.Site.Data,n.set_PathToData("controls/Nuggets/POApprovals"),n.set_DataObject("POApprovals"),n.set_DataAction("NotifySupplierApproval"),n.addParameter("id",this._intLineID),n.addParameter("Subject",this._ctlMail.getValue_Subject()),n.addParameter("Message",this._ctlMail.getValue_Body()),n.addParameter("IsSendToSupplier",this.getFieldValue("ctlSendMailSupplier")),n.addParameter("IsSendToLinemanager",this.getFieldValue("ctlSendMailLineManager")),n.addParameter("LineManagerId",this.getFieldValue("ctlLineManager")),n.addParameter("IsSendToQuality",this.getFieldValue("ctlSendMailQuality")),n.addParameter("Comment",this.getFieldValue("ctlComment")),n.addDataOK(Function.createDelegate(this,this.NotifySUpplierApprovalOK)),n.addError(Function.createDelegate(this,this.NotifySUpplierApprovalError)),n.addTimeout(Function.createDelegate(this,this.NotifySUpplierApprovalError)),$R_DQ.addToQueue(n),$R_DQ.processQueue(),n=null,this.showSaving(!0)):alert("Please select Line Manager."):this.getFieldValue("ctlUpDateLineManager")==!0?this.getFieldValue("ctlUpdateLineManagerlist")!=null?(n=new Rebound.GlobalTrader.Site.Data,n.set_PathToData("controls/Nuggets/POApprovals"),n.set_DataObject("POApprovals"),n.set_DataAction("NotifySupplierApproval"),n.addParameter("id",this._intLineID),n.addParameter("Subject",this._ctlMail.getValue_Subject()),n.addParameter("Message",this._ctlMail.getValue_Body()),n.addParameter("IsSendToSupplier",this.getFieldValue("ctlSendMailSupplier")),n.addParameter("IsSendToLinemanager",this.getFieldValue("ctlSendMailLineManager")),n.addParameter("LineManagerId",this.getFieldValue("ctlLineManager")),n.addParameter("IsSendToQuality",this.getFieldValue("ctlSendMailQuality")),n.addParameter("IsUpdateLinemanager",this.getFieldValue("ctlUpDateLineManager")),n.addParameter("UpdateLineManagerId",this.getFieldValue("ctlUpdateLineManagerlist")),n.addParameter("Comment",this.getFieldValue("ctlComment")),n.addDataOK(Function.createDelegate(this,this.NotifySUpplierApprovalOK)),n.addError(Function.createDelegate(this,this.NotifySUpplierApprovalError)),n.addTimeout(Function.createDelegate(this,this.NotifySUpplierApprovalError)),$R_DQ.addToQueue(n),$R_DQ.processQueue(),n=null,this.showSaving(!0)):alert("Please select Line Manager to update."):(n=new Rebound.GlobalTrader.Site.Data,n.set_PathToData("controls/Nuggets/POApprovals"),n.set_DataObject("POApprovals"),n.set_DataAction("NotifySupplierApproval"),n.addParameter("id",this._intLineID),n.addParameter("Subject",this._ctlMail.getValue_Subject()),n.addParameter("Message",this._ctlMail.getValue_Body()),n.addParameter("IsSendToSupplier",this.getFieldValue("ctlSendMailSupplier")),n.addParameter("IsSendToLinemanager",this.getFieldValue("ctlSendMailLineManager")),n.addParameter("LineManagerId",this.getFieldValue("ctlLineManager")),n.addParameter("IsSendToQuality",this.getFieldValue("ctlSendMailQuality")),n.addParameter("Comment",this.getFieldValue("ctlComment")),n.addDataOK(Function.createDelegate(this,this.NotifySUpplierApprovalOK)),n.addError(Function.createDelegate(this,this.NotifySUpplierApprovalError)),n.addTimeout(Function.createDelegate(this,this.NotifySUpplierApprovalError)),$R_DQ.addToQueue(n),$R_DQ.processQueue(),n=null,this.showSaving(!0))},NotifySUpplierApprovalError:function(n){this._strErrorMessage=n._errorMessage;this.onSaveError()},NotifySUpplierApprovalOK:function(n){n._result.Result==!0?this.onSaveComplete():(this._strErrorMessage=n._errorMessage,this.onSaveError())}};Rebound.GlobalTrader.Site.Controls.Forms.ExportApprovalStatus_Edit.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.ExportApprovalStatus_Edit",Rebound.GlobalTrader.Site.Controls.Forms.Base,Sys.IDisposable);