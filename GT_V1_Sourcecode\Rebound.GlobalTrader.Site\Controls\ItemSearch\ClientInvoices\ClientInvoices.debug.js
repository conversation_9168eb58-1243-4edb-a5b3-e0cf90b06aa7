///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//----------------------------------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//Marker     Changed by      Date         Remarks
//[001]      Vinay           23/08/2012   Customize the invoice control for exported record, Set Exported=1
//----------------------------------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.ItemSearch");

Rebound.GlobalTrader.Site.Controls.ItemSearch.ClientInvoices = function (element) {
    Rebound.GlobalTrader.Site.Controls.ItemSearch.ClientInvoices.initializeBase(this, [element]);
    this._ShipExported = null;
};

Rebound.GlobalTrader.Site.Controls.ItemSearch.ClientInvoices.prototype = {

    initialize: function () {
        Rebound.GlobalTrader.Site.Controls.ItemSearch.ClientInvoices.callBaseMethod(this, "initialize");
        this.addSetupData(Function.createDelegate(this, this.doSetupData));
        this.addGetDataComplete(Function.createDelegate(this, this.doGetDataComplete));
    },

    dispose: function () {
        if (this.isDisposed) return;
        this._ShipExported = null;
        Rebound.GlobalTrader.Site.Controls.ItemSearch.ClientInvoices.callBaseMethod(this, "dispose");
    },

    doSetupData: function () {
        this._objData.set_PathToData("controls/ItemSearch/ClientInvoices");
        this._objData.set_DataObject("ClientInvoices");
        this._objData.set_DataAction("GetData");
        //        this._objData.addParameter("Contact", this.getFieldValue("ctlContact"));
        //        this._objData.addParameter("CMName", this.getFieldValue("ctlCompany"));
        //        this._objData.addParameter("IncludePaid", this.getFieldValue("ctlIncludePaid"));
        //        this._objData.addParameter("Salesman", this.getFieldValue("ctlSalesman"));
        this._objData.addParameter("GoodsInNoLo", this.getFieldValue_Min("ctlGoodsIn"));
        this._objData.addParameter("GoodsInNoHi", this.getFieldValue_Max("ctlGoodsIn"));
        this._objData.addParameter("ClientID", this.getFieldValue("ctlClientName"));
        this._objData.addParameter("InvoiceNoLo", this.getFieldValue_Min("ctlClientInvoiceNo"));
        this._objData.addParameter("InvoiceNoHi", this.getFieldValue_Max("ctlClientInvoiceNo"));
        //        this._objData.addParameter("SONoLo", this.getFieldValue_Min("ctlSalesOrderNo"));
        //        this._objData.addParameter("SONoHi", this.getFieldValue_Max("ctlSalesOrderNo"));
        this._objData.addParameter("DateInvoicedFrom", this.getFieldValue("ctlDateInvoicedFrom"));
        this._objData.addParameter("DateInvoicedTo", this.getFieldValue("ctlDateInvoicedTo"));
        //[001] code start
        this._objData.addParameter("InvoiceExported", this._ShipExported);
        //[001] code end

        this._objData.addParameter("ClientDebitNoLo", this.getFieldValue_Min("ctlClientDebitNo"));
        this._objData.addParameter("ClientDebitNoHi", this.getFieldValue_Max("ctlClientDebitNo"));
    },

    doGetDataComplete: function () {
        for (var i = 0, l = this._objResult.Results.length; i < l; i++) {
            var row = this._objResult.Results[i];
            var aryData = [
				row.No,
				$R_FN.setCleanTextValue(row.CMName),
			    $R_FN.setCleanTextValue(row.SalesmanName) ,
				$R_FN.setCleanTextValue(row.Narrative),
				$R_FN.setCleanTextValue(row.SecondRef),
				$R_FN.setCleanTextValue(row.Date),
				//$R_FN.setCleanTextValue(row.CMNo),
				//$R_FN.setCleanTextValue(row.GoodsInNo)
				//row.GoodsInNo
                
            ];
            this._tblResults.addRow(aryData, row.ID, false, { ClientInvLineNo: row.InvLineNo });
            aryData = null; row = null;
        }
    }
};

Rebound.GlobalTrader.Site.Controls.ItemSearch.ClientInvoices.registerClass("Rebound.GlobalTrader.Site.Controls.ItemSearch.ClientInvoices", Rebound.GlobalTrader.Site.Controls.ItemSearch.Base, Sys.IDisposable);
