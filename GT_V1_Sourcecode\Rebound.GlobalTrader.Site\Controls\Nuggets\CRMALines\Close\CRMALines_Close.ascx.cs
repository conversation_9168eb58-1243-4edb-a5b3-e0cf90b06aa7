using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site;
using Rebound.GlobalTrader.Site.Controls;

namespace Rebound.GlobalTrader.Site.Controls.Forms {
    public partial class CRMALines_Close : Base
    {

		#region Locals

		#endregion

        #region Properties

        private int _intCRMAID = -1;
        public int CRMAID
        {
            get
            {
                return _intCRMAID;
            }
            set
            {
                _intCRMAID = value;
            }
        }

        #endregion
        
        #region Overrides

		/// <summary>
		/// OnInit
		/// </summary>
		/// <param name="e"></param>
        //protected override void OnInit(EventArgs e) {
        //    base.OnInit(e);
        //    TitleText = Functions.GetGlobalResource("FormTitles", "CRMALine_Close");
        //    AddScriptReference("Controls.Nuggets.CRMALines.Close.CRMALines_Close.js");
        //    if (_objQSManager.CRMAID > 0) _intCRMAID = _objQSManager.CRMAID;
        //    WireUpControls();
        //}

        protected override void OnInit(EventArgs e)
        {
            base.OnInit(e);
            TitleText = Functions.GetGlobalResource("FormTitles", "CRMALine_Close");
            AddScriptReference("Controls.Nuggets.CRMALines.Close.CRMALines_Close.js");
        }
		/// <summary>
		/// OnPreRender
		/// </summary>
		/// <param name="e"></param>
		protected override void OnPreRender(EventArgs e) {
			SetupScriptDescriptors();
			base.OnPreRender(e);
		}

		#endregion

		/// <summary>
		/// Wire up controls to the ascx
		/// </summary>
		private void WireUpControls() {
		}

		/// <summary>
		/// Setup Script Descriptors
		/// </summary>
		private void SetupScriptDescriptors() {
            _scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.Forms.CRMALines_Close", ctlDesignBase.ClientID);
           // _scScriptControlDescriptor.AddProperty("intCRMAID", _intCRMAID);
        }

	}
}