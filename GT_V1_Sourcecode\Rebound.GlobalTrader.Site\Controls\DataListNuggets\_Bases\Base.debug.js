///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// Contains functionality common to all DataListNuggets in GT
//
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//
// RP 23.12.2009:
// - push rendering of saved state onto server
// 
// RP 21.12.2009:
// - add getFilterFieldDropDownExtraText function
//
// RP 30.11.2009:
// - new function to delegate setting initial filter values (only if we have no state)
//
// RP 18.10.2009:
// - remove references to ViewLevel
//
// RP 14.10.2009:
// - added Lock / Unlock for saving state
// - automatically add filter parameters
// - ensure dropdowns initialized and preloaded before setting state
//
// RP 12.10.2009:
// - retrofitted changed from v3.0.34 to fix filters not working when in Selection Mode 
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DataListNuggets");

Rebound.GlobalTrader.Site.Controls.DataListNuggets.Base = function (element) {
    Rebound.GlobalTrader.Site.Controls.DataListNuggets.Base.initializeBase(this, [element]);
    this._aryFilterFieldIDs = [];
    this._intCheckDropDownTimer = 0;
    this._blnGettingData = false;
    this._intPageSize = 10;
    this._strPathToData = "";
    this._strDataObject = "";
    this._aryFilterFieldsToInit = [];
    this._intCountFilterFieldsToInit = 0;
    this._intCountDropDownsToCheckForData = 0;
    this._objStateData = {};
    this._blnInitialisedControls = false;
    this._enmInitialSortDirection = 1;
    this._intSortColumnIndex = 0;
};

Rebound.GlobalTrader.Site.Controls.DataListNuggets.Base.prototype = {

    get_Table: function () { return this._table; }, set_Table: function (value) { if (this._table !== value) this._table = value; },
    get_ctlPagingButtonsTop: function () { return this._ctlPagingButtonsTop; }, set_ctlPagingButtonsTop: function (value) { if (this._ctlPagingButtonsTop !== value) this._ctlPagingButtonsTop = value; },
    get_ctlPagingButtonsBottom: function () { return this._ctlPagingButtonsBottom; }, set_ctlPagingButtonsBottom: function (value) { if (this._ctlPagingButtonsBottom !== value) this._ctlPagingButtonsBottom = value; },
    get_pnlFilters: function () { return this._pnlFilters; }, set_pnlFilters: function (value) { if (this._pnlFilters !== value) this._pnlFilters = value; },
    get_pnlNoData: function () { return this._pnlNoData; }, set_pnlNoData: function (value) { if (this._pnlNoData !== value) this._pnlNoData = value; },
    get_aryFilterFieldIDs: function () { return this._aryFilterFieldIDs; }, set_aryFilterFieldIDs: function (value) { if (this._aryFilterFieldIDs !== value) this._aryFilterFieldIDs = value; },
    get_objFilterFieldIDs: function () { return this._objFilterFieldIDs; }, set_objFilterFieldIDs: function (value) { if (this._objFilterFieldIDs !== value) this._objFilterFieldIDs = value; },
    get_ibtnReset: function () { return this._ibtnReset; }, set_ibtnReset: function (value) { if (this._ibtnReset !== value) this._ibtnReset = value; },
    get_ibtnApply: function () { return this._ibtnApply; }, set_ibtnApply: function (value) { if (this._ibtnApply !== value) this._ibtnApply = value; },
    get_ibtnOff: function () { return this._ibtnOff; }, set_ibtnOff: function (value) { if (this._ibtnOff !== value) this._ibtnOff = value; },
    get_ibtnCancel: function () { return this._ibtnCancel; }, set_ibtnCancel: function (value) { if (this._ibtnCancel !== value) this._ibtnCancel = value; },
    get_strFilterExpression: function () { return this._strFilterExpression; }, set_strFilterExpression: function (value) { if (this._strFilterExpression !== value) this._strFilterExpression = value; },
    get_intCurrentTab: function () { return this._intCurrentTab; }, set_intCurrentTab: function (value) { if (this._intCurrentTab !== value) this._intCurrentTab = value; },
    get_blnIsFilterInitiallyOpen: function () { return this._blnIsFilterInitiallyOpen; }, set_blnIsFilterInitiallyOpen: function (value) { if (this._blnIsFilterInitiallyOpen !== value) this._blnIsFilterInitiallyOpen = value; },
    get_strState: function () { return this._strState; }, set_strState: function (value) { if (this._strState !== value) this._strState = value; },
    get_blnAllowSelection: function () { return this._blnAllowSelection; }, set_blnAllowSelection: function (value) { if (this._blnAllowSelection !== value) this._blnAllowSelection = value; },
    get_strSelectModeResultsText: function () { return this._strSelectModeResultsText; }, set_strSelectModeResultsText: function (value) { if (this._strSelectModeResultsText !== value) this._strSelectModeResultsText = value; },
    get_aryButtonIDs: function () { return this._aryButtonIDs; }, set_aryButtonIDs: function (value) { if (this._aryButtonIDs !== value) this._aryButtonIDs = value; },
    get_txtLimitResults: function () { return this._txtLimitResults; }, set_txtLimitResults: function (value) { if (this._txtLimitResults !== value) this._txtLimitResults = value; },
    get_intResultsLimit: function () { return this._intResultsLimit; }, set_intResultsLimit: function (value) { if (this._intResultsLimit !== value) this._intResultsLimit = value; },
    get_ctlMultiSelectionCount: function () { return this._ctlMultiSelectionCount; }, set_ctlMultiSelectionCount: function (value) { if (this._ctlMultiSelectionCount !== value) this._ctlMultiSelectionCount = value; },
    get_lblSelectModeResults: function () { return this._lblSelectModeResults; }, set_lblSelectModeResults: function (value) { if (this._lblSelectModeResults !== value) this._lblSelectModeResults = value; },
    get_intDataListNuggetID: function () { return this._intDataListNuggetID; }, set_intDataListNuggetID: function (value) { if (this._intDataListNuggetID !== value) this._intDataListNuggetID = value; },
    get_intPageSize: function () { return this._intPageSize; }, set_intPageSize: function (value) { if (this._intPageSize !== value) this._intPageSize = value; },
    get_blnAllowSavingState: function () { return this._blnAllowSavingState; }, set_blnAllowSavingState: function (value) { if (this._blnAllowSavingState !== value) this._blnAllowSavingState = value; },
    get_strDataListNuggetSubType: function () { return this._strDataListNuggetSubType; }, set_strDataListNuggetSubType: function (value) { if (this._strDataListNuggetSubType !== value) this._strDataListNuggetSubType = value; },
    get_blnSaveState: function () { return this._blnSaveState; }, set_blnSaveState: function (value) { if (this._blnSaveState !== value) this._blnSaveState = value; },
    get_enmViewLevel: function () { return this._enmViewLevel; }, set_enmViewLevel: function (value) { if (this._enmViewLevel !== value) this._enmViewLevel = value; },

    addPageSizeClickEvent: function (handler) { this.get_events().addHandler("pagesizeclick", handler); },
    removePageSizeClickEvent: function (handler) { this.get_events().removeHandler("pagesizeclick", handler); },
    onPageSizeClick: function () {
        var handler = this.get_events().getHandler("pagesizeclick");
        if (handler) handler(this, Sys.EventArgs.Empty);
    },

    addSortDataEvent: function (handler) { this.get_events().addHandler("SortData", handler); },
    removeSortDataEvent: function (handler) { this.get_events().removeHandler("SortData", handler); },
    onSortData: function () {
        var handler = this.get_events().getHandler("SortData");
        if (handler) handler(this, Sys.EventArgs.Empty);
    },

    addFilterDataEvent: function (handler) { this.get_events().addHandler("filterdata", handler); },
    removeFilterDataEvent: function (handler) { this.get_events().removeHandler("filterdata", handler); },
    onFilterData: function () {
        var handler = this.get_events().getHandler("filterdata");
        if (handler) handler(this, Sys.EventArgs.Empty);
    },

    addPageChangedEvent: function (handler) { this.get_events().addHandler("PageChanged", handler); },
    removePageChangedEvent: function (handler) { this.get_events().removeHandler("PageChanged", handler); },
    onPageChanged: function () {
        var handler = this.get_events().getHandler("PageChanged");
        if (handler) handler(this, Sys.EventArgs.Empty);
    },

    addPageTabChangedEvent: function (handler) { this.get_events().addHandler("PageTabChanged", handler); },
    removePageTabChangedEvent: function (handler) { this.get_events().removeHandler("PageTabChanged", handler); },
    onPageTabChanged: function () {
        var handler = this.get_events().getHandler("PageTabChanged");
        if (handler) handler(this, Sys.EventArgs.Empty);
    },

    addSetupDataCallEvent: function (handler) { this.get_events().addHandler("SetupDataCall", handler); },
    removeSetupDataCallEvent: function (handler) { this.get_events().removeHandler("SetupDataCall", handler); },
    onSetupDataCall: function () {
        var handler = this.get_events().getHandler("SetupDataCall");
        if (handler) handler(this, Sys.EventArgs.Empty);
    },

    addGetDataOKEvent: function (handler) { this.get_events().addHandler("GetDataOK", handler); },
    removeGetDataOKEvent: function (handler) { this.get_events().removeHandler("GetDataOK", handler); },
    onGetDataOK: function () {
        var handler = this.get_events().getHandler("GetDataOK");
        if (handler) handler(this, Sys.EventArgs.Empty);
    },

    addDataCallErrorEvent: function (handler) { this.get_events().addHandler("DataCallError", handler); },
    removeDataCallErrorEvent: function (handler) { this.get_events().removeHandler("DataCallError", handler); },
    onDataCallError: function () {
        var handler = this.get_events().getHandler("DataCallError");
        if (handler) handler(this, Sys.EventArgs.Empty);
    },

    addInitCompleteEvent: function (handler) { this.get_events().addHandler("InitComplete", handler); },
    removeInitCompleteEvent: function (handler) { this.get_events().removeHandler("InitComplete", handler); },
    onInitComplete: function () {
        if (this._blnInitialisedControls) return;
        this._blnInitialisedControls = true;
        var handler = this.get_events().getHandler("InitComplete");
        if (handler) handler(this, Sys.EventArgs.Empty);
    },

    addStateRenderedEvent: function (handler) { this.get_events().addHandler("StateRendered", handler); },
    removeStateRenderedEvent: function (handler) { this.get_events().removeHandler("StateRendered", handler); },
    onStateRendered: function () {
        var handler = this.get_events().getHandler("StateRendered");
        if (handler) handler(this, Sys.EventArgs.Empty);
    },

    addAskPageToChangeTab: function (handler) { this.get_events().addHandler("AskPageToChangeTab", handler); },
    removeAskPageToChangeTab: function (handler) { this.get_events().removeHandler("AskPageToChangeTab", handler); },
    onAskPageToChangeTab: function () {
        var handler = this.get_events().getHandler("AskPageToChangeTab");
        if (handler) handler(this, Sys.EventArgs.Empty);
    },

    addBaseControlsInitialized: function (handler) { this.get_events().addHandler("BaseControlsInitialized", handler); },
    removeBaseControlsInitialized: function (handler) { this.get_events().removeHandler("BaseControlsInitialized", handler); },
    onBaseControlsInitialized: function () {
        var handler = this.get_events().getHandler("BaseControlsInitialized");
        if (handler) handler(this, Sys.EventArgs.Empty);
    },

    initialize: function () {
        Rebound.GlobalTrader.Site.Controls.DataListNuggets.Base.callBaseMethod(this, "initialize");
        if (this._blnAllowSelection) {
            this._ctlMultiSelectionCount.registerTable(this._table);
            $R_TXTBOX.addEnterPressedEvent(this._txtLimitResults, Function.createDelegate(this, this.resultsLimitPressEnter));
            $addHandler(this._txtLimitResults, "blur", Function.createDelegate(this, this.setResultsLimit));
            this._table._blnFiltersOn = true;
            this._blnIsFilterInitiallyOpen = true;
        } else {
            this._ctlPagingButtonsTop.pageSizeClick(this._table._intCurrentPageSize);
            this._ctlPagingButtonsBottom.pageSizeClick(this._table._intCurrentPageSize);
            this._ctlPagingButtonsTop.addPageSizeClickEvent(Function.createDelegate(this, this.topPageSizeClick));
            this._ctlPagingButtonsBottom.addPageSizeClickEvent(Function.createDelegate(this, this.bottomPageSizeClick));
            this._ctlPagingButtonsTop.addPageChangedEvent(Function.createDelegate(this, this.topPageNumberChanged));
            this._ctlPagingButtonsBottom.addPageChangedEvent(Function.createDelegate(this, this.bottomPageNumberChanged));
            this._ctlPagingButtonsTop.addFilterStateChangeEvent(Function.createDelegate(this, this.topFilterStateChanged));
            this._ctlPagingButtonsBottom.addFilterStateChangeEvent(Function.createDelegate(this, this.bottomFilterStateChanged));
            this._ctlPagingButtonsTop.addStateLockChanged(Function.createDelegate(this, this.updateLockStateFromTopPagingButtons));
            this._ctlPagingButtonsBottom.addStateLockChanged(Function.createDelegate(this, this.updateLockStateFromBottomPagingButtons));
            this._ctlPagingButtonsTop.addShowLockLoading(Function.createDelegate(this, this.showLockLoadingFromTopPagingButtons));
            this._ctlPagingButtonsBottom.addShowLockLoading(Function.createDelegate(this, this.showLockLoadingFromBottomPagingButtons));
            this._enmInitialSortDirection = this._table._enmSortDirection;
            this._intInitialSortColumnIndex = this._table._intSortColumnIndex;
            this.addPageChangedEvent(Function.createDelegate(this, this.getData));
            this.filterStateChanged(this._blnIsFilterInitiallyOpen, true);
            this._ctlPagingButtonsBottom.setFilter(this._blnIsFilterInitiallyOpen);
            this._ctlPagingButtonsTop.setFilter(this._blnIsFilterInitiallyOpen);
            if (this._ibtnCancel) $R_IBTN.addClick(this._ibtnCancel, Function.createDelegate(this, this.cancelClicked));
            $R_FN.showElement(this._ctlPagingButtonsTop._element, false);
            $R_FN.showElement(this._ctlPagingButtonsBottom._element, false);
            if (this._ibtnCancel) $R_IBTN.showButton(this._ibtnCancel, false);
        }
        this._table.addSortDataEvent(Function.createDelegate(this, this.onSortData));
        this._table.addFilterDataEvent(Function.createDelegate(this, this.onFilterData));
        this.addSortDataEvent(Function.createDelegate(this, this.getData));
        this.addFilterDataEvent(Function.createDelegate(this, this.getData));
        this.addPageSizeClickEvent(Function.createDelegate(this, this.getData));
        if (this._ibtnReset) $R_IBTN.addClick(this._ibtnReset, Function.createDelegate(this, this.resetFilter));
        if (this._ibtnApply) $R_IBTN.addClick(this._ibtnApply, Function.createDelegate(this, this.applyFilter));
        if (this._ibtnOff) $R_IBTN.addClick(this._ibtnOff, Function.createDelegate(this, this.turnOffFilter));
        this.addRefreshEvent(Function.createDelegate(this, this.applyFilter));
        this.showLoading(!this._blnIsFilterInitiallyOpen);
        this.setFilterFieldEnterPressedEvents();
        if (!this._intCurrentTab) this._intCurrentTab = 0;
        this.showContent(false);

        //step through all filter fields to find if any have controls that need to be initialized before we continue
        this._aryFilterFieldsToInit = [];
        for (var i = 0, l = this._aryFilterFieldIDs.length; i < l; i++) {
            var fld = $find(this._aryFilterFieldIDs[i]);
            if (Object.getTypeName(fld) == "Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.DropDown" || Object.getTypeName(fld) == "Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.StarRating") Array.add(this._aryFilterFieldsToInit, fld);
            fld = null;
        }
        this._intCountFilterFieldsToInit = this._aryFilterFieldsToInit.length;
        if (this._intCountFilterFieldsToInit == 0) {
            this.controlsInitialized();
        } else {
            for (i = 0; i < this._intCountFilterFieldsToInit; i++) {
                this.ensureFilterFieldControlInitialized(this._aryFilterFieldsToInit[i]);
            }
        }
    },

    ensureFilterFieldControlInitialized: function (fld) {
        if (fld.get_isInitialized()) {
            this._intCountFilterFieldsToInit -= 1;
            if (this._intCountFilterFieldsToInit == 0) {
                this._aryFilterFieldsToInit = null;
                this.controlsInitialized();
            }
        } else {
            var strID = this._element.id;
            var fn = function () { $find(strID).ensureFilterFieldControlInitialized(fld); };
            setTimeout(fn, 5);
        }
    },

    controlsInitialized: function () {
        if (this._blnIsFilterInitiallyOpen) {
            this.onBaseControlsInitialized();
            this.getDropDownsData(true);
        } else {
            this.onBaseControlsInitialized();
            this.completionOfInit();
        }
    },

    dispose: function () {
        if (this.isDisposed) return;
        if (this.isDisposed) return;
        if (this.get_element()) $clearHandlers(this.get_element());
        if (this._ibtnReset) $R_IBTN.clearHandlers(this._ibtnReset);
        if (this._ibtnApply) $R_IBTN.clearHandlers(this._ibtnApply);
        if (this._ibtnOff) $R_IBTN.clearHandlers(this._ibtnOff);
        if (this._table) this._table.dispose();
        if (this._ctlPagingButtonsTop) this._ctlPagingButtonsTop.dispose();
        if (this._ctlPagingButtonsBottom) this._ctlPagingButtonsBottom.dispose();
        if (this._objData) this._objData.dispose();
        if (this._ctlMultiSelectionCount) this._ctlMultiSelectionCount.dispose();
        this._table = null;
        this._ctlPagingButtonsTop = null;
        this._ctlPagingButtonsBottom = null;
        this._pnlFilters = null;
        this._pnlNoData = null;
        this._aryFilterFieldIDs = null;
        this._objFilterFieldIDs = null;
        this._ibtnReset = null;
        this._ibtnApply = null;
        this._ibtnOff = null;
        this._txtLimitResults = null;
        this._ctlMultiSelectionCount = null;
        this._lblSelectModeResults = null;
        this._aryButtonIDs = null;
        this._objData = null;
        this._aryFilterFieldsToInit = null;
        this._enmViewLevel = null;
        Rebound.GlobalTrader.Site.Controls.DataListNuggets.Base.callBaseMethod(this, "dispose");
    },

    clearState: function () {
        Rebound.GlobalTrader.Site.WebServices.ClearDataListNuggetState(this._intDataListNuggetID, this._strDataListNuggetSubType);
        Rebound.GlobalTrader.Site.Controls.DataListNuggets.Base.callBaseMethod(this, "clearState");
    },

    setTabFromViewLevelStateVariable: function () {
        var enmOriginalViewLevel = this._enmViewLevel;
        this._intCurrentTab = this.getStateVariableByName("ViewLevel").Value;
        if (typeof (this._intCurrentTab) == "undefined") this._intCurrentTab = enmOriginalViewLevel;
        if (this._intCurrentTab == null) this._intCurrentTab = enmOriginalViewLevel;
        this._intCurrentTab = Number.parseInvariant(this._intCurrentTab.toString());
        this._enmViewLevel = this._intCurrentTab;
        enmOriginalViewLevel = null;
    },

    getDropDownsData: function (blnCheckForCompletion) {
        var fld;

        //if we have to check for completion of dropdowns getting data we need to count them all first
        //otherwise one may finish too soon and we'll tell the control it can get data which will cause errors
        if (blnCheckForCompletion) {
            for (var i = 0, l = this._aryFilterFieldIDs.length; i < l; i++) {
                fld = $find(this._aryFilterFieldIDs[i]);
                if (Object.getTypeName(fld) == "Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.DropDown") this._intCountDropDownsToCheckForData += 1;
                fld = null;
            }
        }

        //get the data
        for (i = 0, l = this._aryFilterFieldIDs.length; i < l; i++) {
            fld = $find(this._aryFilterFieldIDs[i]);
            if (Object.getTypeName(fld) == "Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.DropDown") {
                if (blnCheckForCompletion) fld._ddl.addGotDataComplete(Function.createDelegate(this, this.gotDropDownDataComplete));
                fld.getDropDownData();
            }
            fld = null;
        }

        if (blnCheckForCompletion && this._intCountDropDownsToCheckForData == 0) this.completionOfInit();
    },


    gotDropDownDataComplete: function () {
        this._intCountDropDownsToCheckForData -= 1;
        if (this._intCountDropDownsToCheckForData == 0) this.completionOfInit();
    },

    completionOfInit: function () {
        this.displayLockState();
        this.onInitComplete();
        //if (!this._blnHasState) this.onSetInitialFilterValues();
    },

    getData: function () {
        if (this._blnGettingData) return;
        this.showError(false);
        this.clearMessages();
        $R_FN.showElement(this._pnlLinks, true);
        if (this._ibtnCancel) $R_IBTN.showButton(this._ibtnCancel, true);
        this.showResultsPanels(false);
        this.enableButtons(false);
        if (this._ctlPagingButtonsTop) $R_FN.showElement(this._ctlPagingButtonsTop.get_element(), false);
        if (this._ctlPagingButtonsBottom) $R_FN.showElement(this._ctlPagingButtonsBottom.get_element(), false);
        this.cancelDataCall();
        this._objData = new Rebound.GlobalTrader.Site.Data();
        this._objData._intTimeoutMilliseconds = 600000;
        this._objData.set_DataAction("GetData");
        this._objData.set_PathToData(this._strPathToData);
        this._objData.set_DataObject(this._strDataObject);
        this.addFilterParameters(this._objData);
        this.onSetupDataCall();
        this._objData.addParameter("DLNID", this._intDataListNuggetID);
        this._objData.addParameter("SortIndex", this._table._intSortColumnIndex + 1);
        this._objData.addParameter("SortDir", this._table._enmSortDirection);
        this._objData.addParameter("PageIndex", this._table._intCurrentPage - 1);
        this._objData.addParameter("PageSize", this._table._intCurrentPageSize);
        this._objData.addParameter("SaveState", this._blnSaveState);
        this._objData.addParameter("DLNSubType", this._strDataListNuggetSubType);
        if (this._blnAllowSelection) this._objData.addParameter("ResultsLimit", this._intResultsLimit);
        this._objData.addDataOK(Function.createDelegate(this, this.dataListNugget_getDataOK));
        this._objData.addError(Function.createDelegate(this, this.dataListNugget_getDataError));
        this._objData.addTimeout(Function.createDelegate(this, this.dataListNugget_getDataError));
        this._blnGettingData = true;
        $R_DQ.addToQueue(this._objData);
        $R_DQ.processQueue();
    },

    dataListNugget_getDataOK: function (args) {
        this._blnGettingData = false;
        this._objResult = args._result;
        this._table._intTotalRecords = this._objResult.TotalRecords;
        if (!this._blnAllowSelection) {
            this._ctlPagingButtonsTop._intTotalResults = this._objResult.TotalRecords;
            this._ctlPagingButtonsBottom._intTotalResults = this._objResult.TotalRecords;
        }
        this._table.clearTable();
        this.showContentLoading(false);
        this.onGetDataOK();
        this.showError(false);
        this.showResultsPanels(true);
        if (this._ibtnCancel) $R_IBTN.showButton(this._ibtnCancel, false);
        if (this._table._intTotalRecords > 0) {
            this.showResultsTable();
        } else {
            this.showNoData();
        }
        if (this._blnAllowSelection) {
            this._txtLimitResults.value = this._intResultsLimit;
            $R_FN.setInnerHTML(this._lblSelectModeResults, String.format(this._strSelectModeResultsText, Math.min(this._intResultsLimit, this._table._intTotalRecords), this._table._intTotalRecords));
        } else {
            this._table.calculatePages();
            this.updatePaging();
        }
        this._table.resizeColumns();
        this.enableButtons(true);
    },

    dataListNugget_getDataError: function (args) {
        this._blnGettingData = false;
        this.showError(true, args.get_ErrorMessage());
        if (this._ibtnCancel) $R_IBTN.showButton(this._ibtnCancel, false);
        this.onDataCallError();
        this.enableButtons(true);
    },

    topPageSizeClick: function () {
        if (this._blnGettingData) return;
        this._ctlPagingButtonsBottom.pageSizeClick(this._ctlPagingButtonsTop._intCurrentPageSize);
        this.pageSizeClick(this._ctlPagingButtonsTop._intCurrentPageSize);
    },

    bottomPageSizeClick: function () {
        if (this._blnGettingData) return;
        this._ctlPagingButtonsTop.pageSizeClick(this._ctlPagingButtonsBottom._intCurrentPageSize);
        this.pageSizeClick(this._ctlPagingButtonsBottom._intCurrentPageSize);
    },

    pageSizeClick: function (intSize) {
        if (this._blnGettingData) return;
        if (intSize == this._table._intCurrentPageSize) return;
        this._table._intCurrentPageSize = intSize;
        this._table.calculatePages();
        this._table.calculateStartAndEndRow();
        this.updatePaging();
        this.onPageChanged();
    },

    topPageNumberChanged: function () {
        this._ctlPagingButtonsBottom._intCurrentPage = this._ctlPagingButtonsTop._intCurrentPage;
        this.pageNumberChanged(this._ctlPagingButtonsTop._intCurrentPage);
    },

    bottomPageNumberChanged: function () {
        this._ctlPagingButtonsTop._intCurrentPage = this._ctlPagingButtonsBottom._intCurrentPage;
        this.pageNumberChanged(this._ctlPagingButtonsBottom._intCurrentPage);
    },

    pageNumberChanged: function (intPage) {
        if (intPage == this._table._intCurrentPage) return;
        this._table.changePage(intPage);
        this.updatePaging();
        this.onPageChanged();
    },

    setPage: function (intPage) {
        if (this._blnGettingData) return;
        this._table._intCurrentPage = intPage;
        this.getData();
    },

    updatePaging: function () {
        if (this._blnAllowSelection) return;
        this._ctlPagingButtonsTop._intTotalResults = this._table._intTotalRecords;
        this._ctlPagingButtonsTop._intTotalPages = this._table._intTotalPages;
        this._ctlPagingButtonsTop._intCurrentPage = this._table._intCurrentPage;
        this._ctlPagingButtonsTop.updatePageDisplay();
        this._ctlPagingButtonsBottom._intTotalResults = this._table._intTotalRecords;
        this._ctlPagingButtonsBottom._intTotalPages = this._table._intTotalPages;
        this._ctlPagingButtonsBottom._intCurrentPage = this._table._intCurrentPage;
        this._ctlPagingButtonsBottom.updatePageDisplay();
    },

    topFilterStateChanged: function () {
        if (this._blnGettingData) return;
        this._ctlPagingButtonsBottom.setFilter(this._ctlPagingButtonsTop._blnFiltersOn);
        this.filterStateChanged(this._ctlPagingButtonsTop._blnFiltersOn);
    },

    bottomFilterStateChanged: function () {
        if (this._blnGettingData) return;
        this._ctlPagingButtonsTop.setFilter(this._ctlPagingButtonsBottom._blnFiltersOn);
        this.filterStateChanged(this._ctlPagingButtonsTop._blnFiltersOn);
    },

    turnOffFilter: function () {
        if (this._blnGettingData) return;
        this._ctlPagingButtonsBottom.setFilter(false);
        this._ctlPagingButtonsTop.setFilter(false);
        this.filterStateChanged(false);
    },

    filterStateChanged: function (blnFiltersOn, blnDontRaiseEvent) {
        this._table._blnFiltersOn = blnFiltersOn;
        this.showFilters(blnFiltersOn);
        if (blnFiltersOn) {
            if (this._strFilterExpression != "") {
                this._table._strFilterExpression = this._strFilterExpression;
                if (!blnDontRaiseEvent) this.onFilterData();
            }
        } else {
            if (this._strFilterExpression != "") {
                this._table._strFilterExpression = "";
                if (!blnDontRaiseEvent) this.onFilterData();
            }
        }
    },

    showFilters: function (blnShow) {
        $R_FN.showElement(this._pnlFilters, blnShow);
        if (blnShow) this.getDropDownsData();
    },

    showNoData: function () {
        this._table.show(false);
        $R_FN.showElement(this._pnlNoData, true);
    },

    showResultsTable: function () {
        this._table.show(true);
        $R_FN.showElement(this._pnlNoData, false);
    },

    showResultsPanels: function (blnShow) {
        if (blnShow) {
            this.showContent(true);
            this.showContentLoading(false);
            $R_FN.showElement(this._pnlNoData, false);
            if (!this._blnAllowSelection) {
                $R_FN.showElement(this._ctlPagingButtonsTop._element, true);
                $R_FN.showElement(this._ctlPagingButtonsBottom._element, true);
            }
        }
        this.showLoading(!blnShow);
        if (blnShow) {
            Sys.UI.DomElement.removeCssClass(this._table.get_element(), "dataListNuggetLoading");
        } else {
            Sys.UI.DomElement.addCssClass(this._table.get_element(), "dataListNuggetLoading");
        }
        this.showRefresh(blnShow);
    },

    resetFilter: function () {
        for (var i = 0, l = this._aryFilterFieldIDs.length; i < l; i++) {
            var fld = $find(this._aryFilterFieldIDs[i]);
            fld.reset();
            fld.resetToDefault();
            fld = null;
        }
        if (this._blnAllowSelection) {
            this._txtLimitResults.value = 50;
            this._intResultsLimit = 50;
        }
    },

    applyFilter: function () {
        if (this._blnGettingData) return;
        this.onFilterData();
    },

    getFilterField: function (strField) {
        var strControlID = eval("this._objFilterFieldIDs." + strField);
        if (!strControlID) eval(String.format("FilterFieldNotFound_{0}()", strField));
        return $find(strControlID);
    },

    getFilterFieldByFilterName: function (strName) {
        var fld = null;
        for (var i = 0, l = this._aryFilterFieldIDs.length; i < l; i++) {
            if ($find(this._aryFilterFieldIDs[i])._strFilterField == strName) {
                fld = $find(this._aryFilterFieldIDs[i]);
                break;
            }
        }
        return fld;
    },

    showFilterField: function (strField, bln) {
        var fld = this.getFilterField(strField);
        if (!fld) return;
        fld.show(bln);
    },

    enableFilterField: function (strField, bln) {
        var fld = this.getFilterField(strField);
        if (!fld) return;
        fld.enableField(bln);
    },

    getFilterFieldValue: function (strField) {
        var fld = this.getFilterField(strField);
        if (!fld) return;
        if (!fld._blnOn) return;
        var varReturn = fld.getValue();
        fld = null;
        return varReturn;
    },

    setFilterFieldValue: function (strField, v) {
        var fld = this.getFilterField(strField);
        if (!fld) return;
        fld.setValue(v);
    },

    getFilterFieldDropDownText: function (strField) {
        var fld = this.getFilterField(strField);
        if (!fld) return;
        if (!fld._blnOn) return;
        var varReturn = fld.getText();
        fld = null;
        return varReturn;
    },

    getFilterFieldDropDownExtraText: function (strField) {
        var fld = this.getFilterField(strField);
        if (!fld) return;
        if (!fld._blnOn) return;
        var varReturn = fld.getExtraText();
        fld = null;
        return varReturn;
    },

    getFilterFieldValue_Min: function (strField) {
        var fld = this.getFilterField(strField);
        if (!fld) return;
        if (!fld._blnOn) return;
        var varReturn = fld.getMinValue();
        fld = null;
        return varReturn;
    },

    getFilterFieldValue_Max: function (strField) {
        var fld = this.getFilterField(strField);
        if (!fld) return;
        if (!fld._blnOn) return;
        var varReturn = fld.getMaxValue();
        fld = null;
        return varReturn;
    },

    enableButtons: function (bln) {
        if (this._ibtnReset) $R_IBTN.enableButton(this._ibtnReset, bln);
        if (this._ibtnApply) $R_IBTN.enableButton(this._ibtnApply, bln);
        if (this._ibtnOff) $R_IBTN.enableButton(this._ibtnOff, bln);
    },

    setFilterFieldEnterPressedEvents: function () {
        for (var i = 0, l = this._aryFilterFieldIDs.length; i < l; i++) {
            var fld = $find(this._aryFilterFieldIDs[i]);
            if (Object.getTypeName(fld) == "Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.TextBox"
                || Object.getTypeName(fld) == "Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.Numerical") {
                fld.addEnterPressed(Function.createDelegate(this, this.applyFilterAfterEnter));
            }
        }
    },

    applyFilterAfterEnter: function () {
        for (var i = 0, l = this._aryFilterFieldIDs.length; i < l; i++) {
            var fld = $find(this._aryFilterFieldIDs[i]);
            var typ = Object.getTypeName(fld);
            if (typ == "Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.TextBox") fld.textBoxBlur();
            if (typ == "Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.Numerical") fld.onBlur();
            typ = null; fld = null;
        }
        this.applyFilter();
    },

    addFilterParameters: function (obj) {
        if (!this._table._blnFiltersOn) return;
        for (var i = 0, l = this._aryFilterFieldIDs.length; i < l; i++) {
            if (this._aryFilterFieldIDs[i]) {
                var fld = $find(this._aryFilterFieldIDs[i]);
                if (fld) {
                    if (fld._blnOn) {
                        if (Object.getTypeName(fld) == "Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.Numerical" || Object.getTypeName(fld) == "Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.StarRating") {
                            obj.addParameter(String.format("{0}Lo", fld._strFilterField), fld.getMinValue());
                            obj.addParameter(String.format("{0}Hi", fld._strFilterField), fld.getMaxValue());
                            obj.addParameter(String.format("{0}_Comparison", fld._strFilterField), fld._ddl.value);
                        } else {
                            obj.addParameter(fld._strFilterField, fld.getValue());
                        }
                    }
                    obj.addParameter(String.format("{0}_IsShown", fld._strFilterField), fld._blnOn);
                    obj.addParameter(String.format("{0}_IsOn", fld._strFilterField), fld._blnOn);
                    obj.addParameter(String.format("{0}_Type", fld._strFilterField), fld._enmFieldType);
                }
                fld = null;
            }
        }
    },

    resultsLimitPressEnter: function () {
        if (this.setResultsLimit()) this.getData();
    },

    setResultsLimit: function () {
        if (this._txtLimitResults.value.length == 0) {
            this._txtLimitResults.value = this._intResultsLimit;
            return false;
        }
        if (this._intResultsLimit == this._txtLimitResults.value) return false;
        this._intResultsLimit = Number.parseInvariant(this._txtLimitResults.value);
        if (this._intResultsLimit < 1) this._intResultsLimit = 1;
        return true;
    },

    cancelDataCall: function () {
        this._blnGettingData = false;
        if (this._objData) this._objData.cancel();
    },

    cancelClicked: function () {
        this.cancelDataCall();
        if (this._ibtnCancel) $R_IBTN.showButton(this._ibtnCancel, false);
        this.showResultsPanels(true);
        this.enableButtons(true);
        this.clearMessages();
        this.addMessage($R_RES.SearchCancelled, $R_ENUM$MessageTypeList.Warning);
        //this.showNoData(true);
    },

    updateLockState: function (blnLocked) {
        //have we have changed state? 
        var blnStateWasChanged = (this._blnSaveState != blnLocked);

        //update our locked state for the DLN
        this._blnSaveState = blnLocked;

        //save or clear state (only if state was changed)
        if (blnStateWasChanged) {
            if (this._blnSaveState) {
                this.saveState();
            } else {
                this.clearState();
            }
        }
        if (!this._blnAllowSelection) this.displayLockState();
    },

    displayLockState: function () {
        if (this._ctlPagingButtonsTop) this._ctlPagingButtonsTop.setLockState(this._blnSaveState);
        if (this._ctlPagingButtonsBottom) this._ctlPagingButtonsBottom.setLockState(this._blnSaveState);
    },

    updateLockStateFromTopPagingButtons: function () {
        if (!this._ctlPagingButtonsBottom) return;
        this._ctlPagingButtonsBottom.setLockState(this._ctlPagingButtonsTop._blnStateLocked, true);
        this.updateLockState(this._ctlPagingButtonsTop._blnStateLocked);
    },

    updateLockStateFromBottomPagingButtons: function () {
        if (!this._ctlPagingButtonsTop) return;
        this._ctlPagingButtonsTop.setLockState(this._ctlPagingButtonsBottom._blnStateLocked, true);
        this.updateLockState(this._ctlPagingButtonsBottom._blnStateLocked);
    },

    showLockLoadingFromTopPagingButtons: function () {
        this._ctlPagingButtonsBottom.showLockLoading(true);
    },

    showLockLoadingFromBottomPagingButtons: function () {
        this._ctlPagingButtonsTop.showLockLoading(true);
    },

    clearState: function () {
        //do nothing - allow superclasses to handle it
    },

    saveState: function () {
        if (!this._blnSaveState) return;
        this.enableButtons(false);
        this._objData = new Rebound.GlobalTrader.Site.Data();
        this.addFilterParameters(this._objData);
        this.onSetupDataCall();
        this._objData.set_PathToData(this._strPathToData);
        this._objData.set_DataObject(this._strDataObject);
        this._objData.set_DataAction("SaveState");
        this._objData.addParameter("DLNID", this._intDataListNuggetID);
        this._objData.addParameter("DLNSubType", this._strDataListNuggetSubType);
        this._objData.addParameter("SortIndex", this._table._intSortColumnIndex + 1);
        this._objData.addParameter("SortDir", this._table._enmSortDirection);
        this._objData.addParameter("PageIndex", this._table._intCurrentPage - 1);
        this._objData.addParameter("PageSize", this._table._intCurrentPageSize);
        this._objData.addParameter("SaveState", true);
        this._objData.addDataOK(Function.createDelegate(this, this.saveStateOK));
        this._objData.addError(Function.createDelegate(this, this.saveStateOK));
        this._objData.addTimeout(Function.createDelegate(this, this.saveStateOK));
        $R_DQ.addToQueue(this._objData);
        $R_DQ.processQueue();
    },

    saveStateOK: function () {
        this.enableButtons(true);
    }

};
Rebound.GlobalTrader.Site.Controls.DataListNuggets.Base.registerClass("Rebound.GlobalTrader.Site.Controls.DataListNuggets.Base", Rebound.GlobalTrader.Site.Controls.Nuggets.Base, Sys.IDisposable);
