﻿
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


/*
--===============================================================================================================================================
TASK			UPDATED BY       DATE				ACTION		DESCRIPTION  
[US-236903]     Phuc Hoang		 25-Mar-2025		CREATE		The system doesn't keep selected client when switched from v2 to v1 or vice versa
=================================================================================================================================================
*/

CREATE OR ALTER PROCEDURE [dbo].[usp_select_MasterLoginSetupById]        
--        
@MasterLoginId int         
--        
AS      
BEGIN      
	SELECT ml.MasterLoginId ,ml.ADLoginName ,lg.EmployeeName ,ml.Inactive ,ml.LastClientNo as ClientNo 
	FROM tbMasterLogin ml WITH (NOLOCK) 
		JOIN tbLogin lg WITH (NOLOCK) on ml.MasterLoginId = lg.MasterLoginNo    
	WHERE ml.MasterLoginId  = @MasterLoginId    
END 

GO


