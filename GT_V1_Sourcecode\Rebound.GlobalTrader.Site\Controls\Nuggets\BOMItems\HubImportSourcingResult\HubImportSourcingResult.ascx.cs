﻿using System;
using System.Web.UI;

namespace Rebound.GlobalTrader.Site.Controls.Forms
{
    public partial class HubImportSourcingResult : Base
    {
		protected override void OnInit(EventArgs e)
		{
			base.OnInit(e);
			//TitleText = Functions.GetGlobalResource("Nuggets", "ProsCrossSellImportTool");
			TitleText = "Import Sourcing Results";
			AddScriptReference("Controls.Nuggets.BOMItems.HubImportSourcingResult.HubImportSourcingResult.js");
		}

		/// <summary>
		/// OnPreRender
		/// </summary>
		/// <param name="e"></param>
		protected override void OnPreRender(EventArgs e)
		{
			SetupScriptDescriptors();
			base.OnPreRender(e);
		}

		private void SetupScriptDescriptors()
		{
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.Forms.HubImportSourcingResult", ctlDesignBase.ClientID);
		}
	}
}