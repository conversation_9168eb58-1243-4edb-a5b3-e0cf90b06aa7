using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;
//Marker     Changed by      Date         Remarks
//[001]      <PERSON>     17/08/2021   (GTDP-198) Controlling specific product groups for purchasing
//[002]      Anand <PERSON>     25/10/2021   Product Warning message from copany setup.
namespace Rebound.GlobalTrader.Site.Controls.AutoSearch.Data {

	[WebService(Namespace = "http://tempuri.org/")]
	[WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    public class ProductsSetting : Rebound.GlobalTrader.Site.Controls.AutoSearch.Data.Base
    {

		protected override void GetData() {
            int? intPOHubClientNo;
            intPOHubClientNo = GetFormValue_NullableInt("intPOHubClientNo");

            int? intGlobalLoginClientNo;
            intGlobalLoginClientNo = GetFormValue_NullableInt("GlobalLoginClientNo");

            //int? intGlobalProductNo;
            //intGlobalProductNo = GetFormValue_NullableInt("GlobalProductNo");

            //bool? blnByGlobal;
            //blnByGlobal = GetFormValue_NullableBoolean("blnByGlobal", false);

            List<Product> lst = null;
			try {
                //lst = Product.AutoSearch(GetFormValue_StringForNameSearch("search"), intPOHubClientNo.HasValue && intPOHubClientNo.Value > 0?intPOHubClientNo.Value: SessionManager.ClientID);//GetFormValue_NullableBoolean("blnShowInactive")  GetFormValue_Boolean("ShowInactive")
                lst = Product.AutoSearch(GetFormValue_StringForNameSearch("search"), (intGlobalLoginClientNo.HasValue && intGlobalLoginClientNo.Value > 0) ? intGlobalLoginClientNo.Value : ((intPOHubClientNo.HasValue && intPOHubClientNo.Value > 0) ? intPOHubClientNo : SessionManager.ClientID));
                    
				JsonObject jsn = new JsonObject();
				jsn.AddVariable("TotalRecords", lst.Count);
				JsonObject jsnRows = new JsonObject(true);
				for (int i = 0; i < lst.Count; i++) {
					if (i < lst.Count) {
						JsonObject jsnRow = new JsonObject();
						jsnRow.AddVariable("ID", lst[i].ProductId);
						jsnRow.AddVariable("Name", lst[i].ProductDescription);
                        jsnRow.AddVariable("IsHa", lst[i].Hazarders);
                        //[001] code start
                        jsnRow.AddVariable("IsOrdViaIPO", lst[i].OrderViaIPOonly);
                        //[001] code end
                        //[002] code start
                        jsnRow.AddVariable("ProductMessage", Functions.ReplaceLineBreaks(lst[i].ProductMessage));
                        //[002] code end
                        jsnRow.AddVariable("IsRestrictedProd", lst[i].IsRestrictedProduct);
                        jsnRows.AddVariable(jsnRow);
						jsnRow.Dispose();
						jsnRow = null;
					}
				}
				jsn.AddVariable("Results", jsnRows);
				OutputResult(jsn);
				jsnRows.Dispose(); jsnRows = null;
				jsn.Dispose(); jsn = null;
			} catch (Exception e) {
				WriteError(e);
			} finally {
				lst = null;
			}
			base.GetData();
		}
	}
}