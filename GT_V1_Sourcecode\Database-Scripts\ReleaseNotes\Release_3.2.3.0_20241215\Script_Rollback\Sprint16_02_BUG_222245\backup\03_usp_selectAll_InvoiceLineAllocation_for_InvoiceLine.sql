﻿
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE OR ALTER PROCEDURE [dbo].[usp_selectAll_InvoiceLineAllocation_for_InvoiceLine]            
--            
    @InvoiceLineId int            
--            
AS       
    
Declare @ApprovalStatusId int,@SalesOrderLineId int,@ShowOGELWarning bit    
select @SalesOrderLineId=SalesOrderLineNo from vwInvoiceLineAllocation where InvoiceLineNo =@InvoiceLineId    
    
select  @ApprovalStatusId= sm.ApprovalStatusId  from  dbo.tbSalesOrderLine sol     
LEFT JOIN tbSO_ExportApprovalStatusOGEL es on es.SalesOrderLineNo = sol.SalesOrderLineId              
LEFT JOIN tbSOExportApprovalStatusMaster sm on sm.ApprovalStatusId = es.ApprovalStatusId  WHERE  sol.SalesOrderLineId  = @SalesOrderLineId    
       
SET @ShowOGELWarning = CASE WHEN  @ApprovalStatusId!=7 THEN 1 ELSE 0 END    
-- GET OGELStatus RP-3013 START     
    
    
SELECT     v.InvoiceLineAllocationId, v.InvoiceLineNo, v.Quantity, v.StockNo, v.SalesOrderLineNo, v.LotNo, v.LandedCost, v.SupplierRMALineNo, v.WarehouseNo,                   
                      v.Location, v.GoodsInLineNo, v.PurchaseOrderLineNo, v.CountryOfManufactureNo, v.CustomerRMAGoodsInLineNo, v.LineNotes,                   
                      v.UpdatedBy, v.DLUP, v.CountryOfManufactureName, v.LotName, v.WarehouseName, v.InvoiceNo, v.ClientNo, v.InvoiceNumber,                   
                      v.InvoiceDate, v.SalesOrderNo, v.Salesman2, v.ShippingCost, v.Freight, v.Salesman2Name, v.Salesman2Percent, v.SalesOrderNumber, v.CurrencyNo, v.CompanyNo, v.CompanyName, v.Part, v.DateCode, v.SupplierPart,                   
                      v.ProductNo, v.PackageNo, v.ManufacturerNo, v.ROHS, v.GoodsInNo, v.GoodsInNumber, v.ManufacturerName, v.ManufacturerCode, v.PackageName,                   
                      v.PackageDescription, v.ProductName, v.ProductDescription, v.SalesOrderLineId, v.Price, v.CustomerPart,                   
                      v.CurrencyCode, v.SupplierRMANo, v.SupplierRMANumber, v.PurchaseOrderNo, v.PurchasePrice, v.PurchaseOrderNumber,                   
                      v.SupplierNo, v.PurchaseCurrencyNo, v.PurchaseCurrencyCode, v.SupplierName,                   
                      v.Salesman, v.SalesmanName, v.Buyer, v.BuyerName,v.POSerialNO,v.PurchaseOrderId,            
                      v.QuantityAllocatedToCRMA,           
                      v.CountryName, v.TaxName, v.CustomerType,          
                      --CASE WHEN ipo.InternalPurchaseOrderId IS NULL THEN v.LandedCost ELSE ISNULL(v.ClientLandedCost,v.LandedCost) END AS  ClientLandedCost,        
                      ipo.CompanyNo AS ClientSupplierNo, ipoco.CompanyName AS ClientSupplierName        
                     , ipo.InternalPurchaseOrderId AS InternalPurchaseOrderNo          
, ipo.InternalPurchaseOrderNumber      
,@ShowOGELWarning ShowOGELWarning    
FROM dbo.vwInvoiceLineAllocation  v           
LEFT JOIN dbo.tbInternalPurchaseOrder ipo ON v.PurchaseOrderId = ipo.PurchaseOrderNo          
LEFT JOIN dbo.tbCompany ipoco ON ipoco.CompanyId = ipo.CompanyNo           
WHERE   v.InvoiceLineNo = @InvoiceLineId           
ORDER BY v.InvoiceLineAllocationId       
GO


