﻿/*
============================================================================================================================ 
TASK			UPDATED BY       DATE				ACTION		DESCRIPTION  
[US-235854]     CuongDox		 12-Mar-2025		CREATE		IPO- Simplified HUBRFQ Creation - Addition of PPV/ Bom Qualification at creation stage (Client Side)
============================================================================================================================  
*/
CREATE OR ALTER PROCEDURE  [dbo].[usp_InsertUpdate_Temp_PVVAnswer]        
@BOMGeneratedId   NVARCHAR(100) ,              
@ClientNo  int ,              
@Notes   nvarchar(MAX) = NULL ,              
@UpdatedBy  int    = NULL ,             
@RowsAffected int = NULL Output       
AS        
        
 DECLARE  @Count  integer            
SELECT   @Count = COUNT (*)              
FROM    dbo.tbHUBRFQPVVAnswerTemp               
WHERE    BomNoGenerated = @BOMGeneratedId       
       
select * into #tempAnswer from SplitString(@Notes,',|,')     
 IF  @Count  = 0              
 BEGIN         
    
select val from #tempAnswer tr insert into tbHUBRFQPVVAnswerTemp (PVVQuestionNo,BomNo,PVVAnswerName,clientno,UpdatedBy,BomNoGenerated)     
select LEFT(val,CHARINDEX('|',val)-1) as QuestionID    
,NULL as BOMNo    
,RIGHT(val,CHARINDEX('|',REVERSE(val))-1) as Answer    
,@ClientNo    
,@UpdatedBy
,@BOMGeneratedId
from #tempAnswer    
drop table #tempAnswer      
         
        
        
   End        
  else         
   Begin        
    
   update tbHUBRFQPVVAnswerTemp    
   set PVVAnswerName = RIGHT(#tempAnswer.val,CHARINDEX('|',REVERSE(#tempAnswer.val))-1)  ,UpdatedBy=@UpdatedBy,DLUP=Getdate()  
   from #tempAnswer    
   where PVVAnswerId = LEFT(#tempAnswer.val,CHARINDEX('|',#tempAnswer.val)-1)    
  
  
   drop table #tempAnswer     
   End        
        
 select @RowsAffected = @@ROWCOUNT  
  