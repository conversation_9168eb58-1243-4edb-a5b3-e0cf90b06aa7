///<reference name="MicrosoftAjax.js" />
///<reference path="~/Common/Data/Data.js" />
///<reference path="~/Common/Functions/Functions.js" />
///<reference path="~/Controls/IconButton/IconButton.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - add full dispose
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls");

Rebound.GlobalTrader.Site.Controls.Ellipses = function(element) { 
	Rebound.GlobalTrader.Site.Controls.Ellipses.initializeBase(this, [element]);
	this._objData = null;
};

Rebound.GlobalTrader.Site.Controls.Ellipses.prototype = {

	get_hypValue: function() { return this._hypValue; }, 	set_hypValue: function(lbl) { if (this._hypValue !== lbl)  this._hypValue = lbl; }, 
	get_hyp: function() { return this._hyp; }, 	set_hyp: function(hyp) { if (this._hyp !== hyp)  this._hyp = hyp; }, 
	get_lblLoading: function() { return this._lblLoading; }, 	set_lblLoading: function(pnlEllipsesLoading) { if (this._lblLoading !== pnlEllipsesLoading)  this._lblLoading = pnlEllipsesLoading; }, 
	get_lblError: function() { return this._lblError; }, 	set_lblError: function(pnlEllipsesError) { if (this._lblError !== pnlEllipsesError)  this._lblError = pnlEllipsesError; }, 
	get_blnShowParentheses: function() { return this._blnShowParentheses; }, 	set_blnShowParentheses: function(blnShowParentheses) { if (this._blnShowParentheses !== blnShowParentheses)  this._blnShowParentheses = blnShowParentheses; }, 
	get_blnShowParenthesesAfterResult: function() { return this._blnShowParenthesesAfterResult; }, 	set_blnShowParenthesesAfterResult: function(blnShowParenthesesAfterResult) { if (this._blnShowParenthesesAfterResult !== blnShowParenthesesAfterResult)  this._blnShowParenthesesAfterResult = blnShowParenthesesAfterResult; }, 

	addSetupData: function(handler) { this.get_events().addHandler("SetupData", handler); },
	removeSetupData: function(handler) { this.get_events().removeHandler("SetupData", handler); },
	onSetupData: function() {
		var handler = this.get_events().getHandler("SetupData");
		if (handler) handler(this, Sys.EventArgs.Empty);
	},
	
	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.Ellipses.callBaseMethod(this, "initialize");
		if (this._hyp) $addHandler(this._hyp, "click", Function.createDelegate(this, this.getData));
		if (this._hypValue) $addHandler(this._hypValue, "click", Function.createDelegate(this, this.getData));
	},

	dispose: function() { 
		if (this.isDisposed) return;
		if (this.get_element()) $clearHandlers(this.get_element());
		if (this._hyp) $clearHandlers(this._hyp);
		if (this._hypValue) $clearHandlers(this._hypValue);
		if (this._objData) this._objData.dispose();
		this._objData = null;
		this._hypValue = null;
		this._hyp = null;
		this._lblLoading = null;
		this._lblError = null;
		this._blnShowParentheses = null;
		this._blnShowParenthesesAfterResult = null;
		Rebound.GlobalTrader.Site.Controls.Ellipses.callBaseMethod(this, "dispose");
		this.isDisposed = true;
	},

	getData: function() {
		this.showLoading();
		this._objData = new Rebound.GlobalTrader.Site.Data();
		this.onSetupData();
		this._objData.addDataOK(Function.createDelegate(this, this.getDataComplete));
		this._objData.addTimeout(Function.createDelegate(this, this.showTimeOut));
		this._objData.addError(Function.createDelegate(this, this.showError));
		this._objData.addTimeout(Function.createDelegate(this, this.showError));
		$R_DQ.addToQueue(this._objData, false);
		$R_DQ.processQueue();
		this._objData = null;	
	},
	
	getDataComplete: function(args) {
		var strHTML = args._result.Value;
		if (this._blnShowParenthesesAfterResult) strHTML = String.format("({0})", strHTML);
		$R_FN.setInnerHTML(this._hypValue, strHTML);
		this.showContent();
	},
		
	hide: function() {
		$R_FN.showElement(this.get_element(), false);
	},
	
	show: function() {
		$R_FN.showElement(this.get_element(), true);
	},
	
	reset: function() {
		$R_FN.showElement(this._hypValue, false);
		$R_FN.showElement(this._lblLoading, false);
		$R_FN.showElement(this._lblError, false);
		$R_FN.showElement(this._hyp, true);
	},

	showLoading: function() {
		$R_FN.showElement(this._hypValue, false);
		$R_FN.showElement(this._lblLoading, true);
		$R_FN.showElement(this._lblError, false);
		$R_FN.showElement(this._hyp, false);
	},
	
	showError: function() {
		this.reset();
	},
	
	showTimeOut: function() {
		this.reset();
	},
	
	showContent: function() {
		$R_FN.showElement(this._hypValue, true);
		$R_FN.showElement(this._lblLoading, false);
		$R_FN.showElement(this._lblError, false);
		$R_FN.showElement(this._hyp, false);
	}

};

Rebound.GlobalTrader.Site.Controls.Ellipses.registerClass("Rebound.GlobalTrader.Site.Controls.Ellipses", Sys.UI.Control, Sys.IDisposable);