///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.LeftNuggets");

Rebound.GlobalTrader.Site.Controls.LeftNuggets.RecentlyViewed = function(element) { 
	Rebound.GlobalTrader.Site.Controls.LeftNuggets.RecentlyViewed.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.LeftNuggets.RecentlyViewed.prototype = {

	get_pnlItems: function() { return this._pnlItems; }, 	set_pnlItems: function(v) { if (this._pnlItems !== v)  this._pnlItems = v; }, 

	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.LeftNuggets.RecentlyViewed.callBaseMethod(this, "initialize");	
	},

	dispose: function() { 
		if (this.isDisposed) return;
		this._pnlItems = null;
		Rebound.GlobalTrader.Site.Controls.LeftNuggets.RecentlyViewed.callBaseMethod(this, "dispose");
	},
	
	addItem: function(strURL, strTitle) {
		if (!strURL) strURL = document.URL;
		Rebound.GlobalTrader.Site.WebServices.AddToRecentlyViewedList(this._element.id, strURL, strTitle, Function.createDelegate(this, this.updateItems));
	},
	
	updateItems: function(str) {
		if (!str) return;
		$R_FN.setInnerHTML(this._pnlItems, str);
	},
	
	refresh: function() {
		Rebound.GlobalTrader.Site.WebServices.RefreshRecentlyViewedList(this._element.id, Function.createDelegate(this, this.updateItems));
	},
	
	toggleLock: function(i, intID) {
		var el = $get(String.format("{0}_item{1}", this._element.id, i));
		if (!el) return;
		Rebound.GlobalTrader.Site.WebServices.LockRecentlyViewedItem(i, intID, (el.getAttribute("bgt_locked") == "false"), Function.createDelegate(this, this.toggleLockComplete));
		el = null;
	},

	toggleLockComplete: function(i) {
		var el = $get(String.format("{0}_item{1}", this._element.id, i));
		if (!el) return;
		if (el.getAttribute("bgt_locked") == "true") {
			el.setAttribute("bgt_locked", "false");
			el.className = "recentlyViewedLockOff";
		} else {
			el.setAttribute("bgt_locked", "true");
			el.className = "recentlyViewedLockOn";
		}
		el = null;
	}

};

Rebound.GlobalTrader.Site.Controls.LeftNuggets.RecentlyViewed.registerClass("Rebound.GlobalTrader.Site.Controls.LeftNuggets.RecentlyViewed", Rebound.GlobalTrader.Site.Controls.LeftNuggets.Base);
