using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;

namespace Rebound.GlobalTrader.Site.Controls.Nuggets.Data {
	[WebService(Namespace = "http://tempuri.org/")]
	[WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
	public class CompanyGlobalSalesPDetails : Rebound.GlobalTrader.Site.Data.Base {

		public override void ProcessRequest(HttpContext context) {
			if (base.init(context)) {
				switch (Action) {
					case "GetGlobalSalesPersonsDetails": GetGlobalSalesPersonsDetails(); break;
					case "SaveAddNew": SaveAddNew(); break;
					case "SaveEdit": SaveEdit(); break;
					case "Delete": Delete(); break;
                    case "validateDuplicate": validateDuplicate(); break;
                    default: WriteErrorActionNotFound(); break;
				}
			}
		}

		private void GetGlobalSalesPersonsDetails() {
			JsonObject jsn = new JsonObject();
			JsonObject jsnItems = new JsonObject(true);
			foreach (BLL.GlobalSalesPerson lnk in BLL.GlobalSalesPerson.GetListForGlobalSalesPerson(ID)) {
				JsonObject jsnItem = new JsonObject();
				jsnItem.AddVariable("ID", lnk.GlobalSalesPersonId);
				jsnItem.AddVariable("SalesPersonName", lnk.SalesPersonName);
				jsnItem.AddVariable("ClientName", lnk.ClientName);
				jsnItem.AddVariable("CompanyNo", lnk.CompanyNo);
				jsnItems.AddVariable(jsnItem);
				jsnItem.Dispose(); jsnItem = null;
			}
			jsn.AddVariable("Items", jsnItems);
			OutputResult(jsn);
			jsn.Dispose(); jsn = null;
		}

		private void SaveEdit() {
			BLL.ManufacturerLink lnk = ManufacturerLink.Get(ID);
			if (lnk == null) {
				WriteErrorDataNotFound();
			} else {
				try {
					lnk.SupplierRating = GetFormValue_NullableInt("SupplierRating");
					lnk.UpdatedBy = LoginID;
					JsonObject jsn = new JsonObject();
					jsn.AddVariable("Result", lnk.Update());
					OutputResult(jsn);
				} catch (Exception e) {
					WriteError(e);
				} finally {
					lnk = null;
				}
			}
		}

		private void SaveAddNew() {
			try {
				JsonObject jsn = new JsonObject();
				int intNewID = 0;
				if (BLL.ManufacturerLink.CountForManufacturerAndSupplier(GetFormValue_NullableInt("ManufacturerNo"), GetFormValue_NullableInt("SupplierNo")) > 0) {
					BLL.ManufacturerLink lnk = ManufacturerLink.GetForManufacturerAndSupplier(GetFormValue_NullableInt("ManufacturerNo"), GetFormValue_NullableInt("SupplierNo"));
					intNewID = lnk.ManufacturerLinkId;
					lnk.SupplierRating = GetFormValue_NullableInt("SupplierRating");
					lnk.UpdatedBy = LoginID;
					lnk.Update();
				} else {
					intNewID = BLL.ManufacturerLink.Insert(
						GetFormValue_NullableInt("ManufacturerNo", 0)
					, GetFormValue_NullableInt("SupplierNo", 0)
					, 0
					, GetFormValue_NullableInt("SupplierRating", 0)
					, LoginID
					);
				}
				jsn.AddVariable("NewID", 1);
				OutputResult(jsn);
			} catch (Exception e) {
				WriteError(e);
			}
		}

		private void Delete() {
			try {
				JsonObject jsn = new JsonObject();
				BLL.ManufacturerLink.Delete(ID);
				jsn.AddVariable("Result", true);
				OutputResult(jsn);
			} catch (Exception e) {
				WriteError(e);
			}
		}
        public void validateDuplicate()
        {
            try
            {
                JsonObject jsn = new JsonObject();
                jsn.AddVariable("Result", BLL.Company.validateDuplicate(ID, GetFormValue_NullableInt("ManufacturerNo")));
                OutputResult(jsn);
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }

    }


}

