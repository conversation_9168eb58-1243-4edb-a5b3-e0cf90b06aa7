Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");Rebound.GlobalTrader.Site.Controls.DropDowns.ClientHubRFQ=function(n){Rebound.GlobalTrader.Site.Controls.DropDowns.ClientHubRFQ.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.DropDowns.ClientHubRFQ.prototype={initialize:function(){this.addSetupDataCall(Function.createDelegate(this,this.setupDataCall));this.addGetDataOK(Function.createDelegate(this,this.dataCallOK));Rebound.GlobalTrader.Site.Controls.DropDowns.ClientHubRFQ.callBaseMethod(this,"initialize")},dispose:function(){this.isDisposed||Rebound.GlobalTrader.Site.Controls.DropDowns.ClientHubRFQ.callBaseMethod(this,"dispose")},setupDataCall:function(){this._objData.set_PathToData("controls/DropDowns/ClientHubRFQ");this._objData.set_DataObject("ClientHubRFQ");this._objData.set_DataAction("GetData")},dataCallOK:function(){var t=this._objData._result,n;if(t.Clients)for(n=0;n<t.Clients.length;n++)this.addOption(t.Clients[n].Name,t.Clients[n].ID)}};Rebound.GlobalTrader.Site.Controls.DropDowns.ClientHubRFQ.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.ClientHubRFQ",Rebound.GlobalTrader.Site.Controls.DropDowns.Base);