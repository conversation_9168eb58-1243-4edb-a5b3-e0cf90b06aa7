//-----------------------------------------------------------------------------------------
// RP 14.10.2009:
// - added Lock / Unlock buttons for saving state (in DLN)
//-----------------------------------------------------------------------------------------
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;

namespace Rebound.GlobalTrader.Site.Controls {

	[ToolboxData("<{0}:PagingButtons runat=server></{0}:PagingButtons>")]
	public class PagingButtons : Panel, INamingContainer, IScriptControl {

		#region Locals

		protected ScriptManager _sm;
		private Panel _pnlLeft;
		private Panel _pnlRight;
		private Label _lblTotalResults;
		private Label _lblCurrentPage;
		private Label _lblTotalPages;
		private HyperLink _hypShowFilter;
		private Label _lblPageNumbers;
		private HyperLink _hypPrev;
		private Label _lblPrevDisabled;
		private HyperLink _hypNext;
		private Label _lblNextDisabled;
		private Label _lblPageSizeLinks;
		private Panel _pnlLock;

		#endregion

		#region Properties

		/// <summary>
		/// How many pages to show instead of dots
		/// </summary>
		private int _intPagesToShowAtEnds = 2;
		public int PagesToShowAtEnds {
			get { return _intPagesToShowAtEnds; }
			set { _intPagesToShowAtEnds = value; }
		}

		/// <summary>
		/// How many pages to show instead of dots
		/// </summary>
		private int _intPagesToShowEitherSideOfCurrent = 2;
		public int PagesToShowEitherSideOfCurrent {
			get { return _intPagesToShowEitherSideOfCurrent; }
			set { _intPagesToShowEitherSideOfCurrent = value; }
		}

		/// <summary>
		/// Pages dots
		/// </summary>
		private string _strPagesDots = " ... ";
		public string PagesDots {
			get { return _strPagesDots; }
			set { _strPagesDots = value; }
		}

		/// <summary>
		/// Current Page
		/// </summary>
		private int _intCurrentPage;
		public int CurrentPage {
			get { return _intCurrentPage; }
			set { _intCurrentPage = value; }
		}

		/// <summary>
		/// Total results
		/// </summary>
		protected int _intTotalResults;
		public int TotalResults {
			get { return _intTotalResults; }
		}

		/// <summary>
		/// Page Size
		/// </summary>
		private int _intCurrentPageSize = 10;
		public int CurrentPageSize {
			get { return _intCurrentPageSize; }
			set { _intCurrentPageSize = value; }
		}

		/// <summary>
		/// Are the filters on?
		/// </summary>
		private bool _blnFiltersOn = false;
		public bool FiltersOn {
			get { return _blnFiltersOn; }
			set { _blnFiltersOn = value; }
		}

		/// <summary>
		/// show the filters button?
		/// </summary>
		private bool _blnShowFilterButton = true;
		public bool ShowFilterButton {
			get { return _blnShowFilterButton; }
			set { _blnShowFilterButton = value; }
		}

		private bool _blnShowLockButtons = false;
		public bool ShowLockButtons {
			get { return _blnShowLockButtons; }
			set { _blnShowLockButtons = value; }
		}

		#endregion

		#region Overrides

		/// <summary>
		/// OnPreRender
		/// </summary>
		/// <param name="e"></param>
		protected override void OnPreRender(EventArgs e) {
			if (!this.DesignMode) {
				_sm = ScriptManager.GetCurrent(Page);
				if (_sm == null) throw new HttpException("A ScriptManager control must exist on the current page.");
				_sm.RegisterScriptControl(this);
			}
			base.OnPreRender(e);
		}

		/// <summary>
		/// Render
		/// </summary>
		/// <param name="writer"></param>
		protected override void Render(HtmlTextWriter writer) {
			if (!this.DesignMode) _sm.RegisterScriptDescriptors(this);
			base.Render(writer);
		}

		protected override void CreateChildControls() {

			//outer
			CssClass = "pagingControls invisible";

			//left controls
			_pnlLeft = ControlBuilders.CreatePanelInsideParent(this, "pagingControlsLeft");

			//total results
			_lblTotalResults = ControlBuilders.CreateLabelInsideParent(ControlBuilders.CreateHtmlGenericControlInsideParent(_pnlLeft, "b"));
			_lblTotalResults.ID = "litTotalResults";
			ControlBuilders.CreateLiteralInsideParent(_pnlLeft, string.Format("&nbsp;{0}", Functions.GetGlobalResource("misc", "ResultsWithOptionalS")));

			//page x of y
			ControlBuilders.CreateLiteralInsideParent(_pnlLeft, string.Format("&nbsp;|&nbsp;{0}&nbsp;", Functions.GetGlobalResource("misc", "Page")));
			_lblCurrentPage = ControlBuilders.CreateLabelInsideParent(ControlBuilders.CreateHtmlGenericControlInsideParent(_pnlLeft, "b"));
			_lblCurrentPage.ID = "litCurrentPage";
			ControlBuilders.CreateLiteralInsideParent(_pnlLeft, string.Format("&nbsp;{0}&nbsp;", Functions.GetGlobalResource("misc", "of")));
			_lblTotalPages = ControlBuilders.CreateLabelInsideParent(ControlBuilders.CreateHtmlGenericControlInsideParent(_pnlLeft, "b"));
			_lblTotalPages.ID = "litTotalPages";

			//page size buttons
			ControlBuilders.CreateLiteralInsideParent(_pnlLeft, string.Format("&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<b>{0}:</b>&nbsp;", Functions.GetGlobalResource("misc", "PerPage")));
			_lblPageSizeLinks = ControlBuilders.CreateLabelInsideParent(_pnlLeft);
			_lblPageSizeLinks.ID = "lblPageSizeLinks";

			//Filter
			if (_blnShowFilterButton) {
				ControlBuilders.CreateLiteralInsideParent(_pnlLeft, "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;");
				_hypShowFilter = ControlBuilders.CreateHyperLinkInsideParent(_pnlLeft, "showFilter", "javascript:void(0);", Functions.GetGlobalResource("misc", "Filter"));
				_hypShowFilter.ID = "hypShowFilter";
			}

			if (_blnShowLockButtons) {
				_pnlLock = ControlBuilders.CreatePanelInsideParent(_pnlLeft, "lockState");
				ControlBuilders.CreateImageInsideParent(_pnlLock, "", "~/images/x.gif", 5, 5);
				_pnlLock.ToolTip = Functions.GetGlobalResource("Misc", "DataListNuggetStateButtonTooltip");
				_pnlLock.ID = "pnlLock";
			}

			//page numbers
			_pnlRight = ControlBuilders.CreatePanelInsideParent(this, "pagingControlsRight");
			_lblPageNumbers = ControlBuilders.CreateLabelInsideParent(_pnlRight);
			_lblPageNumbers.ID = "pnlPageNumbers";

			//previous page link
			_hypPrev = ControlBuilders.CreateHyperLinkInsideParent(_pnlRight, "pagingNextPrev pagingPrev", "javascript:void(0);", "&laquo");
			_hypPrev.ID = "hypPrev";

			//previous page disabled link
			_lblPrevDisabled = ControlBuilders.CreateLabelInsideParent(_pnlRight, "pagingPrev pagingNextPrevDisabled invisible", _hypPrev.Text);
			_lblPrevDisabled.ID = "lblPrevDisabled";

			//next page link
			_hypNext = ControlBuilders.CreateHyperLinkInsideParent(_pnlRight, "pagingNextPrev pagingPrev", "javascript:void(0);", "&raquo");
			_hypNext.ID = "hypNext";

			//next page disabled text
			_lblNextDisabled = ControlBuilders.CreateLabelInsideParent(_pnlRight, "pagingNext pagingNextPrevDisabled invisible", _hypNext.Text);
			_lblNextDisabled.ID = "lblNextDisabled";

			base.CreateChildControls();
		}

		#endregion

		#region IScriptControl Members

		protected virtual IEnumerable<ScriptReference> GetScriptReferences() {
			bool blnDebug = false;
#if (DEBUG)
			blnDebug = true;
#endif
			return new ScriptReference[] { Functions.GetScriptReference(blnDebug, "Rebound.GlobalTrader.Site", "Controls.Basic.PagingButtons.PagingButtons", true) };
		}

		protected virtual IEnumerable<ScriptDescriptor> GetScriptDescriptors() {
			ScriptControlDescriptor descriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.PagingButtons", this.ClientID);
			descriptor.AddElementProperty("pnlLeft", _pnlLeft.ClientID);
			descriptor.AddElementProperty("lblTotalResults", _lblTotalResults.ClientID);
			descriptor.AddElementProperty("lblCurrentPage", _lblCurrentPage.ClientID);
			descriptor.AddElementProperty("lblTotalPages", _lblTotalPages.ClientID);
			if (_blnShowFilterButton) descriptor.AddElementProperty("hypShowFilter", _hypShowFilter.ClientID);
			descriptor.AddElementProperty("lblPageNumbers", _lblPageNumbers.ClientID);
			descriptor.AddElementProperty("hypPrev", _hypPrev.ClientID);
			descriptor.AddElementProperty("lblPrevDisabled", _lblPrevDisabled.ClientID);
			descriptor.AddElementProperty("hypNext", _hypNext.ClientID);
			descriptor.AddElementProperty("lblNextDisabled", _lblNextDisabled.ClientID);
			descriptor.AddElementProperty("lblPageSizeLinks", _lblPageSizeLinks.ClientID);
			if (_blnShowLockButtons) descriptor.AddElementProperty("pnlLock", _pnlLock.ClientID);
			descriptor.AddProperty("intPagesToShowAtEnds", _intPagesToShowAtEnds);
			descriptor.AddProperty("intPagesToShowEitherSideOfCurrent", _intPagesToShowEitherSideOfCurrent);
			descriptor.AddProperty("strPagesDots", _strPagesDots);
			descriptor.AddProperty("intCurrentPage", _intCurrentPage);
			descriptor.AddProperty("intTotalResults", _intTotalResults);
			descriptor.AddProperty("intCurrentPageSize", _intCurrentPageSize);
			descriptor.AddProperty("blnFiltersOn", _blnFiltersOn);
			return new ScriptDescriptor[] { descriptor };
		}

		IEnumerable<ScriptReference> IScriptControl.GetScriptReferences() { return GetScriptReferences(); }

		IEnumerable<ScriptDescriptor> IScriptControl.GetScriptDescriptors() { return GetScriptDescriptors(); }

		#endregion
	}
}
