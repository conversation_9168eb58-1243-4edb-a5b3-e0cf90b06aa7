<%--
Marker     Changed by      Date         Remarks
[001]      Vinay           29/05/2012   This need to implement Incoterms field is requird.
[002]      Vinay           13/06/2012   This need to Add Incoterms field in company section
[RP-2339]	Ravi		11-10-2023		AS6081 GT Documents - Show AS6081 on detail screens
--%>
<%@ Control Language="C#" CodeBehind="CRMAAdd_Add.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Forms.CRMAAdd_Add" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>

<ReboundUI_Form:DesignBase ID="ctlDB" runat="server" ShowRequiredFieldsExplanation="true" ShowQuickHelp="false">
	<Links>
		<ReboundUI:IconButton ID="ibtnSave" runat="server" IconGroup="Nugget" IconTitleResource="Save" IconCSSType="Save" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
		<ReboundUI:IconButton ID="ibtnCancel" runat="server" IconGroup="Nugget" IconTitleResource="Cancel" IconCSSType="Cancel" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
		<ReboundUI:IconButton ID="ibtnSend" runat="server" IconGroup="Nugget" IconTitleResource="Send" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
		<ReboundUI:IconButton ID="ibtnContinue" runat="server" IconGroup="Nugget" IconTitleResource="Continue" IconCSSType="Continue" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
	</Links>

	<Explanation>
		<ReboundUI:MultiStep ID="ctlMultiStep" runat="server">
			<Items>
				<ReboundUI:MultiStepItem ID="ctlItem1" runat="server" ResourceTitle="CRMAAdd_SelectInvoice" />
				<ReboundUI:MultiStepItem ID="ctlItem2" runat="server" ResourceTitle="CRMAAdd_EnterDetail" />
				<ReboundUI:MultiStepItem ID="ctlItem3" runat="server" ResourceTitle="CRMAAdd_Notify" />
			</Items>
		</ReboundUI:MultiStep>
	</Explanation>

	<Content>
		<!-- Step 1 ------------------------------------------------------------------->
		<ReboundUI_Table:Form id="frmStep1" runat="server">
			<asp:TableRow id="trSelectInvoice" runat="server">
				<asp:TableCell id="tdSelectInvoice" runat="server">
					<ReboundItemSearch:Invoices id="ctlSelectInvoice" runat="server" />
					<asp:Panel id="pnlLines" runat="server" CssClass="itemSearch invisible">
						<h5><%=Functions.GetGlobalResource("Misc", "Invoices")%></h5>
						<asp:Panel id="pnlLinesError" runat="server" CssClass="itemSearchError invisible"><asp:Label id="lblLinesError" runat="server" /></asp:Panel>
						<asp:Panel id="pnlLinesLoading" runat="server" CssClass="loading invisible"><%=Functions.GetGlobalResource("Misc", "Loading")%></asp:Panel>
						<asp:Panel id="pnlLinesNoneFound" runat="server" CssClass="noneFound invisible"><%=Functions.GetGlobalResource("NotFound", "Generic")%></asp:Panel>
						<ReboundUI:FlexiDataTable ID="tblLines" runat="server" AllowMultipleSelection="true" />
					</asp:Panel>
				</asp:TableCell>
			</asp:TableRow>
		</ReboundUI_Table:Form>

		<!-- Step 2 ------------------------------------------------------------------->
		<ReboundUI_Table:Form id="frmStep2" runat="server">
			<ReboundUI_Form:FormField id="ctlCompany" runat="server" FieldID="lblCompany" ResourceTitle="Company">
				<Field><asp:Label ID="lblCompany" runat="server" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlShipToAddress" runat="server" FieldID="lblShipToAddress" ResourceTitle="ShipToAddress">
				<Field><asp:Label ID="lblShipToAddress" runat="server" Width="450" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlContact" runat="server" FieldID="lblContact" ResourceTitle="Contact">
				<Field><asp:Label ID="lblContact" runat="server" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlInvoiceNumber" runat="server" FieldID="lblInvoiceNumber" ResourceTitle="InvoiceNo">
				<Field><asp:Label ID="lblInvoiceNumber" runat="server" /></Field>
			</ReboundUI_Form:FormField>
			<%--[RP-2339] start--%>
			<ReboundUI_Form:FormField id="ctlAS6081" runat="server" FieldID="lblAS6081" ResourceTitle="AS6081Label">
				<Field><asp:Label ID="lblAS6081" runat="server" /></Field>
			</ReboundUI_Form:FormField>
			<%--[RP-2339] end--%>
			<ReboundUI_Form:FormField id="ctlDivision" runat="server" FieldID="lblDivision" ResourceTitle="Division">
				<Field><asp:Label ID="lblDivision" runat="server" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlWarehouse" runat="server" FieldID="ddlWarehouse" ResourceTitle="Warehouse" IsRequiredField="true">
				<Field><ReboundDropDown:Warehouse ID="ddlWarehouse" runat="server" IncludeVirtual="false" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlShipVia" runat="server" FieldID="ddlShipVia" ResourceTitle="ShipVia" IsRequiredField="true">
				<Field><ReboundDropDown:ShipMethod ID="ddlShipVia" runat="server" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlAccount" runat="server" FieldID="txtAccount" ResourceTitle="ShippingAccountNo">
				<Field><ReboundUI:ReboundTextBox ID="txtAccount" runat="server" Width="300" /></Field>
			</ReboundUI_Form:FormField>

            <%--[001] code start--%>
			<ReboundUI_Form:FormField id="ctlIncoterm" runat="server" FieldID="ddlIncoterm" ResourceTitle="Incoterm" IsRequiredField="true">
				<Field><ReboundDropDown:Incoterm ID="ddlIncoterm" runat="server" /></Field>
			</ReboundUI_Form:FormField>
			<%--[001] code end--%>
			
			<ReboundUI_Form:FormField id="ctlAuthorisedBy" runat="server" FieldID="ddlAuthorisedBy" ResourceTitle="AuthorisedBy" IsRequiredField="true">
				<Field><ReboundDropDown:Employee ID="ddlAuthorisedBy" runat="server" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlRMADate" runat="server" FieldID="txtRMADate" ResourceTitle="CRMADate" IsRequiredField="true">
				<Field>
					<ReboundUI:ReboundTextBox ID="txtRMADate" runat="server" Width="150" />
					<ReboundUI:Calendar ID="calRMADate" runat="server" RelatedTextBoxID="txtRMADate" />
				</Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlNotes" runat="server" FieldID="txtNotes" ResourceTitle="CustomerNotes">
				<Field><ReboundUI:ReboundTextBox ID="txtNotes" runat="server" Width="450" Rows="2" TextMode="MultiLine" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlInstructions" runat="server" FieldID="txtInstructions" ResourceTitle="Instructions">
				<Field><ReboundUI:ReboundTextBox ID="txtInstructions" runat="server" Width="450" Rows="2" TextMode="MultiLine" /></Field>
			</ReboundUI_Form:FormField>

            <ReboundUI_Form:FormField id="ctlCustomerRejectionNo" runat="server" FieldID="txtCRNo" ResourceTitle="CustomerRejectionNo">
				<Field><ReboundUI:ReboundTextBox ID="txtCRNo" MaxLength="20" runat="server" Width="150"   /></Field>
			</ReboundUI_Form:FormField>

		</ReboundUI_Table:Form>
		
		
		<!-- Step 3 ------------------------------------------------------------------->
		<ReboundUI_Table:Form id="frmStep3" runat="server">
			<ReboundUI_Form:FormField id="ctlSendMail" runat="server" FieldID="chkSendMail" ResourceTitle="ShouldMailBeSent">
				<Field><ReboundUI:ImageCheckBox ID="chkSendMail" runat="server" Enabled="true" Checked="true" /></Field>
			</ReboundUI_Form:FormField>
			<ReboundFormFieldCollection:SendMailMessage id="ctlSendMailMessage" runat="server" />
		</ReboundUI_Table:Form>

	</Content>
</ReboundUI_Form:DesignBase>
