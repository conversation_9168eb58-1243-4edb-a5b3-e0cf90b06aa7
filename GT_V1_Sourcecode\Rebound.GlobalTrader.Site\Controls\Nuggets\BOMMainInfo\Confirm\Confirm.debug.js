///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference name="~/Controls/IconButton/IconButton.js" />
///<reference name="~/Controls/ReboundTextBox/ReboundTextBox.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
//[001]     <PERSON><PERSON>r   16-11-2016    Added combox and Get Receipient ID from Combo box.
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.Confirm = function(element) {
    Rebound.GlobalTrader.Site.Controls.Forms.Confirm.initializeBase(this, [element]);
    this._intBOMID = -1;
    this._isAddButtonEnable = true;
    this._ValidMessage = "";
    this._blnReqInValid = false;
    this._intContact2No = -1;
    this._blnPVVBOMValidateMessage = "";
    this._PVVBOMCountValid = false;
};

Rebound.GlobalTrader.Site.Controls.Forms.Confirm.prototype = {

    get_intBOMID: function() { return this._intBOMID; }, set_intBOMID: function(value) { if (this._intBOMID !== value) this._intBOMID = value; },
    get_BomCode: function() { return this._BomCode; }, set_BomCode: function(value) { if (this._BomCode !== value) this._BomCode = value; },
    get_BomName: function() { return this._BomName; }, set_BomName: function(value) { if (this._BomName !== value) this._BomName = value; },
    get_BomCompanyName: function() { return this._BomCompanyName; }, set_BomCompanyName: function(value) { if (this._BomCompanyName !== value) this._BomCompanyName = value; },
    get_BomCompanyNo: function() { return this._BomCompanyNo; }, set_BomCompanyNo: function(value) { if (this._BomCompanyNo !== value) this._BomCompanyNo = value; },
    get_intContact2No: function () { return this._intContact2No; }, set_intContact2No: function (value) { if (this._intContact2No !== value) this._intContact2No = value; },
    initialize: function() {
        Rebound.GlobalTrader.Site.Controls.Forms.Confirm.callBaseMethod(this, "initialize");
        this.addShown(Function.createDelegate(this, this.formShown));
        //$("#Exp1").hide();
        //$("#Exp2").hide();
        //$("#Exp2").html('');
    },

    dispose: function() {
        if (this.isDisposed) return;
        this._intBOMID = null;
        this._intContact2No = null;
        this._blnPVVBOMValidateMessage = null;
        this._PVVBOMCountValid = null;
        Rebound.GlobalTrader.Site.Controls.Forms.Confirm.callBaseMethod(this, "dispose");
    },

    formShown: function () {
        

        if (this._blnFirstTimeShown) {
            this._ctlConfirm = this.getFieldComponent("ctlConfirm");
            this._ctlConfirm.addClickYesEvent(Function.createDelegate(this, this.yesClicked));
            this._ctlConfirm.addClickNoEvent(Function.createDelegate(this, this.noClicked));
            //[001] Start Code
            this._ctlMail = $find(this.getField("ctlSendMailMessage").ID);
            this._ctlMail._ctlRelatedForm = this;
            //[001]  End Code
        }
        this.getFieldDropDownData("ctlSalesperson");
       
        if (this._blnReqInValid == true) {
            this.showError(true, this._ValidMessage);
            this.showField("ctlSalesperson", false);
            this.showField("ctlSendMailMessage", false);
            //this.showField("ctlQuoteRequired", false);
            this.showField("ctlConfirm", false);
        }
        else if (this._PVVBOMCountValid == false) {
           // $("#Exp2").show();
           // $("#Exp1").hide();
            //$("#Exp2").html(this._blnPVVBOMValidateMessage);
            this.showError(true, this._blnPVVBOMValidateMessage)
           // this.showError(true, this._blnPVVBOMValidateMessage);
            this.showField("ctlSalesperson", true);
            this.showField("ctlSendMailMessage", true);
            this.showField("ctlConfirm", true);
        }
        else {
            $("#Exp1").show();
           // $("#Exp2").hide();

            this.showField("ctlSalesperson", true);
            this.showField("ctlSendMailMessage", true);
            //this.showField("ctlQuoteRequired", true);
            this.showField("ctlConfirm", true);
        }

        $addHandler(document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl21_ibtnBack_hyp"), "click", Function.createDelegate(this, this.noClicked1));
        $addHandler(document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl22_ibtnBack_hyp"), "click", Function.createDelegate(this, this.noClicked1));
        
    },

    noClicked1: function () {
        this.onNotConfirmed();
    },

    yesClicked: function () {
       if (!this.validateForm()) return;
        this.showSaving(true);
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("Controls/Nuggets/BOMMainInfo");
        obj.set_DataObject("BOMMainInfo");
        obj.set_DataAction("savePurchaseHUBData");
        obj.addParameter("id", this._intBOMID);
        obj.addParameter("BomCode", this._BomCode);
        obj.addParameter("BomName", this._BomName);
        obj.addParameter("BomCompanyName", this._BomCompanyName);
        obj.addParameter("BomCompanyNo", this._BomCompanyNo);
        obj.addParameter("AssignUserNo", this.getFieldValue("ctlSalesperson"));
        //[001] Start Code
        obj.addParameter("aryRecipientLoginIDs", $R_FN.arrayToSingleString(this._ctlMail._aryRecipientLoginIDs));
        //[001] End Code
        obj.addParameter("Contact2No", this._intContact2No);
        obj.addDataOK(Function.createDelegate(this, this.saveConfirmComplete));
        obj.addError(Function.createDelegate(this, this.saveError));
        obj.addTimeout(Function.createDelegate(this, this.saveError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
        this.onSave();
    },
    validateForm: function () {
        this.onValidate();
        var blnOK = true;


        if (!this.checkFieldEntered("ctlSalesperson")) blnOK = false;
        //if (this.getFieldValue("ctlSalesperson") <= 0) {
        //    blnOK = false;
        //    this.showError(true, "Please select buyer");
        //}
        if (!blnOK) this.showError(true);
        if (this._blnReqInValid == true) {
            blnOK = false;
            this.showError(true, this._ValidMessage);
        }

        return blnOK;
    },

    noClicked: function() {
        this.showSaving(false);
        this.onNotConfirmed();
    },

    saveError: function(args) {
        this.showSaving(false);
        this._strErrorMessage = args._errorMessage;
        this.onSaveError();
    },

    saveConfirmComplete: function (args) {
        // ---------To validate blank column of Requirement (Suhail)--------------------------------- 
        if (args._result.ValidateMessage != '') {
            this.showError(true, args._result.ValidateMessage);
        }
        else {

            if (args._result.Result == true) {
                //$("#ctl00_cphMain_ctlBOMItems_ctlDB_imgRefresh").trigger("click");
                this._isAddButtonEnable = !args._result.Result;
                this.showSavedOK(true);
                this.onSaveComplete();
            } else {
                this._strErrorMessage = args._errorMessage;
                this.onSaveError();
            }
        }
    }

};

Rebound.GlobalTrader.Site.Controls.Forms.Confirm.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.Confirm", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
