using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site.Controls;

namespace Rebound.GlobalTrader.Site.Controls.DataListNuggets {
	public partial class BOM : Base {

		#region Overrides

		/// <summary>
		/// OnInit
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
			SetDataListNuggetType("BOM");
			base.OnInit(e);
			AddScriptReference("Controls.DataListNuggets.BOM.BOM.js");
			TitleText = Functions.GetGlobalResource("Nuggets", "BillOfMaterial");
			SetupTable();
		}

		protected override void OnLoad(EventArgs e) {
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.DataListNuggets.BOM", ctlDesignBase.ClientID);
            _scScriptControlDescriptor.AddProperty("blnPOHub", Convert.ToBoolean(SessionManager.IsPOHub));
			base.OnLoad(e);
		}
        protected override void RenderAdditionalState()
        {
            string strViewLevel = this.GetSavedStateValue("ViewLevel");
            //if (!string.IsNullOrEmpty(strViewLevel))
            //{
            //    ((Pages.Content)Page).CurrentTab = Convert.ToInt32(strViewLevel);
            //    _enmViewLevel = (ViewLevelList)Convert.ToInt32(strViewLevel);
            //    this.OnAskPageToChangeTab();
            //}
            base.RenderAdditionalState();
        }

		#endregion

		private void SetupTable() {
			_tbl.AllowSelection = true;
            _tbl.AllowMultipleSelection = true;                         
			_tbl.SortColumnDirection = Rebound.GlobalTrader.Site.Enumerations.SortColumnDirection.DESC;
            _tbl.Columns.Add(new FlexiDataColumn("Name", Unit.Empty, true));
            _tbl.Columns.Add(new FlexiDataColumn("ClientCode","RequestedBy", WidthManager.GetWidth(WidthManager.ColumnWidth.ListName), true));
            _tbl.Columns.Add(new FlexiDataColumn("AssignedUser","Quantity", WidthManager.GetWidth(WidthManager.ColumnWidth.ListName), true));
            _tbl.Columns.Add(new FlexiDataColumn("Code","Part", WidthManager.GetWidth(WidthManager.ColumnWidth.DateCode), true));
            _tbl.Columns.Add(new FlexiDataColumn("Company", WidthManager.GetWidth(WidthManager.ColumnWidth.EmailAddress), true));
            _tbl.Columns.Add(new FlexiDataColumn("Status", WidthManager.GetWidth(WidthManager.ColumnWidth.DateCode), true));
            _tbl.Columns.Add(new FlexiDataColumn("Date", WidthManager.GetWidth(WidthManager.ColumnWidth.DateCode), true));
            		           
		}
       }
}