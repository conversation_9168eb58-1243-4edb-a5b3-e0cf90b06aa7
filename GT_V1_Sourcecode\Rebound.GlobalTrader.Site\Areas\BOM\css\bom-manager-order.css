﻿
.mailGroup, .mailRecipientGroup {
    /*background-image: url('../../../../App_Themes/Original/images/mail/group_small.gif');*/
    background-position: 1px;
    background-repeat: no-repeat;
    font-size: 11px;
    font-weight: bold;
    /*padding-left: 14px;*/
}

.mailRecipient {
    padding-bottom: 2px;
}
/*.element-notes {
    text-align: left;
    float: left;
    width: 100%;
}*/

.mailRecipientGroup {
    /*background-position: 1px 1px;*/
}

.panel-title {
    font-size: 12px;
    font-weight: normal;
    padding: 2px 0px 0px 8px;
    float: left;
    line-height: 11px;
    font-family: Lucida Sans Unicode, Arial;
    color: #000000;
}

.panel-default {
    border: none !important;
}

.pq-grid-center-o {
    padding: 5px 5px 0px 5px;
}

.panel-default > .panel-heading {
    color: #333;
    background-color: #9fe994;
    border-color: #e4e5e7;
    padding: 0;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    /*background-image: url(/Areas/BOM/Images/tab_bg.jpg);*/
    background-repeat: repeat-x;
    float: left;
    width: 100%;
    padding: 0px 0px;
    background: rgb(159,232,150);
    background: linear-gradient(0deg, rgba(159,232,150,1) 0%, rgba(186,240,178,1) 100%);
    border-radius: 10px 10px 0px 0px;
}

    .panel-default > .panel-heading a {
        padding: 10px 0px 5px 15px;
        right: 5px;
    }

.panel-collapse {
    float: left !important;
}

.panel {
    position: relative;
    float: left;
    width: 100%;
    -webkit-box-shadow: none !important;
    box-shadow: none !important;
    border: none !important;
}



.panel-default > .panel-heading a:after {
    content: "";
    position: relative;
    top: 1px;
    display: inline-block;
    font-family: 'Glyphicons Halflings';
    font-style: normal;
    font-weight: 400;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    float: right;
    transition: transform .25s linear;
    -webkit-transition: -webkit-transform .25s linear;
}



.panel-default .minus_icon {
    /*content: "\2212";
                                -webkit-transform: rotate(180deg);
                                transform: rotate(180deg);*/
    background-image: url(/Areas/BOM/Images/hide.gif);
    background-repeat: no-repeat;
    position: absolute;
    right: 0px;
}

.sec_tab {
    /*content: "\2212";
                                -webkit-transform: rotate(180deg);
                                transform: rotate(180deg);*/
    background-image: url(/Areas/BOM/Images/hide.gif);
    background-repeat: no-repeat;
    position: absolute;
    right: 0px;
    padding: 7px;
}

.panel-default > .panel-heading a[aria-expanded="false"]:after {
    /*content: "\002b";
                            -webkit-transform: rotate(90deg);
                            transform: rotate(90deg);*/

    background-image: url(/Areas/BOM/Images/show.gif);
    background-repeat: no-repeat;
    position: absolute;
    padding: 7px;
    right: 0px;
    height: 30px;
}


    .panel-default > .panel-heading a[aria-expanded="false"]:after > .boxlink {
        /*content: "\002b";
                            -webkit-transform: rotate(90deg);
                            transform: rotate(90deg);*/

        visibility: hidden;
    }


/*css change starts version1.0*/
.boxlink {
    padding: 10px 5px 8px 5px;
    color: #009900;
    font-size: 11px;
    line-height: 0px;
    width: 100%;
    float: left;
}
    /*css change starts version1.0*/


    .boxlink a {
        color: #009900;
        float: left;
        padding: 6px 10px 5px 0px !important;
    }

.linkbuttoncss {
    background-position: left top;
    padding: 3px 5px 0px 21px;
    background-repeat: no-repeat;
}

.edit {
    background-image: url(../../../../App_Themes/Original/images/IconButton/nuggets/edit.gif);
}

.edit_disabled {
    background-image: url(../../../../App_Themes/Original/images/IconButton/nuggets/edit_x.gif);
    pointer-events: none;
    cursor: default;
}

.quote {
    background-image: url(../../../../App_Themes/Original/images/buttons/nuggets/quote.gif);
}

.quote_disabled {
    background-image: url(../../../../App_Themes/Original/images/buttons/nuggets/quote_x.gif);
    pointer-events: none;
    cursor: default;
}

.quoteSeletedItem {
    background-image: url(../../../../App_Themes/Original/images/buttons/nuggets/quote.gif);
}

.quoteSeletedItem_disabled {
    background-image: url(../../../../App_Themes/Original/images/buttons/nuggets/quote_x.gif);
}

.export {
    background-image: url(/Areas/BOM/Images/export.gif);
}

.purchase_hub {
    background-image: url(/Areas/BOM/Images/trusted_rfq.gif);
}

.purchase_hub_disabled {
    background-image: url(../../../../App_Themes/Original/images/buttons/sourcing/trusted_rfq_x.gif);
}

.supplier {
    background-image: url(/Areas/BOM/Images/notify.gif);
}

.close_icon {
    background-image: url(../../../../App_Themes//Original/images/IconButton/nuggets/close.gif);
}

.close_icon_disabled {
    background-image: url(../../../../App_Themes//Original/images/IconButton/nuggets/close_x.gif);
    pointer-events: none;
}

.notes {
    background-image: url(/Areas/BOM/Images/add.gif);
}

.notes_disabled {
    background-image: url(/Areas/BOM/Images/add_x.gif);
    pointer-events: none;
    cursor: default;
}

.release {
    background-image: url(/Areas/BOM/Images/release.gif);
}

.delete {
    background-image: url(../../../../App_Themes//Original/images/IconButton/nuggets/delete.gif);
}

.delete_disabled {
    background-image: url(../../../../App_Themes//Original/images/IconButton/nuggets/delete_x.gif);
    pointer-events: none;
    cursor: default;
}

.add_comm_note {
    background-image: url('../../../App_Themes/Original/images/IconButton/nuggets/add.gif');
}

.add_comm_note_disabled {
    background-image: url('../../../App_Themes/Original/images/IconButton/nuggets/add_x.gif');
    pointer-events: none;
    cursor: default;
}

.btn-communication-note {
    border: none;
    background-color: transparent;
}

    .btn-communication-note:hover, .btn-communication-note:focus {
        color: #23527c;
        text-decoration: underline;
    }


.item_details .desc {
    width: 104px !important;
}

.desc {
    width: 95px; /*changes for version.3*/
    font-size: 11px; /*changes for version.3*/
    font-weight: bold;
    color: #000;
    padding-right: 9px;
    padding-bottom: 5px;
    line-height: 12px;
}

table tr td {
    font-size: 11px; /*changes for version.3*/
}


.top_tbl tr {
    vertical-align: top;
    width: 100%;
    float: left;
}


.first_tbl tr:hover {
    background-color: #e0ffe0;
}

.item label {
    font-weight: normal;
    font-family: Tahoma !important;
    font-size: 11px !important;
    color: #000 !important;
    word-break: break-word !important;
}

table {
    padding: 6px;
}

.comm_tb thead {
    background-image: url(/Areas/BOM/Images/th_bg.gif);
    background-position: bottom;
    background-repeat: no-repeat;
    background-color: #eeeeee;
    font-weight: bold;
    border-bottom: solid 2px #e0e0e0;
    color: #999999;
}

    .comm_tb thead th {
        width: 100vh;
        border-right: solid 1px #bbbbbb;
        padding: 2px 5px 8px 2px;
    }

.sec_panel {
    width: 100%;
    background-color: #ffffff;
    background-image: url(./images/bg.jpg);
    background-repeat: repeat-x;
    background-position: bottom;
    padding: 8px 5px 4px;
    font-size: 11px;
}

.nav-tabs li {
    color: #009900;
    font-weight: bold;
    padding-left: 10px;
    padding-right: 10px;
    line-height: 20px;
}

.nav-tabs {
    border-bottom: 1px solid #ddd !important;
}

.doubleHeaderTop {
    font-size: 11px;
    border-bottom: dotted 1px #e0e0e0;
    margin-bottom: 2px;
    padding-bottom: 6px;
}

.tab-pane thead th {
    width: 100vh;
    border-right: solid 1px #bbbbbb;
    padding: 2px 5px 8px 2px;
    font-weight: bold;
    border-bottom: solid 2px #e0e0e0;
    border-right: solid 1px #bbbbbb;
    background-color: #eeeeee;
    color: #999999;
    background-image: url(./images/th_bg.png);
    background-position: bottom;
    background-repeat: repeat-x;
    font-size: 11px;
}

.bomitem_tbl {
    margin-top: 20px;
}

.nav > li > a {
    padding: 5px;
    color: #009900 !important;
    font-size: 10px;
}

.check_img {
    background-image: url(./images/ready.gif);
    background-repeat: no-repeat;
    background-position: 2px 2px;
    padding-left: 50px;
}

    .check_img input {
        margin-top: 2px;
    }


.doubleValueTop {
    font-size: 11px;
    padding-bottom: 4px;
}

.rohsCompliant {
    background-image: url(./images/compliant.gif);
}

.rohs {
    display: inline;
    background-repeat: no-repeat;
    background-position: right 2px;
    padding-right: 28px;
    font-size: 11px;
    white-space: nowrap;
}


.last_tbl tr td {
    vertical-align: top;
    padding-bottom: 8px;
    padding-right: 2px;
    border-right: solid 1px #bbbbbb;
    border-top: dotted 1px #e0e0e0;
}

/*POPupModalCSS*/
/* The Modal (background) */
.modal {
    display: none; /* Hidden by default */
    position: fixed; /* Stay in place */
    z-index: 1; /* Sit on top */
    padding-top: 100px; /* Location of the box */
    left: 0;
    top: 0;
    width: 100%; /* Full width */
    height: 100%; /* Full height */
    overflow: auto; /* Enable scroll if needed */
    background-color: rgb(0,0,0); /* Fallback color */
    background-color: rgba(0,0,0,0.4); /* Black w/ opacity */
}

/* Modal Content */
.modal-content {
    background-color: #fefefe;
    margin: auto;
    /*padding: 20px;*/ /*padding remover for popup*/
    border: 1px solid #888;
    width: 80%;
}

/* The Close Button */
.close {
    color: #aaaaaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
}

    .close:hover,
    .close:focus {
        color: #000;
        text-decoration: none;
        cursor: pointer;
    }
/*POPupModalCSS*/


.item_details {
    border-top: 4px solid #E0E0E0;
    background-image: url(../../../App_Themes/Original/images/Nuggets/standard/bg.jpg);
    background-position: bottom left;
    background-repeat: repeat-x;
    border-left: 1px solid #AAE2A0 !important;
    border-right: 1px solid #AAE2A0 !important;
    border-bottom: 1px solid #aae2a0 !important;
    padding: 5px;
}

.details tr:hover {
    background-color: #e0ffe0;
}

.icon {
    background-image: url(../../../../App_Themes/Original/images/nubs/nub.gif);
    background-position: left center;
    background-repeat: no-repeat;
    padding-left: 13px;
}

.details tr {
    font-size: 10px !important;
}

.larger_font {
    background-repeat: no-repeat;
    font-size: 20px;
    color: blue;
}

.blackbox {
    left: 52%;
    position: absolute;
    width: 20px;
    height: 17px;
    background-repeat: no-repeat;
    background-position: center center;
}

    .blackbox img {
        width: 29px;
        height: 29px;
    }

/*changes css starts here for version.3*/

.BtnSubmitEdit {
    background-image: url(../../../App_Themes/Original/images/IconButton/nuggets/save.gif);
    background-repeat: no-repeat;
    background-position: left center;
    padding-left: 23px;
    color: #009900;
    cursor: pointer;
}

.BtnCloseEdit {
    background-image: url(../../../App_Themes/Original/images/IconButton/nuggets/cancel.gif);
    background-repeat: no-repeat;
    background-position: left center;
    padding-left: 23px;
    color: #009900;
    cursor: pointer;
}

.BtnSaveSourcingEdit {
    background-image: url(../../../App_Themes/Original/images/IconButton/nuggets/save.gif);
    background-repeat: no-repeat;
    background-position: left center;
    padding-left: 23px;
    color: #009900;
    cursor: pointer;
}

.BtnCancelSourcingEdit {
    background-image: url(../../../App_Themes/Original/images/IconButton/nuggets/cancel.gif);
    background-repeat: no-repeat;
    background-position: left center;
    padding-left: 23px;
    color: #009900;
    cursor: pointer;
}

.BtnSaveQuote {
    background-image: url(../../../App_Themes/Original/images/IconButton/nuggets/save.gif);
    background-repeat: no-repeat;
    background-position: left center;
    padding-left: 23px;
    color: #009900;
    cursor: pointer;
}

.BtnCancelQuote {
    background-image: url(../../../App_Themes/Original/images/IconButton/nuggets/cancel.gif);
    background-repeat: no-repeat;
    background-position: left center;
    padding-left: 23px;
    color: #009900;
    cursor: pointer;
}

.main_header {
    background-image: url(../../../App_Themes/Original/images/Nuggets/standard/bg_top.gif);
    background-repeat: repeat-x;
    background-position: bottom;
    background-color: #b9f0b2;
    padding: 5px;
}

.footer_area {
    background-color: #b9f0b2;
    padding: 5px;
    border-radius: 0px 0px 4px 4px;
}

.main_header h6 {
    padding: 5px 0px 8px 0px;
    font-weight: bold;
}

.main_header {
    border-radius: 4px 4px 0px 0px;
}

    .main_header h6 {
        margin: 0px !important;
    }

.modal_table {
    background-color: #56954E;
}

.form_container {
    padding: 5px 5px !important;
    background-color: #56954E;
}

.modal_table tr td {
    width: 180px;
    padding-right: 10px;
    padding-bottom: 7px;
    text-align: left;
    font-weight: bold;
    font-size: 11px;
    color: #d3fFcC;
}

    .modal_table tr td input, .modal_table tr td select {
        color: #000;
        font-weight: normal;
        border-radius: 2px;
        border: none;
    }


.refresh_icon {
    background-image: url(../../../App_Themes/Original/images/DropDowns/refresh.png);
    position: absolute;
    width: 20px;
    height: 17px;
    background-repeat: no-repeat;
    background-position: center center;
}

.input_feild {
    width: 250px;
}

.header_text h4 {
    border-bottom: dotted 1px #cccccc;
    padding-bottom: 2px;
    margin-bottom: 5px;
    color: #FFFFFF;
    font-size: 11px;
    margin: 0px 0px 2px;
    text-transform: uppercase;
    padding-top: 5px;
}

.formInstructions {
    position: relative;
    margin: 0px 0px;
    color: #fff;
    margin-bottom: 10px;
}

.modal_table #textNotes {
    width: 450px !important;
    border-radius: 4px;
    color: #000;
    font-family: Tahoma;
    border: none !important;
}

/*changes css ends here for version.3*/


/*changes css starts here for version.3*/

.pq-cont-inner span a {
    color: #0000ff !important;
    background-image: url(../../../../App_Themes/Original/images/nubs/nub.gif);
    background-repeat: no-repeat;
    background-position: left 2px;
    padding-left: 13px;
    height: 12px;
}


.pq-grid-row:hover {
    background-color: #e0ffe0;
}

.pq-grid-header-table {
    background-image: url(../../../App_Themes/Original/images/FlexiDataTable/th_bg.png);
    background-position: bottom;
    background-repeat: repeat-x;
    border-bottom: solid 2px #e0e0e0;
    background-color: #eeeeee;
}

.pq-grid-row.pq-striped {
    background: #fff;
}

.pq-grid-col:hover {
    background-color: #e0e0e0;
    background-image: url(../../../App_Themes/Original/images/FlexiDataTable/th_bg.png);
    background-position: bottom;
    background-repeat: repeat-x;
}

.pq-grid-row.pq-striped:hover {
    background-color: #e0ffe0 !important;
    color: #000 !important;
}

.pq-grid-header-table span {
    font-weight: bold;
    font-size: 12px;
    color: #999999 !important;
}

.pq-grid-row {
    font-size: 11px !important;
    line-height: 1.6px;
    border-bottom: 1px dotted #bbb !important;
}

.pq-grid-cell {
    border-right: 1px solid #bbb;
}

.pq-grid-row:first-child > .pq-grid-col {
    border-right: 1px solid #bbb !important;
}
/*changes css ends here for version.3*/


/*changes css starts here for version.3*/

.ui-datepicker-calendar tr {
    float: none !important;
}

    .ui-datepicker-calendar tr td {
        float: none !important;
        padding: 0px !important;
    }

.ui-datepicker {
    width: 19em;
    padding: 0px !important;
    border: solid 2px #50508e !important;
    background-repeat: no-repeat;
    background-position: bottom center;
    font-size: 10px;
    background-image: url(../../../../App_Themes/Original/images/calendar/bg.jpg);
    background-size: cover;
}

    .ui-datepicker td span, .ui-datepicker td a {
        text-align: center;
    }

.ui-state-default {
    color: #575779 !important;
    border: none !important;
    background: none !important;
}

.ui-widget.ui-widget-content {
    background-repeat: no-repeat;
    background-position: bottom center;
    font-size: 10px;
    background-image: url(../../../../App_Themes/Original/images/calendar/bg.jpg);
    background-size: cover;
}

.ui-datepicker table {
    font-size: 10px;
    font-weight: bold;
}

.ui-datepicker th {
    padding: .3em 1.1px !important;
}

.pq-grid-bottom {
    border-bottom: solid 1px #50508e;
    background: #9fe994 !important;
}

.ui-widget-header {
    border-bottom: solid 1px #50508e;
    background: none;
    border-radius: 0px !important;
}

.ui-corner-all, .ui-corner-bottom, .ui-corner-left, .ui-corner-bl {
    border-bottom-left-radius: none !important;
}

.ui-datepicker-title {
    font-size: 12px;
}

.ui-datepicker-week-end {
    color: #8888bb;
}

/*changes css ends here for version.3*/


/*changes css starts for modal 2 here for version.3*/


.BtnBackPH {
    background-image: url(../../../App_Themes/Original/images/IconButton/nuggets/back.gif);
    background-position: left center;
    background-repeat: no-repeat;
    padding-left: 23px;
    color: #009900;
    cursor: pointer;
}

.container {
    margin-top: 10px
}

.ui-autocomplete {
    background: #fff !important;
    font-family: Tahoma !important;
    color: #006600 !important;
    max-height: 200px !important;
    overflow-y: auto;
    overflow-x: hidden;
    background-color: #eeffee !important;
}



    .ui-autocomplete .ui-menu-item {
        padding: 2px !important;
        border-bottom: dotted 1px #bbbbbb !important;
        margin-bottom: 1px !important;
    }

    .ui-autocomplete .ui-state-focus {
        padding: 2px !important;
        border-bottom: dotted 1px #bbbbbb !important;
        background-color: #eeffee !important;
        color: #006600 !important
    }

.ui-autocomplete-input {
    width: 250px;
}




.yes_icon {
    background-image: url(../../../App_Themes/Original/images/IconButton/formbody/yes.gif);
    background-position: left center;
    background-repeat: no-repeat;
    color: #fff;
    font-size: 16px;
    padding: 2px 20px 5px 21px;
    font-weight: normal;
    cursor: pointer;
}

.n_icon {
    background-image: url(../../../App_Themes/Original/images/IconButton/formbody/no.gif);
    background-position: left center;
    background-repeat: no-repeat;
    color: #fff;
    font-size: 16px;
    padding: 2px 20px 5px 21px;
    font-weight: normal;
    cursor: pointer;
}

    .yes_icon:hover, .n_icon:hover {
        color: #fff;
    }

.topTL {
    /* background-image: url(../../../App_Themes/Original/images/Nuggets/list/top.gif);*/
    margin-right: 6px;
    height: 6px;
    font-size: 2px;
}

.topTR {
    /* background-image: url(../../../App_Themes/Original/images/Nuggets/list/top.gif);*/
    margin-top: -6px;
    margin-left: 6px;
    background-position: 100% 0px;
    height: 6px;
    font-size: 2px;
}


.head_in {
    height: 20px;
    border-width: 0px;
    border-style: solid;
    border-color: #bbbbbb;
    position: relative;
    /*background-color: #ffffff;*/
    position: relative;
    /*background-image: url(../../../App_Themes/Original/images/Nuggets/standard/bg_top.gif);
                    background-repeat: repeat-x;*/
}


.panel-body {
    padding: 8px 5px 4px !important;
    background: rgb(240,239,239);
    background: linear-gradient(0deg, rgba(240,239,239,1) 0%, rgba(255,255,255,1) 100%);
    border-radius: 0px 0px 10px 10px;
    border-left: 1px solid #AAE2A0 !important;
    border-right: 1px solid #AAE2A0 !important;
    border-top: none !important;
    border-bottom: 1px solid #aae2a0 !important;
}

.boxBL {
    margin-right: 6px;
    background-image: url(../../../App_Themes/Original/images/Nuggets/allwhite/bottom.gif);
    background-position: 0 -6px;
    height: 6px;
    font-size: 2px;
}

.boxBR {
    margin-top: -6px;
    margin-left: 6px;
    background-image: url(../../../App_Themes/Original/images/Nuggets/allwhite/bottom.gif);
    background-position: 100% -6px;
    height: 6px;
    font-size: 2px;
}

#grid_md, #grid_as, #grid_md_notes {
    background-color: #fff !important;
    border-left: 1px solid #AAE2A0 !important;
    border-right: 1px solid #AAE2A0 !important;
    border-top: none !important;
    border-bottom: 1px solid #aae2a0 !important;
    border-radius: 0px 0px 5px 5px;
    background-image: none !important;
}


.pq-grid-row.pq-striped {
    background: none !important;
    font-size: 11px;
    border-bottom: 1px dotted #bbb !important;
}

.pq-grid-row {
    font-size: 11px !important;
    line-height: 1.6px;
    border-bottom: 1px dotted #bbb !important;
}

.pq-pager {
    padding: 1px 5px;
}

#lblItemManufacturer {
    color: #0000ff !important;
}


.pq-state-select.ui-state-highlight {
    background: #000066 !important;
    color: #fff !important;
}

.ui-state-highlight a, .ui-widget-content .ui-state-highlight a, .ui-widget-header .ui-state-highlight a {
    color: #66ccFF !important;
    background-image: url(../../../App_Themes/Original/images/FlexiDataTable/nub_tb_selected.gif);
}

.page_tittle {
    width: 100%;
    float: left;
    font-family: 'Lucida Sans', 'Lucida Sans Regular', 'Lucida Grande', 'Lucida Sans Unicode', Geneva, Verdana, sans-serif;
    display: contents;
}

    .page_tittle h3 {
        font-size: 22px;
        font-weight: normal;
        margin-top: 2px;
        margin-bottom: 0px;
    }

.head_content {
    background-image: url(../../../App_Themes/Original/images/Nuggets/titlebar/bg.png);
    background-repeat: repeat-x;
    background-position: bottom;
    padding: 15px 5px 5px;
    border-bottom: solid 1px #bbbbbb;
    margin-bottom: 15px;
}

.abovePageTitle {
    color: #009900;
    font-family: Lucida Sans Unicode, Arial;
    font-size: 10px;
    font-weight: normal;
    margin: 0px;
    padding: 0px;
    text-transform: uppercase;
}

.pageTitleItem {
    padding: 2px 0px;
    color: #218610;
    font-size: 11px;
}

    .pageTitleItem .itemTitle {
        font-weight: bold;
        padding-right: 3px;
    }

.noneFound {
    color: #999999;
    font-size: 11px;
    font-style: italic;
    font-weight: normal;
    padding: 10px 0px;
}

.modal_table tr .txt {
    width: 100% !important;
    color: #fff;
    border-top: dotted 1px #ccc;
    border-bottom: dotted 1px #ccc;
    padding-top: 4px;
    margin-bottom: 7px;
    padding-bottom: 4px !important;
    display: block;
}

.pq-grid-center .pq-grid-top {
    border: none !important;
    background: none !important;
    border-bottom: 1px solid #bbb !important;
}

#grid_md .pq-grid-top, #grid_md_notes .pq-grid-top {
    background: none !important;
    border-bottom: none !important;
}

#grid_as .pq-grid-top {
    background: none !important;
    border-bottom: none !important;
}

#grid_as .pq-grid-cell.supplier-and-quote span a {
    background-image: none;
    padding-left: 0px;
}
/*checkbox custom css*/

.sb-checkbox {
    margin: 10px;
}

.sb-checkbox {
    display: inline-block;
    font-size: 0;
}

.sb-checkbox__input {
    display: none;
}

    .sb-checkbox__input:checked + .sb-checkbox__label:after {
        opacity: 1;
    }

.sb-checkbox__label {
    display: inline-block;
    width: 13px;
    height: 13px;
    position: relative;
    cursor: pointer;
    background-color: #c7e7c2;
}

    .sb-checkbox__label:before {
        content: "";
        width: 13px;
        height: 13px;
        border: 1px solid;
        box-sizing: border-box;
        display: inline-block;
        position: absolute;
        top: 0;
        left: 0;
        border-radius: 2px;
    }

    .sb-checkbox__label:after {
        content: "done";
        font-family: "Material Icons";
        font-size: 11px;
        line-height: 13px;
        text-align: center;
        font-weight: bold;
        width: 13px;
        height: 13px;
        display: block;
        border-radius: 2px;
        overflow: hidden;
        text-align: center;
        opacity: 0;
        transition: 0.2s opacity;
    }

.sb-checkbox__label--green:before {
    border-color: #a1c799;
}

.sb-checkbox__label--green:after {
    background-color: #c7e7c2;
    color: #000;
}

/*checkbox custom css end here*/

.update_label {
    width: 100%;
    text-align: center;
    display: block;
    color: #999999;
    font-size: 10px;
    font-style: italic;
}

    .update_label label {
        font-weight: normal !important;
    }

.headingOneCorner {
    border-radius: 7px 7px 7px 7px !important;
}

.PrevYr, .NextYr {
    background: none !important;
    border: none !important;
    top: 4px !important;
}


.ui-datepicker th:nth-child(1) {
    position: absolute !important;
    right: 0px !important;
}

.ui-datepicker-calendar th, .ui-datepicker-calendar td {
    font-family: Tahoma !important;
}

.ui-datepicker-calendar thead {
    display: block !important;
}

.ui-datepicker-calendar tbody {
    display: block !important;
}

.ui-datepicker-week-col {
    position: absolute !important;
    right: 9px !important;
    background: none !important;
}

#ui-datepicker-div {
    width: 180px !important;
}

.ui-datepicker td span, .ui-datepicker td a {
    display: block;
    padding: 0.3em 5.2px;
    text-align: center;
    text-decoration: none;
    font-size: 10px !important;
}

.ui-datepicker th {
    color: #575779;
}

.ui-datepicker-week-col {
    padding: 4px 1px !important;
    font-size: 10px !important;
}

.ui-datepicker-calendar .ui-state-default {
    color: #575779 !important;
}

.ui-datepicker-week-end span {
    color: #8888bb !important;
}

.ui-datepicker-prev {
    left: 30px !important;
    top: 4px !important;
}

.ui-datepicker-next {
    right: 30px !important;
    top: 4px !important;
}

.ui-datepicker-month, .ui-datepicker-year {
    top: 0px !important;
    background: none !important;
    border: none;
    width: 35% !important;
    font-family: Tahoma !important;
    font-size: 12px !important;
    font-weight: 700 !important;
    color: #000000 !important;
}

.ui-datepicker-year {
    left: 0px !important;
}

.ui-datepicker-month {
    left: 14px !important;
}

    .ui-datepicker-month:focus-visible, .ui-datepicker-year:focus-visible {
        outline: none !important;
    }

.container {
}

.ui-datepicker-title select {
    position: relative !important;
}

    .ui-datepicker-title select::-ms-expand {
        display: none;
    }

.ui-datepicker-title select {
    /* for Firefox */
    -moz-appearance: none;
    /* for Safari, Chrome, Opera */
    -webkit-appearance: none;
}

.ui-datepicker-week-end .ui-state-default {
    color: #8888bb !important;
}

    .ui-datepicker-week-end .ui-state-default:hover {
        color: #575779 !important;
    }


.ui-state-active {
    border: solid 1px #50508e !important;
    background-color: #e6e6fa !important;
}

.ui-datepicker .ui-datepicker-buttonpane button {
    float: left !important;
    color: #000 !important;
    font-family: 'Lucida Sans', 'Lucida Sans Regular', 'Lucida Grande', 'Lucida Sans Unicode', Geneva, Verdana, sans-serif;
    font-size: 9px;
    font-weight: bold !important;
    opacity: 1 !important;
}

.ui-datepicker-current {
    margin-left: 30px !important;
}

.ui-datepicker .ui-datepicker-buttonpane {
    border: none !important;
    margin: 0px !important;
}

.AutoCompleteHeader {
    color: #fff !important;
    background-color: #000000;
    padding: 4px;
    font-size: 11px;
    font-weight: bold;
}

.ui-autocomplete-loading {
    background: url('../../../../App_Themes/Original/images/autosearch/loading.gif') no-repeat right center;
    background-color: white;
}

.errorSummary {
    background-color: #EE0000;
    background-image: url(../../../App_Themes/Original/images/Forms/form_error.gif);
    background-position: 6px 6px;
    background-repeat: no-repeat;
    border: 1px solid #990000;
    color: #FFFFFF;
    font-weight: bold;
    margin: 10px 5px 0px;
    padding: 5px 5px 5px 40px;
}

.text_inp textarea {
    color: #000;
    font-weight: normal;
}

.pageTitleItem .itemTitle {
    font-weight: bold;
    padding-right: 3px;
}

.ConfirmYes, .ConfirmNo {
    background-image: url(../../../App_Themes/Original/images/IconButton/filters/apply.gif);
    background-repeat: no-repeat;
    background-position: left center;
    cursor: pointer;
    color: #009900;
    padding-left: 22px;
    margin-right: 15px;
}

.ConfirmNo {
    background-image: url(../../../App_Themes/Original/images/IconButton/nuggets/cancel.gif);
}

.conf_message label {
    color: #fff !important;
    font-family: Tahoma;
    font-weight: bold;
}

input[type=number]::-webkit-outer-spin-button,
input[type=number]::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

input[type=number] {
    -moz-appearance: textfield;
}


.formContent {
    padding: 5px 5px;
}

table.formRows {
    width: 100%;
    margin: 6px 0px;
}

    table.formRows tr {
        vertical-align: top;
        height: 22px;
        border-bottom: 1px #68a960 solid;
    }

        table.formRows tr td {
            padding: 5px 0px 5px 0;
        }

.formRows label {
    padding-right: 7px;
    vertical-align: inherit;
    font-weight: bold;
    font-size: 11px;
    color: #d3fFcC;
    float: left;
}

.tblmain_area select {
    background-color: #fff !important;
}



.fieldTD input {
    display: inline-block;
    border-radius: 2px;
    border: none;
    padding: 2px;
}

.lableTD {
    width: 200px;
}


.fieldTD select {
    width: 175px !important;
    display: inline-block;
    font-size: 11px;
    border-style: none;
    padding: 2px;
    border-radius: 2px;
}

.sndcol {
    width: 146px;
    display: inline-block;
    margin-left: 19px;
    text-align: right;
    margin-right: 5px;
}

a.quickSearchReselect {
    text-decoration: none;
    color: #064500;
    font-weight: bold;
    font-size: 11px;
    margin-left: 20px;
    cursor: pointer;
}

.edit_modal {
    width: 98% !important;
}

#grid_md .ui-button {
    padding: 0px !important;
    color: #0000ff !important;
}

    #grid_md .ui-button:hover {
        text-decoration: underline !important;
    }

#grid_md .ui-button-text {
    padding: 0px !important;
}

#grid_md_notes .ui-button {
    padding: 0px !important;
    color: #0000ff !important;
}

    #grid_md_notes .ui-button:hover {
        text-decoration: underline !important;
    }

#grid_md_notes .ui-button-text {
    padding: 0px !important;
}


#bomItemDetails .desc {
    color: #000 !important;
}

#bomItemDetails .item label {
    color: #000 !important;
}

.form_details .bomitem_details {
    padding: 0px !important;
    border: none !important;
}

.form_details {
    background-image: url(../../../App_Themes/Original/images/Nuggets/standard/bg.jpg) !important;
    background-repeat: repeat-x !important;
    background-position: left bottom !important;
    background-color: #fff !important;
}

.bomitem_details .item_details {
    border: none !important;
    background: none !important;
}

#grid_up {
    background: #fff !important;
}

.pq-slider-icon {
    display: none !important;
}

body {
    font-size: 12px;
    font-family: Tahoma;
}

.container {
    width: 98%;
    padding: 0px;
}

.pq-slider-icon {
    display: none !important;
}

.bom_item_sec {
    border: none !important;
    border-radius: 0px !important;
}

/*      .pq-body-outer {
            color: #000;
            font-family: Tahoma !important;
            height: 255px !important;
            max-height: 255px;
            }

        .pq-grid-center {
            height: 225px !important;
            max-height: 225PX;
        }

        .pq-cont-right {
            max-height: 175px !important;
        }*/

/*      #grid_up .pq-cont-right {
            max-height: 305px !important;
        }

        #grid_up .pq-body-outer {
            height: 284px !important;
            max-height: 284px;
        }

        #grid_up, #grid_SB .pq-grid-top {
            border: none !important;
        }

        #grid_up .pq-grid-center {
            height: 340px !important;
            max-height: 340PX;
        }*/


#grid_md .ui-state-highlight .ui-button {
    color: #66ccFF !important;
}

#grid_md_notes .ui-state-highlight .ui-button {
    color: #66ccFF !important;
}

.add_text {
    width: 100%;
    padding: 10px;
    background-color: #C2FFB8;
}

.morelink, .lesslink,
.morelink2, .lesslink2,
.morelink3, .lesslink3 {
    cursor: pointer !important;
    font-weight: bold !important;
}

.pq-popup .pq-tabs .pq-grid {
    background: none !important;
    border: none !important;
    height: auto !important;
}

.ui-tabs-nav li:nth-child(1) {
    display: none;
}

.ui-button-text {
    color: #0000ff !important;
}

.pq-filter-menu button {
    font-family: Tahoma !important;
    font-size: 12px !important;
}

.ui-tabs-nav {
    border-bottom: solid 1px #50508e;
    background: #9fe994;
}

.ui-tabs-active {
    background: none !important;
    border: none !important;
}

.pq-table-right {
    width: 100% !important;
}

#grid_md .ui-state-highlight .ui-button-text {
    color: #66ccFF !important;
}

    #grid_md .ui-state-highlight .ui-button-text:hover {
        color: #0000ff !important
    }

#grid_md_notes .ui-state-highlight .ui-button-text {
    color: #66ccFF !important;
}

    #grid_md_notes .ui-state-highlight .ui-button-text:hover {
        color: #0000ff !important
    }

#TableErrorDivBOM {
    border: 2px solid red;
    height: 455px;
    overflow: auto;
}

#showMessage {
    display: table-column !important;
}

.BOMErrorTable {
    width: 100%;
    max-height: 350px;
    overflow: auto;
}

    .BOMErrorTable tr th {
        text-align: left;
        padding: 5px;
        color: #fff;
        background-color: #404040;
        border-right: 1px solid #fff;
    }

    .BOMErrorTable tr td {
        font-size: 11px;
        padding: 5px;
        border-right: 1px solid #fff;
        border-bottom: 1px dashed #c9c9c9;
        color: #000;
        background-color: #ffefef;
    }

        .BOMErrorTable tr td a {
            color: #001dff;
            cursor: pointer;
        }

#SendToHubHeader {
    font-weight: bold;
}

.modal_table select {
    background-color: #fff !important;
}

.ECCNIcon {
    padding-right: 30px;
    font-size: 11px;
    white-space: nowrap;
    background-image: url(../../../../App_Themes//Original/images/hazardous/ihspartstatuspng.png);
    display: inline;
    background-repeat: no-repeat;
}

.save_comm_note {
    background-image: url(../../../App_Themes/Original/images/IconButton/nuggets/save.gif);
}

.save_comm_note_disabled {
    background-image: url('../../../App_Themes/Original/images/IconButton/nuggets/save_x.gif');
    pointer-events: none;
    cursor: default;
}

input:disabled, select:disabled {
    background-color: #c6d9c4 !important;
}