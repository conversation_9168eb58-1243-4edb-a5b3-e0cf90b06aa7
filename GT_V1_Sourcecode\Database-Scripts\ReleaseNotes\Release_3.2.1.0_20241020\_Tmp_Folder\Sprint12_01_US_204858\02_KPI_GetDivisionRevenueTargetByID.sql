/* 
===========================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-204858]	    CuongDox			4-Oct-2024		Update			Update Gross profit
===========================================================================================
*/
CREATE OR ALTER PROCEDURE KPI_GetDivisionRevenueTargetByID
    @DivisionNo int,
    @yearNo INT
AS
IF OBJECT_ID('tempdb..#tmpDivRevenue') IS NOT NULL
begin
    DROP TABLE #tmpDivRevenue
end
select distinct
    TeamNo
into #tempTeambyDivision
from tblogin
where DivisionNo = @DivisionNo --and inactive=0                      

;
with CTE_DivisionSummary
as (SELECT 1 as OrderBy,
           a.DivisionId as SectionId,
           'Division Target' as SectionName,
           'TotalTarget' as SectionType,
           --Division Monthly Target                      
           isnull(b.JanTarget, 0) as JanTarget,
           isnull(b.FebTarget, 0) as FebTarget,
           isnull(b.MarchTarget, 0) as MarchTarget,
           isnull(b.AprTarget, 0) as AprTarget,
           isnull(b.MayTarget, 0) as MayTarget,
           isnull(b.JuneTarget, 0) as JuneTarget,
           isnull(b.JulyTarget, 0) as JulyTarget,
           isnull(b.AugTarget, 0) as AugTarget,
           isnull(b.SepTarget, 0) as SepTarget,
           isnull(b.OctTarget, 0) as OctTarget,
           isnull(b.NovTarget, 0) as NovTarget,
           isnull(b.DecTarget, 0) as DecTarget,
           --Division Total Target                      


           --Division Monthly Revenue                      
           (
               select isnull(sum(resale), 0)
               from tbKPIRevenueSummary
               where Divisionno = @DivisionNo
                     and invMonth = 1
                     and yearNo = @yearNo
           ) as JanRevenue,
           (
               select isnull(sum(resale), 0)
               from tbKPIRevenueSummary
               where Divisionno = @DivisionNo
                     and invMonth = 2
                     and yearNo = @yearNo
           ) as FebRevenue,
           (
               select isnull(sum(resale), 0)
               from tbKPIRevenueSummary
               where Divisionno = @DivisionNo
                     and invMonth = 3
                     and yearNo = @yearNo
           ) as MarchRevenue,
           (
               select isnull(sum(resale), 0)
               from tbKPIRevenueSummary
               where Divisionno = @DivisionNo
                     and invMonth = 4
                     and yearNo = @yearNo
           ) as AprRevenue,
           (
               select isnull(sum(resale), 0)
               from tbKPIRevenueSummary
               where Divisionno = @DivisionNo
                     and invMonth = 5
                     and yearNo = @yearNo
           ) as MayRevenue,
           (
               select isnull(sum(resale), 0)
               from tbKPIRevenueSummary
               where Divisionno = @DivisionNo
                     and invMonth = 6
                     and yearNo = @yearNo
           ) as JuneRevenue,
           (
               select isnull(sum(resale), 0)
               from tbKPIRevenueSummary
               where Divisionno = @DivisionNo
                     and invMonth = 7
                     and yearNo = @yearNo
           ) as JulyRevenue,
           (
               select isnull(sum(resale), 0)
               from tbKPIRevenueSummary
               where Divisionno = @DivisionNo
                     and invMonth = 8
                     and yearNo = @yearNo
           ) as AugRevenue,
           (
               select isnull(sum(resale), 0)
               from tbKPIRevenueSummary
               where Divisionno = @DivisionNo
                     and invMonth = 9
                     and yearNo = @yearNo
           ) as SepRevenue,
           (
               select isnull(sum(resale), 0)
               from tbKPIRevenueSummary
               where Divisionno = @DivisionNo
                     and invMonth = 10
                     and yearNo = @yearNo
           ) as OctRevenue,
           (
               select isnull(sum(resale), 0)
               from tbKPIRevenueSummary
               where Divisionno = @DivisionNo
                     and invMonth = 11
                     and yearNo = @yearNo
           ) as NovRevenue,
           (
               select isnull(sum(resale), 0)
               from tbKPIRevenueSummary
               where Divisionno = @DivisionNo
                     and invMonth = 12
                     and yearNo = @yearNo
           ) as DecRevenue,

		   -- division gross profit
		   --Division Monthly Revenue                      
           (
               select isnull(sum(GrossProfit), 0)
               from tbKPIRevenueSummary
               where Divisionno = @DivisionNo
                     and invMonth = 1
                     and yearNo = @yearNo
           ) as JanGrossProfit,
           (
               select isnull(sum(GrossProfit), 0)
               from tbKPIRevenueSummary
               where Divisionno = @DivisionNo
                     and invMonth = 2
                     and yearNo = @yearNo
           ) as FebGrossProfit,
           (
               select isnull(sum(GrossProfit), 0)
               from tbKPIRevenueSummary
               where Divisionno = @DivisionNo
                     and invMonth = 3
                     and yearNo = @yearNo
           ) as MarchGrossProfit,
           (
               select isnull(sum(GrossProfit), 0)
               from tbKPIRevenueSummary
               where Divisionno = @DivisionNo
                     and invMonth = 4
                     and yearNo = @yearNo
           ) as AprGrossProfit,
           (
               select isnull(sum(GrossProfit), 0)
               from tbKPIRevenueSummary
               where Divisionno = @DivisionNo
                     and invMonth = 5
                     and yearNo = @yearNo
           ) as MayGrossProfit,
           (
               select isnull(sum(GrossProfit), 0)
               from tbKPIRevenueSummary
               where Divisionno = @DivisionNo
                     and invMonth = 6
                     and yearNo = @yearNo
           ) as JuneGrossProfit,
           (
               select isnull(sum(GrossProfit), 0)
               from tbKPIRevenueSummary
               where Divisionno = @DivisionNo
                     and invMonth = 7
                     and yearNo = @yearNo
           ) as JulyGrossProfit,
           (
               select isnull(sum(GrossProfit), 0)
               from tbKPIRevenueSummary
               where Divisionno = @DivisionNo
                     and invMonth = 8
                     and yearNo = @yearNo
           ) as AugGrossProfit,
           (
               select isnull(sum(GrossProfit), 0)
               from tbKPIRevenueSummary
               where Divisionno = @DivisionNo
                     and invMonth = 9
                     and yearNo = @yearNo
           ) as SepGrossProfit,
           (
               select isnull(sum(GrossProfit), 0)
               from tbKPIRevenueSummary
               where Divisionno = @DivisionNo
                     and invMonth = 10
                     and yearNo = @yearNo
           ) as OctGrossProfit,
           (
               select isnull(sum(GrossProfit), 0)
               from tbKPIRevenueSummary
               where Divisionno = @DivisionNo
                     and invMonth = 11
                     and yearNo = @yearNo
           ) as NovGrossProfit,
           (
               select isnull(sum(GrossProfit), 0)
               from tbKPIRevenueSummary
               where Divisionno = @DivisionNo
                     and invMonth = 12
                     and yearNo = @yearNo
           ) as DecGrossProfit,
		   -- end division gross profit


           --Division Total Revenue                      
           cast(0 as float) as TotalTarget,
           0 as TotalRevenue,
		   0 as TotalGrossProfit,
           null as AllocatedPer,
           null as RevenuePer,
           b.DivisionTargetId as RowId
    FROM tbDivision a
        left join tbDivisionTargetFinal b
            on a.DivisionId = b.DivisionNo
    --left join DivisionRevenue drv on b.DivisionNo=drv.DivisionNo                      
    -- left join #Revenue drv on b.DivisionNo=drv.DivisionId                      
    WHERE a.DivisionId = @DivisionNo
          and isnull(b.YearNo, year(getdate())) = @yearNo --and drv.YearNo=@yearNo                      

    union
    SELECT 2 as OrderBy,
           0 as SectionId,
           'Division Remaining Target' as SectionName,
           'RemainingTarget' as SectionType,
           0 as JanTarget,
           0 as FebTarget, --                      
           0 as MarchTarget,
           0 as AprTarget,
           0 as MayTarget,
           0 as JuneTarget,
           0 as JulyTarget,
           0 as AugTarget,
           0 as SepTarget,
           0 as OctTarget,
           0 as NovTarget,
           0 as DecTarget,
                           --Division Total Target                      


                           --Division Monthly Revenue                      
           0 as JanRevenue,
           0 as FebRevenue,
           0 as MarchRevenue,
           0 as AprRevenue,
           0 as MayRevenue,
           0 as JuneRevenue,
           0 as JulyRevenue,
           0 as AugRevenue,
           0 as SepRevenue,
           0 as OctRevenue,
           0 as NovRevenue,
           0 as DecRevenue,

		   0 as JanGrossProfit,
           0 as FebGrossProfit,
           0 as MarchGrossProfit,
           0 as AprGrossProfit,
           0 as MayGrossProfit,
           0 as JuneGrossProfit,
           0 as JulyGrossProfit,
           0 as AugGrossProfit,
           0 as SepGrossProfit,
           0 as OctGrossProfit,
           0 as NovGrossProfit,
           0 as DecGrossProfit,

                           --Division Total Revenue                      
           cast(0 as float) as TotalTarget,
           0 as TotalRevenue,
		   0 as TotalGrossProfit,
           null as AllocatedPer,
           null as RevenuePer,
           0 as RowId
    union
    SELECT 3 as OrderBy,
           0 as SectionId,
           'Allocated Team Target' as SectionName,
           'AllocatedTarget' as SectionType,
           0 as JanTarget,
           0 as FebTarget,
           0 as MarchTarget,
           0 as AprTarget,
           0 as MayTarget,
           0 as JuneTarget,
           0 as JulyTarget,
           0 as AugTarget,
           0 as SepTarget,
           0 as OctTarget,
           0 as NovTarget,
           0 as DecTarget,
           --Division Total Target                      


           --Division Monthly Revenue                      
           0 as JanRevenue,
           0 as FebRevenue,
           0 as MarchRevenue,
           0 as AprRevenue,
           0 as MayRevenue,
           0 as JuneRevenue,
           0 as JulyRevenue,
           0 as AugRevenue,
           0 as SepRevenue,
           0 as OctRevenue,
           0 as NovRevenue,
           0 as DecRevenue,

		   0 as JanGrossProfit,
           0 as FebGrossProfit,
           0 as MarchGrossProfit,
           0 as AprGrossProfit,
           0 as MayGrossProfit,
           0 as JuneGrossProfit,
           0 as JulyGrossProfit,
           0 as AugGrossProfit,
           0 as SepGrossProfit,
           0 as OctGrossProfit,
           0 as NovGrossProfit,
           0 as DecGrossProfit,
           --Division Total Revenue                      
           cast(0 as float) as TotalTarget,
           0 as TotalRevenue,
		   0 as TotalGrossProfit,
           null as AllocatedPer,
           null as RevenuePer,
           0 as RowId
    union
    select 4 as OrderBy,
           tm.TeamId as SectionId,
           tm.TeamName as SectionName,
           'Team' as SectionType,
           isnull(tt.JanTarget, 0) as JanTarget,
           isnull(tt.FebTarget, 0) as FebTarget,
           isnull(tt.MarchTarget, 0) as MarchTarget,
           isnull(tt.AprTarget, 0) as AprTarget,
           isnull(tt.MayTarget, 0) as MayTarget,
           isnull(tt.JuneTarget, 0) as JuneTarget,
           isnull(tt.JulyTarget, 0) as JulyTarget,
           isnull(tt.AugTarget, 0) as AugTarget,
           isnull(tt.SepTarget, 0) as SepTarget,
           isnull(tt.OctTarget, 0) as OctTarget,
           isnull(tt.NovTarget, 0) as NovTarget,
           isnull(tt.DecTarget, 0) as DecTarget,

           --Team Monthly Revenue                       
           (
               select isnull(sum(resale), 0)
               from tbKPIRevenueSummary t
               where Divisionno = @DivisionNo
                     and t.TeamNo = tm.Teamid
                     and invMonth = 1
                     and yearNo = @yearNo
           ) as JanRevenue,
           (
               select isnull(sum(resale), 0)
               from tbKPIRevenueSummary t
               where Divisionno = @DivisionNo
                     and t.TeamNo = tm.Teamid
                     and invMonth = 2
                     and yearNo = @yearNo
           ) as FebRevenue,
           (
               select isnull(sum(resale), 0)
               from tbKPIRevenueSummary t
               where Divisionno = @DivisionNo
                     and t.TeamNo = tm.Teamid
                     and invMonth = 3
                     and yearNo = @yearNo
           ) as MarchRevenue,
           (
               select isnull(sum(resale), 0)
               from tbKPIRevenueSummary t
               where Divisionno = @DivisionNo
                     and t.TeamNo = tm.Teamid
                     and invMonth = 4
                     and yearNo = @yearNo
           ) as AprRevenue,
           (
               select isnull(sum(resale), 0)
               from tbKPIRevenueSummary t
               where Divisionno = @DivisionNo
                     and t.TeamNo = tm.Teamid
                     and invMonth = 5
                     and yearNo = @yearNo
           ) as MayRevenue,
           (
               select isnull(sum(resale), 0)
               from tbKPIRevenueSummary t
               where Divisionno = @DivisionNo
                     and t.TeamNo = tm.Teamid
                     and invMonth = 6
                     and yearNo = @yearNo
           ) as JuneRevenue,
           (
               select isnull(sum(resale), 0)
               from tbKPIRevenueSummary t
               where Divisionno = @DivisionNo
                     and t.TeamNo = tm.Teamid
                     and invMonth = 7
                     and yearNo = @yearNo
           ) as JulyRevenue,
           (
               select isnull(sum(resale), 0)
               from tbKPIRevenueSummary t
               where Divisionno = @DivisionNo
                     and t.TeamNo = tm.Teamid
                     and invMonth = 8
                     and yearNo = @yearNo
           ) as AugRevenue,
           (
               select isnull(sum(resale), 0)
               from tbKPIRevenueSummary t
               where Divisionno = @DivisionNo
                     and t.TeamNo = tm.Teamid
                     and invMonth = 9
                     and yearNo = @yearNo
           ) as SepRevenue,
           (
               select isnull(sum(resale), 0)
               from tbKPIRevenueSummary t
               where Divisionno = @DivisionNo
                     and t.TeamNo = tm.Teamid
                     and invMonth = 10
                     and yearNo = @yearNo
           ) as OctRevenue,
           (
               select isnull(sum(resale), 0)
               from tbKPIRevenueSummary t
               where Divisionno = @DivisionNo
                     and t.TeamNo = tm.Teamid
                     and invMonth = 11
                     and yearNo = @yearNo
           ) as NovRevenue,
           (
               select isnull(sum(resale), 0)
               from tbKPIRevenueSummary t
               where Divisionno = @DivisionNo
                     and t.TeamNo = tm.Teamid
                     and invMonth = 12
                     and yearNo = @yearNo
           ) as DecRevenue,

		   --Team Monthly gross profit                       
           (
               select isnull(sum(GrossProfit), 0)
               from tbKPIRevenueSummary t
               where Divisionno = @DivisionNo
                     and t.TeamNo = tm.Teamid
                     and invMonth = 1
                     and yearNo = @yearNo
           ) as JanGrossProfit,
           (
               select isnull(sum(GrossProfit), 0)
               from tbKPIRevenueSummary t
               where Divisionno = @DivisionNo
                     and t.TeamNo = tm.Teamid
                     and invMonth = 2
                     and yearNo = @yearNo
           ) as FebGrossProfit,
           (
               select isnull(sum(GrossProfit), 0)
               from tbKPIRevenueSummary t
               where Divisionno = @DivisionNo
                     and t.TeamNo = tm.Teamid
                     and invMonth = 3
                     and yearNo = @yearNo
           ) as MarchGrossProfit,
           (
               select isnull(sum(GrossProfit), 0)
               from tbKPIRevenueSummary t
               where Divisionno = @DivisionNo
                     and t.TeamNo = tm.Teamid
                     and invMonth = 4
                     and yearNo = @yearNo
           ) as AprGrossProfit,
           (
               select isnull(sum(GrossProfit), 0)
               from tbKPIRevenueSummary t
               where Divisionno = @DivisionNo
                     and t.TeamNo = tm.Teamid
                     and invMonth = 5
                     and yearNo = @yearNo
           ) as MayGrossProfit,
           (
               select isnull(sum(GrossProfit), 0)
               from tbKPIRevenueSummary t
               where Divisionno = @DivisionNo
                     and t.TeamNo = tm.Teamid
                     and invMonth = 6
                     and yearNo = @yearNo
           ) as JuneGrossProfit,
           (
               select isnull(sum(GrossProfit), 0)
               from tbKPIRevenueSummary t
               where Divisionno = @DivisionNo
                     and t.TeamNo = tm.Teamid
                     and invMonth = 7
                     and yearNo = @yearNo
           ) as JulyGrossProfit,
           (
               select isnull(sum(GrossProfit), 0)
               from tbKPIRevenueSummary t
               where Divisionno = @DivisionNo
                     and t.TeamNo = tm.Teamid
                     and invMonth = 8
                     and yearNo = @yearNo
           ) as AugGrossProfit,
           (
               select isnull(sum(GrossProfit), 0)
               from tbKPIRevenueSummary t
               where Divisionno = @DivisionNo
                     and t.TeamNo = tm.Teamid
                     and invMonth = 9
                     and yearNo = @yearNo
           ) as SepGrossProfit,
           (
               select isnull(sum(GrossProfit), 0)
               from tbKPIRevenueSummary t
               where Divisionno = @DivisionNo
                     and t.TeamNo = tm.Teamid
                     and invMonth = 10
                     and yearNo = @yearNo
           ) as OctGrossProfit,
           (
               select isnull(sum(GrossProfit), 0)
               from tbKPIRevenueSummary t
               where Divisionno = @DivisionNo
                     and t.TeamNo = tm.Teamid
                     and invMonth = 11
                     and yearNo = @yearNo
           ) as NovGrossProfit,
           (
               select isnull(sum(GrossProfit), 0)
               from tbKPIRevenueSummary t
               where Divisionno = @DivisionNo
                     and t.TeamNo = tm.Teamid
                     and invMonth = 12
                     and yearNo = @yearNo
           ) as DecGrossProfit,

           cast(0 as float) as TotalTarget,
           0 as TotalRevenue,
		   0 as TotalGrossProfit,
           null as AllocatedPer,
           null as RevenuePer,
           tm.Teamid as RowId
    from tbTeam tm
        JOIN #tempTeambyDivision tmlg
            on tmlg.TeamNo = tm.TeamId
        left join tbTeamTargetFinal tt
            on tm.TeamId = tt.TeamNo
    where --lg.DivisionNo=@DivisionNo                      
        --and isnull(tt.YearNo,year(getdate()))=@yearNo           
        TT.YearNo = @yearNo
   )
select *
from CTE_DivisionSummary
ORDER BY orderby,
         CASE
             WHEN SectionId = 0 THEN
                 0
             ELSE
                 1
         END,
         SectionName

--  tmlg.TeamNo          





