///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.CusReqSourcingResults_Delete = function (element) {
    Rebound.GlobalTrader.Site.Controls.Forms.CusReqSourcingResults_Delete.initializeBase(this, [element]);
    this._intSourcingResultID = -1;
    this._aryCurrentValues = "";
};

Rebound.GlobalTrader.Site.Controls.Forms.CusReqSourcingResults_Delete.prototype = {

    initialize: function () {
        Rebound.GlobalTrader.Site.Controls.Forms.CusReqSourcingResults_Delete.callBaseMethod(this, "initialize");
        this.addShown(Function.createDelegate(this, this.formShown));
    },

    dispose: function () {
        if (this.isDisposed) return;
        if (this._ctlConfirm) this._ctlConfirm.dispose();
        this._ctlConfirm = null;
        Rebound.GlobalTrader.Site.Controls.Forms.CusReqSourcingResults_Delete.callBaseMethod(this, "dispose");
    },

    formShown: function () {
        if (this._blnFirstTimeShown) {
            this._ctlConfirm = this.getFieldComponent("ctlConfirm");
            this._ctlConfirm.addClickYesEvent(Function.createDelegate(this, this.yesClicked));
            this._ctlConfirm.addClickNoEvent(Function.createDelegate(this, this.noClicked));
        }
    },

    yesClicked: function () {
        this.showSaving(true);
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/CusReqSourcingResults");
        obj.set_DataObject("CusReqSourcingResults");
        obj.set_DataAction("DeletePartWatchMatch");
        obj.addParameter("SourcingResultIDs", this._aryCurrentValues);
        obj.addDataOK(Function.createDelegate(this, this.saveComplete));
        obj.addError(Function.createDelegate(this, this.saveError));
        obj.addTimeout(Function.createDelegate(this, this.saveError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
        this.onSave();
    },

    noClicked: function () {
        this.showSaving(false);
        this.onNotConfirmed();
    },

    saveError: function (args) {
        this.showSaving(false);
        this._strErrorMessage = args._errorMessage;
        this.onSaveError();
    },

    saveComplete: function (args) {
        ////this.showSaving(false);
        //if (args._result.Result == true) {
        //    this.onSaveComplete();
        //} else {
        //    this._strErrorMessage = args._errorMessage;
        //    this.onSaveError();
        //}
        if (args._result.Result) {
            this.onSaveComplete();
        } else {
            this._strErrorMessage = args._errorMessage;
            this.onSaveError();
        }
    }

};

Rebound.GlobalTrader.Site.Controls.Forms.CusReqSourcingResults_Delete.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.CusReqSourcingResults_Delete", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
