<%-- Marker     changed by      date         Remarks  
     [001]      <PERSON><PERSON><PERSON>     13-Sep-2018  [REB-12820]:Provision to add Global Security on Contact Section --%>
<%@ Control Language="C#" CodeBehind="Contacts.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.DataListNuggets.Contacts" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Nugget:DesignBase ID="ctlDB" runat="server" BoxType="List">
	<Filters>
		<ReboundUI_DataListNugget:Filter ID="ctlFilter" runat="server">
			<FieldsLeft>
				<ReboundUI_FilterDataItemRow:TextBox id="ctlFirstName" runat="server" ResourceTitle="FirstName" FilterField="FirstName" />
				<ReboundUI_FilterDataItemRow:TextBox id="ctlLastName" runat="server" ResourceTitle="LastName" FilterField="LastName" />
				<ReboundUI_FilterDataItemRow:TextBox id="ctlTel" runat="server" ResourceTitle="Tel" FilterField="TelNo" />
			</FieldsLeft>
			<FieldsRight>
				<ReboundUI_FilterDataItemRow:TextBox id="ctlCompanyName" runat="server" ResourceTitle="CompanyName" FilterField="Company" />
				<ReboundUI_FilterDataItemRow:DropDown id="ctlSalesperson" runat="server" DropDownAssembly="Rebound.GlobalTrader.Site" DropDownType="Employee" ResourceTitle="Salesperson" FilterField="Salesman" />
                <%--[001]Code Start--%>
				<ReboundUI_FilterDataItemRow:DropDown id="ctlClientName" runat="server" ResourceTitle="ClientName" DropDownType="Client" DropDownAssembly="Rebound.GlobalTrader.Site" FilterField="Client"  />				
				<%--[001]Code End--%>
                <ReboundUI_FilterDataItemRow:TextBox id="ctlEmail" runat="server" ResourceTitle="Email" FilterField="Email" />
			</FieldsRight>
		</ReboundUI_DataListNugget:Filter>
	</Filters>
</ReboundUI_Nugget:DesignBase>
