Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DataListNuggets");Rebound.GlobalTrader.Site.Controls.DataListNuggets.Manufacturers=function(n){Rebound.GlobalTrader.Site.Controls.DataListNuggets.Manufacturers.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.DataListNuggets.Manufacturers.prototype={get_ibtnAdd:function(){return this._ibtnAdd},set_ibtnAdd:function(n){this._ibtnAdd!==n&&(this._ibtnAdd=n)},initialize:function(){this.addInitCompleteEvent(Function.createDelegate(this,this.initAfterBaseIsReady));this.addGetDataOKEvent(Function.createDelegate(this,this.getDataOK));this._strPathToData="controls/DataListNuggets/Manufacturers";this._strDataObject="Manufacturers";Rebound.GlobalTrader.Site.Controls.DataListNuggets.Manufacturers.callBaseMethod(this,"initialize")},showAddForm:function(){this._ibtnAdd?this.showForm(this._frmAdd,!0):this.showContent(!0)},initAfterBaseIsReady:function(){this.getData()},dispose:function(){this.isDisposed||Rebound.GlobalTrader.Site.Controls.DataListNuggets.Manufacturers.callBaseMethod(this,"dispose")},getDataOK:function(){for(var t=0,i=this._objResult.Results.length;t<i;t++){var n=this._objResult.Results[t],r=n.Inactive?"ceased":"",u=[$RGT_nubButton_Manufacturer(n.ID,n.Code,n.AdvisoryNotes),$R_FN.setCleanTextValue(n.Name),n.URL?String.format('<a href="{0}" target="_blank" class="nubButton nubButtonAlignLeft">{0}<\/a>',$R_FN.formatURL($R_FN.setCleanTextValue(n.URL))):"",$R_FN.setCleanTextValue(n.ConflictResource),$R_FN.setCleanTextValue(n.GroupName),$R_FN.setCleanTextValue(n.GroupCode),$R_FN.setCleanTextValue(n.SystemManufacturer)];this._table.addRow(u,n.ID,!1,null,r);u=null;n=null;r=null}}};Rebound.GlobalTrader.Site.Controls.DataListNuggets.Manufacturers.registerClass("Rebound.GlobalTrader.Site.Controls.DataListNuggets.Manufacturers",Rebound.GlobalTrader.Site.Controls.DataListNuggets.Base,Sys.IDisposable);