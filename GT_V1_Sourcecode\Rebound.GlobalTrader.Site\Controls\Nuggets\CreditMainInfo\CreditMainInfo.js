Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Nuggets");Rebound.GlobalTrader.Site.Controls.Nuggets.CreditMainInfo=function(n){Rebound.GlobalTrader.Site.Controls.Nuggets.CreditMainInfo.initializeBase(this,[n]);this._intCreditID=-1;this._IsPOHub=!1;this._InvoiceNo=-1;this._blnExported=!1};Rebound.GlobalTrader.Site.Controls.Nuggets.CreditMainInfo.prototype={get_intCreditID:function(){return this._intCreditID},set_intCreditID:function(n){this._intCreditID!==n&&(this._intCreditID=n)},get_ibtnEdit:function(){return this._ibtnEdit},set_ibtnEdit:function(n){this._ibtnEdit!==n&&(this._ibtnEdit=n)},get_ibtnExport:function(){return this._ibtnExport},set_ibtnExport:function(n){this._ibtnExport!==n&&(this._ibtnExport=n)},get_ibtnRelease:function(){return this._ibtnRelease},set_ibtnRelease:function(n){this._ibtnRelease!==n&&(this._ibtnRelease=n)},get_ibtnViewTree:function(){return this._ibtnViewTree},set_ibtnViewTree:function(n){this._ibtnViewTree!==n&&(this._ibtnViewTree=n)},get_IsDiffrentClient:function(){return this._IsDiffrentClient},set_IsDiffrentClient:function(n){this._IsDiffrentClient!==n&&(this._IsDiffrentClient=n)},get_IsGSAEditPermission:function(){return this._IsGSAEditPermission},set_IsGSAEditPermission:function(n){this._IsGSAEditPermission!==n&&(this._IsGSAEditPermission=n)},get_IsGSA:function(){return this._IsGSA},set_IsGSA:function(n){this._IsGSA!==n&&(this._IsGSA=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Nuggets.CreditMainInfo.callBaseMethod(this,"initialize");this.addRefreshEvent(Function.createDelegate(this,this.getData));this._IsDiffrentClient==!0?this._IsGSA==!0?this._IsGSAEditPermission==!0?$("#ctl00_cphMain_ctlLines_ctlDB_pnlLinks").show():$("#ctl00_cphMain_ctlLines_ctlDB_pnlLinks").hide():$("#ctl00_cphMain_ctlLines_ctlDB_pnlLinks").show():$("#ctl00_cphMain_ctlLines_ctlDB_pnlLinks").show();this._ibtnEdit&&($R_IBTN.addClick(this._ibtnEdit,Function.createDelegate(this,this.showEditForm)),this._frmEdit=$find(this._aryFormIDs[0]),this._frmEdit._intCreditID=this._intCreditID,this._frmEdit.addCancel(Function.createDelegate(this,this.cancelEdit)),this._frmEdit.addSaveComplete(Function.createDelegate(this,this.saveEditComplete)));(this._ibtnExport||this._ibtnRelease)&&(this._ibtnExport&&$R_IBTN.addClick(this._ibtnExport,Function.createDelegate(this,this.showExportForm)),this._ibtnRelease&&$R_IBTN.addClick(this._ibtnRelease,Function.createDelegate(this,this.showReleaseForm)),this._frmExport=$find(this._aryFormIDs[1]),this._frmExport.addCancel(Function.createDelegate(this,this.hideExportForm)),this._frmExport.addSaveComplete(Function.createDelegate(this,this.saveExportComplete)),this._frmExport.addNotConfirmed(Function.createDelegate(this,this.hideExportForm)));this._ibtnViewTree&&$R_IBTN.addClick(this._ibtnViewTree,Function.createDelegate(this,this.OpenDocTree));this._blnIsNoDataFound||this._blnHasInitialData||this.getData()},dispose:function(){this.isDisposed||(this._ibtnEdit&&$R_IBTN.clearHandlers(this._ibtnEdit),this._frmEdit&&this._frmEdit.dispose(),this._IsDiffrentClient=null,this._IsGSAEditPermission=null,this._IsGSA=null,this._intCreditID=null,this._ibtnEdit=null,this._frmEdit=null,this._ibtnExport=null,this._ibtnRelease=null,Rebound.GlobalTrader.Site.Controls.Nuggets.CreditMainInfo.callBaseMethod(this,"dispose"))},getData:function(){this.getData_Start();var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/CreditMainInfo");n.set_DataObject("CreditMainInfo");n.set_DataAction("GetData");n.addParameter("id",this._intCreditID);n.addDataOK(Function.createDelegate(this,this.getDataOK));n.addError(Function.createDelegate(this,this.getDataError));n.addTimeout(Function.createDelegate(this,this.getDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getDataOK:function(n){var t=n._result;this.setFieldValue("ctlCustomerName",$RGT_nubButton_Company(t.CustomerNo,t.isClientInvoice==!0?t.RefClientName:t.CustomerName,null,null,null,t.CustomerAdvisoryNotes));this.setFieldValue("ctlContact",$RGT_nubButton_Contact(t.ContactNo,t.isClientInvoice==!0?" .... ":t.Contact));this.setFieldValue("ctlSalesperson",$R_FN.setCleanTextValue(t.Salesman));this.setFieldValue("hidSalespersonNo",t.SalesmanNo);this.setFieldValue("ctlSalesperson2",$R_FN.setCleanTextValue(t.Salesman2));this.setFieldValue("hidSalesperson2No",t.Salesman2No);this.setFieldValue("hidSalesman2Percent",t.Salesman2Percent);this.setFieldValue("ctlRaiser",t.Raiser);this.setFieldValue("ctlDivision",t.Division);this.setFieldValue("ctlCreditDate",t.CreditDate);this.setFieldValue("ctlReferenceDate",t.ReferenceDate);this.setFieldValue("ctlInvoice",$RGT_nubButton_Invoice(t.InvoiceNo,t.Invoice));this.setFieldValue("ctlSalesOrder",$RGT_nubButton_SalesOrder(t.SalesOrderNo,t.SalesOrder));this.setFieldValue("ctlCustomerRMA",$RGT_nubButton_CRMA(t.CustomerRMANo,t.CustomerRMA));this.setFieldValue("ctlTax",t.Tax);this.setFieldValue("ctlFreight",t.Freight);this.setFieldValue("ctlShippingCost",t.ShippingCost);this.setFieldValue("ctlShipVia",t.ShipVia);this.setFieldValue("ctlIncoterm",$R_FN.setCleanTextValue(t.Incoterm));this.setFieldValue("ctlShippingAccountNo",t.ShippingAccountNo);this.setFieldValue("ctlCurrency",t.Currency);this.setFieldValue("ctlCustomerPO",t.CustomerPO);this.setFieldValue("ctlCustomerDebit",$R_FN.setCleanTextValue(t.CustomerDebit));this.setFieldValue("ctlCustomerReturn",$R_FN.setCleanTextValue(t.CustomerReturn));this.setFieldValue("ctlNotes",$R_FN.setCleanTextValue(t.Notes));this.setFieldValue("ctlInstructions",$R_FN.setCleanTextValue(t.Instructions));this.setFieldValue("hidInvoiceNumber",t.Invoice);this.setFieldValue("hidCRMANumber",t.CustomerRMA);this.setFieldValue("hidCurrencyNo",t.CurrencyNo);this.setFieldValue("hidCurrencyCode",t.CurrencyCode);this.setFieldValue("hidCreditNumber",t.CreditNumber);this.setFieldValue("hidCustomerName",$R_FN.setCleanTextValue(t.CustomerName));this.setFieldValue("hidContactName",t.Contact);this.setFieldValue("hidDivisionNo",t.DivisionNo);this.setFieldValue("hidSalesmanNo",t.SalesmanNo);this.setFieldValue("hidRaiserNo",t.RaisedBy);this.setFieldValue("hidSalesOrderNumber",t.SalesOrder);this.setFieldValue("ctlClientInvoice",$RGT_nubButton_ClientInvoice(t.InvoiceNo,t.Invoice));this.showField("ctlClientInvoice",t.isClientInvoice);this.showField("ctlInvoice",!t.isClientInvoice);this.setFieldValue("hidTaxNo",t.TaxNo);this.setFieldValue("hidShipViaNo",t.ShipViaNo);this.setFieldValue("hidFreightRaw",t.FreightVal);this.setFieldValue("hidShippingCostRaw",t.ShippingCostVal);this.setFieldValue("hidIncotermNo",t.IncotermNo);this.setFieldValue("ctlCreditNoteBankFee",t.CreditNoteBankFee);this.setFieldValue("ctlRefNo",t.RefNumber);this.showField("ctlRefNo",t.RefNumber>0);this.setFieldValue("hidIsClientInvoice",t.isClientInvoice);this.setFieldValue("hidClientInvoiceLineNo",t.ClientInvoiceLineNo);this.setFieldValue("hidHubLogin",t.HubLogin);this.setFieldValue("ctlExchangeRate",t.ExchangeRate);this.setFieldValue("ctlSalesDivision",t.invSalesDivisionName);this.setFieldValue("ctlDivisionHeader",t.invDivisionHeaderName);this.setFieldValue("hidSalesDivision",t.invSalesDivisionNo);this.setFieldValue("hidDivisionHeaderNo",t.InvDivisionHeaderNo);this._IsPOHub=t.IsPoHub;this._InvoiceNo=t.InvoiceNo;this._blnExported=t.isExport;this.enableEditButtons(!0);t.isClientInvoice&&t.HubLogin==!1&&this._ibtnEdit&&$R_IBTN.enableButton(this._ibtnEdit,!1);this.setDLUP(t.DLUP);this.getDataOK_End()},getDataError:function(n){this.showError(!0,n.get_ErrorMessage())},enableEditButtons:function(n){n&&(this._ibtnEdit&&$R_IBTN.enableButton(this._ibtnEdit,!this._blnExported),this._ibtnExport&&$R_IBTN.enableButton(this._ibtnExport,!this._blnExported),this._ibtnRelease&&$R_IBTN.enableButton(this._ibtnRelease,this._blnExported))},showExportForm:function(){this.doShowExportForm("EXPORT")},showReleaseForm:function(){this.doShowExportForm("RELEASE")},doShowExportForm:function(n){this._frmExport.changeMode(n);this._frmExport._intCreditID=this._intCreditID;this._frmExport.setFieldValue("ctlCreditNote",this.getFieldValue("hidCreditNumber"));this._frmExport.setFieldValue("ctlCustomer",this.getFieldValue("hidCustomerName"));this.showForm(this._frmExport,!0)},hideExportForm:function(){this.showForm(this._frmExport,!1)},saveExportComplete:function(){this.showForm(this._frmExport,!1);this.showContentLoading(!1);this.getData();this.showSavedOK(!0,$R_RES.ChangesSavedSuccessfully)},showEditForm:function(){this._frmEdit._intLineID=this._intLineID;this._frmEdit.setFieldValue("ctlCustomer",this.getFieldValue("hidCustomerName"));this._frmEdit.setFieldValue("ctlContact",this.getFieldValue("hidContactName"));this._frmEdit.setFieldValue("ctlDivision",this.getFieldValue("hidDivisionNo"));this._frmEdit.setFieldValue("ctlSalesman",this.getFieldValue("hidSalespersonNo"));this._frmEdit.setFieldValue("ctlSalesman2",this.getFieldValue("hidSalesperson2No"));this._frmEdit.setFieldValue("ctlRaisedBy",this.getFieldValue("hidRaiserNo"));this._frmEdit.setFieldValue("ctlShipVia",this.getFieldValue("hidShipViaNo"));this._frmEdit.setFieldValue("ctlSalesman2Percent",this.getFieldValue("hidSalesman2Percent"));this._frmEdit.setFieldValue("ctlRaisedBy",this.getFieldValue("hidRaiserNo"));this._frmEdit.setFieldValue("ctlCreditDate",this.getFieldValue("ctlCreditDate"));this._frmEdit.setFieldValue("ctlReferenceDate",this.getFieldValue("ctlReferenceDate"));this._frmEdit.setFieldValue("ctlCustomerPO",this.getFieldValue("ctlCustomerPO"));this._frmEdit.setFieldValue("ctlCustomerReturn",this.getFieldValue("ctlCustomerReturn"));this._frmEdit.setFieldValue("ctlCustomerDebit",this.getFieldValue("ctlCustomerDebit"));this._frmEdit.setFieldValue("ctlInvoice",this.getFieldValue("hidInvoiceNumber"));this._frmEdit.setFieldValue("ctlSalesOrder",this.getFieldValue("hidSalesOrderNumber"));this._frmEdit.setFieldValue("ctlCustomerRMA",this.getFieldValue("hidCRMANumber"));this._frmEdit.setFieldValue("ctlTax",this.getFieldValue("hidTaxNo"));this._frmEdit.setFieldValue("ctlShipVia",this.getFieldValue("hidShipViaNo"));this._frmEdit.setFieldValue("ctlCurrency",this.getFieldValue("hidCurrencyNo"));this._frmEdit.setFieldValue("ctlFreight",this.getFieldValue("hidFreightRaw"));this._frmEdit.setFieldValue("ctlShippingCost",this.getFieldValue("hidShippingCostRaw"));this._frmEdit.setFieldValue("ctlNotes",this.getFieldValue("ctlNotes"));this._frmEdit.setFieldValue("ctlInstructions",this.getFieldValue("ctlInstructions"));this._frmEdit.setFieldValue("ctlIncoterm",this.getFieldValue("hidIncotermNo"));this._frmEdit.setFieldValue("ctlCreditNoteBankFee",this.getFieldValue("ctlCreditNoteBankFee"));$R_FN.setInnerHTML(this._frmEdit._lblFreight_Currency,this.getFieldValue("hidCurrencyCode"));this._frmEdit.showField("ctlRaisedByLbl",!this._IsPOHub);this._frmEdit.showField("ctlRaisedBy",this._IsPOHub);this._frmEdit._IsPOHub=this._IsPOHub;this._frmEdit._hidRaisedByNo=this.getFieldValue("hidRaiserNo");this._frmEdit.setFieldValue("ctlRaisedBy",this.getFieldValue("hidRaiserNo"));this._frmEdit.setFieldValue("ctlRaisedByLbl",this.getFieldValue("ctlRaiser"));this._frmEdit.showField("ctlShipViaLbl",!this._IsPOHub);this._frmEdit.showField("ctlShipVia",this._IsPOHub);this._frmEdit._hidShipViaNo=this.getFieldValue("hidShipViaNo");this._frmEdit.setFieldValue("ctlShipViaLbl",this.getFieldValue("ctlShipVia"));this._frmEdit.showField("ctlSalesman2",this._IsPOHub);this._frmEdit.showField("ctlSalesman2Percent",this._IsPOHub);this._frmEdit.setFieldValue("ctlExchangeRate",this.getFieldValue("ctlExchangeRate"));this._frmEdit.setFieldValue("ctlDivisionHeader",this.getFieldValue("hidDivisionHeaderNo"));this.showForm(this._frmEdit,!0)},hideEditForm:function(){this.showForm(this._frmEdit,!1)},cancelEdit:function(){this.hideEditForm()},saveEditComplete:function(){this.hideEditForm();this.showSavedOK(!0,$R_RES.ChangesSavedSuccessfully);this.getData()},OpenDocTree:function(){$R_FN.openDocumentTree(this._intCreditID,"CRD",this.getFieldValue("hidCreditNumber"))}};Rebound.GlobalTrader.Site.Controls.Nuggets.CreditMainInfo.registerClass("Rebound.GlobalTrader.Site.Controls.Nuggets.CreditMainInfo",Rebound.GlobalTrader.Site.Controls.Nuggets.Base);