///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything test
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Nuggets");

Rebound.GlobalTrader.Site.Controls.Nuggets.BOMAdd = function(element) { 
	Rebound.GlobalTrader.Site.Controls.Nuggets.BOMAdd.initializeBase(this, [element]);
	this._strGeneratedID = "";
	this._BomCompanyNo = 0;

};

Rebound.GlobalTrader.Site.Controls.Nuggets.BOMAdd.prototype = {

	get_ibtnAdd: function() { return this._ibtnAdd; }, 	set_ibtnAdd: function(v) { if (this._ibtnAdd !== v)  this._ibtnAdd = v; }, 
	
	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.Nuggets.BOMAdd.callBaseMethod(this, "initialize");	
		this._strGeneratedID = 'bom-' + + Date.now() + '-' + Math.floor(Math.random() * 1000);
		this.showRefresh(false);
		this.showLoading(false);
		this.showContentLoading(false);
		this.addRefreshEvent(Function.createDelegate(this, this.showAddForm));
		if (this._ibtnAdd) {
			$R_IBTN.addClick(this._ibtnAdd, Function.createDelegate(this, this.showAddForm));
			this._frmAdd = $find(this._aryFormIDs[0]);
			this._frmAdd.addSaveComplete(Function.createDelegate(this, this.saveAddComplete));
			this._frmAdd.addSaveError(Function.createDelegate(this, this.saveAddError));
			$addHandler($get('ctl00_cphMain_ctlAdd_ctlDB_ctl14_ctlAdd_ctlDB_ibtnEditPPV_hyp'), 'click', Function.createDelegate(this, this.showPPVForm));
			$addHandler($get('ctl00_cphMain_ctlAdd_ctlDB_ctl14_ctlAdd_ctlDB_ibtnViewPPV_hyp'), 'click', Function.createDelegate(this, this.showViewPPVForm));
			$addHandler($get('ctl00_cphMain_ctlAdd_ctlDB_ctl14_ctlAdd_ctlDB_ibtnDeletePPV_hyp'), 'click', Function.createDelegate(this, this.showDeletePPVForm));

			this._frmAdd._strGeneratedID = this._strGeneratedID;
		}
		this._frmEditPPV = $find(this._aryFormIDs[1]);
		this._frmEditPPV._strGeneratedID = this._strGeneratedID;
		this._frmEditPPV._IsFromBOMAdd = true;
		this._frmEditPPV.addCancel(Function.createDelegate(this, this.cancelEditPPV));
		this._frmEditPPV.addSaveComplete(Function.createDelegate(this, this.saveEditPPVComplete));

		this._frmDeletePPV = $find(this._aryFormIDs[2]);
		this._frmDeletePPV._strGeneratedID = this._strGeneratedID;
		this._frmDeletePPV._IsFromBOMAdd = true;
		this._frmDeletePPV.addNotConfirmed(Function.createDelegate(this, this.cancelDeletePPV));
		this._frmDeletePPV.addSaveComplete(Function.createDelegate(this, this.saveDeletePPVtComplete));

	},

	dispose: function() { 
		if (this.isDisposed) return;
		if (this._ibtnAdd) $R_IBTN.clearHandlers(this._ibtnAdd);
		if (this._frmAdd) this._frmAdd.dispose();
		this._frmAdd = null;
		this._ibtnAdd = null;
		this._strGeneratedID = null;
		Rebound.GlobalTrader.Site.Controls.Nuggets.BOMAdd.callBaseMethod(this, "dispose");
	},
	
	showAddForm: function() {
		if (this._ibtnAdd) {
			this.showForm(this._frmAdd, true);
		} else {
			this.showContent(true);
		}
	},
	showDeletePPVForm: function () {

		this.showForm(this._frmDeletePPV, true);
	},
	showViewPPVForm: function () {
		this._frmEditPPV._IsView = true;
		this.showForm(this._frmEditPPV, true);

	},
	showPPVForm: function () {
		this._frmEditPPV._IsView = false;
		this.showForm(this._frmEditPPV, true);

	},
	saveAddComplete: function() {
		location.href = $RGT_gotoURL_BOM(this._frmAdd._intNewID);
	},
	saveEditPPVComplete: function () {
		this.showForm(this._frmAdd, true);
		this.showSavedOK(true, $R_RES.ChangesSavedSuccessfully);
	},
	cancelEditPPV: function () {
		this.showForm(this._frmAdd, true);
		$("#ctl00_cphMain_ctlAdd_ctlDB_ctl20_ibtnSave").show();
		$("#ctl00_cphMain_ctlAdd_ctlDB_ctl19_ibtnSave").show();
	},
	cancelDeletePPV: function () {
		this.showForm(this._frmAdd, true);

	},
	saveDeletePPVtComplete: function () {
		this.showForm(this._frmAdd, true);
		this.showSavedOK(true, $R_RES.ChangesSavedSuccessfully);
},
	saveAddError: function() {
		this.showError(true, this._frmAdd._strErrorMessage);
		$R_FN.showElement(this._pnlLinks, true);
	}
		
};

Rebound.GlobalTrader.Site.Controls.Nuggets.BOMAdd.registerClass("Rebound.GlobalTrader.Site.Controls.Nuggets.BOMAdd", Rebound.GlobalTrader.Site.Controls.Nuggets.Base);
