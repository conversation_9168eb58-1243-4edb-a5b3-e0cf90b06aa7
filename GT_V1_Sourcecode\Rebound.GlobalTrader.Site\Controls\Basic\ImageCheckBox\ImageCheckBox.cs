using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using System.Collections;
using System.Threading;

namespace Rebound.GlobalTrader.Site.Controls {
	[DefaultProperty("")]
	[ToolboxData("<{0}:ImageCheckBox runat=server></{0}:ImageCheckBox>")]
	public class ImageCheckBox : Panel, IScriptControl, INamingContainer {

		#region Locals

		protected ScriptManager _sm;
		protected Image img;

		#endregion

		#region Properties

		/// <summary>
		/// Is it checked?
		/// </summary>
		private bool _blnChecked;
		public bool Checked {
			get { return _blnChecked; }
			set { _blnChecked = value; }
		}

		/// <summary>
		/// Is it Enabled?
		/// </summary>
		private bool _blnEnabled;
		public new bool Enabled {
			get { return _blnEnabled; }
			set { _blnEnabled = value; }
		}

		#endregion

		#region Constructors

		public ImageCheckBox() { }

		#endregion

		#region Overrides

		/// <summary>
		/// OnInit
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
			((Pages.Base)Page).AddCSSFile("ImageCheckBox.css");
			CssClass = "languageSelect";
			base.OnInit(e);
		}

		protected override void CreateChildControls() {
			this.CssClass = "imageCheckBox";
			if (!Enabled) this.CssClass += "Disabled";
			img = ControlBuilders.CreateImage("", "~/images/x.gif");
			img.CssClass = (Checked) ? "on" : "off";
			Controls.Add(img);
			base.CreateChildControls();
		}

		/// <summary>
		/// OnPreRender
		/// </summary>
		/// <param name="e"></param>
		protected override void OnPreRender(EventArgs e) {

			//add script reference
			if (!this.DesignMode) {
				_sm = ScriptManager.GetCurrent(Page);
				if (_sm == null) throw new HttpException("A ScriptManager control must exist on the current page.");
				_sm.RegisterScriptControl(this);
			}
			base.OnPreRender(e);
		}

		/// <summary>
		/// Render
		/// </summary>
		/// <param name="writer"></param>
		protected override void Render(HtmlTextWriter writer) {
			if (!this.DesignMode) _sm.RegisterScriptDescriptors(this);
			base.Render(writer);
		}

		#endregion

		#region IScriptControl Members

		protected virtual IEnumerable<ScriptReference> GetScriptReferences() {
			bool blnDebug = false;
#if (DEBUG)
			blnDebug = true;
#endif
			return new ScriptReference[] { Functions.GetScriptReference(blnDebug, "Rebound.GlobalTrader.Site", "Controls.Basic.ImageCheckBox.ImageCheckBox", true) };
		}

		protected virtual IEnumerable<ScriptDescriptor> GetScriptDescriptors() {
			ScriptControlDescriptor descriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.ImageCheckBox", this.ClientID);
			descriptor.AddProperty("blnChecked", Checked);
			descriptor.AddProperty("blnEnabled", Enabled);
			descriptor.AddElementProperty("img", img.ClientID);
			return new ScriptDescriptor[] { descriptor };
		}

		IEnumerable<ScriptReference> IScriptControl.GetScriptReferences() {
			return GetScriptReferences();
		}

		IEnumerable<ScriptDescriptor> IScriptControl.GetScriptDescriptors() {
			return GetScriptDescriptors();
		}

		#endregion
	}
}