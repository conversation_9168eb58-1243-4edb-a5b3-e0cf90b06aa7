///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.BOMImportSourcingResult = function (element) {
    Rebound.GlobalTrader.Site.Controls.Forms.BOMImportSourcingResult.initializeBase(this, [element]);
    this._intBOMID = -1;
};

Rebound.GlobalTrader.Site.Controls.Forms.BOMImportSourcingResult.prototype = {

    initialize: function () {
        Rebound.GlobalTrader.Site.Controls.Forms.BOMImportSourcingResult.callBaseMethod(this, "initialize");
        this.addShown(Function.createDelegate(this, this.formShown));
        this.addCancel(Function.createDelegate(this, this.cancelClicked));
    },

    formShown: function () {
             if (this._blnFirstTimeShown) {
            $('#lblTotalRecordCount').text('');
            $('#lblTotalRecordMatchCount').text('');
            $('#lblTotalRecordNotMatchCount').text('');
            $('#lblTotalRecordCount2').text('');
            $('#lblTotalRecordMatchCount2').text('');
            $('#lblTotalRecordNotMatchCount2').text('');
            GetDataGridDataLeft();
            GetDataGridDataRight();
            var tableleft = $('#tableBomNotMacthDataLeft').dataTable();
            var dataleft = tableleft.fnGetData();
            // Check the data array length.
            if (dataleft.length == 0) {
            //alert("Table is empty");
            GetDistoryLeftTable();
             } else {
                //alert("Table has " + data.length + " rows.")
            }

            var tableright = $('#tableBomNotMacthDataRight').dataTable();
            var dataright = tableright.fnGetData();
                // Check the data array length.
            if (dataright.length == 0) {
                //alert("Table is empty");
                  GetDistoryRightTable();
            } else {
                //alert("Table has " + data.length + " rows.")
            }
           
            this.addCancel(Function.createDelegate(this, this.cancelClicked));
        }
            else {
               
                 $('#lblTotalRecordCount').text('');
                 $('#lblTotalRecordMatchCount').text('');
                 $('#lblTotalRecordNotMatchCount').text('');
                 $('#lblTotalRecordCount2').text('');
                 $('#lblTotalRecordMatchCount2').text('');
                 $('#lblTotalRecordNotMatchCount2').text('');
                 GetDataGridDataLeft();
                 GetDataGridDataRight();
                 var tableleft = $('#tableBomNotMacthDataLeft').dataTable();
                 var dataleft = tableleft.fnGetData();
                 // Check the data array length.
                 if (dataleft.length == 0) {
                     //alert("Table is empty");
                     GetDistoryLeftTable();
                 } else {
                     //alert("Table has " + data.length + " rows.")
                 }

                 var tableright = $('#tableBomNotMacthDataRight').dataTable();
                 var dataright = tableright.fnGetData();
                 // Check the data array length.
                 if (dataright.length == 0) {
                     //alert("Table is empty");
                     GetDistoryRightTable();
                 } else {
                     //alert("Table has " + data.length + " rows.")
                 }
        }
    },

    dispose: function () {
        if (this.isDisposed) return;
        Rebound.GlobalTrader.Site.Controls.Forms.BOMImportSourcingResult.callBaseMethod(this, "dispose");
    },
    OfferimpotExcelData: function (originalFilename, generatedFilename, iscolumnheaderchk, Client_type) {
        $('#divLoader').show();
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj._intTimeoutMilliseconds = 200000;
        obj.set_PathToData("controls/Nuggets/BOMItems/BOMImportSourcingResult");
        obj.set_DataObject("BOMImportSourcingResult");
        obj.set_DataAction("ImportExcelData");
        obj.addParameter("originalFilename", originalFilename);
        obj.addParameter("generatedFilename", generatedFilename);
        obj.addParameter("BOMId", this._intBOMID);
        obj.addParameter("SelectedClientType", Client_type);
        obj.addParameter("ColumnHeader", iscolumnheaderchk);
        obj.addDataOK(Function.createDelegate(this, this.importExcelDataOK));
        obj.addError(Function.createDelegate(this, this.importExcelDataError));
        obj.addTimeout(Function.createDelegate(this, this.importExcelDataError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },
    importExcelDataOK: function (args) {
        flogId = args._result.Result;
        BomGridData.call(this);
        $('#divLoader').hide();

    },
    importExcelDataError: function (args) {
        alert(args._errorMessage.split('<br/>')[0]);
        $('#divLoader').hide();
    },
    cancelClicked: function (args) {
        $("#divepo1").show();
        $("#divepo3").show();
        $("#hypShowHideepo1").val("-");
        $("#hypShowHideepo3").val("-");
        RowCount1 = $('#tableBomNotMacthDataLeft tr').length;

        if (RowCount1 != 0) {
            GetDistoryLeftTable();
        }
        RowCount2 = $('#tableBomNotMacthDataRight tr').length;

        if (RowCount2 != 0) {
            GetDistoryRightTable();
        }

    }

};

Rebound.GlobalTrader.Site.Controls.Forms.BOMImportSourcingResult.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.BOMImportSourcingResult", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);

