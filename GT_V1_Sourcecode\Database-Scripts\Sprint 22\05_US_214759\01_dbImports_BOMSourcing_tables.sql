﻿/*   
===========================================================================================  
TASK			UPDATED BY      DATE			ACTION		DESCRIPTION  
[US-214760]		An.TranTan		09-Jan-2025		Create		Create new tables for import BOM Sourcing Results
===========================================================================================  
*/
--store temp column header from import file
IF OBJECT_ID(N'BorisGlobalTraderImports.dbo.tbBOMSourcing_tempHeading', N'U') IS NOT NULL
BEGIN
	DROP TABLE BorisGlobalTraderImports.dbo.tbBOMSourcing_tempHeading
END
CREATE TABLE BorisGlobalTraderImports.dbo.tbBOMSourcing_tempHeading(
	BOMSourcingHeadingId INT IDENTITY(1,1) NOT NULL PRIMARY KEY,
	Column1 NVARCHAR(200) NULL,
	Column2 NVARCHAR(200) NULL,
	Column3 NVARCHAR(200) NULL,
	Column4 NVARCHAR(200) NULL,
	Column5 NVARCHAR(200) NULL,
	Column6 NVARCHAR(200) NULL,
	Column7 NVARCHAR(200) NULL,
	Column8 NVARCHAR(200) NULL,
	Column9 NVARCHAR(200) NULL,
	Column10 NVARCHAR(200) NULL,
	Column11 NVARCHAR(200) NULL,
	Column12 NVARCHAR(200) NULL,
	Column13 NVARCHAR(200) NULL,
	Column14 NVARCHAR(200) NULL,
	Column15 NVARCHAR(200) NULL,
	Column16 NVARCHAR(200) NULL,
	Column17 NVARCHAR(200) NULL,
	Column18 NVARCHAR(200) NULL,
	Column19 NVARCHAR(200) NULL,
	Column20 NVARCHAR(200) NULL,
	Column21 NVARCHAR(200) NULL,
	Column22 NVARCHAR(200) NULL,
	Column23 NVARCHAR(200) NULL,
	Column24 NVARCHAR(200) NULL,
	Column25 NVARCHAR(200) NULL,
	Column26 NVARCHAR(200) NULL,
	Column27 NVARCHAR(200) NULL,
	Column28 NVARCHAR(200) NULL,
	Column29 NVARCHAR(200) NULL,
	Column30 NVARCHAR(200) NULL,
	Column31 NVARCHAR(200) NULL,
	[CreatedBy] INT NOT NULL,
	[CreatedDate] DATETIME NOT NULL DEFAULT GETDATE()
)
GO
--store temp column data from import file
IF OBJECT_ID(N'BorisGlobalTraderImports.dbo.tbBOMSourcing_tempData', N'U') IS NOT NULL
BEGIN
	DROP TABLE BorisGlobalTraderImports.dbo.tbBOMSourcing_tempData
END
CREATE TABLE BorisGlobalTraderImports.dbo.tbBOMSourcing_tempData(
	BOMSourcingDataId INT IDENTITY(1,1) NOT NULL PRIMARY KEY,
	CustomerRequirementNo INT NOT NULL,
	Column1 NVARCHAR(MAX) NULL,
	Column2 NVARCHAR(MAX) NULL,
	Column3 NVARCHAR(MAX) NULL,
	Column4 NVARCHAR(MAX) NULL,
	Column5 NVARCHAR(MAX) NULL,
	Column6 NVARCHAR(MAX) NULL,
	Column7 NVARCHAR(MAX) NULL,
	Column8 NVARCHAR(MAX) NULL,
	Column9 NVARCHAR(MAX) NULL,
	Column10 NVARCHAR(MAX) NULL,
	Column11 NVARCHAR(MAX) NULL,
	Column12 NVARCHAR(MAX) NULL,
	Column13 NVARCHAR(MAX) NULL,
	Column14 NVARCHAR(MAX) NULL,
	Column15 NVARCHAR(MAX) NULL,
	Column16 NVARCHAR(MAX) NULL,
	Column17 NVARCHAR(MAX) NULL,
	Column18 NVARCHAR(MAX) NULL,
	Column19 NVARCHAR(MAX) NULL,
	Column20 NVARCHAR(MAX) NULL,
	Column21 NVARCHAR(MAX) NULL,
	Column22 NVARCHAR(MAX) NULL,
	Column23 NVARCHAR(MAX) NULL,
	Column24 NVARCHAR(MAX) NULL,
	Column25 NVARCHAR(MAX) NULL,
	Column26 NVARCHAR(MAX) NULL,
	Column27 NVARCHAR(MAX) NULL,
	Column28 NVARCHAR(MAX) NULL,
	Column29 NVARCHAR(MAX) NULL,
	Column30 NVARCHAR(MAX) NULL,
	Column31 NVARCHAR(MAX) NULL,
	LineNumber INT NULL,
	[OriginalFilename] [nvarchar](200) NULL,
	[GeneratedFilename] [nvarchar](200) NULL,
	[CreatedBy] INT NOT NULL,
	[CreatedDate] DATETIME NOT NULL DEFAULT GETDATE()
)
GO
--store column mapping option for specific supplier and import user
IF OBJECT_ID(N'BorisGlobalTraderImports.dbo.tbBOMSourcing_ColumnMapping', N'U') IS NOT NULL
BEGIN
	DROP TABLE BorisGlobalTraderImports.dbo.tbBOMSourcing_ColumnMapping
END
CREATE TABLE BorisGlobalTraderImports.dbo.tbBOMSourcing_ColumnMapping(
	Manufacturer NVARCHAR(10) NULL,
	SupplierPart NVARCHAR(10) NULL,
	OfferedQuantity NVARCHAR(10) NULL,
	SupplierCost NVARCHAR(10) NULL,
	SPQ NVARCHAR(10) NULL,
	MOQ NVARCHAR(10) NULL,
	SupplierName NVARCHAR(10) NULL,
	MSL NVARCHAR(10) NULL,
	Notes NVARCHAR(10) NULL,
	DateCode NVARCHAR(10) NULL,
	QtyInStock NVARCHAR(10) NULL,
	OfferStatus NVARCHAR(10) NULL,
	BuyPrice NVARCHAR(10) NULL,
	SellPrice NVARCHAR(10) NULL,
	ShippingCost NVARCHAR(10) NULL,
	Package NVARCHAR(10) NULL,
	ROHS NVARCHAR(10) NULL,
	Currency NVARCHAR(10) NULL,
	FactorySealed NVARCHAR(10) NULL,
	Region NVARCHAR(10) NULL,
	LeadTime NVARCHAR(10) NULL,
	LastTimeBuy NVARCHAR(10) NULL,
	DeliveryDate NVARCHAR(10) NULL,
	CustomerRefNo NVARCHAR(10) NULL,
	[CreatedBy] INT NOT NULL,
	[DLUP] DATETIME NOT NULL DEFAULT GETDATE()
)

--INSERT default mapping for dropdown
INSERT INTO BorisGlobalTraderImports.dbo.tbBOMSourcing_ColumnMapping
(
	[Manufacturer]
    ,[SupplierPart]
    ,[OfferedQuantity]
    ,[SupplierCost]
    ,[SPQ]
    ,[MOQ]
    ,[SupplierName]
    ,[MSL]
    ,[Notes]
    ,[DateCode]
    ,[QtyInStock]
    ,[OfferStatus]
    ,[BuyPrice]
    ,[SellPrice]
    ,[ShippingCost]
    ,[Package]
    ,[ROHS]
    ,[Currency]
    ,[FactorySealed]
    ,[Region]
    ,[LeadTime]
    ,[LastTimeBuy]
    ,[DeliveryDate]
    ,[CustomerRefNo]
	,[CreatedBy]
	,DLUP
)VALUES(
	'Column13'
	,'Column10'
	,'Column16'
	,'Column11'
	,'Column18'
	,'Column21'
	,'Column9'
	,'Column31'
	,'Column30'
	,'Column14'
	,'Column20'
	,'Column17'
	,'Column24'
	,'Column25'
	,'Column26'
	,'Column15'
	,'Column12'
	,'Column23'
	,'Column19'
	,'Column28'
	,'Column27'
	,'Column22'
	,'Column29'
	,'Column8'
	,1
	,GETDATE()
)
GO
--store data to be imported
IF OBJECT_ID(N'BorisGlobalTraderImports.dbo.tbBOMSourcing_ToBeImported', N'U') IS NOT NULL
BEGIN
	DROP TABLE BorisGlobalTraderImports.dbo.tbBOMSourcing_ToBeImported
END
CREATE TABLE BorisGlobalTraderImports.dbo.tbBOMSourcing_ToBeImported(
	BOMSourcingImportId INT IDENTITY(1,1) NOT NULL PRIMARY KEY,
	CustomerRequirementNo INT NOT NULL,
	SupplierNo INT NULL,
	SupplierName NVARCHAR(256),
	ManufacturerNo INT NULL,
	ManufacturerName NVARCHAR(256),
	SupplierPart NVARCHAR(30) NULL,
	Quantity INT NOT NULL,
	SupplierCost FLOAT,
	SPQ NVARCHAR(10) NULL,
	MOQ NVARCHAR(100) NULL,
	MSLLevelNo INT NULL,
	MSL NVARCHAR(100) NULL,
	Notes NVARCHAR(500) NULL,
	DateCode NVARCHAR(100) NULL,
	QtyInStock INT NULL,
	OfferStatusNo INT NULL,
	BuyPrice FLOAT NULL,
	SellPrice FLOAT NULL,
	ShippingCost FLOAT NULL,
	PackageNo INT NULL,
	ROHSStatus NVARCHAR(10) NULL,
	CurrencyNo INT NULL,
	FactorySealed NVARCHAR(10) NULL,
	RegionNo INT NULL,
	LeadTime NVARCHAR(10) NULL,
	LastTimeBuy NVARCHAR(10) NULL,
	DeliveryDate DATETIME NULL,
	CustomerRefNo NVARCHAR(200) NULL,
	OriginalFilename NVARCHAR(200) NULL,
	GeneratedFilename NVARCHAR(200) NULL,
	CreatedBy INT NOT NULL,
	DLUP DATETIME NOT NULL DEFAULT GETDATE()
)
GO

--create user-defined table types to store temp data
IF OBJECT_ID('dbo.usp_saveBOMSourcing_tempData', 'P') IS NOT NULL
  DROP PROCEDURE dbo.usp_saveBOMSourcing_tempData
GO

IF EXISTS (SELECT 1 FROM sys.types WHERE name = 'UploadedBOMSourcingResult' AND is_table_type = 1)
BEGIN
    DROP TYPE [dbo].[UploadedBOMSourcingResult]
END
GO

CREATE TYPE [dbo].[UploadedBOMSourcingResult] AS TABLE(
	[Column1] [nvarchar](max) NULL DEFAULT (NULL),
	[Column2] [nvarchar](max) NULL DEFAULT (NULL),
	[Column3] [nvarchar](max) NULL DEFAULT (NULL),
	[Column4] [nvarchar](max) NULL DEFAULT (NULL),
	[Column5] [nvarchar](max) NULL DEFAULT (NULL),
	[Column6] [nvarchar](max) NULL DEFAULT (NULL),
	[Column7] [nvarchar](max) NULL DEFAULT (NULL),
	[Column8] [nvarchar](max) NULL DEFAULT (NULL),
	[Column9] [nvarchar](max) NULL DEFAULT (NULL),
	[Column10] [nvarchar](max) NULL DEFAULT (NULL),
	[Column11] [nvarchar](max) NULL DEFAULT (NULL),
	[Column12] [nvarchar](max) NULL DEFAULT (NULL),
	[Column13] [nvarchar](max) NULL DEFAULT (NULL),
	[Column14] [nvarchar](max) NULL DEFAULT (NULL),
	[Column15] [nvarchar](max) NULL DEFAULT (NULL),
	[Column16] [nvarchar](max) NULL DEFAULT (NULL),
	[Column17] [nvarchar](max) NULL DEFAULT (NULL),
	[Column18] [nvarchar](max) NULL DEFAULT (NULL),
	[Column19] [nvarchar](max) NULL DEFAULT (NULL),
	[Column20] [nvarchar](max) NULL DEFAULT (NULL),
	[Column21] [nvarchar](max) NULL DEFAULT (NULL),
	[Column22] [nvarchar](max) NULL DEFAULT (NULL),
	[Column23] [nvarchar](max) NULL DEFAULT (NULL),
	[Column24] [nvarchar](max) NULL DEFAULT (NULL),
	[Column25] [nvarchar](max) NULL DEFAULT (NULL),
	[Column26] [nvarchar](max) NULL DEFAULT (NULL),
	[Column27] [nvarchar](max) NULL DEFAULT (NULL),
	[Column28] [nvarchar](max) NULL DEFAULT (NULL),
	[Column29] [nvarchar](max) NULL DEFAULT (NULL),
	[Column30] [nvarchar](max) NULL DEFAULT (NULL),
	[Column31] [nvarchar](max) NULL DEFAULT (NULL),
	[LineNumber] [int] NULL
)
GO

IF COL_LENGTH('dbo.tbCustomerRequirement', 'CustomerRefNo') IS NULL
BEGIN
   ALTER TABLE dbo.tbCustomerRequirement ADD CustomerRefNo NVARCHAR(200) NULL
END
GO

