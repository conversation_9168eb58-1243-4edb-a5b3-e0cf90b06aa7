///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 09.09.2010:
// - hide Buyer filter on "My" tab
//
// RP 06.01.2010:
// - fully dispose everything
//
// RP 23.12.2009:
// - render state on server
//
// RP 16.10.2009:
// - Changes due to changes in base class
/* Marker     changed by      date         Remarks  
   [001]      A<PERSON><PERSON>     27-Sep-2018   REB-13083 Change request PO - delivery status
*/
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DataListNuggets");

Rebound.GlobalTrader.Site.Controls.DataListNuggets.InternalPurchaseOrders = function(element) {
    Rebound.GlobalTrader.Site.Controls.DataListNuggets.InternalPurchaseOrders.initializeBase(this, [element]);
};



Rebound.GlobalTrader.Site.Controls.DataListNuggets.InternalPurchaseOrders.prototype = {
    get_intBuyerID: function () { return this._intBuyerID; }, set_intBuyerID: function (value) { if (this._intBuyerID !== value) this._intBuyerID = value; },
    get_blnPOHub: function () { return this._blnPOHub; }, set_blnPOHub: function (v) { if (this._blnPOHub !== v) this._blnPOHub = v; },
    initialize: function() {
        this.addSetupDataCallEvent(Function.createDelegate(this, this.setupDataCall));
        this.addGetDataOKEvent(Function.createDelegate(this, this.getDataOK));
        this.addInitCompleteEvent(Function.createDelegate(this, this.initAfterBaseIsReady));
        this._strPathToData = "controls/DataListNuggets/InternalPurchaseOrders";
        this._strDataObject = "InternalPurchaseOrders";
        Rebound.GlobalTrader.Site.Controls.DataListNuggets.InternalPurchaseOrders.callBaseMethod(this, "initialize");
    },

    initAfterBaseIsReady: function() {
        this.addPageTabChangedEvent(Function.createDelegate(this, this.pageTabChanged));
        this.applyBuyerFilter();
        this.updateFilterVisibility();
        this.getData();
    },

    dispose: function() {
        if (this.isDisposed) return;
        this._intBuyerID = null;
        Rebound.GlobalTrader.Site.Controls.DataListNuggets.InternalPurchaseOrders.callBaseMethod(this, "dispose");
    },

    pageTabChanged: function() {

        this._table._intCurrentPage = 1;
        this._enmViewLevel = this._intCurrentTab;
        this.updateFilterVisibility();
        this.getData();
    },

    setupDataCall: function() {
        this._objData.addParameter("ViewLevel", this._enmViewLevel);
    },

    getDataOK: function (args) {
        
        for (var i = 0, l = this._objResult.Results.length; i < l; i++) {
            var row = this._objResult.Results[i];
            var aryData = [
           
				$RGT_nubButton_InternalPurchaseOrder(row.ID, row.No)
				, $R_FN.writeDoubleCellValue($R_FN.writePartNo(row.Part, row.ROHS), $RGT_nubButton_Manufacturer(row.MfrNo, row.Mfr))
				, row.Price
				, $R_FN.writeDoubleCellValue(row.Quantity, row.QuantityOS)
				,row.isPoHUB==true?$R_FN.setCleanTextValue(row.ClientName): $RGT_nubButton_Company(row.CMNo, row.CM)
				, $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.DeliveryDate), row.RequireASAP)
                 , $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.IPOStatus), $R_FN.setCellValueWithBackground(row.DeliveryStatus, row.RowCSS))
				, $R_FN.setCleanTextValue(row.Status)
               
                
			];
            this._table.addRow(aryData, row.ID, false);
            aryData = null; row = null;
        }
    },

    updateFilterVisibility: function() {
        this.getFilterField("ctlBuyerName").show(this._enmViewLevel != 0);
        this.getFilterField("ctlClientName").show(this._blnPOHub);
    },

    applyBuyerFilter: function() {
        if ((this._intBuyerID) && this._intBuyerID > 0)
            this.getFilterField("ctlBuyerName").setValue(this._intBuyerID);
    }

};
Rebound.GlobalTrader.Site.Controls.DataListNuggets.InternalPurchaseOrders.registerClass("Rebound.GlobalTrader.Site.Controls.DataListNuggets.InternalPurchaseOrders", Rebound.GlobalTrader.Site.Controls.DataListNuggets.Base, Sys.IDisposable);
