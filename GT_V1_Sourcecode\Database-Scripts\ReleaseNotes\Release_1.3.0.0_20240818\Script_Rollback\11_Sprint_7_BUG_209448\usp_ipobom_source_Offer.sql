﻿SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

  ----QuantityQuoted MPNQuoted ManufacturerName DateCode         
  --PackageType ProductType SPQ MOQ Leadtime(wks) Rohs (Y/N)         
  --TQSA LTB FactorySealed MSL UnitPrice(EUR) Notes                     
CREATE OR ALTER PROCEDURE [dbo].[usp_ipobom_source_Offer]   --  101,'testpart100'
--********************************************************************************************                              
--* RP 09.03.2011:                              
--* - add recompile option                              
--*                              
--* RP 25.05.2010:                              
--* - remove UNIONS, process Clients in code                              
--*                              
--* SK 17.02.2010:                              
--* - adjust display of external client data                               
--*                              
--* SK 20.01.2010:                              
--* - add ClientId to parameters and predicate: if equal display data as now, if not show                                
--*   C<PERSON><PERSON><PERSON> as customer  - with no hyperlink - and do not show any price                                
--*                              
--* RP 18.01.2010:                              
--* - coalesce SupplierName and ManufacturerName                              
--*                              
--* RP 05.06.2009:                              
--* - search with LIKE                              
--*                              
--* SK 01.06.2009:                              
--* - add order by clause                              
--*Marker     Changed by      Date         Remarks                                
--*[001]      Vinay           16/10/2012   Display supplier type in stock grid                              
--********************************************************************************************                                  
    @ClientId INT                              
  , @PartSearch NVARCHAR(50)                              
  , @Index int =1                        
  , @StartDate datetime = NULL                          
  , @FinishDate datetime = NULL         
  , @IsPoHUB bit=NULL                       
WITH RECOMPILE AS                               
BEGIN                           
     --DECLARE VARIABLE                      
     DECLARE @Month int                        
     DECLARE @FROMDATE DATETIME                        
     DECLARE @ENDDATE DATETIME                        
     DECLARE @OutPutDate DATETIME       
                          
       DECLARE @FinishDateVW DATETIME                       
     DECLARE @FROMDATEVW DATETIME                              
     DECLARE @ENDDATEVW DATETIME                     
     SET @Month=6                        
     /*                      
        When we get index 1 then we find the maximum date from matching record                      
        and decsrease no of month for the start date.                      
     */    
	  declare @HUBName nvarchar(300)     
	  select top 1 @HUBName = CompanyName from tbCompany where ClientNo = @ClientId and IsPOHub=1       
	                    
     IF @Index=1                        
     BEGIN        
         
      SELECT @FinishDateVW=MAX(ISNULL(OfferStatusChangeDate, OriginalEntryDate)) FROM tbSourcingResult tsr                
      WHERE              
       tsr.SourcingTable in('PQ','EXPH','OFPH')  AND                            
       tsr.FullPart LIKE @PartSearch                                
      SET @FROMDATEVW=dbo.ufn_get_date_from_datetime(DATEADD(month,-@Month,@FinishDateVW))                              
      SET @ENDDATEVW =dbo.ufn_get_date_from_datetime(@FinishDateVW)         
              
                         
      SELECT @FinishDate=MAX(ISNULL(OfferStatusChangeDate, OriginalEntryDate)) FROM [BorisGlobalTraderImports].dbo.tbOffer o          
        JOIN    tbClient cl ON o.ClientNo = cl.ClientId                     
      WHERE   ((o.ClientNo = @ClientId)                          
             OR (o.ClientNo <> @ClientId            
                -- AND cl.OwnDataVisibleToOthers = 1))                     
				 AND (case when o.ClientNo=114 then cast(1 as bit) else  cl.OwnDataVisibleToOthers end) = 1 )) 
      AND FullPart LIKE @PartSearch                          
      SET @FROMDATE=dbo.ufn_get_date_from_datetime(DATEADD(month,-@Month,@FinishDate))                        
      SET @ENDDATE =dbo.ufn_get_date_from_datetime(@FinishDate)                        
     END                        
    ELSE                       
     BEGIN                        
       SET @FROMDATE=dbo.ufn_get_date_from_datetime(@StartDate)                        
       SET @ENDDATE =dbo.ufn_get_date_from_datetime(@FinishDate)       
           
         SET @FROMDATEVW=dbo.ufn_get_date_from_datetime(@StartDate)                        
       SET @ENDDATEVW =dbo.ufn_get_date_from_datetime(@FinishDate)       
                            
     END                        
      --SET THE OUTPUT DATE                      
      SET @OutPutDate=DATEADD(month,-@Month,@FinishDate)    
	                 
    -- If Index value equal to 3 then more than one year data will be pick from archive database.
	 IF @Index=3    
	   
		  BEGIN

		  ;WITH    cteSearch                                                
              AS(                   
    SELECT  o.OfferId                              
          , o.FullPart COLLATE DATABASE_DEFAULT  as FullPart                           
          , o.Part                              
          , o.ManufacturerNo                              
          , o.DateCode                              
          , o.ProductNo                              
      , o.PackageNo                              
          , o.Quantity                              
          ,case when o.ClientNo=114 then 0 else o.Price end AS Price --o.Price                          
          , o.OriginalEntryDate                           
          , o.Salesman                         
          , o.SupplierNo                              
          , o.CurrencyNo                              
          , o.ROHS                              
          , o.UpdatedBy                              
          , o.DLUP                              
          , o.OfferStatusNo                              
          , ISNULL(o.OfferStatusChangeDate, o.OriginalEntryDate) AS OfferStatusChangeDate                              
          , o.OfferStatusChangeLoginNo                              
          , m.ManufacturerCode COLLATE DATABASE_DEFAULT as ManufacturerCode                            
          , p.ProductName   COLLATE DATABASE_DEFAULT as ProductName                          
          , c.CurrencyCode  COLLATE DATABASE_DEFAULT  as CurrencyCode                          
          , c.CurrencyDescription  COLLATE DATABASE_DEFAULT as CurrencyDescription                           
          --, ISNULL(s.CompanyName, o.SupplierName) COLLATE DATABASE_DEFAULT AS SupplierName                             
		  , case when o.ClientNo=114 then @HUBName else  ISNULL(s.CompanyName, o.SupplierName) COLLATE DATABASE_DEFAULT end AS SupplierName  
          , ISNULL(m.ManufacturerName, o.ManufacturerName) COLLATE DATABASE_DEFAULT AS ManufacturerName                              
          , s.EMail COLLATE DATABASE_DEFAULT AS SupplierEmail                             
          , l.EmployeeName COLLATE DATABASE_DEFAULT AS SalesmanName                              
          , l2.EmployeeName COLLATE DATABASE_DEFAULT AS OfferStatusChangeEmployeeName                              
          , g.PackageName COLLATE DATABASE_DEFAULT as PackageName                            
          , o.Notes   COLLATE DATABASE_DEFAULT as Notes                          
          , o.ClientNo          
      , cl.ClientId                            
          , cl.ClientName  COLLATE DATABASE_DEFAULT  as ClientName                          
          , cl.OwnDataVisibleToOthers AS ClientDataVisibleToOthers                              
            --[001] code start                              
          , isnull(cotype.Name,'') COLLATE DATABASE_DEFAULT as SupplierType  
          --[001] code end  
      , cl.ClientCode COLLATE DATABASE_DEFAULT  as ClientCode       
           , o.SPQ   COLLATE DATABASE_DEFAULT  as SPQ    
          , o.LeadTime COLLATE DATABASE_DEFAULT  as LeadTime      
          , o.ROHSStatus  COLLATE DATABASE_DEFAULT as ROHSStatus     
          , o.FactorySealed  COLLATE DATABASE_DEFAULT  as FactorySealed     
         -- , o.MSL           
          , ml.MSLLevel COLLATE DATABASE_DEFAULT  as MSL
    , o.IPOBOMNo         
    ,o.SupplierTotalQSA  COLLATE DATABASE_DEFAULT   as SupplierTotalQSA    
    ,o.SupplierLTB  COLLATE DATABASE_DEFAULT  as SupplierLTB      
    ,o.SupplierMOQ  COLLATE DATABASE_DEFAULT  as SupplierMOQ  
    ,ishub=0                                
    FROM    [BorisGlobalTraderArchive].dbo.tbOffer_arc o                              
    LEFT JOIN dbo.tbManufacturer m ON o.ManufacturerNo = m.ManufacturerId                              
    LEFT JOIN dbo.tbProduct p ON o.ProductNo = p.ProductId                              
    LEFT JOIN dbo.tbCurrency c ON o.CurrencyNo = c.CurrencyId                              
    LEFT JOIN dbo.tbCompany s ON o.SupplierNo = s.CompanyId                              
  LEFT JOIN dbo.tbLogin l ON o.Salesman = l.LoginId                              
    LEFT JOIN dbo.tbLogin l2 ON o.OfferStatusChangeLoginNo = l2.LoginId                              
    LEFT JOIN dbo.tbPackage g ON o.PackageNo = g.PackageId                              
    JOIN    tbClient cl ON o.ClientNo = cl.ClientId                             
      --[001] code start                              
    LEFT JOIN dbo.tbCompanyType cotype ON s.TypeNo=cotype.CompanyTypeId     
    left join tbMSLLevel ml on o.MSLLevelNo = ml.MSLLevelId                                                  
    --[001] code end                               
    WHERE   ((o.ClientNo = @ClientId)                          
             OR (o.ClientNo <> @ClientId                          
                -- AND cl.OwnDataVisibleToOthers = 1))         
					AND (case when o.ClientNo=114 then cast(1 as bit) else  cl.OwnDataVisibleToOthers end) = 1 )) 
                  AND ((@IsPoHUB is NULL)       
                OR (not @IsPoHUB is NULL AND isnull(o.IsPoHub,0)= @IsPoHUB ))          
     AND o.FullPart LIKE @PartSearch                  
     AND (dbo.ufn_get_date_from_datetime(ISNULL(o.OfferStatusChangeDate, o.OriginalEntryDate)) between @FROMDATE   AND  @ENDDATE)                                
   -- ORDER BY ISNULL(o.OfferStatusChangeDate, o.OriginalEntryDate) DESC                              
            union all        
                      
           select OfferId                              
          , FullPart  COLLATE DATABASE_DEFAULT as FullPart                          
          , Part     COLLATE DATABASE_DEFAULT as Part                        
          , ManufacturerNo                              
          , DateCode  COLLATE DATABASE_DEFAULT as DateCode                           
          , ProductNo                              
      , PackageNo                              
          , Quantity                              
            ,case when ClientNo=114 then 0 else Price end AS Price --Price                                 
          , OriginalEntryDate                           
          , Salesman                              
          , SupplierNo                              
          , CurrencyNo                              
          , ROHS                              
          , UpdatedBy      
          , DLUP                              
          , OfferStatusNo                              
          , OfferStatusChangeDate                              
          , OfferStatusChangeLoginNo                              
          , ManufacturerCode                              
 , ProductName                              
          , CurrencyCode                              
          , CurrencyDescription                              
         -- , SupplierName COLLATE DATABASE_DEFAULT  as SupplierName            
		  , case when ClientNo=114 then @HUBName else  SupplierName COLLATE DATABASE_DEFAULT end AS SupplierName  
          , ManufacturerName  COLLATE DATABASE_DEFAULT as ManufacturerName                           
          , SupplierEmail   COLLATE DATABASE_DEFAULT as SupplierEmail                          
          , SalesmanName COLLATE DATABASE_DEFAULT as SalesmanName                           
          , OfferStatusChangeEmployeeName COLLATE DATABASE_DEFAULT as OfferStatusChangeEmployeeName                            
          , PackageName COLLATE DATABASE_DEFAULT as PackageName                            
          , Notes COLLATE DATABASE_DEFAULT  as Notes                           
          , ClientNo                              
          , ClientId                            
          , ClientName  COLLATE DATABASE_DEFAULT  as ClientName                          
          , ClientDataVisibleToOthers                              
            --[001] code start                          
          , SupplierType  COLLATE DATABASE_DEFAULT  as SupplierType                          
          --[001] code end            
          , ClientCode   COLLATE DATABASE_DEFAULT  as ClientCode     
           , SPQ    COLLATE DATABASE_DEFAULT as SPQ   
          , LeadTime  COLLATE DATABASE_DEFAULT  as LeadTime    
          , ROHSStatus  COLLATE DATABASE_DEFAULT as ROHSStatus     
          , FactorySealed     COLLATE DATABASE_DEFAULT as FactorySealed  
          , MSL     COLLATE DATABASE_DEFAULT  as MSL     
    , IPOBOMNo         
    ,SupplierTotalQSA     
    ,SupplierLTB   COLLATE DATABASE_DEFAULT as  SupplierLTB      
    ,SupplierMOQ  COLLATE DATABASE_DEFAULT as SupplierMOQ  
    ,ishub from [vwSourcingDataHubRFQ]           
                      
               where      
  ((ClientNo = @ClientId)                            
             OR (ClientNo <> @ClientId                            
         -- AND ClientDataVisibleToOthers = 1))            
		 	AND (case when ClientNo=114 then cast(1 as bit) else  ClientDataVisibleToOthers end) = 1 )) 
                 
     AND FullPart LIKE @PartSearch                        
     AND (dbo.ufn_get_date_from_datetime(ISNULL(OfferStatusChangeDate, OriginalEntryDate)) between @FROMDATEVW   AND  @ENDDATEVW)                      
  -- ORDER BY ISNULL(OfferStatusChangeDate, OriginalEntryDate) DESC                     
      --SELECT THE OUT DATE         
        )        
      select * ,dbo.ufn_GetSupplierMessage(SupplierNo) as  SupplierMessage from cteSearch  ORDER BY ISNULL(OfferStatusChangeDate, OriginalEntryDate) DESC                        
      --SELECT THE OUT DATE    
	  END
	  ELSE
	  BEGIN
	    ;WITH    cteSearch                                                
              AS(                   
    SELECT  o.OfferId                              
          , o.FullPart COLLATE DATABASE_DEFAULT  as FullPart                           
          , o.Part                              
          , o.ManufacturerNo                              
          , o.DateCode                              
          , o.ProductNo                              
      , o.PackageNo                              
          , o.Quantity                              
          --, o.Price 
		   ,case when o.ClientNo=114 then 0 else o.Price end AS Price                               
, o.OriginalEntryDate            
          , o.Salesman                         
          , o.SupplierNo                              
          , o.CurrencyNo                              
          , o.ROHS                              
          , o.UpdatedBy                              
          , o.DLUP                              
          , o.OfferStatusNo                              
          , ISNULL(o.OfferStatusChangeDate, o.OriginalEntryDate) AS OfferStatusChangeDate                              
          , o.OfferStatusChangeLoginNo                              
          , m.ManufacturerCode COLLATE DATABASE_DEFAULT as ManufacturerCode                            
          , p.ProductName   COLLATE DATABASE_DEFAULT as ProductName                          
          , c.CurrencyCode  COLLATE DATABASE_DEFAULT  as CurrencyCode                          
          , c.CurrencyDescription  COLLATE DATABASE_DEFAULT as CurrencyDescription                           
         -- , ISNULL(s.CompanyName, o.SupplierName) COLLATE DATABASE_DEFAULT AS SupplierName      
		 , case when o.ClientNo=114 then @HUBName else  ISNULL(s.CompanyName, o.SupplierName) COLLATE DATABASE_DEFAULT end AS SupplierName                         
          , ISNULL(m.ManufacturerName, o.ManufacturerName) COLLATE DATABASE_DEFAULT AS ManufacturerName                              
          , s.EMail COLLATE DATABASE_DEFAULT AS SupplierEmail                             
          , l.EmployeeName COLLATE DATABASE_DEFAULT AS SalesmanName                              
          , l2.EmployeeName COLLATE DATABASE_DEFAULT AS OfferStatusChangeEmployeeName                              
          , g.PackageName COLLATE DATABASE_DEFAULT as PackageName                            
          , o.Notes   COLLATE DATABASE_DEFAULT as Notes                          
          , o.ClientNo                              
          , cl.ClientId                            
          , cl.ClientName  COLLATE DATABASE_DEFAULT  as ClientName                          
          , cl.OwnDataVisibleToOthers AS ClientDataVisibleToOthers                              
            --[001] code start                              
          , isnull(cotype.Name,'') COLLATE DATABASE_DEFAULT as SupplierType                              
          --[001] code end            
      , cl.ClientCode COLLATE DATABASE_DEFAULT  as ClientCode       
           , o.SPQ   COLLATE DATABASE_DEFAULT  as SPQ    
          , o.LeadTime COLLATE DATABASE_DEFAULT  as LeadTime      
          , o.ROHSStatus  COLLATE DATABASE_DEFAULT as ROHSStatus     
          , o.FactorySealed  COLLATE DATABASE_DEFAULT  as FactorySealed     
         -- , o.MSL           
          , ml.MSLLevel COLLATE DATABASE_DEFAULT  as MSL
    , o.IPOBOMNo         
    ,o.SupplierTotalQSA  COLLATE DATABASE_DEFAULT   as SupplierTotalQSA    
    ,o.SupplierLTB  COLLATE DATABASE_DEFAULT  as SupplierLTB      
    ,o.SupplierMOQ  COLLATE DATABASE_DEFAULT  as SupplierMOQ  
    ,ishub=0                                
    FROM    [BorisGlobalTraderImports].dbo.tbOffer o                              
    LEFT JOIN dbo.tbManufacturer m ON o.ManufacturerNo = m.ManufacturerId                              
    LEFT JOIN dbo.tbProduct p ON o.ProductNo = p.ProductId                              
    LEFT JOIN dbo.tbCurrency c ON o.CurrencyNo = c.CurrencyId                              
    LEFT JOIN dbo.tbCompany s ON o.SupplierNo = s.CompanyId                              
  LEFT JOIN dbo.tbLogin l ON o.Salesman = l.LoginId                              
    LEFT JOIN dbo.tbLogin l2 ON o.OfferStatusChangeLoginNo = l2.LoginId                              
    LEFT JOIN dbo.tbPackage g ON o.PackageNo = g.PackageId                              
    JOIN    tbClient cl ON o.ClientNo = cl.ClientId                             
      --[001] code start                              
    LEFT JOIN dbo.tbCompanyType cotype ON s.TypeNo=cotype.CompanyTypeId     
    left join tbMSLLevel ml on o.MSLLevelNo = ml.MSLLevelId                                                  
    --[001] code end                               
    WHERE   ((o.ClientNo = @ClientId)                          
             OR (o.ClientNo <> @ClientId                          
                 --AND cl.OwnDataVisibleToOthers = 1))         
				 AND (case when o.ClientNo=114 then cast(1 as bit) else  cl.OwnDataVisibleToOthers end) = 1 )) 
                  AND ((@IsPoHUB is NULL)       
                OR (not @IsPoHUB is NULL AND isnull(o.IsPoHub,0)= @IsPoHUB ))          
     AND o.FullPart LIKE @PartSearch                  
     AND (dbo.ufn_get_date_from_datetime(ISNULL(o.OfferStatusChangeDate, o.OriginalEntryDate)) between @FROMDATE   AND  @ENDDATE)                  
   -- ORDER BY ISNULL(o.OfferStatusChangeDate, o.OriginalEntryDate) DESC                              
            union all        
                      
           select OfferId  
          , FullPart  COLLATE DATABASE_DEFAULT as FullPart                          
          , Part     COLLATE DATABASE_DEFAULT as Part                        
          , ManufacturerNo                              
          , DateCode  COLLATE DATABASE_DEFAULT as DateCode                           
          , ProductNo                              
      , PackageNo                              
          , Quantity                              
          --, Price                              
		    ,case when ClientNo=114 then 0 else Price end AS Price
          , OriginalEntryDate                           
          , Salesman                              
          , SupplierNo                              
          , CurrencyNo                              
          , ROHS                              
          , UpdatedBy                              
          , DLUP                              
          , OfferStatusNo                              
          , OfferStatusChangeDate                              
          , OfferStatusChangeLoginNo                              
          , ManufacturerCode                              
          , ProductName                              
          , CurrencyCode                              
          , CurrencyDescription                              
         -- , SupplierName COLLATE DATABASE_DEFAULT  as SupplierName                           
		 , case when ClientNo=114 then @HUBName else  SupplierName COLLATE DATABASE_DEFAULT end AS SupplierName 
          , ManufacturerName  COLLATE DATABASE_DEFAULT as ManufacturerName                           
          , SupplierEmail   COLLATE DATABASE_DEFAULT as SupplierEmail                          
          , SalesmanName COLLATE DATABASE_DEFAULT as SalesmanName                           
          , OfferStatusChangeEmployeeName COLLATE DATABASE_DEFAULT as OfferStatusChangeEmployeeName                            
          , PackageName COLLATE DATABASE_DEFAULT as PackageName                            
          , Notes COLLATE DATABASE_DEFAULT  as Notes                           
          , ClientNo                              
          , ClientId                            
          , ClientName  COLLATE DATABASE_DEFAULT  as ClientName                          
          , ClientDataVisibleToOthers                              
            --[001] code start                          
          , SupplierType  COLLATE DATABASE_DEFAULT  as SupplierType                          
          --[001] code end            
          , ClientCode   COLLATE DATABASE_DEFAULT  as ClientCode     
           , SPQ    COLLATE DATABASE_DEFAULT as SPQ   
          , LeadTime  COLLATE DATABASE_DEFAULT  as LeadTime    
          , ROHSStatus  COLLATE DATABASE_DEFAULT as ROHSStatus     
          , FactorySealed     COLLATE DATABASE_DEFAULT as FactorySealed  
          , MSL     COLLATE DATABASE_DEFAULT  as MSL     
    , IPOBOMNo         
    ,SupplierTotalQSA     
    ,SupplierLTB   COLLATE DATABASE_DEFAULT as  SupplierLTB      
    ,SupplierMOQ  COLLATE DATABASE_DEFAULT as SupplierMOQ  
    ,ishub from [vwSourcingDataHubRFQ]           
                      
               where      
  ((ClientNo = @ClientId)                            
             OR (ClientNo <> @ClientId                            
         -- AND ClientDataVisibleToOthers = 1))            
		 AND (case when ClientNo=114 then cast(1 as bit) else  ClientDataVisibleToOthers end) = 1 )) 
                 
     AND FullPart LIKE @PartSearch                        
     AND (dbo.ufn_get_date_from_datetime(ISNULL(OfferStatusChangeDate, OriginalEntryDate)) between @FROMDATEVW   AND  @ENDDATEVW)                      
  -- ORDER BY ISNULL(OfferStatusChangeDate, OriginalEntryDate) DESC                     
      --SELECT THE OUT DATE         
        )        
      select * ,dbo.ufn_GetSupplierMessage(SupplierNo) as  SupplierMessage from cteSearch  ORDER BY ISNULL(OfferStatusChangeDate, OriginalEntryDate) DESC                
      --SELECT THE OUT DATE                             
      --SELECT THE OUT DATE    
	  END                   
    SELECT @OutPutDate AS OutPutDate        
END





