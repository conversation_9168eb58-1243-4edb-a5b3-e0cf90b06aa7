﻿
GO

CREATE OR ALTER PROCEDURE [dbo].[usp_select_CustomerRequirementBOM]                 
@CustomerRequirementId int                     
--*===========================================================================================  
--* Action: Altered  By: Abhinav <PERSON>  Date:01-09-2023  Comment: For RP-2228  
--*===========================================================================================                     
AS   
BEGIN                   
   Declare @SourcingResultId int=0          
     declare @PurchaseRequestId nvarchar(1000),            
   @PurchaseRequestNumber nvarchar(1000)          
   --select @PurchaseRequestId=pr.PurchaseRequestId ,@PurchaseRequestNumber= pr.PurchaseRequestNumber  from tbPurchaseRequest pr          
   --uncomment bellow line to use comma seprated      
   select @PurchaseRequestId=COALESCE(@PurchaseRequestId + ',','') + COALESCE(Cast( pr.PurchaseRequestId As Varchar),''),      
   @PurchaseRequestNumber=COALESCE(@PurchaseRequestNumber + ',','') + COALESCE(Cast( pr.PurchaseRequestNumber As Varchar),'')  from tbPurchaseRequest pr       
   join tbPurchaseRequestLine prl on prl.PurchaseRequestNo = pr.PurchaseRequestId where prl.CustomerRequirementNo=@CustomerRequirementId            
   -- sart prev Query             
--   SELECT @SourcingResultId = count(1) FROM tbCustomerRequirement cr                               
--JOIN tbBOM bm ON bm.BOMId = cr.BOMNo                               
--JOIN tbSourcingResult sr on cr.CustomerRequirementId = sr.CustomerRequirementNo                                
--Join tbQuoteLine ql on sr.SourcingResultId=ql.SourcingResultNo                            
--WHERE cr.CustomerRequirementId = @CustomerRequirementId               
-- End Prv Query              
            
-- SELECT  @SourcingResultId = count(1) FROM tbCustomerRequirement cr                               
--JOIN tbBOM bm ON bm.BOMId = cr.BOMNo                               
--JOIN tbSourcingResult sr on cr.CustomerRequirementId = sr.CustomerRequirementNo                                
--Join tbQuoteLine ql on sr.SourcingResultId=ql.SourcingResultNo               
--join tbSalesOrderLine sol on ql.QuoteLineId=sol.QuoteLineNo              
--join tbSalesOrder so on sol.SalesOrderNo=so.SalesOrderId            
--join  tbInternalPurchaseOrder ipo on so.SalesOrderId=ipo.SalesOrderNo                    
--WHERE cr.CustomerRequirementId = @CustomerRequirementId            
             
  SELECT  @SourcingResultId = count(1) FROM tbCustomerRequirement cr                                 
JOIN tbSourcingResult sr on cr.CustomerRequirementId = sr.CustomerRequirementNo           
join  tbInternalPurchaseOrderLine ipo on sr.SourcingResultId=ipo.SourcingResultNo                    
WHERE cr.CustomerRequirementId = @CustomerRequirementId          
              
SELECT * ,                  
( select ServiceName from [tbRequirementDropDownData] where id=cr.QuoteValidityRequired) as QuoteValidityText,                  
( select ServiceName from [tbRequirementDropDownData] where id=cr.ReqType) as ReqTypeText,                  
( select ServiceName from [tbRequirementDropDownData] where id=cr.ReqForTraceability) as ReqForTraceabilityText                
, @SourcingResultId as SourcingResult               
, cuc.CurrencyCode as ClientCurrencyCode              
, cuc.GlobalCurrencyNo as ClientGlobalCurrencyNo           
, dbo.ufn_get_productdutyrate(cr.ProductNo,getdate()) as ProductDutyRate             
, ( select top 1 MSLLevelId from tbMSLLevel where MSLLevel=cr.MSL) as MSLLevelNo        
,@PurchaseRequestId as PurchaseRequestId        
,@PurchaseRequestNumber as PurchaseRequestNumber    
 ,cast(dbo.ufn_GetECCNMessage(cr.ECCNCode,cr.ClientNo)as nvarchar(900))as IHSECCNCodeDefination  
 , CASE WHEN ISNULL(cr.AS6081,0)=1 THEN 'Yes' ELSE 'No' END AS IsAs6081Required     
 ,cr.IsPDFAvailable,cr.IHSPartsId  
FROM  dbo.vwCustomerRequirement cr                  
LEFT JOIN dbo.tbCurrency cuc   ON cr.ClientCurrencyNo = cuc.CurrencyId                    
WHERE cr.CustomerRequirementId = @CustomerRequirementId           
END          
  

GO

