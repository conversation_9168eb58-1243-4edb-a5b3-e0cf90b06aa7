IF OBJECT_ID('usp_IsAllowEnableKubForBOMPart', 'P') IS NOT NULL
	DROP PROC [dbo].[usp_IsAllowEnableKubForBOMPart]
GO
/* 
===========================================================================================
TASK      		UPDATED BY     	DATE         	ACTION 			DESCRIPTION
[US-205444]		An.TranTan		15-Jun-2024		Create			Enable KUB for BOM Part if there is any data of Customer Quotes in the last 12 months 
===========================================================================================
*/

CREATE PROCEDURE [dbo].[usp_IsAllowEnableKubForBOMPart] 
	@PartNo NVARCHAR(100) = '',
	@ClientID INT = 0 
AS 
BEGIN

DECLARE	@IsAllowed BIT = 0,
		@IsExistedQuote BIT = 0,
		@IsExistedQuoteShareByHUB BIT = 0,
		@IsExistedPurchaseOrder BIT = 0;

DECLARE @Last12Months DATETIME = dateadd(month,datediff(month,0,getdate())-12,0); 

SET @ClientID = CASE WHEN @ClientID = 114 THEN 0 
					ELSE ISNULL(@ClientID, 0) 
				END;

--Check if exist quote in last 12 months for part number
IF EXISTS (
	SELECT TOP 1 1 FROM tbQuoteLine ql WITH (NOLOCK)
	JOIN tbQuote q WITH (NOLOCK) on q.QuoteId = ql.QuoteNo 
	WHERE ql.FullPart = @PartNo
		AND (@ClientID = 0 OR q.ClientNo = @ClientID)
		AND q.DateQuoted >= @Last12Months
)
BEGIN
	SET @IsExistedQuote = 1;
END

--Check Quote share by HUB
IF EXISTS (
	SELECT TOP 1 1
	FROM tbSourcingresult sr WITH (NOLOCK)         
	JOIN tbCustomerRequirement cust WITH (NOLOCK) ON sr.CustomerRequirementNo=cust.CustomerRequirementId 
	JOIN tbBOM bom WITH (NOLOCK) ON bom.BOMId = cust.BOMNo 
	WHERE sr.FullPart = @PartNo AND sr.SourcingTable IN ('EPPH','EXPH','OFPH','RLPH') 
	AND (@ClientID = 0 OR bom.ClientNo = @ClientID) 
)
BEGIN
	SET @IsExistedQuoteShareByHUB = 1;
END

--Check Buy Price in the last 6 months
IF EXISTS (
	SELECT TOP 1 1 FROM tbKUBPoDetailsCache WITH (NOLOCK)
	WHERE Part = @PartNo AND (@ClientID = 0 OR ClientNo = @ClientID)
)
BEGIN
	SET @IsExistedPurchaseOrder = 1;
END

IF (@IsExistedQuote = 1 OR @IsExistedQuoteShareByHUB = 1 OR @IsExistedPurchaseOrder = 1)
BEGIN
	SET @IsAllowed = 1;
END

SELECT @IsAllowed AS IsAllowedEnable

END
GO

/*===================Test script===================
EXEC [dbo].[usp_IsAllowEnableKubForBOMPart]
	@PartNo = 'BZX84C36LT1G',
	@ClientID = 0
GO
*/


