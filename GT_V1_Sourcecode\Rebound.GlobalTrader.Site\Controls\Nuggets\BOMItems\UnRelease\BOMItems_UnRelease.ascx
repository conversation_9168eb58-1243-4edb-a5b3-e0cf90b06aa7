<%@ Control Language="C#" CodeBehind="BOMItems_UnRelease.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Forms.BOMItems_UnRelease" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Form:DesignBase ID="ctlDB" runat="server" ShowRequiredFieldsExplanation="false"
    AllowQuickHelp="false">
    <Explanation>
        <%=Functions.GetGlobalResource("FormExplanations", "BomItems_UnRelease")%></Explanation>
    <Content>
        <ReboundUI_Table:Form ID="frm" runat="server">
            <ReboundUI_Form:FormField ID="ctlUnRelease" runat="server" FieldID="ctlUnRelease"
                ResourceTitle="SaveConfirm">
                <Field>
                    <ReboundUI:Confirmation ID="ctlUnRelease" runat="server" />
                </Field>
            </ReboundUI_Form:FormField>
        </ReboundUI_Table:Form>
    </Content>
</ReboundUI_Form:DesignBase>
