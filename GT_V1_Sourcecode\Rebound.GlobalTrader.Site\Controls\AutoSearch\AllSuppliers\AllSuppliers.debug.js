///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 15.02.2010:
// - fix data coming from AllCompanies.ashx rather than AllSuppliers.ashx
//
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.AutoSearch");

Rebound.GlobalTrader.Site.Controls.AutoSearch.AllSuppliers = function(element) { 
	Rebound.GlobalTrader.Site.Controls.AutoSearch.AllSuppliers.initializeBase(this, [element]);
	this._blnUseSupplierNo = false;
};

Rebound.GlobalTrader.Site.Controls.AutoSearch.AllSuppliers.prototype = {

    get_blnUseSupplierNo: function() { return this._blnUseSupplierNo; }, set_blnUseSupplierNo: function(v) { if (this._blnUseSupplierNo !== v) this._blnUseSupplierNo = v; },

    initialize: function() {
        Rebound.GlobalTrader.Site.Controls.AutoSearch.AllSuppliers.callBaseMethod(this, "initialize");
        this.addDataReturnedEvent(Function.createDelegate(this, this.dataReturned));
        this.setupDataObject("AllSuppliers");
    },

    dispose: function() {
        if (this.isDisposed) return;
        this._blnUseSupplierNo = null;
        Rebound.GlobalTrader.Site.Controls.AutoSearch.AllSuppliers.callBaseMethod(this, "dispose");
    },

    dataReturned: function() {
        if (!this._result) return;
        if (this._result.TotalRecords > 0) {
            for (var i = 0, l = this._result.Results.length; i < l; i++) {
                var res = this._result.Results[i];
                var strHTML = "";
                if (this._enmResultsActionType == $R_ENUM$AutoSearchResultsActionType.Navigate) {
                    strHTML = $RGT_nubButton_Company(res.ID, res.Name);
                } else {
                    strHTML = res.Name;
                }
                this.addResultItem(strHTML, res.Name, (this._blnUseSupplierNo) ? res.SupNo : res.ID, res.Email);
                strHTML = null; res = null;
            }
        }
    }

};

Rebound.GlobalTrader.Site.Controls.AutoSearch.AllSuppliers.registerClass("Rebound.GlobalTrader.Site.Controls.AutoSearch.AllSuppliers", Rebound.GlobalTrader.Site.Controls.AutoSearch.Base, Sys.IDisposable);
