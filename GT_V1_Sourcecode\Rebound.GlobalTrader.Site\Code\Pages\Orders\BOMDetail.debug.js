///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference name="~/Controls/IconButton/IconButton.js" />
///<reference name="~/Controls/ReboundTextBox/ReboundTextBox.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Pages.Orders");

Rebound.GlobalTrader.Site.Pages.Orders.BOMDetail = function(el) {
    Rebound.GlobalTrader.Site.Pages.Orders.BOMDetail.initializeBase(this, [el]);
    //this.getClosedStatus=true;
};

Rebound.GlobalTrader.Site.Pages.Orders.BOMDetail.prototype = {
    get_intBOMID: function() { return this._intBOMID; }, set_intBOMID: function(v) { if (this._intBOMID !== v) this._intBOMID = v; },
    get_ctlMainInfo: function () { return this._ctlMainInfo; }, set_ctlMainInfo: function (v) { if (this._ctlMainInfo !== v) this._ctlMainInfo = v; },
    get_ctlBOMPVV: function () { return this._ctlBOMPVV; }, set_ctlBOMPVV: function (v) { if (this._ctlBOMPVV !== v) this._ctlBOMPVV = v; },
    get_ctlBOMItems: function() { return this._ctlBOMItems; }, set_ctlBOMItems: function(v) { if (this._ctlBOMItems !== v) this._ctlBOMItems = v; },
    get_ctlSourcingResults: function() { return this._ctlSourcingResults; }, set_ctlSourcingResults: function(v) { if (this._ctlSourcingResults !== v) this._ctlSourcingResults = v; },
    get_ctlPOHubSourcing: function() { return this._ctlPOHubSourcing; }, set_ctlPOHubSourcing: function(v) { if (this._ctlPOHubSourcing !== v) this._ctlPOHubSourcing = v; },
    get_ctlPOHubAutoSourcing: function () { return this._ctlPOHubAutoSourcing; }, set_ctlPOHubAutoSourcing: function (v) { if (this._ctlPOHubAutoSourcing !== v) this._ctlPOHubAutoSourcing = v; },
    get_ctlBomCSV: function() { return this._ctlBomCSV; }, set_ctlBomCSV: function(v) { if (this._ctlBomCSV !== v) this._ctlBomCSV = v; },
    //get_pnlStatus: function() { return this._pnlStatus; }, set_pnlStatus: function(v) { if (this._pnlStatus !== v) this._pnlStatus = v; },
    get_lblStatus: function() { return this._lblStatus; }, set_lblStatus: function(v) { if (this._lblStatus !== v) this._lblStatus = v; },
    get_ctlCsvExportHistory: function() { return this._ctlCsvExportHistory; }, set_ctlCsvExportHistory: function(v) { if (this._ctlCsvExportHistory !== v) this._ctlCsvExportHistory = v; },
   // get_getClosedStatus: function() { return this._getClosedStatus; }, set_getClosedStatus: function(v) { if (this._getClosedStatus !== v) this._getClosedStatus = v; },
    get_btnPrint: function () { return this._btnPrint; }, set_btnPrint: function (v) { if (this._btnPrint !== v) this._btnPrint = v; },
    //    get_ibtnAssignToMe: function() { return this._ibtnAssignToMe; }, set_ibtnAssignToMe: function(v) { if (this._ibtnAssignToMe !== v) this._ibtnAssignToMe = v; },
    get_ctlExpediteHistory: function () { return this._ctlExpediteHistory; }, set_ctlExpediteHistory: function (v) { if (this._ctlExpediteHistory !== v) this._ctlExpediteHistory = v; },
    get_ctlHubImagesDragDrop: function () { return this._ctlHubImagesDragDrop; }, set_ctlHubImagesDragDrop: function (v) { if (this._ctlHubImagesDragDrop !== v) this._ctlHubImagesDragDrop = v; },
    get_ApprovalMessage: function () { return this._ApprovalMessage; }, set_ApprovalMessage: function (v) { if (this._ApprovalMessage !== v) this._ApprovalMessage = v; },
    get_IsPOHub: function () { return this._IsPOHub; }, set_IsPOHub: function (v) { if (this._IsPOHub !== v) this._IsPOHub = v; },
    initialize: function() {
        //    if (this._ibtnAssignToMe) {
        //        $R_IBTN.addClick(this._ibtnAssignToMe, Function.createDelegate(this, this.assignToMe));
        //        }
        Rebound.GlobalTrader.Site.Pages.Orders.BOMDetail.callBaseMethod(this, "initialize");
    },
    

    goInit: function () {
        
        if (this._btnPrint) {
            this._btnPrint.addPrint(Function.createDelegate(this, this.printHUBRFQ));
            this._btnPrint.addEmail(Function.createDelegate(this, this.emailHUBRFQ));
            this._btnPrint.addExtraButtonClick(Function.createDelegate(this, this.printOtherDocs));
        }
        if (this._ctlMainInfo) this._ctlMainInfo.addGotData(Function.createDelegate(this, this.mainBOMDetailGotDataOK));
        /*if (this._ctlBOMPVV) this._ctlBOMPVV.addGotData(Function.createDelegate(this, this.mainBOMDetailGotDataOK));*/
        //alert(this.getClosedStatus);
        if (this._ctlBOMItems) this._ctlBOMItems.addPartSelected(Function.createDelegate(this, this.selectPart));
        if (this._ctlBOMItems) this._ctlBOMItems.addStartGetData(Function.createDelegate(this, this.mainInfoStartGetData));
        if (this._ctlBOMItems) this._ctlBOMItems.addGotDataOK(Function.createDelegate(this, this.mainInfoGotDataOK));
        if (this._ctlBOMItems) this._ctlBOMItems.addGetDataComplete(Function.createDelegate(this, this.ctlBOMItems_GetDataComplete));
        if (this._ctlSourcingResults) this._ctlSourcingResults.addAddFormShown(Function.createDelegate(this, this.ctlSourcingResults_AddFormShown));
        if (this._ctlSourcingResults) this._ctlSourcingResults.addGotDataOK(Function.createDelegate(this, this.ctlSourcingResults_GetDataComplete));
        if (this._ctlBOMItems) this._ctlBOMItems.addCallBeforeRelease(Function.createDelegate(this, this.ctlBOMItems_addCallBeforeRelease));
        if (this._ctlMainInfo) this._ctlMainInfo.addCallBeforeRelease(Function.createDelegate(this, this.ctlMainInfo_addCallBeforeRelease));
        if (this._ctlBOMItems) this._ctlBOMItems.addRefereshAfterRelease(Function.createDelegate(this, this.ctlBOMItems_RefereshAfterRelease));
        if (this._ctlBOMItems) this._ctlBOMItems.addImportSourcingResultSuccess(Function.createDelegate(this, this.ctlBOMItems_RefreshAfterImportSourcingResult));
        //if (this._ctlMainInfo) { this._ctlBOMItems.disableItemAddButton(this._ctlMainInfo._isAddButtonEnable) };
        if (this._ctlPOHubSourcing) { this._ctlPOHubSourcing.addSourcingResultAdded(Function.createDelegate(this, this.ctlSourcing_SourcingResultAdded)) };
        if (this._ctlSourcingResults) { this._ctlSourcingResults.addSaveEditComplete(Function.createDelegate(this, this.ctlSourcingResults_SaveEditComplete)) };
        if (this._ctlSourcingResults) { this._ctlSourcingResults.addSourcingResultDeleted(Function.createDelegate(this, this.ctlSourcingResults_SourcingResultDeleted)) };
        if (this._ctlSourcingResults) this._ctlSourcingResults.addCallBeforeRelease(Function.createDelegate(this, this.ctlSourcingResults_addCallBeforeRelease));
        if (this._ctlSourcingResults) { this._ctlSourcingResults.addSourcingResultSelect(Function.createDelegate(this, this.ctlSourcingResults_SourcingResultSelect)) };
        if (this._ctlSourcingResults) this._ctlSourcingResults.addAddCustFormShown(Function.createDelegate(this, this.ctlSourcingResults_AddCustFormShown));
        // this.setFieldsFromMainInfo();
        //alert(this._ApprovalMessage);
        if (this._ctlPOHubAutoSourcing) { this._ctlPOHubAutoSourcing.updateSourcingResultComplete(Function.createDelegate(this, this.ctlAutoSourcing_SourcingResultUpdated)) };

        Rebound.GlobalTrader.Site.Pages.Orders.BOMDetail.callBaseMethod(this, "goInit");
    },

    dispose: function() {
        if (this.isDisposed) return;
        if (this._ctlMainInfo) this._ctlMainInfo.dispose();
        if (this._ctlBOMPVV) this._ctlBOMPVV.dispose();
        if (this._ctlSourcingResults) this._ctlSourcingResults.dispose();
        if (this._ctlPOHubSourcing) this._ctlPOHubSourcing.dispose();
        if (this._ctlPOHubAutoSourcing) this._ctlPOHubAutoSourcing.dispose();
        // if (this._ctlLines) this._ctlLines.dispose();
        this._ctlBomCSV = null;
        this._ctlBOMPVV = null;
        this._pnlStatus = null;
        this._lblStatus = null;
        this._ctlMainInfo = null;
        this._ctlBOMItems = null;
        this._ApprovalMessage = null;
        this._ctlPOHubSourcing = null;
        this._ctlExpediteHistory = null;
        this._ctlSourcingResults = null;
        this._ctlCsvExportHistory = null;
        this._ctlHubImagesDragDrop = null;
        Rebound.GlobalTrader.Site.Pages.Orders.BOMDetail.callBaseMethod(this, "dispose");
    },

    selectPart: function () {
        
       
            this._intCustomerRequirementID = this._ctlBOMItems._intCustomerRequirementID;
            this._ctlSourcingResults._blnRequirementClosed = this._ctlBOMItems._blnRequirementClosed;
            this._ctlSourcingResults._intCustomerRequirementID = this._intCustomerRequirementID;
            this._ctlSourcingResults._strPartNo = this._ctlBOMItems.getSelectedPartNo();
            this._ctlSourcingResults._ClientNo = this._ctlBOMItems.getIPOClientNo();
            this._ctlSourcingResults._intBOMID = this._ctlMainInfo._intBOMID;
            this._ctlSourcingResults._BomCode = this._ctlMainInfo.getFieldValue("ctlCode");
            this._ctlSourcingResults._BomName = this._ctlMainInfo.getFieldValue("ctlName");
            this._ctlSourcingResults._BomCompanyName = this._ctlMainInfo.getFieldValue("ctlCompany");
            this._ctlSourcingResults._BomCompanyNo = this._ctlMainInfo.getFieldValue("hidCompanyNo");
            this._ctlSourcingResults._SalesManNo = this._ctlMainInfo._RequestToPOHubBy;
            this._ctlSourcingResults._ReqSalesman = this._ctlBOMItems._ReqSalesman;
        this._ctlSourcingResults._SupportTeamMemberNo = this._ctlBOMItems._SupportTeamMemberNo;

        if (this._ctlBOMPVV) this._ctlSourcingResults._intBOMID = this._ctlBOMPVV._intBOMID;
        //this._ctlSourcingResults._BomCode = this._ctlBOMPVV.getFieldValue("ctlCode");
        //this._ctlSourcingResults._BomName = this._ctlBOMPVV.getFieldValue("ctlName");
        //this._ctlSourcingResults._BomCompanyName = this._ctlBOMPVV.getFieldValue("ctlCompany");
        //this._ctlSourcingResults._BomCompanyNo = this._ctlBOMPVV.getFieldValue("hidCompanyNo");
        //this._ctlSourcingResults._SalesManNo = this._ctlBOMPVV._RequestToPOHubBy;
            
            this._ctlSourcingResults.getData();
            if (!this._ctlBOMItems._isClosed) {
                if (this._ctlPOHubSourcing) this._ctlPOHubSourcing._intCustomerRequirementID = this._intCustomerRequirementID;
                if (this._ctlPOHubSourcing) this._ctlPOHubSourcing._blnRequirementClosed = this._ctlBOMItems._blnRequirementClosed;
                if (this._ctlPOHubSourcing) this._ctlPOHubSourcing.selectPart(this._ctlBOMItems.getSelectedPartNo());
                if (this._ctlPOHubSourcing) this._ctlPOHubSourcing.updateIPOClientId(this._ctlBOMItems.getIPOClientNo());
                if (this._ctlPOHubSourcing) this._ctlPOHubSourcing._blnRequirementReleased = this._ctlBOMItems._blnRequirementReleased || (this._ctlBOMItems._isNoBid && this._ctlBOMItems._blnPOHub);
        }
        if (this._ctlPOHubAutoSourcing) {
            if (this._ctlMainInfo._inActive || this._ctlBOMItems._isClosedOnly) {
                this._ctlPOHubAutoSourcing.show(false);
            } else {
                this._ctlPOHubAutoSourcing._partNo = this._ctlBOMItems.getSelectedPartNo();
                this._ctlPOHubAutoSourcing._cusReqID = this._intCustomerRequirementID;
                this._ctlPOHubAutoSourcing._blnRequirementClosed = this._ctlBOMItems._blnRequirementClosed;
                this._ctlPOHubAutoSourcing.getAutoSourcing();
                this._ctlPOHubAutoSourcing.show(true);
            }
        }
    },

    ctlSourcing_SourcingResultAdded: function() {
        this._ctlMainInfo.getData();
        this._ctlSourcingResults.getData();
        if (this._ctlPOHubSourcing) this._ctlPOHubSourcing.hideAddForm();
    },

    ctlSourcingResults_SourcingResultDeleted: function() {
        this._ctlMainInfo.getData();
        this._ctlMainInfo.enableDisableReleaseButton(this._ctlBOMItems._allExistInSourcingResult);
    },

    ctlAutoSourcing_SourcingResultUpdated: function () {
        this._ctlSourcingResults.getData();
        if (this._ctlPOHubAutoSourcing) this._ctlPOHubAutoSourcing.getAutoSourcing();
    },

    mainInfoStartGetData: function () {
        if (this._ctlPOHubSourcing) this._ctlPOHubSourcing.show(false);
        this._ctlSourcingResults.show(false);
        if (this._ctlHubImagesDragDrop) this._ctlHubImagesDragDrop.show(false);
        if (this._ctlPOHubAutoSourcing) this._ctlPOHubAutoSourcing.show(false);
    },
    mainInfoGotDataOK: function () {
      if (this._ctlSourcingResults) this._ctlSourcingResults._intCompanyID = this._ctlBOMItems._intCompanyID;
        // if (this._ctlPOHubSourcing) this._ctlPOHubSourcing.show(!this._ctlBOMItems._blnRequirementReleased);
        if (this._ctlPOHubSourcing) this._ctlPOHubSourcing.show(!this._ctlMainInfo._inActive && !this._ctlBOMItems._isClosedOnly);

        $R_IBTN.enableButton(this._ctlPOHubSourcing._ibtnSearch, GetAssignToMe());
        this._ctlSourcingResults.show(!this._ctlMainInfo._inActive );

        this._ctlSourcingResults._hasReleasedItem = !this._ctlBOMItems._blnRequirementReleased;
        //$R_FN.setInnerHTML(this._lblStatus, this._ctlMainInfo.getFieldValue("hidDisplayStatus"));
        //this._ctlSourcingResults._hasReleasedItem =!this._ctlBOMItems._isNoBid;

        if (this._ctlPOHubSourcing) {
            this._ctlSourcingResults._Quantity = this._ctlBOMItems.getFieldValue("ctlQuantity");
            this._ctlSourcingResults._PackageID = this._ctlBOMItems.getFieldValue("hidPackageID");
            this._ctlSourcingResults._ProductID = this._ctlBOMItems.getFieldValue("hidProductID");
            this._ctlSourcingResults._ROHS = this._ctlBOMItems.getFieldValue("hidROHS");
            this._ctlSourcingResults._Manufacturer = this._ctlBOMItems.getFieldValue("hidManufacturer");
            this._ctlSourcingResults._ManufacturerNo = this._ctlBOMItems.getFieldValue("hidManufacturerNo");
            this._ctlSourcingResults._MfrAdvisoryNotes = this._ctlBOMItems.getFieldValue("hidMfrAdvisoryNotes");
            this._ctlSourcingResults._DateCode = this._ctlBOMItems.getFieldValue("ctlDateCode");
            this._ctlSourcingResults._Product = this._ctlBOMItems.getFieldValue("ctlProduct");
        }
    },

    ctlBOMItems_RefereshAfterRelease: function() {
        this._ctlMainInfo.getData();
        //this._ctlBOMPVV.getData();
        
    },

    mainBOMDetailGotDataOK: function () {
        let bomStatus = this._ctlMainInfo.getFieldValue("hidDisplayStatus");
        $R_FN.setInnerHTML(this._lblStatus, bomStatus);
        //alert(this._ctlMainInfo.getFieldValue("hidDisplayStatus"));
        if(this._ctlMainInfo.getFieldValue("hidDisplayStatus"))
        this._ctlBOMItems._inActive = this._ctlMainInfo._inActive;
        this._ctlBOMItems._blnRequestedToPoHub = this._ctlMainInfo._blnRequestedToPoHub;
        //alert(this._ctlMainInfo._isClosed);
        this._ctlBOMItems._isClosed =   this._ctlMainInfo._isClosed; 
        this._ctlBOMItems._isClosedOnly = bomStatus == 'CLOSED' ? true : false;
        this._ctlBOMItems._intContact2No = this._ctlMainInfo._intContact2No;
        if (this._ctlBOMItems) this._ctlBOMItems.getData();

        //show BOM Import button in 2 cases:
        //-- BOM Status = NEW in client side
        //-- Bom Status = RFQ in DMCC
        let isShow = (!this._IsPOHub && bomStatus == 'NEW') || (this._IsPOHub && bomStatus == 'RPQ');
        this._ctlBomCSV.showUpdateBOMButton(isShow);
    },

    ctlBOMItems_GetDataComplete: function() {
        //this._ctlBOMItems.disableItemAddButton(!this._ctlMainInfo._inActive);
        this._ctlMainInfo._blnHasRequirement = (this._ctlBOMItems._intCountStock > 0);
        this._ctlMainInfo.enableButtons(true);
        if (this._ctlMainInfo._isPurchaseHub) {
            this._ctlMainInfo.disableNotifyAndExportButton(!this._ctlMainInfo._isPurchaseHub);
        }
        this._ctlMainInfo.enableDisableReleaseButton(this._ctlBOMItems._allExistInSourcingResult);
        if (this._ctlExpediteHistory) this._ctlExpediteHistory.GetExpediteHistory();
       // alert("Rferes");
    },

    ctlSourcingResults_GetDataComplete: function() {
        this._ctlBOMItems._blnAllHasDelDate = this._ctlSourcingResults._blnAllHasDelDate;
        this._ctlMainInfo._blnAllHasDelDate = this._ctlSourcingResults._blnAllHasDelDate;
        this._ctlBOMItems._blnAllHasProduct = this._ctlSourcingResults._blnAllHasProduct;
        this._ctlMainInfo._blnAllHasProduct = this._ctlSourcingResults._blnAllHasProduct;
        //to bind data on Add Quote to CLient load
        //Comment on 17 may : seems default selection not working
        //if (this._ctlPOHubSourcing) this._ctlSourcingResults._Quantity = this._ctlBOMItems.getFieldValue("ctlQuantity");
        //if (this._ctlPOHubSourcing) this._ctlSourcingResults._PackageID = this._ctlBOMItems.getFieldValue("hidPackageID");
        //if (this._ctlPOHubSourcing) this._ctlSourcingResults._ProductID = this._ctlBOMItems.getFieldValue("hidProductID");
        //if (this._ctlPOHubSourcing) this._ctlSourcingResults._ROHS = this._ctlBOMItems.getFieldValue("hidROHS");
        //if (this._ctlPOHubSourcing) this._ctlSourcingResults._Manufacturer = this._ctlBOMItems.getFieldValue("hidManufacturer");
        //if (this._ctlPOHubSourcing) this._ctlSourcingResults._ManufacturerNo = this._ctlBOMItems.getFieldValue("hidManufacturerNo");
        //if (this._ctlPOHubSourcing) this._ctlSourcingResults._DateCode = this._ctlBOMItems.getFieldValue("ctlDateCode");
        //if (this._ctlPOHubSourcing) this._ctlSourcingResults._Product = this._ctlBOMItems.getFieldValue("ctlProduct");
    },
    ctlSourcingResults_addCallBeforeRelease: function () {
        this._ctlSourcingResults.clearMessages();
        var isValidSourcing = this.validateDeliveryDate(this._ctlSourcingResults._blnAllHasDelDate && this._ctlSourcingResults._blnAllHasProduct);
        this._ctlSourcingResults._blnCanRelease = isValidSourcing;
    },
    ctlBOMItems_addCallBeforeRelease: function() {
        this._ctlSourcingResults.clearMessages();
        var isValidSourcing = this.validateReqItemDeliveryDate(this._ctlBOMItems._blnAllHasDelDate && this._ctlBOMItems._blnAllHasProduct);
       // var isProduct = this.validateProduct(this._ctlBOMItems._blnAllHasProduct);
        //alert(isDeliveryDate + " "+ isProduct);
         this._ctlBOMItems._blnCanRelease = isValidSourcing;
       // this._ctlBOMItems._blnCanRelease = (isDeliveryDate == true && isProduct == true);
        //this._ctlBOMItems._blnCanRelease = this.validateDeliveryDate(this._ctlBOMItems._blnAllHasDelDate);
    },
    ctlMainInfo_addCallBeforeRelease: function() {
        this._ctlMainInfo.clearMessages();
        var isValidSourcing = this.validateHUBRFQMainInfoDeliveryDate(this._ctlBOMItems._blnAllItemHasDelDate && this._ctlBOMItems._blnAllItemHasProduct);
        //var isProduct = this.validateReqItemProduct(this._ctlBOMItems._blnAllItemHasProduct);
        this._ctlMainInfo._blnCanReleaseAll = isValidSourcing;
        //this._ctlMainInfo._blnCanReleaseAll = this.validateReqItemDeliveryDate(this._ctlBOMItems._blnAllItemHasDelDate);
    },

    validateDeliveryDate: function(bln) {
        this._ctlSourcingResults.clearMessages();
        if (bln) {
            return true;
        }
        else {

            this._ctlSourcingResults.addMessage("Delivery date and product is required for all attached sourcing results", $R_ENUM$MessageTypeList.Error)
            return false;
        }

    },

    validateReqItemDeliveryDate: function(bln) {
        this._ctlBOMItems.clearMessages();
        if (bln) {
            return true;
        }
        else {

            this._ctlBOMItems.addMessage("Delivery date and product is required for all attached sourcing results", $R_ENUM$MessageTypeList.Error)
            return false;
        }

    },
    validateProduct: function(bln) {
        this._ctlSourcingResults.clearMessages();
        if (bln) {
            return true;
        }
        else {

            this._ctlSourcingResults.addMessage("Delivery date and product is required for all attached sourcing results", $R_ENUM$MessageTypeList.Error)
            return false;
        }

    },

    validateReqItemProduct: function(bln) {
        this._ctlBOMItems.clearMessages();
        if (bln) {
            return true;
        }
        else {

            this._ctlBOMItems.addMessage("Delivery date and product is required for all attached sourcing results", $R_ENUM$MessageTypeList.Error)
            return false;
        }

    },
    ctlSourcingResults_SaveEditComplete: function() {
        //this._ctlBOMItems.getData();
        this._ctlMainInfo.getData();
       // this._ctlBOMPVV.getData();
        
        //this._ctlMainInfo.enableDisableReleaseButton(this._ctlBOMItems._allExistInSourcingResult);
       
    },

    ctlMainInfo_SaveEditComplete: function() {
        this._ctlLines.getData();

    },
    printHUBRFQ: function () {
      //  alert(this._intBOMID);
        this._BomId = this._ctlMainInfo._intBOMID;
       // alert(this._BomId);
        $R_FN.openPrintWindow($R_ENUM$PrintObject.PrintHUBRFQ, this._BomId);
    },
    emailHUBRFQ: function () {
        this._BomId = this._ctlMainInfo._intBOMID;
        $R_FN.openPrintWindow($R_ENUM$PrintObject.EmailHUBRFQ, this._BomId,true);
    },
    printOtherDocs: function () {
        if (this._btnPrint._strExtraButtonClickCommand == "PrintLog") $R_FN.openPrintLogWindow($R_ENUM$PrintObject.PrintLog, this._BomId, false, "PrintHUBRFQ");
    },
    ctlSourcingResults_AddFormShown: function () {
        this._ctlSourcingResults._frmAdd.setFieldValue("ctlMSL", this._ctlBOMItems.getFieldValue("hidMSL"));
        //        this._ctlSourcingResults._frmAdd.setFieldValue("ctlManufacturer", this._ctlMainInfo.getFieldValue("hidManufacturerNo"), null, this._ctlMainInfo.getFieldValue("hidManufacturer"));
        //        this._ctlSourcingResults._frmAdd.setFieldValue("ctlDateCode", this._ctlMainInfo.getFieldValue("ctlDateCode"));
        //        this._ctlSourcingResults._frmAdd.setFieldValue("ctlProduct", this._ctlMainInfo.getFieldValue("hidProductID"));
        //        this._ctlSourcingResults._frmAdd.setFieldValue("ctlPackage", this._ctlMainInfo.getFieldValue("hidPackageID"));
        //        this._ctlSourcingResults._frmAdd.setFieldValue("ctlQuantity", this._ctlMainInfo.getFieldValue("ctlQuantity"));
        //        this._ctlSourcingResults._frmAdd.setFieldValue("ctlCurrency", this._ctlMainInfo.getFieldValue("hidCurrencyID"));
        //        this._ctlSourcingResults._frmAdd.setFieldValue("ctlROHS", this._ctlMainInfo.getFieldValue("hidROHS"));
    },
    ctlSourcingResults_AddCustFormShown: function () {
        this._ctlSourcingResults._frmAdd.setFieldValue("ctlMSL", this._ctlBOMItems.getFieldValue("hidMSL"));
        this._ctlSourcingResults._frmAdd.setFieldValue("ctlManufacturer", this._ctlBOMItems.getFieldValue("hidManufacturerNo"), null, this._ctlBOMItems.getFieldValue("hidManufacturer"));
        this._ctlSourcingResults._frmAdd.setFieldValue("ctlDateCode", this._ctlBOMItems.getFieldValue("ctlDateCode"));
        //this._ctlSourcingResults._frmAdd.setFieldValue("ctlProduct", this._ctlBOMItems.getFieldValue("hidProductID"));
        this._ctlSourcingResults._frmAdd.setFieldValue("ctlProduct", this._ctlBOMItems.getFieldValue("hidProductID"), null, this._ctlBOMItems.getFieldValue("ctlProduct"));
        this._ctlSourcingResults._frmAdd.setFieldValue("ctlPackage", this._ctlBOMItems.getFieldValue("hidPackageID"), null, this._ctlBOMItems.getFieldValue("ctlPackage"));
        this._ctlSourcingResults._frmAdd.setFieldValue("ctlQuantity", this._ctlBOMItems.getFieldValue("ctlQuantity"));
        this._ctlSourcingResults._frmAdd.setFieldValue("ctlCurrency", this._ctlBOMItems.getFieldValue("hidCurrencyID"));
        this._ctlSourcingResults._frmAdd.setFieldValue("ctlROHS", this._ctlBOMItems.getFieldValue("hidROHS"));
    },
    ctlSourcingResults_SourcingResultSelect: function ()
    {
        if (this._ctlHubImagesDragDrop && this._ctlSourcingResults._selectedRow == 1) {
            this._ctlHubImagesDragDrop.show(true);
            this._ctlHubImagesDragDrop._intRefDocID = this._ctlSourcingResults._SourcingResultNo;
            this._ctlHubImagesDragDrop.viewPanel(this._ctlSourcingResults._blnImageAvail);
        }
        else {
            this._ctlHubImagesDragDrop.show(false);
        }
        //alert(this._ctlSourcingResults._SourcingResultNo);
    },
    validateHUBRFQMainInfoDeliveryDate: function (bln) {
        this._ctlMainInfo.clearMessages();
        if (bln) {
            return true;
        }
        else {

            this._ctlMainInfo.addMessage("Delivery date and product is required for all attached sourcing results", $R_ENUM$MessageTypeList.Error)
            return false;
        }

    },
    ctlBOMItems_RefreshAfterImportSourcingResult: function () {
        this._ctlBOMItems.getData();
        this._ctlBomCSV.showPDFPanel();
        this._ctlCsvExportHistory.getCreditHistory();
    }
    
};

Rebound.GlobalTrader.Site.Pages.Orders.BOMDetail.registerClass("Rebound.GlobalTrader.Site.Pages.Orders.BOMDetail", Rebound.GlobalTrader.Site.Pages.Content, Sys.IDisposable);
