﻿using System;
using System.Linq;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;
using System.IO;
//using System.Linq;
using System.Net;
using System.Text;
using HttpUtils;
using IHSPart;
using System.Text.RegularExpressions;
using System.Data.Common;
using System.Reflection;
using Rebound.GlobalTrader.Site.Code.Common;
using Rebound.GlobalTrader.Site.Enumerations;
using System.Threading.Tasks;
using System.Net.Http;
using System.Net.Http.Headers;


using System.Configuration;
//using Microsoft.ApplicationInsights;

namespace Rebound.GlobalTrader.Site.Areas.BOM.Models
{

    public class IHSAPICall : Rebound.GlobalTrader.Site.Data.Base
    {
        class IHSServies
        {



            public class PartDetail
            {
                public string msl { get; set; }
                public string pckMethod { get; set; }
                public string pckCd { get; set; }
            }

            public class PartInfo
            {
                public string avgPrice { get; set; }
            }

            public class MfrPart
            {
                public string mfrFullName { get; set; }
                public string coo { get; set; }
            }
            public class ExpCompliance
            {
                public string htsCd { get; set; }
                public string eccn { get; set; }
            }

            public class Attributes
            {
                public MfrPart mfrPart { get; set; }
                public ExpCompliance expCompliance { get; set; }
                public PartDetail partDetail { get; set; }
                public PartInfo partInfo { get; set; }
                // public Collections collections { get; set; }

            }
            public class Price
            {
                public string source { get; set; }
                public double price { get; set; }
                public string currency { get; set; }
                public string telephone { get; set; }
                public string email { get; set; }
                public string priceAvailabilityLink { get; set; }
            }
            //public Price prices { get; set; }
            public class Collections
            {
                public List<Price> prices { get; set; }
            }

            public class Root
            {


                public Int64 id { get; set; }
                public string prtNbr { get; set; }
                public string mfrName { get; set; }
                public string prtStatus { get; set; }
                public string prtDesc { get; set; }
                public Attributes attributes { get; set; }
                public Collections collections { get; set; }



                public Nullable<int> ManufacturerNo { get; set; }
                public string Descriptions { get; set; }
                public string CountryOfOrigin { get; set; }
                public Nullable<int> CountryOfOriginNo { get; set; }
                public string LifeCycleStage { get; set; }

                public string MSL { get; set; }
                public Nullable<int> MSLNo { get; set; }
                public string HTSCode { get; set; }
                public Nullable<double> AveragePrice { get; set; }
                public string Packaging { get; set; }
                public Nullable<int> PackagingSize { get; set; }
                public Nullable<int> UpdatedBy { get; set; }
                public Nullable<System.DateTime> OriginalEntryDate { get; set; }
                public Nullable<System.DateTime> DLUP { get; set; }
                public Nullable<bool> Inactive { get; set; }
            }
        }
        class TokenRespose
        {
            public string authToken { get; set; }

        }
        private TokenRespose tokenRespose;
        string token = string.Empty;
        int Tokenvalidornot = 0;
        bool ServiceStatus = true;
        private int GetTokenNumber(string Tockenno, int tokenid)
        {

            try
            {
                string TokenNumber = "";
                Tokenvalidornot = BLL.Part.GetIHSTokenData(out TokenNumber);
                return Tokenvalidornot;

            }
            catch (Exception e)
            {
               
                WriteError(e);
                return 0;
            }
            finally
            {

            }
        }
        public void InsertTokenNumber(string TokenNumberInsert)
        {
            try
            {
                int intResult = CustomerRequirement.InsertTokenNumberFromAPI(
                    TokenNumberInsert
                    );
                if (intResult > 0)
                {

                }
                else
                {
                    WriteErrorSQLActionFailed("Insert");
                }
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }
        public List<Part> GetDataGrid(string[] Parts)
        {
            string IHSUserName = Convert.ToString(ConfigurationManager.AppSettings["IHSUserName"]);
            string IHSUserPass = Convert.ToString(ConfigurationManager.AppSettings["IHSUserPass"]);
            string IHSTokenGenURL = Convert.ToString(ConfigurationManager.AppSettings["IHSTokenGenURL"]);

            List<Part> lstDetails = new List<Part>();
            List<Part> lstDetailsMain = new List<Part>();
            try
            {
                //check token validity expire after 24 hr. code start
                //string TokenNumber = "";
                //Tokenvalidornot = BLL.Part.GetIHSTokenData(out TokenNumber);

                ////check token validity expire after 24 hr. code end
                var client = new HttpUtils.RestClient();
                //if (Tokenvalidornot > 0)
                //{
                //    if (!string.IsNullOrEmpty(TokenNumber))
                //    {
                //        GettbIHSpart(TokenNumber);
                //    }
                //}
                //else
                //{

                client.EndPoint = IHSTokenGenURL;//@"https://4donlinetest.ihs.com/parts-saas/auth";
                client.Method = HttpVerb.POST;
                //client.PostData = "{\n\t\"username\":\"rebounde2\",\n\t\"password\":\"kmHs2L#vn8M\"\n}";
                client.PostData = "{\n\t\"username\":\"" + IHSUserName + "\",\n\t\"password\":\"" + IHSUserPass + "\"\n}";
                client.tokenRespose = null;
                var json = client.MakeRequest();
               
                if (json != null)
                {
                    ServiceStatus = true;
                    System.Web.Script.Serialization.JavaScriptSerializer jsSerializer = new System.Web.Script.Serialization.JavaScriptSerializer();
                    var data1 = jsSerializer.Deserialize<TokenRespose>(json);
                    token = data1.authToken;
                    //InsertTokenNumber(token);
                    //string[] partArray =  Parts.Split(',');
                    foreach (string str in Parts)
                    {
                        lstDetails = GettbIHSpart(token,str);
                        if (lstDetails.Count > 0)
                        {
                            foreach (Part prt in lstDetails)
                            {
                                lstDetailsMain.Add(prt);
                            }
                        }
                    }

                    //need to save token number in tbihstoken into  database .
                    // }
                    return lstDetailsMain;
                }
                else
                {
                    ServiceStatus = false;
                    return lstDetailsMain;
                }


            }
            catch (Exception e)
            {
                WriteError(e);
                return lstDetailsMain;
            }
            finally
            {
                lstDetails = null;
            }

        }
        private List<Part> GettbIHSpart(string taktoken, string part)
        {
            List<Part> lstDetails = new List<Part>();
            try
            {

                string IHSPartSearchURL = Convert.ToString(ConfigurationManager.AppSettings["IHSPartSearchURL"]);
                string strJson = "{\r\n   \"prtNbr\": \"'" + part + "'\",\r\n   \"searchType\":\"" + "exact" + "\",\r\n   \"collections\":[\"prices\"]\r\n   \r\n   \r\n}";
                var client = new HttpUtils.RestClient();
                client.EndPoint = IHSPartSearchURL;
                client.Method = HttpVerb.POST;
                client.PostData = strJson;
                client.tokenRespose = taktoken;
                var json2 = client.MakeRequest();

                if (json2 != null)
                {
                    ServiceStatus = true;
                    System.Web.Script.Serialization.JavaScriptSerializer jsSerializer2 = new System.Web.Script.Serialization.JavaScriptSerializer();
                    var data = jsSerializer2.Deserialize<List<IHSServies.Root>>(json2);

                    lstDetails = CustomerRequirement.InsertIHSApiXML_BOMManager(
                           SessionManager.ClientID,
                           SessionManager.LoginID,
                           ParamsToXml(json2)
                          );

                    return lstDetails;
                    // GetDataGridPopupIHSTable();
                }
                else
                {
                    
                    ServiceStatus = false;
                    return lstDetails;
                }
               
            }
            catch (Exception ex)
            {
                WriteError(ex);
                ServiceStatus = false;
                return lstDetails;

            }

        }
        private static string ParamsToXml(string json2)
        {
            System.Text.StringBuilder strBuilder = new System.Text.StringBuilder();
            string strinAvgPrice = string.Empty;
            System.Web.Script.Serialization.JavaScriptSerializer jsSerializer2 = new System.Web.Script.Serialization.JavaScriptSerializer();
            var data = jsSerializer2.Deserialize<List<IHSServies.Root>>(json2);
            strBuilder.Append("<IHSResults>");
            foreach (IHSServies.Root item in data)
            {


                strBuilder.Append("<IHSResult>");
                strBuilder.Append("<IHSID>");
                strBuilder.Append(item.id);
                strBuilder.Append("</IHSID>");
                // Part
                strBuilder.Append("<prtNbr>");
                //strBuilder.Append(item.prtNbr);
                if (!string.IsNullOrEmpty(item.prtNbr))
                    strBuilder.Append(item.prtNbr.Replace("&", "&amp;"));
                else
                    strBuilder.Append("");
                strBuilder.Append("</prtNbr>");
                //Manuf Name
                strBuilder.Append("<mfrName>");
                strBuilder.Append(item.mfrName);
                strBuilder.Append("</mfrName>");

                strBuilder.Append("<prtStatus>");
                strBuilder.Append(item.prtStatus);
                strBuilder.Append("</prtStatus>");

                strBuilder.Append("<prtDesc>");
                //strBuilder.Append(item.prtDesc);
                if (!string.IsNullOrEmpty(item.prtDesc))
                {
                    strinAvgPrice = "";
                    if (item.attributes.partInfo != null)
                    {
                        strinAvgPrice = Convert.ToString(item.attributes.partInfo.avgPrice);
                        strBuilder.Append(item.prtDesc.Replace("&", "&amp;") + " , Avg Price : " + strinAvgPrice + " (Date Updated : " + DateTime.Now.ToString() + ")");
                    }
                    else
                    {
                        strBuilder.Append(item.prtDesc.Replace("&", "&amp;"));
                    }

                    //strBuilder.Append(item.prtDesc.Replace("&", "&amp;"));
                }
                else
                {
                    strBuilder.Append("");
                }
                strBuilder.Append("</prtDesc>");

                //strBuilder.Append("<prtDesc>");
                //strBuilder.Append(item.prtDesc);
                //strBuilder.Append("</prtDesc>");

                //attributes
                if (item.attributes.mfrPart != null)
                {
                    strBuilder.Append("<mfrFullName>");
                    if (!string.IsNullOrEmpty(item.attributes.mfrPart.mfrFullName))
                        strBuilder.Append(item.attributes.mfrPart.mfrFullName.Replace("&", "&amp;"));
                    else
                        strBuilder.Append("");
                    strBuilder.Append("</mfrFullName>");

                    strBuilder.Append("<coo>");
                    strBuilder.Append(item.attributes.mfrPart.coo);
                    strBuilder.Append("</coo>");


                }
                else
                {
                    strBuilder.Append("<mfrFullName>");
                    strBuilder.Append("");
                    strBuilder.Append("</mfrFullName>");

                    strBuilder.Append("<coo>");
                    strBuilder.Append("");
                    strBuilder.Append("</coo>");
                }
                //expCompliance
                if (item.attributes.expCompliance != null)
                {
                    strBuilder.Append("<htsCd>");
                    strBuilder.Append(item.attributes.expCompliance.htsCd);
                    strBuilder.Append("</htsCd>");

                    strBuilder.Append("<eccn>");
                    strBuilder.Append(item.attributes.expCompliance.eccn);
                    strBuilder.Append("</eccn>");

                }
                else
                {

                    strBuilder.Append("<htsCd>");
                    strBuilder.Append(" ");
                    strBuilder.Append("</htsCd>");

                    strBuilder.Append("<eccn>");
                    strBuilder.Append(" ");
                    strBuilder.Append("</eccn>");
                }
                //Attribute part details
                if (item.attributes.partDetail != null)
                {
                    strBuilder.Append("<msl>");
                    strBuilder.Append(item.attributes.partDetail.msl);
                    strBuilder.Append("</msl>");

                    strBuilder.Append("<pckMethod>");
                    strBuilder.Append(item.attributes.partDetail.pckMethod);
                    strBuilder.Append("</pckMethod>");

                    strBuilder.Append("<pckCd>");
                    strBuilder.Append(item.attributes.partDetail.pckCd);
                    strBuilder.Append("</pckCd>");



                }
                else
                {

                    strBuilder.Append("<msl>");
                    strBuilder.Append(" ");
                    strBuilder.Append("</msl>");

                    strBuilder.Append("<pckMethod>");
                    strBuilder.Append(" ");
                    strBuilder.Append("</pckMethod>");

                    strBuilder.Append("<pckCd>");
                    strBuilder.Append(" ");
                    strBuilder.Append("</pckCd>");

                }
                //Collections Price
                if (item.collections != null)
                {

                    strBuilder.Append("<source>");
                    strBuilder.Append(item.collections.prices[0].source);
                    strBuilder.Append("</source>");

                    strBuilder.Append("<price>");
                    strBuilder.Append(item.collections.prices[0].price);
                    strBuilder.Append("</price>");

                    strBuilder.Append("<currency>");
                    strBuilder.Append(item.collections.prices[0].currency);
                    strBuilder.Append("</currency>");

                    strBuilder.Append("<telephone>");
                    strBuilder.Append(item.collections.prices[0].telephone);
                    strBuilder.Append("</telephone>");

                    strBuilder.Append("<email>");
                    strBuilder.Append(item.collections.prices[0].email);
                    strBuilder.Append("</email>");

                    strBuilder.Append("<priceAvailabilityLink>");
                    strBuilder.Append(item.collections.prices[0].priceAvailabilityLink);
                    strBuilder.Append("</priceAvailabilityLink>");

                }
                else
                {
                    strBuilder.Append("<source>");
                    strBuilder.Append(" ");
                    strBuilder.Append("</source>");

                    strBuilder.Append("<price>");
                    strBuilder.Append("0");
                    strBuilder.Append("</price>");

                    strBuilder.Append("<currency>");
                    strBuilder.Append(" ");
                    strBuilder.Append("</currency>");

                    strBuilder.Append("<telephone>");
                    strBuilder.Append(" ");
                    strBuilder.Append("</telephone>");

                    strBuilder.Append("<email>");
                    strBuilder.Append(" ");
                    strBuilder.Append("</email>");

                    strBuilder.Append("<priceAvailabilityLink>");
                    strBuilder.Append(" ");
                    strBuilder.Append("</priceAvailabilityLink>");
                }
                strBuilder.Append("</IHSResult>");
            }





            strBuilder.Append("</IHSResults>");
            return strBuilder.ToString();
        }
    }
}