//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Resources {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option or rebuild the Visual Studio project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.VisualStudio.Web.Application.StronglyTypedResourceProxyBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class PageTitles {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal PageTitles() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Resources.PageTitles", global::System.Reflection.Assembly.Load("App_GlobalResources"));
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Accounts.
        /// </summary>
        internal static string Accounts {
            get {
                return ResourceManager.GetString("Accounts", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Received Customer RMAs.
        /// </summary>
        internal static string Accounts_ReceivedCustomerRMABrowse {
            get {
                return ResourceManager.GetString("Accounts_ReceivedCustomerRMABrowse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Received Customer RMA.
        /// </summary>
        internal static string Accounts_ReceivedCustomerRMADetail {
            get {
                return ResourceManager.GetString("Accounts_ReceivedCustomerRMADetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Received Purchase Orders.
        /// </summary>
        internal static string Accounts_ReceivedPurchaseOrderBrowse {
            get {
                return ResourceManager.GetString("Accounts_ReceivedPurchaseOrderBrowse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Received Purchase Order.
        /// </summary>
        internal static string Accounts_ReceivedPurchaseOrderDetail {
            get {
                return ResourceManager.GetString("Accounts_ReceivedPurchaseOrderDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to All Document.
        /// </summary>
        internal static string All_Document {
            get {
                return ResourceManager.GetString("All_Document", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to GI Line Image.
        /// </summary>
        internal static string All_GILineDocument {
            get {
                return ResourceManager.GetString("All_GILineDocument", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to IHS PDF Document Attached.
        /// </summary>
        internal static string All_IHSDocument {
            get {
                return ResourceManager.GetString("All_IHSDocument", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier Approval PDF Document Attached.
        /// </summary>
        internal static string All_SupplierApprovalDoc {
            get {
                return ResourceManager.GetString("All_SupplierApprovalDoc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier Approval Image Attached.
        /// </summary>
        internal static string All_SupplierApprovalImage {
            get {
                return ResourceManager.GetString("All_SupplierApprovalImage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Assign BOM.
        /// </summary>
        internal static string AssignBOM {
            get {
                return ResourceManager.GetString("AssignBOM", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to BOM Manager.
        /// </summary>
        internal static string BOMManager {
            get {
                return ResourceManager.GetString("BOMManager", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to BOM Manager Sourcing.
        /// </summary>
        internal static string BOMManagerSourcing {
            get {
                return ResourceManager.GetString("BOMManagerSourcing", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to BOMSearch.
        /// </summary>
        internal static string BOMSearch {
            get {
                return ResourceManager.GetString("BOMSearch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Assign BOM.
        /// </summary>
        internal static string BOMSearchAssign {
            get {
                return ResourceManager.GetString("BOMSearchAssign", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Client Exchange Rate.
        /// </summary>
        internal static string ClientExchangeRate {
            get {
                return ResourceManager.GetString("ClientExchangeRate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Certificate PDF Document.
        /// </summary>
        internal static string CompanyCertificatePDF {
            get {
                return ResourceManager.GetString("CompanyCertificatePDF", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Contact.
        /// </summary>
        internal static string Contact {
            get {
                return ResourceManager.GetString("Contact", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Company.
        /// </summary>
        internal static string Contact_CompanyAdd {
            get {
                return ResourceManager.GetString("Contact_CompanyAdd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Browse Companies.
        /// </summary>
        internal static string Contact_CompanyBrowse {
            get {
                return ResourceManager.GetString("Contact_CompanyBrowse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Company Detail.
        /// </summary>
        internal static string Contact_CompanyDetail {
            get {
                return ResourceManager.GetString("Contact_CompanyDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Contacts.
        /// </summary>
        internal static string Contact_ContactDetail {
            get {
                return ResourceManager.GetString("Contact_ContactDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Group Code.
        /// </summary>
        internal static string Contact_GroupCodeCompanyAdd {
            get {
                return ResourceManager.GetString("Contact_GroupCodeCompanyAdd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Manufacturer.
        /// </summary>
        internal static string Contact_ManufacturerAdd {
            get {
                return ResourceManager.GetString("Contact_ManufacturerAdd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Browse Manufacturers.
        /// </summary>
        internal static string Contact_ManufacturerBrowse {
            get {
                return ResourceManager.GetString("Contact_ManufacturerBrowse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Manufacturer Detail.
        /// </summary>
        internal static string Contact_ManufacturerDetail {
            get {
                return ResourceManager.GetString("Contact_ManufacturerDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add in Manufacturer Group Code.
        /// </summary>
        internal static string Contact_ManufacturerGroup {
            get {
                return ResourceManager.GetString("Contact_ManufacturerGroup", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to CSL Sanctioned.
        /// </summary>
        internal static string CSLSanctioned {
            get {
                return ResourceManager.GetString("CSLSanctioned", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to CSL Search.
        /// </summary>
        internal static string CSLsearch {
            get {
                return ResourceManager.GetString("CSLsearch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Import CSV File.
        /// </summary>
        internal static string CSV_Import {
            get {
                return ResourceManager.GetString("CSV_Import", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dashboards.
        /// </summary>
        internal static string Dashboards {
            get {
                return ResourceManager.GetString("Dashboards", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sales.
        /// </summary>
        internal static string Dashboards_SO {
            get {
                return ResourceManager.GetString("Dashboards_SO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sales Performance.
        /// </summary>
        internal static string Dashboards_Stock {
            get {
                return ResourceManager.GetString("Dashboards_Stock", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Root Cause Code Add.
        /// </summary>
        internal static string EightDCode_Add {
            get {
                return ResourceManager.GetString("EightDCode_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invoice Email Status.
        /// </summary>
        internal static string EmailStatus {
            get {
                return ResourceManager.GetString("EmailStatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to End User Undertaking PDF Document.
        /// </summary>
        internal static string EndUserUndetakingPDF {
            get {
                return ResourceManager.GetString("EndUserUndetakingPDF", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error.
        /// </summary>
        internal static string Error {
            get {
                return ResourceManager.GetString("Error", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to EU Sanctions Map.
        /// </summary>
        internal static string EUSanctionsMap {
            get {
                return ResourceManager.GetString("EUSanctionsMap", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Exchange Rate.
        /// </summary>
        internal static string ExchangeRate {
            get {
                return ResourceManager.GetString("ExchangeRate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Feedback.
        /// </summary>
        internal static string Feedback {
            get {
                return ResourceManager.GetString("Feedback", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Germany Exchange Rate.
        /// </summary>
        internal static string GermanyExchangeRate {
            get {
                return ResourceManager.GetString("GermanyExchangeRate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to GT.
        /// </summary>
        internal static string GT {
            get {
                return ResourceManager.GetString("GT", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Help.
        /// </summary>
        internal static string Help {
            get {
                return ResourceManager.GetString("Help", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Home.
        /// </summary>
        internal static string Home {
            get {
                return ResourceManager.GetString("Home", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Division KPI.
        /// </summary>
        internal static string KPI_DivisionDefault {
            get {
                return ResourceManager.GetString("KPI_DivisionDefault", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Division Detail.
        /// </summary>
        internal static string KPI_DivisionDetail {
            get {
                return ResourceManager.GetString("KPI_DivisionDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Division Edit.
        /// </summary>
        internal static string KPI_DivisionEdit {
            get {
                return ResourceManager.GetString("KPI_DivisionEdit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sales KPI.
        /// </summary>
        internal static string KPI_SalesDefault {
            get {
                return ResourceManager.GetString("KPI_SalesDefault", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sales Detail.
        /// </summary>
        internal static string KPI_SalesDetail {
            get {
                return ResourceManager.GetString("KPI_SalesDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sales Edit.
        /// </summary>
        internal static string KPI_SalesEdit {
            get {
                return ResourceManager.GetString("KPI_SalesEdit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Team KPI.
        /// </summary>
        internal static string KPI_TeamDefault {
            get {
                return ResourceManager.GetString("KPI_TeamDefault", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Team Detail.
        /// </summary>
        internal static string KPI_TeamDetail {
            get {
                return ResourceManager.GetString("KPI_TeamDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Team Edit.
        /// </summary>
        internal static string KPI_TeamEdit {
            get {
                return ResourceManager.GetString("KPI_TeamEdit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Logout.
        /// </summary>
        internal static string Logout {
            get {
                return ResourceManager.GetString("Logout", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Page Not Found.
        /// </summary>
        internal static string NotFound {
            get {
                return ResourceManager.GetString("NotFound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Orders.
        /// </summary>
        internal static string Orders {
            get {
                return ResourceManager.GetString("Orders", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Assign HUBRFQ.
        /// </summary>
        internal static string Orders_ATMIPOBOM {
            get {
                return ResourceManager.GetString("Orders_ATMIPOBOM", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New HUBRFQ.
        /// </summary>
        internal static string Orders_BOMAdd {
            get {
                return ResourceManager.GetString("Orders_BOMAdd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New HUBRFQ To This Company.
        /// </summary>
        internal static string Orders_BOMAddRequirement {
            get {
                return ResourceManager.GetString("Orders_BOMAddRequirement", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to HUBRFQ.
        /// </summary>
        internal static string Orders_BOMBrowse {
            get {
                return ResourceManager.GetString("Orders_BOMBrowse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to HUBRFQ.
        /// </summary>
        internal static string Orders_BOMDetail {
            get {
                return ResourceManager.GetString("Orders_BOMDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to BOM Import.
        /// </summary>
        internal static string Orders_BOMImport {
            get {
                return ResourceManager.GetString("Orders_BOMImport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New BOM.
        /// </summary>
        internal static string Orders_ClientBOMAdd {
            get {
                return ResourceManager.GetString("Orders_ClientBOMAdd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to BOM.
        /// </summary>
        internal static string Orders_ClientBOMDetail {
            get {
                return ResourceManager.GetString("Orders_ClientBOMDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Client Invoice.
        /// </summary>
        internal static string Orders_ClientInvoiceAdd {
            get {
                return ResourceManager.GetString("Orders_ClientInvoiceAdd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Client Invoices.
        /// </summary>
        internal static string Orders_ClientInvoiceBrowse {
            get {
                return ResourceManager.GetString("Orders_ClientInvoiceBrowse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bulk Print / Bulk Email.
        /// </summary>
        internal static string Orders_CreditBulkPrint {
            get {
                return ResourceManager.GetString("Orders_CreditBulkPrint", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Credit Note.
        /// </summary>
        internal static string Orders_CreditNoteAdd {
            get {
                return ResourceManager.GetString("Orders_CreditNoteAdd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Credit Notes.
        /// </summary>
        internal static string Orders_CreditNoteBrowse {
            get {
                return ResourceManager.GetString("Orders_CreditNoteBrowse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cross Match.
        /// </summary>
        internal static string Orders_CrossMatchURL {
            get {
                return ResourceManager.GetString("Orders_CrossMatchURL", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to BOM Import.
        /// </summary>
        internal static string Orders_CustomerReqImport {
            get {
                return ResourceManager.GetString("Orders_CustomerReqImport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Print Customer Requirements Enquiry Form.
        /// </summary>
        internal static string Orders_CustomerReqPrint {
            get {
                return ResourceManager.GetString("Orders_CustomerReqPrint", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Requirement .
        /// </summary>
        internal static string Orders_CustomerRequirementAdd {
            get {
                return ResourceManager.GetString("Orders_CustomerRequirementAdd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer Requirements.
        /// </summary>
        internal static string Orders_CustomerRequirementBrowse {
            get {
                return ResourceManager.GetString("Orders_CustomerRequirementBrowse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer Requirement.
        /// </summary>
        internal static string Orders_CustomerRequirementDetail {
            get {
                return ResourceManager.GetString("Orders_CustomerRequirementDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Requirement To This Company.
        /// </summary>
        internal static string Orders_CustomerRequirement_DetailAdd {
            get {
                return ResourceManager.GetString("Orders_CustomerRequirement_DetailAdd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Customer RMA.
        /// </summary>
        internal static string Orders_CustomerRMAAdd {
            get {
                return ResourceManager.GetString("Orders_CustomerRMAAdd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer RMAs.
        /// </summary>
        internal static string Orders_CustomerRMABrowse {
            get {
                return ResourceManager.GetString("Orders_CustomerRMABrowse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bulk Print / Bulk Email.
        /// </summary>
        internal static string Orders_DabitBulkPrint {
            get {
                return ResourceManager.GetString("Orders_DabitBulkPrint", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Debit Note.
        /// </summary>
        internal static string Orders_DebitNoteAdd {
            get {
                return ResourceManager.GetString("Orders_DebitNoteAdd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Debit Notes.
        /// </summary>
        internal static string Orders_DebitNoteBrowse {
            get {
                return ResourceManager.GetString("Orders_DebitNoteBrowse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Internal Purchase.
        /// </summary>
        internal static string Orders_InternalPurchaseOrderAdd {
            get {
                return ResourceManager.GetString("Orders_InternalPurchaseOrderAdd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Internal Purchase Order.
        /// </summary>
        internal static string Orders_InternalPurchaseOrderBrowse {
            get {
                return ResourceManager.GetString("Orders_InternalPurchaseOrderBrowse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Invoice.
        /// </summary>
        internal static string Orders_InvoiceAdd {
            get {
                return ResourceManager.GetString("Orders_InvoiceAdd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invoices.
        /// </summary>
        internal static string Orders_InvoiceBrowse {
            get {
                return ResourceManager.GetString("Orders_InvoiceBrowse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bulk Print / Bulk Email.
        /// </summary>
        internal static string Orders_InvoiceBulkPrint {
            get {
                return ResourceManager.GetString("Orders_InvoiceBulkPrint", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to OGEL Lines.
        /// </summary>
        internal static string Orders_OGELLinesExportBrowse {
            get {
                return ResourceManager.GetString("Orders_OGELLinesExportBrowse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Price Request.
        /// </summary>
        internal static string Orders_POQuoteAdd {
            get {
                return ResourceManager.GetString("Orders_POQuoteAdd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Price Request.
        /// </summary>
        internal static string Orders_POQuoteBrowse {
            get {
                return ResourceManager.GetString("Orders_POQuoteBrowse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Prospective Cross Selling Import.
        /// </summary>
        internal static string Orders_ProsCrossSellingImport {
            get {
                return ResourceManager.GetString("Orders_ProsCrossSellingImport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Prospective Cross Selling.
        /// </summary>
        internal static string Orders_ProspectiveCrossSelling {
            get {
                return ResourceManager.GetString("Orders_ProspectiveCrossSelling", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Purchase Order.
        /// </summary>
        internal static string Orders_PurchaseOrderAdd {
            get {
                return ResourceManager.GetString("Orders_PurchaseOrderAdd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Purchase Orders.
        /// </summary>
        internal static string Orders_PurchaseOrderBrowse {
            get {
                return ResourceManager.GetString("Orders_PurchaseOrderBrowse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Purchase Requisitions.
        /// </summary>
        internal static string Orders_PurchaseRequisitionBrowse {
            get {
                return ResourceManager.GetString("Orders_PurchaseRequisitionBrowse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Quote.
        /// </summary>
        internal static string Orders_QuoteAdd {
            get {
                return ResourceManager.GetString("Orders_QuoteAdd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quotes.
        /// </summary>
        internal static string Orders_QuoteBrowse {
            get {
                return ResourceManager.GetString("Orders_QuoteBrowse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Sales Order.
        /// </summary>
        internal static string Orders_SalesOrderAdd {
            get {
                return ResourceManager.GetString("Orders_SalesOrderAdd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sales Orders.
        /// </summary>
        internal static string Orders_SalesOrderBrowse {
            get {
                return ResourceManager.GetString("Orders_SalesOrderBrowse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sourcing.
        /// </summary>
        internal static string Orders_Sourcing {
            get {
                return ResourceManager.GetString("Orders_Sourcing", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Supplier RMA.
        /// </summary>
        internal static string Orders_SupplierRMAAdd {
            get {
                return ResourceManager.GetString("Orders_SupplierRMAAdd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier RMAs.
        /// </summary>
        internal static string Orders_SupplierRMABrowse {
            get {
                return ResourceManager.GetString("Orders_SupplierRMABrowse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bulk Print / Bulk Email.
        /// </summary>
        internal static string Ord_CreditBulkPrint {
            get {
                return ResourceManager.GetString("Ord_CreditBulkPrint", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Setup | Add Company.
        /// </summary>
        internal static string OverallSetup_AddCompany {
            get {
                return ResourceManager.GetString("OverallSetup_AddCompany", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Setup | Application Settings.
        /// </summary>
        internal static string OverallSetup_AppSettings {
            get {
                return ResourceManager.GetString("OverallSetup_AppSettings", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Setup | Database Settings.
        /// </summary>
        internal static string OverallSetup_DatabaseSettings {
            get {
                return ResourceManager.GetString("OverallSetup_DatabaseSettings", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Setup | Disable Company.
        /// </summary>
        internal static string OverallSetup_DisableCompany {
            get {
                return ResourceManager.GetString("OverallSetup_DisableCompany", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Setup | Email Settings.
        /// </summary>
        internal static string OverallSetup_EmailSettings {
            get {
                return ResourceManager.GetString("OverallSetup_EmailSettings", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Setup.
        /// </summary>
        internal static string OverallSetup_Home {
            get {
                return ResourceManager.GetString("OverallSetup_Home", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Initial Setup.
        /// </summary>
        internal static string OverallSetup_InitialSetup {
            get {
                return ResourceManager.GetString("OverallSetup_InitialSetup", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Setup | Sessions.
        /// </summary>
        internal static string OverallSetup_Sessions {
            get {
                return ResourceManager.GetString("OverallSetup_Sessions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Polish Exchange Rate.
        /// </summary>
        internal static string PolishExchangeRate {
            get {
                return ResourceManager.GetString("PolishExchangeRate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PowerBI Sales.
        /// </summary>
        internal static string Power_BI_Sales {
            get {
                return ResourceManager.GetString("Power_BI_Sales", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sales Dashboard.
        /// </summary>
        internal static string Power_BI_Saless {
            get {
                return ResourceManager.GetString("Power_BI_Saless", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Product.
        /// </summary>
        internal static string Product {
            get {
                return ResourceManager.GetString("Product", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Profile.
        /// </summary>
        internal static string Profile {
            get {
                return ResourceManager.GetString("Profile", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit My Profile.
        /// </summary>
        internal static string Profile_Edit {
            get {
                return ResourceManager.GetString("Profile_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mail Message Groups.
        /// </summary>
        internal static string Profile_MailMessageGroups {
            get {
                return ResourceManager.GetString("Profile_MailMessageGroups", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mail Messages.
        /// </summary>
        internal static string Profile_MailMessages {
            get {
                return ResourceManager.GetString("Profile_MailMessages", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to To Do List.
        /// </summary>
        internal static string Profile_ToDo {
            get {
                return ResourceManager.GetString("Profile_ToDo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to EPR Notify.
        /// </summary>
        internal static string PurchaseOrder_EPRNotify {
            get {
                return ResourceManager.GetString("PurchaseOrder_EPRNotify", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Rebound Global: Trader - Report NPR.
        /// </summary>
        internal static string ReportNPR {
            get {
                return ResourceManager.GetString("ReportNPR", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reports.
        /// </summary>
        internal static string Reports {
            get {
                return ResourceManager.GetString("Reports", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Report.
        /// </summary>
        internal static string Reports_ReportDetail {
            get {
                return ResourceManager.GetString("Reports_ReportDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sales Calculator.
        /// </summary>
        internal static string SalesCalc {
            get {
                return ResourceManager.GetString("SalesCalc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Setup.
        /// </summary>
        internal static string Setup {
            get {
                return ResourceManager.GetString("Setup", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Application Settings.
        /// </summary>
        internal static string Setup_CompanyDetails_ApplicationSettings {
            get {
                return ResourceManager.GetString("Setup_CompanyDetails_ApplicationSettings", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Client Invoice Header.
        /// </summary>
        internal static string Setup_CompanyDetails_ClientInvoiceHeader {
            get {
                return ResourceManager.GetString("Setup_CompanyDetails_ClientInvoiceHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Countries.
        /// </summary>
        internal static string Setup_CompanyDetails_Country {
            get {
                return ResourceManager.GetString("Setup_CompanyDetails_Country", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Currencies.
        /// </summary>
        internal static string Setup_CompanyDetails_Currency {
            get {
                return ResourceManager.GetString("Setup_CompanyDetails_Currency", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Divisions.
        /// </summary>
        internal static string Setup_CompanyDetails_Division {
            get {
                return ResourceManager.GetString("Setup_CompanyDetails_Division", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bulk Invoice Email Composer.
        /// </summary>
        internal static string Setup_CompanyDetails_EmailComposer {
            get {
                return ResourceManager.GetString("Setup_CompanyDetails_EmailComposer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Master Taxes.
        /// </summary>
        internal static string Setup_CompanyDetails_GlobalTax {
            get {
                return ResourceManager.GetString("Setup_CompanyDetails_GlobalTax", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Nice Label Path.
        /// </summary>
        internal static string Setup_CompanyDetails_LabelPath {
            get {
                return ResourceManager.GetString("Setup_CompanyDetails_LabelPath", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Local Currency.
        /// </summary>
        internal static string Setup_CompanyDetails_LocalCurrency {
            get {
                return ResourceManager.GetString("Setup_CompanyDetails_LocalCurrency", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mail Groups.
        /// </summary>
        internal static string Setup_CompanyDetails_MailGroups {
            get {
                return ResourceManager.GetString("Setup_CompanyDetails_MailGroups", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mail Groups.
        /// </summary>
        internal static string Setup_CompanyDetails_MailMessageGroups {
            get {
                return ResourceManager.GetString("Setup_CompanyDetails_MailMessageGroups", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to OGEL Licenses.
        /// </summary>
        internal static string Setup_CompanyDetails_OGELLicenses {
            get {
                return ResourceManager.GetString("Setup_CompanyDetails_OGELLicenses", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Printed Documents.
        /// </summary>
        internal static string Setup_CompanyDetails_PrintedDocuments {
            get {
                return ResourceManager.GetString("Setup_CompanyDetails_PrintedDocuments", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Printer.
        /// </summary>
        internal static string Setup_CompanyDetails_Printer {
            get {
                return ResourceManager.GetString("Setup_CompanyDetails_Printer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Products.
        /// </summary>
        internal static string Setup_CompanyDetails_Product {
            get {
                return ResourceManager.GetString("Setup_CompanyDetails_Product", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Restricted Manufacturer.
        /// </summary>
        internal static string Setup_CompanyDetails_RestrictedManufacture {
            get {
                return ResourceManager.GetString("Setup_CompanyDetails_RestrictedManufacture", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sequence Numbers.
        /// </summary>
        internal static string Setup_CompanyDetails_SequenceNumber {
            get {
                return ResourceManager.GetString("Setup_CompanyDetails_SequenceNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shipping Methods.
        /// </summary>
        internal static string Setup_CompanyDetails_ShippingMethod {
            get {
                return ResourceManager.GetString("Setup_CompanyDetails_ShippingMethod", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sourcing Links.
        /// </summary>
        internal static string Setup_CompanyDetails_SourcingLinks {
            get {
                return ResourceManager.GetString("Setup_CompanyDetails_SourcingLinks", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Stock Log Reasons.
        /// </summary>
        internal static string Setup_CompanyDetails_StockLogReason {
            get {
                return ResourceManager.GetString("Setup_CompanyDetails_StockLogReason", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Taxes.
        /// </summary>
        internal static string Setup_CompanyDetails_Tax {
            get {
                return ResourceManager.GetString("Setup_CompanyDetails_Tax", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Teams.
        /// </summary>
        internal static string Setup_CompanyDetails_Team {
            get {
                return ResourceManager.GetString("Setup_CompanyDetails_Team", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Terms.
        /// </summary>
        internal static string Setup_CompanyDetails_Terms {
            get {
                return ResourceManager.GetString("Setup_CompanyDetails_Terms", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Users.
        /// </summary>
        internal static string Setup_CompanyDetails_User {
            get {
                return ResourceManager.GetString("Setup_CompanyDetails_User", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Warehouses.
        /// </summary>
        internal static string Setup_CompanyDetails_Warehouse {
            get {
                return ResourceManager.GetString("Setup_CompanyDetails_Warehouse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Warnings.
        /// </summary>
        internal static string Setup_CompanyDetails_Warnings {
            get {
                return ResourceManager.GetString("Setup_CompanyDetails_Warnings", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ECCN.
        /// </summary>
        internal static string Setup_CompanySettings_ECCN {
            get {
                return ResourceManager.GetString("Setup_CompanySettings_ECCN", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Restricted Manufacturer.
        /// </summary>
        internal static string Setup_CompanySettings_RestrictedManufacture {
            get {
                return ResourceManager.GetString("Setup_CompanySettings_RestrictedManufacture", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Global Security Groups.
        /// </summary>
        internal static string Setup_GlobalSecurity_Groups {
            get {
                return ResourceManager.GetString("Setup_GlobalSecurity_Groups", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Application Settings.
        /// </summary>
        internal static string Setup_GlobalSettings_ApplicationSettings {
            get {
                return ResourceManager.GetString("Setup_GlobalSettings_ApplicationSettings", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to AS6081.
        /// </summary>
        internal static string Setup_GlobalSettings_AS6081 {
            get {
                return ResourceManager.GetString("Setup_GlobalSettings_AS6081", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Certificate.
        /// </summary>
        internal static string Setup_GlobalSettings_Certificate {
            get {
                return ResourceManager.GetString("Setup_GlobalSettings_Certificate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Client.
        /// </summary>
        internal static string Setup_GlobalSettings_Client {
            get {
                return ResourceManager.GetString("Setup_GlobalSettings_Client", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Communication Log Types.
        /// </summary>
        internal static string Setup_GlobalSettings_CommunicationLogType {
            get {
                return ResourceManager.GetString("Setup_GlobalSettings_CommunicationLogType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Company Types.
        /// </summary>
        internal static string Setup_GlobalSettings_CompanyType {
            get {
                return ResourceManager.GetString("Setup_GlobalSettings_CompanyType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Counting Methods.
        /// </summary>
        internal static string Setup_GlobalSettings_CountingMethod {
            get {
                return ResourceManager.GetString("Setup_GlobalSettings_CountingMethod", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Root Cause Code.
        /// </summary>
        internal static string Setup_GlobalSettings_EightDCode {
            get {
                return ResourceManager.GetString("Setup_GlobalSettings_EightDCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to EntertaimentType.
        /// </summary>
        internal static string Setup_GlobalSettings_EntertainmentType {
            get {
                return ResourceManager.GetString("Setup_GlobalSettings_EntertainmentType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to GT Update Notifications.
        /// </summary>
        internal static string Setup_GlobalSettings_GTUpdate {
            get {
                return ResourceManager.GetString("Setup_GlobalSettings_GTUpdate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Incoterms.
        /// </summary>
        internal static string Setup_GlobalSettings_Incoterm {
            get {
                return ResourceManager.GetString("Setup_GlobalSettings_Incoterm", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Industry Types.
        /// </summary>
        internal static string Setup_GlobalSettings_IndustryType {
            get {
                return ResourceManager.GetString("Setup_GlobalSettings_IndustryType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Client Invoice.
        /// </summary>
        internal static string Setup_GlobalSettings_InvoiceSetting {
            get {
                return ResourceManager.GetString("Setup_GlobalSettings_InvoiceSetting", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Master Country List.
        /// </summary>
        internal static string Setup_GlobalSettings_MasterCountryList {
            get {
                return ResourceManager.GetString("Setup_GlobalSettings_MasterCountryList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Master Currency List.
        /// </summary>
        internal static string Setup_GlobalSettings_MasterCurrencyList {
            get {
                return ResourceManager.GetString("Setup_GlobalSettings_MasterCurrencyList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Master Login.
        /// </summary>
        internal static string Setup_GlobalSettings_MasterLogin {
            get {
                return ResourceManager.GetString("Setup_GlobalSettings_MasterLogin", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Packages.
        /// </summary>
        internal static string Setup_GlobalSettings_Package {
            get {
                return ResourceManager.GetString("Setup_GlobalSettings_Package", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Document File Size.
        /// </summary>
        internal static string Setup_GlobalSettings_PDFDocumentFileSize {
            get {
                return ResourceManager.GetString("Setup_GlobalSettings_PDFDocumentFileSize", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PPV/ BOM Qualification.
        /// </summary>
        internal static string Setup_GlobalSettings_PPVBOMQualification {
            get {
                return ResourceManager.GetString("Setup_GlobalSettings_PPVBOMQualification", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Products.
        /// </summary>
        internal static string Setup_GlobalSettings_Product {
            get {
                return ResourceManager.GetString("Setup_GlobalSettings_Product", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Product Types.
        /// </summary>
        internal static string Setup_GlobalSettings_ProductType {
            get {
                return ResourceManager.GetString("Setup_GlobalSettings_ProductType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Close Reasons.
        /// </summary>
        internal static string Setup_GlobalSettings_Reason {
            get {
                return ResourceManager.GetString("Setup_GlobalSettings_Reason", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Salutations.
        /// </summary>
        internal static string Setup_GlobalSettings_Salutation {
            get {
                return ResourceManager.GetString("Setup_GlobalSettings_Salutation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Master Status.
        /// </summary>
        internal static string Setup_GlobalSettings_SetupList {
            get {
                return ResourceManager.GetString("Setup_GlobalSettings_SetupList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Star Rating.
        /// </summary>
        internal static string Setup_GlobalSettings_StarRating {
            get {
                return ResourceManager.GetString("Setup_GlobalSettings_StarRating", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to To Do List Type.
        /// </summary>
        internal static string Setup_GlobalSettings_ToDoListType {
            get {
                return ResourceManager.GetString("Setup_GlobalSettings_ToDoListType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mail Groups.
        /// </summary>
        internal static string Setup_Personal_MailMessageGroups {
            get {
                return ResourceManager.GetString("Setup_Personal_MailMessageGroups", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to My Profile.
        /// </summary>
        internal static string Setup_Personal_UserProfile {
            get {
                return ResourceManager.GetString("Setup_Personal_UserProfile", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Security Groups.
        /// </summary>
        internal static string Setup_Security_Groups {
            get {
                return ResourceManager.GetString("Setup_Security_Groups", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Security Users.
        /// </summary>
        internal static string Setup_Security_Users {
            get {
                return ResourceManager.GetString("Setup_Security_Users", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The UK Sanctions List.
        /// </summary>
        internal static string TheUKSanctionsList {
            get {
                return ResourceManager.GetString("TheUKSanctionsList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Utility.
        /// </summary>
        internal static string Utility {
            get {
                return ResourceManager.GetString("Utility", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Alternative Import.
        /// </summary>
        internal static string Utility_Alternative {
            get {
                return ResourceManager.GetString("Utility_Alternative", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Alternative Import.
        /// </summary>
        internal static string Utility_AlternativeImport {
            get {
                return ResourceManager.GetString("Utility_AlternativeImport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to BOM Import.
        /// </summary>
        internal static string Utility_BOM {
            get {
                return ResourceManager.GetString("Utility_BOM", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to BOM Import.
        /// </summary>
        internal static string Utility_BOMImport {
            get {
                return ResourceManager.GetString("Utility_BOMImport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to BOM Manager Import.
        /// </summary>
        internal static string Utility_BOMManager {
            get {
                return ResourceManager.GetString("Utility_BOMManager", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to BOM Manager Import.
        /// </summary>
        internal static string Utility_BOMManagerImport {
            get {
                return ResourceManager.GetString("Utility_BOMManagerImport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Strategic Offers Import Tool.
        /// </summary>
        internal static string Utility_HUBOffer {
            get {
                return ResourceManager.GetString("Utility_HUBOffer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Strategic Offers Import Tool.
        /// </summary>
        internal static string Utility_HUBOfferImport {
            get {
                return ResourceManager.GetString("Utility_HUBOfferImport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bulk Offer Scheduled Import.
        /// </summary>
        internal static string Utility_HUBOfferImportLarge {
            get {
                return ResourceManager.GetString("Utility_HUBOfferImportLarge", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Utility Log.
        /// </summary>
        internal static string Utility_Log {
            get {
                return ResourceManager.GetString("Utility_Log", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Utility Log.
        /// </summary>
        internal static string Utility_LogImport {
            get {
                return ResourceManager.GetString("Utility_LogImport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Offer Import.
        /// </summary>
        internal static string Utility_Offer {
            get {
                return ResourceManager.GetString("Utility_Offer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Offer Import.
        /// </summary>
        internal static string Utility_OfferImport {
            get {
                return ResourceManager.GetString("Utility_OfferImport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Price Quote Import.
        /// </summary>
        internal static string Utility_PriceQuoteImport {
            get {
                return ResourceManager.GetString("Utility_PriceQuoteImport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Prospective Offers Import.
        /// </summary>
        internal static string Utility_ProsOfferImport {
            get {
                return ResourceManager.GetString("Utility_ProsOfferImport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Prospective Offers.
        /// </summary>
        internal static string Utility_ProspectiveOffer {
            get {
                return ResourceManager.GetString("Utility_ProspectiveOffer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reverse Logistics Import Tool.
        /// </summary>
        internal static string Utility_ReverseLogisticsImport {
            get {
                return ResourceManager.GetString("Utility_ReverseLogisticsImport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Utility Reverse Logistics Offer Import.
        /// </summary>
        internal static string Utility_ReverseLogisticsOfferImport {
            get {
                return ResourceManager.GetString("Utility_ReverseLogisticsOfferImport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reverse Logistics Import Tool.
        /// </summary>
        internal static string Utility_RLImport {
            get {
                return ResourceManager.GetString("Utility_RLImport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Stock Import.
        /// </summary>
        internal static string Utility_Stock {
            get {
                return ResourceManager.GetString("Utility_Stock", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Stock Import.
        /// </summary>
        internal static string Utility_StockImport {
            get {
                return ResourceManager.GetString("Utility_StockImport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to XMatch Utility.
        /// </summary>
        internal static string Utility_XMatch {
            get {
                return ResourceManager.GetString("Utility_XMatch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Warehouse.
        /// </summary>
        internal static string Warehouse {
            get {
                return ResourceManager.GetString("Warehouse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Goods In Line Notify.
        /// </summary>
        internal static string Warehouse_GILineNotify {
            get {
                return ResourceManager.GetString("Warehouse_GILineNotify", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Goods In Note.
        /// </summary>
        internal static string Warehouse_GoodsInAdd {
            get {
                return ResourceManager.GetString("Warehouse_GoodsInAdd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Goods In.
        /// </summary>
        internal static string Warehouse_GoodsInBrowse {
            get {
                return ResourceManager.GetString("Warehouse_GoodsInBrowse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Part Information.
        /// </summary>
        internal static string Warehouse_IHSCatalogueAdd {
            get {
                return ResourceManager.GetString("Warehouse_IHSCatalogueAdd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Product Catalogue.
        /// </summary>
        internal static string Warehouse_IHSCatalogueBrowse {
            get {
                return ResourceManager.GetString("Warehouse_IHSCatalogueBrowse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Lot.
        /// </summary>
        internal static string Warehouse_LotsAdd {
            get {
                return ResourceManager.GetString("Warehouse_LotsAdd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lots.
        /// </summary>
        internal static string Warehouse_LotsBrowse {
            get {
                return ResourceManager.GetString("Warehouse_LotsBrowse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lot.
        /// </summary>
        internal static string Warehouse_LotsDetail {
            get {
                return ResourceManager.GetString("Warehouse_LotsDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to NPR.
        /// </summary>
        internal static string Warehouse_Npr {
            get {
                return ResourceManager.GetString("Warehouse_Npr", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to NPR Notify.
        /// </summary>
        internal static string Warehouse_NPRNotify {
            get {
                return ResourceManager.GetString("Warehouse_NPRNotify", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Receive Customer RMAs.
        /// </summary>
        internal static string Warehouse_ReceiveCustomerRMABrowse {
            get {
                return ResourceManager.GetString("Warehouse_ReceiveCustomerRMABrowse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Receive Customer RMA.
        /// </summary>
        internal static string Warehouse_ReceiveCustomerRMADetail {
            get {
                return ResourceManager.GetString("Warehouse_ReceiveCustomerRMADetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Receive Purchase Orders.
        /// </summary>
        internal static string Warehouse_ReceivePurchaseOrderBrowse {
            get {
                return ResourceManager.GetString("Warehouse_ReceivePurchaseOrderBrowse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Receive Purchase Order.
        /// </summary>
        internal static string Warehouse_ReceivePurchaseOrderDetail {
            get {
                return ResourceManager.GetString("Warehouse_ReceivePurchaseOrderDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Service.
        /// </summary>
        internal static string Warehouse_ServicesAdd {
            get {
                return ResourceManager.GetString("Warehouse_ServicesAdd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Services.
        /// </summary>
        internal static string Warehouse_ServicesBrowse {
            get {
                return ResourceManager.GetString("Warehouse_ServicesBrowse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Service.
        /// </summary>
        internal static string Warehouse_ServicesDetail {
            get {
                return ResourceManager.GetString("Warehouse_ServicesDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ship Sales Orders.
        /// </summary>
        internal static string Warehouse_ShipSalesOrderBrowse {
            get {
                return ResourceManager.GetString("Warehouse_ShipSalesOrderBrowse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ship Sales Order.
        /// </summary>
        internal static string Warehouse_ShipSalesOrderDetail {
            get {
                return ResourceManager.GetString("Warehouse_ShipSalesOrderDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ship Supplier RMAs.
        /// </summary>
        internal static string Warehouse_ShipSupplierRMABrowse {
            get {
                return ResourceManager.GetString("Warehouse_ShipSupplierRMABrowse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ship Supplier RMA.
        /// </summary>
        internal static string Warehouse_ShipSupplierRMADetail {
            get {
                return ResourceManager.GetString("Warehouse_ShipSupplierRMADetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Short Shipment.
        /// </summary>
        internal static string Warehouse_ShortShipment {
            get {
                return ResourceManager.GetString("Warehouse_ShortShipment", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Short Shipment Details.
        /// </summary>
        internal static string Warehouse_ShortShipmentDetails {
            get {
                return ResourceManager.GetString("Warehouse_ShortShipmentDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Short Shipment Notify.
        /// </summary>
        internal static string Warehouse_ShortShipmentNotify {
            get {
                return ResourceManager.GetString("Warehouse_ShortShipmentNotify", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Stock Item.
        /// </summary>
        internal static string Warehouse_StockAdd {
            get {
                return ResourceManager.GetString("Warehouse_StockAdd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Stock.
        /// </summary>
        internal static string Warehouse_StockBrowse {
            get {
                return ResourceManager.GetString("Warehouse_StockBrowse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Stock Item.
        /// </summary>
        internal static string Warehouse_StockDetail {
            get {
                return ResourceManager.GetString("Warehouse_StockDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Supplier Invoice.
        /// </summary>
        internal static string Warehouse_SupplierInvoiceAdd {
            get {
                return ResourceManager.GetString("Warehouse_SupplierInvoiceAdd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier Invoice.
        /// </summary>
        internal static string Warehouse_SupplierInvoiceBrowse {
            get {
                return ResourceManager.GetString("Warehouse_SupplierInvoiceBrowse", resourceCulture);
            }
        }
    }
}
