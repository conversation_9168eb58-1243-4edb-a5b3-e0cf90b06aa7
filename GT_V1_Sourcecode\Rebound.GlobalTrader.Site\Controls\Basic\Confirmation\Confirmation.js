Type.registerNamespace("Rebound.GlobalTrader.Site.Controls");Rebound.GlobalTrader.Site.Controls.Confirmation=function(n){Rebound.GlobalTrader.Site.Controls.Confirmation.initializeBase(this,[n]);this._dummy=null};Rebound.GlobalTrader.Site.Controls.Confirmation.prototype={get_ibtnYes:function(){return this._ibtnYes},set_ibtnYes:function(n){this._ibtnYes!==n&&(this._ibtnYes=n)},get_ibtnNo:function(){return this._ibtnNo},set_ibtnNo:function(n){this._ibtnNo!==n&&(this._ibtnNo=n)},get_dummy:function(){return this._dummy},set_dummy:function(n){this._dummy!==n&&(this._dummy=n)},addClickYesEvent:function(n){this.get_events().addHandler("ClickYes",n)},removeClickYesEvent:function(n){this.get_events().removeHandler("ClickYes",n)},onClickYes:function(){var n=this.get_events().getHandler("ClickYes");n&&n(this,Sys.EventArgs.Empty)},addClickNoEvent:function(n){this.get_events().addHandler("ClickNo",n)},removeClickNoEvent:function(n){this.get_events().removeHandler("ClickNo",n)},onClickNo:function(){var n=this.get_events().getHandler("ClickNo");n&&n(this,Sys.EventArgs.Empty)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Confirmation.callBaseMethod(this,"initialize");$R_IBTN.addClick(this._ibtnYes,Function.createDelegate(this,this.onClickYes));$R_IBTN.addClick(this._ibtnNo,Function.createDelegate(this,this.onClickNo))},dispose:function(){this.isDisposed||(this.get_element()&&$clearHandlers(this.get_element()),this._ibtnYes&&$R_IBTN.clearHandlers(this._ibtnYes),this._ibtnNo&&$R_IBTN.clearHandlers(this._ibtnNo),this._ibtnYes=null,this._ibtnNo=null,Rebound.GlobalTrader.Site.Controls.Confirmation.callBaseMethod(this,"dispose"),this.isDisposed=!0)}};Rebound.GlobalTrader.Site.Controls.Confirmation.registerClass("Rebound.GlobalTrader.Site.Controls.Confirmation",Sys.UI.Control,Sys.IDisposable);