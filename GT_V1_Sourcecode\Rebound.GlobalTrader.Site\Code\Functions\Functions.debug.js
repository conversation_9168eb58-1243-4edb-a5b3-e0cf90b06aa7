///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />


///<reference name="Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataList" assembly="Rebound.GlobalTrader.Site" />
//-----------------------------------------------------------------------------------------
// RP 08.02.2010:
// - fix scrolling page to an element correctly in all browsers
//
//
//hello
//
// RP 02.02.2010:
// - use setCleanTextValue in createNubButton
//
// RP 27.01.2010:
// - add writeSupplierName function
//
// RP 14.01.2010:
// - improve null checking in setCleanTextValue function
//
// RP 10.11.2009:
// - found efficiences in arrayToSingleString
//
// RP 06.11.2009:
// - added splitDoubleCellValue
//
// RP 21.10.2009:
// - fix formatCurrency to remove all comma thousand separators
//
// RP 14.10.2009:
// - added setTextFilterFieldValueAndSearchType
// - added preventEnterKeyForControl to stop filter checkboxes posting the page
/*
Marker     changed by      date         Remarks
[001]      Vikas Kumar     23/12/20011  ESMS Ref:18 - Qty on Order should be different style as to highlight it.
[002]      Vinay           20/07/2012   Display invoice email status report
[003]      Vinay           26/12/2012   Supplier code should be right align
[004]      Abhinav Goyal   26/06/2013   Sales Calculator  
[005]      Vinay           29/08/2013   NPR Report
[006]      Vinay           04/02/2014   CR:- Add AS9120 Requirement in GT application
[007]      Vinay          26/03/2014    ESMS Ref:105 -  Add two more rohs option 
[008]      Vinay          24/04/2015    ESMS Ticket Number: 	228 
[009]      Vinay           09/04/2018   [REB-11304]: CHG-570795 Hazarders product type
[010]      Aashu Singh    13-Aug-2018   REB-12161:Credit Card Payments
[011]      Aashu Singh    27-Sep-2018   REB-13083 Change request PO - delivery status
[012]      Umendra Gupta  16-Jan-2019   Add View Tree Button.
[013]      Anand Gupta    28-Nov-2019   Add link in Top Menu Get Global Product Search
[014]      Anand Gupta     17/08/2021   (GTDP-198) Controlling specific product groups for purchasing
[015]      Anand Gupta     21/08/2021    IHS PDF view
[016]      Anand Gupta     19/09/2021    show part statu defination
[017]      Anand Gupta     12/01/2022   (GTDP-240)  Add function to add warning message to show on all screens REQ - Quote - SO - PO related to ECCN field
[018]      Anand Gupta     09/05/2022   (GTDP-299)  Add an option to select product Group from the drop down Warning Name field in warning messages and add new
[019]      Ravi Bhushan    18-09-2023   RP-2226  AS6081 - At several pages, we have to highlight text background color for that creating a common function
[RP-2713]    Ravi Bhushan    06-12-2023   URL for sales dashboard in GT
*/
//-----------------------
//------------
//Code Merge for GI Screen
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site");

Rebound.GlobalTrader.Site.Functions = function () {
    Rebound.GlobalTrader.Site.Functions.initializeBase(this);
};

var $R_FN = Rebound.GlobalTrader.Site.Functions;

Rebound.GlobalTrader.Site.Functions.setInnerHTML = function (el, strValue) {
    if (!el || typeof (strValue) == "undefined") return;
    if (strValue == null) strValue = "";
    el.innerHTML = strValue.toString().trim();
};

Rebound.GlobalTrader.Site.Functions.addOptionToListBox = function (lbx, strText, strValue, blnSelected) {
    if (!lbx) return;
    lbx.options.add(new Option(strText, strValue, blnSelected));
};

Rebound.GlobalTrader.Site.Functions.clearListBox = function (lbx) {
    if (!lbx) return;
    for (var i = lbx.options.length - 1; i >= 0; i--) {
        lbx.remove(i);
    }
};

Rebound.GlobalTrader.Site.Functions.doesListBoxHaveSelection = function (lbx) {
    if (!lbx) return false;
    var bln = false;
    for (var i = lbx.options.length - 1; i >= 0; i--) {
        if (lbx.options[i].selected) {
            bln = true;
            break;
        }
    }
    return bln;
};

Rebound.GlobalTrader.Site.Functions.updateAnchor = function (hyp, strText, strHref) {
    if (!hyp) return;
    if (strText) hyp.innerHTML = strText;
    if (strHref) htp.href = strHref;
};

Rebound.GlobalTrader.Site.Functions.showElement = function (el, blnShow, strReplacementClass) {
    if (!el) return;
    if (blnShow) {
        Sys.UI.DomElement.removeCssClass(el, "invisible");
        if (strReplacementClass) Sys.UI.DomElement.addCssClass(el, strReplacementClass);
    } else {
        Sys.UI.DomElement.addCssClass(el, "invisible");
        if (strReplacementClass) Sys.UI.DomElement.removeCssClass(el, strReplacementClass);
    }
};

Rebound.GlobalTrader.Site.Functions.isElementVisible = function (el) {
    if (!el) return false;
    return (!Sys.UI.DomElement.containsCssClass(el, "invisible"));
};

Rebound.GlobalTrader.Site.Functions.showElementIfVisible = function (el, blnShow, strReplacementClass) {
    if (!el) return;
    if (!Sys.UI.DomElement.containsCssClass(el, "invisible")) $R_FN.showElement(el, blnShow, strReplacementClass);
};

Rebound.GlobalTrader.Site.Functions.toggleCssClass = function (el, strOriginalClass, strReplacementClass, blnSwitchThemOver) {
    if (!el || !strOriginalClass || !strReplacementClass) return;
    if (blnSwitchThemOver) {
        var strTemp = strOriginalClass;
        strOriginalClass = strReplacementClass;
        strReplacementClass = strTemp;
        strTemp = null;
    }
    Sys.UI.DomElement.removeCssClass(el, strOriginalClass);
    Sys.UI.DomElement.addCssClass(el, strReplacementClass);
};

Rebound.GlobalTrader.Site.Functions.isEntered = function (strText) {
    if (!strText) return false;
    strText = strText.trim();
    return strText.length > 0;
};

Rebound.GlobalTrader.Site.Functions.isValidEmail = function (strEmail) {
    if (!strEmail) return true;
    if (strEmail.trim().length == 0) return true;
    var regEmail = /^([\w]+)(.[\w]+)*@([\w]+)(.[\w]{2,3}){1,2}$/;
    return regEmail.test(strEmail);
};

Rebound.GlobalTrader.Site.Functions.isValidURL = function (strURL) {
    if (!strURL) return true;
    if (strURL.trim().length == 0) return true;
    var regURL = /(ftp|http|https):\/\/(\w+:{0,1}\w*@)?(\S+)(:[0-9]+)?(\/|\/([\w#!:.?+=&%@!\-\/]))?/;
    //: /^(http:\/\/[\w].|https:\/\/[\w].|[\w].){1}([\w]+)(.[\w]+){1,2}$/;
    return regURL.test(strURL);
};

Rebound.GlobalTrader.Site.Functions.formatEmail = function (strEmail) {
    if (!strEmail) return "";
    if (strEmail.trim().length == 0) return "";
    strEmail = strEmail.trim();
    if (!strEmail.startsWith('mailto:')) return String.format('mailto:{0}', strEmail);
    return strEmail;
};

Rebound.GlobalTrader.Site.Functions.formatURL = function (strURL) {
    if (!strURL) return "";
    if (strURL.trim().length == 0) return "";
    strURL = strURL.trim();
    if (!strURL.startsWith('http')) return String.format('http://{0}', strURL);
    return strURL;
};
Rebound.GlobalTrader.Site.Functions.restrictStringTo30Characters = function (inputString) {
    if (inputString.length > 30) {
        return inputString.substring(0, 30) + " ..."; // Add ellipsis for indication
    }
    return inputString;
};

Rebound.GlobalTrader.Site.Functions.fadeElement = function (el, blnShow, intLo, intHi, strCallback) {
    if (!el) return;
    if (!intLo) intLo = 0;
    if (!intHi) intHi = 1;
    var anims = new Array();
    if (blnShow) {
        Array.add(anims, new AjaxControlToolkit.Animation.ScriptAction(el, 0, 0, String.format("$R_FN.showElement($get('{0}'), true);", el.id)));
        Array.add(anims, new AjaxControlToolkit.Animation.FadeInAnimation(el, $R_ELEMENT_FADE_TIME, $R_ANIMATE_FPS, intLo, intHi));
    } else {
        Array.add(anims, new AjaxControlToolkit.Animation.FadeOutAnimation(el, $R_ELEMENT_FADE_TIME, $R_ANIMATE_FPS, intLo, intHi));
        Array.add(anims, new AjaxControlToolkit.Animation.ScriptAction(el, 0, 0, String.format("$R_FN.showElement($get('{0}'), false);", el.id)));
    }
    if (strCallback) Array.add(anims, new AjaxControlToolkit.Animation.ScriptAction(el, 0, 0, strCallback));
    AjaxControlToolkit.Animation.SequenceAnimation.play(el, 0, 0, anims, 1);
};

Rebound.GlobalTrader.Site.Functions.setElementOpacity = function (el, intOpacity) {
    if (!el) return;
    if (!intOpacity) intOpacity = 100;
    el.style.opacity = intOpacity / 100;
    el.style.filter = 'alpha(opacity=' + intOpacity + ')';
};

Rebound.GlobalTrader.Site.Functions.showModalBG = function (blnShow) {
    var modal = $get('modalBG');
    $R_FN.showElement(modal, blnShow);
    //$R_FN.fadeElement(modal, blnShow, 0, 0.75);
};

Rebound.GlobalTrader.Site.Functions.getNumericalComparator = function (varValue) {
    var strComparator = "=";
    switch (Number.parseInvariant(varValue.toString())) {
        case $R_ENUM$NumericalComparisonType.EqualTo: strComparator = "="; break;
        case $R_ENUM$NumericalComparisonType.GreaterThan: strComparator = ">"; break;
        case $R_ENUM$NumericalComparisonType.LessThan: strComparator = "<"; break;
        case $R_ENUM$NumericalComparisonType.GreaterThanOrEqualTo: strComparator = ">="; break;
        case $R_ENUM$NumericalComparisonType.LessThanOrEqualTo: strComparator = "<="; break;
    }
    return strComparator;
};

Rebound.GlobalTrader.Site.Functions.setWidthFromOneToAnother = function (el1, el2, intOffset) {
    if (!el1 || !el2) return;
    if (!intOffset) intOffset = 0;
    var intW = Sys.UI.DomElement.getBounds(el1).width + intOffset;
    if (intW >= 0 && el2.style) el2.style.width = String.format("{0}px", intW);
};

Rebound.GlobalTrader.Site.Functions.setWidth = function (el, intW) {
    if (!el) return;
    if (!intW) intW = 0;
    if (intW >= 0 && el.style) el.style.width = String.format("{0}px", intW);
};

Rebound.GlobalTrader.Site.Functions.checkNumeric = function (txt) {
    if (!txt) return false;
    if (txt.value.trim() == "") return true;
    return !isNaN(Number.parseLocale(txt.value).toString());
};

Rebound.GlobalTrader.Site.Functions.setToUpperCaseAfterKeyUp = function (txt) {
    if (!txt) return;
    var intPos = $R_FN.getCaretPosition(txt);
    txt.value = txt.value.toUpperCase();
    $R_FN.setCaretPosition(txt, intPos);
};

Rebound.GlobalTrader.Site.Functions.setCaretPosition = function (el, intPos) {
    if (el == null) return;
    if (el.createTextRange) {
        var range = el.createTextRange();
        range.move('character', intPos);
        range.select();
    } else {
        if (el.selectionStart) {
            el.focus();
            el.setSelectionRange(intPos, intPos);
        } else {
            el.focus();
        }
    }
};

Rebound.GlobalTrader.Site.Functions.getCaretPosition = function (el) {
    if (el == null) return;
    var intPos = 0;
    if (document.selection) {
        // IE Support
        el.focus();
        var Sel = document.selection.createRange();
        var SelLength = document.selection.createRange().text.length;
        Sel.moveStart('character', -el.value.length);
        intPos = Sel.text.length - SelLength;
    } else if (el.selectionStart || el.selectionStart == 0) {
        // Firefox support
        intPos = el.selectionStart;
    }
    return (intPos);
};

Rebound.GlobalTrader.Site.Functions.clearAllSelections = function () {
    if (document.selection) document.selection.clear();
    if (window.getSelection) {
        var sel = window.getSelection();
        sel.removeAllRanges();
    }
};

Rebound.GlobalTrader.Site.Functions.createNubButton = function (strHref, strText, strAlign, blnToolTip, strTooltipContent, strTooltipFunction, strTooltipFunctionParametersArray, intTooltipWidth) {
    var strOut = "&nbsp;";
    if (typeof (strText) != "undefined") {
        if (strText.toString().length > 0) {
            if (!strHref) strHref = "";
            if (strHref == "") strHref = "javascript:void(0);";
            if (!strAlign) strAlign = "Left";
            var strClass = "nubButton";
            if (strAlign != "") strClass += " nubButtonAlign" + strAlign;
            strText = $R_FN.setCleanTextValue(strText);

            //create tooltip mouseover?
            var strToolTip = "";
            if (blnToolTip) {
                if (strTooltipContent) {
                    if (typeof (strTooltipContent).toString().toLowerCase() == "string") strTooltipContent = "\"" + strTooltipContent + "\"";
                } else {
                    strTooltipContent = "null";
                }
                strToolTip = String.format(" onmouseover=\"$R_PAGE._ctlToolTip.registerDynamicElement(this, {0}, {1}, {2}, {3});\"", strTooltipContent, strTooltipFunction, strTooltipFunctionParametersArray, (intTooltipWidth) ? intTooltipWidth : "null");
            }
            strOut = String.format("<a href=\"{0}\" class=\"{1}\"{2}>{3}</a>", strHref, strClass, strToolTip, strText);
        }
    }
    return strOut;
};

Rebound.GlobalTrader.Site.Functions.createNubButtonCustomClass = function (strHref, strText,strCusClass, strAlign, blnToolTip, strTooltipContent, strTooltipFunction, strTooltipFunctionParametersArray, intTooltipWidth) {
    var strOut = "&nbsp;";
    if (typeof (strText) != "undefined") {
        if (strText.toString().length > 0) {
            if (!strHref) strHref = "";
            if (strHref == "") strHref = "javascript:void(0);";
            if (!strAlign) strAlign = "Left";
            var strClass = "nubButton";
            if (strAlign != "") strClass += " nubButtonAlign" + strAlign;
            if (strCusClass != "") strClass += " " + strCusClass;
            strText = $R_FN.setCleanTextValue(strText);

            //create tooltip mouseover?
            var strToolTip = "";
            if (blnToolTip) {
                if (strTooltipContent) {
                    if (typeof (strTooltipContent).toString().toLowerCase() == "string") strTooltipContent = "\"" + strTooltipContent + "\"";
                } else {
                    strTooltipContent = "null";
                }
                strToolTip = String.format(" onmouseover=\"$R_PAGE._ctlToolTip.registerDynamicElement(this, {0}, {1}, {2}, {3});\"", strTooltipContent, strTooltipFunction, strTooltipFunctionParametersArray, (intTooltipWidth) ? intTooltipWidth : "null");
            }
            strOut = String.format("<a href=\"{0}\" class=\"{1}\"{2}>{3}</a>", strHref, strClass, strToolTip, strText);
        }
    }
    return strOut;
};

Rebound.GlobalTrader.Site.Functions.createImageCheckBox = function (bln) {
    var str = "<div class=\"imageCheckBoxDisabled\">";
    str += String.format("<img style=\"border-width: 0px;\" src=\"images/x.gif\" class=\"{0}\" />", bln ? "on" : "off");
    str += "</div>";
    return str;
};

Rebound.GlobalTrader.Site.Functions.createStarRating = function (intStars) {
    if (!intStars) intStars = "0";
    intStars = Number.parseInvariant(intStars.toString());
    var str = '<span class="starsOuter starsOuterReadOnly">';
    for (var i = 0; i < 5; i++) {
        str += String.format('<span class="stars {0}">', intStars > i ? "starsSaved" : "starsEmpty");
        str += '<img style="border-width: 0px; height: 15px; width: 13px;" src="images/x.gif"/>';
        str += '</span>';
    }
    str += '</span>';
    return str;
};

Rebound.GlobalTrader.Site.Functions.showRedText = function (strValue) {
    if (!strValue) return "";
    var str = '<span style="color:red">' + strValue + '</span> '
    return str;
};
//----
Rebound.GlobalTrader.Site.Functions.showRedBoldText = function (strValue) {
    if (!strValue) return "";
    var str = '<span style="color:red;font-weight:bold;">' + strValue + '</span> '
    return str;
};
Rebound.GlobalTrader.Site.Functions.showBoldText = function (strValue) {
    if (!strValue) return "";
    var str = '<span style="font-weight:bold;">' + strValue + '</span> '
    return str;
};
//--
Rebound.GlobalTrader.Site.Functions.arrayToSingleString = function (ary, strSep) {
    if (!ary) return "";
    if (!strSep) strSep = "||";
    return ary.join(strSep);
};

Rebound.GlobalTrader.Site.Functions.singleStringToArray = function (strIn, strSep) {
    if (!strIn) return [];
    if (strIn.trim().length == 0) return [];
    if (!strSep) strSep = "||";
    return strIn.split(strSep);
};

Rebound.GlobalTrader.Site.Functions.getTimestamp = function () {
    var dtm = new Date();
    return dtm.localeFormat("F");
};

Rebound.GlobalTrader.Site.Functions.shortDate = function (varDate) {
    var dtm = (varDate) ? new Date(Date.parseLocale(varDate.toString())) : new Date();
    return dtm.localeFormat("d");
};

Rebound.GlobalTrader.Site.Functions.shortDateAndTime = function (varDate) {
    var dtm = (varDate) ? new Date(Date.parseLocale(varDate.toString())) : new Date();
    return String.format("{0} {1}", dtm.localeFormat(Sys.CultureInfo.CurrentCulture.dateTimeFormat.ShortDatePattern), dtm.localeFormat(Sys.CultureInfo.CurrentCulture.dateTimeFormat.ShortTimePattern));
};

Rebound.GlobalTrader.Site.Functions.shortYear = function (input) {
    if (input && input.length > 0) {
        var datePart = input.match(/\d+/g),
            year = datePart[2].substring(2), // get only two digits
            month = datePart[1],
            day = datePart[0];
        return day + '/' + month + '/' + year;
    }
    else
        return "";
};

Rebound.GlobalTrader.Site.Functions.firstDayOfMonth = function (varDate) {
    var dtm = (varDate) ? new Date(Date.parseLocale(varDate.toString())) : new Date();
    dtm.setDate(1);
    return dtm.localeFormat(Sys.CultureInfo.CurrentCulture.dateTimeFormat.ShortDatePattern);
};

Rebound.GlobalTrader.Site.Functions.lastDayOfMonth = function (varDate) {
    var dtm = (varDate) ? new Date(Date.parseLocale(varDate.toString())) : new Date();
    dtm.setMonth(dtm.getMonth() + 1);
    dtm.setDate(1);
    dtm.setDate(dtm.getDate() - 1);
    return dtm.localeFormat(Sys.CultureInfo.CurrentCulture.dateTimeFormat.ShortDatePattern);
};

Rebound.GlobalTrader.Site.Functions.oneWeekAgo = function (varDate) {
    var dtm = (varDate) ? new Date(varDate) : new Date();
    dtm.setDate(dtm.getDate() - 7);
    return dtm.localeFormat(Sys.CultureInfo.CurrentCulture.dateTimeFormat.ShortDatePattern);
};

Rebound.GlobalTrader.Site.Functions.yesterday = function (varDate) {
    var dtm = (varDate) ? new Date(varDate) : new Date();
    dtm.setDate(dtm.getDate() - 1);
    return dtm.localeFormat(Sys.CultureInfo.CurrentCulture.dateTimeFormat.ShortDatePattern);
};

Rebound.GlobalTrader.Site.Functions.getCleanTextValue = function (strIn) {
    strIn = (strIn + "").trim();
    strIn = strIn.replace(/[+]/g, ":PLUS:");
    //strIn = strIn.replace(/.]/g, ":QUOTE:");
    strIn = strIn.replace(/[&]/g, ":AND:");
    return strIn;
};

Rebound.GlobalTrader.Site.Functions.setCleanTextValue = function (strIn, blnReplaceLineBreaks) {
    if (typeof (strIn) == "undefined") strIn = "";
    strIn = (strIn + "").trim();
    strIn = strIn.replace(/(:PLUS:)/g, "+");
    strIn = strIn.replace(/(:QUOTE:)/g, '"');
    strIn = strIn.replace(/((:AND:)|(&amp;))/g, "&");
    if (blnReplaceLineBreaks) strIn = strIn.replace(/(\n)/g, "<br />");
    return strIn;
};

Rebound.GlobalTrader.Site.Functions.setCleanTextURL = function (strIn, blnReplaceLineBreaks) {
    if (typeof (strIn) == "undefined") strIn = "";
    strIn = (strIn + "").trim();
    strIn = strIn.replace(/(:PLUS:)/g, "+");
    strIn = strIn.replace(/(:AND:)/g, "&");
    strIn = strIn.replace(/[+]/g, "%2B");

    return strIn;
};

Rebound.GlobalTrader.Site.Functions.setCleanTextValueForBackSlash = function (strIn, blnReplaceLineBreaks) {
    if (typeof (strIn) == "undefined") strIn = "";
    strIn = (strIn + "").trim();
    strIn = strIn.replace(/(:PLUS:)/g, "+");
    strIn = strIn.replace(/(:QUOTE:)/g, '"');
    strIn = strIn.replace(/((:AND:)|(&amp;))/g, "&");
    strIn = strIn.replace(/(:BACKSLASH:)/g, "\\");
    if (blnReplaceLineBreaks) strIn = strIn.replace(/(\n)/g, "<br />");
    return strIn;
};

Rebound.GlobalTrader.Site.Functions.replaceBRTags = function (strIn) {
    if (!strIn) strIn = "";
    strIn = strIn.replace(/((<br \/>)|(<br>)|(<br\/>))/gi, "\r\n");
    return strIn;
};

Rebound.GlobalTrader.Site.Functions.getDateFromDateAndTime = function (strDate, strTime) {
    var dtm = Date.parseLocale(strDate);
    var aryTime = strTime.split(":");
    dtm.setHours(aryTime[0]);
    dtm.setMinutes(aryTime[1]);
    return dtm;
};

Rebound.GlobalTrader.Site.Functions.parseComparisonToMinMax = function (intComparison, varValue) {
    intComparison = Number.parseLocale(intComparison.toString());
    varValue = Number.parseLocale(varValue.toString());
    var obj;
    switch (intComparison) {
        case $R_ENUM$NumericalComparisonType.EqualTo:
            obj = { Min: varValue, Max: varValue };
            break;
        case $R_ENUM$NumericalComparisonType.GreaterThan:
            obj = { Min: varValue + 1, Max: $R_MAX };
            break;
        case $R_ENUM$NumericalComparisonType.GreaterThanOrEqualTo:
            obj = { Min: varValue, Max: $R_MAX };
            break;
        case $R_ENUM$NumericalComparisonType.LessThan:
            obj = { Min: $R_MIN, Max: varValue - 1 };
            break;
        case $R_ENUM$NumericalComparisonType.LessThanOrEqualTo:
            obj = { Min: $R_MIN, Max: varValue };
            break;
    }
    return obj;
};

Rebound.GlobalTrader.Site.Functions.getDateFromDateAndTimeFields = function (strDateField, strTimeField, frm, blnOutputString) {
    var dtm = Date.parseLocale(frm.getFieldValue(strDateField));
    if (dtm) {
        dtm.setHours(frm.getFieldControl(strTimeField).getHours());
        dtm.setMinutes(frm.getFieldControl(strTimeField).getMinutes());
        if (blnOutputString) dtm = dtm.localeFormat("F");
    }
    return dtm;
};

Rebound.GlobalTrader.Site.Functions.openPrintStockWindow = function (enmPrintObject, intID, blnEmail) {
    var strURL = String.format("Stockprint.aspx?{0}={1}", $R_QS_PrintObject, enmPrintObject);
    if (intID > 0) strURL += String.format("&{0}={1}", $R_QS_GenericID, intID);
    if (blnEmail) strURL += String.format("&{0}=1", $R_QS_EmailMode);
    window.open(strURL, "winPrint", "left=20,top=20,width=770,height=574,toolbar=no,resizable=yes,status=no,directories=no,location=no,menubar=yes,scrollbars=yes");


};

Rebound.GlobalTrader.Site.Functions.openPrintWindow = function (enmPrintObject, intID, blnEmail) {
    var strURL = String.format("Print.aspx?{0}={1}", $R_QS_PrintObject, enmPrintObject);
    if (intID > 0) strURL += String.format("&{0}={1}", $R_QS_GenericID, intID);
    if (blnEmail) strURL += String.format("&{0}=1", $R_QS_EmailMode);
    window.open(strURL, "winPrint", "left=20,top=20,width=770,height=574,toolbar=no,resizable=yes,status=no,directories=no,location=no,menubar=yes,scrollbars=yes");
};

Rebound.GlobalTrader.Site.Functions.openPrintQuoteWindow = function (enmPrintObject, intID, blnEmail) {
    var strURL = String.format("Print.aspx?{0}={1}", $R_QS_PrintObject, enmPrintObject);
    if (intID > 0) strURL += String.format("&{0}={1}", $R_QS_GenericID, intID);
    if (blnEmail) strURL += String.format("&{0}=1", $R_QS_EmailMode);
    var loaded = window.open(strURL, "winPrint", "left=20,top=20,width=770,height=574,toolbar=no,resizable=yes,status=no,directories=no,location=no,menubar=yes,scrollbars=yes");
    return loaded;
};

Rebound.GlobalTrader.Site.Functions.openPrintWindowWithMultiples = function (enmPrintObject, strIDs) {
    var strURL = String.format("Print.aspx?{0}={1}", $R_QS_PrintObject, enmPrintObject);
    if (strIDs.length > 0) strURL += String.format("&{0}={1}", $R_QS_LineIDs, strIDs);
    window.open(strURL, "winPrint", "left=20,top=20,width=770,height=574,toolbar=no,resizable=yes,status=no,directories=no,location=no,menubar=yes,scrollbars=yes");
};
Rebound.GlobalTrader.Site.Functions.openPrintWindowWithMultiplesforDebitandCredit = function (QueryObject,enmPrintObject, strIDs) {
    var strURL = String.format("Print.aspx?{0}={1}", $R_QS_PrintObject, enmPrintObject);
    if (strIDs.length > 0) strURL += String.format("&{0}={1}", QueryObject, strIDs);
    window.open(strURL, "winPrint", "left=20,top=20,width=770,height=574,toolbar=no,resizable=yes,status=no,directories=no,location=no,menubar=yes,scrollbars=yes");
};
Rebound.GlobalTrader.Site.Functions.openReportWindow = function (enmPrintObject, strParms) {
    var strURL = String.format("PrintReport.aspx?{0}={1}", $R_QS_ReportID, enmPrintObject);
    if (strParms.length > 0) strURL += String.format("&{0}={1}", $R_QS_ReportParameters, strParms);
    window.open(strURL, "winReport", "left=20,top=20,width=770,height=574,toolbar=no,resizable=yes,status=no,directories=no,location=no,menubar=yes,scrollbars=yes");
};
//[002] code start
Rebound.GlobalTrader.Site.Functions.openEmailStatusWindow = function (enmPrintObject, strParms) {
    var strURL = String.format("PrintEmailStatus.aspx?{0}={1}", "ies", enmPrintObject);
    if (strParms.length > 0) strURL += String.format("&{0}={1}", $R_QS_ReportParameters, strParms);
    window.open(strURL, "winEmailStatus", "left=50,top=80,width=700,height=500,toolbar=no,resizable=yes,status=no,directories=no,location=no,menubar=yes,scrollbars=yes");
};
//[002] code end
Rebound.GlobalTrader.Site.Functions.openHelpWindow = function (strPage) {
    var strURL = String.format("Help/_PageHelp.aspx?{0}={1}", $R_QS_HelpPage, escape(strPage));
    window.open(strURL, "winHelp", "left=20,top=20,width=750,height=574,toolbar=no,resizable=yes,status=no,directories=no,location=no,menubar=no,scrollbars=yes");
};
//[004] code start
Rebound.GlobalTrader.Site.Functions.openSalesCalc = function (strPage) {
    var strURL = "SaleCalculator.aspx";
    var targetWin = window.open(strURL, "winSales", "left=20,top=100,width=300,height=400,toolbar=no,resizable=no,status=no,directories=no,location=no,menubar=no,scrollbars=no;");
    if (targetWin.location == "about:blank") {
        // alert('test');
        targetWin.location.hef = strURL;
        targetWin.focus();
    }
    else {
        //alert('test 2');
        targetWin.focus();
    }
};
//new method
Rebound.GlobalTrader.Site.Functions.openSalesCalcQoute = function (quotecalc, cqyt, cbuyPrice, cduty, cFreight, cMarginReq) {
    var strURL = "SaleCalculator.aspx?quotecalc=" + quotecalc + "&cqyt=" + cqyt + "&cbuyPrice=" + cbuyPrice + "&cduty=" + cduty + "&cFreight=" + cFreight + "&cMarginReq=" + cMarginReq + "";
    var targetWin = window.open(strURL, "winSales", "left=630,top=85,width=300,height=400,toolbar=no,resizable=no,status=no,directories=no,location=no,menubar=no,scrollbars=no;");
    if (targetWin.location == "about:blank") {
        targetWin.location.hef = strURL;
        targetWin.focus();
    }
    else {
        targetWin.focus();
    }
};
Rebound.GlobalTrader.Site.Functions.openSalesCalcQouteEdit = function (quotecalc, cqyt, cbuyPrice, cduty, cFreight, cMarginReq) {
    var strURL = "SaleCalculator.aspx?quotecalc=" + quotecalc + "&cqyt=" + cqyt + "&cbuyPrice=" + cbuyPrice + "&cduty=" + cduty + "&cFreight=" + cFreight + "&cMarginReq=" + cMarginReq + "";
    var targetWin = window.open(strURL, "winSales", "left=750,top=100,width=300,height=400,toolbar=no,resizable=no,status=no,directories=no,location=no,menubar=no,scrollbars=no;");
    if (targetWin.location == "about:blank") {
        targetWin.location.hef = strURL;
        targetWin.focus();
    }
    else {
        targetWin.focus();
    }
};
//[004] code end
//[004] code start
Rebound.GlobalTrader.Site.Functions.openExchangeRate = function (strPage) {
    var strURL = "ExchangeRate.aspx";
    var targetWin = window.open(strURL, "winSales", "left=20,top=100,width=325,height=200,toolbar=no,resizable=no,status=no,directories=no,location=no,menubar=no,scrollbars=no;");
    if (targetWin.location == "about:blank") {
        // alert('test');
        targetWin.location.hef = strURL;
        targetWin.focus();
    }
    else {
        //alert('test 2');
        targetWin.focus();
    }
};

Rebound.GlobalTrader.Site.Functions.openGermanyExchangeRate = function (strPage) {
    var strURL = "GermanyExchangeRate.aspx";
    var targetWin = window.open(strURL, "winSales", "left=20,top=100,width=1095,height=510,toolbar=no,resizable=no,status=no,directories=no,location=no,menubar=no,scrollbars=no;");
    if (targetWin.location == "about:blank") {
        // alert('test');
        targetWin.location.hef = strURL;
        targetWin.focus();
    }
    else {
        //alert('test 2');
        targetWin.focus();
    }
};

//[001] code start Get ClientExchangeRate
Rebound.GlobalTrader.Site.Functions.openClientExchangeRate = function (strPage) {
    var strURL = "ClientExchangeRate.aspx";
    var targetWin = window.open(strURL, "winSales", "left=20,top=100,width=375,height=230,toolbar=no,resizable=no,status=no,directories=no,location=no,menubar=no,scrollbars=no;");
    if (targetWin.location == "about:blank") {
        // alert('test');
        targetWin.location.hef = strURL;
        targetWin.focus();
    }
    else {
        //alert('test 2');
        targetWin.focus();
    }
};
//[001] code end

//[004] code end

//Integration of CSL & Eupore Sections in GT application / code start
//Ticket No GTDP-315
Rebound.GlobalTrader.Site.Functions.openCSLsearch = function (strPage) {
    var strURL = "CSL_Search.aspx";
    var targetWin = window.open(strURL, "winSales", "left=20,top=100,width=1000,height=500,toolbar=no,resizable=no,status=no,directories=no,location=no,menubar=no,scrollbars=yes;");
    if (targetWin.location == "about:blank") {
        // alert('test');
        targetWin.location.hef = strURL;
        targetWin.focus();
    }
    else {
        //alert('test 2');
        targetWin.focus();
    }
};

Rebound.GlobalTrader.Site.Functions.openPowerBIProject = function (strPage, TokenValue, RequestLogin, ReportNo) {
    //var strURL = "https://localhost:44300/";
    var strURL = strPage + "Home/Index/" + TokenValue.toString() + "?LoginId=" + RequestLogin.toString() + "&Reportno=" + ReportNo;
    var targetWin = window.open(strURL, "winSales", "left=25,top=100,width=1200,height=600,toolbar=no,resizable=no,status=no,directories=no,location=no,menubar=no,scrollbars=yes;");
    if (targetWin.location == "about:blank") {
        // alert('test');
        targetWin.location.hef = strURL;
        targetWin.focus();
    }
    else {
        //alert('test 2');
        targetWin.focus();
    }
};
//[RP-2713] START
Rebound.GlobalTrader.Site.Functions.openPowerBIProjectNewTab = function (strPage, TokenValue, RequestLogin, ReportNo) {
    //var strURL = "https://localhost:44300/";
    var strURL = strPage;
    window.open(strURL, "_blank");
};
//[RP-2713] END
//code end

//[013] code start for Auto Search Global Product [Open ProductSearch.aspx window]
Rebound.GlobalTrader.Site.Functions.openProduct = function (strPage) {
    var strURL = "ProductSearch.aspx";
    var targetWin = window.open(strURL, "winSales", "left=20,top=100,width=730,height=400,toolbar=no,resizable=no,status=no,directories=no,location=no,menubar=no,scrollbars=no;");
    if (targetWin.location == "about:blank") {
        // alert('test');
        targetWin.location.hef = strURL;
        targetWin.focus();
    }
    else {
        //alert('test 2');
        targetWin.focus();
    }
};
//[013] code end

Rebound.GlobalTrader.Site.Functions.openPDFWindow = function (strPDF) {
    var strURL = String.format("PDFs/{0}{1}.pdf", $R_QS_ReportID, strPDF);
    window.open(strURL, "winPDF", "left=20,top=20,width=750,height=574,toolbar=no,resizable=yes,status=no,directories=no,location=no,menubar=no,scrollbars=yes");
};

Rebound.GlobalTrader.Site.Functions.windowScrollPosition = function () {
    var intScroll = document.body.scrollTop;
    if (intScroll == 0) {
        if (window.pageYOffset) {
            intScroll = window.pageYOffset;
        } else {
            intScroll = (document.body.parentElement) ? document.body.parentElement.scrollTop : 0;
        }
    }
    return intScroll;
};

Rebound.GlobalTrader.Site.Functions.getCreateStatement = function (strType, aryProperties, strElement) {
    var str = "$create(";
    str += String.format("{0}", strType);
    var blnHasProperties = (aryProperties) ? (aryProperties.length > 0) : false;
    if (blnHasProperties) {
        str += ", {";
        for (var i = 0, l = aryProperties.length; i < l; i++) {
            if (i > 0) str += ", ";
            str += String.format("\"{0}\": {1}", aryProperties[i][0], aryProperties[i][1]);
        }
        str += "}";
    } else {
        str += ", null";
    }
    str += ", null, null";
    str += String.format(", $get(\"{0}\")", strElement);
    str += ");";
    return str;
};

Rebound.GlobalTrader.Site.Functions.AsciiValue = function (chr) {
    chr = chr.charAt(0);
    var i;
    for (i = 0; i < 256; ++i) {
        var h = i.toString(16);
        if (h.length == 1) h = "0" + h;
        h = "%" + h;
        h = unescape(h);
        if (h == chr) break;
    }
    return i;
};

Rebound.GlobalTrader.Site.Functions.scrollPageToElement = function (el, intOffset) {
    if (!el) return;
    if (!intOffset) intOffset = -10;
    var intY = Sys.UI.DomElement.getBounds(el).y + intOffset;
    //Safari browsers (inc Chrome) calculate element bounds relative to window scroll
    if (Sys.Browser.agent == Sys.Browser.Safari) intY += window.scrollY;
    setTimeout(String.format("$R_FN.finishScrollPageToElement({0})", intY), 0);
};
Rebound.GlobalTrader.Site.Functions.finishScrollPageToElement = function (intY) {
    window.scroll(0, intY);
};

Rebound.GlobalTrader.Site.Functions.writeDoubleCellValue = function (strText1, strText2) {
    if (typeof (strText1) == "undefined") strText1 = "&nbsp;";
    if (!strText1) strText1 = "&nbsp;";
    if (strText1 == "") strText1 = "&nbsp;";
    if (typeof (strText2) == "undefined") strText2 = "&nbsp;";
    if (!strText2) strText2 = "&nbsp;";
    if (strText2 == "") strText2 = "&nbsp;";
    return String.format("<div class=\"doubleValueTop\">{0}</div>{1}", strText1, strText2);
};

Rebound.GlobalTrader.Site.Functions.writeTriCellValue = function (strText1, strText2, strText3) {
    if (typeof (strText1) == "undefined") strText1 = "&nbsp;";
    if (!strText1) strText1 = "&nbsp;";
    if (strText1 == "") strText1 = "&nbsp;";
    if (typeof (strText2) == "undefined") strText2 = "&nbsp;";
    if (!strText2) strText2 = "&nbsp;";
    if (strText2 == "") strText2 = "&nbsp;";
    if (typeof (strText3) == "undefined") strText3 = "&nbsp;";
    if (!strText3) strText3 = "&nbsp;";
    if (strText3 == "") strText3 = "&nbsp;";
    return String.format("<div class=\"doubleValueTop\">{0}</div><div class=\"doubleValueTop\">{1}</div>{2}", strText1, strText2, strText3);
};

//[001] Code Start
Rebound.GlobalTrader.Site.Functions.writeDoubleCellValueSourceStock = function (strText1, strText2, strText3) {
    if (typeof (strText1) == "undefined") strText1 = "&nbsp;";
    if (!strText1) strText1 = "&nbsp;";
    if (strText1 == "") strText1 = "&nbsp;";
    if (typeof (strText2) == "undefined") strText2 = "&nbsp;";
    if (!strText2) strText2 = "&nbsp;";
    if (strText2 == "") strText2 = "&nbsp;";
    if (typeof (strText3) == "undefined") strText3 = "0";
    if (!strText3) strText3 = "0";
    if (strText3 == "") strText3 = "0";
    return String.format("<div class=\"doubleValueTop\">{0} ({2})</div> <div class =\"doubleValueStockBottom\">{1}</div>", strText1, strText2, strText3);
};
//[001] Code End

//[003] code start
Rebound.GlobalTrader.Site.Functions.writeDoubleCellValueAlignRightSupplier = function (strText1, strText2, strText3, inactive) {
    if (typeof (strText1) == "undefined") strText1 = "&nbsp;";
    if (!strText1) strText1 = "&nbsp;";
    if (strText1 == "") strText1 = "&nbsp;";
    if (typeof (strText2) == "undefined") strText2 = "&nbsp;";
    if (!strText2) strText2 = "&nbsp;";
    if (strText2 == "") strText2 = "&nbsp;";
    if (typeof (strText3) == "undefined") strText3 = "&nbsp;";
    if (!strText3) strText3 = "&nbsp;";
    if (strText3 == "") strText3 = "&nbsp;";
    if (inactive) {
        return String.format("<div class=\"doubleValueTop\">{0}<img height=\"12\" src=\"App_Themes/Original/images/buttons/misc/user-disabled-icon.png\" /></div> <div class=\"doubleValueBotom\" ><span>{1}</span> <span class =\"doubleValueAlignRight\" >{2}</span></div>", strText1, strText2, strText3);
    }
    else {
        return String.format("<div class=\"doubleValueTop\">{0}</div> <div class=\"doubleValueBotom\" ><span>{1}</span> <span class =\"doubleValueAlignRight\" >{2}</span></div>", strText1, strText2, strText3);
    }
};
//[003] code end

Rebound.GlobalTrader.Site.Functions.splitDoubleCellValue = function (strHTML) {
    var strTest = "<div class=\"doubleValueTop\">";
    if (!strHTML.startsWith(strTest)) return [strHTML];
    return [strHTML.substring(strTest.length, strHTML.indexOf("</div>", strTest.length)), strHTML.substring(strHTML.indexOf("</div>", strTest.length) + 6, strHTML.length)];
};

Rebound.GlobalTrader.Site.Functions.navigateBack = function () {
    if (history.length > 1) {
        history.back();
    } else {
        location.href = "Default.aspx";
    }
};

Rebound.GlobalTrader.Site.Functions.writeNumericValue = function (varValue) {
    if (!varValue) varValue = 0;
    if (varValue == undefined) varValue = 0;
    return String.format("{0}", varValue);
};

Rebound.GlobalTrader.Site.Functions.findParentElementOfType = function (el, strType) {
    if (!el) return null;
    strType = strType.toUpperCase();
    var blnFound = false;
    var blnContinue = true;
    while (!blnFound && blnContinue) {
        if (!el.tagName) {
            blnContinue = false;
        } else {
            if (el.tagName.toUpperCase() == strType) {
                blnFound = true;
            } else {
                if (el.parentNode) {
                    el = el.parentNode;
                } else {
                    blnContinue = false;
                }
            }
        }
    }
    if (!blnFound) el = null;
    return el;
};
Rebound.GlobalTrader.Site.Functions.isValidNumber = function (strNumber) {
    if (!strNumber) return true;
    var number = 0;
    var isValid = true;
    number = Number.parseInvariant(strNumber);
    if (!number) {
        isValid = false;
    }
    return isValid;
};
Rebound.GlobalTrader.Site.Functions.formatCurrency = function (dblValue, strCurrency, intFigures, blnCommaThousands) {
    if (!intFigures) intFigures = 5;
    if (!dblValue) dblValue = 0;
    dblValue = Number.parseInvariant(Number.parseInvariant(dblValue.toString()).toFixed(intFigures).toString());
    var strReturn = dblValue.localeFormat(String.format("n{0}", intFigures));
    if (!blnCommaThousands) {
        var strNumericPart = strReturn.substring(0, strReturn.lastIndexOf(Sys.CultureInfo.CurrentCulture.numberFormat.CurrencyDecimalSeparator));
        strNumericPart = strNumericPart.replace(new RegExp(Sys.CultureInfo.CurrentCulture.numberFormat.NumberGroupSeparator, "gi"), "");
        strReturn = strNumericPart + strReturn.substring(strReturn.lastIndexOf(Sys.CultureInfo.CurrentCulture.numberFormat.CurrencyDecimalSeparator));
    }
    if (strCurrency) strReturn += String.format(" {0}", strCurrency);
    return strReturn;
};

Rebound.GlobalTrader.Site.Functions.setTextFilterFieldValueAndSearchType = function (fld, value) {
    value = value.toString().trim();
    if (value == "%" || value == "%%" || value == "%%%") {
        value = "";
    } else if (value.startsWith("%") && value.endsWith("%")) {
        value = value.substring(1, value.length - 1);
        fld.setSearchType(1);
    } else if (value.startsWith("%")) {
        value = value.substring(1);
        fld.setSearchType(2);
    } else if (value.endsWith("%")) {
        value = value.substring(0, value.length - 1);
        fld.setSearchType(0);
    }
    fld.setValue(value);
};

Rebound.GlobalTrader.Site.Functions.preventEnterKeyForControl = function (el, evt) {
    if (!el) return;
    var key;
    if (evt.keyCode) key = evt.keyCode;
    if (evt.which) key = evt.which;
    if (key == Sys.UI.Key.enter) return false;
    return true;
};
//[007] code start
Rebound.GlobalTrader.Site.Functions.writeROHS = function (intROHSStatus) {
    var str = $R_RES.ROHSUnknown;
    switch (intROHSStatus) {
        case 0: str = $R_RES.ROHSUnknown; break;
        case 1: str = $R_RES.ROHSCompliant; break;
        case 2: str = $R_RES.ROHSNonCompliant; break;
        case 3: str = $R_RES.ROHSExempt; break;
        case 4: str = $R_RES.ROHSNotApplicable; break;
        case 5: str = $R_RES.ROHS2; break;
        case 6: str = $R_RES.ROHS56; break;
        case 7: str = $R_RES.ROHS66; break;
        default: str = ""; break;
    }
    return $R_FN.writePartNo(str, intROHSStatus);
};

Rebound.GlobalTrader.Site.Functions.writePartNo = function (strPart, intROHSStatus) {
    strPart = $R_FN.setCleanTextValue(strPart);
    var str = strPart;
    if (!intROHSStatus) intROHSStatus = 0;
    intROHSStatus = Number.parseInvariant(intROHSStatus.toString());
    if (intROHSStatus > 0) {
        if (intROHSStatus >= 1 && intROHSStatus <= 7 && intROHSStatus != 0 && intROHSStatus != 4) {
            str = "<div class=\"rohs ";
            strAlt = "";
            switch (intROHSStatus) {
                case 1: str += "rohsCompliant"; strAlt = $R_RES.ROHSCompliant; break;
                case 2: str += "rohsNonCompliant"; strAlt = $R_RES.ROHSNonCompliant; break;
                case 3: str += "rohsExempt"; strAlt = $R_RES.ROHSExempt; break;
                case 5: str += "rohsROHS2"; strAlt = $R_RES.ROHS2; break;
                case 6: str += "rohsROHS56"; strAlt = $R_RES.ROHS56; break;
                case 7: str += "rohsROHS66"; strAlt = $R_RES.ROHS66; break;
            }
            str += String.format("\" title=\"{0}\">{1}</div>", strAlt, strPart);
        }
    }
    return str;
};

//[007] code end

Rebound.GlobalTrader.Site.Functions.writeSupplierName = function (strSupplierName, intStarRating) {
    var str = $R_FN.setCleanTextValue(strSupplierName);
    if (intStarRating > 0) {
        for (var i = 0; i < 5; i++) {
            if (i == 0) str += "&nbsp; ";
            str += String.format("<img src=\"{0}\" />", (i < intStarRating) ? $RGT_STAR_IMG : $RGT_STAR_EMPTY_IMG);
        }
    }
    return str;
};

Rebound.GlobalTrader.Site.Functions.setCookie = function (c_name, value, exdays) {
    var exdate = new Date(); exdate.setDate(exdate.getDate() + exdays);
    var c_value = value + ((exdays == null) ? "" : "; expires=" + exdate.toUTCString());
    document.cookie = c_name + "="; document.cookie = c_name + "=" + c_value;
};

Rebound.GlobalTrader.Site.Functions.getCookie = function (c_name) {
    var i, x, y, ARRcookies = document.cookie.split(";");
    for (i = 0; i < ARRcookies.length; i++) {
        x = ARRcookies[i].substr(0, ARRcookies[i].indexOf("="));
        y = ARRcookies[i].substr(ARRcookies[i].indexOf("=") + 1);
        x = x.replace(/^\s+|\s+$/g, "");
        if (x == c_name) {
            return unescape(y);
        }
    }
};

Rebound.GlobalTrader.Site.Functions.deleteCookie = function (c_name, value) {
    $R_FN.setCookie(c_name, value, -10);
};

Rebound.GlobalTrader.Site.Functions.registerClass("Rebound.GlobalTrader.Site.Functions");

/* Prototype additions */
/*************************************************************************************************/
Array.prototype.sum = function () {
    var sum = 0;
    for (var i = 0, l = this.length; i < l; i++) {
        sum += Number.parseLocale(this[i].toString());
    }
    return sum;
};

//[005] code start
Rebound.GlobalTrader.Site.Functions.openNPRPrintWindow = function (intID, intNPRId) {
    var strURL = ""; //String.format("NPRPrint.aspx?{0}={1}", $R_QS_PrintObject, enmPrintObject);
    if (intID > 0) strURL += String.format("NPRPrint.aspx?{0}={1}", $R_QS_GenericID, intID);
    if (intNPRId > 0) strURL += String.format("&{0}={1}", $R_QS_NPRID, intNPRId);
    window.open(strURL, "winNPRPrint", "left=20,top=20,width=870,height=674,toolbar=no,resizable=yes,status=no,directories=no,location=no,menubar=yes,scrollbars=yes");
};

Rebound.GlobalTrader.Site.Functions.createNubButtonNPR = function (strID, strText, strLineId, strAlign, blnToolTip, strTooltipContent, strTooltipFunction, strTooltipFunctionParametersArray, intTooltipWidth) {
    var strOut = "&nbsp;";
    if (typeof (strText) != "undefined") {
        if (strText.toString().length > 0) {
            var strHref = "javascript:void(0);";
            if (!strAlign) strAlign = "Left";
            var strClass = "topMenuRolloverLink";
            //if (strAlign != "") strClass += " " + strAlign;
            strText = $R_FN.setCleanTextValue(strText);

            var strLog = String.format("&nbsp;&nbsp;<a href=\"javascript:void(0);\" onclick=\"$RGT_openNPRLog($R_ENUM$PrintObject.ReportNPRLog,{0})\" >Log</a>", strID);

            //create tooltip mouseover?
            var strToolTip = "";
            if (blnToolTip) {
                if (strTooltipContent) {
                    if (typeof (strTooltipContent).toString().toLowerCase() == "string") strTooltipContent = "\"" + strTooltipContent + "\"";
                } else {
                    strTooltipContent = "null";
                }
                strToolTip = String.format(" onmouseover=\"$R_PAGE._ctlToolTip.registerDynamicElement(this, {0}, {1}, {2}, {3});\"", strTooltipContent, strTooltipFunction, strTooltipFunctionParametersArray, (intTooltipWidth) ? intTooltipWidth : "null");
            }
            strOut = String.format("<div class=\"{1}\"><a href=\"{0}\" onclick=\"{4}\" {2}>{3}</a>{5}</div>", strHref, strClass, strToolTip, strText, String.format("$RGT_openNPRWindow({0},{1})", strLineId, strID), strLog);
        }
    }
    return strOut;
};



Rebound.GlobalTrader.Site.Functions.createNubButtonNPRNugget = function (strID, strText, strLineId, strAlign, blnToolTip, strTooltipContent, strTooltipFunction, strTooltipFunctionParametersArray, intTooltipWidth) {
    var strOut = "&nbsp;";
    if (typeof (strText) != "undefined") {
        if (strText.toString().length > 0) {
            var strHref = "javascript:void(0);";
            //if (!strAlign) strAlign = "Left";
            var strClass = "nubButton nubButtonAlignLeft";
            //if (strAlign != "") strClass += " " + strAlign;

            strText = $R_FN.setCleanTextValue(strText);

            var strLog = String.format("&nbsp;&nbsp;<a href=\"javascript:void(0);\" onclick=\"$RGT_openNPRLog($R_ENUM$PrintObject.ReportNPRLog,{0})\" >Log</a>", strID);

            //create tooltip mouseover?
            var strToolTip = "";
            if (blnToolTip) {
                if (strTooltipContent) {
                    if (typeof (strTooltipContent).toString().toLowerCase() == "string") strTooltipContent = "\"" + strTooltipContent + "\"";
                } else {
                    strTooltipContent = "null";
                }
                strToolTip = String.format(" onmouseover=\"$R_PAGE._ctlToolTip.registerDynamicElement(this, {0}, {1}, {2}, {3});\"", strTooltipContent, strTooltipFunction, strTooltipFunctionParametersArray, (intTooltipWidth) ? intTooltipWidth : "null");
            }
            strOut = String.format("<div><a href=\"{0}\" class=\"{1}\" onclick=\"{4}\" {2}>{3}</a>{5}</div>", strHref, strClass, strToolTip, strText, String.format("$RGT_openNPRWindow({0},{1})", strLineId, strID), null);


        }
    }
    return strOut;
};




//[005] code end

Rebound.GlobalTrader.Site.Functions.createNubButtonSTO = function (strID, strText, strLineId, strAlign, blnToolTip, strTooltipContent, strTooltipFunction, strTooltipFunctionParametersArray, intTooltipWidth) {
    var strOut = "&nbsp;";
    if (typeof (strText) != "undefined") {
        if (strText.toString().length > 0) {
            var strHref = "javascript:void(0);";
            if (!strAlign) strAlign = "Left";
            var strClass = "topMenuRolloverLink";
            //if (strAlign != "") strClass += " " + strAlign;
            strText = $R_FN.setCleanTextValue(strText);
            strText = "STO ";
            var strLog = String.format("&nbsp;&nbsp;<a href=\"javascript:void(0);\" onclick=\"$RGT_openNPRLog($R_ENUM$PrintObject.STO,{0})\" >"+strID+"</a>", strID);
            //strLog = '';
            //create tooltip mouseover?
            var strToolTip = "";
            if (blnToolTip) {
                if (strTooltipContent) {
                    if (typeof (strTooltipContent).toString().toLowerCase() == "string") strTooltipContent = "\"" + strTooltipContent + "\"";
                } else {
                    strTooltipContent = "null";
                }
                strToolTip = String.format(" onmouseover=\"$R_PAGE._ctlToolTip.registerDynamicElement(this, {0}, {1}, {2}, {3});\"", strTooltipContent, strTooltipFunction, strTooltipFunctionParametersArray, (intTooltipWidth) ? intTooltipWidth : "null");
            }
            strOut = String.format("<div class=\"{1}\"><a href=\"{0}\" onclick=\"{4}\" {2}>{3}</a>{5}</div>", strHref, strClass, strToolTip, strText, String.format("$RGT_openNPRWindow({0},{1})", strLineId, strID), strLog);
        }
    }
    return strOut;
};

Rebound.GlobalTrader.Site.Functions.createNubButtonSOShip = function (strID, strText, strAlign, hypClientID, strMouseOver, strMouseOut) {
    var strOut = "&nbsp;";
    if (typeof (strText) != "undefined") {
        if (strText.toString().length > 0) {
            var strHref = "javascript:void(0);";
            if (!strAlign) strAlign = "Left";
            var strClass = "";
            if (strAlign != "") strClass += " nubButtonAlign" + strAlign;
            strText = $R_FN.setCleanTextValue(strText);
            if (strText.toString().length <= 60)
                strOut = '<span style="color:red">' + strText + '</style>';
            else {
                strOut = String.format("{2}<br/><a onmouseover=\"{4}\" onmouseout=\"{5}\" class=\"{1}\" href=\"{0}\" id=\"{3}\" >...</a>", strHref, strClass, '<span style="color:red">' + strText.substring(0, 59) + '</style>', hypClientID, strMouseOver, strMouseOut);
            }
        }
    }
    return strOut;
};


Rebound.GlobalTrader.Site.Functions.createNubButtonSOShipMSL = function (strID, strText, strAlign, hypClientID, strMouseOver, strMouseOut, strMSL) {
    var strOut = "&nbsp;";
    var strMSLOut = "";
    if (typeof (strText) != "undefined") {
        if (strText.toString().length > 0) {
            var strHref = "javascript:void(0);";
            if (!strAlign) strAlign = "Left";
            var strClass = "";
            if (strAlign != "") strClass += " nubButtonAlign" + strAlign;
            if (strMSL != "" && strMSL.toString().length > 0) strMSLOut = "/MSL: " + strMSL;
            strText = $R_FN.setCleanTextValue(strText);
            if (strText.toString().length <= 60)
                strOut = '<span style="color:red">' + strText + strMSLOut + '</style>';
            else {
                strOut = String.format("{2}<br/><a onmouseover=\"{4}\" onmouseout=\"{5}\" class=\"{1}\" href=\"{0}\" id=\"{3}\" >...</a>", strHref, strClass, '<span style="color:red">' + strText.substring(0, 59) + strMSLOut + '</style>', hypClientID, strMouseOver, strMouseOut);
            }
        }
    }
    if (strText == "" || strText.toString().length <= 0) {
        if (strMSL != "" && strMSL.toString().length > 0) {
            strOut = '<span style="color:red">' + "MSL: " + strMSL + '</style>';
        }
    }
    return strOut;
};

Rebound.GlobalTrader.Site.Functions.openPrintNPRWindow = function (enmPrintObject, intID, blnEmail) {
    var strURL = String.format("Print.aspx?{0}={1}", $R_QS_PrintObject, enmPrintObject);
    if (intID > 0) strURL += String.format("&{0}={1}", $R_QS_NPRID, intID);
    if (blnEmail) strURL += String.format("&{0}=1", $R_QS_EmailMode);
    window.open(strURL, "winPrint", "left=20,top=20,width=770,height=574,toolbar=no,resizable=yes,status=no,directories=no,location=no,menubar=yes,scrollbars=yes");
};

Rebound.GlobalTrader.Site.Functions.openManufacturerSearch = function (strPage) {
    //var strURL = strPage;
    var targetWin = window.open(strPage, "winSales", "left=20,top=100,width=1200,height=500,toolbar=no,resizable=no,status=no,directories=no,location=no,menubar=no,scrollbars=yes;");
    if (targetWin.location == "supplierManufacturerSearch.aspx") {
        // alert('test');
        targetWin.location.hef = strURL;
        targetWin.focus();
    }
    else {
        //alert('test 2');
        targetWin.focus();
    }
};

//[006] code start
Rebound.GlobalTrader.Site.Functions.writeProductSource = function (intProductSource) {
    var str = "";
    switch (intProductSource) {
        case 1: str = $R_RES.NonPreferred; break;
        case 2: str = $R_RES.Traceable; break;
        case 3: str = $R_RES.Trusted; break;
        default: str = ""; break;
    }
    return str;
};
//[006] code end

Rebound.GlobalTrader.Site.Functions.getApprovedByAndDate = function (bln, strText) {
    var str = "<div class=\"imageCheckBoxDisabled\">";
    str += String.format("<img style=\"border-width: 0px;\" src=\"images/x.gif\" class=\"{0}\" />", bln ? "on" : "off");
    if (typeof (strText) != "undefined") {
        if (strText.toString().length > 0) {
            str += '&nbsp;&nbsp;<span style="font-size:9px;vertical-align:top;color:#808080;">' + strText + "</span>"
        }
    }
    str += "</div>";
    return str;
};

Rebound.GlobalTrader.Site.Functions.showYellowText = function (strValue) {
    if (!strValue) return "";
    var str = '<span style="background-color: rgb(255, 255, 0);">' + strValue + '</span> '
    return str;
};
Rebound.GlobalTrader.Site.Functions.showYellowTextImportant = function (strValue) {
    if (!strValue) return "";
    var str = '<span class="yellow-background">' + strValue + '</span> '
    return str;
};

Rebound.GlobalTrader.Site.Functions.showLargeFonts = function (strValue) {
    if (!strValue) return "";
    var str = '<span  class="showLargeFonts">' + strValue + '</span> '
    return str;
};

Rebound.GlobalTrader.Site.Functions.showLargeFontsWithColor = function (strValue) {
    if (!strValue) return "";
    var str = '<span  class="showLargeFontsWithColor">' + strValue + '</span> '
    return str;
};
Rebound.GlobalTrader.Site.Functions.openEPRWindow = function (intPOId, eprId, strPOLineIDs, strPOLineSerialNo) {
    var strURL = ""; //String.format("NPRPrint.aspx?{0}={1}", $R_QS_PrintObject, enmPrintObject);
    if (intPOId > 0) strURL += String.format("EPR.aspx?{0}={1}", $R_QS_PurchaseOrderID, intPOId);
    if (eprId > 0) strURL += String.format("&{0}={1}", $R_QS_EPRId, eprId);

    if (typeof strPOLineIDs != 'undefined') {
        if (strPOLineIDs.length > 0) strURL += String.format("&{0}={1}", "polids", strPOLineIDs);
    }
    if (typeof strPOLineSerialNo != 'undefined') {
        if (strPOLineSerialNo.length > 0)
            strURL += String.format("&{0}={1}", "pols", strPOLineSerialNo);
    }
    window.open(strURL, "winEPR", "left=20,top=20,width=870,height=674,toolbar=no,resizable=yes,status=no,directories=no,location=no,menubar=yes,scrollbars=yes");

    //location.href = $RGT_gotoURL_PurchaseOrder(intPOId);

};

Rebound.GlobalTrader.Site.Functions.openCreditLimitWindow = function (intCompanyId, CreditLimitId) {
    var strURL = "";
    if (intCompanyId > 0) strURL += String.format("CreditLimit.aspx?{0}={1}", "cm", intCompanyId);
    if (CreditLimitId > 0) strURL += String.format("&{0}={1}", "CreditLimit", CreditLimitId);
    window.open(strURL, "winCreditLimit", "left=20,top=20,width=870,height=674,toolbar=no,resizable=yes,status=no,directories=no,location=no,menubar=yes,scrollbars=yes");
};
Rebound.GlobalTrader.Site.Functions.openCreditLimitLogWindow = function (intCompanyId, CreditLimitId) {
    var strURL = "";
    if (intCompanyId > 0) strURL += String.format("CreditLimitLog.aspx?{0}={1}", "cm", intCompanyId);
    if (CreditLimitId > 0) strURL += String.format("&{0}={1}", "CreditLimit", CreditLimitId);
    window.open(strURL, "winCreditLimit", "left=20,top=20,width=870,height=674,toolbar=no,resizable=yes,status=no,directories=no,location=no,menubar=yes,scrollbars=yes");
};

Rebound.GlobalTrader.Site.Functions.createNubButtonEPR = function (strID, strNumber, strText, strAlign, blnToolTip, strTooltipContent, strTooltipFunction, strTooltipFunctionParametersArray, intTooltipWidth) {
    var strOut = "&nbsp;";
    if (typeof (strNumber) != "undefined") {
        if (strNumber.toString().length > 0) {
            var strHref = "javascript:void(0);";
            if (!strAlign) strAlign = "Left";
            var strClass = "topMenuRolloverLink";
            //if (strAlign != "") strClass += " " + strAlign;
            strText = strText + "-" + strNumber;
            var strLog = String.format("&nbsp;&nbsp;<a href=\"javascript:void(0);\" onclick=\"$RGT_openEPRLog($R_ENUM$PrintObject.ReportEPRLog,{0})\" >Log</a>", strNumber);

            //create tooltip mouseover?
            var strToolTip = "";
            if (blnToolTip) {
                if (strTooltipContent) {
                    if (typeof (strTooltipContent).toString().toLowerCase() == "string") strTooltipContent = "\"" + strTooltipContent + "\"";
                } else {
                    strTooltipContent = "null";
                }
                strToolTip = String.format(" onmouseover=\"$R_PAGE._ctlToolTip.registerDynamicElement(this, {0}, {1}, {2}, {3});\"", strTooltipContent, strTooltipFunction, strTooltipFunctionParametersArray, (intTooltipWidth) ? intTooltipWidth : "null");
            }
            strOut = String.format("<div class=\"{1}\"><a href=\"{0}\" onclick=\"{4}\" {2}>{3}</a>{5}</div>", strHref, strClass, strToolTip, strText, String.format("$RGT_openEPRWindow({0},{1})", strID, strNumber), strLog);
        }
    }
    return strOut;
};
Rebound.GlobalTrader.Site.Functions.createNubButtonCreditLimit = function (strID, strNumber, strText, strAlign, blnToolTip, strTooltipContent, strTooltipFunction, strTooltipFunctionParametersArray, intTooltipWidth) {
    var strOut = "&nbsp;";
    if (typeof (strNumber) != "undefined") {
        if (strNumber.toString().length > 0) {
            var strHref = "javascript:void(0);";
            if (!strAlign) strAlign = "Left";
            var strClass = "topMenuRolloverLink";
            //if (strAlign != "") strClass += " " + strAlign;
            strText = strText + "-" + strNumber;
            var strLog = String.format("&nbsp;&nbsp;<a href=\"javascript:void(0);\" onclick=\"$RGT_openCreditLimitLog($R_ENUM$PrintObject.ReportCreditLimitLog,{0})\" >Log</a>", strNumber);

            //create tooltip mouseover?
            var strToolTip = "";
            if (blnToolTip) {
                if (strTooltipContent) {
                    if (typeof (strTooltipContent).toString().toLowerCase() == "string") strTooltipContent = "\"" + strTooltipContent + "\"";
                } else {
                    strTooltipContent = "null";
                }
                strToolTip = String.format(" onmouseover=\"$R_PAGE._ctlToolTip.registerDynamicElement(this, {0}, {1}, {2}, {3});\"", strTooltipContent, strTooltipFunction, strTooltipFunctionParametersArray, (intTooltipWidth) ? intTooltipWidth : "null");
            }
            strOut = String.format("<div class=\"{1}\"><a href=\"{0}\" onclick=\"{4}\" {2}>{3}</a>{5}</div>", strHref, strClass, strToolTip, strText, String.format("$RGT_openCreditLimitWindow({0},{1})", strID, strNumber), strLog);
        }
    }
    return strOut;
};
Rebound.GlobalTrader.Site.Functions.showSerialNumber = function (strValue, intNo) {
    var strOut = "&nbsp;";
    var strOut = strValue;
    if (parseInt(intNo) > 0)
        strOut += String.format(" ({0})", intNo);
    return strOut;
};
Rebound.GlobalTrader.Site.Functions.showInvoiceSerialNumber = function (strValue, intNo) {
    var strOut = "&nbsp;";
    var strOut = strValue;
    if (parseInt(intNo) > 0)
        strOut += String.format("{0}", intNo);
    return strOut;
};
Rebound.GlobalTrader.Site.Functions.openPrintEPRWindow = function (enmPrintObject, intID, blnEmail) {
    var strURL = String.format("Print.aspx?{0}={1}", $R_QS_PrintObject, enmPrintObject);
    if (intID > 0) strURL += String.format("&{0}={1}", $R_QS_EPRId, intID);
    if (blnEmail) strURL += String.format("&{0}=1", $R_QS_EmailMode);
    window.open(strURL, "winPrint", "left=20,top=20,width=770,height=574,toolbar=no,resizable=yes,status=no,directories=no,location=no,menubar=yes,scrollbars=yes");
};

//[008] code start
Rebound.GlobalTrader.Site.Functions.openPrintWindowCustReqWithMultiples = function (enmPrintObject, intID) {
    
    var strURL = String.format("Print.aspx?{0}={1}", $R_QS_PrintObject, enmPrintObject);
    if (intID > 0) strURL += String.format("&{0}={1}", $R_QS_GenericID, intID);
    window.open(strURL, "winPrint", "left=20,top=20,width=950,height=574,toolbar=no,resizable=yes,status=no,directories=no,location=no,menubar=yes,scrollbars=yes");
};
//[008] code end

Rebound.GlobalTrader.Site.Functions.openEPRWindowEmail = function (intPOId, eprId) {
    var strURL = ""; //String.format("NPRPrint.aspx?{0}={1}", $R_QS_PrintObject, enmPrintObject);
    if (intPOId > 0) strURL += String.format("EPR.aspx?{0}={1}", $R_QS_PurchaseOrderID, intPOId);
    if (eprId > 0) strURL += String.format("&{0}={1}", $R_QS_EPRId, eprId);
    window.open(strURL, "winEPR", "left=20,top=20,width=870,height=674,toolbar=no,resizable=yes,status=no,directories=no,location=no,menubar=yes,scrollbars=yes");

    location.href = $RGT_gotoURL_PurchaseOrder(intPOId);

};
Rebound.GlobalTrader.Site.Functions.openForgotPassword = function () {
    var strURL = "Forget_Password.aspx";
    window.open(strURL, "winForgetPassword", "left=20,top=20,width=500,height=400,toolbar=no,resizable=yes,status=no,directories=no,location=no,menubar=yes,scrollbars=yes");
};
//[009] start code

Rebound.GlobalTrader.Site.Functions.openPrintLogWindow = function (enmPrintObject, intID, blnEmail, strSection) {
    var strURL = String.format("Print.aspx?{0}={1}", $R_QS_PrintObject, enmPrintObject);
    if (intID > 0) strURL += String.format("&{0}={1}", $R_QS_GenericID, intID);
    if (blnEmail) strURL += String.format("&{0}=1", $R_QS_EmailMode);
    if (strSection) strURL += String.format("&section={0}", strSection);
    window.open(strURL, "winPrint", "left=20,top=20,width=770,height=574,toolbar=no,resizable=yes,status=no,directories=no,location=no,menubar=yes,scrollbars=yes");
};


Rebound.GlobalTrader.Site.Functions.showHazardous = function (strName, blnHazardous) {
    strName = $R_FN.setCleanTextValue(strName);
    var str = strName;
    if (blnHazardous) {
        str = "<div class=\"hazardous ";
        strAlt = "";
        str += " "; strAlt = $R_RES.HazardousMessage;
        str += String.format("\" title=\"{0}\">{1}</div>", strAlt, strName);
    }
    return str;
};
Rebound.GlobalTrader.Site.Functions.showHazardousNew = function (strName, blnHazardous, HazardousProductMessage) {
    strName = $R_FN.setCleanTextValue(strName);
    var str = strName;
    if (blnHazardous) {
        str = "<div class=\"hazardous ";
        strAlt = "";
        str += " "; strAlt = HazardousProductMessage;
        str += String.format("\" title=\"{0}\">{1}</div>", strAlt, strName);
    }
    return str;
};
//[014] code start
Rebound.GlobalTrader.Site.Functions.showHazardousandOrdViaIPO = function (strName, blnHazardous, blnOrdViaIPOonly) {
    strName = $R_FN.setCleanTextValue(strName);
    var str = strName;
    strAlt = "";
    if (blnHazardous == true || blnOrdViaIPOonly == true) {
        str = "<div class=\"hazardous ";
        if (blnHazardous) {
            str += " ";
            strAlt = $R_RES.HazardousMessage;
        }
        if (blnOrdViaIPOonly) {
            strAlt += "\n\n" + $R_FN.setCleanTextValue($R_RES.OrderViaIPOonlyMessage);
        }
        str += String.format("\" title=\"{0}\">{1}</div>", strAlt, strName);
    }
    return str;
};
//[014] code end

//[017] code start
Rebound.GlobalTrader.Site.Functions.showHazardousandOrdViaIPONew = function (strName, blnHazardous, blnOrdViaIPOonly, ProductMessage, blnIsRestrictedProd ) {
    strName = $R_FN.setCleanTextValue(strName);
    var str = strName;
    strAlt = "";
    if (blnHazardous == true || blnOrdViaIPOonly == true || blnIsRestrictedProd == true) {
        str = "<div class=\"hazardous ";
        strAlt = ProductMessage.replaceAll("&#013;", "\n\n");
        //if (blnHazardous) {
        //    str += " ";
        //    strAlt = $R_RES.HazardousMessage;
        //}
        //if (blnOrdViaIPOonly) {
        //    strAlt += "\n\n" + $R_FN.setCleanTextValue($R_RES.OrderViaIPOonlyMessage);
        //}
        str += String.format("\" title=\"{0}\">{1}</div>", strAlt, strName);
    }
    return str;
};
//[017] code end

//[018] code start
Rebound.GlobalTrader.Site.Functions.showProductWarning = function (strName, blnHazardous, blnOrdViaIPOonly, ProductMessage, blnIsRestrictedProd) {
    var strAppendString = "";
    strName = $R_FN.setCleanTextValue(strName);
    var str = "";
    strAlt = "";
    if (blnHazardous == true || blnOrdViaIPOonly == true || blnIsRestrictedProd == true) {
        //str = "<div>";
        strAlt = ProductMessage.replaceAll("&#013;", "\n\n");
        ////////////////////////////////////////
        if (blnHazardous == true) {
            strAppendString = strAppendString + "\<span class=\"hazardous\" title=\"" + strAlt + "\"></span>";
        }
        if (blnOrdViaIPOonly == true) {
            strAppendString = strAppendString + "\<span class=\"hazardousIpo\" title=\"" + strAlt + "\"></span>";
        }
        if (blnIsRestrictedProd == true) {
            strAppendString = strAppendString + "\<span class=\"hazardousRh\" title=\"" + strAlt + "\"></span>";
        }
        
        str += String.format("\<div>{0}{1}<\div>", strName, strAppendString);
    }
    else { str = strName; }
    return str;
};
Rebound.GlobalTrader.Site.Functions.showProductWarningIndividual = function (strName, blnHazardous, blnOrdViaIPOonly, blnIsRestrictedProd, MsgHazardous, MsgIPO, MsgRestricted) {
    var strAppendString = "";
    strName = $R_FN.setCleanTextValue(strName);
    var str = "";
    Haz = "";
    IPO = "";
    Rt = "";
    if (blnHazardous == true || blnOrdViaIPOonly == true || blnIsRestrictedProd == true) {
        //str = "<div>";
        //strAlt = ProductMessage.replaceAll("&#013;", "\n\n");
        Haz = MsgHazardous.replace("&#013;", "\n");
        IPO = MsgIPO.replace("&#013;", "\n");
        Rt = MsgRestricted.replace("&#013;", "\n");
        ////////////////////////////////////////
        if (blnHazardous == true) {
            strAppendString = strAppendString + "\<span class=\"hazardous\" title=\"" + Haz + "\"></span>";
        }
        if (blnOrdViaIPOonly == true) {
            strAppendString = strAppendString + "\<span class=\"hazardousIpo\" title=\"" + IPO + "\"></span>";
        }
        if (blnIsRestrictedProd == true) {
            strAppendString = strAppendString + "\<span class=\"hazardousRh\" title=\"" + Rt + "\"></span>";
        }

        str += String.format("\<div>{0}{1}<\div>", strName, strAppendString);
    }
    else { str = strName; }
    return str;
};



////////Resel price/////////
Rebound.GlobalTrader.Site.Functions.showReselPriceMessage = function (strValue, strTitle) {
    if (!strValue) return "";
    var str = '';
    if (strTitle != null && strTitle.length > 0) {
        str = String.format("<span class=\"ihspartstatusdoc\"  title=\"{0}\"> {1} </span>", strTitle, strValue);
        
    }
    else {
        str = strValue;
    }

    return str;
};
//[018] code end


//code end
//[009] start end
//[010] start
Rebound.GlobalTrader.Site.Functions.getCheckboxText = function (bln, strText) {
    var str = "<div class=\"imageCheckBoxDisabled\">";
    str += String.format("<img style=\"border-width: 0px;\" src=\"images/x.gif\" class=\"{0}\" />", bln ? "on" : "off");
    if (typeof (strText) != "undefined") {
        if (strText.toString().length > 0) {
            str += '&nbsp;&nbsp;<span style="font-size:9px;vertical-align:top;color:#808080;">' + strText + "</span>"
        }
    }
    str += "</div>";
    return str;
};
//[010] end
//[011] start
Rebound.GlobalTrader.Site.Functions.setCellValueWithBackground = function (strIn, strCSSClass) {
    if (typeof (strIn) == "undefined") strIn = "";
    strIn = (strIn + "").trim();
    strIn = strIn.replace(/(:PLUS:)/g, "+");
    strIn = strIn.replace(/(:QUOTE:)/g, '"');
    strIn = strIn.replace(/((:AND:)|(&amp;))/g, "&");
    if (strCSSClass != '') strIn = '<span class=\'' + strCSSClass + '\'>' + strIn + '</span>'
    return strIn;
};
//[011] end

Rebound.GlobalTrader.Site.Functions.getSecLevel = function (varValue) {
    var strValue = "=";
    switch (Number.parseInvariant(varValue.toString())) {
        case 1: strValue = "My"; break;
        case 2: strValue = "Team"; break;
        case 3: strValue = "Division"; break;
        case 4: strValue = "Company"; break;
    }
    return strValue;
};

//[012] code start
Rebound.GlobalTrader.Site.Functions.openDocumentTree = function (strDocNo, strActiontype, strDocId) {
    var strURL = "AllDocumentInformation.aspx?DocNo=" + strDocNo + "&ActionType=" + strActiontype + "&DocId=" + strDocId;
    var targetWin = window.open(strURL, "winTreeView", "left=20,top=100,width=450,height=570,toolbar=no,resizable=no,status=no,directories=no,location=no,menubar=no,scrollbars=no");
    if (targetWin.location == "about:blank") {
        //alert('test');
        targetWin.location.hef = strURL;
        targetWin.focus();
    }
    else {
        //alert('test 2');
        targetWin.focus();
    }
};
//[012] code end

Rebound.GlobalTrader.Site.Functions.showGILine = function (intNo, intPLine) {
    var strOut = "&nbsp;";
    if (parseInt(intPLine) > 0)
        strOut += String.format("S({0})", intNo);
    else
        strOut += intNo;
    return strOut;
};
//[012] code end
Rebound.GlobalTrader.Site.Functions.openCrossMatch = function (intBomNo, strBomCode) {
    var strURL = "CrossMatch.aspx?BomNo=" + intBomNo + "&BomCode=" + strBomCode;
    var targetWin = window.open(strURL, "wincrossmatch", "directories=no,titlebar=no,toolbar=no,location=no,status=no,menubar=no,scrollbars=no,resizable=no,width=720,height=800");
    if (targetWin.location == "about:blank") {
        // alert('test');
        targetWin.location.hef = strURL;
        targetWin.focus();
    }
    else {
        //alert('test 2');
        targetWin.focus();
    }
};
Rebound.GlobalTrader.Site.Functions.openCrossMatchPage = function () {
    var strURL = "CrossMatch.aspx";
    var targetWin = window.open(strURL, "wincrossmatch", "directories=no,titlebar=no,toolbar=no,location=no,status=no,menubar=no,scrollbars=no,resizable=no,width=720,height=800");
    if (targetWin.location == "about:blank") {
        // alert('test');
        targetWin.location.hef = strURL;
        targetWin.focus();
    }
    else {
        //alert('test 2');
        targetWin.focus();
    }
};


Rebound.GlobalTrader.Site.Functions.showNotes = function (strShortDesc, strDesc) {
    strName = $R_FN.setCleanTextValue(strShortDesc);
    var str = strName;

    str = "<div class=\" ";
    strAlt = "";
    str += " "; strAlt = $R_FN.setCleanTextValue(strDesc);
    str += String.format("\" title=\"{0}\">{1} ...</div>", strAlt, strName);

    return str;
};

Rebound.GlobalTrader.Site.Functions.showExportedDate = function (strShortDesc, strDesc) {
    strName = $R_FN.setCleanTextValue(strShortDesc);
    var str = strName;

    str = "<div id='divExport' class=\" ";
    strAlt = "";
    str += " "; strAlt = $R_FN.setCleanTextValue(strDesc);
    str += String.format("\" title=\"{0}\">{1} </div>", strAlt, strName);

    return str;
};


//[016] code start
//ihs
Rebound.GlobalTrader.Site.Functions.showIHSstatusDefi = function (strName, strmessage) {
    strName = $R_FN.setCleanTextValue(strName);
    var str = strName;
    if (strmessage) {
        str = "<div class=\"ihspartstatusdoc";
        strAlt = "";
        str += " "; strAlt = strmessage;
        str += String.format("\" title=\"{0}\">{1}</div>", strAlt, strName);
    }
    return str;
};


//[016] code end/////////////////
Rebound.GlobalTrader.Site.Functions.showPOCounrtySectionDefi = function (strName, strmessage)
{
    strName = $R_FN.setCleanTextValue(strName);
    var str = strName;
    if (strmessage) {
        str = "<div id='POCounrtySection' class=\"poCourntySectionWarnning";
        strAlt = "";
        str += " "; strAlt = strmessage;
        str += String.format("\" title=\"{0}\">{1}</div>", strAlt, strName);
    }
    return str;
};
//[017] code start
//ihs ECCN Code Defination 10-01-2022
Rebound.GlobalTrader.Site.Functions.showIHSECCNCodeDefi = function (strName, strmessage) {
    strName = $R_FN.setCleanTextValue(strName);
    var str = strName;
    if (strmessage) {
        str = "<div class=\"ihspartstatusdoc";
        strAlt = "";
        str += " "; strAlt = strmessage.replaceAll("<br />".trim(), "");
        str += String.format("\" title=\"{0}\">{1}</div>", strAlt, strName);
    }
    return str;
};
//[017] code end
Rebound.GlobalTrader.Site.Functions.showRestCountry = function (strName, strmessage) {
    strName = $R_FN.setCleanTextValue(strName);
    var str = strName;
    if (strmessage) {
        str = "<div class=\"ihspartstatusdoc";
        strAlt = "";
        str += " "; strAlt = strmessage;
        str += String.format("\" title=\"{0}\">{1}</div>", strAlt, strName);
    }
    return str;
};
Rebound.GlobalTrader.Site.Functions.showRestCountry = function (strName, strmessage) {
    strName = $R_FN.setCleanTextValue(strName);
    var str = strName;
    if (strmessage) {
        str = "<div class=\"ihspartstatusdoc";
        strAlt = "";
        str += " "; strAlt = strmessage;
        str += String.format("\" title=\"{0}\">{1}</div>", strAlt, strName);
    }
    return str;
};
//[015] code start
Rebound.GlobalTrader.Site.Functions.openIHSPDFWindow = function (intIHSPartNo, intF) {
    var strURL = String.format("IHSPDFDocument.aspx?ihs=" + intIHSPartNo + "&iFrom=" + intF);
    window.open(strURL, "winIHSPDFDocument", "left=20,top=20,width=900,height=400,toolbar=no,resizable=yes,status=no,directories=no,location=no,menubar=yes,scrollbars=yes");
};

Rebound.GlobalTrader.Site.Functions.openBomItemIHSWindow = function (intIHSPartNo, intF) {
    var strURL = String.format("IHSPDFDocument.aspx?ihs=" + intIHSPartNo + "&iFrom=" + intF);
    window.open(strURL, "winIHSPDFDocument", "left=20,top=20,width=900,height=400,toolbar=no,resizable=yes,status=no,directories=no,location=no,menubar=yes,scrollbars=yes");
};
Rebound.GlobalTrader.Site.Functions.openEndUserUndertakingPDFWindow = function (SOLineID) {
    var strURL = String.format("EndUserUnderTakingPDF.aspx?SOLineID=" + SOLineID);
    window.open(strURL, "winEndUserUndertakingPDFDocument", "left=20,top=20,width=900,height=400,toolbar=no,resizable=yes,status=no,directories=no,location=no,menubar=yes,scrollbars=yes");
};

Rebound.GlobalTrader.Site.Functions.openCIPPDFWindow = function (intCertificateID, intCompanyID) {
    var strURL = String.format("CIPDocumentPDF.aspx?intCertificateID=" + intCertificateID + "&intCompanyID=" + intCompanyID);
    window.open(strURL, "winCIPPDFDocument", "left=20,top=20,width=900,height=400,toolbar=no,resizable=yes,status=no,directories=no,location=no,menubar=yes,scrollbars=yes");
};
//[015] code start
Rebound.GlobalTrader.Site.Functions.showSupplierMessage = function (strValue, strTitle) {
    if (!strValue) return "";
    var str = '';
    if (strTitle != null && strTitle.length > 0) {
        str = String.format("<span class=\"hazardous\"  style=\"background-color: yellow; background-position: right -1px; !important\" title=\"{0}\"> {1} </span>", strTitle, strValue);
            //'<span style="background-color: rgb(255, 255, 0);">' + strValue + '</span> '
    }
    else {
        str = strValue;
    }
    
    return str;
};

Rebound.GlobalTrader.Site.Functions.openSupplierApprovalDocWindow = function (intSupplierApprovalNo, strUpldTyp, intEditScreen) {
    var strURL = String.format("SupplierApprovalDoc.aspx?saID=" + intSupplierApprovalNo + "&upldTyp=" + strUpldTyp + "&editScreen=" + intEditScreen);
    window.open(strURL, "winIHSPDFDocument", "left=20,top=20,width=900,height=400,toolbar=no,resizable=yes,status=no,directories=no,location=no,menubar=yes,scrollbars=yes");
};
Rebound.GlobalTrader.Site.Functions.openSupplierApprovalImageWindow = function (intSupplierApprovalNo, strUpldTyp, intEditScreen) {
    var strURL = String.format("SupplierApprovalImage.aspx?saID=" + intSupplierApprovalNo + "&upldTyp=" + strUpldTyp + "&editScreen=" + intEditScreen);
    window.open(strURL, "winIHSPDFDocument", "left=20,top=20,width=900,height=400,toolbar=no,resizable=yes,status=no,directories=no,location=no,menubar=yes,scrollbars=yes");
};


Rebound.GlobalTrader.Site.Functions.openGIImageWindow = function (intGILineId) {
    var strURL = String.format("GILineImageDocument.aspx?gilId=" + intGILineId);
    window.open(strURL, "winGIImageDocument", "left=20,top=20,width=900,height=400,toolbar=no,resizable=yes,status=no,directories=no,location=no,menubar=yes,scrollbars=yes");
};


Rebound.GlobalTrader.Site.Functions.getApprovedStatus = function (bln, strText) {
   // alert(type(bln));
    
    var str = "<div class=\"imageCheckBoxDisabled\">";
    str += String.format("<img style=\"border-width: 0px;\" src=\"images/x.gif\" class=\"{0}\" />", (bln == true) ? "on" : "off");
    if (typeof (strText) != "undefined") {
        if (strText.toString().length > 0) {
            str += '&nbsp;&nbsp;<span style="font-size:9px;vertical-align:top;color:#808080;">' + strText + "</span>"
        }
    }
    str += "</div>";
    return str;
};



Rebound.GlobalTrader.Site.Functions.showStockAvailable = function (strShortDesc, strDesc, stockno) {
    strName = $R_FN.setCleanTextValue(strShortDesc);
    var str = strName;
    str = "<div class=\"";
    strAlt = "";
    str += " "; strAlt = $R_FN.setCleanTextValue(strDesc);
    str += String.format("\" title=\"{0}\">{1} <a href=\"{2}\" target='_blank'><img  title=\"{0}\" src='../../../App_Themes/Original/images/hazardous/ihspartstatuspng.png' /></a></div>", strAlt, strName, stockno);

    return str;
};

Rebound.GlobalTrader.Site.Functions.showStockAvailableNew = function (strShortDesc, QuantityInStock, QuantityOnOrder, QuantityAllocated, QuantityAvailable, stockURL, strStockAvailableDetail) {
    //strName = $R_FN.setCleanTextValue(strShortDesc);
    //var str = strName;
    //str = "<div class=\"dropdown";
    //strAlt = "";
    //str += " "; strAlt = $R_FN.setCleanTextValue(strStockAvailableDetail);
    //str += String.format("\" title=\"{0}\"> {1} <span class='dropbtn' style='color: #ff6a00'>Stock Alert!</span><div class='dropdown-content'><a href=\"{6}\" target='_blank' title=\"{2}\">{2}</a><a href=\"{6}\" target='_blank' title=\"{3}\">{3}</a><a href=\"{6}\" target='_blank' title=\"{4}\">{4}</a><a href=\"{6}\" target='_blank' title=\"{5}\">{5}</a></div></div>", strAlt, strName, QuantityInStock, QuantityOnOrder, QuantityAllocated, QuantityAvailable, stockURL);

    //return str;

    strName = $R_FN.setCleanTextValue(strShortDesc);
    strAlt = "";
    strAlt = $R_FN.setCleanTextValue(strStockAvailableDetail);
    var str = "<div class=\"dropdown ";
    var alertLabelHtml = "<span class='dropbtn' style='color: #ff6a00'>Stock Alert!</span>";
    var notesHtml = "<div class='notes'>Stock across all Clients</div>";
    var qtyInStockHtml = String.format("<div class='item' title=\"{0}\">{0}</div>", QuantityInStock);
    var qtyOnOrderHtml = String.format("<div class='item' title=\"{0}\">{0}</div>", QuantityOnOrder);
    var qtyAllocatedHtml = String.format("<div class='item' title=\"{0}\">{0}</div>", QuantityAllocated);
    var qtyAvailableHtml = String.format("<div class='item' title=\"{0}\">{0}</div>", QuantityAvailable);

    str += String.format("\" title=\"{0}\"> {1} {2}<div class='dropdown-content'>{3}{4}{5}{6}{7}</div></div>",
        strAlt,
        strName,
        alertLabelHtml,
        notesHtml,
        qtyInStockHtml,
        qtyOnOrderHtml,
        qtyAllocatedHtml,
        qtyAvailableHtml
    );

    return str;
};

Rebound.GlobalTrader.Site.Functions.showStockAvailableHUBRFQ = function (strShortDesc, QuantityInStock, QuantityOnOrder, QuantityAllocated, QuantityAvailable, stockURL, strStockAvailableDetail, pdfIcon) {
    //strName = $R_FN.setCleanTextValue(strShortDesc);
    //var str = strName;

    //str = "<div class=\"dropdown";
    //strAlt = "";
    //str += " "; strAlt = $R_FN.setCleanTextValue(strStockAvailableDetail);
    //str += String.format("\" title=\"{0}\"> {1} <span class='dropbtn' style='color: #ff6a00'>Stock Alert!</span><div class='dropdown-content'><a href=\"{6}\" target='_blank' title=\"{2}\">{2}</a><a href=\"{6}\" target='_blank' title=\"{3}\">{3}</a><a href=\"{6}\" target='_blank' title=\"{4}\">{4}</a><a href=\"{6}\" target='_blank' title=\"{5}\">{5}</a></div></div>", strAlt, strName, QuantityInStock, QuantityOnOrder, QuantityAllocated, QuantityAvailable, stockURL);
    //str += pdfIcon;
    //return str;

    strName = $R_FN.setCleanTextValue(strShortDesc);
    strAlt = "";
    strAlt = $R_FN.setCleanTextValue(strStockAvailableDetail);
    var str = "<div class=\"dropdown ";
    var alertLabelHtml = "<span class='dropbtn' style='color: #ff6a00'>Stock Alert!</span>";
    var notesHtml = "<div class='notes'>Stock across all Clients</div>";
    var qtyInStockHtml = String.format("<div class='item' title=\"{0}\">{0}</div>", QuantityInStock);
    var qtyOnOrderHtml = String.format("<div class='item' title=\"{0}\">{0}</div>", QuantityOnOrder);
    var qtyAllocatedHtml = String.format("<div class='item' title=\"{0}\">{0}</div>", QuantityAllocated);
    var qtyAvailableHtml = String.format("<div class='item' title=\"{0}\">{0}</div>", QuantityAvailable);

    str += String.format("\" title=\"{0}\"> {1} {2}<div class='dropdown-content'>{3}{4}{5}{6}{7}</div></div>",
        strAlt,
        strName,
        alertLabelHtml,
        notesHtml,
        qtyInStockHtml,
        qtyOnOrderHtml,
        qtyAllocatedHtml,
        qtyAvailableHtml
    );

    str += pdfIcon;
    return str;
};

Rebound.GlobalTrader.Site.Functions.showStockAvailableNA = function (strShortDesc, strNotFund) {
    strName = $R_FN.setCleanTextValue(strShortDesc);
    var str = strName;
    str = "<div class=\"dropdown";
    strAlt = "";
    str += " "; strAlt = $R_FN.setCleanTextValue(strNotFund);
    str += String.format("\" title=\"{0}\"> {1} <span class='dropbtn' style='color: #ff6a00'>Stock Alert!</span></div>", strAlt, strName);

    return str;
};




Rebound.GlobalTrader.Site.Functions.showStockAvailableNewBackup = function (strShortDesc, strDesc, stockno) {
    strName = $R_FN.setCleanTextValue(strShortDesc);
    var str = strName;
    str = "<div class=\"dropdown";
    strAlt = "";
    str += " "; strAlt = $R_FN.setCleanTextValue(strDesc);
    str += String.format("\" title=\"{0}\">{1}<button class='dropbtn'>Stock</button><div class='dropdown-content'><a href=\"{2}\" target='_blank'>Link 1</a><a href=\"{2}\" target='_blank'>Link 2</a><a href=\"{2}\" target='_blank'>Link 3</a><a href=\"{2}\" target='_blank'>Link 4</a></div></div>", strAlt, strName, stockno);

    return str;
};




Rebound.GlobalTrader.Site.Functions.createNubButtonGILineShortShipment = function (strID, strText, strLineId, strAlign, blnToolTip, strTooltipContent, strTooltipFunction, strTooltipFunctionParametersArray, intTooltipWidth) {
    var strOut = "&nbsp;";
    if (typeof (strText) != "undefined") {
        if (strText.toString().length > 0) {
            var strHref = "javascript:void(0);";
            if (!strAlign) strAlign = "Left";
            var strClass = "topMenuRolloverLink";
            //if (strAlign != "") strClass += " " + strAlign;
            strText = $R_FN.setCleanTextValue(strText);

            var strLog = String.format("&nbsp;&nbsp;<a href=\"javascript:void(0);\" onclick=\"$R_FN.openPrintWindow($R_ENUM$PrintObject.PrintSSLog,{0})\" >Log</a>", strID);

            //create tooltip mouseover?
            var strToolTip = "";
            if (blnToolTip) {
                if (strTooltipContent) {
                    if (typeof (strTooltipContent).toString().toLowerCase() == "string") strTooltipContent = "\"" + strTooltipContent + "\"";
                } else {
                    strTooltipContent = "null";
                }
                strToolTip = String.format(" onmouseover=\"$R_PAGE._ctlToolTip.registerDynamicElement(this, {0}, {1}, {2}, {3});\"", strTooltipContent, strTooltipFunction, strTooltipFunctionParametersArray, (intTooltipWidth) ? intTooltipWidth : "null");
            }
            strOut = String.format("<div class=\"{0}\">{1}{2}</div>", strClass, $RGT_nubButton_ShortShipmentDetails(strID, strID), strLog);
        }
    }
    return strOut;
};
//[019] code start
Rebound.GlobalTrader.Site.Functions.openCustomTemplateWindow = function (enmPrintObject, intID, blnEmail) {
    var strURL = String.format("CustomTemplate.aspx?{0}={1}", $R_QS_PrintObject, enmPrintObject);
    if (intID > 0) strURL += String.format("&{0}={1}", $R_QS_GenericID, intID);
    if (blnEmail) strURL += String.format("&{0}=1", $R_QS_EmailMode);
    window.open(strURL, "winPrint", "left=20,top=20,width=" + screen.width +",height=" + screen.height + ",toolbar=no,resizable=yes,status=no,directories=no,location=no,menubar=yes,scrollbars=yes");
};
//[019] code end
//CSL sanctioned list / code start
//Ticket No RP-254
Rebound.GlobalTrader.Site.Functions.openSanctioned = function (strPage) {
    var strURL = "CSL_Sanctioned.aspx";
    var targetWin = window.open(strURL, "winSales", "left=20,top=100,width=1000,height=500,toolbar=no,resizable=no,status=no,directories=no,location=no,menubar=no,scrollbars=yes;");
    if (targetWin.location == "about:blank") {
        // alert('test');
        targetWin.location.hef = strURL;
        targetWin.focus();
    }
    else {
        //alert('test 2');
        targetWin.focus();
    }
};

Rebound.GlobalTrader.Site.Functions.highlightBackgroundColorOfText = function (spanId, toggle) {
    // remove # if developer has prefixed it while passing the spanId in parameter
    if (spanId.charAt(0) === '#') {
        spanId = spanId.substr(1);
    }

    // change background color
    if (toggle == true) {
        //$("#" + spanId).css('background', '#f1a515');
        $("#" + spanId).addClass('AS6081HighlightBackgroundColorOfText');
    } else {
        //$("#" + spanId).css('background', 'transparent');
        $("#" + spanId).removeClass('AS6081HighlightBackgroundColorOfText');
    }
};

Rebound.GlobalTrader.Site.Functions.openSupplierInvoiceDetailWindow = function (intID) {
    var strURL = String.format("SupplierInvoiceDetail.aspx?{0}={1}", "SID", intID);
    window.open(strURL, "winPrint", "left=20,top=20,width=" + screen.width + ",height=" + screen.height + ",toolbar=no,resizable=yes,status=no,directories=no,location=no,menubar=yes,scrollbars=yes");
};

Rebound.GlobalTrader.Site.Functions.createAdvisoryNotesIcon = function (notes, optionalClass) {
    if (notes) {
        let warningNotes = $R_FN.setCleanTextValue(notes);
        warningNotes = warningNotes.replace(/(<br \/>)/g, "&#10;"); //allow line break in title attribute
        if (!optionalClass) optionalClass = "";
        return String.format("<span class=\"advisory-notes {1}\" title=\"{0}\"></span>", warningNotes, optionalClass);
    }
    return '';
};

Rebound.GlobalTrader.Site.Functions.parseDateFromUKFormat = function (strDate) {
    const [day, month, year] = strDate.split('/').map(Number);
    const date = new Date(year, month - 1, day);
    return date;
};