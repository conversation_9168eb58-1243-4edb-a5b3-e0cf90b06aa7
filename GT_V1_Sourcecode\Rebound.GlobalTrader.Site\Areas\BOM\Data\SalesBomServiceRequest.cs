﻿//using System;
//using System.Collections.Generic;
//using System.Data;
//using System.IO;
//using System.Linq;
//using System.Web;
//using Microsoft.Azure.Storage.Blob;
//using Rebound.GlobalTrader.BLL;
//using Rebound.GlobalTrader.DAL;
//using Rebound.GlobalTrader.Site.Areas.BOM.Models;
//using Microsoft.Azure.Storage;
//using Microsoft.Azure.Storage.Auth;
//using Microsoft.ApplicationInsights;
//using System.Configuration;
//using System.Web.UI.WebControls;
//using System.Reflection;
//using System.Net;
//using System.Text;
//using System.Text.RegularExpressions;
//using System.Web.Script.Serialization;
//using System.Web.Mvc;

//namespace Rebound.GlobalTrader.Site.Areas.BOM.Data
//{
//    public class SalesBomServiceRequest
//    {
//        string accountname = ConfigurationManager.AppSettings.Get("StorageName");
//        string accesskey = ConfigurationManager.AppSettings.Get("StorageKey1");
//        StorageCredentials creden;
//        CloudStorageAccount acc;
//        CloudBlobClient cbclient;
//        HttpContext context;
//        Company Company = new Company();

//        public SalesBomServiceRequest()
//        {
//            creden = new StorageCredentials(accountname, accesskey);
//            acc = new CloudStorageAccount(creden, useHttps: true);
//            cbclient = acc.CreateCloudBlobClient();
//        }

//        public SalesBomSheetModel FillDefault(SalesBomSheetModel model)
//        {
//            SalesBomServiceRequest salesBomserviceRequest = new SalesBomServiceRequest();
//            DataTable dtable = new DataTable();
//            dtable = salesBomserviceRequest.GetMasterClient();
//            Int32? clientId = SessionManager.ClientID;

//            model.clients = (from DataRow dr in dtable.Rows
//                             select new Models.Client()
//                             {
//                                 ClientId = Convert.ToInt32(dr["ClientId"]),
//                                 ClientName = dr["ClientName"].ToString(),
//                                 ClientCode = dr["ClientCode"].ToString()
//                             }).ToList();


//            model.currency = (from o in salesBomserviceRequest.DropDownSellForClient(clientId)
//                              select new Models.Currency()
//                              {
//                                  CurrencyId = Convert.ToInt32(o.CurrencyId),
//                                  CurrencyDescription = o.CurrencyDescription.ToString(),
//                                  CurrencyCode = o.CurrencyCode.ToString()
//                              }).ToList();
//            return model;
//        }

//        public List<Models.Client> lstClient()
//        {
//            SalesBomServiceRequest salesBomserviceRequest = new SalesBomServiceRequest();
//            DataTable dtable = new DataTable();
//            dtable = salesBomserviceRequest.GetMasterClient();
//            Int32? clientId = SessionManager.ClientID;
//            List<BLL.Client> clients = new List<BLL.Client>();
//            var dt = (from DataRow dr in dtable.Rows
//                      select new Models.Client()
//                      {
//                          ClientId = Convert.ToInt32(dr["ClientId"]),
//                          ClientName = dr["ClientName"].ToString(),
//                          ClientCode = dr["ClientCode"].ToString()
//                      }).ToList();

//            return null;
//        }

//        private DataTable GetMasterClient()
//        {
//            Int32? intMastLoginNo = SessionManager.MasterLoginNo;
//            JsonObject jsn = new JsonObject();
//            JsonObject jsnList = new JsonObject(true);
//            List<BLL.Client> lst = BLL.Client.GeClientByMaster(intMastLoginNo);
//            DataTable dt = new DataTable();
//            dt.Columns.Add("ClientId", typeof(System.Int32));
//            dt.Columns.Add("ClientName", typeof(System.String));
//            dt.Columns.Add("ClientCode", typeof(System.String));
//            for (int i = 0; i < lst.Count; i++)
//            {
//                DataRow row = dt.NewRow();
//                row["ClientId"] = lst[i].ClientId;
//                row["ClientName"] = lst[i].ClientName;
//                row["ClientCode"] = lst[i].ClientCode;
//                dt.Rows.Add(row);
//            }
//            return dt;
//        }

//        public List<BLL.Currency> GetCurrencyForClient(System.Int32? clientId)
//        {
//            return DropDownSellForClient(clientId);
//        }


//        /// <summary>
//        /// DropDownSellForClient
//        /// Calls [usp_dropdown_Currency_Sell_For_Client]
//        /// </summary>
//        private List<BLL.Currency> DropDownSellForClient(System.Int32? clientId)
//        {
//            List<CurrencyDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.Currency.DropDownSellForClient(clientId);
//            if (lstDetails == null)
//            {
//                return new List<BLL.Currency>();
//            }
//            else
//            {
//                List<BLL.Currency> lst = new List<BLL.Currency>();
//                foreach (CurrencyDetails objDetails in lstDetails)
//                {
//                    Rebound.GlobalTrader.BLL.Currency obj = new Rebound.GlobalTrader.BLL.Currency();
//                    obj.CurrencyId = objDetails.CurrencyId;
//                    obj.CurrencyDescription = objDetails.CurrencyDescription;
//                    obj.CurrencyCode = objDetails.CurrencyCode;
//                    lst.Add(obj);
//                    obj = null;
//                }
//                lstDetails = null;
//                return lst;
//            }
//        }

//        public DataTable GetCompanyAndOtherMasterData(string CompanyId)
//        {
//            try
//            {
//                //int companyId = string.IsNullOrEmpty(context.Request.QueryString["CompanyId"]) ? 0 : int.Parse(context.Request.QueryString["CompanyId"]);
//                int companyId = string.IsNullOrEmpty(CompanyId) ? 0 : int.Parse(CompanyId);
//                DataTable dtResult = Stock.GetContactWithCurrency(companyId);
//                //context.Response.Write(ConvertDataTableToJSON(dtResult));
//                //ConvertDataTableToJSON(dtResult)
//                return dtResult;
//            }
//            catch (Exception ex)
//            {
//                //WriteError(ex);
//            }
//            return null;
//        }


//        public List<BLL.Company> AutoSearchSale(string CompanyId, string searchvalue)
//        {
//            try
//            {
//                List<Company> comlst = new List<Company>();
//                //int companyId = string.IsNullOrEmpty(context.Request.QueryString["CompanyId"]) ? 0 : int.Parse(context.Request.QueryString["CompanyId"]);
//                int companyId = string.IsNullOrEmpty(CompanyId) ? 0 : int.Parse(CompanyId);
//                //DataTable dtResult = Stock.GetContactWithCurrency(companyId);
//                comlst = Company.AutoSearchSale(companyId, searchvalue);
//                //context.Response.Write(ConvertDataTableToJSON(dtResult));
//                //ConvertDataTableToJSON(dtResult)
//                return comlst;
//            }
//            catch (Exception ex)
//            {
//                //WriteError(ex);
//            }
//            return null;
//        }

//        public List<CompanyDetails> CompanyNameList(string CompanyName)
//        {
//            Int32? clientId = SessionManager.ClientID;
//            List<CompanyDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.Company.AutoSearchSale(clientId, CompanyName + '%');
//            if (lstDetails == null)
//            {
//                return new List<CompanyDetails>();
//            }
//            else
//            {
//                //List<BLL.Company> lst = new List<BLL.Company>();
//                //foreach (CompanyDetails objDetails in lstDetails)
//                //{
//                //    Rebound.GlobalTrader.BLL.Company obj = new Rebound.GlobalTrader.BLL.Company();
//                //    obj. = objDetails.CurrencyId;
//                //    obj.CurrencyDescription = objDetails.CurrencyDescription;
//                //    obj.CurrencyCode = objDetails.CurrencyCode;
//                //    lst.Add(obj);
//                //    obj = null;
//                //}
//                //lstDetails = null;
//                //return lst;
//                return lstDetails.ToList();
//            }
//        }


//        public HttpContext UploadExcelFileOnCloud(HttpContext Context)
//        {
//            try
//            {
//                context = Context;
//                HttpPostedFile file = context.Request.Files["myfile"];
//                //string strExcelSection = context.Request.Form["section"];
//                string strExcelSection = "UtilitySales_BOMImport";

//                if (file != null && file.ContentLength > 0 && !string.IsNullOrEmpty(strExcelSection) && strExcelSection.Length > 0)
//                {
//                    string strFile = string.Empty;
//                    string[] strArray = strExcelSection.Split('_');

//                    if (strArray.Length == 3 && strArray[0].Length > 1 && strArray[1].Length > 1)
//                    {
//                        //testing code start
//                        string strDir = FileUploadManager.GetTemporaryUploadFilePath();
//                        //if (!Directory.Exists(strDir)) Directory.CreateDirectory(strDir);

//                        //testing code end
//                        string filEextn = Path.GetExtension(file.FileName);
//                        if (filEextn == ".xlsx")
//                            strFile = string.Format(Functions.GetGlobalResource("Misc", "ExcelFileName"), SessionManager.ClientID, strArray[0].ToUpper(), strArray[1], DateTime.Now.ToString("yyyyMMddHHmmssfff"));
//                        else if (filEextn == ".xls")
//                            strFile = string.Format(Functions.GetGlobalResource("Misc", "ExcelXlsFileName"), SessionManager.ClientID, strArray[0].ToUpper(), strArray[1], DateTime.Now.ToString("yyyyMMddHHmmssfff"));
//                        else
//                            strFile = string.Format(Functions.GetGlobalResource("Misc", "ExcelCsvFileName"), SessionManager.ClientID, strArray[0].ToUpper(), strArray[1], DateTime.Now.ToString("yyyyMMddHHmmssfff"));

//                        if (strExcelSection == "Bom_BomImport_BMI" || strExcelSection == "EPO_EpoImport_Stock" || strExcelSection == "StockImport_StockImport_STKI" || strExcelSection == "UtilityBOM_BOMImport_STKI" || strExcelSection == "Utility_StockImport_STKI")
//                        {
//                            System.IO.File.WriteAllBytes(strDir + strFile, ReadData(file.InputStream));
//                            file.InputStream.Position = 0;
//                        }

//                        CloudBlobContainer cont = cbclient.GetContainerReference("gtdocmgmt");
//                        if (cont.Exists())
//                        {
//                            CloudBlobDirectory directory = cont.GetDirectoryReference(strArray[0].ToUpper());
//                            //cont.SetPermissions(new BlobContainerPermissions { PublicAccess = BlobContainerPublicAccessType.Blob });
//                            CloudBlockBlob cblob = directory.GetBlockBlobReference(strFile);
//                            if (filEextn == ".xlsx")
//                                cblob.Properties.ContentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
//                            else if (filEextn == ".xls")
//                                cblob.Properties.ContentType = "application/vnd.ms-excel";
//                            using (Stream fileStream = file.InputStream)
//                            {
//                                cblob.UploadFromStream(fileStream);
//                            }
//                        }
//                        else
//                        {
//                            //container not exit
//                        }
//                        if (cont.Exists())    //if (System.IO.File.Exists(strDir + strFile))
//                        {
//                            //JsonObject jsn = new JsonObject();
//                            JsonObject jsnItem = new JsonObject();
//                            jsnItem.AddVariable("Result", true);
//                            jsnItem.AddVariable("FileName", strFile);
//                            //jsn.AddVariable(jsnItem);
//                            //context.Response.Write(jsnItem.Result);
//                        }
//                    }
//                    else
//                    {
//                        JsonObject jsnItem = new JsonObject();
//                        jsnItem.AddVariable("Result", false);
//                        jsnItem.AddVariable("FileName", "");
//                        context.Response.Write(jsnItem.Result);
//                        jsnItem = null;
//                    }
//                }
//                else
//                {
//                    JsonObject jsnItem = new JsonObject();
//                    jsnItem.AddVariable("Result", false);
//                    jsnItem.AddVariable("FileName", "");
//                    context.Response.Write(jsnItem.Result);
//                    jsnItem = null;
//                }
//            }
//            catch (Exception e)
//            {
//                var ai = new TelemetryClient();
//                ai.TrackTrace("Section: Excelupload");
//                ai.TrackException(e);
//                //JsonObject jsn = new JsonObject();
//                JsonObject jsnItem = new JsonObject();
//                jsnItem.AddVariable("Result", false);
//                jsnItem.AddVariable("FileName", "");
//                //jsn.AddVariable(jsnItem);
//                context.Response.Write(jsnItem.Result);
//            }
//            return context;
//        }

//        public DataTable FillDefaultDDL(HttpContext context)
//        {
//            //salesBomExcelImport model = new salesBomExcelImport();
//            int clientType_con = string.IsNullOrEmpty(context.Request.QueryString["clientType"]) ? 0 : int.Parse(context.Request.QueryString["clientType"]);
//            if (clientType_con == 2)
//            {
//                //LocalSqlServer uk
//                // DataTable dtClient = BLL.Stock.GetClientName(SessionManager.ClientID, clientType_con);
//                //context.Response.Write(ConvertDataTableToJSON(dtClient));

//                DataTable dtClient = GetMasterClient();
//                //context.Response.Write(ConvertDataTableToJSON(dtClient));
//                return dtClient;
//            }
//            return null;
//        }

//        public DataTable ToDataTable<T>(List<T> items)
//        {
//            DataTable dataTable = new DataTable(typeof(T).Name);
//            DataTable dataTable1 = new DataTable(typeof(T).Name);
//            //Get all the properties
//            PropertyInfo[] Props = typeof(T).GetProperties(BindingFlags.Public | BindingFlags.Instance);
//            //foreach (PropertyInfo prop in Props)
//            //{
//            //    //Setting column names as Property names
//            //    dataTable.Columns.Add(prop.Name);
//            //}

//            //foreach (T item in items)
//            //{
//            //    var values = new object[Props.Length];
//            //    for (int i = 0; i < Props.Length; i++)
//            //    {
//            //        //inserting property values to datatable rows
//            //        values[i] = Props[i].GetValue(item, null);
//            //    }
//            //    dataTable.Rows.Add(values);
//            //}

//            //foreach (T item in items)
//            //{
//            //    //var values = new object[Props.Length];
//            //    for (int i = 0; i < Props.Length; i++)
//            //    {
//            //        dataTable1.Columns.Add(Props[i].GetValue(item, null).ToString());
//            //    }

//            //}

//            int j = 0;
//            foreach (T item in items)
//            {
//                if (j == 0)
//                {
//                    var values = new object[Props.Length];
//                    for (int i = 0; i < Props.Length; i++)
//                    {
//                        dataTable1.Columns.Add(Props[i].GetValue(item, null).ToString());
//                    }
//                    j++;
//                }
//                if (j > 0)
//                {
//                    var values = new object[Props.Length];
//                    for (int i = 0; i < Props.Length; i++)
//                    {
//                        //inserting property values to datatable rows
//                        values[i] = Props[i].GetValue(item, null);
//                    }
//                    dataTable1.Rows.Add(values);
//                }
//            }

//            //put a breakpoint here and check datatable
//            return dataTable1;
//        }

//        private byte[] ReadData(Stream stream)
//        {
//            byte[] buffer = new byte[16 * 1024];
//            using (MemoryStream ms = new MemoryStream())
//            {
//                int read;
//                while ((read = stream.Read(buffer, 0, buffer.Length)) > 0)
//                {
//                    ms.Write(buffer, 0, read);
//                }
//                return ms.ToArray();
//            }
//        }


//        public void ImportCSVData(string generatedFilename, string originalFilename, int SelectedClientType, string ClientId, string ColumnHeader, string SalesDefaultCurrency)
//        {
//            try
//            {
//                bool IsLimitExceeded = false;
//                string LimitErrorMessage = "";
//                //string originalFilename = GetFormValue_String("originalFilename");
//                //string generatedFilename = GetFormValue_String("generatedFilename");
//                //string chkcolumnheader = GetFormValue_String("ColumnHeader");
//                //int clientType_con = GetFormValue_Int("SelectedClientType");
//                int clientType_con = SelectedClientType;
//                string chkcolumnheader = ColumnHeader=true?"Yes":"No";
//                //int FormatId = GetFormValue_Int("FormatId");
//                //string Delimiter = GetFormValue_String("Delimiter");
//                //int ClientId=GetFormValue_String("ClientId");

//                string filepathtempfolder = FileUploadManager.GetTemporaryUploadFilePath() + generatedFilename;
//                //string filepath = ConfigurationManager.AppSettings["ExcelDocumentPhysicalURLForAzureBlob"].ToString() + @"STOCKIMPORT/" + generatedFilename;
//                string filepath = ConfigurationManager.AppSettings["ExcelDocumentPhysicalURLForAzureBlob"].ToString() + @"STOCKIMPORT/" + generatedFilename;
//                //String strorageconn = ConfigurationManager.AppSettings.Get("StorageConnectionString");
//                //CloudStorageAccount storageacc = CloudStorageAccount.Parse(strorageconn);
//                // CloudBlobClient client = storageacc.CreateCloudBlobClient();
//                string accountname = ConfigurationManager.AppSettings.Get("StorageName");
//                string accesskey = ConfigurationManager.AppSettings.Get("StorageKey1");
//                StorageCredentials creden = new StorageCredentials(accountname, accesskey);
//                CloudStorageAccount acc = new CloudStorageAccount(creden, useHttps: true);
//                CloudBlobClient client = acc.CreateCloudBlobClient();
//                CloudBlobContainer cont = client.GetContainerReference("gtdocmgmt");
//                if (cont.Exists())
//                {
//                    CloudBlobDirectory directory = cont.GetDirectoryReference("UTILITYBOM");
//                    //CloudBlockBlob cblob = directory.GetBlockBlobReference(GetFormValue_String("generatedFilename"));
//                    CloudBlockBlob cblob = directory.GetBlockBlobReference(generatedFilename);

//                    if (!cblob.Exists())
//                    {
//                        string fileExtension = Path.GetExtension(filepath);
//                        //int SelectedclientId = int.Parse(string.IsNullOrEmpty(GetFormValue_String("ClientId")) ? "0" : GetFormValue_String("ClientId"));
//                        int SelectedclientId = int.Parse(string.IsNullOrEmpty(ClientId) ? "0" : ClientId);
//                        //DataTable dt = fileExtension == ".csv" ? ReadCsvFile(filepath, chkcolumnheader) : ConvertExcelToDataTable(filepathtempfolder, chkcolumnheader, generatedFilename);//ReadExcel(filepath)/or/ReadCsvFile(filepath);
//                        //DataTable dt = fileExtension == ".csv" ? ConvertCSVToDataTableNew(filepathtempfolder, chkcolumnheader, generatedFilename) : ConvertExcelToDataTableNew(filepathtempfolder, chkcolumnheader, generatedFilename);//ReadExcel(filepath)/or/ReadCsvFile(filepath);
//                        //DataTable dt = fileExtension == ".csv" ? ReadCsvFile(filepathtempfolder, chkcolumnheader, generatedFilename) : ConvertExcelToDataTableNew(filepathtempfolder, chkcolumnheader, generatedFilename);
//                        DataTable dt = fileExtension == ".csv" ? ReadCsvFile(filepathtempfolder, chkcolumnheader, generatedFilename) : ConvertExcelToDataTableNew(filepathtempfolder, chkcolumnheader, generatedFilename);

//                        //string DefaultCurrency = GetFormValue_String("DefaultCurrency");
//                        string DefaultCurrency = SalesDefaultCurrency;

//                        int filelogid = 0;
//                        if (dt.Rows.Count <= Convert.ToInt32(ConfigurationManager.AppSettings["MaxUploadRowCount"]))
//                        {
//                            this.SaveBOMManagerExcelHeader(dt, SelectedclientId, clientType_con);
//                            this.SaveBOMManagerExcelData_BulkSave(dt, originalFilename, generatedFilename, SelectedclientId, clientType_con, DefaultCurrency);
//                            //Vinay: 05 May 2021: Dispose unused object
//                        }
//                        else
//                        {
//                            cblob.DeleteIfExists();
//                            IsLimitExceeded = true;
//                            LimitErrorMessage = "Maximum limit should not exceed " + ConfigurationManager.AppSettings["MaxUploadRowCount"].ToString() + " rows.";
//                        }
//                        dt.Dispose();

//                        JsonObject jsn = new JsonObject();
//                        jsn.AddVariable("FileLogId", filelogid);
//                        jsn.AddVariable("IsLimitExceeded", IsLimitExceeded);
//                        jsn.AddVariable("LimitErrorMessage", LimitErrorMessage);
//                        //OutputResult(jsn);
//                        jsn.Dispose(); jsn = null;
//                    }
//                    else
//                    {
//                        throw new Exception("File not uploaded successfully.", new Exception("FileNotFound"));
//                    }
//                    //Vinay: 05 May 2021: Dispose unused object
//                    cont = null;
//                    client = null;
//                    acc = null;
//                }
//            }
//            catch (Exception ex)
//            {
//                var ai = new TelemetryClient();
//                ai.TrackTrace("Exception Header: ImportCSVData");
//                ai.TrackException(ex);
//                //WriteError(ex);
//            }
//        }

//        private void SaveBOMManagerExcelHeader(DataTable dtData, int SelectedclientId, int clientType_con)
//        {
//            try
//            {
//                string columnList = string.Empty;
//                string insertColumnList = string.Empty;
//                int i = 1;
//                foreach (DataColumn column in dtData.Columns)
//                {
//                    if (i <= 15)
//                    {
//                        columnList = columnList + "'" + column.ColumnName + "',";
//                        insertColumnList = insertColumnList + "Column" + i.ToString() + ",";
//                    }
//                    ++i;
//                }
//                if (!string.IsNullOrEmpty(columnList))
//                    columnList = columnList.Substring(0, columnList.Length - 1);
//                if (!string.IsNullOrEmpty(insertColumnList))
//                    insertColumnList = insertColumnList.Substring(0, insertColumnList.Length - 1);

//                //BLL.Stock.SaveBOMExcelHeader(columnList, insertColumnList, SessionManager.ClientID, SelectedclientId, SessionManager.LoginID ?? 0, clientType_con);
//                BOMManagerContract.SaveBOMManagerExcelHeader(columnList, insertColumnList, SessionManager.ClientID, SelectedclientId, SessionManager.LoginID ?? 0, clientType_con);
//            }
//            catch (Exception ex)
//            {
//                var ai = new TelemetryClient();
//                ai.TrackTrace("Exception Header: SaveExcelHeader");
//                ai.TrackException(ex);
//                //WriteError(ex);
//            }
//        }

//        private void SaveBOMManagerExcelData_BulkSave(DataTable dtData, string originalFilename, string generatedFilename, int SelectedclientId, int clientType_con, string DefaultCurrency)
//        {
//            try
//            {
//                DataTable tempStock = new DataTable("BorisGlobalTraderImports.dbo.tbTempBomManagerData");
//                // Copy the DataTable to SQL Server using SqlBulkCopy
//                BOMManagerContract.SaveBOMManagerExcelBulkSave(tempStock, dtData, originalFilename, generatedFilename, SessionManager.LoginID ?? 0, SessionManager.ClientID ?? 0, SelectedclientId, clientType_con, DefaultCurrency);
//            }
//            catch (Exception ex)
//            {
//                var ai = new TelemetryClient();
//                ai.TrackTrace("Exception Header: saveExcelData_BulkSave");
//                ai.TrackException(ex);
//                // WriteError(ex);
//            }
//        }

//        public DataTable ReadCsvFile(string filepath, string chkhead, string FileName)
//        {
//            DataTable dtCsv = new DataTable();
//            dtCsv.Clear();
//            Regex CSVParser = new Regex(",(?=(?:[^\"]*\"[^\"]*\")*(?![^\"]*\"))");
//            try
//            {
//                string Fulltext;
//                WebClient web = new WebClient();
//                System.IO.Stream stream = web.OpenRead(filepath);
//                using (StreamReader sr = new StreamReader(stream, Encoding.Default))
//                {
//                    while (!sr.EndOfStream)
//                    {
//                        Fulltext = sr.ReadToEnd().ToString(); //read full file text  
//                        string[] rows = Fulltext.Split('\n'); //split full file text into rows  
//                        if (chkhead == "YES")
//                        {
//                            for (int i = 0; i < rows.Length - 1; i++)
//                            {
//                                //string[] rowValues = rows[i].Split(','); //split each row with comma to get individual values  
//                                string[] rowValues = CSVParser.Split(rows[i]); //split each row with comma to get individual values  
//                                {
//                                    if (i == 0)
//                                    {
//                                        for (int j = 0; j < rowValues.Length; j++)
//                                        {
//                                            dtCsv.Columns.Add(Functions.CleanDatabaseFilter(Functions.ReplaceLineBreaks(Functions.CleanJunkCharInCSV(Functions.FormatStringForDatabase((rowValues[j].ToString()))))));
//                                        }
//                                    }
//                                    else
//                                    {
//                                        DataRow dr = dtCsv.NewRow();
//                                        for (int k = 0; k < rowValues.Length; k++)
//                                        {
//                                            dr[k] = Functions.CleanJunkCharInCSV(Functions.CleanDatabaseForStockTool((rowValues[k].ToString())));
//                                            //dr[k] = Functions.FormatStringForDatabase(Functions.CleanDatabaseForStockTool(Functions.CleanJunkCharInCSV((rowValues[k].ToString()))));
//                                        }
//                                        dtCsv.Rows.Add(dr); //add other rows 
//                                    }
//                                }
//                            }
//                        }
//                        else
//                        {
//                            for (int i = 0; i < rows.Length - 1; i++)
//                            {
//                                //string[] rowValues = rows[i].Split(','); //split each row with comma to get individual values  
//                                string[] rowValues = CSVParser.Split(rows[i]); //split each row with comma to get individual values  
//                                {
//                                    if (i == 0)
//                                    {
//                                        int counter = 1;
//                                        for (int j = 0; j < rowValues.Length; j++)
//                                        {
//                                            dtCsv.Columns.Add("F" + counter); //Add header if not have header like F1
//                                            counter++;
//                                        }
//                                        DataRow dr = dtCsv.NewRow();
//                                        for (int k = 0; k < rowValues.Length; k++)
//                                        {

//                                            dr[k] = Functions.CleanJunkCharInCSV(Functions.CleanDatabaseForStockTool((rowValues[k].ToString())));
//                                            //dr[k] = Functions.FormatStringForDatabase(Functions.CleanDatabaseForStockTool(Functions.CleanJunkCharInCSV((rowValues[k].ToString()))));
//                                        }
//                                        dtCsv.Rows.Add(dr); //add other rows 
//                                    }
//                                    else
//                                    {
//                                        DataRow dr = dtCsv.NewRow();
//                                        for (int k = 0; k < rowValues.Length; k++)
//                                        {
//                                            dr[k] = Functions.CleanJunkCharInCSV(Functions.CleanDatabaseForStockTool((rowValues[k].ToString())));
//                                            //dr[k] = Functions.FormatStringForDatabase(Functions.CleanDatabaseForStockTool(Functions.CleanJunkCharInCSV((rowValues[k].ToString()))));
//                                        }
//                                        dtCsv.Rows.Add(dr); //add other rows 
//                                    }
//                                }
//                            }
//                        }
//                    }
//                }

//                //Vinay: 05 May 2021: Dispose unused object
//                stream.Dispose();
//                web.Dispose();
//                web = null;
//                CSVParser = null;
//                string Deletetempfolderfile = FileUploadManager.GetTemporaryUploadFilePath() + FileName;
//                System.IO.File.Delete(Deletetempfolderfile);
//            }
//            catch (Exception ex)
//            {
//                var ai = new TelemetryClient();
//                ai.TrackTrace("Exception Header: ReadCSVFile");
//                ai.TrackException(ex);

//                string Deletetempfolderfile = FileUploadManager.GetTemporaryUploadFilePath() + FileName;
//                System.IO.File.Delete(Deletetempfolderfile);
//                throw new Exception("Error Occured while processing the csv file.Kindly review the data in the csv file.", new Exception("CSVDataError"));
//            }
//            return dtCsv;
//        }

//        public DataTable ConvertExcelToDataTableNew(string FilePath, string chkhead, string FileName)
//        {
//            DataTable dt = new DataTable();
//            FileInfo fi = new FileInfo(FilePath);
//            try
//            {
//                if (fi.Exists)
//                {
//                    List<string> sheets = ExcelAdapter.GetSheet(FilePath);
//                    if (sheets.Count > 0)
//                    {
//                        if (chkhead == "YES")
//                        { dt = ExcelAdapter.ReadExcel(FilePath, sheets[0]); }
//                        else
//                        {
//                            dt = ExcelAdapter.ReadExcel(FilePath, sheets[0], false);
//                            int i = 1;
//                            int c = 0;
//                            foreach (DataColumn column in dt.Columns)
//                            {
//                                if (i <= 15)
//                                {
//                                    dt.Columns[c].ColumnName = "F" + i;
//                                }
//                                ++i;
//                                ++c;
//                            }
//                        }
//                    }
//                }
//                string Deletetempfolderfile = FileUploadManager.GetTemporaryUploadFilePath() + FileName;
//                System.IO.File.Delete(Deletetempfolderfile);
//            }
//            catch (Exception ex)
//            {
//                string Deletetempfolderfile = FileUploadManager.GetTemporaryUploadFilePath() + FileName;
//                System.IO.File.Delete(Deletetempfolderfile);
//                throw new Exception("Error Occured while processing the excel file.Kindly review the data in the excel file.", new Exception("ExcelDataError"));
//            }
//            return dt;
//        }


//        private void GetExcelHeaderFrom(string clientType, string ClientId)
//        {
//            try
//            {
//                int clientType_con = string.IsNullOrEmpty(context.Request.QueryString["clientType"]) ? 0 : int.Parse(context.Request.QueryString["clientType"]);
//                int SelectedclientId = string.IsNullOrEmpty(context.Request.QueryString["ClientId"]) ? 0 : int.Parse(context.Request.QueryString["ClientId"]);
//                DataTable dtColumnList = Stock.GetBOMExcelHeaderFrom(SessionManager.ClientID ?? 0, SessionManager.LoginID ?? 0, SelectedclientId, clientType_con);
//                JavaScriptSerializer js = new JavaScriptSerializer();
//                context.Response.Write(ConvertHeaderToJsonObj(dtColumnList));
//            }
//            catch (Exception ex)
//            {
//                var ai = new TelemetryClient();
//                ai.TrackTrace("Exception Header: GetExcelHeaderFrom");
//                ai.TrackException(ex);
//                //WriteError(ex);
//            }
//        }

//        private string ConvertHeaderToJsonObj(DataTable dt)
//        {
//            StringBuilder JsonString = new StringBuilder();
//            if (dt != null && dt.Rows.Count > 0)
//            {
//                //JsonString.Append("[");
//                JsonString.Append("{");
//                JsonString.Append("\"data\":");
//                JsonString.Append("[");
//                for (int i = 0; i < dt.Rows.Count; i++)
//                {
//                    JsonString.Append("{");
//                    for (int j = 0; j < dt.Columns.Count; j++)
//                    {
//                        if (j < dt.Columns.Count - 1)
//                        {
//                            JsonString.Append("\"" + dt.Columns[j].ColumnName.ToString() + "\":" + "\"" + dt.Rows[i][j].ToString().Replace(@"\r", "").Replace(@"\n", "").Replace(@"\", "") + "\",");
//                        }
//                        else if (j == dt.Columns.Count - 1)
//                        {
//                            JsonString.Append("\"" + dt.Columns[j].ColumnName.ToString() + "\":" + "\"" + dt.Rows[i][j].ToString().Replace(@"\r", "").Replace(@"\n", "").Replace(@"\", "") + "\"");
//                        }
//                    }
//                    if (i == dt.Rows.Count - 1)
//                    {
//                        JsonString.Append("}");
//                    }
//                    else
//                    {
//                        JsonString.Append("},");
//                    }
//                }
//                JsonString.Append("]");
//                JsonString.Append("}");
//                //JsonString.Append("]");

//                return Regex.Replace(JsonString.ToString(), @"\r\n?|\n", "");
//            }
//            else
//            {
//                return null;
//            }
//        }

//        public DataTable GenrateBOMManagerData(string RequirementforTraceability, string Type, DateTime DateTimeRequired, string Company_Id, string Company_Name, 
//            string Selectedclient_Id, string ddl_Currency, string insertData_List,string columnLable, string columnName, string contactName, string contact_Id, 
//            string chkOver_Ride, string defaultCurrency_Name, string defaultCurrency_Id, string currencyColumn_Name)
//        {
//            string Column_Lable = "";
//            string insertDataList = string.Empty;
//            string Column_Name = "";
//            DataTable dtGenrateImport = new DataTable();
//            dtGenrateImport.Clear();
//            try
//            {
//                #region Stock checkbox paramete
//                //int RequirementforTraceabilityId = string.IsNullOrEmpty(context.Request.QueryString["RequirementforTraceability"]) ? 0 : int.Parse(context.Request.QueryString["RequirementforTraceability"]);
//                //int TypeId = string.IsNullOrEmpty(context.Request.QueryString["Type"]) ? 0 : int.Parse(context.Request.QueryString["Type"]);
//                //DateTime DateRequired = string.IsNullOrEmpty(context.Request.QueryString["DateRequired"]) ? DateTime.Now : Convert.ToDateTime((context.Request.QueryString["DateRequired"]));
//                ////int clientType_con = string.IsNullOrEmpty(context.Request.QueryString["clientType"]) ? 0 : int.Parse(context.Request.QueryString["clientType"]);
//                //int CompanyId = string.IsNullOrEmpty(context.Request.QueryString["CompanyId"]) ? 0 : int.Parse(context.Request.QueryString["CompanyId"]);
//                //string CompanyNameText = string.IsNullOrEmpty(context.Request.QueryString["CompanyName"]) ? "" : Convert.ToString((context.Request.QueryString["CompanyName"]));
//                ////int recordType = string.IsNullOrEmpty(context.Request.QueryString["recordType"]) ? 0 : int.Parse(context.Request.QueryString["recordType"]);
//                //int SelectedclientId = string.IsNullOrEmpty(context.Request.QueryString["SelectedclientId"]) ? 0 : int.Parse(context.Request.QueryString["SelectedclientId"]);
//                ////checkbox parameter
//                ///
//                int RequirementforTraceabilityId = string.IsNullOrEmpty(RequirementforTraceability) ? 0 : int.Parse(RequirementforTraceability);
//                int TypeId = string.IsNullOrEmpty(Type) ? 0 : int.Parse(Type);
//                DateTime DateRequired = string.IsNullOrEmpty(DateTimeRequired.ToString()) ? DateTime.Now : Convert.ToDateTime(DateTimeRequired);
//                //int clientType_con = string.IsNullOrEmpty(context.Request.QueryString["clientType"]) ? 0 : int.Parse(context.Request.QueryString["clientType"]);
//                int CompanyId = string.IsNullOrEmpty(Company_Id) ? 0 : int.Parse(Company_Id);
//                string CompanyNameText = string.IsNullOrEmpty(Company_Name) ? "" : Convert.ToString(Company_Name);
//                //int recordType = string.IsNullOrEmpty(context.Request.QueryString["recordType"]) ? 0 : int.Parse(context.Request.QueryString["recordType"]);
//                int SelectedclientId = string.IsNullOrEmpty(Selectedclient_Id) ? 0 : int.Parse(Selectedclient_Id);
//                //checkbox parameter
//                #endregion

//                #region Stock  Dropdwon parameter
//                //string ddlCurrency = string.IsNullOrEmpty(context.Request.QueryString["ddlCurrency"]) ? "" : Convert.ToString((context.Request.QueryString["ddlCurrency"]));
//                //string btnImport = string.IsNullOrEmpty(context.Request.QueryString["btnImport"]) ? "" : Convert.ToString((context.Request.QueryString["btnImport"]));
//                ////mapped selected column list
//                //insertDataList = string.IsNullOrEmpty(context.Request.QueryString["insertDataList"]) ? "" : Convert.ToString((context.Request.QueryString["insertDataList"]));
//                //Column_Lable = string.IsNullOrEmpty(context.Request.QueryString["Column_Lable"]) ? "" : Convert.ToString((context.Request.QueryString["Column_Lable"]));
//                //Column_Name = string.IsNullOrEmpty(context.Request.QueryString["Column_Name"]) ? "" : Convert.ToString((context.Request.QueryString["Column_Name"]));

//                //string ContactText = string.IsNullOrEmpty(context.Request.QueryString["ContactName"]) ? "" : Convert.ToString((context.Request.QueryString["ContactName"]));
//                //int ContactId = string.IsNullOrEmpty(context.Request.QueryString["ContactId"]) ? 0 : int.Parse(context.Request.QueryString["ContactId"]);

//                //string FixedCurrency = string.Empty;

//                //bool OverRideCurrency = string.IsNullOrEmpty(context.Request.QueryString["chkOverRide"]) ? false : bool.Parse(context.Request.QueryString["chkOverRide"]);
//                //string DefaultCurrencyName = string.IsNullOrEmpty(context.Request.QueryString["DefaultCurrencyName"]) ? "" : Convert.ToString((context.Request.QueryString["DefaultCurrencyName"]));
//                //int DefaultCurrencyId = string.IsNullOrEmpty(context.Request.QueryString["DefaultCurrencyId"]) ? 0 : int.Parse(context.Request.QueryString["DefaultCurrencyId"]);
//                //string CurrencyColumnName = string.IsNullOrEmpty(context.Request.QueryString["CurrencyColumnName"]) ? "" : Convert.ToString((context.Request.QueryString["CurrencyColumnName"]));

//                string ddlCurrency = string.IsNullOrEmpty(ddl_Currency) ? "" : Convert.ToString(ddl_Currency);
//                //string btnImport = string.IsNullOrEmpty(context.Request.QueryString["btnImport"]) ? "" : Convert.ToString((context.Request.QueryString["btnImport"]));
//                //mapped selected column list
//                insertDataList = string.IsNullOrEmpty(insertData_List) ? "" : Convert.ToString(insertData_List);
//                Column_Lable = string.IsNullOrEmpty(columnLable) ? "" : Convert.ToString(columnLable);
//                Column_Name = string.IsNullOrEmpty(columnName) ? "" : Convert.ToString(columnName);

//                string ContactText = string.IsNullOrEmpty(contactName) ? "" : Convert.ToString(contactName);
//                int ContactId = string.IsNullOrEmpty(contact_Id) ? 0 : int.Parse(contact_Id);
//                string FixedCurrency = string.Empty;
//                bool OverRideCurrency = string.IsNullOrEmpty(chkOver_Ride) ? false : bool.Parse(chkOver_Ride);
//                string DefaultCurrencyName = string.IsNullOrEmpty(defaultCurrency_Name) ? "" : Convert.ToString(defaultCurrency_Name);
//                int DefaultCurrencyId = string.IsNullOrEmpty(defaultCurrency_Id) ? 0 : int.Parse(defaultCurrency_Id);
//                string CurrencyColumnName = string.IsNullOrEmpty(currencyColumn_Name) ? "" : Convert.ToString(currencyColumn_Name);

//                if (OverRideCurrency)
//                    ddlCurrency = DefaultCurrencyName;
//                #endregion

//                #region Stock Import Genrate JSON Table
//                int displayLength = 20;//string.IsNullOrEmpty(context.Request.Params["Length"]) ? 20 : int.Parse(context.Request.Params["Length"]);//int.Parse(context.Request.Params["Length"]);
//                int displayStart = 0;//string.IsNullOrEmpty(context.Request.Params["Start"]) ? 0 : int.Parse(context.Request.Params["Start"]);//int.Parse(context.Request.Params["Start"]);
//                int sortCol = 0;//string.IsNullOrEmpty(context.Request.Params["order[0][column]"]) ? 0 : int.Parse(context.Request.Params["order[0][column]"]);// int.Parse(context.Request.Params["order[0][column]"]);
//                string sortDir = "";//"asc";// context.Request.Params["order[0][dir]"];
//                string search = "";//context.Request.Params["search[value]"];
//                dtGenrateImport = BOMManagerContract.GetBOMManagerGenrateTempData(displayLength, displayStart, sortCol, sortDir, search, SessionManager.LoginID ?? 0, SessionManager.ClientID ?? 0, SelectedclientId, insertDataList.TrimEnd(','), ddlCurrency, CompanyNameText, 0, Column_Lable.TrimEnd(','), Column_Name.TrimEnd(','), ContactText, OverRideCurrency, DefaultCurrencyName, CurrencyColumnName, DefaultCurrencyId);
//                int total = 20;//Convert.ToInt32(dtGenrateImport.Rows.Count == 0 ? "0" : dtGenrateImport.Rows[0]["TotalCount"].ToString());
//                dtGenrateImport.Columns.Remove("TotalCount");
//                dtGenrateImport.Columns.Remove("RowNum");

//                if (!OverRideCurrency)
//                    if (dtGenrateImport.Columns.Contains("Currency"))
//                        foreach (DataRow dr in dtGenrateImport.Rows) // search whole table
//                        {
//                            if (string.IsNullOrEmpty(dr["Currency"].ToString())) // if id==2
//                                dr["Currency"] = DefaultCurrencyName; //change the name //break; break or not depending on you
//                        }

//                var serializer = new System.Web.Script.Serialization.JavaScriptSerializer();

//                TestData t = new TestData();
//                TestData1 t1 = new TestData1();
//                List<columnsinfo> _col = new List<columnsinfo>();

//                for (int i = 0; i <= dtGenrateImport.Columns.Count - 1; i++)
//                {
//                    _col.Add(new columnsinfo { title = dtGenrateImport.Columns[i].ColumnName, data = dtGenrateImport.Columns[i].ColumnName });
//                }
//                t1.columns = _col;
//                string col = (string)serializer.Serialize(_col);
//                t.columns = col;
//                var lst = dtGenrateImport.AsEnumerable().Select(r => r.Table.Columns.Cast<DataColumn>().Select(c => new KeyValuePair<string, object>(c.ColumnName, r[c.Ordinal])).ToDictionary(z => z.Key, z => z.Value)).ToList();
//                t1.data = lst;
//                string data = serializer.Serialize(lst);
//                t.data = data;
//                string strNewDate = serializer.Serialize(t1);
//                string teststring = "{\"iTotalRecords\":" + total + "," + "\"iTotalDisplayRecords\":" + total + "," + strNewDate.TrimStart('{');
//                //context.Response.Write(teststring);
//                //return teststring;
//                //return dtGenrateImport;
//                List<TestData> tdata = new List<TestData>();
//                tdata.Add(t);
//                return dtGenrateImport;
//                //return tdata;
//                #endregion
//            }
//            catch (Exception ex)
//            {
//                var ai = new TelemetryClient();
//                ai.TrackTrace("Exception Header: GenrateBOMManagerData");
//                ai.TrackException(ex);
//                TestData1 err = new TestData1();
//                err.IsError = true;
//                err.ErrorMessage = ex.Message;
//                //err.ErrorMessage += "<br />" + ex.StackTrace.Replace(System.Environment.NewLine, "<br />");
//                //context.Response.Write(new JavaScriptSerializer().Serialize(err));
//                //WriteError(ex);
//            }
//            return null;
//        }



//        public string ImportBOMManagerdata( HttpContextBase httpContext)
//        {
//            //string ClientId, string company_Id, string companyName_Text, string selectedclient_Id, string ddlCurrency_short,
//            //string insertDataList_Colmn, string columnLable_text, string column_Name, string contactName, string chkOverRide,
//            //string digitalCurrency_Name, string currencyColumn_Name, string requiremenTraceability, string type, string currentDateTime, string BOMName
//            //string chkcolumnheader = httpContext["chkcolumnheader"];
//            string Column_Lable = "";
//            string insertDataList = "";
//            string Column_Name = "";

//            ////string selectedclientId = (context["ClientId"]);
//            ////string digitalCurrency = context["DigitalCurrency"];
//            ////string defaultcurrency = context["Defaultcurrency"];
//            ////string selectedClientType = context["SelectedClientType"];
//            ////string chkcolumnheader = context["chkcolumnheader"];
//            ////string Type = context["Type"];
//            ////string DateTimeRequired = context["DateRequired"];
//            ////string ActionPerformName = context["ActionPerform"];
//            ////string SaveImportOrHubRFQData = context["SaveImportOrHubRFQ"];
//            ////string ddlCurrencyname = context["ddlCurrency"];
//            ////string btnImportName = context["btnImport"];
//            ////string Column_LableName = context["Column_Lable"];
//            ////string insertDataListName = context["insertDataList"];
//            ////string Column_Namedata = context["Column_Name"];
//            ////string searchvalue = context["search"];

//            ////string BomUserName = context["BOMName"];
//            ////string selectedCompanyName = context["Company"];
//            ////string ContactPersonName = context["ContactName"];
//            ////string SalesmanPersonId = context["SalesmanId"];
//            ////string SelectedCompanyId = context["CompanyId"];
//            ////string SelectedContactId = context["ContactId"];
//            ////string ApplyPartWatchId = context["ApplyPartWatch"];
//            ////string SelectedchkOverRide = context["chkOverRide"];
//            ////string SelectedDefaultCurrencyId = context["DefaultCurrencyId"];

//            ////string selectedclientId = ClientId;
//            ////string digitalCurrency = context["DigitalCurrency"];
//            ////string defaultcurrency = context["Defaultcurrency"];
//            ////string selectedClientType = context["SelectedClientType"];
//            ////string chkcolumnheader = context["chkcolumnheader"];
//            ////string Type = context["Type"];
//            ////string DateTimeRequired = context["DateRequired"];
//            ////string ActionPerformName = context["ActionPerform"];
//            ////string SaveImportOrHubRFQData = context["SaveImportOrHubRFQ"];
//            ////string ddlCurrencyname = context["ddlCurrency"];
//            ////string btnImportName = context["btnImport"];
//            ////string Column_LableName = context["Column_Lable"];
//            ////string insertDataListName = context["insertDataList"];
//            ////string Column_Namedata = context["Column_Name"];
//            ////string searchvalue = context["search"];

//            ////string BomUserName = context["BOMName"];
//            ////string selectedCompanyName = context["Company"];
//            ////string ContactPersonName = context["ContactName"];
//            ////string SalesmanPersonId = context["SalesmanId"];
//            ////string SelectedCompanyId = context["CompanyId"];
//            ////string SelectedContactId = context["ContactId"];
//            ////string ApplyPartWatchId = context["ApplyPartWatch"];
//            ////string SelectedchkOverRide = context["chkOverRide"];
//            ////string SelectedDefaultCurrencyId = context["DefaultCurrencyId"];


//            ////int ReqforTraceabilityId = string.IsNullOrEmpty(context.Request.QueryString["requirementforTraceability"]) ? 0 : int.Parse(context.Request.QueryString["RequirementforTraceability"]);

//            //try
//            //{
//            //    #region Bom tool paramete
//            //    new Errorlog().LogMessage("BOM Import method started " + DateTime.UtcNow.ToString());
//            //    //int ReqforTraceabilityId = string.IsNullOrEmpty(context.Request.QueryString["requirementforTraceability"]) ? 0 : int.Parse(context.Request.QueryString["RequirementforTraceability"]);
//            //    //int TypeId = string.IsNullOrEmpty(context.Request.QueryString["Type"]) ? 0 : int.Parse(context.Request.QueryString["Type"]);
//            //    //DateTime DateRequired = string.IsNullOrEmpty(context.Request.QueryString["DateRequired"]) ? DateTime.Now : Convert.ToDateTime((context.Request.QueryString["DateRequired"]));
//            //    //int SelectedclientId = string.IsNullOrEmpty(context.Request.QueryString["SelectedclientId"]) ? 0 : int.Parse(context.Request.QueryString["SelectedclientId"]);
//            //    //string ActionPerform = string.IsNullOrEmpty(context.Request.QueryString["ActionPerform"]) ? "" : Convert.ToString((context.Request.QueryString["ActionPerform"]));
//            //    //string SaveImportOrHubRFQ = string.IsNullOrEmpty(context.Request.QueryString["SaveImportOrHubRFQ"]) ? "" : Convert.ToString((context.Request.QueryString["SaveImportOrHubRFQ"]));

//            //    int ReqforTraceabilityId = string.IsNullOrEmpty(selectedclientId) ? 0 : int.Parse(selectedclientId);
//            //    int TypeId = string.IsNullOrEmpty(Type) ? 0 : int.Parse(Type);
//            //    DateTime DateRequired = string.IsNullOrEmpty(DateTimeRequired) ? DateTime.Now : Convert.ToDateTime(DateTimeRequired);
//            //    int SelectedclientId = string.IsNullOrEmpty(selectedclientId) ? 0 : int.Parse(selectedclientId);
//            //    string ActionPerform = string.IsNullOrEmpty(ActionPerformName) ? "" : Convert.ToString(ActionPerformName);
//            //    string SaveImportOrHubRFQ = string.IsNullOrEmpty(SaveImportOrHubRFQData) ? "" : Convert.ToString(SaveImportOrHubRFQData);
//            //    //checkbox parameter
//            //    #endregion

//            //    #region Bom  Dropdwon parameter
//            //    //string ddlCurrency = string.IsNullOrEmpty(context.Request.QueryString["ddlCurrency"]) ? "" : Convert.ToString((context.Request.QueryString["ddlCurrency"]));
//            //    //string btnImport = string.IsNullOrEmpty(context.Request.QueryString["btnImport"]) ? "" : Convert.ToString((context.Request.QueryString["btnImport"]));
//            //    ////mapped selected column list
//            //    //Column_Lable = string.IsNullOrEmpty(context.Request.QueryString["Column_Lable"]) ? "" : Convert.ToString((context.Request.QueryString["Column_Lable"]));
//            //    //insertDataList = string.IsNullOrEmpty(context.Request.QueryString["insertDataList"]) ? "" : Convert.ToString((context.Request.QueryString["insertDataList"]));
//            //    //Column_Name = string.IsNullOrEmpty(context.Request.QueryString["Column_Name"]) ? "" : Convert.ToString((context.Request.QueryString["Column_Name"]));

//            //    string ddlCurrency = string.IsNullOrEmpty(ddlCurrencyname) ? "" : Convert.ToString(ddlCurrencyname);
//            //    string btnImport = string.IsNullOrEmpty(btnImportName) ? "" : Convert.ToString(btnImportName);
//            //    //mapped selected column list
//            //    Column_Lable = string.IsNullOrEmpty(Column_LableName) ? "" : Convert.ToString(Column_LableName);
//            //    insertDataList = string.IsNullOrEmpty(insertDataListName) ? "" : Convert.ToString(insertDataListName);
//            //    Column_Name = string.IsNullOrEmpty(Column_Namedata) ? "" : Convert.ToString(Column_Namedata);
//            //    #endregion

//            //    DataTable dtcount = new DataTable();
//            //    dtcount.Clear();
//            //    int displayLength = 11;//int.Parse(context.Request.Params["Length"]);
//            //    int displayStart = 0;//int.Parse(context.Request.Params["Start"]);
//            //    int sortCol = 0;// int.Parse(context.Request.Params["order[0][column]"]);
//            //    string sortDir = "asc";// context.Request.Params["order[0][dir]"];
//            //    //string search = context.Request.Params["search[value]"];
//            //    string search = searchvalue;
//            //    dtcount = BOMManagerContract.GetBOMManagerDetailFromTemp(displayLength, displayStart, sortCol, sortDir, search, SessionManager.LoginID ?? 0, SessionManager.ClientID ?? 0, SelectedclientId, 0);
//            //    string TotalCount = dtcount.Rows[0]["TotalCount"].ToString();
//            //    string OriginalFilename = dtcount.Rows[0]["OriginalFilename"].ToString();

//            //    //string BomName = string.IsNullOrEmpty(context.Request.QueryString["BomName"]) ? "" : Convert.ToString((context.Request.QueryString["BomName"]));
//            //    //string CompanyName = string.IsNullOrEmpty(context.Request.QueryString["CompanyName"]) ? "" : Convert.ToString((context.Request.QueryString["CompanyName"]));
//            //    //string ContactName = string.IsNullOrEmpty(context.Request.QueryString["ContactName"]) ? "" : Convert.ToString((context.Request.QueryString["ContactName"]));
//            //    //int SalesmanId = string.IsNullOrEmpty(context.Request.QueryString["SalesmanId"]) ? 0 : int.Parse(context.Request.QueryString["SalesmanId"]);
//            //    //int CompanyId = string.IsNullOrEmpty(context.Request.QueryString["CompanyId"]) ? 0 : int.Parse(context.Request.QueryString["CompanyId"]);
//            //    //int ContactId = string.IsNullOrEmpty(context.Request.QueryString["ContactId"]) ? 0 : int.Parse(context.Request.QueryString["ContactId"]);
//            //    ////bool PartWatch = string.IsNullOrEmpty(context.Request.QueryString["PartWatch"]) ? false : bool.Parse(context.Request.QueryString["PartWatch"]);
//            //    //bool PartWatch = string.IsNullOrEmpty(context.Request.QueryString["ApplyPartWatch"]) ? false : bool.Parse(context.Request.QueryString["ApplyPartWatch"]);

//            //    string BomName = string.IsNullOrEmpty(BomUserName) ? "" : Convert.ToString(BomUserName);
//            //    string CompanyName = string.IsNullOrEmpty(selectedCompanyName) ? "" : Convert.ToString(selectedCompanyName);
//            //    string ContactName = string.IsNullOrEmpty(ContactPersonName) ? "" : Convert.ToString(ContactPersonName);
//            //    int SalesmanId = string.IsNullOrEmpty(SalesmanPersonId) ? 0 : int.Parse(SalesmanPersonId);
//            //    int CompanyId = string.IsNullOrEmpty(SelectedCompanyId) ? 0 : int.Parse(SelectedCompanyId);
//            //    int ContactId = string.IsNullOrEmpty(SelectedContactId) ? 0 : int.Parse(SelectedContactId);
//            //    //bool PartWatch = string.IsNullOrEmpty(context.Request.QueryString["PartWatch"]) ? false : bool.Parse(context.Request.QueryString["PartWatch"]);
//            //    bool PartWatch = string.IsNullOrEmpty(ApplyPartWatchId) ? false : bool.Parse(ApplyPartWatchId);

//            //    #region Stock Import Save data in SQL Table
//            //    if (ActionPerform == "ImportData")
//            //    {
//            //        string fileColName = "BOMInfoName";
//            //        string FixedCurrency = string.Empty;
//            //        //bool OverRideCurrency = string.IsNullOrEmpty(context.Request.QueryString["chkOverRide"]) ? false : bool.Parse(context.Request.QueryString["chkOverRide"]);
//            //        //string DefaultCurrencyName = string.IsNullOrEmpty(context.Request.QueryString["DefaultCurrencyName"]) ? "" : Convert.ToString((context.Request.QueryString["DefaultCurrencyName"]));
//            //        //int DefaultCurrencyId = string.IsNullOrEmpty(context.Request.QueryString["DefaultCurrencyId"]) ? 0 : int.Parse(context.Request.QueryString["DefaultCurrencyId"]);

//            //        bool OverRideCurrency = string.IsNullOrEmpty(SelectedchkOverRide) ? false : bool.Parse(SelectedchkOverRide);
//            //        string DefaultCurrencyName = string.IsNullOrEmpty(defaultcurrency) ? "" : Convert.ToString(defaultcurrency);
//            //        int DefaultCurrencyId = string.IsNullOrEmpty(SelectedDefaultCurrencyId) ? 0 : int.Parse(SelectedDefaultCurrencyId);

//            //        if (OverRideCurrency)
//            //        {
//            //            ddlCurrency = DefaultCurrencyName;
//            //        }
//            //        else
//            //        {
//            //            ddlCurrency = "";
//            //        }

//            //        string insertcolumndata = insertDataList.TrimEnd(',');
//            //        string errorMessage = "";
//            //        string NewBomCode = "";
//            //        int NewBomid = 0;
//            //        int recordCount = BOMManagerContract.SaveBOMManagerImportData(SessionManager.LoginID ?? 0, SessionManager.ClientID ?? 0, SelectedclientId, Column_Lable.TrimEnd(','), Column_Name.TrimEnd(','), insertcolumndata, fileColName, ddlCurrency, out errorMessage, BomName, CompanyName, ContactName, SalesmanId, CompanyId, ContactId, PartWatch, DefaultCurrencyId, OverRideCurrency, SaveImportOrHubRFQ, out NewBomCode, out NewBomid, ReqforTraceabilityId, TypeId, DateRequired);
//            //        new Errorlog().LogMessage("records imported " + TotalCount.ToString());
//            //        new Errorlog().LogMessage("BOM Import method finish " + DateTime.UtcNow.ToString());
//            //        //context.Response.Write(TotalCount+","+OriginalFilename);
//            //        ////context.Response.Write(recordCount + "," + OriginalFilename + "," + errorMessage + "," + NewBomid + "," + NewBomCode);
//            //        return null;
//            //    }
//            //    #endregion
//            //}
//            //catch (Exception ex)
//            //{
//            //    var ai = new TelemetryClient();
//            //    ai.TrackTrace("Exception Header: ImportBOMdata");
//            //    ai.TrackException(ex);
//            //    new Errorlog().LogMessage("ImportBOMdata method : " + ex.InnerException.Message);
//            //    TestData1 err = new TestData1();
//            //    err.IsError = true;
//            //    err.ErrorMessage = ex.InnerException.Message;
//            //    //err.ErrorMessage += "<br />" + ex.StackTrace.Replace(System.Environment.NewLine, "<br />");
//            //    //context.Response.Write(new JavaScriptSerializer().Serialize(err));
//            //    //WriteError(ex);
//            //}
//            return null;
//        }

//    }

//    public class TestData
//    {
//        public string jsondata { get; set; }
//        public string columns { get; set; }
//        public string data { get; set; }
//    }

//    public class TestData1
//    {
//        public List<columnsinfo> columns { get; set; }
//        public List<Dictionary<string, object>> data { get; set; }
//        public bool IsError { get; set; }
//        public string ErrorMessage { get; set; }
//    }

//    public class columnsinfo
//    {
//        public string title { get; set; }
//        public string data { get; set; }
//    }

//    public static class ExcelColumn
//    {
//        public static IList<T> ExcelColumnNameToList<T>(this DataTable table) where T : new()
//        {
//            IList<PropertyInfo> properties = typeof(T).GetProperties().ToList();
//            IList<T> result = new List<T>();

//            foreach (var row in table.Rows)
//            {
//                var item = CreateItemFromRow<T>((DataRow)row, properties);
//                result.Add(item);
//            }

//            return result;
//        }

//        private static T CreateItemFromRow<T>(DataRow row, IList<PropertyInfo> properties) where T : new()
//        {
//            T item = new T();
//            foreach (var property in properties)
//            {
//                property.SetValue(item, row[property.Name], null);
//            }
//            return item;
//        }
//    }
//}