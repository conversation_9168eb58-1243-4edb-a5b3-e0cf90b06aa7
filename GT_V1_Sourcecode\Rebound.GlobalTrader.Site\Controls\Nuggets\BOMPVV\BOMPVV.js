Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Nuggets");Rebound.GlobalTrader.Site.Controls.Nuggets.BOMPVV=function(n){Rebound.GlobalTrader.Site.Controls.Nuggets.BOMPVV.initializeBase(this,[n]);this._intBOMID=-1;this._blnHasRequirement=!1;this._blnRequestedToPoHub=!1;this._blnRelease=!1;this._isAddButtonEnable=!0;this._isPurchaseHub=!1;this._intCurrencyNo=-1;this._BomCode="";this._BomName="";this._BomCompanyName="";this._BomCompanyNo=0;this._intContact2No=-1;this._stringCurrency=null;this._inActive=!1;this._BomContactname="";this._BomContactNo=0;this._CurrentSupplier="";this._QuoteRequired="";this._blnAllHasDelDate=!1;this._blnAllHasProduct=!1;this._blnCanReleaseAll=!1;this._blnAllItemHasSourcing=!1;this.BOMStatus="";this._isClosed=!1;this._UpdatedBy=null;this._blnCanNoBidAll=!0;this._isNoBidCount=!1;this._RequestToPOHubBy=-1;this._UpdateByPH=-1;this._blnReqInValid=!1;this._ValidMessage="";this._BomClientNo=-1;this._blnMerginCanReleaseAll=!1;this._UnitBuyPrice=null;this._UnitSellPrice=null;this._IsRecord=!1;this._IsView=!1};Rebound.GlobalTrader.Site.Controls.Nuggets.BOMPVV.prototype={get_intBOMID:function(){return this._intBOMID},set_intBOMID:function(n){this._intBOMID!==n&&(this._intBOMID=n)},get_ibtnEdit:function(){return this._ibtnEdit},set_ibtnEdit:function(n){this._ibtnEdit!==n&&(this._ibtnEdit=n)},get_ibtnDelete:function(){return this._ibtnDelete},set_ibtnDelete:function(n){this._ibtnDelete!==n&&(this._ibtnDelete=n)},get_blnPOHub:function(){return this._blnPOHub},set_blnPOHub:function(n){this._blnPOHub!==n&&(this._blnPOHub=n)},get_IsDiffrentClient:function(){return this._IsDiffrentClient},set_IsDiffrentClient:function(n){this._IsDiffrentClient!==n&&(this._IsDiffrentClient=n)},get_ClientId:function(){return this._ClientId},set_ClientId:function(n){this._ClientId!==n&&(this._ClientId=n)},get_tblPVVBOM:function(){return this._tblPVVBOM},set_tblPVVBOM:function(n){this._tblPVVBOM!==n&&(this._tblPVVBOM=n)},get_ibtnView:function(){return this._ibtnView},set_ibtnView:function(n){this._ibtnView!==n&&(this._ibtnView=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Nuggets.BOMPVV.callBaseMethod(this,"initialize");this._strPathToData="controls/Nuggets/BOMPVV";this._strDataObject="BOMPVV";this.addRefreshEvent(Function.createDelegate(this,this.getData));this.showLoading(!1);this.showContent(!0);this.showContentLoading(!1);this.getData();this.GetCheckData();(this._ibtnEdit||this._ibtnView)&&(this._ibtnEdit&&$R_IBTN.addClick(this._ibtnEdit,Function.createDelegate(this,this.showEditForm)),this._ibtnView&&$R_IBTN.addClick(this._ibtnView,Function.createDelegate(this,this.showEditViewForm)),this._frmEdit=$find(this._aryFormIDs[0]),this._frmEdit._intBOMID=this._intBOMID,this._frmEdit._BomCode=this._BomCode,this._frmEdit._BomName=this._BomName,this._frmEdit._BomCompanyName=this._BomCompanyName,this._frmEdit._BomCompanyNo=this._BomCompanyNo,this._frmEdit.addCancel(Function.createDelegate(this,this.cancelEdit)),this._frmEdit.addSaveComplete(Function.createDelegate(this,this.saveEditComplete)));this._ibtnDelete&&($R_IBTN.addClick(this._ibtnDelete,Function.createDelegate(this,this.showDeleteForm)),this._frmDelete=$find(this._aryFormIDs[1]),this._frmDelete._intBOMID=this._intBOMID,this._frmDelete._BomCode=this._BomCode,this._frmDelete._BomName=this._BomName,this._frmDelete._BomCompanyName=this._BomCompanyName,this._frmDelete._BomCompanyNo=this._BomCompanyNo,this._frmDelete.addNotConfirmed(Function.createDelegate(this,this.cancelDelete)),this._frmDelete.addSaveComplete(Function.createDelegate(this,this.saveDeleteComplete)));this.getData()},dispose:function(){this.isDisposed||(this._ibtnEdit&&$R_IBTN.clearHandlers(this._ibtnEdit),this._ibtnDelete&&$R_IBTN.clearHandlers(this._ibtnDelete),this._tblPVVBOM&&this._tblPVVBOM.dispose(),this._tblPVVBOM=null,this._frmEdit&&this._frmEdit.dispose(),this._frmDelete&&this._frmDelete.dispose(),this._IsRecord=null,this._intBOMID=null,this._ibtnEdit=null,this._ibtnDelete=null,this._frmEdit=null,this._frmDelete=null,this._IsDiffrentClient=null,this._ClientId=null,this._blnHasRequirement=null,this._blnPOHub=null,this._blnRequestedToPoHub=null,this._blnRelease=null,this._intCurrencyNo=null,this._blnAllHasDelDate=null,this._blnAllHasProduct=null,this._blnCanReleaseAll=null,this._blnAllItemHasSourcing=null,this._ibtnClose=null,this._ibtnNoBid=null,this._ibtnNote=null,this._ValidMessage=null,this._intContact2No=null,this._ibtnCrossMatch=null,this._blnMerginCanReleaseAll=null,this._UnitBuyPrice=null,this._UnitSellPrice=null,this._IsView=null,this._PVVAnswerId=null,Rebound.GlobalTrader.Site.Controls.Nuggets.BOMPVV.callBaseMethod(this,"dispose"))},GetCheckData:function(){var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/BOMPVV");n.set_DataObject("BOMPVV");n.set_DataAction("GetCheckData");n.addParameter("id",this._intBOMID);n.addDataOK(Function.createDelegate(this,this.getDatacheckOK));n.addError(Function.createDelegate(this,this.getDataError));n.addTimeout(Function.createDelegate(this,this.getDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getDatacheckOK:function(n){var t=n._result;t.Results.length==0?($R_FN.showElement(this._ibtnEdit,!1),$("#ctl00_cphMain_ctlBOMPVV_ctlDB_ctl12_lblPVVBOMIHS").show(),document.getElementById("ctl00_cphMain_ctlBOMPVV_ctlDB_ctl12_lblPVVBOMIHS").setAttribute("title","for visibility of Edit button. Please Add PPV/ BOM Qualification  Question,s from company Setting in Setup")):($R_FN.showElement(this._ibtnEdit,!0),$("#ctl00_cphMain_ctlBOMPVV_ctlDB_ctl12_lblPVVBOMIHS").hide())},getData:function(){this.getData_Start();this.showContent(!0);var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData(this._strPathToData);n.set_DataObject(this._strDataObject);n.set_DataAction("GetData");n.addParameter("id",this._intBOMID);n.addDataOK(Function.createDelegate(this,this.getDataOK));n.addError(Function.createDelegate(this,this.getDataError));n.addTimeout(Function.createDelegate(this,this.getDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getDataOK:function(n){var i=n._result,u,r,t;if(this._tblPVVBOM.clearTable(),this.showLoading(!1),i.Items){for(r=0;r<i.Items.length;r++)t=i.Items[r],this._BomCompanyNo=t.PVVAnswerId,u=[$R_FN.setCleanTextValue(t.PVVQuestionName),$R_FN.setCleanTextValue(t.PVVAnswerName)],this._tblPVVBOM.addRow(u,t.ID,!1),t=null,u=null,this._tblPVVBOM.resizeColumns();this._IsRecord=i.Items.length>0?!0:!1}this._inActive=this._BomCompanyNo==0?!1:!0;this.enableButtons(!0);this.getDataOK_End()},getDataError:function(n){this.showError(!0,n.get_ErrorMessage())},enableButtons:function(n){n?(this._ibtnEdit&&$R_IBTN.enableButton(this._ibtnEdit,!this._blnPOHub),this._ibtnDelete&&$R_IBTN.enableButton(this._ibtnDelete,n&&!this._blnPOHub&&this._IsRecord&&this._inActive),this._ibtnView&&$R_IBTN.enableButton(this._ibtnView,n&&this._IsRecord)):(this._ibtnEdit&&$R_IBTN.enableButton(this._ibtnEdit,!1),this._ibtnDelete&&$R_IBTN.enableButton(this._ibtnDelete,!1),this._ibtnView&&$R_IBTN.enableButton(this._ibtnView,!1))},showEditForm:function(){this._frmEdit._intBOMID=this._intBOMID;this._frmEdit._IsView=!1;this.showForm(this._frmEdit,!0)},showEditViewForm:function(){this._frmEdit._intBOMID=this._intBOMID;this._frmEdit._IsView=!0;this.showForm(this._frmEdit,!0)},hideEditForm:function(){this.showForm(this._frmEdit,!1)},cancelEdit:function(){this.hideEditForm()},saveEditComplete:function(){this.hideEditForm();this.showSavedOK(!0,$R_RES.ChangesSavedSuccessfully);this.getData()},showDeleteForm:function(){this._frmDelete._intBOMID=this._intBOMID;this.showForm(this._frmDelete,!0)},hideDeleteForm:function(){this.showForm(this._frmDelete,!1)},cancelDelete:function(){this.hideDeleteForm()},saveDeleteComplete:function(){this.hideEditForm();this.showSavedOK(!0,$R_RES.ChangesSavedSuccessfully);this.getData()}};Rebound.GlobalTrader.Site.Controls.Nuggets.BOMPVV.registerClass("Rebound.GlobalTrader.Site.Controls.Nuggets.BOMPVV",Rebound.GlobalTrader.Site.Controls.Nuggets.Base);