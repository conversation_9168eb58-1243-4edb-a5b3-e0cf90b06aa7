﻿GO
IF NOT EXISTS (SELECT * FROM [dbo].[tbApiURLKey] WHERE HostName = 'api.digikey.com' AND ApiName = 'Digikey')
BEGIN
    INSERT INTO [dbo].[tbApiURLKey]
           ([Url]
           ,[License<PERSON>ey]
           ,[HostName]
           ,[SourceName]
           ,[ModuleName]
           ,[ApiName]
           ,[ApiShortName]
           ,[UpdatedBy])
     VALUES
           ('https://api.digikey.com/products/v4/search/keyword'
           ,''
           ,'api.digikey.com'
           ,'Sourcing'
           ,'DG'
           ,'Digikey'
           ,'DG'
           ,6675)
END

GO