Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.BOMItems_ConfirmRemovePartwatch=function(n){Rebound.GlobalTrader.Site.Controls.Forms.BOMItems_ConfirmRemovePartwatch.initializeBase(this,[n]);this._intCustomerRequirementID=-1;this._intBOMID=-1;this._ReqIds="";this._intBOMNo=-1;this._RequestToPOHubBy=-1;this._UpdateByPH=-1;this._BomName=null;this._BomCompanyNo=-1;this._intContact2No=-1;this._reqSalespeson=""};Rebound.GlobalTrader.Site.Controls.Forms.BOMItems_ConfirmRemovePartwatch.prototype={get_BomCode:function(){return this._BomCode},set_BomCode:function(n){this._BomCode!==n&&(this._BomCode=n)},get_BomName:function(){return this._BomName},set_BomName:function(n){this._BomName!==n&&(this._BomName=n)},get_BomCompanyName:function(){return this._BomCompanyName},set_BomCompanyName:function(n){this._BomCompanyName!==n&&(this._BomCompanyName=n)},get_BomCompanyNo:function(){return this._BomCompanyNo},set_BomCompanyNo:function(n){this._BomCompanyNo!==n&&(this._BomCompanyNo=n)},get_SalesManNo:function(){return this._SalesManNo},set_SalesManNo:function(n){this._SalesManNo!==n&&(this._SalesManNo=n)},get_SalesManName:function(){return this._SalesManName},set_SalesManName:function(n){this._SalesManName!==n&&(this._SalesManName=n)},get_tblAllReleasedetails:function(){return this._tblAllReleasedetails},set_tblAllReleasedetails:function(n){this._tblAllReleasedetails!==n&&(this._tblAllReleasedetails=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Forms.BOMItems_ConfirmRemovePartwatch.callBaseMethod(this,"initialize");this.addShown(Function.createDelegate(this,this.formShown))},dispose:function(){this.isDisposed||(this._ctlConfirm&&this._ctlConfirm.dispose(),this._ctlConfirm=null,this._intRequirementLineID=null,this._ReqSalesman=null,this._SupportTeamMemberNo=null,this._tblAllReleasedetails=null,this._UnitBuyPrice=null,this._UnitSellPrice=null,Rebound.GlobalTrader.Site.Controls.Forms.BOMItems_ConfirmRemovePartwatch.callBaseMethod(this,"dispose"))},formShown:function(){this._blnFirstTimeShown&&(this._ctlConfirm=this.getFieldComponent("ctlConfirm"),this._ctlConfirm.addClickYesEvent(Function.createDelegate(this,this.yesClicked)),this._ctlConfirm.addClickNoEvent(Function.createDelegate(this,this.noClicked)))},yesClicked:function(){var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/BOMItems");n.set_DataObject("BOMItems");n.set_DataAction("RemovePartWatch");n.addParameter("id",this._intBOMID);n.addParameter("AddNotes","test");n.addParameter("ReqIds",$R_FN.arrayToSingleString(this._ReqIds,","));n.addParameter("UpdateByPH",this._UpdateByPH);n.addParameter("RequestToPOHubBy",this._RequestToPOHubBy);n.addParameter("HUBRFQName",this._BomName);n.addParameter("HUBRFQCompanyNo",this._BomCompanyNo);n.addParameter("Contact2No",this._intContact2No);n.addParameter("ReqsalesPerson",this._reqSalespeson);n.addDataOK(Function.createDelegate(this,this.saveComplete));n.addError(Function.createDelegate(this,this.saveError));n.addTimeout(Function.createDelegate(this,this.saveError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},noClicked:function(){this.showSaving(!1);this.onNotConfirmed()},saveError:function(n){this.showSaving(!1);this._strErrorMessage=n._errorMessage;this.onSaveError()},saveComplete:function(n){this.showSaving(!1);n._result.Result?this.onSaveComplete():(this._strErrorMessage=n._errorMessage,this.onSaveError())}};Rebound.GlobalTrader.Site.Controls.Forms.BOMItems_ConfirmRemovePartwatch.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.BOMItems_ConfirmRemovePartwatch",Rebound.GlobalTrader.Site.Controls.Forms.Base,Sys.IDisposable);