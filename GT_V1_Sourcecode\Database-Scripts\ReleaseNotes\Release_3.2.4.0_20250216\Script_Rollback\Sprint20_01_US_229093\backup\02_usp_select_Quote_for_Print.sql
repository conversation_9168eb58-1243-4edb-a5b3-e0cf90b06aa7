﻿
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE OR ALTER PROCEDURE [dbo].[usp_select_Quote_for_Print]  
--  
    @QuoteId int   
--  
AS   
begin  
    -- Update Quote Status Pending to Offer:  
 UPDATE tbQuote set QuoteStatus = case when isnull(QuoteStatus,0) = 4 then  1 else Quote<PERSON>tatus end WHERE QuoteId = @QuoteId  
    SELECT  a.*  
          , b.EMail AS ContactEmail 
		  ,(select FooterText from tbSystemDocumentFooterHistory FTH where FTH.SystemDocumentFooterHistoryId=a.SysDocAS9120HistoryNo)  as SysDocAS9120HistoryText
		  ,(select FooterText from tbSystemDocumentFooterHistory FTH where FTH.SystemDocumentFooterHistoryId=a.SysDocHazardousHistoryNo)  as SysDocHazardousHistoryText
		  ,(select FooterText from tbSystemDocumentFooterHistory FTH where FTH.SystemDocumentFooterHistoryId=a.SysDocCOOHistoryNo)  as SysDocCOOHistoryText 
    FROM    dbo.vwQuote a  
    JOIN    dbo.tbContact b ON a.ContactNo = b.ContactId  
    WHERE   a.QuoteId = @QuoteId  
  
end  
  


GO


