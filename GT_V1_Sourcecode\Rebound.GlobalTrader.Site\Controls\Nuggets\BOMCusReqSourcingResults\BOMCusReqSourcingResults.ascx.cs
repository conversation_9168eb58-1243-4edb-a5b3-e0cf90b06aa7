//------------------------------------------------------------------------------------------------
// RP 05.01.2010:
// - add links back to related Quotes
//------------------------------------------------------------------------------------------------
using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site.Controls;
using Rebound.GlobalTrader.Site;

namespace Rebound.GlobalTrader.Site.Controls.Nuggets {
    public partial class BOMCustomerRequirementSourcingResults : Base {

        #region Locals

        protected FlexiDataTable _tbl = new FlexiDataTable();
        protected IconButton _ibtnAdd;
        protected IconButton _ibtnEdit;
        protected IconButton _ibtnQuote;
        protected IconButton _ibtnDelete;
        protected IconButton _ibtnDeletePartwatch;
        protected IconButton _ibtnConfirm;
        protected IconButton _ibtnApproval;
        protected MultiSelectionCount _ctlMultiSelectionCount;

        protected Panel _pnlLineDetail;
        protected Panel _pnlLoadingLineDetail;
        protected Panel _pnlLineDetailError;
        #endregion

        #region Properties

        private bool _blnCanAdd = true;
        public bool CanAdd {
            get { return _blnCanAdd; }
            set { _blnCanAdd = value; }
        }

        private bool _blnCanEdit = true;
        public bool CanEdit {
            get { return _blnCanEdit; }
            set { _blnCanEdit = value; }
        }

        private bool _blnCanQuote = true;
        public bool CanQuote {
            get { return _blnCanQuote; }
            set { _blnCanQuote = value; }
        }

        private bool _blnCanDelete = true;
        public bool CanDelete
        {
            get { return _blnCanDelete; }
            set { _blnCanDelete = value; }
        }

        #endregion

        #region Overrides

        protected override void OnInit(EventArgs e) {

            TitleText = SessionManager.IsPOHub.Value == true ? Functions.GetGlobalResource("Nuggets", "CustomerRequirementSourcingResultsHub") : Functions.GetGlobalResource("Nuggets", "CustomerRequirementSourcingResults");

          //  TitleText = Functions.GetGlobalResource("Nuggets", "CustomerRequirementSourcingResults");
            base.OnInit(e);
        }

        protected override void OnLoad(EventArgs e) {
            AddScriptReference("Controls.Nuggets.BOMCusReqSourcingResults.BOMCusReqSourcingResults.js");
            WireUpControls();
            SetupTable();
            base.OnLoad(e);
        }

        protected override void OnPreRender(EventArgs e) {
            _ibtnAdd.Visible = _blnCanAdd;
            _ibtnEdit.Visible = _blnCanEdit;
            _ibtnQuote.Visible = _blnCanQuote;
            _ibtnDelete.Visible = _blnCanDelete;
            _ibtnDeletePartwatch.Visible = _blnCanDelete;
            _ibtnConfirm.Visible = Convert.ToBoolean(SessionManager.IsPOHub);
            _ibtnApproval.Visible = false;// Convert.ToBoolean(SessionManager.IsPOHub);
            SetupScriptDescriptors();
            base.OnPreRender(e);
        }

        #endregion

        #region Methods

        private void WireUpControls() {
            _ibtnAdd = FindIconButton("ibtnAdd");            
            _ibtnEdit = FindIconButton("ibtnEdit");
            _ibtnQuote = FindIconButton("ibtnQuote");
            _ibtnDelete = FindIconButton("ibtnDelete");
            _ibtnDeletePartwatch = FindIconButton("ibtnDeletePartwatch");
            if(SessionManager.IsPOHub==false)
            _ibtnDeletePartwatch.IconTitleResource = "DeleteClientPartWatch";
            _ibtnConfirm = FindIconButton("ibtnConfirm");
            _ibtnApproval = FindIconButton("ibtnApproval");
            _pnlLineDetail = (Panel)Functions.FindControlRecursive(this, "pnlLineDetail");
            _pnlLoadingLineDetail = (Panel)Functions.FindControlRecursive(this, "pnlLoadingLineDetail");
            _pnlLineDetailError = (Panel)Functions.FindControlRecursive(this, "pnlLineDetailError");
            _ctlMultiSelectionCount = (MultiSelectionCount)ctlDesignBase.FindLinksControl("ctlMultiSelectionCount");
        }

        private void SetupTable() {
            _tbl = (FlexiDataTable)ctlDesignBase.FindContentControl("tblQuote");
            _tbl.Columns.Add(new FlexiDataColumn("Supplier", "RelatedQuotes"));
            _tbl.Columns.Add(new FlexiDataColumn("PartNo", "Notes"));
            _tbl.Columns.Add(new FlexiDataColumn("ManufacturerAbbreviation", "DateCode", WidthManager.GetWidth(WidthManager.ColumnWidth.ManufacturerCode)));
            _tbl.Columns.Add(new FlexiDataColumn("Product", "Package", WidthManager.GetWidth(WidthManager.ColumnWidth.Product)));
            _tbl.Columns.Add(new FlexiDataColumn("DateOffered", "By", WidthManager.GetWidth(WidthManager.ColumnWidth.Date)));
            _tbl.Columns.Add(new FlexiDataColumn("Quantity","DeliveryDate", WidthManager.GetWidth(WidthManager.ColumnWidth.Quantity)));
            if (SessionManager.IsPOHub == true) { _tbl.Columns.Add(new FlexiDataColumn("BuyPrice", "ActualPriceInBase", WidthManager.GetWidth(WidthManager.ColumnWidth.CurrencyValue))); }
            _tbl.Columns.Add(new FlexiDataColumn(SessionManager.IsPOHub == true ? "UnitSellPrice" : "UnitPrice", "BaseCurrency", WidthManager.GetWidth(WidthManager.ColumnWidth.CurrencyValue)));
            if (SessionManager.IsPOHub == true)
            {
                _tbl.Columns.Add(new FlexiDataColumn("PurchaseRequestNo", "Region", WidthManager.GetWidth(WidthManager.ColumnWidth.BooleanValue)));
            }
            else
            {
                _tbl.Columns.Add(new FlexiDataColumn("Region", "TermsName", WidthManager.GetWidth(WidthManager.ColumnWidth.BooleanValue)));
            }
            _tbl.Columns.Add(new FlexiDataColumn("EstimatedShippingCost", "EstimatedShippingCostInBase",Unit.Empty));
            if (SessionManager.IsPOHub == true)
            {
                _tbl.Columns.Add(new FlexiDataColumn("PartWatchMatch", "SourceClient", Unit.Pixel(87)));
               // _tbl.Columns.Add(new FlexiDataColumn("SourceClient", Unit.Pixel(87)));
            }
            _tbl.Columns.Add(new FlexiDataColumn("ToDoList", WidthManager.GetWidth(WidthManager.ColumnWidth.Quantity)));
        }

        private void SetupScriptDescriptors() {
            _scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.Nuggets.BOMCustomerRequirementSourcingResults", ctlDesignBase.ClientID);
            _scScriptControlDescriptor.AddProperty("intCustomerRequirementID", _objQSManager.CustomerRequirementID);
            _scScriptControlDescriptor.AddProperty("blnPOHub", Convert.ToBoolean(SessionManager.IsPOHub));
            _scScriptControlDescriptor.AddComponentProperty("tbl", _tbl.ClientID);
            if (_blnCanAdd) _scScriptControlDescriptor.AddElementProperty("ibtnAdd", _ibtnAdd.ClientID);
            if (_blnCanEdit) _scScriptControlDescriptor.AddElementProperty("ibtnEdit", _ibtnEdit.ClientID);
            if (_blnCanQuote) _scScriptControlDescriptor.AddElementProperty("ibtnQuote", _ibtnQuote.ClientID);
            if (_blnCanDelete) _scScriptControlDescriptor.AddElementProperty("ibtnDelete", _ibtnDelete.ClientID);
            if (_blnCanDelete) _scScriptControlDescriptor.AddElementProperty("ibtnDeletePartwatch", _ibtnDeletePartwatch.ClientID);
            _scScriptControlDescriptor.AddComponentProperty("ctlMultiSelectionCount", _ctlMultiSelectionCount.ClientID);

            _scScriptControlDescriptor.AddElementProperty("pnlLineDetail", _pnlLineDetail.ClientID);
            _scScriptControlDescriptor.AddElementProperty("pnlLoadingLineDetail", _pnlLoadingLineDetail.ClientID);
            _scScriptControlDescriptor.AddElementProperty("pnlLineDetailError", _pnlLineDetailError.ClientID);
            if (Convert.ToBoolean(SessionManager.IsPOHub)) _scScriptControlDescriptor.AddElementProperty("ibtnConfirm", _ibtnConfirm.ClientID);
            if (Convert.ToBoolean(SessionManager.IsPOHub)) _scScriptControlDescriptor.AddElementProperty("ibtnApproval", _ibtnApproval.ClientID);
        }

        #endregion

    }
}
