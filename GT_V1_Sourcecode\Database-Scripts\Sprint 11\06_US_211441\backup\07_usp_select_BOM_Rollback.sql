﻿SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE OR ALTER PROCEDURE [dbo].[usp_select_BOM]                              
--************************************************************************************************                                  
--* Gets just the information needed for the BOM Detail page                                  
--*                                  
--* 21.01.2016:                                  
--* - new proc              
--* Action: Altered  By: Abhinav Saxena  Date: 25-09-2023  Comment: For RP-2888 (AS6081).                                
--************************************************************************************************                                  
    @BOMId int                                  
AS                                  
BEGIN                             
 --Valiate Requirement :                            
DECLARE @tbcusReq TABLE(                            
CustomerRequirementId varchar(30) NOT NULL)                            
                            
declare @ValidateMessage varchar(250)                            
                            
INSERT INTO @tbCusReq                            
SELECT   cr.CustomerRequirementNumber    FROM  tbCustomerRequirement cr                            
WHERE cr.BOMNo =@BOMId and                             
 ( isnull(cr.ManufacturerNo,'') = '' or cr.Quantity < 0 or isnull(cr.Part,'')='' or cr.ContactNo <= 0                            
 or  isnull(cr.CurrencyNo,'')='' or  isnull(cr.ProductNo,'')='' or isnull(cr.ReqForTraceability,'')='')                            
                            
DECLARE @Count int                            
SET @ValidateMessage = ''                            
SELECT @Count = isnull(count(*),0) from  @tbCusReq                            
IF(@Count >0)                            
BEGIN                            
                            
SELECT @ValidateMessage = @ValidateMessage + CustomerRequirementId + ', ' from @tbCusReq                            
SET @ValidateMessage = SUBSTRING(@ValidateMessage, 0, LEN(@ValidateMessage)) + '  ' + 'Some mandatory data is missing from this requirement. Please go back and fill in the missing data. '                            
                            
END                            
--Validate Cust Req        
    
--PVV/BOM Question validation    
DECLARE @PVVBOMValidateMessage nvarchar(500)=null    
SET @PVVBOMValidateMessage = ''                            
 DECLARE  @PVVBOMCount  integer                
SELECT   @PVVBOMCount = COUNT (*)                  
FROM    dbo.tbHUBRFQPVVAnswer                   
WHERE    BOMNo = @BOMId           
IF  @PVVBOMCount  = 0                  
BEGIN                            
SET @PVVBOMValidateMessage = SUBSTRING(@PVVBOMValidateMessage, 0, LEN(@PVVBOMValidateMessage)) + '  ' + 'PPV/ BOM Qualification section is not currently filled in. Are you sure you want to proceed?. '                            
END       
--end    
                            
    DECLARE @AllHasSourcing INT , @isNoBidCount int                            
 SELECT @AllHasSourcing = count(*) FROM tbCustomerRequirement cr                               
JOIN tbBOM bm ON bm.BOMId = cr.BOMNo                               
JOIN tbSourcingResult sr on cr.CustomerRequirementId = sr.CustomerRequirementNo                              
WHERE bm.BOMId = @BOMId AND sr.SourcingTable in ('PQ','OFPH','EXPH') AND ISNULL(IsReleased,0) = 0                              
select @isNoBidCount=COUNT(1) from tbCustomerRequirement where BOMNo =@BOMId and ISNULL(HasHubSourcingResult,0)=0 and ISNULL(IsNoBid,0)=0 --  and ISNULL(HasClientSourcingResult,0)=0                            
                        
DECLARE @ReqSalesPerson nvarchar(max)                        
SELECT distinct  @ReqSalesPerson = COALESCE(@ReqSalesPerson,'') +''+CAST(Salesman AS VARCHAR(10)) +''+ '||'   FROM tbCustomerRequirement WHERE BOMNo = @BOMId    
                      
DECLARE @SupportTeamMemberNo nvarchar(max)                
SELECT distinct  @SupportTeamMemberNo = COALESCE(@SupportTeamMemberNo,'') +''+CAST(SupportTeamMemberNo AS VARCHAR(10)) +''+ '||'   FROM tbCustomerRequirement WHERE BOMNo = @BOMId                        
                              
    SELECT (select COUNT(1) from tbCustomerRequirement where BOMNo = @BOMId AND   POHubReleaseBy IS NULL) as BomCount,@AllHasSourcing AS AllHasSourcing, @isNoBidCount as NoBidCount,                             
 BOMId,bom.Clientno,BOMName,bom.Notes,BOMCode,bom.Inactive,bom.companyId,c.CompanyName CompanyName,t.Name CompanyType ,contactId,contactName,RequestToPOHubBy,DateRequestToPOHub,Status,                  
 ReleaseBy,DateRelease,bom.DLUP,bom.UpdatedBy,StatusValue,                  
CurrencyCode,CurrencyId,Currency_Code,CompanyNo,CurrentSupplier,QuoteRequired,UpdateByPH,TotalBomLinePrice,AS9120,Releasedby,Requestedby,AssignTo,Contact2Id,Contact2Name                  
,@ValidateMessage as ValidateMessage,@Count as IsReqInValid, @ReqSalesPerson AS ReqSalesPerson      
,@SupportTeamMemberNo as SupportTeamMemberNo,c.PurchasingNotes                    
 ,bom.ClientNo      
 , bom.AssigneeIds AS AssignedUserIds,        
 CASE WHEN bom.AS6081Item=1 THEN 'Yes' ELSE 'No' END AS AS6081     
,@PVVBOMValidateMessage as PVVBOMValidateMessage, @PVVBOMCount as PVVBOMCountValid   
 from  vwBOM bom                   
   left join   tbCompany c on c.CompanyId=bom.companyId                  
   left join tbCompanyType t on t.CompanyTypeId=c.TypeNo                  
    WHERE   bom.BOMId = @BOMId                   
END 



