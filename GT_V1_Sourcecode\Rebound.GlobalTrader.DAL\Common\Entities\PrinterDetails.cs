﻿using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;

namespace Rebound.GlobalTrader.DAL {
	
	public class PrinterDetails {
		
		#region Constructors

        public PrinterDetails() { }
		
		#endregion
		
		#region Properties
	
		/// <summary>
        /// Printer Id
		/// </summary>
        public System.Int32 PrinterId { get; set; }
		/// <summary>
		/// Client No
		/// </summary>
		public System.Int32 ClientNo { get; set; }
		/// <summary>
        /// Printer Name
		/// </summary>
        public System.String PrinterName { get; set; }
		/// <summary>
        /// Printer Description
		/// </summary>
        public System.String PrinterDescription { get; set; }		
		/// <summary>
		/// DLUP
		/// </summary>
		public System.DateTime DLUP { get; set; }
		/// <summary>
        /// Inactive
		/// </summary>
        public System.Boolean Inactive { get; set; }
        /// <summary>
        /// UpdatedBy
        /// </summary>
        public System.Int32? UpdatedBy { get; set; }

        //Properties for List item setup screen: 

        public System.Int32? ListSetpId { get; set; }
        public System.String ListSetupName { get; set; }
        public System.String Descriptions { get; set; }

        //Properties for setup list item screen: 

        public System.Int32? LabelSetupItemId { get; set; }
        public System.String LabelSetupItemName { get; set; }
        public System.String LabelSetupOther { get; set; }

        public System.Int32? RestrictedManufacturerId { get; set; }
        public System.String Notes { get; set; }
        public System.Int32? ManufactureNo { get; set; }
        public System.String ManufactureName { get; set; }
        public System.Int32? ManufacturerCount { get; set; }
        public System.Int32? isRestrictedManufacturer { get; set; }
        public System.String RestrictedMFRMessage { get; set; }
        /// <summary>
        /// ECCNId
        /// </summary>
        public System.Int32? ECCNId { get; set; }
        /// <summary>
        /// ECCNCode
        /// </summary>
        public System.String ECCNCode { get; set; }
        /// <summary>
        /// ECCNStatus
        /// </summary>
        public System.Boolean ECCNStatus { get; set; }

        /// <summary>
        /// ECCNNotify
        /// </summary>
        public System.Boolean ECCNNotify { get; set; }
        /// <summary>
        /// EccnSubject
        /// </summary>
        public System.String EccnSubject { get; set; }
        /// <summary>
        /// EccnMessage
        /// </summary>
        public System.String EccnMessage { get; set; }
        /// <summary>
        /// EmployeeName
        /// </summary>
        public System.String EmployeeName { get; set; }

        public System.String MFRNameSuffix { get; set; }
        #endregion

    }
}