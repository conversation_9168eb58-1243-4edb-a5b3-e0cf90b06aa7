﻿using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;

namespace Rebound.GlobalTrader.Site.Controls.DropDowns {
    public partial class EmployeeByLevel : Base
    {

        //public bool LimitToCurrentUsersTeam { get; set; }
        //public bool LimitToCurrentUsersDivision { get; set; }
		public bool ExcludeCurrentUser { get; set; }

		protected override void OnLoad(EventArgs e) {
            SetDropDownType("EmployeeByLevel");
            AddScriptReference("Controls.DropDowns.EmployeeByLevel.EmployeeByLevel");
            _scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.DropDowns.EmployeeByLevel", ClientID);
            _scScriptControlDescriptor.AddProperty("blnLimitToCurrentUsersDivision", OnlyDivision);
			_scScriptControlDescriptor.AddProperty("blnLimitToCurrentUsersTeam", OnlyTeam);
			_scScriptControlDescriptor.AddProperty("blnExcludeCurrentUser", ExcludeCurrentUser);
			base.OnLoad(e);
		}

	}
}