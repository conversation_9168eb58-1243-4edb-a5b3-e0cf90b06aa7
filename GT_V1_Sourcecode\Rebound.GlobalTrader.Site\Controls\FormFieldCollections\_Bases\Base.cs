using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site.Controls.Forms;

namespace Rebound.GlobalTrader.Site.Controls.FormFieldCollections {
	public class Base : LabelFormField, INamingContainer {

		public FormField.FormFieldControlType FieldType {
			get { return FormField.FormFieldControlType.FieldCollection; }
		}

		protected new void AddScriptReference(string strRef) {
			bool blnDebug = false;
#if DEBUG
			blnDebug = true;
#endif
			base.AddScriptReference(blnDebug, "Rebound.GlobalTrader.Site", strRef);
		}

	}
}
