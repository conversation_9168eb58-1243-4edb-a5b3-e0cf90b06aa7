﻿///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.ItemSearch");

Rebound.GlobalTrader.Site.Controls.ItemSearch.SourcingBulkEditLog = function (element) {
    Rebound.GlobalTrader.Site.Controls.ItemSearch.SourcingBulkEditLog.initializeBase(this, [element]);
    this._sourcingType = "";
    this._partSearch = "";
};

Rebound.GlobalTrader.Site.Controls.ItemSearch.SourcingBulkEditLog.prototype = {

    initialize: function () {
        Rebound.GlobalTrader.Site.Controls.ItemSearch.SourcingBulkEditLog.callBaseMethod(this, "initialize");
        this.addSetupData(Function.createDelegate(this, this.doSetupData));
        this.addGetDataComplete(Function.createDelegate(this, this.doGetDataComplete));
    },

    dispose: function () {
        if (this.isDisposed) return;
        this._sourcingType = null;
        this._partSearch = null;
        Rebound.GlobalTrader.Site.Controls.ItemSearch.SourcingBulkEditLog.callBaseMethod(this, "dispose");
    },

    doSetupData: function () {
        this._objData.set_PathToData("controls/ItemSearch/SourcingBulkEditLog");
        this._objData.set_DataObject("SourcingBulkEditLog");
        this._objData.set_DataAction("GetData");
        this._objData.addParameter("SourcingType", this._sourcingType);
        this._objData.addParameter("PartNo", this.getFieldValue('ctlPartNo'));
        this._objData.addParameter("EditedBy", this.getFieldValue('ctlEditedBy'));
        this._objData.addParameter("EditedDateFrom", this.getFieldValue('ctlEditedDateFrom'));
        this._objData.addParameter("EditedDateTo", this.getFieldValue('ctlEditedDateTo'));
    },

    doGetDataComplete: function () {
        for (var i = 0, l = this._objResult.Results.length; i < l; i++) {
            var row = this._objResult.Results[i];
            var aryData = [
                row.BatchNo
                , $R_FN.setCleanTextValue(row.PartNo)
                , row.Action
                , row.OldValue
                , $R_FN.setCleanTextValue(row.UpdatedBy)
                , $R_FN.setCleanTextValue(row.UpdatedDate)
            ];
            this._tblResults.addRow(aryData, row.ID, false);
            aryData = null; row = null;
        }
    },
    clearFilterInput: function () {
        this.setFieldValue("ctlPartNo", "");
        this.setFieldValue("ctlEditedBy", 0);
        this.setFieldValue("ctlEditedDateFrom", null);
        this.setFieldValue("ctlEditedDateTo", null);
        this.getField("ctlEditedDateFrom")._txt.value = "";
        this.getField("ctlEditedDateTo")._txt.value = "";
    }
};

Rebound.GlobalTrader.Site.Controls.ItemSearch.SourcingBulkEditLog.registerClass("Rebound.GlobalTrader.Site.Controls.ItemSearch.SourcingBulkEditLog", Rebound.GlobalTrader.Site.Controls.ItemSearch.Base, Sys.IDisposable);
