﻿using System;

namespace Rebound.GlobalTrader.DAL
{
    public class StarRatingDetails
    {
        #region Constructors
        public StarRatingDetails() { }
        #endregion

        #region Properties
        /// <summary>
        /// Number of Purchase Order
        /// </summary>
        public int NumOfPO { get; set; }
        /// <summary>
        /// Counted Star
        /// </summary>
        public byte CountedStar { get; set; }
        /// <summary>
        /// DLUP
        /// </summary>
        public DateTime? CreatedDate { get; set; }
        /// <summary>
        /// Created by
        /// </summary>
        public string CreatedBy { get; set; }
        #endregion
    }
}
