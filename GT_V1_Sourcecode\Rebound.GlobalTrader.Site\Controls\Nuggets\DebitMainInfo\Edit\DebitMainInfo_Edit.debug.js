///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//Marker     Changed by      Date         Remarks
//[002]     Shashi Keshar    07/10/2016   Lock update from Client
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.DebitMainInfo_Edit = function(element) { 
	Rebound.GlobalTrader.Site.Controls.Forms.DebitMainInfo_Edit.initializeBase(this, [element]);
	this._intDebitID = -1;
	this._hidRaisedByNo=0;
	this._IsPOHub=false;
	this._RaisedBy = -1;
	this._intGlobalClientNo = -1;
};

Rebound.GlobalTrader.Site.Controls.Forms.DebitMainInfo_Edit.prototype = {

	get_intDebitID: function() { return this._intDebitID; }, 	set_intDebitID: function(value) { if (this._intDebitID !== value)  this._intDebitID = value; }, 
	get_lblCurrency_Freight: function() { return this._lblCurrency_Freight; }, 	set_lblCurrency_Freight: function(value) { if (this._lblCurrency_Freight !== value)  this._lblCurrency_Freight = value; }, 

	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.Forms.DebitMainInfo_Edit.callBaseMethod(this, "initialize");
		this.addShown(Function.createDelegate(this, this.formShown));
	},

	formShown: function() {
		if (this._blnFirstTimeShown) {
			this.addSave(Function.createDelegate(this, this.saveClicked));
			$find(this.getField("ctlCurrency").ControlID).addChanged(Function.createDelegate(this, this.currencyChanged));
		}
		this.getFieldControl("ctlDivision")._intGlobalLoginClientNo = this._intGlobalClientNo;
		this.getFieldDropDownData("ctlDivision");

		this.getFieldControl("ctlBuyer")._intGlobalLoginClientNo = this._intGlobalClientNo;
		this.getFieldDropDownData("ctlBuyer");

		this.getFieldControl("ctlRaisedBy")._intGlobalLoginClientNo = this._intGlobalClientNo;
		this.getFieldDropDownData("ctlRaisedBy");

		this.getFieldControl("ctlTax")._intGlobalLoginClientNo = this._intGlobalClientNo;
		this.getFieldDropDownData("ctlTax");

		this.getFieldControl("ctlCurrency")._intGlobalLoginClientNo = this._intGlobalClientNo;
		this.getFieldDropDownData("ctlCurrency");
		//[001] code start
		this.getFieldDropDownData("ctlIncoterm");
		//[001] code end
	},

	dispose: function() { 
		if (this.isDisposed) return;
		this._intDebitID = null;
		this._lblCurrency_Freight = null;
		Rebound.GlobalTrader.Site.Controls.Forms.DebitMainInfo_Edit.callBaseMethod(this, "dispose");
	},

	saveClicked: function() {
		this._RaisedBy = this.getFieldValue("ctlRaisedBy");
		if (this._RaisedBy <= 0) {
			this._RaisedBy = this._hidRaisedByNo;			
		}
		if (!this._IsPOHub) {
			for (var i = 0, l = this._aryFields.length; i < l; i++) {
				var fld = this._aryFields[i];
				if (fld.ControlID == 'ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlDebitEdit_ctlDB_ctlRaisedBy_ctl04_ddlRaisedBy') {
					fld.Required = 'false';
				}
			}
        }

		if (!this.validateForm()) return;
		var obj = new Rebound.GlobalTrader.Site.Data();

		obj.set_PathToData("controls/Nuggets/DebitMainInfo");
		obj.set_DataObject("DebitMainInfo");
		obj.set_DataAction("SaveEdit");
		obj.addParameter("id", this._intDebitID);
		obj.addParameter("SupplierInvoice", this.getFieldValue("ctlSupplierInvoice"));
		obj.addParameter("SupplierReturn", this.getFieldValue("ctlSupplierReturn"));
		obj.addParameter("SupplierCredit", this.getFieldValue("ctlSupplierCredit"));
		obj.addParameter("Freight", this.getFieldValue("ctlFreight"));
		obj.addParameter("Notes", this.getFieldValue("ctlNotes"));
		obj.addParameter("Instructions", this.getFieldValue("ctlInstructions"));
		obj.addParameter("DivisionNo", this.getFieldValue("ctlDivision"));
		obj.addParameter("Buyer", this.getFieldValue("ctlBuyer"));
		obj.addParameter("RaisedBy",  this._IsPOHub==false ? this._hidRaisedByNo : this.getFieldValue("ctlRaisedBy"));
		obj.addParameter("DebitDate", this.getFieldValue("ctlDebitDate"));
		obj.addParameter("ReferenceDate", this.getFieldValue("ctlReferenceDate"));
		obj.addParameter("TaxNo", this.getFieldValue("ctlTax"));
		obj.addParameter("CurrencyNo", this.getFieldValue("ctlCurrency"));
		//[001] code start
		obj.addParameter("IncotermNo", this.getFieldValue("ctlIncoterm"));
	    //[001] code end
	
		obj.addParameter("URNNumber", this.getFieldValue("ctlURNNumber"));
	    //[002] Start Here
		obj.addParameter("LockUpdateClient", this.getFieldValue("ctlLockUpdateClient"));
	    //[002] End Here
		obj.addDataOK(Function.createDelegate(this, this.saveEditComplete));
		obj.addError(Function.createDelegate(this, this.saveEditError));
		obj.addTimeout(Function.createDelegate(this, this.saveEditError));
		$R_DQ.addToQueue(obj);
		$R_DQ.processQueue();
		obj = null;
	},

	saveEditError: function(args) {
		this._strErrorMessage = args._errorMessage;
		this.onSaveError();
	},

	saveEditComplete: function(args) {
		if (args._result.Result == true) {
			this.onSaveComplete();
		} else {
			this._strErrorMessage = args._errorMessage;
			this.onSaveError();
		}
	},	

	validateForm: function() {
		this.onValidate();
		var blnOK = this.autoValidateFields();
		return blnOK;
	},
	
	setCurrency: function(strCode) {
		$R_FN.setInnerHTML(this._lblCurrency_Freight, strCode);
	},
	
	currencyChanged: function() {
		this.setCurrency(this.getFieldDropDownExtraText("ctlCurrency"));
	}

};

Rebound.GlobalTrader.Site.Controls.Forms.DebitMainInfo_Edit.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.DebitMainInfo_Edit", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
