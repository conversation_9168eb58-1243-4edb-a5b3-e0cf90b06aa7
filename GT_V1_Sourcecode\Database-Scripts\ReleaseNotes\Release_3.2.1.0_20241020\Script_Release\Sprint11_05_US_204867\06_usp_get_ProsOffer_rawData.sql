﻿/*   
===========================================================================================  
TASK			UPDATED BY      DATE			ACTION		DESCRIPTION  
[US-204867]		An.TranTan		24-Sep-2024		Create		Get prospective offer raw data
===========================================================================================  
*/ 
CREATE OR ALTER PROCEDURE usp_get_ProsOffer_rawData 
	@DisplayLength INT
	,@DisplayStart INT
	,@SortCol INT
	,@SortDir NVARCHAR(10)
	,@UserId INT
AS
BEGIN
	SET NOCOUNT OFF;

	DECLARE @FirstRec INT ,@LastRec INT;

	SET @FirstRec = @DisplayStart;
	SET @LastRec = @DisplayStart + @DisplayLength;

	WITH CTE_Stock
	AS (
		SELECT ROW_NUMBER() OVER (
				ORDER BY CASE 
						WHEN (
								@SortCol = 0
								AND @SortDir = 'asc'
								)
							THEN ProspectiveOfferId
						END ASC
				) AS RowNum
			,COUNT(*) OVER () AS TotalCount
			,Column1
			,Column2
			,Column3
			,Column4
			,Column5
			,Column6
			,Column7
			,Column8
			,Column9
			,Column10
			,Column11
			,CreatedDate
			,OriginalFilename
		FROM BorisGlobalTraderImports.dbo.tbProspectiveOffer_tempData WITH (NOLOCK)
		WHERE CreatedBy = @UserId
		)
	SELECT RowNum
		,TotalCount
		,Column1
		,Column2
		,Column3
		,Column4
		,Column5
		,Column6
		,Column7
		,Column8
		,Column9
		,Column10
		,Column11
		,CreatedDate
		,OriginalFilename
	FROM CTE_Stock
	WHERE RowNum > @FirstRec
		AND RowNum <= @LastRec
	ORDER BY CreatedDate DESC
END


/*
EXEC usp_get_ProsOffer_rawData
	@DisplayLength =  10
	,@DisplayStart = 0
	,@SortCol = 0
	,@SortDir ='ASC'
	,@UserId = 6670
*/
