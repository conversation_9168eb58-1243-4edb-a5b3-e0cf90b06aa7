﻿/*===============================================================================================================================
Task			   UPDATED BY			  DATE			    ACTION		DESCRIPTION
[US-231748]	 phudang		      14-Feb-2025		Update		Update SP usp_logout_Login to support concurrent login/logout
=================================================================================================================================
*/

CREATE OR ALTER PROCEDURE [dbo].[usp_logout_Login]
    @LoginId int
  , @SessionName nvarchar(20)
AS
  IF (SELECT  count(*)
    FROM    dbo.tbSession
    WHERE   LoginNo = @LoginId
  ) > 0 
  BEGIN
    DELETE  FROM dbo.tbSession
    WHERE   LoginNo = @LoginId
            -- AND SessionName = @SessionName
  END
