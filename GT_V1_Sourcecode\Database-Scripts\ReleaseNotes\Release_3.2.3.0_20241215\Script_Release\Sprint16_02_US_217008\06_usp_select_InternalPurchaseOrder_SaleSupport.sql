﻿CREATE OR ALTER PROCEDURE usp_select_InternalPurchaseOrder_SaleSupport
@InternalPurchaseOrderId INT
/*  
===========================================================================================  
TASK         UPDATED BY       DATE          ACTION    DESCRIPTION  
[US-217008]  Cuong DoX	      15-Nov-2024   Create    SupportTeamMemberNo  
===========================================================================================  
*/ 
AS
BEGIN
	SET nocount ON;
	SELECT 
	po.PurchaseOrderId
	,po.PurchaseOrderNumber
	,po.Buyer
	,lg.EmployeeName as BuyerName
	,ipo.InternalPurchaseOrderId
	,stm.EmployeeName as SupportTeamMemberName
	,po.SupportTeamMemberNo
	,ipo.Buyer as IPOBuyer
	,lgipo.EmployeeName as IPOBuyername
	,stmipo.EmployeeName as IPOSupportTeamMemberName
	,ipo.SupportTeamMemberNo as IPOSupportTeamMemberNo
	FROM
	dbo.tbInternalPurchaseOrder ipo
	LEFT JOIN dbo.tbPurchaseOrder po ON po.PurchaseOrderId = ipo.PurchaseOrderNo                          
	LEFT JOIN dbo.tbLogin lg ON po.Buyer = lg.LoginId                                                    
	LEFT JOIN dbo.tbLogin lgipo ON ipo.Buyer = lgipo.LoginId                        
	LEFT JOIN dbo.tbLogin stm ON po.SupportTeamMemberNo = stm.LoginId                     
	LEFT JOIN dbo.tbLogin stmipo ON ipo.SupportTeamMemberNo = stmipo.LoginId                     
	WHERE ipo.InternalPurchaseOrderId = @InternalPurchaseOrderId
END