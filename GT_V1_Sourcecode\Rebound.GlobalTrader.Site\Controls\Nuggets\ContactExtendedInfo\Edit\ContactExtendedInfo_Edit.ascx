<%@ Control Language="C#" CodeBehind="ContactExtendedInfo_Edit.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Forms.ContactExtendedInfo_Edit" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Form:DesignBase ID="ctlDB" runat="server" ShowRequiredFieldsExplanation="true" ShowQuickHelp="false">
	<Links>
		<ReboundUI:IconButton ID="ibtnSave" runat="server" IconGroup="Nugget" IconTitleResource="Save" IconCSSType="Save" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
		<ReboundUI:IconButton ID="ibtnCancel" runat="server" IconGroup="Nugget" IconTitleResource="Cancel" IconCSSType="Cancel" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
	</Links>
	<Explanation><%=Functions.GetGlobalResource("FormExplanations", "ContactExtendedInfo_Edit")%></Explanation>
	<Content>
		<ReboundUI_Table:Form id="frm" runat="server">
			<ReboundUI_Form:FormField id="ctlGender" runat="server" FieldID="ddlGender" ResourceTitle="Gender">
				<Field><ReboundDropDown:Gender ID="ddlGender" runat="server" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlBirthday" runat="server" FieldID="txtBirthday" ResourceTitle="Birthday">
				<Field>
					<ReboundUI:ReboundTextBox ID="txtBirthday" runat="server" Width="140" />
					<ReboundUI:Calendar ID="calBirthday" runat="server" RelatedTextBoxID="txtBirthday" FormatAsBirthday="true" />
				</Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlMaritalStatus" runat="server" FieldID="ddlMaritalStatus" ResourceTitle="MaritalStatus">
				<Field><ReboundDropDown:MaritalStatus ID="ddlMaritalStatus" runat="server" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlPartner" runat="server" FieldID="txtPartner" ResourceTitle="Partner">
				<Field><ReboundUI:ReboundTextBox ID="txtPartner" runat="server" Width="200" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlPartnerBirthday" runat="server" FieldID="txtPartnerBirthday" ResourceTitle="PartnerBirthday">
				<Field>
					<ReboundUI:ReboundTextBox ID="txtPartnerBirthday" runat="server" Width="140" />
					<ReboundUI:Calendar ID="calPartnerBirthday" runat="server" RelatedTextBoxID="txtPartnerBirthday" FormatAsBirthday="true" />
				</Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlAnniversary" runat="server" FieldID="txtAnniversary" ResourceTitle="Anniversary">
				<Field>
					<ReboundUI:ReboundTextBox ID="txtAnniversary" runat="server" Width="140" />
					<ReboundUI:Calendar ID="calAnniversary" runat="server" RelatedTextBoxID="txtAnniversary" FormatAsBirthday="true" />
				</Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlNumberChildren" runat="server" FieldID="txtNumberChildren" ResourceTitle="NumberChildren">
				<Field><ReboundUI:ReboundTextBox ID="txtNumberChildren" runat="server" Width="50" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlChild1Name" runat="server" FieldID="txtChild1Name" ResourceTitle="Child1Name">
				<Field><ReboundUI:ReboundTextBox ID="txtChild1Name" runat="server" Width="200" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlChild1Sex" runat="server" FieldID="ddlChild1Sex" ResourceTitle="Child1Sex">
				<Field><ReboundDropDown:Gender ID="ddlChild1Sex" runat="server" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlChild1Birthday" runat="server" FieldID="txtChild1Birthday" ResourceTitle="Child1Birthday">
				<Field>
					<ReboundUI:ReboundTextBox ID="txtChild1Birthday" runat="server" Width="140" />
					<ReboundUI:Calendar ID="calChild1Birthday" runat="server" RelatedTextBoxID="txtChild1Birthday" FormatAsBirthday="true" />
				</Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlChild2Name" runat="server" FieldID="txtChild2Name" ResourceTitle="Child2Name">
				<Field><ReboundUI:ReboundTextBox ID="txtChild2Name" runat="server" Width="200" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlChild2Sex" runat="server" FieldID="ddlChild2Sex" ResourceTitle="Child2Sex">
				<Field><ReboundDropDown:Gender ID="ddlChild2Sex" runat="server" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlChild2Birthday" runat="server" FieldID="txtChild2Birthday" ResourceTitle="Child2Birthday">
				<Field>
					<ReboundUI:ReboundTextBox ID="txtChild2Birthday" runat="server" Width="140" />
					<ReboundUI:Calendar ID="calChild2Birthday" runat="server" RelatedTextBoxID="txtChild2Birthday" FormatAsBirthday="true" />
				</Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlChild3Name" runat="server" FieldID="txtChild3Name" ResourceTitle="Child3Name">
				<Field><ReboundUI:ReboundTextBox ID="txtChild3Name" runat="server" Width="200" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlChild3Sex" runat="server" FieldID="ddlChild3Sex" ResourceTitle="Child3Sex">
				<Field><ReboundDropDown:Gender ID="ddlChild3Sex" runat="server" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlChild3Birthday" runat="server" FieldID="txtChild3Birthday" ResourceTitle="Child3Birthday">
				<Field>
					<ReboundUI:ReboundTextBox ID="txtChild3Birthday" runat="server" Width="140" />
					<ReboundUI:Calendar ID="calChild3Birthday" runat="server" RelatedTextBoxID="txtChild3Birthday" FormatAsBirthday="true" />
				</Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlMobileTel" runat="server" FieldID="txtMobileTel" ResourceTitle="MobileTel">
				<Field><ReboundUI:ReboundTextBox ID="txtMobileTel" runat="server" Width="200" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlFavouriteSport" runat="server" FieldID="txtFavouriteSport" ResourceTitle="FavouriteSport">
				<Field><ReboundUI:ReboundTextBox ID="txtFavouriteSport" runat="server" Width="200" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlFavouriteTeam" runat="server" FieldID="txtFavouriteTeam" ResourceTitle="FavouriteTeam">
				<Field><ReboundUI:ReboundTextBox ID="txtFavouriteTeam" runat="server" Width="200" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlHobbies" runat="server" FieldID="txtHobbies" ResourceTitle="Hobbies">
				<Field><ReboundUI:ReboundTextBox ID="txtHobbies" runat="server" Width="350" TextMode="MultiLine" Rows="2" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlNotes" runat="server" FieldID="txtNotes" ResourceTitle="Notes">
				<Field><ReboundUI:ReboundTextBox ID="txtNotes" runat="server" Width="450" TextMode="MultiLine" Rows="2" /></Field>
			</ReboundUI_Form:FormField>

		</ReboundUI_Table:Form>
	</Content>
</ReboundUI_Form:DesignBase>

