SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

IF OBJECT_ID('usp_insert_QuoteLine_from_SourcingResult_MultiQuote', 'P') IS NOT NULL
	DROP PROC dbo.usp_insert_QuoteLine_from_SourcingResult_MultiQuote
GO


CREATE PROCEDURE [dbo].[usp_insert_QuoteLine_from_SourcingResult_MultiQuote]                                         
--******************************************************************************************                                        
--* RP 14.12.2009:                                        
--* - new columns - SourcingResultNo and OriginalOfferSupplierNo                                        
--*                                        
--* SK 29.10.2009:                                        
--* - allow for new column - FullCustomerPart - used for searching                        
--*[001] -anand gupta - 20/0820 - IHS Column data passing                      
--******************************************************************************************                                        
    @SourcingResultId int                                        
  , @QuoteNo int                                        
  , @DateQuoted datetime            
  , @CustomerRequirementIds varchar(Max)             
  , @QuoteLineId int OUTPUT                                        
                                        
AS                                        
                                        
    BEGIN                                       
                                     
  DECLARE @OriginalOfferCurrencyNo INT                                    
  DECLARE @ClientCompanyNo INT                                    
  DECLARE @SourceTable nvarchar(20)                                
  DECLARE @SupplierCurrencyNo INT    
  DECLARE @IsBomManager BIT=0     
                                    
  SELECT @IsBomManager=IsBOMManager,@SourceTable=SourcingTable,@OriginalOfferCurrencyNo = CurrencyNo,@ClientCompanyNo = ClientCompanyNo  ,                              
  @SupplierCurrencyNo = ClientCurrencyNo                                   
  FROM tbSourcingResult WHERE SourcingResultId = @SourcingResultId                  
              
  create table #tempCustomerRequirementId (CustomerRequirementId int)            
  insert into #tempCustomerRequirementId            
  select * from string_split(@CustomerRequirementIds,'|')            
            
  delete from #tempCustomerRequirementId where isnull(CustomerRequirementId,'')=''            
                                    
  IF @SourceTable = 'PQ' OR @SourceTable = 'OFPH' OR @SourceTable = 'EXPH' OR @SourceTable='HUBSTK'OR @SourceTable='EPPH'OR @SourceTable='RLPH' OR @SourceTable='ALTPART'                                    
  OR @SourceTable='API-Manual' OR (@SourceTable='Offers' and @IsBomManager=1) OR @SourceTable='API-FE' OR @SourceTable='XMatch / Sourcing' OR @SourceTable='API Results' -- changes for bommanager      
  BEGIN                                    
     SET @OriginalOfferCurrencyNo = @SupplierCurrencyNo                                    
  END                                    
                                       
                                            
        INSERT  INTO dbo.tbQuoteLine (                                        
                  QuoteNo                                        
                , FullPart                                        
                , Part                                        
                , ManufacturerNo                                        
                , DateCode                                        
                , PackageNo                                        
                , Quantity                                        
                , Price                                        
                , ETA                                        
                , Instructions                                        
                , ProductNo                                        
                , ReasonNo                                        
                , CustomerPart                                        
                , StockNo                                        
                , ServiceNo                      
                , ROHS                                        
                , Closed              
 , OriginalOfferPrice                                        
                , OriginalOfferCurrencyNo                                        
                , OriginalOfferDate                                        
                , OriginalOfferSupplierNo                                        
                , UpdatedBy                                 
                , DLUP                                        
                , FullCustomerPart                                 
                , SourcingResultNo                 
                , PurchaseQuoteLineNo                                     
                , Notes                              
    , MSLLevel             
 --[001] code start                             
  --IHS code start from Columne to be insert to tbQuoteLine line                            
 -- ,CountryOfOrigin                            
 ,CountryOfOriginNo                            
 ,LifeCycleStage                            
 ,HTSCode                            
 ,AveragePrice                            
 ,Packing                            
 ,PackagingSize                        
 ,Descriptions                  
 ,IHSProduct                 
 ,ECCNCode                           
  --[001] code end                                 
     )                                        
                SELECT  @QuoteNo                                        
                      , sr.FullPart                                        
					  , sr.Part                                        
                      , sr.ManufacturerNo                                        
                      , sr.DateCode                                        
					  , sr.PackageNo                                        
                      , sr.Quantity                                                                  
					  , 0              
					  , NULL                                        
                      , NULL                                      
                      , sr.ProductNo                                        
                      , NULL                         
                      , cr.CustomerPart                                        
                      , NULL                                        
                      , NULL                                        
                      , sr.ROHS                                        
                      , 0                              
                      , sr.Price                                        
                      , @OriginalOfferCurrencyNo                                        
                      , sr.OriginalEntryDate                                        
					  , sr.SupplierNo                                        
                      , sr.UpdatedBy                                        
                      , CURRENT_TIMESTAMP                                        
                      , dbo.ufn_get_fullpart(cr.CustomerPart)                                        
                      , sr.SourcingResultId                                        
                      , SourcingTableItemNo                                     
           ,[dbo].[ufn_get_spqnew] (sr.SourcingResultId)                               
       , isnull(ml.MSLLevel,cr.MSL)                              
    --[001] code start                      
     --IHS code start from Columne to be insert to tbCustomerRequirement                             
  --,cr.CountryOfOrigin                     
  --,cr.CountryOfOriginNo                         
   ,isnull(sr.IHSCountryOfOriginNO, cr.CountryOfOriginNo)                             
 ,cr.LifeCycleStage                            
 ,cr.HTSCode                            
 ,cr.AveragePrice                            
 ,cr.Packing                            
 ,cr.PackagingSize                        
 ,cr.Descriptions                  
 ,cr.IHSProduct                     
 ,cr.ECCNCode                       
--[001] code end                           
                FROM    dbo.tbSourcingResult sr                                        
                JOIN    dbo.tbCustomerRequirement cr ON sr.CustomerRequirementNo = cr.CustomerRequirementId             
    Join #tempCustomerRequirementId tmpcr on tmpcr.CustomerRequirementId = sr.CustomerRequirementNo            
    left join tbMSLLevel ml on sr.MSLLevelNo = ml.MSLLevelId                                      
 --WHERE   SourcingResultId = @SourcingResultId                
 Where sr.isprimarySourcing= 1  and isnull(sr.Closed,0)=0          
    END                                        
                                        
    SET @QuoteLineId = scope_identity()                                        
                                        
 --check to see if Quote should be closed                                        
    EXEC usp_update_Quote_CheckClosed @QuoteNo               
             
            
    update a             
 set Closed=1 --,SourceRef='Q'            
 from tbSourcingResult a join #tempCustomerRequirementId tmpcr on a.CustomerRequirementNo=tmpcr.CustomerRequirementId            
            
    update a             
 set reqstatus=5 --,SourceRef='Q'            
 from tbcustomerrequirement a join #tempCustomerRequirementId tmpcr on a.CustomerRequirementId=tmpcr.CustomerRequirementId            
          
 Declare @BOMManagerid int          
 select @BOMManagerid= cr.BOMManagerNo  FROM    dbo.tbSourcingResult sr                                        
 JOIN    dbo.tbCustomerRequirement cr ON sr.CustomerRequirementNo = cr.CustomerRequirementId             
 JOIN #tempCustomerRequirementId tmpcr on tmpcr.CustomerRequirementId = sr.CustomerRequirementNo            
            
if((select count(1) from tbcustomerrequirement where BOMManagerNo = @BOMManagerid and REQStatus <5)>0)          
begin           
print 'pending'          
end          
else           
begin          
update tbbommanager set status = 6 where BOMManagerId=@BOMManagerid          
end          
          
 --where ((SourcingTable = 'PQ')  OR (SourcingTable  = 'OFPH') OR (SourcingTable = 'EXPH'))             
            
  --  IF(@SourcingResultId>0)                                  
  --  BEGIN                                 
  --   IF @SourceTable = 'PQ' OR @SourceTable = 'OFPH' OR @SourceTable = 'EXPH'                                    
  --BEGIN                                  
  --  UPDATE tbSourcingResult SET Closed=1,SourceRef='Q' WHERE SourcingResultId=@SourcingResultId                                  
  --  END                    
  --  END   
  

GO
