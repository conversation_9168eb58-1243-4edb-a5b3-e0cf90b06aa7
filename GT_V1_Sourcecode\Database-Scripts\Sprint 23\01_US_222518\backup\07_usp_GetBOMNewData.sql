﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO
              
CREATE OR ALTER PROCEDURE [dbo].[usp_GetBOMNewData]                          
@DisplayLength int,                          
@DisplayStart int,                          
@SortCol int,                          
@SortDir nvarchar(10),                          
@Search nvarchar(255) = NULL,                        
@UserId INT,                      
@ClientId INT  ,                  
@SelectedClientId INT                    
                        
as                          
begin              
SET nocount off;                        
    Declare @FirstRec int, @LastRec int                          
    Set @FirstRec = @DisplayStart;                          
    Set @LastRec = @DisplayStart + @DisplayLength;                          
                             
    With CTE_Stock as                          
    (                          
         Select ROW_NUMBER() over (order by                          
                                  
         case when (@SortCol = 0 and @SortDir='asc')                          
             then SelectedClientId                          
         end asc                         
   )                          
         as RowNum,                          
         COUNT(*) over() as TotalCount,                         
  --   STRING_ESCAPE( Column1 , 'json' )as Column1,STRING_ESCAPE( Column2 , 'json' )as Column2 , STRING_ESCAPE( Column3 , 'json' )as Column3,    
  -- STRING_ESCAPE( Column4 , 'json' )as Column4 ,STRING_ESCAPE( Column5 , 'json' )as Column5,STRING_ESCAPE( Column6 , 'json' )as Column6,                        
  --STRING_ESCAPE( Column7 , 'json' )as Column7,STRING_ESCAPE( Column8 , 'json' )as Column8,STRING_ESCAPE( Column9 , 'json' )as Column9,     
  --STRING_ESCAPE( Column10 , 'json' )as Column10,STRING_ESCAPE( Column11 , 'json' )as Column11,STRING_ESCAPE( Column12 , 'json' )as Column12,    
  --STRING_ESCAPE( Column13 , 'json' )as Column13, STRING_ESCAPE( Column14 , 'json' )as Column14, STRING_ESCAPE( Column15 , 'json' )as Column15,                          
    REPLACE(Column1, '\', '\\')as Column1,REPLACE( Column2 , '\', '\\')as Column2 , REPLACE( Column3 ,'\', '\\' )as Column3,    
   REPLACE( Column4 ,'\', '\\' )as Column4 ,REPLACE( Column5 ,'\', '\\' )as Column5,REPLACE( Column6 , '\', '\\' )as Column6,                        
  REPLACE( Column7 , '\', '\\')as Column7,REPLACE( Column8 , '\', '\\' )as Column8,REPLACE( Column9 , '\', '\\' )as Column9,     
  REPLACE( Column10 ,'\', '\\' )as Column10,REPLACE( Column11 , '\', '\\' )as Column11,REPLACE( Column12 , '\', '\\' )as Column12,    
  REPLACE( Column13 ,'\', '\\' )as Column13, REPLACE( Column14 , '\', '\\' )as Column14, REPLACE( Column15 , '\', '\\' )as Column15,  
  Id ,UpdatedOn,ClientId,CreatedOn,SelectedClientId , OriginalFilename                     
  from BorisGlobalTraderimports.dbo.tbTempBomData                          
  where (ClientId = @ClientId and SelectedClientId=@SelectedClientId and CreatedBy = @UserId and Inactive=0 ))                          
    Select RowNum,TotalCount,                      
  Column1, Column2 , Column3,Column4 ,Column5,Column6,                        
  Column7,Column8,Column9, Column10,Column11,Column12,Column13,Column14, Column15,                         
  Id ,UpdatedOn,ClientId,CreatedOn,SelectedClientId , OriginalFilename                  
    from CTE_Stock                          
    where RowNum > @FirstRec and RowNum <= @LastRec                         
    ORDER BY CreatedOn desc                        
end 


GO


