///<reference name="MicrosoftAjax.js" />
///<reference path="~/Common/Data/Data.js" />
///<reference path="~/Common/Functions/Functions.js" />
///<reference path="~/Controls/IconButton/IconButton.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - dispose everything fully
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls");

Rebound.GlobalTrader.Site.Controls.LanguageSwitch = function(element) { 
	Rebound.GlobalTrader.Site.Controls.LanguageSwitch.initializeBase(this, [element]);
	this._aryLinkIDs = [];
	this._blnSelected = false;
	this._intHideTimeoutID = -1;
	this._strCurrentCulture = "";
};

Rebound.GlobalTrader.Site.Controls.LanguageSwitch.prototype = {

	get_aryLinkIDs: function() { return this._aryLinkIDs; }, 	set_aryLinkIDs: function(v) { if (this._aryLinkIDs !== v)  this._aryLinkIDs = v; }, 
	get_strCurrentCulture: function() { return this._strCurrentCulture; }, 	set_strCurrentCulture: function(v) { if (this._strCurrentCulture !== v)  this._strCurrentCulture = v; }, 

	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.LanguageSwitch.callBaseMethod(this, "initialize");
		if (this._aryLinkIDs.length > 1) {
			$addHandler(this.get_element(), "mouseover", Function.createDelegate(this, this.onMouseOver));
			$addHandler(this.get_element(), "mouseout", Function.createDelegate(this, this.onMouseOut));
			this.setupLinks();
		}
	},

	dispose: function() { 
		if (this.isDisposed) return;
		if (this.get_element()) $clearHandlers(this.get_element());
		for (var i = 0, l = this._aryLinkIDs.length; i < l; i++) {
			var hyp = $get(String.format("{0}_hyp", this._aryLinkIDs[i]));
			if (hyp) $clearHandlers(hyp);
			hyp = null;
		}
		this._aryLinkIDs = null;
		this._blnSelected = null;
		this._intHideTimeoutID = null;
		this._strCurrentCulture = null;
		Rebound.GlobalTrader.Site.Controls.LanguageSwitch.callBaseMethod(this, "dispose");
		this.isDisposed = true;
	},
	
	setupLinks: function() {
		for (var i = 0, l = this._aryLinkIDs.length; i < l; i++) {
			var hyp = $get(String.format("{0}_hyp", this._aryLinkIDs[i]));
			$addHandler(hyp, "click", Function.createDelegate(this, this.onLinkClick));
			hyp = null;
		}	
	},

	onMouseOver: function() {
		clearTimeout(this._intHideTimeoutID);
		Sys.UI.DomElement.addCssClass(this.get_element(), "languageSelectHover");
		this.setAllLinksVisible(true);
	},
	
	onMouseOut: function() {
		clearTimeout(this._intHideTimeoutID);
		var strFN = String.format("$find('{0}').completeMouseOut()", this.get_element().id);
		this._intHideTimeoutID = setTimeout(strFN, 100);
	},
	
	completeMouseOut: function() {
		Sys.UI.DomElement.removeCssClass(this.get_element(), "languageSelectHover");
		this.setAllLinksVisible(false);
	},
	
	setAllLinksVisible: function(blnSelect) {
		for (var i = 0, l = this._aryLinkIDs.length; i < l; i++) {
			var lnk = $get(this._aryLinkIDs[i]);
			if (!lnk._blnSelected) {
				if (blnSelect) {
					Sys.UI.DomElement.addCssClass(lnk, "langSelect"); 
				} else {
					Sys.UI.DomElement.removeCssClass(lnk, "langSelect"); 
				}
			}
			lnk = null;
		}
	},
		
	updateLanguageError: function(e) {
	},
	
	updateLanguageComplete: function(result) {
		location.reload(true);
	},
	
	onLinkClick: function(eventElement) {
		if (this._strCurrentCulture != eventElement.target.getAttribute("bgt_culture")) Rebound.GlobalTrader.Site.WebServices.SetCulture(eventElement.target.getAttribute("bgt_culture"), Function.createDelegate(this, this.updateLanguageComplete), Function.createDelegate(this, this.updateLanguageError));
	}

};

Rebound.GlobalTrader.Site.Controls.LanguageSwitch.registerClass("Rebound.GlobalTrader.Site.Controls.LanguageSwitch", Sys.UI.Control, Sys.IDisposable);