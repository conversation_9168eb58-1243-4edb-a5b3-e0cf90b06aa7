using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;

namespace Rebound.GlobalTrader.Site.Controls.DropDowns.Data {
	[WebService(Namespace = "http://tempuri.org/")]
	[WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    public class LinkMultiCurrency : Rebound.GlobalTrader.Site.Data.DropDowns.Base
    {

		protected override void GetData() {
			
             int? intCustClientNo;
             int? intBuyCurrencyNo;
             intCustClientNo = GetFormValue_NullableInt("CustClientNo");
             intBuyCurrencyNo = GetFormValue_NullableInt("BuyCurrencyNo");
             string strOptions = CacheManager.SerializeOptions(new object[] { SessionManager.ClientID, intCustClientNo, intBuyCurrencyNo });
            string strCachedData = CacheManager.GetDropDownData(_objDropDown.ID, strOptions);
			if (string.IsNullOrEmpty(strCachedData)) {
				JsonObject jsn = new JsonObject();
				JsonObject jsnList = new JsonObject(true);
                List<BLL.Currency> lst = BLL.Currency.DropDownLinkMultiCurrency(SessionManager.ClientID, intCustClientNo, intBuyCurrencyNo);
				for (int i = 0; i < lst.Count; i++) {
					JsonObject jsnItem = new JsonObject();
					jsnItem.AddVariable("ID", lst[i].CurrencyId);
					jsnItem.AddVariable("Name", String.Format("{1} - {0}", lst[i].CurrencyDescription, lst[i].CurrencyCode));
					jsnItem.AddVariable("Code", lst[i].CurrencyCode);
                    jsnItem.AddVariable("LinkID", lst[i].LinkMultiCurrencyId);
					jsnList.AddVariable(jsnItem);
					jsnItem.Dispose(); jsnItem = null;
				}
				lst.Clear(); lst = null;
				jsn.AddVariable("Currencies", jsnList);
				jsnList.Dispose(); jsnList = null;
				CacheManager.StoreDropDown(_objDropDown.ID, strOptions, jsn.Result, CacheManager.CacheExpiryType.OneWorkingDay);
				OutputResult(jsn);
				jsn.Dispose(); jsn = null;
			} else {
				_context.Response.Write(strCachedData);
			}
			strCachedData = null;
		}
	}
}
