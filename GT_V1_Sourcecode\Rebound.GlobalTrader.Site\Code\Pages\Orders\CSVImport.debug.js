///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference name="~/Controls/IconButton/IconButton.js" />
///<reference name="~/Controls/ReboundTextBox/ReboundTextBox.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//Marker     Changed by      Date         Remarks
//[001]      Vinay           12/10/2012   Upload PDF document for invoices
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Pages.Orders");

Rebound.GlobalTrader.Site.Pages.Orders.CSVImport = function(el) { 
	Rebound.GlobalTrader.Site.Pages.Orders.CSVImport.initializeBase(this, [el]);
};

Rebound.GlobalTrader.Site.Pages.Orders.CSVImport.prototype = {

   
    get_ctlUploadExcelDragDrop: function() { return this._ctlUploadExcelDragDrop; }, set_ctlUploadExcelDragDrop: function(v) { if (this._ctlUploadExcelDragDrop !== v) this._ctlUploadExcelDragDrop = v; },
    get_ctlCsvUploadHistory: function() { return this._ctlCsvUploadHistory; }, set_ctlCsvUploadHistory: function(v) { if (this._ctlCsvUploadHistory !== v) this._ctlCsvUploadHistory = v; },
   
    initialize: function() {
        Rebound.GlobalTrader.Site.Pages.Orders.CSVImport.callBaseMethod(this, "initialize");
    },

    goInit: function() {
        if (this._ctlUploadExcelDragDrop) {
         this._ctlUploadExcelDragDrop.addPotentialStatusChange(Function.createDelegate(this, this.ctlUploadExcelDragDrop_PotentialStatusChange));
       }
        Rebound.GlobalTrader.Site.Pages.Orders.CSVImport.callBaseMethod(this, "goInit");
    },

    dispose: function() {
        if (this.isDisposed) return;
        if (this._ctlUploadExcelDragDrop) this._ctlUploadExcelDragDrop.dispose();
        if (this._ctlCsvUploadHistory) this._ctlCsvUploadHistory.dispose();
       
        this._ctlUploadExcelDragDrop = null;
        this._ctlCsvUploadHistory = null;
       
        Rebound.GlobalTrader.Site.Pages.Orders.CSVImport.callBaseMethod(this, "dispose");
    },

   ctlUploadExcelDragDrop_PotentialStatusChange: function() {
       
        this._ctlCsvUploadHistory.getHistory();
      
    }
   
};
Rebound.GlobalTrader.Site.Pages.Orders.CSVImport.registerClass("Rebound.GlobalTrader.Site.Pages.Orders.CSVImport", Rebound.GlobalTrader.Site.Pages.Content, Sys.IDisposable);
