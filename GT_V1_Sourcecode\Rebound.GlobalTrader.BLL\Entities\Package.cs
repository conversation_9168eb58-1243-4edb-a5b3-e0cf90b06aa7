﻿using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;
using Rebound.GlobalTrader.DAL;
using Rebound.GlobalTrader.BLL;

namespace Rebound.GlobalTrader.BLL {
		public partial class Package : BizObject {
		
		#region Properties

		protected static DAL.PackageElement Settings {
			get { return Globals.Settings.Packages; }
		}

		/// <summary>
		/// PackageId
		/// </summary>
		public System.Int32 PackageId { get; set; }		
		/// <summary>
		/// PackageName
		/// </summary>
		public System.String PackageName { get; set; }		
		/// <summary>
		/// PackageDescription
		/// </summary>
		public System.String PackageDescription { get; set; }		
		/// <summary>
		/// Inactive
		/// </summary>
		public System.Boolean Inactive { get; set; }


        /// <summary>
        /// UpdatedBy
        /// </summary>
        public System.Int32? UpdatedBy { get; set; }		
		/// <summary>
		/// DLUP
		/// </summary>
		public System.DateTime DLUP { get; set; }

        /// <summary>
        /// ClientId (from Table)
        /// </summary>
        public System.Int32 ClientId { get; set; }
        /// <summary>
        /// ClientName (from Table)
        /// </summary>
        public System.String ClientName { get; set; }
        /// <summary>
        /// ParentClientNo (from Table)
        /// </summary>
        public System.Int32? ParentClientNo { get; set; }
        /// <summary>
        /// CurrencyNo (from Table)
        /// </summary>
        public System.Int32 CurrencyNo { get; set; }
        /// <summary>
        /// Telephone (from Table)
        /// </summary>
        public System.String Telephone { get; set; }
        /// <summary>
        /// Fax (from Table)
        /// </summary>
        public System.String Fax { get; set; }
        /// <summary>
        /// EMail (from Table)
        /// </summary>
        public System.String EMail { get; set; }
        /// <summary>
        /// URL (from Table)
        /// </summary>
        public System.String URL { get; set; }
        /// <summary>
        /// ClientBillTo (from Table)
        /// </summary>
        public System.String ClientBillTo { get; set; }
        /// <summary>
        /// CustomerCode (from Table)
        /// </summary>
        public System.String CustomerCode { get; set; }


        public System.String AddressName { get; set; }

        public System.String AddressLine1 { get; set; }
        public System.String AddressLine2 { get; set; }
        public System.String AddressLine3 { get; set; }
        public System.String Town { get; set; }
        public System.String County { get; set; }
        public System.String State { get; set; }
        public System.String Postcode { get; set; }

        public System.Int32? Country { get; set; }

        //          GetFormValue_String("AddressName")
        //, GetFormValue_String("AddressLine1")
        //, GetFormValue_String("AddressLine2")
        //, GetFormValue_String("AddressLine3")
        //, GetFormValue_String("Town")
        //, GetFormValue_String("County")
        //, GetFormValue_String("State")
        //, GetFormValue_NullableInt("Country")
        //, GetFormValue_String("Postcode")
        /// <summary>
        /// TaxNo (from Table)
        /// </summary>
        public System.Int32 TaxNo { get; set; }
        /// <summary>
        /// TermsNo (from Table)
        /// </summary>
        public System.Int32 TermsNo { get; set; }
        /// <summary>
        /// ShipViaNo (from Table)
        /// </summary>
        public System.Int32 ShipViaNo { get; set; }
        /// <summary>
        /// ShipViaName (from Table)
        /// </summary>
        public System.String ShipViaName { get; set; }
        /// <summary>
        /// TermsName (from Table)
        /// </summary>
        public System.String TermsName { get; set; }
        /// <summary>
        /// TaxName (from Table)
        /// </summary>
        public System.String TaxName { get; set; }

        public System.Int32 MasterLoginId { get; set; }
        public System.String ADLoginName { get; set; }
        public System.String EmployeeName { get; set; }
        /// <summary>
		/// ECCNId
		/// </summary>
		public System.Int32 ECCNId { get; set; }
        /// <summary>
        /// ECCNCode
        /// </summary>
        public System.String ECCNCode { get; set; }
        public System.Boolean? ECCNStatus { get; set; }
        public System.String ECCNWarning { get; set; }

        //
        public System.String addressName { get; set; }
        public System.String line1 { get; set; }
        public System.String line2 { get; set; }
        public System.String line3 { get; set; }
        public System.String city { get; set; }
        public System.String county { get; set; }
        public System.String state { get; set; }
        public System.Int32? countryNo { get; set; }
        public System.String zip { get; set; }
        /// <summary>
        #endregion

        #region Methods

        /// <summary>
        /// Delete
        /// Calls [usp_delete_Package]
        /// </summary>
        public static bool Delete(System.Int32? packageId) {
			return Rebound.GlobalTrader.DAL.SiteProvider.Package.Delete(packageId);
		}
		/// <summary>
		/// DropDown
		/// Calls [usp_dropdown_Package]
		/// </summary>
		public static List<Package> DropDown() {
			List<PackageDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.Package.DropDown();
			if (lstDetails == null) {
				return new List<Package>();
			} else {
				List<Package> lst = new List<Package>();
				foreach (PackageDetails objDetails in lstDetails) {
					Rebound.GlobalTrader.BLL.Package obj = new Rebound.GlobalTrader.BLL.Package();
					obj.PackageId = objDetails.PackageId;
					obj.PackageDescription = objDetails.PackageDescription;
					obj.PackageName = objDetails.PackageName;
					lst.Add(obj);
					obj = null;
				}
				lstDetails = null;
				return lst;
			}
		}
		/// <summary>
		/// Insert
		/// Calls [usp_insert_Package]
		/// </summary>
		public static Int32 Insert(System.String packageName, System.String packageDescription, System.Int32? updatedBy) {
			Int32 objReturn = Rebound.GlobalTrader.DAL.SiteProvider.Package.Insert(packageName, packageDescription, updatedBy);
			return objReturn;
		}
		/// <summary>
		/// Insert (without parameters)
		/// Calls [usp_insert_Package]
		/// </summary>
		public Int32 Insert() {
			return Rebound.GlobalTrader.DAL.SiteProvider.Package.Insert(PackageName, PackageDescription, UpdatedBy);
		}
		/// <summary>
		/// Get
		/// Calls [usp_select_Package]
		/// </summary>
		public static Package Get(System.Int32? packageId) {
			Rebound.GlobalTrader.DAL.PackageDetails objDetails = Rebound.GlobalTrader.DAL.SiteProvider.Package.Get(packageId);
			if (objDetails == null) {
				return null;
			} else {
				Package obj = new Package();
				obj.PackageId = objDetails.PackageId;
				obj.PackageName = objDetails.PackageName;
				obj.PackageDescription = objDetails.PackageDescription;
				obj.Inactive = objDetails.Inactive;
				obj.UpdatedBy = objDetails.UpdatedBy;
				obj.DLUP = objDetails.DLUP;
				objDetails = null;
				return obj;
			}
		}

        /// <summary>
        /// Get
        /// Calls [usp_select_ClientSetupById]
        /// </summary>
        public static Package GetClient(System.Int32? clientId)
        {
            Rebound.GlobalTrader.DAL.PackageDetails objDetails = Rebound.GlobalTrader.DAL.SiteProvider.Package.GetClient(clientId);
            if (objDetails == null)
            {
                return null;
            }
            else
            {
                Package obj = new Package();
                obj.ClientId = objDetails.ClientId;
                obj.ClientName = objDetails.ClientName;
                obj.ParentClientNo = objDetails.ParentClientNo;
                obj.CurrencyNo = objDetails.CurrencyNo;
                obj.Telephone = objDetails.Telephone;
                obj.Fax = objDetails.Fax;
                obj.EMail = objDetails.EMail;
                obj.URL = objDetails.URL;
                obj.Inactive = objDetails.Inactive;
                obj.UpdatedBy = objDetails.UpdatedBy;
                obj.ClientBillTo = objDetails.ClientBillTo;
                obj.CustomerCode = objDetails.CustomerCode;
                obj.TaxNo = objDetails.TaxNo;
                obj.TermsNo = objDetails.TermsNo;
                obj.ShipViaNo = objDetails.ShipViaNo;
                obj.ShipViaName = objDetails.ShipViaName;
                obj.TermsName = objDetails.TermsName;
                obj.TaxName = objDetails.TaxName;
                obj.DLUP = objDetails.DLUP;
                objDetails = null;
                return obj;
            }
        }
        /// <summary>
        /// Get
        /// Calls [usp_select_MasterLoginSetupById]
        /// </summary>
        public static Package GetMasterLogin(System.Int32? MasterLoginId)
        {
            Rebound.GlobalTrader.DAL.PackageDetails objDetails = Rebound.GlobalTrader.DAL.SiteProvider.Package.GetMasterLogin(MasterLoginId);
            if (objDetails == null)
            {
                return null;
            }
            else
            {
                Package obj = new Package();
                obj.MasterLoginId = objDetails.MasterLoginId;
                obj.ADLoginName = objDetails.ADLoginName;
                obj.EmployeeName = objDetails.EmployeeName;
                obj.Inactive = objDetails.Inactive;
                obj.ClientId = objDetails.ClientId;
                objDetails = null;
                return obj;
            }
        }
        /// <summary>
        /// GetList
        /// Calls [usp_selectAll_Package]
        /// </summary>
        public static List<Package> GetList() {
			List<PackageDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.Package.GetList();
			if (lstDetails == null) {
				return new List<Package>();
			} else {
				List<Package> lst = new List<Package>();
				foreach (PackageDetails objDetails in lstDetails) {
					Rebound.GlobalTrader.BLL.Package obj = new Rebound.GlobalTrader.BLL.Package();
					obj.PackageId = objDetails.PackageId;
					obj.PackageName = objDetails.PackageName;
					obj.PackageDescription = objDetails.PackageDescription;
					obj.Inactive = objDetails.Inactive;
					obj.UpdatedBy = objDetails.UpdatedBy;
					obj.DLUP = objDetails.DLUP;
					lst.Add(obj);
					obj = null;
				}
				lstDetails = null;
				return lst;
			}
		}

        /// <summary>
        /// GetList
        /// Calls [usp_select_ClientSetup_All]
        /// </summary>
        public static List<Package> GetClientList()
        {
            List<PackageDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.Package.GetClientList();
            if (lstDetails == null)
            {
                return new List<Package>();
            }
            else
            {
                List<Package> lst = new List<Package>();
                foreach (PackageDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.Package obj = new Rebound.GlobalTrader.BLL.Package();
                    obj.ClientId = objDetails.ClientId;
                    obj.ClientName = objDetails.ClientName;
                    obj.ParentClientNo = objDetails.ParentClientNo;
                    obj.CurrencyNo = objDetails.CurrencyNo;
                    obj.Telephone = objDetails.Telephone;
                    obj.Fax = objDetails.Fax;
                    obj.EMail = objDetails.EMail;
                    obj.URL = objDetails.URL;
                    obj.Inactive = objDetails.Inactive;
                    obj.UpdatedBy = objDetails.UpdatedBy;
                    obj.ClientBillTo = objDetails.ClientBillTo;
                    obj.CustomerCode = objDetails.CustomerCode;
                    obj.TaxNo = objDetails.TaxNo;
                    obj.TermsNo = objDetails.TermsNo;
                    obj.ShipViaNo = objDetails.ShipViaNo;
                    obj.ShipViaName = objDetails.ShipViaName;
                    obj.TermsName = objDetails.TermsName;
                    obj.TaxName = objDetails.TaxName;
                    obj.DLUP = objDetails.DLUP;
                    obj.addressName = objDetails.addressName;
                    obj.city = objDetails.city;
                    obj.line1 = objDetails.line1;
                    obj.line2 = objDetails.line2;
                    obj.line3 = objDetails.line3;
                    obj.county = objDetails.county;
                    obj.state = objDetails.state;
                    obj.zip = objDetails.zip;
                    obj.countryNo = objDetails.countryNo;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }

        /// <summary>
        /// GetList
        /// Calls [usp_select_MasterLoginSetup_All]
        /// </summary>
        public static List<Package> GetMasterLoginList()
        {
            List<PackageDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.Package.GetMasterLoginList();
            if (lstDetails == null)
            {
                return new List<Package>();
            }
            else
            {
                List<Package> lst = new List<Package>();
                foreach (PackageDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.Package obj = new Rebound.GlobalTrader.BLL.Package();
                    obj.MasterLoginId = objDetails.MasterLoginId;
                    obj.ADLoginName = objDetails.ADLoginName;
                    obj.EmployeeName = objDetails.EmployeeName;
                    obj.Inactive = objDetails.Inactive;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }

        /// <summary>
        /// Update
        /// Calls [usp_update_Package]
        /// </summary>
        public static bool Update(System.Int32? packageId, System.String packageName, System.String packageDescription, System.Boolean? inactive, System.Int32? updatedBy) {
			return Rebound.GlobalTrader.DAL.SiteProvider.Package.Update(packageId, packageName, packageDescription, inactive, updatedBy);
		}
		/// <summary>
		/// Update (without parameters)
		/// Calls [usp_update_Package]
		/// </summary>
		public bool Update() {
			return Rebound.GlobalTrader.DAL.SiteProvider.Package.Update(PackageId, PackageName, PackageDescription, Inactive, UpdatedBy);
		}


        /// <summary>
        /// UpdateClient
        /// Calls [usp_update_ClientDetail]
        /// </summary>
        /// 
            
        public static bool UpdateClient(System.Int32? clientId, System.String clientBillTo, System.String customerCode, Int32? termsNo,Int32? shipViaNo,Int32? taxNo, System.Boolean? inactive, System.Int32? updatedBy, System.String addressName, System.String line1, System.String line2, System.String line3, System.String city, System.String county, System.String state, System.Int32? countryNo, System.String zip)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Package.UpdateClient(clientId,  clientBillTo, customerCode, termsNo,shipViaNo,taxNo,  inactive,  updatedBy, addressName, line1, line2, line3, city, county, state, countryNo, zip);
        }
        /// <summary>
        /// UpdateClient (without parameters)
        /// Calls [usp_update_ClientDetail]
        /// </summary>
        public bool UpdateClient()
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Package.UpdateClient(ClientId, ClientBillTo,CustomerCode,TermsNo,ShipViaNo,TaxNo,  Inactive, UpdatedBy, addressName, line1, line2, line3, city, county, state, countryNo, zip);
        }

        /// <summary>
        /// UpdateClient (without parameters)
        /// Calls [usp_update_MasterLoginDetail]
        /// </summary>
        public bool UpdateMasterLogin()
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Package.UpdateMasterLogin(MasterLoginId, Inactive);
        }

        private static Package PopulateFromDBDetailsObject(PackageDetails obj) {
            Package objNew = new Package();
			objNew.PackageId = obj.PackageId;
			objNew.PackageName = obj.PackageName;
			objNew.PackageDescription = obj.PackageDescription;
			objNew.Inactive = obj.Inactive;
			objNew.UpdatedBy = obj.UpdatedBy;
			objNew.DLUP = obj.DLUP;
            return objNew;
        }
        public static List<Package> AutoSearch(System.String nameSearch, Boolean? showInactive)
        {
            List<PackageDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.Package.AutoSearch(nameSearch, showInactive);
            if (lstDetails == null)
            {
                return new List<Package>();
            }
            else
            {
                List<Package> lst = new List<Package>();
                foreach (PackageDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.Package obj = new Rebound.GlobalTrader.BLL.Package();
                    obj.PackageId = objDetails.PackageId;
                    obj.PackageDescription = objDetails.PackageDescription;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }
        public static List<Package> AllSearch()
        {
            List<PackageDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.Package.AllSearch();
            if (lstDetails == null)
            {
                return new List<Package>();
            }
            else
            {
                List<Package> lst = new List<Package>();
                foreach (PackageDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.Package obj = new Rebound.GlobalTrader.BLL.Package();
                    obj.PackageId = objDetails.PackageId;
                    obj.PackageDescription = objDetails.PackageDescription;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }
        //Part ECCN Mapped data Search
        public static List<Package> AutoSearchPartEccnCode(System.String nameSearch, Boolean? showInactive)
        {
            List<PackageDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.Package.AutoSearchPartEccnCode(nameSearch, showInactive);
            if (lstDetails == null)
            {
                return new List<Package>();
            }
            else
            {
                List<Package> lst = new List<Package>();
                foreach (PackageDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.Package obj = new Rebound.GlobalTrader.BLL.Package();
                    obj.ECCNId = objDetails.ECCNId;
                    obj.ECCNCode = objDetails.ECCNCode;
                    obj.ECCNStatus = objDetails.ECCNStatus;
                    obj.ECCNWarning = objDetails.ECCNWarning;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }
        //code end
        #endregion

    }
}