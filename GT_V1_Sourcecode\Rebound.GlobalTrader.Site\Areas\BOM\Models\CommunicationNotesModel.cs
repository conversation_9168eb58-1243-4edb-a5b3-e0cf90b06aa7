﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace Rebound.GlobalTrader.Site.Areas.BOM.Models
{
    public class CommunicationNotesModel
    {

        public int ID { get; set; }
        public string Note { get; set; }
        public string DateTimeNote { get; set; }
        public string EmployeeName { get; set; }
        public string ReqNos { get; set; }
        public string NoteTo { get; set; }
        public string CCUserID { get; set; }
        public string SendToGroup { get; set; }
        public int Line { get; set; }
    }

    public class CommunicationNoteGroupModel
    {
        public string Id { get; set; }
        public string Name { get; set; }
    }
}