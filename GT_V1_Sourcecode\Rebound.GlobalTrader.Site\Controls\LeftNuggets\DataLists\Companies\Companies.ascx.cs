//-----------------------------------------------------------------------------------------
// RP 26.10.2009:
// - new control
//-----------------------------------------------------------------------------------------
using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site;
using Rebound.GlobalTrader.Site.Controls;
using Rebound.GlobalTrader.Site.Enumerations;

namespace Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists {

	public partial class Companies : Base {

		private Controls.DataListNuggets.CompanyListType _enmCompanyListType;
		public Controls.DataListNuggets.CompanyListType CompanyListType {
			get { return _enmCompanyListType; }
			set {
				_enmCompanyListType = value;
				SetDataListNuggetType("Companies", _enmCompanyListType);
			}
		}

		protected override void OnInit(EventArgs e) {
			SetLeftNuggetType("DataList_Companies");
			SetDataListNuggetType("Companies", _enmCompanyListType);
			LoadDataListNugget(_objDataListNugget.Name);
			TitleResource = "QuickBrowse";
			base.OnInit(e);
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Companies", ctlDesignBase.ClientID);
			AddScriptReference("Controls.LeftNuggets.DataLists.Companies.Companies");
			SetupTable();
		}

		protected override void OnLoad(EventArgs e) {
			_scScriptControlDescriptor.AddProperty("enmCompanyListType", _enmCompanyListType);
			base.OnLoad(e);
		}

		private void SetupTable() {
			_tbl.AllowSelection = false;
			_tbl.SortColumnDirection = SortColumnDirection.ASC;
			string strColumn = "";
			switch (_enmCompanyListType) {
				case Rebound.GlobalTrader.Site.Controls.DataListNuggets.CompanyListType.AllCompanies: strColumn = "Company"; break;
				case Rebound.GlobalTrader.Site.Controls.DataListNuggets.CompanyListType.Customers: strColumn = "Customer"; break;
				case Rebound.GlobalTrader.Site.Controls.DataListNuggets.CompanyListType.Suppliers: strColumn = "Supplier"; break;
				case Rebound.GlobalTrader.Site.Controls.DataListNuggets.CompanyListType.Prospects: strColumn = "Prospect"; break;
				case Rebound.GlobalTrader.Site.Controls.DataListNuggets.CompanyListType.Manufacturers: strColumn = "Manufacturer"; break;
				case Rebound.GlobalTrader.Site.Controls.DataListNuggets.CompanyListType.Contacts: strColumn = "Contact"; break;
			}
			_tbl.Columns.Add(new FlexiDataColumn(strColumn, Unit.Empty, true));
			strColumn = null;
		}

		protected override void AddNewFilterItemsToStartOfList() {
			AddNewDropDownFilter("ViewLevel", "Rebound.GlobalTrader.Site", "ViewLevel", "ViewLevel", "ViewLevel", Convert.ToInt32(SessionManager.DefaultListPageView));
			ShowFilter("ViewLevel", true);
			SetFilterValue("ViewLevel", Convert.ToInt32(SessionManager.DefaultListPageView));
			base.AddNewFilterItemsToStartOfList();
		}

		protected override void RenderAdditionalState() {
			SetFilterValue("ViewLevel", this.GetSavedStateValue("ViewLevel"));
			base.RenderAdditionalState();
		}

	}

}