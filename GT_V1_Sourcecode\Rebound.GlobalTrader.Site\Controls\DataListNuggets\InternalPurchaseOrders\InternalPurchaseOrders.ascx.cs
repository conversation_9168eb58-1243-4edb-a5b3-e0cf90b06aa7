﻿/* Marker     changed by      date         Remarks  
   [001]      <PERSON><PERSON><PERSON>     27-Sep-2018   REB-13083 Change request PO - delivery status
*/
using System;
using System.Collections;
using System.Configuration;
using System.Data;
using System.Linq;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.HtmlControls;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Xml.Linq;

namespace Rebound.GlobalTrader.Site.Controls.DataListNuggets {

    public partial class InternalPurchaseOrders  : Base {

		#region Properties
		#endregion

		#region Overrides

		/// <summary>
		/// OnInit
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
            SetDataListNuggetType("InternalPurchaseOrders");
			base.OnInit(e);
			TitleText = Functions.GetGlobalResource("Nuggets", "InternalPurchaseOrders");
            AddScriptReference("Controls.DataListNuggets.InternalPurchaseOrders.InternalPurchaseOrders.js");
			SetupTable();
		}

		protected override void OnLoad(EventArgs e) {
            _scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.DataListNuggets.InternalPurchaseOrders", ctlDesignBase.ClientID);
            _scScriptControlDescriptor.AddProperty("intBuyerID", _objQSManager.BuyerID > 0 ? _objQSManager.BuyerID : 0);
            _scScriptControlDescriptor.AddProperty("blnPOHub", Convert.ToBoolean(SessionManager.IsPOHub));
			base.OnLoad(e);
		}

		protected override void RenderAdditionalState() {
			string strViewLevel = this.GetSavedStateValue("ViewLevel");
			if (!string.IsNullOrEmpty(strViewLevel)) {
				((Pages.Content)Page).CurrentTab = Convert.ToInt32(strViewLevel);
				_enmViewLevel = (ViewLevelList)Convert.ToInt32(strViewLevel);
				this.OnAskPageToChangeTab();
			}
			base.RenderAdditionalState();
		}

		#endregion

		private void SetupTable() {
			_tbl.AllowSelection = false;
			_tbl.SortColumnDirection = Rebound.GlobalTrader.Site.Enumerations.SortColumnDirection.DESC;
			_tbl.Columns.Add(new FlexiDataColumn("IPONo", WidthManager.GetWidth(WidthManager.ColumnWidth.SystemDocumentNumber), true));
			_tbl.Columns.Add(new FlexiDataColumn("PartNo", "Manufacturer", WidthManager.GetWidth(WidthManager.ColumnWidth.PartNo), true));
			_tbl.Columns.Add(new FlexiDataColumn("Price", WidthManager.GetWidth(WidthManager.ColumnWidth.CurrencyValue), true));
			_tbl.Columns.Add(new FlexiDataColumn("QuantityOrdered", "QuantityOutstanding", WidthManager.GetWidth(WidthManager.ColumnWidth.Quantity), true));
            _tbl.Columns.Add(new FlexiDataColumn("Company", WidthManager.GetWidth(WidthManager.ColumnWidth.CompanyName), true));
			_tbl.Columns.Add(new FlexiDataColumn("DeliveryDate","RequireASAP", WidthManager.GetWidth(WidthManager.ColumnWidth.Date), true));
            _tbl.Columns.Add(new FlexiDataColumn("IPOStatus", "DeliveryStatus", Unit.Pixel(109), false));
            _tbl.Columns.Add(new FlexiDataColumn("Status", Unit.Pixel(104), false));
            //[001] start
            //_tbl.Columns.Add(new FlexiDataColumn("DeliveryStatus", Unit.Empty, true));
            //_tbl.Columns.Add(new FlexiDataColumn("DeliveryStatus", WidthManager.GetWidth(WidthManager.ColumnWidth.DeliveryStatus), true));
            //[001] end
		}

	}
}