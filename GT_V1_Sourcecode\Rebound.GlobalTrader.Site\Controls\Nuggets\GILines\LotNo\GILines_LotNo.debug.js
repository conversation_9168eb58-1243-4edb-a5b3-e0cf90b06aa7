///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//Marker     Changed by      Date         Remarks
//[001]      Vinay           26/07/2012   Set the application to only accept qtys greater than 0:- ESMS Task-103
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.GILines_LotNo = function(element) { 
	Rebound.GlobalTrader.Site.Controls.Forms.GILines_LotNo.initializeBase(this, [element]);
	
	this._intLineID = -1;
	this._intGIID = -1;
	this._intLotID = -1;
	this._lotDetail = -1;
	this._status = null;
	this._intInvoiceLineNo = -1;
	this._invoiceExist = false;
	this._Quantity = -1;
	this._countLotNo = -1;
	this._isDeleted = false;
	this._blnSerNoRecorded = false;
};

Rebound.GlobalTrader.Site.Controls.Forms.GILines_LotNo.prototype = {
    get_intLotID: function () { return this._intLotID; }, set_intLotID: function (v) { if (this._intLotID !== v) this._intLotID = v; },
    get_intGIID: function () { return this._intGIID; }, set_intGIID: function (v) { if (this._intGIID !== v) this._intGIID = v; },
    get_intLineID: function () { return this._intLineID; }, set_intLineID: function (v) { if (this._intLineID !== v) this._intLineID = v; },
    get_btnAdd: function () { return this._btnAdd; }, set_btnAdd: function (v) { if (this._btnAdd !== v) this._btnAdd = v; },
    get_btnRefresh: function () { return this._btnRefresh; }, set_btnRefresh: function (v) { if (this._btnRefresh !== v) this._btnRefresh = v; },
    get_btnUpdate: function () { return this._btnUpdate; }, set_btnUpdate: function (v) { if (this._btnUpdate !== v) this._btnUpdate = v; },
    get_lblDuplicateError: function () { return this._lblDuplicateError; }, set_lblDuplicateError: function (v) { if (this._lblDuplicateError !== v) this._lblDuplicateError = v; },
    get_lblLotCount: function () { return this._lblLotCount; }, set_lblLotCount: function (v) { if (this._lblLotCount !== v) this._lblLotCount = v; },
    //get_tblLotNodetails: function () { return this._tblLotNodetails; }, set_tblLotNodetails: function (v) { if (this._tblLotNodetails !== v) this._tblLotNodetails = v; },
    get_intInvoiceLineNo: function () { return this._intInvoiceLineNo; }, set_intInvoiceLineNo: function (v) { if (this._intInvoiceLineNo !== v) this._intInvoiceLineNo = v; },
    get_status: function () { return this._status; }, set_status: function (v) { if (this._status !== v) this._status = v; },
    get_invoiceExist: function () { return this._invoiceExist; }, set_invoiceExist: function (v) { if (this._invoiceExist !== v) this._invoiceExist = v; },
    get_isDeleted: function () { return this._isDeleted; }, set_isDeleted: function (v) { if (this._isDeleted !== v) this._isDeleted = v; },
    get_btnRefGrid: function () { return this._btnRefGrid; }, set_btnRefGrid: function (v) { if (this._btnRefGrid !== v) this._btnRefGrid = v; },
    get_ctlGiTempLotNo: function () { return this._ctlGiTempLotNo; }, set_ctlGiTempLotNo: function (v) { if (this._ctlGiTempLotNo !== v) this._ctlGiTempLotNo = v; },

    initialize: function () {
        Rebound.GlobalTrader.Site.Controls.Forms.GILines_LotNo.callBaseMethod(this, "initialize");
        this.addShown(Function.createDelegate(this, this.formShown));
        this.addSave(Function.createDelegate(this, this.saveLotClicked));
        // this.addCancel(Function.createDelegate(this, this.cancelClicked));
        
        $R_TXTBOX.addEnterPressedEvent(document.getElementById("ctl00_cphMain_ctlLines_ctlDB_ctl14_frmLotNo_ctlDB_ctlLotNo_ctl04_txtSerailNo"), Function.createDelegate(this, this.saveClicked));
    
        //this._tblLotNodetails.addSelectedIndexChanged(Function.createDelegate(this, this.getLotDetail));
        this._ctlGiTempLotNo.addItemSelected(Function.createDelegate(this, this.getLotDetail));
        $R_FN.showElement(this._btnUpdate, false);
        $R_FN.showElement(this._btnAdd, true);
        $R_FN.showElement(this._btnRefresh, true);   
        $R_FN.showElement(this._lblDuplicateError, false);
       // $R_FN.showElement(this._lblLotCount, false);
        this.showField("ctlLotNoDetail", false);
        //this.setFieldValue("ctlSubGroup", 0, null, "");
        this.setFieldValue("ctlLotNo", "");
        this.setFieldValue("ctlQuantity", this._Quantity);
        if (this._btnAdd) $addHandler(this._btnAdd, "click", Function.createDelegate(this, this.saveClicked));
        if (this._btnUpdate) $addHandler(this._btnUpdate, "click", Function.createDelegate(this, this.updateClicked));
        if (this._btnRefresh) $addHandler(this._btnRefresh, "click", Function.createDelegate(this, this.refreshClicked));
        if (this._btnRefGrid) $addHandler(this._btnRefGrid, "click", Function.createDelegate(this, this.refreshGrid));
        this._ctlGiTempLotNo.setFieldValue("ctlLotNo", "t");
        this._ctlGiTempLotNo._elementId = this._element.id;
        document.getElementsByClassName("dataFilter")[0].style.display = "none";
        if (document.getElementsByClassName("itemSearchGo")[0])
            document.getElementsByClassName("itemSearchGo")[0].style.display = "none";
        this._ctlGiTempLotNo.addPotentialStatusChange(Function.createDelegate(this, this.ctlGiLotNumber_PotentialStatusChange));
        document.getElementById("ctl00_cphMain_ctlLines_ctlDB_ctl14_frmLotNo_ctlDB_ctlAddUpdate_ctl02_ctlGiTempLotNo_ctlDB_tblOuter").style.display = "none";
        document.getElementById("ctl00_cphMain_ctlLines_ctlDB_ctl14_frmLotNo_ctlDB_ctlAddUpdate_ctl02_ctlGiTempLotNo_ctlDB_ctl03_hyp").style.display = "none";
        //this.LoadSerailNoGrid();

        
        
    },

    formShown: function () {
        this.setFieldValue("ctlQuantity", this._Quantity);
        this.showField("ctlLotNoDetail", false);
        //this.setFieldValue("ctlSubGroup", 0, null, "");
        this.setFieldValue("ctlLotNo", "");    
        $R_FN.showElement(this._lblDuplicateError, false);
        $R_FN.showElement(this._lblLotCount, false);
        $R_FN.showElement(this._btnUpdate, false);
        $R_FN.showElement(this._btnAdd, true);
        $R_FN.showElement(this._btnRefresh, false);
        //this.LoadSerailNoGrid();
        $R_IBTN.showButton(document.getElementById("ctl00_cphMain_ctlLines_ctlDB_ctl31_ibtnSave_hyp"), !this._blnSerNoRecorded);
        $R_IBTN.showButton(document.getElementById("ctl00_cphMain_ctlLines_ctlDB_ctl32_ibtnSave_hyp"), !this._blnSerNoRecorded);
        this.refreshGrid();
    },

    dispose: function() {
        if (this.isDisposed) return;
        if (this._ctlGiTempLotNo) this._ctlGiTempLotNo.dispose();
        this._intLotID = -1;
        this._btnAdd = null;
        this._btnRefresh = null;
        this._btnUpdate = null;
        this._intGIID = -1;
        this._lblDuplicateError = null;
        this._lblLotCount = null;
        //this._tblLotNodetails = null;
        this._intLineID = null;
        this._lotDetail = -1;
        this._status = null;
        this._invoiceExist = false;
        this._intInvoiceLineNo = -1;
        this._isDeleted = false;
        this._Quantity = null;
        this._countLotNo = null;
        this._blnSerNoRecorded = null;
        this._btnRefGrid = null;
        this._ctlGiTempLotNo = null;
        Rebound.GlobalTrader.Site.Controls.Forms.GILines_LotNo.callBaseMethod(this, "dispose");
    },

    saveLotClicked: function () {
        if (this._lotDetail <= 0) {
            this._strErrorMessage = 'No Record Exist';
            this.showError(true, this._strErrorMessage);
            return;

        }
        //if (this._countLotNo == this._Quantity) {
        //    this._strErrorMessage = 'Cannot insert Lot beyond Quantity limit';
        //    this.showError(true, this._strErrorMessage);
        //    return;
        //}
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/GILines");
        obj.set_DataObject("GILines");
        obj.set_DataAction("AddAllLotNo");
        obj.addParameter("GoodsInId", this._intGIID);
        obj.addParameter("GoodsInLineId", this._intLineID); 
        obj.addDataOK(Function.createDelegate(this, this.saveLotComplete));
        obj.addError(Function.createDelegate(this, this.saveLotError));
        obj.addTimeout(Function.createDelegate(this, this.saveLotError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    saveLotError: function (args) {
        this._strErrorMessage = args._errorMessage;
        this.onSaveError();
    },

    saveLotComplete: function (args) {
        if (args._result.Result == true) {
            //this.setFieldValue("ctlSubGroup", 0, null, "");
            this.setFieldValue("ctlLotNo", "");
            this.onSaveComplete();

        }
        if (args._result.Result == false && args._result.ValidateMessage != null) {
            this._strErrorMessage = args._result.ValidateMessage;
            this.showError(true, this._strErrorMessage);
            $R_FN.showElement(this._lblDuplicateError, false);
            return;
        }
        //else {
        //    this._strErrorMessage = args._errorMessage;
        //   this.onSaveError();
        //}
    },

    cancelClicked: function () {
        alert("sss");
        //this.onCancel();
    },

    saveClicked: function () {
        if (!this.validateForm()) return;
        if (this._countLotNo < this._Quantity) {
            var obj = new Rebound.GlobalTrader.Site.Data();
            obj.set_PathToData("controls/Nuggets/GILines");
            obj.set_DataObject("GILines");
            obj.set_DataAction("AddLotNo");
            obj.addParameter("SubGroup", this.getFieldValue("ctlSubGroup"));
            obj.addParameter("LotNo", this.getFieldValue("ctlLotNo"));
            obj.addParameter("GoodsInId", this._intGIID);
            obj.addParameter("GoodsInLineId", this._intLineID);
            obj.addDataOK(Function.createDelegate(this, this.saveComplete));
            obj.addError(Function.createDelegate(this, this.saveError));
            obj.addTimeout(Function.createDelegate(this, this.saveError));
            $R_DQ.addToQueue(obj);
            $R_DQ.processQueue();
            obj = null;
           
        }
        else {
            this.showError(true, $R_RES.LotNoLimit);
            return;
        }  
    },

    saveError: function (args) {
        this._strErrorMessage = args._errorMessage;
        this.onSaveError();
    },

    saveComplete: function (args) {
       // alert(args._result.TCount);
        if (args._result)
            this._countLotNo = args._result.TCount;
        if (args._result.Result == true) {           
            // this.showField("ctlLotNoDetail", true); 
            $R_FN.setInnerHTML(this._lblLotCount, "Lot No saved successfully. Kindly referesh to load data");
            $R_FN.showElement(this._lblLotCount, true);

            this.setFieldValue("ctlLotNo", "");
            this._strErrorMessage = '';
            this.showError(false, this._strErrorMessage);
            $R_FN.showElement(this._lblDuplicateError, false);
            this.refreshGrid();
            return;
        }
        if (args._result.Result == false && args._result.ValidateMessage !=null) {
            this._strErrorMessage = args._result.ValidateMessage;
            this.showError(true, this._strErrorMessage);
            $R_FN.showElement(this._lblDuplicateError, false);
            $R_FN.setInnerHTML(this._lblLotCount, "");
            $R_FN.showElement(this._lblLotCount, false);
            return;
        }
        if (args._result.Result == false && args._result.ValidateMessage != null) {            
            $R_FN.showElement(this._lblDuplicateError, true);
            $R_FN.setInnerHTML(this._lblLotCount, "");
            $R_FN.showElement(this._lblLotCount, false);
            return;
        }
       

    },

    updateClicked: function () {
        if (!this.validateForm()) return;
          
            var obj = new Rebound.GlobalTrader.Site.Data();
            obj.set_PathToData("controls/Nuggets/GILines");
            obj.set_DataObject("GILines");
            obj.set_DataAction("UpdateLotNo"); 
            obj.addParameter("LotID", this._intLotID);
            obj.addParameter("SubGroup", this.getFieldValue("ctlSubGroup"));
            obj.addParameter("LotNo", this.getFieldValue("ctlLotNo"));
            obj.addParameter("GoodsInId", this._intGIID);
            obj.addParameter("Status", this._status);
            obj.addParameter("GoodsInLineId", this._intLineID);
            obj.addDataOK(Function.createDelegate(this, this.updateComplete));
            obj.addError(Function.createDelegate(this, this.updateError));
            obj.addTimeout(Function.createDelegate(this, this.updateError));
            $R_DQ.addToQueue(obj);
            $R_DQ.processQueue();
            obj = null;     
            
        },
    updateError: function (args) {
        this._strErrorMessage = args._errorMessage;
        this.onSaveError();
    },

    updateComplete: function (args) {
        if (args._result.Result == true) {     
            this.showField("ctlLotNoDetail", true);
            //this.setFieldValue("ctlSubGroup", 0, null, "");
            this.setFieldValue("ctlLotNo", "");
            $R_FN.showElement(this._btnUpdate, false);
            $R_FN.showElement(this._btnAdd, true);
            $R_FN.showElement(this._lblDuplicateError, false);
            $R_FN.showElement(this._btnRefresh, false);
            //this.LoadSerailNoGrid();
            this.refreshGrid();
            return;
        }
        if (args._result.Result == false && args._result.ValidateMessage != null) {        
            $R_FN.showElement(this._lblDuplicateError, true);
            return;
        }
        else {
            this._strErrorMessage = args._errorMessage;
            this.onSaveError();
        }
    },

    LoadSerailNoGrid: function () {         

            var obj = new Rebound.GlobalTrader.Site.Data();
            obj.set_PathToData("controls/Nuggets/GILines");
            obj.set_DataObject("GILines");
            obj.set_DataAction("GetDataGrid");
            obj.addParameter("GoodsInId", this._intGIID);
            obj.addParameter("GoodsInLineId", this._intLineID);
            obj.addDataOK(Function.createDelegate(this, this.getDataGrid));
            obj.addError(Function.createDelegate(this, this.getDataGridError));
            obj.addTimeout(Function.createDelegate(this, this.getDataGridError));
            $R_DQ.addToQueue(obj);
            $R_DQ.processQueue();
            obj = null;
        

    },

    getDataGridError: function (args) {

    },
    getDataGrid: function (args) {
       
        this._invoiceExist = false;
        this._countLotNo = 0;
        res = args._result;
        $R_FN.setInnerHTML(this._lblLotCount, 'Lot No. Count: ' + res.Count + ((this._blnSerNoRecorded) ? ' All required lot numbers are recorded' : ''));
        $R_FN.showElement(this._lblLotCount, res.Count>0);
        this._lotDetail = res.LotNoDetails.length;
        this.showField("ctlLotNoDetail", res.LotNoDetails.length > 0);
        //this.showField("ctlLotNoDetail", true);
        this._tblLotNodetails.clearTable();
        this._countLotNo = res.Count;
        for (var i = 0; i < res.LotNoDetails.length; i++) {
            var row = res.LotNoDetails[i];
            this._invoiceExist = row.InvoiceLineNo > 0 ? true : false;
            var aryData = "";
            this._invoiceExist = true;
            if (this._invoiceExist == false) {
                aryData = [
                 row.SubGroup,
                  row.LotNo,
                row.GoodsInNo,
               // "<a id='" + row.LotNoId + '' + row.Status + "' Style='color:red'  href='javascript:void(0)' onclick='$find.deleteRow();'>" + row.Delete + "</a>"
                  String.format("<a href=\"javascript:void(0);\" Style='color:#006600' class='Delete'  onclick=\"$find('{0}').removeItem({1});\" class=\"quickSearchReselect\">Delete</a>", this._element.id, row.LotNoId)
                ];
            }
            else {
                aryData = [
              row.SubGroup,
               row.LotNo,
             row.GoodsInNo,
            // "<a id='" + row.LotNoId + '' + row.Status + "' Style='color:red'  href='javascript:void(0)' onclick='$find.deleteRow();'>" + row.Delete + "</a>"
               String.format("<a href=\"javascript:void(0);\" Style='color: #006600' Hidden='true'  onclick=\"return false;\" class=\"quickSearchReselect\">Delete</a>", this._element.id, row.LotNoId)
                ];
            }


                var objExtraData = {
                    LotNoId: row.LotNoId,
                    SubGroup: row.SubGroup,
                    LotNo: row.LotNo,
                    GoodsInNo: row.GoodsInNo,
                    Status: row.Status,
                    InvoiceLineNo: row.InvoiceLineNo
                    
                };
            var strCSS = (row.Inactive) ? "ceased" : "";
            this._tblLotNodetails.addRow(aryData, row.LotNoId, false, objExtraData, strCSS);            
            aryData = null;
            row = null;           
        }
        
        this._tblLotNodetails.resizeColumns();
        this._blnSerNoRecorded = this._countLotNo >= this._Quantity;
        if (this._btnAdd) $R_IBTN.showButton(this._btnAdd, !this._blnSerNoRecorded);
        
        //$R_IBTN.showButton(document.getElementById("ctl00_cphMain_ctlLines_ctlDB_ctl31_ibtnSave_hyp"), !this._blnSerNoRecorded);
        //$R_IBTN.showButton(document.getElementById("ctl00_cphMain_ctlLines_ctlDB_ctl32_ibtnSave_hyp"), !this._blnSerNoRecorded);
    },

   

    removeItem: function (id, strStatus) {
       
        var isDelete = confirm("Are you sure you want to delete");
        if (isDelete == true) {        
        this.getValuesByLot();
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/GILines");
        obj.set_DataObject("GILines");
        obj.set_DataAction("DeleteLotNo");
        obj.addParameter("LotNoId", id);
        obj.addParameter("Status", strStatus);
        obj.addParameter("GoodsInId", this._intGIID);
        obj.addParameter("GoodsInLineId", this._intLineID);
        obj.addDataOK(Function.createDelegate(this, this.deleteComplete));
        obj.addError(Function.createDelegate(this, this.deleteError));
        obj.addTimeout(Function.createDelegate(this, this.deleteError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
        } else {
            this.showField("ctlLotNoDetail", true);
            //this.setFieldValue("ctlSubGroup", 0, null, "");
            this.setFieldValue("ctlLotNo", "");
            $R_FN.showElement(this._btnUpdate, false);
            $R_FN.showElement(this._btnAdd, true);
            $R_FN.showElement(this._lblDuplicateError, false);
            $R_FN.showElement(this._btnRefresh, false);
            //this.LoadSerailNoGrid();            
            return false;
        }
    },

    deleteError: function (args) {
        this._strErrorMessage = args._errorMessage;
        this.onSaveError();
    },

    deleteComplete: function (args) {
        if (args._result.Result == true) {
            this.showField("ctlLotNoDetail", true);
            //this.setFieldValue("ctlSubGroup", 0, null, "");
            this.setFieldValue("ctlLotNo", "");
            $R_FN.showElement(this._btnUpdate, false);
            $R_FN.showElement(this._btnAdd, true);
            $R_FN.showElement(this._lblDuplicateError, false);
            $R_FN.showElement(this._btnRefresh, false);
            //this.LoadSerailNoGrid();
            this.refreshGrid();
            return;
        } else {
            this._strErrorMessage = args._errorMessage;
            this.onSaveError();
        }
    },

    getLotDetail: function () {
        $R_FN.showElement(this._lblDuplicateError, false);
        this._strErrorMessage = '';
        this.showError(false, this._strErrorMessage);        
        //this.setFieldValue("ctlSubGroup", 0, null, "");
        this.setFieldValue("ctlLotNo", "");        
        //this._isDeleted = false;
        this.getValuesByLot();        
    },

    getValuesByLot: function () {
        this._intLotID = -1;
       
        //var obj = this._tblLotNodetails.getSelectedExtraData(); if (!obj) return;
        var obj = this._ctlGiTempLotNo._tblResults.getSelectedExtraData(); if (!obj) return;
       // alert(obj.InvoiceLineNo);
        if (obj.InvoiceLineNo <= 0) {
            this.setFieldValue("ctlSubGroup", obj.SubGroup);
            this.setFieldValue("ctlLotNo", obj.LotNo);

            $R_FN.showElement(this._btnUpdate, true);
            $R_FN.showElement(this._btnAdd, false);
        }
        this._intLotID = obj.LotNoId;
        this._status = obj.Status;
        this._intInvoiceLineNo = obj.InvoiceLineNo;
        $R_FN.showElement(this._btnRefresh, true);
    },
    validateForm: function () {
        this.onValidate();
        var blnOK = this.autoValidateFields();
        return blnOK;
    },

    refreshClicked: function () {       
        //this.setFieldValue("ctlSubGroup", 0, null, "");
        this.setFieldValue("ctlLotNo", "");
        $R_FN.showElement(this._btnUpdate, false);
        $R_FN.showElement(this._btnAdd, true);
        this._strErrorMessage = '';
        this.showError(false, this._strErrorMessage);
        $R_FN.showElement(this._lblDuplicateError, false);
        $R_FN.showElement(this._btnRefresh, false);
        //Comment: 12 Nov 2018: Need to referesh Grid on click of button
        //this.LoadSerailNoGrid();

    },
    refreshGrid: function () {
        this._ctlGiTempLotNo._intGoodsInNo = this._intGIID;
        this._ctlGiTempLotNo._intGoodsInLineNo = this._intLineID;
        
        this._ctlGiTempLotNo.getData();
        //this.LoadSerailNoGrid();
    },
    ctlGiLotNumber_PotentialStatusChange: function () {
        this._countLotNo = this._ctlGiTempLotNo._objResult.Count;
        // alert(this._ctlGiTempLotNo._objResult.Count);
        this._blnSerNoRecorded = this._countLotNo >= this._Quantity;
        //this._lotDetail = res.LotNoDetails.length;
        $R_FN.setInnerHTML(this._lblLotCount, 'Lot No. Count: ' + this._countLotNo + ((this._blnSerNoRecorded) ? ' All required lot numbers are recorded' : ''));
        $R_FN.showElement(this._lbllotCount, this._countLotNo > 0);
        this._lotDetail = this._countLotNo;
        this.showField("ctlLotNoDetail", this._countLotNo > 0);
       
        if (this._btnAdd) $R_IBTN.showButton(this._btnAdd, !this._blnSerNoRecorded);
    }


};

Rebound.GlobalTrader.Site.Controls.Forms.GILines_LotNo.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.GILines_LotNo", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
