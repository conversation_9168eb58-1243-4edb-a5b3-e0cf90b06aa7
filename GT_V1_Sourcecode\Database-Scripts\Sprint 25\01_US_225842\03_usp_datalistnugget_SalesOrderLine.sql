﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO
/*
--===================================================================================================================================
TASK			UPDATED BY       DATE				ACTION		DESCRIPTION  
[US-225842]		An.TranTan		 02-Apr-2025		UPDATE		Get number of TODO task related to SO
===================================================================================================================================== 
*/
CREATE OR ALTER PROCEDURE [dbo].[usp_datalistnugget_SalesOrderLine]                                
--********************************************************************************************                                                                                                             
--* Marker     Changed By      Date          Remarks                                
--* [001]      Vikas kumar     22/11/2011    ESMS Ref:21 - Add Country search option in SO             
--* [002]      Ravi            19-09-2023    RP-2338  AS6081 Search/Filter functionality on different pages             
--* [003]      Abhinav Saxena  09-11-2023  RP-1564.      
--********************************************************************************************                                
    @ClientId int                                
  , @TeamId int = NULL                                
  , @DivisionId int = NULL                                
  , @LoginId int = NULL                                
  , @OrderBy int = 1                                
  , @SortDir int = 1                                
  , @PageIndex int = 0                                
  , @PageSize int = 10                                
  , @PartSearch nvarchar(50) = NULL                                
  , @ContactSearch nvarchar(50) = NULL                               
  --[001]Code Start                              
  , @CountrySearch int = NULL                              
  --[001]Code End                              
  , @CMSearch nvarchar(50) = NULL                                
  , @SalesmanNo int = NULL                                
  , @CustomerPOSearch nvarchar(50) = NULL                                
  , @RecentOnly bit = 1                                
  , @IncludeClosed bit = 0                                
  , @SalesOrderNoLo int = NULL                                
  , @SalesOrderNoHi int = NULL                                
  , @DateOrderedFrom datetime = NULL                                
  , @DateOrderedTo datetime = NULL                                
  , @DatePromisedFrom datetime = NULL                          
  , @DatePromisedTo datetime = NULL                                
  , @UnauthorisedOnly bit = 0                         
  --, @IncludeSentOrder bit = 0                              
   , @IncludeSentOrder int = NULL                         
   , @ContractNo nvarchar(100) = NULL                    
   , @IsGlobalLogin BIT = 0                       
   , @ClientSearch int = NULL                      
   , @SOCheckedStatus int =null                  
   , @SOStatus int=null            
   , @AS6081 BIT = NULL --[002]        
   , @SelectedLoginId INT=NULL          
    WITH RECOMPILE                                
AS                                 
    DECLARE @RecentDate datetime                                
      , @StartPage int                                
      , @EndPage int                                
    SET @RecentDate = dbo.ufn_get_date_from_datetime(dateadd(mm, -3, getdate()))                                
    SET @StartPage = (@PageIndex * @PageSize + 1)                                
    SET @EndPage = ((@PageIndex + 1) * @PageSize)                                
                                
    IF (NOT @DateOrderedFrom IS NULL)                                 
        SET @DateOrderedFrom = dbo.ufn_get_start_of_day_for_date(@DateOrderedFrom)                                
                                
    IF (NOT @DateOrderedTo IS NULL)                                 
        SET @DateOrderedTo = dbo.ufn_get_end_of_day_for_date(@DateOrderedTo)                                
                                
    IF (NOT @DatePromisedFrom IS NULL)                                
        SET @DatePromisedFrom = dbo.ufn_get_start_of_day_for_date(@DatePromisedFrom)                                
                                
    IF (NOT @DatePromisedTo IS NULL)                                 
        SET @DatePromisedTo = dbo.ufn_get_end_of_day_for_date(@DatePromisedTo)                         
                                
 IF (NOT @LoginId IS NULL)                                 
        SET @SalesmanNo = NULL                                
                                
-- semi-colon needed for WITH                                
;                                
    WITH    cteSearch                                
              AS (SELECT    sol.SalesOrderLineId                                
                          , so.SalesOrderId AS SalesOrderNo                                
                          , so.SalesOrderNumber                                
                          , sol.Part                                
                          , sol.ManufacturerNo                                
                          , mf.ManufacturerCode                                
                          , sol.Quantity                                
        , IsNull((SELECT Sum(IsNull(ila.Quantity,0))                            
          FROM  dbo.tbInvoicelineAllocation ila                                
          WHERE sol.SalesOrderLineId = ila.SalesOrderLineNo), 0) AS QuantityShipped                                
                          , so.CompanyNo                                
                          , co.CompanyName                                
                          , so.ContactNo                                
                          , cn.ContactName                                
                          , so.DateOrdered                                
                          , sol.RequiredDate                                
                          , sol.DatePromised                                
                          , so.CustomerPO                           
						  ,sol.ContractNo                          
						  ,(case when ((DATEDIFF(DAY, GETDATE(),sol.DatePromised ))=1 AND ISNULL(sol.closed,0)=0 AND sol.Quantity>0 AND dbo.ufn_get_salesOrder_statusNo(so.SalesOrderId) != 10) 
								 then 'Amber' 
								 when ((DATEDIFF(DAY, GETDATE(),sol.DatePromised ))<=0 AND ISNULL(sol.closed,0)=0 AND sol.Quantity>0 AND dbo.ufn_get_salesOrder_statusNo(so.SalesOrderId) != 10) 
								 Then 'Red'
								 when ((DATEDIFF(DAY, GETDATE(),sol.DatePromised ))>1 AND ISNULL(sol.closed,0)=0 AND sol.Quantity>0 AND dbo.ufn_get_salesOrder_statusNo(so.SalesOrderId) != 10) 
								 then 'Green'
								 else 'White' end )as DatePromisedStatus                
                       -- , ISNULL(stk.QuantityInStock,0) AS QuantityInStock                            
                          , (Select sum(isnull(QuantityInStock,0))  from tbStock stk                          
                             JOIN dbo.tbAllocation  al on al.StockNo = stk.StockId                           
                             WHERE sol.SalesOrderLineId = al.SalesOrderLineNo ) AS QuantityInStock                          
						  , ROW_NUMBER() OVER (ORDER BY --                                
                                    case WHEN @OrderBy = 1                                
                                                AND @SortDir = 2 THEN SalesOrderNumber                             
                                    END DESC                                 
                                     , case WHEN @OrderBy = 1 THEN SalesOrderNumber                                
                                       END                           
                                     , case WHEN @OrderBy = 2                                
                                                 AND @SortDir = 2 THEN sol.FullPart                                
                                       END DESC                                
                                     , case WHEN @OrderBy = 2 THEN sol.FullPart                                
  END                        
                                     , case WHEN @OrderBy = 3                                
                                                 AND @SortDir = 2 THEN sol.Quantity                                
                      END DESC                                
                                     , case WHEN @OrderBy = 3 THEN sol.Quantity                                
                                       END      
                                     , case WHEN @OrderBy = 4                                
                                                 AND @SortDir = 2 THEN CompanyName                                
                                       END DESC              
           , case WHEN @OrderBy = 4 THEN CompanyName                                
                                       END                                
                                     , case WHEN @OrderBy = 5                                
                                                 AND @SortDir = 2 THEN DateOrdered                                
                                       END DESC                      
                                     , case WHEN @OrderBy = 5 THEN DateOrdered                                
                                       END                             
                                      , case WHEN @OrderBy = 6                                
                                                 AND @SortDir = 2 THEN DatePromised                                
                                       END DESC                                
                                     , case WHEN @OrderBy = 6 THEN DatePromised                                
                                       END                           
                                     --, case WHEN @OrderBy = 6                                
                                     --            AND @SortDir = 2 THEN RequiredDate                            
                                     --  END DESC                                
                                     --, case WHEN @OrderBy = 6 THEN RequiredDate                                
                                     --  END                        
            , case WHEN @OrderBy = 7                                
                                                 AND @SortDir = 2 THEN ContractNo                                
   END DESC                                
                                    , case WHEN @OrderBy = 7 THEN ContractNo                                
                                       END) AS RowNum              
            , ISNULL(so.AS6081,0) AS AS6081 --[004]            
                  FROM      dbo.tbSalesOrder so                                
                  LEFT JOIN dbo.tbSalesOrderLine sol ON sol.SalesOrderNo = so.SalesOrderId                                
                  LEFT JOIN dbo.tbCompany co ON so.CompanyNo = co.CompanyId                                
                  LEFT JOIN dbo.tbContact cn ON so.ContactNo = cn.ContactId                                
                  LEFT JOIN dbo.tbLogin lg ON lg.LoginId = so.Salesman                                
     LEFT JOIN dbo.tbManufacturer mf ON sol.ManufacturerNo = mf.ManufacturerId                              
                  --[001]Code Start                              
                  JOIN dbo.tbAddress ad ON so.ShipToAddressNo = ad.AddressID                              
                  --[001]Code End                              
                  LEFT OUTER JOIN tbStock stk On stk.StockId=sol.StockNo                            
                                              
                  WHERE                       
       (( @IsGlobalLogin=1 ) OR (@IsGlobalLogin=0 AND so.ClientNo = CASE WHEN @ClientSearch IS NULL THEN @ClientId ELSE @ClientSearch END ))                       
          --so.ClientNo = @ClientId                                
                            AND ((@TeamId IS NULL)                 
      OR (NOT @TeamId IS NULL                                
                                     AND lg.TeamNo = @TeamId))                                
                            AND ((@DivisionId IS NULL)                                
                                 OR (NOT @DivisionId IS NULL                                
                            AND lg.DivisionNo = @DivisionId))                                
                            AND ((@LoginId IS NULL)                                
OR (NOT @LoginId IS NULL                       
                                   AND (so.Salesman = @LoginId                                
                                          OR (so.Salesman2 = @LoginId                                
                                              AND so.Salesman2Percent > 0))))                        
                            AND ((@RecentOnly = 0)                                
                                 OR (@RecentOnly = 1                                
       AND so.DateOrdered >= @RecentDate))                                
                            AND ((@SalesOrderNoLo IS NULL)                                
                                 OR (NOT @SalesOrderNoLo IS NULL                                
                                     AND SalesOrderNumber >= @SalesOrderNoLo))                                
                            AND ((@SalesOrderNoHi IS NULL)                           
                                 OR (NOT @SalesOrderNoHi IS NULL                                
                                     AND SalesOrderNumber <= @SalesOrderNoHi))                                
                            AND ((@DateOrderedFrom IS NULL)                                
                                 OR (NOT @DateOrderedFrom IS NULL                                
                                     AND DateOrdered >= @DateOrderedFrom))                                
                            AND ((@DateOrderedTo IS NULL)                                
                                 OR (NOT @DateOrderedTo IS NULL                                
                                     AND DateOrdered <= @DateOrderedTo))                                
                            AND ((@DatePromisedFrom IS NULL)                                
                                 OR (NOT @DatePromisedFrom IS NULL                                
                                     AND DatePromised >= @DatePromisedFrom))                                
                            AND ((@DatePromisedTo IS NULL)                                
                                 OR (NOT @DatePromisedTo IS NULL                                
                                     AND DatePromised <= @DatePromisedTo))                                
                            AND ((@ContactSearch IS NULL)                                
                                 OR (NOT @ContactSearch IS NULL                                
                                     AND ContactName LIKE @ContactSearch))        
                                      
                             --[001]Code Start                                      
   AND ((@CountrySearch IS NULL)                                
                                 OR (NOT @CountrySearch IS NULL                          
                                     AND ad.CountryNo = @CountrySearch))                                
                       --[001]Code End                                      
                            AND ((@CMSearch IS NULL)                                
            OR (NOT @CMSearch IS NULL                           
                                     AND co.FullName LIKE @CMSearch))                                
                            AND ((@SalesmanNo IS NULL)                                
                                 OR (NOT @SalesmanNo IS NULL                   
                                     AND (so.Salesman = @SalesmanNo                                
  OR (so.Salesman2 = @SalesmanNo                                
                   AND so.Salesman2Percent > 0))))                                
                            AND ((@CustomerPOSearch IS NULL)                                
                                 OR (NOT @CustomerPOSearch IS NULL                                
                                  AND CustomerPO LIKE @CustomerPOSearch))                                
                            AND ((@PartSearch IS NULL)                                
                                 OR (NOT @PartSearch IS NULL                           
                                     AND (sol.FullPart LIKE @PartSearch                                
                                  OR FullCustomerPart LIKE @PartSearch)))                                
                            AND so.Closed IN (0, @IncludeClosed)                                
       AND (sol.Closed IS NULL                        
  OR sol.Closed IN (0, @IncludeClosed))                             
                            --AND ((@UnauthorisedOnly = 0)                      
     --     OR (@UnauthorisedOnly = 1                                
                            --         AND so.DateAuthorised IS NULL))                                
                            AND (sol.Inactive IS NULL                                
                                 OR sol.Inactive = 0)                          
                        
        --[002]Code Start                                      
                       AND ((@IncludeSentOrder  IS NULL)                        
        OR (@IncludeSentOrder = 2 AND   so.SentOrdertoCust  IS NULL)                                
                                 OR ( @IncludeSentOrder = 1 AND   so.SentOrdertoCust  IS NOT NULL)                        
         OR (@IncludeSentOrder = -1 OR @IncludeSentOrder = 0))                                   
          --[002]Code End                            
                                     
                               
         AND ((@ContractNo IS NULL)                                
        OR (NOT @ContractNo IS NULL                                
                                     AND sol.ContractNo LIKE @ContractNo))                      
         AND ((@ClientSearch IS NULL)                                                
                                 OR (NOT @ClientSearch IS NULL                                                
                                     AND so.ClientNo = @ClientSearch))       
  AND ((@ClientSearch IS NULL) OR(NOT @ClientSearch IS NULL AND @IsGlobalLogin=1)                          
                                 OR (NOT @ClientSearch IS NULL                           
                                     AND so.companyNo IN(SELECT CompanyNo FROM dbo.ufn_GSA_GetMyCompnayIds (@SelectedLoginID,@ClientId))))                           
                            
  --[003]Code Start                                      
        AND ((@SOCheckedStatus  IS NULL)                        
        OR (@SOCheckedStatus = 1 AND   so.DateAuthorised  IS not NULL)                                
        OR (@SOCheckedStatus = 2 AND   so.DateAuthorised  IS  NULL)                        
        OR (@SOCheckedStatus = -1 OR @SOCheckedStatus = 3))                   
                                
        AND ((@SOStatus IS NULL)                                                
        OR (NOT @SOStatus IS NULL AND dbo.ufn_get_salesOrder_statusNo(SalesOrderNo) =@SOStatus)                  
  OR (@SOStatus = -1 OR @SOStatus =0))                                   
        --[003]Code End                  
 --[004] start            
--AND ((@AS6081 IS NULL) OR (NOT @AS6081 IS NULL AND so.AS6081 = @AS6081))              
 AND (            
    (@AS6081 = 1 and so.AS6081 = 1)            
    OR            
    (@AS6081 IS NULL and (so.AS6081 IS NULL OR so.AS6081 = 1 or so.AS6081 = 0))            
    OR            
    (@AS6081 = 0 and (so.AS6081 IS NULL or so.AS6081 = 0))            
   )         
--[004] end                      
                
                                    
               )                                
        SELECT  *                                
              , (SELECT count(*) FROM   cteSearch) AS RowCnt                                
              , dbo.ufn_get_salesOrder_statusNo(SalesOrderNo) AS [Status]
			  , (SELECT COUNT(*) FROM tbToDo td WITH(NOLOCK) WHERE td.SalesOrderNo = cteSearch.SalesOrderNo) AS TaskCount  
			  , CASE WHEN EXISTS(SELECT TOP 1 1 FROM tbToDo td WITH(NOLOCK) 
									WHERE td.SalesOrderNo = cteSearch.SalesOrderNo AND td.IsComplete = 0) 
					THEN 1 ELSE 0 END AS HasUnFinishedTask  
        FROM    cteSearch                                
        WHERE   RowNum BETWEEN @StartPage AND @EndPage                                
        ORDER BY RowNum      
GO


