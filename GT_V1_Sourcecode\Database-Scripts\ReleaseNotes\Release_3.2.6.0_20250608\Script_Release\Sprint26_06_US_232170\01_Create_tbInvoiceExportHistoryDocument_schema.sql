﻿IF NOT EXISTS (
    SELECT * FROM sys.tables WHERE name = 'tbInvoiceExportHistoryDocument' AND schema_id = SCHEMA_ID('dbo')
)
BEGIN
    CREATE TABLE dbo.tbInvoiceExportHistoryDocument (
        InvoiceDocumentId INT IDENTITY(1,1) PRIMARY KEY,
        InvoiceNo INT NOT NULL,
		Caption NVARCHAR(500) NULL,
		FileName NVARCHAR(500) NULL,
		FileType NVARCHAR(10) NULL,
		PrefixAction NVARCHAR(50) NULL,
		ActionType NVARCHAR(100) NULL,
		UpdatedBy INT NULL,
        DLUP DATETIME DEFAULT GETDATE(),
		REFIDHK INT NULL,
		NewRecord INT NULL
    );
END