//-----------------------------------------------------------------------------------------
// RP 12.10.2009:
// - newly created from retrofitting changes from v3.0.34 (task 322)
//-----------------------------------------------------------------------------------------
using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;

namespace Rebound.GlobalTrader.Site.Controls.DropDowns.Data {
	[WebService(Namespace = "http://tempuri.org/")]
	[WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
	public class GoodsInUpdate : Rebound.GlobalTrader.Site.Data.DropDowns.Base {

		public override void ProcessRequest(HttpContext context) {
			SetDropDownType("GoodsInUpdate");
			base.ProcessRequest(context);
		}

		protected override void GetData() {
			JsonObject jsn = new JsonObject();
			JsonObject jsnList = new JsonObject(true);
            jsnList.AddVariable(GetNewItem(Controls.DropDowns.GoodsInUpdate.GoodsInUpdateList.ReceiptOnly));
            jsnList.AddVariable(GetNewItem(Controls.DropDowns.GoodsInUpdate.GoodsInUpdateList.ReceiptAndLinkedStock));
            jsnList.AddVariable(GetNewItem(Controls.DropDowns.GoodsInUpdate.GoodsInUpdateList.ReceiptLinkedStockAndShipments));
			jsn.AddVariable("Types", jsnList);
			jsnList.Dispose(); jsnList = null;
			OutputResult(jsn);
			jsn.Dispose(); jsn = null;
		}

		private JsonObject GetNewItem(Controls.DropDowns.GoodsInUpdate.GoodsInUpdateList enmGoodsInUpdateList) {
			JsonObject jsnItem = new JsonObject();
			jsnItem.AddVariable("ID", (int)enmGoodsInUpdateList);
			jsnItem.AddVariable("Name", Functions.GetGlobalResource("Misc", enmGoodsInUpdateList));
			return jsnItem;
		}

	}


}
