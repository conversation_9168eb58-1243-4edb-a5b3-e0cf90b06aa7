///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything abhinav
//
// RP 06.10.2009:
// - Changes due to changes in base class

//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");

Rebound.GlobalTrader.Site.Controls.DropDowns.LineManagerContact = function(element) { 
    Rebound.GlobalTrader.Site.Controls.DropDowns.LineManagerContact.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.DropDowns.LineManagerContact.prototype = {
    get_intBuyerID: function () { return this._intBuyerID; }, set_intBuyerID: function (value) { if (this._intBuyerID !== value) this._intBuyerID = value; }, 
	initialize: function() {
        Rebound.GlobalTrader.Site.Controls.DropDowns.LineManagerContact.callBaseMethod(this, "initialize");
		this.addSetupDataCall(Function.createDelegate(this, this.setupDataCall));
		this.addGetDataOK(Function.createDelegate(this, this.dataCallOK));
	},
	
	dispose: function() { 
		if (this.isDisposed) return;
        Rebound.GlobalTrader.Site.Controls.DropDowns.LineManagerContact.callBaseMethod(this, "dispose");
        this._intBuyerID = null;
	},
	
	setupDataCall: function() { 		
        this._objData.set_PathToData("controls/DropDowns/LineManagerContact");
        this._objData.set_DataObject("LineManagerContact");
        this._objData.set_DataAction("GetData");
        this._objData.addParameter("id", this._intBuyerID);
        this._objData.addParameter("ClientNo", this._intClientID);
        this._objData.addParameter("UpdateManager", this._UpdateManager);
	},
	
	dataCallOK: function() { 
		var result = this._objData._result;
		if (result.Types) {
			for (var i = 0; i < result.Types.length; i++) {
				this.addOption(result.Types[i].Name, result.Types[i].ID);
			}
		}
	}
	
};

Rebound.GlobalTrader.Site.Controls.DropDowns.LineManagerContact.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.LineManagerContact", Rebound.GlobalTrader.Site.Controls.DropDowns.Base);
