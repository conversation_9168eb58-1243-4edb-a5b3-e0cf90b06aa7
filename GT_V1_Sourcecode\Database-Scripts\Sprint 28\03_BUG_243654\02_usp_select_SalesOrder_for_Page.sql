﻿
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO
  
CREATE OR ALTER PROCEDURE [dbo].[usp_select_SalesOrder_for_Page]            
--************************************************************************************************            
--* Gets just the information needed for the Sales Order Detail page            
--*            
--* RP 14.12.2009:            
--* - new proc            
--* Marker     Changed by      Date         Remarks            
--* [001]      Vinay           07/05/2012   This need to upload pdf document.            
--************************************************************************************************        
/*     
===========================================================================================    
TASK        	UPDATED BY       DATE          ACTION    DESCRIPTION    
[BUG-243654]	NgaiTo		 	19-May-2025		Update	243654: [PROD Bug] SO - Customer Template is not working
===========================================================================================    
*/
    @SalesOrderId int            
AS
	DECLARE @FirstQuoteNo INT = 0;

	SELECT TOP 1 @FirstQuoteNo = tbqtl.QuoteNo 
	FROM tbSalesOrderLine tbsol
	JOIN tbQuoteLine tbqtl ON tbsol.QuoteLineNo = tbqtl.QuoteLineId
	JOIN tbSourcingResult c ON tbqtl.SourcingResultNo = c.SourcingResultId
	JOIN tbCustomerRequirement d ON c.CustomerRequirementNo = d.CustomerRequirementId
	JOIN tbBOMManager e ON d.BOMManagerNo = e.BOMManagerId
	WHERE tbsol.SalesOrderNo = @SalesOrderId
	ORDER BY e.DLUP DESC

    SELECT  so.SalesOrderId             
          , so.SalesOrderNumber               
          , so.ClientNo            
          --, co.CompanyName            
    , co.CompanyName + (CASE WHEN LTRIM(RTRIM(ISNULL(co.CustomerCode, ''))) <> '' THEN ' ( '+ co.CustomerCode + ' ) ' ELSE '' END) as CompanyName  
          , so.CompanyNo            
          , dbo.ufn_get_salesOrder_statusNo(so.SalesOrderId) AS StatusNo            
          , so.IsPDFAvailable           
          , lg.TeamNo          
          , lg.DivisionNo           
          , so.Salesman        
          , so.IsSORPDFAvailable     
       
          ,(SELECT COUNT(*) FROM dbo.tbSalesOrderLine sol                                  
           JOIN tbSourcingResult sr                    
            ON sol.SourcingResultNo = sr.SourcingResultId    
           WHERE (sr.POHubCompanyNo IS NOT NULL)   AND (sol.SalesOrderNo = @SalesOrderId)     AND (sr.SourcingTableItemNo IS NOT NULL)                
           AND (sr.SourcingTable='PQ' OR sr.SourcingTable='OFPH' OR sr.SourcingTable='EXPH')      
          ) AS IpoCount      
     ,case when so.IsConsolidated =0 then 'Actual lines' when so.IsConsolidated =1 then 'Consolidate lines' else 'None' end as ConsolidateStatus    
   ,c.ClientName
   ,so.IsExcelDocAvailable,
	iSNULL(@FirstQuoteNo, 0) AS FirstQuoteNo    
    FROM    tbSalesOrder so            
    JOIN    tbCompany co ON so.CompanyNo = co.CompanyId            
    LEFT JOIN tbLogin lg on lg.LoginId=so.Salesman     
 LEFT JOIN tbClient c on so.ClientNo=c.ClientId          
    WHERE   so.SalesOrderId = @SalesOrderId     

GO


