Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");Rebound.GlobalTrader.Site.Controls.DropDowns.CertificateCategory=function(n){Rebound.GlobalTrader.Site.Controls.DropDowns.CertificateCategory.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.DropDowns.CertificateCategory.prototype={initialize:function(){Rebound.GlobalTrader.Site.Controls.DropDowns.CertificateCategory.callBaseMethod(this,"initialize");this.addSetupDataCall(Function.createDelegate(this,this.setupDataCall));this.addGetDataOK(Function.createDelegate(this,this.dataCallOK))},dispose:function(){this.isDisposed||Rebound.GlobalTrader.Site.Controls.DropDowns.CertificateCategory.callBaseMethod(this,"dispose")},setupDataCall:function(){this._objData.set_PathToData("controls/DropDowns/CertificateCategory");this._objData.set_DataObject("CertificateCategory");this._objData.set_DataAction("GetData")},dataCallOK:function(){var t=this._objData._result,n;if(t.Categories)for(n=0;n<t.Categories.length;n++)this.addOption(t.Categories[n].Name,t.Categories[n].ID)}};Rebound.GlobalTrader.Site.Controls.DropDowns.CertificateCategory.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.CertificateCategory",Rebound.GlobalTrader.Site.Controls.DropDowns.Base);