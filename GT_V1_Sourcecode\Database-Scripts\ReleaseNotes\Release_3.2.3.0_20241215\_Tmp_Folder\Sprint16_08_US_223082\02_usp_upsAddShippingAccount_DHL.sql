﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

/* 
===========================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-210540]		Phuc Hoang			07-Aug-2024		UPDATE			[PROD Bug] DHL Interface - Label Print Issue
[US-223082]		An.TranTan			03-Dec-2024		UPDATE			Add VatNo and EORINumber for Importer in DHL
===========================================================================================
*/
CREATE OR ALTER PROCEDURE [dbo].[usp_upsAddShippingAccount_DHL]    
(          
  @UPSAccount varchar(100) ,        
  @CompanyName varchar(200),        
  @Attention varchar(200),        
  @Address1  nvarchar(500),        
  @Address2  nvarchar(500),        
  @Address3  nvarchar(500),        
  @Country  varchar(10),        
  @PostalCode nvarchar(100),        
  @City   nvarchar(100),        
  @State  nvarchar(100),        
  @Telephone nvarchar(30),        
  @Fax   nvarchar(200) =NULL,        
  @TaxIDNumber nvarchar(100)=NULL,        
  @TaxIDType nvarchar(50),        
  @Active  bit,  
  @LoadDefault bit ,
  @BillingCurrency varchar(30) = NULL  ,
  @CountryName varchar(100)=null,
  @Email nvarchar(128)=NULL,
  @ShipType nvarchar(100) = NULL,
  @VatNo nvarchar(200) = NULL,
  @EORINumber nvarchar(200) = NULL
)        
--Created By: Vinay Mishra        
--Created On: 27 sep 2012                    
--Purpose: To Add shipping account           
As        
BEGIN        
BEGIN TRY      
      
  INSERT INTO tbupsShippingAccount        
              (        
				UPSAccount,        
				CompanyName,        
				Attention,         
				Address1,         
				Address2,         
				Address3,         
				Country ,        
				PostalCode,        
				City,          
				[State] ,        
				Telephone ,        
				Fax ,         
				TaxIDNumber,        
				TaxIDType ,        
				Active,  
				LoadDefault ,
				BillingCurrency,
				ClientNo ,
				AccountType,
				CountryName,
				Email,
				ShipType,
				VatNo,
				EORINumber
              )        
    VALUES        
              (        
     @UPSAccount,        
     @CompanyName ,        
     @Attention,         
     @Address1 ,         
     @Address2 ,         
     @Address3 ,         
     @Country ,         
     @PostalCode ,        
     @City  ,         
     @State,          
     @Telephone,         
     @Fax ,          
     @TaxIDNumber ,        
     @TaxIDType,         
     @Active,  
     @LoadDefault ,
     @BillingCurrency,
	 0    ,
	 'DHL',
	 @CountryName,
	 @Email,
	 @ShipType,
	 @VatNo,
	 @EORINumber
              )        
  SELECT @@IDENTITY        
 END TRY      
 BEGIN CATCH      
  RAISERROR('Please enter unique dhl account number.',16,1)      
 END CATCH      
END 


GO


