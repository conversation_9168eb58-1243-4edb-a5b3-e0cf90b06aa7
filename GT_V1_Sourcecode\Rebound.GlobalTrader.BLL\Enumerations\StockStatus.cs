﻿using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;

namespace Rebound.GlobalTrader.BLL {
    public partial class StockStatus {
		/// <summary>
		/// An enum representation of the 'tbStockStatus' table.
		/// </summary>
		/// <remark>This enumeration contains the items contained in the table tbStockStatus</remark>
		[Serializable]
		public enum List {
			Available_PartAllocated = 3, 
			Available_Unallocated = 2, 
			FullyAllocated = 4, 
			NoneInStock = 1, 
			OnOrder = 5, 
			Quarantined = 6
		}		

	

		
	}
}