/* Marker     changed by      date         Remarks  
   [001]      <PERSON><PERSON><PERSON> kumar     22/11/2011  ESMS Ref:21 - Add Country search option in PO */
/* [0002]      <PERSON><PERSON><PERSON><PERSON>   14/07/2014  ESMS Ref:172 - Issue in Part Searching in whole GT */
/*[003]      <PERSON><PERSON><PERSON>     27-Sep-2018   REB-13083 Change request PO - delivery status*/

using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;
using Rebound.GlobalTrader.Site.Enumerations;

namespace Rebound.GlobalTrader.Site.Controls.DataListNuggets.Data {

	public class InternalPurchaseOrders : Base { 

		protected override void GetData() {
			JsonObject jsn = new JsonObject();

			//check view level
			ViewLevelList enmViewLevel = (ViewLevelList)GetFormValue_Int("ViewLevel");

			//get data	
            List<InternalPurchaseOrderLine> lst = InternalPurchaseOrderLine.DataListNugget(
                SessionManager.IsPOHub==true?null:SessionManager.ClientID
                 
				, (enmViewLevel == ViewLevelList.Team) ? (int?)SessionManager.LoginTeamID : null
				, (enmViewLevel == ViewLevelList.Division) ? (int?)SessionManager.LoginDivisionID : null
				, (enmViewLevel == ViewLevelList.My) ? (int?)LoginID : null
				, GetFormValue_NullableInt("PageIndex", 0)
				, GetFormValue_NullableInt("PageSize", 10)
				, GetFormValue_NullableInt("SortIndex")
				, GetFormValue_NullableInt("SortDir", SortColumnDirection.ASC)
                //[0001] start code
                //, GetFormValue_StringForPartSearch("Part")
                  , GetFormValue_PartForLikeSearch("Part")
                //[0001] end code
                //, GetFormValue_StringForNameSearch("Contact")
                 , GetFormValue_StringForNameSearchDecode("Contact")
                //, GetFormValue_StringForNameSearch("CMName")
                 , GetFormValue_StringForNameSearchDecode("CMName")
				, GetFormValue_NullableInt("BuyerName")
                //[001]Code Start
                , GetFormValue_NullableInt("Country")
                //[001]Code End
				, GetFormValue_Boolean("IncludeClosed")
				, GetFormValue_NullableInt("PONoLo")
				, GetFormValue_NullableInt("PONoHi")
				, GetFormValue_NullableDateTime("DateOrderedFrom")
				, GetFormValue_NullableDateTime("DateOrderedTo")
				, GetFormValue_NullableDateTime("ExpediteDateFrom")
				, GetFormValue_NullableDateTime("ExpediteDateTo")
				, GetFormValue_NullableDateTime("DeliveryDateFrom")
				, GetFormValue_NullableDateTime("DeliveryDateTo")
				, GetFormValue_Boolean("RecentOnly")
                , GetFormValue_NullableInt("Client")
			);

			//check counts
			jsn.AddVariable("TotalRecords", (lst.Count > 0) ? lst[0].RowCnt : 0);

			//format data
			JsonObject jsnRowsArray = new JsonObject(true);
			for (int i = 0; i < lst.Count; i++) {
				JsonObject jsnRow = new JsonObject();
				jsnRow.AddVariable("ID", lst[i].PurchaseOrderId);
				jsnRow.AddVariable("No", lst[i].PurchaseOrderNumber);
				jsnRow.AddVariable("Part", lst[i].Part);
				jsnRow.AddVariable("Price", Functions.FormatCurrency(lst[i].Price, lst[i].CurrencyCode));
				jsnRow.AddVariable("Quantity", Functions.FormatNumeric(lst[i].QuantityOrdered));
				jsnRow.AddVariable("QuantityOS", Functions.FormatNumeric(lst[i].QuantityOutstanding));
				jsnRow.AddVariable("DeliveryDate", Functions.FormatDate(lst[i].DeliveryDate));
				jsnRow.AddVariable("CM", lst[i].CompanyName);
				jsnRow.AddVariable("CMNo", lst[i].CompanyNo);
                //jsnRow.AddVariable("Contact", Functions.GetGlobalResource("Misc", "PurchaseHub"));
                //jsnRow.AddVariable("ContactNo", Functions.GetGlobalResource("Misc", "PurchaseHub"));
                jsnRow.AddVariable("Contact", lst[i].ContactName);
                jsnRow.AddVariable("ContactNo", lst[i].ContactNo);
				jsnRow.AddVariable("ROHS", lst[i].ROHS);
				jsnRow.AddVariable("Mfr", lst[i].ManufacturerCode);
				jsnRow.AddVariable("MfrNo", lst[i].ManufacturerNo);
				jsnRow.AddVariable("Status", Functions.GetGlobalResource("Status", (BLL.PurchaseOrderStatus.List)lst[i].Status));
                jsnRow.AddVariable("IPOStatus", Functions.GetGlobalResource("Status", (BLL.PurchaseOrderStatus.IPOList)lst[i].IPOStatus));
                jsnRow.AddVariable("ClientName", lst[i].ClientName);
                jsnRow.AddVariable("isPoHUB", SessionManager.IsPOHub);
                //[003] start
                jsnRow.AddVariable("DeliveryStatus", lst[i].DeliveryStatus);
                jsnRow.AddVariable("RowCSS", lst[i].RowCSS);
                //[003] end
                jsnRow.AddVariable("RequireASAP", lst[i].RequireASAP);
				jsnRowsArray.AddVariable(jsnRow);
				jsnRow.Dispose();
				jsnRow = null;
			}
			jsn.AddVariable("Results", jsnRowsArray);
			OutputResult(jsn);
			jsnRowsArray.Dispose();
			jsnRowsArray = null;
			jsn.Dispose();
			jsn = null;
			base.GetData();
		}

		protected override void AddFilterStates() {
            //Prevent filter state for Tab
			//AddExplicitFilterState("ViewLevel", GetFormValue_Int("ViewLevel"));
			AddFilterState("Part");
			AddFilterState("Contact");
			AddFilterState("CMName");
			AddFilterState("BuyerName");
			AddFilterState("IncludeClosed");
			AddFilterState("PONo");
			AddFilterState("DateOrderedFrom");
			AddFilterState("DateOrderedTo");
			AddFilterState("ExpediteDateFrom");
			AddFilterState("ExpediteDateTo");
			AddFilterState("DeliveryDateFrom");
			AddFilterState("DeliveryDateTo");
			AddFilterState("RecentOnly");
			base.AddFilterStates();
		}
	}
}
