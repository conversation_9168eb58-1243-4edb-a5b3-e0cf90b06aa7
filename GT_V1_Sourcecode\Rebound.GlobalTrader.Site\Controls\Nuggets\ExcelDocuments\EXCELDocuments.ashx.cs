﻿using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;
using System.IO;
using System.Drawing;
using System.Drawing.Imaging;
using System.Configuration;
//[001] add namespace for Azure Blob storage
using Microsoft.Azure;
using Microsoft.Azure.Storage;
using Microsoft.Azure.Storage.Blob;
using Microsoft.Azure.KeyVault;
using Microsoft.Azure.Storage.Shared;
using Microsoft.Azure.Storage.Auth;
using Microsoft.Azure.Storage.Auth.Protocol;
using Microsoft.Azure.Storage.RetryPolicies;
using Microsoft.Azure.KeyVault.Core;
using System.Net;
using System.Text;
//[001]
namespace Rebound.GlobalTrader.Site.Controls.Nuggets.Data
{
    /// <summary>
    /// Summary description for $codebehindclassname$
    /// </summary>
    [WebService(Namespace = "http://tempuri.org/")]
    [WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    public class EXCELDocuments : Rebound.GlobalTrader.Site.Data.Base
    {

        public string Section { get; private set; }
        public string Excel { get; private set; }
        public override void ProcessRequest(HttpContext context)
        {

            if (base.init(context))
            {
                switch (Action)
                {
                    case "GetData": GetData(context); break;
                    case "AddNew": AddNew(); break;
                    case "Delete": Delete(context); break;
                    case "MaxPDFDoc": MaxPDFDoc(); break;
                    default: WriteErrorActionNotFound(); break;
                }
            }
        }

        /// <summary>
        /// Set maximum excel document upload value
        /// </summary>
        private void MaxPDFDoc()
        {
            try
            {
                JsonObject jsn = new JsonObject();
                jsn.AddVariable("MaxPDFDocument", SettingsManager.GetSetting_Int(SettingItem.List.MaxPDFDocuments));
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }

        /// <summary>
        /// Get the excel document for related section
        /// </summary>
        /// <param name="context"></param>
        //private void GetData(HttpContext context)
        //{
        //    try
        //    {

              
        //        this.Section = GetFormValue_String("section").Split('_')[0];
        //        string ExcelPath = string.Format(Convert.ToString(System.Configuration.ConfigurationManager.AppSettings["ExcelDocumentPhysicalURL"]) + "{0}\\", this.Section);

        //        List<PDFDocument> lstExcelDocument = null;

        //        switch (this.Section.ToUpper())
        //        {
                   
        //            case "MFGEXCEL":
        //                lstExcelDocument = BLL.Manufacturer.GetExcelListForManufacturer(ID);
        //                break;
        //            //[003] code end
        //            default:
        //                WriteError(Functions.GetGlobalResource("NotFound", "PDFDocument"));
        //                break;
        //        }

        //        JsonObject jsn = new JsonObject();
        //        JsonObject jsnItems = new JsonObject(true);
        //        if (lstExcelDocument != null)
        //        {
        //            foreach (BLL.PDFDocument pdfDoc in lstExcelDocument)
        //            {
        //                JsonObject jsnItem = new JsonObject();
        //                jsnItem.AddVariable("ID", pdfDoc.PDFDocumentId);
        //                jsnItem.AddVariable("Date", Functions.FormatDate(pdfDoc.DLUP, false, true));
        //                jsnItem.AddVariable("By", pdfDoc.UpdatedByName);
        //                jsnItem.AddVariable("Caption", Functions.ReplaceLineBreaks(pdfDoc.Caption));
        //                jsnItem.AddVariable("FilePath", ExcelPath + pdfDoc.FileName);
        //                jsnItem.AddVariable("FileName", pdfDoc.FileName);
        //                jsnItems.AddVariable(jsnItem);
        //                jsnItem.Dispose();
        //                jsnItem = null;
        //            }
        //        }

        //        jsn.AddVariable("Items", jsnItems);
        //        jsn.AddVariable("IconPath", "app_themes/original/images/IconButton/ExcelIcon.jpg");
        //        OutputResult(jsn);
        //        jsn.Dispose(); jsn = null;

        //    }
        //    catch (Exception e)
        //    {
        //        WriteError(e);
        //    }
        //}
        private void GetData(HttpContext context)
        {
            try
            {


                this.Section = GetFormValue_String("section").Split('_')[0];
                string ExcelPath = string.Format(Convert.ToString(System.Configuration.ConfigurationManager.AppSettings["ExcelDocumentPhysicalURLForAzureBlob"]) + "{0}/", this.Section.ToUpper());

                List<PDFDocument> lstExcelDocument = null;

                switch (this.Section.ToUpper())
                {

                    case "MFGEXCEL":
                        lstExcelDocument = BLL.Manufacturer.GetExcelListForManufacturer(ID);
                        break;
                    //[003] code end
                    default:
                        WriteError(Functions.GetGlobalResource("NotFound", "PDFDocument"));
                        break;
                }

                JsonObject jsn = new JsonObject();
                JsonObject jsnItems = new JsonObject(true);
                if (lstExcelDocument != null)
                {
                    foreach (BLL.PDFDocument pdfDoc in lstExcelDocument)
                    {
                        JsonObject jsnItem = new JsonObject();
                        jsnItem.AddVariable("ID", pdfDoc.PDFDocumentId);
                        jsnItem.AddVariable("Date", Functions.FormatDate(pdfDoc.DLUP, false, true));
                        jsnItem.AddVariable("By", pdfDoc.UpdatedByName);
                        jsnItem.AddVariable("Caption", Functions.ReplaceLineBreaks(pdfDoc.Caption));
                        jsnItem.AddVariable("FilePath", ExcelPath + pdfDoc.FileName);
                        jsnItem.AddVariable("FileName", pdfDoc.FileName);
                        jsnItems.AddVariable(jsnItem);
                        jsnItem.Dispose();
                        jsnItem = null;
                    }
                }

                jsn.AddVariable("Items", jsnItems);
                jsn.AddVariable("IconPath", "app_themes/original/images/IconButton/ExcelIcon.jpg");
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;

            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }

        /// <summary>
        /// Add new excel document
        /// </summary>
        private void AddNew()
        {
            int intDocumentID = -1;
            string caption = GetFormValue_String("Caption");
            string tempFile = GetFormValue_String("TempFile");

            string extension = Path.GetExtension(tempFile);
           
            string filenameNoExtension = Path.GetFileNameWithoutExtension(tempFile);
            string file = filenameNoExtension + extension;

            bool result = true;

            try
            {
                this.Section = GetFormValue_String("Section").Split('_')[0];
              
                switch (this.Section.ToUpper())
                {
                    
                    case "MFGEXCEL":
                        intDocumentID = BLL.Manufacturer.InsertExcel(ID, caption, file, LoginID);
                        break;
                  
                    default:
                        result = false;
                        break;
                }

                JsonObject jsn = new JsonObject();
                jsn.AddVariable("Result", result);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;

            }
            catch (Exception e)
            {
                WriteError(e);
            }

        }

        /// <summary>
        /// Delete excel document
        /// </summary>
        /// <param name="context"></param>
        private void Delete(HttpContext context)
        {
            try
            {
                string accountname = ConfigurationManager.AppSettings.Get("StorageName");
                string accesskey = ConfigurationManager.AppSettings.Get("StorageKey1");
                StorageCredentials creden = new StorageCredentials(accountname, accesskey);
                CloudStorageAccount acc = new CloudStorageAccount(creden, useHttps: true);
                CloudBlobClient client = acc.CreateCloudBlobClient();
                CloudBlobContainer cont = client.GetContainerReference("gtexcelfile");
                if (!string.IsNullOrEmpty(GetFormValue_String("pdffilename")))
                {
                    bool result = false;
                    this.Section = GetFormValue_String("Section").Split('_')[0];
                    switch (this.Section.ToUpper())
                    {
                       
                        case "MFGEXCEL":
                            result = BLL.Manufacturer.DeleteManufacturerExcel(ID);
                            //[003] for delete pdf on azure blob storage
                            if (cont.Exists())
                            {
                                CloudBlobDirectory directory = cont.GetDirectoryReference(this.Section.ToUpper());
                                CloudBlockBlob cblob = directory.GetBlockBlobReference(GetFormValue_String("pdffilename"));
                                if (cblob.Exists())
                                {
                                    cblob.DeleteIfExists();
                                }
                            }
                            //[003]
                            break;
                      
                        default:
                            result = false;
                            break;
                    }
                    //File.Delete(FileUploadManager.GetPDFUploadFilePath(this.Section) + GetFormValue_String("pdffilename"));
                    JsonObject jsn = new JsonObject();
                    jsn.AddVariable("Result", result);
                    OutputResult(jsn);
                    jsn.Dispose(); jsn = null;

                }
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }

       
    }
}
