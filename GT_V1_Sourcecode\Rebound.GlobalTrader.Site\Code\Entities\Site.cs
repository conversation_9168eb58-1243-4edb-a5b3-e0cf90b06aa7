/*
  [001]        Arpit           21-June-2022 Dictionary to ConcurrentDictionary 
  [002]      Soorya          03/03/2023      RP-1048 Remove AI code
  [003]      Soorya          28/04/2023      RP-1017 NullReferenceException 
 [004]      beer singh         02/06/2023     RP-1027 IndexOutOfRangeException 
*/
using System;
using System.Collections.Generic;
using System.Text;
using System.Web.Caching;
using System.Configuration;
using System.Web.Configuration;
using Rebound.GlobalTrader.Site.Settings;
using System.Collections;
using Rebound.GlobalTrader.Site;
using System.Web.UI.WebControls;
using System.Linq;
using System.Collections.Concurrent;
//using Microsoft.ApplicationInsights; [002]

namespace Rebound.GlobalTrader.Site
{
    public class Site
    {

        private ReboundUIConfigSection _cfg;

        #region Properties

        private int _intApplicationID;
        public int ApplicationID
        {
            get { return _intApplicationID; }
            set { _intApplicationID = value; }
        }

        private string _strApplicationName;
        public string ApplicationName
        {
            get { return _strApplicationName; }
            set { _strApplicationName = value; }
        }
        private string _strApplicationNameShort;
        public string ApplicationNameShort
        {
            get { return _strApplicationNameShort; }
            set { _strApplicationNameShort = value; }
        }

        private string _strTemplatePath;
        public string TemplatePath
        {
            get { return _strTemplatePath; }
            set { _strTemplatePath = value; }
        }

        //[001]
        //protected Dictionary<int, SitePage> _dctPages;
        protected ConcurrentDictionary<int, SitePage> _dctPages;
        protected List<SitePage> _lstPages;
        public List<SitePage> Pages
        {
            get
            {
                if (_lstPages == null) this.ProcessPageList();
                return _lstPages;
            }
        }

        //[001]
        //protected Dictionary<int, SiteSection> _dctSections;
        protected ConcurrentDictionary<int, SiteSection> _dctSections;
        protected List<SiteSection> _lstSections;
        public List<SiteSection> Sections
        {
            get
            {
                if (_lstSections == null) this.ProcessSectionList();
                return _lstSections;
            }
        }

        //[001]
        //protected Dictionary<string, ColumnWidth> _dctColumnWidth;
        protected ConcurrentDictionary<string, ColumnWidth> _dctColumnWidth;
        protected List<ColumnWidth> _lstColumnWidth;
        public List<ColumnWidth> ColumnWidths
        {
            get
            {
                if (_lstColumnWidth == null) this.ProcessColumnWidthList();
                return _lstColumnWidth;
            }
        }

        //[001]
        //protected Dictionary<int, DropDown> _dctDropDowns;
        protected ConcurrentDictionary<int, DropDown> _dctDropDowns;
        protected List<DropDown> _lstDropDowns;
        public List<DropDown> DropDowns
        {
            get
            {
                if (_lstDropDowns == null) this.ProcessDropDownList();
                return _lstDropDowns;
            }
        }

        //[001]
        //protected Dictionary<int, AutoSearch> _dctAutoSearches;
        protected ConcurrentDictionary<int, AutoSearch> _dctAutoSearches;
        protected List<AutoSearch> _lstAutoSearches;
        public List<AutoSearch> AutoSearches
        {
            get
            {
                if (_lstAutoSearches == null) this.ProcessAutoSearchList();
                return _lstAutoSearches;
            }
        }

        //[001]
        //protected Dictionary<int, ItemSearch> _dctItemSearches;
        protected ConcurrentDictionary<int, ItemSearch> _dctItemSearches;
        protected List<ItemSearch> _lstItemSearches;
        public List<ItemSearch> ItemSearches
        {
            get
            {
                if (_lstItemSearches == null) this.ProcessItemSearchList();
                return _lstItemSearches;
            }
        }

        //[001]
        //protected Dictionary<int, Nugget> _dctNuggets;
        protected ConcurrentDictionary<int, Nugget> _dctNuggets;
        protected List<Nugget> _lstNuggets;
        public List<Nugget> Nuggets
        {
            get
            {
                if (_lstNuggets == null) this.ProcessNuggetList();
                return _lstNuggets;
            }
        }

        //[001]
        //protected Dictionary<int, LeftNugget> _dctLeftNuggets;
        protected ConcurrentDictionary<int, LeftNugget> _dctLeftNuggets;
        protected List<LeftNugget> _lstLeftNuggets;
        public List<LeftNugget> LeftNuggets
        {
            get
            {
                if (_lstLeftNuggets == null) this.ProcessLeftNuggetList();
                return _lstLeftNuggets;
            }
        }

        //[001]
        //protected Dictionary<int, DataListNugget> _dctDataListNuggets;
        protected ConcurrentDictionary<int, DataListNugget> _dctDataListNuggets;
        protected List<DataListNugget> _lstDataListNuggets;
        public List<DataListNugget> DataListNuggets
        {
            get
            {
                if (_lstDataListNuggets == null) this.ProcessDataListNuggetList();
                return _lstDataListNuggets;
            }
        }

        //[001]
        //protected Dictionary<int, SetupNugget> _dctSetupNuggets;
        protected ConcurrentDictionary<int, SetupNugget> _dctSetupNuggets;
        protected List<SetupNugget> _lstSetupNuggets;
        public List<SetupNugget> SetupNuggets
        {
            get
            {
                if (_lstSetupNuggets == null) this.ProcessSetupNuggetList();
                return _lstSetupNuggets;
            }
        }

        //[001]
        //protected Dictionary<int, HomeNugget> _dctHomeNuggets;
        protected ConcurrentDictionary<int, HomeNugget> _dctHomeNuggets;
        protected List<HomeNugget> _lstHomeNuggets;
        public List<HomeNugget> HomeNuggets
        {
            get
            {
                if (_lstHomeNuggets == null) this.ProcessHomeNuggetList();
                return _lstHomeNuggets;
            }
        }

        #endregion

        #region Constructors

        private Site()
        {
            EnsureConfig();
            _intApplicationID = _cfg.ApplicationID;
            _strApplicationName = _cfg.ApplicationName;
            _strApplicationNameShort = _cfg.ApplicationNameShort;
            _strTemplatePath = _cfg.TemplatePath.TrimEnd(new char[] { '/' });
        }

        #endregion

        #region General Methods

        private void EnsureConfig()
        {
            if (_cfg == null) _cfg = ReboundUIConfigSection.GetConfig();
        }

        public static Site GetInstance()
        {
            Site obj = (Site)CacheManager.GetItem(CacheKeys.SiteObject);
            if (obj == null)
            {
                obj = new Site();
                CacheManager.StoreItem(CacheKeys.SiteObject, obj, CacheManager.CacheExpiryType.ThreeHours);
            }
            return obj;
        }

        #endregion

        #region SitePage Methods

        private void ProcessPageList()
        {
            if (_lstPages == null) _lstPages = new List<SitePage>();
            //if (_dctPages == null) _dctPages = new Dictionary<int, SitePage>();
            if (_dctPages == null) _dctPages = new ConcurrentDictionary<int, SitePage>();
            //_lstPages = new List<SitePage>();
            // _dctPages = new Dictionary<int, SitePage>();
            EnsureConfig();
            foreach (SitePageElement el in _cfg.SitePages)
            {
                SitePage pg = new SitePage(el.ID, el.Name, el.Url);
                _lstPages.Add(pg);
                if (_dctPages.ContainsKey(pg.ID) == false)
                    _dctPages.TryAdd(pg.ID, pg);
            }
        }

        public SitePage GetPage(string strSitePageName)
        {
            SitePage obj = new SitePage();
            if (Pages.Count > 0)
            {
                try
                {
                    // obj = _lstPages.Find(delegate (SitePage f) { return f.Name == strSitePageName; });
                    obj = _lstPages.Where(x => x.Name == strSitePageName).FirstOrDefault();
                }
                catch (Exception ex)
                {
                    //[002]
                    //var ai = new TelemetryClient(); // or re-use an existing instance
                    //ai.TrackTrace("Rebound.GlobalTrader.Site config file does not contain a Page called " + strSitePageName);
                    //ai.TrackException(ex);

                    new Errorlog().LogMessage("Error at GetPage in Site.cs : " + ex.Message);
                    //WriteError(ex);
                }

            }
            //  obj.Name = null;

            if (string.IsNullOrEmpty(obj.Name))
            {
                // _cfg = null;
                // throw new Exception(string.Format(@"Rebound.GlobalTrader.Site config file does not contain a Page called ""{0}""", strSitePageName));
                try
                {
                    // Method added due to check again if data is updated in ProcessLeftNuggetList method
                    _lstPages = null;
                    _dctPages = null;
                    _cfg = null;
                    this.ProcessPageList();
                    obj = _lstPages.Where(x => x.Name == strSitePageName).FirstOrDefault();
                }
                catch (Exception ex)
                {
                    //[002]
                    //var ai = new TelemetryClient(); // or re-use an existing instance
                    //ai.TrackTrace("Rebound.GlobalTrader.Site config file does not contain a Page called " + strSitePageName);
                    //ai.TrackException(ex);
                    new Errorlog().LogMessage("Error at GetPage in Site.cs : " + ex.Message);
                    //WriteError(ex);
                }
            }
            return obj;
        }

        public SitePage GetPage(int intID)
        {
            SitePage obj = new SitePage();
            if (Pages.Count > 0)
            {
                try
                {
                    if (_dctPages.ContainsKey(intID)) obj = _dctPages[intID];
                }
                catch (Exception ex)
                {
                    //[002]
                    //var ai = new TelemetryClient(); // or re-use an existing instance
                    //ai.TrackTrace("Exception GetPage by ID method : Site.cs");
                    //ai.TrackException(ex);
                    new Errorlog().LogMessage("Error at GetPage in Site.cs : " + ex.Message);
                    //WriteError(ex);
                }

            }
            if (string.IsNullOrEmpty(obj.Name))
            {
                try
                {
                    _lstPages = null;
                    _dctPages = null;
                    _cfg = null;
                    this.ProcessPageList();
                    if (_dctPages.ContainsKey(intID)) obj = _dctPages[intID];
                }
                catch (Exception ex)
                {
                    //[002]
                    //var ai = new TelemetryClient(); // or re-use an existing instance
                    //ai.TrackTrace("Exception GetPage by ID method : Site.cs");
                    //ai.TrackException(ex);
                    new Errorlog().LogMessage("Error at GetPage in Site.cs : " + ex.Message);
                    //WriteError(ex);
                }
            }
            //if (string.IsNullOrEmpty(obj.Name)) throw new Exception(string.Format(@"Rebound.GlobalTrader.Site config file does not contain a Page with ID {0}", intID));
            return obj;
        }

        public SitePage GetPage(Enum enmPage)
        {
            return GetPage(Convert.ToInt32(enmPage));
        }

        #endregion

        #region SiteSection Methods

        private void ProcessSectionList()
        {
            if (_lstSections == null) _lstSections = new List<SiteSection>();
            //if (_dctSections == null) _dctSections = new Dictionary<int, SiteSection>();
            if (_dctSections == null) _dctSections = new ConcurrentDictionary<int, SiteSection>();
            EnsureConfig();
            foreach (SiteSectionElement el in _cfg.SiteSections)
            {
                SiteSection ss = new SiteSection(el.ID, el.Name, el.Url);
                _lstSections.Add(ss);
                if (_dctSections.ContainsKey(ss.ID) == false)
                    _dctSections.TryAdd(ss.ID, ss);
            }
        }

        public string GetSectionUrl(int intSiteSectionID)
        {
            string strOut = "";
            if (Sections.Count > 0)
            {
                try
                {
                    if (_dctSections.ContainsKey(intSiteSectionID)) strOut = _dctSections[intSiteSectionID].Url;
                }
                catch (Exception ex)
                {
                    //[002]
                    //var ai = new TelemetryClient(); // or re-use an existing instance
                    //ai.TrackTrace("Exception GetSectionUrl by ID method : Site.cs");
                    //ai.TrackException(ex);
                    new Errorlog().LogMessage("Error at GetSectionUrl in Site.cs : " + ex.Message);
                    //WriteError(ex);
                }

            }
            if (strOut == "")
            {
                try
                {
                    _lstSections = null;
                    _dctSections = null;
                    _cfg = null;
                    this.ProcessSectionList();
                    if (_dctSections.ContainsKey(intSiteSectionID)) strOut = _dctSections[intSiteSectionID].Url;
                }
                catch (Exception ex)
                {
                    //[002]
                    //var ai = new TelemetryClient(); // or re-use an existing instance
                    //ai.TrackTrace("Exception GetSectionUrl by ID method : Site.cs");
                    //ai.TrackException(ex);
                    new Errorlog().LogMessage("Error at GetSectionUrl in Site.cs : " + ex.Message);
                    //WriteError(ex);
                }
            }
            //if (strOut == "") throw new Exception(string.Format(@"Rebound.GlobalTrader.Site config file does not contain a SiteSection with ID {0}", intSiteSectionID));
            return strOut;
        }

        public string GetSectionUrl(string strSiteSectionName)
        {
            string str = "";
            SiteSection obj = GetSection(strSiteSectionName);
            if (obj.Name == "")
            {
                throw new Exception(string.Format(@"Rebound.GlobalTrader.Site config file does not contain a SiteSection called ""{0}""", strSiteSectionName));
            }
            else
            {
                str = GetSectionUrl(obj.ID);
            }
            return str;
        }

        public SiteSection GetSection(string strSiteSectionName)
        {
            SiteSection obj = new SiteSection();
            if (Sections.Count > 0)
            {
                try
                {
                    //obj = _lstSections.Find(delegate (SiteSection f) { return f.Name == strSiteSectionName; });
                    obj = _lstSections.Where(x => x.Name == strSiteSectionName).FirstOrDefault();
                }
                catch (Exception ex)
                {
                    //[002]
                    //var ai = new TelemetryClient(); // or re-use an existing instance
                    //ai.TrackTrace("Exception GetSection with Name method : Site.cs");
                    //ai.TrackException(ex);
                    new Errorlog().LogMessage("Error at GetSection in Site.cs : " + ex.Message);
                    //WriteError(ex);
                }

            }
            //obj.Name = null;
            if (string.IsNullOrEmpty(obj.Name))
            {
                try
                {
                    _lstSections = null;
                    _dctSections = null;
                    _cfg = null;
                    this.ProcessSectionList();
                    obj = _lstSections.Where(x => x.Name == strSiteSectionName).FirstOrDefault();
                }
                catch (Exception ex)
                {
                    //[002]
                    //var ai = new TelemetryClient(); // or re-use an existing instance
                    //ai.TrackTrace("Exception GetSection with Name method : Site.cs");
                    //ai.TrackException(ex);
                    new Errorlog().LogMessage("Error at GetSection in Site.cs : " + ex.Message);
                    //WriteError(ex);
                }
            }
            //if (string.IsNullOrEmpty(obj.Name)) throw new Exception(string.Format(@"Rebound.GlobalTrader.Site config file does not contain a Section called ""{0}""", strSiteSectionName));
            return obj;
        }

        public SiteSection GetSection(int intID)
        {
            SiteSection obj = new SiteSection();
            if (Sections.Count > 0)
            {
                try
                {
                    if (_dctSections.ContainsKey(intID)) obj = _dctSections[intID];
                }
                catch (Exception ex)
                {
                    //[002]
                    //var ai = new TelemetryClient(); // or re-use an existing instance
                    //ai.TrackTrace("Exception GetSection by ID method : Site.cs");
                    //ai.TrackException(ex);
                    new Errorlog().LogMessage("Error at GetSection in Site.cs : " + ex.Message);
                    //WriteError(ex);
                }

            }
            if (string.IsNullOrEmpty(obj.Name))
            {
                try
                {
                    _lstSections = null;
                    _dctSections = null;
                    _cfg = null;
                    this.ProcessSectionList();
                    if (_dctSections.ContainsKey(intID)) obj = _dctSections[intID];
                }
                catch (Exception ex)
                {
                    //[002]
                    //var ai = new TelemetryClient(); // or re-use an existing instance
                    //ai.TrackTrace("Exception GetSection by ID method : Site.cs");
                    //ai.TrackException(ex);
                    new Errorlog().LogMessage("Error at GetSection in Site.cs : " + ex.Message);
                    //WriteError(ex);
                }
            }
            //if (string.IsNullOrEmpty(obj.Name)) throw new Exception(string.Format(@"Rebound.GlobalTrader.Site config file does not contain a Section with ID {0}", intID));
            return obj;
        }

        public SiteSection GetSection(Enum enmSection)
        {
            return GetSection(Convert.ToInt32(enmSection));
        }

        #endregion

        #region ColumnWidth Methods

        private void ProcessColumnWidthList()
        {
            if (_lstColumnWidth == null) _lstColumnWidth = new List<ColumnWidth>();
            //if (_dctColumnWidth == null) _dctColumnWidth = new Dictionary<string, ColumnWidth>();
            if (_dctColumnWidth == null) _dctColumnWidth = new ConcurrentDictionary<string, ColumnWidth>();
            EnsureConfig();
            foreach (ColumnWidthElement el in _cfg.ColumnWidths)
            {
                ColumnWidth cw = new ColumnWidth(el.Name, el.Width);
                _lstColumnWidth.Add(cw);
                if (_dctColumnWidth.ContainsKey(cw.Name) == false)
                    _dctColumnWidth.TryAdd(cw.Name, cw);
            }
        }

        public ColumnWidth GetColumnWidth(string strColumnName)
        {
            ColumnWidth cw = new ColumnWidth();
            if (ColumnWidths.Count > 0)
            {
                if (_dctColumnWidth.ContainsKey(strColumnName)) cw = _dctColumnWidth[strColumnName];
            }
            if (cw.Name == "") throw new Exception(string.Format(@"Rebound.GlobalTrader.Site config file does not contain a ColumnWidth called ""{0}""", strColumnName));
            return cw;
        }

        #endregion

        #region DropDown Methods

        private void ProcessDropDownList()
        {
            try     // [004] 
            {
                if (_lstDropDowns == null) _lstDropDowns = new List<DropDown>();
                //if (_dctDropDowns == null) _dctDropDowns = new Dictionary<int, DropDown>();
                if (_dctDropDowns == null) _dctDropDowns = new ConcurrentDictionary<int, DropDown>();
                EnsureConfig();
                //Error:Rebound.GlobalTrader.Site config file does not contain a DropDown with ID ##
                //Code changes: 29 Oct 2018: Ensure all dropdown list and dictinory clear before create new list:
                _lstDropDowns.Clear();
                _dctDropDowns.Clear();

                if (_cfg != null && _cfg.DropDowns.Count > 0) // [003]
                {
                    foreach (DropDownElement el in _cfg.DropDowns)
                    {
                        DropDown dd = new DropDown(el.ID, el.Name);
                        _lstDropDowns.Add(dd);
                        _dctDropDowns.TryAdd(dd.ID, dd);
                    }
                }
            }
            catch(Exception ex)
            {
                var exception = ex.Message;

            }

        }
        public DropDown GetDropDown(string strName)
        {
            DropDown obj = new DropDown();
            if (DropDowns.Count > 0)
            {
                try
                {
                    // obj = _lstDropDowns.Find(delegate (DropDown f) { return f.Name == strName; });
                    obj = DropDowns.Where(x => x.Name == strName).FirstOrDefault();
                }
                catch (Exception ex)
                {
                    //[002]
                    //var ai = new TelemetryClient(); // or re-use an existing instance
                    //ai.TrackTrace("Exception dropdwon method : Site.cs");
                    //ai.TrackException(ex);
                    new Errorlog().LogMessage("Error at GetDropDown in Site.cs : " + ex.Message);
                    //WriteError(ex);
                }
            }
            if (string.IsNullOrEmpty(obj.Name))
            {
                try
                {
                    // Method added due to check again if data is updated in ProcessLeftNuggetList method
                    _lstDropDowns = null;
                    _dctDropDowns = null;
                    _cfg = null;
                    this.ProcessDropDownList();
                    obj = DropDowns.Where(x => x.Name == strName).FirstOrDefault();
                }
                catch (Exception ex)
                {
                    //[002]
                    //var ai = new TelemetryClient(); // or re-use an existing instance
                    //ai.TrackTrace("Exception dropdown method : Site.cs");
                    //ai.TrackException(ex);
                    new Errorlog().LogMessage("Error at GetDropDown in Site.cs : " + ex.Message);
                    //WriteError(ex);
                }

            }
            //if (string.IsNullOrEmpty(obj.Name)) throw new Exception(string.Format(@"Rebound.GlobalTrader.Site config file does not contain a DropDown called ""{0}""", strName));
            return obj;
        }

        public DropDown GetDropDown(int intID)
        {
            DropDown obj = new DropDown();
            if (DropDowns.Count > 0)
            {
                try
                {
                    //if (_dctDropDowns.ContainsKey(intID)) obj = _dctDropDowns[intID];
                    obj = DropDowns.Where(x => x.ID == intID).FirstOrDefault();
                }
                catch (Exception ex)
                {
                    //[002]
                    //var ai = new TelemetryClient(); // or re-use an existing instance
                    //ai.TrackTrace("Exception GetDropDown by ID method : Site.cs");
                    //ai.TrackException(ex);
                    new Errorlog().LogMessage("Error at GetDropDown in Site.cs : " + ex.Message);
                    //WriteError(ex);
                }
            }
            // obj.Name = null;
            if (string.IsNullOrEmpty(obj.Name))
            {
                try
                {
                    // Method added due to check again if data is updated in ProcessLeftNuggetList method
                    _lstDropDowns = null;
                    _dctDropDowns = null;
                    _cfg = null;
                    this.ProcessDropDownList();
                    obj = DropDowns.Where(x => x.ID == intID).FirstOrDefault();
                }
                catch (Exception ex)
                {
                    //[002]
                    //var ai = new TelemetryClient(); // or re-use an existing instance
                    //ai.TrackTrace("Exception dropdown method : Site.cs");
                    //ai.TrackException(ex);
                    new Errorlog().LogMessage("Error at GetDropDown in Site.cs : " + ex.Message);
                    //WriteError(ex);
                }

            }
            //if (string.IsNullOrEmpty(obj.Name)) throw new Exception(string.Format(@"Rebound.GlobalTrader.Site config file does not contain a DropDown with ID {0}", intID));
            return obj;
        }


        //public DropDown GetDropDown(string strName)
        //{
        //    DropDown obj = new DropDown();
        //    if (DropDowns.Count > 0)
        //    {
        //        obj = _lstDropDowns.Find(delegate (DropDown f) { return f.Name == strName; });
        //    }
        //    if (string.IsNullOrEmpty(obj.Name)) throw new Exception(string.Format(@"Rebound.GlobalTrader.Site config file does not contain a DropDown called ""{0}""", strName));
        //    return obj;
        //}

        //public DropDown GetDropDown(int intID)
        //{
        //    DropDown obj = new DropDown();
        //    if (DropDowns.Count > 0)
        //    {
        //        if (_dctDropDowns.ContainsKey(intID)) obj = _dctDropDowns[intID];
        //    }
        //    if (string.IsNullOrEmpty(obj.Name)) throw new Exception(string.Format(@"Rebound.GlobalTrader.Site config file does not contain a DropDown with ID {0}", intID));
        //    return obj;
        //}

        #endregion

        #region AutoSearch Methods

        private void ProcessAutoSearchList()
        {
            if (_lstAutoSearches == null) _lstAutoSearches = new List<AutoSearch>();
            //if (_dctAutoSearches == null) _dctAutoSearches = new Dictionary<int, AutoSearch>();
            if (_dctAutoSearches == null) _dctAutoSearches = new ConcurrentDictionary<int, AutoSearch>();
            EnsureConfig();
            foreach (AutoSearchElement el in _cfg.AutoSearches)
            {
                AutoSearch dd = new AutoSearch(el.ID, el.Name);
                _lstAutoSearches.Add(dd);
                if (_dctAutoSearches.ContainsKey(dd.ID) == false)
                    _dctAutoSearches.TryAdd(dd.ID, dd);
            }
        }

        public AutoSearch GetAutoSearch(string strName)
        {
            AutoSearch obj = new AutoSearch();
            if (AutoSearches.Count > 0)
            {
                try
                {
                    //obj = _lstAutoSearches.Find(delegate (AutoSearch f) { return f.Name == strName; });
                    obj = _lstAutoSearches.Where(x => x.Name == strName).FirstOrDefault();
                }
                catch (Exception ex)
                {
                    //[002]
                    //var ai = new TelemetryClient(); // or re-use an existing instance
                    //ai.TrackTrace("Exception GetAutoSearch by string name method : Site.cs");
                    //ai.TrackException(ex);
                    new Errorlog().LogMessage("Error at GetAutoSearch in Site.cs : " + ex.Message);
                    //WriteError(ex);
                }

            }
            if (string.IsNullOrEmpty(obj.Name))
            {
                try
                {
                    _lstAutoSearches = null;
                    _dctAutoSearches = null;
                    _cfg = null;
                    this.ProcessAutoSearchList();
                    obj = _lstAutoSearches.Where(x => x.Name == strName).FirstOrDefault();
                }
                catch (Exception ex)
                {
                    //[002]
                    //var ai = new TelemetryClient(); // or re-use an existing instance
                    //ai.TrackTrace("Exception GetAutoSearch by string name method : Site.cs");
                    //ai.TrackException(ex);
                    new Errorlog().LogMessage("Error at GetAutoSearch in Site.cs : " + ex.Message);
                    //WriteError(ex);
                }
            }
            //if (string.IsNullOrEmpty(obj.Name)) throw new Exception(string.Format(@"Rebound.GlobalTrader.Site config file does not contain an AutoSearch called ""{0}""", strName));
            return obj;
        }

        public AutoSearch GetAutoSearch(int intID)
        {
            AutoSearch obj = new AutoSearch();
            if (AutoSearches.Count > 0)
            {
                try
                {
                    if (_dctAutoSearches.ContainsKey(intID)) obj = _dctAutoSearches[intID];
                }
                catch (Exception ex)
                {
                    //[002]
                    //var ai = new TelemetryClient(); // or re-use an existing instance
                    //ai.TrackTrace("Exception GetAutoSearch by ID method : Site.cs");
                    //ai.TrackException(ex);
                    new Errorlog().LogMessage("Error at GetAutoSearch in Site.cs : " + ex.Message);
                    //WriteError(ex);
                }

            }
            if (string.IsNullOrEmpty(obj.Name))
            {
                try
                {
                    _lstAutoSearches = null;
                    _dctAutoSearches = null;
                    _cfg = null;
                    this.ProcessAutoSearchList();
                    if (_dctAutoSearches.ContainsKey(intID)) obj = _dctAutoSearches[intID];
                }
                catch (Exception ex)
                {
                    //[002]
                    //var ai = new TelemetryClient(); // or re-use an existing instance
                    //ai.TrackTrace("Exception GetAutoSearch by ID method : Site.cs");
                    //ai.TrackException(ex);
                    new Errorlog().LogMessage("Error at GetAutoSearch in Site.cs : " + ex.Message);
                    //WriteError(ex);
                }
            }
            //if (string.IsNullOrEmpty(obj.Name)) throw new Exception(string.Format(@"Rebound.GlobalTrader.Site config file does not contain an AutoSearch with ID {0}", intID));
            return obj;
        }

        public AutoSearch GetAutoSearch(Enum enmAutoSearch)
        {
            return GetAutoSearch(Convert.ToInt32(enmAutoSearch));
        }

        #endregion

        #region ItemSearch Methods

        private void ProcessItemSearchList()
        {
            if (_lstItemSearches == null) _lstItemSearches = new List<ItemSearch>();
            //if (_dctItemSearches == null) _dctItemSearches = new Dictionary<int, ItemSearch>();
            if (_dctItemSearches == null) _dctItemSearches = new ConcurrentDictionary<int, ItemSearch>();
            EnsureConfig();
            foreach (ItemSearchElement el in _cfg.ItemSearches)
            {
                ItemSearch dd = new ItemSearch(el.ID, el.Name);
                _lstItemSearches.Add(dd);
                if (_dctItemSearches.ContainsKey(dd.ID) == false)
                    _dctItemSearches.TryAdd(dd.ID, dd);
            }
        }

        public ItemSearch GetItemSearch(string strName)
        {
            ItemSearch obj = new ItemSearch();
            if (ItemSearches.Count > 0)
            {
                try
                {
                    // obj = _lstItemSearches.Find(delegate (ItemSearch f) { return f.Name == strName; });
                    obj = _lstItemSearches.Where(x => x.Name == strName).FirstOrDefault();
                }
                catch (Exception ex)
                {
                    //[002]
                    //var ai = new TelemetryClient(); // or re-use an existing instance
                    //ai.TrackTrace("Exception GetItemSearch method : Site.cs");
                    //ai.TrackException(ex);
                    new Errorlog().LogMessage("Error at GetItemSearch in Site.cs : " + ex.Message);
                    //WriteError(ex);
                }
            }
            // obj.Name = null;
            if (string.IsNullOrEmpty(obj.Name))
            {
                try
                {
                    // Method added due to check again if data is updated in ProcessLeftNuggetList method
                    _lstItemSearches = null;
                    _dctItemSearches = null;
                    _cfg = null;
                    this.ProcessItemSearchList();
                    obj = _lstItemSearches.Where(x => x.Name == strName).FirstOrDefault();
                }
                catch (Exception ex)
                {
                    //[002]
                    //var ai = new TelemetryClient(); // or re-use an existing instance
                    //ai.TrackTrace("Exception GetItemSearch method : Site.cs");
                    //ai.TrackException(ex);
                    new Errorlog().LogMessage("Error at GetItemSearch in Site.cs : " + ex.Message);
                    //WriteError(ex);
                }
                //  throw new Exception(string.Format(@"Rebound.GlobalTrader.Site config file does not contain an ItemSearch called ""{0}""", strName));
            }
            return obj;
        }

        public ItemSearch GetItemSearch(int intID)
        {
            ItemSearch obj = new ItemSearch();
            if (ItemSearches.Count > 0)
            {
                try
                {
                    if (_dctItemSearches.ContainsKey(intID)) obj = _dctItemSearches[intID];
                }
                catch (Exception ex)
                {
                    //[002]
                    //var ai = new TelemetryClient(); // or re-use an existing instance
                    //ai.TrackTrace("Exception GetItemSearch by ID method : Site.cs");
                    //ai.TrackException(ex);
                    new Errorlog().LogMessage("Error at GetItemSearch in Site.cs : " + ex.Message);
                    //WriteError(ex);
                }
            }
            if (string.IsNullOrEmpty(obj.Name))
            {
                try
                {
                    _lstItemSearches = null;
                    _dctItemSearches = null;
                    _cfg = null;
                    this.ProcessItemSearchList();
                    if (_dctItemSearches.ContainsKey(intID)) obj = _dctItemSearches[intID];
                }
                catch (Exception ex)
                {
                    //[002]
                    //var ai = new TelemetryClient(); // or re-use an existing instance
                    //ai.TrackTrace("Exception GetItemSearch by ID method : Site.cs");
                    //ai.TrackException(ex);
                    new Errorlog().LogMessage("Error at GetItemSearch in Site.cs : " + ex.Message);
                    //WriteError(ex);
                }
            }
            //if (string.IsNullOrEmpty(obj.Name))
            //{

            //    throw new Exception(string.Format(@"Rebound.GlobalTrader.Site config file does not contain an ItemSearch with ID {0}", intID));
            //}
            return obj;
        }

        public ItemSearch GetItemSearch(Enum enmItemSearch)
        {
            return GetItemSearch(Convert.ToInt32(enmItemSearch));
        }

        #endregion

        #region Nugget Methods

        private void ProcessNuggetList()
        {
            if (_lstNuggets == null) _lstNuggets = new List<Nugget>();
            if (_dctNuggets == null) _dctNuggets = new ConcurrentDictionary<int, Nugget>();
            EnsureConfig();
            foreach (NuggetElement el in _cfg.Nuggets)
            {
                Nugget ng = new Nugget();
                ng.ID = el.ID;
                ng.Name = el.Name;
                _lstNuggets.Add(ng);
                if (_dctNuggets.ContainsKey(ng.ID) == false)
                    _dctNuggets.TryAdd(ng.ID, ng);
            }
        }

        public Nugget GetNugget(string strName)
        {
            Nugget obj = new Nugget();
            if (Nuggets.Count > 0)
            {
                try
                {
                    //obj = _lstNuggets.Find(delegate (Nugget f) { return f.Name == strName; });
                    obj = _lstNuggets.Where(x => x.Name == strName).FirstOrDefault();
                }
                catch (Exception ex)
                {
                    //[002]
                    //var ai = new TelemetryClient(); // or re-use an existing instance
                    //ai.TrackTrace("Exception GetNugget with Name method : Site.cs");
                    //ai.TrackException(ex);
                    new Errorlog().LogMessage("Error at GetNugget in Site.cs : " + ex.Message);
                    //WriteError(ex);
                }

            }
            if (string.IsNullOrEmpty(obj.Name))
            {
                try
                {
                    _lstNuggets = null;
                    _dctNuggets = null;
                    _cfg = null;
                    this.ProcessNuggetList();
                    obj = _lstNuggets.Where(x => x.Name == strName).FirstOrDefault();
                }
                catch (Exception ex)
                {
                    //[002]
                    //var ai = new TelemetryClient(); // or re-use an existing instance
                    //ai.TrackTrace("Exception GetNugget with Name method : Site.cs");
                    //ai.TrackException(ex);
                    new Errorlog().LogMessage("Error at GetNugget in Site.cs : " + ex.Message);
                    //WriteError(ex);
                }
            }
            //if (string.IsNullOrEmpty(obj.Name)) throw new Exception(string.Format(@"Rebound.GlobalTrader.Site config file does not contain a Nugget called ""{0}""", strName));
            return obj;
        }

        public Nugget GetNugget(int intID)
        {
            Nugget obj = new Nugget();
            if (Nuggets.Count > 0)
            {
                try
                {
                    if (_dctNuggets.ContainsKey(intID)) obj = _dctNuggets[intID];
                }
                catch (Exception ex)
                {
                    //[002]
                    //var ai = new TelemetryClient(); // or re-use an existing instance
                    //ai.TrackTrace("Exception GetNugget by ID method : Site.cs");
                    //ai.TrackException(ex);
                    new Errorlog().LogMessage("Error at GetNugget in Site.cs : " + ex.Message);
                    //WriteError(ex);
                }

            }
            if (string.IsNullOrEmpty(obj.Name))
            {
                try
                {
                    _lstNuggets = null;
                    _dctNuggets = null;
                    _cfg = null;
                    this.ProcessNuggetList();
                    if (_dctNuggets.ContainsKey(intID)) obj = _dctNuggets[intID];
                }
                catch (Exception ex)
                {
                    //[002]
                    //var ai = new TelemetryClient(); // or re-use an existing instance
                    //ai.TrackTrace("Exception GetNugget by ID method : Site.cs");
                    //ai.TrackException(ex);
                    new Errorlog().LogMessage("Error at GetNugget in Site.cs : " + ex.Message);
                    //WriteError(ex);
                }
            }
            //if (string.IsNullOrEmpty(obj.Name)) throw new Exception(string.Format(@"Rebound.GlobalTrader.Site config file does not contain a Nugget with ID {0}", intID));
            return obj;
        }

        public Nugget GetNugget(Enum enmNugget)
        {
            return GetNugget(Convert.ToInt32(enmNugget));
        }

        #endregion

        #region LeftNugget Methods

        private void ProcessLeftNuggetList()
        {
            if (_lstLeftNuggets == null) _lstLeftNuggets = new List<LeftNugget>();
            if (_dctLeftNuggets == null) _dctLeftNuggets = new ConcurrentDictionary<int, LeftNugget>();
            EnsureConfig();
            foreach (LeftNuggetElement el in _cfg.LeftNuggets)
            {
                LeftNugget ng = new LeftNugget();
                ng.ID = el.ID;
                ng.Name = el.Name;
                _lstLeftNuggets.Add(ng);
                if (_dctLeftNuggets.ContainsKey(ng.ID) == false)
                    _dctLeftNuggets.TryAdd(ng.ID, ng);
            }
        }

        public LeftNugget GetLeftNugget(string strName)
        {
            LeftNugget obj = new LeftNugget();
            if (LeftNuggets.Count > 0)
            {
                try
                {
                    // Code commented for handle index out of range 
                    //obj = _lstLeftNuggets.Find(delegate (LeftNugget f) { return f.Name == strName; });
                    obj = _lstLeftNuggets.Where(x => x.Name == strName).FirstOrDefault();
                }
                catch (Exception ex)
                {
                    //[002]
                    //var ai = new TelemetryClient(); // or re-use an existing instance
                    //ai.TrackTrace("Exception GetLeftNugget with Name method : Site.cs");
                    //ai.TrackException(ex);
                    new Errorlog().LogMessage("Error at GetLeftNugget in Site.cs : " + ex.Message);
                    //WriteError(ex);
                }
            }
            //if (string.IsNullOrEmpty(obj.Name)) throw new Exception(string.Format(@"Rebound.GlobalTrader.Site config file does not contain a LeftNugget called ""{0}""", strName));
            if (string.IsNullOrEmpty(obj.Name))
            {
                try
                {
                    // Method added due to check again if data is updated in ProcessLeftNuggetList method
                    _lstLeftNuggets = null;
                    _dctLeftNuggets = null;
                    _cfg = null;
                    this.ProcessLeftNuggetList();
                    obj = _lstLeftNuggets.Where(x => x.Name == strName).FirstOrDefault();
                }
                catch (Exception ex)
                {
                    //[002]
                    //var ai = new TelemetryClient(); // or re-use an existing instance
                    //ai.TrackTrace("Exception GetLeftNugget with Name method : Site.cs");
                    //ai.TrackException(ex);
                    new Errorlog().LogMessage("Error at GetLeftNugget in Site.cs : " + ex.Message);
                    //WriteError(ex);
                }
            }
            return obj;
        }

        public LeftNugget GetLeftNugget(int intID)
        {
            LeftNugget obj = new LeftNugget();
            if (LeftNuggets.Count > 0)
            {
                try
                {
                    if (_dctLeftNuggets.ContainsKey(intID)) obj = _dctLeftNuggets[intID];
                }
                catch (Exception ex)
                {
                    //[002]
                    //var ai = new TelemetryClient(); // or re-use an existing instance
                    //ai.TrackTrace("Exception GetLeftNugget by ID method : Site.cs");
                    //ai.TrackException(ex);
                    new Errorlog().LogMessage("Error at GetLeftNugget in Site.cs : " + ex.Message);
                    //WriteError(ex);
                }

            }
            if (string.IsNullOrEmpty(obj.Name))
            {
                try
                {
                    _lstLeftNuggets = null;
                    _dctLeftNuggets = null;
                    _cfg = null;
                    this.ProcessLeftNuggetList();
                    if (_dctLeftNuggets.ContainsKey(intID)) obj = _dctLeftNuggets[intID];
                }
                catch (Exception ex)
                {
                    //[002]
                    //var ai = new TelemetryClient(); // or re-use an existing instance
                    //ai.TrackTrace("Exception GetLeftNugget by ID method : Site.cs");
                    //ai.TrackException(ex);
                    new Errorlog().LogMessage("Error at GetLeftNugget in Site.cs : " + ex.Message);
                    //WriteError(ex);
                }
            }
            //if (string.IsNullOrEmpty(obj.Name)) throw new Exception(string.Format(@"Rebound.GlobalTrader.Site config file does not contain a LeftNugget with ID {0}", intID));
            return obj;
        }

        public LeftNugget GetLeftNugget(Enum enmLeftNugget)
        {
            return GetLeftNugget(Convert.ToInt32(enmLeftNugget));
        }

        #endregion

        #region DataListNugget Methods

        private void ProcessDataListNuggetList()
        {
            if (_lstDataListNuggets == null) _lstDataListNuggets = new List<DataListNugget>();
            if (_dctDataListNuggets == null) _dctDataListNuggets = new ConcurrentDictionary<int, DataListNugget>();
            EnsureConfig();
            foreach (DataListNuggetElement el in _cfg.DataListNuggets)
            {
                DataListNugget ng = new DataListNugget();
                ng.ID = el.ID;
                ng.Name = el.Name;
                _lstDataListNuggets.Add(ng);
                if (_dctDataListNuggets.ContainsKey(ng.ID) == false)
                    _dctDataListNuggets.TryAdd(ng.ID, ng);
            }
        }

        //public DataListNugget GetDataListNugget(string strName)
        //{
        //    DataListNugget obj = new DataListNugget();
        //    if (DataListNuggets.Count > 0)
        //    {
        //        obj = _lstDataListNuggets.Find(delegate (DataListNugget f) { return f.Name == strName; });
        //    }
        //    if (string.IsNullOrEmpty(obj.Name)) throw new Exception(string.Format(@"Rebound.GlobalTrader.Site config file does not contain a DataListNugget called ""{0}""", strName));
        //    return obj;
        //}

        public DataListNugget GetDataListNugget(string strName)
        {
            DataListNugget obj = new DataListNugget();
            if (DataListNuggets.Count > 0)
            {
                try
                {
                    //obj = _lstDataListNuggets.Find(delegate (DataListNugget f) { return f.Name == strName; });
                    obj = _lstDataListNuggets.Where(x => x.Name == strName).FirstOrDefault();
                }
                catch (Exception ex)
                {
                    //[002]
                    //var ai = new TelemetryClient(); // or re-use an existing instance
                    //ai.TrackTrace("Exception GetDataListNugget by Name method : Site.cs");
                    //ai.TrackException(ex);
                    new Errorlog().LogMessage("Error at GetDataListNugget in Site.cs : " + ex.Message);
                    //WriteError(ex);
                }
            }
            if (string.IsNullOrEmpty(obj.Name))
            {
                try
                {
                    _lstDataListNuggets = null;
                    _dctDataListNuggets = null;
                    _cfg = null;
                    this.ProcessDataListNuggetList();
                    obj = _lstDataListNuggets.Where(x => x.Name == strName).FirstOrDefault();
                }
                catch (Exception ex)
                {
                    //[002]
                    //var ai = new TelemetryClient(); // or re-use an existing instance
                    //ai.TrackTrace("Exception GetDataListNugget by Name method : Site.cs");
                    //ai.TrackException(ex);
                    new Errorlog().LogMessage("Error at GetDataListNugget in Site.cs : " + ex.Message);
                    //WriteError(ex);
                }
            }
            // if (string.IsNullOrEmpty(obj.Name)) throw new Exception(string.Format(@"Rebound.GlobalTrader.Site config file does not contain a DataListNugget called ""{0}""", strName));
            return obj;
        }

        //public DataListNugget GetDataListNugget(int intID)
        //{
        //    DataListNugget obj = new DataListNugget();
        //    if (DataListNuggets.Count > 0)
        //    {
        //        if (_dctDataListNuggets.ContainsKey(intID)) obj = _dctDataListNuggets[intID];
        //    }
        //    if (string.IsNullOrEmpty(obj.Name)) throw new Exception(string.Format(@"Rebound.GlobalTrader.Site config file does not contain a DataListNugget with ID {0}", intID));
        //    return obj;
        //}
        public DataListNugget GetDataListNugget(int intID)
        {
            DataListNugget obj = new DataListNugget();
            if (DataListNuggets.Count > 0)
            {
                try
                {
                    if (_dctDataListNuggets.ContainsKey(intID)) obj = _dctDataListNuggets[intID];
                }
                catch (Exception ex)
                {
                    //[002]
                    //var ai = new TelemetryClient(); // or re-use an existing instance
                    //ai.TrackTrace("Exception GetDataListNugget by ID method : Site.cs");
                    //ai.TrackException(ex);
                    new Errorlog().LogMessage("Error at GetDataListNugget in Site.cs : " + ex.Message);
                    //WriteError(ex);
                }

            }
            if (string.IsNullOrEmpty(obj.Name))
            {
                try
                {
                    _lstDataListNuggets = null;
                    _dctDataListNuggets = null;
                    _cfg = null;
                    this.ProcessDataListNuggetList();
                    if (_dctDataListNuggets.ContainsKey(intID)) obj = _dctDataListNuggets[intID];
                }
                catch (Exception ex)
                {
                    //[002]
                    //var ai = new TelemetryClient(); // or re-use an existing instance
                    //ai.TrackTrace("Exception GetDataListNugget by ID method : Site.cs");
                    //ai.TrackException(ex);
                    new Errorlog().LogMessage("Error at GetDataListNugget in Site.cs : " + ex.Message);
                    //WriteError(ex);
                }
            }
            //if (string.IsNullOrEmpty(obj.Name)) throw new Exception(string.Format(@"Rebound.GlobalTrader.Site config file does not contain a DataListNugget with ID {0}", intID));
            return obj;
        }
        public DataListNugget GetDataListNugget(Enum enmDataListNugget)
        {
            return GetDataListNugget(Convert.ToInt32(enmDataListNugget));
        }

        #endregion

        #region SetupNugget Methods

        private void ProcessSetupNuggetList()
        {
            if (_lstSetupNuggets == null) _lstSetupNuggets = new List<SetupNugget>();
            if (_dctSetupNuggets == null) _dctSetupNuggets = new ConcurrentDictionary<int, SetupNugget>();
            EnsureConfig();
            foreach (SetupNuggetElement el in _cfg.SetupNuggets)
            {
                SetupNugget ng = new SetupNugget();
                ng.ID = el.ID;
                ng.Name = el.Name;
                _lstSetupNuggets.Add(ng);
                if (_dctSetupNuggets.ContainsKey(ng.ID) == false)
                    _dctSetupNuggets.TryAdd(ng.ID, ng);
            }
        }

        public SetupNugget GetSetupNugget(string strName)
        {
            SetupNugget obj = new SetupNugget();
            if (SetupNuggets.Count > 0)
            {
                try
                {
                    //obj = _lstSetupNuggets.Find(delegate (SetupNugget f) { return f.Name == strName; });
                    obj = _lstSetupNuggets.Where(x => x.Name == strName).FirstOrDefault();
                }
                catch (Exception ex)
                {
                    //[002]
                    //var ai = new TelemetryClient(); // or re-use an existing instance
                    //ai.TrackTrace("Exception GetSetupNugget with Name method : Site.cs");
                    //ai.TrackException(ex);
                    new Errorlog().LogMessage("Error at GetSetupNugget in Site.cs : " + ex.Message);
                    //WriteError(ex);
                }
            }
            if (string.IsNullOrEmpty(obj.Name))
            {
                try
                {
                    _lstSetupNuggets = null;
                    _dctSetupNuggets = null;
                    _cfg = null;
                    this.ProcessSetupNuggetList();
                    obj = _lstSetupNuggets.Where(x => x.Name == strName).FirstOrDefault();
                }
                catch (Exception ex)
                {
                    //[002]
                    //var ai = new TelemetryClient(); // or re-use an existing instance
                    //ai.TrackTrace("Exception GetSetupNugget with Name method : Site.cs");
                    //ai.TrackException(ex);
                    new Errorlog().LogMessage("Error at GetSetupNugget in Site.cs : " + ex.Message);
                    //WriteError(ex);
                }
            }
            //if (string.IsNullOrEmpty(obj.Name)) throw new Exception(string.Format(@"Rebound.GlobalTrader.Site config file does not contain a SetupNugget called ""{0}""", strName));
            return obj;
        }

        public SetupNugget GetSetupNugget(int intID)
        {
            SetupNugget obj = new SetupNugget();
            if (SetupNuggets.Count > 0)
            {
                try
                {
                    if (_dctSetupNuggets.ContainsKey(intID)) obj = _dctSetupNuggets[intID];
                }
                catch (Exception ex)
                {
                    //[002]
                    //var ai = new TelemetryClient(); // or re-use an existing instance
                    //ai.TrackTrace("Exception GetSetupNugget by ID method : Site.cs");
                    //ai.TrackException(ex);
                    new Errorlog().LogMessage("Error at GetSetupNugget in Site.cs : " + ex.Message);
                    //WriteError(ex);
                }

            }
            if (string.IsNullOrEmpty(obj.Name))
            {
                try
                {
                    _lstSetupNuggets = null;
                    _dctSetupNuggets = null;
                    _cfg = null;
                    this.ProcessSetupNuggetList();
                    if (_dctSetupNuggets.ContainsKey(intID)) obj = _dctSetupNuggets[intID];
                }
                catch (Exception ex)
                {
                    //[002]
                    //var ai = new TelemetryClient(); // or re-use an existing instance
                    //ai.TrackTrace("Exception GetSetupNugget by ID method : Site.cs");
                    //ai.TrackException(ex);
                    new Errorlog().LogMessage("Error at GetSetupNugget in Site.cs : " + ex.Message);
                    //WriteError(ex);
                }
            }
            //if (string.IsNullOrEmpty(obj.Name)) throw new Exception(string.Format(@"Rebound.GlobalTrader.Site config file does not contain a SetupNugget with ID {0}", intID));
            return obj;
        }

        public SetupNugget GetSetupNugget(Enum enmSetupNugget)
        {
            return GetSetupNugget(Convert.ToInt32(enmSetupNugget));
        }

        #endregion

        #region HomeNugget Methods

        private void ProcessHomeNuggetList()
        {
            if (_lstHomeNuggets == null) _lstHomeNuggets = new List<HomeNugget>();
            if (_dctHomeNuggets == null) _dctHomeNuggets = new ConcurrentDictionary<int, HomeNugget>();
            EnsureConfig();
            foreach (HomeNuggetElement el in _cfg.HomeNuggets)
            {
                HomeNugget ng = new HomeNugget();
                ng.ID = el.ID;
                ng.Name = el.Name;
                _lstHomeNuggets.Add(ng);
                if (_dctHomeNuggets.ContainsKey(ng.ID) == false)
                    _dctHomeNuggets.TryAdd(ng.ID, ng);
            }
        }

        public HomeNugget GetHomeNugget(string strName)
        {
            HomeNugget obj = new HomeNugget();
            if (HomeNuggets.Count > 0)
            {
                try
                {
                    //obj = _lstHomeNuggets.Find(delegate (HomeNugget f) { return f.Name == strName; });
                    obj = _lstHomeNuggets.Where(x => x.Name == strName).FirstOrDefault();
                }
                catch (Exception ex)
                {
                    //[002]
                    //var ai = new TelemetryClient(); // or re-use an existing instance
                    //ai.TrackTrace("Exception GetHomeNugget with Name method : Site.cs");
                    //ai.TrackException(ex);
                    new Errorlog().LogMessage("Error at GetHomeNugget in Site.cs : " + ex.Message);
                    //WriteError(ex);
                }

            }
            if (string.IsNullOrEmpty(obj.Name))
            {
                try
                {
                    _lstHomeNuggets = null;
                    _dctHomeNuggets = null;
                    _cfg = null;
                    this.ProcessHomeNuggetList();
                    obj = _lstHomeNuggets.Where(x => x.Name == strName).FirstOrDefault();
                }
                catch (Exception ex)
                {
                    //[002]
                    //var ai = new TelemetryClient(); // or re-use an existing instance
                    //ai.TrackTrace("Exception GetHomeNugget with Name method : Site.cs");
                    //ai.TrackException(ex);
                    new Errorlog().LogMessage("Error at GetHomeNugget in Site.cs : " + ex.Message);
                    //WriteError(ex);
                }
            }
            //if (string.IsNullOrEmpty(obj.Name)) throw new Exception(string.Format(@"Rebound.GlobalTrader.Site config file does not contain a HomeNugget called ""{0}""", strName));
            return obj;
        }

        public HomeNugget GetHomeNugget(int intID)
        {
            HomeNugget obj = new HomeNugget();
            if (HomeNuggets.Count > 0)
            {
                try
                {
                    if (_dctHomeNuggets.ContainsKey(intID)) obj = _dctHomeNuggets[intID];
                }
                catch (Exception ex)
                {
                    //[002]
                    //var ai = new TelemetryClient(); // or re-use an existing instance
                    //ai.TrackTrace("Exception GetHomeNugget by ID method : Site.cs");
                    //ai.TrackException(ex);
                    new Errorlog().LogMessage("Error at GetHomeNugget in Site.cs : " + ex.Message);
                    //WriteError(ex);
                }

            }
            if (string.IsNullOrEmpty(obj.Name))
            {
                try
                {
                    _lstHomeNuggets = null;
                    _dctHomeNuggets = null;
                    _cfg = null;
                    this.ProcessHomeNuggetList();
                    if (_dctHomeNuggets.ContainsKey(intID)) obj = _dctHomeNuggets[intID];
                }
                catch (Exception ex)
                {
                    //[002]
                    //var ai = new TelemetryClient(); // or re-use an existing instance
                    //ai.TrackTrace("Exception GetHomeNugget by ID method : Site.cs");
                    //ai.TrackException(ex);
                    new Errorlog().LogMessage("Error at GetHomeNugget in Site.cs : " + ex.Message);
                    //WriteError(ex);
                }
            }
            //if (string.IsNullOrEmpty(obj.Name)) throw new Exception(string.Format(@"Rebound.GlobalTrader.Site config file does not contain a HomeNugget with ID {0}", intID));
            return obj;
        }

        public HomeNugget GetHomeNugget(Enum enmHomeNugget)
        {
            return GetHomeNugget(Convert.ToInt32(enmHomeNugget));
        }

        #endregion

    }
}
