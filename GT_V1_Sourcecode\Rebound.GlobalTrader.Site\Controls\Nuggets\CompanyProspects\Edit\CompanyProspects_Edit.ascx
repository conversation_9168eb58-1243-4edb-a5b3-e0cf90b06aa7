<%@ Control Language="C#" CodeBehind="CompanyProspects_Edit.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Forms.CompanyProspects_Edit" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<%@ Register TagPrefix="cc1" Namespace="Rebound.GlobalTrader.Site" Assembly="Rebound.GlobalTrader.Site" %>
<ReboundUI_Form:DesignBase ID="ctlDB" runat="server" ShowRequiredFieldsExplanation="true" ShowQuickHelp="false">
    <Links>
        <ReboundUI:IconButton ID="ibtnSave" runat="server" IconGroup="Nugget" IconTitleResource="Save" IconCSSType="Save" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
        <ReboundUI:IconButton ID="ibtnCancel" runat="server" IconGroup="Nugget" IconTitleResource="Cancel" IconCSSType="Cancel" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
    </Links>
    <Explanation><%=Functions.GetGlobalResource("FormExplanations", "CompanyProspects_Edit")%></Explanation>
    <Content>
        <ReboundUI_Table:Form ID="frm" runat="server">
            <ReboundUI_Form:FormField ID="ctlProspectType" runat="server" FieldID="ddlProspectType" ResourceTitle="ProspectType">
                <Field>
                    <ReboundDropDown:CompanyProspects ID="ddlProspectType" runat="server" />
                </Field>
            </ReboundUI_Form:FormField>
            <ReboundUI_Form:FormField ID="ctlMFRBoardLevel" runat="server" FieldID="radMFRBoardLevel" ResourceTitle="MFRBoardLevel">
                <Field>
                    <asp:RadioButtonList ID="radMFRBoardLevel" name="radioList" runat="server" RepeatDirection="Horizontal">
                        <asp:ListItem Text="Yes" Value="1" />
                        <asp:ListItem Text="No" Value="0" />
                    </asp:RadioButtonList>
                </Field>
            </ReboundUI_Form:FormField>

            <ReboundUI_Form:FormField ID="ctlFinalAssembly" runat="server" FieldID="radFinalAssembly" ResourceTitle="FinalAssembly">
                <Field>
                    <asp:RadioButtonList ID="radFinalAssembly" name="radioList" runat="server" RepeatDirection="Horizontal">
                        <asp:ListItem Text="Yes" Value="1" />
                        <asp:ListItem Text="No" Value="0" />
                    </asp:RadioButtonList>
                </Field>
            </ReboundUI_Form:FormField>
            <ReboundUI_Form:FormField ID="ctlEndCustomer" runat="server" FieldID="txtEndCustomer" ResourceTitle="EndCustomer">
                <Field>
                    <ReboundUI:ReboundTextBox ID="txtEndCustomer" runat="server" TextMode="MultiLine" Width="350" />
                </Field>
            </ReboundUI_Form:FormField>
            <%-- <ReboundUI_Form:FormField ID="ctlIndustry" runat="server" FieldID="ddlIndustry" ResourceTitle="Industry">
                <Field>
                    <ReboundDropDown:CompanyProspects ID="ddlIndustry" runat="server" />
                </Field>
            </ReboundUI_Form:FormField>--%>
           <%-- <ReboundUI_Form:FormField ID="ctlIndustry" runat="server" FieldID="chkIndustry" Visible="false" ResourceTitle="Industry">
                <Field>
                    <ReboundUI:ImageCheckBox ID="chkIndustry" runat="server" Enabled="true" />
                </Field>
            </ReboundUI_Form:FormField>--%>

            <ReboundUI_Form:FormField id="ctlIndustryType" runat="server" FieldID="ctlSelectIndustryType" ResourceTitle="IndustryType">
				<Field>
					<ReboundUI_Table:Base id="tblSelectIndustryType" runat="server">
						<ReboundUI_FormFieldCollection:MultiSelection id="ctlSelectIndustryType" runat="server" SelectedTitleResource="Selected" UnselectedTitleResource="Unselected" PanelHeight="75" style="margin:0px;" />
					</ReboundUI_Table:Base>
					<Reboundui:FlexiDataTable id="tblIndustryType" runat="server" />
                      <style>
                          #ctl00_cphMain_ctlCRMProspects_ctlDB_ctl14_ctlCompanyProspects_Edit_ctlDB_ctlIndustryType_ctl03_ctlSelectIndustryType_tdLabel table tbody tr td:nth-child(1) {
                              width: 175px !important;
                              float: left;
                          }

                          #ctl00_cphMain_ctlCRMProspects_ctlDB_ctl14_ctlCompanyProspects_Edit_ctlDB_ctlIndustryType_ctl03_ctlSelectIndustryType_tdLabel table tbody tr td:nth-child(2) {
                              width: 118px !important;
                              float: left;
                              padding: 22px !important;
                              margin-left: 11px;
                          }

                          #ctl00_cphMain_ctlCRMProspects_ctlDB_ctl14_ctlCompanyProspects_Edit_ctlDB_ctlIndustryType_ctl03_ctlSelectIndustryType_tdLabel table tbody tr td:nth-child(3) {
                              width: 175px !important;
                              float: left;
                          }

                          #ctl00_cphMain_ctlCRMProspects_ctlDB_ctl14_ctlCompanyProspects_Edit_ctlDB_ctlMFRBoardLevel_ctl03_radMFRBoardLevel tr td {
                              float: left !important;
                              display: block !important;
                          }

                          #ctl00_cphMain_ctlCRMProspects_ctlDB_ctl14_ctlCompanyProspects_Edit_ctlDB_ctlFinalAssembly_ctl03_radFinalAssembly tr td {
                              float: left !important;
                              display: block !important;
                          }
                      </style>
				</Field>
			</ReboundUI_Form:FormField>

            
            <%--<ReboundUI_Form:FormField id="ctlEntertainmentType" runat="server" FieldID="ctlEntertainmentType" ResourceTitle="EntertainmentType">
				<Field>
					<ReboundUI_Table:Base id="tblSelectEntertainmentType" runat="server">
						<ReboundUI_FormFieldCollection:MultiSelection id="ctlEntertainmentType" runat="server" SelectedTitleResource="Selected" UnselectedTitleResource="Unselected" PanelHeight="75" style="margin:0px;" />
					</ReboundUI_Table:Base>
					<Reboundui:FlexiDataTable id="tblEntertainmentType" runat="server" />
                      <style>
                          #ctl00_cphMain_ctlCRMProspects_ctlDB_ctl14_ctlCompanyProspects_Edit_ctlDB_ctlEntertainmentType_ctl03_ctlEntertainmentType_tdLabel table tbody tr td:nth-child(1) {
                              width: 175px !important;
                              float: left;
                          }

                          #ctl00_cphMain_ctlCRMProspects_ctlDB_ctl14_ctlCompanyProspects_Edit_ctlDB_ctlEntertainmentType_ctl03_ctlEntertainmentType_tdLabel table tbody tr td:nth-child(2) {
                              width: 118px !important;
                              float: left;
                              padding: 22px !important;
                              margin-left: 11px;
                          }

                          #ctl00_cphMain_ctlCRMProspects_ctlDB_ctl14_ctlCompanyProspects_Edit_ctlDB_ctlEntertainmentType_ctl03_ctlEntertainmentType_tdLabel table tbody tr td:nth-child(3) {
                              width: 175px !important;
                              float: left;
                          }

                          #ctl00_cphMain_ctlCRMProspects_ctlDB_ctl14_ctlCompanyProspects_Edit_ctlDB_ctlMFRBoardLevel_ctl03_radMFRBoardLevel tr td {
                              float: left !important;
                              display: block !important;
                          }

                          #ctl00_cphMain_ctlCRMProspects_ctlDB_ctl14_ctlCompanyProspects_Edit_ctlDB_ctlFinalAssembly_ctl03_radFinalAssembly tr td {
                              float: left !important;
                              display: block !important;
                          }
                      </style>
				</Field>
			</ReboundUI_Form:FormField>


                 <ReboundUI_Form:FormField id="ctlIndustryAreaType" runat="server" FieldID="ctlSelectIndustryAreaType" ResourceTitle="IndustryAreaType">
				<Field>
					<ReboundUI_Table:Base id="tblSelectIndustryAreaType" runat="server">
						<ReboundUI_FormFieldCollection:MultiSelection id="ctlSelectIndustryAreaType" runat="server" SelectedTitleResource="Selected" UnselectedTitleResource="Unselected" PanelHeight="75" style="margin:0px;" />
					</ReboundUI_Table:Base>
					<Reboundui:FlexiDataTable id="tblIndustryAreaType" runat="server" />
                      <style>
                          #ctl00_cphMain_ctlCRMProspects_ctlDB_ctl14_ctlCompanyProspects_Edit_ctlDB_ctlIndustryType_ctl03_ctlSelectIndustryType_tdLabel table tbody tr td:nth-child(1) {
                              width: 175px !important;
                              float: left;
                          }

                          #ctl00_cphMain_ctlCRMProspects_ctlDB_ctl14_ctlCompanyProspects_Edit_ctlDB_ctlIndustryAreaType_ctl03_ctlSelectIndustryAreaType_tdLabel table tbody tr td:nth-child(2) {
                              width: 118px !important;
                              float: left;
                              padding: 22px !important;
                              margin-left: 11px;
                          }

                          #ctl00_cphMain_ctlCRMProspects_ctlDB_ctl14_ctlCompanyProspects_Edit_ctlDB_ctlIndustryAreaType_ctl03_ctlSelectIndustryAreaType_tdLabel table tbody tr td:nth-child(3) {
                              width: 175px !important;
                              float: left;
                          }
                      </style>
				</Field>
			</ReboundUI_Form:FormField>--%>




            <ReboundUI_Form:FormField ID="ctlLimitedEstimate" runat="server" FieldID="txtLimitedEstimate" ResourceTitle="LimitedEstimate">
                <Field>
                    <ReboundUI:ReboundTextBox ID="txtLimitedEstimate" runat="server" AllowEnterButtonPress="true" TextMode="MultiLine" Width="350" />
                </Field>
            </ReboundUI_Form:FormField>
            <ReboundUI_Form:FormField ID="ctlHealthRating" runat="server" FieldID="txtHealthRating" ResourceTitle="HealthRating">
                <Field>
                    <ReboundUI:ReboundTextBox ID="txtHealthRating" runat="server" AllowEnterButtonPress="true" TextMode="MultiLine" Width="350" />
                </Field>
            </ReboundUI_Form:FormField>
            <ReboundUI_Form:FormField ID="ctlElectronicSpend" runat="server" FieldID="ddlElectronicSpend" ResourceTitle="ElectronicSpend">
                <Field>
                    <ReboundDropDown:CompanyProspects ID="ddlElectronicSpend" runat="server" />
                </Field>
            </ReboundUI_Form:FormField>
            <ReboundUI_Form:FormField ID="ctlFrequencyOfPurchase" runat="server" FieldID="ddlFrequencyOfPurchase" ResourceTitle="FrequencyOfPurchase">
                <Field>
                    <ReboundDropDown:CompanyProspects ID="ddlFrequencyOfPurchase" runat="server" />
                </Field>
            </ReboundUI_Form:FormField>
            <ReboundUI_Form:FormField ID="ctlCommodities" runat="server" FieldID="txtCommodities" ResourceTitle="Commodities">
                <Field>
                    <%--<ReboundDropDown:CompanyProspects ID="ddlCommodities" runat="server" />--%>
                    <ReboundUI:ReboundTextBox ID="txtCommodities" runat="server" AllowEnterButtonPress="true" TextMode="MultiLine" Width="350" />
                </Field>
            </ReboundUI_Form:FormField>
            <ReboundUI_Form:FormField ID="ctlTurnover" runat="server" FieldID="ddlTurnover" ResourceTitle="Turnover">
                <Field>
                    <ReboundDropDown:CompanyProspects ID="ddlTurnover" runat="server" />
                </Field>
            </ReboundUI_Form:FormField>
        </ReboundUI_Table:Form>
    </Content>
</ReboundUI_Form:DesignBase>
