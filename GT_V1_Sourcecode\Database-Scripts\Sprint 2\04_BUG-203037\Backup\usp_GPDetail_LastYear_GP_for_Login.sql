/****** Object:  StoredProcedure [dbo].[usp_GPDetail_LastYear_GP_for_Login]    Script Date: 5/22/2024 6:12:45 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

ALTER PROC [dbo].[usp_GPDetail_LastYear_GP_for_Login] 
--********************************************************************************************
--* SK 29.07.2010:
--* - Services are allocated by definition without existing on tbAllocation
--*   BackOrderQuantity - despite being on tbBackOrder - will therefore be zero
--* 
--* SK 26.04.2010:
--* - redo process for open orders to reflect reporting process elsewhere
--* - 
--* SK 22.04.2010:
--* - distinct count of orders to remove lines
--* - 
--* SK 12.04.2010:
--* - do not add shipping or freight into cost and resale for shipped sales as that is 
--*   performed in the report
--* - 
--* SK 06.04.2010:
--* - correct retrieval of duty for landed cost calculation 
--* - 
--* SK 04.11.2009:
--* - use Price from tbInvoiceLine rather than tbSalesOrderLine 
--*   which allows us to get the price of service items correctly
--* 
--* SK 22.08.2009:
--* - allow for second salesman on open orders 
--* - 
--* SK 15.07.2009:
--* - open orders do not allow for salesman2 at this time - and do not include shipping  
--* - 
--* SK 14.07.2009:
--* - recalculate LandedCost as at today's date  
--* - 
--* SK 25.06.2009:
--* - allow for currency date on SO  
--* - 
--* SK 17.06.2009:
--* - 
--* - include shipping in landed cost 
--* - ensure calculations for open(and shipped) as per reports
--********************************************************************************************
@LoginId int

AS 

DECLARE	@FromDate	datetime
DECLARE	@ToDate		datetime

SET		@FromDate	= dateadd(yy, -1, dateadd(yy, datediff(yy, 0, getdate()), 0))  
SET		@ToDate		= dateadd(ms, -3, dateadd(yy, 0, dateadd(yy, datediff(yy, 0, getdate()), 0))) 

CREATE	
TABLE	#tmpGP
(
 		SalesOrderNumber		int
,		OpenShippingCost		float
,		OpenFreightCharge		float
,		OpenLandedCost			float
,		OpenSalesValue			float
,		InvoiceNumber			int		
,		ShippedShippingCost		float
,		ShippedFreightCharge	float
,		ShippedLandedCost		float
,		ShippedSalesValue		float
,		SalesmanPercent			float
)

CREATE	
TABLE	#tmpOpenGP
(
 		SalesOrderNumber		int
,		OpenShippingCost		float
,		OpenFreightCharge		float
,		LandedCost				float
,		BuyPrice				float
,		Allocation				int
,		PONumber				int
,		GILineNo				int
,		LandedCostExAllocation	float
,		OpenSalesValue			float
,		SalesmanPercent			float
,		InStock					int   -- GA 03/12/2012
,		Pct						int
)

INSERT  
INTO	#tmpOpenGP
SELECT  SalesOrderNumber																													
,		ShippingCost
,		Freight 
,		LandedCost
,		BuyPriceInBase
,		QuantityAllocated  
,		PurchaseOrderNumber
,		GoodsInLineNo
,		LandedCost
,		GoodsSales								
,		SalesmanPercent
,		QuantityInStock
,		1
FROM    dbo.tmpOpenOrders o
WHERE   dbo.ufn_get_date_from_datetime(DatePromised) BETWEEN @FromDate AND @ToDate	
AND		Salesman				= @LoginId


-- allow for salesman2
INSERT  
INTO	#tmpOpenGP
SELECT  SalesOrderNumber																													
,		ShippingCost
,		Freight 
,		LandedCost
,		BuyPriceInBase
,		QuantityAllocated  
,		PurchaseOrderNumber
,		GoodsInLineNo
,		LandedCost
,		GoodsSales								
,		Salesman2Percent
,		QuantityInStock
,		2
FROM    dbo.tmpOpenOrders o
WHERE   dbo.ufn_get_date_from_datetime(DatePromised) BETWEEN @FromDate AND @ToDate	
AND		Salesman2			= @LoginId

----if manual stock (i.e. PO does not exist) take landed cost from Allocation 
--UPDATE	#tmpOpenGP
--SET		LandedCost	= IsNull(LandedCostExAllocation,0)
--WHERE	InStock > 0								-- GA 03/12/2012


INSERT  
INTO	#tmpGP
SELECT  tmp.SalesOrderNumber																													
,		tmp.OpenShippingCost 
,		tmp.OpenFreightCharge
,		SUM(ISNULL(IsNull(tmp.LandedCost, tmp.BuyPrice) * tmp.Allocation,0))
,		SUM(ISNULL(tmp.OpenSalesValue,0))								
,		0
,		0
,		0
,		0
,		0
,		tmp.SalesmanPercent
FROM    #tmpOpenGP tmp
Group BY SalesOrderNumber
		,OpenShippingCost
		,OpenFreightCharge
		,Pct
		,SalesmanPercent

INSERT  
INTO	#tmpGP
SELECT	0
,		0
,		0
,		0
,		0
,		InvoiceNumber
,		ShippingCost
,		Freight 
,		sum(GoodsCost)
,		sum(GoodsSales)
,		SalesmanPercent
FROM    dbo.tmpShippedOrders a
WHERE   a.InvoiceDate			BETWEEN @FromDate AND @ToDate
AND		a.Salesman				= @LoginId
GROUP BY  a.InvoiceNumber
,		a.ShippingCost
,		a.Freight
,		a.CurrencyNo
,		a.InvoiceDate
,		a.SalesmanPercent

-- allow for salesman2
INSERT  
INTO	#tmpGP
SELECT	0
,		0
,		0
,		0
,		0
,		InvoiceNumber
,		ShippingCost
,		Freight 
,		sum(GoodsCost)
,		sum(GoodsSales)
,		Salesman2Percent
FROM    dbo.tmpShippedOrders a
WHERE   a.InvoiceDate			BETWEEN @FromDate AND @ToDate
AND		a.Salesman2			= @LoginId
GROUP BY  a.InvoiceNumber
,		a.ShippingCost
,		a.Freight
,		a.CurrencyNo
,		a.InvoiceDate
,		a.Salesman2Percent

DELETE
FROM	dbo.tbGPDetail
WHERE	LoginNo	= @LoginId
AND		Period	= 'LY'

INSERT
INTO	dbo.tbGPDetail
SELECT	null
,		null
,		@LoginId
,		null
,		'LY'  
,		IsNull(sum((OpenShippingCost	/ 100) * SalesmanPercent), 0)		
,		IsNull(sum((OpenFreightCharge	/ 100) * SalesmanPercent), 0)		
,		IsNull(sum((OpenLandedCost		/ 100) * SalesmanPercent), 0)			
,		IsNull(sum((OpenSalesValue		/ 100) * SalesmanPercent), 0)			
,		(SELECT count(DISTINCT SalesOrderNumber) FROM #tmpGP WHERE SalesOrderNumber > 0) 			
,		IsNull(sum((ShippedShippingCost	/ 100) * SalesmanPercent), 0)		
,		IsNull(sum((ShippedFreightCharge/ 100) * SalesmanPercent), 0)	
,		IsNull(sum((ShippedLandedCost	/ 100) * SalesmanPercent), 0)		
,		IsNull(sum((ShippedSalesValue	/ 100) * SalesmanPercent), 0)		
,		(SELECT count(DISTINCT InvoiceNumber) FROM #tmpGP WHERE InvoiceNumber > 0) 			
FROM    #tmpGP

DROP	TABLE	#tmpGP
DROP	TABLE	#tmpopenGP
