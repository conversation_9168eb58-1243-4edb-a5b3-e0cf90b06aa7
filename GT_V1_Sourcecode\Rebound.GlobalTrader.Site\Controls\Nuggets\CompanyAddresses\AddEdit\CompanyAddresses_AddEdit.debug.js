///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//Marker     Changed by      Date         Remarks
//[001]      Vinay           11/06/2012   This need to Add Incoterms field in company section
//[002]      Vinay           13/03/2014   EMS Ref No: 104
//[003]      Aashu Singh     13-Sep-2018    [REB-12820]:Provision to add Global Security on Contact Section
//[004]      Ravi Bhushan    31-03-2024     [RP-1224]
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.CompanyAddresses_AddEdit = function(element) { 
	Rebound.GlobalTrader.Site.Controls.Forms.CompanyAddresses_AddEdit.initializeBase(this, [element]);
	this._intCompanyAddressID = -1;
	this._intCompanyID = -1;
	this._blnCanEditTax = false;
    this._vatNo = "";
};

Rebound.GlobalTrader.Site.Controls.Forms.CompanyAddresses_AddEdit.prototype = {

    get_intCompanyAddressID: function() { return this._intCompanyAddressID; }, set_intCompanyAddressID: function(value) { if (this._intCompanyAddressID !== value) this._intCompanyAddressID = value; },
    get_intCompanyID: function() { return this._intCompanyID; }, set_intCompanyID: function(value) { if (this._intCompanyID !== value) this._intCompanyID = value; },
    get_strTitleEdit: function() { return this._strTitleEdit; }, set_strTitleEdit: function(value) { if (this._strTitleEdit !== value) this._strTitleEdit = value; },
    get_strTitleAdd: function() { return this._strTitleAdd; }, set_strTitleAdd: function(value) { if (this._strTitleAdd !== value) this._strTitleAdd = value; },
    get_strExplanationEdit: function() { return this._strExplanationEdit; }, set_strExplanationEdit: function(value) { if (this._strExplanationEdit !== value) this._strExplanationEdit = value; },
    get_strExplanationAdd: function () { return this._strExplanationAdd; }, set_strExplanationAdd: function (value) { if (this._strExplanationAdd !== value) this._strExplanationAdd = value; },
    
    initialize: function() {
        Rebound.GlobalTrader.Site.Controls.Forms.CompanyAddresses_AddEdit.callBaseMethod(this, "initialize");
        this.addShown(Function.createDelegate(this, this.formShown));
        this.addModeChanged(Function.createDelegate(this, this.changeTitleOnMode));
    },

    dispose: function() {
        if (this.isDisposed) return;
        if (this._ctlAddress) this._ctlAddress.dispose();
        this._ctlAddress = null;
        this._intCompanyAddressID = null;
        this._intCompanyID = null;
        this._strTitleEdit = null;
        this._strTitleAdd = null;
        this._strExplanationEdit = null;
        this._strExplanationAdd = null;
        this._blnCanEditTax = null;
        Rebound.GlobalTrader.Site.Controls.Forms.CompanyAddresses_AddEdit.callBaseMethod(this, "dispose");
    },

    formShown: function() {
        if (this._blnFirstTimeShown) {
            //alert(this._blnCanEditTax + '--AddEdit');
            this._strPathToData = "controls/Nuggets/CompanyAddresses";
            this._strDataObject = "CompanyAddresses";
            this.addSaveClick(Function.createDelegate(this, this.saveClicked));
            this.addCancelClick(Function.createDelegate(this, this.cancelClicked));
            this._ctlAddress = $find(this.getField("ctlAddress").ID);
            this._ctlAddress._ctlRelatedForm = this;
            
        }
        this.resetFormFields();
        //
        this.setFormFieldsToDefaults();
        //[003] start
        
        this.getFieldControl("ctlCountry")._intGlobalLoginClientNo = this._globalLoginClientNo;
        this.getFieldControl("ctlShipVia")._intGlobalLoginClientNo = this._globalLoginClientNo;
        this.getFieldControl("ctlTax")._intGlobalLoginClientNo = this._globalLoginClientNo;
        //[003] end
        this.getFieldDropDownData("ctlLabelType");
        this.getFieldDropDownData("ctlCountry");
        this.getFieldDropDownData("ctlShipVia");
        //ESMS #14
       // this.showField("ctlTax", this._blnCanEditTax);
        this.getFieldDropDownData("ctlTax");
       // this.allowEditingTax(this._blnCanEditTax);
        //[001] code start
        this.showField("ctlIncoterm", true);
        this.getFieldDropDownData("ctlIncoterm");
        //[001] code end
        //[002] code start

        //this.showField("ctlVatNo", this._blnCanEditTax);
        //this.allowEditingVat(this._blnCanEditTax);

        this.getFieldDropDownData("ctlRegion");
        this.getFieldDropDownData("ctlDivisionHeader");
        //[002] code end

        if (this._mode == "ADD") {
            this.showField("ctlTax", true);
            this.allowEditingTax(true);
            this.showField("ctlVatNo", true);
            this.allowEditingVat(true);
        }
        else {
            this.showField("ctlTax", this._blnCanEditTax);
            this.allowEditingTax(this._blnCanEditTax);
            this.showField("ctlVatNo", this._blnCanEditTax);
            this.allowEditingVat(this._blnCanEditTax);
        }

       
    },

    saveClicked: function() {
        this.resetFormFields();
        if (!this.validateForm()) return;
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData(this._strPathToData);
        obj.set_DataObject(this._strDataObject);
        if (this._mode == "ADD") {
            obj.set_DataAction("AddNew");
        } else {
            obj.set_DataAction("SaveEdit");
            obj.addParameter("ID", this._intCompanyAddressID);
        }
        obj.addParameter("CMNo", this._intCompanyID);
        obj.addParameter("Name", this.getFieldValue("ctlAddressName"));
        obj.addParameter("Line1", this.getFieldValue("ctlLine1"));
        obj.addParameter("Line2", this.getFieldValue("ctlLine2"));
        obj.addParameter("Line3", this.getFieldValue("ctlLine3"));
        obj.addParameter("County", this.getFieldValue("ctlCounty"));
        obj.addParameter("City", this.getFieldValue("ctlTown"));
        obj.addParameter("State", this.getFieldValue("ctlState"));
        obj.addParameter("Country", this.getFieldValue("ctlCountry"));
        obj.addParameter("Zip", this.getFieldValue("ctlPostcode"));
        obj.addParameter("Notes", this.getFieldValue("ctlNotes"));
        obj.addParameter("ShipViaNo", this.getFieldValue("ctlShipVia"));
        obj.addParameter("ShipViaAccount", this.getFieldValue("ctlShipViaAccount"));
        //ESMS #14
        obj.addParameter("TaxbyAddress", this.getFieldValue("ctlTax"));
        //
        //[001] code start
        obj.addParameter("IncotermNo", this.getFieldValue("ctlIncoterm"));
        //[001] code end
        obj.addParameter("ShippingNotes", this.getFieldValue("ctlShippingNotes"));
        //[002] code start
        obj.addParameter("VatNo", this.getFieldValue("ctlVatNo"));
        //[002] code end
        obj.addParameter("RNotes", this.getFieldValue("ctlRecieveNotes"));
        obj.addParameter("Region", this.getFieldValue("ctlRegion"));
        obj.addParameter("DivisionHeaderNo", this.getFieldValue("ctlDivisionHeader"));
        obj.addParameter("LabelTypeNo", this.getFieldValue("ctlLabelType"));
        

        

        obj.addDataOK(Function.createDelegate(this, this.saveAddEditComplete));
        obj.addError(Function.createDelegate(this, this.saveError));
        obj.addTimeout(Function.createDelegate(this, this.saveError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
        this.onSave();
    },

    saveError: function(args) {
        this._strErrorMessage = args._errorMessage;
        this.onSaveError();
    },

    saveAddEditComplete: function(args) {
        if (args._result.Result == true) {
            this.onSaveComplete();
        } else {
            this.saveError(args);
        }
    },

    saveEditComplete: function(args) {
        if (args._result.Result == true) {
            this.onSaveComplete();
        } else {
            this.saveError(args);
        }
    },

    validateForm: function() {
        var blnOK = this.autoValidateFields();
        if (!this._ctlAddress.validateFields()) blnOK = false;
        if (!blnOK) this.showError(true);
        return blnOK;
    },

    cancelClicked: function() {
        this.onCancel();
    },

    changeTitleOnMode: function() {
        switch (this._mode) {
            case "ADD":
                this.changeTitle(this._strTitleAdd);
                this.changeExplanation(this._strExplanationAdd);
                break;
            case "EDIT":
                this.changeTitle(this._strTitleEdit);
                this.changeExplanation(this._strExplanationEdit);
                break;
        }
    },

    allowEditingTax: function(bln) {
        this.showField("ctlTax", bln);
        this.showField("ctlTax_Label", !bln);
    },
    allowEditingVat: function(bln) {
        this.showField("ctlVatNo", bln);
        this.showField("ctlVatNo_Label", !bln);
    },
    
};

Rebound.GlobalTrader.Site.Controls.Forms.CompanyAddresses_AddEdit.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.CompanyAddresses_AddEdit", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
