SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE OR ALTER PROCEDURE [dbo].[usp_update_BOMManager_Items_ByHub]                                                            
--******************************************************************************************                                                    
--New = 1, Open = 2,RPQ = 3, PartialReleased = 4,  Released = 5                                                            
--******************************************************************************************                                                            
    @BOMManagerID int                                                            
  , @UpdatedBy int = NULL                            
  , @CustomerRequirementIds varchar(max)                
  , @ReqType int                
  , @NoBidNotes nvarchar(max) =NULL        
  , @AutoSourceId varchar(max) = null    
  , @RowsAffected int = NULL OUTPUT                                                            
AS                                                           
BEGIN                                                 
Declare @Count int=0,@ReqCount int=0,@Max int=0,@CustomerRequirementId int=0, @BuyandSellPriceCount int =0, @BOMStatus int = 0                 
                
Create table #tempCid (CustomerRequirementId int)                
insert into #tempCid                
select distinct * from SplitString(@CustomerRequirementIds,'|')                
                
delete from #tempCid where isnull(CustomerRequirementId ,0)=0       
    
    
Create table #tempASid (AutoSouceId int)                
insert into #tempASid                
select distinct * from SplitString(@AutoSourceId,'|')                
                
delete from #tempASid where isnull(AutoSouceId ,0)=0       
             
   --select * from #tempCid    
   --select * from #tempASid    
    
    
BEGIN TRAN                
BEGIN TRY                
                
-- to Release the items from reqstatus 3=>4                
if(@ReqType =1)                
BEGIN                
--if exists (select BOMManagerNo from tbCustomerRequirement where BOMManagerNo = @BOMManagerID AND   POHubReleaseBy IS NULL and REQStatus = 3)                 
--Begin                
  UPDATE  cr                  
    SET     POHubReleaseBy = @UpdatedBy                                                           
          , DatePOHubRelease = CURRENT_TIMESTAMP                                                          
          , DLUP = CURRENT_TIMESTAMP                 
    from  dbo.tbCustomerRequirement cr join #tempCid tbtemp on cr.CustomerRequirementId=tbtemp.CustomerRequirementId                
    WHERE   cr.BOMManagerNo = @BOMManagerID -- And cr.CustomerRequirementId= @CustomerRequirementId                
   and cr.POHubReleaseBy IS NULL and cr.REQStatus = 3                  
                 
  delete a from  tbsourcingresult a           
 join tbautosource b on a.AutoSourceID = b.sourceid and a.CustomerRequirementNo = b.CustomerRequirementId          
 join #tempCid tbtemp on a.CustomerRequirementNo = tbtemp.CustomerRequirementId     
 join #tempASid tbtempAS on a.AutoSourceID = tbtempAS.AutoSouceId    
 where b.BOMManagerNo = @BOMManagerID  -- and a.IsSoftDelete=1      
     
 update a  set IsReleased=1 from  tbautosource  a      
 join #tempCid tbtemp on a.CustomerRequirementId = tbtemp.CustomerRequirementId     
 join #tempASid tbtempAS on a.SourceId = tbtempAS.AutoSouceId    
 where a.BOMManagerNo = @BOMManagerID  -- and a.IsSoftDelete=1      
         
 Declare @ClientCurrencyNo int,@ClientNo int      
 select @ClientNo=ClientNo from tbBomManager where BomManagerId = @BOMManagerID      
 select  @ClientCurrencyNo=CurrencyNo from tbClient where ClientId = @ClientNo      
 --select @ClientCurrencyNo      
       
      
      
 INSERT INTO [dbo].[tbSourcingResult]                
   ([CustomerRequirementNo],[SourcingTable],[FullPart],[Part],[ManufacturerNo],[DateCode],[ProductNo],[PackageNo],[Quantity],[Price],[CurrencyNo],[OriginalEntryDate]                
   ,[Salesman],[SupplierNo],[UpdatedBy],[DLUP],[ROHS],[OfferStatusNo],[OfferStatusChangeDate],[OfferStatusChangeLoginNo],[Notes],[POHubCompanyNo],[SupplierPrice]                
   ,[ClientCompanyNo],[IsReleased],[Closed],[EstimatedShippingCost],[ClientCurrencyNo],[SupplierManufacturerName],[SupplierDateCode],[SupplierPackageType],[SupplierProductType]                
   ,[SupplierMOQ],[SupplierTotalQSA],[SupplierLTB],[SupplierNotes],[DeliveryDate],[SPQ],[LeadTime],[ROHSStatus],[FactorySealed],[MSL],[RegionNo],[IsSoCreated],[SourceRef]                
   ,[Recalled],[Buyer],[ActualPrice],[ActualCurrencyNo],[ExchangeRate],[LinkMultiCurrencyNo] ,[MSLLevelNo],[SupplierWarranty],[TestRecommended],[IsImageAvailable]                
   ,[PriorityNo],[IHSCountryOfOriginNo],[RefIdHK],[RefIdHKSourcingTableItemNo],[ReReleased],[IsSoftDelete],[PartWatchMatch],[ClientNo],[PartWatchMatchHUBIPO],[IsBOMManager]         
   ,[AutoSourceID] ,[IsPrimarySourcing]                 
   )                
 (select                 
   cr.CustomerRequirementId ,ar.VendorCategory ,ar.FullPart ,ar.Part ,ar.ManufacturerNo ,ar.DateCode ,ar.ProductNo ,ar.PackageNo ,ar.ADJQty       
   ,dbo.ufn_convert_currency_value(ar.Resale,      
           (SELECT l.SupplierCurrencyNo as ClientCurrencyNo              
         from tbCurrency c       
         left join tbLinkMultiCurrency l   on l.GlobalCurrencyNo = c.GlobalCurrencyNo               
         WHERE  l.ClientNo = @ClientNo       
         and c.ClientNo = 114       
         AND c.CurrencyId = ar.CurrencyNo),      
         @ClientCurrencyNo,      
           GETDATE())      
   ,@ClientCurrencyNo      
   ,ar.OriginalEntryDate ,cr.Salesman ,ar.SupplierNo ,@UpdatedBy ,GetDate() ,ar.ROHS ,ar.OfferStatusNo ,ar.OfferStatusChangeDate ,ar.OfferStatusChangeLoginNo ,ar.Notes                
   ,ar.POHubCompanyNo ,ar.SupplierPrice ,ar.ClientCompanyNo ,ar.IsReleased ,ar.Closed ,ar.EstimatedShippingCost ,ar.ClientCurrencyNo ,null ,ar.SupplierDateCode                
   ,ar.SupplierPackageType ,ar.SupplierProductType ,ar.SupplierMOQ ,ar.SupplierTotalQSA ,ar.SupplierLTB ,ar.SupplierNotes ,ar.DeliveryDate ,ar.SPQ ,null ,ar.ROHSStatus                
   ,ar.FactorySealed ,ar.MSL ,ar.RegionNo ,ar.IsSoCreated ,ar.SourceRef ,null ,null ,ar.Resale ,ar.CurrencyNo ,null ,null ,ar.MSLLevelNo ,ar.SupplierWarranty                
   ,ar.TestRecommended ,ar.IsImageAvailable ,ar.PriorityNo ,ar.IHSCountryOfOriginNo ,null ,null ,null ,null ,null ,ar.ClientNo ,null ,1 ,ar.SourceId                 
   ,case when (select count(1) from tbAutoSource tbasr where tbasr.CustomerRequirementId=cr.CustomerRequirementId and isnull(tbasr.isdeleted,0)=0 and IsReleased=1) >1    
   then 0 else 1 end                 
 from tbAutoSource ar                
 join tbCustomerRequirement cr on cr.CustomerRequirementId = ar.CustomerRequirementId                
 join #tempCid tbtemp on cr.CustomerRequirementId = tbtemp.CustomerRequirementId         
 join #tempASid tbtempAS on ar.SourceId = tbtempAS.AutoSouceId    
 where ar.BOMManagerNo = @BOMManagerID  and isnull(ar.isdeleted,0)=0          
 )                  
          
        
   UPDATE cr  set REQStatus = 4                 
   from tbCustomerRequirement cr join #tempCid tbtemp on cr.CustomerRequirementId=tbtemp.CustomerRequirementId                
   where   cr.BOMManagerNo = @BOMManagerID --and cr.CustomerRequirementId = @CustomerRequirementId                 
   and cr.REQStatus = 3                    
                
   UPDATE tbBOMManager                                                     
   SET ReleaseBy=@UpdatedBy,                
   DateRelease=CURRENT_TIMESTAMP,                
   [Status]= case when (select COUNT(CustomerRequirementId) from tbCustomerRequirement where BOMManagerNo = @BOMManagerID AND   POHubReleaseBy IS NULL and REQStatus < 4) >0                 
   then 4 else 5 end                 
   WHERE BOMManagerId=@BOMManagerID               
           
                
--End                       
END                
ELSE IF (@ReqType=2)                
BEGIN                
                       
 UPDATE tbBOMManager                          
  SET    ReleaseBy = NULL,                          
  DateRelease = NULL,                          
  [status] = case when (select COUNT(CustomerRequirementId) from tbCustomerRequirement where BOMManagerNo = @BOMManagerID AND   POHubReleaseBy IS NULL and REQStatus = 3) >1                 
   then 3 else 4 end                         
 WHERE  BOMManagerId = @BOMManagerID                  
                  
 UPDATE tbsrc                 
  Set IsReleased = 0,Recalled=1                 
 from tbSourcingResult  tbsrc     
 join #tempCid tbtemp on tbsrc.CustomerRequirementNo = tbtemp.CustomerRequirementId       
 join #tempASid tbtempAS on tbsrc.AutoSourceID = tbtempAS.AutoSouceId    
 where tbsrc.IsBOMManager = 1                        
          
  update a set a.IsSoftDelete = 1,a.IsPrimarySourcing= 0 from  tbsourcingresult a           
 join tbautosource b on a.AutoSourceID = b.sourceid and a.CustomerRequirementNo = b.CustomerRequirementId          
 join #tempCid tbtemp on a.CustomerRequirementNo = tbtemp.CustomerRequirementId     
 join #tempASid tbtempAS on a.AutoSourceID = tbtempAS.AutoSouceId    
 where b.BOMManagerNo = @BOMManagerID   and isnull(b.isdeleted,0)=0        
    
     
 update a  set IsReleased=null from  tbautosource  a      
 join #tempCid tbtemp on a.CustomerRequirementId = tbtemp.CustomerRequirementId     
 join #tempASid tbtempAS on a.SourceId = tbtempAS.AutoSouceId    
 where a.BOMManagerNo = @BOMManagerID  -- and a.IsSoftDelete=1      
         
         
 -- delete a from  tbsourcingresult a           
 --join tbautosource b on a.AutoSourceID = b.sourceid and a.CustomerRequirementNo = b.CustomerRequirementId          
 --join #tempCid tbtemp on a.CustomerRequirementNo = tbtemp.CustomerRequirementId          
 --where b.BOMManagerNo = @BOMManagerID   and isnull(b.isdeleted,0)=0         
     
select a.CustomerRequirementid into #tempDelCid    
from tbautosource a join #tempCid b on a.CustomerRequirementId=b.CustomerRequirementId    
where bommanagerno = @BOMManagerID and isnull(IsDeleted,0)=0 and IsReleased = 1     
--and CustomerRequirementid in (4187771,4187772)    
group by a.CustomerRequirementid    
    
 -- to set primary sourcing if there is only one item.    
 select SourcingResultId into #tempPrimarySourceId from tbsourcingresult where customerrequirementno in  (    
   select a.CustomerRequirementNo  from  tbsourcingresult a           
 join tbautosource b on a.AutoSourceID = b.sourceid and a.CustomerRequirementNo = b.CustomerRequirementId          
 join #tempCid tbtemp on a.CustomerRequirementNo = tbtemp.CustomerRequirementId     
 where b.BOMManagerNo = @BOMManagerID   and isnull(b.isdeleted,0)=0 and isnull(a.IsSoftDelete,0)=0    
 group by a.CustomerRequirementNo having count(1) =1 ) and isnull(IsSoftDelete,0)=0    
    
  update a set a.IsPrimarySourcing = 1 from  tbsourcingresult a join #tempPrimarySourceId b     
 on a.SourcingResultId = b.SourcingResultId where a.IsBOMManager = 1     
    -- to set primary sourcing if there is only one item.            
    
delete a  from #tempCid a join #tempDelCid b on a.CustomerRequirementId = b.CustomerRequirementId    
    
 UPDATE cr                           
  SET    POHubReleaseBy = NULL,                          
  DatePOHubRelease = NULL,                          
  DLUP = CURRENT_TIMESTAMP ,                
  REQStatus = 3                
 from dbo.tbCustomerRequirement cr     
 join #tempCid tbtemp on cr.CustomerRequirementId = tbtemp.CustomerRequirementId                
 WHERE  cr.BOMManagerNo  = @BOMManagerID  and REQStatus = 4                       
     
    
 --declare @offerReleasedCount int    
 --select @offerReleasedCount = count(1) from tbautosource  tbas    
 --join #tempCid tbtemp on tbas.CustomerRequirementId = tbtemp.CustomerRequirementId                
 --where bommanagerno = @BOMManagerID and isnull(IsDeleted,0)=0 and IsReleased = 1    
 --if(@offerReleasedCount <1)   
 --begin    
 --  UPDATE cr                           
 -- SET    POHubReleaseBy = NULL,                          
 -- DatePOHubRelease = NULL,                          
 -- DLUP = CURRENT_TIMESTAMP ,                
 -- REQStatus = 3                
 --from dbo.tbCustomerRequirement cr     
 --join #tempCid tbtemp on cr.CustomerRequirementId = tbtemp.CustomerRequirementId                
 --WHERE cr.BOMManagerNo  = @BOMManagerID  and REQStatus = 4                       
     
 --end    
    
    
END                
                
ELSE IF(@ReqType = 3)                
BEGIN                
UPDATE  cr                                    
    SET                                        
           IsNoBid = 1                                     
          , DLUP = CURRENT_TIMESTAMP                      
    ,NoBidNotes =@NoBidNotes                 
 --,status = 4                
 from dbo.tbCustomerRequirement  cr join #tempCid tbtemp on cr.CustomerRequirementId=tbtemp.CustomerRequirementId                
    WHERE   cr.BOMManagerNo = @BOMManagerID  AND REQStatus < 3                
                
 UPDATE tbBOMManager                          
  SET    ReleaseBy = NULL,                          
  DateRelease = NULL,                          
  [status] = case when (select COUNT(CustomerRequirementId) from tbCustomerRequirement where BOMManagerNo = @BOMManagerID AND   POHubReleaseBy IS NULL and REQStatus < 3) >1                 
   then 4 else 5 end                         
 WHERE  BOMManagerId = @BOMManagerID                  
                
END                
                
ELSE IF(@ReqType = 4)                
BEGIN                
 UPDATE  cr                                     
    SET                                        
     IsNoBid = NULL                  
    ,NoBidNotes = NULL                  
          ,DLUP = CURRENT_TIMESTAMP                        
          ,UpdatedBy= @UpdatedBy                  
    --, status= 2                
    from dbo.tbCustomerRequirement  cr join #tempCid tbtemp on cr.CustomerRequirementId = tbtemp.CustomerRequirementId                
    WHERE   cr.BOMManagerNo = @BOMManagerID  and cr.IsNoBid = 1                  
                 
          UPDATE tbBOMManager                          
  SET    ReleaseBy = NULL,                          
  DateRelease = NULL,                          
  [status] = case when (select COUNT(CustomerRequirementId) from tbCustomerRequirement where BOMManagerNo = @BOMManagerID AND   POHubReleaseBy IS NULL and REQStatus < 3) >1                 
   then 4 else 5 end                         
 WHERE  BOMManagerId = @BOMManagerID                  
                
END                
                
ELSE                 
BEGIN                
select 'Success' as 'Status','Invalid Request Type. No item updated.' as 'Message'                
END                
                
COMMIT TRAN                
select 'Success' as 'Status','Items Updated' as 'Message'                     
END TRY                
BEGIN CATCH                
ROLLBACK TRAN                          
select 'Fail' as 'Status',ERROR_MESSAGE() as 'Message'                
END CATCH                
                
drop table #tempCid                
END             
        
GO


