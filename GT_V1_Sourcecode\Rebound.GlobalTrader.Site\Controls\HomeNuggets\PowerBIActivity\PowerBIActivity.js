Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.HomeNuggets");Rebound.GlobalTrader.Site.Controls.HomeNuggets.PowerBIActivity=function(n){Rebound.GlobalTrader.Site.Controls.HomeNuggets.PowerBIActivity.initializeBase(this,[n]);this._tblPowerBIActivity=null};Rebound.GlobalTrader.Site.Controls.HomeNuggets.PowerBIActivity.prototype={get_tblPowerBIActivity:function(){return this._tblPowerBIActivity},set_tblPowerBIActivity:function(n){this._tblPowerBIActivity!==n&&(this._tblPowerBIActivity=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.HomeNuggets.PowerBIActivity.callBaseMethod(this,"initialize");this.addRefreshEvent(Function.createDelegate(this,this.getData))},dispose:function(){this.isDisposed||(this._tblPowerBIActivity&&this._tblPowerBIActivity.dispose(),this._tblPowerBIActivity=null,Rebound.GlobalTrader.Site.Controls.HomeNuggets.PowerBIActivity.callBaseMethod(this,"dispose"))},setupLoadingState:function(){this._tblPowerBIActivity.show(!1);Rebound.GlobalTrader.Site.Controls.HomeNuggets.PowerBIActivity.callBaseMethod(this,"setupLoadingState")},getData:function(){this.setupLoadingState();this._tblPowerBIActivity.clearTable();var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/HomeNuggets/PowerBIActivity");n.set_DataObject("PowerBIActivity");n.set_DataAction("GetData");n._intTimeOutMiliseconds=1;n.addParameter("OtherLoginID",this._intLoginID_Other);n.addParameter("rowcount",this._intRowCount);n.addDataOK(Function.createDelegate(this,this.getDataComplete));n.addError(Function.createDelegate(this,this.homeNuggetDataError));n.addTimeout(Function.createDelegate(this,this.homeNuggetDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getDataComplete:function(n){var i=n._result,r,t,u;for(this.showNoneFoundOrContent(i.Count),r=0;r<i.Items.length;r++)t=i.Items[r],u=[$R_FN.setCleanTextValue(t.LoginName),$R_FN.setCleanTextValue(t.LastVisited),$R_FN.setCleanTextValue(t.ReportName)],this._tblPowerBIActivity.addRow(u,null),t=null;this._tblPowerBIActivity.show(i.Count>0);this.hideLoading()}};Rebound.GlobalTrader.Site.Controls.HomeNuggets.PowerBIActivity.registerClass("Rebound.GlobalTrader.Site.Controls.HomeNuggets.PowerBIActivity",Rebound.GlobalTrader.Site.Controls.HomeNuggets.Base,Sys.IDisposable);