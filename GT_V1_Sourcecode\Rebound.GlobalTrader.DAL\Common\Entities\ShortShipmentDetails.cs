﻿
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Rebound.GlobalTrader.DAL
{
    public class ShortShipmentDetails
    {
        #region Constructors
        public ShortShipmentDetails() { }
        #endregion

        #region Properties
        /// <summary>
        /// ShortShipmentID
        /// </summary>
        public System.Int32? ShortShipmentId { get; set; }
        /// <summary>
        /// Supplier
        /// </summary>
        public System.String Supplier { get; set; }
        /// <summary>
        /// PurchaseOrderNo
        /// </summary>
        public System.Int32? PurchaseOrderNo { get; set; }
        /// <summary>
        /// IPONo
        /// </summary>
        public System.Int32? IPONo { get; set; }
        /// <summary>
        /// Salesman
        /// </summary>
        public System.String Salesman { get; set; }
        /// <summary>
        /// SalesmanId
        /// </summary>
        public System.Int32? SalesmanId { get; set; }
        /// <summary>
        /// DeliveryNoteNo
        /// </summary>
        public System.String Reference { get; set; }
        /// <summary>
        /// GINoteNo
        /// </summary>
        public System.Int32? GoodsInNo { get; set; }
        /// <summary>
        /// DateReceived
        /// </summary>
        public System.DateTime? DateReceived { get; set; }
        /// <summary>
        /// Raisedby
        /// </summary>
        public System.String Raisedby { get; set; }
        /// <summary>
        /// BuyerId
        /// </summary>
        public System.Int32? BuyerId { get; set; }
        /// <summary>
        /// Buyer
        /// </summary>
        public System.String Buyer { get; set; }
        /// <summary>
        /// PartNo
        /// </summary>
        public System.String PartNo { get; set; }
        /// <summary>
        /// ManufacturerName
        /// </summary>
        public System.String ManufacturerName { get; set; }
        /// <summary>
        /// QuantityOrdered
        /// </summary>
        public System.Int32? QuantityOrdered { get; set; }
        /// <summary>
        /// QuantityAdvised
        /// </summary>
        public System.Int32? QuantityAdvised { get; set; }
        /// <summary>
        /// QuantityReceived
        /// </summary>
        public System.Int32? QuantityReceived { get; set; }
        /// <summary>
        /// ShortageQuantity
        /// </summary>
        public System.Int32? ShortageQuantity { get; set; }
        /// <summary>
        /// ShortValue
        /// </summary>
        public System.Double? ShortageValue { get; set; }
        /// <summary>
        /// IsShortageRefundIssue
        /// </summary>
        public System.Boolean? IsShortageRefundIssue { get; set; }
        /// <summary>
        /// ShortageRefundIssue
        /// </summary>
        public System.String ShortageRefundIssue { get; set; }
        /// <summary>
        /// Completedby
        /// </summary>
        public System.String Completedby { get; set; }
        /// <summary>
        /// CompletedDate
        /// </summary>
        public System.DateTime? CompletedDate { get; set; }
        /// <summary>
        /// Status
        /// </summary>
        public System.String Status { get; set; }
        /// <summary>
        /// StatusId
        /// </summary>
        public System.Int32? StatusId { get; set; }
        /// <summary>
        /// RowNum
        /// </summary>
        public System.Int32? RowNum { get; set; }
        /// <summary>
        /// RowCnt
        /// </summary>
        public System.Int32? RowCnt { get; set; }
        /// <summary>
        /// IsStageTwoUpdated
        /// </summary>
        public System.Boolean IsStageTwoUpdated { get; set; }
        /// <summary>
        /// RaisedbyId
        /// </summary>
        public System.Int32? RaisedbyId { get; set; }
        /// <summary>
        /// GoodsInId
        /// </summary>
        public System.Int32? GoodsInId { get; set; }
        /// <summary>
        /// ClientNo
        /// </summary>
        public System.Int32? ClientNo { get; set; }
        /// <summary>
        /// PurchaseOrderId
        /// </summary>
        public System.Int32? PurchaseOrderId { get; set; }
        /// <summary>
        /// DebitNoteNo
        /// </summary>
        public System.Int32? DebitNoteNo { get; set; }
        /// <summary>
        /// DebitNumber
        /// </summary>
        public System.Int32? DebitNumber { get; set; }
        /// <summary>
        /// IsCancel
        /// </summary>
        public System.Boolean IsCancel { get; set; }
        /// <summary>
        /// IsClosed
        /// </summary>
        public System.Boolean IsClosed { get; set; }
        /// <summary>
        /// IsDebitNoteExists
        /// </summary>
        public System.Boolean IsDebitNoteExists { get; set; }
        /// <summary>
        /// IsPOHub
        /// </summary>
        public System.Boolean? IsPOHub { get; set; }
        /// <summary>
        /// ManufacturerId
        /// </summary>
        public System.Int32? ManufacturerId { get; set; }
        /// <summary>
        /// SupportTeamMemberNo
        /// </summary>
        public System.Int32? SupportTeamMemberNo { get; set; }
        /// <summary>
        /// CurrencyNo
        /// </summary>
        public System.Int32? CurrencyNo { get; set; }
        /// <summary>
        /// DebitIds
        /// </summary>
        public System.String DebitIds { get; set; }
        /// <summary>
        /// DebitNumbers
        /// </summary>
        public System.String DebitNumbers { get; set; }
        /// <summary>
        /// InternalPurchaseOrderId
        /// </summary>
        public int? InternalPurchaseOrderId { get; set; }
        /// <summary>
        /// InternalPurchaseOrderNo
        /// </summary>
        public int? InternalPurchaseOrderNo { get; set; }
        /// <summary>
        /// CurrencyCode
        /// </summary>
        public System.String CurrencyCode { get; set; }
        /// <summary>
        /// ClientCurrencyCode
        /// </summary>
        public System.String ClientCurrencyCode { get; set; }
        public System.String CompanyAdvisoryNotes { get; set; }
        public System.String MfrAdvisoryNotes { get; set; }
        #endregion
    }
}
