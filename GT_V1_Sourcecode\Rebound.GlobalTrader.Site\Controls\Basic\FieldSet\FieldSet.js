Type.registerNamespace("Rebound.GlobalTrader.Site.Controls");Rebound.GlobalTrader.Site.Controls.FieldSet=function(n){Rebound.GlobalTrader.Site.Controls.FieldSet.initializeBase(this,[n]);this._intCount=0};Rebound.GlobalTrader.Site.Controls.FieldSet.prototype={get_pnlOuter:function(){return this._pnlOuter},set_pnlOuter:function(n){this._pnlOuter!==n&&(this._pnlOuter=n)},get_lblCount:function(){return this._lblCount},set_lblCount:function(n){this._lblCount!==n&&(this._lblCount=n)},get_hypShowHide:function(){return this._hypShowHide},set_hypShowHide:function(n){this._hypShowHide!==n&&(this._hypShowHide=n)},get_blnIsRolledUp:function(){return this._blnIsRolledUp},set_blnIsRolledUp:function(n){this._blnIsRolledUp!==n&&(this._blnIsRolledUp=n)},get_pnlContent:function(){return this._pnlContent},set_pnlContent:function(n){this._pnlContent!==n&&(this._pnlContent=n)},get_pnlLoading:function(){return this._pnlLoading},set_pnlLoading:function(n){this._pnlLoading!==n&&(this._pnlLoading=n)},get_pnlError:function(){return this._pnlError},set_pnlError:function(n){this._pnlError!==n&&(this._pnlError=n)},get_lblError:function(){return this._lblError},set_lblError:function(n){this._lblError!==n&&(this._lblError=n)},get_pnlNoData:function(){return this._pnlNoData},set_pnlNoData:function(n){this._pnlNoData!==n&&(this._pnlNoData=n)},get_lblLoadingTop:function(){return this._lblLoadingTop},set_lblLoadingTop:function(n){this._lblLoadingTop!==n&&(this._lblLoadingTop=n)},get_hypRefresh:function(){return this._hypRefresh},set_hypRefresh:function(n){this._hypRefresh!==n&&(this._hypRefresh=n)},addShown:function(n){this.get_events().addHandler("Shown",n)},removeShown:function(n){this.get_events().removeHandler("Shown",n)},onShown:function(){var n=this.get_events().getHandler("Shown");n&&n(this,Sys.EventArgs.Empty)},addRefresh:function(n){this.get_events().addHandler("Refresh",n)},removeRefresh:function(n){this.get_events().removeHandler("Refresh",n)},onRefresh:function(){var n=this.get_events().getHandler("Refresh");n&&n(this,Sys.EventArgs.Empty)},initialize:function(){Rebound.GlobalTrader.Site.Controls.FieldSet.callBaseMethod(this,"initialize");this._hypShowHide&&$addHandler(this._hypShowHide,"click",Function.createDelegate(this,this.toggleRollUp));this._hypRefresh&&$addHandler(this._hypRefresh,"click",Function.createDelegate(this,this.onRefresh))},dispose:function(){this.isDisposed||(this.get_element()&&$clearHandlers(this.get_element()),this._hypShowHide&&$clearHandlers(this._hypShowHide),this._hypRefresh&&$clearHandlers(this._hypRefresh),this._pnlOuter=null,this._lblCount=null,this._hypShowHide=null,this._pnlContent=null,this._pnlLoading=null,this._pnlError=null,this._lblError=null,this._pnlNoData=null,this._lblLoadingTop=null,this._hypRefresh=null,this._intCount=null,Rebound.GlobalTrader.Site.Controls.FieldSet.callBaseMethod(this,"dispose"),this.isDisposed=!0)},toggleRollUp:function(){this.rollUp(!this._blnIsRolledUp)},rollUp:function(n){this._blnIsRolledUp=n;n?Sys.UI.DomElement.addCssClass(this._pnlOuter,"fieldSetCollapsed"):(Sys.UI.DomElement.removeCssClass(this._pnlOuter,"fieldSetCollapsed"),this.onShown())},showContent:function(n){n?($R_FN.showElement(this._pnlContent,!0),$R_FN.showElement(this._pnlError,!1),$R_FN.showElement(this._pnlLoading,!1),$R_FN.showElement(this._lblLoadingTop,!1),$R_FN.showElement(this._pnlNoData,!1)):$R_FN.showElement(this._pnlContent,!1)},showLoading:function(n){n?($R_FN.showElement(this._lblLoadingTop,!0),$R_FN.showElement(this._pnlLoading,!0),$R_FN.showElement(this._pnlContent,!1),$R_FN.showElement(this._pnlError,!1),$R_FN.showElement(this._pnlNoData,!1),this.resetCount()):($R_FN.showElement(this._lblLoadingTop,!1),$R_FN.showElement(this._pnlLoading,!1))},showLoadingTop:function(n){$R_FN.showElement(this._lblLoadingTop,n)},showError:function(n,t){n?($R_FN.showElement(this._pnlError,!0),$R_FN.showElement(this._pnlNoData,!1),$R_FN.showElement(this._pnlContent,!1),$R_FN.showElement(this._pnlLoading,!1),$R_FN.showElement(this._lblLoadingTop,!1),$R_FN.setInnerHTML(this._lblError,t)):($R_FN.showElement(this._pnlError,!1),$R_FN.setInnerHTML(this._lblError,""))},showNoData:function(n,t){n?($R_FN.showElement(this._pnlNoData,!0),$R_FN.showElement(this._pnlError,!1),$R_FN.showElement(this._pnlContent,!1),$R_FN.showElement(this._pnlLoading,!1),$R_FN.showElement(this._lblLoadingTop,!1),$R_FN.setInnerHTML(this._lblError,t)):($R_FN.showElement(this._pnlError,!1),$R_FN.setInnerHTML(this._lblError,""))},showMessage:function(n,t){$R_FN.showElement(this._pnlNoData,n);$R_FN.setInnerHTML(this._pnlNoData,t)},resetCount:function(){$R_FN.setInnerHTML(this._lblCount.innerHTML,"");$R_FN.showElement(this._lblCount,!1)},updateCount:function(n,t){this._intCount=n;$R_FN.showElement(this._lblCount,!0);var i="&nbsp;";t||(i+="(");i+=n;t||(i+=")");$R_FN.setInnerHTML(this._lblCount,i);n==0&&this.showNoData(!0)},show:function(n){$R_FN.showElement(this._element,n)}};Rebound.GlobalTrader.Site.Controls.FieldSet.registerClass("Rebound.GlobalTrader.Site.Controls.FieldSet",Sys.UI.Control,Sys.IDisposable);