///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.ToDoList_Edit = function(element) { 
	Rebound.GlobalTrader.Site.Controls.Forms.ToDoList_Edit.initializeBase(this, [element]);
	this._intToDoID = null;
	this._intCompanyID = null;
	this._intMessageID = null;
	this._blnInitializeComplete = false;
};

Rebound.GlobalTrader.Site.Controls.Forms.ToDoList_Edit.prototype = {

	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.Forms.ToDoList_Edit.callBaseMethod(this, "initialize");
		this.addShown(Function.createDelegate(this, this.formShown));
		this._blnInitializeComplete = true;
	},

	dispose: function() { 
		if (this.isDisposed) return;
		if (this._ctlToDo) this._ctlToDo.dispose();
		this._ctlToDo = null;
		this._intToDoID = null;
		this._intCompanyID = null;
		this._intMessageID = null;
		this._blnInitializeComplete = false;
		Rebound.GlobalTrader.Site.Controls.Forms.ToDoList_Edit.callBaseMethod(this, "dispose");
	},
	
	formShown: function() {
		if (this._blnFirstTimeShown) {
			this.addSave(Function.createDelegate(this, this.saveClicked));
			this._ctlToDo = $find(this.getField("ctlToDo").ID);
			this._ctlToDo._ctlRelatedForm = this;
			this._ctlToDo.setupReminderClick();
		}
		this._ctlToDo._intToDoID = this._intToDoID;
		this._ctlToDo._intCompanyID = this._intCompanyID;
		this._ctlToDo._intMessageID = this._intMessageID;
		this.getData();
	},
	
	getData: function() { 
		this.showLoading(true);
		var obj = new Rebound.GlobalTrader.Site.Data();
		obj.set_PathToData("controls/Nuggets/ToDo");
		obj.set_DataObject("ToDo");
		obj.set_DataAction("GetItem");
		obj.addParameter("ID", this._intToDoID);
		obj.addDataOK(Function.createDelegate(this, this.getDataOK));
		obj.addError(Function.createDelegate(this, this.getDataError));
		obj.addTimeout(Function.createDelegate(this, this.getDataError));
		$R_DQ.addToQueue(obj);
		$R_DQ.processQueue();
		obj = null;
	},

	getDataOK: function(args) { 
		var result = args._result;
		this.setFormFieldsToDefaults();
		if (result) {
			this._ctlToDo._intToDoID = this._intToDoID;
			this.setFieldValue("ctlSubject", result.Subject);
			this.setFieldValue("ctlText", result.Text);
			this.setFieldValue("ctlDueDate", result.DueDate);
			this.setFieldValue("ctlDueTime", result.DueTime);
		 	this.setFieldValue("ctlReminder", result.HasReminder);
			this._ctlToDo.selectReminder();
			if (result.HasReminder) {
				this.setFieldValue("ctlReminderDate", result.ReminderDate);
				this.setFieldValue("ctlReminderTime", result.ReminderTime);
				this.setFieldValue("ctlReminderText", result.ReminderText);
			}
		}
		this.showLoading(false);
		this.showInnerContent(true);
	},

	getDataError: function(args) {
		this._ctlRelatedNugget.showNuggetError(true, args.get_ErrorMessage());
	},
	
	saveClicked: function() {
		if (!this.validateForm()) return false;
		var obj = new Rebound.GlobalTrader.Site.Data();
		obj.set_PathToData("controls/Nuggets/ToDo");
		obj.set_DataObject("ToDo");
		obj.set_DataAction("SaveEdit");
		this._ctlToDo.addFieldsToDataObject(obj);
		obj.addDataOK(Function.createDelegate(this, this.saveOK));
		obj.addError(Function.createDelegate(this, this.saveError));
		obj.addTimeout(Function.createDelegate(this, this.saveError));
		$R_DQ.addToQueue(obj);
		$R_DQ.processQueue();
		obj = null;
	},
	
	validateForm: function() {
		this.onValidate();
		var bln = this._ctlToDo.validateFields();
		if (!bln) this.showError(true);
		return bln;
	},
	
	saveError: function(args) {
		this._strErrorMessage = args._errorMessage;
		this.onSaveError();
	},
	
	saveOK: function(args) {
		if (args._result.Result) {
			this.onSaveComplete();
		} else {
			this.saveError(args);
		}
	}

};

Rebound.GlobalTrader.Site.Controls.Forms.ToDoList_Edit.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.ToDoList_Edit", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
