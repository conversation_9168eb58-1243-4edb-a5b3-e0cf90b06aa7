Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.EXCELDocuments_Delete=function(n){Rebound.GlobalTrader.Site.Controls.Forms.EXCELDocuments_Delete.initializeBase(this,[n]);this._intPDFDocumentID=-1;this._ctlConfirm=null;this._pdfFileName="";this._strSectionName=""};Rebound.GlobalTrader.Site.Controls.Forms.EXCELDocuments_Delete.prototype={get_intPDFDocumentID:function(){return this._intPDFDocumentID},set_intPDFDocumentID:function(n){this._intPDFDocumentID!==n&&(this._intPDFDocumentID=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Forms.EXCELDocuments_Delete.callBaseMethod(this,"initialize");this.addShown(Function.createDelegate(this,this.formShown))},dispose:function(){this.isDisposed||(this._ctlConfirm&&this._ctlConfirm.dispose(),this._ctlConfirm=null,this._intPDFDocumentID=null,this._pdfFileName=null,Rebound.GlobalTrader.Site.Controls.Forms.EXCELDocuments_Delete.callBaseMethod(this,"dispose"))},formShown:function(){this._blnFirstTimeShown&&(this._ctlConfirm=this.getFieldComponent("ctlConfirm"),this._ctlConfirm.addClickYesEvent(Function.createDelegate(this,this.yesClicked)),this._ctlConfirm.addClickNoEvent(Function.createDelegate(this,this.noClicked)))},yesClicked:function(){this.showSaving(!0);var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("Controls/Nuggets/EXCELDocuments");n.set_DataObject("EXCELDocuments");n.set_DataAction("Delete");n.addParameter("id",this._intPDFDocumentID);n.addParameter("pdffilename",this._pdfFileName);n.addParameter("Section",this._strSectionName);n.addDataOK(Function.createDelegate(this,this.saveComplete));n.addError(Function.createDelegate(this,this.saveError));n.addTimeout(Function.createDelegate(this,this.saveError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null;this.onSave()},noClicked:function(){this.showSaving(!1);this.onNotConfirmed()},saveError:function(n){this.showSaving(!1);this._strErrorMessage=n._errorMessage;this.onSaveError()},saveComplete:function(n){this.showSaving(!1);n._result.Result==!0?this.onSaveComplete():(this._strErrorMessage=n._errorMessage,this.onSaveError())}};Rebound.GlobalTrader.Site.Controls.Forms.EXCELDocuments_Delete.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.EXCELDocuments_Delete",Rebound.GlobalTrader.Site.Controls.Forms.Base,Sys.IDisposable);