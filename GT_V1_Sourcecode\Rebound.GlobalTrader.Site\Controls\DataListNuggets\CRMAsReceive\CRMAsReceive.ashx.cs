/* Marker     changed by      date         Remarks
/* [0001]      A<PERSON><PERSON><PERSON>   14/07/2014  ESMS Ref:172 - Issue in Part Searching in whole GT */

using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;
using Rebound.GlobalTrader.Site.Enumerations;

namespace Rebound.GlobalTrader.Site.Controls.DataListNuggets.Data {

	public class CRMAsReceive : Base {

		private string _strCallType = "";

		public override void ProcessRequest(HttpContext context) {
			base.ProcessRequest(context);
			if (Action == "GetData_All") GetData_All();
		}

		protected override void GetData() {
			_strCallType = "READY";
			//get data	
			List<CustomerRmaLine> lst = CustomerRmaLine.DataListNuggetReadyToReceive(
				SessionManager.ClientID
				, GetFormValue_NullableInt("PageIndex", 0)
				, GetFormValue_NullableInt("PageSize", 10)
				, GetFormValue_NullableInt("SortIndex")
				, GetFormValue_NullableInt("SortDir", SortColumnDirection.ASC)
                //[0001] start code
                //, GetFormValue_StringForPartSearch("Part")
                  , GetFormValue_PartForLikeSearch("Part")
                //[0001] end code
				//, GetFormValue_StringForNameSearch("Contact")
                 ,GetFormValue_StringForNameSearchDecode("Contact")
				//, GetFormValue_StringForNameSearch("CMName")
                 ,GetFormValue_StringForNameSearchDecode("CMName")
				, GetFormValue_NullableInt("SalesmanName")
				, GetFormValue_StringForSearch("CRMANotes")
				, GetFormValue_NullableInt("InvNoLo")
				, GetFormValue_NullableInt("InvNoHi")
				, GetFormValue_NullableInt("CRMANoLo")
				, GetFormValue_NullableInt("CRMANoHi")
				, GetFormValue_NullableDateTime("CRMADateFrom")
				, GetFormValue_NullableDateTime("CRMADateTo")
                , GetFormValue_NullableInt("Client")
                , GetFormValue_Boolean("IsGlobalLogin")
			);

			ProcessRows(lst);

			base.GetData();
		}

		private void GetData_All() {
			_strCallType = "ALL";
			//get data	
			List<CustomerRmaLine> lst = CustomerRmaLine.DataListNugget(
				SessionManager.ClientID
				, null
				, null
				, null
				, GetFormValue_NullableInt("PageIndex", 0)
				, GetFormValue_NullableInt("PageSize", 10)
				, GetFormValue_NullableInt("SortIndex")
				, GetFormValue_NullableInt("SortDir", SortColumnDirection.ASC)
                //[0001] start code
                //, GetFormValue_StringForPartSearch("Part")
                  , GetFormValue_PartForLikeSearch("Part")
                //[0001] end code
				// GetFormValue_StringForNameSearch("Contact"),
               , GetFormValue_StringForNameSearchDecode("Contact")
                //GetFormValue_StringForNameSearch("CMName"),
                , GetFormValue_StringForNameSearchDecode("CMName")
				, GetFormValue_NullableInt("SalesmanName")
				, GetFormValue_StringForSearch("CRMANotes")
				, GetFormValue_NullableInt("InvNoLo")
				, GetFormValue_NullableInt("InvNoHi")
				, GetFormValue_NullableInt("CRMANoLo")
				, GetFormValue_NullableInt("CRMANoHi")
				, GetFormValue_NullableDateTime("CRMADateFrom")
				, GetFormValue_NullableDateTime("CRMADateTo")
				, false
				, false
                ,false
                 , GetFormValue_NullableInt("Client")
                , GetFormValue_Boolean("IsGlobalLogin")
			);

			ProcessRows(lst);
			SaveState();
		}

		private void ProcessRows(List<CustomerRmaLine> lst) {
			JsonObject jsn = new JsonObject();
			JsonObject jsnRows = new JsonObject(true);
			jsn.AddVariable("TotalRecords", (lst.Count > 0) ? lst[0].RowCnt : 0);
			for (int i = 0; i < lst.Count; i++) {
				JsonObject jsnRow = new JsonObject();
				jsnRow.AddVariable("ID", lst[i].CustomerRMAId);
				jsnRow.AddVariable("No", lst[i].CustomerRMANumber);
				jsnRow.AddVariable("Part", lst[i].Part);
				jsnRow.AddVariable("Quantity", Functions.FormatNumeric(lst[i].Quantity));
				jsnRow.AddVariable("QuantityOutstanding", Functions.FormatNumeric(lst[i].Quantity - lst[i].QuantityReceived));
				jsnRow.AddVariable("Date", Functions.FormatDate(lst[i].CustomerRMADate));
				jsnRow.AddVariable("CM", lst[i].CompanyName);
				jsnRow.AddVariable("CMNo", lst[i].CompanyNo);
				jsnRow.AddVariable("Invoice", lst[i].InvoiceNumber);
				jsnRow.AddVariable("InvNo", lst[i].InvoiceNo);
				jsnRow.AddVariable("Contact", lst[i].ContactName);
				jsnRow.AddVariable("ContactNo", lst[i].ContactNo);
				jsnRow.AddVariable("Mfr", lst[i].ManufacturerCode);
				jsnRow.AddVariable("MfrNo", lst[i].ManufacturerNo);
				jsnRow.AddVariable("ROHS", lst[i].ROHS);
                jsnRow.AddVariable("ClientName", lst[i].ClientName);
				jsnRows.AddVariable(jsnRow);
				jsnRow.Dispose();
				jsnRow = null;
			}
			jsn.AddVariable("Results", jsnRows);
			OutputResult(jsn);
			jsnRows.Dispose(); jsnRows = null;
			jsn.Dispose(); jsn = null;
		}

		protected override void AddFilterStates() {
			AddExplicitFilterState("CallType", _strCallType);
			AddFilterState("Part");
			AddFilterState("Contact");
			AddFilterState("CMName");
			AddFilterState("SalesmanName");
			AddFilterState("CRMANotes");
			AddFilterState("InvNo");
			AddFilterState("CRMANo");
			AddFilterState("CRMADateFrom");
			AddFilterState("CRMADateTo");
			base.AddFilterStates();
		}
	}
}
