﻿GO

IF OBJECT_ID('dbo.usp_selectAll_SalesOrderLine_ReportShipped', 'P') IS NOT NULL
BEGIN
    DROP PROCEDURE dbo.usp_selectAll_SalesOrderLine_ReportShipped;
END

GO

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

/*
===========================================================================================
TASK      			UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-210625]			Phuc Hoang			09-Aug-2024		CREATE          Sanctioned manufacturers need to be highlighted in red on the SOR PDF and SO screens.
===========================================================================================
*/

CREATE PROCEDURE  [dbo].[usp_selectAll_SalesOrderLine_ReportShipped]              
--***********************************************************************************************                    
--* RP 08.07.2010:                    
--* - add handling for Services                    
--*                      
--* RP 15.02.2010:                    
--* - add ROHS                    
--*                      
--* SK 04.11.2009:                    
--* - include SupplierPart                    
--*                      
--* SK 01.10.2009:                    
--* - include SupplierType                    
--*                     
--* SK 06.08.2009:                    
--* - attach "isnull" to landedcost to allow for Service lines                    
--***********************************************************************************************                    
    @SalesOrderLineId int                    
AS                     
    SELECT  ila.SalesOrderLineNo AS SalesOrderLineId                    
          , ila.Quantity AS QuantityShipped                    
          , gil.GoodsInLineId AS GoodsInLineNo                    
          , gil.GoodsInNo                    
          , CASE                      
              WHEN (ivl.ServiceNo > 0) THEN ivl.Part                    
              ELSE stk.Part                    
            END AS Part                     
          , stk.ROHS                    
          , stk.ManufacturerNo                    
          , mf.ManufacturerName                    
          , mf.ManufacturerCode                    
          , stk.DateCode                    
          , CASE                     
              WHEN (ivl.ServiceNo > 0) THEN sv.Cost                    
              ELSE isnull( isnull(stk.ClientLandedCost,stk.LandedCost), 0)                    
            END AS LandedCost                    
          , stk.ProductNo                    
          , pr.ProductName    
    , pr.ProductDescription                    
          , iv.InvoiceDate                    
          , isnull((gil.ShipInCost / gil.Quantity * ila.Quantity), 0) AS ShipInCost                    
          , cast(pol.Taxable AS char(1)) AS Taxable                    
          , CASE                     
              WHEN (ivl.ServiceNo > 0) THEN sv.Cost                    
              ELSE isnull(ipol.Price, pol.Price)          
            END AS PurchasePrice                    
          , pol.PurchaseOrderNo                    
          --, po.PurchaseOrderNumber                    
    , isnull(ipo.InternalPurchaseOrderNumber, po.PurchaseOrderNumber ) as PurchaseOrderNumber          
          --, po.CurrencyNo                    
          --, cu.CurrencyCode                    
          --, cu.CurrencyDescription             
    , isnull(ipo.CurrencyNo, po.CurrencyNo) as CurrencyNo          
          , isnull(cup.CurrencyCode, cu.CurrencyCode) as CurrencyCode          
          , isnull(cup.CurrencyDescription, cu.CurrencyDescription ) as CurrencyDescription                 
          , po.TermsNo                    
          , tm.TermsName                    
         -- , ISNULL(rg.RegionName , cn.CountryName ) as CountryName          
           , case when ipo.InternalPurchaseOrderId  is null then cn.CountryName else '' end as CountryName          
          , isnull(cnipo.Duty, cn.Duty) as Duty           
          , stk.ClientLandedCost                   
          , case isnull(cnipo.Duty, cn.Duty)                    
              WHEN 1 THEN dbo.ufn_get_productdutyrate(stk.ProductNo, iv.InvoiceDate)                    
         ELSE 0                    
            END AS DutyRate                    
          , po.CompanyNo AS SupplierNo                    
          , cm.CompanyName AS SupplierName                    
          , ct.Name AS SupplierType         
          , CASE                     
              WHEN (ivl.ServiceNo > 0) THEN ivl.CustomerPart                    
     ELSE pol.SupplierPart                    
            END AS SupplierPart                    
          , ivl.ServiceNo                    
          , pol.POSerialNo                  
          ,isnull(po.InternalPurchaseOrderNo,0) as InternalPurchaseOrderNo                 
--          ,(select                     
--        co.CompanyName        
--       from tbInternalPurchaseOrderLine ipol                 
--      Left Join tbSalesOrderLine sol on sol.SalesOrderLineId=ipol.SalesOrderLineNo                
--left join tbInternalPurchaseOrder ipo on ipo.InternalPurchaseOrderId=ipol.InternalPurchaseOrderNo                  
--left join tbCompany   co ON co.CompanyId = ipo.CompanyNo where sol.SalesOrderLineId=@SalesOrderLineId ) as IPOSupplierName              
 , cop.CompanyName as   IPOSupplierName             
   , ISNULL(po.DateOrdered,GETDATE()) as PODateOrdered           
--,(select                     
--        ct.Name                
--  from tbInternalPurchaseOrderLine ipol                 
--      Left Join tbSalesOrderLine sol on sol.SalesOrderLineId=ipol.SalesOrderLineNo                
--left join tbInternalPurchaseOrder ipo on ipo.InternalPurchaseOrderId=ipol.InternalPurchaseOrderNo                  
--left join tbCompany   co ON co.CompanyId = ipo.CompanyNo                
-- LEFT JOIN dbo.tbCompanyType ct ON co.TypeNo = ct.CompanyTypeId                 
-- where sol.SalesOrderLineId=@SalesOrderLineId )  as IPOSupplierType            
  --,  ctp.Name as  IPOSupplierType                 
  ,  ct.Name as  IPOSupplierType       
    , isnull(pr.IsHazardous,0) as IsProdHazardous          
 ,isnull(pr.IsOrderViaIPOonly,0) as IsOrderViaIPOonly      
 ,pol.ECCNCode 
 ,isnull((select top 1 m.ECCNStatus from dbo.tbECCN m   where m.ECCNCode=pol.ECCNCode ),0)  as IsECCNWarning                   
    FROM    dbo.tbInvoiceLineAllocation ila                    
    JOIN    dbo.tbInvoiceLine ivl ON ivl.InvoiceLineId = ila.InvoiceLineNo                    
    JOIN    dbo.tbInvoice iv ON iv.InvoiceId = ivl.InvoiceNo                    
    LEFT JOIN dbo.tbGoodsInLine gil ON gil.GoodsInLineId = ila.GoodsInLineNo                    
    LEFT JOIN dbo.tbStock stk ON stk.StockId = ila.StockNo                    
    LEFT JOIN dbo.tbPurchaseOrderLine pol ON pol.PurchaseOrderLineId = gil.PurchaseOrderLineNo                     
    LEFT JOIN dbo.tbPurchaseOrder po ON po.PurchaseOrderId = pol.PurchaseOrderNo                    
    LEFT JOIN dbo.tbCurrency cu ON cu.CurrencyId = po.CurrencyNo                    
    LEFT JOIN dbo.tbCompany cm ON cm.CompanyId = po.CompanyNo                    
    LEFT JOIN dbo.tbCountry cn ON cn.CountryId = po.ImportCountryNo                    
    LEFT JOIN dbo.tbTerms tm ON tm.TermsId = po.TermsNo                    
    LEFT JOIN dbo.tbProduct pr ON pr.ProductId = stk.ProductNo                    
    LEFT JOIN dbo.tbManufacturer mf ON mf.ManufacturerId = stk.ManufacturerNo                    
    LEFT JOIN dbo.tbCompanyType ct ON cm.TypeNo = ct.CompanyTypeId                    
    LEFT JOIN dbo.tbService sv ON sv.ServiceId = ivl.ServiceNo            
 LEFT JOIN dbo.tbInternalPurchaseOrderLine ipol on ipol.PurchaseOrderLineNo = pol.PurchaseOrderLineId          
    LEFT JOIN dbo.tbInternalPurchaseOrder ipo on ipo.InternalPurchaseOrderId=ipol.InternalPurchaseOrderNo             
    LEFT JOIN tbRegion rg on ipo.RegionNo = rg.RegionId          
    LEFT JOIN dbo.tbCountry cnipo ON cnipo.CountryId = ipo.ImportCountryNo                
 LEFT JOIN dbo.tbCurrency cup ON cup.CurrencyId = ipo.CurrencyNo              
 left join tbCompany   cop ON cop.CompanyId = ipo.CompanyNo               
 --LEFT JOIN dbo.tbCompanyType ctp ON cop.TypeNo = ctp.CompanyTypeId            
                       
    WHERE   ila.SalesOrderLineNo = @SalesOrderLineId 


GO