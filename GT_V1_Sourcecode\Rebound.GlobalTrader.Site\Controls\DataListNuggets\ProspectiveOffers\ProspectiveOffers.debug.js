﻿///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />

Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DataListNuggets");

Rebound.GlobalTrader.Site.Controls.DataListNuggets.ProspectiveOffers = function (element) {
    Rebound.GlobalTrader.Site.Controls.DataListNuggets.ProspectiveOffers.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.DataListNuggets.ProspectiveOffers.prototype = {
    //get_intSalesPersonID: function () { return this._intSalesPersonID; }, set_intSalesPersonID: function (value) { if (this._intSalesPersonID !== value) this._intSalesPersonID = value; },
    initialize: function () {
        this.showLeftPanel();
        this.addSetupDataCallEvent(Function.createDelegate(this, this.setupDataCall));
        this.addGetDataOKEvent(Function.createDelegate(this, this.getDataOK));
        this.addInitCompleteEvent(Function.createDelegate(this, this.initAfterBaseIsReady));
        this._strPathToData = "controls/DataListNuggets/ProspectiveOffers";
        this._strDataObject = "ProspectiveOffers";
        Rebound.GlobalTrader.Site.Controls.DataListNuggets.ProspectiveOffers.callBaseMethod(this, "initialize");
    },

    initAfterBaseIsReady: function () {
        this.addPageTabChangedEvent(Function.createDelegate(this, this.pageTabChanged));
        //this.applySalesPersonFilter();
        this.updateFilterVisibility();
        this.getData();
    },

    dispose: function () {
        if (this.isDisposed) return;
        this._intSalesPersonID = null;
        Rebound.GlobalTrader.Site.Controls.DataListNuggets.ProspectiveOffers.callBaseMethod(this, "dispose");
    },

    pageTabChanged: function () {
        this._table._intCurrentPage = 1;
        this._enmViewLevel = this._intCurrentTab;
        this.updateFilterVisibility();
        this.getData();
    },

    setupDataCall: function () {
        this._objData.addParameter("ViewLevel", this._enmViewLevel);
    },

    getDataOK: function () {
        for (var i = 0, l = this._objResult.Results.length; i < l; i++) {
            var row = this._objResult.Results[i];
            var aryData = [
                $R_FN.createNubButton('Ord_ProspectiveCSDetail.aspx?PROId=' + row.ID, row.ID)
                , $R_FN.setCleanTextValue(row.ImportDate)
                , $R_FN.setCleanTextValue(row.Source)
                , row.Rows
                , $R_FN.setCleanTextValue(row.ImportStatus)
                , $R_FN.setCleanTextValue(row.Supplier)
                , $R_FN.setCleanTextValue(row.ImportedBy)

            ];
            this._table.addRow(aryData, row.ID, false);
            aryData = null; row = null;
        }
    },

    updateFilterVisibility: function () {
        this.getFilterField("ctlImportedBy").show(this._enmViewLevel != 0);
    },

    //applySalesPersonFilter: function () {
    //    if ((this._intSalesPersonID) && this._intSalesPersonID > 0)
    //        this.getFilterField("ctlSalesman").setValue(this._intSalesPersonID);
    //},

    showLeftPanel: function () {
        var panel = document.getElementById("ctl00_pnlLeftButton");
        if (panel != undefined && panel.classList.contains('leftbarButton_Off')) {  //left panel is close
            var $link = $('#ctl00_pnlLeftButton a:first');
            $link[0].click();
        }
    }
};

Rebound.GlobalTrader.Site.Controls.DataListNuggets.ProspectiveOffers.registerClass("Rebound.GlobalTrader.Site.Controls.DataListNuggets.ProspectiveOffers", Rebound.GlobalTrader.Site.Controls.DataListNuggets.Base, Sys.IDisposable);
