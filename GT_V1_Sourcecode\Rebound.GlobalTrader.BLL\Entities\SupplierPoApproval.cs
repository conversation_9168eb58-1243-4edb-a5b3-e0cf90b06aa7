﻿/* Marker     changed by      date         Remarks  
   [001]      A<PERSON><PERSON><PERSON>  25-Aug-2021   Add for Supplier Approval.
   [002]      A<PERSON><PERSON><PERSON>   22-Sep-2021   Add new property for TypeNo
   [003]      A<PERSON><PERSON>av <PERSON>   26-Oct-2021   Add columns for QualityApprovalDate & LineManagerApprovalDate.
   [004]      A<PERSON><PERSON>av <PERSON>   04-Jan-2022   Add subject in term and condetion email log.
   [005]      Ab<PERSON>av <PERSON>   20-Jan-2022   Add new column IsEscalate.
   [006]      Abhinav <PERSON>   27-Jan-2022   Add Line Manager Snapshot.
   [007]      Abhinav <PERSON>xena   14-Feb-2022   Add new notes field on Approval popup.
   [008]      Abhinav <PERSON>a   15-Mar-2022   Add new props for approval permissions.
 */
using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;
using Rebound.GlobalTrader.DAL;
using Rebound.GlobalTrader.BLL;

namespace Rebound.GlobalTrader.BLL
{
    public partial class SupplierPoApproval : BizObject
    {

        #region Properties
        public System.Int32? PurchaseOrderId { get; set; }
        public System.Int32? PurchaseOrderNumber { get; set; }
        public System.Int32? SupplierApprovalId { get; set; }
        public System.Int32? SupplierId { get; set; }
        public System.String SupplierName { get; set; }
        public System.Int32? ApprovedOrdersCount { get; set; }
        public System.Int32? SupplierRMAsCount { get; set; }
        public System.Int32? PurchasingMethodNo { get; set; }
        public System.String CommentText { get; set; }
        public System.String PurchasingMethod { get; set; }
        public System.String QualityApproval { get; set; }
        public System.String QualityApprovedBy { get; set; }
        public System.String LineManagerApproval { get; set; }
        public System.String LineManagerApprovedBy { get; set; }
        public System.Boolean? SupplierERAIReported { get; set; }
        public System.Boolean? PartIsERAIReported { get; set; }
        public System.Boolean? IsQualityApproved { get; set; }
        public System.Boolean? IsLineManagerApproved { get; set; }
        public System.String TradeReferenceOne { get; set; }
        public System.Boolean? IsPDFAvalableOne { get; set; }
        public System.String TradeReferenceTwo { get; set; }
        public System.Boolean? IsPDFAvalableTwo { get; set; }
        public System.String TradeRefrenceThree { get; set; }
        public System.Boolean? IsPDFAvalableThree { get; set; }
        public System.String ApprovalStatus { get; set; }
        public System.DateTime? ApprovedDated { get; set; }
        public System.String ApprovedBy { get; set; }
        public System.Int32? BuyerId { get; set; }
        public System.String BuyerName { get; set; }
        public System.Int32? LineManagerId { get; set; }
        public System.String LineManagerName { get; set; }
        public System.String LineManagerEmail { get; set; }
        public System.Boolean? Result { get; set; }
        public System.Int32? QualityGroupId { get; set; }
        public System.Int32? POEscalationGroupId { get; set; }
        public System.String FranchiseweblinkOrEvidence { get; set; }
        public System.Int32? PrecogsSupplierNo { get; set; }
        public System.Int32? UpdatedBy { get; set; }
        public System.Int32? ImageId { get; set; }
        public System.DateTime? DLUP { get; set; }
        public System.String Caption { get; set; }
        public System.String ImageName { get; set; }
        public System.String TypeNo { get; set; }
        private string _strUpdatedByName = null;
        public System.DateTime? QualityApproveDate { get; set; }
        public System.DateTime? LineManagerApproveDate { get; set; }
        //[005] code start
        public System.Boolean? IsEscalate { get; set; }
        //[005] code end

        //[006] code start
        public System.String PartNo { get; set; }
        public System.Int32? Quantity { get; set; }
        public System.String PaymentTerms { get; set; }
        public System.String Incoterms { get; set; }
        public System.String ShipVia { get; set; }
        public System.String ShipFromCountry { get; set; }
        public System.DateTime? DeliveryDateToRebound { get; set; }
        public System.DateTime? DeliveryDateToCustomer { get; set; }
        public System.String TotalValueOfPOCurrency { get; set; }
        public System.String Margin { get; set; }
        public System.String HUBRFQQuoteNo { get; set; }
        public System.String RepeatOrder { get; set; }
        public System.String ReboundPurchaserDivision { get; set; }
        public System.String GTClinetForPO { get; set; }
        public System.String Warehouse { get; set; }
        public System.String CustomerDefinedVendor { get; set; }
        //[006] code end
        public System.Boolean? InDraftMode { get; set; }
        public System.String Status { get; set; }
        public System.Int32? EvidenceCount { get; set; }
        public System.Int32? TradeRefOneCount { get; set; }
        public System.Int32? TRadeRefTwoCount { get; set; }
        public System.Int32? TradeRefThreeCount { get; set; }
        public System.Int32? DevicePictureCount { get; set; }
        public System.Int32? ManufacturerPictureCount { get; set; }
        public System.Int32? TraceblityPictureCount { get; set; }
        public System.Boolean? IsSendToLineManager { get; set; }
        public System.DateTime? SendToLineManagerDLUP { get; set; }
        public System.String SendToLineManagerUpdatedBy { get; set; }
        public System.Boolean? IsSendToSupplier { get; set; }
        public System.DateTime? SendToSupplierDLUP { get; set; }
        public System.String SendToSupplierUpdatedBy { get; set; }
        public System.Boolean? IsSendToQuality { get; set; }
        public System.DateTime? SendToQualityDLUP { get; set; }
        public System.String SendToQualityUpdatedBy { get; set; }
        public System.Int32? SupplierApprovalStatus { get; set; }
        public System.Boolean? ApproverPartERAIReported { get; set; }
        public System.String SupplierType { get; set; }
        public System.Boolean? IsPOApproved { get; set; }
        //[008] code start
        public System.Boolean? IsLineManagerApprovalPermission { get; set; }
        public System.Boolean? IsQualityTeamApprovalPermission { get; set; }
        public System.Boolean? ISEscalationApprovalPermission { get; set; }
        public System.String LineManagerComment { get; set; }
        public System.String QualityComment { get; set; }
        public System.Int32? WarrantyPeriod { get; set; }
        public System.Int32? CountryOnHighRisk { get; set; }
        public System.Int32? ClientNo { get; set; }
        //[008] code end
        public string UpdatedByName
        {
            get
            {
                if (_strUpdatedByName == null)
                {
                    _strUpdatedByName = "";
                    if (UpdatedBy != null)
                    {
                        BLL.Login lg = BLL.Login.GetName(UpdatedBy);
                        if (lg != null) _strUpdatedByName = lg.EmployeeName;
                        lg = null;
                    }
                }
                return _strUpdatedByName;
            }
        }
        public string FullCaption { get; set; }
        #endregion

        #region Methods

        /// <summary>
        /// Line Manager Approvals.
        /// Calls [usp_PoSupplierLineManagerApproval]
        /// </summary>
        public static List<SupplierPoApproval> LineManagerApprovalDeclineIndependentTest(System.Int32? supplierApprovalId, System.Int32? Status, System.Int32? UpdatedBy, System.Int32? clientId, System.String Notes)
        {
            List<SupplierPoApprovalDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.SupplierPoApproval.LineManagerApprovalDeclineIndependentTest(supplierApprovalId, Status, UpdatedBy, clientId, Notes);
            if (lstDetails == null)
            {
                return new List<SupplierPoApproval>();
            }
            else
            {
                List<SupplierPoApproval> lst = new List<SupplierPoApproval>();
                foreach (SupplierPoApprovalDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.SupplierPoApproval obj = new Rebound.GlobalTrader.BLL.SupplierPoApproval();
                    obj.PurchaseOrderId = objDetails.PurchaseOrderId;
                    obj.PurchaseOrderNumber = objDetails.PurchaseOrderNumber;
                    obj.BuyerId = objDetails.BuyerId;
                    obj.BuyerName = objDetails.BuyerName;
                    obj.QualityGroupId = objDetails.QualityGroupId;
                    obj.SupplierId = objDetails.SupplierId;
                    obj.Result = objDetails.Result;
                    obj.CommentText = objDetails.CommentText;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }

        /// <summary>
        /// Get selected data for edit screen.
        /// Calls [usp_select_TradeRefForSupplier]
        /// </summary>
        public static SupplierPoApproval Get(System.Int32? SupplierApprovalId)
        {
            Rebound.GlobalTrader.DAL.SupplierPoApprovalDetails objDetails = Rebound.GlobalTrader.DAL.SiteProvider.SupplierPoApproval.Get(SupplierApprovalId);
            if (objDetails == null)
            {
                return null;
            }
            else
            {
                SupplierPoApproval obj = new SupplierPoApproval();
                obj.SupplierApprovalId = objDetails.SupplierApprovalId;
                obj.SupplierId = objDetails.SupplierId;
                obj.SupplierName = objDetails.SupplierName;
                obj.FranchiseweblinkOrEvidence = objDetails.FranchiseweblinkOrEvidence;
                obj.TradeReferenceOne = objDetails.TradeReferenceOne;
                obj.IsPDFAvalableOne = objDetails.IsPDFAvalableOne;
                obj.TradeReferenceTwo = objDetails.TradeReferenceTwo;
                obj.IsPDFAvalableTwo = objDetails.IsPDFAvalableTwo;
                obj.TradeRefrenceThree = objDetails.TradeRefrenceThree;
                obj.IsPDFAvalableThree = objDetails.IsPDFAvalableThree;
                obj.PurchasingMethodNo = objDetails.PurchasingMethodNo;
                obj.PrecogsSupplierNo = objDetails.PrecogsSupplierNo;
                obj.TypeNo = objDetails.TypeNo;
                obj.InDraftMode = objDetails.InDraftMode;
                obj.EvidenceCount = objDetails.EvidenceCount;
                obj.TradeRefOneCount = objDetails.TradeRefOneCount;
                obj.TRadeRefTwoCount = objDetails.TRadeRefTwoCount;
                obj.TradeRefThreeCount = objDetails.TradeRefThreeCount;
                obj.DevicePictureCount = objDetails.DevicePictureCount;
                obj.ManufacturerPictureCount = objDetails.ManufacturerPictureCount;
                obj.TraceblityPictureCount = objDetails.TraceblityPictureCount;
                obj.LineManagerId = objDetails.LineManagerId;
                obj.PurchaseOrderId = objDetails.PurchaseOrderId;
                obj.PurchaseOrderNumber = objDetails.PurchaseOrderNumber;
                obj.IsSendToLineManager = objDetails.IsSendToLineManager;
                obj.SendToLineManagerDLUP = objDetails.SendToLineManagerDLUP;
                obj.SendToLineManagerUpdatedBy = objDetails.SendToLineManagerUpdatedBy;
                obj.IsSendToSupplier = objDetails.IsSendToSupplier;
                obj.SendToSupplierDLUP = objDetails.SendToSupplierDLUP;
                obj.SendToSupplierUpdatedBy = objDetails.SendToSupplierUpdatedBy;
                obj.IsSendToQuality = objDetails.IsSendToQuality;
                obj.SendToQualityDLUP = objDetails.SendToQualityDLUP;
                obj.SendToQualityUpdatedBy = objDetails.SendToQualityUpdatedBy;
                obj.CommentText = objDetails.CommentText;
                obj.IsLineManagerApproved = objDetails.IsLineManagerApproved;
                objDetails = null;
                return obj;
            }
        }


        /// <summary>
        /// Get Data for the Suppler approval history tab.
        /// Calls [usp_selectSupplierApprovalPOHistory]
        /// </summary>
        public static List<SupplierPoApproval> GetData_SupplierApprovalHistory(System.Int32? purchaseOrderId)
        {
            List<SupplierPoApprovalDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.SupplierPoApproval.GetData_SupplierApprovalHistory(purchaseOrderId);
            if (lstDetails == null)
            {
                return new List<SupplierPoApproval>();
            }
            else
            {
                List<SupplierPoApproval> lst = new List<SupplierPoApproval>();
                foreach (SupplierPoApprovalDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.SupplierPoApproval obj = new Rebound.GlobalTrader.BLL.SupplierPoApproval();
                    obj.SupplierApprovalId = objDetails.SupplierApprovalId;
                    obj.SupplierId = objDetails.SupplierId;
                    obj.SupplierName = objDetails.SupplierName;
                    obj.ApprovalStatus = objDetails.ApprovalStatus;
                    obj.ApprovedDated = objDetails.ApprovedDated;
                    obj.ApprovedBy = objDetails.ApprovedBy;

                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }
        /// <summary>
        /// Get data for the Approval Approval Status tab.
        /// Calls [usp_selectSupplierPoApproval]
        /// </summary>
        public static List<SupplierPoApproval> GetData_ApprovalStatus(System.Int32? purchaseOrderId,System.Int32? LoginId, System.Int32? ClientNo)
        {
            List<SupplierPoApprovalDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.SupplierPoApproval.GetData_ApprovalStatus(purchaseOrderId, LoginId, ClientNo);
            if (lstDetails == null)
            {
                return new List<SupplierPoApproval>();
            }
            else
            {
                List<SupplierPoApproval> lst = new List<SupplierPoApproval>();
                foreach (SupplierPoApprovalDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.SupplierPoApproval obj = new Rebound.GlobalTrader.BLL.SupplierPoApproval();
                    obj.SupplierApprovalId = objDetails.SupplierApprovalId;
                    obj.SupplierId = objDetails.SupplierId;
                    obj.SupplierName = objDetails.SupplierName;
                    obj.ApprovedOrdersCount = objDetails.ApprovedOrdersCount;
                    obj.SupplierRMAsCount = objDetails.SupplierRMAsCount;
                    obj.PurchasingMethod = objDetails.PurchasingMethod;
                    obj.QualityApproval = objDetails.QualityApproval;
                    obj.QualityApprovedBy = objDetails.QualityApprovedBy;
                    obj.LineManagerApproval = objDetails.LineManagerApproval;
                    obj.LineManagerApprovedBy = objDetails.LineManagerApprovedBy;
                    obj.SupplierERAIReported = objDetails.SupplierERAIReported;
                    obj.PartIsERAIReported = objDetails.PartIsERAIReported;
                    obj.IsQualityApproved = objDetails.IsQualityApproved;
                    obj.IsLineManagerApproved = objDetails.IsLineManagerApproved;
                    obj.QualityApproveDate = objDetails.QualityApproveDate;
                    obj.LineManagerApproveDate = objDetails.LineManagerApproveDate;
                    //[005] code start
                    obj.IsEscalate = objDetails.IsEscalate;
                    //[005] code end

                    //[006] code start
                    obj.PartNo = objDetails.PartNo;
                    obj.Quantity = objDetails.Quantity;
                    obj.PaymentTerms = objDetails.PaymentTerms;
                    obj.Incoterms = objDetails.Incoterms;
                    obj.ShipVia = objDetails.ShipVia;
                    obj.ShipFromCountry = objDetails.ShipFromCountry;
                    obj.TotalValueOfPOCurrency = objDetails.TotalValueOfPOCurrency;
                    obj.Margin = objDetails.Margin;
                    obj.RepeatOrder = objDetails.RepeatOrder;
                    obj.ReboundPurchaserDivision = objDetails.ReboundPurchaserDivision;
                    obj.GTClinetForPO = objDetails.GTClinetForPO;
                    obj.Warehouse = objDetails.Warehouse;
                    obj.CustomerDefinedVendor = objDetails.CustomerDefinedVendor;
                    //[006] code end
                    obj.IsSendToQuality = objDetails.IsSendToQuality;
                    obj.SupplierApprovalStatus = objDetails.SupplierApprovalStatus;
                    obj.ApproverPartERAIReported = objDetails.ApproverPartERAIReported;
                    obj.SupplierType = objDetails.SupplierType;
                    obj.IsPOApproved = objDetails.IsPOApproved;
                    obj.InDraftMode = objDetails.InDraftMode;
                    //[008] code start
                    obj.IsLineManagerApprovalPermission = objDetails.IsLineManagerApprovalPermission;
                    obj.IsQualityTeamApprovalPermission = objDetails.IsQualityTeamApprovalPermission;
                    obj.ISEscalationApprovalPermission = objDetails.ISEscalationApprovalPermission;
                    obj.CommentText = objDetails.CommentText;
                    obj.LineManagerComment = objDetails.LineManagerComment;
                    obj.QualityComment = objDetails.QualityComment;
                    //[008] code end
                    obj.WarrantyPeriod = objDetails.WarrantyPeriod;
                    obj.CountryOnHighRisk = objDetails.CountryOnHighRisk;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }

        /// <summary>
        /// Get Data for the Trade Reference tab.
        /// Calls [usp_selectSupplierApprovalTradeReferences]
        /// </summary>
        public static List<SupplierPoApproval> GetData_TradeReference(System.Int32? purchaseOrderId, System.Int32? ClientNo)
        {
            List<SupplierPoApprovalDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.SupplierPoApproval.GetData_TradeReference(purchaseOrderId,  ClientNo);
            if (lstDetails == null)
            {
                return new List<SupplierPoApproval>();
            }
            else
            {
                List<SupplierPoApproval> lst = new List<SupplierPoApproval>();
                foreach (SupplierPoApprovalDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.SupplierPoApproval obj = new Rebound.GlobalTrader.BLL.SupplierPoApproval();
                    obj.SupplierApprovalId = objDetails.SupplierApprovalId;
                    obj.SupplierName = objDetails.SupplierName;
                    obj.FranchiseweblinkOrEvidence = objDetails.FranchiseweblinkOrEvidence;
                    obj.SupplierId = objDetails.SupplierId;
                    obj.TradeReferenceOne = objDetails.TradeReferenceOne;
                    obj.IsPDFAvalableOne = objDetails.IsPDFAvalableOne;
                    obj.TradeReferenceTwo = objDetails.TradeReferenceTwo;
                    obj.IsPDFAvalableTwo = objDetails.IsPDFAvalableTwo;
                    obj.TradeRefrenceThree = objDetails.TradeRefrenceThree;
                    obj.IsPDFAvalableThree = objDetails.IsPDFAvalableThree;
                    obj.Status = objDetails.Status;
                    obj.EvidenceCount = objDetails.EvidenceCount;
                    obj.TradeRefOneCount = objDetails.TradeRefOneCount;
                    obj.TRadeRefTwoCount = objDetails.TRadeRefTwoCount;
                    obj.TradeRefThreeCount = objDetails.TradeRefThreeCount;
                    obj.DevicePictureCount = objDetails.DevicePictureCount;
                    obj.ManufacturerPictureCount = objDetails.ManufacturerPictureCount;
                    obj.TraceblityPictureCount = objDetails.TraceblityPictureCount;
                    obj.BuyerId = objDetails.BuyerId;
                    obj.SupplierApprovalStatus = objDetails.SupplierApprovalStatus;
                    obj.SupplierType = objDetails.SupplierType;
                    obj.IsPOApproved = objDetails.IsPOApproved;
                    obj.CountryOnHighRisk = objDetails.CountryOnHighRisk;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }

        /// <summary>
        /// Update Trade reference data.
        /// Calls [usp_update_TradeReferenceDetails]
        /// </summary>
        public static bool Update(System.Int32? SupplierApprovalId, System.String FranchiseweblinkOrEvidence, System.String TradeReferenceOne, System.String TradeReferenceTwo, System.String TradeRefrenceThree, System.Int32? PurchasingMethodNo, System.Int32? PrecogsSupplierNo, System.Int32? UpdatedBy, System.Boolean? InDraftMode, System.String CommentText)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.SupplierPoApproval.Update(SupplierApprovalId, FranchiseweblinkOrEvidence, TradeReferenceOne, TradeReferenceTwo, TradeRefrenceThree, PurchasingMethodNo, PrecogsSupplierNo, UpdatedBy, InDraftMode, CommentText);
        }
        /// <summary>
        /// Update Trade reference data(without parameter).
        /// Calls [usp_update_TradeReferenceDetails]
        /// </summary>
        public bool Update()
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.SupplierPoApproval.Update(SupplierApprovalId, FranchiseweblinkOrEvidence, TradeReferenceOne, TradeReferenceTwo, TradeRefrenceThree, PurchasingMethodNo, PrecogsSupplierNo, UpdatedBy, InDraftMode, CommentText);
        }

        /// <summary>
        /// Quality Approvals
        /// Calls [usp_PoSupplierQualityApproval]
        /// </summary>
        public static List<SupplierPoApproval> QualityApproveDecline(System.Int32? purchaseOrderLineId, System.Int32? Approved, System.Int32? updatedBy, System.String Notes, System.Boolean? PartERAI)
        {
            List<SupplierPoApprovalDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.SupplierPoApproval.QualityApproveDecline(purchaseOrderLineId, Approved, updatedBy, Notes, PartERAI);
            if (lstDetails == null)
            {
                return new List<SupplierPoApproval>();
            }
            else
            {
                List<SupplierPoApproval> lst = new List<SupplierPoApproval>();
                foreach (SupplierPoApprovalDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.SupplierPoApproval obj = new Rebound.GlobalTrader.BLL.SupplierPoApproval();
                    obj.PurchaseOrderId = objDetails.PurchaseOrderId;
                    obj.PurchaseOrderNumber = objDetails.PurchaseOrderNumber;
                    obj.BuyerId = objDetails.BuyerId;
                    obj.BuyerName = objDetails.BuyerName;
                    obj.LineManagerId = objDetails.LineManagerId;
                    obj.LineManagerName = objDetails.LineManagerName;
                    obj.SupplierId = objDetails.SupplierId;
                    obj.Result = objDetails.Result;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }

        }

        public static int InsertTradeRefPDF(System.Int32? ID, System.String caption, System.String tempFile, System.Int32 LoginID, System.String UploadType)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.SupplierPoApproval.InsertTradeRefPDF(ID, caption, tempFile, LoginID, UploadType);
        }
        // [001] code start
        /// <summary>
        /// GetListForSupplierApproval
        /// Calls [usp_selectAll_PDF_for_SupplierApproval]
        /// </summary>
        public static List<PDFDocument> GetPDFListForSupplierApproval(System.Int32? SupplierApprovalId, System.String UploadType)
        {
            List<PDFDocumentDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.SupplierPoApproval.GetPDFListForSupplierApproval(SupplierApprovalId, UploadType);
            if (lstDetails == null)
            {
                return new List<PDFDocument>();
            }
            else
            {
                List<PDFDocument> lst = new List<PDFDocument>();
                foreach (PDFDocumentDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.PDFDocument obj = new Rebound.GlobalTrader.BLL.PDFDocument();
                    obj.PDFDocumentId = objDetails.PDFDocumentId;
                    obj.PDFDocumentRefNo = objDetails.PDFDocumentRefNo;
                    obj.Caption = objDetails.Caption;
                    obj.FileName = objDetails.FileName;
                    obj.UpdatedBy = objDetails.UpdatedBy;
                    obj.DLUP = objDetails.DLUP;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }

        /// Delete
        /// Calls [usp_delete_SupplierApprovalPDF]
        /// </summary>
        public static bool DeleteSupplierApprovalPDF(System.Int32? SATradeReferencePDFID)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.SupplierPoApproval.DeleteSupplierApprovalPDF(SATradeReferencePDFID);
        }
        public static int InsertSupplierAprrovalImage(System.Int32? ID, System.String caption, System.String tempFile, System.Int32 LoginID, System.String UploadType)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.SupplierPoApproval.InsertSupplierAprrovalImage(ID, caption, tempFile, LoginID, UploadType);
        }

        public static List<SupplierPoApproval> GetImageListForSupplierApproval(System.Int32? SupplierApprovalId, System.String UploadType)
        {
            List<SupplierPoApprovalDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.SupplierPoApproval.GetImageListForSupplierApproval(SupplierApprovalId, UploadType);
            if (lstDetails == null)
            {
                return new List<SupplierPoApproval>();
            }
            else
            {
                List<SupplierPoApproval> lst = new List<SupplierPoApproval>();
                foreach (SupplierPoApprovalDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.SupplierPoApproval obj = new Rebound.GlobalTrader.BLL.SupplierPoApproval();
                    obj.ImageId = objDetails.ImageId;
                    obj.Caption = objDetails.Caption;
                    obj.ImageName = objDetails.ImageName;
                    obj.UpdatedBy = objDetails.UpdatedBy;
                    obj.DLUP = objDetails.DLUP;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }
        public static bool DeleteSupplierApprovalImage(System.Int32? SAImageID)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.SupplierPoApproval.DeleteSupplierApprovalImage(SAImageID);
        }
        /// <summary>
        /// Update Trade reference data(without parameter).
        /// Calls [usp_update_TradeReferenceDetails]
        /// </summary>
        public static int InsertTermCondetionEmailLog(System.Int32? SupplierApprovalNo, System.Int32? SendFromId, System.String SendToEmail, System.Int32? SupplierNo, System.String Subject, System.Boolean? IsNotTCEmail, System.Int32? SendToId)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.SupplierPoApproval.InsertTermCondetionEmailLog(SupplierApprovalNo, SendFromId, SendToEmail, SupplierNo, Subject, IsNotTCEmail, SendToId);
        }
        /// <summary>
        /// Update Trade reference data(without parameter).
        /// Calls [usp_update_LineManagerDetails]
        /// </summary>
        public static SupplierPoApproval UpdateLineManagerDetails(System.Int32? SupplierApprovalId, System.Int32? LineManagerId, System.Boolean? IsSendToSupplier, System.Boolean? IsSendToLineManager, System.Int32? LoginId, System.Boolean? isSendToQuality, System.Int32? ClientId)
        {
            Rebound.GlobalTrader.DAL.SupplierPoApprovalDetails objDetails = Rebound.GlobalTrader.DAL.SiteProvider.SupplierPoApproval.UpdateLineManagerDetails(SupplierApprovalId, LineManagerId, IsSendToSupplier, IsSendToLineManager, LoginId, isSendToQuality, ClientId);
            if (objDetails == null)
            {
                return null;
            }
            else
            {
                SupplierPoApproval obj = new SupplierPoApproval();
                obj.LineManagerId = objDetails.LineManagerId;
                obj.LineManagerName = objDetails.LineManagerName;
                obj.LineManagerEmail = objDetails.LineManagerEmail;
                obj.QualityGroupId = objDetails.QualityGroupId;
                objDetails = null;
                return obj;
            }
        }

        public static SupplierPoApproval ChangeLineManager(System.Int32? SupplierApprovalId, System.Int32? LineManagerId, System.Int32? LoginId, System.Int32? ClientId)
        {
            Rebound.GlobalTrader.DAL.SupplierPoApprovalDetails objDetails = Rebound.GlobalTrader.DAL.SiteProvider.SupplierPoApproval.ChangeLineManager(SupplierApprovalId, LineManagerId, LoginId, ClientId);
            if (objDetails == null)
            {
                return null;
            }
            else
            {
                SupplierPoApproval obj = new SupplierPoApproval();
                obj.LineManagerId = objDetails.LineManagerId;
                obj.LineManagerName = objDetails.LineManagerName;
                obj.LineManagerEmail = objDetails.LineManagerEmail;
                objDetails = null;
                return obj;
            }
        }

        /// <summary>
        /// Quality Team Escalate.
        /// Calls [usp_PoSupplierQualityEscalate]
        /// </summary>
        public static List<SupplierPoApproval> QualityEscalate(System.Int32? SupplierApprovalId, System.Int32? updatedBy, System.Int32? ClientId, System.String Notes, System.Boolean? isPartERAI)
        {
            List<SupplierPoApprovalDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.SupplierPoApproval.QualityEscalate(SupplierApprovalId, updatedBy, ClientId, Notes, isPartERAI);
            if (lstDetails == null)
            {
                return new List<SupplierPoApproval>();
            }
            else
            {
                List<SupplierPoApproval> lst = new List<SupplierPoApproval>();
                foreach (SupplierPoApprovalDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.SupplierPoApproval obj = new Rebound.GlobalTrader.BLL.SupplierPoApproval();
                    obj.PurchaseOrderId = objDetails.PurchaseOrderId;
                    obj.PurchaseOrderNumber = objDetails.PurchaseOrderNumber;
                    obj.POEscalationGroupId = objDetails.POEscalationGroupId;
                    obj.SupplierId = objDetails.SupplierId;
                    obj.Result = objDetails.Result;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }
        /// <summary>
        /// Quality Team Escalate.
        /// Calls [usp_GetSupplierApprovalEmailData]
        /// </summary>
        public static List<SupplierPoApproval> GetEmailData(System.Int32? PurchaseOrderNo)
        {
            List<SupplierPoApprovalDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.SupplierPoApproval.GetEmailData(PurchaseOrderNo);
            if (lstDetails == null)
            {
                return new List<SupplierPoApproval>();
            }
            else
            {
                List<SupplierPoApproval> lst = new List<SupplierPoApproval>();
                foreach (SupplierPoApprovalDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.SupplierPoApproval obj = new Rebound.GlobalTrader.BLL.SupplierPoApproval();
                    //[006] code start
                    obj.PartNo = objDetails.PartNo;
                    obj.Quantity = objDetails.Quantity;
                    obj.PaymentTerms = objDetails.PaymentTerms;
                    obj.Incoterms = objDetails.Incoterms;
                    obj.ShipVia = objDetails.ShipVia;
                    obj.ShipFromCountry = objDetails.ShipFromCountry;
                    obj.TotalValueOfPOCurrency = objDetails.TotalValueOfPOCurrency;
                    obj.Margin = objDetails.Margin;
                    obj.RepeatOrder = objDetails.RepeatOrder;
                    obj.ReboundPurchaserDivision = objDetails.ReboundPurchaserDivision;
                    obj.GTClinetForPO = objDetails.GTClinetForPO;
                    obj.Warehouse = objDetails.Warehouse;
                    obj.CustomerDefinedVendor = objDetails.CustomerDefinedVendor;
                    obj.SupplierName = objDetails.SupplierName;
                    obj.SupplierType = objDetails.SupplierType;
                    obj.ApprovedOrdersCount = objDetails.ApprovedOrdersCount;
                    obj.SupplierRMAsCount = objDetails.SupplierRMAsCount;
                    obj.WarrantyPeriod = objDetails.WarrantyPeriod;
                    //[006] code end
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }
        #endregion
    }
}
