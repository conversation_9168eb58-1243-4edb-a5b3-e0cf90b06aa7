///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//
// RP 23.10.2009:
// - new control
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists");

Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Manufacturers = function(element) { 
	Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Manufacturers.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Manufacturers.prototype = {

	initialize: function() {	
		this.addGetDataOKEvent(Function.createDelegate(this, this.getDataOK));
		this._strPathToData = "controls/DataListNuggets/Manufacturers";
		this._strDataObject = "Manufacturers";
		Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Manufacturers.callBaseMethod(this, "initialize");
	},
	
	dispose: function() { 
		if (this.isDisposed) return;
		Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Manufacturers.callBaseMethod(this, "dispose");
	},

	getDataOK: function() {
		for (var i = 0, l = this._objResult.Results.length; i < l; i++) {
			var row = this._objResult.Results[i];
			this._tbl.addRow([ String.format("<a href=\"{0}\">{1} - {2}", $RGT_gotoURL_Manufacturer(row.ID), $R_FN.setCleanTextValue(row.Code), $R_FN.setCleanTextValue(row.Name)) ]);
			row = null;
		}
	}

};
Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Manufacturers.registerClass("Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Manufacturers", Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Base, Sys.IDisposable);
