<%@ Control Language="C#" CodeBehind="BOMItems_ConfirmRemovePartwatch.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Forms.BOMItems_ConfirmRemovePartwatch" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Form:DesignBase ID="ctlDB" runat="server" ShowRequiredFieldsExplanation="false" AllowQuickHelp="false">
<Explanation><%=Functions.GetGlobalResource("FormExplanations", "BOMItem_ApplyRemovePartwatch")%></Explanation>
	<Content>
		<ReboundUI_Table:Form id="frm" runat="server">		     
	
            <ReboundUI_Form:FormField id="ctlConfirm" runat="server" FieldID="ctlConfirmation" ResourceTitle="SaveConfirm">
				<Field><ReboundUI:Confirmation ID="ctlConfirmation" runat="server" /></Field>
			</ReboundUI_Form:FormField>
		</ReboundUI_Table:Form>		 
	</Content>
</ReboundUI_Form:DesignBase>
