Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.AutoSearch");Rebound.GlobalTrader.Site.Controls.AutoSearch.Stock=function(n){Rebound.GlobalTrader.Site.Controls.AutoSearch.Stock.initializeBase(this,[n]);this._searchType=null};Rebound.GlobalTrader.Site.Controls.AutoSearch.Stock.prototype={get_searchType:function(){return this._searchType},set_searchType:function(n){this._searchType!==n&&(this._searchType=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.AutoSearch.Stock.callBaseMethod(this,"initialize");this.addDataReturnedEvent(Function.createDelegate(this,this.dataReturned));this.addSetupParametersEvent(Function.createDelegate(this,this.setupParameters));this.setupDataObject("Stock")},dispose:function(){this.isDisposed||(this.__searchType=null,Rebound.GlobalTrader.Site.Controls.AutoSearch.Stock.callBaseMethod(this,"dispose"))},setupParameters:function(){this.addDataParameter("searchType",this._searchType)},dataReturned:function(){var t,r,n,i;if(this._result&&this._result.TotalRecords>0)for(t=0,r=this._result.Results.length;t<r;t++)n=this._result.Results[t],i="",i=this._enmResultsActionType==$R_ENUM$AutoSearchResultsActionType.Navigate?$RGT_nubButton_Stock(n.ID,n.Name):$R_FN.setCleanTextValue(n.Name),this.addResultItem(i,$R_FN.setCleanTextValue(n.Name),n.ID),i=null,n=null}};Rebound.GlobalTrader.Site.Controls.AutoSearch.Stock.registerClass("Rebound.GlobalTrader.Site.Controls.AutoSearch.Stock",Rebound.GlobalTrader.Site.Controls.AutoSearch.Base,Sys.IDisposable);