//----------------------------------------------------------------------------------------
// RP 21.12.2009:
//- use SimpleDataTable
//----------------------------------------------------------------------------------------
using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site.Controls;

namespace Rebound.GlobalTrader.Site.Controls.HomeNuggets {
	public partial class ReceivedOrders : Base {

		protected SimpleDataTable _tblR<PERSON>eived;

		/// <summary>
		/// On init
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
			this.HomePageNuggetType = "ReceivedOrders";
			base.OnInit(e);
			WireUpControls();
			SetupTables();
			AddScriptReference("Controls.HomeNuggets.ReceivedOrders.ReceivedOrders.js");
		}

		protected override void OnLoad(EventArgs e) {
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.HomeNuggets.ReceivedOrders", this.ctlDesignBase.ClientID);
			_scScriptControlDescriptor.AddComponentProperty("tblReceived", _tblReceived.ClientID);
			base.OnLoad(e);
		}

		private void SetupTables() {
			_tblReceived.Columns.Add(new SimpleDataColumn("PurchaseOrder", Unit.Pixel(65)));
			_tblReceived.Columns.Add(new SimpleDataColumn("Supplier"));
			_tblReceived.Columns.Add(new SimpleDataColumn("ReceivedDate", Unit.Pixel(75)));
		}

		private void WireUpControls() {
			_tblReceived = (SimpleDataTable)FindContentControl("tblReceived");
		}
	}
}