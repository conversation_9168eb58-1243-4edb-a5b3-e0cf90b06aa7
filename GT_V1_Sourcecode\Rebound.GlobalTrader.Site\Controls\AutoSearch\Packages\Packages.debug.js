///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.AutoSearch");

Rebound.GlobalTrader.Site.Controls.AutoSearch.Packages = function(element){
    Rebound.GlobalTrader.Site.Controls.AutoSearch.Packages.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.AutoSearch.Packages.prototype = {

get_ShowInactive: function() { return this._ShowInactive; }, 	set_ShowInactive: function(value) { if (this._ShowInactive !== value)  this._ShowInactive = value; }, 

	initialize: function(){
        Rebound.GlobalTrader.Site.Controls.AutoSearch.Packages.callBaseMethod(this, "initialize");
		this.addDataReturnedEvent(Function.createDelegate(this, this.dataReturned));
    	//this.addSetupParametersEvent(Function.createDelegate(this, this.setupParameters));
	
        this.setupDataObject("Packages");
	},
	
	dispose: function(){
		if (this.isDisposed) return;
		this._ShowInactive=null;
        Rebound.GlobalTrader.Site.Controls.AutoSearch.Packages.callBaseMethod(this, "dispose");
	},
	
//	setupParameters: function() {
//	alert("hi");
//	//this.addParameter("blnShowInactive", this._ShowInactive);
//		this.addDataParameter("blnShowInactive", this._ShowInactive);
//	},
	
	dataReturned: function(){
	
		if (!this._result) return;
		if (this._result.TotalRecords > 0) {
			for (var i = 0, l = this._result.Results.length; i < l; i++) {
				var res = this._result.Results[i];
				var strHTML = "";
				if (this._enmResultsActionType == $R_ENUM$AutoSearchResultsActionType.Navigate) {
                    strHTML = $RGT_nubButton_Package(res.ID, res.Name);
				} else {
					strHTML = $R_FN.setCleanTextValue(res.Name);
				}
				this.addResultItem(strHTML, $R_FN.setCleanTextValue(res.Name), res.ID);
				strHTML = null;
				res = null;
			}
		}
	}
};
Rebound.GlobalTrader.Site.Controls.AutoSearch.Packages.registerClass("Rebound.GlobalTrader.Site.Controls.AutoSearch.Packages", Rebound.GlobalTrader.Site.Controls.AutoSearch.Base, Sys.IDisposable);
