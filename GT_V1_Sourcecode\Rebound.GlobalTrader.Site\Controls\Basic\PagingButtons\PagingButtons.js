Type.registerNamespace("Rebound.GlobalTrader.Site.Controls");Rebound.GlobalTrader.Site.Controls.PagingButtons=function(n){Rebound.GlobalTrader.Site.Controls.PagingButtons.initializeBase(this,[n]);this._blnEnabled=!0;this._blnStateLocked=!0};Rebound.GlobalTrader.Site.Controls.PagingButtons.prototype={get_pnlLeft:function(){return this._pnlLeft},set_pnlLeft:function(n){this._pnlLeft!==n&&(this._pnlLeft=n)},get_lblTotalResults:function(){return this._lblTotalResults},set_lblTotalResults:function(n){this._lblTotalResults!==n&&(this._lblTotalResults=n)},get_lblCurrentPage:function(){return this._lblCurrentPage},set_lblCurrentPage:function(n){this._lblCurrentPage!==n&&(this._lblCurrentPage=n)},get_lblTotalPages:function(){return this._lblTotalPages},set_lblTotalPages:function(n){this._lblTotalPages!==n&&(this._lblTotalPages=n)},get_hypShowFilter:function(){return this._hypShowFilter},set_hypShowFilter:function(n){this._hypShowFilter!==n&&(this._hypShowFilter=n)},get_lblPageNumbers:function(){return this._lblPageNumbers},set_lblPageNumbers:function(n){this._lblPageNumbers!==n&&(this._lblPageNumbers=n)},get_hypPrev:function(){return this._hypPrev},set_hypPrev:function(n){this._hypPrev!==n&&(this._hypPrev=n)},get_lblPrevDisabled:function(){return this._lblPrevDisabled},set_lblPrevDisabled:function(n){this._lblPrevDisabled!==n&&(this._lblPrevDisabled=n)},get_hypNext:function(){return this._hypNext},set_hypNext:function(n){this._hypNext!==n&&(this._hypNext=n)},get_lblNextDisabled:function(){return this._lblNextDisabled},set_lblNextDisabled:function(n){this._lblNextDisabled!==n&&(this._lblNextDisabled=n)},get_intPagesToShowEitherSideOfCurrent:function(){return this._intPagesToShowEitherSideOfCurrent},set_intPagesToShowEitherSideOfCurrent:function(n){this._intPagesToShowEitherSideOfCurrent!==n&&(this._intPagesToShowEitherSideOfCurrent=n)},get_intPagesToShowAtEnds:function(){return this._intPagesToShowAtEnds},set_intPagesToShowAtEnds:function(n){this._intPagesToShowAtEnds!==n&&(this._intPagesToShowAtEnds=n)},get_strPagesDots:function(){return this._strPagesDots},set_strPagesDots:function(n){this._strPagesDots!==n&&(this._strPagesDots=n)},get_intCurrentPage:function(){return this._intCurrentPage},set_intCurrentPage:function(n){this._intCurrentPage!==n&&(this._intCurrentPage=n)},get_intTotalResults:function(){return this._intTotalResults},set_intTotalResults:function(n){this._intTotalResults!==n&&(this._intTotalResults=n)},get_intTotalPages:function(){return this._intTotalPages},set_intTotalPages:function(n){this._intTotalPages!==n&&(this._intTotalPages=n)},get_intCurrentPageSize:function(){return this._intCurrentPageSize},set_intCurrentPageSize:function(n){this._intCurrentPageSize!==n&&(this._intCurrentPageSize=n)},get_blnFiltersOn:function(){return this._blnFiltersOn},set_blnFiltersOn:function(n){this._blnFiltersOn!==n&&(this._blnFiltersOn=n)},get_lblPageSizeLinks:function(){return this._lblPageSizeLinks},set_lblPageSizeLinks:function(n){this._lblPageSizeLinks!==n&&(this._lblPageSizeLinks=n)},get_pnlLock:function(){return this._pnlLock},set_pnlLock:function(n){this._pnlLock!==n&&(this._pnlLock=n)},addPageSizeClickEvent:function(n){this.get_events().addHandler("pagesizeclick",n)},removePageSizeClickEvent:function(n){this.get_events().removeHandler("pagesizeclick",n)},onPageSizeClick:function(){var n=this.get_events().getHandler("pagesizeclick");n&&n(this,Sys.EventArgs.Empty)},addFilterStateChangeEvent:function(n){this.get_events().addHandler("FilterStateChange",n)},removeFilterStateChangeEvent:function(n){this.get_events().removeHandler("FilterStateChange",n)},onFilterStateChange:function(){var n=this.get_events().getHandler("FilterStateChange");n&&n(this,Sys.EventArgs.Empty)},addPageChangedEvent:function(n){this.get_events().addHandler("PageChanged",n)},removePageChangedEvent:function(n){this.get_events().removeHandler("PageChanged",n)},onPageChanged:function(){var n=this.get_events().getHandler("PageChanged");n&&n(this,Sys.EventArgs.Empty)},addStateLockChanged:function(n){this.get_events().addHandler("StateLockChanged",n)},removeStateLockChanged:function(n){this.get_events().removeHandler("StateLockChanged",n)},onStateLockChanged:function(){var n=this.get_events().getHandler("StateLockChanged");n&&n(this,Sys.EventArgs.Empty)},addShowLockLoading:function(n){this.get_events().addHandler("ShowLockLoading",n)},removeShowLockLoading:function(n){this.get_events().removeHandler("ShowLockLoading",n)},onShowLockLoading:function(){var n=this.get_events().getHandler("ShowLockLoading");n&&n(this,Sys.EventArgs.Empty)},initialize:function(){Rebound.GlobalTrader.Site.Controls.PagingButtons.callBaseMethod(this,"initialize");this._hypShowFilter&&$addHandler(this._hypShowFilter,"click",Function.createDelegate(this,this.toggleShowFilter));this._hypPrev&&$addHandler(this._hypPrev,"click",Function.createDelegate(this,this.prevPage));this._hypNext&&$addHandler(this._hypNext,"click",Function.createDelegate(this,this.nextPage));this._pnlLock&&$addHandler(this._pnlLock,"click",Function.createDelegate(this,this.clickLock));this.setupPageSizeLinks()},dispose:function(){this.isDisposed||(this.get_element()&&$clearHandlers(this.get_element()),this._hypShowFilter&&$clearHandlers(this._hypShowFilter),this._hypPrev&&$clearHandlers(this._hypPrev),this._hypNext&&$clearHandlers(this._hypNext),this._pnlLock&&$clearHandlers(this._pnlLock),this._pnlLeft=null,this._lblTotalResults=null,this._lblCurrentPage=null,this._lblTotalPages=null,this._hypShowFilter=null,this._lblPageNumbers=null,this._hypPrev=null,this._lblPrevDisabled=null,this._hypNext=null,this._lblNextDisabled=null,this._lblPageSizeLinks=null,this._pnlLock=null,this._blnEnabled=!1,this._blnStateLocked=!1,Rebound.GlobalTrader.Site.Controls.PagingButtons.callBaseMethod(this,"dispose"),this.isDisposed=!0)},show:function(n){$R_FN.showElement(this.get_element(),n)},enable:function(n){this._blnEnabled=n},setupPageSizeLinks:function(){$R_FN.setInnerHTML(this._lblPageSizeLinks,"");var n=this.addPageSizeLink(5,"5");n+=this.addPageSizeLink(10,"10");n+=this.addPageSizeLink(25,"25");n+=this.addPageSizeLink(50,"50");$R_FN.setInnerHTML(this._lblPageSizeLinks,n)},addPageSizeLink:function(n,t){var i="",r=n==this._intCurrentPageSize?"pagingNo_Selected":"pagingNo",u=String.format("$find('{0}').pageSizeClick({1});",this.get_element().id,n);return i=String.format('<a href="javascript:void(0);" class="{0}" onclick="{1}">{2}<\/a>',r,u,t),r=null,u=null,i},pageSizeClick:function(n){this._blnEnabled&&n!=this._intCurrentPageSize&&(this._intCurrentPageSize=n,this.setupPageSizeLinks(),this.onPageSizeClick())},toggleShowFilter:function(){this._blnFiltersOn=!this._blnFiltersOn;this.setFilter(this._blnFiltersOn);this.onFilterStateChange()},setFilter:function(n){this._blnFiltersOn=n;this._hypShowFilter&&(this._hypShowFilter.className=this._blnFiltersOn?"showFilterOn":"showFilter")},updatePageDisplay:function(){var t,o,i,n,s,h,r,u,f,e;if($R_FN.setInnerHTML(this._lblCurrentPage,this._intTotalPages==0?0:this._intCurrentPage),$R_FN.setInnerHTML(this._lblTotalPages,this._intTotalPages),$R_FN.setInnerHTML(this._lblTotalResults,this._intTotalResults),t="",this._intTotalPages>1)for(o=this._intTotalPages<=this._intPagesToShowAtEnds+this._intPagesToShowEitherSideOfCurrent*2,i=!1,n=0,s=this._intTotalPages;n<s;n++)h=o||n+1>this._intTotalPages-this._intPagesToShowAtEnds||n<this._intPagesToShowAtEnds||n+1>=this._intCurrentPage-this._intPagesToShowEitherSideOfCurrent&&n+1<=this._intCurrentPage+this._intPagesToShowEitherSideOfCurrent,h?(r=n+1==this._intCurrentPage?"pagingNo_Selected":"pagingNo",u=String.format("$find('{0}').changePage({1});",this.get_element().id,n+1),t+=String.format('<a href="javascript:void(0);" class="{0}" onclick="{1}">{2}<\/a>',r,u,n+1),i=!1,r=null,u=null):(i||(t+="..."),i=!0);$R_FN.setInnerHTML(this._lblPageNumbers,t);f=this._intCurrentPage==1;e=this._intCurrentPage==this._intTotalPages;$R_FN.showElement(this._hypPrev,!f);$R_FN.showElement(this._lblPrevDisabled,f);$R_FN.showElement(this._hypNext,!e);$R_FN.showElement(this._lblNextDisabled,e)},nextPage:function(){this._blnEnabled&&this.changePage(this._intCurrentPage+1)},prevPage:function(){this._blnEnabled&&this.changePage(this._intCurrentPage-1)},changePage:function(n){this._blnEnabled&&(this._intCurrentPage=this.limitPage(n),this.onPageChanged())},limitPage:function(n){return Math.max(Math.min(n,this._intTotalPages),1)},clickLock:function(){this._blnEnabled&&(this.showLockLoading(),this._blnStateLocked=!this._blnStateLocked,this.onStateLockChanged())},setLockState:function(n){this._pnlLock&&(this._blnStateLocked=n,Sys.UI.DomElement.removeCssClass(this._pnlLock,"lockLoading"),n?(Sys.UI.DomElement.addCssClass(this._pnlLock,"locked"),Sys.UI.DomElement.removeCssClass(this._pnlLock,"unlocked")):(Sys.UI.DomElement.addCssClass(this._pnlLock,"unlocked"),Sys.UI.DomElement.removeCssClass(this._pnlLock,"locked")))},showLockLoading:function(n){this._pnlLock&&(Sys.UI.DomElement.addCssClass(this._pnlLock,"lockLoading"),Sys.UI.DomElement.removeCssClass(this._pnlLock,"locked"),Sys.UI.DomElement.removeCssClass(this._pnlLock,"unlocked"),n||this.onShowLockLoading())}};Rebound.GlobalTrader.Site.Controls.PagingButtons.registerClass("Rebound.GlobalTrader.Site.Controls.PagingButtons",Sys.UI.Control,Sys.IDisposable);