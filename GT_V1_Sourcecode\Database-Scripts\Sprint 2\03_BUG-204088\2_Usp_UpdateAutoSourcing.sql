SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


IF OBJECT_ID('Usp_UpdateAutoSourcing','P') IS NOT NULL
    DROP PROC [dbo].[Usp_UpdateAutoSourcing]
GO
/* 
===========================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-202577]		An.TranTan			16-May-2024		Update			Add more fields to auto sourcing
[US-204008]		An.TranTan			22-May-2024		Update			Update ADJQty of auto source
===========================================================================================
*/
CREATE PROCEDURE [dbo].[Usp_UpdateAutoSourcing]                    
                    
@BOMManagerNo int,                    
@SourceId int,                    
@SPQ int = null,                    
@ReSell Decimal(30,5)= null,        
@Cost Decimal(30,5)= null,        
@Reason Nvarchar(max) =  null,              
@UpdatedBy int = null              
--,@RowsAffected int = NULL Output                     
,@EditMfrId int
,@EditProdId int
,@Part NVARCHAR(30) = NULL
,@ROHS TINYINT = NULL
,@CountryOfOriginNo INT = NULL
,@DateCode NVARCHAR(5) = NULL
,@PackageNo INT = NULL
,@Quantity INT = NULL
,@OfferStatusNo INT = NULL
,@FactorySealed NVARCHAR(50) = NULL
,@MSLNo INT = NULL
,@SupplierTotalQSA NVARCHAR(50) = NULL
,@SupplierMOQ NVARCHAR(50) = NULL
,@SupplierLTB NVARCHAR(50) = NULL
,@CurrencyNo INT = NULL
,@ShippingCost DECIMAL(30,5)= null
,@LeadTime NVARCHAR(50) = NULL
,@RegionNo INT = NULL
,@DeliveryDate DATETIME = NULL
,@SupplierWarranty INT = NULL
,@RoHSStatus NVARCHAR(50) = NULL
,@Notes NVARCHAR(128) = NULL
,@TestingRecommended BIT = NULL

AS                    
BEGIN                    
                    
begin tran                    
begin try                    

Declare @EditMfrName varchar(200)
select @EditMfrName =ManufacturerName from tbManufacturer where ManufacturerId=@EditMfrId
                    
--select * from tbEMSOffers                    
                    
--select * from tbAPIOffers                    
                    
                    
                    
select * into #tempOffers from tbAutoSource where BOMManagerNo = @BOMManagerNo and SourceId = @SourceId                    
                    
                    
/*                    
update #tempOffers                    
set Resale = @ReSell,SPQ = @SPQ, Cost= @Cost                    
                    
                    
update #tempOffers set spq =0  where isnull(spq,'')=''                    
                    
update #tempOffers                     
set spq = case when isnull(spq,'')='' then 0 else spq end                    
, MOQ = case when isnull(MOQ,'')='' then 0 else MOQ end
*/
UPDATE #tempOffers
SET 
	Resale = @ReSell,
	Cost = @Cost,
	SPQ = ISNULL(@SPQ, 0),
	MOQ = ISNULL(@SupplierMOQ, 0),
	Quantity = @Quantity,
	ADJQty = @Quantity
                    
-- ADJQTY                    
--update a                     
--set ADJQty =                    
--case when TRY_PARSE(a.spq as int) is not null then                    
-- case when a.spq>0 then  b.Quantity else b.Quantity end                    
-- else 0 end --,                    
---- a.customerrequirementid = b.CustomerRequirementId                    
--from #tempOffers a join tbCustomerRequirement b on a.customerrequirementid =b.customerrequirementid     
--where b.BOMManagerNo = @BOMManagerNo                    
                    
  --select * from #tempOffers                  
                  
--profit                    
update  a                     
set Profit =                   
--cast(case when a.Cost>0                     
--   then                  
   (cast(isnull(a.Resale ,0) as Decimal(30,5)) - cast( a.Cost as Decimal(30,5)))* cast(ADJQty as Decimal(30,5))                     
   --else 0 end as Decimal(30,5))                    
from #tempOffers a join tbCustomerRequirement b on a.customerrequirementid =b.customerrequirementid where b.BOMManagerNo = @BOMManagerNo                    
                    
                    
--Margin                    
update a                     
set Margin =                   
case when a.Resale >0 then                  
cast(cast(100 as Decimal(30,5))-((cast(100  as Decimal(30,5))/cast(a.Resale  as Decimal(30,5))* cast(a.Cost  as Decimal(30,5)))) as Decimal(30,5))                    
else 0 end                  
from #tempOffers a join tbCustomerRequirement b on a.customerrequirementid =b.customerrequirementid where b.BOMManagerNo = @BOMManagerNo                    
                    
                    
                    
--Excess                    
update a                     
set Excess=                    
case when b.Quantity >0 then                  
cast((                    
cast(100 as Decimal(30,5))                    
/cast(b.Quantity as Decimal(30,5))                    
* ADJQty)                     
/cast(100 as Decimal(30,5)) as Decimal(30,5))                   
else 0 end                  
from #tempOffers a join tbCustomerRequirement b on a.customerrequirementid =b.customerrequirementid where b.BOMManagerNo = @BOMManagerNo                    
                    
                    
                    
--select * from #tempOffers                    
                    
-- Value                    
update #tempOffers                     
set SourceValue= ADJQty * cast(Resale as Decimal(30,5))                    

DECLARE @EditFullPart nvarchar(30);
SET @EditFullPart = dbo.Ufn_get_fullpart(@Part);

update a                     
set                 
a.Resale=b.Resale,                
a.SPQ=b.SPQ,                    
a.ADJQty=b.ADJQty,                    
a.Profit=b.Profit,                    
a.Margin=b.Margin,                    
a.Excess=b.Excess,                    
a.SourceValue=b.SourceValue,        
a.Cost = b.Cost        
,a.ManufacturerNo =@EditMfrId
,a.ManufacturerName= m.ManufacturerName
,a.ProductNo = @EditProdId
,a.Part = @EditFullPart
,a.FullPart = @EditFullPart
,a.ROHS = @ROHS
,a.IHSCountryOfOriginNo = @CountryOfOriginNo
,a.DateCode = @DateCode
,a.PackageNo = @PackageNo
,a.Quantity = b.Quantity
,a.OfferStatusNo = @OfferStatusNo
,a.OfferStatusChangeDate = GETDATE()
,a.OfferStatusChangeLoginNo = @UpdatedBy
,a.FactorySealed = @FactorySealed
,a.MSLLevelNo = @MSLNo
,a.MSL = msl.MSLLevel
,a.SupplierTotalQSA = @SupplierTotalQSA
,a.MOQ = b.MOQ
,a.SupplierMOQ = b.MOQ
,a.SupplierLTB = @SupplierLTB
,a.CurrencyNo = @CurrencyNo
,a.EstimatedShippingCost = @ShippingCost
,a.LT = @LeadTime
,a.RegionNo = @RegionNo
,a.DeliveryDate = @DeliveryDate
,a.SupplierWarranty = @SupplierWarranty
,a.ROHSStatus = @RoHSStatus
,a.Notes = @Notes
,a.TestRecommended = @TestingRecommended
from tbAutoSource a 
join #tempOffers b on a.BOMManagerNo = b.BOMManagerNo and a.SourceId = b.SourceId
join tbManufacturer m on m.ManufacturerId=@EditMfrId
left join dbo.tbMSLLevel msl on msl.MSLLevelId = @MSLNo

            
Declare @CustomerRequirementID int,@FullPart nvarchar(30)      
            
select @CustomerRequirementID = CustomerRequirementId          
   ,@FullPart = FullPart            
 from tbAutoSource where SourceId =@SourceId            
            
if(ISNULL(@Reason, '') <> '')              
begin            
            
if (not exists (select AuditId from tbAudit_BOMManager where BOMManagerNo = @BOMManagerNo and AutoSourceNo = @SourceId))            
begin            
  INSERT INTO dbo.tbAudit_BOMManager                      
  (BOMManagerNo            
  ,CustomerRequirementNo            
  ,AutoSourceNo              
  ,Reason              
  ,CostPrice            
  ,ResellPrice            
  ,FullPart            
  ,UpdatedBy            
  ,DLUP )                      
  VALUES                       
  (@BOMManagerNo            
  ,@CustomerRequirementID            
  ,@SourceId            
  ,@Reason            
  ,@Cost            
  ,@ReSell            
  ,@FullPart            
  ,@UpdatedBy            
  ,GETDATE())              
end            
else            
begin            
 update tbAudit_BOMManager set
 CostPrice = @Cost 
 ,ResellPrice = @ReSell            
 ,Reason = @Reason            
 ,DLUP = GETDATE()            
 ,UpdatedBy = @UpdatedBy where BOMManagerNo = @BOMManagerNo and AutoSourceNo = @SourceId            
end            
              
end              
                    
select 'Success' as 'Status','Sourcing Updated.' as 'Message'                    
--select 1/0                    
commit tran                    
--SELECT  @RowsAffected = @@ROWCOUNT                    
end try                    
begin catch                    
rollback tran                    
select 'Fail',ERROR_MESSAGE() as Message                    
end catch                    
             
       
                    
                    
END   
  
  
  
  


GO


