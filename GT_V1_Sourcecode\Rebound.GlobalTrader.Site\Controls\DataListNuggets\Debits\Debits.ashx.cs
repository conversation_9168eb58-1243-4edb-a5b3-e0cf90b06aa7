/* Marker     changed by      date         Remarks
/* [0001]      A<PERSON><PERSON><PERSON>   14/07/2014  ESMS Ref:172 - Issue in Part Searching in whole GT */

using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;
using Rebound.GlobalTrader.Site.Enumerations;

namespace Rebound.GlobalTrader.Site.Controls.DataListNuggets.Data {

	public class Debits : Base {

		protected override void GetData() {
			//check view level
			ViewLevelList enmViewLevel = (ViewLevelList)GetFormValue_Int("ViewLevel");
			int? SelectedclientNo = null;
			int? SessionClientNo = SessionManager.ClientID;
			bool? blnMakeYellow = false;
			if (SessionManager.IsGSA == true && SessionManager.IsGlobalUser == false)
			{
				SelectedclientNo = GetFormValue_NullableInt("Client");
				if (SelectedclientNo != null)
				{
					blnMakeYellow = true;
				}
				else
				{
					blnMakeYellow = false;
				}

			}
			else
			{
				blnMakeYellow = false;
			}
			//get data	
			List<DebitLine> lst = DebitLine.DataListNugget(
				SessionManager.ClientID
				, (enmViewLevel == ViewLevelList.Team) ? (int?)SessionManager.LoginTeamID : null
				, (enmViewLevel == ViewLevelList.Division) ? (int?)SessionManager.LoginDivisionID : null
				, (enmViewLevel == ViewLevelList.My) ? (int?)LoginID : null
				, GetFormValue_NullableInt("SortIndex")
				, GetFormValue_NullableInt("SortDir", SortColumnDirection.ASC)
				, GetFormValue_NullableInt("PageIndex", 0)
				, GetFormValue_NullableInt("PageSize", 10)
                //[0001] start code
                //, GetFormValue_StringForPartSearch("Part")
                  , GetFormValue_PartForLikeSearch("Part")
                //[0001] end code
                // , GetFormValue_StringForNameSearch("Contact")
                , GetFormValue_StringForNameSearchDecode("Contact")
                //, GetFormValue_StringForNameSearch("CMName")
                 , GetFormValue_StringForNameSearchDecode("CMName")
				, GetFormValue_NullableInt("Buyer")
				, GetFormValue_StringForSearch("DebitNotes")
				, GetFormValue_StringForSearch("SupplierInvoice")
				, GetFormValue_NullableInt("DebitNoLo")
				, GetFormValue_NullableInt("DebitNoHi")
				, GetFormValue_NullableInt("PONoLo")
				, GetFormValue_NullableInt("PONoHi")
				, GetFormValue_NullableInt("SRMANoLo")
				, GetFormValue_NullableInt("SRMANoHi")
				, GetFormValue_NullableDateTime("DebitDateFrom")
				, GetFormValue_NullableDateTime("DebitDateTo")
                 , SessionManager.IsPOHub == true ? GetFormValue_Boolean("PohubOnly") : false
                   , GetFormValue_Boolean("IsGlobalLogin")
                , GetFormValue_NullableInt("Client")
				, SessionManager.LoginID
			);
			JsonObject jsn = new JsonObject();
			jsn.AddVariable("TotalRecords", (lst.Count > 0) ? lst[0].RowCnt : 0);
			JsonObject jsnRowsArray = new JsonObject(true);
			for (int i = 0; i < lst.Count; i++) {
				if (i < lst.Count) {
					JsonObject jsnRow = new JsonObject();
					jsnRow.AddVariable("ID", lst[i].DebitId);
					jsnRow.AddVariable("No", lst[i].DebitNumber);
					jsnRow.AddVariable("Part", lst[i].Part);
					jsnRow.AddVariable("Date", Functions.FormatDate(lst[i].DebitDate));

                    if (lst[i].InternalPurchaseOrderId.HasValue && lst[i].InternalPurchaseOrderId.Value > 0)
                    {
                        jsnRow.AddVariable("CM", SessionManager.IsPOHub.Value ? lst[i].CompanyName : lst[i].IPOCompanyName);
                        jsnRow.AddVariable("CMNo",SessionManager.IsPOHub.Value ?  lst[i].CompanyNo : lst[i].IPOCompanyNo);
                    }
                    else
                    {
                        jsnRow.AddVariable("CM", lst[i].CompanyName);
                        jsnRow.AddVariable("CMNo", lst[i].CompanyNo);
                    }


					jsnRow.AddVariable("SRMA", lst[i].SupplierRMANumber);
					jsnRow.AddVariable("SRMANo", lst[i].SupplierRMANo);
					jsnRow.AddVariable("SupplierInvoice", lst[i].SupplierInvoice);
					jsnRow.AddVariable("PurchaseOrder", lst[i].PurchaseOrderNumber);
					jsnRow.AddVariable("PONo", lst[i].PurchaseOrderNo);
					jsnRow.AddVariable("Contact", lst[i].ContactName);
					jsnRow.AddVariable("ContactNo", lst[i].ContactNo);
					jsnRow.AddVariable("ROHS", lst[i].ROHS);
					jsnRow.AddVariable("MfrNo", lst[i].ManufacturerNo);
					jsnRow.AddVariable("Mfr", lst[i].ManufacturerCode);
                    jsnRow.AddVariable("ClientName", lst[i].ClientName);
					jsnRow.AddVariable("SuppMessage", Functions.ReplaceLineBreaks(lst[i].SupplierMessage));
					jsnRow.AddVariable("blnMakeYellow", blnMakeYellow);
					jsnRowsArray.AddVariable(jsnRow);
					jsnRow.Dispose();
					jsnRow = null;
				}
			}
			jsn.AddVariable("Results", jsnRowsArray);
			OutputResult(jsn);
			jsnRowsArray.Dispose();
			jsnRowsArray = null;
			jsn.Dispose();
			jsn = null; lst = null;
			base.GetData();
		}

		protected override void AddFilterStates() {
            //Prevent filter state for Tab
			//AddExplicitFilterState("ViewLevel", GetFormValue_Int("ViewLevel"));
			AddFilterState("Part");
			AddFilterState("Contact");
			AddFilterState("CMName");
			AddFilterState("Buyer");
			AddFilterState("DebitNotes");
			AddFilterState("SupplierInvoice");
			AddFilterState("DebitNo");
			AddFilterState("PONo");
			AddFilterState("SRMANo");
			AddFilterState("DebitDateFrom");
			AddFilterState("DebitDateTo");
            AddFilterState("PohubOnly");
			base.AddFilterStates();
		}

	}
}
