Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.BOM_Confirm=function(n){Rebound.GlobalTrader.Site.Controls.Forms.BOM_Confirm.initializeBase(this,[n]);this._strBOM="";this._blnCOC=!1;this._selectedUse="";this._selectedUseName="";this._selectedIndex=-1;this._IsGroupAssignment=!1;this._strType=""};Rebound.GlobalTrader.Site.Controls.Forms.BOM_Confirm.prototype={initialize:function(){Rebound.GlobalTrader.Site.Controls.Forms.BOM_Confirm.callBaseMethod(this,"initialize");this.addShown(Function.createDelegate(this,this.formShown))},dispose:function(){this.isDisposed||(this._ctlConfirm&&this._ctlConfirm.dispose(),this._ctlConfirm=null,this._strBOM=null,this._blnCOC=null,this._selectedUse=null,this._selectedUseName=null,Rebound.GlobalTrader.Site.Controls.Forms.BOM_Confirm.callBaseMethod(this,"dispose"))},formShown:function(){this._blnFirstTimeShown&&(this._ctlConfirm=this.getFieldComponent("ctlConfirm"),this._ctlConfirm.addClickYesEvent(Function.createDelegate(this,this.yesClicked)),this._ctlConfirm.addClickNoEvent(Function.createDelegate(this,this.noClicked)))},yesClicked:function(){if(this.validateDropDown()){this.showSaving(!0);var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/BOMMainInfo");n.set_DataObject("BOMMainInfo");n.set_DataAction("UpdateBOMByPH");n.addParameter("BOMIds",this._strBOM);n.addParameter("SelectedUser",this._selectedUse);n.addParameter("SelectedUserName",this._selectedUseName);n.addParameter("IsGroupAssignment",this._IsGroupAssignment);n.addParameter("strType",this._strType);n.addDataOK(Function.createDelegate(this,this.saveComplete));n.addError(Function.createDelegate(this,this.saveError));n.addTimeout(Function.createDelegate(this,this.saveError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null;this.onSave()}},noClicked:function(){this.showSaving(!1);this.onNotConfirmed()},validateDropDown:function(){var n=!0;return this._selectedIndex<=0&&(n=!1,alert("Please select user to assign HUBRFQ items."),this.noClicked()),n},saveError:function(n){this.showSaving(!1);this._strErrorMessage=n._errorMessage;this.onSaveError()},saveComplete:function(n){this.showSaving(!1);n._result.Result>=1?(this.onSaveComplete(),alert("Selected HUBRFQ has been assigned successfully."),this.getData()):(this._strErrorMessage=n._errorMessage,this.onSaveError())}};Rebound.GlobalTrader.Site.Controls.Forms.BOM_Confirm.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.BOM_Confirm",Rebound.GlobalTrader.Site.Controls.Forms.Base,Sys.IDisposable);