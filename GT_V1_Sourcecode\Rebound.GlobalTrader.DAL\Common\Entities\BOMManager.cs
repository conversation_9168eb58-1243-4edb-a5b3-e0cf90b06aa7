﻿using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;

namespace Rebound.GlobalTrader.DAL
{
    public class BOMManager
    {
        #region Constructors

        public BOMManager() { }

        #endregion

        #region Properties
        public System.Int32 BOMManagerId { get; set; }
        public System.Int32 Line { get; set; }
        public System.Double LineValue { get; set; }
        public System.Boolean isIncludeAltPart { get; set; }
        /// <summary>
        /// CustomerRequirementId (from Table)
        /// </summary>
        public System.Int32 CustomerRequirementId { get; set; }
        /// <summary>
        /// CustomerRequirementNumber (from Table)
        /// </summary>
        public System.Int32 CustomerRequirementNumber { get; set; }
        public System.String IHSProduct { get; set; }
        public System.String IHSHTSCode { get; set; }
        public System.Int32? IHSProductNo { get; set; }
        public System.String IHSProductName { get; set; }
        public System.String IHSDutyCode { get; set; }
        public System.String PurchaseRequestId { get; set; }
        public System.String PurchaseRequestNumber { get; set; }
        public System.String ECCNCode { get; set; }
        public System.String REQStatusName { get; set; }
        public System.Int32? ParentRequirementNo { get; set; }
        public System.Int32? ParentRequirementId { get; set; }

        /// <summary>
        /// ClientNo (from Table)
        /// </summary>
        public System.Int32 ClientNo { get; set; }
        /// <summary>
        /// SupportTeamMemberNo
        /// </summary>
        public System.Int32? SupportTeamMemberNo { get; set; }
        /// <summary>
        /// SupportTeamMemberName
        /// </summary>
        public System.String SupportTeamMemberName { get; set; }
        /// <summary>
        /// FullPart (from Table)
        /// </summary>
        public System.String FullPart { get; set; }
        /// <summary>
        /// Part (from Table)
        /// </summary>
        public System.String Part { get; set; }
        /// <summary>
        /// ManufacturerNo (from Table)
        /// </summary>
        public System.Int32? ManufacturerNo { get; set; }
        /// <summary>
        /// DateCode (from Table)
        /// </summary>
        public System.String DateCode { get; set; }
        /// <summary>
        /// PackageNo (from Table)
        /// </summary>
        public System.Int32? PackageNo { get; set; }
        /// <summary>
        /// Quantity (from Table)
        /// </summary>
        public System.Int32 Quantity { get; set; }
        /// <summary>
        /// Price (from Table)
        /// </summary>
        public System.Double Price { get; set; }
        /// <summary>
        /// CurrencyNo (from Table)
        /// </summary>
        public double TotalBomManagerLinePrice { get; set; }
        public System.Int32? CurrencyNo { get; set; }
        /// <summary>
        /// ReceivedDate (from Table)
        /// </summary>
        public System.DateTime ReceivedDate { get; set; }
        /// <summary>
        /// Salesman (from Table)
        /// </summary>
        public System.Int32 Salesman { get; set; }
        /// <summary>
        /// DatePromised (from Table)
        /// </summary>
        public System.DateTime DatePromised { get; set; }
        /// <summary>
        /// Notes (from Table)
        /// </summary>
        public System.String Notes { get; set; }
        /// <summary>
        /// Instructions (from Table)
        /// </summary>
        public System.String Instructions { get; set; }
        /// <summary>
        /// Shortage (from Table)
        /// </summary>
        public System.Boolean Shortage { get; set; }
        /// <summary>
        /// CompanyNo (from Table)
        /// </summary>
        public System.Int32? CompanyNo { get; set; }
        /// <summary>
        /// ContactNo (from Table)
        /// </summary>
        public System.Int32? ContactNo { get; set; }
        /// <summary>
        /// Alternate (from Table)
        /// </summary>
        public System.Boolean Alternate { get; set; }
        /// <summary>
        /// OriginalCustomerRequirementNo (from Table)
        /// </summary>
        public System.Int32? OriginalCustomerRequirementNo { get; set; }
        /// <summary>
        /// ReasonNo (from Table)
        /// </summary>
        public System.Int32? ReasonNo { get; set; }
        /// <summary>
        /// ProductNo (from Table)
        /// </summary>
        public System.Int32? ProductNo { get; set; }
        /// <summary>
        /// CustomerPart (from Table)
        /// </summary>
        public System.String CustomerPart { get; set; }
        /// <summary>
        /// Closed (from Table)
        /// </summary>
        public System.Boolean Closed { get; set; }
        /// <summary>
        /// ROHS (from Table)
        /// </summary>
        public System.Byte? ROHS { get; set; }
        /// <summary>
        /// UpdatedBy (from Table)
        /// </summary>
        public System.Int32? UpdatedBy { get; set; }
        /// <summary>
        /// Updated By Name (from Table)
        /// </summary>
        public System.String UpdatedByName { get; set; }
        /// <summary>
        /// DLUP (from Table)
        /// </summary>
        public System.DateTime DLUP { get; set; }
        /// <summary>
        /// UsageNo (from Table)
        /// </summary>
        public System.Int32? UsageNo { get; set; }
        /// <summary>
        /// FullCustomerPart (from Table)
        /// </summary>
        public System.String FullCustomerPart { get; set; }
        /// <summary>
        /// BOM (from Table)
        /// </summary>
        public System.Boolean? BOMManagerflag { get; set; }
        /// <summary>
        /// BOMName (from Table)
        /// </summary>
        public System.String BOMManagerName { get; set; }
        /// <summary>
		/// BomStatus (from Table)
		/// </summary>
		public System.String BomManagerStatus { get; set; }

        /// <summary>
        /// PartWatch (from Table)
        /// </summary>
        public System.Boolean? PartWatch { get; set; }
        /// <summary>
        /// SalesmanName (from usp_select_Credit)
        /// </summary>
        public System.String SalesmanName { get; set; }
        /// <summary>
        /// ManufacturerCode (from usp_datalistnugget_CustomerRequirement)
        /// </summary>
        public System.String ManufacturerCode { get; set; }
        /// <summary>
        /// CompanyName (from usp_select_Credit)
        /// </summary>
        public System.String CompanyName { get; set; }
        /// <summary>
        /// ContactName (from usp_select_Credit)
        /// </summary>
        public System.String ContactName { get; set; }
        /// <summary>
        /// RowNum (from usp_datalistnugget_CustomerRequirement)
        /// </summary>
        public System.Int64? RowNum { get; set; }
        /// <summary>
        /// RowCnt (from usp_datalistnugget_CustomerRequirement)
        /// </summary>
        public System.Int32? RowCnt { get; set; }
        /// <summary>
        /// CurrencyCode (from usp_select_Credit)
        /// </summary>
        public System.String CurrencyCode { get; set; }
        /// <summary>
        /// DisplayStatus (from usp_select_CustomerRequirement)
        /// </summary>
        public System.String DisplayStatus { get; set; }
        /// <summary>
        /// DivisionNo (from Table)
        /// </summary>
        public System.Int32? DivisionNo { get; set; }
        /// <summary>
        /// TeamNo (from usp_select_Credit)
        /// </summary>
        public System.Int32? TeamNo { get; set; }
        /// <summary>
        /// CompanyOnStop (from usp_select_CustomerRequirement)
        /// </summary>
        public System.Boolean? CompanyOnStop { get; set; }
        /// <summary>
        /// CurrencyDescription (from usp_select_Credit)
        /// </summary>
        public System.String CurrencyDescription { get; set; }
        /// <summary>
        /// ProductName (from usp_select_CustomerRequirement)
        /// </summary>
        public System.String ProductName { get; set; }
        /// <summary>
        /// ProductDescription (from usp_select_CustomerRequirement)
        /// </summary>
        public System.String ProductDescription { get; set; }
        /// <summary>
        /// ManufacturerName (from usp_select_CustomerRequirement)
        /// </summary>
        public System.String ManufacturerName { get; set; }
        /// <summary>
        /// PackageName (from usp_select_CustomerRequirement)
        /// </summary>
        public System.String PackageName { get; set; }
        /// <summary>
        /// PackageDescription (from usp_select_CustomerRequirement)
        /// </summary>
        public System.String PackageDescription { get; set; }
        /// <summary>
        /// UsageName (from usp_select_CustomerRequirement)
        /// </summary>
        public System.String UsageName { get; set; }
        /// <summary>
        /// CustomerRequirementValue (from usp_select_CustomerRequirement)
        /// </summary>
        public System.Double CustomerRequirementValue { get; set; }
        /// <summary>
        /// ClosedReason (from usp_select_CustomerRequirement)
        /// </summary>
        public System.String ClosedReason { get; set; }
        /// <summary>
        /// DivisionName (from usp_select_Credit)
        /// </summary>
        public System.String DivisionName { get; set; }
        /// <summary>
        /// Status (from usp_selectAll_CustomerRequirement_open_for_Login)
        /// </summary>
        public System.String Status { get; set; }
        /// <summary>
        /// CreditLimit (from usp_selectAll_CustomerRequirement_open_for_Login)
        /// </summary>
        public System.Double? CreditLimit { get; set; }
        /// <summary>
        /// Balance (from usp_selectAll_CustomerRequirement_open_for_Login)
        /// </summary>
        public System.Double? Balance { get; set; }
        /// <summary>
        /// DaysOverdue (from usp_selectAll_CustomerRequirement_open_for_Login)
        /// </summary>
        public System.Int32? DaysOverdue { get; set; }
        /// <summary>
        /// ClientName (from usp_source_CustomerRequirement)
        /// </summary>
        public System.String ClientName { get; set; }
        /// <summary>
        /// Traceability
        /// </summary>
        public System.Boolean? Traceability { get; set; }

        /// <summary>
        /// BOMNo (from Table)
        /// </summary>
        public System.Int32? BOMManagerNo { get; set; }
        /// <summary>
        /// BOMHeader
        /// </summary>
        public System.String BOMManagerHeader { get; set; }
        /// <summary>
        /// BOMCode
        /// </summary>
        public System.String BOMManagerCode { get; set; }
        public string BOMManagerFullName { get; set; }
        public System.Int32? POHubReleaseBy { get; set; }
        public System.Int32? RequestToPOHubBy { get; set; }
        public int? SourcingResultId { get; set; }
        /// <summary>
        /// Price (from Table)
        /// </summary>
        public System.Double ConvertedTargetValue { get; set; }
        public System.String BOMManagerCurrencyCode { get; set; }
        public int? PurchaseQuoteNumber { get; set; }
        public int? PurchaseQuoteId { get; set; }
        public System.String BOMManagerStatus { get; set; }
        public System.Double PHPrice { get; set; }
        public System.String PHCurrencyCode { get; set; }
        public int? POHubCompany { get; set; }
        public System.Boolean? FactorySealed { get; set; }
        public System.String MSL { get; set; }
        public System.Int32 AllSorcingHasDelDate { get; set; }
        public int AllSorcingHasProduct { get; set; }
        public System.Boolean? AS9120 { get; set; }
        public System.Int32 SourcingResult { get; set; }
        public int? SourcingResultNo { get; set; }

        public System.Boolean? PQA { get; set; }
        public System.Boolean? Obsolete { get; set; }
        public System.Boolean? LastTimeBuy { get; set; }
        public System.Boolean? RefirbsAcceptable { get; set; }
        public System.Boolean? TestingRequired { get; set; }
        public System.Double? TargetSellPrice { get; set; }
        public System.Double? CompetitorBestOffer { get; set; }
        public System.DateTime? CustomerDecisionDate { get; set; }
        public System.DateTime? RFQClosingDate { get; set; }
        public System.Int32? QuoteValidityRequired { get; set; }
        public System.Int32? Type { get; set; }
        public System.Boolean? OrderToPlace { get; set; }
        public System.Int32? RequirementforTraceability { get; set; }
        public System.String QuoteValidityText { get; set; }
        public System.String ReqTypeText { get; set; }
        public System.String ReqForTraceabilityText { get; set; }
        public System.Boolean? IsGlobalCurrencySame { get; set; }
        public System.Boolean? HasClientSourcingResult { get; set; }
        public System.Boolean? HasHubSourcingResult { get; set; }
        public System.String EAU { get; set; }
        public System.Int32? ClientGlobalCurrencyNo { get; set; }
        public System.Int32? ReqGlobalCurrencyNo { get; set; }
        public System.String ClientCurrencyCode { get; set; }
        public System.Int32? ClientCurrencyNo { get; set; }
        public System.String ReqNotes { get; set; }
        /// ClientCode
        /// </summary>
        public System.String ClientCode { get; set; }
        /// <summary>
        public Boolean? IsNoBid { get; set; }
        public System.String NoBidNotes { get; set; }
        /// <summary>
        /// IsCurrencyInSameFaimly
        /// </summary>
        public System.Boolean? IsCurrencyInSameFaimly { get; set; }
        public System.Boolean? AlternativesAccepted { get; set; }
        public System.Boolean? RepeatBusiness { get; set; }
        public System.DateTime? DateRequestToPOHub { get; set; }
        public System.Int32 POCurrencyNo { get; set; }

        public System.DateTime? ExpeditDate { get; set; }
        public System.Int32? UpdateByPH { get; set; }
        public System.Boolean? ProductInactive { get; set; }
        public System.String DutyCode { get; set; }
        public System.Double? DutyRate { get; set; }
        public System.String ValidateMessage { get; set; }
        public System.Int32? MSLLevelNo { get; set; }
        public System.Boolean? IsProdHaz { get; set; }
        public System.Double? TotalValue { get; set; }
        public System.String TotalValueStr { get; set; }
        public System.Double? TotalInBase { get; set; }
        public System.String TotalInBaseStr { get; set; }
        /// <summary>
        /// AlternateStatus (from Table)
        /// </summary>
        public System.Byte? AlternateStatus { get; set; }
        //[001] start
        public string SalesOrderNumber { get; set; }
        //[001] end

        //[002] start
        public System.Int32 ClientBOMManagerId { get; set; }
        public System.String ClientBOMManagerCode { get; set; }
        public string ClientBOMManagerName { get; set; }
        public System.DateTime? ImportDateFrom { get; set; }
        public System.DateTime? ImportDateTo { get; set; }
        public System.DateTime? ImportDate { get; set; }
        public System.Int32 NoOfRequirements { get; set; }
        //[002] end

        //[003] start
        public System.Int32? RecordsProcessed { get; set; }
        public System.Int32? RecordsRemaining { get; set; }
        //[003] end

        //[001] start
        public System.Int32 ID { get; set; }
        public System.String Number { get; set; }
        public System.String ResultType { get; set; }
        //[001] end
        public System.Int32? TotalCount { get; set; }

        /// <summary>
        /// CountryOfOrigin
        /// </summary>
        public System.String CountryOfOrigin { get; set; }
        /// <summary>
        /// CountryOfOriginNo
        /// </summary>
        public System.Int32? CountryOfOriginNo { get; set; }
        /// <summary>
        /// LifeCycleStage
        /// </summary>
        public System.String LifeCycleStage { get; set; }
        /// <summary>
        /// HTSCode
        /// </summary>
        public System.String HTSCode { get; set; }
        /// <summary>
        /// AveragePrice
        /// </summary>
        public System.Double? AveragePrice { get; set; }
        /// <summary>
        /// Packaging
        /// </summary>
        public System.String Packaging { get; set; }
        /// <summary>
        /// PackagingSize
        /// </summary>
        public System.String PackagingSize { get; set; }
        public System.String Descriptions { get; set; }
        public System.String IHSCurrencyCode { get; set; }
        public System.Boolean? IsOrderViaIPOonly { get; set; }
        public System.Boolean? IsRestManufaturer { get; set; }
        public System.Int32? PartEditStatus { get; set; }
        public System.String IHSECCNCodeDefination { get; set; }


        public System.Boolean? PriceIssueBuyAndSell { get; set; }
        /// <summary>
        /// IsRestrictedProduct
        /// </summary>
        public System.Boolean? IsRestrictedProduct { get; set; }
        /// <summary>
        /// ECCNNotify
        /// </summary>
        public System.Boolean? ECCNNotify { get; set; }
        /// <summary>
        /// EccnSubject
        /// </summary>
        public System.String EccnSubject { get; set; }
        /// <summary>
        /// EccnMessage
        /// </summary>
        public System.String EccnMessage { get; set; }

        public System.String StockAvailableDetail { get; set; }
        public System.String WarningMessage { get; set; }

        public System.String StockAlerturl { get; set; }
        public System.Int32? InStock { get; set; }
        public System.Int32? OnOrder { get; set; }
        public System.Int32? Allocated { get; set; }
        public System.Int32? Available { get; set; }
        public System.String UserName { get; set; }
        public System.String ToEmail { get; set; }
        public System.Int32? stockId { get; set; }
        public System.Boolean Inactive { get; set; }
        public System.Int32? ReleaseBy { get; set; }
        public System.DateTime? DateRelease { get; set; }
        //public System.String BOMManagerStatus { get; set; }
        public System.Int32? BomManagerCount { get; set; }
        public int? StatusValue { get; set; }
        public System.String Currency_Code { get; set; }
        public System.String CurrentSupplier { get; set; }
        public System.DateTime? QuoteRequired { get; set; }
        public System.Int32? AllItemHasSourcing { get; set; }
        public string Releasedby { get; set; }
        public string Requestedby { get; set; }
        public string AssignedUser { get; set; }
        public int NoBidCount { get; set; }
        public System.Int32? Contact2Id { get; set; }
        public System.String Contact2Name { get; set; }
        public System.String ValidationMessage { get; set; }
        public System.Boolean IsReqInValid { get; set; }
        public System.String ReqSalesPerson { get; set; }
        public System.String ReqSalesPersonName { get; set; }
        public System.String SupportTeamMemberNoAsString { get; set; }
        public System.Boolean? BOM { get; set; }
        public int BOMItemsCount { get; set; }
        public System.Boolean? IsPrimarySourceActual { get; set; }
        public System.Boolean? IsPrimarySource { get; set; }
        public System.Boolean? QuoteGenerated { get; set; }
        public System.Int32? QuoteId { get; set; }
        public System.Int32? QuoteNumber { get; set; }
        public System.Int32? ReqStatus { get; set; }
        public System.Boolean? AutosourcingStatus { get; set; }
        public System.String ReqStatusText { get; set; }
        public int OfferCount { get; set; }
        public System.String GeneratedFilename { get; set; }
        /// <summary>
        /// Release note after releasd BOM items
        /// </summary>
        public System.String ReleaseNote { get; set; }
        public System.String CompanyAdvisoryNotes { get; set; }
        public System.String MfrAdvisoryNotes { get; set; }
        #endregion

    }
}
