﻿SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

/*   
===========================================================================================  
TASK			UPDATED BY			DATE			ACTION		DESCRIPTION  
[US-211441]		Trung Pham Van		24-Sep-2024		CREATE		Clone Customer Requirement By CustomerRequirementId
[US-211441]		Trung Pham Van		14-Sep-2024		CREATE		Just clone new Req with some value
[US-221304]		Trung Pham Van		14-Nov-2024		UPDATE		Add NewOfferPriceFromProspective column
===========================================================================================  
*/
CREATE OR ALTER PROCEDURE [dbo].[usp_Clone_Customer_Requirement] 
	@CustomerRequirementId INT, @CurrencyNo INT, @InsertedId INT OUTPUT
AS
BEGIN
	DECLARE @ClientNo INT,
		@UpdatedBy INT

	SELECT @ClientNo = ClientNo,
		@UpdatedBy = UpdatedBy
	FROM tbCustomerRequirement
	WHERE CustomerRequirementId = @CustomerRequirementId

	DECLARE @CustomerRequirementNumber INT

	EXEC usp_select_CustomerRequirement_NextNumber @ClientNo,
		@UpdatedBy,
		@CustomerRequirementNumber OUTPUT

	INSERT INTO tbCustomerRequirement (
		CustomerRequirementNumber,
		ClientNo,
		FullPart,
		Part,
		ManufacturerNo,
		DateCode,
		PackageNo,
		Quantity,
		Price,
		CurrencyNo,
		ReceivedDate,
		Salesman,
		DatePromised,
		Notes,
		Instructions,
		Shortage,
		CompanyNo,
		ContactNo,
		Alternate,
		OriginalCustomerRequirementNo,
		ReasonNo,
		ProductNo,
		CustomerPart,
		Closed,
		ROHS,
		UpdatedBy,
		DLUP,
		UsageNo,
		FullCustomerPart,
		BOM,
		BOMName,
		PartWatch,
		BOMNo,
		POHubReleaseBy,
		DatePOHubRelease,
		PHPrice,
		FactorySealed,
		MSL,
		PartialQuantityAcceptable,
		Obsolete,
		LastTimeBuy,
		RefirbsAcceptable,
		TestingRequired,
		TargetSellPrice,
		CompetitorBestOffer,
		CustomerDecisionDate,
		RFQClosingDate,
		QuoteValidityRequired,
		ReqType,
		OrderToPlace,
		ReqForTraceability,
		EAU,
		HasClientSourcingResult,
		HasHubSourcingResult,
		IsNoBid,
		NoBidNotes,
		AlternativesAccepted,
		RepeatBusiness,
		ExpediteDate,
		IsAltAttached,
		AlternateStatus,
		ClientBOMNo,
		SupportTeamMemberNo,
		CountryOfOriginNo,
		LifeCycleStage,
		HTSCode,
		AveragePrice,
		Packing,
		PackagingSize,
		Descriptions,
		IHSPartsNo,
		IHSCurrencyCode,
		IHSProduct,
		RefIdHK,
		ECCNCode,
		ParentRequirementNo,
		REQStatus,
		Sequence,
		PartWatch_HUBIPO,
		PartWatch_HUBIPOBy,
		PartWatch_HUBIPODate,
		BOMManagerNo,
		IsGroupAssignment,
		UpdateByPH,
		AS6081,
		ReleaseNote
		)
	SELECT @CustomerRequirementNumber,
		ClientNo,
		FullPart,
		Part,
		ManufacturerNo,
		DateCode,
		PackageNo,
		Quantity,
		Price,
		CurrencyNo,
		ReceivedDate,
		Salesman,
		DatePromised,
		Notes,
		Instructions,
		Shortage,
		CompanyNo,
		ContactNo,
		Alternate,
		OriginalCustomerRequirementNo,
		ReasonNo,
		ProductNo,
		CustomerPart,
		Closed,
		ROHS,
		UpdatedBy,
		DLUP,
		UsageNo,
		FullCustomerPart,
		BOM,
		BOMName,
		PartWatch,
		BOMNo,
		NULL,
		DatePOHubRelease,
		PHPrice,
		FactorySealed,
		MSL,
		PartialQuantityAcceptable,
		Obsolete,
		LastTimeBuy,
		RefirbsAcceptable,
		TestingRequired,
		TargetSellPrice,
		CompetitorBestOffer,
		CustomerDecisionDate,
		RFQClosingDate,
		QuoteValidityRequired,
		ReqType,
		OrderToPlace,
		ReqForTraceability,
		EAU,
		HasClientSourcingResult,
		1,
		IsNoBid,
		NoBidNotes,
		AlternativesAccepted,
		RepeatBusiness,
		ExpediteDate,
		IsAltAttached,
		AlternateStatus,
		ClientBOMNo,
		SupportTeamMemberNo,
		CountryOfOriginNo,
		LifeCycleStage,
		HTSCode,
		AveragePrice,
		Packing,
		PackagingSize,
		Descriptions,
		IHSPartsNo,
		IHSCurrencyCode,
		IHSProduct,
		RefIdHK,
		ECCNCode,
		ParentRequirementNo,
		1,
		Sequence,
		PartWatch_HUBIPO,
		PartWatch_HUBIPOBy,
		PartWatch_HUBIPODate,
		BOMManagerNo,
		IsGroupAssignment,
		UpdateByPH,
		AS6081,
		ReleaseNote
	FROM tbCustomerRequirement
	WHERE CustomerRequirementId = @CustomerRequirementId;

	SELECT @InsertedId = SCOPE_IDENTITY();
END
