Type.registerNamespace("Rebound.GlobalTrader.Site.Pages.Setup");Rebound.GlobalTrader.Site.Pages.Setup.CD_LocalCurrency=function(n){Rebound.GlobalTrader.Site.Pages.Setup.CD_LocalCurrency.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Pages.Setup.CD_LocalCurrency.prototype={get_ctlLocalCurrency:function(){return this._ctlLocalCurrency},set_ctlLocalCurrency:function(n){this._ctlLocalCurrency!==n&&(this._ctlLocalCurrency=n)},get_ctlExchangeRates:function(){return this._ctlExchangeRates},set_ctlExchangeRates:function(n){this._ctlExchangeRates!==n&&(this._ctlExchangeRates=n)},initialize:function(){Rebound.GlobalTrader.Site.Pages.Setup.CD_LocalCurrency.callBaseMethod(this,"initialize")},goInit:function(){this._ctlLocalCurrency&&this._ctlLocalCurrency.addSelectCurrency(Function.createDelegate(this,this.ctlLocalCurrency_SelectCurrency));Rebound.GlobalTrader.Site.Pages.Setup.CD_LocalCurrency.callBaseMethod(this,"goInit")},dispose:function(){this.isDisposed||(this._ctlLocalCurrency&&this._ctlLocalCurrency.dispose(),this._ctlLocalCurrency=null,this._ctlExchangeRates=null,Rebound.GlobalTrader.Site.Pages.Setup.CD_LocalCurrency.callBaseMethod(this,"dispose"))},ctlLocalCurrency_SelectCurrency:function(){this._ctlExchangeRates._intCurrencyID=this._ctlLocalCurrency._intLocalCurrencyID;this._ctlExchangeRates.show(!this._ctlLocalCurrency._blnCurrencyIsBase);this._ctlExchangeRates.refresh();this._ctlLocalCurrency._tbl.resizeColumns()}};Rebound.GlobalTrader.Site.Pages.Setup.CD_LocalCurrency.registerClass("Rebound.GlobalTrader.Site.Pages.Setup.CD_LocalCurrency",Rebound.GlobalTrader.Site.Pages.Content,Sys.IDisposable);