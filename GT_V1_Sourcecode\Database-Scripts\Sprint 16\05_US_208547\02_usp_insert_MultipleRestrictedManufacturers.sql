﻿
GO

/****** Object:  StoredProcedure [dbo].[usp_insert_MultipleRestrictedManufacturers]    Script Date: 12/4/2024 4:40:38 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


/*
============================================================================================================================= 
TASK			UPDATED BY       DATE				ACTION		DESCRIPTION  
[US-208547]     Phuc Hoang		 01-Dec-2024		CREATE		Company Settings - Mass Restricted/ Unrestricted Manufacturer
=============================================================================================================================  
*/

CREATE OR ALTER PROCEDURE [dbo].[usp_insert_MultipleRestrictedManufacturers] (
    @ClientNo INT,
    @ManufacturerNos NVARCHAR(2000),
	@MFRNameSuffix NVARCHAR(255) = Null,
    @Notes NVARCHAR(500) = Null,
    @InActive BIT = Null,
    @UpdatedBy INT = Null,
	@RestrictedManufacturerNames NVARCHAR(MAX) OUTPUT
)               
AS 
BEGIN 	
	DECLARE @ManufacturerCount INT = 0;

	SELECT DISTINCT String as ManufacturerNo
	INTO #tempManufacturersNo
	FROM dbo.[ufn_splitString](@ManufacturerNos, '||');

	SELECT DISTINCT temp.ManufacturerNo, m.ManufacturerName
	INTO #tempRestrictedManufacturerNames
	FROM #tempManufacturersNo temp
	INNER JOIN tbRestrictedManufacturer rm ON temp.ManufacturerNo = rm.ManufacturerNo
	INNER JOIN tbManufacturer m ON temp.ManufacturerNo = m.ManufacturerId
	WHERE rm.ClientNo = @ClientNo AND ISNULL(m.Inactive, 0) = 0;

	SELECT @ManufacturerCount = COUNT(ManufacturerName)
	FROM #tempRestrictedManufacturerNames;
	
	IF @ManufacturerCount > 0
	BEGIN
		DECLARE @results NVARCHAR(MAX) = '';
		
		SELECT @results = COALESCE(@results + '||', '') +  convert(NVARCHAR(255), ManufacturerName)
		FROM #tempRestrictedManufacturerNames; 

		SET @RestrictedManufacturerNames = @results;

		DROP TABLE #tempRestrictedManufacturerNames;
	END

	ELSE
	BEGIN
		SELECT temp.ManufacturerNo, m.ManufacturerName
		INTO #tempRestrictedManufacturers
		FROM #tempManufacturersNo temp
		INNER JOIN tbManufacturer m ON temp.ManufacturerNo = m.ManufacturerId;

		INSERT INTO dbo.tbRestrictedManufacturer (
				ClientNo,
				ManufacturerNo,
				Notes,
				Inactive,
				UpdatedBy,
				DLUP,
				ManufacturerNameSuffix
			)
		SELECT DISTINCT
				@ClientNo,
				m.ManufacturerId,
				@Notes,
				@InActive,
				@UpdatedBy,
				CURRENT_TIMESTAMP,
				@MFRNameSuffix
		FROM tbManufacturer m
		INNER JOIN #tempRestrictedManufacturers temp ON temp.ManufacturerName = m.ManufacturerName
		WHERE ISNULL(m.Inactive, 0) = 0;

		SET @RestrictedManufacturerNames = '';

		DROP TABLE #tempRestrictedManufacturers;
	END
	
	DROP TABLE #tempManufacturersNo;

END 
GO


