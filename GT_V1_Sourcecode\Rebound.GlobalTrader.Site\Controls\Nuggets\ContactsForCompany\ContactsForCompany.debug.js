///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//Marker     Changed by      Date         Remarks
//[001]      Vinay           09/07/2012   This need for Rebound- Invoice bulk Emailer
//[002]      Vinay           04/10/2012   Degete Ref:#26#  - Add two more columns contact to identify Default Purchase ledger and Default Sales ledger
//[003]      Vinay           23/11/2015   ESMS Ticket Number : - Erroe while uncheck the Default so
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Nuggets");

Rebound.GlobalTrader.Site.Controls.Nuggets.ContactsForCompany = function(element) { 
	Rebound.GlobalTrader.Site.Controls.Nuggets.ContactsForCompany.initializeBase(this, [element]);
	this._intCompanyID = -1;
	this._intContactID = -1;
	this._intNumberOfContacts = 0;
};

Rebound.GlobalTrader.Site.Controls.Nuggets.ContactsForCompany.prototype = {

    get_intCompanyID: function() { return this._intCompanyID; }, set_intCompanyID: function(value) { if (this._intCompanyID !== value) this._intCompanyID = value; },
    get_intContactID: function() { return this._intContactID; }, set_intContactID: function(value) { if (this._intContactID !== value) this._intContactID = value; },
    get_ibtnDelete: function() { return this._ibtnDelete; }, set_ibtnDelete: function(value) { if (this._ibtnDelete !== value) this._ibtnDelete = value; },
    get_ibtnAdd: function() { return this._ibtnAdd; }, set_ibtnAdd: function(value) { if (this._ibtnAdd !== value) this._ibtnAdd = value; },
    get_ibtnMakeDefaultSO: function() { return this._ibtnMakeDefaultSO; }, set_ibtnMakeDefaultSO: function(value) { if (this._ibtnMakeDefaultSO !== value) this._ibtnMakeDefaultSO = value; },
    get_ibtnMakeDefaultPO: function() { return this._ibtnMakeDefaultPO; }, set_ibtnMakeDefaultPO: function(value) { if (this._ibtnMakeDefaultPO !== value) this._ibtnMakeDefaultPO = value; },
    get_tbl: function() { return this._tbl; }, set_tbl: function(value) { if (this._tbl !== value) this._tbl = value; },
    get_strCompanyName: function() { return this._strCompanyName; }, set_strCompanyName: function(value) { if (this._strCompanyName !== value) this._strCompanyName = value; },
    get_strContactName: function() { return this._strContactName; }, set_strContactName: function(value) { if (this._strContactName !== value) this._strContactName = value; },
    //[002] code start
    get_ibtnDefaultPOLedger: function() { return this._ibtnDefaultPOLedger; }, set_ibtnDefaultPOLedger: function(value) { if (this._ibtnDefaultPOLedger !== value) this._ibtnDefaultPOLedger = value; },
    get_ibtnDefaultSOLedger: function() { return this._ibtnDefaultSOLedger; }, set_ibtnDefaultSOLedger: function(value) { if (this._ibtnDefaultSOLedger !== value) this._ibtnDefaultSOLedger = value; },
    //[002] code end
    addSelectContact: function(handler) { this.get_events().addHandler("SelectContact", handler); },
    removeSelectContact: function(handler) { this.get_events().removeHandler("SelectContact", handler); },
    onSelectContact: function() {
        var handler = this.get_events().getHandler("SelectContact");
        if (handler) handler(this, Sys.EventArgs.Empty);
    },

    initialize: function() {
        Rebound.GlobalTrader.Site.Controls.Nuggets.ContactsForCompany.callBaseMethod(this, "initialize");
        this.addRefreshEvent(Function.createDelegate(this, this.getData));
        this.addRolledOut(Function.createDelegate(this, this.resizeTableColumns));
        this._strPathToData = "controls/Nuggets/ContactsForCompany";
        this._strDataObject = "ContactsForCompany";
        this._tbl.addSelectedIndexChanged(Function.createDelegate(this, this.tbl_SelectedIndexChanged));

        //add form
        if (this._ibtnAdd) {
            $R_IBTN.addClick(this._ibtnAdd, Function.createDelegate(this, this.showAddForm));
            this._frmAdd = $find(this._aryFormIDs[0]);
            this._frmAdd.addCancel(Function.createDelegate(this, this.cancelAdd));
            this._frmAdd.addSaveComplete(Function.createDelegate(this, this.saveAddComplete));
            this._frmAdd.addSaveError(Function.createDelegate(this, this.saveAddError));
        }

        //delete form
        if (this._ibtnDelete) {
            $R_IBTN.addClick(this._ibtnDelete, Function.createDelegate(this, this.showDeleteForm));
            this._frmDelete = $find(this._aryFormIDs[1]);
            this._frmDelete.addNotConfirmed(Function.createDelegate(this, this.hideDeleteForm));
            this._frmDelete.addSaveComplete(Function.createDelegate(this, this.saveDeleteOK));
            this._frmDelete.addSaveError(Function.createDelegate(this, this.saveDeleteError));
        }

        //make default form
        if (this._ibtnMakeDefaultSO || this._ibtnMakeDefaultPO || this._ibtnDefaultPOLedger || this._ibtnDefaultSOLedger) {
            //[003] code start
            if (this._ibtnMakeDefaultSO) $R_IBTN.addClick(this._ibtnMakeDefaultSO, Function.createDelegate(this, this.showMakeDefaultSOForm));
            //[003] code end
            if (this._ibtnMakeDefaultPO) $R_IBTN.addClick(this._ibtnMakeDefaultPO, Function.createDelegate(this, this.showMakeDefaultPOForm));
            //[002] code start
            if (this._ibtnDefaultPOLedger) $R_IBTN.addClick(this._ibtnDefaultPOLedger, Function.createDelegate(this, this.showMakeDefaultPOLedgerForm));
            if (this._ibtnDefaultSOLedger) $R_IBTN.addClick(this._ibtnDefaultSOLedger, Function.createDelegate(this, this.showMakeDefaultSOLedgerForm));
            //[002] code end
            this._frmMakeDefault = $find(this._aryFormIDs[2]);
            this._frmMakeDefault.addNotConfirmed(Function.createDelegate(this, this.hideMakeDefaultForm));
            this._frmMakeDefault.addSaveComplete(Function.createDelegate(this, this.saveMakeDefaultOK));
            this._frmMakeDefault.addSaveError(Function.createDelegate(this, this.saveMakeDefaultError));
        }

        //get data
        if (!this._blnIsNoDataFound) this.getData();
    },

    dispose: function() {
        if (this.isDisposed) return;
        if (this._ibtnMakeDefaultSO) $R_IBTN.clearHandlers(this._ibtnMakeDefaultSO);
        if (this._ibtnMakeDefaultPO) $R_IBTN.clearHandlers(this._ibtnMakeDefaultPO);
        //[002] code start
        if (this._ibtnDefaultPOLedger) $R_IBTN.clearHandlers(this._ibtnDefaultPOLedger);
        if (this._ibtnDefaultSOLedger) $R_IBTN.clearHandlers(this._ibtnDefaultSOLedger);
        //[002] code end
        if (this._ibtnAdd) $R_IBTN.clearHandlers(this._ibtnAdd);
        if (this._ibtnDelete) $R_IBTN.clearHandlers(this._ibtnDelete);
        if (this._tbl) this._tbl.dispose();
        if (this._frmAdd) this._frmAdd.dispose();
        if (this._frmDelete) this._frmDelete.dispose();
        if (this._frmMakeDefault) this._frmMakeDefault.dispose();
        this._intCompanyID = null;
        this._intContactID = null;
        this._intNumberOfContacts = null;
        this._ibtnDelete = null;
        this._ibtnAdd = null;
        this._ibtnMakeDefaultSO = null;
        this._ibtnMakeDefaultPO = null;
        this._tbl = null;
        this._strCompanyName = null;
        this._strContactName = null;
        this._frmAdd = null;
        this._frmDelete = null;
        this._frmMakeDefault = null;
        //[002] code start
        this._ibtnDefaultPOLedger = null;
        this._ibtnDefaultSOLedger = null;
        //[002] code end
        Rebound.GlobalTrader.Site.Controls.Nuggets.ContactsForCompany.callBaseMethod(this, "dispose");
    },

    getData: function() {
        this.enableButtons(false);
        this.getData_Start();
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData(this._strPathToData);
        obj.set_DataObject(this._strDataObject);
        obj.set_DataAction("GetContactList");
        obj.addParameter("id", this._intCompanyID);
        obj.addDataOK(Function.createDelegate(this, this.getDataOK));
        obj.addError(Function.createDelegate(this, this.getDataError));
        obj.addTimeout(Function.createDelegate(this, this.getDataError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    getDataOK: function(args) {
        var res = args._result;
        this._tbl.clearTable();
        if (res) {
            var RowColor ='';
            this._intNumberOfContacts = 0;
            for (var i = 0; i < res.Contacts.length; i++) {
                var row = res.Contacts[i];
                var aryData = [
						$R_FN.setCleanTextValue(row.Name)
					, $R_FN.setCleanTextValue(row.Tel)
					, (row.Email) ? $R_FN.createNubButton("mailto:" + $R_FN.setCleanTextValue(row.Email), $R_FN.setCleanTextValue(row.Email)) : ""
					, (row.DefaultSO) ? $R_RES.Yes : "-"
					, (row.DefaultPO) ? $R_RES.Yes : "-"
                //[001] code start
					, (row.FinanceContact) ? $R_RES.Yes : "-"
                //[001] code end

                //[002] code start
                    , (row.DefaultPOLedger) ? $R_RES.Yes : "-"
                    , (row.DefaultSOLedger) ? $R_RES.Yes : "-"
                //[002] code end
					];
                //if (row.Inactive) aryData[0] += String.format(" ({0})", $R_RES.Inactive);
                RowColor = row.Inactive == true ? 'RowColorInactive' : ''
                var objExtra = { Inactive: row.Inactive, Name: $R_FN.setCleanTextValue(row.Name), DefaultSO: row.DefaultSO, DefaultPO: row.DefaultPO, DefaultPOLedger: row.DefaultPOLedger, DefaultSOLedger: row.DefaultSOLedger };
                this._tbl.addRowRowColor(aryData, row.ID, (row.ID == this._intContactID), objExtra, (row.DefaultSO || row.DefaultPO || row.DefaultPOLedger || row.DefaultSOLedger) ? "defaultContact" : "", null, row.Inactive, RowColor);
                row = null; aryData = null;
                this._intNumberOfContacts += 1;
            }
        }
        this.resizeTableColumns();
       
        this.getDataOK_End();

        this.enableButtons(true);
    },

    getDataError: function(args) {
        this.showError(true, args.get_ErrorMessage());
    },

    tbl_SelectedIndexChanged: function() {
        this._intContactID = this._tbl._varSelectedValue;
        this._strContactName = this._tbl.getSelectedExtraData().Name;
        this.enableButtons(true);
        this.onSelectContact();
    },

    resizeTableColumns: function() {
        this._tbl.resizeColumns();
    },

    enableButtons: function(bln) {
        if (bln) {
           // alert(this._tbl.getSelectedExtraData().DefaultPO);
            
            if (this._ibtnMakeDefaultPO) $R_IBTN.enableButton(this._ibtnMakeDefaultPO, this._intContactID > 0 && !this._tbl.getSelectedExtraData().DefaultPO);
            if (this._ibtnMakeDefaultSO) $R_IBTN.enableButton(this._ibtnMakeDefaultSO, this._intContactID > 0 && !this._tbl.getSelectedExtraData().DefaultSO);

            //[002] code start
            if (this._ibtnDefaultPOLedger) $R_IBTN.enableButton(this._ibtnDefaultPOLedger, this._intContactID > 0 && !this._tbl.getSelectedExtraData().DefaultPOLedger);
            if (this._ibtnDefaultSOLedger) $R_IBTN.enableButton(this._ibtnDefaultSOLedger, this._intContactID > 0 && !this._tbl.getSelectedExtraData().DefaultSOLedger);
            //[002] code end
            if (this._ibtnDelete) $R_IBTN.enableButton(this._ibtnDelete, this._intNumberOfContacts > 1 && !this._tbl.getSelectedExtraData().Inactive);
        } else {
            if (this._ibtnDelete) $R_IBTN.enableButton(this._ibtnDelete, false);
            if (this._ibtnMakeDefaultPO) $R_IBTN.enableButton(this._ibtnMakeDefaultPO, false);
            if (this._ibtnMakeDefaultSO) $R_IBTN.enableButton(this._ibtnMakeDefaultSO, false);

            //[002] code start
            if (this._ibtnDefaultPOLedger) $R_IBTN.enableButton(this._ibtnDefaultPOLedger, false);
            if (this._ibtnDefaultSOLedger) $R_IBTN.enableButton(this._ibtnDefaultSOLedger, false);
            //[002] code end
        }
    },

    showDeleteForm: function() {
        this._frmDelete._intContactID = this._intContactID;
        this._frmDelete.setFieldValue("ctlContactName", this._tbl.getSelectedCellValue(0));
        this.showForm(this._frmDelete, true);
    },

    hideDeleteForm: function() {
        this.showForm(this._frmDelete, false);
        this._tbl.resizeColumns();
    },

    saveDeleteOK: function() {
        this.hideDeleteForm();
        this.showSavedOK(true, $R_RES.ChangesSavedSuccessfully);
        this._intContactID = -1;
        this.onSelectContact();
        this.getData();
    },

    saveDeleteError: function(args) {
        this.showError(true, this._frmDelete._strErrorMessage);
    },

    showAddForm: function () {
        this._frmAdd._intCompanyID = this._intCompanyID;
        this.showForm(this._frmAdd, true);        
    },

    hideAddForm: function() {
        this.showForm(this._frmAdd, false);
        this._tbl.resizeColumns();
    },

    cancelAdd: function() {
        this.hideAddForm();
    },

    saveAddComplete: function() {
        this.hideAddForm();
        this.showSavedOK(true, $R_RES.ChangesSavedSuccessfully);
        this._intContactID = this._frmAdd._intNewID;
        this.getData();
    },

    saveAddError: function(args) {
        this.showError(true, this._frmAdd._strErrorMessage);
    },

    showMakeDefaultSOForm: function() {
        this._frmMakeDefault.changeMode("SO");
        this.showMakeDefaultForm();
    },

    showMakeDefaultPOForm: function() {
        this._frmMakeDefault.changeMode("PO");
        this.showMakeDefaultForm();
    },
    //[002] code start
    showMakeDefaultPOLedgerForm: function() {
        this._frmMakeDefault.changeMode("POLedger");
        this.showMakeDefaultForm();
    },
    showMakeDefaultSOLedgerForm: function() {
        this._frmMakeDefault.changeMode("SOLedger");
        this.showMakeDefaultForm();
    },
    //[002] code end

    showMakeDefaultForm: function() {
        this._frmMakeDefault._intContactID = this._intContactID;
        this._frmMakeDefault._intCompanyID = this._intCompanyID;
        this._frmMakeDefault.setFieldValue("ctlContactName", this._tbl.getSelectedCellValue(0));
        this.showForm(this._frmMakeDefault, true);
    },

    hideMakeDefaultForm: function() {
        this.showForm(this._frmMakeDefault, false);
        this._tbl.resizeColumns();
    },

    saveMakeDefaultOK: function() {
        this.hideMakeDefaultForm();
        this.showSavedOK(true, $R_RES.ChangesSavedSuccessfully);
        this.onSelectContact();
        this.getData();
    },

    saveMakeDefaultError: function(args) {
        this.showError(true, this._frmMakeDefault._strErrorMessage);
    }

};

Rebound.GlobalTrader.Site.Controls.Nuggets.ContactsForCompany.registerClass("Rebound.GlobalTrader.Site.Controls.Nuggets.ContactsForCompany", Rebound.GlobalTrader.Site.Controls.Nuggets.Base);
