<%@ Control Language="C#" CodeBehind="GILines_NPRPrinted.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Forms.GILines_NPRPrinted" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>

<ReboundUI_Form:DesignBase ID="ctlDB" runat="server" ShowRequiredFieldsExplanation="true" ShowQuickHelp="false">

	<Links>
		<ReboundUI:IconButton ID="ibtnSave" runat="server" IconGroup="Nugget" IconTitleResource="Save" IconCSSType="Save" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
		<ReboundUI:IconButton ID="ibtnCancel" runat="server" IconGroup="Nugget" IconTitleResource="Cancel" IconCSSType="Cancel" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
	</Links>
	
	<Explanation><%=Functions.GetGlobalResource("FormExplanations", "GILines_NPRPrinted")%></Explanation>
	
	<Content>
	
		<ReboundUI_Table:Form id="frm" runat="server">

			<ReboundUI_Form:FormField id="ctlNPRPrinted" runat="server" FieldID="chkNPRPrinted" ResourceTitle="NPRPrinted">
				<Field><ReboundUI:ImageCheckBox ID="chkNPRPrinted" runat="server" Enabled="true" /></Field>
			</ReboundUI_Form:FormField>
			
		</ReboundUI_Table:Form>
		
	</Content>
	
</ReboundUI_Form:DesignBase>
