﻿using Rebound.GlobalTrader.BLL;
using Rebound.GlobalTrader.DAL;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Web.Mvc;

namespace Rebound.GlobalTrader.Site.Areas.BOM.Controllers
{
    public class ProspectiveOffersController : Controller
    {
        // GET: BOM/ProspectiveOffers
        public ActionResult ProspectiveOffersDetail()
        {
            return View();
        }

        [HttpPost]
        public ActionResult GetProspectiveOffersStatus(int prOId)
        {
            string GetProspectiveOffersStatus = string.Empty;
            try
            {
                //TODO
                var data = ProspectiveOffer.GetStatus(prOId);
                string importedDate = data.ImportDate.ToString("dd-MM-yyyy");
                DataTable dt = new DataTable();
                dt.Columns.Add("Supplier");
                dt.Columns.Add("SourceFileName");
                dt.Columns.Add("ImportRowCount");
                dt.Columns.Add("ImportedBy");
                dt.Columns.Add("Date");
                dt.Columns.Add("ImportStatus");

                dt.Rows.Add(new object[]
                {
                    data.SupplierName,
                    data.SourceFileName,
                    data.ImportRowCount,
                    data.ImportedBy,
                    importedDate,
                    data.ImportStatus
                });

                GetProspectiveOffersStatus = dt.Rows[0][5].ToString();

                var jsonData = new
                {
                    SupplierName = dt.Rows[0][0].ToString(),
                    SourceFile = dt.Rows[0][1].ToString(),
                    NoOfRows = dt.Rows[0][2].ToString(),
                    UploadBy = dt.Rows[0][3].ToString(),
                    Date = dt.Rows[0][4].ToString()
                };

                return Json(jsonData, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                new Errorlog().LogMessage("Inside ProspectiveOffersController class, Method name : GetProspectiveOffersStatus. Exception details:" + (ex.InnerException == null ? ex.Message : ex.InnerException.Message));
                return Json(GetProspectiveOffersStatus, JsonRequestBehavior.AllowGet);
            }
            finally
            {
                GetProspectiveOffersStatus = null;
            }
        }

        [HttpGet]
        public ActionResult GetProspectiveOffersLogs(int prospectiveOfferLineId)
        {
            try
            {
                //TODO
                var dataLogs = ProspectiveOffer.GetProspectiveOffersLogs(prospectiveOfferLineId);
                var dataLine = ProspectiveOffer.GetProspectiveOfferLineByID(prospectiveOfferLineId);
                var jsonData = new
                {
                    DataLogs = dataLogs,
                    DataLine = dataLine
                };

                return Json(jsonData, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                new Errorlog().LogMessage("Inside ProspectiveOffersController class, Method name : GetProspectiveOffersStatus. Exception details:" + (ex.InnerException == null ? ex.Message : ex.InnerException.Message));
                return Json(new { message = "There is an error occurred." }, JsonRequestBehavior.AllowGet);
            }
        }

        [HttpPost]
        public ActionResult GetProspectiveOffersLogsSentDate(int prospectiveOfferLineId, List<int> customerRequirementIds)
        {
            try
            {
                //TODO
                var data = ProspectiveOffer.GetProspectiveOffersLogsSentDate(prospectiveOfferLineId, customerRequirementIds);

                var jsonData = data.Select(log => new {
                    log.Manufacturer,
                    log.PartNo,
                    log.SentDate
                });

                return Json(jsonData, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                new Errorlog().LogMessage("Inside ProspectiveOffersController class, Method name : GetProspectiveOffersStatus. Exception details:" + (ex.InnerException == null ? ex.Message : ex.InnerException.Message));
                return Json("There is an error occurred.", JsonRequestBehavior.AllowGet);
            }
        }

        [HttpPost]
        public ActionResult GetPrOLineItem(int proId)
        {
            int curPage = 1;
            if (Request["pq_curpage"] != null)
            {
                curPage = Convert.ToInt32(Request["pq_curpage"].ToString());
            }
            int Rpp = 20;
            if (Request["pq_rpp"] != null)
            {
                Rpp = Convert.ToInt32(Request["pq_rpp"].ToString());
            }

            try
            {
                var result = ProspectiveOffer.GetProspectiveOfferLines(proId, curPage, Rpp);
                foreach (var item in result)
                {
                    double? reqMaxInBasePrice = BLL.Currency.ConvertValueToBaseCurrency(item.HighestOffer, (int)item.CurrencyNo, DateTime.Now);
                    double? reqMinInBasePrice = BLL.Currency.ConvertValueToBaseCurrency(item.LowestOffer, (int)item.CurrencyNo, DateTime.Now);
                    item.HighestOffer = (double)reqMaxInBasePrice;
                    item.LowestOffer = (double)reqMinInBasePrice;
                }
                result[0].curpage = curPage;
                return Json(result, JsonRequestBehavior.AllowGet);
            }

            catch (Exception ex)
            {
                new Errorlog().LogMessage("Inside ProspectiveOffersController class, Method name : GetPOLineItem. Exception details:" + (ex.InnerException == null ? ex.Message : ex.InnerException.Message));
                return this.Content(null, "text/text");
            }
        }

        [HttpPost]
        public ActionResult GetGTOffersDetail(int proId, List<int> ids, List<string> mfrsName, int monthRange = 12, int minOfferQty = 0, int minSOVal = 0)
        {
            int curPage = 1;
            if (Request["pq_curpage"] != null)
            {
                curPage = Convert.ToInt32(Request["pq_curpage"].ToString());
            }
            int Rpp = 20;
            if (Request["pq_rpp"] != null)
            {
                Rpp = Convert.ToInt32(Request["pq_rpp"].ToString());
            }

            try
            {
                var result = new List<ProspectiveOfferLines>();
                if (ids != null)
                {
                    result = ProspectiveOffer.GetGTOffersDetail(proId, ids, monthRange, mfrsName, minOfferQty, minSOVal, curPage, Rpp);
                    foreach (var item in result)
                    {
                        if (item.CusReqCurrencyNo != item.ClientCurrencyNo)
                        {
                            double? reqInBasePrice = BLL.Currency.ConvertValueBetweenTwoCurrencies(item.UploadedOfferPrice, item.CusReqCurrencyNo, item.ClientCurrencyNo, DateTime.Now);
                            item.UploadedOfferPrice = (double)reqInBasePrice;
                        }
                    }

                    if (result.Count > 0)
                    {
                        result[0].curpage = curPage;
                    }
                    else
                    {
                        result = new List<ProspectiveOfferLines>();
                    }
                }
                else
                {
                    result = new List<ProspectiveOfferLines>();
                }
                return Json(result, JsonRequestBehavior.AllowGet);
            }

            catch (Exception ex)
            {
                new Errorlog().LogMessage("Inside ProspectiveOffersController class, Method name : GetPOLineItem. Exception details:" + (ex.InnerException == null ? ex.Message : ex.InnerException.Message));
                return this.Content(null, "text/text");
            }
        }

        [HttpPost]
        public ActionResult SendOffers(int proId, List<int> ids, List<ProspectiveOfferLines> gtOfferIds)
        {
            try
            {
                var customerRequirementIds = gtOfferIds.Select(x => x.CustomerReqId).ToList();
                List<ProspectiveOfferForPowerApp> teamNotifications = CustomerRequirement.ListEmailPowerApp(customerRequirementIds, "Prospective_Offer_Outlook");
                WebServices servic = new WebServices();
                List<int> lst = teamNotifications.Select(x => x.SalemanId).ToList();
                string salesManIds = string.Join(" || ", lst);
                ProspectiveOffer.UpdateProspectiveOfferLine(proId, ids, gtOfferIds);
                servic.NotifyHUBRFQProspectiveOffer(customerRequirementIds, salesManIds, "Prospective Offer Detected", gtOfferIds, proId, ids, teamNotifications);
                //logging for history
                foreach (ProspectiveOfferLines item in gtOfferIds)
                {
                    ProspectiveOffer.InsertProspectiveOfferLogs(item.ProspectiveOfferLineId, item.CustomerReqId);
                }
                return Json(new { mesage = "send successfully" });
            }
            catch (Exception ex)
            {
                new Errorlog().LogMessage("Inside ProspectiveOffersController class, Method name : SendOffers. Exception details:" + (ex.InnerException == null ? ex.Message : ex.InnerException.Message));
                return this.Content(null, "text/text");
            }
        }

        [HttpPost]
        public ActionResult UpdateProspectiveOfferManufacturer(int proId, int proLineId, int manufacturerNo)
        {
            try
            {
                ProspectiveOffer.UpdateProspectiveOfferManufacturer(proId, proLineId, manufacturerNo);
                return Json(new { mesage = "Updated successfully" });
            }
            catch (Exception ex)
            {
                new Errorlog().LogMessage("Inside ProspectiveOffersController class, Method name : UpdateProspectiveOfferManufacturer. Exception details:" + (ex.InnerException == null ? ex.Message : ex.InnerException.Message));
                return this.Content(null, "text/text");
            }
        }
    }
}