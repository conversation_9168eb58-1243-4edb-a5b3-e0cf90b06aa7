﻿using System;
using System.Data;
using System.Data.SqlClient;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;
using System.Web.Caching;
using System.Data.Common;
using System.Reflection;

namespace Rebound.GlobalTrader.DAL.SqlClient
{
	public class SqlDivisionTargetProvider : DivisionTargetProvider
	{


		/// <summary>
		/// GetDivisionAndTeamTarget 
		/// Calls [KPI_GetDivisionTarget]
		/// </summary>
		public override List<DivisionTargetDetails> GetDivisionAndTeamTarget(System.Int32? divisionNo, System.Int32? managerNo, System.Int32 yearNo, System.String table)
		{
			SqlConnection cn = null;
			SqlCommand cmd = null;
			try
			{
				cn = new SqlConnection(this.ConnectionString);
				cmd = new SqlCommand("KPI_GetDivisionTarget", cn);
				cmd.CommandType = CommandType.StoredProcedure;
				cmd.CommandTimeout = 30;
				cmd.Parameters.Add("@DivisionNo", SqlDbType.Int).Value = divisionNo;
				cmd.Parameters.Add("@ManagerNo", SqlDbType.Int).Value = managerNo;
				cmd.Parameters.Add("@yearNo", SqlDbType.Int).Value = yearNo;
				cmd.Parameters.Add("@Table", SqlDbType.NVarChar).Value = table;
				cn.Open();
				DbDataReader reader = ExecuteReader(cmd);
				List<DivisionTargetDetails> lst = new List<DivisionTargetDetails>();
				while (reader.Read())
				{
					DivisionTargetDetails obj = new DivisionTargetDetails();
					obj.DivisionId = GetReaderValue_Int32(reader, "DivisionId", 0);
					obj.DivisionName = GetReaderValue_String(reader, "DivisionName", "");
					obj.PersonType = GetReaderValue_String(reader, "PersonType", "");
					obj.JanTarget = GetReaderValue_NullableDouble(reader, "JanTarget", null);
					obj.FebTarget = GetReaderValue_NullableDouble(reader, "FebTarget", null);
					obj.MarchTarget = GetReaderValue_NullableDouble(reader, "MarchTarget", null);
					obj.AprTarget = GetReaderValue_NullableDouble(reader, "AprTarget", null);
					obj.MayTarget = GetReaderValue_NullableDouble(reader, "MayTarget", null);
					obj.JuneTarget = GetReaderValue_NullableDouble(reader, "JuneTarget", null);
					obj.JulyTarget = GetReaderValue_NullableDouble(reader, "JulyTarget", null);
					obj.AugTarget = GetReaderValue_NullableDouble(reader, "AugTarget", null);
					obj.SepTarget = GetReaderValue_NullableDouble(reader, "SepTarget", null);
					obj.OctTarget = GetReaderValue_NullableDouble(reader, "OctTarget", null);
					obj.NovTarget = GetReaderValue_NullableDouble(reader, "NovTarget", null);
					obj.DecTarget = GetReaderValue_NullableDouble(reader, "DecTarget", null);
					obj.TotalTarget = GetReaderValue_NullableDouble(reader, "TotalTarget", null);
					obj.AllocatedPer = GetReaderValue_NullableDouble(reader, "AllocatedPer", null);
					obj.RowId = GetReaderValue_Int32(reader, "RowId", 0);
					lst.Add(obj);
					obj = null;
				}
				return lst;
			}
			catch (SqlException sqlex)
			{
				//LogException(sqlex);
				throw new Exception("Failed to get Divisions", sqlex);
			}
			finally
			{
				cmd.Dispose();
				cn.Close();
				cn.Dispose();
			}
		}

		/// <summary>
		/// <summary>
		/// Update
		/// Calls [usp_update_DivisionTeamTarget]
		/// </summary>
		public override bool Update(System.Int32? rowId, System.String rowType, System.String columnName, System.Double? targetValue, System.Int32? updatedBy, System.Int32? Year, System.Int32? divisionNo)
		{
			SqlConnection cn = null;
			SqlCommand cmd = null;
			try
			{
				cn = new SqlConnection(this.ConnectionString);
				cmd = new SqlCommand("usp_update_DivisionTeamTarget", cn);
				cmd.CommandType = CommandType.StoredProcedure;
				cmd.Parameters.Add("@RowId", SqlDbType.Int).Value = rowId;
				cmd.Parameters.Add("@RowType", SqlDbType.NVarChar).Value = rowType;
				cmd.Parameters.Add("@ColumnName", SqlDbType.NVarChar).Value = columnName;
				cmd.Parameters.Add("@TargetValue", SqlDbType.Float).Value = targetValue;
				cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
				cmd.Parameters.Add("@Year", SqlDbType.Int).Value = Year;
				cmd.Parameters.Add("@DivisionOrTeamNo", SqlDbType.Int).Value = divisionNo;
				cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
				cn.Open();
				int ret = ExecuteNonQuery(cmd);
				return (ret > 0);
			}
			catch (SqlException sqlex)
			{
				//LogException(sqlex);
				throw new Exception("Failed to update Team Division Traget", sqlex);
			}
			finally
			{
				cmd.Dispose();
				cn.Close();
				cn.Dispose();
			}
		}


		/// <summary>
		/// <summary>
		/// Update
		/// Calls [usp_saveAllDivisionTeamTarget]
		/// </summary>
		public override bool SaveAllDivisionTeamTarget(System.Int32? Year, System.Int32? divisionNo, System.Int32? updatedBy)
		{
			SqlConnection cn = null;
			SqlCommand cmd = null;
			try
			{
				cn = new SqlConnection(this.ConnectionString);
				cmd = new SqlCommand("usp_saveAllDivisionTeamTarget", cn);
				cmd.CommandType = CommandType.StoredProcedure;
				cmd.Parameters.Add("@Year", SqlDbType.Int).Value = Year;
				cmd.Parameters.Add("@DivisionNo", SqlDbType.Int).Value = divisionNo;
				cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
				cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
				cn.Open();
				int ret = ExecuteNonQuery(cmd);
				return (ret > 0);
			}
			catch (SqlException sqlex)
			{
				//LogException(sqlex);
				throw new Exception("Failed to save all division Team Division Traget", sqlex);
			}
			finally
			{
				cmd.Dispose();
				cn.Close();
				cn.Dispose();
			}
		}

		/// <summary>
		/// GetDivisionAndTeamTarget 
		/// Calls [KPI_GetDetailsEditGrid]
		/// </summary>
		public override List<DivisionTargetDetails> GetTargetDataForEdit(System.Int32? clientID, System.Int32? selectedDDLID, System.Int32? Userid, System.Int32 yearNo, System.String reqDataType)
		{
			SqlConnection cn = null;
			SqlCommand cmd = null;
			try
			{
				cn = new SqlConnection(this.ConnectionString);
				cmd = new SqlCommand("KPI_GetDetailsEditGrid", cn);
				cmd.CommandType = CommandType.StoredProcedure;
				cmd.CommandTimeout = 30;
				cmd.Parameters.Add("@DDLRequestType", SqlDbType.NVarChar).Value = reqDataType;
				cmd.Parameters.Add("@Targetno", SqlDbType.Int).Value = selectedDDLID;
				cmd.Parameters.Add("@yearNo", SqlDbType.Int).Value = yearNo;
				cmd.Parameters.Add("@UserId", SqlDbType.Int).Value = Userid;
				cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientID;

				cn.Open();
				DbDataReader reader = ExecuteReader(cmd);
				List<DivisionTargetDetails> lst = new List<DivisionTargetDetails>();
				while (reader.Read())
				{
					DivisionTargetDetails obj = new DivisionTargetDetails();
					obj.RowId = GetReaderValue_Int32(reader, "RowId", 0);
					obj.RowValue = GetReaderValue_Int32(reader, "RowValue", 0);
					obj.RowName = GetReaderValue_String(reader, "RowName", "");
					obj.PersonType = GetReaderValue_String(reader, "PersonType", "");
					obj.JanTarget = GetReaderValue_NullableDouble(reader, "JanTarget", 0);
					obj.FebTarget = GetReaderValue_NullableDouble(reader, "FebTarget", 0);
					obj.MarchTarget = GetReaderValue_NullableDouble(reader, "MarchTarget", 0);
					obj.AprTarget = GetReaderValue_NullableDouble(reader, "AprTarget", 0);
					obj.MayTarget = GetReaderValue_NullableDouble(reader, "MayTarget", 0);
					obj.JuneTarget = GetReaderValue_NullableDouble(reader, "JuneTarget", 0);
					obj.JulyTarget = GetReaderValue_NullableDouble(reader, "JulyTarget", 0);
					obj.AugTarget = GetReaderValue_NullableDouble(reader, "AugTarget", 0);
					obj.SepTarget = GetReaderValue_NullableDouble(reader, "SepTarget", 0);
					obj.OctTarget = GetReaderValue_NullableDouble(reader, "OctTarget", 0);
					obj.NovTarget = GetReaderValue_NullableDouble(reader, "NovTarget", 0);
					obj.DecTarget = GetReaderValue_NullableDouble(reader, "DecTarget", 0);
					obj.IsDrafted = GetReaderValue_Int32(reader, "IsDrafted", 0);
					obj.ClientId = clientID;
					obj.UserId = Userid;

					lst.Add(obj);
					obj = null;
				}
				return lst;
			}
			catch (SqlException sqlex)
			{
				//LogException(sqlex);
				throw new Exception("Failed to get Divisions", sqlex);
			}
			finally
			{
				cmd.Dispose();
				cn.Close();
				cn.Dispose();
			}
		}
		/// <summary>
		/// soorya
		/// Update
		/// Calls [KPI_UpdateDraftGridDetails]
		/// </summary>
		public override bool UpdateDivisionGrid(List<DivisionTargetDetails> updateDivisionsTeamGrid)
		{

			SqlConnection cn = null;
			SqlCommand cmd = null;
			DataTable dt = ListToDataTable(updateDivisionsTeamGrid);
			try
			{
				cn = new SqlConnection(this.ConnectionString);
				cn.Open();


				// Bulk insert into draft temp table
				using (var bulkcopy = new SqlBulkCopy(cn))
				{
					bulkcopy.BulkCopyTimeout = 300;
					bulkcopy.DestinationTableName = "Temp_tbKPIEditGridDataDraft";
					bulkcopy.WriteToServer(dt);
					bulkcopy.Close();
				}

				// Updating draft table and truncating data
				//cmd.CommandTimeout = 300;
				cmd = new SqlCommand("KPI_UpdateDraftGridDetails", cn);
				cmd.CommandType = CommandType.StoredProcedure;
				cmd.ExecuteNonQuery();
			}
			catch (Exception ex)
			{
				var log = ex.Message;
			}
			return true;
		}

		public override bool SubmitEditGridChanges(System.Int32? clientID, System.Int32? Userid)
		{
			SqlConnection cn = null;
			SqlCommand cmd = null;
			try
			{
				cn = new SqlConnection(this.ConnectionString);
				cn.Open();
				// Updating draft table and truncating data
				//cmd.CommandTimeout = 300;
				cmd = new SqlCommand("KPI_UpdateEditGridDetails", cn);
				cmd.CommandType = CommandType.StoredProcedure;
				cmd.Parameters.Add("@ClientID", SqlDbType.Int).Value = clientID;
				cmd.Parameters.Add("@UserId", SqlDbType.Int).Value = Userid;
				cmd.ExecuteNonQuery();
			}
			catch (Exception ex)
			{
				var log = ex.Message;
			}
			finally
			{
				cmd.Dispose();
				cn.Close();
				cn.Dispose();
			}
			return true;
		}

		public DataTable ListToDataTable<T>(List<T> items)
		{
			DataTable dataTable = new DataTable(typeof(T).Name);
			//Get all the properties
			PropertyInfo[] Props = typeof(T).GetProperties(BindingFlags.Public | BindingFlags.Instance);
			foreach (PropertyInfo prop in Props)
			{
				//Setting column names as Property names
				dataTable.Columns.Add(prop.Name);
			}
			foreach (T item in items)
			{
				var values = new object[Props.Length];
				for (int i = 0; i < Props.Length; i++)
				{
					//inserting property values to datatable rows
					values[i] = Props[i].GetValue(item, null);
				}
				dataTable.Rows.Add(values);
			}
			//put a breakpoint here and check datatable
			return dataTable;
		}

	}
}
