<%@ Control Language="C#" CodeBehind="CommunicationLog.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.DataListNuggets.CommunicationLog" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Nugget:DesignBase ID="ctlDB" runat="server" BoxType="List">
	<Links>
		<ReboundUI:IconButton ID="ibtnAdd" runat="server" IconGroup="Nugget" IconTitleResource="Add" IconCSSType="Add" IconButtonMode="Hyperlink"></ReboundUI:IconButton>
		<ReboundUI:IconButton ID="ibtnEdit" runat="server" IconGroup="Nugget" IconTitleResource="Edit" IconCSSType="Edit" IconButtonMode="Hyperlink" IsInitiallyEnabled="false"></ReboundUI:IconButton>
	</Links>
	<Filters>
		<ReboundUI_DataListNugget:Filter ID="ctlFilter" runat="server">
			<FieldsLeft>
				<ReboundUI_FilterDataItemRow:DropDown id="ctlType" runat="server" ResourceTitle="Type" DropDownType="CommunicationLogType" DropDownAssembly="Rebound.GlobalTrader.Site" FilterField="CommunicationLogTypeNo" />
				<ReboundUI_FilterDataItemRow:DropDown id="ctlLogin" runat="server" ResourceTitle="AddedBy" FilterField="LoginNo" DropDownType="Employee" DropDownAssembly="Rebound.GlobalTrader.Site" />
				<ReboundUI_FilterDataItemRow:TextBox id="ctlNotes" runat="server" ResourceTitle="Notes" InitialSearchType="Contains" FilterField="Details" />
			</FieldsLeft>
			<FieldsRight>
				<ReboundUI_FilterDataItemRow:DropDown id="ctlContact" runat="server" DropDownType="ContactsForCompany" DropDownAssembly="Rebound.GlobalTrader.Site" ResourceTitle="Contact" FilterField="ContactNo" />
				<ReboundUI_FilterDataItemRow:DateSelect id="ctlLogDateFrom" runat="server" ResourceTitle="LogDateFrom" FilterField="LogDateFrom" />
				<ReboundUI_FilterDataItemRow:DateSelect id="ctlLogDateTo" runat="server" ResourceTitle="LogDateTo" FilterField="LogDateTo" />
			</FieldsRight>
		</ReboundUI_DataListNugget:Filter>
	</Filters>
	<Forms>
		<ReboundForm:CommunicationLog_AddEdit id="frmAddEdit" runat="server" />
	</Forms>
</ReboundUI_Nugget:DesignBase>
