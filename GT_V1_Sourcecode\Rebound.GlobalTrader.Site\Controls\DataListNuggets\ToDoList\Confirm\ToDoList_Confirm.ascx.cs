using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site;
using Rebound.GlobalTrader.Site.Controls;

namespace Rebound.GlobalTrader.Site.Controls.Forms {
	public partial class ToDoList_Confirm : Base {

		#region Locals
		private string _strTitle_Delete;
		private string _strTitle_MarkComplete;
		private string _strTitle_MarkIncomplete;
		private string _strExplanation_Delete;
		private string _strExplanation_MarkComplete;
		private string _strExplanation_MarkIncomplete;
		#endregion

		#region Properties

		#endregion

		#region Overrides

		/// <summary>
		/// OnInit
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
			base.OnInit(e);
			//TitleText = Functions.GetGlobalResource("FormTitles", "ToDoList_Confirm");
			AddScriptReference("Controls.DataListNuggets.ToDoList.Confirm.ToDoList_Confirm.js");
			_strTitle_Delete = Functions.GetGlobalResource("FormTitles", "ToDo_Delete");
			_strTitle_MarkComplete = Functions.GetGlobalResource("FormTitles", "ToDo_MarkComplete");
			_strTitle_MarkIncomplete = Functions.GetGlobalResource("FormTitles", "ToDo_MarkIncomplete");
			_strExplanation_Delete = Functions.GetGlobalResource("FormExplanations", "ToDo_Delete");
			_strExplanation_MarkComplete = Functions.GetGlobalResource("FormExplanations", "ToDo_MarkComplete");
			_strExplanation_MarkIncomplete = Functions.GetGlobalResource("FormExplanations", "ToDo_MarkIncomplete");
		}

		/// <summary>
		/// OnPreRender
		/// </summary>
		/// <param name="e"></param>
		protected override void OnPreRender(EventArgs e) {
			WireUpControls();
			SetupScriptDescriptors();
			base.OnPreRender(e);
		}

		#endregion

		/// <summary>
		/// Wire up controls to the ascx
		/// </summary>
		private void WireUpControls() {
		}

		/// <summary>
		/// Setup Script Descriptors
		/// </summary>
		private void SetupScriptDescriptors() {
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.Forms.ToDoList_Confirm", ctlDesignBase.ClientID);
			_scScriptControlDescriptor.AddProperty("strTitle_Delete", _strTitle_Delete);
			_scScriptControlDescriptor.AddProperty("strTitle_MarkComplete", _strTitle_MarkComplete);
			_scScriptControlDescriptor.AddProperty("strTitle_MarkIncomplete", _strTitle_MarkIncomplete);
			_scScriptControlDescriptor.AddProperty("strExplanation_Delete", _strExplanation_Delete);
			_scScriptControlDescriptor.AddProperty("strExplanation_MarkComplete", _strExplanation_MarkComplete);
			_scScriptControlDescriptor.AddProperty("strExplanation_MarkIncomplete", _strExplanation_MarkIncomplete);
		}


	}
}