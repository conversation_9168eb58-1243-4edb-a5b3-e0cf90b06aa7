﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

/*   
===========================================================================================  
TASK         UPDATED BY   DATE			ACTION		DESCRIPTION  
[US-210037]  An.TranTan   23-Oct-2024	create		Get Manufacturer advisory notes  
[US-221009]  An.TranTan   29-Nov-2024	Update		Use function to get message 
===========================================================================================  
*/  
CREATE OR ALTER   PROCEDURE  [dbo].[usp_select_ManufacturerAdvisoryNotes]      
  @ManufacturerId INT
  ,@ClientID INT = 0
AS  
BEGIN
	SELECT dbo.ufn_get_MfrNotes(@ManufacturerId,@ClientID) AS AdvisoryNotes
END
GO
/*
exec usp_select_ManufacturerAdvisoryNotes
@ManufacturerId = 9632,
@ClientID = 101
*/

