USE [BorisGlobalTrader]
GO

/****** Object:  StoredProcedure [dbo].[usp_report_Report_130]    Script Date: 5/21/2024 2:34:48 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE PROCEDURE [dbo].[usp_report_Report_130]
    --*****************************************************************************************  
    -- >>>> PLEASE APPLY THE SAME CHANGES TO [dbo].[usp_selectAll_Login_Top_Salespersons] <<<<  
    --*****************************************************************************************  
    --* Summary Shipped Sales by Salesperson  
    --*  
    --* RP 18.10.2009:  
    --* - Move percentage split calculation so 2nd Salesperson works correctly  
    --* - order by Gross Profit, then Margin, then Name  
    --*  
    --* RP 14.10.2009:  
    --* - fix null credits causing zero GP  
    --*  
    --* SK 21.12.2009:  
    --* - correct processing so that Credits do not subtract per salesman percentage  
    --*  
    --* SK 18.12.2009:  
    --* - allow for null values on Credits  
    --*  
    --* SK 02.11.2009:  
    --* - if the price on SO is null use the PO (use vwInvoiceLineAllocation in place of  
    --*   tbSalesOrderLine)  
    --*  
    --* SK 14.10.2009:  
    --* - allow for salesman2 on Credits  
    --*  
    --* SK 30.09.2009:  
    --* - salesman/salesman2 on invoice + minor tweaks  
    --*  
    --* SK 24.06.2009:  
    --* - allow for salesman2  
    --*  
    --* SK 23.05.2009:  
    --* - put in intermediate step to stop over-inclusion of shipping and freight when orders  
    --*   have multiple lines  
    --*  
    --* RP 08.05.2009:  
    --* - complete rewrite!  
    --*  
    --* RP: 29.04.2009  
    --* - took out unnecessary cursor processing  
    --* - catered for time on dates  
    --* - dropped temp tables  
    --* - added IncludeCredits parameter and processing  
    --*****************************************************************************************  
    @ClientNo int,
    @StartDate datetime = NULL,
    @EndDate datetime = NULL,
    @IncludeCredits bit = 1,
    @ViewMyReport bit = 0,
    @IntLoginId int = NULL,
    @IntTeamNo int = NULL,
    @IntDivisionNo int = NULL
AS
SET NOCOUNT ON
SET ANSI_WARNINGS OFF
SET ARITHIGNORE ON

--Summary Shipped Sales by Salesperson  

-- Create the temporary tables  
CREATE TABLE #Invoices
(
    InvoiceId int,
    CurrencyRate float,
    Salesman int,
    ShippingCost float,
    Freight float,
    SalesmanPct float
)

CREATE TABLE #InvoicePreSummary
(
    Salesman int,
    InvoiceId int,
    ShippingCost float,
    Freight float,
    Cost float,
    Resale float,
    SalesmanPct float
)

CREATE TABLE #InvoiceSummary
(
    Salesman int,
    Cost float,
    Resale float,
    SalesmanPct float
)

CREATE TABLE #InvoicePostSummary
(
    Salesman int,
    Cost float,
    Resale float,
    NoOfOrders int,
    NoOfCredits int
)

CREATE TABLE #Credits
(
    CreditNo int,
    Salesman int,
    CurrencyRate float,
    SalesmanPct float
)
CREATE TABLE #CreditSummary
(
    Salesman int,
    Cost float,
    Resale float
)

-- Put the list of invoices into a temporary table  

-- For PO Hub only  
IF @ClientNo = 9999
BEGIN
    INSERT INTO #Invoices
    SELECT cinvl.ClientInvoiceLineId as InvoiceId,
           dbo.ufn_get_exchange_rate(cinv.CurrencyNo, cinv.ClientInvoiceDate),
           cinvL.Salesman,
           isnull(cinv.DeliveryCharge, 0),
           0,
           100
    FROM tbClientInvoiceLine cinvL
        JOIN tbClientInvoice cinv
            ON cinv.ClientInvoiceId = cinvL.ClientInvoiceNo
        LEFT JOIN tbLogin lg
            on lg.LoginId = cinvL.Salesman
    WHERE --VIEW REPORT-- Espire : 31 May 2018  
        -- ((@ViewMyReport = 0)   OR (@ViewMyReport = 1   AND cinvL.Salesman = @IntLoginId))  
        --Espire 28 Dec 2018:  REB12439 - Team and Division reports  
        /* SG 12.01.2021 - Temporarily removed permission check - did not seem to be working after consolidation  
        ((@IntTeamNo IS NULL)  
                                 OR (NOT @IntTeamNo IS NULL  
                                     AND lg.TeamNo = @IntTeamNo))  
                            AND ((@IntDivisionNo IS NULL)  
                                 OR (NOT @IntDivisionNo IS NULL  
                            AND lg.DivisionNo = @IntDivisionNo))  
                            AND ((@IntLoginId IS NULL)  
                                 OR (NOT @IntLoginId IS NULL  
                                     AND (cinvL.Salesman = @IntLoginId )))  
 AND  
*/
        dbo.ufn_get_date_from_datetime(cinv.ClientInvoiceDate)
    BETWEEN @StartDate AND @EndDate


    --prepare the summary of invoices - necessary to get the shipping and freight once per invoice  
    INSERT INTO #InvoicePreSummary
    SELECT i.Salesman,
           i.InvoiceId,
           i.ShippingCost,
           i.Freight / i.CurrencyRate,
           isnull(sum(isnull(cil.LandedCost, 0) * cil.QtyReceived), 0),
           isnull(sum((cil.UnitPrice * cil.QtyReceived) / i.CurrencyRate), 0),
           i.SalesmanPct
    FROM #Invoices i
        LEFT JOIN tbClientInvoiceLine cil
            on cil.ClientInvoiceLineId = i.InvoiceId
    --LEFT JOIN tbGoodsInLine gil on gil.GoodsInLineId = cil.GoodsInLineNo  
    GROUP BY i.Salesman,
             i.InvoiceId,
             i.ShippingCost,
             i.Freight / i.CurrencyRate,
             i.SalesmanPct


    INSERT INTO #InvoiceSummary
    SELECT Salesman,
           (sum(Cost + ShippingCost)),
           (sum(Resale + Freight)),
           SalesmanPct
    FROM #InvoicePreSummary
    GROUP BY Salesman,
             SalesmanPct

    --now summarise to get rid of the different percentages  
    INSERT INTO #InvoicePostSummary
    SELECT i.Salesman,
           sum(i.Cost),
           sum(i.Resale),
           (
               SELECT count(Distinct InvoiceId)
               FROM #Invoices
               WHERE Salesman = i.Salesman
           ),
           0
    FROM #InvoiceSummary i
    GROUP BY i.Salesman


    --Comments the credit     GA**  

    --minus credits if required  
    IF @IncludeCredits = 1
    BEGIN

        --get all credits  
        INSERT INTO #Credits
        SELECT CreditId,
               Salesman,
               dbo.ufn_get_exchange_rate(CurrencyNo, ClientInvoiceDate),
               100
        FROM dbo.vwCredit
            LEFT JOIN tbLogin lg
                on lg.LoginId = Salesman
        WHERE RefClientNo is not NULL
              --VIEW REPORT-- Espire : 31 May 2018  
              --AND ((@ViewMyReport = 0)   OR (@ViewMyReport = 1   AND Salesman = @IntLoginId))  
              --Espire 28 Dec 2018:  REB12439 - Team and Division reports  
              AND (
                      (@IntTeamNo IS NULL)
                      OR (
                             NOT @IntTeamNo IS NULL
                             AND lg.TeamNo = @IntTeamNo
                         )
                  )
              AND (
                      (@IntDivisionNo IS NULL)
                      OR (
                             NOT @IntDivisionNo IS NULL
                             AND lg.DivisionNo = @IntDivisionNo
                         )
                  )
              AND (
                      (@IntLoginId IS NULL)
                      OR (
                             NOT @IntLoginId IS NULL
                             AND (Salesman = @IntLoginId)
                         )
                  )
              AND dbo.ufn_get_date_from_datetime(CreditDate)
              BETWEEN @StartDate AND @EndDate

        --summarise credits -must be c.Salesman so second salesman are represented  
        INSERT INTO #CreditSummary
        SELECT v.Salesman,
               SUM(v.Cost),
               SUM(v.Resale)
        FROM
        (
            SELECT c.Salesman,
                   isnull((((cr.CreditCost + cr.ShippingCost) / 100) * c.SalesmanPct), 0) AS Cost,
                   isnull((((cr.CreditValue + cr.Freight) / 100) * c.SalesmanPct) / c.CurrencyRate, 0) AS Resale
            FROM #Credits c
                JOIN dbo.vwCredit cr
                    ON cr.CreditId = c.CreditNo
        ) AS v
        GROUP BY v.Salesman

        --subtract credits from invoices  
        UPDATE #InvoicePostSummary
        SET Cost = i.Cost - c.Cost,
            Resale = i.Resale - c.Resale,
            NoOfCredits =
            (
                SELECT count(Distinct CreditNo) FROM #Credits WHERE Salesman = i.Salesman
            )
        FROM #InvoicePostSummary i
            JOIN #CreditSummary c
                ON c.Salesman = i.Salesman

        --add companies with no Invoices but some Credits  
        INSERT INTO #InvoicePostSummary
        SELECT c.Salesman,
               -c.Cost,
               -c.Resale,
               0,
               (
                   SELECT count(Distinct CreditNo) FROM #Credits WHERE Salesman = c.Salesman
               )
        FROM #CreditSummary c
        WHERE NOT EXISTS
        (
            SELECT 1 FROM #Invoices WHERE Salesman = c.Salesman
        )

    END --GA**  
END
ELSE
BEGIN

    INSERT INTO #Invoices
    SELECT a.InvoiceId,
           dbo.ufn_get_exchange_rate(a.CurrencyNo, a.InvoiceDate),
           a.Salesman,
           isnull(a.ShippingCost, 0),
           isnull(a.Freight, 0),
           (100 - a.Salesman2Percent)
    FROM dbo.tbInvoice a
        LEFT JOIN tbLogin lg
            on lg.LoginId = a.Salesman
    WHERE a.ClientNo = @ClientNo
          --VIEW REPORT--  
          -- AND ((@ViewMyReport = 0)   OR (@ViewMyReport = 1   AND a.Salesman=@IntLoginId))  
          --Espire 28 Dec 2018:  REB12439 - Team and Division reports  
          AND (
                  (@IntTeamNo IS NULL)
                  OR (
                         NOT @IntTeamNo IS NULL
                         AND lg.TeamNo = @IntTeamNo
                     )
              )
          AND (
                  (@IntDivisionNo IS NULL)
                  OR (
                         NOT @IntDivisionNo IS NULL
                         AND lg.DivisionNo = @IntDivisionNo
                     )
              )
          AND (
                  (@IntLoginId IS NULL)
                  OR (
                         NOT @IntLoginId IS NULL
                         AND (a.Salesman = @IntLoginId)
                     )
              )
          -------END -----  
          AND dbo.ufn_get_date_from_datetime(a.InvoiceDate)
          BETWEEN @StartDate AND @EndDate
          AND a.SupplierRMANo IS NULL

    --and again for salesman2  
    INSERT INTO #Invoices
    SELECT a.InvoiceId,
           dbo.ufn_get_exchange_rate(a.CurrencyNo, a.InvoiceDate),
           a.Salesman2,
           isnull(a.ShippingCost, 0),
           isnull(a.Freight, 0),
           a.Salesman2Percent
    FROM dbo.tbInvoice a
        LEFT JOIN tbLogin lg
            on lg.LoginId = a.Salesman2
    WHERE a.ClientNo = @ClientNo
          --VIEW REPORT--  
          --AND ((@ViewMyReport = 0)   OR (@ViewMyReport = 1   AND a.Salesman2=@IntLoginId))  
          --Espire 28 Dec 2018:  REB12439 - Team and Division reports  
          AND (
                  (@IntTeamNo IS NULL)
                  OR (
                         NOT @IntTeamNo IS NULL
                         AND lg.TeamNo = @IntTeamNo
                     )
              )
          AND (
                  (@IntDivisionNo IS NULL)
                  OR (
                         NOT @IntDivisionNo IS NULL
                         AND lg.DivisionNo = @IntDivisionNo
                     )
              )
          AND (
                  (@IntLoginId IS NULL)
                  OR (
                         NOT @IntLoginId IS NULL
                         AND (
                         (
                             a.Salesman2 = @IntLoginId
                             AND a.Salesman2Percent > 0
                         )
                             )
                     )
              )
          -------END -----  
          AND dbo.ufn_get_date_from_datetime(a.InvoiceDate)
          BETWEEN @StartDate AND @EndDate
          AND a.SupplierRMANo IS NULL
          AND a.Salesman2 IS NOT NULL

    --prepare the summary of invoices - necessary to get the shipping and freight once per invoice  
    INSERT INTO #InvoicePreSummary
    SELECT i.Salesman,
           i.InvoiceId,
           i.ShippingCost,
           i.Freight / i.CurrencyRate,
           isnull(sum(ila.LandedCost * ila.Quantity), 0),
           isnull(sum((ila.Price * ila.Quantity) / i.CurrencyRate), 0),
           i.SalesmanPct
    FROM #Invoices i
        LEFT JOIN dbo.tbInvoiceLine il
            ON il.InvoiceNo = i.InvoiceId
        LEFT JOIN dbo.vwInvoiceLineAllocation ila
            ON ila.InvoiceLineNo = il.InvoiceLineId
    GROUP BY i.Salesman,
             i.InvoiceId,
             i.ShippingCost,
             i.Freight / i.CurrencyRate,
             i.SalesmanPct
    --select * from #InvoicePreSummary  
    --summarise the invoices  
    INSERT INTO #InvoiceSummary
    SELECT Salesman,
           (sum(Cost + ShippingCost)) * (SalesmanPct / 100),
           (sum(Resale + Freight)) * (SalesmanPct / 100),
           SalesmanPct
    FROM #InvoicePreSummary
    GROUP BY Salesman,
             SalesmanPct

    --now summarise to get rid of the different percentages  
    INSERT INTO #InvoicePostSummary
    SELECT i.Salesman,
           sum(i.Cost),
           sum(i.Resale),
           (
               SELECT count(Distinct InvoiceId)
               FROM #Invoices
               WHERE Salesman = i.Salesman
           ),
           0
    FROM #InvoiceSummary i
    GROUP BY i.Salesman



    --minus credits if required  
    IF @IncludeCredits = 1
    BEGIN

        --get all credits  
        INSERT INTO #Credits
        SELECT CreditId,
               Salesman,
               dbo.ufn_get_exchange_rate(CurrencyNo, InvoiceDate),
               (100 - Salesman2Percent)
        FROM dbo.vwCredit c
            LEFT JOIN tbLogin lg
                on lg.LoginId = c.salesman
        WHERE c.ClientNo = @ClientNo
              -- AND ((@ViewMyReport = 0)   OR (@ViewMyReport = 1   AND Salesman2=@IntLoginId))  
              --Espire 28 Dec 2018:  REB12439 - Team and Division reports  
              AND (
                      (@IntTeamNo IS NULL)
                      OR (
                             NOT @IntTeamNo IS NULL
                             AND lg.TeamNo = @IntTeamNo
                         )
                  )
              AND (
                      (@IntDivisionNo IS NULL)
                      OR (
                             NOT @IntDivisionNo IS NULL
                             AND lg.DivisionNo = @IntDivisionNo
                         )
                  )
              AND (
                      (@IntLoginId IS NULL)
                      OR (
                             NOT @IntLoginId IS NULL
                             AND (Salesman = @IntLoginId)
                         )
                  )
              AND dbo.ufn_get_date_from_datetime(CreditDate)
              BETWEEN @StartDate AND @EndDate


        --and again for salesman2  
        INSERT INTO #Credits
        SELECT CreditId,
               Salesman2,
               dbo.ufn_get_exchange_rate(CurrencyNo, InvoiceDate),
               Salesman2Percent
        FROM dbo.vwCredit c
            LEFT JOIN tbLogin lg
                on lg.LoginId = c.Salesman2
        WHERE c.ClientNo = @ClientNo
              -- AND ((@ViewMyReport = 0)   OR (@ViewMyReport = 1   AND Salesman2=@IntLoginId))  
              --Espire 28 Dec 2018:  REB12439 - Team and Division reports  
              AND (
                      (@IntTeamNo IS NULL)
                      OR (
                             NOT @IntTeamNo IS NULL
                             AND lg.TeamNo = @IntTeamNo
                         )
                  )
              AND (
                      (@IntDivisionNo IS NULL)
                      OR (
                             NOT @IntDivisionNo IS NULL
                             AND lg.DivisionNo = @IntDivisionNo
                         )
                  )
              AND (
                      (@IntLoginId IS NULL)
                      OR (
                             NOT @IntLoginId IS NULL
                             AND (Salesman2 = @IntLoginId)
                         )
                  )
              AND dbo.ufn_get_date_from_datetime(CreditDate)
              BETWEEN @StartDate AND @EndDate
              AND Salesman2 IS NOT NULL

        --summarise credits -must be c.Salesman so second salesman are represented  
        INSERT INTO #CreditSummary
        SELECT v.Salesman,
               SUM(v.Cost),
               SUM(v.Resale)
        FROM
        (
            SELECT c.Salesman,
                   isnull((((cr.CreditCost + cr.ShippingCost) / 100) * c.SalesmanPct), 0) AS Cost,
                   isnull((((cr.CreditValue + cr.Freight) / 100) * c.SalesmanPct) / c.CurrencyRate, 0) AS Resale
            FROM #Credits c
                JOIN dbo.vwCredit cr
                    ON cr.CreditId = c.CreditNo
        ) AS v
        GROUP BY v.Salesman

        --subtract credits from invoices  
        UPDATE #InvoicePostSummary
        SET Cost = i.Cost - c.Cost,
            Resale = i.Resale - c.Resale,
            NoOfCredits =
            (
                SELECT count(Distinct CreditNo) FROM #Credits WHERE Salesman = i.Salesman
            )
        FROM #InvoicePostSummary i
            JOIN #CreditSummary c
                ON c.Salesman = i.Salesman

        --add companies with no Invoices but some Credits  
        INSERT INTO #InvoicePostSummary
        SELECT c.Salesman,
               -c.Cost,
               -c.Resale,
               0,
               (
                   SELECT count(Distinct CreditNo) FROM #Credits WHERE Salesman = c.Salesman
               )
        FROM #CreditSummary c
        WHERE NOT EXISTS
        (
            SELECT 1 FROM #Invoices WHERE Salesman = c.Salesman
        )

    END

END


--return the data  
SELECT lg.EmployeeName,
       i.Cost,
       i.Resale,
       --margin & gp should be unaffected by the salesman percent  
       (i.Resale - i.Cost) AS GrossProfit,
       case i.Resale
           WHEN 0 THEN
               -100
           ELSE
       ((i.Resale - i.Cost) / abs(i.Resale)) * 100
       END AS Margin,
       i.NoOfOrders,
       i.NoOfCredits
FROM #InvoicePostSummary i
    JOIN tbLogin lg
        ON lg.LoginId = i.Salesman
ORDER BY lg.EmployeeName

--clean up  
DROP TABLE #Credits
DROP TABLE #CreditSummary
DROP TABLE #Invoices
DROP TABLE #InvoiceSummary
DROP TABLE #InvoicePreSummary
DROP TABLE #InvoicePostSummary
GO


