﻿CREATE OR ALTER PROC [dbo].[usp_get_GermanyExchangeRateAtMonthForCurrency] (
	@CurrencyCode NVARCHAR(5) = null,
	@InvoiceDate datetime = null
)
AS
/*===========================================================================================  
TASK			UPDATED BY       DATE				ACTION		DESCRIPTION  
[US-211683]     An.TranTan		 02-OCT-2024		CREATE		German Invoice: get ExchangeRate at Invoice month for currency
===========================================================================================  
*/
BEGIN
	SELECT 
		ExchangeRateId,
		(SELECT MONTH(MonthInYear + ' 1 2024')) as MonthNum, 
		MonthInYear,
		FromCurrency,
		ToCurrency,
		ExchangeRate
	INTO #tempExchangeRate
	FROM 
		(SELECT TOP 1 ExchangeRateId, CountryName, FromCurrency, ToCurrency, January, February, March, April, May, June, July, August, September, October, November, December
			FROM tbGermanyExchangeRate WITH(NOLOCK)
			WHERE ToCurrency = @CurrencyCode
				AND YEAR(DLUP) = YEAR(@InvoiceDate)
			ORDER BY DLUP DESC
	 ) AS SourceTable
	UNPIVOT 
		(ExchangeRate FOR MonthInYear IN (January, February, March, April, May, June, July, August, September, October, November, December)) AS UnpivotedTable;

	SELECT FromCurrency,
		ToCurrency,
		CAST(ExchangeRate AS DECIMAL(9,5)) AS ExchangeRate
	FROM #tempExchangeRate
	WHERE MonthNum = MONTH(@InvoiceDate) AND ExchangeRate > 0
	
	DROP TABLE #tempExchangeRate
END

GO


