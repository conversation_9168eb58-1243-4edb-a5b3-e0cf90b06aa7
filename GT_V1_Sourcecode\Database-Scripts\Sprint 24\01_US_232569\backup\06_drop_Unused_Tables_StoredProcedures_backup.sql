﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO
/*   
===========================================================================================  
TASK			UPDATED BY      DATE			ACTION		DESCRIPTION  
[US-214760]		An.TranTan		09-Jan-2025		Create		Insert temp column header for HUBRFQ import sourcing result
===========================================================================================  
*/  
CREATE OR ALTER   PROCEDURE [dbo].[usp_saveBOMSourcing_tempHeading]  
    @InsertColumnList NVARCHAR(3000),  
    @SelectColumns NVARCHAR(3000),  
    @UserId INT  
AS  
BEGIN
	--Delete user previous data
	DELETE BorisGlobalTraderImports.dbo.tbBOMSourcing_tempHeading WHERE CreatedBy = @UserId;
  
    DECLARE @DynamicQuery NVARCHAR(4000) = '';  
    SET @DynamicQuery  
        = 'INSERT INTO BorisGlobalTraderImports.dbo.tbBOMSourcing_tempHeading(' 
			+ @InsertColumnList + ', CreatedBy)'
			+ ' Select ' + @SelectColumns +',' + CONVERT(NVARCHAR(10), @UserId);
    EXEC (@DynamicQuery);  
END  
GO
/*   
===========================================================================================  
TASK			UPDATED BY      DATE			ACTION		DESCRIPTION  
[US-214760]		An.TranTan		09-Jan-2025		Create		Get BOM Sourcing Result raw data headers
===========================================================================================  
*/ 
CREATE OR ALTER   PROCEDURE [dbo].[usp_get_BOMSourcing_tempHeading] 
	@UserId INT
AS
BEGIN
	SELECT ColumnId, ColumnHeading
	FROM (
		SELECT TOP 1 
			[Column1]
			,[Column2]
			,[Column3]
			,[Column4]
			,[Column5]
			,[Column6]
			,[Column7]
			,[Column8]
			,[Column9]
			,[Column10]
			,[Column11]
			,[Column12]
			,[Column13]
			,[Column14]
			,[Column15]
			,[Column16]
			,[Column17]
			,[Column18]
			,[Column19]
			,[Column20]
			,[Column21]
			,[Column22]
			,[Column23]
			,[Column24]
			,[Column25]
			,[Column26]
			,[Column27]
			,[Column28]
			,[Column29]
			,[Column30]
			,[Column31]
		FROM BorisGlobalTraderimports.dbo.tbBOMSourcing_tempHeading
		WHERE CreatedBy = @UserId
		ORDER BY CreatedDate DESC
	) AS SourceTable
	UNPIVOT
	(
		ColumnHeading FOR ColumnId IN ([Column1]
										,[Column2]
										,[Column3]
										,[Column4]
										,[Column5]
										,[Column6]
										,[Column7]
										,[Column8]
										,[Column9]
										,[Column10]
										,[Column11]
										,[Column12]
										,[Column13]
										,[Column14]
										,[Column15]
										,[Column16]
										,[Column17]
										,[Column18]
										,[Column19]
										,[Column20]
										,[Column21]
										,[Column22]
										,[Column23]
										,[Column24]
										,[Column25]
										,[Column26]
										,[Column27]
										,[Column28]
										,[Column29]
										,[Column30]
										,[Column31]
										)
	) AS UnpivotTable;
END
GO

/*   
===========================================================================================  
TASK			UPDATED BY      DATE			ACTION		DESCRIPTION  
[US-214760]		An.TranTan		09-Jan-2025		Create		Insert temp data when import prospective offers
[US-232568]		An.TranTan		17-Feb-2025		Update		Change insert HUBRFQ ID instead of Req ID
===========================================================================================  
*/   
CREATE OR ALTER   Procedure [dbo].[usp_saveBOMSourcing_tempData]    
    @UploadedData UploadedBOMSourcingResult READONLY,
	@BOMNo INT,
    @originalFilename NVARCHAR(max),    
    @generatedFilename NVARCHAR(max),    
    @UserId INT = 1
AS    
BEGIN    
    SET NOCOUNT ON;
	--Clear previous temp data
	DELETE BorisGlobalTraderimports.dbo.tbBOMSourcing_tempData WHERE CreatedBy = @UserId;
	
    INSERT INTO BorisGlobalTraderimports.dbo.tbBOMSourcing_tempData    
    (    
        [BOMNo]
		,[Column1]
		,[Column2]
		,[Column3]
		,[Column4]
		,[Column5]
		,[Column6]
		,[Column7]
		,[Column8]
		,[Column9]
		,[Column10]
		,[Column11]
		,[Column12]
		,[Column13]
		,[Column14]
		,[Column15]
		,[Column16]
		,[Column17]
		,[Column18]
		,[Column19]
		,[Column20]
		,[Column21]
		,[Column22]
		,[Column23]
		,[Column24]
		,[Column25]
		,[Column26]
		,[Column27]
		,[Column28]
		,[Column29]
		,[Column30]
		,[Column31]
		,[LineNumber]
		,[OriginalFilename]
		,[GeneratedFilename]
		,[CreatedBy]
		,[CreatedDate]
    )    
    SELECT
		@BOMNo
		,[Column1]
		,[Column2]
		,[Column3]
		,[Column4]
		,[Column5]
		,[Column6]
		,[Column7]
		,[Column8]
		,[Column9]
		,[Column10]
		,[Column11]
		,[Column12]
		,[Column13]
		,[Column14]
		,[Column15]
		,[Column16]
		,[Column17]
		,[Column18]
		,[Column19]
		,[Column20]
		,[Column21]
		,[Column22]
		,[Column23]
		,[Column24]
		,[Column25]
		,[Column26]
		,[Column27]
		,[Column28]
		,[Column29]
		,[Column30]
		,[Column31]
		,[LineNumber]
        ,@originalFilename    
        ,@generatedFilename    
        ,@userId
		,GETDATE()
    FROM @UploadedData    
END  
GO

/*   
===========================================================================================  
TASK			UPDATED BY      DATE			ACTION		DESCRIPTION  
[US-214760]		An.TranTan		13-Jan-2025		Create		Get BOM Sourcing Result raw data
===========================================================================================  
*/ 
CREATE OR ALTER   PROCEDURE [dbo].[usp_get_BOMSourcing_rawData] 
	@DisplayLength INT
	,@DisplayStart INT
	,@SortCol INT
	,@SortDir NVARCHAR(10)
	,@UserId INT
AS
BEGIN
	SET NOCOUNT OFF;

	DECLARE @FirstRec INT ,@LastRec INT;

	SET @FirstRec = @DisplayStart;
	SET @LastRec = @DisplayStart + @DisplayLength;

	WITH CTE_Stock
	AS (
		SELECT ROW_NUMBER() OVER (
				ORDER BY CASE 
						WHEN (
								@SortCol = 0
								AND @SortDir = 'asc'
								)
							THEN BOMSourcingDataId
						END ASC
				) AS RowNum
			,COUNT(*) OVER () AS TotalCount
			,Column1
			,Column2
			,Column3
			,Column4
			,Column5
			,Column6
			,Column7
			,Column8
			,Column9
			,Column10
			,Column11
			,Column12
			,Column13
			,Column14
			,Column15
			,Column16
			,Column17
			,Column18
			,Column19
			,Column20
			,Column21
			,Column22
			,Column23
			,Column24
			,Column25
			,Column26
			,Column27
			,Column28
			,Column29
			,Column30
			,Column31
			,CreatedDate
			,OriginalFilename
		FROM BorisGlobalTraderImports.dbo.tbBOMSourcing_tempData WITH (NOLOCK)
		WHERE CreatedBy = @UserId
		)
	SELECT RowNum
		,TotalCount
		,Column1
		,Column2
		,Column3
		,Column4
		,Column5
		,Column6
		,Column7
		,Column8
		,Column9
		,Column10
		,Column11
		,Column12
		,Column13
		,Column14
		,Column15
		,Column16
		,Column17
		,Column18
		,Column19
		,Column20
		,Column21
		,Column22
		,Column23
		,Column24
		,Column25
		,Column26
		,Column27
		,Column28
		,Column29
		,Column30
		,Column31
		,CreatedDate
		,OriginalFilename
	FROM CTE_Stock
	WHERE RowNum > @FirstRec
		AND RowNum <= @LastRec
	ORDER BY CreatedDate DESC
END
GO

/*   
===========================================================================================  
TASK			UPDATED BY      DATE			ACTION		DESCRIPTION  
[US-214760]		An.TranTan		14-Jan-2025		CREATE		Get default column mapping for BOM import sourcing results
===========================================================================================  
*/ 
CREATE OR ALTER     PROCEDURE [dbo].[usp_select_BOMSourcing_ColumnMapping]                
AS                
BEGIN
	IF EXISTS (
		SELECT TOP 1 1 FROM [BorisGlobalTraderImports].[dbo].[tbBOMSourcing_ColumnMapping] WITH(NOLOCK)
	)
	BEGIN

		SELECT TOP (1) 
			[Manufacturer]
		    ,[SupplierPart]
		    ,[OfferedQuantity]
		    ,[SupplierCost]
		    ,[SPQ]
		    ,[MOQ]
		    ,[SupplierName]
		    ,[MSL]
		    ,[Notes]
		    ,[DateCode]
		    ,[QtyInStock]
		    ,[OfferStatus]
		    ,[BuyPrice]
		    ,[SellPrice]
		    ,[ShippingCost]
		    ,[Package]
		    ,[ROHS]
		    ,[Currency]
		    ,[FactorySealed]
		    ,[Region]
		    ,[LeadTime]
		    ,[LastTimeBuy]
		    ,[DeliveryDate]
		    ,[CustomerRefNo]
		FROM [BorisGlobalTraderImports].[dbo].[tbBOMSourcing_ColumnMapping] WITH(NOLOCK)
	END
	ELSE BEGIN
		SELECT    
			NULL AS [Manufacturer]
		    ,NULL AS [SupplierPart]
		    ,NULL AS [OfferedQuantity]
		    ,NULL AS [SupplierCost]
		    ,NULL AS [SPQ]
		    ,NULL AS [MOQ]
		    ,NULL AS [SupplierName]
		    ,NULL AS [MSL]
		    ,NULL AS [Notes]
		    ,NULL AS [DateCode]
		    ,NULL AS [QtyInStock]
		    ,NULL AS [OfferStatus]
		    ,NULL AS [BuyPrice]
		    ,NULL AS [SellPrice]
		    ,NULL AS [ShippingCost]
		    ,NULL AS [Package]
		    ,NULL AS [ROHS]
		    ,NULL AS [Currency]
		    ,NULL AS [FactorySealed]
		    ,NULL AS [Region]
		    ,NULL AS [LeadTime]
		    ,NULL AS [LastTimeBuy]
		    ,NULL AS [DeliveryDate]
		    ,NULL AS [CustomerRefNo]
	END
END
GO

/*   
===========================================================================================  
TASK			UPDATED BY      DATE			ACTION		DESCRIPTION  
[US-214760]		An.TranTan		13-Jan-2025		Create		Insert BOM Import Sourcing Results column mapping
===========================================================================================  
*/   
CREATE OR ALTER   Procedure [dbo].[usp_save_BOMSourcing_ColumnMapping]    
    @TargetColumns NVARCHAR(max),
    @SelectedColumns NVARCHAR(max),
    @UserId INT = 1
AS    
BEGIN    
    SET NOCOUNT ON;
	--Clear previous data
	DELETE BorisGlobalTraderimports.dbo.tbBOMSourcing_ColumnMapping;
	
	DECLARE @DynamicSQL NVARCHAR(MAX);
	SET @DynamicSQL = 'INSERT INTO BorisGlobalTraderimports.dbo.tbBOMSourcing_ColumnMapping(' + @TargetColumns + ',CreatedBy) '
					+ 'VALUES( ' + @SelectedColumns + ',' + CAST(@UserId AS NVARCHAR(10)) + ')'
	PRINT(@DynamicSQL);
	EXEC (@DynamicSQL);
END  
GO
/*   
===========================================================================================  
TASK			UPDATED BY      DATE			ACTION		DESCRIPTION  
[US-214760]		An.TranTan		13-Jan-2025		Create		Validate BOM Import Sourcing Results data with mapped column
[US-214760]		An.TranTan		16-Jan-2025		Update		Update validation rule and unmap value
[US-214759]		An.TranTan		17-Jan-2025		Update		Remove unnecessary LEFT JOIN cause duplicate data
[US-214759]		An.TranTan		20-Jan-2025		Update		Check requirement is released
[US-232568]		An.TranTan		17-Feb-2025		Update		Allow import for multiple req in single file
[US-232568]		An.TranTan		03-Mar-2025		Update		Validate datecode allows 5 characters
===========================================================================================  
*/ 
CREATE OR ALTER     PROCEDURE [dbo].[usp_validate_BOMSourcingResults]
	@TargetColumns NVARCHAR(max),
    @SelectedColumns NVARCHAR(max),
	@UserId INT = 0
AS BEGIN
	SET NOCOUNT ON;
	--Variables
	DECLARE @BOMNo INT;
	
	--get temp data with dynamic columns
	IF OBJECT_ID('tempdb..#tbBOMSourcing_tempData') IS NOT NULL
		DROP TABLE #tbBOMSourcing_tempData
	CREATE TABLE #tbBOMSourcing_tempData
	(
		BOMSourcingDataId INT
		, CustomerRequirementNo INT NULL
		, CustomerRequirementNumber INT NULL
		, Manufacturer NVARCHAR(MAX) NULL
		, SupplierPart NVARCHAR(MAX) NULL
		, OfferedQuantity NVARCHAR(MAX) NULL
		, SupplierCost NVARCHAR(MAX) NULL
		, SPQ NVARCHAR(MAX) NULL
		, MOQ NVARCHAR(MAX) NULL
		, SupplierName NVARCHAR(MAX) NULL
		, MSL NVARCHAR(MAX) NULL
		, Notes NVARCHAR(MAX) NULL
		, DateCode NVARCHAR(MAX) NULL
		, QtyInStock NVARCHAR(MAX) NULL
		, OfferStatus NVARCHAR(MAX) NULL
		, BuyPrice NVARCHAR(MAX) NULL
		, SellPrice NVARCHAR(MAX) NULL
		, ShippingCost NVARCHAR(MAX) NULL
		, Package NVARCHAR(MAX) NULL
		, ROHS NVARCHAR(MAX) NULL
		, Currency NVARCHAR(MAX) NULL
		, FactorySealed NVARCHAR(MAX) NULL
		, Region NVARCHAR(MAX) NULL
		, LeadTime NVARCHAR(MAX) NULL
		, LastTimeBuy NVARCHAR(MAX) NULL
		, DeliveryDate NVARCHAR(MAX) NULL
		, CustomerRefNo NVARCHAR(MAX) NULL
		, LineNumber INT NULL
		, OriginalFilename NVARCHAR(200) NULL
		, GeneratedFilename NVARCHAR(200) NULL
		, UnmappedMessage NVARCHAR(MAX) DEFAULT ''
		, ValidationMessage NVARCHAR(MAX) DEFAULT ''
	)

	--get HUBRFQ ID, unique for the single import
	SELECT TOP 1 @BOMNo = BOMNo FROM BorisGlobalTraderImports.dbo.tbBOMSourcing_tempData WHERE CreatedBy = @UserId;

	--Get raw data with dynamic columns
	DECLARE @DynamicSQL NVARCHAR(MAX);
	SET @DynamicSQL = 'INSERT INTO #tbBOMSourcing_tempData(' + @TargetColumns  
					+		',BOMSourcingDataId,CustomerRequirementNumber,LineNumber,OriginalFilename,GeneratedFilename) '
					+ 'SELECT ' + @SelectedColumns + ', BOMSourcingDataId, Column2, LineNumber, OriginalFilename, GeneratedFilename '
					+ 'FROM BorisGlobalTraderImports.dbo.tbBOMSourcing_tempData '
					+ 'WHERE CreatedBy=' + CAST(@UserId AS NVARCHAR(10))
	EXEC (@DynamicSQL);

	--get requirement id base on HUBRFQ ID and Requirement Number
	UPDATE temp
	SET temp.CustomerRequirementNo = cr.CustomerRequirementId
	FROM #tbBOMSourcing_tempData temp
	JOIN tbCustomerRequirement cr WITH(NOLOCK) 
		ON cr.CustomerRequirementNumber = temp.CustomerRequirementNumber AND cr.BOMNo = @BOMNo

	/************ Validation input data ************/
	--requirement number is required
	update temp
	set temp.ValidationMessage = 'Requirement cannot be blank.<br/>'
	from #tbBOMSourcing_tempData temp
	where ISNULL(temp.CustomerRequirementNumber,0) = 0

	--check valid requirement
	update temp
	set temp.ValidationMessage = temp.ValidationMessage + ' Requirement is invalid.<br/>'
	from #tbBOMSourcing_tempData temp
	where ISNULL(temp.CustomerRequirementNumber,0) <> 0 AND ISNULL(temp.CustomerRequirementNo, 0) = 0

	--check Customer Requirement released
	update temp
	set temp.ValidationMessage = temp.ValidationMessage + ' This requirement has been released.<br/>'
	from #tbBOMSourcing_tempData temp
	join tbCustomerRequirement cr WITH(NOLOCK) on cr.CustomerRequirementId = temp.CustomerRequirementNo
	where ISNULL(cr.REQStatus,0) > 3 

	-- Check suppliername existed in GT client
	update temp
	set temp.ValidationMessage = temp.ValidationMessage + ' This supplier does not exist on the DMCC client, unable to import.<br/>'
	from #tbBOMSourcing_tempData temp
	where not exists (
		select top 1 1 from dbo.tbCompany co with (nolock)
		where co.Inactive = 0 
			AND co.ClientNo = 114
			AND co.CompanyName = temp.SupplierName
	)

	--check supplier part
	update temp
	set temp.ValidationMessage = temp.ValidationMessage + ' Supplier Part is required.<br/>'
	from #tbBOMSourcing_tempData temp
	where LEN(ISNULL(temp.SupplierPart,'')) = 0

	--check manufacturer
	update temp
	set temp.ValidationMessage = temp.ValidationMessage + ' This manufacturer does not exist or being inactive, unable to import.<br/>'
	from #tbBOMSourcing_tempData temp
	where not exists(
		select top 1 1 from tbManufacturer m with(nolock) 
		where m.ManufacturerName = temp.Manufacturer
		and m.Inactive = 0
	)

	--check quantity
	update #tbBOMSourcing_tempData
	set ValidationMessage = ValidationMessage + ' Offered Quantity is required and accept numberic values.<br/>'
	where TRY_PARSE(OfferedQuantity AS INT) IS NULL

	--CurrencyCode No length 3--                                                            
	UPDATE #tbBOMSourcing_tempData
	SET ValidationMessage = ValidationMessage + ' Currency code accepts 3 characters.<br/>'
	WHERE LEN(Currency) <> 3

	--datecode length 5
	UPDATE #tbBOMSourcing_tempData
	SET ValidationMessage = ValidationMessage + ' DateCode Field only accepts 5 characters.<br/>'
	WHERE LEN(DateCode) > 5

	--datecode allow AlphaNumeric
	UPDATE #tbBOMSourcing_tempData
	SET ValidationMessage = ValidationMessage + ' DateCode Field only accepts AlphaNumeric values.<br/>'
	WHERE DateCode IS NOT NULL AND dbo.stripAlphahnumeric2(DateCode) <> DateCode;

	--requires Buy price
	UPDATE #tbBOMSourcing_tempData
	SET ValidationMessage = ValidationMessage + ' Buy Price is required and accept numeric values.<br/>'
	WHERE TRY_PARSE(BuyPrice AS FLOAT) IS NULL

	--requires Sell price
	UPDATE #tbBOMSourcing_tempData
	SET ValidationMessage = ValidationMessage + ' Sell Price is required and accept numeric values.<br/>'
	WHERE TRY_PARSE(SellPrice AS FLOAT) IS NULL

	 --- Validate delivery date format--                                                              
    update temp  
    set temp.ValidationMessage = case  
                                     when TRY_PARSE(temp.DeliveryDate AS DATE USING 'en-GB') IS NULL then  
                                         temp.ValidationMessage + ' Delivery Date is invalid. Accept formart: dd/mm/yyyy.<br/>'  
                                     else  
                                         temp.ValidationMessage  
                                 end  
    from #tbBOMSourcing_tempData temp 
	
	--- Validate Customer Ref No--
	UPDATE #tbBOMSourcing_tempData
	SET ValidationMessage = ValidationMessage + ' Customer Ref No. must be less than 200 characters.'
	WHERE LEN(CustomerRefNo) > 200

	/************ Validation end ************/
	IF NOT EXISTS(SELECT TOP 1 1 FROM #tbBOMSourcing_tempData WHERE ISNULL(ValidationMessage, '') <> '')
	BEGIN
		--get mfr from import file
		WITH cteManufacturer AS(
			SELECT DISTINCT Manufacturer FROM #tbBOMSourcing_tempData
		)SELECT 
			MIN(m.ManufacturerId) AS ManufacturerId
			,m.ManufacturerName
		INTO #tempImportedManufacturer
		FROM cteManufacturer cte
		JOIN tbManufacturer m WITH(NOLOCK) ON m.ManufacturerName = cte.Manufacturer
			AND m.Inactive = 0
		GROUP BY m.ManufacturerName;
		-------------------------------------------
		--get supplier from import file
		WITH cteSupplier AS(
			SELECT DISTINCT SupplierName FROM #tbBOMSourcing_tempData
		)SELECT
			MIN(co.CompanyId) AS SupplierId
			,co.CompanyName AS SupplierName
		INTO #tempImportedSupplier
		FROM cteSupplier cte
		JOIN tbCompany co WITH(NOLOCK) ON co.CompanyName = cte.SupplierName
		WHERE co.Inactive = 0 
			AND co.ClientNo = 114
		GROUP BY co.CompanyName;
		-------------------------------------------
		--get package from import file
		DECLARE @tbPackage TABLE(PackageId INT, Package NVARCHAR(256))
		;WITH ctePackage AS(
			SELECT DISTINCT Package FROM #tbBOMSourcing_tempData WHERE ISNULL(Package,'') <> ''
		)
		INSERT INTO @tbPackage (PackageId, Package)
		SELECT 
			p.PackageId, p.PackageName
		FROM tbPackage p WITH(NOLOCK)
			JOIN ctePackage cte ON cte.Package = p.PackageName
		WHERE p.Inactive = 0
		UNION ALL
		SELECT 
			p.PackageId, p.PackageDescription
		FROM tbPackage p WITH(NOLOCK)
			JOIN ctePackage cte ON cte.Package = p.PackageDescription
		WHERE p.Inactive = 0
		-------------------------------------------
		--get msl from import file
		DECLARE @tbMSLLevel TABLE(MSLLevelId INT, MSL NVARCHAR(200));
		;WITH cteMSL AS(
			SELECT DISTINCT MSL FROM #tbBOMSourcing_tempData WHERE ISNULL(MSL,'') <> ''
		)INSERT INTO @tbMSLLevel (MSLLevelId, MSL)
		SELECT 
			msl.MSLLevelId, cte.MSL
		FROM cteMSL cte
		JOIN tbMSLLevel msl WITH(NOLOCK) ON msl.MSLLevel = cte.MSL
		UNION ALL
		SELECT 
			msl.MSLLevelId, cte.MSL
		FROM cteMSL cte
		JOIN tbMSLLevel msl WITH(NOLOCK) ON msl.MSLLevel = CONCAT('MSL ', cte.MSL)
		-------------------------------------------

		/*============= Update unmap value to Notes field ===================*/
		--check shipping cost
		UPDATE #tbBOMSourcing_tempData
		SET UnmappedMessage = UnmappedMessage + 'Shipping Cost: ' + ShippingCost + ' | '
		WHERE ISNULL(ShippingCost,'') <> '' AND TRY_PARSE(ShippingCost AS FLOAT) IS NULL

		--check supplier cost
		UPDATE #tbBOMSourcing_tempData
		SET UnmappedMessage = UnmappedMessage + 'Supplier Cost: ' + SupplierCost + ' | '
		WHERE ISNULL(SupplierCost,'') <> '' AND TRY_PARSE(SupplierCost AS FLOAT) IS NULL

		--check quantity in stock
		UPDATE #tbBOMSourcing_tempData
		SET UnmappedMessage = UnmappedMessage + 'Qty in stock: ' + QtyInStock + ' | '
		WHERE ISNULL(QtyInStock,'') <> '' AND TRY_PARSE(QtyInStock AS INT) IS NULL

		--check offer status
		UPDATE #tbBOMSourcing_tempData
		SET UnmappedMessage = UnmappedMessage + 'Offer Status: ' + OfferStatus + ' | '
		WHERE ISNULL(OfferStatus,'') <> ''
			AND NOT EXISTS(
				SELECT TOP 1 1 FROM tbOfferStatus WITH(NOLOCK) WHERE [Name] = OfferStatus
			)

		--check region
		UPDATE #tbBOMSourcing_tempData
		SET UnmappedMessage = UnmappedMessage + 'Region: ' + Region + ' | '
		WHERE ISNULL(Region,'') <> ''
			AND NOT EXISTS(
				SELECT TOP 1 1 FROM tbRegion WITH(NOLOCK) WHERE RegionName = Region
			)

		--check package
		UPDATE t
		SET t.UnmappedMessage = t.UnmappedMessage + 'Package: ' + t.Package + ' | '
		FROM #tbBOMSourcing_tempData t
		WHERE ISNULL(t.Package,'') <> '' AND NOT EXISTS(
			SELECT TOP 1 1 FROM @tbPackage p WHERE p.Package = t.Package
		)

		--check MSL
		UPDATE t
		SET t.UnmappedMessage = t.UnmappedMessage + 'MSL: ' + t.MSL + ' | '
		FROM #tbBOMSourcing_tempData t
		WHERE ISNULL(t.MSL,'') <> '' AND NOT EXISTS(
			SELECT TOP 1 1 FROM @tbMSLLevel m WHERE m.MSL = t.MSL
		)

		UPDATE #tbBOMSourcing_tempData
		SET Notes = UnmappedMessage + '<br/>' + Notes
		WHERE UnmappedMessage <> ''
		/*===================================================================*/

		--clear previous temp data
		DELETE BorisGlobalTraderImports.dbo.tbBOMSourcing_ToBeImported WHERE CreatedBy = @UserId;

		INSERT INTO BorisGlobalTraderImports.dbo.tbBOMSourcing_ToBeImported
		(
			CustomerRequirementNo
			,SupplierNo
			,SupplierName
			,ManufacturerNo
			,ManufacturerName
			,SupplierPart
			,Quantity
			,SupplierCost
			,SPQ
			,MOQ
			,MSLLevelNo
			,MSL
			,Notes
			,DateCode
			,QtyInStock
			,OfferStatusNo
			,BuyPrice
			,SellPrice
			,ShippingCost
			,PackageNo
			,ROHSStatus
			,CurrencyNo
			,FactorySealed
			,RegionNo
			,LeadTime
			,LastTimeBuy
			,DeliveryDate
			,CustomerRefNo
			,OriginalFilename
			,GeneratedFilename
			,CreatedBy
			,DLUP
		)
		SELECT 
			temp.CustomerRequirementNo
			,s.SupplierId
			,s.SupplierName
			,m.ManufacturerId
			,m.ManufacturerName
			,temp.SupplierPart
			,temp.OfferedQuantity
			,ISNULL(TRY_PARSE(temp.SupplierCost AS FLOAT), 0)
			,temp.SPQ
			,temp.MOQ
			,(SELECT TOP 1 MSLLevelId FROM @tbMSLLevel m WHERE m.MSL = temp.MSL)
			,temp.MSL
			,temp.Notes
			,temp.DateCode
			,ISNULL(TRY_PARSE(temp.QtyInStock AS INT), 0)
			,os.OfferStatusId
			,ISNULL(TRY_PARSE(temp.BuyPrice AS FLOAT), 0)
			,ISNULL(TRY_PARSE(temp.SellPrice AS FLOAT), 0)
			,ISNULL(TRY_PARSE(temp.ShippingCost AS FLOAT), 0)
			,(SELECT TOP 1 PackageId FROM @tbPackage p WHERE p.Package = temp.Package)
			,case when ISNULL(temp.[ROHS], '') = 'Y' then 'Y' else 'N' end AS ROHSStatus
			,case when temp.Currency = ISNULL(pocr.CurrencyCode,'') then pocr.CurrencyId else null end
			,temp.FactorySealed
			,r.RegionId
			,temp.LeadTime
			,temp.LastTimeBuy
			,CONVERT(DATETIME, temp.DeliveryDate, 103)
			,temp.CustomerRefNo
			,temp.OriginalFilename
			,temp.GeneratedFilename
			,@UserId
			,GETDATE()
		FROM #tbBOMSourcing_tempData temp
		JOIN #tempImportedManufacturer m ON m.ManufacturerName = temp.Manufacturer
		JOIN #tempImportedSupplier s ON s.SupplierName = temp.SupplierName
		JOIN tbCompany c WITH(NOLOCK) on c.CompanyId = s.SupplierId
		LEFT JOIN tbCurrency pocr WITH(NOLOCK) on pocr.CurrencyId = c.POCurrencyNo
		LEFT JOIN tbOfferStatus os WITH(NOLOCK) on os.[Name] = temp.OfferStatus
		LEFT JOIN tbRegion r WITH(NOLOCK) on r.RegionName = temp.Region

		DROP TABLE #tempImportedSupplier;
		DROP TABLE #tempImportedManufacturer;
	END
	
	SELECT 
		LineNumber AS 'LINE NO.'
		,ISNULL(CustomerRequirementNumber,'') AS 'REQUIREMENT'
		,ISNULL(CustomerRefNo,'') AS 'CUSTOMER REF NO.'
		,SupplierName AS 'SUPPLIER NAME'
		,SupplierPart AS 'SUPPLIER PART NO.'
		,ISNULL(SupplierCost,'') AS 'Supplier Cost'
		,ISNULL(ROHS,'') AS ROHS
		,Manufacturer AS MANUFACTURER
		,ISNULL(DateCode,'') AS 'DATE CODE'
		,ISNULL(Package,'') AS PACKAGE
		,OfferedQuantity AS 'OFFERED QTY.'
		,ISNULL(OfferStatus,'') AS 'OFFER STATUS'
		,ISNULL(SPQ,'') AS SPQ
		,ISNULL(FactorySealed,'') AS 'FACTORY SEALED'
		,ISNULL(QtyInStock,'') AS 'QTY IN STOCK'
		,ISNULL(MOQ,'') AS MOQ
		,ISNULL(LastTimeBuy,'') AS 'LAST TIME BUY'
		,ISNULL(Currency,'') AS 'CURRENCY'
		,ISNULL(BuyPrice,'') AS 'BUY PRICE'
		,ISNULL(SellPrice,'') AS 'SELL PRICE'
		,ISNULL(ShippingCost,'') AS 'SHIPPING COST'
		,ISNULL(LeadTime,'') AS LEADTIME
		,ISNULL(Region,'') AS REGION
		,ISNULL(DeliveryDate,'') AS 'DELIVERY DATE'
		,ISNULL(Notes,'') AS NOTES
		,ISNULL(MSL,'') AS MSL
		,OriginalFilename
		,ValidationMessage AS Reason  
	FROM #tbBOMSourcing_tempData
	WHERE ISNULL(ValidationMessage, '') <> ''
	ORDER BY LineNumber;
	--DROP temp tables
	DROP TABLE #tbBOMSourcing_tempData;
END
GO
