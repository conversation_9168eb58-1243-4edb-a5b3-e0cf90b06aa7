using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using System.Collections;
using Rebound.GlobalTrader.Site.Controls.Nuggets;

namespace Rebound.GlobalTrader.Site.Controls {
	[DefaultProperty("")]
	[ToolboxData("<{0}:FieldSet runat=server></{0}:FieldSet>")]
	public class FieldSet : WebControl, IScriptControl, INamingContainer {

		#region Locals

		protected ScriptManager _sm;
		protected Label _lblCount;
		protected Panel _pnlOuter;
		protected HyperLink _hypShowHide;
		protected Panel _pnlButtons;
		protected Panel _pnlContent;
		protected Label _lblLegend;
		protected Label _lblLoadingTop;
		protected HyperLink _hypRefresh;
		protected Panel _pnlLoading;
		protected Panel _pnlError;
		protected Label _lblError;
		protected Panel _pnlNoData;

		#endregion

		#region Properties

		public string FieldSetType { get; set; }

		/// <summary>
		/// Can this fieldset be rolled up?
		/// </summary>
		private bool _blnAllowRollUp = true;
		public bool AllowRollUp {
			get { return _blnAllowRollUp; }
			set { _blnAllowRollUp = value; }
		}

		private bool _blnIsInitiallyRolledUp = false; 
		/// 
		/// </summary>
		public bool IsInitiallyRolledUp {
			get { return _blnIsInitiallyRolledUp; }
			set { _blnIsInitiallyRolledUp = value; }
		}

		/// <summary>
		/// Should the refresh button be shown?
		/// </summary>
		private bool _blnShowRefresh = true;
		public bool ShowRefresh {
			get { return _blnShowRefresh; }
			set { _blnShowRefresh = value; }
		}

		/// <summary>
		/// Name of anchor to go to for the included 'To Top' link
		/// </summary>
		private string _strToTopAnchorName = "";
		public string ToTopAnchorName {
			get { return _strToTopAnchorName; }
			set { _strToTopAnchorName = value; }
		}

		/// <summary>
		/// Title container
		/// </summary>
		private ITemplate _tmpTitle = null;
		[PersistenceMode(PersistenceMode.InnerProperty)]
		[TemplateContainer(typeof(Container))]
		public ITemplate Title {
			get { return _tmpTitle; }
			set { _tmpTitle = value; }
		}

		/// <summary>
		/// Buttons container
		/// </summary>
		private ITemplate _tmpButtons = null;
		[PersistenceMode(PersistenceMode.InnerProperty)]
		[TemplateContainer(typeof(Container))]
		public ITemplate Buttons {
			get { return _tmpButtons; }
			set { _tmpButtons = value; }
		}

		/// <summary>
		/// Content container
		/// </summary>
		private ITemplate _tmpContent = null;
		[PersistenceMode(PersistenceMode.InnerProperty)]
		[TemplateContainer(typeof(Container))]
		public ITemplate Content {
			get { return _tmpContent; }
			set { _tmpContent = value; }
		}

		/// <summary>
		/// Which Resource in NotFound.resx to use when the FieldSet has no data
		/// </summary>
		private string _strNoDataMessageResource = "Generic";
		public string NoDataMessageResource {
			get { return _strNoDataMessageResource; }
			set { _strNoDataMessageResource = value; }
		}


		#endregion

		#region Overrides

		protected override void OnInit(EventArgs e) {
			((Pages.Base)Page).AddCSSFile("FieldSet.css");
			base.OnInit(e);
		}

		/// <summary>
		/// create controls
		/// </summary>
		protected override void CreateChildControls() {

			//outer 
			_pnlOuter = ControlBuilders.CreatePanelInsideParent(this);

			//legend
			Panel lblLegendOuter = ControlBuilders.CreatePanelInsideParent(_pnlOuter, "fieldSetLegendOuter");
			_lblLegend = ControlBuilders.CreateLabelInsideParent(lblLegendOuter, "fieldSetLegend");

			//plus and minus buttons
			if (_blnAllowRollUp) {
				_hypShowHide = ControlBuilders.CreateHyperLinkInsideParent(_lblLegend, "fieldSetShowHide", "javascript:void(0);");
				_hypShowHide.ID = "hypShowHide";
				Image imgShowHide = ControlBuilders.CreateImageInsideParent(_hypShowHide, "", "~/images/x.gif", 12, 11);
				imgShowHide.ID = "imgShowHide";
			}

			//add title
			if (_tmpTitle != null) Functions.AddControlsFromTemplate(_lblLegend, _tmpTitle);

			//count
			_lblCount = ControlBuilders.CreateLabelInsideParent(_lblLegend);
			_lblCount.ID = "lblCount";

			//loading top
			_lblLoadingTop = ControlBuilders.CreateLabelInsideParent(_lblLegend, "fieldSetLoadingTop");
			_lblLoadingTop.ID = "lblLoadingTop";
			Image imgLoadingTop = ControlBuilders.CreateImageInsideParent(_lblLoadingTop, "", "~/images/x.gif", 12, 12);
			imgLoadingTop.ID = "imgLoadingTop";

			//refresh
			if (_blnShowRefresh) {
				_hypRefresh = ControlBuilders.CreateHyperLinkInsideParent(_lblLegend, "fieldSetRefresh", "javascript:void(0);");
				_hypRefresh.ID = "hypRefresh";
				Image imgRefresh = ControlBuilders.CreateImageInsideParent(_hypRefresh, "", "~/images/x.gif", 11, 10);
				imgRefresh.ID = "imgRefresh";
			}

			//content
			_pnlContent = ControlBuilders.CreatePanelInsideParent(_pnlOuter, "fieldSetContent");
			_pnlContent.ID = "pnlContent";

			//add buttons if we have any
			if (_tmpButtons != null) {
				_pnlButtons = ControlBuilders.CreatePanelInsideParent(_pnlContent, "fieldSetButtons");
				Functions.AddControlsFromTemplate(_pnlButtons, _tmpButtons);
			}

			//add content
			if (_tmpContent != null) Functions.AddControlsFromTemplate(_pnlContent, _tmpContent);

			//content loading
			_pnlLoading = ControlBuilders.CreatePanelInsideParent(_pnlOuter, "fieldSetContentLoading");
			_pnlLoading.ID = "pnlLoading";
			ControlBuilders.CreateLiteralInsideParent(_pnlLoading, Functions.GetGlobalResource("Misc", "Loading"));
			Functions.SetCSSVisibility(_pnlLoading, false);

			//No data
			_pnlNoData = ControlBuilders.CreatePanelInsideParent(_pnlOuter, "noData");
			_pnlNoData.ID = "pnlNoData";
			ControlBuilders.CreateLiteralInsideParent(_pnlNoData, Functions.GetGlobalResource("NotFound", _strNoDataMessageResource));
			Functions.SetCSSVisibility(_pnlNoData, false);

			//error
			_pnlError = ControlBuilders.CreatePanelInsideParent(_pnlOuter, "fieldSetErrorSmall");
			_pnlError.ID = "pnlError";
			_lblError = ControlBuilders.CreateLabelInsideParent(_pnlError);
			_lblError.ID = "lblError";
			Functions.SetCSSVisibility(_pnlError, false);

			//add link to top of screen
			if (!String.IsNullOrEmpty(_strToTopAnchorName)) {
				Panel pnlTopLink = ControlBuilders.CreatePanelInsideParent(_pnlOuter, "fieldSetTopLink");
				pnlTopLink.ID = "pnlTopLink";
				ControlBuilders.CreateLiteralInsideParent(pnlTopLink, string.Format(@"<a href=""#{0}""><img src=""images/x.gif"" border=""0"" width=""9"" height=""9"" /></a>", _strToTopAnchorName));
			}

			base.CreateChildControls();
		}

		/// <summary>
		/// OnPreRender
		/// </summary>
		/// <param name="e"></param>
		protected override void OnPreRender(EventArgs e) {
			DeriveCSSClass();
			if (!this.DesignMode) {
				_sm = ScriptManager.GetCurrent(Page);
				if (_sm == null) throw new HttpException("A ScriptManager control must exist on the current page.");
				_sm.RegisterScriptControl(this);
			}
			base.OnPreRender(e);
		}

		/// <summary>
		/// Render
		/// </summary>
		/// <param name="writer"></param>
		protected override void Render(HtmlTextWriter writer) {
			if (!this.DesignMode) _sm.RegisterScriptDescriptors(this);
			base.Render(writer);
		}

		#endregion

		#region Methods

		private void DeriveCSSClass() {
			string strCssClass = "fieldSet";
			if (_blnIsInitiallyRolledUp) strCssClass += " fieldSetCollapsed";
			if (!string.IsNullOrEmpty(FieldSetType)) strCssClass += string.Format(" fieldSet{0}", FieldSetType);
			_pnlOuter.CssClass = strCssClass;
		}

		/// <summary>
		/// Finds a control in the Buttons container
		/// </summary>
		public Control FindButtonControl(string strName) {
			EnsureChildControls();
			return (Functions.FindControlRecursive(_pnlButtons, strName));
		}

		/// <summary>
		/// Finds a control in the Buttons container
		/// </summary>
		public Control FindContentControl(string strName) {
			EnsureChildControls();
			return (Functions.FindControlRecursive(_pnlContent, strName));

		}

		internal void SetTitle(string strTitle) {
			EnsureChildControls();
			_lblLegend.Text = strTitle;
		}

		internal void AddToContent(Control ctl) {
			EnsureChildControls();
			_pnlContent.Controls.Add(ctl);
		}

		#endregion

		#region IScriptControl Members

		protected virtual IEnumerable<ScriptReference> GetScriptReferences() {
			bool blnDebug = false;
#if (DEBUG)
			blnDebug = true;
#endif
			return new ScriptReference[] { Functions.GetScriptReference(blnDebug, "Rebound.GlobalTrader.Site", "Controls.Basic.FieldSet.FieldSet", true) };
		}

		protected virtual IEnumerable<ScriptDescriptor> GetScriptDescriptors() {
			ScriptControlDescriptor descriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.FieldSet", ClientID);
			descriptor.AddElementProperty("pnlOuter", _pnlOuter.ClientID);
			descriptor.AddElementProperty("pnlContent", _pnlContent.ClientID);
			descriptor.AddElementProperty("pnlLoading", _pnlLoading.ClientID);
			descriptor.AddElementProperty("pnlError", _pnlError.ClientID);
			descriptor.AddElementProperty("pnlNoData", _pnlNoData.ClientID);
			descriptor.AddElementProperty("lblError", _lblError.ClientID);
			descriptor.AddElementProperty("lblCount", _lblCount.ClientID);
			descriptor.AddElementProperty("lblLoadingTop", _lblLoadingTop.ClientID);
			descriptor.AddProperty("blnIsRolledUp", _blnIsInitiallyRolledUp);
			if (_blnShowRefresh) descriptor.AddElementProperty("hypRefresh", _hypRefresh.ClientID);
			if (_hypShowHide != null) descriptor.AddElementProperty("hypShowHide", _hypShowHide.ClientID);
			return new ScriptDescriptor[] { descriptor };
		}

		IEnumerable<ScriptReference> IScriptControl.GetScriptReferences() {
			return GetScriptReferences();
		}

		IEnumerable<ScriptDescriptor> IScriptControl.GetScriptDescriptors() {
			return GetScriptDescriptors();
		}

		#endregion
	}
}
