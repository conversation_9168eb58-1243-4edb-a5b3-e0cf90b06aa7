﻿
GO

CREATE OR ALTER FUNCTION [dbo].[ufn_GetStockAvailableDetail]                           
(                          
 @PartNo nvarchar(50)= null,                          
 @ClientNo int ,                
 @stockid int =0                               
)                                      
RETURNS NVARCHAR(400)                                       
AS                                      
BEGIN                                      
 Declare @StockAvailableMessage nvarchar(400)                                    
 Declare @QuantityInStock nvarchar(100)            
 Declare @QuantityOnOrder nvarchar(100)            
 Declare @QuantityAllocated nvarchar(100)            
 Declare @QuantityAvailable nvarchar(100)             
 Declare @stockNo nvarchar(100)             
 SET @StockAvailableMessage=''                                 
 IF (@StockAvailableMessage is not null)                                  
  BEGIN                                  
   if(@stockid!=0)          
   begin          
 if exists(select top 1 QuantityInStock,QuantityOnOrder,QuantityAllocated,QuantityAvailable from vwStock where stockid in (select stockid from tbStock where stockid=@stockid) order by dlup desc  )                                
   begin              
  select top 1 @stockNo=stockid, @QuantityInStock=QuantityInStock,@QuantityOnOrder=QuantityOnOrder,@QuantityAllocated=QuantityAllocated,@QuantityAvailable=QuantityAvailable   
  from vwStock            
  where stockid in (select stockid from tbStock where stockid=@stockid) order by dlup desc                     
      end            
   end          
  else          
   begin          
 if exists(select top 1 QuantityInStock,QuantityOnOrder,QuantityAllocated,QuantityAvailable from vwStock where stockid in (select stockid from tbStock where Part=@PartNo) order by dlup desc  )                                
  begin              
  select top 1 @stockNo=stockid, @QuantityInStock=QuantityInStock,@QuantityOnOrder=QuantityOnOrder,@QuantityAllocated=QuantityAllocated,@QuantityAvailable=QuantityAvailable from vwStock            
  where stockid in (select stockid from tbStock where Part=@PartNo) order by dlup desc                     
  end            
   end                              
   --SET @StockAvailableMessage = @StockAvailableMessage +  @QuantityInStock +' In Stock -'+ @QuantityOnOrder +' On Order -'+ @QuantityAllocated +' Allocated -'+ @QuantityAvailable +' Available -' + @stockNo            
   IF(@QuantityAvailable>0)  
    BEGIN     
  SET @StockAvailableMessage = @StockAvailableMessage +' In Stock : '+@QuantityInStock+' -'+' On Order : '+@QuantityOnOrder+'-' +' Allocated : '+@QuantityAllocated+'-' + ' Available : '+@QuantityAvailable+'-'+ @stockNo     
    End  
   end                                 
   Return @StockAvailableMessage                                      
 End  
GO

