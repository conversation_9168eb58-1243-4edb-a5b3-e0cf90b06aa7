<?xml version="1.0"?>
<doc>
    <assembly>
        <name>System.IO.Packaging</name>
    </assembly>
    <members>
        <member name="T:System.IO.Packaging.CompressionOption">
            <summary>
            This class is used to control Compression for package parts.
            </summary>
        </member>
        <member name="F:System.IO.Packaging.CompressionOption.NotCompressed">
            <summary>
            Compression is turned off in this mode.
            </summary>
        </member>
        <member name="F:System.IO.Packaging.CompressionOption.Normal">
            <summary>
            Compression is optimized for a reasonable compromise between size and performance.
            </summary>
        </member>
        <member name="F:System.IO.Packaging.CompressionOption.Maximum">
            <summary>
            Compression is optimized for size.
            </summary>
        </member>
        <member name="F:System.IO.Packaging.CompressionOption.Fast">
            <summary>
            Compression is optimized for performance.
            </summary>
        </member>
        <member name="F:System.IO.Packaging.CompressionOption.SuperFast">
            <summary>
            Compression is optimized for super performance.
            </summary>
        </member>
        <member name="T:System.IO.Packaging.ContentType">
            <summary>
            Content Type class
            </summary>
        </member>
        <member name="M:System.IO.Packaging.ContentType.#ctor(System.String)">
            <summary>
            This constructor creates a ContentType object that represents
            the content-type string. At construction time we validate the
            string as per the grammar specified in RFC 2616.
            Note: We allow empty strings as valid input. Empty string should
            we used more as an indication of an absent/unknown ContentType.
            </summary>
            <param name="contentType">content-type</param>
            <exception cref="T:System.ArgumentNullException">If the contentType parameter is null</exception>
            <exception cref="T:System.ArgumentException">If the contentType string has leading or
            trailing Linear White Spaces(LWS) characters</exception>
            <exception cref="T:System.ArgumentException">If the contentType string invalid CR-LF characters</exception>
        </member>
        <member name="P:System.IO.Packaging.ContentType.TypeComponent">
            <summary>
            TypeComponent of the Content Type
            If the content type is "text/xml". This property will return "text"
            </summary>
        </member>
        <member name="P:System.IO.Packaging.ContentType.SubTypeComponent">
            <summary>
            SubType component
            If the content type is "text/xml". This property will return "xml"
            </summary>
        </member>
        <member name="P:System.IO.Packaging.ContentType.ParameterValuePairs">
            <summary>
            Enumerator which iterates over the Parameter and Value pairs which are stored
            in a dictionary. We hand out just the enumerator in order to make this property
            ReadOnly
            Consider following Content type -
            type/subtype ; param1=value1 ; param2=value2 ; param3="value3"
            This will return an enumerator over a dictionary of the parameter/value pairs.
            </summary>
        </member>
        <member name="M:System.IO.Packaging.ContentType.AreTypeAndSubTypeEqual(System.IO.Packaging.ContentType)">
            <summary>
            This method does a strong comparison of the content types, as parameters are not allowed.
            We only compare the type and subType values in an ASCII case-insensitive manner.
            Parameters are not allowed to be present on any of the content type operands.
            </summary>
            <param name="contentType">Content type to be compared with</param>
            <returns></returns>
        </member>
        <member name="M:System.IO.Packaging.ContentType.AreTypeAndSubTypeEqual(System.IO.Packaging.ContentType,System.Boolean)">
            <summary>
            This method does a weak comparison of the content types. We only compare the
            type and subType values in an ASCII case-insensitive manner.
            Parameter and value pairs are not used for the comparison.
            If you wish to compare the parameters too, then you must get the ParameterValuePairs from
            both the ContentType objects and compare each parameter entry.
            The allowParameterValuePairs parameter is used to indicate whether the
            comparison is tolerant to parameters being present or no.
            </summary>
            <param name="contentType">Content type to be compared with</param>
            <param name="allowParameterValuePairs">If true, allows the presence of parameter value pairs.
            If false, parameter/value pairs cannot be present in the content type string.
            In either case, the parameter value pair is not used for the comparison.</param>
            <returns></returns>
        </member>
        <member name="M:System.IO.Packaging.ContentType.ToString">
            <summary>
            ToString - outputs a normalized form of the content type string
            </summary>
            <returns></returns>
        </member>
        <member name="M:System.IO.Packaging.ContentType.ValidateCarriageReturns(System.String)">
            <summary>
            This method validates if the content type string has
            valid CR-LF characters. Specifically we test if '\r' is
            accompanied by a '\n' in the string, else its an error.
            </summary>
            <param name="contentType"></param>
        </member>
        <member name="M:System.IO.Packaging.ContentType.ParseTypeAndSubType(System.ReadOnlySpan{System.Char})">
            <summary>
            Parses the type and subType tokens from the string.
            Also verifies if the Tokens are valid as per the grammar.
            </summary>
            <param name="typeAndSubType">substring that has the type and subType of the content type</param>
            <exception cref="T:System.ArgumentException">If the typeAndSubType parameter does not have the "/" character</exception>
        </member>
        <member name="M:System.IO.Packaging.ContentType.ParseParameterAndValue(System.ReadOnlySpan{System.Char})">
            <summary>
            Parse the individual parameter=value strings
            </summary>
            <param name="parameterAndValue">This string has the parameter and value pair of the form
            parameter=value</param>
            <exception cref="T:System.ArgumentException">If the string does not have the required "="</exception>
        </member>
        <member name="M:System.IO.Packaging.ContentType.GetLengthOfParameterValue(System.ReadOnlySpan{System.Char},System.Int32)">
            <summary>
            This method returns the length of the first parameter value in the input string.
            </summary>
            <param name="s"></param>
            <param name="startIndex">Starting index for parsing</param>
            <returns></returns>
        </member>
        <member name="M:System.IO.Packaging.ContentType.ValidateToken(System.String)">
            <summary>
            Validating the given token
            The following checks are being made -
            1. If all the characters in the token are either ASCII letter or digit.
            2. If all the characters in the token are either from the remaining allowed character set.
            </summary>
            <param name="token">string token</param>
            <returns>validated string token</returns>
            <exception cref="T:System.ArgumentException">If the token is Empty</exception>
        </member>
        <member name="M:System.IO.Packaging.ContentType.ValidateQuotedStringOrToken(System.String)">
            <summary>
            Validating if the value of a parameter is either a valid token or a
            valid quoted string
            </summary>
            <param name="parameterValue">parameter value string</param>
            <returns>validate parameter value string</returns>
            <exception cref="T:System.ArgumentException">If the parameter value is empty</exception>
        </member>
        <member name="M:System.IO.Packaging.ContentType.ValidateQuotedText(System.ReadOnlySpan{System.Char})">
            <summary>
            This method validates if the text in the quoted string
            </summary>
            <param name="quotedText"></param>
        </member>
        <member name="M:System.IO.Packaging.ContentType.IsAllowedCharacter(System.Char)">
            <summary>
            Returns true if the input character is an allowed character
            Returns false if the input character is not an allowed character
            </summary>
            <param name="character">input character</param>
            <returns></returns>
        </member>
        <member name="M:System.IO.Packaging.ContentType.IsAsciiLetterOrDigit(System.Char)">
            <summary>
            Returns true if the input character is an ASCII digit or letter
            Returns false if the input character is not an ASCII digit or letter
            </summary>
            <param name="character">input character</param>
            <returns></returns>
        </member>
        <member name="M:System.IO.Packaging.ContentType.IsLinearWhiteSpaceChar(System.Char)">
            <summary>
            Returns true if the input character is one of the Linear White Space characters -
            ' ', '\t', '\n', '\r'
            Returns false if the input character is none of the above
            </summary>
            <param name="ch">input character</param>
            <returns></returns>
        </member>
        <member name="T:System.IO.Packaging.EncryptionOption">
            <summary>
            This class is used to control Encryption RM for package parts.
            </summary>
        </member>
        <member name="F:System.IO.Packaging.EncryptionOption.None">
            <summary>
            Encryption is turned off in this mode. This is not supported.
            </summary>
        </member>
        <member name="F:System.IO.Packaging.EncryptionOption.RightsManagement">
            <summary>
            RightsManagement is the only supported option right now.
            </summary>
        </member>
        <member name="T:System.IO.Packaging.IgnoreFlushAndCloseStream">
            <summary>
            This class ignores all calls to Flush() and Close() methods
            depending on whether the IgnoreFlushAndClose property is set to true
            or false. This has been created for performance improvements for the
            ZipPackage.
            </summary>
        </member>
        <member name="M:System.IO.Packaging.IgnoreFlushAndCloseStream.#ctor(System.IO.Stream)">
            <summary>
            Constructor
            </summary>
            <param name="stream"></param>
        </member>
        <member name="P:System.IO.Packaging.IgnoreFlushAndCloseStream.CanRead">
            <summary>
            Member of the abstract Stream class
            </summary>
            <value>Bool, true if the stream can be read from, else false</value>
        </member>
        <member name="P:System.IO.Packaging.IgnoreFlushAndCloseStream.CanSeek">
            <summary>
            Member of the abstract Stream class
            </summary>
            <value>Bool, true if the stream can be seeked, else false</value>
        </member>
        <member name="P:System.IO.Packaging.IgnoreFlushAndCloseStream.CanWrite">
            <summary>
            Member of the abstract Stream class
            </summary>
            <value>Bool, true if the stream can be written to, else false</value>
        </member>
        <member name="P:System.IO.Packaging.IgnoreFlushAndCloseStream.Length">
            <summary>
            Member of the abstract Stream class
            </summary>
            <value>Long value indicating the length of the stream</value>
        </member>
        <member name="P:System.IO.Packaging.IgnoreFlushAndCloseStream.Position">
            <summary>
            Member of the abstract Stream class
            </summary>
            <value>Long value indicating the current position in the stream</value>
        </member>
        <member name="M:System.IO.Packaging.IgnoreFlushAndCloseStream.Seek(System.Int64,System.IO.SeekOrigin)">
            <summary>
            Member of the abstract Stream class
            </summary>
            <param name="offset">only zero is supported</param>
            <param name="origin">only SeekOrigin.Begin is supported</param>
            <returns>zero</returns>
        </member>
        <member name="M:System.IO.Packaging.IgnoreFlushAndCloseStream.SetLength(System.Int64)">
            <summary>
            Member of the abstract Stream class
            </summary>
            <param name="newLength"></param>
        </member>
        <member name="M:System.IO.Packaging.IgnoreFlushAndCloseStream.Read(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Member of the abstract Stream class
            </summary>
            <param name="buffer"></param>
            <param name="offset"></param>
            <param name="count"></param>
            <returns></returns>
            <remarks>
            The standard Stream.Read semantics, and in particular the restoration of the current
            position in case of an exception, is implemented by the underlying stream.
            </remarks>
        </member>
        <member name="M:System.IO.Packaging.IgnoreFlushAndCloseStream.Write(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Member of the abstract Stream class
            </summary>
            <param name="buf"></param>
            <param name="offset"></param>
            <param name="count"></param>
        </member>
        <member name="M:System.IO.Packaging.IgnoreFlushAndCloseStream.Flush">
            <summary>
            Member of the abstract Stream class
            </summary>
        </member>
        <member name="M:System.IO.Packaging.IgnoreFlushAndCloseStream.Dispose(System.Boolean)">
            <summary>
            Dispose(bool)
            </summary>
            <param name="disposing"></param>
        </member>
        <member name="T:System.IO.Packaging.InternalRelationshipCollection">
            <summary>
            Collection of all the relationships corresponding to a given source PackagePart
            </summary>
        </member>
        <member name="M:System.IO.Packaging.InternalRelationshipCollection.System#Collections#IEnumerable#GetEnumerator">
            <summary>
            Returns an enumerator over all the relationships for a Package or a PackagePart
            </summary>
            <returns></returns>
        </member>
        <member name="M:System.IO.Packaging.InternalRelationshipCollection.GetEnumerator">
            <summary>
            Returns an enumerator over all the relationships for a Package or a PackagePart
            </summary>
            <returns></returns>
        </member>
        <member name="M:System.IO.Packaging.InternalRelationshipCollection.#ctor(System.IO.Packaging.PackagePart)">
            <summary>
            Constructor
            </summary>
            <remarks>For use by PackagePart</remarks>
        </member>
        <member name="M:System.IO.Packaging.InternalRelationshipCollection.#ctor(System.IO.Packaging.Package)">
            <summary>
            Constructor
            </summary>
            <remarks>For use by Package</remarks>
        </member>
        <member name="M:System.IO.Packaging.InternalRelationshipCollection.Add(System.Uri,System.IO.Packaging.TargetMode,System.String,System.String)">
            <summary>
            Add new relationship
            </summary>
            <param name="targetUri">target</param>
            <param name="targetMode">Enumeration indicating the base uri for the target uri</param>
            <param name="relationshipType">relationship type that uniquely defines the role of the relationship</param>
            <param name="id">String that conforms to the xsd:ID datatype. Unique across the source's relationships.
            Null OK (ID will be generated).</param>
        </member>
        <member name="M:System.IO.Packaging.InternalRelationshipCollection.GetRelationship(System.String)">
            <summary>
            Return the relationship whose id is 'id', and null if not found.
            </summary>
        </member>
        <member name="M:System.IO.Packaging.InternalRelationshipCollection.Delete(System.String)">
            <summary>
            Delete relationship with ID 'id'
            </summary>
            <param name="id">ID of the relationship to remove</param>
        </member>
        <member name="M:System.IO.Packaging.InternalRelationshipCollection.Clear">
            <summary>
            Clear all the relationships in this collection
            Today it is only used when the entire relationship part is being deleted
            </summary>
        </member>
        <member name="M:System.IO.Packaging.InternalRelationshipCollection.Flush">
            <summary>
            Flush to stream (destructive)
            </summary>
            <remarks>
            Flush part.
            </remarks>
        </member>
        <member name="M:System.IO.Packaging.InternalRelationshipCollection.#ctor(System.IO.Packaging.Package,System.IO.Packaging.PackagePart)">
            <summary>
            Constructor
            </summary>
            <param name="package">package</param>
            <param name="part">part will be null if package is the source of the relationships</param>
            <remarks>Shared constructor</remarks>
        </member>
        <member name="M:System.IO.Packaging.InternalRelationshipCollection.GetRelationshipPartUri(System.IO.Packaging.PackagePart)">
            <summary>
            Returns the associated RelationshipPart for this part
            </summary>
            <param name="part">may be null</param>
            <returns>name of relationship part for the given part</returns>
        </member>
        <member name="M:System.IO.Packaging.InternalRelationshipCollection.ParseRelationshipPart(System.IO.Packaging.PackagePart)">
            <summary>
            Parse PackageRelationship Stream
            </summary>
            <param name="part">relationship part</param>
            <exception cref="T:System.Xml.XmlException">Thrown if XML is malformed</exception>
        </member>
        <member name="M:System.IO.Packaging.InternalRelationshipCollection.Add(System.Uri,System.IO.Packaging.TargetMode,System.String,System.String,System.Boolean)">
            <summary>
            Add new relationship to the Collection
            </summary>
            <param name="targetUri">target</param>
            <param name="targetMode">Enumeration indicating the base uri for the target uri</param>
            <param name="relationshipType">relationship type that uniquely defines the role of the relationship</param>
            <param name="id">String that conforms to the xsd:ID datatype. Unique across the source's relationships.
            Null OK (ID will be generated).</param>
            <param name="parsing">Indicates whether the add call is made while parsing existing relationships
            from a relationship part, or we are adding a new relationship</param>
        </member>
        <member name="M:System.IO.Packaging.InternalRelationshipCollection.WriteRelationshipPart(System.IO.Packaging.PackagePart)">
            <summary>
            Write PackageRelationship Stream
            </summary>
            <param name="part">part to persist to</param>
        </member>
        <member name="M:System.IO.Packaging.InternalRelationshipCollection.WriteRelationshipsAsXml(System.Xml.XmlWriter,System.Collections.Generic.IEnumerable{System.IO.Packaging.PackageRelationship},System.Boolean)">
            <summary>
            Write one Relationship element for each member of relationships.
            This method is used by XmlDigitalSignatureProcessor code as well
            </summary>
        </member>
        <member name="M:System.IO.Packaging.InternalRelationshipCollection.EnsureRelationshipPart">
            <summary>
            Ensures that the PackageRelationship PackagePart has been created - lazy init
            </summary>
            <remarks>
            </remarks>
        </member>
        <member name="M:System.IO.Packaging.InternalRelationshipCollection.GetResolvedTargetUri(System.Uri,System.IO.Packaging.TargetMode)">
            <summary>
            Resolves the target uri in the relationship against the source part or the
            package root. This resolved Uri is then used by the Add method to figure
            out if a relationship is being created to another relationship part.
            </summary>
            <param name="target">PackageRelationship target uri</param>
            <param name="targetMode"> Enum value specifying the interpretation of the base uri
            for the relationship target uri</param>
            <returns>Resolved Uri</returns>
        </member>
        <member name="T:System.IO.Packaging.OrderedDictionary`2">
            <summary>
            A collection that ensures uniqueness among a list of elements while maintaining the order in which the elements were added.
            This is similar to <see cref="T:System.IO.Packaging.OrderedDictionary`2"/>, but the items will not be sorted by a comparer but rather retain the
            order in which they were added while still retaining good lookup, insertion, and removal.
            </summary>
        </member>
        <member name="T:System.IO.Packaging.Package">
            <summary>
            Abstract Base class for the Package.
            This is a part of the Packaging Layer APIs
            </summary>
        </member>
        <member name="M:System.IO.Packaging.Package.#ctor(System.IO.FileAccess)">
            <summary>
            Protected constructor for the abstract Base class.
            This is the current contract between the subclass and the base class
            If we decide some registration mechanism then this might change
            </summary>
            <param name="openFileAccess"></param>
            <exception cref="T:System.ArgumentOutOfRangeException">If FileAccess enumeration does not have one of the valid values</exception>
        </member>
        <member name="P:System.IO.Packaging.Package.FileOpenAccess">
            <summary>
            Gets the FileAccess with which the package was opened. This is a read only property.
            This property gets set when the package is opened.
            </summary>
            <value>FileAccess</value>
            <exception cref="T:System.ObjectDisposedException">If this Package object has been disposed</exception>
        </member>
        <member name="P:System.IO.Packaging.Package.PackageProperties">
            <summary>
            The package properties are a subset of the standard OLE property sets
            SummaryInformation and DocumentSummaryInformation, and include such properties
            as Title and Subject.
            </summary>
            <exception cref="T:System.ObjectDisposedException">If this Package object has been disposed</exception>
        </member>
        <member name="M:System.IO.Packaging.Package.Open(System.String)">
            <summary>
            Opens a package at the specified Path. This method calls the overload which accepts all the parameters
            with the following defaults -
            FileMode - FileMode.OpenOrCreate,
            FileAccess - FileAccess.ReadWrite
            FileShare  - FileShare.None
            </summary>
            <param name="path">Path to the package</param>
            <returns>Package</returns>
            <exception cref="T:System.ArgumentNullException">If path parameter is null</exception>
        </member>
        <member name="M:System.IO.Packaging.Package.Open(System.String,System.IO.FileMode)">
            <summary>
            Opens a package at the specified Path in the given mode. This method calls the overload which
            accepts all the parameters with the following defaults -
            FileAccess - FileAccess.ReadWrite
            FileShare  - FileShare.None
            </summary>
            <param name="path">Path to the package</param>
            <param name="packageMode">FileMode in which the package should be opened</param>
            <returns>Package</returns>
            <exception cref="T:System.ArgumentNullException">If path parameter is null</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">If FileMode enumeration [packageMode] does not have one of the valid values</exception>
        </member>
        <member name="M:System.IO.Packaging.Package.Open(System.String,System.IO.FileMode,System.IO.FileAccess)">
            <summary>
            Opens a package at the specified Path in the given mode with the specified access. This method calls
            the overload which accepts all the parameters with the following defaults -
            FileShare  - FileShare.None
            </summary>
            <param name="path">Path to the package</param>
            <param name="packageMode">FileMode in which the package should be opened</param>
            <param name="packageAccess">FileAccess with which the package should be opened</param>
            <returns>Package</returns>
            <exception cref="T:System.ArgumentNullException">If path parameter is null</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">If FileMode enumeration [packageMode] does not have one of the valid values</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">If FileAccess enumeration [packageAccess] does not have one of the valid values</exception>
        </member>
        <member name="M:System.IO.Packaging.Package.Open(System.IO.Stream)">
            <summary>
            Open a package on this stream. This method calls the overload which accepts all the parameters
            with the following defaults -
            FileMode - FileMode.Open
            FileAccess - FileAccess.Read
            </summary>
            <param name="stream">Stream on which the package is to be opened</param>
            <returns>Package</returns>
            <exception cref="T:System.ArgumentNullException">If stream parameter is null</exception>
            <exception cref="T:System.IO.IOException">If package to be created should have readwrite/read access and underlying stream is write only</exception>
            <exception cref="T:System.IO.IOException">If package to be created should have readwrite/write access and underlying stream is read only</exception>
        </member>
        <member name="M:System.IO.Packaging.Package.Open(System.IO.Stream,System.IO.FileMode)">
            <summary>
            Open a package on this stream. This method calls the overload which accepts all the parameters
            with the following defaults -
            FileAccess - FileAccess.ReadWrite
            </summary>
            <param name="stream">Stream on which the package is to be opened</param>
            <param name="packageMode">FileMode in which the package should be opened.</param>
            <returns>Package</returns>
            <exception cref="T:System.ArgumentNullException">If stream parameter is null</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">If FileMode enumeration [packageMode] does not have one of the valid values</exception>
            <exception cref="T:System.IO.IOException">If package to be created should have readwrite/read access and underlying stream is write only</exception>
            <exception cref="T:System.IO.IOException">If package to be created should have readwrite/write access and underlying stream is read only</exception>
        </member>
        <member name="M:System.IO.Packaging.Package.CreatePart(System.Uri,System.String)">
            <summary>
            Creates a new part in the package. An empty stream corresponding to this part will be created in the
            package. If a part with the specified uri already exists then we throw an exception.
            This methods will call the CreatePartCore method which will create the actual PackagePart in the package.
            </summary>
            <param name="partUri">Uri of the PackagePart that is to be added</param>
            <param name="contentType">ContentType of the stream to be added</param>
            <returns></returns>
            <exception cref="T:System.ObjectDisposedException">If this Package object has been disposed</exception>
            <exception cref="T:System.IO.IOException">If the package is readonly, it cannot be modified</exception>
            <exception cref="T:System.ArgumentNullException">If partUri parameter is null</exception>
            <exception cref="T:System.ArgumentNullException">If contentType parameter is null</exception>
            <exception cref="T:System.ArgumentException">If partUri parameter does not conform to the valid partUri syntax</exception>
            <exception cref="T:System.InvalidOperationException">If a PackagePart with the given partUri already exists in the Package</exception>
        </member>
        <member name="M:System.IO.Packaging.Package.CreatePart(System.Uri,System.String,System.IO.Packaging.CompressionOption)">
            <summary>
            Creates a new part in the package. An empty stream corresponding to this part will be created in the
            package. If a part with the specified uri already exists then we throw an exception.
            This methods will call the CreatePartCore method which will create the actual PackagePart in the package.
            </summary>
            <param name="partUri">Uri of the PackagePart that is to be added</param>
            <param name="contentType">ContentType of the stream to be added</param>
            <param name="compressionOption">CompressionOption  describing compression configuration
            for the new part. This compression apply only to the part, it doesn't affect relationship parts or related parts.
            This parameter is optional. </param>
            <returns></returns>
            <exception cref="T:System.ObjectDisposedException">If this Package object has been disposed</exception>
            <exception cref="T:System.IO.IOException">If the package is readonly, it cannot be modified</exception>
            <exception cref="T:System.ArgumentNullException">If partUri parameter is null</exception>
            <exception cref="T:System.ArgumentNullException">If contentType parameter is null</exception>
            <exception cref="T:System.ArgumentException">If partUri parameter does not conform to the valid partUri syntax</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">If CompressionOption enumeration [compressionOption] does not have one of the valid values</exception>
            <exception cref="T:System.InvalidOperationException">If a PackagePart with the given partUri already exists in the Package</exception>
        </member>
        <member name="M:System.IO.Packaging.Package.GetPart(System.Uri)">
            <summary>
            Returns a part that already exists in the package. If the part
            Corresponding to the URI does not exist in the package then an exception is
            thrown. The method calls the GetPartCore method which actually fetches the part.
            </summary>
            <param name="partUri"></param>
            <returns></returns>
            <exception cref="T:System.ObjectDisposedException">If this Package object has been disposed</exception>
            <exception cref="T:System.IO.IOException">If the package is write only, information cannot be retrieved from it</exception>
            <exception cref="T:System.ArgumentNullException">If partUri parameter is null</exception>
            <exception cref="T:System.ArgumentException">If partUri parameter does not conform to the valid partUri syntax</exception>
            <exception cref="T:System.InvalidOperationException">If the requested part does not exists in the Package</exception>
        </member>
        <member name="M:System.IO.Packaging.Package.PartExists(System.Uri)">
            <summary>
            This is a convenient method to check whether a given part exists in the
            package. This will have a default implementation that will try to retrieve
            the part and then if successful, it will return true.
            If the custom file format has an easier way to do this, they can override this method
            to get this information in a more efficient way.
            </summary>
            <param name="partUri"></param>
            <returns></returns>
            <exception cref="T:System.ObjectDisposedException">If this Package object has been disposed</exception>
            <exception cref="T:System.IO.IOException">If the package is write only, information cannot be retrieved from it</exception>
            <exception cref="T:System.ArgumentNullException">If partUri parameter is null</exception>
            <exception cref="T:System.ArgumentException">If partUri parameter does not conform to the valid partUri syntax</exception>
        </member>
        <member name="M:System.IO.Packaging.Package.DeletePart(System.Uri)">
            <summary>
            This method will do all the house keeping required when a part is deleted
            Then the DeletePartCore method will be called which will have the actual logic to
            do the work specific to the underlying file format and will actually delete the
            stream corresponding to this part. This method does not throw if the specified
            part does not exist. This is in conformance with the FileInfo.Delete call.
            </summary>
            <param name="partUri"></param>
            <exception cref="T:System.ObjectDisposedException">If this Package object has been disposed</exception>
            <exception cref="T:System.IO.IOException">If the package is readonly, it cannot be modified</exception>
            <exception cref="T:System.ArgumentNullException">If partUri parameter is null</exception>
            <exception cref="T:System.ArgumentException">If partUri parameter does not conform to the valid partUri syntax</exception>
        </member>
        <member name="M:System.IO.Packaging.Package.GetParts">
            <summary>
            This returns a collection of all the Parts within the package.
            </summary>
            <returns></returns>
            <exception cref="T:System.ObjectDisposedException">If this Package object has been disposed</exception>
            <exception cref="T:System.IO.IOException">If the package is writeonly, no information can be retrieved from it</exception>
        </member>
        <member name="M:System.IO.Packaging.Package.System#IDisposable#Dispose">
            <summary>
            Member of the IDisposable interface. This method will clean up all the resources.
            It calls the Flush method to make sure that all the changes made get persisted.
            Note - subclasses should only override Dispose(bool) if they have resources to release.
            See the Design Guidelines for the Dispose() pattern.
            </summary>
        </member>
        <member name="M:System.IO.Packaging.Package.Close">
            <summary>
            Closes the package and all the underlying parts and relationships.
            Calls the Dispose Method, since they have the same semantics
            </summary>
        </member>
        <member name="M:System.IO.Packaging.Package.Flush">
            <summary>
            Flushes the contents of the parts and the relationships to the package.
            This method will call the FlushCore method which will do the actual flushing of contents.
            </summary>
            <exception cref="T:System.ObjectDisposedException">If this Package object has been disposed</exception>
            <exception cref="T:System.IO.IOException">If the package is readonly, it cannot be modified</exception>
        </member>
        <member name="M:System.IO.Packaging.Package.CreateRelationship(System.Uri,System.IO.Packaging.TargetMode,System.String)">
            <summary>
            Creates a relationship at the Package level with the Target PackagePart specified as the Uri
            </summary>
            <param name="targetUri">Target's URI</param>
            <param name="targetMode">Enumeration indicating the base uri for the target uri</param>
            <param name="relationshipType">PackageRelationship type, having uri like syntax that is used to
            uniquely identify the role of the relationship</param>
            <returns></returns>
            <exception cref="T:System.ObjectDisposedException">If this Package object has been disposed</exception>
            <exception cref="T:System.IO.IOException">If the package is readonly, it cannot be modified</exception>
            <exception cref="T:System.ArgumentNullException">If parameter "targetUri" is null</exception>
            <exception cref="T:System.ArgumentNullException">If parameter "relationshipType" is null</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">If parameter "targetMode" enumeration does not have a valid value</exception>
            <exception cref="T:System.ArgumentException">If TargetMode is TargetMode.Internal and the targetUri is an absolute Uri </exception>
            <exception cref="T:System.ArgumentException">If relationship is being targeted to a relationship part</exception>
        </member>
        <member name="M:System.IO.Packaging.Package.CreateRelationship(System.Uri,System.IO.Packaging.TargetMode,System.String,System.String)">
            <summary>
            Creates a relationship at the Package level with the Target PackagePart specified as the Uri
            </summary>
            <param name="targetUri">Target's URI</param>
            <param name="targetMode">Enumeration indicating the base uri for the target uri</param>
            <param name="relationshipType">PackageRelationship type, having uri like syntax that is used to
            uniquely identify the role of the relationship</param>
            <param name="id">String that conforms to the xsd:ID datatype. Unique across the source's
            relationships. Null is OK (ID will be generated). An empty string is an invalid XML ID.</param>
            <returns></returns>
            <exception cref="T:System.ObjectDisposedException">If this Package object has been disposed</exception>
            <exception cref="T:System.IO.IOException">If the package is readonly, it cannot be modified</exception>
            <exception cref="T:System.ArgumentNullException">If parameter "targetUri" is null</exception>
            <exception cref="T:System.ArgumentNullException">If parameter "relationshipType" is null</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">If parameter "targetMode" enumeration does not have a valid value</exception>
            <exception cref="T:System.ArgumentException">If TargetMode is TargetMode.Internal and the targetUri is an absolute Uri </exception>
            <exception cref="T:System.ArgumentException">If relationship is being targeted to a relationship part</exception>
            <exception cref="T:System.Xml.XmlException">If parameter "id" is not a valid Xsd Id</exception>
            <exception cref="T:System.Xml.XmlException">If an id is provided in the method, and its not unique</exception>
        </member>
        <member name="M:System.IO.Packaging.Package.DeleteRelationship(System.String)">
            <summary>
            Deletes a relationship from the Package. This is done based on the
            relationship's ID. The target PackagePart is not affected by this operation.
            </summary>
            <param name="id">The ID of the relationship to delete. An invalid ID will not
            throw an exception, but nothing will be deleted.</param>
            <exception cref="T:System.ObjectDisposedException">If this Package object has been disposed</exception>
            <exception cref="T:System.IO.IOException">If the package is readonly, it cannot be modified</exception>
            <exception cref="T:System.ArgumentNullException">If parameter "id" is null</exception>
            <exception cref="T:System.Xml.XmlException">If parameter "id" is not a valid Xsd Id</exception>
        </member>
        <member name="M:System.IO.Packaging.Package.GetRelationships">
            <summary>
            Returns a collection of all the Relationships that are
            owned by the package
            </summary>
            <returns></returns>
            <exception cref="T:System.ObjectDisposedException">If this Package object has been disposed</exception>
            <exception cref="T:System.IO.IOException">If the package is write only, no information can be retrieved from it</exception>
        </member>
        <member name="M:System.IO.Packaging.Package.GetRelationshipsByType(System.String)">
            <summary>
            Returns a collection of filtered Relationships that are
            owned by the package
            The filter string is compared with the type of the relationships
            in a case sensitive and culture ignorant manner.
            </summary>
            <returns></returns>
            <exception cref="T:System.ObjectDisposedException">If this Package object has been disposed</exception>
            <exception cref="T:System.IO.IOException">If the package is write only, no information can be retrieved from it</exception>
            <exception cref="T:System.ArgumentNullException">If parameter "relationshipType" is null</exception>
            <exception cref="T:System.ArgumentException">If parameter "relationshipType" is an empty string</exception>
        </member>
        <member name="M:System.IO.Packaging.Package.GetRelationship(System.String)">
            <summary>
            Retrieve a relationship per ID.
            </summary>
            <param name="id">The relationship ID.</param>
            <returns>The relationship with ID 'id' or throw an exception if not found.</returns>
            <exception cref="T:System.ObjectDisposedException">If this Package object has been disposed</exception>
            <exception cref="T:System.IO.IOException">If the package is write only, no information can be retrieved from it</exception>
            <exception cref="T:System.ArgumentNullException">If parameter "id" is null</exception>
            <exception cref="T:System.Xml.XmlException">If parameter "id" is not a valid Xsd Id</exception>
            <exception cref="T:System.InvalidOperationException">If the requested relationship does not exist in the Package</exception>
        </member>
        <member name="M:System.IO.Packaging.Package.RelationshipExists(System.String)">
            <summary>
            Returns whether there is a relationship with the specified ID.
            </summary>
            <param name="id">The relationship ID.</param>
            <returns>true iff a relationship with ID 'id' is defined on this source.</returns>
            <exception cref="T:System.ObjectDisposedException">If this Package object has been disposed</exception>
            <exception cref="T:System.IO.IOException">If the package is write only, no information can be retrieved from it</exception>
            <exception cref="T:System.ArgumentNullException">If parameter "id" is null</exception>
            <exception cref="T:System.Xml.XmlException">If parameter "id" is not a valid Xsd Id</exception>
        </member>
        <member name="M:System.IO.Packaging.Package.CreatePartCore(System.Uri,System.String,System.IO.Packaging.CompressionOption)">
            <summary>
            This method is for custom implementation corresponding to the underlying file format.
            This method will actually add a new part to the package. An empty part should be
            created as a result of this call.
            </summary>
            <param name="partUri"></param>
            <param name="contentType"></param>
            <param name="compressionOption"></param>
            <returns></returns>
        </member>
        <member name="M:System.IO.Packaging.Package.GetPartCore(System.Uri)">
            <summary>
            This method is for custom implementation corresponding to the underlying file format.
            This method will actually return the part after reading the actual physical bits.
            If the PackagePart does not exists in the underlying package then this method should return a null.
            This method must not throw an exception if a part does not exist.
            </summary>
            <param name="partUri"></param>
            <returns></returns>
        </member>
        <member name="M:System.IO.Packaging.Package.DeletePartCore(System.Uri)">
            <summary>
            This method is for custom implementation corresponding to the underlying file format.
            This method will actually delete the part from the underlying package.
            This method should not throw if the specified part does not exist.
            This is in conformance with the FileInfo.Delete call.
            </summary>
            <param name="partUri"></param>
        </member>
        <member name="M:System.IO.Packaging.Package.GetPartsCore">
            <summary>
            This method is for custom implementation corresponding to the underlying file format.
            This is the method that knows how to get the actual parts. If there are no parts,
            this method should return an empty array.
            </summary>
            <returns></returns>
        </member>
        <member name="M:System.IO.Packaging.Package.Dispose(System.Boolean)">
            <summary>
            This method is for custom implementation corresponding to the underlying file format.
            This method should be used to dispose the resources that are specific to the file format.
            Also everything should be flushed to the disc before closing the package.
            </summary>
            <remarks>Subclasses that manage non-memory resources should override this method and free these resources.
            Any override should be careful to always call base.Dispose(disposing) to ensure orderly cleanup.</remarks>
        </member>
        <member name="M:System.IO.Packaging.Package.FlushCore">
            <summary>
            This method is for custom implementation corresponding to the underlying file format.
            This method flushes the contents of the package to the disc.
            </summary>
        </member>
        <member name="M:System.IO.Packaging.Package.Open(System.String,System.IO.FileMode,System.IO.FileAccess,System.IO.FileShare)">
            <summary>
            </summary>
            <param name="path">Path to the package.</param>
            <param name="packageMode">FileMode in which the package should be opened.</param>
            <param name="packageAccess">FileAccess with which the package should be opened.</param>
            <param name="packageShare">FileShare with which the package is opened.</param>
            <returns>Package</returns>
            <exception cref="T:System.ArgumentNullException">If path parameter is null</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">If FileAccess enumeration [packageAccess] does not have one of the valid values</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">If FileMode enumeration [packageMode] does not have one of the valid values</exception>
        </member>
        <member name="M:System.IO.Packaging.Package.Open(System.IO.Stream,System.IO.FileMode,System.IO.FileAccess)">
            <summary>
            </summary>
            <param name="stream">Stream on which the package is created</param>
            <param name="packageMode">FileMode in which the package is to be opened</param>
            <param name="packageAccess">FileAccess on the package that is opened</param>
            <returns>Package</returns>
            <exception cref="T:System.ArgumentNullException">If stream parameter is null</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">If FileMode enumeration [packageMode] does not have one of the valid values</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">If FileAccess enumeration [packageAccess] does not have one of the valid values</exception>
            <exception cref="T:System.IO.IOException">If package to be created should have readwrite/read access and underlying stream is write only</exception>
            <exception cref="T:System.IO.IOException">If package to be created should have readwrite/write access and underlying stream is read only</exception>
        </member>
        <member name="M:System.IO.Packaging.Package.GetRelationshipHelper(System.String)">
            <summary>
            Retrieve a relationship per ID.
            </summary>
            <param name="id">The relationship ID.</param>
            <returns>The relationship with ID 'id' or null if not found.</returns>
        </member>
        <member name="M:System.IO.Packaging.Package.GetRelationshipsHelper(System.String)">
            <summary>
            Returns a collection of all the Relationships that are
            owned by the package based on the filter string.
            </summary>
            <returns></returns>
        </member>
        <member name="T:System.IO.Packaging.PackagePart">
            <summary>
            This class represents the a PackagePart within a container.
            This is a part of the Packaging Layer APIs
            </summary>
        </member>
        <member name="M:System.IO.Packaging.PackagePart.#ctor(System.IO.Packaging.Package,System.Uri)">
             <summary>
             Protected constructor for the abstract Base class.
             This is the current contract between the subclass and the base class
             If we decide some registration mechanism then this might change
            
             You should use this constructor in the rare case when you do not have
             the content type information related to this part and would prefer to
             obtain it later as required.
            
             These parts have the CompressionOption as NotCompressed by default.
            
             NOTE : If you are using this constructor from your subclass or passing a null
             for the content type parameter, be sure to implement the GetContentTypeCore
             method, as that will be called to get the content type value. This is provided
             to enable lazy initialization of the ContentType property.
            
             </summary>
             <param name="package">Package in which this part is being created</param>
             <param name="partUri">uri of the part</param>
             <exception cref="T:System.ArgumentNullException">If parameter "package" is null</exception>
             <exception cref="T:System.ArgumentNullException">If parameter "partUri" is null</exception>
        </member>
        <member name="M:System.IO.Packaging.PackagePart.#ctor(System.IO.Packaging.Package,System.Uri,System.String)">
             <summary>
             Protected constructor for the abstract Base class.
             This is the current contract between the subclass and the base class
             If we decide some registration mechanism then this might change
            
             These parts have the CompressionOption as NotCompressed by default.
            
             NOTE : If you are using this constructor from your subclass or passing a null
             for the content type parameter, be sure to implement the GetContentTypeCore
             method, as that will be called to get the content type value. This is provided
             to enable lazy initialization of the ContentType property.
            
             </summary>
             <param name="package">Package in which this part is being created</param>
             <param name="partUri">uri of the part</param>
             <param name="contentType">Content Type of the part, can be null if the value
             is unknown at the time of construction. However the value has to be made
             available anytime the ContentType property is called. A null value only indicates
             that the value will be provided later. Every PackagePart must have a valid
             Content Type</param>
             <exception cref="T:System.ArgumentNullException">If parameter "package" is null</exception>
             <exception cref="T:System.ArgumentNullException">If parameter "partUri" is null</exception>
             <exception cref="T:System.ArgumentException">If parameter "partUri" does not conform to the valid partUri syntax</exception>
        </member>
        <member name="M:System.IO.Packaging.PackagePart.#ctor(System.IO.Packaging.Package,System.Uri,System.String,System.IO.Packaging.CompressionOption)">
             <summary>
             Protected constructor for the abstract Base class.
             This is the current contract between the subclass and the base class
             If we decide some registration mechanism then this might change
            
             NOTE : If you are using this constructor from your subclass or passing a null
             for the content type parameter, be sure to implement the GetContentTypeCore
             method, as that will be called to get the content type value. This is provided
             to enable lazy initialization of the ContentType property.
            
             </summary>
             <param name="package">Package in which this part is being created</param>
             <param name="partUri">uri of the part</param>
             <param name="contentType">Content Type of the part, can be null if the value
             is unknown at the time of construction. However the value has to be made
             available anytime the ContentType property is called. A null value only indicates
             that the value will be provided later. Every PackagePart must have a valid
             Content Type</param>
             <param name="compressionOption">compression option for this part</param>
             <exception cref="T:System.ArgumentNullException">If parameter "package" is null</exception>
             <exception cref="T:System.ArgumentNullException">If parameter "partUri" is null</exception>
             <exception cref="T:System.ArgumentOutOfRangeException">If CompressionOption enumeration [compressionOption] does not have one of the valid values</exception>
             <exception cref="T:System.ArgumentException">If parameter "partUri" does not conform to the valid partUri syntax</exception>
        </member>
        <member name="P:System.IO.Packaging.PackagePart.Uri">
            <summary>
            The Uri for this PackagePart. It is always relative to the Package Root
            The PackagePart properties can not be accessed if the parent container is closed.
            </summary>
            <value></value>
            <exception cref="T:System.InvalidOperationException">If this part has been deleted</exception>
            <exception cref="T:System.InvalidOperationException">If the parent package has been closed or disposed</exception>
        </member>
        <member name="P:System.IO.Packaging.PackagePart.ContentType">
            <summary>
            The Content type of the stream that is represented by this part.
            The PackagePart properties can not be accessed if the parent container is closed.
            The content type value can be provided by the underlying physical format
            implementation at the time of creation of the Part object ( constructor ) or
            We can initialize it in a lazy manner when the ContentType property is called
            called for the first time by calling the GetContentTypeCore method.
            Note: This method GetContentTypeCore() is only for lazy initialization of the Content
            type value and will only be called once. There is no way to change the content type of
            the part once it has been assigned.
            </summary>
            <value>Content Type of the Part [can never return null] </value>
            <exception cref="T:System.InvalidOperationException">If this part has been deleted</exception>
            <exception cref="T:System.InvalidOperationException">If the parent package has been closed or disposed</exception>
            <exception cref="T:System.InvalidOperationException">If the subclass fails to provide a non-null content type value.</exception>
        </member>
        <member name="P:System.IO.Packaging.PackagePart.Package">
            <summary>
            The parent container for this PackagePart
            The PackagePart properties can not be accessed if the parent container is closed.
            </summary>
            <value></value>
            <exception cref="T:System.InvalidOperationException">If this part has been deleted</exception>
            <exception cref="T:System.InvalidOperationException">If the parent package has been closed or disposed</exception>
        </member>
        <member name="P:System.IO.Packaging.PackagePart.CompressionOption">
            <summary>
            CompressionOption class that was provided as a parameter during the original CreatePart call.
            The PackagePart properties can not be accessed if the parent container is closed.
            </summary>
            <exception cref="T:System.InvalidOperationException">If this part has been deleted</exception>
            <exception cref="T:System.InvalidOperationException">If the parent package has been closed or disposed</exception>
        </member>
        <member name="M:System.IO.Packaging.PackagePart.GetContentTypeCore">
            <summary>
            Custom Implementation for the GetContentType Method
            This method should only be implemented by those physical format implementors where
            the value for the content type cannot be provided at the time of construction of
            Part object and if calculating the content type value is a non-trivial or costly
            operation. The return value has to be a valid ContentType. This method will be used in
            real corner cases. The most common usage should be to provide the content type in the
            constructor.
            This method is only for lazy initialization of the Content type value and will only
            be called once. There is no way to change the content type of the part once it is
            assigned.
            </summary>
            <returns>Content type for the Part</returns>
            <exception cref="T:System.NotSupportedException">By default, this method throws a NotSupportedException. If a subclass wants to
            initialize the content type for a PackagePart in a lazy manner they must override this method.</exception>
        </member>
        <member name="M:System.IO.Packaging.PackagePart.GetStream">
            <summary>
            Returns the underlying stream that is represented by this part
            with the default FileMode and FileAccess
            Note: If you are requesting a stream for a relationship part and
            at the same time using relationship APIs to manipulate relationships,
            the final persisted data will depend on which data gets flushed last.
            </summary>
            <returns></returns>
            <exception cref="T:System.InvalidOperationException">If this part has been deleted</exception>
            <exception cref="T:System.InvalidOperationException">If the parent package has been closed or disposed</exception>
            <exception cref="T:System.IO.IOException">If the subclass fails to provide a non-null stream object</exception>
        </member>
        <member name="M:System.IO.Packaging.PackagePart.GetStream(System.IO.FileMode)">
            <summary>
            Returns the underlying stream in the specified mode and the
            default FileAccess
            Note: If you are requesting a stream for a relationship part for editing
            and at the same time using relationship APIs to manipulate relationships,
            the final persisted data will depend on which data gets flushed last.
            </summary>
            <param name="mode"></param>
            <returns></returns>
            <exception cref="T:System.InvalidOperationException">If this part has been deleted</exception>
            <exception cref="T:System.InvalidOperationException">If the parent package has been closed or disposed</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">If FileMode enumeration [mode] does not have one of the valid values</exception>
            <exception cref="T:System.IO.IOException">If FileAccess.Read is provided and FileMode values are any of the following -
            FileMode.Create, FileMode.CreateNew, FileMode.Truncate, FileMode.Append</exception>
            <exception cref="T:System.IO.IOException">If the mode and access for the Package and the Stream are not compatible</exception>
            <exception cref="T:System.IO.IOException">If the subclass fails to provide a non-null stream object</exception>
        </member>
        <member name="M:System.IO.Packaging.PackagePart.GetStream(System.IO.FileMode,System.IO.FileAccess)">
            <summary>
            Returns the underlying stream that is represented by this part
            in the specified mode with the access.
            Note: If you are requesting a stream for a relationship part and
            at the same time using relationship APIs to manipulate relationships,
            the final persisted data will depend on which data gets flushed last.
            </summary>
            <param name="mode"></param>
            <param name="access"></param>
            <returns></returns>
            <exception cref="T:System.InvalidOperationException">If this part has been deleted</exception>
            <exception cref="T:System.InvalidOperationException">If the parent package has been closed or disposed</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">If FileMode enumeration [mode] does not have one of the valid values</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">If FileAccess enumeration [access] does not have one of the valid values</exception>
            <exception cref="T:System.IO.IOException">If FileAccess.Read is provided and FileMode values are any of the following -
            FileMode.Create, FileMode.CreateNew, FileMode.Truncate, FileMode.Append</exception>
            <exception cref="T:System.IO.IOException">If the mode and access for the Package and the Stream are not compatible</exception>
            <exception cref="T:System.IO.IOException">If the subclass fails to provide a non-null stream object</exception>
        </member>
        <member name="M:System.IO.Packaging.PackagePart.GetStreamCore(System.IO.FileMode,System.IO.FileAccess)">
            <summary>
            Custom Implementation for the GetSream Method
            </summary>
            <param name="mode"></param>
            <param name="access"></param>
            <returns></returns>
        </member>
        <member name="M:System.IO.Packaging.PackagePart.CreateRelationship(System.Uri,System.IO.Packaging.TargetMode,System.String)">
            <summary>
            Adds a relationship to this PackagePart with the Target PackagePart specified as the Uri
            Initial and trailing spaces in the name of the PackageRelationship are trimmed.
            </summary>
            <param name="targetUri"></param>
            <param name="targetMode">Enumeration indicating the base uri for the target uri</param>
            <param name="relationshipType">PackageRelationship type, having uri like syntax that is used to
            uniquely identify the role of the relationship</param>
            <returns></returns>
            <exception cref="T:System.InvalidOperationException">If this part has been deleted</exception>
            <exception cref="T:System.InvalidOperationException">If the parent package has been closed or disposed</exception>
            <exception cref="T:System.IO.IOException">If the package is readonly, it cannot be modified</exception>
            <exception cref="T:System.ArgumentNullException">If parameter "targetUri" is null</exception>
            <exception cref="T:System.ArgumentNullException">If parameter "relationshipType" is null</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">If parameter "targetMode" enumeration does not have a valid value</exception>
            <exception cref="T:System.ArgumentException">If TargetMode is TargetMode.Internal and the targetUri is an absolute Uri </exception>
            <exception cref="T:System.ArgumentException">If relationship is being targeted to a relationship part</exception>
        </member>
        <member name="M:System.IO.Packaging.PackagePart.CreateRelationship(System.Uri,System.IO.Packaging.TargetMode,System.String,System.String)">
            <summary>
            Adds a relationship to this PackagePart with the Target PackagePart specified as the Uri
            Initial and trailing spaces in the name of the PackageRelationship are trimmed.
            </summary>
            <param name="targetUri"></param>
            <param name="targetMode">Enumeration indicating the base uri for the target uri</param>
            <param name="relationshipType">PackageRelationship type, having uri like syntax that is used to
            uniquely identify the role of the relationship</param>
            <param name="id">String that conforms to the xsd:ID datatype. Unique across the source's
            relationships. Null is OK (ID will be generated). An empty string is an invalid XML ID.</param>
            <returns></returns>
            <exception cref="T:System.InvalidOperationException">If this part has been deleted</exception>
            <exception cref="T:System.InvalidOperationException">If the parent package has been closed or disposed</exception>
            <exception cref="T:System.IO.IOException">If the package is readonly, it cannot be modified</exception>
            <exception cref="T:System.ArgumentNullException">If parameter "targetUri" is null</exception>
            <exception cref="T:System.ArgumentNullException">If parameter "relationshipType" is null</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">If parameter "targetMode" enumeration does not have a valid value</exception>
            <exception cref="T:System.ArgumentException">If TargetMode is TargetMode.Internal and the targetUri is an absolute Uri </exception>
            <exception cref="T:System.ArgumentException">If relationship is being targeted to a relationship part</exception>
            <exception cref="T:System.Xml.XmlException">If parameter "id" is not a valid Xsd Id</exception>
            <exception cref="T:System.Xml.XmlException">If an id is provided in the method, and its not unique</exception>
        </member>
        <member name="M:System.IO.Packaging.PackagePart.DeleteRelationship(System.String)">
            <summary>
            Deletes a relationship from the PackagePart. This is done based on the
            relationship's ID. The target PackagePart is not affected by this operation.
            </summary>
            <param name="id">The ID of the relationship to delete. An invalid ID will not
            throw an exception, but nothing will be deleted.</param>
            <exception cref="T:System.InvalidOperationException">If this part has been deleted</exception>
            <exception cref="T:System.InvalidOperationException">If the parent package has been closed or disposed</exception>
            <exception cref="T:System.IO.IOException">If the package is readonly, it cannot be modified</exception>
            <exception cref="T:System.ArgumentNullException">If parameter "id" is null</exception>
            <exception cref="T:System.Xml.XmlException">If parameter "id" is not a valid Xsd Id</exception>
        </member>
        <member name="M:System.IO.Packaging.PackagePart.GetRelationships">
            <summary>
            Returns a collection of all the Relationships that are
            owned by this PackagePart
            </summary>
            <returns></returns>
            <exception cref="T:System.InvalidOperationException">If this part has been deleted</exception>
            <exception cref="T:System.InvalidOperationException">If the parent package has been closed or disposed</exception>
            <exception cref="T:System.IO.IOException">If the package is write only, no information can be retrieved from it</exception>
        </member>
        <member name="M:System.IO.Packaging.PackagePart.GetRelationshipsByType(System.String)">
            <summary>
            Returns a collection of filtered Relationships that are
            owned by this PackagePart
            The relationshipType string is compared with the type of the relationships
            in a case sensitive and culture ignorant manner.
            </summary>
            <returns></returns>
            <exception cref="T:System.InvalidOperationException">If this part has been deleted</exception>
            <exception cref="T:System.InvalidOperationException">If the parent package has been closed or disposed</exception>
            <exception cref="T:System.IO.IOException">If the package is write only, no information can be retrieved from it</exception>
            <exception cref="T:System.ArgumentNullException">If parameter "relationshipType" is null</exception>
            <exception cref="T:System.ArgumentException">If parameter "relationshipType" is an empty string</exception>
        </member>
        <member name="M:System.IO.Packaging.PackagePart.GetRelationship(System.String)">
            <summary>
            Retrieve a relationship per ID.
            </summary>
            <param name="id">The relationship ID.</param>
            <returns>The relationship with ID 'id' or throw an exception if not found.</returns>
            <exception cref="T:System.InvalidOperationException">If this part has been deleted</exception>
            <exception cref="T:System.InvalidOperationException">If the parent package has been closed or disposed</exception>
            <exception cref="T:System.IO.IOException">If the package is write only, no information can be retrieved from it</exception>
            <exception cref="T:System.ArgumentNullException">If parameter "id" is null</exception>
            <exception cref="T:System.Xml.XmlException">If parameter "id" is not a valid Xsd Id</exception>
            <exception cref="T:System.InvalidOperationException">If the requested relationship does not exist in the Package</exception>
        </member>
        <member name="M:System.IO.Packaging.PackagePart.RelationshipExists(System.String)">
            <summary>
            Returns whether there is a relationship with the specified ID.
            </summary>
            <param name="id">The relationship ID.</param>
            <returns>true iff a relationship with ID 'id' is defined on this source.</returns>
            <exception cref="T:System.InvalidOperationException">If this part has been deleted</exception>
            <exception cref="T:System.InvalidOperationException">If the parent package has been closed or disposed</exception>
            <exception cref="T:System.IO.IOException">If the package is write only, no information can be retrieved from it</exception>
            <exception cref="T:System.ArgumentNullException">If parameter "id" is null</exception>
            <exception cref="T:System.Xml.XmlException">If parameter "id" is not a valid Xsd Id</exception>
        </member>
        <member name="P:System.IO.Packaging.PackagePart.ValidatedContentType">
            <summary>
            This property returns the content type of the part
            as a validated strongly typed ContentType object
            </summary>
        </member>
        <member name="M:System.IO.Packaging.PackagePart.FlushRelationships">
            <summary>
            write the relationships part
            </summary>
            <remarks>
            </remarks>
        </member>
        <member name="M:System.IO.Packaging.PackagePart.GetRelationshipHelper(System.String)">
            <summary>
            Retrieve a relationship per ID.
            </summary>
            <param name="id">The relationship ID.</param>
            <returns>The relationship with ID 'id' or null if not found.</returns>
        </member>
        <member name="M:System.IO.Packaging.PackagePart.GetRelationshipsHelper(System.String)">
            <summary>
            Returns a collection of all the Relationships that are
            owned by this PackagePart, based on the filter string
            </summary>
            <returns></returns>
        </member>
        <member name="T:System.IO.Packaging.PackagePartCollection">
            <summary>
            This class is used to get an enumerator for the Parts in a container.
            This is a part of the MMCF Packaging Layer APIs
            </summary>
        </member>
        <member name="M:System.IO.Packaging.PackagePartCollection.System#Collections#IEnumerable#GetEnumerator">
            <summary>
            Returns an enumerator over all the Parts in the container
            </summary>
            <returns></returns>
        </member>
        <member name="M:System.IO.Packaging.PackagePartCollection.System#Collections#Generic#IEnumerable{System#IO#Packaging#PackagePart}#GetEnumerator">
            <summary>
            Returns an enumerator over all the Parts in the container
            </summary>
            <returns></returns>
        </member>
        <member name="M:System.IO.Packaging.PackagePartCollection.GetEnumerator">
            <summary>
            Returns an enumerator over all the Parts in the Container
            </summary>
            <returns></returns>
        </member>
        <member name="T:System.IO.Packaging.PackageProperties">
            <summary>
            This class provides access to the "core properties", such as Title and
            Subject, of an RM-protected XPS package. These properties are a subset of
            of the standard OLE property sets SummaryInformation and
            DocumentSummaryInformation.
            </summary>
        </member>
        <member name="M:System.IO.Packaging.PackageProperties.Dispose">
            <summary>
            Allow the object to clean up all resources it holds (both managed and
            unmanaged), and ensure that the resources won't be released a
            second time by removing it from the finalization queue.
            </summary>
        </member>
        <member name="M:System.IO.Packaging.PackageProperties.Dispose(System.Boolean)">
            <summary>
            This default implementation is provided for subclasses that do not
            make use of the IDisposable functionality.
            </summary>
        </member>
        <member name="P:System.IO.Packaging.PackageProperties.Title">
            <summary>
            The title.
            </summary>
        </member>
        <member name="P:System.IO.Packaging.PackageProperties.Subject">
            <summary>
            The topic of the contents.
            </summary>
        </member>
        <member name="P:System.IO.Packaging.PackageProperties.Creator">
            <summary>
            The primary creator. The identification is environment-specific and
            can consist of a name, email address, employee ID, etc. It is
            recommended that this value be only as verbose as necessary to
            identify the individual.
            </summary>
        </member>
        <member name="P:System.IO.Packaging.PackageProperties.Keywords">
            <summary>
            A delimited set of keywords to support searching and indexing. This
            is typically a list of terms that are not available elsewhere in the
            properties.
            </summary>
        </member>
        <member name="P:System.IO.Packaging.PackageProperties.Description">
            <summary>
            The description or abstract of the contents.
            </summary>
        </member>
        <member name="P:System.IO.Packaging.PackageProperties.LastModifiedBy">
            <summary>
            The user who performed the last modification. The identification is
            environment-specific and can consist of a name, email address,
            employee ID, etc. It is recommended that this value be only as
            verbose as necessary to identify the individual.
            </summary>
        </member>
        <member name="P:System.IO.Packaging.PackageProperties.Revision">
            <summary>
            The revision number. This value indicates the number of saves or
            revisions. The application is responsible for updating this value
            after each revision.
            </summary>
        </member>
        <member name="P:System.IO.Packaging.PackageProperties.LastPrinted">
            <summary>
            The date and time of the last printing.
            </summary>
        </member>
        <member name="P:System.IO.Packaging.PackageProperties.Created">
            <summary>
            The creation date and time.
            </summary>
        </member>
        <member name="P:System.IO.Packaging.PackageProperties.Modified">
            <summary>
            The date and time of the last modification.
            </summary>
        </member>
        <member name="P:System.IO.Packaging.PackageProperties.Category">
            <summary>
            The category. This value is typically used by UI applications to create navigation
            controls.
            </summary>
        </member>
        <member name="P:System.IO.Packaging.PackageProperties.Identifier">
            <summary>
            A unique identifier.
            </summary>
        </member>
        <member name="P:System.IO.Packaging.PackageProperties.ContentType">
            <summary>
            The type of content represented, generally defined by a specific
            use and intended audience. Example values include "Whitepaper",
            "Security Bulletin", and "Exam". (This property is distinct from
            MIME content types as defined in RFC 2045.)
            </summary>
        </member>
        <member name="P:System.IO.Packaging.PackageProperties.Language">
            <summary>
            The primary language of the package content. The language tag is
            composed of one or more parts: A primary language subtag and a
            (possibly empty) series of subsequent subtags, for example, "EN-US".
            These values MUST follow the convention specified in RFC 3066.
            </summary>
        </member>
        <member name="P:System.IO.Packaging.PackageProperties.Version">
            <summary>
            The version number. This value is set by the user or by the application.
            </summary>
        </member>
        <member name="P:System.IO.Packaging.PackageProperties.ContentStatus">
            <summary>
            The status of the content. Example values include "Draft",
            "Reviewed", and "Final".
            </summary>
        </member>
        <member name="T:System.IO.Packaging.PackageRelationship">
            <summary>
            This class is used to express a relationship between a source and a target part.
            The only way to create a PackageRelationship, is to call the PackagePart.CreateRelationship()
            or Package.CreateRelationship(). A relationship is owned by a part or by the package itself.
            If the source part is deleted all the relationships it owns are also deleted.
            A target of the relationship need not be present.
            This class is part of the MMCF Packaging layer.
            </summary>
        </member>
        <member name="P:System.IO.Packaging.PackageRelationship.SourceUri">
            <summary>
            This is a reference to the parent PackagePart to which this relationship belongs.
            </summary>
            <value>Uri</value>
        </member>
        <member name="P:System.IO.Packaging.PackageRelationship.TargetUri">
            <summary>
            Uri of the TargetPart, that this relationship points to.
            </summary>
            <value></value>
        </member>
        <member name="P:System.IO.Packaging.PackageRelationship.RelationshipType">
            <summary>
            Type of the relationship - used to uniquely define the role of the relationship
            </summary>
            <value></value>
        </member>
        <member name="P:System.IO.Packaging.PackageRelationship.TargetMode">
            <summary>
            Enumeration value indicating the interpretations of the "base" of the target uri.
            </summary>
            <value></value>
        </member>
        <member name="P:System.IO.Packaging.PackageRelationship.Id">
            <summary>
            PackageRelationship's identifier. Unique across relationships for the given source.
            </summary>
            <value>String</value>
        </member>
        <member name="P:System.IO.Packaging.PackageRelationship.Package">
            <summary>
            PackageRelationship's owning Package object.
            </summary>
            <value>Package</value>
        </member>
        <member name="M:System.IO.Packaging.PackageRelationship.#ctor(System.IO.Packaging.Package,System.IO.Packaging.PackagePart,System.Uri,System.IO.Packaging.TargetMode,System.String,System.String)">
            <summary>
            PackageRelationship constructor
            </summary>
            <param name="package">Owning Package object for this relationship</param>
            <param name="sourcePart">owning part - will be null if the owner is the container</param>
            <param name="targetUri">target of relationship</param>
            <param name="targetMode">enum specifying the interpretation of the base uri for the target uri</param>
            <param name="relationshipType">type name</param>
            <param name="id">unique identifier</param>
        </member>
        <member name="T:System.IO.Packaging.PackageRelationshipCollection">
            <summary>
            Collection of all the relationships corresponding to a given source PackagePart.
            This class is part of the MMCF Packaging Layer. It handles serialization to/from
            relationship parts, creation of those parts and offers methods to create, delete
            and enumerate relationships.
            </summary>
        </member>
        <member name="M:System.IO.Packaging.PackageRelationshipCollection.System#Collections#IEnumerable#GetEnumerator">
            <summary>
            Returns an enumerator for all the relationships for a PackagePart
            </summary>
            <returns></returns>
        </member>
        <member name="M:System.IO.Packaging.PackageRelationshipCollection.GetEnumerator">
            <summary>
            Returns an enumerator over all the relationships for a PackagePart
            </summary>
            <returns></returns>
        </member>
        <member name="M:System.IO.Packaging.PackageRelationshipCollection.#ctor(System.IO.Packaging.InternalRelationshipCollection,System.String)">
            <summary>
            Constructor
            </summary>
            <remarks>For use by PackagePart</remarks>
        </member>
        <member name="T:System.IO.Packaging.PackageRelationshipCollection.FilteredEnumerator">
            <summary>
            Internal class for the FilteredEnumerator
            </summary>
        </member>
        <member name="M:System.IO.Packaging.PackageRelationshipCollection.FilteredEnumerator.#ctor(System.Collections.Generic.IEnumerator{System.IO.Packaging.PackageRelationship},System.String)">
            <summary>
            Constructs a FilteredEnumerator
            </summary>
            <param name="enumerator"></param>
            <param name="filter"></param>
        </member>
        <member name="M:System.IO.Packaging.PackageRelationshipCollection.FilteredEnumerator.System#Collections#IEnumerator#MoveNext">
            <summary>
            This method keeps moving the enumerator the next position till
            a relationship is found with the matching Name
            </summary>
            <returns>Bool indicating if the enumerator successfully moved to the next position</returns>
        </member>
        <member name="P:System.IO.Packaging.PackageRelationshipCollection.FilteredEnumerator.System#Collections#IEnumerator#Current">
            <summary>
            Gets the current object in the enumerator
            </summary>
            <value></value>
        </member>
        <member name="M:System.IO.Packaging.PackageRelationshipCollection.FilteredEnumerator.System#Collections#IEnumerator#Reset">
            <summary>
            Resets the enumerator to the beginning
            </summary>
        </member>
        <member name="P:System.IO.Packaging.PackageRelationshipCollection.FilteredEnumerator.Current">
            <summary>
            Gets the current object in the enumerator
            </summary>
            <value></value>
        </member>
        <member name="T:System.IO.Packaging.PackageRelationshipSelector">
            <summary>
            This class is used to represent a PackageRelationship selector. PackageRelationships can be
            selected based on their Type or ID. This class will specify what the selection is based on and
            what the actual criteria is. </summary>
        </member>
        <member name="M:System.IO.Packaging.PackageRelationshipSelector.#ctor(System.Uri,System.IO.Packaging.PackageRelationshipSelectorType,System.String)">
            <summary>
            Constructor
            </summary>
            <param name="sourceUri">Source Uri of the PackagePart or PackageRoot ("/") that owns the relationship</param>
            <param name="selectorType">PackageRelationshipSelectorType enum representing the type of the selectionCriteria</param>
            <param name="selectionCriteria">The actual string that is used to select the relationships</param>
            <exception cref="T:System.ArgumentNullException">If sourceUri is null</exception>
            <exception cref="T:System.ArgumentNullException">If selectionCriteria is null</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">If selectorType Enumeration does not have a valid value</exception>
            <exception cref="T:System.Xml.XmlException">If PackageRelationshipSelectorType.Id and selection criteria is not valid Xsd Id</exception>
            <exception cref="T:System.ArgumentException">If PackageRelationshipSelectorType.Type and selection criteria is not valid relationship type</exception>
            <exception cref="T:System.ArgumentException">If sourceUri is not "/" to indicate the PackageRoot, then it must conform to the
            valid PartUri syntax</exception>
        </member>
        <member name="P:System.IO.Packaging.PackageRelationshipSelector.SourceUri">
            <summary>
            This is a uri to the parent PackagePart to which this relationship belongs.
            </summary>
            <value>PackagePart</value>
        </member>
        <member name="P:System.IO.Packaging.PackageRelationshipSelector.SelectorType">
            <summary>
            Enumeration value indicating the interpretations of the SelectionCriteria.
            </summary>
            <value></value>
        </member>
        <member name="P:System.IO.Packaging.PackageRelationshipSelector.SelectionCriteria">
            <summary>
            Selection Criteria - actual value (could be ID or type) on which the selection is based
            </summary>
            <value></value>
        </member>
        <member name="M:System.IO.Packaging.PackageRelationshipSelector.Select(System.IO.Packaging.Package)">
            <summary>
            This method returns the list of selected PackageRelationships as per the
            given criteria, from a part in the Package provided
            </summary>
            <param name="package">Package object from which we get the relationships</param>
            <returns></returns>
            <exception cref="T:System.ArgumentNullException">If package parameter is null</exception>
        </member>
        <member name="T:System.IO.Packaging.PackageRelationshipSelectorType">
            <summary>
            Enum to represent the different selector types for PackageRelationshipSelector
            </summary>
        </member>
        <member name="F:System.IO.Packaging.PackageRelationshipSelectorType.Id">
            <summary>
            Id
            </summary>
        </member>
        <member name="F:System.IO.Packaging.PackageRelationshipSelectorType.Type">
            <summary>
            Type
            </summary>
        </member>
        <member name="M:System.IO.Packaging.PackagingUtilities.PerformInitialReadAndVerifyEncoding(System.Xml.XmlReader)">
            <summary>
            This method is used to determine if we support a given Encoding as per the
            OPC and XPS specs. Currently the only two encodings supported are UTF-8 and
            UTF-16 (Little Endian and Big Endian)
            </summary>
            <param name="reader">XmlReader</param>
        </member>
        <member name="M:System.IO.Packaging.PackagingUtilities.GetNonXmlnsAttributeCount(System.Xml.XmlReader)">
            <summary>
            This method returns the count of xml attributes other than:
            1. xmlns="namespace"
            2. xmlns:someprefix="namespace"
            Reader should be positioned at the Element whose attributes
            are to be counted.
            </summary>
            <param name="reader"></param>
            <returns>An integer indicating the number of non-xmlns attributes</returns>
        </member>
        <member name="F:System.IO.Packaging.PackagingUtilities.XmlNamespace">
            <summary>
            Synchronize access to IsolatedStorage methods that can step on each-other
            </summary>
            <remarks>See PS 1468964 for details.</remarks>
        </member>
        <member name="T:System.IO.Packaging.PackUriHelper">
            <summary>
            This class has the utility methods for composing and parsing an Uri of pack:// scheme
            </summary>
            <summary>
            This class has the utility methods for composing and parsing an Uri of pack:// scheme
            </summary>
        </member>
        <member name="M:System.IO.Packaging.PackUriHelper.CreatePartUri(System.Uri)">
            <summary>
            This method is used to create a valid part Uri given a relative URI
            Makes sure that the URI -
            1. Relative
            2. Begins with '/'
            3. Does not begin with two "//"
            4. Does not end with "/"
            5. Does not have a fragment component
            6. Does the correct escaping
            7. And is refined correctly
            </summary>
            <param name="partUri">The relative uri that represents the part within a package</param>
            <returns>Returns a relative URI with an absolute path that points to a part within a package</returns>
            <exception cref="T:System.ArgumentNullException">If partUri parameter is null</exception>
            <exception cref="T:System.ArgumentException">If partUri parameter is an absolute Uri</exception>
            <exception cref="T:System.ArgumentException">If partUri parameter is empty</exception>
            <exception cref="T:System.ArgumentException">If partUri parameter ends with "/"</exception>
            <exception cref="T:System.ArgumentException">If partUri parameter starts with two "/"</exception>
            <exception cref="T:System.ArgumentException">If partUri parameter has a fragment</exception>
        </member>
        <member name="M:System.IO.Packaging.PackUriHelper.ResolvePartUri(System.Uri,System.Uri)">
            <summary>
            This method is used to resolve part Uris
            Constructs resolved relative URI from two relative URIs
            This method should be used in places where we have a
            a target URI in the PackageRelationship and we want to get the
            name of the part it targets with respect to the source part
            </summary>
            <param name="sourcePartUri">This should be a valid partName.
            The only exception to this rule is an Uri of the form "/". This uri
            will only be used to resolve package level relationships. This Uri
            indicates that the relative Uri is being resolved against the root of the
            package.</param>
            <param name="targetUri">This URI can be any relative URI</param>
            <returns></returns>
            <exception cref="T:System.ArgumentNullException">If either sourcePartUri or targetUri parameter is null</exception>
            <exception cref="T:System.ArgumentException">If either sourcePartUri or targetUri parameter is an absolute Uri</exception>
            <exception cref="T:System.ArgumentException">If sourcePartUri parameter does not conform to the valid partUri syntax</exception>
        </member>
        <member name="M:System.IO.Packaging.PackUriHelper.GetRelativeUri(System.Uri,System.Uri)">
            <summary>
            This method returns the relative uri between two given parts
            </summary>
            <param name="sourcePartUri"></param>
            <param name="targetPartUri"></param>
            <returns>The relative path between two parts</returns>
            <exception cref="T:System.ArgumentNullException">If either the sourcePartUri or targetPartUri parameter is null</exception>
            <exception cref="T:System.ArgumentException">If either sourcePartUri or targetPartUri parameter does not conform to the valid partUri syntax</exception>
        </member>
        <member name="M:System.IO.Packaging.PackUriHelper.GetNormalizedPartUri(System.Uri)">
            <summary>
            Returns the normalized form of the part URI
            </summary>
            <param name="partUri">Part Uri</param>
            <returns>Normalized Part Uri</returns>
            <exception cref="T:System.ArgumentNullException">If partUri is null</exception>
            <exception cref="T:System.ArgumentException">If partUri parameter does not conform to the valid partUri syntax</exception>
        </member>
        <member name="M:System.IO.Packaging.PackUriHelper.ComparePartUri(System.Uri,System.Uri)">
            <summary>
            This method compares two part uris and returns an int to indicate the equivalence
            Null values are allowed
            </summary>
            <param name="firstPartUri">First part Uri to be compared</param>
            <param name="secondPartUri">Second part Uri to be compared</param>
            <returns>A 32-bit signed integer indicating the lexical relationship between the compared Uri components.
            Value - Less than zero means firstUri is less than secondUri
            Value - Equal to zero means both the Uris are equal</returns>
            Value - Greater than zero means firstUri is greater than secondUri
            <exception cref="T:System.ArgumentException">If firstPartUri or secondPartUri parameter does not conform to the valid partUri syntax</exception>
        </member>
        <member name="M:System.IO.Packaging.PackUriHelper.IsRelationshipPartUri(System.Uri)">
            <summary>
            IsRelationshipPartUri method returns a boolean indicating whether the
            Uri given is a relationship part Uri or not.
            </summary>
            <param name="partUri">uri of part to evaluate</param>
            <returns>true if the given part is a PackageRelationship part</returns>
            <remarks>Does not inspect the part contents - this is based solely on the name</remarks>
            <exception cref="T:System.ArgumentNullException">If partUri parameter is null</exception>
            <exception cref="T:System.ArgumentException">If partUri parameter is an absolute Uri</exception>
            <exception cref="T:System.ArgumentException">If partUri parameter does not conform to the valid partUri Syntax</exception>
        </member>
        <member name="M:System.IO.Packaging.PackUriHelper.GetRelationshipPartUri(System.Uri)">
            <summary>
            This method returns a relationship part Uri given a part Uri
            Example Input - partUri - /files/document.xaml
            Return  - Relationship Uri  - /files/_rels/document.xaml.rels
            If the input to the method is Uri - "/", then we will return /_rels/.rels as the
            relationship part Uri for the Package level relationships
            </summary>
            <param name="partUri">Part Uri for which the relationship part Uri is wanted</param>
            <returns>returns a Uri that conforms to the relationship part Uri syntax</returns>
            <exception cref="T:System.ArgumentException">If the input Uri is a relationship part Uri itself</exception>
            <exception cref="T:System.ArgumentNullException">If partUri parameter is null</exception>
            <exception cref="T:System.ArgumentException">If partUri parameter is an absolute Uri</exception>
            <exception cref="T:System.ArgumentException">If partUri parameter does not conform to the valid partUri Syntax</exception>
        </member>
        <member name="M:System.IO.Packaging.PackUriHelper.GetSourcePartUriFromRelationshipPartUri(System.Uri)">
            <summary>
            Given a valid relationship Part Uri, this method returns the source Part Uri for
            this relationship Part Uri.
            If the relationship part name is for the Package Level relationships [/_rels/.rels],
            we return a relative Uri of the form "/" indicating that it has no part as the parent,
            but is at the package level
            Example Input - Relationship Uri  - /files/_rels/document.xaml.rels
            Returns -Source Part Uri - /files/document.xaml
            </summary>
            <param name="relationshipPartUri">relationship part Uri</param>
            <returns>A uri that is a valid source part Uri for the relationship Uri provided</returns>
            <exception cref="T:System.ArgumentNullException">If relationshipPartUri parameter is null</exception>
            <exception cref="T:System.ArgumentException">If relationshipPartUri parameter is an absolute Uri</exception>
            <exception cref="T:System.ArgumentException">If relationshipPartUri parameter does not conform to the valid partUri Syntax</exception>
            <exception cref="T:System.ArgumentException">If the relationshipPartUri is not a relationship part Uri itself</exception>
            <exception cref="T:System.ArgumentException">If the resultant Uri obtained is a relationship part Uri</exception>
        </member>
        <member name="M:System.IO.Packaging.PackUriHelper.ValidatePartUri(System.Uri)">
            <summary>
            This method is used to validate a part Uri
            This method does not perform a case sensitive check of the Uri
            </summary>
            <param name="partUri">The string that represents the part within a package</param>
            <returns>Returns the part uri if it is valid</returns>
            <exception cref="T:System.ArgumentNullException">If partUri parameter is null</exception>
            <exception cref="T:System.ArgumentException">If partUri parameter is an absolute Uri</exception>
            <exception cref="T:System.ArgumentException">If partUri parameter is empty</exception>
            <exception cref="T:System.ArgumentException">If partUri parameter does not start with a "/"</exception>
            <exception cref="T:System.ArgumentException">If partUri parameter starts with two "/"</exception>
            <exception cref="T:System.ArgumentException">If partUri parameter ends with a "/"</exception>
            <exception cref="T:System.ArgumentException">If partUri parameter has a fragment</exception>
            <exception cref="T:System.ArgumentException">If partUri parameter has some escaped characters that should not be escaped
            or some characters that should be escaped are not escaped.</exception>
        </member>
        <member name="F:System.IO.Packaging.PackUriHelper.UriSchemePack">
            <summary>
            pack scheme name
            </summary>
        </member>
        <member name="T:System.IO.Packaging.PackUriHelper.ValidatedPartUri">
            <summary>
            ValidatedPartUri class
            Once the partUri has been validated as per the syntax in the OPC spec
            we create a ValidatedPartUri, this way we do not have to re-validate
            this.
            This class is heavily used throughout the Packaging APIs and in order
            to reduce the parsing and number of allocations for Strings and Uris
            we cache the results after parsing.
            </summary>
        </member>
        <member name="M:System.IO.Packaging.PackUriHelper.Create(System.Uri)">
            <summary>
            This method is used to create a valid pack Uri
            </summary>
            <param name="packageUri">This is the uri that points to the entire package.
            This parameter should be an absolute Uri. This parameter cannot be null or empty
            This method will create a valid pack uri that references the entire package</param>
            <returns>A Uri with the "pack://" scheme</returns>
            <exception cref="T:System.ArgumentNullException">If packageUri parameter is null</exception>
            <exception cref="T:System.ArgumentException">If packageUri parameter is not an absolute Uri</exception>
        </member>
        <member name="M:System.IO.Packaging.PackUriHelper.Create(System.Uri,System.Uri)">
            <summary>
            This method is used to create a valid pack Uri
            </summary>
            <param name="packageUri">This is the uri that points to the entire package.
            This parameter should be an absolute Uri. This parameter cannot be null or empty </param>
            <param name="partUri">This is the uri that points to the part within the package
            This parameter should be a relative Uri.
            This parameter can be null in which case we will create a valid pack uri
            that references the entire package</param>
            <returns>A Uri with the "pack://" scheme</returns>
            <exception cref="T:System.ArgumentNullException">If packageUri parameter is null</exception>
            <exception cref="T:System.ArgumentException">If packageUri parameter is not an absolute Uri</exception>
            <exception cref="T:System.ArgumentException">If partUri parameter does not conform to the valid partUri syntax</exception>
        </member>
        <member name="M:System.IO.Packaging.PackUriHelper.Create(System.Uri,System.Uri,System.String)">
            <summary>
            This method is used to create a valid pack Uri
            </summary>
            <param name="packageUri">This is the uri that points to the entire package.
            This parameter should be an absolute Uri. This parameter cannot be null or empty </param>
            <param name="partUri">This is the uri that points to the part within the package
            This parameter should be a relative Uri.
            This parameter can be null in which case we will create a valid pack uri
            that references the entire package</param>
            <param name="fragment">Fragment for the resulting Pack URI. This parameter can be null
            The fragment string must start with a "#"</param>
            <returns>A Uri with the "pack://" scheme</returns>
            <exception cref="T:System.ArgumentNullException">If packageUri parameter is null</exception>
            <exception cref="T:System.ArgumentException">If packageUri parameter is not an absolute Uri</exception>
            <exception cref="T:System.ArgumentException">If partUri parameter does not conform to the valid partUri syntax</exception>
            <exception cref="T:System.ArgumentException">If fragment parameter is empty or does not start with a "#"</exception>
        </member>
        <member name="M:System.IO.Packaging.PackUriHelper.GetPackageUri(System.Uri)">
            <summary>
            This method parses the pack uri and returns the inner
            Uri that points to the package as a whole.
            </summary>
            <param name="packUri">Uri which has pack:// scheme</param>
            <returns>Returns the inner uri that points to the entire package</returns>
            <exception cref="T:System.ArgumentNullException">If packUri parameter is null</exception>
            <exception cref="T:System.ArgumentException">If packUri parameter is not an absolute Uri</exception>
            <exception cref="T:System.ArgumentException">If packUri parameter does not have "pack://" scheme</exception>
            <exception cref="T:System.ArgumentException">If inner packageUri extracted from the packUri has a fragment component</exception>
        </member>
        <member name="M:System.IO.Packaging.PackUriHelper.GetPartUri(System.Uri)">
            <summary>
            This method parses the pack uri and returns the absolute
            path of the URI. This corresponds to the part within the
            package. This corresponds to the absolute path component in
            the Uri. If there is no part component present, this method
            returns a null
            </summary>
            <param name="packUri">Returns a relative Uri that represents the
            part within the package. If the pack Uri points to the entire
            package then we return a null</param>
            <returns>Returns a relative URI with an absolute path that points to a part within a package</returns>
            <exception cref="T:System.ArgumentNullException">If packUri parameter is null</exception>
            <exception cref="T:System.ArgumentException">If packUri parameter is not an absolute Uri</exception>
            <exception cref="T:System.ArgumentException">If packUri parameter does not have "pack://" scheme</exception>
            <exception cref="T:System.ArgumentException">If partUri extracted from packUri does not conform to the valid partUri syntax</exception>
        </member>
        <member name="M:System.IO.Packaging.PackUriHelper.ComparePackUri(System.Uri,System.Uri)">
            <summary>
            This method compares two pack uris and returns an int to indicate the equivalence.
            </summary>
            <param name="firstPackUri">First Uri of pack:// scheme to be compared</param>
            <param name="secondPackUri">Second Uri of pack:// scheme to be compared</param>
            <returns>A 32-bit signed integer indicating the lexical relationship between the compared Uri components.
            Value - Less than zero means firstUri is less than secondUri
            Value - Equal to zero means both the Uris are equal
            Value - Greater than zero means firstUri is greater than secondUri </returns>
            <exception cref="T:System.ArgumentException">If either of the Uris are not absolute or if either of the Uris are not with pack:// scheme</exception>
            <exception cref="T:System.ArgumentException">If firstPackUri or secondPackUri parameter is not an absolute Uri</exception>
            <exception cref="T:System.ArgumentException">If firstPackUri or secondPackUri parameter does not have "pack://" scheme</exception>
        </member>
        <member name="M:System.IO.Packaging.PackUriHelper.ValidatePackageUri(System.Uri)">
            <summary>
            This method is used to validate the package uri
            </summary>
            <param name="packageUri"></param>
            <returns></returns>
        </member>
        <member name="M:System.IO.Packaging.PackUriHelper.EscapeSpecialCharacters(System.String)">
            <summary>
            Escapes -  %', '@', ',', '?' in the package URI
            This method modifies the string in a culture safe and case safe manner.
            </summary>
            <param name="path"></param>
            <returns></returns>
        </member>
        <member name="T:System.IO.Packaging.PartBasedPackageProperties">
            <summary>
             The package properties are a subset of the standard OLE property sets
             SummaryInformation and DocumentSummaryInformation, and include such properties
             as Title and Subject.
            </summary>
            <remarks>
            <para>Setting a property to null deletes this property. 'null' is never strictly speaking
            a property value, but an absence indicator.</para>
            </remarks>
        </member>
        <member name="P:System.IO.Packaging.PartBasedPackageProperties.Creator">
            <value>
            The primary creator. The identification is environment-specific and
            can consist of a name, email address, employee ID, etc. It is
            recommended that this value be only as verbose as necessary to
            identify the individual.
            </value>
        </member>
        <member name="P:System.IO.Packaging.PartBasedPackageProperties.Title">
            <value>
            The title.
            </value>
        </member>
        <member name="P:System.IO.Packaging.PartBasedPackageProperties.Subject">
            <value>
            The topic of the contents.
            </value>
        </member>
        <member name="P:System.IO.Packaging.PartBasedPackageProperties.Category">
            <value>
            The category. This value is typically used by UI applications to create navigation
            controls.
            </value>
        </member>
        <member name="P:System.IO.Packaging.PartBasedPackageProperties.Keywords">
            <value>
            A delimited set of keywords to support searching and indexing. This
            is typically a list of terms that are not available elsewhere in the
            properties.
            </value>
        </member>
        <member name="P:System.IO.Packaging.PartBasedPackageProperties.Description">
            <value>
            The description or abstract of the contents.
            </value>
        </member>
        <member name="P:System.IO.Packaging.PartBasedPackageProperties.ContentType">
            <value>
            The type of content represented, generally defined by a specific
            use and intended audience. Example values include "Whitepaper",
            "Security Bulletin", and "Exam". (This property is distinct from
            MIME content types as defined in RFC 2616.)
            </value>
        </member>
        <member name="P:System.IO.Packaging.PartBasedPackageProperties.ContentStatus">
            <value>
            The status of the content. Example values include "Draft",
            "Reviewed", and "Final".
            </value>
        </member>
        <member name="P:System.IO.Packaging.PartBasedPackageProperties.Version">
            <value>
            The version number. This value is set by the user or by the application.
            </value>
        </member>
        <member name="P:System.IO.Packaging.PartBasedPackageProperties.Revision">
            <value>
            The revision number. This value indicates the number of saves or
            revisions. The application is responsible for updating this value
            after each revision.
            </value>
        </member>
        <member name="P:System.IO.Packaging.PartBasedPackageProperties.Created">
            <value>
            The creation date and time.
            </value>
        </member>
        <member name="P:System.IO.Packaging.PartBasedPackageProperties.Modified">
            <value>
            The date and time of the last modification.
            </value>
        </member>
        <member name="P:System.IO.Packaging.PartBasedPackageProperties.LastModifiedBy">
            <value>
            The user who performed the last modification. The identification is
            environment-specific and can consist of a name, email address,
            employee ID, etc. It is recommended that this value be only as
            verbose as necessary to identify the individual.
            </value>
        </member>
        <member name="P:System.IO.Packaging.PartBasedPackageProperties.LastPrinted">
            <value>
            The date and time of the last printing.
            </value>
        </member>
        <member name="P:System.IO.Packaging.PartBasedPackageProperties.Language">
            <value>
            A language of the intellectual content of the resource
            </value>
        </member>
        <member name="P:System.IO.Packaging.PartBasedPackageProperties.Identifier">
            <value>
            A unique identifier.
            </value>
        </member>
        <member name="T:System.IO.Packaging.TargetMode">
            <summary>
             The TargetMode enumeration is used to interpret the
             "base" uri for the PackageRelationship target Uri.
             Possible values -
                         0: "Internal" - target points to a part within the package
                                         PackageRelationship target uri must be relative.
                         1: "External" - target points to an external resource. The
                                         resource can be relative to the package entity
                                         or an absolute URI
            </summary>
        </member>
        <member name="F:System.IO.Packaging.TargetMode.Internal">
            <summary>
            TargetMode is "Internal".
            PackageRelationship target points to a part within the package
            PackageRelationship target uri must be relative.
            </summary>
        </member>
        <member name="F:System.IO.Packaging.TargetMode.External">
            <summary>
            TargetMode is "External".
            PackageRelationship target points to an external resource.
            PackageRelationship target uri can be relative or absolute.
            The resource can be relative to the package entity or an absolute URI.
            </summary>
        </member>
        <member name="M:System.IO.Packaging.XmlCompatibilityReader.DeclareNamespaceCompatibility(System.String,System.String)">
            <summary>
            replaces all future references of namespace URI 'oldNamespace' with 'newNamespace'
            </summary>
            <param name="newNamespace">
            the namespace to subsume with
            </param>
            <param name="oldNamespace">
            the namespace to be subsumed
            </param>
        </member>
        <member name="M:System.IO.Packaging.XmlCompatibilityReader.Read">
            <summary>
            Reads the next node from the stream.
            </summary>
            <returns>
            true if the next node was read successfully; false if there are no more nodes to read.
            </returns>
        </member>
        <member name="M:System.IO.Packaging.XmlCompatibilityReader.ReadStartElement(System.Boolean@)">
            <summary>
            Used to handle 'start element' tags.  These are actually
            just called 'element' tags, the 'start' is just for clarity
            </summary>
            <param name="more">
            is set to true if there is the document contains more elements, false if the end of the
            document has been reached.
            </param>
            <returns>
            true if an element was read that should not be ignored
            false if the element read should be ignored or the end of document has been reached
            </returns>
        </member>
        <member name="M:System.IO.Packaging.XmlCompatibilityReader.ReadEndElement(System.Boolean@)">
            <summary>
            Used to handle any end element tag
            </summary>
            <param name="more">
            is set to true if there is the document contains more elements, false if the end of the
            document has been reached.
            </param>
            <returns>
            true if an element was read that should not be ignored
            false if the element read should be ignored or the end of document has been reached
            </returns>
        </member>
        <member name="M:System.IO.Packaging.XmlCompatibilityReader.GetAttribute(System.Int32)">
            <summary>
            Gets the value of the attribute with the specified index.
            </summary>
            <param name="i">
            The index of the attribute. The index is zero-based. (The first attribute has index 0.)
            </param>
            <returns>
            The value of the specified attribute. If the attribute is not found, a null reference is returned.
            </returns>
        </member>
        <member name="M:System.IO.Packaging.XmlCompatibilityReader.GetAttribute(System.String)">
            <summary>
            Gets the value of the attribute with the specified name.
            </summary>
            <param name="name">
            The qualified name of the attribute.
            </param>
            <returns>
            The value of the specified attribute. If the attribute is not found, a null reference is returned.
            </returns>
        </member>
        <member name="M:System.IO.Packaging.XmlCompatibilityReader.GetAttribute(System.String,System.String)">
            <summary>
            Gets the value of the attribute with the specified local name and namespace URI.
            </summary>
            <param name="localName">
            The local name of the attribute.
            </param>
            <param name="namespaceURI">
            The namespace URI of the attribute.
            </param>
            <returns>
            The value of the specified attribute. If the attribute is not found, a null reference is returned.
            </returns>
        </member>
        <member name="M:System.IO.Packaging.XmlCompatibilityReader.MoveToAttribute(System.Int32)">
            <summary>
            Gets the value of the attribute with the specified index.
            </summary>
            <param name="i">
            The index of the attribute. The index is zero-based. (The first attribute has index 0.)
            </param>
        </member>
        <member name="M:System.IO.Packaging.XmlCompatibilityReader.MoveToAttribute(System.String)">
            <summary>
            Moves to the attribute with the specified name.
            </summary>
            <param name="name">
            The qualified name of the attribute.
            </param>
            <returns>
            true if the attribute is found; otherwise, false. If false, the reader's position does not change.
            </returns>
        </member>
        <member name="M:System.IO.Packaging.XmlCompatibilityReader.MoveToAttribute(System.String,System.String)">
            <summary>
            Moves to the attribute with the specified local name and namespace URI.
            </summary>
            <param name="localName">
            The local name of the attribute.
            </param>
            <param name="namespaceURI">
            The namespace URI of the attribute.
            </param>
            <returns>
            true if the attribute is found; otherwise, false. If false, the reader's position does not change.
            </returns>
        </member>
        <member name="M:System.IO.Packaging.XmlCompatibilityReader.MoveToFirstAttribute">
            <summary>
            Moves to the first attribute.
            </summary>
            <returns>
            true if an attribute exists (the reader moves to the first attribute);
            otherwise, false (the position of the reader does not change).
            </returns>
        </member>
        <member name="M:System.IO.Packaging.XmlCompatibilityReader.MoveToNextAttribute">
            <summary>
            Moves to the next attribute.
            </summary>
            <returns>
            true if there is a next attribute; false if there are no more attributes.
            </returns>
        </member>
        <member name="M:System.IO.Packaging.XmlCompatibilityReader.LookupNamespace(System.String)">
            <summary>
            Resolves a namespace prefix in the current element's scope.
            </summary>
            <param name="prefix">
            The prefix whose namespace URI you want to resolve. To match the default namespace,
            pass an empty string. This string does not have to be atomized.
            </param>
            <returns>
            The namespace URI to which the prefix maps or a null reference if no matching prefix is found.
            </returns>
        </member>
        <member name="P:System.IO.Packaging.XmlCompatibilityReader.Value">
            <summary>
            This override is to ensure that the value
            for the xmlns attribute reflects all the
            compatibility (subsuming) rules.
            </summary>
        </member>
        <member name="P:System.IO.Packaging.XmlCompatibilityReader.NamespaceURI">
            <summary>
            Gets the namespace URI (as defined in the W3C Namespace specification) of the node
            on which the reader is positioned.
            </summary>
        </member>
        <member name="P:System.IO.Packaging.XmlCompatibilityReader.Depth">
            <summary>
            Gets the depth of the current node in the XML document.
            </summary>
        </member>
        <member name="P:System.IO.Packaging.XmlCompatibilityReader.HasAttributes">
            <summary>
            Gets a value indicating whether the current node has any attributes
            </summary>
        </member>
        <member name="P:System.IO.Packaging.XmlCompatibilityReader.AttributeCount">
            <summary>
            Gets the number of attributes on the current node.
            </summary>
        </member>
        <member name="M:System.IO.Packaging.XmlCompatibilityReader.GetMappedNamespace(System.String)">
            <summary>
            Retrieves the correctly mapped namespace from the namespace provided
            </summary>
            <param name="namespaceName">
            The name of the namespace to retrieve the mapping of
            </param>
            <returns>
            The name of the mapped namespace.
            </returns>
        </member>
        <member name="M:System.IO.Packaging.XmlCompatibilityReader.MapNewNamespace(System.String)">
            <summary>
            Adds the namespace to the namespace map.  The default is to map the namespace to itself.
            The namespace is mapped to the value returned by the callback, if a callback exists and the
            callback returns a subsuming namespace.
            </summary>
            <param name="namespaceName">
            The name of the namespace to be mapped.
            </param>
            <returns>
            The name of the mapped namespace.
            </returns>
        </member>
        <member name="M:System.IO.Packaging.XmlCompatibilityReader.IsSubsumingNamespace(System.String)">
            <summary>
            Used to determine whether a given namespace subsumes another namespace
            </summary>
            <param name="namespaceName">
            The name of the namespace to be checked.
            </param>
            <returns>
            true if the namespace subsumes another namespace; false otherwise
            </returns>
        </member>
        <member name="M:System.IO.Packaging.XmlCompatibilityReader.AddSubsumingNamespace(System.String)">
            <summary>
            Used to specify that a namespace subsumes another namespace
            </summary>
            <param name="namespaceName">
            The name of the namespace to be added.
            </param>
        </member>
        <member name="M:System.IO.Packaging.XmlCompatibilityReader.IsNamespaceKnown(System.String)">
            <summary>
            Used to determine whether a given namespace is known/supported
            </summary>
            <param name="namespaceName">
            The name of the namespace to be checked.
            </param>
            <returns>
            true if the namespace is known/supported; false otherwise
            </returns>
        </member>
        <member name="M:System.IO.Packaging.XmlCompatibilityReader.AddKnownNamespace(System.String)">
            <summary>
            Used to specify that a namespace is known or supported
            </summary>
            <param name="namespaceName">
            The name of the namespace to be added.
            </param>
        </member>
        <member name="M:System.IO.Packaging.XmlCompatibilityReader.ShouldIgnoreNamespace(System.String)">
            <summary>
            Used to determine whether a given namespace should be ignored.  A namespace should be ignored if:
            EITHER
            a) the namespace is not known/supported and has been marked Ignorable
            OR
            b) the namespace is the markup-compatibility namespace
            </summary>
            <param name="namespaceName">
            The name of the prefix to be checked.
            </param>
            <returns>
            true if the namespace should be ignored; false otherwise
            </returns>
        </member>
        <member name="M:System.IO.Packaging.XmlCompatibilityReader.ParseContentToNamespaceElementPair(System.String,System.String)">
            <summary>
            breaks up a space-delineated string into namespace/element pairs
            </summary>
            <param name="content">
            the string to be parsed
            </param>
            <param name="callerContext">
            The calling element, used in case of an error
            </param>
            <returns>
            the list of namespace/element pairs
            </returns>
        </member>
        <member name="M:System.IO.Packaging.XmlCompatibilityReader.PrefixesToNamespaces(System.String)">
            <summary>
            converts a string of space-delineated prefixes into a list of namespaces
            </summary>
            <param name="prefixes">
            the string to be parsed
            </param>
            <returns>
            the list of namespace/element pairs
            </returns>
        </member>
        <member name="M:System.IO.Packaging.XmlCompatibilityReader.SkipToKnownAttribute">
            <summary>
            advances the reader to the next known namespace/attribute pair
            </summary>
            <returns>
            true if a known namespace/attribute pair was found
            </returns>
        </member>
        <member name="M:System.IO.Packaging.XmlCompatibilityReader.ScanForCompatibility(System.Int32)">
             <summary>
             Scans the current element for compatibility attributes.  Pushes a new
             scope onto the stack under the following conditions:
             1) Ignorable or MustUnderstand attribute read
             2) current element has not previously declared an Ignorable or
                MustUnderstand attribute
            
             However, if a last condition is not fulfilled, then the scope is popped off
             before the function returns
             3) current element is not empty
            
             stores in _ignoredAttributeCount the number of attributes on the current element
             that should be ignored, for the sake of improving perf in attribute-related
             methods/properties
             </summary>
             <param name="elementDepth">
             the depth of the Reader at the element currently being processed
             </param>
        </member>
        <member name="M:System.IO.Packaging.XmlCompatibilityReader.ScanForEndCompatibility(System.Int32)">
            <summary>
            pops a scope if the end of a compatibility region.
            </summary>
            <param name="elementDepth">
            the depth of the Reader at the element currently being processed
            </param>
        </member>
        <member name="M:System.IO.Packaging.XmlCompatibilityReader.PushScope(System.Int32)">
            <summary>
            pushes a new scope onto the stack with a depth passed as an arg.
            PushScope does not push a scope if the top scope on the stack is not a lower depth.
            </summary>
            <param name="elementDepth">
            the depth of the Reader at the element currently being processed
            </param>
        </member>
        <member name="M:System.IO.Packaging.XmlCompatibilityReader.PopScope">
            <summary>
            pops a scope off the top of the stack.
            PopScope *always* pops, it does not check the depth before doing so
            </summary>
        </member>
        <member name="M:System.IO.Packaging.XmlCompatibilityReader.HandleAlternateContent(System.Int32,System.Boolean@)">
             <summary>
             handles mc:AlternateContent element
            
             a good way to think of AlternateContent blocks is as a switch/case
             statement.  The AlternateContent tag is like switch, Choice is like
             case, and Fallback is like default.
             </summary>
             <param name="elementDepth">
             the depth of the Reader at the element currently being processed
             </param>
             <param name="more">
             returns whether the Reader has more to be read
             </param>
        </member>
        <member name="M:System.IO.Packaging.XmlCompatibilityReader.HandleChoice(System.Int32,System.Boolean@)">
             <summary>
             handles mc:Choice element
            
             a good way to think of AlternateContent blocks is as a switch/case
             statement.  The AlternateContent tag is like switch, Choice is like
             case, and Fallback is like default.
             </summary>
             <param name="elementDepth">
             the depth of the Reader at the element currently being processed
             </param>
             <param name="more">
             returns whether the Reader has more to be read
             </param>
        </member>
        <member name="M:System.IO.Packaging.XmlCompatibilityReader.HandleFallback(System.Int32,System.Boolean@)">
             <summary>
             handles mc:Fallback element
            
             a good way to think of AlternateContent blocks is as a switch/case
             statement.  The AlternateContent tag is like switch, Choice is like
             case, and Fallback is like default.
             </summary>
             <param name="elementDepth">
             the depth of the Reader at the element currently being processed
             </param>
             <param name="more">
             returns whether the Reader has more to be read
             </param>
        </member>
        <member name="M:System.IO.Packaging.XmlCompatibilityReader.HandleIgnorable(System.Int32)">
             <summary>
             handles mc:Ignorable="foo" attribute
            
             Ignorable is used to indicate that the namespace the prefix is mapped to can
             be ignored, i.e. when the namespace/element or namespace/attribute occurs it
             is not returned by the reader.
             </summary>
        </member>
        <member name="M:System.IO.Packaging.XmlCompatibilityReader.HandleMustUnderstand(System.Int32)">
             <summary>
             handles mc:MustUnderstand="foo" attribute
            
             MustUnderstand is used to indicate that the namespace the prefix is mapped to
             cannot be handled, and if it is not understood an exception is thrown
             </summary>
        </member>
        <member name="M:System.IO.Packaging.XmlCompatibilityReader.HandleProcessContent(System.Int32)">
             <summary>
             handles mc:ProcessContent="foo:bar" attribute
            
             ProcessContent is used to indicate that an ignorable namespace has some
             elements that should be skipped, but contain child elements that should be processed.
            
             The wildcard token ("foo:*") indicates that the children of any element in that
             namespace should be processed.
             </summary>
        </member>
        <member name="M:System.IO.Packaging.XmlCompatibilityReader.HandlePreserveElements(System.Int32)">
             <summary>
             handles mc:PreserveElements="foo:bar" attribute
            
             functionality is supported, but not implemented
             </summary>
        </member>
        <member name="M:System.IO.Packaging.XmlCompatibilityReader.HandlePreserveAttributes(System.Int32)">
             <summary>
             handles mc:PreserveAttributes="foo:bar" attribute
            
             functionality is supported, but not implemented
             </summary>
        </member>
        <member name="M:System.IO.Packaging.XmlCompatibilityReader.Error(System.String,System.Object[])">
            <summary>
            helper method to generate an exception
            </summary>
        </member>
        <member name="T:System.IO.Packaging.XmlCompatibilityReader.CompatibilityScope">
            <summary>
            CompatibilityScopes are used to handle markup-compatibility elements and attributes.
            Each scope stores the "previous" or parent scope, its depth, and an associated XmlCompatibilityReader.
            At a particular Reader depth, only one scope should be pushed.
            </summary>
        </member>
        <member name="T:System.IO.Packaging.ZipPackage">
            <summary>
            ZipPackage is a specific implementation for the abstract Package
            class, corresponding to the Zip file format.
            This is a part of the Packaging Layer APIs.
            </summary>
        </member>
        <member name="M:System.IO.Packaging.ZipPackage.CreatePartCore(System.Uri,System.String,System.IO.Packaging.CompressionOption)">
            <summary>
            This method is for custom implementation for the underlying file format
            Adds a new item to the zip archive corresponding to the PackagePart in the package.
            </summary>
            <param name="partUri">PartName</param>
            <param name="contentType">Content type of the part</param>
            <param name="compressionOption">Compression option for this part</param>
            <returns></returns>
            <exception cref="T:System.ArgumentNullException">If partUri parameter is null</exception>
            <exception cref="T:System.ArgumentNullException">If contentType parameter is null</exception>
            <exception cref="T:System.ArgumentException">If partUri parameter does not conform to the valid partUri syntax</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">If CompressionOption enumeration [compressionOption] does not have one of the valid values</exception>
        </member>
        <member name="M:System.IO.Packaging.ZipPackage.GetPartCore(System.Uri)">
            <summary>
            This method is for custom implementation specific to the file format.
            Returns the part after reading the actual physical bits. The method
            returns a null to indicate that the part corresponding to the specified
            Uri was not found in the container.
            This method does not throw an exception if a part does not exist.
            </summary>
            <param name="partUri"></param>
            <returns></returns>
        </member>
        <member name="M:System.IO.Packaging.ZipPackage.DeletePartCore(System.Uri)">
            <summary>
            This method is for custom implementation specific to the file format.
            Deletes the part corresponding to the uri specified. Deleting a part that does not
            exists is not an error and so we do not throw an exception in that case.
            </summary>
            <param name="partUri"></param>
            <exception cref="T:System.ArgumentNullException">If partUri parameter is null</exception>
            <exception cref="T:System.ArgumentException">If partUri parameter does not conform to the valid partUri syntax</exception>
        </member>
        <member name="M:System.IO.Packaging.ZipPackage.GetPartsCore">
            <summary>
            This method is for custom implementation specific to the file format.
            This is the method that knows how to get the actual parts from the underlying
            zip archive.
            </summary>
            <remarks>
            <para>
            Some or all of the parts may be interleaved. The Part object for an interleaved part encapsulates
            the Uri of the proper part name and the ZipFileInfo of the initial piece.
            This function does not go through the extra work of checking piece naming validity
            throughout the package.
            </para>
            <para>
            This means that interleaved parts without an initial piece will be silently ignored.
            Other naming anomalies get caught at the Stream level when an I/O operation involves
            an anomalous or missing piece.
            </para>
            <para>
            This function reads directly from the underlying IO layer and is supposed to be called
            just once in the lifetime of a package (at init time).
            </para>
            </remarks>
            <returns>An array of ZipPackagePart.</returns>
        </member>
        <member name="M:System.IO.Packaging.ZipPackage.FlushCore">
            <summary>
            This method is for custom implementation corresponding to the underlying zip file format.
            </summary>
        </member>
        <member name="M:System.IO.Packaging.ZipPackage.Dispose(System.Boolean)">
            <summary>
            Closes the underlying ZipArchive object for this container
            </summary>
            <param name="disposing">True if called during Dispose, false if called during Finalize</param>
        </member>
        <member name="M:System.IO.Packaging.ZipPackage.#ctor(System.String,System.IO.FileMode,System.IO.FileAccess,System.IO.FileShare)">
            <summary>
            Internal constructor that is called by the OpenOnFile static method.
            </summary>
            <param name="path">File path to the container.</param>
            <param name="packageFileMode">Container is opened in the specified mode if possible</param>
            <param name="packageFileAccess">Container is opened with the specified access if possible</param>
            <param name="share">Container is opened with the specified share if possible</param>
        </member>
        <member name="M:System.IO.Packaging.ZipPackage.#ctor(System.IO.Stream,System.IO.FileMode,System.IO.FileAccess)">
            <summary>
            Internal constructor that is called by the Open(Stream) static methods.
            </summary>
            <param name="s"></param>
            <param name="packageFileMode"></param>
            <param name="packageFileAccess"></param>
        </member>
        <member name="T:System.IO.Packaging.ZipPackage.ExtensionEqualityComparer">
            <summary>
            ExtensionComparer
            The Extensions are stored in the Default Dictionary in their original form,
            however they are compared in a normalized manner.
            Equivalence for extensions in the content type stream, should follow
            the same rules as extensions of partnames. Also, by the time this code is invoked,
            we have already validated, that the extension is in the correct format as per the
            part name rules.So we are simplifying the logic here to just convert the extensions
            to Upper invariant form and then compare them.
            </summary>
        </member>
        <member name="T:System.IO.Packaging.ZipPackage.ContentTypeHelper">
            <summary>
            This is a helper class that maintains the Content Types File related to
            this ZipPackage.
            </summary>
        </member>
        <member name="M:System.IO.Packaging.ZipPackage.ContentTypeHelper.#ctor(System.IO.Compression.ZipArchive,System.IO.FileMode,System.IO.FileAccess,System.IO.Packaging.ZipStreamManager)">
            <summary>
            Initialize the object without uploading any information from the package.
            Complete initialization in read mode also involves calling ParseContentTypesFile
            to deserialize content type information.
            </summary>
        </member>
        <member name="M:System.IO.Packaging.ZipPackage.ContentTypeHelper.OpenContentTypeStream(System.Collections.ObjectModel.ReadOnlyCollection{System.IO.Compression.ZipArchiveEntry})">
            <summary>
            Find the content type stream, allowing for interleaving. Naming collisions
            (as between an atomic and an interleaved part) will result in an exception being thrown.
            Return null if no content type stream has been found.
            </summary>
            <remarks>
            The input array is lexicographically sorted
            </remarks>
        </member>
        <member name="T:System.IO.Packaging.ZipPackagePart">
            <summary>
            This class represents a Part within a Zip container.
            This is a part of the Packaging Layer APIs.
            This implementation is specific to the Zip file format.
            </summary>
        </member>
        <member name="M:System.IO.Packaging.ZipPackagePart.GetStreamCore(System.IO.FileMode,System.IO.FileAccess)">
            <summary>
            Custom Implementation for the GetStream Method
            </summary>
            <param name="streamFileMode">Mode in which the stream should be opened</param>
            <param name="streamFileAccess">Access with which the stream should be opened</param>
            <returns>Stream Corresponding to this part</returns>
        </member>
        <member name="M:System.IO.Packaging.ZipPackagePart.#ctor(System.IO.Packaging.ZipPackage,System.IO.Compression.ZipArchive,System.IO.Compression.ZipArchiveEntry,System.IO.Packaging.ZipStreamManager,System.IO.Packaging.PackUriHelper.ValidatedPartUri,System.String,System.IO.Packaging.CompressionOption)">
            <summary>
            Constructs a ZipPackagePart for an atomic (i.e. non-interleaved) part.
            This is called from the ZipPackage class as a result of GetPartCore,
            GetPartsCore or CreatePartCore methods
            </summary>
            <param name="zipPackage"></param>
            <param name="zipArchive"></param>
            <param name="zipArchiveEntry"></param>
            <param name="zipStreamManager"></param>
            <param name="partUri"></param>
            <param name="compressionOption"></param>
            <param name="contentType"></param>
        </member>
        <member name="P:System.IO.Packaging.ZipPackagePart.ZipArchiveEntry">
            <summary>
            Obtain the ZipFileInfo descriptor of an atomic part.
            </summary>
        </member>
        <member name="T:System.IO.FileFormatException">
            <summary>
            The FileFormatException class is thrown when an input file or a data stream that is supposed to conform
            to a certain file format specification is malformed.
            </summary>
        </member>
        <member name="M:System.IO.FileFormatException.#ctor">
            <summary>
            Creates a new instance of FileFormatException class.
            This constructor initializes the Message property of the new instance to a system-supplied message that describes the error,
            such as "An input file or a data stream does not conform to the expected file format specification."
            This message takes into account the current system culture.
            </summary>
        </member>
        <member name="M:System.IO.FileFormatException.#ctor(System.String)">
            <summary>
            Creates a new instance of FileFormatException class.
            This constructor initializes the Message property of the new instance with a specified error message.
            </summary>
            <param name="message">The message that describes the error.</param>
        </member>
        <member name="M:System.IO.FileFormatException.#ctor(System.String,System.Exception)">
            <summary>
            Creates a new instance of FileFormatException class.
            This constructor initializes the Message property of the new instance with a specified error message.
            The InnerException property is initialized using the innerException parameter.
            </summary>
            <param name="message">The error message that explains the reason for the exception.</param>
            <param name="innerException">The exception that is the cause of the current exception.</param>
        </member>
        <member name="M:System.IO.FileFormatException.#ctor(System.Uri)">
            <summary>
            Creates a new instance of FileFormatException class.
            This constructor initializes the Message property of the new instance to a system-supplied message that describes the error and includes the file name,
            such as "The file 'sourceUri' does not conform to the expected file format specification."
            This message takes into account the current system culture.
            The SourceUri property is initialized using the sourceUri parameter.
            </summary>
            <param name="sourceUri">The Uri of a file that caused this error.</param>
        </member>
        <member name="M:System.IO.FileFormatException.#ctor(System.Uri,System.String)">
            <summary>
            Creates a new instance of FileFormatException class.
            This constructor initializes the Message property of the new instance using the message parameter.
            The content of message is intended to be understood by humans.
            The caller of this constructor is required to ensure that this string has been localized for the current system culture.
            The SourceUri property is initialized using the sourceUri parameter.
            </summary>
            <param name="sourceUri">The Uri of a file that caused this error.</param>
            <param name="message">The message that describes the error.</param>
        </member>
        <member name="M:System.IO.FileFormatException.#ctor(System.Uri,System.Exception)">
            <summary>
            Creates a new instance of FileFormatException class.
            This constructor initializes the Message property of the new instance to a system-supplied message that describes the error and includes the file name,
            such as "The file 'sourceUri' does not conform to the expected file format specification."
            This message takes into account the current system culture.
            The SourceUri property is initialized using the sourceUri parameter.
            The InnerException property is initialized using the innerException parameter.
            </summary>
            <param name="sourceUri">The Uri of a file that caused this error.</param>
            <param name="innerException">The exception that is the cause of the current exception.</param>
        </member>
        <member name="M:System.IO.FileFormatException.#ctor(System.Uri,System.String,System.Exception)">
            <summary>
            Creates a new instance of FileFormatException class.
            This constructor initializes the Message property of the new instance using the message parameter.
            The content of message is intended to be understood by humans.
            The caller of this constructor is required to ensure that this string has been localized for the current system culture.
            The SourceUri property is initialized using the sourceUri parameter.
            The InnerException property is initialized using the innerException parameter.
            </summary>
            <param name="sourceUri">The Uri of a file that caused this error.</param>
            <param name="message">The message that describes the error.</param>
            <param name="innerException">The exception that is the cause of the current exception.</param>
        </member>
        <member name="M:System.IO.FileFormatException.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Sets the SerializationInfo object with the file name and additional exception information.
            </summary>
            <param name="info">The object that holds the serialized object data.</param>
            <param name="context">The contextual information about the source or destination.</param>
        </member>
        <member name="P:System.IO.FileFormatException.SourceUri">
            <summary>
            Returns the name of a file that caused this exception. This property may be equal to an empty string
            if obtaining the file path that caused the error was not possible.
            </summary>
            <value>The file name.</value>
            <SecurityNote>
                Critical : Calls critical Demand for path discovery
                Safe     : Path which could be leaked by an exception is already known to caller since it is supplied by the caller
            </SecurityNote>
        </member>
        <member name="P:System.SR.BadPackageFormat">
            <summary>Format error in package.</summary>
        </member>
        <member name="P:System.SR.CannotModifyReadOnlyContainer">
            <summary>Cannot modify readonly container</summary>
        </member>
        <member name="P:System.SR.CannotRetrievePartsOfWriteOnlyContainer">
            <summary>Cannot retrieve parts of writeonly container</summary>
        </member>
        <member name="P:System.SR.ContainerAndPartModeIncompatible">
            <summary>FileMode/FileAccess for Part.GetStream is not compatible with FileMode/FileAccess used to open the Package.</summary>
        </member>
        <member name="P:System.SR.ContentTypeCannotHaveLeadingTrailingLWS">
            <summary>ContentType string cannot have leading/trailing Linear White Spaces [LWS - RFC 2616].</summary>
        </member>
        <member name="P:System.SR.CorePropertiesElementExpected">
            <summary>Unrecognized root element in Core Properties part.</summary>
        </member>
        <member name="P:System.SR.CreateNewNotSupported">
            <summary>CreateNew not supported</summary>
        </member>
        <member name="P:System.SR.DanglingMetadataRelationship">
            <summary>The target of the Core Properties relationship does not reference an existing part.</summary>
        </member>
        <member name="P:System.SR.DefaultTagDoesNotMatchSchema">
            <summary>Default tag is not valid per the schema. Verify that attributes are correct.</summary>
        </member>
        <member name="P:System.SR.DuplicateCorePropertyName">
            <summary>More than one '{0}' property found.</summary>
        </member>
        <member name="P:System.SR.ElementIsNotEmptyElement">
            <summary>'{0}' element must be empty.</summary>
        </member>
        <member name="P:System.SR.EncodingNotSupported">
            <summary>Encoding format is not supported. Only UTF-8 and UTF-16 are supported.</summary>
        </member>
        <member name="P:System.SR.ExpectedRelationshipsElementTag">
            <summary>Relationships tag expected at root level.</summary>
        </member>
        <member name="P:System.SR.ExpectingParameterValuePairs">
            <summary>';' must be followed by parameter=value pair.</summary>
        </member>
        <member name="P:System.SR.ExpectingSemicolon">
            <summary>Semicolon separator is required between two valid parameter=value pairs.</summary>
        </member>
        <member name="P:System.SR.FileFormatException">
            <summary>Invalid file format.</summary>
        </member>
        <member name="P:System.SR.FileFormatExceptionWithFileName">
            <summary>File '{0}' has an invalid file format.</summary>
        </member>
        <member name="P:System.SR.GetContentTypeCoreNotImplemented">
            <summary>PackagePart subclass must implement GetContentTypeCore method if passing a null value for the content type when PackagePart object is constructed.</summary>
        </member>
        <member name="P:System.SR.InvalidLinearWhiteSpaceCharacter">
            <summary>A Linear White Space character is not valid.</summary>
        </member>
        <member name="P:System.SR.InvalidParameterValue">
            <summary>Parameter value must be a valid token or a quoted string as per RFC 2616.</summary>
        </member>
        <member name="P:System.SR.InvalidParameterValuePair">
            <summary>Parameter and value pair is not valid. Expected form is parameter=value.</summary>
        </member>
        <member name="P:System.SR.InvalidPartUri">
            <summary>Part URI is not valid per rules defined in the Open Packaging Conventions specification.</summary>
        </member>
        <member name="P:System.SR.InvalidPropertyNameInCorePropertiesPart">
            <summary>'{0}' property name is not valid in Core Properties part.</summary>
        </member>
        <member name="P:System.SR.InvalidRelationshipType">
            <summary>Relationship Type cannot contain only spaces or be empty.</summary>
        </member>
        <member name="P:System.SR.InvalidToken_ContentType">
            <summary>A token is not valid. Refer to RFC 2616 for correct grammar of content types.</summary>
        </member>
        <member name="P:System.SR.InvalidTypeSubType">
            <summary>ContentType string is not valid. Expected format is type/subtype.</summary>
        </member>
        <member name="P:System.SR.InvalidValueForTheAttribute">
            <summary>'{0}' attribute value is not valid.</summary>
        </member>
        <member name="P:System.SR.InvalidXmlBaseAttributePresent">
            <summary>Relationships XML elements cannot specify attribute '{0}'.</summary>
        </member>
        <member name="P:System.SR.MoreThanOneMetadataRelationships">
            <summary>Package has more than one Core Properties relationship.</summary>
        </member>
        <member name="P:System.SR.NoExternalTargetForMetadataRelationship">
            <summary>TargetMode for a Core Properties relationship must be 'Internal'.</summary>
        </member>
        <member name="P:System.SR.NoStructuredContentInsideProperties">
            <summary>Core Properties part: core property elements can contain only text data.</summary>
        </member>
        <member name="P:System.SR.NotAUniqueRelationshipId">
            <summary>'{0}' ID conflicts with the ID of an existing relationship for the specified source.</summary>
        </member>
        <member name="P:System.SR.NotAValidRelationshipPartUri">
            <summary>PackageRelationship part URI syntax is not valid.</summary>
        </member>
        <member name="P:System.SR.NotAValidXmlIdString">
            <summary>'{0}' ID is not a valid XSD ID.</summary>
        </member>
        <member name="P:System.SR.NullContentTypeProvided">
            <summary>GetContentTypeCore method cannot return null for the content type stream.</summary>
        </member>
        <member name="P:System.SR.NullStreamReturned">
            <summary>Returned stream for the part is null.</summary>
        </member>
        <member name="P:System.SR.ObjectDisposed">
            <summary>Package object was closed and disposed, so cannot carry out operations on this object or any stream opened on a part of this package.</summary>
        </member>
        <member name="P:System.SR.OverrideTagDoesNotMatchSchema">
            <summary>Override tag is not valid per the schema. Verify that attributes are correct.</summary>
        </member>
        <member name="P:System.SR.PackagePartDeleted">
            <summary>Part was deleted.</summary>
        </member>
        <member name="P:System.SR.PackagePartRelationshipDoesNotExist">
            <summary>PackageRelationship with specified ID does not exist for the source part.</summary>
        </member>
        <member name="P:System.SR.PackageRelationshipDoesNotExist">
            <summary>PackageRelationship with specified ID does not exist at the Package level.</summary>
        </member>
        <member name="P:System.SR.ParentContainerClosed">
            <summary>Cannot access part because parent package was closed.</summary>
        </member>
        <member name="P:System.SR.PartAlreadyExists">
            <summary>Cannot add part for the specified URI because it is already in the package.</summary>
        </member>
        <member name="P:System.SR.PartDoesNotExist">
            <summary>Specified part does not exist in the package.</summary>
        </member>
        <member name="P:System.SR.PartNamePrefixExists">
            <summary>Cannot add part to the package. Part names cannot be derived from another part name by appending segments to it.</summary>
        </member>
        <member name="P:System.SR.PartUriCannotHaveAFragment">
            <summary>Part URI cannot contain a Fragment component.</summary>
        </member>
        <member name="P:System.SR.PartUriIsEmpty">
            <summary>Part URI is empty.</summary>
        </member>
        <member name="P:System.SR.PartUriShouldNotEndWithForwardSlash">
            <summary>Part URI cannot end with a forward slash.</summary>
        </member>
        <member name="P:System.SR.PartUriShouldNotStartWithTwoForwardSlashes">
            <summary>Part URI cannot start with two forward slashes.</summary>
        </member>
        <member name="P:System.SR.PartUriShouldStartWithForwardSlash">
            <summary>Part URI must start with a forward slash.</summary>
        </member>
        <member name="P:System.SR.PropertyStartTagExpected">
            <summary>Core Properties part: A property start-tag was expected.</summary>
        </member>
        <member name="P:System.SR.PropertyWrongNumbOfAttribsDefinedOn">
            <summary>Unexpected number of attributes is found on '{0}'.</summary>
        </member>
        <member name="P:System.SR.RelationshipPartIncorrectContentType">
            <summary>Incorrect content type for PackageRelationship part.</summary>
        </member>
        <member name="P:System.SR.RelationshipPartsCannotHaveRelationships">
            <summary>PackageRelationship parts cannot have relationships to other parts.</summary>
        </member>
        <member name="P:System.SR.RelationshipPartUriExpected">
            <summary>PackageRelationship part URI is expected.</summary>
        </member>
        <member name="P:System.SR.RelationshipPartUriNotExpected">
            <summary>PackageRelationship part URI is not expected.</summary>
        </member>
        <member name="P:System.SR.RelationshipsTagHasExtraAttributes">
            <summary>Relationships tag has extra attributes.</summary>
        </member>
        <member name="P:System.SR.RelationshipTagDoesntMatchSchema">
            <summary>Relationship tag contains incorrect attribute.</summary>
        </member>
        <member name="P:System.SR.RelationshipTargetMustBeRelative">
            <summary>PackageRelationship target must be relative URI if TargetMode is Internal.</summary>
        </member>
        <member name="P:System.SR.RelationshipToRelationshipIllegal">
            <summary>PackageRelationship cannot target another PackageRelationship.</summary>
        </member>
        <member name="P:System.SR.RequiredAttributeEmpty">
            <summary>'{0}' tag requires a nonempty '{1}' attribute.</summary>
        </member>
        <member name="P:System.SR.RequiredAttributeMissing">
            <summary>'{0}' tag requires attribute '{1}'.</summary>
        </member>
        <member name="P:System.SR.RequiredRelationshipAttributeMissing">
            <summary>Relationship tag requires attribute '{0}'.</summary>
        </member>
        <member name="P:System.SR.StreamObjectDisposed">
            <summary>Cannot access Stream object because it was closed or disposed.</summary>
        </member>
        <member name="P:System.SR.TruncateNotSupported">
            <summary>Truncate not supported</summary>
        </member>
        <member name="P:System.SR.TypesElementExpected">
            <summary>Required Types tag not found.</summary>
        </member>
        <member name="P:System.SR.TypesTagHasExtraAttributes">
            <summary>Types tag has attributes not valid per the schema.</summary>
        </member>
        <member name="P:System.SR.TypesXmlDoesNotMatchSchema">
            <summary>Content Types XML does not match schema.</summary>
        </member>
        <member name="P:System.SR.UnknownDCDateTimeXsiType">
            <summary>Unknown xsi:type for DateTime on '{0}'.</summary>
        </member>
        <member name="P:System.SR.UnknownNamespaceInCorePropertiesPart">
            <summary>Unrecognized namespace in Core Properties part.</summary>
        </member>
        <member name="P:System.SR.UnknownTagEncountered">
            <summary>Unrecognized tag found in Relationships XML.</summary>
        </member>
        <member name="P:System.SR.UnsupportedCombinationOfModeAccess">
            <summary>Cannot get stream with FileMode.Create, FileMode.CreateNew, FileMode.Truncate, FileMode.Append when access is FileAccess.Read.</summary>
        </member>
        <member name="P:System.SR.URIShouldNotBeAbsolute">
            <summary>Cannot be an absolute URI.</summary>
        </member>
        <member name="P:System.SR.WrongContentTypeForPropertyPart">
            <summary>The Core Properties relationship references a part that has an incorrect content type.</summary>
        </member>
        <member name="P:System.SR.XCRChoiceAfterFallback">
            <summary>Choice cannot follow a Fallback.</summary>
        </member>
        <member name="P:System.SR.XCRChoiceNotFound">
            <summary>AlternateContent must contain one or more Choice elements.</summary>
        </member>
        <member name="P:System.SR.XCRChoiceOnlyInAC">
            <summary>Choice is valid only in AlternateContent.</summary>
        </member>
        <member name="P:System.SR.XCRCompatCycle">
            <summary>There is a cycle of XML compatibility definitions, such that namespace '{0}' overrides itself. This could be due to inconsistent XmlnsCompatibilityAttributes in different assemblies. Please change the definitions to eliminate this cycle.</summary>
        </member>
        <member name="P:System.SR.XCRDuplicatePreserve">
            <summary>Duplicate Preserve declaration for element {1} in namespace '{0}'.</summary>
        </member>
        <member name="P:System.SR.XCRDuplicateProcessContent">
            <summary>Duplicate ProcessContent declaration for element '{1}' in namespace '{0}'.</summary>
        </member>
        <member name="P:System.SR.XCRDuplicateWildcardPreserve">
            <summary>Duplicate wildcard Preserve declaration for namespace '{0}'.</summary>
        </member>
        <member name="P:System.SR.XCRDuplicateWildcardProcessContent">
            <summary>Duplicate wildcard ProcessContent declaration for namespace '{0}'.</summary>
        </member>
        <member name="P:System.SR.XCRFallbackOnlyInAC">
            <summary>Fallback is valid only in AlternateContent.</summary>
        </member>
        <member name="P:System.SR.XCRInvalidACChild">
            <summary>'{0}' element is not a valid child of AlternateContent. Only Choice and Fallback elements are valid children of an AlternateContent element.</summary>
        </member>
        <member name="P:System.SR.XCRInvalidAttribInElement">
            <summary>'{0}' attribute is not valid for '{1}' element.</summary>
        </member>
        <member name="P:System.SR.XCRInvalidFormat">
            <summary>'{0}' format is not valid.</summary>
        </member>
        <member name="P:System.SR.XCRInvalidPreserve">
            <summary>Cannot have both a specific and a wildcard Preserve declaration for namespace '{0}'.</summary>
        </member>
        <member name="P:System.SR.XCRInvalidProcessContent">
            <summary>Cannot have both a specific and a wildcard ProcessContent declaration for namespace '{0}'.</summary>
        </member>
        <member name="P:System.SR.XCRInvalidRequiresAttribute">
            <summary>Requires attribute must contain a valid namespace prefix.</summary>
        </member>
        <member name="P:System.SR.XCRInvalidXMLName">
            <summary>'{0}' attribute value is not a valid XML name.</summary>
        </member>
        <member name="P:System.SR.XCRMultipleFallbackFound">
            <summary>AlternateContent must contain only one Fallback element.</summary>
        </member>
        <member name="P:System.SR.XCRMustUnderstandFailed">
            <summary>MustUnderstand condition failed on namespace '{0}'</summary>
        </member>
        <member name="P:System.SR.XCRNSPreserveNotIgnorable">
            <summary>'{0}' namespace cannot preserve items; it must be declared Ignorable first.</summary>
        </member>
        <member name="P:System.SR.XCRNSProcessContentNotIgnorable">
            <summary>'{0}' namespace cannot process content; it must be declared Ignorable first.</summary>
        </member>
        <member name="P:System.SR.XCRRequiresAttribNotFound">
            <summary>Choice must contain Requires attribute.</summary>
        </member>
        <member name="P:System.SR.XCRUndefinedPrefix">
            <summary>'{0}' prefix is not defined.</summary>
        </member>
        <member name="P:System.SR.XCRUnknownCompatAttrib">
            <summary>Unrecognized compatibility attribute '{0}'.</summary>
        </member>
        <member name="P:System.SR.XCRUnknownCompatElement">
            <summary>Unrecognized Compatibility element '{0}'.</summary>
        </member>
        <member name="P:System.SR.XsdDateTimeExpected">
            <summary>Core Properties part: Text data of XSD type 'DateTime' was expected.</summary>
        </member>
        <member name="P:System.SR.CreateNewOnNonEmptyStream">
            <summary>CreateNew is not a valid FileMode for a non-empty stream.</summary>
        </member>
        <member name="P:System.SR.ZipZeroSizeFileIsNotValidArchive">
            <summary>Archive file cannot be size 0.</summary>
        </member>
        <member name="P:System.SR.InnerPackageUriHasFragment">
            <summary>Package URI obtained from the pack URI cannot contain a Fragment.</summary>
        </member>
        <member name="P:System.SR.FragmentMustStartWithHash">
            <summary>The '{0}' parameter must start with '#'.</summary>
        </member>
        <member name="P:System.SR.UriShouldBePackScheme">
            <summary>URI must contain pack:// scheme.</summary>
        </member>
        <member name="P:System.SR.UriShouldBeAbsolute">
            <summary>Must have absolute URI.</summary>
        </member>
        <member name="P:System.SR.FileContainsCorruptedData">
            <summary>File contains corrupted data.</summary>
        </member>
    </members>
</doc>
