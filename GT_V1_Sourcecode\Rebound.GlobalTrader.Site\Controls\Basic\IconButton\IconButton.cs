using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using System.Collections;

namespace Rebound.GlobalTrader.Site.Controls {
	[DefaultProperty("")]
	[ToolboxData("<{0}:IconButton runat=server></{0}:IconButton>")]
	public class IconButton : WebControl, INamingContainer {

		#region Locals

		private LinkButton _lnkButton;
		private HyperLink _hypButton;
		private Label _lblDisabled;

		#endregion

		#region Properties
		/// <summary>
		/// Icon button mode
		/// </summary>
		private IconButtonModeList _enmIconButtonMode = IconButton.IconButtonModeList.Hyperlink;
		public IconButtonModeList IconButtonMode {
			get { return _enmIconButtonMode; }
			set { _enmIconButtonMode = value; }
		}

		/// <summary>
		/// Is the button enabled?
		/// </summary>
		private Boolean _blnIsInitiallyEnabled = true;
		public Boolean IsInitiallyEnabled {
			get { return _blnIsInitiallyEnabled; }
			set { _blnIsInitiallyEnabled = value; }
		}

		/// <summary>
		/// overall grouping of icon (denotes position)
		/// </summary>
		private IconGroupList _enmIconGroup = IconGroupList.Generic;
		public IconGroupList IconGroup {
			get { return _enmIconGroup; }
			set { _enmIconGroup = value; }
		}

		/// <summary>
		/// Type of icon (add, edit etc) used to get the appropriate CSS class
		/// </summary>
		private string _strIconCSSType = "";
		public string IconCSSType {
			get { return _strIconCSSType; }
			set { _strIconCSSType = value; }
		}

		/// <summary>
		/// title resource (from Buttons.resx)
		/// </summary>
		private string _strIconTitleResource;
		public string IconTitleResource {
			get { return _strIconTitleResource; }
			set { _strIconTitleResource = value; }
		}

		/// <summary>
		/// The contained LinkButton or HyperLink ID
		/// </summary>
		public string IconButtonID {
			get {
				EnsureChildControls();
				return (_enmIconButtonMode == IconButtonModeList.Hyperlink) ? _hypButton.ID : _lnkButton.ID;
			}
		}

		/// <summary>
		/// The contained LinkButton or HyperLink Unique ID
		/// </summary>
		public string IconButtonUniqueID {
			get {
				EnsureChildControls();
				return (_enmIconButtonMode == IconButtonModeList.Hyperlink) ? _hypButton.UniqueID : _lnkButton.UniqueID;
			}
		}

		/// <summary>
		/// The contained LinkButton or HyperLink Client ID
		/// </summary>
		public string IconButtonClientID {
			get {
				EnsureChildControls();
				return (_enmIconButtonMode == IconButtonModeList.Hyperlink) ? _hypButton.ClientID : _lnkButton.ClientID;
			}
		}

		/// <summary>
		/// which side the button is aligned and has padding etc
		/// </summary>
		private String _strAlignment = "LEFT";
		public String Alignment {
			get { return _strAlignment; }
			set { _strAlignment = value; }
		}

		/// <summary>
		/// client-side code to run on button click
		/// </summary>
		private string _strClientClick = "";
		public string ClientClick {
			get { return _strClientClick; }
			set { _strClientClick = value; }
		}

		/// <summary>
		/// Anchor Href (anchor mode only)
		/// </summary>
		private string _strHref = "javascript:void(0);";
		public string Href {
			get { return _strHref; }
			set { _strHref = value; }
		}

		/// <summary>
		/// Is it now enabled?
		/// </summary>
		private bool _blnIsNowEnabled;
		public bool IsNowEnabled {
			get { return _blnIsNowEnabled; }
			set { _blnIsNowEnabled = value; }
		}

		#endregion

		#region Event handlers
		public event EventHandler Click;
		#endregion

		#region Overrides

		protected override void OnInit(EventArgs e) {
			string strCSS = "IconButton.css";
			if (Page is Pages.Base) {
				((Pages.Base)Page).AddCSSFile(strCSS);
			} else {
				//probably a login page
				Literal lit = new Literal();
				lit.Text = string.Format(@"<link rel=""stylesheet"" text=""text/css"" href=""css/Controls/{0}/Original/{1}"" />", Page.Theme, strCSS);
				Page.Header.Controls.Add(lit);
			}
			base.OnInit(e);
		}

		/// <summary>
		/// OnLoad
		/// </summary>
		/// <param name="e"></param>
		protected override void OnLoad(EventArgs e) {
			EnsureChildControls();
			//set button text
			string strButtonText = Functions.GetGlobalResource("Buttons", _strIconTitleResource);
			_lblDisabled.Text = strButtonText;
			if (_enmIconButtonMode == IconButton.IconButtonModeList.Hyperlink) _hypButton.Text = strButtonText;
			if (_enmIconButtonMode == IconButton.IconButtonModeList.LinkButton) _lnkButton.Text = Functions.GetGlobalResource("Buttons", _strIconTitleResource);
			if (!Page.IsPostBack) _blnIsNowEnabled = IsInitiallyEnabled;
			base.OnLoad(e);
		}

		/// <summary>
		/// OnPreRender
		/// </summary>
		/// <param name="e"></param>
		protected override void OnPreRender(EventArgs e) {
			SetEnabledState();
			base.OnPreRender(e);
		}

		/// <summary>
		/// create controls
		/// </summary>
		protected override void CreateChildControls() {
			_lblDisabled = ControlBuilders.CreateLabelInsideParent(this);
			_lblDisabled.ID = "lblDisabled";
			if (_enmIconButtonMode == IconButton.IconButtonModeList.Hyperlink) {
				_hypButton = ControlBuilders.CreateHyperLinkInsideParent(this);
				_hypButton.ID = "hyp";
				if (_strClientClick == "") {
					_hypButton.Attributes["onclick"] = "";
					_hypButton.NavigateUrl = (_strHref == "") ? "javascript:void(0);" : _strHref;
				} else {
					_hypButton.Attributes["onclick"] = _strClientClick;
					_hypButton.NavigateUrl = "javascript:void(0);";
				}
			} else {
				_lnkButton = ControlBuilders.CreateLinkButtonInsideParent(this);
				_lnkButton.ID = "lnk";
				if (_strClientClick == "") {
					_lnkButton.Click += new EventHandler(lnkButton_Click);
				} else {
					_lnkButton.OnClientClick = _strClientClick;
				}
			}

			base.CreateChildControls();
		}

		#endregion

		#region EventHandlers

		/// <summary>
		/// linkbutton click event
		/// </summary>
		/// <param name="sender"></param>
		/// <param name="e"></param>
		void lnkButton_Click(object sender, EventArgs e) {
			if (this.Click != null) {
				Click(sender, e);
			}
		}

		#endregion

		/// <summary>
		/// Derive css class for button
		/// </summary>
		/// <param name="blnEnabled"></param>
		/// <returns></returns>
		private string GetCssClass(bool blnEnabled) {
			string strCssClass = "iconButton";
			string strDisabled = (blnEnabled) ? "" : "_Disabled";
			if (_strIconCSSType == "") _strIconCSSType = _strIconTitleResource;
			strCssClass += string.Format(" iconButton_{0}{1} iconButton_{0}_{2}{1}", _enmIconGroup, strDisabled, _strIconCSSType);
			switch (_strAlignment.ToUpper()) {
				case "LEFT": strCssClass += " iconButton_alignLeft"; break;
				case "RIGHT": strCssClass += " iconButton_alignRight"; break;
			}
			return strCssClass;
		}

		/// <summary>
		/// Sets enabled state
		/// </summary>
		/// <param name="blnEnable"></param>
		private void SetEnabledState() {
			if (_enmIconButtonMode == IconButton.IconButtonModeList.LinkButton) {
				_lnkButton.CssClass = GetCssClass(true) + ((_blnIsNowEnabled) ? "" : " invisible");
			} else {
				_hypButton.CssClass = GetCssClass(true) + ((_blnIsNowEnabled) ? "" : " invisible");
			}
			_lblDisabled.CssClass = GetCssClass(false) + ((_blnIsNowEnabled) ? " invisible" : "");
		}


		#region Enumerations
		/// <summary>
		/// Defines the 'group' of icon for css to pick-up and apply to an IconButton
		/// ie. the ones in the top bar are on a different background colour and are a different size
		/// </summary>
		public enum IconGroupList {
			Sub, //in the sub-title bar underneath the main navigation
			Nugget, //inside a nugget
			LeftNugget, //inside a left nugget
			Top, //right at the top of the screen
			Generic, //anywhere else, eg. Login screen
			Big, //Big icons
			DropDown, //Dropdown listbox icons
			Filter, //Filter
			FormBody, //inside the body of a form
			ButtonStrip //on a button strip
		}

		/// <summary>
		/// Icon button mode
		/// </summary>
		public enum IconButtonModeList {
			LinkButton,
			Hyperlink
		}

		#endregion
	}
}
