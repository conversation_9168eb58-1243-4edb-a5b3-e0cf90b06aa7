<%@ Control Language="C#" CodeBehind="MyOpenPurchaseOrders.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyOpenPurchaseOrders" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Nugget:DesignBase id="ctlDB" runat="server">
	<Content>
		<div class="homepageNugget">
		    <asp:Panel ID="pnlOpen" runat="server">
			    <h5><%=Functions.GetGlobalResource("misc", "Open")%></h5>
			    <ReboundUI:SimpleDataTable ID="tblOpen" runat="server" AllowSelection="false" />
		    </asp:Panel>
		    <asp:Panel ID="pnlOverdue" runat="server" CssClass="overdue">
			    <h5><%=Functions.GetGlobalResource("misc", "Overdue")%></h5>
			    <ReboundUI:SimpleDataTable ID="tblOverdue" runat="server" AllowSelection="false" />
		    </asp:Panel>
		    <asp:Panel ID="pnlMore" runat="server" CssClass="homeNuggetMoreLink">
				<ReboundUI:PageHyperLink id="lnkMore" runat="server" PageType="Orders_PurchaseOrderBrowse" OverrideTextResource="MoreOpenPurchaseOrders" CssClass="nubButton nubButtonAlignLeft" />
			</asp:Panel>
		</div>
	</Content>
</ReboundUI_Nugget:DesignBase>
