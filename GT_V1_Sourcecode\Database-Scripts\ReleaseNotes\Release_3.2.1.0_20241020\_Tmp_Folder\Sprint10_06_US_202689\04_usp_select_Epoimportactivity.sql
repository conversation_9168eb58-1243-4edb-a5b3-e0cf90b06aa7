﻿SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
/*
===========================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-202689]		Trung Pham			12-SEP-2024		UPDATE			Get record by inactive param
===========================================================================================
*/

CREATE OR ALTER PROCEDURE [dbo].[usp_select_Epoimportactivity]                                  
 @DisplayLength int=0                                
,@DisplayStart int=0                                
,@SortCol int=0                              
,@SortDir nvarchar(10)                                  
,@Search nvarchar(255) = NULL             
,@ClientType int=0               
,@selectedclientid int=0             
,@CreateBy int=0
,@IsInactive int=0
                                
as                                  
begin                   
                             
    Declare @FirstRec int, @LastRec int                                  
    Set @FirstRec = @DisplayStart;                                  
    Set @LastRec = @DisplayStart + @DisplayLength;                                  
                                     
    With CTE_Stock as                                  
    (                                  
         Select ROW_NUMBER() over (order by                                  
                                          
         importdate  desc                             
   )                                  
         as RowNum,                                  
         COUNT(*) over() as TotalCount,                                  
                              
  epo.ImportDate ,epo.ImportName  ,epo.RowsAffected ,epo.Target ,(case when epo.ClientType=1 then 'HUB' when epo.ClientType=2 then 'UK' when epo.ClientType=3 then 'HK' else '' end) ClientTypeText ,
  epo.ClientType, lg.EmployeeName as ImportedBy, epo.Inactive, lg.EmployeeName as InactiveBy, epo.InactiveDate, epo.ImportId
  
  from BorisGlobalTraderimports.dbo.tbImportActivity_Epo epo
  left join tbLogin lg on lg.LoginId = epo.CreateBy OR epo.InactiveBy = lg.LoginId
  where isnull(epo.ClientType,2)=@ClientType AND ISNULL(epo.Inactive,0) = @IsInactive
                   
    )                                  
    Select RowNum,TotalCount,                              
                       
 CONVERT(varchar,ImportDate,9) as Date                
 ,ImportName as Source ,RowsAffected as Rows ,Target  as Status ,ClientTypeText as Client, ImportedBy,ISNULL(Inactive,0), InactiveBy, CONVERT(varchar,InactiveDate,9) as InactiveDate, ImportId
                               
    from CTE_Stock                                  
    where  RowNum > @FirstRec and RowNum <= @LastRec                                 
    ORDER BY                 
                 
ImportDate desc                  
    
end 


