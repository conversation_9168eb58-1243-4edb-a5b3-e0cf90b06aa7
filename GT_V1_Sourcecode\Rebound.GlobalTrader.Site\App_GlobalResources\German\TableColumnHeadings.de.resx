<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Action" xml:space="preserve">
    <value>Tätigkeit</value>
  </data>
  <data name="ActivityDate" xml:space="preserve">
    <value>Tätigkeits-Datum</value>
  </data>
  <data name="Address" xml:space="preserve">
    <value>Adresse</value>
  </data>
  <data name="AddressName" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="AddressType" xml:space="preserve">
    <value>Art</value>
  </data>
  <data name="AirWayBill" xml:space="preserve">
    <value>Luft-Weise Bill</value>
  </data>
  <data name="Allocated" xml:space="preserve">
    <value>Zugeteilt</value>
  </data>
  <data name="Authoriser" xml:space="preserve">
    <value>Authoriser</value>
  </data>
  <data name="Buy" xml:space="preserve">
    <value>Kauf</value>
  </data>
  <data name="Buyer" xml:space="preserve">
    <value>Kunde</value>
  </data>
  <data name="By" xml:space="preserve">
    <value>Durch</value>
  </data>
  <data name="CeaseDate" xml:space="preserve">
    <value>Hören Sie Datum auf</value>
  </data>
  <data name="Charge" xml:space="preserve">
    <value>Gebühr</value>
  </data>
  <data name="City" xml:space="preserve">
    <value>Stadt</value>
  </data>
  <data name="Code" xml:space="preserve">
    <value>Code</value>
  </data>
  <data name="CommunicationLogType" xml:space="preserve">
    <value>Kommunikations-Maschinenbordbuch-Art</value>
  </data>
  <data name="Company" xml:space="preserve">
    <value>Firma</value>
  </data>
  <data name="CompanyName" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="CompanyType" xml:space="preserve">
    <value>Art</value>
  </data>
  <data name="Contact" xml:space="preserve">
    <value>Contact</value>
  </data>
  <data name="ContactLogContactName" xml:space="preserve">
    <value>Contact Names</value>
  </data>
	<data name="EntertainTypes" xml:space="preserve">
    <value>Entertain Types</value>
  </data>
  <data name="ContactLogDetails" xml:space="preserve">
    <value>Details</value>
  </data>
  <data name="ContactLogType" xml:space="preserve">
    <value>Art</value>
  </data>
  <data name="ContactName" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="Cost" xml:space="preserve">
    <value>Kosten</value>
  </data>
  <data name="Country" xml:space="preserve">
    <value>Land</value>
  </data>
  <data name="CountryOfManufacture" xml:space="preserve">
    <value>Land der Fertigung</value>
  </data>
  <data name="CreditDate" xml:space="preserve">
    <value>Gutschrift-Datum</value>
  </data>
  <data name="CreditNote" xml:space="preserve">
    <value>Gutschrift</value>
  </data>
  <data name="CRMA" xml:space="preserve">
    <value>Kunde RMA</value>
  </data>
  <data name="CurrentRate" xml:space="preserve">
    <value>Tageskurs</value>
  </data>
  <data name="CurrentRate2" xml:space="preserve">
    <value>Tageskurs 2</value>
  </data>
  <data name="Customer" xml:space="preserve">
    <value>Kunde </value>
  </data>
  <data name="CustomerPart" xml:space="preserve">
    <value>Kunden-Teil</value>
  </data>
  <data name="CustomerPO" xml:space="preserve">
    <value>Kunde PO</value>
  </data>
  <data name="CustomerPurchaseOrderNo" xml:space="preserve">
    <value>Kunde PO Nr</value>
  </data>
  <data name="CustomerRequirement" xml:space="preserve">
    <value>Kunden-Anforderung</value>
  </data>
  <data name="CustomerRMADate" xml:space="preserve">
    <value>CRMA Date</value>
  </data>
  <data name="Date" xml:space="preserve">
    <value>Date</value>
  </data>
  <data name="DateCode" xml:space="preserve">
    <value>DC</value>
  </data>
  <data name="DateDelivered" xml:space="preserve">
    <value>Geliefert</value>
  </data>
  <data name="DateDue" xml:space="preserve">
    <value>Passend</value>
  </data>
  <data name="DateInvoiced" xml:space="preserve">
    <value>Fakturiert</value>
  </data>
  <data name="DateOffered" xml:space="preserve">
    <value>Angeboten</value>
  </data>
  <data name="DateOrdered" xml:space="preserve">
    <value>Bestellt</value>
  </data>
  <data name="DatePromised" xml:space="preserve">
    <value>Versprochen</value>
  </data>
  <data name="DateQuoted" xml:space="preserve">
    <value>Veranschlagen</value>
  </data>
  <data name="DateReceived" xml:space="preserve">
    <value>Empfangen</value>
  </data>
  <data name="DateRequired" xml:space="preserve">
    <value>Erforderlich</value>
  </data>
  <data name="Days" xml:space="preserve">
    <value>Tage</value>
  </data>
  <data name="DebitDate" xml:space="preserve">
    <value>Schuldposten-Datum</value>
  </data>
  <data name="DebitNote" xml:space="preserve">
    <value>Schuldposten</value>
  </data>
  <data name="DeliveryDate" xml:space="preserve">
    <value>Lieferfrist</value>
  </data>
  <data name="Description" xml:space="preserve">
    <value>Beschreibung</value>
  </data>
  <data name="Detail" xml:space="preserve">
    <value>Detail</value>
  </data>
  <data name="Details" xml:space="preserve">
    <value>Details</value>
  </data>
  <data name="Division" xml:space="preserve">
    <value>Abteilung</value>
  </data>
  <data name="Document" xml:space="preserve">
    <value>Dokument</value>
  </data>
  <data name="Due" xml:space="preserve">
    <value>Passend</value>
  </data>
  <data name="DueDate" xml:space="preserve">
    <value>Abgabefrist</value>
  </data>
  <data name="DutyCode" xml:space="preserve">
    <value>Aufgaben-Code</value>
  </data>
  <data name="EECMember" xml:space="preserve">
    <value>EWG-Mitglied</value>
  </data>
  <data name="Email" xml:space="preserve">
    <value>Email</value>
  </data>
  <data name="EnteredBy" xml:space="preserve">
    <value>Vorbei hereingekommen</value>
  </data>
  <data name="ExpediteDate" xml:space="preserve">
    <value>Beschleunigen Sie Datum</value>
  </data>
  <data name="Extension" xml:space="preserve">
    <value>Ext</value>
  </data>
  <data name="Fax" xml:space="preserve">
    <value>Telefax</value>
  </data>
  <data name="FooterText" xml:space="preserve">
    <value>Footer Text</value>
  </data>
  <data name="From" xml:space="preserve">
    <value>Von</value>
  </data>
  <data name="FullPart" xml:space="preserve">
    <value>Volles Teil</value>
  </data>
  <data name="GlobalCountryList" xml:space="preserve">
    <value>Vorlagenland</value>
  </data>
  <data name="GlobalCurrencyList" xml:space="preserve">
    <value>Vorlagenwährung</value>
  </data>
  <data name="GoodsIn" xml:space="preserve">
    <value>Goods In</value>
  </data>
  <data name="GoodsInDate" xml:space="preserve">
    <value>Waren im Datum</value>
  </data>
  <data name="GoodsInNo" xml:space="preserve">
    <value>Goods In Note</value>
  </data>
  <data name="GP" xml:space="preserve">
    <value>GP (%)</value>
  </data>
  <data name="GPDetail" xml:space="preserve">
    <value>Gp-Sonderkommando</value>
  </data>
  <data name="InAdvance" xml:space="preserve">
    <value>Im Voraus</value>
  </data>
  <data name="Include" xml:space="preserve">
    <value>Schließen Sie ein</value>
  </data>
  <data name="IndustryType" xml:space="preserve">
    <value>Industrie-Art</value>
  </data>
  <data name="Invoice" xml:space="preserve">
    <value>Rechnung</value>
  </data>
  <data name="InvoiceAmount" xml:space="preserve">
    <value>Menge</value>
  </data>
  <data name="InvoiceDate" xml:space="preserve">
    <value>Rechnungs-Datum</value>
  </data>
  <data name="InvoiceNo" xml:space="preserve">
    <value>Rechnungs-Nr.</value>
  </data>
  <data name="InvoiceTotal" xml:space="preserve">
    <value>Rechnungs-Gesamtmenge</value>
  </data>
  <data name="IPAddress" xml:space="preserve">
    <value>IP Address</value>
  </data>
  <data name="IsConsignment" xml:space="preserve">
    <value>Lieferung?</value>
  </data>
  <data name="IsDefault" xml:space="preserve">
    <value>Rückstellung?</value>
  </data>
  <data name="IsDefaultBill" xml:space="preserve">
    <value>Rückstellung Bill?</value>
  </data>
  <data name="IsDefaultPO" xml:space="preserve">
    <value>Rückstellung PO?</value>
  </data>
  <data name="IsDefaultShip" xml:space="preserve">
    <value>Rückstellungs-Schiff?</value>
  </data>
  <data name="IsDefaultSO" xml:space="preserve">
    <value>Default SO?</value>
  </data>
  <data name="IsOnHold" xml:space="preserve">
    <value>Auf Einfluss?</value>
  </data>
  <data name="IsPrinted" xml:space="preserve">
    <value>Gedruckt?</value>
  </data>
  <data name="Item" xml:space="preserve">
    <value>Einzelteil</value>
  </data>
  <data name="JobTitle" xml:space="preserve">
    <value>Stellenbezeichnung</value>
  </data>
  <data name="LandedCost" xml:space="preserve">
    <value>Gelandete Kosten</value>
  </data>
  <data name="LastContacted" xml:space="preserve">
    <value>Letztes in Verbindung getreten</value>
  </data>
  <data name="LastReceived" xml:space="preserve">
    <value>Letztes empfangen</value>
  </data>
  <data name="ListName" xml:space="preserve">
    <value>Listen-Name</value>
  </data>
  <data name="Location" xml:space="preserve">
    <value>Position</value>
  </data>
  <data name="Login" xml:space="preserve">
    <value>Login</value>
  </data>
  <data name="Lot" xml:space="preserve">
    <value>Los</value>
  </data>
  <data name="Manufacturer" xml:space="preserve">
    <value>Hersteller</value>
  </data>
  <data name="ManufacturerAbbreviation" xml:space="preserve">
    <value>Hersteller</value>
  </data>
  <data name="ManufacturerName" xml:space="preserve">
    <value>Hersteller-Name</value>
  </data>
  <data name="Members" xml:space="preserve">
    <value>Mitglieder</value>
  </data>
  <data name="Name" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="NextNumber" xml:space="preserve">
    <value>Folgende Zahl</value>
  </data>
  <data name="Notes" xml:space="preserve">
    <value>Anmerkungen</value>
  </data>
  <data name="Number" xml:space="preserve">
    <value>Zahl</value>
  </data>
  <data name="NumberOfUsers" xml:space="preserve">
    <value>Benutzer</value>
  </data>
  <data name="OfferPrice" xml:space="preserve">
    <value>Angebots-Preis</value>
  </data>
  <data name="OpenOrdersRepriced" xml:space="preserve">
    <value>Geöffnete Aufträge Repriced?</value>
  </data>
  <data name="OrderValue" xml:space="preserve">
    <value>Auftrags-Wert</value>
  </data>
  <data name="Package" xml:space="preserve">
    <value>Paket</value>
  </data>
  <data name="Part" xml:space="preserve">
    <value>Teil</value>
  </data>
  <data name="PartNo" xml:space="preserve">
    <value>Teilenummer</value>
  </data>
  <data name="PostedValue" xml:space="preserve">
    <value>Informierte Linien Wert</value>
  </data>
  <data name="Price" xml:space="preserve">
    <value>Price</value>
  </data>
  <data name="Priority" xml:space="preserve">
    <value>Priorität</value>
  </data>
  <data name="Product" xml:space="preserve">
    <value>Produkt</value>
  </data>
  <data name="ProductAndPackage" xml:space="preserve">
    <value>Prod / Pack</value>
  </data>
  <data name="PurchaseOrder" xml:space="preserve">
    <value>PO</value>
  </data>
  <data name="PurchaseOrderDate" xml:space="preserve">
    <value>PO-Datum</value>
  </data>
  <data name="PurchasePrice" xml:space="preserve">
    <value>Kaufpreis</value>
  </data>
  <data name="QualityControlNotes" xml:space="preserve">
    <value>Anmerkungen</value>
  </data>
  <data name="Quantity" xml:space="preserve">
    <value>Quantität</value>
  </data>
  <data name="QuantityAllocated" xml:space="preserve">
    <value>Quantität zugeteilt</value>
  </data>
  <data name="QuantityAuthorised" xml:space="preserve">
    <value> Quantität autorisierte</value>
  </data>
  <data name="QuantityAvailable" xml:space="preserve">
    <value>Quantität vorhanden</value>
  </data>
  <data name="QuantityBackOrder" xml:space="preserve">
    <value>Quantität-unerledigter Auftrag</value>
  </data>
  <data name="QuantityInStock" xml:space="preserve">
    <value>Quantität auf Lager</value>
  </data>
  <data name="QuantityOnOrder" xml:space="preserve">
    <value>Quantität auf Auftrag</value>
  </data>
  <data name="QuantityOrdered" xml:space="preserve">
    <value>Quantität bestellt</value>
  </data>
  <data name="QuantityOutstanding" xml:space="preserve">
    <value>Quantität hervorragend</value>
  </data>
  <data name="QuantityReceived" xml:space="preserve">
    <value>Quantität empfangen</value>
  </data>
  <data name="QuantityRemaining" xml:space="preserve">
    <value>Quantität-Bleiben</value>
  </data>
  <data name="QuantityShipped" xml:space="preserve">
    <value>Quantität versendete</value>
  </data>
  <data name="Quarantined" xml:space="preserve">
    <value>Unter Quarantäne gestellt</value>
  </data>
  <data name="QuarantinedAbbreviation" xml:space="preserve">
    <value>Qtnd?</value>
  </data>
  <data name="Quote" xml:space="preserve">
    <value>Preisangabe</value>
  </data>
  <data name="RaisedBy" xml:space="preserve">
    <value>Vorbei angehoben</value>
  </data>
  <data name="Rate" xml:space="preserve">
    <value>Rate</value>
  </data>
  <data name="Rate2" xml:space="preserve">
    <value>Rate 2</value>
  </data>
  <data name="Reason" xml:space="preserve">
    <value>Grund</value>
  </data>
  <data name="Received" xml:space="preserve">
    <value>Empfangen</value>
  </data>
  <data name="ReceivedBy" xml:space="preserve">
    <value>Vorbei empfangen</value>
  </data>
  <data name="ReceivedDate" xml:space="preserve">
    <value>Received Date</value>
  </data>
  <data name="Receiver" xml:space="preserve">
    <value>Vorbei empfangen</value>
  </data>
  <data name="Reference" xml:space="preserve">
    <value>Hinweis</value>
  </data>
  <data name="RequiredDate" xml:space="preserve">
    <value>Erforderliches Datum</value>
  </data>
  <data name="Requirement" xml:space="preserve">
    <value>Anforderung</value>
  </data>
  <data name="Resale" xml:space="preserve">
    <value>Weiterverkauf</value>
  </data>
  <data name="ResalePrice" xml:space="preserve">
    <value>Wiederverkaufspreis</value>
  </data>
  <data name="ReturnDate" xml:space="preserve">
    <value>Rückholdatum</value>
  </data>
  <data name="ROHS" xml:space="preserve">
    <value>ROHS</value>
  </data>
  <data name="Salesman" xml:space="preserve">
    <value>Verkäufer</value>
  </data>
  <data name="SalesOrder" xml:space="preserve">
    <value>SO</value>
  </data>
  <data name="Salesperson" xml:space="preserve">
    <value>Verkäufer</value>
  </data>
  <data name="SalesValue" xml:space="preserve">
    <value>Verkaufswert</value>
  </data>
  <data name="SecurityGroup" xml:space="preserve">
    <value>Gruppe</value>
  </data>
  <data name="Sell" xml:space="preserve">
    <value>Verkauf</value>
  </data>
  <data name="SellingPrice" xml:space="preserve">
    <value>Verkaufspreis</value>
  </data>
  <data name="Service" xml:space="preserve">
    <value>Service</value>
  </data>
  <data name="ShipDate" xml:space="preserve">
    <value>Schiffs-Datum</value>
  </data>
  <data name="ShipInCost" xml:space="preserve">
    <value>Schiff in den Kosten</value>
  </data>
  <data name="ShippedBy" xml:space="preserve">
    <value>Vorbei versendet</value>
  </data>
  <data name="Shipper" xml:space="preserve">
    <value>Verlader</value>
  </data>
  <data name="ShippingCost" xml:space="preserve">
    <value>Verschiffen-Kosten</value>
  </data>
  <data name="ShipStatus" xml:space="preserve">
    <value>Schiffs-Status</value>
  </data>
  <data name="ShipVia" xml:space="preserve">
    <value>Verschiffen-Methode</value>
  </data>
  <data name="SRMA" xml:space="preserve">
    <value>Lieferant RMA</value>
  </data>
  <data name="StartDate" xml:space="preserve">
    <value>Anfangsdatum</value>
  </data>
  <data name="StartTime" xml:space="preserve">
    <value>Anlasszeit</value>
  </data>
  <data name="Status" xml:space="preserve">
    <value>Status</value>
  </data>
  <data name="StockCount" xml:space="preserve">
    <value>Auf lagerzählimpuls</value>
  </data>
  <data name="Subject" xml:space="preserve">
    <value>Thema</value>
  </data>
  <data name="Supplier" xml:space="preserve">
    <value>Lieferant</value>
  </data>
  <data name="SupplierInvoice" xml:space="preserve">
    <value>Lieferanten-Rechnung</value>
  </data>
  <data name="SupplierPart" xml:space="preserve">
    <value>Lieferanten-Teil</value>
  </data>
  <data name="SupplierRating" xml:space="preserve">
    <value>Bewertung</value>
  </data>
  <data name="SupplierRMADate" xml:space="preserve">
    <value>Datum des Lieferanten-RMA</value>
  </data>
  <data name="Symbol" xml:space="preserve">
    <value>Symbol</value>
  </data>
  <data name="Table" xml:space="preserve">
    <value>Activity</value>
  </data>
  <data name="Task" xml:space="preserve">
    <value>Aufgabe</value>
  </data>
  <data name="Tax" xml:space="preserve">
    <value>Steuer</value>
  </data>
  <data name="Team" xml:space="preserve">
    <value>Mannschaft</value>
  </data>
  <data name="Tel" xml:space="preserve">
    <value>Telefon</value>
  </data>
  <data name="TelephonePrefix" xml:space="preserve">
    <value>Wählender Code</value>
  </data>
  <data name="Terms" xml:space="preserve">
    <value>Ausdrücke</value>
  </data>
  <data name="Text" xml:space="preserve">
    <value>Text</value>
  </data>
  <data name="Time" xml:space="preserve">
    <value>Zeit</value>
  </data>
  <data name="To" xml:space="preserve">
    <value>Zu</value>
  </data>
  <data name="Total" xml:space="preserve">
    <value>Total</value>
  </data>
  <data name="Type" xml:space="preserve">
    <value>Art</value>
  </data>
  <data name="URL" xml:space="preserve">
    <value>URL</value>
  </data>
  <data name="User" xml:space="preserve">
    <value>Benutzer</value>
  </data>
  <data name="Value" xml:space="preserve">
    <value>Wert</value>
  </data>
  <data name="Warehouse" xml:space="preserve">
    <value>Lager</value>
  </data>
  <data name="GrossProfit" xml:space="preserve">
    <value>Bruttogewinn</value>
  </data>
  <data name="Margin" xml:space="preserve">
    <value>Gewinnspanne</value>
  </data>
  <data name="SubGroup" xml:space="preserve">
    <value>Sub Group</value>
  </data>
  <data name="SerailNo" xml:space="preserve">
    <value>Serail Number</value>
  </data>
</root>