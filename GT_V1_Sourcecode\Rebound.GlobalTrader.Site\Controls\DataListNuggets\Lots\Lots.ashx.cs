//Marker    changed by        date           Remarks
//[001]      Umendra         25/02/2019     Add IsInactive parameter

using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;
using Rebound.GlobalTrader.Site;
using Rebound.GlobalTrader.Site.Enumerations;

namespace Rebound.GlobalTrader.Site.Controls.DataListNuggets.Data {

	public class Lots : Base {

		/// <summary>
		/// Gets the main data
		/// </summary>
		protected override void GetData() {
			JsonObject jsn = new JsonObject();
			JsonObject jsnRowsArray = new JsonObject(true);
			List<Lot> lst = BLL.Lot.DataListNugget(
				SessionManager.ClientID
				, GetFormValue_NullableInt("SortIndex")
				, GetFormValue_NullableInt("SortDir", SortColumnDirection.ASC)
				, GetFormValue_NullableInt("PageIndex", 0)
				, GetFormValue_NullableInt("PageSize", 10)
				, GetFormValue_StringForSearch("Code")
				//, GetFormValue_StringForSearch("Name")
                , GetFormValue_StringForLikeSearch("Name",true)
               // , GetFormValue_StringForNameSearchDecode("Name")
               , GetFormValue_Boolean("IsInActive")//[001] 
			);
			jsn.AddVariable("TotalRecords", (lst.Count > 0) ? lst[0].RowCnt : 0);
			foreach (BLL.Lot lt in lst) {
				JsonObject jsnRow = new JsonObject();
				jsnRow.AddVariable("ID", lt.LotId);
				jsnRow.AddVariable("Code", lt.LotCode);
				jsnRow.AddVariable("Name", lt.LotName);
				jsnRow.AddVariable("Consignment", (lt.Consignment) ? Functions.GetGlobalResource("Misc", "Yes") : "-");
				jsnRow.AddVariable("OnHold", (lt.OnHold) ? Functions.GetGlobalResource("Misc", "Yes") : "-");
				jsnRow.AddVariable("StockCount", lt.StockCount);
                jsnRow.AddVariable("Inactive", lt.Inactive);
				jsnRowsArray.AddVariable(jsnRow);
				jsnRow.Dispose();
				jsnRow = null;
			}
			jsn.AddVariable("Results", jsnRowsArray);
			OutputResult(jsn);
			jsnRowsArray.Dispose();
			jsnRowsArray = null;
			jsn.Dispose();
			jsn = null;
			base.GetData();
		}

		protected override void AddFilterStates() {
			AddFilterState("Code");
			AddFilterState("Name");
			base.AddFilterStates();
		}
	}
}
