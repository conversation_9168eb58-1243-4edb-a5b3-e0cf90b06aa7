<%--
Marker     changed by      date         Remarks
[001]      Vinay           04/10/2012   Degete Ref:#26#  - Add two more columns contact to identify Default Purchase ledger and Default Sales ledger
--%>
<%@ Control Language="C#" CodeBehind="ContactsForCompany.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Nuggets.ContactsForCompany" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Nugget:DesignBase ID="ctlDB" runat="server" BoxType="InPageSelection">
	<Links>
		<ReboundUI:IconButton ID="ibtnAdd" runat="server" IconButtonMode="hyperlink" IconGroup="Nugget" IconTitleResource="Add" IconCSSType="Add" />
		<ReboundUI:IconButton ID="ibtnDelete" runat="server" IconButtonMode="hyperlink" IconGroup="Nugget" IconTitleResource="InActive" IconCSSType="Delete" IsInitiallyEnabled="false" />
		<ReboundUI:IconButton ID="ibtnMakeDefaultSO" runat="server" IconButtonMode="hyperlink" IconGroup="Nugget" IconTitleResource="MakeDefaultSO" IconCSSType="Default" IsInitiallyEnabled="false" />
		<ReboundUI:IconButton ID="ibtnMakeDefaultPO" runat="server" IconButtonMode="hyperlink" IconGroup="Nugget" IconTitleResource="MakeDefaultPO" IconCSSType="Default" IsInitiallyEnabled="false" />
		<%--[001] code start--%>
		<ReboundUI:IconButton ID="ibtnDefaultPOLedger" runat="server" IconButtonMode="hyperlink" IconGroup="Nugget" IconTitleResource="MakeDefaultPOLedger" IconCSSType="Default" IsInitiallyEnabled="false" />
		<ReboundUI:IconButton ID="ibtnDefaultSOLedger" runat="server" IconButtonMode="hyperlink" IconGroup="Nugget" IconTitleResource="MakeDefaultSOLedger" IconCSSType="Default" IsInitiallyEnabled="false" />
		<%--[001] code start--%>
	</Links>
	
	<Content>
		<ReboundUI:FlexiDataTable ID="tblContacts" runat="server" PanelHeight="170" AllowSelection="true" />
	</Content>
	
	<Forms>
		<ReboundForm:ContactsForCompany_Add id="ctlFormAdd" runat="server" />
		<ReboundForm:ContactsForCompany_Delete id="ctlFormDelete" runat="server" />
		<ReboundForm:ContactsForCompany_MakeDefault id="ctlFormMakeDefault" runat="server" />
	</Forms>
</ReboundUI_Nugget:DesignBase>
