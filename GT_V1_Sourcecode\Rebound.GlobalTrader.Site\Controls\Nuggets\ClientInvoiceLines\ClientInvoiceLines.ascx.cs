//Marker     Changed by      Date               Remarks
//[005]      Prakash           11/04/2014         Add Client Invoice
using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.BLL;
using Rebound.GlobalTrader.Site.Controls;
using Rebound.GlobalTrader.Site;

namespace Rebound.GlobalTrader.Site.Controls.Nuggets {
    public partial class ClientInvoiceLines : Base
    {

		#region Locals

		protected IconButton _ibtnAdd;
		protected IconButton _ibtnDelete;
        protected FlexiDataTable _tblAll;
        protected Label _lblTotal;
        protected Panel _pnlSummary;
		#endregion

		#region Properties

		private int _intSIID = -1;
		public int GIID {
            get { return _intSIID; }
            set { _intSIID = value; }
		}

		private bool _blnCanAdd = true;
		public bool CanAdd {
            get { return _blnCanAdd; }
            set { _blnCanAdd = value; }
		}

	

		private bool _blnCanDelete = true;
		public bool CanDelete {
			get { return _blnCanDelete; }
			set { _blnCanDelete = value; }
		}
        private Int32 _intCompnayNo = -1;
        public Int32 CompanyNo
        {
            get { return _intCompnayNo; }
            set { _intCompnayNo = value; }
        }

            

		#endregion

		#region Overrides

		/// <summary>
		/// Oninit
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
			base.OnInit(e);
			WireUpControls();
            AddScriptReference("Controls.Nuggets.ClientInvoiceLines.ClientInvoiceLines.js");
            TitleText = Functions.GetGlobalResource("Nuggets", "ClientInvoiceLines");
			if (_objQSManager.ClientInvoiceID > 0) _intSIID = _objQSManager.ClientInvoiceID;
			SetupTables();
		}

		protected override void OnPreRender(EventArgs e) {
            _ibtnAdd.Visible = CanAdd;
            _ibtnDelete.Visible = CanDelete;

            SetupScriptDescriptors();
			base.OnPreRender(e);
		}

		#endregion

		/// <summary>
		/// Sets up AJAX script descriptors
		/// </summary>
		private void SetupScriptDescriptors() {
            _scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.Nuggets.ClientInvoiceLines", ctlDesignBase.ClientID);
			if (_blnCanAdd) _scScriptControlDescriptor.AddElementProperty("ibtnAdd", _ibtnAdd.ClientID);
			if (_blnCanDelete) _scScriptControlDescriptor.AddElementProperty("ibtnDelete", _ibtnDelete.ClientID);
			
			_scScriptControlDescriptor.AddProperty("intSIID", _intSIID);
			_scScriptControlDescriptor.AddComponentProperty("tblAll", _tblAll.ClientID);
            _scScriptControlDescriptor.AddProperty("intCompanyNo", _intCompnayNo);
            _scScriptControlDescriptor.AddElementProperty("pnlSummary", _pnlSummary.ClientID);
            _scScriptControlDescriptor.AddElementProperty("lblTotal", _lblTotal.ClientID);
            _scScriptControlDescriptor.AddProperty("blnPOHub", Convert.ToBoolean(SessionManager.IsPOHub));
		}

		private void SetupTables() {
            _tblAll.Columns.Add(new FlexiDataColumn("GoodsIn", WidthManager.GetWidth(WidthManager.ColumnWidth.SystemDocumentNumber)));
            _tblAll.Columns.Add(new FlexiDataColumn("PurchaseOrder", "InternalPurchaseOrder", WidthManager.GetWidth(WidthManager.ColumnWidth.PartNo)));
            _tblAll.Columns.Add(new FlexiDataColumn("PartNo", "SupplierPart", WidthManager.GetWidth(WidthManager.ColumnWidth.PartNo)));
            _tblAll.Columns.Add(new FlexiDataColumn("MFR", "DC", WidthManager.GetWidth(WidthManager.ColumnWidth.Quantity)));
            _tblAll.Columns.Add(new FlexiDataColumn("Product", "Package", WidthManager.GetWidth(WidthManager.ColumnWidth.Quantity)));
           // _tblAll.Columns.Add(new FlexiDataColumn("Notes", "Pack", WidthManager.GetWidth(WidthManager.ColumnWidth.Quantity)));
            _tblAll.Columns.Add(new FlexiDataColumn("ReceivedDate", Unit.Empty));

            _tblAll.Columns.Add(new FlexiDataColumn("Quantity", "UnitPrice", WidthManager.GetWidth(WidthManager.ColumnWidth.Quantity)));
            _tblAll.Columns.Add(new FlexiDataColumn("Value", WidthManager.GetWidth(WidthManager.ColumnWidth.CurrencyValue)));

			
		}

		/// <summary>
		/// Wire up controls from the ascx
		/// </summary>
		private void WireUpControls() {
			_ibtnAdd = FindIconButton("ibtnAdd");
			_ibtnDelete = FindIconButton("ibtnDelete");
            _tblAll = (FlexiDataTable)Functions.FindControlRecursive(this, "tblAll");
            _pnlSummary = (Panel)ctlDesignBase.FindContentControl("pnlSummary");
            _lblTotal = (Label)ctlDesignBase.FindContentControl("lblTotal");
		}
	}
}
