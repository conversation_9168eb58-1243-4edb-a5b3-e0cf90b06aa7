﻿<!DOCTYPE html>

<html lang="en" xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta charset="utf-8" />
    <link rel='stylesheet' href='https://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/4.1.3/css/bootstrap.min.css'>
    <script src='https://cdnjs.cloudflare.com/ajax/libs/jquery/3.4.1/jquery.min.js'></script>
    <script src='https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.15.0/popper.min.js'></script>
    <script src='https://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/4.3.1/js/bootstrap.min.js'></script>
    <script src='https://code.jquery.com/jquery-2.2.4.min.js'></script>
    <script src='https://maxcdn.bootstrapcdn.com/bootstrap/3.3.6/js/bootstrap.min.js'></script>
    <!-- Latest compiled and minified CSS -->
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.6/css/bootstrap.min.css" integrity="sha384-1q8mTJOASx8j1Au+a5WDVnPi2lkFfwwEAa8hDDdjZlpLegxhjVME1fgjWPGmkzs7" crossorigin="anonymous">
    <script src="https://unpkg.com/jquery@2.2.4/dist/jquery.js"></script>
    <!--jQueryUI version 1.11.4 -->
    <link rel="stylesheet" href="https://code.jquery.com/ui/1.11.4/themes/smoothness/jquery-ui.css" />
    <script src="https://code.jquery.com/ui/1.11.4/jquery-ui.min.js"></script>
    <link rel="stylesheet" href="//code.jquery.com/ui/1.13.2/themes/base/jquery-ui.css">
    <!--ParamQuery Grid css files-->
    <link rel="stylesheet" href="~/paramquery-8.1.0/pqgrid.dev.css" />
    <!--add pqgrid.ui.css for jQueryUI theme support-->
    <link rel="stylesheet" href="~/paramquery-8.1.0/pqgrid.ui.dev.css" />
    <!--ParamQuery Grid custom theme e.g., office, bootstrap, rosy, chocolate, etc (optional)-->
    @*
        <link rel="stylesheet" href="~/paramquery-8.1.0/themes/bootstrap/pqgrid.css" />*@   @*commented this file  version1.0*@
        <!--ParamQuery Grid js files-->
        <script type="text/javascript" src="~/paramquery-8.1.0/pqgrid.dev.js"></script>
        <!--ParamQuery Grid localization file-->
        <script src="~/paramquery-8.1.0/localize/pq-localize-en.js"></script>
        <!--Include pqTouch file to provide support for touch devices (optional)-->
        <script type="text/javascript" src="~/paramquery-8.1.0/pqTouch/pqtouch.min.js"></script>
        <!--Include jsZip file to support xlsx and zip export (optional)-->
        <script type="text/javascript" src="~/paramquery-8.1.0/jsZip-2.5.0/jszip.min.js"></script>

        <title></title>
        <style>
/*            body {
                font-family: Tahoma;
                background-color: #56954e;
            }*/

            .pq-grid-header-table {
                background-image: url(../../../App_Themes/Original/images/FlexiDataTable/th_bg.png);
                background-position: bottom;
                background-repeat: repeat-x;
                border-bottom: solid 2px #e0e0e0;
                background-color: #eeeeee;
            }

                .pq-grid-header-table span {
                    font-weight: bold;
                    font-size: 12px;
                    color: #999999 !important;
                }

            .pq-grid-col:hover {
                background-color: #e0e0e0;
                background-image: url(../../../App_Themes/Original/images/FlexiDataTable/th_bg.png);
                background-position: bottom;
                background-repeat: repeat-x;
            }

            .pq-grid-row:first-child > .pq-grid-col {
                border-right: 1px solid #bbb !important;
            }

            .pq-header-outer {
                border-bottom: none !important;
                background: none !important;
            }

            .pq-td-border-right > .pq-grid-row > .pq-grid-cell {
                border-right-color: #bbb !important;
                font-family: Tahoma !important;
            }

            .pq-grid-row.pq-striped {
                background: #fff;
            }

            .pq-grid-row {
                font-size: 11px !important;
            }

            .pq-cont-inner span a {
                color: #0000ff !important;
                background-image: url(../../../../App_Themes/Original/images/nubs/nub.gif);
                background-repeat: no-repeat;
                background-position: left 2px;
                padding-left: 13px;
                height: 12px;
            }

            .pq-state-select.ui-state-highlight {
                background: #000066 !important;
                color: #fff !important;
            }

            .ui-state-highlight a, .ui-widget-content .ui-state-highlight a, .ui-widget-header .ui-state-highlight a {
                color: #66ccFF !important;
                background-image: url(../../../App_Themes/Original/images/FlexiDataTable/nub_tb_selected.gif);
            }

            .pq-grid-row.pq-striped {
                border-bottom: 1px dotted #bbb !important;
            }

            .pq-grid-bottom {
                background-image: url(../../../App_Themes/Original/images/Nuggets/standard/bg_top.gif) !important;
                background-position: left top !important;
                background-repeat: repeat-x !important;
            }

            .ui-widget-header {
                background: #afeda5;
            }

            .ui-widget.ui-widget-content {
                border: none !important;
            }

            .pq-pager {
                padding: 1px 5px !important;
                font-size: 11px !important;
            }

            .head_in {
                height: 42px;
                border-width: 0px;
                border-style: solid;
                border-color: #bbbbbb;
                position: relative;
                background-color: #ffffff;
                position: relative;
                background-image: url(../../../App_Themes/Original/images/Nuggets/standard/bg_top.gif);
                background-repeat: repeat-x;
            }

            .topTR {
                background-image: url(../../../App_Themes/Original/images/Nuggets/list/top.gif);
                margin-top: -6px;
                margin-left: 6px;
                background-position: 100% 0px;
                height: 6px;
                font-size: 2px;
            }

            .topTL {
                background-image: url(../../../App_Themes/Original/images/Nuggets/list/top.gif);
                margin-right: 6px;
                height: 6px;
                font-size: 2px;
            }

            .top_inner h4 {
                font-family: Lucida Sans Unicode, Arial;
                font-weight: normal;
                font-size: 12px;
                line-height: 12px;
                color: #000000;
                margin: 0px;
                padding: 2px 0px 0px 5px;
            }

            .section_one {
                height: 25px;
            }
            .pq-grid-center-o {
                padding:0 1px 0 1px;
            }
        </style>
    </head>
<body>
    <div class="container-greenbase" style="padding: 1%;">
        <div class="section_one" id="headingThree">
            <div class="topTL"></div>
            <div class="topTR"></div>
            <div id="head_in_DivPartBom" class="head_in">
                <div class="top_inner">
                    <h4>IHS Cross Match BOM</h4>
                </div>
            </div>
        </div>
        <div id="grid_IHS"></div>
        <br>

        <div class="section_one" id="headingThree">
            <div class="topTL"></div>
            <div class="topTR"></div>
            <div id="head_in_DivPartBom" class="head_in">
                <div class="top_inner">
                    <h4>Sales X-Match</h4>
                </div>
            </div>
        </div>
        <div id="grid_xm"></div>

    </div>
    <script>
        var BOMID;
        $(document).ready(function () {
            var qrystr = new URLSearchParams(window.location.search);
            BOMID = qrystr.get("BOMID");
            var CRID = qrystr.get("CRID");

            LoadXMatchData(CRID, true);
            LoadIHSData(CRID, true);

        });
        function LoadIHSData(CId, refreshGrid) {
            if (CId == 'AllParts') {
                CId = ""
            }
            var colModel = [
                {
                    title: "Part No.",
                    width: "13%",
                    //dataType: "float",
                    //align: "right",
                    dataIndx: "PartName",
                    //render: function (ui) {

                    //    var htmlstr = "<span>" + ui.rowData["ManufacturerName"] + "</span ><br/>";
                    //    //htmlstr = htmlstr + " <span>" + ui.rowData.CustomerPart.toString() + "</span > ";
                    //    return htmlstr;
                    //}
                },
                {
                    title: "Description", //title of column.
                    width: "47%", //initial width of column
                    //dataType: "integer", //data type of column
                    dataIndx: "Descriptions",
                    //hidden: true,
                    //should match one of the keys in row data.
                    render: function (ui) {
                        return '<span  title="' + ui.rowData["Descriptions"] + '">' + ui.rowData["Descriptions"] + "</span ><br/>";
                    }
                },
                {
                    title: "Part Status", //title of column.
                    width: "7%", //initial width of column
                    //dataType: "integer", //data type of column
                    dataIndx: "PartStatus", //should match one of the keys in row data.
                    //hidden: true,
                    //render: function (ui) {
                    //    customerrequirementidval = ui.rowData.CustomerRequirementId.toString();
                    //    console.log(customerrequirementidval + '-----');
                    //}
                },
                {
                    title: "Manufacturer",
                    width: "13%",
                    //dataType: "string",
                    dataIndx: "ManufacturerName",
                    render: function (ui) {
                        return '<span class=' + "gridlink" + ' title="' + ui.rowData["ManufacturerName"] + '">' + "<a  href='../../Con_ManufacturerDetail.aspx?mfr=" + ui.rowData["ManufacturerNo"] + "' target='_Blank'>" + ui.rowData["ManufacturerName"] + "</a></span ><br/>";

                    }
                },
                {
                    title: "Average Price",
                    width: "10%",
                    dataIndx: "Descriptions",
                    /*dataType: "float",*/
                    //align: "right",
                    render: function (ui) {
                        var start = ui.rowData["Descriptions"].toString().indexOf("Avg Price :");
                        var end = ui.rowData["Descriptions"].toString().indexOf("(Date Updated :");
                        if (start > -1) {

                            var htmlstr = "<span>" + parseFloat(ui.rowData["Descriptions"].toString().substring(start + 11, end)).toFixed(4) + "</span >";
                        }
                        else {
                            htmlstr = "";
                        }
                        //htmlstr = htmlstr + " <span>" + ui.rowData.CustomerPart.toString() + "</span > ";
                        return htmlstr;
                    }
                },
                {
                    title: "Date Updated",
                    width: "10%",
                    /*dataType: "float",*/
                    //align: "right",
                    dataIndx: "Descriptions",
                    render: function (ui) {
                        var startdate = ui.rowData["Descriptions"].toString().indexOf("(Date Updated :");
                        var enddate = ui.rowData["Descriptions"].toString().lastIndexOf(")");
                        if (startdate > -1) {
                            var htmlstr = "<span>" + ui.rowData["Descriptions"].toString().substring(startdate + 15, enddate - 8) + "</span >";
                        }
                        else {
                            htmlstr = "";
                        }
                        return htmlstr;
                    }
                },
            ];

            //main object to be passed to pqGrid constructor.
            var qrystr = new URLSearchParams(window.location.search);
            var BomManagerID = qrystr.get("BOM");
            //var dataModel = GetBOMSearchData1();
            var dataModel = {
                location: "remote",
                dataType: "JSON",
                method: "POST",
                url: 'CustReqPartsGRIDIHSAPIBOMManager?BOMManagerID=' + BOMID + '&CustomerReqID=' + CId,
                getData: function (dataJSON) {
                    var data = dataJSON;
                    console.log('IHS data');
                    console.log(dataJSON);
                    //debugger;
                    var totalRecords = 0;
                    if (dataJSON.length != 0) {
                        totalRecords = dataJSON[0].TotalRecords;
                    }
                    return { curPage: dataJSON.curPage, totalRecords: totalRecords, data: dataJSON };
                    //return (dataJSON);
                }
            };
            //$gridPrj = $("#grid_md").pqGrid(obj).pqGrid('refreshDataAndView');
            var grid1 = $("#grid_IHS").pqGrid({
                width: "auto", height: 272,
                selectionModel: { type: null, native: true },
                dataModel: dataModel,
                rowHt: 35,
                hwrap: false,
                wrap: false,
                hoverMode: 'cell',
                colModel: colModel,

                editable: false,
                //freezeCols: 2,
                pageModel: { type: "local", rPP: 5, strRpp: "{0}", rPPOptions: [5, 10, 20, 50, 100] },
                numberCell: { show: false }
            })/*.pqGrid('refreshDataAndView')*/
            if (refreshGrid) {
                //debugger;
                grid1.pqGrid('refreshDataAndView');
            }
        }
        function LoadXMatchData(parts, refreshGrid) {
            if (parts == 'AllParts')
                parts = '';
            var colModel = [

                //{
                //    title: "CustomerRequirementId", //title of column.
                //    width: "10%", //initial width of column
                //    //dataType: "integer", //data type of column
                //    dataIndx: "CustomerRequirementId",
                //    hidden: true,
                //    //should match one of the keys in row data.
                //    //render: function (ui) {
                //    //    customerrequirementidval = ui.rowData.CustomerRequirementId.toString();
                //    //    console.log(customerrequirementidval + '-----');
                //    //}
                //},
                {
                    title: "", //title of column.
                    /*width: "10%", //initial width of column*/
                    //dataType: "integer", //data type of column
                    dataIndx: "BOMManagerNo", //should match one of the keys in row data.
                    hidden: true,
                    //render: function (ui) {
                    //    customerrequirementidval = ui.rowData.CustomerRequirementId.toString();
                    //    console.log(customerrequirementidval + '-----');
                    //}
                },
                {
                    title: "XPart No.",
                    width: "14%",
                    //dataType: "string",
                    dataIndx: "Part",
                    render: function (ui) {
                        //return ui.rowData.BOMManagerNo + '-<br/>' + ui.rowData.Part
                        //console.log("test row data" + ui.rowData);
                        return /*"<span><a  href='../../Ord_CusReqDetail.aspx?req=" + ui.rowData.XPartNumber + "' target='_top'>" +*/ ui.rowData.Part + "</a></span>";

                    }
                },
                {
                    title: "Invoice No",
                    width: "7%",
                    //dataType: "float",
                    //align: "right",
                    dataIndx: "InvoiceNumber",
                    render: function (ui) {

                        var htmlstr = "<span>" + ui.rowData["InvoiceNumber"] + "</span ><br/>";
                        //htmlstr = htmlstr + " <span>" + ui.rowData.CustomerPart.toString() + "</span > ";
                        return htmlstr;
                    }
                },
                {
                    title: "Inv. Sls",
                    width: "8%",
                    dataIndx: "InvoiceSls",
                    /*dataType: "float",*/
                    //align: "right",
                    render: function (ui) {

                        var htmlstr = "<span>" + ui.rowData["InvoiceSls"] + "</span ><br/>";
                        //htmlstr = htmlstr + " <span>" + ui.rowData.CustomerPart.toString() + "</span > ";
                        return htmlstr;
                    }
                },
                {
                    title: "Inv. Customer",
                    width: "15%",
                    /*dataType: "float",*/
                    //align: "right",
                    dataIndx: "InvoiceCustomer",
                    render: function (ui) {
                        var htmlstr = /*"<span><a  href='../../Con_ManufacturerDetail.aspx?mfr=" + ui.rowData["Invoice.Customer"] + "' target='_top'>" +*/ ui.rowData["InvoiceCustomer"] + "</a></span>";
                        //var htmlstr = "<span>" + ui.rowData.ManufacturerCode.toString() + "</span ><br/>";
                        //htmlstr = htmlstr + " <span>" + ui.rowData.DateCode.toString() + "</span > ";
                        return htmlstr;
                    }
                },
                {
                    title: "Inv. Date",
                    width: "6%",
                    dataType: "Date",
                    //align: "right",
                    dataIndx: "InvoiceDate",
                    render: function (ui) {
                        var date = new Date(parseInt(ui.rowData["InvoiceDate"].substr(6)));
                        var htmlstr = "<span>" + date.toLocaleDateString("en-GB") + "</span ><br/>";
                        //htmlstr = htmlstr + " <span>" + ui.rowData.PackageName.toString() + "</span > ";
                        return htmlstr;
                    }
                },
                {
                    title: "Inv. Qty",
                    width: "6%",
                    /*dataType: "float",*/
                    //align: "right",
                    dataIndx: "InvoiceQuantity",
                    render: function (ui) {
                        var htmlstr = "<span>" + ui.rowData["InvoiceQuantity"] + "</span ><br/>";
                        //htmlstr = htmlstr + " <span>" + getCustomDate(ui.rowData.DatePromised.toString()) + "</span > ";
                        return htmlstr;
                    }
                },
                {
                    title: "Unit Price",
                    width: "6%",
                    /*dataType: "float",*/
                    //align: "right",
                    dataIndx: "UnitPrice",
                    render: function (ui) {
                        var htmlstr = "<span>" + ui.rowData["UnitPrice"] + "</span ><br/>";
                        //htmlstr = htmlstr + "<span>" + ui.rowData.SalesmanName + "</span ><br/>";
                        return htmlstr;
                    }
                },
                {
                    title: "PO Order",
                    width: "7%",
                    /*dataType: "float",*/
                    //align: "right",
                    dataIndx: "POOrder",
                    render: function (ui) {
                        var htmlstr = "<span>" + ui.rowData["POOrder"] + "</span ><br/>";
                        //htmlstr = htmlstr + "<span>" + ui.rowData.SalesmanName + "</span ><br/>";
                        return htmlstr;
                    }
                },
                {
                    title: "PO Company",
                    width: "10.5%",
                    /*dataType: "float",*/
                    //align: "right",
                    dataIndx: "POCompany",
                    render: function (ui) {
                        var htmlstr = "<span>" + ui.rowData["POCompany"] + "</span ><br/>";
                        //htmlstr = htmlstr + "<span>" + ui.rowData.SalesmanName + "</span ><br/>";
                        return htmlstr;
                    }
                },
                {
                    title: "PO Qty",
                    width: "6%",
                    /*dataType: "float",*/
                    //align: "right",
                    dataIndx: "POQuantity",
                    render: function (ui) {
                        var htmlstr = "<span>" + ui.rowData["POQuantity"] + "</span ><br/>";
                        //htmlstr = htmlstr + "<span>" + ui.rowData.SalesmanName + "</span ><br/>";
                        return htmlstr;
                    }
                },
                {
                    title: "PO Price",
                    width: "6%",
                    /*dataType: "float",*/
                    //align: "right",
                    dataIndx: "POPrice",
                    render: function (ui) {
                        var htmlstr = "<span>" + ui.rowData["POPrice"] + "</span ><br/>";
                        //htmlstr = htmlstr + "<span>" + ui.rowData.SalesmanName + "</span ><br/>";
                        return htmlstr;
                    }
                },
                {
                    title: "PO Currency",
                    width: "8%",
                    /*dataType: "float",*/
                    //align: "right",
                    dataIndx: "POCurrency",
                    render: function (ui) {
                        var htmlstr = "<span>" + ui.rowData["POCurrency"] + "</span ><br/>";
                        //htmlstr = htmlstr + "<span>" + ui.rowData.SalesmanName + "</span ><br/>";
                        return htmlstr;
                    }
                }
            ];

            //main object to be passed to pqGrid constructor.
            var qrystr = new URLSearchParams(window.location.search);
            var BomManagerID = qrystr.get("BOM");
            //var dataModel = GetBOMSearchData1();
            var dataModel = {
                location: "remote",
                dataType: "JSON",
                method: "POST",
                url: 'GetUserMatchData?crossMatchType=rdInvoiceXMatch&matchFirstValue=3&chkMatchBase=false&chkMatchFirst=false&BOMManagerID=' + BOMID + '&CustomerRequirementID=' + parts,
                getData: function (dataJSON) {
                    var data = dataJSON;
                    console.log('xmatch data');
                    console.log(dataJSON);
                    //debugger;
                    var totalRecords = 0;
                    if (dataJSON.length != 0) {
                        totalRecords = dataJSON[0].TotalCount;
                    }
                    var cur_page = 1;
                    if (dataJSON.length != 0) {
                        cur_page = dataJSON[0].curpage;
                    }
                    return { curPage: cur_page, totalRecords: totalRecords, data: dataJSON };
                    //return (dataJSON);
                }
            };
            //$gridPrj = $("#grid_md").pqGrid(obj).pqGrid('refreshDataAndView');
            var grid1 = $("#grid_xm").pqGrid({
                width: "auto", height: 272,
                selectionModel: { type: null, native: true },
                dataModel: dataModel,
                rowHt: 35,
                hwrap: false,
                wrap: false,
                hoverMode: 'cell',
                colModel: colModel,

                editable: false,
                //freezeCols: 2,
                pageModel: { type: "remote", rPP: 5, strRpp: "{0}", rPPOptions: [5, 10, 20, 50, 100] },
                numberCell: { show: false }
            })/*.pqGrid('refreshDataAndView')*/;
            if ((parts != 'AllParts' && parts != "") || refreshGrid) {
                grid1.pqGrid('refreshDataAndView');
            }
        }
    </script>
</body>
</html>
