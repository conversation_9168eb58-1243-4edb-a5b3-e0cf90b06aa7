Type.registerNamespace("Rebound.GlobalTrader.Site.Pages.Warehouse");Rebound.GlobalTrader.Site.Pages.Warehouse.Warehouse=function(n){Rebound.GlobalTrader.Site.Pages.Warehouse.Warehouse.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Pages.Warehouse.Warehouse.prototype={get_ctlEllipses_ReceivePOs:function(){return this._ctlEllipses_ReceivePOs},set_ctlEllipses_ReceivePOs:function(n){this._ctlEllipses_ReceivePOs!==n&&(this._ctlEllipses_ReceivePOs=n)},get_txtReceivePO:function(){return this._txtReceivePO},set_txtReceivePO:function(n){this._txtReceivePO!==n&&(this._txtReceivePO=n)},get_ibtnReceivePOs:function(){return this._ibtnReceivePOs},set_ibtnReceivePOs:function(n){this._ibtnReceivePOs!==n&&(this._ibtnReceivePOs=n)},get_ctlEllipses_ShipSOs:function(){return this._ctlEllipses_ShipSOs},set_ctlEllipses_ShipSOs:function(n){this._ctlEllipses_ShipSOs!==n&&(this._ctlEllipses_ShipSOs=n)},get_txtShipSO:function(){return this._txtShipSO},set_txtShipSO:function(n){this._txtShipSO!==n&&(this._txtShipSO=n)},get_ibtnShipSOs:function(){return this._ibtnShipSOs},set_ibtnShipSOs:function(n){this._ibtnShipSOs!==n&&(this._ibtnShipSOs=n)},get_ctlEllipses_ReceiveCRMAs:function(){return this._ctlEllipses_ReceiveCRMAs},set_ctlEllipses_ReceiveCRMAs:function(n){this._ctlEllipses_ReceiveCRMAs!==n&&(this._ctlEllipses_ReceiveCRMAs=n)},get_txtReceiveCRMA:function(){return this._txtReceiveCRMA},set_txtReceiveCRMA:function(n){this._txtReceiveCRMA!==n&&(this._txtReceiveCRMA=n)},get_ibtnReceiveCRMAs:function(){return this._ibtnReceiveCRMAs},set_ibtnReceiveCRMAs:function(n){this._ibtnReceiveCRMAs!==n&&(this._ibtnReceiveCRMAs=n)},get_ctlEllipses_ShipSRMAs:function(){return this._ctlEllipses_ShipSRMAs},set_ctlEllipses_ShipSRMAs:function(n){this._ctlEllipses_ShipSRMAs!==n&&(this._ctlEllipses_ShipSRMAs=n)},get_txtShipSRMA:function(){return this._txtShipSRMA},set_txtShipSRMA:function(n){this._txtShipSRMA!==n&&(this._txtShipSRMA=n)},get_ibtnShipSRMAs:function(){return this._ibtnShipSRMAs},set_ibtnShipSRMAs:function(n){this._ibtnShipSRMAs!==n&&(this._ibtnShipSRMAs=n)},get_ctlEllipses_AllStock:function(){return this._ctlEllipses_AllStock},set_ctlEllipses_AllStock:function(n){this._ctlEllipses_AllStock!==n&&(this._ctlEllipses_AllStock=n)},get_txtSearchStock:function(){return this._txtSearchStock},set_txtSearchStock:function(n){this._txtSearchStock!==n&&(this._txtSearchStock=n)},get_ctlEllipses_Services:function(){return this._ctlEllipses_Services},set_ctlEllipses_Services:function(n){this._ctlEllipses_Services!==n&&(this._ctlEllipses_Services=n)},get_txtSearchService:function(){return this._txtSearchService},set_txtSearchService:function(n){this._txtSearchService!==n&&(this._txtSearchService=n)},get_ctlEllipses_Lots:function(){return this._ctlEllipses_Lots},set_ctlEllipses_Lots:function(n){this._ctlEllipses_Lots!==n&&(this._ctlEllipses_Lots=n)},get_ctlEllipses_GoodsIn:function(){return this._ctlEllipses_GoodsIn},set_ctlEllipses_GoodsIn:function(n){this._ctlEllipses_GoodsIn!==n&&(this._ctlEllipses_GoodsIn=n)},get_txtGoodsIn:function(){return this._txtGoodsIn},set_txtGoodsIn:function(n){this._txtGoodsIn!==n&&(this._txtGoodsIn=n)},get_ibtnGoodsIn:function(){return this._ibtnGoodsIn},set_ibtnGoodsIn:function(n){this._ibtnGoodsIn!==n&&(this._ibtnGoodsIn=n)},initialize:function(){Rebound.GlobalTrader.Site.Pages.Warehouse.Warehouse.callBaseMethod(this,"initialize")},goInit:function(){this._strPathToData="Code/Pages/Warehouse";this._strDataObject="Warehouse";this._strPathToData_Go="controls/LeftNuggets/QuickJump";this._strDataObject_Go="QuickJump";this._ibtnShipSOs&&$R_IBTN.addClick(this._ibtnShipSOs,Function.createDelegate(this,this.goShipSO));this._ibtnReceivePOs&&$R_IBTN.addClick(this._ibtnReceivePOs,Function.createDelegate(this,this.goReceivePO));this._ibtnReceiveCRMAs&&$R_IBTN.addClick(this._ibtnReceiveCRMAs,Function.createDelegate(this,this.goReceiveCRMA));this._ibtnShipSRMAs&&$R_IBTN.addClick(this._ibtnShipSRMAs,Function.createDelegate(this,this.goShipSRMA));this._ibtnGoodsIn&&$R_IBTN.addClick(this._ibtnGoodsIn,Function.createDelegate(this,this.goGoodsIn));$R_TXTBOX.addEnterPressedEvent(this._txtReceivePO,Function.createDelegate(this,this.goReceivePO));$R_TXTBOX.addEnterPressedEvent(this._txtShipSO,Function.createDelegate(this,this.goShipSO));$R_TXTBOX.addEnterPressedEvent(this._txtReceiveCRMA,Function.createDelegate(this,this.goReceiveCRMA));$R_TXTBOX.addEnterPressedEvent(this._txtShipSRMA,Function.createDelegate(this,this.goShipSRMA));$R_TXTBOX.addEnterPressedEvent(this._txtGoodsIn,Function.createDelegate(this,this.goGoodsIn));Rebound.GlobalTrader.Site.Pages.Warehouse.Warehouse.callBaseMethod(this,"goInit")},dispose:function(){this.isDisposed||(this._ctlEllipses_ReceivePOs&&this._ctlEllipses_ReceivePOs.dispose(),this._ctlEllipses_ShipSOs&&this._ctlEllipses_ShipSOs.dispose(),this._ctlEllipses_ReceiveCRMAs&&this._ctlEllipses_ReceiveCRMAs.dispose(),this._ctlEllipses_ShipSRMAs&&this._ctlEllipses_ShipSRMAs.dispose(),this._ctlEllipses_AllStock&&this._ctlEllipses_AllStock.dispose(),this._ctlEllipses_Services&&this._ctlEllipses_Services.dispose(),this._ctlEllipses_Lots&&this._ctlEllipses_Lots.dispose(),this._ctlEllipses_GoodsIn&&this._ctlEllipses_GoodsIn.dispose(),this._ibtnShipSOs&&$R_IBTN.clearHandlers(this._ibtnShipSOs),this._ibtnReceivePOs&&$R_IBTN.clearHandlers(this._ibtnReceivePOs),this._ibtnReceiveCRMAs&&$R_IBTN.clearHandlers(this._ibtnReceiveCRMAs),this._ibtnShipSRMAs&&$R_IBTN.clearHandlers(this._ibtnShipSRMAs),this._txtReceivePO&&$R_TXTBOX.clearEvents(this._txtReceivePO),this._txtShipSO&&$R_TXTBOX.clearEvents(this._txtShipSO),this._txtReceiveCRMA&&$R_TXTBOX.clearEvents(this._txtReceiveCRMA),this._txtShipSRMA&&$R_TXTBOX.clearEvents(this._txtShipSRMA),this._txtGoodsIn&&$R_TXTBOX.clearEvents(this._txtGoodsIn),this._ctlEllipses_ReceivePOs=null,this._txtReceivePO=null,this._ibtnReceivePOs=null,this._ctlEllipses_ShipSOs=null,this._txtShipSO=null,this._ibtnShipSOs=null,this._ctlEllipses_ReceiveCRMAs=null,this._txtReceiveCRMA=null,this._ibtnReceiveCRMAs=null,this._ctlEllipses_ShipSRMAs=null,this._txtShipSRMA=null,this._ibtnShipSRMAs=null,this._ctlEllipses_AllStock=null,this._txtSearchStock=null,this._ctlEllipses_Services=null,this._txtSearchService=null,this._ctlEllipses_Lots=null,this._ctlEllipses_GoodsIn=null,this._txtGoodsIn=null,this._ibtnGoodsIn=null,Rebound.GlobalTrader.Site.Pages.Warehouse.Warehouse.callBaseMethod(this,"dispose"))},countReceivePOs:function(){var n=this._ctlEllipses_ReceivePOs._objData;n.set_PathToData(this._strPathToData);n.set_DataObject(this._strDataObject);n.set_DataAction("CountReceivePOs")},countShipSOs:function(){var n=this._ctlEllipses_ShipSOs._objData;n.set_PathToData(this._strPathToData);n.set_DataObject(this._strDataObject);n.set_DataAction("CountShipSOs")},countReceiveCRMAs:function(){var n=this._ctlEllipses_ReceiveCRMAs._objData;n.set_PathToData(this._strPathToData);n.set_DataObject(this._strDataObject);n.set_DataAction("CountReceiveCRMAs")},countShipSRMAs:function(){var n=this._ctlEllipses_ShipSRMAs._objData;n.set_PathToData(this._strPathToData);n.set_DataObject(this._strDataObject);n.set_DataAction("CountShipSRMAs")},countStock:function(){var n=this._ctlEllipses_AllStock._objData;n.set_PathToData(this._strPathToData);n.set_DataObject(this._strDataObject);n.set_DataAction("CountStock")},countServices:function(){var n=this._ctlEllipses_Services._objData;n.set_PathToData(this._strPathToData);n.set_DataObject(this._strDataObject);n.set_DataAction("CountServices")},countLots:function(){var n=this._ctlEllipses_Lots._objData;n.set_PathToData(this._strPathToData);n.set_DataObject(this._strDataObject);n.set_DataAction("CountLots")},countGoodsIn:function(){var n=this._ctlEllipses_GoodsIn._objData;n.set_PathToData(this._strPathToData);n.set_DataObject(this._strDataObject);n.set_DataAction("CountGIs")},goReceivePO:function(){if(!(this._txtReceivePO.value.trim().length<1)){var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData(this._strPathToData_Go);n.set_DataObject(this._strDataObject_Go);n.set_DataAction("GetPurchaseOrderID");n.addParameter("No",this._txtReceivePO.value.trim());n.addDataOK(Function.createDelegate(this,this.goReceivePOComplete));$R_DQ.addToQueue(n);$R_DQ.processQueue()}},goReceivePOComplete:function(n){n._result.ID&&(location.href=$RGT_gotoURL_ReceivePurchaseOrder(n._result.ID))},goShipSO:function(){if(!(this._txtShipSO.value.trim().length<1)){var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData(this._strPathToData_Go);n.set_DataObject(this._strDataObject_Go);n.set_DataAction("GetSalesOrderID");n.addParameter("No",this._txtShipSO.value.trim());n.addDataOK(Function.createDelegate(this,this.goShipSOComplete));$R_DQ.addToQueue(n);$R_DQ.processQueue()}},goShipSOComplete:function(n){n._result.ID&&(location.href=$RGT_gotoURL_ShipSalesOrder(n._result.ID))},goReceiveCRMA:function(){if(!(this._txtReceiveCRMA.value.trim().length<1)){var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData(this._strPathToData_Go);n.set_DataObject(this._strDataObject_Go);n.set_DataAction("GetCRMAID");n.addParameter("No",this._txtReceiveCRMA.value.trim());n.addDataOK(Function.createDelegate(this,this.goReceiveCRMAComplete));$R_DQ.addToQueue(n);$R_DQ.processQueue()}},goReceiveCRMAComplete:function(n){n._result.ID&&(location.href=$RGT_gotoURL_ReceiveCRMA(n._result.ID))},goShipSRMA:function(){if(!(this._txtShipSRMA.value.trim().length<1)){var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData(this._strPathToData_Go);n.set_DataObject(this._strDataObject_Go);n.set_DataAction("GetSRMAID");n.addParameter("No",this._txtShipSRMA.value.trim());n.addDataOK(Function.createDelegate(this,this.goShipSRMAComplete));$R_DQ.addToQueue(n);$R_DQ.processQueue()}},goShipSRMAComplete:function(n){n._result.ID&&(location.href=$RGT_gotoURL_ShipSRMA(n._result.ID))},goGoodsIn:function(){if(!(this._txtGoodsIn.value.trim().length<1)){var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData(this._strPathToData_Go);n.set_DataObject(this._strDataObject_Go);n.set_DataAction("GetGoodsInID");n.addParameter("No",this._txtGoodsIn.value.trim());n.addDataOK(Function.createDelegate(this,this.goGoodsInComplete));$R_DQ.addToQueue(n);$R_DQ.processQueue()}},goGoodsInComplete:function(n){n._result.ID&&(location.href=$RGT_gotoURL_GoodsIn(n._result.ID))}};Rebound.GlobalTrader.Site.Pages.Warehouse.Warehouse.registerClass("Rebound.GlobalTrader.Site.Pages.Warehouse.Warehouse",Rebound.GlobalTrader.Site.Pages.Content,Sys.IDisposable);