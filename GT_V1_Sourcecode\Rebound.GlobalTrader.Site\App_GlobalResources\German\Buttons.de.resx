<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Add" xml:space="preserve">
    <value>Hinzufügen</value>
  </data>
  <data name="AddAlternate" xml:space="preserve">
    <value>Addieren Sie Alternative</value>
  </data>
  <data name="AddContact" xml:space="preserve">
    <value>Addieren Sie Kontakt</value>
  </data>
  <data name="AddFolder" xml:space="preserve">
    <value>Neues Faltblatt</value>
  </data>
  <data name="AddNew" xml:space="preserve">
    <value>Addieren Sie Neues</value>
  </data>
  <data name="AddScheduledCall" xml:space="preserve">
    <value>Addieren Sie Zeitlich Geplanten Anruf</value>
  </data>
  <data name="AddSuggestion" xml:space="preserve">
    <value>Addieren Sie Vorschlag</value>
  </data>
  <data name="AddToRequirement" xml:space="preserve">
    <value>Fügen Sie Anforderung Hinzu</value>
  </data>
  <data name="AllCompanies" xml:space="preserve">
    <value>Alle Firmen</value>
  </data>
  <data name="Allocate" xml:space="preserve">
    <value>Teilen Sie zu</value>
  </data>
  <data name="Apply" xml:space="preserve">
    <value>Treffen Sie Zu</value>
  </data>
  <data name="Authorise" xml:space="preserve">
    <value>Autorisieren Sie</value>
  </data>
  <data name="Browse" xml:space="preserve">
    <value>Grasen Sie</value>
  </data>
  <data name="Calculate" xml:space="preserve">
    <value>Berechnen Sie</value>
  </data>
  <data name="Cancel" xml:space="preserve">
    <value>Abbrechen</value>
  </data>
  <data name="Cease" xml:space="preserve">
    <value>Hören Sie auf</value>
  </data>
  <data name="ChangeCompany" xml:space="preserve">
    <value>Umlegen auf Geschäftshaus</value>
  </data>
  <data name="ChangePassword" xml:space="preserve">
    <value>Ändern Sie Kennwort</value>
  </data>
  <data name="Close" xml:space="preserve">
    <value>Abschluss</value>
  </data>
  <data name="CollapseAll" xml:space="preserve">
    <value>Stürzen alle ein</value>
  </data>
  <data name="Company" xml:space="preserve">
    <value>Geschäftshaus</value>
  </data>
  <data name="CompanyContacts" xml:space="preserve">
    <value>Kontakte</value>
  </data>
  <data name="Contacts" xml:space="preserve">
    <value>Anprechpartner/innen</value>
  </data>
  <data name="Continue" xml:space="preserve">
    <value>Fahren Sie fort</value>
  </data>
  <data name="CreatePDF" xml:space="preserve">
    <value>Stellen Sie PDF her</value>
  </data>
  <data name="CreatePurchaseOrder" xml:space="preserve">
    <value>Stellen Sie POF her</value>
  </data>
  <data name="CreateSalesOrder" xml:space="preserve">
    <value>Stellen Sie SO her</value>
  </data>
  <data name="Customers" xml:space="preserve">
    <value>Kunden</value>
  </data>
  <data name="Data" xml:space="preserve">
    <value>Daten</value>
  </data>
  <data name="Deallocate" xml:space="preserve">
    <value>Zuordnung Aufheben</value>
  </data>
  <data name="Deauthorise" xml:space="preserve">
    <value>Unautorisiert</value>
  </data>
  <data name="Default" xml:space="preserve">
    <value>Bilden Sie Rückstellung</value>
  </data>
  <data name="DefaultBilling" xml:space="preserve">
    <value>Bilden Sie Rückstellungs-Gebührenzählung</value>
  </data>
  <data name="DefaultShipping" xml:space="preserve">
    <value>Bilden Sie Rückstellungs-Verschiffen</value>
  </data>
  <data name="Delete" xml:space="preserve">
    <value>Löschen</value>
  </data>
  <data name="DeleteAlternate" xml:space="preserve">
    <value>Löschung-Alternative</value>
  </data>
  <data name="DeleteFolder" xml:space="preserve">
    <value>Löschung-Faltblatt</value>
  </data>
  <data name="DeleteMessage" xml:space="preserve">
    <value>Löschung-Mitteilungen</value>
  </data>
  <data name="DeleteReceipt" xml:space="preserve">
    <value>Löschung-Empfang</value>
  </data>
  <data name="DeleteUnallocatedService" xml:space="preserve">
    <value>Löschung nicht zugewiesener Service</value>
  </data>
  <data name="DeleteUnallocatedServices" xml:space="preserve">
    <value>Löschung nicht zugewiesene Dienstleistungen</value>
  </data>
  <data name="DeleteUnallocatedStock" xml:space="preserve">
    <value>Löschung nicht zugewiesener Vorrat</value>
  </data>
  <data name="Deselect" xml:space="preserve">
    <value>Wählen Sie ab</value>
  </data>
  <data name="Disable" xml:space="preserve">
    <value>Sperrung</value>
  </data>
  <data name="Dismiss" xml:space="preserve">
    <value>Entlassen Sie</value>
  </data>
  <data name="Edit" xml:space="preserve">
    <value>Bearbeiten</value>
  </data>
  <data name="EditCurrentRates" xml:space="preserve">
    <value>Redigieren Sie Tageskurse</value>
  </data>
  <data name="EditFolder" xml:space="preserve">
    <value>Redigieren Sie Faltblatt</value>
  </data>
  <data name="EditMembers" xml:space="preserve">
    <value>Redigieren Sie Mitglieder</value>
  </data>
  <data name="EditOffer" xml:space="preserve">
    <value>Redigieren Sie Angebot</value>
  </data>
  <data name="EditReceipt" xml:space="preserve">
    <value>Redigieren Sie den Empfang</value>
  </data>
  <data name="Enable" xml:space="preserve">
    <value>Ermöglichen Sie</value>
  </data>
  <data name="ExpandAll" xml:space="preserve">
    <value>Erweitern Sie alle</value>
  </data>
  <data name="ExportToCSV" xml:space="preserve">
    <value>Export zu CSV</value>
  </data>
  <data name="Forward" xml:space="preserve">
    <value>Vorwärts</value>
  </data>
  <data name="GetCounts" xml:space="preserve">
    <value>Erhalten Sie Zählimpulse</value>
  </data>
  <data name="GetData" xml:space="preserve">
    <value>Erhalten Sie Daten</value>
  </data>
  <data name="Go" xml:space="preserve">
    <value>Gehe</value>
  </data>
  <data name="Help" xml:space="preserve">
    <value>Hilfe</value>
  </data>
  <data name="Home" xml:space="preserve">
    <value>Startseite</value>
  </data>
  <data name="Inspect" xml:space="preserve">
    <value>Kontrollieren Sie</value>
  </data>
  <data name="Login" xml:space="preserve">
    <value>Einloggen</value>
  </data>
  <data name="Logout" xml:space="preserve">
    <value>Ausloggen</value>
  </data>
  <data name="MakeAvailable" xml:space="preserve">
    <value>Freigabe</value>
  </data>
  <data name="MakeDefaultBilling" xml:space="preserve">
    <value>Bilden Sie Rückstellungs-Gebührenzählung</value>
  </data>
  <data name="MakeDefaultPO" xml:space="preserve">
    <value>Bilden Sie Rückstellung für POs</value>
  </data>
  <data name="MakeDefaultShipping" xml:space="preserve">
    <value>Bilden Sie Rückstellungs-Verschiffen</value>
  </data>
  <data name="MakeDefaultSO" xml:space="preserve">
    <value>Bilden Sie Rückstellung für Sos</value>
  </data>
  <data name="Manufacturers" xml:space="preserve">
    <value>Hersteller</value>
  </data>
  <data name="MarkAsToDo" xml:space="preserve">
    <value>Verursachen Sie, um Einzelteil zu tun</value>
  </data>
  <data name="MarkComplete" xml:space="preserve">
    <value>Markieren Sie komplettes</value>
  </data>
  <data name="MarkIncomplete" xml:space="preserve">
    <value>Markieren Sie unvollständiges</value>
  </data>
  <data name="MoveMessage" xml:space="preserve">
    <value>Verschieben Sie Mitteilungen</value>
  </data>
  <data name="New" xml:space="preserve">
    <value>Neu</value>
  </data>
  <data name="NewFolder" xml:space="preserve">
    <value>Neues Faltblatt</value>
  </data>
  <data name="NewMessage" xml:space="preserve">
    <value>Neue Mitteilung</value>
  </data>
  <data name="No" xml:space="preserve">
    <value>Nein</value>
  </data>
  <data name="Notify" xml:space="preserve">
    <value>Teilen Sie mit</value>
  </data>
  <data name="Off" xml:space="preserve">
    <value>Weg von</value>
  </data>
  <data name="OK" xml:space="preserve">
    <value>OK</value>
  </data>
  <data name="OpenItem" xml:space="preserve">
    <value>Öffnen Sie Einzelteil</value>
  </data>
  <data name="Orders" xml:space="preserve">
    <value>Aufträge</value>
  </data>
  <data name="Post" xml:space="preserve">
    <value>Pfosten</value>
  </data>
  <data name="PostAll" xml:space="preserve">
    <value>Geben Sie alle bekannt</value>
  </data>
  <data name="Print" xml:space="preserve">
    <value>Druck</value>
  </data>
  <data name="PrintAll" xml:space="preserve">
    <value>Drucken Sie alle</value>
  </data>
  <data name="PrintProForma" xml:space="preserve">
    <value>Pro-Forma</value>
  </data>
  <data name="Prospects" xml:space="preserve">
    <value>Aussichten</value>
  </data>
  <data name="Quarantine" xml:space="preserve">
    <value>Quarantäne</value>
  </data>
  <data name="QuickAdd" xml:space="preserve">
    <value>Schnell Tragen Bei</value>
  </data>
  <data name="Quote" xml:space="preserve">
    <value>Preisangabe</value>
  </data>
  <data name="Receive" xml:space="preserve">
    <value>Empfangen Sie</value>
  </data>
  <data name="ReceiveSelected" xml:space="preserve">
    <value>Empfangen Sie vorgewählt</value>
  </data>
  <data name="ReplaceImage" xml:space="preserve">
    <value>Ersetzen Sie Bild</value>
  </data>
  <data name="Reply" xml:space="preserve">
    <value>Antwort</value>
  </data>
  <data name="Report" xml:space="preserve">
    <value>Report</value>
  </data>
  <data name="RequestForQuote" xml:space="preserve">
    <value>Ersuchen um Anführungsstrich</value>
  </data>
  <data name="Reset" xml:space="preserve">
    <value>Zurücksetzen</value>
  </data>
  <data name="ResetPassword" xml:space="preserve">
    <value>Stellen Sie Kennwort zurück</value>
  </data>
  <data name="RunReport" xml:space="preserve">
    <value>Lassen Sie Report laufen</value>
  </data>
  <data name="Save" xml:space="preserve">
    <value>Speichern</value>
  </data>
  <data name="SaveAsDefault" xml:space="preserve">
    <value>Außer als Rückstellung</value>
  </data>
  <data name="Search" xml:space="preserve">
    <value>Suche</value>
  </data>
  <data name="Select" xml:space="preserve">
    <value>Auserwählt</value>
  </data>
  <data name="SelectAllAvailableForShipping" xml:space="preserve">
    <value>Wählen Sie alle vor, die für Verschiffen vorhanden sind</value>
  </data>
  <data name="Send" xml:space="preserve">
    <value>Senden Sie</value>
  </data>
  <data name="Ship" xml:space="preserve">
    <value>Ship</value>
  </data>
  <data name="ShipAll" xml:space="preserve">
    <value>Versenden Sie alle</value>
  </data>
  <data name="ShipSelected" xml:space="preserve">
    <value>Schiff vorgewählt</value>
  </data>
  <data name="Show" xml:space="preserve">
    <value>Anzeigen</value>
  </data>
  <data name="Snooze" xml:space="preserve">
    <value>Snooze</value>
  </data>
  <data name="Source" xml:space="preserve">
    <value>Quelle</value>
  </data>
  <data name="Split" xml:space="preserve">
    <value>Spalte</value>
  </data>
  <data name="Suppliers" xml:space="preserve">
    <value>Lieferanten</value>
  </data>
  <data name="Transactions" xml:space="preserve">
    <value>Verhandlungen</value>
  </data>
  <data name="TransferService" xml:space="preserve">
    <value>Übergangsservice</value>
  </data>
  <data name="TransferServicesToDifferentLot" xml:space="preserve">
    <value>Übergangsservices zum unterschiedlichen Los</value>
  </data>
  <data name="TransferStock" xml:space="preserve">
    <value>Übergangsvorrat</value>
  </data>
  <data name="TransferStockToDifferentLot" xml:space="preserve">
    <value>Übergangsvorrat zum unterschiedlichen Los</value>
  </data>
  <data name="Unpost" xml:space="preserve">
    <value>Unposten</value>
  </data>
  <data name="UnpostAll" xml:space="preserve">
    <value>Unpost alles</value>
  </data>
  <data name="Update" xml:space="preserve">
    <value>Aktualisierung</value>
  </data>
  <data name="ViewPDF" xml:space="preserve">
    <value>Ansicht speicherte pdf</value>
  </data>
  <data name="Yes" xml:space="preserve">
    <value>Ja</value>
  </data>
</root>