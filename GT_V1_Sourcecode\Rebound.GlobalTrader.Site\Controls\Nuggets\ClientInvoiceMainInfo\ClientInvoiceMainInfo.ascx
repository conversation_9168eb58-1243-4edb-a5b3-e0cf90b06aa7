<%--
Marker     Created by      Date               Remarks
[005]      Prakash           11/04/2014         Add Client Invoice
--%>
<%@ Control Language="C#" CodeBehind="ClientInvoiceMainInfo.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Nuggets.ClientInvoiceMainInfo" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>

<ReboundUI_Nugget:DesignBase ID="ctlDB" runat="server" TitleText="Main Information" BoxType="Standard">
	<Links>
		<ReboundUI:IconButton ID="ibtnEdit" runat="server" IconButtonMode="hyperlink" IconGroup="Nugget" IconTitleResource="Edit" IconCSSType="Edit"  />
		<%--<ReboundUI:IconButton ID="ibtnNotify" runat="server" IconButtonMode="hyperlink" IconGroup="Nugget" IconTitleResource="Notify" IsInitiallyEnabled="false" />--%>
		<ReboundUI:IconButton ID="ibtnNotify" runat="server" IconButtonMode="hyperlink" IconGroup="Nugget" IconTitleResource="Notify" style="display:none" />
	</Links>
	<Content>
					<table class="twoCols">
						<tr>
							<td class="col1">
								<table cellpadding="0" cellspacing="0" border="0" class="dataItems">
									<ReboundUI:DataItemRow id="ctlClientInvoice" runat="server" ResourceTitle="ClientInvoice" />
									<ReboundUI:DataItemRow id="ctlSupplierCode" runat="server" ResourceTitle="SupplierCode" FieldType="Hidden"/>
									<ReboundUI:DataItemRow id="ctlInvoiceDate" runat="server" ResourceTitle="InvoiceDate" />
									<ReboundUI:DataItemRow id="ctlCurrency" runat="server" ResourceTitle="Currency" />
									<ReboundUI:DataItemRow id="ctlInvoiceAmount" runat="server" ResourceTitle="InvoiceAmount" />
									<ReboundUI:DataItemRow id="ctlGoodsValue" runat="server" ResourceTitle="GoodsValue" />
									<ReboundUI:DataItemRow id="ctlTax" runat="server" ResourceTitle="Tax" />
									<ReboundUI:DataItemRow id="ctlTaxName" runat="server" ResourceTitle="Tax" />
									<ReboundUI:DataItemRow id="ctlPOOrder" runat="server" ResourceTitle="PurchaseOrder" FieldType="Hidden" />
									 <ReboundUI:DataItemRow id="ctlInternalPurchaseOrder" runat="server" ResourceTitle="InternalPurchaseOrderNo" FieldType="Hidden"/>
								</table>
							</td>
							<td class="col2">
								<table cellpadding="0" cellspacing="0" border="0" class="dataItems">
									<ReboundUI:DataItemRow id="ctlDeliveryCharge" runat="server" ResourceTitle="DeliveryCharge" />
									<ReboundUI:DataItemRow id="ctlBankFee" runat="server" ResourceTitle="BankFee" />
									<ReboundUI:DataItemRow id="ctlCreditCardFee" runat="server" ResourceTitle="CreditCardFee" />
									<ReboundUI:DataItemRow id="ctlNotes" runat="server" ResourceTitle="Notes" />
				<%--					<ReboundUI:DataItemRow id="ctlExported" runat="server" FieldType="CheckBox" ResourceTitle="Exported"/>
									<ReboundUI:DataItemRow id="ctlCanBeExported" runat="server" FieldType="CheckBox" ResourceTitle="CanBeExported"/>
									<ReboundUI:DataItemRow id="ctlURNNumber" runat="server" ResourceTitle="URNNumber" />--%>
									<ReboundUI:DataItemRow id="ctlSecondRef" runat="server" ResourceTitle="SecondRef" />
									<ReboundUI:DataItemRow id="ctlNarrative" runat="server" ResourceTitle="Narrative" />
									
									<ReboundUI:DataItemRow id="hidSupplier" runat="server" FieldType="Hidden" />
									<ReboundUI:DataItemRow id="hidCompnayNo" runat="server" FieldType="Hidden" />
									<ReboundUI:DataItemRow id="hidCurrency" runat="server" FieldType="Hidden" />
									<ReboundUI:DataItemRow id="hidInvoiceAmount" runat="server" FieldType="Hidden" />
									<ReboundUI:DataItemRow id="hidGoodsInValue" runat="server" FieldType="Hidden" />
									<ReboundUI:DataItemRow id="hidTax" runat="server" FieldType="Hidden" />
									<ReboundUI:DataItemRow id="hidDeliveryCharges" runat="server" FieldType="Hidden" />
									<ReboundUI:DataItemRow id="hidBankFee" runat="server" FieldType="Hidden" />
									<ReboundUI:DataItemRow id="hidCreditCardFee" runat="server" FieldType="Hidden" />
									<ReboundUI:DataItemRow id="hidTaxId" runat="server" FieldType="Hidden" />
                                    <ReboundUI:DataItemRow id="DataItemRow1" runat="server" FieldType="Hidden" />
									
								</table>
							</td>
						</tr>
					</table>
				
	</Content>
	
	<Forms>
		<ReboundForm:ClientInvoiceMainInfo_Edit ID="ctlEdit" runat="server" />
		<ReboundForm:ClientInvoiceMainInfo_Notify ID="ctlNotify" runat="server" />
	</Forms>

</ReboundUI_Nugget:DesignBase>
