﻿/*
Marker     Changed by               Date         Remarks
[001]      A<PERSON><PERSON><PERSON>           29/07/2021   Implement a new dropdown for supplier Approval Status
*/
using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;

namespace Rebound.GlobalTrader.Site.Controls.DropDowns
{
    public class SupplierApprovalStatus : Base
    {
        protected override void OnLoad(EventArgs e)
        {
            SetDropDownType("SupplierApprovalStatus");
            AddScriptReference("Controls.DropDowns.SupplierApprovalStatus.SupplierApprovalStatus");
            _scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.DropDowns.SupplierApprovalStatus", ClientID);
            base.OnLoad(e);
        }
    }
}