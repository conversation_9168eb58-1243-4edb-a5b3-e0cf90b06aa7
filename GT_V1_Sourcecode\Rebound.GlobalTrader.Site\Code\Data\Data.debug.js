///<reference name="MicrosoftAjax.js" />
///<reference path="~/Common/Functions/Functions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - dispose everything fully
//
// RP 18.10.2009:
// - add reset function
//
// RP 14.10.2009:
// - allow data calls to be cancelled
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site");

Rebound.GlobalTrader.Site.DataQueueManager = function() { 
	this._aryDataCalls = [];
	this._aryLowPriorityDataCalls = [];
	this._intAllowedSimultaneousCalls = 3;
	this._intCurrentlyActiveCalls = 0;
	this._intTimeBetweenCalls = 25; //milliseconds
	this._intTimeToWaitForLowPriorityCalls = 25; //milliseconds
    this._intTimeoutID = null;
};

Rebound.GlobalTrader.Site.DataQueueManager.prototype = {

	get_aryDataCalls: function() { return this._aryDataCalls; }, 	set_aryDataCalls: function(value) { if (this._aryDataCalls !== value)  this._aryDataCalls = value; }, 
	get_intAllowedSimultaneousCalls: function() { return this._intAllowedSimultaneousCalls; }, 	set_intAllowedSimultaneousCalls: function(value) { if (this._intAllowedSimultaneousCalls !== value)  this._intAllowedSimultaneousCalls = value; }, 
	get_intCurrentlyActiveCalls: function() { return this._intCurrentlyActiveCalls; }, 	set_intCurrentlyActiveCalls: function(value) { if (this._intCurrentlyActiveCalls !== value)  this._intCurrentlyActiveCalls = value; }, 
		
	initialize: function() {
		Rebound.GlobalTrader.Site.DataQueueManager.callBaseMethod(this, "initialize");
	},

	dispose: function() {
		if (this.isDisposed) return;
		if (this._aryDataCalls) this.clearAllDataCalls();
		this._aryDataCalls = null;
		this._aryLowPriorityDataCalls = null;
		this._intAllowedSimultaneousCalls = null;
		this._intCurrentlyActiveCalls = null;
		this._intTimeBetweenCalls = null; 
		this._intTimeToWaitForLowPriorityCalls = null; 
		this._intTimeoutID = null;
		Rebound.GlobalTrader.Site.DataQueueManager.callBaseMethod(this, "dispose");
		this.isDisposed = true;
	},
	
	addToQueue: function(obj, blnLowPriority) {
		//Sys.Debug.trace("addToQueue");
		//Sys.Debug.trace(String.format("Action: {0}", obj._dataAction));
		//Sys.Debug.trace(String.format("Calls: {0}", this._aryDataCalls.length));
		//Sys.Debug.trace(String.format("Active: {0}", this._intCurrentlyActiveCalls));
		//Sys.Debug.trace("---");
		obj.addDataOK(Function.createDelegate(this, this.callFinished));
		obj.addTimeout(Function.createDelegate(this, this.callFinished));
		obj.addError(Function.createDelegate(this, this.callFinished));
		obj.addCancelled(Function.createDelegate(this, this.callFinished));
		obj._intQueueIndex = this._aryLowPriorityDataCalls.length + this._aryDataCalls.length;
		if (blnLowPriority) {
			Array.add(this._aryLowPriorityDataCalls, obj);
		} else {
			Array.add(this._aryDataCalls, obj);
		}
	},
	
	processQueue: function() {
		//wait to process queue if we only have low priority calls in the queue - allows high priority ones to come first
		if (this._aryLowPriorityDataCalls.length > 0 && this._aryDataCalls.length == 0) {
			this.waitBeforeProcessingQueue();
		} else {
			this.doNextCall();
		}
	},
	
	doNextCall: function() {
		//Sys.Debug.trace("doNextCall");
		//Sys.Debug.trace(String.format("Calls: {0}", this._aryDataCalls.length));
		//Sys.Debug.trace(String.format("Active: {0}", this._intCurrentlyActiveCalls));
		//Sys.Debug.trace("---");
		var obj;
		clearTimeout(this._intTimeoutID);
		if (this._aryDataCalls.length == 0 && this._aryLowPriorityDataCalls.length == 0) return;
		if (this._intCurrentlyActiveCalls >= this._intAllowedSimultaneousCalls) return;
		if (this._aryDataCalls.length > 0) {
			obj = this._aryDataCalls[0];
			Array.removeAt(this._aryDataCalls, 0);
			obj.getData();
			this._intCurrentlyActiveCalls += 1;
			return;
		}
		if (this._aryLowPriorityDataCalls.length > 0) {
			//allow high priority calls to come first
			if (this._aryDataCalls.length == 0) {
				obj = this._aryLowPriorityDataCalls[0];
				Array.removeAt(this._aryLowPriorityDataCalls, 0);
				obj.getData();
				this._intCurrentlyActiveCalls += 1;
			}
			return;
		}
	},

	callFinished: function(obj) {
		if (obj) this._intCurrentlyActiveCalls -= 1;
		//Sys.Debug.trace("callFinished");
		//Sys.Debug.trace(String.format("Action: {0}", obj._dataAction));
		//Sys.Debug.trace(String.format("Calls: {0}", this._aryDataCalls.length));
		//Sys.Debug.trace(String.format("Active: {0}", this._intCurrentlyActiveCalls));
		//Sys.Debug.trace("---");
		if (this._aryDataCalls.length > 0 || this._aryLowPriorityDataCalls.length > 0) this.doNextCall();
	},
	
	waitBeforeProcessingQueue: function() {
		clearTimeout(this._intTimeoutID);
		this._intTimeoutID = setTimeout(Function.createDelegate(this, this.doNextCall), this._intTimeToWaitForLowPriorityCalls);
	},
	
	clearAllDataCalls: function() {
		if (this._aryDataCalls) {
			for (var i = 0, l = this._aryDataCalls.length; i < l; i++) {
				var obj = this._aryDataCalls[i];
				if (obj) obj.dispose();
				obj = null;
			}
			Array.clear(this._aryDataCalls);
		}
		if (this._aryLowPriorityDataCalls) {
			for (i = 0, l = this._aryLowPriorityDataCalls.length; i < l; i++) {
				obj = this._aryLowPriorityDataCalls[i];
				if (obj) obj.dispose();
				obj = null;
			}
			Array.clear(this._aryLowPriorityDataCalls);
		}
	}
	
};

Rebound.GlobalTrader.Site.DataQueueManager.registerClass("Rebound.GlobalTrader.Site.DataQueueManager", Sys.Component, Sys.IDisposable);

//--------------------------------------------------------------------------------------------
Rebound.GlobalTrader.Site.DataEventArgs = function(strStatusCode, strErrorMessage, objResult, strURL) { 
	Rebound.GlobalTrader.Site.DataEventArgs.initializeBase(this);
	this._statusCode = strStatusCode;
	this._errorMessage = strErrorMessage;
	this._result = objResult;
	this._url = strURL;
};

Rebound.GlobalTrader.Site.DataEventArgs.prototype = {

	get_ErrorMessage: function() { return this._errorMessage; }, 	set_ErrorMessage: function(value) { if (this._errorMessage !== value)  this._errorMessage = value; }, 
	get_StatusCode: function() { return this._statusCode; }, 	set_StatusCode: function(value) { if (this._statusCode !== value)  this._statusCode = value; }, 
	get_Result: function() { return this._result; }, 	set_Result: function(value) { if (this._result !== value)  this._result = value; }, 

	initialize: function() {
		Rebound.GlobalTrader.Site.Data.callBaseMethod(this, "initialize");
	}
	
};
Rebound.GlobalTrader.Site.DataEventArgs.registerClass("Rebound.GlobalTrader.Site.DataEventArgs", Sys.EventArgs);

//--------------------------------------------------------------------------------------------
Rebound.GlobalTrader.Site.Data = function() { 
	Rebound.GlobalTrader.Site.Data.initializeBase(this);
	this._dataObject = null;
	this._dataAction = null;
	this._dataParams = [];
	this._result = null;
	this._pathToData = "";
	this._errorMessage = "";
	this._statusCode = 0;
	this._result = null;
	this._url = "";
	this._blnReturnSerialized = false;
	this._intTimeoutMilliseconds = -1;
	this._wReq;
};

Rebound.GlobalTrader.Site.Data.prototype = {

	get_DataObject: function() { return this._dataObject; }, set_DataObject: function(value) { if (this._dataObject !== value) this._dataObject = value; },
	get_DataAction: function() { return this._dataAction; }, set_DataAction: function(value) { if (this._dataAction !== value) this._dataAction = value; },
	get_DataParams: function() { return this._dataParams; }, set_DataParams: function(value) { if (this._dataParams !== value) this._dataParams = value; },
	get_Result: function() { return this._result; }, set_Result: function(value) { if (this._result !== value) this._result = value; },
	get_PathToData: function() { return this._pathToData; }, set_PathToData: function(value) { if (this._pathToData !== value) this._pathToData = value; },
	get_StatusCode: function() { return this._statusCode; }, 	set_StatusCode: function(value) { if (this._statusCode !== value)  this._statusCode = value; }, 
	get_Result: function() { return this._result; }, 	set_Result: function(value) { if (this._result !== value)  this._result = value; }, 
	get_ErrorMessage: function() { return this._errorMessage; }, 	set_ErrorMessage: function(value) { if (this._errorMessage !== value)  this._errorMessage = value; }, 
	get_blnReturnSerialized: function() { return this._blnReturnSerialized; }, 	set_blnReturnSerialized: function(value) { if (this._blnReturnSerialized !== value)  this._blnReturnSerialized = value; }, 
	get_intTimeoutMilliseconds: function() { return this._intTimeoutMilliseconds; }, 	set_intTimeoutMilliseconds: function(value) { if (this._intTimeoutMilliseconds !== value)  this._intTimeoutMilliseconds = value; }, 

	addDataOK: function(handler) { this.get_events().addHandler("DataOK", handler); },
	removeDataOK: function(handler) { this.get_events().removeHandler("DataOK", handler); },
	onDataOK: function() {
		var handler = this.get_events().getHandler("DataOK");
		if (handler) handler(this, new Rebound.GlobalTrader.Site.DataEventArgs(this._statusCode, this._errorMessage, this._result, this._url));
	},

	addError: function(handler) { this.get_events().addHandler("Error", handler); },
	removeError: function(handler) { this.get_events().removeHandler("Error", handler); },
	onError: function() {
		var handler = this.get_events().getHandler("Error");
		if (handler) handler(this, new Rebound.GlobalTrader.Site.DataEventArgs(this._statusCode, this._errorMessage, this._result, this._url));
	},

	addTimeout: function(handler) { this.get_events().addHandler("Timeout", handler); },
	removeTimeout: function(handler) { this.get_events().removeHandler("Timeout", handler); },
	onTimeout: function() {
		var handler = this.get_events().getHandler("Timeout");
		if (!handler) handler = this.get_events().getHandler("Error");
		if (handler) handler(this, new Rebound.GlobalTrader.Site.DataEventArgs(this._statusCode, this._errorMessage, this._result, this._url));
	},

	addCancelled: function(handler) { this.get_events().addHandler("Cancelled", handler); },
	removeCancelled: function(handler) { this.get_events().removeHandler("Cancelled", handler); },
	onCancelled: function() {
		var handler = this.get_events().getHandler("Cancelled");
		if (handler) handler(this, new Rebound.GlobalTrader.Site.DataEventArgs(this._statusCode, this._errorMessage, this._result, this._url));
	},
	
	initialize: function() {
		Rebound.GlobalTrader.Site.Data.callBaseMethod(this, "initialize");
	},

	dispose: function() { 
		if (this.isDisposed) return;
		this.cancel();
		this._dataObject = null;
		this._dataAction = null;
		this._dataParams = null;
		this._result = null;
		this._pathToData = null;
		this._errorMessage = null;
		this._result = null;
		this._url = null;
		this._wReq = null;
		Rebound.GlobalTrader.Site.Data.callBaseMethod(this, "dispose");
		this.isDisposed = true;
	},

	addParameter: function(strKey, strValue) {
		if (!strKey) return; 
		if (!strValue) return;
		Array.add(this._dataParams, {Name:strKey, Value:$R_FN.getCleanTextValue(strValue)});
	},

	clearParameters: function() {
		Array.clear(this._dataParams);
	},
	
	getData: function() {
		var prm, i;
		this._url = String.format("{0}/{1}.ashx", this._pathToData, this._dataObject);
		if (!this._wReq) this._wReq = new Sys.Net.WebRequest();
		this._wReq.set_url(this._url);
		this._wReq.set_httpVerb("POST");
		if (this._intTimeoutMilliseconds == -1) this._intTimeoutMilliseconds = $R_GENERIC_DATABASE_TIMEOUT * 1000;
		this._wReq.set_timeout(this._intTimeoutMilliseconds);
		var body = "";
		body = String.format("action={0}", this._dataAction);
		
		if (this._dataParams != null) {
			for (i = 0; i < this._dataParams.length; i++) {
				prm = this._dataParams[i];
				body += String.format("&{0}={1}", prm.Name, prm.Value);
				prm = null;
			}
		}
        //Espire: 1st Nov 2017: for checking current user and current client
		body += String.format("&{0}={1}", "cLoginUser", $R_LOGGIN_USER);
		this._wReq.set_body(body);
		this._wReq.add_completed(Function.createDelegate(this, this.getDataComplete));
		this._wReq.invoke();
	},
		
	getDataComplete: function(executor, eventArgs) {
		if (executor.get_timedOut()) {
			this._errorMessage = $R_RES.DatabaseTimeout;
			this.onTimeout();
			return;
		}
		if (executor.get_aborted()) {
			this.onCancelled();
			return;
		}
		if (executor.get_responseAvailable()) {
			if (executor.get_statusCode() != 200) {
				this._statusCode = executor.get_statusCode();
				this._errorMessage = executor.get_statusText();
				this._result = executor.get_responseData();
				this.onError();
				return;
			}
			this._result = executor.get_responseData();
			if (this._result.startsWith('{"Error":') || this._result.startsWith('{Error:')) {
				this._result = Sys.Serialization.JavaScriptSerializer.deserialize(executor.get_responseData());
				this._errorMessage = this._result.Message;
				if (this._errorMessage == "LOGGED_OUT") {
					location.href = $R_URL_Logout;
				} else {
					this.onError();
				}
			} else {
				if (!this._blnReturnSerialized) {
					if (this._result.length == 0) {
						this._result = null;
					} else {
						try {
							this._result = Sys.Serialization.JavaScriptSerializer.deserialize(executor.get_responseData());
						} catch (e) {
							this._errorMessage = "Javascript deserialization error:<br /><br />" + executor.get_responseData();
							this._result = {Error: true};
							this.onError();
							return;
						}
					}
				}
				this.onDataOK();
			}
			return;
		}
	},
	
	cancel: function() {
		if (!this._wReq) return;
		this._wReq.remove_completed(Function.createDelegate(this, this.getDataComplete)); 
		var exec = this._wReq.get_executor(); 
		if (!exec) return;
		exec.abort();
		exec = null;
	},
	
	reset: function() {
		this.cancel();
		this._events = new Sys.EventHandlerList();
		Array.clear(this._dataParams);
	}
	
};
Rebound.GlobalTrader.Site.Data.registerClass("Rebound.GlobalTrader.Site.Data", Sys.Component, Sys.IDisposable);
