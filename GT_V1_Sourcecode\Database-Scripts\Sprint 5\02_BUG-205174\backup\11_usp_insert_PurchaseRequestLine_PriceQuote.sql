﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

IF OBJECT_ID('dbo.usp_insert_PurchaseRequestLine_PriceQuote', 'P') IS NOT NULL
	DROP PROCEDURE dbo.usp_insert_PurchaseRequestLine_PriceQuote
GO

CREATE PROCEDURE [dbo].[usp_insert_PurchaseRequestLine_PriceQuote]                
 --******************************************************************************************                                
 --******************************************************************************************                                
 @PurchaseRequestNo INT                
 ,@xmlCustReqNo XML                
 ,@UpdatedBy INT = NULL                
 --, @ClientNo int = NULL                         
 , @Rowaffected int = NULL OUTPUT             
 ,@IsPoHub BIT                
AS                
BEGIN                
 INSERT INTO dbo.tbPurchaseRequestLine (                
  PurchaseRequestNo                
  ,CustomerRequirementNo                
  --, ClientNo                        
  ,BOMNo                
  ,FullPart                
  ,Part                
  ,Price                
  ,Closed                
  ,UpdatedBy                
  ,DLUP                
  )                
 SELECT @PurchaseRequestNo                
  ,cr.CustomerRequirementId                
  -- , @ClientNo                                        
  ,cr.BOMNo                
  ,cr.FullPart                
  ,cr.Part                
  ,0                
  ,0                
  ,@UpdatedBy                
  ,CURRENT_TIMESTAMP                
 FROM dbo.tbCustomerRequirement cr                
 JOIN @xmlCustReqNo.nodes('/ CustomerRequirements / CustomerRequirement') AS [Table]([Column]) ON cr.CustomerRequirementNumber = [Table].[Column].value('CustReqNo [1]', 'int')                
 WHERE [Table].[Column].value('CustReqNo [1]', 'int') NOT IN (                
   SELECT CustomerRequirementNo                
   FROM tbPurchaseRequestLine                
   WHERE PurchaseRequestNo = @PurchaseRequestNo                
   ) and  cr.BOMNo is not null           
   SELECT  @Rowaffected = @@ROWCOUNT                
END   
  
GO


