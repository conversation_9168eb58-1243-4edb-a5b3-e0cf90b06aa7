﻿using Rebound.GlobalTrader.DAL.SQLClient;
using Rebound.GlobalTrader.DAL;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Rebound.GlobalTrader.BLL
{
    public partial class KubMainProductGroup : BizObject
    {
        #region Properties
        public System.String MainPoductGroup { get; set; }
        #endregion
        #region Methods
        public static List<KubMainProductGroup> FetchKubMainProductGroupDetails(System.String PartNo, System.Int32 ClientID)
        {
            List<KubMainProductGroup> lts = new List<KubMainProductGroup>();
            List<KubMainProductGroupDetails> lstkubSearchDetails = new List<KubMainProductGroupDetails>();
            SQLKubProvider objSQLKubProvider = new SQLKubProvider();
            try
            {
                lstkubSearchDetails = objSQLKubProvider.ListKubMainProductGroupDetails(PartNo, ClientID);
                if (lstkubSearchDetails == null)
                {
                    return new List<KubMainProductGroup>();
                }
                else
                {
                    foreach (KubMainProductGroupDetails objDetails in lstkubSearchDetails)
                    {
                        KubMainProductGroup obj = new KubMainProductGroup();
                        obj.MainPoductGroup = objDetails.MainPoductGroup;
                        lts.Add(obj);
                    }
                }
                return lts;
            }
            catch (Exception ex)
            {
                throw ex;
            }
            finally
            {
                lts = null;
                lstkubSearchDetails = null;
            }
        }
        #endregion
    }
}
