Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");Rebound.GlobalTrader.Site.Controls.DropDowns.PreferredWarehouse=function(n){Rebound.GlobalTrader.Site.Controls.DropDowns.PreferredWarehouse.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.DropDowns.PreferredWarehouse.prototype={get_blnIncludeVirtual:function(){return this._blnIncludeVirtual},set_blnIncludeVirtual:function(n){this._blnIncludeVirtual!==n&&(this._blnIncludeVirtual=n)},get_intPOHubClientNo:function(){return this._intPOHubClientNo},set_intPOHubClientNo:function(n){this._intPOHubClientNo!==n&&(this._intPOHubClientNo=n)},get_intGlobalLoginClientNo:function(){return this._intGlobalLoginClientNo},set_intGlobalLoginClientNo:function(n){this._intGlobalLoginClientNo!==n&&(this._intGlobalLoginClientNo=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.DropDowns.PreferredWarehouse.callBaseMethod(this,"initialize");this.addSetupDataCall(Function.createDelegate(this,this.setupDataCall));this.addGetDataOK(Function.createDelegate(this,this.dataCallOK))},dispose:function(){this.isDisposed||(this._blnIncludeVirtual=null,this._intPOHubClientNo=null,this._intGlobalLoginClientNo=null,Rebound.GlobalTrader.Site.Controls.DropDowns.PreferredWarehouse.callBaseMethod(this,"dispose"))},setupDataCall:function(){this._objData.set_PathToData("controls/DropDowns/Warehouse");this._objData.set_DataObject("Warehouse");this._objData.set_DataAction("GetData");this._objData.addParameter("IncludeVirtual",this._blnIncludeVirtual);this._objData.addParameter("POHubClientNo",this._intPOHubClientNo);this._objData.addParameter("GlobalLoginClientNo",this._intGlobalLoginClientNo)},dataCallOK:function(){var t=this._objData._result,n;if(t.Warehouses)for(n=0;n<t.Warehouses.length;n++)this.addOption(t.Warehouses[n].Name,t.Warehouses[n].ID)}};Rebound.GlobalTrader.Site.Controls.DropDowns.PreferredWarehouse.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.PreferredWarehouse",Rebound.GlobalTrader.Site.Controls.DropDowns.Base);