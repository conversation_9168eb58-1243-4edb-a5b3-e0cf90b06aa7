
echo off

pause

if (%DB_SCRIPT_PATH%)==() set DB_SCRIPT_PATH=%CD%\Script_Release\

if (%SERVER_NAME%)==() set SERVER_NAME=%COMPUTERNAME%

if (%DATABASE_NAME%)==() set DATABASE_NAME=BorisGlobalTrader

if (%USER_ID%)==() set USER_ID=BorisGT

if (%PASSWORD%)==() set PASSWORD=********

cls
REM type readme.txt
call setEnv
pause

set hr=%time:~0,2%

if "%hr:~0,1%" equ " " set hr=0%hr:~1,1%
set logfilepath= %DB_SCRIPT_PATH%\logs\_ExecutionLog_%date:~-4,4%%date:~-10,2%%date:~-7,2%_%hr%%time:~3,2%%time:~6,2%.log
set cmd='dir %DB_SCRIPT_PATH%\*.sql /b/s'

set /a counter=01

setlocal ENABLEDELAYEDEXPANSION

FOR /F "tokens=1" %%G IN (%cmd%) DO (

echo ******PROCESSING %%G FILE******
echo ******PROCESSING %%G FILE****** >> %logfilepath%

sqlcmd -S %SERVER_NAME% -U %USER_ID% -P  %PASSWORD% -d %DATABASE_NAME% -i %%G -o %DB_SCRIPT_PATH%\logs\!counter!_%%~nxG.log
type %DB_SCRIPT_PATH%\logs\!counter!_%%~nxG.log
REM IF %errorlevel% NEQ 0 GOTO :OnError

set /a counter=counter+1
)
GOTO :Success

:OnError
echo ERROR ERROR ERROR
echo One\more script(s) failed to execute, terminating bath.
echo Check output.log file for more details
pause
EXIT /b

:Success
echo ALL the scripts deployed successfully!!
pause
EXIT /b