//Marker     Changed by      Date               Remarks
//[001]      Prakash           12/04/2016         CR:- Client Invoice
using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site;
using Rebound.GlobalTrader.Site.Controls;

namespace Rebound.GlobalTrader.Site.Controls.Forms {
    public partial class ClientInvoiceLines_Delete : Base
    {

		#region Locals

		#endregion

		#region Properties

		private int _intLineID;
		public int LineID {
			get { return _intLineID; }
			set { _intLineID = value; }
		}

		#endregion

		#region Overrides

		/// <summary>
		/// OnInit
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
			base.OnInit(e);
            TitleText = Functions.GetGlobalResource("FormTitles", "ClientInvoiceLines_Delete");
            AddScriptReference("Controls.Nuggets.ClientInvoiceLines.Delete.ClientInvoiceLines_Delete.js");
            WireUpControls();
        }

		/// <summary>
		/// OnPreRender
		/// </summary>
		/// <param name="e"></param>
		protected override void OnPreRender(EventArgs e) {
			SetupScriptDescriptors();
			base.OnPreRender(e);
		}

		#endregion

        /// <summary>
        /// Wire up controls to the ascx
        /// </summary>
        private void WireUpControls()
        {
        }

		/// <summary>
		/// Setup Script Descriptors
		/// </summary>
		private void SetupScriptDescriptors() {
            _scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.Forms.ClientInvoiceLines_Delete", ctlDesignBase.ClientID);
			_scScriptControlDescriptor.AddProperty("intLineID", _intLineID);
        }
	}
}