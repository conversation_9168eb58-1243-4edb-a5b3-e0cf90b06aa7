Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.ItemSearch");Rebound.GlobalTrader.Site.Controls.ItemSearch.SalesOrderLinesClose=function(n){Rebound.GlobalTrader.Site.Controls.ItemSearch.SalesOrderLinesClose.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.ItemSearch.SalesOrderLinesClose.prototype={initialize:function(){Rebound.GlobalTrader.Site.Controls.ItemSearch.SalesOrderLinesClose.callBaseMethod(this,"initialize");this.addSetupData(Function.createDelegate(this,this.doSetupData));this.addGetDataComplete(Function.createDelegate(this,this.doGetDataComplete));$("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmAdd_ctlDB_ctlSourceFromInvoiceSoc_ctlDB_ctlIncludeClosed").hide()},dispose:function(){this.isDisposed||Rebound.GlobalTrader.Site.Controls.ItemSearch.SalesOrderLinesClose.callBaseMethod(this,"dispose")},doSetupData:function(){this._objData.set_PathToData("controls/ItemSearch/SalesOrderLinesClose");this._objData.set_DataObject("SalesOrderLinesClose");this._objData.set_DataAction("GetData");this._objData.addParameter("Part",this.getFieldValue("ctlPartNo"));this._objData.addParameter("CMName",this.getFieldValue("ctlCompany"));this._objData.addParameter("IncludeClosed",this.getFieldValue("ctlIncludeClosed"));this._objData.addParameter("CustomerPO",this.getFieldValue("ctlCustomerPO"));this._objData.addParameter("SalesOrderNoLo",this.getFieldValue_Min("ctlSalesOrderNo"));this._objData.addParameter("SalesOrderNoHi",this.getFieldValue_Max("ctlSalesOrderNo"));this._objData.addParameter("DateOrderedFrom",this.getFieldValue("ctlDateOrderedFrom"));this._objData.addParameter("DateOrderedTo",this.getFieldValue("ctlDateOrderedTo"));this._objData.addParameter("DatePromisedFrom",this.getFieldValue("ctlDatePromisedFrom"));this._objData.addParameter("DatePromisedTo",this.getFieldValue("ctlDatePromisedTo"));this._objData.addParameter("Salesman",this.getFieldValue("ctlSalesman"));this._objData.addParameter("OnlyFromIPO",this.getFieldValue("ctlOnlyFromIPO"))},doGetDataComplete:function(){for(var n,i,t=0,r=this._objResult.Results.length;t<r;t++)n=this._objResult.Results[t],i=[n.No,$R_FN.setCleanTextValue(n.CMName),$R_FN.writePartNo(n.Part,n.ROHS),$R_FN.setCleanTextValue(n.Date),n.Price,n.Quantity,$R_FN.setCleanTextValue(n.Salesman),$R_FN.setCleanTextValue(n.CustomerPO),n.Cost,],this._tblResults.addRow(i,n.ID,!1),i=null,n=null}};Rebound.GlobalTrader.Site.Controls.ItemSearch.SalesOrderLinesClose.registerClass("Rebound.GlobalTrader.Site.Controls.ItemSearch.SalesOrderLinesClose",Rebound.GlobalTrader.Site.Controls.ItemSearch.Base,Sys.IDisposable);