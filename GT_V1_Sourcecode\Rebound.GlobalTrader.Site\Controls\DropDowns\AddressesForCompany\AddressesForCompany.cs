using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;

namespace Rebound.GlobalTrader.Site.Controls.DropDowns {
	public partial class AddressesForCompany : Base {

		private int _intCompanyID;
		public int CompanyID {
			get { return _intCompanyID; }
			set { _intCompanyID = value; }
		}

		protected override void OnLoad(EventArgs e) {
            SetDropDownType("AddressesForCompany");
            AddScriptReference("Controls.DropDowns.AddressesForCompany.AddressesForCompany");
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.DropDowns.AddressesForCompany", ClientID);
			_scScriptControlDescriptor.AddProperty("intCompanyID", _intCompanyID);
			base.OnLoad(e);
		}

	}
}