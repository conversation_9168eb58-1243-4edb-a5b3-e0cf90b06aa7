﻿<%@ Control Language="C#" CodeBehind="OpenSupplierPOApproval.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.HomeNuggets.OpenSupplierPOApproval" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Nugget:DesignBase id="ctlDB" runat="server" BoxType="allwhite">
	<Content>
		<div class="homepageNugget">
            <asp:Panel ID="pnlOpenSupplierPOApproval" runat="server" CssClass="overdue">
			    <ReboundUI:SimpleDataTable ID="tblOpenSupplierPOApproval" runat="server" AllowSelection="false" />
		    </asp:Panel>
		</div>
	</Content>
</ReboundUI_Nugget:DesignBase>
