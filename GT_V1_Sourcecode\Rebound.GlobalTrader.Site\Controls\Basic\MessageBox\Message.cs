using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;



/// <summary>
/// Generic message class
/// </summary>
namespace Rebound.GlobalTrader.Site.Controls {

	public class Message {

		#region Properties
		/// <summary>
		/// Text of message
		/// </summary>
		private string _messageText;
		public string MessageText {
			get { return _messageText; }
			set { _messageText = value; }
		}

		/// <summary>
		/// Message type
		/// </summary>
		private Controls.MessageBox.MessageTypeList _messageType;
		public Controls.MessageBox.MessageTypeList MessageType {
			get { return _messageType; }
			set { _messageType = value; }
		}
		#endregion

		public Message(Controls.MessageBox.MessageTypeList enmType, string strMessage) {
			this._messageText = strMessage;
			this._messageType = enmType;
		}
	}

}