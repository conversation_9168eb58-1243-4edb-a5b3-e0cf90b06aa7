﻿
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

/*
-- ==========================================================================================
-- TASK      		UPDATED BY     		DATE         ACTION 	DESCRIPTION                                    
-- 001 				Devendra Sikarwar  	20-12-2023   Update		RP-2722/RP-2528 (The email notification should not be going to the RL team if they have offers with 0 quantity for that HUB RFQ.)
-- US-201305		Phuc.HoangDinh		24-04-2024	 Update		Update for US-201305 [Production bug] RL team are not being notified when new requirement has been raised		
-- ==========================================================================================
*/

CREATE OR ALTER PROCEDURE [dbo].[usp_select_PartFoundInReverseLogistics]                                          
	@PartNo NVARCHAR(50) = NULL               
AS                                   
BEGIN                            
   SELECT DISTINCT  TOP 1 COUNT(ReverseLogisticid)AS Partcount,Part FROM BorisGlobalTraderImports.dbo.tbReverseLogistic WHERE 
    /*[001]*/  
	--  part=@PartNo 
	-- [dbo].[ufn_get_fullpart] will be trim '%'
	-- Example: SELECT  [dbo].[ufn_get_fullpart]('SA606DK%') as Part => Result: 'SA606DK'
	
    FullPart LIKE [dbo].[ufn_get_fullpart](@PartNo) + '%'       
 AND ISNULL(Quantity,0)<>0            
   /*[001]*/            
   GROUP BY ReverseLogisticid,Part              
                                  
END 

GO


