using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site;
using Rebound.GlobalTrader.Site.Controls;
using Rebound.GlobalTrader.Site.Controls.FormFieldCollections;

namespace Rebound.GlobalTrader.Site.Controls.Forms {
    public partial class CRMAReceiveLines_SerialNo : Base
    {

        #region Locals

        protected FlexiDataTable _tblSerialNodetails;
        #endregion

        #region Properties

        private int _intGIID = -1;
        public int GIID
        {
            get { return _intGIID; }
            set { _intGIID = value; }
        }
        #endregion

        #region Overrides

        /// <summary>
        /// OnInit
        /// </summary>
        /// <param name="e"></param>
        protected override void OnInit(EventArgs e)
        {
            base.OnInit(e);
            //TitleText = Functions.GetGlobalResource("FormTitles", "CustomerRequirementMainInfo_Edit");
            //AddScriptReference("Controls.Nuggets.CusReqMainInfo.Edit.CusReqMainInfo_Edit.js");
            AddScriptReference("Controls.Nuggets.CRMAReceivingLines.SerialNo.CRMAReceiveLines_SerialNo.js");
            if (_objQSManager.GoodsInID > 0) _intGIID = _objQSManager.GoodsInID;
            WireUpControls();
            SetupTable();
        }

        /// <summary>
        /// OnPreRender
        /// </summary>
        /// <param name="e"></param>
        protected override void OnPreRender(EventArgs e)
        {

            SetupScriptDescriptors();
            base.OnPreRender(e);
        }


        private void SetupTable()
        {

            _tblSerialNodetails.AllowSelection = true;
            _tblSerialNodetails.Columns.Add(new FlexiDataColumn("SubGroup", WidthManager.GetWidth(WidthManager.ColumnWidth.DateAndTime)));
            _tblSerialNodetails.Columns.Add(new FlexiDataColumn("SerialNo", Unit.Empty, true));
            //_tblSerialNodetails.Columns.Add(new FlexiDataColumn("GI", Unit.Empty, true));

        }

        /// <summary>
        /// Wire up controls to the ascx
        /// </summary>
        private void WireUpControls()
        {

            _tblSerialNodetails = (FlexiDataTable)ctlDesignBase.FindContentControl("tblSerialNodetails");
        }
        #endregion

        /// <summary>
        /// Setup Script Descriptors
        /// </summary>
        private void SetupScriptDescriptors()
        {
            _scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.Forms.CRMAReceiveLines_SerialNo", ctlDesignBase.ClientID);
            //  _scScriptControlDescriptor.AddProperty("intCustomerRequirementID", _objQSManager.CustomerRequirementID);


            _scScriptControlDescriptor.AddElementProperty("btnAdd", FindFieldControl("ctlAddUpdate", "btnAdd").ClientID);
            _scScriptControlDescriptor.AddElementProperty("btnRefresh", FindFieldControl("ctlAddUpdate", "btnRefresh").ClientID);
            _scScriptControlDescriptor.AddProperty("intGIID", _intGIID);
            // _scScriptControlDescriptor.AddElementProperty("lblSubGroupError", FindFieldControl("ctlSubGroup", "lblSubGroupError").ClientID);           
            _scScriptControlDescriptor.AddElementProperty("lblDuplicateError", FindFieldControl("ctlAddUpdate", "lblDuplicateError").ClientID);
            _scScriptControlDescriptor.AddComponentProperty("tblSerialNodetails", FindFieldControl("ctlSerialNoDetail", "tblSerialNodetails").ClientID);
            //_scScriptControlDescriptor.AddElementProperty("btnUpdate", FindFieldControl("ctlAddUpdate", "btnUpdate").ClientID);
        }
	}
}
