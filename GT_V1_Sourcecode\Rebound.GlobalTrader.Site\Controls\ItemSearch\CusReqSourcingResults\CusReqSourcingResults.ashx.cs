/* Marker     changed by      date         Remarks
/* [0001]      A<PERSON><PERSON><PERSON>   14/07/2014  ESMS Ref:172 - Issue in Part Searching in whole GT */

using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;
using Rebound.GlobalTrader.Site.Enumerations;

namespace Rebound.GlobalTrader.Site.Controls.ItemSearch.Data {
	[WebService(Namespace = "http://tempuri.org/")]
	[WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
	public class CustomerRequirementSourcingResults : Rebound.GlobalTrader.Site.Data.ItemSearch.Base {

		protected override void GetData() {
			List<SourcingResult> lst = null;
			try {
				lst = SourcingResult.ItemSearch(
					SessionManager.ClientID
					, GetFormValue_NullableInt("Order", 0)
					, GetFormValue_NullableInt("SortDir", SortColumnDirection.ASC)
					, GetFormValue_NullableInt("PageIndex", 0)
					, GetFormValue_NullableInt("PageSize", 10)
                    //[0001] start code
                    //, GetFormValue_StringForPartSearch("Part")
                      , GetFormValue_PartForLikeSearch("Part")
                    //[0001] end code
					//, GetFormValue_StringForNameSearch("CM")
                    , GetFormValue_StringForNameSearchDecode("CM")
					, GetFormValue_NullableInt("ReqNoLo")
					, GetFormValue_NullableInt("ReqNoHi")
					, GetFormValue_StringForNameSearch("Supplier")
                    , GetFormValue_Boolean("IsPoHub")
                    , GetFormValue_NullableInt("intQuoteID")
                    , GetFormValue_StringForNameSearchDecode("BOM")
				);
				JsonObject jsn = new JsonObject();
				JsonObject jsnItems = new JsonObject(true);
				for (int i = 0; i < lst.Count; i++) {
					JsonObject jsnItem = new JsonObject();
					jsnItem.AddVariable("ID", lst[i].SourcingResultId);
					jsnItem.AddVariable("No", lst[i].CustomerRequirementNumber);
					jsnItem.AddVariable("Price", Functions.FormatCurrency(lst[i].Price, lst[i].CurrencyCode));
					jsnItem.AddVariable("Quantity", lst[i].Quantity);
					jsnItem.AddVariable("CMNo", lst[i].CompanyNo);
					jsnItem.AddVariable("CMName", lst[i].CompanyName);
					jsnItem.AddVariable("Part", lst[i].Part);
					jsnItem.AddVariable("Mfr", lst[i].ManufacturerCode);
					jsnItem.AddVariable("DC", lst[i].DateCode);
					jsnItem.AddVariable("Product", lst[i].ProductName);
					jsnItem.AddVariable("Package", lst[i].PackageName);

                    jsnItem.AddVariable("Supplier", (lst[i].IsPoHub == "Yes") ? lst[i].ClientSupplierName : lst[i].SupplierName);
					jsnItem.AddVariable("OfferDate", Functions.FormatDate(lst[i].OfferStatusChangeDate));
					jsnItem.AddVariable("OfferBy", lst[i].OfferStatusChangeEmployeeName);
                    jsnItem.AddVariable("IsPoHub", lst[i].IsPoHub);
                    BLL.Printer rm = BLL.Printer.GetRestrictedManufacture(SessionManager.ClientID, lst[i].ManufacturerNo);
                    if (rm == null)
                    {
                        jsnItem.AddVariable("isRestrictedManufacturer", null);
                    }
                    else
                    {

                        jsnItem.AddVariable("isRestrictedManufacturer", rm.isRestrictedManufacturer);

                    }
                    jsnItems.AddVariable(jsnItem);
					jsnItem.Dispose(); jsnItem = null;
				}
				jsn.AddVariable("Count", (lst.Count > 0) ? lst[0].RowCnt : 0);
				jsn.AddVariable("Results", jsnItems);
				OutputResult(jsn);
				jsnItems.Dispose(); jsnItems = null;
				jsn.Dispose(); jsn = null;
			} catch (Exception ex) {
				WriteError(ex);
			} finally {
				lst = null;
			}
			base.GetData();
		}

	}
}
