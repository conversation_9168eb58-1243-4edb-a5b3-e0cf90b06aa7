﻿using Rebound.GlobalTrader.DAL.SQLClient;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Rebound.GlobalTrader.BLL
{
    public partial class KubStockDetailsForBOM : BizObject
    {
        #region Properties
        /// <summary>
        /// Reverse Logistics 10 most recent offers for selected part
        /// </summary>
        public List<StockOffer> Top10ReverseLogisticsOffer { get; set; } = new List<StockOffer>();
        /// <summary>
        /// Strategic Stock 10 most recent offers for selected part 
        /// </summary>
        public List<StockOffer> Top10StrategicStockOffer { get; set; } = new List<StockOffer>();
        /// <summary>
        /// Stock 20 most recent for selected part
        /// </summary>
        public List<StockDetailsForPart> Top20RecentStockForPart { get; set; } = new List<StockDetailsForPart>();
        #endregion

        #region Methods
        public static KubStockDetailsForBOM GetStockDetailsForBOMPart(string partNo, int clientId, int BOMId, bool isHUBRFQ = false)
        {
            var stockDetails = new KubStockDetailsForBOM();
            var stockDataDetails = new DAL.KubStockDetailsForBOM();
            SQLKubProvider objSQLKubProvider = new SQLKubProvider();
            try
            {
                stockDataDetails = objSQLKubProvider.GetStockDetailsForBOMPart(partNo, clientId, BOMId, isHUBRFQ);
                if (stockDataDetails == null)
                {
                    return null;
                }
                stockDetails.Top10ReverseLogisticsOffer = stockDataDetails.Top10ReverseLogisticsOffer.Select(o => new BLL.StockOffer
                {
                    DateAdded = o.DateAdded,
                    Quantity = o.Quantity,
                    Price = o.Price,
                    Manufacturer = o.Manufacturer
                }).ToList();

                stockDetails.Top10StrategicStockOffer = stockDataDetails.Top10StrategicStockOffer.Select(o => new BLL.StockOffer
                {
                    DateAdded = o.DateAdded,
                    Quantity = o.Quantity,
                    Price = o.Price,
                    Manufacturer = o.Manufacturer
                }).ToList();

                stockDetails.Top20RecentStockForPart = stockDataDetails.Top20RecentStockForPart.Select(o => new BLL.StockDetailsForPart
                {
                    StockId = o.StockId,
                    Quantity = o.Quantity,
                    Price = o.Price,
                    Client = o.Client
                }).ToList();

                return stockDetails;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        #endregion
    }

    public class StockOffer
    {
        public string DateAdded { get; set; }
        public int Quantity { get; set; }
        public string Price { get; set; }
        public string Manufacturer { get; set; }
    }


    public class StockDetailsForPart
    {
        public string StockId { get; set; }
        public int Quantity { get; set; }
        public string Price { get; set; }
        public string Client { get; set; }
    }
}
