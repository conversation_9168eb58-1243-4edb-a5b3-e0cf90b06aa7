/*
Marker     Changed by      Date          Remarks
[001]      Abhinav         25/04/2014    ESMS #111 Quick Jump for NPR
[002]     <PERSON><PERSON>    16/02/2017    Quick Jump of IPO & HUBRFQ
[003]     <PERSON><PERSON><PERSON>      30/05/2018    Quick Jump in Global Warehouse [11815]
[004]     <PERSON><PERSON><PERSON>      28-Nov-2018   Show customer requirement all info in tree view. 
*/

using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site.Controls;
using Rebound.GlobalTrader.Site;

namespace Rebound.GlobalTrader.Site.Controls.LeftNuggets {
	public partial class QuickJump : Base {


		#region Properties

		public QuickJumpType _enmDefaultType = QuickJumpType.None;
		public QuickJumpType DefaultType {
			get { return _enmDefaultType; }
			set { _enmDefaultType = value; }
		}
        private QuickJumpSection _enmSection = QuickJumpSection.Orders;
		public QuickJumpSection Section {
			get { return _enmSection; }
			set { _enmSection = value; }
		}

		#endregion

		#region Locals

		public RadioButtonList _rad;
		private ReboundTextBox _txt;
		private IconButton _ibtnGo;
        //[004] start
       // private IconButton _ibtnShowRelDoc;
        //[004] end
		private int _intIndex = 0;
        protected Panel _pnlQJSearchResultTooltip;
        protected Panel _pnlQJSearchResult;

		#endregion

		protected override void OnInit(EventArgs e) {
			SetLeftNuggetType("QuickJump");
			base.OnInit(e);
			AddScriptReference("Controls.LeftNuggets.QuickJump.QuickJump");
			WireUpControls();
			SetupScriptDescriptors();
		}

		protected override void OnPreRender(EventArgs e) {
			SetupRadioButtons();
			base.OnPreRender(e);
		}

		private void SetupRadioButtons() {
            Boolean IsIPOHUB = Convert.ToBoolean(System.Configuration.ConfigurationManager.AppSettings["IsIPOHUB"]);
			switch (_enmSection) {
				case QuickJumpSection.Orders:
					AddItemToRadioList(QuickJumpType.CustomerRequirement);
					AddItemToRadioList(QuickJumpType.Quote);
					AddItemToRadioList(QuickJumpType.PurchaseOrder);
					AddItemToRadioList(QuickJumpType.SalesOrder);
					AddItemToRadioList(QuickJumpType.Invoice);
					AddItemToRadioList(QuickJumpType.CustomerRMA);
					AddItemToRadioList(QuickJumpType.SupplierRMA);
					AddItemToRadioList(QuickJumpType.CreditNote);
					AddItemToRadioList(QuickJumpType.DebitNote);
					AddItemToRadioList(QuickJumpType.Sourcing);
                    //[002] start
                    if (IsIPOHUB)
                    {
                        AddItemToRadioList(QuickJumpType.InternalPurchaseOrder);
                        AddItemToRadioList(QuickJumpType.HUBRFQ);
                    }
                    //[002] end
					break;
				case QuickJumpSection.Warehouse:
					AddItemToRadioList(QuickJumpType.ReceivePurchaseOrder);
					AddItemToRadioList(QuickJumpType.ShipSalesOrder);
					AddItemToRadioList(QuickJumpType.ReceiveCustomerRMA);
					AddItemToRadioList(QuickJumpType.ShipSupplierRMA);
					AddItemToRadioList(QuickJumpType.GoodsIn);
					AddItemToRadioList(QuickJumpType.Stock);
                    //[0001] start
                    AddItemToRadioList(QuickJumpType.NPR);
                    //[001] end
                    AddItemToRadioList(QuickJumpType.StockNumber);
                    break;
				case QuickJumpSection.Contact:
					AddItemToRadioList(QuickJumpType.Company);
					AddItemToRadioList(QuickJumpType.Customer);
					AddItemToRadioList(QuickJumpType.Manufacturer);
					AddItemToRadioList(QuickJumpType.Supplier);
					AddItemToRadioList(QuickJumpType.Prospect);
					AddItemToRadioList(QuickJumpType.Contact);
					break;
				case QuickJumpSection.Accounts:
					AddItemToRadioList(QuickJumpType.ReceivedPurchaseOrder);
					AddItemToRadioList(QuickJumpType.ReceivedCustomerRMA);
					break;
			}
			if (_enmDefaultType == QuickJumpType.None) _rad.SelectedIndex = 0;
			switch (_enmDefaultType) {
				case QuickJumpType.CustomerRequirement:
				case QuickJumpType.Quote:
				case QuickJumpType.PurchaseOrder:
				case QuickJumpType.SalesOrder:
				case QuickJumpType.Invoice:
				case QuickJumpType.CustomerRMA:
				case QuickJumpType.SupplierRMA:
				case QuickJumpType.CreditNote:
				case QuickJumpType.DebitNote:
				case QuickJumpType.ReceivePurchaseOrder:
				case QuickJumpType.ShipSalesOrder:
				case QuickJumpType.ReceiveCustomerRMA:
				case QuickJumpType.ShipSupplierRMA:
				case QuickJumpType.GoodsIn:
				case QuickJumpType.ReceivedPurchaseOrder:
				case QuickJumpType.ReceivedCustomerRMA:
                //[002] start
                case QuickJumpType.InternalPurchaseOrder:
               
                //[002] end
                //[0001] start
                case QuickJumpType.NPR:
                //[0001] end
                    _txt.TextBoxMode = ReboundTextBox.TextBoxModeList.Numeric;
					break;

				case QuickJumpType.Sourcing:
				case QuickJumpType.Stock:
				case QuickJumpType.Company:
				case QuickJumpType.Customer:
				case QuickJumpType.Manufacturer:
				case QuickJumpType.Supplier:
				case QuickJumpType.Prospect:
				case QuickJumpType.Contact:
                //[002] start
                case QuickJumpType.HUBRFQ:
                //[002] end
                case QuickJumpType.StockNumber:
                    _txt.TextBoxMode = ReboundTextBox.TextBoxModeList.Normal;
					break;

				case QuickJumpType.None:
					if (_enmSection == QuickJumpSection.Contact) _txt.TextBoxMode = ReboundTextBox.TextBoxModeList.Normal;
					if (_enmSection == QuickJumpSection.Orders) _txt.TextBoxMode = ReboundTextBox.TextBoxModeList.Numeric;
					if (_enmSection == QuickJumpSection.Warehouse) _txt.TextBoxMode = ReboundTextBox.TextBoxModeList.Numeric;
					break;
			}
		}

		private void SetupScriptDescriptors() {
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.LeftNuggets.QuickJump", ctlDesignBase.ClientID);
			_scScriptControlDescriptor.AddElementProperty("ibtnGo", _ibtnGo.ClientID);
            //[004] start
           // _scScriptControlDescriptor.AddElementProperty("ibtnShowRelDoc", _ibtnShowRelDoc.ClientID);
            //[004] end
			_scScriptControlDescriptor.AddElementProperty("txt", _txt.ClientID);
			_scScriptControlDescriptor.AddElementProperty("rad", _rad.ClientID);
			_scScriptControlDescriptor.AddElementProperty("pnlNotFound", Functions.FindControlRecursive(this, "pnlNotFound").ClientID);
			_scScriptControlDescriptor.AddProperty("enmSection", _enmSection);
			_scScriptControlDescriptor.AddProperty("enmSelectedType", _enmDefaultType);

            _scScriptControlDescriptor.AddElementProperty("pnlQJSearchResultTooltip", _pnlQJSearchResultTooltip.ClientID);
            _scScriptControlDescriptor.AddElementProperty("pnlQJSearchResult", _pnlQJSearchResult.ClientID);
        }

		private void AddItemToRadioList(QuickJumpType enmType) {
			ListItem li = new ListItem();
			li.Value = ((int)enmType).ToString();
			li.Text = string.Format("{0}", Functions.GetGlobalResource("misc", string.Format("{0}Short", enmType.ToString())));
			li.Selected = (enmType == _enmDefaultType);
			_rad.Items.Add(li);
			if (enmType == _enmDefaultType) _rad.SelectedIndex = _intIndex;
			_intIndex += 1;
			li = null;
		}

		private void WireUpControls() {
			_rad = (RadioButtonList)ctlDesignBase.FindContentControl("rad");
			_txt = (ReboundTextBox)ctlDesignBase.FindContentControl("txt");
			_ibtnGo = (IconButton)ctlDesignBase.FindContentControl("ibtnGo");
            //[004] start
            //_ibtnShowRelDoc = (IconButton)ctlDesignBase.FindContentControl("ibtnShowRelDoc");
            //[004] end
            _pnlQJSearchResultTooltip = (Panel)Functions.FindControlRecursive(this, "pnlQJSearchResultTooltip");
            _pnlQJSearchResult = (Panel)FindContentControl("pnlQJSearchResult");
		}

		public enum QuickJumpSection {
			Orders,
			Warehouse,
			Contact,
			Accounts
		}

		public enum QuickJumpType {
			None,

			CustomerRequirement,
			Quote,
			PurchaseOrder,
			SalesOrder,
			Invoice,
			CustomerRMA,
			SupplierRMA,
			CreditNote,
			DebitNote,
			Sourcing,

			ReceivePurchaseOrder,
			ShipSalesOrder,
			ReceiveCustomerRMA,
			ShipSupplierRMA,
			GoodsIn,
			Stock,
            //[0001] start
            NPR,
            //[0001] end
			Company,
			Customer,
			Manufacturer,
			Supplier,
			Prospect,
			Contact,

			ReceivedPurchaseOrder,
			ReceivedCustomerRMA,
            //[002] start
            InternalPurchaseOrder,
            HUBRFQ,
            //[002] end
            StockNumber
        }
        
	}
}
