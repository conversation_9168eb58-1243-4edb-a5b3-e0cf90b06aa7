﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


/* 
===========================================================================================
TASK      			UPDATED BY     		DATE         		ACTION 			DESCRIPTION
[BUG-204859]		Cuong.DoX			12-JULY-2024		Create			Remove quarantine status
[US-224605]			Trung Pham Van		17-Jan-2025			Update			Update new matrix status
===========================================================================================
*/
CREATE OR ALTER   FUNCTION [dbo].[ufn_get_goodsIn_statusNo] (@GoodsInId int)  
--  
RETURNS int  
--  
AS --  
   BEGIN  
  
    DECLARE @StatusNo int  
      , @QuarantinedLines int  
      , @InspectedLines int  
      , @Lines int  
      , @InvoiceDate datetime  
      , @SupplierInvoice nvarchar(50)  
	  , @QueriedLines int
	  , @ReleasedLines int
	  , @ApprovedLines int
	  , @InspectionLines int
	  , @DeclinedQueried int
        
    SELECT  @StatusNo = 1  
          , @InvoiceDate = InvoiceDate  
          , @SupplierInvoice = SupplierInvoice  
    FROM    dbo.tbGoodsIn  
    WHERE   GoodsInId = @GoodsInId  
  
    SELECT  @Lines = isnull(count(*), 0)  
    FROM    dbo.tbGoodsInLine  
    WHERE   GoodsInNo = @GoodsInId  
  
-- GI Received: no lines or no inspected  
--------------------------------------------------------------------------------------------------  
    IF @Lines = 0   
        BEGIN  
            SET @StatusNo = 1  
            RETURN @StatusNo    
        END  
  
-- Quarantined: all lines unavailable  remove due to the ticket [BUG-204859]
--------------------------------------------------------------------------------------------------  
    
	/*SELECT  @QuarantinedLines = isnull(count(*), 0)  
    FROM    dbo.tbGoodsInLine gil  
    JOIN    dbo.tbStock sk ON gil.GoodsInLineId = sk.GoodsInLineNo  
    WHERE   GoodsInNo = @GoodsInId  
            AND sk.Unavailable = 1  
  
    IF @Lines > 0  
        AND @QuarantinedLines = @Lines   
        BEGIN  
            SET @StatusNo = 5  
            RETURN @StatusNo    
        END  */
          
-- InvoicePaid: invoiceDate set on header  
--------------------------------------------------------------------------------------------------  
--    IF @SupplierInvoice > ' '  
--        AND @InvoiceDate IS NOT NULL   
--        BEGIN  
--            SET @StatusNo = 6  
--            RETURN @StatusNo    
--        END  
  
  
  -- GI Awaiting Query: any of associated GI line and awaiting response

 	SELECT  @QuarantinedLines = isnull(count(*), 0)  
    FROM    dbo.tbGoodsInLine gil  
    JOIN    dbo.tbStock sk ON gil.GoodsInLineId = sk.GoodsInLineNo  
    WHERE   GoodsInNo = @GoodsInId AND sk.Unavailable = 1  

	SELECT @QueriedLines = ISNULL(COUNT(DISTINCT GILineNo), 0)
	FROM tbGiQueryMessage
	WHERE GoodInNo = @GoodsInId AND IsSalesApproved IS NULL AND IsPurchasingApproved IS NULL AND IsQualityApproved IS NULL	
	
	SELECT @DeclinedQueried = ISNULL(COUNT(DISTINCT GILineNo), 0)
	FROM tbGiQueryMessage
	WHERE (IsSalesApproved = 2 OR IsPurchasingApproved = 2 OR IsQualityApproved = 2)
	AND GoodInNo = @GoodsInId

	SELECT @ApprovedLines = ISNULL(COUNT(*), 0)
	FROM tbGI_QueryMessageApprovals
	WHERE GoodInNo = @GoodsInId AND (SalesApprovalStatus = 1 OR PurchasingApprovalStatus = 1 OR QualityApprovalStatus = 1)

	IF (@Lines > 0 AND @QueriedLines > 0 AND @ApprovedLines < @QueriedLines AND @QuarantinedLines = 0 AND @DeclinedQueried = 0 AND @ApprovedLines = 0)
		BEGIN
			SET @StatusNo = 4
			RETURN @StatusNo  
		END

-- GI Partially Quarantined

	IF @Lines > 0  
        AND @QuarantinedLines < @Lines AND @QuarantinedLines > 0
        BEGIN  
            SET @StatusNo = 7  
            RETURN @StatusNo    
        END 

-- GI Quarantined
    IF @Lines > 0  
        AND @QuarantinedLines = @Lines   
        BEGIN  
            SET @StatusNo = 8  
            RETURN @StatusNo    
        END 

-- GI Partially Released
	SELECT @ReleasedLines = ISNULL(COUNT(*), 0)
	FROM tbGoodsInLine
	WHERE GoodsInNo = @GoodsInId AND InspectedBy IS NOT NULL

	IF (@Lines > 0 AND @ReleasedLines > 0 AND @ReleasedLines < @Lines AND @QuarantinedLines = 0)
		BEGIN
			SET @StatusNo = 5
			RETURN @StatusNo
		END

-- GI Released
	IF (@Lines > 0 AND @ReleasedLines > 0 AND @ReleasedLines = @Lines)
		BEGIN
			SET @StatusNo = 6
			RETURN @StatusNo
		END

-- GI Inspected: all received lines inspected
--------------------------------------------------------------------------------------------------  
    SELECT  @InspectedLines = isnull(count(*), 0)  
    FROM    dbo.tbGoodsInLine  
    WHERE   GoodsInNo = @GoodsInId  
            AND (InspectionStatus = 2 OR DateInspected IS NOT NULL)

	SELECT @InspectionLines = ISNULL(COUNT(*), 0)
	FROM    dbo.tbGoodsInLine  
    WHERE   GoodsInNo = @GoodsInId  
            AND IsStartInspection = 1 AND InspectionStatus = 1

    IF @Lines > 0  
        AND @InspectedLines = @Lines   
        BEGIN  
            SET @StatusNo = 3  
            RETURN @StatusNo    
        END  
  
  
-- GI Partially Inspected: any/all received lines inspecting or any Lines inspected
--------------------------------------------------------------------------------------------------  
			
    IF @Lines > 0  
        AND ((@InspectionLines > 0  
        AND @InspectionLines <= @Lines) OR (
		@InspectedLines > 0  
        AND @InspectedLines <= @Lines))
        BEGIN  
            SET @StatusNo = 2
        END  
  
    RETURN @StatusNo  
--  
   END  
GO


