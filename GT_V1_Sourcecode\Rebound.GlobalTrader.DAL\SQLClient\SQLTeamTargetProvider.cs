﻿using System;
using System.Data;
using System.Data.SqlClient;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;
using System.Web.Caching;
using System.Data.Common;


namespace Rebound.GlobalTrader.DAL.SqlClient
{
	public class SqlTeamTargetProvider : TeamTargetProvider
	{


		/// <summary>
		/// KPI_GetTeamSalesTarget 
		/// Calls [KPI_GetTeamSalesTarget]
		/// </summary>
		public override List<TeamTargetDetails> GetTeamAndSalesTarget(System.Int32? teamNo, System.Int32? managerNo, System.Int32 yearNo, System.String table)
		{
			SqlConnection cn = null;
			SqlCommand cmd = null;
			try
			{
				cn = new SqlConnection(this.ConnectionString);
				cmd = new SqlCommand("KPI_GetTeamSalesTarget", cn);
				cmd.CommandType = CommandType.StoredProcedure;
				cmd.CommandTimeout = 30;
				cmd.Parameters.Add("@TeamNo", SqlDbType.Int).Value = teamNo;
				cmd.Parameters.Add("@ManagerNo", SqlDbType.Int).Value = managerNo;
				cmd.Parameters.Add("@YearNo", SqlDbType.Int).Value = yearNo;
				cmd.Parameters.Add("@Table", SqlDbType.NVarChar).Value = table;
				cn.Open();
				DbDataReader reader = ExecuteReader(cmd);
				List<TeamTargetDetails> lst = new List<TeamTargetDetails>();
				while (reader.Read())
				{
					TeamTargetDetails obj = new TeamTargetDetails();
					obj.TeamId = GetReaderValue_Int32(reader, "TeamId", 0);
					obj.TeamName = GetReaderValue_String(reader, "TeamName", "");
					obj.PersonType = GetReaderValue_String(reader, "PersonType", "");
					obj.JanTarget = GetReaderValue_NullableDouble(reader, "JanTarget", null);
					obj.FebTarget = GetReaderValue_NullableDouble(reader, "FebTarget", null);
					obj.MarchTarget = GetReaderValue_NullableDouble(reader, "MarchTarget", null);
					obj.AprTarget = GetReaderValue_NullableDouble(reader, "AprTarget", null);
					obj.MayTarget = GetReaderValue_NullableDouble(reader, "MayTarget", null);
					obj.JuneTarget = GetReaderValue_NullableDouble(reader, "JuneTarget", null);
					obj.JulyTarget = GetReaderValue_NullableDouble(reader, "JulyTarget", null);
					obj.AugTarget = GetReaderValue_NullableDouble(reader, "AugTarget", null);
					obj.SepTarget = GetReaderValue_NullableDouble(reader, "SepTarget", null);
					obj.OctTarget = GetReaderValue_NullableDouble(reader, "OctTarget", null);
					obj.NovTarget = GetReaderValue_NullableDouble(reader, "NovTarget", null);
					obj.DecTarget = GetReaderValue_NullableDouble(reader, "DecTarget", null);
					obj.TotalTarget = GetReaderValue_NullableDouble(reader, "TotalTarget", null);
					obj.AllocatedPer = GetReaderValue_NullableDouble(reader, "AllocatedPer", null);
					obj.RowId = GetReaderValue_Int32(reader, "RowId", 0);
					obj.IsSPMoved = GetReaderValue_Int32(reader, "IsSPMoved", 0);
					lst.Add(obj);
					obj = null;
				}
				return lst;
			}
			catch (SqlException sqlex)
			{
				throw new Exception("Failed to get Team", sqlex);
			}
			finally
			{
				cmd.Dispose();
				cn.Close();
				cn.Dispose();
			}
		}

		/// <summary>
		/// <summary>
		/// Update
		/// Calls [usp_update_TeamSalesTarget]
		/// </summary>
		public override bool Update(System.Int32? rowId, System.String rowType, System.String columnName, System.Double? targetValue, System.Int32? updatedBy, System.Int32? Year, System.Int32? teamNo)
		{
			SqlConnection cn = null;
			SqlCommand cmd = null;
			try
			{
				cn = new SqlConnection(this.ConnectionString);
				cmd = new SqlCommand("usp_update_TeamSalesTarget", cn);
				cmd.CommandType = CommandType.StoredProcedure;
				cmd.Parameters.Add("@RowId", SqlDbType.Int).Value = rowId;
				cmd.Parameters.Add("@RowType", SqlDbType.NVarChar).Value = rowType;
				cmd.Parameters.Add("@ColumnName", SqlDbType.NVarChar).Value = columnName;
				cmd.Parameters.Add("@TargetValue", SqlDbType.Float).Value = targetValue;
				cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
				cmd.Parameters.Add("@Year", SqlDbType.Int).Value = Year;
				cmd.Parameters.Add("@TeamOrSales", SqlDbType.Int).Value = teamNo;
				cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
				cn.Open();
				int ret = ExecuteNonQuery(cmd);
				return (ret > 0);
			}
			catch (SqlException sqlex)
			{
				//LogException(sqlex);
				throw new Exception("Failed to update Team Division Traget", sqlex);
			}
			finally
			{
				cmd.Dispose();
				cn.Close();
				cn.Dispose();
			}
		}


		/// <summary>
		/// <summary>
		/// Update
		/// Calls [usp_saveAllTeamSalesTarget]
		/// </summary>
		public override bool SaveAllTeamSalesData(System.Int32? Year, System.Int32? TeamNo, System.Int32? updatedBy)
		{
			SqlConnection cn = null;
			SqlCommand cmd = null;
			try
			{
				cn = new SqlConnection(this.ConnectionString);
				cmd = new SqlCommand("usp_saveAllTeamSalesTarget", cn);
				cmd.CommandType = CommandType.StoredProcedure;
				cmd.Parameters.Add("@Year", SqlDbType.Int).Value = Year;
				cmd.Parameters.Add("@TeamNo", SqlDbType.Int).Value = TeamNo;
				cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
				cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
				cn.Open();
				int ret = ExecuteNonQuery(cmd);
				return (ret > 0);
			}
			catch (SqlException sqlex)
			{
				//LogException(sqlex);
				throw new Exception("Failed to save all division Team Division Traget", sqlex);
			}
			finally
			{
				cmd.Dispose();
				cn.Close();
				cn.Dispose();
			}
		}



	}
}
