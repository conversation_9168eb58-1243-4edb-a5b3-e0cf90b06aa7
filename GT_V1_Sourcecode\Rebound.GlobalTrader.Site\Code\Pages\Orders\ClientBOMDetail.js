Type.registerNamespace("Rebound.GlobalTrader.Site.Pages.Orders");Rebound.GlobalTrader.Site.Pages.Orders.ClientBOMDetail=function(n){Rebound.GlobalTrader.Site.Pages.Orders.ClientBOMDetail.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Pages.Orders.ClientBOMDetail.prototype={get_intBOMID:function(){return this._intBOMID},set_intBOMID:function(n){this._intBOMID!==n&&(this._intBOMID=n)},get_ctlMainInfo:function(){return this._ctlMainInfo},set_ctlMainInfo:function(n){this._ctlMainInfo!==n&&(this._ctlMainInfo=n)},get_ctlBOMItems:function(){return this._ctlBOMItems},set_ctlBOMItems:function(n){this._ctlBOMItems!==n&&(this._ctlBOMItems=n)},get_ctlPOHubSourcing:function(){return this._ctlPOHubSourcing},set_ctlPOHubSourcing:function(n){this._ctlPOHubSourcing!==n&&(this._ctlPOHubSourcing=n)},get_ctlClientBomCSV:function(){return this._ctlClientBomCSV},set_ctlClientBomCSV:function(n){this._ctlClientBomCSV!==n&&(this._ctlClientBomCSV=n)},get_lblStatus:function(){return this._lblStatus},set_lblStatus:function(n){this._lblStatus!==n&&(this._lblStatus=n)},get_ctlClientBOMCsvExportHistory:function(){return this._ctlClientBOMCsvExportHistory},set_ctlClientBOMCsvExportHistory:function(n){this._ctlClientBOMCsvExportHistory!==n&&(this._ctlClientBOMCsvExportHistory=n)},get_btnPrint:function(){return this._btnPrint},set_btnPrint:function(n){this._btnPrint!==n&&(this._btnPrint=n)},get_ctlExpediteHistory:function(){return this._ctlExpediteHistory},set_ctlExpediteHistory:function(n){this._ctlExpediteHistory!==n&&(this._ctlExpediteHistory=n)},get_ctlClientBOMHubImagesDragDrop:function(){return this._ctlClientBOMHubImagesDragDrop},set_ctlClientBOMHubImagesDragDrop:function(n){this._ctlClientBOMHubImagesDragDrop!==n&&(this._ctlClientBOMHubImagesDragDrop=n)},get_ctlBomCSV:function(){return this._ctlBomCSV},set_ctlBomCSV:function(n){this._ctlBomCSV!==n&&(this._ctlBomCSV=n)},initialize:function(){Rebound.GlobalTrader.Site.Pages.Orders.ClientBOMDetail.callBaseMethod(this,"initialize")},goInit:function(){this._btnPrint&&(this._btnPrint.addPrint(Function.createDelegate(this,this.printHUBRFQ)),this._btnPrint.addEmail(Function.createDelegate(this,this.emailHUBRFQ)),this._btnPrint.addExtraButtonClick(Function.createDelegate(this,this.printOtherDocs)));this._ctlMainInfo&&this._ctlMainInfo.addGotData(Function.createDelegate(this,this.mainBOMDetailGotDataOK));this._ctlBOMItems&&this._ctlBOMItems.addPartSelected(Function.createDelegate(this,this.selectPart));this._ctlBOMItems&&this._ctlBOMItems.addStartGetData(Function.createDelegate(this,this.mainInfoStartGetData));this._ctlBOMItems&&this._ctlBOMItems.addGotDataOK(Function.createDelegate(this,this.mainInfoGotDataOK));this._ctlBOMItems&&this._ctlBOMItems.addGetDataComplete(Function.createDelegate(this,this.ctlBOMItems_GetDataComplete));this._ctlBOMItems&&this._ctlBOMItems.addCallBeforeRelease(Function.createDelegate(this,this.ctlBOMItems_addCallBeforeRelease));this._ctlMainInfo&&this._ctlMainInfo.addCallBeforeRelease(Function.createDelegate(this,this.ctlMainInfo_addCallBeforeRelease));this._ctlBOMItems&&this._ctlBOMItems.addRefereshAfterRelease(Function.createDelegate(this,this.ctlBOMItems_RefereshAfterRelease));this._ctlPOHubSourcing&&this._ctlPOHubSourcing.addSourcingResultAdded(Function.createDelegate(this,this.ctlSourcing_SourcingResultAdded));this._ctlBomCSV&&this._ctlBomCSV.getData();Rebound.GlobalTrader.Site.Pages.Orders.ClientBOMDetail.callBaseMethod(this,"goInit")},dispose:function(){this.isDisposed||(this._ctlMainInfo&&this._ctlMainInfo.dispose(),this._ctlPOHubSourcing&&this._ctlPOHubSourcing.dispose(),this._ctlMainInfo=null,this._ctlBOMItems=null,this._ctlPOHubSourcing=null,this._ctlClientBomCSV=null,this._pnlStatus=null,this._lblStatus=null,this._ctlClientBOMCsvExportHistory=null,this._ctlExpediteHistory=null,this._ctlClientBOMHubImagesDragDrop=null,this._ctlBomCSV=null,Rebound.GlobalTrader.Site.Pages.Orders.ClientBOMDetail.callBaseMethod(this,"dispose"))},selectPart:function(){this._ctlBOMItems._isClosed||(this._intCustomerRequirementID=this._ctlBOMItems._intCustomerRequirementID,this._ctlPOHubSourcing&&(this._ctlPOHubSourcing._intCustomerRequirementID=this._intCustomerRequirementID),this._ctlPOHubSourcing&&(this._ctlPOHubSourcing._blnRequirementClosed=this._ctlBOMItems._blnRequirementClosed),this._ctlPOHubSourcing&&this._ctlPOHubSourcing.selectPart(this._ctlBOMItems.getSelectedPartNo()),this._ctlPOHubSourcing&&this._ctlPOHubSourcing.updateIPOClientId(this._ctlBOMItems.getIPOClientNo()),this._ctlPOHubSourcing&&(this._ctlPOHubSourcing._blnRequirementReleased=this._ctlBOMItems._blnRequirementReleased||this._ctlBOMItems._isNoBid&&this._ctlBOMItems._blnPOHub))},ctlSourcing_SourcingResultAdded:function(){this._ctlMainInfo.getData();this._ctlPOHubSourcing&&this._ctlPOHubSourcing.hideAddForm()},ctlClientBOMSourcingResults_SourcingResultDeleted:function(){this._ctlMainInfo.getData();this._ctlMainInfo.enableDisableReleaseButton(this._ctlBOMItems._allExistInSourcingResult)},mainInfoStartGetData:function(){this._ctlPOHubSourcing&&this._ctlPOHubSourcing.show(!1);this._ctlClientBOMHubImagesDragDrop&&this._ctlClientBOMHubImagesDragDrop.show(!1)},mainInfoGotDataOK:function(){},ctlBOMItems_RefereshAfterRelease:function(){this._ctlMainInfo.getData()},mainBOMDetailGotDataOK:function(){$R_FN.setInnerHTML(this._lblStatus,this._ctlMainInfo.getFieldValue("hidDisplayStatus"));this._ctlMainInfo.getFieldValue("hidDisplayStatus")&&(this._ctlBOMItems._inActive=this._ctlMainInfo._inActive);this._ctlBOMItems._blnRequestedToPoHub=this._ctlMainInfo._blnRequestedToPoHub;this._ctlBOMItems._isClosed=this._ctlMainInfo._isClosed;this._ctlBOMItems._isClosedOnly=this._ctlMainInfo.getFieldValue("hidDisplayStatus")=="CLOSED"?!0:!1;this._ctlBOMItems._intContact2No=this._ctlMainInfo._intContact2No;this._ctlBOMItems._intClientStatus=this._ctlMainInfo._ClientBOMStatus;this._ctlBOMItems._intClientCompanyId=this._ctlMainInfo._ClientCompanyId;this._ctlBOMItems&&this._ctlBOMItems.getData()},ctlBOMItems_GetDataComplete:function(){this._ctlMainInfo._blnHasRequirement=this._ctlBOMItems._intCountStock>0;this._ctlMainInfo.enableButtons(!0);this._ctlMainInfo._isPurchaseHub&&this._ctlMainInfo.disableNotifyAndExportButton(!this._ctlMainInfo._isPurchaseHub);this._ctlMainInfo.enableDisableReleaseButton(this._ctlBOMItems._allExistInSourcingResult);this._ctlExpediteHistory&&this._ctlExpediteHistory.GetExpediteHistory()},ctlClientBOMSourcingResults_GetDataComplete:function(){},ctlClientBOMSourcingResults_addCallBeforeRelease:function(){},ctlBOMItems_addCallBeforeRelease:function(){var n=this.validateDeliveryDate(this._ctlBOMItems._blnAllHasDelDate&&this._ctlBOMItems._blnAllHasProduct);this._ctlBOMItems._blnCanRelease=n},ctlMainInfo_addCallBeforeRelease:function(){this._ctlBOMItems.clearMessages();var n=this.validateReqItemDeliveryDate(this._ctlBOMItems._blnAllItemHasDelDate&&this._ctlBOMItems._blnAllItemHasProduct);this._ctlMainInfo._blnCanReleaseAll=n},validateDeliveryDate:function(n){return n?!0:!1},validateReqItemDeliveryDate:function(n){return this._ctlBOMItems.clearMessages(),n?!0:(this._ctlBOMItems.addMessage("Delivery date and product is required for all attached sourcing results",$R_ENUM$MessageTypeList.Error),!1)},validateProduct:function(n){return n?!0:!1},validateReqItemProduct:function(n){return this._ctlBOMItems.clearMessages(),n?!0:(this._ctlBOMItems.addMessage("Delivery date and product is required for all attached sourcing results",$R_ENUM$MessageTypeList.Error),!1)},ctlClientBOMSourcingResults_SaveEditComplete:function(){this._ctlMainInfo.getData()},ctlMainInfo_SaveEditComplete:function(){this._ctlLines.getData()},printHUBRFQ:function(){this._BomId=this._ctlMainInfo._intBOMID;$R_FN.openPrintWindow($R_ENUM$PrintObject.PrintHUBRFQ,this._BomId)},emailHUBRFQ:function(){this._BomId=this._ctlMainInfo._intBOMID;$R_FN.openPrintWindow($R_ENUM$PrintObject.EmailHUBRFQ,this._BomId,!0)},printOtherDocs:function(){this._btnPrint._strExtraButtonClickCommand=="PrintLog"&&$R_FN.openPrintLogWindow($R_ENUM$PrintObject.PrintLog,this._BomId,!1,"PrintHUBRFQ")},ctlClientBOMSourcingResults_AddFormShown:function(){},ctlClientBOMSourcingResults_SourcingResultSelect:function(){}};Rebound.GlobalTrader.Site.Pages.Orders.ClientBOMDetail.registerClass("Rebound.GlobalTrader.Site.Pages.Orders.ClientBOMDetail",Rebound.GlobalTrader.Site.Pages.Content,Sys.IDisposable);