
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE PROCEDURE [dbo].[usp_KubGetTotalLineInvoicedDetails_Cache]     
@PartNo NVARCHAR(100)=null,    
@ClientID INT=null,
@RequirementId	INT=0    
AS   
/*  
 * Action: Created  By: <PERSON><PERSON><PERSON><PERSON>  Date:03/08/2023  Comment: Add new for quote data cache.  
 */     
BEGIN      
SET NOCOUNT ON;
DECLARE @CompanyNo INT=0
SELECT TOP 1 @CompanyNo= CompanyNo FROM tbCustomerRequirement WHERE CustomerRequirementId=@RequirementId    
IF((SELECT COUNT(1) FROM tb_KubGetTotalLineInvoicedDetailsCache   
WHERE Part=@PartNo AND ClientNo=@ClientID
AND CompanyNo=@CompanyNo
AND DLUP>DATEAdd(DAY,-3,GETDATE()))=0)  
BEGIN  
  
DELETE  FROM tb_KubGetTotalLineInvoicedDetailsCache   
WHERE Part=@PartNo AND ClientNo=@ClientID 
AND CompanyNo=@CompanyNo   
  
  INSERT INTO tb_KubGetTotalLineInvoicedDetailsCache  
  SELECT   
  i.ClientNo,  
  il.FullPart,   
  COUNT(il.InvoiceLineId),  
  GETDATE(),
  @CompanyNo     
  FROM  tbInvoiceLine AS il    
  LEFT JOIN tbInvoice AS i ON il.InvoiceNo = i.InvoiceId    
  WHERE i.InvoiceDate > DATEADD(m, -12, CURRENT_TIMESTAMP)    
  AND il.FullPart=@PartNo AND i.ClientNo=@ClientID 
  AND i.CompanyNo=(SELECT TOP 1 CompanyNo  FROM tbCustomerRequirement 
  WHERE CustomerRequirementId=@RequirementId) 
  GROUP BY i.ClientNo,il.FullPart   
END  
SET NOCOUNT OFF;  
END   
GO


