﻿@{
    Layout = null;
}

<!DOCTYPE html>

<html lang="en" xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta charset="utf-8" />
    <title>BOM Manager Sourcing</title>
    <link rel="stylesheet" href="~/Areas/BOM/css/BOMManagerPOHub.css">

    @*<link rel='stylesheet' href='https://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/4.1.3/css/bootstrap.min.css'>*@
    <link href="~/Areas/BOM/css/po-hub-libs/4.1.3-bootstrap.min.css" rel="stylesheet" />

    @*<script src='https://cdnjs.cloudflare.com/ajax/libs/jquery/3.4.1/jquery.min.js'></script>
        <script src='https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.15.0/popper.min.js'></script>
        <script src='https://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/4.3.1/js/bootstrap.min.js'></script>
        <script src='https://code.jquery.com/jquery-2.2.4.min.js'></script>
        <script src='https://maxcdn.bootstrapcdn.com/bootstrap/3.3.6/js/bootstrap.min.js'></script>*@
    <script src="~/Areas/BOM/js/po-hub-libs/3.4.1-jquery.min.js"></script>
    <script src="~/Areas/BOM/js/po-hub-libs/1.15.0-popper.min.js"></script>
    <script src="~/Areas/BOM/js/po-hub-libs/4.3.1-bootstrap.min.js"></script>
    <script src="~/Areas/BOM/js/po-hub-libs/jquery-2.2.4.min.js"></script>
    <script src="~/Areas/BOM/js/po-hub-libs/3.3.6-bootstrap.min.js"></script>

    <!-- Latest compiled and minified CSS -->
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.6/css/bootstrap.min.css" integrity="sha384-1q8mTJOASx8j1Au+a5WDVnPi2lkFfwwEAa8hDDdjZlpLegxhjVME1fgjWPGmkzs7" crossorigin="anonymous">
    @*<link href="~/Areas/BOM/css/po-hub-libs/3.3.6-bootstrap.min.css" rel="stylesheet" />*@

    @*<script src="https://unpkg.com/jquery@2.2.4/dist/jquery.js"></script>*@
    <script src="~/Areas/BOM/js/po-hub-libs/2.2.4-dist-jquery.js"></script>

    <!--jQueryUI version 1.11.4 -->
    @*<link rel="stylesheet" href="https://code.jquery.com/ui/1.11.4/themes/smoothness/jquery-ui.css" />
        <script src="https://code.jquery.com/ui/1.11.4/jquery-ui.min.js"></script>
        <link rel="stylesheet" href="//code.jquery.com/ui/1.13.2/themes/base/jquery-ui.css">*@
    <link href="~/Areas/BOM/css/po-hub-libs/1.11.4-themes-smoothness-jquery-ui.css" rel="stylesheet" />
    <script src="~/Areas/BOM/js/po-hub-libs/1.11.4-jquery-ui.min.js"></script>
    @*<link href="~/Areas/BOM/css/po-hub-libs/1.13.2-themes-base-jquery-ui.css" rel="stylesheet" />*@
    <link rel="stylesheet" href="//code.jquery.com/ui/1.13.2/themes/base/jquery-ui.css">
    <script src="~/Areas/BOM/js/POHubDatePicker.js"></script>
    <!--ParamQuery Grid css files-->
    <link rel="stylesheet" href="~/paramquery-8.1.0/pqgrid.dev.css" />
    <!--add pqgrid.ui.css for jQueryUI theme support-->
    <link rel="stylesheet" href="~/paramquery-8.1.0/pqgrid.ui.dev.css" />
    <!--ParamQuery Grid custom theme e.g., office, bootstrap, rosy, chocolate, etc (optional)-->
    @*<link rel="stylesheet" href="~/paramquery-8.1.0/themes/bootstrap/pqgrid.css" />*@   @*commented this file  version1.0*@
    <!--ParamQuery Grid js files-->
    <script type="text/javascript" src="~/paramquery-8.1.0/pqgrid.dev.js"></script>
    <!--ParamQuery Grid localization file-->
    <script src="~/paramquery-8.1.0/localize/pq-localize-en.js"></script>
    <!--Include pqTouch file to provide support for touch devices (optional)-->
    <script type="text/javascript" src="~/paramquery-8.1.0/pqTouch/pqtouch.min.js"></script>
    <!--Include jsZip file to support xlsx and zip export (optional)-->
    <script type="text/javascript" src="~/paramquery-8.1.0/jsZip-2.5.0/jszip.min.js"></script>

    <link href="~/Areas/BOM/css/bom-manager-order-po-hub.css" rel="stylesheet" />
</head>
<body>
    <div class="boxContent">
        <div class="pageTitle">
            <h3>BOM Manager Sourcing</h3>
        </div>
        <div class="BomLink ">
            <a id="Bomlink"><span></span></a><br>
            <a class="itemTitle" id="BomName"><span></span></a>
            @*<span class="BomLink">Status</span>&nbsp;<span>open</span>*@
            <br>
            <a class="itemTitle" id="Company"><span></span></a><br>
            <a id="SalesPerson"><span></span></a><br>
        </div>
        <div class="pageTitleItem ">

            <span class="itemTitle">Status:</span>&nbsp; <label id="BOMStatus"></label>
            <label style="float:right" id="ItemsSourcedCount"></label>
            <input type="hidden" id="hdBomStatus" />
        </div>
        <div class="pageTitleTopRight">
        </div>
    </div>
    <div id="divPartBom" class="section_container">
        <div class="section_one" id="headingThree">
            <div class="topTL"></div>
            <div class="topTR"></div>
            <div id="head_in_DivPartBom" class="head_in">
                <div class="top_inner">
                    <h4>Part BOM</h4>

                    <img id="refreshBOMItem" class="refresh" src="/Areas/BOM/Images/refresh.gif" / style="width:11px; height:10px; float:right; position:absolute; right:28px; top:4px;">
                    <a id="ShowPartBomDiv" role="button" class="sec_tab">

                    </a>

                    <div class="boxlink" id="divBOMItemButtons">
                        <a id="abc"><span class="linkbuttoncss Release" id="btnRelease">Release</span></a>
                        <a><span class="linkbuttoncss Recall" id="btnRecall">Recall</span></a>
                        <a><span class="linkbuttoncss NoBid" id="btnNoBid">No-Bid</span></a>
                        <a><span class="linkbuttoncss RecallNoBid" id="btnNoBidRecall">Recall No-Bid</span></a>
                        <a><span class="linkbuttoncss add_comm_note_disabled" id="btnAddCommunicationNote">Add New Communication Note</span></a>
                        <a><span class="linkbuttoncss CopyToClipboard" id="btnCopyToClipboard">Copy To Clipboard</span></a>
                        <input type="hidden" id="hdnPrimarySource" />
                        <input type="hidden" id="hdnCustomerRequirementId" />
                        <input type="hidden" id="hdnMultiQuote" />
                        <a href="#" id='LinkAddOffer' style="float:right; display:none;">Add Offer</a>
                    </div>
                </div>
            </div>
        </div>
        <div id="DivPartBomGrid">
            <div id="grid_md"></div>
        </div>

        <input type="hidden" id="hdnBomPartId" />
        <input type="hidden" id="hdnReqSttsBtBom" />
        <input type="hidden" id="hdnBomPartNumber" />
    </div>

    <div id="divParNote" class="section_container">
        <div class="section_one" id="headingThreeNote">
            <div class="topTL"></div>
            <div class="topTR"></div>
            <div id="head_in_DivPartNote" class="head_in">
                <div class="top_inner">
                    <h4>Communication Note</h4>

                    <img id="refreshNotesItem" class="refresh" src="/Areas/BOM/Images/refresh.gif" style="width:11px; height:10px; float:right; position:absolute; right:28px; top:4px;">
                    <a id="ShowNotesItemDiv" role="button" class="sec_tab"></a>
                </div>
            </div>
        </div>
        <div id="divNotesItemGrid">
            <div id="grid_md_notes"></div>
        </div>
    </div>

    <div class="section_container">
        <div class="section_one" id="headingThree">
            <div class="topTL"></div>
            <div class="topTR"></div>
            <div id="head_in_DivAutoSource" class="head_in">
                <div class="top_inner">
                    <h4>Auto Sourcing</h4>

                    <img id="refreshAutoSource" class="refresh" src="/Areas/BOM/Images/refresh.gif" / style="width:11px; height:10px; float:right; position:absolute; right:24px; top:4px;">
                    <a id="ShowAutoSourcediv" role="button" class="sec_tab">

                    </a>
                </div>
            </div>
        </div>
        <div id="divAutoSource">
            <div class="add_text">
                <label>** Auto-Sourcing is done on the basis of <b>Manufacturer Match</b> and <b>Lowest Price</b></label>
            </div>
            <div id="grid_AutoSource"></div>
        </div>

        <input type="hidden" id="hdnOfferId" />
    </div>

    <div class="section_container" id="auto-sourcing-details" style="display:none">
        <div class="section_one" id="headingThree">
            <div class="topTL"></div>
            <div class="topTR"></div>
            <div class="head_in" style="height:24px">
                <div class="top_inner">
                    <h4>Sourcing Results Details</h4>
                </div>
            </div>
        </div>
        <div class="panel-body">
            <table class="top_tbl">
                <tbody>
                    <tr>
                        <td width="33%">
                            <table class="first_tbl">
                                <tbody>
                                    <tr>
                                        <td class="desc">Manufacturer Name</td>
                                        <td class="item"><Label id="lblSRManufactuer"></Label></td>
                                    </tr>
                                    <tr>
                                        <td class="desc">Date Code</td>
                                        <td class="item"><Label id="lblSRDateCode"></Label></td>
                                    </tr>
                                    <tr>
                                        <td class="desc">Package Type</td>
                                        <td class="item"><Label id="lblSRPackageType"></Label></td>
                                    </tr>
                                    <tr>
                                        <td class="desc">Product Type</td>
                                        <td class="item"><Label id="lblSRProductType"></Label></td>
                                    </tr>
                                    <tr>
                                        <td class="desc">Supplier Warranty</td>
                                        <td class="item"><Label id="lblSRSupplierWarrantly"></Label></td>
                                    </tr>
                                    <tr>
                                        <td class="desc">Testing Recommended</td>
                                        <td class="item"><Label id="lblSRTestingRecommended"></Label></td>
                                    </tr>
                                    <tr>
                                        <td class="desc">Country Of Origin</td>
                                        <td class="item"><Label id="lblSRCountryOfOrigin"></Label></td>
                                    </tr>
                                </tbody>
                            </table>
                        </td>
                        <td width="33%">
                            <table class="first_tbl">
                                <tbody>
                                    <tr>
                                        <td class="desc">Minimum Order Quantity (MOQ)</td>
                                        <td class="item"><Label id="lblSRMoq"></Label></td>
                                    </tr>
                                    <tr>
                                        <td class="desc">Total quantity of stock available</td>
                                        <td class="item"><Label id="lblSRTotalQuantity"></Label></td>
                                    </tr>
                                    <tr>
                                        <td class="desc">Last time buy (LTB) - (Y/N)</td>
                                        <td class="item"><Label id="lblSRLastTimeBuy"></Label></td>
                                    </tr>
                                    <tr>
                                        <td class="desc">Notes</td>
                                        <td class="item"><Label id="lblSRNotes"></Label></td>
                                    </tr>
                                    <tr>
                                        <td class="desc">Images attached</td>
                                        <td class="item"><Label id="lblSRImagesAttached"></Label></td>
                                    </tr>
                                </tbody>
                            </table>
                        </td>
                        <td width="33%">
                            <table class="first_tbl">
                                <tbody>
                                    <tr>
                                        <td class="desc">Standard Pack Quantity (SPQ)</td>
                                        <td class="item"><Label id="lblSrSPQ"></Label></td>
                                    </tr>
                                    <tr>
                                        <td class="desc">Lead Time</td>
                                        <td class="item"><Label id="lblSrLeadTime"></Label></td>
                                    </tr>
                                    <tr>
                                        <td class="desc">RoHS</td>
                                        <td class="item"><Label id="lblSrRoHS"></Label></td>
                                    </tr>
                                    <tr>
                                        <td class="desc">Factory Sealed (Y/N)</td>
                                        <td class="item"><Label id="lblSrFactorySealed"></Label></td>
                                    </tr>
                                    <tr>
                                        <td class="desc">MSL</td>
                                        <td class="item"><Label id="lblSrMSL"></Label></td>
                                    </tr>
                                </tbody>
                            </table>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <div class="section_one" id="headingSourcing">
        <div class="topTL"></div>
        <div class="topTR"></div>
        <div id="head_in_DivAvailableSourcing" class="head_in">
            <div class="top_inner">
                <h4>Available Sourcing</h4>
                <a id="ShowAvailableSourcingDiv" role="button" class="sec_tab">

                </a>

            </div>
        </div>
    </div>

    <div id="DivAvailableSourcing">
        <div class="tab">
        </div>

        <div id="divSecondTab" class="tabcontent1 nested_tab">
            @*Nested tab class added Version1.0*@
            <div class="tab">
                <button id="btnxmatch" class="tablinks" onclick="OpenSecondTab(event, 'divXMatchSource')">X-Match / Sourcing</button>
                <button class="tablinks" onclick="OpenSecondTab(event, 'divEMSOffer')">Offers</button>
                <button class="tablinks" onclick="OpenSecondTab(event, 'divAPI')">API Results</button>
                <button class="tablinks" onclick="OpenSecondTab(event, 'divLyticaAPI')">Lytica API</button>


                <div id="divXMatchSource" class="tabcontent2" style="background-color:#C2FFB8;">
                    <div class="add_text">
                        <label>** Only Last 6 Months Data Will be Shown</label>
                    </div>
                    <div id="grid_xmSourcing"></div>
                </div>

                <div id="divEMSOffer" class="tabcontent2" style="background-color:#C2FFB8;">
                    <div class="add_text">
                        <label>** Only Last 6 Months Data Will be Shown</label>
                        <a href="#" id='LinkAddOfferPart' style="float:right; display:none;">Add Offer/Alternate Offer</a>
                    </div>
                    <div id="grid_EmsOffer"></div>
                </div>
                <div id="divAPI" class="tabcontent2 api_section">
                    <div class="add_text">
                        <label>** Only Last 6 Months Data Will be Shown</label>
                    </div>
                    <div id="grid_API"></div>
                </div>
                <div id="divLyticaAPI" class="tabcontent2 api_section">
                    <div class="add_text">
                        <label>** Only Last 6 Months Data Will be Shown</label>
                        <label style="float:right;">Lytica API Log: <span id="Lyticalogcount"></span> </label>
                    </div>
                    <div id="grid_Lytica_API"></div>
                </div>


            </div>
        </div>
    </div>

    <div id="dvLoading" style="display: none">
    </div>




    <div id="CloseHubModal" class="modal">

        <!-- Modal content -->
        <div class="modal-content" style="width: 100px;">
            <img src="~/Areas/BOM/Images/processing_image.gif" />


            <!--changes for version.3 ends -->
        </div>
    </div>
    <div class="modal bd-example-modal-lg" id="AddOfferModal" role="dialog">
        <div>
            <div class="modal-content" style="background-color: #AAE2A0;">
                <div class="main_header">
                    <h6>Add Offer</h6>
                    <a id="btnHeaderSave" class="BtnSubmitEdit" rel="modal:close">Save</a>
                    &nbsp;&nbsp;&nbsp;&nbsp;
                    <a id="btnHeaderCancel" class="BtnCloseEdit" rel="modal:close">Cancel</a>

                </div>
                <div class="form_container">
                    <div class="header_text">
                        <h4>Add Offer</h4>
                        <div class="formInstructions">Enter the details of the offer and press Save (You will be able to Add Offers for Parts available in this BOM only)</div>
                    </div>
                    <div class="modal-body">
                        <div style="display:none; margin-bottom:20px" id="AddOfferValidationError" class="errorSummary">
                            There were some problems with your form<br>Please check below and try again.
                        </div>
                        <div id="divAddAltOffer">
                            <span style="color: #b9f0b2;">Part No. :</span> <label id="lblOfferPartName" style="color: #b9f0b2;">PartName</label>
                            <br />
                            <input type="radio" name="OfferType" id="rdAddoffer" value="1" /><span style="color: #b9f0b2;">Add Offer</span>
                            <input type="radio" name="OfferType" id="rdAddAltoffer" value="2" /><span style="color: #b9f0b2;">Add Alternate Offer</span>
                        </div>
                        <br />
                        <table class="offer_tbl">
                            <tr id="RowAddOfferSupplier">
                                <td class="column-title">
                                    Supplier<span class="mandetory">*</span>
                                </td>
                                <td class="column-value">
                                    <div>
                                        <input id="textSupplier" type="text" placeholder="Type 2 chars to search" />
                                        <input type="hidden" id="hdsupplier" />
                                    </div>
                                    <div class="row">
                                        <div class="col">
                                            <label id="LabelSupplier"></label>
                                            <a id="RemoveSupplier" style="display:none">[Reselect]</a>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            <tr class="row-error" style="display:none" id="RowAddOfferSupplierError">
                                <td class="column-title">&nbsp;</td>
                                <td class="column-value">
                                    <div class="formMessages">Please enter a value</div>
                                </td>
                            </tr>
                            <tr id="RowAddOfferPartNo">
                                <td class="column-title">
                                    Part No<span class="mandetory">*</span>
                                </td>
                                <td class="column-value">
                                    <div class="row">
                                        <div class="col leftsec">
                                            <input id="textPartNo" type="text" required="required" placeholder="Type 4 chars or click on Search" />
                                            <input type="hidden" id="hdpartid" />
                                        </div>
                                        <div id="partsearchdiv" class="col" style="flex-grow: 2; left:-26px;">
                                            <img style="cursor:pointer;" id="PartSearch" src="../../../../App_Themes/Original/images/autosearch/form.gif" alt="Search Part" />
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col">
                                            <label id="LabelPartNo"></label>
                                            <a id="RemovePartNo" style="display:none">[Reselect]</a>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            <tr class="row-error" style="display:none" id="RowAddOfferPartNoError">
                                <td class="column-title">&nbsp;</td>
                                <td class="column-value">
                                    <div class="formMessages">Please enter a value</div>
                                </td>
                            </tr>
                            <tr>
                                <td class="column-title">
                                    ROHS
                                </td>
                                <td class="column-value">
                                    <div>
                                        <div>
                                            <select id="ddlROHS"></select><a id="RefreshROHS" class="refresh_icon"></a>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            <tr id="RowAddOfferManufacturer">
                                <td class="column-title">
                                    Manufacturer<span class="mandetory">*</span>
                                </td>
                                <td class="column-value">
                                    <div class="row">
                                        <div class="col">
                                            <input id="textManufacturer" type="text" placeholder="Type 2 chars to search" />
                                            <input type="hidden" id="hdmrfid" />

                                            <div class="row">
                                                <div class="col">
                                                    <label id="LabelManufacturer"></label>
                                                    <a id="RemoveManufacturer" style="display:none">[Reselect]</a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            <tr class="row-error" style="display:none" id="RowAddOfferManufacturerError">
                                <td class="column-title">&nbsp;</td>
                                <td class="column-value">
                                    <div class="formMessages">Please enter a value</div>
                                </td>
                            </tr>
                            <tr>
                                <td class="column-title">
                                    Country Of Origin
                                </td>
                                <td class="column-value">
                                    <div>
                                        <div>
                                            <select id="ddlCountryOfOrigin"></select><a id="RefreshCOO" class="refresh_icon"></a>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td class="column-title">
                                    Date Code
                                </td>
                                <td class="column-value">
                                    <input id="textdatecode" type="text" />
                                </td>
                            </tr>
                            <tr id="RowAddOfferProduct">
                                <td class="column-title">
                                    Product<span class="mandetory">*</span>
                                </td>
                                <td class="column-value">
                                    <div class="row">
                                        <div class="col">
                                            <input id="textProductName" type="text" style="width:450px;" placeholder="Type 2 chars to search" />
                                            <input type="hidden" id="prodid" />
                                            <div class="row">
                                                <div class="col">
                                                    <label id="LabelProduct"></label>
                                                    <span>
                                                        <a id="RemoveProduct" style="display:none">[Reselect]</a>
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            <tr class="row-error" style="display:none" id="RowAddOfferProductError">
                                <td class="column-title">&nbsp;</td>
                                <td class="column-value">
                                    <div class="formMessages">Please enter a value</div>
                                </td>
                            </tr>
                            <tr>
                                <td class="column-title">
                                    Package
                                </td>
                                <td class="column-value">
                                    <div class="row">
                                        <div class="col">
                                            <input id="textPackage" type="text" placeholder="Type 2 chars to search" />
                                            <input type="hidden" id="pkgid" />
                                            <div class="row">
                                                <div class="col">
                                                    <label id="LabelPackage"></label>
                                                    <a id="RemovePackage" style="display:none">[Reselect]</a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            <tr id="RowAddOfferQuantity">
                                <td class="column-title">
                                    Quantity<span class="mandetory">*</span>
                                </td>
                                <td class="column-value">
                                    <input id="textQuantity" type="number" required="required" autofocus="autofocus" onkeydown="limitCharactersOnNumberField(event)"/>
                                </td>
                            </tr>
                            <tr class="row-error" style="display:none" id="RowAddOfferQuantityError">
                                <td class="column-title">&nbsp;</td>
                                <td class="column-value">
                                    <div class="formMessages">Please enter a value</div>
                                </td>
                            </tr>
                            <tr id="RowAddOfferPrice">
                                <td class="column-title">
                                    Buy Price<span class="mandetory">*</span>
                                </td>
                                <td class="column-value">
                                    <input id="textPrice" type="number" required="required" autofocus="autofocus" onkeydown="limitCharactersOnNumberField(event)"/>
                                </td>
                            </tr>
                            <tr class="row-error" style="display:none" id="RowAddOfferPriceError">
                                <td class="column-title">&nbsp;</td>
                                <td class="column-value">
                                    <div class="formMessages">Please enter a value</div>
                                </td>
                            </tr>
                            <tr id="RowAddOfferSellPrice">
                                <td class="column-title">
                                    Uplift Sell Price<span class="mandetory">*</span>
                                </td>
                                <td class="column-value">
                                    <input id="textSellPrice" type="number" required="required" autofocus="autofocus" onkeydown="limitCharactersOnNumberField(event)"/>
                                </td>
                            </tr>
                            <tr class="row-error" style="display: none" id="RowAddOfferSellPriceError">
                                <td class="column-title">&nbsp;</td>
                                <td class="column-value">
                                    <div class="formMessages">Please enter a value</div>
                                </td>
                            </tr>
                            <tr id="RowAddOfferShippingCost">
                                <td class="column-title">
                                    Estimated Shipping Cost
                                </td>
                                <td class="column-value">
                                    <input id="textShippingCost" type="number" required="required" autofocus="autofocus" />
                                </td>
                            </tr>
                            <tr id="RowAddOfferCurrency">
                                <td class="column-title">
                                    Sell Currency<span class="mandetory">*</span>
                                </td>
                                <td class="column-value">
                                    <select id="ddlCurrency" required="required" autofocus="autofocus"></select>
                                    <a id="RefreshCurrency" class="refresh_icon"></a>
                                </td>
                            </tr>
                            <tr class="row-error" style="display:none" id="RowAddOfferCurrencyError">
                                <td class="column-title">&nbsp;</td>
                                <td class="column-value">
                                    <div class="formMessages">Please select a value</div>
                                </td>
                            </tr>
                            <tr>
                                <td class="column-title">
                                    Offer Status
                                </td>
                                <td class="column-value">
                                    <select id="ddlOfferStatus"></select><a id="RefreshOfferStatus" class="refresh_icon"></a>
                                </td>
                            </tr>
                            <tr style="height:36px !important">
                                <td class="column-title">
                                    Total Quantity of Stock Available
                                </td>
                                <td class="column-value">
                                    <input id="texttotalQuantityStockAvil" type="number" onkeydown="limitCharactersOnNumberField(event)"/>
                                </td>
                            </tr>
                            <tr style="height:36px !important">
                                <td class="column-title">
                                    Minimum Order Quantity (MOQ)
                                </td>
                                <td class="column-value">
                                    <input id="textMinimumOderderQuantity" type="number" onkeydown="limitCharactersOnNumberField(event)"/>
                                </td>
                            </tr>
                            <tr>
                                <td class="column-title">
                                    Last time buy (LTB) - (Y/N)
                                </td>
                                <td class="column-value">
                                    <input id="textlastTimeBuy" type="text" />
                                </td>
                            </tr>
                            <tr>
                                <td class="column-title">
                                    MSL
                                </td>
                                <td class="column-value">
                                    <select id="ddlMSL">
                                    </select><a id="RefreshMSL" class="refresh_icon"></a>
                                </td>
                            </tr>
                            <tr style="height:36px !important">
                                <td class="column-title">
                                    Standard Pack Quantity (SPQ)
                                </td>
                                <td class="column-value">
                                    <input id="textSPQ" type="number" onkeydown="limitCharactersOnNumberField(event)"/>
                                </td>
                            </tr>
                            <tr>
                                <td class="column-title">
                                    Lead Time
                                </td>
                                <td class="column-value">
                                    <input id="textLeadTime" type="text" />
                                </td>
                            </tr>
                            <tr id="RowAddOfferRegion">
                                <td class="column-title">
                                    Region
                                </td>
                                <td class="column-value">
                                    <select id="ddlRegion">
                                    </select><a id="RefreshRegion" class="refresh_icon"></a>
                                </td>
                            </tr>
                            <tr id="RowAddOfferDeliveryDate">
                                <td class="column-title">Delivery date</td>
                                <td class="column-value">
                                    <input type="text" id="textAddDeliveryDate" class="delivery-date" autocomplete="off" onkeydown="preventKeyboardInput(event)">
                                </td>
                            </tr>
                            <tr>
                                <td class="column-title">
                                    Factory Sealed (Y/N)
                                </td>
                                <td class="column-value">
                                    <input id="textFactorySealedYN" type="text" />
                                </td>
                            </tr>
                            <tr>
                                <td class="column-title">
                                    ROHS Status (Y/N)
                                </td>
                                <td class="column-value">
                                    <input id="textROHSStatusYN" type="text" />
                                </td>
                            </tr>
                            <tr>
                                <td class="column-title">
                                    Supplier Warranty
                                </td>
                                <td class="column-value">
                                    <input id="textSupplierWarranty" class="supplier-warranty" type="number" onkeydown="limitCharactersOnNumberField(event)"/><label>days</label>
                                </td>
                            </tr>
                            <tr>
                                <td class="column-title">
                                    Notes
                                </td>
                                <td class="column-value">
                                    <input id="textNotes" type="text" style="width:400px;" maxlength="128" />
                                </td>
                            </tr>
                            <tr>
                                <td class="column-title">Testing Recommended</td>
                                <td class="column-value"><input type="checkbox" id="chkTestingRecommended" /></td>
                            </tr>
                        </table>

                        <table class="offer_tbl tbl_reason">
                            <tr id="RowUplipSellPriceLess" style="display:none">
                                <td></td>
                                <td class="leftsec">
                                    <div>
                                        <div class="formMessages" style="color: #FFFF00;">Uplift Sell Price is Less than Buy Price</div>
                                    </div>
                                </td>
                            </tr>
                            <tr id="RowUplipSellPriceLessReason" style="height:100px !important; display:none">
                                <td style="width:12% !important">Reason <span class="mandetory">*</span></td>
                                <td class="leftsec">
                                    <div>
                                        <Textarea style=" color: #000 !important; font-weight: normal !important; height: 99px; width: 100%;" id="textUplipSellPriceLessReason" type="text" required="required"></Textarea>
                                    </div>
                                </td>
                            </tr>
                            <tr id="RowUplipSellPriceLessReasonError" style="display:none; background-color:#990000">
                                <td></td>
                                <td class="leftsec">
                                    <div>
                                        <div class="formMessages">Reason is Required when Uplift Sell Price is less than Buy Price</div>
                                    </div>
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>
                <div class="formNotes">
                    <span class="mandetory">*</span> denotes a required field
                </div>
                <div class="footer_area">
                    <a id="btnFooterSave" class="BtnSubmitEdit" rel="modal:close">Save</a> <!--changes for version.3-->
                    &nbsp;&nbsp;&nbsp;&nbsp;
                    <a id="btnFooterCancel" class="BtnCloseEdit" rel="modal:close">Cancel</a><!--changes for version.3-->
                </div>
            </div>
        </div>
    </div>
        <div class="modal bd-example-modal-lg" id="EditSourceModal" role="dialog">
            <div>
                <div class="modal-content" style="background-color: #AAE2A0;">
                    <div class="main_header">
                        <h6>Edit Sourcing</h6>
                        <a id="btnSaveSource1" class="SaveSource BtnSubmitEdit" rel="modal:close">Save</a>
                        &nbsp;&nbsp;&nbsp;&nbsp;
                        <a id="btnCancelSource1" class="CancelSource BtnCloseEdit" rel="modal:close">Cancel</a>

                    </div>
                    <div class="form_container">
                        <div class="header_text">
                            <h4></h4>
                            <div class="formInstructions">Enter the details of the sourcing and press Save</div>
                        </div>
                        <div class="modal-body edit_modal">
                            <div style="display:none; margin-bottom:20px" id="EditSourcingValidationError" class="errorSummary">
                                There were some problems with your form<br>Please check below and try again.
                            </div>
                            <table class="offer_tbl">
                                <tr>
                                    <td class="column-title">
                                        Supplier
                                    </td>
                                    <td class="column-value">
                                        <div>
                                            <label id="lblSupplierName"></label>
                                        </div>
                                    </td>
                                </tr>

                                <tr id="RowEditPartNo">
                                    <td class="column-title">
                                        Part No<span class="mandetory">*</span>
                                    </td>
                                    <td class="column-value">
                                        <div class="row" id="search-part">
                                            <div class="col">
                                                <input id="textEditPartNo" class="ui-autocomplete-input" type="text" required="required" placeholder="Type 4 chars or click on Search" />
                                                <input type="hidden" id="hiddenEditPartNo" />
                                                <img style="cursor:pointer;" id="part-search-icon" src="../../../../App_Themes/Original/images/autosearch/form.gif" alt="Search Part" />
                                            </div>
                                            @*<div class="col" style="flex-grow: 2; left:-25px;">
                                                    <img style="cursor:pointer;" id="part-search-icon" src="../../../../App_Themes/Original/images/autosearch/form.gif" alt="Search Part" />
                                                </div>*@
                                        </div>
                                        <div class="row" id="display-part" style="display:none">
                                            <div class="col">
                                                <label id="lblEditPart"></label>
                                                <a class="re-select" id="ReselectEditPartNo">[Reselect]</a>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                <tr class="row-error" style="display:none" id="RowEditPartNoError">
                                    <td class="column-title">&nbsp;</td>
                                    <td class="column-value">
                                        <div class="formMessages">Please enter a value</div>
                                    </td>
                                </tr>

                                <tr>
                                    <td class="column-title">
                                        ROHS
                                    </td>
                                    <td class="column-value">
                                        <select id="ddlEditROHS"></select>
                                        <a id="RefreshEditROHS" class="refresh_icon"></a>
                                    </td>
                                </tr>

                                <tr id="RowEditManufacturer">
                                    <td class="column-title">
                                        Manufacturer<span class="mandetory">*</span>
                                    </td>
                                    <td class="column-value">
                                        <div class="row" id="search-manufacturer">
                                            <div class="col">
                                                <input id="textEditManufacturer" class="ui-autocomplete-input" type="text" placeholder="Type 2 chars to search" />
                                                <input type="hidden" id="hiddenEditManufacturer" />
                                            </div>
                                        </div>
                                        <div class="row" id="display-manufacturer">
                                            <div class="col">
                                                <label id="lblEditManufacturer"></label>
                                                <a class="re-select" id="ReselectEditManufacturer">[Reselect]</a>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                <tr class="row-error" style="display:none" id="RowEditManufacturerError">
                                    <td class="column-title">&nbsp;</td>
                                    <td class="column-value">
                                        <div class="formMessages">Please enter a value</div>
                                    </td>
                                </tr>

                                <tr id="RowEditCOO">
                                    <td class="column-title">
                                        Country of Origin
                                    </td>
                                    <td class="column-value">
                                        <select id="ddlEditCOO"></select>
                                        <a id="RefreshCOO" class="refresh_icon"></a>
                                    </td>
                                </tr>

                                <tr>
                                    <td class="column-title">
                                        Date Code
                                    </td>
                                    <td class="column-value">
                                        <input id="textEditDateCode" type="text" />
                                    </td>
                                </tr>

                                <tr id="RowEditProduct">
                                    <td class="column-title">
                                        Product<span class="mandetory">*</span>
                                    </td>
                                    <td class="column-value">
                                        <div class="row" id="search-product">
                                            <div class="col">
                                                <input id="textEditProduct" type="text" style="width:450px;" placeholder="Type 2 chars to search" />
                                                <input type="hidden" id="hiddenEditProductId" />
                                            </div>
                                        </div>
                                        <div class="row" id="display-product">
                                            <div class="col" style="display:flex !important">
                                                <label id="lblEditProduct"></label>
                                                <a class="re-select" id="ReselectEditProduct">[Reselect]</a>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                <tr class="row-error" style="display:none" id="RowEditProductError">
                                    <td class="column-title">&nbsp;</td>
                                    <td class="column-value">
                                        <div class="formMessages">Please enter a value</div>
                                    </td>
                                </tr>

                                <tr id="RowEditPackage">
                                    <td class="column-title">
                                        Package
                                    </td>
                                    <td class="column-value">
                                        <div class="row" id="search-package">
                                            <div class="col">
                                                <input id="textEditPackage" class="ui-autocomplete-input" type="text" placeholder="Type 2 chars to search" />
                                                <input type="hidden" id="hiddenEditPackageId" />
                                            </div>
                                        </div>
                                        <div class="row" id="display-package">
                                            <div class="col">
                                                <label id="lblEditPackage"></label>
                                                <a class="re-select" id="ReselectEditPackage">[Reselect]</a>
                                            </div>
                                        </div>
                                    </td>
                                </tr>

                                <tr id="RowEditQuantity">
                                    <td class="column-title">
                                        Quantity<span class="mandetory">*</span>
                                    </td>
                                    <td class="column-value">
                                        <input id="textEditQuantity" type="number" onkeydown="limitCharactersOnNumberField(event)" />
                                    </td>
                                </tr>
                                <tr class="row-error" style="display:none" id="RowEditQuantityError">
                                    <td class="column-title">&nbsp;</td>
                                    <td class="column-value">
                                        <div class="formMessages">Please enter a value</div>
                                    </td>
                                </tr>

                                <tr id="RowEditBuyPrice">
                                    <td class="column-title">
                                        Buy Price<span class="mandetory">*</span>
                                    </td>
                                    <td class="column-value">
                                        <input id="textEditBuyPrice" required type="number"
                                               onkeydown="limitCharactersOnNumberField(event)"
                                               onblur="displayReasonSellPriceLess()" />
                                    </td>
                                </tr>
                                <tr class="row-error" style="display:none" id="RowEditBuyPriceError">
                                    <td class="column-title">&nbsp;</td>
                                    <td class="column-value">
                                        <div class="formMessages">Please enter a value</div>
                                    </td>
                                </tr>

                                <tr id="RowEditSellPrice">
                                    <td class="column-title">
                                        Uplift Sell Price<span class="mandetory">*</span>
                                    </td>
                                    <td class="column-value">
                                        <input id="textEditSellPrice" required type="number"
                                               onkeydown="limitCharactersOnNumberField(event)"
                                               onblur="displayReasonSellPriceLess()" />
                                    </td>
                                </tr>
                                <tr class="row-error" style="display:none" id="RowEditSellPriceError">
                                    <td class="column-title">&nbsp;</td>
                                    <td class="column-value">
                                        <div class="formMessages">Please enter a value</div>
                                    </td>
                                </tr>

                                <tr id="RowEditShippingCost">
                                    <td class="column-title">
                                        Estimated Shipping Cost
                                    </td>
                                    <td class="column-value">
                                        <input id="textEditShippingCost" type="number"
                                               onkeydown="limitCharactersOnNumberField(event)" />
                                    </td>
                                </tr>

                                <tr id="RowEditCurrency">
                                    <td class="column-title">
                                        Sell Currency<span class="mandetory">*</span>
                                    </td>
                                    <td class="column-value">
                                        <select id="ddlEditCurrency" required="required" autofocus="autofocus"></select>
                                        <a id="RefreshEditCurrency" class="refresh_icon"></a>
                                    </td>
                                </tr>
                                <tr class="row-error" style="display:none" id="RowEditCurrencyError">
                                    <td class="column-title">&nbsp;</td>
                                    <td class="column-value">
                                        <div class="formMessages">Please select a value</div>
                                    </td>
                                </tr>

                                <tr id="RowEditOfferStatus">
                                    <td class="column-title">
                                        Offer Status
                                    </td>
                                    <td class="column-value">
                                        <select id="ddlEditOfferStatus"></select>
                                        <a id="RefreshOfferStatus" class="refresh_icon"></a>
                                    </td>
                                </tr>

                                <tr id="RowEditTotalQSA">
                                    <td class="column-title">
                                        Total Quantity of Stock Available
                                    </td>
                                    <td class="column-value">
                                        <input id="textEditTotalQSA" type="number" onkeydown="limitCharactersOnNumberField(event)" />
                                    </td>
                                </tr>

                                <tr id="RowEditMOQ">
                                    <td class="column-title">
                                        Minimum Order Quantity (MOQ)
                                    </td>
                                    <td class="column-value">
                                        <input id="textEditMOQ" type="number" onkeydown="limitCharactersOnNumberField(event)" />
                                    </td>
                                </tr>

                                <tr id="RowEditLTB">
                                    <td class="column-title">
                                        Last time buy (LTB) - (Y/N)
                                    </td>
                                    <td class="column-value">
                                        <input id="textEditLTB" type="text" />
                                    </td>
                                </tr>

                                <tr id="RowEditMSL">
                                    <td class="column-title">
                                        MSL
                                    </td>
                                    <td class="column-value">
                                        <select id="ddlEditMSL"></select>
                                        <a id="RefreshEditMSL" class="refresh_icon"></a>
                                    </td>
                                </tr>

                                <tr id="RowEditSPQ">
                                    <td class="column-title">
                                        Standard Pack Quantity (SPQ)
                                    </td>
                                    <td class="column-value">
                                        <input id="textEditSPQ" type="number" onkeydown="limitCharactersOnNumberField(event)" />
                                    </td>
                                </tr>

                                <tr id="RowEditLeadTime">
                                    <td class="column-title">
                                        Lead Time
                                    </td>
                                    <td class="column-value">
                                        <input id="textEditLeadTime" type="text" />
                                    </td>
                                </tr>

                                <tr id="RowEditRegion">
                                    <td class="column-title">
                                        Region
                                    </td>
                                    <td class="column-value">
                                        <select id="ddlEditRegion"></select>
                                        <a id="RefreshEditRegion" class="refresh_icon"></a>
                                    </td>
                                </tr>

                                <tr id="RowEditDeliveryDate">
                                    <td class="column-title">Delivery date</td>
                                    <td class="column-value">
                                        <input type="text" class="delivery-date" id="textEditDeliveryDate" autocomplete="off" onkeydown="preventKeyboardInput(event)">
                                    </td>
                                </tr>

                                <tr id="RowEditFactorySealed">
                                    <td class="column-title">
                                        Factory Sealed (Y/N)
                                    </td>
                                    <td class="column-value">
                                        <input id="textEditFactorySealed" type="text" />
                                    </td>
                                </tr>

                                <tr id="RowEditROHSStatus">
                                    <td class="column-title">
                                        ROHS Status (Y/N)
                                    </td>
                                    <td class="column-value">
                                        <input id="textEditROHSStatus" type="text" />
                                    </td>
                                </tr>

                                <tr>
                                    <td class="column-title">
                                        Supplier Warranty
                                    </td>
                                    <td class="column-value">
                                        <input id="textEditSupplierWarranty" class="supplier-warranty" type="number" onkeydown="limitCharactersOnNumberField(event)" />
                                        <label> days</label>
                                    </td>
                                </tr>

                                <tr id="RowEditNotes">
                                    <td class="column-title">
                                        Notes
                                    </td>
                                    <td class="column-value">
                                        <input id="textEditNotes" type="text" style="width:400px" maxlength="128" />
                                    </td>
                                </tr>

                                <tr>
                                    <td class="column-title">
                                        Testing Recommended
                                    </td>
                                    <td class="column-value">
                                        <input type="checkbox" id="chkEditTestingRecommended" />
                                    </td>
                                </tr>
                            </table>

                            <table class="offer_tbl tbl_reason">
                                <tr id="RowEditSellPriceLess" style="display:none">
                                    <td></td>
                                    <td class="leftsec">
                                        <div>
                                            <div class="formMessages" style="color: #FFFF00;">Uplift Sell Price is Less than Buy Price</div>
                                        </div>
                                    </td>
                                </tr>
                                <tr id="RowEditSellPriceLessReason" style="height:100px !important; display:none">
                                    <td style="width:12% !important">Reason <span class="mandetory">*</span></td>
                                    <td class="leftsec">
                                        <div>
                                            <Textarea style=" color: #000 !important; font-weight: normal !important; height: 99px; width: 100%;" id="textEditSellPriceLessReason" type="text" required="required"></Textarea>
                                        </div>
                                    </td>
                                </tr>
                                <tr id="RowEditSellPriceLessReasonError" style="display:none; background-color:#990000">
                                    <td></td>
                                    <td class="leftsec">
                                        <div>
                                            <div class="formMessages">Reason is Required when Uplift Sell Price is less than Buy Price</div>
                                        </div>
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <!-- Modal Footer -->
                    </div>
                    <input type="hidden" id="hdnSourceId" />
                    <div class="formNotes">
                        <span class="mandetory">*</span> denotes a required field
                    </div>
                    <div class="footer_area">
                        <a id="btnSaveSource" class="SaveSource BtnSubmitEdit" rel="modal:close">Save</a> <!--changes for version.3-->
                        &nbsp;&nbsp;&nbsp;&nbsp;
                        <a id="btnCancelSource" class="CancelSource BtnCloseEdit" rel="modal:close">Cancel</a><!--changes for version.3-->
                    </div>
                </div>
            </div>
        </div>
        <div class="modal bd-example-modal-lg" id="ConfirmationBoxModal" role="dialog">
            <div>
                <div class="modal-content" style="background-color: #AAE2A0;">
                    <div class="main_header">
                        <h6>Replace Sourcing</h6>
                        <a id="btnConfirmModal1" class="ConfirmYes BtnSubmitEdit" rel="modal:close">Confirm</a>
                        &nbsp;&nbsp;&nbsp;&nbsp;
                        <a id="btnCancelModal1" class="ConfirmNo BtnCloseEdit" rel="modal:close">Cancel</a>

                    </div>
                    <div class="form_container">
                        <div class="header_text">
                            <h4></h4>
                            <div class="formInstructions">Confirmation to Replace Sourcing</div>
                        </div>
                        <div class="modal-body">
                            <div class="row">
                                <div class="col">
                                    <label>Are You Sure Want to Replace Sourcing? </label>
                                </div>
                                <div class="col">
                                    <label id="partNoConfirmation"></label>
                                    <input type="hidden" id="HDBOMManagaerID" value="false" />
                                    <input type="hidden" id="HDCustomerRequirementID" value="false" />
                                    <input type="hidden" id="HDSourcetype" value="false" />
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="footer_area">
                        <a id="btnConfirmModal" class="ConfirmYes BtnSubmitEdit" rel="modal:close">Confirm</a>
                        &nbsp;&nbsp;&nbsp;&nbsp;
                        <a id="btnCancelModal" class="ConfirmNo BtnCloseEdit" rel="modal:close">Cancel</a>
                    </div>
                </div>
            </div>
        </div>
        <div class="modal bd-example-modal-lg" id="XMatchSourcingModal" role="dialog">
            <div>
                <div class="modal-content" style="background-color: #AAE2A0;">
                    <div class="main_header">
                        <h6>XMatch Sourcing</h6>
                        <a id="btnXMConfirmModal1" class="ConfirmYesXMatch BtnSubmitEdit" rel="modal:close">Save</a>
                        &nbsp;&nbsp;&nbsp;&nbsp;
                        <a id="btnXMCancelModal1" class="ConfirmNoXMatch BtnCloseEdit" rel="modal:close">Cancel</a>

                    </div>
                    <div class="form_container">
                        <div class="header_text">
                            <h4>Enter Values for XMatch Sourcing</h4>
                            <div class="formInstructions">Enter Values for XMatch Sourcing</div>
                            <input type="hidden" id="XMSalesXMatchID" value="" />
                        </div>
                        <div class="modal-body">
                            <div class="row">
                                <div class="col-md-auto" style="width:120px">
                                    <label style="color: #fff;">MOQ </label>
                                </div>
                                <div class="col-md-auto" style="width:200px">
                                    <input type="number" id="XMMOQ" />
                                </div>
                            </div>
                            <div class="row" id="XMSPQRow">
                                <div class="col-md-auto" style="width:120px">
                                    <label style="color: #fff;">SPQ* </label>
                                </div>
                                <div class="col-md-auto" style="width:200px">
                                    <input type="number" id="XMSPQ" />
                                </div>
                                <div id="XMSPQError" class="col" style="display:none">
                                    <label>Please Enter a Value</label>
                                </div>
                            </div>
                            <div class="row" style="display:none">
                                <div class="col-md-auto" style="width:120px">
                                    <label style="color: #fff;">Stock Quantity </label>
                                </div>
                                <div class="col-md-auto" style="width:200px">
                                    <input type="number" id="XMStockQTY" />
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-auto" style="width:120px">
                                    <label style="color: #fff;">Date Code </label>
                                </div>
                                <div class="col-md-auto" style="width:200px">
                                    <input type="text" id="XMMDC" />
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-auto" style="width:120px">
                                    <label style="color: #fff;">Lead Time </label>
                                </div>
                                <div class="col-md-auto" style="width:200px">
                                    <input type="text" id="XMLT" />
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="footer_area">
                        <a id="btnXMConfirmModal" class="ConfirmYesXMatch BtnSubmitEdit" rel="modal:close">Save</a>
                        &nbsp;&nbsp;&nbsp;&nbsp;
                        <a id="btnXMCancelModal" class="ConfirmNoXMatch BtnCloseEdit" rel="modal:close">Cancel</a>
                    </div>
                </div>
            </div>
        </div>
        <div class="modal bd-example-modal-lg" id="SendBOMModal" role="dialog">
            <div>
                <div class="modal-content" style="background-color: #AAE2A0;">
                    <div class="main_header">
                        <h6 id="SenBOMTitle"></h6>
                        <a id="BackSendBOM1" style="display:none" class="BtnBackPH CancelSendBOM" href="#">Back</a>
                    </div>
                    <div class="form_container">
                        <div class="header_text">
                            <h4><label id="ActionText"></label></h4>
                            <div class="formInstructions"></div>
                        </div>
                        <div class="modal-body">
                            <div id="BOMPartsSourced" class="">
                                <div class="row">
                                    <div class="col-md-auto" style="width: 14%; float: left; color: #d3fFcC; font-weight:bold;">
                                        Please Confirm
                                    </div>
                                    <div class="col" style="width:85%; float:left;">
                                        <a id="btnSBConfirmModal1" class="ConfirmSendBOM yes_icon" rel="modal:close">Yes</a>&nbsp;
                                        <a id="btnSBCancelModal1" class="CancelSendBOM n_icon" rel="modal:close">No</a>
                                        <input type="hidden" id="hdnReqType" />
                                    </div>
                                </div>
                                <br>
                                <div class="row">
                                    <div class="col-md-auto" style="width: 14%; float: left; color: #d3fFcC; font-weight: bold;">
                                        <span id="lblResult">Sourcing Results</span>
                                    </div>

                                </div>
                                <br>
                                <div class="row">
                                    <div class="col" style="width:100%; float:left; display:flex;    ">
                                        <div id="grid_SB"></div>
                                    </div>
                                </div>
                            </div>
                            @*<div id="BOMPartsNotSourced" class="container">
                                    <br>
                                    <div class="row" style="background-color: #990000">
                                        <div style="color: #FFFFFF;width:250px" class="col-md-auto">
                                            Please Complete Sourcing for these Parts Before Release
                                        </div>
                                        <div class="col-md-auto" style="width:20px">
                                            <div style="border: 1px solid #FFFFFF; height:35px;"></div>
                                        </div>
                                        <div class="col">
                                            <label style="color: #FFFFFF" id="PartListNotSourced"></label>
                                        </div>
                                    </div>
                                    <br>
                                </div>*@
                        </div>
                    </div>
                    <div class="footer_area">
                        <a id="BackSendBOM2" style="display:none" class="BtnBackPH CancelSendBOM" href="#">Back</a>
                    </div>
                </div>
            </div>
            <input type="hidden" id="RequestToPOHubBy" />
            <input type="hidden" id="SupportTeamMemberNo" />
            <input type="hidden" id="UpdatedBy" />
            <input type="hidden" id="BOMManagerCode" />
            <input type="hidden" id="BOMManagerName" />
            <input type="hidden" id="BOMManagerCompanyNo" />
            <input type="hidden" id="BOMManagerCompanyName" />

        </div>
        <div class="modal bd-example-modal-lg" id="LyticaAPIDetails" role="dialog">
            <input type="hidden" id="LyticaPart" />
            <div>
                <div class="modal-content" style="background-color: #AAE2A0;">
                    <div class="main_header">
                        <h6 id="SenBOMTitle">Details</h6>
                        <a id="CloseLyticaAPI" style="display:block" class="BtnBackPH CancelSendBOM1" href="#">Cancel</a>
                    </div>
                    <div class="form_container">
                        <div class="header_text">

                            <div class="formInstructions"></div>
                        </div>
                        <div class="modal-body">
                            <div id="BOMPartsSourced" class="container">

                                <div class="details_col">
                                    <table class="table_one">
                                        <tbody>

                                            <tr>
                                                <td class="name_text">Manufacturer</td>
                                                <td class="name_right"><label id="lblMfr"></label></td>

                                            </tr>
                                            <tr>
                                                <td class="name_text">Commodity</td>
                                                <td class="name_right"><label id="lblCommodity"></label></tdclass="name_right">

                                            </tr>
                                            <tr>
                                                <td class="name_text">Avg. Price</td>
                                                <td class="name_right"><label id="lblAvgPrice"></label></td>

                                            </tr>
                                            <tr>
                                                <td class="name_text">Target Price</td>
                                                <td class="name_right"><label id="lblTargetPrice"></label></td>

                                            </tr>
                                            <tr>
                                                <td class="name_text">Market Leading</td>
                                                <td class="name_right"><label id="lblMarketLeading"></label></td>

                                            </tr>
                                        </tbody>
                                    </table>
                                    <table class="table_one">
                                        <tbody>

                                            <tr>
                                                <td class="name_text">Life Cycle</td>
                                                <td class="name_right"><label id="lblLifeCycle"></label></td>

                                            </tr>
                                            <tr>
                                                <td class="name_text">Life Cycle Status</td>
                                                <td class="name_right"><label id="lbllifeCycleStatus"></label></td>

                                            </tr>
                                            <tr>
                                                <td class="name_text">Overall Risk</td>
                                                <td class="name_right"><label id="lblOverallRisk"></label></td>

                                            </tr>
                                            <tr>
                                                <td class="name_text">Mpn Breadth</td>
                                                <td class="name_right"><label id="lblPartBreadth"></label></td>

                                            </tr>
                                            <tr>
                                                <td class="name_text">Mfr Breadth</td>
                                                <td class="name_right"><label id="lblManufacturerBreadth"></label></td>

                                            </tr>
                                        </tbody>
                                    </table>
                                    <table class="table_one">
                                        <tbody>

                                            <tr>
                                                <td class="name_text">Due Diligence</td>
                                                <td class="name_right"><label id="lblDueDiligence"></label></td>

                                            </tr>
                                            <tr>
                                                <td class="name_text">Mpn Concentration</td>
                                                <td class="name_right"><label id="lblPartConcentration"></label></td>

                                            </tr>

                                        </tbody>
                                    </table>

                                </div>
                                <div class="alternate_part">
                                    <div class="alt_heading">
                                        Alternate Parts
                                    </div>
                                    <div class="lyticadtl_grid">
                                        <div id="grid_LyticataDetails"></div>
                                    </div>
                                </div>
                                <br>
                            </div>
                            @*<div id="BOMPartsNotSourced" class="container">
                                    <br>
                                    <div class="row" style="background-color: #990000">
                                        <div style="color: #FFFFFF;width:250px" class="col-md-auto">
                                            Please Complete Sourcing for these Parts Before Release
                                        </div>
                                        <div class="col-md-auto" style="width:20px">
                                            <div style="border: 1px solid #FFFFFF; height:35px;"></div>
                                        </div>
                                        <div class="col">
                                            <label style="color: #FFFFFF" id="PartListNotSourced"></label>
                                        </div>
                                    </div>
                                    <br>
                                </div>*@
                        </div>
                    </div>

                </div>
            </div>
        </div>

        <div class="modal bd-example-modal-lg" id="AddCommunicationNoteModal" role="dialog">
            <div>
                <div class="modal-content" style="background-color: #AAE2A0;">
                    <div class="main_header">
                        <h6>BOM Items</h6>
                        <a id="btnSaveSource1" class="SaveCommunicationNote BtnSubmitEdit" rel="modal:close">Save</a>
                        &nbsp;&nbsp;&nbsp;
                        <a id="btnCancelSource1" class="CancelCommunicationNote BtnCloseEdit" rel="modal:close">Cancel</a>
                    </div>
                    <div class="form_container">
                        <div class="header_text">
                            <h4>ADD BOM COMMUNICATION NOTE</h4>
                            <div class="formInstructions">Add Communication Note</div>
                        </div>
                        <div class="modal-body edit_modal">
                            <div style="display:none; margin-bottom:20px" id="CommunicationNoteValidationError" class="errorSummary">
                                There were some problems with your form<br>Please check below and try again.
                            </div>
                            <table class="table_one">
                                <tbody>
                                    <tr>
                                        <td class="name_text">
                                            <label for="css">Send to</label><br>
                                        </td>
                                        <td class="name_right">
                                            <div class="row">
                                                <div class="col">
                                                    <div style="width: 100px; float: left;">
                                                        <input type="radio" id="radioGroupNotes" name="CommunicationNodeRadio" value="Group" checked="checked">
                                                        <label style="margin-top: 3px;" for="radioGroupNotes">Group</label>
                                                    </div>
                                                    &nbsp; &nbsp;&nbsp;
                                                    <div style="width: 100px; float: left;">
                                                        <input type="radio" id="radioIndividualNotes" name="CommunicationNodeRadio" value="Individual">
                                                        <label style="margin-top: 3px;" for="radioIndividualNotes">Individual</label>
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                                <tbody>
                                    <tr id="RowSelectCommunicationNoteGroup">
                                        <td class="name_text">
                                            <label>Group *</label>
                                        </td>
                                        <td class="name_right">
                                            <div class="row">
                                                <div class="col">
                                                    <select id="SelectCommunicationNoteGroup" style="width: 175px;">
                                                        <option value="0">- Select -</option>
                                                    </select>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>

                                    <tr id="RowCommunicationNoteIndividual">
                                        <td class="name_text">
                                            <label>To *</label>
                                        </td>
                                        <td class="name_right">
                                            <div class="row">
                                                <div class="col">
                                                    <select id="SelectNoteIndividual" style="width: 175px;">
                                                        <option value="0">- Select -</option>
                                                    </select><a style="cursor:pointer;" id="RefreshNoteIndividual" class="refresh_icon"></a>
                                                    <input type="text" id="txtIndividualNoteTo" style="width: 175px; display: none;" />
                                                </div>
                                            </div>
                                        </td>
                                    </tr>

                                    <tr id="RowTxtCommunicationNotes" style="height: auto !important;">
                                        <td class="name_text"><label>Add Notes *</label></td>
                                        <td class="name_right" colspan="5">
                                            <div class="row">
                                                <div class="col">
                                                    <textarea id="txtCommunicationNotes" maxlength="2000" style="width:250px; height:100px; float:left; color: #000;">
                                                    </textarea>
                                                    <div style="float:left;display: inline-block; clear:both;"><label>Character Count (2000 chrs max) : </label></div>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>

                                    <tr id="RowTxtCommunicationNoteCC">
                                        <td class="name_text"><label>CC</label></td>
                                        <td class="name_right buyerfld_imput">
                                            <div class="row">
                                                <div class="col">
                                                    <div class="lablearea" style="display: grid;">
                                                        <div class='element-notes' id='div_2_BuyerCC'>
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <input type="text" id="txtCommunicationNoteCC" />
                                                    </div>

                                                    <input type="hidden" id="HiddenCommunicationNoteMailReceiver" class="cc_input" />
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <!-- Modal Footer -->
                    </div>
                    <input type="hidden" id="hdnSourceId" />
                    <div class="formNotes">
                        <span class="mandetory">*</span> denotes a required field
                    </div>
                    <div class="footer_area">
                        <a class="SaveCommunicationNote BtnSubmitEdit" rel="modal:close">Save</a> <!--changes for version.3-->
                        &nbsp;&nbsp;&nbsp;
                        <a class="CancelCommunicationNote BtnCloseEdit" rel="modal:close">Cancel</a><!--changes for version.3-->
                    </div>
                </div>
            </div>
        </div>

        <input type="hidden" id="hdnProductNo" />
        <input type="hidden" id="hdnProductDesc" />
        <input type="hidden" id="hdnGlobalCurrencyNo" />
        <input type="hidden" id="hdnManufacturerNo" />
        <input type="hidden" id="hdnManufacturerName" />
        <input type="hidden" id="hdnManufacturerCode" />
    
        <input type="hidden" id="hdnDateCode" />
        <input type="hidden" id="hdnPackageNo" />
        <input type="hidden" id="hdnPackageName" />
        <input type="hidden" id="hdnQuantity" />
        <input type="hidden" id="hdnMSL" />
        <input type="hidden" id="hdnROHS" />
        <input type="hidden" id="HiddenBomCompanyNo" />
        <input type="hidden" id="HiddenBomRequestToPOHubBy" />
        <input type="hidden" id="HiddenBomUpdateByPH" />
        <input type="hidden" id="HiddenBomContact2No" />
        <script src="~/Areas/BOM/js/POHub.js"></script>
        <script type="text/javascript">
            var firstload = true;
            var BOMItemRefresh = true;
            var partload = false;
            var editsource = false;
            var handlerUrl = window.location.origin + "/Controls/Nuggets/CrossMatch/CrossMatch.ashx";
            var defaultOption = '<option value="-1">&lt; Select &gt;</option>';
            var refershOption = '<option selected="true" value="Loading">loading</option>';
            var optionStart = "<option value='";
            var optionEnd = "</option>";
            var autocompleteCount = 0;
            //var firstload = true;
            var RowIndexPartBOMGrid = -1;
            var RowIndexCommunicationNoteGrid = -1;
            var MoreInfoClicked = false;
            var bomStatus = 0;
            var APIGridFirstLoad = true;
            var LyticaAPILogCount = 0;
            $(document).ready(function () {
                var qrystr1 = new URLSearchParams(window.location.search);
                var qrystrkey1 = qrystr1.get("BOM");
                GetBOMManagerStatus(qrystrkey1);
                $('#hdnBomPartId').val('AllParts');

                LoadBOMItemData('AllParts', false);
                LoadXMatchSourcingData('AllParts', false);
                LoadAPIOffersData('AllParts', false);
                LoadLyticaAPIData('AllParts', false);
                LoadEMSOffers('AllParts', false);
                LoadAutoSource($('#hdnBomPartId').val(), false);

                OpenSecondTab(event, 'divXMatchSource');
                $('#btnxmatch').addClass('active');

                GetBOMManagerStatus(qrystrkey1);
                //$("#textPartNo").autocomplete("disable");

                LoadCommunicationNotesItemData();
                LoadGroupCommunicationNotes();
                LoadCommunicationNoteBuyers();

                $("#grid_md .pq-page-next").click(function () {
                    var gridInstanceRPP = $('#grid_md').pqGrid('instance');
                    var rpp = gridInstanceRPP.options.pageModel.rPP;
                    var totalRecords = gridInstanceRPP.options.pageModel.totalRecords;
                    var curPage = gridInstanceRPP.options.pageModel.curPage;
                    var recordsLeft = totalRecords - (rpp * curPage);
                    BOMItemGridHeightChange(recordsLeft, rpp);

                });
                $("#grid_md .pq-page-prev").click(function () {
                    var gridInstanceRPP = $('#grid_md').pqGrid('instance');
                    var rpp = gridInstanceRPP.options.pageModel.rPP;
                    var totalRecords = gridInstanceRPP.options.pageModel.totalRecords;
                    var curPage = gridInstanceRPP.options.pageModel.curPage;
                    var recordsLeft = totalRecords - (rpp * curPage);
                    BOMItemGridHeightChange(recordsLeft, rpp);
                });
                $("#grid_md .pq-page-first").click(function () {
                    var gridInstanceRPP = $('#grid_md').pqGrid('instance');
                    var rpp = gridInstanceRPP.options.pageModel.rPP;
                    var totalRecords = gridInstanceRPP.options.pageModel.totalRecords;
                    var curPage = 1;
                    var recordsLeft = totalRecords - (rpp * curPage);
                    BOMItemGridHeightChange(recordsLeft, rpp);
                });
                $("#grid_md .pq-page-last").click(function () {
                    var gridInstanceRPP = $('#grid_md').pqGrid('instance');
                    var rpp = gridInstanceRPP.options.pageModel.rPP;
                    var totalRecords = gridInstanceRPP.options.pageModel.totalRecords;
                    var curPage = gridInstanceRPP.options.pageModel.totalPages;
                    var recordsLeft = totalRecords - (rpp * curPage);
                    BOMItemGridHeightChange(recordsLeft, rpp);
                });
                $("#grid_md .pq-page-select").change(function () {

                    var gridInstanceRPP = $('#grid_md').pqGrid('instance');
                    var rpp = gridInstanceRPP.options.pageModel.rPP;
                    var totalRecords = gridInstanceRPP.options.pageModel.totalRecords;
                    var curPage = gridInstanceRPP.options.pageModel.curPage;
                    var recordsLeft = totalRecords - (rpp * curPage);
                    BOMItemGridHeightChange(recordsLeft, rpp);
                });
                $("#grid_md .pq-page-current").change(function () {
                    var gridInstanceRPP = $('#grid_md').pqGrid('instance');
                    var rpp = gridInstanceRPP.options.pageModel.rPP;
                    var totalRecords = gridInstanceRPP.options.pageModel.totalRecords;
                    var curPage = $('#grid_md .pq-page-current').val();
                    var recordsLeft = totalRecords - (rpp * curPage);
                    BOMItemGridHeightChange(recordsLeft, rpp);
                });

                $("#grid_md_notes .pq-page-next").click(function () {
                    var gridInstanceRPP = $('#grid_md_notes').pqGrid('instance');
                    var rpp = gridInstanceRPP.options.pageModel.rPP;
                    var totalRecords = gridInstanceRPP.options.pageModel.totalRecords;
                    var curPage = gridInstanceRPP.options.pageModel.curPage;
                    var recordsLeft = totalRecords - (rpp * curPage);
                    NotesItemGridHeightChange(recordsLeft, rpp);
                });
                $("#grid_md_notes .pq-page-prev").click(function () {
                    var gridInstanceRPP = $('#grid_md_notes').pqGrid('instance');
                    var rpp = gridInstanceRPP.options.pageModel.rPP;
                    var totalRecords = gridInstanceRPP.options.pageModel.totalRecords;
                    var curPage = gridInstanceRPP.options.pageModel.curPage;
                    var recordsLeft = totalRecords - (rpp * curPage);
                    NotesItemGridHeightChange(recordsLeft, rpp);
                });
                $("#grid_md_notes .pq-page-first").click(function () {
                    var gridInstanceRPP = $('#grid_md_notes').pqGrid('instance');
                    var rpp = gridInstanceRPP.options.pageModel.rPP;
                    var totalRecords = gridInstanceRPP.options.pageModel.totalRecords;
                    var curPage = 1;
                    var recordsLeft = totalRecords - (rpp * curPage);
                    NotesItemGridHeightChange(recordsLeft, rpp);
                });
                $("#grid_md_notes .pq-page-last").click(function () {
                    var gridInstanceRPP = $('#grid_md_notes').pqGrid('instance');
                    var rpp = gridInstanceRPP.options.pageModel.rPP;
                    var totalRecords = gridInstanceRPP.options.pageModel.totalRecords;
                    var curPage = gridInstanceRPP.options.pageModel.totalPages;
                    var recordsLeft = totalRecords - (rpp * curPage);
                    NotesItemGridHeightChange(recordsLeft, rpp);
                });
                $("#grid_md_notes .pq-page-select").change(function () {
                    var gridInstanceRPP = $('#grid_md_notes').pqGrid('instance');
                    var rpp = gridInstanceRPP.options.pageModel.rPP;
                    var totalRecords = gridInstanceRPP.options.pageModel.totalRecords;
                    var curPage = gridInstanceRPP.options.pageModel.curPage;
                    var recordsLeft = totalRecords - (rpp * curPage);
                    NotesItemGridHeightChange(recordsLeft, rpp);
                });
                $("#grid_md_notes .pq-page-current").change(function () {

                    var gridInstanceRPP = $('#grid_md_notes').pqGrid('instance');
                    var rpp = gridInstanceRPP.options.pageModel.rPP;
                    var totalRecords = gridInstanceRPP.options.pageModel.totalRecords;
                    var curPage = $('#grid_md_notes .pq-page-current').val();
                    var recordsLeft = totalRecords - (rpp * curPage);
                    NotesItemGridHeightChange(recordsLeft, rpp);
                });
            });

            $('#ShowPartBomDiv').click(function () {
                if ($('#DivPartBomGrid').css("display") == 'none') {
                    $('#DivPartBomGrid').css("display", 'block');
                    $('#refreshBOMItem').css("display", 'block');
                    $('#divBOMItemButtons').css("display", 'block');
                    $('#headingThree').removeClass('headingOneCorner');
                    $('#ShowPartBomDiv').css("background-image", "url(/Areas/BOM/Images/hide.gif)");
                    $('#head_in_DivPartBom').removeClass('hide_top');

                }
                else {
                    $('#DivPartBomGrid').css("display", 'none');
                    $('#refreshBOMItem').css("display", 'none');
                    $('#divBOMItemButtons').css("display", 'none');

                    $('#headingThree').addClass('headingOneCorner');
                    $('#ShowPartBomDiv').css("background-image", "url(/Areas/BOM/Images/show.gif)");
                    $('#head_in_DivPartBom').addClass('hide_top');
                }
            });
            $('#ShowAutoSourcediv').click(function () {
                if ($('#divAutoSource').css("display") == 'none') {
                    $('#divAutoSource').css("display", 'block');
                    $('#refreshAutoSource').css("display", 'block');

                    $('#headingThree').removeClass('headingOneCorner');
                    $('#ShowAutoSourcediv').css("background-image", "url(/Areas/BOM/Images/hide.gif)");
                    $('#head_in_DivAutoSource').removeClass('hide_top');
                }
                else {
                    $('#divAutoSource').css("display", 'none');
                    $('#refreshAutoSource').css("display", 'none');


                    $('#headingThree').addClass('headingOneCorner');
                    $('#ShowAutoSourcediv').css("background-image", "url(/Areas/BOM/Images/show.gif)");
                    $('#head_in_DivAutoSource').addClass('hide_top');
                }
            });
            $('#ShowAvailableSourcingDiv').click(function () {
                if ($('#DivAvailableSourcing').css("display") == 'none') {
                    $('#DivAvailableSourcing').css("display", 'block');

                    $('#headingSourcing').removeClass('headingOneCorner');
                    $('#ShowAvailableSourcingDiv').css("background-image", "url(/Areas/BOM/Images/hide.gif)");
                    $('#head_in_DivAvailableSourcing').removeClass('hide_top');
                }
                else {
                    $('#DivAvailableSourcing').css("display", 'none');
                    $('#refreshBOMItem').css("display", 'none');
                    //$('#divBOMItemButtons').css("display", 'none');

                    $('#headingSourcing').addClass('headingOneCorner');
                    $('#ShowAvailableSourcingDiv').css("background-image", "url(/Areas/BOM/Images/show.gif)");
                    $('#head_in_DivAvailableSourcing').addClass('hide_top');
                }
            });
            function GetBOMManagerStatus(bom) {
                $.ajax({
                    processData: true,
                    contentType: 'application/json',
                    type: 'POST',
                    //asynch: false,
                    url: 'GetBOMManagerStatus?BOM=' + bom,
                    dataType: "json",
                    success: function (data) {
                        $('#Bomlink').text("BOM Code: " + data.BOMManagerCode);
                        $('#BomName').text(" BOM Name: " + data.BOMManagerName);
                        $('#Company').text("Company: " + data.CompanyName);
                        $('#SalesPerson').text("Salesperson: " + data.Salesperson);
                        $('#BOMStatus').text(data.BOMStatus);
                        $('#BOMManagerCompanyName').val(data.CompanyName);
                        $('#BOMManagerCompanyNo').val(data.CompanyNo);
                        $('#BOMManagerName').val(data.BOMManagerName);
                        $('#BOMManagerCode').val(data.BOMManagerCode);
                        $('#SupportTeamMemberNo').val(data.SupportTeamMemberNo);
                        $('#RequestToPOHubBy').val(data.RequestToPOHubBy);

                        //$('#hdnBomStatus').text(data.Status);
                        $('#ItemsSourcedCount').text("Items Sourced: " + data.ItemsSourcedCount + "/" + data.TotalItemsCount);
                        bomStatus = data.Status;
                        $('#hdBomStatus').val(data.Status);
                        if (data.Status == 7) {
                            $('#btnRelease').removeClass("Release");
                            $('#btnRelease').addClass("Release_disabled");

                            $('#btnRecall').removeClass("Recall");
                            $('#btnRecall').addClass("Recall_disabled");

                            $('#btnNoBid').removeClass("NoBid");
                            $('#btnNoBid').addClass("NoBid_disabled");

                            $('#btnNoBidRecall').removeClass("RecallNoBid");
                            $('#btnNoBidRecall').addClass("RecallNoBid_disabled");
                        }
                    },
                    error: function (err) {}
                });
            }

            $('.ConfirmNoXMatch').click(function () {
                ResetXmatchUserForm();
            });
            $('.ConfirmYesXMatch').click(function () {
                if ($('#XMSPQ').val() == "" || $('#XMSPQ').val() == null) {
                    $('#XMSPQError').css("display", "block");
                    $('#XMSPQRow').css("background-color", "#990000");
                }
                else {
                    var qrystr = new URLSearchParams(window.location.search);
                    var BomManagerID = qrystr.get("BOM");
                    $.ajax({
                        processData: true,
                        contentType: 'application/json',
                        type: 'POST',
                        url: 'SaveUserXmatchData?MOQ=' + $('#XMMOQ').val() + '&SPQ=' + $('#XMSPQ').val() + '&StockQTY=' + $('#XMStockQTY').val() + '&DateCode=' + $('#XMMDC').val() + '&LeadTime=' + $('#XMLT').val() + '&BOMManagerID=' + BomManagerID + '&SalesXMatchID=' + $('#XMSalesXMatchID').val(),
                        dataType: "json",
                        success: function (data) {
                            ResetXmatchUserForm();
                        },
                        error: function (err) {}
                    });
                }
            });

            $("input:radio[name=OfferType]").click(function () {

                if ($("input[name=OfferType]:checked").val() == '1') {
                    //alert('Radio1');
                    $("#textPartNo").css("display", "block");
                    $("#textPartNo").val(null);
                    var partName = $('#hdnBomPartNumber').val();
                    $('#textPartNo').val(partName);
                    $('#textPartNo').attr("disabled", true);
                    $('#PartSearch').hide();
                    $('#textPartNo').hide();
                    $("#hdpartid").val(partName);
                    $('#LabelPartNo').text(partName);
                    $('#RemovePartNo').hide();
                    var productno = $('#hdnProductNo').val();
                    var productdesc = $('#hdnProductDesc').val();
                    if (productno != null && productno > 0) {
                        $("#textProductName").css("display", "none");
                        $("#prodid").val(productno);
                        $("#LabelProduct").text(productdesc, true);
                        $("#RemoveProduct").css("display", "block");
                    }


                    var hdnmfrno = $('#hdnManufacturerNo').val();
                    var hdnmfrname = $('#hdnManufacturerName').val();
                    if (hdnmfrno != null && hdnmfrno > 0) {
                        $("#textManufacturer").css("display", "none");
                        $("#hdmrfid").val(hdnmfrno);
                        $("#LabelManufacturer").text(SetCleanText(hdnmfrname, true));
                        $("#RemoveManufacturer").css("display", "block");
                    }
                    var hdnpckno = $('#hdnPackageNo').val();
                    var hdnpckname = $('#hdnPackageName').val();
                    if (hdnpckno != null && hdnpckno > 0) {
                        $("#textPackage").css("display", "none");
                        $("#pkgid").val(hdnpckno);
                        $("#LabelPackage").text(SetCleanText(hdnpckname, true));
                        $("#RemovePackage").css("display", "block");
                    }
                    var hdndatecode = $('#hdnDateCode').val();
                    var hdnquantity = $('#hdnQuantity').val();
                    $('#textdatecode').val(hdndatecode);
                    $('#textQuantity').val(hdnquantity);
                    BindMSL('ddlMSL', -1);
                    var hdnmsl = $('#hdnMSL').val();
                    var mslid = 0;
                    switch (hdnmsl) {
                        case "N/A":
                            mslid = 1;
                            break;
                        case "MSL 1":
                            mslid = 2;
                            break;
                        case "MSL 2":
                            mslid = 3;
                            break;
                        case "MSL 2A":
                            mslid = 4;
                            break;
                        case "MSL 3":
                            mslid = 5;
                            break;
                        case "MSL 4":
                            mslid = 6;
                            break;
                        case "MSL 5":
                            mslid = 7;
                            break;
                        case "MSL 5A":
                            mslid = 8;
                            break;
                        case "MSL 6":
                            mslid = 9;
                            break;
                        case "Exempt":
                            mslid = 10;
                            break;
                        default:
                            mslid = 0;
                    }
                    //$('#ddlMSL').val(mslid)
                    BindMSL('ddlMSL', mslid);
                    BindROHSStatus('ddlROHS', -1)
                    var hdnrohs = $('#hdnROHS').val();
                    var rohsid = 0;
                    BindROHSStatus('ddlROHS', hdnrohs);
                }
                else if ($("input[name=OfferType]:checked").val() == '2') {
                    $('#textPartNo').val('');
                    $('#textPartNo').attr("disabled", false);
                    $('#PartSearch').show();
                    $("#textPartNo").css("display", "block");
                    $("#textPartNo").val(null);
                    $('#LabelPartNo').text('');
                    $("#RemoveProduct").click();
                    $("#RemoveManufacturer").click();
                    $("#RemovePackage").click();
                    $("#RemovePackage").click();
                    $('#textQuantity').val('');
                    $('#ddlMSL').val(0);
                    BindROHSStatus('ddlROHS', -1);
                }
            });

            $("#LinkAddOffer").click(function () {
                $('#divAddAltOffer').hide();
                $('#textPartNo').val('');
                $('#textPartNo').attr("disabled", false);
                $('#PartSearch').show();
                $("#textPartNo").css("display", "block");
                $("#textPartNo").val(null);
                $('#LabelPartNo').text('');
                //alert('Edit click');
                var modal = document.getElementById("AddOfferModal");
                AddOfferDropDownBind();
                modal.style.display = "block"
                $('html, body', window.parent.document).animate({ scrollTop: 240 }, "fast");
            });
            $("#LinkAddOfferPart").click(function () {
                //alert('Edit click');
                var partName = $('#hdnBomPartNumber').val();
                $('#lblOfferPartName').text(partName);
                $('#divAddAltOffer').show();
                var modal = document.getElementById("AddOfferModal");
                AddOfferDropDownBind();
                $("#rdAddoffer").prop("checked", true).trigger("click");
                modal.style.display = "block"
                $('html, body', window.parent.document).animate({ scrollTop: 240 }, "fast");
            });
            $("#btnHeaderSave").click(function () {
                $("#btnHeaderSave").css('pointer-events', 'none');
                if (SaveNewOffer()) {
                    var modal = document.getElementById("AddOfferModal");
                    modal.style.display = "none"
                    $('html, body', window.parent.document).animate({ scrollTop: 240 }, "fast");
                }
            });
            $("#btnFooterSave").click(function () {
                $("#btnHeaderSave").css('pointer-events', 'none');
                if (SaveNewOffer()) {
                    var modal = document.getElementById("AddOfferModal");
                    modal.style.display = "none"
                    $('html, body', window.parent.document).animate({ scrollTop: 240 }, "fast");
                }
            });
            $("#textPartNo").focusout(function () {
                //var partno = $("#textPartNo").val();
                var partno = $("#LabelPartNo").text();
                $("#textPartNo").css("display", "none");
                $("#hdpartid").val(partno);
                $("#LabelPartNo").text(SetCleanText(partno));
                $("#RemovePartNo").css("display", "block");
                $("#ValidationErrorPartNo").css("display", "none");
                $("#RowAddOfferPartNoError").css("display", "none");
                $("#RowAddOfferPartNo").css("background-color", "#56954E");
                $("#partsearchdiv").css("display", "none");
            });
            $("#btnHeaderCancel").click(function () {
                ClearOfferModalData();
                var modal = document.getElementById("AddOfferModal");
                modal.style.display = "none"
                $('html, body', window.parent.document).animate({ scrollTop: 240 }, "fast");
            });
            $("#btnFooterCancel").click(function () {
                ClearOfferModalData();
                var modal = document.getElementById("AddOfferModal");
                modal.style.display = "none"
                $('html, body', window.parent.document).animate({ scrollTop: 240 }, "fast");
            });
            $("#RemoveSupplier").click(function () {
                $("#textSupplier").css("display", "block");
                $("#textSupplier").val(null);
                $("#hdsupplier").val(null);
                $("#LabelSupplier").text(null);
                $("#RemoveSupplier").css("display", "none");
            });
            $("#RemovePartNo").click(function () {
                $("#textPartNo").css("display", "block");
                $("#textPartNo").val(null);
                $("#hdpartid").val(null);
                $("#LabelPartNo").text(null);
                $("#RemovePartNo").css("display", "none");
                $("#partsearchdiv").css("display", "block");
            });
            $("#RemoveManufacturer").click(function () {
                $("#textManufacturer").css("display", "block");
                $("#textManufacturer").val(null);
                $("#hdmrfid").val(null);
                $("#LabelManufacturer").text(null);
                $("#RemoveManufacturer").css("display", "none");
            });
            $("#RemoveProduct").click(function () {
                $("#textProductName").css("display", "block");
                $("#textProductName").val(null);
                $("#prodid").val(null);
                $("#LabelProduct").text(null);
                $("#RemoveProduct").css("display", "none");
            });
            $("#RemovePackage").click(function () {
                $("#textPackage").css("display", "block");
                $("#textPackage").val(null);
                $("#pkgid").val(null);
                $("#LabelPackage").text(null);
                $("#RemovePackage").css("display", "none");
            });
            $("#RefreshMSL").click(function () {
                BindMSL('ddlMSL', $("#ddlMSL").val());
            });
            $("#RefreshOfferStatus").click(function () {
                BindOfferStatus('ddlOfferStatus', $("#ddlOfferStatus").val());
            });
            $("#RefreshCurrency").click(function () {
                BindCurrencyList('ddlCurrency', $("#ddlCurrency").val());
            });
            $("#RefreshROHS").click(function () {
                BindROHSStatus('ddlROHS', $("#ddlROHS").val());
            });
            $("#RefreshCOO").click(function () {
                BindCountryOfOrigin('ddlCountryOfOrigin', $("#ddlCountryOfOrigin").val());
            });
            $("#RefreshRegion").click(function () {
                BindRegion('ddlRegion', $("#ddlRegion").val());
            });

            function openimagepop() {
                //alert('Edit click');
                $('#CloseHubModal').css('display', 'block');
            }

            function closeimagepop() {
                $('#CloseHubModal').css('display', 'none');
                //$('html, body', window.parent.document).animate({ scrollTop: 0 }, "fast");
            }

            function OpenFirstTab(evt, tabName) {

                var i, tabcontent, tablinks;
                tabcontent = document.getElementsByClassName("tabcontent1");
                for (i = 0; i < tabcontent.length; i++) {
                    tabcontent[i].style.display = "none";

                }
                tablinks = document.getElementsByClassName("tablinks");
                for (i = 0; i < tablinks.length; i++) {
                    tablinks[i].className = tablinks[i].className.replace(" active", "");
                }
                document.getElementById(tabName).style.display = "block";
                if (firstload == true)
                    $('#btnDivBOMPart').addClass('active');
                else
                    evt.currentTarget.className += " active";
                firstload = false;

                if (tabName == 'divIHS') {
                    if ($('#hdnBomPartId').val() == 'AllParts')
                        LoadIHSData($('#hdnBomPartId').val(), true, 0);
                }
                if (tabName == 'divXMatch') {
                    if ($('#hdnBomPartId').val() == 'AllParts')
                        LoadXMatchData($('#hdnBomPartId').val(), true, 0);
                }

                if (tabName == 'divSecondTab') {
                    OpenSecondTab(event, 'divXMatchSource');
                    $('#btnxmatch').addClass('active');
                }
            }
            function disableFisrtTab() {
                var i, tabcontent, tablinks;
                tabcontent = document.getElementsByClassName("tabcontent1");
                for (i = 0; i < tabcontent.length; i++) {
                    tabcontent[i].style.display = "block";
                }
            }
            function disableSecondTab() {
                var i, tabcontent, tablinks;
                tabcontent = document.getElementsByClassName("tabcontent2");
                for (i = 0; i < tabcontent.length; i++) {
                    tabcontent[i].style.display = "block";
                }
            }
            function OpenSecondTab(evt, tabName) {
                var i, tabcontent, tablinks;
                tabcontent = document.getElementsByClassName("tabcontent2");
                for (i = 0; i < tabcontent.length; i++) {
                    tabcontent[i].style.display = "none";
                }
                tablinks = document.getElementsByClassName("tablinks");
                for (i = 0; i < tablinks.length; i++) {
                    tablinks[i].className = tablinks[i].className.replace(" active", "");
                }
                document.getElementById(tabName).style.display = "block";
                if (partload == true)
                    $('#divXMatchSource').addClass('active');
                else
                    evt.currentTarget.className += " active";
                partload = false;

                $('#btnPurchasingWorking').addClass('active');
                //evt.currentTarget.className += " active";

                if (tabName == 'divXMatchSource') {
                    //if ($('#hdnBomPartId').val() == 'AllParts')
                    LoadXMatchSourcingData($('#hdnBomPartId').val(), true, 0);
                }
                if (tabName == 'divEMSOffer') {
                    //if ($('#hdnBomPartId').val() == 'AllParts')
                    LoadEMSOffers($('#hdnBomPartNumber').val(), true, 0);

                }
                if (tabName == 'divAPI') {

                    LoadAPIOffersData($('#hdnBomPartId').val(), true, 0);
                }
                if (tabName == 'divLyticaAPI') {
                    document.querySelectorAll('.pq-state-select').length != 0 ? LoadLyticaAPIData($('#hdnBomPartId').val(), true, 0, true) : LoadLyticaAPIData($('#hdnBomPartId').val(), true, 0, false);
                    GetLyticaAPILogCount();
                }
            }

            
            function LoadXMatchSourcingData(CId, refreshGrid, OfferId) {
                if (CId == 'AllParts') {
                    CId = ""
                }
                var PartNo = "";
                var colModel = [
                    {
                        //add offer button
                        title: "",
                        width: "10%",
                        sortable: false,
                        render: function (ui) {
                            if (ui.rowData["OfferAddFlag"] == true)
                                return "<button type='button' class='Add_btn_APIGrid button_disabled'>Added</button>";
                            // if ($("#hdnBomStatus").text() < 5) {
                            //return "<button type='button' class='edit_btn'>Add Offer</button><br><button type='button' class='update_btn'>Update</button>";
                            if (ui.rowData["REQStatus"] > 3 || $('#hdBomStatus').val() == 7) {
                                return "<button type='button' class='Add_btn_APIGrid button_disabled'>Add Additional</button>";
                            }
                            else {
                                return "<button type='button' class='Add_btn_APIGrid'>Add Additional</button>";
                            }
                            // }
                        },
                        postRender: function (ui) {
                            var rowIndx = ui.rowIndx,
                                grid = this,
                                $cell = grid.getCell(ui);
                            if (grid.hasClass({ rowData: ui.rowData, cls: 'pq-row-edit' })) {
                                //Edit button
                                $cell.find(".Add_btn_APIGrid")
                                    .button({ label: "Add Additional" })
                            }
                            else {
                                //edit button
                                $cell.find(".Add_btn_APIGrid").button()
                                    .off("click")
                                    .on("click", function (evt) {
                                        AddNewOffer(rowIndx, grid, 3, ui, ui.rowData["OfferSource"]);
                                    });
                            }

                        }
                    },
                    {
                        title: "CustomerRequirementId", //title of column.
                        /* width: "10%", //initial width of column*/
                        //dataType: "integer", //data type of column
                        dataIndx: "CustomerRequirementId",
                        hidden: true,
                        //should match one of the keys in row data.
                        //render: function (ui) {
                        //    customerrequirementidval = ui.rowData.CustomerRequirementId.toString();
                        //}
                    },
                    {
                        title: "BOMManagerNo", //title of column.
                        /*width: "10%", //initial width of column*/
                        //dataType: "integer", //data type of column
                        dataIndx: "BOMManagerNo", //should match one of the keys in row data.
                        hidden: true,
                        //render: function (ui) {
                        //    customerrequirementidval = ui.rowData.CustomerRequirementId.toString();
                        //}
                    },
                    {
                        title: "Part No.",
                        width: "13%",
                        //dataType: "string",
                        dataIndx: "Part",
                        filter: { crules: [{ condition: 'range' }] },
                        menuIcon: true,
                        //render: function (ui) {
                        //    //return ui.rowData.BOMManagerNo + '-<br/>' + ui.rowData.Part
                        //    return /*"<span><a  href='../../Ord_CusReqDetail.aspx?req=" + ui.rowData.XPartNumber + "' target='_top'>" +*/ ui.rowData.Part + "</a></span>";
                        //}
                    },
                    {
                        title: "Invoice No",
                        width: "6%",
                        //dataType: "float",
                        //align: "right",
                        dataIndx: "InvoiceNumber",
                        render: function (ui) {

                            var htmlstr = "<span>" + ui.rowData["InvoiceNumber"] + "</span ><br/>";
                            //htmlstr = htmlstr + " <span>" + ui.rowData.CustomerPart.toString() + "</span > ";
                            return htmlstr;
                        }
                    },
                    {
                        title: "Inv. Sls",
                        width: "10%",
                        dataIndx: "InvoiceSls",
                        /*dataType: "float",*/
                        filter: { crules: [{ condition: 'range' }] },
                        menuIcon: true,
                        render: function (ui) {

                            var htmlstr = "<span>" + ui.rowData["InvoiceSls"] + "</span ><br/>";
                            //htmlstr = htmlstr + " <span>" + ui.rowData.CustomerPart.toString() + "</span > ";
                            return htmlstr;
                        }
                    },
                    {
                        title: "Inv. Customer",
                        width: "15%",
                        /*dataType: "float",*/
                        dataIndx: "InvoiceCustomer",
                        filter: { crules: [{ condition: 'range' }] },
                        menuIcon: true,
                        render: function (ui) {
                            var htmlstr = "<Span title='" + ui.rowData["InvoiceCustomer"] + "'>" + ui.rowData["InvoiceCustomer"] + "</a></span>";
                            return htmlstr;
                        }
                    },
                    {
                        title: "Inv. Date",
                        width: "7%",
                        dataType: "Date",
                        //align: "right",
                        dataIndx: "InvoiceDate",
                        render: function (ui) {
                            var date = new Date(parseInt(ui.rowData["InvoiceDate"].substr(6)));
                            return date.toLocaleDateString("en-GB");
                        }
                    },
                    {
                        title: "Inv. Qty",
                        width: "6%",
                        /*dataType: "float",*/
                        //align: "right",
                        dataIndx: "InvoiceQuantity",
                        render: function (ui) {
                            var htmlstr = "<span>" + ui.rowData["InvoiceQuantity"] + "</span ><br/>";
                            //htmlstr = htmlstr + " <span>" + getCustomDate(ui.rowData.DatePromised.toString()) + "</span > ";
                            return htmlstr;
                        }
                    },
                    {
                        title: "Unit Price",
                        width: "7%",
                        /*dataType: "float",*/
                        //align: "right",
                        dataIndx: "UnitPrice",
                        render: function (ui) {
                            var htmlstr = "<span>" + parseFloat(ui.rowData["UnitPrice"]).toFixed(4) + "</span ><br/>";
                            //htmlstr = htmlstr + "<span>" + ui.rowData.SalesmanName + "</span ><br/>";
                            return htmlstr;
                        }
                    },
                    {
                        title: "PO Order",
                        width: "8%",
                        /*dataType: "float",*/
                        filter: { crules: [{ condition: 'range' }] },
                        menuIcon: true,
                        dataIndx: "POOrder",
                        render: function (ui) {
                            var htmlstr = "<span>" + ui.rowData["POOrder"] + "</span ><br/>";
                            //htmlstr = htmlstr + "<span>" + ui.rowData.SalesmanName + "</span ><br/>";
                            return htmlstr;
                        }
                    },
                    {
                        title: "PO Company",
                        width: "10%",
                        /*dataType: "float",*/
                        filter: { crules: [{ condition: 'range' }] },
                        menuIcon: true,
                        dataIndx: "POCompany",
                        render: function (ui) {
                            var htmlstr = "<Span title='" + ui.rowData["POCompany"] + "'>" + ui.rowData["POCompany"] + "</span ><br/>";
                            return htmlstr;
                        }
                    },
                    {
                        title: "PO Qty",
                        width: "5%",
                        /*dataType: "float",*/
                        //align: "right",
                        dataIndx: "POQuantity",
                        render: function (ui) {
                            var htmlstr = "<span>" + ui.rowData["POQuantity"] + "</span ><br/>";
                            //htmlstr = htmlstr + "<span>" + ui.rowData.SalesmanName + "</span ><br/>";
                            return htmlstr;
                        }
                    },
                    {
                        title: "PO Price",
                        width: "5%",
                        /*dataType: "float",*/
                        //align: "right",
                        dataIndx: "POPrice",
                        render: function (ui) {
                            var htmlstr = "<span>" + parseFloat(ui.rowData["POPrice"]).toFixed(4) + "</span ><br/>";
                            return htmlstr;
                        }
                    },
                    {
                        title: "PO Currency",
                        width: "7%",
                        /*dataType: "float",*/
                        //align: "right",
                        dataIndx: "POCurrency",
                        render: function (ui) {
                            var htmlstr = "<span>" + ui.rowData["POCurrency"] + "</span ><br/>";
                            //htmlstr = htmlstr + "<span>" + ui.rowData.SalesmanName + "</span ><br/>";
                            return htmlstr;
                        }
                    }
                ];

                //main object to be passed to pqGrid constructor.
                var qrystr = new URLSearchParams(window.location.search);
                var BOMManagerID = qrystr.get("BOM");
                var dataModel = {
                    location: "remote",
                    dataType: "JSON",
                    method: "POST",
                    recIndx: "SalesXMatchID",
                    url: 'GetUserMatchData?crossMatchType=rdInvoiceXMatch&matchFirstValue=3&chkMatchBase=false&chkMatchFirst=false&BOMManagerID=' + BOMManagerID + '&CustomerRequirementID=' + CId,
                    //data: { BOMId: BOMID, clientId: clientid, BOMDate: BomDateVal },
                    //url: "/pro/invoice.php",//for PHP
                    getData: function (dataJSON) {
                        var data = dataJSON.data;
                        $.each(dataJSON, function (index, value) {
                            if (value.OfferId == OfferId)
                                dataJSON[index].pq_rowcls = "red";
                        });
                        var totalRecords = 0;
                        if (dataJSON.length != 0) {
                            totalRecords = dataJSON[0].TotalCount;
                        }
                        var cur_page = 1;
                        if (dataJSON.length > 0) {
                            cur_page = dataJSON[0].curpage;
                        }
                        return { curPage: cur_page, totalRecords: totalRecords, data: dataJSON };
                    }
                };
                var grid1 = $("#grid_xmSourcing").pqGrid({
                    width: "auto", height: 272,
                    selectionModel: { type: null, native: true },
                    dataModel: dataModel,
                    rowHt: 35,
                    hwrap: false,
                    wrap: false,
                    hoverMode: 'cell',
                    colModel: colModel,
                    complete: function (event, ui) {
                        var gridInstanceRPP = $('#grid_xmSourcing').pqGrid('instance');
                        var rpp = gridInstanceRPP.options.pageModel.rPP;
                        var totalRecords = gridInstanceRPP.options.pageModel.totalRecords;
                        var curPage = gridInstanceRPP.options.pageModel.curPage;
                        var recordsLeft = totalRecords - (rpp * curPage);
                        XMatchSourcingGridHeightChange(recordsLeft, rpp);
                    },
                    editable: false,
                    postRenderInterval: -1, //synchronous post rendering.;
                    freezeCols: 2,
                    pageModel: { type: "remote", rPP: 5, strRpp: "{0}", rPPOptions: [5, 10] },
                    numberCell: { show: false },
                    rowSelect: function (evt, ui) {
                    }
                });
                if ((CId != 'AllParts' && CId != "") || refreshGrid) {
                    grid1.pqGrid('refreshDataAndView');
                }
            }

            function LoadAPIOffersData(CId, refreshGrid, OfferId) {
                if (CId == 'AllParts') {
                    CId = ""
                }
                if (OfferId == null || OfferId == undefined) {
                    OfferId = -1;
                }
                var width;
                if ($('#hdnReqSttsBtBom').val() > 3) {
                    width = "19.5%";
                }
                else {
                    width = "13%";
                }
                var PartNo = "";
                var colModel = [
                    {// add offer button
                        title: "", editable: false,
                        width: "8.5%",
                        sortable: false,
                        //hidden:false,
                        render: function (ui) {
                            //if (bomStatus >4) {
                            //    this.getColumn({ dataIndx: '0' }).hidden = true;
                            //    //return "<button type='button' class='edit_btn_APIGrid'>Add offer</button>";
                            //    //return "<button type='button' class='edit_btn_APIGrid'>Add offer</button>";
                            //}
                            //else
                            if (ui.rowData["OfferAddFlag"] == true)
                                return "<button type='button' class='Add_btn_APIGrid button_disabled'>Added</button>";

                            if (ui.rowData["REQStatus"] > 3 || $('#hdBomStatus').val() == 7) {
                                return "<button type='button' class='Add_btn_APIGrid button_disabled'>Add Additional</button>";
                            }
                            //if (ui.rowData["OfferSource"] == "Future Electronics") {
                            //    return "<button type='button' class='Add_btn_APIGrid button_disabled'>Add Additional</button>";
                            //}
                            else {
                                return "<button type='button' class='Add_btn_APIGrid'>Add Additional</button>";
                            }
                        },
                        postRender: function (ui) {
                            var rowIndx = ui.rowIndx,
                                grid = this,
                                $cell = grid.getCell(ui);
                            if (grid.hasClass({ rowData: ui.rowData, cls: 'pq-row-edit' })) {
                                //Edit button
                                $cell.find(".Add_btn_APIGrid")
                                    .button({ label: "Add Additional" })
                            }
                            else {
                                //edit button
                                $cell.find(".Add_btn_APIGrid").button()
                                    .off("click")
                                    .on("click", function (evt) {
                                        AddNewOffer(rowIndx, grid, 2, ui, ui.rowData["ApiSourceName"]);
                                    });
                            }
                        }
                    },
                    {
                        title: "Part No.",
                        width: "12%",
                        dataIndx: "Part",
                        /*dataType: "float",*/
                        filter: { crules: [{ condition: 'range' }] },
                        menuIcon: true,
                        render: function (ui) {
                            return
                        }
                    },
                    {
                        title: "API Offer ID", //title of column.
                        width: "0%", //initial width of column
                        dataIndx: "FElectronicsId",
                        hidden: true,
                        render: function (ui) {
                            return ui.rowData["FElectronicsId"].toString();
                        }
                    },
                    {
                        title: "Offer Source",
                        width: "8%",
                        dataIndx: "ApiSourceName",
                        /*dataType: "float",*/
                        filter: { crules: [{ condition: 'range' }] },
                        menuIcon: true,
                        render: function (ui) {
                            return ui.rowData["ApiSourceName"].toString()
                        }
                    },
                    {
                        title: "Vendor",
                        width: "12%",
                        //dataType: "string",
                        filter: { crules: [{ condition: 'range' }] },
                        menuIcon: true,
                        dataIndx: "SupplierName",
                        render: function (ui) {
                            return '<span class=' + "gridlink" + ' title="' + ui.rowData["SupplierName"] + '">' + "<a  href='../../Con_CompanyDetail.aspx?cm=" + ui.rowData["SupplierNo"] + "' target='_Blank'>" + ui.rowData["SupplierName"] + "</a></span ><br/>";
                        }
                    },
                    {
                        title: "Type",
                        width: "12%",
                        //dataType: "float",
                        //align: "right",
                        dataIndx: "SupplierType",
                        render: function (ui) {

                            return '<span title="' + ui.rowData["SupplierType"] + '">' + ui.rowData["SupplierType"] + "</span ><br/>";
                        }
                    },
                    {
                        title: "Manufacturer",
                        width: width,
                        /*dataType: "float",*/
                        dataIndx: "ManufacturerName",
                        filter: { crules: [{ condition: 'range' }] },
                        menuIcon: true,
                        render: function (ui) {
                            if (ui.rowData["ApiSourceName"] == "Future Electronics") {
                                if (ui.rowData["ManufacturerName"] != "") {
                                    return '<span class=' + "gridlink" + ' title="' + ui.rowData["ManufacturerCode"] + '">' + "<a  href='../../Con_ManufacturerDetail.aspx?mfr=" + ui.rowData["ManufacturerNo"] + "' target='_Blank'>" + ui.rowData["ManufacturerCode"] + "</a></span ><br/>";
                                }
                            }
                            //if (ui.rowData["ManufacturerName"] != "") {
                            //    return '<span class=' + "gridlink" + ' title="' + ui.rowData["ManufacturerName"] + '">' + "<a  href='../../Con_ManufacturerDetail.aspx?mfr=" + ui.rowData["ManufacturerNo"] + "' target='_Blank'>" + ui.rowData["ManufacturerName"] + "</a></span ><br/>";
                            //}
                            return "";
                        }
                    },
                    {
                        title: "Price",
                        width: "8.5%",
                        //dataType: "Date",
                        //align: "right",
                        dataIndx: "UnitCostPrice",
                        render: function (ui) {
                            //var htmlstr;
                            if (ui.rowData["ApiSourceName"] == "Future Electronics") {
                                return parseFloat(ui.rowData["UnitCostPrice"]).toFixed(4) + ' ' + ui.rowData["CurrencyCode"];
                            }
                            else {
                                return parseFloat(ui.rowData["UnitCostPrice"]).toFixed(4) + ' ' + ui.rowData["CurrencyCode"];
                            }
                        }
                    },
                    {
                        title: "Date",
                        width: "7%",
                        dataType: "Date",
                        dataIndx: "OriginalEntryDate",
                        render: function (ui) {
                            var htmlstr;
                            if (ui.rowData["OriginalEntryDate"] != null && ui.rowData["OriginalEntryDate"] != undefined && ui.rowData["OriginalEntryDate"] != "") {
                                var date = new Date(parseInt(ui.rowData["OriginalEntryDate"].substr(6)));
                                htmlstr = "<span>" + date.toLocaleDateString("en-GB") + "</span ><br/>";
                            }
                            return htmlstr;
                        }
                    },
                    {
                        title: "MOQ",
                        width: "6%",
                        dataIndx: "SupplierMOQ",
                        render: function (ui) {
                            return "<span>" + ui.rowData["SupplierMOQ"] + "</span >";
                        }
                    },
                    {
                        title: "SPQ",
                        width: "6%",
                        /*dataType: "float",*/
                        //align: "right",
                        dataIndx: "SPQ",
                        render: function (ui) {
                            if (ui.rowData["SPQ"] != null && ui.rowData["SPQ"] != undefined && ui.rowData["SPQ"] != "") {
                                return "<span>" + ui.rowData["SPQ"] + "</span ><br/>";
                            }
                        }
                    },
                    {
                        title: "Stock<br>Quantity",
                        width: "7%",
                        dataIndx: "SupplierTotalQSA",
                        render: function (ui) {
                            var htmlstr = "";
                            if (ui.rowData["SupplierTotalQSA"] != null && ui.rowData["SupplierTotalQSA"] != undefined && ui.rowData["SupplierTotalQSA"] != "") {
                                htmlstr = "<span>" + ui.rowData["SupplierTotalQSA"] + "</span >";
                            }
                            return htmlstr + "</br><span>" + ui.rowData["Quantity"] + "</span>";
                        }
                    }
                ];

                var qrystr = new URLSearchParams(window.location.search);
                var BomManagerID = qrystr.get("BOM");
                var dataModel = {
                    location: "remote",
                    dataType: "JSON",
                    method: "POST",
                    recIndx: "FElectronicsId",
                    url: 'GetAPIOffersData?BOMManagerID=' + BomManagerID + '&Part=' + PartNo + '&CustomerReqID=' + CId + '&FirstLoad=' + APIGridFirstLoad,
                    getData: function (dataJSON) {
                        APIGridFirstLoad = false;
                        var data = dataJSON.data;
  
                        $.each(dataJSON, function (index, value) {
                            if (value.OfferId == OfferId) {
                                dataJSON[index].pq_rowcls = "red";
                            }
                        });
                        var totalRecords = 0;
                        if (dataJSON.length > 0) {
                            totalRecords = dataJSON[0].TotalCount;
                        }
                        var cur_page = 1;
                        if (dataJSON.length > 0) {
                            cur_page = dataJSON[0].curpage;
                        }
                        return { curPage: cur_page, totalRecords: totalRecords, data: dataJSON };
                    }
                };
                var grid1 = $("#grid_API").pqGrid({
                    width: "auto", height: 272,
                    selectionModel: { type: null, native: true },
                    dataModel: dataModel,
                    rowHt: 35,
                    hwrap: false,
                    wrap: false,
                    hoverMode: 'cell',
                    colModel: colModel,
                    complete: function (event, ui) {
                        var gridInstanceRPP = $('#grid_API').pqGrid('instance');
                        var rpp = gridInstanceRPP.options.pageModel.rPP;
                        var totalRecords = gridInstanceRPP.options.pageModel.totalRecords;
                        var curPage = gridInstanceRPP.options.pageModel.curPage;
                        var recordsLeft = totalRecords - (rpp * curPage);
                        APIOfferGridHeightChange(recordsLeft, rpp);
                        //BindAPIGridEvents();
                    },
                    editable: false,
                    postRenderInterval: -1, //synchronous post rendering.;
                    freezeCols: 2,
                    pageModel: { type: "remote", rPP: 5, strRpp: "{0}", rPPOptions: [5, 10] },
                    numberCell: { show: false },
                    rowSelect: function (evt, ui) {
                    }
                })/*.pqGrid('refreshDataAndView');*/
                if (refreshGrid) {
                    grid1.pqGrid('refreshDataAndView');
                }

            }

            function LoadLyticaAPIData(CId, refreshGrid, OfferId, isRefresh) {
                if (CId == 'AllParts') {
                    CId = ""
                }
                if (OfferId == null || OfferId == undefined) {
                    OfferId = -1;
                }
                var PartNo = "";
                var colModel = [

                    {
                        title: "LyticaAPIId",
                        width: "12%",
                        dataIndx: "LyticaAPIId",
                        hidden: true,
                        /*dataType: "float",*/
                    },
                    {
                        title: "Manufacturer",
                        width: "16%",
                        dataIndx: "Manufacturer",
                        /*dataType: "float",*/
                        filter: { crules: [{ condition: 'range' }] },
                        menuIcon: true,
                        render: function (ui) {
                            return
                        }
                    },
                    {
                        title: "Commodity",
                        width: "12%",
                        dataIndx: "Commodity",
                        /*dataType: "float",*/
                    },
                    {
                        title: "Original Part Searched",
                        width: "14%",
                        //dataType: "string",
                        filter: { crules: [{ condition: 'range' }] },
                        menuIcon: true,
                        dataIndx: "OriginalPartSearched",

                    },
                    {
                        title: "Avg. Price",
                        width: "8%",
                        //dataType: "float",
                        //align: "right",
                        dataIndx: "AveragePrice",
                    },
                    {
                        title: "Target Price",
                        width: "8%",
                        /*dataType: "float",*/
                        dataIndx: "TargetPrice",
                    },
                    {
                        title: "Market Leading",
                        width: "8%",
                        dataIndx: "MarketLeading",
                    },
                    {
                        title: "Life Cycle",
                        width: "8%",
                        //dataType: "Date",
                        filter: { crules: [{ condition: 'range' }] },
                        menuIcon: true,
                        dataIndx: "LifeCycle",
                    },
                    {
                        title: "Life Cycle Status",
                        width: "10%",
                        filter: { crules: [{ condition: 'range' }] },
                        menuIcon: true,
                        dataIndx: "lifeCycleStatus",
                    },
                    {
                        title: "Over All Risk",
                        width: "8%",
                        /*dataType: "float",*/
                        //align: "right",
                        filter: { crules: [{ condition: 'range' }] },
                        menuIcon: true,
                        dataIndx: "OverAllRisk",
                    },
                    {
                        title: "",
                        width: "7%",
                        /*dataType: "float",*/
                        //align: "right",
                        //dataIndx: "OverAllRisk",
                        render: function (ui) {
                            var originalpartno = ui.rowData.OriginalPartSearched;
                            return "<span class='gridlink'><button type='button' class='EMS_Add_Additional'>More Info</button></span>";
                            //var htmlstr = '<span class="gridlink"><a  style="cursor: pointer;" onClick = "return fncLyticaAPIDetails("' + originalpartno+'")> More Info </a></span>';
                            //return htmlstr;
                        },
                        postRender: function (ui) {
                            var rowIndx = ui.rowIndx,
                                grid = this,
                                $cell = grid.getCell(ui);
                            if (grid.hasClass({ rowData: ui.rowData, cls: 'pq-row-edit' })) {
                                //Edit button
                                $cell.find(".EMS_Add_Additional")
                                    .button({ label: "More Info" })
                            }
                            else {
                                //edit button
                                $cell.find(".EMS_Add_Additional").button()
                                    .off("click")
                                    .on("click", function (evt) {
                                        fncLyticaAPIDetails(ui.rowData.LyticaAPIId);
                                    });
                            }
                        }
                    }
                ];

                var qrystr = new URLSearchParams(window.location.search);
                var BomManagerID = qrystr.get("BOM");
                var strUpdatedBy = $("#UpdatedBy").val();
                var partNumber = beautifyPartNumber($("#hdpartid").val());

                var lyticaApi = isRefresh
                    ? 'RefreshLyticaAPIAfter3Days?BOMManagerID=' + BomManagerID + '&Parts=' + partNumber + '&MfrCode=' + $("#hdnManufacturerCode").val() + '&CustomerReqID=' + CId + '&MfrNo=' + $("#hdnManufacturerNo").val()
                    : 'GetLyticaAPIData?BOMManagerID=' + BomManagerID + '&Parts=' + partNumber + '&CustomerReqID=' + CId + '&FirstLoad=' + APIGridFirstLoad;

                var dataModel = {
                    location: "remote",
                    dataType: "JSON",
                    method: "POST",
                    //recIndx: "FElectronicsId",
                    url: lyticaApi,
                    getData: function (dataJSON) {
                        APIGridFirstLoad = false;
                        var data = dataJSON;
                        $.each(dataJSON, function (index, value) {
                            if (value.OfferId == OfferId) {
                                dataJSON[index].pq_rowcls = "red";
                            }
                        });
                        var totalRecords = 0;
                        if (dataJSON.length > 0) {
                            totalRecords = dataJSON[0].TotalCount;
                        }

                        var cur_page = 1;
                        if (dataJSON.length > 0) {
                            cur_page = dataJSON[0].curpage;
                        }
                        return { curPage: cur_page, totalRecords: totalRecords, data: dataJSON };
                    }
                };

                var grid1 = $("#grid_Lytica_API").pqGrid({
                    width: "auto", height: 272,
                    selectionModel: { type: null, native: true },
                    dataModel: dataModel,
                    rowHt: 35,
                    hwrap: false,
                    wrap: false,
                    hoverMode: 'cell',
                    colModel: colModel,
                    complete: function (event, ui) {
                        var gridInstanceRPP = $('#grid_Lytica_API').pqGrid('instance');
                        var rpp = gridInstanceRPP.options.pageModel.rPP;
                        var totalRecords = gridInstanceRPP.options.pageModel.totalRecords;
                        var curPage = gridInstanceRPP.options.pageModel.curPage;
                        var recordsLeft = totalRecords - (rpp * curPage);
                        LyticaAPIGridHeightChange(recordsLeft, rpp);
                    },
                    editable: false,
                    postRenderInterval: -1, //synchronous post rendering.;
                    //freezeCols: 2,
                    pageModel: { type: "remote", rPP: 5, strRpp: "{0}", rPPOptions: [5, 10] },
                    numberCell: { show: false },
                    rowSelect: function (evt, ui) {
                    }
                })/*.pqGrid('refreshDataAndView');*/
                if (refreshGrid) {
                    grid1.pqGrid('refreshDataAndView');
                }
            }

            function beautifyPartNumber(partNumber) {
                partNumber = partNumber.replace(" (Alternate)", "");
                partNumber = partNumber.replace("&", "_AMPERSAND_");
                partNumber = partNumber.replace("#", "_HASH_");
                partNumber = partNumber.replace("=", "_EQUALS_");

                return partNumber;
            }

            function fncLyticaAPIDetails(Part) {
                //alert(Part);
                LoadLyticaAPIDetails(Part);
                var modal = document.getElementById("LyticaAPIDetails");
                //AddOfferDropDownBind();
                modal.style.display = "block"
                $('html, body', window.parent.document).animate({ scrollTop: 240 }, "fast");

            }
            $('#CloseLyticaAPI').click(function () {
                var modal = document.getElementById("LyticaAPIDetails");
                modal.style.display = "none"
                $('html, body', window.parent.document).animate({ scrollTop: 240 }, "fast");
            });
            function LoadLyticaAPIDetails(PartName) {
                var PartNo = "";
                var colModel = [
                    {
                        title: "Manufacturer",
                        width: "33%",
                        dataIndx: "Manufacturer",
                        /*dataType: "float",*/
                        filter: { crules: [{ condition: 'range' }] },
                        menuIcon: true,
                        render: function (ui) {
                            return
                        }
                    },
                    {
                        title: "Part",
                        width: "33%",
                        dataIndx: "Part",
                        /*dataType: "float",*/
                    },
                    {
                        title: "Life Cycle Status",
                        width: "33%",
                        dataIndx: "lifeCycleStatus",

                    }
                ];

                var qrystr = new URLSearchParams(window.location.search);
                var BomManagerID = qrystr.get("BOM");
                var dataModel = {
                    location: "remote",
                    dataType: "JSON",
                    method: "POST",
                    //recIndx: "FElectronicsId",
                    url: 'GetLyticaAPIAlternateData?Parts=' + PartName,
                    getData: function (dataJSON) {
                        var headerJson = dataJSON.LyticaApiData;
                        $('#lblMfr').text(headerJson.Manufacturer);
                        $('#lblCommodity').text(headerJson.Commodity);
                        $('#lblAvgPrice').text(headerJson.AveragePrice);
                        $('#lblTargetPrice').text(headerJson.TargetPrice);
                        $('#lblMarketLeading').text(headerJson.MarketLeading);
                        $('#lblLifeCycle').text(headerJson.LifeCycle);
                        $('#lbllifeCycleStatus').text(headerJson.lifeCycleStatus);
                        $('#lblOverallRisk').text(headerJson.OverAllRisk);
                        $('#lblPartBreadth').text(headerJson.PartBreadth);
                        $('#lblManufacturerBreadth').text(headerJson.ManufacturerBreadth);
                        $('#lblDueDiligence').text(headerJson.DueDiligence);
                        $('#lblPartConcentration').text(headerJson.PartConcentration);

                        dataJSON = dataJSON.LyticaAPIAlternatePartData;
                        APIGridFirstLoad = false;
                        var data = dataJSON.data;
                        var totalRecords = 0;
                        if (dataJSON.length > 0) {
                            totalRecords = dataJSON[0].TotalCount;
                        }
                        var cur_page = 1;
                        if (dataJSON.length > 0) {
                            cur_page = dataJSON[0].curpage;
                        }
                        return { curPage: cur_page, totalRecords: totalRecords, data: dataJSON };
                    }
                };
                var grid1 = $("#grid_LyticataDetails").pqGrid({
                    width: "auto", height: 261,
                    selectionModel: { type: null, native: true },
                    dataModel: dataModel,
                    rowHt: 35,
                    hwrap: false,
                    wrap: false,
                    hoverMode: 'cell',
                    colModel: colModel,
                    complete: function (event, ui) {
                        var gridInstanceRPP = $('#grid_LyticataDetails').pqGrid('instance');
                        var rpp = gridInstanceRPP.options.pageModel.rPP;
                        var totalRecords = gridInstanceRPP.options.pageModel.totalRecords;
                        var curPage = gridInstanceRPP.options.pageModel.curPage;
                        var recordsLeft = totalRecords - (rpp * curPage);
                        //APIOfferGridHeightChange(recordsLeft, rpp);
                        //BindAPIGridEvents();
                    },
                    editable: false,
                    postRenderInterval: -1, //synchronous post rendering.;
                    //freezeCols: 2,
                    //pageModel: { type: "remote", rPP: 5, strRpp: "{0}", rPPOptions: [5, 10] },
                    numberCell: { show: false },
                    rowSelect: function (evt, ui) {
                    }
                })/*.pqGrid('refreshDataAndView');*/
                //if (refreshGrid) {
                grid1.pqGrid('refreshDataAndView');
            }

            function LoadBOMItemData(parts, refreshGrid) {

                var qrystr = new URLSearchParams(window.location.search);
                var BOMID = qrystr.get("BOM");

                if (parts == 'AllParts')
                    parts = '';
                var colModel = [
                    {
                        dataIndx: 'NotRelease',
                        dataType: 'bool',
                        hidden: true,
                        cb: { select: true, header: true }
                    },
                    {
                        title: "",
                        //type: 'checkbox',
                        width: "6%",
                        hidden: true,
                        dataIndx: 'NotRelease',
                        type: 'checkBoxSelection',
                        //cls: 'ui-state-default',
                        sortable: false,
                        //editable: true,
                        editable: function (ui) {
                            if (ui.rowData.QuoteNumber == null && ui.rowData.AutosourcingStatus == true)
                                return true;
                            else
                                return false
                        },

                        dataType: 'bool'//,
                        //cb: { header: false, select: true, all: true }
                    },
                    {
                        title: "CustomerRequirementId", //title of column.
                        width: "10%", //initial width of column
                        //dataType: "integer", //data type of column
                        dataIndx: "CustomerRequirementId",
                        hidden: true,
                        //should match one of the keys in row data.
                        //render: function (ui) {
                        //    customerrequirementidval = ui.rowData.CustomerRequirementId.toString();
                        //}
                    },
                    {
                        title: "", //title of column.
                        width: "10%", //initial width of column
                        //dataType: "integer", //data type of column
                        dataIndx: "BOMManagerNo", //should match one of the keys in row data.
                        hidden: true,
                        //render: function (ui) {
                        //    customerrequirementidval = ui.rowData.CustomerRequirementId.toString();
                        //}
                    },
                    {
                        title: "Line#",
                        width: "5%",
                        /*dataType: "float",*/
                        //align: "right",
                        dataIndx: "Line",
                        render: function (ui) {
                            var htmlstr = ui.rowData["Line"];
                            return htmlstr;
                        }
                    },
                    {
                        title: "Part No.<br/> Quote No.",
                        width: "15%",
                        //dataType: "string",
                        dataIndx: "Part",
                        filter: { crules: [{ condition: 'range' }] },
                        menuIcon: true,
                        render: function (ui) {
                            var htmlstr = "<span>" + ui.rowData.Part.toString() + "</span ><br/>";
                            htmlstr = htmlstr + " <span>" + (ui.rowData.QuoteNumber != null ? ui.rowData.QuoteNumber : '') + "</span > ";
                            return htmlstr;
                        }
                    },
                    {
                        title: "Status",
                        width: "12%",
                        //dataType: "string",
                        dataIndx: "ReqStatusText",
                        filter: { crules: [{ condition: 'range' }] },
                        menuIcon: true,
                        render: function (ui) {
                            var htmlstr;

                            if (ui.rowData.ReqStatus < 3) {
                                htmlstr = "<div class='BOMItemStatus' style='background-color:#FF6B6B;position: absolute;width: 99.5%;left: 0px;padding: 0px 5px;height: 33px;top: 0px;'> <span style='padding: 3px 3px;display:block;' class='OfferAddedBOMItem'>" + ui.rowData.ReqStatusText.toString() + "</span >";
                            }
                            else {
                                if (ui.rowData.ReqStatus == 3) {
                                    htmlstr = "<div class='BOMItemStatus' style='background-color:#F6DD5C;position: absolute;width: 99.5%;left: 0px;padding: 0px 5px;height: 33px;top: 0px;'> <span style='padding: 3px 3px;display:block;' class='NoOfferBOMItem'>" + ui.rowData.ReqStatusText.toString() + "</span >";
                                }
                                else {
                                    htmlstr = "<div class='BOMItemStatus' style='background-color:#7ED472;position: absolute;width: 99.5%;left: 0px;padding: 0px 5px;height: 33px;top: 0px;'> <span style='padding: 3px 3px;display:block;' class='NoOfferBOMItem'>" + ui.rowData.ReqStatusText.toString() + "</span >";
                                }
                            }

                            if (parseInt(ui.rowData.OfferCount) > 0) {
                                htmlstr = htmlstr + " <span>" + ui.rowData.OfferCount.toString() + " Offer(s)" + "</span ></div>";
                            }
                            else {
                                htmlstr = htmlstr + "</div>"
                            }
                            return htmlstr;
                        }
                    },

                    {
                        title: "Manufacturer",
                        width: "20%",
                        dataIndx: "ManufacturerName",
                        filter: { crules: [{ condition: 'range' }] },
                        menuIcon: true,
                        render: function (ui) {

                            var htmlstr = '<span class=' + "gridlink" + ' title="' + ui.rowData["ManufacturerName"] + '">' + "<a  href='../../Con_ManufacturerDetail.aspx?mfr=" + ui.rowData["ManufacturerNo"] + "' target='_Blank'>" + ui.rowData["ManufacturerName"] + "</a></span ><br/>";
                            return htmlstr;
                        }
                    },
                    {
                        title: "Quantity",
                        width: "12%",
                        dataType: "Date",
                        //align: "right",
                        dataIndx: "Quantity",
                        render: function (ui) {
                            var htmlstr = "<span>" + ui.rowData["Quantity"] + "</span ><br/>";
                            return htmlstr;
                        }
                    },
                    {
                        title: "Target Price",
                        width: "12%",
                        /*dataType: "float",*/
                        //align: "right",
                        dataIndx: "Price",
                        render: function (ui) {
                            var htmlstr = parseFloat(ui.rowData["Price"]).toFixed(4) + ' ' + ui.rowData["BOMManagerCurrencyCode"];
                            return htmlstr;
                        }
                    },
                    {
                        title: "Line Value",
                        width: "15%",
                        /*dataType: "float",*/
                        //align: "right",
                        dataIndx: "LineValue",
                        render: function (ui) {
                            var htmlstr = "<span>" + parseFloat(ui.rowData["LineValue"]).toFixed(4) + ' ' + ui.rowData["CurrencyCode"] + "</span ><br/>";
                            //htmlstr = htmlstr + "<span>" + ui.rowData.SalesmanName + "</span ><br/>";
                            return htmlstr;
                        }
                    },
                    {
                        title: "Release Note",
                        width: "15%",

                        dataIndx: "ReleaseNote",
                        render: function (ui) {
                            var htmlstr = '<span title="' + ui.rowData.ReleaseNote + '">' + ui.rowData.ReleaseNote + "</span ><br/>";
                            return htmlstr;
                        }
                    },
                    {
                        title: "More Info",
                        width: "8%",
                        render: function (ui) {
                            var url = "../../BOMManagerGrids.aspx?BOMID=" + BOMID + " &CRID=" + ui.rowData["CustomerRequirementId"];
                            var htmlstr = '<span class=' + "gridlink" + '">' + '<a href="#" onClick = "return LoadBOMItemMoreinfo(' + "'" + url + "'" + ')"' + "> More Info </a></span>";
                            return htmlstr;
                        }
                    }
                ];

                //main object to be passed to pqGrid constructor.
                var qrystr = new URLSearchParams(window.location.search);
                var qrystrkey = qrystr.get("BOM");
                //var dataModel = GetBOMSearchData1();
                var dataModel = {
                    location: "remote",
                    dataType: "JSON",
                    method: "POST",
                    url: 'GetBOMItem?BOMManagerId=' + qrystrkey + '&partSearch=' + parts + '&ClientId=0',
                    //data: { BOMId: BOMID, clientId: clientid, BOMDate: BomDateVal },
                    //url: "/pro/invoice.php",//for PHP
                    getData: function (dataJSON) {
                        var data = dataJSON;
                        if (dataJSON.length > 0) {
                            var strRequestToPOHubBy = data[0].RequestToPOHubBy;
                            var strSupportTeamMemberNo = data[0].SupportTeamMemberNo;
                            var strUpdatedBy = data[0].UpdatedBy;
                            var strBOMManagerCode = data[0].BomCode;
                            var strBOMManagerName = data[0].BOMName;
                            var strBOMManagerCompanyNo = data[0].CompanyNo;
                            var strBOMManagerCompanyName = data[0].CompanyName;
                        }
                        var totalRecords = 0;
                        if (data.length != 0) {
                            totalRecords = data[0].TotalCount;
                        }
                        var cur_page = 1;
                        if (dataJSON.length != 0) {
                            cur_page = dataJSON[0].curpage;
                        }
                        return { curPage: cur_page, totalRecords: totalRecords, data: data };
                    }
                };
                var grid1 = $("#grid_md").pqGrid({
                    width: "auto", height: 272,
                    selectionModel: { type: 'row', native: true },
                    dataModel: dataModel,
                    rowHt: 35,
                    hwrap: false,
                    wrap: false,
                    hoverMode: 'cell',
                    colModel: colModel,
                    complete: function (event, ui) {
                        var gridInstance = grid1.pqGrid('getInstance');
                        if (RowIndexPartBOMGrid > -1) {
                            gridInstance.grid.SelectRow().add({ rowIndx: RowIndexPartBOMGrid });
                            RowIndexPartBOMGrid = -1;
                        }

                        var gridInstanceRPP = $('#grid_md').pqGrid('instance');
                        var rpp = gridInstanceRPP.options.pageModel.rPP;
                        var totalRecords = gridInstanceRPP.options.pageModel.totalRecords;
                        var curPage = gridInstanceRPP.options.pageModel.curPage;
                        var recordsLeft = totalRecords - (rpp * curPage);
                        BOMItemGridHeightChange(recordsLeft, rpp);
                    },
                    beforeRowSelect: function (event, ui) {
                        if (MoreInfoClicked) {
                            MoreInfoClicked = false;
                            return false;
                        }
                        else {
                            return true;
                        }
                    },
                    cellKeyDown: function (evt, ui) {
                        var sr = this.SelectRow();
                        var currentIndx = ui.rowIndx;
                        var LastIndx = ((this.options.pageModel.curPage - 1) * this.options.pageModel.rPP) + this.pdata.length;
                        var StartIndx = ((this.options.pageModel.curPage - 1) * this.options.pageModel.rPP);
                        if (evt.keyCode == $.ui.keyCode.DOWN) {
                            if (currentIndx == LastIndx - 1) {
                                return;
                            }
                            sr.removeAll();
                            sr.add({ rowIndx: ui.rowIndx + 1 });
                        } else if (evt.keyCode == $.ui.keyCode.UP) {
                            if (currentIndx == StartIndx) {
                                return;
                            }
                            sr.removeAll();
                            sr.add({ rowIndx: ui.rowIndx - 1 });
                        }
                    },
                    editable: false,
                    //freezeCols: 2,
                    pageModel: { type: "local", rPP: 5, strRpp: "{0}", rPPOptions: [5, 10] },
                    numberCell: { show: false },
                    rowSelect: function (evt, ui) {
                        if (ui.addList[0] != undefined) {
                            var BOMStatus = $('#hdBomStatus').val();
                            if (BOMStatus < 7) {
                                $('#btnAddCommunicationNote').removeClass("add_comm_note_disabled");
                                $('#btnAddCommunicationNote').addClass("add_comm_note");
                            } else {
                                $('#btnAddCommunicationNote').off('click');
                                $('#btnAddCommunicationNote').removeClass("add_comm_note");
                                $('#btnAddCommunicationNote').addClass("add_comm_note_disabled");
                            }
                            $('#hdnBomPartNumber').val(ui.addList[0].rowData.Part);
                            $('#hdnBomPartId').val(ui.addList[0].rowData.CustomerRequirementId);
                            $('#hdnReqSttsBtBom').val(ui.addList[0].rowData.ReqStatus);


                            $('#hdnProductNo').val(ui.addList[0].rowData.ProductNo);
                            $('#hdnProductDesc').val(ui.addList[0].rowData.ProductDescription);
                            $('#hdnManufacturerNo').val(ui.addList[0].rowData.ManufacturerNo);
                            $('#hdnManufacturerName').val(ui.addList[0].rowData.ManufacturerName);
                            $('#hdnManufacturerCode').val(ui.addList[0].rowData.ManufacturerCode);
                            
                            $('#hdnDateCode').val(ui.addList[0].rowData.DateCode);
                            $('#hdnPackageNo').val(ui.addList[0].rowData.PackageNo);
                            $('#hdnPackageName').val(ui.addList[0].rowData.PackageName);
                            $('#hdnQuantity').val(ui.addList[0].rowData.Quantity);
                            $('#hdnMSL').val(ui.addList[0].rowData.MSL);
                            $('#hdnROHS').val(ui.addList[0].rowData.ROHS);

                            $('#HiddenBomCompanyNo').val(ui.addList[0].rowData.CompanyNo);
                            $('#HiddenBomRequestToPOHubBy').val(ui.addList[0].rowData.RequestToPOHubBy);
                            $('#HiddenBomUpdateByPH').val(ui.addList[0].rowData.UpdateByPH);
                            $('#HiddenBomContact2No').val(ui.addList[0].rowData.Contact2Id);

                            $("#rdAddoffer").prop("checked", true).trigger("click");
                            $('#divAddAltOffer').show();
                            $('#LinkAddOfferPart').show();

                            var CId = ui.addList[0].rowData.CustomerRequirementId;
                            var Part = ui.addList[0].rowData.FullPart;
                            LoadAutoSource($('#hdnBomPartId').val(), true);

                            LoadCommunicationNotesItemData(ui.addList[0].rowData.CustomerRequirementNumber, true, true);
                            
                            //Load KUB Assistant
                            var iframe = window.parent.frames['ctl00_cphMain_kubIframe'];
                            iframe.contentWindow.LoadPartNumber(ui.addList[0].rowData, qrystrkey);

                            var ActivetabName;

                            if (document.getElementsByClassName("active").length > 0) {
                                ActivetabName = document.getElementsByClassName("active")[0].innerHTML;
                                if (ActivetabName == "API Results") {
                                    LoadAPIOffersData($('#hdnBomPartId').val(), true, 0);
                                }
                                if (ActivetabName == "X-Match / Sourcing") {
                                    LoadXMatchSourcingData($('#hdnBomPartId').val(), true, 0);
                                }
                                if (ActivetabName == "Offers") {
                                    LoadEMSOffers($('#hdnBomPartNumber').val(), true, 0);
                                }
                                if (ActivetabName == "Lytica API") {
                                    LoadLyticaAPIData($('#hdnBomPartId').val(), true, 0, true);
                                }
                                else {
                                    LoadLyticaAPIData($('#hdnBomPartId').val(), true, 0, true);
                                }
                            }
                        }
                        else {
                            $('#LinkAddOfferPart').hide();
                            $('#divAddAltOffer').hide();
                            $('#hdnBomPartId').val('AllParts');
                            $('#hdnReqSttsBtBom').val('0');
                            $('#hdnProductNo').val('');
                            $('#hdnProductDesc').val('');
                            $('#hdnManufacturerNo').val('');
                            $('#hdnManufacturerName').val('');
                            $('#hdnManufacturerCode').val('');
                            $('#hdnDateCode').val('');
                            $('#hdnPackageNo').val('');
                            $('#hdnPackageName').val('');
                            $('#hdnQuantity').val('');
                            $('#hdnMSL').val('');
                            $('#hdnROHS').val('');

                            $('#btnAddCommunicationNote').removeClass("add_comm_note");
                            $('#btnAddCommunicationNote').addClass("add_comm_note_disabled");

                            LoadAutoSource($('#hdnBomPartId').val(), true);
                            if (document.getElementsByClassName("active").length > 0) {
                                ActivetabName = document.getElementsByClassName("active")[0].innerHTML;
                                if (ActivetabName == "API Results") {
                                    LoadAPIOffersData($('#hdnBomPartId').val(), true, 0);
                                }
                                if (ActivetabName == "X-Match / Sourcing") {
                                    LoadXMatchSourcingData($('#hdnBomPartId').val(), true, 0);
                                }
                                if (ActivetabName == "Offers") {
                                    LoadEMSOffers($('#hdnBomPartNumber').val(), true, 0);
                                }
                                if (ActivetabName == "Lytica API") {
                                    LoadLyticaAPIData($('#hdnBomPartId').val(), true, 0, false);
                                }
                            }
                        }
                    }

                });
            }

            function CheckMissingFields(reqStatus) {
                var qrystr = new URLSearchParams(window.location.search);
                var BomManagerID = qrystr.get("BOM");
                $.ajax({
                    processData: true,
                    contentType: 'application/json',
                    type: 'POST',
                    url: 'GetAllSourcingResultBOMManager?BOMManagerID=' + BomManagerID + '&reqStatus=' + reqStatus,
                    dataType: "json",
                    success: function (dataJSON) {
                        if (dataJSON.length > 0) {
                            if (dataJSON[0].MissingRequiredFeilds == true) {
                                alert('Manufacturer/Product is missing in attached offers.');
                                return false;
                            }
                            else {
                                LoadSendBomDataModal(reqStatus)
                            }
                        }
                    },
                    error: function (err) {}
                });
            }

            function LoadBOMDataToClipboard() {
                var colModel = []
                colModel = [
                    {
                        title: "<label>Select All</label><br/><input id='headerCheckbox' type='checkbox' />",
                        cb: { header: true, select: true, all: true },
                        width: "9%",
                        dataIndx: 'checkedColumn',
                        type: 'checkbox',
                        sortable: false,
                        editable: function (ui) {
                            return true;
                        },

                        dataType: 'bool'
                    },

                    {
                        title: "Line#", //title of column.
                        width: "7%", //initial width of column
                        dataIndx: "Line", //should match one of the keys in row data.
                    },
                    {
                        title: "Part No", //title of column.
                        width: "21%", //initial width of column
                        dataIndx: "Part", //should match one of the keys in row data.
                        filter: { crules: [{ condition: 'range' }] },
                        menuIcon: true,
                        render: function (ui) {
                            var htmlstr = '<span title="' + ui.rowData.Part + '">' + ui.rowData.Part + "</span ><br/>";
                            return htmlstr;
                        }
                    },
                    {
                        title: "Manufacturer", //title of column.
                        width: "21%", //initial width of column
                        dataIndx: "ManufacturerName", //should match one of the keys in row data.
                        render: function (ui) {
                            var htmlstr = '<span title="' + ui.rowData.ManufacturerName + '">' + ui.rowData.ManufacturerName + "</span ><br/>";
                            return htmlstr;
                        }
                    },
                    {
                        title: "Quantity", //title of column.
                        width: "21%", //initial width of column
                        dataIndx: "Quantity", //should match one of the keys in row data.
                    },
                    {
                        title: "Target Price",
                        width: "20%",
                        dataIndx: "Price",
                        render: function (ui) {

                            return "<span>" + parseFloat(ui.rowData.Price).toFixed(4) + " " + ui.rowData.BOMManagerCurrencyCode + "</span>";

                        }
                    }
                ];

                var qrystr = new URLSearchParams(window.location.search);
                var BomManagerID = qrystr.get("BOM");
                var parts = '';
                var dataModel = {
                    location: "remote",
                    dataType: "JSON",
                    method: "POST",
                    url: 'GetBOMItem?BOMManagerId=' + BomManagerID + '&partSearch=' + parts + '&ClientId=0',
                    getData: function (dataJSON) {
                        lstMergeCells = CalculateReleaseNoteMerge(dataJSON)
                        return { curPage: dataJSON.curPage, totalRecords: Object.keys(dataJSON).length, data: dataJSON };
                    }
                };
                var grid1 = $("#grid_SB").pqGrid({
                    width: "auto", height: 340,
                    selectionModel: { type: null, native: true },
                    dataModel: dataModel,
                    selectChange: function (evt, ui) {
                    },
                    colModel: colModel,
                    editable: false,
                    postRenderInterval: -1,
                    numberCell: { show: false },
                    complete: function (event, ui) {

                    },
                    filter: function (event, ui) {
                    },

                    refresh: function (event, ui) {

                    },


                }).pqGrid('refreshDataAndView');
                var grid1 = $('#grid_SB').pqGrid('instance');
                grid1.on('scroll', function (event, ui) {
                    // Fetch and display additional rows here based on startRowIndex and visibleRows
                    // Example: Fetch additional data and update the grid
                });
            }

            function LoadSendBOMData(reqStatus) {
                //reload table each time opened the popup
                var grid = $("#grid_SB").pqGrid("instance")
                if (grid != null) $("#grid_SB").pqGrid("destroy");

                //if reqStatus == '5' then show the copy to clipboard popup instead of release popup
                if (reqStatus == '5') {
                    LoadBOMDataToClipboard()
                }
                else {
                    var colModel = []
                    colModel = [

                        {
                            dataIndx: 'Status1',
                            dataType: 'bool',
                            hidden: true,
                            cb: { header: true, select: true, all: true },
                        },
                        {
                            title: "<label>Select All</label><br/><input id='headerCheckbox' type='checkbox' />",
                            cb: { header: true, select: true, all: true },
                            //type: 'checkbox',
                            width: "6%",
                            dataIndx: 'checkedColumn',
                            type: 'checkbox',
                            //cls: 'ui-state-default',
                            sortable: false,
                            //editable: true,
                            editable: function (ui) {
                                return true;

                                //return ui.rowData.ISPrimarySourceActual;
                            },

                            dataType: 'bool'//,
                            //cb: { header: false, select: true, all: true }
                        },
                        {
                            title: "Line#", //title of column.
                            width: "7%", //initial width of column
                            dataIndx: "LineNumber", //should match one of the keys in row data.
                            //  hidden: true,
                            render: function (ui) {
                                if (ui.rowData.HeaderFlag == true) {
                                    return ui.rowData.LineNumber.toString();
                                }
                                else
                                    return '';
                            }
                        },
                        {
                            title: "Part No", //title of column.
                            width: "10%", //initial width of column
                            dataIndx: "Part", //should match one of the keys in row data.
                            filter: { crules: [{ condition: 'range' }] },
                            menuIcon: true,
                        },
                        {
                            title: "Vendor Name", //title of column.
                            width: "16%", //initial width of column
                            //dataType: "integer", //data type of column
                            dataIndx: "SupplierName", //should match one of the keys in row data.
                            //filter: { crules: [{ condition: 'range' }] },
                            //menuIcon: true,
                        },
                        {
                            title: "Delivery Date", //title of column.
                            width: "10%", //initial width of column
                            //dataType: "integer", //data type of column
                            dataIndx: "DeliveryDate", //should match one of the keys in row data.
                            render: function (ui) {
                                if (ui.rowData.HeaderFlag == true) {
                                    return '';
                                }
                                else
                                    return getCustomDate(ui.rowData.DeliveryDate.toString());
                            }
                        },
                        {
                            title: "Buy Price",
                            width: "11%",
                            //dataType: "string",
                            dataIndx: "OriginalPrice",
                            render: function (ui) {
                                if (ui.rowData.HeaderFlag == true) {
                                    return '';
                                }
                                else
                                    return "<span>" + parseFloat(ui.rowData.OriginalPrice).toFixed(4) + " " + ui.rowData.ActualCurrencyCode + "</span>";

                            }
                        },
                        {
                            title: "Unit Sell Price",
                            width: "11%",
                            dataIndx: "Price",
                            render: function (ui) {
                                if (ui.rowData.HeaderFlag == true) {
                                    return '';
                                }
                                else {
                                    let renderHtml = ui.rowData.Price <= ui.rowData.OriginalPrice
                                        ? '<span style="color: red;">' + parseFloat(ui.rowData.Price).toFixed(4) + ' ' + ui.rowData.ActualCurrencyCode.toString() + '</span>'
                                        : "<span>" + parseFloat(ui.rowData.Price).toFixed(4) + ' ' + ui.rowData.ActualCurrencyCode.toString() + "</span>";
                                    return renderHtml;
                                }
                            }
                        },
                        {
                            title: "Reason For Loss",
                            width: "15%",
                            dataIndx: "Reason",
                            editable: function (ui) {
                                if (ui.rowData.Price <= ui.rowData.OriginalPrice && ui.rowData.CustomerRequirementNumber > 0) {
                                    return true;
                                }
                                else {
                                    return false;
                                }
                            },
                            render: disableTextRenderer
                        },
                        {
                            title: "Release Note",
                            width: "15%",
                            dataIndx: "ReleaseNote",
                            sortable: false,
                            editable: true,

                            dataType: 'string'//,
                        },
                        {
                            title: "HeaderFlag", //title of column.
                            width: "25%", //initial width of column
                            dataIndx: "HeaderFlag", //should match one of the keys in row data.
                            hidden: true,
                        },
                    ];

                    var qrystr = new URLSearchParams(window.location.search);
                    var BomManagerID = qrystr.get("BOM");
                    var lstMergeCells = []
                    var dataModel = {
                        location: "remote",
                        dataType: "JSON",
                        method: "POST",
                        url: 'GetAllSourcingResultBOMManager?BOMManagerID=' + BomManagerID + '&reqStatus=' + reqStatus,
                        getData: function (dataJSON) {
                            lstMergeCells = CalculateReleaseNoteMerge(dataJSON)
                            return { curPage: dataJSON.curPage, totalRecords: Object.keys(dataJSON).length, data: dataJSON };
                        }
                    };
                    var grid1 = $("#grid_SB").pqGrid({
                        width: "auto", height: 340,
                        selectionModel: { type: null, native: true },
                        dataModel: dataModel,
                        selectChange: function (evt, ui) {
                        },
                        //change: function (evt, ui) { alert('on changes'); },
                        colModel: colModel,
                        editable: false,
                        postRenderInterval: -1,
                        numberCell: { show: false },
                        complete: function (event, ui) {
                            this.option("mergeCells", lstMergeCells);
                            this.refreshView();

                            $('#headerCheckbox').change(function () {
                                FormatFinalData();
                            });
                            FormatFinalData();
                        },
                        filter: function (event, ui) {
                            FormatFinalDatawithFilter();
                        },
                        refresh: function (event, ui) {
                            //alert('refreshed');
                            FormatFinalDatawithFilter();
                        },
                    }).pqGrid('refreshDataAndView');
                    var grid1 = $('#grid_SB').pqGrid('instance');
                    grid1.on('scroll', function (event, ui) {
                        FormatFinalData();
                        // Fetch and display additional rows here based on startRowIndex and visibleRows
                        // Example: Fetch additional data and update the grid
                    });
                }

            }

            function disableTextRenderer(ui) {
                var //grid = $(this).pqGrid('getInstance').grid,
                    grid = this,
                    rowData = ui.rowData,
                    rowIndx = ui.rowIndx,
                    dataIndx = ui.dataIndx;

                if (grid.isEditableCell({ rowIndx: rowIndx, dataIndx: dataIndx }) == false) {
                    //inject disabled class into read only cells.
                    grid.addClass({ rowIndx: rowIndx, dataIndx: dataIndx, cls: 'disabled' });
                    grid.removeClass({ rowIndx: rowIndx, dataIndx: dataIndx, cls: 'reasonforloss' });
                }
                else {
                    grid.removeClass({ rowIndx: rowIndx, dataIndx: dataIndx, cls: 'disabled' });
                    grid.addClass({ rowIndx: rowIndx, dataIndx: dataIndx, cls: 'reasonforloss' });
                }

                return ui.rowData.Reason.toString();
            };

            function CalculateReleaseNoteMerge(dataJson) {
                var lstMergeCells = []

                var r1 = 0, rc = 0
                dataJson.forEach((x, index) => {
                    if (x.HeaderFlag) {
                        if (index > 0) {
                            var mergerInfo = {
                                r1: r1,
                                c1: 9,
                                rc: rc,
                                cc: 1
                            }
                            lstMergeCells.push(mergerInfo)
                            rc = 0
                        }
                        r1 = index
                    }
                    rc++
                })
                var mergerInfo = {
                    r1: r1,
                    c1: 9,
                    rc: rc,
                    cc: 1
                }
                lstMergeCells.push(mergerInfo)
                return lstMergeCells
            }

            function FormatFinalData() {
                var grid1 = $('#grid_SB').pqGrid('instance');
                var rowcount = grid1.getTotalRows();
                for (var i = 0; i < rowcount; i++) {
                    var headerflag = grid1.getRowData({ rowIndx: i }).HeaderFlag;
                    if (headerflag == true) {
                        try {
                            grid1.getCell({ rowIndx: i, dataIndx: "checkedColumn" })[0].children[0].children[0].remove();
                        } catch (e) {

                        }
                        grid1.addClass({ rowIndx: i, cls: 'highlighted-row' });
                        //grid1("addClass", { rowIndx: i, cls: 'highlighted-row' });
                    }

                }
            }

            function FormatFinalDatawithFilter() {
                var grid1 = $('#grid_SB').pqGrid('instance');
                var rowcount = grid1.getTotalRows();
                for (var i = 0; i < rowcount; i++) {
                    if (grid1.getRowData({ rowIndx: i }) != undefined) {
                        var headerflag = grid1.getRowData({ rowIndx: i }).HeaderFlag;
                        if (headerflag == true) {
                            try {
                                grid1.getCell({ rowIndx: i, dataIndx: "checkedColumn" })[0].children[0].children[0].remove();
                            } catch (e) {

                            }
                            grid1.addClass({ rowIndx: i, cls: 'highlighted-row' });
                        }

                        //grid1("addClass", { rowIndx: i, cls: 'highlighted-row' });
                    }

                }
            }

            function getCustomDate(dtinput) {
                var ParamDate = dtinput;
                if (dtinput != null) {
                    var dt = new Date(parseInt(ParamDate.replace("/Date(", "").replace(")/", "")))
                    return AppendZero(dt.getDate()) + "/" + AppendZero(dt.getMonth() + 1) + "/" + AppendZero(dt.getFullYear());
                }
                else {
                    return '';
                }
            }

            function AppendZero(n) {
                return (n < 10) ? '0' + n : n;
            }

            function myFunction() {
                /* $("#icon_id").attr("src", "..//App_Themes/Original/images/Nuggets/list/show.gif");*/
                /*jQuery('#icon_id img').attr('src', "http://localhost:49861/App_Themes/Original/images/Nuggets/list/show.gif");*/
                var x = document.getElementById("myDIV");


                if (x.style.display === "none") {
                    x.style.display = "block";
                    $(".head_in").removeClass("head_effect");
                    $("#icon_id").attr("src", "../../../../App_Themes/Original/images/Nuggets/list/show.gif");


                } else {
                    x.style.display = "none";
                    $(".head_in").addClass("head_effect");

                    $("#icon_id").attr("src", "../../../../App_Themes/Original/images/Nuggets/list/show.gif");

                }
                var x = document.getElementById("Export_exl");
                if (x.style.display === "none") {
                    x.style.display = "block";

                } else {
                    x.style.display = "none";
                }
            };


            function LoadEMSOffers(parts, refreshGrid, OfferId) {
                if (parts == 'AllParts')
                    parts = '';
                /*OpenSecondTab(event, 'divEMSOffer');*/
                if (OfferId == null || OfferId == undefined) {
                    OfferId = -1;
                }
                var colModel = [
                    { // Add offer button
                        title: "", editable: false, width: "8.5%", sortable: false,
                        render: function (ui) {
                            //if ($("#hdnBomStatus").text() < 5) {
                            if (ui.rowData["OfferAddFlag"] == true)
                                return "<button type='button' class='EMS_Add_Additional button_disabled'>Added</button>";
                            if (ui.rowData["REQStatus"] > 3 || $('#hdBomStatus').val() == 7) {
                                return "<button type='button' class='EMS_Add_Additional button_disabled'>Add Additional</button>";
                            }
                            else {
                                return "<button type='button' class='EMS_Add_Additional'>Add Additional</button>";
                            }
                        },
                        postRender: function (ui) {
                            var rowIndx = ui.rowIndx,
                                grid = this,
                                $cell = grid.getCell(ui);
                            if (grid.hasClass({ rowData: ui.rowData, cls: 'pq-row-edit' })) {
                                //Edit button
                                $cell.find(".EMS_Add_Additional")
                                    .button({ label: "Add Additional" })
                            }
                            else {
                                //edit button
                                $cell.find(".EMS_Add_Additional").button()
                                    .off("click")
                                    .on("click", function (evt) {
                                        AddNewOffer(rowIndx, grid, 1, ui);
                                    });
                            }
                        }
                    },
                    {
                        title: "Part No.",
                        width: "15%",
                        //dataType: "string",
                        dataIndx: "Part",
                        filter: { crules: [{ condition: 'range' }] },
                        menuIcon: true,
                        //render: function (ui) {
                        //    //return ui.rowData.BOMManagerNo + '-<br/>' + ui.rowData.Part
                        //    return /*"<span><a  href='../../Ord_CusReqDetail.aspx?req=" + ui.rowData.XPartNumber + "' target='_top'>" +*/ ui.rowData.Part + "</a></span>";

                        //}
                    },
                    {
                        title: "Vendor Name", //title of column.
                        width: "15%", //initial width of column
                        dataIndx: "SupplierName",
                        filter: { crules: [{ condition: 'range' }] },
                        menuIcon: true,
                        hidden: false,
                        render: function (ui) {

                            var htmlstr = '<span class=' + "gridlink" + ' title="' + ui.rowData["SupplierName"] + '">' + "<a  href='../../Con_CompanyDetail.aspx?cm=" + ui.rowData["SupplierNo"] + "' target='_Blank'>" + ui.rowData["SupplierName"] + "</a></span ><br/>";
                            return htmlstr;
                        }
                    },
                    {
                        title: "TypeName", //title of column.
                        width: "10%", //initial width of column
                        //dataType: "integer", //data type of column
                        dataIndx: "SupplierType", //should match one of the keys in row data.
                        hidden: false,
                        render: function (ui) {
                            var htmlstr = '<span title="' + ui.rowData["SupplierType"] + '">' + ui.rowData["SupplierType"] + "</span ><br/>";
                            //htmlstr = htmlstr + " <span>" + ui.rowData.CustomerPart.toString() + "</span > ";
                            return htmlstr;
                        }
                    },

                    {
                        title: "Manufacturer",
                        width: "15%",
                        //dataType: "float",
                        dataIndx: "ManufacturerName",
                        filter: { crules: [{ condition: 'range' }] },
                        menuIcon: true,
                        render: function (ui) {
                            var htmlstr;
                            if (ui.rowData["ManufacturerName"] != "") {
                                htmlstr = '<span class=' + "gridlink" + ' title="' + ui.rowData["ManufacturerName"] + '">' + "<a  href='../../Con_ManufacturerDetail.aspx?mfr=" + ui.rowData["ManufacturerNo"] + "' target='_Blank'>" + ui.rowData["ManufacturerName"] + "</a></span ><br/>";
                            }
                            return htmlstr;
                        }
                    },
                    {
                        title: "Price",
                        width: "10%",
                        dataIndx: "Price",
                        render: function (ui) {
                            var htmlstr = parseFloat(ui.rowData["Price"]).toFixed(4) + ' ' + ui.rowData["CurrencyCode"];
                            return htmlstr;
                        }
                    },
                    {
                        title: "Date",
                        width: "10%",
                        /*dataType: "float",*/
                        //align: "right",
                        dataIndx: "DLUP",
                        render: function (ui) {
                            var htmlstr = "<span>" + getCustomDate(ui.rowData.DLUP.toString()) + "</span>";
                            return htmlstr;
                        }
                    },
                    {
                        title: "MOQ",
                        width: "10%",
                        //dataType: "Date",
                        //align: "right",
                        dataIndx: "SupplierMOQ",
                        //render: function (ui) {
                        //    var htmlstr = "<span>" + ui.rowData["Quantity"] + "</span ><br/>";
                        //    //htmlstr = htmlstr + " <span>" + ui.rowData.PackageName.toString() + "</span > ";
                        //    return htmlstr;
                        //}
                    },
                    {
                        title: "SPQ",
                        width: "10%",
                        /*dataType: "float",*/
                        //align: "right",
                        dataIndx: "SPQ",
                        //render: function (ui) {
                        //    var htmlstr = "<span>" + ui.rowData["TargetSellPrice"] + " TBD" + "</span ><br/>";
                        //    //htmlstr = htmlstr + " <span>" + getCustomDate(ui.rowData.DatePromised.toString()) + "</span > ";
                        //    return htmlstr;
                        //}
                    },
                    {
                        title: "Stock Qty",
                        width: "10%",
                        /*dataType: "float",*/
                        //align: "right",
                        dataIndx: "Quantity",
                        //render: function (ui) {
                        //    var htmlstr = "<span>" + "TBD" + "</span ><br/>";
                        //    //htmlstr = htmlstr + "<span>" + ui.rowData.SalesmanName + "</span ><br/>";
                        //    return htmlstr;
                        //}
                    },
                    {
                        title: "D/C",
                        width: "10%",
                        /*dataType: "float",*/
                        //align: "right",
                        dataIndx: "DateCode",
                        //render: function (ui) {
                        //    var htmlstr = "<span>" + ui.rowData["CurrencyCode"] + "</span ><br/>";
                        //    //htmlstr = htmlstr + "<span>" + ui.rowData.SalesmanName + "</span ><br/>";
                        //    return htmlstr;
                        //}
                    },
                    {
                        title: "LT",
                        width: "10%",
                        /*dataType: "float",*/
                        //align: "right",
                        dataIndx: "LeadTime",
                        //render: function (ui) {
                        //    var htmlstr = "<span>" + ui.rowData["CurrencyCode"] + "</span ><br/>";
                        //    //htmlstr = htmlstr + "<span>" + ui.rowData.SalesmanName + "</span ><br/>";
                        //    return htmlstr;
                        //}
                    }
                ];

                //main object to be passed to pqGrid constructor.
                var qrystr = new URLSearchParams(window.location.search);
                var qrystrkey = qrystr.get("BOM");
                var cusReqId = $('#hdnBomPartId').val();
                var dataModel = {
                    location: "remote",
                    dataType: "JSON",
                    method: "POST",
                    recIndx: "OfferId",
                    url: 'IPOBOMAutoSource?partSearch=' + parts + '&BOM=' + qrystrkey + '&customerRequirementId=' + cusReqId,
                    getData: function (dataJSON, textStatus, jqXHR) {
                        var data = dataJSON;
                        $.each(dataJSON, function (index, value) {
                            if (value.OfferId == OfferId) {
                                dataJSON[index].pq_rowcls = "red";
                            }
                        });
                        var totalRecords = 0;
                        if (dataJSON.length != 0) {
                            totalRecords = dataJSON[0].TotalCount;
                        }
                        var cur_page = 1;
                        if (dataJSON.length > 0) {
                            cur_page = dataJSON[0].curpage;
                        }
                        return { curPage: cur_page, totalRecords: totalRecords, data: data };
                    }
                };
                var grid1 = $("#grid_EmsOffer").pqGrid({
                    width: "auto", height: 272,
                    selectionModel: { type: null, native: true },
                    dataModel: dataModel,
                    rowHt: 35,
                    hwrap: false,
                    wrap: false,
                    hoverMode: 'cell',
                    colModel: colModel,
                    complete: function (event, ui) {
                        var gridInstanceRPP = $('#grid_EmsOffer').pqGrid('instance');
                        var rpp = gridInstanceRPP.options.pageModel.rPP;
                        var totalRecords = gridInstanceRPP.options.pageModel.totalRecords;
                        var curPage = gridInstanceRPP.options.pageModel.curPage;
                        var recordsLeft = totalRecords - (rpp * curPage);
                        EMSOfferGridHeightChange(recordsLeft, rpp);
                    },
                    editable: false,

                    postRenderInterval: -1, //synchronous post rendering.;
                    freezeCols: 1,
                    pageModel: { type: "remote", rPP: 5, strRpp: "{0}", rPPOptions: [5, 10] },
                    numberCell: { show: false },
                    rowSelect: function (evt, ui) {}
                });

                $("#grid_EmsOffer").pqGrid({ render: function (event, ui) { /*debugger;*/ } });
                if ((parts != 'AllParts' && parts != "") || refreshGrid) {
                    grid1.pqGrid('refreshDataAndView');
                }
            }
            function LoadAutoSource(CId, refreshGrid) {
                if (CId == 'AllParts') {
                    CId = ""
                }
                /*OpenSecondTab(event, 'divEMSOffer');*/
                var colModel = [

                    {
                        title: "", editable: false, /*minWidth: 86,*/
                        sortable: false,
                        width: "3%",
                        render: function (ui) {

                            if (parseInt(ui.rowData["REQStatus"]) > 3 || $('#hdBomStatus').val() == 7) {
                                return "<button type='button' class='edit_btn button_disabled'>Edit</button>";
                            }

                            else {
                                return "<button type='button' class='edit_btn'>Edit</button>";
                            }

                        },
                        postRender: function (ui) {
                            var rowIndx = ui.rowIndx,
                                grid = this,
                                $cell = grid.getCell(ui);
                            if (grid.hasClass({ rowData: ui.rowData, cls: 'pq-row-edit' })) {
                                //Edit button
                                $cell.find(".edit_btn")
                                    .button({ label: "Edit" })
                            }
                            else {
                                //edit button
                                $cell.find(".edit_btn").button()
                                    .off("click")
                                    .on("click", function (evt) {
                                        EditSourceRow(rowIndx, grid, ui);
                                    });
                            }
                        }
                    },
                    {
                        title: "", editable: false, /*minWidth: 86,*/
                        sortable: false,
                        width: "7%",
                        render: function (ui) {

                            if (parseInt(ui.rowData["REQStatus"]) > 3 || $('#hdBomStatus').val() == 7) {
                                return "<button type='button' class='edit_btn button_disabled'>Remove Offer</button>";
                            }

                            else {
                                return "<button type='button' class='edit_btn'>Remove Offer</button>";
                            }

                        },
                        postRender: function (ui) {
                            var rowIndx = ui.rowIndx,
                                grid = this,
                                $cell = grid.getCell(ui);
                            if (grid.hasClass({ rowData: ui.rowData, cls: 'pq-row-edit' })) {
                                //Edit button
                                $cell.find(".edit_btn")
                                    .button({ label: "Edit" })
                            }
                            else {
                                //edit button
                                $cell.find(".edit_btn").button()
                                    .off("click")
                                    .on("click", function (evt) {
                                        RemoveOffer(rowIndx, ui);
                                    });
                            }
                        }
                    },
                    {
                        title: "Part No.",
                        width: "13%",
                        //dataType: "string",
                        dataIndx: "Part",
                        filter: { crules: [{ condition: 'range' }] },
                        menuIcon: true,
                        //filter: { crules: [{ condition: 'range' }] },
                        //menuIcon: true,
                        render: function (ui) {
                            if (ui.rowData.AlternateOfferFlag == true)
                                return '<span style="background: yellow;" title="' + ui.rowData["Part"] + '">' + ui.rowData["Part"] + "</span ><br/>"
                            else
                                return '<span  title="' + ui.rowData["Part"] + '">' + ui.rowData["Part"] + "</span ><br/>"
                        }
                    },
                    {
                        title: "Sourcing Category", //title of column.
                        width: "7%", //initial width of column
                        //dataType: "integer", //data type of column
                        dataIndx: "VendorCategory",
                        filter: { crules: [{ condition: 'range' }] },
                        menuIcon: true,
                        render: function (ui) {
                            return '<span title="' + ui.rowData["VendorCategory"] + '">' + ui.rowData["VendorCategory"] + "</span ><br/>"
                        }
                    },
                    {
                        title: "SourceID", //title of column.
                        width: "0%", //initial width of column
                        //dataType: "integer", //data type of column
                        dataIndx: "SourceId",
                        hidden: true,
                        //should match one of the keys in row data.

                    },
                    {
                        title: "Manufacturer",
                        width: "15%",
                        //dataType: "float",
                        //align: "right",
                        dataIndx: "ManufacturerName",
                        filter: { crules: [{ condition: 'range' }] },
                        menuIcon: true,
                        render: function (ui) {
                            var htmlstr;
                            if (ui.rowData["ManufacturerName"] != "") {
                                htmlstr = '<span class=' + "gridlink" + ' title="' + ui.rowData["ManufacturerName"] + '">' + "<a  href='../../Con_ManufacturerDetail.aspx?mfr=" + ui.rowData["ManufacturerNo"] + "' target='_Blank'>" + ui.rowData["ManufacturerName"] + "</a></span ><br/>";
                            }
                            return htmlstr;
                        }
                    },
                    {
                        title: "Quantity",
                        width: "7%",
                        /*dataType: "float",*/
                        //align: "right",
                        dataIndx: "ADJQty",
                        //render: function (ui) {
                        //    var htmlstr = "<span>" + "TBD" + "</span ><br/>";
                        //    //htmlstr = htmlstr + "<span>" + ui.rowData.SalesmanName + "</span ><br/>";
                        //    return htmlstr;
                        //}
                    },
                    {
                        title: "Cost",
                        width: "10%",
                        dataIndx: "Cost",
                        render: function (ui) {
                            var htmlstr = "<span>" + parseFloat(ui.rowData["Cost"]).toFixed(4) + " " + ui.rowData["CurrencyCode"] + "</span>";
                            return htmlstr;
                        }
                    },
                    {
                        title: "Re-sell",
                        width: "10%",
                        dataIndx: "Resale",
                        render: function (ui) {
                            var htmlstr;
                            if (parseFloat(ui.rowData["Resale"]) < parseFloat(ui.rowData["Cost"])) {
                                htmlstr = "<span style='color: red;'>" + parseFloat(ui.rowData["Resale"]).toFixed(4) + " " + ui.rowData["CurrencyCode"] + "</span>";
                            }
                            else {
                                htmlstr = "<span style='color: green;'>" + parseFloat(ui.rowData["Resale"]).toFixed(4) + " " + ui.rowData["CurrencyCode"] + "</span>";
                            }
                            return htmlstr;
                        }
                    },
                    {
                        title: "Total Profit",
                        width: "10%",
                        dataIndx: "Profit",
                        render: function (ui) {
                            var htmlstr;
                            if (parseFloat(ui.rowData["Resale"]) < parseFloat(ui.rowData["Cost"])) {
                                htmlstr = "<span style='color: red;'>" + parseFloat(ui.rowData["Profit"]).toFixed(4) + " " + ui.rowData["CurrencyCode"] + "</span>";
                            }
                            else {
                                htmlstr = "<span style='color: green;'>" + parseFloat(ui.rowData["Profit"]).toFixed(4) + " " + ui.rowData["CurrencyCode"] + "</span>";
                            }
                            return htmlstr;
                        }
                    },
                    {
                        title: "Margin",
                        width: "7%",
                        dataIndx: "Margin",
                        render: function (ui) {
                            var htmlstr;
                            if (parseFloat(ui.rowData["Resale"]) < parseFloat(ui.rowData["Cost"])) {
                                htmlstr = "<span style='color: red;'>" + ui.rowData["Margin"] + "</span>";
                            }
                            else {
                                htmlstr = "<span style='color: green;'>" + ui.rowData["Margin"] + "</span>";
                            }
                            return htmlstr;
                        }
                    },
                    {
                        title: "Vendor Name", //title of column.
                        width: "15%", //initial width of column
                        //dataType: "integer", //data type of column
                        dataIndx: "VendorName",
                        filter: { crules: [{ condition: 'range' }] },
                        menuIcon: true,
                        hidden: false,
                        render: function (ui) {

                            var htmlstr = '<span class=' + "gridlink" + ' title="' + ui.rowData["VendorName"] + '">' + "<a  href='../../Con_CompanyDetail.aspx?cm=" + ui.rowData["SupplierNo"] + "' target='_Blank'>" + ui.rowData["VendorName"] + "</a></span ><br/>";
                            return htmlstr;
                        }
                    },
                    {
                        title: "Date",
                        width: "8%",
                        /*dataType: "float",*/
                        //align: "right",
                        dataIndx: "OriginalEntryDate",
                        render: function (ui) {
                            var htmlstr = "<span>" + getCustomDate(ui.rowData.OriginalEntryDate.toString()) + "</span>";
                            return htmlstr;
                        }
                    },
                    {
                        title: "MOQ",
                        width: "7%",
                        //dataType: "Date",
                        //align: "right",
                        dataIndx: "MOQ",
                        //render: function (ui) {
                        //    var htmlstr = "<span>" + ui.rowData["Quantity"] + "</span ><br/>";
                        //    //htmlstr = htmlstr + " <span>" + ui.rowData.PackageName.toString() + "</span > ";
                        //    return htmlstr;
                        //}
                    },
                    {
                        title: "SPQ",
                        width: "7%",
                        /*dataType: "float",*/
                        //align: "right",
                        dataIndx: "SPQ",
                        //render: function (ui) {
                        //    var htmlstr = "<span>" + ui.rowData["TargetSellPrice"] + " TBD" + "</span ><br/>";
                        //    //htmlstr = htmlstr + " <span>" + getCustomDate(ui.rowData.DatePromised.toString()) + "</span > ";
                        //    return htmlstr;
                        //}
                    },
                    {
                        title: "D/C",
                        width: "7%",
                        /*dataType: "float",*/
                        //align: "right",
                        dataIndx: "DateCode",
                        //render: function (ui) {
                        //    var htmlstr = "<span>" + ui.rowData["CurrencyCode"] + "</span ><br/>";
                        //    //htmlstr = htmlstr + "<span>" + ui.rowData.SalesmanName + "</span ><br/>";
                        //    return htmlstr;
                        //    return htmlstr;
                        //}
                    },
                    {
                        title: "LT",
                        width: "7%",
                        /*dataType: "float",*/
                        //align: "right",
                        dataIndx: "LT",
                        //render: function (ui) {
                        //    var htmlstr = "<span>" + ui.rowData["CurrencyCode"] + "</span ><br/>";
                        //    //htmlstr = htmlstr + "<span>" + ui.rowData.SalesmanName + "</span ><br/>";
                        //    return htmlstr;
                        //}
                    },
                    {
                        title: "Reason For Loss",
                        width: "15%",
                        dataIndx: "Reason",
                        render: function (ui) {
                            var htmlstr = "";
                            if (parseFloat(ui.rowData["Resale"]).toFixed(4) < parseFloat(ui.rowData["Cost"]).toFixed(4)) {
                                htmlstr = '<span title="' + ui.rowData["Reason"].toString() + '">' + ui.rowData["Reason"].toString() + "</span><br>";
                            }
                            return htmlstr;
                        }
                    }
                ];

                //main object to be passed to pqGrid constructor.
                var qrystr = new URLSearchParams(window.location.search);
                var qrystrkey = qrystr.get("BOM");
                var dataModel = {
                    location: "remote",
                    dataType: "JSON",
                    method: "POST",
                    recIndx: "SourceId",
                    url: 'GetAutoSourcingResult?BOM=' + qrystrkey + '&CustomerReqID=' + CId,
                    beforeSend: function () {
                        clearSourcingResultDetails();
                    },
                    getData: function (dataJSON) {
                        var data = dataJSON;
                        GetBOMManagerStatus(qrystrkey);
                        if (BOMItemRefresh) {
                            $('#grid_md').pqGrid('refreshDataAndView');
                            BOMItemRefresh = false;
                        }
                        var totalRecords = 0;
                        if (dataJSON.length != 0) {
                            totalRecords = dataJSON[0].TotalRecords;
                        }
                        var cur_page = 1;
                        if (dataJSON.length != 0) {
                            cur_page = dataJSON[0].curpage;
                        }
                        return { curPage: cur_page, totalRecords: totalRecords, data: data };
                    }
                };
                var grid1 = $("#grid_AutoSource").pqGrid({
                    width: "auto", height: 272,
                    selectionModel: { type: 'row', mode: 'single' },
                    dataModel: dataModel,
                    rowHt: 35,
                    hwrap: false,
                    wrap: false,
                    hoverMode: 'cell',
                    postRenderInterval: -1, //synchronous post rendering.;
                    colModel: colModel,
                    editable: false,
                    freezeCols: 2,
                    pageModel: { type: "remote", rPP: 5, strRpp: "{0}", rPPOptions: [5, 10] },
                    numberCell: { show: false },
                    editable: function (ui) {
                        return this.hasClass({ rowIndx: ui.rowIndx, cls: 'pq-row-edit' });
                    },
                    rowSelect: function (evt, ui) {
                        if (ui.addList.length > 0 && ui.addList[0].rowData) {
                            var rowData = ui.addList[0].rowData;
                            displaySourcingResultDetails(rowData);
                        }
                    },
                    complete: function (event, ui) {
                        var gridInstanceRPP = $('#grid_AutoSource').pqGrid('instance');
                        var rpp = gridInstanceRPP.options.pageModel.rPP;
                        var totalRecords = gridInstanceRPP.options.pageModel.totalRecords;
                        var curPage = gridInstanceRPP.options.pageModel.curPage;
                        var recordsLeft = totalRecords - (rpp * curPage);
                        AutoSourceGridHeightChange(recordsLeft, rpp);
                    }
                });
                if (refreshGrid) {
                    grid1.pqGrid('refreshDataAndView');
                }
                if (editsource == true) {
                    grid1.pqGrid('refreshDataAndView');
                    editsource = false;
                }
            }
            $("#textProductName").autocomplete({
                minLength: 2,
                source: function (request, response) {
                    // Fetch data
                    $.ajax({
                        processData: true,
                        contentType: 'application/json',
                        type: 'GET',
                        url: handlerUrl + '?action=GetProduct&ProdSearch=' + $("#textProductName").val(),
                        dataType: "json",
                        //data: {
                        //    projects: request.term
                        //},
                        success: function (data) {
                            autocompleteCount = data.length;
                            response(data);


                        },
                        error: function (err) {
                            /* alert(err);*/

                        }
                    });
                },
                open: function (event, ui) {
                    $("ul").prepend('<li class ="AutoCompleteHeader""><div>' + autocompleteCount + ' result(s)</div></li>');
                    autocompleteCount = 0;
                },
                select: function (event, ui) {
                    $("#textProductName").css("display", "none");
                    $("#prodid").val(ui.item.value);
                    $("#LabelProduct").text(SetCleanText(ui.item.label, true));
                    $("#RemoveProduct").css("display", "block");

                    return false;
                }
            }).autocomplete("instance")._renderItem = function (ul, item) {
                    return $("<li>")
                        .append("<div>" + SetCleanText(item.label, true) + "</div>")
                        .appendTo(ul);
                };


            $("#textSupplier").autocomplete({
                minLength: 2,
                source: function (request, response) {
                    // Fetch data
                    $.ajax({
                        processData: true,
                        contentType: 'application/json',
                        type: 'GET',
                        url: handlerUrl + '?action=GetSupplier&SuppSearch=' + $("#textSupplier").val(),
                        dataType: "json",
                        success: function (data) {
                            autocompleteCount = data.length;
                            response(data);


                        },
                        error: function (err) {}
                    });
                },
                open: function (event, ui) {
                    $("ul").prepend('<li class ="AutoCompleteHeader""><div>' + autocompleteCount + ' result(s)</div></li>');
                    autocompleteCount = 0;
                },
                select: function (event, ui) {

                    $("#textSupplier").css("display", "none");
                    $("#hdsupplier").val(ui.item.value);
                    $("#LabelSupplier").text(SetCleanText(ui.item.label, true));
                    $("#RemoveSupplier").css("display", "block");
                    $("#RowAddOfferSupplierError").css("display", "none");
                    $("#RowAddOfferSupplier").css("background-color", "#56954E");
                    getSupplierCurrencyDetails(ui.item.value, -1, true);
                    return false;
                }
            }).autocomplete("instance")._renderItem = function (ul, item) {
                    return $("<li>")
                        .append("<div>" + SetCleanText(item.label, true) + "</div>")
                        .appendTo(ul);
                };

            function getSupplierCurrencyDetails(supplierId, selectedCurrency, isAddOffer) {
                $.ajax({
                    processData: true,
                    contentType: 'application/json',
                    type: 'POST',
                    url: 'GetSupplierCurrencyDetails?SupplierId=' + supplierId,
                    dataType: "json",
                    success: function (data) {
                        $('#hdnGlobalCurrencyNo').val(data.GlobalCurrencyNo);
                        var supplierWarranty = data.SupplierWarranty ?? 0;
                        if (isAddOffer) {
                            BindCurrencyList('ddlCurrency', selectedCurrency);
                            $('#textSupplierWarranty').val(supplierWarranty);
                        } else {
                            BindCurrencyList('ddlEditCurrency', selectedCurrency);
                        }
                        supplierWarranty = null;
                    },
                    error: function (err) {}
                });
            }

            $('#PartSearch').click(function () {
                $("#textPartNo").autocomplete("enable");
                if ($("#textPartNo").val().length > 3) {
                    $("#textPartNo").autocomplete('search', $("#textPartNo").val());
                }
            });

            $("#textPartNo").autocomplete({
                minLength: 4,
                source: function (request, response) {
                    // Fetch data
                    $.ajax({
                        processData: true,
                        contentType: 'application/json',
                        type: 'POST',
                        url: 'AutoSearchBomManager?Part=' + $("#textPartNo").val(),
                        dataType: "json",
                        success: function (data) {
                            autocompleteCount = data.length;
                            response(data);
                        },
                        error: function (err) {
                        }
                    });
                },
                open: function (event, ui) {
                    $("ul").prepend('<li class ="AutoCompleteHeader""><div>' + autocompleteCount + ' result(s)</div></li>');
                    autocompleteCount = 0;
                },
                select: function (event, ui) {
                    $("#textPartNo").css("display", "none");
                    $("#hdpartid").val(ui.item.PartName);
                    $("#LabelPartNo").text(SetCleanText(ui.item.PartName, true));
                    $("#RemovePartNo").css("display", "block");
                    $("#ValidationErrorPartNo").css("display", "none");
                    $("#RowAddOfferPartNoError").css("display", "none");
                    $("#RowAddOfferPartNo").css("background-color", "#56954E");
                    $("#partsearchdiv").css("display", "none");
                    if (ui.item.ProductNo != null && ui.item.ProductNo > 0) {
                        $("#textProductName").css("display", "none");
                        $("#prodid").val(ui.item.ProductNo);
                        $("#LabelProduct").text(SetCleanText(ui.item.ProductDescription, true));
                        $("#RemoveProduct").css("display", "block");
                    }
                    if (ui.item.ManufacturerNo != null && ui.item.ManufacturerNo > 0) {
                        $("#textManufacturer").css("display", "none");
                        $("#hdmrfid").val(ui.item.ManufacturerNo);
                        $("#LabelManufacturer").text(SetCleanText(ui.item.ManufacturerName, true));
                        $("#RemoveManufacturer").css("display", "block");
                    }
                }
            }).autocomplete("instance")._renderItem = function (ul, item) {
                return $("<li>")
                    .append("<div>" + SetCleanText(item.PartName, true) + "</div>")
                    .appendTo(ul);
            };

            $("#textManufacturer").autocomplete({
                minLength: 2,
                source: function (request, response) {
                    // Fetch data
                    $.ajax({
                        processData: true,
                        contentType: 'application/json',
                        type: 'GET',
                        url: handlerUrl + '?action=GetManufacturer&MrfSearch=' + $("#textManufacturer").val(),
                        dataType: "json",
                        success: function (data) {
                            autocompleteCount = data.length;
                            response(data);
                        },
                        error: function (err) {
                            /* alert(err);*/
                        }
                    });
                },
                open: function (event, ui) {
                    $("ul").prepend('<li class ="AutoCompleteHeader""><div>' + autocompleteCount + ' result(s)</div></li>');
                    autocompleteCount = 0;
                },
                select: function (event, ui) {
                    $("#textManufacturer").css("display", "none");
                    $("#hdmrfid").val(ui.item.value);
                    $("#LabelManufacturer").text(SetCleanText(ui.item.label, true));
                    $("#RemoveManufacturer").css("display", "block");

                    return false;
                }
            }).autocomplete("instance")._renderItem = function (ul, item) {
                    return $("<li>")
                        //.append("<div>" + item.label + "<br>" + item.value + "</div>")
                        .append("<div>" + SetCleanText(item.label, true) + "</div>")
                        .appendTo(ul);
                };

            $("#textPackage").autocomplete({
                minLength: 2,
                source: function (request, response) {
                    // Fetch data
                    $.ajax({
                        processData: true,
                        contentType: 'application/json',
                        type: 'GET',
                        url: 'GetPackages?search=' + $("#textPackage").val() + '%',
                        dataType: "json",

                        success: function (data) {
                            autocompleteCount = data.length;
                            response($.map(data, function (item) {
                                return { label: item.PackageDescription, value: item.PackageId };
                                //return { label: item.EmployeeName, value: item.LoginId };
                            }))
                        },
                        error: function (err) {
                            /*alert(err);*/
                        }
                    });
                },
                open: function (event, ui) {
                    $("ul").prepend('<li class ="AutoCompleteHeader""><div>' + autocompleteCount + ' result(s)</div></li>');
                    autocompleteCount = 0;
                },
                select: function (event, ui) {
                    $("#textPackage").css("display", "none");
                    $("#pkgid").val(ui.item.value);
                    $("#LabelPackage").text(SetCleanText(ui.item.label, true));
                    $("#RemovePackage").css("display", "block");

                    return false;
                }
            }).autocomplete("instance")._renderItem = function (ul, item) {
                    return $("<li>")
                        .append("<div>" + SetCleanText(item.label, true) + "</div>")
                        .appendTo(ul);
                };

            function AddOfferDropDownBind() {
                $('#hdnGlobalCurrencyNo').val(0);
                BindCurrencyList('ddlCurrency', -1);
                BindMSL('ddlMSL', -1);
                BindOfferStatus('ddlOfferStatus', -1);
                BindROHSStatus('ddlROHS', -1);
                BindCountryOfOrigin('ddlCountryOfOrigin', -1);
                BindRegion('ddlRegion', -1);
            }

            function BindMSL(dropdownId, MSL) {
                $("#" + dropdownId).html(refershOption);
                $.ajax({
                    type: 'GET',
                    contentType: 'application/json',
                    url: handlerUrl + '?action=GetMSL',
                    async: true,
                    success: function (data) {
                        var jsonData = JSON.parse(data);
                        listItems = '';
                        listItems = defaultOption;
                        for (var i = 0; i < jsonData.length; i++) {
                            listItems += optionStart + jsonData[i].MSLLevelId + "'>" + jsonData[i].MSLLevels + optionEnd;
                        }
                        $("#" + dropdownId).html(listItems);
                        $("#" + dropdownId).val(MSL);
                    },
                    error: function () {}
                });
            }

            function BindOfferStatus(dropdownId, OfferStatus) {
                $("#" + dropdownId).html(refershOption);
                $.ajax({
                    type: 'GET',
                    contentType: 'application/json',
                    url: handlerUrl + '?action=GetOfferStatus',
                    async: true,
                    success: function (data) {
                        var jsonData = JSON.parse(data);
                        listItems = '';
                        listItems = defaultOption;
                        for (var i = 0; i < jsonData.length; i++) {
                            listItems += optionStart + jsonData[i].ID + "'>" + jsonData[i].Name + optionEnd;
                        }
                        $("#" + dropdownId).html(listItems);
                        $("#" + dropdownId).val(OfferStatus);
                    },
                    error: function () {}
                });
            }

            function BindROHSStatus(dropdownId, ROHS) {
                $('#' + dropdownId).html(refershOption);
                $.ajax({
                    type: 'GET',
                    contentType: 'application/json',
                    url: handlerUrl + '?action=GetROHSStatus',
                    async: true,
                    success: function (data) {
                        var jsonData = JSON.parse(data);
                        listItems = '';
                        listItems = defaultOption;
                        for (var i = 0; i < jsonData.length; i++) {
                            listItems += optionStart + jsonData[i].ID + "'>" + jsonData[i].Name + optionEnd;
                        }
                        $('#' + dropdownId).html(listItems);
                        $('#' + dropdownId).val(ROHS);
                    },
                    error: function () {}
                });
            }

            function BindCurrencyList(dropdownId, currency) {
                $("#" + dropdownId).html(refershOption);
                $.ajax({
                    type: 'POST',
                    contentType: 'application/json',
                    dataType: 'json',
                    url: 'GetSupplierCurrency?GlobalCurrencyNo=' + $('#hdnGlobalCurrencyNo').val(),
                    async: true,
                    success: function (data) {
                        var jsonData = data;
                        listItems = '';
                        listItems = defaultOption;
                        for (var i = 0; i < jsonData.length; i++) {
                            listItems += optionStart + jsonData[i].CurrencyId + "'>" + jsonData[i].CurrencyCode + ' - ' + jsonData[i].CurrencyDescription + optionEnd;
                        }
                        $("#" + dropdownId).html(listItems);
                        if (currency) {
                            $("#" + dropdownId).val(currency);
                            return;
                        }
                        if (jsonData.length > 0) {
                            $("#" + dropdownId).val(jsonData[0].CurrencyId);
                            return;
                        }
                        $("#ddlCurrency").val(0);
                    },
                    error: function (jqXHR, textStatus, errorThrown) {}
                });
            }

            function BindCountryOfOrigin(dropdownId, countryOfOrigin) {
                $("#" + dropdownId).html(refershOption);
                $.ajax({
                    type: 'GET',
                    contentType: 'application/json',
                    url: handlerUrl + '?action=GetCountryOfOrigin',
                    async: true,
                    success: function (data) {
                        var jsonData = JSON.parse(data);
                        listItems = '';
                        listItems = defaultOption;
                        for (var i = 0; i < jsonData.length; i++) {
                            listItems += optionStart + jsonData[i].ID + "'>" + jsonData[i].Name + optionEnd;
                        }
                        $("#" + dropdownId).html(listItems);
                        $("#" + dropdownId).val(countryOfOrigin);
                    },
                    error: function () {}
                });
            }

            function BindRegion(dropdownId, region) {
                $("#" + dropdownId).html(refershOption);
                $.ajax({
                    type: 'GET',
                    contentType: 'application/json',
                    url: handlerUrl + '?action=GetRegion',
                    async: true,
                    success: function (data) {
                        var jsonData = JSON.parse(data);
                        listItems = '';
                        listItems = defaultOption;
                        for (var i = 0; i < jsonData.length; i++) {
                            listItems += optionStart + jsonData[i].ID + "'>" + jsonData[i].Name + optionEnd;
                        }
                        $("#" + dropdownId).html(listItems);
                        $("#" + dropdownId).val(region);
                    },
                    error: function () {}
                });
            }

            function SaveNewOffer() {
                if (ValidateAddOfferForm()) {
                    var qrystr = new URLSearchParams(window.location.search);
                    var BomManagerID = qrystr.get("BOM");
                    var alternateRequirementNumber = $('#hdnBomPartId').val();
                    if (alternateRequirementNumber == 'AllParts')
                        alternateRequirementNumber = '0';

                    var offer = {
                        BOMManagerId: BomManagerID,
                        AlterCRNumber: alternateRequirementNumber,
                        Supplier: $('#hdsupplier').val(),
                        SupplierName: $('#LabelSupplier').text(),
                        PartNo: $('#hdpartid').val(),
                        ROHS: $("#ddlROHS").val(),
                        ManufacturerNo: $("#hdmrfid").val(),
                        ManufacturerName: $("#LabelManufacturer").text(),
                        DateCode: $("#textdatecode").val(),
                        ProductNo: $('#prodid').val(),
                        ProductName: $('#LabelProduct').text(),
                        PackageNo: $("#pkgid").val(),
                        PackageName: $("#LabelPackage").text(),
                        Quantity: $('#textQuantity').val(),
                        Price: $('#textPrice').val(),
                        Currency: $("#ddlCurrency").val(),
                        OfferStatus: $('#ddlOfferStatus').val(),
                        SupplierTotalQSA: $('#texttotalQuantityStockAvil').val(),
                        SupplierMOQ: $('#textMinimumOderderQuantity').val(),
                        SupplierLTB: $('#textlastTimeBuy').val(),
                        MSLNo: $('#ddlMSL').val(),
                        SPQ: $('#textSPQ').val(),
                        LeadTime: $('#textLeadTime').val(),
                        FactorySealed: $('#textFactorySealedYN').val(),
                        ROHSStatus: $('#textROHSStatusYN').val(),
                        Notes: $('#textNotes').val(),
                        SupplierWarranty: $('#textSupplierWarranty').val(),
                        CountryOfOrigin: $("#ddlCountryOfOrigin").val(),
                        SellPrice: $('#textSellPrice').val(),
                        ShippingCost: $('#textShippingCost').val(),
                        Region: $('#ddlRegion').val(),
                        DeliveryDate: $('#textAddDeliveryDate').val(),
                        IsTestingRecommended: $('#chkTestingRecommended').is(":checked"),
                        Reason: $('#textUplipSellPriceLessReason').val()
                    };

                    $.ajax({
                        processData: false,
                        contentType: 'application/json',
                        type: 'POST',
                        url: 'BOMManagerNewOffer',
                        async: true,
                        dataType: 'json',
                        data: JSON.stringify({ offer: offer }),
                        success: function (data) {
                            ClearOfferModalData();
                            var modal = document.getElementById("AddOfferModal");
                            modal.style.display = "none"
                            $('html, body', window.parent.document).animate({ scrollTop: 240 }, "fast");
                            LoadEMSOffers($('#hdnBomPartNumber').val(), true, 0);
                            $("#btnHeaderSave").css('pointer-events', 'auto');
                            $("#btnFooterSave").css('pointer-events', 'auto');
                            return true;
                        },
                        error: function () {
                            $("#AddOfferValidationError").css("display", "none");
                            $("#btnHeaderSave").css('pointer-events', 'auto');
                            $("#btnFooterSave").css('pointer-events', 'auto');
                            return false;
                        },
                    });
                }
                else {
                    $("#AddOfferValidationError").css("display", "block");
                    $("#btnHeaderSave").css('pointer-events', 'auto');
                    $("#btnFooterSave").css('pointer-events', 'auto');
                }
            }

            function ClearOfferModalData() {
                $("#textSupplier").css("display", "block");
                $("#textSupplier").val(null);
                $("#hdsupplier").val(null);
                $("#LabelSupplier").text(null);
                $("#RemoveSupplier").css("display", "none");
                $("#textPartNo").css("display", "block");
                $("#textPartNo").val(null);
                $("#hdpartid").val(null);
                $("#LabelPartNo").text(null);
                $("#RemovePartNo").css("display", "none");
                $("#textManufacturer").css("display", "block");
                $("#textManufacturer").val(null);
                $("#hdmrfid").val(null);
                $("#LabelManufacturer").text(null);
                $("#RemoveManufacturer").css("display", "none");
                $("#textProductName").css("display", "block");
                $("#textProductName").val(null);
                $("#prodid").val(null);
                $("#LabelProduct").text(null);
                $("#RemoveProduct").css("display", "none");
                $("#textPackage").css("display", "block");
                $("#textPackage").val(null);
                $("#pkgid").val(null);
                $("#LabelPackage").text(null);
                $("#RemovePackage").css("display", "none");
                $("#ddlROHS").empty().append("--Select--");
                $("#textdatecode").val(null);
                $("#textQuantity").val(null);
                $("#textPrice").val(null);
                $("#textSellPrice").val(null);
                $("#textShippingCost").val(null);
                $("#ddlCurrency").empty().append("--Select--");
                $("#ddlOfferStatus").empty().append("--Select--");
                $("#texttotalQuantityStockAvil").val(null);
                $("#textMinimumOderderQuantity").val(null);
                $("#textlastTimeBuy").val(null);
                $("#ddlMSL").empty().append("--Select--");
                $("#textSPQ").val(null);
                $("#textLeadTime").val(null);
                $("#textFactorySealedYN").val(null);
                $("#textROHSStatusYN").val(null);
                $("#textNotes").val(null);
                $('#textSupplierWarranty').val(null);
                $('#textAddDeliveryDate').val(null);
                $('#textUplipSellPriceLessReason').val(null);
                $("#ddlCountryOfOrigin").empty().append("--Select--");
                $("#ddlRegion").empty().append("--Select--");
                $('#chkTestingRecommended').removeAttr('checked');
                $("#ValidationErrorSupplier").css("display", "none");
                $("#ValidationErrorPartNo").css("display", "none");
                $("#ValidationErrorQuantity").css("display", "none");
                $("#ValidationErrorPrice").css("display", "none");
                $("#ValidationErrorCurrency").css("display", "none");
                $("#ValidationErrorProduct").css("display", "none")
                $("#ValidationErrorManufacturer").css("display", "none")
                $("#RowAddOfferSupplier").css("background-color", "#56954E");
                $("#RowAddOfferSupplierError").css("background-color", "#56954E");
                $("#RowAddOfferSupplierError").css("display", "none");
                $("#RowAddOfferProduct").css("background-color", "#56954E");
                $("#RowAddOfferProductError").css("background-color", "#56954E");
                $("#RowAddOfferProductError").css("display", "none");
                $("#RowAddOfferManufacturer").css("background-color", "#56954E");
                $("#RowAddOfferManufacturerError").css("background-color", "#56954E");
                $("#RowAddOfferManufacturerError").css("display", "none");
                $("#RowAddOfferPartNo").css("background-color", "#56954E");
                $("#RowAddOfferPartNoError").css("background-color", "#56954E");
                $("#RowAddOfferPartNoError").css("display", "none");
                $("#RowAddOfferQuantity").css("background-color", "#56954E");
                $("#RowAddOfferQuantityError").css("background-color", "#56954E");
                $("#RowAddOfferQuantityError").css("display", "none");
                $("#RowAddOfferPrice").css("background-color", "#56954E");
                $("#RowAddOfferPriceError").css("display", "none");
                $("#RowAddOfferSellPrice").css("background-color", "#56954E");
                $("#RowAddOfferSellPriceError").css("display", "none");
                $("#RowAddOfferShippingCost").css("background-color", "#56954E");
                $("#RowAddOfferShippingCostError").css("display", "none");
                $("#RowAddOfferCurrency").css("background-color", "#56954E");
                $("#RowAddOfferCurrencyError").css("background-color", "#56954E");
                $("#RowAddOfferCurrencyError").css("display", "none");
                $("#RowAddOfferRegion").css("background-color", "#56954E");
                $("#RowAddOfferDeliveryDate").css("background-color", "#56954E");
                $("#ValidationErrorCurrency").css("background-color", "#56954E");
                $("#RowUplipSellPriceLess").css("display", "none");
                $("#RowUplipSellPriceLessReason").css("display", "none");
                $("#RowUplipSellPriceLessReason").css("background-color", "#56954E");
                $("#RowUplipSellPriceLessReasonError").css("display", "none");
                $("#AddOfferValidationError").css("display", "none");
                $("#partsearchdiv").css("display", "block");
            }

            function SetCleanText(strIn, blnReplaceLineBreaks) {
                if (typeof (strIn) == "undefined") strIn = "";
                strIn = (strIn + "").trim();
                strIn = strIn.replace(/(:PLUS:)/g, "+");
                strIn = strIn.replace(/(:QUOTE:)/g, '"');
                strIn = strIn.replace(/((:AND:)|(&amp;))/g, "&");
                if (blnReplaceLineBreaks) strIn = strIn.replace(/(\n)/g, "<br />");
                return strIn;
            };

            function editRow(rowIndx, grid) {
                grid.addClass({ rowIndx: rowIndx, cls: 'pq-row-edit' });
                grid.refreshRow({ rowIndx: rowIndx });

                grid.editFirstCellInRow({ rowIndx: rowIndx });
            }

            function RemoveOffer(rowIndx, ui) {
                var qrystr = new URLSearchParams(window.location.search);
                var BomManagerID = qrystr.get("BOM");
                var SourceId = ui.rowData.SourceId;
                $.ajax({
                    processData: true,
                    contentType: 'application/json',
                    type: 'POST',
                    url: 'RemoveOfferBOMManager?BOM=' + BomManagerID + '&SourceId=' + SourceId,
                    dataType: "json",
                    success: function (data) {
                        var autoSourceGrid = $('#grid_AutoSource').pqGrid('instance');
                        var VendorType = autoSourceGrid.getRowData({ rowIndx: rowIndx }).VendorCategory;
                        if (VendorType == 'Offers')
                            LoadEMSOffers($('#hdnBomPartNumber').val(), true, 0);
                        else if (VendorType == 'XMatch / Sourcing')
                            LoadXMatchSourcingData($('#hdnBomPartId').val(), true, 0);
                        else
                            LoadAPIOffersData($('#hdnBomPartId').val(), true, 0);

                        editsource = true;
                        LoadAutoSource($('#hdnBomPartId').val(), true);
                        editsource = false;
                        GetBOMManagerStatus(BomManagerID);

                        $('#grid_md').pqGrid('refreshDataAndView');
                    },
                    error: function (err) {}
                });
            }
            $('.ConfirmYes').click(function () {
                $('#divAutoSource').css("display", 'block');
                $('#refreshAutoSource').css("display", 'block');
                $('#headingThree').removeClass('headingOneCorner');
                $('#ShowAutoSourcediv').css("background-image", "url(/Areas/BOM/Images/hide.gif)");

                var BomManagerID = $("#HDBOMManagaerID").val();
                var CustomerRequirementId = $("#HDCustomerRequirementID").val();
                var sourcetype = $("#HDSourcetype").val();
                replaceByEMSSourcing(BomManagerID, CustomerRequirementId, sourcetype);
                ResetConfirmationModal("false");
            });
            $('.ConfirmNo').click(function () {
                var modal = document.getElementById("ConfirmationBoxModal");
                modal.style.display = "none"
                $('html, body', window.parent.document).animate({ scrollTop: 240 }, "fast");
            });

            function SelectEMSSource(rowIndx, grid) {
                grid.addClass({ rowIndx: rowIndx, cls: 'pq-row-delete' });
                var qrystr = new URLSearchParams(window.location.search);
                var BomManagerID = qrystr.get("BOM");
                ReplaceConfirmation(BomManagerID, grid.getRecId({ rowIndx: rowIndx }), 1);
            }

            function replaceByEMSSourcing(BOM, CustomerRequirementId, source) {
                $.ajax({
                    processData: true,
                    contentType: 'application/json',
                    type: 'POST',
                    url: 'ReplaceSourcingBOMManager?BOM=' + BOM + '&CustomerRequirementId=' + CustomerRequirementId + '&ReplaceSourceType=' + source,
                    dataType: "json",

                    success: function (data) {
                        editsource = true;
                        LoadAutoSource($('#hdnBomPartId').val(), true);
                        editsource = false;
                        GetBOMManagerStatus(BOM);
                    },
                    error: function (err) {}
                });
            }

            function SelectAPIOffer(rowIndx, grid) {
                grid.addClass({ rowIndx: rowIndx, cls: 'pq-row-delete' });
                var qrystr = new URLSearchParams(window.location.search);
                var BomManagerID = qrystr.get("BOM");
                ReplaceConfirmation(BomManagerID, grid.getRecId({ rowIndx: rowIndx }), 2);
            }

            function AddNewOffer(rowIndx, grid, offertype, ui, APIOfferSource) {
                $('#divAutoSource').css("display", 'block');
                $('#refreshAutoSource').css("display", 'block');
                $('#headingThree').removeClass('headingOneCorner');
                $('#ShowAutoSourcediv').css("background-image", "url(/Areas/BOM/Images/hide.gif)");
                var qrystr = new URLSearchParams(window.location.search);
                var BomManagerID = qrystr.get("BOM");
                var offerSource = APIOfferSource ? APIOfferSource : "";
                var partBOMGrid = $('#grid_md').pqGrid('getInstance').grid;
                var cusReqId = $('#hdnBomPartId').val();

                //Case when not select any item in BOM Line Grid
                if (isNaN(cusReqId) || Number(cusReqId) == 0) {
                    alert("Please select BOM item to add offer.");
                    return;
                }

                $.ajax({
                    processData: true,
                    contentType: 'application/json',
                    type: 'POST',
                    url: 'AddNewOffer?BOM=' + BomManagerID + '&sourceId=' + grid.getRecId({ rowIndx: rowIndx }) + '&CustomerRequirementId=' + cusReqId + '&ReplaceSourceType=' + offertype + '&OfferSource=' + offerSource,
                    dataType: "json",
                    success: function (data) {
                        editsource = true;
                        if (partBOMGrid.SelectRow().getSelection().length > 0) {
                            RowIndexPartBOMGrid = partBOMGrid.SelectRow().getSelection()[0].rowIndx;;
                        }
                        else {
                            $('#grid_AutoSource').pqGrid('refreshDataAndView');
                        }
                        $('#grid_md').pqGrid('refreshDataAndView');
                        editsource = false;
                        GetBOMManagerStatus(BomManagerID);
                        grid.getRow({ rowIndx: rowIndx }).find("button").addClass('button_disabled');
                        grid.getRow({ rowIndx: rowIndx }).find("button").text('Added');
                    },
                    error: function (err) {}
                });
            }

            function SelectXmatch(rowIndx, grid) {
                grid.addClass({ rowIndx: rowIndx, cls: 'pq-row-delete' });
                var qrystr = new URLSearchParams(window.location.search);
                var BomManagerID = qrystr.get("BOM");
                ReplaceConfirmation(BomManagerID, grid.getRecId({ rowIndx: rowIndx }), 3);
            }

            function ValidateAddOfferForm() {
                var formIsValid = true;

                //validate supplier
                if ($("#hdsupplier").val() == "" || $("#LabelSupplier").text() == "") {
                    $("#ValidationErrorSupplier").css("display", "block");
                    $("#RowAddOfferSupplier").css("background-color", "#990000");
                    $("#RowAddOfferSupplierError").css("display", "block");
                    $("#RowAddOfferSupplierError").css("background-color", "#990000");
                    formIsValid = false;
                }
                else {
                    $("#ValidationErrorSupplier").css("display", "none");
                    $("#RowAddOfferSupplier").css("background-color", "#56954E");
                    $("#RowAddOfferSupplierError").css("display", "none");
                }

                //validate part no
                if ($("#hdpartid").val() == "" || $("#LabelPartNo").text() == "") {
                    $("#ValidationErrorPartNo").css("display", "block");
                    $("#RowAddOfferPartNo").css("background-color", "#990000");
                    $("#RowAddOfferPartNoError").css("display", "block");
                    $("#RowAddOfferPartNoError").css("background-color", "#990000");
                    formIsValid = false;
                }
                else {
                    $("#ValidationErrorPartNo").css("display", "none");
                    $("#RowAddOfferPartNo").css("background-color", "#56954E");
                    $("#RowAddOfferPartNoError").css("display", "none");
                }

                //validate quantity
                if ($("#textQuantity").val() == "") {
                    $("#ValidationErrorQuantity").css("display", "block");
                    $("#RowAddOfferQuantity").css("background-color", "#990000");
                    $("#RowAddOfferQuantityError").css("display", "block");
                    $("#RowAddOfferQuantityError").css("background-color", "#990000");
                    formIsValid = false;
                }
                else {
                    $("#ValidationErrorQuantity").css("display", "none");
                    $("#RowAddOfferQuantity").css("background-color", "#56954E");
                    $("#RowAddOfferQuantityError").css("display", "none");
                }

                //validate buy price
                var buyPrice = parseFloat($("#textPrice").val());
                if (!buyPrice) {
                    $("#ValidationErrorPrice").css("display", "block");
                    $("#RowAddOfferPrice").css("background-color", "#990000");
                    $("#RowAddOfferPriceError").css("display", "block");
                    $("#RowAddOfferPriceError").css("background-color", "#990000");
                    formIsValid = false;
                }
                else {
                    $("#ValidationErrorPrice").css("display", "none");
                    $("#RowAddOfferPrice").css("background-color", "#56954E");
                    $("#RowAddOfferPriceError").css("display", "none");
                }

                //validate sell price
                var sellPrice = parseFloat($("#textSellPrice").val());
                if (!sellPrice) {
                    $("#RowAddOfferSellPrice").css("background-color", "#990000");
                    $("#RowAddOfferSellPriceError").css("display", "block");
                    formIsValid = false;
                }
                else {
                    $("#RowAddOfferSellPrice").css("background-color", "#56954E");
                    $("#RowAddOfferSellPriceError").css("display", "none");
                }

                //validate reason sell price less then buy price
                if (sellPrice && buyPrice) {
                    if (sellPrice < buyPrice && $("#textUplipSellPriceLessReason").val().trim() == "") {
                        $("#RowUplipSellPriceLessReason").css("background-color", "#990000");
                        $("#RowUplipSellPriceLessReasonError").css("display", "block");
                        formIsValid = false;
                    }
                    else {
                        $("#RowUplipSellPriceLessReason").css("background-color", "#56954E");
                        $("#RowUplipSellPriceLessReasonError").css("display", "none");
                    }
                }
                else {
                    $("#RowUplipSellPriceLess").css("display", "none");
                    $("#RowUplipSellPriceLessReason").css("display", "none");
                    $("#RowUplipSellPriceLessReasonError").css("display", "none");
                }

                //validate currency
                if ($("#ddlCurrency").val() <= 0) {
                    $("#ValidationErrorCurrency").css("display", "block");
                    $("#ValidationErrorCurrency").css("background-color", "#990000");
                    $("#RowAddOfferCurrency").css("background-color", "#990000");
                    $("#RowAddOfferCurrencyError").css("display", "block");
                    $("#RowAddOfferCurrencyError").css("background-color", "#990000");
                    formIsValid = false;
                }
                else {
                    $("#ValidationErrorCurrency").css("display", "none");
                    $("#RowAddOfferCurrency").css("background-color", "#56954E");
                    $("#RowAddOfferCurrencyError").css("display", "none");
                }

                //validate manufacturer
                if ($("#hdmrfid").val() == "" || $("#LabelManufacturer").text() == "") {
                    $("#ValidationErrorManufacturer").css("display", "block");
                    $("#RowAddOfferManufacturer").css("background-color", "#990000");
                    $("#RowAddOfferManufacturerError").css("display", "block");
                    $("#RowAddOfferManufacturerError").css("background-color", "#990000");
                    formIsValid = false;
                }
                else {
                    $("#ValidationErrorManufacturer").css("display", "none");
                    $("#RowAddOfferManufacturer").css("background-color", "#56954E");
                    $("#RowAddOfferManufacturerError").css("display", "none");
                }

                //validate product
                if ($("#prodid").val() == "" || $("#LabelProduct").text() == "") {
                    $("#ValidationErrorProduct").css("display", "block");
                    $("#RowAddOfferProduct").css("background-color", "#990000");
                    $("#RowAddOfferProductError").css("display", "block");
                    $("#RowAddOfferProductError").css("background-color", "#990000");
                    formIsValid = false;
                }
                else {
                    $("#ValidationErrorProduct").css("display", "none");
                    $("#RowAddOfferProduct").css("background-color", "#56954E");
                    $("#RowAddOfferProductError").css("display", "none");
                }

                return formIsValid;
            }

            function ReplaceConfirmation(BOMManagerID, CustomerReqID, SourceType) {
                $("#partNoConfirmation").val(CustomerReqID);
                $("#HDBOMManagaerID").val(BOMManagerID);
                $("#HDCustomerRequirementID").val(CustomerReqID);
                $("#HDSourcetype").val(SourceType);
                var modal = document.getElementById("ConfirmationBoxModal");
                modal.style.display = "block"
                $('html, body', window.parent.document).animate({ scrollTop: 240 }, "fast");

            }
            function ResetConfirmationModal(confirmation) {
                $("partNoConfirmation").text("");
                $("#ConfirmationValue").val(confirmation);
                var modal = document.getElementById("ConfirmationBoxModal");
                modal.style.display = "none"
                $('html, body', window.parent.document).animate({ scrollTop: 240 }, "fast");
            }
            function UpdateXMatchData(rowIndx, grid) {
                LoadUserXmatchData(grid.getRecId({ rowIndx: rowIndx }));
                var modal = document.getElementById("XMatchSourcingModal");
                modal.style.display = "block"
                $('html, body', window.parent.document).animate({ scrollTop: 240 }, "fast");
                $('#XMSalesXMatchID').val(grid.getRecId({ rowIndx: rowIndx }));
            }
            function LoadUserXmatchData(SalesXMatchID) {
                var qrystr = new URLSearchParams(window.location.search);
                var BomManagerID = qrystr.get("BOM");
                $.ajax({
                    processData: true,
                    contentType: 'application/json',
                    type: 'POST',
                    url: 'GetUserXMatchData?BOMManagerID=' + BomManagerID + '&SalesXMatchID=' + SalesXMatchID,
                    dataType: "json",

                    success: function (data) {
                        if (data[0].SupplierMOQ != "" || data[0].SupplierMOQ != null)
                            $('#XMMOQ').val(data[0].SupplierMOQ);
                        else
                            $('#XMMOQ').val("0");

                        if (data[0].SPQ != "" || data[0].SPQ != null)
                            $('#XMSPQ').val(data[0].SPQ);
                        else
                            $('#XMSPQ').val("0");

                        $('#XMStockQTY').val('0');
                        $('#XMMDC').val(data[0].DateCode);
                        $('#XMLT').val(data[0].LeadTime);
                    },
                    error: function (err) {}
                });
            }

            function ResetXmatchUserForm() {
                var modal = document.getElementById("XMatchSourcingModal");
                modal.style.display = "none"
                $('html, body', window.parent.document).animate({ scrollTop: 240 }, "fast");
                $('#XMStockQTY').val('');
                $('#XMMDC').val('');
                $('#XMLT').val('');
                $('#XMSPQ').val('');
                $('#XMMOQ').val('');
                $('#XMSalesXMatchID').val('');
                $('#XMSPQError').css("display", "none");
                $('#XMSPQRow').css("background-color", "#56954E");
            }
            $('#btnSendBOM').click(function () {
                LoadSendBOMToBuyerModal();
            });
            $('#btnRelease').click(function () {
                $('#SenBOMTitle').text("Release BOM Items");
                $('#ActionText').text('Are you sure would like to Release this BOM items to Salesperson ?');
                $("#lblResult").html("Sourcing Results")
                $('#hdnReqType').val('1');
                LoadSendBOMToBuyerModal(1);
            });

            $('#btnCopyToClipboard').click(function () {
                $('#SenBOMTitle').text("Copy to Clipboard");
                $('#ActionText').text('Would you like to Copy this BOM items to Clipboard ?');
                $("#lblResult").html("Results")
                $('#hdnReqType').val('5');
                LoadSendBOMToBuyerModal(5);
            });

            $('#btnRecall').click(function () {
                $('#SenBOMTitle').text("Recall BOM Items");
                $('#ActionText').text('Are you sure would like to Recall this BOM items ?');
                $("#lblResult").html("Sourcing Results")
                $('#hdnReqType').val('2');
                LoadSendBOMToBuyerModal(2);
            });
            $('#btnNoBid').click(function () {
                $('#SenBOMTitle').text("No-Bid BOM Items");
                $('#ActionText').text('Are you sure would like to No-Bid this BOM items to Salesperson ?');
                $("#lblResult").html("Sourcing Results")
                $('#hdnReqType').val('3');
                LoadSendBOMToBuyerModal(3);
            });
            $('#btnNoBidRecall').click(function () {
                $('#SenBOMTitle').text("Recall No-Bid BOM Items");
                $('#ActionText').text('Are you sure would like to Recall No-Bid this BOM items ?');
                $("#lblResult").html("Sourcing Results")
                $('#hdnReqType').val('4');
                LoadSendBOMToBuyerModal(4);
            });
            $('#btnRefreshSourcing').click(function () {
                RefreshAutoSourcing();
            });

            $('.CancelSendBOM').click(function () {
                $('#grid_SB').pqGrid('option', 'dataModel.data', '');
                $('#grid_SB').pqGrid('refreshView');
                $('#hdnReqType').val('0');
                var modal = document.getElementById("SendBOMModal");
                modal.style.display = "none"
                $('html, body', window.parent.document).animate({ scrollTop: 240 }, "fast");
            });

            $('.ConfirmSendBOM').click(function () {
                var selectedCustomerRequirementIds = '';
                var selectedAutoSourceIds = '';
                var reasons = [];
                var grid = $('#grid_SB').pqGrid('instance');
                if (grid.Checkbox('checkedColumn').getCheckedNodes().length > 0) {
                    var checked = grid.Checkbox('checkedColumn').getCheckedNodes().map(function (rd) {
                        selectedCustomerRequirementIds = selectedCustomerRequirementIds + rd.CustomerRequirementNo + '|';
                        selectedAutoSourceIds = selectedAutoSourceIds + rd.SourceId + '|';

                        if (rd.Price <= rd.OriginalPrice && rd.SourceId > 0) {
                            var reasonObj = {
                                Reason: rd.Reason,
                                SourceId: rd.SourceId
                            };
                            reasons.push(reasonObj);
                        }
                    });
                }

                if (selectedCustomerRequirementIds == "") {
                    alert("Please Select Items to Proceed");
                    return;
                }

                var callType = $('#hdnReqType').val();
                if (callType == '1') {
                    if (!confirm("Confirm Selected BOM release to Salesperson")) {
                        return;
                    }
                }
                if (callType == '2') {
                    if (!confirm("Are you sure you want to Recall these Items")) {
                        return;
                    }
                }
                if (callType == '3') {
                    if (!confirm("Are you sure you want to No-Bid these items")) {
                        return;
                    }
                } if (callType == '4') {
                    if (!confirm("Are you sure you want to Recall these No-Bid items")) {
                        return;
                    }
                }
                if (callType == '5') {
                    var selectedRows = grid.SelectRow().getSelection();
                    var strClipboard = "Line#	Part No	Manufacturer	Quantity	Target Price\r\n"
                    strClipboard += selectedRows.map(x => [x.rowData.Line, x.rowData.Part, x.rowData.ManufacturerName, x.rowData.Quantity, parseFloat(x.rowData["Price"]).toFixed(4) + ' ' + x.rowData.BOMManagerCurrencyCode].join("	")
                    ).join("\r\n")
                    navigator.clipboard.writeText(strClipboard);
                    alert("BOM items copied to Clipboard successfully.")
                    var modal = document.getElementById("SendBOMModal");
                    modal.style.display = "none"
                    $('html, body', window.parent.document).animate({ scrollTop: 240 }, "fast")
                    return
                }
                //******** get data of release note and create object for passing data ************//
                var selectedRows = grid.SelectRow().getSelection();
                var lstReleaseNotes = [];
                var lstRequirementIDSelected = [];
                var validateLength = true;

                selectedRows.forEach(x => {
                    lstRequirementIDSelected.push(x.rowData.CustomerRequirementNo);
                });

                for (var i = 0; i < grid.getTotalRows(); i++) {
                    var rowData = grid.getRowData({ rowIndx: i })
                    if (rowData.HeaderFlag && lstRequirementIDSelected.includes(rowData.CustomerRequirementNo)) {
                        const note = rowData.ReleaseNote
                        if (note?.length > 500) validateLength = false
                        else if (note?.length > 0) {
                            var noteObject = {
                                ReleaseNote: rowData.ReleaseNote,
                                CustomerRequirementID: rowData.CustomerRequirementNo
                            }
                            lstReleaseNotes.push(noteObject)
                        }
                    }
                }

                if (!validateLength) {
                    alert("One of the Release notes exceeds 500 character limit.");
                    return;
                }

                if (lstRequirementIDSelected.length > 0) {
                    if (reasons.some(r => !r.Reason)) {
                        alert("Reason For Loss has not been completed for all lines in selected release. Please complete reason fields or correct for profit and retry release.")
                        return;
                    }
                }

                var qrystr = new URLSearchParams(window.location.search);
                var BomManagerID = qrystr.get("BOM");
                var strRequestToPOHubBy = $("#RequestToPOHubBy").val();
                var strSupportTeamMemberNo = $("#SupportTeamMemberNo").val();
                var strUpdatedBy = $("#UpdatedBy").val();
                var strBOMManagerCode = $("#BOMManagerCode").val();
                var strBOMManagerName = $("#BOMManagerName").val();
                var strBOMManagerCompanyNo = $("#BOMManagerCompanyNo").val();
                var strBOMManagerCompanyName = $("#BOMManagerCompanyName").val();
                var reqType = $('#hdnReqType').val();
                var postData = {
                    lstReleaseNotes: lstReleaseNotes,
                    reasons: reasons.filter(r => r.Reason)
                };
                $.ajax({
                    processData: true,
                    contentType: 'application/json',
                    type: 'POST',
                    url: 'BOMReleaseRequirement?BOMManagerID=' + BomManagerID + '&RequestToPOHubBy=' + strRequestToPOHubBy + '&SupportTeamMemberNo=' + strSupportTeamMemberNo + '&UpdatedBy=' + strUpdatedBy + '&BOMManagerCode=' + strBOMManagerCode + '&BOMManagerName=' + strBOMManagerName + '&strBOMManagerCompanyNo=' + strBOMManagerCompanyNo + '&strBOMManagerCompanyName=' + strBOMManagerCompanyName +
                        '&Cids=' + selectedCustomerRequirementIds + '&ReqType=' + reqType + '&NoBidNotes=' + reqType + '&ASId=' + selectedAutoSourceIds,
                    dataType: "json",
                    data: JSON.stringify(postData),
                    success: function (data) {

                        $('#grid_SB').pqGrid('option', 'dataModel.data', '');
                        $('#grid_SB').pqGrid('refreshView');
                        $('#hdnReqType').val('0');
                        if (callType == '1') {
                            if (!confirm("BOM items were Released to Salesperson ")) {
                            }
                        }
                        if (callType == '2') {
                            if (!confirm("BOM items recalled successfully")) {
                            }
                        }
                        if (callType == '3') {
                            if (!confirm("BOM items No-Bid Successful")) {
                            }
                        } if (callType == '4') {
                            if (!confirm("BOM No-Bid items recalled successfully")) {
                            }
                        }

                        location.reload(true);
                    },
                    error: function (err) {}
                });
                var modal = document.getElementById("SendBOMModal");
                modal.style.display = "none"
                $('html, body', window.parent.document).animate({ scrollTop: 240 }, "fast");
            });

            function LoadSendBomDataModal(reqStatus) {
                LoadSendBOMData(reqStatus);
                $('#BOMPartsNotSourced').css("display", "none");
                $('#BOMPartsSourced').css("display", "block");
                $('#BackSendBOM1').css("display", "none");
                $('#BackSendBOM2').css("display", "none");
                var modal = document.getElementById("SendBOMModal");
                modal.style.display = "block"
                $('html, body', window.parent.document).animate({ scrollTop: 240 }, "fast");
            }

            function LoadSendBOMToBuyerModal(reqStatus) {
                if (reqStatus == '5') {
                    LoadSendBomDataModal(reqStatus)
                    return
                }

                CheckMissingFields(reqStatus);
            }

            function RefreshAutoSourcing() {
                editsource = true;
                LoadAutoSource($('#hdnBomPartId').val(), true);
                editsource = false;
            }

            function preventKeyboardInput(event) {
                event.preventDefault();
            }

            function limitCharactersOnNumberField(event) {
                !/(^\d*\.?\d*$)|(Backspace|Control|Meta|Left|Right|Shift|Tab)/.test(event.key) && event.preventDefault();
            }

            $('#textPrice').blur(function () {
                displayReasonSellPriceLessInAddForm();
            })

            $('#textSellPrice').blur(function () {
                displayReasonSellPriceLessInAddForm();
            })

            function displayReasonSellPriceLessInAddForm() {
                var buyPrice = parseFloat($('#textPrice').val());
                var sellPrice = parseFloat($('#textSellPrice').val());

                if (isNaN(buyPrice) || isNaN(sellPrice)) return;

                //show reason field
                if (sellPrice < buyPrice) {
                    $("#RowUplipSellPriceLess").css("display", "block");
                    $("#RowUplipSellPriceLessReason").css("display", "block");
                } else {
                    //clear reason field and hide
                    $('#textUplipSellPriceLessReason').val('');
                    $("#RowUplipSellPriceLess").css("display", "none");
                    $("#RowUplipSellPriceLessReason").css("display", "none");
                    $("#RowUplipSellPriceLessReasonError").css("display", "none");
                }
            };

            $("#refreshBOMItem").click(function () {
                var gridInstance = $('#grid_md').pqGrid('getInstance');
                if (gridInstance.grid.SelectRow().getSelection().length > 0) {
                    RowIndexPartBOMGrid = gridInstance.grid.SelectRow().getSelection()[0].rowIndx;
                }
                $('#grid_md').pqGrid('refreshDataAndView');
            });
            $("#refreshAutoSource").click(function () {
                $('#grid_AutoSource').pqGrid('refreshDataAndView');
            });

            function BOMItemGridHeightChange(recordsLeft, rpp) {
                var height;

                if (recordsLeft < 0) {
                    recordsLeft = rpp + recordsLeft;
                    height = recordsLeft * 35 + 40;
                }
                else {
                    height = rpp * 35 + 40;
                }
                $('#grid_md .pq-grid-center-o').css('height', height + 7);
                $('#grid_md .pq-grid-center').css('height', height + 7);
                $('#grid_md .pq-body-outer').css('height', height + 7);
                $('#grid_md').pqGrid('option', 'height', height + 50).pqGrid('refresh');
            }

            function NotesItemGridHeightChange(recordsLeft, rpp) {
                var height;

                if (recordsLeft < 0) {
                    recordsLeft = rpp + recordsLeft;
                    height = recordsLeft * 35 + 40;
                }
                else {
                    height = rpp * 35 + 40;
                }
                $('#grid_md_notes .pq-grid-center-o').css('height', height + 7);
                $('#grid_md_notes .pq-grid-center').css('height', height + 7);
                $('#grid_md_notes .pq-body-outer').css('height', height + 7);
                $('#grid_md_notes').pqGrid('option', 'height', height + 50).pqGrid('refresh');
            }

            function AutoSourceGridHeightChange(recordsLeft, rpp) {
                var height;

                if (recordsLeft < 0) {
                    recordsLeft = rpp + recordsLeft;
                    height = recordsLeft * 35 + 40;
                }
                else {
                    height = rpp * 35 + 40;
                }

                $('#grid_AutoSource .pq-grid-center-o').css('height', height + 15);
                $('#grid_AutoSource .pq-grid-center').css('height', height + 9);
                $('#grid_AutoSource .pq-body-outer').css('height', height + 7);
                $('#grid_AutoSource .pq-body-outer').css('padding-bottom', '8px');
                $('#grid_AutoSource').pqGrid('option', 'height', height + 60).pqGrid('refresh');
            }

            function XMatchSourcingGridHeightChange(recordsLeft, rpp) {
                var height;

                if (recordsLeft < 0) {
                    recordsLeft = rpp + recordsLeft;
                    height = recordsLeft * 35 + 40;
                }
                else {
                    height = rpp * 35 + 40;
                }

                $('#grid_xmSourcing .pq-grid-center-o').css('height', height + 16);
                $('#grid_xmSourcing .pq-grid-center').css('height', height + 9);
                $('#grid_xmSourcing .pq-body-outer').css('height', height + 7);
                $('#grid_xmSourcing .pq-body-outer').css('padding-bottom', '8px');
                $('#grid_xmSourcing').pqGrid('option', 'height', height + 60).pqGrid('refresh');
            }

            function EMSOfferGridHeightChange(recordsLeft, rpp) {
                var height;

                if (recordsLeft < 0) {
                    recordsLeft = rpp + recordsLeft;
                    height = recordsLeft * 35 + 40;
                }
                else {
                    height = rpp * 35 + 40;
                }

                $('#grid_EmsOffer .pq-grid-center-o').css('height', height + 16);
                $('#grid_EmsOffer .pq-grid-center').css('height', height + 9);
                $('#grid_EmsOffer .pq-body-outer').css('height', height - 1);
                $('#grid_EmsOffer .pq-body-outer').css('padding-bottom', '8px');
                $('#grid_EmsOffer').pqGrid('option', 'height', height + 60).pqGrid('refresh');
            }

            function APIOfferGridHeightChange(recordsLeft, rpp) {
                var height;
                if (recordsLeft < 0) {
                    recordsLeft = rpp + recordsLeft;
                    height = recordsLeft * 35 + 40;
                }
                else {
                    height = rpp * 35 + 40;
                }
                $('#grid_API .pq-grid-center-o').css('height', height + 16);
                $('#grid_API .pq-grid-center').css('height', height + 9);
                $('#grid_API .pq-body-outer').css('height', height + 7);
                $('#grid_API .pq-body-outer').css('padding-bottom', '8px');
                $('#grid_API').pqGrid('option', 'height', height + 60).pqGrid('refresh');
            }

            function LyticaAPIGridHeightChange(recordsLeft, rpp) {
                var height;
                if (recordsLeft < 0) {
                    recordsLeft = rpp + recordsLeft;
                    height = recordsLeft * 35 + 40;
                }
                else {
                    height = rpp * 35 + 40;
                }
                $('#grid_Lytica_API .pq-grid-center-o').css('height', height + 16);
                $('#grid_Lytica_API .pq-grid-center').css('height', height + 9);
                $('#grid_Lytica_API .pq-body-outer').css('height', height + 7);
                $('#grid_Lytica_API .pq-body-outer').css('padding-bottom', '8px');
                $('#grid_Lytica_API').pqGrid('option', 'height', height + 60).pqGrid('refresh');
            }

            function BindAPIGridEvents() {
                $("#grid_API .pq-page-next").click(function () {
                    var gridInstanceRPP = $('#grid_API').pqGrid('instance');
                    var rpp = gridInstanceRPP.options.pageModel.rPP;
                    var totalRecords = gridInstanceRPP.options.pageModel.totalRecords;
                    var curPage = gridInstanceRPP.options.pageModel.curPage;
                    var recordsLeft = totalRecords - (rpp * curPage);
                    APIOfferGridHeightChange(recordsLeft, rpp);

                });
                $("#grid_API .pq-page-prev").click(function () {
                    var gridInstanceRPP = $('#grid_API').pqGrid('instance');
                    var rpp = gridInstanceRPP.options.pageModel.rPP;
                    var totalRecords = gridInstanceRPP.options.pageModel.totalRecords;
                    var curPage = gridInstanceRPP.options.pageModel.curPage;
                    var recordsLeft = totalRecords - (rpp * curPage);
                    APIOfferGridHeightChange(recordsLeft, rpp);
                });
                $("#grid_API .pq-page-first").click(function () {
                    var gridInstanceRPP = $('#grid_API').pqGrid('instance');
                    var rpp = gridInstanceRPP.options.pageModel.rPP;
                    var totalRecords = gridInstanceRPP.options.pageModel.totalRecords;
                    var curPage = 1;
                    var recordsLeft = totalRecords - (rpp * curPage);
                    APIOfferGridHeightChange(recordsLeft, rpp);
                });
                $("#grid_API .pq-page-last").click(function () {
                    var gridInstanceRPP = $('#grid_API').pqGrid('instance');
                    var rpp = gridInstanceRPP.options.pageModel.rPP;
                    var totalRecords = gridInstanceRPP.options.pageModel.totalRecords;
                    var curPage = gridInstanceRPP.options.pageModel.totalPages;
                    var recordsLeft = totalRecords - (rpp * curPage);
                    APIOfferGridHeightChange(recordsLeft, rpp);
                });
                $("#grid_API .pq-page-select").change(function () {

                    var gridInstanceRPP = $('#grid_API').pqGrid('instance');
                    var rpp = gridInstanceRPP.options.pageModel.rPP;
                    var totalRecords = gridInstanceRPP.options.pageModel.totalRecords;
                    var curPage = gridInstanceRPP.options.pageModel.curPage;
                    var recordsLeft = totalRecords - (rpp * curPage);
                    APIOfferGridHeightChange(recordsLeft, rpp);
                });
                $("#grid_API .pq-page-current").change(function () {

                    var gridInstanceRPP = $('#grid_API').pqGrid('instance');
                    var rpp = gridInstanceRPP.options.pageModel.rPP;
                    var totalRecords = gridInstanceRPP.options.pageModel.totalRecords;
                    var curPage = $('#grid_md .pq-page-current').val();
                    var recordsLeft = totalRecords - (rpp * curPage);
                    APIOfferGridHeightChange(recordsLeft, rpp);
                });
            }

            $("#EditBOMItem").click(function () {
                LoadEditHubBOMData();
                var modal = document.getElementById("EditBOMItemModal");
                modal.style.display = "block";
                $('html, body', window.parent.document).animate({ scrollTop: 240 }, "fast");
            });

            function LoadEditHubBOMData() {
                $.ajax({
                    processData: true,
                    contentType: 'application/json',
                    type: 'POST',
                    url: 'LoadEditBOMData?CustReqID=' + $("#HDEditCustomerRequirementID").val(),
                    dataType: "json",
                    success: function (data) {
                        $("#EditBOMValidationError").css("display", "none");
                        HubResetEditBOMForm();
                        $("#LabelEditBOMPartNo").text(data.Part);
                        if (data.ManufacturerNo != null && data.ManufacturerNo != 0) {
                            $("#LabelEditBOMManufacturer").text(data.ManufacturerName);
                            $("#HDEditBOMManufacturerID").val(data.ManufacturerNo);
                            $("#TextEditBOMManufacturer").css("display", "none");
                            $("#RemoveEditBOMManufacturer").css("display", "block");
                        }
                        else {
                            $("#TextEditBOMManufacturer").css("display", "block");
                            $("#TextEditBOMManufacturer").val(null);
                            $("#HDEditBOMManufacturerID").val(null);
                            $("#LabelEditBOMManufacturer").text(null);
                            $("#RemoveEditBOMManufacturer").css("display", "none");
                        }
                        if (data.ProductNo != null && data.ProductNo != 0) {
                            $("#LabelEditBOMProduct").text(data.ProductDescription);
                            $("#HDEditBOMProductID").val(data.ProductNo);
                            $("#TextEditBOMProduct").css("display", "none");
                            $("#RemoveEditBOMProduct").css("display", "block");
                        }
                        else {
                            $("#TextEditBOMProduct").css("display", "block");
                            $("#TextEditBOMProduct").val(null);
                            $("#HDEditBOMProductID").val(null);
                            $("#LabelEditBOMProduct").text(null);
                            $("#RemoveEditBOMProduct").css("display", "none");
                        }
                    },
                    error: function (err) {}
                });
            }

            $("#TextEditBOMManufacturer").autocomplete({
                minLength: 2,
                source: function (request, response) {
                    $.ajax({
                        processData: true,
                        contentType: 'application/json',
                        type: 'GET',
                        url: handlerUrl + '?action=GetManufacturer&MrfSearch=' + $("#TextEditBOMManufacturer").val(),
                        dataType: "json",

                        success: function (data) {
                            autocompleteCount = data.length;
                            response(data);
                        },
                        error: function (err) {
                        }
                    });
                },
                open: function (event, ui) {
                    $("ul").prepend('<li class ="AutoCompleteHeader""><div>' + autocompleteCount + ' result(s)</div></li>');
                    autocompleteCount = 0;
                },
                select: function (event, ui) {
                    $("#TextEditBOMManufacturer").css("display", "none");
                    $("#HDEditBOMManufacturerID").val(ui.item.value);
                    $("#LabelEditBOMManufacturer").text(SetCleanText(ui.item.label, true));
                    $("#RemoveEditBOMManufacturer").css("display", "block");
                    return false;
                }
            }).autocomplete("instance")._renderItem = function (ul, item) {
                    return $("<li>")
                        .append("<div>" + SetCleanText(item.label, true) + "</div>")
                        .appendTo(ul);
                };

            $("#RemoveEditBOMManufacturer").click(function () {
                $("#TextEditBOMManufacturer").css("display", "block");
                $("#TextEditBOMManufacturer").val(null);
                $("#HDEditBOMManufacturerID").val(null);
                $("#LabelEditBOMManufacturer").text(null);
                $("#RemoveEditBOMManufacturer").css("display", "none");
            });

            $("#TextEditBOMProduct").autocomplete({
                minLength: 2,
                source: function (request, response) {
                    $.ajax({
                        processData: true,
                        contentType: 'application/json',
                        type: 'GET',
                        url: handlerUrl + '?action=GetProduct&ProdSearch=' + $("#TextEditBOMProduct").val(),
                        dataType: "json",
                        success: function (data) {
                            autocompleteCount = data.length;
                            response(data);
                        },
                        error: function (err) {
                        }
                    });
                },
                open: function (event, ui) {
                    $("ul").prepend('<li class ="AutoCompleteHeader""><div>' + autocompleteCount + ' result(s)</div></li>');
                    autocompleteCount = 0;
                },
                select: function (event, ui) {
                    $("#TextEditBOMProduct").css("display", "none");
                    $("#HDEditBOMProductID").val(ui.item.value);
                    $("#LabelEditBOMProduct").text(SetCleanText(ui.item.label, true));
                    $("#RemoveEditBOMProduct").css("display", "block");
                    return false;
                }
            }).autocomplete("instance")._renderItem = function (ul, item) {
                    return $("<li>")
                        .append("<div>" + SetCleanText(item.label, true) + "</div>")
                        .appendTo(ul);
                };

            $("#RemoveEditBOMProduct").click(function () {
                $("#TextEditBOMProduct").css("display", "block");
                $("#TextEditBOMProduct").val(null);
                $("#HDEditBOMProductID").val(null);
                $("#LabelEditBOMProduct").text(null);
                $("#RemoveEditBOMProduct").css("display", "none");
            });

            function HubResetEditBOMForm() {
                $("#RowEditBOMManufacturer").css("background-color", "#56954E");
                $("#RowEditBOMProduct").css("background-color", "#56954E");
            }

            $(".BtnSaveBOMEdit").click(function () {
                if (ValidateEditBOMForm()) {
                    SaveEditBOMItemData();
                }
                else {
                    $("#EditBOMValidationError").css("display", "block");
                }
            });

            function ValidateEditBOMForm() {
                var formIsValid = true;

                if ($('#HDEditBOMManufacturerID').val() == "" || $('#HDEditBOMManufacturerID').val() == null) {
                    $("#RowEditBOMManufacturer").css("background-color", "#990000");
                    formIsValid = false;
                }
                else {
                    $("#RowEditBOMManufacturer").css("background-color", "#56954E");
                    if (formIsValid)
                        formIsValid = true;
                }
                if ($('#HDEditBOMProductID').val() == "" || $('#HDEditBOMProductID').val() == null) {
                    $("#RowEditBOMProduct").css("background-color", "#990000");
                    formIsValid = false;
                }
                else {
                    $("#RowEditBOMProduct").css("background-color", "#56954E");
                    if (formIsValid)
                        formIsValid = true;
                }
                return formIsValid;
            }

            function SaveEditBOMItemData() {
                var qrystr = new URLSearchParams(window.location.search);
                var BOMManagerID = qrystr.get("BOM");
                $.ajax({
                    processData: true,
                    contentType: 'application/json',
                    type: 'POST',
                    url: 'PHSaveEditBOMItemData?&customerRequirementId=' + $('#HDEditCustomerRequirementID').val()
                        + '&manufacturerNo=' + $('#HDEditBOMManufacturerID').val()
                        + '&productNo=' + $('#HDEditBOMProductID').val()
                        + '&BOMManagerID=' + BOMManagerID,
                    dataType: "json",

                    success: function (data) {
                        HubResetEditBOMForm();
                        if (data) {
                            //location.reload();
                            var modal = document.getElementById("EditBOMItemModal");
                            modal.style.display = "none";
                            $('html, body', window.parent.document).animate({ scrollTop: 240 }, "fast");
                            $("#refreshBOMItem").trigger("click");
                        }
                    },
                    error: function (err) {}
                });
            }
            $(".BtnCancelBOMEdit").click(function () {
                var modal = document.getElementById("EditBOMItemModal");
                modal.style.display = "none";
                $('html, body', window.parent.document).animate({ scrollTop: 240 }, "fast");
                ResetEditBOMForm();
            });

            function LoadBOMItemMoreinfo(url) {
                MoreInfoClicked = true;
                MyWindow = window.open(url, 'More Information', 'width = 900, height = 700');
                return false;
            }

            function GetLyticaAPILogCount() {
                var qrystr = new URLSearchParams(window.location.search);
                var BOMManagerID = qrystr.get("BOM");
                $.ajax({
                    processData: true,
                    contentType: 'application/json',
                    type: 'POST',
                    url: 'GetLyticaAPILogCount?BOMManagerID=' + BOMManagerID,
                    dataType: "json",
                    success: function (data) {
                        LyticaAPILogCount = data;
                        $('#Lyticalogcount').text(LyticaAPILogCount);
                    },
                    error: function (err) {}
                });
            }
            $("#RemoveEditManufacturer").click(function () {
                $("#txtEditMfr").css("display", "block");
                $("#txtEditMfr").val(null);
                $("#hdEditmrfid").val(null);
                $("#LabelEditManufacturer").text(null);
                $("#RemoveEditManufacturer").css("display", "none");
            });

            $("#txtEditMfr").autocomplete({
                minLength: 2,
                source: function (request, response) {
                    // Fetch data
                    $.ajax({
                        processData: true,
                        contentType: 'application/json',
                        type: 'GET',
                        url: handlerUrl + '?action=GetManufacturer&MrfSearch=' + $("#txtEditMfr").val(),
                        dataType: "json",

                        success: function (data) {
                            autocompleteCount = data.length;
                            response(data);


                        },
                        error: function (err) {
                            /* alert(err);*/

                        }
                    });
                },
                open: function (event, ui) {
                    $("ul").prepend('<li class ="AutoCompleteHeader""><div>' + autocompleteCount + ' result(s)</div></li>');
                    autocompleteCount = 0;
                },
                select: function (event, ui) {
                    $("#txtEditMfr").css("display", "none");
                    $("#hdEditmrfid").val(ui.item.value);
                    $("#LabelEditManufacturer").text(SetCleanText(ui.item.label, true));
                    $("#RemoveEditManufacturer").css("display", "block");
                    return false;
                }
            }).autocomplete("instance")._renderItem = function (ul, item) {
                    return $("<li>")
                        .append("<div>" + SetCleanText(item.label, true) + "</div>")
                        .appendTo(ul);
                };

            $('#btnAddCommunicationNote').click(function () {
                $('.BtnSaveCommunicationNode').removeClass("save_comm_note_disabled");
                $('.BtnSaveCommunicationNode').addClass("save_comm_note");

                var modal = document.getElementById("AddCommunicationNoteModal");
                modal.style.display = "block"
                $('html, body', window.parent.document).animate({ scrollTop: 240 }, "fast");
            });

            $('.SaveCommunicationNote').click(function () {
                if (ValidateCommunicationNodeForm()) {
                    $('.BtnSaveCommunicationNode').off('click');
                    $('.BtnSaveCommunicationNode').removeClass("save_comm_note");
                    $('.BtnSaveCommunicationNode').addClass("save_comm_note_disabled");
                    $('#CommunicationNoteValidationError').css("display", "none");

                    event.preventDefault();
                    SaveCommunicationNodeData();
                }
                else {
                    $("#CommunicationNoteValidationError").css("display", "block");
                }
            });

            $('.CancelCommunicationNote').click(function () {
                CancelCommunicationNode();
            });

            function CancelCommunicationNode() {
                var modal = document.getElementById("AddCommunicationNoteModal");
                modal.style.display = "none";
                $('html, body', window.parent.document).animate({ scrollTop: 240 }, "fast");
                ResetCommunicationNodeForm();
            }

            function LoadGroupCommunicationNotes() {
                ResetCommunicationNodeForm();
                $.ajax({
                    type: "POST", url: "GetGroupForCommunicationNotes",
                    dataType: "json",
                    contentType: "application/json",
                    success: function (Jsondata) {
                        $.each(Jsondata, function (data, value) {
                            $("#SelectCommunicationNoteGroup").append($("<option></option>").val(value.Id).html(value.Name));
                        });
                    }

                });
            }

            function ResetCommunicationNodeForm() {
                $("#RowSelectCommunicationNoteGroup").css("background-color", "#56954E");
                $("#RowTxtCommunicationNotes").css("background-color", "#56954E");
                $("#RowTxtCommunicationNoteCC").css("background-color", "#56954E");
                $("#CommunicationNoteValidationError").css("display", "none");
                $('#RowCommunicationNoteIndividual').css("display", "none");

                $('#SelectCommunicationNoteGroup').val(0);
                $('#SelectNoteIndividual').val(-1);
                $('#txtIndividualNoteTo').val('');
                $('#txtCommunicationNotes').val('');
                $('#txtCommunicationNoteCC').val('');
                $('#HiddenCommunicationNoteMailReceiver').val('');
                $('input:radio[name=CommunicationNodeRadio][value=Group]').attr('checked', true)
                $('input:radio[name=CommunicationNodeRadio][value=Individual]').attr('checked', false)
            }

            function LoadCommunicationNotesItemData(CusReqNo, refreshGrid, hasRowSelected) {
                var colModel = [
                    {
                        title: "Line#",
                        width: "4%",
                        /*dataType: "float",*/
                        //align: "right",
                        dataIndx: "Line",
                        render: function (ui) {
                            var htmlstr = ui.rowData["Line"];
                            return htmlstr;
                        }
                    },
                    {
                        title: "Notes", //title of column.
                        width: "41%", //initial width of column
                        //dataType: "integer", //data type of column
                        dataIndx: "Note",
                        render: function (ui) {
                            var htmlstr = '<span title="' + ui.rowData.Note + '">' + ui.rowData.Note + "</span ><br/>";
                            return htmlstr;
                        }
                    },
                    {
                        title: "Date",
                        width: "13%",
                        dataIndx: "Date",
                        render: function (ui) {
                            var htmlstr = '<span title="' + ui.rowData.DateTimeNote + '">' + ui.rowData.DateTimeNote + "</span ><br/>";
                            return htmlstr;
                        }
                    },
                    {
                        title: "By",
                        width: "13%",
                        dataIndx: "EmployeeName",
                        render: function (ui) {
                            var htmlstr = '<span title="' + ui.rowData.EmployeeName + '">' + ui.rowData.EmployeeName + "</span ><br/>";
                            return htmlstr;
                        }
                    },
                    {
                        title: "To <br/> CC",
                        width: "18%",
                        dataIndx: "NoteTo",
                        render: function (ui) {
                            var htmlstr = '<span title="' + ui.rowData.NoteTo + '">' + ui.rowData.NoteTo + "</span ><br/>";
                            htmlstr += '<span title="' + ui.rowData.CCUserID + '">' + ui.rowData.CCUserID + "</span ><br/>"
                            return htmlstr;
                        }
                    },
                    {
                        title: "Send To Group",
                        width: "10%",
                        dataIndx: "SendToGroup",
                        render: function (ui) {
                            var htmlstr = '<span title="' + ui.rowData.SendToGroup + '">' + ui.rowData.SendToGroup + "</span ><br/>";
                            return htmlstr;
                        }
                    }
                ];

                //main object to be passed to pqGrid constructor.
                var qrystr = new URLSearchParams(window.location.search);
                var qrystrkey = qrystr.get("BOM");
                var noteNo = qrystr.get("Note");
                CusReqNo = parseInt(CusReqNo) > 0 ? CusReqNo : 0;

                var dataModel = {
                    location: "remote",
                    dataType: "JSON",
                    method: "POST",
                    url: 'GetComunicationNotes?BOMManagerId=' + qrystrkey + '&CustomerReqNo=' + CusReqNo,
                    //data: { BOMId: BOMID, clientId: clientid, BOMDate: BomDateVal },
                    getData: function (dataJSON) {
                        var data = dataJSON;
                        var totalRecords = 0;
                        if (data != 0) {
                            totalRecords = data[0].TotalCount;
                        }
                        if (hasRowSelected == true) {
                            ExpandCommunicationNoteGrid();
                        }
                        else {
                            if (totalRecords <= 0) {
                                CollapseCommunicationNoteGrid();
                            }
                            else {
                                ExpandCommunicationNoteGrid();
                            }
                        }
                        var cur_page = 1;
                        if (dataJSON.length != 0) {
                            cur_page = dataJSON[0].curpage;
                        }
                        return { curPage: cur_page, totalRecords: totalRecords, data: data };
                    }
                };
                //$gridPrj = $("#grid_md").pqGrid(obj).pqGrid('refreshDataAndView');
                var grid1 = $("#grid_md_notes").pqGrid({
                    width: "auto", height: 277,
                    selectionModel: { type: 'row', mode: 'single', native: true },
                    dataModel: dataModel,
                    rowHt: 35,

                    colModel: colModel,
                    editable: false,
                    postRenderInterval: -1,
                    //freezeCols: 2,
                    rowHt: 35,
                    hwrap: false,
                    wrap: false,
                    hoverMode: 'cell',
                    pageModel: { type: "local", rPP: 5, strRpp: "{0}", rPPOptions: [5, 10] },
                    numberCell: { show: false },
                    complete: function (event, ui) {
                        var gridInstance = grid1.pqGrid('getInstance');
                        if (RowIndexCommunicationNoteGrid > -1) {
                            gridInstance.grid.SelectRow().add({ rowIndx: RowIndexCommunicationNoteGrid });
                            RowIndexCommunicationNoteGrid = -1;
                        }

                        // Communication Note line highlighted while clicking the BOM Item hyperlink from Email.
                        if (parseInt(noteNo) > 0) {
                            $.each(gridInstance.grid.pdata, function (index, value) {
                                if (value.ID == noteNo) {
                                    gridInstance.grid.SelectRow().add({ rowIndx: index });
                                }
                            });
                        }

                        var gridInstanceRPP = $('#grid_md_notes').pqGrid('instance');
                        var rpp = gridInstanceRPP.options.pageModel.rPP;
                        var totalRecords = gridInstanceRPP.options.pageModel.totalRecords;
                        var curPage = gridInstanceRPP.options.pageModel.curPage;
                        var recordsLeft = totalRecords - (rpp * curPage);
                        //NotesItemGridHeightChange(recordsLeft, rpp);
                    }
                });

                if (refreshGrid) {
                    grid1.pqGrid('refreshDataAndView');
                }
            }

            function NotesItemGridHeightChange(recordsLeft, rpp) {
                var height;

                if (recordsLeft < 0) {
                    recordsLeft = rpp + recordsLeft;
                    height = recordsLeft * 35 + 40;
                }
                else {
                    height = rpp * 35 + 40;
                }
                $('#grid_md_notes .pq-grid-center-o').css('height', height + 7);
                $('#grid_md_notes .pq-grid-center').css('height', height + 7);
                $('#grid_md_notes .pq-body-outer').css('height', height + 7);
                $('#grid_md_notes').pqGrid('option', 'height', height + 50).pqGrid('refresh');
            }

            function LoadCommunicationNoteBuyers() {
                //var Crncyval = $('#HiddenBomCurrency').val();
                //var valmatch = false;
                $.ajax({
                    type: "POST", url: "GetLoginListForClient",
                    dataType: "json",
                    contentType: "application/json",
                    success: function (Jsondata) {
                        //$.each(Jsondata, function (data, value) {

                        //    $("#ddlBuyers").append($("<option></option>").val(value.LoginId).html(value.EmployeeName));
                        //});
                        listItems = '';
                        listItems = defaultOption;
                        for (var i = 0; i < Jsondata.length; i++) {
                            listItems += optionStart + Jsondata[i].LoginId + "'>" + Jsondata[i].EmployeeName + optionEnd;
                        }
                        $("#SelectNoteIndividual").html(listItems);
                        $('#SelectNoteIndividual').val(-1);
                    }

                });
            }

            $('#ShowNotesItemDiv').click(function () {
                if ($('#divNotesItemGrid').css("display") == 'none') {
                    ExpandCommunicationNoteGrid();
                }
                else {
                    CollapseCommunicationNoteGrid();
                }
            });

            function ExpandCommunicationNoteGrid() {
                $('#divNotesItemGrid').css("display", 'block');
                $('#refreshNotesItem').css("display", 'block');
                $('#headingThreeNote').removeClass('headingOneCorner');
                $('#ShowNotesItemDiv').css("background-image", "url(/Areas/BOM/Images/hide.gif)");
            }

            function CollapseCommunicationNoteGrid() {
                $('#divNotesItemGrid').css("display", 'none');
                $('#refreshNotesItem').css("display", 'none');
                $('#headingThreeNote').addClass('headingOneCorner');
                $('#ShowNotesItemDiv').css("background-image", "url(/Areas/BOM/Images/show.gif)");
            }

            $("#refreshNotesItem").click(function () {
                var gridInstance = $('#grid_md_notes').pqGrid('getInstance');
                if (gridInstance.grid.SelectRow().getSelection().length > 0) {
                    RowIndexCommunicationNoteGrid = gridInstance.grid.SelectRow().getSelection()[0].rowIndx;
                }
                $('#grid_md_notes').pqGrid('refreshDataAndView');
            });

            $("#RefreshNoteIndividual").click(function () {
                LoadCommunicationNoteBuyers();
            });

            function ValidateCommunicationNodeForm() {
                var formIsValid = true;
                if (($('#SelectCommunicationNoteGroup').val() == 0 || $('#SelectCommunicationNoteGroup').val() == null)
                    && $('#SelectNoteIndividual').val() == 0 || $('#SelectNoteIndividual').val() == null) {
                    $("#RowSelectCommunicationNoteGroup").css("background-color", "#990000");
                    $("#RowCommunicationNoteIndividual").css("background-color", "#990000");
                    formIsValid = false;
                }
                else {
                    $("#RowSelectCommunicationNoteGroup").css("background-color", "#56954E");
                    $("#RowCommunicationNoteIndividual").css("background-color", "#56954E");
                    if (formIsValid)
                        formIsValid = true;
                }

                if ($('#txtCommunicationNotes').val() == '' || $('#txtCommunicationNotes').val() == null) {
                    $("#RowTxtCommunicationNotes").css("background-color", "#990000");
                    formIsValid = false;
                }
                else {
                    $("#RowTxtCommunicationNotes").css("background-color", "#56954E");
                    if (formIsValid)
                        formIsValid = true;
                }
                return formIsValid;
            }

            function SaveCommunicationNodeData() {
                var qrystr = new URLSearchParams(window.location.search);
                var BOMManagerID = qrystr.get("BOM");
                var selectedCusReqIds = [];

                var gridInstance = $('#grid_md').pqGrid('getInstance');
                $.each(gridInstance.grid.SelectRow().getSelection(), function (index, value) {
                    selectedCusReqIds.push(value.rowData.CustomerRequirementId);
                });

                var cCUserIds = $(".dynamiclabelid-note").map(function () {
                    return this.id;
                }).get().join("||");
                var requestToPOHubBy = $('#HiddenBomRequestToPOHubBy').val() == null || $('#HiddenBomRequestToPOHubBy').val() == '' ? 0 : $('#HiddenBomRequestToPOHubBy').val();
                var updateByPH = $('#HiddenBomUpdateByPH').val() == null || $('#HiddenBomUpdateByPH').val() == '' ? 0 : $('#HiddenBomUpdateByPH').val();
                var companyNo = $('#HiddenBomCompanyNo').val() == null || $('#HiddenBomCompanyNo').val() == '' ? 0 : $('#HiddenBomCompanyNo').val();
                var contact2No = $('#HiddenBomContact2No').val() == null || $('#HiddenBomContact2No').val() == '' ? 0 : $('#HiddenBomContact2No').val();

                $.ajax({
                    processData: true,
                    contentType: 'application/json',
                    type: 'POST',
                    url: 'SaveCommunicationNotesData?&BOMManagerID=' + BOMManagerID
                        + '&cusReqIds=' + selectedCusReqIds.join(',')
                        + '&sendToGroup=' + $('#SelectCommunicationNoteGroup').val()
                        + '&notes=' + $('#txtCommunicationNotes').val()
                        + '&cCUserIds=' + cCUserIds
                        + '&BOMManagerName=' + $('#BOMManagerName').val()
                        + '&sendTo=' + $('#SelectNoteIndividual').val()
                        + '&companyNo=' + companyNo
                        + '&contact2No=' + contact2No
                        + '&requestToPOHubBy=' + requestToPOHubBy
                        + '&updateByPH=' + updateByPH,
                    dataType: "json",

                    success: function (data) {
                        ResetCommunicationNodeForm();
                        if (data) {
                            location.reload();
                        }
                    },
                    error: function (err) {}
                });
            }

            $('input[type=radio][name=CommunicationNodeRadio]').change(function () {
                if ($(this).val() == 'Group') {
                    $('#RowCommunicationNoteIndividual').css("display", "none");
                    $('#RowSelectCommunicationNoteGroup').css("display", "table-row");
                    $('#RowSelectCommunicationNoteGroup').css("background-color", "#56954E");
                }
                else if ($(this).val() == 'Individual') {
                    $('#RowSelectCommunicationNoteGroup').css("display", "none");
                    $('#RowCommunicationNoteIndividual').css("display", "table-row");
                    $('#RowCommunicationNoteIndividual').css("background-color", "#56954E");
                }
                $('#SelectCommunicationNoteGroup').val(0);
                $('#SelectNoteIndividual').val(0);
            });

            $("#txtCommunicationNoteCC").autocomplete({
                source: function (request, response) {
                    $.ajax({
                        url: "GetMailToList",
                        type: "POST",
                        dataType: "json",
                        data: { searchString: request.term + '%' },
                        success: function (data) {
                            response($.map(data, function (item) {
                                return { label: item.EmployeeName, value: item.EmployeeName, recieverid: item.LoginId, MailMessageAddressType: item.MailMessageAddressType };
                            }))

                        }
                    })
                },
                select: function (e, i) {
                    e.preventDefault();
                    $("#txtCommunicationNoteCC").val('');
                    var hiddenReceiver = $("#HiddenCommunicationNoteMailReceiver").val();
                    var total_element = $(".element-notes").length;
                    // last <div> with element class id
                    var lastid = $(".element-notes:last").attr("id");
                    var split_id = lastid.split("_");
                    var nextindex = Number(split_id[1]) + 1;
                    var max = 5;
                    // Check total number elements
                    //if (total_element < max) {
                    // Adding new div container after last occurance of element class
                    $(".element-notes:last").after("<div class='element-notes' id='div_" + i.item.recieverid + "'></div>");
                    // Adding element to <div>
                    if (i.item.MailMessageAddressType == "Group")
                        $("#div_" + i.item.recieverid).append("<label class='dynamiclabelid-note mailRecipientGroup' id='" + i.item.recieverid + "_MailGroup' >" + i.item.value + "</label>&nbsp;<span id='remove_" + i.item.recieverid + "_MailGroup' style='color: black; cursor: default;' class='remove'>X</span>");
                    else
                        $("#div_" + i.item.recieverid).append("<label class='dynamiclabelid-note mailRecipient' id='" + i.item.recieverid + "' >" + i.item.value + "</label>&nbsp;<span id='remove_" + i.item.recieverid + "' style='color: black; cursor: default;' class='remove'>X</span>");

                    if (hiddenReceiver == '' || hiddenReceiver == undefined) {
                        $("#HiddenCommunicationNoteMailReceiver").val(i.item.recieverid);
                    }
                    else {
                        $("#HiddenCommunicationNoteMailReceiver").val(hiddenReceiver + ',' + i.item.recieverid);
                    }
                    //}
                    $("#txtCommunicationNoteCC").val('');
                    $('.remove').click(function () {
                        var id = this.id;
                        var split_id = id.split("_");
                        var deleteindex = split_id[1];

                        // Remove <div> with id
                        $("#div_" + deleteindex).remove();
                    });
                }, minLength: 0
            }).autocomplete("instance")._renderItem = function (ul, item) {
                if (item.MailMessageAddressType == "Group")
                    return $("<li>").append("<div class='mailRecipientGroup'>" + item.label + "</div>").appendTo(ul);
                else
                    return $("<li>").append("<div class='mailRecipient'>" + item.label + "</div>").appendTo(ul);
                };

            function displaySourcingResultDetails(rowData) {
                $("#lblSRManufactuer").text(rowData.ManufacturerName);
                $("#lblSRDateCode").text(rowData.DateCode);
                $("#lblSRPackageType").text(rowData.PackageName);
                $("#lblSRProductType").text(rowData.ProductName);
                var supplierWarrantlyText = rowData.SupplierWarranty > 0 ? (rowData.SupplierWarranty + ' days') : '';
                $("#lblSRSupplierWarrantly").text(supplierWarrantlyText);
                $("#lblSRTestingRecommended").text(rowData.IsTestingRecommended == true ? "Yes" : "No");
                $("#lblSRCountryOfOrigin").text(rowData.CountryOfOrigin);
                $("#lblSRMoq").text(rowData.MOQ);
                $("#lblSRTotalQuantity").text(rowData.SupplierTotalQSA);
                $("#lblSRLastTimeBuy").text(rowData.SupplierLTB);
                $("#lblSRNotes").text(SetCleanText(rowData.Notes));
                $("#lblSRImagesAttached").text(rowData.IsImageAvailable == true ? "Yes" : "No");
                $("#lblSrSPQ").text(rowData.SPQ);
                $("#lblSrLeadTime").text(rowData.LT);
                $("#lblSrRoHS").text(rowData.ROHSDescription);
                $("#lblSrFactorySealed").text(rowData.FactorySealed);
                $("#lblSrMSL").text(rowData.MSL);
                $('#auto-sourcing-details').css('display', 'block');
            }

            function clearSourcingResultDetails() {
                $("#lblSRManufactuer").text("");
                $("#lblSRDateCode").text("");
                $("#lblSRPackageType").text("");
                $("#lblSRProductType").text("");
                $("#lblSRSupplierWarrantly").text("");
                $("#lblSRTestingRecommended").text("");
                $("#lblSRCountryOfOrigin").text("");
                $("#lblSRMoq").text("");
                $("#lblSRTotalQuantity").text("");
                $("#lblSRLastTimeBuy").text("");
                $("#lblSRNotes").text("");
                $("#lblSRImagesAttached").text("");
                $("#lblSrSPQ").text("");
                $("#lblSrLeadTime").text("");
                $("#lblSrRoHS").text("");
                $("#lblSrFactorySealed").text("");
                $("#lblSrMSL").text("");
                $('#auto-sourcing-details').css('display', 'none');
            }
        </script>
</body>
</html>
