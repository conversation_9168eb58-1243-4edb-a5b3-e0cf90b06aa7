﻿
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE OR ALTER PROCEDURE [dbo].[usp_select_Login_by_Name]                              
@LoginName nvarchar(17)                                  
AS  
/*  
 * Action: Created  By:<PERSON><PERSON><PERSON><PERSON> Date:14-11-2023  Comment: For RP-1564.  
 */                                
BEGIN              
DECLARE @MailGroupId INT                
DECLARE @IsGlobal bit              
SELECT TOP 1 @MailGroupId = ml.MailGroupId FROM dbo.tbMailGroup ml                
INNER JOIN dbo.tbClient c ON ml.ClientNo=c.ClientId WHERE REPLACE(ml.Name , ' ', '')='PurchaseHUB' AND c.IsPOHub=1               
              
--Espire 28 th aug 2017               
select @IsGlobal = sgl.IsGlobal from tbLogin lg left join tbSecurityGroupLogin sgl on lg.LoginId = sgl.LoginNo              
where lg.LoginName = @LoginName and isnull(sgl.IsGlobal,0) = 1              
              
declare @DefaultClientNo int              
select @DefaultClientNo = ml.LastClientNo from tbMasterLogin ml join tblogin lg on ml.MasterLoginId = lg.MasterLoginNo              
WHERE lg.LoginName = @LoginName               
            
--            
declare @isDivision bit              
set @isDivision=(select count(*) from tbDivision dv join tblogin lg on dv.Manager = lg.LoginId  WHERE lg.LoginName = @LoginName and not dv.Inactive = 1 and dv.Manager is not null)            
            
declare @isTeam bit              
set @isTeam=(select count(*) from tbTeam tm join tblogin lg on tm.Manager = lg.LoginId  WHERE lg.LoginName = @LoginName and tm.Manager is not null )            
--            
    
--IsGSA    
declare @IsGSA bit    
declare @loginid int     
select top 1 @loginid= loginid from tbLogin where LoginName = @LoginName    
--IsGSAViewPermission  
DECLARE @IsGSAViewPermission BIT=1;  
    
if((select count(1) from tbGlobalSalesPerson where loginno = @loginid )>0)    
begin     
set @IsGSA = 1    
end     
else    
begin    
set @IsGSA = 0    
end    
                
SELECT v.*,CASE WHEN sg.LoginNo IS NOT NULL OR sg.LoginNo<>'' THEN 1 ELSE 0 END AS 'IsPOHub',@MailGroupId AS 'SecurityGroupId' ,              
isnull(@IsGlobal,0) as IsGlobal,isnull(@DefaultClientNo,v.ClientNo) as DefaultClientNo , cast(@isDivision as bit) as isDivision, CAST(@isTeam as bit )as isTeam      
, cast(@IsGSA as bit) as IsGSA,@IsGSAViewPermission AS IsGSAViewPermission    
FROM vwLogin v                 
LEFT OUTER JOIN vwSecurityGroupLogin sg  ON v.LoginId=sg.LoginNo                
AND sg.SecurityGroupNo IN (SELECT sg.SecurityGroupId FROM dbo.tbSecurityGroup sg                
INNER JOIN dbo.tbClient c ON sg.ClientNo=c.ClientId WHERE REPLACE(sg.SecurityGroupName , ' ', '')='PurchaseHUB' AND c.IsPOHub=1)               
              
WHERE LoginName = @LoginName               
END   
GO


