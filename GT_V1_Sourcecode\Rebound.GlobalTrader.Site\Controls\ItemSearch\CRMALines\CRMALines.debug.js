///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//----------------------------------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//----------------------------------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.ItemSearch");

Rebound.GlobalTrader.Site.Controls.ItemSearch.CRMALines = function(element) { 
	Rebound.GlobalTrader.Site.Controls.ItemSearch.CRMALines.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.ItemSearch.CRMALines.prototype = {

	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.ItemSearch.CRMALines.callBaseMethod(this, "initialize");
		this.addSetupData(Function.createDelegate(this, this.doSetupData));
		this.addGetDataComplete(Function.createDelegate(this, this.doGetDataComplete));
	},

	dispose: function() { 
		if (this.isDisposed) return;
		Rebound.GlobalTrader.Site.Controls.ItemSearch.CRMALines.callBaseMethod(this, "dispose");
	},
	
	doSetupData: function() {
		this._objData.set_PathToData("controls/ItemSearch/CRMALines");
		this._objData.set_DataObject("CRMALines");
		this._objData.set_DataAction("GetData");
		this._objData.addParameter("CRMANoLo", this.getFieldValue_Min("ctlCRMANo"));
		this._objData.addParameter("CRMANoHi", this.getFieldValue_Max("ctlCRMANo"));
		this._objData.addParameter("Part", this.getFieldValue("ctlPart"));
		this._objData.addParameter("Contact", this.getFieldValue("ctlContact"));
		this._objData.addParameter("CMName", this.getFieldValue("ctlCompany"));
		this._objData.addParameter("Salesman", this.getFieldValue("ctlSalesman"));
		this._objData.addParameter("Notes", this.getFieldValue("ctlCRMANotes"));
		this._objData.addParameter("InvoiceNoLo", this.getFieldValue_Min("ctlInvoiceNo"));
		this._objData.addParameter("InvoiceNoHi", this.getFieldValue_Max("ctlInvoiceNo"));
		this._objData.addParameter("CRMADateFrom", this.getFieldValue("ctlCRMADateFrom"));
		this._objData.addParameter("CRMADateTo", this.getFieldValue("ctlCRMADateTo"));
	},
	
	doGetDataComplete: function() {
		for (var i = 0, l = this._objResult.Results.length; i < l; i++) {
			var row = this._objResult.Results[i];
			var aryData = [
				row.No,
				$R_FN.setCleanTextValue(row.CMName),
				$R_FN.writePartNo(row.Part, row.ROHS),
				$R_FN.setCleanTextValue(row.Date),
				row.Quantity,
				$R_FN.setCleanTextValue(row.Salesman),
				row.InvoiceNo
			];
			this._tblResults.addRow(aryData, row.ID, false);
			aryData = null; row = null;
		}
	}
};
Rebound.GlobalTrader.Site.Controls.ItemSearch.CRMALines.registerClass("Rebound.GlobalTrader.Site.Controls.ItemSearch.CRMALines", Rebound.GlobalTrader.Site.Controls.ItemSearch.Base, Sys.IDisposable);
