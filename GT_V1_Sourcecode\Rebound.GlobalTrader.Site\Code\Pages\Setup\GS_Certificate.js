Type.registerNamespace("Rebound.GlobalTrader.Site.Pages.Setup");Rebound.GlobalTrader.Site.Pages.Setup.GS_Certificate=function(n){Rebound.GlobalTrader.Site.Pages.Setup.GS_Certificate.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Pages.Setup.GS_Certificate.prototype={get_ctlCertificatecategory:function(){return this._ctlCertificatecategory},set_ctlCertificatecategory:function(n){this._ctlCertificatecategory!==n&&(this._ctlCertificatecategory=n)},get_ctlCertificate:function(){return this._ctlCertificate},set_ctlCertificate:function(n){this._ctlCertificate!==n&&(this._ctlCertificate=n)},initialize:function(){Rebound.GlobalTrader.Site.Pages.Setup.GS_Certificate.callBaseMethod(this,"initialize")},goInit:function(){this._ctlCertificatecategory&&this._ctlCertificatecategory.addSelectCategory(Function.createDelegate(this,this.ctlCertificatecategory_SelectCategory));Rebound.GlobalTrader.Site.Pages.Setup.GS_Certificate.callBaseMethod(this,"goInit")},dispose:function(){this.isDisposed||(this._ctlCertificatecategory&&this._ctlCertificatecategory.dispose(),this._ctlCertificate&&this._ctlCertificate.dispose(),this._ctlCertificatecategory=null,this._ctlCertificate=null,Rebound.GlobalTrader.Site.Pages.Setup.GS_Certificate.callBaseMethod(this,"dispose"))},ctlCertificatecategory_SelectCategory:function(){this._ctlCertificate._intCertificatecategoryID=this._ctlCertificatecategory._intCertificateCategoryID;this._ctlCertificate._tbl.resizeColumns();this._ctlCertificate.show(!0);this._ctlCertificate.refresh()}};Rebound.GlobalTrader.Site.Pages.Setup.GS_Certificate.registerClass("Rebound.GlobalTrader.Site.Pages.Setup.GS_Certificate",Rebound.GlobalTrader.Site.Pages.Content,Sys.IDisposable);