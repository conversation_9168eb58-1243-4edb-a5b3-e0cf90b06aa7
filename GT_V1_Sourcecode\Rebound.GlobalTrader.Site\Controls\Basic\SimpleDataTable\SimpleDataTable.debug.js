///<reference name="MicrosoftAjax.js" />
///<reference path="~/Common/Data/Data.js" />
///<reference path="~/Common/Functions/Functions.js" />
///<reference path="~/Controls/IconButton/IconButton.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - dispose everything fully
//
// RP 21.12.2009:
// - new control
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls");

Rebound.GlobalTrader.Site.Controls.SimpleDataTable = function(element) { 
	Rebound.GlobalTrader.Site.Controls.SimpleDataTable.initializeBase(this, [element]);
	this.ControlType_SimpleDataTable = true;
	this._blnShowHeader = true;
	this._aryColumnAlignment = [];
	this._aryColumnWidth = [];
	this._aryExtraData = [];
	this._blnColumnsResized = false;
};

Rebound.GlobalTrader.Site.Controls.SimpleDataTable.prototype = {

	get_tbl: function() { return this._tbl; }, 	set_tbl: function(value) { if (this._tbl !== value)  this._tbl = value; }, 
	get_blnInsideDataListNugget: function() { return this._blnInsideDataListNugget; }, 	set_blnInsideDataListNugget: function(value) { if (this._blnInsideDataListNugget !== value)  this._blnInsideDataListNugget = value; }, 
	get_blnShowHeader: function() { return this._blnShowHeader; }, 	set_blnShowHeader: function(value) { if (this._blnShowHeader !== value)  this._blnShowHeader = value; }, 
	get_aryExtraData: function() { return this._aryExtraData; }, 	set_aryExtraData: function(value) { if (this._aryExtraData !== value)  this._aryExtraData = value; }, 
	get_aryColumnAlignment: function() { return this._aryColumnAlignment; }, 	set_aryColumnAlignment: function(value) { if (this._aryColumnAlignment !== value)  this._aryColumnAlignment = value; }, 
	get_aryColumnWidth: function() { return this._aryColumnWidth; }, 	set_aryColumnWidth: function(value) { if (this._aryColumnWidth !== value)  this._aryColumnWidth = value; }, 

	addAddAsyncRow: function(handler) { this.get_events().addHandler("AddAsyncRow", handler); },
	removeAddAsyncRow: function(handler) { this.get_events().removeHandler("AddAsyncRow", handler); },
	onAddAsyncRow: function() {
		var handler = this.get_events().getHandler("AddAsyncRow");
		if (handler) handler(this, Sys.EventArgs.Empty);
	},

	addAsyncDataAdditionComplete: function(handler) { this.get_events().addHandler("AsyncDataAdditionComplete", handler); },
	removeAsyncDataAdditionComplete: function(handler) { this.get_events().removeHandler("AsyncDataAdditionComplete", handler); },
	onAsyncDataAdditionComplete: function() {
		var handler = this.get_events().getHandler("AsyncDataAdditionComplete");
		if (handler) handler(this, Sys.EventArgs.Empty);
	},

	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.SimpleDataTable.callBaseMethod(this, "initialize");
	},

	dispose: function() { 
		if (this.isDisposed) return;
		if (this.get_element()) $clearHandlers(this.get_element());
		if (this._tbl) {
			this.unhookTableRowEvents();
			$clearHandlers(this._tbl);
		}
		this._tbl = null;
		this._aryColumnAlignment = null;
		this._aryColumnWidth = null;
		Rebound.GlobalTrader.Site.Controls.SimpleDataTable.callBaseMethod(this, "dispose");
		this.isDisposed = true;
	},

	clearTable: function() {
		this.unhookTableRowEvents();
		for (var i = this._tbl.rows.length - 1; i > 0; i--) { this._tbl.deleteRow(i); }
		Array.clear(this._aryExtraData);
	},
	
	getID: function(strControl, intRowIndex) {
		return String.format("{0}_{1}{2}", this._tbl.id, strControl, intRowIndex);
	},
	
	addRow: function(aryData, varValue, blnSelected, objExtraData, strSpecialClass) {
		if (!aryData) return;
		if (!strSpecialClass) strSpecialClass = "";
		var tr = this._tbl.insertRow(-1);
		var intRowIndex = tr.rowIndex;
		tr.setAttribute("bui_tableRowIndex", intRowIndex);
		for (var i = 0, l = aryData.length; i < l; i++) {
			if (i >= this._aryColumnAlignment.length) break;
			var td = document.createElement("td");
			if (i == 0) td.className = "first";
			if (i == (l - 1)) td.className = "last";
			if (strSpecialClass != "") {
				td.className += " " + strSpecialClass;
				if (i == 0) td.className += String.format(" {0}_First", strSpecialClass);
				if (i == (l - 1)) td.className += String.format(" {0}_Last", strSpecialClass);
			}
			switch (this._aryColumnAlignment[i]) {
				case $R_ENUM$HorizontalAlign.Center: td.className += " alignCenter"; break;
				case $R_ENUM$HorizontalAlign.Right: td.className += " alignRight"; break;
				case $R_ENUM$HorizontalAlign.Justify: td.className += " alignJustify"; break;
			}
			aryData[i] += ""; //convert to string
			var strData = aryData[i].toString().trim();
			if (strData.length == 0 || strData == "undefined") strData = "&nbsp;";
			td.innerHTML = strData;
			td.setAttribute("bui_tableRowIndex", intRowIndex);
			tr.appendChild(td);
			td = null; strData = null;
		}
		Array.add(this._aryExtraData, objExtraData);
		tr = null;
	},

	addRowRowColor: function (aryData, varValue, blnSelected, objExtraData, strSpecialClass, blnRowColor, RowColor) {
	    if (!aryData) return;
	    if (!strSpecialClass) strSpecialClass = "";
	    var tr = this._tbl.insertRow(-1);
	    var intRowIndex = tr.rowIndex;
	    tr.setAttribute("bui_tableRowIndex", intRowIndex);
	    for (var i = 0, l = aryData.length; i < l; i++) {
	        if (i >= this._aryColumnAlignment.length) break;
	        var td = document.createElement("td");
	        if (i == 0) td.className = "first";
	        if (i == (l - 1)) td.className = "last";
	        if (strSpecialClass != "") {
	            td.className += " " + strSpecialClass;
	            if (i == 0) td.className += String.format(" {0}_First", strSpecialClass);
	            if (i == (l - 1)) td.className += String.format(" {0}_Last", strSpecialClass);
	        }
	        switch (this._aryColumnAlignment[i]) {
	            case $R_ENUM$HorizontalAlign.Center: td.className += " alignCenter"; break;
	            case $R_ENUM$HorizontalAlign.Right: td.className += " alignRight"; break;
	            case $R_ENUM$HorizontalAlign.Justify: td.className += " alignJustify"; break;
	        }
	        aryData[i] += ""; //convert to string
	        var strData = aryData[i].toString().trim();
	        if (strData.length == 0 || strData == "undefined") strData = "&nbsp;";
	        td.innerHTML = strData;
	        td.setAttribute("bui_tableRowIndex", intRowIndex);
	        tr.appendChild(td);
	        td = null; strData = null;
	    }
	    Array.add(this._aryExtraData, objExtraData);
	    tr = null;
	    if (blnRowColor) this.RowColor(intRowIndex, RowColor);
	},
	RowColor: function (intRowIndex, RowColor) {
	    Sys.UI.DomElement.addCssClass(this._tbl.rows[intRowIndex], RowColor);
	},
	show: function(blnShow) {
		$R_FN.showElement(this.get_element(), blnShow);
	},
	
	enable: function(bln) {
		this._blnEnabled = bln;
	},
		
	countRows: function() {
		if (!this._tbl) return 0;
		if (!this._tbl.rows) return 0;
		return (this._tbl.rows.length);
	},
	
	removeRow: function(intIndex) {
		if (this._tbl.rows[intIndex]) $clearHandlers(this._tbl.rows[intIndex]);
		this._tbl.deleteRow(intIndex);
		Array.removeAt(this._aryExtraData, intIndex);
	},
	
	scrollToRow: function(intIndex) {
		if (intIndex < 0) intIndex = 0;
		var tr = null;
		if (intIndex < this._tbl.rows.length) tr = this._tbl.rows[intIndex];
		if (!tr) return;
		this._pnlScroll.scrollTop += Sys.UI.DomElement.getBounds(tr).y - Sys.UI.DomElement.getBounds(this._pnlScroll).y;
		tr = null;
	},
	
	scrollToRowAfterPause: function(intRowIndex) {
		setTimeout(String.format("$find('{0}').scrollToRow({1});", this._element.id, intRowIndex), 10);
	},
	
	unhookTableRowEvents: function() {
		for (var i = 0, l = this._tbl.rows; i < l; i++) {
			if (this._tbl.rows[i]) $clearHandlers(this._tbl.rows[i]);
		}
	},
	
	resetAsyncData: function(aryDataToAdd) {
		this._aryAsyncDataToAdd = [];
		this._intAsyncRowsToAdd = 0;
		this._intAsyncRowsAdded = 0;
	},
	
	addDataForAsyncAddition: function(aryDataToAdd) {
		if (aryDataToAdd) Array.addRange(this._aryAsyncDataToAdd, aryDataToAdd);
		this._intAsyncRowsToAdd = this._aryAsyncDataToAdd.length;
	},
	
	startAddingRowsAsync: function() {
		this._intAsyncRowsAdded = 0;
		if (!this._aryAsyncDataToAdd) this._aryAsyncDataToAdd = [];
		this._intAsyncRowsToAdd = this._aryAsyncDataToAdd.length;
		if (this._aryAsyncDataToAdd.length > 0) {
			this.addRowAsync();
		} else {
			this.finishedAddingRowsAsync();
		}
	},
	
	addRowAsync: function() {
		if (this._intAsyncRowsAdded < this._intAsyncRowsToAdd) {
			this._objAsyncDataRow = this._aryAsyncDataToAdd[this._intAsyncRowsAdded];
			this.onAddAsyncRow();
			this._intAsyncRowsAdded += 1;
			var strID = this._element.id;
			var fn = function() { $find(strID).addRowAsync(); };
			setTimeout(fn, 0);
		} else {
			this.finishedAddingRowsAsync();
		}
	},
	
	finishedAddingRowsAsync: function() {
		this.resetAsyncData();
		this.resizeColumns();	
		this.onAsyncDataAdditionComplete();
	}

};

Rebound.GlobalTrader.Site.Controls.SimpleDataTable.registerClass("Rebound.GlobalTrader.Site.Controls.SimpleDataTable", Sys.UI.Control, Sys.IDisposable);