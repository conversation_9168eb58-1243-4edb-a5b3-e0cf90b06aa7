﻿CREATE OR ALTER PROCEDURE  [dbo].[usp_insert_SupplierInvoice]                               
--******************************************************************************************                              
--* Created By :- <PERSON><PERSON>                            
--* Created Date :- 05-06-2013                            
--* Description :- Insert Supplier Invoice                 
--* Updated By  :- [001] <PERSON> Singh RP-34          
      
--* [RP-2630]  02-02-2024  <PERSON>         
--******************************************************************************************                              
  @ClientNo INT                              
 ,@CompanyNo INT                            
 ,@SupplierInvoiceNumber nvarchar(100)                            
 ,@SupplierInvoiceDate DATETIME                            
 ,@SupplierCode NVARCHAR(60)=NULL                            
 ,@SupplierName NVARCHAR(512)= NULL                            
 ,@CurrencyNo INT = NULL                            
 ,@InvoiceAmount FLOAT = NULL                            
 ,@GoodsValue FLOAT = NULL                            
 ,@Tax FLOAT = NULL                            
 ,@DeliveryCharge FLOAT = NULL                            
 ,@BankFee FLOAT = NULL                            
 ,@CreditCardFee FLOAT = NULL                            
 ,@Notes NVARCHAR(512) = NULL                            
 ,@SecondRef NVARCHAR(32) = NULL                            
 ,@Narrative NVARCHAR(82) = NULL                            
 ,@CanbeExported BIT = NULL                            
 ,@TaxNo INT  = NULL                    
 ,@TaxCode NVARCHAR(10) = NULL                        
 ,@CurrencyCode NVARCHAR(10)                       
 ,@UpdatedBy INT                            
 ,@StatusReason INT  = NULL                    
 ,@AuthoriseNotes NVARCHAR(500) = NULL         
 , @containsGiLineItems int = 0 -- [RP-2630]      
 ,@SupplierInvoiceId int OUTPUT                              
AS                               
                          
    BEGIN                             
    --Get 'Automatically exported for supplier invoice setting' from tbsetting table                
        DECLARE @Exported bit= 0                
        DECLARE @SettingValue nvarchar(5)                
                        
        SELECT TOP 1 @SettingValue=SettingValue FROM tbsetting WHERE ClientID=@ClientNo AND SettingItemID=9                
                        
        IF UPPER(ISNULL(@SettingValue,''))='TRUE' AND ISNULL(@CanbeExported,0)=1                
        BEGIN                
           SET @Exported=1                
        END                
    --End              
           
 /***** [001]*************/          
 --if(@StatusReason=0)          
 --set @StatusReason=( select StatusReasonId from  tbStatusReason where section='SI' and Name='Lines Not Added')          
 /***** [001]*************/       
       
 --[RP-2630] start      
 if(ISNULL(@containsGiLineItems,0) = 0 ) --[RP-2630]    (and ISNULL(@StatusReason,0) = 0)    
 set @StatusReason=( select StatusReasonId from  tbStatusReason where section='SI' and StatusReasonId = 17)            
 --[RP-2630] end      
    --Insert CreatedDate  :               
    declare @DateCreated date              
 set @DateCreated = GETDATE()              
               
 DECLARE @ADLogin nchar(60)              
               
 SELECT @ADLogin = ADLogin from tbLogin WHERE LoginId = @UpdatedBy              
            
                
              
                              
   INSERT  INTO dbo.tbSupplierInvoice (                              
    ClientNo                               
   ,CompanyNo                             
   ,SupplierInvoiceNumber                          
   ,SupplierInvoiceDate                            
   ,SupplierCode                             
   ,SupplierName                             
   ,CurrencyNo                             
   ,InvoiceAmount                             
   ,GoodsValue                             
   ,Tax                 
   ,DeliveryCharge                            
   ,BankFee                            
   ,CreditCardFee                            
   ,Notes                  
   ,SecondRef                            
   ,Narrative                            
   ,CanbeExported                            
   ,TaxNo                            
,TaxCode                       
   ,CurrencyCode                            
   ,DLUP                            
   ,UpdatedBy                  
   ,Exported              
   , DateCreated               
   , ADLogin                 
   , StatusReasonNo                      
           )                              
  VALUES  (                              
     @ClientNo                            
    ,@CompanyNo                            
    ,@SupplierInvoiceNumber                            
  ,@SupplierInvoiceDate                            
    ,@SupplierCode                 
    ,@SupplierName                            
    ,@CurrencyNo                            
    ,@InvoiceAmount                            
    ,@GoodsValue                            
    ,@Tax           
    ,@DeliveryCharge                            
    ,@BankFee                             
    ,@CreditCardFee                             
    ,@Notes                             
    ,@SecondRef                            
    ,@Narrative                            
    ,@CanbeExported                             
    ,@TaxNo                             
    ,@TaxCode                       
    ,@CurrencyCode                           
    ,CURRENT_TIMESTAMP                             
    ,@UpdatedBy                  
    ,@Exported                 
    ,@DateCreated               
    , @ADLogin                 
 , @StatusReason                    
    )                               
                                
    SET @SupplierInvoiceId = scope_identity()                 
 if(@AuthoriseNotes is not null)            
 begin            
 --Reset the SupplierInvoice:                                    
   EXEC usp_insert_SupplierAuthorized @SupplierInvoiceId ,@AuthoriseNotes,@UpdatedBy             
   end                                                                                    
END