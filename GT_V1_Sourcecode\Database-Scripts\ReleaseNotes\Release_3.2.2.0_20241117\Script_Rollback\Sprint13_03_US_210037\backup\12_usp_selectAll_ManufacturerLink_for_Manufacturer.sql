﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO 
CREATE OR ALTER PROCEDURE [dbo].[usp_selectAll_ManufacturerLink_for_Manufacturer]      
--****************************************************************************************      
--* RP 12.11.09      
--* - add @ClientID to restrict display to current Client Company only      
--*Updated Date: 11-08-2014    
--*Description: Under Manufactures Screen,     
--*Supplier Who Distribute’ nugget need to have another column named as Company Type.    
--* This column will show company type against each company showing in the grid    
--****************************************************************************************      
@ManufacturerId INT      
,@ClientID int      
AS      
SELECT a.ManufacturerLinkId      
,  a.ManufacturerNo      
,  b.ManufacturerName      
,  a.SupplierCompanyNo       
,  c.CompanyName  As SupplierName       
,  a.ManufacturerRating      
,  a.SupplierRating      
,  a.UpdatedBy      
,  a.DLUP    
,  t.Name as CompanyType       
FROM  dbo.tbManufacturerLink a      
JOIN dbo.tbManufacturer b      
 ON a.ManufacturerNo = b.ManufacturerId      
JOIN dbo.tbCompany c      
 ON a.SupplierCompanyNo = c.CompanyId      
 AND c.ClientNo = @ClientID      
LEFT JOIN tbCompanyType t on c.TypeNo=t.CompanyTypeId    
WHERE a.ManufacturerNo = @ManufacturerId      
ORDER BY ManufacturerName  
  