using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site;

namespace Rebound.GlobalTrader.Site.Controls.LeftNuggets {
	public partial class WarehouseSelection : Selection {

		protected override void OnInit(EventArgs e) {
			SetLeftNuggetType("WarehouseSelection");
			base.OnInit(e);
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.LeftNuggets.Base", ctlDesignBase.ClientID);
			AddItems();
		}

		private void AddItems() {
			HtmlControl ul = new HtmlGenericControl("ul");
			if (SecurityManager.CheckGeneralPermission(BLL.SecurityFunction.List.Warehouse_ReceivePO)) ul.Controls.Add(AddItem("Warehouse_ReceivePurchaseOrderBrowse"));
			if (SecurityManager.CheckGeneralPermission(BLL.SecurityFunction.List.Warehouse_ShipSO)) ul.Controls.Add(AddItem("Warehouse_ShipSalesOrderBrowse"));
			if (SecurityManager.CheckGeneralPermission(BLL.SecurityFunction.List.Warehouse_ReceiveCRMA)) ul.Controls.Add(AddItem("Warehouse_ReceiveCustomerRMABrowse"));
			if (SecurityManager.CheckGeneralPermission(BLL.SecurityFunction.List.Warehouse_ShipSRMA)) ul.Controls.Add(AddItem("Warehouse_ShipSupplierRMABrowse"));
			ul.Controls.Add(AddItem("Warehouse_StockBrowse"));
			ul.Controls.Add(AddItem("Warehouse_ServicesBrowse"));
			ul.Controls.Add(AddItem("Warehouse_LotsBrowse"));
			ul.Controls.Add(AddItem("Warehouse_GoodsInBrowse"));
            ul.Controls.Add(AddItem("Warehouse_IHSCatalogueBrowse"));
			_plhItems.Controls.Add(ul);
		}

	}
}