///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// SK 08.01.2010:
// - comment out references to ctlIncludeAlternates
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.CusReqMainInfo_Close = function(element) { 
	Rebound.GlobalTrader.Site.Controls.Forms.CusReqMainInfo_Close.initializeBase(this, [element]);
	this._intCustomerRequirementID = -1;
};

Rebound.GlobalTrader.Site.Controls.Forms.CusReqMainInfo_Close.prototype = {

	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.Forms.CusReqMainInfo_Close.callBaseMethod(this, "initialize");
		this.addShown(Function.createDelegate(this, this.formShown));
	},

	dispose: function() { 
		if (this.isDisposed) return;
		if (this._ctlConfirm) this._ctlConfirm.dispose();
		this._ctlConfirm = null;
		this._intCustomerRequirementID = null;
		Rebound.GlobalTrader.Site.Controls.Forms.CusReqMainInfo_Close.callBaseMethod(this, "dispose");
	},

	formShown: function() {
		if (this._blnFirstTimeShown) {
			this._ctlConfirm = this.getFieldComponent("ctlConfirm");
			this._ctlConfirm.addClickYesEvent(Function.createDelegate(this, this.yesClicked));
			this._ctlConfirm.addClickNoEvent(Function.createDelegate(this, this.noClicked));
		}
//		this.setFieldValue("ctlIncludeAlternates", false);
		this.getFieldDropDownData("ctlReason");		
	},

	yesClicked: function() {
		this.showSaving(true);
		var obj = new Rebound.GlobalTrader.Site.Data();
		obj.set_PathToData("controls/Nuggets/CusReqMainInfo");
		obj.set_DataObject("CusReqMainInfo");
		obj.set_DataAction("Close");
		obj.addParameter("id", this._intCustomerRequirementID);
//		obj.addParameter("IncludeAlternates", this.getFieldValue("ctlIncludeAlternates"));
		obj.addParameter("Reason", this.getFieldValue("ctlReason"));
		obj.addDataOK(Function.createDelegate(this, this.saveComplete));
		obj.addError(Function.createDelegate(this, this.saveError));
		obj.addTimeout(Function.createDelegate(this, this.saveError));
		$R_DQ.addToQueue(obj);
		$R_DQ.processQueue();
		obj = null;
		this.onSave();
	},

	noClicked: function() {
		this.showSaving(false);
		this.onNotConfirmed();
	},

	saveError: function(args) {
		this.showSaving(false);
		this._strErrorMessage = args._errorMessage;
		this.onSaveError();
	},

	saveComplete: function(args) {
		this.showSaving(false);
		if (args._result.Result == true) {
			this.onSaveComplete();
		} else {
			this._strErrorMessage = args._errorMessage;
			this.onSaveError();
		}
	}
	
};

Rebound.GlobalTrader.Site.Controls.Forms.CusReqMainInfo_Close.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.CusReqMainInfo_Close", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
