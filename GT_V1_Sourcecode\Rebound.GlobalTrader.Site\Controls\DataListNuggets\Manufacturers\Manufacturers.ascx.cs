//----------------------------------------------------------------------------------------------------------------
// RP 30.11.2009:
// - allow passing of an initial manufacturer name search
//----------------------------------------------------------------------------------------------------------------
using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.ComponentModel;
using Rebound.GlobalTrader.Site.Controls;

namespace Rebound.GlobalTrader.Site.Controls.DataListNuggets {

	/// <summary>
	/// Data List Nugget: Manufacturers
	/// </summary>
	[DefaultProperty("")]
	[ToolboxData("<{0}:Manufacturers runat=server></{0}:Manufacturers>")]
	public partial class Manufacturers : Base {

		protected IconButton ibtnAdd;

		#region Overrides

		protected override void OnInit(EventArgs e) {
			SetDataListNuggetType("Manufacturers");
			base.OnInit(e);
			AddScriptReference("Controls.DataListNuggets.Manufacturers.Manufacturers.js");
			ibtnAdd = FindIconButton("ibtnAdd");
			TitleText = Functions.GetGlobalResource("CompanyListType", DataListNuggets.CompanyListType.Manufacturers);
			SetupTable();
		}

		protected override void OnLoad(EventArgs e) {
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.DataListNuggets.Manufacturers", ctlDesignBase.ClientID);
			//_scScriptControlDescriptor.AddElementProperty("ibtnAdd", FindIconButton("ibtnAdd").ClientID);
			base.OnLoad(e);
		}

		protected override void GetSavedState() {
			base.GetSavedState();

			//don't render state if we are searching for a Manufcturer
			if (!String.IsNullOrEmpty(_objQSManager.ManufacturerName)) {
				ResetAllState();
				SetFilterValue("Name", _objQSManager.ManufacturerName);
			}
		}

		#endregion

		private void SetupTable() {
			_tbl.AllowSelection = false;
			_tbl.Columns.Add(new FlexiDataColumn("Code", WidthManager.GetWidth(WidthManager.ColumnWidth.ManufacturerCode), true));
			_tbl.Columns.Add(new FlexiDataColumn("ManufacturerName", WidthManager.GetWidth(WidthManager.ColumnWidth.CompanyName), true));
            _tbl.Columns.Add(new FlexiDataColumn("URL", WidthManager.GetWidth(WidthManager.ColumnWidth.EmailAddress), true));
            _tbl.Columns.Add(new FlexiDataColumn("ConflictResource", Unit.Empty, false));
			_tbl.Columns.Add(new FlexiDataColumn("GroupName", Unit.Empty, false));
			_tbl.Columns.Add(new FlexiDataColumn("GroupCode", Unit.Empty, false));
			_tbl.Columns.Add(new FlexiDataColumn("SystemManufacturer", Unit.Empty, false));
		}

	}
}