using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Reflection;

namespace Rebound.GlobalTrader.Site {
	public class AutoSearchManager {

		public static Controls.AutoSearch.Base GetAutoSearch(string strAssembly, string strAutoSearch) {
			string strFullAssemblyAutoSearchType = string.Format("{0}.Controls.AutoSearch.{1}", strAssembly, strAutoSearch);
			try {
				Type typ = Assembly.Load(strAssembly).GetType(strFullAssemblyAutoSearchType);
				return (Rebound.GlobalTrader.Site.Controls.AutoSearch.Base)Assembly.GetAssembly(typ).CreateInstance(strFullAssemblyAutoSearchType);
			} catch {
				throw new Exception(string.Format(@"Type '{0}' cannot be found", strFullAssemblyAutoSearchType));
			}
		}

		public static Controls.AutoSearch.Base GetAutoSearch(string strAssembly, int intAutoSearchID) {
			return GetAutoSearch(strAssembly, Site.GetInstance().GetAutoSearch(intAutoSearchID).Name);
		}

	}
}
