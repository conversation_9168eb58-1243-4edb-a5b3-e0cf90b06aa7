<%@ Control Language="C#" CodeBehind="CompanyCertificate.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyCertificate" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>

<ReboundUI_Nugget:DesignBase ID="ctlDB" runat="server" TitleText="Lines" BoxType="Standard" >

	<Links>
		<ReboundUI:IconButton ID="ibtnAdd" runat="server" IconButtonMode="hyperlink" IconGroup="Nugget" IconTitleResource="Add" IconCSSType="Add" />
		<ReboundUI:IconButton ID="ibtnEdit" runat="server" IconButtonMode="hyperlink" IconGroup="Nugget" IconTitleResource="Edit" IconCSSType="Edit" IsInitiallyEnabled="false" />
	</Links>
	
	<Content>
	
		<ReboundUI:FlexiDataTable ID="tbl" runat="server" AllowSelection="true" PanelHeight="150" />
		
	</Content>
	
	<Forms>
		<ReboundForm:CompanyCertificate_Add id="frmAdd" runat="server" />
		<ReboundForm:CompanyCertificate_Edit id="frmEdit" runat="server" />
	</Forms>
	
</ReboundUI_Nugget:DesignBase>
