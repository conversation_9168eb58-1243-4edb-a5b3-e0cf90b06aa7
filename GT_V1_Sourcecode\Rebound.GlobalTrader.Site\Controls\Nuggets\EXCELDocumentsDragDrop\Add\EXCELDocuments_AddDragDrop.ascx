﻿<%@ Control Language="C#" AutoEventWireup="true" CodeBehind="EXCELDocuments_AddDragDrop.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Forms.EXCELDocuments_AddDragDrop" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Form:DesignBase ID="ctlDB" runat="server" ShowRequiredFieldsExplanation="true" ShowQuickHelp="false">
	<Links>
		<ReboundUI:IconButton ID="ibtnSave" runat="server" IconGroup="Nugget" IconTitleResource="Save" IconCSSType="Save" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
		<ReboundUI:IconButton ID="ibtnCancel" runat="server" IconGroup="Nugget" IconTitleResource="Cancel" IconCSSType="Cancel" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
	</Links>
	<Explanation><%=String.Format(Functions.GetGlobalResource("FormExplanations", "EXCELDragDrop_Add"), "6")%></Explanation>
	<Content>
	
	
		<ReboundUI_Table:Form id="frm" runat="server" >
		
			<ReboundUI_Form:FormField id="ctlFile" runat="server" FieldID="lblFileName" ResourceTitle="File" IsRequiredField="true" >
				<Field>
				<asp:Label ID="lblFileName" runat="server"></asp:Label>
				</Field>				
			</ReboundUI_Form:FormField>			
			<ReboundUI_Form:FormField id="ctlCaption" IsRequiredField="true" runat="server" FieldID="txtCaption" ResourceTitle="Caption">
				<Field><ReboundUI:ReboundTextBox ID="txtCaption" TextMode="multiLine" runat="server" Width="400" Rows="2" /></Field>
			</ReboundUI_Form:FormField>
		</ReboundUI_Table:Form>
		
	</Content>
</ReboundUI_Form:DesignBase>