SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE OR ALTER Procedure [dbo].[usp_insert_Offer_APIBOMManager]                     
 @Part    NVARCHAR(30) = NULL,                     
 @ManufacturerNo    INT = NULL,                     
 @ManufacturerName  NVARCHAR(128) = NULL,                     
 @DateCode          NVARCHAR(5) = NULL,                     
 @ProductNo         INT = NULL,                
 @ProductName       NVARCHAR(128) = NULL,                     
 @PackageNo         INT = NULL,                     
 @PackageName       NVARCHAR(128) = NULL,                     
 @Quantity          INT = NULL,                     
 @Price             FLOAT = NULL,                     
 @CurrencyNo        INT = NULL,                     
 @OriginalEntryDate DATETIME = NULL,                     
 --@Salesman          INT = NULL,                     
 @SupplierNo        INT = NULL,                     
 @SupplierName      NVARCHAR(128) = NULL,                     
 @ROHS              TINYINT = NULL,                     
 @OfferStatusNo     INT = NULL,                     
 @Notes             NVARCHAR(128) = NULL,                     
 --@UpdatedBy         INT = NULL,                     
 --@ClientNo          INT = NULL,                     
 @SupplierTotalQSA  NVARCHAR(50) = NULL,                     
 @SupplierLTB       NVARCHAR(50) = NULL,                     
 @SupplierMOQ       NVARCHAR(50) = NULL,                     
 @MSL               NVARCHAR(50) = NULL,                     
 @SPQ               NVARCHAR(50) = NULL,                     
 @LeadTime          NVARCHAR(50) = NULL,                     
 @FactorySealed     NVARCHAR(50) = NULL,                     
 @ROHSStatus        NVARCHAR(50) = NULL,                          
 --@IsPoHUB           BIT=0,                     
 @MSLNo             INT = NULL ,                
 @BOMManagerID      INT = NULL,                
 @ClientID          INT = NULL,               
 @AlternateCustomerRequirementNumber INT = NULL,    
 @APIOfferId        INT output                
AS                     
  BEGIN                     
                    
Declare @Alternatestatus bit    
  Declare @IsOffer int     
if(isnull(@AlternateCustomerRequirementNumber,0)<>0)    
begin     
set @Alternatestatus = 1    
  set @IsOffer =1                    
      INSERT INTO dbo.tbEMSOffers                     
                  (BOMManagerNo,                
       OfferID,                
       fullpart,                     
                   part,                     
                   manufacturerno,              
       ManufacturerName,              
                   datecode,                     
                   productno,                     
                   packageno,                     
                   quantity,                     
                   price,                     
                   currencyno,      
       CurrencyCode,      
                   originalentrydate,                
                   supplierno,                     
                   suppliername,                 
       SupplierType,                
                   rohs,                     
                   notes,                     
                   dlup,                     
                   offerstatusno,                     
                   --offerstatuschangedate,                     
                   --offerstatuschangeloginno,                     
                   --updatedby,                     
                   clientno,                
       ClientId,                
       ClientName,                
       ClientCode,                
                   suppliertotalqsa,                     
                   supplierltb,                     
                   suppliermoq,                     
                   msl,                     
                   spq,                     
                   leadtime,                     
               factorysealed,                     
                   rohsstatus,                     
                   --ispohub,                     
            msllevelno,                
       CustomerRequirementId,          
       SupplierMessage,    
    Alternatestatus,    
    alternateCustomerRequirementNumber,
	POHubCompanyNo)                   
                 
    select @BOMManagerID,                
     1,                
     dbo.Ufn_get_fullpart(@Part),                 
     @Part,                
                    @ManufacturerNo,              
     (select ManufacturerName from tbManufacturer where ManufacturerId = @ManufacturerNo),              
                    @DateCode,                
                    @ProductNo,                     
                    @PackageNo,                     
                    @Quantity,                     
                    @Price,            
                    @CurrencyNo,      
     (Select CurrencyCode from tbCurrency where CurrencyId = @CurrencyNo),      
                    @OriginalEntryDate,                     
                    --@Salesman,                     
                    @SupplierNo,                     
                    (select CompanyName from tbCompany where CompanyId = @SupplierNo),                
     ( select Name from tbCompanytype where CompanyTypeId = (select top 1 TypeNo from tbCompany where CompanyId = @SupplierNo)),                
                    @ROHS,                     
     @Notes,                     
                    CURRENT_TIMESTAMP,                     
                    @OfferStatusNo,                     
                    --CURRENT_TIMESTAMP,                     
                    --@UpdatedBy,                     
                    --@UpdatedBy,                     
                    a.clientno,-- @ClientID,                
      a.clientno, --@ClientID,                
      b.clientName,--(select ClientName from tbClient where ClientId = @ClientID),                
      b.clientcode, --(select ClientCode from tbClient where ClientId = @ClientID),                
      @SupplierTotalQSA,                     
      @SupplierLTB,                     
                    @SupplierMOQ,                     
                    (Select MSLLevel from tbMSLLevel where MSLLevelId = @MSL),                     
                    @SPQ,                     
                    @LeadTime,                     
                    @FactorySealed,                     
                    @ROHSStatus,                     
                    --@IsPoHUB,                     
                    @MSLNo ,                
     a.CustomerRequirementId,          
     '',    
  @Alternatestatus,    
  @AlternateCustomerRequirementNumber  ,
  @SupplierNo
     from tbcustomerrequirement a join tbClient b on a.clientno=b.clientid                
     where bommanagerno = @BOMManagerID --and a.Part = @part      
    and a.CustomerRequirementId = @AlternateCustomerRequirementNumber    
   SET @APIOfferId = Scope_identity()       
end     
else     
begin    
    
    
  set @Alternatestatus = 0    
  set @IsOffer =1                    
      INSERT INTO dbo.tbEMSOffers                     
                  (BOMManagerNo,                
       OfferID,                
       fullpart,                     
                   part,                     
                   manufacturerno,              
       ManufacturerName,              
                   datecode,                     
                   productno,                     
                   packageno,                     
                   quantity,                     
                   price,                     
                   currencyno,      
       CurrencyCode,      
                   originalentrydate,                
                   supplierno,                
                   suppliername,                 
       SupplierType,                
                   rohs,                     
                   notes,                     
                   dlup,                     
                   offerstatusno,                     
                   --offerstatuschangedate,                
                   --offerstatuschangeloginno,                     
                   --updatedby,                     
                   clientno,                
       ClientId,                
       ClientName,                
       ClientCode,                
                   suppliertotalqsa,         
                   supplierltb,                     
                   suppliermoq,                     
                   msl,                     
                   spq,                     
                   leadtime,                     
                   factorysealed,                     
                   rohsstatus,                     
                   --ispohub,                     
                   msllevelno,                
       CustomerRequirementId,          
       SupplierMessage,    
    Alternatestatus,    
    alternateCustomerRequirementNumber,
	POHubCompanyNo)                   
                 
    select @BOMManagerID,                
     1,                
     dbo.Ufn_get_fullpart(@Part),                 
     @Part,                
                    @ManufacturerNo,              
     (select ManufacturerName from tbManufacturer where ManufacturerId = @ManufacturerNo),              
                    @DateCode,                
                    @ProductNo,                     
                    @PackageNo,                     
                    @Quantity,                     
                    @Price,            
                    @CurrencyNo,      
     (Select CurrencyCode from tbCurrency where CurrencyId = @CurrencyNo),      
                    @OriginalEntryDate,                     
                    --@Salesman,                     
                    @SupplierNo,                     
                    (select CompanyName from tbCompany where CompanyId = @SupplierNo),                
     ( select Name from tbCompanytype where CompanyTypeId = (select top 1 TypeNo from tbCompany where CompanyId = @SupplierNo)),                
                    @ROHS,                     
     @Notes,                     
                    CURRENT_TIMESTAMP,                     
                    @OfferStatusNo,                     
                    --CURRENT_TIMESTAMP,                     
                    --@UpdatedBy,                     
                    --@UpdatedBy,                     
                    a.clientno,-- @ClientID,                
      a.clientno, --@ClientID,                
      b.clientName,--(select ClientName from tbClient where ClientId = @ClientID),                
      b.clientcode, --(select ClientCode from tbClient where ClientId = @ClientID),                
      @SupplierTotalQSA,                     
      @SupplierLTB,                     
                    @SupplierMOQ,                     
                    (Select MSLLevel from tbMSLLevel where MSLLevelId = @MSL),                     
                    @SPQ,                     
                    @LeadTime,                     
                    @FactorySealed,                     
                    @ROHSStatus,                     
                    --@IsPoHUB,                     
                    @MSLNo ,                
     a.CustomerRequirementId,          
     '',    
  @Alternatestatus,    
  @AlternateCustomerRequirementNumber ,
  @SupplierNo
     from tbcustomerrequirement a join tbClient b on a.clientno=b.clientid                
     where bommanagerno = @BOMManagerID and a.Part = @part      
    --and a.CustomerRequirementId = @AlternateCustomerRequirementNumber    
   SET @APIOfferId = Scope_identity()       
end    
   
   /*code to insert same offer in BorisGlobalTraderImports.dbo.tbOffer  */
  INSERT INTO BorisGlobalTraderImports.dbo.tbOffer  
 (  SupplierNo  
 ,  Part  
 ,  FullPart  
 ,  DateCode  
 ,  Quantity  
 ,  Price  
 ,  OriginalEntryDate  
 ,  ManufacturerNo  
 ,  CurrencyNo  
 ,  ProductNo  
 ,  PackageNo  
 ,  Notes  
 ,  ManufacturerName  
 ,  ProductName  
 ,  PackageName  
 ,  ClientNo  
 ,  ActionType  
 ,  OfferStatusNo     
 ,  SupplierTotalQSA   
 ,  SupplierLTB   
 ,  SupplierMOQ   
 ,  LeadTime  
 ,  ROHSStatus  
 ,  FactorySealed  
 ,  MSLLevelNo  
 ,  SPQ  
 ,  ROHS  
 ,       RefId 
 ,IsBomManager
   
 )       
 select @SupplierNo,@Part,dbo.Ufn_get_fullpart(@Part),@DateCode,@Quantity,@Price,@OriginalEntryDate,@ManufacturerNo,@CurrencyNo,@ProductNo,@PackageNo,@Notes,
 @ManufacturerName,null,@PackageName,@ClientID,null,null,@SupplierTotalQSA,@SupplierLTB,@SupplierMOQ,@LeadTime,
 @ROHSStatus,@FactorySealed,@MSLNo,@SPQ,@ROHS,null,1
    --from tbcustomerrequirement a join tbClient b on a.clientno=b.clientid                
    -- where bommanagerno = @BOMManagerID and a.Part = @part           
        
   END                          
    

GO


