Type.registerNamespace("Rebound.GlobalTrader.Site.Pages.Setup");Rebound.GlobalTrader.Site.Pages.Setup.Sec_Users=function(n){Rebound.GlobalTrader.Site.Pages.Setup.Sec_Users.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Pages.Setup.Sec_Users.prototype={get_ctlSecurityUsers:function(){return this._ctlSecurityUsers},set_ctlSecurityUsers:function(n){this._ctlSecurityUsers!==n&&(this._ctlSecurityUsers=n)},get_ctlUserProfile:function(){return this._ctlUserProfile},set_ctlUserProfile:function(n){this._ctlUserProfile!==n&&(this._ctlUserProfile=n)},get_ctlSecurityUserGroups:function(){return this._ctlSecurityUserGroups},set_ctlSecurityUserGroups:function(n){this._ctlSecurityUserGroups!==n&&(this._ctlSecurityUserGroups=n)},initialize:function(){Rebound.GlobalTrader.Site.Pages.Setup.Sec_Users.callBaseMethod(this,"initialize")},goInit:function(){this._ctlSecurityUsers&&this._ctlSecurityUsers.addSelectUser(Function.createDelegate(this,this.ctlSecurityGroups_SelectUser));this._ctlUserProfile&&this._ctlUserProfile.addSaveEditComplete(Function.createDelegate(this,this.ctlUserProfile_SaveEditComplete));this._ctlSecurityUserGroups&&this._ctlSecurityUserGroups.addSaveEditComplete(Function.createDelegate(this,this.ctlSecurityUserGroups_SaveEditComplete));Rebound.GlobalTrader.Site.Pages.Setup.Sec_Users.callBaseMethod(this,"goInit")},dispose:function(){this.isDisposed||(this._ctlSecurityUsers&&this._ctlSecurityUsers.dispose(),this._ctlUserProfile&&this._ctlUserProfile.dispose(),this._ctlSecurityUserGroups&&this._ctlSecurityUserGroups.dispose(),this._ctlSecurityUsers=null,this._ctlUserProfile=null,this._ctlSecurityUserGroups=null,Rebound.GlobalTrader.Site.Pages.Setup.Sec_Users.callBaseMethod(this,"dispose"))},ctlSecurityGroups_SelectUser:function(){this._ctlUserProfile._intLoginID=this._ctlSecurityUsers._intLoginID;this._ctlUserProfile.refresh();this._ctlSecurityUserGroups._intLoginID=this._ctlSecurityUsers._intLoginID;this._ctlSecurityUserGroups.refresh();this._ctlSecurityUsers._tbl.resizeColumns();this.showNuggets(!0)},ctlUserProfile_SaveEditComplete:function(){this._ctlSecurityUsers.refresh()},ctlSecurityUserGroups_SaveEditComplete:function(){this._ctlSecurityUsers.refresh()},showNuggets:function(n){this._ctlUserProfile.show(n);this._ctlSecurityUserGroups.show(n)}};Rebound.GlobalTrader.Site.Pages.Setup.Sec_Users.registerClass("Rebound.GlobalTrader.Site.Pages.Setup.Sec_Users",Rebound.GlobalTrader.Site.Pages.Content,Sys.IDisposable);