using System;
using System.Collections.Generic;
using System.Text;

namespace Rebound.GlobalTrader.Site {
	public struct SitePage {

		public int ID;
		public string Name;
		/// <summary>
		/// Full absolute URL from the root of the site "~"
		/// </summary>
		public string Url;
		/// <summary>
		/// URL without "~"
		/// </summary>
		public string RelativeUrl;

		public SitePage(int intID, string strName, string strUrl) {
			ID = intID;
			Name = strName;
			RelativeUrl = strUrl;
			Url = string.Format("~/{0}", strUrl);
		}

	}

}
