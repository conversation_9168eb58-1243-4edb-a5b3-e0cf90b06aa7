﻿GO
/*===========================================================================================  
TASK			UPDATED BY       DATE				ACTION		DESCRIPTION  
[US-202901]     Phuc Hoang		 11-Sep-2024		CREATE		German Invoice Part 1-Implement monthly Germany exchange rates published by tax authorities
===========================================================================================  
*/

CREATE OR ALTER PROC usp_Get_GermanyExchangeRate (
	@CurrencyDate DATETIME = NULL
)
AS

BEGIN
	SELECT * FROM dbo.tbGermanyExchangeRate 
	WHERE YEAR(@CurrencyDate) = YEAR(DLUP)
	--AND WHERE MONTH(@CurrencyDate) = MONTH(DLUP)
	AND ExchangeRateId in (SELECT MAX(ExchangeRateId) FROM dbo.tbGermanyExchangeRate GROUP BY CountryName)
END

GO

