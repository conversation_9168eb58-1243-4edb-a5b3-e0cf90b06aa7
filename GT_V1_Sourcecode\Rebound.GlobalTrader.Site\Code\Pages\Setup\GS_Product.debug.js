///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Pages.Setup");

Rebound.GlobalTrader.Site.Pages.Setup.GS_Product = function (el) {
    Rebound.GlobalTrader.Site.Pages.Setup.GS_Product.initializeBase(this, [el]);
};

Rebound.GlobalTrader.Site.Pages.Setup.GS_Product.prototype = {

	get_ctlProduct: function() { return this._ctlProduct; }, 	set_ctlProduct: function(v) { if (this._ctlProduct !== v)  this._ctlProduct = v; }, 
	get_ctlRateHistory: function () { return this._ctlRateHistory; }, set_ctlRateHistory: function (v) { if (this._ctlRateHistory !== v) this._ctlRateHistory = v; },
	get_ctlProductName: function () { return this._ctlProductName; }, set_ctlProductName: function (v) { if (this._ctlProductName !== v) this._ctlProductName = v; },
	get_ctlGlobalProductMainCategory: function () { return this._ctlGlobalProductMainCategory; }, set_ctlGlobalProductMainCategory: function (v) { if (this._ctlGlobalProductMainCategory !== v) this._ctlGlobalProductMainCategory = v; },

	initialize: function() {
	    Rebound.GlobalTrader.Site.Pages.Setup.GS_Product.callBaseMethod(this, "initialize");
	},
	
	goInit: function () {
	    //alert(this._ctlProduct._ibtnSearch);
	   	this._ctlProduct.addSelectProduct(Function.createDelegate(this, this.ctlProduct_SelectProduct));		

	   	this._ctlProductName.addSelectProduct(Function.createDelegate(this, this.ctlProduct_SelectProductName));
		this._ctlProductName.addSearchClick(Function.createDelegate(this, this.ctlProduct_Search));

		this._ctlGlobalProductMainCategory.addSelectProduct(Function.createDelegate(this, this.ctlGlobalProductMainCategory_SelectProductName));
		this._ctlGlobalProductMainCategory.addSearchClick(Function.createDelegate(this, this.ctlGlobalProductMainCategory_Search));
	   	
		Rebound.GlobalTrader.Site.Pages.Setup.GS_Product.callBaseMethod(this, "goInit");
	},

	dispose: function() { 
		if (this.isDisposed) return;
		if (this._ctlProduct) this._ctlProduct.dispose();
		if (this._ctlRateHistory) this._ctlRateHistory.dispose();
		if (this._ctlProductName) this._ctlProductName.dispose();
		this._ctlProduct = null;
		this._ctlRateHistory = null;
		this._ctlProductName = null;
		Rebound.GlobalTrader.Site.Pages.Setup.GS_Product.callBaseMethod(this, "dispose");
	},
	
	ctlProduct_SelectProduct: function () {	    
		this._ctlRateHistory._intProductID = this._ctlProduct._intProductID;
		this._ctlRateHistory.show(true);
		this._ctlProduct._tbl.resizeColumns();
		this._ctlRateHistory.refresh();
	},

	ctlProduct_SelectProductName: function () {
	    
		this._ctlProduct._intProductNameID = this._ctlProductName._intProductNameEditID;
	    this._ctlProduct.show(true);
	    this._ctlRateHistory.show(false);
	    this._ctlProductName._tbl.resizeColumns();
	    this._ctlProduct.refresh();
	},
	ctlProduct_Search: function () {
	    
	    this._ctlProductName.ctlProduct = this._ctlProduct;
	    this._ctlProductName.getData();
	    this._ctlProduct.show(false);
	    
	},
	ctlGlobalProductMainCategory_SelectProductName: function () {

		this._ctlProductName._intProductNameID = this._ctlGlobalProductMainCategory._intProductNameID;
		this._ctlProductName._stringProductType = this._ctlGlobalProductMainCategory._stringProductType;
		this._ctlGlobalProductMainCategory.show(true);
		this._ctlProductName.show(true);
		this._ctlRateHistory.show(false);
		this._ctlProduct.show(false);
		this._ctlGlobalProductMainCategory._tbl.resizeColumns();
		this._ctlProductName.refresh();
	},
	ctlGlobalProductMainCategory_Search: function () {

		//this._ctlGlobalProductMainCategory.ctlProduct = this._ctlProduct;
		this._ctlGlobalProductMainCategory.getData();
		this._ctlGlobalProductMainCategory.show(true);

	}
};

Rebound.GlobalTrader.Site.Pages.Setup.GS_Product.registerClass("Rebound.GlobalTrader.Site.Pages.Setup.GS_Product", Rebound.GlobalTrader.Site.Pages.Content, Sys.IDisposable);
