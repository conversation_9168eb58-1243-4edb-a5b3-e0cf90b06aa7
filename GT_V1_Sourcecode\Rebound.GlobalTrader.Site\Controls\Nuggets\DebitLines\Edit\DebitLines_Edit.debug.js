///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.DebitLines_Edit = function(element) { 
	Rebound.GlobalTrader.Site.Controls.Forms.DebitLines_Edit.initializeBase(this, [element]);
	this._intLineID = -1;
	this._blnLineIsService = false;
	this._blnProductHaza = false;
};

Rebound.GlobalTrader.Site.Controls.Forms.DebitLines_Edit.prototype = {

	get_lblCurrency: function() { return this._lblCurrency; }, set_lblCurrency: function(v) { if (this._lblCurrency !== v)  this._lblCurrency = v; }, 

	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.Forms.DebitLines_Edit.callBaseMethod(this, "initialize");
		this.addShown(Function.createDelegate(this, this.formShown));
	},

	formShown: function() {
		if (this._blnFirstTimeShown) {
			this.addSave(Function.createDelegate(this, this.saveClicked));
		}
		this.showField("ctlPartNo", !this._blnLineIsService);
		this.showField("ctlManufacturer", !this._blnLineIsService);
		this.showField("ctlDateCode", !this._blnLineIsService);
		this.showField("ctlPackage", !this._blnLineIsService);
		this.showField("ctlProduct", !this._blnLineIsService);
		this.showField("ctlService", this._blnLineIsService);
		this.showField("ctlServiceDescription", this._blnLineIsService);
		this.enableFieldCheckBox("ctlPrintHazWar", this._blnProductHaza);
	},

	dispose: function() { 
		if (this.isDisposed) return;
		this._lblCurrency = null;
		this._intLineID = null;
		this._blnLineIsService = null;
		this._blnProductHaza = null;
		Rebound.GlobalTrader.Site.Controls.Forms.DebitLines_Edit.callBaseMethod(this, "dispose");
	},

	saveClicked: function() {
		if (!this.validateForm()) return;
		var obj = new Rebound.GlobalTrader.Site.Data();
		obj.set_PathToData("controls/Nuggets/DebitLines");
		obj.set_DataObject("DebitLines");
		obj.set_DataAction("SaveEdit");
		obj.addParameter("id", this._intLineID);
		obj.addParameter("Quantity", this.getFieldValue("ctlQuantity"));
		obj.addParameter("Price", this.getFieldValue("ctlPrice"));
		obj.addParameter("LineIsService", this._blnLineIsService);
		obj.addParameter("LineNotes", this.getFieldValue("ctlLineNotes"));
		if (this._blnLineIsService) {
			obj.addParameter("Service", this.getFieldValue("ctlService"));
			obj.addParameter("ServiceDescription", this.getFieldValue("ctlServiceDescription"));
		}
		obj.addParameter("PrintHazWar", this.getFieldValue("ctlPrintHazWar"));
		obj.addDataOK(Function.createDelegate(this, this.saveEditComplete));
		obj.addError(Function.createDelegate(this, this.saveEditError));
		obj.addTimeout(Function.createDelegate(this, this.saveEditError));
		$R_DQ.addToQueue(obj);
		$R_DQ.processQueue();
		obj = null;
	},

	saveEditError: function(args) {
		this._strErrorMessage = args._errorMessage;
		this.onSaveError();
	},
	
	saveEditComplete: function(args) {
		if (args._result.Result == true) {
			this.onSaveComplete();
		} else {
			this._strErrorMessage = args._errorMessage;
			this.onSaveError();
		}
	},	

	validateForm: function() {
		this.onValidate();
		var blnOK = true;
		if (this._blnLineIsService) {
			if (!this.checkFieldEntered("ctlService")) blnOK = false;
		}
		if (!this.checkFieldEntered("ctlQuantity")) blnOK = false;
		if (!this.checkFieldNumeric("ctlQuantity")) blnOK = false;
		if (!this.checkFieldEntered("ctlPrice")) blnOK = false;
		if (!this.checkFieldNumeric("ctlPrice")) blnOK = false;
		if (!blnOK) this.showError(true);
		return blnOK;
	},
	
	setCurrency: function(strCode) {
		$R_FN.setInnerHTML(this._lblCurrency, strCode);
	}

};

Rebound.GlobalTrader.Site.Controls.Forms.DebitLines_Edit.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.DebitLines_Edit", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
