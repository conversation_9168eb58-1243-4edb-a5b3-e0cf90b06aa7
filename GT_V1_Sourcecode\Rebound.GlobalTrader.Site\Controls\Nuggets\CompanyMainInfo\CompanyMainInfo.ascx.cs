//Marker     changed by      date         Remarks
//[001]      Vinay          24/03/2014     ESMS Ref:106 -  Add new field(EARI Member and EARI Reported) to Compnay Form  
using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;
using System.Text;
using Rebound.GlobalTrader.BLL;
using Rebound.GlobalTrader.Site.Controls;


namespace Rebound.GlobalTrader.Site.Controls.Nuggets
{
    public partial class CompanyMainInfo : Base
    {

        #region Locals

        protected IconButton _ibtnEdit;
        protected IconButton _ibtnContacts;
        protected FlexiDataTable _tblContacts;
        protected IconButton _ibtnCreditLimit;
        protected IconButton _ibtnActive;
        protected IconButton _ibtnInactive;
        protected Panel _pnlCreditLimitToolTip;
        protected Panel _pnlCreditLimit;
        protected HyperLink _hypNewCreditLimit;

        #endregion

        #region Properties

        private int _intCompanyID = -1;
        public int CompanyID
        {
            get { return _intCompanyID; }
            set { _intCompanyID = value; }
        }

        private int _intContactID = -1;
        public int ContactID
        {
            get { return _intContactID; }
            set { _intContactID = value; }
        }

        private Controls.DataListNuggets.CompanyListType _enmCompanyListType;
        public Controls.DataListNuggets.CompanyListType CompanyListType
        {
            get { return _enmCompanyListType; }
            set { _enmCompanyListType = value; }
        }

        private bool _blnCanEdit = true;
        public bool CanEdit
        {
            get { return _blnCanEdit; }
            set { _blnCanEdit = value; }
        }

        private bool _blnCanEditCompanyType = false;
        public bool CanEditCompanyType
        {
            get { return _blnCanEditCompanyType; }
            set { _blnCanEditCompanyType = value; }
        }

        private bool _blnCanEditPremierCustomer = false;
        public bool CanEditisPremierCustomer
        {
            get { return _blnCanEditPremierCustomer; }
            set { _blnCanEditPremierCustomer = value; }
        }
        private bool _blnCanEditTier2PremierCustomer = false;
        public bool CanEditisTier2PremierCustomer
        {
            get { return _blnCanEditTier2PremierCustomer; }
            set { _blnCanEditTier2PremierCustomer = value; }
        }
        private bool _blnCanEditisPurchasingNotes = false;
        public bool CanEditisPurchasingNotes
        {
            get { return _blnCanEditisPurchasingNotes; }
            set { _blnCanEditisPurchasingNotes = value; }
        }
        
        //[001] code start
        private bool _blnCanEditAccountNotes = true;
        public bool CanEditAccountNotes
        {
            get { return _blnCanEditAccountNotes; }
            set { _blnCanEditAccountNotes = value; }
        }
        //[001] code end
        private bool _IsGSAEditPermission = true;
        public bool IsGSAEditPermission
        {
            get { return _IsGSAEditPermission; }
            set { _IsGSAEditPermission = value; }
        }
        private bool _IsDiffrentClient = false;
        public bool IsDiffrentClient
        {
            get { return _IsDiffrentClient; }
            set { _IsDiffrentClient = value; }
        }
        private bool _IsGSA = false;
        public bool IsGSA
        {
            get { return _IsGSA; }
            set { _IsGSA = value; }
        }
        private bool _CanAddCAF = false;
        public bool CanAddCAF
        {
            get { return _CanAddCAF; }
            set { _CanAddCAF = value; }
        }

        private bool _AllowActiveCompany = false;
        public bool AllowActiveCompany
        {
            get { return _AllowActiveCompany; }
            set { _AllowActiveCompany = value; }
        }

        #endregion

        #region Overrides

        /// <summary>
        /// Oninit
        /// </summary>
        /// <param name="e"></param>
        protected override void OnInit(EventArgs e)
        {
            base.OnInit(e);
            WireUpControls();
            AddScriptReference("Controls.Nuggets.CompanyMainInfo.CompanyMainInfo.js");
            TitleText = Functions.GetGlobalResource("Nuggets", "CompanyMainInfo");
            SetupContactsTable();
        }

        protected override void OnLoad(EventArgs e)
        {
            if (_intCompanyID < 1) _intCompanyID = _objQSManager.CompanyID;
            if (_intContactID < 1) _intContactID = _objQSManager.ContactID;
            SetupScriptDescriptors();
            _tblContacts.IsInitiallyVisible = !_blnHasInitialData;
            base.OnLoad(e);
        }

        protected override void OnPreRender(EventArgs e)
        {
            _ibtnEdit.Visible = _blnCanEdit;
            _ibtnActive.Visible = _AllowActiveCompany;
            _ibtnInactive.Visible = _AllowActiveCompany;
            if (SessionManager.IsGlobalUser != true)
            {
                if (_IsDiffrentClient == true)
                {
                    if (SessionManager.IsGSA == true)
                    {
                        if (_IsGSAEditPermission == true)
                        {
                            _ibtnEdit.Visible = true;
                            _ibtnContacts.Visible = true;
                        }
                        else
                        {
                            _ibtnEdit.Visible = false;
                            _ibtnContacts.Visible = false;
                        }
                    }
                }
            }
            _hypNewCreditLimit.Visible = _CanAddCAF;
            base.OnPreRender(e);
        }

        #endregion

        /// <summary>
        /// sets up the contact table headings
        /// </summary>
        private void SetupContactsTable()
        {
            _tblContacts.Columns.Add(new FlexiDataColumn("ContactName", WidthManager.GetWidth(WidthManager.ColumnWidth.LoginName)));
            _tblContacts.Columns.Add(new FlexiDataColumn("Tel", WidthManager.GetWidth(WidthManager.ColumnWidth.TelNo)));
            _tblContacts.Columns.Add(new FlexiDataColumn("Email"));
        }

        /// <summary>
        /// Sets up AJAX script descriptors
        /// </summary>
        private void SetupScriptDescriptors()
        {
            if (SessionManager.IsGlobalUser != true)
            {
                _IsGSA = (bool)SessionManager.IsGSA;
            }
            else
            {
                _IsGSA = false;
            }
            _scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyMainInfo", ctlDesignBase.ClientID);
            if (_blnCanEdit) _scScriptControlDescriptor.AddElementProperty("ibtnEdit", _ibtnEdit.ClientID);
            _scScriptControlDescriptor.AddElementProperty("ibtnContacts", _ibtnContacts.ClientID);
            _scScriptControlDescriptor.AddProperty("intCompanyID", _intCompanyID);
            _scScriptControlDescriptor.AddProperty("intContactID", _intContactID);
            _scScriptControlDescriptor.AddProperty("enmCompanyListType", _enmCompanyListType);
            _scScriptControlDescriptor.AddProperty("blnCanEditCompanyType", _blnCanEditCompanyType);
            _scScriptControlDescriptor.AddProperty("blnCanEditPremierCustomer", _blnCanEditPremierCustomer);
            _scScriptControlDescriptor.AddProperty("blnCanEditTier2PremierCustomer", _blnCanEditTier2PremierCustomer);
            if (_blnCanEditisPurchasingNotes) _scScriptControlDescriptor.AddProperty("blnCanEditisPurchasingNotes", _blnCanEditisPurchasingNotes);
            




            _scScriptControlDescriptor.AddComponentProperty("tblContacts", _tblContacts.ClientID);
            //[001] code start
            _scScriptControlDescriptor.AddProperty("blnCanEditAccountNotes", _blnCanEditAccountNotes);
            //[001] code end

            _scScriptControlDescriptor.AddProperty("IsDiffrentClient", _IsDiffrentClient);
            _scScriptControlDescriptor.AddProperty("IsGSAEditPermission", _IsGSAEditPermission);
            _scScriptControlDescriptor.AddProperty("IsGSA", _IsGSA);
            _scScriptControlDescriptor.AddElementProperty("ibtnCreditLimit", _ibtnCreditLimit.ClientID);
            _scScriptControlDescriptor.AddElementProperty("pnlCreditLimitToolTip", _pnlCreditLimitToolTip.ClientID);
            _scScriptControlDescriptor.AddElementProperty("hypNewCreditLimit", _hypNewCreditLimit.ClientID);
            _scScriptControlDescriptor.AddElementProperty("pnlCreditLimit", _pnlCreditLimit.ClientID);
            _scScriptControlDescriptor.AddProperty("AllowActiveCompany", _AllowActiveCompany);
            _scScriptControlDescriptor.AddElementProperty("ibtnActive", _ibtnActive.ClientID);
            _scScriptControlDescriptor.AddElementProperty("ibtnInactive", _ibtnInactive.ClientID);
        }

        /// <summary>
        /// Wire up controls from the ascx
        /// </summary>
        private void WireUpControls()
        {
            _ibtnEdit = FindIconButton("ibtnEdit");
            _ibtnContacts = FindIconButton("ibtnContacts");
            _tblContacts = (FlexiDataTable)ctlDesignBase.FindContentControl("tblContacts");
            _ibtnCreditLimit = FindIconButton("ibtnCreditLimit");
            _ibtnActive = FindIconButton("ibtnActive");
            _ibtnInactive = FindIconButton("ibtnInactive");
            _pnlCreditLimitToolTip = (Panel)Functions.FindControlRecursive(this, "pnlCreditLimitToolTip");
            _hypNewCreditLimit = (HyperLink)Functions.FindControlRecursive(this, "hypNewCreditLimit");
            _pnlCreditLimit = (Panel)Functions.FindControlRecursive(this, "pnlCreditLimit");
        }


    }
}
