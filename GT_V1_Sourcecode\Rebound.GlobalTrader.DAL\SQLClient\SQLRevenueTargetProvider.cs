﻿using System;
using System.Data;
using System.Data.SqlClient;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;
using System.Web.Caching;
using System.Data.Common;


namespace Rebound.GlobalTrader.DAL.SqlClient
{
	public class SqlRevenueTargetProvider : RevenueTargetProvider
	{


		/// <summary>
		/// GetDivisionAndTeamTarget 
		/// Calls [KPI_GetDivisionRevenueTargetByID]
		/// </summary>
		public override List<RevenueTargetDetails> GetDivisionRevenueByID(System.Int32? divisionNo, System.Int32 yearNo)
		{
			SqlConnection cn = null;
			SqlCommand cmd = null;
			try
			{
				cn = new SqlConnection(this.ConnectionString);
				cmd = new SqlCommand("KPI_GetDivisionRevenueTargetByID", cn);
				cmd.CommandType = CommandType.StoredProcedure;
				cmd.CommandTimeout = 30;
				cmd.Parameters.Add("@DivisionNo", SqlDbType.Int).Value = divisionNo;
				cmd.Parameters.Add("@yearNo", SqlDbType.Int).Value = yearNo;
				cn.Open();
				DbDataReader reader = ExecuteReader(cmd);
				List<RevenueTargetDetails> lst = new List<RevenueTargetDetails>();
				while (reader.Read())
				{
					RevenueTargetDetails obj = new RevenueTargetDetails();
					obj.SectionId = GetReaderValue_Int32(reader, "SectionId", 0);
					obj.SectionName = GetReaderValue_String(reader, "SectionName", "");
					obj.SectionType = GetReaderValue_String(reader, "SectionType", "");
					obj.JanTarget = GetReaderValue_NullableDouble(reader, "JanTarget", null);
					obj.FebTarget = GetReaderValue_NullableDouble(reader, "FebTarget", null);
					obj.MarchTarget = GetReaderValue_NullableDouble(reader, "MarchTarget", null);
					obj.AprTarget = GetReaderValue_NullableDouble(reader, "AprTarget", null);
					obj.MayTarget = GetReaderValue_NullableDouble(reader, "MayTarget", null);
					obj.JuneTarget = GetReaderValue_NullableDouble(reader, "JuneTarget", null);
					obj.JulyTarget = GetReaderValue_NullableDouble(reader, "JulyTarget", null);
					obj.AugTarget = GetReaderValue_NullableDouble(reader, "AugTarget", null);
					obj.SepTarget = GetReaderValue_NullableDouble(reader, "SepTarget", null);
					obj.OctTarget = GetReaderValue_NullableDouble(reader, "OctTarget", null);
					obj.NovTarget = GetReaderValue_NullableDouble(reader, "NovTarget", null);
					obj.DecTarget = GetReaderValue_NullableDouble(reader, "DecTarget", null);
					obj.JanRevenue = GetReaderValue_NullableDouble(reader, "JanRevenue", null);
					obj.FebRevenue = GetReaderValue_NullableDouble(reader, "FebRevenue", null);
					obj.MarchRevenue = GetReaderValue_NullableDouble(reader, "MarchRevenue", null);
					obj.AprRevenue = GetReaderValue_NullableDouble(reader, "AprRevenue", null);
					obj.MayRevenue = GetReaderValue_NullableDouble(reader, "MayRevenue", null);
					obj.JuneRevenue = GetReaderValue_NullableDouble(reader, "JuneRevenue", null);
					obj.JulyRevenue = GetReaderValue_NullableDouble(reader, "JulyRevenue", null);
					obj.AugRevenue = GetReaderValue_NullableDouble(reader, "AugRevenue", null);
					obj.SepRevenue = GetReaderValue_NullableDouble(reader, "SepRevenue", null);
					obj.OctRevenue = GetReaderValue_NullableDouble(reader, "OctRevenue", null);
					obj.NovRevenue = GetReaderValue_NullableDouble(reader, "NovRevenue", null);
					obj.DecRevenue = GetReaderValue_NullableDouble(reader, "DecRevenue", null);

					obj.JanGrossProfit = GetReaderValue_NullableDouble(reader, "JanGrossProfit", null);
					obj.FebGrossProfit = GetReaderValue_NullableDouble(reader, "FebGrossProfit", null);
					obj.MarchGrossProfit = GetReaderValue_NullableDouble(reader, "MarchGrossProfit", null);
					obj.AprGrossProfit = GetReaderValue_NullableDouble(reader, "AprGrossProfit", null);
					obj.MayGrossProfit = GetReaderValue_NullableDouble(reader, "MayGrossProfit", null);
					obj.JuneGrossProfit = GetReaderValue_NullableDouble(reader, "JuneGrossProfit", null);
					obj.JulyGrossProfit = GetReaderValue_NullableDouble(reader, "JulyGrossProfit", null);
					obj.AugGrossProfit = GetReaderValue_NullableDouble(reader, "AugGrossProfit", null);
					obj.SepGrossProfit = GetReaderValue_NullableDouble(reader, "SepGrossProfit", null);
					obj.OctGrossProfit = GetReaderValue_NullableDouble(reader, "OctGrossProfit", null);
					obj.NovGrossProfit = GetReaderValue_NullableDouble(reader, "NovGrossProfit", null);
					obj.DecGrossProfit = GetReaderValue_NullableDouble(reader, "DecGrossProfit", null);

					//obj.TotalTarget = GetReaderValue_NullableDouble(reader, "TotalTarget,", null);
					obj.AllocatedPer = GetReaderValue_NullableDouble(reader, "AllocatedPer", null);
					//obj.TotalRevenue = GetReaderValue_NullableDouble(reader, "TotalRevenue", null);
					obj.RevenuePer = GetReaderValue_NullableDouble(reader, "RevenuePer", null);
					obj.RowId = GetReaderValue_Int32(reader, "RowId", 0);
					//obj.TeamName = GetReaderValue_String(reader, "TeamName", "");
					//obj.SalesmanName = GetReaderValue_String(reader, "SalesmanName", "");
					//obj.CustomerName = GetReaderValue_String(reader, "CustomerName", "");
					lst.Add(obj);
					obj = null;
				}
				return lst;
			}
			catch (SqlException sqlex)
			{
				//LogException(sqlex);
				throw new Exception("Failed to get Revenue and Target", sqlex);
			}
			finally
			{
				cmd.Dispose();
				cn.Close();
				cn.Dispose();
			}
		}
		//public override List<RevenueTargetDetails> KPI_GETDivisionTop3Rows(System.Int32? divisionNo, System.Int32 yearNo)
		//{
		//	SqlConnection cn = null;
		//	SqlCommand cmd = null;
		//	try
		//	{
		//		cn = new SqlConnection(this.ConnectionString);
		//		cmd = new SqlCommand("KPI_GETDivisionTop3Rows", cn);
		//		cmd.CommandType = CommandType.StoredProcedure;
		//		cmd.CommandTimeout = 30;
		//		cmd.Parameters.Add("@DivisionNo", SqlDbType.Int).Value = divisionNo;
		//		cmd.Parameters.Add("@yearNo", SqlDbType.Int).Value = yearNo;
		//		cn.Open();
		//		DbDataReader reader = ExecuteReader(cmd);
		//		List<RevenueTargetDetails> lst = new List<RevenueTargetDetails>();
		//		while (reader.Read())
		//		{
		//			RevenueTargetDetails obj = new RevenueTargetDetails();
		//			obj.SectionId = GetReaderValue_Int32(reader, "SectionId", 0);
		//			obj.SectionName = GetReaderValue_String(reader, "SectionName", "");
		//			obj.SectionType = GetReaderValue_String(reader, "SectionType", "");
		//			obj.JanTarget = GetReaderValue_NullableDouble(reader, "JanTarget", null);
		//			obj.FebTarget = GetReaderValue_NullableDouble(reader, "FebTarget", null);
		//			obj.MarchTarget = GetReaderValue_NullableDouble(reader, "MarchTarget", null);
		//			obj.AprTarget = GetReaderValue_NullableDouble(reader, "AprTarget", null);
		//			obj.MayTarget = GetReaderValue_NullableDouble(reader, "MayTarget", null);
		//			obj.JuneTarget = GetReaderValue_NullableDouble(reader, "JuneTarget", null);
		//			obj.JulyTarget = GetReaderValue_NullableDouble(reader, "JulyTarget", null);
		//			obj.AugTarget = GetReaderValue_NullableDouble(reader, "AugTarget", null);
		//			obj.SepTarget = GetReaderValue_NullableDouble(reader, "SepTarget", null);
		//			obj.OctTarget = GetReaderValue_NullableDouble(reader, "OctTarget", null);
		//			obj.NovTarget = GetReaderValue_NullableDouble(reader, "NovTarget", null);
		//			obj.DecTarget = GetReaderValue_NullableDouble(reader, "DecTarget", null);
		//			obj.JanRevenue = GetReaderValue_NullableDouble(reader, "JanRevenue", null);
		//			obj.FebRevenue = GetReaderValue_NullableDouble(reader, "FebRevenue", null);
		//			obj.MarchRevenue = GetReaderValue_NullableDouble(reader, "MarchRevenue", null);
		//			obj.AprRevenue = GetReaderValue_NullableDouble(reader, "AprRevenue", null);
		//			obj.MayRevenue = GetReaderValue_NullableDouble(reader, "MayRevenue", null);
		//			obj.JuneRevenue = GetReaderValue_NullableDouble(reader, "JuneRevenue", null);
		//			obj.JulyRevenue = GetReaderValue_NullableDouble(reader, "JulyRevenue", null);
		//			obj.AugRevenue = GetReaderValue_NullableDouble(reader, "AugRevenue", null);
		//			obj.SepRevenue = GetReaderValue_NullableDouble(reader, "SepRevenue", null);
		//			obj.OctRevenue = GetReaderValue_NullableDouble(reader, "OctRevenue", null);
		//			obj.NovRevenue = GetReaderValue_NullableDouble(reader, "NovRevenue", null);
		//			obj.DecRevenue = GetReaderValue_NullableDouble(reader, "DecRevenue", null);
		//			//obj.TotalTarget = GetReaderValue_NullableDouble(reader, "TotalTarget,", null);
		//			obj.AllocatedPer = GetReaderValue_NullableDouble(reader, "AllocatedPer", null);
		//			//obj.TotalRevenue = GetReaderValue_NullableDouble(reader, "TotalRevenue", null);
		//			obj.RevenuePer = GetReaderValue_NullableDouble(reader, "RevenuePer", null);
		//			obj.RowId = GetReaderValue_Int32(reader, "RowId", 0);
		//			obj.TeamName = GetReaderValue_String(reader, "TeamName", "");
		//			obj.SalesmanName = GetReaderValue_String(reader, "SalesmanName", "");
		//			obj.CustomerName = GetReaderValue_String(reader, "CustomerName", "");
		//			lst.Add(obj);
		//			obj = null;
		//		}
		//		return lst;
		//	}
		//	catch (SqlException sqlex)
		//	{
		//		//LogException(sqlex);
		//		throw new Exception("Failed to get Revenue and Target", sqlex);
		//	}
		//	finally
		//	{
		//		cmd.Dispose();
		//		cn.Close();
		//		cn.Dispose();
		//	}
		//}

		/// <summary>
		/// KPI_GetSalesRevenueTargetByID
		/// </summary>
		/// <param name="teamTargetNo"></param>
		/// <param name="yearNo"></param>
		/// <returns></returns>
		public override List<RevenueTargetDetails> GetSalesRevenueByID(System.Int32? teamTargetNo, System.Int32 yearNo)
		{
			SqlConnection cn = null;
			SqlCommand cmd = null;
			try
			{
				cn = new SqlConnection(this.ConnectionString);
				cmd = new SqlCommand("KPI_GetSalesRevenueTargetByID", cn);
				cmd.CommandType = CommandType.StoredProcedure;
				cmd.CommandTimeout = 120;
				cmd.Parameters.Add("@TeamTargetNo", SqlDbType.Int).Value = teamTargetNo;
				cmd.Parameters.Add("@yearNo", SqlDbType.Int).Value = yearNo;
				cn.Open();
				DbDataReader reader = ExecuteReader(cmd);
				List<RevenueTargetDetails> lst = new List<RevenueTargetDetails>();
				while (reader.Read())
				{
					RevenueTargetDetails obj = new RevenueTargetDetails();
					obj.SectionId = GetReaderValue_Int32(reader, "SectionId", 0);
					obj.SectionName = GetReaderValue_String(reader, "SectionName", "");
					obj.SectionType = GetReaderValue_String(reader, "SectionType", "");
					obj.JanTarget = GetReaderValue_NullableDouble(reader, "JanTarget", null);
					obj.FebTarget = GetReaderValue_NullableDouble(reader, "FebTarget", null);
					obj.MarchTarget = GetReaderValue_NullableDouble(reader, "MarchTarget", null);
					obj.AprTarget = GetReaderValue_NullableDouble(reader, "AprTarget", null);
					obj.MayTarget = GetReaderValue_NullableDouble(reader, "MayTarget", null);
					obj.JuneTarget = GetReaderValue_NullableDouble(reader, "JuneTarget", null);
					obj.JulyTarget = GetReaderValue_NullableDouble(reader, "JulyTarget", null);
					obj.AugTarget = GetReaderValue_NullableDouble(reader, "AugTarget", null);
					obj.SepTarget = GetReaderValue_NullableDouble(reader, "SepTarget", null);
					obj.OctTarget = GetReaderValue_NullableDouble(reader, "OctTarget", null);
					obj.NovTarget = GetReaderValue_NullableDouble(reader, "NovTarget", null);
					obj.DecTarget = GetReaderValue_NullableDouble(reader, "DecTarget", null);
					obj.JanRevenue = GetReaderValue_NullableDouble(reader, "JanRevenue", null);
					obj.FebRevenue = GetReaderValue_NullableDouble(reader, "FebRevenue", null);
					obj.MarchRevenue = GetReaderValue_NullableDouble(reader, "MarchRevenue", null);
					obj.AprRevenue = GetReaderValue_NullableDouble(reader, "AprRevenue", null);
					obj.MayRevenue = GetReaderValue_NullableDouble(reader, "MayRevenue", null);
					obj.JuneRevenue = GetReaderValue_NullableDouble(reader, "JuneRevenue", null);
					obj.JulyRevenue = GetReaderValue_NullableDouble(reader, "JulyRevenue", null);
					obj.AugRevenue = GetReaderValue_NullableDouble(reader, "AugRevenue", null);
					obj.SepRevenue = GetReaderValue_NullableDouble(reader, "SepRevenue", null);
					obj.OctRevenue = GetReaderValue_NullableDouble(reader, "OctRevenue", null);
					obj.NovRevenue = GetReaderValue_NullableDouble(reader, "NovRevenue", null);
					obj.DecRevenue = GetReaderValue_NullableDouble(reader, "DecRevenue", null);

					obj.JanGrossProfit = GetReaderValue_NullableDouble(reader, "JanGrossProfit", null);
					obj.FebGrossProfit = GetReaderValue_NullableDouble(reader, "FebGrossProfit", null);
					obj.MarchGrossProfit = GetReaderValue_NullableDouble(reader, "MarchGrossProfit", null);
					obj.AprGrossProfit = GetReaderValue_NullableDouble(reader, "AprGrossProfit", null);
					obj.MayGrossProfit = GetReaderValue_NullableDouble(reader, "MayGrossProfit", null);
					obj.JuneGrossProfit = GetReaderValue_NullableDouble(reader, "JuneGrossProfit", null);
					obj.JulyGrossProfit = GetReaderValue_NullableDouble(reader, "JulyGrossProfit", null);
					obj.AugGrossProfit = GetReaderValue_NullableDouble(reader, "AugGrossProfit", null);
					obj.SepGrossProfit = GetReaderValue_NullableDouble(reader, "SepGrossProfit", null);
					obj.OctGrossProfit = GetReaderValue_NullableDouble(reader, "OctGrossProfit", null);
					obj.NovGrossProfit = GetReaderValue_NullableDouble(reader, "NovGrossProfit", null);
					obj.DecGrossProfit = GetReaderValue_NullableDouble(reader, "DecGrossProfit", null);

					//obj.TotalTarget = GetReaderValue_NullableDouble(reader, "TotalTarget,", null);
					//obj.AllocatedPer = GetReaderValue_NullableDouble(reader, "AllocatedPer", null);
					//obj.TotalRevenue = GetReaderValue_NullableDouble(reader, "TotalRevenue", null);
					//obj.RevenuePer = GetReaderValue_NullableDouble(reader, "RevenuePer", null);
					obj.RowId = GetReaderValue_Int32(reader, "RowId", 0);
					//obj.TeamName = GetReaderValue_String(reader, "TeamName", "");
					//obj.SalesmanName = GetReaderValue_String(reader, "SalesmanName", "");
					//obj.CustomerName = GetReaderValue_String(reader, "CustomerName", "");
					lst.Add(obj);
					obj = null;
				}
				return lst;
			}
			catch (SqlException sqlex)
			{
				//LogException(sqlex);
				throw new Exception("Failed to get Revenue and Target", sqlex);
			}
			finally
			{
				cmd.Dispose();
				cn.Close();
				cn.Dispose();
			}
		}

		/// <summary>
		/// KPI_GetCustomerRevenueTargetByID
		/// </summary>
		/// <param name="salesTargetNo"></param>
		/// <param name="yearNo"></param>
		/// <returns></returns>
		public override List<RevenueTargetDetails> GetCustomerRevenueByID(System.Int32? salesTargetNo, System.Int32 yearNo)
		{
			SqlConnection cn = null;
			SqlCommand cmd = null;
			try
			{

				cn = new SqlConnection(this.ConnectionString);
				cmd = new SqlCommand("KPI_GetCustomerRevenueTargetByID", cn);
				cmd.CommandType = CommandType.StoredProcedure;
				cmd.CommandTimeout = 30;
				cmd.Parameters.Add("@SalesTargetNo", SqlDbType.Int).Value = salesTargetNo;
				cmd.Parameters.Add("@yearNo", SqlDbType.Int).Value = yearNo;
				cn.Open();
				DbDataReader reader = ExecuteReader(cmd);
				List<RevenueTargetDetails> lst = new List<RevenueTargetDetails>();
				while (reader.Read())
				{
					RevenueTargetDetails obj = new RevenueTargetDetails();
					obj.SectionId = GetReaderValue_Int32(reader, "CompanyId", 0);
					obj.SectionName = GetReaderValue_String(reader, "CustomerName", "");
					obj.SectionType = GetReaderValue_String(reader, "SectionType", "");
					obj.JanTarget = GetReaderValue_NullableDouble(reader, "JanTarget", null);
					obj.FebTarget = GetReaderValue_NullableDouble(reader, "FebTarget", null);
					obj.MarchTarget = GetReaderValue_NullableDouble(reader, "MarchTarget", null);
					obj.AprTarget = GetReaderValue_NullableDouble(reader, "AprTarget", null);
					obj.MayTarget = GetReaderValue_NullableDouble(reader, "MayTarget", null);
					obj.JuneTarget = GetReaderValue_NullableDouble(reader, "JuneTarget", null);
					obj.JulyTarget = GetReaderValue_NullableDouble(reader, "JulyTarget", null);
					obj.AugTarget = GetReaderValue_NullableDouble(reader, "AugTarget", null);
					obj.SepTarget = GetReaderValue_NullableDouble(reader, "SepTarget", null);
					obj.OctTarget = GetReaderValue_NullableDouble(reader, "OctTarget", null);
					obj.NovTarget = GetReaderValue_NullableDouble(reader, "NovTarget", null);
					obj.DecTarget = GetReaderValue_NullableDouble(reader, "DecTarget", null);
					obj.JanRevenue = GetReaderValue_NullableDouble(reader, "JanRevenue", null);
					obj.FebRevenue = GetReaderValue_NullableDouble(reader, "FebRevenue", null);
					obj.MarchRevenue = GetReaderValue_NullableDouble(reader, "MarchRevenue", null);
					obj.AprRevenue = GetReaderValue_NullableDouble(reader, "AprRevenue", null);
					obj.MayRevenue = GetReaderValue_NullableDouble(reader, "MayRevenue", null);
					obj.JuneRevenue = GetReaderValue_NullableDouble(reader, "JuneRevenue", null);
					obj.JulyRevenue = GetReaderValue_NullableDouble(reader, "JulyRevenue", null);
					obj.AugRevenue = GetReaderValue_NullableDouble(reader, "AugRevenue", null);
					obj.SepRevenue = GetReaderValue_NullableDouble(reader, "SepRevenue", null);
					obj.OctRevenue = GetReaderValue_NullableDouble(reader, "OctRevenue", null);
					obj.NovRevenue = GetReaderValue_NullableDouble(reader, "NovRevenue", null);
					obj.DecRevenue = GetReaderValue_NullableDouble(reader, "DecRevenue", null);

					obj.JanGrossProfit = GetReaderValue_NullableDouble(reader, "JanGrossProfit", null);
					obj.FebGrossProfit = GetReaderValue_NullableDouble(reader, "FebGrossProfit", null);
					obj.MarchGrossProfit = GetReaderValue_NullableDouble(reader, "MarchGrossProfit", null);
					obj.AprGrossProfit = GetReaderValue_NullableDouble(reader, "AprGrossProfit", null);
					obj.MayGrossProfit = GetReaderValue_NullableDouble(reader, "MayGrossProfit", null);
					obj.JuneGrossProfit = GetReaderValue_NullableDouble(reader, "JuneGrossProfit", null);
					obj.JulyGrossProfit = GetReaderValue_NullableDouble(reader, "JulyGrossProfit", null);
					obj.AugGrossProfit = GetReaderValue_NullableDouble(reader, "AugGrossProfit", null);
					obj.SepGrossProfit = GetReaderValue_NullableDouble(reader, "SepGrossProfit", null);
					obj.OctGrossProfit = GetReaderValue_NullableDouble(reader, "OctGrossProfit", null);
					obj.NovGrossProfit = GetReaderValue_NullableDouble(reader, "NovGrossProfit", null);
					obj.DecGrossProfit = GetReaderValue_NullableDouble(reader, "DecGrossProfit", null);

					//obj.TotalTarget = GetReaderValue_NullableDouble(reader, "TotalTarget,", null);
					//  obj.AllocatedPer = GetReaderValue_NullableDouble(reader, "AllocatedPer", null);
					//obj.TotalRevenue = GetReaderValue_NullableDouble(reader, "TotalRevenue", null);
					//   obj.RevenuePer = GetReaderValue_NullableDouble(reader, "RevenuePer", null);
					obj.RowId = GetReaderValue_Int32(reader, "RowId", 0);
					//obj.TeamName = GetReaderValue_String(reader, "TeamName", "");
					//obj.SalesmanName = GetReaderValue_String(reader, "SalesmanName", "");
					//obj.CustomerName = GetReaderValue_String(reader, "CustomerName", "");
					lst.Add(obj);
					obj = null;
				}
				return lst;
			}
			catch (SqlException sqlex)
			{
				//LogException(sqlex);
				throw new Exception("Failed to get Revenue and Target", sqlex);
			}
			finally
			{
				cmd.Dispose();
				cn.Close();
				cn.Dispose();
			}
		}

		/// <summary>
		/// GetDivisionAndTeamTarget 
		/// Calls [[KPI_GetSalesRevenueTargetByTeamID]]
		/// </summary>
		public override List<RevenueTargetDetails> GetSalesRevenueTargetByTeamID(System.Int32? teamNo, System.Int32 yearNo)
		{
			SqlConnection cn = null;
			SqlCommand cmd = null;
			try
			{
				cn = new SqlConnection(this.ConnectionString);
				cmd = new SqlCommand("KPI_GetSalesRevenueTargetByTeamID", cn);
				cmd.CommandType = CommandType.StoredProcedure;
				cmd.CommandTimeout = 30;
				cmd.Parameters.Add("@TeamNo", SqlDbType.Int).Value = teamNo;
				cmd.Parameters.Add("@yearNo", SqlDbType.Int).Value = yearNo;
				cn.Open();
				DbDataReader reader = ExecuteReader(cmd);
				List<RevenueTargetDetails> lst = new List<RevenueTargetDetails>();
				while (reader.Read())
				{
					RevenueTargetDetails obj = new RevenueTargetDetails();
					obj.SectionId = GetReaderValue_Int32(reader, "SectionId", 0);
					obj.SectionName = GetReaderValue_String(reader, "SectionName", "");
					obj.SectionType = GetReaderValue_String(reader, "SectionType", "");
					obj.JanTarget = GetReaderValue_NullableDouble(reader, "JanTarget", null);
					obj.FebTarget = GetReaderValue_NullableDouble(reader, "FebTarget", null);
					obj.MarchTarget = GetReaderValue_NullableDouble(reader, "MarchTarget", null);
					obj.AprTarget = GetReaderValue_NullableDouble(reader, "AprTarget", null);
					obj.MayTarget = GetReaderValue_NullableDouble(reader, "MayTarget", null);
					obj.JuneTarget = GetReaderValue_NullableDouble(reader, "JuneTarget", null);
					obj.JulyTarget = GetReaderValue_NullableDouble(reader, "JulyTarget", null);
					obj.AugTarget = GetReaderValue_NullableDouble(reader, "AugTarget", null);
					obj.SepTarget = GetReaderValue_NullableDouble(reader, "SepTarget", null);
					obj.OctTarget = GetReaderValue_NullableDouble(reader, "OctTarget", null);
					obj.NovTarget = GetReaderValue_NullableDouble(reader, "NovTarget", null);
					obj.DecTarget = GetReaderValue_NullableDouble(reader, "DecTarget", null);
					obj.JanRevenue = GetReaderValue_NullableDouble(reader, "JanRevenue", null);
					obj.FebRevenue = GetReaderValue_NullableDouble(reader, "FebRevenue", null);
					obj.MarchRevenue = GetReaderValue_NullableDouble(reader, "MarchRevenue", null);
					obj.AprRevenue = GetReaderValue_NullableDouble(reader, "AprRevenue", null);
					obj.MayRevenue = GetReaderValue_NullableDouble(reader, "MayRevenue", null);
					obj.JuneRevenue = GetReaderValue_NullableDouble(reader, "JuneRevenue", null);
					obj.JulyRevenue = GetReaderValue_NullableDouble(reader, "JulyRevenue", null);
					obj.AugRevenue = GetReaderValue_NullableDouble(reader, "AugRevenue", null);
					obj.SepRevenue = GetReaderValue_NullableDouble(reader, "SepRevenue", null);
					obj.OctRevenue = GetReaderValue_NullableDouble(reader, "OctRevenue", null);
					obj.NovRevenue = GetReaderValue_NullableDouble(reader, "NovRevenue", null);
					obj.DecRevenue = GetReaderValue_NullableDouble(reader, "DecRevenue", null);
					//obj.TotalTarget = GetReaderValue_NullableDouble(reader, "TotalTarget,", null);
					obj.AllocatedPer = GetReaderValue_NullableDouble(reader, "AllocatedPer", null);
					//obj.TotalRevenue = GetReaderValue_NullableDouble(reader, "TotalRevenue", null);
					obj.RevenuePer = GetReaderValue_NullableDouble(reader, "RevenuePer", null);
					obj.RowId = GetReaderValue_Int32(reader, "RowId", 0);
					lst.Add(obj);
					obj = null;
				}
				return lst;
			}
			catch (SqlException sqlex)
			{
				//LogException(sqlex);
				throw new Exception("Failed to get Revenue and Target", sqlex);
			}
			finally
			{
				cmd.Dispose();
				cn.Close();
				cn.Dispose();
			}
		}

		/// <summary>
		/// GetDivisionAndTeamTarget 
		/// Calls [[KPI_GetCustomerRevenueTargetBySalesManNo]]
		/// </summary>
		public override List<RevenueTargetDetails> GetSalesRevenueTargetBySalesID(System.Int32? salemanNo, System.Int32 yearNo)
		{
			SqlConnection cn = null;
			SqlCommand cmd = null;
			try
			{
				cn = new SqlConnection(this.ConnectionString);
				cmd = new SqlCommand("KPI_GetCustomerRevenueTargetBySalesManNo", cn);
				cmd.CommandType = CommandType.StoredProcedure;
				cmd.CommandTimeout = 30;
				cmd.Parameters.Add("@SalesManNo", SqlDbType.Int).Value = salemanNo;
				cmd.Parameters.Add("@yearNo", SqlDbType.Int).Value = yearNo;
				cn.Open();
				DbDataReader reader = ExecuteReader(cmd);
				List<RevenueTargetDetails> lst = new List<RevenueTargetDetails>();
				while (reader.Read())
				{
					RevenueTargetDetails obj = new RevenueTargetDetails();
					obj.SectionId = GetReaderValue_Int32(reader, "SectionId", 0);
					obj.SectionName = GetReaderValue_String(reader, "SectionName", "");
					obj.SectionType = GetReaderValue_String(reader, "SectionType", "");
					obj.JanTarget = GetReaderValue_NullableDouble(reader, "JanTarget", null);
					obj.FebTarget = GetReaderValue_NullableDouble(reader, "FebTarget", null);
					obj.MarchTarget = GetReaderValue_NullableDouble(reader, "MarchTarget", null);
					obj.AprTarget = GetReaderValue_NullableDouble(reader, "AprTarget", null);
					obj.MayTarget = GetReaderValue_NullableDouble(reader, "MayTarget", null);
					obj.JuneTarget = GetReaderValue_NullableDouble(reader, "JuneTarget", null);
					obj.JulyTarget = GetReaderValue_NullableDouble(reader, "JulyTarget", null);
					obj.AugTarget = GetReaderValue_NullableDouble(reader, "AugTarget", null);
					obj.SepTarget = GetReaderValue_NullableDouble(reader, "SepTarget", null);
					obj.OctTarget = GetReaderValue_NullableDouble(reader, "OctTarget", null);
					obj.NovTarget = GetReaderValue_NullableDouble(reader, "NovTarget", null);
					obj.DecTarget = GetReaderValue_NullableDouble(reader, "DecTarget", null);
					obj.JanRevenue = GetReaderValue_NullableDouble(reader, "JanRevenue", null);
					obj.FebRevenue = GetReaderValue_NullableDouble(reader, "FebRevenue", null);
					obj.MarchRevenue = GetReaderValue_NullableDouble(reader, "MarchRevenue", null);
					obj.AprRevenue = GetReaderValue_NullableDouble(reader, "AprRevenue", null);
					obj.MayRevenue = GetReaderValue_NullableDouble(reader, "MayRevenue", null);
					obj.JuneRevenue = GetReaderValue_NullableDouble(reader, "JuneRevenue", null);
					obj.JulyRevenue = GetReaderValue_NullableDouble(reader, "JulyRevenue", null);
					obj.AugRevenue = GetReaderValue_NullableDouble(reader, "AugRevenue", null);
					obj.SepRevenue = GetReaderValue_NullableDouble(reader, "SepRevenue", null);
					obj.OctRevenue = GetReaderValue_NullableDouble(reader, "OctRevenue", null);
					obj.NovRevenue = GetReaderValue_NullableDouble(reader, "NovRevenue", null);
					obj.DecRevenue = GetReaderValue_NullableDouble(reader, "DecRevenue", null);
					//obj.TotalTarget = GetReaderValue_NullableDouble(reader, "TotalTarget,", null);
					obj.AllocatedPer = GetReaderValue_NullableDouble(reader, "AllocatedPer", null);
					//obj.TotalRevenue = GetReaderValue_NullableDouble(reader, "TotalRevenue", null);
					obj.RevenuePer = GetReaderValue_NullableDouble(reader, "RevenuePer", null);
					obj.RowId = GetReaderValue_Int32(reader, "RowId", 0);
					obj.RecordCount = GetReaderValue_Int32(reader, "RecordCount", 0);
					lst.Add(obj);
					obj = null;
				}
				return lst;
			}
			catch (SqlException sqlex)
			{
				//LogException(sqlex);
				throw new Exception("Failed to get Revenue and Target", sqlex);
			}
			finally
			{
				cmd.Dispose();
				cn.Close();
				cn.Dispose();
			}
		}

		/// <summary>
		/// <summary>
		/// Update
		/// Calls [usp_update_DivisionTeamTarget]
		/// </summary>
		public override bool Update(System.Int32? rowId, System.String rowType, System.String columnName, System.Double? targetValue, System.Int32? updatedBy, System.Int32? Year, System.Int32? divisionNo)
		{
			SqlConnection cn = null;
			SqlCommand cmd = null;
			try
			{
				cn = new SqlConnection(this.ConnectionString);
				cmd = new SqlCommand("usp_update_DivisionTeamTarget", cn);
				cmd.CommandType = CommandType.StoredProcedure;
				cmd.Parameters.Add("@RowId", SqlDbType.Int).Value = rowId;
				cmd.Parameters.Add("@RowType", SqlDbType.NVarChar).Value = rowType;
				cmd.Parameters.Add("@ColumnName", SqlDbType.NVarChar).Value = columnName;
				cmd.Parameters.Add("@TargetValue", SqlDbType.Float).Value = targetValue;
				cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
				cmd.Parameters.Add("@Year", SqlDbType.Int).Value = Year;
				cmd.Parameters.Add("@DivisionOrTeamNo", SqlDbType.Int).Value = divisionNo;
				cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
				cn.Open();
				int ret = ExecuteNonQuery(cmd);
				return (ret > 0);
			}
			catch (SqlException sqlex)
			{
				//LogException(sqlex);
				throw new Exception("Failed to update Team Division Traget", sqlex);
			}
			finally
			{
				cmd.Dispose();
				cn.Close();
				cn.Dispose();
			}
		}


		/// <summary>
		/// <summary>
		/// Update
		/// Calls [usp_saveAllDivisionTeamTarget]
		/// </summary>
		public override bool SaveAllDivisionTeamTarget(System.Int32? Year, System.Int32? divisionNo, System.Int32? updatedBy)
		{
			SqlConnection cn = null;
			SqlCommand cmd = null;
			try
			{
				cn = new SqlConnection(this.ConnectionString);
				cmd = new SqlCommand("usp_saveAllDivisionTeamTarget", cn);
				cmd.CommandType = CommandType.StoredProcedure;
				cmd.Parameters.Add("@Year", SqlDbType.Int).Value = Year;
				cmd.Parameters.Add("@DivisionNo", SqlDbType.Int).Value = divisionNo;
				cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
				cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
				cn.Open();
				int ret = ExecuteNonQuery(cmd);
				return (ret > 0);
			}
			catch (SqlException sqlex)
			{
				//LogException(sqlex);
				throw new Exception("Failed to save all division Team Division Traget", sqlex);
			}
			finally
			{
				cmd.Dispose();
				cn.Close();
				cn.Dispose();
			}
		}



	}
}
