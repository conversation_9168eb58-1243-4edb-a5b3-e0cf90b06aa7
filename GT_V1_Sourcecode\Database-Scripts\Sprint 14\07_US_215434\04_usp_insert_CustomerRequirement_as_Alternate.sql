﻿
GO


SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

/*   
======================================================================================================================= 
TASK         UPDATED BY       DATE          ACTION    DESCRIPTION  
[US-215434]  Phuc Hoang       06-Nov-2024   CREATE    Lytica Price should apply fuzzy logic for inserting & displaying  
=======================================================================================================================
*/ 

CREATE OR ALTER PROCEDURE [dbo].[usp_insert_CustomerRequirement_as_Alternate] 
--******************************************************************************************                
--* SK 20.01.2010:                
--* - allow for new columns - PartWatch, BOM, BOMName                
--*                 
--* SK 29.10.2009:                
--* - allow for new column - FullCustomerPart - used for searching                
--*                
--* RP 17.08.2009:                
--* - Made @Price default to 0 (task 97)                
--[002]      Suhail          10/04/2018   Adding multiple controls  not exist in Alternate comparing with Add Request              
--[003]      Bhooma          01/01/2021   Added alternate part same as add new requirement              
--******************************************************************************************                                      
      @CustomerRequirementId int                                        
    , @Part nvarchar(40)                                        
    , @ManufacturerNo int = NULL                                        
    , @DateCode nvarchar(5) = NULL                                        
    , @PackageNo int = NULL                                        
    , @Quantity int                                        
    , @Price float =NULL                                       
    , @CurrencyNo int   =0                                     
    , @ReceivedDate datetime                                        
    , @Salesman int                                        
    , @DatePromised datetime                                        
    , @Notes nvarchar(max) = NULL                                        
    , @Instructions nvarchar(max) = NULL                                        
    , @Shortage bit                                        
    , @CompanyNo int                                        
    , @ContactNo int                                        
    , @UsageNo int = NULL                                        
    , @Alternate bit                                        
    , @OriginalCustomerRequirementNo int = NULL                                        
    , @ReasonNo int = NULL                                        
    , @ProductNo int = NULL                                        
    , @CustomerPart nvarchar(30) = NULL                                        
    , @Closed bit                                        
    , @ROHS tinyint  =NULL                                      
    , @UpdatedBy int = NULL                                        
 , @PartWatch bit = Null                                          
 , @BOM bit = Null                                          
 , @BOMName nvarchar(128) = Null                                          
    ,@BOMNo int =Null                                     
    ,@FactorySealed bit=Null                                    
    ,@MSL nvarchar(50) = Null                               
                                  
    ,@PartialQuantityAcceptable bit=Null,                                  
@Obsolete bit =Null,                                  
@LastTimeBuy bit=Null,                                  
@RefirbsAcceptable bit=Null,                                  
@TestingRequired bit =Null,                                  
@TargetSellPrice float=Null,                                  
@CompetitorBestOffer float=Null,                                  
@CustomerDecisionDate datetime=Null,                                  
@RFQClosingDate datetime=Null,                                  
@QuoteValidityRequired int=Null,                                  
@Type int=Null,                                  
@OrderToPlace int =Null,                                  
@RequirementForTraceability int=Null ,                            
@EAU   nvarchar(50) = Null,               
                            
@AlternativesAccepted bit =NULL,                                  
@RepeatBusiness bit=NULL,                              
@SupportTeamMemberNo   int =null,                        
 --add parameter for ihs code start                                        
@CountryOfOrigin   NVARCHAR(100) = NULL,                                         
@CountryOfOriginNo int=0 ,                                        
@LifeCycleStage    NVARCHAR(100) = NULL,                                         
@HTSCode           VARCHAR(20) = NULL,                                        
@AveragePrice      FLOAT=0,                                        
@Packing VARCHAR(60) = NULL,                                        
@PackagingSize     NVARCHAR(100) = NULL,                          
@Descriptions      NVARCHAR(max) = NULL,                                    
@IHSPartsNo        INT  =null  ,                                  
@IHSCurrencyCode   NVARCHAR(100) = NULL,                         
@IHSProduct        NVARCHAR(100) = NULL,                         
@ECCNCode          NVARCHAR(30) = NULL,       
@ECCNNo int = null,                
                 
                     
                                      
 --add paramerter for ihs code end                      
@NewGenerateId int = NULL OUTPUT                                        
                                        
AS                                                                 
  BEGIN              
  SET @Alternate=1            
   INSERT INTO [dbo].[tbCustomerRequirement]            
           ([CustomerRequirementNumber]            
           ,[ClientNo]            
           ,[FullPart]            
           ,[Part]            
           ,[ManufacturerNo]            
           ,[DateCode]            
           ,[PackageNo]            
           ,[Quantity]            
           ,[Price]            
           ,[CurrencyNo]            
           ,[ReceivedDate]            
           ,[Salesman]            
           ,[DatePromised]            
           ,[Notes]            
           ,[Instructions]            
           ,[Shortage]            
           ,[CompanyNo]            
           ,[ContactNo]            
           ,[Alternate]            
           ,[OriginalCustomerRequirementNo]            
           ,[ReasonNo]            
           ,[ProductNo]            
           ,[CustomerPart]            
           ,[Closed]            
           ,[ROHS]            
           ,[UpdatedBy]            
           ,[DLUP]            
           ,[UsageNo]            
           ,[FullCustomerPart]            
           ,[BOM]            
           ,[BOMName]            
           ,[PartWatch]            
           ,[BOMNo]            
           ,[POHubReleaseBy]            
           ,[DatePOHubRelease]            
           ,[PHPrice]            
           ,[FactorySealed]            
           ,[MSL]            
           ,[PartialQuantityAcceptable]            
           ,[Obsolete]            
           ,[LastTimeBuy]            
           ,[RefirbsAcceptable]            
           ,[TestingRequired]            
           ,[TargetSellPrice]            
           ,[CompetitorBestOffer]            
           ,[CustomerDecisionDate]            
           ,[RFQClosingDate]            
           ,[QuoteValidityRequired]            
           ,[ReqType]            
           ,[OrderToPlace]            
           ,[ReqForTraceability]            
           ,[EAU]            
           ,[HasClientSourcingResult]            
           ,[HasHubSourcingResult]            
           ,[IsNoBid]            
           ,[NoBidNotes]            
           ,[AlternativesAccepted]            
           ,[RepeatBusiness]            
           ,[ExpediteDate]            
           ,[IsAltAttached]            
          ,[AlternateStatus]            
           ,[ClientBOMNo]            
           ,[SupportTeamMemberNo]            
           ,[CountryOfOriginNo]            
           ,[LifeCycleStage]            
           ,[HTSCode]            
           ,[AveragePrice]            
           ,[Packing]      
           ,[PackagingSize]            
           ,[Descriptions]            
           ,[IHSPartsNo]            
           ,[IHSCurrencyCode]            
           ,[IHSProduct]            
           ,[RefIdHK]            
           ,[ECCNCode]            
           ,[REQStatus]            
           ,[ParentRequirementNo])            
select [CustomerRequirementNumber]            
           ,[ClientNo]            
           ,[FullPart]            
           ,[Part]            
           ,[ManufacturerNo]            
           ,[DateCode]            
           ,[PackageNo]            
           ,[Quantity]            
           ,[Price]            
           ,[CurrencyNo]            
          ,[ReceivedDate]            
           ,[Salesman]            
           ,[DatePromised]            
           ,[Notes]            
           ,[Instructions]            
           ,[Shortage]            
           ,[CompanyNo]            
           ,[ContactNo]            
           ,[Alternate]            
           ,[OriginalCustomerRequirementNo]            
           ,[ReasonNo]            
           ,[ProductNo]            
           ,[CustomerPart]            
           ,[Closed]            
           ,[ROHS]            
           ,[UpdatedBy]            
           ,[DLUP]            
           ,[UsageNo]            
           ,[FullCustomerPart]            
           ,[BOM]            
           ,[BOMName]            
           ,[PartWatch]            
           ,[BOMNo]            
           ,[POHubReleaseBy]            
           ,[DatePOHubRelease]            
           ,[PHPrice]            
           ,[FactorySealed]            
           ,[MSL]            
           ,[PartialQuantityAcceptable]            
           ,[Obsolete]            
           ,[LastTimeBuy]            
           ,[RefirbsAcceptable]            
           ,[TestingRequired]            
           ,[TargetSellPrice]            
           ,[CompetitorBestOffer]            
           ,[CustomerDecisionDate]            
           ,[RFQClosingDate]            
           ,[QuoteValidityRequired]            
           ,[ReqType]            
           ,[OrderToPlace]            
           ,[ReqForTraceability]            
           ,[EAU]            
           ,[HasClientSourcingResult]            
           ,[HasHubSourcingResult]            
           ,[IsNoBid]            
           ,[NoBidNotes]            
           ,[AlternativesAccepted]            
           ,[RepeatBusiness]            
           ,[ExpediteDate]            
           ,[IsAltAttached]            
           ,[AlternateStatus]            
           ,[ClientBOMNo]            
           ,[SupportTeamMemberNo]            
           ,[CountryOfOriginNo]            
           ,[LifeCycleStage]            
           ,[HTSCode]            
           ,[AveragePrice]            
           ,[Packing]            
           ,[PackagingSize]            
           ,[Descriptions]            
           ,[IHSPartsNo]            
           ,[IHSCurrencyCode]            
           ,[IHSProduct]            
           ,[RefIdHK]            
           ,[ECCNCode]            
           ,[REQStatus]            
           ,[ParentRequirementNo]            
            
     from [dbo].[tbCustomerRequirement] where CustomerRequirementId = @CustomerRequirementId                   
               
   SET @CustomerRequirementId=SCOPE_IDENTITY()            
                                  
                                        
  DECLARE @CustomerRequirementNumber int =NULL;                                   
  DECLARE @CurPrice float                                
  DECLARE @ClientNo  int                
  DECLARE @PartEditStatus  int=0                                 
                                
  SELECT  @CustomerRequirementNumber=CustomerRequirementNumber,@CurPrice = Price , @ClientNo = ClientNo                                
  FROM tbCustomerRequirement  where CustomerRequirementId=@CustomerRequirementId 
  
  DECLARE @manufacturerName NVARCHAR(256) = NULL,
		@LyticaManufacturerRef NVARCHAR(256) = NULL,
		@LyticaAveragePrice FLOAT = NULL,
		@LyticaTargetPrice FLOAT = NULL,
		@LyticaMarketLeading FLOAT = NULL;
	
	SELECT TOP 1 @manufacturerName = ManufacturerName FROM dbo.tbManufacturer WHERE ManufacturerId = @ManufacturerNo;  

	SELECT TOP 1 @LyticaManufacturerRef = Manufacturer, @LyticaAveragePrice = AveragePrice, @LyticaTargetPrice = TargetPrice, @LyticaMarketLeading = MarketLeading
	FROM dbo.tbLyticaAPI
	WHERE OriginalPartSearched = @Part 
		AND ISNULL(Inactive, 0) = 0
		AND (ISNULL(AveragePrice, 0) + ISNULL(TargetPrice, 0) + ISNULL(MarketLeading, 0)) > 0
		AND (
			Manufacturer = ISNULL(@manufacturerName, '') 
			OR Manufacturer LIKE ISNULL(@manufacturerName, '')  + '%'
			OR @manufacturerName LIKE ISNULL(Manufacturer, '') + '%' 
			OR Manufacturer LIKE ISNULL([dbo].[ufn_GetFirstWord](@manufacturerName), '') + '%'
		)

  --PartEditStatus check                
  --select @PartEditStatus=count(CustomerRequirementId) from tbCustomerRequirement             
  --where CustomerRequirementId=@CustomerRequirementId and REQStatus=1 and BOMNo is null and REQStatus!=5           
  SET @PartEditStatus=1                  
                    
  --when part status allow edit                
  if(@PartEditStatus=1 and @IHSPartsNo is not null)                
  begin                
  UPDATE  dbo.tbCustomerRequirement           
 SET     FullPart = dbo.ufn_get_fullpart(@Part)                                        
  , Part = @Part                                        
  , ManufacturerNo = @ManufacturerNo                                        
  , DateCode = @DateCode                                        
  , PackageNo = @PackageNo                                        
  , Quantity = @Quantity                                        
  , Price = @Price                                        
  , CurrencyNo = @CurrencyNo                                        
  , ReceivedDate = @ReceivedDate                                        
  , Salesman = @Salesman                                        
  , DatePromised = @DatePromised                                        
  , Notes = @Notes                                        
  , Instructions = @Instructions                                        
  , Shortage = @Shortage                                        
  , CompanyNo = @CompanyNo                                        
  , ContactNo = @ContactNo                                        
  , UsageNo = @UsageNo                                        
  , Alternate = @Alternate                                  
  , OriginalCustomerRequirementNo = @OriginalCustomerRequirementNo                                        
  , ReasonNo = @ReasonNo                                        
  , ProductNo = @ProductNo                                        
  , CustomerPart = @CustomerPart                                        
  , Closed = @Closed                                        
  , ROHS = @ROHS                                        
  , UpdatedBy = @UpdatedBy              , DLUP = CURRENT_TIMESTAMP                                        
  , FullCustomerPart = dbo.ufn_get_fullpart(@CustomerPart)                                        
  , PartWatch = @PartWatch                                        
  , BOM = @BOM                                        
  , BOMName = @BOMName                                       
  ,BOMNo=@BOMNo                                     
  ,FactorySealed=@FactorySealed                                     
   ,MSL=@MSL                               
   ,PartialQuantityAcceptable=@PartialQuantityAcceptable                                  
 ,Obsolete  =@Obsolete                                
 ,LastTimeBuy  =@LastTimeBuy                                
 ,RefirbsAcceptable =@RefirbsAcceptable                                 
 ,TestingRequired  =@TestingRequired              
 ,TargetSellPrice   =@TargetSellPrice                               
 ,CompetitorBestOffer  =@CompetitorBestOffer                                
 ,CustomerDecisionDate  =@CustomerDecisionDate                                
 ,RFQClosingDate    =@RFQClosingDate                              
 ,QuoteValidityRequired  =@QuoteValidityRequired                                
 ,ReqType    =@Type                    
 ,OrderToPlace    =@OrderToPlace                              
 ,ReqForTraceability  =@RequirementForTraceability                                 
 ,EAU =@EAU                             
 ,AlternativesAccepted=@AlternativesAccepted                            
 ,RepeatBusiness=@RepeatBusiness                            
 ,SupportTeamMemberNo=@SupportTeamMemberNo                          
  --ihs code start   when part status allow edit                                     
,CountryOfOriginNo =@CountryOfOriginNo                                
,LifeCycleStage    =@LifeCycleStage           
,HTSCode           =@HTSCode                               
,AveragePrice      =@AveragePrice                               
,Packing           =@Packing                               
,PackagingSize     =@PackagingSize                              
,Descriptions     =@Descriptions                      
,IHSPartsNo        =@IHSPartsNo                        
,ihsCurrencyCode  =@IHSCurrencyCode                   
,IHSProduct       =@IHSProduct                        
,ECCNCode         =@ECCNCode             
, AlternateStatus =1 -- 1-Alternate,2-Possible Alternate,3-Firm Alternate 
 ,LyticaManufacturerRef = @LyticaManufacturerRef
 ,LyticaAveragePrice = @LyticaAveragePrice
 ,LyticaTargetPrice = @LyticaTargetPrice
 ,LyticaMarketLeading = @LyticaMarketLeading
 --ihs code end                 
  WHERE   CustomerRequirementId = @CustomerRequirementId                
  end                
  else                
  begin                                  
 UPDATE  dbo.tbCustomerRequirement                                        
 SET     FullPart = dbo.ufn_get_fullpart(@Part)                                        
  , Part = @Part                                        
  , ManufacturerNo = @ManufacturerNo                                        
  , DateCode = @DateCode                                        
  , PackageNo = @PackageNo                                        
  , Quantity = @Quantity                                        
  , Price = @Price                                       
  , CurrencyNo = @CurrencyNo                                        
  , ReceivedDate = @ReceivedDate                                        
  , Salesman = @Salesman                                        
  , DatePromised = @DatePromised                                        
  , Notes = @Notes                                        
  , Instructions = @Instructions                                        
  , Shortage = @Shortage                                        
  , CompanyNo = @CompanyNo                                        
  , ContactNo = @ContactNo                                        
  , UsageNo = @UsageNo                                        
  , Alternate = @Alternate              
  , OriginalCustomerRequirementNo = @OriginalCustomerRequirementNo                                        
  , ReasonNo = @ReasonNo                                        
  , ProductNo = @ProductNo                                        
  , CustomerPart = @CustomerPart                                        
  , Closed = @Closed                                        
  , ROHS = @ROHS                                        
  , UpdatedBy = @UpdatedBy              , DLUP = CURRENT_TIMESTAMP                                        
  , FullCustomerPart = dbo.ufn_get_fullpart(@CustomerPart)                                        
  , PartWatch = @PartWatch                                        
  , BOM = @BOM                                        
  , BOMName = @BOMName                                       
  ,BOMNo=@BOMNo                                     
  ,FactorySealed=@FactorySealed                                     
   ,MSL=@MSL                               
   ,PartialQuantityAcceptable=@PartialQuantityAcceptable                                  
 ,Obsolete  =@Obsolete                                
 ,LastTimeBuy  =@LastTimeBuy                                
 ,RefirbsAcceptable =@RefirbsAcceptable                                 
 ,TestingRequired  =@TestingRequired                                
 ,TargetSellPrice   =@TargetSellPrice                               
 ,CompetitorBestOffer  =@CompetitorBestOffer                                
 ,CustomerDecisionDate  =@CustomerDecisionDate                                
 ,RFQClosingDate    =@RFQClosingDate                              
 ,QuoteValidityRequired  =@QuoteValidityRequired                                
 ,ReqType    =@Type                              
 ,OrderToPlace    =@OrderToPlace                              
 ,ReqForTraceability  =@RequirementForTraceability                                 
 ,EAU =@EAU                             
 ,AlternativesAccepted=@AlternativesAccepted                     
 ,RepeatBusiness=@RepeatBusiness                            
 ,SupportTeamMemberNo=@SupportTeamMemberNo                          
 , AlternateStatus =1 -- 1-Alternate,2-Possible Alternate,3-Firm Alternate 
 ,LyticaManufacturerRef = @LyticaManufacturerRef
 ,LyticaAveragePrice = @LyticaAveragePrice
 ,LyticaTargetPrice = @LyticaTargetPrice
 ,LyticaMarketLeading = @LyticaMarketLeading

  WHERE   CustomerRequirementId = @CustomerRequirementId                                       
  end                
                                
  --if(@BOMNo>0)                                      
  --BEGIN                                
  --IF NOT EXISTS(select count(1) from dbo.tbBOM where BOMId=@BOMNo and status=3)                              
  --begin                                      
  --UPDATE  dbo.tbBOM                                      
  --SET [Status] =2 where BOMId=@BOMNo                                      
  -- end                               
                                     
  --Update dbo.tbCustomerRequirement                                      
  --set BOMNo=@BOMNo                                       
  --WHERE ClientNo = @ClientNo and   CustomerRequirementNumber = @CustomerRequirementNumber                                      
  --AND Alternate = 1                                 
                                
  ----Update price, if price updated                                
  -- IF @CurPrice <> @Price                                
  -- BEGIN                                
                                
  --DECLARE @PHCurrencyNo INT                                        
  --DECLARE  @TotalValue float                                      
  -- SELECT TOP 1 @PHCurrencyNo = POCurrencyNo                                          
  -- FROM tbCompany where ClientNo = @ClientNo and isnull(IsPOHub,0) = 1                                  
                                    
  --     UPDATE tbCustomerRequirement SET PHPrice = isnull(isnull(price,0)/ dbo.ufn_get_exchange_rate(CurrencyNo, getdate()),0)*dbo.ufn_get_exchange_rate(@PHCurrencyNo, getdate())                                        
  -- WHERE BOMNo = @BOMNo                                        
                                        
  --SELECT @TotalValue=SUM(Quantity * Isnull(PHPrice,0))  from tbCustomerRequirement where BOMNo = @BOMNo                                  
  --UPDATE tbBOM set TotalBomLinePrice=@TotalValue where BOMId=  @BOMNo                                  
  -- END                                
                            --END                 
if(@ECCNNo is not null and @ECCNCode is not null)              
begin              
declare @checkdublicate int=0                   
set @checkdublicate=(select count(*) from tbPartEccnMapped where  Part=@Part )                               
if(@checkdublicate=0)                      
begin                        
INSERT INTO dbo.tbPartEccnMapped                                            
 ( Part                                            
 , ECCNNo                                            
 , ECCNCode                  
 , ClientNo                                      
 , UpdatedBy                                            
                                          
 )                                            
VALUES                      
 ( @Part                                            
 , @ECCNNo                                            
 , @ECCNCode                
 , @ClientNo                                            
 , @UpdatedBy                                             
)                            
end       
 --------------------------------for add log entry into Division header when update the record-----------------------------            
        
  declare @SectionName varchar(50) = 'CustomerRequirementECCN'              
    declare @SubSectionName varchar(50) = 'ECCN'              
    declare @ActionName   varchar(10) = 'Print'              
    declare @DocumentNo     int     = @CustomerRequirementId              
 declare @Detail     nvarchar(max) = 'Action¦¦' +' CUSTOMER REQUIREMENT (ALTERNATE) ADDED WITH THIS ECCN CODE  ( ' + @ECCNCode + ' )'          
    declare @PrintDocumentLogId  int =NULL             
   -----------------------------------------------------------------------              
   EXEC [dbo].[usp_insert_PrintEmailLog]                 
   @SectionName               
       , @SubSectionName              
       , @ActionName                 
       , @DocumentNo                 
       , @Detail                   
       , @UpdatedBy                
       , @PrintDocumentLogId                                 
   -----------------------------------------          
end                                   
                                
                                
 SELECT  @NewGenerateId = @CustomerRequirementId                                      
                                       
END   
GO


