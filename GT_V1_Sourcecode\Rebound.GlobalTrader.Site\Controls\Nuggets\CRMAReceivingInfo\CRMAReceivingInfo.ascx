<%@ Control Language="C#" CodeBehind="CRMAReceivingInfo.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Nuggets.CRMAReceivingInfo" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Nugget:DesignBase ID="ctlDB" runat="server" TitleText="Receiving Information" BoxType="Standard">
	<Content>
		<table class="twoCols">
			<tr>
				<td class="col1">
					<table cellpadding="0" cellspacing="0" border="0" class="dataItems">
						<ReboundUI:DataItemRow id="hidNo" runat="server" FieldType="Hidden" />
						<ReboundUI:DataItemRow id="ctlCustomerName" runat="server" ResourceTitle="Customer" />
						<ReboundUI:DataItemRow id="hidCustomer" runat="server" FieldType="Hidden" />
						<ReboundUI:DataItemRow id="hidCustomerNo" runat="server" FieldType="Hidden" />
						<ReboundUI:DataItemRow id="ctlWarehouse" runat="server" ResourceTitle="Warehouse" />
						<ReboundUI:DataItemRow id="hidWarehouseNo" runat="server" FieldType="Hidden" />
						<ReboundUI:DataItemRow id="ctlContact" runat="server" ResourceTitle="Contact" />
						<ReboundUI:DataItemRow id="hidContactNo" runat="server" FieldType="Hidden" />
						<ReboundUI:DataItemRow id="ctlDivision" runat="server" ResourceTitle="Division" />
						<ReboundUI:DataItemRow id="hidDivisionNo" runat="server" FieldType="Hidden" />
						<ReboundUI:DataItemRow id="ctlAuthoriser" runat="server" ResourceTitle="AuthorisedBy" />
						<ReboundUI:DataItemRow id="hidAuthorisedBy" runat="server" FieldType="Hidden" />
						<ReboundUI:DataItemRow id="ctlRMADate" runat="server" ResourceTitle="RMADate" />
						<ReboundUI:DataItemRow id="ctlInvoice" runat="server" ResourceTitle="InvoiceNo" />
						<ReboundUI:DataItemRow id="hidInvoiceNo" runat="server" FieldType="Hidden" />
						<ReboundUI:DataItemRow id="ctlSalesOrder" runat="server" ResourceTitle="SalesOrderNo" />
						<ReboundUI:DataItemRow id="hidSalesOrderNo" runat="server" FieldType="Hidden" />
						<ReboundUI:DataItemRow id="ctlCurrency" runat="server" ResourceTitle="Currency" />
						<ReboundUI:DataItemRow id="hidCurrencyNo" runat="server" FieldType="Hidden" />
					</table>
				</td>
				<td class="col2">
					<table cellpadding="0" cellspacing="0" border="0" class="dataItems">
						<ReboundUI:DataItemRow id="ctlShippingAccountNo" runat="server" ResourceTitle="ShippingAccountNo" />
						<ReboundUI:DataItemRow id="ctlShipVia" runat="server" ResourceTitle="ShipVia" />
						<ReboundUI:DataItemRow id="hidShipViaNo" runat="server" FieldType="Hidden" />
						<ReboundUI:DataItemRow id="ctlInstructions" runat="server" ResourceTitle="Instructions" />
						<ReboundUI:DataItemRow id="ctlNotes" runat="server" ResourceTitle="CustomerNotes" />
                         <ReboundUI:DataItemRow id="hidGlobalClientNo" runat="server" FieldType="Hidden" />
					</table>
				</td>
			</tr>
		</table>
		<ReboundUI:FieldSet ID="fldReceived" runat="server" FieldSetType="CRMAReceived" IsInitiallyRolledUp="false">
			<Title><%=Functions.GetGlobalResource("Misc", "Received")%></Title>
			<Content><ReboundUI:FlexiDataTable ID="tblReceived" runat="server" /></Content>
		</ReboundUI:FieldSet>
	</Content>
	
</ReboundUI_Nugget:DesignBase>
