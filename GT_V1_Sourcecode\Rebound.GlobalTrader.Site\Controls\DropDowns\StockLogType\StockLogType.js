Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");Rebound.GlobalTrader.Site.Controls.DropDowns.StockLogType=function(n){Rebound.GlobalTrader.Site.Controls.DropDowns.StockLogType.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.DropDowns.StockLogType.prototype={initialize:function(){Rebound.GlobalTrader.Site.Controls.DropDowns.StockLogType.callBaseMethod(this,"initialize");this.addSetupDataCall(Function.createDelegate(this,this.setupDataCall));this.addGetDataOK(Function.createDelegate(this,this.dataCallOK))},dispose:function(){this.isDisposed||Rebound.GlobalTrader.Site.Controls.DropDowns.StockLogType.callBaseMethod(this,"dispose")},setupDataCall:function(){this._objData.set_PathToData("controls/DropDowns/StockLogType");this._objData.set_DataObject("StockLogType");this._objData.set_DataAction("GetData")},dataCallOK:function(){var t=this._objData._result,n;if(t.Types)for(n=0;n<t.Types.length;n++)this.addOption(t.Types[n].Name,t.Types[n].ID)}};Rebound.GlobalTrader.Site.Controls.DropDowns.StockLogType.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.StockLogType",Rebound.GlobalTrader.Site.Controls.DropDowns.Base);