Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.ClientImportBOMMainInfo_Release=function(n){Rebound.GlobalTrader.Site.Controls.Forms.ClientImportBOMMainInfo_Release.initializeBase(this,[n]);this._intBOMID=-1;this._UpdatedBy=null};Rebound.GlobalTrader.Site.Controls.Forms.ClientImportBOMMainInfo_Release.prototype={get_intBOMID:function(){return this._intBOMID},set_intBOMID:function(n){this._intBOMID!==n&&(this._intBOMID=n)},get_BomCode:function(){return this._BomCode},set_BomCode:function(n){this._BomCode!==n&&(this._BomCode=n)},get_BomName:function(){return this._BomName},set_BomName:function(n){this._BomName!==n&&(this._BomName=n)},get_BomCompanyName:function(){return this._BomCompanyName},set_BomCompanyName:function(n){this._BomCompanyName!==n&&(this._BomCompanyName=n)},get_BomCompanyNo:function(){return this._BomCompanyNo},set_BomCompanyNo:function(n){this._BomCompanyNo!==n&&(this._BomCompanyNo=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Forms.ClientImportBOMMainInfo_Release.callBaseMethod(this,"initialize");this.addShown(Function.createDelegate(this,this.formShown))},dispose:function(){this.isDisposed||(this._intBOMID=null,Rebound.GlobalTrader.Site.Controls.Forms.ClientImportBOMMainInfo_Release.callBaseMethod(this,"dispose"))},formShown:function(){this._blnFirstTimeShown&&(this._ctlRelease=this.getFieldComponent("ctlRelease"),this._ctlRelease.addClickYesEvent(Function.createDelegate(this,this.yesClicked)),this._ctlRelease.addClickNoEvent(Function.createDelegate(this,this.noClicked)))},yesClicked:function(){this.showSaving(!0);var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("Controls/Nuggets/BOMMainInfo");n.set_DataObject("BOMMainInfo");n.set_DataAction("BOMReleaseRequirement");n.addParameter("id",this._intBOMID);n.addParameter("BomCode",this._BomCode);n.addParameter("BomName",this._BomName);n.addParameter("BomCompanyName",this._BomCompanyName);n.addParameter("BomCompanyNo",this._BomCompanyNo);n.addParameter("UpdatedBy",this._UpdatedBy);n.addDataOK(Function.createDelegate(this,this.saveReleaseComplete));n.addError(Function.createDelegate(this,this.saveError));n.addTimeout(Function.createDelegate(this,this.saveError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null;this.onSave()},noClicked:function(){this.showSaving(!1);this.onNotConfirmed()},saveError:function(n){this.showSaving(!1);this._strErrorMessage=n._errorMessage;this.onSaveError()},saveReleaseComplete:function(n){n._result.Result==!0?(this.showSavedOK(!0),this.onSaveComplete()):(this._strErrorMessage=n._errorMessage,this.onSaveError())}};Rebound.GlobalTrader.Site.Controls.Forms.ClientImportBOMMainInfo_Release.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.ClientImportBOMMainInfo_Release",Rebound.GlobalTrader.Site.Controls.Forms.Base,Sys.IDisposable);