Type.registerNamespace("Rebound.GlobalTrader.Site.Controls");Rebound.GlobalTrader.Site.Controls.FlexiDataTableSorting=function(){};var $R_TABLESORT=Rebound.GlobalTrader.Site.Controls.FlexiDataTableSorting;$R_TABLESORT._objSortData={};Rebound.GlobalTrader.Site.Controls.FlexiDataTableSorting.dispose=function(){$R_TABLESORT.isDisposed||($R_TABLESORT._objSortData=null,$R_TABLESORT.isDisposed=!0)};Rebound.GlobalTrader.Site.Controls.FlexiDataTableSorting.registerTable=function(n){$R_TABLESORT._objSortData||($R_TABLESORT._objSortData=[]);$R_TABLESORT.resetSort(n)};Rebound.GlobalTrader.Site.Controls.FlexiDataTableSorting.doSort=function(n,t){var r,i,u,o,f,e,s;if(($R_TABLESORT.resetSort(n),r=$find(n),r)&&(i=$R_TABLESORT._objSortData[n],i)){for(u=0,o=r._tbl.rows.length;u<o;u++){for(Array.add(i.SortData,{I:u,V:$R_FN.splitDoubleCellValue(r._tbl.rows[u].cells[t].innerHTML)[0]}),f=[],e=0,s=r._tbl.rows[u].cells.length;e<s;e++)Array.add(f,r._tbl.rows[u].cells[e].innerHTML);Array.add(i.TableData,{D:f,ID:r._aryValues[u],X:r._aryExtraData[u],CSS:r._tbl.rows[u].cells[0].className.indexOf(" ")>0?r._tbl.rows[u].cells[0].className.split(" ")[1]:""});f=null}r.clearTable();switch(r._aryColumnClientSortFormat[t]){case $R_ENUM$ClientSortFormat.Date:i.SortData=i.SortData.sort($R_TABLESORT.doSort_Date);break;case $R_ENUM$ClientSortFormat.DateAndTime:i.SortData=i.SortData.sort($R_TABLESORT.doSort_DateTime);break;case $R_ENUM$ClientSortFormat.Numeric:case $R_ENUM$ClientSortFormat.Currency:i.SortData=i.SortData.sort($R_TABLESORT.doSort_Numeric);break;case $R_ENUM$ClientSortFormat.Text:default:i.SortData=i.SortData.sort($R_TABLESORT.doSort_Text)}r._enmSortDirection==$R_ENUM$SortColumnDirection.DESC&&i.SortData.reverse();i.RowsAdded=0;$R_TABLESORT.addRow(n);r=null;i=null;fnAdd=null;fnFinish=null}};Rebound.GlobalTrader.Site.Controls.FlexiDataTableSorting.addRow=function(n){var t=$R_TABLESORT._objSortData[n],i,r;t&&(i=$find(n),i)&&(t.RowsAdded<t.SortData.length?(r=t.TableData[t.SortData[t.RowsAdded].I],t.RowsAdded+=1,i.addRow(r.D,r.ID,!1,r.X,r.CSS),setTimeout(function(){$R_TABLESORT.addRow(n)},0)):(i.resizeColumns(),$R_TABLESORT.resetSort(n)),i=null)};Rebound.GlobalTrader.Site.Controls.FlexiDataTableSorting.resetSort=function(n){$R_TABLESORT._objSortData[n]={SortData:[],TableData:[],RowsAdded:0}};Rebound.GlobalTrader.Site.Controls.FlexiDataTableSorting.doSort_Text=function(n,t){var i=n.V.toLowerCase(),r=t.V.toLowerCase();return i<r?-1:i==r?0:i>r?1:void 0};Rebound.GlobalTrader.Site.Controls.FlexiDataTableSorting.doSort_Numeric=function(n,t){var u=new RegExp(String.format("[^0-9{0}]| ",Sys.CultureInfo.CurrentCulture.numberFormat.NumberDecimalSeparator),"gi"),i=Number.parseLocale(n.V.replace(u,"")),r=Number.parseLocale(t.V.replace(u,""));return i<r?-1:i==r?0:i>r?1:void 0};Rebound.GlobalTrader.Site.Controls.FlexiDataTableSorting.doSort_Date=function(n,t){var i=Date.parseLocale(n.V),r=Date.parseLocale(t.V);return i<r?-1:i==r?0:i>r?1:void 0};Rebound.GlobalTrader.Site.Controls.FlexiDataTableSorting.doSort_DateTime=function(n,t){var u=n.V.split(" "),i=$R_FN.getDateFromDateAndTime(u[0],u[1]),f=t.V.split(" "),r=$R_FN.getDateFromDateAndTime(f[0],f[1]);return i<r?-1:i==r?0:i>r?1:void 0};Rebound.GlobalTrader.Site.Controls.FlexiDataTableSorting.registerClass("Rebound.GlobalTrader.Site.Controls.FlexiDataTableSorting");