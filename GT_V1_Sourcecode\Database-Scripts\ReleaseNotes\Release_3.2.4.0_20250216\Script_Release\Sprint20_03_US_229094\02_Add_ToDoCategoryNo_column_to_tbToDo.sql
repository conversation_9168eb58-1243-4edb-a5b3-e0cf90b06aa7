﻿/*   
===========================================================================================  
TASK			UPDATED BY      DATE			ACTION		DESCRIPTION  
[US-229094]		Phuc Hoang		20-Jan-2025		Create		Quote - Add automatic Task Reminder based on Quote Status
===========================================================================================  
*/

GO

IF COL_LENGTH('tbToDo','ToDoCategoryNo') IS NULL
BEGIN
	ALTER TABLE [dbo].[tbToDo] ADD [ToDoCategoryNo] [int] NULL;
END

GO

IF COL_LENGTH('tbToDo','QuoteNo') IS NULL
BEGIN
	ALTER TABLE [dbo].[tbToDo] ADD [QuoteNo] [int] NULL;
END

GO

IF COL_LENGTH('tbToDo','DailyReminder ') IS NULL
BEGIN
	ALTER TABLE [dbo].[tbToDo] ADD [DailyReminder] [bit] NULL;
END

GO

--Change current ToDo records to ToDoCategoryNo = Company
GO
UPDATE [dbo].[tbToDo] 
SET [ToDoCategoryNo] = 1
WHERE ISNULL([ToDoCategoryNo], 0) = 0