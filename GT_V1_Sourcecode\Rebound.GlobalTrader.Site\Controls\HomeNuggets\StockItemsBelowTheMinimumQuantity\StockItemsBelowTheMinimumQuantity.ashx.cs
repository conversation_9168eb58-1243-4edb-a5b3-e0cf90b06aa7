using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;

namespace Rebound.GlobalTrader.Site.Controls.HomeNuggets.Data {
	[WebService(Namespace = "http://tempuri.org/")]
	[WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
	public class StockItemsBelowTheMinimumQuantity : Rebound.GlobalTrader.Site.Data.Base {

		public override void ProcessRequest(HttpContext context) {
			if (base.init(context)) {
				switch (Action) {
					case "GetData": GetData(); break;
					default: WriteErrorActionNotFound(); break;
				}
			}
		}

		/// <summary>
		/// Gets the main data
		/// </summary>
		private void GetData() {
            try
            {
                List<SalesOrder> lst = SalesOrder.GetListOpenForLogin(LoginID, RowCount);
                if (lst == null)
                {
                    WriteErrorDataNotFound();
                }
                else
                {
                    //stock
                    JsonObject jsn = new JsonObject();
                    JsonObject jsnItems = new JsonObject(true);
                    for (int i = 0; i < lst.Count; i++)
                    {
                        JsonObject jsnItem = new JsonObject();
                        jsnItem.AddVariable("ID", lst[i].SalesOrderId);
                        jsnItem.AddVariable("No", lst[i].SalesOrderNumber);
                        jsnItem.AddVariable("Due", Functions.FormatDate(lst[i].DatePromised));
                        jsnItem.AddVariable("CM", lst[i].CompanyName);
                        jsnItem.AddVariable("CMNo", lst[i].CompanyNo);
                        jsnItems.AddVariable(jsnItem);
                        jsnItem.Dispose();
                        jsnItem = null;
                    }
                    jsn.AddVariable("Stock", jsnItems);
                    jsn.AddVariable("Count", lst.Count);
                    jsnItems.Dispose();
                    jsnItems = null;

                    OutputResult(jsn);
                    jsn.Dispose();
                    jsn = null;
                }
                lst = null;
            }
            catch (Exception ex)
            {
                Errorlog obj = new Errorlog();
                obj.LogMessage(Convert.ToString("Exception- " + ex.Message + " " + ex.StackTrace));
            }
        }


	}
}
