﻿  
  
--Marker     Changed by      Date         Remarks    
--[001]      Vinay           01/11/2012   Add comma(,) seprated credit notes and CustomerRMA in invoice section    
/*     
===========================================================================================    
TASK			UPDATED BY       DATE				ACTION		DESCRIPTION    
[US-225812]     Trung Pham		 20-Apr-2025		UPDATE		Get country and related warning message 
===========================================================================================    
*/    
CREATE OR ALTER PROCEDURE [dbo].[usp_select_Invoice]      
    @InvoiceId int        
AS        
BEGIN      
      
    Declare @vSalesOrderNos Varchar(1000)      
    Declare @vSalesOrderNumbers Varchar(1000)      
        
    Declare @vCRMAIDs Varchar(1000)    
    Declare @vCRMANumbers Varchar(1000)    
        
    Declare @vCreditIDs Varchar(1000)    
    Declare @vCreditNumbers Varchar(1000)   
   
   
                      
    Execute usp_select_SalesOrderNos_By_Invoice @InvoiceId, @vSalesOrderNos Out, @vSalesOrderNumbers Out     
        
    Execute usp_select_CustomerRMANos_By_Invoice @InvoiceId,@vCRMAIDs Out,@vCRMANumbers Out    
        
    Execute usp_select_CreditNos_By_Invoice @InvoiceId,@vCreditIDs Out,@vCreditNumbers Out  
   --------------changes------------------  
    declare @ExchangeRate1 float   
    declare @TotalValue float  declare @ShipSurChargeValue float  declare @ShippingSurchargePercent float  select @ExchangeRate1 = dbo.ufn_get_exchange_rate(CurrencyNo, isnull(InvoiceDate,GETDATE())),@ShippingSurchargePercent=ShippingSurchargePercent from
  
  
 tbInvoice where InvoiceId = @InvoiceId   select @TotalValue = dbo.ufn_calculate_InvoicePriceForSurcharge(@InvoiceId)/@ExchangeRate1     set @ShipSurChargeValue =round( ((@TotalValue*isnull(@ShippingSurchargePercent,0))/100),2)  
 --change end------------  
  
    SELECT  *      
    , @vSalesOrderNos As 'SalesOrderNos', @vSalesOrderNumbers As 'SalesOrderNumbers'     
    , @vCRMAIDs As 'CRMAIds',@vCRMANumbers As 'CRMANumbers'      
    , @vCreditIDs As 'CreditIds',@vCreditNumbers As 'CreditNumbers'    
    , (case when isnull(vi.IsAppShippingSurcharge,0)=0 then   @ShipSurChargeValue else vi.ShippingSurchargeValue end) as  AppliedShippingSurcharge  
	, ct.CountryName
	, swm.WarningText
	, CAST(CASE 
		WHEN EXISTS (SELECT 1 FROM tbSystemWarningMessage WHERE SystemWarningMessageId = swm.SystemWarningMessageId )
		THEN 1 ELSE 0 END AS BIT
	) AS IsHasCountryMessage
    FROM    dbo.vwInvoice  vi  
	LEFT JOIN dbo.tbCompany c ON c.CompanyId = vi.CompanyNo
	LEFT JOIN dbo.tbCompanyAddress ca ON c.CompanyId = ca.CompanyNo AND ca.DefaultBilling = 1
	LEFT JOIN dbo.tbAddress bad ON ca.AddressNo = bad.AddressId
	LEFT JOIN dbo.tbCountry ct ON bad.CountryNo = ct.CountryId AND ISNULL(ct.InActive, 0) = 0
	LEFT JOIN dbo.tbSystemWarningMessage swm ON ct.CountryId = swm.ApplyTo AND swm.ClientNo = vi.ClientNo AND ISNULL(swm.InActive, 0) = 0
    WHERE   InvoiceId = @InvoiceId        
END    
    
  
  