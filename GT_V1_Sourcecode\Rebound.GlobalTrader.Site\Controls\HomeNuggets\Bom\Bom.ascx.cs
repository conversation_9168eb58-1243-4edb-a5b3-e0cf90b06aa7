//----------------------------------------------------------------------------------------
// RP 21.12.2009:
//- use SimpleDataTable
//----------------------------------------------------------------------------------------
using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site.Controls;

namespace Rebound.GlobalTrader.Site.Controls.HomeNuggets {
	public partial class Bom : Base {

		protected Panel _pnlReady;
		protected SimpleDataTable _tblReady;
        protected Panel _pnlPartial;
        protected SimpleDataTable _tblPartial; 
        protected Panel _pnlNew; 
        protected SimpleDataTable _tblNew;
        protected Panel _pnlRFQ; 
        protected SimpleDataTable _tblRFQ; 
		protected PageHyperLink _lnkMore;
        protected Panel _pnlClosed;
        protected SimpleDataTable _tblClosed;
        protected Panel _pnlNoBid;
        protected SimpleDataTable _tblNoBid;
		/// <summary>
		/// On init
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
			this.HomePageNuggetType = "Bom";
			base.OnInit(e);
			WireUpControls();
			SetupTables();
            AddScriptReference("Controls.HomeNuggets.Bom.Bom.js");
		}

		protected override void OnLoad(EventArgs e) {
            _scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.HomeNuggets.Bom", this.ctlDesignBase.ClientID);
			_scScriptControlDescriptor.AddElementProperty("pnlReady", _pnlReady.ClientID);
			_scScriptControlDescriptor.AddComponentProperty("tblReady", _tblReady.ClientID);

            _scScriptControlDescriptor.AddElementProperty("pnlPartial", _pnlPartial.ClientID);
            _scScriptControlDescriptor.AddComponentProperty("tblPartial", _tblPartial.ClientID);

            _scScriptControlDescriptor.AddElementProperty("pnlNew", _pnlNew.ClientID);
            _scScriptControlDescriptor.AddComponentProperty("tblNew", _tblNew.ClientID);

            _scScriptControlDescriptor.AddElementProperty("pnlRFQ", _pnlRFQ.ClientID);
            _scScriptControlDescriptor.AddComponentProperty("tblRFQ", _tblRFQ.ClientID);

            _scScriptControlDescriptor.AddElementProperty("pnlClosed", _pnlClosed.ClientID);
            _scScriptControlDescriptor.AddComponentProperty("tblClosed", _tblClosed.ClientID);

            _scScriptControlDescriptor.AddElementProperty("pnlNoBid", _pnlNoBid.ClientID);
            _scScriptControlDescriptor.AddComponentProperty("tblNoBid", _tblNoBid.ClientID);

			_scScriptControlDescriptor.AddElementProperty("pnlMore", FindContentControl("pnlMore").ClientID);
			_lnkMore.AddQueryStringVariable(QueryStringManager.QueryStringVariables.BypassSavedState, true);
			base.OnLoad(e);
		}

		private void SetupTables() {
			_tblReady.Columns.Add(new SimpleDataColumn("Name"));
            _tblReady.Columns.Add(new SimpleDataColumn(SessionManager.IsPOHub == true ? "Client" : "Company", Unit.Pixel(65)));
			_tblReady.Columns.Add(new SimpleDataColumn("RequestedDate", Unit.Pixel(75)));
            _tblReady.Columns.Add(new SimpleDataColumn("QuoteRequired", Unit.Pixel(65)));

            _tblPartial.Columns.Add(new SimpleDataColumn("Name"));
            _tblPartial.Columns.Add(new SimpleDataColumn(SessionManager.IsPOHub == true ? "Client" : "Company", Unit.Pixel(65)));
            _tblPartial.Columns.Add(new SimpleDataColumn("RequestedDate", Unit.Pixel(75)));
            _tblPartial.Columns.Add(new SimpleDataColumn("QuoteRequired", Unit.Pixel(65)));

            _tblNew.Columns.Add(new SimpleDataColumn("Name"));
            _tblNew.Columns.Add(new SimpleDataColumn(SessionManager.IsPOHub == true ? "Client" : "Company", Unit.Pixel(65)));
            _tblNew.Columns.Add(new SimpleDataColumn("RequestedDate", Unit.Pixel(75)));
            _tblNew.Columns.Add(new SimpleDataColumn("QuoteRequired", Unit.Pixel(65)));

            _tblRFQ.Columns.Add(new SimpleDataColumn("Name"));
            _tblRFQ.Columns.Add(new SimpleDataColumn(SessionManager.IsPOHub == true ? "Client" : "Company", Unit.Pixel(65)));
            _tblRFQ.Columns.Add(new SimpleDataColumn("RequestedDate", Unit.Pixel(75)));
            _tblRFQ.Columns.Add(new SimpleDataColumn("QuoteRequired", Unit.Pixel(65)));

            _tblClosed.Columns.Add(new SimpleDataColumn("Name"));
            _tblClosed.Columns.Add(new SimpleDataColumn(SessionManager.IsPOHub == true ? "Client" : "Company", Unit.Pixel(65)));
            _tblClosed.Columns.Add(new SimpleDataColumn("RequestedDate", Unit.Pixel(75)));
            _tblClosed.Columns.Add(new SimpleDataColumn("QuoteRequired", Unit.Pixel(65)));

            _tblNoBid.Columns.Add(new SimpleDataColumn("Name"));
            _tblNoBid.Columns.Add(new SimpleDataColumn(SessionManager.IsPOHub == true ? "Client" : "Company", Unit.Pixel(65)));
            _tblNoBid.Columns.Add(new SimpleDataColumn("RequestedDate", Unit.Pixel(75)));
            _tblNoBid.Columns.Add(new SimpleDataColumn("QuoteRequired", Unit.Pixel(65)));

			_lnkMore = (PageHyperLink)FindContentControl("lnkMore");
		}

		private void WireUpControls() {
			_pnlReady = (Panel)FindContentControl("pnlReady");
			_tblReady = (SimpleDataTable)FindContentControl("tblReady");

            _pnlPartial = (Panel)FindContentControl("pnlPartial");
            _tblPartial = (SimpleDataTable)FindContentControl("tblPartial");

            _pnlNew = (Panel)FindContentControl("pnlNew");
            _tblNew = (SimpleDataTable)FindContentControl("tblNew");

            _pnlRFQ = (Panel)FindContentControl("pnlRFQ");
            _tblRFQ = (SimpleDataTable)FindContentControl("tblRFQ");

            _pnlClosed = (Panel)FindContentControl("pnlClosed");
            _tblClosed = (SimpleDataTable)FindContentControl("tblClosed");

            _pnlNoBid = (Panel)FindContentControl("pnlNoBid");
            _tblNoBid = (SimpleDataTable)FindContentControl("tblNoBid");
		}
	}
}