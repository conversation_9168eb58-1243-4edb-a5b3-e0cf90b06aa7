﻿//Marker     changed by      date         Remarks
//[001]      Aashu          12/06/2018    Added supplier warranty field
//[002]      <PERSON><PERSON>  30/08/2018    Add ClientCode and ReceivedBy field
//[003]      <PERSON><PERSON>  20/12/2018    Add BOM Status Dropdown Fields.
//[004]      <PERSON><PERSON>  27/12/2018    Showing Client BOM Details Fields.
//[005]      <PERSON><PERSON>  18-Mar-2019    Showing Records Processed and Records Remaining.
using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;

namespace Rebound.GlobalTrader.DAL {

    public class BOMDetails
    {

        #region Constructors

        public BOMDetails() { }

        #endregion

        #region Properties

        /// <summary>
        /// BOMId (from Table)
        /// </summary>
        public System.Int32 BOMId { get; set; }
        /// <summary>
        /// ClientNo (from Table)
        /// </summary>
        public System.Int32 ClientNo { get; set; }
        /// <summary>
        /// BOMName (from usp_select_GoodsInLine)
        /// </summary>
        public System.String BOMName { get; set; }
        public System.Int32? RequesterId { get; set; }
        public System.String BOMIds { get; set; }
        /// <summary>
        /// Cost (from usp_selectAll_Login_Top_Salespersons)
        /// </summary>
        //public System.Double? Cost { get; set; }
        /// <summary>
        /// CurrencyNo (from usp_selectAll_Allocation_for_CustomerRMALine)
        /// </summary>
        //public System.Int32? CurrencyNo { get; set; }
        /// <summary>
        /// OnHold (from Table)
        /// </summary>
        //public System.Boolean OnHold { get; set; }
        /// <summary>
        /// Consignment (from Table)
        /// </summary>
        //public System.Boolean Consignment { get; set; }
        /// <summary>
        /// Notes (from usp_select_Address_DefaultBilling_for_Company)
        /// </summary>
        public System.String Notes { get; set; }
        /// <summary>
        /// BOMCode (from Table)
        /// </summary>
        public System.String BOMCode { get; set; }
        /// <summary>
        /// Inactive (from Table)
        /// </summary>
        public System.Boolean Inactive { get; set; }
        /// <summary>
        /// UpdatedBy (from Table)
        /// </summary>
        public System.Int32? UpdatedBy { get; set; }
        /// <summary>
        /// DLUP (from Table)
        /// </summary>
        public System.DateTime DLUP { get; set; }
        /// <summary>
        /// CurrencyCode (from usp_list_Activity_by_Client_with_filter)
        /// </summary>
        //public System.String CurrencyCode { get; set; }
        /// <summary>
        /// StockCount (from usp_datalistnugget_Lot)
        /// </summary>
        //public System.Int32? StockCount { get; set; }
        /// <summary>
        /// RowNum (from usp_list_Activity_by_Client_with_filter)
        /// </summary>
        public System.Int64? RowNum { get; set; }


        public System.DateTime? RequiredDate { get; set; }
        public System.String RequiredDateStatus { get; set; }

        public System.String ExpediteNotes { get; set; }
        /// <summary>
        /// RowCnt (from usp_list_Activity_by_Client_with_filter)
        /// </summary>
        public System.Int32? RowCnt { get; set; }
        /// <summary> 
        /// Company No
        /// </summary>
        public System.Int32? CompanyNo { get; set; }
        /// <summary>
        ///CompanyName
        ///</summary>
        public System.String CompanyName { get; set; }
        /// <summary>
        ///CompanyType
        ///</summary>
        public System.String CompanyType { get; set; }
        /// <summary> 
        /// Contact No
        /// </summary>
        public System.Int32? ContactNo { get; set; }
        public System.String Part { get; set; }
        public System.Int32? Quantity { get; set; }

        public System.Int32? CustomerRequirementId { get; set; }
        /// <summary>
        ///ContactName
        ///</summary>
        public System.String ContactName { get; set; }
        /// <summary>
        ///BOM Status
        ///</summary>
        public System.String BOMStatus { get; set; }
        public System.Int32? UpdateRequirement { get; set; }
        public System.Int32? RequestToPOHubBy { get; set; }
        public System.DateTime? DateRequestToPOHub { get; set; }
        public System.Int32? ReleaseBy { get; set; }
        public System.DateTime? DateRelease { get; set; }
        public System.Int32? BomCount { get; set; }
        public System.String CurrencyCode { get; set; }
        public int? StatusValue { get; set; }
        public System.Int32? CurrencyNo { get; set; }
        public System.String Currency_Code { get; set; }
        public string ClientName { get; set; }
        public string CurrentSupplier { get; set; }
        public System.DateTime? QuoteRequired { get; set; }
        public string UpdatedByList { get; set; }
        public double TotalBomLinePrice { get; set; }
        public int POCurrencyNo { get; set; }
        public System.Int32? AllItemHasSourcing { get; set; }
        public System.String AssignedUserIds { get; set; }
        public System.Boolean? AS9120 { get; set; }
        public string Releasedby { get; set; }
        public string Requestedby { get; set; }
        public string AssignedUser { get; set; }
        public int NoBidCount { get; set; }
        public System.String DivisionName { get; set; }

        public System.Int32? UpdateByPH { get; set; }
        public System.String ValidationMessage { get; set; }
        public System.Boolean IsReqInValid { get; set; }
        public System.Int32? Contact2Id { get; set; }
        public System.String Contact2Name { get; set; }
        /// <summary>
        ///SupplierWarranty
        ///</summary>
        public System.Int32? SupplierWarranty { get; set; }
        public System.String ClientCode { get; set; }//[002]
        public System.String ReceivedBy { get; set; }//[002]
        public System.String SalesmanName { get; set; }

        public System.Int32? BOMStatusId { get; set; }//[003]
        public System.String BOMStatusName { get; set; }//[003]



        public System.Int32 ClientBOMId  { get; set; }//[004]
        public System.String ClientBOMCode  { get; set; }//[004]
        public System.String  ClientBOMName  { get; set; }//[004]
        public System.String Salesman { get; set; }//[004]
        public System.DateTime? ClosedDate { get; set; }//[004]
        public System.DateTime? ImportDate { get; set; }//[004]
        public System.String CurrencyName { get; set; }//[004]
        public System.Int32? SalesmanId { get; set; }//[004]
        public System.Int32? RecordsProcessed { get; set; }//[005]  
        public System.Int32? RecordsRemaining { get; set; }//[005]  
        public System.Int32? Status { get; set; }
        public System.String ReqSalesPerson { get; set; }
        /// <summary>
        /// SupportTeamMemberNo
        /// </summary>
        public System.Int32? SupportTeamMemberNo { get; set; }
        /// <summary>
        /// SupportTeamMemberName
        /// </summary>
        public System.String SupportTeamMemberName { get; set; }
        /// <summary>
        /// SupportTeamMemberNo
        /// </summary>
        public System.String SupportTeamMemberNoAsString { get; set; }
        public System.Int32? CustomerRequirementNo { get; set; }
        public System.String Manufacturer { get; set; }

        public System.String AS6081 { get; set; }
        /// <summary>
        /// PurchasingNotes
        /// </summary>
        public System.String PurchasingNotes { get; set; }

        public System.Int32 PVVQuestionId { get; set; }
        public System.String PVVQuestionName { get; set; }
        public System.Int32 PVVAnswerId { get; set; }
        public System.String PVVAnswerName { get; set; }
        public System.Int32 PVVQuestionNo { get; set; }
        public System.String HUBRFQNo { get; set; }

        public System.String PVVBOMValidateMessage { get; set; }
        public System.Boolean PVVBOMCountValid { get; set; }
        public System.Boolean IsFromProspectiveOffer { get; set; }
        public System.String UploadedBy { get; set; }
        public System.Double? TargetSellPrice { get; set; }
        #endregion


    }
}
