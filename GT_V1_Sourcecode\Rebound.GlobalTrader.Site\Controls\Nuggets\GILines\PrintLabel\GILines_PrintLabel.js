Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.GILines_PrintLabel=function(n){Rebound.GlobalTrader.Site.Controls.Forms.GILines_PrintLabel.initializeBase(this,[n]);this._intGIID=0;this._intLineID=-1;this._ComboExtraText=""};Rebound.GlobalTrader.Site.Controls.Forms.GILines_PrintLabel.prototype={get_intGIID:function(){return this._intGIID},set_intGIID:function(n){this._intGIID!==n&&(this._intGIID=n)},get_intQuantity:function(){return this._intQuantity},set_intQuantity:function(n){this._intQuantity!==n&&(this._intQuantity=n)},get_intPrintCount:function(){return this._intPrintCount},set_intPrintCount:function(n){this._intPrintCount!==n&&(this._intPrintCount=n)},get_strNiceLableURL:function(){return this._strNiceLableURL},set_strNiceLableURL:function(n){this._strNiceLableURL!==n&&(this._strNiceLableURL=n)},get_IsNiceLabelText:function(){return this._IsNiceLabelText},set_IsNiceLabelText:function(n){this._IsNiceLabelText!==n&&(this._IsNiceLabelText=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Forms.GILines_PrintLabel.callBaseMethod(this,"initialize");this.addShown(Function.createDelegate(this,this.formShown));this._IsNiceLabelText?this.addSave(Function.createDelegate(this,this.saveClicked)):this.addSave(Function.createDelegate(this,this.saveClickedNew))},formShown:function(){this.validateCustomer();$find(this.getField("ctlCustomer").ControlID)&&$find(this.getField("ctlCustomer").ControlID)._aut.addSelectionMadeEvent(Function.createDelegate(this,this.validateCustomer));this.storeOriginalFieldValues();this.getFieldDropDownData("ctlMsl");this.getFieldDropDownData("ctlCountryOfManufacture")},dispose:function(){this.isDisposed||(this._intGIID=null,this._intLineID=null,this._intQuantity=null,this._intPrintCount=null,this._ComboExtraText=null,this._strNiceLableURL=null,Rebound.GlobalTrader.Site.Controls.Forms.GILines_PrintLabel.callBaseMethod(this,"dispose"))},saveClicked:function(){if(this.validateForm()){var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/GILines");n.set_DataObject("GILines");n.set_DataAction("PrintLabel");n.addParameter("id",this._intLineID);n.addParameter("Quantity",this.getFieldValue("ctlQuantity"));n.addParameter("NoOfPrint",this.getFieldValue("ctlNoOfPrint"));n.addParameter("MSLLevel",this.getFieldValue("ctlMsl"));n.addParameter("Customer",this.getFieldValue("ctlCustomerhid"));n.addParameter("CustomerPart",this.getFieldValue("ctlCustomerPartNo"));n.addParameter("DateCode",this.getFieldValue("ctlDateCode"));n.addParameter("DatePicked",$R_FN.shortYear(this.getFieldValue("ctlDatePicked")));n.addParameter("InspectedBy",this.getFieldValue("ctlInspectedBy"));n.addParameter("Manufacturer",this.getFieldValue("ctlManufacturer"));n.addParameter("PartNo",this.getFieldValue("ctlPartNo"));n.addParameter("SalesOrderNo",this.getFieldValue("ctlSalesOrderNo"));n.addParameter("Rohs",this.getFieldDropDownText("ctlROHS"));n.addParameter("Printer",this.getFieldDropDownText("ctlPrinter"));n.addParameter("CustomerPO",this.getFieldValue("ctlPONumber"));n.addParameter("MfgCountry",this.getFieldDropDownText("ctlCountryOfManufacture"));n.addDataOK(Function.createDelegate(this,this.saveEditComplete));n.addError(Function.createDelegate(this,this.saveEditError));n.addTimeout(Function.createDelegate(this,this.saveEditError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null}},saveEditError:function(n){this._strErrorMessage=n._errorMessage;this.onSaveError()},saveEditComplete:function(n){n._result.Result==!0?(this.showFormWithoutError(!0,""),alert($R_RES.LabelPrintMessage)):this.showError(!0,n._result.Message)},saveClickedNew:function(){if(this.validateForm()){var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/GILines");n.set_DataObject("GILines");n.set_DataAction("PrintLabelNew");n.addParameter("id",this._intLineID);n.addParameter("Quantity",this.getFieldValue("ctlQuantity"));n.addParameter("NoOfPrint",this.getFieldValue("ctlNoOfPrint"));n.addParameter("MSLLevel",this.getFieldValue("ctlMsl"));n.addParameter("Customer",this.getFieldValue("ctlCustomerhid"));n.addParameter("CustomerPart",this.getFieldValue("ctlCustomerPartNo"));n.addParameter("DateCode",this.getFieldValue("ctlDateCode"));n.addParameter("DatePicked",$R_FN.shortYear(this.getFieldValue("ctlDatePicked")));n.addParameter("InspectedBy",this.getFieldValue("ctlInspectedBy"));n.addParameter("Manufacturer",this.getFieldValue("ctlManufacturer"));n.addParameter("PartNo",this.getFieldValue("ctlPartNo"));n.addParameter("SalesOrderNo",this.getFieldValue("ctlSalesOrderNo"));n.addParameter("Rohs",this.getFieldDropDownText("ctlROHS"));n.addParameter("Printer",this.getFieldDropDownText("ctlPrinter"));n.addParameter("CustomerPO",this.getFieldValue("ctlPONumber"));n.addParameter("MfgCountry",this.getFieldDropDownText("ctlCountryOfManufacture"));n.addDataOK(Function.createDelegate(this,this.saveEditComplete));n.addError(Function.createDelegate(this,this.saveEditError));n.addTimeout(Function.createDelegate(this,this.saveEditError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null}},saveClickedAPI:function(){if(this.validateForm()){var t="Type=GoodsInLabel&NoOfPrint="+this.getFieldValue("ctlNoOfPrint")+"&PrinterName="+this.getFieldDropDownText("ctlPrinter")+"&Customer="+this.getFieldValue("ctlCustomerhid")+"&SalesOrderNumber="+this.getFieldValue("ctlSalesOrderNo")+"&PartNo="+this.getFieldValue("ctlPartNo")+"&Quantity="+this.getFieldValue("ctlQuantity")+"&CustomerPartNo="+this.getFieldValue("ctlCustomerPartNo")+"&DateCode="+this.getFieldValue("ctlDateCode")+"&Manufacturer="+this.getFieldValue("ctlManufacturer")+"&DatePicked="+this.getFieldValue("ctlDatePicked")+"&InspectedBy="+this.getFieldValue("ctlInspectedBy")+"&MSLLevel="+this.getFieldValue("ctlMsl")+"&ROHSStatus="+this.getFieldDropDownText("ctlROHS")+"&LabelName=GI Label.nlbl&CustomerPO="+this.getFieldValue("ctlPONumber"),n=new Sys.Net.WebRequest;n.set_url(this._strNiceLableURL);n.set_httpVerb("POST");n.set_body(t);n.add_completed(Function.createDelegate(this,this.getDataComplete));n.invoke()}},getDataComplete:function(){this.showFormWithoutError(!0,"");alert($R_RES.LabelPrintMessage)},validateForm:function(){this.onValidate();var n=this.autoValidateFields();return this.getFieldValue("ctlCustomerhid").length>30&&(n=!1,this.showError(!0,$R_RES.CustomerMessage)),this.getFieldValue("ctlInspectedBy").length>12&&(n=!1,this.showError(!0,$R_RES.InspectedBy)),this.getFieldValue("ctlDateCode").length>9&&(n=!1,this.showError(!0,$R_RES.DateCode)),this.getFieldValue("ctlCustomerPartNo").length>30&&(n=!1,this.showError(!0,$R_RES.CustomerPart)),this.getFieldValue("ctlPONumber").length>15&&(n=!1,this.showError(!0,"Customer PO cannot exceed more than 15 characters. Please edit the Customer PO manually to proceed.")),n},validateCustomer:function(){var n=this.getFieldComboText("ctlCustomer");n==null&&(n="");this.setFieldValue("ctlCustomerhid",n);n.length>30?(this.showError(!0,$R_RES.CustomerMessage),this.showField("ctlCustomer",!1),this.showField("ctlCustomerhid",!0)):(this.showField("ctlCustomer",!0),this.showField("ctlCustomerhid",!1))},getLotCode:function(){this._ComboExtraText=$find(this.getField("ctlLotNo").ControlID)._strSelectedExtraText},getLot:function(){return $find(this.getField("ctlLotNo").ControlID)._aut._varSelectedID==null||$find(this.getField("ctlLotNo").ControlID)._aut._varSelectedID==""?$find(this.getField("ctlLotNo").ControlID)._txt.value:this._ComboExtraText}};Rebound.GlobalTrader.Site.Controls.Forms.GILines_PrintLabel.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.GILines_PrintLabel",Rebound.GlobalTrader.Site.Controls.Forms.Base,Sys.IDisposable);