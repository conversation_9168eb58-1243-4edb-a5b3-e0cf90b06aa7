//Marker     changed by     Date         Remarks
//[001]      Vinay         16/11/2016    Logout, if user change login from other browser tab
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using System.Collections;

namespace Rebound.GlobalTrader.Site.Controls.AutoSearch {
	[DefaultProperty("")]
	[ToolboxData("<{0}:Base runat=server></{0}:Base>")]
	public class Base : Panel, IScriptControl, INamingContainer {

		#region Locals

		protected ScriptManager _sm;
		protected ScriptReference[] _srScriptReference = new ScriptReference[5];
		private int _intNumberOfScriptReferences = 0;
		protected ScriptControlDescriptor _scScriptControlDescriptor;
		private Panel _pnlHeader;
		private Panel _pnlClose;
		private HyperLink _hypClose;
		private Panel _pnlResultsOuter;
		private Panel _pnlContainer;
		private Label _lblResults;
		private Panel _pnlLoading;
		private Panel _pnlResults;
		private Label _lblInstructions;
		private Label _lblSelectedValue;
		private HyperLink _hypReselect;
		private Panel _pnlButtonTrigger;
		private HyperLink _hypButtonTrigger;
		protected Rebound.GlobalTrader.Site.AutoSearch _objAutoSearch;

		#endregion

		#region Properties

		/// <summary>
		/// related text box id
		/// </summary>
		private string _strRelatedTextBoxID = "";
		public string RelatedTextBoxID {
			get { return _strRelatedTextBoxID; }
			set { _strRelatedTextBoxID = value; }
		}

		/// <summary>
		/// Height of results box
		/// default: 100px
		/// </summary>
		private Unit _untResultsHeight = Unit.Pixel(100);
		public Unit ResultsHeight {
			get { return _untResultsHeight; }
			set { _untResultsHeight = value; }
		}

		/// <summary>
		/// How many seconds to delay before doing the search
		/// default: 0.5
		/// </summary>
		private double _dblSearchDelay = 0.5;
		public double SearchDelay {
			get { return _dblSearchDelay; }
			set { _dblSearchDelay = value; }
		}

		/// <summary>
		/// How many characters to enter before the search is performed
		/// default: 4
		/// </summary>
		private int _intCharactersToEnterBeforeSearch = 4;
		public int CharactersToEnterBeforeSearch {
			get { return _intCharactersToEnterBeforeSearch; }
			set { _intCharactersToEnterBeforeSearch = value; }
		}

		/// <summary>
		/// How many characters to enter before the search is performed
		/// default: false
		/// </summary>
		private bool _blnTriggerByClickEvent = false;
		public bool TriggerByClickEvent
		{
			get { return _blnTriggerByClickEvent; }
			set { _blnTriggerByClickEvent = value; }
		}

		/// <summary>
		/// What type of action should happen on click of a result?
		/// </summary>
		private AutoSearchResultsActionType _enmResultsActionType = AutoSearchResultsActionType.RaiseEvent;
		public AutoSearchResultsActionType ResultsActionType {
			get { return _enmResultsActionType; }
			set { _enmResultsActionType = value; }
		}

		/// <summary>
		/// Initially selected ID
		/// </summary>
		private string _strInitialSelectedID = null;
		public string InitialSelectedID {
			get { return _strInitialSelectedID; }
			set { _strInitialSelectedID = value; }
		}

		/// <summary>
		/// Should the search be explicitly triggered by a button click?
		/// </summary>
		private bool _blnTriggerByButton = false;
		public bool TriggerByButton {
			get { return _blnTriggerByButton; }
			set { _blnTriggerByButton = value; }
		}

		/// <summary>
		/// Pixels to correctly position the left side of the Trigger Button
		/// Generally you can set this to the same as the width of the control
		/// </summary>
		private int _intTriggerButtonLeftOffset = 0;
		public int TriggerButtonLeftOffset {
			get { return _intTriggerButtonLeftOffset; }
			set { _intTriggerButtonLeftOffset = value; }
		}

        private int? _intPOHubClientNo = null;
        public int? POHubClientNo
        {
            get { return _intPOHubClientNo; }
            set { _intPOHubClientNo = value; }
        }
        private string _strApprovalType;

        public string strApprovalType
        {
            get { return _strApprovalType; }
            set { _strApprovalType = value; }
        }
        private int? _intDocmentId = null;
        public int? intDocmentId
        {
            get { return _intDocmentId; }
            set { _intDocmentId = value; }
        }
        private string _strRelatedTextBoxClientID;

		private bool _blnIsSelectionAllowed = true;
		/// <summary>
		/// To handle the selection on the list created with data
		/// </summary>
		public bool IsSelectionAllowed
		{
			get { return _blnIsSelectionAllowed; }
			set { _blnIsSelectionAllowed = value; }
		}

		#endregion

		#region Overrides

		/// <summary>
		/// OnInit
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
			if (_strRelatedTextBoxID == "") throw new Exception("RelatedTextBoxID must be set on the AutoSearch control");
			((Pages.Base)Page).AddCSSFile("AutoSearch.css");
			AddScriptReference("Controls.AutoSearch._Bases.Base");
			base.OnInit(e);
		}

		/// <summary>
		/// OnPreRender
		/// </summary>
		/// <param name="e"></param>
		protected override void OnPreRender(EventArgs e) {
			TextBox txt = (TextBox)Functions.FindControlRecursive(this.Parent.Parent, _strRelatedTextBoxID);
			_strRelatedTextBoxClientID = txt.ClientID;
			txt.Dispose();
			txt = null;
			Functions.SetCSSVisibility(_pnlButtonTrigger, _blnTriggerByButton);
			if (!this.DesignMode) {
				_sm = ScriptManager.GetCurrent(Page);
				if (_sm == null) throw new HttpException("A ScriptManager control must exist on the current page.");
				_sm.RegisterScriptControl(this);
			}
			base.OnPreRender(e);
		}

		/// <summary>
		/// Render
		/// </summary>
		/// <param name="writer"></param>
		protected override void Render(HtmlTextWriter writer) {
			if (!this.DesignMode) _sm.RegisterScriptDescriptors(this);
			base.Render(writer);
		}

		/// <summary>
		/// create controls
		/// </summary>
		protected override void CreateChildControls() {

			//set some css stuff
			CssClass = "quickSearchWrapper";

			//button if this is to be triggered by a button
			_pnlButtonTrigger = ControlBuilders.CreatePanelInsideParent(this, "buttonTrigger");
			_pnlButtonTrigger.ID = "pnlButtonTrigger";
			if (_intTriggerButtonLeftOffset == 0) _intTriggerButtonLeftOffset = Convert.ToInt32(Width.Value) + 4;
			_pnlButtonTrigger.Attributes["style"] = String.Format("left:{0}px;", _intTriggerButtonLeftOffset);
			_hypButtonTrigger = ControlBuilders.CreateHyperLinkInsideParent(_pnlButtonTrigger, "", "javascript:void(0);");
			ControlBuilders.CreateImageInsideParent(_hypButtonTrigger, "", "~/images/x.gif", 15, 15);

			//selected value
			_lblSelectedValue = ControlBuilders.CreateLabelInsideParent(this);
			Functions.SetCSSVisibility(_lblSelectedValue, false);

			//reselect link
			_hypReselect = ControlBuilders.CreateHyperLinkInsideParent(this, "quickSearchReselect");
			_hypReselect.Text = string.Format("[ {0} ]", Functions.GetGlobalResource("Misc", "Reselect"));
			_hypReselect.NavigateUrl = "javascript:void(0);";
			Functions.SetCSSVisibility(_hypReselect, false);

			//container panel
			_pnlContainer = ControlBuilders.CreatePanel("quickSearchOuter");
			Functions.SetCSSVisibility(_pnlContainer, false);
			_pnlContainer.Width = Width;
			Width = Unit.Empty;
			Panel pnlInner = ControlBuilders.CreatePanelInsideParent(_pnlContainer, "quickSearch");

			//header
			_pnlHeader = ControlBuilders.CreatePanel("header");
			_pnlHeader.ID = "pnlHeader";
			_pnlClose = ControlBuilders.CreatePanelInsideParent(_pnlHeader, "resultsClose");
			_pnlClose.ID = "pnlClose";
			_hypClose = ControlBuilders.CreateHyperLinkInsideParent(_pnlClose, "", "javascript:void(0);", "x");
			_hypClose.ID = "hypClose";
			_lblResults = ControlBuilders.CreateLabelInsideParent(_pnlHeader, "", "0 result(s)");
			_lblResults.ID = "lblResults";
			Functions.SetCSSVisibility(_lblResults, false);
			_lblInstructions = ControlBuilders.CreateLabelInsideParent(_pnlHeader, "instructions");
			ControlBuilders.CreateLiteralInsideParent(_lblInstructions, string.Format("Type {0} chars to search", CharactersToEnterBeforeSearch));
			_lblInstructions.ID = "lblInstructions";
			pnlInner.Controls.Add(_pnlHeader);

			//results
			_pnlResultsOuter = ControlBuilders.CreatePanel("resultsInner");
			_pnlResultsOuter.Height = ResultsHeight;
			_pnlResultsOuter.ID = "pnlResultsOuter";
			Functions.SetCSSVisibility(_pnlResultsOuter, false);
			_pnlResults = ControlBuilders.CreatePanelInsideParent(_pnlResultsOuter);
			_pnlResults.ID = "pnlResults";
			Functions.SetCSSVisibility(_pnlResults, false);
			_pnlLoading = ControlBuilders.CreatePanelInsideParent(_pnlResultsOuter, "quickSearchLoading");
			_pnlLoading.Controls.Add(ControlBuilders.CreateLiteral(Functions.GetGlobalResource("misc", "Loading")));
			pnlInner.Controls.Add(_pnlResultsOuter);

			Controls.Add(_pnlContainer);

			base.CreateChildControls();
		}

		#endregion

		/// <summary>
		/// Add AJAX script reference
		/// </summary>
		/// <param name="sr"></param>
		protected void AddScriptReference(ScriptReference sr) {
			_srScriptReference[_intNumberOfScriptReferences] = sr;
			_intNumberOfScriptReferences += 1;
		}
		protected void AddScriptReference(bool blnDebug, string strAssembly, string strRef) {
			AddScriptReference(Functions.GetScriptReference(blnDebug, strAssembly, strRef, true));
		}
		protected void AddScriptReference(string strAssembly, string strRef) {
			AddScriptReference(false, strAssembly, strRef);
		}
		protected void AddScriptReference(string strRef) {
			bool blnDebug = false;
#if (DEBUG)
			blnDebug = true;
#endif
			AddScriptReference(blnDebug, "Rebound.GlobalTrader.Site", strRef);
		}

		protected void SetAutoSearchType(string strType) {
			_objAutoSearch = Rebound.GlobalTrader.Site.Site.GetInstance().GetAutoSearch(strType);
		}

		#region IScriptControl Members

		protected virtual IEnumerable<ScriptReference> GetScriptReferences() {
			return _srScriptReference;
		}

		protected virtual IEnumerable<ScriptDescriptor> GetScriptDescriptors() {
			_scScriptControlDescriptor.AddElementProperty("pnlContainer", _pnlContainer.ClientID);
			_scScriptControlDescriptor.AddElementProperty("lblResults", _lblResults.ClientID);
			_scScriptControlDescriptor.AddElementProperty("hypClose", _hypClose.ClientID);
			_scScriptControlDescriptor.AddElementProperty("pnlResultsOuter", _pnlResultsOuter.ClientID);
			_scScriptControlDescriptor.AddElementProperty("pnlResults", _pnlResults.ClientID);
			_scScriptControlDescriptor.AddElementProperty("txtRelatedTextBox", _strRelatedTextBoxClientID);
			_scScriptControlDescriptor.AddElementProperty("pnlLoading", _pnlLoading.ClientID);
			_scScriptControlDescriptor.AddElementProperty("lblInstructions", _lblInstructions.ClientID);
			_scScriptControlDescriptor.AddElementProperty("lblSelectedValue", _lblSelectedValue.ClientID);
			_scScriptControlDescriptor.AddElementProperty("hypReselect", _hypReselect.ClientID);
			_scScriptControlDescriptor.AddElementProperty("hypButtonTrigger", _hypButtonTrigger.ClientID);
			_scScriptControlDescriptor.AddProperty("dblSearchDelay", SearchDelay);
			_scScriptControlDescriptor.AddProperty("intCharactersToEnterBeforeSearch", CharactersToEnterBeforeSearch);
			_scScriptControlDescriptor.AddProperty("enmResultsActionType", _enmResultsActionType);
			_scScriptControlDescriptor.AddProperty("varSelectedID", _strInitialSelectedID);
			_scScriptControlDescriptor.AddProperty("blnTriggerByButton", _blnTriggerByButton);
			_scScriptControlDescriptor.AddProperty("evtTriggerByClickEvent", TriggerByClickEvent);
			//[001] code start
			_scScriptControlDescriptor.AddProperty("intCurrentLogin", (int)SessionManager.LoginID);
            //[001] code end

            _scScriptControlDescriptor.AddProperty("intPOHubClientNo", _intPOHubClientNo);
            _scScriptControlDescriptor.AddProperty("strApprovalType", _strApprovalType);
            _scScriptControlDescriptor.AddProperty("intDocmentId", _intDocmentId);

			_scScriptControlDescriptor.AddProperty("blnIsSelectionAllowed", _blnIsSelectionAllowed);

			return new ScriptDescriptor[] { _scScriptControlDescriptor };
		}

		System.Collections.Generic.IEnumerable<ScriptDescriptor> IScriptControl.GetScriptDescriptors() { return GetScriptDescriptors(); }
		System.Collections.Generic.IEnumerable<ScriptReference> IScriptControl.GetScriptReferences() { return GetScriptReferences(); }

		#endregion

		#region Enumerations
		public enum AutoSearchResultsActionType {
			Navigate,
			RaiseEvent
		}
		#endregion

	}
}