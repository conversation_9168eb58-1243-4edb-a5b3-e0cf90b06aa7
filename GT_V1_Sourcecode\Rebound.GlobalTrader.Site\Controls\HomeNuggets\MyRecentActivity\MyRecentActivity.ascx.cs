//----------------------------------------------------------------------------------------
// RP 21.12.2009:
//- use SimpleDataTable
//----------------------------------------------------------------------------------------
using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site.Controls;

namespace Rebound.GlobalTrader.Site.Controls.HomeNuggets {
	public partial class MyRecentActivity : Base {

		protected Panel _pnlCR;
		protected Panel _pnlGI;
		protected Panel _pnlQU;
		protected Panel _pnlPO;
		protected Panel _pnlSO;
		protected Panel _pnlCRMA;
		protected Panel _pnlSRMA;
		protected Panel _pnlCredit;
		protected Panel _pnlDebit;
		protected Panel _pnlInv;
		protected Panel _pnlOsPoApproval;
        protected SimpleDataTable _tblCR;
		protected SimpleDataTable _tblGI;
		protected SimpleDataTable _tblQU;
		protected SimpleDataTable _tblPO;
		protected SimpleDataTable _tblSO;
		protected SimpleDataTable _tblCRMA;
		protected SimpleDataTable _tblSRMA;
		protected SimpleDataTable _tblCredit;
		protected SimpleDataTable _tblDebit;
		protected SimpleDataTable _tblInv;
		protected SimpleDataTable _tblOsPoApproval;

		/// <summary>
		/// On init
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
			this.HomePageNuggetType = "MyRecentActivity";
			base.OnInit(e);
			WireUpControls();
			SetupTables();
			AddScriptReference("Controls.HomeNuggets.MyRecentActivity.MyRecentActivity.js");
		}

		protected override void OnLoad(EventArgs e) {
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyRecentActivity", this.ctlDesignBase.ClientID);
			_scScriptControlDescriptor.AddElementProperty("pnlCR", _pnlCR.ClientID);
			_scScriptControlDescriptor.AddElementProperty("pnlGI", _pnlGI.ClientID);
			_scScriptControlDescriptor.AddElementProperty("pnlQU", _pnlQU.ClientID);
			_scScriptControlDescriptor.AddElementProperty("pnlPO", _pnlPO.ClientID);
			_scScriptControlDescriptor.AddElementProperty("pnlSO", _pnlSO.ClientID);
			_scScriptControlDescriptor.AddElementProperty("pnlCRMA", _pnlCRMA.ClientID);
			_scScriptControlDescriptor.AddElementProperty("pnlSRMA", _pnlSRMA.ClientID);
			_scScriptControlDescriptor.AddElementProperty("pnlCredit", _pnlCredit.ClientID);
			_scScriptControlDescriptor.AddElementProperty("pnlDebit", _pnlDebit.ClientID);
			_scScriptControlDescriptor.AddElementProperty("pnlInv", _pnlInv.ClientID);
			_scScriptControlDescriptor.AddElementProperty("pnlOsPoApproval", _pnlOsPoApproval.ClientID);
			_scScriptControlDescriptor.AddComponentProperty("tblCR", _tblCR.ClientID);
			_scScriptControlDescriptor.AddComponentProperty("tblGI", _tblGI.ClientID);
			_scScriptControlDescriptor.AddComponentProperty("tblQU", _tblQU.ClientID);
			_scScriptControlDescriptor.AddComponentProperty("tblPO", _tblPO.ClientID);
			_scScriptControlDescriptor.AddComponentProperty("tblSO", _tblSO.ClientID);
			_scScriptControlDescriptor.AddComponentProperty("tblCRMA", _tblCRMA.ClientID);
			_scScriptControlDescriptor.AddComponentProperty("tblSRMA", _tblSRMA.ClientID);
			_scScriptControlDescriptor.AddComponentProperty("tblCredit", _tblCredit.ClientID);
			_scScriptControlDescriptor.AddComponentProperty("tblDebit", _tblDebit.ClientID);
			_scScriptControlDescriptor.AddComponentProperty("tblInv", _tblInv.ClientID);
			_scScriptControlDescriptor.AddComponentProperty("tblOsPoApproval", _tblOsPoApproval.ClientID);
			base.OnLoad(e);
		}

		private void SetupTables() {
			_tblCR.Columns.Add(new SimpleDataColumn("Number", Unit.Pixel(65)));
			_tblCR.Columns.Add(new SimpleDataColumn("Customer"));
			_tblCR.Columns.Add(new SimpleDataColumn("Date", Unit.Pixel(75)));
			_tblGI.Columns.Add(new SimpleDataColumn("Number", Unit.Pixel(65)));
			_tblGI.Columns.Add(new SimpleDataColumn("Customer"));
			_tblGI.Columns.Add(new SimpleDataColumn("Date", Unit.Pixel(75)));
			_tblQU.Columns.Add(new SimpleDataColumn("Number", Unit.Pixel(65)));
			_tblQU.Columns.Add(new SimpleDataColumn("Customer"));
			_tblQU.Columns.Add(new SimpleDataColumn("Date", Unit.Pixel(75)));
			_tblPO.Columns.Add(new SimpleDataColumn("Number", Unit.Pixel(65)));
			_tblPO.Columns.Add(new SimpleDataColumn("Customer"));
			_tblPO.Columns.Add(new SimpleDataColumn("Date", Unit.Pixel(75)));
			_tblSO.Columns.Add(new SimpleDataColumn("Number", Unit.Pixel(65)));
			_tblSO.Columns.Add(new SimpleDataColumn("Customer"));
			_tblSO.Columns.Add(new SimpleDataColumn("Date", Unit.Pixel(75)));
			_tblCRMA.Columns.Add(new SimpleDataColumn("Number", Unit.Pixel(65)));
			_tblCRMA.Columns.Add(new SimpleDataColumn("Customer"));
			_tblCRMA.Columns.Add(new SimpleDataColumn("Date", Unit.Pixel(75)));
			_tblSRMA.Columns.Add(new SimpleDataColumn("Number", Unit.Pixel(65)));
			_tblSRMA.Columns.Add(new SimpleDataColumn("Customer"));
			_tblSRMA.Columns.Add(new SimpleDataColumn("Date", Unit.Pixel(75)));
			_tblCredit.Columns.Add(new SimpleDataColumn("Number", Unit.Pixel(65)));
			_tblCredit.Columns.Add(new SimpleDataColumn("Customer"));
			_tblCredit.Columns.Add(new SimpleDataColumn("Date", Unit.Pixel(75)));
			_tblDebit.Columns.Add(new SimpleDataColumn("Number", Unit.Pixel(65)));
			_tblDebit.Columns.Add(new SimpleDataColumn("Customer"));
			_tblDebit.Columns.Add(new SimpleDataColumn("Date", Unit.Pixel(75)));
			_tblInv.Columns.Add(new SimpleDataColumn("Number", Unit.Pixel(65)));
			_tblInv.Columns.Add(new SimpleDataColumn("Customer"));
			_tblInv.Columns.Add(new SimpleDataColumn("Date", Unit.Pixel(75)));
            _tblOsPoApproval.Columns.Add(new SimpleDataColumn("Number", Unit.Pixel(65)));
            _tblOsPoApproval.Columns.Add(new SimpleDataColumn("Customer"));
            _tblOsPoApproval.Columns.Add(new SimpleDataColumn("Date", Unit.Pixel(75)));
        }

		private void WireUpControls() {
			_pnlCR = (Panel)FindContentControl("pnlCR");
			_pnlGI = (Panel)FindContentControl("pnlGI");
			_pnlQU = (Panel)FindContentControl("pnlQU");
			_pnlPO = (Panel)FindContentControl("pnlPO");
			_pnlSO = (Panel)FindContentControl("pnlSO");
			_pnlCRMA = (Panel)FindContentControl("pnlCRMA");
			_pnlSRMA = (Panel)FindContentControl("pnlSRMA");
			_pnlCredit = (Panel)FindContentControl("pnlCredit");
			_pnlDebit = (Panel)FindContentControl("pnlDebit");
			_pnlInv = (Panel)FindContentControl("pnlInv");
            _pnlOsPoApproval = (Panel)FindContentControl("pnlOsPoApproval");
			_tblCR = (SimpleDataTable)FindContentControl("tblCR");
			_tblGI = (SimpleDataTable)FindContentControl("tblGI");
			_tblQU = (SimpleDataTable)FindContentControl("tblQU");
			_tblPO = (SimpleDataTable)FindContentControl("tblPO");
			_tblSO = (SimpleDataTable)FindContentControl("tblSO");
			_tblCRMA = (SimpleDataTable)FindContentControl("tblCRMA");
			_tblSRMA = (SimpleDataTable)FindContentControl("tblSRMA");
			_tblCredit = (SimpleDataTable)FindContentControl("tblCredit");
			_tblDebit = (SimpleDataTable)FindContentControl("tblDebit");
			_tblInv = (SimpleDataTable)FindContentControl("tblInv");
            _tblOsPoApproval = (SimpleDataTable)FindContentControl("tblOsPoApproval");
		}
	}
}