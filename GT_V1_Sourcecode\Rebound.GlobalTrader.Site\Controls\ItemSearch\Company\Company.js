Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.ItemSearch");Rebound.GlobalTrader.Site.Controls.ItemSearch.Company=function(n){Rebound.GlobalTrader.Site.Controls.ItemSearch.Company.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.ItemSearch.Company.prototype={get_blnForPOs:function(){return this._blnForPOs},set_blnForPOs:function(n){this._blnForPOs!==n&&(this._blnForPOs=n)},get_blnForSOs:function(){return this._blnForSOs},set_blnForSOs:function(n){this._blnForSOs!==n&&(this._blnForSOs=n)},get_SupplierOnStop:function(){return this._SupplierOnStop},set_SupplierOnStop:function(n){this._SupplierOnStop!==n&&(this._SupplierOnStop=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.ItemSearch.Company.callBaseMethod(this,"initialize");this.addSetupData(Function.createDelegate(this,this.doSetupData));this.addGetDataComplete(Function.createDelegate(this,this.doGetDataComplete))},dispose:function(){this.isDisposed||(this._blnForPOs=null,this._blnForSOs=null,this._SupplierOnStop=null,Rebound.GlobalTrader.Site.Controls.ItemSearch.Company.callBaseMethod(this,"dispose"))},doSetupData:function(){this._objData.set_PathToData("controls/ItemSearch/Company");this._objData.set_DataObject("Company");this._objData.set_DataAction("GetData");this._objData.addParameter("Name",this.getFieldValue("ctlCompanyName"));this._blnForPOs&&this._objData.addParameter("POApproved",!0);this._blnForSOs&&this._objData.addParameter("SOApproved",!0);this._SupplierOnStop&&this._objData.addParameter("SupplierStop",!0)},doGetDataComplete:function(){for(var n,i,t=0,r=this._objResult.Results.length;t<r;t++)n=this._objResult.Results[t],i=[$R_FN.setCleanTextValue(n.Name)+$R_FN.createAdvisoryNotesIcon(n.AdvisoryNotes,"margin-left-10"),$R_FN.setCleanTextValue(n.Type),$R_FN.setCleanTextValue(n.City),$R_FN.setCleanTextValue(n.Country),$R_FN.setCleanTextValue(n.Tel),$R_FN.setCleanTextValue(n.Salesperson),$R_FN.setCleanTextValue(n.LastContact)],this._tblResults.addRow(i,n.ID,!1,{CompanyName:n.Name,Traceability:n.Traceability,SalemanNo:n.SalespersonNo,AdvisoryNotes:n.AdvisoryNotes}),i=null,n=null}};Rebound.GlobalTrader.Site.Controls.ItemSearch.Company.registerClass("Rebound.GlobalTrader.Site.Controls.ItemSearch.Company",Rebound.GlobalTrader.Site.Controls.ItemSearch.Base,Sys.IDisposable);