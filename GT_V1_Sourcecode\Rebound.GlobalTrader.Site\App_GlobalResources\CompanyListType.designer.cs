//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Resources {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option or rebuild the Visual Studio project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.VisualStudio.Web.Application.StronglyTypedResourceProxyBuilder", "16.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class CompanyListType {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal CompanyListType() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Resources.CompanyListType", global::System.Reflection.Assembly.Load("App_GlobalResources"));
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to All Companies.
        /// </summary>
        internal static string AllCompanies {
            get {
                return ResourceManager.GetString("AllCompanies", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Contacts.
        /// </summary>
        internal static string Contacts {
            get {
                return ResourceManager.GetString("Contacts", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customers.
        /// </summary>
        internal static string Customers {
            get {
                return ResourceManager.GetString("Customers", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Manufacturers.
        /// </summary>
        internal static string Manufacturers {
            get {
                return ResourceManager.GetString("Manufacturers", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Prospects.
        /// </summary>
        internal static string Prospects {
            get {
                return ResourceManager.GetString("Prospects", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Suppliers.
        /// </summary>
        internal static string Suppliers {
            get {
                return ResourceManager.GetString("Suppliers", resourceCulture);
            }
        }
    }
}
