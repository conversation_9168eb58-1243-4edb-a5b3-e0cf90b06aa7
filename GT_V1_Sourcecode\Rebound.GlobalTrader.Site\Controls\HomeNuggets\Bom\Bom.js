Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.HomeNuggets");Rebound.GlobalTrader.Site.Controls.HomeNuggets.Bom=function(n){Rebound.GlobalTrader.Site.Controls.HomeNuggets.Bom.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.HomeNuggets.Bom.prototype={get_pnlReady:function(){return this._pnlReady},set_pnlReady:function(n){this._pnlReady!==n&&(this._pnlReady=n)},get_tblReady:function(){return this._tblReady},set_tblReady:function(n){this._tblReady!==n&&(this._tblReady=n)},get_pnlPartial:function(){return this._pnlPartial},set_pnlPartial:function(n){this._pnlPartial!==n&&(this._pnlPartial=n)},get_tblPartial:function(){return this._tblPartial},set_tblPartial:function(n){this._tblPartial!==n&&(this._tblPartial=n)},get_pnlNew:function(){return this._pnlNew},set_pnlNew:function(n){this._pnlNew!==n&&(this._pnlNew=n)},get_tblNew:function(){return this._tblNew},set_tblNew:function(n){this._tblNew!==n&&(this._tblNew=n)},get_pnlRFQ:function(){return this._pnlRFQ},set_pnlRFQ:function(n){this._pnlRFQ!==n&&(this._pnlRFQ=n)},get_tblRFQ:function(){return this._tblRFQ},set_tblRFQ:function(n){this._tblRFQ!==n&&(this._tblRFQ=n)},get_pnlClosed:function(){return this._pnlClosed},set_pnlClosed:function(n){this._pnlClosed!==n&&(this._pnlClosed=n)},get_tblClosed:function(){return this._tblClosed},set_tblClosed:function(n){this._tblClosed!==n&&(this._tblClosed=n)},get_pnlNoBid:function(){return this._pnlNoBid},set_pnlNoBid:function(n){this._pnlNoBid!==n&&(this._pnlNoBid=n)},get_tblNoBid:function(){return this._tblNoBid},set_tblNoBid:function(n){this._tblNoBid!==n&&(this._tblNoBid=n)},get_pnlMore:function(){return this._pnlMore},set_pnlMore:function(n){this._pnlMore!==n&&(this._pnlMore=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.HomeNuggets.Bom.callBaseMethod(this,"initialize");this.addRefreshEvent(Function.createDelegate(this,this.getData))},dispose:function(){this.isDisposed||(this._tblReady&&this._tblReady.dispose(),this._pnlReady=null,this._tblReady=null,this._tblPartial&&this._tblPartial.dispose(),this._pnlPartial=null,this._tblPartial=null,this._tblNew&&this._tblNew.dispose(),this._pnlNew=null,this._tblNew=null,this._tblRFQ&&this._tblRFQ.dispose(),this._pnlRFQ=null,this._tblRFQ=null,this._tblClosed&&this._tblClosed.dispose(),this._pnlClosed=null,this._tblClosed=null,this._pnlMore=null,Rebound.GlobalTrader.Site.Controls.HomeNuggets.Bom.callBaseMethod(this,"dispose"))},setupLoadingState:function(){$R_FN.showElement(this._pnlMore,!1);$R_FN.showElement(this._pnlReady,!1);$R_FN.showElement(this._pnlNew,!1);$R_FN.showElement(this._pnlRFQ,!1);$R_FN.showElement(this._pnlClosed,!1);Rebound.GlobalTrader.Site.Controls.HomeNuggets.Bom.callBaseMethod(this,"setupLoadingState")},showNoData:function(n){this.showContent(!0);$R_FN.showElement(this._pnlNoData,n)},getData:function(){this.setupLoadingState();var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/HomeNuggets/Bom");n.set_DataObject("Bom");n.set_DataAction("GetData");n.addParameter("rowcount",this._intRowCount);n.addDataOK(Function.createDelegate(this,this.getDataComplete));n.addError(Function.createDelegate(this,this.homeNuggetDataError));n.addTimeout(Function.createDelegate(this,this.homeNuggetDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getDataComplete:function(n){var r,u,t,i,f;for(this.showNoData(n._result.Count==0),$R_FN.showElement(this._pnlMore,n._result.Count>0),r=n._result,this._tblReady.clearTable(),i=0;i<r.Ready.length;i++)t=r.Ready[i],f="",u=[$RGT_nubButton_BOM(t.BOMId,t.BOMCode),$R_FN.setCleanTextValue(t.Name),t.Date,t.QuoteRequired],this._tblReady.addRow(u,null,null,null,f);for($R_FN.showElement(this._pnlReady,r.Ready.length>0),this._tblPartial.clearTable(),i=0;i<r.Partial.length;i++)t=r.Partial[i],f="",u=[$RGT_nubButton_BOM(t.BOMId,t.BOMCode),$R_FN.setCleanTextValue(t.Name),t.Date,t.QuoteRequired],this._tblPartial.addRow(u,null,null,f);if($R_FN.showElement(this._pnlPartial,r.Partial.length>0),r.IsHub==!1){for(this._tblNew.clearTable(),i=0;i<r.New.length;i++)t=r.New[i],u=[$RGT_nubButton_BOM(t.BOMId,t.BOMCode),$R_FN.setCleanTextValue(t.Name),t.Date,t.QuoteRequired],this._tblNew.addRow(u,null),t=null;$R_FN.showElement(this._pnlNew,r.New.length>0)}for(this._tblRFQ.clearTable(),i=0;i<r.RFQ.length;i++)t=r.RFQ[i],u=[$RGT_nubButton_BOM(t.BOMId,t.BOMCode),$R_FN.setCleanTextValue(t.Name),t.Date,t.QuoteRequired],this._tblRFQ.addRow(u,null),t=null;for($R_FN.showElement(this._pnlRFQ,r.RFQ.length>0),this._tblClosed.clearTable(),i=0;i<r.Closed.length;i++)t=r.Closed[i],f="",u=[$RGT_nubButton_BOM(t.BOMId,t.BOMCode),$R_FN.setCleanTextValue(t.Name),t.Date,t.QuoteRequired],this._tblClosed.addRow(u,null,null,null,f),t=null;for($R_FN.showElement(this._pnlClosed,r.Closed.length>0),this._tblNoBid.clearTable(),i=0;i<r.NoBid.length;i++)t=r.NoBid[i],f="",u=[$RGT_nubButton_BOM(t.BOMId,t.BOMCode),$R_FN.setCleanTextValue(t.Name),t.Date,t.QuoteRequired],this._tblNoBid.addRow(u,null,null,null,f),t=null;$R_FN.showElement(this._pnlNoBid,r.NoBid.length>0);this.hideLoading()}};Rebound.GlobalTrader.Site.Controls.HomeNuggets.Bom.registerClass("Rebound.GlobalTrader.Site.Controls.HomeNuggets.Bom",Rebound.GlobalTrader.Site.Controls.HomeNuggets.Base,Sys.IDisposable);