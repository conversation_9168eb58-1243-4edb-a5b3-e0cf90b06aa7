<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Abbreviation" xml:space="preserve">
    <value>Abkürzung</value>
  </data>
  <data name="Account" xml:space="preserve">
    <value>Konto</value>
  </data>
  <data name="Address" xml:space="preserve">
    <value>Adresse</value>
  </data>
  <data name="AddressName" xml:space="preserve">
    <value>Adressen-Name</value>
  </data>
  <data name="AdminSecurityGroupName" xml:space="preserve">
    <value>Verwalter-Sicherheits-Gruppen-Name</value>
  </data>
  <data name="AdminUserName" xml:space="preserve">
    <value>Verwalter-Benutzername</value>
  </data>
  <data name="AirWayBill" xml:space="preserve">
    <value>Luft-Weise Bill</value>
  </data>
  <data name="Allocated" xml:space="preserve">
    <value>Zugeteilt</value>
  </data>
  <data name="AmountOwed" xml:space="preserve">
    <value>Ausstehender Betrag</value>
  </data>
  <data name="Anniversary" xml:space="preserve">
    <value>Jahrestag</value>
  </data>
  <data name="ApplyDuty" xml:space="preserve">
    <value>Wenden Sie Aufgabe an?</value>
  </data>
  <data name="ApprovedCustomer" xml:space="preserve">
    <value>Anerkannter Kunde</value>
  </data>
  <data name="ApprovedSupplier" xml:space="preserve">
    <value>Anerkannter Lieferant</value>
  </data>
  <data name="AuthorisationHistory" xml:space="preserve">
    <value>Ermächtigungs-Geschichte</value>
  </data>
  <data name="AuthorisedBy" xml:space="preserve">
    <value>Vorbei autorisiert</value>
  </data>
  <data name="Authoriser" xml:space="preserve">
    <value>Authoriser</value>
  </data>
  <data name="Available" xml:space="preserve">
    <value>Vorhanden</value>
  </data>
  <data name="BackgroundImage" xml:space="preserve">
    <value>Hintergrund</value>
  </data>
  <data name="Balance" xml:space="preserve">
    <value>Balance</value>
  </data>
  <data name="BalanceWithBoth" xml:space="preserve">
    <value>Balance mit diesem Auftrag &amp;&amp; Informierte Linien</value>
  </data>
  <data name="BalanceWithOpenOrders" xml:space="preserve">
    <value>Balance mit allen informierten Linien</value>
  </data>
  <data name="BalanceWithThisOrder" xml:space="preserve">
    <value>Balance mit diesem Auftrag</value>
  </data>
  <data name="BankFee" xml:space="preserve">
    <value>Bank-Gebühr</value>
  </data>
  <data name="BillingAddress" xml:space="preserve">
    <value>Gebührenzählungs-Adressen</value>
  </data>
  <data name="BillingAddressName" xml:space="preserve">
    <value>Gebührenzählungs-Adressen-Name</value>
  </data>
  <data name="BillToAddress" xml:space="preserve">
    <value>Zu wenden Bill sich</value>
  </data>
  <data name="BillToAddressName" xml:space="preserve">
    <value>Gebührenzählungs-Adressen-Name</value>
  </data>
  <data name="Birthday" xml:space="preserve">
    <value>Geburtstag</value>
  </data>
  <data name="Boxes" xml:space="preserve">
    <value>Boxes</value>
  </data>
  <data name="Budget" xml:space="preserve">
    <value>Etat</value>
  </data>
  <data name="Buy" xml:space="preserve">
    <value>Käuf</value>
  </data>
  <data name="Buyer" xml:space="preserve">
    <value>Käufer</value>
  </data>
  <data name="BuyerName" xml:space="preserve">
    <value>Käufer</value>
  </data>
  <data name="BuyPrice" xml:space="preserve">
    <value>Kaufender Preis</value>
  </data>
  <data name="BuyShipVia" xml:space="preserve">
    <value>Kaufen Sie Schiff über</value>
  </data>
  <data name="BuyShipViaAccount" xml:space="preserve">
    <value>Kaufen Sie Schiff über Konto</value>
  </data>
  <data name="BuyShipViaNo" xml:space="preserve">
    <value>Kaufen Sie Schiff über Nr.</value>
  </data>
  <data name="Caption" xml:space="preserve">
    <value>Caption</value>
  </data>
  <data name="CeaseDate" xml:space="preserve">
    <value>Hören Sie Datum auf</value>
  </data>
  <data name="Charge" xml:space="preserve">
    <value>Gebühr</value>
  </data>
  <data name="Child1" xml:space="preserve">
    <value>Kind 1</value>
  </data>
  <data name="Child1Birthday" xml:space="preserve">
    <value>Kind 1 Geburtstag</value>
  </data>
  <data name="Child1Name" xml:space="preserve">
    <value>Name des Kind-1</value>
  </data>
  <data name="Child1Sex" xml:space="preserve">
    <value>Geschlecht des Kind-1</value>
  </data>
  <data name="Child2" xml:space="preserve">
    <value>Kind 2</value>
  </data>
  <data name="Child2Birthday" xml:space="preserve">
    <value>Kind 2 Geburtstag</value>
  </data>
  <data name="Child2Name" xml:space="preserve">
    <value>Name des Kind-2</value>
  </data>
  <data name="Child2Sex" xml:space="preserve">
    <value>Geschlecht des Kind-2</value>
  </data>
  <data name="Child3" xml:space="preserve">
    <value>Kind 3</value>
  </data>
  <data name="Child3Birthday" xml:space="preserve">
    <value>Kind 3 Geburtstag</value>
  </data>
  <data name="Child3Name" xml:space="preserve">
    <value>Name des Kind-3</value>
  </data>
  <data name="Child3Sex" xml:space="preserve">
    <value>Geschlecht des Kind-3</value>
  </data>
  <data name="City" xml:space="preserve">
    <value>Stadt</value>
  </data>
  <data name="CityTown" xml:space="preserve">
    <value>Stadt/Großstadt</value>
  </data>
  <data name="CloseAllAlternates" xml:space="preserve">
    <value>Schließen Sie alle Stellvertreter?</value>
  </data>
  <data name="Closed" xml:space="preserve">
    <value>Geschlossen</value>
  </data>
  <data name="CloseReason" xml:space="preserve">
    <value>Naher Grund</value>
  </data>
  <data name="Code" xml:space="preserve">
    <value>Code</value>
  </data>
  <data name="Code1" xml:space="preserve">
    <value>Code 1</value>
  </data>
  <data name="Code2" xml:space="preserve">
    <value>Code 2</value>
  </data>
  <data name="CodeN" xml:space="preserve">
    <value>Code N</value>
  </data>
  <data name="CodeY" xml:space="preserve">
    <value>Code Y</value>
  </data>
  <data name="CofCNotes" xml:space="preserve">
    <value>Konformitätsbescheinigung Anmerkungen</value>
  </data>
  <data name="Company" xml:space="preserve">
    <value>Firma</value>
  </data>
  <data name="CompanyName" xml:space="preserve">
    <value>Firmennamen</value>
  </data>
  <data name="CompanyType" xml:space="preserve">
    <value>Firma-Art</value>
  </data>
  <data name="Confirm" xml:space="preserve">
    <value>Bestätigen Sie bitte</value>
  </data>
  <data name="Confirmed" xml:space="preserve">
    <value>Bestätigt?</value>
  </data>
  <data name="Contact" xml:space="preserve">
    <value>Kontakt</value>
  </data>
  <data name="ContactFirstName" xml:space="preserve">
    <value>Kontakt-Vorname</value>
  </data>
  <data name="ContactLastName" xml:space="preserve">
    <value>Kontakt-Nachname</value>
  </data>
  <data name="ContactLogItem" xml:space="preserve">
    <value>Kontakt-Maschinenbordbuch-Einzelteil</value>
  </data>
  <data name="ContactName" xml:space="preserve">
    <value>Kontakt-Name</value>
  </data>
  <data name="Cost" xml:space="preserve">
    <value>Kosten</value>
  </data>
  <data name="CountingMethod" xml:space="preserve">
    <value>Zählung-Methode</value>
  </data>
  <data name="Country" xml:space="preserve">
    <value>Land</value>
  </data>
  <data name="CountryOfManufacture" xml:space="preserve">
    <value>Land der Fertigung</value>
  </data>
  <data name="County" xml:space="preserve">
    <value>Grafschaft</value>
  </data>
  <data name="CountyState" xml:space="preserve">
    <value>Grafschaft/Zustand</value>
  </data>
  <data name="CreditDate" xml:space="preserve">
    <value>Gutschrift-Datum</value>
  </data>
  <data name="CreditDateFrom" xml:space="preserve">
    <value>Gutschrift-Datum von</value>
  </data>
  <data name="CreditDateTo" xml:space="preserve">
    <value>Gutschrift-Datum zu</value>
  </data>
  <data name="CreditHistory" xml:space="preserve">
    <value>Gutschrift-Geschichte</value>
  </data>
  <data name="CreditLimit" xml:space="preserve">
    <value>Kreditlinie</value>
  </data>
  <data name="CreditNo" xml:space="preserve">
    <value>Gutschrift-Nr.</value>
  </data>
  <data name="CRMA" xml:space="preserve">
    <value>Kunden-RMA</value>
  </data>
  <data name="CRMADate" xml:space="preserve">
    <value>Datum des Kunden-RMA</value>
  </data>
  <data name="CRMANo" xml:space="preserve">
    <value>v</value>
  </data>
  <data name="Currency" xml:space="preserve">
    <value>Währung</value>
  </data>
  <data name="Current" xml:space="preserve">
    <value>Gegenwärtig</value>
  </data>
  <data name="CurrentRate" xml:space="preserve">
    <value>Tageskurs</value>
  </data>
  <data name="CusReqNo" xml:space="preserve">
    <value>Kunden-Anforderungs-Nr.</value>
  </data>
  <data name="Customer" xml:space="preserve">
    <value>Kunde</value>
  </data>
  <data name="CustomerDebit" xml:space="preserve">
    <value>Kunden-Schuldposten-Nr.</value>
  </data>
  <data name="CustomerName" xml:space="preserve">
    <value>Kunden-Name</value>
  </data>
  <data name="CustomerNo" xml:space="preserve">
    <value>Kunden-Nr.</value>
  </data>
  <data name="CustomerNotes" xml:space="preserve">
    <value>Kunden-Anmerkungen</value>
  </data>
  <data name="CustomerPart" xml:space="preserve">
    <value>Kunden-Teil</value>
  </data>
  <data name="CustomerPartNo" xml:space="preserve">
    <value>Kunden-Teil-Nr.</value>
  </data>
  <data name="CustomerPO" xml:space="preserve">
    <value>Kunden-PO</value>
  </data>
  <data name="CustomerPONo" xml:space="preserve">
    <value>Nr. des Kunden-PO</value>
  </data>
  <data name="CustomerPurchaseOrderNo" xml:space="preserve">
    <value>Kunden-PO</value>
  </data>
  <data name="CustomerRating" xml:space="preserve">
    <value>Beurteilung der Kreditwürdigkeit eines Kunden</value>
  </data>
  <data name="CustomerReturn" xml:space="preserve">
    <value>Kunden-Rückkehr-Nr.</value>
  </data>
  <data name="CustomerRMADate" xml:space="preserve">
    <value>Datum des Kunden-RMA</value>
  </data>
  <data name="CustomerRMADateFrom" xml:space="preserve">
    <value>Datum des Kunden-RMA von</value>
  </data>
  <data name="CustomerRMADateTo" xml:space="preserve">
    <value>Datum des Kunden-RMA zu</value>
  </data>
  <data name="CustomerRMANo" xml:space="preserve">
    <value>Kunden-RMA</value>
  </data>
  <data name="DatabaseServer" xml:space="preserve">
    <value>Datenbank-Bediener</value>
  </data>
  <data name="DatabaseUsername" xml:space="preserve">
    <value>Datenbank-username</value>
  </data>
  <data name="DatabaseUserPassword" xml:space="preserve">
    <value>Datenbank-Kennwort</value>
  </data>
  <data name="Date" xml:space="preserve">
    <value>Datum</value>
  </data>
  <data name="DateAuthorised" xml:space="preserve">
    <value>Datum autorisiert</value>
  </data>
  <data name="DateCode" xml:space="preserve">
    <value>Datum-Code</value>
  </data>
  <data name="DateDeliveredFrom" xml:space="preserve">
    <value>Datum geliefert von</value>
  </data>
  <data name="DateDeliveredTo" xml:space="preserve">
    <value>Datum geliefert an</value>
  </data>
  <data name="DateInspected" xml:space="preserve">
    <value>Date Inspected</value>
  </data>
  <data name="DateInvoiced" xml:space="preserve">
    <value>Datum fakturiert zu</value>
  </data>
  <data name="DateInvoicedFrom" xml:space="preserve">
    <value>Datum fakturiert von</value>
  </data>
  <data name="DateInvoicedTo" xml:space="preserve">
    <value>Datum fakturiert zu</value>
  </data>
  <data name="DateOrdered" xml:space="preserve">
    <value>Datum bestellt</value>
  </data>
  <data name="DateOrderedFrom" xml:space="preserve">
    <value>Datum bestellt von</value>
  </data>
  <data name="DateOrderedTo" xml:space="preserve">
    <value>Datum bestellt zu</value>
  </data>
  <data name="DatePromised" xml:space="preserve">
    <value>Datum versprochen</value>
  </data>
  <data name="DatePromisedFrom" xml:space="preserve">
    <value>Datum versprochen von</value>
  </data>
  <data name="DatePromisedTo" xml:space="preserve">
    <value>Datum versprochen zu</value>
  </data>
  <data name="DateQuoted" xml:space="preserve">
    <value>Datum veranschlagen</value>
  </data>
  <data name="DateQuotedFrom" xml:space="preserve">
    <value>Datum veranschlagen von</value>
  </data>
  <data name="DateQuotedTo" xml:space="preserve">
    <value>Datum veranschlagen zu</value>
  </data>
  <data name="DateReceivedFrom" xml:space="preserve">
    <value>Datum empfangen von</value>
  </data>
  <data name="DateReceivedTo" xml:space="preserve">
    <value>Datum empfangen zu</value>
  </data>
  <data name="DateRequired" xml:space="preserve">
    <value>Datum erfordert</value>
  </data>
  <data name="Days" xml:space="preserve">
    <value>Tage</value>
  </data>
  <data name="Days120" xml:space="preserve">
    <value>120+ Tage</value>
  </data>
  <data name="Days30" xml:space="preserve">
    <value>30 - 60 Tage</value>
  </data>
  <data name="Days60" xml:space="preserve">
    <value>60 - 90 Tage</value>
  </data>
  <data name="Days90" xml:space="preserve">
    <value>90 - 120 Tage</value>
  </data>
  <data name="DebitDate" xml:space="preserve">
    <value>Schuldposten-Datum</value>
  </data>
  <data name="DebitDateFrom" xml:space="preserve">
    <value>Schuldposten-Datum von</value>
  </data>
  <data name="DebitDateTo" xml:space="preserve">
    <value>Schuldposten-Datum zu</value>
  </data>
  <data name="DebitNo" xml:space="preserve">
    <value>Schuldposten-Nr.</value>
  </data>
  <data name="DefaultContactNo" xml:space="preserve">
    <value>Default Contact</value>
  </data>
  <data name="DefaultDivisionName" xml:space="preserve">
    <value>Rückstellungs-Abteilungs-Name</value>
  </data>
  <data name="DefaultHomepageTab" xml:space="preserve">
    <value>Rückstellungs-homepage-Vorsprung</value>
  </data>
  <data name="DefaultLanguage" xml:space="preserve">
    <value>Default Language</value>
  </data>
  <data name="DefaultListPageSize" xml:space="preserve">
    <value>Rückstellungs-Listen-Seiten-Größe</value>
  </data>
  <data name="DefaultListPageView" xml:space="preserve">
    <value>Rückstellungs-Listen-Seiten-Ansicht</value>
  </data>
  <data name="DefaultShippingAccountNo" xml:space="preserve">
    <value>Rückstellungs-Verschiffen-Kontonummer</value>
  </data>
  <data name="DefaultShipVia" xml:space="preserve">
    <value>Rückstellungs-Schiff über</value>
  </data>
  <data name="DefaultTeamName" xml:space="preserve">
    <value>Rückstellungs-Mannschaft-Name</value>
  </data>
  <data name="DeliverByDate" xml:space="preserve">
    <value>Liefern Sie bis zu Date</value>
  </data>
  <data name="DeliveryDate" xml:space="preserve">
    <value>Lieferfrist</value>
  </data>
  <data name="DeliveryDateFrom" xml:space="preserve">
    <value>Lieferfrist von</value>
  </data>
  <data name="DeliveryDateTo" xml:space="preserve">
    <value>Lieferfrist zu</value>
  </data>
  <data name="DeliveryNotes" xml:space="preserve">
    <value>Lieferscheine</value>
  </data>
  <data name="Description" xml:space="preserve">
    <value>Beschreibung</value>
  </data>
  <data name="DimensionalWeight" xml:space="preserve">
    <value>Maßgewicht</value>
  </data>
  <data name="Division" xml:space="preserve">
    <value>Abteilung</value>
  </data>
  <data name="Document" xml:space="preserve">
    <value>Document</value>
  </data>
  <data name="DueDate" xml:space="preserve">
    <value>Abgabefrist</value>
  </data>
  <data name="DueTime" xml:space="preserve">
    <value>Passende Zeit</value>
  </data>
  <data name="DutyCode" xml:space="preserve">
    <value>Aufgaben-Code</value>
  </data>
  <data name="DutyFreeCode" xml:space="preserve">
    <value>Zollfreier harmonisierter Code</value>
  </data>
  <data name="DutyHarmonisedCode" xml:space="preserve">
    <value>Aufgabe harmonisierter Code</value>
  </data>
  <data name="EditDetails" xml:space="preserve">
    <value>Redigieren Sie Details</value>
  </data>
  <data name="EECMember" xml:space="preserve">
    <value>EWG-Mitglied</value>
  </data>
  <data name="Email" xml:space="preserve">
    <value>Email</value>
  </data>
  <data name="EmployeeName" xml:space="preserve">
    <value>Angestellter</value>
  </data>
  <data name="EstimatedFreight" xml:space="preserve">
    <value>Geschätzte Fracht </value>
  </data>
  <data name="ETA" xml:space="preserve">
    <value>ETA</value>
  </data>
  <data name="ExpediteDate" xml:space="preserve">
    <value>Beschleunigen Sie Datum</value>
  </data>
  <data name="ExpediteDateFrom" xml:space="preserve">
    <value>Beschleunigen Sie Datum von</value>
  </data>
  <data name="ExpediteDateTo" xml:space="preserve">
    <value>Beschleunigen Sie Datum zu</value>
  </data>
  <data name="ExpediteNotes" xml:space="preserve">
    <value>Beschleunigen Sie Anmerkungen</value>
  </data>
  <data name="ExportData" xml:space="preserve">
    <value>Export?</value>
  </data>
  <data name="Exported" xml:space="preserve">
    <value>Exportiert</value>
  </data>
  <data name="Extension" xml:space="preserve">
    <value>Verlängerung</value>
  </data>
  <data name="FavouriteSport" xml:space="preserve">
    <value>Lieblingssport</value>
  </data>
  <data name="FavouriteTeam" xml:space="preserve">
    <value>Lieblingsmannschaft</value>
  </data>
  <data name="Fax" xml:space="preserve">
    <value>Telefax</value>
  </data>
  <data name="Feedback" xml:space="preserve">
    <value>Ihr Rückgespräch</value>
  </data>
  <data name="File" xml:space="preserve">
    <value>Akte</value>
  </data>
  <data name="FirstName" xml:space="preserve">
    <value>Vorname</value>
  </data>
  <data name="FOB" xml:space="preserve">
    <value>FOB</value>
  </data>
  <data name="Folder" xml:space="preserve">
    <value>Faltblatt</value>
  </data>
  <data name="FooterText" xml:space="preserve">
    <value>Seitenende-Text</value>
  </data>
  <data name="FreeOnBoard" xml:space="preserve">
    <value>Frei Schiff</value>
  </data>
  <data name="Freight" xml:space="preserve">
    <value>Fracht</value>
  </data>
  <data name="From" xml:space="preserve">
    <value>Von</value>
  </data>
  <data name="Funds" xml:space="preserve">
    <value>Kapital</value>
  </data>
  <data name="Gender" xml:space="preserve">
    <value>Gender</value>
  </data>
  <data name="GeneralUsersSecurityGroupName" xml:space="preserve">
    <value>Sicherheits-Gruppen-Name des General-Users</value>
  </data>
  <data name="GenericDatabaseTimeout" xml:space="preserve">
    <value>Generische Datenbankabschaltung</value>
  </data>
  <data name="GINo" xml:space="preserve">
    <value>Waren in der Anmerkung</value>
  </data>
  <data name="GoodsIn" xml:space="preserve">
    <value>Waren innen</value>
  </data>
  <data name="GoodsInNo" xml:space="preserve">
    <value>Waren in der Anmerkung</value>
  </data>
  <data name="GoodsInValue" xml:space="preserve">
    <value>Goods In Value</value>
  </data>
  <data name="GP" xml:space="preserve">
    <value>GP</value>
  </data>
  <data name="Group" xml:space="preserve">
    <value>Gruppe</value>
  </data>
  <data name="Hobbies" xml:space="preserve">
    <value>Liebhabereien</value>
  </data>
  <data name="HomeEmail" xml:space="preserve">
    <value>Haupt-eMail</value>
  </data>
  <data name="HomeFax" xml:space="preserve">
    <value>Haupttelefax</value>
  </data>
  <data name="HomeTel" xml:space="preserve">
    <value>Haupttelefon</value>
  </data>
  <data name="HomeTelephone" xml:space="preserve">
    <value>Haupttelefon</value>
  </data>
  <data name="IgnoreRowLimit" xml:space="preserve">
    <value>Ignorieren Sie Reihen-Begrenzung</value>
  </data>
  <data name="ImportantNotes" xml:space="preserve">
    <value>Wichtige Anmerkungen</value>
  </data>
  <data name="InAdvance" xml:space="preserve">
    <value>Im Voraus</value>
  </data>
  <data name="IncludeClosed" xml:space="preserve">
    <value>Schließen Sie geschlossenes mit ein?</value>
  </data>
  <data name="IncludeHistory" xml:space="preserve">
    <value>Schließen Sie Geschichte ein</value>
  </data>
  <data name="IncludeInvoiced" xml:space="preserve">
    <value>Schließen Sie fakturiert ein?</value>
  </data>
  <data name="IncludePaid" xml:space="preserve">
    <value>Schließen Sie zahlendes mit ein</value>
  </data>
  <data name="IncludeZeroStock" xml:space="preserve">
    <value>Schließen Sie nullvorrat mit ein?</value>
  </data>
  <data name="IndustryType" xml:space="preserve">
    <value>Industrie-Art</value>
  </data>
  <data name="Initial" xml:space="preserve">
    <value>Initiale</value>
  </data>
  <data name="InspectedBy" xml:space="preserve">
    <value>Vorbei kontrolliert</value>
  </data>
  <data name="InStock" xml:space="preserve">
    <value>Auf Lager</value>
  </data>
  <data name="Instructions" xml:space="preserve">
    <value>Instructions</value>
  </data>
  <data name="InternalInstructions" xml:space="preserve">
    <value>Interne Anweisungen</value>
  </data>
  <data name="InternalNotes" xml:space="preserve">
    <value>Interne Anmerkungen</value>
  </data>
  <data name="InvoiceAmount" xml:space="preserve">
    <value>Rechnungsbetrag</value>
  </data>
  <data name="InvoiceDate" xml:space="preserve">
    <value>Rechnungs-Datum</value>
  </data>
  <data name="InvoiceNo" xml:space="preserve">
    <value>Rechnungs-Nr.</value>
  </data>
  <data name="IsApproved" xml:space="preserve">
    <value>Anerkannt?</value>
  </data>
  <data name="IsApprovedCustomer" xml:space="preserve">
    <value>Anerkannter Kunde?</value>
  </data>
  <data name="IsApprovedSupplier" xml:space="preserve">
    <value>Anerkannter Lieferant?</value>
  </data>
  <data name="IsClosed" xml:space="preserve">
    <value>Geschlossen?</value>
  </data>
  <data name="IsConfirmed" xml:space="preserve">
    <value>Bestätigt?</value>
  </data>
  <data name="IsConsignment" xml:space="preserve">
    <value>Lieferung?</value>
  </data>
  <data name="IsDefaultBill" xml:space="preserve">
    <value>Rückstellung Bill?</value>
  </data>
  <data name="IsDefaultShip" xml:space="preserve">
    <value>Rückstellungs-Schiff?</value>
  </data>
  <data name="IsDutyPayable" xml:space="preserve">
    <value>Aufgabe?</value>
  </data>
  <data name="IsEmailTextOnly" xml:space="preserve">
    <value>Text-nur eMail?</value>
  </data>
  <data name="IsExported" xml:space="preserve">
    <value>Exportiert?</value>
  </data>
  <data name="IsHighPriority" xml:space="preserve">
    <value>Die hohe Priorität?</value>
  </data>
  <data name="IsInactive" xml:space="preserve">
    <value>Unaktiviert?</value>
  </data>
  <data name="IsIncluded" xml:space="preserve">
    <value>Enthalten</value>
  </data>
  <data name="IsListPriority" xml:space="preserve">
    <value>Listen-Priorität?</value>
  </data>
  <data name="IsOnHold" xml:space="preserve">
    <value>Auf Einfluss?</value>
  </data>
  <data name="IsOnStop" xml:space="preserve">
    <value>Auf Anschlag?</value>
  </data>
  <data name="IsPaid" xml:space="preserve">
    <value>Zahlend?</value>
  </data>
  <data name="IsPosted" xml:space="preserve">
    <value>Bekannt gegeben</value>
  </data>
  <data name="IsProspect" xml:space="preserve">
    <value>Aussicht?</value>
  </data>
  <data name="IsQuarantined" xml:space="preserve">
    <value>Unter Quarantäne gestellt?</value>
  </data>
  <data name="IsROHSCompliant" xml:space="preserve">
    <value>ROHS Gefällig?</value>
  </data>
  <data name="IsShipASAP" xml:space="preserve">
    <value>Schiff So Bald Wie Möglich </value>
  </data>
  <data name="IsShippingWaived" xml:space="preserve">
    <value>Geben Sie Verschiffen auf?</value>
  </data>
  <data name="IsTaxable" xml:space="preserve">
    <value>Steuerpflichtig ?</value>
  </data>
  <data name="JobTitle" xml:space="preserve">
    <value>Stellenbezeichnung</value>
  </data>
  <data name="LandedCost" xml:space="preserve">
    <value>Gelandete Kosten</value>
  </data>
  <data name="LastContacted" xml:space="preserve">
    <value>Letztes in Verbindung getreten</value>
  </data>
  <data name="LastName" xml:space="preserve">
    <value>Nachname</value>
  </data>
  <data name="LastYear" xml:space="preserve">
    <value>Letztes Jahr </value>
  </data>
  <data name="LineDetail" xml:space="preserve">
    <value>Linie Detail</value>
  </data>
  <data name="ListName" xml:space="preserve">
    <value>Listen-Name</value>
  </data>
  <data name="ListNo" xml:space="preserve">
    <value>Listen-Nr.</value>
  </data>
  <data name="ListViewResultsLimit" xml:space="preserve">
    <value>Listen-Seiten-Resultats-Begrenzung</value>
  </data>
  <data name="LMGp" xml:space="preserve">
    <value>Letzter Monat GP</value>
  </data>
  <data name="LMGpPct" xml:space="preserve">
    <value>Letzter Monat GP (%)</value>
  </data>
  <data name="LMPct" xml:space="preserve">
    <value>Letzter Monat %</value>
  </data>
  <data name="Location" xml:space="preserve">
    <value>Position</value>
  </data>
  <data name="Login" xml:space="preserve">
    <value>Login</value>
  </data>
  <data name="LoginTimeout" xml:space="preserve">
    <value>Login Abschaltung</value>
  </data>
  <data name="LogNotes" xml:space="preserve">
    <value>Maschinenbordbuch-Anmerkungen</value>
  </data>
  <data name="LogType" xml:space="preserve">
    <value>Maschinenbordbuch-Art</value>
  </data>
  <data name="Lot" xml:space="preserve">
    <value>Los</value>
  </data>
  <data name="LTAir" xml:space="preserve">
    <value>Anlieferungs-Vorbereitungs- und Anlaufzeit - Luft</value>
  </data>
  <data name="LTSurface" xml:space="preserve">
    <value>Anlieferungs-Vorbereitungs- und Anlaufzeit - Oberfläche</value>
  </data>
  <data name="LYGp" xml:space="preserve">
    <value>Letztes Jahr GP</value>
  </data>
  <data name="LYGpPct" xml:space="preserve">
    <value>Letztes Jahr GP (%)</value>
  </data>
  <data name="LYPct" xml:space="preserve">
    <value>Letztes Jahr %</value>
  </data>
  <data name="Manager" xml:space="preserve">
    <value>Manager</value>
  </data>
  <data name="Manufacturer" xml:space="preserve">
    <value>Hersteller</value>
  </data>
  <data name="Manufacturers" xml:space="preserve">
    <value>Hersteller</value>
  </data>
  <data name="MaritalStatus" xml:space="preserve">
    <value>Familienstand</value>
  </data>
  <data name="MasterCountryName" xml:space="preserve">
    <value>orlagenländername</value>
  </data>
  <data name="Message" xml:space="preserve">
    <value>Mitteilung</value>
  </data>
  <data name="MessageAlertShown" xml:space="preserve">
    <value>Zeigen Sie Mitteilung-Alarm?</value>
  </data>
  <data name="MessageCheckTimeout" xml:space="preserve">
    <value>Mitteilungüberprüfungsabstand</value>
  </data>
  <data name="Mobile" xml:space="preserve">
    <value>Handy</value>
  </data>
  <data name="MobileTel" xml:space="preserve">
    <value>Handy</value>
  </data>
  <data name="MTDGp" xml:space="preserve">
    <value>MTD GP</value>
  </data>
  <data name="MTDPct" xml:space="preserve">
    <value>MTD %</value>
  </data>
  <data name="Name" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="Name1" xml:space="preserve">
    <value>Name 1</value>
  </data>
  <data name="Name2" xml:space="preserve">
    <value>Name 2</value>
  </data>
  <data name="NewLot" xml:space="preserve">
    <value>Neues Los</value>
  </data>
  <data name="NewPassword" xml:space="preserve">
    <value>Neues Kennwort</value>
  </data>
  <data name="Nickname" xml:space="preserve">
    <value>Spitzname</value>
  </data>
  <data name="Notes" xml:space="preserve">
    <value>Anmerkungen</value>
  </data>
  <data name="NumberChildren" xml:space="preserve">
    <value>Zahl der Kinder</value>
  </data>
  <data name="NumberRecentlyViewedPages" xml:space="preserve">
    <value>Vor kurzem gesehene Seiten</value>
  </data>
  <data name="OfferDate" xml:space="preserve">
    <value>Angebot-Datum</value>
  </data>
  <data name="OfferPrice" xml:space="preserve">
    <value>Angebots-Preis</value>
  </data>
  <data name="OfferStatus" xml:space="preserve">
    <value>Angebot-Status</value>
  </data>
  <data name="OldPassword" xml:space="preserve">
    <value>Altes Kennwort</value>
  </data>
  <data name="OnOrder" xml:space="preserve">
    <value>Auf Auftrag</value>
  </data>
  <data name="OpenCost" xml:space="preserve">
    <value>Öffnen Sie gelandete Kosten</value>
  </data>
  <data name="OpenFreight" xml:space="preserve">
    <value>Öffnen Sie Frachtgebühren</value>
  </data>
  <data name="OpenGpPct" xml:space="preserve">
    <value>Öffnen GP (%)</value>
  </data>
  <data name="OpenPOs" xml:space="preserve">
    <value>Öffnen Sie Kaufaufträge</value>
  </data>
  <data name="OpenSales" xml:space="preserve">
    <value>Öffnen Sie Verkaufswert</value>
  </data>
  <data name="OpenSOs" xml:space="preserve">
    <value>Öffnen Sie Verkaufs-Aufträge</value>
  </data>
  <data name="OpenSOTotal" xml:space="preserve">
    <value>Informierte Verkaufs-Auftrags-Gesamtmenge</value>
  </data>
  <data name="OrderValue" xml:space="preserve">
    <value>Dieser Auftrags-Wert</value>
  </data>
  <data name="OutstandingInvoices" xml:space="preserve">
    <value>Ausstehende Rechnungen</value>
  </data>
  <data name="OverduePOs" xml:space="preserve">
    <value>Überfällige Kaufaufträge</value>
  </data>
  <data name="OverdueSOs" xml:space="preserve">
    <value>Überfällige Verkaufs-Aufträge</value>
  </data>
  <data name="OverrideInvoiceHeader" xml:space="preserve">
    <value>Schalten Sie zur Firmaüberschrift auf Rechnung um?</value>
  </data>
  <data name="Package" xml:space="preserve">
    <value>Paket</value>
  </data>
  <data name="PackageAbbreviation" xml:space="preserve">
    <value>Paket</value>
  </data>
  <data name="PackageUnit" xml:space="preserve">
    <value>Paket-Maßeinheit</value>
  </data>
  <data name="Paid" xml:space="preserve">
    <value>Zahlend</value>
  </data>
  <data name="ParentCompany" xml:space="preserve">
    <value>Parent Company</value>
  </data>
  <data name="PartMarkings" xml:space="preserve">
    <value>Teil-Markierungen</value>
  </data>
  <data name="Partner" xml:space="preserve">
    <value>Partner</value>
  </data>
  <data name="PartnerBirthday" xml:space="preserve">
    <value>Partner-Geburtstag</value>
  </data>
  <data name="PartNo" xml:space="preserve">
    <value>Part No</value>
  </data>
  <data name="PartOf" xml:space="preserve">
    <value>Teil von</value>
  </data>
  <data name="Password" xml:space="preserve">
    <value>Kennwort</value>
  </data>
  <data name="Pct" xml:space="preserve">
    <value>%</value>
  </data>
  <data name="PermissionValue" xml:space="preserve">
    <value>Erlaubnis-Wert</value>
  </data>
  <data name="PersonalAddress" xml:space="preserve">
    <value>Persönliche Adresse</value>
  </data>
  <data name="PersonalAddressName" xml:space="preserve">
    <value>Personal Address Name</value>
  </data>
  <data name="PickUp" xml:space="preserve">
    <value>Heben Sie auf</value>
  </data>
  <data name="Postcode" xml:space="preserve">
    <value>Postleitzah</value>
  </data>
  <data name="Posted" xml:space="preserve">
    <value>Bekannt gegeben</value>
  </data>
  <data name="Price" xml:space="preserve">
    <value>Preis</value>
  </data>
  <data name="PrintNotes" xml:space="preserve">
    <value>Druck-Anmerkungen</value>
  </data>
  <data name="Product" xml:space="preserve">
    <value>Produkt</value>
  </data>
  <data name="ProductAbbreviation" xml:space="preserve">
    <value>Produkt</value>
  </data>
  <data name="Progress" xml:space="preserve">
    <value>Fortschritt</value>
  </data>
  <data name="PromisedDate" xml:space="preserve">
    <value>Datum versprochen </value>
  </data>
  <data name="PurchaseOrder" xml:space="preserve">
    <value>Kaufauftrag </value>
  </data>
  <data name="PurchaseOrderNo" xml:space="preserve">
    <value>Kaufauftrag </value>
  </data>
  <data name="PurchasePrice" xml:space="preserve">
    <value>Kaufpreis</value>
  </data>
  <data name="QCNotes" xml:space="preserve">
    <value>Qualitätskontrolle-Anmerkungen</value>
  </data>
  <data name="QualityControlNotes" xml:space="preserve">
    <value>Qualitätskontrolle-Anmerkungen</value>
  </data>
  <data name="Quantities" xml:space="preserve">
    <value>Quantitäten</value>
  </data>
  <data name="Quantity" xml:space="preserve">
    <value>Quantität</value>
  </data>
  <data name="QuantityAllocated" xml:space="preserve">
    <value>Quantität zugeteilt</value>
  </data>
  <data name="QuantityAuthorised" xml:space="preserve">
    <value>Quantität Authorised</value>
  </data>
  <data name="QuantityAvailable" xml:space="preserve">
    <value>Quantität vorhanden</value>
  </data>
  <data name="QuantityAvailableForSplit" xml:space="preserve">
    <value>Qty Vorhanden für Spalte</value>
  </data>
  <data name="QuantityBackOrder" xml:space="preserve">
    <value>Quantität-unerledigter Auftrag</value>
  </data>
  <data name="QuantityInStock" xml:space="preserve">
    <value>Quantität Auf Lager</value>
  </data>
  <data name="QuantityOnOrder" xml:space="preserve">
    <value>Quantität Auf Auftrag</value>
  </data>
  <data name="QuantityOrdered" xml:space="preserve">
    <value>Quantität bestellt</value>
  </data>
  <data name="QuantityOutstanding" xml:space="preserve">
    <value>Quantität hervorragend</value>
  </data>
  <data name="QuantityReceived" xml:space="preserve">
    <value>Quantität empfangen</value>
  </data>
  <data name="QuantityRequired" xml:space="preserve">
    <value>Quantität Erforderlich</value>
  </data>
  <data name="QuantityShipped" xml:space="preserve">
    <value>Quantität versendete</value>
  </data>
  <data name="QuantityToSplit" xml:space="preserve">
    <value>Quantität zu sich aufspalten</value>
  </data>
  <data name="Quarantine" xml:space="preserve">
    <value>Unter Quarantäne gestellt</value>
  </data>
  <data name="Quarantined" xml:space="preserve">
    <value>Unter Quarantäne gestellt</value>
  </data>
  <data name="QuarantineThisItem" xml:space="preserve">
    <value>Stellen Sie dieses Einzelteil unter Quarantäne?</value>
  </data>
  <data name="QuoteNo" xml:space="preserve">
    <value>Preisangabe Nr</value>
  </data>
  <data name="RaisedBy" xml:space="preserve">
    <value>Vorbei angehoben worden</value>
  </data>
  <data name="Rate" xml:space="preserve">
    <value>Rate</value>
  </data>
  <data name="Rate2" xml:space="preserve">
    <value>Rate 2</value>
  </data>
  <data name="Rating" xml:space="preserve">
    <value>Bewertung</value>
  </data>
  <data name="Reason" xml:space="preserve">
    <value>Grund</value>
  </data>
  <data name="ReceivedBy" xml:space="preserve">
    <value>Vorbei empfangen</value>
  </data>
  <data name="ReceivedDate" xml:space="preserve">
    <value>Empfangenes Datum</value>
  </data>
  <data name="ReceivedDateFrom" xml:space="preserve">
    <value>Erhaltenes Datum Davon</value>
  </data>
  <data name="ReceivedDateTo" xml:space="preserve">
    <value>Empfangenes Datum zu</value>
  </data>
  <data name="ReceivingInstructions" xml:space="preserve">
    <value>Empfangen von Anweisungen</value>
  </data>
  <data name="ReceivingNotes" xml:space="preserve">
    <value>Frachtannahmescheine</value>
  </data>
  <data name="RecentOnly" xml:space="preserve">
    <value>Neu nur?</value>
  </data>
  <data name="Reference" xml:space="preserve">
    <value>Hinweis</value>
  </data>
  <data name="ReferenceDate" xml:space="preserve">
    <value>Bezugsdatum</value>
  </data>
  <data name="RelatedToDos" xml:space="preserve">
    <value>Bezogen, um zu tun</value>
  </data>
  <data name="Reminder" xml:space="preserve">
    <value>Anzeige</value>
  </data>
  <data name="ReminderDate" xml:space="preserve">
    <value>Anzeigen-Datum</value>
  </data>
  <data name="ReminderText" xml:space="preserve">
    <value>Anzeigen-Text</value>
  </data>
  <data name="ReminderTime" xml:space="preserve">
    <value>Anzeigen-Zeit</value>
  </data>
  <data name="ReplyTo" xml:space="preserve">
    <value>Antwort auf</value>
  </data>
  <data name="RepriceOpenOrders" xml:space="preserve">
    <value>Reprice Open Orders?</value>
  </data>
  <data name="RequiredDate" xml:space="preserve">
    <value>Datum erfordert</value>
  </data>
  <data name="RequirementNo" xml:space="preserve">
    <value>Anforderungs-Nr.</value>
  </data>
  <data name="ResalePrice" xml:space="preserve">
    <value>Wiederverkaufspreis</value>
  </data>
  <data name="ResetQuantity" xml:space="preserve">
    <value>Stellen Sie Quantität zurück</value>
  </data>
  <data name="ReturnDate" xml:space="preserve">
    <value>Rückholdatum</value>
  </data>
  <data name="RMADate" xml:space="preserve">
    <value>RMA Datum</value>
  </data>
  <data name="RoHS" xml:space="preserve">
    <value>RoHS</value>
  </data>
  <data name="Salesman" xml:space="preserve">
    <value>Verkäufer</value>
  </data>
  <data name="Salesman2Percent" xml:space="preserve">
    <value>Zusätzlicher Verkäufer %</value>
  </data>
  <data name="SalesmanName" xml:space="preserve">
    <value>Verkäufer</value>
  </data>
  <data name="SalesOrder" xml:space="preserve">
    <value>Verkaufs-Auftrag</value>
  </data>
  <data name="SalesOrderNo" xml:space="preserve">
    <value>Verkaufs-Auftrag</value>
  </data>
  <data name="Salesperson" xml:space="preserve">
    <value>Verkäufer</value>
  </data>
  <data name="Salesperson2" xml:space="preserve">
    <value>Additional Salesperson</value>
  </data>
  <data name="ScheduledCall" xml:space="preserve">
    <value>Zeitlich geplanter Anruf</value>
  </data>
  <data name="ScheduledCallFor" xml:space="preserve">
    <value>Für</value>
  </data>
  <data name="SecurityGroupName" xml:space="preserve">
    <value>Sicherheits-Gruppen-Name</value>
  </data>
  <data name="SecurityGroupNo" xml:space="preserve">
    <value>Sicherheits-Gruppe</value>
  </data>
  <data name="SecurityPageCode" xml:space="preserve">
    <value>Sicherheits-Seiten-Code</value>
  </data>
  <data name="SecurityPageFunctionNo" xml:space="preserve">
    <value>Sicherheits-Seiten-Funktion </value>
  </data>
  <data name="SelectedGroups" xml:space="preserve">
    <value>Vorgewählte Gruppen</value>
  </data>
  <data name="SelectedLogins" xml:space="preserve">
    <value>Vorgewählte Logins</value>
  </data>
  <data name="SelectItem" xml:space="preserve">
    <value>Wählen Sie Einzelteil vor</value>
  </data>
  <data name="SelectMasterCountry" xml:space="preserve">
    <value>Wählen Sie Vorlagenland vor</value>
  </data>
  <data name="SelectMasterCurrency" xml:space="preserve">
    <value>Wählen Sie Vorlagenwährung vor</value>
  </data>
  <data name="SelectNewOrExistingHeader" xml:space="preserve">
    <value>Wählen Sie eine neue oder vorhandene Überschrift vor</value>
  </data>
  <data name="SelectSource" xml:space="preserve">
    <value>Wählen Sie Quelle vor</value>
  </data>
  <data name="SelectTarget" xml:space="preserve">
    <value>Wählen Sie Ziel vor</value>
  </data>
  <data name="Sell" xml:space="preserve">
    <value>Verkauf</value>
  </data>
  <data name="SellPrice" xml:space="preserve">
    <value>Verkaufspreis</value>
  </data>
  <data name="SellShipVia" xml:space="preserve">
    <value>Verkaufs-Schiff über</value>
  </data>
  <data name="SellShipViaAccount" xml:space="preserve">
    <value>Verkaufs-Schiff über Konto</value>
  </data>
  <data name="SellShipViaNo" xml:space="preserve">
    <value>Verkaufs-Schiff über Nr.</value>
  </data>
  <data name="SerialNosRecorded" xml:space="preserve">
    <value>Seriennr. notierten</value>
  </data>
  <data name="Service" xml:space="preserve">
    <value>Service</value>
  </data>
  <data name="ShipASAP" xml:space="preserve">
    <value>Schiff So Bald Wie Möglich </value>
  </data>
  <data name="ShipCost" xml:space="preserve">
    <value>Rückstellungs-Schiff in den Kosten</value>
  </data>
  <data name="ShipFreight" xml:space="preserve">
    <value>Versendete Frachtgebühren</value>
  </data>
  <data name="ShipFrom" xml:space="preserve">
    <value>Schiff von</value>
  </data>
  <data name="ShipGpPct" xml:space="preserve">
    <value>Versendet GP (%)</value>
  </data>
  <data name="ShipInCost" xml:space="preserve">
    <value>Schiff in den Kosten</value>
  </data>
  <data name="ShipmentsListTitle" xml:space="preserve">
    <value>Zu versendende Teile:</value>
  </data>
  <data name="Shipped" xml:space="preserve">
    <value>Versendet</value>
  </data>
  <data name="ShippedBy" xml:space="preserve">
    <value>Vorbei versendet</value>
  </data>
  <data name="ShippedFrom" xml:space="preserve">
    <value>Versendet von</value>
  </data>
  <data name="Shipper" xml:space="preserve">
    <value>Verlader</value>
  </data>
  <data name="Shipping" xml:space="preserve">
    <value>Verschiffen</value>
  </data>
  <data name="ShippingAccountNo" xml:space="preserve">
    <value>Verschiffen A/C</value>
  </data>
  <data name="ShippingCost" xml:space="preserve">
    <value>Verschiffen-Kosten</value>
  </data>
  <data name="ShippingInstructions" xml:space="preserve">
    <value>Verschiffen-Anweisungen</value>
  </data>
  <data name="ShippingNotes" xml:space="preserve">
    <value>Frachtannahmescheine</value>
  </data>
  <data name="ShippingStatus" xml:space="preserve">
    <value>Verschiffen-Status</value>
  </data>
  <data name="ShipSales" xml:space="preserve">
    <value>Versendeter Verkaufswert</value>
  </data>
  <data name="ShipTo" xml:space="preserve">
    <value>Schiff zu</value>
  </data>
  <data name="ShipToAddress" xml:space="preserve">
    <value>Zu wenden Schiff sich</value>
  </data>
  <data name="ShipToAddressName" xml:space="preserve">
    <value>Schiff, zum des Namens zu adressieren</value>
  </data>
  <data name="ShipVia" xml:space="preserve">
    <value>Schiff über</value>
  </data>
  <data name="ShipViaAccount" xml:space="preserve">
    <value>Schiff über Konto</value>
  </data>
  <data name="ShipViaNo" xml:space="preserve">
    <value>Schiff über</value>
  </data>
  <data name="ShouldMailBeSent" xml:space="preserve">
    <value>Senden Sie Post?</value>
  </data>
  <data name="SMTPHost" xml:space="preserve">
    <value>SMTP Host</value>
  </data>
  <data name="SMTPPassword" xml:space="preserve">
    <value>SMTP Password</value>
  </data>
  <data name="SMTPPort" xml:space="preserve">
    <value>SMTP Port</value>
  </data>
  <data name="SMTPUsername" xml:space="preserve">
    <value>SMTP Username</value>
  </data>
  <data name="SOName" xml:space="preserve">
    <value>SO Name</value>
  </data>
  <data name="SplitQuantities" xml:space="preserve">
    <value>Aufgeteilte Quantitäten</value>
  </data>
  <data name="SRMADate" xml:space="preserve">
    <value>Datum des Lieferanten-RMA </value>
  </data>
  <data name="SRMANo" xml:space="preserve">
    <value>Lieferanten-RMA </value>
  </data>
  <data name="StandardShipping" xml:space="preserve">
    <value>Standardverschiffen</value>
  </data>
  <data name="State" xml:space="preserve">
    <value>Zustand</value>
  </data>
  <data name="Status" xml:space="preserve">
    <value>Status</value>
  </data>
  <data name="StockKeepingUnit" xml:space="preserve">
    <value>Auf lager haltene Maßeinheit</value>
  </data>
  <data name="StockLogReason" xml:space="preserve">
    <value>Maschinenbordbuch-Grund</value>
  </data>
  <data name="Subject" xml:space="preserve">
    <value>Thema</value>
  </data>
  <data name="SubTotal" xml:space="preserve">
    <value>Teilsumme</value>
  </data>
  <data name="SuccessfulSaveMessageTime" xml:space="preserve">
    <value>Erfolgreich außer Mitteilungzeit</value>
  </data>
  <data name="Supplier" xml:space="preserve">
    <value>Lieferant</value>
  </data>
  <data name="SupplierCredit" xml:space="preserve">
    <value>Lieferantenkredit</value>
  </data>
  <data name="SupplierInvoice" xml:space="preserve">
    <value>Lieferanten-Rechnung</value>
  </data>
  <data name="SupplierName" xml:space="preserve">
    <value>Lieferanten-Name</value>
  </data>
  <data name="SupplierNo" xml:space="preserve">
    <value>Lieferanten-Nr.</value>
  </data>
  <data name="SupplierNotes" xml:space="preserve">
    <value>Lieferanten-Anmerkungen</value>
  </data>
  <data name="SupplierPart" xml:space="preserve">
    <value>Lieferanten-Teil</value>
  </data>
  <data name="SupplierPartNo" xml:space="preserve">
    <value>Lieferanten-Teilenummer</value>
  </data>
  <data name="SupplierRating" xml:space="preserve">
    <value>Lieferantenbewertung</value>
  </data>
  <data name="SupplierReturn" xml:space="preserve">
    <value>Lieferanten-Rückkehr</value>
  </data>
  <data name="SupplierRMA" xml:space="preserve">
    <value>Lieferanten-RMA </value>
  </data>
  <data name="SupplierRMADate" xml:space="preserve">
    <value>Datum des Lieferanten-RMA </value>
  </data>
  <data name="SupplierRMADateFrom" xml:space="preserve">
    <value>Datum des Lieferanten-RMA von</value>
  </data>
  <data name="SupplierRMADateTo" xml:space="preserve">
    <value>Datum des Lieferanten-RMA zu</value>
  </data>
  <data name="SupplierRMANo" xml:space="preserve">
    <value>Lieferanten-RMA </value>
  </data>
  <data name="Surname" xml:space="preserve">
    <value>Familienname</value>
  </data>
  <data name="Symbol" xml:space="preserve">
    <value>Symbol</value>
  </data>
  <data name="TableName" xml:space="preserve">
    <value>Tätigkeit</value>
  </data>
  <data name="TargetPrice" xml:space="preserve">
    <value>Richtpreis</value>
  </data>
  <data name="Tax" xml:space="preserve">
    <value>Steuer</value>
  </data>
  <data name="Tax1On2" xml:space="preserve">
    <value>Steuer 1 auf 2</value>
  </data>
  <data name="Taxable" xml:space="preserve">
    <value>Steuerpflichtig </value>
  </data>
  <data name="TaxCode" xml:space="preserve">
    <value>Steuerkennziffer</value>
  </data>
  <data name="TaxName" xml:space="preserve">
    <value>Steuer-Name</value>
  </data>
  <data name="Team" xml:space="preserve">
    <value>Mannschaft</value>
  </data>
  <data name="Tel" xml:space="preserve">
    <value>Tel</value>
  </data>
  <data name="Telephone" xml:space="preserve">
    <value>Telephone</value>
  </data>
  <data name="Telephone800" xml:space="preserve">
    <value>Nr. 0800</value>
  </data>
  <data name="TelExt" xml:space="preserve">
    <value>Verlängerung</value>
  </data>
  <data name="TelPrefix" xml:space="preserve">
    <value>Wählender Code</value>
  </data>
  <data name="Terms" xml:space="preserve">
    <value>Ausdrücke</value>
  </data>
  <data name="Text" xml:space="preserve">
    <value>Text</value>
  </data>
  <data name="Time" xml:space="preserve">
    <value>Zeit</value>
  </data>
  <data name="Title" xml:space="preserve">
    <value>Titel</value>
  </data>
  <data name="TMGpPct" xml:space="preserve">
    <value>Dieser Monat GP (%)</value>
  </data>
  <data name="To" xml:space="preserve">
    <value>Zu</value>
  </data>
  <data name="Total" xml:space="preserve">
    <value>Gesamtmenge</value>
  </data>
  <data name="TotalCost" xml:space="preserve">
    <value>Gesamtmenge landete Kosten</value>
  </data>
  <data name="TotalFreight" xml:space="preserve">
    <value>Gesamtfrachtgebühren</value>
  </data>
  <data name="TotalGpPct" xml:space="preserve">
    <value>Gesamtmenge GP (%)</value>
  </data>
  <data name="TotalLandedCost" xml:space="preserve">
    <value>Gesamtmenge landete Kosten</value>
  </data>
  <data name="TotalSales" xml:space="preserve">
    <value>Gesamtverkaufswert</value>
  </data>
  <data name="TotalShipInCost" xml:space="preserve">
    <value>Empfohlenes Schiff in den Kosten</value>
  </data>
  <data name="TotalWork" xml:space="preserve">
    <value>Gesamtarbeit</value>
  </data>
  <data name="TYGpPct" xml:space="preserve">
    <value>Dieses Jahr GP (%)</value>
  </data>
  <data name="Type" xml:space="preserve">
    <value>Art</value>
  </data>
  <data name="Unauthorised" xml:space="preserve">
    <value>Nicht autorisiert</value>
  </data>
  <data name="UnauthorisedOnly" xml:space="preserve">
    <value>Nicht autorisiert nur?</value>
  </data>
  <data name="UnselectedGroups" xml:space="preserve">
    <value>Unselected Groups</value>
  </data>
  <data name="UnselectedLogins" xml:space="preserve">
    <value>Gemischte Logins</value>
  </data>
  <data name="UpdateBillToAddress" xml:space="preserve">
    <value>Update zu wenden Bill sich?</value>
  </data>
  <data name="UpdateShipToAddress" xml:space="preserve">
    <value>Zu wenden Update-Schiff sich?</value>
  </data>
  <data name="URL" xml:space="preserve">
    <value>Website</value>
  </data>
  <data name="Usage" xml:space="preserve">
    <value>Verbrauch</value>
  </data>
  <data name="User" xml:space="preserve">
    <value>Benutzer</value>
  </data>
  <data name="UserField1" xml:space="preserve">
    <value>Benutzer-Feld 1</value>
  </data>
  <data name="UserField10" xml:space="preserve">
    <value>Benutzer-Feld 10</value>
  </data>
  <data name="UserField2" xml:space="preserve">
    <value>Benutzer-Feld 2</value>
  </data>
  <data name="UserField3" xml:space="preserve">
    <value>Benutzer-Feld 3</value>
  </data>
  <data name="UserField4" xml:space="preserve">
    <value>Benutzer-Feld 4</value>
  </data>
  <data name="UserField5" xml:space="preserve">
    <value>Benutzer-Feld 5</value>
  </data>
  <data name="UserField6" xml:space="preserve">
    <value>Benutzer-Feld 6</value>
  </data>
  <data name="UserField7" xml:space="preserve">
    <value>Benutzer-Feld 7</value>
  </data>
  <data name="UserField8" xml:space="preserve">
    <value>Benutzer-Feld 8</value>
  </data>
  <data name="UserField9" xml:space="preserve">
    <value>Benutzer-Feld 9</value>
  </data>
  <data name="Username" xml:space="preserve">
    <value>Benutzer</value>
  </data>
  <data name="VATNumber" xml:space="preserve">
    <value>Vat-Nr.</value>
  </data>
  <data name="Warehouse" xml:space="preserve">
    <value>Lager</value>
  </data>
  <data name="Weight" xml:space="preserve">
    <value>Gewicht</value>
  </data>
  <data name="WeightInPounds" xml:space="preserve">
    <value>Gewicht (lbs)?</value>
  </data>
  <data name="YearToDate" xml:space="preserve">
    <value>Jahr bis jetzt</value>
  </data>
  <data name="YTDGp" xml:space="preserve">
    <value>Jahr bis jetzt GP</value>
  </data>
  <data name="YTDPct" xml:space="preserve">
    <value>Jahr bis jetzt %</value>
  </data>
  <data name="BOM" xml:space="preserve">
    <value>BOM</value>
  </data>
  <data name="BOMName" xml:space="preserve">
    <value>HUBRFQ Name</value>
  </data>
  <data name="PartWatch" xml:space="preserve">
    <value>PartWatch</value>
  </data>
  <data name="OwnDataVisibleToOthers" xml:space="preserve">
    <value>Daten sind zu anderen Firmen sichtbar</value>
  </data>
 
</root>