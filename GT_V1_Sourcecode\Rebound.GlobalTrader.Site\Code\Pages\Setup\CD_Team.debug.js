///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Pages.Setup");

Rebound.GlobalTrader.Site.Pages.Setup.CD_Team = function(el) { 
	Rebound.GlobalTrader.Site.Pages.Setup.CD_Team.initializeBase(this, [el]);
};

Rebound.GlobalTrader.Site.Pages.Setup.CD_Team.prototype = {

	get_ctlTeam: function() { return this._ctlTeam; }, 	set_ctlTeam: function(v) { if (this._ctlTeam !== v)  this._ctlTeam = v; }, 
	get_ctlTeamMembers: function() { return this._ctlTeamMembers; }, 	set_ctlTeamMembers: function(v) { if (this._ctlTeamMembers !== v)  this._ctlTeamMembers = v; }, 

	initialize: function() {
		Rebound.GlobalTrader.Site.Pages.Setup.CD_Team.callBaseMethod(this, "initialize");
	},
	
	goInit: function() {
		this._ctlTeam.addSelectTeam(Function.createDelegate(this, this.ctlTeam_SelectTeam));
		Rebound.GlobalTrader.Site.Pages.Setup.CD_Team.callBaseMethod(this, "goInit");
	},

	dispose: function() { 
		if (this.isDisposed) return;
		if (this._ctlTeam) this._ctlTeam.dispose();
		if (this._ctlTeamMembers) this._ctlTeamMembers.dispose();
		this._ctlTeam = null;
		this._ctlTeamMembers = null;
		Rebound.GlobalTrader.Site.Pages.Setup.CD_Team.callBaseMethod(this, "dispose");
	},
	
	ctlTeam_SelectTeam: function() {
		this._ctlTeamMembers._intTeamID = this._ctlTeam._intTeamID;
		this._ctlTeamMembers.refresh();
		this._ctlTeam._tbl.resizeColumns();
		this._ctlTeamMembers.show(true);
	},
	
	showNuggets: function(bln) {
		this._ctlSecurityUserGroups.show(bln);
	}
	
};

Rebound.GlobalTrader.Site.Pages.Setup.CD_Team.registerClass("Rebound.GlobalTrader.Site.Pages.Setup.CD_Team", Rebound.GlobalTrader.Site.Pages.Content, Sys.IDisposable);
