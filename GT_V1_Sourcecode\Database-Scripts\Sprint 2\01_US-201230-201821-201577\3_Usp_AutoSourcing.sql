SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO
IF OBJECT_ID('Usp_AutoSourcing','P') IS NOT NULL
    DROP PROC [dbo].[Usp_AutoSourcing]
GO
/* 
===========================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-201821]		An.TranTan			07-May-2024		Update			Get more info for auto sourcing  display in HUB site
[US-201577]		An.TranTan			21-May-2024		Update			Get more info for auto sourcing  display in HUB site
===========================================================================================
*/

CREATE Procedure [dbo].[Usp_AutoSourcing]
(
    @BOMManagerNo int,
    @curPage int = 1,
    @Rpp int = 10,
    @CustomerRequirementID INT = null
)
WITH RECOMPILE
AS
BEGIN
    EXEC USP_AutoSourcingResultCalculation @BOMManagerNo

    CREATE TABLE #PartTemp
    (
        PartNo varchar(100) COLLATE Latin1_General_CI_AS,
        Fullpart varchar(100) COLLATE Latin1_General_CI_AS,
        CustomerRequirementId int
    )
    IF (@CustomerRequirementID is null or @CustomerRequirementID = '')
    BEGIN
        insert into #Parttemp
        select cus.Part,
               cus.FullPart,
               CustomerRequirementId
        from dbo.tbCustomerRequirement cus
        where cus.BOMManagerNo = @BOMManagerNo
    END
    ELSE
    BEGIN
        insert into #Parttemp
        select cus.Part,
               cus.FullPart,
               CustomerRequirementId
        from dbo.tbCustomerRequirement cus
        where cus.BOMManagerNo = @BOMManagerNo
              and CustomerRequirementId = @CustomerRequirementID
    END

    DECLARE @TotalRecords int,
            @skip int
    SELECT @TotalRecords = count(a.SourceId)
    FROM tbAutoSource a
        join tbCustomerRequirement cr
            on cr.CustomerRequirementId = a.CustomerRequirementId
        join #Parttemp prt
            on cr.CustomerRequirementId = prt.CustomerRequirementId --and  cr.FullPart = prt.PartNo                 
    WHERE a.BOMManagerNo = @BOMManagerNo
          and isnull(a.IsDeleted, 0) = 0
    SET @skip = (@Rpp * (@curPage - 1))
    IF (@skip >= @TotalRecords and @TotalRecords > 0)
    BEGIN
        SET @curPage = CAST(@TotalRecords / @Rpp as int)
        SET @skip = CAST((@Rpp * (@curPage - 1)) as int)
    END
    IF (@skip < 0)
    BEGIN
        SET @skip = 0
    END
    SELECT a.*,
           abm.Reason,
           cr.REQStatus,
           (CASE
                WHEN EXISTS
                     (
                         SELECT SourcingResultId
                         FROM tbSourcingResult
                         WHERE AutoSourceID = a.SourceId
                     ) THEN
                    'TRUE'
                ELSE
                    'FALSE'
            END
           ) as ItemReleased,
           @TotalRecords as TotalRecords,
           c.CurrencyCode,
           cast(ISNULL(o.AlternateStatus, 0) as BIT) as AlternateOfferFlag,
           tbprd.ProductDescription as ProductName,
           tbpck.PackageDescription as PackageName,
		   ro.[Description] as ROHSDescription,
		   gcl.GlobalCountryName as CountryOfOrigin
    FROM tbAutoSource a
        JOIN tbCustomerRequirement cr
            ON cr.CustomerRequirementId = a.CustomerRequirementId
        JOIN #Parttemp prt
            ON cr.CustomerRequirementId = prt.CustomerRequirementId --and  cr.FullPart = prt.PartNo                 
        LEFT JOIN tbaudit_BOMManager abm
            ON abm.AutoSourceNo = a.SourceID AND abm.BOMManagerNo = @BOMManagerNo
        LEFT JOIN tbCurrency c
            ON c.CurrencyId = a.CurrencyNo
        --LEFT JOIN tbEMSOffers tbems
        --    ON a.OfferId = tbems.EMSOfferId
		LEFT JOIN BorisGlobalTraderImports.dbo.tbOffer o
            ON a.OfferId = o.OfferId
        LEFT JOIN tbproduct tbprd
            ON a.productno = tbprd.ProductId
        LEFT JOIN tbPackage tbpck
            ON a.PackageNo = tbpck.PackageId
		LEFT JOIN tbROHSStatus ro
			ON ro.ROHSStatusId = a.ROHS
		LEFT JOIN tbGlobalCountryList gcl
			ON gcl.GlobalCountryId = a.IHSCountryOfOriginNo
    WHERE a.BOMManagerNo = @BOMManagerNo
          AND isnull(a.IsDeleted, 0) = 0
    ORDER BY cr.CustomerRequirementId OFFSET @skip ROWS FETCH NEXT @Rpp ROWS ONLY
END
GO


