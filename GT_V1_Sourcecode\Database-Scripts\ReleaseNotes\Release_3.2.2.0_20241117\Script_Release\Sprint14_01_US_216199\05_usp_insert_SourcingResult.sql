﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO
/* 
===========================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-216199]		An.TranTan			13-Nov-2024		UPDATE			Update supplier mfr/package/product/date code when insert from client side
===========================================================================================
*/
CREATE OR ALTER PROCEDURE [dbo].[usp_insert_SourcingResult]
	--***************************************************************************************************                          
	--* RP 16.02.2010:                          
	--* - Copy the result to the Offer table (unless it is from Excess)               
	--* - Abhinav      07-09-2021        Add new column ISSoftDelete  and add partwatch match functionality.                  
	--* - Abhinav      10-09-2021        Remove auto partwatch functionality.                  
	--***************************************************************************************************                          
	@CustomerRequirementNo INT
	,@TypeName NVARCHAR(40) = NULL
	,@Notes NVARCHAR(128) = NULL
	,@Part NVARCHAR(30)
	,@ManufacturerNo INT = NULL
	,@DateCode NVARCHAR(5) = NULL
	,@ProductNo INT = NULL
	,@PackageNo INT = NULL
	,@Quantity INT
	,@Price FLOAT
	,@CurrencyNo INT = NULL
	,@OriginalEntryDate DATETIME = NULL
	,@Salesman INT = NULL
	,@OfferStatusNo INT = NULL
	,@SupplierNo INT = NULL
	,@ROHS TINYINT = NULL
	,@ClientNo INT
	,@UpdatedBy INT = NULL
	,@MSLLevelNo INT = NULL
	,@IsSupHasCurrency BIT = NULL
	,@SourcingResultId INT OUTPUT
	,@OfferId INT = NULL OUTPUT
AS
BEGIN
	--. When adding sourcing results against requirement from a company not approved as a supplier allow the user to select a currency and then set this as the default supplier currency. Warn the user this is happening with a pop up.        
	IF isnull(@IsSupHasCurrency, 0) = 0
	BEGIN
		UPDATE tbCompany
		SET POCurrencyNo = @CurrencyNo
		WHERE CompanyId = @SupplierNo
	END

	IF (@CustomerRequirementNo IS NOT NULL)
	BEGIN
		UPDATE tbCustomerRequirement
		SET REQStatus = 3
		WHERE CustomerRequirementId = @CustomerRequirementNo
	END

	IF (@CustomerRequirementNo <= 0)
	BEGIN
		RETURN
	END

	DECLARE @SourcingTableItemNo INT
			,@SupplierName NVARCHAR(256) = NULL
			,@ManufacturerName NVARCHAR(256) = NULL
			,@ProductName NVARCHAR(256) = NULL
			,@PackageName NVARCHAR(120) = NULL

	SELECT @SupplierName = CompanyName FROM tbCompany WHERE CompanyId = @SupplierNo;
	SELECT @ManufacturerName = ManufacturerName FROM tbManufacturer WHERE ManufacturerId = @ManufacturerNo;
	SELECT @ProductName = ProductName FROM tbProduct WHERE ProductId = @ProductNo;
	SELECT @PackageName = PackageName FROM tbPackage WHERE PackageId = @PackageNo;

	INSERT INTO [BorisGlobalTraderImports].[dbo].[tbOffer] (
		FullPart
		,Part
		,ManufacturerNo
		,DateCode
		,ProductNo
		,PackageNo
		,Quantity
		,Price
		,OriginalEntryDate
		,Salesman
		,SupplierNo
		,CurrencyNo
		,ROHS
		,UpdatedBy
		,DLUP
		,OfferStatusNo
		,OfferStatusChangeDate
		,OfferStatusChangeLoginNo
		,SupplierName
		,Notes
		,ManufacturerName
		,ProductName
		,PackageName
		,ClientNo
		,MSLLevelNo

		)
	SELECT dbo.ufn_get_fullpart(@Part)
		,@Part
		,@ManufacturerNo
		,@DateCode
		,@ProductNo
		,@PackageNo
		,@Quantity
		,@Price
		,@OriginalEntryDate
		,@Salesman
		,@SupplierNo
		,@CurrencyNo
		,@ROHS
		,@UpdatedBy
		,CURRENT_TIMESTAMP
		,@OfferStatusNo
		,CURRENT_TIMESTAMP
		,@UpdatedBy
		,@SupplierName
		,@Notes
		,@ManufacturerName
		,@ProductName
		,@PackageName
		,@ClientNo
		,@MSLLevelNo

	SET @SourcingTableItemNo = scope_identity()
	SET @OfferId = @SourcingTableItemNo

	--add sourcing result                      
	INSERT INTO dbo.tbSourcingResult (
		CustomerRequirementNo
		,SourcingTable
		,SourcingTableItemNo
		,TypeName
		,Notes
		,FullPart
		,Part
		,ManufacturerNo
		,DateCode
		,ProductNo
		,PackageNo
		,Quantity
		,Price
		,CurrencyNo
		,OriginalEntryDate
		,Salesman
		,OfferStatusNo
		,OfferStatusChangeDate
		,OfferStatusChangeLoginNo
		,SupplierNo
		,ROHS
		,UpdatedBy
		,Buyer
		,MSLLevelNo
		,PartWatchMatch
		,IsSoftDelete
		,[SupplierManufacturerName]
		,[SupplierDateCode]
		,[SupplierPackageType]
		,[SupplierProductType])
	VALUES (
		@CustomerRequirementNo
		,'OF'
		,@SourcingTableItemNo
		,@TypeName
		,@Notes
		,dbo.ufn_get_fullpart(@Part)
		,@Part
		,@ManufacturerNo
		,@DateCode
		,@ProductNo
		,@PackageNo
		,@Quantity
		,@Price
		,@CurrencyNo
		,@OriginalEntryDate
		,@Salesman
		,@OfferStatusNo
		,CURRENT_TIMESTAMP
		,@UpdatedBy
		,@SupplierNo
		,@ROHS
		,@UpdatedBy
		,@UpdatedBy
		,@MSLLevelNo
		,0
		,0
		,@ManufacturerName
		,@DateCode
		,@PackageName
		,@ProductName)

	SET @SourcingResultId = scope_identity()
END
GO


