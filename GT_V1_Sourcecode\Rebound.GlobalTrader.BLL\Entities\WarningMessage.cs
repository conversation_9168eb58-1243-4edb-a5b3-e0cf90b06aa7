﻿using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;
using Rebound.GlobalTrader.DAL;
using Rebound.GlobalTrader.BLL;

namespace Rebound.GlobalTrader.BLL
{
   public partial class WarningMessage : BizObject
    {
        #region Properties

        protected static DAL.WarningMessageElement Settings
        {
            get { return Globals.Settings.WarningMessages; }
        }

        /// <summary>
        /// SystemWarningMessageId
        /// </summary>
        public System.Int32 SystemWarningMessageId { get; set; }
        /// <summary>
        /// ClientNo
        /// </summary>
        public System.Int32 ClientNo { get; set; }
        /// <summary>
        /// SystemDocumentNo
        /// </summary>
        public System.Int32 SystemDocumentNo { get; set; }
        /// <summary>
        /// WarningText
        /// </summary>
        public System.String WarningText { get; set; }
        /// <summary>
        /// UpdatedBy
        /// </summary>
        public System.Int32? UpdatedBy { get; set; }
        /// <summary>
        /// DLUP
        /// </summary>
        public System.DateTime? DLUP { get; set; }
        /// <summary>
        /// WarningKey
        /// </summary>
        public System.String WarningKey { get; set; }

        /// <summary>
        /// ApplyToCatagory (from Table)
        /// </summary>
        public System.String ApplyToCatagory { get; set; }

        /// <summary>
        /// ApplyTo (from Table)
        /// </summary>
        public System.String ApplyTo { get; set; }

        /// <summary>
        /// InActive (from Table)
        /// </summary>
        public System.Boolean? InActive { get; set; }

        /// <summary>
        /// ProductName (from Table)
        /// </summary>
        public System.String ProductName { get; set; }

        /// <summary>
        /// ProductDescription (from Table)
        /// </summary>
        public System.String ProductDescription { get; set; }
        /// <summary>
        /// GlobalProductNameId (from Table)
        /// </summary>
        public System.Int32 GlobalProductNameId { get; set; }
        /// <summary>
        /// ProductId (from Table)
        /// </summary>
        public System.Int32 ProductId { get; set; }

        public System.Int32 GlobalProductCategoryId { get; set; }
        #endregion

        #region Methods

        /// <summary>
        /// Delete
        /// Calls [usp_delete_SystemDocumentFooter]
        /// </summary>
        public static bool Delete(System.Int32? systemDocumentFooterNo)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.SystemDocumentFooter.Delete(systemDocumentFooterNo);
        }
        /// <summary>
        /// Insert
        /// Calls [usp_insert_SystemWarningMessage]
        /// </summary>
        public static int Insert(System.Int32? clientNo, System.Int32? WarningNo, System.String WarningText,System.Int32? ApplyCatagoryNo,System.Int32? ApplyTo, System.Int32? updatedBy)
        {
            Int32 objReturn = Rebound.GlobalTrader.DAL.SiteProvider.WarningMessage.Insert(clientNo, WarningNo, WarningText, ApplyCatagoryNo, ApplyTo, updatedBy);
            return objReturn;
        }
        /// <summary>
        /// Insert
        /// Calls [usp_CheckDuplicate_SystemWarningMessage]
        /// </summary>
        public static int CheckDuplicate(System.Int32? clientNo, System.Int32? WarningNo, System.Int32? ApplyCatagoryNo, System.Int32? ApplyTo)
        {
            Int32 objReturn = Rebound.GlobalTrader.DAL.SiteProvider.WarningMessage.CheckDuplicate(clientNo, WarningNo,ApplyCatagoryNo, ApplyTo);
            return objReturn;
        }
        /// <summary>
        /// Insert
        /// Calls [usp_CheckDuplicate_SystemWarningMessage]
        /// </summary>
        public static bool CheckApplyCatagory(System.Int32? WarningNo)
        {
            bool objReturn = Rebound.GlobalTrader.DAL.SiteProvider.WarningMessage.CheckApplyCatagory(WarningNo);
            return objReturn;
        }
        /// <summary>
        /// Insert (without parameters)
        /// Calls [usp_insert_SystemDocumentFooter]
        /// </summary>
        //public Int32 Insert()
        //{
        //    return Rebound.GlobalTrader.DAL.SiteProvider.SystemDocumentFooter.Insert(ClientNo, SystemDocumentNo, FooterText, UpdatedBy);
        //}
        /// <summary>
        /// Get
        /// Calls [usp_select_SystemDocumentFooter]
        /// </summary>
        public static WarningMessage Get(System.Int32? systemDocumentFooterNo)
        {
            Rebound.GlobalTrader.DAL.WarningMessageDetails objDetails = Rebound.GlobalTrader.DAL.SiteProvider.WarningMessage.Get(systemDocumentFooterNo);
            if (objDetails == null)
            {
                return null;
            }
            else
            {
                WarningMessage obj = new WarningMessage();
                obj.SystemWarningMessageId = objDetails.SystemWarningMessageId;
                obj.ClientNo = objDetails.ClientNo;
                obj.WarningKey = objDetails.WarningKey;
                obj.WarningText = objDetails.WarningText;
                obj.UpdatedBy = objDetails.UpdatedBy;
                obj.DLUP = objDetails.DLUP;
                obj.ApplyToCatagory = objDetails.ApplyToCatagory;
                obj.ApplyTo = objDetails.ApplyTo;
                obj.InActive = objDetails.InActive;
                objDetails = null;
                return obj;
            }
        }
        /// <summary>
        /// GetForClientAndDocument
        /// Calls [usp_select_SystemDocumentFooter_for_Client_and_Document]
        /// </summary>
        public static SystemDocumentFooter GetForClientAndDocument(System.Int32? clientNo, System.Int32? systemDocumentNo)
        {
            Rebound.GlobalTrader.DAL.SystemDocumentFooterDetails objDetails = Rebound.GlobalTrader.DAL.SiteProvider.SystemDocumentFooter.GetForClientAndDocument(clientNo, systemDocumentNo);
            if (objDetails == null)
            {
                return null;
            }
            else
            {
                SystemDocumentFooter obj = new SystemDocumentFooter();
                obj.FooterText = objDetails.FooterText;
                objDetails = null;
                return obj;
            }
        }
        /// <summary>
        /// GetListForClient
        /// Calls [usp_selectAll_SystemDocumentFooter_for_Client]
        /// </summary>
        public static List<WarningMessage> GetListForClient(System.Int32? clientNo)
        {
            List<WarningMessageDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.WarningMessage.GetListForClient(clientNo);
            if (lstDetails == null)
            {
                return new List<WarningMessage>();
            }
            else
            {
                List<WarningMessage> lst = new List<WarningMessage>();
                foreach (WarningMessageDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.WarningMessage obj = new Rebound.GlobalTrader.BLL.WarningMessage();
                    obj.SystemWarningMessageId = objDetails.SystemWarningMessageId;
                    obj.ClientNo = objDetails.ClientNo;
                    obj.WarningKey = objDetails.WarningKey;
                    obj.WarningText = objDetails.WarningText;
                    obj.DLUP = objDetails.DLUP;
                    obj.UpdatedBy = objDetails.UpdatedBy;
                    obj.ApplyToCatagory = objDetails.ApplyToCatagory;
                    obj.ApplyTo = objDetails.ApplyTo;
                    obj.InActive = objDetails.InActive;
                    obj.ProductName = objDetails.ProductName;
                    obj.ProductDescription = objDetails.ProductDescription;
                    obj.GlobalProductNameId = objDetails.GlobalProductNameId;
                    obj.ProductId = objDetails.ProductId;
                    obj.GlobalProductCategoryId = objDetails.GlobalProductCategoryId;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }
        /// <summary>
        /// Update
        /// Calls [usp_update_SystemDocumentFooter]
        /// </summary>
        public static bool Update(System.Int32? SystemWarningMessageId, System.Int32? clientNo,  System.String WarningText, System.Int32? updatedBy,System.Boolean? InActive)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.WarningMessage.Update(SystemWarningMessageId, clientNo, WarningText, updatedBy, InActive);
        }
        /// <summary>
        /// Update (without parameters)
        /// Calls [usp_update_SystemDocumentFooter]
        /// </summary>
        public bool Update()
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.WarningMessage.Update(SystemWarningMessageId, ClientNo, WarningText, UpdatedBy,InActive);
        }

        private static SystemDocumentFooter PopulateFromDBDetailsObject(SystemDocumentFooterDetails obj)
        {
            SystemDocumentFooter objNew = new SystemDocumentFooter();
            objNew.SystemDocumentFooterId = obj.SystemDocumentFooterId;
            objNew.ClientNo = obj.ClientNo;
            objNew.SystemDocumentNo = obj.SystemDocumentNo;
            objNew.FooterText = obj.FooterText;
            objNew.UpdatedBy = obj.UpdatedBy;
            objNew.DLUP = obj.DLUP;
            objNew.SystemDocumentName = obj.SystemDocumentName;
            return objNew;
        }

        #endregion
    }
}
