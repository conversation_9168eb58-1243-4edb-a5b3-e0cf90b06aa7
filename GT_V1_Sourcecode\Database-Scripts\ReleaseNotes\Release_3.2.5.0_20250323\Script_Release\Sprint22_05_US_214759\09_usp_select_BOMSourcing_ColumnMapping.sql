﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

/*   
===========================================================================================  
TASK			UPDATED BY      DATE			ACTION		DESCRIPTION  
[US-214760]		An.TranTan		14-Jan-2025		CREATE		Get default column mapping for BOM import sourcing results
===========================================================================================  
*/ 
CREATE OR ALTER   PROCEDURE [dbo].[usp_select_BOMSourcing_ColumnMapping]                
AS                
BEGIN
	IF EXISTS (
		SELECT TOP 1 1 FROM [BorisGlobalTraderImports].[dbo].[tbBOMSourcing_ColumnMapping] WITH(NOLOCK)
	)
	BEGIN

		SELECT TOP (1) 
			[Manufacturer]
		    ,[SupplierPart]
		    ,[OfferedQuantity]
		    ,[SupplierCost]
		    ,[SPQ]
		    ,[MOQ]
		    ,[SupplierName]
		    ,[MSL]
		    ,[Notes]
		    ,[DateCode]
		    ,[QtyInStock]
		    ,[OfferStatus]
		    ,[BuyPrice]
		    ,[SellPrice]
		    ,[ShippingCost]
		    ,[Package]
		    ,[ROHS]
		    ,[Currency]
		    ,[FactorySealed]
		    ,[Region]
		    ,[LeadTime]
		    ,[LastTimeBuy]
		    ,[DeliveryDate]
		    ,[CustomerRefNo]
		FROM [BorisGlobalTraderImports].[dbo].[tbBOMSourcing_ColumnMapping] WITH(NOLOCK)
	END
	ELSE BEGIN
		SELECT    
			NULL AS [Manufacturer]
		    ,NULL AS [SupplierPart]
		    ,NULL AS [OfferedQuantity]
		    ,NULL AS [SupplierCost]
		    ,NULL AS [SPQ]
		    ,NULL AS [MOQ]
		    ,NULL AS [SupplierName]
		    ,NULL AS [MSL]
		    ,NULL AS [Notes]
		    ,NULL AS [DateCode]
		    ,NULL AS [QtyInStock]
		    ,NULL AS [OfferStatus]
		    ,NULL AS [BuyPrice]
		    ,NULL AS [SellPrice]
		    ,NULL AS [ShippingCost]
		    ,NULL AS [Package]
		    ,NULL AS [ROHS]
		    ,NULL AS [Currency]
		    ,NULL AS [FactorySealed]
		    ,NULL AS [Region]
		    ,NULL AS [LeadTime]
		    ,NULL AS [LastTimeBuy]
		    ,NULL AS [DeliveryDate]
		    ,NULL AS [CustomerRefNo]
	END
END
GO

/*
exec usp_select_BOMSourcing_ColumnMapping
*/