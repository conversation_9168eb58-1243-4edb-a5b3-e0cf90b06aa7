Type.registerNamespace("Rebound.GlobalTrader.Site.Pages.Orders");Rebound.GlobalTrader.Site.Pages.Orders.CustomerRequirementDetail=function(n){Rebound.GlobalTrader.Site.Pages.Orders.CustomerRequirementDetail.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Pages.Orders.CustomerRequirementDetail.prototype={get_intCustomerRequirementID:function(){return this._intCustomerRequirementID},set_intCustomerRequirementID:function(n){this._intCustomerRequirementID!==n&&(this._intCustomerRequirementID=n)},get_ctlMainInfo:function(){return this._ctlMainInfo},set_ctlMainInfo:function(n){this._ctlMainInfo!==n&&(this._ctlMainInfo=n)},get_ctlSourcingResults:function(){return this._ctlSourcingResults},set_ctlSourcingResults:function(n){this._ctlSourcingResults!==n&&(this._ctlSourcingResults=n)},get_ctlSourcing:function(){return this._ctlSourcing},set_ctlSourcing:function(n){this._ctlSourcing!==n&&(this._ctlSourcing=n)},get_lblStatus:function(){return this._lblStatus},set_lblStatus:function(n){this._lblStatus!==n&&(this._lblStatus=n)},get_pnlStatus:function(){return this._pnlStatus},set_pnlStatus:function(n){this._pnlStatus!==n&&(this._pnlStatus=n)},initialize:function(){Rebound.GlobalTrader.Site.Pages.Orders.CustomerRequirementDetail.callBaseMethod(this,"initialize")},goInit:function(){this._ctlMainInfo&&this._ctlMainInfo.addPartSelected(Function.createDelegate(this,this.selectPart));this._ctlSourcing&&this._ctlSourcing.addSourcingResultAdded(Function.createDelegate(this,this.ctlSourcing_SourcingResultAdded));this._ctlMainInfo&&this._ctlMainInfo.addStartGetData(Function.createDelegate(this,this.mainInfoStartGetData));this._ctlMainInfo&&this._ctlMainInfo.addGotDataOK(Function.createDelegate(this,this.mainInfoGotDataOK));this._ctlSourcingResults&&this._ctlSourcingResults.addAddFormShown(Function.createDelegate(this,this.ctlSourcingResults_AddFormShown));Rebound.GlobalTrader.Site.Pages.Orders.CustomerRequirementDetail.callBaseMethod(this,"goInit")},dispose:function(){this.isDisposed||(this._ctlMainInfo&&this._ctlMainInfo.dispose(),this._ctlSourcingResults&&this._ctlSourcingResults.dispose(),this._ctlSourcing&&this._ctlSourcing.dispose(),this._ctlMainInfo=null,this._ctlSourcingResults=null,this._ctlSourcing=null,this._lblStatus=null,this._pnlStatus=null,Rebound.GlobalTrader.Site.Pages.Orders.CustomerRequirementDetail.callBaseMethod(this,"dispose"))},selectPart:function(){this._intCustomerRequirementID=this._ctlMainInfo._intCustomerRequirementID;this._ctlSourcingResults._blnRequirementClosed=this._ctlMainInfo._blnRequirementClosed;this._ctlSourcingResults._intCustomerRequirementID=this._intCustomerRequirementID;this._ctlSourcingResults._strPartNo=this._ctlMainInfo.getSelectedPartNo();this._ctlSourcingResults.getData();this._ctlSourcing&&(this._ctlSourcing._intCustomerRequirementID=this._intCustomerRequirementID);this._ctlSourcing&&(this._ctlSourcing._blnRequirementClosed=this._ctlMainInfo._blnRequirementClosed);this._ctlSourcing&&this._ctlSourcing.selectPart(this._ctlMainInfo.getSelectedPartNo())},ctlSourcing_SourcingResultAdded:function(){this._ctlSourcingResults.getData();this._ctlSourcing&&this._ctlSourcing.hideAddForm()},mainInfoStartGetData:function(){this._ctlSourcing&&this._ctlSourcing.show(!1);this._ctlSourcingResults.show(!1)},mainInfoGotDataOK:function(){this._ctlSourcing&&(this._ctlSourcing._frmRFQ._strPartNo=this._ctlMainInfo.getFieldValue("ctlPartNo"));this._ctlSourcing&&(this._ctlSourcing._frmRFQ._intROHSNo=this._ctlMainInfo.getFieldValue("hidROHS"));this._ctlSourcing&&(this._ctlSourcing._frmRFQ._intQuantity=this._ctlMainInfo.getFieldValue("ctlQuantity"));this._ctlSourcing&&(this._ctlSourcing._frmRFQ._strManufacturer=this._ctlMainInfo.getFieldValue("hidManufacturer"));this._ctlSourcing&&(this._ctlSourcing._frmRFQ._intManufacturerNo=this._ctlMainInfo.getFieldValue("hidManufacturerNo"));this._ctlSourcing&&(this._ctlSourcing._frmRFQ._strProduct=this._ctlMainInfo.getFieldValue("ctlProduct"));this._ctlSourcing&&(this._ctlSourcing._frmRFQ._intProductNo=this._ctlMainInfo.getFieldValue("hidProductID"));this._ctlSourcing&&(this._ctlSourcing._frmRFQ._strPackage=this._ctlMainInfo.getFieldValue("ctlPackage"));this._ctlSourcing&&(this._ctlSourcing._frmRFQ._intPackageNo=this._ctlMainInfo.getFieldValue("hidPackageID"));this._ctlSourcing&&(this._ctlSourcing._frmRFQ._strDateCode=this._ctlMainInfo.getFieldValue("ctlDateCode"));this._ctlSourcing&&(this._ctlSourcing._frmRFQ._dtmRequired=this._ctlMainInfo.getFieldValue("ctlDateRequired"));this._ctlSourcingResults._intCompanyID=this._ctlMainInfo.getFieldValue("hidCompanyID");this._ctlSourcing&&this._ctlSourcing.show(!0);this._ctlSourcingResults.show(!0);$R_FN.setInnerHTML(this._lblStatus,this._ctlMainInfo.getFieldValue("hidDisplayStatus"))},ctlSourcingResults_AddFormShown:function(){this._ctlSourcingResults._frmAdd.setFieldValue("ctlManufacturer",this._ctlMainInfo.getFieldValue("hidManufacturerNo"),null,this._ctlMainInfo.getFieldValue("hidManufacturer"));this._ctlSourcingResults._frmAdd.setFieldValue("ctlDateCode",this._ctlMainInfo.getFieldValue("ctlDateCode"));this._ctlSourcingResults._frmAdd.setFieldValue("ctlProduct",this._ctlMainInfo.getFieldValue("hidProductID"),null,this._ctlMainInfo.getFieldValue("ctlProduct"));this._ctlSourcingResults._frmAdd.setFieldValue("ctlPackage",this._ctlMainInfo.getFieldValue("hidPackageID"),null,this._ctlMainInfo._ctlPackage);this._ctlSourcingResults._frmAdd.setFieldValue("ctlQuantity",this._ctlMainInfo.getFieldValue("ctlQuantity"));this._ctlSourcingResults._frmAdd.setFieldValue("ctlCurrency",this._ctlMainInfo.getFieldValue("hidCurrencyID"));this._ctlSourcingResults._frmAdd.setFieldValue("ctlROHS",this._ctlMainInfo.getFieldValue("hidROHS"));this._ctlSourcingResults._frmAdd.setFieldValue("ctlMSL",this._ctlMainInfo.getFieldValue("hidMSL"))}};Rebound.GlobalTrader.Site.Pages.Orders.CustomerRequirementDetail.registerClass("Rebound.GlobalTrader.Site.Pages.Orders.CustomerRequirementDetail",Rebound.GlobalTrader.Site.Pages.Content,Sys.IDisposable);