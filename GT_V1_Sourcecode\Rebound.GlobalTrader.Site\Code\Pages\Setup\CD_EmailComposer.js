Type.registerNamespace("Rebound.GlobalTrader.Site.Pages.Setup");Rebound.GlobalTrader.Site.Pages.Setup.CD_EmailComposer=function(n){Rebound.GlobalTrader.Site.Pages.Setup.CD_EmailComposer.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Pages.Setup.CD_EmailComposer.prototype={get_ctlEmailComposer:function(){return this._ctlEmailComposer},set_ctlEmailComposer:function(n){this._ctlEmailComposer!==n&&(this._ctlEmailComposer=n)},initialize:function(){Rebound.GlobalTrader.Site.Pages.Setup.CD_EmailComposer.callBaseMethod(this,"initialize")},goInit:function(){Rebound.GlobalTrader.Site.Pages.Setup.CD_EmailComposer.callBaseMethod(this,"goInit")},dispose:function(){this.isDisposed||(this._ctlEmailComposer&&this._ctlEmailComposer.dispose(),this._ctlEmailComposer=null,Rebound.GlobalTrader.Site.Pages.Setup.CD_EmailComposer.callBaseMethod(this,"dispose"))},showNuggets:function(n){this._ctlEmailComposer.show(n)}};Rebound.GlobalTrader.Site.Pages.Setup.CD_EmailComposer.registerClass("Rebound.GlobalTrader.Site.Pages.Setup.CD_EmailComposer",Rebound.GlobalTrader.Site.Pages.Content,Sys.IDisposable);