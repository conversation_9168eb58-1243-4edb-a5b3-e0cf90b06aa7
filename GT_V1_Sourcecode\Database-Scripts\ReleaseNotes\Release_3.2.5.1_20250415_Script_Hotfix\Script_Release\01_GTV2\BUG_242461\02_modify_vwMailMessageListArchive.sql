﻿/*============================================================================================================================================================== 
TASK			      UPDATED BY       DATE				    ACTION		DESCRIPTION  
[US-242461]     Phu Dang		     15-Apr-2024		UPDATE		Bug 242461: Mail Message box issue when Sender ID (FromLoginNo) = 0
================================================================================================================================================================ 
*/

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

---------Soorya----- 
CREATE OR ALTER VIEW [dbo].[vwMailMessageListArchive]    
AS    
SELECT m.MailMessageId, m.MailMessageFolderNo, m.FromLoginNo, m.ToLoginNo, m.Subject, m.Body, m.DateSent, m.RecipientHasBeenNotified,     
  m.HasBeenRead, m.UpdatedBy, m.DLUP, ISNull(lf.EmployeeName, N'GT') AS FromLoginName, lt.EmployeeName AS ToLoginName, m.CompanyNo,     
  co.CompanyName--,ISNULL(m.DisableReplyButton,0) AS DisableReplyButton    
FROM dbo.tbMailMessageArchive AS m with (nolock) LEFT OUTER JOIN    
  dbo.tbCompany AS co with (nolock) ON m.CompanyNo = co.CompanyId LEFT OUTER JOIN    
  dbo.tbLogin AS lt with (nolock) ON m.ToLoginNo = lt.LoginId LEFT OUTER JOIN    
  dbo.tbLogin AS lf with (nolock) ON m.FromLoginNo = lf.LoginId   
  
GO
