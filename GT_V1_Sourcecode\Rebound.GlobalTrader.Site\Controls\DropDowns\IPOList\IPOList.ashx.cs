using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;

namespace Rebound.GlobalTrader.Site.Controls.DropDowns.Data {
	[WebService(Namespace = "http://tempuri.org/")]
	[WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    public class IPOList : Rebound.GlobalTrader.Site.Data.DropDowns.Base
    {

		protected override void GetData() {
            int? intDocNo = GetFormValue_NullableInt("intDocNo");
				JsonObject jsn = new JsonObject();
				JsonObject jsnList = new JsonObject(true);
                List<BLL.InternalPurchaseOrder> lst = BLL.InternalPurchaseOrder.IPODropDownList(intDocNo);
				for (int i = 0; i < lst.Count; i++) {
					JsonObject jsnItem = new JsonObject();
					jsnItem.AddVariable("ID", lst[i].InternalPurchaseOrderId);
                    jsnItem.AddVariable("Name", lst[i].InternalPurchaseOrderNumber);
					jsnItem.AddVariable("Code", "");
					jsnList.AddVariable(jsnItem);
					jsnItem.Dispose(); jsnItem = null;
				}
				lst.Clear(); lst = null;
				jsn.AddVariable("IPOList", jsnList);
				jsnList.Dispose(); jsnList = null;
				OutputResult(jsn);
				jsn.Dispose(); jsn = null;
			
		}
	}
}
