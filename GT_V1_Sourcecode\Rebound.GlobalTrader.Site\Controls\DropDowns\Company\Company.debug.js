///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//
// RP 06.10.2009:
// - Changes due to changes in base class
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");

Rebound.GlobalTrader.Site.Controls.DropDowns.Company = function(element) { 
	Rebound.GlobalTrader.Site.Controls.DropDowns.Company.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.DropDowns.Company.prototype = {
	
	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.DropDowns.Company.callBaseMethod(this, "initialize");
		this.addSetupDataCall(Function.createDelegate(this, this.setupDataCall));
		this.addGetDataOK(Function.createDelegate(this, this.dataCallOK));
	},

	dispose: function() { 
		if (this.isDisposed) return;
		Rebound.GlobalTrader.Site.Controls.DropDowns.Company.callBaseMethod(this, "dispose");
	},

	setupDataCall: function() { 
		this._objData.set_PathToData("controls/DropDowns/Company");
		this._objData.set_DataObject("Company");
		this._objData.set_DataAction("GetData");
	},

	dataCallOK: function() { 
		var result = this._objData._result;
		if (result.Companies) {
			for (var i = 0; i < result.Companies.length; i++) {
				this.addOption(result.Companies[i].Name, result.Companies[i].ID);
			}
		}
	}
	
};

Rebound.GlobalTrader.Site.Controls.DropDowns.Company.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.Company", Rebound.GlobalTrader.Site.Controls.DropDowns.Base);
