﻿
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

/*
==============================================================================================================================================================  
TASK			UPDATED BY       DATE				ACTION		DESCRIPTION  
[US-246240]     Phuc Hoang		 15-May-2025		UPDATE		Invoice - Line from 'Authorised SO Service Line' source function on Client/ DMCC side (Part 2)
==============================================================================================================================================================  
*/

---------Abhinav-------
CREATE OR ALTER VIEW [dbo].[vwInvoiceLine]                                  
--***********************************************************************************************                                                                  
--* RP 16.06.2009:                                  
--* - Updates to handle SRMAs correctly (SRMA Invoice Lines are at ILA level)                              
--* RP 17.04.2018:                                  
--* -TO get MSLLevel for Invoice LIne No 17-04-2018           
        
-- Marker   Owner   Date    Remarks        
-- [011]   Ravi   18-09-2023   RP-2339  AS6081 (Show AS6081 on detail screens)      
-- Altered  Abhinav Saxena  17-11-2023  For RP-2658.    
--***********************************************************************************************                                  
AS  SELECT     
            ivl.InvoiceNo                                  
          , iv.InvoiceNumber                                  
          , ivl.InvoiceLineId                                  
          , iv.InvoiceDate                                  
          , iv.ClientNo                                  
          , iv.DateOrdered                                  
          , iv.CustomerPO                                  
          , iv.SalesOrderNo                                  
          , iv.SalesOrderNumber                                  
          , ivl.SalesOrderLineNo                                  
          , NULL AS SupplierRMANo                                  
          , NULL AS SupplierRMANumber                                  
          , NULL AS SupplierRMALineNo                                  
          , NULL AS SupplierRMADate                                  
          , iv.Salesman                                  
          , lgSO.EmployeeName AS SalesmanName                                  
          , iv.DivisionNo                                  
          , lgSO.TeamNo                                  
          , iv.CompanyNo                                  
          , cm.CompanyName                                  
          , iv.ContactNo                                  
          , cn.ContactName                                  
          , isnull(ivl.Price, 0) AS Price                                  
          , ivl.FullPart                                  
          , ivl.Part                                  
          , ivl.ROHS                              
          , ivl.CustomerPart                                  
          , ivl.Quantity                                  
          , ivl.DateCode      
          , ivl.DatePromised                                  
          , iv.CurrencyNo                                  
, cu.CurrencyCode                       
          , cu.CurrencyDescription                                  
          , ivl.ProductNo                             
          , pr.ProductName                                  
          , pr.ProductDescription                                  
          , pr.DutyCode AS ProductDutyCode           
          , ivl.PackageNo                                  
          , pk.PackageName                                  
          , pk.PackageDescription                   
          , ivl.ManufacturerNo                                  
          , mf.ManufacturerName                                  
          , mf.ManufacturerCode                       
          , iv.InvoicePaid                                  
          , isnull((SELECT  sum(Quantity)                                  
                    FROM    dbo.tbInvoiceLineAllocation ila                                  
                    WHERE   ila.InvoiceLineNo = ivl.InvoiceLineId                                  
                   ), 0) AS QuantityShipped                           
          , case isnull((SELECT sum(Quantity)                                  
                         FROM   tbInvoiceLineAllocation                                  
            WHERE  InvoiceLineNo = ivl.InvoiceLineId                                  
                        ), 0)                                  
              WHEN 0 THEN 0                                  
              ELSE isnull((SELECT  sum(LandedCost * Quantity)                                  
                           FROM     dbo.tbInvoiceLineAllocation                                  
                           WHERE    InvoiceLineNo = ivl.InvoiceLineId                                  
                          ) / (SELECT   sum(Quantity)                                  
                               FROM    tbInvoiceLineAllocation                                  
                               WHERE    InvoiceLineNo = ivl.InvoiceLineId                                  
                              ), 0)                                  
            END AS LandedCost                                  
          , 'SalesOrderLine' AS LineSource                                  
   , isnull(ivl.Quantity, 0) AS QuantityOrdered                                  
          , ivl.Taxable                                 
          , ivl.ShippedBy                                  
          , lgShipped.EmployeeName AS ShippedByName                                  
          , ivl.ShippedDate                                  
          , isnull(ivl.Notes, '') AS LineNotes                                  
          , ivl.Inactive                                  
          , ivl.ServiceNo                                
          , ivl.BatchReference                              
    -- Code Start                            
    , ivl.MSLLevel  --- TO get MSLLevel for Invoice LIne No 17-04-2018                                 
    -- Code End                            
    , ivl.PrintHazardous                            
    ,  isnull(pr.IsHazardous,0) as IsProdHazardous                           
  ,col.GlobalCountryName as   CountryOfOrigin                          
 ,ivl.LifeCycleStage                          
 ,ivl.HTSCode                          
 ,ivl.AveragePrice                          
 ,ivl.Packing                          
 ,ivl.PackagingSize                           
 ,col.GlobalCountryName as IHSCountryOfOrigin                        
   ,ivl.Descriptions                  
   ,ivl.IHSProduct                  
   ,ivl.ECCNCode              
   ,isnull(pr.IsOrderViaIPOonly,0) as IsOrderViaIPOonly            
   , ISNULL(ivl.AS6081,0) AS AS6081 --[011]
   , ivl.RequiredDate
    FROM    dbo.tbInvoice iv                                  
    JOIN    dbo.tbInvoiceLine ivl ON ivl.InvoiceNo = iv.InvoiceId                                  
    LEFT JOIN dbo.tbLogin lgSO ON lgSO.LoginId = iv.Salesman                            
    LEFT JOIN dbo.tbCompany cm ON cm.CompanyId = iv.CompanyNo                                  
    LEFT JOIN dbo.tbContact cn ON cn.ContactId = iv.ContactNo                                  
    LEFT JOIN dbo.tbLogin lgShipped ON lgShipped.LoginId = ivl.Shippedby                                  
    LEFT JOIN dbo.tbCurrency cu ON cu.CurrencyId = iv.CurrencyNo                                  
    LEFT JOIN dbo.tbPackage pk ON pk.PackageId = ivl.PackageNo         
    LEFT JOIN dbo.tbProduct pr ON pr.ProductId = ivl.ProductNo                                  
    LEFT JOIN dbo.tbManufacturer mf ON mf.ManufacturerId = ivl.ManufacturerNo                            
 LEFT JOIN dbo.tbGlobalCountryList col ON ivl.CountryOfOriginNo = col.GlobalCountryId                                 
 --LEFT JOIN dbo.tbSalesOrderLine lgSales ON lgSales.SalesOrderLineId = ivl.SalesOrderLineNo   -- TO get MSLLevel for Invoice LIne No 17-04-2018                            
    WHERE   NOT iv.SalesOrderNo IS NULL                                  
            AND iv.SalesOrderNo > 0          
   --AND ISNULL(ivl.ISTokenLine,0)=0                              
    UNION                                  
    SELECT     
         ivl.InvoiceNo                                  
         , iv.InvoiceNumber                                  
          , ivl.InvoiceLineId                                  
          , iv.InvoiceDate                                  
          , iv.ClientNo                
          , NULL AS DateOrdered                                  
          , NULL AS CustomerPO                                  
          , NULL AS SalesOrderNo                                  
          , NULL AS SalesOrderNumber                                  
          , NULL AS SalesOrderLineNo                                  
          , iv.SupplierRMANo                                  
          , srma.SupplierRMANumber                                  
          , ila.SupplierRMALineNo                                  
          , srma.SupplierRMADate                                  
          , srma.AuthorisedBy AS Salesman                                  
          , srma.AuthoriserName AS SalesmanName                                  
          , srma.DivisionNo                                  
          , srma.TeamNo                                  
          , srma.CompanyNo                                  
          , srma.CompanyName                                  
          , srma.ContactNo                                  
          , srma.ContactName                                  
          , isnull(sln.Price, 0) AS Price                                  
          , sln.FullPart                                  
          , sln.Part                     
          , sln.ROHS                                  
          , ivl.CustomerPart                                  
          , sln.Quantity                                  
          , sln.DateCode                                  
          , NULL AS DatePromised                                  
          , sln.CurrencyNo                                  
          , sln.CurrencyCode                                  
          , sln.CurrencyDescription                                 
          , sln.ProductNo                                  
          , sln.ProductName                                  
          , sln.ProductDescription                                  
          , pr.DutyCode AS ProductDutyCode                                  
          , sln.PackageNo                                  
          , sln.PackageName                                  
          , sln.PackageDescription                                  
          , sln.ManufacturerNo                                  
          , sln.ManufacturerName                                  
          , sln.ManufacturerCode                                  
          , iv.InvoicePaid                                  
          , ila.Quantity AS QuantityShipped                                  
          , ila.LandedCost             
          , 'SupplierRMALine' AS LineSource                                 
          , isnull(sln.Quantity, 0) AS QuantityOrdered                                  
          , cast(sln.Taxable AS nvarchar(1)) AS Taxable                                  
, ivl.ShippedBy                                  
          , lgShipped.EmployeeName AS ShippedByName                   
          , ivl.ShippedDate                                  
          , isnull(ivl.Notes, '') AS LineNotes                                  
          , ivl.Inactive                                  
          , ivl.ServiceNo                            
          , ivl.BatchReference                              
     -- Code Start                            
   , ivl.MSLLevel --- TO get MSLLevel for Invoice LIne No 17-04-2018                                
    -- Code End                            
    , ivl.PrintHazardous                            
    ,  isnull(pr.IsHazardous,0) as IsProdHazardous                            
  ,col.GlobalCountryName as   CountryOfOrigin                          
 ,ivl.LifeCycleStage                          
 ,ivl.HTSCode                          
 ,ivl.AveragePrice                          
 ,ivl.Packing                          
 ,ivl.PackagingSize                   ,col.GlobalCountryName as   IHSCountryOfOrigin                         
 ,ivl.Descriptions                   
 ,ivl.IHSProduct                  
 ,ivl.ECCNCode              
 ,isnull(pr.IsOrderViaIPOonly,0) as IsOrderViaIPOonly            
 , ISNULL(ivl.AS6081,0) as AS6081 --[011]   
 , ivl.RequiredDate
    FROM    dbo.tbInvoice iv                                  
    JOIN    dbo.tbInvoiceLine ivl ON ivl.InvoiceNo = iv.InvoiceId                                  
    JOIN    dbo.tbInvoiceLineAllocation ila ON ivl.InvoiceLineId = ila.InvoiceLineNo                                  
    JOIN    dbo.vwSupplierRMALine sln ON ila.SupplierRMALineNo = sln.SupplierRMALineId                                  
    JOIN    dbo.vwSupplierRMA srma ON iv.SupplierRMANo = srma.SupplierRMAId                                  
    LEFT JOIN dbo.tbProduct pr ON pr.ProductId = sln.ProductNo                                  
    LEFT JOIN dbo.tbLogin lgShipped ON lgShipped.LoginId = ivl.Shippedby                             
 LEFT JOIN dbo.tbGlobalCountryList col ON ivl.CountryOfOriginNo = col.GlobalCountryId                           
   -- LEFT JOIN dbo.tbSalesOrderLine lgSales ON lgSales.SalesOrderLineId = ivl.SalesOrderLineNo   -- TO get MSLLevel for Invoice LIne No 17-04-2018                                  
    WHERE   NOT iv.SupplierRMANo IS NULL                                  
            AND iv.SupplierRMANo > 0        
   --AND ISNULL(ivl.ISTokenLine,0)=0            
            
UNION            
SELECT          
            ivl.InvoiceNo                                  
          , iv.InvoiceNumber                                  
          , ivl.InvoiceLineId                                  
          , iv.InvoiceDate                                  
          , iv.ClientNo                                  
          , iv.DateOrdered                                  
          , iv.CustomerPO                                  
          , iv.SalesOrderNo                                  
          , iv.SalesOrderNumber                                  
          , ivl.SalesOrderLineNo                                  
          , NULL AS SupplierRMANo                                  
          , NULL AS SupplierRMANumber                                  
          , NULL AS SupplierRMALineNo                                  
          , NULL AS SupplierRMADate                                  
          , iv.Salesman                                  
          , lgSO.EmployeeName AS SalesmanName                                  
          , iv.DivisionNo                                  
          , lgSO.TeamNo                                  
          , iv.CompanyNo                                  
          , cm.CompanyName                                  
          , iv.ContactNo                                  
          , cn.ContactName                                  
          , isnull(ivl.Price, 0) AS Price                                  
          , ivl.FullPart                                  
          , ivl.Part                                  
          , ivl.ROHS                              
          , ivl.CustomerPart                                  
          , ivl.Quantity                                  
          , ivl.DateCode                                  
          , ivl.DatePromised                                  
          , iv.CurrencyNo                 
          , cu.CurrencyCode                                  
          , cu.CurrencyDescription                                  
          , ivl.ProductNo                                  
          , pr.ProductName                                  
          , pr.ProductDescription                                  
          , pr.DutyCode AS ProductDutyCode                                  
          , ivl.PackageNo                                  
          , pk.PackageName                          
       , pk.PackageDescription                                  
          , ivl.ManufacturerNo                                  
          , mf.ManufacturerName                                  
          , mf.ManufacturerCode                       
          , iv.InvoicePaid                                  
          , isnull((SELECT  sum(Quantity)                                  
                    FROM    dbo.tbInvoiceLineAllocation ila                                  
                    WHERE   ila.InvoiceLineNo = ivl.InvoiceLineId                                  
                   ), 0) AS QuantityShipped                           
          , case isnull((SELECT sum(Quantity)                                  
                         FROM   tbInvoiceLineAllocation                                  
                         WHERE  InvoiceLineNo = ivl.InvoiceLineId                                  
                        ), 0)                                  
              WHEN 0 THEN 0                                  
              ELSE isnull((SELECT  sum(LandedCost * Quantity)                                  
                           FROM     dbo.tbInvoiceLineAllocation                                  
                           WHERE    InvoiceLineNo = ivl.InvoiceLineId                                  
                          ) / (SELECT   sum(Quantity)                                  
                               FROM    tbInvoiceLineAllocation                                  
                               WHERE    InvoiceLineNo = ivl.InvoiceLineId                                  
                              ), 0)                                  
            END AS LandedCost                                  
          , 'SalesOrderLine' AS LineSource                                  
   , isnull(ivl.Quantity, 0) AS QuantityOrdered                                  
          , ivl.Taxable                                 
          , ivl.ShippedBy                                  
          , lgShipped.EmployeeName AS ShippedByName                                  
          , ivl.ShippedDate                                  
          , isnull(ivl.Notes, '') AS LineNotes                                  
          , ivl.Inactive                                  
          , ivl.ServiceNo                                
          , ivl.BatchReference                              
    -- Code Start                            
    , ivl.MSLLevel  --- TO get MSLLevel for Invoice LIne No 17-04-2018                                 
    -- Code End                            
    , ivl.PrintHazardous                            
    ,  isnull(pr.IsHazardous,0) as IsProdHazardous                           
  ,col.GlobalCountryName as   CountryOfOrigin                          
 ,ivl.LifeCycleStage                          
 ,ivl.HTSCode                          
 ,ivl.AveragePrice                          
 ,ivl.Packing                          
 ,ivl.PackagingSize                           
 ,col.GlobalCountryName as IHSCountryOfOrigin                        
   ,ivl.Descriptions                  
   ,ivl.IHSProduct                  
   ,ivl.ECCNCode              
   ,isnull(pr.IsOrderViaIPOonly,0) as IsOrderViaIPOonly               
   , ISNULL(ivl.AS6081,0) as AS6081 --[011]  
   , ivl.RequiredDate
    FROM    dbo.tbInvoice iv                                  
    JOIN    dbo.tbInvoiceLine ivl ON ivl.InvoiceNo = iv.InvoiceId                                  
    LEFT JOIN dbo.tbLogin lgSO ON lgSO.LoginId = iv.Salesman                                  
    LEFT JOIN dbo.tbCompany cm ON cm.CompanyId = iv.CompanyNo                                  
    LEFT JOIN dbo.tbContact cn ON cn.ContactId = iv.ContactNo                                  
    LEFT JOIN dbo.tbLogin lgShipped ON lgShipped.LoginId = ivl.Shippedby                                  
    LEFT JOIN dbo.tbCurrency cu ON cu.CurrencyId = iv.CurrencyNo                                  
    LEFT JOIN dbo.tbPackage pk ON pk.PackageId = ivl.PackageNo                                  
    LEFT JOIN dbo.tbProduct pr ON pr.ProductId = ivl.ProductNo                                  
    LEFT JOIN dbo.tbManufacturer mf ON mf.ManufacturerId = ivl.ManufacturerNo                            
 LEFT JOIN dbo.tbGlobalCountryList col ON ivl.CountryOfOriginNo = col.GlobalCountryId                                 
 --LEFT JOIN dbo.tbSalesOrderLine lgSales ON lgSales.SalesOrderLineId = ivl.SalesOrderLineNo   -- TO get MSLLevel for Invoice LIne No 17-04-2018                            
    WHERE   NOT iv.SalesOrderNo IS NULL                                  
            AND iv.SalesOrderNo > 0          
   --AND ISNULL(ivl.ISTokenLine,0)=1       
   AND ISNULL(ivl.Price,0)>0                             
    UNION                                   
    SELECT      
         ivl.InvoiceNo                                  
          , iv.InvoiceNumber                                  
          , ivl.InvoiceLineId                                  
          , iv.InvoiceDate                                  
          , iv.ClientNo                
          , NULL AS DateOrdered                                  
          , NULL AS CustomerPO                             
          , NULL AS SalesOrderNo                                  
          , NULL AS SalesOrderNumber                                  
          , NULL AS SalesOrderLineNo                                  
          , iv.SupplierRMANo                                  
          , srma.SupplierRMANumber                                  
          , ila.SupplierRMALineNo                                  
          , srma.SupplierRMADate                                  
          , srma.AuthorisedBy AS Salesman                                  
          , srma.AuthoriserName AS SalesmanName                                  
          , srma.DivisionNo                                  
          , srma.TeamNo                                  
          , srma.CompanyNo                                  
          , srma.CompanyName                                  
          , srma.ContactNo                                  
          , srma.ContactName                                  
          , isnull(sln.Price, 0) AS Price                                  
          , sln.FullPart                                  
          , sln.Part                     
          , sln.ROHS                                  
          , ivl.CustomerPart                                  
          , sln.Quantity                                  
          , sln.DateCode                                  
          , NULL AS DatePromised                                  
          , sln.CurrencyNo                                  
          , sln.CurrencyCode                                  
          , sln.CurrencyDescription                                 
          , sln.ProductNo                                  
          , sln.ProductName                                  
          , sln.ProductDescription                                  
          , pr.DutyCode AS ProductDutyCode                                  
          , sln.PackageNo                                  
          , sln.PackageName                                  
          , sln.PackageDescription                                  
          , sln.ManufacturerNo                                  
          , sln.ManufacturerName                                  
          , sln.ManufacturerCode                                  
          , iv.InvoicePaid                                  
          , ila.Quantity AS QuantityShipped                                  
          , ila.LandedCost                                  
          , 'SupplierRMALine' AS LineSource                                  
          , isnull(sln.Quantity, 0) AS QuantityOrdered                                  
          , cast(sln.Taxable AS nvarchar(1)) AS Taxable                            
, ivl.ShippedBy                                  
          , lgShipped.EmployeeName AS ShippedByName                                  
          , ivl.ShippedDate                                  
          , isnull(ivl.Notes, '') AS LineNotes                                  
          , ivl.Inactive                                  
          , ivl.ServiceNo                            
          , ivl.BatchReference                              
     -- Code Start                            
   , ivl.MSLLevel  --- TO get MSLLevel for Invoice LIne No 17-04-2018                                
    -- Code End                            
    , ivl.PrintHazardous                            
    ,  isnull(pr.IsHazardous,0) as IsProdHazardous                            
  ,col.GlobalCountryName as   CountryOfOrigin                          
 ,ivl.LifeCycleStage                          
 ,ivl.HTSCode                          
 ,ivl.AveragePrice                          
 ,ivl.Packing                          
 ,ivl.PackagingSize                          
 ,col.GlobalCountryName as   IHSCountryOfOrigin                         
 ,ivl.Descriptions                   
 ,ivl.IHSProduct                  
 ,ivl.ECCNCode              
 ,isnull(pr.IsOrderViaIPOonly,0) as IsOrderViaIPOonly            
 , ISNULL(ivl.AS6081,0) as AS6081 --[011]     
 , ivl.RequiredDate
    FROM    dbo.tbInvoice iv                                  
    JOIN    dbo.tbInvoiceLine ivl ON ivl.InvoiceNo = iv.InvoiceId                                  
    JOIN    dbo.tbInvoiceLineAllocation ila ON ivl.InvoiceLineId = ila.InvoiceLineNo                                  
    JOIN    dbo.vwSupplierRMALine sln ON ila.SupplierRMALineNo = sln.SupplierRMALineId                                  
    JOIN    dbo.vwSupplierRMA srma ON iv.SupplierRMANo = srma.SupplierRMAId                          
    LEFT JOIN dbo.tbProduct pr ON pr.ProductId = sln.ProductNo                                  
    LEFT JOIN dbo.tbLogin lgShipped ON lgShipped.LoginId = ivl.Shippedby                             
 LEFT JOIN dbo.tbGlobalCountryList col ON ivl.CountryOfOriginNo = col.GlobalCountryId                           
   -- LEFT JOIN dbo.tbSalesOrderLine lgSales ON lgSales.SalesOrderLineId = ivl.SalesOrderLineNo   -- TO get MSLLevel for Invoice LIne No 17-04-2018                                  
    WHERE   NOT iv.SupplierRMANo IS NULL                                  
            AND iv.SupplierRMANo > 0        
   --AND ISNULL(ivl.ISTokenLine,0)=1       
   AND ISNULL(ivl.Price,0)>0 
GO


