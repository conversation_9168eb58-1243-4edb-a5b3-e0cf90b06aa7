﻿SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
/*
===========================================================================================
TASK      			UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-213152]			Trung Pham			15-Oct-2024		CREATE          Add Prospective Offer to Clone HUBRFQ
[US-221304]			Trung Pham Van		21-Nov-2024		UPDATE			Edit new price
===========================================================================================
*/
CREATE OR ALTER PROCEDURE [dbo].[usp_insert_ProspectiveOffer_SourcingResult]
	@CustomerRequirementNo INT
	,@LineId INT
	,@PartNo VARCHAR(MAX)
	,@MfrNo INT
	,@DateCode VARCHAR(MAX)
	,@ProductNo INT
	,@PackageNo INT
	,@QuantityOffer INT
	,@Salesman INT
	,@SupplierNo INT
	,@POCurrencyNo INT
	,@POHubCompanyNo INT = NULL
	,@IsClone BIT = 1
	,@OfferPriceFromProspective FLOAT = NULL
	,@SourcingResultId int OUTPUT

AS
BEGIN
	IF (@CustomerRequirementNo <= 0)
	BEGIN
		RETURN
	END
	
    DECLARE @ClientCompanyNo INT, @CompanyNo INT, @ClientNo INT, @ClientCurrencyNo INT, @HubCurrencyNo INT, @LinkMultiCurrencyNo INT,
	@ManufacturerNo INT, @OfferCurrencyNo INT, @BuyExchangeRate FLOAT, @HubExchangeRate FLOAT,
	@GlobalProductNo INT, @ClientProductNo INT, @HubCurrencyName VARCHAR(20), @ClientCode VARCHAR(20)    
  
                          
    SELECT @ClientNo = ClientNo
    FROM tbCustomerRequirement where CustomerRequirementId = @CustomerRequirementNo                 
    SELECT TOP 1 @ClientCompanyNo = CompanyId FROM tbCompany WHERE ClientNo = @ClientNo AND IsPOHub =1     

	SELECT @OfferCurrencyNo = o.CurrencyNo , @GlobalProductNo = isnull(p.GlobalProductNo,0) 
	FROM tbProspectiveOfferLines o 
	left join tbProduct p on o.ProductNo = p.ProductId      
    WHERE o.ProspectiveOfferLineId =  @LineId    
  
    SELECT @LinkMultiCurrencyNo = l.LinkMultiCurrencyId, @ClientCurrencyNo = l.SupplierCurrencyNo    
     from tbCurrency c left join tbLinkMultiCurrency l   on l.GlobalCurrencyNo = c.GlobalCurrencyNo     
        WHERE  l.ClientNo = @ClientNo and c.ClientNo = 114 AND c.CurrencyId = @OfferCurrencyNo    
                                
    SELECT  @HubCurrencyNo = dbo.ufn_get_HUB_DefaultCurrencyNo(@OfferCurrencyNo,@ClientNo,114)    
    --Get the buy exchange rate .66    
	SELECT @BuyExchangeRate = dbo.ufn_get_exchange_rate(ISNULL(@OfferCurrencyNo, 0), GETDATE())
	--Get Hub Exchange rate
	SELECT @HubExchangeRate = dbo.ufn_get_exchange_rate(ISNULL(@HubCurrencyNo, 0), GETDATE())     
               
    select top 1 @ClientProductNo = ProductId from tbProduct where GlobalProductNo = @GlobalProductNo and ClientNo = @ClientNo and isnull(Inactive,0) = 0

	INSERT INTO dbo.tbSourcingResult (
		CustomerRequirementNo, 
		Part, 
		FullPart,
		ManufacturerNo,
		DateCode, 
		ProductNo,
		PackageNo,
		Quantity,
		Price, 
		CurrencyNo,
		OriginalEntryDate,
		Salesman,
		SupplierNo, 
		DLUP, 
		ROHS, 
		POHubCompanyNo, 
		ActualPrice,
		ActualCurrencyNo,
		LinkMultiCurrencyNo,
		MSLLevelNo
	)

     SELECT 
		@CustomerRequirementNo,
		oph.Part,
		oph.Part,
		@MfrNo,
		@DateCode,
		ISNULL(@ClientProductNo ,@ProductNo),
		@PackageNo,
		CASE WHEN @IsClone=1 THEN ISNULL(@QuantityOffer, 0) ELSE 0 END,
		((ISNULL(ISNULL(@OfferPriceFromProspective, oph.Price), 0) / @BuyExchangeRate) + ((ISNULL(ISNULL(@OfferPriceFromProspective, oph.Price), 0) / @BuyExchangeRate) * ISNULL(company.UPLiftPrice,0))/100) * @HubExchangeRate,
		dbo.ufn_get_HUB_DefaultCurrencyNo(ISNULL(oph.CurrencyNo, @POCurrencyNo),@ClientNo,114), 
		GETDATE(),
		@Salesman,
		@SupplierNo,
		GETDATE(), 
		1, 
		ISNULL(@POHubCompanyNo, 0),
		ISNULL(ISNULL(@OfferPriceFromProspective, oph.Price), 0), 
		ISNULL(oph.CurrencyNo, 
		ISNULL(company.POCurrencyNo,0)),
		@LinkMultiCurrencyNo, 
		1
     FROM   tbProspectiveOfferLines oph
     LEFT JOIN dbo.tbCompany company on company.CompanyId = @SupplierNo
     LEFT JOIN dbo.tbCountry country on country.CountryId = company.DefaultPOShipCountryNo AND company.ClientNo=country.ClientNo                              
     LEFT JOIN dbo.tbManufacturer mfr ON mfr.ManufacturerId=oph.ManufacturerNo        
     LEFT JOIN dbo.tbProduct pr ON pr.ProductId=oph.ProductNo        
     LEFT JOIN dbo.tbPackage pk ON pk.PackageId = @PackageNo        
     WHERE  oph.ProspectiveOfferLineId =  @LineId

	SET @SourcingResultId = scope_identity() ;
END
