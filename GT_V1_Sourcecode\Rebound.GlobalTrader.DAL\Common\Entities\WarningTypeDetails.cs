﻿//Marker     Changed by         Date         Remarks
//[001]      A<PERSON><PERSON><PERSON>     25/10/2021    added new class for the Warning Type.
using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
namespace Rebound.GlobalTrader.DAL
{
    public class WarningTypeDetails
    {
        #region Constructors

        public WarningTypeDetails() { }

        #endregion

        #region Properties

        /// <summary>
        /// ROHSStatusId (from Table)
        /// </summary>
        public System.Int32 WarningId { get; set; }
        /// <summary>
        /// Name (from Table)
        /// </summary>
        public System.String WarningName { get; set; }

        /// <summary>
        /// LableTypeId (from Table)
        /// </summary>
        public System.Int32 LableTypeId { get; set; }
        /// <summary>
        /// LableTypeName (from Table)
        /// </summary>
        public System.String LableTypeName { get; set; }

        #endregion
    }
}
