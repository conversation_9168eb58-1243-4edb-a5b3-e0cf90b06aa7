
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE PROCEDURE [dbo].[usp_KubInsertAssistanceDetailsInCache_Step1]                      
@PartNumber  NVARCHAR(MAX)=NULL,                      
@ClientID  INT=NULL                      
AS                      
/*        
 *Action: Created  By:<PERSON><PERSON><PERSON><PERSON>  Date:31-07-2023  Comment:<PERSON><PERSON> is cached the top 4 info of the Add new Req KUB.        
 */                     
BEGIN                      
SET NOCOUNT ON                      
                              
DECLARE                    
@PartNo nvarchar(MAX)=@PartNumber,                    
@TotalPartsTreaded nvarchar(MAX)=null,                    
@TotalGP nvarchar(MAX)=null,                    
@AverageBestPrice nvarchar(MAX)=null,  
@IsClientPrice BIT=0                  
                  
IF((SELECT COUNT(1) FROM tbKubAssistanceDetailsCache WHERE PartNo=@PartNumber AND ClientID=@ClientID        
AND DLUP>DATEAdd(DAY,-3,GETDATE()))=0)        
BEGIN         
DELETE FROM tbKubAssistanceDetailsCache WHERE PartNo=@PartNumber AND ClientID=@ClientID                   
---Total number of parts from SO in the last 12 months (Rolling)---                      
SELECT @TotalPartsTreaded=                   
SUM(sol.Quantity)                      
FROM tbSalesOrderLine sol                      
LEFT OUTER JOIN tbSalesOrder so ON sol.SalesOrderNo=so.SalesOrderId                      
WHERE so.ClientNo=@ClientID                   
AND sol.FullPart=@PartNumber                   
AND (sol.DLUP)>CAST(DATEADD(MONTH,-12,GETDATE()) AS DATE)                   
 ---END---                      
                  
                  
                  
                      
---Total GP based on the last 12 months(Rolling)------                      
    CREATE TABLE #Invoices (                      
          InvoiceID int                      
        , CurrencyRate float                      
        , ShippingCost float                      
        , Freight float                      
        )                      
    CREATE TABLE #InvoicePreSummary (                      
          InvoiceID int                      
        , ShippingCost float                      
        , Freight float                      
        , Cost float                      
        , Resale float                   
        , ClientNo INT                     
        )                      
    CREATE TABLE #InvoiceSummary (                      
          Cost float                      
        , Resale float                     
        , ClientNo INT                   
        )                      
    INSERT  INTO #Invoices                      
            SELECT  InvoiceID                      
                  , dbo.ufn_get_exchange_rate(a.CurrencyNo, a.InvoiceDate)                      
                  , isnull(a.ShippingCost,0)                      
                  , isnull(a.Freight,0)                      
            FROM    dbo.tbInvoice a                      
            WHERE   a.ClientNo = @ClientID                  
            AND CAST(a.InvoiceDate AS DATE)>CAST(DATEADD(MONTH,-12,GETDATE()) AS DATE)          
    INSERT  INTO #InvoicePreSummary                      
            SELECT                   
                   i.InvoiceID                      
                  , i.ShippingCost                      
                  , i.Freight / i.CurrencyRate                      
                  , isnull(sum(CONVERT(DECIMAL(16,5),ila.LandedCost)* ila.Quantity), 0)                      
                  , isnull(sum((ila.Price * ila.Quantity) / i.CurrencyRate), 0)                  
                  , ila.ClientNo                      
            FROM    #Invoices i                      
            LEFT JOIN dbo.tbInvoiceLine il ON il.InvoiceNo = i.InvoiceId                      
            LEFT JOIN dbo.vwInvoiceLineAllocation ila ON ila.InvoiceLineNo = il.InvoiceLineId                      
   Where ila.ClientNo=@ClientID AND il.FullPart=@PartNumber                  
            GROUP BY                   
                    i.InvoiceID                      
                  , i.ShippingCost                      
                  , i.Freight / i.CurrencyRate                     
      , ila.ClientNo        
             
    INSERT  INTO #InvoiceSummary                      
            SELECT                    
                   sum(Cost) + sum(ShippingCost)                      
          , sum(Resale) + sum(Freight)                    
                  , ClientNo                    
            FROM    #InvoicePreSummary                    
   GROUP BY ClientNo                  
                     
       SELECT @TotalGP=                  
               
    CAST(CONVERT(DECIMAL(16,2),ISNULL((i.Resale - i.Cost),0)) AS NVARCHAR(100))+' '+                  
    cr.CurrencyCode                  
    FROM    #InvoiceSummary i                    
    LEFT JOIN tbClient cl ON i.ClientNo=cl.ClientId                  
    LEFT JOIN tbCurrency cr ON cl.CurrencyNo=cr.CurrencyId                    
   GROUP BY  i.Resale,i.Cost,cr.CurrencyCode                 
                  
    DROP TABLE #Invoices                      
    DROP TABLE #InvoiceSummary                      
    DROP TABLE #InvoicePreSummary                       
------------END---------------------                      
                      
---Best buy prices in the last 12months (Rolling)--------------                      
CREATE TABLE #tb_tempAverageBestPrice  
(  
BuyId INT,  
BuyNumber INT,  
BuyLineId INT,  
AverageBestPrice FLOAT,  
IsClientPrice BIT,  
CurrencyCode    NVARCHAR(50),  
ActualAveragePrice FLOAT,  
ActualAverageCurrency NVARCHAR(100)  
)  
INSERT INTO #tb_tempAverageBestPrice(  
BuyId,  
BuyNumber,  
BuyLineId,  
AverageBestPrice,  
IsClientPrice,  
CurrencyCode  
)  
SELECT  
po.purchaseOrderId,  
po.purchaseOrderNumber,  
pol.purchaseorderLineId,                   
CASE WHEN MIN(ISNULL(pol.Price,0))>0 THEN                   
CAST(ISNULL(                  
dbo.ufn_convert_currency_value(MIN(ISNULL(CONVERT(DECIMAL(16,5),pol.Price),0)),po.CurrencyNo,cr.CurrencyId,GETDATE()),0) AS NVARCHAR(100))                  
  ELSE '0' END   
,0  
,cr.CurrencyCode                 
FROM tbPurchaseOrderLine pol                      
LEFT OUTER JOIN tbPurchaseOrder po ON pol.PurchaseOrderNo=po.PurchaseOrderId                    
LEFT OUTER JOIN tbClient cl ON po.ClientNo=cl.ClientId                  
LEFT JOIN tbCurrency cr on cl.CurrencyNo= cr.CurrencyId                  
WHERE po.ClientNo=@ClientID AND pol.FullPart=@PartNumber                      
AND (pol.DeliveryDate)>CAST(DATEADD(MONTH,-12,GETDATE()) AS DATE) AND pol.Price>0                       
GROUP BY po.CurrencyNo , cr.CurrencyCode,cr.CurrencyId,po.purchaseOrderId,  
po.purchaseOrderNumber,pol.purchaseorderLineId   
  
UNION  
SELECT  
ipo.InternalPurchaseOrderId,  
ipo.InternalPurchaseOrderNumber,  
ipol.InternalPurchaseOrderLineId,                  
CASE WHEN MIN(ISNULL(pol.Price,0))>0 THEN                   
CAST(ISNULL(                  
dbo.ufn_convert_currency_value(MIN(ISNULL(CONVERT(DECIMAL(16,5),ipol.Price),0)),ipo.CurrencyNo,cr.CurrencyId,GETDATE()),0) AS NVARCHAR(100))                  
 ELSE '0' END,  
 1,  
cr.CurrencyCode                  
FROM tbInternalPurchaseOrderLine ipol                      
LEFT OUTER JOIN tbInternalPurchaseOrder ipo ON ipol.InternalPurchaseOrderNo=ipo.InternalPurchaseOrderId    
LEFT OUTER JOIN tbPurchaseOrderLine pol ON ipol.PurchaseOrderLineNo=pol.PurchaseOrderLineId                  
LEFT OUTER JOIN tbClient cl ON ipo.ClientNo=cl.ClientId                  
LEFT JOIN tbCurrency cr on cl.CurrencyNo= cr.CurrencyId                  
WHERE ipo.ClientNo=@ClientID AND pol.FullPart=@PartNumber                      
AND (pol.DeliveryDate)>CAST(DATEADD(MONTH,-12,GETDATE()) AS DATE) AND pol.Price>0                       
GROUP BY ipo.CurrencyNo , cr.CurrencyCode,cr.CurrencyId,ipo.InternalPurchaseOrderNumber,  
ipo.InternalPurchaseOrderId,ipol.InternalPurchaseOrderLineId   
         
UPDATE  t1   
SET t1.ActualAveragePrice=t2.Price,  
t1.ActualAverageCurrency=t4.CurrencyCode  
FROM #tb_tempAverageBestPrice t1 LEFT JOIN tbPurchaseOrderLine t2 ON t1.BuyLineId=t2.PurchaseOrderLineId  
LEFT OUTER JOIN tbPurchaseOrder t3 ON t2.PurchaseOrderNo=t3.PurchaseOrderId  
LEFT JOIN tbCurrency t4 on t4.CurrencyId=t3.CurrencyNo  
WHERE ISNULL(t1.IsClientPrice,0)=0    
  
UPDATE  t1   
SET t1.ActualAveragePrice=t2.Price,  
t1.ActualAverageCurrency=t4.CurrencyCode  
FROM #tb_tempAverageBestPrice t1 LEFT JOIN tbInternalPurchaseOrderLine t2 ON t1.BuyLineId=t2.InternalPurchaseOrderLineId  
LEFT OUTER JOIN tbInternalPurchaseOrder t3 ON t2.InternalPurchaseOrderNo=t3.InternalPurchaseOrderId  
LEFT JOIN tbCurrency t4 on t4.CurrencyId=t3.CurrencyNo  
WHERE ISNULL(t1.IsClientPrice,0)=1      
  
SELECT TOP 1 @AverageBestPrice= CAST(MIN(AverageBestPrice) AS NVARCHAR(100))+' '+CurrencyCode+  
+'<span class="actualCurrency">['+CAST(CONVERT(DECIMAL(16,5),ActualAveragePrice) AS NVARCHAR(100))+      
' '+ActualAverageCurrency+']</span>'+  
CASE WHEN ISNULL(IsClientPrice,0)=1 THEN '<a class="documentachor" href="Ord_InternalPODetail.aspx?ipo='+        
CAST(BuyId AS NVARCHAR(20))+        
'" target="_blank">('+CAST(BuyNumber AS NVARCHAR(25)) +')</a>' ELSE '<a class="documentachor" href="Ord_PODetail.aspx?po='+        
CAST(BuyId AS NVARCHAR(20))+        
'" target="_blank">('+CAST(BuyNumber AS NVARCHAR(25)) +')</a>' END,  
@IsClientPrice=IsClientPrice  
  
FROM #tb_tempAverageBestPrice GROUP BY CurrencyCode,IsClientPrice,AverageBestPrice,  
BuyId,BuyNumber,ActualAverageCurrency,ActualAveragePrice  
ORDER BY AverageBestPrice ASC  
  
  
DROP TABLE #tb_tempAverageBestPrice                 
-------------END---------------------                      
                  
               
                  
                  
                  
INSERT INTO tbKubAssistanceDetailsCache (ClientID,PartNo,TotalPartsTreaded,        
TotalGP,AverageBestPrice,DLUP,IsClientPrice)        
VALUES(                
@ClientID,                  
ISNULL(@PartNo,0),                    
ISNULL(@TotalPartsTreaded,0),                    
ISNULL(@TotalGP,0),                    
ISNULL(@AverageBestPrice,0),                   
GETDATE(),  
@IsClientPrice                   
)                    
END                    
                             
SET NOCOUNT OFF                      
END 
GO


