﻿using Rebound.GlobalTrader;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Rebound.GlobalTrader.DAL;



namespace Rebound.GlobalTrader.BLL
{
	public partial class DivisionTarget : BizObject
	{

		#region Properties

		protected static DAL.DivisionTargetElement Settings
		{
			get { return Globals.Settings.DivisionTargets; }
		}
		public System.Int32 DivisionId { get; set; }
		public string DivisionName { get; set; }
		public string RowName { get; set; }
		public string PersonType { get; set; }
		public System.Double? JanTarget { get; set; }
		public System.Double? AllocatedPer { get; set; }
		public System.Double? TotalTarget { get; set; }
		public System.Double? FebTarget { get; set; }
		public System.Double? MarchTarget { get; set; }
		public System.Double? AprTarget { get; set; }
		public System.Double? MayTarget { get; set; }
		public System.Double? JuneTarget { get; set; }
		public System.Double? JulyTarget { get; set; }
		public System.Double? AugTarget { get; set; }
		public System.Double? SepTarget { get; set; }
		public System.Double? OctTarget { get; set; }
		public System.Double? NovTarget { get; set; }
		public System.Double? DecTarget { get; set; }
		public System.Int32? RowId { get; set; }

		public System.Int32? RowValue { get; set; }
		public System.Int32? ClientId { get; set; }
		public System.Int32? UserId { get; set; }
		public System.Int32? IsDrafted { get; set; }

		#endregion
		#region Methods


		public static List<DivisionTarget> GetDivisionAndTeamTarget(System.Int32? divisionNo, System.Int32? managerNo, System.Int32 yearNo, System.String table)
		{
			List<DivisionTargetDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.DivisionTarget.GetDivisionAndTeamTarget(divisionNo, managerNo, yearNo, table);
			if (lstDetails == null)
			{
				return new List<DivisionTarget>();
			}
			else
			{
				List<DivisionTarget> lst = new List<DivisionTarget>();
				foreach (DivisionTargetDetails objDetails in lstDetails)
				{
					BLL.DivisionTarget obj = new BLL.DivisionTarget();
					obj.DivisionId = objDetails.DivisionId;
					obj.DivisionName = objDetails.DivisionName;
					obj.PersonType = objDetails.PersonType;
					obj.JanTarget = objDetails.JanTarget;
					obj.FebTarget = objDetails.FebTarget;
					obj.MarchTarget = objDetails.MarchTarget;
					obj.AprTarget = objDetails.AprTarget;
					obj.MayTarget = objDetails.MayTarget;
					obj.JuneTarget = objDetails.JuneTarget;
					obj.JulyTarget = objDetails.JulyTarget;
					obj.AugTarget = objDetails.AugTarget;
					obj.SepTarget = objDetails.SepTarget;
					obj.OctTarget = objDetails.OctTarget;
					obj.NovTarget = objDetails.NovTarget;
					obj.DecTarget = objDetails.DecTarget;
					obj.TotalTarget = objDetails.TotalTarget;
					obj.AllocatedPer = objDetails.AllocatedPer;
					obj.RowId = objDetails.RowId;
					lst.Add(obj);
					obj = null;
				}
				lstDetails = null;
				return lst;
			}
		}

		public static List<DivisionTarget> GetTargetDataForEdit(System.Int32? clientID, System.Int32? selectedDDLID, System.Int32? Userid, System.Int32 yearNo,System.String reqDataType)
		{
			List<DivisionTargetDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.DivisionTarget.GetTargetDataForEdit(clientID, selectedDDLID, Userid, yearNo, reqDataType);
			if (lstDetails == null)
			{
				return new List<DivisionTarget>();
			}
			else
			{
				List<DivisionTarget> lst = new List<DivisionTarget>();
				foreach (DivisionTargetDetails objDetails in lstDetails)
				{
					BLL.DivisionTarget obj = new BLL.DivisionTarget();
					obj.RowValue = objDetails.RowValue;
					obj.RowName = objDetails.RowName;
					obj.PersonType = objDetails.PersonType;
					obj.JanTarget = objDetails.JanTarget;
					obj.FebTarget = objDetails.FebTarget;
					obj.MarchTarget = objDetails.MarchTarget;
					obj.AprTarget = objDetails.AprTarget;
					obj.MayTarget = objDetails.MayTarget;
					obj.JuneTarget = objDetails.JuneTarget;
					obj.JulyTarget = objDetails.JulyTarget;
					obj.AugTarget = objDetails.AugTarget;
					obj.SepTarget = objDetails.SepTarget;
					obj.OctTarget = objDetails.OctTarget;
					obj.NovTarget = objDetails.NovTarget;
					obj.DecTarget = objDetails.DecTarget;
					obj.TotalTarget = objDetails.TotalTarget;
					obj.AllocatedPer = objDetails.AllocatedPer;
					obj.RowId = objDetails.RowId;
					obj.ClientId = objDetails.ClientId;
					obj.UserId = objDetails.UserId;
					obj.IsDrafted = objDetails.IsDrafted;
					lst.Add(obj);
					obj = null;
				}
				lstDetails = null;
				return lst;
			}
		}

		/// <summary>
		/// Update
		/// Calls [usp_update_DivisionTeamTarget]
		/// </summary>
		public static bool Update(System.Int32? rowId, System.String rowType, System.String columnName, System.Double? targetValue,System.Int32? updatedBy,System.Int32? Year, System.Int32? divisionNo)
		{
			return SiteProvider.DivisionTarget.Update(rowId, rowType, columnName, targetValue, updatedBy, Year, divisionNo);
		}

		//soorya
		public static bool UpdateDivisionGrid(List<DivisionTargetDetails> updateDivisions)
        {
			return SiteProvider.DivisionTarget.UpdateDivisionGrid(updateDivisions);
		}

		public static bool SubmitEditGridChanges(System.Int32? clientID, System.Int32? Userid)
		{
			return SiteProvider.DivisionTarget.SubmitEditGridChanges(clientID, Userid);
		}

		/// <summary>
		/// Update
		/// Calls [usp_saveAllDivisionTeamTarget]
		/// </summary>
		public static bool SaveAllDivisionTeamTarget(System.Int32? Year, System.Int32? divisionNo, System.Int32? updatedBy)
		{
			return DAL.SiteProvider.DivisionTarget.SaveAllDivisionTeamTarget(Year, divisionNo, updatedBy);
		}

		#endregion

	}
}