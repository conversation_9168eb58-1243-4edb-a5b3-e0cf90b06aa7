<%--
     Marker     Changed by      Date         Remarks
<%--[001]      Suhail          02/05/2018   Added Credit Limit2  --%>
<%--[002]      <PERSON>     21/01/2020   Added maxlength="1" for StopStatus  --%>
<%@ Control Language="C#" CodeBehind="CompanyFinanceInfo_Link.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Forms.CompanyFinanceInfo_Link" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<%@ Register TagPrefix="cc1" Namespace="Rebound.GlobalTrader.Site" Assembly="Rebound.GlobalTrader.Site" %>
<ReboundUI_Form:DesignBase ID="ctlDB" runat="server" ShowRequiredFieldsExplanation="true" ShowQuickHelp="false">
	<Links>	    
		<ReboundUI:IconButton ID="ibtnSave" runat="server" IconGroup="Nugget" IconTitleResource="LinkAccount" IconCSSType="Save" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
		<ReboundUI:IconButton ID="ibtnCancel" runat="server" IconGroup="Nugget" IconTitleResource="Cancel" IconCSSType="Cancel" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
	</Links>

	<Explanation><%=Functions.GetGlobalResource("FormExplanations", "CompanyFinanceInfo_Link")%></Explanation>

	<Content>
		<ReboundUI_Table:Form id="frm" runat="server">
			<ReboundUI_Form:FormField id="ctlVatNumber" runat="server" FieldID="txtVatNumber" ResourceTitle="VatNumber" IsRequiredField="true">
				<Field><ReboundUI:ReboundTextBox ID="txtVatNumber" runat="server"  maxlength="100" />
					<img id="btnSearchCompanybyVatNo" style="cursor:pointer;vertical-align: middle;"  src="../../../../App_Themes/Original/images/autosearch/form.gif" title="Search Company by Vat No"/></Field>	
			</ReboundUI_Form:FormField>
			<ReboundUI_FormFieldCollection:MultiSelection ID="ctlSelectCompany" runat="server" SelectedTitleResource="SelectedCompanies" UnselectedTitleResource="UnselectedCompanies" CssClass="SelectCompanyTable" IsRequiredField="true"/>
		</ReboundUI_Table:Form>
	</Content>
</ReboundUI_Form:DesignBase>
		<style>
        	#ctl00_cphMain_ctlFinanceInfo_ctlDB_ctl14_ctlLink_ctlDB_frm{
        		width:50% !important;
			}
        </style>