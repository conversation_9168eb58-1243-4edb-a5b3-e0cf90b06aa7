///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//Marker     Changed by      Date         Remarks
//[001]      Vinay           26/07/2012   Set the application to only accept qtys greater than 0:- ESMS Task-103
//[003]      Suhail          12/04/2018    changes MSL text to drop down list
//[004]      Aashu Singh    22/06/2018    make type field mandatory
//[005]      Anand Gupta    22/06/2018   IHS
//[006]      Ravi Bhushan   29-08-2023   RP-2227 (AS6081)
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.CustomerRequirementMainInfo_Edit = function (element) {
    Rebound.GlobalTrader.Site.Controls.Forms.CustomerRequirementMainInfo_Edit.initializeBase(this, [element]);
    this._intCustomerRequirementID = -1;
    this._intCompanyID = 0;
    this._strSourceSelected = "";
    this._RadChecked = false;
    this._radioCheck = false;
    this._isPoRequest = false;
    this._intBOMID = -1;
    this._radObsoleteChk = false;
    this._radLastTimeBuyChk = false;
    this._radRefirbsAcceptableChk = false;
    this._radTestingRequiredChk = false;
    this._BOMHeaderDisplayStatus = false;
    this._blnCurInSameFaimly = true;
    this._intCurrencyNo = 0;
    this._radAlternativesAcceptedChK = false;
    this._radRepeatBusinessChk = false;
    this._hidCountryOfOrigin = "";
    this._hidCountryOfOriginNo = 0;
    this._hidLifeCycleStage = "";
    this._hidHTSCode = "";
    this._hidAveragePrice = 0;
    this._hidPackaging = "";
    this._hidPackagingSize = "";
    this._hidDescriptions = "";
    //ihs variable start
    this._ctlCompany = "";
    // this._hidCompanyName = "";
    //this._hidCompanyID = 0;
    this._ctlContact = -1;//res.ContactNo, res.Contact
    // this._hidContactID = -1;
    this._ctlQuantity = 0;
    this._ctlPartNo = "";
    this._ctlCustomerPart = "";
    // this._ctlManufacturer = "";//res.ManufacturerNo, res.Manufacturer
    this._hidManufacturer = "";
    this._hidManufacturerNo = "";
    this._ctlDateCode = "";
    this._ctlProduct = "";
    this._ctlProductDis = false;//(res.Product, res.IsProdHaz)
    // this._ctlPrdDutyCodeRate = "";
    this._hidProductID = 0;
    //this._ctlPackage = "";
    this._hidPackageID = -1;
    //this._ctlTargetPrice = 0;
    this._hidPrice = 0;
    //this._ctlCurrency = "";
    this._hidCurrencyID = 0;
    this._ctlDateRequired = "";
    //this._ctlUsage = "";
    this._hidUsageID = -1;
    this._ctlNotes = "";
    this._ctlInstructions = "";
    this._ctlROHS = -1;
    //this._hidROHS = 0;
    // this._ctlClosed = "";
    // this._ctlClosedReason = "";
    //this._hidDisplayStatus = "";
    this._ctlPartWatch = "";
    this._ctlBOM = false;
    this._ctlBOMName = "";
    this._ctlBOMHeader = "";//res.BOMId, $R_FN.setCleanTextValue(res.BOMHeader
    this._hidBOMID = 0;
    //this._hidBOMHeaderDisplayStatus = false;
    this._ctlMSL = -1;
    // this._ctlFactorySealed = false;
    this._ctlPQA = false;
    //this._ctlObsolete = false;
    //this._ctlLastTimeBuy = false;
    //this._ctlRefirbsAcceptable = false;
    //this._ctlTestingRequired =false;
    // this._ctlTargetSellPrice = 0;
    //this._ctlCompetitorBestoffer = 0;
    this._ctlCustomerDecisionDate = "";
    this._ctlRFQClosingDate = "";
    this._ctlQuoteValidityRequiredHid = "";
    // this._ctlQuoteValidityRequired = "";
    this._ctlTypeHid = -1;
    //this._ctlType = "";
    this._ctlOrderToPlace = false;
    // this._ctlRequirementforTraceability = "";
    this._ctlRequirementforTraceabilityHid = "";
    this._ctlTargetSellPriceHidden = 0;
    this._ctlCompetitorBestofferHidden = 0;
    this._ctlEAU = "";
    this._hidCustGCNo = null;
    //this._ctlAlternativesAccepted = false;
    //this._ctlRepeatBusiness = false;
    //this._blnProdInactive =false;
    //this._strhidMSL = 0;
    this._ctlSalespersion = null;
    this._hidSalesPersion = null;

    this._IHSProductNo = 0;
    this._IHSProduct = "";
    this._IHSHTSCode = "";
    this._IHSDutyCode = 0;
    this._AlternateStatus = 0;
    this.chkAlternatives = false;
    this._ECCNCode = "";
    this._ctlPackage = "";
    //ihs variable end
    this._isRestrictedManufacturer = 0;
    this._RestrictedMFRMessage = "";
    this._PartEditStatus = 0;
    this._searchType = "";
    var strsearchtype = "";
    this._enmSearchType = 0;
    this._ctlAS6081 = "0";


};

Rebound.GlobalTrader.Site.Controls.Forms.CustomerRequirementMainInfo_Edit.prototype = {

    get_intCustomerRequirementID: function () { return this._intCustomerRequirementID; }, set_intCustomerRequirementID: function (value) { if (this._intCustomerRequirementID !== value) this._intCustomerRequirementID = value; },
    // get_intCompanyID: function() { return this._intCompanyID; }, set_intCompanyID: function(value) { if (this._intCompanyID !== value) this._intCompanyID = value; },
    // get_radFactorySealedSource: function () { return this._radFactorySealedSource; }, set_radFactorySealedSource: function (v) { if (this._radFactorySealedSource !== v) this._radFactorySealedSource = v; },
    //get_arySources: function () { return this._arySources; }, set_arySources: function (v) { if (this._arySources !== v) this._arySources = v; },
    //get_radObsolete: function () { return this._radObsolete; }, set_radObsolete: function (v) { if (this._radObsolete !== v) this._radObsolete = v; },
    //get_radLastTimeBuy: function () { return this._radLastTimeBuy; }, set_radLastTimeBuy: function (v) { if (this._radLastTimeBuy !== v) this._radLastTimeBuy = v; },
    //get_radRefirbsAcceptable: function () { return this._radRefirbsAcceptable; }, set_radRefirbsAcceptable: function (v) { if (this._radRefirbsAcceptable !== v) this._radRefirbsAcceptable = v; },
    //get_radTestingRequired: function () { return this._radTestingRequired; }, set_radTestingRequired: function (v) { if (this._radTestingRequired !== v) this._radTestingRequired = v; },
    //get_radAlternativesAccepted: function () { return this._radAlternativesAccepted; }, set_radAlternativesAccepted: function (v) { if (this._radAlternativesAccepted !== v) this._radAlternativesAccepted = v; },
    //get_radRepeatBusiness: function () { return this._radRepeatBusiness; }, set_radRepeatBusiness: function (v) { if (this._radRepeatBusiness !== v) this._radRepeatBusiness = v; },
    // get_tblPartdetails: function () { return this._tblPartdetails; }, set_tblPartdetails: function (v) { if (this._tblPartdetails !== v) this._tblPartdetails = v; },
    get_btn1: function () { return this._btn1; }, set_btn1: function (v) { if (this._btn1 !== v) this._btn1 = v; },
    get_btn2: function () { return this._btn2; }, set_btn2: function (v) { if (this._btn2 !== v) this._btn2 = v; },
    get_lblError: function () { return this._lblError; }, set_lblError: function (v) { if (this._lblError !== v) this._lblError = v; },
    get_pnlPartDetail: function () { return this._pnlPartDetail; }, set_pnlPartDetail: function (value) { if (this._pnlPartDetail !== value) this._pnlPartDetail = value; },
    get_ctltblPartdetails: function () { return this._ctltblPartdetails; }, set_ctltblPartdetails: function (v) { if (this._ctltblPartdetails !== v) this._ctltblPartdetails = v; },


    initialize: function () {
        Rebound.GlobalTrader.Site.Controls.Forms.CustomerRequirementMainInfo_Edit.callBaseMethod(this, "initialize");
        this.addShown(Function.createDelegate(this, this.formShown));
        //this._tblPartdetails.addSelectedIndexChanged(Function.createDelegate(this, this.getParSearch));
        
        this._ctltblPartdetails.addItemSelected(Function.createDelegate(this, this.getIHSDataSelected));
        document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_cmbIHS12txt").addEventListener("blur", Function.createDelegate(this, this.myBlurFunction));

    },
    getFormControlID: function (ParentId, controlID) {

        var str = "";
        str = String.format("{0}_{1}", ParentId, controlID);
        return str;
    },
    formShown: function () {
        //code start for part edit
        //alert(this._PartEditStatus);
        if (this._PartEditStatus == 1) {
            $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_showhidepartnolable").hide();
            $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_showhidePartnotext").show();
            //$("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_cmbPartEccnMappedaut_ctl04").show();
            //$("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_cmbPartEccnMappedaut").hide();
            $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_cmbPartEccnMappedaut").show();
            this.ClearIhsDataOnLoad();
            document.getElementById("ParttypeSearch1").addEventListener("click", Function.createDelegate(this, this.changeSearchType));
            if (this._enmSearchType == 0) {
                $("#ParttypeSearch1").removeClass("searchType_StartsWith");
                $("#ParttypeSearch1").removeClass("searchType_ExactWith");
                $("#ParttypeSearch1").addClass("searchType_Contains");
                $("#ParttypeSearch1").attr("title", "Contains");
                $("#ParttypeSearch1").attr("alt", "Contains");
                $find(this.getFormControlID(this._element.id, 'cmbIHS'))._aut._txtGroup = "contains";
            }
            if ($find(this.getFormControlID(this._element.id, 'cmbIHS'))) $find(this.getFormControlID(this._element.id, 'cmbIHS'))._aut.addSelectionMadeEvent(Function.createDelegate(this, this.getIHSPartDetails));
            document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_btnClear").addEventListener("click", Function.createDelegate(this, this.ClearIhsData));
            //edit part
            let PartNo;
            if (this._ctlPartNo != undefined && this._ctlPartNo.indexOf("<") != -1) {
                let myPartNo = this._ctlPartNo.split("<");
                myPartNo = myPartNo[1].split(">");
                PartNo = myPartNo[1];
            }
            else {
                PartNo = this._ctlPartNo;
            }

            document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_cmbIHS12txt").value = PartNo;
            //code end
            document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_btn1").addEventListener("click", Function.createDelegate(this, this.SearchTypePopup));
            document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_btn3").addEventListener("click", Function.createDelegate(this, this.Toggle1));
            document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_btn2").addEventListener("click", Function.createDelegate(this, this.Toggle2));
            document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_btn5").addEventListener("click", Function.createDelegate(this, this.Canceltop));
            document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_btn6").addEventListener("click", Function.createDelegate(this, this.Cancelbottom));
            $addHandler(document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_btnOK_hyp"), "click", Function.createDelegate(this, this.getValuesByPartsOnClick));
        }

        else {
            $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_showhidePartnotext").hide();
            $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_showhidepartnolable").show();
            //$("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_cmbPartEccnMappedaut_ctl04").hide();
        }
        $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_txtPartNo").hide();
        $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_lblError").hide();
        // part edit status end

        $("#lblRsMFREdit").hide();
        $("#spanmfrEdit").text("");
        $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_chkAlternatives_ctl00").addClass("off");
        if (this._AlternateStatus != 'undefined' && this._AlternateStatus > 0) {
            $find(this.getFormControlID(this._element.id, 'chkAlternatives')).enableButton(false);
        }
        else {
            $find(this.getFormControlID(this._element.id, 'chkAlternatives')).enableButton(true);
        }
        $("#lbledittxtInstructions").text(2000 - this._ctlInstructions.length);
        $("#lbledittxtNotes").text(2000 - this._ctlNotes.length);
        //[005] ihs set value
        $find(this.getFormControlID(this._element.id, 'ddlSalesman')).getData();
        $find(this.getFormControlID(this._element.id, 'ddlROHS')).getData();
        $find(this.getFormControlID(this._element.id, 'ddlType')).getData();
        $find(this.getFormControlID(this._element.id, 'ddlUsage')).getData();
        //$find(this.getFormControlID(this._element.id, 'ddlPackage')).getData();

        $find(this.getFormControlID(this._element.id, 'ddlMsl')).getData();
        $find(this.getFormControlID(this._element.id, 'ddlQuoteValidityRequired')).getData();
        $find(this.getFormControlID(this._element.id, 'ddlRequirementforTraceability')).getData();

        $find(this.getFormControlID(this._element.id, 'ddlAS6081')).getData(); // [006] (bind dropdown)
        //Dropdown value bind
        $find(this.getFormControlID(this._element.id, 'ddlSalesman')).setValue(this._salesmanNo);
        // alert(this.setControlValue(this.getFormControlID(this._element.id, 'ddlSalesman'), 'DropDown'));
        $find(this.getFormControlID(this._element.id, 'ddlROHS')).setValue(this._ctlROHS);
        $find(this.getFormControlID(this._element.id, 'ddlType')).setValue(this._ctlTypeHid);
        $find(this.getFormControlID(this._element.id, 'ddlUsage')).setValue(this._hidUsageID);
        //$find(this.getFormControlID(this._element.id, 'ddlPackage')).setValue(this._hidPackageID);
        $find(this.getFormControlID(this._element.id, 'ddlMsl')).setValue(this._ctlMSL);
        $find(this.getFormControlID(this._element.id, 'ddlQuoteValidityRequired')).setValue(this._ctlQuoteValidityRequiredHid);
        $find(this.getFormControlID(this._element.id, 'ddlRequirementforTraceability')).setValue(this._ctlRequirementforTraceabilityHid);
        //debugger;
        $find(this.getFormControlID(this._element.id, 'ddlAS6081')).setValue(this._ctlAS6081); //[006]
        //Lable Value bind
        $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_lblCustomer").text(this._ctlCompany);
        $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_lblContact").text(this._ctlContact);


        $find(this.getFormControlID(this._element.id, 'cmbManufacturer')).setValue(this._hidManufacturer, $R_FN.setCleanTextValue(this._hidManufacturerNo));
        $find(this.getFormControlID(this._element.id, 'cmbProducts')).setValue(this._hidProductID, this._ctlProduct);
        $find(this.getFormControlID(this._element.id, 'cmbPackage')).setValue(this._hidPackageID, this._ctlPackage);
        $find(this.getFormControlID(this._element.id, 'cmbSalespersion')).setValue(this._hidSalesPersion, this._ctlSalespersion);


        //TextBox value bind
        $get(this.getFormControlID(this._element.id, 'txtCustomerPartNo')).value = this._ctlCustomerPart;
        $get(this.getFormControlID(this._element.id, 'txtQuantity')).value = this._ctlQuantity;
        let PartNo;
        if (this._ctlPartNo != undefined && this._ctlPartNo.indexOf("<") != -1) {
            let myPartNo = this._ctlPartNo.split("<");
            myPartNo = myPartNo[1].split(">");
            PartNo = myPartNo[1];
        }
        else {
            PartNo = this._ctlPartNo;
        }
        $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_lblPartNo").text(PartNo);



        $get(this.getFormControlID(this._element.id, 'txtNotes')).value = this._ctlNotes;
        $get(this.getFormControlID(this._element.id, 'txtInstructions')).value = this._ctlInstructions;
        $get(this.getFormControlID(this._element.id, 'txtDateCode')).value = this._ctlDateCode;
        $get(this.getFormControlID(this._element.id, 'txtBOMName')).value = this._ctlBOMName;
        $get(this.getFormControlID(this._element.id, 'txtTargetPrice')).value = this._hidPrice;
        $get(this.getFormControlID(this._element.id, 'txtTargetSellPrice')).value = this._ctlTargetSellPriceHidden;
        $get(this.getFormControlID(this._element.id, 'txtRFQClosingDate')).value = this._ctlRFQClosingDate;
        $get(this.getFormControlID(this._element.id, 'txtDateRequired')).value = this._ctlDateRequired;
        $get(this.getFormControlID(this._element.id, 'txtCustomerDecisionDate')).value = this._ctlCustomerDecisionDate;
        $get(this.getFormControlID(this._element.id, 'txtEau')).value = this._ctlEAU;
        $get(this.getFormControlID(this._element.id, 'txtCompetitorBestoffer')).value = this._ctlCompetitorBestofferHidden;
       
        //Checkbox Value Bind
        $find(this.getFormControlID(this._element.id, 'chkPartWatch')).setChecked(this._ctlPartWatch);
        $find(this.getFormControlID(this._element.id, 'chkPQA')).setChecked(this._ctlPQA);
        $find(this.getFormControlID(this._element.id, 'chkBOM')).setChecked(this._ctlBOM);
        $find(this.getFormControlID(this._element.id, 'chkOrderToPlace')).setChecked(this._ctlOrderToPlace);

        $find(this.getFormControlID(this._element.id, 'chkAlternativesAccepted')).setChecked(this._radAlternativesAcceptedChK);
        $find(this.getFormControlID(this._element.id, 'chkTestingRequired')).setChecked(this._radTestingRequiredChk);
        $find(this.getFormControlID(this._element.id, 'chkRefirbsAcceptable')).setChecked(this._radRefirbsAcceptableChk);
        $find(this.getFormControlID(this._element.id, 'chkFactorySealedSource')).setChecked(this._radioCheck);
        $find(this.getFormControlID(this._element.id, 'chkRepeatBusiness')).setChecked(this._radRepeatBusinessChk);
        $find(this.getFormControlID(this._element.id, 'chkObsolete')).setChecked(this._radObsoleteChk);
        $find(this.getFormControlID(this._element.id, 'chkLastTimeBuy')).setChecked(this._radLastTimeBuyChk);

        $find(this.getFormControlID(this._element.id, 'ddlCurrency'))._intGlobalCurrencyNo = this._hidCustGCNo;
        $find(this.getFormControlID(this._element.id, 'ddlCurrency'))._blnIsBuy = false;
        $find(this.getFormControlID(this._element.id, 'ddlCurrency')).setValue(this._hidCurrencyID);
        $find(this.getFormControlID(this._element.id, 'ddlCurrency')).getData();

        //[005] ihs set value end
        //this.showField("ctlPartNo", !this._isPoRequest);
        $('#divBlockBox').hide();
        if (this._blnFirstTimeShown) {
            this.addSave(Function.createDelegate(this, this.saveClicked));
        }

        //var checkBoxId = checkBoxId = String.format("{0}_{1}", this._radFactorySealedSource.id, (this._radioCheck == true ? 0 : 1));
        //document.getElementById(checkBoxId).checked = true;
        //var radObsoleteId = String.format("{0}_{1}", this._radObsolete.id, (this._radObsoleteChk == true || this._radObsoleteChk == "true" ? 0 : 1));
        //document.getElementById(radObsoleteId).checked = true;
        //var radOLastTimeBuyId = String.format("{0}_{1}", this._radLastTimeBuy.id, (this._radLastTimeBuyChk == true || this._radLastTimeBuyChk == "true" ? 0 : 1));
        //document.getElementById(radOLastTimeBuyId).checked = true;
        //var radRefirbsAcceptableId = String.format("{0}_{1}", this._radRefirbsAcceptable.id, (this._radRefirbsAcceptableChk == true || this._radRefirbsAcceptableChk == "true" ? 0 : 1));
        //document.getElementById(radRefirbsAcceptableId).checked = true;
        //var radTestingRequiredId = String.format("{0}_{1}", this._radTestingRequired.id, (this._radTestingRequiredChk == true || this._radTestingRequiredChk == "true" ? 0 : 1));
        //document.getElementById(radTestingRequiredId).checked = true;
        //var radAlternativesAcceptedID = String.format("{0}_{1}", this._radAlternativesAccepted.id, (this._radAlternativesAcceptedChK == true || this._radAlternativesAcceptedChK == "true" ? 0 : 1));
        //document.getElementById(radAlternativesAcceptedID).checked = true;
        //var radRepeatBusinessID = String.format("{0}_{1}", this._radRepeatBusiness.id, (this._radRepeatBusinessChk == true || this._radRepeatBusinessChk == "true" ? 0 : 1));
        //document.getElementById(radRepeatBusinessID).checked = true;


        //creat error style color

        document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_parterror").style.backgroundColor = "";
        document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_parterror1").style.backgroundColor = "";
        document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_QuantityError").style.backgroundColor = "";
        document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_QuantityError1").style.backgroundColor = "";
        document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_CustomerTargetPriceError").style.backgroundColor = "";
        document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_CustomerTargetPriceError1").style.backgroundColor = "";
        document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_ManufacturerError").style.backgroundColor = "";
        document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_ManufacturerError1").style.backgroundColor = "";
        document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_ManufacturerError").style.backgroundColor = "";
        document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_ManufacturerError1").style.backgroundColor = "";
        document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_ProductError").style.backgroundColor = "";
        document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_ProductError1").style.backgroundColor = "";
        document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_TraceabilityError").style.backgroundColor = "";
        document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_TraceabilityError1").style.backgroundColor = "";
        document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_ddlTypeError").style.backgroundColor = "";
        document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_ddlTypeError1").style.backgroundColor = "";
        document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_SalespersonError").style.backgroundColor = "";
        document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_SalespersonError1").style.backgroundColor = "";
        document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_CurrencyError").style.backgroundColor = "";
        document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_CurrencyError1").style.backgroundColor = "";
        document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_DateRequiredError").style.backgroundColor = "";
        document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_DateRequiredError1").style.backgroundColor = "";

        //end

        $find(this.getFormControlID(this._element.id, 'ddlBOM'))._intCompanyID = this._intCompanyID;
        $find(this.getFormControlID(this._element.id, 'ddlBOM')).getData();
        $find(this.getFormControlID(this._element.id, 'ddlBOM')).setValue(this._intCompanyID);
        this.allowEditingCurrency(this._blnCurInSameFaimly);


        if (this._IHSProductNo != 0 && this._hidProductID != 0) {

            document.getElementById("lblihsproduct").style.color = 'yellow'
            //document.getElementById("lblMatchproduct").style.color = 'yellow'
            $("#spnIHSProduct").text(this._IHSProduct);
        }
        else if (this._IHSProduct != 'undefined' && this._IHSProduct != '') {
            document.getElementById("lblihsproduct").style.color = 'yellow'
            $("#spnIHSProduct").text(this._IHSProduct);
            document.getElementById("lblMatchproduct").style.color = ''

        }
        else {
            document.getElementById("lblihsproduct").style.color = ''
            $("#spnIHSProduct").text(" ( N/A ) ");
            document.getElementById("spnIHSProduct").style.color = 'yellow'
            document.getElementById("lblMatchproduct").style.color = ''

        }

        if (this._IHSHTSCode != 'undefined' && this._IHSHTSCode != '') {
            document.getElementById("lblihsHTSCode").style.color = 'yellow'
            $("#spnHTSCode").text(this._hidHTSCode);

        }
        else if (this._hidHTSCode != 'undefined' && this._hidHTSCode != '') {
            document.getElementById("lblihsHTSCode").style.color = 'yellow'
            $("#spnHTSCode").text(this._hidHTSCode);//text(this._hidHTSCode);
        }
        else {

            document.getElementById("lblihsHTSCode").style.color = ''
            $("#spnHTSCode").text(" ( N/A ) ");
            document.getElementById("spnHTSCode").style.color = 'yellow'
        }
        if (this._IHSDutyCode != "") {
            $("#spnIHSDutyCode").text(this._IHSDutyCode);
            document.getElementById("lblduty").style.color = 'yellow'
            //alert(this._IHSDutyCode);

        }
        else {
            document.getElementById("lblduty").style.color = ''
            //$("#spnIHSDutyCode").text(this.IHSDutyCode);
            $("#spnIHSDutyCode").text(" ( N/A ) ");
            document.getElementById("spnIHSDutyCode").style.color = 'yellow'

        }
        if (this._hidCountryOfOriginNo != 'undefined' && this._hidCountryOfOriginNo != 0) {
            document.getElementById("lblCoo").style.color = 'yellow'
            $("#spnIHSCountryOfOrigin").text(this._hidCountryOfOrigin);
        }
        else {
            document.getElementById("lblCoo").style.color = ''
            $("#spnIHSCountryOfOrigin").text(" ( " + "N/A" + " ) ");
            document.getElementById("spnIHSCountryOfOrigin").style.color = 'yellow'

        }
        if (this._hidLifeCycleStage != 'undefined' && this._hidLifeCycleStage != '') {
            document.getElementById("lblpartstaus").style.color = 'yellow'
            $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_LableLifeStatus").text(this._hidLifeCycleStage);
        }
        else {
            document.getElementById("lblpartstaus").style.color = ''
            $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_LableLifeStatus").text(" ( " + "N/A" + " ) ");
            document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_LableLifeStatus").style.color = 'yellow'

        }

        //code for ECCN Code
        if (this._ECCNCode != 'undefined' && this._ECCNCode != '') {
            document.getElementById("lblECCNCode").style.color = 'yellow'
            //$("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_LableECCNCode").text(this._ECCNCode);
            //this.SelectIHSPartEccnMappedFormLoad(this._ECCNCode, this._ctlPartNo);
            this.SelectIHSEccnCode(this._ECCNCode);
        }
        else {
            document.getElementById("lblECCNCode").style.color = ''
            $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_LableECCNCode").text(" ( " + "N/A" + " ) ");
            document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_LableECCNCode").style.color = 'yellow'

        }
        //code end for ECCN Code

        //this._ECCNCode


        document.getElementById("myplasegrde").addEventListener("click", Function.createDelegate(this, this.getPartDetaildata));
        document.getElementById("closePoppartdetails").addEventListener("click", Function.createDelegate(this, this.hidpartdetaildive));

        $R_FN.showElement(this._pnlPartDetail, false);
        $('.dropDownRefresh').hide();
        if ($find(this.getFormControlID(this._element.id, 'cmbManufacturer'))) $find(this.getFormControlID(this._element.id, 'cmbManufacturer'))._aut.addSelectionMadeEvent(Function.createDelegate(this, this.RsMfrChanged));
        document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_cmbManufactureraut_ctl04").addEventListener("click", Function.createDelegate(this, this.ResetMFR));
        //this.myBlurFunction();
        //trigger check restrict manufacturer for first time load
        this.RsMfrChanged();
    },

    getIHSPartDetails: function () {
        var id = $find(this.getFormControlID(this._element.id, 'cmbIHS'))._aut._varSelectedValue;
        var arr = $find(this.getFormControlID(this._element.id, 'cmbIHS'))._aut._varSelectedValue.split("->");
        $find(this.getFormControlID(this._element.id, 'cmbIHS'))._txt.value = arr[0];
        console.log($find(this.getFormControlID(this._element.id, 'cmbIHS'))._aut._varSelectedExtraData);
        this.SaveIHSPartDetail($find(this.getFormControlID(this._element.id, 'cmbIHS'))._aut._varSelectedExtraData);

    },
    SaveIHSPartDetail: function (IHSResult) {
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/CusReqAdd");
        obj.set_DataObject("CusReqAdd");
        obj.set_DataAction("SaveIHSPartDetail");
        obj.addParameter("IHSResult", IHSResult);
        obj.addDataOK(Function.createDelegate(this, this.setIHSPartDetail));
        obj.addError(Function.createDelegate(this, this.IHSPartDetailError));
        obj.addTimeout(Function.createDelegate(this, this.IHSPartDetailError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;


    },
    setIHSPartDetail: function (args) {
        var obj = args._result.Result
        if (obj[0].IHSPartsId > 0) {
            $("#lblihsproduct").show();
            $("#lblihsHTSCode").show();
            $("#lblduty").show();
            $("#lblCoo").show();
            var PartName = obj[0].ID;
            if (PartName != 'undefined ' && PartName.length > 0) {
                //$get(this.getFormControlID(this._element.id, 'txtPartNo')).value = $R_FN.setCleanTextValue(PartName);
                $get(this.getFormControlID(this._element.id, 'cmbIHS12txt')).value = $R_FN.setCleanTextValue(PartName);
                document.getElementById("lblparts").style.color = 'yellow'
            }
            else {
                document.getElementById("lblparts").style.color = ''
            }
            if (obj[0].ROHSName != 'undefined ' && obj[0].ROHSName != '' && obj[0].ROHSName.length > 0) {
                // $("#spnMSL").show();
                $("#spnMSL").text(" ( " + obj[0].ROHSName + " ) ");
                $find(this.getFormControlID(this._element.id, 'ddlMsl')).setValue(obj[0].ROHSName);
                document.getElementById("lblmls").style.color = 'yellow';
                if (obj[0].ROHSName == 'N/A')
                    document.getElementById("spnMSL").style.color = 'yellow';
                else
                    document.getElementById("spnMSL").style.color = '';
            }
            else {
                $("#spnMSL").text(" ( " + "N/A" + " ) ");
                $find(this.getFormControlID(this._element.id, 'ddlMsl')).setValue("N/A");
                document.getElementById("spnMSL").style.color = 'yellow';
                document.getElementById("lblmls").style.color = 'yellow';





            }
            if (obj[0].Manufacturer != 'undefined ' && obj[0].Manufacturer != '' && obj[0].Manufacturer.length > 0) {
                if (obj[0].ManufacturerNo != 0) {
                    $find(this.getFormControlID(this._element.id, 'cmbManufacturer')).setValue(obj[0].ManufacturerNo, $R_FN.setCleanTextValue(obj[0].Manufacturer));
                    $("#spnManufacturer").hide();
                    document.getElementById("lblmrf").style.color = 'yellow'
                }
                else {

                    $find(this.getFormControlID(this._element.id, 'cmbManufacturer')).setValue("", 0);
                    $("#spnManufacturer").show();
                    $("#spnManufacturer").text(" (" + obj[0].ManufacturerFullName + " ) ");
                    document.getElementById("lblmrf").style.color = ''
                }

            }
            else {

                $("#spnManufacturer").hide();

            }

            if (obj[0].ProdDesc != 'undefined ' && obj[0].IHSProdDesc.length > 0) {

                if (obj[0].ProdNo != 0) {
                    $find(this.getFormControlID(this._element.id, 'cmbProducts')).setValue(obj[0].ProdNo, $R_FN.setCleanTextValue(obj[0].ProdDesc));
                    //$("#spnManufacturer").hide();
                    document.getElementById("lblMatchproduct").style.color = 'yellow';
                    $("#spnIHSProduct").text($R_FN.setCleanTextValue(obj[0].IHSProdDesc));
                    document.getElementById("lblihsproduct").style.color = 'yellow';
                }
                else {
                    $find(this.getFormControlID(this._element.id, 'cmbProducts')).setValue("", 0);
                    $("#spnIHSProduct").show();
                    $("#spnIHSProduct").text($R_FN.setCleanTextValue(obj[0].IHSProdDesc));
                    document.getElementById("lblihsproduct").style.color = 'yellow';
                }

            }
            else {
                if (obj[0].HTSCode != '' || obj[0].IHSProdDes != '') {
                    $("#spnIHSProduct").show();
                    $("#spnIHSProduct").text(" ( " + "N/A" + " ) ");
                    document.getElementById("lblihsproduct").style.color = 'yellow';
                    document.getElementById("spnIHSProduct").style.color = 'yellow';
                } else {
                    document.getElementById("lblihsproduct").style.color = '';
                }
            }

            if (obj[0].PackageId > 0) {
                $("#lblECCNCode").show();
                // $find(this.getFormControlID(this._element.id, 'ddlPackage')).setValue(obj[0].PackageId);
                $find(this.getFormControlID(this._element.id, 'cmbPackage')).setValue(obj[0].PackageId, $R_FN.setCleanTextValue(obj[0].PackageDescription));
                $("#spnPackage").text(" ( " + obj[0].Packaging + " ) ");
                document.getElementById('lblPackage').style.color = "yellow";
                document.getElementById("spnPackage").style.color = "";


            }
            else {
                $("#lblECCNCode").show();
                $("#spnPackage").text("  ( " + "N/A" + " ) ");
                document.getElementById('lblPackage').style.color = "";
                document.getElementById("spnPackage").style.color = 'yellow'
                // $find(this.getFormControlID(this._element.id, 'ddlPackage')).setValue(0);
                $find(this.getFormControlID(this._element.id, 'cmbPackage')).setValue("", 0);

            }

            if (obj[0].HTSCode != 'undefined ' && obj[0].HTSCode != '' && obj[0].HTSCode.length > 0) {
                $("#spnHTSCode").show();
                $("#spnHTSCode").text(obj[0].HTSCode);
                document.getElementById("lblihsHTSCode").style.color = 'yellow'
            }
            else {
                if (obj[0].HTSCode != '' || obj[0].IHSProdDes != '') {
                    $("#spnHTSCode").show();
                    $("#spnHTSCode").text(" ( " + "N/A" + " ) ");
                    document.getElementById("lblihsHTSCode").style.color = 'yellow'
                    document.getElementById("spnHTSCode").style.color = 'yellow'
                } else {
                    document.getElementById("lblihsHTSCode").style.color = ''
                    document.getElementById("spnHTSCode").style.color = ''
                }

            }

            if (obj[0].IHSDutyCode != 'undefined ' && obj[0].IHSDutyCode != '' && obj[0].IHSDutyCode.length > 0) {
                $("#spnIHSDutyCode").show();
                $("#spnIHSDutyCode").text(" ( " + obj[0].IHSDutyCode + " ) ");
                document.getElementById("lblduty").style.color = 'yellow'
                document.getElementById("spnIHSDutyCode").style.color = ''

            }
            else {
                $("#spnIHSDutyCode").text("( N/A )");
                document.getElementById("lblduty").style.color = ''
                document.getElementById("spnIHSDutyCode").style.color = 'yellow'
            }
            if (obj[0].CountryOfOrigin != 'undefined ' && obj[0].CountryOfOrigin != '' && obj[0].CountryOfOrigin.length > 0) {
                $("#spnIHSCountryOfOrigin").show();
                $("#spnIHSCountryOfOrigin").text(obj[0].CountryOfOrigin);
                document.getElementById("lblCoo").style.color = 'yellow'
                document.getElementById("spnIHSCountryOfOrigin").style.color = ''
            }
            else {
                $("#spnIHSCountryOfOrigin").text(" ( " + "N/A" + " ) ");
                document.getElementById("lblCoo").style.color = ''
                document.getElementById("spnIHSCountryOfOrigin").style.color = 'yellow'
            }

            if (obj[0].PartStatus != 'undefined ' && obj[0].PartStatus != '' && obj[0].PartStatus.length > 0) {
                //$get(this.getFormControlID(this._element.id, 'txtLifeStatus')).value = $R_FN.setCleanTextValue(obj[0].PartStatus);
                $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_LableLifeStatus").text($R_FN.setCleanTextValue(obj[0].PartStatus));
                document.getElementById("lblpartstaus").style.color = 'yellow'
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_LableLifeStatus").style.color = ''
            }
            else {
                //$get(this.getFormControlID(this._element.id, 'txtLifeStatus')).value = "";
                $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_LableLifeStatus").text(" ( " + "N/A" + " ) ");
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_LableLifeStatus").style.color = 'yellow'
                document.getElementById("lblpartstaus").style.color = ''
            }
            //ECCN Code start
            if (obj[0].ECCNCode != 'undefined ' && obj[0].ECCNCode != '' && obj[0].ECCNCode.length > 0) {
                //$("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_LableECCNCode").text($R_FN.setCleanTextValue(obj[0].ECCNCode));
                this.SelectIHSPartEccnMapped(obj[0].ECCNCode, PartName);
                document.getElementById("lblECCNCode").style.color = 'yellow'
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_LableECCNCode").style.color = ''
            }
            else {
                $("#ctl00_cphMain_ctlAdd_ctlDB_ctl14_ctlAdd_ctlDB_LableECCNCode").text(" ( " + "N/A" + " ) ");
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_LableECCNCode").style.color = 'yellow'
                document.getElementById("lblECCNCode").style.color = ''
                //$find('ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_cmbPartEccnMappedaut').reselect();
                this.myBlurFunction();
            }
            //ECCN Code End

            if (obj[0].Packaging != 'undefined ' && obj[0].Packaging != '') {
                this._hidPackaging = obj[0].Packaging;
            }
            else {
                this._hidPackaging = "";
            }
            if (obj[0].PackagingSize != 'undefined ' && obj[0].PackagingSize != '') {
                this._hidPackagingSize = obj[0].PackagingSize;
            }
            else {
                this._hidPackagingSize = "";
            }
            this._hidCountryOfOrigin = obj[0].CountryOfOrigin;
            this._hidCountryOfOriginNo = obj[0].CountryOfOriginNo;
            this._hidLifeCycleStage = obj[0].PartStatus;
            this._hidHTSCode = obj[0].HTSCode;
            this._hidAveragePrice = obj[0].AveragePrice;
            this._hidDescriptions = obj[0].Descriptions;
            this._IHSPartsId = obj[0].IHSPartsId;
            this._ihsCurrencyCode = obj[0].ihsCurrencyCode;
            this._hidIHSProduct = obj[0].IHSProduct;
            this._hidECCNCode = obj[0].ECCNCode;

        }

    },
    IHSPartDetailError: function (args) {
    },
    //Part Eccn Mapping strat code
    SelectIHSPartEccnMapped: function (IHSEccnCode, PartName) {
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/CusReqAdd");
        obj.set_DataObject("CusReqAdd");
        obj.set_DataAction("GetIHSPartEccnDetail");
        obj.addParameter("PartEccnCode", IHSEccnCode);
        obj.addParameter("PartNo", PartName);
        obj.addDataOK(Function.createDelegate(this, this.setIHSPartEccnDetail));
        obj.addError(Function.createDelegate(this, this.IHSPartECCNDetailError));
        obj.addTimeout(Function.createDelegate(this, this.IHSPartECCNDetailError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;


    },
    setIHSPartEccnDetail: function (args) {
        var obj = args._result.Result
        if (obj[0].PartEccnMappedId > 0) {
            //ECCN Code start
            if (obj[0].ECCNCode != 'undefined' && obj[0].ECCNCode != '' && obj[0].ECCNCode.length > 0) {

                $find(this.getFormControlID(this._element.id, 'cmbPartEccnMapped')).setValue(obj[0].ECCNNo, $R_FN.setCleanTextValue(obj[0].ECCNCode));
                this._hidECCNCode = obj[0].ECCNCode;

                $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_cmbPartEccnMappedaut").show();
                $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_cmbPartEccnMappedtxt").show();
                document.getElementById("lblECCNCode").style.color = 'yellow'

            }


        }
        else if (this._hidECCNCode != '') {

            this.SelectIHSEccnCode(this._hidECCNCode);
            $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_cmbPartEccnMappedaut").show();
            $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_cmbPartEccnMappedtxt").show();

        }
        else {
            // alert(3);
            $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_LableECCNCode").text(" ( " + "N/A" + " ) ");
            document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_LableECCNCode").style.color = 'yellow'
            document.getElementById("lblECCNCode").style.color = ''
            this._hidECCNCode = "";
            $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_cmbPartEccnMappedaut").show();
            $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_cmbPartEccnMappedtxt").show();
        }
    },
    IHSPartECCNDetailError: function (args) {
    },
    //code end


    //Bind Eccn Code from TbECCN Table strat code
    SelectIHSEccnCode: function (sEccnCode) {
        //
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/CusReqAdd");
        obj.set_DataObject("CusReqAdd");
        obj.set_DataAction("GetIHSEccnCodeDetail");
        obj.addParameter("ECCNCode", sEccnCode);
        obj.addDataOK(Function.createDelegate(this, this.setIHSEccnCodeDetail));
        obj.addError(Function.createDelegate(this, this.IHSECCNCodeDetailError));
        obj.addTimeout(Function.createDelegate(this, this.IHSECCNCodeDetailError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    setIHSEccnCodeDetail: function (args) {
        //
        var obj = args._result.Result
        if (obj[0].ECCNNo > 0) {
            //ECCN Code start
            if (obj[0].ECCNCode != '') {

                $find(this.getFormControlID(this._element.id, 'cmbPartEccnMapped')).setValue(obj[0].ECCNNo, $R_FN.setCleanTextValue(obj[0].ECCNCode));
                document.getElementById("lblECCNCode").style.color = 'yellow'

                this._hidECCNCode = obj[0].ECCNCode;
                $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_cmbPartEccnMappedaut").show();
                $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_cmbPartEccnMappedtxt").show();
                if (obj[0].ECCNStatus) {
                    $('#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_cmbPartEccnMappedaut_ctl03').addClass('ihspartstatusdoc');
                    $('#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_cmbPartEccnMappedaut_ctl03').prop("title", obj[0].ECCNWarning.replace(/<br\s*\/?>/gi, ''));
                }
                else {
                    $('#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_cmbPartEccnMappedaut_ctl03').removeClass('ihspartstatusdoc');
                    $('#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_cmbPartEccnMappedaut_ctl03').prop("title", "");
                }
            }
            else {
                $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_LableECCNCode").text(" ( " + "N/A" + " ) ");
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_LableECCNCode").style.color = 'yellow'
                document.getElementById("lblECCNCode").style.color = ''
                this._hidECCNCode = "";
                $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_cmbPartEccnMappedaut").show();
                $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_cmbPartEccnMappedtxt").show();
            }
        }
        else {
            //$find('ctl00_cphMain_ctlNotify_ctlDB_ctl14_ctlNotify_ctlDB_ctlSalespersion_ctl03_aut').reselect();
            $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_LableECCNCode").text(" ( " + "N/A" + " ) ");
            document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_LableECCNCode").style.color = 'yellow'
            document.getElementById("lblECCNCode").style.color = ''
            this._hidECCNCode = "";
            $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_cmbPartEccnMappedaut").show();
            $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_cmbPartEccnMappedtxt").show();
        }
    },
    IHSECCNCodeDetailError: function (args) {
        //
    },
    //code end

    ClearIhsData: function () {
        $find('ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_cmbPartEccnMappedaut').reselect();
        //$("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_cmbPartEccnMappedaut").hide();
        //$("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_cmbPartEccnMappedtxt").hide();
        $("#spanmfrEdit").text("");
        $("#spanmfr").text("");
        document.getElementById("lblMatchproduct").style.color = ''
        $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_cmbIHS12aut_hypClose")[0].click();
        $("#lblIhsServiceMessage").text("");
        $("#lblservicemsgerror").text("");
        $get(this.getFormControlID(this._element.id, 'cmbIHS12txt')).value = "";
        $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_LableLifeStatus").text("");
        document.getElementById("lblparts").style.color = '';
        document.getElementById("lblmls").style.color = '';
        $find(this.getFormControlID(this._element.id, 'ddlMsl')).setValue(0);
        $find(this.getFormControlID(this._element.id, 'cmbManufacturer')).setValue("", 0);
        document.getElementById("lblmrf").style.color = '';
        $find(this.getFormControlID(this._element.id, 'cmbProducts')).setValue("", 0);
        document.getElementById("lblihsproduct").style.color = '';
        document.getElementById('lblPackage').style.color = "";
        document.getElementById("lblihsproduct").style.color = '';
        document.getElementById("lblduty").style.color = '';
        $("#spnMSL").text("");
        $("#spnManufacturer").text("");
        $("#spnIHSProduct").text("");
        $("#spnPackage").text("");
        $("#spnHTSCode").text("");
        $("#spnIHSDutyCode").text("");
        $("#spnIHSCountryOfOrigin").text("");
        document.getElementById("lblduty").style.color = '';
        document.getElementById("lblpartstaus").style.color = '';
        document.getElementById("lblCoo").style.color = '';
        document.getElementById("lblihsHTSCode").style.color = '';

        document.getElementById("spnMSL").style.color = ''
        document.getElementById("spnIHSProduct").style.color = '';
        document.getElementById("spnPackage").style.color = ''
        document.getElementById("spnHTSCode").style.color = ''
        document.getElementById("spnIHSDutyCode").style.color = ''
        document.getElementById("spnIHSCountryOfOrigin").style.color = ''
        document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_LableECCNCode").style.color = '';
        document.getElementById("lblECCNCode").style.color = '';
        document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_LableLifeStatus").style.color = ''
        $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_LableECCNCode").text("");
        //lblCoo
        //lblihsHTSCode
        this._hidPackaging = "";
        this._hidPackagingSize = "";
        this._hidCountryOfOrigin = "";
        this._hidCountryOfOriginNo = ""
        this._hidLifeCycleStage = "";
        this._hidHTSCode = "";
        this._hidAveragePrice = "";
        this._hidDescriptions = "";
        this._IHSPartsId = "";
        this._ihsCurrencyCode = "";
        this._hidIHSProduct = "";
        //$("#lblihsproduct").hide();
        //$("#lblihsHTSCode").hide();
        //$("#lblduty").hide();
        //$("#lblCoo").hide();
        //$("#lblECCNCode").hide();
        this._hidECCNCode = "";
        $find(this.getFormControlID(this._element.id, 'cmbPackage')).setValue("", 0);
        //this.onSaveComplete();

    },
    //Part Eccn Mapping strat code
    SelectIHSPartEccnMappedFormLoad: function (IHSEccnCode, PartName) {
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/CusReqAdd");
        obj.set_DataObject("CusReqAdd");
        obj.set_DataAction("GetIHSPartEccnDetail");
        obj.addParameter("PartEccnCode", IHSEccnCode);
        obj.addParameter("PartNo", PartName);
        obj.addDataOK(Function.createDelegate(this, this.setIHSPartEccnDetailFormLoad));
        obj.addError(Function.createDelegate(this, this.IHSPartECCNDetailErrorFormLoad));
        obj.addTimeout(Function.createDelegate(this, this.IHSPartECCNDetailErrorFormLoad));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    setIHSPartEccnDetailFormLoad: function (args) {
        var obj = args._result.Result
        if (obj[0].PartEccnMappedId > 0) {
            //ECCN Code start
            if (obj[0].ECCNCode != 'undefined' && obj[0].ECCNCode != '' && obj[0].ECCNCode.length > 0) {

                $find(this.getFormControlID(this._element.id, 'cmbPartEccnMapped')).setValue(obj[0].ECCNNo, $R_FN.setCleanTextValue(obj[0].ECCNCode));
                this._hidECCNCode = obj[0].ECCNCode;

                $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_cmbPartEccnMappedaut").show();
                $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_cmbPartEccnMappedtxt").show();
                document.getElementById("lblECCNCode").style.color = 'yellow'

                if (obj[0].ECCNStatus) {
                    $('#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_cmbPartEccnMappedaut_ctl03').addClass('ihspartstatusdoc');
                    $('#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_cmbPartEccnMappedaut_ctl03').prop("title", obj[0].ECCNWarning.replace(/<br\s*\/?>/gi, ''));
                }
                else {
                    $('#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_cmbPartEccnMappedaut_ctl03').removeClass('ihspartstatusdoc');
                    $('#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_cmbPartEccnMappedaut_ctl03').prop("title", "");
                }
            }
        }
        else if (this._ECCNCode != '') {
            this.SelectIHSEccnCodeFormLoad(this._ECCNCode);
            $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_cmbPartEccnMappedaut").show();
            $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_cmbPartEccnMappedtxt").show();
        }
        else {
            // alert(3);
            $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_LableECCNCode").text(" ( " + "N/A" + " ) ");
            document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_LableECCNCode").style.color = 'yellow'
            document.getElementById("lblECCNCode").style.color = ''
            this._hidECCNCode = "";
            $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_cmbPartEccnMappedaut").show();
            $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_cmbPartEccnMappedtxt").show();
        }
    },

    IHSPartECCNDetailErrorFormLoad: function (args) {
    },
    //code end


    //Bind Eccn Code from TbECCN Table strat code
    SelectIHSEccnCodeFormLoad: function (sEccnCode) {
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/CusReqAdd");
        obj.set_DataObject("CusReqAdd");
        obj.set_DataAction("GetIHSEccnCodeDetail");
        obj.addParameter("ECCNCode", sEccnCode);
        obj.addDataOK(Function.createDelegate(this, this.setIHSEccnCodeDetailFormLoad));
        obj.addError(Function.createDelegate(this, this.IHSECCNCodeDetailErrorFormLoad));
        obj.addTimeout(Function.createDelegate(this, this.IHSECCNCodeDetailErrorFormLoad));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    setIHSEccnCodeDetailFormLoad: function (args) {
        var obj = args._result.Result
        if (obj[0].ECCNNo > 0) {
            //ECCN Code start
            if (obj[0].ECCNCode != '') {

                $find(this.getFormControlID(this._element.id, 'cmbPartEccnMapped')).setValue(obj[0].ECCNNo, $R_FN.setCleanTextValue(obj[0].ECCNCode));
                document.getElementById("lblECCNCode").style.color = 'yellow'

                this._hidECCNCode = obj[0].ECCNCode;
                $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_cmbPartEccnMappedaut").show();
                $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_cmbPartEccnMappedtxt").show();
                if (obj[0].ECCNStatus) {
                    $('#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_cmbPartEccnMappedaut_ctl03').addClass('ihspartstatusdoc');
                    $('#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_cmbPartEccnMappedaut_ctl03').prop("title", obj[0].ECCNWarning.replace(/<br\s*\/?>/gi, ''));
                }
                else {
                    $('#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_cmbPartEccnMappedaut_ctl03').removeClass('ihspartstatusdoc');
                    $('#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_cmbPartEccnMappedaut_ctl03').prop("title", "");
                }
            }
            else {
                $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_LableECCNCode").text(" ( " + "N/A" + " ) ");
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_LableECCNCode").style.color = 'yellow'
                document.getElementById("lblECCNCode").style.color = ''
                this._hidECCNCode = "";
                $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_cmbPartEccnMappedaut").show();
                $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_cmbPartEccnMappedtxt").show();

            }
        }
        else {
            //$find('ctl00_cphMain_ctlNotify_ctlDB_ctl14_ctlNotify_ctlDB_ctlSalespersion_ctl03_aut').reselect();
            $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_LableECCNCode").text(" ( " + "N/A" + " ) ");
            document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_LableECCNCode").style.color = 'yellow'
            document.getElementById("lblECCNCode").style.color = ''
            this._hidECCNCode = "";
            $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_cmbPartEccnMappedaut").show();
            $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_cmbPartEccnMappedtxt").show();
        }

    },
    IHSECCNCodeDetailErrorFormLoad: function (args) {
    },
    //code end
    ClearIhsDataOnLoad: function () {
        document.getElementById("lblMatchproduct").style.color = ''
        document.getElementById("lblparts").style.color = '';
        document.getElementById("lblmls").style.color = '';
        document.getElementById("lblmrf").style.color = '';
        document.getElementById('lblPackage').style.color = "";
        $("#spnMSL").text("");
        $("#spnManufacturer").text("");
        $("#spnIHSProduct").text("");
        $("#spnPackage").text("");

    },
    changeSearchType: function () {
        this.setSearchType(this._enmSearchType + 1);
    },

    setSearchType: function (i) {
        this._enmSearchType = i;
        if (this._enmSearchType > 2) this._enmSearchType = 0;
        this.showSearchType();
    },

    showSearchType: function () {
        if (this._enmSearchType == 0) {
            $("#ParttypeSearch1").removeClass("searchType_StartsWith");
            $("#ParttypeSearch1").removeClass("searchType_ExactWith");
            $("#ParttypeSearch1").addClass("searchType_Contains");
            $("#ParttypeSearch1").attr("title", "Contains");
            $("#ParttypeSearch1").attr("alt", "Contains");
            $find(this.getFormControlID(this._element.id, 'cmbIHS'))._aut._txtGroup = "contains";

        }
        if (this._enmSearchType == 1) {
            $("#ParttypeSearch1").removeClass("searchType_Contains");
            $("#ParttypeSearch1").removeClass("searchType_ExactWith");
            $("#ParttypeSearch1").addClass("searchType_StartsWith");
            $("#ParttypeSearch1").attr("title", "Startswith");
            $("#ParttypeSearch1").attr("alt", "Startswith");
            $find(this.getFormControlID(this._element.id, 'cmbIHS'))._aut._txtGroup = "startswith";

        }


        if (this._enmSearchType == 2) {
            $("#ParttypeSearch1").removeClass("searchType_StartsWith");
            $("#ParttypeSearch1").removeClass("searchType_Contains");
            $("#ParttypeSearch1").addClass("searchType_ExactWith");
            $("#ParttypeSearch1").attr("title", "Exact");
            $("#ParttypeSearch1").attr("alt", "Exact");
            $find(this.getFormControlID(this._element.id, 'cmbIHS'))._aut._txtGroup = "exact";

        }


    },
    ResetMFR: function () {
        $("#spanmfrEdit").text("");
    },
    RsMfrChanged: function () {
        this._intManufacturerNo = this.getControlValue(this.getFormControlID(this._element.id, 'cmbManufacturer'), 'Combo')
        this.getRsMfr(this._intManufacturerNo);

    },
    getRsMfr: function (RsManufacturerNo) {
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/CusReqAdd");
        obj.set_DataObject("CusReqAdd");
        obj.set_DataAction("GetRestrictedManufacturer");
        obj.addParameter("RsManufacturerNo", RsManufacturerNo);
        obj.addDataOK(Function.createDelegate(this, this.getRsMfrOK));
        obj.addError(Function.createDelegate(this, this.getRsMfrError));
        obj.addTimeout(Function.createDelegate(this, this.getRsMfrError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },
    getRsMfrOK: function (args) {
        var res = args._result;
        this._RestrictedMFRMessage = res.RestrictedMFRMessage;
        this._isRestrictedManufacturer = res.isRestrictedManufacturer;
        if (this._isRestrictedManufacturer > 0 && this._isRestrictedManufacturer != "undefined") {
            $("#lblRsMFREdit").show();
            $("#spanmfrEdit").text(this._RestrictedMFRMessage);

        } else { $("#lblRsMFREdit").hide(); }


    },
    getRsMfrError: function () {
        //this.setFieldValue($find(this.getFormControlID(this._element.id, 'cmbManufacturer')), "");
        // this.showShipViaFieldsLoading(false);
    },
    SearchTypePopup: function () {
        $('input:radio[name="searchType"][value="contains"]').prop('checked', true);
        $R_FN.showElement(this._pnlPartDetail, false);
        this._ctltblPartdetails._tblResults.clearTable();
        $("#myModal").show();
        var strPart = $R_FN.setCleanTextValue($get(this.getFormControlID(this._element.id, 'cmbIHS12txt')).value);
        $get(this.getFormControlID(this._element.id, 'SearchtxtPartNo')).value = strPart;
        //this.loadIHSGrid();
        this.Toggle1();

        document.getElementsByClassName("dataFilter")[1].style.display = "none";
        if (document.getElementsByClassName("itemSearchGo")[1])
            document.getElementsByClassName("itemSearchGo")[1].style.display = "none";

        // $R_FN.showElement(document.getElementById("ctl00_cphMain_ctlAdd_ctlDB_ctl14_ctlAdd_ctlDB_btnOK_hyp"), false);
        $R_FN.showElement(document.getElementById("okbtn"), false);
        $('.okbtn').hide();


    },
    loadIHSGrid: function () {
        $("#lblihserror").text("");
        this._ctltblPartdetails._tblResults.clearTable();
        this._ctltblPartdetails._tblResults.resizeColumns();
        this._ctltblPartdetails.showNoneFound(true);
        this._ctltblPartdetails.resetToFirstPage();
        $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_ctltblPartdetails_ctlDB_tblOuter").hide();
        $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_ctltblPartdetails_ctlDB_ctl03_hyp").hide();

        this._ctltblPartdetails.setFieldValue("ctlSearchtxtPartNo", $R_FN.setCleanTextValue($get(this.getFormControlID(this._element.id, 'SearchtxtPartNo')).value));
        this._ctltblPartdetails._strpart = $R_FN.setCleanTextValue($get(this.getFormControlID(this._element.id, 'SearchtxtPartNo')).value);
        this._ctltblPartdetails._searchType = $("input[name='searchType']:checked").val();
        this._ctltblPartdetails.getData();
        //$R_FN.showElement(document.getElementById("ctl00_cphMain_ctlAdd_ctlDB_ctl14_ctlAdd_ctlDB_btnOK_hyp"), true);
        //$('.okbtn').show();

    },
    Canceltop: function () {
        $("#myModal").hide();
        this.Toggle3();
    },
    Cancelbottom: function () {
        $("#myModal").hide();
        this.Toggle3();

    },
    hidpartdetaildive: function () {
        this.showDetailDiv(false);


    },

    Toggle3: function () {
        // this._tblPartdetails.clearTable();
        // this.showField("ctlPartDetail", false);
        //this.showDetailDiv(false);

    },
    dispose: function () {
        if (this.isDisposed) return;
        this._intCustomerRequirementID = null;
        this._arySources = null;
        this._radFactorySealedSource = null;
        this._radObsolete = null;
        this._radLastTimeBuy = null;
        this._radRefirbsAcceptable = null;
        this._radTestingRequired = null;
        this._blnCurInSameFaimly = true;
        this._intCurrencyNo = null;
        this._radRepeatBusiness = null;
        this._radAlternativesAccepted = null;
        //this._tblPartdetails = null;
        this._btn1 = null;
        this._btn2 = null;
        this._lblError = null;
        this._hidCountryOfOrigin = null;
        this._hidCountryOfOriginNo = null;
        this._hidLifeCycleStage = null;
        this._hidHTSCode = null;
        this._hidAveragePrice = null;
        this._hidPackaging = null;
        this._hidPackagingSize = null;
        this._hidDescriptions = null;
        this._pnlPartDetail = null;
        this._hidBOMID = null;
        this._IHSProductNo = null;
        this._IHSProduct = null;
        this._HTSCode = null;
        this._CountryOfOrigin = null;
        this._IHSDutyCode = null;
        this._AlternateStatus = null;
        this.chkAlternatives = null;
        this._ECCNCode = null;
        this._ctltblPartdetails = null;
        this._strpart = null;
        this._searchType = null;
        this._enmSearchType = 0;
        Rebound.GlobalTrader.Site.Controls.Forms.CustomerRequirementMainInfo_Edit.callBaseMethod(this, "dispose");
    },
    findWhichTypeSelected: function (radChk) {
        for (var i = 0; i < this._arySources.length; i++) {
            var rad = $get(String.format("{0}_{1}", radChk.id, i));

            if (rad.checked) {
                return this._arySources[i];
            }
            else if (i == this._arySources.length - 1) {
                return "false";
            }

        }
    },
    Toggle1: function () {
        $("#lblIhsServiceMessage").text("");
        $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_ctltblPartdetails_ctlDB_ctl08").hide();
        if ($("input[name='searchType']:checked").val() == "exact".trim()) {
            var strexact = $get(this.getFormControlID(this._element.id, 'SearchtxtPartNo')).value;
            if (strexact.length > 2) {
                $("#lblihserror").text("");
                $('#divLoader').show();
                $R_FN.showElement(this._pnlPartDetail, true);
                this.showProductLoading(true);
                var obj = new Rebound.GlobalTrader.Site.Data();
                obj.set_PathToData("controls/Nuggets/CusReqAdd");
                obj.set_DataObject("CusReqAdd");
                obj.set_DataAction("GetDataGrid");
                obj.addParameter("partsearch", $get(this.getFormControlID(this._element.id, 'SearchtxtPartNo')).value);
                obj.addParameter("searchType", $("input[name='searchType']:checked").val());
                obj.addDataOK(Function.createDelegate(this, this.getDataGrid));
                obj.addError(Function.createDelegate(this, this.getDataGridError));
                obj.addTimeout(Function.createDelegate(this, this.getDataGridError));
                $R_DQ.addToQueue(obj);
                $R_DQ.processQueue();
                obj = null;

            }
            else {
                $("#lblihserror").text("The search term must contain at least 3 alphanumeric characters. Examples are '2n222', 'F245', etc.");
                $R_FN.showElement(this._lblError, true);
                $R_FN.showElement(this._pnlPartDetail, false);
                this._ctltblPartdetails._tblResults.clearTable();
                this._ctltblPartdetails._tblResults.resizeColumns();
                this._ctltblPartdetails.showNoneFound(true);
                return;

            }
        }
        if ($("input[name='searchType']:checked").val() == "startswith".trim()) {
            var strexact = $get(this.getFormControlID(this._element.id, 'SearchtxtPartNo')).value;
            if (strexact.length > 2) {
                $("#lblihserror").text("");
                $('#divLoader').show();
                $R_FN.showElement(this._pnlPartDetail, true);
                this.showProductLoading(true);
                var obj = new Rebound.GlobalTrader.Site.Data();
                obj.set_PathToData("controls/Nuggets/CusReqAdd");
                obj.set_DataObject("CusReqAdd");
                obj.set_DataAction("GetDataGrid");
                obj.addParameter("partsearch", $get(this.getFormControlID(this._element.id, 'SearchtxtPartNo')).value);
                obj.addParameter("searchType", $("input[name='searchType']:checked").val());
                obj.addDataOK(Function.createDelegate(this, this.getDataGrid));
                obj.addError(Function.createDelegate(this, this.getDataGridError));
                obj.addTimeout(Function.createDelegate(this, this.getDataGridError));
                $R_DQ.addToQueue(obj);
                $R_DQ.processQueue();
                obj = null;

            }
            else {
                $("#lblihserror").text("The search term must contain at least 3 alphanumeric characters. Examples are '2n222', 'F245', etc.");
                $R_FN.showElement(this._lblError, true);
                $R_FN.showElement(this._pnlPartDetail, false);
                this._ctltblPartdetails._tblResults.clearTable();
                this._ctltblPartdetails._tblResults.resizeColumns();
                this._ctltblPartdetails.showNoneFound(true);
                return;

            }
        }
        if ($("input[name='searchType']:checked").val() == "contains".trim()) {
            var strexact = $get(this.getFormControlID(this._element.id, 'SearchtxtPartNo')).value;
            if (strexact.length > 3) {
                $("#lblihserror").text("");
                $('#divLoader').show();
                $R_FN.showElement(this._pnlPartDetail, true);
                this.showProductLoading(true);
                var obj = new Rebound.GlobalTrader.Site.Data();
                obj.set_PathToData("controls/Nuggets/CusReqAdd");
                obj.set_DataObject("CusReqAdd");
                obj.set_DataAction("GetDataGrid");
                obj.addParameter("partsearch", $get(this.getFormControlID(this._element.id, 'SearchtxtPartNo')).value);
                obj.addParameter("searchType", $("input[name='searchType']:checked").val());
                obj.addDataOK(Function.createDelegate(this, this.getDataGrid));
                obj.addError(Function.createDelegate(this, this.getDataGridError));
                obj.addTimeout(Function.createDelegate(this, this.getDataGridError));
                $R_DQ.addToQueue(obj);
                $R_DQ.processQueue();
                obj = null;

            }
            else {
                $("#lblihserror").text("The search term must contain at least 4 alphanumeric characters. Examples are '2n222', 'F245', etc.");
                $R_FN.showElement(this._lblError, true);
                $R_FN.showElement(this._pnlPartDetail, false);
                this._ctltblPartdetails._tblResults.clearTable();
                this._ctltblPartdetails._tblResults.resizeColumns();
                this._ctltblPartdetails.showNoneFound(true);
                return;

            }
        }

    },

    getIHSDataSelected: function () {
        $('.okbtn').show();
    },
    getValuesByPartsOnClick: function () {
        var PartNo = this._ctltblPartdetails._tblResults._varSelectedValue;
        if (PartNo.length > 0) {
            this.getValuesByParts();

        }
        // this.Canceltop();
    },
    getValuesByParts: function () {
        var obj = this._ctltblPartdetails._tblResults.getSelectedExtraData(); if (!obj) return;
        this.getIHSValuesByParts(obj.IHSPartsId, obj.ROHSName, obj.ROHSNo, obj.Manufacturer, obj.IHSProdDesc, obj.Packaging, obj.HTSCode, obj.IHSDutyCode, obj.CountryOfOrigin, obj.CountryOfOriginNo, obj.PackagingSize);

    },
    getIHSValuesByParts: function (IHSPartsId, MSLName, MSLNo, Manufacturer, IHSProdDesc, Packaging, HTSCode, IHSDutyCode, CountryOfOrigin, CountryOfOriginNo, PackagingSize) {
        $('#divLoader').show();
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/CusReqAdd");
        obj.set_DataObject("CusReqAdd");
        obj.set_DataAction("GetIHSPartDetails");
        obj.addParameter("IHSPartsId", IHSPartsId);
        obj.addParameter("MSLName", MSLName);
        obj.addParameter("MSLNo", MSLNo);
        obj.addParameter("Manufacturer", Manufacturer);
        obj.addParameter("IHSProdDesc", IHSProdDesc);
        obj.addParameter("Packaging", Packaging);
        obj.addParameter("HTSCode", HTSCode);
        obj.addParameter("IHSDutyCode", IHSDutyCode);
        obj.addParameter("CountryOfOrigin", CountryOfOrigin);
        obj.addParameter("CountryOfOriginNo", CountryOfOriginNo);
        obj.addParameter("PackagingSize", PackagingSize);
        obj.addDataOK(Function.createDelegate(this, this.getIHSDataGrid));
        obj.addError(Function.createDelegate(this, this.getIHSDataError));
        obj.addTimeout(Function.createDelegate(this, this.getIHSDataGridError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },
    getIHSDataError: function (args) {
        $('#divLoader').hide();
    },
    getIHSDataGrid: function (args) {
        var obj = args._result.Result
        if (obj[0].IHSPartsId > 0) {

            $("#lblihsproduct").show();
            $("#lblihsHTSCode").show();
            $("#lblduty").show();
            $("#lblCoo").show();

            var PartName = obj[0].ID;
            if (PartName != 'undefined ' && PartName.length > 0) {
                // $get(this.getFormControlID(this._element.id, 'txtPartNo')).value = $R_FN.setCleanTextValue(PartName);
                $get(this.getFormControlID(this._element.id, 'cmbIHS12txt')).value = $R_FN.setCleanTextValue(PartName);
                document.getElementById("lblparts").style.color = 'yellow'
            }
            else {
                document.getElementById("lblparts").style.color = ''
            }
            if (obj[0].ROHSName != 'undefined ' && obj[0].ROHSName != '' && obj[0].ROHSName.length > 0) {
                // $("#spnMSL").show();
                $("#spnMSL").text(" ( " + obj[0].ROHSName + " ) ");
                $find(this.getFormControlID(this._element.id, 'ddlMsl')).setValue(obj[0].ROHSName);
                document.getElementById("lblmls").style.color = 'yellow'
                if (obj[0].ROHSName == 'N/A')
                    document.getElementById("spnMSL").style.color = 'yellow';
                else
                    document.getElementById("spnMSL").style.color = '';

            }
            else {

                $("#spnMSL").text(" ( " + "N/A" + " ) ");
                document.getElementById("lblmls").style.color = 'yellow'
                document.getElementById("spnMSL").style.color = 'yellow'
                $find(this.getFormControlID(this._element.id, 'ddlMsl')).setValue("N/A");


            }


            if (obj[0].Manufacturer != 'undefined ' && obj[0].Manufacturer != '' && obj[0].Manufacturer.length > 0) {
                if (obj[0].ManufacturerNo != 0) {
                    $find(this.getFormControlID(this._element.id, 'cmbManufacturer')).setValue(obj[0].ManufacturerNo, $R_FN.setCleanTextValue(obj[0].Manufacturer));
                    $("#spnManufacturer").hide();
                    document.getElementById("lblmrf").style.color = 'yellow'

                }
                else {

                    $find(this.getFormControlID(this._element.id, 'cmbManufacturer')).setValue("", 0);
                    $("#spnManufacturer").show();
                    $("#spnManufacturer").text(" ( " + obj[0].ManufacturerFullName + " ) ");
                    document.getElementById("lblmrf").style.color = ''
                }

            }
            else {

                $("#spnManufacturer").hide();

            }

            if (obj[0].ProdDesc != 'undefined ' && obj[0].IHSProdDesc.length > 0) {

                if (obj[0].ProdNo != 0) {
                    $find(this.getFormControlID(this._element.id, 'cmbProducts')).setValue(obj[0].ProdNo, $R_FN.setCleanTextValue(obj[0].ProdDesc));
                    // $("#spnManufacturer").hide();
                    document.getElementById("lblMatchproduct").style.color = 'yellow'
                    $("#spnIHSProduct").text($R_FN.setCleanTextValue(obj[0].IHSProdDesc));
                    document.getElementById("lblihsproduct").style.color = 'yellow'
                }
                else {
                    $find(this.getFormControlID(this._element.id, 'cmbProducts')).setValue("", 0);
                    $("#spnIHSProduct").show();
                    $("#spnIHSProduct").text($R_FN.setCleanTextValue(obj[0].IHSProdDesc));
                    document.getElementById("lblihsproduct").style.color = 'yellow'
                }

            }
            else {
                if (obj[0].HTSCode != '' || obj[0].IHSProdDes != '') {
                    $("#spnIHSProduct").show();
                    $("#spnIHSProduct").text(" ( " + "N/A" + " ) ");
                    document.getElementById("spnIHSProduct").style.color = 'yellow'
                    document.getElementById("lblihsproduct").style.color = 'yellow'
                } else {
                    document.getElementById("lblihsproduct").style.color = ''
                    document.getElementById("spnIHSProduct").style.color = ''
                }
            }

            //if (obj[0].PackageId > 0) {

            //    //$("#spnPackaging").show();
            //    //$("#spnPackaging").text(" (" + obj[0].Packaging + " ) ");
            //   // $find(this.getFormControlID(this._element.id, 'ddlPackage')).setValue(obj[0].PackageId);
            //    //document.getElementById("lblpackage").style.color = 'yellow'
            //    $("#spnPackaging").text(" ( " + obj[0].Packaging + " ) ");
            //   // document.getElementById("spnPackaging").style.color = ''
            //}
            //else {
            //    // $("#spnPackaging").hide();
            //      $("#spnPackaging").text(" ( " + "N/A" + " ) ");
            //     // document.getElementById("spnPackaging").style.color = 'yellow'
            //   // document.getElementById("lblpackage").style.color = ''
            //    //$find(this.getFormControlID(this._element.id, 'ddlPackage')).setValue(0);

            //}

            if (obj[0].PackageId > 0) {
                $("#lblECCNCode").show();
                $("#spnPackage").text(" ( " + obj[0].Packaging + " ) ");
                document.getElementById('lblPackage').style.color = "yellow";
                document.getElementById("spnPackage").style.color = "";
                $find(this.getFormControlID(this._element.id, 'cmbPackage')).setValue(obj[0].PackageId, $R_FN.setCleanTextValue(obj[0].PackageDescription));

            }
            else {
                $("#lblECCNCode").show();
                $("#spnPackage").text(" ( " + "N/A" + " ) ");
                document.getElementById('lblPackage').style.color = "";
                document.getElementById("spnPackage").style.color = 'yellow'
                $find(this.getFormControlID(this._element.id, 'cmbPackage')).setValue("", 0);

            }


            if (obj[0].HTSCode != 'undefined ' && obj[0].HTSCode != '' && obj[0].HTSCode.length > 0) {
                $("#spnHTSCode").show();
                $("#spnHTSCode").text(obj[0].HTSCode);
                document.getElementById("lblihsHTSCode").style.color = 'yellow'
                document.getElementById("spnHTSCode").style.color = ''
            }
            else {
                if (obj[0].HTSCode != '' || obj[0].IHSProdDes != '') {
                    $("#spnHTSCode").show();
                    $("#spnHTSCode").text(" ( " + "N/A" + " ) ");
                    document.getElementById("lblihsHTSCode").style.color = 'yellow'
                    document.getElementById("spnHTSCode").style.color = 'yellow'
                } else {
                    document.getElementById("lblihsHTSCode").style.color = ''
                    document.getElementById("spnHTSCode").style.color = ''
                }
            }

            //if (obj.ProdDesc != 'undefined ' && obj.IHSProdDesc.length > 0) {
            //    $("#spnIHSProduct").show();
            //    //$("#spnIHSProduct").text("(" + obj.ProdDesc + " ) ");
            //    $("#spnIHSProduct").text(obj.IHSProdDesc);
            //}
            //else {
            //    $find(this.getFormControlID(this._element.id, 'cmbProducts')).setValue(obj.ProdNo, obj.ProdDesc);
            //    $("#spnIHSProduct").hide();
            //}
            if (obj[0].IHSDutyCode != 'undefined ' && obj[0].IHSDutyCode != '' && obj[0].IHSDutyCode.length > 0) {
                $("#spnIHSDutyCode").show();
                $("#spnIHSDutyCode").text(" ( " + obj[0].IHSDutyCode + " ) ");
                document.getElementById("lblduty").style.color = 'yellow'
                document.getElementById("spnIHSDutyCode").style.color = ''

            }
            else {

                $("#spnIHSDutyCode").text("( N/A )")
                document.getElementById("spnIHSDutyCode").style.color = 'yellow'
                document.getElementById("lblduty").style.color = ''
            }
            if (obj[0].CountryOfOrigin != 'undefined ' && obj[0].CountryOfOrigin != '' && obj[0].CountryOfOrigin.length > 0) {
                $("#spnIHSCountryOfOrigin").show();
                $("#spnIHSCountryOfOrigin").text(obj[0].CountryOfOrigin);
                document.getElementById("lblCoo").style.color = 'yellow'
            }
            else {

                $("#spnIHSCountryOfOrigin").text(" ( " + "N/A" + " ) ");
                document.getElementById("lblCoo").style.color = ''
                document.getElementById("spnIHSCountryOfOrigin").style.color = 'yellow'
            }

            if (obj[0].PartStatus != 'undefined ' && obj[0].PartStatus != '' && obj[0].PartStatus.length > 0) {
                $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_LableLifeStatus").text($R_FN.setCleanTextValue(obj[0].PartStatus));
                document.getElementById("lblpartstaus").style.color = 'yellow'
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_LableLifeStatus").style.color = ''
            }
            else {
                $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_LableLifeStatus").text(" ( " + "N/A" + " ) ");
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_LableLifeStatus").style.color = 'yellow'
                document.getElementById("lblpartstaus").style.color = ''
            }
            //ECCN Code start
            if (obj[0].ECCNCode != 'undefined ' && obj[0].ECCNCode != '' && obj[0].ECCNCode.length > 0) {
                //$("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_LableECCNCode").text($R_FN.setCleanTextValue(obj[0].ECCNCode));
                this.SelectIHSPartEccnMapped(obj[0].ECCNCode, PartName);
                document.getElementById("lblECCNCode").style.color = 'yellow'
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_LableECCNCode").style.color = ''
            }
            else {
                $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_LableECCNCode").text(" ( " + "N/A" + " ) ");
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_LableECCNCode").style.color = 'yellow'
                document.getElementById("lblECCNCode").style.color = ''
                //$find('ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_cmbPartEccnMappedaut').reselect();
                this.myBlurFunction();
            }
            //ECCN Code End

            if (obj[0].Packaging != 'undefined ' && obj[0].Packaging != '') {
                this._hidPackaging = obj[0].Packaging;
            }
            else {
                this._hidPackaging = "";
            }
            if (obj[0].PackagingSize != 'undefined ' && obj[0].PackagingSize != '') {
                this._hidPackagingSize = obj[0].PackagingSize;
            }
            else {
                this._hidPackagingSize = "";
            }


            this._hidCountryOfOrigin = obj[0].CountryOfOrigin;
            this._hidCountryOfOriginNo = obj[0].CountryOfOriginNo;
            this._hidLifeCycleStage = obj[0].PartStatus;
            this._hidHTSCode = obj[0].HTSCode;
            this._hidAveragePrice = obj[0].AveragePrice;
            this._hidDescriptions = obj[0].Descriptions;
            this._IHSPartsId = obj[0].IHSPartsId;
            this._ihsCurrencyCode = obj[0].ihsCurrencyCode;
            this._hidIHSProduct = obj[0].IHSProduct;
            this._hidECCNCode = obj[0].ECCNCode;

        }
        $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_cmbIHS12aut_hypClose")[0].click();
        $('#divLoader').hide();
        $("#myModal").hide();
    },
    getIHSDataGridError: function (args) {
        $('#divLoader').hide();
        $("#myModal").hide();
    },
    getDataGridError: function (args) {
        $('#divLoader').hide();
        $("#myModal").hide();
    },
    getDataGrid: function (args) {

        $('#divLoader').hide();
        var ihsmsg = args._result.ServiceStatus;
        if (ihsmsg == false) {
            $("#lblIhsServiceMessage").text("Sorry, the IHS part lookup service is not currently available.");
            $R_FN.showElement(this._lblError, true);
            $R_FN.showElement(this._pnlPartDetail, false);
            this._ctltblPartdetails._tblResults.clearTable();
            this._ctltblPartdetails._tblResults.resizeColumns();
        }
        else {
            if (args._result.Result) {
                this.loadIHSGrid();
                $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_ctltblPartdetails_ctlDB_ctl08").show();
            }
            else {
                this.loadIHSGrid();
            }
        }
    },
    Toggle2: function () {
        $("#lblIhsServiceMessage").text("");
        $("#lblservicemsgerror").text("");
        $('input:radio[name="searchType"][value="contains"]').prop('checked', true);
        $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_ctltblPartdetails_ctlDB_ctl08").hide();
        $get(this.getFormControlID(this._element.id, 'SearchtxtPartNo')).value = "";
        $("#lblihserror").text("");
        $R_FN.showElement(this._pnlPartDetail, false);
        this._ctltblPartdetails._tblResults.clearTable();
        this._ctltblPartdetails._tblResults.resizeColumns();
        this._ctltblPartdetails.showNoneFound(true);

    },
    getParSearch: function () {

    },

    getPartDetaildata: function () {
        //if (this._ctlMultiStep._intCurrentStep == 2) {
        if (this._PartEditStatus == 1) {
            if ($get(this.getFormControlID(this._element.id, 'cmbIHS12txt')).value.length > 0) {
                this.getPartDetail($get(this.getFormControlID(this._element.id, 'cmbIHS12txt')).value);
                this.showDetailDiv(true);
            }
            else {
                if (!this.validateForm2()) return;
                this.showDetailDiv(false);
            }
        }
        else {
            if ($("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_lblPartNo").text().length > 0) {
                this.getPartDetail($("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_lblPartNo").text());
                this.showDetailDiv(true);
            }
            else {
                if (!this.validateForm2()) return;
                this.showDetailDiv(false);
            }
        }
        //}

    },
    validateForm2: function () {
        this.onValidate();
        var blnOK = true;
        //if (!this.checkControlAddEntered(this.getFormControlID(this._element.id, 'txtPartNo'), 'TextBox')) blnOK = false;
        if (this._PartEditStatus == 1) {
            if (!this.checkControlEditEntered(this.getFormControlID(this._element.id, 'cmbIHS12txt'), 'TextBox')) blnOK = false;
        }
        //if (!this.checkControlAddEntered(this.getFormControlID(this._element.id, 'cmbIHS'), 'Combo')) blnOK = false;
        if (!blnOK) this.showError(true);
        return blnOK;
    },
    showDetailDiv: function (visible) {
        if (visible) {
            $("#mydiv").show();
            $("#tbpartdet").show();
        }
        else {
            $("#mydiv").hide();
            $("#tbpartdet").hide();
        }
    },
    saveClicked: function () {
        if (!this.validateForm()) return;
        if (this._isRestrictedManufacturer > 0 && this._isRestrictedManufacturer != "undefined") {
            this.showError(true);
        }
        else {
            var obj = new Rebound.GlobalTrader.Site.Data();
            //chkAlternatives = ($("#chkAlternatives").is(':checked')) ? 'true' : 'false';
            //this._strSourceSelected = this.findWhichTypeSelected(this._radFactorySealedSource);
            //this._RadChecked = this._strSourceSelected == "YES" ? true : false;
            //this._strSourceSelected = this.findWhichTypeSelected(this._radObsolete);
            //this._radObsoleteChk = this._strSourceSelected == "YES" ? true : false;

            //this._strSourceSelected = this.findWhichTypeSelected(this._radLastTimeBuy);
            //this._radLastTimeBuyChk = this._strSourceSelected == "YES" ? true : false;

            //this._strSourceSelected = this.findWhichTypeSelected(this._radRefirbsAcceptable);
            //this._radRefirbsAcceptableChk = this._strSourceSelected == "YES" ? true : false;
            //this._strSourceSelected = this.findWhichTypeSelected(this._radTestingRequired);
            //this._radTestingRequiredChk = this._strSourceSelected == "YES" ? true : false;

            //ihs changes start
            obj.set_PathToData("controls/Nuggets/CusReqMainInfo");
            obj.set_DataObject("CusReqMainInfo");
            obj.set_DataAction("SaveEdit");
            obj.addParameter("id", this._intCustomerRequirementID);
            obj.addParameter("Quantity", $get(this.getFormControlID(this._element.id, 'txtQuantity')).value);
            // obj.addParameter("PartNo", $get(this.getFormControlID(this._element.id, 'txtPartNo')).value);
            if (this._PartEditStatus == 1) {
                obj.addParameter("PartNo", $get(this.getFormControlID(this._element.id, 'cmbIHS12txt')).value);
            }
            else {
                obj.addParameter("PartNo", $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_lblPartNo").text());

            }
            //debugger;
            obj.addParameter("CustomerPartNo", $get(this.getFormControlID(this._element.id, 'txtCustomerPartNo')).value);
            obj.addParameter("Manufacturer", this.getControlValue(this.getFormControlID(this._element.id, 'cmbManufacturer'), 'Combo'));
            obj.addParameter("DateCode", $get(this.getFormControlID(this._element.id, 'txtDateCode')).value);
            obj.addParameter("Product", this.getControlValue(this.getFormControlID(this._element.id, 'cmbProducts'), 'Combo'));
            obj.addParameter("Package", this.getControlValue(this.getFormControlID(this._element.id, 'cmbPackage'), 'Combo'));
            //obj.addParameter("Package", $find(this.getFormControlID(this._element.id, 'ddlPackage')).getValue());
            obj.addParameter("TargetPrice", $get(this.getFormControlID(this._element.id, 'txtTargetPrice')).value);
            obj.addParameter("DateRequired", $get(this.getFormControlID(this._element.id, 'txtDateRequired')).value);
            obj.addParameter("Usage", $find(this.getFormControlID(this._element.id, 'ddlUsage')).getValue());
            obj.addParameter("Notes", $get(this.getFormControlID(this._element.id, 'txtNotes')).value);
            obj.addParameter("Instructions", $get(this.getFormControlID(this._element.id, 'txtInstructions')).value);
            obj.addParameter("ROHS", $find(this.getFormControlID(this._element.id, 'ddlROHS')).getValue());
            obj.addParameter("PartWatch", this.getControlValue(this.getFormControlID(this._element.id, 'chkPartWatch'), 'CheckBox'));
            obj.addParameter("BOM", this.getControlValue(this.getFormControlID(this._element.id, 'chkBOM'), 'CheckBox'));
            obj.addParameter("BOMName", $get(this.getFormControlID(this._element.id, 'txtBOMName')).value);
            //obj.addParameter("BOMNo", this._BOMHeaderDisplayStatus == false ? this._intBOMID : this._ctlBOMHeader);
            obj.addParameter("BOMNo", this._BOMHeaderDisplayStatus == false ? this._intBOMID : this._hidBOMID);
            obj.addParameter("FactorySealed", this.getControlValue(this.getFormControlID(this._element.id, 'chkFactorySealedSource'), 'CheckBox'));//this._RadChecked);
            //  obj.addParameter("MSL", this.getFieldValue("ctlMsl"));
            //[003] code start
            obj.addParameter("MSL", $find(this.getFormControlID(this._element.id, 'ddlMsl')).getValue());
            //[003] code end

            obj.addParameter("BOMID", this._intBOMID);
            obj.addParameter("PQA", this.getControlValue(this.getFormControlID(this._element.id, 'chkPQA'), 'CheckBox'));
            // obj.addParameter("PQA", $get(this.getFormControlID(this._element.id, 'chkPQA')).value);
            obj.addParameter("ObsoleteChk", this.getControlValue(this.getFormControlID(this._element.id, 'chkObsolete'), 'CheckBox'));//this._radObsoleteChk);
            obj.addParameter("LastTimeBuyChk", this.getControlValue(this.getFormControlID(this._element.id, 'chkLastTimeBuy'), 'CheckBox'));//this._radLastTimeBuyChk);
            obj.addParameter("RefirbsAcceptableChk", this.getControlValue(this.getFormControlID(this._element.id, 'chkRefirbsAcceptable'), 'CheckBox'));//this._radRefirbsAcceptableChk);
            obj.addParameter("TestingRequiredChk", this.getControlValue(this.getFormControlID(this._element.id, 'chkTestingRequired'), 'CheckBox'));//this._radTestingRequiredChk);
            obj.addParameter("TargetSellPrice", $get(this.getFormControlID(this._element.id, 'txtTargetSellPrice')).value);
            obj.addParameter("CompetitorBestOffer", $get(this.getFormControlID(this._element.id, 'txtCompetitorBestoffer')).value);
            obj.addParameter("CustomerDecisionDate", $get(this.getFormControlID(this._element.id, 'txtCustomerDecisionDate')).value);
            obj.addParameter("RFQClosingDate", $get(this.getFormControlID(this._element.id, 'txtRFQClosingDate')).value);
            obj.addParameter("QuoteValidityRequired", $find(this.getFormControlID(this._element.id, 'ddlQuoteValidityRequired')).getValue());
            obj.addParameter("Type", $find(this.getFormControlID(this._element.id, 'ddlType')).getValue());
            obj.addParameter("OrderToPlace", this.getControlValue(this.getFormControlID(this._element.id, 'chkOrderToPlace'), 'CheckBox'));
            obj.addParameter("RequirementforTraceability", $find(this.getFormControlID(this._element.id, 'ddlRequirementforTraceability')).getValue());
            obj.addParameter("EAU", $get(this.getFormControlID(this._element.id, 'txtEau')).value);
            obj.addParameter("AlternativesAccepted", this.getControlValue(this.getFormControlID(this._element.id, 'chkAlternativesAccepted'), 'CheckBox')); //this.getRadioSeclectedValue(this._radAlternativesAccepted));
            obj.addParameter("RepeatBusiness", this.getControlValue(this.getFormControlID(this._element.id, 'chkRepeatBusiness'), 'CheckBox'));//this.getRadioSeclectedValue(this._radRepeatBusiness));
            obj.addParameter("SalesPersion", this.getControlValue(this.getFormControlID(this._element.id, 'cmbSalespersion'), 'Combo'));

            if (this._blnCurInSameFaimly == false) {
                obj.addParameter("Currency", this._intCurrencyNo);
            }
            else {
                obj.addParameter("Currency", $find(this.getFormControlID(this._element.id, 'ddlCurrency')).getValue());//this.getFieldValue("ctlCurrency"));
            }
            obj.addParameter("SalesmanNo", $find(this.getFormControlID(this._element.id, 'ddlSalesman')).getValue());
            obj.addParameter("Alternatives", this.getControlValue(this.getFormControlID(this._element.id, 'chkAlternatives'), 'CheckBox'));
            //ihs code end
            //ihs part edit code start
            obj.addParameter("CountryOfOrigin", this._hidCountryOfOrigin);
            obj.addParameter("CountryOfOriginNo", this._hidCountryOfOriginNo);
            obj.addParameter("LifeCycleStage", this._hidLifeCycleStage);
            obj.addParameter("HTSCode", this._hidHTSCode);
            obj.addParameter("AveragePrice", this._hidAveragePrice);
            obj.addParameter("Packaging", this._hidPackaging);
            obj.addParameter("PackagingSize", this._hidPackagingSize);
            obj.addParameter("Descriptions", this._hidDescriptions);
            obj.addParameter("IHSPartsId", this._IHSPartsId);
            obj.addParameter("ihsCurrencyCode", this._ihsCurrencyCode);
            obj.addParameter("IHSProduct", this._hidIHSProduct);
            //obj.addParameter("ECCNCode", this._hidECCNCode);
            obj.addParameter("ECCNCode", $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_cmbPartEccnMappedaut_ctl03").text());
            obj.addParameter("SalesmanName", $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_ddlSalesman_ddl option:selected").text());
            obj.addParameter("ECCNNo", this.getControlValue(this.getFormControlID(this._element.id, 'cmbPartEccnMapped'), 'Combo'));
            //code end

            //[006] AS6081 start
            //obj.addParameter("AS6081", this._ctlAS6081);
            debugger;
            obj.addParameter("AS6081", this.ConvertAS6081ToBool());
            
            //[006] end
            obj.addDataOK(Function.createDelegate(this, this.saveEditComplete));
            obj.addError(Function.createDelegate(this, this.saveEditError));
            obj.addTimeout(Function.createDelegate(this, this.saveEditError));
            $R_DQ.addToQueue(obj);
            $R_DQ.processQueue();
            obj = null;
        }
    },

    saveEditError: function (args) {
        this._strErrorMessage = args._errorMessage;
        this.onSaveError();
    },

    saveEditComplete: function (args) {
        if (args._result.Result == true) {
            this.onSaveComplete();
        }
        ////        else if (args._result.Result == false && args._result.Refresh == true) {
        ////        this._strErrorMessage = "Please referesh the page";
        ////        this.onSaveError();
        ////        }
        else {
            this._strErrorMessage = args._errorMessage;
            this.onSaveError();
        }
    },

    getRadioSeclectedValue: function (radchk) {
        var value = false;
        var rad = $get(String.format("{0}_{1}", radchk.id, 0));
        //var rad1 = $get(String.format("{0}_{1}", radchk.id, 1));
        if (rad.checked) {
            value = true;
        }
        return value;

    },


    validateForm: function () {
        this.onValidate();
        var strQuantity = "";
        //var blnOK = this.autoValidateFields();
        var blnOK = true;
        //when edit part code start
        if (this._PartEditStatus == 1) {
            if (!this.checkControlEditEntered(this.getFormControlID(this._element.id, 'cmbIHS12txt'), 'TextBox')) blnOK = false;
        }
        //code end
        if (!this.checkControlEditEntered(this.getFormControlID(this._element.id, 'txtQuantity'), 'TextBox')) blnOK = false;
        if (!this.checkControlEditEntered(this.getFormControlID(this._element.id, 'cmbManufacturer'), 'Combo')) blnOK = false;
        if (!this.checkControlEditEntered(this.getFormControlID(this._element.id, 'cmbManufacturertxt'), 'TextBox')) blnOK = false;
        //if (!this.checkControlEntered(this.getFormControlID(this._element.id, 'txtPartNo'), 'TextBox')) blnOK = false;
        if (!this.checkControlEditEntered(this.getFormControlID(this._element.id, 'cmbProducts'), 'Combo')) blnOK = false;
        if (!this.checkControlEditEntered(this.getFormControlID(this._element.id, 'cmbProductstxt'), 'TextBox')) blnOK = false;
        //if (!this.checkControlEditEntered(this.getFormControlID(this._element.id, 'cmbPackage'), 'Combo')) blnOK = false;
        //if (!this.checkControlEditEntered(this.getFormControlID(this._element.id, 'cmbPackagetxt'), 'TextBox')) blnOK = false;


        // if (!this.checkFieldEntered("ctlPartNo")) blnOK = false;
        // if (!this.checkFieldEntered("ctlManufacturer")) blnOK = false;
        //if (!this.checkFieldEntered("ctlProduct")) blnOK = false; // TO make product mandatory on Edit (Suhail)
        if (this._blnCurInSameFaimly == false) {
            if (this._intCurrencyNo <= 0) blnOK = false;
        }
        else {
            // if (!this.checkFieldEntered("ctlCurrency")) blnOK = false;
            if (!this.checkControlEditEntered(this.getFormControlID(this._element.id, 'ddlCurrency'), 'DropDown')) blnOK = false;
        }

        if (!this.checkControlEditEntered(this.getFormControlID(this._element.id, 'txtDateRequired'), 'TextBox')) blnOK = false;
        if (!this.checkControlEditEntered(this.getFormControlID(this._element.id, 'ddlRequirementforTraceability'), 'DropDown')) blnOK = false;
        if (!this.checkControlEditEntered(this.getFormControlID(this._element.id, 'ddlSalesman'), 'DropDown')) blnOK = false;
        // if (!this.checkFieldEntered("ctlDateRequired")) blnOK = false;
        // if (!this.checkFieldEntered("ctlRequirementforTraceability")) blnOK = false;
        // if (!this.checkFieldEntered("ctlSalesman")) blnOK = false;

        //  [003] code start
        //if (!this.checkFieldEntered("ctlMsl")) blnOK = false;
        //  [003] code start
        //alert(blnOK);

        //[004] start
        //if (!this.checkFieldEntered("ctlType")) blnOK = false;
        if (!this.checkControlEditEntered(this.getFormControlID(this._element.id, 'ddlType'), 'DropDown')) blnOK = false;
        //[004] end

        //[006] start (validation for AS6081)

        switch ($find(this.getFormControlID(this._element.id, 'ddlAS6081')).getValue()) {
            
            case "1":
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_AS6081Error").style.backgroundColor = "";
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_AS6081Error1").style.backgroundColor = "";

                break;
            case "2":
                console.log($find(this.getFormControlID(this._element.id, 'ddlAS6081')).getValue());
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_AS6081Error").style.backgroundColor = "";
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_AS6081Error1").style.backgroundColor = "";
                break;
            default:
                console.log($find(this.getFormControlID(this._element.id, 'ddlAS6081')).getValue());
                blnOK = false;
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_AS6081Error").style.backgroundColor = "#990000";
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_AS6081Error1").style.backgroundColor = "#990000";
                break;
        }
            //[006] end
        if (!blnOK) this.showError(true);
        return blnOK;
    },

    //for Edit req validation
    checkControlEditEntered: function (strControlID, strControlType) {
        var blnEntered = true;
        switch (strControlType) {
            case "TextBox": blnEntered = $R_FN.isEntered($get(strControlID).value); break;
            case "DropDown": blnEntered = !$find(strControlID).isSetAsNoValue(); break;
            case "FileUpload": blnEntered = $find(strControlID).checkEntered(); break;
            case "Combo": blnEntered = $find(strControlID).checkEntered(); break;
        }
        if (!blnEntered) {
            this.setControleditInError(strControlID, true, $R_RES.RequiredFieldMissingMessage);
        }
        else {
            document.getElementById(strControlID).style.border = '';
            if (this._PartEditStatus == 1) {
                if (strControlID == "ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_cmbIHS12txt" && blnEntered == true) {
                    document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_parterror").style.backgroundColor = "";
                    document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_parterror1").style.backgroundColor = "";
                }
            }
            if (strControlID == "ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_txtQuantity" && blnEntered == true) {
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_QuantityError").style.backgroundColor = "";
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_QuantityError1").style.backgroundColor = "";
            }
            if (strControlID == "ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_txtTargetPrice" && blnEntered == true) {
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_CustomerTargetPriceError").style.backgroundColor = "";
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_CustomerTargetPriceError1").style.backgroundColor = "";
            }
            if (strControlID == "ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_cmbManufacturer" && blnEntered == true) {
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_ManufacturerError").style.backgroundColor = "";
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_ManufacturerError1").style.backgroundColor = "";
            }
            if (strControlID == "ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_cmbManufacturertxt" && blnEntered == true) {
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_ManufacturerError").style.backgroundColor = "";
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_ManufacturerError1").style.backgroundColor = "";
            }
            if (strControlID == "ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_cmbProducts" && blnEntered == true) {
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_ProductError").style.backgroundColor = "";
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_ProductError1").style.backgroundColor = "";
            }
            if (strControlID == "ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_cmbProductstxt" && blnEntered == true) {
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_ProductError").style.backgroundColor = "";
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_ProductError1").style.backgroundColor = "";
            }
            if (strControlID == "ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_ddlRequirementforTraceability" && blnEntered == true) {
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_TraceabilityError").style.backgroundColor = "";
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_TraceabilityError1").style.backgroundColor = "";
            }
            if (strControlID == "ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_ddlType" && blnEntered == true) {
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_ddlTypeError").style.backgroundColor = "";
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_ddlTypeError1").style.backgroundColor = "";
            }

            if (strControlID == "ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_ddlSalesman" && blnEntered == true) {
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_SalespersonError").style.backgroundColor = "";
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_SalespersonError1").style.backgroundColor = "";
            }

            if (strControlID == "ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_ddlCurrency" && blnEntered == true) {
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_CurrencyError").style.backgroundColor = "";
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_CurrencyError1").style.backgroundColor = "";
            }

            if (strControlID == "ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_txtDateRequired" && blnEntered == true) {
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_DateRequiredError").style.backgroundColor = "";
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_DateRequiredError1").style.backgroundColor = "";
            }
            //if (strControlID == "ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_cmbPackage" && blnEntered == true) {
            //    document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_PackageError").style.backgroundColor = "";
            //    document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_PackageError1").style.backgroundColor = "";
            //}
            //if (strControlID == "ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_cmbPackagetxt" && blnEntered == true) {
            //    document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_PackageError").style.backgroundColor = "";
            //    document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_PackageError1").style.backgroundColor = "";
            //}

        }

        return blnEntered;
    },
    setControleditInError: function (strControlID, blnInError, strMessage) {
        if (blnInError) {

            document.getElementById(strControlID).focus();
            // document.getElementById(strControlID).style.borderColor = "red";
            // document.getElementById(strControlID).style.border = '2px solid red';
            //alert(strControlID + " " + blnInError + " " + strMessage);
            if (this._PartEditStatus == 1) {
                if (strControlID == "ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_cmbIHS12txt" && blnInError == true) {
                    document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_parterror").style.backgroundColor = "#990000";
                    document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_parterror1").style.backgroundColor = "#990000";
                }
            }
            if (strControlID == "ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_txtQuantity" && blnInError == true) {
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_QuantityError").style.backgroundColor = "#990000";
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_QuantityError1").style.backgroundColor = "#990000";
            }
            if (strControlID == "ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_txtTargetPrice" && blnInError == true) {
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_CustomerTargetPriceError").style.backgroundColor = "#990000";
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_CustomerTargetPriceError1").style.backgroundColor = "#990000";
            }
            if (strControlID == "ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_cmbManufacturer" && blnInError == true) {
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_ManufacturerError").style.backgroundColor = "#990000";
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_ManufacturerError1").style.backgroundColor = "#990000";
            }
            if (strControlID == "ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_cmbManufacturertxt" && blnInError == true) {
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_ManufacturerError").style.backgroundColor = "#990000";
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_ManufacturerError1").style.backgroundColor = "#990000";
            }
            if (strControlID == "ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_cmbProducts" && blnInError == true) {
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_ProductError").style.backgroundColor = "#990000";
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_ProductError1").style.backgroundColor = "#990000";
            }
            if (strControlID == "ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_cmbProductstxt" && blnInError == true) {
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_ProductError").style.backgroundColor = "#990000";
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_ProductError1").style.backgroundColor = "#990000";
            }
            if (strControlID == "ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_ddlRequirementforTraceability" && blnInError == true) {
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_TraceabilityError").style.backgroundColor = "#990000";
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_TraceabilityError1").style.backgroundColor = "#990000";
            }
            if (strControlID == "ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_ddlType" && blnInError == true) {
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_ddlTypeError").style.backgroundColor = "#990000";
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_ddlTypeError1").style.backgroundColor = "#990000";
            }

            if (strControlID == "ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_ddlSalesman" && blnInError == true) {
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_SalespersonError").style.backgroundColor = "#990000";
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_SalespersonError1").style.backgroundColor = "#990000";
            }

            if (strControlID == "ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_ddlCurrency" && blnInError == true) {
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_CurrencyError").style.backgroundColor = "#990000";
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_CurrencyError1").style.backgroundColor = "#990000";
            }
            if (strControlID == "ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_txtDateRequired" && blnInError == true) {
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_DateRequiredError").style.backgroundColor = "#990000";
                document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_DateRequiredError1").style.backgroundColor = "#990000";
            }
            //if (strControlID == "ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_cmbPackage" && blnInError == true) {
            //    document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_PackageError").style.backgroundColor = "#990000";
            //    document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_PackageError1").style.backgroundColor = "#990000";
            //}
            //if (strControlID == "ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_cmbPackagetxt" && blnInError == true) {
            //    document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_PackageError").style.backgroundColor = "#990000";
            //    document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_PackageError1").style.backgroundColor = "#990000";
            //}

        } else {
            document.getElementById(strControlID).style.border = '';

        }

    }
    ,
    allowEditingCurrency: function (bln) {
        //this.showField("ctlEditCurrency", bln);
        //this.showField("ctlCurrency", bln);
    },
    showProductLoading: function (bln) {
        //this.showFieldLoading("ctlPartNo", bln);
    },
    getPartDetail: function (partNo) {
        $('#divBlockBox').show();
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/CusReqAdd");
        obj.set_DataObject("CusReqAdd");
        obj.set_DataAction("GetPartDetail");
        obj.addParameter("partNo", partNo);
        obj.addParameter("CompanyNo", this._intCompanyID);
        obj.addDataOK(Function.createDelegate(this, this.setPartDetail));
        obj.addError(Function.createDelegate(this, this.getPartDetailError));
        obj.addTimeout(Function.createDelegate(this, this.getPartDetailError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;


    },
    setPartDetail: function (args) {
        res = args._result;

        for (var i = 0; i < res.LastPriceCustDetails.length; i++) {
            var row = res.LastPriceCustDetails[i];

            //$("#spnpartname").text($("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_lblPartNo").text());
            //$("#spnLastSoldPrice").text(row.LastPricePaidByCust);
            //$("#spnsoldtocuston").text(row.LastSoldtoCustomer);
            //$("#spnAvgPrice").text(row.LastAverageReboundPriceSold);
            //$("#spnlastsoldon").text(row.LastSoldOn);

            //$("#spnLastQuantity").text(row.LastQuantity);
            //$("#spnLastSupplierType").text(row.LastSupplierType);
            //$("#spnLastDatecode").text(row.LastDatecode);
            //$("#spnLastDatePurchased").text(row.LastDatePurchased);
            //$("#spnLastCustomerRegion").text(row.LastCustomerRegion);

            //$("#spnCustLastSoldPrice").text(row.CustLastPricePaidByCust);
            //$("#spnCurrentCust").text(this._ctlCompany);
            //$("#spnCustAvgPrice").text(row.CustLastAvgPriceSold);
            //$("#spnCustlastsoldon").text(row.CustLastSoldOn);

            //$("#spnCustQuantity").text(row.CustQuantity);
            //$("#spnCustSupplierType").text(row.CustSupplierType);
            //$("#spnCustDatecode").text(row.CustDatecode);
            //$("#spnCustDatePurchased").text(row.CustDatePurchased);
            //$("#spnCustomerRegion").text(row.CustomerRegion);


            $("#spnpartname").text($("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_lblPartNo").text());
            $("#spnLastSoldPrice").text(row.LastPricePaidByCust);
            $("#spnsoldtocuston").text($R_FN.setCleanTextValue(row.LastSoldtoCustomer));
            $("#spnAvgPrice").text(row.LastAverageReboundPriceSold);
            $("#spnlastsoldon").text(row.LastSoldOn);

            $("#spnLastQuantity").text(row.LastQuantity);
            $("#spnLastSupplierType").text($R_FN.setCleanTextValue(row.LastSupplierType));
            $("#spnLastDatecode").text($R_FN.setCleanTextValue(row.LastDatecode));
            $("#spnLastDatePurchased").text(row.LastDatePurchased);
            $("#spnLastCustomerRegion").text($R_FN.setCleanTextValue(row.LastCustomerRegion));

            $("#spnCustLastSoldPrice").text(row.CustLastPricePaidByCust);
            $("#spnCurrentCust").text(this._strCompanyName);
            // $("#spnCustAvgPrice").text(row.CustLastAvgPriceSold);
            $("#spnCustlastsoldon").text(row.CustLastSoldOn);

            $("#spnCustQuantity").text(row.CustQuantity);
            $("#spnCustSupplierType").text($R_FN.setCleanTextValue(row.CustSupplierType));
            $("#spnCustDatecode").text($R_FN.setCleanTextValue(row.CustDatecode));
            $("#spnCustDatePurchased").text(row.CustDatePurchased);
            $("#spnCustomerRegion").text($R_FN.setCleanTextValue(row.CustomerRegion));
            $("#spnLastPricePaid12").text(row.BestLastPricePaid12);
            $("#spnCleintBestPricePaid12").text(row.CleintBestPricePaid12);

        }
        $('#divBlockBox').hide();

        //$("#spnpartname").text($get(this.getFormControlID(this._element.id, 'txtPartNo')).value);
        //$("#spnpartname").text($("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_lblPartNo").text());

        ////$("#spnLastSoldPrice").text(res.LastPricePaidByCust)
        //$("#spnLastSoldPrice").text(res.LastPricePaidByCust);
        //$("#spnsoldtocuston").text(res.LastSoldtoCustomer);
        //$("#spnAvgPrice").text(res.LastAverageReboundPriceSold);
        //$("#spnlastsoldon").text(res.LastSoldOn);

        //$("#spnCustLastSoldPrice").text(res.CustLastPricePaidByCust);
        //$("#spnCurrentCust").text(this._ctlCompany);
        //$("#spnCustAvgPrice").text(res.CustLastAvgPriceSold);
        //$("#spnCustlastsoldon").text(res.CustLastSoldOn);
        //$("#spnInStock").text(res.QuantityInStock);
        //$("#spnOnOrder").text(res.QuantityOnOrder);
        //$("#spnAllocated").text(res.QuantityAllocated);
        //$("#spnAvailable").text(res.QuantityAvailable);
    },
    getPartDetailError: function (args) {
    },
    myBlurFunction: function () {
        this.SelectIHSPartEccnMappedEdit();
    },
    //Part Eccn Mapping strat code
    SelectIHSPartEccnMappedEdit: function () {
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/SOLines");
        obj.set_DataObject("SOLines");
        obj.set_DataAction("GetSOIHSEccnDetail");
        obj.addParameter("Part", $get(this.getFormControlID(this._element.id, 'cmbIHS12txt')).value);
        obj.addDataOK(Function.createDelegate(this, this.seteditIHSPartEccnDetail));
        obj.addError(Function.createDelegate(this, this.IHSeditPartECCNDetailError));
        obj.addTimeout(Function.createDelegate(this, this.IHSeditPartECCNDetailError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },
    seteditIHSPartEccnDetail: function (args) {
        var obj = args._result.Result
        if (obj[0].ECCNNo > 0) {
            //ECCN Code start
            if (obj[0].ECCNCode != 'undefined' && obj[0].ECCNCode != '' && obj[0].ECCNCode.length > 0) {
                $find(this.getFormControlID(this._element.id, 'cmbPartEccnMapped')).setValue(obj[0].ECCNNo, $R_FN.setCleanTextValue(obj[0].ECCNCode));
                if (obj[0].ECCNStatus) {
                    $('#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_cmbPartEccnMappedaut_ctl03').addClass('ihspartstatusdoc');
                    $('#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_cmbPartEccnMappedaut_ctl03').prop("title", obj[0].ECCNWarning.replace(/<br\s*\/?>/gi, ''));
                }
                else {
                    $('#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_cmbPartEccnMappedaut_ctl03').removeClass('ihspartstatusdoc');
                    $('#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_cmbPartEccnMappedaut_ctl03').prop("title", "");
                }
            }
            


        }
        else {
            $find('ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_cmbPartEccnMappedaut').reselect();
        }
    },
    IHSeditPartECCNDetailError: function (args) {
    },
    //[006] start
    ConvertAS6081ToBool: function () {
        debugger;
        switch ($find(this.getFormControlID(this._element.id, 'ddlAS6081')).getValue()) {
            
            case "1":
                return true;
                break;
            default:
                return false;
                break;
        }
            //[006] end
    }

};

Rebound.GlobalTrader.Site.Controls.Forms.CustomerRequirementMainInfo_Edit.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.CustomerRequirementMainInfo_Edit", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
