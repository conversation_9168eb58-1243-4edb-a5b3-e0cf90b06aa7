SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE OR ALTER Procedure  [dbo].[Usp_AutoSourcing]                          
(                          
  @BOMManagerNo int                  
, @curPage int = 1                  
, @Rpp int = 10                  
, @CustomerRequirementID INT  = null                  
)                          
WITH RECOMPILE                          
AS                           
BEGIN                          
                          
exec USP_AutoSourcingResultCalculation @BOMManagerNo                          
                  
                  
  create table #PartTemp(PartNo varchar(100) COLLATE Latin1_General_CI_AS,Fullpart  varchar(100) COLLATE Latin1_General_CI_AS,CustomerRequirementId int )                   
                
  IF (@CustomerRequirementID is null or @CustomerRequirementID = '')                          
  begin                        
  insert into #Parttemp                         
   select cus.Part, cus.FullPart, CustomerRequirementId from dbo.tbCustomerRequirement cus where cus.BOMManagerNo = @BOMManagerNo                        
                     
  END                          
                          
 ELSE                          
 Begin                         
  insert into #Parttemp                        
 select cus.Part,cus.FullPart,CustomerRequirementId from dbo.tbCustomerRequirement cus where cus.BOMManagerNo = @BOMManagerNo and CustomerRequirementId= @CustomerRequirementID                        
                    
 END                  
                
                  
   declare @TotalRecords int, @skip int                  
                  
      select @TotalRecords = count(a.SourceId) FROM tbAutoSource a                      
  join tbCustomerRequirement cr on cr.CustomerRequirementId = a.CustomerRequirementId                 
  join #Parttemp prt on cr.CustomerRequirementId=prt.CustomerRequirementId --and  cr.FullPart = prt.PartNo                 
  where a.BOMManagerNo = @BOMManagerNo    and isnull(a.IsDeleted,0)=0              
                      
 set @skip = (@Rpp * (@curPage - 1))                  
                  
 if (@skip >= @TotalRecords and @TotalRecords > 0)                  
 begin                  
  set @curPage = CAST(@TotalRecords/@Rpp as int)                  
  set @skip = CAST((@Rpp * (@curPage - 1)) as int)                   
 end                  
             
  if(@skip<0)            
 begin set @skip = 0 end            
            
SELECT a.*                    
   ,abm.Reason                    
   ,cr.REQStatus                    
   ,(CASE WHEN EXISTS                    
   (                    
  SELECT SourcingResultId FROM tbSourcingResult WHERE AutoSourceID = a.SourceId                    
   )                    
   THEN 'TRUE'                     
   ELSE 'FALSE'                    
   END) as ItemReleased                  
   ,@TotalRecords as TotalRecords              
   ,c.CurrencyCode          
   ,tbems.Part as CR_Part,      
   case when ( tbems.Alternatestatus=1) then cast(1 as bit) else cast(0 as bit) end as AlternateOfferFlag      
   ,tbprd.ProductName, tbpck.PackageName  
FROM tbAutoSource a                      
join tbCustomerRequirement cr on cr.CustomerRequirementId = a.CustomerRequirementId                 
join #Parttemp prt on cr.CustomerRequirementId=prt.CustomerRequirementId --and  cr.FullPart = prt.PartNo                 
LEFT JOIN tbaudit_BOMManager abm on abm.AutoSourceNo = a.SourceID              
LEFT JOIN tbCurrency c on c.CurrencyId = a.CurrencyNo              
left join tbEMSOffers tbems on a.OfferId = tbems.EMSOfferId   
left join tbproduct tbprd on a.productno=tbprd.ProductId  
left join tbPackage tbpck on a.PackageNo = tbpck.PackageId  
where a.BOMManagerNo = @BOMManagerNo  and isnull(a.IsDeleted,0)=0              
order by cr.CustomerRequirementId                  
OFFSET @skip ROWS                  
FETCH NEXT @Rpp ROWS ONLY    
                  
END         


GO


