Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.Confirm=function(n){Rebound.GlobalTrader.Site.Controls.Forms.Confirm.initializeBase(this,[n]);this._intCustomerRequirementID=-1;this._intCurrencyID=-1;this._intCompanyID=-1;this._blnReqValidated=!0;this._ctlPartNo=null};Rebound.GlobalTrader.Site.Controls.Forms.Confirm.prototype={get_intCustomerRequirementID:function(){return this._intCustomerRequirementID},set_intCustomerRequirementID:function(n){this._intCustomerRequirementID!==n&&(this._intCustomerRequirementID=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Forms.Confirm.callBaseMethod(this,"initialize");this.addShown(Function.createDelegate(this,this.formShown))},dispose:function(){this.isDisposed||(this._intCustomerRequirementID=null,this._intCurrencyID=-0,this._intCompanyID=null,Rebound.GlobalTrader.Site.Controls.Forms.Confirm.callBaseMethod(this,"dispose"))},formShown:function(){this._blnFirstTimeShown&&(this._ctlConfirm=this.getFieldComponent("ctlConfirm"),this._ctlConfirm.addClickYesEvent(Function.createDelegate(this,this.yesClicked)),this._ctlConfirm.addClickNoEvent(Function.createDelegate(this,this.noClicked)),this._ctlMail=$find(this.getField("ctlSendMailMessage").ID),this._ctlMail._ctlRelatedForm=this);this.getFieldDropDownData("ctlSalesperson");this.getFieldDropDownData("ctlSalesman");this._blnReqValidated==!1?(this.showError(!0,"Some mandatory data is missing from this requirement. Please go back and fill in the missing data."),this.showField("ctlSalesperson",!1),this.showField("ctlSendMailMessage",!1),this.showField("ctlQuoteRequired",!1),this.showField("ctlConfirm",!1)):(this.showField("ctlSalesperson",!0),this.showField("ctlSendMailMessage",!0),this.showField("ctlQuoteRequired",!0),this.showField("ctlConfirm",!0));$addHandler(document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl21_ibtnBack_hyp"),"click",Function.createDelegate(this,this.noClicked1));$addHandler(document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl22_ibtnBack_hyp"),"click",Function.createDelegate(this,this.noClicked1))},noClicked1:function(){this.onNotConfirmed()},yesClicked:function(){if(this.validateForm()){this.showSaving(!0);var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("Controls/Nuggets/CusReqMainInfo");n.set_DataObject("CusReqMainInfo");n.set_DataAction("SaveRequirementData");n.addParameter("id",this._intCustomerRequirementID);n.addParameter("CurrencyID",this._intCurrencyID);n.addParameter("CompanyID",this._intCompanyID);n.addParameter("AssignUserNo",this.getFieldValue("ctlSalesperson"));n.addParameter("DateRequired",this.getFieldValue("ctlQuoteRequired"));n.addParameter("aryRecipientLoginIDs",$R_FN.arrayToSingleString(this._ctlMail._aryRecipientLoginIDs));n.addParameter("Contact2No",this.getFieldValue("ctlSalesman"));n.addParameter("PartNo",this._ctlPartNo);n.addDataOK(Function.createDelegate(this,this.saveConfirmComplete));n.addError(Function.createDelegate(this,this.saveError));n.addTimeout(Function.createDelegate(this,this.saveError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null;this.onSave()}},noClicked:function(){this.showSaving(!1);this.onNotConfirmed()},validateForm:function(){this.onValidate();var n=!0;return this.checkFieldEntered("ctlSalesperson")||(n=!1),this.checkFieldEntered("ctlQuoteRequired")||(n=!1),this._blnReqValidated==!1&&(n=!1,this.showError(!0,"Some mandatory data is missing from this requirement. Please go back and fill in the missing data.")),n},saveError:function(n){this.showSaving(!1);this._strErrorMessage=n._errorMessage;this.onSaveError()},saveConfirmComplete:function(n){var t=n._result;n._result.Result?(this.showSavedOK(!0),this.onSaveComplete()):this.showError(!0,n._result.ValidationMessage)}};Rebound.GlobalTrader.Site.Controls.Forms.Confirm.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.Confirm",Rebound.GlobalTrader.Site.Controls.Forms.Base,Sys.IDisposable);