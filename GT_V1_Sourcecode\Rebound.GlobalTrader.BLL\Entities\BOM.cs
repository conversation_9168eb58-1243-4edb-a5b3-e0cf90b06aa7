﻿//Marker    changed by      date           Remarks
//[001]      Vinay           12/08/2014     ESMS  Ticket Number: 	201
//[002]      <PERSON><PERSON>   30/08/2018     Add ClientCode,ReceivedBy and <PERSON><PERSON><PERSON> field
//[003]      <PERSON><PERSON><PERSON>     15-Oct-2018    Export all result instead of single page on HUBRFQ.
//[004]      <PERSON><PERSON>   20-Dec-2018    Add Status Dropdown in Customer Requiremnt Import.
//[005]      <PERSON><PERSON>   27-Dec-2018    Showing Client BOM Details.
//[006]      <PERSON><PERSON>   03-Dec-2018    Add and Update Client BOM.
//[007]      <PERSON><PERSON>   18-Mar-2019    Showing Records Processed and Records Remaining.
//[008]  <PERSON><PERSON>  19-Dec-2023     RP-2629 (Line 759 added 3 parameters in Method DataListNugget_Export)
using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;
using Rebound.GlobalTrader.DAL;
using Rebound.GlobalTrader.BLL;

namespace Rebound.GlobalTrader.BLL
{
    public partial class BOM : BizObject
    {

        #region Properties

        protected static DAL.BOMElement Settings
        {
            get { return Globals.Settings.BOM; }
        }
        /// <summary>
        /// BOMId
        /// </summary>
        public System.Int32 BOMId { get; set; }
        /// <summary>
        /// ClientNo
        /// </summary>
        public System.Int32 ClientNo { get; set; }

        public System.String AssignedUserIds { get; set; }
        /// <summary>
        /// BOMName
        /// </summary>
        public System.String BOMName { get; set; }
        /// <summary>
        /// BOMName
        /// </summary>
        public System.String BOMIds { get; set; }
        /// <summary>
        /// Notes
        /// </summary>
        public System.String Notes { get; set; }
        /// <summary>
        /// BOMCode
        /// </summary>
        public System.String BOMCode { get; set; }
        /// <summary>
        /// Inactive
        /// </summary>
        public System.Boolean Inactive { get; set; }
        /// <summary>
        /// UpdatedBy
        /// </summary>
        public System.Int32? UpdatedBy { get; set; }
        /// <summary>
        /// DLUP
        /// </summary>
        public System.DateTime DLUP { get; set; }

        /// <summary>
        /// RowNum
        /// </summary>
        public System.Int64? RowNum { get; set; }
        /// <summary>
        /// RowCnt
        /// </summary>
        public System.Int32? RowCnt { get; set; }
        /// <summary> 
        /// Company No
        /// </summary>
        public System.Int32? CompanyNo { get; set; }
        /// <summary>
        ///Company Name
        ///</summary>
        public System.String CompanyName { get; set; }
        /// <summary> 
        /// Contact No
        /// </summary>
        public System.Int32? ContactNo { get; set; }
        public System.String Part { get; set; }
        public System.Int32? Quantity { get; set; }

        public System.Int32? CustomerRequirementId { get; set; }
        /// <summary>
        ///Contact Name
        ///</summary>
        public System.String ContactName { get; set; }
        /// <summary>
        ///Contact Name
        ///</summary>
        public System.String CompanyType { get; set; }
        /// <summary>
        ///Update Requirement
        ///</summary>
        public System.Int32? UpdateRequirement { get; set; }
        /// <summary>
        ///BOM Status
        ///</summary>
        public System.String BOMStatus { get; set; }
        /// <summary>
        ///Status
        ///</summary>
        public System.Int32? Status { get; set; }
        /// <summary>
        ///Client Code
        ///</summary>
        public System.String ClientCode { get; set; }//[002]
        /// <summary>
        ///ReceivedBy
        ///</summary>
        public System.String ReceivedBy { get; set; }//[002]
        /// SalesPerson
        /// </summary>
        public System.Int32? SalesPerson { get; set; }//[002]
        /// <summary>
		/// <summary>
		/// CountForClient
        public System.Int32? RequestToPOHubBy { get; set; }
        public System.DateTime? DateRequestToPOHub { get; set; }
        public System.Int32? ReleaseBy { get; set; }
        public System.DateTime? DateRelease { get; set; }
        public System.Int32? BomCount { get; set; }
        public System.Int32? CurrencyNo { get; set; }
        public System.String CurrencyCode { get; set; }

        public int? StatusValue { get; set; }
        public System.String Currency_Code { get; set; }
        public string ClientName { get; set; }
        public System.String CurrentSupplier { get; set; }
        public System.DateTime? QuoteRequired { get; set; }
        public string UpdatedByList { get; set; }
        public double TotalBomLinePrice { get; set; }
        public int POCurrencyNo { get; set; }
        public System.Int32? AllItemHasSourcing { get; set; }
        /// AS9120
        /// </summary>
        public System.Boolean? AS9120 { get; set; }
        public System.Int32 AssignUserNo { get; set; }
        public System.String AS6081 { get; set; }
        public string Releasedby { get; set; }
        public string Requestedby { get; set; }
        public System.Int32? RequesterId { get; set; }
        public string AssignedUser { get; set; }
        public int NoBidCount { get; set; }
        public System.String DivisionName { get; set; }

        public System.Int32? UpdateByPH { get; set; }
        public System.String ValidationMessage { get; set; }
        public System.Boolean IsReqInValid { get; set; }
        public System.Int32? Contact2Id { get; set; }
        public System.String Contact2Name { get; set; }
        public System.String SalesmanName { get; set; }
        public System.Int32? BOMStatusId { get; set; }//[004]
        public System.String BOMStatusName { get; set; }//[004]

        public System.Int32 ClientBOMId { get; set; }//[005]
        public System.String ClientBOMCode { get; set; }//[005]
        public System.String ClientBOMName { get; set; }//[005]
        public System.String Salesman { get; set; }//[005]
        public System.DateTime? ClosedDate { get; set; }//[005]
        public System.DateTime? ImportDate { get; set; }//[005]
        public System.String CurrencyName { get; set; }//[005]
        public System.Int32? SalesmanId { get; set; }//[005]
        public System.Int32? CompanyTypeid { get; set; }//[005]
        public System.Int32? RecordsProcessed { get; set; }//[007]  
        public System.Int32? RecordsRemaining { get; set; }//[007]  
        public System.String ReqSalesPerson { get; set; }

        public System.DateTime? RequiredDate { get; set; }

        public System.String RequiredDateStatus { get; set; }


        public System.String ExpediteNotes { get; set; }
        /// <summary>
        /// SupportTeamMemberNo
        /// </summary>
        public System.Int32? SupportTeamMemberNo { get; set; }
        /// <summary>
        /// SupportTeamMemberName
        /// </summary>
        public System.String SupportTeamMemberName { get; set; }
        /// <summary>
        /// SupportTeamMemberNo
        /// </summary>
        public System.String SupportTeamMemberNoAsString { get; set; }

        public System.String Manufacturer { get; set; }
        public System.Int32? CustomerRequirementNo { get; set; }
        /// <summary>
        /// PurchasingNotes
        /// </summary>
        public System.String PurchasingNotes { get; set; }
        public System.Int32 PVVQuestionId { get; set; }
        public System.String PVVQuestionName { get; set; }
        public System.Int32 PVVAnswerId { get; set; }
        public System.String PVVAnswerName { get; set; }
        public System.Int32 PVVQuestionNo { get; set; }
        public System.String HUBRFQNo { get; set; }
        public System.String PVVBOMValidateMessage { get; set; }
        public System.Boolean PVVBOMCountValid { get; set; }
        public System.Boolean IsFromProspectiveOffer { get; set; }
        public System.String UploadedBy { get; set; }
        public System.Double? TargetSellPrice { get; set; }
        #endregion

        #region Methods

        /// <summary>
        /// CountForClient
        /// Calls [usp_count_BOM]
        /// </summary>
        public static Int32 CountForBOM(System.Int32? clientId)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.BOM.CountForBOM(clientId);
        }       /// <summary>
                /// DataListNugget
                /// Calls [usp_datalistnugget_BOM]
                /// </summary>
        //public static List<BOM> DataListNugget(System.Int32? clientId, System.Int32? orderBy, System.Int32? sortDir, System.Int32? pageIndex, System.Int32? pageSize, System.Int32? BOMIdLo, System.Int32? BOMIdHi, System.String bomName, System.Boolean? isPOHub, System.Int32? selectedClientNo)
        public static List<BOM> DataListNugget(System.Int32? clientId, System.Int32? teamId, System.Int32? divisionId, System.Int32? loginId, System.Int32? orderBy, System.Int32? sortDir, System.Int32? pageIndex, System.Int32? pageSize, System.String BOMCode, System.String bomName, System.Boolean? isPOHub, System.Int32? selectedClientNo,
            int? bomStatus, int? isAssignToMe, int? assignedUser, System.Int32? intDivisionNo, System.Int32? salesPerson, System.Int32? CompanyTypeid, System.DateTime? startdate, System.DateTime? enddate, System.DateTime? RequiredStartdate, System.DateTime? RequiredEndDate, System.Int32? AS6081Required = 0, System.Boolean? IsAS6081Tab = false, System.Int32? MyPageSize = 10, System.Boolean? isSearchFromRequirements = false, System.Int32? SelectedLoginId = null)

        {
            List<BOMDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.BOM.DataListNugget(clientId, teamId, divisionId, loginId, orderBy, sortDir, pageIndex, pageSize, BOMCode, bomName, isPOHub,
                selectedClientNo, bomStatus, isAssignToMe, assignedUser, intDivisionNo, salesPerson, CompanyTypeid, startdate, enddate, RequiredStartdate, RequiredEndDate, AS6081Required, IsAS6081Tab, MyPageSize, isSearchFromRequirements, SelectedLoginId);
            if (lstDetails == null)
            {
                return new List<BOM>();
            }
            else
            {
                List<BOM> lst = new List<BOM>();
                foreach (BOMDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.BOM obj = new Rebound.GlobalTrader.BLL.BOM();
                    obj.BOMId = objDetails.BOMId;
                    obj.BOMCode = objDetails.BOMCode.TrimEnd();
                    obj.BOMName = objDetails.BOMName;
                    obj.RowNum = objDetails.RowNum;
                    obj.RowCnt = objDetails.RowCnt;
                    obj.CompanyName = objDetails.CompanyName;
                    obj.CompanyType = objDetails.CompanyType;
                    obj.CompanyNo = objDetails.CompanyNo;
                    obj.Inactive = objDetails.Inactive;
                    obj.BOMStatus = objDetails.BOMStatus;
                    obj.DLUP = objDetails.DLUP;
                    obj.QuoteRequired = objDetails.QuoteRequired;
                    obj.UpdatedBy = objDetails.UpdatedBy;
                    obj.CurrencyNo = objDetails.CurrencyNo;
                    obj.TotalBomLinePrice = objDetails.TotalBomLinePrice;
                    obj.DateRequestToPOHub = objDetails.DateRequestToPOHub;
                    obj.POCurrencyNo = objDetails.POCurrencyNo;
                    obj.AssignedUser = objDetails.AssignedUser;
                    obj.DivisionName = objDetails.DivisionName;
                    obj.ClientCode = objDetails.ClientCode;//[002]
                    obj.Requestedby = objDetails.Requestedby;//[002]
                    obj.SalesmanName = objDetails.SalesmanName;
                    obj.ContactName = objDetails.ContactName;
                    obj.ExpediteNotes = objDetails.ExpediteNotes;
                    obj.ContactNo = objDetails.ContactNo;
                    obj.RequiredDate = objDetails.RequiredDate;
                    obj.RequiredDateStatus = objDetails.RequiredDateStatus;
                    obj.Part = objDetails.Part;
                    obj.Quantity = objDetails.Quantity;
                    obj.CustomerRequirementId = objDetails.CustomerRequirementId;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }
        /// <summary>
        /// Delete
        /// Calls [usp_delete_BOM]
        /// </summary>
        public static bool Delete(System.Int32? bomId)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.BOM.Delete(bomId);
        }
        /// <summary>
        /// DropDownForClient
        /// Calls [usp_dropdown_BOM_for_Client]
        /// </summary>
        public static List<BOM> DropDownForClient(System.Int32? clientId, System.Int32? CompanyId)
        {
            List<BOMDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.BOM.DropDownForClient(clientId, CompanyId);
            if (lstDetails == null)
            {
                return new List<BOM>();
            }
            else
            {
                List<BOM> lst = new List<BOM>();
                foreach (BOMDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.BOM obj = new Rebound.GlobalTrader.BLL.BOM();
                    obj.BOMId = objDetails.BOMId;
                    obj.BOMName = objDetails.BOMName;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }
        /// <summary>
        /// Insert
        /// Calls [usp_insert_BOM]
        /// </summary>
        public static Int32 Insert(System.Int32? clientNo, System.String bomName, System.String notes, System.String bomCode, System.Int32? updatedBy, System.Int32? companyId, System.Int32? contactId, System.Boolean? inactive, System.Int32? updateRequirement, System.Int32? status, System.Int32? currencyNo, System.String currentSupplier, System.DateTime? quoteRequired, System.Boolean? AS9120, System.Int32? Contact2Id, System.Int32 AssignUserNo, out System.String ValidationMessage)
        {
            Int32 objReturn = Rebound.GlobalTrader.DAL.SiteProvider.BOM.Insert(clientNo, bomName, notes, bomCode, updatedBy, companyId, contactId, inactive, updateRequirement, status, currencyNo, currentSupplier, quoteRequired, AS9120, Contact2Id, AssignUserNo, out ValidationMessage);
            return objReturn;
        }
        /// <summary>
        /// Insert (without parameters)
        /// Calls [usp_insert_BOM]
        /// </summary>
        public Int32 Insert()
        {
            String ValidationMessage = null;            
            int creditLineId = Rebound.GlobalTrader.DAL.SiteProvider.BOM.Insert(ClientNo,
                                                                                BOMName,
                                                                                Notes,
                                                                                BOMCode,
                                                                                UpdatedBy,
                                                                                CompanyNo,
                                                                                ContactNo,
                                                                                Inactive,
                                                                                UpdateRequirement,
                                                                                Status,
                                                                                CurrencyNo,
                                                                                CurrentSupplier,
                                                                                QuoteRequired,
                                                                                AS9120,
                                                                                Contact2Id,
                                                                                AssignUserNo,
                                                                                out ValidationMessage);
            this.ValidationMessage = ValidationMessage;
            return creditLineId;
        }
        /// <summary>
        /// Get
        /// Calls [usp_select_BOM]
        /// </summary>
        public static BOM Get(System.Int32? bomId)
        {
            Rebound.GlobalTrader.DAL.BOMDetails objDetails = Rebound.GlobalTrader.DAL.SiteProvider.BOM.Get(bomId);
            if (objDetails == null)
            {
                return null;
            }
            else
            {
                BOM obj = new BOM();
                obj.BOMId = objDetails.BOMId;
                //obj.ClientNo = objDetails.ClientNo;
                obj.BOMName = objDetails.BOMName;
                obj.CompanyNo = objDetails.CompanyNo;
                obj.CompanyName = objDetails.CompanyName;
                obj.CompanyType = objDetails.CompanyType;
                obj.Notes = objDetails.Notes;
                obj.BOMCode = objDetails.BOMCode;
                obj.Inactive = objDetails.Inactive;
                obj.ContactNo = objDetails.ContactNo;
                obj.ContactName = objDetails.ContactName;
                obj.RequestToPOHubBy = objDetails.RequestToPOHubBy;
                obj.DateRequestToPOHub = objDetails.DateRequestToPOHub;
                obj.ReleaseBy = objDetails.ReleaseBy;
                obj.DateRelease = objDetails.DateRelease;
                obj.UpdatedBy = objDetails.UpdatedBy;
                obj.DLUP = objDetails.DLUP;
                obj.BOMStatus = objDetails.BOMStatus;
                obj.BomCount = objDetails.BomCount;
                obj.StatusValue = objDetails.StatusValue;
                obj.CurrencyCode = objDetails.CurrencyCode;
                obj.CurrencyNo = objDetails.CurrencyNo;
                obj.Currency_Code = objDetails.Currency_Code;
                obj.CurrentSupplier = objDetails.CurrentSupplier;
                obj.QuoteRequired = objDetails.QuoteRequired;
                obj.AllItemHasSourcing = objDetails.AllItemHasSourcing;
                obj.AS9120 = objDetails.AS9120;
                obj.Requestedby = objDetails.Requestedby;
                obj.Releasedby = objDetails.Releasedby;
                obj.NoBidCount = objDetails.NoBidCount;
                obj.UpdateByPH = objDetails.UpdateByPH;
                obj.AssignedUser = objDetails.AssignedUser;
                obj.Contact2Id = objDetails.Contact2Id;
                obj.Contact2Name = objDetails.Contact2Name;
                obj.ValidationMessage = objDetails.ValidationMessage;
                obj.IsReqInValid = objDetails.IsReqInValid;
                obj.ReqSalesPerson = objDetails.ReqSalesPerson;
                obj.SupportTeamMemberNoAsString = objDetails.SupportTeamMemberNoAsString;
                obj.ClientNo = objDetails.ClientNo;
                obj.AssignedUserIds = objDetails.AssignedUserIds;
                obj.AS6081 = objDetails.AS6081;
                obj.PurchasingNotes = objDetails.PurchasingNotes;
                obj.PVVBOMValidateMessage = objDetails.PVVBOMValidateMessage;
                obj.PVVBOMCountValid = objDetails.PVVBOMCountValid;
                obj.IsFromProspectiveOffer = objDetails.IsFromProspectiveOffer;
                obj.UploadedBy = objDetails.UploadedBy;
                objDetails = null;
                return obj;
            }
        }
        /// <summary>
        /// GetForPage
        /// Calls [usp_select_BOM_for_Page]
        /// </summary>
        public static BOM GetForPage(System.Int32? bomId)
        {
            Rebound.GlobalTrader.DAL.BOMDetails objDetails = Rebound.GlobalTrader.DAL.SiteProvider.BOM.GetForPage(bomId);
            if (objDetails == null)
            {
                return null;
            }
            else
            {
                BOM obj = new BOM();
                obj.BOMId = objDetails.BOMId;
                obj.BOMName = objDetails.BOMName;
                obj.ClientNo = objDetails.ClientNo;
                obj.Inactive = objDetails.Inactive;
                obj.BOMStatus = objDetails.BOMStatus;
                obj.RequestToPOHubBy = objDetails.RequestToPOHubBy;
                obj.ClientName = objDetails.ClientName;
                objDetails = null;
                return obj;
            }
        }
        /// <summary>
        /// Update
        /// Calls [usp_update_BOM]
        /// </summary>
        public static bool Update(System.Int32? bomId, System.Int32? clientNo, System.String bomName, System.String notes, System.String bomCode, System.Boolean? inactive, System.Int32? updatedBy, System.Int32? companyId, System.Int32? contactId, System.Int32? currencyNo, System.String currentSupplier, System.DateTime? quoteRequired, System.Boolean? AS9120, System.Int32? contact2Id)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.BOM.Update(bomId, clientNo, bomName, notes, bomCode, inactive, updatedBy, companyId, contactId, currencyNo, currentSupplier, quoteRequired, AS9120, contact2Id);
        }
        /// <summary>
        /// Update (without parameters)
        /// Calls [usp_update_BOM]
        /// </summary>
        public bool Update()
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.BOM.Update(BOMId, ClientNo, BOMName, Notes, BOMCode, Inactive, UpdatedBy, CompanyNo, ContactNo, CurrencyNo, CurrentSupplier, QuoteRequired, AS9120, Contact2Id);
        }
        /// <summary>
        /// UpdateDelete
        /// Calls [usp_update_BOM_Delete]
        /// </summary>
        public static bool UpdateDelete(System.Int32? BOMId, System.Int32? updatedBy)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.BOM.UpdateDelete(BOMId, updatedBy);
        }

        private static BOM PopulateFromDBDetailsObject(BOMDetails obj)
        {
            BOM objNew = new BOM();
            objNew.BOMId = obj.BOMId;
            objNew.ClientNo = obj.ClientNo;
            objNew.BOMName = obj.BOMName;
            objNew.Notes = obj.Notes;
            objNew.BOMCode = obj.BOMCode;
            objNew.Inactive = obj.Inactive;
            objNew.UpdatedBy = obj.UpdatedBy;
            objNew.DLUP = obj.DLUP;
            objNew.RowNum = obj.RowNum;
            objNew.RowCnt = obj.RowCnt;
            return objNew;
        }

        //[001] code start
        /// <summary>
        /// AutoSearch
        /// Calls [usp_autosearch_BOM]
        /// </summary>
        public static List<BOM> AutoSearch(System.Int32? clientId, System.String nameSearch)
        {
            List<BOMDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.BOM.AutoSearch(clientId, nameSearch);
            if (lstDetails == null)
            {
                return new List<BOM>();
            }
            else
            {
                List<BOM> lst = new List<BOM>();
                foreach (BOMDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.BOM obj = new Rebound.GlobalTrader.BLL.BOM();
                    obj.BOMId = objDetails.BOMId;
                    obj.BOMName = objDetails.BOMName;
                    obj.BOMCode = objDetails.BOMCode;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }
        //[001] code end

        /// <summary>
        /// Update
        /// Calls [usp_update_BOM_POHubQuote]
        /// </summary>
        public static bool UpdatePurchaseQuote(System.Int32? BOMId, System.Int32? updatedBy, System.Int32? bomStatus, System.Int32 AssignUserNo, out System.String ValidateMessage)
        {
            string ValidationMessage = null;
            bool Isupdated = Rebound.GlobalTrader.DAL.SiteProvider.BOM.UpdatePurchaseQuote(BOMId, updatedBy, bomStatus, AssignUserNo, out ValidateMessage);
            ValidationMessage = ValidateMessage;
            return Isupdated;
        }

        /// <summary>
        /// GetListReadyForClient
        /// Calls [usp_selectAll_BOM]
        /// </summary>
        public static List<BOM> GetBomList(System.Int32? clientId, System.Boolean? isPoHUB, System.Int32? topToSelect, System.Int32? bomStatus, System.Int32? updatedBy)
        {
            List<BOMDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.BOM.GetBomList(clientId, isPoHUB, topToSelect, bomStatus, updatedBy);
            if (lstDetails == null)
            {
                return new List<BOM>();
            }
            else
            {
                List<BOM> lst = new List<BOM>();
                foreach (BOMDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.BOM obj = new Rebound.GlobalTrader.BLL.BOM();
                    obj.BOMId = objDetails.BOMId;
                    obj.CompanyName = objDetails.CompanyName;
                    obj.ClientName = objDetails.ClientName;
                    obj.BOMCode = objDetails.BOMCode;
                    obj.BOMName = objDetails.BOMName;
                    obj.DLUP = objDetails.DLUP;
                    obj.StatusValue = objDetails.StatusValue;
                    obj.UpdatedBy = objDetails.UpdatedBy;
                    obj.QuoteRequired = objDetails.QuoteRequired;
                    obj.RequestToPOHubBy = objDetails.RequestToPOHubBy;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }

        /// <summary>
        /// GetCSVListForBOM
        /// Calls [usp_selectAll_CSV_for_BOM]
        /// </summary>
        public static List<PDFDocument> GetCSVListForBOM(System.Int32? bomNo, System.String strType)
        {
            List<PDFDocumentDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.BOM.GetCSVListForBOM(bomNo, strType);
            if (lstDetails == null)
            {
                return new List<PDFDocument>();
            }
            else
            {
                List<PDFDocument> lst = new List<PDFDocument>();
                foreach (PDFDocumentDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.PDFDocument obj = new Rebound.GlobalTrader.BLL.PDFDocument();
                    obj.PDFDocumentId = objDetails.PDFDocumentId;
                    obj.PDFDocumentRefNo = objDetails.PDFDocumentRefNo;
                    obj.Caption = objDetails.Caption;
                    obj.FileName = objDetails.FileName;
                    obj.UpdatedBy = objDetails.UpdatedBy;
                    obj.DLUP = objDetails.DLUP;
                    obj.Section = objDetails.Section;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }


        /// <summary>
        /// UpdateBOMByPH
        /// Calls [usp_update_BOMByPH]
        /// </summary>
        public static bool UpdateBOMByPH(System.String BOMIdList, System.Int32? updatedBy, System.Int32? LoginId, System.Boolean? IsGroupAssignment = false, System.String Type = "Header")
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.BOM.UpdateBOMByPH(BOMIdList, updatedBy, LoginId, IsGroupAssignment, Type);
        }

        /// <summary>
        /// Delete
        /// Calls [usp_IpoBomCsvDelete]
        /// </summary>
        public static bool DeleteBomCsv(System.Int32? BomCSVId)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.BOM.DeleteBomCsv(BomCSVId);
        }


        /// <summary>
        /// DropDownForClient
        /// Calls [usp_GetUpdatedByListFromBOMIdList]
        /// </summary>
        public static BOM GetUpdatedByListFromBOMIdList(System.String BOMIdList)
        {
            BOMDetails lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.BOM.GetUpdatedByListFromBOMIdList(BOMIdList);
            // BOM lst = new BOM();
            Rebound.GlobalTrader.BLL.BOM obj = new Rebound.GlobalTrader.BLL.BOM();
            obj.BOMName = lstDetails.BOMName;
            obj.UpdatedByList = lstDetails.UpdatedByList;
            lstDetails = null;
            return obj;

        }
        public static BOM GetBomDetailsForAS6081AssignedToMe(System.String BOMIdList)
        {
            BOMDetails lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.BOM.GetBomDetailsForAS6081AssignedToMe(BOMIdList);

            Rebound.GlobalTrader.BLL.BOM obj = new Rebound.GlobalTrader.BLL.BOM();
            obj.BOMName = lstDetails.BOMName;
            obj.BOMIds = lstDetails.BOMIds;

            lstDetails = null;
            return obj;

        }

        public static List<BOM> GetRequirementDetailsForAS6081AssignedToMe(System.String ReqIdList)
        {
            List<BOMDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.BOM.GetRequirementDetailsForAS6081AssignedToMe(ReqIdList);
            if (lstDetails == null)
            {
                return new List<BOM>();
            }
            else
            {
                List<BOM> lst = new List<BOM>();
                foreach (BOMDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.BOM obj = new Rebound.GlobalTrader.BLL.BOM();
                    obj.BOMName = objDetails.BOMName;
                    obj.BOMIds = objDetails.BOMIds;
                    obj.CustomerRequirementId = objDetails.CustomerRequirementId;
                    obj.CustomerRequirementNo = objDetails.CustomerRequirementNo;
                    obj.Part = objDetails.Part;
                    obj.Quantity = objDetails.Quantity;
                    obj.Manufacturer = objDetails.Manufacturer;
                    obj.QuoteRequired = objDetails.QuoteRequired;
                    obj.CompanyName = objDetails.CompanyName;
                    obj.TargetSellPrice = objDetails.TargetSellPrice;
                    obj.CurrencyCode = objDetails.CurrencyCode;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }

        }

        public static List<BOM> GetBomDetailsForRequester(System.String BOMIdList)
        {
            List<BOMDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.BOM.GetBomDetailsForRequester(BOMIdList);

            if (lstDetails == null)
            {
                return new List<BOM>();
            }
            else
            {
                List<BOM> lst = new List<BOM>();
                foreach (BOMDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.BOM obj = new Rebound.GlobalTrader.BLL.BOM();
                    obj.BOMIds = objDetails.BOMIds;
                    obj.BOMName = objDetails.BOMName;
                    obj.RequesterId = objDetails.RequesterId;
                    obj.Requestedby = objDetails.Requestedby;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }

        }

        public static List<BOM> GetRequirementDetailsForRequestedUser(System.String ReqIdList)
        {
            List<BOMDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.BOM.GetRequirementDetailsForRequestedUser(ReqIdList);

            if (lstDetails == null)
            {
                return new List<BOM>();
            }
            else
            {
                List<BOM> lst = new List<BOM>();
                foreach (BOMDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.BOM obj = new Rebound.GlobalTrader.BLL.BOM();
                    obj.RequesterId = objDetails.RequesterId;
                    obj.Requestedby = objDetails.Requestedby;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }

        }

        public static BOM GetSecurityUserDetails(System.Int32 GroupId)
        {
            BOMDetails lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.BOM.GetSecurityUserDetails(GroupId);

            Rebound.GlobalTrader.BLL.BOM obj = new Rebound.GlobalTrader.BLL.BOM();
            obj.UpdatedByList = lstDetails.UpdatedByList;

            lstDetails = null;
            return obj;

        }


        /// <summary>
        /// Update
        /// 
        /// </summary>
        public static bool UpdateBOMStatusToClosed(System.Int32? BOMId, System.Int32? updatedBy, System.Int32? bomStatus)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.BOM.UpdateBOMStatusToClosed(BOMId, updatedBy, bomStatus);
        }


        /// <summary>
        /// GetIDByNumber
        /// Calls [usp_select_BOM_ID_by_Name]
        /// </summary>
        public static BOM GetIDByNumber(System.String bomName, System.Int32? clientNo)
        {
            Rebound.GlobalTrader.DAL.BOMDetails objDetails = Rebound.GlobalTrader.DAL.SiteProvider.BOM.GetIDByNumber(bomName, clientNo);
            if (objDetails == null)
            {
                return null;
            }
            else
            {
                BOM obj = new BOM();
                obj.BOMId = objDetails.BOMId;
                objDetails = null;
                return obj;
            }
        }

        // [001] code start
        /// <summary>
        /// GetListForSalesOrder
        /// Calls [usp_selectAll_StockPDF_for_Stock]
        /// </summary>
        public static List<StockImage> GetImageListForReq(System.Int32? sourcingResultNo, System.String fileType)
        {
            List<StockImageDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.BOM.GetImageListForReq(sourcingResultNo, fileType);
            if (lstDetails == null)
            {
                return new List<StockImage>();
            }
            else
            {
                List<StockImage> lst = new List<StockImage>();
                foreach (StockImageDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.StockImage obj = new Rebound.GlobalTrader.BLL.StockImage();
                    obj.StockImageId = objDetails.StockImageId;
                    //obj.PDFDocumentRefNo = objDetails.PDFDocumentRefNo;
                    obj.Caption = objDetails.Caption;
                    obj.ImageName = objDetails.ImageName;
                    obj.UpdatedBy = objDetails.UpdatedBy;
                    obj.By = objDetails.UpdatedByName;
                    obj.DLUP = objDetails.DLUP;
                    obj.ImageDocumentRefNo = objDetails.ImageDocumentRefNo;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }
        //[003] start
        public static DataTable DataListNugget_Export(System.Int32? clientId, System.Int32? teamId, System.Int32? divisionId, System.Int32? loginId, System.Int32? orderBy, System.Int32? sortDir, System.String BOMCode, System.String bomName, System.Boolean? isPOHub, System.Int32? selectedClientNo, int? bomStatus, int? isAssignToMe, int? assignedUser, System.Int32? intDivisionNo, System.Int32? salesPerson, System.DateTime? startdate, System.DateTime? enddate, System.Int32? CompanyTypeid, System.Int32? AS6081Required = 0, System.Int32? SelectedLoginId = null)
        {
            DataTable dt = Rebound.GlobalTrader.DAL.SiteProvider.BOM.DataListNugget_Export(clientId, teamId, divisionId, loginId, orderBy, sortDir, BOMCode, bomName, isPOHub, selectedClientNo, bomStatus, isAssignToMe, assignedUser, intDivisionNo, salesPerson, startdate, enddate, CompanyTypeid, AS6081Required, SelectedLoginId);
            //List<BOMDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.BOM.DataListNugget_Export(clientId, teamId, divisionId, loginId, orderBy, sortDir,BOMCode, bomName, isPOHub, selectedClientNo, bomStatus, isAssignToMe, assignedUser, intDivisionNo, salesPerson);
            //if (lstDetails == null)
            //{
            //    return new List<BOM>();
            //}
            //else
            //{
            //    List<BOM> lst = new List<BOM>();
            //    foreach (BOMDetails objDetails in lstDetails)
            //    {
            //        Rebound.GlobalTrader.BLL.BOM obj = new Rebound.GlobalTrader.BLL.BOM();
            //        obj.BOMId = objDetails.BOMId;
            //        obj.BOMCode = objDetails.BOMCode.TrimEnd();
            //        obj.BOMName = objDetails.BOMName;
            //        obj.RowNum = objDetails.RowNum;
            //        obj.RowCnt = objDetails.RowCnt;
            //        obj.CompanyName = objDetails.CompanyName;
            //        obj.CompanyNo = objDetails.CompanyNo;
            //        obj.Inactive = objDetails.Inactive;
            //        obj.BOMStatus = objDetails.BOMStatus;
            //        obj.DLUP = objDetails.DLUP;
            //        obj.QuoteRequired = objDetails.QuoteRequired;
            //        obj.UpdatedBy = objDetails.UpdatedBy;
            //        obj.CurrencyNo = objDetails.CurrencyNo;
            //        obj.TotalBomLinePrice = objDetails.TotalBomLinePrice;
            //        obj.DateRequestToPOHub = objDetails.DateRequestToPOHub;
            //        obj.POCurrencyNo = objDetails.POCurrencyNo;
            //        obj.AssignedUser = objDetails.AssignedUser;
            //        obj.DivisionName = objDetails.DivisionName;
            //        obj.ClientCode = objDetails.ClientCode;//[002]
            //        obj.Requestedby = objDetails.Requestedby;//[002]
            //        obj.SalesmanName = objDetails.SalesmanName;
            //        obj.ContactName = objDetails.ContactName;
            //        obj.ContactNo = objDetails.ContactNo;
            //        lst.Add(obj);
            //        obj = null;
            //    }
            //    lstDetails = null;
            //    return lst;
            //}
            return dt;
        }

        public static DataTable DataListNugget_ExportToExcel(System.Int32? clientId, System.Int32? BOMId)
        {
            DataTable dt = Rebound.GlobalTrader.DAL.SiteProvider.BOM.DataListNugget_ExportToExcel(clientId, BOMId);
            return dt;
        }
        //[003] end

        //[004]start
        /// <summary>
        /// DropDown
        /// Calls [usp_dropdown_ClientBOMStatus]
        /// </summary>
        public static List<BOM> GetDropDownStatus(System.String strSection)
        {
            List<BOMDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.BOM.GetDropDownBOMStatus(strSection);
            if (lstDetails == null)
            {
                return new List<BOM>();
            }
            else
            {
                List<BOM> lst = new List<BOM>();
                foreach (BOMDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.BOM obj = new Rebound.GlobalTrader.BLL.BOM();
                    obj.BOMStatusId = objDetails.BOMStatusId;
                    obj.BOMStatusName = objDetails.BOMStatusName;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }
        //[004]end

        //[005] start
        /// <summary>
        /// GetForPage
        /// Calls [usp_select_ClientBOMImport_for_Page]
        /// </summary>
        public static BOM GetForClientBOMPage(System.Int32? bomId)
        {
            Rebound.GlobalTrader.DAL.BOMDetails objDetails = Rebound.GlobalTrader.DAL.SiteProvider.BOM.GetForClientBOMPage(bomId);
            if (objDetails == null)
            {
                return null;
            }
            else
            {
                BOM obj = new BOM();
                obj.ClientBOMId = objDetails.ClientBOMId;
                obj.ClientNo = objDetails.ClientNo;
                obj.ClientBOMCode = objDetails.ClientBOMCode;
                obj.ClientBOMName = objDetails.ClientBOMName;
                obj.Notes = objDetails.Notes;
                obj.CompanyNo = objDetails.CompanyNo;
                obj.ContactNo = objDetails.ContactNo;
                obj.CurrencyNo = objDetails.CurrencyNo;
                obj.Salesman = objDetails.Salesman;
                obj.Status = objDetails.BOMStatusId;
                obj.ImportDate = objDetails.ImportDate;
                obj.Inactive = objDetails.Inactive;
                obj.UpdatedBy = objDetails.UpdatedBy;
                obj.DLUP = objDetails.DLUP;
                objDetails = null;
                return obj;
            }
        }

        /// <summary>
        /// GetForPage
        /// Calls [usp_select_ClientBOMImport_Details]
        /// </summary>
        public static BOM GetClientBOMDetails(System.Int32? bomId, Int32? loginId)
        {
            Rebound.GlobalTrader.DAL.BOMDetails objDetails = Rebound.GlobalTrader.DAL.SiteProvider.BOM.GetClientBOMDetails(bomId, loginId);
            if (objDetails == null)
            {
                return null;
            }
            else
            {
                BOM obj = new BOM();
                obj.ClientBOMId = objDetails.ClientBOMId;
                obj.ClientNo = objDetails.ClientNo;
                obj.ClientBOMCode = objDetails.ClientBOMCode;
                obj.ClientBOMName = objDetails.ClientBOMName;
                obj.Notes = objDetails.Notes;
                obj.CompanyName = objDetails.CompanyName;
                obj.ContactName = objDetails.ContactName;
                obj.CompanyNo = objDetails.CompanyNo;
                obj.ContactNo = objDetails.ContactNo;
                obj.CurrencyNo = objDetails.CurrencyNo;
                obj.CurrencyName = objDetails.CurrencyName;
                obj.Salesman = objDetails.Salesman;
                obj.SalesmanId = objDetails.SalesmanId;
                obj.BOMStatus = objDetails.BOMStatus;
                obj.ImportDate = objDetails.ImportDate;
                obj.Inactive = objDetails.Inactive;
                obj.UpdatedBy = objDetails.UpdatedBy;
                obj.DLUP = objDetails.DLUP;
                obj.RecordsProcessed = objDetails.RecordsProcessed;
                obj.RecordsRemaining = objDetails.RecordsRemaining;
                obj.BOMId = objDetails.BOMId;
                obj.BOMName = objDetails.BOMName;
                obj.Status = objDetails.Status;

                objDetails = null;
                return obj;
            }
        }
        //[005] end

        //[006] start
        /// <summary>
        /// Insert (without parameters)
        /// Calls [usp_insert_ClientBOM]
        /// </summary>
        public Int32 InsertClientBOM()
        {
            String ValidationMessage = null;
            int creditLineId = Rebound.GlobalTrader.DAL.SiteProvider.BOM.InsertClientBOM(ClientNo, BOMName, Notes, BOMCode, UpdatedBy, CompanyNo, ContactNo, Inactive, Status, CurrencyNo, Salesman, out ValidationMessage);
            this.ValidationMessage = ValidationMessage;
            return creditLineId;
        }

        /// <summary>
        /// Update (without parameters)
        /// Calls [usp_update_ClientBOM]
        /// </summary>
        public bool UpdateClientBOM()
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.BOM.UpdateClientBOM(ClientBOMId, ClientNo, ClientBOMName, Notes, ClientBOMCode, Inactive, UpdatedBy, CompanyNo, ContactNo, CurrencyNo, Salesman);
        }

        /// <summary>
        /// usp_complete_ClientBOM
        /// </summary>
        /// <param name="ClientBOMId"></param>
        /// <param name="updatedBy"></param>
        /// <returns></returns>
        public static bool ClientBOMMarkComplete(System.Int32? ClientBOMId, System.Int32? updatedBy)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.BOM.ClientBOMMarkComplete(ClientBOMId, updatedBy);
        }
        // usp_ClientBom_SaveAsHUBRFQ
        /// </summary>
        /// <param name="ClientBOMId"></param>
        /// <param name="updatedBy"></param>
        /// <returns></returns>
        public static bool SaveAsHUBRFQ(System.Int32? ClientBOMId, System.Int32? updatedBy, out string errorMessage)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.BOM.SaveAsHUBRFQ(ClientBOMId, updatedBy, out errorMessage);
        }

        /// <summary>
        /// usp_update_SourcingApproval
        /// </summary>
        /// <param name="bomId"></param>
        /// <param name="sourcingNo"></param>
        /// <param name="strStatus"></param>
        /// <param name="updatedBy"></param>
        /// <param name="message"></param>
        /// <param name="approvedStatus"></param>
        /// <returns></returns>
        public static bool UpdateApprovalStatus(System.Int32? bomId, System.Int32? sourcingNo, System.String strStatus, System.Int32? updatedBy, out string message, out string approvedStatus)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.BOM.UpdateApprovalStatus(bomId, sourcingNo, strStatus, updatedBy, out message, out approvedStatus);
        }

        //PVV Question Answer Saving Start hear
        /// <summary>
        /// Update
        /// Calls [usp_update_BOM]
        /// </summary>
        public static bool UpdatePVV(System.Int32? bomId, System.Int32? clientNo, System.String bomName, System.String notes, System.String bomCode, System.Boolean? inactive, System.Int32? updatedBy, System.Int32? companyId, System.Int32? contactId, System.Int32? currencyNo, System.String currentSupplier, System.DateTime? quoteRequired, System.Boolean? AS9120, System.Int32? contact2Id)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.BOM.UpdatePVV(bomId, clientNo, bomName, notes, bomCode, inactive, updatedBy, companyId, contactId, currencyNo, currentSupplier, quoteRequired, AS9120, contact2Id);
        }
        /// <summary>
        /// Update (without parameters)
        /// Calls [usp_update_BOM]
        /// </summary>
        public bool UpdatePVV()
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.BOM.UpdatePVV(BOMId, ClientNo, BOMName, Notes, BOMCode, Inactive, UpdatedBy, CompanyNo, ContactNo, CurrencyNo, CurrentSupplier, QuoteRequired, AS9120, Contact2Id);
        }

        public bool UpdatePVV(string generatedBOMId)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.BOM.UpdatePVV(generatedBOMId,BOMId);

        }
        public bool UpdatePVVTemp(string generatedId)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.BOM.UpdatePVVTemp(ClientNo, Notes, UpdatedBy, generatedId);
        }
        //

        /// <summary>
        /// Get
        /// Calls [usp_select_BOM]
        /// </summary>
        public static BOM GetPVV(System.Int32? bomId)
        {
            Rebound.GlobalTrader.DAL.BOMDetails objDetails = Rebound.GlobalTrader.DAL.SiteProvider.BOM.GetPVV(bomId);
            if (objDetails == null)
            {
                return null;
            }
            else
            {
                BOM obj = new BOM();
                obj.BOMId = objDetails.BOMId;
                //obj.ClientNo = objDetails.ClientNo;
                obj.BOMName = objDetails.BOMName;
                obj.CompanyNo = objDetails.CompanyNo;
                obj.CompanyName = objDetails.CompanyName;
                obj.CompanyType = objDetails.CompanyType;
                obj.Notes = objDetails.Notes;
                obj.BOMCode = objDetails.BOMCode;
                obj.Inactive = objDetails.Inactive;
                obj.ContactNo = objDetails.ContactNo;
                obj.ContactName = objDetails.ContactName;
                obj.RequestToPOHubBy = objDetails.RequestToPOHubBy;
                obj.DateRequestToPOHub = objDetails.DateRequestToPOHub;

                obj.PVVQuestionId = objDetails.PVVQuestionId;
                obj.PVVQuestionName = objDetails.PVVQuestionName;
                //edit
                obj.PVVAnswerId = objDetails.PVVAnswerId;
                obj.PVVAnswerName = objDetails.PVVAnswerName;
                obj.HUBRFQNo = objDetails.HUBRFQNo;
                objDetails = null;
                return obj;
            }
        }


        /// <summary>
        /// UpdateDelete
        /// Calls [usp_update_BOM_Delete]
        /// </summary>
        public static bool PVVDelete(System.Int32? BOMId, System.Int32? updatedBy)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.BOM.PVVDelete(BOMId, updatedBy);
        }

        public static bool HasImportFile(int BOMId)
        {
            return SiteProvider.BOM.HasImportFile(BOMId);
        }

        public static DataTable GetUpdateBOMData(int BOMId)
        {
            return SiteProvider.BOM.GetUpdateBOMData(BOMId);
        }

        public static bool PVVDeleteTemp(string GeneratedId, System.Int32? updatedBy)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.BOM.PVVDeleteTemp(GeneratedId, updatedBy);
        }
        #endregion
        //[006] end
    }
}
