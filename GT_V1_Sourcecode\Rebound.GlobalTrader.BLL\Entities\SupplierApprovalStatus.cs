﻿/*
Marker     Changed by               Date         Remarks
[001]      A<PERSON><PERSON><PERSON>           29/07/2021   Implement a new dropdown for supplier Approval Status
*/
using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;
using Rebound.GlobalTrader.DAL;
using Rebound.GlobalTrader.BLL;

namespace Rebound.GlobalTrader.BLL
{
    public class SupplierApprovalStatus : BizObject
    {
        #region Properties

        /// <summary>
        /// SupplierApprovalStatusId
        /// </summary>
        public System.Int32 SupplierApprovalStatusId { get; set; }
        /// <summary>
        /// Name
        /// </summary>
        public System.String Name { get; set; }

        #endregion

        #region Methods

        
        /// <summary>
        /// DropDown
        /// Calls [usp_dropdown_PurchaseOrderStatus]
        /// </summary>
        public static List<SupplierApprovalStatus> DropDown()
        {
            List<SupplierApprovalStatusDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.SupplierApprovalStatus.DropDown();
            if (lstDetails == null)
            {
                return new List<SupplierApprovalStatus>();
            }
            else
            {
                List<SupplierApprovalStatus> lst = new List<SupplierApprovalStatus>();
                foreach (SupplierApprovalStatusDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.SupplierApprovalStatus obj = new Rebound.GlobalTrader.BLL.SupplierApprovalStatus();
                    obj.SupplierApprovalStatusId = objDetails.SupplierApprovalStatusId;
                    obj.Name = objDetails.Name;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }

        #endregion
    }
}
