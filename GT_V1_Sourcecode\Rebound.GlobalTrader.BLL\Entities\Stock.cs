﻿/* Marker     changed by      date         Remarks
 [001]      V<PERSON><PERSON> kumar     17/11/2011  ESMS Ref:23 - PO No. and Crma No. should also be displayed 
 [002]      Vinay           07/05/2012   This need to upload pdf document for stock section
 [003]      Vinay           01/08/2012   Delete UnAllocated Stock Bug
 [004]      Vinay           15/10/2012   Display company type in stock grid
 [005]      Vinay           08/04/2014   CR:- Stock Provision
 [006]      Vinay           30/07/2015   ESMS Ticket No : 255
 [007]      <PERSON>     17/08/2021   (GTDP-198) Controlling specific product groups for purchasing
 [008]      Ravi            13/09/2023   RP-2340 AS6081
 [009]      Ravi            19-09-2023   RP-2338  AS6081 Search/Filter functionality on different pages
 [008]      A<PERSON><PERSON><PERSON>  17/10/2022   (RP-31) Add new methods for this lot chnages.
 [009]      <PERSON>     21/02/2023   (RP-31) Ticket No: 217.
 */

using Rebound.GlobalTrader.DAL;
using System;
using System.Collections.Generic;
using System.Data;

namespace Rebound.GlobalTrader.BLL
{
    public partial class Stock : BizObject
    {

        #region Properties

        protected static DAL.StockElement Settings
        {
            get { return Globals.Settings.Stocks; }
        }

        /// <summary>
        /// StockId
        /// </summary>
        public System.Int32 StockId { get; set; }
        /// <summary>
        /// FullPart
        /// </summary>
        public System.String FullPart { get; set; }
        /// <summary>
        /// Part
        /// </summary>
        public System.String Part { get; set; }
        /// <summary>
        /// ManufacturerNo
        /// </summary>
        public System.Int32? ManufacturerNo { get; set; }
        /// <summary>
        /// DateCode
        /// </summary>
        public System.String DateCode { get; set; }
        /// <summary>
        /// PackageNo
        /// </summary>
        public System.Int32? PackageNo { get; set; }
        /// <summary>
        /// WarehouseNo
        /// </summary>
        public System.Int32? WarehouseNo { get; set; }
        /// <summary>
        /// ClientNo
        /// </summary>
        public System.Int32 ClientNo { get; set; }
        /// <summary>
        /// QualityControlNotes
        /// </summary>
        public System.String QualityControlNotes { get; set; }
        /// <summary>
        /// PurchaseOrderNo
        /// </summary>
        public System.Int32? PurchaseOrderNo { get; set; }
        /// <summary>
        /// PurchaseOrderLineNo
        /// </summary>
        public System.Int32? PurchaseOrderLineNo { get; set; }
        /// <summary>
        /// QuantityInStock
        /// </summary>
        public System.Int32 QuantityInStock { get; set; }
        /// <summary>
        /// QuantityOnOrder
        /// </summary>
        public System.Int32 QuantityOnOrder { get; set; }
        /// <summary>
        /// Location
        /// </summary>
        public System.String Location { get; set; }
        /// <summary>
        /// ProductNo
        /// </summary>
        public System.Int32? ProductNo { get; set; }
        /// <summary>
        /// ResalePrice
        /// </summary>
        public System.Double? ResalePrice { get; set; }
        /// <summary>
        /// Unavailable
        /// </summary>
        public System.Boolean Unavailable { get; set; }
        /// <summary>
        /// LotNo
        /// </summary>
        public System.Int32? LotNo { get; set; }
        /// <summary>
        /// LandedCost
        /// </summary>
        public System.Double? LandedCost { get; set; }
        /// <summary>
        /// SupplierPart
        /// </summary>
        public System.String SupplierPart { get; set; }
        /// <summary>
        /// ROHS
        /// </summary>
        public System.Byte? ROHS { get; set; }
        /// <summary>
        /// PackageUnit
        /// </summary>
        public System.Int32? PackageUnit { get; set; }
        /// <summary>
        /// StockKeepingUnit
        /// </summary>
        public System.Int32? StockKeepingUnit { get; set; }
        /// <summary>
        /// CustomerRMANo
        /// </summary>
        public System.Int32? CustomerRMANo { get; set; }
        /// <summary>
        /// CustomerRMALineNo
        /// </summary>
        public System.Int32? CustomerRMALineNo { get; set; }
        /// <summary>
        /// GoodsInLineNo
        /// </summary>
        public System.Int32? GoodsInLineNo { get; set; }
        /// <summary>
        /// UpdatedBy
        /// </summary>
        public System.Int32? UpdatedBy { get; set; }
        /// <summary>
        /// DLUP
        /// </summary>
        public System.DateTime DLUP { get; set; }
        /// <summary>
        /// FullSupplierPart
        /// </summary>
        public System.String FullSupplierPart { get; set; }
        /// <summary>
        /// CountryOfManufacture
        /// </summary>
        public System.Int32? CountryOfManufacture { get; set; }
        /// <summary>
        /// PartMarkings
        /// </summary>
        public System.String PartMarkings { get; set; }
        /// <summary>
        /// CountingMethodNo
        /// </summary>
        public System.Int32? CountingMethodNo { get; set; }
        /// <summary>
        /// ManufacturerCode
        /// </summary>
        public System.String ManufacturerCode { get; set; }
        /// <summary>
        /// QuantityAllocated
        /// </summary>
        public System.Int32 QuantityAllocated { get; set; }
        /// <summary>
        /// WarehouseName
        /// </summary>
        public System.String WarehouseName { get; set; }
        /// <summary>
        /// LotName
        /// </summary>
        public System.String LotName { get; set; }
        /// <summary>
        /// SupplierNo
        /// </summary>
        public System.Int32? SupplierNo { get; set; }
        /// <summary>
        /// SupplierName
        /// </summary>
        public System.String SupplierName { get; set; }
        /// <summary>
        /// RowNum
        /// </summary>
        public System.Int64? RowNum { get; set; }
        /// <summary>
        /// QuantityAvailable
        /// </summary>
        public System.Int32? QuantityAvailable { get; set; }
        /// <summary>
        /// StatusNo
        /// </summary>
        public System.Int32? StatusNo { get; set; }
        /// <summary>
        /// RowCnt
        /// </summary>
        public System.Int32? RowCnt { get; set; }
        /// <summary>
        /// PODeliveryDate
        /// </summary>
        public System.DateTime? PODeliveryDate { get; set; }
        /// <summary>
        /// PurchaseOrderNumber
        /// </summary>
        public System.Int32? PurchaseOrderNumber { get; set; }
        /// <summary>
        /// CustomerRMANumber
        /// </summary>
        public System.Int32? CustomerRMANumber { get; set; }
        /// <summary>
        /// CustomerRMADate
        /// </summary>
        public System.DateTime? CustomerRMADate { get; set; }
        /// <summary>
        /// PackageName
        /// </summary>
        public System.String PackageName { get; set; }
        /// <summary>
        /// PackageDescription
        /// </summary>
        public System.String PackageDescription { get; set; }
        /// <summary>
        /// ProductName
        /// </summary>
        public System.String ProductName { get; set; }
        /// <summary>
        /// ProductDescription
        /// </summary>
        public System.String ProductDescription { get; set; }
        /// <summary>
        /// ManufacturerName
        /// </summary>
        public System.String ManufacturerName { get; set; }
        /// <summary>
        /// CurrencyCode
        /// </summary>
        public System.String CurrencyCode { get; set; }
        /// <summary>
        /// BaseCurrency of stock related client
        /// </summary>
        public System.String ClientBaseCurrencyCode { get; set; }
        /// <summary>
        /// LotCode
        /// </summary>
        public System.String LotCode { get; set; }
        /// <summary>
        /// Buyer
        /// </summary>
        public System.Int32? Buyer { get; set; }
        /// <summary>
        /// BuyerName
        /// </summary>
        public System.String BuyerName { get; set; }
        /// <summary>
        /// GoodsInNo
        /// </summary>
        public System.Int32? GoodsInNo { get; set; }
        /// <summary>
        /// GoodsInPrice
        /// </summary>
        public System.Double? GoodsInPrice { get; set; }
        /// <summary>
        /// GoodsInShipInCost
        /// </summary>
        public System.Double? GoodsInShipInCost { get; set; }
        /// <summary>
        /// GoodsInNumber
        /// </summary>
        public System.Int32? GoodsInNumber { get; set; }
        /// <summary>
        /// GoodsInCurrencyNo
        /// </summary>
        public System.Int32? GoodsInCurrencyNo { get; set; }
        /// <summary>
        /// StockDate
        /// </summary>
        public System.DateTime StockDate { get; set; }
        /// <summary>
        /// ROHSStatus
        /// </summary>
        public System.String ROHSStatus { get; set; }
        /// <summary>
        /// CountryOfManufactureName
        /// </summary>
        public System.String CountryOfManufactureName { get; set; }
        /// <summary>
        /// PurchasePrice
        /// </summary>
        public System.Double? PurchasePrice { get; set; }
        /// <summary>
        /// CountingMethodDescription
        /// </summary>
        public System.String CountingMethodDescription { get; set; }
        /// <summary>
        /// StockLogDetail
        /// </summary>
        public System.String StockLogDetail { get; set; }
        /// <summary>
        /// StockLogChangeNotes
        /// </summary>
        public System.String StockLogChangeNotes { get; set; }
        /// <summary>
        /// StockLogReasonNo
        /// </summary>
        public System.Int32? StockLogReasonNo { get; set; }
        /// <summary>
        /// UpdateShipments
        /// </summary>
        public System.Boolean? UpdateShipments { get; set; }
        /// <summary>
        /// RelationType
        /// </summary>
        public System.String RelationType { get; set; }
        /// <summary>
        /// ClientName
        /// </summary>
        public System.String ClientName { get; set; }
        /// <summary>
        /// ClientDataVisibleToOthers
        /// </summary>
        public System.Boolean? ClientDataVisibleToOthers { get; set; }

        /// <summary>
        /// Is image available
        /// </summary>
        public System.Boolean? IsImageAvailable { get; set; }
        /// <summary>
        /// IsPDFAvailable
        /// [002] code start
        /// </summary>
        public System.Boolean? IsPDFAvailable { get; set; }
        // [002] code end

        ///[003] code start
        /// <summary>
        /// StockUnallocatedCount
        /// </summary>
        public System.Int32 StockUnallocatedCount { get; set; }
        //[003] code end

        //[004] code start
        public System.String SupplierType { get; set; }
        //[004] code end

        //[005] code start
        /// <summary>
        /// OriginalLandedCost
        /// </summary>
        public System.Double? OriginalLandedCost { get; set; }
        /// <summary>
        /// ManualStockSplitDate
        /// </summary>
        public System.DateTime? ManualStockSplitDate { get; set; }
        /// <summary>
        /// FirstStockProvisionDate
        /// </summary>
        public System.DateTime? FirstStockProvisionDate { get; set; }
        /// <summary>
        /// LastStockProvisionDate
        /// </summary>
        public System.DateTime? LastStockProvisionDate { get; set; }
        /// <summary>
        /// IsManual
        /// </summary>
        public System.Boolean? IsManual { get; set; }
        //[005] code start

        /// <summary>
        /// Stock Start Date
        /// </summary>
        public System.String StockStartDate { get; set; }

        /// <summary>
        /// LotId
        /// </summary>
        public System.Int32 LotId { get; set; }
        /// <summary>
        /// Current Landed cost.
        /// </summary>
        public System.Double? CurrentLandedCost { get; set; }

        /// <summary>
        /// New Landed cost.
        /// </summary>
        public System.Double? NewLandedCost { get; set; }
        /// <summary>
        /// POSerialNo
        /// </summary>
        public System.Int16? POSerialNo { get; set; }
        /// <summary>
		/// DivisionNo (from Table)
		/// </summary>
		public System.Int32? DivisionNo { get; set; }
        /// <summary>
        /// DivisionStatus (from Table)
        /// </summary>
        public System.Int32? DivisionStatus { get; set; }

        /// <summary>
        /// DivisionName (from Table)
        /// </summary>
        public System.String DivisionName { get; set; }




        public int? InternalPurchaseOrderNumber { get; set; }

        public int? InternalPurchaseOrderId { get; set; }

        public int? IPOSupplier { get; set; }

        public string IPOSupplierName { get; set; }

        /// <summary>
        /// POClientNo
        /// </summary>
        public System.Int32? POClientNo { get; set; }
        /// <summary>
        /// ClientLandedCost
        /// </summary>
        public System.Double? ClientLandedCost { get; set; }
        /// <summary>
        /// ClientPurchasePrice (from usp_select_Stock)
        /// </summary>
        public System.Double? ClientPurchasePrice { get; set; }
        public int? IPONo { get; set; }
        public System.Boolean? IsClientUpdate { get; set; }
        /// <summary>
        /// ClientCode
        /// </summary>
        public System.String ClientCode { get; set; }
        /// <summary>

        public int? SalesOrderNumber { get; set; }
        public System.String NPRNo { get; set; }
        public System.String CustomerPO { get; set; }

        public System.Int32? CustomerNo { get; set; }
        public System.String CustomerName { get; set; }
        public System.Int32? ClientBaseCurrencyID { get; set; }
        public System.String SerialNo { get; set; }
        public System.Int32? SerialNoId { get; set; }
        public System.String SubGroup { get; set; }
        //public System.Boolean? ReqSerialNo { get; set; }
        public System.String MSLLevel { get; set; }
        public System.Int32? CustomerRequirementId { get; set; }
        public System.Int32? CustomerRequirementNumber { get; set; }
        /// <summary>
        /// CurrencyNo
        /// </summary>
        public System.Int32? CurrencyNo { get; set; }
        //[007] code start
        public System.Boolean? IsProdHazardous { get; set; }
        public System.Boolean? IsOrderViaIPOonly { get; set; }
        //[007] code end
        public System.String SupplierMessage { get; set; }
        public System.String GeneralInspectionNotes { get; set; }
        public System.Boolean? ReqSerialNo { get; set; }
        public System.Boolean? IsLotCodesReq { get; set; }
        public System.String ActeoneTestStatus { get; set; }
        public System.String IsopropryleStatus { get; set; }
        public System.String ActeoneTest { get; set; }
        public System.String Isopropryle { get; set; }
        public System.String HICStatusName { get; set; }
        public System.String BakingLevelAdded { get; set; }
        /// <summary>
        /// IsRestrictedProduct
        /// </summary>
        public System.Boolean? IsRestrictedProduct { get; set; }
        /// <summary>
        /// PurchaseOrderNo
        /// </summary>
        public System.Int32? StockNo { get; set; }
        /// <summary>
        /// IsImport
        /// </summary>
        public System.Boolean? IsImport { get; set; }
        /// <summary>
        /// ImportSupplierNo (from Table)
        /// </summary>
        public System.Int32? ImportSupplierNo { get; set; }
        /// <summary>
		/// ImportupplierName (from Table)
		/// </summary>
		public System.String ImportupplierName { get; set; }

        /// <summary>
		/// importCompanyType (from Table)
		/// </summary>
		public System.String importCompanyType { get; set; }
        /// <summary>
        /// CompanyType (from Table)
        /// </summary>
        public System.String CompanyType { get; set; }
        /// <summary>
		/// IsImport
		/// </summary>
		public System.Boolean? isLotOnHold { get; set; }

        public System.Boolean AS6081 { get; set; }
        /// <summary>
        /// DivisionNo
        /// </summary>
        /// 
        /// <summary>
        /// ClientUPLiftPrice
        /// </summary>
        public System.Double? ClientUPLiftPrice { get; set; }
        /// <summary>
        /// HasSTO
        /// </summary>
        public System.Boolean? HasSTO { get; set; }
        public string CountryWarningMessage { get; set; }
        public bool IsHasCountryMessage { get; set; }
        #endregion

        #region Methods

        /// <summary>
        /// AutoSearch
        /// Calls [usp_autosearch_Stock]
        /// </summary>
        public static List<Stock> AutoSearch(System.Int32? clientId, System.String nameSearch)
        {
            List<StockDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.Stock.AutoSearch(clientId, nameSearch);
            if (lstDetails == null)
            {
                return new List<Stock>();
            }
            else
            {
                List<Stock> lst = new List<Stock>();
                foreach (StockDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.Stock obj = new Rebound.GlobalTrader.BLL.Stock();
                    obj.StockId = objDetails.StockId;
                    obj.Part = objDetails.Part;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }
        /// <summary>
        /// CountForClient
        /// Calls [usp_count_Stock_for_Client]
        /// </summary>
        public static Int32 CountForClient(System.Int32? clientId)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.CountForClient(clientId);
        }       /// <summary>
                /// DataListNugget
                /// Calls [usp_datalistnugget_Stock]
                /// </summary>
        public static List<Stock> DataListNugget(System.Int32? clientId, System.Int32? orderBy, System.Int32? sortDir, System.Int32? pageIndex, System.Int32? pageSize, System.Boolean? quarantine, System.String partSearch, System.Int32? lotNo, System.Int32? purchaseOrderNoLo, System.Int32? purchaseOrderNoHi, System.String supplierPartSearch, System.String supplierNameSearch, System.String locationSearch, System.Int32? warehouseNo, System.Boolean? recentOnly, System.Int32? customerRmaNoLo, System.Int32? customerRmaNoHi, System.Boolean? includeZeroStock, System.Int32? clientSearch, int? IsPoHub, Boolean IsGlobalLogin, System.Int32? stockNoLo, System.Int32? stockNoHi, System.Boolean? AS6081)
        {
            List<StockDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.Stock.DataListNugget(clientId, orderBy, sortDir, pageIndex, pageSize, quarantine, partSearch, lotNo, purchaseOrderNoLo, purchaseOrderNoHi, supplierPartSearch, supplierNameSearch, locationSearch, warehouseNo, recentOnly, customerRmaNoLo, customerRmaNoHi, includeZeroStock, clientSearch, IsPoHub, IsGlobalLogin, stockNoLo, stockNoHi, AS6081); //[009]
            if (lstDetails == null)
            {
                return new List<Stock>();
            }
            else
            {
                List<Stock> lst = new List<Stock>();
                foreach (StockDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.Stock obj = new Rebound.GlobalTrader.BLL.Stock();
                    obj.StockId = objDetails.StockId;
                    obj.Part = objDetails.Part;
                    obj.ROHS = objDetails.ROHS;
                    obj.ManufacturerNo = objDetails.ManufacturerNo;
                    obj.ManufacturerCode = objDetails.ManufacturerCode;
                    obj.QuantityInStock = objDetails.QuantityInStock;
                    obj.QuantityOnOrder = objDetails.QuantityOnOrder;
                    obj.QuantityAllocated = objDetails.QuantityAllocated;
                    obj.WarehouseNo = objDetails.WarehouseNo;
                    obj.WarehouseName = objDetails.WarehouseName;
                    obj.Location = objDetails.Location;
                    obj.LotNo = objDetails.LotNo;
                    obj.LotName = objDetails.LotName;
                    obj.SupplierPart = objDetails.SupplierPart;
                    obj.SupplierNo = objDetails.SupplierNo;
                    obj.SupplierName = objDetails.SupplierName;
                    obj.RowNum = objDetails.RowNum;
                    obj.QuantityAvailable = objDetails.QuantityAvailable;
                    obj.StatusNo = objDetails.StatusNo;
                    obj.RowCnt = objDetails.RowCnt;
                    // [001]Code Start 
                    obj.PurchaseOrderNumber = objDetails.PurchaseOrderNumber;
                    obj.PurchaseOrderNo = objDetails.PurchaseOrderNo;
                    obj.CustomerRMANumber = objDetails.CustomerRMANumber;
                    obj.CustomerRMANo = objDetails.CustomerRMANo;
                    obj.ClientName = objDetails.ClientName;
                    obj.ClientNo = objDetails.ClientNo;
                    // obj.SupplierMessage = objDetails.SupplierMessage;
                    // [001]Code End 
                    obj.StockNo = objDetails.StockNo;
                    obj.AS6081 = objDetails.AS6081 ?? false; //[009]
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }
        /// <summary>
        /// Delete
        /// Calls [usp_delete_Stock]
        /// </summary>
        public static bool Delete(System.Int32? stockId)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.Delete(stockId);
        }
        /// <summary>
        /// DeleteUnallocatedForLot
        /// Calls [usp_delete_Stock_Unallocated_for_Lot]
        /// </summary>
        public static bool DeleteUnallocatedForLot(System.Int32? lotNo)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.DeleteUnallocatedForLot(lotNo);
        }
        /// <summary>
        /// Insert
        /// Calls [usp_insert_Stock]
        /// </summary>
        public static Int32 Insert(System.String part, System.Int32? manufacturerNo, System.String dateCode, System.Int32? packageNo, System.Int32? warehouseNo, System.Int32? clientNo, System.String qualityControlNotes, System.Int32? purchaseOrderNo, System.Int32? purchaseOrderLineNo, System.Int32? customerRmaNo, System.Int32? customerRmaLineNo, System.Int32? quantityInStock, System.Int32? quantityOnOrder, System.Int32? productNo, System.Double? resalePrice, System.Boolean? unavailable, System.Int32? lotNo, System.Double? landedCost, System.String supplierPart, System.Byte? rohs, System.Int32? packageUnit, System.Int32? stockKeepingUnit, System.String stockLogChangeNotes, System.String location, System.Int32? countryOfManufacture, System.String partMarkings, System.Int32? countingMethodNo, System.Int32? updatedBy, System.Int32? divisionNo, System.String mslLevel, System.Boolean AS6081, System.Double? ClientUPLiftPrice)
        {
            Int32 objReturn = Rebound.GlobalTrader.DAL.SiteProvider.Stock.Insert(part, manufacturerNo, dateCode, packageNo, warehouseNo, clientNo, qualityControlNotes, purchaseOrderNo, purchaseOrderLineNo, customerRmaNo, customerRmaLineNo, quantityInStock, quantityOnOrder, productNo, resalePrice, unavailable, lotNo, landedCost, supplierPart, rohs, packageUnit, stockKeepingUnit, stockLogChangeNotes, location, countryOfManufacture, partMarkings, countingMethodNo, updatedBy, divisionNo, mslLevel, AS6081, ClientUPLiftPrice);
            return objReturn;
        }
        /// <summary>
        /// Insert (without parameters)
        /// Calls [usp_insert_Stock]
        /// </summary>
        public Int32 Insert()
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.Insert(Part, ManufacturerNo, DateCode, PackageNo, WarehouseNo, ClientNo, QualityControlNotes, PurchaseOrderNo, PurchaseOrderLineNo, CustomerRMANo, CustomerRMALineNo, QuantityInStock, QuantityOnOrder, ProductNo, ResalePrice, Unavailable, LotNo, LandedCost, SupplierPart, ROHS, PackageUnit, StockKeepingUnit, StockLogChangeNotes, Location, CountryOfManufacture, PartMarkings, CountingMethodNo, UpdatedBy, DivisionNo, MSLLevel, AS6081, ClientUPLiftPrice);
        }
        /// <summary>
        /// InsertIdentityOff
        /// Calls [usp_insert_Stock_Identity_Off]
        /// </summary>
        public static Int32 InsertIdentityOff(System.Int32? stockId, System.String part, System.Int32? manufacturerNo, System.String dateCode, System.Int32? packageNo, System.Int32? warehouseNo, System.Int32? clientNo, System.String qualityControlNotes, System.Int32? purchaseOrderNo, System.Int32? purchaseOrderLineNo, System.Int32? customerRmaNo, System.Int32? customerRmaLineNo, System.Int32? goodsInLineNo, System.Int32? quantityInStock, System.Int32? quantityOnOrder, System.Int32? productNo, System.Double? resalePrice, System.Boolean? unavailable, System.Int32? lotNo, System.Double? landedCost, System.String supplierPart, System.Byte? rohs, System.Int32? packageUnit, System.Int32? stockKeepingUnit, System.String stockLogChangeNotes, System.String location, System.Int32? updatedBy)
        {
            Int32 objReturn = Rebound.GlobalTrader.DAL.SiteProvider.Stock.InsertIdentityOff(stockId, part, manufacturerNo, dateCode, packageNo, warehouseNo, clientNo, qualityControlNotes, purchaseOrderNo, purchaseOrderLineNo, customerRmaNo, customerRmaLineNo, goodsInLineNo, quantityInStock, quantityOnOrder, productNo, resalePrice, unavailable, lotNo, landedCost, supplierPart, rohs, packageUnit, stockKeepingUnit, stockLogChangeNotes, location, updatedBy);
            return objReturn;
        }
        /// <summary>
        /// InsertSplit
        /// Calls [usp_insert_Stock_Split]
        /// </summary>
        public static Int32 InsertSplit(System.Int32? stockId, System.Int32? quantitySplit, System.String location, System.Int32? updatedBy)
        {
            Int32 objReturn = Rebound.GlobalTrader.DAL.SiteProvider.Stock.InsertSplit(stockId, quantitySplit, location, updatedBy);
            return objReturn;
        }
        /// <summary>
        /// ItemSearch
        /// Calls [usp_itemsearch_Stock]
        /// </summary>
        public static List<Stock> ItemSearch(System.Int32? clientId, System.Int32? orderBy, System.Int32? sortDir, System.Int32? pageIndex, System.Int32? pageSize, System.String partSearch, System.Boolean? forRmAs, System.Int32? supplierRmaNo, System.Boolean? includeQuarantined, System.Boolean? includeLotsOnHold, System.Int32? poNoLo, System.Int32? poNoHi, System.Int32? crmaNoLo, System.Int32? crmaNoHi, System.Int32? warehouseNo, System.String location, System.Int32? incLockCustNo)
        {
            List<StockDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.Stock.ItemSearch(clientId, orderBy, sortDir, pageIndex, pageSize, partSearch, forRmAs, supplierRmaNo, includeQuarantined, includeLotsOnHold, poNoLo, poNoHi, crmaNoLo, crmaNoHi, warehouseNo, location, incLockCustNo);
            if (lstDetails == null)
            {
                return new List<Stock>();
            }
            else
            {
                List<Stock> lst = new List<Stock>();
                foreach (StockDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.Stock obj = new Rebound.GlobalTrader.BLL.Stock();
                    obj.ClientNo = objDetails.ClientNo;
                    obj.StockId = objDetails.StockId;
                    obj.Part = objDetails.Part;
                    obj.SupplierPart = objDetails.SupplierPart;
                    obj.QuantityInStock = objDetails.QuantityInStock;
                    obj.QuantityOnOrder = objDetails.QuantityOnOrder;
                    obj.QuantityAvailable = objDetails.QuantityAvailable;
                    obj.QuantityAllocated = objDetails.QuantityAllocated;
                    obj.LandedCost = objDetails.LandedCost;
                    obj.Unavailable = objDetails.Unavailable;
                    obj.SupplierName = objDetails.SupplierName;
                    obj.PODeliveryDate = objDetails.PODeliveryDate;
                    obj.PurchaseOrderNumber = objDetails.PurchaseOrderNumber;
                    obj.CustomerRMANumber = objDetails.CustomerRMANumber;
                    obj.CustomerRMADate = objDetails.CustomerRMADate;
                    obj.WarehouseName = objDetails.WarehouseName;
                    obj.Location = objDetails.Location;
                    obj.RowNum = objDetails.RowNum;
                    obj.RowCnt = objDetails.RowCnt;
                    obj.POSerialNo = objDetails.POSerialNo;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }
        /// <summary>
        /// ItemSearch
        /// Calls [usp_itemsearch_IpoStock]
        /// </summary>
        public static List<Stock> IpoItemSearch(System.Int32? clientId, System.Int32? orderBy, System.Int32? sortDir, System.Int32? pageIndex, System.Int32? pageSize, System.String partSearch, System.Boolean? forRmAs, System.Int32? supplierRmaNo, System.Boolean? includeQuarantined, System.Boolean? includeLotsOnHold, System.Int32? poNoLo, System.Int32? poNoHi, System.Int32? crmaNoLo, System.Int32? crmaNoHi, System.Int32? warehouseNo, System.String location, System.Int32? incLockCustNo, int? salesOrderNo, System.Boolean? stopNONIpoStock)
        {
            List<StockDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.Stock.IpoItemSearch(clientId, orderBy, sortDir, pageIndex, pageSize, partSearch, forRmAs, supplierRmaNo, includeQuarantined, includeLotsOnHold, poNoLo, poNoHi, crmaNoLo, crmaNoHi, warehouseNo, location, incLockCustNo, salesOrderNo, stopNONIpoStock);
            if (lstDetails == null)
            {
                return new List<Stock>();
            }
            else
            {
                List<Stock> lst = new List<Stock>();
                foreach (StockDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.Stock obj = new Rebound.GlobalTrader.BLL.Stock();
                    obj.ClientNo = objDetails.ClientNo;
                    obj.StockId = objDetails.StockId;
                    obj.Part = objDetails.Part;
                    obj.SupplierPart = objDetails.SupplierPart;
                    obj.QuantityInStock = objDetails.QuantityInStock;
                    obj.QuantityOnOrder = objDetails.QuantityOnOrder;
                    obj.QuantityAvailable = objDetails.QuantityAvailable;
                    obj.QuantityAllocated = objDetails.QuantityAllocated;
                    obj.LandedCost = objDetails.LandedCost;
                    obj.Unavailable = objDetails.Unavailable;
                    obj.SupplierName = objDetails.SupplierName;
                    obj.PODeliveryDate = objDetails.PODeliveryDate;
                    obj.PurchaseOrderNumber = objDetails.PurchaseOrderNumber;
                    obj.CustomerRMANumber = objDetails.CustomerRMANumber;
                    obj.CustomerRMADate = objDetails.CustomerRMADate;
                    obj.WarehouseName = objDetails.WarehouseName;
                    obj.Location = objDetails.Location;
                    obj.RowNum = objDetails.RowNum;
                    obj.RowCnt = objDetails.RowCnt;
                    obj.POSerialNo = objDetails.POSerialNo;
                    obj.IPONo = objDetails.IPONo;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }
        /// <summary>
        /// Get
        /// Calls [usp_select_Stock]
        /// </summary>
        public static Stock Get(System.Int32? stockId)
        {
            Rebound.GlobalTrader.DAL.StockDetails objDetails = Rebound.GlobalTrader.DAL.SiteProvider.Stock.Get(stockId);
            if (objDetails == null)
            {
                return null;
            }
            else
            {
                Stock obj = new Stock();
                obj.StockId = objDetails.StockId;
                obj.FullPart = objDetails.FullPart;
                obj.Part = objDetails.Part;
                obj.ManufacturerNo = objDetails.ManufacturerNo;
                obj.DateCode = objDetails.DateCode;
                obj.PackageNo = objDetails.PackageNo;
                obj.WarehouseNo = objDetails.WarehouseNo;
                obj.ClientNo = objDetails.ClientNo;
                obj.QualityControlNotes = objDetails.QualityControlNotes;
                obj.PurchaseOrderNo = objDetails.PurchaseOrderNo;
                obj.PurchaseOrderLineNo = objDetails.PurchaseOrderLineNo;
                obj.QuantityInStock = objDetails.QuantityInStock;
                obj.QuantityOnOrder = objDetails.QuantityOnOrder;
                obj.Location = objDetails.Location;
                obj.ProductNo = objDetails.ProductNo;
                obj.ResalePrice = objDetails.ResalePrice;
                obj.Unavailable = objDetails.Unavailable;
                obj.LotNo = objDetails.LotNo;
                obj.LandedCost = objDetails.LandedCost;
                obj.SupplierPart = objDetails.SupplierPart;
                obj.ROHS = objDetails.ROHS;
                obj.PackageUnit = objDetails.PackageUnit;
                obj.StockKeepingUnit = objDetails.StockKeepingUnit;
                obj.CustomerRMANo = objDetails.CustomerRMANo;
                obj.CustomerRMALineNo = objDetails.CustomerRMALineNo;
                obj.GoodsInLineNo = objDetails.GoodsInLineNo;
                obj.UpdatedBy = objDetails.UpdatedBy;
                obj.DLUP = objDetails.DLUP;
                obj.FullSupplierPart = objDetails.FullSupplierPart;
                obj.CountryOfManufacture = objDetails.CountryOfManufacture;
                obj.PackageName = objDetails.PackageName;
                obj.PackageDescription = objDetails.PackageDescription;
                obj.WarehouseName = objDetails.WarehouseName;
                obj.ProductName = objDetails.ProductName;
                obj.ProductDescription = objDetails.ProductDescription;
                obj.ManufacturerName = objDetails.ManufacturerName;
                obj.ManufacturerCode = objDetails.ManufacturerCode;
                obj.PurchaseOrderNumber = objDetails.PurchaseOrderNumber;
                obj.CustomerRMANumber = objDetails.CustomerRMANumber;
                obj.CurrencyCode = objDetails.CurrencyCode;
                obj.SupplierName = objDetails.SupplierName;
                obj.LotName = objDetails.LotName;
                obj.LotCode = objDetails.LotCode;
                obj.SupplierNo = objDetails.SupplierNo;
                obj.Buyer = objDetails.Buyer;
                obj.BuyerName = objDetails.BuyerName;
                obj.QuantityAllocated = objDetails.QuantityAllocated;
                obj.QuantityAvailable = objDetails.QuantityAvailable;
                obj.GoodsInNo = objDetails.GoodsInNo;
                obj.GoodsInPrice = objDetails.GoodsInPrice;
                obj.GoodsInShipInCost = objDetails.GoodsInShipInCost;
                obj.GoodsInNumber = objDetails.GoodsInNumber;
                obj.GoodsInCurrencyNo = objDetails.GoodsInCurrencyNo;
                obj.StockDate = objDetails.StockDate;
                obj.ROHSStatus = objDetails.ROHSStatus;
                obj.CountryOfManufactureName = objDetails.CountryOfManufactureName;
                obj.PurchasePrice = objDetails.PurchasePrice;
                obj.PartMarkings = objDetails.PartMarkings;
                obj.CountingMethodNo = objDetails.CountingMethodNo;
                obj.CountingMethodDescription = objDetails.CountingMethodDescription;
                obj.StatusNo = objDetails.StatusNo;
                obj.StockLogDetail = objDetails.StockLogDetail;
                obj.StockLogChangeNotes = objDetails.StockLogChangeNotes;
                obj.StockLogReasonNo = objDetails.StockLogReasonNo;
                obj.UpdateShipments = objDetails.UpdateShipments;
                obj.StockStartDate = objDetails.StockStartDate;
                obj.OriginalLandedCost = objDetails.OriginalLandedCost;
                obj.FirstStockProvisionDate = objDetails.FirstStockProvisionDate;
                obj.LastStockProvisionDate = objDetails.LastStockProvisionDate;
                obj.ManualStockSplitDate = objDetails.ManualStockSplitDate;
                obj.IsManual = objDetails.IsManual;
                //[006] code start
                obj.DivisionNo = objDetails.DivisionNo;
                obj.DivisionName = objDetails.DivisionName;
                //[006] code end
                obj.IPOSupplier = objDetails.IPOSupplier;
                obj.IPOSupplierName = objDetails.IPOSupplierName;

                obj.InternalPurchaseOrderNumber = objDetails.InternalPurchaseOrderNumber;
                obj.InternalPurchaseOrderId = objDetails.InternalPurchaseOrderId;
                obj.ClientLandedCost = objDetails.ClientLandedCost;
                obj.ClientPurchasePrice = objDetails.ClientPurchasePrice;
                obj.IPONo = objDetails.IPONo;
                obj.SalesOrderNumber = objDetails.SalesOrderNumber;
                obj.CustomerPO = objDetails.CustomerPO;
                obj.CustomerNo = objDetails.CustomerNo;
                obj.CustomerName = objDetails.CustomerName;
                //obj.ReqSerialNo = objDetails.ReqSerialNo;
                obj.MSLLevel = objDetails.MSLLevel;
                obj.CurrencyNo = objDetails.CurrencyNo;
                //[007] code start
                obj.IsProdHazardous = objDetails.IsProdHazardous;
                obj.IsOrderViaIPOonly = objDetails.IsOrderViaIPOonly;
                obj.GeneralInspectionNotes = objDetails.GeneralInspectionNotes;
                obj.IsRestrictedProduct = objDetails.IsRestrictedProduct;
                //[007] code end
                obj.ActeoneTestStatus = objDetails.ActeoneTestStatus;
                obj.IsopropryleStatus = objDetails.IsopropryleStatus;
                obj.ActeoneTest = objDetails.ActeoneTest;
                obj.Isopropryle = objDetails.Isopropryle;
                obj.HICStatusName = objDetails.HICStatusName;
                obj.ReqSerialNo = objDetails.ReqSerialNo;
                obj.IsLotCodesReq = objDetails.IsLotCodesReq;
                obj.BakingLevelAdded = objDetails.BakingLevelAdded;
                //RP-1519
                obj.IsImport = objDetails.IsImport;
                obj.ImportSupplierNo = objDetails.ImportSupplierNo;
                obj.ImportupplierName = objDetails.ImportupplierName;
                obj.importCompanyType = objDetails.importCompanyType;
                obj.CompanyType = objDetails.CompanyType;
                obj.isLotOnHold = objDetails.isLotOnHold;
                obj.ClientUPLiftPrice = objDetails.ClientUPLiftPrice;
                obj.HasSTO = objDetails.HasSTO;

                obj.AS6081 = objDetails.AS6081 ?? false; //[008]
                obj.CountryWarningMessage = objDetails.CountryWarningMessage;
                obj.IsHasCountryMessage = objDetails.IsHasCountryMessage;
                objDetails = null;
                return obj;
            }
        }
        /// <summary>
        /// Get 
        /// Calls [USP_GetSTOIds]
        /// </summary>
        public static DataTable GetSTOIds(Boolean? IsHub, System.Int32? stockId)
        {
            DataTable dt;
            try
            {
                dt = Rebound.GlobalTrader.DAL.SiteProvider.Stock.GetSTOIds(IsHub, stockId);
                return dt;
            }
            catch (Exception ex)
            {
                dt = null;
                return dt;
            }


        }
        /// <summary>
        /// Get 
        /// Calls [USP_GetSTOIds]
        /// </summary>
        public static DataTable GetSTO(System.Int32 STOId)
        {
            DataTable dt;
            try
            {
                dt = Rebound.GlobalTrader.DAL.SiteProvider.Stock.GetSTO(STOId);
                return dt;
            }
            catch (Exception ex)
            {
                dt = null;
                return dt;
            }


        }
        /// <summary>
        /// GetForCustomerRMALine
        /// Calls [usp_select_Stock_for_CustomerRMALine]
        /// </summary>
        public static Stock GetForCustomerRMALine(System.Int32? customerRmaLineId)
        {
            Rebound.GlobalTrader.DAL.StockDetails objDetails = Rebound.GlobalTrader.DAL.SiteProvider.Stock.GetForCustomerRMALine(customerRmaLineId);
            if (objDetails == null)
            {
                return null;
            }
            else
            {
                Stock obj = new Stock();
                obj.StockId = objDetails.StockId;
                obj.FullPart = objDetails.FullPart;
                obj.Part = objDetails.Part;
                obj.ManufacturerNo = objDetails.ManufacturerNo;
                obj.DateCode = objDetails.DateCode;
                obj.PackageNo = objDetails.PackageNo;
                obj.WarehouseNo = objDetails.WarehouseNo;
                obj.ClientNo = objDetails.ClientNo;
                obj.QualityControlNotes = objDetails.QualityControlNotes;
                obj.PurchaseOrderNo = objDetails.PurchaseOrderNo;
                obj.PurchaseOrderLineNo = objDetails.PurchaseOrderLineNo;
                obj.QuantityInStock = objDetails.QuantityInStock;
                obj.QuantityOnOrder = objDetails.QuantityOnOrder;
                obj.Location = objDetails.Location;
                obj.ProductNo = objDetails.ProductNo;
                obj.ResalePrice = objDetails.ResalePrice;
                obj.Unavailable = objDetails.Unavailable;
                obj.LotNo = objDetails.LotNo;
                obj.LandedCost = objDetails.LandedCost;
                obj.SupplierPart = objDetails.SupplierPart;
                obj.ROHS = objDetails.ROHS;
                obj.PackageUnit = objDetails.PackageUnit;
                obj.StockKeepingUnit = objDetails.StockKeepingUnit;
                obj.CustomerRMANo = objDetails.CustomerRMANo;
                obj.CustomerRMALineNo = objDetails.CustomerRMALineNo;
                obj.GoodsInLineNo = objDetails.GoodsInLineNo;
                obj.UpdatedBy = objDetails.UpdatedBy;
                obj.DLUP = objDetails.DLUP;
                obj.FullSupplierPart = objDetails.FullSupplierPart;
                obj.CountryOfManufacture = objDetails.CountryOfManufacture;
                obj.PackageName = objDetails.PackageName;
                obj.PackageDescription = objDetails.PackageDescription;
                obj.WarehouseName = objDetails.WarehouseName;
                obj.ProductName = objDetails.ProductName;
                obj.ProductDescription = objDetails.ProductDescription;
                obj.ManufacturerName = objDetails.ManufacturerName;
                obj.ManufacturerCode = objDetails.ManufacturerCode;
                obj.PurchaseOrderNumber = objDetails.PurchaseOrderNumber;
                obj.CustomerRMANumber = objDetails.CustomerRMANumber;
                obj.CurrencyCode = objDetails.CurrencyCode;
                obj.SupplierName = objDetails.SupplierName;
                obj.LotName = objDetails.LotName;
                obj.LotCode = objDetails.LotCode;
                obj.SupplierNo = objDetails.SupplierNo;
                obj.Buyer = objDetails.Buyer;
                obj.BuyerName = objDetails.BuyerName;
                obj.QuantityAllocated = objDetails.QuantityAllocated;
                obj.QuantityAvailable = objDetails.QuantityAvailable;
                obj.GoodsInNo = objDetails.GoodsInNo;
                obj.GoodsInPrice = objDetails.GoodsInPrice;
                obj.GoodsInShipInCost = objDetails.GoodsInShipInCost;
                obj.GoodsInNumber = objDetails.GoodsInNumber;
                obj.GoodsInCurrencyNo = objDetails.GoodsInCurrencyNo;
                obj.StockDate = objDetails.StockDate;
                obj.ROHSStatus = objDetails.ROHSStatus;
                obj.CountryOfManufactureName = objDetails.CountryOfManufactureName;
                obj.PurchasePrice = objDetails.PurchasePrice;
                obj.PartMarkings = objDetails.PartMarkings;
                obj.CountingMethodNo = objDetails.CountingMethodNo;
                obj.CountingMethodDescription = objDetails.CountingMethodDescription;
                objDetails = null;
                return obj;
            }
        }
        /// <summary>
        /// GetForPage
        /// Calls [usp_select_Stock_for_Page]
        /// </summary>
        public static Stock GetForPage(System.Int32? stockId)
        {
            Rebound.GlobalTrader.DAL.StockDetails objDetails = Rebound.GlobalTrader.DAL.SiteProvider.Stock.GetForPage(stockId);
            if (objDetails == null)
            {
                return null;
            }
            else
            {
                Stock obj = new Stock();
                obj.StockId = objDetails.StockId;
                obj.ClientNo = objDetails.ClientNo;
                obj.Part = objDetails.Part;
                obj.IsImageAvailable = objDetails.IsImageAvailable;
                // [002] code start
                obj.IsPDFAvailable = objDetails.IsPDFAvailable;
                // [002] code end
                obj.POClientNo = objDetails.POClientNo;
                // [003] code start
                obj.ClientName = objDetails.ClientName;
                // [003] code end
                obj.ClientBaseCurrencyCode = objDetails.ClientBaseCurrencyCode;
                obj.ClientBaseCurrencyID = objDetails.ClientBaseCurrencyID;
                objDetails = null;
                return obj;
            }
        }
        /// <summary>
        /// GetForPurchaseOrderLine
        /// Calls [usp_select_Stock_for_PurchaseOrderLine]
        /// </summary>
        public static Stock GetForPurchaseOrderLine(System.Int32? purchaseOrderLineId)
        {
            Rebound.GlobalTrader.DAL.StockDetails objDetails = Rebound.GlobalTrader.DAL.SiteProvider.Stock.GetForPurchaseOrderLine(purchaseOrderLineId);
            if (objDetails == null)
            {
                return null;
            }
            else
            {
                Stock obj = new Stock();
                obj.StockId = objDetails.StockId;
                obj.FullPart = objDetails.FullPart;
                obj.Part = objDetails.Part;
                obj.ManufacturerNo = objDetails.ManufacturerNo;
                obj.DateCode = objDetails.DateCode;
                obj.PackageNo = objDetails.PackageNo;
                obj.WarehouseNo = objDetails.WarehouseNo;
                obj.ClientNo = objDetails.ClientNo;
                obj.QualityControlNotes = objDetails.QualityControlNotes;
                obj.PurchaseOrderNo = objDetails.PurchaseOrderNo;
                obj.PurchaseOrderLineNo = objDetails.PurchaseOrderLineNo;
                obj.QuantityInStock = objDetails.QuantityInStock;
                obj.QuantityOnOrder = objDetails.QuantityOnOrder;
                obj.Location = objDetails.Location;
                obj.ProductNo = objDetails.ProductNo;
                obj.ResalePrice = objDetails.ResalePrice;
                obj.Unavailable = objDetails.Unavailable;
                obj.LotNo = objDetails.LotNo;
                obj.LandedCost = objDetails.LandedCost;
                obj.SupplierPart = objDetails.SupplierPart;
                obj.ROHS = objDetails.ROHS;
                obj.PackageUnit = objDetails.PackageUnit;
                obj.StockKeepingUnit = objDetails.StockKeepingUnit;
                obj.CustomerRMANo = objDetails.CustomerRMANo;
                obj.CustomerRMALineNo = objDetails.CustomerRMALineNo;
                obj.GoodsInLineNo = objDetails.GoodsInLineNo;
                obj.UpdatedBy = objDetails.UpdatedBy;
                obj.DLUP = objDetails.DLUP;
                obj.FullSupplierPart = objDetails.FullSupplierPart;
                obj.CountryOfManufacture = objDetails.CountryOfManufacture;
                obj.PackageName = objDetails.PackageName;
                obj.PackageDescription = objDetails.PackageDescription;
                obj.WarehouseName = objDetails.WarehouseName;
                obj.ProductName = objDetails.ProductName;
                obj.ProductDescription = objDetails.ProductDescription;
                obj.ManufacturerName = objDetails.ManufacturerName;
                obj.ManufacturerCode = objDetails.ManufacturerCode;
                obj.PurchaseOrderNumber = objDetails.PurchaseOrderNumber;
                obj.CustomerRMANumber = objDetails.CustomerRMANumber;
                obj.CurrencyCode = objDetails.CurrencyCode;
                obj.SupplierName = objDetails.SupplierName;
                obj.LotName = objDetails.LotName;
                obj.LotCode = objDetails.LotCode;
                obj.SupplierNo = objDetails.SupplierNo;
                obj.Buyer = objDetails.Buyer;
                obj.BuyerName = objDetails.BuyerName;
                obj.QuantityAllocated = objDetails.QuantityAllocated;
                obj.QuantityAvailable = objDetails.QuantityAvailable;
                obj.GoodsInNo = objDetails.GoodsInNo;
                obj.GoodsInPrice = objDetails.GoodsInPrice;
                obj.GoodsInShipInCost = objDetails.GoodsInShipInCost;
                obj.GoodsInNumber = objDetails.GoodsInNumber;
                obj.GoodsInCurrencyNo = objDetails.GoodsInCurrencyNo;
                obj.StockDate = objDetails.StockDate;
                obj.ROHSStatus = objDetails.ROHSStatus;
                obj.CountryOfManufactureName = objDetails.CountryOfManufactureName;
                obj.PurchasePrice = objDetails.PurchasePrice;
                obj.PartMarkings = objDetails.PartMarkings;
                obj.CountingMethodNo = objDetails.CountingMethodNo;
                obj.CountingMethodDescription = objDetails.CountingMethodDescription;
                objDetails = null;
                return obj;
            }
        }



        /// <summary>
        /// GetListForLot
        /// Calls [usp_selectAll_Stock_for_Lot]
        /// </summary>
        public static List<Stock> GetListForLot(System.Int32? lotId)
        {
            List<StockDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.Stock.GetListForLot(lotId);
            if (lstDetails == null)
            {
                return new List<Stock>();
            }
            else
            {
                List<Stock> lst = new List<Stock>();
                foreach (StockDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.Stock obj = new Rebound.GlobalTrader.BLL.Stock();
                    obj.StockId = objDetails.StockId;
                    obj.FullPart = objDetails.FullPart;
                    obj.Part = objDetails.Part;
                    obj.ManufacturerNo = objDetails.ManufacturerNo;
                    obj.DateCode = objDetails.DateCode;
                    obj.PackageNo = objDetails.PackageNo;
                    obj.WarehouseNo = objDetails.WarehouseNo;
                    obj.ClientNo = objDetails.ClientNo;
                    obj.QualityControlNotes = objDetails.QualityControlNotes;
                    obj.PurchaseOrderNo = objDetails.PurchaseOrderNo;
                    obj.PurchaseOrderLineNo = objDetails.PurchaseOrderLineNo;
                    obj.QuantityInStock = objDetails.QuantityInStock;
                    obj.QuantityOnOrder = objDetails.QuantityOnOrder;
                    obj.Location = objDetails.Location;
                    obj.ProductNo = objDetails.ProductNo;
                    obj.ResalePrice = objDetails.ResalePrice;
                    obj.Unavailable = objDetails.Unavailable;
                    obj.LotNo = objDetails.LotNo;
                    obj.LandedCost = objDetails.LandedCost;
                    obj.SupplierPart = objDetails.SupplierPart;
                    obj.ROHS = objDetails.ROHS;
                    obj.PackageUnit = objDetails.PackageUnit;
                    obj.StockKeepingUnit = objDetails.StockKeepingUnit;
                    obj.CustomerRMANo = objDetails.CustomerRMANo;
                    obj.CustomerRMALineNo = objDetails.CustomerRMALineNo;
                    obj.GoodsInLineNo = objDetails.GoodsInLineNo;
                    obj.UpdatedBy = objDetails.UpdatedBy;
                    obj.DLUP = objDetails.DLUP;
                    obj.FullSupplierPart = objDetails.FullSupplierPart;
                    obj.CountryOfManufacture = objDetails.CountryOfManufacture;
                    obj.PackageName = objDetails.PackageName;
                    obj.PackageDescription = objDetails.PackageDescription;
                    obj.WarehouseName = objDetails.WarehouseName;
                    obj.ProductName = objDetails.ProductName;
                    obj.ProductDescription = objDetails.ProductDescription;
                    obj.ManufacturerName = objDetails.ManufacturerName;
                    obj.ManufacturerCode = objDetails.ManufacturerCode;
                    obj.PurchaseOrderNumber = objDetails.PurchaseOrderNumber;
                    obj.CustomerRMANumber = objDetails.CustomerRMANumber;
                    obj.CurrencyCode = objDetails.CurrencyCode;
                    obj.SupplierName = objDetails.SupplierName;
                    obj.LotName = objDetails.LotName;
                    obj.LotCode = objDetails.LotCode;
                    obj.SupplierNo = objDetails.SupplierNo;
                    obj.Buyer = objDetails.Buyer;
                    obj.BuyerName = objDetails.BuyerName;
                    obj.QuantityAllocated = objDetails.QuantityAllocated;
                    obj.QuantityAvailable = objDetails.QuantityAvailable;
                    obj.GoodsInNo = objDetails.GoodsInNo;
                    obj.GoodsInPrice = objDetails.GoodsInPrice;
                    obj.GoodsInShipInCost = objDetails.GoodsInShipInCost;
                    obj.GoodsInNumber = objDetails.GoodsInNumber;
                    obj.GoodsInCurrencyNo = objDetails.GoodsInCurrencyNo;
                    obj.StockDate = objDetails.StockDate;
                    obj.ROHSStatus = objDetails.ROHSStatus;
                    obj.CountryOfManufactureName = objDetails.CountryOfManufactureName;
                    obj.PurchasePrice = objDetails.PurchasePrice;
                    obj.PartMarkings = objDetails.PartMarkings;
                    obj.CountingMethodNo = objDetails.CountingMethodNo;
                    obj.CountingMethodDescription = objDetails.CountingMethodDescription;
                    //[003] code start
                    obj.StockUnallocatedCount = objDetails.StockUnallocatedCount;
                    //[003] code end
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }

        public static List<Stock> GetListForNonZeroStockLot(System.Int32? lotId)
        {
            List<StockDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.Stock.GetListForNonZeroStockLot(lotId);
            if (lstDetails == null)
            {
                return new List<Stock>();
            }
            else
            {
                List<Stock> lst = new List<Stock>();
                foreach (StockDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.Stock obj = new Rebound.GlobalTrader.BLL.Stock();
                    obj.StockId = objDetails.StockId;
                    obj.Part = objDetails.Part;
                    obj.ManufacturerNo = objDetails.ManufacturerNo;
                    obj.WarehouseNo = objDetails.WarehouseNo;
                    obj.WarehouseName = objDetails.WarehouseName;
                    obj.QuantityInStock = objDetails.QuantityInStock;
                    obj.Location = objDetails.Location;
                    obj.ProductNo = objDetails.ProductNo;
                    obj.ROHS = objDetails.ROHS;
                    obj.ProductName = objDetails.ProductName;
                    obj.ManufacturerCode = objDetails.ManufacturerCode;
                    obj.StockUnallocatedCount = objDetails.StockUnallocatedCount;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }
        /// <summary>
        /// GetListRelatedStock
        /// Calls [usp_selectAll_Stock_RelatedStock]
        /// </summary>
        public static List<Stock> GetListRelatedStock(System.Int32? stockNo)
        {
            List<StockDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.Stock.GetListRelatedStock(stockNo);
            if (lstDetails == null)
            {
                return new List<Stock>();
            }
            else
            {
                List<Stock> lst = new List<Stock>();
                foreach (StockDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.Stock obj = new Rebound.GlobalTrader.BLL.Stock();
                    obj.StockId = objDetails.StockId;
                    obj.ManufacturerName = objDetails.ManufacturerName;
                    obj.ManufacturerNo = objDetails.ManufacturerNo;
                    obj.ProductNo = objDetails.ProductNo;
                    obj.ProductName = objDetails.ProductName;
                    obj.PackageNo = objDetails.PackageNo;
                    obj.PackageName = objDetails.PackageName;
                    obj.QuantityAllocated = objDetails.QuantityAllocated;
                    obj.QuantityAvailable = objDetails.QuantityAvailable;
                    obj.QuantityInStock = objDetails.QuantityInStock;
                    obj.QuantityOnOrder = objDetails.QuantityOnOrder;
                    obj.Part = objDetails.Part;
                    obj.ROHS = objDetails.ROHS;
                    obj.RelationType = objDetails.RelationType;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }
        /// <summary>
        /// Source
        /// Calls [usp_source_Stock]
        /// </summary>
        public static List<Stock> Source(System.Int32? clientId, System.String partSearch, bool IsServerLocal)
        {
            List<StockDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.Stock.Source(clientId, partSearch, IsServerLocal);
            if (lstDetails == null)
            {
                return new List<Stock>();
            }
            else
            {
                List<Stock> lst = new List<Stock>();
                foreach (StockDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.Stock obj = new Rebound.GlobalTrader.BLL.Stock();
                    obj.StockId = objDetails.StockId;
                    obj.QuantityInStock = objDetails.QuantityInStock;
                    obj.QuantityOnOrder = objDetails.QuantityOnOrder;
                    obj.Part = objDetails.Part;
                    obj.ManufacturerCode = objDetails.ManufacturerCode;
                    obj.ManufacturerNo = objDetails.ManufacturerNo;
                    obj.ProductName = objDetails.ProductName;
                    obj.DateCode = objDetails.DateCode;
                    obj.SupplierNo = objDetails.SupplierNo;
                    obj.SupplierName = objDetails.SupplierName;
                    obj.ResalePrice = objDetails.ResalePrice;
                    obj.ROHS = objDetails.ROHS;
                    obj.WarehouseName = objDetails.WarehouseName;
                    obj.Location = objDetails.Location;
                    obj.PackageName = objDetails.PackageName;
                    obj.SupplierPart = objDetails.SupplierPart;
                    obj.ClientNo = objDetails.ClientNo;
                    obj.ClientName = objDetails.ClientName;
                    obj.ClientDataVisibleToOthers = objDetails.ClientDataVisibleToOthers;
                    //[004] code start
                    obj.SupplierType = objDetails.SupplierType;
                    //[004] code end
                    obj.QuantityAvailable = objDetails.QuantityAvailable;
                    obj.ClientBaseCurrencyCode = objDetails.ClientBaseCurrencyCode;
                    obj.ClientCode = objDetails.ClientCode;
                    obj.DivisionNo = objDetails.DivisionNo;
                    obj.DivisionName = objDetails.DivisionName;
                    obj.DivisionStatus = objDetails.DivisionStatus;
                    obj.ClientUPLiftPrice = objDetails.ClientUPLiftPrice;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }
        /// <summary>
        /// Update
        /// Calls [usp_update_Stock]
        /// </summary>
        public static bool Update(System.Int32? stockId, System.String part, System.Int32? manufacturerNo, System.String dateCode, System.Int32? packageNo, System.Int32? warehouseNo, System.Int32? clientNo, System.String qualityControlNotes, System.Int32? purchaseOrderNo, System.Int32? purchaseOrderLineNo, System.Int32? customerRmaNo, System.Int32? customerRmaLineNo, System.Int32? quantityInStock, System.Int32? quantityOnOrder, System.Int32? productNo, System.Double? resalePrice, System.Boolean? unavailable, System.Int32? lotNo, System.Double? landedCost, System.String supplierPart, System.Byte? rohs, System.Int32? packageUnit, System.Int32? stockKeepingUnit, System.Int32? updatedBy, System.String stockLogDetail, System.String stockLogChangeNotes, System.Int32? stockLogReasonNo, System.String location, System.Int32? countryOfManufacture, System.Boolean? updateShipments, System.String partMarkings, System.Int32? countingMethodNo, System.Int32? divisionNo, System.Boolean? isClientUpdate, System.String mslLevel, System.Double? ClientUPLiftPrice, out System.String strLotMessage)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.Update(stockId, part, manufacturerNo, dateCode, packageNo, warehouseNo, clientNo, qualityControlNotes, purchaseOrderNo, purchaseOrderLineNo, customerRmaNo, customerRmaLineNo, quantityInStock, quantityOnOrder, productNo, resalePrice, unavailable, lotNo, landedCost, supplierPart, rohs, packageUnit, stockKeepingUnit, updatedBy, stockLogDetail, stockLogChangeNotes, stockLogReasonNo, location, countryOfManufacture, updateShipments, partMarkings, countingMethodNo, divisionNo, isClientUpdate, mslLevel, ClientUPLiftPrice, out strLotMessage);
        }
        /// <summary>
        /// Update (without parameters)
        /// Calls [usp_update_Stock]
        /// </summary>
        public bool Update(out string strLotMsg)
        {
            bool result = Rebound.GlobalTrader.DAL.SiteProvider.Stock.Update(StockId, Part, ManufacturerNo, DateCode, PackageNo, WarehouseNo, ClientNo, QualityControlNotes, PurchaseOrderNo, PurchaseOrderLineNo, CustomerRMANo, CustomerRMALineNo, QuantityInStock, QuantityOnOrder, ProductNo, ResalePrice, Unavailable, LotNo, LandedCost, SupplierPart, ROHS, PackageUnit, StockKeepingUnit, UpdatedBy, StockLogDetail, StockLogChangeNotes, StockLogReasonNo, Location, CountryOfManufacture, UpdateShipments, PartMarkings, CountingMethodNo, DivisionNo, IsClientUpdate, MSLLevel, ClientUPLiftPrice, out System.String strLotMessage, AS6081);
            strLotMsg = strLotMessage;
            return result;
        }
        /// <summary>
        /// UpdateQuarantined
        /// Calls [usp_update_Stock_Quarantined]
        /// </summary>
        public static bool UpdateQuarantined(System.Int32? stockId, System.Boolean? quarantine, System.String location, System.Int32? updatedBy)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.UpdateQuarantined(stockId, quarantine, location, updatedBy);
        }
        /// <summary>
        /// call [usp_Get_All_GILine_ID_From_Stock]
        /// </summary>
        /// <param name="iD">stock id</param>
        /// <returns></returns>
        public static List<int> GetAllGILineIDsFromStock(int stockId)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.GetAllGILineIDsFromStock(stockId);
        }
        /// <summary>
        /// call [usp_update_GILine_Quarantined_From_Stock]
        /// </summary>
        /// <param name="stockId"></param>
        /// <param name="logInID"></param>
        /// <param name="quarantine"></param>
        /// <param name="clientId"></param>
        /// <returns></returns>
        public static bool UpdateGILineQuarantineFromStock(int giLineId, int updatedBy, int? clientId)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.UpdateGILineQuarantineFromStock(giLineId, updatedBy, clientId);
        }
        /// <summary>
        /// UpdateTransferLot
        /// Calls [usp_update_Stock_Transfer_Lot]
        /// </summary>
        public static bool UpdateTransferLot(System.Int32? oldNotNo, System.Int32? newLotNo)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.UpdateTransferLot(oldNotNo, newLotNo);
        }

        private static Stock PopulateFromDBDetailsObject(StockDetails obj)
        {
            Stock objNew = new Stock();
            objNew.StockId = obj.StockId;
            objNew.FullPart = obj.FullPart;
            objNew.Part = obj.Part;
            objNew.ManufacturerNo = obj.ManufacturerNo;
            objNew.DateCode = obj.DateCode;
            objNew.PackageNo = obj.PackageNo;
            objNew.WarehouseNo = obj.WarehouseNo;
            objNew.ClientNo = obj.ClientNo;
            objNew.QualityControlNotes = obj.QualityControlNotes;
            objNew.PurchaseOrderNo = obj.PurchaseOrderNo;
            objNew.PurchaseOrderLineNo = obj.PurchaseOrderLineNo;
            objNew.QuantityInStock = obj.QuantityInStock;
            objNew.QuantityOnOrder = obj.QuantityOnOrder;
            objNew.Location = obj.Location;
            objNew.ProductNo = obj.ProductNo;
            objNew.ResalePrice = obj.ResalePrice;
            objNew.Unavailable = obj.Unavailable;
            objNew.LotNo = obj.LotNo;
            objNew.LandedCost = obj.LandedCost;
            objNew.SupplierPart = obj.SupplierPart;
            objNew.ROHS = obj.ROHS;
            objNew.PackageUnit = obj.PackageUnit;
            objNew.StockKeepingUnit = obj.StockKeepingUnit;
            objNew.CustomerRMANo = obj.CustomerRMANo;
            objNew.CustomerRMALineNo = obj.CustomerRMALineNo;
            objNew.GoodsInLineNo = obj.GoodsInLineNo;
            objNew.UpdatedBy = obj.UpdatedBy;
            objNew.DLUP = obj.DLUP;
            objNew.FullSupplierPart = obj.FullSupplierPart;
            objNew.CountryOfManufacture = obj.CountryOfManufacture;
            objNew.PartMarkings = obj.PartMarkings;
            objNew.CountingMethodNo = obj.CountingMethodNo;
            objNew.ManufacturerCode = obj.ManufacturerCode;
            objNew.QuantityAllocated = obj.QuantityAllocated;
            objNew.WarehouseName = obj.WarehouseName;
            objNew.LotName = obj.LotName;
            objNew.SupplierNo = obj.SupplierNo;
            objNew.SupplierName = obj.SupplierName;
            objNew.RowNum = obj.RowNum;
            objNew.QuantityAvailable = obj.QuantityAvailable;
            objNew.StatusNo = obj.StatusNo;
            objNew.RowCnt = obj.RowCnt;
            objNew.PODeliveryDate = obj.PODeliveryDate;
            objNew.PurchaseOrderNumber = obj.PurchaseOrderNumber;
            objNew.CustomerRMANumber = obj.CustomerRMANumber;
            objNew.CustomerRMADate = obj.CustomerRMADate;
            objNew.PackageName = obj.PackageName;
            objNew.PackageDescription = obj.PackageDescription;
            objNew.ProductName = obj.ProductName;
            objNew.ProductDescription = obj.ProductDescription;
            objNew.ManufacturerName = obj.ManufacturerName;
            objNew.CurrencyCode = obj.CurrencyCode;
            objNew.LotCode = obj.LotCode;
            objNew.Buyer = obj.Buyer;
            objNew.BuyerName = obj.BuyerName;
            objNew.GoodsInNo = obj.GoodsInNo;
            objNew.GoodsInPrice = obj.GoodsInPrice;
            objNew.GoodsInShipInCost = obj.GoodsInShipInCost;
            objNew.GoodsInNumber = obj.GoodsInNumber;
            objNew.GoodsInCurrencyNo = obj.GoodsInCurrencyNo;
            objNew.StockDate = obj.StockDate;
            objNew.ROHSStatus = obj.ROHSStatus;
            objNew.CountryOfManufactureName = obj.CountryOfManufactureName;
            objNew.PurchasePrice = obj.PurchasePrice;
            objNew.CountingMethodDescription = obj.CountingMethodDescription;
            objNew.StockLogDetail = obj.StockLogDetail;
            objNew.StockLogChangeNotes = obj.StockLogChangeNotes;
            objNew.StockLogReasonNo = obj.StockLogReasonNo;
            objNew.UpdateShipments = obj.UpdateShipments;
            objNew.RelationType = obj.RelationType;
            objNew.ClientName = obj.ClientName;
            objNew.ClientDataVisibleToOthers = obj.ClientDataVisibleToOthers;
            return objNew;
        }
        // [002] code start
        /// <summary>
        /// GetListForSalesOrder
        /// Calls [usp_selectAll_PDF_for_Stock]
        /// </summary>
        public static List<PDFDocument> GetPDFListForStock(System.Int32? StockId)
        {
            List<PDFDocumentDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.Stock.GetPDFListForStock(StockId);
            if (lstDetails == null)
            {
                return new List<PDFDocument>();
            }
            else
            {
                List<PDFDocument> lst = new List<PDFDocument>();
                foreach (PDFDocumentDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.PDFDocument obj = new Rebound.GlobalTrader.BLL.PDFDocument();
                    obj.PDFDocumentId = objDetails.PDFDocumentId;
                    obj.PDFDocumentRefNo = objDetails.PDFDocumentRefNo;
                    obj.Caption = objDetails.Caption;
                    obj.FileName = objDetails.FileName;
                    obj.UpdatedBy = objDetails.UpdatedBy;
                    obj.DLUP = objDetails.DLUP;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }
        /// <summary>
        /// Insert
        /// Calls [usp_insert_StockPDF]
        /// </summary>
        public static Int32 Insert(System.Int32? StockId, System.String Caption, System.String FileName, System.Int32? UpdatedBy)
        {
            Int32 objReturn = Rebound.GlobalTrader.DAL.SiteProvider.Stock.Insert(StockId, Caption, FileName, UpdatedBy);
            return objReturn;
        }
        /// Delete
        /// Calls [usp_delete_StockPDF]
        /// </summary>
        public static bool DeleteStockPDF(System.Int32? StockPdfId)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.DeleteStockPDF(StockPdfId);
        }

        // [002] code start

        //[005] code start
        /// <summary>
        /// UpdateStockProvision
        /// Calls [usp_update_Stock_Provision]
        /// </summary>
        public static bool UpdateStockProvision(System.Int32? stockId, System.Double? newLandedCost, System.Int32? updatedBy, System.Int32? percentageValue)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.UpdateStockProvision(stockId, newLandedCost, updatedBy, percentageValue);
        }
        //[005] code end
        public static bool UpdateHubLandedCost(System.Int32? stockId, System.Double? landedCost, System.Double? resalePrice, System.Int32? updatedBy)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.UpdateHubLandedCost(stockId, landedCost, resalePrice, updatedBy);
        }
        #endregion

        #region Lot stock provision start

        /// <summary>
        ///Get list of stock Provision
        /// Calls [uspGetAllLotStockProvision]
        /// </summary>
        public static List<Stock> GetListStockProvision(System.Int32? lotId)
        {
            List<StockDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.Stock.GetListStockProvision(lotId);
            if (lstDetails == null)
            {
                return new List<Stock>();
            }
            else
            {
                List<Stock> lst = new List<Stock>();
                foreach (StockDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.Stock obj = new Rebound.GlobalTrader.BLL.Stock();
                    obj.StockId = objDetails.StockId;
                    obj.Part = objDetails.Part;
                    obj.ManufacturerNo = objDetails.ManufacturerNo;
                    obj.WarehouseNo = objDetails.WarehouseNo;
                    obj.WarehouseName = objDetails.WarehouseName;
                    obj.QuantityInStock = objDetails.QuantityInStock;
                    obj.Location = objDetails.Location;
                    obj.ProductNo = objDetails.ProductNo;
                    obj.ProductName = objDetails.ProductName;
                    obj.OriginalLandedCost = objDetails.OriginalLandedCost;// Currency.ConvertValueFromBaseCurrency(objDetails.OriginalLandedCost, objDetails.CurrencyId, DateTime.Now);
                    obj.CurrentLandedCost = objDetails.CurrentLandedCost;// Currency.ConvertValueFromBaseCurrency(objDetails.CurrentLandedCost, objDetails.CurrencyId, DateTime.Now);
                    obj.ClientBaseCurrencyCode = objDetails.ClientBaseCurrencyCode;
                    obj.ROHS = objDetails.ROHS;
                    obj.ManufacturerCode = objDetails.ManufacturerCode;

                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }

        public static Int32 InserLotStock(System.Int32 lotId, double provisionPercentage, System.Int32? UpdatedBy, Double? TotalPrimaryLandedCost, Double? TotalCurrentLandedCost)
        {
            Int32 objReturn = Rebound.GlobalTrader.DAL.SiteProvider.Stock.InserLotStock(lotId, provisionPercentage, UpdatedBy, TotalPrimaryLandedCost, TotalCurrentLandedCost);
            return objReturn;


        }

        #endregion

        public static List<Stock> GetSerial(System.Int32? stockId)
        {
            List<GoodsInLineDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.Stock.GetSerial(stockId);
            if (lstDetails == null)
            {
                return new List<Stock>();
            }
            else
            {
                List<Stock> lst = new List<Stock>();
                foreach (GoodsInLineDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.Stock obj = new Rebound.GlobalTrader.BLL.Stock();
                    obj.SerialNoId = objDetails.SerialNoId;
                    obj.SerialNo = objDetails.SerialNo;
                    obj.SubGroup = objDetails.SubGroup;
                    obj.GoodsInNo = objDetails.GoodsInNo;

                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }

        public static List<Stock> GetCustreqtest(System.Int32? BomNo, System.Int32? clientId)
        {
            List<StockDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.Stock.GetCustreqtest(BomNo, clientId);
            if (lstDetails == null)
            {
                return new List<Stock>();
            }
            else
            {
                List<Stock> lst = new List<Stock>();
                foreach (StockDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.Stock obj = new Rebound.GlobalTrader.BLL.Stock();
                    obj.CustomerRequirementId = objDetails.CustomerRequirementId;
                    obj.CustomerRequirementNumber = objDetails.CustomerRequirementNumber;


                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }

        //[005]
        public static DataTable GetBomCusReq(System.Int32? clientId, System.Int32? BomNo, System.Int32? UserId)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.GetBomCusReq(clientId, BomNo, UserId);
        }
        public static DataTable GetBomClient(System.Int32? clientId, System.Int32? UserId)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.GetBomClient(clientId, UserId);
        }

        //[005]
        #region Stock Import Tool
        public static DataTable GetClient(System.Int32? clientId)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.GetClient(clientId);
        }
        public static DataTable GetIndustryType(System.Int32? clientId)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.GetIndustryType(clientId);
        }
        public static DataTable GetClientName(System.Int32? clientId, System.Int32? clientType_con)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.GetClientName(clientId, clientType_con);
        }
        public static DataTable GetImportActivity(System.Int32? clientId, System.Int32? clientType_con)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.GetImportActivity(clientId, clientType_con);
        }
        public static DataTable GetSupplier(System.Int32? clientId)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.GetSupplier(clientId);
        }
        public static DataTable GetCurrency(System.Int32? clientId, System.Int32? clientType_con)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.GetCurrency(clientId, clientType_con);
        }
        public static void SaveExcelHeader(string columnList, string insertColumnList, System.Int32? clientId, int? SelectedclientId, int? loginID, int clientType_con)
        {
            Rebound.GlobalTrader.DAL.SiteProvider.Stock.SaveExcelHeader(columnList, insertColumnList, clientId, SelectedclientId, loginID, clientType_con);
        }
        public static void saveExcelData(DataTable dtstock, int? SelectedclientId, int clientType_con)
        {
            Rebound.GlobalTrader.DAL.SiteProvider.Stock.saveExcelData(dtstock, clientType_con);
        }
        public static void saveExcelBulkSave(DataTable tempStock, DataTable dtData, string originalFilename, string generatedFilename, System.Int32 userId, System.Int32 clientId, int? SelectedclientId, int clientType_con)
        {
            Rebound.GlobalTrader.DAL.SiteProvider.Stock.saveExcelBulkSave(tempStock, dtData, originalFilename, generatedFilename, userId, clientId, SelectedclientId, clientType_con);
        }
        public static DataTable GetExcelHeader(System.Int32 clientStockId, System.Int32 userId, System.Int32 SelectedclientId, int clientType_con)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.GetExcelHeader(clientStockId, userId, SelectedclientId, clientType_con);
        }
        public static DataTable GetImportExcelHeader(System.Int32 clientStockId, System.Int32 userId, System.Int32 SelectedclientId, string selectedColumnlist, int clientType_con)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.GetImportExcelHeader(clientStockId, userId, SelectedclientId, selectedColumnlist, clientType_con);
        }
        public static DataTable GetExcelHeaderFrom(System.Int32 clientStockId, System.Int32 userId, System.Int32 SelectedclientId, int clientType_con)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.GetExcelHeaderFrom(clientStockId, userId, SelectedclientId, clientType_con);
        }
        public static DataTable GetStockDetailFromTemp(int displayLength, int displayStart, int sortCol, string sortDir, string search, int userid, int ClientId, int SelectedclientId, int clientType_con)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.GetStockDetailFromTemp(displayLength, displayStart, sortCol, sortDir, search, userid, ClientId, SelectedclientId, clientType_con);
        }
        //
        public static DataTable GetStockImportactivity(int displayLength, int displayStart, int sortCol, string sortDir, string search, int ClientId, int clientType_con, int SelectedClientId, int userLoginid)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.GetStockImportactivity(displayLength, displayStart, sortCol, sortDir, search, ClientId, clientType_con, SelectedClientId, userLoginid);
        }
        //
        public static DataTable GetStockGenrateTempData(int displayLength, int displayStart, int sortCol, string sortDir, string search, int userid, int ClientId, int SelectedclientId, string ColumnList, string ddlCurrency, string SupplierNameText, int clientType_con, string Column_Lable, string Column_Name)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.GetStockGenrateTempData(displayLength, displayStart, sortCol, sortDir, search, userid, ClientId, SelectedclientId, ColumnList, ddlCurrency, SupplierNameText, clientType_con, Column_Lable, Column_Name);
        }

        public static DataTable GetMappedColumn(int clientBomId, int loginId)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.GetMappedColumn(clientBomId, loginId);
        }
        public static List<PDFDocument> GetBomUploadedFiles(int bomId)
        {
            List<PDFDocumentDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.GetBomUploadedFiles(bomId);
            if (lstDetails == null)
            {
                return new List<PDFDocument>();
            }
            else
            {
                List<PDFDocument> lst = new List<PDFDocument>();
                foreach (PDFDocumentDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.PDFDocument obj = new Rebound.GlobalTrader.BLL.PDFDocument();
                    obj.PDFDocumentId = objDetails.PDFDocumentId;
                    //obj.PDFDocumentRefNo = objDetails.PDFDocumentRefNo;
                    obj.GeneratedFileName = objDetails.GeneratedFileName;
                    obj.FileName = objDetails.FileName;
                    obj.UpdatedBy = objDetails.UpdatedBy;
                    obj.DLUP = objDetails.DLUP;
                    obj.Caption = objDetails.Caption;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }
        public static void ResetBomData(int bomId, int userId, int fileLogId)
        {
            Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.ResetBomData(bomId, userId, fileLogId);
        }
        public static DataTable GetCustTableAllColumn()
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.GetCustTableAllColumn();
        }
        public static int DeleteRecord(System.Int32 SelectedclientId, System.Int32 userId, int ClientId, int clientType_con)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.DeleteRecord(SelectedclientId, userId, ClientId, clientType_con);
        }
        public static void DeleteTempMapping(int SupplierId, int clientType_con)
        {
            Rebound.GlobalTrader.DAL.SiteProvider.Stock.DeleteTempMapping(SupplierId, clientType_con);
        }
        public static int SaveSupplierColumnMapping(int SupplierId, string insertMapColumnList, int clientType_con)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.SaveSupplierColumnMapping(SupplierId, insertMapColumnList, clientType_con);
        }
        public static DataTable GetSupplierMappedColumn(int SupplierId, int clientType_con)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.GetSupplierMappedColumn(SupplierId, clientType_con);
        }
        public static int saveStockImportData(int userId, int ClientId, int SelectedclientId, string Column_Lable, string Column_Name, string insertDataList, string fileColName, int SupplierId, string ddlCurrency, int recordType, int clientType_con, out System.String errorMessage)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.saveStockImportData(userId, ClientId, SelectedclientId, Column_Lable, Column_Name, insertDataList, fileColName, SupplierId, ddlCurrency, recordType, clientType_con, out errorMessage);
        }
        public static int InsertUpdateStockImportData(int userId, int ClientId, int SelectedclientId, string Column_Lable, string Column_Name, string insertDataList, string fileColName, int SupplierId, string ddlCurrency, int recordType, int clientType_con, out System.String errorMessage)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.InsertUpdateStockImportData(userId, ClientId, SelectedclientId, Column_Lable, Column_Name, insertDataList, fileColName, SupplierId, ddlCurrency, recordType, clientType_con, out errorMessage);
        }

        public static int SaveImportActivity(string insertDataList, int clientType_con)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.SaveImportActivity(insertDataList, clientType_con);
        }
        #endregion

        /// <summary>
        /// Delete
        /// Calls [usp_deleteOfferRecord]
        /// </summary>
        public static int Delete_OfferRecord(string OfferId, System.Int32 BomId, System.Int32 userId, int ClientId, string OfferType)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.Delete_OfferRecord(OfferId, BomId, userId, ClientId, OfferType);
        }

        /// <summary>
        /// Delete
        /// Calls [usp_deleteTrustedRecord]
        /// </summary>
        public static int Delete_TrustedRecord(string OfferId, System.Int32 BomId, System.Int32 userId, int ClientId, string OfferType)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.Delete_TrustedRecord(OfferId, BomId, userId, ClientId, OfferType);
        }

        /// <summary>
        /// Delete
        /// Calls [usp_deletePriceRequestRecord]
        /// </summary>
        public static int Delete_POQuotesRecord(string POQuotesId, System.Int32 BomId, System.Int32 userId, int ClientId, string OfferType)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.Delete_POQuotesRecord(POQuotesId, BomId, userId, ClientId, OfferType);
        }


        /// <summary>
        /// Maintain Search Log Details
        /// Calls [usp_CrossMatch_SearchLog]
        /// </summary>
        public static int CrossMatchSearchLog(System.Int32 BomId, System.Int32 userId, int ClientId, string logdetails)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.CrossMatchSearchLog(BomId, userId, ClientId, logdetails);
        }


        //codee add for CrossMatch Filter Save
        /// <summary>
        /// Insert
        /// Calls [usp_offer_Filter_CrossMatchRequirement]
        /// </summary>

        public static int FilterOfferCrossMatchReq(System.Int32 offerId, out System.String errorMessage)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.FilterOfferCrossMatchReq(offerId, out errorMessage);
        }

        //codee add for CrossMatch Filter Delete
        /// <summary>
        /// Insert
        /// Calls [usp_offer_Delete_CrossMatchRequirement]
        /// </summary>
        public static int DeleteFilterOfferCrossMatchReq(int offerId, out System.String errorMessage)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.DeleteFilterOfferCrossMatchReq(offerId, out errorMessage);
        }
        //code end

        //codee add for CrossMatch Log Delete
        /// <summary>
        /// Insert
        /// Calls [usp_AutoLog_Delete_CrossMatch]
        /// </summary>
        public static int DeleteAutoLogCrossMatchReq(int BomId, int Userid, out System.String errorMessage)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.DeleteAutoLogCrossMatchReq(BomId, Userid, out errorMessage);
        }
        //code end

        //codee add for CrossMatch Export To excel
        /// <summary>
        /// Select for Export
        /// Calls [usp_ExportToExcel_CrossMatch]
        /// </summary>
        public static DataTable ExportToCSVCrossMatch(System.Int32? BomID, System.Int32? CustReqId, System.Int32? clientId, System.Int32? UserId)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.ExportToCSVCrossMatch(BomID, CustReqId, clientId, UserId);
        }
        //codee add for CrossMatch Export To excel
        /// <summary>
        /// Select for Export
        /// Calls [usp_ExportToExcel_CrossMatch_list]
        /// </summary>
        public static DataTable ExportToCSVCrossMatchlist(System.Int32? BomID, System.Int32? CustReqId, System.Int32? clientId, System.Int32? UserId)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.ExportToCSVCrossMatchlist(BomID, CustReqId, clientId, UserId);
        }


        /// <summary>
        /// Insert Epo Header Column Heading
        /// Calls [usp_SaveEpoExcelColumnHeading]
        /// </summary>
        public static void SaveExcelEpoHeader(string columnList, string insertColumnList, System.Int32? clientId, int? SelectedclientId, int? loginID, int clientType_con)
        {
            Rebound.GlobalTrader.DAL.SiteProvider.Stock.SaveExcelEpoHeader(columnList, insertColumnList, clientId, SelectedclientId, loginID, clientType_con);
        }
        /// <summary>
        /// Insert Epo Temp Data 
        /// Calls [usp_SaveEpoExcelColumnHeading]
        /// </summary>
        public static void saveExcelEpoBulkSave(DataTable tempEpo, DataTable dtData, string originalFilename, string generatedFilename, System.Int32 userId, System.Int32 clientId, int? SelectedclientId, int clientType_con)
        {
            Rebound.GlobalTrader.DAL.SiteProvider.Stock.saveExcelEpoBulkSave(tempEpo, dtData, originalFilename, generatedFilename, userId, clientId, SelectedclientId, clientType_con);
        }

        /// <summary>
        /// Select Epo Header Column 
        /// Calls [usp_GetEpoExcelHeaderColumn]
        /// </summary>
        public static DataTable GetEpoExcelHeader(System.Int32 clientStockId, System.Int32 userId, System.Int32 SelectedclientId, int clientType_con)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.GetEpoExcelHeader(clientStockId, userId, SelectedclientId, clientType_con);
        }
        /// <summary>
        /// Select Epo Data from temp table
        /// Calls [usp_GetEpobindTempData]
        /// </summary>
        public static DataTable GetEpoDetailFromTemp(int displayLength, int displayStart, int sortCol, string sortDir, string search, int userid, int ClientId, int SelectedclientId, int clientType_con)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.GetEpoDetailFromTemp(displayLength, displayStart, sortCol, sortDir, search, userid, ClientId, SelectedclientId, clientType_con);
        }
        /// <summary>
        /// Delete Epo Record
        /// Calls [usp_deleteTempEpoRecord]
        /// </summary>
        public static int DeleteEpoRecord(System.Int32 SelectedclientId, System.Int32 userId, int ClientId, int clientType_con)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.DeleteEpoRecord(SelectedclientId, userId, ClientId, clientType_con);
        }

        /// <summary>
        /// Get Genrate Epo Data
        /// Calls [usp_GenrateDynamicEpoData]
        /// </summary>
        public static DataTable GetEpoGenrateTempData(int displayLength, int displayStart, int sortCol, string sortDir, string search, int userid, int ClientId, int SelectedclientId, string SupplierNameText, int clientType_con, System.Double? UpliftpValue, int NoChangesToSourcingSheet)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.GetEpoGenrateTempData(displayLength, displayStart, sortCol, sortDir, search, userid, ClientId, SelectedclientId, SupplierNameText, clientType_con, UpliftpValue, NoChangesToSourcingSheet);
        }

        /// <summary>
        /// Insert Epo Data
        /// Calls [usp_Select_ImportEpoData]
        /// </summary>
        public static int saveEpoImportData(int userId, int ClientId, int SelectedclientId, int SupplierId, int recordType, int clientType_con, System.Double? UpliftPercentage, int NoChangesToSourcingSheet, out System.String errorMessage)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.saveEpoImportData(userId, ClientId, SelectedclientId, SupplierId, recordType, clientType_con, UpliftPercentage, NoChangesToSourcingSheet, out errorMessage);
        }
        /// <summary>
        /// Insert Epo Import Activity Data
        /// Calls [usp_Select_EpoImportEpoData]
        /// </summary>
        public static int SaveEpoImportActivity(string insertDataList, int clientType_con)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.SaveEpoImportActivity(insertDataList, clientType_con);
        }

        /// <summary>
        /// Get Epo Import Activity Data
        /// Calls [usp_select_Epoimportactivity]
        /// </summary>
        public static DataTable GetEpoImportactivity(int displayLength, int displayStart, int sortCol, string sortDir, string search, int ClientId, int clientType_con, int SelectedClientId, int userLoginid, int isInactive = 0)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.GetEpoImportactivity(displayLength, displayStart, sortCol, sortDir, search, ClientId, clientType_con, SelectedClientId, userLoginid, isInactive);
        }

        /// <summary>
        /// Inactivate Epo Import Activity Data
        /// Calls [usp_Inactivate_EpoImportActivity]
        /// </summary>
        public static InsertedData InactivateEpoImportActivity(int clientId, int selectedImportId, int userId)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.InactivateEpoImportActivity(clientId, selectedImportId, userId);
        }



        /// <summary>
        /// Insert Bom Sourcing Data 
        /// Calls [usp_SaveEpoExcelColumnHeading]
        /// </summary>
        public static void LeftSideExcelSourcingResuleBulkSave(DataTable tempEpo, DataTable dtData, string originalFilename, string generatedFilename, System.Int32 userId, System.Int32 clientId, int bomId)
        {
            Rebound.GlobalTrader.DAL.SiteProvider.Stock.LeftSideExcelSourcingResuleBulkSave(tempEpo, dtData, originalFilename, generatedFilename, userId, clientId, bomId);
        }

        /// <summary>
        /// Insert Bom Sourcing Data 
        /// Calls [usp_SaveEpoExcelColumnHeading]
        /// </summary>
        public static void RightSideExcelSourcingResuleBulkSave(DataTable tempEpo, DataTable dtData, string originalFilename, string generatedFilename, System.Int32 userId, System.Int32 clientId, int bomId)
        {
            Rebound.GlobalTrader.DAL.SiteProvider.Stock.RightSideExcelSourcingResuleBulkSave(tempEpo, dtData, originalFilename, generatedFilename, userId, clientId, bomId);
        }

        /// <summary>
        /// Get Left Side Excel Sourcing Result
        /// Calls [usp_select_LeftOfferDetails]
        /// </summary>
        public static DataTable GetLeftSideExcelSourcingResult(int displayLength, int displayStart, int sortCol, string sortDir, string search, int ClientId, int userLoginid, System.Int32 bomId)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.GetLeftSideExcelSourcingResult(displayLength, displayStart, sortCol, sortDir, search, ClientId, userLoginid, bomId);
        }

        /// <summary>
        /// Get Right Side Excel Sourcing Result
        /// Calls [usp_select_RightOfferDetails]
        /// </summary>
        public static DataTable GetRightSideExcelSourcingResult(int displayLength, int displayStart, int sortCol, string sortDir, string search, int ClientId, int userLoginid, System.Int32 bomId)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.GetRightSideExcelSourcingResult(displayLength, displayStart, sortCol, sortDir, search, ClientId, userLoginid, bomId);
        }

        /// <summary>
        /// Delete Epo Record
        /// Calls [usp_deleteTempRecordfromLeftandRight]
        /// </summary>
        public static int DeleteLeftAndRightData(System.Int32 userId, int ClientId, System.Int32 bomId)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.DeleteLeftAndRightData(userId, ClientId, bomId);
        }

        /// <summary>
        /// Delete Bom Re-ProcessData Record
        /// Calls [usp_Multivendor_Process_Offers]
        /// </summary>
        public static int BomReProcessData(System.Int32 bomId, System.Int32 userId, int ClientId)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.BomReProcessData(bomId, userId, ClientId);
        }


        /// <summary>
        /// Get Left Side Excel Sourcing Result
        /// Calls [usp_select_StatusFileExitOrNot]
        /// </summary>
        public static DataTable GetStatusFileExitOrNot(System.Int32 bomId, int ClientId, int userLoginid, string originalFilename)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.GetStatusFileExitOrNot(bomId, ClientId, userLoginid, originalFilename);
        }

        /// <summary>
        /// Delete Epo Record
        /// Calls [usp_deleteTempRecordfromLeftandRight]
        /// </summary>
        public static int updatecellvalueMfrVender(System.Int32 userId, int ClientId, System.Int32? SourcingResultId, string updatestatus, string manufacturename, string Vendor)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.updatecellvalueMfrVender(userId, ClientId, SourcingResultId, updatestatus, manufacturename, Vendor);
        }


        public static int updatecellvalueMfr(System.Int32 userId, int ClientId, System.Int32? SourcingResultId, string manufacturename)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.updatecellvalueMfr(userId, ClientId, SourcingResultId, manufacturename);
        }
        public static DataTable GetBOMGenrateTempData(int displayLength, int displayStart, int sortCol, string sortDir, string search, int userid, int ClientId, int SelectedclientId, string ColumnList, string ddlCurrency, string CompanyNameText, int clientType_con, string Column_Lable, string Column_Name, string ContactText, bool OverRideCurrency, string DefaultCurrencyName, string CurrencyColumnName, int DefaultCurrencyId)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.GetBOMGenrateTempData(displayLength, displayStart, sortCol, sortDir, search, userid, ClientId, SelectedclientId, ColumnList, ddlCurrency, CompanyNameText, clientType_con, Column_Lable, Column_Name, ContactText, OverRideCurrency, DefaultCurrencyName, CurrencyColumnName, DefaultCurrencyId);
        }
        public static DataTable GetBOMManagerGenrateTempData(int displayLength, int displayStart, int sortCol, string sortDir, string search, int userid, int ClientId, int SelectedclientId, string ColumnList, string ddlCurrency, string CompanyNameText, int clientType_con, string Column_Lable, string Column_Name, string ContactText, bool OverRideCurrency, string DefaultCurrencyName, string CurrencyColumnName, int DefaultCurrencyId)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.GetBOMManagerGenrateTempData(displayLength, displayStart, sortCol, sortDir, search, userid, ClientId, SelectedclientId, ColumnList, ddlCurrency, CompanyNameText, clientType_con, Column_Lable, Column_Name, ContactText, OverRideCurrency, DefaultCurrencyName, CurrencyColumnName, DefaultCurrencyId);
        }
        public static int saveBOMImportData(int userId,
                                            int ClientId,
                                            int SelectedclientId,
                                            string Column_Lable,
                                            string Column_Name,
                                            string insertDataList,
                                            string fileColName,
                                            string ddlCurrency,
                                            out System.String errorMessage,
                                            string BomName,
                                            string CompanyName,
                                            string ContactName,
                                            int SalesmanId,
                                            int CompanyId,
                                            int ContactId,
                                            bool PartWatch,
                                            int DefaultCurrencyId,
                                            bool OverRideCurrency,
                                            string SaveImportOrHubRFQ,
                                            out System.String NewBomCode,
                                            out System.Int32 NewBomid,
                                            System.Int32? ReqforTraceabilityId,
                                            System.Int32? TypeId,
                                            System.DateTime DateRequired,
                                            int? updateBomId,
                                            string originalFileName = null,
                                            string generatedFileName = null)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.saveBOMImportData(userId,
                                                                                 ClientId,
                                                                                 SelectedclientId,
                                                                                 Column_Lable,
                                                                                 Column_Name,
                                                                                 insertDataList,
                                                                                 fileColName,
                                                                                 ddlCurrency,
                                                                                 out errorMessage,
                                                                                 BomName,
                                                                                 CompanyName,
                                                                                 ContactName,
                                                                                 SalesmanId,
                                                                                 CompanyId,
                                                                                 ContactId,
                                                                                 PartWatch,
                                                                                 DefaultCurrencyId,
                                                                                 OverRideCurrency,
                                                                                 SaveImportOrHubRFQ,
                                                                                 out NewBomCode,
                                                                                 out NewBomid,
                                                                                 ReqforTraceabilityId,
                                                                                 TypeId,
                                                                                 DateRequired,
                                                                                 updateBomId,
                                                                                 originalFileName,
                                                                                 generatedFileName);
        }
        public static int UpdateHUBOfferImportLargeFileTempList(string jsonData, int updatedBy)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.UpdateHUBOfferImportLargeFileTempList(jsonData, updatedBy);
        }
        public static int GenerateGTVendorMFRHUBOfferImportLargeFileTemp(int id)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.GenerateGTVendorMFRHUBOfferImportLargeFileTemp(id);
        }
        public static int UpdateHUBOfferImportLargeFileStatus(int id, string status, int updatedBy)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.UpdateHUBOfferImportLargeFileStatus(id, status, updatedBy);
        }
        public static int CountInvalidHUBOfferImportLargeFileTemp(int id)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.CountInvalidHUBOfferImportLargeFileTemp(id);
        }

        public static List<HUBOfferImportLargeFileTemp> GetGTVendorMFRHUBOfferImportLargeFileTemp(string ids)
        {
            List<HUBOfferImportLargeFileTemp> lst = new List<HUBOfferImportLargeFileTemp>();

            List<HUBOfferImportLargeFileTempDetails> lstDetail = Rebound.GlobalTrader.DAL.SiteProvider.Stock.GetGTVendorMFRHUBOfferImportLargeFileTemp(ids);
            foreach (var detail in lstDetail)
            {
                HUBOfferImportLargeFileTemp temp = new HUBOfferImportLargeFileTemp();
                temp.ClientNo = detail.ClientNo;
                temp.COST = detail.COST;
                temp.DLUP = detail.DLUP;
                temp.HUBOfferImportLargeFileID = detail.HUBOfferImportLargeFileID;
                temp.LeadTime = detail.LeadTime;
                temp.MFR = detail.MFR;
                temp.MOQ = detail.MOQ;
                temp.MPN = detail.MPN;
                temp.OfferedDate = detail.OfferedDate;
                temp.OfferTempId = detail.OfferTempId;
                temp.Remarks = detail.Remarks;
                temp.Vendor = detail.Vendor;
                temp.SPQ = detail.SPQ;
                temp.GTVendor = detail.GTVendor;
                temp.GTMFR = detail.GTMFR;
                lst.Add(temp);
                temp = null;
            }
            return lst;
        }
        public static HUBOfferImportLargeFileTempList GetHUBOfferImportLargeFileTempList(int fileId, bool isShowMismatchOnly, int pageNumber, int pageSize)
        {
            HUBOfferImportLargeFileTempListDetails lstDetail = Rebound.GlobalTrader.DAL.SiteProvider.Stock.GetHUBOfferImportLargeFileTempList(fileId, isShowMismatchOnly, pageNumber, pageSize);
            HUBOfferImportLargeFileTempList lst = new HUBOfferImportLargeFileTempList();
            lst.lstHUBOfferImportLargeFileTemp = new List<HUBOfferImportLargeFileTemp>();
            lst.CurrentPage = pageNumber;
            lst.TotalRecords = lstDetail.TotalRecords;
            lst.TotalRecordsError = lstDetail.TotalRecordsError;
            foreach (var detail in lstDetail.lstHUBOfferImportLargeFileTemp)
            {
                HUBOfferImportLargeFileTemp temp = new HUBOfferImportLargeFileTemp();
                temp.ClientNo = detail.ClientNo;
                temp.COST = detail.COST;
                temp.DLUP = detail.DLUP;
                temp.HUBOfferImportLargeFileID = detail.HUBOfferImportLargeFileID;
                temp.LeadTime = detail.LeadTime;
                temp.MFR = detail.MFR;
                temp.MOQ = detail.MOQ;
                temp.MPN = detail.MPN;
                temp.OfferedDate = detail.OfferedDate;
                temp.OfferTempId = detail.OfferTempId;
                temp.Remarks = detail.Remarks;
                temp.Vendor = detail.Vendor;
                temp.SPQ = detail.SPQ;
                temp.GTMFR = detail.GTMFR;
                temp.GTVendor = detail.GTVendor;

                temp.LeadTimeMessage = detail.LeadTimeMessage;
                temp.MFRMessage = detail.MFRMessage;
                temp.MOQMessage = detail.MOQMessage;
                temp.MPNMessage = detail.MPNMessage;
                temp.OfferedDateMessage = detail.OfferedDateMessage;
                temp.SPQMessage = detail.SPQMessage;
                temp.RemarksMessage = detail.RemarksMessage;
                temp.VendorMessage = detail.VendorMessage;
                temp.COSTMessage = detail.COSTMessage;
                lst.lstHUBOfferImportLargeFileTemp.Add(temp);
                temp = null;
            }
            return lst;
        }

        public static int SaveHUBOfferImportLargeTemp(DataTable td, int updatedBy)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.SaveHUBOfferImportLargeTemp(td);
        }
        public static int CheckExistHUBOfferImportLarge(string originalFileName)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.CheckExistHUBOfferImportLarge(originalFileName);
        }
        public static int saveHUBOfferImportLarge(int userId, int ClientId, string originalFileName, string generatedFileName, string generatedErrorFileName, string status)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.saveHUBOfferImportLarge(userId, ClientId, originalFileName, generatedFileName, generatedErrorFileName, status);
        }
        public static int saveBOMManagerImportData(int userId, int ClientId, int SelectedclientId, string Column_Lable, string Column_Name, string insertDataList, string fileColName, string ddlCurrency, out System.String errorMessage, string BomName, string CompanyName, string ContactName, int SalesmanId, int CompanyId, int ContactId, bool PartWatch, int DefaultCurrencyId, bool OverRideCurrency, string SaveImportOrHubRFQ, out System.String NewBomCode, out System.Int32 NewBomid, System.Int32? ReqforTraceabilityId, System.Int32? TypeId, System.DateTime DateRequired, System.Int32? LastMappingId)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.saveBOMManagerImportData(userId, ClientId, SelectedclientId, Column_Lable, Column_Name, insertDataList, fileColName, ddlCurrency, out errorMessage, BomName, CompanyName, ContactName, SalesmanId, CompanyId, ContactId, PartWatch, DefaultCurrencyId, OverRideCurrency, SaveImportOrHubRFQ, out NewBomCode, out NewBomid, ReqforTraceabilityId, TypeId, DateRequired, LastMappingId);
        }

        public static DataTable GetBOMDetailFromTemp(int displayLength, int displayStart, int sortCol, string sortDir, string search, int userid, int ClientId, int SelectedclientId, int clientType_con)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.GetBOMDetailFromTemp(displayLength, displayStart, sortCol, sortDir, search, userid, ClientId, SelectedclientId, clientType_con);
        }
        public static void SaveBOMExcelHeader(string columnList, string insertColumnList, System.Int32? clientId, int? SelectedclientId, int? loginID, int clientType_con)
        {
            Rebound.GlobalTrader.DAL.SiteProvider.Stock.SaveBOMExcelHeader(columnList, insertColumnList, clientId, SelectedclientId, loginID, clientType_con);
        }
        //Manager start
        public static DataTable GetBOMManagerDetailFromTemp(int displayLength, int displayStart, int sortCol, string sortDir, string search, int userid, int ClientId, int SelectedclientId, int clientType_con)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.GetBOMManagerDetailFromTemp(displayLength, displayStart, sortCol, sortDir, search, userid, ClientId, SelectedclientId, clientType_con);
        }
        public static void SaveBOMExcelHeaderManager(string columnList, string insertColumnList, System.Int32? clientId, int? SelectedclientId, int? loginID, int clientType_con)
        {
            Rebound.GlobalTrader.DAL.SiteProvider.Stock.SaveBOMExcelHeaderManager(columnList, insertColumnList, clientId, SelectedclientId, loginID, clientType_con);
        }
        public static void saveBOMExcelBulkSaveManager(DataTable tempStock, DataTable dtData, string originalFilename, string generatedFilename, System.Int32 userId, System.Int32 clientId, int? SelectedclientId, int clientType_con, string DefaultCurrency)
        {
            Rebound.GlobalTrader.DAL.SiteProvider.Stock.saveBOMExcelBulkSaveManager(tempStock, dtData, originalFilename, generatedFilename, userId, clientId, SelectedclientId, clientType_con, DefaultCurrency);
        }
        //Manager end
        public static void saveBOMExcelBulkSave(DataTable tempStock, DataTable dtData, string originalFilename, string generatedFilename, System.Int32 userId, System.Int32 clientId, int? SelectedclientId, int clientType_con, string DefaultCurrency)
        {
            Rebound.GlobalTrader.DAL.SiteProvider.Stock.saveBOMExcelBulkSave(tempStock, dtData, originalFilename, generatedFilename, userId, clientId, SelectedclientId, clientType_con, DefaultCurrency);
        }
        public static DataTable GetBOMExcelHeaderFrom(System.Int32 clientStockId, System.Int32 userId, System.Int32 SelectedclientId, int clientType_con)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.GetBOMExcelHeaderFrom(clientStockId, userId, SelectedclientId, clientType_con);
        }
        public static DataTable GetBOMManagerExcelHeaderFrom(System.Int32 clientStockId, System.Int32 userId, System.Int32 SelectedclientId, int clientType_con)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.GetBOMManagerExcelHeaderFrom(clientStockId, userId, SelectedclientId, clientType_con);
        }

        public static DataTable GetBOMManagerExcelHeaderFromNew(System.Int32 clientStockId, System.Int32 userId, System.Int32 SelectedclientId, int clientType_con)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.GetBOMManagerExcelHeaderFromNew(clientStockId, userId, SelectedclientId, clientType_con);
        }

        public static DataTable GetBOMExcelHeader(System.Int32 clientStockId, System.Int32 userId, System.Int32 SelectedclientId, int clientType_con)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.GetBOMExcelHeader(clientStockId, userId, SelectedclientId, clientType_con);
        }
        public static DataTable GetBOMManagerExcelHeader(System.Int32 clientStockId, System.Int32 userId, System.Int32 SelectedclientId, int clientType_con)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.GetBOMManagerExcelHeader(clientStockId, userId, SelectedclientId, clientType_con);
        }
        public static int DeleteBOMRecord(System.Int32 SelectedclientId, System.Int32 userId, int ClientId, int clientType_con)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.DeleteBOMRecord(SelectedclientId, userId, ClientId, clientType_con);
        }
        public static int DeleteBOMManagerRecord(System.Int32 SelectedclientId, System.Int32 userId, int ClientId, int clientType_con)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.DeleteBOMManagerRecord(SelectedclientId, userId, ClientId, clientType_con);
        }

        public static DataTable GetContactWithCurrency(int companyId)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.GetContactWithCurrency(companyId);
        }
        public static DataTable GetMasterDataApi(int companyId)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.GetMasterDataApi(companyId);
        }

        #region Editable KPI Grid Soorya
        public static DataTable GetCurrencySymbol(System.Int32? clientId, System.String CurrencyCode)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.GetCurrencySymbol(clientId, CurrencyCode);
        }

        public static DataTable GetDivisionTeamNameByLoginid(System.Int32? clientId, System.Int32? userId, string selectKPIName)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.GetDivisionTeamNameByLoginid(clientId, userId, selectKPIName);
        }
        public static DataTable GetDDLDivisionKpi(System.Int32? clientId, System.Int32? userId)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.GetDDLDivisionKpi(clientId, userId);
        }

        public static DataTable GetDDLTeamKpi(System.Int32? clientId, System.Int32? userId, System.Int32? DivisionTargetNo)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.GetDDLTeamKpi(clientId, userId, DivisionTargetNo);
        }

        public static DataTable GetDDLSalesKpi(System.Int32? clientId, System.Int32? userId, System.Int32? Teamtargetno)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.GetDDLSalesKpi(clientId, userId, Teamtargetno);
        }

        #endregion

        /// <summary>
        /// GetDivisionKpi
        /// Calls [[usp_dropdown_DivisionKpi_for_Client]]
        /// </summary>
        public static DataTable GetDivisionKpi(System.Int32? clientId, System.Int32? userId)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.GetDivisionKpi(clientId, userId);
        }
        /// <summary>
        /// GetTeamKpi
        /// Calls [[usp_dropdown_TeamKpi_for_Client]]
        /// </summary>
        public static DataTable GetTeamKpi(System.Int32? clientId, System.Int32? userId)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.GetTeamKpi(clientId, userId);
        }

        public static DataTable GetTeamKpiDivision(int DivisionID)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.GetTeamKpiDivision(DivisionID);
        }
        public static DataTable GetTeamKpiSalesman(int TeamTargetID)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.GetTeamKpiSalesman(TeamTargetID);
        }

        //1 start
        public static void SaveStockExcelHeader(string columnList, string insertColumnList, System.Int32? clientId, int? SelectedclientId, int? loginID, int clientType_con)
        {
            Rebound.GlobalTrader.DAL.SiteProvider.Stock.SaveStockExcelHeader(columnList, insertColumnList, clientId, SelectedclientId, loginID, clientType_con);
        }
        public static void saveStockExcelBulkSave(DataTable tempStock, DataTable dtData, string originalFilename, string generatedFilename, System.Int32 userId, System.Int32 clientId, int? SelectedclientId, int clientType_con, string DefaultCurrency)
        {
            Rebound.GlobalTrader.DAL.SiteProvider.Stock.saveStockExcelBulkSave(tempStock, dtData, originalFilename, generatedFilename, userId, clientId, SelectedclientId, clientType_con, DefaultCurrency);
        }
        public static DataTable GetStockExcelHeader(System.Int32 clientStockId, System.Int32 userId, System.Int32 SelectedclientId, int clientType_con)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.GetStockExcelHeader(clientStockId, userId, SelectedclientId, clientType_con);
        }
        public static DataTable GetStockExcelHeaderFrom(System.Int32 clientStockId, System.Int32 userId, System.Int32 SelectedclientId, int clientType_con)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.GetStockExcelHeaderFrom(clientStockId, userId, SelectedclientId, clientType_con);
        }
        public static int DeleteStockRecord(System.Int32 SelectedclientId, System.Int32 userId, int ClientId, int clientType_con)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.DeleteStockRecord(SelectedclientId, userId, ClientId, clientType_con);
        }
        public static DataTable GetStockGenrateTempDataFromDB(int displayLength, int displayStart, int sortCol, string sortDir, string search, int userid, int ClientId, int SelectedclientId, string ColumnList, string ddlCurrency, string CompanyNameText, int clientType_con, string Column_Lable, string Column_Name, string ContactText, bool OverRideCurrency, string DefaultCurrencyName, string CurrencyColumnName, int DefaultCurrencyId, string SupplierNameText)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.GetStockGenrateTempDataFromDB(displayLength, displayStart, sortCol, sortDir, search, userid, ClientId, SelectedclientId, ColumnList, ddlCurrency, CompanyNameText, clientType_con, Column_Lable, Column_Name, ContactText, OverRideCurrency, DefaultCurrencyName, CurrencyColumnName, DefaultCurrencyId, SupplierNameText);
        }
        public static int saveStockImportDataFromDB(int userId, int ClientId, int SelectedclientId, string Column_Lable, string Column_Name, string insertDataList, string fileColName, string ddlCurrency, out System.String errorMessage, string BomName, string CompanyName, string ContactName, int SalesmanId, int CompanyId, int ContactId, bool PartWatch, int DefaultCurrencyId, bool OverRideCurrency, string SaveImportOrHubRFQ, System.Int32? LotId, System.Int32? DivisionId, System.String MSLLevel, System.Int32? SupplierId)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.saveStockImportDataFromDB(userId, ClientId, SelectedclientId, Column_Lable, Column_Name, insertDataList, fileColName, ddlCurrency, out errorMessage, BomName, CompanyName, ContactName, SalesmanId, CompanyId, ContactId, PartWatch, DefaultCurrencyId, OverRideCurrency, SaveImportOrHubRFQ, LotId, DivisionId, MSLLevel, SupplierId);
        }

        public static DataTable GetStocksDetailFromTemp(int displayLength, int displayStart, int sortCol, string sortDir, string search, int userid, int ClientId, int SelectedclientId, int clientType_con)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.GetStocksDetailFromTemp(displayLength, displayStart, sortCol, sortDir, search, userid, ClientId, SelectedclientId, clientType_con);
        }
        public static DataTable GetSupplierSearch(System.Int32? mfrid, System.Int32? prodCatgid, System.Int32? suppid, System.String industryType, System.String supplierType, System.Int32? lastOrderDate, System.Int32? prodId, System.Int32? GlobalProductNameId, System.Int32? MfrGroupId, System.Int32? clientId)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.GetSupplierSearch(mfrid, prodCatgid, suppid, industryType, supplierType, lastOrderDate, prodId, GlobalProductNameId, MfrGroupId, clientId);
        }
        public static DataTable GetSupplierType(System.Int32? ClientNo, System.Int32? MfrId)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.GetSupplierType(ClientNo, MfrId);
        }
        public static DataTable ProductCategory()
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.ProductCategory();
        }
        public static DataTable GetProductByCategory(System.Int32? ClientNo, System.Int32? ProdId)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.GetProductByCategory(ClientNo, ProdId);
        }
        public static DataTable GetGlobalProductNameByCategory(System.Int32? ProductCategoryId)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.GetGlobalProductNameByCategory(ProductCategoryId);
        }



        //1 end

        //save mapping for bom import toll
        public static int SaveCompanyColumnMapping(int CompanyId, string insertMapColumnList, int clientType_con)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.SaveCompanyColumnMapping(CompanyId, insertMapColumnList, clientType_con);
        }
        public static int SaveCompanyColumnMappingBOMManager(int CompanyId, string insertMapColumnList, int clientType_con)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.SaveCompanyColumnMappingBOMManager(CompanyId, insertMapColumnList, clientType_con);
        }
        public static int SaveBOMManagerExcelColumnHeaderDetails(string insertMapColumnList, string MappingHeaderColumns, int? clientId, int CompanyId, int? LoggedId, Boolean mappingType = false)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.SaveBOMManagerExcelColumnHeaderDetails(insertMapColumnList, MappingHeaderColumns, clientId, CompanyId, LoggedId, mappingType);
        }

        public static int SaveBOMManagerAPiData(string insertMapColumnList, string MappingHeaderColumns, int? clientId, int CompanyId, int? LoggedId, Boolean mappingType = false)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.SaveBOMManagerAPI(insertMapColumnList, MappingHeaderColumns, clientId, CompanyId, LoggedId, mappingType);
        }

        public static void DeleteCompanyTempMapping(int CompanyId, int clientType_con)
        {
            Rebound.GlobalTrader.DAL.SiteProvider.Stock.DeleteCompanyTempMapping(CompanyId, clientType_con);
        }
        public static void DeleteCompanyTempMappingBOMManager(int CompanyId, int clientType_con)
        {
            Rebound.GlobalTrader.DAL.SiteProvider.Stock.DeleteCompanyTempMappingBOMManager(CompanyId, clientType_con);
        }
        public static DataTable GetCompanyMappedColumn(int CompanyId, int clientType_con)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.GetCompanyMappedColumn(CompanyId, clientType_con);
        }
        public static DataTable GetCompanyMappedColumnBOMManager(int CompanyId, int clientType_con)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.GetCompanyMappedColumnBOMManager(CompanyId, clientType_con);
        }


        public static DataTable GetClientDivisionName(System.Int32? ClientID, System.Int32? clientType_con)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.GetClientDivisionName(ClientID, clientType_con);
        }
        public static DataTable GetClientWarehouse(System.Int32? ClientID, System.Int32? clientType_con)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.GetClientWarehouse(ClientID, clientType_con);
        }
        public static DataTable GetClientLot(System.Int32? ClientID, System.Int32? clientType_con)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.GetClientLot(ClientID, clientType_con);
        }

        public static DataTable GetCompanyType(System.Int32? SelectedCompanyId)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.GetCompanyType(SelectedCompanyId);
        }

        /// <summary>
        /// Delete
        /// Calls [usp_deleteOfferRecord]
        /// </summary>
        public static int Delete_EPORecord(string OfferId, System.Int32 BomId, System.Int32 userId, int ClientId, string OfferType)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.Delete_EPORecord(OfferId, BomId, userId, ClientId, OfferType);
        }

        public static DataTable GetLogImportactivity(int displayLength, int displayStart, int sortCol, string sortDir, string search, int ClientId, int clientType_con, int SelectedClientId, int userLoginid)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.GetLogImportactivity(displayLength, displayStart, sortCol, sortDir, search, ClientId, clientType_con, SelectedClientId, userLoginid);
        }
        //code end


        //code start for Reverse Logistic

        /// <summary>
        /// Insert Epo Header Column Heading
        /// Calls [usp_SaveEpoExcelColumnHeading]
        /// </summary>
        public static void SaveExcelReverseLogisticHeader(string columnList, string insertColumnList, System.Int32? clientId, int? SelectedclientId, int? loginID, int clientType_con)
        {
            Rebound.GlobalTrader.DAL.SiteProvider.Stock.SaveExcelReverseLogisticHeader(columnList, insertColumnList, clientId, SelectedclientId, loginID, clientType_con);
        }
        /// <summary>
        /// Insert Epo Temp Data 
        /// Calls [usp_SaveEpoExcelColumnHeading]
        /// </summary>
        public static void saveExcelReverseLogisticBulkSave(DataTable tempEpo, DataTable dtData, string originalFilename, string generatedFilename, System.Int32 userId, System.Int32 clientId, int? SelectedclientId, int clientType_con)
        {
            Rebound.GlobalTrader.DAL.SiteProvider.Stock.saveExcelReverseLogisticBulkSave(tempEpo, dtData, originalFilename, generatedFilename, userId, clientId, SelectedclientId, clientType_con);
        }

        /// <summary>
        /// Select Epo Header Column 
        /// Calls [usp_GetEpoExcelHeaderColumn]
        /// </summary>
        public static DataTable GetReverseLogisticExcelHeader(System.Int32 clientStockId, System.Int32 userId, System.Int32 SelectedclientId, int clientType_con)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.GetReverseLogisticExcelHeader(clientStockId, userId, SelectedclientId, clientType_con);
        }
        /// <summary>
        /// Select Epo Data from temp table
        /// Calls [usp_GetEpobindTempData]
        /// </summary>
        public static DataTable GetReverseLogisticDetailFromTemp(int displayLength, int displayStart, int sortCol, string sortDir, string search, int userid, int ClientId, int SelectedclientId, int clientType_con)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.GetReverseLogisticDetailFromTemp(displayLength, displayStart, sortCol, sortDir, search, userid, ClientId, SelectedclientId, clientType_con);
        }
        /// <summary>
        /// Delete Epo Record
        /// Calls [usp_deleteTempEpoRecord]
        /// </summary>
        public static int DeleteReverseLogisticRecord(System.Int32 SelectedclientId, System.Int32 userId, int ClientId, int clientType_con)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.DeleteReverseLogisticRecord(SelectedclientId, userId, ClientId, clientType_con);
        }

        /// <summary>
        /// Get Genrate Epo Data
        /// Calls [usp_GenrateDynamicEpoData]
        /// </summary>
        public static DataTable GetReverseLogisticGenrateTempData(int displayLength, int displayStart, int sortCol, string sortDir, string search, int userid, int ClientId, int SelectedclientId, string SupplierNameText, int clientType_con, System.Double? UpliftpValue, int NoChangesToSourcingSheet)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.GetReverseLogisticGenrateTempData(displayLength, displayStart, sortCol, sortDir, search, userid, ClientId, SelectedclientId, SupplierNameText, clientType_con, UpliftpValue, NoChangesToSourcingSheet);
        }

        /// <summary>
        /// Insert Epo Data
        /// Calls [usp_Select_ImportEpoData]
        /// </summary>
        public static int saveReverseLogisticImportData(int userId, int ClientId, int SelectedclientId, int SupplierId, int recordType, int clientType_con, System.Double? UpliftPercentage, int NoChangesToSourcingSheet, out System.String errorMessage)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.saveReverseLogisticImportData(userId, ClientId, SelectedclientId, SupplierId, recordType, clientType_con, UpliftPercentage, NoChangesToSourcingSheet, out errorMessage);
        }
        /// <summary>
        /// Insert Epo Import Activity Data
        /// Calls [usp_Select_EpoImportEpoData]
        /// </summary>
        public static int SaveReverseLogisticImportActivity(string insertDataList, int clientType_con)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.SaveReverseLogisticImportActivity(insertDataList, clientType_con);
        }

        /// <summary>
        /// Get Epo Import Activity Data
        /// Calls [usp_select_ReverseLogistic_ImportHistory]
        /// </summary>
        public static DataTable GetReverseLogisticImportactivity(int displayLength, int displayStart, int sortCol, string sortDir, string search, int ClientId, int clientType_con, int SelectedClientId, int userLoginid, Boolean InactiveId)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.GetReverseLogisticImportactivity(displayLength, displayStart, sortCol, sortDir, search, ClientId, clientType_con, SelectedClientId, userLoginid, InactiveId);
        }

        /// <summary>
        /// Delete
        /// Calls [usp_deleteOfferRecord]
        /// </summary>
        public static int Delete_ReverseLogisticRecord(string OfferId, System.Int32 BomId, System.Int32 userId, int ClientId, string OfferType)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.Delete_ReverseLogisticRecord(OfferId, BomId, userId, ClientId, OfferType);
        }

        /// <summary>
        /// ExportToExcel
        /// Calls [usp_ExcelUpload_Error_ReverseLogistics]
        /// </summary>
        public static DataTable GetExportToExcelError(int userid, int ClientId, int SelectedclientId)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.GetExportToExcelError(userid, ClientId, SelectedclientId);
        }
        //code end for Reverse Logistic
        public static DataTable InactivateReverseLogistics(int userid, int ClientId, int SelectedImportId)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.InactivateReverseLogistics(userid, ClientId, SelectedImportId);
        }
        public static DataTable GetExportToExcelError_BomManager(int userid, int ClientId, int SelectedclientId, string mappingcolumlist, string fileHeaderCheck)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.GetExportToExcelError_BomManager(userid, ClientId, SelectedclientId, mappingcolumlist, fileHeaderCheck);
        }

        public static DataTable GetStockDetailFromTemp_PriceQuote(int displayLength, int displayStart, int sortCol, string sortDir, string search, int userid, int ClientId, int SelectedclientId, int clientType_con)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.GetStockDetailFromTemp_PriceQuote(displayLength, displayStart, sortCol, sortDir, search, userid, ClientId, SelectedclientId, clientType_con);
        }
        public static DataTable GetExportToExcelError_PriceQuote(int userid, int clientId, string targetColumns, string selectedColumns)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.GetExportToExcelError_PriceQuote(userid, clientId, targetColumns, selectedColumns);
        }
        public static DataTable GetPriceQuoteGenrateTempData(int displayLength, int displayStart, int sortCol, string sortDir, string search, int userid, int ClientId, int SelectedclientId, string ColumnList, string ddlCurrency, string SupplierNameText, int clientType_con, string Column_Lable, string Column_Name)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.GetPriceQuoteGenrateTempData(displayLength, displayStart, sortCol, sortDir, search, userid, ClientId, SelectedclientId, ColumnList, ddlCurrency, SupplierNameText, clientType_con, Column_Lable, Column_Name);
        }
        public static DataTable GetPriceQuoteDetailFromTemp(int displayLength, int displayStart, int sortCol, string sortDir, string search, int userid, int ClientId, int SelectedclientId, int clientType_con)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.GetPriceQuoteDetailFromTemp(displayLength, displayStart, sortCol, sortDir, search, userid, ClientId, SelectedclientId, clientType_con);
        }
        public static DataTable GetExcelHeader_PriceQuote(System.Int32 clientStockId, System.Int32 userId, System.Int32 SelectedclientId, int clientType_con)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.GetExcelHeader_PriceQuote(clientStockId, userId, SelectedclientId, clientType_con);
        }
        public static void SaveExcelHeader_PriceQuote(string columnList, string insertColumnList, System.Int32? clientId, int? SelectedclientId, int? loginID, int clientType_con)
        {
            Rebound.GlobalTrader.DAL.SiteProvider.Stock.SaveExcelHeader_PriceQuote(columnList, insertColumnList, clientId, SelectedclientId, loginID, clientType_con);
        }
        public static DataTable GetExcelHeaderFrom_PriceQuote(System.Int32 clientStockId, System.Int32 userId, System.Int32 SelectedclientId, int clientType_con)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.GetExcelHeaderFrom_PriceQuote(clientStockId, userId, SelectedclientId, clientType_con);
        }
        public static void saveExcelBulkSave_PriceQuote(DataTable tempStock, DataTable dtData, string originalFilename, string generatedFilename, System.Int32 userId, System.Int32 clientId, int? SelectedclientId, int clientType_con)
        {
            Rebound.GlobalTrader.DAL.SiteProvider.Stock.saveExcelBulkSave_PriceQuote(tempStock, dtData, originalFilename, generatedFilename, userId, clientId, SelectedclientId, clientType_con);
        }

        public static int InsertUpdatePriceQuoteImportData(int userId, int ClientId, int SelectedclientId, string Column_Lable, string Column_Name, string insertDataList, string fileColName, int SupplierId, string ddlCurrency, int recordType, int clientType_con, out System.String errorMessage, out System.Int32 PriceQuoteNo)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.InsertUpdatePriceQuoteImportData(userId, ClientId, SelectedclientId, Column_Lable, Column_Name, insertDataList, fileColName, SupplierId, ddlCurrency, recordType, clientType_con, out errorMessage, out PriceQuoteNo);
        }

        public static int DeleteRecord_PriceQuote(System.Int32 SelectedclientId, System.Int32 userId, int ClientId, int clientType_con)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.DeleteRecord_PriceQuote(SelectedclientId, userId, ClientId, clientType_con);
        }

        public static DataTable GetSupplierMappedColumn_PriceQuote(int userId)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.GetSupplierMappedColumn_PriceQuote(userId);
        }

        public static int SaveSupplierColumnMapping_PriceQuote(int userId, string insertMapColumnList)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.SaveSupplierColumnMapping_PriceQuote(userId, insertMapColumnList);
        }

        public static DataTable GetStockImportactivity_PriceQuote(int displayLength, int displayStart, int sortCol, string sortDir, string search, int ClientId, int clientType_con, int SelectedClientId, int userLoginid)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.GetStockImportactivity_PriceQuote(displayLength, displayStart, sortCol, sortDir, search, ClientId, clientType_con, SelectedClientId, userLoginid);
        }
        //[009] code start
        //for Altranative Tool  code start
        /// <summary>
        /// Calls [usp_Insert_AltranativeHeaderExcelColumn]
        /// </summary>
        public static void SaveAltranativeHeader(string columnList, string insertColumnList, System.Int32? clientId, int? SelectedclientId, int? loginID, int clientType_con)
        {
            Rebound.GlobalTrader.DAL.SiteProvider.Stock.SaveAltranativeHeader(columnList, insertColumnList, clientId, SelectedclientId, loginID, clientType_con);
        }

        /// <summary>
        /// Calls [usp_Insert_AltranativeExcelRows]
        /// </summary>
        public static void InsertAltranativeExcelRows(DataTable tempStock, DataTable dtData, string originalFilename, string generatedFilename, System.Int32 userId, System.Int32 clientId, int? SelectedclientId, int clientType_con)
        {
            Rebound.GlobalTrader.DAL.SiteProvider.Stock.InsertAltranativeExcelRows(tempStock, dtData, originalFilename, generatedFilename, userId, clientId, SelectedclientId, clientType_con);
        }
        /// <summary>
        /// Calls [usp_Select_All_AltranativeRowsFromTemp]
        /// </summary>
        public static DataTable Get_All_AltranativeRowsFromTemp(int displayLength, int displayStart, int sortCol, string sortDir, string search, int userid, int ClientId, int SelectedclientId, int clientType_con)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.Get_All_AltranativeRowsFromTemp(displayLength, displayStart, sortCol, sortDir, search, userid, ClientId, SelectedclientId, clientType_con);
        }

        /// <summary>
        /// Calls [usp_Select_ALL_AltranativeHeaderColumnTemp]
        /// </summary>
        public static DataTable Get_ALL_AltranativeHeaderColumnTemp(System.Int32 clientStockId, System.Int32 userId, System.Int32 SelectedclientId, int clientType_con)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.Get_ALL_AltranativeHeaderColumnTemp(clientStockId, userId, SelectedclientId, clientType_con);
        }
        /// <summary>
        /// Calls [usp_Select_ALL_AltHeaderColumnDropdownBind]
        /// </summary>
        public static DataTable Get_Select_ALL_AltHeaderColumnDropdownBind(System.Int32 clientStockId, System.Int32 userId, System.Int32 SelectedclientId, int clientType_con)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.Get_Select_ALL_AltHeaderColumnDropdownBind(clientStockId, userId, SelectedclientId, clientType_con);
        }

        /// <summary>
        /// Calls [usp_Delete_AltranativeTempRecord]
        /// </summary>
        public static int DeleteAltranativeTempRecord(System.Int32 SelectedclientId, System.Int32 userId, int ClientId, int clientType_con)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.DeleteAltranativeTempRecord(SelectedclientId, userId, ClientId, clientType_con);
        }
        /// <summary>
        /// Calls [usp_Delete_AltranativeTempMapping]
        /// </summary>
        public static void DeleteAltranativeTempMapping(int SupplierId, int clientType_con)
        {
            Rebound.GlobalTrader.DAL.SiteProvider.Stock.DeleteAltranativeTempMapping(SupplierId, clientType_con);
        }
        /// <summary>
        /// Calls [usp_Insert_AltranativeSupplierColumnMapping]
        /// </summary>
        public static int InsertAltranativeSupplierColumnMapping(int SupplierId, string insertMapColumnList, int clientType_con)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.InsertAltranativeSupplierColumnMapping(SupplierId, insertMapColumnList, clientType_con);
        }
        /// <summary>
        /// Calls [usp_Select_AltranativeSupplierMappedColumn]
        /// </summary>
        public static DataTable GetAltranativeSupplierMappedColumn(int SupplierId, int clientType_con)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.GetAltranativeSupplierMappedColumn(SupplierId, clientType_con);
        }
        /// <summary>
        /// Calls [usp_Select_GenerateAlternativeGridBind]
        /// </summary>
        public static DataTable GenerateAlternativeGridBind(int displayLength, int displayStart, int sortCol, string sortDir, string search, int userid, int ClientId, int SelectedclientId, string ColumnList, string ddlCurrency, string SupplierNameText, int clientType_con, string Column_Lable, string Column_Name)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.GenerateAlternativeGridBind(displayLength, displayStart, sortCol, sortDir, search, userid, ClientId, SelectedclientId, ColumnList, ddlCurrency, SupplierNameText, clientType_con, Column_Lable, Column_Name);
        }
        /// <summary>
        /// Calls [usp_Insert_Alternative_ImportData]
        /// </summary>
        public static int InsertAlternativeImportData(int userId, int ClientId, int SelectedclientId, string Column_Lable, string Column_Name, string insertDataList, string fileColName, int SupplierId, string ddlCurrency, int recordType, int clientType_con, out System.String errorMessage)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.InsertAlternativeImportData(userId, ClientId, SelectedclientId, Column_Lable, Column_Name, insertDataList, fileColName, SupplierId, ddlCurrency, recordType, clientType_con, out errorMessage);
        }
        /// <summary>
        /// Calls [usp_Select_GenerateDataAlternativeForCount]
        /// </summary>
        public static DataTable GenerateDataAlternativeForCount(int displayLength, int displayStart, int sortCol, string sortDir, string search, int userid, int ClientId, int SelectedclientId, int clientType_con)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.GenerateDataAlternativeForCount(displayLength, displayStart, sortCol, sortDir, search, userid, ClientId, SelectedclientId, clientType_con);
        }
        //for Altranative Tool  code end
        //[009] code start

        /// <summary>
        /// ExportToExcel
        /// Calls [usp_ExcelUpload_Error_StockImport]
        /// </summary>
        public static DataTable GetExportToExcelError_StockImport(int userid, int ClientId, int SelectedclientId, string ColumnList, string Column_Lable, string Column_Name)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.GetExportToExcelError_StockImport(userid, ClientId, SelectedclientId, ColumnList, Column_Lable, Column_Name);
        }

        /// <summary>
        /// ExportToExcel
        /// Calls [usp_ExcelUpload_Error_StrategicOffer]
        /// </summary>
        public static DataTable GetExportToExcelError_StrategicOffer(int userid, int ClientId, int SelectedclientId)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.GetExportToExcelError_StrategicOffer(userid, ClientId, SelectedclientId);
        }

        /// <summary>
        /// ExportToExcel
        /// Calls [usp_ExcelUpload_Error_BomImport]
        /// </summary>
        public static DataTable GetExportToExcelError_BomImport(int userid,
                                                                int ClientId,
                                                                int SelectedclientId,
                                                                string ColumnList,
                                                                string Column_Lable,
                                                                string Column_Name)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Stock.GetExportToExcelError_BomImport(userid, ClientId, SelectedclientId, ColumnList, Column_Lable, Column_Name);
        }
        public static DataTable GetPriceQuoteDataTobeImported(int displayLength, int userId)
        {
            return SiteProvider.Stock.GetPriceQuoteDataTobeImported(displayLength, userId);
        }
        /// <summary>
        /// Call usp_Import_PriceQuote
        /// </summary>
        /// <returns>number of records import success</returns>
        public static int ImportPriceQuote(int userId, out int priceQuoteId, out int priceQuoteNumber, out string message)
        {
            return SiteProvider.Stock.ImportPriceQuote(userId, out priceQuoteId, out priceQuoteNumber, out message);
        }


        public static void SaveBOMImportSourcingData(DataTable dtData, string originalFilename, string generatedFilename, int hubrfqId, int loginId)
        {
            SiteProvider.Stock.SaveBOMImportSourcingData(dtData, originalFilename, generatedFilename, hubrfqId, loginId);
        }

        public static int ImportBOMSourcingResults(int loginId, out string outputMessage)
        {
            return SiteProvider.Stock.ImportBOMSourcingResults(loginId, out outputMessage);
        }

        public static DataTable GetHUBOfferImportLargeHistory(int displayLength, int displayStart, int sortCol, string sortDir)
        {
            return SiteProvider.Stock.GetHUBOfferImportLargeHistory(displayLength, displayStart, sortCol, sortDir);
        }

        public static void UpdateHUBOfferImportLargeCount(int logindId, int importId, int total)
        {
            SiteProvider.Stock.UpdateHUBOfferImportLargeCount(logindId, importId, total);
        }

        public static void DeleteHUBOfferImportLarge(int logindId, int importId)
        {
            SiteProvider.Stock.DeleteHUBOfferImportLarge(logindId, importId);
        }

        public static int UpdateVendorsOfferImportByExcelTemp(int importFileId, string incorrectVendor, string newVendor, int clientNo, int updatedBy)
        {
            return DAL.SiteProvider.Stock.UpdateVendorsOfferImportByExcelTemp(importFileId, incorrectVendor, newVendor, clientNo, updatedBy);
        }

        public static int UpdateMFRsOfferImportByExcelTemp(int importFileId, string incorrectMFR, string newMFR, int clientNo, int updatedBy)
        {
            return DAL.SiteProvider.Stock.UpdateMFRsOfferImportByExcelTemp(importFileId, incorrectMFR, newMFR, clientNo, updatedBy);
        }

        public static bool DeleteBomTempData(int loginId, string generatedFileName, int clientId, int selectedClientId)
        {
            return SiteProvider.Stock.DeleteBomTempData(loginId,generatedFileName, clientId, selectedClientId);
        }

        public static HubSourcingResultImportTemp GetHubSourcingTempData(int loginId, int pageNumber, int pageSize, bool showMismatchOnly)
        {
            var objDetails = SiteProvider.Stock.GetHubSourcingTempData(loginId, pageNumber, pageSize, showMismatchOnly);
            if (objDetails == null)
                return new HubSourcingResultImportTemp();

            var obj = new HubSourcingResultImportTemp
            {
                CurrentPage = pageNumber,
                TotalRecords = objDetails.TotalRecords,
                TotalRecordsError = objDetails.TotalRecordsError
            };
            foreach (var rowDetails in objDetails.RawDataList)
            {
                var row = new SourcingResultImportTemp()
                {
                    BomImportSourcingId = rowDetails.BomImportSourcingId,
                    Requirement = rowDetails.Requirement,
                    RequirementMessage = rowDetails.RequirementMessage,
                    CustomerRefNo = rowDetails.CustomerRefNo,
                    CustomerRefNoMessage = rowDetails.CustomerRefNoMessage,
                    Supplier = rowDetails.Supplier,
                    SupplierMessage = rowDetails.SupplierMessage,
                    SupplierPart = rowDetails.SupplierPart,
                    SupplierPartMessage = rowDetails.SupplierPartMessage,
                    SupplierCost = rowDetails.SupplierCost,
                    SupplierCostMessage = rowDetails.SupplierCostMessage,
                    ROHS = rowDetails.ROHS,
                    ROHSMessage = rowDetails.ROHSMessage,
                    MFR = rowDetails.MFR,
                    MFRMessage = rowDetails.MFRMessage,
                    DateCode = rowDetails.DateCode,
                    DateCodeMessage = rowDetails.DateCodeMessage,
                    Package = rowDetails.Package,
                    PackageMessage = rowDetails.PackageMessage,
                    OfferedQuantity = rowDetails.OfferedQuantity,
                    OfferedQuantityMessage = rowDetails.OfferedQuantityMessage,
                    OfferStatus = rowDetails.OfferStatus,
                    OfferStatusMessage = rowDetails.OfferStatusMessage,
                    SPQ = rowDetails.SPQ,
                    SPQMessage = rowDetails.SPQMessage,
                    FactorySealed = rowDetails.FactorySealed,
                    FactorySealedMessage = rowDetails.FactorySealedMessage,
                    QtyInStock = rowDetails.QtyInStock,
                    QtyInStockMessage = rowDetails.QtyInStockMessage,
                    MOQ = rowDetails.MOQ,
                    MOQMessage = rowDetails.MOQMessage,
                    LastTimeBuy = rowDetails.LastTimeBuy,
                    LastTimeBuyMessage = rowDetails.LastTimeBuyMessage,
                    Currency = rowDetails.Currency,
                    CurrencyMessage = rowDetails.CurrencyMessage,
                    BuyPrice = rowDetails.BuyPrice,
                    BuyPriceMessage = rowDetails.BuyPriceMessage,
                    SellPrice = rowDetails.SellPrice,
                    SellPriceMessage = rowDetails.SellPriceMessage,
                    ShippingCost = rowDetails.ShippingCost,
                    ShippingCostMessage = rowDetails.ShippingCostMessage,
                    LeadTime = rowDetails.LeadTime,
                    LeadTimeMessage = rowDetails.LeadTimeMessage,
                    Region = rowDetails.Region,
                    RegionMessage = rowDetails.RegionMessage,
                    DeliveryDate = rowDetails.DeliveryDate,
                    DeliveryDateMessage = rowDetails.DeliveryDateMessage,
                    Notes = rowDetails.Notes,
                    NotesMessage = rowDetails.NotesMessage,
                    MSL = rowDetails.MSL,
                    MSLMessage = rowDetails.MSLMessage
                };
                obj.RawDataList.Add(row);
            }

            foreach (var itemDetails in objDetails.IncorrectDataList)
            {
                var item = new SRImportTempIncorrect()
                {
                    ID = itemDetails.ID,
                    Value = itemDetails.Value,
                    Type = itemDetails.Type,
                    Count = itemDetails.Count
                };
                obj.IncorrectDataList.Add(item);
            }

            return obj;
        }

        public static void DeleteHubSourcingTempData(int loginId)
        {
            SiteProvider.Stock.DeleteHubSourcingTempData(loginId);
        }
        public static int CorrectHubSourcingTempData(string incorrectValue, string newValue, string type, int loginId)
        {
            return DAL.SiteProvider.Stock.CorrectHubSourcingTempData(incorrectValue, newValue, type, loginId);
        }

        public static int BulkSaveHubSourcingTempData(string jsonData, int loginId)
        {
            return DAL.SiteProvider.Stock.BulkSaveHubSourcingTempData(jsonData, loginId);
        }

        /// <summary>
        /// Get part number list to call IHS API when import BOM
        /// </summary>
        /// <param name="loginId"></param>
        /// <param name="clientId"></param>
        /// <param name="selectedClientId"></param>
        /// <returns></returns>
        public static List<string> GetBOMPartsForIHS(int loginId, int clientId, int selectedClientId)
        {
            return SiteProvider.Stock.GetBOMPartsForIHS(loginId, clientId, selectedClientId);
        }
    }
}
