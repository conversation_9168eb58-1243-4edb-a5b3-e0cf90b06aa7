﻿/*     
===========================================================================================    
TASK			UPDATED BY      DATE			ACTION  DESCRIPTION    
[US-207821]		An.TranTan		26-Aug-2024		CREATE  Check KUB enable whenever sections in KUB assistant has values 
[US-212628]	    An.TranTan		19-Sep-2024		UPDATE	Check lytica data for client side

===========================================================================================    
*/    
    
CREATE OR ALTER   PROCEDURE [dbo].[usp_check_KubEnableForBOMPart]      
 @PartNo  NVARCHAR(100) = NULL,                  
 @ClientID  INT = 0,    
 @BOMID INT = 0,    
 @IsHubRFQ BIT = 0,    
 @ManufacturerID INT = NULL,    
 @ManufacturerName NVARCHAR(500) = NULL    
AS    
BEGIN    
SET NOCOUNT ON  
 DECLARE @Last12Months DATETIME = dateadd(month,datediff(month,0,getdate())-12,0);  
 DECLARE @CustomerNo INT;  
 IF(@IsHubRFQ = 1)    
 BEGIN    
  SELECT @CustomerNo = CompanyNo FROM dbo.tbBOM WITH (NOLOCK) WHERE BOMId = @BOMID;    
 END    
 ELSE BEGIN    
  SELECT @CustomerNo = CompanyNo FROM dbo.tbBOMManager WITH (NOLOCK) WHERE BOMManagerId = @BOMID;    
 END  
   
 --Get all data if client is POHub    
 SET @ClientID = CASE WHEN @ClientID = 114 THEN 0     
  ELSE ISNULL(@ClientID, 0)     
    END;  
  
    /*============ Check values for CUSTOMER QUOTES section ============*/  
 -- Latest Quote Price to Customer  
 -- 10 Most recent Quotes in the last 12 months  
 -- Win/Loss Ratio of selected part for selected customer in last 12 months: same as Latest Quote Price  
 IF EXISTS (SELECT TOP 1 1 FROM tbQuoteLine qol WITH (NOLOCK)    
      JOIN tbQuote qt WITH (NOLOCK) ON qol.QuoteNo = qt.QuoteId  
      WHERE qol.FullPart = @PartNo                             
       AND (@ClientID = 0 OR qt.ClientNo = @ClientID)   
 )  
 BEGIN  
  SELECT CAST(1 AS BIT) AS IsAllowedEnable;  
  RETURN;  
 END  
   
 -- Latest Offer shared by HUB  
 IF EXISTS (SELECT TOP 1 1 FROM tbSourcingresult sr WITH (NOLOCK)    
      JOIN tbCustomerRequirement cust WITH (NOLOCK) ON sr.CustomerRequirementNo=cust.CustomerRequirementId  
      JOIN tbBOM bom WITH (NOLOCK) ON bom.BOMId = cust.BOMNo  
      WHERE sr.FullPart = @PartNo   
       AND sr.SourcingTable IN ('EPPH','EXPH','OFPH','RLPH')     
       AND (@ClientID = 0 OR bom.ClientNo = @ClientID)  
 )  
 BEGIN  
  SELECT CAST(1 AS BIT) AS IsAllowedEnable;  
  RETURN;  
 END  
  
 -- Top 3 Buy Price in last 6 months (for DMCC side)  
 IF (@ClientID = 0 AND EXISTS (    
  SELECT TOP 1 1 FROM tbKUBPoDetailsCache WITH (NOLOCK)    
  WHERE Part = @PartNo AND (@ClientID = 0 OR ClientNo = @ClientID)    
 ))  
 BEGIN  
  SELECT CAST(1 AS BIT) AS IsAllowedEnable;  
  RETURN;  
 END  
  
 /*============ Check values for REQUIREMENTS section ============*/  
 -- Number of Customer Requirements of this part in the last 12 months  
 IF EXISTS (SELECT TOP 1 1 FROM tbCustomerRequirement WITH (NOLOCK)    
      WHERE FullPart = @PartNo    
       AND (@ClientID = 0 OR ClientNo = @ClientID)    
       AND ISNULL(BOMManagerNo, 0) = 0  
       AND ReceivedDate >= @Last12Months  
 )  
 BEGIN  
  SELECT CAST(1 AS BIT) AS IsAllowedEnable;  
  RETURN;  
 END  
  
 -- Lastest HUBRFQ for the selected part  
 IF EXISTS (SELECT TOP 1 1 FROM tbCustomerRequirement cr WITH (NOLOCK)   
       JOIN tbBOM bom WITH (NOLOCK) ON bom.BOMId = cr.BOMNo    
      WHERE cr.FullPart = @PartNo     
       AND (@ClientID = 0 OR cr.ClientNo = @ClientID)  
 )  
 BEGIN  
  SELECT CAST(1 AS BIT) AS IsAllowedEnable;  
  RETURN;  
 END  
  
 /*============ Check values for SALES section ============*/  
 -- Price last invoiced to this Customer for the selected part in the last 12 months  
 IF EXISTS (SELECT TOP 1 1 FROM vwInvoiceLine inl                                                           
      WHERE inl.FullPart = @PartNo     
       AND inl.ShippedDate >= @Last12Months    
       AND (@ClientID = 0 OR inl.ClientNo = @ClientID)    
       AND inl.CompanyNo = @CustomerNo  
 )  
 BEGIN  
  SELECT CAST(1 AS BIT) AS IsAllowedEnable;  
  RETURN;  
 END  
  
 /*============ Check values for API DATA section ============*/  
 -- IHS data  
 IF EXISTS (SELECT TOP 1 1 FROM tbIHSparts WITH (NOLOCK)    
      WHERE FullPart = @PartNo    
       AND (ManufacturerNo = @ManufacturerID OR ManufacturerName = @ManufacturerName)    
       AND ISNULL(Inactive, 0) = 0   
 )  
 BEGIN  
  SELECT CAST(1 AS BIT) AS IsAllowedEnable;  
  RETURN;  
 END  
  
 -- Lytica data 
 IF (EXISTS (SELECT TOP 1 1 FROM tbLyticaAPI WITH (NOLOCK)    
            WHERE dbo.ufn_get_fullpart(OriginalPartSearched) = @PartNo    
            AND Manufacturer = @ManufacturerName    
            AND ISNULL(Inactive, 0) = 0)  
 )  
 BEGIN  
  SELECT CAST(1 AS BIT) AS IsAllowedEnable;  
  RETURN;  
 END  
  
 /*============ Check values for Stock section (client side only) ============*/  
 IF @ClientID > 0  
 BEGIN  
  -- Reverse Logistics  
  IF EXISTS (SELECT TOP 1 1 FROM [BorisGlobalTraderImports].dbo.tbReverseLogistic o WITH (NOLOCK)  
        JOIN tbClient cl WITH (NOLOCK) ON o.ClientNo = cl.ClientId  
        WHERE (    
             (o.ClientNo = @ClientId)    
             OR (    
                    o.ClientNo <> @ClientId    
                    AND (case    
                             when o.ClientNo = 114 then    
                                 cast(1 as bit)    
                             else    
                                 cl.OwnDataVisibleToOthers    
                         end    
                        ) = 1    
                )    
         )    
         AND o.FullPart = @PartNo    
         AND ISNULL(o.InActive, 0) = 0    
         AND o.Quantity > 0  
  )  
  BEGIN  
   SELECT CAST(1 AS BIT) AS IsAllowedEnable;  
   RETURN;  
  END  
  
  -- Strategic Stock  
  IF EXISTS (SELECT TOP 1 1 FROM [BorisGlobalTraderImports].dbo.tbEpo o WITH (NOLOCK)  
        JOIN tbClient cl WITH (NOLOCK) ON o.ClientNo = cl.ClientId  
        WHERE (    
             (o.ClientNo = @ClientId)    
             OR (    
                    o.ClientNo <> @ClientId    
                    AND (case    
                             when o.ClientNo = 114 then    
                                 cast(1 as bit)    
                             else    
                                 cl.OwnDataVisibleToOthers    
                         end    
                        ) = 1    
                )    
         )    
         AND o.FullPart = @PartNo    
  )  
  BEGIN  
   SELECT CAST(1 AS BIT) AS IsAllowedEnable;  
   RETURN;  
  END  
  
  -- 20 most recent stock for selected part  
  IF EXISTS (SELECT TOP 1 1 FROM dbo.tbStock s WITH (NOLOCK)  
       WHERE (s.FullPart = @PartNo OR s.FullSupplierPart = @PartNo)    
        AND (s.QuantityInStock > 0 OR s.QuantityOnOrder > 0)      
  )  
  BEGIN  
   SELECT CAST(1 AS BIT) AS IsAllowedEnable;  
   RETURN;  
  END  
 END  
  
 --Else set KUB is disable  
 SELECT CAST(0 AS BIT) AS IsAllowedEnable;  
END            
GO


