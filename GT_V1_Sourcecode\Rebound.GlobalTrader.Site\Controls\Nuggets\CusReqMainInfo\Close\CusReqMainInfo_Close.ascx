<%@ Control Language="C#" CodeBehind="CusReqMainInfo_Close.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Forms.CusReqMainInfo_Close" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>

<ReboundUI_Form:DesignBase ID="ctlDB" runat="server" ShowRequiredFieldsExplanation="false" AllowQuickHelp="false">
	<Explanation><%=Functions.GetGlobalResource("FormExplanations", "CusReqMainInfo_Close")%></Explanation>
	
	<Content>
	
		<ReboundUI_Table:Form id="frm" runat="server">



			
			<ReboundUI_Form:FormField id="ctlReason" runat="server" FieldID="ddlReason" ResourceTitle="Reason">
				<Field><ReboundDropDown:Reason ID="ddlReason" runat="server" /></Field>
			</ReboundUI_Form:FormField>

			<%--<ReboundUI_Form:FormField id="ctlIncludeAlternates" runat="server" FieldID="chkIncludeAlternates" ResourceTitle="CloseAllAlternates">
				<Field><ReboundUI:ImageCheckBox ID="chkIncludeAlternates" runat="server" Enabled="true" /></Field>
			</ReboundUI_Form:FormField>--%>
			
			<ReboundUI_Form:FormField id="ctlConfirm" runat="server" FieldID="ctlConfirmation" ResourceTitle="Confirm">
				<Field><ReboundUI:Confirmation ID="ctlConfirmation" runat="server" /></Field>
			</ReboundUI_Form:FormField>
			
		</ReboundUI_Table:Form>
		
	</Content>
	
</ReboundUI_Form:DesignBase>
