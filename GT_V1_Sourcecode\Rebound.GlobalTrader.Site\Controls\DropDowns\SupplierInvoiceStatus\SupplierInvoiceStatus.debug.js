///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
//Marker     Changed by      Date               Remarks
//[001]      Vinay           25/06/2013         CR:- Supplier Invoice
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");

Rebound.GlobalTrader.Site.Controls.DropDowns.SupplierInvoiceStatus = function(element) { 
	Rebound.GlobalTrader.Site.Controls.DropDowns.SupplierInvoiceStatus.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.DropDowns.SupplierInvoiceStatus.prototype = {

	initialize: function() 	{
		Rebound.GlobalTrader.Site.Controls.DropDowns.SupplierInvoiceStatus.callBaseMethod(this, "initialize");
		this.addSetupDataCall(Function.createDelegate(this, this.setupDataCall));
		this.addGetDataOK(Function.createDelegate(this, this.dataCallOK));
	},
	
	dispose: function() { 
		if (this.isDisposed) return;
		Rebound.GlobalTrader.Site.Controls.DropDowns.SupplierInvoiceStatus.callBaseMethod(this, "dispose");
	},
	
	setupDataCall: function() { 
		this._objData.set_PathToData("controls/DropDowns/SupplierInvoiceStatus");
		this._objData.set_DataObject("SupplierInvoiceStatus");
		this._objData.set_DataAction("GetData");
	},
	
	dataCallOK: function() { 
		var result = this._objData._result;
		if (result.Items) {
		    for (var i = 0; i < result.Items.length; i++) {
		        this.addOption(result.Items[i].Name, result.Items[i].ID);
			}
		}
	}
	
};

Rebound.GlobalTrader.Site.Controls.DropDowns.SupplierInvoiceStatus.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.SupplierInvoiceStatus", Rebound.GlobalTrader.Site.Controls.DropDowns.Base);
