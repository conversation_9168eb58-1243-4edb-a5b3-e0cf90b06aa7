using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;
using System.Linq;

namespace Rebound.GlobalTrader.Site.Controls.Nuggets.Data {
	[WebService(Namespace = "http://tempuri.org/")]
	[WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
	public class CRMAReceivingLines : Rebound.GlobalTrader.Site.Data.Base {

		public override void ProcessRequest(HttpContext context) {
			if (base.init(context)) {
				switch (Action) {
					case "GetLines": GetLines(); break;
					case "GetInvoiceLineAllocationCandidates": GetInvoiceLineAllocationCandidates(); break;
					case "GetLine": GetLine(); break;
					case "ReceiveLine": ReceiveLine(); break;
                    case "AttachSerialNo": AttachSerialNo(); break;
                    case "GetAttachedSerial": GetAttachedSerial(); break;
                    case "DeleteAttachedSerial": DeleteAttachedSerial(); break;
                    case "AttachSerialByCRMA": AttachSerialByCRMA(); break;
                    case "DeattachCRMASerial": DeattachCRMASerial(); break;
                        
					default: WriteErrorActionNotFound(); break;
				}
			}
		}

		/// <summary>
		/// get all customerRMALines for specified customerRMA
		/// </summary>
		private void GetLines() {
			try {
				List<CustomerRmaLine> lst = CustomerRmaLine.GetListForReceiving(ID);
                var listMfrNotes = Manufacturer.GetAdvisoryNotes(lst.Select(x => x.ManufacturerNo).ToList(), (int)SessionManager.ClientID);
				
                JsonObject jsn = new JsonObject();
				JsonObject jsnItems = new JsonObject(true);
                foreach (CustomerRmaLine ln in lst) {
                    string mfrNotes = !Functions.HasNumbericValue(ln.ManufacturerNo) ? "" : listMfrNotes.Find(x => x.ManufacturerId == (int)ln.ManufacturerNo).AdvisoryNotes;
                    JsonObject jsnItem = new JsonObject();
					jsnItem.AddVariable("ID", ln.CustomerRMALineId);
					jsnItem.AddVariable("ILAID", ln.InvoiceLineAllocationId);
					jsnItem.AddVariable("Part", ln.Part);
					jsnItem.AddVariable("ManufacturerNo", ln.ManufacturerNo);
					jsnItem.AddVariable("Manufacturer", ln.ManufacturerCode);
                    jsnItem.AddVariable("MfrAdvisoryNotes", Functions.ReplaceLineBreaks(mfrNotes));
                    //jsnItem.AddVariable("Product", ln.ProductName);
                    jsnItem.AddVariable("Product", ln.ProductDescription);
                    jsnItem.AddVariable("Package", ln.PackageName);
					jsnItem.AddVariable("DC", ln.DateCode);
					jsnItem.AddVariable("Quantity", Functions.FormatNumeric(ln.Quantity));
					jsnItem.AddVariable("Received", Functions.FormatNumeric(ln.QuantityReceived));
					jsnItem.AddVariable("Outstanding", Functions.FormatNumeric(ln.Quantity - ln.QuantityReceived));
					jsnItem.AddVariable("ReturnDate", Functions.FormatDate(ln.ReturnDate));
					jsnItem.AddVariable("CustomerPart", ln.CustomerPart);
					jsnItem.AddVariable("ROHS", ln.ROHS);
					jsnItem.AddVariable("Reason", Functions.ReplaceLineBreaks(ln.Reason));
                    jsnItem.AddVariable("InvoiceLineNo", ln.InvoiceLineNo);
					jsnItems.AddVariable(jsnItem);
					jsnItem.Dispose(); jsnItem = null;
				}
				jsn.AddVariable("Lines", jsnItems);
				OutputResult(jsn);
				jsn.Dispose(); jsn = null;
			} catch (Exception e) {
				WriteError(e);
			}
		}

		public void GetInvoiceLineAllocationCandidates() {
			try {
				List<InvoiceLineAllocation> lst = InvoiceLineAllocation.GetListCandidatesForCustomerRMA(ID);
				JsonObject jsn = new JsonObject();
				JsonObject jsnItems = new JsonObject(true);
				foreach (InvoiceLineAllocation ln in lst) {
					JsonObject jsnItem = new JsonObject();
					jsnItem.AddVariable("Cost", Functions.FormatCurrency(ln.LandedCost, ln.CurrencyCode));
					jsnItem.AddVariable("Qty", Functions.FormatNumeric(ln.Quantity));
					jsnItem.AddVariable("LineID", ln.InvoiceLineNo);
					jsnItem.AddVariable("ID", ln.InvoiceLineAllocationId);
					jsnItem.AddVariable("Part", ln.Part);
					jsnItem.AddVariable("Product", ln.ProductDescription);
					jsnItem.AddVariable("Package", ln.PackageDescription);
					jsnItem.AddVariable("Mfr", ln.ManufacturerCode);
					jsnItem.AddVariable("DC", ln.DateCode);
					jsnItem.AddVariable("Price", Functions.FormatCurrency(ln.Price, ln.CurrencyCode));
					jsnItem.AddVariable("Location", ln.Location);
					jsnItem.AddVariable("Lot", ln.LotName);
					jsnItem.AddVariable("InvoiceDate", Functions.FormatDate(ln.InvoiceDate));
					jsnItems.AddVariable(jsnItem);
					jsnItem.Dispose();
					jsnItem = null;
				}
				jsn.AddVariable("LineAllocations", jsnItems);
				jsn.AddVariable("Count", lst.Count);
				OutputResult(jsn);
				jsn.Dispose(); jsn = null;
			} catch (Exception e) {
				WriteError(e);
			}
		}

		private void GetLine() {
			CustomerRmaLine ln = null;
			try {
				ln = CustomerRmaLine.GetForReceiving(ID);
				if (ln == null) {
					WriteErrorDataNotFound();
				} else {
					JsonObject jsn = new JsonObject();
					jsn.AddVariable("CRMANo", ln.CustomerRMANo);
					jsn.AddVariable("InvoiceLineNo", ln.InvoiceLineNo);
					jsn.AddVariable("Part", ln.Part);
					jsn.AddVariable("ManufacturerNo", ln.ManufacturerNo);
					jsn.AddVariable("Manufacturer", ln.ManufacturerName);
					jsn.AddVariable("Package", ln.PackageDescription);
					jsn.AddVariable("Quantity", Functions.FormatNumeric(ln.Quantity));
					jsn.AddVariable("QuantityAllocated", Functions.FormatNumeric(ln.QuantityAllocated));
					jsn.AddVariable("Received", Functions.FormatNumeric(ln.QuantityReceived));
					jsn.AddVariable("ReturnDate", Functions.FormatDate(ln.ReturnDate));
					jsn.AddVariable("Reason", Functions.ReplaceLineBreaks(ln.Reason));
					jsn.AddVariable("ProductNo", ln.ProductNo);
					jsn.AddVariable("ProductName", ln.ProductName);
					jsn.AddVariable("Product", ln.ProductDescription);
					if (ln.CurrencyNo != GetFormValue_Int("GICurrencyNo")) ln.Price = BLL.Currency.ConvertValueBetweenTwoCurrencies(ln.Price, ln.CurrencyNo, GetFormValue_Int("GICurrencyNo"), GetFormValue_DateTime("GIDate"));
					jsn.AddVariable("Price", Functions.FormatCurrency(ln.Price, GetFormValue_String("GICurrencyCode")));
					jsn.AddVariable("PriceRaw", ln.Price);
					jsn.AddVariable("PackageNo", ln.PackageNo);
					jsn.AddVariable("PackageName", ln.PackageName);
					jsn.AddVariable("Package", ln.PackageDescription);
					jsn.AddVariable("CustomerPart", ln.CustomerPart);
					jsn.AddVariable("DC", ln.DateCode);
					jsn.AddVariable("ROHS", ln.ROHS);
					jsn.AddVariable("LineNotes", Functions.ReplaceLineBreaks(ln.LineNotes));
					OutputResult(jsn);
					jsn.Dispose();
					jsn = null;
				}
			} catch (Exception e) {
				WriteError(e);
			} finally {
				ln = null;
			}
		}

		/// <summary>
		/// Add new goodsInLine for CRMALine
		/// </summary>
		public void ReceiveLine() {
			bool blnOK = false;
            string strMessage = "";
            int intNewGoodsInLineID = 0;
			try {
                intNewGoodsInLineID = GoodsInLine.Insert(
                    ID
                    , null
                    , GetFormValue_String("Part")
                    , GetFormValue_NullableInt("ManufacturerNo")
                    , GetFormValue_String("DC")
                    , GetFormValue_NullableInt("PackageNo")
                    , GetFormValue_Int("Quantity")
                    , GetFormValue_Double("Price")
                    , GetFormValue_Double("ShipInCost")
                    , GetFormValue_String("QCNotes")
                    , GetFormValue_String("Location")
                    , null
                    , GetFormValue_NullableInt("ProductNo")
                    , GetFormValue_Int("CurrencyNo")
                    , GetFormValue_Int("CRMALineNo")
                    , GetFormValue_String("SupplierPart")
                    , GetFormValue_Byte("ROHS")
                    , null
                    , GetFormValue_Boolean("Unavailable")
                    , null
                    , ""
                    , GetFormValue_Int("CountingMethodNo")
                    , GetFormValue_Boolean("SerialNosRecorded")
                    , GetFormValue_String("PartMarkings")
                    , LoginID
                    , GetFormValue_Double("Price") // Later we need to do on CRMA 
                    , false
                     , out strMessage
                    );

				if (intNewGoodsInLineID > 0) {
					InvoiceLineAllocation ila = InvoiceLineAllocation.Get(GetFormValue_Int("InvoiceLineAllocationID"));
					if (ila != null) {
						ila.CustomerRMAGoodsInLineNo = intNewGoodsInLineID;
						blnOK = ila.Update();
					}
				}
				JsonObject jsn = new JsonObject();
				jsn.AddVariable("Result", blnOK);
				jsn.AddVariable("NewID", intNewGoodsInLineID);
                jsn.AddVariable("Message", strMessage);
                OutputResult(jsn);
				jsn.Dispose();
				jsn = null;
			} catch (Exception e) {
				WriteError(e);
			}
		}
        public void AttachSerialNo()
        {
            try
            {
                bool blnOK = false;
                blnOK = CustomerRmaLine.AttachSerialNo(
                     GetFormValue_String("strSerialNo")
                   , GetFormValue_Int("CustomerRMANo")
                    , GetFormValue_Int("CustomerRMALineNo")
                    , LoginID
                    );

                // blnOK = (intNewSerialNoID > 0);
                JsonObject jsn = new JsonObject();
                jsn.AddVariable("Result", blnOK);
                //jsn.AddVariable("NewID", intNewSerialNoID);
                OutputResult(jsn);
                jsn.Dispose();
                jsn = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }
        private void GetAttachedSerial()
        {
            List<BLL.GoodsInLine> lst = null;
            try
            {

                lst = GoodsInLine.GetAttachedSerial(
                    GetFormValue_NullableInt("Order", 0),
                    GetFormValue_NullableInt("SortDir", 1),
                    GetFormValue_NullableInt("PageIndex", 0),
                    GetFormValue_NullableInt("PageSize", 10),
                    GetFormValue_NullableInt("CustomerRMANo"),
                    GetFormValue_NullableInt("CustomerRMALineNo")
                    );
                JsonObject jsn = new JsonObject();
                JsonObject jsnItems = new JsonObject(true);
                for (int i = 0; i < lst.Count; i++)
                {
                    JsonObject jsnItem = new JsonObject();
                    jsnItem.AddVariable("ID", lst[i].SerialNoId);
                    jsnItem.AddVariable("SerialNo", lst[i].SerialNo);
                    jsnItem.AddVariable("Group", lst[i].SubGroup);
                    jsnItem.AddVariable("InvoiceLineNo", lst[i].InvoiceLineNo);
                    jsnItems.AddVariable(jsnItem);
                    jsnItem.Dispose();
                    jsnItem = null;
                }
                jsn.AddVariable("Count", (lst.Count > 0) ? lst[0].RowCnt : 0);
                jsn.AddVariable("Results", jsnItems);
                OutputResult(jsn);
                jsnItems.Dispose();
                jsnItems = null;
                jsn.Dispose();
                jsn = null;
            }
            catch (Exception ex)
            {
                WriteError(ex);
            }
            finally
            {
                lst = null;
            }
        }
        public void DeleteAttachedSerial()
        {
            try
            {
                bool blnOK = false;
                int intNewSerialNoID = GoodsInLine.DeleteAttachedSerial(
                    GetFormValue_NullableInt("SerialId")
                    , GetFormValue_NullableInt("CustomerRMANo")
                    , GetFormValue_NullableInt("CustomerRMALineNo")
                    , LoginID
                    );

                blnOK = (intNewSerialNoID > 0);
                JsonObject jsn = new JsonObject();
                jsn.AddVariable("Result", blnOK);
                jsn.AddVariable("NewID", intNewSerialNoID);
                OutputResult(jsn);
                jsn.Dispose();
                jsn = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }
        public void DeattachCRMASerial()
        {
            try
            {
                bool blnOK = false;
                int intNewSerialNoID = GoodsInLine.DeattachCRMASerial(GetFormValue_NullableInt("InvoiceLineNo")
                     );

                blnOK = (intNewSerialNoID > 0);
                JsonObject jsn = new JsonObject();
                jsn.AddVariable("Result", blnOK);
                jsn.AddVariable("NewID", intNewSerialNoID);
                OutputResult(jsn);
                jsn.Dispose();
                jsn = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }
        public void AttachSerialByCRMA()
        {
            try
            {
                String ValidateMessage = "";
                bool blnOK = false;
                int intNewSerialNoID = GoodsInLine.AttachSerialByCRMA(
                   GetFormValue_String("SubGroup")
                    , GetFormValue_NullableInt("InvoiceLineNo")
                    , GetFormValue_NullableInt("CustomerRMANo")
                    , GetFormValue_NullableInt("CustomerRMALineNo")
                    , out ValidateMessage
                    );

                blnOK = (intNewSerialNoID > 0);
                JsonObject jsn = new JsonObject();
                jsn.AddVariable("Result", blnOK);
                jsn.AddVariable("NewID", intNewSerialNoID);
                jsn.AddVariable("ValidateMessage", ValidateMessage);
                OutputResult(jsn);
                jsn.Dispose();
                jsn = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }

	}
}