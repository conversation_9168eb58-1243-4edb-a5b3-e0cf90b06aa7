//-----------------------------------------------------------------------------------------
// RP 17.12.2009:
// - add SearchCompanyName
//Marker     Changed by      Date         Remarks
//[001]      Vinay           20/07/2012   Rebound- Invoice bulk Emailer
//[002]      Vinay           22/08/2012   ESMS Ref:54 - If SO line created from Quote line then create hyperlink from sales order to quote
//[003]      Vinay           28/08/2012   Add purchase order link in srma lines
//[004]      Vinay           31/08/2012   Add sales order link in purchase order allocation lines
//[005]      Vinay           10/06/2013   Add Supplier Invoice Detail links
//[006]      Vinay           28/08/2013   Print NPR
//[007]      A<PERSON><PERSON> Singh     21-Aug-2018  REB-12084:Lock PO lines when EPR is authorised
//[008]      A<PERSON><PERSON>     29-Nov-2018  Show customer requirement all info in tree view. 
//[008]      <PERSON><PERSON>   21-Dec-2018  Adding ClientBomId for Customer Requirement Import
//[009]      <PERSON><PERSON><PERSON> & <PERSON><PERSON>   09-July-2021  Adding ShortShipmentID for Short Shipment Notification
//[010]      Bhooma & Sunil Kumar   13-July-2021  Adding ShortShipmentStage for Short Shipment Notification
//-----------------------------------------------------------------------------------------
using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections;
using System.Text;
using System.Collections.Specialized;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;
using Rebound.GlobalTrader.Site;

/// <summary>
/// QueryString Manager Class
/// </summary>
namespace Rebound.GlobalTrader.Site {
	public class QueryStringManager {

		#region Properties

		protected int _intTab = -1;
		public int Tab {
			get { return _intTab; }
			set { _intTab = value; }
		}

		protected int _intCompanyID = -1;
		public int CompanyID {
			get { return _intCompanyID; }
			set { _intCompanyID = value; }
		}

		private string _strCompanyName;
		public string CompanyName {
			get { return _strCompanyName; }
			set { _strCompanyName = value; }
		}

		protected int _intContactID = -1;
		public int ContactID {
			get { return _intContactID; }
			set { _intContactID = value; }
		}

		private string _strContactName;
		public string ContactName {
			get { return _strContactName; }
			set { _strContactName = value; }
		}

		private string _strReturnURL;
		public string ReturnURL {
			get { return _strReturnURL; }
			set { _strReturnURL = value; }
		}

		private int _intLoginID;
		public int LoginID {
			get { return _intLoginID; }
			set { _intLoginID = value; }
		}

		private int _intGenericID;
		public int GenericID {
			get { return _intGenericID; }
			set { _intGenericID = value; }
		}

		private bool _blnEmailMode = false;
		public bool EmailMode {
			get { return _blnEmailMode; }
			set { _blnEmailMode = value; }
		}

		protected int _intStockListType = -1;
		public int StockListType {
			get { return _intStockListType; }
			set { _intStockListType = value; }
		}

		protected int _intAddressID = -1;
		public int AddressID {
			get { return _intAddressID; }
			set { _intAddressID = value; }
		}

		private int _intCustomerRequirementID = -1;
		public int CustomerRequirementID {
			get { return _intCustomerRequirementID; }
			set { _intCustomerRequirementID = value; }
		}

		protected int _intSalesOrderID = -1;
		public int SalesOrderID {
			get { return _intSalesOrderID; }
			set { _intSalesOrderID = value; }
		}

		protected int _intPurchaseOrderID = -1;
		public int PurchaseOrderID {
			get { return _intPurchaseOrderID; }
			set { _intPurchaseOrderID = value; }
		}

        protected int _intInternalPurchaseOrderID = -1;
        public int InternalPurchaseOrderID
        {
            get { return _intInternalPurchaseOrderID; }
            set { _intInternalPurchaseOrderID = value; }
        }

		protected int _intPurchaseRequisitionID = -1;
		public int PurchaseRequisitionID {
			get { return _intPurchaseRequisitionID; }
			set { _intPurchaseRequisitionID = value; }
		}

		protected int _intQuoteID = -1;
		public int QuoteID {
			get { return _intQuoteID; }
			set { _intQuoteID = value; }
		}

		protected int _intInvoiceID = -1;
		public int InvoiceID {
			get { return _intInvoiceID; }
			set { _intInvoiceID = value; }
		}

		protected int _intCRMAID = -1;
		public int CRMAID {
			get { return _intCRMAID; }
			set { _intCRMAID = value; }
		}

		protected int _intSRMAID = -1;
		public int SRMAID {
			get { return _intSRMAID; }
			set { _intSRMAID = value; }
		}

		protected int _intCreditID = -1;
		public int CreditID {
			get { return _intCreditID; }
			set { _intCreditID = value; }
		}

		protected int _intDebitID = -1;
		public int DebitID {
			get { return _intDebitID; }
			set { _intDebitID = value; }
		}

		private SystemDocument.ListForPrint _enmPrintObject;
		public SystemDocument.ListForPrint PrintObject {
			get { return _enmPrintObject; }
			set { _enmPrintObject = value; }
		}

		private string _strPartNo = "";
		public string PartNo {
			get { return _strPartNo; }
			set { _strPartNo = value; }
		}

		private string _strSearchPartNo = "";
		public string SearchPartNo {
			get { return _strSearchPartNo; }
			set { _strSearchPartNo = value; }
		}

		private Array _aryLineIDs;
		public Array LineIDs {
			get { return _aryLineIDs; }
			set { _aryLineIDs = value; }
		}

		private int _intManufacturerID;
		public int ManufacturerID {
			get { return _intManufacturerID; }
			set { _intManufacturerID = value; }
		}

        private int _intIHSPartID;
        public int IHSPartID
        {
            get { return _intIHSPartID; }
            set { _intIHSPartID = value; }
        }

		private int _intSOLineID;
		public int SOLineID
		{
			get { return _intSOLineID; }
			set { _intSOLineID = value; }
		}

		private int _intsaId;
        public int SAId
        {
            get { return _intsaId; }
            set { _intsaId = value; }
        }
        private string _strUploadType;
        public string UploadType
        {
            get { return _strUploadType; }
            set { _strUploadType = value; }
        }
        private int _EDitScreen;
        public int EDitScreen
        {
            get { return _EDitScreen; }
            set { _EDitScreen = value; }
        }
        private string _strManufacturerName;
		public string ManufacturerName {
			get { return _strManufacturerName; }
			set { _strManufacturerName = value; }
		}

		private int _intReportID;
		public int ReportID {
			get { return _intReportID; }
			set { _intReportID = value; }
		}

		private Array _aryReportParameters;
		public Array ReportParameters {
			get { return _aryReportParameters; }
			set { _aryReportParameters = value; }
		}

		private int _intMailMessageID;
		public int MailMessageID {
			get { return _intMailMessageID; }
			set { _intMailMessageID = value; }
		}

		private int _intToDoID;
		public int ToDoID {
			get { return _intToDoID; }
			set { _intToDoID = value; }
		}

		private string _strHelpPage;
		public string HelpPage {
			get { return _strHelpPage; }
			set { _strHelpPage = value; }
		}

		private int _intSecurityGroupID;
		public int SecurityGroupID {
			get { return _intSecurityGroupID; }
			set { _intSecurityGroupID = value; }
		}

		protected int _intCRMAListType = -1;
		public int CRMAListType {
			get { return _intCRMAListType; }
			set { _intCRMAListType = value; }
		}

		protected int _intSRMAListType = -1;
		public int SRMAListType {
			get { return _intSRMAListType; }
			set { _intSRMAListType = value; }
		}

		protected int _intPOListType = -1;
		public int POListType {
			get { return _intPOListType; }
			set { _intPOListType = value; }
		}

		protected int _intSOListType = -1;
		public int SOListType {
			get { return _intSOListType; }
			set { _intSOListType = value; }
		}

		protected int _intCompanyListType = -1;
		public int CompanyListType {
			get { return _intCompanyListType; }
			set { _intCompanyListType = value; }
		}

		private int _intGoodsInID;
		public int GoodsInID {
			get { return _intGoodsInID; }
			set { _intGoodsInID = value; }
		}
        private int _intLandedGiLineId;
        public int LandedGiLineId
        {
            get { return _intLandedGiLineId; }
            set { _intLandedGiLineId = value; }
        }
        private bool _bIsLandedQuery;
        public bool IsLandedQuery
        {
            get { return _bIsLandedQuery; }
            set { _bIsLandedQuery = value; }
        }
        private int _intStockID;
		public int StockID {
			get { return _intStockID; }
			set { _intStockID = value; }
		}

		private int _intLotID;
		public int LotID {
			get { return _intLotID; }
			set { _intLotID = value; }
		}

		private int _intServiceID;
		public int ServiceID {
			get { return _intServiceID; }
			set { _intServiceID = value; }
		}

		private string _strSearchCompanyName;
		public string SearchCompanyName {
			get { return _strSearchCompanyName; }
			set { _strSearchCompanyName = value; }
		}

		private bool _blnBypassSavedState = false;
		public bool BypassSavedState {
			get { return _blnBypassSavedState; }
			set { _blnBypassSavedState = value; }
		}
        //[001] code start
        private int _intInvoiceEmailStatusID;
        public int InvoiceEmailStatusID
        {
            get { return _intInvoiceEmailStatusID; }
            set { _intInvoiceEmailStatusID = value; }
        }
        //[001] code end

        //[002] code start
        private int _intQuoteLineID;
        public int QuoteLineID
        {
            get { return _intQuoteLineID; }
            set { _intQuoteLineID = value; }
        }
        //[002] code end
        //[003] code start
        protected int _intPurchaseOrderLineID = -1;
        public int PurchaseOrderLineID
        {
            get { return _intPurchaseOrderLineID; }
            set { _intPurchaseOrderLineID = value; }
        }

        protected int _intInternalPurchaseOrderLineID = -1;
        public int InternalPurchaseOrderLineID
        {
            get { return _intInternalPurchaseOrderLineID; }
            set { _intInternalPurchaseOrderLineID = value; }
        }
        //[003] code end
        //[004] code start
        protected int _intSalesOrderLineID = -1;
        public int SalesOrderLineID
        {
            get { return _intSalesOrderLineID; }
            set { _intSalesOrderLineID = value; }
        }
        //[004] code end

        //[005] code start
        protected int _intSupplierInvoiceID = -1;
        public int SupplierInvoiceID
        {
            get { return _intSupplierInvoiceID; }
            set { _intSupplierInvoiceID = value; }
        }
        //[005] code end

        //[006] code start
        protected int _intNPRID = -1;
        public int NPRID
        {
            get { return _intNPRID; }
            set { _intNPRID = value; }
        }
		//[006] code end
		protected long _STOId=-1;
		public long STOId
		{
			get { return _STOId; }
			set { _STOId = value; }
		}
		//[007] code start
		protected int _intEPRId = -1;
        public int EPRId
        {
            get { return _intEPRId; }
            set { _intEPRId = value; }
        }
        //[007] code end _intSalesPersonID

        protected int _intSalesPersonID = -1;
        public int SalesPersonID
        {
            get { return _intSalesPersonID; }
            set { _intSalesPersonID = value; }
        }


        protected int _intBuyerID = -1;
        public int BuyerID
        {
            get { return _intBuyerID; }
            set { _intBuyerID = value; }
        }

        protected int _intPOQuoteID = -1;
        public int POQuoteID
        {
            get { return _intPOQuoteID; }
            set { _intPOQuoteID = value; }
        }

        //[007] start
        protected string _strPOLineIds = string.Empty;
        public string POLineIds
        {
            get { return _strPOLineIds; }
            set { _strPOLineIds = value; }
        }
        protected string _strPOLineSerialNo = string.Empty;
        public string POLineSerialNo
        {
            get { return _strPOLineSerialNo; }
            set { _strPOLineSerialNo = value; }
        }
        //[007] end
		#endregion

		public QueryStringManager() { }
		public QueryStringManager(NameValueCollection objQS) {
			try
			{
				ProcessQuerystring(objQS);
			}
			catch (Exception ex)
			{ 
			string log =ex.Message;
			}
		}
        private int _intBOMID;
        public int BOMID
        {
            get { return _intBOMID; }
            set { _intBOMID = value; }
        }

        //[0010] code start
        protected int _intClientInvoiceID = -1;
        public int ClientInvoiceID
        {
            get { return _intClientInvoiceID; }
            set { _intClientInvoiceID = value; }
        }
        // code End
        //[008] start
        private string _DocNo;
        public string DocNo
        {
            get { return _DocNo; }
            set { _DocNo = value; }
        }
        private string _actionType;
        public string ActionType
        {
            get { return _actionType; }
            set { _actionType = value; }
        }
        private string _DocId;
        public string DocId
        {
            get { return _DocId; }
            set { _DocId = value; }
        }
        //[008] end

        //start [008]
        private int _intClientBOMID;
        public int ClientBOMID
        {
            get { return _intClientBOMID; }
            set { _intClientBOMID = value; }
        }
        //end [008]

        private int _intSourcingResultID;
        public int SourcingResultID
        {
            get { return _intSourcingResultID; }
            set { _intSourcingResultID = value; }
        }

        protected int _intShortShipmentID = -1;
        public int ShortShipmentID
        {
            get { return _intShortShipmentID; }
            set { _intShortShipmentID = value; }
        }


        protected int _intShortShipmentStage = -1;
        public int ShortShipmentStage
        {
            get { return _intShortShipmentStage; }
            set { _intShortShipmentStage = value; }
        }
        protected int _intGILineId = -1;
        public int GILineId
        {
            get { return _intGILineId; }
            set { _intGILineId = value; }
        }
		protected int _intCreditLimitId = -1;
		public int CreditLimitId
		{
			get { return _intCreditLimitId; }
			set { _intCreditLimitId = value; }
		}

		protected int _intCategory = -1;
		public int Category
		{
			get { return _intCategory; }
			set { _intCategory = value; }
		}
		protected int _intQuoteNumber = -1;
		public int QuoteNumber
		{
			get { return _intQuoteNumber; }
			set { _intQuoteNumber = value; }
		}
		public class QueryStringVariables {
			//Try to max 4 letters
			//Only ever refer to querystring variables using these objects
			//When adding a new one, don't forget to add it to the properties list above and 
			//_lstVariables and ProcessQuerystring() below
			public static readonly QueryStringVariable Tab = new QueryStringVariable(1, "Tab", "tab");
			public static readonly QueryStringVariable CompanyID = new QueryStringVariable(2, "CompanyID", "cm");
			public static readonly QueryStringVariable CompanyName = new QueryStringVariable(3, "CompanyName", "cmn");
			public static readonly QueryStringVariable ContactID = new QueryStringVariable(4, "ContactID", "con");
			public static readonly QueryStringVariable ContactName = new QueryStringVariable(5, "ContactName", "ctn");
			public static readonly QueryStringVariable ReturnURL = new QueryStringVariable(6, "ReturnURL", "ret");
			public static readonly QueryStringVariable LoginID = new QueryStringVariable(7, "LoginID", "lg");
			public static readonly QueryStringVariable GenericID = new QueryStringVariable(8, "GenericID", "id");
			public static readonly QueryStringVariable EmailMode = new QueryStringVariable(9, "EmailMode", "eml");
			public static readonly QueryStringVariable CompanyListType = new QueryStringVariable(1, "CompanyListType", "clt");
			public static readonly QueryStringVariable CreditID = new QueryStringVariable(4, "CreditID", "crd");
			public static readonly QueryStringVariable CRMAID = new QueryStringVariable(5, "CRMAID", "crma");
			public static readonly QueryStringVariable CRMAListType = new QueryStringVariable(6, "CRMAListType", "crlt");
			public static readonly QueryStringVariable CustomerRequirementID = new QueryStringVariable(7, "CustomerRequirementID", "req");
			public static readonly QueryStringVariable DebitID = new QueryStringVariable(8, "DebitID", "deb");
			public static readonly QueryStringVariable GoodsInID = new QueryStringVariable(10, "GoodsInID", "gi");
			public static readonly QueryStringVariable HelpPage = new QueryStringVariable(11, "HelpPage", "hpg");
			public static readonly QueryStringVariable InvoiceID = new QueryStringVariable(12, "InvoiceID", "inv");
			public static readonly QueryStringVariable LineIDs = new QueryStringVariable(13, "LineIDs", "lns");
			public static readonly QueryStringVariable LotID = new QueryStringVariable(14, "LotID", "lot");
			public static readonly QueryStringVariable MailMessageID = new QueryStringVariable(15, "MailMessageID", "mm");
			public static readonly QueryStringVariable ManufacturerID = new QueryStringVariable(16, "ManufacturerID", "mfr");
			public static readonly QueryStringVariable ManufacturerName = new QueryStringVariable(17, "ManufacturerName", "mfn");
			public static readonly QueryStringVariable PurchaseOrderID = new QueryStringVariable(18, "PurchaseOrderID", "po");
			public static readonly QueryStringVariable PartNo = new QueryStringVariable(19, "PartNo", "pn");
			public static readonly QueryStringVariable PrintObject = new QueryStringVariable(20, "PrintObject", "pro");
			public static readonly QueryStringVariable POListType = new QueryStringVariable(21, "POListType", "polt");
			public static readonly QueryStringVariable PurchaseRequisitionID = new QueryStringVariable(22, "PurchaseRequisitionID", "prq");
			public static readonly QueryStringVariable QuoteID = new QueryStringVariable(23, "QuoteID", "qt");
			public static readonly QueryStringVariable ReportID = new QueryStringVariable(24, "ReportID", "rpt");
			public static readonly QueryStringVariable ReportParameters = new QueryStringVariable(25, "ReportParameters", "rpms");
			public static readonly QueryStringVariable SecurityGroupID = new QueryStringVariable(27, "SecurityGroupID", "sgp");
			public static readonly QueryStringVariable StockListType = new QueryStringVariable(28, "StockListType", "slt");
			public static readonly QueryStringVariable SalesOrderID = new QueryStringVariable(29, "SalesOrderID", "so");
			public static readonly QueryStringVariable SearchPartNo = new QueryStringVariable(30, "SearchPartNo", "spn");
			public static readonly QueryStringVariable ServiceID = new QueryStringVariable(31, "ServiceID", "srv");
			public static readonly QueryStringVariable SOListType = new QueryStringVariable(32, "SOListType", "solt");
			public static readonly QueryStringVariable SRMAID = new QueryStringVariable(33, "SRMAID", "srma");
			public static readonly QueryStringVariable SRMAListType = new QueryStringVariable(34, "SRMAListType", "srlt");
			public static readonly QueryStringVariable StockID = new QueryStringVariable(35, "StockID", "stk");
			public static readonly QueryStringVariable ToDoID = new QueryStringVariable(36, "ToDoID", "stk");
			public static readonly QueryStringVariable SearchCompanyName = new QueryStringVariable(37, "SearchCompanyName", "scn");
			public static readonly QueryStringVariable BypassSavedState = new QueryStringVariable(38, "BypassSavedState", "bss");
            //[001] code start
            public static readonly QueryStringVariable InvoiceEmailStatusID = new QueryStringVariable(39, "InvoiceEmailStatusID", "ies");
            //[001] code end
            //[002] code start
            public static readonly QueryStringVariable QuoteLineID = new QueryStringVariable(40, "QuoteLineID", "qtl");
            //[002] code end
            //[003] code start
            public static readonly QueryStringVariable PurchaseOrderLineID = new QueryStringVariable(41, "PurchaseOrderLineID", "pol");
            public static readonly QueryStringVariable InternalPurchaseOrderLineID = new QueryStringVariable(64645, "InternalPurchaseOrderLineID", "ipol");
            //[003] code end
            //[004] code start
            public static readonly QueryStringVariable SalesOrderLineID = new QueryStringVariable(42, "SalesOrderLineID", "sol");
            //[004] code end

            //[005] code start
            public static readonly QueryStringVariable SupplierInvoiceID = new QueryStringVariable(43, "SupplierInvoiceID", "si");
            //[005] code end

            //[006] code start
            public static readonly QueryStringVariable NPRID = new QueryStringVariable(44, "NPRID", "npr");
            //[006] code end
            //[007] code start
            public static readonly QueryStringVariable EPRId = new QueryStringVariable(45, "EPRId", "epr");
			
			//[007] code end
			public static readonly QueryStringVariable SalesPersonID = new QueryStringVariable(46, "SalesPersonID", "sp");

            public static readonly QueryStringVariable BuyerID = new QueryStringVariable(46, "BuyerID", "buy");
            public static readonly QueryStringVariable BOMID = new QueryStringVariable(47, "BOMID", "BOM");
            public static readonly QueryStringVariable POQuoteID = new QueryStringVariable(48, "POQuoteID", "pqt");
            public static readonly QueryStringVariable InternalPurchaseOrderID = new QueryStringVariable(18, "InternalPurchaseOrderID", "ipo");
            //[010] code start
            public static readonly QueryStringVariable ClientInvoiceID = new QueryStringVariable(49, "ClientInvoiceID", "ci");
            //[005] code end
            //[007] start
            public static readonly QueryStringVariable POLineSerialNo = new QueryStringVariable(50, "POLineSerialNo", "pols");
            public static readonly QueryStringVariable POLineIds = new QueryStringVariable(50, "POLineIds", "polids");
            //[007] end
            //[008] start
            public static readonly QueryStringVariable DocNo = new QueryStringVariable(51, "DocNo", "DocNo");
            public static readonly QueryStringVariable ActionType = new QueryStringVariable(52, "ActionType", "ActionType");
            //[008] end

            //[008] start
            public static readonly QueryStringVariable ClientBOMID = new QueryStringVariable(47, "ClientBOMID", "ClientBOMID");
            //[008] end
            public static readonly QueryStringVariable SourcingResultID = new QueryStringVariable(53, "SourcingResultID", "sor");
            //[008] start
            public static readonly QueryStringVariable DocId = new QueryStringVariable(54, "DocId", "DocId");
            public static readonly QueryStringVariable IHSPartID = new QueryStringVariable(55, "IHSPartID", "ihs");
            public static readonly QueryStringVariable ShortShipmentID = new QueryStringVariable(56, "ShortShipmentID", "ssi");
            public static readonly QueryStringVariable ShortShipmentStage = new QueryStringVariable(57, "ShortShipmentStage", "stg");
            public static readonly QueryStringVariable UploadType = new QueryStringVariable(56, "UploadType", "upldTyp");
            public static readonly QueryStringVariable SAId= new QueryStringVariable(57, "SAId", "saID");
            public static readonly QueryStringVariable GILineId = new QueryStringVariable(56, "GILineId", "gilId");
            public static readonly QueryStringVariable EDitScreen = new QueryStringVariable(58, "EDitScreen", "editScreen");
            public static readonly QueryStringVariable LandedGiLineId = new QueryStringVariable(59, "LandedGiLineId", "landedgilineid");
            public static readonly QueryStringVariable IsLandedQuery = new QueryStringVariable(60, "IsLandedQuery", "islandedquery");
			public static readonly QueryStringVariable SOLineID = new QueryStringVariable(61, "SOLineID", "SOLineID");
			//code start for [012] 
			public static readonly QueryStringVariable BMMID = new QueryStringVariable(63, "BMMID", "BOM");
			public static readonly QueryStringVariable CreditLimitId = new QueryStringVariable(64, "CreditLimitId", "CreditLimit");
			//code end for [012] 
			public static readonly QueryStringVariable Category = new QueryStringVariable(65, "Category", "Category");
			public static readonly QueryStringVariable QuoteNumber = new QueryStringVariable(66, "QuoteNumber", "qn");
		}



		private List<QueryStringVariable> _lstVariables;
		public List<QueryStringVariable> Variables {
			get {
				if (_lstVariables == null) {
					_lstVariables = new List<QueryStringVariable>();
					_lstVariables.Add(QueryStringVariables.Tab);
					_lstVariables.Add(QueryStringVariables.CompanyID);
					_lstVariables.Add(QueryStringVariables.CompanyName);
					_lstVariables.Add(QueryStringVariables.ContactID);
					_lstVariables.Add(QueryStringVariables.ContactName);
					_lstVariables.Add(QueryStringVariables.ReturnURL);
					_lstVariables.Add(QueryStringVariables.GenericID);
					_lstVariables.Add(QueryStringVariables.LoginID);
					_lstVariables.Add(QueryStringVariables.EmailMode);
					_lstVariables.Add(QueryStringVariables.CompanyListType);
					_lstVariables.Add(QueryStringVariables.CreditID);
					_lstVariables.Add(QueryStringVariables.CRMAID);
					_lstVariables.Add(QueryStringVariables.CRMAListType);
					_lstVariables.Add(QueryStringVariables.CustomerRequirementID);
					_lstVariables.Add(QueryStringVariables.DebitID);
					_lstVariables.Add(QueryStringVariables.GoodsInID);
					_lstVariables.Add(QueryStringVariables.HelpPage);
					_lstVariables.Add(QueryStringVariables.InvoiceID);
					_lstVariables.Add(QueryStringVariables.LineIDs);
					_lstVariables.Add(QueryStringVariables.LotID);
					_lstVariables.Add(QueryStringVariables.MailMessageID);
					_lstVariables.Add(QueryStringVariables.ManufacturerID);
					_lstVariables.Add(QueryStringVariables.ManufacturerName);
					_lstVariables.Add(QueryStringVariables.PurchaseOrderID);
                    _lstVariables.Add(QueryStringVariables.InternalPurchaseOrderID);
					_lstVariables.Add(QueryStringVariables.PartNo);
					_lstVariables.Add(QueryStringVariables.PrintObject);
					_lstVariables.Add(QueryStringVariables.POListType);
					_lstVariables.Add(QueryStringVariables.PurchaseRequisitionID);
					_lstVariables.Add(QueryStringVariables.QuoteID);
					_lstVariables.Add(QueryStringVariables.ReportID);
					_lstVariables.Add(QueryStringVariables.ReportParameters);
					_lstVariables.Add(QueryStringVariables.SecurityGroupID);
					_lstVariables.Add(QueryStringVariables.StockListType);
					_lstVariables.Add(QueryStringVariables.SalesOrderID);
					_lstVariables.Add(QueryStringVariables.SearchPartNo);
					_lstVariables.Add(QueryStringVariables.ServiceID);
					_lstVariables.Add(QueryStringVariables.SOListType);
					_lstVariables.Add(QueryStringVariables.SRMAID);
					_lstVariables.Add(QueryStringVariables.SRMAListType);
					_lstVariables.Add(QueryStringVariables.StockID);
					_lstVariables.Add(QueryStringVariables.ToDoID);
					_lstVariables.Add(QueryStringVariables.SearchCompanyName);
					_lstVariables.Add(QueryStringVariables.BypassSavedState);
                    //[001] code start
                    _lstVariables.Add(QueryStringVariables.InvoiceEmailStatusID);
                    //[001] code end
                    //[002] code start
                    _lstVariables.Add(QueryStringVariables.QuoteLineID);
                    //[002] code end
                    //[003] code start
                    _lstVariables.Add(QueryStringVariables.PurchaseOrderLineID);
                    _lstVariables.Add(QueryStringVariables.InternalPurchaseOrderLineID);
                    //[003] code end
                    //[004] code start
                    _lstVariables.Add(QueryStringVariables.SalesOrderLineID);
                    //[004] code end
                    //[005] code start
                    _lstVariables.Add(QueryStringVariables.SupplierInvoiceID);
                    //[005] code end

                    //[006] code start
                    _lstVariables.Add(QueryStringVariables.NPRID);
                    //[006] code end


                    //[007] code start
                    _lstVariables.Add(QueryStringVariables.EPRId);
					_lstVariables.Add(QueryStringVariables.CreditLimitId);
					//[007] code end
					_lstVariables.Add(QueryStringVariables.SalesPersonID);

                    _lstVariables.Add(QueryStringVariables.BuyerID);
                    _lstVariables.Add(QueryStringVariables.BOMID);
                    _lstVariables.Add(QueryStringVariables.POQuoteID);
                    _lstVariables.Add(QueryStringVariables.ClientInvoiceID);
                    //[007] start
                    _lstVariables.Add(QueryStringVariables.POLineSerialNo);
                    _lstVariables.Add(QueryStringVariables.POLineIds);
                    //[007] end
                    //[008] start
                    _lstVariables.Add(QueryStringVariables.DocNo);
                    _lstVariables.Add(QueryStringVariables.ActionType);
                    //[008] end
                    //[008] start
                    _lstVariables.Add(QueryStringVariables.ClientBOMID);
                    //[008] end
                    _lstVariables.Add(QueryStringVariables.SourcingResultID);
                    _lstVariables.Add(QueryStringVariables.IHSPartID);
					_lstVariables.Add(QueryStringVariables.SOLineID);
					_lstVariables.Add(QueryStringVariables.ShortShipmentID);
                    _lstVariables.Add(QueryStringVariables.ShortShipmentStage);
                    _lstVariables.Add(QueryStringVariables.UploadType);
                    _lstVariables.Add(QueryStringVariables.SAId);
                    _lstVariables.Add(QueryStringVariables.GILineId);
                    _lstVariables.Add(QueryStringVariables.EDitScreen);
                    _lstVariables.Add(QueryStringVariables.LandedGiLineId);
                    _lstVariables.Add(QueryStringVariables.IsLandedQuery);
					_lstVariables.Add(QueryStringVariables.ToDoID);
					_lstVariables.Add(QueryStringVariables.Category);
					_lstVariables.Add(QueryStringVariables.QuoteNumber);
				}
				return _lstVariables;
			}
		}

		public static string GetVariableQSName(QueryStringVariable objVariable) {
			return objVariable.VariableQSName;
		}

		public void ProcessQuerystring(NameValueCollection objQS) {
			if (objQS[QueryStringVariables.Tab.VariableQSName] != null) _intTab = int.Parse(objQS[QueryStringVariables.Tab.VariableQSName]);
			if (objQS[QueryStringVariables.CompanyID.VariableQSName] != null) _intCompanyID = int.Parse(objQS[QueryStringVariables.CompanyID.VariableQSName]);
			if (objQS[QueryStringVariables.CompanyName.VariableQSName] != null) _strCompanyName = objQS[QueryStringVariables.CompanyName.VariableQSName].ToString();
			if (objQS[QueryStringVariables.ContactID.VariableQSName] != null) _intContactID = int.Parse(objQS[QueryStringVariables.ContactID.VariableQSName]);
			if (objQS[QueryStringVariables.ContactName.VariableQSName] != null) _strContactName = objQS[QueryStringVariables.ContactName.VariableQSName].ToString();
			if (objQS[QueryStringVariables.ReturnURL.VariableQSName] != null) _strReturnURL = objQS[QueryStringVariables.ReturnURL.VariableQSName].ToString();
			if (objQS[QueryStringVariables.GenericID.VariableQSName] != null) _intGenericID = int.Parse(objQS[QueryStringVariables.GenericID.VariableQSName]);
			if (objQS[QueryStringVariables.LoginID.VariableQSName] != null) _intLoginID = int.Parse(objQS[QueryStringVariables.LoginID.VariableQSName]);
			if (objQS[QueryStringVariables.EmailMode.VariableQSName] != null) _blnEmailMode = (int.Parse(objQS[QueryStringVariables.EmailMode.VariableQSName]) == 1);
			if (objQS[QueryStringVariables.CompanyListType.VariableQSName] != null) _intCompanyListType = int.Parse(objQS[QueryStringVariables.CompanyListType.VariableQSName]);
			if (objQS[QueryStringVariables.CreditID.VariableQSName] != null) _intCreditID = int.Parse(objQS[QueryStringVariables.CreditID.VariableQSName]);
			if (objQS[QueryStringVariables.CompanyListType.VariableQSName] != null) _intCompanyListType = int.Parse(objQS[QueryStringVariables.CompanyListType.VariableQSName]);
			if (objQS[QueryStringVariables.StockListType.VariableQSName] != null) _intStockListType = int.Parse(objQS[QueryStringVariables.StockListType.VariableQSName]);
			if (objQS[QueryStringVariables.CustomerRequirementID.VariableQSName] != null) _intCustomerRequirementID = int.Parse(objQS[QueryStringVariables.CustomerRequirementID.VariableQSName]);
			if (objQS[QueryStringVariables.ManufacturerID.VariableQSName] != null) _intManufacturerID = int.Parse(objQS[QueryStringVariables.ManufacturerID.VariableQSName]);
			if (objQS[QueryStringVariables.ManufacturerName.VariableQSName] != null) _strManufacturerName = objQS[QueryStringVariables.ManufacturerName.VariableQSName].ToString();
			if (objQS[QueryStringVariables.MailMessageID.VariableQSName] != null) _intMailMessageID = int.Parse(objQS[QueryStringVariables.MailMessageID.VariableQSName]);
			if (objQS[QueryStringVariables.SalesOrderID.VariableQSName] != null) _intSalesOrderID = int.Parse(objQS[QueryStringVariables.SalesOrderID.VariableQSName]);
			if (objQS[QueryStringVariables.QuoteID.VariableQSName] != null) _intQuoteID = int.Parse(objQS[QueryStringVariables.QuoteID.VariableQSName]);
			if (objQS[QueryStringVariables.CRMAID.VariableQSName] != null) _intCRMAID = int.Parse(objQS[QueryStringVariables.CRMAID.VariableQSName]);
			if (objQS[QueryStringVariables.SRMAID.VariableQSName] != null) _intSRMAID = int.Parse(objQS[QueryStringVariables.SRMAID.VariableQSName]);
			if (objQS[QueryStringVariables.PurchaseOrderID.VariableQSName] != null) _intPurchaseOrderID = int.Parse(objQS[QueryStringVariables.PurchaseOrderID.VariableQSName]);
            if (objQS[QueryStringVariables.InternalPurchaseOrderID.VariableQSName] != null) _intInternalPurchaseOrderID = int.Parse(objQS[QueryStringVariables.InternalPurchaseOrderID.VariableQSName]);
			if (objQS[QueryStringVariables.PrintObject.VariableQSName] != null) _enmPrintObject = (SystemDocument.ListForPrint)int.Parse(objQS[QueryStringVariables.PrintObject.VariableQSName]);
			if (objQS[QueryStringVariables.PartNo.VariableQSName] != null) _strPartNo = objQS[QueryStringVariables.PartNo.VariableQSName].ToString();
			if (objQS[QueryStringVariables.SearchPartNo.VariableQSName] != null) _strSearchPartNo = objQS[QueryStringVariables.SearchPartNo.VariableQSName].ToString();
			if (objQS[QueryStringVariables.LineIDs.VariableQSName] != null) _aryLineIDs = Functions.JavascriptStringToArray(objQS[QueryStringVariables.LineIDs.VariableQSName]);
			if (objQS[QueryStringVariables.ReportID.VariableQSName] != null) _intReportID = int.Parse(objQS[QueryStringVariables.ReportID.VariableQSName]);
			if (objQS[QueryStringVariables.ReportParameters.VariableQSName] != null) _aryReportParameters = Functions.JavascriptStringToArray(objQS[QueryStringVariables.ReportParameters.VariableQSName]);
			if (objQS[QueryStringVariables.PurchaseRequisitionID.VariableQSName] != null) _intPurchaseRequisitionID = int.Parse(objQS[QueryStringVariables.PurchaseRequisitionID.VariableQSName]);
			if (objQS[QueryStringVariables.InvoiceID.VariableQSName] != null) _intInvoiceID = int.Parse(objQS[QueryStringVariables.InvoiceID.VariableQSName]);
			if (objQS[QueryStringVariables.HelpPage.VariableQSName] != null) _strHelpPage = objQS[QueryStringVariables.HelpPage.VariableQSName].ToString();
			if (objQS[QueryStringVariables.CRMAListType.VariableQSName] != null) _intCRMAListType = int.Parse(objQS[QueryStringVariables.CRMAListType.VariableQSName]);
			if (objQS[QueryStringVariables.POListType.VariableQSName] != null) _intPOListType = int.Parse(objQS[QueryStringVariables.POListType.VariableQSName]);
			if (objQS[QueryStringVariables.GoodsInID.VariableQSName] != null) _intGoodsInID = int.Parse(objQS[QueryStringVariables.GoodsInID.VariableQSName]);
			if (objQS[QueryStringVariables.SecurityGroupID.VariableQSName] != null) _intSecurityGroupID = int.Parse(objQS[QueryStringVariables.SecurityGroupID.VariableQSName]);
			if (objQS[QueryStringVariables.SOListType.VariableQSName] != null) _intSOListType = int.Parse(objQS[QueryStringVariables.SOListType.VariableQSName]);
			if (objQS[QueryStringVariables.CompanyListType.VariableQSName] != null) _intCompanyListType = int.Parse(objQS[QueryStringVariables.CompanyListType.VariableQSName]);
			if (objQS[QueryStringVariables.ServiceID.VariableQSName] != null) _intServiceID = int.Parse(objQS[QueryStringVariables.ServiceID.VariableQSName]);
			if (objQS[QueryStringVariables.LotID.VariableQSName] != null) _intLotID = int.Parse(objQS[QueryStringVariables.LotID.VariableQSName]);
            if (objQS[QueryStringVariables.BOMID.VariableQSName] != null) _intBOMID = int.Parse(objQS[QueryStringVariables.BOMID.VariableQSName]);
			if (objQS[QueryStringVariables.DebitID.VariableQSName] != null) _intDebitID = int.Parse(objQS[QueryStringVariables.DebitID.VariableQSName]);
			if (objQS[QueryStringVariables.SRMAListType.VariableQSName] != null) _intSRMAListType = int.Parse(objQS[QueryStringVariables.SRMAListType.VariableQSName]);
			if (objQS[QueryStringVariables.StockID.VariableQSName] != null) _intStockID = int.Parse(objQS[QueryStringVariables.StockID.VariableQSName]);
			if (objQS[QueryStringVariables.ToDoID.VariableQSName] != null) _intToDoID = int.Parse(objQS[QueryStringVariables.ToDoID.VariableQSName]);
			if (objQS[QueryStringVariables.SearchCompanyName.VariableQSName] != null) _strSearchCompanyName = objQS[QueryStringVariables.SearchCompanyName.VariableQSName].ToString();
			if (objQS[QueryStringVariables.BypassSavedState.VariableQSName] != null) _blnBypassSavedState = Convert.ToBoolean(objQS[QueryStringVariables.BypassSavedState.VariableQSName]);
            //[001] code start
            if (objQS[QueryStringVariables.InvoiceEmailStatusID.VariableQSName] != null) _intInvoiceEmailStatusID = int.Parse(objQS[QueryStringVariables.InvoiceEmailStatusID.VariableQSName]);
            //[001] code end
            //[002] code start
            if (objQS[QueryStringVariables.QuoteLineID.VariableQSName] != null) _intQuoteLineID = int.Parse(objQS[QueryStringVariables.QuoteLineID.VariableQSName]);
            //[002] code end
            //[003] code start
            if (objQS[QueryStringVariables.PurchaseOrderLineID.VariableQSName] != null) _intPurchaseOrderLineID = int.Parse(objQS[QueryStringVariables.PurchaseOrderLineID.VariableQSName]);
            if (objQS[QueryStringVariables.InternalPurchaseOrderLineID.VariableQSName] != null) _intInternalPurchaseOrderLineID = int.Parse(objQS[QueryStringVariables.InternalPurchaseOrderLineID.VariableQSName]);
            //[003] code end
            //[004] code start
            if (objQS[QueryStringVariables.SalesOrderLineID.VariableQSName] != null) _intSalesOrderLineID = int.Parse(objQS[QueryStringVariables.SalesOrderLineID.VariableQSName]);
            //[004] code end

            //[005] code start
            if (objQS[QueryStringVariables.SupplierInvoiceID.VariableQSName] != null) _intSupplierInvoiceID = int.Parse(objQS[QueryStringVariables.SupplierInvoiceID.VariableQSName]);
            //[005] code end

            //[006] code start
            if (objQS[QueryStringVariables.NPRID.VariableQSName] != null) _intNPRID = int.Parse(objQS[QueryStringVariables.NPRID.VariableQSName]);
            //[006] code end

            //[007] code start
            if (objQS[QueryStringVariables.EPRId.VariableQSName] != null) _intEPRId = int.Parse(objQS[QueryStringVariables.EPRId.VariableQSName]);
			if (objQS[QueryStringVariables.CreditLimitId.VariableQSName] != null) _intCreditLimitId = int.Parse(objQS[QueryStringVariables.CreditLimitId.VariableQSName]);
			//[007] code end
			if (objQS[QueryStringVariables.SalesPersonID.VariableQSName] != null) _intSalesPersonID = int.Parse(objQS[QueryStringVariables.SalesPersonID.VariableQSName]);

            if (objQS[QueryStringVariables.BuyerID.VariableQSName] != null) _intBuyerID = int.Parse(objQS[QueryStringVariables.BuyerID.VariableQSName]);
            if (objQS[QueryStringVariables.POQuoteID.VariableQSName] != null) _intPOQuoteID = int.Parse(objQS[QueryStringVariables.POQuoteID.VariableQSName]);

            //[010] code start
            if (objQS[QueryStringVariables.ClientInvoiceID.VariableQSName] != null) _intClientInvoiceID = int.Parse(objQS[QueryStringVariables.ClientInvoiceID.VariableQSName]);
            //[005] code end
            //[007] start
            if (objQS[QueryStringVariables.POLineSerialNo.VariableQSName] != null) _strPOLineSerialNo = objQS[QueryStringVariables.POLineSerialNo.VariableQSName];
            if (objQS[QueryStringVariables.POLineIds.VariableQSName] != null) _strPOLineIds = objQS[QueryStringVariables.POLineIds.VariableQSName];
            //[007] end
            //[008] start
            if (objQS[QueryStringVariables.DocNo.VariableQSName] != null) _DocNo = objQS[QueryStringVariables.DocNo.VariableQSName];
            if (objQS[QueryStringVariables.ActionType.VariableQSName] != null) _actionType = objQS[QueryStringVariables.ActionType.VariableQSName];
            if (objQS[QueryStringVariables.DocId.VariableQSName] != null) _DocId = objQS[QueryStringVariables.DocId.VariableQSName];
            //[008] end

            //[008] start
            if (objQS[QueryStringVariables.ClientBOMID.VariableQSName] != null) _intClientBOMID = int.Parse(objQS[QueryStringVariables.ClientBOMID.VariableQSName]);
            //[008] end
            if (objQS[QueryStringVariables.SourcingResultID.VariableQSName] != null) _intSourcingResultID = int.Parse(objQS[QueryStringVariables.SourcingResultID.VariableQSName]);
            if (objQS[QueryStringVariables.IHSPartID.VariableQSName] != null) _intIHSPartID = int.Parse(objQS[QueryStringVariables.IHSPartID.VariableQSName]);
			if (objQS[QueryStringVariables.SOLineID.VariableQSName] != null) _intSOLineID = int.Parse(objQS[QueryStringVariables.SOLineID.VariableQSName]);
			if (objQS[QueryStringVariables.ShortShipmentID.VariableQSName] != null) _intShortShipmentID = int.Parse(objQS[QueryStringVariables.ShortShipmentID.VariableQSName]);
            if (objQS[QueryStringVariables.ShortShipmentStage.VariableQSName] != null) _intShortShipmentStage = int.Parse(objQS[QueryStringVariables.ShortShipmentStage.VariableQSName]);
            if (objQS[QueryStringVariables.UploadType.VariableQSName] != null) _strUploadType = (string)(objQS[QueryStringVariables.UploadType.VariableQSName]);
            if (objQS[QueryStringVariables.SAId.VariableQSName] != null) _intsaId = int.Parse(objQS[QueryStringVariables.SAId.VariableQSName]);
            if (objQS[QueryStringVariables.GILineId.VariableQSName] != null) _intGILineId = int.Parse(objQS[QueryStringVariables.GILineId.VariableQSName]);
            if (objQS[QueryStringVariables.EDitScreen.VariableQSName] != null) _EDitScreen = int.Parse(objQS[QueryStringVariables.EDitScreen.VariableQSName]);
            if (objQS[QueryStringVariables.LandedGiLineId.VariableQSName] != null) _intLandedGiLineId = int.Parse(objQS[QueryStringVariables.LandedGiLineId.VariableQSName]);
            if (objQS[QueryStringVariables.IsLandedQuery.VariableQSName] != null) _bIsLandedQuery = bool.Parse(objQS[QueryStringVariables.IsLandedQuery.VariableQSName]);
			if (objQS[QueryStringVariables.Category.VariableQSName] != null) _intCategory = int.Parse(objQS[QueryStringVariables.Category.VariableQSName]);
			if (objQS[QueryStringVariables.QuoteNumber.VariableQSName] != null) _intQuoteNumber = int.Parse(objQS[QueryStringVariables.QuoteNumber.VariableQSName]);
		}

	}

	public class QueryStringVariable {

		public string VariableQSName;
		public string Name;
		public int Value;

		public QueryStringVariable(int intValue, string strName, string strVariableQSName)			 {
			Value = intValue;
			Name = strName;
			VariableQSName = strVariableQSName;
		}

	}

}
