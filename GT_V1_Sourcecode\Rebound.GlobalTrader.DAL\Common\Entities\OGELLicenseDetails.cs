﻿using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;

namespace Rebound.GlobalTrader.DAL {
	
	public class OGELLicenseDetails
    {
		
		#region Constructors

        public OGELLicenseDetails() { }

        #endregion

        #region Properties

        /// <summary>
        /// Ogel Id
        /// </summary>
        public System.Int32 OgelId { get; set; }
        /// <summary>
        /// Ogel Number
        /// </summary>
        public System.String OgelNumber { get; set; }
		/// <summary>
        /// Description
		/// </summary>
        public System.String Description { get; set; }		
		/// <summary>
		/// DLUP
		/// </summary>
		public System.DateTime DLUP { get; set; }
		/// <summary>
        /// Inactive
		/// </summary>
        public System.Boolean Inactive { get; set; }
        /// <summary>
        /// UpdatedBy
        /// </summary>
        public System.Int32? UpdatedBy { get; set; }
        /// <summary>
        /// EmployeeName
        /// </summary>
        public System.String EmployeeName { get; set; }
        #endregion

    }
}