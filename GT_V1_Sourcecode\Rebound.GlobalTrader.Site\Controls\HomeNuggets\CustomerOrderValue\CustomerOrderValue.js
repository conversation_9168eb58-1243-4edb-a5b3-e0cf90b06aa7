Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.HomeNuggets");Rebound.GlobalTrader.Site.Controls.HomeNuggets.CustomerOrderValue=function(n){Rebound.GlobalTrader.Site.Controls.HomeNuggets.CustomerOrderValue.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.HomeNuggets.CustomerOrderValue.prototype={get_tblCustomerOrder:function(){return this._tblCustomerOrder},set_tblCustomerOrder:function(n){this._tblCustomerOrder!==n&&(this._tblCustomerOrder=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.HomeNuggets.CustomerOrderValue.callBaseMethod(this,"initialize");this.addRefreshEvent(Function.createDelegate(this,this.getData))},dispose:function(){this.isDisposed||(this._tblCustomerOrder&&this._tblCustomerOrder.dispose(),this._tblCustomerOrder=null,Rebound.GlobalTrader.Site.Controls.HomeNuggets.CustomerOrderValue.callBaseMethod(this,"dispose"))},setupLoadingState:function(){this._tblCustomerOrder.show(!1);Rebound.GlobalTrader.Site.Controls.HomeNuggets.CustomerOrderValue.callBaseMethod(this,"setupLoadingState")},getData:function(){this.setupLoadingState();var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/HomeNuggets/CustomerOrderValue");n.set_DataObject("CustomerOrderValue");n.set_DataAction("GetData");n.addDataOK(Function.createDelegate(this,this.getDataComplete));n.addError(Function.createDelegate(this,this.homeNuggetDataError));n.addTimeout(Function.createDelegate(this,this.homeNuggetDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getDataComplete:function(n){var r,i,t,u;for(this.showNoneFoundOrContent(n._result.Count),r=n._result,this._tblCustomerOrder.clearTable(),this._tblCustomerOrder.show(r.CustomerOrder.length>0),i=0;i<r.CustomerOrder.length;i++)t=r.CustomerOrder[i],u=[$RGT_nubButton_Company(t.CompanyNo,t.CompanyName),t.TotalValue,t.AvailCredit],this._tblCustomerOrder.addRow(u,null),this._tblCustomerOrder.RowColor(i+1,t.RowCSS);this.hideLoading()}};Rebound.GlobalTrader.Site.Controls.HomeNuggets.CustomerOrderValue.registerClass("Rebound.GlobalTrader.Site.Controls.HomeNuggets.CustomerOrderValue",Rebound.GlobalTrader.Site.Controls.HomeNuggets.Base,Sys.IDisposable);