<%@ Control Language="C#" CodeBehind="CompanyApiCustomer_Edit.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Forms.CompanyApiCustomer_Edit" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Form:DesignBase ID="ctlDB" runat="server" ShowRequiredFieldsExplanation="true" ShowQuickHelp="false">
	<Links>
		<ReboundUI:IconButton ID="ibtnSave" runat="server" IconGroup="Nugget" IconTitleResource="Save" IconCSSType="Save" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
		<ReboundUI:IconButton ID="ibtnCancel" runat="server" IconGroup="Nugget" IconTitleResource="Cancel" IconCSSType="Cancel" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
	</Links>
	<Explanation><%=Functions.GetGlobalResource("FormExplanations", "CompanyApiCustomer_Edit")%></Explanation>
	<Content>
		<ReboundUI_Table:Form id="frm" runat="server">
		
          <ReboundUI_Form:FormField id="ctlEmail" runat="server" FieldID="txtEmail" ResourceTitle="Email" IsRequiredField="true">
				<Field><ReboundUI:ReboundTextBox Enabled="false" ID="txtEmail" runat="server" Width="200"  MaxLength="100" /></Field>
			</ReboundUI_Form:FormField>
			<%--Added By Vineet--%>
			<ReboundUI_Form:FormField id="ctlContactPerson" runat="server" FieldID="txtContactPerson" ResourceTitle="ContactName*">
				<Field><ReboundUI:ReboundTextBox Enabled="false" ID="txtContactPerson" runat="server" Width="200" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlMobile" runat="server" FieldID="txtMobile" ResourceTitle="Mobile*">
				<Field><ReboundUI:ReboundTextBox Enabled="false" ID="txtCountryCode" runat="server" Width="30" />-<ReboundUI:ReboundTextBox Enabled="false" ID="txtMobile" runat="server" Width="162" /></Field>
			</ReboundUI_Form:FormField>



			<ReboundUI_Form:FormField id="ctlInActive" runat="server" FieldID="chkInActive" ResourceTitle="IsInactive">
				<Field><ReboundUI:ImageCheckBox ID="chkInActive" runat="server" Enabled="true" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlBomUser" runat="server" FieldID="chkBomActive" ResourceTitle="BomUse">
				<Field><ReboundUI:ImageCheckBox ID="chkBomActive" runat="server" Enabled="true"/></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlSupplierUser" runat="server" FieldID="chkSupActive" ResourceTitle="SupUse">
				<Field><ReboundUI:ImageCheckBox ID="chkSupActive" runat="server" Enabled="true"/></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlPassword" runat="server" FieldID="txtPassword" ResourceTitle="Password*">
									<Field><ReboundUI:ReboundTextBox  id="txtPassword" runat="server" TextMode="password" Width="200" /></Field>
			</ReboundUI_Form:FormField>

			<%--<ReboundUI_Form:FormField id="ctlConfirmPassword" runat="server" FieldID="txtConfirmPassword" ResourceTitle="ConfirmPassword*">
									<Field><ReboundUI:ReboundTextBox  id="txtConfirmPassword" onpaste="return false;"  runat="server" TextMode="password" Width="200" /></Field>
			</ReboundUI_Form:FormField>--%>
			
			<asp:TableRow>
              <asp:TableCell class="title" RowSpan="2" Style="width: 10%">

				  <style>
		#btngenpass2 {margin: -23px 0px !important;
    position: absolute !important;
    width: 14% !important;
    padding: 2px !important;
    left: 400px !important;}

		.check_icon{margin: -20px 0px !important;
    position: absolute !important;
    width: 14% !important;
    padding: 3px !important;
    left: 335px !important;
    display: block;}
		.show_pass{margin: -24px 0px !important;
    position: absolute !important;
    width: 14% !important;
    padding: 3px !important;
    left: 418px !important;
    display: block;}


		#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmEdit_ctlDB_frm tr td { margin-right:10px !important; display:inline-block;}
		/*#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmAdd_ctlDB_ctlEmail_ctl04_txtEmail{ width:200px !important;color:white  !important;}*/

/*	input:disabled{
		color:#d5d5d5 !important;
	}*/

#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmEdit_ctlDB_ctlEmail_pnlFieldControls input:disabled{
	color:#d5d5d5 !important;
}
 
#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmEdit_ctlDB_ctlContactPerson_pnlFieldControls input:disabled{
	color:#d5d5d5 !important;
}

#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmEdit_ctlDB_ctlMobile_pnlFieldControls input:disabled{
	color:#d5d5d5 !important;
}


	</style>
				
              <input type="button" class="btn right" id="btngenpass2" value="GeneratePassword"> 
				
				  
               </asp:TableCell>
                  </asp:TableRow>

		</ReboundUI_Table:Form>
	</Content>
</ReboundUI_Form:DesignBase>
