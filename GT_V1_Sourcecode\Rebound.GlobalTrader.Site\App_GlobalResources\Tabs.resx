<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Accounts" xml:space="preserve">
    <value>Accounts</value>
  </data>
  <data name="All" xml:space="preserve">
    <value>All</value>
  </data>
  <data name="AllOrders" xml:space="preserve">
    <value>All Orders</value>
  </data>
  <data name="AllUnshipped" xml:space="preserve">
    <value>All Unshipped</value>
  </data>
  <data name="Available" xml:space="preserve">
    <value>Available</value>
  </data>
  <data name="AwaitingReceipt" xml:space="preserve">
    <value>Awaiting Receipt</value>
  </data>
  <data name="Broker" xml:space="preserve">
    <value>Broker</value>
  </data>
  <data name="Calls" xml:space="preserve">
    <value>Calls</value>
  </data>
  <data name="Closed" xml:space="preserve">
    <value>Closed</value>
  </data>
  <data name="CreditNotes" xml:space="preserve">
    <value>Credits</value>
  </data>
  <data name="Credits" xml:space="preserve">
    <value>Credits</value>
  </data>
  <data name="CRMAs" xml:space="preserve">
    <value>CRMAs</value>
  </data>
  <data name="CustomerRMAs" xml:space="preserve">
    <value>CRMAs</value>
  </data>
  <data name="DebitNotes" xml:space="preserve">
    <value>Debits</value>
  </data>
  <data name="Debits" xml:space="preserve">
    <value>Debits</value>
  </data>
  <data name="Detail" xml:space="preserve">
    <value>Detail</value>
  </data>
  <data name="GoodsIn" xml:space="preserve">
    <value>Goods In</value>
  </data>
  <data name="Invoices" xml:space="preserve">
    <value>Invoices</value>
  </data>
  <data name="Log" xml:space="preserve">
    <value>Log</value>
  </data>
  <data name="Main" xml:space="preserve">
    <value>Main</value>
  </data>
  <data name="None" xml:space="preserve">
    <value>None</value>
  </data>
  <data name="Old" xml:space="preserve">
    <value>Old</value>
  </data>
  <data name="Open" xml:space="preserve">
    <value>Open</value>
  </data>
  <data name="POs" xml:space="preserve">
    <value>POs</value>
  </data>
  <data name="PurchaseOrders" xml:space="preserve">
    <value>POs</value>
  </data>
  <data name="Purchasing" xml:space="preserve">
    <value>Purchasing</value>
  </data>
  <data name="Quarantined" xml:space="preserve">
    <value>Quarantined</value>
  </data>
  <data name="Quotations" xml:space="preserve">
    <value>Quotes</value>
  </data>
  <data name="Quotes" xml:space="preserve">
    <value>Quotes</value>
  </data>
  <data name="ReadyToReceive" xml:space="preserve">
    <value>Ready to Receive</value>
  </data>
  <data name="ReadyToShip" xml:space="preserve">
    <value>Ready to Ship</value>
  </data>
  <data name="Received" xml:space="preserve">
    <value>Received</value>
  </data>
  <data name="RelatedByCRMALine" xml:space="preserve">
    <value>By CRMA Line</value>
  </data>
  <data name="RelatedByPart" xml:space="preserve">
    <value>By Part</value>
  </data>
  <data name="RelatedByPOLine" xml:space="preserve">
    <value>By PO Line</value>
  </data>
  <data name="Reqs" xml:space="preserve">
    <value>Reqs</value>
  </data>
  <data name="Requirements" xml:space="preserve">
    <value>Reqs</value>
  </data>
  <data name="Sales" xml:space="preserve">
    <value>Sales</value>
  </data>
  <data name="SalesOrders" xml:space="preserve">
    <value>SOs</value>
  </data>
  <data name="Services" xml:space="preserve">
    <value>Services</value>
  </data>
  <data name="Shipped" xml:space="preserve">
    <value>Shipped</value>
  </data>
  <data name="SOs" xml:space="preserve">
    <value>SOs</value>
  </data>
  <data name="SRMAs" xml:space="preserve">
    <value>SRMAs</value>
  </data>
  <data name="Stock" xml:space="preserve">
    <value>Stock</value>
  </data>
  <data name="SupplierRMAs" xml:space="preserve">
    <value>SRMAs</value>
  </data>
  <data name="Today" xml:space="preserve">
    <value>Today</value>
  </data>
  <data name="Uninspected" xml:space="preserve">
    <value>Unreleased</value>
  </data>
  <data name="Warehouse" xml:space="preserve">
    <value>Warehouse</value>
  </data>
  <data name="CanNotBeExported" xml:space="preserve">
    <value>On Hold</value>
  </data>
  <data name="NonZeroStock" xml:space="preserve">
    <value>Non Zero Stock</value>
  </data>
  <data name="Certificate" xml:space="preserve">
    <value>Certificate</value>
  </data>
  <data name="LotStockProvision" xml:space="preserve">
    <value>Stock Provision</value>
  </data>
  <data name="CustReq" xml:space="preserve">
    <value>Customer Requirements</value>
  </data>
  <data name="PowerBIDash" xml:space="preserve">
    <value>Sales BI</value>
  </data>
  <data name="ApprovalSatus" xml:space="preserve">
    <value>Approval Status</value>
  </data>
  <data name="SupplierApprovalHistory" xml:space="preserve">
    <value>Supplier Approval History</value>
  </data>
  <data name="TradeRefrences" xml:space="preserve">
    <value>Supplier Information</value>
  </data>
  <data name="Approved" xml:space="preserve">
    <value>Approved</value>
  </data>
  <data name="Awaiting" xml:space="preserve">
    <value>Awaiting</value>
  </data>
  <data name="GI_Queried" xml:space="preserve">
    <value>Queried</value>
  </data>
  <data name="CustomerAPI" xml:space="preserve">
    <value>Customer API</value>
  </data>
  <data name="AS6081" xml:space="preserve">
    <value>AS6081</value>
  </data>
  <data name="BOMs" xml:space="preserve">
    <value>BOMs</value>
  </data>
</root>