using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;

namespace Rebound.GlobalTrader.Site.Controls.HomeNuggets.Data
{
    [WebService(Namespace = "http://tempuri.org/")]
    [WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    public class Bom : Rebound.GlobalTrader.Site.Data.Base
    {

        public override void ProcessRequest(HttpContext context)
        {
            if (base.init(context))
            {
                switch (Action)
                {
                    case "GetData": GetData(); break;
                    default: WriteErrorActionNotFound(); break;
                }
            }
        }

        /// <summary>
        /// Gets the main data
        /// </summary>
        private void GetData()
        {
            try
            {
                //List<BLL.BOM> allBom = BLL.BOM.GetBomList(SessionManager.ClientID, SessionManager.IsPOHub, null,);
                List<BLL.BOM> lstReady = new List<BOM>();
                List<BLL.BOM> lstPartial = new List<BOM>();
                List<BLL.BOM> lstNew = new List<BOM>();
                List<BLL.BOM> lstRFQ = new List<BOM>();
                List<BLL.BOM> lstClosed = new List<BOM>();
                List<BLL.BOM> lstNoBid = new List<BOM>();

                //Closed
                JsonObject jsn = new JsonObject();
                JsonObject jsnItems = new JsonObject(true);
                //lstClosed = allBom.FindAll(x => x.StatusValue == (int)BOMStatus.List.Closed);
                //lstClosed = lstClosed.FindAll(x => SessionManager.IsPOHub == true ? true : x.UpdatedBy == SessionManager.LoginID);
                lstClosed = BLL.BOM.GetBomList(SessionManager.ClientID, SessionManager.IsPOHub, null, (int)BOMStatus.List.Closed, SessionManager.LoginID);
                //for (int i = 0; i < (lstClosed.Count > 5 ? 5 : lstClosed.Count); i++)
                for (int i = 0; i < lstClosed.Count; i++)
                {
                    JsonObject jsnItem = new JsonObject();
                    jsnItem.AddVariable("BOMId", lstClosed[i].BOMId);
                    jsnItem.AddVariable("BOMCode", lstClosed[i].BOMName);
                    jsnItem.AddVariable("Name", SessionManager.IsPOHub == true && lstClosed[i].RequestToPOHubBy > 0 ? lstClosed[i].ClientName : lstClosed[i].CompanyName);
                    jsnItem.AddVariable("Date", Functions.FormatDate(lstClosed[i].DLUP));
                    jsnItem.AddVariable("QuoteRequired", Functions.FormatDate(lstClosed[i].QuoteRequired));
                    jsnItems.AddVariable(jsnItem);
                    jsnItem.Dispose();
                    jsnItem = null;
                }
                jsn.AddVariable("Closed", jsnItems);
                jsn.AddVariable("ClosedCount", lstClosed.Count);
                jsnItems.Dispose();
                jsnItems = null;
                lstClosed = null;
                //Ready                
                jsnItems = new JsonObject(true);
                //lstReady = allBom.FindAll(x => x.StatusValue == (int)BOMStatus.List.Released );
                //lstReady = lstReady.FindAll(x=> SessionManager.IsPOHub == true ? true : x.UpdatedBy == SessionManager.LoginID);
                lstReady = BLL.BOM.GetBomList(SessionManager.ClientID, SessionManager.IsPOHub, null, (int)BOMStatus.List.Released, SessionManager.LoginID);
                for (int i = 0; i < lstReady.Count; i++)
                {
                    JsonObject jsnItem = new JsonObject();
                    jsnItem.AddVariable("BOMId", lstReady[i].BOMId);
                    jsnItem.AddVariable("BOMCode", lstReady[i].BOMName);
                    jsnItem.AddVariable("Name", SessionManager.IsPOHub == true && lstReady[i].RequestToPOHubBy > 0 ? lstReady[i].ClientName : lstReady[i].CompanyName);
                    jsnItem.AddVariable("Date", Functions.FormatDate(lstReady[i].DLUP));
                    jsnItem.AddVariable("QuoteRequired", Functions.FormatDate(lstReady[i].QuoteRequired));
                    jsnItems.AddVariable(jsnItem);
                    jsnItem.Dispose();
                    jsnItem = null;
                }
                jsn.AddVariable("Ready", jsnItems);
                jsn.AddVariable("ReadyCount", lstReady.Count);
                jsnItems.Dispose();
                jsnItems = null;
                lstReady = null;
                //Partial
                jsnItems = new JsonObject(true);
                //lstPartial = allBom.FindAll(x => x.StatusValue == (int)BOMStatus.List.PartialReleased );
                //lstPartial = lstPartial.FindAll(x=> SessionManager.IsPOHub == true ? true : x.UpdatedBy == SessionManager.LoginID);
                lstPartial = BLL.BOM.GetBomList(SessionManager.ClientID, SessionManager.IsPOHub, null, (int)BOMStatus.List.PartialReleased, SessionManager.LoginID);
                for (int i = 0; i < lstPartial.Count; i++)
                {
                    JsonObject jsnItem = new JsonObject();
                    jsnItem.AddVariable("BOMId", lstPartial[i].BOMId);
                    jsnItem.AddVariable("BOMCode", lstPartial[i].BOMName);
                    jsnItem.AddVariable("Name", SessionManager.IsPOHub == true && lstPartial[i].RequestToPOHubBy > 0 ? lstPartial[i].ClientName : lstPartial[i].CompanyName);
                    jsnItem.AddVariable("Date", Functions.FormatDate(lstPartial[i].DLUP));
                    jsnItem.AddVariable("QuoteRequired", Functions.FormatDate(lstPartial[i].QuoteRequired));
                    jsnItems.AddVariable(jsnItem);
                    jsnItem.Dispose();
                    jsnItem = null;
                }
                jsn.AddVariable("Partial", jsnItems);
                jsn.AddVariable("PartialCount", lstPartial.Count);
                jsnItems.Dispose();
                jsnItems = null;
                lstPartial = null;

                //New               
                jsnItems = new JsonObject(true);
                //lstNew = allBom.FindAll(x => x.StatusValue == (int)BOMStatus.List.New);
                //lstNew = lstNew.FindAll(x=> SessionManager.IsPOHub == true ? true : x.UpdatedBy == SessionManager.LoginID);
                lstNew = BLL.BOM.GetBomList(SessionManager.ClientID, SessionManager.IsPOHub, null, (int)BOMStatus.List.New, SessionManager.LoginID);
                for (int i = 0; i < lstNew.Count; i++)
                {
                    JsonObject jsnItem = new JsonObject();
                    jsnItem.AddVariable("BOMId", lstNew[i].BOMId);
                    jsnItem.AddVariable("BOMCode", lstNew[i].BOMName);
                    jsnItem.AddVariable("Name", SessionManager.IsPOHub == true && lstNew[i].RequestToPOHubBy > 0 ? lstNew[i].ClientName : lstNew[i].CompanyName);
                    jsnItem.AddVariable("Date", Functions.FormatDate(lstNew[i].DLUP));
                    jsnItem.AddVariable("QuoteRequired", Functions.FormatDate(lstNew[i].QuoteRequired));
                    jsnItems.AddVariable(jsnItem);
                    jsnItem.Dispose();
                    jsnItem = null;
                }
                jsn.AddVariable("New", jsnItems);
                jsn.AddVariable("NewCount", lstNew.Count);
                jsnItems.Dispose();
                jsnItems = null;
                lstNew = null;

                //RFQ
                jsnItems = new JsonObject(true);
                //lstRFQ = allBom.FindAll(x => x.StatusValue == (int)BOMStatus.List.RPQ);
                //lstRFQ = lstRFQ.FindAll(x => SessionManager.IsPOHub == true ? true : x.UpdatedBy == SessionManager.LoginID);
                lstRFQ = BLL.BOM.GetBomList(SessionManager.ClientID, SessionManager.IsPOHub, null, (int)BOMStatus.List.RPQ, SessionManager.LoginID);
                for (int i = 0; i < lstRFQ.Count; i++)
                {
                    JsonObject jsnItem = new JsonObject();
                    jsnItem.AddVariable("BOMId", lstRFQ[i].BOMId);
                    jsnItem.AddVariable("BOMCode", lstRFQ[i].BOMName);
                    jsnItem.AddVariable("Name", SessionManager.IsPOHub == true && lstRFQ[i].RequestToPOHubBy > 0 ? lstRFQ[i].ClientName : lstRFQ[i].CompanyName);
                    jsnItem.AddVariable("Date", Functions.FormatDate(lstRFQ[i].DLUP));
                    jsnItem.AddVariable("QuoteRequired", Functions.FormatDate(lstRFQ[i].QuoteRequired));
                    jsnItems.AddVariable(jsnItem);
                    jsnItem.Dispose();
                    jsnItem = null;
                }
                jsn.AddVariable("IsHub", SessionManager.IsPOHub == true);
                jsn.AddVariable("RFQ", jsnItems);
                jsn.AddVariable("RFQCount", lstRFQ.Count);
                jsnItems.Dispose();
                jsnItems = null;
                lstRFQ = null;
                //NoBid
                jsnItems = new JsonObject(true);
                //lstNoBid = allBom.FindAll(x => x.StatusValue == (int)BOMStatus.List.NoBid);
                //lstNoBid = lstNoBid.FindAll(x => SessionManager.IsPOHub == true ? true : x.UpdatedBy == SessionManager.LoginID);
                lstNoBid = BLL.BOM.GetBomList(SessionManager.ClientID, SessionManager.IsPOHub, null, (int)BOMStatus.List.NoBid, SessionManager.LoginID);
                for (int i = 0; i < lstNoBid.Count; i++)
                {
                    JsonObject jsnItem = new JsonObject();
                    jsnItem.AddVariable("BOMId", lstNoBid[i].BOMId);
                    jsnItem.AddVariable("BOMCode", lstNoBid[i].BOMName);
                    jsnItem.AddVariable("Name", SessionManager.IsPOHub == true && lstNoBid[i].RequestToPOHubBy > 0 ? lstNoBid[i].ClientName : lstNoBid[i].CompanyName);
                    jsnItem.AddVariable("Date", Functions.FormatDate(lstNoBid[i].DLUP));
                    jsnItem.AddVariable("QuoteRequired", Functions.FormatDate(lstNoBid[i].QuoteRequired));
                    jsnItems.AddVariable(jsnItem);
                    jsnItem.Dispose();
                    jsnItem = null;
                }
                jsn.AddVariable("NoBid", jsnItems);
                jsn.AddVariable("NoBidCount", (lstNoBid.Count > 5 ? 5 : lstNoBid.Count));
                jsnItems.Dispose();
                jsnItems = null;
                lstNoBid = null;

                OutputResult(jsn);
                jsn.Dispose();
                jsn = null;
            }
            catch (Exception ex)
            {
                Errorlog obj = new Errorlog();
                obj.LogMessage(Convert.ToString("Exception- " + ex.Message + " " + ex.StackTrace));
            }

        }
    }
}

