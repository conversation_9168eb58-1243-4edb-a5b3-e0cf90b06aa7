<%--
Marker     changed by     Date           Remarks
[001]      Vinay          18/05/2015     ESMS Ref:233
--%>
<%@ Control Language="C#" AutoEventWireup="false" CodeBehind="CompanyPurchasingInfo.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyPurchasingInfo"  %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Nugget:DesignBase ID="ctlDB" runat="server" BoxType="Standard">
	<Links>
		<ReboundUI:IconButton ID="ibtnEdit" runat="server" IconGroup="Nugget" IconTitleResource="Edit" IconCSSType="Edit" IconButtonMode="Hyperlink" Href="javascript:void(0);" />
	</Links>
	
	<Content>
		<table class="twoCols">
			<tr>
				<td class="col1">
					<table cellpadding="0" cellspacing="0" border="0" class="dataItems">
						<ReboundUI:DataItemRow id="ctlIsApproved" runat="server" ResourceTitle="IsApproved"  />
						<ReboundUI:DataItemRow id="hidIsApproved" runat="server" FieldType="Hidden" />
						<%--[001] code start--%>
						<ReboundUI:DataItemRow id="ctlOnStop" runat="server" ResourceTitle="OnStop"  FieldType="CheckBox" />
						<%--[001] code end--%>
						<ReboundUI:DataItemRow id="ctlCurrency" runat="server" ResourceTitle="Currency" />
						<ReboundUI:DataItemRow id="hidCurrencyNo" runat="server" FieldType="Hidden" />
						<ReboundUI:DataItemRow id="ctlSupplierNo" runat="server" ResourceTitle="SupplierNo" />
						<ReboundUI:DataItemRow id="ctlTerms" runat="server" ResourceTitle="Terms" />
						<ReboundUI:DataItemRow id="hidTermsNo" runat="server" FieldType="Hidden" />
						<ReboundUI:DataItemRow id="hidSupOnStop" runat="server" FieldType="Hidden" />
						<%--<ReboundUI:DataItemRow id="ctlTax" runat="server" ResourceTitle="Tax" />
						<ReboundUI:DataItemRow id="hidTaxNo" runat="server" FieldType="Hidden" />
					--%>	
					<ReboundUI:DataItemRow id="ctlRating" runat="server" ResourceTitle="Rating" FieldType="StarRating" />
						<ReboundUI:DataItemRow id="ctlShipVia" runat="server" ResourceTitle="DefaultShipVia" FieldType="Hidden" />
						<ReboundUI:DataItemRow id="hidShipViaNo" runat="server" FieldType="Hidden" />
						<ReboundUI:DataItemRow id="ctlShippingAccountNo" runat="server" ResourceTitle="DefaultShippingAccountNo" FieldType="Hidden" />
						<ReboundUI:DataItemRow id="ctlContact" runat="server" ResourceTitle="DefaultContactNo" />
						<ReboundUI:DataItemRow id="hidContactNo" runat="server" FieldType="Hidden" />
						<ReboundUI:DataItemRow id="ctlPOShipCountry" runat="server" ResourceTitle="DefaultShipCountry" />
						<ReboundUI:DataItemRow id="hidPOShipCountryNo" runat="server" FieldType="Hidden" />
					</table>
				</td>
				<td class="col2">
					<div class="dataItem_Title"><%=Functions.GetGlobalResource("FormFields", "OpenPOs")%></div>
					<asp:Panel ID="pnlGetOpenPOs" runat="server" CssClass="getData"><asp:HyperLink ID="hypGetOpenPOs" runat="server" NavigateUrl="javascript:void(0);"><%=Functions.GetGlobalResource("Buttons", "GetData")%></asp:HyperLink></asp:Panel>
					<asp:Panel ID="pnlLoadingOpenPOs" runat="server" CssClass="subInfoLoading invisible"><%=Functions.GetGlobalResource("Misc", "Loading")%></asp:Panel>
					<asp:Panel ID="pnlOpenPOsError" runat="server" CssClass="error invisible" />
					<asp:Panel ID="pnlOpenPOs" runat="server" CssClass="invisible"><ReboundUI:FlexiDataTable ID="tblOpenPOs" runat="server" PanelHeight="70" /></asp:Panel>
				</td>
			</tr>
			<tr><td colspan="2"><div class="line"></div></td></tr>
			<tr>
				<td>
					<table cellpadding="0" cellspacing="0" border="0" class="dataItems">
						<ReboundUI:DataItemRow id="ctlYearToDate" runat="server" ResourceTitle="YearToDate" ShowEllipses="true" />
						<ReboundUI:DataItemRow id="ctlLastYear" runat="server" ResourceTitle="LastYear" ShowEllipses="true" />
					</table>
				</td>
				<td class="col3">
					<div class="dataItem_Title"><%=Functions.GetGlobalResource("FormFields", "OverduePOs")%></div>
					<asp:Panel ID="pnlGetOverduePOs" runat="server" CssClass="getData"><asp:HyperLink ID="hypGetOverduePOs" runat="server" NavigateUrl="javascript:void(0);"><%=Functions.GetGlobalResource("Buttons", "GetData")%></asp:HyperLink></asp:Panel>
					<asp:Panel ID="pnlLoadingOverduePOs" runat="server" CssClass="subInfoLoading invisible"><%=Functions.GetGlobalResource("Misc", "Loading")%></asp:Panel>
					<asp:Panel ID="pnlOverduePOsError" runat="server" CssClass="error invisible" />
					<asp:Panel ID="pnlOverduePOs" runat="server" CssClass="invisible"><ReboundUI:FlexiDataTable ID="tblOverduePOs" runat="server" PanelHeight="70" /></asp:Panel>
				</td>
			</tr>
		</table>
	</Content>
	
	<Forms>
		<ReboundForm:CompanyPurchasingInfo_Edit id="ctlEdit" runat="server" />
	</Forms>
	
</ReboundUI_Nugget:DesignBase>
