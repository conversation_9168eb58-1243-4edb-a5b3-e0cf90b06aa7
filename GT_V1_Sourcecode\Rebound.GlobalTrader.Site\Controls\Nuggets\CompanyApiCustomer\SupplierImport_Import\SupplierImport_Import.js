Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.SupplierImport_Import=function(n){Rebound.GlobalTrader.Site.Controls.Forms.SupplierImport_Import.initializeBase(this,[n]);this._intCompanyID=0};Rebound.GlobalTrader.Site.Controls.Forms.SupplierImport_Import.prototype={get_ibtnSend:function(){return this._ibtnSend},set_ibtnSend:function(n){this._ibtnSend!==n&&(this._ibtnSend=n)},get_ibtnSend_Footer:function(){return this._ibtnSend_Footer},set_ibtnSend_Footer:function(n){this._ibtnSend_Footer!==n&&(this._ibtnSend_Footer=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Forms.SupplierImport_Import.callBaseMethod(this,"initialize")},formShown:function(){this._blnFirstTimeShown&&($R_IBTN.addClick(this._ibtnSend,Function.createDelegate(this,this.sendMail)),$R_IBTN.addClick(this._ibtnSend_Footer,Function.createDelegate(this,this.sendMail)),this.addCancel(Function.createDelegate(this,this.cancelClicked)))},dispose:function(){this.isDisposed||Rebound.GlobalTrader.Site.Controls.Forms.SupplierImport_Import.callBaseMethod(this,"dispose")},getMessageText:function(){Rebound.GlobalTrader.Site.WebServices.GetMailMessage_NotifyNPR(this._intNPRID,Function.createDelegate(this,this.getMessageTextComplete))},getMessageTextComplete:function(n){this._ctlMail.setValue_Body(n);this._ctlMail.setValue_Subject(String.format($R_RES.NotifyNPR,this._strNPRNo));this._ctlMail.addNewLoginRecipient(this._intBuyerId,this._strBuyerName)},sendMail:function(){this.validateForm()&&(this.showLoading(!0),this.enableButton(!1),Rebound.GlobalTrader.Site.WebServices.NotifyNPRMessage($R_FN.arrayToSingleString(this._ctlMail._aryRecipientLoginIDs),$R_FN.arrayToSingleString(this._ctlMail._aryRecipientGroupIDs),this._ctlMail.getValue_Subject(),this._ctlMail.getValue_Body(),"",this._intNPRID,this._intGoodsInLineId,$R_FN.arrayToSingleString(this._ctlMail._aryRecipientLoginNames,"/"),$R_FN.arrayToSingleString(this._ctlMail._aryRecipientGroupNames,"/"),Function.createDelegate(this,this.sendMailComplete)))},validateForm:function(){var n=this._ctlMail.validateFields();return n||this.showError(!0),n},cancelClicked:function(){alert("abc11");this.showForm(this.frmSupplierImport,!1)},sendMailComplete:function(){this.showLoading(!1);this.showSavedOK(!0);location.href=$RGT_gotoURL_GoodsIn(this._intGoodsIn);this.enableButton(!0)},impotExcelData:function(n,t,i,r,u){var e,f;return $("#divLoader").show(),e=$("#ddlClient"),e.val()==0?(alert("Please select client!"),$("#divLoader").hide(),!1):(f=new Rebound.GlobalTrader.Site.Data,f._intTimeoutMilliseconds=2e5,f.set_PathToData("controls/Nuggets/CompanyApiCustomer"),f.set_DataObject("SupplierImport"),f.set_DataAction("ImportExcelData"),f.addParameter("originalFilename",n),f.addParameter("generatedFilename",t),u==1?f.addParameter("ClientId",i):f.addParameter("ClientId",i),f.addParameter("ColumnHeader",r),f.addParameter("SelectedClientType",u),f.addDataOK(Function.createDelegate(this,this.importExcelDataOK)),f.addError(Function.createDelegate(this,this.importExcelDataError)),f.addTimeout(Function.createDelegate(this,this.importExcelDataError)),$R_DQ.addToQueue(f),$R_DQ.processQueue(),f=null,!0)},importExcelDataOK:function(n){flogId=n._result.FileLogId;$("#divLoader").hide();$("#btnDisplayCsvDataSupp").prop("disabled",!1).css("opacity",5.5);$("#excelipload").prop("disabled",!0).css("opacity",.5);$('input:checkbox[id="chkFileCCH"]').prop("disabled",!0);$("input:file").filter(function(){return this.files.length==0}).prop("disabled",!0);var t=n._result.IsLimitExceeded;t&&alert(n._result.LimitErrorMessage)},importExcelDataError:function(n){alert(n._errorMessage.split("<br/>")[0]);$("#divLoader").hide()},enableButton:function(n){$R_IBTN.enableButton(this._ibtnSend,n);$R_IBTN.enableButton(this._ibtnSend_Footer,n)}};Rebound.GlobalTrader.Site.Controls.Forms.SupplierImport_Import.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.SupplierImport_Import",Rebound.GlobalTrader.Site.Controls.Forms.Base,Sys.IDisposable);