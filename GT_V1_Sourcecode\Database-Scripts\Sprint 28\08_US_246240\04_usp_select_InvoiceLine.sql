﻿
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

/*
==============================================================================================================================================================  
TASK			UPDATED BY       DATE				ACTION		DESCRIPTION  
[US-246240]     Phuc Hoang		 15-May-2025		UPDATE		Invoice - Line from 'Authorised SO Service Line' source function on Client/ DMCC side (Part 2)
==============================================================================================================================================================  
*/

CREATE OR ALTER PROCEDURE[dbo].[usp_select_InvoiceLine]                
--***********************************************************************************************              
/*                                  
Marker     changed by      date         Remarks                                  
              
[002]      Suhail        20/04/2018    Added Column MSLLevel in table tbInvoicelIne  and setting value from tbSalesorderLine                        
[003]    Ravi    18-09-2023    18-09-2023      RP-2339  AS6081 (Show AS6081 on detail screens)               
*/                    
--***********************************************************************************************                  
    @InvoiceLineId int                  
AS                   
BEGIN                
                
    Declare @vvSalesOrderNo Int                
    Declare @vvSalesOrderNumber Int                
                  
    Select @vvSalesOrderNo = InvLine.SalesOrderNo,                 
           @vvSalesOrderNumber = SO.SalesOrderNumber                 
    From tbInvoiceLine InvLine                 
    Inner Join tbSalesOrder SO On SO.SalesOrderId = InvLine.SalesOrderNo                
    Where InvoiceLineId = @InvoiceLineId                
                
                
    SELECT  l.InvoiceNo,                
            l.InvoiceNumber,                
            l.InvoiceLineId,                
            l.InvoiceDate,                
            l.ClientNo,                
            l.DateOrdered,                
            l.CustomerPO,                
            l.SalesOrderLineNo,                
            l.SupplierRMANo,                
            l.SupplierRMANumber,                
            l.SupplierRMALineNo,                
            l.SupplierRMADate,                
            l.Salesman,                
            l.SalesmanName,                
            l.DivisionNo,                
            l.TeamNo,                
            l.CompanyNo,                
            l.CompanyName,                
            l.ContactNo,                
            l.ContactName,                
            l.Price,                
            l.FullPart,                
            l.Part,                
            l.ROHS,                
            l.CustomerPart,                
            l.Quantity,                
            l.DateCode,                
            l.DatePromised,                
            l.CurrencyNo,                
            l.CurrencyCode,                
            l.CurrencyDescription,                
            l.ProductNo,                
            l.ProductName,                
            l.ProductDescription,                
            l.ProductDutyCode,                
            l.PackageNo,                
            l.PackageName,                
            l.PackageDescription,                
            l.ManufacturerNo,                
            l.ManufacturerName,                
            l.ManufacturerCode,                
            l.InvoicePaid,                
            l.QuantityShipped,                
            l.LandedCost,                
            l.LineSource,                
            l.QuantityOrdered,                
            l.Taxable,                
            l.ShippedBy,                
            l.ShippedByName,                
            l.ShippedDate,                
            l.LineNotes,                
            l.Inactive,                
            l.ServiceNo,                
            i.CurrencyNo,                  
            i.CurrencyCode,                
            i.CurrencyDescription,                
            @vvSalesOrderNo As 'SalesOrderNo',                
            @vvSalesOrderNumber As 'SalesOrderNumber'                
   , dbo.ufn_get_productdutyrate(l.ProductNo,getdate()) as ProductDutyRate                 
   ---[002]  Code Start              
   ,l.MSLLevel --- TO get MSLLevel for Invoice LIne No 17-04-2018               
   ---[002]  Code End              
   , l.PrintHazardous              
   , l.IsProdHazardous              
    ,l.CountryOfOrigin            
           
 ,l.LifeCycleStage            
 ,l.HTSCode            
 ,l.AveragePrice            
 ,l.Packing            
 ,l.PackagingSize            
 ,l.IHSCountryOfOrigin          
 ,l.Descriptions         
 ,l.IHSProduct          
 ,l.ECCNCode    
 ,l.IsOrderViaIPOonly   
 , ISNULL(l.AS6081,0) as AS6081 --[003]  
 , l.RequiredDate
    FROM    vwInvoiceLine l                  
    JOIN    vwInvoice i ON l.InvoiceNo = i.InvoiceID                  
    WHERE   InvoiceLineId = @InvoiceLineId                                
END; 
GO


