<%--
Marker     Changed by      Date         Remarks
[001]      Abhinav         25/03/2014   EMS#108 lock Quantity & Price field--%>
<%@ Control Language="C#" CodeBehind="DebitLines_Edit.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Forms.DebitLines_Edit" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>

<ReboundUI_Form:DesignBase ID="ctlDB" runat="server" ShowRequiredFieldsExplanation="true" ShowQuickHelp="false">

	<Links>
		<ReboundUI:IconButton ID="ibtnSave" runat="server" IconGroup="Nugget" IconTitleResource="Save" IconCSSType="Save" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
		<ReboundUI:IconButton ID="ibtnCancel" runat="server" IconGroup="Nugget" IconTitleResource="Cancel" IconCSSType="Cancel" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
	</Links>

	<Explanation><%=Functions.GetGlobalResource("FormExplanations", "DebitLines_Edit")%></Explanation>

	<Content>
	
		<ReboundUI_Table:Form id="frm" runat="server">

			<ReboundUI_Form:FormField id="ctlPartNo" runat="server" FieldID="lblPartNo" ResourceTitle="PartNo">
				<Field><asp:Label ID="lblPartNo" runat="server" /></Field>
			</ReboundUI_Form:FormField>
			
			<ReboundUI_Form:FormField id="ctlService" runat="server" FieldID="txtService" ResourceTitle="Service" IsRequiredField="true">
				<Field><ReboundUI:ReboundTextBox ID="txtService" runat="server" Width="200" /></Field>
			</ReboundUI_Form:FormField>
			
			<ReboundUI_Form:FormField id="ctlServiceDescription" runat="server" FieldID="txtServiceDescription" ResourceTitle="Description">
				<Field><ReboundUI:ReboundTextBox ID="txtServiceDescription" runat="server" Width="300" /></Field>
			</ReboundUI_Form:FormField>
			
			<ReboundUI_Form:FormField id="ctlManufacturer" runat="server" FieldID="lblManufacturer" ResourceTitle="Manufacturer">
				<Field><asp:Label ID="lblManufacturer" runat="server" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlDateCode" runat="server" FieldID="lblDateCode" ResourceTitle="DateCode">
				<Field><asp:Label ID="lblDateCode" runat="server" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlProduct" runat="server" FieldID="lblProduct" ResourceTitle="Product">
				<Field><asp:Label ID="lblProduct" runat="server" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlPackage" runat="server" FieldID="lblPackage" ResourceTitle="Package">
				<Field><asp:Label ID="lblPackage" runat="server" /></Field>
			</ReboundUI_Form:FormField>
			
			<ReboundUI_Form:FormField id="ctlQuantity" runat="server" FieldID="lblQuantity" ResourceTitle="Quantity" IsRequiredField="false">
				<Field><asp:Label ID="lblQuantity" runat="server" /></Field>
			<%--[0001] start--%>
				<%--<ReboundUI:ReboundTextBox ID="txtQuantity" runat="server" Width="100" TextBoxMode="Numeric" /></Field>--%>
			</ReboundUI_Form:FormField>
            
			<ReboundUI_Form:FormField id="ctlPrice" runat="server" FieldID="txtPrice" ResourceTitle="Price" IsRequiredField="false">
				<Field><ReboundUI:ReboundTextBox ID="txtPrice"  runat="server" TextBoxMode="currency" FormatDecimalPlaces="true" Width="100" />
<%--				<asp:Label ID="lblPrice" runat="server" />--%>&nbsp;<asp:Label ID="lblCurrency" runat="server" /></Field>
			</ReboundUI_Form:FormField>
			<%--[0001] start--%>
			<ReboundUI_Form:FormField id="ctlLineNotes" runat="server" FieldID="txtLineNotes" ResourceTitle="Notes">
				<Field><ReboundUI:ReboundTextBox ID="txtLineNotes" runat="server" Width="400" TextMode="multiLine" Rows="2" /></Field>
			</ReboundUI_Form:FormField>
             <ReboundUI_Form:FormField id="ctlPrintHazWar" runat="server" FieldID="chkPrintHazWar" ResourceTitle="PrintHazWarning" >
				<Field><ReboundUI:ImageCheckBox ID="chkPrintHazWar" runat="server" Enabled="true" /></Field>
			</ReboundUI_Form:FormField>
			
		</ReboundUI_Table:Form>
		
	</Content>
	
</ReboundUI_Form:DesignBase>
