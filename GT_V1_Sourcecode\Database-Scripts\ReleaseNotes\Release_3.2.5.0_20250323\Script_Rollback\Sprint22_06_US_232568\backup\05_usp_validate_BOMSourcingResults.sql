﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


/*   
===========================================================================================  
TASK			UPDATED BY      DATE			ACTION		DESCRIPTION  
[US-214760]		An.TranTan		13-Jan-2025		Create		Validate BOM Import Sourcing Results data with mapped column
[US-214760]		An.TranTan		16-Jan-2025		Update		Update validation rule and unmap value
[US-214759]		An.TranTan		17-Jan-2025		Update		Remove unnecessary LEFT JOIN cause duplicate data
[US-214759]		An.TranTan		20-Jan-2025		Update		Check requirement is released
===========================================================================================  
*/ 
CREATE OR ALTER   PROCEDURE [dbo].[usp_validate_BOMSourcingResults]
	@TargetColumns NVARCHAR(max),
    @SelectedColumns NVARCHAR(max),
	@UserId INT = 0
AS BEGIN
	SET NOCOUNT ON;
	--Variables
	
	--get temp data with dynamic columns
	IF OBJECT_ID('tempdb..#tbBOMSourcing_tempData') IS NOT NULL
		DROP TABLE #tbBOMSourcing_tempData
	CREATE TABLE #tbBOMSourcing_tempData
	(
		BOMSourcingDataId INT
		, CustomerRequirementNo INT
		, Manufacturer NVARCHAR(MAX)
		, SupplierPart NVARCHAR(MAX)
		, OfferedQuantity NVARCHAR(MAX)
		, SupplierCost NVARCHAR(MAX)
		, SPQ NVARCHAR(MAX)
		, MOQ NVARCHAR(MAX)
		, SupplierName NVARCHAR(MAX)
		, MSL NVARCHAR(MAX)
		, Notes NVARCHAR(MAX)
		, DateCode NVARCHAR(MAX)
		, QtyInStock NVARCHAR(MAX)
		, OfferStatus NVARCHAR(MAX)
		, BuyPrice NVARCHAR(MAX)
		, SellPrice NVARCHAR(MAX)
		, ShippingCost NVARCHAR(MAX)
		, Package NVARCHAR(MAX)
		, ROHS NVARCHAR(MAX)
		, Currency NVARCHAR(MAX)
		, FactorySealed NVARCHAR(MAX)
		, Region NVARCHAR(MAX)
		, LeadTime NVARCHAR(MAX)
		, LastTimeBuy NVARCHAR(MAX)
		, DeliveryDate NVARCHAR(MAX)
		, CustomerRefNo NVARCHAR(MAX)
		, LineNumber INT
		, OriginalFilename NVARCHAR(200)
		, GeneratedFilename NVARCHAR(200)
		, UnmappedMessage NVARCHAR(MAX) DEFAULT ''
		, ValidationMessage NVARCHAR(MAX) DEFAULT ''
	)

	--Get raw data with dynamic columns
	DECLARE @DynamicSQL NVARCHAR(MAX);
	SET @DynamicSQL = 'INSERT INTO #tbBOMSourcing_tempData(' + @TargetColumns  
					+		',BOMSourcingDataId,CustomerRequirementNo,LineNumber,OriginalFilename,GeneratedFilename) '
					+ 'SELECT ' + @SelectedColumns + ', BOMSourcingDataId, CustomerRequirementNo, LineNumber, OriginalFilename, GeneratedFilename '
					+ 'FROM BorisGlobalTraderImports.dbo.tbBOMSourcing_tempData '
					+ 'WHERE CreatedBy=' + CAST(@UserId AS NVARCHAR(10))
	EXEC (@DynamicSQL);

	/************ Validation input data ************/
	--check Customer Requirement released
	update temp
	set temp.ValidationMessage = 'This requirement has been released.<br/>'
	from #tbBOMSourcing_tempData temp
	join tbCustomerRequirement cr WITH(NOLOCK) on cr.CustomerRequirementId = temp.CustomerRequirementNo
	where ISNULL(cr.REQStatus,0) > 3 

	-- Check suppliername existed in GT client
	update temp
	set temp.ValidationMessage = temp.ValidationMessage + ' This supplier does not exist on the DMCC client, unable to import.<br/>'
	from #tbBOMSourcing_tempData temp
	where not exists (
		select top 1 1 from dbo.tbCompany co with (nolock)
		where co.Inactive = 0 
			AND co.ClientNo = 114
			AND co.CompanyName = temp.SupplierName
	)

	--check supplier part
	update temp
	set temp.ValidationMessage = temp.ValidationMessage + ' Supplier Part is required.<br/>'
	from #tbBOMSourcing_tempData temp
	where LEN(ISNULL(temp.SupplierPart,'')) = 0

	--check manufacturer
	update temp
	set temp.ValidationMessage = temp.ValidationMessage + ' This manufacturer does not exist or being inactive, unable to import.<br/>'
	from #tbBOMSourcing_tempData temp
	where not exists(
		select top 1 1 from tbManufacturer m with(nolock) 
		where m.ManufacturerName = temp.Manufacturer
		and m.Inactive = 0
	)

	--check quantity
	update #tbBOMSourcing_tempData
	set ValidationMessage = ValidationMessage + ' Offered Quantity is required and accept numberic values.<br/>'
	where TRY_PARSE(OfferedQuantity AS INT) IS NULL

	--CurrencyCode No length 3--                                                            
	UPDATE #tbBOMSourcing_tempData
	SET ValidationMessage = ValidationMessage + ' Currency code accepts 3 characters.<br/>'
	WHERE LEN(Currency) <> 3

	--requires Buy price
	UPDATE #tbBOMSourcing_tempData
	SET ValidationMessage = ValidationMessage + ' Buy Price is required and accept numeric values.<br/>'
	WHERE TRY_PARSE(BuyPrice AS FLOAT) IS NULL

	--requires Sell price
	UPDATE #tbBOMSourcing_tempData
	SET ValidationMessage = ValidationMessage + ' Sell Price is required and accept numeric values.<br/>'
	WHERE TRY_PARSE(SellPrice AS FLOAT) IS NULL

	 --- Validate delivery date format--                                                              
    update temp  
    set temp.ValidationMessage = case  
                                     when TRY_PARSE(temp.DeliveryDate AS DATE USING 'en-GB') IS NULL then  
                                         temp.ValidationMessage + ' Delivery Date is invalid. Accept formart: dd/mm/yyyy.<br/>'  
                                     else  
                                         temp.ValidationMessage  
                                 end  
    from #tbBOMSourcing_tempData temp 
	
	--- Validate Customer Ref No--
	UPDATE #tbBOMSourcing_tempData
	SET ValidationMessage = ValidationMessage + ' Customer Ref No. must be less than 200 characters.'
	WHERE LEN(CustomerRefNo) > 200

	/************ Validation end ************/
	IF NOT EXISTS(SELECT TOP 1 1 FROM #tbBOMSourcing_tempData WHERE ISNULL(ValidationMessage, '') <> '')
	BEGIN
		--get mfr from import file
		WITH cteManufacturer AS(
			SELECT DISTINCT Manufacturer FROM #tbBOMSourcing_tempData
		)SELECT 
			MIN(m.ManufacturerId) AS ManufacturerId
			,m.ManufacturerName
		INTO #tempImportedManufacturer
		FROM cteManufacturer cte
		JOIN tbManufacturer m WITH(NOLOCK) ON m.ManufacturerName = cte.Manufacturer
			AND m.Inactive = 0
		GROUP BY m.ManufacturerName;
		-------------------------------------------
		--get supplier from import file
		WITH cteSupplier AS(
			SELECT DISTINCT SupplierName FROM #tbBOMSourcing_tempData
		)SELECT
			MIN(co.CompanyId) AS SupplierId
			,co.CompanyName AS SupplierName
		INTO #tempImportedSupplier
		FROM cteSupplier cte
		JOIN tbCompany co WITH(NOLOCK) ON co.CompanyName = cte.SupplierName
		WHERE co.Inactive = 0 
			AND co.ClientNo = 114
		GROUP BY co.CompanyName;
		-------------------------------------------
		--get package from import file
		DECLARE @tbPackage TABLE(PackageId INT, Package NVARCHAR(256))
		;WITH ctePackage AS(
			SELECT DISTINCT Package FROM #tbBOMSourcing_tempData WHERE ISNULL(Package,'') <> ''
		)
		INSERT INTO @tbPackage (PackageId, Package)
		SELECT 
			p.PackageId, p.PackageName
		FROM tbPackage p WITH(NOLOCK)
			JOIN ctePackage cte ON cte.Package = p.PackageName
		WHERE p.Inactive = 0
		UNION ALL
		SELECT 
			p.PackageId, p.PackageDescription
		FROM tbPackage p WITH(NOLOCK)
			JOIN ctePackage cte ON cte.Package = p.PackageDescription
		WHERE p.Inactive = 0
		-------------------------------------------
		--get msl from import file
		DECLARE @tbMSLLevel TABLE(MSLLevelId INT, MSL NVARCHAR(200));
		;WITH cteMSL AS(
			SELECT DISTINCT MSL FROM #tbBOMSourcing_tempData WHERE ISNULL(MSL,'') <> ''
		)INSERT INTO @tbMSLLevel (MSLLevelId, MSL)
		SELECT 
			msl.MSLLevelId, cte.MSL
		FROM cteMSL cte
		JOIN tbMSLLevel msl WITH(NOLOCK) ON msl.MSLLevel = cte.MSL
		UNION ALL
		SELECT 
			msl.MSLLevelId, cte.MSL
		FROM cteMSL cte
		JOIN tbMSLLevel msl WITH(NOLOCK) ON msl.MSLLevel = CONCAT('MSL ', cte.MSL)
		-------------------------------------------

		/*============= Update unmap value to Notes field ===================*/
		--check shipping cost
		UPDATE #tbBOMSourcing_tempData
		SET UnmappedMessage = UnmappedMessage + 'Shipping Cost: ' + ShippingCost + ' | '
		WHERE ISNULL(ShippingCost,'') <> '' AND TRY_PARSE(ShippingCost AS FLOAT) IS NULL

		--check supplier cost
		UPDATE #tbBOMSourcing_tempData
		SET UnmappedMessage = UnmappedMessage + 'Supplier Cost: ' + SupplierCost + ' | '
		WHERE ISNULL(SupplierCost,'') <> '' AND TRY_PARSE(SupplierCost AS FLOAT) IS NULL

		--check quantity in stock
		UPDATE #tbBOMSourcing_tempData
		SET UnmappedMessage = UnmappedMessage + 'Qty in stock: ' + QtyInStock + ' | '
		WHERE ISNULL(QtyInStock,'') <> '' AND TRY_PARSE(QtyInStock AS INT) IS NULL

		--check offer status
		UPDATE #tbBOMSourcing_tempData
		SET UnmappedMessage = UnmappedMessage + 'Offer Status: ' + OfferStatus + ' | '
		WHERE ISNULL(OfferStatus,'') <> ''
			AND NOT EXISTS(
				SELECT TOP 1 1 FROM tbOfferStatus WITH(NOLOCK) WHERE [Name] = OfferStatus
			)

		--check region
		UPDATE #tbBOMSourcing_tempData
		SET UnmappedMessage = UnmappedMessage + 'Region: ' + Region + ' | '
		WHERE ISNULL(Region,'') <> ''
			AND NOT EXISTS(
				SELECT TOP 1 1 FROM tbRegion WITH(NOLOCK) WHERE RegionName = Region
			)

		--check package
		UPDATE t
		SET t.UnmappedMessage = t.UnmappedMessage + 'Package: ' + t.Package + ' | '
		FROM #tbBOMSourcing_tempData t
		WHERE ISNULL(t.Package,'') <> '' AND NOT EXISTS(
			SELECT TOP 1 1 FROM @tbPackage p WHERE p.Package = t.Package
		)

		--check MSL
		UPDATE t
		SET t.UnmappedMessage = t.UnmappedMessage + 'MSL: ' + t.MSL + ' | '
		FROM #tbBOMSourcing_tempData t
		WHERE ISNULL(t.MSL,'') <> '' AND NOT EXISTS(
			SELECT TOP 1 1 FROM @tbMSLLevel m WHERE m.MSL = t.MSL
		)

		UPDATE #tbBOMSourcing_tempData
		SET Notes = UnmappedMessage + '<br/>' + Notes
		WHERE UnmappedMessage <> ''
		/*===================================================================*/

		--clear previous temp data
		DELETE BorisGlobalTraderImports.dbo.tbBOMSourcing_ToBeImported WHERE CreatedBy = @UserId;

		INSERT INTO BorisGlobalTraderImports.dbo.tbBOMSourcing_ToBeImported
		(
			CustomerRequirementNo
			,SupplierNo
			,SupplierName
			,ManufacturerNo
			,ManufacturerName
			,SupplierPart
			,Quantity
			,SupplierCost
			,SPQ
			,MOQ
			,MSLLevelNo
			,MSL
			,Notes
			,DateCode
			,QtyInStock
			,OfferStatusNo
			,BuyPrice
			,SellPrice
			,ShippingCost
			,PackageNo
			,ROHSStatus
			,CurrencyNo
			,FactorySealed
			,RegionNo
			,LeadTime
			,LastTimeBuy
			,DeliveryDate
			,CustomerRefNo
			,OriginalFilename
			,GeneratedFilename
			,CreatedBy
			,DLUP
		)
		SELECT 
			temp.CustomerRequirementNo
			,s.SupplierId
			,s.SupplierName
			,m.ManufacturerId
			,m.ManufacturerName
			,temp.SupplierPart
			,temp.OfferedQuantity
			,ISNULL(TRY_PARSE(temp.SupplierCost AS FLOAT), 0)
			,temp.SPQ
			,temp.MOQ
			,(SELECT TOP 1 MSLLevelId FROM @tbMSLLevel m WHERE m.MSL = temp.MSL)
			,temp.MSL
			,temp.Notes
			,temp.DateCode
			,ISNULL(TRY_PARSE(temp.QtyInStock AS INT), 0)
			,os.OfferStatusId
			,ISNULL(TRY_PARSE(temp.BuyPrice AS FLOAT), 0)
			,ISNULL(TRY_PARSE(temp.SellPrice AS FLOAT), 0)
			,ISNULL(TRY_PARSE(temp.ShippingCost AS FLOAT), 0)
			,(SELECT TOP 1 PackageId FROM @tbPackage p WHERE p.Package = temp.Package)
			,case when ISNULL(temp.[ROHS], '') = 'Y' then 'Y' else 'N' end AS ROHSStatus
			,case when temp.Currency = ISNULL(pocr.CurrencyCode,'') then pocr.CurrencyId else null end
			,temp.FactorySealed
			,r.RegionId
			,temp.LeadTime
			,temp.LastTimeBuy
			,CONVERT(DATETIME, temp.DeliveryDate, 103)
			,temp.CustomerRefNo
			,temp.OriginalFilename
			,temp.GeneratedFilename
			,@UserId
			,GETDATE()
		FROM #tbBOMSourcing_tempData temp
		JOIN #tempImportedManufacturer m ON m.ManufacturerName = temp.Manufacturer
		JOIN #tempImportedSupplier s ON s.SupplierName = temp.SupplierName
		JOIN tbCompany c WITH(NOLOCK) on c.CompanyId = s.SupplierId
		LEFT JOIN tbCurrency pocr WITH(NOLOCK) on pocr.CurrencyId = c.POCurrencyNo
		LEFT JOIN tbOfferStatus os WITH(NOLOCK) on os.[Name] = temp.OfferStatus
		LEFT JOIN tbRegion r WITH(NOLOCK) on r.RegionName = temp.Region

		DROP TABLE #tempImportedSupplier;
		DROP TABLE #tempImportedManufacturer;
	END
	
	SELECT 
		LineNumber AS 'LINE NO.'
		,CustomerRefNo AS 'CUSTOMER REF NO.'
		,SupplierName AS 'SUPPLIER NAME'
		,SupplierPart AS 'SUPPLIER PART NO.'
		,SupplierCost AS 'Supplier Cost'
		,ROHS
		,Manufacturer AS MANUFACTURER
		,DateCode AS 'DATE CODE'
		,Package AS PACKAGE
		,OfferedQuantity AS 'OFFERED QTY.'
		,OfferStatus AS 'OFFER STATUS'
		,SPQ
		,FactorySealed AS 'FACTORY SEALED'
		,QtyInStock AS 'QTY IN STOCK'
		,MOQ
		,LastTimeBuy AS 'LAST TIME BUY'
		,Currency AS 'CURRENCY'
		,BuyPrice AS 'BUY PRICE'
		,SellPrice AS 'SELL PRICE'
		,ShippingCost AS 'SHIPPING COST'
		,LeadTime AS LEADTIME
		,Region AS REGION
		,DeliveryDate AS 'DELIVERY DATE'
		,Notes AS NOTES
		,MSL
		,OriginalFilename
		,ValidationMessage AS Reason  
	FROM #tbBOMSourcing_tempData
	WHERE ISNULL(ValidationMessage, '') <> ''
	ORDER BY LineNumber;
	--DROP temp tables
	DROP TABLE #tbBOMSourcing_tempData;
END
GO


