///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//[001]      A<PERSON><PERSON>     07-Aug-2018  [REB-12084]:Lock PO lines when EPR is authorised
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.ExportApprovalStatus_EditAll= function (element) {
    Rebound.GlobalTrader.Site.Controls.Forms.ExportApprovalStatus_EditAll.initializeBase(this, [element]);
    this._intPurchaseOrderID = 0;
    this._intLineID = -1;
    this._blnPartReceivedEdit = false;
    this._blnRestrictedEdit = false;
    this._intLineReceived = 0;
    this._intIPOClientNo = -1;
    this._blnClientPO = false;
    this._intGlobalClientNo = -1;
    this._blnProductHaza = false;
    //[001] start
    this._blnCanEditQty = true;
    this._blnCanEditPrice = true;
    this._TypeNo = "";
    this._IsSendToLineManager = false;
    this._IsSendToQuality = false;
    this._aryCurrentValues = "";
    //[001] end
};

Rebound.GlobalTrader.Site.Controls.Forms.ExportApprovalStatus_EditAll.prototype = {

    get_intPurchaseOrderID: function () { return this._intPurchaseOrderID; }, set_intPurchaseOrderID: function (v) { if (this._intPurchaseOrderID !== v) this._intPurchaseOrderID = v; },
    get_strTitleMessage: function () { return this._strTitleMessage; }, set_strTitleMessage: function (v) { if (this._strTitleMessage !== v) this._strTitleMessage = v; },
    get_lblCurrency: function () { return this._lblCurrency; }, set_lblCurrency: function (v) { if (this._lblCurrency !== v) this._lblCurrency = v; },
    get_lblTotalShipInCost: function () { return this._lblTotalShipInCost; }, set_lblTotalShipInCost: function (v) { if (this._lblTotalShipInCost !== v) this._lblTotalShipInCost = v; },

    initialize: function () {
        Rebound.GlobalTrader.Site.Controls.Forms.ExportApprovalStatus_EditAll.callBaseMethod(this, "initialize");
        this.addShown(Function.createDelegate(this, this.formShown));
     },

    dispose: function () {
        if (this.isDisposed) return;
        this._intPurchaseOrderID = null;
        this._strTitleMessage = null;
        if (this._ctlMail) this._ctlMail.dispose();
        this._intLineID = null;
        this._IsSendToLineManager = null;
        this._IsSendToQuality = null;
        this._blnPartReceivedEdit = null;
        this._blnRestrictedEdit = null;
        this._intLineReceived = null;
        this._lblCurrency = null;
        this._lblTotalShipInCost = null;
        this._intIPOClientNo = null;
        this._blnClientPO = null;
        this._aryCurrentValues = "";
        Rebound.GlobalTrader.Site.Controls.Forms.ExportApprovalStatus_EditAll.callBaseMethod(this, "dispose");
    },

    formShown: function () {
        if (this._blnFirstTimeShown) {

            this.addSave(Function.createDelegate(this, this.saveClicked));
           
        }
        this.getFieldDropDownData("ctlDestinationCountry");
        this.getFieldDropDownData("ctlMilitaryuse");

    },
   
   
    saveClicked: function () {
        if (!this.validateForm()) return;
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/ExportApprovalStatus");
        obj.set_DataObject("ExportApprovalStatus");
        obj.set_DataAction("SaveAllExportApprovalDetails");
        obj.addParameter("ExportApprovalIds", this._aryCurrentValues);

        obj.addParameter("DestinationCountryNo", this.getFieldValue("ctlDestinationCountry"));
        obj.addParameter("MilitaryuseNo", this.getFieldValue("ctlMilitaryuse"));
        obj.addParameter("EndUserText", this.getFieldValue("ctlEndUser"));
        obj.addDataOK(Function.createDelegate(this, this.saveEditOK));
        obj.addError(Function.createDelegate(this, this.saveEditError));
        obj.addTimeout(Function.createDelegate(this, this.saveEditError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    saveEditError: function (args) {
        this._strErrorMessage = args._errorMessage;
        this.onSaveError();
    },

    saveEditOK: function (args) {
        if (args._result.Result == true) {
            this.onSaveComplete();
            this.setFieldValue("ctlDestinationCountry", '');
            this.setFieldValue("ctlMilitaryuse", '');
            this.setFieldValue("ctlEndUser", '');
            $("#AllocationErrorMsg").hide();
            $("#dvtxt").html("");
        } else {
            this._strErrorMessage = args._errorMessage;
            this.onSaveError();
        }
    },

    validateForm: function () {
        this.onValidate();
        var blnOK = true;
        if (!this.checkFieldEntered("ctlDestinationCountry")) blnOK = false;
        if (!this.checkFieldEntered("ctlMilitaryuse")) blnOK = false;
        if (!this.checkFieldEntered("ctlEndUser")) blnOK = false;
        if (!blnOK) this.showError(true);
        return blnOK;

    }

};

Rebound.GlobalTrader.Site.Controls.Forms.ExportApprovalStatus_EditAll.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.ExportApprovalStatus_EditAll", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
