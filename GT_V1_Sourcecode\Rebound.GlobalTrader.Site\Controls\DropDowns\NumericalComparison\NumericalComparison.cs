using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site;

namespace Rebound.GlobalTrader.Site.Controls.DropDowns {
	public partial class NumericalComparison : Base {

		#region Overrides

		protected override void OnInit(EventArgs e) {
			ServerSideOnly = true;
			CanAddTo = false;
			GetData();
			base.OnInit(e);
		}

		#endregion

		/// <summary>
		/// Get Data
		/// </summary>
		internal override void GetData() {
			ClearDropDown();
			if (IncludeNoValue) AddNoValueToDropDown();

			AddToDropDown(Functions.GetGlobalResource("misc", NumericalComparisonType.EqualTo), ((int)NumericalComparisonType.EqualTo).ToString());
			AddToDropDown(Functions.GetGlobalResource("misc", NumericalComparisonType.LessThan), ((int)NumericalComparisonType.LessThan).ToString());
			AddToDropDown(Functions.GetGlobalResource("misc", NumericalComparisonType.LessThanOrEqualTo), ((int)NumericalComparisonType.LessThanOrEqualTo).ToString());
			AddToDropDown(Functions.GetGlobalResource("misc", NumericalComparisonType.GreaterThan), ((int)NumericalComparisonType.GreaterThan).ToString());
			AddToDropDown(Functions.GetGlobalResource("misc", NumericalComparisonType.GreaterThanOrEqualTo), ((int)NumericalComparisonType.GreaterThanOrEqualTo).ToString());
			base.GetData();
		}

		internal void MakeChildControls() {
			EnsureChildControls();
		}

		#region Enumerations

		/// <summary>
		/// Numerical Comparison Type
		/// *** MAKE SURE THIS ENUM IS COPIED TO JAVASCRIPT ***
		/// </summary>
		public enum NumericalComparisonType {
			EqualTo,
			GreaterThan,
			LessThan,
			GreaterThanOrEqualTo,
			LessThanOrEqualTo
		}

		#endregion
	}
}