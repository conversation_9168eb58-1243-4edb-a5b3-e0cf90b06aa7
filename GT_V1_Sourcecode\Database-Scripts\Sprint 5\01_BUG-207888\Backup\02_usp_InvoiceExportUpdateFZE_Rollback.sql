-- Drop the procedure if it already exists
IF OBJECT_ID('dbo.usp_InvoiceExportUpdateFZE', 'P') IS NOT NULL
BEGIN
    DROP PROCEDURE dbo.usp_InvoiceExportUpdateFZE;
END

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE PROCEDURE [dbo].[usp_InvoiceExportUpdateFZE]                                                                                                              

--************************************************************************
--* GA 18.05.2012:- SK's Update was updateting all clients & all Records
--*					where Exported = 1
--* SK 13.04.2010:
--* - new concept -
--*       invoices will be marked for export ([Exported] = 1
--*       on completion of export (i.e. this job) the DateExported is set
--*       invoices where this date is set will not be re-exported
--************************************************************************
AS
BEGIN
--* SK
--UPDATE	dbo.tbInvoice
--SET		DateExported	= getdate()
--WHERE	Exported		= 1
--AND		SupplierRMANo	Is Null
--*

--UPDATE	dbo.tbInvoice
--SET		DateExported	= getdate()
----Espire 04 June 20
--from tbInvoice iv join tbClient c on iv.ClientNo=c.ClientId
--WHERE	((Exported		= 1) or (isnull(iv.IsAutoInvoiceExport,1)=1 and isnull(c.IsAutoInvoiceExport,1)=1))
--AND		SupplierRMANo	Is Null
--AND     DateExported    is Null
--AND		ClientNo		IN (114)
--AND (iv.InvoiceDate > '2020-01-01')

--UPDATE	dbo.tbCredit
--SET		DateExported	= getdate()
--		,Exported		= 1     -- GA 01 Nov 2017
--		--Espire 04 June 20
--from tbCredit cr join tbClient c on cr.ClientNo=c.ClientId
--LEFT JOIN	dbo.tbInvoice		AS iv ON cr.InvoiceNo = iv.InvoiceId
--WHERE   cr.DateExported    is Null
--AND     ISNULL(cr.Exported,0)	= 0 -- GA 01 Nov 2017
--AND		cr.ClientNo		IN (114)
----Espire 04 June 20
-- and isnull(iv.IsAutoInvoiceExport,1)=1 and isnull(c.IsAutoInvoiceExport,1)=1

/*
UPDATE	dbo.tbCredit
SET		DateExported	= getdate()
		,Exported		= 1     -- GA 01 Nov 2017
WHERE   DateExported    is Null
AND     ISNULL(Exported,0)	= 0 -- GA 01 Nov 2017
AND		ClientNo		IN (114)

-- **Update Client Invoice table
UPDATE	dbo.tbClientInvoice
SET		DateExported	= getdate()
WHERE   DateExported    is Null
AND		ClientNo		IN (114)
AND		InvoiceClientNo NOT IN (116, 118)
*/

-- ** Update Client Invoice table
UPDATE	dbo.tbClientInvoice
SET		DateExported	= getdate()
WHERE   DateExported    is Null
AND		ClientNo		= 114
AND		ClientInvoiceId IN (SELECT ClientInvoiceId FROM vwInvoiceExportFZE_Int WHERE ISNULL([TaxCode], 0) > 0)

-- ** Update Credit table
UPDATE	dbo.tbCredit
SET		DateExported	= getdate()
		,Exported		= 1     -- GA 01 Nov 2017
WHERE	DateExported	is Null
AND		ClientNo		= 114
AND		CreditId IN (SELECT CreditId FROM vwInvoiceExportCreditFZE WHERE ISNULL([TaxCode], 0) > 0)
END
GO