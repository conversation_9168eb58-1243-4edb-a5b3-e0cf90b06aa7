///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.HomeNuggets");

Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyRecentlyShippedOrders = function(element) { 
	Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyRecentlyShippedOrders.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyRecentlyShippedOrders.prototype = {

	get_tblShipped: function() { return this._tblShipped; }, 	set_tblShipped: function(value) { if (this._tblShipped !== value)  this._tblShipped = value; }, 

	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyRecentlyShippedOrders.callBaseMethod(this, "initialize");
		this.addRefreshEvent(Function.createDelegate(this, this.getData));
	},

	dispose: function() { 
		if (this.isDisposed) return;
		if (this._tblShipped) this._tblShipped.dispose();
		this._tblShipped = null;
		Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyRecentlyShippedOrders.callBaseMethod(this, "dispose");
	},
	
	setupLoadingState: function() {
		this._tblShipped.show(false);
		Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyRecentlyShippedOrders.callBaseMethod(this, "setupLoadingState");
	},
	
	getData: function() {
		this.setupLoadingState();
		var obj = new Rebound.GlobalTrader.Site.Data();
		obj.set_PathToData('controls/HomeNuggets/MyRecentlyShippedOrders');
		obj.set_DataObject('MyRecentlyShippedOrders');
		obj.set_DataAction('GetData');
		obj.addParameter("rowcount", this._intRowCount);
		obj.addDataOK(Function.createDelegate(this, this.getDataComplete));
		obj.addError(Function.createDelegate(this, this.homeNuggetDataError));
		obj.addTimeout(Function.createDelegate(this, this.homeNuggetDataError));
		$R_DQ.addToQueue(obj);
		$R_DQ.processQueue();
		obj = null;
	},
	
	getDataComplete: function(args) {
		this.showNoneFoundOrContent(args._result.Count);
		var result = args._result;
		//shipped
		this._tblShipped.clearTable();
		this._tblShipped.show(result.Shipped.length > 0);
		for (var i = 0; i < result.Shipped.length; i++) {
			var row = result.Shipped[i];
			var aryData = [
				$RGT_nubButton_SalesOrder(row.ID, row.No),
				$RGT_nubButton_Company(row.CMNo, row.CM),
				$R_FN.writeDoubleCellValue(row.Due,row.DateShipped)
				];
			this._tblShipped.addRow(aryData, null);
		}
		this.hideLoading();
	}
};

Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyRecentlyShippedOrders.registerClass("Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyRecentlyShippedOrders", Rebound.GlobalTrader.Site.Controls.HomeNuggets.Base, Sys.IDisposable);
