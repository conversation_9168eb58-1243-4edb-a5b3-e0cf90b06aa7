Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.BOMCusReqSourcingResults_Confirm=function(n){Rebound.GlobalTrader.Site.Controls.Forms.BOMCusReqSourcingResults_Confirm.initializeBase(this,[n]);this._intRequirementLineID=-1;this._intBOMID=-1;this._CustReqNo=-1;this._intSourcingResultID=-1;this._BomCode=-1;this._BomName=null;this._BomCompanyName=null;this._BomCompanyNo=null;this._SalesManNo=null;this._SalesManName=null;this._ReqSalesman=-1;this._SupportTeamMemberNo=null;this._UnitBuyPrice=null;this._UnitSellPrice=null;this._UnitBuyPriceWithCurrany=null;this._UnitSellPriceWithCurrany=null};Rebound.GlobalTrader.Site.Controls.Forms.BOMCusReqSourcingResults_Confirm.prototype={get_BomCode:function(){return this._BomCode},set_BomCode:function(n){this._BomCode!==n&&(this._BomCode=n)},get_BomName:function(){return this._BomName},set_BomName:function(n){this._BomName!==n&&(this._BomName=n)},get_BomCompanyName:function(){return this._BomCompanyName},set_BomCompanyName:function(n){this._BomCompanyName!==n&&(this._BomCompanyName=n)},get_BomCompanyNo:function(){return this._BomCompanyNo},set_BomCompanyNo:function(n){this._BomCompanyNo!==n&&(this._BomCompanyNo=n)},get_SalesManNo:function(){return this._SalesManNo},set_SalesManNo:function(n){this._SalesManNo!==n&&(this._SalesManNo=n)},get_SalesManName:function(){return this._SalesManName},set_SalesManName:function(n){this._SalesManName!==n&&(this._SalesManName=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Forms.BOMCusReqSourcingResults_Confirm.callBaseMethod(this,"initialize");this.addShown(Function.createDelegate(this,this.formShown))},dispose:function(){this.isDisposed||(this._ctlConfirm&&this._ctlConfirm.dispose(),this._ctlConfirm=null,this._intRequirementLineID=null,this._ReqSalesman=null,this._SupportTeamMemberNo=null,this._UnitBuyPrice=null,this._UnitSellPrice=null,this._UnitBuyPriceWithCurrany=null,this._UnitSellPriceWithCurrany=null,Rebound.GlobalTrader.Site.Controls.Forms.BOMCusReqSourcingResults_Confirm.callBaseMethod(this,"dispose"))},formShown:function(){var n=null;this._blnFirstTimeShown?(this._UnitBuyPrice!=null&&this._UnitSellPrice!=null?this._UnitBuyPrice>=this._UnitSellPrice?(this.showField("ctlBuyUnitSellPriceDiff_Label",!0),$("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlConfirm_ctlDB_ctlBuyUnitSellPriceDiff_Label_tdItem").show(),this._UnitBuyPrice>this._UnitSellPrice&&(n="The buy price ("+this._UnitBuyPriceWithCurrany+") of selected part is greater then the sell price ("+this._UnitSellPriceWithCurrany+") "),this._UnitBuyPrice==this._UnitSellPrice&&(n="The buy price ("+this._UnitBuyPriceWithCurrany+") of selected part is equal to the sell price ("+this._UnitSellPriceWithCurrany+")"),$("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlConfirm_ctlDB_ctlBuyUnitSellPriceDiff_Label_tdItem").text(n)):(this.showField("ctlBuyUnitSellPriceDiff_Label",!1),$("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlConfirm_ctlDB_ctlBuyUnitSellPriceDiff_Label_tdItem").hide()):(this.showField("ctlBuyUnitSellPriceDiff_Label",!1),$("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlConfirm_ctlDB_ctlBuyUnitSellPriceDiff_Label_tdItem").hide()),this._ctlConfirm=this.getFieldComponent("ctlConfirm"),this._ctlConfirm.addClickYesEvent(Function.createDelegate(this,this.yesClicked)),this._ctlConfirm.addClickNoEvent(Function.createDelegate(this,this.noClicked))):this._UnitBuyPrice!=null&&this._UnitSellPrice!=null?this._UnitBuyPrice>=this._UnitSellPrice?(this.showField("ctlBuyUnitSellPriceDiff_Label",!0),$("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlConfirm_ctlDB_ctlBuyUnitSellPriceDiff_Label_tdItem").show(),this._UnitBuyPrice>this._UnitSellPrice&&(n="The buy price ("+this._UnitBuyPriceWithCurrany+") of selected part is greater then the sell price ("+this._UnitSellPriceWithCurrany+") "),this._UnitBuyPrice==this._UnitSellPrice&&(n="The buy price ("+this._UnitBuyPriceWithCurrany+") of selected part is equal to the sell price ("+this._UnitSellPriceWithCurrany+")"),$("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlConfirm_ctlDB_ctlBuyUnitSellPriceDiff_Label_tdItem").text(n)):(this.showField("ctlBuyUnitSellPriceDiff_Label",!1),$("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlConfirm_ctlDB_ctlBuyUnitSellPriceDiff_Label_tdItem").hide()):(this.showField("ctlBuyUnitSellPriceDiff_Label",!1),$("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlConfirm_ctlDB_ctlBuyUnitSellPriceDiff_Label_tdItem").hide())},yesClicked:function(){this.showSaving(!0);var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("Controls/Nuggets/BOMCusReqSourcingResults");n.set_DataObject("BOMCusReqSourcingResults");n.set_DataAction("ReleaseSourcing");n.addParameter("id",this._intRequirementLineID);n.addParameter("BomId",this._intBOMID);n.addParameter("BomCode",this._BomCode);n.addParameter("BomName",this._BomName);n.addParameter("BomCompanyName",this._BomCompanyName);n.addParameter("BomCompanyNo",this._BomCompanyNo);n.addParameter("SalesManName",this._SalesManName);n.addParameter("SalesManNo",this._SalesManNo);n.addParameter("CustReqNo",this._CustReqNo);n.addParameter("SID",this._intSourcingResultID);n.addParameter("Reqsalesman",this._ReqSalesman);n.addParameter("SupportTeamMemberNo",this._SupportTeamMemberNo);n.addDataOK(Function.createDelegate(this,this.saveComplete));n.addError(Function.createDelegate(this,this.saveError));n.addTimeout(Function.createDelegate(this,this.saveError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},noClicked:function(){this.showSaving(!1);this.onNotConfirmed()},saveError:function(n){this.showSaving(!1);this._strErrorMessage=n._errorMessage;this.onSaveError()},saveComplete:function(n){this.showSaving(!1);n._result.Result?this.onSaveComplete():(this._strErrorMessage=n._errorMessage,this.onSaveError())}};Rebound.GlobalTrader.Site.Controls.Forms.BOMCusReqSourcingResults_Confirm.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.BOMCusReqSourcingResults_Confirm",Rebound.GlobalTrader.Site.Controls.Forms.Base,Sys.IDisposable);