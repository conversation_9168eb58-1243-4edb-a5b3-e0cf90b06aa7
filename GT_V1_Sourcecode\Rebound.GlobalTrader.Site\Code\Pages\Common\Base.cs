using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;
using Rebound.GlobalTrader.Site;
using Rebound.GlobalTrader.BLL;
using System.Globalization;
using System.Threading;
using Microsoft.Owin.Security;
using Microsoft.Owin.Security.Cookies;
using Microsoft.Owin.Security.OpenIdConnect;
// [001] Soorya Vyas   02/05/2023   RP-1017 NullReferenceException  
namespace Rebound.GlobalTrader.Site.Pages
{
    public class Base : System.Web.UI.Page
    {

        protected List<string> _lstEmbeddedCSS = new List<string>();
        protected Site _objSite = Rebound.GlobalTrader.Site.Site.GetInstance();
        protected SitePage _objSitePage;
        protected QueryStringManager _objQSManager = new QueryStringManager(HttpContext.Current.Request.QueryString);
        protected bool _blnConfigurationIsDebug = false;
        protected bool _blnLoggedIn = false;

        #region Properties

        public string Filename
        {
            get
            {
                string str = Request.ServerVariables["PATH_INFO"];
                int iPos = str.LastIndexOf("/");
                return str.Substring(iPos + 1, str.Length - iPos - 1);
            }
        }

        public string FullURLWithQueryString
        {
            get { return string.Format("{0}{1}", this.AppRelativeVirtualPath.Replace("~/", ""), Request.Url.Query); }
        }

        /// <summary>
        /// Title text
        /// </summary>
        private string _strTitleText;
        public string TitleText
        {
            get { return _strTitleText; }
            set { _strTitleText = value; }
        }

        #endregion

        #region Overrides

        protected override void OnPreInit(EventArgs e)
        {
            _blnConfigurationIsDebug = false;
#if (DEBUG)
            _blnConfigurationIsDebug = true;
#endif
            SetupQueryStringManager();
            DisablePageCaching();
            MaintainScrollPositionOnPostBack = true;
            EnableViewState = false;
            base.OnPreInit(e);
        }

        protected override void OnInit(EventArgs e)
        {
            if (Master is Masters.Base) ((Masters.Base)Master).ConfigurationIsDebug = _blnConfigurationIsDebug;
            TitleText = Functions.GetGlobalResource("PageTitles", _objSitePage.Name);
            base.OnInit(e);
        }

        protected override void InitializeCulture()
        {
            SetupCulture(this);
            base.InitializeCulture();
        }

        protected override void OnPreRender(EventArgs e)
        {
            SetPageTitlebar(TitleText);
            base.OnPreRender(e);
        }

        protected override void Render(HtmlTextWriter writer)
        {
            AddBrowserSpecificCSS();
            base.Render(writer);
        }

        #endregion

        #region Methods

        protected void SetupQueryStringManager()
        {
            _objQSManager = new QueryStringManager(Request.QueryString);
        }

        public void AddCSSFile(string strFile)
        {
            try  //[001] applied try catch
            {
                if (!_lstEmbeddedCSS.Contains(strFile))
                {

                    Literal lit = new Literal();
                    string strTheme = Page.Theme;
                    if (string.IsNullOrEmpty(strTheme)) strTheme = "Original";
                    lit.Text = string.Format(@"<link rel=""stylesheet"" text=""text/css"" href=""{0}"" />", string.Format("css/Controls/{0}/{1}", strTheme, strFile));
                    this.Header.Controls.Add(lit);
                    strFile = null;
                    _lstEmbeddedCSS.Add(strFile);
                }
            }
            catch (Exception ex)
            {
                var log = ex.Message;
            }
        }

        protected void SetPageType(string strSitePage)
        {
            _objSitePage = _objSite.GetPage(strSitePage);
        }

        protected void SetPageType(Enum enmPage)
        {
            _objSitePage = _objSite.GetPage(enmPage);
        }

        public void NotLoggedIn(string strRedirectURL)
        {
            HttpContext.Current.Response.Clear();
            string strURL = _objSite.GetPage("Logout").Url;
            if (!String.IsNullOrEmpty(strRedirectURL)) strURL += string.Format("?{0}={1}", QueryStringManager.GetVariableQSName(Rebound.GlobalTrader.Site.QueryStringManager.QueryStringVariables.ReturnURL), HttpContext.Current.Server.UrlEncode(strRedirectURL));
            HttpContext.Current.Response.Redirect(strURL, true);
        }

        public void NotLoggedInNew(string strRedirectURL)
        {
            HttpContext.Current.Response.Clear();
            //string strURL = _objSite.GetPage("Logout").Url;
            // if (!String.IsNullOrEmpty(strRedirectURL)) strURL += string.Format("?{0}={1}", QueryStringManager.GetVariableQSName(Rebound.GlobalTrader.Site.QueryStringManager.QueryStringVariables.ReturnURL), HttpContext.Current.Server.UrlEncode(strRedirectURL));
            HttpContext.Current.Response.Redirect("ADLogin.aspx");
        }

        public void NotADLoggedIn()
        {
            //Wait for 2 second, in case of cookies write issues
            // System.Threading.Thread.Sleep(3000);

            var userClaims = User.Identity as System.Security.Claims.ClaimsIdentity;
            string strUserEmail = userClaims?.FindFirst("preferred_username")?.Value;

            //if (!Request.IsAuthenticated)
            //{
            //    Response.Redirect("ADLogin.aspx");

            //}
            if (string.IsNullOrEmpty(strUserEmail))
            {
                Response.Redirect("ADLogin.aspx");

            }
            // else if(!SessionManager.IsLoggedIn && Request.IsAuthenticated)
            else if (!string.IsNullOrEmpty(strUserEmail))
            {
                try
                {
                    //LoginManager.LogUserOut();
                    // SessionManager.EndSession();

                    BLL.Login login = null;
                    int intReturn = 0;
                    string strServerIP = (Request != null) ? Request.ServerVariables["LOCAL_ADDR"] : "";
                    login = LoginManager.LogMasterUserIntoSystem(userClaims?.FindFirst("preferred_username")?.Value, Request.UserHostAddress, Session.SessionID, out intReturn, strServerIP);
                    if (login != null)
                    {
                        var reqUrlFromV2= (Request != null) ? Request.QueryString["redirectUri"] : "";
                        if (!string.IsNullOrEmpty(reqUrlFromV2))
                        {
                            Response.Redirect(reqUrlFromV2);
                        }
                        else
                        {
                            Response.Redirect("Default.aspx", false);
                        } 
                    }
                    else
                    {
                        Response.Redirect("ADLogout.aspx");
                    }
                }
                catch (Exception)
                {
                    Response.Redirect("ADLogin.aspx");
                }
            }


            //_hidAD.Value = userClaims?.FindFirst("name")?.Value + ":  " + userClaims?.FindFirst("preferred_username")?.Value;


            //}
        }

        /// <summary>
        /// Sets the page titlebar text
        /// </summary>
        public void SetPageTitlebar(SitePage objSitePage)
        {
            SetPageTitlebar(Functions.GetGlobalResource("PageTitles", objSitePage.Name));
        }

        /// <summary>
        /// Sets the page titlebar text
        /// </summary>
        public void SetPageTitlebar(string strPageTitle)
        {
            Page.Title = string.Format("{0} - {1}", Functions.GetGlobalResource("Misc", "AppTitle"), strPageTitle);
        }

        /// <summary>
        /// Add Script Reference
        /// </summary>
        protected void AddScriptReference(string strAssembly, string strRef)
        {
            ScriptManager sm = ScriptManager.GetCurrent(this);
            if (sm != null)
            {
                sm.Scripts.Add(Functions.GetScriptReference(_blnConfigurationIsDebug, strAssembly, strRef));
            }
        }
        internal void AddScriptReference(string strRef)
        {
            AddScriptReference("Rebound.GlobalTrader.Site", strRef);
        }

        public static void SetupCulture(System.Web.UI.Page objPage)
        {
            string strCulture = "en-GB";
            strCulture = SessionManager.Culture;
            objPage.UICulture = strCulture;
            objPage.Culture = strCulture;
            Thread.CurrentThread.CurrentUICulture = new CultureInfo(strCulture);
            Thread.CurrentThread.CurrentCulture = CultureInfo.CreateSpecificCulture(strCulture);
        }

        private void DisablePageCaching()
        {
            HttpContext.Current.Response.Cache.SetExpires(DateTime.UtcNow.AddDays(-1));
            HttpContext.Current.Response.Cache.SetValidUntilExpires(false);
            HttpContext.Current.Response.Cache.SetRevalidation(HttpCacheRevalidation.AllCaches);
            HttpContext.Current.Response.Cache.SetCacheability(HttpCacheability.NoCache);
            HttpContext.Current.Response.Cache.SetNoStore();
        }

        private void AddBrowserSpecificCSS()
        {

            try //[001] applied try catch
            {
                switch (Request.Browser.Browser.ToUpper())
                {
                    case "SAFARI":
                    case "APPLEMAC-SAFARI":
                        if (Request.Browser.MajorVersion >= 5) AddCSSFile("_Chrome.css");
                        break;
                }
            }
            catch (Exception ex)
            {
                var log = ex.Message;
            }

        }
        #endregion

    }
}
