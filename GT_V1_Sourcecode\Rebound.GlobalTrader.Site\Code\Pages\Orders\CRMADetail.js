Type.registerNamespace("Rebound.GlobalTrader.Site.Pages.Orders");Rebound.GlobalTrader.Site.Pages.Orders.CRMADetail=function(n){Rebound.GlobalTrader.Site.Pages.Orders.CRMADetail.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Pages.Orders.CRMADetail.prototype={get_intCRMAID:function(){return this._intCRMAID},set_intCRMAID:function(n){this._intCRMAID!==n&&(this._intCRMAID=n)},get_ctlMainInfo:function(){return this._ctlMainInfo},set_ctlMainInfo:function(n){this._ctlMainInfo!==n&&(this._ctlMainInfo=n)},get_ctlLines:function(){return this._ctlLines},set_ctlLines:function(n){this._ctlLines!==n&&(this._ctlLines=n)},get_btnPrint:function(){return this._btnPrint},set_btnPrint:function(n){this._btnPrint!==n&&(this._btnPrint=n)},get_ctlCRMADocuments:function(){return this._ctlCRMADocuments},set_ctlCRMADocuments:function(n){this._ctlCRMADocuments!==n&&(this._ctlCRMADocuments=n)},get_ctlCRMAPDFDragDrop:function(){return this._ctlCRMAPDFDragDrop},set_ctlCRMAPDFDragDrop:function(n){this._ctlCRMAPDFDragDrop!==n&&(this._ctlCRMAPDFDragDrop=n)},initialize:function(){Rebound.GlobalTrader.Site.Pages.Orders.CRMADetail.callBaseMethod(this,"initialize")},goInit:function(){this._btnPrint&&this._btnPrint.addPrint(Function.createDelegate(this,this.printCRMA));this._btnPrint&&this._btnPrint.addEmail(Function.createDelegate(this,this.emailCRMA));this._ctlMainInfo&&this._ctlMainInfo.addGetDataComplete(Function.createDelegate(this,this.ctlMainInfo_GetDataComplete));this._ctlMainInfo&&this.setLineFieldsFromHeader();this._ctlCRMADocuments&&this._ctlCRMADocuments.getData();this._ctlCRMAPDFDragDrop&&this._ctlCRMAPDFDragDrop.getData();this._btnPrint&&this._btnPrint.addExtraButtonClick(Function.createDelegate(this,this.printOtherDocs));Rebound.GlobalTrader.Site.Pages.Orders.CRMADetail.callBaseMethod(this,"goInit")},dispose:function(){this.isDisposed||(this._ctlMainInfo&&this._ctlMainInfo.dispose(),this._ctlLines&&this._ctlLines.dispose(),this._btnPrint&&this._btnPrint.dispose(),this._ctlCRMADocuments&&this._ctlCRMADocuments.dispose(),this._ctlCRMADocuments=null,this._btnPrint=null,this._ctlMainInfo=null,this._ctlLines=null,this._intCRMAID=null,Rebound.GlobalTrader.Site.Pages.Orders.CRMADetail.callBaseMethod(this,"dispose"))},printCRMA:function(){$R_FN.openPrintWindow($R_ENUM$PrintObject.CustomerRMA,this._intCRMAID)},emailCRMA:function(){$R_FN.openPrintWindow($R_ENUM$PrintObject.CustomerRMA,this._intCRMAID,!0)},printOtherDocs:function(){this._btnPrint._strExtraButtonClickCommand=="PrintLog"&&$R_FN.openPrintLogWindow($R_ENUM$PrintObject.PrintLog,this._intCRMAID,!1,"CustomerRMA")},ctlMainInfo_GetDataComplete:function(){this.setLineFieldsFromHeader()},setLineFieldsFromHeader:function(){var n=this._ctlMainInfo.getFieldValue("hidCustomer"),t=this._ctlMainInfo.getFieldValue("hidNo");this._ctlLines._frmAdd&&this._ctlLines._frmAdd.setFieldsFromHeader(t,n);this._ctlLines._frmEdit&&this._ctlLines._frmEdit.setFieldsFromHeader(t,n);this._ctlLines._frmDelete&&this._ctlLines._frmDelete.setFieldsFromHeader(t,n);this._ctlLines._frmClose&&this._ctlLines._frmClose.setFieldsFromHeader(t,n);this._ctlLines._frmDeallocate&&this._ctlLines._frmDeallocate.setFieldsFromHeader(t,n);n=null;t=null}};Rebound.GlobalTrader.Site.Pages.Orders.CRMADetail.registerClass("Rebound.GlobalTrader.Site.Pages.Orders.CRMADetail",Rebound.GlobalTrader.Site.Pages.Content,Sys.IDisposable);