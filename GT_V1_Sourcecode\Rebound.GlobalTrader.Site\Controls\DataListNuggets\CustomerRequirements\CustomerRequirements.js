Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DataListNuggets");Rebound.GlobalTrader.Site.Controls.DataListNuggets.CustomerRequirements=function(n){Rebound.GlobalTrader.Site.Controls.DataListNuggets.CustomerRequirements.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.DataListNuggets.CustomerRequirements.prototype={get_table:function(){return this._table},get_table:function(n){this._table!==n&&(this._table=n)},get_intSalesPersonID:function(){return this._intSalesPersonID},set_intSalesPersonID:function(n){this._intSalesPersonID!==n&&(this._intSalesPersonID=n)},get_IsGSA:function(){return this._IsGSA},set_IsGSA:function(n){this._IsGSA!==n&&(this._IsGSA=n)},initialize:function(){this.addSetupDataCallEvent(Function.createDelegate(this,this.setupDataCall));this.addGetDataOKEvent(Function.createDelegate(this,this.getDataOK));this.addInitCompleteEvent(Function.createDelegate(this,this.initAfterBaseIsReady));this._strPathToData="controls/DataListNuggets/CustomerRequirements";this._strDataObject="CustomerRequirements";Rebound.GlobalTrader.Site.Controls.DataListNuggets.CustomerRequirements.callBaseMethod(this,"initialize")},initAfterBaseIsReady:function(){this.addPageTabChangedEvent(Function.createDelegate(this,this.pageTabChanged));this.applySalesPersonFilter();this.updateFilterVisibility();this.getData()},dispose:function(){this.isDisposed||(this._intSalesPersonID=null,this._IsGSA=null,Rebound.GlobalTrader.Site.Controls.DataListNuggets.CustomerRequirements.callBaseMethod(this,"dispose"))},pageTabChanged:function(){this._table._intCurrentPage=1;this._enmViewLevel=this._intCurrentTab;this.updateFilterVisibility();this.getData()},setupDataCall:function(){this._objData.addParameter("ViewLevel",this._enmViewLevel)},getDataOK:function(){var t,i;for(this._objResult.Results.length==0?HideKubIcon():HideKubIcon(),t=0,i=this._objResult.Results.length;t<i;t++){var n=this._objResult.Results[t],u={StrPartNo:n.Part},r=[$RGT_nubButton_CustomerRequirement(n.ID,n.No),$R_FN.writeDoubleCellValue($R_FN.writePartNo(n.Part,n.ROHS),$RGT_nubButton_Manufacturer(n.MfrNo,n.Mfr)),n.Quantity,$R_FN.writeDoubleCellValue(n.blnMakeYellow==!0?'<span style="background-color:yellow;">'+$RGT_nubButton_Company(n.CMNo,n.CM)+"<\/span>":$RGT_nubButton_Company(n.CMNo,n.CM),$RGT_nubButton_Contact(n.ContactNo,n.Contact)),$R_FN.writeDoubleCellValue(n.Salesman,n.IndustryName),$R_FN.writeDoubleCellValue(n.Received,n.Promised),$R_FN.writeDoubleCellValue($RGT_nubButton_BOM(n.BOMNo,n.BOMName),$R_FN.setCleanTextValue(n.BomStatus)),$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(n.TotalValue),$R_FN.setCleanTextValue(n.TotalBase))];this._table.addRow(r,n.ID,!1,u);r=null;n=null}},updateFilterVisibility:function(){this.getFilterField("ctlSalesman").show(this._enmViewLevel!=0);this.getFilterField("ctlClientName").show(this._IsGSA)},applySalesPersonFilter:function(){this._intSalesPersonID&&this._intSalesPersonID>0&&this.getFilterField("ctlSalesman").setValue(this._intSalesPersonID)}};Rebound.GlobalTrader.Site.Controls.DataListNuggets.CustomerRequirements.registerClass("Rebound.GlobalTrader.Site.Controls.DataListNuggets.CustomerRequirements",Rebound.GlobalTrader.Site.Controls.DataListNuggets.Base,Sys.IDisposable);