<%@ Control Language="C#" CodeBehind="CompanyApiCustomer.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyApiCustomer" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>

<ReboundUI_Nugget:DesignBase ID="ctlDB" runat="server" TitleText="Lines" BoxType="Standard" >

	<Links>
		<ReboundUI:IconButton ID="ibtnAdd" runat="server" IconButtonMode="hyperlink" IconGroup="Nugget" IconTitleResource="Add" IconCSSType="Add" IsInitiallyEnabled="false" />
		
		<ReboundUI:IconButton ID="ibtnEdit" runat="server" IconButtonMode="hyperlink" IconGroup="Nugget" IconTitleResource="Edit" IconCSSType="Edit" IsInitiallyEnabled="false" />
		
		<ReboundUI:IconButton ID="ibtnBomMapping" runat="server" IconButtonMode="hyperlink" IconGroup="Nugget" IconTitleResource="BomMapping" IconCSSType="Edit" />
		<ReboundUI:IconButton ID="ibtnSupplierImport" runat="server" IconButtonMode="hyperlink" IconGroup="Nugget" IconTitleResource="SupplierMapping" IconCSSType="Edit" />
	</Links>
	
	<Content>
	
		<ReboundUI:FlexiDataTable ID="tbl" runat="server" AllowSelection="true" PanelHeight="150" />
		
	</Content>
	
	<Forms>
		<ReboundForm:CompanyApiCustomer_Add id="frmAdd" runat="server" />
		<ReboundForm:CompanyApiCustomer_Edit id="frmEdit" runat="server" />
		<ReboundForm:CompanyApiCustomer_MappingAdd  id="frmBomMapping" runat="server" />
		<ReboundForm:SupplierImport_Import  id="frmSupplierImport" runat="server" />
	</Forms>
	
</ReboundUI_Nugget:DesignBase>
