﻿/*
 * Action: Altered                                          Action By: <PERSON><PERSON><PERSON><PERSON>                                               Dated:26-10-2021
 * Comment: Make changes in the Supplier Approval Tab column header.
 */
using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.BLL;
using Rebound.GlobalTrader.Site.Controls;
using Rebound.GlobalTrader.Site;

namespace Rebound.GlobalTrader.Site.Controls.Nuggets
{
    public partial class ExportApprovalStatus : Base
    {

        #region Locals

        //protected Panel _pnlSummary;
        private TabStrip _ctlTabs;
        protected Tab _ctlTabApproved;
        protected FlexiDataTable _tblApproved;
        protected Tab _ctlTabAll;
        protected FlexiDataTable _tblAll;
        protected Tab _ctlTabAwaiting;
        protected FlexiDataTable _tblAwaiting;
        protected FieldSet _fldAllocations;
        protected FlexiDataTable _tblAllocations;
        protected IconButton _ibtnApproval;
        protected IconButton _ibtnEditApproval;
        protected IconButton _ibtnEditAllApproval;
        protected IconButton _ibtnAdd;
        protected IconButton _ibtnRequestApproval;

        protected IconButton _ibtnUnpostAll;
        protected IconButton _ibtnDeallocate;
        protected IconButton _ibtnAddExpditeNote;
        protected FieldSet _fldReceived;
        protected FlexiDataTable _tblReceived;
        protected Label _lblLineNumber;
        protected Label _lblLineInactive;
        protected Panel _pnlLineDetail;
        protected Panel _pnlLoadingLineDetail;
        protected Panel _pnlLineDetailError;
        protected HyperLink _hypPrev;
        protected HyperLink _hypNext;
        private bool _blnDisableAllButtons = false;
        protected Controls.Forms.POLines_Add _frmPOLines_Add;
        //[002] start
        protected IconButton _ibtnEPR;
        protected Panel _pnlEPRTootTip;
        protected Panel _pnlEPR;
        protected HyperLink _hypNewEPR;
        protected IconButton _ibtnRelease;
        protected MultiSelectionCount _ctlMultiSelectionCount;
        //[002] end

        #endregion

        #region Properties

        private int _intSalesOrderID = -1;
        public int SalesOrderID
        {
            get { return _intSalesOrderID; }
            set { _intSalesOrderID = value; }
        }

        private bool _blnCanAdd = true;
        public bool CanAdd
        {
            get { return _blnCanAdd; }
            set { _blnCanAdd = value; }
        }

        private bool _blnCanAddNewLine = true;
        public bool CanAddNewLine
        {
            get { return _blnCanAddNewLine; }
            set { _blnCanAddNewLine = value; }
        }

        private bool _blnCanAddPOReq = true;
        public bool CanAddPOReq
        {
            get { return _blnCanAddPOReq; }
            set { _blnCanAddPOReq = value; }
        }

        private bool _blnCanAddPO = true;
        public bool CanAddPO
        {
            get { return _blnCanAddPO; }
            set { _blnCanAddPO = value; }
        }

        private bool _blnCanAddStock = true;
        public bool CanAddStock
        {
            get { return _blnCanAddStock; }
            set { _blnCanAddStock = value; }
        }

        private bool _blnCanAddReq = true;
        public bool CanAddReq
        {
            get { return _blnCanAddReq; }
            set { _blnCanAddReq = value; }
        }

        private bool _blnCanEdit = true;
        public bool CanEdit
        {
            get { return _blnCanEdit; }
            set { _blnCanEdit = value; }
        }

        private bool _blnCanPost = true;
        public bool CanPost
        {
            get { return _blnCanPost; }
            set { _blnCanPost = value; }
        }

        private bool _blnCanUnpost = true;
        public bool CanUnpost
        {
            get { return _blnCanUnpost; }
            set { _blnCanUnpost = value; }
        }

        private bool _blnCanDelete = true;
        public bool CanDelete
        {
            get { return _blnCanDelete; }
            set { _blnCanDelete = value; }
        }

        private bool _blnCanDeallocate = true;
        public bool CanDeallocate
        {
            get { return _blnCanDeallocate; }
            set { _blnCanDeallocate = value; }
        }

        private bool _blnCanClose = true;
        public bool CanClose
        {
            get { return _blnCanClose; }
            set { _blnCanClose = value; }
        }

        private bool _blnCanEditPriceWithoutUnpost = true;
        public bool CanEditPriceWithoutUnpost
        {
            get { return _blnCanEditPriceWithoutUnpost; }
            set { _blnCanEditPriceWithoutUnpost = value; }
        }


        private PurchaseOrderStatus.List _enmPOStatus;
        public PurchaseOrderStatus.List POStatus
        {
            get { return _enmPOStatus; }
            set
            {
                _enmPOStatus = value;
                _blnDisableAllButtons = ((int)_enmPOStatus >= (int)PurchaseOrderStatus.List.Received);
            }
        }

        private bool _blnPOClosed;
        public bool POClosed
        {
            get { return _blnPOClosed; }
            set { _blnPOClosed = value; }
        }
        //[001] code start
        private int _intPurchaseOrderLineID;
        public int PurchaseOrderLineID
        {
            get { return _intPurchaseOrderLineID; }
            set { _intPurchaseOrderLineID = value; }
        }

        private bool _isFromIPO = false;
        public bool IsFromIPO
        {
            get { return _isFromIPO; }
            set { _isFromIPO = value; }
        }

        private int _purchaseOrderNumber = 0;
        public int PurchaseOrderNumber
        {
            get { return _purchaseOrderNumber; }
            set { _purchaseOrderNumber = value; }
        }
        //[001] code end

        //[002] start
        private bool _blnCanRelease = true;
        public bool CanRelease
        {
            get { return _blnCanRelease; }
            set { _blnCanRelease = value; }
        }

        private bool _blnCanUnRelease = true;
        public bool CanUnRelease
        {
            get { return _blnCanUnRelease; }
            set { _blnCanUnRelease = value; }
        }
        //[002] end
        private bool _blnCanApprovalRequest = true;
        public bool ApprovalRequest
        {
            get { return _blnCanApprovalRequest; }
            set { _blnCanApprovalRequest = value; }
        }
        private bool _blnCanSendRequest = true;
        public bool SendRequest
        {
            get { return _blnCanSendRequest; }
            set { _blnCanSendRequest = value; }
        }
        #endregion

        #region Overrides

        /// <summary>
        /// Oninit
        /// </summary>
        /// <param name="e"></param>
        protected override void OnInit(EventArgs e)
        {
            base.OnInit(e);
            WireUpControls();
            AddScriptReference("Controls.Nuggets.ExportApprovalStatus.ExportApprovalStatus.js");
            TitleText = Functions.GetGlobalResource("Nuggets", "ExportApprovalStatus");
            if (_objQSManager.SalesOrderID > 0) _intSalesOrderID = _objQSManager.SalesOrderID;
            //[001] code start
            if (_objQSManager.PurchaseOrderLineID > 0) _intPurchaseOrderLineID = _objQSManager.PurchaseOrderLineID;
            //[001] code end

        }

        /// <summary>
        /// OnPreRender
        /// </summary>
        /// <param name="e"></param>
        protected override void OnPreRender(EventArgs e)
        {
            _ibtnApproval.Visible = _blnCanApprovalRequest;
            _ibtnRequestApproval.Visible = _blnCanSendRequest;
            _ibtnEditApproval.Visible = _blnCanEdit;
            _ibtnEditAllApproval.Visible = _blnCanEdit;
            _frmPOLines_Add.IsFromIPO = this.IsFromIPO;
            _frmPOLines_Add.PurchaseOrderNumber = this.PurchaseOrderNumber;
            SetupScriptDescriptors();

            base.OnPreRender(e);
        }

        /// <summary>
        /// OnLoad
        /// </summary>
        /// <param name="e"></param>
        protected override void OnLoad(EventArgs e)
        {
            base.OnLoad(e);
            _frmPOLines_Add.CanAddNewLine = _blnCanAddNewLine;
            _frmPOLines_Add.CanAddPOReq = _blnCanAddPOReq;
            _frmPOLines_Add.CanAddPO = _blnCanAddPO;
            _frmPOLines_Add.CanAddStock = _blnCanAddStock;
            _frmPOLines_Add.CanAddReq = _blnCanAddReq;
            SetupTables();
        }

        #endregion

        private void SetupTables()
        {
            Unit untQuantity = WidthManager.GetWidth(WidthManager.ColumnWidth.Quantity);
            Unit untPrice = WidthManager.GetWidth(WidthManager.ColumnWidth.CurrencyValue);
            Unit untPart = WidthManager.GetWidth(WidthManager.ColumnWidth.PartNo);
            Unit untManufacturer = WidthManager.GetWidth(WidthManager.ColumnWidth.ManufacturerCode);
            Unit untProduct = WidthManager.GetWidth(WidthManager.ColumnWidth.Product);
            Unit untDate = WidthManager.GetWidth(WidthManager.ColumnWidth.Date);
            Unit untDocumentNo = WidthManager.GetWidth(WidthManager.ColumnWidth.SystemDocumentNumber);


            //_tblAll.Columns.Add(new FlexiDataColumn("Select", Unit.Pixel(35), false, HorizontalAlign.Center));
            _tblAll.Columns.Add(new FlexiDataColumn("LineNo", WidthManager.GetWidth(WidthManager.ColumnWidth.LineNo)));
            _tblAll.Columns.Add(new FlexiDataColumn("PartNo", "CustomerPart"));
            _tblAll.Columns.Add(new FlexiDataColumn("ManufacturerAbbreviation", "DateCode", WidthManager.GetWidth(WidthManager.ColumnWidth.ManufacturerCode)));
            _tblAll.Columns.Add(new FlexiDataColumn("ExportApprovalStatus", WidthManager.GetWidth(WidthManager.ColumnWidth.ClientName)));
            _tblAll.Columns.Add(new FlexiDataColumn("OgelLicReq", WidthManager.GetWidth(WidthManager.ColumnWidth.ManufacturerCode)));
            _tblAll.Columns.Add(new FlexiDataColumn("EUUFormReq", WidthManager.GetWidth(WidthManager.ColumnWidth.ManufacturerCode)));
            _tblAll.Columns.Add(new FlexiDataColumn("Date", WidthManager.GetWidth(WidthManager.ColumnWidth.Date)));
            _tblAll.Columns.Add(new FlexiDataColumn("By", WidthManager.GetWidth(WidthManager.ColumnWidth.ClientName)));
            _tblAll.Columns.Add(new FlexiDataColumn("Comment"));

            //_tblApproved.Columns.Add(new FlexiDataColumn("Select", Unit.Pixel(35), false, HorizontalAlign.Center));
            _tblApproved.Columns.Add(new FlexiDataColumn("LineNo", WidthManager.GetWidth(WidthManager.ColumnWidth.LineNo)));
            _tblApproved.Columns.Add(new FlexiDataColumn("PartNo", "CustomerPart"));
            _tblApproved.Columns.Add(new FlexiDataColumn("ManufacturerAbbreviation", "DateCode", WidthManager.GetWidth(WidthManager.ColumnWidth.ManufacturerCode)));
            _tblApproved.Columns.Add(new FlexiDataColumn("ExportApprovalStatus", WidthManager.GetWidth(WidthManager.ColumnWidth.ClientName)));
            _tblApproved.Columns.Add(new FlexiDataColumn("OgelLicReq", WidthManager.GetWidth(WidthManager.ColumnWidth.ManufacturerCode)));
            _tblApproved.Columns.Add(new FlexiDataColumn("EUUFormReq", WidthManager.GetWidth(WidthManager.ColumnWidth.ManufacturerCode)));
            _tblApproved.Columns.Add(new FlexiDataColumn("Date", WidthManager.GetWidth(WidthManager.ColumnWidth.Date)));
            _tblApproved.Columns.Add(new FlexiDataColumn("By", WidthManager.GetWidth(WidthManager.ColumnWidth.ClientName)));
            _tblApproved.Columns.Add(new FlexiDataColumn("Comment"));

            //_tblAwaiting.Columns.Add(new FlexiDataColumn("Select", Unit.Pixel(35), false, HorizontalAlign.Center));
            _tblAwaiting.Columns.Add(new FlexiDataColumn("LineNo", WidthManager.GetWidth(WidthManager.ColumnWidth.LineNo)));
            _tblAwaiting.Columns.Add(new FlexiDataColumn("PartNo", "CustomerPart"));
            _tblAwaiting.Columns.Add(new FlexiDataColumn("ManufacturerAbbreviation", "DateCode", WidthManager.GetWidth(WidthManager.ColumnWidth.ManufacturerCode)));
            _tblAwaiting.Columns.Add(new FlexiDataColumn("ExportApprovalStatus", WidthManager.GetWidth(WidthManager.ColumnWidth.ClientName)));
            _tblAwaiting.Columns.Add(new FlexiDataColumn("OgelLicReq", WidthManager.GetWidth(WidthManager.ColumnWidth.ManufacturerCode)));
            _tblAwaiting.Columns.Add(new FlexiDataColumn("EUUFormReq", WidthManager.GetWidth(WidthManager.ColumnWidth.ManufacturerCode)));
            _tblAwaiting.Columns.Add(new FlexiDataColumn("Date", WidthManager.GetWidth(WidthManager.ColumnWidth.Date)));
            _tblAwaiting.Columns.Add(new FlexiDataColumn("By", WidthManager.GetWidth(WidthManager.ColumnWidth.ClientName)));
            _tblAwaiting.Columns.Add(new FlexiDataColumn("Comment"));


            _tblAllocations.Columns.Add(new FlexiDataColumn("PartNo", "CustomerPart", untPart));
            _tblAllocations.Columns.Add(new FlexiDataColumn("Customer", "CustomerPO", untQuantity));
            _tblAllocations.Columns.Add(new FlexiDataColumn("SalesOrderSerialNo", "DatePromised", untDate));
            _tblAllocations.Columns.Add(new FlexiDataColumn("SRMA", "ReturnDate", untDate));
            _tblAllocations.Columns.Add(new FlexiDataColumn("Quantity", "Price"));
            _tblAllocations.Columns.Add(new FlexiDataColumn("DateCode", "DeliveryDate", untDate));
            _tblAllocations.Columns.Add(new FlexiDataColumn("MarginPercentage", "MarginValue"));

            _tblReceived.Columns.Add(new FlexiDataColumn("GoodsInNo", untDocumentNo));
            _tblReceived.Columns.Add(new FlexiDataColumn("PartNo", "SupplierPart", untPart));
            _tblReceived.Columns.Add(new FlexiDataColumn("ManufacturerAbbreviation", "DateCode", untManufacturer));
            _tblReceived.Columns.Add(new FlexiDataColumn("Product", "Package", untProduct));
            _tblReceived.Columns.Add(new FlexiDataColumn("QuantityReceived", "LandedCost", untQuantity));
            _tblReceived.Columns.Add(new FlexiDataColumn("Location", "ReceivedDate"));
        }

        /// <summary>
        /// Sets up AJAX script descriptors
        /// </summary>
        private void SetupScriptDescriptors()
        {
            _scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.Nuggets.ExportApprovalStatus", ctlDesignBase.ClientID);
            if (_blnCanEdit) _scScriptControlDescriptor.AddElementProperty("ibtnEditApproval", _ibtnEditApproval.ClientID);
            if (_blnCanEdit) _scScriptControlDescriptor.AddElementProperty("ibtnEditAllApproval", _ibtnEditAllApproval.ClientID);
            if (_blnCanApprovalRequest) _scScriptControlDescriptor.AddElementProperty("ibtnApproval", _ibtnApproval.ClientID);
            
            if (_blnCanSendRequest) _scScriptControlDescriptor.AddElementProperty("ibtnRequestApproval", _ibtnRequestApproval.ClientID);
            _scScriptControlDescriptor.AddComponentProperty("ctlMultiSelectionCount", _ctlMultiSelectionCount.ClientID);
            _scScriptControlDescriptor.AddProperty("intSalesOrderID", _intSalesOrderID);
            _scScriptControlDescriptor.AddComponentProperty("ctlTabStrip", _ctlTabs.ClientID);
            _scScriptControlDescriptor.AddComponentProperty("tblApproved", _tblApproved.ClientID);
            _scScriptControlDescriptor.AddComponentProperty("tblAwaiting", _tblAwaiting.ClientID);
            _scScriptControlDescriptor.AddComponentProperty("tblAll", _tblAll.ClientID);
            _scScriptControlDescriptor.AddElementProperty("hypPrev", _hypPrev.ClientID);
            _scScriptControlDescriptor.AddElementProperty("hypNext", _hypNext.ClientID);
            _scScriptControlDescriptor.AddElementProperty("lblLineNumber", _lblLineNumber.ClientID);
            _scScriptControlDescriptor.AddElementProperty("lblLineInactive", _lblLineInactive.ClientID);
            _scScriptControlDescriptor.AddElementProperty("pnlLineDetail", _pnlLineDetail.ClientID);
            _scScriptControlDescriptor.AddElementProperty("pnlLoadingLineDetail", _pnlLoadingLineDetail.ClientID);
            _scScriptControlDescriptor.AddElementProperty("pnlLineDetailError", _pnlLineDetailError.ClientID);
            _scScriptControlDescriptor.AddComponentProperty("fldAllocations", _fldAllocations.ClientID);
            _scScriptControlDescriptor.AddComponentProperty("tblAllocations", _tblAllocations.ClientID);
            _scScriptControlDescriptor.AddComponentProperty("fldReceived", _fldReceived.ClientID);
            _scScriptControlDescriptor.AddComponentProperty("tblReceived", _tblReceived.ClientID);
            _scScriptControlDescriptor.AddProperty("enmPOStatus", (int)_enmPOStatus);
            _scScriptControlDescriptor.AddProperty("blnPOClosed", _blnPOClosed);
            _scScriptControlDescriptor.AddProperty("blnDisableAllButtons", _blnDisableAllButtons);
            //[001] code start
            _scScriptControlDescriptor.AddProperty("intPurchaseOrderLineID", _intPurchaseOrderLineID);
            //[001] code end
            _scScriptControlDescriptor.AddProperty("blnCanEditPriceWithoutUnpost", _blnCanEditPriceWithoutUnpost);

            //[002] code start



            //[002] code end

        }

        private void WireUpControls()
        {
            _ibtnEditApproval = FindIconButton("ibtnEditApproval");
            _ibtnEditAllApproval = FindIconButton("ibtnEditAllApproval");
            _ibtnAdd = FindIconButton("ibtnAdd");
            _ibtnApproval = FindIconButton("ibtnApproval");

            _ibtnRequestApproval = FindIconButton("ibtnRequestApproval");
            _ctlMultiSelectionCount = (MultiSelectionCount)ctlDesignBase.FindLinksControl("ctlMultiSelectionCount");

            _ibtnUnpostAll = FindIconButton("ibtnUnpostAll");

            _ctlTabs = (TabStrip)ctlDesignBase.FindContentControl("ctlTabs");
            _ctlTabs.CreateControls();
            _ctlTabApproved = (Tab)ctlDesignBase.FindContentControl("ctlTabApproved");
            _tblApproved = (FlexiDataTable)_ctlTabApproved.FindContentControl("tblApproved");

            _ctlTabAwaiting = (Tab)ctlDesignBase.FindContentControl("ctlTabAwaiting");
            _tblAwaiting = (FlexiDataTable)_ctlTabAwaiting.FindContentControl("tblAwaiting");

            _ctlTabAll = (Tab)ctlDesignBase.FindContentControl("ctlTabAll");
            _tblAll = (FlexiDataTable)_ctlTabAll.FindContentControl("tblAll");

            _hypPrev = (HyperLink)Functions.FindControlRecursive(this, "hypPrev");
            _hypNext = (HyperLink)Functions.FindControlRecursive(this, "hypNext");
            _lblLineNumber = (Label)Functions.FindControlRecursive(this, "lblLineNumber");
            _lblLineInactive = (Label)FindContentControl("lblLineInactive");
            _pnlLineDetail = (Panel)Functions.FindControlRecursive(this, "pnlLineDetail");
            _pnlLoadingLineDetail = (Panel)Functions.FindControlRecursive(this, "pnlLoadingLineDetail");
            _pnlLineDetailError = (Panel)Functions.FindControlRecursive(this, "pnlLineDetailError");
            _fldAllocations = (FieldSet)Functions.FindControlRecursive(this, "fldAllocations");
            _tblAllocations = (FlexiDataTable)_fldAllocations.FindContentControl("tblAllocations");
            _ibtnDeallocate = (IconButton)_fldAllocations.FindButtonControl("ibtnDeallocate");
            _fldReceived = (FieldSet)Functions.FindControlRecursive(this, "fldReceived");
            _tblReceived = (FlexiDataTable)_fldReceived.FindContentControl("tblReceived");
            _frmPOLines_Add = (Forms.POLines_Add)FindFormControl("frmAdd");
            //[002] start

            //[002] end

        }
    }
}