using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site.Controls;

namespace Rebound.GlobalTrader.Site.Controls.ItemSearch {
	public partial class PurchaseOrderLines : Base {

		protected override void OnInit(EventArgs e) {
			base.OnInit(e);
			SetItemSearchType("PurchaseOrderLines");
			AddScriptReference("Controls.ItemSearch.PurchaseOrderLines.PurchaseOrderLines.js");
		}

		protected override void OnLoad(EventArgs e) {
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.ItemSearch.PurchaseOrderLines", ctlDesignBase.ClientID);
			base.OnLoad(e);
		}
	
		protected override void OnPreRender(EventArgs e) {
			ctlDesignBase.MakeChildControls();
			ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("PurchaseOrder", WidthManager.GetWidth(WidthManager.ColumnWidth.SystemDocumentNumber), true));
			ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("Company", Unit.Empty, true));
			ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("PartNo", WidthManager.GetWidth(WidthManager.ColumnWidth.PartNo), true));
			ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("DateOrdered", WidthManager.GetWidth(WidthManager.ColumnWidth.Date), true));
			ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("Price", WidthManager.GetWidth(WidthManager.ColumnWidth.CurrencyValue), true));
			ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("DateDelivered", WidthManager.GetWidth(WidthManager.ColumnWidth.Date), true));
			base.OnPreRender(e);
		}
	}
}