Type.registerNamespace("Rebound.GlobalTrader.Site.Pages.Warehouse");Rebound.GlobalTrader.Site.Pages.Warehouse.ShortShipmentDetails=function(n){Rebound.GlobalTrader.Site.Pages.Warehouse.ShortShipmentDetails.initializeBase(this,[n]);this._blnIsApproved=!1};Rebound.GlobalTrader.Site.Pages.Warehouse.ShortShipmentDetails.prototype={get_intShortShipmentID:function(){return this._intShortShipmentID},set_intShortShipmentID:function(n){this._intShortShipmentID!==n&&(this._intShortShipmentID=n)},initialize:function(){Rebound.GlobalTrader.Site.Pages.Warehouse.ShortShipmentDetails.callBaseMethod(this,"initialize")},goInit:function(){Rebound.GlobalTrader.Site.Pages.Warehouse.ShortShipmentDetails.callBaseMethod(this,"goInit")},dispose:function(){this.isDisposed||(this._ctlMainInfo&&this._ctlMainInfo.dispose(),this._intShortShipmentID=null,Rebound.GlobalTrader.Site.Pages.Warehouse.ShortShipmentDetails.callBaseMethod(this,"dispose"))},ctlMainInfo_SaveEditComplete:function(){},ctlMainInfo_GetDataComplete:function(){var n,t,i;for(this.setLineFieldsFromHeader(),this._ctlLines._intGlobalClientNo=this._IsGlobalLogin==!0?this._ctlMainInfo.getFieldValue("hidGlobalClientNo"):null,this._ctlMainInfo._IsGlobalLogin=this._IsGlobalLogin,$R_FN.setInnerHTML(this._lblStatus,this._ctlMainInfo.getFieldValue("hidStatus")),this._ctlLines.updateStatus(this._ctlMainInfo.getFieldValue("hidStatusNo")),this._ctlLines._ipoClientNo=this._ctlMainInfo._ipoClientNo,this._btnPrint&&$R_FN.showElement(this._btnPrint._element,this._ctlMainInfo._blnIsApproved),this._ctlLines.enableDisableAddButton(this._ctlMainInfo._isIPO),this._ctlLines._PONumber=this._ctlMainInfo._PONumber,n="",t=0;t<this._ctlMainInfo._POLineEPRIds.length;t++)i=this._ctlMainInfo._POLineEPRIds[t],n+=$RGT_nubButton_EPR(this._ctlMainInfo._intPurchaseOrderID,i.POLineEPRIds,this._ctlMainInfo._PONumber);$R_FN.setInnerHTML(this._ctlLines._pnlEPR,"");$R_FN.setInnerHTML(this._ctlLines._pnlEPR,n);$R_FN.showElement(this._ctlLines._pnlEPR,n.length>0);this.CompanyStatus()}};Rebound.GlobalTrader.Site.Pages.Warehouse.ShortShipmentDetails.registerClass("Rebound.GlobalTrader.Site.Pages.Warehouse.ShortShipmentDetails",Rebound.GlobalTrader.Site.Pages.Content,Sys.IDisposable);