///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//Code Merge for GI Screen
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.HomeNuggets");

Rebound.GlobalTrader.Site.Controls.HomeNuggets.PowerBIActivity = function (element) {
    Rebound.GlobalTrader.Site.Controls.HomeNuggets.PowerBIActivity.initializeBase(this, [element]);
    this._tblPowerBIActivity = null;
};

Rebound.GlobalTrader.Site.Controls.HomeNuggets.PowerBIActivity.prototype = {

    get_tblPowerBIActivity: function () { return this._tblPowerBIActivity; }, set_tblPowerBIActivity: function (v) { if (this._tblPowerBIActivity !== v) this._tblPowerBIActivity = v; },

    initialize: function () {
        Rebound.GlobalTrader.Site.Controls.HomeNuggets.PowerBIActivity.callBaseMethod(this, "initialize");
        this.addRefreshEvent(Function.createDelegate(this, this.getData));
    },

    dispose: function () {
        if (this.isDisposed) return;
        if (this._tblPowerBIActivity) this._tblPowerBIActivity.dispose();
        this._tblPowerBIActivity = null;
        Rebound.GlobalTrader.Site.Controls.HomeNuggets.PowerBIActivity.callBaseMethod(this, "dispose");
    },

    setupLoadingState: function () {
        this._tblPowerBIActivity.show(false);
        Rebound.GlobalTrader.Site.Controls.HomeNuggets.PowerBIActivity.callBaseMethod(this, "setupLoadingState");
    },

    getData: function () {
        this.setupLoadingState();
        this._tblPowerBIActivity.clearTable();
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/HomeNuggets/PowerBIActivity");
        obj.set_DataObject("PowerBIActivity");
        obj.set_DataAction("GetData");
        obj._intTimeOutMiliseconds = 1;
        obj.addParameter("OtherLoginID", this._intLoginID_Other);
        obj.addParameter("rowcount", this._intRowCount);
        obj.addDataOK(Function.createDelegate(this, this.getDataComplete));
        obj.addError(Function.createDelegate(this, this.homeNuggetDataError));
        obj.addTimeout(Function.createDelegate(this, this.homeNuggetDataError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    getDataComplete: function (args) {
        var result = args._result;
        this.showNoneFoundOrContent(result.Count);
        for (var i = 0; i < result.Items.length; i++) {
            var row = result.Items[i];
            var aryData = [
                $R_FN.setCleanTextValue(row.LoginName),
                $R_FN.setCleanTextValue(row.LastVisited),
                $R_FN.setCleanTextValue(row.ReportName)
            ];
            this._tblPowerBIActivity.addRow(aryData, null);
            row = null;
        }
        this._tblPowerBIActivity.show(result.Count > 0);
        this.hideLoading();
    }
};

Rebound.GlobalTrader.Site.Controls.HomeNuggets.PowerBIActivity.registerClass("Rebound.GlobalTrader.Site.Controls.HomeNuggets.PowerBIActivity", Rebound.GlobalTrader.Site.Controls.HomeNuggets.Base, Sys.IDisposable);
