﻿
GO

/*
=========================================================================================================================================================
TASK			UPDATED BY       DATE				ACTION		DESCRIPTION  
[US-223401]     Phuc Hoang		 20-Feb-2025		CREATE		IPO - Bulk Offer Import tool - Apply fuzzy matching and rationalize data before importing
=========================================================================================================================================================
*/

IF OBJECT_ID('BorisGlobalTraderimports.dbo.tbOfferImportByExcelTemp_bk2025_03_20', 'U') IS NULL
BEGIN
	SELECT offerTemp.* 
	INTO [BorisGlobalTraderimports].[dbo].[tbOfferImportByExcelTemp_bk2025_03_20]
	FROM [BorisGlobalTraderimports].[dbo].[tbOfferImportByExcelTemp] offerTemp
		JOIN [BorisGlobalTraderImports].[dbo].[tbHUBOfferImportLargeFile] offerFile
			ON offerTemp.HUBOfferImportLargeFileID = offerFile.ID
	WHERE offerFile.[status] = 'Awaiting Data Correction' AND (ISNULL(offerTemp.[SPQ], '') = '' OR ISNULL(offerTemp.[MOQ], '') = '');
END


GO

UPDATE offerTemp
SET offerTemp.[SPQ] = CASE 
             WHEN ISNULL(offerTemp.[SPQ], '') = '' THEN '0'
             ELSE offerTemp.[SPQ]
          END,
	offerTemp.[MOQ] = CASE 
             WHEN ISNULL(offerTemp.[MOQ], '') = '' THEN '0'
             ELSE offerTemp.[MOQ]
          END
		  
FROM [BorisGlobalTraderimports].[dbo].[tbOfferImportByExcelTemp] offerTemp
	JOIN [BorisGlobalTraderImports].[dbo].[tbHUBOfferImportLargeFile] offerFile
		ON offerTemp.HUBOfferImportLargeFileID = offerFile.ID
WHERE offerFile.[status] = 'Awaiting Data Correction' AND (ISNULL(offerTemp.[SPQ], '') = '' OR ISNULL(offerTemp.[MOQ], '') = '');


