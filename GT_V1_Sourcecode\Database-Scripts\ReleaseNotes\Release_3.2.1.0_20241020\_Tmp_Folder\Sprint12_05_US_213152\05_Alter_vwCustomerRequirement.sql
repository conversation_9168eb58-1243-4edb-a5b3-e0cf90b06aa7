﻿CREATE OR ALTER VIEW [dbo].[vwCustomerRequirement]                                                                      
--************************************************************************************                                                                                        
--* SK 16.02.2010:                                                                                        
--* - add new column - DivisionName                                                                                        
--*                                                                                         
--* SK 20.01.2010:                                                                                        
--* - allow for new columns - PartWatch, BOM, BOMName                                                                                        
--*                                                                                         
--* SK 13.08.2009:                                                                                        
--* - Add DisplayStatus for use on form + extend "select *" to column list                                                                                        
--*                                                                                         
--* SK 27.07.2009:                                                                                        
--* - Include reason for closure                                                                                        
--*                                                                                         
--* RP 29.05.2009:                                                                                        
--* - Added Company OnStop flag         
    
-- Marker  Date   Owner   Remars    
--[005]   29-08-2023  Ravi Bhushan RP-2227 (AS6081) CounterFeit Electronic Part
/*
===========================================================================================  
TASK			UPDATED BY			DATE			ACTION		DESCRIPTION  
[US-213152]		Trung Pham Van		09-Oct-2024		UPDATE		Get IHSAveragePrice
===========================================================================================  
*/
--************************************************************************************                                                                                        
AS                                                                        
SELECT                                                                        
 cr.CustomerRequirementId                                                                        
 ,cr.CustomerRequirementNumber                                                                        
 ,cr.ClientNo                                                                        
 ,REPLACE(cr.FullPart, '"', '') AS FullPart                                                                        
 ,REPLACE(cr.Part, '"', '') AS Part                                                                        
 ,cr.ManufacturerNo                                                                        
 ,cr.DateCode                                                                        
 ,cr.PackageNo                                                                        
 ,cr.Quantity                                                                        
 ,cr.Price                                                                        
 ,cr.CurrencyNo                                                                        
 ,cr.ReceivedDate                                                                        
 ,cr.Salesman                                                                        
 ,cr.DatePromised                                                                        
 ,cr.Notes                                                                        
 ,cr.Instructions                                                                        
 ,cr.Shortage                                                                        
 ,cr.CompanyNo                                                                        
 ,cr.ContactNo   
 ,cr.Alternate                                                                        
 ,cr.OriginalCustomerRequirementNo                                                                        
 ,cr.ReasonNo                                                                    
 ,cr.ProductNo                                                                
 ,cr.CustomerPart                                                           
 ,cr.Closed                                            
 ,cr.ROHS                       
 ,cr.UpdatedBy                                                                        
 ,cr.DLUP                                                                      
 ,cr.UsageNo                                     
 ,cr.FactorySealed                                                                  
 ,cr.MSL                                                         
 ,cr.PartialQuantityAcceptable                            
 ,cr.Obsolete                                                              
 ,cr.LastTimeBuy                                  
 ,cr.RefirbsAcceptable                                                               
 ,cr.TestingRequired                                              
 ,cr.TargetSellPrice                                                               
 ,cr.CompetitorBestOffer                                                              
 ,cr.CustomerDecisionDate                                                                
 ,cr.RFQClosingDate                                                               
 ,cr.QuoteValidityRequired                                                               
 ,cr.ReqType                                                             
 ,cr.OrderToPlace                                                               
 ,cr.ReqForTraceability                                                 
 ,CASE cr.Closed                                                                        
  WHEN 0 THEN 'Open'                                                                        
 ELSE 'Closed'                                                                        
 END AS DisplayStatus                                                                        
 ,lg.EmployeeName AS SalesmanName                                                                        
 ,lg.DivisionNo                                                                        
 ,lg.TeamNo                                                                        
 ,co.CompanyName                                                                        
 ,co.OnStop AS CompanyOnStop                                                                        
 ,cn.ContactName                                                  
 ,cu.CurrencyCode                                                                        
 ,cu.CurrencyDescription                                                                        
 ,pr.ProductName                                                   
 ,pr.ProductDescription                                                                        
 ,mf.ManufacturerName                                                                        
 ,mf.ManufacturerCode                                                
 ,pk.PackageName                                                                        
 ,pk.PackageDescription                                                                        
 ,us.Name AS UsageName                                                                        
 ,(cr.Price * cr.Quantity) AS CustomerRequirementValue                                                                        
 ,re.Name AS ClosedReason                                                                        
 ,cr.PartWatch                                                                        
 ,cr.bom                                                              
 ,cr.BOMName     
 ,dv.DivisionName                                                                        
 ,ct.IsTraceability                                                                        
,bom.BOMName AS BOMHeader               
 ,cr.BOMNo                                                                        
 ,cr.POHubReleaseBy                                                                        
 ,bom.RequestToPOHubBy                                                                        
 ,bom.BOMCode                                                                        
 ,bom.BOMName AS BOMFullName                                                              
 ,bom.CurrencyNo AS BOMCurrencyNo                                                                        
 ,bom.DLUP AS BOMDate                                                                        
 ,CASE bom.[Status]                                                                               
     WHEN  1 THEN 'NEW'                                                              
     WHEN  2 THEN 'OPEN'                                                                              
     WHEN  3 THEN 'RPQ'                                                                              
     WHEN  4 THEN 'PARTIAL RELEASED'                                                                              
   WHEN  5 THEN 'RELEASED'                                                                     
   WHEN  6 THEN 'CLOSED'                                                                          
   END                                                                   
   as BOMStatus                                                                       
   ,c.ClientName                                                               
   ,cr.EAU                                               
   , (SELECT TOP 1 sr.SourcingResultId FROM tbSourcingResult sr               
    WHERE (sr.CustomerRequirementNo=cr.CustomerRequirementId) AND (sr.SourcingTable='PQ' OR sr.SourcingTable='OFPH' OR sr.SourcingTable='EXPH')) AS SourcingResultNo                                        
   , bom.ClientCurrencyNo                                                            
   , cu.GlobalCurrencyNo as ReqGlobalCurrencyNo                                                            
   ,cr.IsNoBid                                                          
   , co.SOCurrencyNo                                                          
   ,cr.NoBidNotes                                                          
   ,cr.AlternativesAccepted                                                          
   ,cr.RepeatBusiness                                                          
   , isnull(pr.Inactive,0) as ProductInactive                                                          
   , pr.DutyCode                                                          
   , pr.IsHazardous                                
   , pr.IsOrderViaIPOonly                              
   , cr.AlternateStatus                                             
   ,cr.SupportTeamMemberNo                                                         
   , stm.EmployeeName AS SupportTeamMemberName                                                      
    ,coo.GlobalCountryName  as CountryOfOrigin                                                      
 ,cr.CountryOfOriginNo                                                      
 ,cr.LifeCycleStage                                              
 ,cr.HTSCode                                                      
 ,cr.AveragePrice                                                      
 ,cr.Packing                                                      
 ,cr.PackagingSize                                                      
 ,cr.Descriptions                                                  
 ,cr.ihsCurrencyCode                                              
 ,cr.IHSProduct    
 ,cr.IHSPartsNo  
 ,(select top 1 cast(ISNULL(dbo.ufn_get_productdutyrate(p.ProductId,getdate()),0)as nvarchar(18))                                            
from tbProduct p where lower(p.DutyCode)= lower(substring(REPLACE(cr.HTSCode, '.', ''),0,9)) and p.Inactive=0 ) as IHSDutyCode                                            
,CASE WHEN cr.ECCNCode is null          
               THEN ('[Blank]')          
               ELSE (cr.ECCNCode)          
          END AS ECCNCode                                       
,cr.ParentRequirementNo as  ParentRequirementId                                    
,(select top 1 CustomerRequirementNumber from tbCustomerRequirement where CustomerRequirementId=cr.ParentRequirementNo) as ParentRequirementNo                            
                                      
,isnull(pr.IsRestrictedProduct ,0) as IsRestrictedProduct                            
--,ECode.ECCNClientNotify                          
--,ECode.ECCNSubject             
--,ECode.ECCNMessage                         
--,ECode.Notes as WarningMessage                   
,CASE WHEN cr.ECCNCode is null          
               THEN (select top 1 ECCNClientNotify from tbECCN eccn where eccn.ECCNCode='[Blank]')          
               ELSE (select top 1 ECCNClientNotify from tbECCN eccn where eccn.ECCNCode=cr.ECCNCode)          
          END AS ECCNClientNotify          
,CASE WHEN cr.ECCNCode is null          
            THEN (select top 1 ECCNSubject from tbECCN eccn where eccn.ECCNCode='[Blank]')          
            ELSE (select top 1 ECCNSubject from tbECCN eccn where eccn.ECCNCode=cr.ECCNCode)          
        END AS ECCNSubject                
,CASE WHEN cr.ECCNCode is null          
            THEN (select top 1 ECCNMessage from tbECCN eccn where eccn.ECCNCode='[Blank]')          
            ELSE (select top 1 ECCNMessage from tbECCN eccn where eccn.ECCNCode=cr.ECCNCode)          
        END AS ECCNMessage                
,CASE WHEN cr.ECCNCode is null          
            THEN (select top 1 Notes from tbECCN eccn where eccn.ECCNCode='[Blank]')          
            ELSE (select top 1 Notes from tbECCN eccn where eccn.ECCNCode=cr.ECCNCode)          
        END AS WarningMessage               
--,isnull(tbstock.QuantityInStock,0)QuantityInStock              
--,isnull(tbstock.QuantityOnOrder,0)QuantityOnOrder        
,'[Blank]' as BlankECCNCode      
,co.PurchasingNotes       
, ISNULL(cr.AS6081,0) as AS6081 --[005]    
,ISNULL(b.IsPDFAvailable,0) as IsPDFAvailable,  
B.IHSPartsId
,B.AveragePrice AS IHSAveragePrice
FROM dbo.tbCustomerRequirement cr                
JOIN dbo.tbCompany co ON cr.CompanyNo = co.CompanyId                                                                        
LEFT JOIN dbo.tbCurrency cu ON cr.CurrencyNo = cu.CurrencyId                                                                        
LEFT JOIN dbo.tbLogin lg ON cr.Salesman = lg.LoginId                                            
LEFT JOIN dbo.tbContact cn ON cr.ContactNo = cn.ContactId                                                                        
LEFT JOIN dbo.tbProduct pr ON cr.ProductNo = pr.ProductId                                                                        
LEFT JOIN dbo.tbPackage pk ON cr.PackageNo = pk.PackageId                                           
LEFT JOIN dbo.tbManufacturer mf ON cr.ManufacturerNo = mf.ManufacturerId                                                                        
LEFT JOIN dbo.tbUsage us ON cr.UsageNo = us.UsageId                                                                        
LEFT JOIN dbo.tbReason re ON cr.ReasonNo = re.ReasonId                                                                        
LEFT JOIN dbo.tbDivision dv ON lg.DivisionNo = dv.DivisionId                                                                        
LEFT JOIN tbCompanyType ct ON ct.CompanyTypeId = co.TypeNo                                                                        
LEFT JOIN tbBOM bom ON bom.BOMId = cr.BOMNo     
left join tbIHSParts B on cr.IHSPartsNo=B.IHSPartsId  
LEFT JOIN tbClient c ON cr.ClientNo = c.ClientId                                                          
LEFT JOIN dbo.tbLogin stm ON cr.SupportTeamMemberNo = stm.LoginId                                                 
LEFT JOIN  tbGlobalCountryList coo on cr.CountryOfOriginNo=coo.GlobalCountryId                           
--left join  tbECCN ECode on cr.ECCNCode=ECode.ECCNCode   
  
  
  
  
  
GO


