﻿
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE OR ALTER PROCEDURE [dbo].[USP_OGEL_PackingExport_ToFile]
@ClientId int  
, @TeamId int = NULL                      
, @DivisionId int = NULL                      
, @LoginId int = NULL                      
, @OrderBy int = 1                      
, @SortDir int = 1                      
--, @PageIndex int = 0                      
--, @PageSize int = 10                      
,@CountrySearch int = NULL   
,@InvoiceNoLo int= null
,@InvoiceNoHi int= null
,@SoNoLo nvarchar(50) = NULL 
,@SoNoHi nvarchar(50) = NULL 
,@RecentOnly bit = 1  
,@DateOrderedFrom  datetime = NULL    
,@DateOrderedTo  datetime = NULL    
as  
begin  
DECLARE @RecentDate datetime                      
      , @StartPage int                      
      , @EndPage int                      
    SET @RecentDate = dbo.ufn_get_date_from_datetime(dateadd(mm, -3, getdate()))                      
    --SET @StartPage = (@PageIndex * @PageSize + 1)                      
    --SET @EndPage = ((@PageIndex + 1) * @PageSize)                      
                      
    IF (NOT @DateOrderedFrom IS NULL)                       
        SET @DateOrderedFrom = dbo.ufn_get_start_of_day_for_date(@DateOrderedFrom)                      
                      
    IF (NOT @DateOrderedTo IS NULL)                       
        SET @DateOrderedTo = dbo.ufn_get_end_of_day_for_date(@DateOrderedTo)                      
DECLARE @ShippedFromDt datetime ,@ShippedToDt datetime 
set @ShippedFromDt =  dbo.ufn_get_date_from_datetime(dateadd(WW, -2, getdate())) 
set @ShippedToDt =  dbo.ufn_get_date_from_datetime(dateadd(WW, 2, getdate())) 
              --SET @OGELNumber= REPLACE(REPLACE(REPLACE(REPLACE(@OGELNumber,' ', ''),'/',''),'&',''),'\','') 
  
 create table #tempOGELLines (
 salesordernumber int ,SOSerialNo int ,AirWayBill varchar(100),
ECCNCode varchar(100), Notes varchar(1000),
OGELNumber varchar(100),  OGEL_MilitaryUse int ,OGEL_EndDestinationCountry int, CountryName varchar(100), RowNum int
 )


if(@RecentOnly =1)
begin
 insert into #tempOGELLines
			  select salesordernumber as 'SONumber',SOSerialNo as 'SOLineNumber',AirWayBill as 'AirWayBill',
vw.ECCNCode as 'CommodityCode',--vw.ECCNCode,
eccn.Notes as 'ECCNDescription',
OGELNumber as 'OGELNumber',  OGEL_MilitaryUse as 'MilitaryUse',OGEL_EndDestinationCountry as 'DestinationCountryCode', tbc.CountryName as 'DestinationCountry'
--(select top 1 airwaybill from tbinvoice where salesorderno = tbso.salesorderid )  
, ROW_NUMBER() OVER ( ORDER BY                       
         case WHEN @OrderBy = 1 AND @SortDir = 2 THEN salesordernumber END  DESC                       
       , case WHEN @OrderBy = 1 THEN  salesordernumber END                      
       , case WHEN @OrderBy = 2 AND @SortDir = 2 THEN  SOSerialNo END DESC                      
       , case WHEN @OrderBy = 2 THEN  SOSerialNo  END              
       , case WHEN @OrderBy = 3  AND @SortDir = 2 THEN  AirWayBill END DESC                      
       , case WHEN @OrderBy = 3 THEN AirWayBill END           
       --, case WHEN @OrderBy = 4 AND @SortDir = 2 THEN CommodityCode END DESC                      
       --, case WHEN @OrderBy = 4 THEN CommodityCode END      
       , case WHEN @OrderBy = 5 AND @SortDir = 2 THEN  vw.ECCNCode END DESC                      
       , case WHEN @OrderBy = 5 THEN  vw.ECCNCode END                      
       , case WHEN @OrderBy = 6  AND @SortDir = 2 THEN  eccn.Notes  END DESC            
       , case WHEN @OrderBy = 6 THEN  eccn.Notes END                   
       , case WHEN @OrderBy = 7 AND @SortDir = 2 THEN OGELNumber END DESC                      
       , case WHEN @OrderBy = 7 THEN OGELNumber END   
       , case WHEN @OrderBy = 8 AND @SortDir = 2 THEN  OGEL_MilitaryUse END DESC                      
       , case WHEN @OrderBy = 8 THEN  OGEL_MilitaryUse END   
       , case WHEN @OrderBy = 9 AND @SortDir = 2 THEN  OGEL_EndDestinationCountry END DESC                      
       , case WHEN @OrderBy = 9 THEN  OGEL_EndDestinationCountry END   
       --, case WHEN @OrderBy = 10 AND @SortDir = 2 THEN @DateOrderedTo END DESC                      
       --, case WHEN @OrderBy = 10 THEN @DateOrderedTo END    
       ) AS RowNum     
from VW_GetOGELLine vw left join dbo.tbeccn eccn on vw.ECCNCode=eccn.ECCNCode 
left join tbcountry tbc on tbc.countryid = vw.OGEL_EndDestinationCountry 
where  
 vw.clientno = @ClientId 
 AND ((@CountrySearch IS NULL) OR (NOT @CountrySearch IS NULL  AND  OGEL_EndDestinationCountry = @CountrySearch)) 
 --AND ((@InvoiceNumber IS NULL) OR (NOT @InvoiceNumber IS NULL  AND InvoiceNumber = @InvoiceNumber)) 
  AND ((@InvoiceNoLo IS NULL)  OR (NOT @InvoiceNoLo IS NULL AND InvoiceNumber >= @InvoiceNoLo))                    
AND ((@InvoiceNoHi IS NULL)  OR (NOT @InvoiceNoHi IS NULL AND InvoiceNumber <= @InvoiceNoHi)) 
 --AND ((@OGELNumber IS NULL) OR (NOT @OGELNumber IS NULL  AND OGELNumber = @OGELNumber)) 
  --AND (@OGELNumber IS NULL OR (NOT @OGELNumber IS NULL AND REPLACE(REPLACE(REPLACE(REPLACE(salesordernumber,' ', ''),'/',''),'&',''),'\','')             
  --     LIKE REPLACE(REPLACE(REPLACE(REPLACE(@OGELNumber,' ', ''),'/',''),'&',''),'\',''))) 
  AND ((@SoNoLo IS NULL)  OR (NOT @SoNoLo IS NULL AND SalesOrderNumber >= @SoNoLo))                    
AND ((@SoNoHi IS NULL)  OR (NOT @SoNoHi IS NULL AND SalesOrderNumber <= @SoNoHi)) 
  --AND ((@RecentOnly = 0) OR (@RecentOnly = 1 AND  DatePromised >= @RecentDate))  
    AND ((@ShippedFromDt IS NULL) OR (NOT @ShippedFromDt IS NULL AND  DatePromised >= @ShippedFromDt))   
  AND ((@ShippedToDt IS NULL) OR (NOT @ShippedToDt IS NULL  AND  DatePromised <= @ShippedToDt))
  AND ((@DateOrderedFrom IS NULL) OR (NOT @DateOrderedFrom IS NULL AND  DatePromised >= @DateOrderedFrom))   
  AND ((@DateOrderedTo IS NULL) OR (NOT @DateOrderedTo IS NULL  AND  DatePromised <= @DateOrderedTo))    
--and  salesordernumber = 992732  
end
else
begin
 insert into #tempOGELLines
			  select salesordernumber as 'SONumber',SOSerialNo as 'SOLineNumber',AirWayBill as 'AirWayBill',
vw.ECCNCode as 'CommodityCode',--vw.ECCNCode,
eccn.Notes as 'ECCNDescription',
OGELNumber as 'OGELNumber',  OGEL_MilitaryUse as 'MilitaryUse',OGEL_EndDestinationCountry as 'DestinationCountryCode', tbc.CountryName as 'DestinationCountry'
--(select top 1 airwaybill from tbinvoice where salesorderno = tbso.salesorderid )  
, ROW_NUMBER() OVER ( ORDER BY                       
         case WHEN @OrderBy = 1 AND @SortDir = 2 THEN salesordernumber END  DESC                       
       , case WHEN @OrderBy = 1 THEN  salesordernumber END                      
       , case WHEN @OrderBy = 2 AND @SortDir = 2 THEN  SOSerialNo END DESC                      
       , case WHEN @OrderBy = 2 THEN  SOSerialNo  END              
       , case WHEN @OrderBy = 3  AND @SortDir = 2 THEN  AirWayBill END DESC                      
       , case WHEN @OrderBy = 3 THEN AirWayBill END           
       --, case WHEN @OrderBy = 4 AND @SortDir = 2 THEN CommodityCode END DESC                      
       --, case WHEN @OrderBy = 4 THEN CommodityCode END      
       , case WHEN @OrderBy = 5 AND @SortDir = 2 THEN  vw.ECCNCode END DESC                      
       , case WHEN @OrderBy = 5 THEN  vw.ECCNCode END                      
       , case WHEN @OrderBy = 6  AND @SortDir = 2 THEN  eccn.Notes  END DESC            
       , case WHEN @OrderBy = 6 THEN  eccn.Notes END                   
       , case WHEN @OrderBy = 7 AND @SortDir = 2 THEN OGELNumber END DESC                      
       , case WHEN @OrderBy = 7 THEN OGELNumber END   
       , case WHEN @OrderBy = 8 AND @SortDir = 2 THEN  OGEL_MilitaryUse END DESC                      
       , case WHEN @OrderBy = 8 THEN  OGEL_MilitaryUse END   
       , case WHEN @OrderBy = 9 AND @SortDir = 2 THEN  OGEL_EndDestinationCountry END DESC                      
       , case WHEN @OrderBy = 9 THEN  OGEL_EndDestinationCountry END   
       --, case WHEN @OrderBy = 10 AND @SortDir = 2 THEN @DateOrderedTo END DESC                      
       --, case WHEN @OrderBy = 10 THEN @DateOrderedTo END    
       ) AS RowNum     
from VW_GetOGELLine vw left join dbo.tbeccn eccn on vw.ECCNCode=eccn.ECCNCode 
left join tbcountry tbc on tbc.countryid = vw.OGEL_EndDestinationCountry 
where  
 vw.clientno = @ClientId 
 AND ((@CountrySearch IS NULL) OR (NOT @CountrySearch IS NULL  AND  OGEL_EndDestinationCountry = @CountrySearch)) 
 --AND ((@InvoiceNumber IS NULL) OR (NOT @InvoiceNumber IS NULL  AND InvoiceNumber = @InvoiceNumber)) 
  AND ((@InvoiceNoLo IS NULL)  OR (NOT @InvoiceNoLo IS NULL AND InvoiceNumber >= @InvoiceNoLo))                    
AND ((@InvoiceNoHi IS NULL)  OR (NOT @InvoiceNoHi IS NULL AND InvoiceNumber <= @InvoiceNoHi)) 
 --AND ((@OGELNumber IS NULL) OR (NOT @OGELNumber IS NULL  AND OGELNumber = @OGELNumber)) 
  --AND (@OGELNumber IS NULL OR (NOT @OGELNumber IS NULL AND REPLACE(REPLACE(REPLACE(REPLACE(salesordernumber,' ', ''),'/',''),'&',''),'\','')             
  --     LIKE REPLACE(REPLACE(REPLACE(REPLACE(@OGELNumber,' ', ''),'/',''),'&',''),'\',''))) 
  AND ((@SoNoLo IS NULL)  OR (NOT @SoNoLo IS NULL AND SalesOrderNumber >= @SoNoLo))                    
AND ((@SoNoHi IS NULL)  OR (NOT @SoNoHi IS NULL AND SalesOrderNumber <= @SoNoHi)) 
  --AND ((@RecentOnly = 0) OR (@RecentOnly = 1 AND  DatePromised >= @RecentDate))  

  AND ((@DateOrderedFrom IS NULL) OR (NOT @DateOrderedFrom IS NULL AND  DatePromised >= @DateOrderedFrom))   
  AND ((@DateOrderedTo IS NULL) OR (NOT @DateOrderedTo IS NULL  AND  DatePromised <= @DateOrderedTo))    
--and  salesordernumber = 992732  
end
   
  SELECT  
   salesordernumber as SONumber, SOSerialNo as SOLineNumber, AirWayBill,
 ECCNCode ,
Notes as ECCNDescription,
OGELNumber,
case when OGEL_MilitaryUse=1 then 'Yes' else 'No' end as 'MilitaryUse',
--DestinationCountry,
CountryName
    --          , (SELECT count(*)                      
    --             FROM   cteSearch                      
    --) AS RowCnt                      
    --          , dbo.ufn_get_salesOrder_statusNo(salesordernumber) AS [Status]                      
       FROM    #tempOGELLines                      
        --WHERE   RowNum BETWEEN @StartPage AND @EndPage                      
        order by SONumber,SOLineNumber  
  
end  
GO


