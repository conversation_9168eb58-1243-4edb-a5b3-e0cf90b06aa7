Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");Rebound.GlobalTrader.Site.Controls.DropDowns.ClientByMaster=function(n){Rebound.GlobalTrader.Site.Controls.DropDowns.ClientByMaster.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.DropDowns.ClientByMaster.prototype={get_intMasterLoginNo:function(){return this._intMasterLoginNo},set_intMasterLoginNo:function(n){this._intMasterLoginNo!==n&&(this._intMasterLoginNo=n)},initialize:function(){this.addSetupDataCall(Function.createDelegate(this,this.setupDataCall));this.addGetDataOK(Function.createDelegate(this,this.dataCallOK));Rebound.GlobalTrader.Site.Controls.DropDowns.ClientByMaster.callBaseMethod(this,"initialize")},dispose:function(){this.isDisposed||(this._intMasterLoginNo=null,Rebound.GlobalTrader.Site.Controls.DropDowns.ClientByMaster.callBaseMethod(this,"dispose"))},setupDataCall:function(){this._objData.set_PathToData("controls/DropDowns/ClientByMaster");this._objData.set_DataObject("ClientByMaster");this._objData.set_DataAction("GetData");this._objData.addParameter("MasterLoginNo",this._intMasterLoginNo)},dataCallOK:function(){var t=this._objData._result,n;if(t.Clients)for(n=0;n<t.Clients.length;n++)this.addOption(t.Clients[n].Name,t.Clients[n].ID)}};Rebound.GlobalTrader.Site.Controls.DropDowns.ClientByMaster.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.ClientByMaster",Rebound.GlobalTrader.Site.Controls.DropDowns.Base);