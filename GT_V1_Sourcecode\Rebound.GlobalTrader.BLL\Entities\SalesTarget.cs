﻿using Rebound.GlobalTrader;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Rebound.GlobalTrader.DAL;


namespace Rebound.GlobalTrader.BLL
{
	public partial class SalesTarget : BizObject
	{

        #region Properties

        protected static SalesTargetElement Settings
        {
            get { return Globals.Settings.SalesTargets; }
        }
		public System.Int32 LoginId { get; set; }
        public string EmployeeName { get; set; }
		public string PersonType { get; set; }
		public System.Double? JanTarget { get; set; }
		public System.Double? AllocatedPer { get; set; }
		public System.Double? TotalTarget { get; set; }
		public System.Double? FebTarget { get; set; }
		public System.Double? MarchTarget { get; set; }
		public System.Double? AprTarget { get; set; }
		public System.Double? MayTarget { get; set; }
		public System.Double? JuneTarget { get; set; }
		public System.Double? JulyTarget { get; set; }
		public System.Double? AugTarget { get; set; }
		public System.Double? SepTarget { get; set; }
		public System.Double? OctTarget { get; set; }
		public System.Double? NovTarget { get; set; }
		public System.Double? DecTarget { get; set; }
		public System.Int32 RowId { get; set; }
        public System.Int32 RecordCount { get; set; }
		public System.Int32 IsCustMoved { get; set; }



		#endregion

		#region Methods
		/// <summary>
		/// GetSalesAndCustomerTarget 
		/// Calls [[KPI_GetSalesCustomerTarget]]
		/// </summary>
		public static List<SalesTarget> GetSalesAndCustomerTarget(System.Int32? salesManNo, System.Int32? managerNo,System.Int32 yearNo,System.String table)
		{
			List<SalesTargetDetails> lstDetails = SiteProvider.SalesTarget.GetSalesAndCustomerTarget(salesManNo, managerNo, yearNo, table);
			if (lstDetails == null)
			{
				return new List<SalesTarget>();
			}
			else
			{
				List<SalesTarget> lst = new List<SalesTarget>();
				foreach (SalesTargetDetails objDetails in lstDetails)
				{
					BLL.SalesTarget obj = new BLL.SalesTarget();
					obj.LoginId = objDetails.LoginId;
					obj.EmployeeName = objDetails.EmployeeName;
					obj.PersonType = objDetails.PersonType;
					obj.JanTarget = objDetails.JanTarget;
					obj.FebTarget = objDetails.FebTarget;
					obj.MarchTarget = objDetails.MarchTarget;
					obj.AprTarget = objDetails.AprTarget;
					obj.MayTarget = objDetails.MayTarget;
					obj.JuneTarget = objDetails.JuneTarget;
					obj.JulyTarget = objDetails.JulyTarget;
					obj.AugTarget = objDetails.AugTarget;
					obj.SepTarget = objDetails.SepTarget;
					obj.OctTarget = objDetails.OctTarget;
					obj.NovTarget = objDetails.NovTarget;
					obj.DecTarget = objDetails.DecTarget;
					obj.TotalTarget = objDetails.TotalTarget;
					obj.AllocatedPer = objDetails.AllocatedPer;
					obj.RowId = objDetails.RowId;
                    obj.RecordCount = objDetails.RecordCount;
					obj.IsCustMoved = objDetails.IsCustMoved;
                    lst.Add(obj);
					obj = null;
				}
				lstDetails = null;
				return lst;
			}
		}

		/// <summary>
		/// Update
		/// Calls [usp_update_SalesCustomerTarget]
		/// </summary>
		public static bool Update(System.Int32? rowId, System.String rowType, System.String columnName, System.Double? targetValue,System.Int32? updatedBy,System.Int32? Year, System.Int32? salesmanNo)
		{
			return SiteProvider.SalesTarget.Update(rowId, rowType, columnName, targetValue, updatedBy, Year, salesmanNo);
		}

		/// <summary>
		/// Update
		/// Calls [usp_saveAllSalesCustomerTarget]
		/// </summary>
		public static bool SaveAllSalesCustomerData(System.Int32? Year, System.Int32? intSalesmanNo, System.Int32? updatedBy)
		{
			return SiteProvider.SalesTarget.SaveAllSalesCustomerData(Year, intSalesmanNo, updatedBy);
		}

		#endregion

	}
}