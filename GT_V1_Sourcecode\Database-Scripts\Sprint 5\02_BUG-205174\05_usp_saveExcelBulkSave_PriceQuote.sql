﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO
IF OBJECT_ID('dbo.usp_saveExcelBulkSave_PriceQuote', 'P') IS NOT NULL
BEGIN
    DROP PROCEDURE dbo.usp_saveExcelBulkSave_PriceQuote
END
GO
/* 
===========================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-205174]		An.TranTan			28-Jun-2024		Update			Insert column ClientNo at Column2 to tempdata table
===========================================================================================
*/
CREATE Procedure [dbo].[usp_saveExcelBulkSave_PriceQuote]
    @UploadedData UploadedDataPriceQuote READONLY,
    @originalFilename varchar(max),
    @generatedFilename varchar(max),
    @userId Int,
    @clientId int,
    @SelectedclientId int
AS
BEGIN
    SET NOCOUNT ON;
    INSERT INTO BorisGlobalTraderimports.dbo.tbPriceQuoteImport_tempData
    (
        [Column1],
        [Column2],
        [Column3],
        [Column4],
        [Column5],
        [Column6],
        [Column7],
        [Column8],
        [Column9],
        [Column10],
        [Column11],
        [Column12],
        [Column13],
        [Column14],
        [Column15],
        [Column16],
        [Column17],
        [Column18],
        [Column19],
        [Column20],
        [Column21],
        [Column22],
        [Column23],
        [Column24],
        [Column25],
        [OriginalFilename],
        [GeneratedFilename],
        [ClientId],
        [SelectedClientId],
        [CreatedBy]
    )
    select [Column1],
           [Column2],
           [Column3],
           [Column4],
           [Column5],
           [Column6],
           [Column7],
           [Column8],
           [Column9],
           [Column10],
           [Column11],
           [Column12],
           [Column13],
           [Column14],
           [Column15],
           [Column16],
           [Column17],
           [Column18],
           [Column19],
           [Column20],
           [Column21],
           [Column22],
           [Column23],
           [Column24],
           [Column25],
           @originalFilename,
           @generatedFilename,
           @clientId,
		   @SelectedclientId,
           @userId
    from @UploadedData
END
GO


