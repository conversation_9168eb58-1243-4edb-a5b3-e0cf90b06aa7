/**********************************************************************************************
Marker     changed by      date         Remarks
[001]      Bhooma         04-Feb-2022   Create CRM Prospect 
 * **********************************************************************************************/
using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;

using Rebound.GlobalTrader.Site.Controls;
using Rebound.GlobalTrader.Site;


namespace Rebound.GlobalTrader.Site.Controls.Forms {
	public partial class CompanyProspects_Edit : Base {
      
		#region Locals
       
		#endregion

		#region Overrides

		/// <summary>
		/// OnInit
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
			base.OnInit(e);
			TitleText = Functions.GetGlobalResource("FormTitles", "CompanyProspects_Edit");
			AddScriptReference("Controls.Nuggets.CompanyProspects.Edit.CompanyProspects_Edit.js");
		}

		/// <summary>
		/// OnPreRender
		/// </summary>
		/// <param name="e"></param>
		protected override void OnPreRender(EventArgs e) {
         
			WireUpControls();
			SetupScriptDescriptors();
       
			base.OnPreRender(e);
		}

		#endregion
		/// <summary>
		/// Wire up controls to the ascx
		/// </summary>
		private void WireUpControls() {
		}

		/// <summary>
		/// Setup Script Descriptors
		/// </summary>
		private void SetupScriptDescriptors() {
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.Forms.CompanyProspects_Edit", ctlDesignBase.ClientID);
		}

	}
}