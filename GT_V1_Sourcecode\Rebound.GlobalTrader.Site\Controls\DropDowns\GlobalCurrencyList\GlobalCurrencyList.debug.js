///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//
// RP 06.10.2009:
// - Changes due to changes in base class
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");

Rebound.GlobalTrader.Site.Controls.DropDowns.GlobalCurrencyList = function(element) { 
	Rebound.GlobalTrader.Site.Controls.DropDowns.GlobalCurrencyList.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.DropDowns.GlobalCurrencyList.prototype = {
	
	get_blnIncludeSelected: function() { return this._blnIncludeSelected; }, 	set_blnIncludeSelected: function(v) { if (this._blnIncludeSelected !== v)  this._blnIncludeSelected = v; }, 

	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.DropDowns.GlobalCurrencyList.callBaseMethod(this, "initialize");
		this.addSetupDataCall(Function.createDelegate(this, this.setupDataCall));
		this.addGetDataOK(Function.createDelegate(this, this.dataCallOK));
	},
	
	dispose: function() { 
		if (this.isDisposed) return;
		this._blnIncludeSelected = null;
		Rebound.GlobalTrader.Site.Controls.DropDowns.GlobalCurrencyList.callBaseMethod(this, "dispose");
	},
	
	setupDataCall: function() { 		
		this._objData.set_PathToData(this._strDataPathModification + "controls/DropDowns/GlobalCurrencyList");
		this._objData.set_DataObject("GlobalCurrencyList");
		this._objData.set_DataAction("GetData");
		this._objData.addParameter("IncludeSelected", this._blnIncludeSelected);
	},
	
	dataCallOK: function() { 
		var result = this._objData._result;
		if (result.Currencies) {
			for (var i = 0; i < result.Currencies.length; i++) {
				this.addOption(result.Currencies[i].Name, result.Currencies[i].ID, result.Currencies[i].Code);
			}
		}
	}
	
};

Rebound.GlobalTrader.Site.Controls.DropDowns.GlobalCurrencyList.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.GlobalCurrencyList", Rebound.GlobalTrader.Site.Controls.DropDowns.Base);
