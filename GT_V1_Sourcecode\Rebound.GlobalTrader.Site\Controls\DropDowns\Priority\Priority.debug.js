///<reference name="MicrosoftAjax.js" />
//-----------------------------------------------------------------------------------------
// RP 06.10.2009:
// - Changes due to changes in base class
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");

Rebound.GlobalTrader.Site.Controls.DropDowns.Priority = function (element) {
    Rebound.GlobalTrader.Site.Controls.DropDowns.Priority.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.DropDowns.Priority.prototype = {
//get_intPOHubClientNo: function() { return this._intPOHubClientNo; }, set_intPOHubClientNo: function(v) { if (this._intPOHubClientNo !== v) this._intPOHubClientNo = v; },
	
	initialize: function() {
	    Rebound.GlobalTrader.Site.Controls.DropDowns.Priority.callBaseMethod(this, "initialize");
		this.addSetupDataCall(Function.createDelegate(this, this.setupDataCall));
		this.addGetDataOK(Function.createDelegate(this, this.dataCallOK));
	},

	dispose: function() { 
		if (this.isDisposed) return;
		this._intPOHubClientNo = null;
		Rebound.GlobalTrader.Site.Controls.DropDowns.Priority.callBaseMethod(this, "dispose");
	},
	
	setupDataCall: function() { 
	    this._objData.set_PathToData("controls/DropDowns/Priority");
	    this._objData.set_DataObject("Priority");
		this._objData.set_DataAction("GetData");
//		this._objData.addParameter("POHubClientNo", this._intPOHubClientNo);
	},

	dataCallOK: function() { 
		var result = this._objData._result;
		if (result.Priority) {
		    for (var i = 0; i < result.Priority.length; i++) {
		        this.addOption(result.Priority[i].PriorityNo, result.Priority[i].PriorityId);
			}
		}
	}
	
};

Rebound.GlobalTrader.Site.Controls.DropDowns.Priority.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.Priority", Rebound.GlobalTrader.Site.Controls.DropDowns.Base);
