﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

/*   
===========================================================================================  
TASK			UPDATED BY      DATE			ACTION		DESCRIPTION  
[US-214760]		An.TranTan		09-Jan-2025		Create		Insert temp column header for HUBRFQ import sourcing result
===========================================================================================  
*/  
CREATE OR ALTER PROCEDURE [dbo].[usp_saveBOMSourcing_tempHeading]  
    @InsertColumnList NVARCHAR(3000),  
    @SelectColumns NVARCHAR(3000),  
    @UserId INT  
AS  
BEGIN
	--Delete user previous data
	DELETE BorisGlobalTraderImports.dbo.tbBOMSourcing_tempHeading WHERE CreatedBy = @UserId;
  
    DECLARE @DynamicQuery NVARCHAR(4000) = '';  
    SET @DynamicQuery  
        = 'INSERT INTO BorisGlobalTraderImports.dbo.tbBOMSourcing_tempHeading(' 
			+ @InsertColumnList + ', CreatedBy)'
			+ ' Select ' + @SelectColumns +',' + CONVERT(NVARCHAR(10), @UserId);
    EXEC (@DynamicQuery);  
END  
GO


