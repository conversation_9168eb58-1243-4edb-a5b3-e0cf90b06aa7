Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Nuggets");Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyGlobalSalesPDetails=function(n){Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyGlobalSalesPDetails.initializeBase(this,[n]);this._intSupplierID=-1;this._blnNoData=!1};Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyGlobalSalesPDetails.prototype={get_intSupplierID:function(){return this._intSupplierID},set_intSupplierID:function(n){this._intSupplierID!==n&&(this._intSupplierID=n)},get_tbl:function(){return this._tbl},set_tbl:function(n){this._tbl!==n&&(this._tbl=n)},get_ibtnEdit:function(){return this._ibtnEdit},set_ibtnEdit:function(n){this._ibtnEdit!==n&&(this._ibtnEdit=n)},get_ibtnAdd:function(){return this._ibtnAdd},set_ibtnAdd:function(n){this._ibtnAdd!==n&&(this._ibtnAdd=n)},get_ibtnDelete:function(){return this._ibtnDelete},set_ibtnDelete:function(n){this._ibtnDelete!==n&&(this._ibtnDelete=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyGlobalSalesPDetails.callBaseMethod(this,"initialize");this._strPathToData="controls/Nuggets/CompanyGlobalSalesPDetails";this._strDataObject="CompanyGlobalSalesPDetails";this.addRefreshEvent(Function.createDelegate(this,this.getData));(this._ibtnEdit||this._ibtnAdd)&&(this._ibtnEdit&&$R_IBTN.addClick(this._ibtnEdit,Function.createDelegate(this,this.showEditForm)),this._ibtnAdd&&$R_IBTN.addClick(this._ibtnAdd,Function.createDelegate(this,this.showAddForm)),this._frmAddEdit=$find(this._aryFormIDs[0]),this._frmAddEdit._intSupplierID=this._intSupplierID,this._frmAddEdit.addShown(Function.createDelegate(this,this.addEditFormShown)),this._frmAddEdit.addCancel(Function.createDelegate(this,this.hideAddEditForm)),this._frmAddEdit.addSaveComplete(Function.createDelegate(this,this.saveAddEditOK)),this._frmAddEdit.addSaveError(Function.createDelegate(this,this.saveAddEditError)));this._ibtnDelete&&($R_IBTN.addClick(this._ibtnDelete,Function.createDelegate(this,this.showDeleteForm)),this._frmDelete=$find(this._aryFormIDs[1]),this._frmDelete.addShown(Function.createDelegate(this,this.deleteFormShown)),this._frmDelete.addNotConfirmed(Function.createDelegate(this,this.hideDeleteForm)),this._frmDelete.addSaveComplete(Function.createDelegate(this,this.saveDeleteOK)),this._frmDelete.addSaveError(Function.createDelegate(this,this.saveDeleteError)));this._tbl.addSelectedIndexChanged(Function.createDelegate(this,this.tbl_SelectedIndexChanged))},dispose:function(){this.isDisposed||(this._ibtnAdd&&$R_IBTN.clearHandlers(this._ibtnAdd),this._ibtnEdit&&$R_IBTN.clearHandlers(this._ibtnEdit),this._ibtnDelete&&$R_IBTN.clearHandlers(this._ibtnDelete),this._frmDelete&&this._frmDelete.dispose(),this._frmAddEdit&&this._frmAddEdit.dispose(),this._tbl&&this._tbl.dispose(),this._frmDelete=null,this._frmAddEdit=null,this._tbl=null,this._ibtnAdd=null,this._ibtnEdit=null,this._ibtnDelete=null,this._intSupplierID=null,this._blnNoData=null,Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyGlobalSalesPDetails.callBaseMethod(this,"dispose"))},getData:function(){Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyGlobalSalesPDetails.callBaseMethod(this,"getData_Start");this.updateButtonsEnabledState(!1);var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData(this._strPathToData);n.set_DataObject(this._strDataObject);n.set_DataAction("GetGlobalSalesPersonsDetails");n.addParameter("id",this._intSupplierID);n.addDataOK(Function.createDelegate(this,this.getDataComplete));n.addError(Function.createDelegate(this,this.getDataError));n.addTimeout(Function.createDelegate(this,this.getDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getDataError:function(n){this.showError(!0,n.get_ErrorMessage())},getDataComplete:function(n){var i=n._result,r;if(this._tbl.clearTable(),this._blnNoData=!0,i.Items){for(r=0;r<i.Items.length;r++){var t=i.Items[r],u=[$R_FN.setCleanTextValue(t.SalesPersonName),$R_FN.setCleanTextValue(t.ClientName)],f={MfrName:t.CompanyNo};this._tbl.addRow(u,t.ID,!1,f);t=null;u=null}this._blnNoData=i.Items.length==0}this._tbl.resizeColumns();this.getDataOK_End();this.showNoData(this._blnNoData)},updateButtonsEnabledState:function(n){this._ibtnEdit&&$R_IBTN.enableButton(this._ibtnEdit,n);this._ibtnDelete&&$R_IBTN.enableButton(this._ibtnDelete,n)},tbl_SelectedIndexChanged:function(){this.updateButtonsEnabledState(!0);this._frmAddEdit._intManufacturerLinkID=this._tbl._varSelectedValue;this._frmDelete._intManufacturerLinkID=this._tbl._varSelectedValue},showAddForm:function(){this._frmAddEdit.changeMode("ADD");this.showForm(this._frmAddEdit,!0)},showEditForm:function(){this._frmAddEdit.changeMode("EDIT");this.showForm(this._frmAddEdit,!0)},addEditFormShown:function(){this._frmAddEdit._mode=="ADD"?(this._frmAddEdit._autManufacturers.reselect(),this._frmAddEdit.setFieldValue("ctlRating",0)):(this._frmAddEdit.setFieldValue("ctlManufacturerSelected",this._tbl.getSelectedExtraData().MfrName),this._frmAddEdit.setFieldValue("ctlRating",this._tbl.getSelectedExtraData().Rating))},hideAddEditForm:function(){this.showForm(this._frmAddEdit,!1);this.showNoData(this._blnNoData)},saveAddEditOK:function(){this.hideAddEditForm();this.showSavedOK(!0,$R_RES.ChangesSavedSuccessfully);this.getData()},saveAddEditError:function(){this.showError(!0,this._frmAddEdit._strErrorMessage)},showDeleteForm:function(){this.showForm(this._frmDelete,!0)},hideDeleteForm:function(){this.showForm(this._frmDelete,!1);this.showNoData(this._blnNoData)},saveDeleteOK:function(){this.hideDeleteForm();this.showSavedOK(!0,$R_RES.ChangesSavedSuccessfully);this.getData()},saveDeleteError:function(){this.showError(!0,this._frmDelete._strErrorMessage)},deleteFormShown:function(){this._frmDelete.setFieldValue("ctlManufacturerSelected",this._tbl.getSelectedExtraData().MfrName)}};Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyGlobalSalesPDetails.registerClass("Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyGlobalSalesPDetails",Rebound.GlobalTrader.Site.Controls.Nuggets.Base);