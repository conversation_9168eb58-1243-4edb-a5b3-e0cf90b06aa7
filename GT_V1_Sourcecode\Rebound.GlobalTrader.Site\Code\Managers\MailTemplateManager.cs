//Marker     Changed by      Date         Remarks
//[001]      Vinay           17/06/2013   CR:- Supplier Invoice
//[002]      Ravi            17-05-2023   RP-1600
using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.IO;
using Rebound.GlobalTrader.Site;
using Rebound.GlobalTrader.Site.Pages;
using System.Text;
using Rebound.GlobalTrader.BLL;
using System.Collections.Generic;
using System.Linq;
using System.Web.Mail;
using Rebound.GlobalTrader.DAL;

namespace Rebound.GlobalTrader.Site {
	public class MailTemplateManager {

		private static string GetRawTemplateText(TemplateList enm) {
			return Functions.GetGlobalResource("MailTemplates", enm);
		}

		public static string GetMessage_NewCustomerRequirement(BLL.CustomerRequirement cr) {
			string strRaw = GetRawTemplateText(TemplateList.NewCustomerRequirement);
			string strOut = strRaw;
			strOut = PrintPage.ReplaceVariable(strOut, "CUSREQ_NUMBER", cr.CustomerRequirementNumber);
			strOut = PrintPage.ReplaceVariable(strOut, "CUSTOMER", cr.CompanyName);
			strOut = PrintPage.ReplaceVariable(strOut, "QUANTITY", cr.Quantity);
			strOut = PrintPage.ReplaceVariable(strOut, "PART", cr.Part);
			strOut = PrintPage.ReplaceVariable(strOut, "CUSTOMERPART", cr.CustomerPart);
			strOut = PrintPage.ReplaceVariable(strOut, "MANUFACTURER", cr.ManufacturerName);
			strOut = PrintPage.ReplaceVariable(strOut, "DATECODE", cr.DateCode);
			strOut = PrintPage.ReplaceVariable(strOut, "PRODUCT", cr.ProductName);
			strOut = PrintPage.ReplaceVariable(strOut, "PACKAGE", cr.PackageName);
			strOut = PrintPage.ReplaceVariable(strOut, "PRICE", Functions.FormatCurrency(cr.Price, cr.CurrencyCode));
			strOut = PrintPage.ReplaceVariable(strOut, "DATEREQUIRED", cr.DatePromised);
			strOut = PrintPage.ReplaceVariable(strOut, "HYPERLINK", PageManager.GotoURL_CustomerRequirement(cr.CustomerRequirementId).Replace("~/", ""));
			return strOut;
		}

        public static string GetMessage_NewCustomerRequirement(BLL.CustomerRequirement cr,string strMessage)
        {
            string strRaw = strMessage;
            string strOut = strRaw;
            strOut = PrintPage.ReplaceVariable(strOut, "CUSREQ_NUMBER",  cr.CustomerRequirementNumber);
            strOut = PrintPage.ReplaceVariable(strOut, "CUSTOMER", cr.CompanyName);
            strOut = PrintPage.ReplaceVariable(strOut, "QUANTITY", cr.Quantity);
            strOut = PrintPage.ReplaceVariable(strOut, "PART", cr.Part);
            strOut = PrintPage.ReplaceVariable(strOut, "CUSTOMERPART", cr.CustomerPart);
            strOut = PrintPage.ReplaceVariable(strOut, "MANUFACTURER", cr.ManufacturerName);
            strOut = PrintPage.ReplaceVariable(strOut, "DATECODE", cr.DateCode);
            strOut = PrintPage.ReplaceVariable(strOut, "PRODUCT", cr.ProductName);
            strOut = PrintPage.ReplaceVariable(strOut, "PACKAGE", cr.PackageName);
            strOut = PrintPage.ReplaceVariable(strOut, "PRICE", Functions.FormatCurrency(cr.Price, cr.CurrencyCode));
            strOut = PrintPage.ReplaceVariable(strOut, "DATEREQUIRED", cr.DatePromised);
            strOut = PrintPage.ReplaceVariable(strOut, "HYPERLINK", PageManager.GotoURL_CustomerRequirement(cr.CustomerRequirementId).Replace("~/", ""));
            return strOut;
        }

        public static string GetMessage_NewProspectiveOfferCusReq(List<CustomerRequirement> crs, int proId, List<int> lineIds, List<ProspectiveOfferLines> offerLineIds)
        {
            string strRaw = GetRawTemplateText(TemplateList.ProspectiveOfferEmail);
            string strOut = strRaw;
            string StrmailData = "";
            StringBuilder strMail = new StringBuilder();
            foreach (CustomerRequirement cr in crs)
            {
                int prospectiveOfferLineId = offerLineIds.Where(c => c.CustomerReqId == cr.CustomerRequirementId).Select(x => x.ProspectiveOfferLineId).FirstOrDefault();
                double ? reqInBasePrice = BLL.Currency.ConvertValueToBaseCurrency(cr.Price, (int)cr.CurrencyNo, DateTime.Now);
                cr.Price = (double)reqInBasePrice;
                string Line = "<tr>";
                string url = string.Format(Convert.ToString(System.Configuration.ConfigurationManager.AppSettings["redirecturi"]));
                //double newPrice = (double)(cr.NewOfferPriceFromProspective != null ? cr.NewOfferPriceFromProspective : cr.Price);

                var hubrfqLink = url + string.Format("Ord_BOMDetail.aspx?BOM={0}", cr.BOMNo);
                var cloneHUBRFQLink = url + string.Format("ProspectiveOfferHUBRFQ.aspx?PROId={0}&GTId={1}&LineId={2}&BOMId={3}&CurrencyNo={4}", proId, cr.CustomerRequirementId, prospectiveOfferLineId, cr.BOMNo, cr.ClientCurrencyNo);
                Line = Line + "<td class=\"Textclass\"><a href=\"" + hubrfqLink + "\">" + cr.BOMNo + "</a><br /><br/>" + cr.ReceivedDate.ToString("dd-MM-yyyy") + "</td>";
                Line = Line + "<td class=\"Textclass\">" + cr.CompanyName + "</td>";
                Line = Line + "<td class=\"Textclass\">" + cr.Part + "<br /><br/>" + cr.CustomerPart + "</td>";
                Line = Line + "<td class=\"Textclass\">" + cr.ManufacturerName + "<br /><br/>" + cr.DateCode + "</td>";
                Line = Line + "<td class=\"Textclass\">" + cr.IHSAveragePrice.ToString("F5") + " " + cr.CurrencyCode + "<br /><br/>" 
                        + cr.LyticaAveragePrice.ToString("F5") + " " + cr.CurrencyCode + "</td>";
                Line = Line + "<td class=\"Textclass\">" + cr.ProductName + "<br />" + cr.PackageName + "</td>";
                Line = Line + "<td class=\"Textclass\">" + cr.Price.ToString("F5") + " " + cr.CurrencyCode + "<br /><br/>" + cr.Quantity + "</td>";
                Line = Line + "<td class=\"Textclass\"><a id=\"cloneOfferLink\" href=\"" + cloneHUBRFQLink + "\">Create New HUBRFQ</a></td>";
                Line = Line + "</tr>";
                strMail.Append(Line);
            }
            StrmailData = Convert.ToString(strMail);
            strOut = strOut.Replace("#trProspectiveOfferLine#", StrmailData);
            return strOut;
        }

        public static string GetMessage_RLStock(List<CustomerRequirement> customerRequirements, bool isFromBOM)
        {
            string strRaw = isFromBOM ? GetRawTemplateText(TemplateList.ReverseLogisticStockBody1) : GetRawTemplateText(TemplateList.ReverseLogisticStockBody2);
            string strOut = strRaw;
            string StrmailData = "";
            StringBuilder strMail = new StringBuilder();
            foreach (CustomerRequirement cr in customerRequirements)
            {
                string warehouse = GetWareHouseLocation(cr);

                string strPrice = "";
                if ((cr.CurrencyCode != cr.ClientCurrencyCode) && (cr.ReqGlobalCurrencyNo != cr.ClientGlobalCurrencyNo))
                {
                    double? reqInBasePrice = BLL.Currency.ConvertValueToBaseCurrency(cr.Price, (int)cr.CurrencyNo, DateTime.Now);
                    strPrice = String.Format("{0}", Functions.FormatCurrency(BLL.Currency.ConvertValueFromBaseCurrency(reqInBasePrice, (int)cr.ClientCurrencyNo, DateTime.Now), cr.ClientCurrencyCode));
                }
                else
                {
                   strPrice = Functions.FormatCurrency(cr.Price, cr.CurrencyCode);
                }

                string Line = "<tr>";
                if (!isFromBOM)
                {
                    string url = string.Format(Convert.ToString(System.Configuration.ConfigurationManager.AppSettings["redirecturi"]));
                    var hubrfqLink = url + string.Format("Ord_BOMDetail.aspx?BOM={0}", cr.BOMNo);
                    Line = Line + "<td class=\"Textclass\"><a href=\"" + hubrfqLink + "\">" + cr.BOMNo + "</a></td>";
                }
                Line = Line + "<td class=\"Textclass\">" + cr.CompanyName + "</td>";
                if (isFromBOM)
                {
                    Line = Line + "<td class=\"Textclass\">" + cr.Part + "</td>";
                }
                Line = Line + "<td class=\"Textclass\">" + cr.ManufacturerName + "</td>";
                Line = Line + "<td class=\"Textclass\">" + cr.ProductName + "</td>";
                Line = Line + "<td class=\"Textclass\">" + cr.PackageName + "</td>";
                Line = Line + "<td class=\"Textclass\">" + cr.DateCode + "</td>";
                Line = Line + "<td class=\"Textclass\">" + cr.Quantity + "</td>";
                Line = Line + "<td class=\"Textclass\">" + strPrice + "</td>";
                Line = Line + "<td class=\"Textclass\">" + warehouse + "</td>";
                Line = Line + "</tr>";
                strMail.Append(Line);
            }
            StrmailData = Convert.ToString(strMail);
            strOut = strOut.Replace("#ResverseLogisticStocks#", StrmailData);
            return strOut;
        }

        private static string GetWareHouseLocation(CustomerRequirement crReqs)
        {
            string wareHouse = "";
            List<int> asia = new List<int> { 107, 116, 118, 121 };
            List<int> europe = new List<int> { 101, 108, 117 };
            bool isLocateAsia = asia.Contains(crReqs.ClientNo);
            bool isLocateEurope = europe.Contains(crReqs.ClientNo);
            if (isLocateAsia && isLocateEurope)
            {
                wareHouse = "ASIA/EUROPE";
            }
            else if (isLocateEurope)
            {
                wareHouse = "EUROPE";
            }
            else if (isLocateAsia)
            {
                wareHouse = "ASIA";
            }

            return wareHouse;
        }

        public static string GetHeaderMessage_NewProspectiveOfferCusReq(string message)
        {
            string strRaw = GetRawTemplateText(TemplateList.ProspectiveOfferHeaderMail);
            string strOut = strRaw;
            strOut = PrintPage.ReplaceVariable(strOut, "SALEMAN", message);
            return strOut;
        }

        public static string GetMessage_NewCustomerRequirementECCN(BLL.CustomerRequirement cr)
        {
            string strRaw = GetRawTemplateText(TemplateList.NewCustomerRequirementwithECCNNotify);
            string strOut = strRaw;
            strOut = PrintPage.ReplaceVariable(strOut, "CUSREQ_NUMBER", cr.CustomerRequirementNumber);
            strOut = PrintPage.ReplaceVariable(strOut, "PART", cr.Part);
            strOut = PrintPage.ReplaceVariable(strOut, "ECCNCODE", cr.ECCNCode);
            strOut = PrintPage.ReplaceVariable(strOut, "WARNINGMESSAGE", cr.WarningMessage);
            strOut = PrintPage.ReplaceVariable(strOut, "ECCNMESSAGE", cr.EccnMessage);
            //HttpRequest Request = System.Web.HttpContext.Current.Request;
            //string baseUrl = Request.Url.Scheme + "://" + Request.Url.Authority + Request.ApplicationPath.TrimEnd('/') + "/";
            strOut = PrintPage.ReplaceVariable(strOut, "HYPERLINK", PageManager.GotoURL_CustomerRequirement(cr.CustomerRequirementId).Replace("~/", string.Format(Convert.ToString(System.Configuration.ConfigurationManager.AppSettings["redirecturi"])) + "/"));
            return strOut;
        }
        //public static string GetMessage_NotifyPurchaseRequestBom(string bomCode, string bomName, int? bomId, string bomCompanyName, int? bomCompanyNo)
        //{
        //    string strRaw = GetRawTemplateText(TemplateList.NotifyPurchaseRequestBom);
        //    string strOut = strRaw;
        //    // strOut = PrintPage.ReplaceVariable(strOut, "IPO_BOM_NUMBER", bomCode);
        //    strOut = PrintPage.ReplaceVariable(strOut, "IPO_BOM_NUMBER", bomName);// new code added by Prakash. Now Send Bom Name for HUBRFQ.
        //    strOut = PrintPage.ReplaceVariable(strOut, "HYPERLINK", PageManager.GotoURL_BOM(bomId).Replace("~/", ""));
        //    return strOut;
        //}
        public static string GetMessage_ReverseLogistics(string bomCode, string bomName, int? bomId, string bomCompanyName, int? bomCompanyNo,string PartNo,string PartLinesRL)
        {
            string strRaw = GetRawTemplateText(TemplateList.NotifyReverseLogistics);
            string strOut = strRaw;
            strOut = PrintPage.ReplaceVariable(strOut, "IPO_BOM_NUMBER", bomName);
            strOut = PrintPage.ReplaceVariable(strOut, "PART", PartNo);
            strOut = PrintPage.ReplaceVariable(strOut, "BOMCODE", bomName);
            strOut = PrintPage.ReplaceVariable(strOut, "COMPANYNAME", bomCompanyName);
            //strOut = PrintPage.ReplaceVariable(strOut, "HYPERLINK", PageManager.GotoURL_BOM(bomId).Replace("~/", ""));
            //string baseUrl = Request.Url.Scheme + "://" + Request.Url.Authority + Request.ApplicationPath.TrimEnd('/') + "/";
            strOut = PrintPage.ReplaceVariable(strOut, "HYPERLINK", PageManager.GotoURL_BOM(bomId).Replace("~/", string.Format(Convert.ToString(System.Configuration.ConfigurationManager.AppSettings["redirecturi"])) + "/"));
            strOut = PrintPage.ReplaceVariable(strOut, "PARTLINESRL", PartLinesRL);
            return strOut;
        }

        public static string GetMessage_NotifySalesOrderECCN(BLL.SalesOrderLine so)
        {
            //NotifyECCNSalesOrder
            string strRaw = GetRawTemplateText(TemplateList.NotifyECCNSalesOrder);
            string strOut = strRaw;
            strOut = PrintPage.ReplaceVariable(strOut, "SO_NUMBER", so.SalesOrderNumber);
            strOut = PrintPage.ReplaceVariable(strOut, "PART", so.Part);
            strOut = PrintPage.ReplaceVariable(strOut, "ECCNCODE", so.ECCNCode);
            strOut = PrintPage.ReplaceVariable(strOut, "WARNINGMESSAGE", so.WarningMessage);
            strOut = PrintPage.ReplaceVariable(strOut, "ECCNMESSAGE", so.EccnMessage);
            //HttpRequest Request = System.Web.HttpContext.Current.Request;
            //string baseUrl = Request.Url.Scheme + "://" + Request.Url.Authority + Request.ApplicationPath.TrimEnd('/') + "/";
            strOut = PrintPage.ReplaceVariable(strOut, "HYPERLINK", PageManager.GotoURL_SalesOrder(so.SalesOrderNo).Replace("~/", string.Format(Convert.ToString(System.Configuration.ConfigurationManager.AppSettings["redirecturi"])) + "/" )); //[002]
            return strOut;
        }

        public static string GetMessage_NotifyPOECCN(BLL.PurchaseOrderLine so)
        {
            //NotifyECCNSalesOrder
            string strRaw = GetRawTemplateText(TemplateList.NotifyECCNPO);
            string strOut = strRaw;
            strOut = PrintPage.ReplaceVariable(strOut, "PO_NUMBER", so.PurchaseOrderNumber);
            strOut = PrintPage.ReplaceVariable(strOut, "PART", so.Part);
            strOut = PrintPage.ReplaceVariable(strOut, "ECCNCODE", so.ECCNCode);
            strOut = PrintPage.ReplaceVariable(strOut, "WARNINGMESSAGE", so.EccnWarningMessage);
            strOut = PrintPage.ReplaceVariable(strOut, "ECCNMESSAGE", so.EccnMessage);
            strOut = PrintPage.ReplaceVariable(strOut, "HYPERLINK", PageManager.GotoURL_PurchaseOrder(so.PurchaseOrderNo).Replace("~/", string.Format(Convert.ToString(System.Configuration.ConfigurationManager.AppSettings["redirecturi"])) + "/")); //[002]
            return strOut;
        }

        public static string GetMessage_NewQuote(BLL.Quote qt) {
			string strRaw = GetRawTemplateText(TemplateList.NewQuote);
			string strOut = strRaw;
			strOut = PrintPage.ReplaceVariable(strOut, "QUOTE_NUMBER", qt.QuoteNumber);
			strOut = PrintPage.ReplaceVariable(strOut, "CUSTOMER", qt.CompanyName);
			strOut = PrintPage.ReplaceVariable(strOut, "CONTACT", qt.ContactName);
			strOut = PrintPage.ReplaceVariable(strOut, "CURRENCY", Functions.FormatCurrencyDescription(qt.CurrencyDescription, qt.CurrencyCode));
			strOut = PrintPage.ReplaceVariable(strOut, "DATE", Functions.FormatDate(qt.DateQuoted));
			strOut = PrintPage.ReplaceVariable(strOut, "HYPERLINK", PageManager.GotoURL_Quote(qt.QuoteId).Replace("~/", ""));
			return strOut;
		}

		public static string GetMessage_NewSupplierRMA(BLL.SupplierRma srma) {
			string strRaw = GetRawTemplateText(TemplateList.NewSupplierRMA);
			string strOut = strRaw;
			strOut = PrintPage.ReplaceVariable(strOut, "SRMA_NUMBER", srma.SupplierRMANumber);
			strOut = PrintPage.ReplaceVariable(strOut, "SUPPLIER", srma.CompanyName);
			strOut = PrintPage.ReplaceVariable(strOut, "CONTACT", srma.ContactName);
			strOut = PrintPage.ReplaceVariable(strOut, "PO_NUMBER", srma.PurchaseOrderNumber);
			strOut = PrintPage.ReplaceVariable(strOut, "AUTHORISED_BY", srma.AuthoriserName);
			strOut = PrintPage.ReplaceVariable(strOut, "HYPERLINK", PageManager.GotoURL_SupplierRMA(srma.SupplierRMAId).Replace("~/", ""));
			return strOut;
		}

		public static string GetMessage_NewCustomerRMA(BLL.CustomerRma crma) {
			string strRaw = GetRawTemplateText(TemplateList.NewCustomerRMA);
			string strOut = strRaw;
			strOut = PrintPage.ReplaceVariable(strOut, "CRMA_NUMBER", crma.CustomerRMANumber);
			strOut = PrintPage.ReplaceVariable(strOut, "CUSTOMER", crma.CompanyName);
			strOut = PrintPage.ReplaceVariable(strOut, "CONTACT", crma.ContactName);
			strOut = PrintPage.ReplaceVariable(strOut, "INVOICE_NUMBER", crma.InvoiceNumber);
			strOut = PrintPage.ReplaceVariable(strOut, "AUTHORISED_BY", crma.AuthoriserName);
			strOut = PrintPage.ReplaceVariable(strOut, "HYPERLINK", PageManager.GotoURL_CustomerRMA(crma.CustomerRMAId).Replace("~/", ""));
			return strOut;
		}

		public static string GetMessage_NewGoodsIn(BLL.GoodsIn gi) {
			string strRaw = GetRawTemplateText(TemplateList.NewGoodsIn);
			string strOut = strRaw;
			strOut = PrintPage.ReplaceVariable(strOut, "GI_NUMBER", gi.GoodsInNumber);
			strOut = PrintPage.ReplaceVariable(strOut, "SUPPLIER", gi.CompanyName);
			strOut = PrintPage.ReplaceVariable(strOut, "PO_NUMBER", gi.PurchaseOrderNumber);
			strOut = PrintPage.ReplaceVariable(strOut, "HYPERLINK", PageManager.GotoURL_GoodsIn(gi.GoodsInId).Replace("~/", ""));
			return strOut;
		}

        public static string GetMessage_NotifyGoodsIn(BLL.GoodsIn gi)
        {
            string strRaw = GetRawTemplateText(TemplateList.NotifyGoodsIn);
            string strOut = strRaw;
            strOut = PrintPage.ReplaceVariable(strOut, "GI_NUMBER", gi.GoodsInNumber);
            strOut = PrintPage.ReplaceVariable(strOut, "SUPPLIER", gi.CompanyName);
            strOut = PrintPage.ReplaceVariable(strOut, "RECEIVED_BY", gi.ReceiverName);
			strOut = PrintPage.ReplaceVariable(strOut, "PO_NUMBER", gi.PurchaseOrderNumber);
            strOut = PrintPage.ReplaceVariable(strOut, "HYPERLINK", PageManager.GotoURL_GoodsIn(gi.GoodsInId).Replace("~/", ""));
            return strOut;
        }

        public static string GetMessage_ReceivedPurchaseOrder(BLL.GoodsIn gi)
        {
			string strRaw = GetRawTemplateText(TemplateList.ReceivedPurchaseOrder);
			string strOut = strRaw;
			strOut = PrintPage.ReplaceVariable(strOut, "GI_NUMBER", gi.GoodsInNumber);
			strOut = PrintPage.ReplaceVariable(strOut, "SUPPLIER", gi.CompanyName);
			strOut = PrintPage.ReplaceVariable(strOut, "PO_NUMBER", gi.PurchaseOrderNumber);
			strOut = PrintPage.ReplaceVariable(strOut, "HYPERLINK", PageManager.GotoURL_GoodsIn(gi.GoodsInId).Replace("~/", ""));
			return strOut;
		}

		public static string GetMessage_NewSalesOrder(BLL.SalesOrder so) {
			string strRaw = GetRawTemplateText(TemplateList.NewSalesOrder);
			string strOut = strRaw;
			strOut = PrintPage.ReplaceVariable(strOut, "SO_NUMBER", so.SalesOrderNumber);
			strOut = PrintPage.ReplaceVariable(strOut, "CUSTOMER", so.CompanyName);
			strOut = PrintPage.ReplaceVariable(strOut, "CONTACT", so.ContactName);
			strOut = PrintPage.ReplaceVariable(strOut, "SALESMAN", so.SalesmanName);
			strOut = PrintPage.ReplaceVariable(strOut, "HYPERLINK", PageManager.GotoURL_SalesOrder(so.SalesOrderId).Replace("~/", ""));
			return strOut;
		}

        public static string GetMessage_NotifySalesOrder(BLL.SalesOrder so)
        {
            string strRaw = GetRawTemplateText(TemplateList.NotifySalesOrder);
            string strOut = strRaw;
            strOut = PrintPage.ReplaceVariable(strOut, "SO_NUMBER", so.SalesOrderNumber);
            strOut = PrintPage.ReplaceVariable(strOut, "CUSTOMER", so.CompanyName);
            strOut = PrintPage.ReplaceVariable(strOut, "CONTACT", so.ContactName);
            strOut = PrintPage.ReplaceVariable(strOut, "SALESMAN", so.SalesmanName);
            strOut = PrintPage.ReplaceVariable(strOut, "HYPERLINK", PageManager.GotoURL_SalesOrder(so.SalesOrderId).Replace("~/", ""));
            return strOut;
        }

        public static string GetMessage_NewPurchaseOrder(BLL.PurchaseOrder po)
        {
			string strRaw = GetRawTemplateText(TemplateList.NewPurchaseOrder);
			string strOut = strRaw;
			strOut = PrintPage.ReplaceVariable(strOut, "PO_NUMBER", po.PurchaseOrderNumber);
			strOut = PrintPage.ReplaceVariable(strOut, "SUPPLIER", po.CompanyName);
			strOut = PrintPage.ReplaceVariable(strOut, "CONTACT", po.ContactName);
			strOut = PrintPage.ReplaceVariable(strOut, "BUYER", po.BuyerName);
			strOut = PrintPage.ReplaceVariable(strOut, "HYPERLINK", PageManager.GotoURL_PurchaseOrder(po.PurchaseOrderId).Replace("~/", ""));
			return strOut;
		}

        public static string GetMessage_NotifyPOTOCreateIPO(BLL.PurchaseOrder po) 
        {
            string strRaw = GetRawTemplateText(TemplateList.NotifyCreatePOMail);
            string strOut = strRaw;
            strOut = PrintPage.ReplaceVariable(strOut, "IPO_NUMBER", po.InternalPurchaseOrderNumber);
            strOut = PrintPage.ReplaceVariable(strOut, "PO_NUMBER", po.PurchaseOrderNumber);
            //strOut = PrintPage.ReplaceVariable(strOut, "SUPPLIER", po.CompanyName);
            //strOut = PrintPage.ReplaceVariable(strOut, "CONTACT", po.ContactName);
            strOut = PrintPage.ReplaceVariable(strOut, "BUYER", po.IPOBuyerName);
            strOut = PrintPage.ReplaceVariable(strOut, "HYPERLINK", PageManager.GotoURL_PurchaseOrder(po.PurchaseOrderId).Replace("~/", ""));
            //strOut = PrintPage.ReplaceVariable(strOut, "HYPERLINK", ("Ord_InternalPODetail.aspx?ipo=" + po.InternalPurchaseOrderNo));
            return strOut;
        }
        public static string GetMessage_NotifyCreateIPO(BLL.PurchaseOrder po)
        {
            string strRaw = GetRawTemplateText(TemplateList.NotifyCreateIPO);
            string strOut = strRaw;
            strOut = PrintPage.ReplaceVariable(strOut, "IPO_NUMBER", po.InternalPurchaseOrderNumber);
            strOut = PrintPage.ReplaceVariable(strOut, "PO_NUMBER", po.PurchaseOrderNumber);
            //strOut = PrintPage.ReplaceVariable(strOut, "SUPPLIER", po.CompanyName);
            //strOut = PrintPage.ReplaceVariable(strOut, "CONTACT", po.ContactName);
            strOut = PrintPage.ReplaceVariable(strOut, "BUYER", po.BuyerName);
            //strOut = PrintPage.ReplaceVariable(strOut, "HYPERLINK", PageManager.GotoURL_PurchaseOrder(po.PurchaseOrderId).Replace("~/", ""));
            strOut = PrintPage.ReplaceVariable(strOut, "HYPERLINK", ("Ord_InternalPODetail.aspx?ipo=" + po.InternalPurchaseOrderId));
            return strOut;
        }
       
        public static string GetMessage_NotifyPOApprovedNew(BLL.PurchaseOrder po)
        {
            string strRaw = GetRawTemplateText(TemplateList.NotifyPOApprovedNew);
            string strOut = strRaw;
            strOut = PrintPage.ReplaceVariable(strOut, "IPO_NUMBER", po.InternalPurchaseOrderNumber);
            strOut = PrintPage.ReplaceVariable(strOut, "PO_NUMBER", po.PurchaseOrderNumber);
            strOut = PrintPage.ReplaceVariable(strOut, "BUYER", po.BuyerName);
            //strOut = PrintPage.ReplaceVariable(strOut, "HYPERLINK", PageManager.GotoURL_PurchaseOrder(po.InternalPurchaseOrderId).Replace("~/", ""));
            strOut = PrintPage.ReplaceVariable(strOut, "HYPERLINK", ("Ord_InternalPODetail.aspx?ipo="+ po.InternalPurchaseOrderId));
            return strOut;
        }

        public static string GetMessage_NotifyReleaseBom(int? bomId, string bomCode, string bomName, string bomCompanyName, int? bomCompanyNo)
        {
            string strRaw = GetRawTemplateText(TemplateList.NotifyReleaseBom);
            string strOut = strRaw;
            //strOut = PrintPage.ReplaceVariable(strOut, "IPO_BOM_NUMBER", bomCode);
            strOut = PrintPage.ReplaceVariable(strOut, "IPO_BOM_NUMBER", bomName);// new code added by Prakash. Now Send Bom Name for HUBRFQ.
            strOut = PrintPage.ReplaceVariable(strOut, "HYPERLINK", PageManager.GotoURL_BOM(bomId).Replace("~/", ""));
            return strOut;
        }
        public static string GetMessage_NotifyReleaseBomManager(int? bomId, string bomCode, string bomName, string bomCompanyName, int? bomCompanyNo)
        {
            string strRaw = GetRawTemplateText(TemplateList.NotifyReleaseBomManager);
            string strOut = strRaw;
            //strOut = PrintPage.ReplaceVariable(strOut, "IPO_BOM_NUMBER", bomCode);
            strOut = PrintPage.ReplaceVariable(strOut, "IPO_BOM_NUMBER", bomName);// new code added by Prakash. Now Send Bom Name for HUBRFQ.
            strOut = PrintPage.ReplaceVariable(strOut, "HYPERLINK", PageManager.GotoURL_BOMManager(bomId).Replace("~/", ""));
            return strOut;
        }
        public static string GetMessage_NotifyPurchaseRequestBomManager(string bomCode, string bomName, int? bomId, string bomCompanyName, int? bomCompanyNo)
        {
            string strRaw = GetRawTemplateText(TemplateList.NotifyPurchaseRequestBomManager);
            string strOut = strRaw;
            // strOut = PrintPage.ReplaceVariable(strOut, "IPO_BOM_NUMBER", bomCode);
            strOut = PrintPage.ReplaceVariable(strOut, "IPO_BOM_NUMBER", bomName);// new code added by Prakash. Now Send Bom Name for HUBRFQ.
            strOut = PrintPage.ReplaceVariable(strOut, "HYPERLINK", PageManager.GotoURL_BOMManagerSourcing(bomId).Replace("~/", ""));
            return strOut;
        }
        public static string GetMessage_NotifyCloseBomManager(int? bomId, string bomCode, string bomName, string bomCompanyName, int? bomCompanyNo)
        {
            string strRaw = GetRawTemplateText(TemplateList.NotifyCloseBomManager);
            string strOut = strRaw;
            //strOut = PrintPage.ReplaceVariable(strOut, "IPO_BOM_NUMBER", bomCode);
            strOut = PrintPage.ReplaceVariable(strOut, "IPO_BOM_NUMBER", bomName);// new code added by Prakash. Now Send Bom Name for HUBRFQ.
            strOut = PrintPage.ReplaceVariable(strOut, "HYPERLINK", PageManager.GotoURL_BOMManager(bomId).Replace("~/", ""));
            return strOut;
        }
        public static string GetMessage_NotifyPurchaseRequestBom(string bomCode, string bomName,int? bomId,string bomCompanyName,int? bomCompanyNo) 
        {
            string strRaw = GetRawTemplateText(TemplateList.NotifyPurchaseRequestBom);
            string strOut = strRaw;
           // strOut = PrintPage.ReplaceVariable(strOut, "IPO_BOM_NUMBER", bomCode);
            strOut = PrintPage.ReplaceVariable(strOut, "IPO_BOM_NUMBER", bomName);// new code added by Prakash. Now Send Bom Name for HUBRFQ.
            strOut = PrintPage.ReplaceVariable(strOut, "HYPERLINK", PageManager.GotoURL_BOM(bomId).Replace("~/", ""));
            return strOut;
        }
        public static string GetMessage_NotifyPurchaseOrder(BLL.PurchaseOrder po) {
            string strRaw = GetRawTemplateText(TemplateList.NotifyPurchaseOrder);
            string strOut = strRaw;
            strOut = PrintPage.ReplaceVariable(strOut, "PO_NUMBER", po.PurchaseOrderNumber);
            strOut = PrintPage.ReplaceVariable(strOut, "SUPPLIER", po.CompanyName);
            strOut = PrintPage.ReplaceVariable(strOut, "CONTACT", po.ContactName);
            strOut = PrintPage.ReplaceVariable(strOut, "BUYER", po.BuyerName);
            strOut = PrintPage.ReplaceVariable(strOut, "HYPERLINK", PageManager.GotoURL_PurchaseOrder(po.PurchaseOrderId).Replace("~/", ""));
            return strOut;
        }

        public static string GetMessage_NewInvoice(BLL.Invoice inv) {
			string strRaw = GetRawTemplateText(TemplateList.NewInvoice);
			string strOut = strRaw;
			strOut = PrintPage.ReplaceVariable(strOut, "INVOICE_NUMBER", inv.InvoiceNumber);
			strOut = PrintPage.ReplaceVariable(strOut, "CUSTOMER", inv.CompanyName);
			strOut = PrintPage.ReplaceVariable(strOut, "CONTACT", inv.ContactName);
			strOut = PrintPage.ReplaceVariable(strOut, "SO_NUMBER", inv.SalesOrderNumber);
			strOut = PrintPage.ReplaceVariable(strOut, "CUSTOMER_PO", inv.CustomerPO);
			strOut = PrintPage.ReplaceVariable(strOut, "SALESMAN", inv.SalesmanName);
			strOut = PrintPage.ReplaceVariable(strOut, "HYPERLINK", PageManager.GotoURL_Invoice(inv.InvoiceId).Replace("~/", ""));
			return strOut;
		}

		public static string GetMessage_NewCredit(BLL.Credit credit) {
			string strRaw = GetRawTemplateText(TemplateList.NewCredit);
			string strOut = strRaw;
			strOut = PrintPage.ReplaceVariable(strOut, "CREDIT_NUMBER", credit.CreditNumber);
			strOut = PrintPage.ReplaceVariable(strOut, "CUSTOMER", credit.CompanyName);
			strOut = PrintPage.ReplaceVariable(strOut, "CONTACT", credit.ContactName);
			strOut = PrintPage.ReplaceVariable(strOut, "INV_NUMBER", credit.InvoiceNumber);
			strOut = PrintPage.ReplaceVariable(strOut, "CUSTOMER_PO", credit.CustomerPO);
			strOut = PrintPage.ReplaceVariable(strOut, "SALESMAN", credit.SalesmanName);
			strOut = PrintPage.ReplaceVariable(strOut, "HYPERLINK", PageManager.GotoURL_CreditNote(credit.CreditId).Replace("~/", ""));
			return strOut;
		}

		public static string GetMessage_NewDebit(BLL.Debit debit) {
			string strRaw = GetRawTemplateText(TemplateList.NewDebit);
			string strOut = strRaw;
			strOut = PrintPage.ReplaceVariable(strOut, "DEBIT_NUMBER", debit.DebitNumber);
			strOut = PrintPage.ReplaceVariable(strOut, "SUPPLIER", debit.CompanyName);
			strOut = PrintPage.ReplaceVariable(strOut, "CONTACT", debit.ContactName);
			strOut = PrintPage.ReplaceVariable(strOut, "PO_NUMBER", debit.PurchaseOrderNumber);
			strOut = PrintPage.ReplaceVariable(strOut, "SRMA_NUMBER", debit.SupplierRMANumber);
			strOut = PrintPage.ReplaceVariable(strOut, "SUPPLIER_INV", debit.SupplierInvoice);
			strOut = PrintPage.ReplaceVariable(strOut, "BUYER", debit.BuyerName);
			strOut = PrintPage.ReplaceVariable(strOut, "HYPERLINK", PageManager.GotoURL_DebitNote(debit.DebitId).Replace("~/", ""));
			return strOut;
		}

		public static string GetMessage_RFQ(string strPartNo, int? intQuantity, string strROHS, string strManufacturer, string strProduct, string strPackage, string strDateCode, DateTime? dtmRequired) {
            //public static string GetMessage_RFQ(string strPartNo, int? intQuantity, string strROHS, string strManufacturer, string strProduct, string strPackage, string strDateCode, DateTime? dtmRequired, string strTargetPrice) { //sk010509
			string strRaw = GetRawTemplateText(TemplateList.RFQ);
			string strData = "";
			strData += AddField("PartNo", strPartNo);
			strData += AddField("Quantity", intQuantity);
			strData += AddField("ROHS", strROHS);
			strData += AddField("Manufacturer", strManufacturer);
			strData += AddField("Product", strProduct);
			strData += AddField("Package", strPackage);
			strData += AddField("DateCode", strDateCode);
			if (dtmRequired != null) strData += AddField("DateRequired", Functions.FormatDate(dtmRequired, true));
            //strData += AddField("TargetPrice", strTargetPrice); //sk010509
			return PrintPage.ReplaceVariable(strRaw, "DATA", strData);
		}
        //[001] code start
        /// <summary>
        /// Get mail template of supplier invoice
        /// </summary>
        /// <param name="si"></param>
        /// <returns></returns>
        public static string GetMessage_NewSupplierInvoice(BLL.SupplierInvoice si)
        {
            string strRaw = GetRawTemplateText(TemplateList.NewSupplierInvoice);
            string strOut = strRaw;
            strOut = PrintPage.ReplaceVariable(strOut, "SI_NUMBER", si.SupplierInvoiceNumber);
            strOut = PrintPage.ReplaceVariable(strOut, "SUPPLIER", si.SupplierName);
            strOut = PrintPage.ReplaceVariable(strOut, "HYPERLINK", PageManager.GotoURL_SupplierInvoice(si.SupplierInvoiceID).Replace("~/", ""));
            return strOut;
        }
        public static string GetMessage_NotifySupplierInvoice(BLL.SupplierInvoice si)
        {
            string strRaw = GetRawTemplateText(TemplateList.NotifySupplierInvoice);
            string strOut = strRaw;
            strOut = PrintPage.ReplaceVariable(strOut, "SI_NUMBER", si.SupplierInvoiceNumber);
            strOut = PrintPage.ReplaceVariable(strOut, "SUPPLIER", si.SupplierName);
            strOut = PrintPage.ReplaceVariable(strOut, "HYPERLINK", PageManager.GotoURL_SupplierInvoice(si.SupplierInvoiceID).Replace("~/", ""));
            return strOut;
        }
        //[001] code end

        //[002] code start
        public static string GetMessage_NotifyNPR(BLL.ReportNPR npr)
        {
            string strRaw = GetRawTemplateText(TemplateList.NotifyNPR);
            string strOut = strRaw;
            strOut = PrintPage.ReplaceVariable(strOut, "PO_NO", npr.PurchaseOrderNumber);
            strOut = PrintPage.ReplaceVariable(strOut, "NPR_NO", npr.NPRNo);
            strOut = PrintPage.ReplaceVariable(strOut, "SUPPLIER", npr.Supplier);
            strOut = PrintPage.ReplaceVariable(strOut, "CLIENT", npr.ClientName);
            strOut = PrintPage.ReplaceVariable(strOut, "NC_DETAIL", npr.RejectionReason);

            //strOut = PrintPage.ReplaceVariable(strOut, "FUNCTION", string.Format("$RGT_openNPRWindow({0} ,{1})", npr.GoodsInLineId, npr.NPRId));
            return strOut;
        }
        public static string GetMessage_NotifyNPROutLook(string strUrl)
        {
            string strRaw = GetRawTemplateText(TemplateList.NPREmail);
            string strOut = strRaw;
            strOut = PrintPage.ReplaceVariable(strOut, "HYPERLINK", strUrl);
            return strOut;
        }
        //[002] code end

        public static string GetMessage_NotifyEPR(BLL.EPR epr)
        {
            string strRaw = GetRawTemplateText(TemplateList.NotifyEPR);
            string strOut = strRaw;
            strOut = PrintPage.ReplaceVariable(strOut, "EPR_NO", epr.PurchaseOrderNumber + "-" + epr.EPRId);
            strOut = PrintPage.ReplaceVariable(strOut, "SUPPLIER", epr.CompanyName);
            strOut = PrintPage.ReplaceVariable(strOut, "CLIENT", SessionManager.ClientName);
            //strOut = PrintPage.ReplaceVariable(strOut, "FUNCTION", string.Format("$RGT_openNPRWindow({0} ,{1})", npr.GoodsInLineId, npr.NPRId));
            return strOut;
        }

        public static string GetMessage_SOCompanyApproved(BLL.Company cmp)
        {
            string strRaw = GetRawTemplateText(TemplateList.CompanyApproved);
            //string strOut = strRaw;
            string strOut = strRaw.Substring(0, strRaw.LastIndexOf("Supplier No: [#SUPPLIERNO#]") + 0);
            strOut = PrintPage.ReplaceVariable(strOut, "CUSTOMERNAME", cmp.CompanyName);
            strOut = PrintPage.ReplaceVariable(strOut, "SALESPERSONNAME", cmp.SalesmanName);
            strOut = PrintPage.ReplaceVariable(strOut, "CURRENCY", cmp.SOCurrencyCode);
            strOut = PrintPage.ReplaceVariable(strOut, "CUSTOMERNO", cmp.CustomerCode);
            strOut = PrintPage.ReplaceVariable(strOut, "TERM", cmp.SOTermsName);
            strOut = PrintPage.ReplaceVariable(strOut, "APPROVEDBY", SessionManager.LoginFullName);
            strOut = PrintPage.ReplaceVariable(strOut, "CLIENTNAME", SessionManager.ClientName);
            
            return strOut;
        }
        public static string GetMessage_POCompanyApproved(BLL.Company cmp)
        {
            string strRaw = GetRawTemplateText(TemplateList.CompanyApproved);
            string strOut = strRaw;
            strOut = PrintPage.ReplaceVariable(strOut, "CUSTOMERNAME", cmp.CompanyName);
            strOut = PrintPage.ReplaceVariable(strOut, "SALESPERSONNAME", cmp.DefaultPOContactName);
            strOut = PrintPage.ReplaceVariable(strOut, "CURRENCY", Functions.FormatCurrencyDescription(cmp.POCurrencyDescription, cmp.POCurrencyCode));
            strOut = PrintPage.ReplaceVariable(strOut, "CUSTOMERNO", cmp.CustomerCode);
            strOut = PrintPage.ReplaceVariable(strOut, "TERM", cmp.POTermsName);
            strOut = PrintPage.ReplaceVariable(strOut, "APPROVEDBY", SessionManager.LoginFullName);
            strOut = PrintPage.ReplaceVariable(strOut, "CLIENTNAME", SessionManager.ClientName);
            strOut = PrintPage.ReplaceVariable(strOut, "SUPPLIERNO", cmp.SupplierCode);

            return strOut;
        }
       

		private static string AddField(string strTitle, object objData) {
			if (objData == null) return "";
			if (objData.ToString().Trim().Length == 0) return "";
			return string.Format("{0}: {1}\n", Functions.GetGlobalResource("FormFields", strTitle), objData);
		}

        public static string GetMessage_CertificateOfConformance(string strMessage,string strRegards)
        {
            string strRaw = GetRawTemplateText(TemplateList.COCBodyMessage);
            string strOut = strRaw;
            strOut = string.Format(strRaw, strMessage, strRegards).Replace("\r\n", "<br />"); ;
            return strOut;
        }
        public static string GetMessage_InvoiceIncludeCofc(string strMessage, string strRegards)
        {
            string strRaw = GetRawTemplateText(TemplateList.InvoiceWithCofc);
            string strOut = strRaw;
            strOut = string.Format(strRaw, strMessage, strRegards).Replace("\r\n", "<br />"); ;
            return strOut;
        }
        public static string GetMessage_Invoice(string strMessage, string strRegards)
        {
            string strRaw = GetRawTemplateText(TemplateList.InvoicePDF);
            string strOut = strRaw;
            strOut = string.Format(strRaw, strMessage, strRegards).Replace("\r\n", "<br />"); ;
            return strOut;
        }

        public static string GetMessage_PackingSlip(string strMessage, string strRegards)
        {
            string strRaw = GetRawTemplateText(TemplateList.PackingSlip);
            string strOut = strRaw;
            strOut = string.Format(strRaw, strMessage, strRegards).Replace("\r\n", "<br />"); ;
            return strOut;
        }
        public static string GetMessage_CommercialInvoice(string strMessage, string strRegards)
        {
            string strRaw = GetRawTemplateText(TemplateList.CommercialInvoice);
            string strOut = strRaw;
            strOut = string.Format(strRaw, strMessage, strRegards).Replace("\r\n", "<br />"); ;
            return strOut;
        }

        public static string GetMessage_SalesOrder(string strMessage, string strRegards)
        {
            string strRaw = GetRawTemplateText(TemplateList.SalesOrder);
            string strOut = strRaw;
            strOut = string.Format(strRaw, strMessage, strRegards).Replace("\r\n", "<br />"); ;
            return strOut;
        }

        public static string GetMessage_PurchaseOrder(string strMessage, string strRegards)
        {
            string strRaw = GetRawTemplateText(TemplateList.PurchaseOrder);
            string strOut = strRaw;
            strOut = string.Format(strRaw, strMessage, strRegards).Replace("\r\n", "<br />"); ;
            return strOut;
        }

        public static string GetMessage_EmailQuote(string strMessage, string strRegards)
        {
            string strRaw = GetRawTemplateText(TemplateList.EmailQuote);
            string strOut = strRaw;
            strOut = string.Format(strRaw, strMessage, strRegards).Replace("\r\n", "<br />"); ;
            return strOut;
        }

        public static string GetMessage_PackingSlipIncludeCofc(string strMessage, string strRegards)
        {
            string strRaw = GetRawTemplateText(TemplateList.PackingSlipWithCofc);
            string strOut = strRaw;
            strOut = string.Format(strRaw, strMessage, strRegards).Replace("\r\n", "<br />"); ;
            return strOut;
        }

        public static string GetMessage_SOProForma(string strMessage, string strRegards)
        {
            string strRaw = GetRawTemplateText(TemplateList.SOProForma);
            string strOut = strRaw;
            strOut = string.Format(strRaw, strMessage, strRegards).Replace("\r\n", "<br />"); ;
            return strOut;
        }
        public static string GetMessage_ClientInvoice(string strMessage, string strRegards)
        {
            string strRaw = GetRawTemplateText(TemplateList.ClientInvoicePDF);
            string strOut = strRaw;
            strOut = string.Format(strRaw, strMessage, strRegards).Replace("\r\n", "<br />"); ;
            return strOut;
        }

        public static string GetMessage_InternalPurchaseOrder(string strMessage, string strRegards)
        {
            string strRaw = GetRawTemplateText(TemplateList.InternalPurchaseOrder);
            string strOut = strRaw;
            strOut = string.Format(strRaw, strMessage, strRegards).Replace("\r\n", "<br />"); ;
            return strOut;
        }

        public static string GetMessage_CreditNote(string strMessage, string strRegards)
        {
            string strRaw = GetRawTemplateText(TemplateList.CreditNote);
            string strOut = strRaw;
            strOut = string.Format(strRaw, strMessage, strRegards).Replace("\r\n", "<br />"); ;
            return strOut;
        }
        public static string GetMessage_DebitNote(string strMessage, string strRegards)
        {
            string strRaw = GetRawTemplateText(TemplateList.DebitNote);
            string strOut = strRaw;
            strOut = string.Format(strRaw, strMessage, strRegards).Replace("\r\n", "<br />"); ;
            return strOut;
        }
        public static string GetMessage_HUBDebitNote(string strMessage, string strRegards)
        {
            string strRaw = GetRawTemplateText(TemplateList.HubDebit);
            string strOut = strRaw;
            strOut = string.Format(strRaw, strMessage, strRegards).Replace("\r\n", "<br />"); ;
            return strOut;
        }
        public static string GetMessage_CreditNoteIssueNotification_Salesman(BLL.Credit credit)
        {
            string strRaw = GetRawTemplateText(TemplateList.CreditNoteIssueNotification);
            string strOut = strRaw;
            strOut = PrintPage.ReplaceVariable(strOut, "CREDIT_NUMBER", credit.CreditNumber);
            strOut = PrintPage.ReplaceVariable(strOut, "CUSTOMER", credit.CompanyName);
            strOut = PrintPage.ReplaceVariable(strOut, "CONTACT", credit.ContactName);
            strOut = PrintPage.ReplaceVariable(strOut, "INV_NUMBER", credit.InvoiceNumber);
            strOut = PrintPage.ReplaceVariable(strOut, "CUSTOMER_PO", credit.CustomerPO);
            strOut = PrintPage.ReplaceVariable(strOut, "SALESMAN", credit.SalesmanName);
            strOut = PrintPage.ReplaceVariable(strOut, "HYPERLINK", PageManager.GotoURL_CreditNote(credit.CreditId).Replace("~/", ""));
            return strOut;
        }
        public static string GetMessage_CreditNoteIssueNotification_AdditionalSalesman(BLL.Credit credit)
        {
            string strRaw = GetRawTemplateText(TemplateList.CreditNoteIssueNotification);
            string strOut = strRaw;
            strOut = PrintPage.ReplaceVariable(strOut, "CREDIT_NUMBER", credit.CreditNumber);
            strOut = PrintPage.ReplaceVariable(strOut, "CUSTOMER", credit.CompanyName);
            strOut = PrintPage.ReplaceVariable(strOut, "CONTACT", credit.ContactName);
            strOut = PrintPage.ReplaceVariable(strOut, "INV_NUMBER", credit.InvoiceNumber);
            strOut = PrintPage.ReplaceVariable(strOut, "CUSTOMER_PO", credit.CustomerPO);
            strOut = PrintPage.ReplaceVariable(strOut, "SALESMAN", credit.Salesman2Name);
            strOut = PrintPage.ReplaceVariable(strOut, "HYPERLINK", PageManager.GotoURL_CreditNote(credit.CreditId).Replace("~/", ""));
            return strOut;
        }
        public static string GetMessage_CreditLimitApplication_Salesman(int CompanyID, string CompanyName, string CAF, string ApplicationStatus)
        {
            string strRaw = GetRawTemplateText(TemplateList.CreditLimitApplicationNotification);
            string strOut = strRaw;
            strOut = PrintPage.ReplaceVariable(strOut, "CAF", CAF);
            strOut = PrintPage.ReplaceVariable(strOut, "APPLICATIONSTATUS", ApplicationStatus);
            strOut = PrintPage.ReplaceVariable(strOut, "COMPANYNAME", CompanyName);
            strOut = PrintPage.ReplaceVariable(strOut, "HYPERLINK", PageManager.GotoURL_CompanyDetail(CompanyID).Replace("~/", ""));
            return strOut;
        }
        public enum TemplateList {
			NewCustomerRequirement,
			NewQuote,
			NewSupplierRMA,
			NewCustomerRMA,
			NewGoodsIn,
			NewSalesOrder,
			NewPurchaseOrder,
			NewInvoice,
			NewCredit,
			NewDebit,
			RFQ,
			NotifySalesOrder,
			ReceivedPurchaseOrder,
			NotifyGoodsIn,
			NotifyPurchaseOrder,
            NewSupplierInvoice,
            NotifySupplierInvoice,
            NotifyNPR,
            NPREmail,
            CompanyApproved,
            COCBodyMessage,
            InvoiceWithCofc,
            NotifyEPR,
            InvoicePDF,
            PackingSlip,
            CommercialInvoice,
            SalesOrder,
            PurchaseOrder,
            PackingSlipWithCofc,
            SOProForma,
            NotifyCreateIPO,
            NotifyReleaseBom,
            NotifyPurchaseRequestBom  ,
            NotifyPOApprovedNew,
            NotifyCustomerRMA,
            NotifySupplierRMA, 
            NotifyCreditNotes,
            NotifyDebitNotes,
            NotifyCreatePOMail,
            ClientInvoicePDF,
            InternalPurchaseOrder,
            EmailQuote,
            CreditNote,
            DebitNote,
            HubDebit,
            NewCustomerRequirementwithECCNNotify,
            NotifyECCNSalesOrder,
            NotifyECCNPO,
            NotifyReverseLogistics,
            NotifyReleaseBomManager,
            NotifyPurchaseRequestBomManager,
            NotifyCloseBomManager,
            CreditNoteIssueNotification,
            CreditLimitApplicationNotification,
            ProspectiveOfferEmail,
            ProspectiveOfferHeaderMail,
            ReverseLogisticStockBody1,
            ReverseLogisticStockBody2
        }

	}
}
