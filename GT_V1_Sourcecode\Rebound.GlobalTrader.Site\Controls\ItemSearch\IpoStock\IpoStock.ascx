<%@ Control Language="C#" CodeBehind="IpoStock.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.ItemSearch.IpoStock" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_ItemSearch:DesignBase id="ctlDB" runat="server">
	<FieldsLeft>
		<ReboundUI_FilterDataItemRow:TextBox id="ctlPartNo" runat="server" ResourceTitle="PartNo" />
		<ReboundUI_FilterDataItemRow:Numerical id="ctlPurchaseOrderNo"  runat="server" ResourceTitle="IPurchaseOrderNo" />
		<ReboundUI_FilterDataItemRow:Numerical id="ctlCRMANo" runat="server" ResourceTitle="CRMANo" />
	</FieldsLeft>
	<FieldsRight>
		<ReboundUI_FilterDataItemRow:DropDown id="ctlWarehouse" runat="server" ResourceTitle="Warehouse" DropDownType="Warehouse" DropDownAssembly="Rebound.GlobalTrader.Site" />
		<ReboundUI_FilterDataItemRow:TextBox id="ctlLocation" runat="server" ResourceTitle="Location" />
	</FieldsRight>
</ReboundUI_ItemSearch:DesignBase>
