﻿//Marker     Changed by       Date         Remarks
//[001]      A<PERSON><PERSON><PERSON>   05/10/2021   Add new dropdown for Ship SO Status
using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;
using System.Data.Common;

namespace Rebound.GlobalTrader.DAL
{
    public abstract class ShipSOStatusProvider : DataAccess
    {
        static private ShipSOStatusProvider _instance = null;

        static public ShipSOStatusProvider Instance
        {
            get
            {
                if (_instance == null) _instance = (ShipSOStatusProvider)Activator.CreateInstance(Type.GetType(Globals.Settings.ShipSOStatuss.ProviderType));
                return _instance;
            }
        }
        public ShipSOStatusProvider()
        {
            this.ConnectionString = Globals.Settings.Logins.ConnectionString;
        }

        #region Method Registrations

        /// <summary>
        /// DropDownForClient
        /// Calls [usp_dropdown_ShipSo_Status]
        /// </summary>
        public abstract List<ShipSOStatusDetails> DropDownForClient();

        /// <summary>
        /// DropDownForReadyStatus
        /// Calls [usp_dropdown_ShipSoReady_Status]
        /// </summary>
        public abstract List<ShipSOStatusDetails> DropDownForReadyStatus();


        #endregion
    }
}
