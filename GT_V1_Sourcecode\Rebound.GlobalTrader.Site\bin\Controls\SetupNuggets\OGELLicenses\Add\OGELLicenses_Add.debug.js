///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//Marker     changed by      date         Remarks
//[001]      NgaiTo          10/04/2024   Add/edit OGELLicenses in setup screen
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.OGELLicenses_Add = function(element) {
    Rebound.GlobalTrader.Site.Controls.Forms.OGELLicenses_Add.initializeBase(this, [element]);
    this._intItemID = -1;
};

Rebound.GlobalTrader.Site.Controls.Forms.OGELLicenses_Add.prototype = {
    initialize: function() {
        Rebound.GlobalTrader.Site.Controls.Forms.OGELLicenses_Add.callBaseMethod(this, "initialize");
        this.addShown(Function.createDelegate(this, this.formShown));
    },

    dispose: function() {
        if (this.isDisposed) return;
        this._intItemID = null;
        Rebound.GlobalTrader.Site.Controls.Forms.OGELLicenses_Add.callBaseMethod(this, "dispose");
    },

    formShown: function() {
        if (this._blnFirstTimeShown) {
            this.addSave(Function.createDelegate(this, this.saveClicked));
            this._strPathToData = "controls/SetupNuggets/OGELLicenses";
            this._strDataObject = "OGELLicenses";
        }
        this.setFormFieldsToDefaults();
    },

    saveClicked: function() {
        this.resetFormFields();
        if (this.validateForm()) this.saveEdit();
    },

    validateForm: function() {
        var blnOK = this.autoValidateFields();
        return blnOK;
    },

    saveEdit: function() {
        if (!this.validateForm())
            return;
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData(this._strPathToData);
        obj.set_DataObject(this._strDataObject);
        obj.set_DataAction("AddNew");
        obj.addParameter("OgelNumber", this.getFieldValue("ctlOgelNumber"));
        obj.addParameter("Description", this.getFieldValue("ctlDescription"));
        obj.addParameter("InActive", this.getFieldValue("ctlInActive"));
        obj.addDataOK(Function.createDelegate(this, this.saveEditOK));
        obj.addError(Function.createDelegate(this, this.saveEditError));
        obj.addTimeout(Function.createDelegate(this, this.saveEditError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    saveEditError: function(args) {
        this._strErrorMessage = args._errorMessage;
        this.onSaveError();
    },

    saveEditOK: function (args) {
        if (args._result.Result == -1) {
            this.showError(true, "'OGEL Number' is already exists.");
            return
        } else {
            this.onSaveComplete();
        }
    }

};

Rebound.GlobalTrader.Site.Controls.Forms.OGELLicenses_Add.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.OGELLicenses_Add", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
