Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.AutoSearch");Rebound.GlobalTrader.Site.Controls.AutoSearch.MailMessageTo=function(n){Rebound.GlobalTrader.Site.Controls.AutoSearch.MailMessageTo.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.AutoSearch.MailMessageTo.prototype={initialize:function(){Rebound.GlobalTrader.Site.Controls.AutoSearch.MailMessageTo.callBaseMethod(this,"initialize");this.addDataReturnedEvent(Function.createDelegate(this,this.dataReturned));this.setupDataObject("MailMessageTo")},dispose:function(){this.isDisposed||Rebound.GlobalTrader.Site.Controls.AutoSearch.MailMessageTo.callBaseMethod(this,"dispose")},dataReturned:function(){var t,i,n;if(this._result&&this._result.TotalRecords>0)for(t=0,i=this._result.Results.length;t<i;t++)n=this._result.Results[t],strHTML="",n.Type.toUpperCase()=="GROUP"&&(strHTML+='<div class="mailGroup">'),strHTML+=n.Name,n.Type.toUpperCase()=="GROUP"&&(strHTML+="<\/div>"),this.addResultItem(strHTML,n.Name,n.ID,n.Type),strHTML=null,n=null}};Rebound.GlobalTrader.Site.Controls.AutoSearch.MailMessageTo.registerClass("Rebound.GlobalTrader.Site.Controls.AutoSearch.MailMessageTo",Rebound.GlobalTrader.Site.Controls.AutoSearch.Base,Sys.IDisposable);