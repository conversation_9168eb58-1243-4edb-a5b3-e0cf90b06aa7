﻿//Marker     changed by      date         Remarks
//[001]      Aashu          07/06/2018     Added supplier warranty field
//[002]      Aashu          20/06/2018     [REB-11754]: MSL level
//[003]      Aashu          16/08/2018     REB-12322 : A tick box to recomond test the parts from HUB side.
//[004]      Ab<PERSON>av <PERSON> 15/07/2021     Add new tickbox for Partwatch Match
//[005]      Bhooma&Sunil   11/08/2021     Added method for delete req partwatch match
//[006]      Abhinav <PERSON>a 07-09-2021     Add notification logic for the manual partwatch adding.
//[007]      Abhinav Saxena 10-09-2021     Remove partwatch from manual adding sourcing result
//[008]      Abhinav <PERSON>a 04-10-2021      Add new flag for different client
//[009]      Soorya Vyas    14-04-2023   [RP-1421] add revers logistic similar to strategic offer on HUBRFQ page
//[010]      Ravi Bhushan   12-09-2023   [RP-2340] AS6081
//-----------------------------------------------------------------------------------------
using System;
using System.Data;
using System.Data.SqlClient;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;
using System.Web.Caching;
using System.Data.Common;

namespace Rebound.GlobalTrader.DAL.SqlClient
{
    public class SqlSourcingResultProvider : SourcingResultProvider
    {
        /// <summary>
        /// Delete SourcingResult
        /// Calls [usp_delete_SourcingResult]
        /// </summary>
        public override bool Delete(System.String sourcingResultIds, System.Boolean? IsPOHub)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_delete_SourcingResult", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@SourcingResultId", SqlDbType.NVarChar).Value = sourcingResultIds;
                cmd.Parameters.Add("@IsPOHub", SqlDbType.Bit).Value = IsPOHub;
                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to delete SourcingResult", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        /// <summary>
        /// Create a new row
        /// Calls [usp_insert_SourcingResult]
        /// </summary>
        public override Int32 Insert(System.Int32? customerRequirementNo, System.String typeName, System.String notes, System.String part, System.Int32? manufacturerNo, System.String dateCode, System.Int32? productNo, System.Int32? packageNo, System.Int32? quantity, System.Double? price, System.Int32? currencyNo, System.DateTime? originalEntryDate, System.Int32? salesman, System.Int32? offerStatusNo, System.Int32? supplierNo, System.Byte? rohs, System.Int32? clientNo, System.Int32? updatedBy, System.Int32? mslLevelNo, System.Boolean? blnSupHasCurrency, out System.Int32? offerID)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            offerID = -1;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_insert_SourcingResult", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@CustomerRequirementNo", SqlDbType.Int).Value = customerRequirementNo;
                cmd.Parameters.Add("@TypeName", SqlDbType.NVarChar).Value = typeName;
                cmd.Parameters.Add("@Notes", SqlDbType.NVarChar).Value = notes;
                cmd.Parameters.Add("@Part", SqlDbType.NVarChar).Value = part;
                cmd.Parameters.Add("@ManufacturerNo", SqlDbType.Int).Value = manufacturerNo;
                cmd.Parameters.Add("@DateCode", SqlDbType.NVarChar).Value = dateCode;
                cmd.Parameters.Add("@ProductNo", SqlDbType.Int).Value = productNo;
                cmd.Parameters.Add("@PackageNo", SqlDbType.Int).Value = packageNo;
                cmd.Parameters.Add("@Quantity", SqlDbType.Int).Value = quantity;
                cmd.Parameters.Add("@Price", SqlDbType.Float).Value = price;
                cmd.Parameters.Add("@CurrencyNo", SqlDbType.Int).Value = currencyNo;
                cmd.Parameters.Add("@OriginalEntryDate", SqlDbType.DateTime).Value = originalEntryDate;
                cmd.Parameters.Add("@Salesman", SqlDbType.Int).Value = salesman;
                cmd.Parameters.Add("@OfferStatusNo", SqlDbType.Int).Value = offerStatusNo;
                cmd.Parameters.Add("@SupplierNo", SqlDbType.Int).Value = supplierNo;
                cmd.Parameters.Add("@ROHS", SqlDbType.TinyInt).Value = rohs;
                cmd.Parameters.Add("@ClientNo", SqlDbType.Int).Value = clientNo;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@MSLLevelNo", SqlDbType.Int).Value = mslLevelNo;
                cmd.Parameters.Add("@IsSupHasCurrency", SqlDbType.Bit).Value = blnSupHasCurrency;
                cmd.Parameters.Add("@SourcingResultId", SqlDbType.Int).Direction = ParameterDirection.Output;
                cmd.Parameters.Add("@OfferId", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                offerID = (Int32?)cmd.Parameters["@OfferId"].Value;
                return (Int32)cmd.Parameters["@SourcingResultId"].Value;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to insert SourcingResult", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// Create a new row
        /// Calls [usp_insert_ProspectiveOffer_SourcingResult]
        /// </summary>
        public override Int32 InsertProspectiveSourcingResult(System.Int32? customerRequirementNo, System.Int32 offerNo, System.String part, System.Int32? manufacturerNo, System.String dateCode, System.Int32? productNo, System.Int32? packageNo, System.Int32? quantity,
             System.Int32? salesman, System.Int32? supplierNo, System.Int32? poCurrencyNo, System.Int32? poHubCompanyNo, System.Double? offerPriceFromProspective, out System.Int32? offerID)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            offerID = -1;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_insert_ProspectiveOffer_SourcingResult", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@CustomerRequirementNo", SqlDbType.Int).Value = customerRequirementNo;
                cmd.Parameters.Add("@LineId", SqlDbType.Int).Value = offerNo;
                cmd.Parameters.Add("@PartNo", SqlDbType.NVarChar).Value = part;
                cmd.Parameters.Add("@MfrNo", SqlDbType.Int).Value = manufacturerNo;
                cmd.Parameters.Add("@DateCode", SqlDbType.Int).Value = dateCode;
                cmd.Parameters.Add("@ProductNo", SqlDbType.Int).Value = productNo;
                cmd.Parameters.Add("@PackageNo", SqlDbType.Int).Value = packageNo;
                cmd.Parameters.Add("@QuantityOffer", SqlDbType.Int).Value = quantity;
                cmd.Parameters.Add("@Salesman", SqlDbType.Int).Value = salesman;
                cmd.Parameters.Add("@SupplierNo", SqlDbType.Int).Value = supplierNo;
                cmd.Parameters.Add("@POCurrencyNo", SqlDbType.Int).Value = poCurrencyNo;
                cmd.Parameters.Add("@POHubCompanyNo", SqlDbType.Int).Value = poHubCompanyNo;
                cmd.Parameters.Add("@IsClone", SqlDbType.Int).Value = 1;
                cmd.Parameters.Add("@OfferPriceFromProspective", SqlDbType.Float).Value = offerPriceFromProspective;
                cmd.Parameters.Add("@SourcingResultId", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (Int32)cmd.Parameters["@SourcingResultId"].Value;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to insert SourcingResult", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        /// <summary>
        /// Create a new row
        /// Calls [usp_insert_SourcingResult_From_History]
        /// </summary>
        public override Int32 InsertFromHistory(System.Int32? customerRequirementNo, System.Int32? historyNo, System.Int32? clientNo, System.Int32? updatedBy)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_insert_SourcingResult_From_History", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@CustomerRequirementNo", SqlDbType.Int).Value = customerRequirementNo;
                cmd.Parameters.Add("@HistoryNo", SqlDbType.Int).Value = historyNo;
                cmd.Parameters.Add("@ClientNo", SqlDbType.Int).Value = clientNo;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@SourcingResultId", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (Int32)cmd.Parameters["@SourcingResultId"].Value;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to insert SourcingResult", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        /// <summary>
        /// Create a new row 
        /// Calls [usp_insert_SourcingResult_From_Offer]
        /// </summary>
        public override Int32 InsertFromOffer(System.Int32? customerRequirementNo, System.Int32? offerNo, System.Int32? updatedBy, System.Boolean isPOHub, out System.String strLinkMessage)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            strLinkMessage = "";
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                if (isPOHub)
                    cmd = new SqlCommand("usp_insert_SourcingResult_From_OfferPH", cn);
                else
                    cmd = new SqlCommand("usp_insert_SourcingResult_From_Offer", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@CustomerRequirementNo", SqlDbType.Int).Value = customerRequirementNo;
                cmd.Parameters.Add("@OfferNo", SqlDbType.Int).Value = offerNo;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@SourcingResultId", SqlDbType.Int).Direction = ParameterDirection.Output;
                cmd.Parameters.Add("@LinkCurrencyMsg", SqlDbType.VarChar, 150).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                strLinkMessage = (string)cmd.Parameters["@LinkCurrencyMsg"].Value;
                return (Int32)cmd.Parameters["@SourcingResultId"].Value;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to insert SourcingResult", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        /// <summary>
        /// Create a new row 
        /// Calls [usp_insert_SourcingResult_From_AltPart]
        /// </summary>
        public override Int32 InsertFromAltPart(System.Int32? customerRequirementNo, System.Int32? AltPartNo, System.Int32? updatedBy, System.Boolean isPOHub, out System.String strLinkMessage)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            strLinkMessage = "";
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                if (isPOHub)
                    cmd = new SqlCommand("usp_insert_SourcingResult_From_AltPartPH", cn);
                else
                    cmd = new SqlCommand("usp_insert_SourcingResult_From_AltPart", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@CustomerRequirementNo", SqlDbType.Int).Value = customerRequirementNo;
                cmd.Parameters.Add("@AlternativePartId", SqlDbType.Int).Value = AltPartNo;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@SourcingResultId", SqlDbType.Int).Direction = ParameterDirection.Output;
                cmd.Parameters.Add("@LinkCurrencyMsg", SqlDbType.VarChar, 150).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                strLinkMessage = (string)cmd.Parameters["@LinkCurrencyMsg"].Value;
                return (Int32)cmd.Parameters["@SourcingResultId"].Value;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to insert SourcingResult", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }



        /// <summary>
        /// Create a new row 
        /// Calls [usp_insert_SourcingResult_From_Offer]
        /// </summary>
        public override Int32 InsertFromStock(System.Int32? customerRequirementNo, System.Int32? StockNo, System.Int32? updatedBy, System.Boolean isPOHub, out System.String strLinkMessage)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            strLinkMessage = "";
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_insert_SourcingResult_From_StockPH", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@CustomerRequirementNo", SqlDbType.Int).Value = customerRequirementNo;
                cmd.Parameters.Add("@StcokNo", SqlDbType.Int).Value = StockNo;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@SourcingResultId", SqlDbType.Int).Direction = ParameterDirection.Output;
                cmd.Parameters.Add("@LinkCurrencyMsg", SqlDbType.VarChar, 150).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                strLinkMessage = (string)cmd.Parameters["@LinkCurrencyMsg"].Value;
                return (Int32)cmd.Parameters["@SourcingResultId"].Value;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to insert SourcingResult for Stock", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        /// <summary>
        /// Create a new row 
        /// Calls [usp_insert_SourcingResult_From_EpoPH]
        /// </summary>
        public override Int32 InsertFromEpo(System.Int32? customerRequirementNo, System.Int32? EpoNo, System.Int32? updatedBy, System.Boolean isPOHub, out System.String strLinkMessage)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            strLinkMessage = "";
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                //if (isPOHub)
                cmd = new SqlCommand("usp_insert_SourcingResult_From_EpoPH", cn);
                //else
                //  cmd = new SqlCommand("usp_insert_SourcingResult_From_EpoPH", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@CustomerRequirementNo", SqlDbType.Int).Value = customerRequirementNo;
                cmd.Parameters.Add("@EpoNo", SqlDbType.Int).Value = EpoNo;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@SourcingResultId", SqlDbType.Int).Direction = ParameterDirection.Output;
                cmd.Parameters.Add("@LinkCurrencyMsg", SqlDbType.VarChar, 150).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                strLinkMessage = (string)cmd.Parameters["@LinkCurrencyMsg"].Value;
                return (Int32)cmd.Parameters["@SourcingResultId"].Value;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to insert SourcingResult", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }



        /// <summary>
        /// //[009]
        /// Create a new row 
        /// Calls [usp_insert_SourcingResult_From_ReverseLogisticPH]
        /// </summary>
        public override Int32 InsertFromReverseLogistic(System.Int32? customerRequirementNo, System.Int32? ReverseLogisticNo, System.Int32? updatedBy, System.Boolean isPOHub, out System.String strLinkMessage)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            strLinkMessage = "";
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                //if (isPOHub)
                cmd = new SqlCommand("usp_insert_SourcingResult_From_ReverseLogisticPH", cn);
                //else
                //  cmd = new SqlCommand("usp_insert_SourcingResult_From_EpoPH", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@CustomerRequirementNo", SqlDbType.Int).Value = customerRequirementNo;
                cmd.Parameters.Add("@ReverseLogisticNo", SqlDbType.Int).Value = ReverseLogisticNo;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@SourcingResultId", SqlDbType.Int).Direction = ParameterDirection.Output;
                cmd.Parameters.Add("@LinkCurrencyMsg", SqlDbType.VarChar, 150).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                strLinkMessage = (string)cmd.Parameters["@LinkCurrencyMsg"].Value;
                return (Int32)cmd.Parameters["@SourcingResultId"].Value;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to insert SourcingResult", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        /// <summary>
        /// Create a new row for CrossMatch
        /// Calls [usp_insert_SourcingResult_From_CrossMatch_Offer]
        /// </summary>
        public override Int32 InsertFromOfferCrossMatch(System.Int32? customerRequirementNo, System.Int32? offerNo, System.Int32? updatedBy, System.Boolean isPOHub, out System.String strLinkMessage)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            strLinkMessage = "";
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_insert_SourcingResult_From_CrossMatch_OfferPH", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@CustomerRequirementNo", SqlDbType.Int).Value = customerRequirementNo;
                cmd.Parameters.Add("@OfferNo", SqlDbType.Int).Value = offerNo;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@SourcingResultId", SqlDbType.Int).Direction = ParameterDirection.Output;
                cmd.Parameters.Add("@LinkCurrencyMsg", SqlDbType.VarChar, 150).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                strLinkMessage = (string)cmd.Parameters["@LinkCurrencyMsg"].Value;
                return (Int32)cmd.Parameters["@SourcingResultId"].Value;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to insert SourcingResult", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        /// <summary>
        /// Create a new row
        /// Calls [usp_insert_SourcingResult_From_Trusted]
        /// </summary>
        public override Int32 InsertFromTrusted(System.Int32? customerRequirementNo, System.Int32? excessNo, System.Int32? updatedBy, System.Boolean isPOHub, out System.String strLinkMessage)
        {
            strLinkMessage = "";
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                if (isPOHub)
                    cmd = new SqlCommand("usp_insert_SourcingResult_From_TrustedPH", cn);
                else
                    cmd = new SqlCommand("usp_insert_SourcingResult_From_Trusted", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@CustomerRequirementNo", SqlDbType.Int).Value = customerRequirementNo;
                cmd.Parameters.Add("@ExcessNo", SqlDbType.Int).Value = excessNo;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@SourcingResultId", SqlDbType.Int).Direction = ParameterDirection.Output;
                cmd.Parameters.Add("@LinkCurrencyMsg", SqlDbType.VarChar, 150).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                strLinkMessage = (string)cmd.Parameters["@LinkCurrencyMsg"].Value;
                return (Int32)cmd.Parameters["@SourcingResultId"].Value;
                
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to insert SourcingResult", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        //code aded by anand cross march trusted
        /// <summary>
        /// Create a new row
        /// Calls [usp_insert_SourcingResult_From_Trusted_CrossMatch]
        /// </summary>
        public override Int32 InsertFromTrustedCrossMatch(System.Int32? customerRequirementNo, System.Int32? excessNo, System.Int32? updatedBy, System.Boolean isPOHub, out System.String strLinkMessage)
        {
            strLinkMessage = "";
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                //if (isPOHub)
                //    cmd = new SqlCommand("[usp_insert_SourcingResult_From_TrustedPH]", cn);
                //else
                cmd = new SqlCommand("[usp_insert_SourcingResult_From_Trusted_CrossMatch]", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@CustomerRequirementNo", SqlDbType.Int).Value = customerRequirementNo;
                cmd.Parameters.Add("@ExcessNo", SqlDbType.Int).Value = excessNo;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@SourcingResultId", SqlDbType.Int).Direction = ParameterDirection.Output;
                cmd.Parameters.Add("@LinkCurrencyMsg", SqlDbType.VarChar, 150).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                strLinkMessage = (string)cmd.Parameters["@LinkCurrencyMsg"].Value;
                return (Int32)cmd.Parameters["@SourcingResultId"].Value;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to insert SourcingResult", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        //end
        /// <summary>
        /// Create a new row
        /// Calls [usp_insert_SourcingResult_From_POQuote]
        /// </summary>
        /// 

        public override Int32 InsertFromPOQuote(System.Int32? customerRequirementNo, System.Int32? poQuoteLineNo, System.Int32? updatedBy, out System.String strLinkMessage)
        {
            strLinkMessage = "";
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_insert_SourcingResult_From_PurchaseRequest", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@CustomerRequirementNo", SqlDbType.Int).Value = customerRequirementNo;
                cmd.Parameters.Add("@POQuoteLineDetailNo", SqlDbType.Int).Value = poQuoteLineNo;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@SourcingResultId", SqlDbType.Int).Direction = ParameterDirection.Output;
                cmd.Parameters.Add("@LinkCurrencyMsg", SqlDbType.VarChar, 150).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                strLinkMessage = (string)cmd.Parameters["@LinkCurrencyMsg"].Value;
                return (Int32)cmd.Parameters["@SourcingResultId"].Value;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to insert SourcingResult", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }




        //code add for price request for corss match
        /// <summary>
        /// Create a new row fro cross match
        /// Calls [[usp_insert_SourcingResult_From_PurchaseRequest_CrossMatch]]
        /// </summary>
        /// 
        public override Int32 InsertFromPOQuoteCrossMatch(System.Int32? customerRequirementNo, System.Int32? poQuoteLineNo, System.Int32? updatedBy, out System.String strLinkMessage)
        {
            strLinkMessage = "";
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("[usp_insert_SourcingResult_From_PurchaseRequest_CrossMatch]", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@CustomerRequirementNo", SqlDbType.Int).Value = customerRequirementNo;
                cmd.Parameters.Add("@POQuoteLineDetailNo", SqlDbType.Int).Value = poQuoteLineNo;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@SourcingResultId", SqlDbType.Int).Direction = ParameterDirection.Output;
                cmd.Parameters.Add("@LinkCurrencyMsg", SqlDbType.VarChar, 150).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                strLinkMessage = (string)cmd.Parameters["@LinkCurrencyMsg"].Value;
                return (Int32)cmd.Parameters["@SourcingResultId"].Value;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to insert SourcingResult", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        //code end

        /// <summary>
        /// ItemSearch 
        /// Calls [usp_itemsearch_SourcingResult]
        /// </summary>
        public override List<SourcingResultDetails> ItemSearch(System.Int32? clientId, System.Int32? orderBy, System.Int32? sortDir, System.Int32? pageIndex, System.Int32? pageSize, System.String partSearch, System.String cmSearch, System.Int32? customerRequirementNoLo, System.Int32? customerRequirementNoHi, System.String supplierSearch, System.Boolean? IsPoHub, System.Int32? intQuoteID, System.String bom)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_itemsearch_SourcingResult", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@OrderBy", SqlDbType.Int).Value = orderBy;
                cmd.Parameters.Add("@SortDir", SqlDbType.Int).Value = sortDir;
                cmd.Parameters.Add("@PageIndex", SqlDbType.Int).Value = pageIndex;
                cmd.Parameters.Add("@PageSize", SqlDbType.Int).Value = pageSize;
                cmd.Parameters.Add("@PartSearch", SqlDbType.NVarChar).Value = partSearch;
                cmd.Parameters.Add("@CMSearch", SqlDbType.NVarChar).Value = cmSearch;
                cmd.Parameters.Add("@CustomerRequirementNoLo", SqlDbType.Int).Value = customerRequirementNoLo;
                cmd.Parameters.Add("@CustomerRequirementNoHi", SqlDbType.Int).Value = customerRequirementNoHi;
                cmd.Parameters.Add("@SupplierSearch", SqlDbType.NVarChar).Value = supplierSearch;
                cmd.Parameters.Add("@IsPoHub", SqlDbType.Bit).Value = IsPoHub;
                cmd.Parameters.Add("@intQuoteID", SqlDbType.Int).Value = intQuoteID;
                cmd.Parameters.Add("@BOM", SqlDbType.NVarChar).Value = bom;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<SourcingResultDetails> lst = new List<SourcingResultDetails>();
                while (reader.Read())
                {
                    SourcingResultDetails obj = new SourcingResultDetails();
                    obj.SourcingResultId = GetReaderValue_Int32(reader, "SourcingResultId", 0);
                    obj.Part = GetReaderValue_String(reader, "Part", "");
                    obj.DateCode = GetReaderValue_String(reader, "DateCode", "");
                    obj.Quantity = GetReaderValue_Int32(reader, "Quantity", 0);
                    obj.Price = GetReaderValue_Double(reader, "Price", 0);
                    obj.SupplierNo = GetReaderValue_NullableInt32(reader, "SupplierNo", null);
                    obj.OfferStatusChangeDate = GetReaderValue_NullableDateTime(reader, "OfferStatusChangeDate", null);
                    obj.CurrencyCode = GetReaderValue_String(reader, "CurrencyCode", "");
                    obj.CustomerRequirementId = GetReaderValue_NullableInt32(reader, "CustomerRequirementId", null);
                    obj.CustomerRequirementNumber = GetReaderValue_NullableInt32(reader, "CustomerRequirementNumber", null);
                    obj.ClientNo = GetReaderValue_NullableInt32(reader, "ClientNo", null);
                    obj.CompanyNo = GetReaderValue_NullableInt32(reader, "CompanyNo", null);
                    obj.CompanyName = GetReaderValue_String(reader, "CompanyName", "");
                    obj.SupplierName = GetReaderValue_String(reader, "SupplierName", "");
                    obj.ManufacturerCode = GetReaderValue_String(reader, "ManufacturerCode", "");
                    obj.ProductName = GetReaderValue_String(reader, "ProductName", "");
                    obj.PackageName = GetReaderValue_String(reader, "PackageName", "");
                    obj.OfferStatusChangeEmployeeName = GetReaderValue_String(reader, "OfferStatusChangeEmployeeName", "");
                    obj.RowCnt = GetReaderValue_NullableInt32(reader, "RowCnt", null);
                    obj.IsPoHub = GetReaderValue_String(reader, "IsPoHub", "");
                    obj.ClientCompanyNo = GetReaderValue_NullableInt32(reader, "ClientCompanyNo", null);
                    obj.ClientSupplierName = GetReaderValue_String(reader, "ClientCompanyName", "");
                    obj.ManufacturerNo = GetReaderValue_NullableInt32(reader, "ManufacturerId", 0);
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get SourcingResults", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        /// <summary>
        /// Get 
        /// Calls [usp_select_SourcingResult]
        /// </summary>
        public override SourcingResultDetails Get(System.Int32? sourcingResultId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_select_SourcingResult", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@SourcingResultId", SqlDbType.Int).Value = sourcingResultId;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd, CommandBehavior.SingleRow);
                if (reader.Read())
                {
                    //return GetSourcingResultFromReader(reader);
                    SourcingResultDetails obj = new SourcingResultDetails();
                    obj.SourcingResultId = GetReaderValue_Int32(reader, "SourcingResultId", 0);
                    obj.CustomerRequirementNo = GetReaderValue_Int32(reader, "CustomerRequirementNo", 0);
                    obj.SourcingTable = GetReaderValue_String(reader, "SourcingTable", "");
                    obj.SourcingTableItemNo = GetReaderValue_NullableInt32(reader, "SourcingTableItemNo", null);
                    obj.TypeName = GetReaderValue_String(reader, "TypeName", "");
                    obj.FullPart = GetReaderValue_String(reader, "FullPart", "");
                    obj.Part = GetReaderValue_String(reader, "Part", "");
                    obj.ManufacturerNo = GetReaderValue_NullableInt32(reader, "ManufacturerNo", null);
                    obj.DateCode = GetReaderValue_String(reader, "DateCode", "");
                    obj.ProductNo = GetReaderValue_NullableInt32(reader, "ProductNo", null);
                    obj.PackageNo = GetReaderValue_NullableInt32(reader, "PackageNo", null);
                    obj.Quantity = GetReaderValue_Int32(reader, "Quantity", 0);
                    obj.Price = GetReaderValue_Double(reader, "Price", 0);
                    obj.CurrencyNo = GetReaderValue_Int32(reader, "CurrencyNo", 0);
                    obj.OriginalEntryDate = GetReaderValue_NullableDateTime(reader, "OriginalEntryDate", null);
                    obj.Salesman = GetReaderValue_Int32(reader, "Salesman", 0);
                    obj.SupplierNo = GetReaderValue_NullableInt32(reader, "SupplierNo", null);
                    obj.UpdatedBy = GetReaderValue_NullableInt32(reader, "UpdatedBy", null);
                    obj.DLUP = GetReaderValue_DateTime(reader, "DLUP", DateTime.MinValue);
                    obj.ROHS = GetReaderValue_NullableByte(reader, "ROHS", null);
                    obj.OfferStatusNo = GetReaderValue_NullableInt32(reader, "OfferStatusNo", null);
                    obj.OfferStatusChangeDate = GetReaderValue_NullableDateTime(reader, "OfferStatusChangeDate", null);
                    obj.OfferStatusChangeLoginNo = GetReaderValue_NullableInt32(reader, "OfferStatusChangeLoginNo", null);
                    obj.Notes = GetReaderValue_String(reader, "Notes", "");
                    obj.SupplierName = GetReaderValue_String(reader, "SupplierName", "");
                    obj.ManufacturerCode = GetReaderValue_String(reader, "ManufacturerCode", "");
                    obj.ManufacturerName = GetReaderValue_String(reader, "ManufacturerName", "");
                    obj.CustomerPart = GetReaderValue_String(reader, "CustomerPart", "");
                    obj.SupplierPrice = GetReaderValue_Double(reader, "SupplierPrice", 0);
                    obj.POHubSupplierName = GetReaderValue_String(reader, "POHubSupplierName", "");
                    obj.POHubCompanyNo = GetReaderValue_NullableInt32(reader, "POHubCompanyNo", 0);
                    obj.UPLiftPrice = GetReaderValue_NullableDouble(reader, "UPLiftPrice", 0);
                    obj.ClientCompanyNo = GetReaderValue_NullableInt32(reader, "ClientCompanyNo", null);
                    obj.ClientSupplierName = GetReaderValue_String(reader, "ClientSupplierName", "");
                    obj.EstimatedShippingCost = GetReaderValue_NullableDouble(reader, "EstimatedShippingCost", 0);
                    obj.ActualPrice = GetReaderValue_NullableDouble(reader, "SupplierPrice", 0);
                    obj.SupplierPercentage = GetReaderValue_NullableDouble(reader, "UPLiftPrice", 0);
                    obj.ClientCurrencyNo = GetReaderValue_Int32(reader, "ClientCurrencyNo", 0);
                    obj.ClientCurrencyCode = GetReaderValue_String(reader, "ClientCurrencyCode", "");
                    obj.DeliveryDate = GetReaderValue_NullableDateTime(reader, "DeliveryDate", null);

                    obj.SPQ = GetReaderValue_String(reader, "SPQ", "");
                    obj.LeadTime = GetReaderValue_String(reader, "LeadTime", "");
                    obj.ROHSStatus = GetReaderValue_String(reader, "ROHSStatus", "");
                    obj.FactorySealed = GetReaderValue_String(reader, "FactorySealed", "");
                    obj.MSL = GetReaderValue_String(reader, "MSL", "");
                    obj.SupplierMOQ = GetReaderValue_String(reader, "SupplierMOQ", "");
                    obj.SupplierTotalQSA = GetReaderValue_String(reader, "SupplierTotalQSA", "");
                    obj.SupplierLTB = GetReaderValue_String(reader, "SupplierLTB", "");
                    obj.SupplierNotes = GetReaderValue_String(reader, "SupplierNotes", "");
                    obj.ClientNo = GetReaderValue_Int32(reader, "ClientNo", 0);
                    obj.RegionNo = GetReaderValue_Int32(reader, "RegionNo", 0);

                    obj.CurrencyCode = GetReaderValue_String(reader, "CurrencyCode", "");
                    obj.OriginalPrice = GetReaderValue_NullableDouble(reader, "ActualPrice", 0);
                    obj.ActualCurrencyNo = GetReaderValue_Int32(reader, "ActualCurrencyNo", 0);
                    obj.ActualCurrencyCode = GetReaderValue_String(reader, "ActualCurrencyCode", "");
                    obj.SourcingNotes = GetReaderValue_String(reader, "SourcingNotes", "");
                    obj.ProductDescription = GetReaderValue_String(reader, "ProductDescription", "");
                    obj.ProductInactive = GetReaderValue_NullableBoolean(reader, "ProductInactive", false);
                    obj.MSLLevelNo = GetReaderValue_Int32(reader, "MSLLevelNo", 0);

                    //[001] start
                    obj.SupplierWarranty = GetReaderValue_NullableInt32(reader, "SupplierWarranty", null);
                    obj.NonPreferredCompany = GetReaderValue_NullableBoolean(reader, "NonPreferredCompany", null);
                    //[001] end
                    obj.MSLLevelText = GetReaderValue_String(reader, "MSLLevel", "");
                    obj.PriorityNo = GetReaderValue_Int32(reader, "PriorityNo", 0);
                    obj.IHSCountryOfOriginNo = GetReaderValue_Int32(reader, "IHSCountryOfOriginNo", 0);
                    obj.IHSCountryOfOriginName = GetReaderValue_String(reader, "IHSCountryOfOriginName", "");
                    obj.CountryOfOriginNo = GetReaderValue_Int32(reader, "CountryOfOriginNo", 0);
                    obj.PackageDescription = GetReaderValue_String(reader, "PackageDescription", "");
                    obj.PartWatchMatch = GetReaderValue_NullableBoolean(reader, "partWatchMatch", false);
                    obj.PartWatchMatchHUBIPO = GetReaderValue_NullableBoolean(reader, "PartWatchMatchHUBIPO", false);
                    obj.TypeOfSupplierNo= GetReaderValue_Int32(reader, "TypeOfSupplierNo", 0);
                    obj.ReasonForSupplierNo = GetReaderValue_Int32(reader, "ReasonForSupplierNo", 0);
                    obj.RiskOfSupplierNo = GetReaderValue_Int32(reader, "RiskOfSupplierNo", 0);
                    obj.ISAS6081Required = GetReaderValue_NullableBoolean(reader, "AS6081", false); //[010]
                    obj.IsCountryFound= GetReaderValue_NullableBoolean(reader, "IsCountryFound", false);
                    obj.CountryName= GetReaderValue_String(reader, "CountryName", "");
                    obj.CountryNo = GetReaderValue_Int32(reader, "CountryNo", 0);
                    obj.SellPriceLessReason = GetReaderValue_String(reader, "SellPriceLessReason", "");
                    obj.IsTestingRecommended = GetReaderValue_Boolean(reader, "IsTestingRecommended", false);
                    return obj;
                }
                else
                {
                    return null;
                }
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get SourcingResult", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        /// <summary>
        /// GetListForCustomerRequirement 
        /// Calls [usp_SourcingResult_for_CustomerRequirement]
        /// </summary>
        public override List<SourcingResultDetails> GetListForSourcing(System.Int32? customerRequirementId, System.Boolean? isFromQuote)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_SourcingResult_for_CustomerRequirement", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@CustomerRequirementId", SqlDbType.Int).Value = customerRequirementId;
                cmd.Parameters.Add("@IsFromQuote", SqlDbType.Bit).Value = isFromQuote;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<SourcingResultDetails> lst = new List<SourcingResultDetails>();
                while (reader.Read())
                {
                    SourcingResultDetails obj = new SourcingResultDetails();
                    obj.SourcingResultId = GetReaderValue_Int32(reader, "SourcingResultId", 0);
                    obj.CustomerRequirementNo = GetReaderValue_Int32(reader, "CustomerRequirementNo", 0);
                    obj.SourcingTable = GetReaderValue_String(reader, "SourcingTable", "");
                    obj.SourcingTableItemNo = GetReaderValue_NullableInt32(reader, "SourcingTableItemNo", null);
                    obj.TypeName = GetReaderValue_String(reader, "TypeName", "");
                    obj.FullPart = GetReaderValue_String(reader, "FullPart", "");
                    obj.Part = GetReaderValue_String(reader, "Part", "");
                    obj.ManufacturerNo = GetReaderValue_NullableInt32(reader, "ManufacturerNo", null);
                    obj.DateCode = GetReaderValue_String(reader, "DateCode", "");
                    obj.ProductNo = GetReaderValue_NullableInt32(reader, "ProductNo", null);
                    obj.PackageNo = GetReaderValue_NullableInt32(reader, "PackageNo", null);
                    obj.Quantity = GetReaderValue_Int32(reader, "Quantity", 0);
                    obj.Price = GetReaderValue_Double(reader, "Price", 0);
                    obj.CurrencyNo = GetReaderValue_Int32(reader, "CurrencyNo", 0);
                    obj.OriginalEntryDate = GetReaderValue_NullableDateTime(reader, "OriginalEntryDate", null);
                    obj.Salesman = GetReaderValue_Int32(reader, "Salesman", 0);
                    obj.SupplierNo = GetReaderValue_NullableInt32(reader, "SupplierNo", null);
                    obj.UpdatedBy = GetReaderValue_NullableInt32(reader, "UpdatedBy", null);
                    obj.DLUP = GetReaderValue_DateTime(reader, "DLUP", DateTime.MinValue);
                    obj.ROHS = GetReaderValue_Byte(reader, "ROHS", (byte)0);
                    obj.OfferStatusNo = GetReaderValue_NullableInt32(reader, "OfferStatusNo", null);
                    obj.OfferStatusChangeDate = GetReaderValue_NullableDateTime(reader, "OfferStatusChangeDate", null);
                    obj.OfferStatusChangeLoginNo = GetReaderValue_NullableInt32(reader, "OfferStatusChangeLoginNo", null);
                    obj.Notes = GetReaderValue_String(reader, "Notes", "");
                    obj.CurrencyCode = GetReaderValue_String(reader, "CurrencyCode", "");
                    obj.SupplierName = GetReaderValue_String(reader, "SupplierName", "");
                    obj.ManufacturerCode = GetReaderValue_String(reader, "ManufacturerCode", "");
                    obj.ProductName = GetReaderValue_String(reader, "ProductName", "");
                    obj.PackageName = GetReaderValue_String(reader, "PackageName", "");
                    obj.OfferStatusChangeEmployeeName = GetReaderValue_String(reader, "OfferStatusChangeEmployeeName", "");
                    obj.ManufacturerName = GetReaderValue_String(reader, "ManufacturerName", "");
                    obj.ProductDescription = GetReaderValue_String(reader, "ProductDescription", "");
                    obj.PackageDescription = GetReaderValue_String(reader, "PackageDescription", "");
                    obj.SalesmanName = GetReaderValue_String(reader, "SalesmanName", "");
                    obj.POHubCompanyNo = GetReaderValue_NullableInt32(reader, "POHubCompanyNo", null);
                    obj.POHubSupplierName = GetReaderValue_String(reader, "POHubSupplierName", "");
                    obj.POHubReleaseBy = GetReaderValue_NullableInt32(reader, "POHubReleaseBy", 0);
                    obj.ClientCompanyNo = GetReaderValue_NullableInt32(reader, "ClientCompanyNo", null);
                    obj.ClientSupplierName = GetReaderValue_String(reader, "ClientSupplierName", "");
                    obj.ClientCurrencyNo = GetReaderValue_Int32(reader, "ClientCurrencyNo", 0);
                    obj.IsReleased = GetReaderValue_Boolean(reader, "IsReleased", false);
                    obj.Recalled = GetReaderValue_Boolean(reader, "Recalled", false);
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get SourcingResults", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// GetListForCustomerRequirement 
        /// Calls [usp_selectAll_SourcingResult_for_CustomerRequirement]
        /// </summary>
        public override List<SourcingResultDetails> GetListForCustomerRequirement(System.Int32? customerRequirementId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_selectAll_SourcingResult_for_CustomerRequirement", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@CustomerRequirementId", SqlDbType.Int).Value = customerRequirementId;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<SourcingResultDetails> lst = new List<SourcingResultDetails>();
                while (reader.Read())
                {
                    SourcingResultDetails obj = new SourcingResultDetails();
                    obj.SourcingResultId = GetReaderValue_Int32(reader, "SourcingResultId", 0);
                    obj.CustomerRequirementNo = GetReaderValue_Int32(reader, "CustomerRequirementNo", 0);
                    obj.SourcingTable = GetReaderValue_String(reader, "SourcingTable", "");
                    obj.SourcingTableItemNo = GetReaderValue_NullableInt32(reader, "SourcingTableItemNo", null);
                    obj.TypeName = GetReaderValue_String(reader, "TypeName", "");
                    obj.FullPart = GetReaderValue_String(reader, "FullPart", "");
                    obj.Part = GetReaderValue_String(reader, "Part", "");
                    obj.ManufacturerNo = GetReaderValue_NullableInt32(reader, "ManufacturerNo", null);
                    obj.DateCode = GetReaderValue_String(reader, "DateCode", "");
                    obj.ProductNo = GetReaderValue_NullableInt32(reader, "ProductNo", null);
                    obj.PackageNo = GetReaderValue_NullableInt32(reader, "PackageNo", null);
                    obj.Quantity = GetReaderValue_Int32(reader, "Quantity", 0);
                    obj.Price = GetReaderValue_Double(reader, "Price", 0);
                    obj.CurrencyNo = GetReaderValue_Int32(reader, "CurrencyNo", 0);
                    obj.OriginalEntryDate = GetReaderValue_NullableDateTime(reader, "OriginalEntryDate", null);
                    obj.Salesman = GetReaderValue_Int32(reader, "Salesman", 0);
                    obj.SupplierNo = GetReaderValue_NullableInt32(reader, "SupplierNo", null);
                    obj.UpdatedBy = GetReaderValue_NullableInt32(reader, "UpdatedBy", null);
                    obj.DLUP = GetReaderValue_DateTime(reader, "DLUP", DateTime.MinValue);
                    obj.ROHS = GetReaderValue_Byte(reader, "ROHS", (byte)0);
                    obj.OfferStatusNo = GetReaderValue_NullableInt32(reader, "OfferStatusNo", null);
                    obj.OfferStatusChangeDate = GetReaderValue_NullableDateTime(reader, "OfferStatusChangeDate", null);
                    obj.OfferStatusChangeLoginNo = GetReaderValue_NullableInt32(reader, "OfferStatusChangeLoginNo", null);
                    obj.Notes = GetReaderValue_String(reader, "Notes", "");
                    obj.CurrencyCode = GetReaderValue_String(reader, "CurrencyCode", "");
                    obj.SupplierName = GetReaderValue_String(reader, "SupplierName", "");
                    obj.ManufacturerCode = GetReaderValue_String(reader, "ManufacturerCode", "");
                    obj.ProductName = GetReaderValue_String(reader, "ProductName", "");
                    obj.PackageName = GetReaderValue_String(reader, "PackageName", "");
                    obj.OfferStatusChangeEmployeeName = GetReaderValue_String(reader, "OfferStatusChangeEmployeeName", "");
                    obj.ManufacturerName = GetReaderValue_String(reader, "ManufacturerName", "");
                    obj.ProductDescription = GetReaderValue_String(reader, "ProductDescription", "");
                    obj.PackageDescription = GetReaderValue_String(reader, "PackageDescription", "");
                    obj.SalesmanName = GetReaderValue_String(reader, "SalesmanName", "");
                    obj.POHubCompanyNo = GetReaderValue_NullableInt32(reader, "POHubCompanyNo", null);
                    obj.POHubSupplierName = GetReaderValue_String(reader, "POHubSupplierName", "");
                    obj.POHubReleaseBy = GetReaderValue_NullableInt32(reader, "POHubReleaseBy", 0);
                    obj.ClientCompanyNo = GetReaderValue_NullableInt32(reader, "ClientCompanyNo", null);
                    obj.ClientSupplierName = GetReaderValue_String(reader, "ClientSupplierName", "");
                    obj.ClientCurrencyNo = GetReaderValue_Int32(reader, "ClientCurrencyNo", 0);
                    obj.IsClosed = GetReaderValue_Boolean(reader, "Closed", false);
                    obj.IsSoCreated = GetReaderValue_Boolean(reader, "IsSoCreated", false);
                    obj.EstimatedShippingCost = GetReaderValue_NullableDouble(reader, "EstimatedShippingCost", 0);
                    obj.IsSoCreated = GetReaderValue_Boolean(reader, "IsSoCreated", false);
                    obj.IsApplyPOBankFee = GetReaderValue_Boolean(reader, "IsApplyPOBankFee", false);
                    obj.TermsName = GetReaderValue_String(reader, "TermsName", "");
                    obj.SourceRef = GetReaderValue_String(reader, "SourceRef", "");

                    obj.OriginalPrice = GetReaderValue_NullableDouble(reader, "BuyPrice", 0);
                    obj.ActualCurrencyNo = GetReaderValue_Int32(reader, "ActualCurrencyNo", 0);
                    obj.ActualCurrencyCode = GetReaderValue_String(reader, "BuyCurrencyCode", "");
                    obj.SupplierType = GetReaderValue_String(reader, "SupplierType", "");
                    obj.RegionName = GetReaderValue_String(reader, "RegionName", "");
                    //[002] start
                    obj.MSL = GetReaderValue_String(reader, "MSLLevel", "");
                    obj.PartWatchMatch = GetReaderValue_Boolean(reader, "PartWatchMatch", false);
                    obj.DiffrentClientOffer= GetReaderValue_Boolean(reader, "DiffrentClientOffer", false);
                    obj.ClientCode = GetReaderValue_String(reader, "ClientCode", "");
                    //[002] end
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get SourcingResults", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// GetListForCustomerRequirement 
        /// Calls [usp_selectAll_SourcingResult_for_CustomerRequirement]
        /// </summary>
        public override List<SourcingResultDetails> GetListForCustomerRequirementCopy(System.Int32? customerRequirementId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_select_SourcingResult_for_CustomerRequirement", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@CustomerRequirementId", SqlDbType.Int).Value = customerRequirementId;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<SourcingResultDetails> lst = new List<SourcingResultDetails>();
                while (reader.Read())
                {
                    SourcingResultDetails obj = new SourcingResultDetails();
                    obj.SourcingResultId = GetReaderValue_Int32(reader, "SourcingResultId", 0);
                    obj.CustomerRequirementNo = GetReaderValue_Int32(reader, "CustomerRequirementNo", 0);
                    obj.SourcingTable = GetReaderValue_String(reader, "SourcingTable", "");
                    obj.SourcingTableItemNo = GetReaderValue_NullableInt32(reader, "SourcingTableItemNo", null);
                    obj.TypeName = GetReaderValue_String(reader, "TypeName", "");
                    obj.FullPart = GetReaderValue_String(reader, "FullPart", "");
                    obj.Part = GetReaderValue_String(reader, "Part", "");
                    obj.ManufacturerNo = GetReaderValue_NullableInt32(reader, "ManufacturerNo", null);
                    obj.DateCode = GetReaderValue_String(reader, "DateCode", "");
                    obj.ProductNo = GetReaderValue_NullableInt32(reader, "ProductNo", null);
                    obj.PackageNo = GetReaderValue_NullableInt32(reader, "PackageNo", null);
                    obj.Quantity = GetReaderValue_Int32(reader, "Quantity", 0);
                    obj.Price = GetReaderValue_Double(reader, "Price", 0);
                    obj.CurrencyNo = GetReaderValue_Int32(reader, "CurrencyNo", 0);
                    obj.OriginalEntryDate = GetReaderValue_NullableDateTime(reader, "OriginalEntryDate", null);
                    obj.Salesman = GetReaderValue_Int32(reader, "Salesman", 0);
                    obj.SupplierNo = GetReaderValue_NullableInt32(reader, "SupplierNo", null);
                    obj.UpdatedBy = GetReaderValue_NullableInt32(reader, "UpdatedBy", null);
                    obj.DLUP = GetReaderValue_DateTime(reader, "DLUP", DateTime.MinValue);
                    obj.ROHS = GetReaderValue_Byte(reader, "ROHS", (byte)0);
                    obj.OfferStatusNo = GetReaderValue_NullableInt32(reader, "OfferStatusNo", null);
                    obj.OfferStatusChangeDate = GetReaderValue_NullableDateTime(reader, "OfferStatusChangeDate", null);
                    obj.OfferStatusChangeLoginNo = GetReaderValue_NullableInt32(reader, "OfferStatusChangeLoginNo", null);
                    obj.Notes = GetReaderValue_String(reader, "Notes", "");
                    obj.CurrencyCode = GetReaderValue_String(reader, "CurrencyCode", "");
                    obj.SupplierName = GetReaderValue_String(reader, "SupplierName", "");
                    obj.ManufacturerCode = GetReaderValue_String(reader, "ManufacturerCode", "");
                    obj.ProductName = GetReaderValue_String(reader, "ProductName", "");
                    obj.PackageName = GetReaderValue_String(reader, "PackageName", "");
                    obj.OfferStatusChangeEmployeeName = GetReaderValue_String(reader, "OfferStatusChangeEmployeeName", "");
                    obj.ManufacturerName = GetReaderValue_String(reader, "ManufacturerName", "");
                    obj.ProductDescription = GetReaderValue_String(reader, "ProductDescription", "");
                    obj.PackageDescription = GetReaderValue_String(reader, "PackageDescription", "");
                    obj.SalesmanName = GetReaderValue_String(reader, "SalesmanName", "");
                    obj.POHubCompanyNo = GetReaderValue_NullableInt32(reader, "POHubCompanyNo", null);
                    obj.POHubSupplierName = GetReaderValue_String(reader, "POHubSupplierName", "");
                    obj.POHubReleaseBy = GetReaderValue_NullableInt32(reader, "POHubReleaseBy", 0);
                    obj.ClientCompanyNo = GetReaderValue_NullableInt32(reader, "ClientCompanyNo", null);
                    obj.ClientSupplierName = GetReaderValue_String(reader, "ClientSupplierName", "");
                    obj.ClientCurrencyNo = GetReaderValue_Int32(reader, "ClientCurrencyNo", 0);
                    obj.IsClosed = GetReaderValue_Boolean(reader, "Closed", false);
                    obj.IsSoCreated = GetReaderValue_Boolean(reader, "IsSoCreated", false);
                    obj.EstimatedShippingCost = GetReaderValue_NullableDouble(reader, "EstimatedShippingCost", 0);
                    obj.IsSoCreated = GetReaderValue_Boolean(reader, "IsSoCreated", false);
                    obj.IsApplyPOBankFee = GetReaderValue_Boolean(reader, "IsApplyPOBankFee", false);
                    obj.TermsName = GetReaderValue_String(reader, "TermsName", "");
                    obj.SourceRef = GetReaderValue_String(reader, "SourceRef", "");

                    obj.OriginalPrice = GetReaderValue_NullableDouble(reader, "BuyPrice", 0);
                    obj.ActualCurrencyNo = GetReaderValue_Int32(reader, "ActualCurrencyNo", 0);
                    obj.ActualCurrencyCode = GetReaderValue_String(reader, "BuyCurrencyCode", "");
                    obj.SupplierType = GetReaderValue_String(reader, "SupplierType", "");
                    obj.RegionName = GetReaderValue_String(reader, "RegionName", "");

                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get SourcingResults", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        /// <summary>
        /// GetListForQuoteLine 
        /// Calls [usp_selectAll_SourcingResult_for_QuoteLine]
        /// </summary>
        public override List<SourcingResultDetails> GetListForQuoteLine(System.Int32? quoteLineId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_selectAll_SourcingResult_for_QuoteLine", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@QuoteLineId", SqlDbType.Int).Value = quoteLineId;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<SourcingResultDetails> lst = new List<SourcingResultDetails>();
                while (reader.Read())
                {
                    SourcingResultDetails obj = new SourcingResultDetails();
                    obj.SourcingResultId = GetReaderValue_Int32(reader, "SourcingResultId", 0);
                    obj.CustomerRequirementNo = GetReaderValue_Int32(reader, "CustomerRequirementNo", 0);
                    obj.SourcingTable = GetReaderValue_String(reader, "SourcingTable", "");
                    obj.SourcingTableItemNo = GetReaderValue_NullableInt32(reader, "SourcingTableItemNo", null);
                    obj.TypeName = GetReaderValue_String(reader, "TypeName", "");
                    obj.FullPart = GetReaderValue_String(reader, "FullPart", "");
                    obj.Part = GetReaderValue_String(reader, "Part", "");
                    obj.ManufacturerNo = GetReaderValue_NullableInt32(reader, "ManufacturerNo", null);
                    obj.DateCode = GetReaderValue_String(reader, "DateCode", "");
                    obj.ProductNo = GetReaderValue_NullableInt32(reader, "ProductNo", null);
                    obj.PackageNo = GetReaderValue_NullableInt32(reader, "PackageNo", null);
                    obj.Quantity = GetReaderValue_Int32(reader, "Quantity", 0);
                    obj.Price = GetReaderValue_Double(reader, "Price", 0);
                    obj.CurrencyNo = GetReaderValue_Int32(reader, "CurrencyNo", 0);
                    obj.OriginalEntryDate = GetReaderValue_NullableDateTime(reader, "OriginalEntryDate", null);
                    obj.Salesman = GetReaderValue_Int32(reader, "Salesman", 0);
                    obj.SupplierNo = GetReaderValue_NullableInt32(reader, "SupplierNo", null);
                    obj.UpdatedBy = GetReaderValue_NullableInt32(reader, "UpdatedBy", null);
                    obj.DLUP = GetReaderValue_DateTime(reader, "DLUP", DateTime.MinValue);
                    obj.ROHS = GetReaderValue_Byte(reader, "ROHS", (byte)0);
                    obj.OfferStatusNo = GetReaderValue_NullableInt32(reader, "OfferStatusNo", null);
                    obj.OfferStatusChangeDate = GetReaderValue_NullableDateTime(reader, "OfferStatusChangeDate", null);
                    obj.OfferStatusChangeLoginNo = GetReaderValue_NullableInt32(reader, "OfferStatusChangeLoginNo", null);
                    obj.Notes = GetReaderValue_String(reader, "Notes", "");
                    obj.CurrencyCode = GetReaderValue_String(reader, "CurrencyCode", "");
                    obj.CustomerRequirementNumber = GetReaderValue_NullableInt32(reader, "CustomerRequirementNumber", null);
                    obj.SupplierName = GetReaderValue_String(reader, "SupplierName", "");
                    obj.ManufacturerCode = GetReaderValue_String(reader, "ManufacturerCode", "");
                    obj.ProductName = GetReaderValue_String(reader, "ProductName", "");
                    obj.PackageName = GetReaderValue_String(reader, "PackageName", "");
                    obj.OfferStatusChangeEmployeeName = GetReaderValue_String(reader, "OfferStatusChangeEmployeeName", "");
                    obj.ManufacturerName = GetReaderValue_String(reader, "ManufacturerName", "");
                    obj.ProductDescription = GetReaderValue_String(reader, "ProductDescription", "");
                    obj.PackageDescription = GetReaderValue_String(reader, "PackageDescription", "");
                    obj.SalesmanName = GetReaderValue_String(reader, "SalesmanName", "");

                    obj.POHubCompanyNo = GetReaderValue_NullableInt32(reader, "POHubCompanyNo", null);
                    obj.POHubSupplierName = GetReaderValue_String(reader, "POHubSupplierName", "");
                    //obj.POHubReleaseBy = GetReaderValue_NullableInt32(reader, "POHubReleaseBy", 0);
                    obj.ClientCompanyNo = GetReaderValue_NullableInt32(reader, "ClientCompanyNo", null);
                    obj.ClientSupplierName = GetReaderValue_String(reader, "ClientSupplierName", "");
                    obj.ClientCurrencyNo = GetReaderValue_Int32(reader, "ClientCurrencyNo", 0);
                    obj.ClientCurrencyCode = GetReaderValue_String(reader, "ClientCurrencyCode", "");
                    // obj.ConvertedSourcingPrice = GetReaderValue_Double(reader, "ConvertedSourcingPrice", 0);
                    obj.SupplierType = GetReaderValue_String(reader, "SupplierType", "");
                    obj.DeliveryDate = GetReaderValue_NullableDateTime(reader, "DeliveryDate", null);
                    obj.EstimatedShippingCost = GetReaderValue_Double(reader, "EstimatedShippingCost", 0);
                    obj.RegionName = GetReaderValue_String(reader, "RegionName", "");

                    obj.HubRFQName = GetReaderValue_String(reader, "HubRFQName", "");
                    obj.HubRFQNo = GetReaderValue_Int32(reader, "HubRFQNo", 0);
                    obj.TermsName = GetReaderValue_String(reader, "TermsName", "");
                    obj.IsApplyPOBankFee = GetReaderValue_Boolean(reader, "IsApplyPOBankFee", false);

                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get SourcingResults", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        /// <summary>
        /// Update SourcingResult
        /// Calls [usp_update_SourcingResult]
        /// </summary>
        public override bool Update(System.Int32? sourcingResultId, System.String part, System.Int32? manufacturerNo, System.String dateCode, System.Int32? productNo, System.Int32? packageNo, System.Int32? quantity, System.Double? price, System.Int32? currencyNo, System.Int32? offerStatusNo, System.Int32? supplierNo, System.Byte? rohs, System.String notes, System.Int32? updatedBy, System.Int32? mslLevelNo, System.Boolean? PartWatchMatch)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_update_SourcingResult", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@SourcingResultId", SqlDbType.Int).Value = sourcingResultId;
                cmd.Parameters.Add("@Part", SqlDbType.NVarChar).Value = part;
                cmd.Parameters.Add("@ManufacturerNo", SqlDbType.Int).Value = manufacturerNo;
                cmd.Parameters.Add("@DateCode", SqlDbType.NVarChar).Value = dateCode;
                cmd.Parameters.Add("@ProductNo", SqlDbType.Int).Value = productNo;
                cmd.Parameters.Add("@PackageNo", SqlDbType.Int).Value = packageNo;
                cmd.Parameters.Add("@Quantity", SqlDbType.Int).Value = quantity;
                cmd.Parameters.Add("@Price", SqlDbType.Float).Value = price;
                cmd.Parameters.Add("@CurrencyNo", SqlDbType.Int).Value = currencyNo;
                cmd.Parameters.Add("@OfferStatusNo", SqlDbType.Int).Value = offerStatusNo;
                cmd.Parameters.Add("@SupplierNo", SqlDbType.Int).Value = supplierNo;
                cmd.Parameters.Add("@ROHS", SqlDbType.TinyInt).Value = rohs;
                cmd.Parameters.Add("@Notes", SqlDbType.NVarChar).Value = notes;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@MSLLevelNo", SqlDbType.Int).Value = mslLevelNo;
                cmd.Parameters.Add("@PartWatchMatch", SqlDbType.Bit).Value = PartWatchMatch;
                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to update SourcingResult", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// GetListForBOMCustomerRequirement 
        /// Calls [usp_selectAll_SourcingResult_for_BOMCustomerRequirement]
        /// </summary>
        public override List<SourcingResultDetails> GetListForBOMCustomerRequirement(System.Int32? customerRequirementId, System.Boolean isPOHub)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_selectAll_SourcingResult_for_BOMCustomerRequirement", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@CustomerRequirementId", SqlDbType.Int).Value = customerRequirementId;
                cmd.Parameters.Add("@IsPoHub", SqlDbType.Bit).Value = isPOHub;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<SourcingResultDetails> lst = new List<SourcingResultDetails>();
                while (reader.Read())
                {
                    SourcingResultDetails obj = new SourcingResultDetails();
                    obj.SourcingResultId = GetReaderValue_Int32(reader, "SourcingResultId", 0);
                    obj.CustomerRequirementNo = GetReaderValue_Int32(reader, "CustomerRequirementNo", 0);
                    obj.SourcingTable = GetReaderValue_String(reader, "SourcingTable", "");
                    obj.SourcingTableItemNo = GetReaderValue_NullableInt32(reader, "SourcingTableItemNo", null);
                    obj.TypeName = GetReaderValue_String(reader, "TypeName", "");
                    obj.FullPart = GetReaderValue_String(reader, "FullPart", "");
                    obj.Part = GetReaderValue_String(reader, "Part", "");
                    obj.ManufacturerNo = GetReaderValue_NullableInt32(reader, "ManufacturerNo", null);
                    obj.DateCode = GetReaderValue_String(reader, "DateCode", "");
                    obj.ProductNo = GetReaderValue_NullableInt32(reader, "ProductNo", null);
                    obj.PackageNo = GetReaderValue_NullableInt32(reader, "PackageNo", null);
                    obj.Quantity = GetReaderValue_Int32(reader, "Quantity", 0);
                    obj.Price = GetReaderValue_Double(reader, "Price", 0);
                    obj.CurrencyNo = GetReaderValue_Int32(reader, "CurrencyNo", 0);
                    obj.OriginalEntryDate = GetReaderValue_NullableDateTime(reader, "OriginalEntryDate", null);
                    obj.Salesman = GetReaderValue_Int32(reader, "Salesman", 0);
                    obj.SupplierNo = GetReaderValue_NullableInt32(reader, "SupplierNo", null);
                    obj.UpdatedBy = GetReaderValue_NullableInt32(reader, "UpdatedBy", null);
                    obj.DLUP = GetReaderValue_DateTime(reader, "DLUP", DateTime.MinValue);
                    obj.ROHS = GetReaderValue_Byte(reader, "ROHS", (byte)0);
                    obj.OfferStatusNo = GetReaderValue_NullableInt32(reader, "OfferStatusNo", null);
                    obj.OfferStatusChangeDate = GetReaderValue_NullableDateTime(reader, "OfferStatusChangeDate", null);
                    obj.OfferStatusChangeLoginNo = GetReaderValue_NullableInt32(reader, "OfferStatusChangeLoginNo", null);
                    obj.Notes = GetReaderValue_String(reader, "Notes", "");
                    obj.CurrencyCode = GetReaderValue_String(reader, "CurrencyCode", "");
                    obj.SupplierName = GetReaderValue_String(reader, "SupplierName", "");
                    obj.ManufacturerCode = GetReaderValue_String(reader, "ManufacturerCode", "");
                    obj.ProductName = GetReaderValue_String(reader, "ProductName", "");
                    obj.PackageName = GetReaderValue_String(reader, "PackageName", "");
                    obj.OfferStatusChangeEmployeeName = GetReaderValue_String(reader, "OfferStatusChangeEmployeeName", "");
                    obj.ManufacturerName = GetReaderValue_String(reader, "ManufacturerName", "");
                    obj.ProductDescription = GetReaderValue_String(reader, "ProductDescription", "");
                    obj.PackageDescription = GetReaderValue_String(reader, "PackageDescription", "");
                    obj.SalesmanName = GetReaderValue_String(reader, "SalesmanName", "");
                    obj.SupplierPrice = GetReaderValue_Double(reader, "SupplierPrice", 0);
                    obj.POHubCompanyNo = GetReaderValue_NullableInt32(reader, "POHubCompanyNo", null);
                    obj.POHubSupplierName = GetReaderValue_String(reader, "POHubSupplierName", "");
                    obj.POHubReleaseBy = GetReaderValue_NullableInt32(reader, "POHubReleaseBy", 0);
                    obj.ClientCompanyNo = GetReaderValue_NullableInt32(reader, "ClientCompanyNo", null);
                    obj.ClientSupplierName = GetReaderValue_String(reader, "ClientSupplierName", "");
                    obj.ClientCurrencyNo = GetReaderValue_Int32(reader, "ClientCurrencyNo", 0);
                    obj.ClientCurrencyCode = GetReaderValue_String(reader, "ClientCurrencyCode", "");
                    obj.ConvertedSourcingPrice = GetReaderValue_Double(reader, "ConvertedSourcingPrice", 0);
                    obj.MslSpqFactorySealed = GetReaderValue_String(reader, "MslSpqFactorySealed", "");
                    obj.EstimatedShippingCost = GetReaderValue_NullableDouble(reader, "EstimatedShippingCost", 0);

                    obj.SupplierType = GetReaderValue_String(reader, "SupplierType", "");
                    obj.DeliveryDate = GetReaderValue_NullableDateTime(reader, "DeliveryDate", null);

                    obj.ActualPrice = GetReaderValue_NullableDouble(reader, "SupplierPrice", 0);
                    obj.SupplierPercentage = GetReaderValue_NullableDouble(reader, "UPLiftPrice", 0);

                    obj.SupplierManufacturerName = GetReaderValue_String(reader, "SupplierManufacturerName", "");
                    obj.SupplierDateCode = GetReaderValue_String(reader, "SupplierDateCode", "");
                    obj.SupplierPackageType = GetReaderValue_String(reader, "SupplierPackageType", "");
                    obj.SupplierProductType = GetReaderValue_String(reader, "SupplierProductType", "");
                    obj.SupplierMOQ = GetReaderValue_String(reader, "SupplierMOQ", "");
                    obj.SupplierTotalQSA = GetReaderValue_String(reader, "SupplierTotalQSA", "");
                    obj.SupplierLTB = GetReaderValue_String(reader, "SupplierLTB", "");
                    obj.SupplierNotes = GetReaderValue_String(reader, "SupplierNotes", "");
                    obj.SourcingRelease = GetReaderValue_NullableBoolean(reader, "SourcingReleased", false);
                    obj.SPQ = GetReaderValue_String(reader, "SPQ", "");
                    obj.LeadTime = GetReaderValue_String(reader, "LeadTime", "");
                    obj.ROHSStatus = GetReaderValue_String(reader, "ROHSStatus", "");
                    obj.FactorySealed = GetReaderValue_String(reader, "FactorySealed", "");
                    obj.MSL = GetReaderValue_String(reader, "MSL", "");
                    obj.IsClosed = GetReaderValue_Boolean(reader, "Closed", false);
                    obj.ClientNo = GetReaderValue_Int32(reader, "ClientNo", 0);
                    obj.RegionName = GetReaderValue_String(reader, "RegionName", "");
                    obj.IsClosed = GetReaderValue_Boolean(reader, "Closed", false);
                    obj.IsSoCreated = GetReaderValue_Boolean(reader, "IsSoCreated", false);
                    obj.IsApplyPOBankFee = GetReaderValue_Boolean(reader, "IsApplyPOBankFee", false);
                    obj.TermsName = GetReaderValue_String(reader, "TermsName", "");
                    obj.SourceRef = GetReaderValue_String(reader, "SourceRef", "");

                    obj.OriginalPrice = GetReaderValue_NullableDouble(reader, "BuyPrice", 0);
                    obj.ActualCurrencyNo = GetReaderValue_Int32(reader, "ActualCurrencyNo", 0);
                    obj.ActualCurrencyCode = GetReaderValue_String(reader, "BuyCurrencyCode", "");
                    obj.SourcingReleasedCount = GetReaderValue_Int32(reader, "SourcingReleasedCount", 0);
                    obj.MSLLevelNo = GetReaderValue_Int32(reader, "MSLLevelNo", 0);
                    obj.MSLLevelText = GetReaderValue_String(reader, "MSLLevelText", "");

                    //[001] start
                    //[001] start
                    obj.SupplierWarranty = GetReaderValue_Int32(reader, "SupplierWarranty", 0);
                    //[001] end
                    //[003] start
                    obj.IsTestingRecommended = GetReaderValue_Boolean(reader, "IsTestingRecommended", false);
                    //[003] end
                    obj.IsImageAvailable = GetReaderValue_NullableBoolean(reader, "IsImageAvailable", false);
                    obj.PriorityNo = GetReaderValue_Int32(reader, "PriorityNo", 0);
                    obj.IHSCountryOfOriginNo = GetReaderValue_Int32(reader, "IHSCountryOfOriginNo", 0);
                    obj.IHSCountryOfOriginName = GetReaderValue_String(reader, "IHSCountryOfOriginName", "");
                    obj.CountryOfOriginNo = GetReaderValue_Int32(reader, "CountryOfOriginNo", 0);
                    obj.CountryOfOriginName = GetReaderValue_String(reader, "CountryOfOriginName", "");
                    obj.ReReleased = GetReaderValue_Int32(reader, "ReReleased", 0);
                    obj.ROHSDescription = GetReaderValue_String(reader, "ROHSDescription", "");
                    obj.PartWatchMatchHUBIPO = GetReaderValue_NullableBoolean(reader, "PartWatchMatchHUBIPO", false);
                    obj.IsPartWatchMatchClient = GetReaderValue_NullableBoolean(reader, "IsPartWatchMatchClient", false);
                    obj.SourceClient = GetReaderValue_String(reader, "SourceClient", "");
                    obj.SourceClientNo = GetReaderValue_Int32(reader, "SourceClientNo", 0);
                    obj.ISAS6081Required= GetReaderValue_NullableBoolean(reader, "ISAS6081Required", false);
                    obj.TypeOfSupplierName= GetReaderValue_String(reader, "TypeOfSupplierName", "");
                    obj.ReasonForSupplierName = GetReaderValue_String(reader, "ReasonForSupplierName", "");
                    obj.RiskOfSupplierName = GetReaderValue_String(reader, "RiskOfSupplierName", "");
                    obj.AssigneeId= GetReaderValue_String(reader, "AssigneeId", "");
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get SourcingResults", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// GetListForBOMCustomerRequirement 
        /// Calls [usp_selectAll_SourcingResult_for_LogDetail]
        /// </summary>
        public override List<SourcingResultDetails> GetListForBOMCustomerRequirementLogDetail(System.Int32? customerRequirementId, System.Boolean isPOHub)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_selectAll_SourcingResult_for_LogDetail", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@CustomerRequirementId", SqlDbType.Int).Value = customerRequirementId;
                cmd.Parameters.Add("@IsPoHub", SqlDbType.Bit).Value = isPOHub;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<SourcingResultDetails> lst = new List<SourcingResultDetails>();
                while (reader.Read())
                {
                    SourcingResultDetails obj = new SourcingResultDetails();
                    obj.SourcingResultId = GetReaderValue_Int32(reader, "SourcingResultId", 0);
                    obj.CustomerRequirementNo = GetReaderValue_Int32(reader, "CustomerRequirementNo", 0);
                    obj.ChangeNotes = GetReaderValue_String(reader, "ChangeNotes", "");
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get SourcingResults", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override SourcingResultDetails GetSourcingLog(System.Int32? sourcingResultId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_select_SourcingResultLogDetail", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@SourcingResultId", SqlDbType.Int).Value = sourcingResultId;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd, CommandBehavior.SingleRow);
                if (reader.Read())
                {
                    //return GetSourcingResultFromReader(reader);
                    SourcingResultDetails obj = new SourcingResultDetails();
                    obj.SourcingResultId = GetReaderValue_Int32(reader, "SourcingResultId", 0);
                    obj.CustomerRequirementNo = GetReaderValue_Int32(reader, "CustomerRequirementNo", 0);
                    obj.ChangeNotes = GetReaderValue_String(reader, "ChangeNotes", "");
                    return obj;
                }
                else
                {
                    return null;
                }
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get SourcingResult", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        /// <summary>
        /// GetListForBOMCustomerRequirement 
        /// Calls [GetListForBOMSourcingResult]
        /// </summary>
        public override List<SourcingResultDetails> GetListForBOMSourcingResult(System.Int32? BomId, System.Boolean isPOHub)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_selectAll_SourcingResult_for_BOME", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@BomId", SqlDbType.Int).Value = BomId;
                cmd.Parameters.Add("@IsPoHub", SqlDbType.Bit).Value = isPOHub;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<SourcingResultDetails> lst = new List<SourcingResultDetails>();
                while (reader.Read())
                {
                    SourcingResultDetails obj = new SourcingResultDetails();
                    obj.SourcingResultId = GetReaderValue_Int32(reader, "SourcingResultId", 0);
                    obj.CustomerRequirementNo = GetReaderValue_Int32(reader, "CustomerRequirementNo", 0);
                    obj.SourcingTable = GetReaderValue_String(reader, "SourcingTable", "");
                    obj.SourcingTableItemNo = GetReaderValue_NullableInt32(reader, "SourcingTableItemNo", null);
                    obj.TypeName = GetReaderValue_String(reader, "TypeName", "");
                    obj.FullPart = GetReaderValue_String(reader, "FullPart", "");
                    obj.Part = GetReaderValue_String(reader, "Part", "");
                    obj.ManufacturerNo = GetReaderValue_NullableInt32(reader, "ManufacturerNo", null);
                    obj.DateCode = GetReaderValue_String(reader, "DateCode", "");
                    obj.ProductNo = GetReaderValue_NullableInt32(reader, "ProductNo", null);
                    obj.PackageNo = GetReaderValue_NullableInt32(reader, "PackageNo", null);
                    obj.Quantity = GetReaderValue_Int32(reader, "Quantity", 0);
                    obj.Price = GetReaderValue_Double(reader, "Price", 0);
                    obj.CurrencyNo = GetReaderValue_Int32(reader, "CurrencyNo", 0);
                    obj.OriginalEntryDate = GetReaderValue_NullableDateTime(reader, "OriginalEntryDate", null);
                    obj.Salesman = GetReaderValue_Int32(reader, "Salesman", 0);
                    obj.SupplierNo = GetReaderValue_NullableInt32(reader, "SupplierNo", null);
                    obj.UpdatedBy = GetReaderValue_NullableInt32(reader, "UpdatedBy", null);
                    obj.DLUP = GetReaderValue_DateTime(reader, "DLUP", DateTime.MinValue);
                    obj.ROHS = GetReaderValue_Byte(reader, "ROHS", (byte)0);
                    obj.OfferStatusNo = GetReaderValue_NullableInt32(reader, "OfferStatusNo", null);
                    obj.OfferStatusChangeDate = GetReaderValue_NullableDateTime(reader, "OfferStatusChangeDate", null);
                    obj.OfferStatusChangeLoginNo = GetReaderValue_NullableInt32(reader, "OfferStatusChangeLoginNo", null);
                    obj.Notes = GetReaderValue_String(reader, "Notes", "");
                    obj.CurrencyCode = GetReaderValue_String(reader, "CurrencyCode", "");
                    obj.SupplierName = GetReaderValue_String(reader, "SupplierName", "");
                    obj.ManufacturerCode = GetReaderValue_String(reader, "ManufacturerCode", "");
                    obj.ProductName = GetReaderValue_String(reader, "ProductName", "");
                    obj.PackageName = GetReaderValue_String(reader, "PackageName", "");
                    obj.OfferStatusChangeEmployeeName = GetReaderValue_String(reader, "OfferStatusChangeEmployeeName", "");
                    obj.ManufacturerName = GetReaderValue_String(reader, "ManufacturerName", "");
                    obj.ProductDescription = GetReaderValue_String(reader, "ProductDescription", "");
                    obj.PackageDescription = GetReaderValue_String(reader, "PackageDescription", "");
                    obj.SalesmanName = GetReaderValue_String(reader, "SalesmanName", "");
                    obj.SupplierPrice = GetReaderValue_Double(reader, "SupplierPrice", 0);
                    obj.POHubCompanyNo = GetReaderValue_NullableInt32(reader, "POHubCompanyNo", null);
                    obj.POHubSupplierName = GetReaderValue_String(reader, "POHubSupplierName", "");
                    obj.POHubReleaseBy = GetReaderValue_NullableInt32(reader, "POHubReleaseBy", 0);
                    obj.ClientCompanyNo = GetReaderValue_NullableInt32(reader, "ClientCompanyNo", null);
                    obj.ClientSupplierName = GetReaderValue_String(reader, "ClientSupplierName", "");
                    obj.ClientCurrencyNo = GetReaderValue_Int32(reader, "ClientCurrencyNo", 0);
                    obj.ClientCurrencyCode = GetReaderValue_String(reader, "ClientCurrencyCode", "");
                    obj.ConvertedSourcingPrice = GetReaderValue_Double(reader, "ConvertedSourcingPrice", 0);
                    obj.MslSpqFactorySealed = GetReaderValue_String(reader, "MslSpqFactorySealed", "");
                    obj.EstimatedShippingCost = GetReaderValue_NullableDouble(reader, "EstimatedShippingCost", 0);

                    obj.SupplierType = GetReaderValue_String(reader, "SupplierType", "");
                    obj.DeliveryDate = GetReaderValue_NullableDateTime(reader, "DeliveryDate", null);

                    obj.ActualPrice = GetReaderValue_NullableDouble(reader, "SupplierPrice", 0);
                    obj.SupplierPercentage = GetReaderValue_NullableDouble(reader, "UPLiftPrice", 0);

                    obj.SupplierManufacturerName = GetReaderValue_String(reader, "SupplierManufacturerName", "");
                    obj.SupplierDateCode = GetReaderValue_String(reader, "SupplierDateCode", "");
                    obj.SupplierPackageType = GetReaderValue_String(reader, "SupplierPackageType", "");
                    obj.SupplierProductType = GetReaderValue_String(reader, "SupplierProductType", "");
                    obj.SupplierMOQ = GetReaderValue_String(reader, "SupplierMOQ", "");
                    obj.SupplierTotalQSA = GetReaderValue_String(reader, "SupplierTotalQSA", "");
                    obj.SupplierLTB = GetReaderValue_String(reader, "SupplierLTB", "");
                    obj.SupplierNotes = GetReaderValue_String(reader, "SupplierNotes", "");
                    obj.SourcingRelease = GetReaderValue_NullableBoolean(reader, "SourcingReleased", false);
                    obj.SPQ = GetReaderValue_String(reader, "SPQ", "");
                    obj.LeadTime = GetReaderValue_String(reader, "LeadTime", "");
                    obj.ROHSStatus = GetReaderValue_String(reader, "ROHSStatus", "");
                    obj.FactorySealed = GetReaderValue_String(reader, "FactorySealed", "");
                    obj.MSL = GetReaderValue_String(reader, "MSL", "");
                    obj.IsClosed = GetReaderValue_Boolean(reader, "Closed", false);
                    obj.ClientNo = GetReaderValue_Int32(reader, "ClientNo", 0);
                    obj.RegionName = GetReaderValue_String(reader, "RegionName", "");
                    obj.IsClosed = GetReaderValue_Boolean(reader, "Closed", false);
                    obj.IsSoCreated = GetReaderValue_Boolean(reader, "IsSoCreated", false);
                    obj.IsApplyPOBankFee = GetReaderValue_Boolean(reader, "IsApplyPOBankFee", false);
                    obj.TermsName = GetReaderValue_String(reader, "TermsName", "");
                    obj.SourceRef = GetReaderValue_String(reader, "SourceRef", "");

                    obj.OriginalPrice = GetReaderValue_NullableDouble(reader, "BuyPrice", 0);
                    obj.ActualCurrencyNo = GetReaderValue_Int32(reader, "ActualCurrencyNo", 0);
                    obj.ActualCurrencyCode = GetReaderValue_String(reader, "BuyCurrencyCode", "");
                    obj.SourcingReleasedCount = GetReaderValue_Int32(reader, "SourcingReleasedCount", 0);
                    obj.MSLLevelNo = GetReaderValue_Int32(reader, "MSLLevelNo", 0);
                    obj.MSLLevelText = GetReaderValue_String(reader, "MSLLevelText", "");

                    //[001] start
                    obj.SupplierWarranty = GetReaderValue_Int32(reader, "SupplierWarranty", 0);
                    //[001] end
                    //[003] start
                    obj.IsTestingRecommended = GetReaderValue_Boolean(reader, "IsTestingRecommended", false);
                    //[003] end
                    obj.IsImageAvailable = GetReaderValue_NullableBoolean(reader, "IsImageAvailable", false);
                    obj.PriorityNo = GetReaderValue_Int32(reader, "PriorityNo", 0);
                    obj.IHSCountryOfOriginNo = GetReaderValue_Int32(reader, "IHSCountryOfOriginNo", 0);
                    obj.IHSCountryOfOriginName = GetReaderValue_String(reader, "IHSCountryOfOriginName", "");
                    obj.CountryOfOriginNo = GetReaderValue_Int32(reader, "CountryOfOriginNo", 0);
                    obj.CountryOfOriginName = GetReaderValue_String(reader, "CountryOfOriginName", "");
                    obj.ReReleased = GetReaderValue_Int32(reader, "ReReleased", 0);
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get SourcingResults", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        //code added by anand crossmatch
        /// <summary>
        /// GetListForBOMCustomerRequirement 
        /// Calls [usp_selectAll_SourcingResult_for_BOMCustomerRequirement]
        /// </summary>
        public override List<SourcingResultDetails> GetListForBOMCustomerRequirement_List(System.Int32? customerRequirementId, System.Boolean isPOHub)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;

            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_selectAll_SourcingResult_for_BOMCustomerRequirement_List", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@CustomerRequirementId", SqlDbType.Int).Value = customerRequirementId;
                cmd.Parameters.Add("@IsPoHub", SqlDbType.Bit).Value = isPOHub;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<SourcingResultDetails> lst = new List<SourcingResultDetails>();
                while (reader.Read())
                {
                    SourcingResultDetails obj = new SourcingResultDetails();
                    obj.SourcingResultId = GetReaderValue_Int32(reader, "SourcingResultId", 0);
                    obj.CustomerRequirementNo = GetReaderValue_Int32(reader, "CustomerRequirementNo", 0);
                    obj.SourcingTable = GetReaderValue_String(reader, "SourcingTable", "");
                    obj.SourcingTableItemNo = GetReaderValue_NullableInt32(reader, "SourcingTableItemNo", null);
                    obj.TypeName = GetReaderValue_String(reader, "TypeName", "");
                    obj.FullPart = GetReaderValue_String(reader, "FullPart", "");
                    obj.Part = GetReaderValue_String(reader, "Part", "");
                    obj.ManufacturerNo = GetReaderValue_NullableInt32(reader, "ManufacturerNo", null);
                    obj.DateCode = GetReaderValue_String(reader, "DateCode", "");
                    obj.ProductNo = GetReaderValue_NullableInt32(reader, "ProductNo", null);
                    obj.PackageNo = GetReaderValue_NullableInt32(reader, "PackageNo", null);
                    obj.Quantity = GetReaderValue_Int32(reader, "Quantity", 0);
                    obj.Price = GetReaderValue_Double(reader, "Price", 0);
                    obj.CurrencyNo = GetReaderValue_Int32(reader, "CurrencyNo", 0);
                    obj.OriginalEntryDate = GetReaderValue_NullableDateTime(reader, "OriginalEntryDate", null);
                    obj.Salesman = GetReaderValue_Int32(reader, "Salesman", 0);
                    obj.SupplierNo = GetReaderValue_NullableInt32(reader, "SupplierNo", null);
                    obj.UpdatedBy = GetReaderValue_NullableInt32(reader, "UpdatedBy", null);
                    obj.DLUP = GetReaderValue_DateTime(reader, "DLUP", DateTime.MinValue);
                    obj.ROHS = GetReaderValue_Byte(reader, "ROHS", (byte)0);
                    obj.OfferStatusNo = GetReaderValue_NullableInt32(reader, "OfferStatusNo", null);
                    obj.OfferStatusChangeDate = GetReaderValue_NullableDateTime(reader, "OfferStatusChangeDate", null);
                    obj.OfferStatusChangeLoginNo = GetReaderValue_NullableInt32(reader, "OfferStatusChangeLoginNo", null);
                    obj.Notes = GetReaderValue_String(reader, "Notes", "");
                    obj.CurrencyCode = GetReaderValue_String(reader, "CurrencyCode", "");
                    obj.SupplierName = GetReaderValue_String(reader, "SupplierName", "");
                    obj.ManufacturerCode = GetReaderValue_String(reader, "ManufacturerCode", "");
                    obj.ProductName = GetReaderValue_String(reader, "ProductName", "");
                    obj.PackageName = GetReaderValue_String(reader, "PackageName", "");
                    obj.OfferStatusChangeEmployeeName = GetReaderValue_String(reader, "OfferStatusChangeEmployeeName", "");
                    obj.ManufacturerName = GetReaderValue_String(reader, "ManufacturerName", "");
                    obj.ProductDescription = GetReaderValue_String(reader, "ProductDescription", "");
                    obj.PackageDescription = GetReaderValue_String(reader, "PackageDescription", "");
                    obj.SalesmanName = GetReaderValue_String(reader, "SalesmanName", "");
                    obj.SupplierPrice = GetReaderValue_Double(reader, "SupplierPrice", 0);
                    obj.POHubCompanyNo = GetReaderValue_NullableInt32(reader, "POHubCompanyNo", null);
                    obj.POHubSupplierName = GetReaderValue_String(reader, "POHubSupplierName", "");
                    obj.POHubReleaseBy = GetReaderValue_NullableInt32(reader, "POHubReleaseBy", 0);
                    obj.ClientCompanyNo = GetReaderValue_NullableInt32(reader, "ClientCompanyNo", null);
                    obj.ClientSupplierName = GetReaderValue_String(reader, "ClientSupplierName", "");
                    obj.ClientCurrencyNo = GetReaderValue_Int32(reader, "ClientCurrencyNo", 0);
                    obj.ClientCurrencyCode = GetReaderValue_String(reader, "ClientCurrencyCode", "");
                    obj.ConvertedSourcingPrice = GetReaderValue_Double(reader, "ConvertedSourcingPrice", 0);
                    obj.MslSpqFactorySealed = GetReaderValue_String(reader, "MslSpqFactorySealed", "");
                    obj.EstimatedShippingCost = GetReaderValue_NullableDouble(reader, "EstimatedShippingCost", 0);

                    obj.SupplierType = GetReaderValue_String(reader, "SupplierType", "");
                    obj.DeliveryDate = GetReaderValue_NullableDateTime(reader, "DeliveryDate", null);

                    obj.ActualPrice = GetReaderValue_NullableDouble(reader, "SupplierPrice", 0);
                    obj.SupplierPercentage = GetReaderValue_NullableDouble(reader, "UPLiftPrice", 0);

                    obj.SupplierManufacturerName = GetReaderValue_String(reader, "SupplierManufacturerName", "");
                    obj.SupplierDateCode = GetReaderValue_String(reader, "SupplierDateCode", "");
                    obj.SupplierPackageType = GetReaderValue_String(reader, "SupplierPackageType", "");
                    obj.SupplierProductType = GetReaderValue_String(reader, "SupplierProductType", "");
                    obj.SupplierMOQ = GetReaderValue_String(reader, "SupplierMOQ", "");
                    obj.SupplierTotalQSA = GetReaderValue_String(reader, "SupplierTotalQSA", "");
                    obj.SupplierLTB = GetReaderValue_String(reader, "SupplierLTB", "");
                    obj.SupplierNotes = GetReaderValue_String(reader, "SupplierNotes", "");
                    obj.SourcingRelease = GetReaderValue_NullableBoolean(reader, "SourcingReleased", false);
                    obj.SPQ = GetReaderValue_String(reader, "SPQ", "");
                    obj.LeadTime = GetReaderValue_String(reader, "LeadTime", "");
                    obj.ROHSStatus = GetReaderValue_String(reader, "ROHSStatus", "");
                    obj.FactorySealed = GetReaderValue_String(reader, "FactorySealed", "");
                    obj.MSL = GetReaderValue_String(reader, "MSL", "");
                    obj.IsClosed = GetReaderValue_Boolean(reader, "Closed", false);
                    obj.ClientNo = GetReaderValue_Int32(reader, "ClientNo", 0);
                    obj.RegionName = GetReaderValue_String(reader, "RegionName", "");
                    obj.IsClosed = GetReaderValue_Boolean(reader, "Closed", false);
                    obj.IsSoCreated = GetReaderValue_Boolean(reader, "IsSoCreated", false);
                    obj.IsApplyPOBankFee = GetReaderValue_Boolean(reader, "IsApplyPOBankFee", false);
                    obj.TermsName = GetReaderValue_String(reader, "TermsName", "");
                    obj.SourceRef = GetReaderValue_String(reader, "SourceRef", "");

                    obj.OriginalPrice = GetReaderValue_NullableDouble(reader, "BuyPrice", 0);
                    obj.ActualCurrencyNo = GetReaderValue_Int32(reader, "ActualCurrencyNo", 0);
                    obj.ActualCurrencyCode = GetReaderValue_String(reader, "BuyCurrencyCode", "");
                    obj.SourcingReleasedCount = GetReaderValue_Int32(reader, "SourcingReleasedCount", 0);
                    obj.MSLLevelNo = GetReaderValue_Int32(reader, "MSLLevelNo", 0);
                    obj.MSLLevelText = GetReaderValue_String(reader, "MSLLevelText", "");

                    //[001] start
                    obj.SupplierWarranty = GetReaderValue_Int32(reader, "SupplierWarranty", 0);
                    //[001] end
                    //[003] start
                    obj.IsTestingRecommended = GetReaderValue_Boolean(reader, "IsTestingRecommended", false);
                    //[003] end
                    obj.IsImageAvailable = GetReaderValue_NullableBoolean(reader, "IsImageAvailable", false);
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get SourcingResults", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        //end

        /// <summary>
        /// Update SourcingResult
        /// Calls [usp_update_POHubSourcingResult]
        /// </summary>
        //[003] start
        public override bool UpdatePOHub(System.Int32? sourcingResultId, System.String part,
                                         System.Int32? manufacturerNo, System.String dateCode, System.Int32? productNo,
                                         System.Int32? packageNo, System.Int32? quantity, System.Double? price,
                                         System.Int32? currencyNo, System.Int32? offerStatusNo, System.Int32? supplierNo,
                                         System.Byte? rohs, System.String notes, System.Int32? updatedBy,
                                         System.Double? suplierPrice, double? estimatedShippingCost,
                                         DateTime? deliveryDate, bool isPoHub, System.String SPQ, System.String leadTime,
                                         System.String rohsStatus, System.String factorySealed, System.String MSL,
                                         System.String supplierTotalQSA, System.String supplierMOQ,
                                         System.String supplierLTB, System.Int32? regionNo, System.Int32? hubCurrencyNo,
                                         System.Int32? linkMultiCurrencyNo, System.Int32? mslLevelNo,
                                         System.Int32? supplierWarranty, System.Boolean? isTestingRecommended,
                                         System.Int32? PriorityNo, System.Int32? CountryOfOriginNo,
                                         System.String ChangedFields, System.Boolean? PartWatchMatch,
                                         string sellPriceLessReason, System.Int32? TypeOfSupplier,
                                         System.Int32? ReasonForSupplier, System.Int32? RiskOfSupplier,
                                         System.Int32? CountryNo = 0)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_update_POHubSourcingResult", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@SourcingResultId", SqlDbType.Int).Value = sourcingResultId;
                cmd.Parameters.Add("@Part", SqlDbType.NVarChar).Value = part;
                cmd.Parameters.Add("@ManufacturerNo", SqlDbType.Int).Value = manufacturerNo;
                cmd.Parameters.Add("@DateCode", SqlDbType.NVarChar).Value = dateCode;
                cmd.Parameters.Add("@ProductNo", SqlDbType.Int).Value = productNo;
                cmd.Parameters.Add("@PackageNo", SqlDbType.Int).Value = packageNo;
                cmd.Parameters.Add("@Quantity", SqlDbType.Int).Value = quantity;
                cmd.Parameters.Add("@Price", SqlDbType.Float).Value = price;
                //cmd.Parameters.Add("@CurrencyNo", SqlDbType.Int).Value = currencyNo;
                cmd.Parameters.Add("@OfferStatusNo", SqlDbType.Int).Value = offerStatusNo;
                cmd.Parameters.Add("@SupplierNo", SqlDbType.Int).Value = supplierNo;
                cmd.Parameters.Add("@ROHS", SqlDbType.TinyInt).Value = rohs;
                cmd.Parameters.Add("@Notes", SqlDbType.NVarChar).Value = notes;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@SuplierPrice", SqlDbType.Float).Value = suplierPrice;
                cmd.Parameters.Add("@EstimatedShippingCost", SqlDbType.Float).Value = estimatedShippingCost;
                cmd.Parameters.Add("@DeliveryDate", SqlDbType.DateTime).Value = deliveryDate;
                cmd.Parameters.Add("@PUHUB", SqlDbType.Bit).Value = isPoHub;

                cmd.Parameters.Add("@SPQ", SqlDbType.NVarChar).Value = SPQ;
                cmd.Parameters.Add("@LeadTime", SqlDbType.NVarChar).Value = leadTime;
                cmd.Parameters.Add("@ROHSStatus", SqlDbType.NVarChar).Value = rohsStatus;
                cmd.Parameters.Add("@FactorySealed", SqlDbType.NVarChar).Value = factorySealed;
                cmd.Parameters.Add("@MSL", SqlDbType.NVarChar).Value = MSL;
                cmd.Parameters.Add("@SupplierTotalQSA", SqlDbType.NVarChar).Value = supplierTotalQSA;
                cmd.Parameters.Add("@SupplierLTB", SqlDbType.NVarChar).Value = supplierLTB;
                cmd.Parameters.Add("@SupplierMOQ", SqlDbType.NVarChar).Value = supplierMOQ;
                cmd.Parameters.Add("@RegionNo", SqlDbType.Int).Value = regionNo;
                cmd.Parameters.Add("@CurrencyNo", SqlDbType.Int).Value = hubCurrencyNo;
                cmd.Parameters.Add("@LinkMultiCurrencyNo", SqlDbType.Int).Value = linkMultiCurrencyNo;
                cmd.Parameters.Add("@MSLLevelNo", SqlDbType.Int).Value = mslLevelNo;
                cmd.Parameters.Add("@SupplierWarranty", SqlDbType.Int).Value = supplierWarranty;
                //[003] start
                cmd.Parameters.Add("@isTestingRecommended", SqlDbType.Bit).Value = isTestingRecommended;
                //[003] end
                cmd.Parameters.Add("@PriorityNo", SqlDbType.Int).Value = PriorityNo;
                cmd.Parameters.Add("@IHSCountryOfOriginNo", SqlDbType.Int).Value = CountryOfOriginNo;
                cmd.Parameters.Add("@ChangedFields", SqlDbType.NVarChar).Value = ChangedFields;
                cmd.Parameters.Add("@PartWatchMatchHUBIPO", SqlDbType.Bit).Value = PartWatchMatch;
                cmd.Parameters.Add("@TypeOfSupplier", SqlDbType.Int).Value = TypeOfSupplier;
                cmd.Parameters.Add("@ReasonForSupplier", SqlDbType.Int).Value = ReasonForSupplier;
                cmd.Parameters.Add("@RiskOfSupplier", SqlDbType.Int).Value = RiskOfSupplier;
                cmd.Parameters.Add("@CountryNo", SqlDbType.Int).Value = CountryNo;
                cmd.Parameters.Add("@SellPriceLessReason", SqlDbType.NVarChar).Value = sellPriceLessReason;
                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to update SourcingResult", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// ConvertPriceToDifferentCurrency
        /// Calls [usp_Convert_Price_To_Different_Currency]
        /// </summary>
        public override SourcingResultDetails ConvertPriceToDifferentCurrency(System.Int32? intFromCurrency, System.Int32? intToCurrency, System.Double? upliftPrice, System.Double? hubBuyPrice, System.Int32? sourcingResultNo)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_Convert_Price_To_Different_Currency", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                // cmd.Parameters.Add("@FromCurrencyNo", SqlDbType.Int).Value = intFromCurrency;
                cmd.Parameters.Add("@ToCurrencyNo", SqlDbType.Int).Value = intToCurrency;
                //cmd.Parameters.Add("@UpliftPrice", SqlDbType.Float).Value = upliftPrice;
                //cmd.Parameters.Add("@HubBuyPrice", SqlDbType.Float).Value = hubBuyPrice;
                cmd.Parameters.Add("@SourcingResultNo", SqlDbType.Int).Value = sourcingResultNo;

                cn.Open();
                DbDataReader reader = ExecuteReader(cmd, CommandBehavior.SingleRow);
                if (reader.Read())
                {
                    //return GetSourcingResultFromReader(reader);
                    SourcingResultDetails obj = new SourcingResultDetails();
                    obj.CurrencyCode = GetReaderValue_String(reader, "CurrencyCode", "");
                    obj.UPLiftPrice = GetReaderValue_NullableDouble(reader, "UpliftPriceInToCurrency", 0);
                    obj.SupplierPrice = GetReaderValue_NullableDouble(reader, "HubBuyPriceInBase", 0);
                    obj.EstimatedShippingCost = GetReaderValue_NullableDouble(reader, "ToShippingCost", 0);
                    obj.SupplierPercentage = GetReaderValue_NullableDouble(reader, "UpliftPer", 0);


                    return obj;
                }
                else
                {
                    return null;
                }
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to convert price", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }



        /// <summary>
        /// Release 
        /// Calls [usp_update_Sourcing_Release]
        /// </summary>
        public override bool ReleaseSourcing(System.Int32? sourcingResultID, System.Int32? updatedBy)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_update_Sourcing_Release", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@SourcingResultID", SqlDbType.Int).Value = sourcingResultID;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;

                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to update Sourcing Result", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// Approval 
        /// Calls [usp_update_Sourcing_Approval]
        /// </summary>
        public override bool ApproveSourcing(System.Int32? sourcingResultID, System.Int32? updatedBy)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_update_Sourcing_Approval", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@SourcingResultID", SqlDbType.Int).Value = sourcingResultID;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;

                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to approve Sourcing Result", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// Create a new row
        /// Calls [usp_insert_SourcingResult]
        /// </summary>
        //[003] start
        public override Int32 InsertSourcingResult(System.Int32? customerRequirementNo,
                                                   System.String typeName,
                                                   System.String notes,
                                                   System.String part,
                                                   System.Int32? manufacturerNo,
                                                   System.String dateCode,
                                                   System.Int32? productNo,
                                                   System.Int32? packageNo,
                                                   System.Int32? quantity,
                                                   System.Double? price,
                                                   System.DateTime? originalEntryDate,
                                                   System.Int32? salesman,
                                                   System.Int32? offerStatusNo,
                                                   System.Int32? supplierNo,
                                                   System.Byte? rohs,
                                                   System.Int32? clientNo,
                                                   System.Int32? updatedBy,
                                                   System.Double? suplierPrice,
                                                   double? estimatedShippingCost,
                                                   DateTime? deliveryDate,
                                                   bool isPoHub,
                                                   System.String SPQ,
                                                   System.String leadTime,
                                                   System.String rohsStatus,
                                                   System.String factorySealed,
                                                   System.String MSL,
                                                   System.String supplierTotalQSA,
                                                   System.String supplierMOQ,
                                                   System.String supplierLTB,
                                                   System.Int32? regionNo,
                                                   System.Int32? hubCurrencyNo,
                                                   System.Int32? mslLevelNo,
                                                   System.Int32? supplierWarranty,
                                                   System.Boolean? isTestingRecommended,
                                                   out System.String strLinkMessage,
                                                   System.Int32? IHSCountryOfOriginNo,
                                                   string sellPriceLessReason,
                                                   System.Int32? TypeOfSupplier,
                                                   System.Int32? ReasonForSupplier,
                                                   System.Int32? RiskOfSupplier,
                                                   System.Int32? CountryNo = 0)
        {
            strLinkMessage = "";
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                int? custreqno = customerRequirementNo;
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_insert_SourcingResultWithOffer", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@CustomerRequirementNo", SqlDbType.Int).Value = customerRequirementNo;
                cmd.Parameters.Add("@TypeName", SqlDbType.NVarChar).Value = typeName;
                cmd.Parameters.Add("@Notes", SqlDbType.NVarChar).Value = notes;
                cmd.Parameters.Add("@Part", SqlDbType.NVarChar).Value = part;
                cmd.Parameters.Add("@ManufacturerNo", SqlDbType.Int).Value = manufacturerNo;
                cmd.Parameters.Add("@DateCode", SqlDbType.NVarChar).Value = dateCode;
                cmd.Parameters.Add("@ProductNo", SqlDbType.Int).Value = productNo;
                cmd.Parameters.Add("@PackageNo", SqlDbType.Int).Value = packageNo;
                cmd.Parameters.Add("@Quantity", SqlDbType.Int).Value = quantity;
                cmd.Parameters.Add("@Price", SqlDbType.Float).Value = price;
                cmd.Parameters.Add("@OriginalEntryDate", SqlDbType.DateTime).Value = originalEntryDate;
                cmd.Parameters.Add("@Salesman", SqlDbType.Int).Value = salesman;
                cmd.Parameters.Add("@OfferStatusNo", SqlDbType.Int).Value = offerStatusNo;
                cmd.Parameters.Add("@SupplierNo", SqlDbType.Int).Value = supplierNo;
                cmd.Parameters.Add("@ROHS", SqlDbType.TinyInt).Value = rohs;
                cmd.Parameters.Add("@ClientNo", SqlDbType.Int).Value = clientNo;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;

                cmd.Parameters.Add("@SuplierPrice", SqlDbType.Float).Value = suplierPrice;
                cmd.Parameters.Add("@EstimatedShippingCost", SqlDbType.Float).Value = estimatedShippingCost;
                cmd.Parameters.Add("@DeliveryDate", SqlDbType.DateTime).Value = deliveryDate;
                cmd.Parameters.Add("@PUHUB", SqlDbType.Bit).Value = isPoHub;
                cmd.Parameters.Add("@SPQ", SqlDbType.NVarChar).Value = SPQ;
                cmd.Parameters.Add("@LeadTime", SqlDbType.NVarChar).Value = leadTime;
                cmd.Parameters.Add("@ROHSStatus", SqlDbType.NVarChar).Value = rohsStatus;
                cmd.Parameters.Add("@FactorySealed", SqlDbType.NVarChar).Value = factorySealed;
                cmd.Parameters.Add("@MSL", SqlDbType.NVarChar).Value = MSL;
                cmd.Parameters.Add("@SupplierTotalQSA", SqlDbType.NVarChar).Value = supplierTotalQSA;
                cmd.Parameters.Add("@SupplierLTB", SqlDbType.NVarChar).Value = supplierLTB;
                cmd.Parameters.Add("@SupplierMOQ", SqlDbType.NVarChar).Value = supplierMOQ;
                cmd.Parameters.Add("@RegionNo", SqlDbType.Int).Value = regionNo;
                cmd.Parameters.Add("@CurrencyNo", SqlDbType.Int).Value = hubCurrencyNo;
                cmd.Parameters.Add("@MSLLevelNo", SqlDbType.Int).Value = mslLevelNo;

                //  cmd.Parameters.Add("@LinkMultiCurrencyNo", SqlDbType.Int).Value = linkMultiCurrencyNo;
                //[001] start
                cmd.Parameters.Add("@SupplierWarranty", SqlDbType.Int).Value = supplierWarranty;
                //[001] end
                //[003] start
                cmd.Parameters.Add("@isTestingRecommended", SqlDbType.Bit).Value = isTestingRecommended;

                cmd.Parameters.Add("@TypeOfSupplier", SqlDbType.Int).Value = TypeOfSupplier;
                cmd.Parameters.Add("@ReasonForSupplier", SqlDbType.Int).Value = ReasonForSupplier;
                cmd.Parameters.Add("@RiskOfSupplier", SqlDbType.Int).Value = RiskOfSupplier;
                cmd.Parameters.Add("@CountryNo", SqlDbType.Int).Value = CountryNo;
                //[003] end
                cmd.Parameters.Add("@IHSCountryOfOriginNo", SqlDbType.Int).Value = IHSCountryOfOriginNo;
                cmd.Parameters.Add("@SellPriceLessReason", SqlDbType.NVarChar).Value = sellPriceLessReason;
                cmd.Parameters.Add("@SourcingResultId", SqlDbType.Int).Direction = ParameterDirection.Output;
                cmd.Parameters.Add("@LinkCurrencyMsg", SqlDbType.VarChar, 150).Direction = ParameterDirection.Output;

                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                strLinkMessage = (string)cmd.Parameters["@LinkCurrencyMsg"].Value;
                return (Int32)cmd.Parameters["@SourcingResultId"].Value;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to insert SourcingResult", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        /// <summary>
        /// Calls [usp_Delete_ReqPartWatchMatch]
        /// </summary>
        /// <param name="sourcingResultId"></param>
        /// <returns></returns>
        public override Int32 DeletePartWatchMatch(System.String sourcingResultIds)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            int ret = 0;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_Delete_ReqPartWatchMatch", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@SourcingResultID", SqlDbType.NVarChar).Value = sourcingResultIds;
                cn.Open();
                ret = ExecuteNonQuery(cmd);
                return ret;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to Delete PartWatch Match (DeletePartWatchMatch)", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// Calls [usp_Delete_HubPartWatchMatch]
        /// </summary>
        /// <param name="sourcingResultId"></param>
        /// <returns></returns>
        public override Int32 DeleteHubPartWatchMatch(System.String sourcingResultIds)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            int ret = 0;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_Delete_HubPartWatchMatch", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@SourcingResultID", SqlDbType.NVarChar).Value = sourcingResultIds;
                cn.Open();
                ret = ExecuteNonQuery(cmd);
                return ret;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to Delete PartWatch Match (DeletePartWatchMatch)", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        /// Calls [usp_Delete_ReqPartWatchMatch]
        /// </summary>
        /// <param name="sourcingResultId"></param>
        /// <returns></returns>
        public override Int32 DeletePartWatchMatchHUBIPO(System.String sourcingResultIds,System.Int32? updatedBy)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            int ret = 0;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_Delete_ReqPartWatchMatchHUBIPO", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@RequirementNo", SqlDbType.NVarChar).Value = sourcingResultIds;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cn.Open();
                ret = ExecuteNonQuery(cmd);
                return ret;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to Delete PartWatch Match HUBIPO (DeletePartWatchMatchHUBIPO)", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// GetListForBOMReleaseAll 
        /// Calls [usp_selectAll_SourcingResult_for_AllRelease]
        /// </summary>
        public override List<SourcingResultDetails> GetListForBOMReleaseAll(System.Int32? customerRequirementId, System.Boolean isPOHub)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_selectAll_SourcingResult_for_AllRelease", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@CustomerRequirementId", SqlDbType.Int).Value = customerRequirementId;
                cmd.Parameters.Add("@IsPoHub", SqlDbType.Bit).Value = isPOHub;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<SourcingResultDetails> lst = new List<SourcingResultDetails>();
                while (reader.Read())
                {
                    SourcingResultDetails obj = new SourcingResultDetails();
                    obj.SourcingResultId = GetReaderValue_Int32(reader, "SourcingResultId", 0);
                    obj.CustomerRequirementNo = GetReaderValue_Int32(reader, "CustomerRequirementNo", 0);
                    obj.FullPart = GetReaderValue_String(reader, "FullPart", "");
                    obj.Part = GetReaderValue_String(reader, "Part", "");
                    obj.ProductNo = GetReaderValue_NullableInt32(reader, "ProductNo", null);
                    obj.PackageNo = GetReaderValue_NullableInt32(reader, "PackageNo", null);
                    obj.Quantity = GetReaderValue_Int32(reader, "Quantity", 0);
                    obj.Price = GetReaderValue_Double(reader, "Price", 0);
                    obj.CurrencyCode = GetReaderValue_String(reader, "CurrencyCode", "");
                    obj.DeliveryDate = GetReaderValue_NullableDateTime(reader, "DeliveryDate", null);
                    obj.OriginalPrice = GetReaderValue_NullableDouble(reader, "BuyPrice", 0);
                    obj.ActualCurrencyCode = GetReaderValue_String(reader, "BuyCurrencyCode", "");

                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get SourcingResults", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        //////////////////////////////////
        /// <summary>
        /// GetListPVVQuestion 
        /// Calls [usp_selectAll_PVVBOM_Question]
        /// </summary>
        public override List<SourcingResultDetails> GetListPVVQuestion(System.Int32? BomId, System.Boolean isPOHub)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_selectAll_PVVBOM_Question", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@BomId", SqlDbType.Int).Value = BomId;
                cmd.Parameters.Add("@IsPoHub", SqlDbType.Bit).Value = isPOHub;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<SourcingResultDetails> lst = new List<SourcingResultDetails>();
                while (reader.Read())
                {
                    SourcingResultDetails obj = new SourcingResultDetails();
                    obj.PVVQuestionId = GetReaderValue_Int32(reader, "PVVQuestionId", 0);
                    obj.PVVQuestionName = GetReaderValue_String(reader, "PVVQuestionName", "");
                    obj.PVVAnswerId = GetReaderValue_Int32(reader, "PVVAnswerId", 0);
                    obj.PVVAnswerName = GetReaderValue_String(reader, "PVVAnswerName", "");
                    obj.BomId = GetReaderValue_Int32(reader, "BomNo", 0);
                    obj.HUBRFQNo = GetReaderValue_String(reader, "HUBRFQNo", "");
                    
                    
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get PVV BOM Question Result", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override List<SourcingResultDetails> GetListPVVQuestionTemp(string BomId, System.Boolean isPOHub)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_selectAll_Temp_PVVBOM_Question", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@BomIdGenerated", SqlDbType.NVarChar).Value = BomId;
                cmd.Parameters.Add("@IsPoHub", SqlDbType.Bit).Value = isPOHub;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<SourcingResultDetails> lst = new List<SourcingResultDetails>();
                while (reader.Read())
                {
                    SourcingResultDetails obj = new SourcingResultDetails();
                    obj.PVVQuestionId = GetReaderValue_Int32(reader, "PVVQuestionId", 0);
                    obj.PVVQuestionName = GetReaderValue_String(reader, "PVVQuestionName", "");
                    obj.PVVAnswerId = GetReaderValue_Int32(reader, "PVVAnswerId", 0);
                    obj.PVVAnswerName = GetReaderValue_String(reader, "PVVAnswerName", "");
                    obj.BomId = GetReaderValue_Int32(reader, "BomNo", 0);
                    obj.HUBRFQNo = GetReaderValue_String(reader, "HUBRFQNo", "");


                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get PVV BOM Question Result", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        /// <summary>
        /// GetListPVVQuestion 
        /// Calls [usp_selectAll_PVVBOM_Question]
        /// </summary>
        public override List<SourcingResultDetails> GetDataPVV(System.Int32? BomId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_selectAll_PVVBOM", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@BomId", SqlDbType.Int).Value = BomId;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<SourcingResultDetails> lst = new List<SourcingResultDetails>();
                while (reader.Read())
                {
                    SourcingResultDetails obj = new SourcingResultDetails();
                    obj.PVVQuestionId = GetReaderValue_Int32(reader, "PVVQuestionId", 0);
                    obj.PVVQuestionName = GetReaderValue_String(reader, "PVVQuestionName", "");
                    obj.PVVAnswerId = GetReaderValue_Int32(reader, "PVVAnswerId", 0);
                    obj.PVVAnswerName = GetReaderValue_String(reader, "PVVAnswerName", "");
                    obj.BomId = GetReaderValue_Int32(reader, "BomNo", 0);
                    obj.HUBRFQNo = GetReaderValue_String(reader, "HUBRFQNo", "");
                    
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get PVV BOM Question Result", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        /// <summary>
        /// GetListPVVQuestion 
        /// Calls [usp_selectAll_PVVBOM_Question_Temp]
        /// </summary>
        public override List<SourcingResultDetails> GetDataPVVTemp(string BomIdGenerated)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_selectAll_Temp_PVVBOM", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@BomNoGenerated", SqlDbType.NVarChar).Value = BomIdGenerated;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<SourcingResultDetails> lst = new List<SourcingResultDetails>();
                while (reader.Read())
                {
                    SourcingResultDetails obj = new SourcingResultDetails();
                    obj.PVVQuestionId = GetReaderValue_Int32(reader, "PVVQuestionId", 0);
                    obj.PVVQuestionName = GetReaderValue_String(reader, "PVVQuestionName", "");
                    obj.PVVAnswerId = GetReaderValue_Int32(reader, "PVVAnswerId", 0);
                    obj.PVVAnswerName = GetReaderValue_String(reader, "PVVAnswerName", "");
                    obj.BomId = GetReaderValue_Int32(reader, "BomNo", 0);
                    obj.HUBRFQNo = GetReaderValue_String(reader, "HUBRFQNo", "");

                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get PVV BOM Question temp Result", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        /// <summary>
        /// GetListPVVQuestion 
        /// Calls [usp_selectAll_PVVBOM_Question]
        /// </summary>
        public override List<SourcingResultDetails> GetListCOunt(System.Int32? BomId, System.Boolean isPOHub)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_CheckMasterData_PVVBOM", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@BomId", SqlDbType.Int).Value = BomId;
                cmd.Parameters.Add("@IsPoHub", SqlDbType.Bit).Value = isPOHub;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<SourcingResultDetails> lst = new List<SourcingResultDetails>();
                while (reader.Read())
                {
                    SourcingResultDetails obj = new SourcingResultDetails();
                    obj.PVVQuestionId = GetReaderValue_Int32(reader, "PVVQuestionId", 0);
                    obj.PVVQuestionName = GetReaderValue_String(reader, "PVVQuestionName", "");
                    obj.PVVAnswerId = GetReaderValue_Int32(reader, "PVVAnswerId", 0);
                    obj.PVVAnswerName = GetReaderValue_String(reader, "PVVAnswerName", "");
                    obj.BomId = GetReaderValue_Int32(reader, "BomNo", 0);
                    obj.HUBRFQNo = GetReaderValue_String(reader, "HUBRFQNo", "");


                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get PVV BOM Question Result", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
    }
}
