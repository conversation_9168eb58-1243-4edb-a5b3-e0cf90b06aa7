Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.ItemSearch");Rebound.GlobalTrader.Site.Controls.ItemSearch.CustomerRequirementSourcingResults=function(n){Rebound.GlobalTrader.Site.Controls.ItemSearch.CustomerRequirementSourcingResults.initializeBase(this,[n]);this._intQuoteID=0};Rebound.GlobalTrader.Site.Controls.ItemSearch.CustomerRequirementSourcingResults.prototype={initialize:function(){Rebound.GlobalTrader.Site.Controls.ItemSearch.CustomerRequirementSourcingResults.callBaseMethod(this,"initialize");this.addSetupData(Function.createDelegate(this,this.doSetupData));this.addGetDataComplete(Function.createDelegate(this,this.doGetDataComplete))},dispose:function(){this.isDisposed||Rebound.GlobalTrader.Site.Controls.ItemSearch.CustomerRequirementSourcingResults.callBaseMethod(this,"dispose")},doSetupData:function(){this._objData.set_PathToData("controls/ItemSearch/CusReqSourcingResults");this._objData.set_DataObject("CusReqSourcingResults");this._objData.set_DataAction("GetData");this._objData.addParameter("Part",this.getFieldValue("ctlPartNo"));this._objData.addParameter("CM",this.getFieldValue("ctlCompany"));this._objData.addParameter("ReqNoLo",this.getFieldValue_Min("ctlReqNo"));this._objData.addParameter("ReqNoHi",this.getFieldValue_Max("ctlReqNo"));this._objData.addParameter("Supplier",this.getFieldValue("ctlSupplier"));this._objData.addParameter("IsPoHub",this.getFieldValue("ctlIsPoHub"));this._objData.addParameter("intQuoteID",this._intQuoteID);this._objData.addParameter("BOM",this.getFieldValue("ctlBOM"))},doGetDataComplete:function(){for(var t=0,i=this._objResult.Results.length;t<i;t++){var n=this._objResult.Results[t],r=[$R_FN.writeDoubleCellValue(String.format("{0} - {1}",n.No,$R_FN.setCleanTextValue(n.CMName)),$R_FN.setCleanTextValue(n.Supplier)),$R_FN.writePartNo(n.Part,n.ROHS),$R_FN.writeDoubleCellValue(n.isRestrictedManufacturer==1?'<span   style="background-color: #ff0000; color:#282424f2; background-position: right -1px; !important" title="this manufacturer is restricted use"> Restricted Use <\/span>':$R_FN.setCleanTextValue(n.Mfr),$R_FN.setCleanTextValue(n.DC)),$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(n.Product),$R_FN.setCleanTextValue(n.Package)),$R_FN.setCleanTextValue(n.OfferDate),$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(n.Quantity),$R_FN.setCleanTextValue(n.Price)),$R_FN.setCleanTextValue(n.IsPoHub)],u={isRestrictedManufacturer:n.isRestrictedManufacturer},f=n.isRestrictedManufacturer?"ceased2":"";this._tblResults.addRow(r,n.ID,n.ID==this._intInitialID,u,f);r=null;n=null}}};Rebound.GlobalTrader.Site.Controls.ItemSearch.CustomerRequirementSourcingResults.registerClass("Rebound.GlobalTrader.Site.Controls.ItemSearch.CustomerRequirementSourcingResults",Rebound.GlobalTrader.Site.Controls.ItemSearch.Base,Sys.IDisposable);