﻿/*   
===========================================================================================  
TASK			UPDATED BY      DATE			ACTION		DESCRIPTION  
[US-204867]		An.TranTan		25-Sep-2024		Create		Validate import data and insert to tbProspectiveOffer_ToBeImported
===========================================================================================  
*/
CREATE OR ALTER PROCEDURE usp_validate_ProspectiveOfferImport
	@UserId INT,
	@SupplierId INT
WITH RECOMPILE
AS
BEGIN
	CREATE TABLE #tempOfferData
	(
		Manufacturer NVARCHAR(MAX) NULL,
		Part NVARCHAR(MAX) NULL,
		Quantity NVARCHAR(MAX) NULL,
		Price NVARCHAR(MAX) NULL,
		[Description] NVARCHAR(MAX) NULL,
		AlterPart NVARCHAR(MAX) NULL,
		DateCode NVARCHAR(MAX) NULL,
		Product NVARCHAR(MAX) NULL,
		Package NVARCHAR(MAX) NULL,
		ROHSName NVARCHAR(MAX) NULL,
		SupplierPart NVARCHAR(MAX) NULL,
		LineNumber INT NULL,
		OriginalFilename nvarchar(100) NULL,
		IsValid BIT DEFAULT 1,
		ValidationMessage NVARCHAR(MAX) DEFAULT ''
	);
	
	CREATE TABLE #tempManufacturer (ManufacturerId INT, ManufacturerName NVARCHAR(MAX));
	CREATE TABLE #tempPackage (PackageId INT, PackageName NVARCHAR(MAX));
	CREATE TABLE #tempProduct (ProductId INT, ProductName NVARCHAR(MAX));

	DECLARE @ColumnNameList nvarchar(500) = '',
			@ColumnOrderList nvarchar(500) = '',
			@CurrencyNo INT = NULL;
	
	/*======== Create temp offer data BEGIN ========*/
	--get column mapping for supplier
	SELECT @ColumnNameList = ColumnNameList,
		@ColumnOrderList = ColumnOrderList
	FROM dbo.ufn_get_ProsOffer_Supplier_Mapping(@SupplierId);

	IF(ISNULL(@ColumnNameList, '') <> '' AND ISNULL(@ColumnOrderList, '') <> '')
	BEGIN
		DECLARE @InsertTempDataSQL NVARCHAR(MAX);
		SET @InsertTempDataSQL = 'INSERT INTO #tempOfferData('+ @ColumnNameList + ',LineNumber,OriginalFilename)'
			+ ' SELECT ' + @ColumnOrderList + ',LineNumber,OriginalFilename'
			+ ' FROM BorisGlobalTraderImports.dbo.tbProspectiveOffer_tempData'
			+ ' WHERE CreatedBy= ' + CAST(@UserId AS nvarchar(10)); 
		
		EXECUTE  sp_executesql @InsertTempDataSQL;
	END
	/*======== Create temp offer data END ========*/

	/*======== Validate section BEGIN ========*/
	--Part No is required
	UPDATE t
	SET t.ValidationMessage = 'Part No. is mandatory.<br/>'
		,t.IsValid = 0
	FROM #tempOfferData t
	WHERE ISNULL(t.Part, '') = ''

	--Duplicate Part is not allowed
	UPDATE t
	SET t.ValidationMessage = t.ValidationMessage + 'Duplicate Part is not allowed within import file.<br/>'
		,t.IsValid = 0
	FROM #tempOfferData t
		JOIN (
			SELECT Part FROM #tempOfferData GROUP BY Part HAVING COUNT(*) > 1
		) t1 ON t1.Part = t.Part
	WHERE ISNULL(t.Part, '') <> ''
	/*======== Validate section END ========*/


	/*======== Convert to import data BEGIN ========*/
	IF NOT EXISTS (SELECT TOP 1 1 FROM #tempOfferData WHERE IsValid = 0)
	BEGIN
		SELECT @CurrencyNo = ISNULL(FixedCurrencyNo, 0)
		FROM BorisGlobalTraderImports.dbo.tbProspectiveOffer_ColumnMapping
		WHERE SupplierNo = @SupplierId

		--if currency not exists in mapping setting, get PO currency of supplier
		IF ISNULL(@CurrencyNo, 0) = 0
			SELECT @CurrencyNo = POCurrencyNo FROM tbCompany WHERE CompanyId = @SupplierId

		--get DMCC currency at last choice
		IF ISNULL(@CurrencyNo, 0) = 0
			SELECT @CurrencyNo = CurrencyNo FROM tbClient WHERE ClientId = 114 --DMCC

		UPDATE t
		SET 
			t.Part = CASE 
						WHEN LEN(t.Part) > 30 THEN dbo.stripAlphahnumeric(SUBSTRING(t.Part, 0, 30))
						ELSE dbo.stripAlphahnumeric(t.Part)
					END,
			t.ALterPart = CASE 
							WHEN LEN(t.ALterPart) > 30 THEN dbo.stripAlphahnumeric(SUBSTRING(t.ALterPart, 0, 30))
							ELSE dbo.stripAlphahnumeric(t.ALterPart)
						END,
			t.SupplierPart = CASE 
								WHEN LEN(t.SupplierPart) > 30 THEN dbo.stripAlphahnumeric(SUBSTRING(t.SupplierPart, 0, 30))
								ELSE dbo.stripAlphahnumeric(t.SupplierPart)
							END,
			t.Datecode = CASE 
							WHEN LEN(t.Datecode) > 5 THEN dbo.stripAlphahnumeric(SUBSTRING(t.Datecode, 0, 5))
							ELSE  dbo.stripAlphahnumeric(t.Datecode)
						END,
			t.[Description] = CASE
								WHEN LEN(t.[Description]) > 500 THEN SUBSTRING(t.[Description], 0, 500)
									ELSE t.[Description]
							END
		FROM #tempOfferData t

		--get manufacturer data
		INSERT INTO #tempManufacturer(ManufacturerName) 
		SELECT DISTINCT Manufacturer FROM #tempOfferData

		UPDATE temp
		SET temp.ManufacturerId = m1.ManufacturerId
		FROM #tempManufacturer temp
		CROSS APPLY (                
			SELECT MIN(m.ManufacturerId) AS ManufacturerId                
			FROM tbManufacturer AS m                
			WHERE m.ManufacturerName = temp.ManufacturerName AND  m.Inactive = 0
		) AS m1

		--get package data
		INSERT INTO #tempPackage(PackageName) 
		SELECT DISTINCT Package FROM #tempOfferData

		UPDATE temp
		SET temp.PackageId = p1.PackageId
		FROM #tempPackage temp
		CROSS APPLY (                
			SELECT MIN(p.PackageId) AS PackageId                
			FROM tbPackage AS p               
			WHERE p.PackageName = temp.PackageName
		) AS p1

		--get product data
		INSERT INTO #tempProduct(ProductName) SELECT DISTINCT Product FROM #tempOfferData;
		UPDATE temp
		SET temp.ProductId = p1.ProductId
		FROM #tempProduct temp
		CROSS APPLY(
			SELECT MIN(p.ProductId) AS ProductId
			FROM tbProduct AS p
			WHERE p.ProductName = temp.ProductName
		) as p1

		INSERT INTO BorisGlobalTraderImports.dbo.tbProspectiveOffer_ToBeImported
		(
			SupplierNo,
			ManufacturerNo,
			Part,
			AlternativePart,
			Quantity,
			Price,
			[Description],
			DateCode,
			ProductNo,
			PackageNo,
			ROHS,
			CurrencyNo ,
			SupplierPart,
			OriginalFilename,
			CreatedBy,
			DLUP
		)
		SELECT
			@SupplierId,
			m.ManufacturerId,
			t.Part,
			t.AlterPart,
			CASE WHEN TRY_PARSE(t.Quantity AS INT) IS NULL THEN 0
				ELSE CAST(t.Quantity AS INT)
			END,
			CASE WHEN TRY_PARSE(t.Price AS FLOAT) IS NULL THEN 0
				ELSE CAST(t.Price AS FLOAT)
			END,
			t.[Description],
			t.DateCode,
			pr.ProductId,
			pa.PackageId,
			r.ROHSStatusId,
			@CurrencyNo,
			t.SupplierPart,
			t.OriginalFilename,
			@UserId,
			GETDATE()
		FROM #tempOfferData t
			LEFT JOIN #tempManufacturer m ON m.ManufacturerName = t.Manufacturer
			LEFT JOIN #tempPackage pa ON pa.Packagename = t.Package
			LEFT JOIN #tempProduct pr ON pr.ProductName = t.Product
			LEFT JOIN tbROHSStatus r ON r.[Name] = t.ROHSName
	END
	/*======== Convert to import data END ========*/

	--Return invalid data
	SELECT
		t.LineNumber AS 'Line No.',
		Manufacturer,
		Part AS 'Part No.',
		Quantity,
		Price,
		[Description],
		AlterPart AS 'Alt. Part No.',
		Datecode AS 'Date Code',
		Product,
		Package,
		ROHSName AS ROHS,
		SupplierPart AS 'Supplier Part',
		OriginalFilename,
		ValidationMessage AS Reason
	FROM #tempOfferData t
	WHERE t.IsValid = 0
	ORDER BY t.LineNumber ASC

	DROP TABLE #tempOfferData;
	DROP TABLE #tempManufacturer;
	DROP TABLE #tempPackage;
	DROP TABLE #tempProduct;
END

/*
	EXEC usp_validate_ProspectiveOfferImport
		@UserId = 6670,
		@SupplierId = 234399
*/