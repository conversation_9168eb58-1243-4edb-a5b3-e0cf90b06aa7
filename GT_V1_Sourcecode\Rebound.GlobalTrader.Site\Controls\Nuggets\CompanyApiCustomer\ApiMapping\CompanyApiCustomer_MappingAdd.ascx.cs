using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site;
using Rebound.GlobalTrader.Site.Controls;

namespace Rebound.GlobalTrader.Site.Controls.Forms {
    public partial class CompanyApiCustomer_MappingAdd : Base
    {

		#region Locals
        protected IconButton _ibtnSend;
        protected IconButton _ibtnSend_Footer;
		#endregion

		#region Properties

		#endregion

		#region Overrides

		/// <summary>
		/// OnInit
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
			base.OnInit(e);
            TitleText = "";//Functions.GetGlobalResource("FormTitles", "CompanyApiCustomer_MappingAdd");
            AddScriptReference("Controls.Nuggets.CompanyApiCustomer.ApiMapping.CompanyApiCustomer_MappingAdd.js");
		}
		

		/// <summary>
		/// OnPreRender
		/// </summary>
		/// <param name="e"></param>
		protected override void OnPreRender(EventArgs e) {
			WireUpButtons();
			SetupScriptDescriptors();
			base.OnPreRender(e);
		}

		#endregion

		private void WireUpButtons() {
            _ibtnSend = FindIconButton("ibtnSend");
            _ibtnSend_Footer = (IconButton)FindFooterIconButton("ibtnSend");
		}

		/// <summary>
		/// Setup Script Descriptors
		/// </summary>
		private void SetupScriptDescriptors() {
            _scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.Forms.CompanyApiCustomer_MappingAdd", ctlDesignBase.ClientID);
            _scScriptControlDescriptor.AddProperty("intMasterLoginNo", SessionManager.MasterLoginNo);

            //_scScriptControlDescriptor.AddProperty("intCompanyID", _objQSManager.CompanyID);
            //_scScriptControlDescriptor.AddProperty("strCompanyName", _objQSManager.CompanyName);
            //_scScriptControlDescriptor.AddProperty("intContactID", _objQSManager.ContactID);
            //_scScriptControlDescriptor.AddProperty("strSearchCompanyName", _objQSManager.SearchCompanyName);
            //_scScriptControlDescriptor.AddProperty("strLoginName", SessionManager.LoginFullName);
            //if (_objQSManager.CompanyID > 0)
            //{
            //    BLL.Company cmp = BLL.Company.Get(_objQSManager.CompanyID);
            //    if (cmp != null)
            //        _scScriptControlDescriptor.AddProperty("intCompnaySalemanNo", cmp.Salesman);
            //    else
            //        _scScriptControlDescriptor.AddProperty("intCompnaySalemanNo", (Int32)SessionManager.LoginID);
            //}
            //else
            //    _scScriptControlDescriptor.AddProperty("intCompnaySalemanNo", (Int32)SessionManager.LoginID);

            //_scScriptControlDescriptor.AddProperty("intLoginID", (Int32)SessionManager.LoginID);

        }

	}
}
