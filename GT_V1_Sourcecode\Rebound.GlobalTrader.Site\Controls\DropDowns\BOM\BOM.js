Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");Rebound.GlobalTrader.Site.Controls.DropDowns.BOM=function(n){Rebound.GlobalTrader.Site.Controls.DropDowns.BOM.initializeBase(this,[n]);this._intCompanyID=null};Rebound.GlobalTrader.Site.Controls.DropDowns.BOM.prototype={get_intCompanyID:function(){return this._intCompanyID},set_intCompanyID:function(n){this._intCompanyID!==n&&(this._intCompanyID=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.DropDowns.BOM.callBaseMethod(this,"initialize");this.addSetupDataCall(Function.createDelegate(this,this.setupDataCall));this.addGetDataOK(Function.createDelegate(this,this.dataCallOK))},dispose:function(){this.isDisposed||Rebound.GlobalTrader.Site.Controls.DropDowns.BOM.callBaseMethod(this,"dispose")},setupDataCall:function(){this._objData.set_PathToData("controls/DropDowns/BOM");this._objData.set_DataObject("BOM");this._objData.set_DataAction("GetData");this._objData.addParameter("id",this._intCompanyID)},dataCallOK:function(){var t=this._objData._result,n;if(t.BOM)for(n=0;n<t.BOM.length;n++)this.addOption(t.BOM[n].Name,t.BOM[n].ID)}};Rebound.GlobalTrader.Site.Controls.DropDowns.BOM.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.BOM",Rebound.GlobalTrader.Site.Controls.DropDowns.Base);