﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

/*   
===========================================================================================  
TASK			UPDATED BY		DATE			ACTION		DESCRIPTION  
[US-216072]		Trung Pham		12-Dec-2024		CREATE		Create table StarRatingConfig
===========================================================================================  
*/

IF NOT EXISTS (
    SELECT 1
    FROM sys.objects
    WHERE object_id = OBJECT_ID(N'dbo.tbStarRatingConfig') AND type = N'U'
)
BEGIN
    CREATE TABLE [dbo].[tbStarRatingConfig] (
		[StarRatingConfigId] [int] IDENTITY(1,1) NOT NULL,
        [NumOfPO] [int] NOT NULL,
        [CountedStar] [tinyint] NOT NULL,
        [CreatedDate] [datetime] NOT NULL,
        [CreatedBy] [int] NULL,
	PRIMARY KEY CLUSTERED 
	(
		[StarRatingConfigId] ASC
	)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
	) ON [PRIMARY]

	ALTER TABLE [dbo].[tbStarRatingConfig] ADD  CONSTRAINT [DF_tbStarRatingConfig_CountedStar]  DEFAULT ((1)) FOR [CountedStar]
	
	ALTER TABLE [dbo].[tbStarRatingConfig] ADD  CONSTRAINT [DF_tbStarRatingConfig_CreatedDate]  DEFAULT (getdate()) FOR [CreatedDate]
END;