using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.IO;
using System.Diagnostics;
using Rebound.GlobalTrader.Site;
using System.Text;
using Rebound.GlobalTrader.BLL;

namespace Rebound.GlobalTrader.Site.Pages {
	public class PrintPage : Base {

        protected new QueryStringManager _objQSManager = new QueryStringManager(HttpContext.Current.Request.QueryString);
		protected string _strOutputText = "";
		protected bool _blnForEmail = false;
		protected bool _blnHidePrintAds = false;

        protected override void OnInit(EventArgs e) {
#if (DEBUG)
			_blnConfigurationIsDebug = true;
#endif
            BuildSettings objBuildSettings = new BuildSettings();
            _blnHidePrintAds = objBuildSettings.HideReboundPrintMessage();
			_blnForEmail = _objQSManager.EmailMode;
			AddCSSFile("ReboundUI.css");
			AddCSSFile("FlexiDataTable.css");
			EnableViewState = false;
			base.OnInit(e);
        }

		protected override void InitializeCulture() {
			Pages.Base.SetupCulture(this);
			base.InitializeCulture();
		}

        protected override void OnError(EventArgs e) {
            Title = Functions.GetGlobalResource("PageTitles", "Error");

            //format error message
            Exception ex = Server.GetLastError();
            Response.Clear();
            StringBuilder sbMessage = new StringBuilder(Functions.GetGlobalResource("Messages", "ApplicationError"));
            StringBuilder sbDebugInfo = new StringBuilder("");
            sbMessage.AppendFormat("<br /><br /><b>URL</b><br />{0}", Request.Url.PathAndQuery);
            sbMessage.AppendFormat("<br /><br /><b>User</b><br />{0} ({1}), {2}", SessionManager.LoginFullName, SessionManager.LoginID, SessionManager.ClientName);
            sbMessage.AppendFormat("<br /><br /><b>Time</b><br />{0}", DateTime.Now);

            StringBuilder sbExceptionMessage = new StringBuilder("");
            if (ex != null) {
                sbMessage.AppendFormat("<br /><br /><b>Message</b><br />{0}", ex.Message);
                sbExceptionMessage.Append(ex.Message);
                if (ex.InnerException != null) {
                    sbMessage.AppendFormat("<br /><br /><b>Message 2</b><br />{0}", ex.InnerException.Message);
                    sbExceptionMessage.Append(System.Environment.NewLine);
                    sbExceptionMessage.Append(ex.InnerException.Message);
                    if (ex.InnerException.InnerException != null) {
                        sbMessage.AppendFormat("<br /><br /><b>Message 3</b><br />{0}", ex.InnerException.InnerException.Message);
                        sbExceptionMessage.Append(System.Environment.NewLine);
                        sbExceptionMessage.Append(ex.InnerException.InnerException.Message);
                    }
                }
                sbDebugInfo.AppendFormat("<br /><br /><b>Source</b><br />{0}", ex.Source);
                sbDebugInfo.AppendFormat("<br /><br /><b>Stack</b><br />{0}", ex.StackTrace);
                sbDebugInfo = sbDebugInfo.Replace(System.Environment.NewLine, "<br />");
            }
            sbMessage = sbMessage.Replace(System.Environment.NewLine, "<br />");
            Context.ClearError();

#if (DEBUG)
			//write message to the screen
			Response.Write(string.Format("{0}{1}<br />&nbsp;", sbMessage.ToString(), sbDebugInfo.ToString()));
#else
            //write message to the screen
            Response.Write(String.Format("{0}<br />&nbsp;", sbMessage.ToString()));
            sbMessage.Append(sbDebugInfo);
#endif
        }

		#region Misc Text Functions

		protected void ReplaceChopOfText(string strStart, string strEnd, string strReplacement) {
			_strOutputText = PrintPage.ReplaceChopOfText(_strOutputText, strStart, strEnd, strReplacement);
		}
		public static string ReplaceChopOfText(string strIn, string strStart, string strEnd, string strReplacement) {
			string str = PrintPage.GetChopOfText(strIn, strStart, strEnd, true);
			if (str != "") str = strIn.Replace(str, strReplacement);
			return str;
		}


		protected string GetChopOfText(string strStart, string strEnd, bool blnIncludeStartAndEnd) {
			return PrintPage.GetChopOfText(_strOutputText, strStart, strEnd, blnIncludeStartAndEnd);
		}
		public static string GetChopOfText(string strIn, string strStart, string strEnd, bool blnIncludeStartAndEnd) {
			string strOut = "";
			int intStartPos = strIn.IndexOf(strStart);
			if (intStartPos >= 0) {
				int intEndPos = strIn.IndexOf(strEnd);
				if (blnIncludeStartAndEnd) {
					strOut = strIn.Substring(intStartPos, intEndPos - intStartPos + strEnd.Length);
				} else {
					strOut = strIn.Substring(intStartPos + strStart.Length, intEndPos - intStartPos - strStart.Length);
				}
			}
			return strOut;
		}
		protected string GetChopOfText(string strStart, string strEnd) { return GetChopOfText(strStart, strEnd, false); }
		public static string GetChopOfText(string strIn, string strStart, string strEnd) { return PrintPage.GetChopOfText(strIn, strStart, strEnd, false); }

		protected void GetTemplateText(string strFileName) {
			strFileName = String.Format("{0}/{1}.htm", _objSite.TemplatePath, strFileName);
			StreamReader sr = File.OpenText(Server.MapPath(strFileName));
			_strOutputText = sr.ReadToEnd();
			sr.Close();
			sr.Dispose();
			ReplaceResources();
			ReplaceStandardVariables();
		}

		protected void ReplaceResources() {
			string strWork = GetChopOfText("<%@RESOURCES:", "%>");
			ReplaceChopOfText("<%@RESOURCES:", "%>", "");
			string[] aryResources = strWork.Split(',');
			for (int i = 0; i < aryResources.Length; i++) {
				ReplaceResource(aryResources[i].ToString().Trim());
			}
		}

		protected void ReplaceStandardVariables() {
			if (_strOutputText.Length < 1) return;
			string strAd = (_blnHidePrintAds) ? "" : Functions.GetGlobalResource("Misc", "PrintAd");
			if (!_blnForEmail) strAd = String.Format(strAd, "<b>{0}</b>");
			strAd = string.Format(strAd, Functions.GetGlobalResource("Misc", "AppTitle"));
			ReplaceVariable("AD", strAd);
		}

		protected void ReplaceVariable(string strVarName, object objReplace) {
			_strOutputText = PrintPage.ReplaceVariable(_strOutputText, strVarName, objReplace);
		}
		public static string ReplaceVariable(string strIn, string strVarName, object objReplace) {
			if (objReplace == null) objReplace = "";
			return strIn.Replace(PrintPage.GetVariableName(strVarName).ToUpper(), objReplace.ToString());
		}

		public static string GetVariableName(string strVar) {
			return String.Format("[#{0}#]", strVar.ToUpper());
		}

		protected void ReplaceResource(string strResourceName) {
			string strTitle = Functions.GetGlobalResource("Printing", strResourceName);
			if (strTitle != "&nbsp;" && !String.IsNullOrEmpty(strTitle)) {
				_strOutputText = _strOutputText.Replace(GetResourceName(strResourceName), strTitle);
			}
			string strLineHeaderTitle = Functions.GetGlobalResource("Printing", "LineHeader_" + strResourceName);
			if (strLineHeaderTitle != "&nbsp;" && !String.IsNullOrEmpty(strLineHeaderTitle)) {
				_strOutputText = _strOutputText.Replace(GetLineHeaderResourceName(strResourceName), strLineHeaderTitle);
			}
		}

		protected string GetResourceName(string strVar) {
			return String.Format("[#RESOURCE:{0}#]", strVar);
		}

		protected string GetLineHeaderResourceName(string strVar) {
			return String.Format("[#LINE_HEADER_RESOURCE:{0}#]", strVar);
		}

		/// <summary>
		/// Returns a part number showing ROHS status)
		/// </summary>
		protected string GetFormattedPartForPrint(string strPart, int? intROHS) {
			string strPartROHS = "";
			switch (intROHS) {
				case 0:
					strPartROHS = string.Format(" ({0})", Functions.GetGlobalResource("Misc", "ROHSUnknown"));
					break;
				case 1:
					strPartROHS = string.Format(" ({0})", Functions.GetGlobalResource("Misc", "ROHSCompliant"));
					break;
				case 2:
					strPartROHS = string.Format(" ({0})", Functions.GetGlobalResource("Misc", "ROHSNonCompliant"));
					break;
				case 3:
					strPartROHS = string.Format(" ({0})", Functions.GetGlobalResource("Misc", "ROHSExempt"));
					break;
				case 4:
					strPartROHS = string.Format(" ({0})", Functions.GetGlobalResource("Misc", "ROHSNotApplicable"));
					break;
                case 5:
                    strPartROHS = string.Format(" ({0})", Functions.GetGlobalResource("Misc", "ROHSROHS2"));
                    break;
                case 6:
                    strPartROHS = string.Format(" ({0})", Functions.GetGlobalResource("Misc", "ROHSROHS56"));
                    break;
                case 7:
                    strPartROHS = string.Format(" ({0})", Functions.GetGlobalResource("Misc", "ROHSROHS66"));
                    break;
                default:
                    strPartROHS = string.Empty;
                    break;

			}
			string strPartOut = string.Format("{0}{1}", strPart.Trim(), strPartROHS);
			return strPartOut;
		}

		protected string GetFormattedROHSForPrint(int? intROHS)
		{
			string strPartROHS = "";
			switch (intROHS)
			{
				case 0:
					strPartROHS = string.Format("{0}", Functions.GetGlobalResource("Misc", "ROHSUnknown"));
					break;
				case 1:
					strPartROHS = string.Format("{0}", Functions.GetGlobalResource("Misc", "ROHSCompliant"));
					break;
				case 2:
					strPartROHS = string.Format("{0}", Functions.GetGlobalResource("Misc", "ROHSNonCompliant"));
					break;
				case 3:
					strPartROHS = string.Format("{0}", Functions.GetGlobalResource("Misc", "ROHSExempt"));
					break;
				case 4:
					strPartROHS = string.Format("{0}", Functions.GetGlobalResource("Misc", "ROHSNotApplicable"));
					break;
				case 5:
					strPartROHS = string.Format("{0}", Functions.GetGlobalResource("Misc", "ROHSROHS2"));
					break;
				case 6:
					strPartROHS = string.Format("{0}", Functions.GetGlobalResource("Misc", "ROHSROHS56"));
					break;
				case 7:
					strPartROHS = string.Format("{0}", Functions.GetGlobalResource("Misc", "ROHSROHS66"));
					break;
				default:
					strPartROHS = string.Empty;
					break;

			}
			string strPartOut = string.Format("{0}", strPartROHS);
			return strPartOut;
		}

		protected string GetFormattedProductSourceForPrint(bool As9120, int? intProductSource)
        {
            string strPartROHSProductSource = "";
            ProductSource obj = ProductSource.Get(intProductSource);
            if (obj != null)
            {
                strPartROHSProductSource = string.Format("&#91;{0}&#93;", obj.Name);
            }
            string strPartOut = string.Empty;
            if (!string.IsNullOrEmpty(strPartROHSProductSource) && As9120 == true)
                strPartOut = strPartROHSProductSource;
            else
                strPartOut = strPartOut.Trim();
            return strPartOut;
        }
        //protected string GetFormattedProductSourceForPrint(bool As9120,int? intProductSource)
        //{
        //    string strPartROHSProductSource = "";
        //    switch (intProductSource)
        //    {
        //        case 1:
        //            strPartROHSProductSource = string.Format("&#91;{0}&#93;", Functions.GetGlobalResource("Misc", "NonPreferred"));
        //            break;
        //        case 2:
        //            strPartROHSProductSource = string.Format("&#91;{0}&#93;", Functions.GetGlobalResource("Misc", "Traceable"));
        //            break;
        //        case 3:
        //            strPartROHSProductSource = string.Format("&#91;{0}&#93;", Functions.GetGlobalResource("Misc", "Trusted"));
        //            break;
        //        default:
        //            strPartROHSProductSource = string.Empty;
        //            break;

        //    }
        //    string strPartOut = string.Empty;
        //    if (!string.IsNullOrEmpty(strPartROHSProductSource) && As9120 == true)
        //        strPartOut = strPartROHSProductSource;
        //    else
        //        strPartOut = strPartOut.Trim();
        //    return strPartOut;
        //}

		#endregion


    }
}
