using System;
using System.Web;
using System.Web.Services;
using Rebound.GlobalTrader.BLL;

namespace Rebound.GlobalTrader.Site.Controls.Nuggets.Data
{
    [WebService(Namespace = "http://tempuri.org/")]
    [WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    public class CompanyManufacturers : Rebound.GlobalTrader.Site.Data.Base
    {
        public override void ProcessRequest(HttpContext context)
        {
            if (base.init(context))
            {
                switch (Action)
                {
                    case "GetManufacturers": GetManufacturers(); break;
                    case "SaveAddNew": SaveAddNew(); break;
                    case "SaveEdit": SaveEdit(); break;
                    case "Delete": Delete(); break;
                    case "validateDuplicate": validateDuplicate(); break;
                    case "GetCompanyDetailInactive": GetCompanyDetailInactive(); break;
                    case "SaveFranchise": SaveFranchise(); break;
                    case "MarkFranchised": MarkFranchised(); break;
                    default: WriteErrorActionNotFound(); break;
                }
            }
        }

        private void GetManufacturers()
        {
            JsonObject jsn = new JsonObject();
            JsonObject jsnItems = new JsonObject(true);
            var contactGroupID = GetFormValue_NullableInt("ContactGroupID");
            foreach (BLL.ManufacturerLink lnk in BLL.ManufacturerLink.GetListForSupplier(ID, contactGroupID, (int)SessionManager.ClientID, GetFormValue_Int("SortDir")))
            {
                JsonObject jsnItem = new JsonObject();
                jsnItem.AddVariable("ID", lnk.ManufacturerLinkId);
                jsnItem.AddVariable("MfrID", lnk.ManufacturerNo);
                jsnItem.AddVariable("MfrName", lnk.ManufacturerName);
                jsnItem.AddVariable("Rating", lnk.SupplierRating);
                jsnItem.AddVariable("MfrAdvisoryNotes", Functions.ReplaceLineBreaks(lnk.AdvisoryNotes));
                jsnItem.AddVariable("POLineCount", lnk.POLineCount);
                jsnItem.AddVariable("IsOldData", lnk.IsOldData);
                jsnItem.AddVariable("IsFranchised", lnk.IsFranchised);
                jsnItems.AddVariable(jsnItem);
                jsnItem.Dispose(); jsnItem = null;
            }
            jsn.AddVariable("Items", jsnItems);
            OutputResult(jsn);
            jsn.Dispose(); jsn = null;
        }

        private void GetCompanyDetailInactive()
        {
            Company cm = Company.GetCompanyDetailInactive(ID);
            if (cm == null)
            {
                WriteErrorDataNotFound();
            }
            else
            {
                JsonObject jsn = null;
                if (cm != null)
                {
                    jsn = new JsonObject();
                    jsn.AddVariable("CompanyId", cm.CompanyId);
                    jsn.AddVariable("Inactive", cm.Inactive);
                }
                cm = null;
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            cm = null;
        }

        private void SaveEdit()
        {
            BLL.ManufacturerLink lnk = ManufacturerLink.Get(ID);
            if (lnk == null)
            {
                WriteErrorDataNotFound();
            }
            else
            {
                try
                {
                    lnk.SupplierRating = GetFormValue_NullableInt("SupplierRating");
                    lnk.UpdatedBy = LoginID;
                    JsonObject jsn = new JsonObject();
                    jsn.AddVariable("Result", lnk.Update());
                    OutputResult(jsn);
                }
                catch (Exception e)
                {
                    WriteError(e);
                }
                finally
                {
                    lnk = null;
                }
            }
        }

        private void SaveAddNew()
        {
            try
            {
                JsonObject jsn = new JsonObject();
                int intNewID = 0;
                intNewID = BLL.ManufacturerLink.Insert(
                    GetFormValue_NullableInt("ManufacturerNo", 0)
                    , GetFormValue_NullableInt("SupplierNo", 0)
                    , 0
                    , LoginID
                );
                jsn.AddVariable("NewID", 1);
                OutputResult(jsn);
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }

        private void Delete()
        {
            try
            {
                JsonObject jsn = new JsonObject();
                BLL.ManufacturerLink.Delete(ID);
                jsn.AddVariable("Result", true);
                OutputResult(jsn);
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }
        public void validateDuplicate()
        {
            try
            {
                JsonObject jsn = new JsonObject();
                jsn.AddVariable("Result", BLL.Company.validateDuplicate(ID, GetFormValue_NullableInt("ManufacturerNo")));
                OutputResult(jsn);
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }

        private void SaveFranchise()
        {
            try
            {
                JsonObject jsn = new JsonObject();
                bool result = BLL.ManufacturerLink.UpdateFranchise(ID, GetFormValue_Boolean("IsFranchised"), (int)SessionManager.LoginID);
                jsn.AddVariable("Result", result);
                OutputResult(jsn);
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }

        private void MarkFranchised()
        {
            try
            {
                var supplierId = GetFormValue_NullableInt("SupplierId");
                var groupCodeId = GetFormValue_NullableInt("GroupCodeId");

                JsonObject jsn = new JsonObject();
                bool result = BLL.ManufacturerLink.MarkFranchisedBySupplier(supplierId, groupCodeId, SessionManager.ClientID, SessionManager.LoginID);
                jsn.AddVariable("Result", result);
                OutputResult(jsn);
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }
    }
}

