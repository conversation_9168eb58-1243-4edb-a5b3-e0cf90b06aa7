﻿
GO

/****** Object:  StoredProcedure [dbo].[usp_GetXMatchMatchingData]    Script Date: 2/4/2025 11:43:12 AM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


/* 
===========================================================================================
TASK      			UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-212600]		    Phuc Hoang			18-Sep-2024		UPDATE			212600: Utility - Allow cross-matching Offers function (Part 1)
===========================================================================================
*/

CREATE OR ALTER PROCEDURE [dbo].[usp_GetXMatchMatchingData] (          
    @FromDate           DATE,          
    @ToDate             DATE,          
    @MatchType          VARCHAR(500),          
    @MatchFirstValue    INT,          
    @LoginID            INT,          
    @ClinetId           INT          
)          
--* SG 20.04.2022          
--* - Updated SQL for performance (Customer Requirements)          
--* SG 14.04.2022          
--* - Updated to show results for all clients for Jeff          
AS          
BEGIN          
-- SG 20.04.2022          
-- - Requirements match performace has been improved, so allow to match for all clients          
  IF (@MatchType = 'ReqXMatch')          
    SET @ClinetId = 0          
          
  IF (@MatchType = 'InvoiceXMatch')          
  BEGIN          
    SELECT DISTINCT          
      Excess.[XPN] AS XPartNumber,          
      Excess.XQty AS XQty,          
      Excess.XDC AS XDC,          
      Invc.InvoiceNo 'Invoice.No',          
      Invc.Sls 'Invoice.Sls',          
      invc.MFG 'Invoice.MFG',          
      Invc.ClientName 'Invoice.Client',          
      Invc.CustName 'Invoice.Customer',          
      CAST(Invc.InvoiceDate AS date) 'Invoice.Date',          
      Invc.Quantity 'Invoice.Qty',          
      Invc.[Unit Sale] 'Invoice.UnitPrice',          
      Pur.[Purchase Order Number] 'Purchase.Order# ',          
      Pur.Company 'Purchase.Company',          
      Pur.Quantity 'Purchase.Qty',          
      Pur.Price 'Purchase.Price',          
      Pur.Currency 'Purchase.Currency',          
      Pur.Value 'Purchase.Value'         ,
	  Invc.InvoiceCurrencyNo as 'Invoice Currency' 
    FROM BorisGlobalTraderImports.dbo.XMatchExcess Excess          
    JOIN vw_re_Invoice Invc          
      ON Invc.FullPN = Excess.FullPN          
    LEFT JOIN vw_re_Purchase Pur          
      ON Pur.PurchaseOrderLineId = Invc.PurchaseOrderLineId          
      AND Pur.GoodsInLineId = Invc.GoodsInLineNo          
    WHERE CAST(Invc.InvoiceDate AS date) >= CAST(@FromDate AS date)          
    AND CAST(Invc.InvoiceDate AS date) <= CAST(@ToDate AS date)          
    AND Invc.Quantity > 0          
    --AND Invc.[Unit Sale] > 0 -- 19-09-2024: Ignore condition by Mister Jeff          
    --AND (@ClinetId = 0 OR Invc.ClientNo = @ClinetId)-- Commented due to [001] -- SG 14.04.2022          
    AND Excess.UserId = @LoginID          
    GROUP BY Excess.[XPN],          
             Excess.XQty,          
             XQty,          
             Excess.XDC,          
             Invc.InvoiceNo,          
             Invc.Sls,          
             Invc.ClientName,          
             Invc.CustName,          
             Invc.InvoiceDate,          
             Invc.Quantity,          
             Invc.[Unit Sale],          
             Pur.[Purchase Order Number],          
             Pur.Company,          
             Pur.Quantity,          
             Pur.Price,          
             Pur.Currency,          
             Pur.Value,          
             invc.MFG  ,
			 Invc.InvoiceCurrencyNo
    ORDER BY Invc.CustName DESC          
  END          
  -- Above query for RepExs_InvoiceXMatch          
          
  ELSE          
  IF (@MatchType = 'InvoiceXMatchBase')          
  BEGIN          
    SELECT DISTINCT          
      Excess.[XPN] AS XPartNumber,          
      Excess.XQty AS XQty,          
      Excess.XDC AS XDC,          
      CASE          
        WHEN [Excess].FullPN = Invc.[FullPN] THEN '*'          
        ELSE ''          
      END AS 'Exact Match',          
      Invc.InvoiceNo 'Invoice.No',          
      Invc.Sls 'Invoice.Sls',          
      Invc.MFG 'Invoice.MFG',          
      Invc.ClientName 'Invoice.Client',          
      Invc.CustName 'Invoice.Customer',          
      Invc.PartNumber 'Invoice.PartNumber',          
      Invc.Quantity 'Invoice.Qty',          
      Invc.[Unit Sale] 'Invoice.UnitPrice',          
      CAST(Invc.InvoiceDate AS date) 'Invoice.Date',          
      Pur.[Purchase Order Number] 'Purchase.Order# ',          
      Pur.Company 'Purchase.Company',          
      Pur.Quantity 'Purchase.Qty',          
      Pur.Price 'Purchase.Price',          
      Pur.Currency 'Purchase.Currency',       
      Pur.Value 'Purchase.Value' ,
	  Invc.InvoiceCurrencyNo as 'Invoice Currency'         
    FROM BorisGlobalTraderImports.dbo.XMatchExcess Excess          
    JOIN vw_re_Invoice Invc          
      ON Invc.BasePN = Excess.BASEPN          
    LEFT JOIN vw_re_Purchase Pur          
      ON Pur.PurchaseOrderLineId = Invc.PurchaseOrderLineId          
      AND Pur.GoodsInLineId = Invc.GoodsInLineNo          
    WHERE CAST(Invc.InvoiceDate AS date) >= CAST(@FromDate AS date)          
    AND CAST(Invc.InvoiceDate AS date) <= CAST(@ToDate AS date)          
    AND Invc.Quantity > 0          
    --AND Invc.[Unit Sale] > 0 -- 19-09-2024: Ignore condition by Mister Jeff          
    AND Excess.FullPN <> ''          
    --AND (@ClinetId = 0 OR Invc.ClientNo = @ClinetId)-- Commented due to [001]  -- SG 14.04.2022          
    AND Excess.UserId = @LoginID          
    GROUP BY Excess.[XPN],          
             Excess.XQty,          
             Excess.XDC,          
             Invc.InvoiceNo,          
             Invc.Sls,          
             Invc.ClientName,          
             Invc.CustName,          
             Invc.PartNumber,          
             Invc.Quantity,          
             Invc.[Unit Sale],          
             Invc.InvoiceDate,          
             Pur.[Purchase Order Number],          
             Pur.Company,          
             Pur.Quantity,          
             Pur.Price,          
             Pur.Currency,          
             Pur.Value,          
             [Excess].FullPN,          
             Invc.[FullPN],          
             Invc.MFG    ,
			 Invc.InvoiceCurrencyNo 
    ORDER BY Invc.CustName DESC          
  END          
  -- Above for InvoiceXMatchBase          
          
  ELSE          
  IF (@MatchType = 'InvoiceXMatchLike')          
  BEGIN          
    --        SELECT  DISTINCT top (@MatchFirstValue) Excess.[XPN] as XPartNumber,Excess.XQty as XQty,Excess.XDC as XDC,          
    --   Case when [Excess].FullPN=Invc.[FullPN] collate Latin1_General_CI_AS then '*' else '' end as 'Exact Match',          
    --Invc.InvoiceNo 'Invoice.No',Invc.Sls 'Invoice.Sls' ,Invc.ClientName 'Invoice.Client',          
    --Invc.CustName 'Invoice.Customer',Invc.PartNumber 'Invoice.PartNumber',Invc.Quantity 'Invoice.Qty',Invc.[Unit Sale] 'Invoice.UnitPrice',          
    --         CAST(Invc.InvoiceDate as DATE) 'Invoice.Date',Pur.[Purchase Order Number] 'Purchase.Order# ',Pur.Company 'Purchase.Company',Pur.Quantity 'Purchase.Qty',          
    --Pur.Price 'Purchase.Price',Pur.Currency 'Purchase.Currency',Pur.Value 'Purchase.Value'          
    --FROM BorisGlobalTraderImports.dbo.XMatchExcess Excess          
    --JOIN vw_re_Invoice Invc on Invc.FullPN=Excess.FullPN collate Latin1_General_CI_AS          
    --JOIN vw_re_Purchase Pur on Pur.PurchaseOrderLineId=Invc.PurchaseOrderLineId and Pur.GoodsInLineId=Invc.GoodsInLineNo          
    --where CAST(Invc.InvoiceDate as DATE) >= CAST(@FromDate as DATE) and CAST(Invc.InvoiceDate as DATE)<= CAST(@ToDate as DATE)          
    -- group by Excess.[XPN],Excess.XQty,Excess.XDC,Invc.InvoiceNo,Invc.Sls,Invc.ClientName,Invc.CustName,Invc.PartNumber,          
    --    Invc.Quantity ,Invc.[Unit Sale],Invc.InvoiceDate,Pur.[Purchase Order Number],Pur.Company,Pur.Quantity,Pur.Price,Pur.Currency,Pur.Value,[Excess].FullPN,Invc.[FullPN]          
          
    SELECT          
      Excess.[XPN] AS XPartNumber,          
      Excess.XQty AS XQty,          
      Excess.XDC AS XDC,          
      CASE          
        WHEN [Excess].FullPN = Exs_vwInvoice.[Invoice.FullPN] THEN '*'          
        ELSE ''          
      END AS 'Exact Match',          
      Exs_vwInvoice.[Invoice.InvoiceNo],          
      Exs_vwInvoice.[Invoice.Sls],          
   -- Exs_vwInvoice.[Invoice.MFG],          
      Exs_vwInvoice.[Invoice.ClientName] AS 'Invoice.Client',          
      Exs_vwInvoice.[Invoice.CustName] AS 'Invoice.Customer',          
      Exs_vwInvoice.[Invoice.PartNumber],          
      Exs_vwInvoice.[Invoice.Quantity] AS 'Invoice.Qty',          
      Exs_vwInvoice.[Invoice.UnitSale] AS 'Invoice.UnitPrice',          
      Exs_vwInvoice.[Invoice.InvoiceDate] AS 'Invoice.Date',          
      Exs_vwInvoice.[Purchase.PurchaseOrderNumber] AS 'Purchase.Order#',          
      Exs_vwInvoice.[Purchase.Company],          
      Exs_vwInvoice.[Purchase.Quantity] AS 'Purchase.Qty',          
      Exs_vwInvoice.[Purchase.Price],          
      Exs_vwInvoice.[Purchase.Currency],          
      Exs_vwInvoice.[Purchase.Value]          
    FROM BorisGlobalTraderImports.dbo.XMatchExcess Excess,          
         Exs_vwInvoice          
    WHERE (((LEFT([Excess].[FullPN], @MatchFirstValue)) = LEFT([Exs_vwInvoice].[Invoice.FullPN], @MatchFirstValue)))          
    AND (Exs_vwInvoice.[Invoice.InvoiceDate]) >= CAST(@FromDate AS date)          
    AND (Exs_vwInvoice.[Invoice.InvoiceDate]) <= CAST(@ToDate AS date)          
    --AND (@ClinetId = 0 OR Exs_vwInvoice.[Invoice.ClientNo] = @ClinetId)-- Commented due to [001]  -- SG 14.04.2022          
    AND Excess.UserId = @LoginID          
    GROUP BY Excess.[XPN],          
             Excess.XQty,          
             Excess.XDC,          
             Exs_vwInvoice.[Invoice.CustName],          
             Exs_vwInvoice.[Invoice.Quantity],          
             Exs_vwInvoice.[Invoice.UnitSale],          
           Exs_vwInvoice.[Invoice.PartNumber],          
             Exs_vwInvoice.[Invoice.InvoiceDate],          
             [Excess].FullPN,          
             Exs_vwInvoice.[Invoice.FullPN],          
             Exs_vwInvoice.[Purchase.PurchaseOrderNumber],          
             Exs_vwInvoice.[Purchase.Company],          
             Exs_vwInvoice.[Purchase.Part],          
             Exs_vwInvoice.[Purchase.Quantity],          
             Exs_vwInvoice.[Purchase.Price],          
             Exs_vwInvoice.[Purchase.Currency],          
             Exs_vwInvoice.[Purchase.Value],          
             Exs_vwInvoice.[Invoice.InvoiceNo],          
             Exs_vwInvoice.[Invoice.ClientName],          
             Exs_vwInvoice.[Invoice.ClientNo],          
             Exs_vwInvoice.[Invoice.Sls]          
    ORDER BY CASE          
      WHEN [Excess].[FullPN] = Exs_vwInvoice.[Invoice.FullPN] THEN '*'          
    END DESC          
  END          
  -- Above for InvoiceXMatchLike          
          
  ELSE          
  IF (@MatchType = 'ReqXMatch')          
  BEGIN          
  SELECT DISTINCT          
      Excess.[XPN] AS XPartNumber,          
      Excess.XQty AS XQty,          
      Excess.XDC AS XDC,          
      CR.CustomerRequirementNumber AS 'CustReq.No',          
      CR.Salesperson AS 'CustReq.Sls',          
      CR.Mfg AS 'CustReq.Mfg',          
      CR.ClientName AS 'CustReq.Client',          
      CR.Customer AS 'CustReq.Customer',          
      CR.Qty AS 'CustReq.Qty',          
      CR.DC AS 'CustReq.DC',       
   --*[002] code start      
  dr.ServiceName as 'CustReq.Type',       
   --*[002] code end      
      CR.Price AS 'CustReq.Price',          
      CR.Funds AS 'CustReq.Funds',          
      CR.Value AS 'CustReq.Value',          
      CAST(CR.[Date] AS date) AS 'CustReq.DateRequested'      
  , CAST(CR.DatePromised AS date) AS 'CustReq.deliverydate'      
       
    FROM BorisGlobalTraderImports.dbo.XMatchExcess Excess WITH (NoLock)          
    -- SG 20.04.2022          
    JOIN tbCustomerRequirement req WITH (NoLock)          
        ON  CAST(req.ReceivedDate AS date) >= CAST(@FromDate AS date)          
        AND CAST(req.ReceivedDate AS date) <= CAST(@ToDate AS date)          
        --AND (@ClinetId = 0 OR req.ClientNo = @ClinetId)-- Commented due to [001]  -- SG 14.04.2022          
        AND req.FullPart = Excess.FullPN          
  join tbRequirementDropDownData dr on req.ReqType=dr.Id        
    JOIN dbo.vw_re_Cust_Req CR WITH (NoLock)          
        ON CR.CustomerRequirementNo = req.CustomerRequirementId          
    WHERE Excess.UserId = @LoginID          
    GROUP BY Excess.[XPN],          
             Excess.XQty,          
             Excess.XDC,          
             CR.Customer,          
             CR.Qty,          
             CR.DC,     
             CR.Value,          
             CR.Funds,          
             CR.Date,          
             CR.Price,          
             CR.CustomerRequirementNumber,          
             CR.ClientName,          
             CR.Salesperson,          
             CR.Mfg  ,        
     --*[002] code start      
     dr.ServiceName        
   --*[002] code end      
   ,cr.DatePromised    
    ORDER BY CR.Customer DESC          
  END          
  -- Above query for ReqXMatch          
          
  ELSE          
  IF (@MatchType = 'ReqXMatchBase')          
  BEGIN          
    --select DISTINCT Excess.[XPN] as XPartNumber,Excess.XQty as XQty,Excess.XDC as XDC,          
    -- Case when [Excess].FullPN=CR.[FullPN] then '*' end as 'Exact Match',          
    --CR.CustomerRequirementNumber as 'CustReq.No',CR.Salesperson as 'CustReq.Sls',CR.ClientName as 'CustReq.Client',          
    --CR.Customer as 'CustReq.Customer',CR.Qty as 'CustReq.Qty',          
    --CR.DC as 'CustReq.DC',CR.Price as 'CustReq.Price',CR.Funds as 'CustReq.Funds', CR.Value as 'CustReq.Value',CAST(CR.Date as DATE) as 'CustReq.DateRequested'          
    --from BorisGlobalTraderImports.dbo.XMatchExcess Excess          
    --LEFT Join dbo.vw_re_Cust_Req CR on Excess.BASEPN=CR.BasePN          
    --where CAST([Date] as DATE) >= CAST(@FromDate as DATE) and CAST([Date] as DATE)<= CAST(@ToDate as DATE)          
    --and (Excess.BasePN)<>''          
    --and CR.ClientNo=@ClinetId          
   --Group By Excess.[XPN], Excess.XQty,Excess.XDC,CR.Customer,CR.Qty,CR.DC,Value,CR.Funds,CR.Date,CR.Price,CR.CustomerRequirementNumber,          
    --CR.ClientName,CR.Salesperson,[Excess].FullPN,CR.[FullPN]          
          
    SELECT DISTINCT          
      Excess.[XPN] AS XPartNumber,          
      Excess.XQty AS XQty,          
      Excess.XDC AS XDC,          
      CASE          
        WHEN [Excess].FullPN = CR.[FullPN] THEN '*'          
      END AS 'Exact Match',          
      CR.CustomerRequirementNumber AS 'CustReq.No',          
      CR.Salesperson AS 'CustReq.Sls',          
      CR.Mfg AS 'CustReq.Mfg',          
      CR.ClientName AS 'CustReq.Client',          
      CR.Customer AS 'CustReq.Customer',          
      CR.Qty AS 'CustReq.Qty',          
      CR.DC AS 'CustReq.DC',       
    --*[002] code start      
    cr.Type as 'CustReq.Type',       
   --*[002] code end     
      CR.Price AS 'CustReq.Price',          
      CR.Funds AS 'CustReq.Funds',          
      CR.Value AS 'CustReq.Value',          
      CAST(CR.Date AS date) AS 'CustReq.DateRequested'     
   , CAST(CR.DatePromised AS date) AS 'CustReq.deliverydate'     
    FROM BorisGlobalTraderImports.dbo.XMatchExcess Excess          
    LEFT JOIN dbo.vw_re_Cust_Req CR          
      ON Excess.BASEPN = CR.BasePN          
    WHERE CAST([Date] AS date) >= CAST(@FromDate AS date)          
    AND CAST([Date] AS date) <= CAST(@ToDate AS date)          
    AND (Excess.BasePN) <> ''          
    --AND (@ClinetId = 0 OR CR.ClientNo = @ClinetId)-- Commented due to [001]  -- SG 14.04.2022          
    AND Excess.UserId = @LoginID          
    GROUP BY Excess.[XPN],          
             Excess.XQty,          
             Excess.XDC,          
             CR.Customer,          
             CR.Qty,          
             CR.DC,          
             Value,          
             CR.Funds,          
             CR.Date,          
             CR.Price,          
             CR.CustomerRequirementNumber,          
             CR.ClientName,          
             CR.Salesperson,          
             [Excess].FullPN,          
             CR.[FullPN],          
             CR.Mfg     
   ,CR.DatePromised    
   ,cr.Type    
    ORDER BY CR.Customer DESC,          
    CASE          
      WHEN [Excess].[FullPN] = CR.FullPN THEN '*'         
    END DESC          
  END          
  --- Above query for RepExs_ReqXMatchBase          
          
  ELSE          
  IF (@MatchType = 'ReqXMatchLike')          
  BEGIN          
    -- select DISTINCT Excess.[XPN] as XPartNumber,Excess.XQty as XQty,Excess.XDC as XDC,          
    -- Case when [Excess].FullPN=CR.[FullPN] then '*' end as 'Exact Match',          
    -- CR.CustomerRequirementNumber as 'CustReq.No',CR.Salesperson as 'CustReq.Sls',CR.ClientName as 'CustReq.Client',          
    -- CR.Customer as 'CustReq.Customer',CR.Qty as 'CustReq.Qty',          
    -- CR.DC as 'CustReq.DC',CR.Price as 'CustReq.Price',CR.Funds as 'CustReq.Funds', CR.Value as 'CustReq.Value',CAST(CR.Date as DATE) as 'CustReq.DateRequested'          
    -- from BorisGlobalTraderImports.dbo.XMatchExcess Excess, dbo.vw_re_Cust_Req CR          
    -- where CAST([Date] as DATE) >= CAST(@FromDate as DATE) and CAST([Date] as DATE)<= CAST(@ToDate as DATE)          
    -- and (((Left([Excess].[FullPN],@MatchFirstValue))=Left(CR.FullPN,@MatchFirstValue)))          
    -- and CR.ClientNo=@ClinetId          
    -- Group By Excess.[XPN], Excess.XQty,Excess.XDC,Customer,Qty,DC,Value,Funds,Date,CR.Price,CustomerRequirementNumber,ClientName,Salesperson,[Excess].FullPN, CR.[FullPN]          
    --ORDER BY Case WHEN [Excess].[FullPN]= CR.FullPN THEN '*' END DESC          
          
    SELECT DISTINCT          
      Excess.[XPN] AS XPartNumber,          
      Excess.XQty AS XQty,          
      Excess.XDC AS XDC,          
      CASE          
        WHEN [Excess].FullPN = CR.[FullPN] THEN '*'          
      END AS 'Exact Match',          
      CR.CustomerRequirementNumber AS 'CustReq.No',          
      CR.Salesperson AS 'CustReq.Sls',          
      CR.Mfg AS 'CustReq.Mfg',          
      CR.ClientName AS 'CustReq.Client',          
      CR.Customer AS 'CustReq.Customer',          
      CR.Qty AS 'CustReq.Qty',          
      CR.DC AS 'CustReq.DC',      
      --*[002] code start      
    cr.Type as 'CustReq.Type',       
   --*[002] code end     
      CR.Price AS 'CustReq.Price',          
      CR.Funds AS 'CustReq.Funds',          
      CR.Value AS 'CustReq.Value',          
      CAST(CR.Date AS date) AS 'CustReq.DateRequested'      
  , CAST(CR.DatePromised AS date) AS 'CustReq.deliverydate'     
    FROM BorisGlobalTraderImports.dbo.XMatchExcess Excess,          
         dbo.vw_re_Cust_Req CR          
    WHERE CAST([Date] AS date) >= CAST(@FromDate AS date)          
    AND CAST([Date] AS date) <= CAST(@ToDate AS date)          
    AND (((LEFT([Excess].[FullPN], @MatchFirstValue)) = LEFT(CR.FullPN, @MatchFirstValue)))          
    --AND (@ClinetId = 0 OR CR.ClientNo = @ClinetId) -- Commented due to [001] -- SG 14.04.2022          
    AND Excess.UserId = @LoginID          
    GROUP BY Excess.[XPN],          
             Excess.XQty,          
             Excess.XDC,          
             Customer,          
             Qty,          
             DC,          
             Value,          
             Funds,          
             Date,          
     CR.Price,          
             CustomerRequirementNumber,          
             ClientName,          
             Salesperson,          
             [Excess].FullPN,          
             CR.[FullPN],          
             CR.Mfg     
    ,cr.DatePromised    
    ,cr.Type    
    ORDER BY CR.Customer DESC,          
    CASE          
      WHEN [Excess].[FullPN] = CR.FullPN THEN '*'          
    END DESC          
  END          
  --- Above RepExs_ReqXMatchLike          
          
  ELSE          
  IF (@MatchType = 'QuoteXMatch')          
  BEGIN          
    SELECT DISTINCT          
      Excess.[XPN] AS XPartNumber,          
      Excess.XQty AS XQty,          
      Excess.XDC AS XDC,          
      Quote.QuoteNumber AS 'Quote.No',          
      Quote.EmployeeName AS 'Quote.Sls',          
      Quote.ManufacturerName AS 'Quote.ManufacturerName',          
      Quote.ClientName AS 'Quote.Client',          
      Quote.CompanyName AS 'Quote.Customer',          
      Quote.Quantity AS 'Quote.Qty',          
      Quote.DateCode AS 'Quote.DC',          
      Quote.Price AS 'Quote.Price',          
      Quote.CurrencyCode AS 'Quote.Currency',          
      Quote.QuoteDate AS 'Quote.Date'          
    FROM BorisGlobalTraderImports.dbo.XMatchExcess Excess          
    LEFT JOIN vw_re_Quotes Quote          
      ON Quote.FullPart = Excess.FullPN          
    WHERE CAST(Quote.QuoteDate AS date) >= CAST(@FromDate AS date)          
    AND CAST(Quote.QuoteDate AS date) <= CAST(@ToDate AS date)          
    --AND (@ClinetId = 0 OR Quote.ClientNo = @ClinetId)-- Commented due to [001]  -- SG 14.04.2022          
    AND Excess.UserId = @LoginID          
    GROUP BY Excess.[XPN],          
             Excess.XQty,          
             Excess.XDC,          
             Quote.QuoteNumber,          
             Quote.EmployeeName,          
             Quote.ClientName,          
             Quote.CompanyName,          
             Quote.Quantity,          
             Quote.DateCode,          
             Quote.Price,          
             Quote.CurrencyCode,          
             Quote.QuoteDate,          
             Quote.ManufacturerName          
    ORDER BY Quote.CompanyName DESC          
  END          
  -- Above query for RepExs_QuoteXMatch          
          
  ELSE          
  IF (@MatchType = 'QuoteXMatchBase')          
  BEGIN          
    SELECT DISTINCT          
      Excess.[XPN] AS XPartNumber,          
      Excess.XQty AS XQty,          
      Excess.XDC AS XDC,          
      CASE          
        WHEN [Excess].FullPN = Quote.FullPart THEN '*'          
      END AS 'Exact Match',          
      Quote.CompanyName AS 'Quote.Customer',          
      Quote.Quantity AS 'Quote.Qty',          
      Quote.DateCode AS 'Quote.DC',          
      Quote.CurrencyCode AS 'Quote.Currency',          
      Quote.QuoteDate AS 'Quote.Date',        
      Quote.ManufacturerName AS 'Quote.ManufacturerName'          
    FROM BorisGlobalTraderImports.dbo.XMatchExcess Excess          
    LEFT JOIN vw_re_Quotes Quote          
      ON Quote.BasePart = Excess.BASEPN          
    WHERE CAST(Quote.QuoteDate AS date) >= CAST(@FromDate AS date)          
    AND CAST(Quote.QuoteDate AS date) <= CAST(@ToDate AS date)          
    --AND (@ClinetId = 0 OR Quote.ClientNo = @ClinetId)-- Commented due to [001]  -- SG 14.04.2022          
    AND Excess.UserId = @LoginID          
    AND Quote.Quantity > 0          
    --AND Quote.Price > 0  -- 19-09-2024: Ignore condition by Mister Jeff        
    AND Excess.BASEPN <> ''          
    GROUP BY Excess.[XPN],          
             Excess.XQty,          
             Excess.XDC,          
             Quote.CompanyName,          
             Quote.Quantity,          
             Quote.DateCode,          
             Quote.CurrencyCode,          
             Quote.QuoteDate,          
      [Excess].FullPN,          
             Quote.FullPart,          
             Quote.ManufacturerName          
    ORDER BY Quote.CompanyName DESC, CASE          
      WHEN [Excess].[FullPN] = Quote.FullPart THEN '*'          
    END DESC          
  END          
  -- Above query for RepExs_QuoteXMatchBase          
          
  ELSE          
  IF (@MatchType = 'QuoteXMatchLike')          
  BEGIN          
    SELECT DISTINCT          
      Excess.[XPN] AS XPartNumber,          
      Excess.XQty AS XQty,          
      Excess.XDC AS XDC,          
      CASE          
        WHEN [Excess].FullPN = Quote.FullPart THEN '*'          
      END AS 'Exact Match',          
      Quote.QuoteNumber AS 'Quote.No',          
      Quote.EmployeeName AS 'Quote.Sls',          
      Quote.ManufacturerName AS 'Quote.ManufacturerName',          
      Quote.ClientName AS 'Quote.Client',          
      Quote.CompanyName AS 'Quote.Customer',          
      Quote.Part AS 'Quote.Part',          
      Quote.Quantity AS 'Quote.Qty',          
      Quote.DateCode AS 'Quote.DC',          
      Quote.Price AS 'Quote.Price',          
      Quote.CurrencyCode AS 'Quote.Currency',          
      Quote.QuoteDate AS 'Quote.Date'          
    FROM BorisGlobalTraderImports.dbo.XMatchExcess Excess,          
         vw_re_Quotes Quote          
    WHERE CAST(Quote.QuoteDate AS date) >= CAST(@FromDate AS date)          
    AND CAST(Quote.QuoteDate AS date) <= CAST(@ToDate AS date)          
    AND (((LEFT([Excess].[FullPN], @MatchFirstValue)) = LEFT(Quote.FullPart, @MatchFirstValue)))          
    --AND (@ClinetId = 0 OR Quote.ClientNo = @ClinetId) -- Commented due to [001] -- SG 14.04.2022          
    AND Excess.UserId = @LoginID          
    GROUP BY Excess.[XPN],          
             Excess.XQty,          
             Excess.XDC,          
[Excess].FullPN,          
             Quote.FullPart,          
             Quote.QuoteNumber,          
             Quote.EmployeeName,          
             Quote.ClientName,          
             Quote.CompanyName,          
             Quote.Part,          
             Quote.Quantity,          
             Quote.DateCode,          
             Quote.Price,          
             Quote.CurrencyCode,          
             Quote.QuoteDate,          
             Quote.ManufacturerName          
    ORDER BY Quote.CompanyName DESC,          
    CASE          
      WHEN [Excess].[FullPN] = Quote.FullPart THEN '*'          
    END DESC          
  END          
  -- Above query for RepExs_QuoteXMatchLike          
          
  ELSE          
  IF (@MatchType = 'OpenSOXMatch')          
  BEGIN          
    SELECT DISTINCT          
 --code start 001        
      Excess.[XPN] AS XPartNumber,          
      Excess.XQty AS XQty,          
      Excess.XDC AS XDC,          
      OpenSO.CompanyName AS 'OpenSO.Customer',          
      OpenSO.Quantity AS 'OpenSO.Qty',          
      OpenSO.Price AS 'OpenSO.Price',          
      OpenSO.DatePromised AS 'OpenSO.DatePromised',          
      OpenSO.PurchaseOrderNumber AS 'Purchase.Order# ',          
      OpenSO.supp AS 'Purchase.Company',          
      OpenSO.bkorder AS 'OpenSO.BackOrderQty',          
      OpenSO.pur_price AS 'Purchase.Price',          
      OpenSO.buy_cur AS 'Purchase.Currency',          
      OpenSO.allocated_sum AS 'OpenSO.TotalAllocated',          
      OpenSO.SalesOrderNumber AS 'OpenSO.No',          
      OpenSO.ClientName AS 'OpenSO.Client',          
   ManufatureName as 'MFG',        
      OpenSO.CurrencyCode AS 'OpenSO.Currency',          
      OpenSO.Sls AS 'OpenSO.Sls'          
   --code end 001        
    FROM BorisGlobalTraderImports.dbo.XMatchExcess Excess          
    LEFT JOIN vw_re_openso OpenSO          
      ON OpenSO.FullPart = Excess.FullPN          
    WHERE OpenSO.Quantity > 0      
    --AND OpenSO.Price > 0 -- 19-09-2024: Ignore condition by Mister Jeff          
    --AND (@ClinetId = 0 OR OpenSO.ClientNo = @ClinetId)-- Commented due to [001]-- SG 14.04.2022          
    AND Excess.UserId = @LoginID          
 --code start 001        
    GROUP BY Excess.[XPN],          
             Excess.XQty,          
             Excess.XDC,          
             OpenSO.CompanyName,          
             OpenSO.Quantity,          
             OpenSO.Price,          
             OpenSO.DatePromised,          
             OpenSO.PurchaseOrderNumber,          
             OpenSO.supp,          
             OpenSO.bkorder,          
             OpenSO.pur_price,        
    OpenSO.ManufatureName,        
             OpenSO.buy_cur,          
             OpenSO.allocated_sum,          
             OpenSO.SalesOrderNumber,          
             OpenSO.ClientName,          
             OpenSO.CurrencyCode,          
             OpenSO.Sls          
    --code end 001        
    ORDER BY OpenSO.CompanyName DESC          
  END          
  -- Above query for RepExs_OpenSOXMatch          
          
  ELSE          
  IF (@MatchType = 'OpenSOXMatchBase')          
  BEGIN          
    SELECT DISTINCT          
      Excess.[XPN] AS XPartNumber,          
      Excess.XQty AS XQty,          
      Excess.XDC AS XDC,         
      OpenSO.CompanyName AS 'OpenSO.Customer',          
      OpenSO.Quantity AS 'OpenSO.Qty',          
      OpenSO.Price AS 'OpenSO.Price',          
      OpenSO.DatePromised AS 'OpenSO.DatePromised',          
      OpenSO.PurchaseOrderNumber AS 'Purchase.Order# ',          
      OpenSO.supp AS 'Purchase.Company',          
      OpenSO.bkorder AS 'OpenSO.BackOrderQty',          
      OpenSO.pur_price AS 'Purchase.Price',          
      OpenSO.buy_cur AS 'Purchase.Currency',          
      OpenSO.allocated_sum AS 'OpenSO.TotalAllocated',          
      OpenSO.SalesOrderNumber AS 'OpenSO.No',          
      OpenSO.ClientName AS 'OpenSO.Client',          
      OpenSO.CurrencyCode AS 'OpenSO.Currency',          
      OpenSO.Sls AS 'OpenSO.Sls',          
      CASE          
        WHEN [Excess].FullPN = OpenSO.FullPart THEN '*'          
      END AS 'Exact Match',          
      OpenSO.Part AS Part          
FROM BorisGlobalTraderImports.dbo.XMatchExcess Excess          
    LEFT JOIN vw_re_openso OpenSO          
      ON OpenSO.BasePart = Excess.BasePN          
    WHERE OpenSO.Quantity > 0          
    --AND OpenSO.Price > 0 -- 19-09-2024: Ignore condition by Mister Jeff          
    AND Excess.BasePN <> ''          
    --AND (@ClinetId = 0 OR OpenSO.ClientNo = @ClinetId)-- Commented due to [001]-- SG 14.04.2022          
    AND Excess.UserId = @LoginID          
    GROUP BY Excess.[XPN],          
             Excess.XQty,          
             Excess.XDC,          
             OpenSO.CompanyName,          
             OpenSO.Quantity,          
    OpenSO.Price,          
             OpenSO.DatePromised,          
             OpenSO.PurchaseOrderNumber,          
             OpenSO.supp,          
             OpenSO.bkorder,          
             OpenSO.pur_price,          
             OpenSO.buy_cur,          
             OpenSO.allocated_sum,          
             OpenSO.SalesOrderNumber,          
             OpenSO.ClientName,          
             OpenSO.CurrencyCode,          
             OpenSO.Sls,          
             [Excess].FullPN,          
             OpenSO.FullPart,          
             OpenSO.Part          
    ORDER BY OpenSO.CompanyName DESC,          
    CASE          
      WHEN [Excess].[FullPN] = OpenSO.FullPart THEN '*'          
    END DESC          
  END          
  -- Above query for RepExs_OpenSOXMatchBase          
          
  ELSE          
  IF (@MatchType = 'OpenSOXMatchLike')          
  BEGIN          
    SELECT DISTINCT TOP (@MatchFirstValue)          
      Excess.[XPN] AS XPartNumber,          
 Excess.XQty AS XQty,          
      Excess.XDC AS XDC,          
      OpenSO.CompanyName AS 'OpenSO.Customer',          
      OpenSO.Quantity AS 'OpenSO.Qty',          
      OpenSO.Price AS 'OpenSO.Price',          
     OpenSO.DatePromised AS 'OpenSO.DatePromised',          
      OpenSO.PurchaseOrderNumber AS 'Purchase.Order# ',          
      OpenSO.supp AS 'Purchase.Company',          
      OpenSO.bkorder AS 'OpenSO.BackOrderQty',          
      OpenSO.pur_price AS 'Purchase.Price',          
      OpenSO.buy_cur AS 'Purchase.Currency',          
      OpenSO.allocated_sum AS 'OpenSO.TotalAllocated',          
      OpenSO.SalesOrderNumber AS 'OpenSO.No',          
      OpenSO.ClientName AS 'OpenSO.Client',          
      OpenSO.CurrencyCode AS 'OpenSO.Currency',          
      OpenSO.Sls AS 'OpenSO.Sls',          
      CASE          
        WHEN [Excess].FullPN = OpenSO.FullPart THEN '*'          
      END AS 'Exact Match',          
      OpenSO.Part AS Part          
    FROM BorisGlobalTraderImports.dbo.XMatchExcess Excess,          
         vw_re_openso OpenSO          
    WHERE OpenSO.Quantity > 0          
    --AND OpenSO.Price > 0 -- 19-09-2024: Ignore condition by Mister Jeff          
    --AND (@ClinetId = 0 OR OpenSO.ClientNo = @ClinetId) -- Commented due to [001] -- SG 14.04.2022          
    AND Excess.UserId = @LoginID          
    AND (((LEFT([Excess].[FullPN], @MatchFirstValue)) = LEFT(OpenSO.FullPart, @MatchFirstValue)))          
    GROUP BY Excess.[XPN],          
  Excess.XQty,          
             Excess.XDC,          
             OpenSO.CompanyName,          
             OpenSO.Quantity,          
             OpenSO.Price,          
             OpenSO.DatePromised,          
             OpenSO.PurchaseOrderNumber,          
             OpenSO.supp,          
             OpenSO.bkorder,          
             OpenSO.pur_price,          
             OpenSO.buy_cur,          
             OpenSO.allocated_sum,          
             OpenSO.SalesOrderNumber,          
             OpenSO.ClientName,          
             OpenSO.CurrencyCode,          
             OpenSO.Sls,          
             [Excess].FullPN,          
             OpenSO.FullPart,          
             OpenSO.Part          
    ORDER BY OpenSO.CompanyName DESC,          
    CASE          
      WHEN [Excess].[FullPN] = OpenSO.FullPart THEN '*'          
    END DESC          
  END          
  -- Above query for RepExs_OpenSOXMatchLike 

  ELSE 
  IF (@MatchType = 'OffersXMatch')            
  BEGIN            
    SELECT DISTINCT            
		Excess.[XPN] AS XPartNumber,            
		Excess.XQty AS XQty,            
		Excess.XDC AS XDC, 
		offer.Part,
		CASE 
		    WHEN ISNULL(offer.OfferStatusChangeDate, offer.OriginalEntryDate) IS NULL THEN ''
			ELSE FORMAT(ISNULL(offer.OfferStatusChangeDate, offer.OriginalEntryDate), 'dd/MM/yyyy')
		END AS 'Offer.OfferdDate',
		CASE 
		    WHEN  @ClinetId <> 114 AND offer.clientNo = 114 THEN (SELECT TOP 1 CompanyName FROM tbCompany WHERE ClientNo = @ClinetId AND IsPOHub = 1)
			WHEN @ClinetId <> 114 AND @ClinetId <> offer.clientNo AND cl.OwnDataVisibleToOthers <> 1 THEN (SELECT TOP 1 CompanyName FROM tbCompany WHERE ClientNo = offer.clientNo AND IsPOHub = 1)          
			ELSE com.CompanyName 
		END AS 'Offer.Supplier',
		offer.Quantity 'Offer.Quantity',
		CAST(offer.Price AS NVARCHAR) 'Offer.Amount',
		fac.ManufacturerName 'Offer.Manufacturer'
	
    FROM BorisGlobalTraderImports.dbo.tbOffer offer  
    JOIN BorisGlobalTraderImports.dbo.XMatchExcess Excess ON offer.FullPart = Excess.FullPN 
    LEFT JOIN dbo.tbCompany com ON com.CompanyId = offer.SupplierNo
	LEFT JOIN dbo.tbManufacturer fac ON fac.ManufacturerId = offer.ManufacturerNo
	LEFT JOIN tbClient cl ON offer.ClientNo = cl.ClientId

    WHERE CAST(ISNULL(offer.OfferStatusChangeDate, offer.OriginalEntryDate) AS DATE) >= CAST(@FromDate AS DATE)            
    AND CAST(ISNULL(offer.OfferStatusChangeDate, offer.OriginalEntryDate) AS DATE) <= CAST(@ToDate AS DATE)          
    AND offer.Quantity > 0
	--AND offer.Price > 0 -- 19-09-2024: Ignore condition by Mister Jeff 
    AND Excess.UserId = @LoginID       
    GROUP BY Excess.[XPN],    
             Excess.XQty,
             XQty, 
             Excess.XDC, 
			 offer.Part,
             ISNULL(offer.OfferStatusChangeDate, offer.OriginalEntryDate),
			 com.CompanyName,
             offer.Quantity,
             offer.Price,
			 fac.ManufacturerName,
			 offer.clientNo,
			 cl.OwnDataVisibleToOthers

    ORDER BY offer.Part ASC        
  END            
  -- Above query for OfferXMatch  offer date, offer amount, supplier, quantity, and manufacturer. 

  ELSE	
  IF (@MatchType = 'OffersXMatchBase')            
  BEGIN            
    SELECT DISTINCT            
		Excess.[XPN] AS XPartNumber, 
		Excess.XQty AS XQty,    
		Excess.XDC AS XDC, 
		CASE            
			WHEN Excess.FullPN = offer.FullPart THEN '*'            
		END AS 'Exact Match',
		offer.Part,
		CASE 
		    WHEN ISNULL(offer.OfferStatusChangeDate, offer.OriginalEntryDate) IS NULL THEN ''
			ELSE FORMAT(ISNULL(offer.OfferStatusChangeDate, offer.OriginalEntryDate), 'dd/MM/yyyy')
		END AS 'Offer.OfferdDate',
		CASE 
		    WHEN  @ClinetId <> 114 AND offer.clientNo = 114 THEN (SELECT TOP 1 CompanyName FROM tbCompany WHERE ClientNo = @ClinetId AND IsPOHub = 1)
			WHEN @ClinetId <> 114 AND @ClinetId <> offer.clientNo AND cl.OwnDataVisibleToOthers <> 1 THEN (SELECT TOP 1 CompanyName FROM tbCompany WHERE ClientNo = offer.clientNo AND IsPOHub = 1)          
			ELSE com.CompanyName 
		END AS 'Offer.Supplier',
		offer.Quantity 'Offer.Quantity',
		CAST(offer.Price AS NVARCHAR) 'Offer.Amount',
		fac.ManufacturerName 'Offer.Manufacturer'
	
    FROM BorisGlobalTraderImports.dbo.tbOffer offer  
    LEFT JOIN BorisGlobalTraderImports.dbo.XMatchExcess Excess ON dbo.ufn_get_basepart(offer.Part) = Excess.BasePN 
    LEFT JOIN dbo.tbCompany com ON com.CompanyId = offer.SupplierNo
	LEFT JOIN dbo.tbManufacturer fac ON fac.ManufacturerId = offer.ManufacturerNo
	LEFT JOIN tbClient cl ON offer.ClientNo = cl.ClientId

    WHERE CAST(ISNULL(offer.OfferStatusChangeDate, offer.OriginalEntryDate) AS DATE) >= CAST(@FromDate AS DATE)            
    AND CAST(ISNULL(offer.OfferStatusChangeDate, offer.OriginalEntryDate) AS DATE) <= CAST(@ToDate AS DATE)          
    AND offer.Quantity > 0
	--AND offer.Price > 0 -- 19-09-2024: Ignore condition by Mister Jeff 
	AND Excess.BasePN <> '' 
    AND Excess.UserId = @LoginID   

    GROUP BY Excess.[XPN],    
             Excess.XQty,
             XQty, 
             Excess.XDC, 
			 Excess.FullPN,            
             offer.FullPart,
			 offer.Part,
             ISNULL(offer.OfferStatusChangeDate, offer.OriginalEntryDate),
			 com.CompanyName,
             offer.Quantity,
             offer.Price,
			 fac.ManufacturerName,
			 offer.clientNo,
			 cl.OwnDataVisibleToOthers

    ORDER BY offer.Part ASC,
	CASE            
      WHEN [Excess].[FullPN] = offer.FullPart THEN '*'            
    END DESC
  END            
  -- Above query for OfferXMatchBase  offer date, offer amount, supplier, quantity, and manufacturer.

  ELSE
  IF (@MatchType = 'OffersXMatchLike')            
  BEGIN            
    SELECT DISTINCT            
		Excess.[XPN] AS XPartNumber,            
		Excess.XQty AS XQty,            
		Excess.XDC AS XDC, 
		CASE            
			WHEN Excess.FullPN = offer.FullPart THEN '*'            
		END AS 'Exact Match',
		offer.Part,
		CASE 
		    WHEN ISNULL(offer.OfferStatusChangeDate, offer.OriginalEntryDate) IS NULL THEN ''
			ELSE FORMAT(ISNULL(offer.OfferStatusChangeDate, offer.OriginalEntryDate), 'dd/MM/yyyy')
		END AS 'Offer.OfferdDate',
		CASE 
		    WHEN  @ClinetId <> 114 AND offer.clientNo = 114 THEN (SELECT TOP 1 CompanyName FROM tbCompany WHERE ClientNo = @ClinetId AND IsPOHub = 1)
			WHEN @ClinetId <> 114 AND @ClinetId <> offer.clientNo AND cl.OwnDataVisibleToOthers <> 1 THEN (SELECT TOP 1 CompanyName FROM tbCompany WHERE ClientNo = offer.clientNo AND IsPOHub = 1)          
			ELSE com.CompanyName 
		END AS 'Offer.Supplier',
		offer.Quantity 'Offer.Quantity',
		CAST(offer.Price AS NVARCHAR) 'Offer.Amount',
		fac.ManufacturerName 'Offer.Manufacturer'
	
    FROM BorisGlobalTraderImports.dbo.tbOffer offer 
	CROSS JOIN BorisGlobalTraderImports.dbo.XMatchExcess Excess
    LEFT JOIN dbo.tbCompany com ON com.CompanyId = offer.SupplierNo
	LEFT JOIN dbo.tbManufacturer fac ON fac.ManufacturerId = offer.ManufacturerNo
	LEFT JOIN tbClient cl ON offer.ClientNo = cl.ClientId

    WHERE CAST(ISNULL(offer.OfferStatusChangeDate, offer.OriginalEntryDate) AS DATE) >= CAST(@FromDate AS DATE)            
    AND CAST(ISNULL(offer.OfferStatusChangeDate, offer.OriginalEntryDate) AS DATE) <= CAST(@ToDate AS DATE)  
	AND (((LEFT([Excess].[FullPN], @MatchFirstValue)) = LEFT(offer.FullPart, @MatchFirstValue)))    
    AND offer.Quantity > 0
	--AND offer.Price > 0 -- 19-09-2024: Ignore condition by Mister Jeff 
    AND Excess.UserId = @LoginID       
    GROUP BY Excess.[XPN],    
             Excess.XQty,
             XQty, 
             Excess.XDC, 
			 offer.Part,
			 Excess.FullPN,            
             offer.FullPart,
             ISNULL(offer.OfferStatusChangeDate, offer.OriginalEntryDate),
			 com.CompanyName,
             offer.Quantity,
             offer.Price,
			 fac.ManufacturerName,
			 offer.clientNo,
			 cl.OwnDataVisibleToOthers

    ORDER BY offer.Part ASC,
	CASE            
      WHEN Excess.FullPN = offer.FullPart THEN '*'            
    END DESC
  END 
  -- Above query for OfferXMatchLike  offer date, offer amount, supplier, quantity, and manufacturer.
  
END -- END of BEGIN Stored Proc
--RP-2603 Devendra SQL Script 

GO


