using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;

using Rebound.GlobalTrader.Site.Controls;
using Rebound.GlobalTrader.Site;


namespace Rebound.GlobalTrader.Site.Controls.Forms {
	public partial class BOMCusReqSourcingResults_Edit : Base {

		#region Locals

		#endregion

		#region Overrides

		/// <summary>
		/// OnInit
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
			base.OnInit(e);
            TitleText = SessionManager.IsPOHub.Value == true ? Functions.GetGlobalResource("FormTitles", "CusReqSourcingResults_EditHub"): Functions.GetGlobalResource("FormTitles", "CusReqSourcingResults_Edit") ;
			AddScriptReference("Controls.Nuggets.BOMCusReqSourcingResults.Edit.BOMCusReqSourcingResults_Edit.js");
		}

		/// <summary>
		/// OnPreRender
		/// </summary>
		/// <param name="e"></param>
		protected override void OnPreRender(EventArgs e) {
			SetupScriptDescriptors();
			base.OnPreRender(e);
		}

		#endregion

		/// <summary>
		/// Setup Script Descriptors
		/// </summary>
		private void SetupScriptDescriptors() {
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.Forms.BOMCusReqSourcingResults_Edit", ctlDesignBase.ClientID);
			_scScriptControlDescriptor.AddProperty("intCustomerRequirementID", _objQSManager.CustomerRequirementID);
            _scScriptControlDescriptor.AddElementProperty("lblUpliftPrice", FindFieldControl("ctlPrice", "lblUpliftPrice").ClientID);
            _scScriptControlDescriptor.AddElementProperty("lblProduct", FindFieldControl("ctlProduct", "lblProduct").ClientID);
            _scScriptControlDescriptor.AddElementProperty("lblPackage", FindFieldControl("ctlPackage", "lblPackage").ClientID);
            _scScriptControlDescriptor.AddElementProperty("lblManufacturer", FindFieldControl("ctlManufacturer", "lblManufacturer").ClientID);

            _scScriptControlDescriptor.AddElementProperty("lblClientPrice", FindFieldControl("ctlPriceClient", "txtcPrice_Currency").ClientID);
            _scScriptControlDescriptor.AddElementProperty("lblSupplierPrice", FindFieldControl("ctlSupplierPrice", "lblSupplierPrice_Currency").ClientID);
            _scScriptControlDescriptor.AddElementProperty("lblQty", FindFieldControl("ctlQuantity", "lblQty").ClientID);
		}

	}
}