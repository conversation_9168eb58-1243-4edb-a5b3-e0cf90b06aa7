﻿///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.HomeNuggets");

Rebound.GlobalTrader.Site.Controls.HomeNuggets.OpenSupplierPOApproval = function (element) {
    Rebound.GlobalTrader.Site.Controls.HomeNuggets.OpenSupplierPOApproval.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.HomeNuggets.OpenSupplierPOApproval.prototype = {

    get_pnlOpenSupplierPOApproval: function () { return this._pnlOpenSupplierPOApproval; }, set_pnlOpenSupplierPOApproval: function (value) { if (this._pnlOpenSupplierPOApproval !== value) this._pnlOpenSupplierPOApproval = value; },
    get_tblOpenSupplierPOApproval: function () { return this._tblOpenSupplierPOApproval; }, set_tblOpenSupplierPOApproval: function (value) { if (this._tblOpenSupplierPOApproval !== value) this._tblOpenSupplierPOApproval = value; },


    initialize: function () {
        Rebound.GlobalTrader.Site.Controls.HomeNuggets.OpenSupplierPOApproval.callBaseMethod(this, "initialize");
        this.addRefreshEvent(Function.createDelegate(this, this.getData));
        this.getData();
    },

    dispose: function () {
        if (this.isDisposed) return;
        if (this._tblOpenSupplierPOApproval) this._tblOpenSupplierPOApproval.dispose();
        this._pnlOpenSupplierPOApproval = null;
        this._tblOpenSupplierPOApproval = null;
        Rebound.GlobalTrader.Site.Controls.HomeNuggets.OpenSupplierPOApproval.callBaseMethod(this, "dispose");
    },

    setupLoadingState: function () {
        $R_FN.showElement(this._pnlOpenSupplierPOApproval, false);
        Rebound.GlobalTrader.Site.Controls.HomeNuggets.OpenSupplierPOApproval.callBaseMethod(this, "setupLoadingState");
    },

    showNoData: function (bln) {
        this.showContent(true);
        $R_FN.showElement(this._pnlNoData, bln);
    },

    getData: function () {
        this.setupLoadingState();
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/HomeNuggets/OpenSupplierPOApproval");
        obj.set_DataObject("OpenSupplierPOApproval");
        obj.set_DataAction("GetData");

        obj.addDataOK(Function.createDelegate(this, this.getDataComplete));
        obj.addError(Function.createDelegate(this, this.homeNuggetDataError));
        obj.addTimeout(Function.createDelegate(this, this.homeNuggetDataError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    getDataComplete: function (args) {
        var result = args._result;
        var aryData, row;
        this._tblOpenSupplierPOApproval.clearTable();
        for (var i = 0; i < result.POApprovalStatus.length; i++) {
            row = result.POApprovalStatus[i];
            aryData = [
                $RGT_nubButton_PurchaseOrder(row.ID, row.No),
                row.LineManagerApproveStatus,
                row.QualityStatus,
                row.Date,
                row.DateOrdered
            ];
            this._tblOpenSupplierPOApproval.addRow(aryData, null);
        }
        $R_FN.showElement(this._pnlOpenSupplierPOApproval, result.POApprovalStatus.length > 0);
        this.hideLoading();
        this.showNoneFoundOrContent(result.POApprovalStatus.length);
    }
};

Rebound.GlobalTrader.Site.Controls.HomeNuggets.OpenSupplierPOApproval.registerClass("Rebound.GlobalTrader.Site.Controls.HomeNuggets.OpenSupplierPOApproval", Rebound.GlobalTrader.Site.Controls.HomeNuggets.Base, Sys.IDisposable);
