/*   
===========================================================================================  
TASK        UPDATED BY       DATE          ACTION    DESCRIPTION  
[BUG-237113]    Cuong Do	25-Mar-2025	  Update   change formular for Salesman2
===========================================================================================  
*/
CREATE OR ALTER PROCEDURE [dbo].[usp_kpi_getClientWiseCustomerRevenueSummary]
    @ClientNo int,
    @StartDate datetime = null,
    @EndDate datetime = null,
    @IncludeCredits bit = 1,
    @ViewMyReport bit = 0,
    @IntLoginId int = NULL,
    @IntTeamNo int = NULL,
    @IntDivisionNo int = NULL,
    @invMonth int = 0,
    @yearNo int = 0
AS
SET NOCOUNT ON
SET ANSI_WARNINGS OFF
SET ARITHIGNORE ON

-- Create the temporary tables    
CREATE TABLE #Invoices
(
    InvoiceID int,
    CurrencyRate float,
    CompanyNo int,
    ShippingCost float,
    Freight float,
    Salesman int,
    SalesmanPct float
)

CREATE TABLE #InvoicePreSummary
(
    Salesman int,
    CompanyNo int,
    InvoiceID int,
    ShippingCost float,
    Freight float,
    Cost float,
    Resale float,
    SalesmanPct float
)

CREATE TABLE #InvoiceSummary
(
    Salesman int,
    CompanyNo int,
    Cost float,
    Resale float,
    SalesmanPct float
)
CREATE TABLE #InvoicePostSummary
(
    Salesman int,
    CompanyNo int,
    Cost float,
    Resale float
)
CREATE TABLE #Credits
(
    CreditNo int,
    CompanyNo int,
    CurrencyRate float,
    Salesman int,
    SalesmanPct float
)
CREATE TABLE #CreditSummary
(
    CompanyNo int,
    Cost float,
    Resale float,
    Salesman int
)
-- Put the list of invoices into a temporary table    
INSERT INTO #Invoices
SELECT InvoiceID,
       dbo.ufn_get_exchange_rate(a.CurrencyNo, a.InvoiceDate),
       a.CompanyNo,
       isnull(a.ShippingCost, 0),
       isnull(a.Freight, 0),
       a.Salesman,
       (100 - a.Salesman2Percent)
FROM dbo.tbInvoice a
    LEFT JOIN tbLogin lg
        on lg.LoginId = a.Salesman
WHERE a.ClientNo = @ClientNo
      --VIEW REPORT--  
      -- AND ((@ViewMyReport = 0)   OR (@ViewMyReport = 1   AND a.Salesman=@IntLoginId))   
      --Espire 28 Dec 2018:  REB12439 - Team and Division reports  
      AND (
              (@IntTeamNo IS NULL)
              OR (
                     NOT @IntTeamNo IS NULL
                     AND lg.TeamNo = @IntTeamNo
                 )
          )
      AND (
              (@IntDivisionNo IS NULL)
              OR (
                     NOT @IntDivisionNo IS NULL
                     AND lg.DivisionNo = @IntDivisionNo
                 )
          )
      AND (
              (@IntLoginId IS NULL)
              OR (
                     NOT @IntLoginId IS NULL
                     AND (a.Salesman = @IntLoginId)
                 )
          )
      -------END -----   
      AND dbo.ufn_get_date_from_datetime(a.InvoiceDate)
      BETWEEN @StartDate AND @EndDate
      AND a.SupplierRMANo IS NULL
--and again for salesman2    
INSERT INTO #Invoices
SELECT InvoiceID,
       dbo.ufn_get_exchange_rate(a.CurrencyNo, a.InvoiceDate),
       a.CompanyNo,
       isnull(a.ShippingCost, 0),
       isnull(a.Freight, 0),
       a.Salesman2,
       a.Salesman2Percent
FROM dbo.tbInvoice a
    --JOIN    dbo.tbSalesOrder b ON a.SalesOrderNo = b.SalesOrderId --07/02/2013    
    LEFT JOIN tbLogin lg
        on lg.LoginId = a.Salesman2
WHERE a.ClientNo = @ClientNo
      --VIEW REPORT--  
      --AND ((@ViewMyReport = 0)   OR (@ViewMyReport = 1   AND a.Salesman2=@IntLoginId))   
      --Espire 28 Dec 2018:  REB12439 - Team and Division reports  
      AND (
              (@IntTeamNo IS NULL)
              OR (
                     NOT @IntTeamNo IS NULL
                     AND lg.TeamNo = @IntTeamNo
                 )
          )
      AND (
              (@IntDivisionNo IS NULL)
              OR (
                     NOT @IntDivisionNo IS NULL
                     AND lg.DivisionNo = @IntDivisionNo
                 )
          )
      AND (
              (@IntLoginId IS NULL)
              OR (
                     NOT @IntLoginId IS NULL
                     AND (Salesman2 = @IntLoginId)
                 )
          )

      -------END -----  
      AND dbo.ufn_get_date_from_datetime(a.InvoiceDate)
      BETWEEN @StartDate AND @EndDate
      AND a.SupplierRMANo IS NULL
      AND a.Salesman2 IS NOT NULL
--AND a.Salesman2Percent > 0

--prepare the summary of invoices - necessary to get the shipping and freight once per invoice     
INSERT INTO #InvoicePreSummary
SELECT i.Salesman,
       i.CompanyNo,
       i.InvoiceID,
       i.ShippingCost,
       i.Freight / i.CurrencyRate,
       isnull(sum(ila.LandedCost * ila.Quantity), 0),
       isnull(sum((ila.Price * ila.Quantity) / i.CurrencyRate), 0),
       i.SalesmanPct
FROM #Invoices i
    LEFT JOIN dbo.tbInvoiceLine il
        ON il.InvoiceNo = i.InvoiceId
    LEFT JOIN dbo.vwInvoiceLineAllocation ila
        ON ila.InvoiceLineNo = il.InvoiceLineId
GROUP BY i.Salesman,
         i.CompanyNo,
         i.InvoiceID,
         i.ShippingCost,
         i.Freight / i.CurrencyRate,
         i.SalesmanPct

--summarise the invoices    
INSERT INTO #InvoiceSummary
SELECT Salesman,
       CompanyNo,
       sum(Cost) + sum(ShippingCost),
       sum(Resale) + sum(Freight),
       SalesmanPct
FROM #InvoicePreSummary
GROUP BY Salesman,
         CompanyNo,
         SalesmanPct


--now summarise to get rid of the different percentages    
INSERT INTO #InvoicePostSummary
SELECT i.Salesman,
       i.CompanyNo,
       sum((i.Cost / 100) * i.SalesmanPct),
       sum((i.Resale / 100) * i.SalesmanPct)
FROM #InvoiceSummary i
GROUP BY i.Salesman,
         i.CompanyNo

--minus credits if required    
IF @IncludeCredits = 1
BEGIN
    --get all credits    
    INSERT INTO #Credits
    SELECT c.CreditId,
           c.CompanyNo,
           dbo.ufn_get_exchange_rate(c.CurrencyNo, c.InvoiceDate),
           Salesman,
           (100 - Salesman2Percent)
    FROM dbo.vwCredit c
        LEFT JOIN tbLogin lg
            on lg.LoginId = c.Salesman
    WHERE c.ClientNo = @ClientNo
          --VIEW REPORT--  
          -- AND ((@ViewMyReport = 0)   OR (@ViewMyReport = 1   AND Salesman=@IntLoginId))   
          --Espire 28 Dec 2018:  REB12439 - Team and Division reports  
          AND (
                  (@IntTeamNo IS NULL)
                  OR (
                         NOT @IntTeamNo IS NULL
                         AND lg.TeamNo = @IntTeamNo
                     )
              )
          AND (
                  (@IntDivisionNo IS NULL)
                  OR (
                         NOT @IntDivisionNo IS NULL
                         AND lg.DivisionNo = @IntDivisionNo
                     )
              )
          AND (
                  (@IntLoginId IS NULL)
                  OR (
                         NOT @IntLoginId IS NULL
                         AND (Salesman = @IntLoginId)
                     )
              )
          -------END -----  
          AND dbo.ufn_get_date_from_datetime(c.CreditDate)
          BETWEEN @StartDate AND @EndDate

    --again for salesman2

    INSERT INTO #Credits
    SELECT c.CreditId,
           c.CompanyNo,
           dbo.ufn_get_exchange_rate(c.CurrencyNo, c.InvoiceDate),
           Salesman2,
           (Salesman2Percent)
    FROM dbo.vwCredit c
        LEFT JOIN tbLogin lg
            on lg.LoginId = c.Salesman2
    WHERE c.ClientNo = @ClientNo
          --VIEW REPORT--  
          -- AND ((@ViewMyReport = 0)   OR (@ViewMyReport = 1   AND Salesman=@IntLoginId))   
          --Espire 28 Dec 2018:  REB12439 - Team and Division reports  
          AND (
                  (@IntTeamNo IS NULL)
                  OR (
                         NOT @IntTeamNo IS NULL
                         AND lg.TeamNo = @IntTeamNo
                     )
              )
          AND (
                  (@IntDivisionNo IS NULL)
                  OR (
                         NOT @IntDivisionNo IS NULL
                         AND lg.DivisionNo = @IntDivisionNo
                     )
              )
          AND (
                  (@IntLoginId IS NULL)
                  OR (
                         NOT @IntLoginId IS NULL
                         AND (Salesman2 = @IntLoginId)
                     )
              )
          -------END -----  
          AND dbo.ufn_get_date_from_datetime(c.CreditDate)
          BETWEEN @StartDate AND @EndDate
          AND Salesman2 IS NOT NULL
    --summarise credits                                                                    
    INSERT INTO #CreditSummary
    SELECT v.CompanyNo,
           SUM(v.cost),
           SUM(v.Resale),
           v.Salesman
    FROM
    (
        SELECT isnull((((cr.CreditCost + cr.ShippingCost) / 100) * SalesmanPct), 0) AS Cost,
               isnull((((cr.CreditValue + cr.Freight) / 100) * SalesmanPct) / CurrencyRate, 0) AS Resale,
               c.Salesman,
               c.CompanyNo
        FROM #Credits c
            LEFT JOIN dbo.vwCredit cr
                ON cr.CreditId = c.CreditNo
    ) AS v
    GROUP BY Salesman,
             CompanyNo

    --subtract credits from invoices    
    UPDATE #InvoicePostSummary
    SET Cost = i.Cost - c.Cost,
        Resale = i.Resale - c.Resale
    FROM #InvoicePostSummary i
        JOIN #CreditSummary c
            ON c.Salesman = i.Salesman
               AND c.CompanyNo = i.CompanyNo

    --add companies with no Invoices but some Credits    
    INSERT INTO #InvoicePostSummary
    SELECT c.Salesman,
           c.CompanyNo,
           -c.Cost,
           -c.Resale
    FROM #CreditSummary c
    WHERE NOT EXISTS
    (
        SELECT 1
        FROM #Invoices i
        WHERE i.CompanyNo = c.CompanyNo
              and c.Salesman = i.Salesman
    )


END

--return the data    
;
with CTE_KPICustomerRevenueSummary
as (SELECT @ClientNo AS ClientNo,
           co.CompanyName,
           co.CompanyId,
           i.Salesman,
           i.Cost,
           i.Resale,
           i.Resale - i.Cost AS GrossProfit,
           case i.Resale
               WHEN 0 THEN
                   -100
               ELSE
           ((i.Resale - i.Cost) / abs(i.Resale)) * 100
           END AS Margin,
           (
               SELECT count(*)
               FROM #Invoices
               WHERE CompanyNo = i.CompanyNo
                     AND Salesman = i.Salesman
           ) AS NoOfOrders,
           (
               SELECT count(*)
               FROM #Credits
               WHERE CompanyNo = i.CompanyNo
                     AND Salesman = i.Salesman
           ) AS NoOfCredits,
           @invMonth as invMonth,
           @yearNo as yearNo
    FROM #InvoicePostSummary i
        JOIN tbCompany co
            ON co.CompanyId = i.CompanyNo
   )
INSERT INTO tbKPICustomersRevenueSummary
(
    ClientNo,
    CompanyName,
    CompanyId,
    Salesman,
    Cost,
    Resale,
    GrossProfit,
    Margin,
    NoOfOrders,
    NoOfCredits,
    invMonth,
    yearNo
)
SELECT *
from CTE_KPICustomerRevenueSummary nolock

DROP TABLE #Credits
DROP TABLE #CreditSummary
DROP TABLE #Invoices
DROP TABLE #InvoiceSummary
DROP TABLE #InvoicePreSummary
DROP TABLE #InvoicePostSummary









