<%@ Control Language="C#" CodeBehind="BOMCusReqSourcingResults_Confirm.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Forms.BOMCusReqSourcingResults_Confirm" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Form:DesignBase ID="ctlDB" runat="server" ShowRequiredFieldsExplanation="false" AllowQuickHelp="false">
<Explanation><%=Functions.GetGlobalResource("FormExplanations", "BOMItem_PartialRelease")%></Explanation>
	<Content>
		<ReboundUI_Table:Form id="frm" runat="server">		     
				<ReboundUI_Form:FormField id="ctlBuyUnitSellPriceDiff_Label" runat="server" FieldID="lblBuyUnitSellPriceDiff_Label" ResourceTitle="Message" >
				<Field><asp:Label ID="lblBuyUnitSellPriceDiff_Label" runat="server" /></Field>
			    </ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlConfirm" runat="server" FieldID="ctlConfirmation" ResourceTitle="SaveConfirm">
				<Field><ReboundUI:Confirmation ID="ctlConfirmation" runat="server" /></Field>
			</ReboundUI_Form:FormField>
		</ReboundUI_Table:Form>		 
	</Content>
</ReboundUI_Form:DesignBase>
