Type.registerNamespace("Rebound.GlobalTrader.Site.Pages.CrossMatch");Rebound.GlobalTrader.Site.Pages.CrossMatch.CrossMatchPage=function(n){Rebound.GlobalTrader.Site.Pages.CrossMatch.CrossMatchPage.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Pages.CrossMatch.CrossMatchPage.prototype={get_strSaleskUrl:function(){return this._strSaleskUrl},set_strSaleskUrl:function(n){this._strSaleskUrl!==n&&(this._strSaleskUrl=n)},initialize:function(){Rebound.GlobalTrader.Site.Pages.CrossMatch.CrossMatchPage.callBaseMethod(this,"initialize")},goInit:function(){alert("hiiii");Rebound.GlobalTrader.Site.Pages.CrossMatch.CrossMatchPage.callBaseMethod(this,"goInit")},dispose:function(){this.isDisposed||(this._strSaleskUrl=null,Rebound.GlobalTrader.Site.Pages.CrossMatch.CrossMatchPage.callBaseMethod(this,"dispose"))}};Rebound.GlobalTrader.Site.Pages.CrossMatch.CrossMatchPage.registerClass("Rebound.GlobalTrader.Site.Pages.CrossMatch.CrossMatchPage",Rebound.GlobalTrader.Site.Pages.Content,Sys.IDisposable);