Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.CompanyFinanceInfo_Link=function(n){Rebound.GlobalTrader.Site.Controls.Forms.CompanyFinanceInfo_Link.initializeBase(this,[n]);this._intCompanyID=-1};Rebound.GlobalTrader.Site.Controls.Forms.CompanyFinanceInfo_Link.prototype={get_intCompanyID:function(){return this._intCompanyID},set_intCompanyID:function(n){this._intCompanyID!==n&&(this._intCompanyID=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Forms.CompanyFinanceInfo_Link.callBaseMethod(this,"initialize");this.addShown(Function.createDelegate(this,this.formShown))},dispose:function(){this.isDisposed||(this._intCompanyID=null,this._lblCurrency_ActualCreditLimit=null,Rebound.GlobalTrader.Site.Controls.Forms.CompanyFinanceInfo_Link.callBaseMethod(this,"dispose"))},formShown:function(){this._ctlSelectCompanies=$find(this.getField("ctlSelectCompany").ID);this._blnFirstTimeShown&&this.addSave(Function.createDelegate(this,this.saveClicked));var n=this;this.loadLinkedCompanies();$("#btnSearchCompanybyVatNo").click(function(){n.loadLinkedCompanies()})},loadLinkedCompanies:function(){var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/CompanyFinanceInfo");n.set_DataObject("CompanyFinanceInfo");n.set_DataAction("loadLinkedCompanies");n.addParameter("id",this._intCompanyID);n.addParameter("VatNo",$("#ctl00_cphMain_ctlFinanceInfo_ctlDB_ctl14_ctlLink_ctlDB_ctlVatNumber_ctl04_txtVatNumber").val());n.addDataOK(Function.createDelegate(this,this.loadLinkedCompaniesComplete));n.addError(Function.createDelegate(this,this.loadLinkedCompaniesError));n.addTimeout(Function.createDelegate(this,this.loadLinkedCompaniesError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},loadLinkedCompaniesError:function(n){this._strErrorMessage=n._errorMessage;this.onSaveError()},loadLinkedCompaniesComplete:function(n){var i,t,r;if(console.log(n),this.showLoading(!1),this._ctlSelectCompanies.clearData(),i=n._result,i!=null){if(i.Selected!=null)for(r=0;r<i.Selected.length;r++)t=i.Selected[r],this._ctlSelectCompanies.addRow(!0,[$R_FN.setCleanTextValue(t.Name)],t.ID),$("#ctl00_cphMain_ctlFinanceInfo_ctlDB_ctl14_ctlLink_ctlDB_ctlVatNumber_ctl04_txtVatNumber").val(t.VatNo),t=null;if(i.Unselected!=null)for(r=0;r<i.Unselected.length;r++)t=i.Unselected[r],this._ctlSelectCompanies.addRow(!1,[$R_FN.setCleanTextValue(t.Name)],t.ID),t=null;this._ctlSelectCompanies.populateTables();this.showInnerContent(!0)}},saveClicked:function(){if(this.validateForm()){var n=new Rebound.GlobalTrader.Site.Data;return n.set_PathToData("controls/Nuggets/CompanyFinanceInfo"),n.set_DataObject("CompanyFinanceInfo"),n.set_DataAction("SaveLinked"),n.addParameter("id",this._intCompanyID),n.addParameter("VatNo",$("#ctl00_cphMain_ctlFinanceInfo_ctlDB_ctl14_ctlLink_ctlDB_ctlVatNumber_ctl04_txtVatNumber").val()),n.addParameter("Selected",this._ctlSelectCompanies.getValuesAsString(!0)),n.addParameter("Unselected",this._ctlSelectCompanies.getValuesAsString(!1)),n.addDataOK(Function.createDelegate(this,this.saveEditComplete)),n.addError(Function.createDelegate(this,this.saveEditError)),n.addTimeout(Function.createDelegate(this,this.saveEditError)),$R_DQ.addToQueue(n),$R_DQ.processQueue(),n=null,this.showFormWithoutError(!0,""),!1}return this.showError(!0,"Please Enter Vat Number"),!1},saveEditError:function(n){this._strErrorMessage=n._errorMessage;this.onSaveError()},saveEditComplete:function(n){n._result.Result==!0?(this.showSavedOK(!0),this.onSaveComplete()):(this._strErrorMessage=n._errorMessage,this.onSaveError())},validateForm:function(){this.onValidate();return this.autoValidateFields()},cancelClicked:function(){this.onCancel()}};Rebound.GlobalTrader.Site.Controls.Forms.CompanyFinanceInfo_Link.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.CompanyFinanceInfo_Link",Rebound.GlobalTrader.Site.Controls.Forms.Base,Sys.IDisposable);