using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site;
using Rebound.GlobalTrader.Site.Controls;

namespace Rebound.GlobalTrader.Site.Controls.Forms {
    public partial class ClientInvoiceAdd_Add : Base
    {

		#region Locals

		protected IconButton _ibtnSend;
		protected IconButton _ibtnSend_Footer;
       

		#endregion

		#region Properties

		#endregion

		#region Overrides

		/// <summary>
		/// OnInit
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
			base.OnInit(e);
            TitleText = Functions.GetGlobalResource("FormTitles", "ClientInvoiceAdd_Add");
            AddScriptReference("Controls.Nuggets.ClientInvoiceAdd.Add.ClientInvoiceAdd_Add.js");
		}

		/// <summary>
		/// OnPreRender
		/// </summary>
		/// <param name="e"></param>
		protected override void OnPreRender(EventArgs e) {
			WireUpButtons();
			SetupScriptDescriptors();
			base.OnPreRender(e);
		}

		#endregion

		private void WireUpButtons() {
			_ibtnSend = (IconButton)FindIconButton("ibtnSend");
			_ibtnSend_Footer = (IconButton)FindFooterIconButton("ibtnSend");
          
		}

		/// <summary>
		/// Setup Script Descriptors
		/// </summary>
		private void SetupScriptDescriptors() {
            _scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.Forms.ClientInvoiceAdd_Add", ctlDesignBase.ClientID);
			_scScriptControlDescriptor.AddComponentProperty("ctlSelectCompany", ((ItemSearch.Base)FindContentControl("ctlSelectCompany")).ctlDesignBase.ClientID);
            _scScriptControlDescriptor.AddComponentProperty("ctlSelectSIGILines", ((ItemSearch.Base)FindContentControl("ctlSelectSIGILines")).ctlDesignBase.ClientID);
			_scScriptControlDescriptor.AddElementProperty("ibtnSend", _ibtnSend.ClientID);
			if (FooterLinksHolder != null) _scScriptControlDescriptor.AddElementProperty("ibtnSend_Footer", _ibtnSend_Footer.ClientID);
			_scScriptControlDescriptor.AddElementProperty("ibtnContinue", (FindIconButton("ibtnContinue").ClientID));
			if (FooterLinksHolder != null) _scScriptControlDescriptor.AddElementProperty("ibtnContinue_Footer", (FindFooterIconButton("ibtnContinue").ClientID));
			_scScriptControlDescriptor.AddProperty("intCompanyID", _objQSManager.CompanyID);
            _scScriptControlDescriptor.AddProperty("intGoodsInID", _objQSManager.GoodsInID);
			_scScriptControlDescriptor.AddProperty("strCompanyName", _objQSManager.CompanyName);
			_scScriptControlDescriptor.AddProperty("dtFromDate", Functions.FormatDate(Functions.GetUKLocalTime().AddDays(-30)));
			_scScriptControlDescriptor.AddProperty("dtToDate", Functions.FormatDate(Functions.GetUKLocalTime()));
            _scScriptControlDescriptor.AddElementProperty("lblCurrency_InvoiceAmount", FindFieldControl("ctlInvoiceAmount", "lblCurrency_InvoiceAmount").ClientID);
            _scScriptControlDescriptor.AddElementProperty("lblCurrency_GoodsInValue", FindFieldControl("ctlGoodsValue", "lblCurrency_GoodsInValue").ClientID);
            _scScriptControlDescriptor.AddElementProperty("lblCurrency_Tax", FindFieldControl("ctlTax", "lblCurrency_Tax").ClientID);
            _scScriptControlDescriptor.AddElementProperty("lblCurrency_DeliveryCharge", FindFieldControl("ctlDeliveryCharge", "lblCurrency_DeliveryCharge").ClientID);
            _scScriptControlDescriptor.AddElementProperty("lblCurrency_BankFee", FindFieldControl("ctlBankFee", "lblCurrency_BankFee").ClientID);
            _scScriptControlDescriptor.AddElementProperty("lblCurrency_CreditCardFee", FindFieldControl("ctlCreditCardFee", "lblCurrency_CreditCardFee").ClientID);
            _scScriptControlDescriptor.AddProperty("SelectedGI", FindFieldControl("ctlGoodsValue", "txtSelectedGI").ClientID);
		}
       

	}
}
