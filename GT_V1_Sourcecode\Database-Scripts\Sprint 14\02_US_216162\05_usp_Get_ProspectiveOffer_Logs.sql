﻿GO
/*     
===========================================================================================    
TASK          UPDATED BY      DATE          ACTION    DESCRIPTION    
[US-216162]   CuongDox        2-Nov-2024    CREATE    Get Prospective Offer logs By Id proId and CusrId
[US-215434]	  Phuc Hoang	  06-Nov-2024	Update	  Lytica Price should apply fuzzy logic for inserting & displaying
===========================================================================================    
*/  
CREATE OR ALTER PROCEDURE [dbo].[usp_Get_ProspectiveOffer_Logs]  
	@ProspectiveOfferId INT = NULL
AS
BEGIN
	DECLARE @ClientCurrencyNo INT, @CurrencyCode VARCHAR(MAX), @GlobalClientCurrencyNo INT 
	SELECT @ClientCurrencyNo = CurrencyNo FROM tbClient WHERE ClientId = 114  
	SELECT @CurrencyCode = CurrencyCode,  @GlobalClientCurrencyNo = GlobalCurrencyNo FROM tbCurrency WHERE CurrencyId = @ClientCurrencyNo  
	SELECT DISTINCT
	pol.ProspectiveOfferLineId
	,b.BOMCode
	,b.BOMId 
	,cr.ReceivedDate
	,c.CompanyName AS HUBRFQCustomer
	,cr.Part AS PartNo
	,pol.SupplierPart as CustomerPartNo
	,mfr.ManufacturerName AS Manufacturer
	,cr.DateCode
	,pk.PackageName
	,pr.ProductName
	,cr.Price
	,cr.Quantity
	,cr.CustomerRequirementId
	,@CurrencyCode AS Currency
	,ISNULL(bomdes.BOMId,0) AS HUBRFQCreatedId
	,bomdes.BOMName AS HUBRFQCreatedNo
	,ISNULL(  
	   (ihs.AveragePrice / dbo.ufn_get_exchange_rate(cur.CurrencyId, ihs.DLUP)) * dbo.ufn_get_exchange_rate(@ClientCurrencyNo, GETDATE()),  
	   ISNULL(dbo.ufn_extract_IHS_AvgPrice(ihs.Descriptions), 0)  
	  ) AS IHSAvgPrice
	,ISNULL(lytica.AveragePrice, 0) AS LyticaAvgPrice
	,l.EmployeeName AS SalesPerson
	FROM tbProspectiveOfferLogs polg
	INNER JOIN tbProspectiveOfferLines pol ON polg.ProspectiveOfferLineNo = pol.ProspectiveOfferLineId
	INNER JOIN tbCustomerRequirement cr ON polg.CustomerRequirementNo = cr.CustomerRequirementId
	INNER JOIN tbBOM b ON cr.BOMNo = b.BOMId
	JOIN tbCompany c ON b.CompanyNo = c.CompanyId  
	JOIN tbLogin l ON l.LoginId = c.Salesman 
	LEFT JOIN tbManufacturer mfr ON mfr.ManufacturerId = cr.ManufacturerNo  
	LEFT JOIN tbIHSparts ihs ON ihs.Part = pol.Part AND ihs.ManufacturerFullName = mfr.ManufacturerName  
	--LEFT JOIN tbLyticaAPI lytica ON dbo.ufn_get_fullpart(lytica.OriginalPartSearched) = pol.Part AND lytica.Manufacturer = mfr.ManufacturerName  
	LEFT JOIN tbCurrency cur ON cur.CurrencyCode = ihs.ColPriceCurrency 
	LEFT JOIN tbPackage pk ON cr.PackageNo = pk.PackageId
	LEFT JOIN tbProduct pr ON cr.ProductNo = pr.ProductId
	OUTER APPLY (
		SELECT  TOP 1 *
			FROM tbLyticaAPI
			WHERE OriginalPartSearched = pol.Part 
			AND (Manufacturer = mfr.ManufacturerName OR Manufacturer LIKE mfr.ManufacturerName + '%' OR Manufacturer LIKE [dbo].[ufn_GetFirstWord](mfr.ManufacturerName) + '%')
	) lytica
	OUTER APPLY (
		SELECT top 1 * from tbBOM bs
		WHERE 
		bs.CustomerRequirementSourceId =  cr.CustomerRequirementId

	) bomdes
	WHERE
		pol.ProspectiveOfferLineId = @ProspectiveOfferId
		
END

GO