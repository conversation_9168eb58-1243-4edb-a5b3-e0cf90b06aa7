/*
Marker      Date        Changed By       Remarks
[001]      <PERSON><PERSON>   21-Jan-2019   Add View Tree Button.
 */
using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.BLL;
using Rebound.GlobalTrader.Site.Controls;

namespace Rebound.GlobalTrader.Site.Controls.Nuggets {
	public partial class BOMMainInfo : Base {

		#region Locals
		protected IconButton _ibtnEdit;
        protected IconButton _ibtnExportCSV;
        protected IconButton _ibtnExportPurchaseHUB;
		protected IconButton _ibtnDelete;
        protected IconButton _ibtnNotify;
        protected IconButton _ibtnRelease;
        protected IconButton _ibtnClose;
        protected IconButton _ibtnNoBid;
        protected IconButton _ibtnNote;
        protected IconButton _ibtnViewTree;//[001]
        protected IconButton _ibtnCrossMatch;
        #endregion

        #region Properties

        private int _intBOMID = -1;
		public int BOMID {
			get { return _intBOMID; }
			set { _intBOMID = value; }
		}

		private bool _blnCanEdit = true;
		public bool CanEdit {
			get { return _blnCanEdit; }
			set { _blnCanEdit = value; }
		}

		private bool _blnCanDelete = true;
		public bool CanDelete {
			get { return _blnCanDelete; }
			set { _blnCanDelete = value; }
		}

        private bool _blnCanNotify = true;
        public bool CanNotify
        {
            get { return _blnCanNotify; }
            set { _blnCanNotify = value; }
        }

        private bool _blnCanRelease = true;
        public bool CanRelease
        {
            get { return _blnCanRelease; }
            set { _blnCanRelease = value; }
        }

        private bool _blnCanClosed = true;
        public bool CanClosed
        {
            get { return _blnCanClosed; }
            set { _blnCanClosed = value; }
        }
        private bool _blnCanNobid = true;
        public bool CanNoBid
        {
            get { return _blnCanNobid; }
            set { _blnCanNobid = value; }
        }
        private bool _blnCanNote = true;
        public bool CanNote
        {
            get { return _blnCanNote; }
            set { _blnCanNote = value; }
        }
        private bool _IsGSAEditPermission = true;
        public bool IsGSAEditPermission
        {
            get { return _IsGSAEditPermission; }
            set { _IsGSAEditPermission = value; }
        }
        private bool _IsDiffrentClient = false;
        public bool IsDiffrentClient
        {
            get { return _IsDiffrentClient; }
            set { _IsDiffrentClient = value; }
        }
        private bool _IsGSA = false;
        public bool IsGSA
        {
            get { return _IsGSA; }
            set { _IsGSA = value; }
        }
        #endregion

        #region Overrides

        /// <summary>
        /// Oninit
        /// </summary>
        /// <param name="e"></param>
        protected override void OnInit(EventArgs e) {
			base.OnInit(e);
			WireUpControls();
			AddScriptReference("Controls.Nuggets.BOMMainInfo.BOMMainInfo.js");
			TitleText = Functions.GetGlobalResource("Nuggets", "BOMMainInfo");
			if (_objQSManager.BOMID > 0) _intBOMID = _objQSManager.BOMID;
		}

		protected override void OnLoad(EventArgs e) {
			SetupScriptDescriptors();
           
            base.OnLoad(e);
		}

        protected override void OnPreRender(EventArgs e)
        {
            _ibtnEdit.Visible = _blnCanEdit;
            _ibtnDelete.Visible = _blnCanDelete;
            _ibtnExportPurchaseHUB.Visible = !Convert.ToBoolean(SessionManager.IsPOHub);
            _ibtnRelease.Visible = Convert.ToBoolean(SessionManager.IsPOHub);
            _ibtnClose.Visible = !Convert.ToBoolean(SessionManager.IsPOHub);// false;
            _ibtnNoBid.Visible = Convert.ToBoolean(SessionManager.IsPOHub);
            _ibtnCrossMatch.Visible =  Convert.ToBoolean(SessionManager.IsPOHub);
            if(SessionManager.ClientID!=114)
            {
                if (_IsDiffrentClient == true)
                {
                    if (SessionManager.IsGSA == true)
                    {
                        if (_IsGSAEditPermission == true)
                        {
                            _ibtnEdit.Visible = true;
                            _ibtnDelete.Visible = true;
                            _ibtnExportPurchaseHUB.Visible = !Convert.ToBoolean(SessionManager.IsPOHub);
                            _ibtnRelease.Visible = Convert.ToBoolean(SessionManager.IsPOHub);
                            _ibtnClose.Visible = !Convert.ToBoolean(SessionManager.IsPOHub);// false;
                            _ibtnNoBid.Visible = Convert.ToBoolean(SessionManager.IsPOHub);
                            _ibtnCrossMatch.Visible = Convert.ToBoolean(SessionManager.IsPOHub);
                            _ibtnExportCSV.Visible = true;
                            _ibtnNotify.Visible = true;
                            _ibtnNote.Visible = true;
                        }
                        else
                        {
                            _ibtnEdit.Visible = false;
                            _ibtnDelete.Visible = false;
                            _ibtnExportPurchaseHUB.Visible = false;
                            _ibtnRelease.Visible = false;
                            _ibtnClose.Visible = false;
                            _ibtnNoBid.Visible = false;
                            _ibtnCrossMatch.Visible = false;
                            _ibtnExportCSV.Visible = false;
                            _ibtnNotify.Visible = false;
                            _ibtnNote.Visible = false;

                        }
                    }
                }
            }

            // _ibtnRelease.Enabled = false;
            //_ibtnRelease.Visible = _blnCanRelease;
            base.OnPreRender(e);
        }

		#endregion

		/// <summary>
		/// Sets up AJAX script descriptors
		/// </summary>
		private void SetupScriptDescriptors() {
            _IsGSA = (bool)SessionManager.IsGSA;
            _scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.Nuggets.BOMMainInfo", ctlDesignBase.ClientID);
			if (_blnCanEdit) _scScriptControlDescriptor.AddElementProperty("ibtnEdit", _ibtnEdit.ClientID);
			if (_blnCanDelete) _scScriptControlDescriptor.AddElementProperty("ibtnDelete", _ibtnDelete.ClientID);
			_scScriptControlDescriptor.AddProperty("intBOMID", _intBOMID);
            _scScriptControlDescriptor.AddElementProperty("ibtnExportCSV", _ibtnExportCSV.ClientID);
            if (_ibtnExportPurchaseHUB.Visible) _scScriptControlDescriptor.AddElementProperty("ibtnExportPurchaseHUB", _ibtnExportPurchaseHUB.ClientID);
            if (_ibtnRelease.Visible) _scScriptControlDescriptor.AddElementProperty("ibtnRelease", _ibtnRelease.ClientID);
            //if (_blnCanNotify)
            _scScriptControlDescriptor.AddElementProperty("ibtnNotify", _ibtnNotify.ClientID);
            _scScriptControlDescriptor.AddProperty("blnPOHub", Convert.ToBoolean(SessionManager.IsPOHub));
            if (_blnCanClosed) _scScriptControlDescriptor.AddElementProperty("ibtnClose", _ibtnClose.ClientID);

            if (_blnCanNobid) _scScriptControlDescriptor.AddElementProperty("ibtnNoBid", _ibtnNoBid.ClientID);
            //if (_blnCanNote) _scScriptControlDescriptor.AddElementProperty("ibtnNote", _ibtnNote.ClientID);
             _scScriptControlDescriptor.AddElementProperty("ibtnNote", _ibtnNote.ClientID);
          
           // if (_blnCanRelease) _scScriptControlDescriptor.AddElementProperty("ibtnRelease", _ibtnRelease.ClientID);
             _scScriptControlDescriptor.AddElementProperty("ibtnViewTree", _ibtnViewTree.ClientID);//[001]
            _scScriptControlDescriptor.AddElementProperty("ibtnCrossMatch", _ibtnCrossMatch.ClientID);//[001]

            _scScriptControlDescriptor.AddProperty("IsDiffrentClient", _IsDiffrentClient);
            _scScriptControlDescriptor.AddProperty("IsGSAEditPermission", _IsGSAEditPermission);
            _scScriptControlDescriptor.AddProperty("IsGSA", _IsGSA);
            _scScriptControlDescriptor.AddProperty("ClientId", SessionManager.ClientID);
        }

		/// <summary>
		/// Wire up controls from the ascx
		/// </summary>
		private void WireUpControls() {
			_ibtnEdit = FindIconButton("ibtnEdit");
			_ibtnDelete = FindIconButton("ibtnDelete");
            _ibtnExportCSV = (IconButton)FindIconButton("ibtnExportCSV");
            _ibtnExportPurchaseHUB = (IconButton)FindIconButton("ibtnExportPurchaseHUB");
            _ibtnRelease = (IconButton)FindIconButton("ibtnRelease");
            _ibtnNotify = (IconButton)FindIconButton("ibtnNotify");
            _ibtnClose = (IconButton)FindIconButton("ibtnClose");
            _ibtnNoBid = FindIconButton("ibtnNoBid");
            _ibtnNote = FindIconButton("ibtnNote");
           // _ibtnRelease = (IconButton)FindIconButton("ibtnRelease");
            _ibtnViewTree = FindIconButton("ibtnViewTree");//[001]
            _ibtnCrossMatch = FindIconButton("ibtnCrossMatch");

           
            

        }

    }
}
