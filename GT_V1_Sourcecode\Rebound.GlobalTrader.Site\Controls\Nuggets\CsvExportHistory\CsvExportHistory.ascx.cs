using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;
using System.Text;
using Rebound.GlobalTrader.BLL;
using Rebound.GlobalTrader.Site.Controls;
using Rebound.GlobalTrader.Site;

namespace Rebound.GlobalTrader.Site.Controls.Nuggets
{
    public partial class CsvExportHistory : Base
    {

        #region Locals


        protected FlexiDataTable _tblCreditHistory;

        #endregion

        #region Properties

        private int _intBOMID = -1;
        public int BOMID
        {
            get { return _intBOMID; }
            set { _intBOMID = value; }
        }
        private int _intPOQuoteID = -1;
        public int POQuoteID
        {
            get { return _intPOQuoteID; }
            set { _intPOQuoteID = value; }
        }

        #endregion

        #region Overrides

        /// <summary>
        /// Oninit
        /// </summary>
        /// <param name="e"></param>
        protected override void OnInit(EventArgs e)
        {
            base.OnInit(e);
            WireUpControls();
            AddScriptReference("Controls.Nuggets.CsvExportHistory.CsvExportHistory.js");
            TitleText = Functions.GetGlobalResource("Nuggets", "CsvExportHistory");
            if (_objQSManager.POQuoteID > 0) _intPOQuoteID = _objQSManager.POQuoteID;
            if (_objQSManager.BOMID > 0) _intBOMID = _objQSManager.BOMID;
            SetupTable();
        }

        protected override void OnLoad(EventArgs e)
        {
            SetupScriptDescriptors();
            base.OnLoad(e);
        }

        protected override void OnPreRender(EventArgs e)
        {
            base.OnPreRender(e);
        }

        #endregion

        private void SetupTable()
        {

            _tblCreditHistory.AllowSelection = false;
            _tblCreditHistory.Columns.Add(new FlexiDataColumn("Message"));
            _tblCreditHistory.Columns.Add(new FlexiDataColumn("Date", WidthManager.GetWidth(WidthManager.ColumnWidth.DateAndTime)));
          
        }

        /// <summary>
        /// Sets up AJAX script descriptors
        /// </summary>
        private void SetupScriptDescriptors()
        {
            _scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.Nuggets.CsvExportHistory", ctlDesignBase.ClientID);

            _scScriptControlDescriptor.AddProperty("intBOMID", _intBOMID);
            _scScriptControlDescriptor.AddProperty("intPOQuoteID", _intPOQuoteID);
            _scScriptControlDescriptor.AddComponentProperty("tblCreditHistory", _tblCreditHistory.ClientID);
        }

        /// <summary>
        /// Wire up controls from the ascx
        /// </summary>
        private void WireUpControls()
        {

            _tblCreditHistory = (FlexiDataTable)ctlDesignBase.FindContentControl("tblCreditHistory");
        }

    }
}
