Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.ItemSearch");Rebound.GlobalTrader.Site.Controls.ItemSearch.IpoStock=function(n){Rebound.GlobalTrader.Site.Controls.ItemSearch.IpoStock.initializeBase(this,[n]);this._blnForRMAs=!1;this._blnIncludeQuarantined=!1;this._blnIncludeLotsOnHold=!1;this._IncludeLockLotCustNo="";this._blnLineIPO=!1;this._blnParAllocated=!1};Rebound.GlobalTrader.Site.Controls.ItemSearch.IpoStock.prototype={get_blnForRMAs:function(){return this._blnForRMAs},set_blnForRMAs:function(n){this._blnForRMAs!==n&&(this._blnForRMAs=n)},get_blnIncludeQuarantined:function(){return this._blnIncludeQuarantined},set_blnIncludeQuarantined:function(n){this._blnIncludeQuarantined!==n&&(this._blnIncludeQuarantined=n)},get_blnIncludeLotsOnHold:function(){return this._blnIncludeLotsOnHold},set_blnIncludeLotsOnHold:function(n){this._blnIncludeLotsOnHold!==n&&(this._blnIncludeLotsOnHold=n)},get_intSupplierRMANo:function(){return this._intSupplierRMANo},set_intSupplierRMANo:function(n){this._intSupplierRMANo!==n&&(this._intSupplierRMANo=n)},get_intSalesOrderID:function(){return this._intSalesOrderID},set_intSalesOrderID:function(n){this._intSalesOrderID!==n&&(this._intSalesOrderID=n)},get_blnLineIPO:function(){return this._blnLineIPO},set_blnLineIPO:function(n){this._blnLineIPO!==n&&(this._blnLineIPO=n)},get_blnParAllocated:function(){return this._blnParAllocated},set_blnParAllocated:function(n){this._blnParAllocated!==n&&(this._blnParAllocated=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.ItemSearch.IpoStock.callBaseMethod(this,"initialize");this.addSetupData(Function.createDelegate(this,this.doSetupData));this.addGetDataComplete(Function.createDelegate(this,this.doGetDataComplete));$R_FN.showElement(this.getField("ctlPurchaseOrderNo")._element,!this._blnForRMAs);$R_FN.showElement(this.getField("ctlCRMANo")._element,this._blnForRMAs)},dispose:function(){this.isDisposed||(this._blnForRMAs=null,this._blnIncludeQuarantined=null,this._blnIncludeLotsOnHold=null,this._intSupplierRMANo=null,this._IncludeLockLotCustNo=null,this._blnLineIPO=null,this._blnParAllocated=null,Rebound.GlobalTrader.Site.Controls.ItemSearch.IpoStock.callBaseMethod(this,"dispose"))},doSetupData:function(){this._objData.set_PathToData("controls/ItemSearch/IpoStock");this._objData.set_DataObject("IpoStock");this._objData.set_DataAction("GetData");this._objData.addParameter("ForRMAs",this._blnForRMAs);this._objData.addParameter("SupplierRMANo",this._intSupplierRMANo);this._objData.addParameter("IncludeQuarantined",this._blnIncludeQuarantined);this._objData.addParameter("IncludeLotsOnHold",this._blnIncludeLotsOnHold);this._objData.addParameter("PartNo",this.getFieldValue("ctlPartNo"));this._objData.addParameter("WarehouseNo",this.getFieldValue("ctlWarehouse"));this._objData.addParameter("Location",this.getFieldValue("ctlLocation"));this._objData.addParameter("PONoLo",this.getFieldValue_Min("ctlPurchaseOrderNo"));this._objData.addParameter("PONoHi",this.getFieldValue_Max("ctlPurchaseOrderNo"));this._objData.addParameter("CRMANoLo",this.getFieldValue_Min("ctlCRMANo"));this._objData.addParameter("CRMANoHi",this.getFieldValue_Max("ctlCRMANo"));this._objData.addParameter("IncLockCust",this._IncludeLockLotCustNo);this._objData.addParameter("SalesOrderNo",this._intSalesOrderID);this._objData.addParameter("StopNonIPOStock",this._blnLineIPO&&this._blnParAllocated);this._objData.addParameter("GlobalLoginClientNo",this._intGlobalClientNo)},doGetDataComplete:function(){for(var n,i,t=0,r=this._objResult.Results.length;t<r;t++)n=this._objResult.Results[t],i=[$R_FN.writeDoubleCellValue($R_FN.writePartNo(n.Part,n.ROHS),$R_FN.setCleanTextValue(n.SupplierPart)),$R_FN.writeDoubleCellValue(n.QtyStock,n.QtyOrder),$R_FN.writeDoubleCellValue(n.QtyAllocated,n.QtyAvailable),$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(n.Warehouse),$R_FN.setCleanTextValue(n.Location)),$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(n.Supplier),$R_FN.setCleanTextValue(n.Landed)),this._blnForRMAs?$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(n.CRMA),$R_FN.setCleanTextValue(n.CRMADate)):$R_FN.writeDoubleCellValue($R_FN.showSerialNumber($R_FN.setCleanTextValue(n.PO),n.LineNo),$R_FN.setCleanTextValue(n.PODelivDate))],this._blnIncludeQuarantined&&Array.add(i,$R_FN.setCleanTextValue(n.Unavailable)),this._tblResults.addRow(i,n.ID,!1),i=null,n=null;this._tblResults.resizeColumns()}};Rebound.GlobalTrader.Site.Controls.ItemSearch.IpoStock.registerClass("Rebound.GlobalTrader.Site.Controls.ItemSearch.IpoStock",Rebound.GlobalTrader.Site.Controls.ItemSearch.Base,Sys.IDisposable);