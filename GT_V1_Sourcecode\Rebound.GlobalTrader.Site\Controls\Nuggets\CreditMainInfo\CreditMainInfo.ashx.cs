//-----------------------------------------------------------------------------------------
// RP 08.12.2009:
// - allow seeing zero second salesperson percentage
//-----------------------------------------------------------------------------------------

//Marker     Changed by      Date         Remarks
//[001]      Shashi Keshar           06/10/2016   This hass been changed to show 100% in salesman when credit note generated for hub from client side  .

using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;
using System.Linq;

namespace Rebound.GlobalTrader.Site.Controls.Nuggets.Data {
	[WebService(Namespace = "http://tempuri.org/")]
	[WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
	public class CreditMainInfo : Rebound.GlobalTrader.Site.Data.Base {
		public override void ProcessRequest(HttpContext context) {
			if (base.init(context)) {
				switch (Action) {
					case "GetData": GetData(); break;
					case "SaveEdit": SaveEdit(); break;
                    case "ExportRelease": ExportRelease(); break;
					case "SaveCreditEmail": SaveCreditEmail(); break;
					case "SaveCreditPrint": SaveCreditPrint(); break;
					default: WriteErrorActionNotFound(); break;
				}
			}
		}
		/// <summary>
		/// get specific credit by key
		/// </summary>
		public JsonObject GetData(Credit credit) {
			JsonObject jsn = null;
			if (credit != null) {
				jsn = new JsonObject();
				jsn.AddVariable("CreditNumber", credit.CreditNumber);
				jsn.AddVariable("CustomerNo", credit.CompanyNo);
				jsn.AddVariable("CustomerName", credit.CompanyName);
				string companyNotes = Company.GetAdvisoryNotes(credit.CompanyNo);
				jsn.AddVariable("CustomerAdvisoryNotes", Functions.ReplaceLineBreaks(companyNotes));
				jsn.AddVariable("Contact", credit.ContactName);
				jsn.AddVariable("ContactNo", credit.ContactNo);
				jsn.AddVariable("SalesmanNo", credit.Salesman);
				jsn.AddVariable("Salesman2No", credit.Salesman2);
				jsn.AddVariable("Salesman2Percent", credit.Salesman2Percent);
				if (credit.Salesman2 > 0) {
					jsn.AddVariable("Salesman2", string.Format("{0} ({1}%)", credit.Salesman2Name, credit.Salesman2Percent));
					jsn.AddVariable("Salesman", string.Format("{0} ({1}%)", credit.SalesmanName, (100 - credit.Salesman2Percent)));
				} else {
					jsn.AddVariable("Salesman2", credit.Salesman2Name);
                    //[001] start Here
					jsn.AddVariable("Salesman", credit.isClientInvoice==true? string.Format("{0} ({1}%)", credit.SalesmanName, (100 - credit.Salesman2Percent)) :credit.SalesmanName);
                    //[001] End Here 
				}
				jsn.AddVariable("Raiser", credit.RaiserName);
				jsn.AddVariable("RaisedBy", credit.RaisedBy);
				jsn.AddVariable("Division", credit.DivisionName);
				jsn.AddVariable("DivisionNo", credit.DivisionNo);
                jsn.AddVariable("Invoice", credit.isClientInvoice == true ? credit.ClientInvoiceNumber : credit.InvoiceNumber);
				jsn.AddVariable("InvoiceNo",credit.isClientInvoice ==true?credit.ClientInvoiceNo :credit.InvoiceNo);
				jsn.AddVariable("CustomerRMA", credit.CustomerRMANumber);
				jsn.AddVariable("CustomerRMANo", credit.CustomerRMANo);
				jsn.AddVariable("SalesOrder", credit.SalesOrderNumber);
				jsn.AddVariable("SalesOrderNo", credit.SalesOrderNo);
				jsn.AddVariable("ShippingAccountNo", credit.Account);
				jsn.AddVariable("ShipViaNo", credit.ShipViaNo);
				jsn.AddVariable("ShipVia", credit.ShipViaName);
				jsn.AddVariable("Incoterm", credit.IncotermName);
				jsn.AddVariable("IncotermNo", credit.IncotermNo);
				jsn.AddVariable("CreditDate", Functions.FormatDate(credit.CreditDate));
				jsn.AddVariable("ReferenceDate", Functions.FormatDate(credit.ReferenceDate));
				jsn.AddVariable("Currency", Functions.FormatCurrencyDescription(credit.CurrencyDescription, credit.CurrencyCode));
				jsn.AddVariable("CurrencyNo", credit.CurrencyNo);
				jsn.AddVariable("CurrencyCode", credit.CurrencyCode);
				jsn.AddVariable("DLUP", Functions.FormatDLUP(credit.DLUP, credit.UpdatedBy));
				jsn.AddVariable("CustomerPO", credit.CustomerPO);
				jsn.AddVariable("CustomerDebit", credit.CustomerDebit);
				jsn.AddVariable("CustomerReturn", credit.CustomerRMA);

				//convert freight to base currency if required
				string strFreight = Functions.FormatCurrency(credit.Freight, credit.CurrencyCode, 2,true);
				string strShippingCost = Functions.FormatCurrency(credit.ShippingCost, SessionManager.ClientCurrencyCode, 2,true);
				if (credit.CurrencyNo != SessionManager.ClientCurrencyID) {
					credit.Freight = (credit.Freight == null) ? 0 : (double)credit.Freight;
					strFreight = string.Format("{0} ({1})", strFreight, Functions.FormatCurrency(BLL.Currency.ConvertValueToBaseCurrency(credit.Freight, credit.CurrencyNo, credit.ReferenceDate), SessionManager.ClientCurrencyCode, 2,true));
					strShippingCost = string.Format("{0} ({1})", strShippingCost, Functions.FormatCurrency(BLL.Currency.ConvertValueFromBaseCurrency(credit.ShippingCost, credit.CurrencyNo, credit.ReferenceDate), credit.CurrencyCode, 2,true));
				}
				jsn.AddVariable("Freight", strFreight);
				jsn.AddVariable("FreightVal", Functions.FormatCurrency(credit.Freight,null, 2,true));

				jsn.AddVariable("ShippingCost", strShippingCost);
				jsn.AddVariable("ShippingCostVal", Functions.FormatCurrency(credit.ShippingCost,null, 2,true));
				jsn.AddVariable("Tax", credit.TaxName);
				jsn.AddVariable("TaxNo", credit.TaxNo);
				jsn.AddVariable("Notes", Functions.ReplaceLineBreaks(credit.Notes));
				jsn.AddVariable("Instructions", Functions.ReplaceLineBreaks(credit.Instructions));
                jsn.AddVariable("CreditNoteBankFee", Functions.FormatCurrency(credit.CreditNoteBankFee,null, 2,true));
                jsn.AddVariable("IsPoHub", SessionManager.IsPOHub == true && credit.ClientCreditNo>0 ? false : true);
                jsn.AddVariable("RefNumber", Functions.FormatNumeric(credit.RefNumber));
              //  jsn.AddVariable("IsClientCredit", SessionManager.IsPOHub.Value && sdf);
                jsn.AddVariable("isClientInvoice", credit.isClientInvoice);
                jsn.AddVariable("RefClientName", Functions.ReplaceLineBreaks(credit.RefClientName));
                jsn.AddVariable("RefClientNo", credit.RefClientNo);
                jsn.AddVariable("ClientInvoiceLineNo", credit.ClientInvoiceLineNo);
                jsn.AddVariable("HubLogin", SessionManager.IsPOHub.Value);
                //jsn.AddVariable("IsHubCreditEditable", (SessionManager.IsPOHub.Value && ));
                jsn.AddVariable("isExport", credit.isExport);
                jsn.AddVariable("ExchangeRate", credit.ExchangeRate);
				jsn.AddVariable("invSalesDivisionNo", credit.invSalesDivisionNo);
				jsn.AddVariable("invSalesDivisionName", credit.invSalesDivisionName);
				jsn.AddVariable("InvDivisionHeaderNo", credit.InvDivisionHeaderNo);
				jsn.AddVariable("invDivisionHeaderName", credit.invDivisionHeaderName);
			}
			credit = null;
			return jsn;
		}
		public void GetData() {
			Credit credit = Credit.Get(ID);
			OutputResult(GetData(credit));
			credit = null;
		}

		/// <summary>
		/// Update an existing credit
		/// </summary>
		public void SaveEdit() {
			try {
				bool blnResult = Credit.Update(
					ID
					, GetFormValue_String("CustomerPO")
					, GetFormValue_String("CustomerReturn")
					, GetFormValue_String("CustomerDebit")
					, GetFormValue_String("Notes")
					, GetFormValue_String("Instructions")
					, GetFormValue_Int("DivisionNo")
					, GetFormValue_Int("SalesmanNo")
					, GetFormValue_Int("RaisedBy")
					, GetFormValue_DateTime("CreditDate")
					, GetFormValue_DateTime("ReferenceDate")
					, GetFormValue_Int("TaxNo")
					, GetFormValue_Int("CurrencyNo")
					, GetFormValue_Int("ShipViaNo")
					, GetFormValue_String("ShippingAccount")
					, GetFormValue_Double("ShippingCost")
					, GetFormValue_Double("Freight")
					, GetFormValue_NullableInt("Salesman2No")
					, GetFormValue_Double("Salesman2Percent")
					, GetFormValue_NullableInt("Incoterm")
                    , GetFormValue_Double("CreditNoteBankFee")
					, LoginID
                    , GetFormValue_NullableDouble("ExchangeRate", null)
					, GetFormValue_Int("DivisionHeader")
				);
				JsonObject jsn = new JsonObject();
				jsn.AddVariable("Result", blnResult);
				OutputResult(jsn);
				jsn.Dispose();
				jsn = null;
			} catch (Exception e) {
				WriteError(e);
			}
		}

        /// <summary>
        /// Export or Release an existing Credit Note
        /// </summary>
        public void ExportRelease()
        {
            try
            {
                bool blnResult = Credit.UpdateExport(
                    ID,
                    LoginID,
                    GetFormValue_Boolean("Exported")
                );
                JsonObject jsn = new JsonObject();
                jsn.AddVariable("Result", blnResult);
                OutputResult(jsn);
                jsn.Dispose();
                jsn = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }

		private void SaveCreditEmail()
		{
			try
			{
				JsonObject jsn = new JsonObject();
				int CreditsCount = GetFormValue_String("Credits").Split(',').Count();
				bool isXmlFormat = GetFormValue_Boolean("IsXmlFormat");
				List<string> lstResult = BLL.Credit.InsertIntoCreditEmail(GetFormValue_String("Credits"), isXmlFormat, SessionManager.LoginID);
				if ((CreditsCount == lstResult.Count) && lstResult.Count > 0)
				{
					jsn.AddVariable("Result", 1);
					jsn.AddVariable("Credits", lstResult.Aggregate((a, b) => (a + "," + b)));
				}
				else if ((CreditsCount != lstResult.Count) && lstResult.Count != 0)
				{
					jsn.AddVariable("Result", 2);
					jsn.AddVariable("Credits", lstResult.Aggregate((a, b) => (a + "," + b)));
				}
				else
				{
					jsn.AddVariable("Result", 3);
				}
				OutputResult(jsn);
				jsn.Dispose(); jsn = null;
			}
			catch (Exception e)
			{
				WriteError(e);
			}
		}

		private void SaveCreditPrint()
		{
			try
			{
				JsonObject jsn = new JsonObject();
				//int CreditsCount = GetFormValue_String("Credits").Split(',').Count();
				int? intResult = BLL.Credit.InsertIntoCreditPrint(GetFormValue_String("CreditsPrintId"), SessionManager.LoginID);
				jsn.AddVariable("Result", Convert.ToString(intResult));
				jsn.AddVariable("Url", Convert.ToString(System.Configuration.ConfigurationManager.AppSettings["redirecturi"]));
				OutputResult(jsn);
				jsn.Dispose(); jsn = null;
			}
			catch (Exception e)
			{
				WriteError(e);
			}
		}
	}
}
