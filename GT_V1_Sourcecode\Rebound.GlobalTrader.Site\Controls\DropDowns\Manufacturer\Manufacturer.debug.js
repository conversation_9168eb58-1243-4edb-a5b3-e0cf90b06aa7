///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//
// RP 06.10.2009:
// - Changes due to changes in base class
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");

Rebound.GlobalTrader.Site.Controls.DropDowns.Manufacturer = function(element) { 
	Rebound.GlobalTrader.Site.Controls.DropDowns.Manufacturer.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.DropDowns.Manufacturer.prototype = {

	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.DropDowns.Manufacturer.callBaseMethod(this, "initialize");
		this.addSetupDataCall(Function.createDelegate(this, this.setupDataCall));
		this.addGetDataOK(Function.createDelegate(this, this.dataCallOK));
	},
	
	dispose: function() { 
		if (this.isDisposed) return;
		Rebound.GlobalTrader.Site.Controls.DropDowns.Manufacturer.callBaseMethod(this, "dispose");
	},
	
	setupDataCall: function() { 		
		this._objData.set_PathToData("controls/DropDowns/Manufacturer");
		this._objData.set_DataObject("Manufacturer");
		this._objData.set_DataAction("GetData");
	},
	
	dataCallOK: function() { 
		var result = this._objData._result;
		if (result.Manufacturers) {
			for (var i = 0; i < result.Manufacturers.length; i++) {
				this.addOption(result.Manufacturers[i].Name, result.Manufacturers[i].ID);
			}
		}
	}
	
};

Rebound.GlobalTrader.Site.Controls.DropDowns.Manufacturer.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.Manufacturer", Rebound.GlobalTrader.Site.Controls.DropDowns.Base);
