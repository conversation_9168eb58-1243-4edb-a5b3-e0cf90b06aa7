﻿using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;

namespace Rebound.GlobalTrader.Site.Controls.DropDowns {
	public partial class GroupCodeForCompany : Base {

		public int CompanyID { get; set; }

		protected override void OnLoad(EventArgs e) {
            SetDropDownType("GroupCodeForCompany");
            AddScriptReference("Controls.DropDowns.GroupCodeForCompany.GroupCodeForCompany");
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.DropDowns.GroupCodeForCompany", ClientID);
			_scScriptControlDescriptor.AddProperty("intCompanyID", CompanyID);
			base.OnLoad(e);
		}

	}
}