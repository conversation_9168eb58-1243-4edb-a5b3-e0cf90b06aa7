Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.ItemSearch");Rebound.GlobalTrader.Site.Controls.ItemSearch.ClientInvoices=function(n){Rebound.GlobalTrader.Site.Controls.ItemSearch.ClientInvoices.initializeBase(this,[n]);this._ShipExported=null};Rebound.GlobalTrader.Site.Controls.ItemSearch.ClientInvoices.prototype={initialize:function(){Rebound.GlobalTrader.Site.Controls.ItemSearch.ClientInvoices.callBaseMethod(this,"initialize");this.addSetupData(Function.createDelegate(this,this.doSetupData));this.addGetDataComplete(Function.createDelegate(this,this.doGetDataComplete))},dispose:function(){this.isDisposed||(this._ShipExported=null,Rebound.GlobalTrader.Site.Controls.ItemSearch.ClientInvoices.callBaseMethod(this,"dispose"))},doSetupData:function(){this._objData.set_PathToData("controls/ItemSearch/ClientInvoices");this._objData.set_DataObject("ClientInvoices");this._objData.set_DataAction("GetData");this._objData.addParameter("GoodsInNoLo",this.getFieldValue_Min("ctlGoodsIn"));this._objData.addParameter("GoodsInNoHi",this.getFieldValue_Max("ctlGoodsIn"));this._objData.addParameter("ClientID",this.getFieldValue("ctlClientName"));this._objData.addParameter("InvoiceNoLo",this.getFieldValue_Min("ctlClientInvoiceNo"));this._objData.addParameter("InvoiceNoHi",this.getFieldValue_Max("ctlClientInvoiceNo"));this._objData.addParameter("DateInvoicedFrom",this.getFieldValue("ctlDateInvoicedFrom"));this._objData.addParameter("DateInvoicedTo",this.getFieldValue("ctlDateInvoicedTo"));this._objData.addParameter("InvoiceExported",this._ShipExported);this._objData.addParameter("ClientDebitNoLo",this.getFieldValue_Min("ctlClientDebitNo"));this._objData.addParameter("ClientDebitNoHi",this.getFieldValue_Max("ctlClientDebitNo"))},doGetDataComplete:function(){for(var n,i,t=0,r=this._objResult.Results.length;t<r;t++)n=this._objResult.Results[t],i=[n.No,$R_FN.setCleanTextValue(n.CMName),$R_FN.setCleanTextValue(n.SalesmanName),$R_FN.setCleanTextValue(n.Narrative),$R_FN.setCleanTextValue(n.SecondRef),$R_FN.setCleanTextValue(n.Date),],this._tblResults.addRow(i,n.ID,!1,{ClientInvLineNo:n.InvLineNo}),i=null,n=null}};Rebound.GlobalTrader.Site.Controls.ItemSearch.ClientInvoices.registerClass("Rebound.GlobalTrader.Site.Controls.ItemSearch.ClientInvoices",Rebound.GlobalTrader.Site.Controls.ItemSearch.Base,Sys.IDisposable);