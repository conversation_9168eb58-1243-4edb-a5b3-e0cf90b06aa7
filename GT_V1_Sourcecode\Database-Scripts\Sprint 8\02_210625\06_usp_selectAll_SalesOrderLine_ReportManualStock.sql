﻿GO

IF OBJECT_ID('dbo.usp_selectAll_SalesOrderLine_ReportManualStock', 'P') IS NOT NULL
BEGIN
    DROP PROCEDURE dbo.usp_selectAll_SalesOrderLine_ReportManualStock;
END

GO

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

/*
===========================================================================================
TASK      			UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-210625]			Phuc Hoang			09-Aug-2024		CREATE          Sanctioned manufacturers need to be highlighted in red on the SOR PDF and SO screens.
===========================================================================================
*/

CREATE PROCEDURE [dbo].[usp_selectAll_SalesOrderLine_ReportManualStock]            
--******************************************************************************************            
--* RP 15.02.2010:            
--* - add ROHS            
--*              
--* SK 04.11.2009:            
--* - include SupplierPart            
--*              
--* SK 01.07.2009:            
--* - set LandedCost to 0 if null            
--*             
--* RP 04.06.2009:            
--* - added check of PO Line No to tighten up check for Manual Stock            
--******************************************************************************************            
    @SalesOrderLineId int            
AS             
    SELECT  sol.SalesOrderLineId            
          , sol.SalesOrderNo            
          , al.StockNo            
          , al.QuantityAllocated            
          , sk.Part            
          , sk.ManufacturerNo            
          , mf.ManufacturerCode            
          , sk.DateCode            
          , isnull(sk.LandedCost, 0) AS LandedCost            
          , sk.ProductNo            
          , pr.ProductName      
    , pr.ProductDescription          
          , so.CurrencyNo            
          , cu.CurrencyCode            
          , sk.ResalePrice            
          , sk.SupplierPart            
          , sk.ROHS           
              , isnull(pr.IsHazardous,0) as IsProdHazardous          
	,isnull(pr.IsOrderViaIPOonly,0) as IsOrderViaIPOonly   
	,sol.ECCNCode 
	,isnull((select top 1 m.ECCNStatus from dbo.tbECCN m   where m.ECCNCode= sol.ECCNCode ),0)  as IsECCNWarning 
    , retr.ManufacturerNo AS RestrictedMfrNo
	, retr.Inactive AS RestrictedMfrInactive

    FROM    tbAllocation al            
    JOIN    tbSalesOrderLine sol ON sol.SalesOrderLineId = al.SalesOrderLineNo            
    JOIN    tbSalesOrder so ON so.SalesOrderId = sol.SalesOrderNo            
    JOIN    tbCurrency cu ON cu.CurrencyId = so.CurrencyNo            
    JOIN    tbStock sk ON sk.StockId = al.StockNo            
    LEFT JOIN tbProduct pr ON pr.ProductId = sk.ProductNo            
    LEFT JOIN tbManufacturer mf ON mf.ManufacturerId = sk.ManufacturerNo 
	LEFT JOIN dbo.tbRestrictedManufacturer retr ON retr.ManufacturerNo = mf.ManufacturerId AND retr.ClientNo = so.ClientNo
    WHERE   sol.SalesOrderLineId = @SalesOrderLineId            
            AND sk.QuantityOnOrder = 0            
            AND sk.GoodsInLineNo IS NULL            
            AND sk.PurchaseOrderLineNo IS NULL 


GO
