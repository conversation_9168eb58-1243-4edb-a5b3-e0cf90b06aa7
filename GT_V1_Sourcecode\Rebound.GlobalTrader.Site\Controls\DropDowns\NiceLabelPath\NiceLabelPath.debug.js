///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------

Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");

Rebound.GlobalTrader.Site.Controls.DropDowns.NiceLabelPath = function(element) {
    Rebound.GlobalTrader.Site.Controls.DropDowns.NiceLabelPath.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.DropDowns.NiceLabelPath.prototype = {
	initialize: function() {
	    Rebound.GlobalTrader.Site.Controls.DropDowns.NiceLabelPath.callBaseMethod(this, "initialize");
		this.addSetupDataCall(Function.createDelegate(this, this.setupDataCall));
		this.addGetDataOK(Function.createDelegate(this, this.dataCallOK));
	},
	
	dispose: function() { 
		if (this.isDisposed) return;
		   Rebound.GlobalTrader.Site.Controls.DropDowns.NiceLabelPath.callBaseMethod(this, "dispose");
	},
	
	setupDataCall: function() {
	    this._objData.set_PathToData("controls/DropDowns/NiceLabelPath");
	    this._objData.set_DataObject("NiceLabelPath");
		this._objData.set_DataAction("GetData");
	},

	dataCallOK: function() { 
		var result = this._objData._result;
		if (result.NiceLabels) {
		    for (var i = 0; i < result.NiceLabels.length; i++) {
		        this.addOption($R_FN.setCleanTextValueForBackSlash(result.NiceLabels[i].Name), result.NiceLabels[i].ID);
			}
		}
	}
	
};

Rebound.GlobalTrader.Site.Controls.DropDowns.NiceLabelPath.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.NiceLabelPath", Rebound.GlobalTrader.Site.Controls.DropDowns.Base);
