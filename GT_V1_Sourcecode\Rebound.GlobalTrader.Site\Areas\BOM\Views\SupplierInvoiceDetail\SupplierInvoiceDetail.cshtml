﻿<script src='https://cdn.rawgit.com/edenspiekermann/a11y-toggle/master/a11y-toggle.min.js'></script>
<style>
    #SIDetails td {
        color: whitesmoke;
        padding: 5px;
        font-family:Tahoma;
        font-size:12px;

    }
    #SIDetails td label {
        color: whitesmoke;
    }
    #MatchingGILabel {
        color: whitesmoke;
        background-color: #000;
        width: 99.8%;
        font-size: 16px;
        font-family: tahoma;
        font-weight: bold;
        padding: 6px 0px 6px 3px;
    }

/*    #SupplierInvoice_PO_GI_Data table {
        text-align: left;
        border-collapse: collapse;
        background-color: #fff;
    }


    #SupplierInvoice_PO_GI_Data table tbody tr th {
        border: 1px solid black;
    }

    #SupplierInvoice_PO_GI_Data table tbody tr td {
        border: 1px solid black;
    }*/

    /*#SupplierInvoice_PO_GI_Data table tbody {*/
        /*float: left;*/
        /*width: 100%;*/
        /*margin-top:10px;*//*
        background-color:whitesmoke;*/
    /*}*/


    #hidden {
        display: none;
    }

    .extend_tbl thead th {
        background-color: #419b1b !important;
        color: #fff !important;
        background-image: none !important;
        border: 1px solid #bfbfbf;
        font-family: tahoma;
    }

    .supplier_part #collapseButton {
        color: #1F01FF;
        cursor: pointer;
    }

    .total_area {
        background-color: #dbb324 !important;
        font-weight: bold;
        color: #fff !important;
    }
    .extend_tbl {
        border-collapse:collapse;
        width:100%;
    }
    .extend_tbl tr td {
        background-color: #f7fdff;
    }
    .MatchingDataTable {
        width:100%;
        background-color:#fff;
        border-collapse:collapse;
    }
        .MatchingDataTable tbody td {
            border: 1px solid #bfbfbf;
            padding: 6px 3px 6px 3px;
            font-size: 12px;
            font-family: tahoma;
        }
    #collapseButton {
        color: #1F01FF;
        cursor: pointer;
    }
    .MatchingDataTable tbody tr th {
        background-image: url(../../../App_Themes/Original/images/FlexiDataTable/th_bg.png);
        background-position: bottom;
        background-repeat: repeat-x;
        font-weight: bold;
        border-bottom: solid 2px #e0e0e0;
        border-right: solid 1px #ffffff;
        background-color: #eeeeee;
        color: #999999;
        text-align: left;
        padding: 6px 3px 6px 3px;
        font-size: 12px;
        font-family: tahoma;
    }
    .LeftItem {
        font-weight: bold;
    }
</style>
<table id="SIDetails" class="details" style="width:100%">
    <tbody>
        <tr>
            <td style="width:30%; float:left">
                <table style="width:100%">
                    <tr>
                        <td class="LeftItem">
                            Supplier Invoice
                        </td>
                        <td class="item">
                            <label id="SupplierInvoiceNumber"></label>
                        </td>
                    </tr>
                    <tr>
                        <td class="LeftItem">
                            Supplier Code
                        </td>
                        <td class="item">
                            <label id="SupplierCode"></label>
                        </td>
                    </tr>
                    <tr>
                        <td class="LeftItem">
                            Invoice Date
                        </td>
                        <td class="item">
                            <label id="InvoiceDate"> </label>
                        </td>
                    </tr>
                    <tr>
                        <td class="LeftItem">
                            Currency
                        </td>
                        <td class="item">
                            <label id="Currency"></label>
                        </td>
                    </tr>
                    <tr>
                        <td class="LeftItem">
                            Invoice Amount
                        </td>
                        <td class="item">
                            <label id="InvoiceAmount"></label>
                        </td>
                    </tr>
                    <tr>
                        <td class="LeftItem">
                            Goods Value
                        </td>
                        <td class="item">
                            <label id="GoodsValue" style="color: black;background: yellow;"></label>
                        </td>
                    </tr>
                    <tr>
                        <td class="LeftItem">
                            Tax
                        </td>
                        <td class="item">
                            <label id="TaxAmount"></label>
                        </td>
                    </tr>
                    <tr>
                        <td class="LeftItem">
                            Tax
                        </td>
                        <td class="item">
                            <label id="TaxName"></label>
                        </td>
                    </tr>
                    <tr>
                        <td class="LeftItem">
                            Delivery Charge
                        </td>
                        <td class="item">
                            <label id="DeliveryCharge"></label>
                        </td>
                    </tr>
                </table>
            </td>
            <td style="width:30%; float:left;">
                <table style="width:100%">
                    <tr>
                        <td class="LeftItem">
                            Bank Fee
                        </td>
                        <td class="item">
                            <label id="BankFee"></label>
                        </td>
                    </tr>
                    <tr>
                        <td class="LeftItem">
                            Credit Card Fee
                        </td>
                        <td class="item">
                            <label id="CreditCardFee"></label>
                        </td>
                    </tr>
                    <tr>
                        <td class="LeftItem">
                            Notes
                        </td>
                        <td class="item">
                            <label id="Notes"></label>
                        </td>
                    </tr>
                    <tr>
                        <td class="LeftItem">
                            Exported?
                        </td>
                        <td class="item">
                            <input type="checkbox" class="sb-checkbox__input" name="check1" id="Exported" disabled/>
                            <label class="sb-checkbox__label sb-checkbox__label--green" for="check1"></label>
                            @*<label id="Exported"></label>*@
                        </td>
                    </tr>
                    <tr>
                        <td class="LeftItem">
                            Approved For Export
                        </td>
                        <td class="item">
                            <input type="checkbox" class="sb-checkbox__input" name="check2" id="ApprovedForExport" disabled/>
                            <label class="sb-checkbox__label sb-checkbox__label--green" for="check2"></label>
                            @*<label id="ApprovedForExport"></label>*@
                        </td>
                    </tr>
                    <tr>
                        <td class="LeftItem">
                            URN Number
                        </td>
                        <td class="item">
                            <label id="URNNumber"></label>
                        </td>
                    </tr>
                    <tr>
                        <td class="LeftItem">
                            Second Ref
                        </td>
                        <td class="item">
                            <label id="SecondRef"></label>
                        </td>
                    </tr>
                    <tr>
                        <td class="LeftItem">
                            Narrative
                        </td>
                        <td class="item">
                            <label id="Narrative"></label>
                        </td>
                    </tr>
                    <tr>
                        <td class="LeftItem">
                            Hold Reason
                        </td>
                        <td class="item">
                            <label id="Holdreason"></label>
                        </td>
                    </tr>
                    @*<tr>
            <td>
                Authorised By
            </td>
            <td class="item">
                <label id="AuthorisedBy"></label>
            </td>
        </tr>
        <tr>
            <td>
                Authorised Date
            </td>
            <td class="item">
                <label id="AuthorisedDate"></label>
            </td>
        </tr>
        <tr>
            <td>
                Authorised Note
            </td>
            <td class="item">
                <label id="AuthorisedNote"></label>
            </td>
        </tr>*@
                </table>
            </td>
        </tr>    
    </tbody>
</table>
<br>
<div id="MatchingGILabel">
    <label >Matching GI Lines as Per PO Numbers in Supplier Invoice</label>
</div>

<div id="SupplierInvoice_PO_GI_Data">
</div>

<table>
    <tr>
        <td><button id="ConfirmGILine" title="Add Selected GI Lines to Supplier Invoice">Confirm GI Lines</button></td>
        <td><button id="ExportForApproval" title="Authorize Supplier Invoice for Export">Save for Export</button></td>
    </tr>
</table>

<script src='https://cdnjs.cloudflare.com/ajax/libs/jquery/3.4.1/jquery.min.js'></script>
<script type="text/javascript">
    
    $(document).ready(function () {
        var qrystr = new URLSearchParams(window.location.search);
        
        $.ajax({
            processData: true,
            type: 'POST',
            url: 'GetSupplierInvoiceDetails?SID=' + qrystr.get("id"),
            success: function (data) {
                console.log(data);
                $('#SupplierInvoiceNumber').text(SetCleanText(data.SupplierInvoiceNumber));
                $('#SupplierCode').text(SetCleanText(data.SupplierCode));
                $('#InvoiceDate').text(getCustomDate(data.SupplierInvoiceDate));
                $('#Currency').text(data.CurrencyCode);
                $('#InvoiceAmount').text(parseFloat(data.InvoiceAmount).toFixed(4));
                $('#GoodsValue').text(parseFloat(data.GoodsValue).toFixed(4));
                $('#TaxAmount').text(parseFloat(data.Tax).toFixed(4));
                $('#TaxName').text(data.TaxName);
                $('#DeliveryCharge').text(data.DeliveryCharge);
                $('#BankFee').text(parseFloat(data.BankFee).toFixed(4));
                $('#CreditCardFee').text(data.CreditCardFee);
                $('#Notes').text(SetCleanText(data.Notes));
                $('#Exported').prop("checked", data.Exported);
                $('#ApprovedForExport').prop("checked", data.CanbeExported);
                $('#URNNumber').text(data.URNNumber);
                $('#SecondRef').text(data.SecondRef);
                $('#Narrative').text(data.Narrative);
                $('#Holdreason').text(data.StatusReason);
                $('#AuthorisedBy').text(data.AuthorisedBy);
                $('#AuthorisedDate').text(getCustomDate(data.AuthorisedDate));
                $('#AuthorisedNote').text(data.AuthorisedNote);
            },
            error: function (err) {
                alert('Error in Updating Export Status for Selected Supplier Invoices');

            }
        });

        $.ajax({
            processData: true,
            type: 'POST',
            url: 'GetMatchingGILines?SID=' + qrystr.get("id"),
            dataType: "json",
            contentType: "application/json;charset=utf-8",
            success: function (data) {
                var MatchedData = JSON.parse(data);
                var PurchaseOrderNumberList = MatchedData.PurchaseOrderNumberList;
                var MatchingGILineList = MatchedData.supplierInvoiceLineList;


                console.log(PurchaseOrderNumberList);
                console.log(MatchingGILineList);

                var content = "<table class='MatchingDataTable'>";
                content += '<tr>';
                content += '<th> Invoice Item </th>';
                content += '<th > Date </th>';
                content += '<th > PO </th>';
                content += '<th > Part No </th>';
                content += '<th > Unit Price </th>';
                content += '<th > Quantity </th>';
                content += '<th > Value </th>';
                content += '<th >Details</th>';
                content += '</tr>';
                for (i = 0; i < PurchaseOrderNumberList.length; i++) {
                    content += '<tr>';
                    content += '<td>' + "TBD" + '</td>';
                    content += '<td>' + PurchaseOrderNumberList[i].InvoiceDate.substring(0,10) + '</td>';
                    content += '<td>' + PurchaseOrderNumberList[i].PONumber + '</td>';
                    content += '<td>' + PurchaseOrderNumberList[i].Part + '</td>';
                    content += '<td>' + parseFloat(PurchaseOrderNumberList[i].UnitPrice).toFixed(4) + '</td>';
                    content += '<td>' + PurchaseOrderNumberList[i].Quantity + '</td>';
                    content += '<td>' + parseFloat(parseFloat(PurchaseOrderNumberList[i].UnitPrice) * parseInt(PurchaseOrderNumberList[i].Quantity)).toFixed(4) + '</td>';
                    content += '<td id="collapseButton" onclick="collapse(this)">View Details + </td>';
                    content += '</tr>';

                    /*content += '<tr style="height:5px;"><td colspan="9"></td></tr>';*/
                    content += '<tr id="hidden" style="background-color#c9c9c9;padding:3px;">';
                    content += '<td colspan="8" style="background-color:#c9c9c9; padding:3px;">';
                    content += '<div style="color: blue;font-size: medium;text-align: center;">';
                    content += '<table class="extend_tbl">';
                    if (MatchingGILineList.length > 0 && MatchingGILineList.some(el => parseInt(el.PurchaseOrderNumber) == parseInt(PurchaseOrderNumberList[i].PONumber))) {

                        content += '<thead>';
                        content += '<tr>';
                        content += '<th style="width:5%;">Select</th>';
                        content += '<th style="width:10%;"> Goods In</th>';
                        content += '<th style="width:10%;"> GT Date </th>';
                        content += '<th style="width:10%;"> PO </th>';
                        content += '<th style="width:15%;"> Part No</th>';
                        content += '<th style="width:10%;"> Unit Price </th>';
                        content += '<th style="width:10%;"> Quantity </th>';
                        content += '<th style="width:10%;"> Value </th>';
                        content += '<th style="width:10%;"> Status </th>';
                        content += '<th style="width:10%;"> SupplierInvoice </th>';
                        content += '</tr>';
                        content += '</thead>';
                        content += '<tbody style="width:100%;">';

                        for (var j = 0; j < MatchingGILineList.length; j++) {
                            var color = "";
                            if (parseInt(PurchaseOrderNumberList[i].PONumber) == parseInt(MatchingGILineList[j].PurchaseOrderNumber) && (PurchaseOrderNumberList[i].Part.replace(' ', '')) == (MatchingGILineList[j].Part.replace(' ',''))) {
                                if (parseInt(MatchingGILineList[j].MatchPercentage) <= 40) {
                                    color = "Red";
                                }
                                else {
                                    if (parseInt(MatchingGILineList[j].MatchPercentage) > 40 && parseInt(MatchingGILineList[j].MatchPercentage) <= 65) {
                                        color = "Orange";
                                    }
                                    else {
                                        if (parseInt(MatchingGILineList[j].MatchPercentage) > 65 && parseInt(MatchingGILineList[j].MatchPercentage) <= 90) {
                                            color = "Yellow";
                                        }
                                        else {
                                            color = "Green";
                                        }
                                    }
                                }
                                content += '<tr style="color: ' + color + ' !important">';

                                if (MatchingGILineList[j].GILineSelected) {
                                    content += '<td>' + '<input type="checkbox" ' + 'class="MatchedGILines" value="' + MatchingGILineList[j].GoodsInLineNo + '" ' + ' checked disabled/>' + '</td>';
                                }
                                else {
                                    content += '<td>' + '<input type="checkbox" ' + 'class="MatchedGILines" value="' + MatchingGILineList[j].GoodsInLineNo + '" ' + '/>' + '</td>';
                                }
                                content += '<td>' + '<a href="/Whs_GIDetail.aspx?gi=' + MatchingGILineList[j].GoodsInNo + '">' + MatchingGILineList[j].GoodsInNumber + '</a> (' + MatchingGILineList[j].RowNum + ')</td>';
                                content += '<td>' + MatchingGILineList[j].DateReceived.substring(0, 10) + '</td>';
                                content += '<td>' + MatchingGILineList[j].PurchaseOrderNumber + '</td>';
                                content += '<td>' + MatchingGILineList[j].Part + '</td>';
                                content += '<td>' + parseFloat(MatchingGILineList[j].Price).toFixed(4) + '</td>';
                                content += '<td>' + MatchingGILineList[j].QtyReceived + '</td>';
                                content += '<td>' + parseFloat(parseFloat(MatchingGILineList[j].Price) * parseInt(MatchingGILineList[j].QtyReceived)).toFixed(4) + '</td>';

                                if (MatchingGILineList[j].Quarantine) {
                                    content += '<td> <input type="checkbox" checked disabled />' + MatchingGILineList[j].GoodsInLineStatus + ' </td>';
                                    
                                }
                                else {
                                    content += '<td> <input type="checkbox" disabled />' + MatchingGILineList[j].GoodsInLineStatus + ' </td>';
                                }

                                if (MatchingGILineList[j].GILineSelected) {
                                    content += '<td> <a href="/Whs_SupplierInvoiceDetail.aspx?si=' + qrystr.get("id") + '" target="_blank" >' + MatchingGILineList[j].SupplierInvoiceNumber + '</a> </td>';
                                }
                                else {
                                    content += '<td>  </td>';
                                }

                                content += '</tr>';
                            }
                        }
                        content += '</tbody>';
                    }
                    else {
                        content += '<thead>';
                        content += '<tr style="width:100%;">No relevant GI line found for respective line item';

                        content += '</tr>';
                        content += '</thead>';
                        content += '<tbody style="width:100%;">';
                    }
                    content += '</table>';
                    content += '</div></td></tr>';
                }
                content += "</table>";
                $('#SupplierInvoice_PO_GI_Data').html(content);


                $('.MatchedGILines').change(function () {
                    debugger;
                    var total = 0;
                    $('.MatchedGILines:checked').each(function () {

                        total += parseFloat($(this).closest('tr').find("td:nth-child(8)").text());
                    });
                    $("#GoodsValue").text(parseFloat(total).toFixed(4));
                });




            },
            error: function (err) {
                alert('Error in Getting Matching GI Lines Data' + err);
                console.log(err);

            }
        });
        
        

        $('#ConfirmGILine').click(function () {
            
            if (confirm("This will Insert Selected GI Lines in Supplier Invoice Lines \n Do You Want to Coninue")) {
                var GILineSelected = "";
                $(".MatchedGILines:checkbox:checked").not(":disabled").each(function () {
                    GILineSelected += $(this).val() + "|";
                });

                console.log(GILineSelected);
                $.ajax({
                    processData: true,
                    type: 'POST',
                    url: 'InsertSupplierInvoiceLines?GIIds=' + GILineSelected + '&SID=' + qrystr.get("id"),
                    success: function (data) {
                        debugger;
                        console.log(data);
                        location.reload();
                    },
                    error: function (err) {
                        alert('Error in inserting Supplier Invoice Lines');

                    }
                });
            }

        });
        $('#ExportForApproval').click(function () {

            if (confirm("This will Save for Export \n Do You Want to Coninue")) {
                $.ajax({
                    processData: true,
                    type: 'POST',
                    url: 'SaveForExport?SID=' + qrystr.get("id"),
                    success: function (data) {
                        console.log(data);
                        if (data) {
                            alert("Export Approval Successful");
                            location.reload();
                        }
                        else {
                            alert("Export Approval Failure");
                        }
                    },
                    error: function (err) {
                        alert('Error in Save for Export');
                    }
                });
            }
        });

    });
    function SetCleanText(strIn, blnReplaceLineBreaks) {
        if (typeof (strIn) == "undefined") strIn = "";
        strIn = (strIn + "").trim();
        strIn = strIn.replace(/(:PLUS:)/g, "+");
        strIn = strIn.replace(/(:QUOTE:)/g, '"');
        strIn = strIn.replace(/((:AND:)|(&amp;))/g, "&");
        if (blnReplaceLineBreaks) strIn = strIn.replace(/(\n)/g, "<br />");
        return strIn;
    }

    function getCustomDate(dtinput) {
        var ParamDate = dtinput;
        if (dtinput != null) {
            var dt = new Date(parseInt(ParamDate.replace("/Date(", "").replace(")/", "")));
            var FormattedDate = AppendZero(dt.getDate()) + "/" + AppendZero(dt.getMonth() + 1) + "/" + AppendZero(dt.getFullYear());
            if (FormattedDate != "01/01/01") {
                return FormattedDate;
            }
            else {
                return '';
            }
        }
        else {
            return '';
        }
    }

    function AppendZero(n) {
        return (n < 10) ? '0' + n : n;
    }
    function collapse(cell) {
        var row = cell.parentElement;
        var target_row = row.parentElement.children[row.rowIndex + 1];
        if (target_row != undefined) {
            if (target_row.style.display == 'table-row') {
                cell.innerHTML = 'View Details +';
                target_row.style.display = 'none';
            } else {
                cell.innerHTML = 'View Details -';
                target_row.style.display = 'table-row';
            }
        }
    }

    $(document).ready(function () {
        debugger;
        $('.MatchedGILines').change(function () {
            var total = 0;
            $('.MatchedGILines:checked').each(function () {
                total += parseInt($(this).closest('tr').find("td:nth-child(8)").text());
            });
            $("#GoodsValue").text(total);
        });
    });
</script>