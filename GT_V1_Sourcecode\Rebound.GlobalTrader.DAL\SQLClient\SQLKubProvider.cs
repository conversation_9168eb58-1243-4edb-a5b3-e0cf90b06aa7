﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Data.SqlClient;

namespace Rebound.GlobalTrader.DAL.SQLClient
{
    //Anuj
    public class SQLKubProvider : KubProvider
    {
        public override KubAssistanceDetails FetchKubAssistanceDetails(System.String PartNo, System.Int32 ClientID)
        {
            KubAssistanceDetails obj = new KubAssistanceDetails();
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_KubGetAssistanceDetails", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.AddWithValue("@PartNumber", PartNo);
                cmd.Parameters.AddWithValue("@ClientID", ClientID);
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                while (reader.Read())
                {
                    obj.PartNo = GetReaderValue_String(reader, "PartNo", "");
                    obj.TotalPartsTreaded = GetReaderValue_String(reader, "TotalPartsTreaded", "");
                    obj.TotalGP = GetReaderValue_String(reader, "TotalGP", "");
                    obj.AverageBestPrice = GetReaderValue_String(reader, "AverageBestPrice", "");
                    obj.LastUpdatedDate = GetReaderValue_String(reader, "LastUpdatedDate", "");
                    obj.IsClientPrice = GetReaderValue_Boolean(reader, "IsClientPrice", false);
                }
                return obj;
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to get Kubs", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override KubAssistanceDetails ShowReadMoreData(System.String PartNo, System.Int32 ClientID)
        {
            KubAssistanceDetails obj = new KubAssistanceDetails();
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_KubGetAssistanceDetailsReadMore", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.AddWithValue("@PartNumber", PartNo);
                cmd.Parameters.AddWithValue("@ClientID", ClientID);
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                while (reader.Read())
                {
                    obj.TotalGPbasedLastActualBuyPrice = GetReaderValue_String(reader, "TotalGPbasedLastActualBuyPrice", "");
                    obj.LowestSalesPriceLast12Months = GetReaderValue_String(reader, "LowestSalesPriceLast12Months", "");
                    obj.NumberOfPartsSoldLast12months = GetReaderValue_String(reader, "NumberOfPartsSoldLast12months", "");
                    obj.LastEnquiredDateOfPart = GetReaderValue_String(reader, "LastEnquiredDateOfPart", "");
                    obj.LastQuotedPrice = GetReaderValue_String(reader, "LastQuotedPrice", "");
                    obj.LastSoldPrice = GetReaderValue_String(reader, "LastSoldPrice", "");
                    obj.LastHubprice = GetReaderValue_String(reader, "LastHubprice", "");
                }
                return obj;
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to get Kubs", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override KubAssistanceDetails StartKubCache(System.String PartNo, System.Int32 ClientID)
        {
            KubAssistanceDetails obj = new KubAssistanceDetails();
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_KubStartCacheProcessForAddRequirement", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.AddWithValue("@PartNumber", PartNo);
                cmd.Parameters.AddWithValue("@ClientID", ClientID);
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                while (reader.Read())
                {
                    obj.PartNo = GetReaderValue_String(reader, "PartNo", "");
                    obj.TotalPartsTreaded = GetReaderValue_String(reader, "TotalPartsTreaded", "");
                    obj.TotalGP = GetReaderValue_String(reader, "TotalGP", "");
                    obj.AverageBestPrice = GetReaderValue_String(reader, "AverageBestPrice", "");
                    obj.TotalGPbasedLastActualBuyPrice = GetReaderValue_String(reader, "TotalGPbasedLastActualBuyPrice", "");
                    obj.LowestSalesPriceLast12Months = GetReaderValue_String(reader, "LowestSalesPriceLast12Months", "");
                    obj.NumberOfPartsSoldLast12months = GetReaderValue_String(reader, "NumberOfPartsSoldLast12months", "");
                    obj.LastEnquiredDateOfPart = GetReaderValue_String(reader, "LastEnquiredDateOfPart", "");
                    obj.LastQuotedPrice = GetReaderValue_String(reader, "LastQuotedPrice", "");
                    obj.LastSoldPrice = GetReaderValue_String(reader, "LastSoldPrice", "");
                    obj.LastHubprice = GetReaderValue_String(reader, "LastHubprice", "");
                }
                return obj;
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to get Kubs", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override List<KubCountryWiseSaleDetails> ListKubCountryWiseSaleDetails(System.String PartNo, System.Int32 ClientID)
        {
            List<KubCountryWiseSaleDetails> list = new List<KubCountryWiseSaleDetails>();
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_KubGetCountryWiseSalesDetails", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.AddWithValue("@PartNo", PartNo);
                cmd.Parameters.AddWithValue("@ClientID", ClientID);
                cn.Open();
                DbDataReader dr = ExecuteReader(cmd);
                if (dr.HasRows)
                {
                    while (dr.Read())
                    {
                        KubCountryWiseSaleDetails obj = new KubCountryWiseSaleDetails();
                        obj.Countries = GetReaderValue_String(dr, "Countries", "");
                        obj.NoOfSales = GetReaderValue_String(dr, "NoOfSales", "");
                        obj.ReSale = GetReaderValue_String(dr, "ReSale", "");
                        obj.UnShippedReSale = GetReaderValue_String(dr, "UnShipReSale", "");
                        obj.UnShippedNoOfSales = GetReaderValue_String(dr, "UnshipNoOfSales", "");
                        list.Add(obj);
                        obj = null;
                    }
                }
                else
                {
                    list = null;
                }
                return list;
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to get Kub Country", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override List<KubTop3BuyPriceDetails> ListKubTop3BuyPriceDetails(System.String PartNo, System.Int32 ClientID)
        {
            List<KubTop3BuyPriceDetails> list = new List<KubTop3BuyPriceDetails>();
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_KubGetTop3BuyPriceDetails", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.AddWithValue("@PartNo", PartNo);
                cmd.Parameters.AddWithValue("@ClientID", ClientID);
                cn.Open();
                DbDataReader dr = ExecuteReader(cmd);
                if (dr.HasRows)
                {
                    while (dr.Read())
                    {
                        KubTop3BuyPriceDetails obj = new KubTop3BuyPriceDetails();
                        obj.POID = GetReaderValue_String(dr, "POID", "");
                        obj.PONo = GetReaderValue_String(dr, "PONo", "");
                        obj.Date = GetReaderValue_String(dr, "Date", "");
                        obj.Price = GetReaderValue_String(dr, "Price", "");
                        obj.IsClientPrice = GetReaderValue_Boolean(dr, "IsClientPrice", false);
                        list.Add(obj);
                        obj = null;
                    }
                }
                else
                {
                    list = null;
                }
                return list;
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to get Kub Country", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override List<KubTop3BuyPriceDetails> ListKubTop3BuyPriceDetailsForHUB(System.String PartNo, System.Int32 ClientID)
        {
            List<KubTop3BuyPriceDetails> list = new List<KubTop3BuyPriceDetails>();
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                //cmd = new SqlCommand("usp_KubGetTop3BuyPriceDetailsForHUB", cn);
                cmd = new SqlCommand("usp_get_Top3BuyPrice_BOM_KUB", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.AddWithValue("@PartNo", PartNo);
                cmd.Parameters.AddWithValue("@ClientID", ClientID);
                cn.Open();
                DbDataReader dr = ExecuteReader(cmd);
                if (dr.HasRows)
                {
                    while (dr.Read())
                    {
                        KubTop3BuyPriceDetails obj = new KubTop3BuyPriceDetails();
                        obj.POID = GetReaderValue_String(dr, "POID", "");
                        obj.PONo = GetReaderValue_String(dr, "PONo", "");
                        obj.Date = GetReaderValue_String(dr, "Date", "");
                        obj.Price = GetReaderValue_String(dr, "Price", "");
                        obj.IsClientPrice = GetReaderValue_Boolean(dr, "IsClientPrice", false);
                        list.Add(obj);
                        obj = null;
                    }
                }
                else
                {
                    list = null;
                }
                return list;
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to get Kub Country", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override List<KubLast10RecentQuoteDetails> ListKubLast10RecentQuoteDetails(System.String PartNo, System.String CustomerReqId, System.Int32 ClientID)
        {
            List<KubLast10RecentQuoteDetails> lst = new List<KubLast10RecentQuoteDetails>();
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_KubGetLast10QuoteDetails", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.AddWithValue("@PartNo", PartNo);
                cmd.Parameters.AddWithValue("@ClientID", ClientID);
                cmd.Parameters.AddWithValue("@CustomerReqId", CustomerReqId);
                cn.Open();
                DbDataReader dr = ExecuteReader(cmd);
                if (dr.HasRows)
                {
                    while (dr.Read())
                    {
                        KubLast10RecentQuoteDetails obj = new KubLast10RecentQuoteDetails();
                        obj.QuoteID = GetReaderValue_String(dr, "QuoteID", "");
                        obj.QuoteNumber = GetReaderValue_String(dr, "QuoteNumber", "");
                        obj.QuoteDate = GetReaderValue_String(dr, "QuoteDate", "");
                        obj.Quantity = GetReaderValue_String(dr, "Quantity", "");
                        obj.UnitPrice = GetReaderValue_String(dr, "UnitPrice", "");
                        obj.BuyPrice = GetReaderValue_String(dr, "BuyPrice", "");
                        obj.Profit = GetReaderValue_String(dr, "Profit", "");
                        lst.Add(obj);
                        obj = null;
                    }
                }
                else
                {
                    lst = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to get Kub search details", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override KubAvgPriceDetails FetchKubAvgPriceDetails(System.String PartNo, System.Int32 ClientID)
        {
            KubAvgPriceDetails obj = new KubAvgPriceDetails();
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_KubGetAveragePriceDetails", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.AddWithValue("@PartNo", PartNo);
                cmd.Parameters.AddWithValue("@ClientID", ClientID);
                cn.Open();
                DbDataReader dr = ExecuteReader(cmd);
                while (dr.Read())
                {
                    obj.AveragePriceOfPartLast12Months = GetReaderValue_String(dr, "AveragePriceOfPartLast12Months", "");
                    obj.LastUpdatedDate = GetReaderValue_String(dr, "LastUpdatedDate", "");
                }
                return obj;
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to get Kubs Price", sqlex);
            }
            finally
            {
                obj = null;
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override KubTotalLineInvoiceDetails FetchKubTotalLineInvoiceDetails(System.String PartNo, System.Int32 ClientID, System.Int32? CustomerReqId)
        {
            KubTotalLineInvoiceDetails obj = new KubTotalLineInvoiceDetails();
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_KubGetTotalLineInvoicedDetails", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.AddWithValue("@PartNo", PartNo);
                cmd.Parameters.AddWithValue("@ClientID", ClientID);
                cmd.Parameters.AddWithValue("@CustomerReqId", CustomerReqId);
                cn.Open();
                DbDataReader dr = ExecuteReader(cmd);
                while (dr.Read())
                {
                    obj.TotalNoOfLinesInvoicedIn12Months = GetReaderValue_String(dr, "TotalNoOfLinesInvoicedIn12Months", "");
                }
                return obj;
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to get Kubs KubTotalLineInvoiceDetails", sqlex);
            }
            finally
            {
                obj = null;
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override List<KubMainProductGroupDetails> ListKubMainProductGroupDetails(System.String PartNo, System.Int32 ClientID)
        {
            List<KubMainProductGroupDetails> lst = new List<KubMainProductGroupDetails>();
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_KubGetMainProductGroupsDetails", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.AddWithValue("@PartNo", PartNo);
                cmd.Parameters.AddWithValue("@ClientID", ClientID);
                cn.Open();
                DbDataReader dr = ExecuteReader(cmd);
                if (dr.HasRows)
                {
                    while (dr.Read())
                    {
                        KubMainProductGroupDetails obj = new KubMainProductGroupDetails();
                        obj.MainPoductGroup = GetReaderValue_String(dr, "MainPoductGroup", "");
                        lst.Add(obj);
                        obj = null;
                    }
                }
                else
                {
                    lst = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to get Kub MainPoductGroup details", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override List<KubLast10RecentQuoteDetails> StartKubCacheForBrowsePage(System.String PartNo, System.String CustomerReqId, System.Int32 ClientID)
        {
            List<KubLast10RecentQuoteDetails> lst = new List<KubLast10RecentQuoteDetails>();
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_KubStartCacheProcessForBrowseRequirement", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.AddWithValue("@PartNo", PartNo);
                cmd.Parameters.AddWithValue("@ClientID", ClientID);
                cmd.Parameters.AddWithValue("@CustomerReqId", CustomerReqId);
                cn.Open();
                DbDataReader dr = ExecuteReader(cmd);
                if (dr.HasRows)
                {
                    while (dr.Read())
                    {
                        KubLast10RecentQuoteDetails obj = new KubLast10RecentQuoteDetails();
                        obj.QuoteID = GetReaderValue_String(dr, "QuoteID", "");
                        obj.QuoteNumber = GetReaderValue_String(dr, "QuoteNumber", "");
                        obj.QuoteDate = GetReaderValue_String(dr, "QuoteDate", "");
                        obj.Quantity = GetReaderValue_String(dr, "Quantity", "");
                        obj.UnitPrice = GetReaderValue_String(dr, "UnitPrice", "");
                        obj.BuyPrice = GetReaderValue_String(dr, "BuyPrice", "");
                        obj.Profit = GetReaderValue_String(dr, "Profit", "");
                        lst.Add(obj);
                        obj = null;
                    }
                }
                else
                {
                    lst = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to get Kub search details", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override List<KubLast10RecentQuoteDetails> IsAllowedEnabled(System.String PartNo, System.String CustomerReqId, System.Int32 ClientID)
        {
            List<KubLast10RecentQuoteDetails> lst = new List<KubLast10RecentQuoteDetails>();
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_KubIsAllowedEnable", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.AddWithValue("@PartNo", PartNo);
                cmd.Parameters.AddWithValue("@ClientID", ClientID);
                cmd.Parameters.AddWithValue("@CustomerReqId", CustomerReqId);
                cn.Open();
                DbDataReader dr = ExecuteReader(cmd);
                if (dr.HasRows)
                {
                    while (dr.Read())
                    {
                        KubLast10RecentQuoteDetails obj = new KubLast10RecentQuoteDetails();
                        obj.IsAllowedEnable = GetReaderValue_NullableBoolean(dr, "IsAllowedEnable", false);

                        lst.Add(obj);
                        obj = null;
                    }
                }
                else
                {
                    lst = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to get Kub search details", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override List<KubCountryWiseSaleDetails> GetGPCalculationDetails(System.String PartNo, System.Int32 ClientID)
        {
            List<KubCountryWiseSaleDetails> list = new List<KubCountryWiseSaleDetails>();
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_KUB_GPCalculationDetails", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.AddWithValue("@ClientID", ClientID);
                cmd.Parameters.AddWithValue("@PartNumber", PartNo);
                cn.Open();
                DbDataReader dr = ExecuteReader(cmd);
                if (dr.HasRows)
                {
                    while (dr.Read())
                    {
                        KubCountryWiseSaleDetails obj = new KubCountryWiseSaleDetails();
                        obj.InvoiceId = GetReaderValue_String(dr, "InvoiceId", "");
                        obj.InvoiceNumber = GetReaderValue_String(dr, "InvoiceNumber", "");
                        obj.Quantity = GetReaderValue_String(dr, "Quantity", "");
                        obj.ShippingCost = GetReaderValue_String(dr, "ShippingCost", "");
                        obj.Freight = GetReaderValue_String(dr, "Freight", "");
                        obj.LandedCost = GetReaderValue_String(dr, "LandedCost", "");
                        obj.InvoiceValue = GetReaderValue_String(dr, "InvoiceValue", "");
                        obj.TOTAL = GetReaderValue_String(dr, "TOTAL", "");

                        list.Add(obj);
                        obj = null;
                    }
                }
                else
                {
                    list = null;
                }
                return list;
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to get Kub Country", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override List<KubCountryWiseSaleDetails> GetGPLastSaleCalculationDetails(System.String PartNo, System.Int32 ClientID)
        {
            List<KubCountryWiseSaleDetails> list = new List<KubCountryWiseSaleDetails>();
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_KUB_GPLastSaleCalculationDetails", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.AddWithValue("@ClientID", ClientID);
                cmd.Parameters.AddWithValue("@PartNumber", PartNo);
                cn.Open();
                DbDataReader dr = ExecuteReader(cmd);
                if (dr.HasRows)
                {
                    while (dr.Read())
                    {
                        KubCountryWiseSaleDetails obj = new KubCountryWiseSaleDetails();
                        obj.InvoiceId = GetReaderValue_String(dr, "InvoiceId", "");
                        obj.InvoiceNumber = GetReaderValue_String(dr, "InvoiceNumber", "");
                        obj.Quantity = GetReaderValue_String(dr, "Quantity", "");
                        obj.ShippingCost = GetReaderValue_String(dr, "ShippingCost", "");
                        obj.Freight = GetReaderValue_String(dr, "Freight", "");
                        obj.LandedCost = GetReaderValue_String(dr, "LandedCost", "");
                        obj.InvoiceValue = GetReaderValue_String(dr, "InvoiceValue", "");
                        obj.TOTAL = GetReaderValue_String(dr, "TOTAL", "");

                        list.Add(obj);
                        obj = null;
                    }
                }
                else
                {
                    list = null;
                }
                return list;
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to get Kub Country", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override SQLAllowedEnabledForHUB IsAllowedEnabledForHUB(System.String PartNo, System.Int32 ClientID, int bomId, int manufacturerId, string manufacturerName, bool isHubRFQ)
        {
            SQLAllowedEnabledForHUB obj = new SQLAllowedEnabledForHUB();
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_check_KubEnableForBOMPart", cn)
                {
                    CommandType = CommandType.StoredProcedure,
                    CommandTimeout = 30
                };
                cmd.Parameters.AddWithValue("@PartNo", PartNo);
                cmd.Parameters.AddWithValue("@ClientID", ClientID);
                cmd.Parameters.AddWithValue("@BOMID", bomId);
                cmd.Parameters.AddWithValue("@IsHubRFQ", isHubRFQ);
                cmd.Parameters.AddWithValue("@ManufacturerID", manufacturerId);
                cmd.Parameters.AddWithValue("@ManufacturerName", manufacturerName);
                cn.Open();
                DbDataReader dr = ExecuteReader(cmd);
                if (dr.HasRows)
                {
                    while (dr.Read())
                    {
                        obj.IsAllowedEnable = GetReaderValue_NullableBoolean(dr, "IsAllowedEnable", false);
                    }
                }
                return obj;
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to get usp_check_KubEnableForBOMPart", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override KubAssistanceDetails LoadKubAssistanceForBOMManager(string PartNo, int ClientID, int BOMManagerID, bool isHubRFQ, int manufacturerId, string manufacturerName)
        {
            KubAssistanceDetails obj = new KubAssistanceDetails();
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_select_KubAssistanceForBOMManager", cn)
                {
                    CommandType = CommandType.StoredProcedure,
                    CommandTimeout = 30
                };
                cmd.Parameters.AddWithValue("@PartNumber", PartNo);
                cmd.Parameters.AddWithValue("@ClientID", ClientID);
                cmd.Parameters.AddWithValue("@BOMManagerID", BOMManagerID);
                cmd.Parameters.AddWithValue("@IsHubRFQ", isHubRFQ);
                cmd.Parameters.AddWithValue("@ManufacturerId", manufacturerId);
                cmd.Parameters.AddWithValue("@ManufacturerName", manufacturerName);
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                while (reader.Read())
                {
                    obj.NumberOfRequirement = GetReaderValue_String(reader, "NumberOfRequirement", "");
                    obj.LastQuotedPrice = GetReaderValue_String(reader, "LastQuotedPrice", "");
                    obj.LastHubprice = GetReaderValue_String(reader, "LastHubprice", "");
                    obj.LastSoldPrice = GetReaderValue_String(reader, "LastSoldPrice", "");
                    obj.LastHighestSoldPrice = GetReaderValue_String(reader, "LastHighestSoldPrice", "");
                    obj.LastLowestSoldPrice = GetReaderValue_String(reader, "LastLowestSoldPrice", "");
                    obj.NumberOfInvoice = GetReaderValue_String(reader, "NumberOfInvoice", "");
                    obj.LastestHubRFQName = GetReaderValue_String(reader, "LastestHubRFQName", "");
                    obj.LastestHubNumberDate = GetReaderValue_DateTime(reader, "LastestHubNumberDate", DateTime.MinValue);
                    obj.LastestHubRFQId = GetReaderValue_String(reader, "LastestHubRFQId", "");
                    obj.NumberOfQuote = GetReaderValue_String(reader, "NumberOfQuote", "");
                    obj.NumberQuoteToSalesOrder = GetReaderValue_String(reader, "NumberQuoteToSalesOrder", "");
                    obj.LastUpdatedDate = GetReaderValue_String(reader, "LastUpdatedDate", "");
                    obj.LastDatePartSoldToBomCustomer = GetReaderValue_String(reader, "LastDatePartSoldToBomCustomer", "");
                    obj.IHSResultForPartNo = GetReaderValue_String(reader, "IHSResult", "");
                    obj.LyticaResultForPartNo = GetReaderValue_String(reader, "LyticaResult", "");
                }
                return obj;
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to get LoadKubAssistanceForBOMManager", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override List<KubTop20CustomeRequirementForBOM> ListKubTop20CustomerRequirementForBOM(System.String PartNo, System.Int32 ClientID)
        {
            List<KubTop20CustomeRequirementForBOM> list = new List<KubTop20CustomeRequirementForBOM>();
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_select_KubAssistanceLastTop20CusRqForBOM", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.AddWithValue("@PartNumber", PartNo);
                cmd.Parameters.AddWithValue("@ClientID", ClientID);
                cn.Open();
                DbDataReader dr = ExecuteReader(cmd);
                if (dr.HasRows)
                {
                    while (dr.Read())
                    {
                        KubTop20CustomeRequirementForBOM obj = new KubTop20CustomeRequirementForBOM();
                        obj.BOMName = GetReaderValue_String(dr, "BOMName", "");
                        obj.Price = GetReaderValue_String(dr, "Price", "");
                        obj.Quantity = GetReaderValue_String(dr, "Quantity", "");
                        obj.Quoted = GetReaderValue_String(dr, "Quoted", "");
                        list.Add(obj);
                        obj = null;
                    }
                }
                else
                {
                    list = null;
                }
                return list;
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to get ListKubTop20CustomerRequirementForBOM", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override List<SqlKubTop10QuoteForBOM> ListKubTop10QuoteForBOM(System.String PartNo, System.Int32 ClientID)
        {
            List<SqlKubTop10QuoteForBOM> list = new List<SqlKubTop10QuoteForBOM>();
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_select_KubAssistanceLastTop10QuoteForBOM", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.AddWithValue("@PartNumber", PartNo);
                cmd.Parameters.AddWithValue("@ClientID", ClientID);

                cn.Open();
                DbDataReader dr = ExecuteReader(cmd);
                if (dr.HasRows)
                {
                    while (dr.Read())
                    {
                        SqlKubTop10QuoteForBOM obj = new SqlKubTop10QuoteForBOM
                        {
                            QuoteNumber = GetReaderValue_String(dr, "QuoteNumber", ""),
                            Price = GetReaderValue_String(dr, "Price", ""),
                            Quantity = GetReaderValue_String(dr, "Quantity", ""),
                            ConvertedToSO = GetReaderValue_String(dr, "ConvertedToSO", "")
                        };

                        list.Add(obj);
                        obj = null;
                    }
                }
                else
                {
                    list = null;
                }
                return list;
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to get ListKubTop10QuoteForBOM", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override KubStockDetailsForBOM GetStockDetailsForBOMPart(string partNo, int clientId, int BOMId, bool isHubRFQ)
        {
            KubStockDetailsForBOM stockDetailsForPart = new KubStockDetailsForBOM();
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_select_StockDetailsForBOMPart", cn)
                {
                    CommandType = CommandType.StoredProcedure,
                    CommandTimeout = 30
                };
                cmd.Parameters.AddWithValue("@PartNumber", partNo);
                cmd.Parameters.AddWithValue("@ClientID", clientId);
                cmd.Parameters.AddWithValue("@BOMID", BOMId);
                cmd.Parameters.AddWithValue("@IsHUBRFQ", isHubRFQ);

                cn.Open();
                SqlDataReader dr = cmd.ExecuteReader();
                if (dr.HasRows)
                {
                    List<StockOffer> top10RLOffers = new List<StockOffer>();
                    //get Reverse Logistics 10 most recent offers for selected part
                    while (dr.Read())
                    {
                        StockOffer rlOffer = new StockOffer()
                        {
                            DateAdded = GetReaderValue_String(dr, "DateAdded", ""),
                            Quantity = GetReaderValue_Int32(dr, "Quantity", 0),
                            Price = GetReaderValue_String(dr, "Price", ""),
                            Manufacturer = GetReaderValue_String(dr, "Manufacturer", "")
                        };
                        top10RLOffers.Add(rlOffer);
                    }

                    //get Strategic Stock 10 most recent offers for selected part 
                    dr.NextResult();
                    List<StockOffer> top10StrategicStockOffers = new List<StockOffer>();
                    while (dr.Read())
                    {
                        StockOffer strategicStockOffer = new StockOffer()
                        {
                            DateAdded = GetReaderValue_String(dr, "DateAdded", ""),
                            Quantity = GetReaderValue_Int32(dr, "Quantity", 0),
                            Price = GetReaderValue_String(dr, "Price", ""),
                            Manufacturer = GetReaderValue_String(dr, "Manufacturer", "")
                        };
                        top10StrategicStockOffers.Add(strategicStockOffer);
                    }

                    //get Stock 20 most recent for selected part
                    dr.NextResult();
                    List<StockDetailsForPart> top20RecentStock = new List<StockDetailsForPart>();
                    while (dr.Read())
                    {
                        StockDetailsForPart stockDetail = new StockDetailsForPart()
                        {
                            StockId = GetReaderValue_String(dr, "StockId", ""),
                            Quantity = GetReaderValue_Int32(dr, "Quantity", 0),
                            Price = GetReaderValue_String(dr, "Price", ""),
                            Client = GetReaderValue_String(dr, "Client", "")
                        };
                        top20RecentStock.Add(stockDetail);
                    }
                    stockDetailsForPart.Top10ReverseLogisticsOffer = top10RLOffers;
                    stockDetailsForPart.Top10StrategicStockOffer = top10StrategicStockOffers;
                    stockDetailsForPart.Top20RecentStockForPart = top20RecentStock;
                }
                return stockDetailsForPart;
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to get GetStockDetailsForBOMPart", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
    }
}
