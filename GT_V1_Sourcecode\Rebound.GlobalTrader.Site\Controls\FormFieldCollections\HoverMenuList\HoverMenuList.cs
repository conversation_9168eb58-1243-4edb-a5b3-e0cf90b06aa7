using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using System.Collections;
using Rebound.GlobalTrader.Site.Controls.Forms;

namespace Rebound.GlobalTrader.Site.Controls.FormFieldCollections {
	[DefaultProperty("")]
    [ToolboxData("<{0}:HoverMenuList runat=server></{0}:HoverMenuList>")]
	public class HoverMenuList : Base, INamingContainer {

		#region Locals

        //private Table _tbl;
       // private TableRow _trMenuList;
        Panel pnlSubMenu;
        ReboundTextBox txtReason;
        ReboundTextBox txtReasonHidden;
        IconButton icon;
       // Panel pnlSubMenu1;
        int i = 0;
		#endregion

		#region Properties

		#endregion

		protected override void OnInit(EventArgs e) {
			base.OnInit(e);
			AddScriptReference("Controls.FormFieldCollections.HoverMenuList.HoverMenuList");
			RemoveCSSClass = true;
            ((Pages.Base)Page).AddCSSFile("ddsmoothmenu.css");
            //((Pages.Base)Page).AddScriptReference("ddsmoothmenu");
		}

        protected override void OnLoad(EventArgs e)
        {
            
            //start table
           
                Reason1();
                //Reson2();
                SetupScriptDescriptors();
                base.OnLoad(e);
           
        }

        private void Reason1()
        {
            HtmlTable tbl = new HtmlTable();
            tbl.Border = 0;
            tbl.CellPadding = 0;
            tbl.CellSpacing = 0;
            HtmlTableRow tr = new HtmlTableRow();
            tr.VAlign = "middle";
            tbl.Controls.Add(tr);

            
            //right cell
            HtmlTableCell tdRight = new HtmlTableCell();
            //tdRight.Style.Add("width", "175px");
            tdRight.Style.Add("vertical-align", "top");
            tdRight.Style.Add("padding", "0px 4px 0px 0px");
            txtReason = new ReboundTextBox();
            txtReason.ID = "txtReason";
            txtReason.Width = 65;
            txtReason.ReadOnly = true;
            txtReason.TextBoxMode = ReboundTextBox.TextBoxModeList.Normal;
            tdRight.Controls.Add(txtReason);
            txtReasonHidden = new ReboundTextBox();            
            txtReasonHidden.ID = "txtReasonHidden";
            txtReasonHidden.Width = 10;
            txtReasonHidden.TextBoxMode = ReboundTextBox.TextBoxModeList.Normal;
            txtReasonHidden.Style.Add("display","none");
            tdRight.Controls.Add(txtReasonHidden);
            tr.Controls.Add(tdRight);

            //

            //left cell
            HtmlTableCell tdLeft = new HtmlTableCell();
            tdLeft.Style.Add("width", "17px");
            icon = new IconButton();
            icon.ID = "ctlIcon";
            icon.IconGroup = Rebound.GlobalTrader.Site.Controls.IconButton.IconGroupList.Nugget;
            icon.IconCSSType = "Arrow8D";
            icon.IconTitleResource = "Arrow";
            icon.IconButtonMode = Rebound.GlobalTrader.Site.Controls.IconButton.IconButtonModeList.Hyperlink;
            icon.Href = "javascript:void(0);";
            tdLeft.Controls.Add(icon);
            tr.Controls.Add(tdLeft);



            HtmlTableCell tdMiddle = new HtmlTableCell();
            tdMiddle.Style.Add("width", "175px");
            pnlSubMenu = ControlBuilders.CreatePanel("sidebarmenu");
            pnlSubMenu.CssClass = "sidebarmenu";
            // pnlSubMenu.Style.Add("display", "none");
            tdMiddle.Controls.Add(pnlSubMenu);
            tr.Controls.Add(tdMiddle);

            
                     
            AddControl(tbl);
        }
        //private void Reson2()
        //{
        //    HtmlTable tbl = new HtmlTable();
        //    tbl.Border = 0;
        //    tbl.CellPadding = 0;
        //    tbl.CellSpacing = 0;
        //    HtmlTableRow tr = new HtmlTableRow();
        //    tr.VAlign = "middle";
        //    tbl.Controls.Add(tr);

        //    //left cell
        //    HtmlTableCell tdLeft = new HtmlTableCell();
        //    tdLeft.Style.Add("width", "175px");
        //    pnlSubMenu1 = ControlBuilders.CreatePanel("sidebarmenu");
        //    pnlSubMenu1.CssClass = "sidebarmenu";
        //    tdLeft.Controls.Add(pnlSubMenu1);
        //    tr.Controls.Add(tdLeft);

        //    //right cell
        //    HtmlTableCell tdRight = new HtmlTableCell();
        //    tdRight.Style.Add("width", "175px");
        //    tdRight.Style.Add("vertical-align", "middle");
        //    tdRight.Style.Add("padding", "0px 0px 0px 12px");
        //    ReboundTextBox txtQuantity = new ReboundTextBox();
        //    txtQuantity.ID = "txtQuantity1";
        //    txtQuantity.Width = 60;
        //    txtQuantity.TextBoxMode = ReboundTextBox.TextBoxModeList.Normal;
        //    tdRight.Controls.Add(txtQuantity);
        //    tr.Controls.Add(tdRight);

        //    AddControl(tbl);
        //}

		private void SetupScriptDescriptors() {
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.FormFieldCollections.HoverMenuList", this.ClientID);
            _scScriptControlDescriptor.AddElementProperty("pnlSubMenu", pnlSubMenu.ClientID);
            _scScriptControlDescriptor.AddElementProperty("txtReason", txtReason.ClientID);
            _scScriptControlDescriptor.AddElementProperty("txtReasonHidden", txtReasonHidden.ClientID);
            _scScriptControlDescriptor.AddElementProperty("ibtnIcon", icon.ClientID);
            //_scScriptControlDescriptor.AddElementProperty("pnlSubMenu1", pnlSubMenu1.ClientID);
		}

	}
}
