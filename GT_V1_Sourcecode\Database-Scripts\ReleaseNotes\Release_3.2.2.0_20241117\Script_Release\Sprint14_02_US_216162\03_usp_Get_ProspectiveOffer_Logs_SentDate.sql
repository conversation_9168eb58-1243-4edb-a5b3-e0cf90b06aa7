﻿/*     
===========================================================================================    
TASK   UPDATED BY   DATE   ACTION  DESCRIPTION    
[US-216162]  CuongDox  2-Nov-2024  CREATE  Get Prospective Offer logs sent date By Id proId and CusrId
===========================================================================================    
*/  
CREATE OR ALTER PROCEDURE [dbo].[usp_Get_ProspectiveOffer_Logs_SentDate]  
	@ProspectiveOfferId INT = NULL, @CustomerRequirementIds  Nvarchar(MAX) = NULL
AS
BEGIN
	SELECT
	cr.Part AS PartNo
	,mfr.ManufacturerName AS Manufacturer
	,polg.SentDate AS SentDate
	FROM tbProspectiveOfferLogs polg
	INNER JOIN tbProspectiveOfferLines pol ON polg.ProspectiveOfferLineNo = pol.ProspectiveOfferLineId
	INNER JOIN tbCustomerRequirement cr ON polg.CustomerRequirementNo = cr.CustomerRequirementId
	LEFT JOIN tbManufacturer mfr ON mfr.ManufacturerId = pol.ManufacturerNo  
	WHERE
		pol.ProspectiveOfferLineId =@ProspectiveOfferId
		AND cr.CustomerRequirementId in ( SELECT CAST(value AS INT) FROM STRING_SPLIT(@CustomerRequirementIds, ',') )
	ORDER  BY SentDate
END