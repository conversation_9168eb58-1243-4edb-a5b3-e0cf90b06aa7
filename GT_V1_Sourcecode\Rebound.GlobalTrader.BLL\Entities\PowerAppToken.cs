﻿/*
Marker     Changed by              Date            Remarks
[001]      A<PERSON><PERSON><PERSON>          18/05/2022      Need to generate Power App token.
*/
using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;
using Rebound.GlobalTrader.DAL;
using Rebound.GlobalTrader.BLL;

namespace Rebound.GlobalTrader.BLL
{
    public partial class PowerAppToken : BizObject
    {
        #region Properties

        protected static DAL.PowerAppTokenElement Settings
        {
            get { return Globals.Settings.PowerAppTokens; }
        }

        /// <summary>
        /// RequestId
        /// </summary>
        public System.Int32 RequestId { get; set; }  
        /// <summary>
        /// TokenValue
        /// </summary>
        public System.String TokenValue { get; set; }
        #endregion

        #region Methods
        public static PowerAppToken GetTokenForPowerApp(System.Int32? loginId, System.String WorkFlowType,System.Boolean? IsNotifySO)
        {
            Rebound.GlobalTrader.DAL.PowerAppTokenDetails objDetails = Rebound.GlobalTrader.DAL.SiteProvider.PowerAppToken.GetTokenForPowerApp(loginId, WorkFlowType, IsNotifySO);
            if (objDetails == null)
            {
                return null;
            }
            else
            {
                PowerAppToken obj = new PowerAppToken();
                obj.RequestId = objDetails.RequestId;
                obj.TokenValue = objDetails.TokenValue;
                objDetails = null;
                return obj;
            }
        }
        #endregion
    }
}
