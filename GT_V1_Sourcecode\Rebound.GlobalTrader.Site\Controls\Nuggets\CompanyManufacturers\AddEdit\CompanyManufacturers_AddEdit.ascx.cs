using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site;
using Rebound.GlobalTrader.Site.Controls;

namespace Rebound.GlobalTrader.Site.Controls.Forms {
	public partial class CompanyManufacturers_AddEdit : Base {


		#region Locals

		protected AutoSearch.Manufacturers _autManufacturers;

		#endregion

		#region Properties

		private string _strTitleEdit = "";
		public string TitleEdit {
			get { return _strTitleEdit; }
			set { _strTitleEdit = value; }
		}

		private string _strTitleAdd = "";
		public string TitleAdd {
			get { return _strTitleAdd; }
			set { _strTitleAdd = value; }
		}

		#endregion

		#region Overrides

		/// <summary>
		/// OnInit
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
			base.OnInit(e);
			_strTitleEdit = Functions.GetGlobalResource("FormTitles", "CompanyManufacturers_Edit");
			_strTitleAdd = Functions.GetGlobalResource("FormTitles", "CompanyManufacturers_Add");
			TitleText = _strTitleAdd;
			AddScriptReference("Controls.Nuggets.CompanyManufacturers.AddEdit.CompanyManufacturers_AddEdit.js");
		}

		protected override void OnLoad(EventArgs e) {
			WireUpControls();
			_autManufacturers.ResultsActionType = Rebound.GlobalTrader.Site.Controls.AutoSearch.Base.AutoSearchResultsActionType.RaiseEvent;
			SetupScriptDescriptors();
			base.OnLoad(e);
		}

		#endregion

		/// <summary>
		/// Wire up controls to the ascx
		/// </summary>
		private void WireUpControls() {
			_autManufacturers = (AutoSearch.Manufacturers)FindFieldControl("ctlManufacturer", "autManufacturers");
		}

		/// <summary>
		/// Setup Script Descriptors
		/// </summary>
		private void SetupScriptDescriptors() {
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.Forms.CompanyManufacturers_AddEdit", ctlDesignBase.ClientID);
			_scScriptControlDescriptor.AddProperty("strTitleAdd", _strTitleAdd);
			_scScriptControlDescriptor.AddProperty("strTitleEdit", _strTitleEdit);
			_scScriptControlDescriptor.AddComponentProperty("autManufacturers", _autManufacturers.ClientID);
		}

	}
}