///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.BOM_Confirm = function(element) {
    Rebound.GlobalTrader.Site.Controls.Forms.BOM_Confirm.initializeBase(this, [element]);
    this._strBOM = "";
    this._blnCOC = false;
    this._selectedUse = "";
    this._selectedUseName = "";
    this._selectedIndex = -1;
    this._IsGroupAssignment = false;
    this._strType = "";
};

Rebound.GlobalTrader.Site.Controls.Forms.BOM_Confirm.prototype = {
    initialize: function() {
        Rebound.GlobalTrader.Site.Controls.Forms.BOM_Confirm.callBaseMethod(this, "initialize");
        this.addShown(Function.createDelegate(this, this.formShown));
    },

    dispose: function() {
        if (this.isDisposed) return;
        if (this._ctlConfirm) this._ctlConfirm.dispose();
        this._ctlConfirm = null;
        this._strBOM = null;
        this._blnCOC = null;
        this._selectedUse = null;
        this._selectedUseName = null;
        Rebound.GlobalTrader.Site.Controls.Forms.BOM_Confirm.callBaseMethod(this, "dispose");
    },

    formShown: function() {
        if (this._blnFirstTimeShown) {
            this._ctlConfirm = this.getFieldComponent("ctlConfirm");
            this._ctlConfirm.addClickYesEvent(Function.createDelegate(this, this.yesClicked));
            this._ctlConfirm.addClickNoEvent(Function.createDelegate(this, this.noClicked));
        }
    },

    yesClicked: function() {
        //alert(this._selectedUse);
       
        if (!this.validateDropDown()) return;
        this.showSaving(true);
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/BOMMainInfo");
        obj.set_DataObject("BOMMainInfo");
        obj.set_DataAction("UpdateBOMByPH");
        obj.addParameter("BOMIds", this._strBOM);
        obj.addParameter("SelectedUser", this._selectedUse);
        obj.addParameter("SelectedUserName", this._selectedUseName);  
        obj.addParameter("IsGroupAssignment", this._IsGroupAssignment);
        obj.addParameter("strType", this._strType);
        obj.addDataOK(Function.createDelegate(this, this.saveComplete));
        obj.addError(Function.createDelegate(this, this.saveError));
        obj.addTimeout(Function.createDelegate(this, this.saveError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
        this.onSave();
    },

    noClicked: function() {
        this.showSaving(false);
        this.onNotConfirmed();
    },

    validateDropDown: function () {
        var status = true;
        if (this._selectedIndex <= 0) {
            status = false;
            alert('Please select user to assign HUBRFQ items.');
            this.noClicked();
        }
        return status;
    },

    saveError: function(args) {
        this.showSaving(false);
        this._strErrorMessage = args._errorMessage;
        this.onSaveError();
    },

    saveComplete: function(args) {
        this.showSaving(false);
        if (args._result.Result >= 1) {
            this.onSaveComplete();
            alert('Selected HUBRFQ has been assigned successfully.');
            //alert($R_RES.HUBRFQAssigned);
            //+ ' ' + args._result.BOM);
            this.getData();

        } else {
            this._strErrorMessage = args._errorMessage;
            this.onSaveError();
        }
    }
};
Rebound.GlobalTrader.Site.Controls.Forms.BOM_Confirm.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.BOM_Confirm", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
