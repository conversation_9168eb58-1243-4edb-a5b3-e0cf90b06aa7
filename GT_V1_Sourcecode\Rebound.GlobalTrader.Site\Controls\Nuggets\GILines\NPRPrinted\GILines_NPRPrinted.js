Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.GILines_NPRPrinted=function(n){Rebound.GlobalTrader.Site.Controls.Forms.GILines_NPRPrinted.initializeBase(this,[n]);this._intLineID=-1};Rebound.GlobalTrader.Site.Controls.Forms.GILines_NPRPrinted.prototype={initialize:function(){Rebound.GlobalTrader.Site.Controls.Forms.GILines_NPRPrinted.callBaseMethod(this,"initialize");this.addShown(Function.createDelegate(this,this.formShown));this.addSave(Function.createDelegate(this,this.saveClicked))},formShown:function(){},dispose:function(){this.isDisposed||(this._intLineID=null,Rebound.GlobalTrader.Site.Controls.Forms.GILines_NPRPrinted.callBaseMethod(this,"dispose"))},saveClicked:function(){var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/GILines");n.set_DataObject("GILines");n.set_DataAction("NPRPrinted");n.addParameter("id",this._intLineID);n.addParameter("NPRPrintStatus",this.getFieldValue("ctlNPRPrinted"));n.addDataOK(Function.createDelegate(this,this.saveEditComplete));n.addError(Function.createDelegate(this,this.saveEditError));n.addTimeout(Function.createDelegate(this,this.saveEditError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},saveEditError:function(n){this._strErrorMessage=n._errorMessage;this.onSaveError()},saveEditComplete:function(n){n._result.Result==!0?this.onSaveComplete():(this._strErrorMessage=n._errorMessage,this.onSaveError())}};Rebound.GlobalTrader.Site.Controls.Forms.GILines_NPRPrinted.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.GILines_NPRPrinted",Rebound.GlobalTrader.Site.Controls.Forms.Base,Sys.IDisposable);