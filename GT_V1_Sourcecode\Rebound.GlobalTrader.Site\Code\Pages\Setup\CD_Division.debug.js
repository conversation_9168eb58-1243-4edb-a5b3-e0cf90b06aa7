///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Pages.Setup");

Rebound.GlobalTrader.Site.Pages.Setup.CD_Division = function(el) { 
	Rebound.GlobalTrader.Site.Pages.Setup.CD_Division.initializeBase(this, [el]);
};

Rebound.GlobalTrader.Site.Pages.Setup.CD_Division.prototype = {

	get_ctlDivision: function() { return this._ctlDivision; }, 	set_ctlDivision: function(v) { if (this._ctlDivision !== v)  this._ctlDivision = v; }, 
	get_ctlDivisionMembers: function() { return this._ctlDivisionMembers; }, 	set_ctlDivisionMembers: function(v) { if (this._ctlDivisionMembers !== v)  this._ctlDivisionMembers = v; }, 
	get_ctlDocHeaderImage: function() { return this._ctlDocHeaderImage; }, 	set_ctlDocHeaderImage: function(v) { if (this._ctlDocHeaderImage !== v)  this._ctlDocHeaderImage = v; }, 

	initialize: function() {
		Rebound.GlobalTrader.Site.Pages.Setup.CD_Division.callBaseMethod(this, "initialize");
	},
	
	goInit: function() {
		this._ctlDivision.addSelectDivision(Function.createDelegate(this, this.ctlDivision_SelectDivision));
		Rebound.GlobalTrader.Site.Pages.Setup.CD_Division.callBaseMethod(this, "goInit");
	},

	dispose: function() { 
		if (this.isDisposed) return;
		if (this._ctlDivision) this._ctlDivision.dispose();
		if (this._ctlDivisionMembers) this._ctlDivisionMembers.dispose();
		if (this._ctlDocHeaderImage) this._ctlDocHeaderImage.dispose();
		this._ctlDivision = null;
		this._ctlDivisionMembers = null;
		this._ctlDocHeaderImage = null;
		Rebound.GlobalTrader.Site.Pages.Setup.CD_Division.callBaseMethod(this, "dispose");
	},
	
	ctlDivision_SelectDivision: function() {
		this._ctlDivisionMembers._intDivisionID = this._ctlDivision._intDivisionID;
		this._ctlDivisionMembers.refresh();
		this._ctlDocHeaderImage._intDivisionID = this._ctlDivision._intDivisionID;
		this._ctlDocHeaderImage.refresh();
		this._ctlDivision._tbl.resizeColumns();
		this.showNuggets(true);
	},
	
	showNuggets: function(bln) {
		this._ctlDivisionMembers.show(bln);
		this._ctlDocHeaderImage.show(bln);
	}
	
};

Rebound.GlobalTrader.Site.Pages.Setup.CD_Division.registerClass("Rebound.GlobalTrader.Site.Pages.Setup.CD_Division", Rebound.GlobalTrader.Site.Pages.Content, Sys.IDisposable);
