﻿using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;

namespace Rebound.GlobalTrader.DAL
{
	public class ClientInvoiceHeaderDetails
	{
		#region Constructors

		public ClientInvoiceHeaderDetails() { }

		#endregion

		#region Properties
		/// <summary>
		/// ClientInvoiceHeaderId
		/// </summary>
		public System.Int32 ClientInvoiceHeaderId { get; set; }


		/// <summary>
		/// ClientInvoiceHeaderName
		/// </summary>
		public System.String ClientInvoiceHeaderName { get; set; }

		/// <summary>
		/// ClientNo
		/// </summary>
		public System.Int32 ClientNo { get; set; }

		/// <summary>
		/// ClientName
		/// </summary>
		public System.String ClientName { get; set; }

		/// <summary>
		/// Notes
		/// </summary>
		public System.String Notes { get; set; }

		/// <summary>
		/// DocumentHeaderImageName
		/// </summary>
		public System.String DocumentHeaderImageName { get; set; }

		/// <summary>
		/// HasDocumentHeaderImage
		/// </summary>
		public System.Boolean HasDocumentHeaderImage { get; set; }

		public System.Boolean Inactive { get; set; }

		/// <summary>
		/// UpdatedBy
		/// </summary>
		public System.Int32? UpdatedBy { get; set; }
		/// <summary>
		/// DLUP
		/// </summary>
		public System.DateTime DLUP { get; set; }
		#endregion
	}
}
