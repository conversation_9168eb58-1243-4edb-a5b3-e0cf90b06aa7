﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

/*
===========================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-202689]		Trung Pham			13-SEP-2024		UPDATE			Get record by inactive param
[US-220887]		An.TranTan			19-NOV-2024		UPDATE			Check if offer added to sourcing result
===========================================================================================
*/
CREATE OR ALTER PROCEDURE [dbo].[usp_ipobom_source_Epo] 
	@ClientId INT
	,@PartSearch NVARCHAR(50)
	,@Index INT = 1
	,@StartDate DATETIME = NULL
	,@FinishDate DATETIME = NULL
	--, @IsPoHUB bit=NULL                                                 
	WITH RECOMPILE
AS
BEGIN
	--DECLARE VARIABLE                                                
	DECLARE @Month INT
	DECLARE @FROMDATE DATETIME
	DECLARE @ENDDATE DATETIME
	DECLARE @OutPutDate DATETIME
	DECLARE @EPO NVARCHAR(50) = 'Strategic Vendor'
	DECLARE @FinishDateVW DATETIME
	DECLARE @FROMDATEVW DATETIME
	DECLARE @ENDDATEVW DATETIME

	SET @Month = 6

	/*                                                
        When we get index 1 then we find the maximum date from matching record                                                
        and decsrease no of month for the start date.                                                
     */
	DECLARE @HUBName NVARCHAR(300)

	SELECT TOP 1 @HUBName = CompanyName
	FROM tbCompany
	WHERE ClientNo = @ClientId
		AND IsPOHub = 1

	IF @Index = 1
	BEGIN
		SELECT @FinishDate = MAX(ISNULL(EpoStatusChangeDate, OriginalEntryDate))
		FROM [BorisGlobalTraderImports].dbo.tbEpo o
		JOIN tbClient cl ON o.ClientNo = cl.ClientId
		WHERE (
				(o.ClientNo = @ClientId)
				OR (
					o.ClientNo <> @ClientId
					-- AND cl.OwnDataVisibleToOthers = 1))                                               
					AND (
						CASE 
							WHEN o.ClientNo = 114
								THEN cast(1 AS BIT)
							ELSE cl.OwnDataVisibleToOthers
							END
						) = 1
					)
				)
			AND FullPart LIKE @PartSearch
			AND ISNULL(o.Inactive, 0) = 0

		SET @FROMDATE = dbo.ufn_get_date_from_datetime(DATEADD(month, - @Month, @FinishDate))
		SET @ENDDATE = dbo.ufn_get_date_from_datetime(@FinishDate)
	END
	ELSE
	BEGIN
		SET @FROMDATE = dbo.ufn_get_date_from_datetime(@StartDate)
		SET @ENDDATE = dbo.ufn_get_date_from_datetime(@FinishDate)
		SET @FROMDATEVW = dbo.ufn_get_date_from_datetime(@StartDate)
		SET @ENDDATEVW = dbo.ufn_get_date_from_datetime(@FinishDate)
	END

	--SET THE OUTPUT DATE                           
	SET @OutPutDate = DATEADD(month, - @Month, @FinishDate)
		--    if @FinishDate IS NULL                          
		-- BEGIN                    
		--  SET @Index=3                          
		-- END                          
		---- If Index value equal to 3 then more than one year data will be pick from archive database.                             
		-- IF @Index = 3                          
		--     BEGIN                     
		;

	WITH cteSearch
	AS (
		SELECT o.EpoId
			,o.FullPart COLLATE DATABASE_DEFAULT AS FullPart
			,o.Part
			,o.ManufacturerNo
			,o.DateCode
			,o.ProductNo
			,o.PackageNo
			,o.Quantity
			,o.Price
			-- ,case when o.ClientNo=114 then 0 else o.Price end AS Price                                                         
			--, o.OriginalEntryDate
			,o.DLUP AS OriginalEntryDate
			,o.Salesman
			,o.SupplierNo
			,o.CurrencyNo
			,o.ROHS
			,o.UpdatedBy
			,o.DLUP
			,o.EpoStatusNo
			,ISNULL(o.EpoStatusChangeDate, o.OriginalEntryDate) AS EpoStatusChangeDate
			,o.EpoStatusChangeLoginNo
			,m.ManufacturerCode COLLATE DATABASE_DEFAULT AS ManufacturerCode
			,p.ProductName COLLATE DATABASE_DEFAULT AS ProductName
			,c.CurrencyCode COLLATE DATABASE_DEFAULT AS CurrencyCode
			,c.CurrencyDescription COLLATE DATABASE_DEFAULT AS CurrencyDescription
			-- , ISNULL(s.CompanyName, o.SupplierName) COLLATE DATABASE_DEFAULT AS SupplierName                                
			--, case when o.ClientNo=114 then @HUBName else  ISNULL(s.CompanyName, o.SupplierName) COLLATE DATABASE_DEFAULT end AS SupplierName                                                   
			--, case when o.ClientNo=114 then ISNULL(s.CompanyName, o.SupplierName) else  ISNULL(s.CompanyName, o.SupplierName) COLLATE DATABASE_DEFAULT end AS SupplierName                                           
			,CASE 
				WHEN o.SupplierNo = 0
					THEN o.SupplierName
				ELSE ISNULL(s.CompanyName, o.SupplierName) COLLATE DATABASE_DEFAULT
				END AS SupplierName
			,ISNULL(m.ManufacturerName, o.ManufacturerName) COLLATE DATABASE_DEFAULT AS ManufacturerName
			,s.EMail COLLATE DATABASE_DEFAULT AS SupplierEmail
			,l.EmployeeName COLLATE DATABASE_DEFAULT AS SalesmanName
			,l2.EmployeeName COLLATE DATABASE_DEFAULT AS EpoStatusChangeEmployeeName
			,g.PackageName COLLATE DATABASE_DEFAULT AS PackageName
			,o.Notes COLLATE DATABASE_DEFAULT AS Notes
			--,(o.LeadTime +' ,'+ convert(nvarchar(25),o. OriginalEntryDate, 121) + CONVERT(varchar(12), 9))  COLLATE DATABASE_DEFAULT AS Notes                                                 
			,o.ClientNo
			,cl.ClientId
			,cl.ClientName COLLATE DATABASE_DEFAULT AS ClientName
			,isnull(cl.OwnDataVisibleToOthers, 0) AS ClientDataVisibleToOthers
			--[001] code start                                                        
			,isnull(cotype.Name, '') COLLATE DATABASE_DEFAULT AS SupplierType
			--[001] code end                    
			,cl.ClientCode COLLATE DATABASE_DEFAULT AS ClientCode
			,o.SPQ COLLATE DATABASE_DEFAULT AS SPQ
			,o.LeadTime COLLATE DATABASE_DEFAULT AS LeadTime
			,o.ROHSStatus COLLATE DATABASE_DEFAULT AS ROHSStatus
			,o.FactorySealed COLLATE DATABASE_DEFAULT AS FactorySealed
			-- , o.MSL                                     
			,ml.MSLLevel COLLATE DATABASE_DEFAULT AS MSL
			,o.IPOBOMNo
			,o.SupplierTotalQSA COLLATE DATABASE_DEFAULT AS SupplierTotalQSA
			,o.SupplierLTB COLLATE DATABASE_DEFAULT AS SupplierLTB
			,o.SupplierMOQ COLLATE DATABASE_DEFAULT AS SupplierMOQ
			,o.UpliftPercentage
			,o.UpliftPrice
			,ishub = 0
			,@EPO AS SupplierEpo
			,o.Description
			,CASE WHEN EXISTS (SELECT TOP 1 1 FROM tbSourcingResult sr WITH(NOLOCK) WHERE sr.SourcingTableItemNo = o.EpoId AND sr.SourcingTable = 'EPPH') THEN CAST(1 AS BIT)
				ELSE CAST(0 AS BIT)
			END AS OfferAddFlag
		FROM [BorisGlobalTraderImports].dbo.tbEpo o
		LEFT JOIN dbo.tbManufacturer m ON o.ManufacturerNo = m.ManufacturerId
		LEFT JOIN dbo.tbProduct p ON o.ProductNo = p.ProductId
		LEFT JOIN dbo.tbCurrency c ON o.CurrencyNo = c.CurrencyId
		LEFT JOIN dbo.tbCompany s ON o.SupplierNo = s.CompanyId
		LEFT JOIN dbo.tbLogin l ON o.Salesman = l.LoginId
		LEFT JOIN dbo.tbLogin l2 ON o.EpoStatusChangeLoginNo = l2.LoginId
		LEFT JOIN dbo.tbPackage g ON o.PackageNo = g.PackageId
		JOIN tbClient cl ON o.ClientNo = cl.ClientId
		--[001] code start                                                        
		LEFT JOIN dbo.tbCompanyType cotype ON s.TypeNo = cotype.CompanyTypeId
		LEFT JOIN tbMSLLevel ml ON o.MSLLevelNo = ml.MSLLevelId
		--[001] code end                                                         
		WHERE (
				(o.ClientNo = @ClientId)
				OR (
					o.ClientNo <> @ClientId
					--AND cl.OwnDataVisibleToOthers = 1))                                   
					AND (
						CASE 
							WHEN o.ClientNo = 114
								THEN cast(1 AS BIT)
							ELSE cl.OwnDataVisibleToOthers
							END
						) = 1
					)
				)
			--   AND ((@IsPoHUB is NULL)                                 
			-- OR (not @IsPoHUB is NULL AND isnull(o.IsPoHub,0)= @IsPoHUB ))                                    
			AND o.FullPart LIKE @PartSearch
			AND (
				dbo.ufn_get_date_from_datetime(ISNULL(o.EpoStatusChangeDate, o.OriginalEntryDate)) BETWEEN @FROMDATE
					AND @ENDDATE
				)
			AND ISNULL(o.Inactive, 0) = 0
			-- ORDER BY ISNULL(o.OfferStatusChangeDate, o.OriginalEntryDate) DESC                                                        
		)
	SELECT *
	FROM cteSearch
	ORDER BY ISNULL(EpoStatusChangeDate, OriginalEntryDate) DESC

	--SELECT THE OUT DATE                              
	SELECT @OutPutDate AS OutPutDate
END
GO


