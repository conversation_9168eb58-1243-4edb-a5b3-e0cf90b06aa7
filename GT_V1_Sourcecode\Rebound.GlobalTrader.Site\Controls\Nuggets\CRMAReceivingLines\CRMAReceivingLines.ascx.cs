using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site.Controls;
using Rebound.GlobalTrader.Site;

namespace Rebound.GlobalTrader.Site.Controls.Nuggets {
	public partial class CRMAReceivingLines : Base {

		#region Locals

		protected IconButton _ibtnReceive;
		protected FlexiDataTable _tblAll;
		protected HyperLink _hypPrev;
		protected HyperLink _hypNext;
		protected Label _lblLineNumber;
		protected Panel _pnlLineDetail;
		protected Panel _pnlLoadingLineDetail;
		protected Panel _pnlLineDetailError;

		#endregion

		#region Properties

		private int _intCRMAID = -1;
		public int CRMAID {
			get { return _intCRMAID; }
			set { _intCRMAID = value; }
		}

        private bool _blnCanReceive = true;
        public bool CanReceive
        {
            get { return _blnCanReceive; }
            set { _blnCanReceive = value; }
        }

        #endregion

		#region Overrides

		/// <summary>
		/// Oninit
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
			base.OnInit(e);
			WireUpControls();
			AddScriptReference("Controls.Nuggets.CRMAReceivingLines.CRMAReceivingLines.js");
			TitleText = Functions.GetGlobalResource("Nuggets", "CRMAReceivingLines");
			if (_objQSManager.CRMAID > 0) _intCRMAID = _objQSManager.CRMAID;
			SetupTables();
		}
		protected override void OnLoad(EventArgs e) {
			SetupScriptDescriptors();
            base.OnLoad(e);
		}

		/// <summary>
		/// OnPreRender
		/// </summary>
		/// <param name="e"></param>
		protected override void OnPreRender(EventArgs e) {
            _ibtnReceive.Visible = _blnCanReceive;
            base.OnPreRender(e);
		}

		#endregion

		/// <summary>
		/// Sets up AJAX script descriptors
		/// </summary>
		private void SetupScriptDescriptors() {
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.Nuggets.CRMAReceivingLines", ctlDesignBase.ClientID);
            if (_blnCanReceive) _scScriptControlDescriptor.AddElementProperty("ibtnReceive", _ibtnReceive.ClientID);
            _scScriptControlDescriptor.AddProperty("intCRMAID", _intCRMAID);
			_scScriptControlDescriptor.AddComponentProperty("tblAll", _tblAll.ClientID);
			_scScriptControlDescriptor.AddElementProperty("hypPrev", _hypPrev.ClientID);
			_scScriptControlDescriptor.AddElementProperty("hypNext", _hypNext.ClientID);
			_scScriptControlDescriptor.AddElementProperty("lblLineNumber", _lblLineNumber.ClientID);
			_scScriptControlDescriptor.AddElementProperty("pnlLineDetail", _pnlLineDetail.ClientID);
			_scScriptControlDescriptor.AddElementProperty("pnlLoadingLineDetail", _pnlLoadingLineDetail.ClientID);
			_scScriptControlDescriptor.AddElementProperty("pnlLineDetailError", _pnlLineDetailError.ClientID);
		}

		private void SetupTables() {
			_tblAll.Columns.Add(new FlexiDataColumn("PartNo", "CustomerPart", WidthManager.GetWidth(WidthManager.ColumnWidth.PartNo)));
			_tblAll.Columns.Add(new FlexiDataColumn("ManufacturerAbbreviation", "DateCode", WidthManager.GetWidth(WidthManager.ColumnWidth.ManufacturerCode)));
			_tblAll.Columns.Add(new FlexiDataColumn("Product", "Package", WidthManager.GetWidth(WidthManager.ColumnWidth.Product)));
			_tblAll.Columns.Add(new FlexiDataColumn("QuantityAuthorised", "QuantityReceived", WidthManager.GetWidth(WidthManager.ColumnWidth.Quantity)));
			_tblAll.Columns.Add(new FlexiDataColumn("QuantityOutstanding", WidthManager.GetWidth(WidthManager.ColumnWidth.Quantity)));
			_tblAll.Columns.Add(new FlexiDataColumn("ReturnDate", "Reason"));
		}

		/// <summary>
		/// Wire up controls from the ascx
		/// </summary>
		private void WireUpControls() {
			_ibtnReceive = FindIconButton("ibtnReceive");
			_tblAll = (FlexiDataTable)Functions.FindControlRecursive(this, "tblAll");
			_hypPrev = (HyperLink)Functions.FindControlRecursive(this, "hypPrev");
			_hypNext = (HyperLink)Functions.FindControlRecursive(this, "hypNext");
			_lblLineNumber = (Label)Functions.FindControlRecursive(this, "lblLineNumber");
			_pnlLineDetail = (Panel)Functions.FindControlRecursive(this, "pnlLineDetail");
			_pnlLoadingLineDetail = (Panel)Functions.FindControlRecursive(this, "pnlLoadingLineDetail");
			_pnlLineDetailError = (Panel)Functions.FindControlRecursive(this, "pnlLineDetailError");
		}
	}
}
