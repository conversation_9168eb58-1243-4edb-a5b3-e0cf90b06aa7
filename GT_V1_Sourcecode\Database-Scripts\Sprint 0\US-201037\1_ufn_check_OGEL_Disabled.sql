SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

IF object_id('ufn_check_OGEL_Disabled', 'FN') IS NOT NULL
BEGIN
    DROP FUNCTION [dbo].[ufn_check_OGEL_Disabled]
END
GO
/*
-- ==========================================================================================
-- TASK      		UPDATED BY     DATE         ACTION 			DESCRIPTION                                    
-- US-201037 		An.TranTan     22-04-2024   CREATE			Get addition information of sourcing results
-- ==========================================================================================
*/
CREATE FUNCTION dbo.ufn_check_OGEL_Disabled
(
	@SalesOrderId INT
)
RETURNS BIT
AS
BEGIN
	DECLARE @IsDisabled BIT = 0;
	IF EXISTS(
		SELECT TOP 1 1 
		FROM dbo.tbInvoicelineAllocation ila
		JOIN dbo.tbSalesOrderLine sol ON sol.SalesOrderLineId = ila.SalesOrderLineNo
		JOIN dbo.tbSalesOrder so ON  so.SalesOrderId  = sol.SalesOrderNo
		WHERE 
			so.SalesOrderId = @SalesOrderId
	        AND ila.Quantity > 0
	)
	BEGIN
		SET @IsDisabled = 1;
	END

	RETURN @IsDisabled
END
GO

