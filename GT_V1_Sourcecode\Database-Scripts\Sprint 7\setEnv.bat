echo off

 if %DB_SCRIPT_PATH%==() (
    echo.
    echo ***Please enter DB_SCRIPT_PATH: 
  ) else (
    echo.
    echo ***Current DB_SCRIPT_PATH =%DB_SCRIPT_PATH%
    echo ***Enter new DB_SCRIPT_PATH, 
    echo ***Press "<ENTER>" to retain the Current value :
  )
  SET /P DB_SCRIPT_PATH=

  if (%SERVER_NAME%)==() (
    echo.
    echo ***Please enter SERVER_NAME : 
  ) else (
    echo.
    echo ***Current SERVER_NAME =%SERVER_NAME%
    echo ***Enter new SERVER_NAME, 
    echo ***Press "<ENTER>" to retain the Current value :
  )
  SET /P SERVER_NAME=

  if (%DATABASE_NAME%)==() (
    echo.
    echo ***Please enter DATABASE_NAME : 
  ) else (
    echo.
    echo ***Current DATABASE_NAME =%DATABASE_NAME%
    echo ***Enter new DATABASE_NAME, 
    echo ***Press "<ENTER>" to retain the Current value :
  )
  SET /P DATABASE_NAME=

  if (%USER_ID%)==() (
    echo.
    echo ***Please enter USER_ID : 
  ) else (
    echo.
    echo ***Current USER_ID =%USER_ID%
    echo ***Enter new USER_ID, 
    echo ***Press "<ENTER>" to retain the Current value :
  )
  SET /P USER_ID=

  if (%PASSWORD%)==() (
    echo.
    echo ***Please enter PASSWORD : 
  ) else (
    echo.
    echo ***Current PASSWORD =%PASSWORD%
    echo ***Enter new PASSWORD, 
    echo ***Press "<ENTER>" to retain the Current value :
  )
  SET /P PASSWORD=
