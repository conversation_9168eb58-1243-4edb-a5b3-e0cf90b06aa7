<%@ Control Language="C#" CodeBehind="DebitAdd.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Nuggets.DebitAdd" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Nugget:DesignBase ID="ctlDB" runat="server" BoxType="Standard">
	<Links>
		<ReboundUI:IconButton ID="ibtnAdd" runat="server" IconGroup="Nugget" IconTitleResource="Add" IconCSSType="Add" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
	</Links>
	
	<Content>
		<asp:PlaceHolder ID="plhAddNotAllowed" runat="server" Visible="false">
			<div class="addNotAllowed"><%=Functions.GetGlobalResource("Messages", "AddNotAllowed_Debit")%></div>
		</asp:PlaceHolder>
	</Content>

	<Forms>
		<ReboundForm:DebitAdd_Add ID="ctlAdd" runat="server" />
	</Forms>
</ReboundUI_Nugget:DesignBase>
