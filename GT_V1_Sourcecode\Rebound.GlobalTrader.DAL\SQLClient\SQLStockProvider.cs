﻿/* Marker     changed by      date         Remarks
 [001]      <PERSON><PERSON><PERSON> kumar     17/11/2011  ESMS Ref:23 - PO No. and Crma No. should alos be displayed 
 [002]      Vinay           07/05/2012   This need to upload pdf document for stock section
 [003]      Vinay           01/08/2012   Delete UnAllocated Stock Bug
 [004]      Vinay           15/10/2012   Display company type in stock grid
 [005]      Vinay           08/04/2014   CR:- Stock Provision
 [006]      Vinay           30/07/2015   ESMS Ticket No: 255
 [007]      Suhail          01/05/2018   Adding MSL 
 [008]      <PERSON>     28/10/2020   Adding CurrencyNo 
 [009]      Anand <PERSON>     17/08/2021   (GTDP-198) Controlling specific product groups for purchasing
 [010]      Abhinav <PERSON>a  17/08/2021   Change connection timeout.
 [011]      <PERSON><PERSON><PERSON>av <PERSON>  17/10/2022   (RP-31) Add new methods for this lot chnages.
 [012]      <PERSON>     21/02/2023   (RP-31) Ticket No: 217.
[011]      Soorya Vyas     20/03/2023    RP-1019 Win32Exception excecution TimeOut issue
[012]       Ravi            13/09/2023   RP-2340 AS6081
[013]       Ravi            19-09-2023      RP-2338  AS6081 Search/Filter functionality on different pages
 */

using System;
using System.Data;
using System.Data.SqlClient;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;
using System.Web.Caching;
using System.Data.Common;
using System.Web.Hosting;
using System.Linq;

namespace Rebound.GlobalTrader.DAL.SqlClient
{
    public class SqlStockProvider : StockProvider
    {
        /// <summary>
        /// AutoSearch 
        /// Calls [usp_autosearch_Stock]
        /// </summary>
        public override List<StockDetails> AutoSearch(System.Int32? clientId, System.String nameSearch)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_autosearch_Stock", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@NameSearch", SqlDbType.NVarChar).Value = nameSearch;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<StockDetails> lst = new List<StockDetails>();
                while (reader.Read())
                {
                    StockDetails obj = new StockDetails();
                    obj.StockId = GetReaderValue_Int32(reader, "StockId", 0);
                    obj.Part = GetReaderValue_String(reader, "Part", "");
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Stocks", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        /// <summary>
        /// Count Stock
        /// Calls [usp_count_Stock_for_Client]
        /// </summary>
        public override Int32 CountForClient(System.Int32? clientId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_count_Stock_for_Client", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cn.Open();
                return (Int32)ExecuteScalar(cmd);
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to count Stock", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        /// <summary>
        /// DataListNugget 
        /// Calls [usp_datalistnugget_Stock]
        /// </summary>
        public override List<StockDetails> DataListNugget(System.Int32? clientId, System.Int32? orderBy, System.Int32? sortDir, System.Int32? pageIndex, System.Int32? pageSize, System.Boolean? quarantine, System.String partSearch, System.Int32? lotNo, System.Int32? purchaseOrderNoLo, System.Int32? purchaseOrderNoHi, System.String supplierPartSearch, System.String supplierNameSearch, System.String locationSearch, System.Int32? warehouseNo, System.Boolean? recentOnly, System.Int32? customerRmaNoLo, System.Int32? customerRmaNoHi, System.Boolean? includeZeroStock, System.Int32? clientSearch, int? IsPoHub, Boolean IsGlobalLogin, System.Int32? stockNoLo, System.Int32? stockNoHi, System.Boolean? AS6081)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_datalistnugget_Stock", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@OrderBy", SqlDbType.Int).Value = orderBy;
                cmd.Parameters.Add("@SortDir", SqlDbType.Int).Value = sortDir;
                cmd.Parameters.Add("@PageIndex", SqlDbType.Int).Value = pageIndex;
                cmd.Parameters.Add("@PageSize", SqlDbType.Int).Value = pageSize;
                cmd.Parameters.Add("@Quarantine", SqlDbType.Bit).Value = quarantine;
                cmd.Parameters.Add("@PartSearch", SqlDbType.NVarChar).Value = partSearch;
                cmd.Parameters.Add("@LotNo", SqlDbType.Int).Value = lotNo;
                cmd.Parameters.Add("@PurchaseOrderNoLo", SqlDbType.Int).Value = purchaseOrderNoLo;
                cmd.Parameters.Add("@PurchaseOrderNoHi", SqlDbType.Int).Value = purchaseOrderNoHi;
                cmd.Parameters.Add("@SupplierPartSearch", SqlDbType.NVarChar).Value = supplierPartSearch;
                cmd.Parameters.Add("@SupplierNameSearch", SqlDbType.NVarChar).Value = supplierNameSearch;
                cmd.Parameters.Add("@LocationSearch", SqlDbType.NVarChar).Value = locationSearch;
                cmd.Parameters.Add("@WarehouseNo", SqlDbType.Int).Value = warehouseNo;
                cmd.Parameters.Add("@RecentOnly", SqlDbType.Bit).Value = recentOnly;
                cmd.Parameters.Add("@CustomerRMANoLo", SqlDbType.Int).Value = customerRmaNoLo;
                cmd.Parameters.Add("@CustomerRMANoHi", SqlDbType.Int).Value = customerRmaNoHi;
                cmd.Parameters.Add("@IncludeZeroStock", SqlDbType.Bit).Value = includeZeroStock;
                cmd.Parameters.Add("@ClientSearch", SqlDbType.Int).Value = clientSearch;
                cmd.Parameters.Add("@IsPoHub", SqlDbType.Int).Value = IsPoHub ?? 0;
                cmd.Parameters.Add("@IsGlobalLogin", SqlDbType.Bit).Value = IsGlobalLogin;
                cmd.Parameters.Add("@StockNoLo", SqlDbType.Int).Value = stockNoLo;
                cmd.Parameters.Add("@StockNoHi", SqlDbType.Int).Value = stockNoHi;
                cmd.Parameters.Add("@AS6081", SqlDbType.Bit).Value = AS6081; //[013]
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<StockDetails> lst = new List<StockDetails>();
                while (reader.Read())
                {
                    StockDetails obj = new StockDetails();
                    obj.StockId = GetReaderValue_Int32(reader, "StockId", 0);
                    obj.Part = GetReaderValue_String(reader, "Part", "");
                    obj.ManufacturerNo = GetReaderValue_NullableInt32(reader, "ManufacturerNo", null);
                    obj.WarehouseNo = GetReaderValue_NullableInt32(reader, "WarehouseNo", null);
                    obj.QuantityInStock = GetReaderValue_Int32(reader, "QuantityInStock", 0);
                    obj.QuantityOnOrder = GetReaderValue_Int32(reader, "QuantityOnOrder", 0);
                    obj.Location = GetReaderValue_String(reader, "Location", "");
                    obj.LotNo = GetReaderValue_NullableInt32(reader, "LotNo", null);
                    obj.SupplierPart = GetReaderValue_String(reader, "SupplierPart", "");
                    obj.ROHS = GetReaderValue_Byte(reader, "ROHS", (byte)0);
                    obj.ManufacturerCode = GetReaderValue_String(reader, "ManufacturerCode", "");
                    obj.QuantityAllocated = GetReaderValue_Int32(reader, "QuantityAllocated", 0);
                    obj.WarehouseName = GetReaderValue_String(reader, "WarehouseName", "");
                    obj.LotName = GetReaderValue_String(reader, "LotName", "");
                    obj.SupplierNo = GetReaderValue_NullableInt32(reader, "SupplierNo", null);
                    obj.SupplierName = GetReaderValue_String(reader, "SupplierName", "");
                    obj.QuantityAvailable = GetReaderValue_NullableInt32(reader, "QuantityAvailable", null);
                    obj.StatusNo = GetReaderValue_NullableInt32(reader, "StatusNo", null);
                    obj.RowCnt = GetReaderValue_NullableInt32(reader, "RowCnt", null);
                    //[001]Code Start
                    obj.PurchaseOrderNumber = GetReaderValue_NullableInt32(reader, "PurchaseOrderNumber", null);
                    obj.PurchaseOrderNo = GetReaderValue_NullableInt32(reader, "PurchaseOrderNo", null);
                    obj.CustomerRMANumber = GetReaderValue_NullableInt32(reader, "CustomerRMANumber", null);
                    obj.CustomerRMANo = GetReaderValue_NullableInt32(reader, "CustomerRMANo", null);
                    obj.ClientName = GetReaderValue_String(reader, "ClientName", null);
                    // obj.SupplierMessage = GetReaderValue_String(reader, "SupplierMessage", "");
                    //obj.ClientNo = GetReaderValue_NullableInt32(reader, "ClientNo", null)??0;
                    //[001]Code End
                    obj.AS6081 = GetReaderValue_Boolean(reader, "AS6081", false); //[013]
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Stocks", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        /// <summary>
        /// Delete Stock
        /// Calls [usp_delete_Stock]
        /// </summary>
        public override bool Delete(System.Int32? stockId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_delete_Stock", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@StockId", SqlDbType.Int).Value = stockId;
                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to delete Stock", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        /// <summary>
        /// Delete Stock
        /// Calls [usp_delete_Stock_Unallocated_for_Lot]
        /// </summary>
        public override bool DeleteUnallocatedForLot(System.Int32? lotNo)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_delete_Stock_Unallocated_for_Lot", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@LotNo", SqlDbType.Int).Value = lotNo;
                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (ret >= 0);
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to delete Stock", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }




        /// <summary>
        /// Create a new row
        /// Calls [usp_insert_Stock]
        /// </summary>
        public override Int32 Insert(System.String part, System.Int32? manufacturerNo, System.String dateCode, System.Int32? packageNo, System.Int32? warehouseNo, System.Int32? clientNo, System.String qualityControlNotes, System.Int32? purchaseOrderNo, System.Int32? purchaseOrderLineNo, System.Int32? customerRmaNo, System.Int32? customerRmaLineNo, System.Int32? quantityInStock, System.Int32? quantityOnOrder, System.Int32? productNo, System.Double? resalePrice, System.Boolean? unavailable, System.Int32? lotNo, System.Double? landedCost, System.String supplierPart, System.Byte? rohs, System.Int32? packageUnit, System.Int32? stockKeepingUnit, System.String stockLogChangeNotes, System.String location, System.Int32? countryOfManufacture, System.String partMarkings, System.Int32? countingMethodNo, System.Int32? updatedBy, System.Int32? divisionNo, System.String mslLevel, System.Boolean AS6081, System.Double? ClientUPLiftPrice)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_insert_Stock", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@Part", SqlDbType.NVarChar).Value = part;
                cmd.Parameters.Add("@ManufacturerNo", SqlDbType.Int).Value = manufacturerNo;
                cmd.Parameters.Add("@DateCode", SqlDbType.NVarChar).Value = dateCode;
                cmd.Parameters.Add("@PackageNo", SqlDbType.Int).Value = packageNo;
                cmd.Parameters.Add("@WarehouseNo", SqlDbType.Int).Value = warehouseNo;
                cmd.Parameters.Add("@ClientNo", SqlDbType.Int).Value = clientNo;
                cmd.Parameters.Add("@QualityControlNotes", SqlDbType.NVarChar).Value = qualityControlNotes;
                cmd.Parameters.Add("@PurchaseOrderNo", SqlDbType.Int).Value = purchaseOrderNo;
                cmd.Parameters.Add("@PurchaseOrderLineNo", SqlDbType.Int).Value = purchaseOrderLineNo;
                cmd.Parameters.Add("@CustomerRMANo", SqlDbType.Int).Value = customerRmaNo;
                cmd.Parameters.Add("@CustomerRMALineNo", SqlDbType.Int).Value = customerRmaLineNo;
                cmd.Parameters.Add("@QuantityInStock", SqlDbType.Int).Value = quantityInStock;
                cmd.Parameters.Add("@QuantityOnOrder", SqlDbType.Int).Value = quantityOnOrder;
                cmd.Parameters.Add("@ProductNo", SqlDbType.Int).Value = productNo;
                cmd.Parameters.Add("@ResalePrice", SqlDbType.Float).Value = resalePrice;
                cmd.Parameters.Add("@Unavailable", SqlDbType.Bit).Value = unavailable;
                cmd.Parameters.Add("@LotNo", SqlDbType.Int).Value = lotNo;
                cmd.Parameters.Add("@LandedCost", SqlDbType.Float).Value = landedCost;
                cmd.Parameters.Add("@SupplierPart", SqlDbType.NVarChar).Value = supplierPart;
                cmd.Parameters.Add("@ROHS", SqlDbType.TinyInt).Value = rohs;
                cmd.Parameters.Add("@PackageUnit", SqlDbType.Int).Value = packageUnit;
                cmd.Parameters.Add("@StockKeepingUnit", SqlDbType.Int).Value = stockKeepingUnit;
                cmd.Parameters.Add("@StockLogChangeNotes", SqlDbType.NVarChar).Value = stockLogChangeNotes;
                cmd.Parameters.Add("@Location", SqlDbType.NVarChar).Value = location;
                cmd.Parameters.Add("@CountryOfManufacture", SqlDbType.Int).Value = countryOfManufacture;
                cmd.Parameters.Add("@PartMarkings", SqlDbType.NVarChar).Value = partMarkings;
                cmd.Parameters.Add("@CountingMethodNo", SqlDbType.Int).Value = countingMethodNo;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                //[006] code start
                cmd.Parameters.Add("@DivisionNo", SqlDbType.Int).Value = divisionNo;
                //[006] code end
                cmd.Parameters.Add("@StockId", SqlDbType.Int).Direction = ParameterDirection.Output;
                cmd.Parameters.Add("@MSLLevel", SqlDbType.NVarChar).Value = mslLevel;
                cmd.Parameters.Add("@AS6081", SqlDbType.Bit).Value = AS6081;
                cmd.Parameters.Add("@ClientUPLiftPrice", SqlDbType.Float).Value = ClientUPLiftPrice;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (Int32)cmd.Parameters["@StockId"].Value;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                LogExceptionAzure(sqlex);
                throw new Exception("Failed to insert Stock", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        /// <summary>
        /// Create a new row
        /// Calls [usp_insert_Stock_Identity_Off]
        /// </summary>
        public override Int32 InsertIdentityOff(System.Int32? stockId, System.String part, System.Int32? manufacturerNo, System.String dateCode, System.Int32? packageNo, System.Int32? warehouseNo, System.Int32? clientNo, System.String qualityControlNotes, System.Int32? purchaseOrderNo, System.Int32? purchaseOrderLineNo, System.Int32? customerRmaNo, System.Int32? customerRmaLineNo, System.Int32? goodsInLineNo, System.Int32? quantityInStock, System.Int32? quantityOnOrder, System.Int32? productNo, System.Double? resalePrice, System.Boolean? unavailable, System.Int32? lotNo, System.Double? landedCost, System.String supplierPart, System.Byte? rohs, System.Int32? packageUnit, System.Int32? stockKeepingUnit, System.String stockLogChangeNotes, System.String location, System.Int32? updatedBy)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_insert_Stock_Identity_Off", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@StockId", SqlDbType.Int).Value = stockId;
                cmd.Parameters.Add("@Part", SqlDbType.NVarChar).Value = part;
                cmd.Parameters.Add("@ManufacturerNo", SqlDbType.Int).Value = manufacturerNo;
                cmd.Parameters.Add("@DateCode", SqlDbType.NVarChar).Value = dateCode;
                cmd.Parameters.Add("@PackageNo", SqlDbType.Int).Value = packageNo;
                cmd.Parameters.Add("@WarehouseNo", SqlDbType.Int).Value = warehouseNo;
                cmd.Parameters.Add("@ClientNo", SqlDbType.Int).Value = clientNo;
                cmd.Parameters.Add("@QualityControlNotes", SqlDbType.NVarChar).Value = qualityControlNotes;
                cmd.Parameters.Add("@PurchaseOrderNo", SqlDbType.Int).Value = purchaseOrderNo;
                cmd.Parameters.Add("@PurchaseOrderLineNo", SqlDbType.Int).Value = purchaseOrderLineNo;
                cmd.Parameters.Add("@CustomerRMANo", SqlDbType.Int).Value = customerRmaNo;
                cmd.Parameters.Add("@CustomerRMALineNo", SqlDbType.Int).Value = customerRmaLineNo;
                cmd.Parameters.Add("@GoodsInLineNo", SqlDbType.Int).Value = goodsInLineNo;
                cmd.Parameters.Add("@QuantityInStock", SqlDbType.Int).Value = quantityInStock;
                cmd.Parameters.Add("@QuantityOnOrder", SqlDbType.Int).Value = quantityOnOrder;
                cmd.Parameters.Add("@ProductNo", SqlDbType.Int).Value = productNo;
                cmd.Parameters.Add("@ResalePrice", SqlDbType.Float).Value = resalePrice;
                cmd.Parameters.Add("@Unavailable", SqlDbType.Bit).Value = unavailable;
                cmd.Parameters.Add("@LotNo", SqlDbType.Int).Value = lotNo;
                cmd.Parameters.Add("@LandedCost", SqlDbType.Float).Value = landedCost;
                cmd.Parameters.Add("@SupplierPart", SqlDbType.NVarChar).Value = supplierPart;
                cmd.Parameters.Add("@ROHS", SqlDbType.TinyInt).Value = rohs;
                cmd.Parameters.Add("@PackageUnit", SqlDbType.Int).Value = packageUnit;
                cmd.Parameters.Add("@StockKeepingUnit", SqlDbType.Int).Value = stockKeepingUnit;
                cmd.Parameters.Add("@StockLogChangeNotes", SqlDbType.NVarChar).Value = stockLogChangeNotes;
                cmd.Parameters.Add("@Location", SqlDbType.NVarChar).Value = location;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (Int32)cmd.Parameters["@RowsAffected"].Value;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to insert Stock", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        /// <summary>
        /// Create a new row
        /// Calls [usp_insert_Stock_Split]
        /// </summary>
        public override Int32 InsertSplit(System.Int32? stockId, System.Int32? quantitySplit, System.String location, System.Int32? updatedBy)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_insert_Stock_Split", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@StockId", SqlDbType.Int).Value = stockId;
                cmd.Parameters.Add("@QuantitySplit", SqlDbType.Int).Value = quantitySplit;
                cmd.Parameters.Add("@Location", SqlDbType.NVarChar).Value = location;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@NewStockId", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (Int32)cmd.Parameters["@NewStockId"].Value;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to insert Stock", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        /// <summary>
        /// ItemSearch 
        /// Calls [usp_itemsearch_Stock]
        /// </summary>
        public override List<StockDetails> ItemSearch(System.Int32? clientId, System.Int32? orderBy, System.Int32? sortDir, System.Int32? pageIndex, System.Int32? pageSize, System.String partSearch, System.Boolean? forRmAs, System.Int32? supplierRmaNo, System.Boolean? includeQuarantined, System.Boolean? includeLotsOnHold, System.Int32? poNoLo, System.Int32? poNoHi, System.Int32? crmaNoLo, System.Int32? crmaNoHi, System.Int32? warehouseNo, System.String location, System.Int32? incLockCustNo)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_itemsearch_Stock", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@OrderBy", SqlDbType.Int).Value = orderBy;
                cmd.Parameters.Add("@SortDir", SqlDbType.Int).Value = sortDir;
                cmd.Parameters.Add("@PageIndex", SqlDbType.Int).Value = pageIndex;
                cmd.Parameters.Add("@PageSize", SqlDbType.Int).Value = pageSize;
                cmd.Parameters.Add("@PartSearch", SqlDbType.NVarChar).Value = partSearch;
                cmd.Parameters.Add("@ForRMAs", SqlDbType.Bit).Value = forRmAs;
                cmd.Parameters.Add("@SupplierRMANo", SqlDbType.Int).Value = supplierRmaNo;
                cmd.Parameters.Add("@IncludeQuarantined", SqlDbType.Bit).Value = includeQuarantined;
                cmd.Parameters.Add("@IncludeLotsOnHold", SqlDbType.Bit).Value = includeLotsOnHold;
                cmd.Parameters.Add("@PONoLo", SqlDbType.Int).Value = poNoLo;
                cmd.Parameters.Add("@PONoHi", SqlDbType.Int).Value = poNoHi;
                cmd.Parameters.Add("@CRMANoLo", SqlDbType.Int).Value = crmaNoLo;
                cmd.Parameters.Add("@CRMANoHi", SqlDbType.Int).Value = crmaNoHi;
                cmd.Parameters.Add("@WarehouseNo", SqlDbType.Int).Value = warehouseNo;
                cmd.Parameters.Add("@Location", SqlDbType.NVarChar).Value = location;
                cmd.Parameters.Add("@IncludeLockLotCustNo", SqlDbType.Int).Value = incLockCustNo;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<StockDetails> lst = new List<StockDetails>();
                while (reader.Read())
                {
                    StockDetails obj = new StockDetails();
                    obj.StockId = GetReaderValue_Int32(reader, "StockId", 0);
                    obj.Part = GetReaderValue_String(reader, "Part", "");
                    obj.ClientNo = GetReaderValue_Int32(reader, "ClientNo", 0);
                    obj.QuantityInStock = GetReaderValue_Int32(reader, "QuantityInStock", 0);
                    obj.QuantityOnOrder = GetReaderValue_Int32(reader, "QuantityOnOrder", 0);
                    obj.Location = GetReaderValue_String(reader, "Location", "");
                    obj.Unavailable = GetReaderValue_Boolean(reader, "Unavailable", false);
                    obj.LandedCost = GetReaderValue_NullableDouble(reader, "LandedCost", null);
                    obj.SupplierPart = GetReaderValue_String(reader, "SupplierPart", "");
                    obj.QuantityAllocated = GetReaderValue_Int32(reader, "QuantityAllocated", 0);
                    obj.WarehouseName = GetReaderValue_String(reader, "WarehouseName", "");
                    obj.SupplierName = GetReaderValue_String(reader, "SupplierName", "");
                    obj.QuantityAvailable = GetReaderValue_NullableInt32(reader, "QuantityAvailable", null);
                    obj.RowCnt = GetReaderValue_NullableInt32(reader, "RowCnt", null);
                    obj.PODeliveryDate = GetReaderValue_NullableDateTime(reader, "PODeliveryDate", null);
                    obj.PurchaseOrderNumber = GetReaderValue_NullableInt32(reader, "PurchaseOrderNumber", null);
                    obj.CustomerRMANumber = GetReaderValue_NullableInt32(reader, "CustomerRMANumber", null);
                    obj.CustomerRMADate = GetReaderValue_NullableDateTime(reader, "CustomerRMADate", null);
                    obj.POSerialNo = GetReaderValue_Int16(reader, "POSerialNo", 0);
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Stocks", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        /// <summary>
        /// ItemSearch 
        /// Calls [usp_itemsearch_IpoStock]
        /// </summary>
        public override List<StockDetails> IpoItemSearch(System.Int32? clientId, System.Int32? orderBy, System.Int32? sortDir, System.Int32? pageIndex, System.Int32? pageSize, System.String partSearch, System.Boolean? forRmAs, System.Int32? supplierRmaNo, System.Boolean? includeQuarantined, System.Boolean? includeLotsOnHold, System.Int32? poNoLo, System.Int32? poNoHi, System.Int32? crmaNoLo, System.Int32? crmaNoHi, System.Int32? warehouseNo, System.String location, System.Int32? incLockCustNo, int? salesOrderNo, System.Boolean? stopNONIpoStock)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_itemsearch_IpoStock", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@OrderBy", SqlDbType.Int).Value = orderBy;
                cmd.Parameters.Add("@SortDir", SqlDbType.Int).Value = sortDir;
                cmd.Parameters.Add("@PageIndex", SqlDbType.Int).Value = pageIndex;
                cmd.Parameters.Add("@PageSize", SqlDbType.Int).Value = pageSize;
                cmd.Parameters.Add("@PartSearch", SqlDbType.NVarChar).Value = partSearch;
                cmd.Parameters.Add("@ForRMAs", SqlDbType.Bit).Value = forRmAs;
                cmd.Parameters.Add("@SupplierRMANo", SqlDbType.Int).Value = supplierRmaNo;
                cmd.Parameters.Add("@IncludeQuarantined", SqlDbType.Bit).Value = includeQuarantined;
                cmd.Parameters.Add("@IncludeLotsOnHold", SqlDbType.Bit).Value = includeLotsOnHold;
                cmd.Parameters.Add("@IPONo", SqlDbType.Int).Value = poNoLo ?? 0;
                cmd.Parameters.Add("@CRMANoLo", SqlDbType.Int).Value = crmaNoLo;
                cmd.Parameters.Add("@CRMANoHi", SqlDbType.Int).Value = crmaNoHi;
                cmd.Parameters.Add("@WarehouseNo", SqlDbType.Int).Value = warehouseNo;
                cmd.Parameters.Add("@Location", SqlDbType.NVarChar).Value = location;
                cmd.Parameters.Add("@IncludeLockLotCustNo", SqlDbType.Int).Value = incLockCustNo;
                cmd.Parameters.Add("@SalesOrderNo", SqlDbType.Int).Value = salesOrderNo ?? 0;
                cmd.Parameters.Add("@StopNonIPOStock", SqlDbType.Bit).Value = stopNONIpoStock;

                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<StockDetails> lst = new List<StockDetails>();
                while (reader.Read())
                {
                    StockDetails obj = new StockDetails();
                    obj.StockId = GetReaderValue_Int32(reader, "StockId", 0);
                    obj.Part = GetReaderValue_String(reader, "Part", "");
                    obj.ClientNo = GetReaderValue_Int32(reader, "ClientNo", 0);
                    obj.QuantityInStock = GetReaderValue_Int32(reader, "QuantityInStock", 0);
                    obj.QuantityOnOrder = GetReaderValue_Int32(reader, "QuantityOnOrder", 0);
                    obj.Location = GetReaderValue_String(reader, "Location", "");
                    obj.Unavailable = GetReaderValue_Boolean(reader, "Unavailable", false);
                    obj.LandedCost = GetReaderValue_NullableDouble(reader, "LandedCost", null);
                    obj.SupplierPart = GetReaderValue_String(reader, "SupplierPart", "");
                    obj.QuantityAllocated = GetReaderValue_Int32(reader, "QuantityAllocated", 0);
                    obj.WarehouseName = GetReaderValue_String(reader, "WarehouseName", "");
                    obj.SupplierName = GetReaderValue_String(reader, "SupplierName", "");
                    obj.QuantityAvailable = GetReaderValue_NullableInt32(reader, "QuantityAvailable", null);
                    obj.RowCnt = GetReaderValue_NullableInt32(reader, "RowCnt", null);
                    obj.PODeliveryDate = GetReaderValue_NullableDateTime(reader, "PODeliveryDate", null);
                    obj.PurchaseOrderNumber = GetReaderValue_NullableInt32(reader, "PurchaseOrderNumber", null);
                    obj.CustomerRMANumber = GetReaderValue_NullableInt32(reader, "CustomerRMANumber", null);
                    obj.CustomerRMADate = GetReaderValue_NullableDateTime(reader, "CustomerRMADate", null);
                    obj.POSerialNo = GetReaderValue_Int16(reader, "POSerialNo", 0);
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Stocks", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }



        /// <summary>
        /// Get 
        /// Calls [usp_select_Stock]
        /// </summary>
        public override StockDetails Get(System.Int32? stockId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_select_Stock", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@StockId", SqlDbType.Int).Value = stockId;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd, CommandBehavior.SingleRow);
                if (reader.Read())
                {
                    //return GetStockFromReader(reader);
                    StockDetails obj = new StockDetails();



                    obj.StockId = GetReaderValue_Int32(reader, "StockId", 0);
                    obj.FullPart = GetReaderValue_String(reader, "FullPart", "");
                    obj.Part = GetReaderValue_String(reader, "Part", "");
                    obj.ManufacturerNo = GetReaderValue_NullableInt32(reader, "ManufacturerNo", null);
                    obj.DateCode = GetReaderValue_String(reader, "DateCode", "");
                    obj.PackageNo = GetReaderValue_NullableInt32(reader, "PackageNo", null);
                    obj.WarehouseNo = GetReaderValue_NullableInt32(reader, "WarehouseNo", null);
                    obj.ClientNo = GetReaderValue_Int32(reader, "ClientNo", 0);
                    obj.QualityControlNotes = GetReaderValue_String(reader, "QualityControlNotes", "");
                    obj.PurchaseOrderNo = GetReaderValue_NullableInt32(reader, "PurchaseOrderNo", null);
                    obj.PurchaseOrderLineNo = GetReaderValue_NullableInt32(reader, "PurchaseOrderLineNo", null);
                    obj.QuantityInStock = GetReaderValue_Int32(reader, "QuantityInStock", 0);
                    obj.QuantityOnOrder = GetReaderValue_Int32(reader, "QuantityOnOrder", 0);
                    obj.Location = GetReaderValue_String(reader, "Location", "");
                    obj.ProductNo = GetReaderValue_NullableInt32(reader, "ProductNo", null);
                    obj.ResalePrice = GetReaderValue_NullableDouble(reader, "ResalePrice", null);
                    obj.Unavailable = GetReaderValue_Boolean(reader, "Unavailable", false);
                    obj.LotNo = GetReaderValue_NullableInt32(reader, "LotNo", null);
                    obj.LandedCost = GetReaderValue_NullableDouble(reader, "LandedCost", null);
                    obj.SupplierPart = GetReaderValue_String(reader, "SupplierPart", "");
                    obj.ROHS = GetReaderValue_NullableByte(reader, "ROHS", null);
                    obj.PackageUnit = GetReaderValue_NullableInt32(reader, "PackageUnit", null);
                    obj.StockKeepingUnit = GetReaderValue_NullableInt32(reader, "StockKeepingUnit", null);
                    obj.CustomerRMANo = GetReaderValue_NullableInt32(reader, "CustomerRMANo", null);
                    obj.CustomerRMALineNo = GetReaderValue_NullableInt32(reader, "CustomerRMALineNo", null);
                    obj.GoodsInLineNo = GetReaderValue_NullableInt32(reader, "GoodsInLineNo", null);
                    obj.UpdatedBy = GetReaderValue_NullableInt32(reader, "UpdatedBy", null);
                    obj.DLUP = GetReaderValue_DateTime(reader, "DLUP", DateTime.MinValue);
                    obj.FullSupplierPart = GetReaderValue_String(reader, "FullSupplierPart", "");
                    obj.CountryOfManufacture = GetReaderValue_NullableInt32(reader, "CountryOfManufacture", null);
                    obj.PartMarkings = GetReaderValue_String(reader, "PartMarkings", "");
                    obj.CountingMethodNo = GetReaderValue_NullableInt32(reader, "CountingMethodNo", null);
                    obj.ManufacturerCode = GetReaderValue_String(reader, "ManufacturerCode", "");
                    obj.QuantityAllocated = GetReaderValue_Int32(reader, "QuantityAllocated", 0);
                    obj.WarehouseName = GetReaderValue_String(reader, "WarehouseName", "");
                    obj.LotName = GetReaderValue_String(reader, "LotName", "");
                    obj.SupplierNo = GetReaderValue_NullableInt32(reader, "SupplierNo", null);
                    obj.SupplierName = GetReaderValue_String(reader, "SupplierName", "");
                    obj.QuantityAvailable = GetReaderValue_NullableInt32(reader, "QuantityAvailable", null);
                    obj.StatusNo = GetReaderValue_NullableInt32(reader, "StatusNo", null);
                    obj.PurchaseOrderNumber = GetReaderValue_NullableInt32(reader, "PurchaseOrderNumber", null);
                    obj.CustomerRMANumber = GetReaderValue_NullableInt32(reader, "CustomerRMANumber", null);
                    obj.PackageName = GetReaderValue_String(reader, "PackageName", "");
                    obj.PackageDescription = GetReaderValue_String(reader, "PackageDescription", "");
                    obj.ProductName = GetReaderValue_String(reader, "ProductName", "");
                    obj.ProductDescription = GetReaderValue_String(reader, "ProductDescription", "");
                    obj.ManufacturerName = GetReaderValue_String(reader, "ManufacturerName", "");
                    obj.CurrencyCode = GetReaderValue_String(reader, "CurrencyCode", "");
                    obj.LotCode = GetReaderValue_String(reader, "LotCode", "");
                    obj.Buyer = GetReaderValue_NullableInt32(reader, "Buyer", null);
                    obj.BuyerName = GetReaderValue_String(reader, "BuyerName", "");
                    obj.GoodsInNo = GetReaderValue_NullableInt32(reader, "GoodsInNo", null);
                    obj.GoodsInPrice = GetReaderValue_NullableDouble(reader, "GoodsInPrice", null);
                    obj.GoodsInShipInCost = GetReaderValue_NullableDouble(reader, "GoodsInShipInCost", null);
                    obj.GoodsInNumber = GetReaderValue_NullableInt32(reader, "GoodsInNumber", null);
                    obj.GoodsInCurrencyNo = GetReaderValue_NullableInt32(reader, "GoodsInCurrencyNo", null);
                    obj.StockDate = GetReaderValue_DateTime(reader, "StockDate", DateTime.MinValue);
                    obj.ROHSStatus = GetReaderValue_String(reader, "ROHSStatus", "");
                    obj.CountryOfManufactureName = GetReaderValue_String(reader, "CountryOfManufactureName", "");
                    obj.PurchasePrice = GetReaderValue_NullableDouble(reader, "PurchasePrice", null);
                    obj.CountingMethodDescription = GetReaderValue_String(reader, "CountingMethodDescription", "");
                    obj.StockLogDetail = GetReaderValue_String(reader, "StockLogDetail", "");
                    obj.StockLogChangeNotes = GetReaderValue_String(reader, "StockLogChangeNotes", "");
                    obj.StockLogReasonNo = GetReaderValue_NullableInt32(reader, "StockLogReasonNo", null);
                    obj.UpdateShipments = GetReaderValue_NullableBoolean(reader, "UpdateShipments", null);
                    obj.StockStartDate = GetReaderValue_String(reader, "StockStartDate", "");
                    //[005] code start
                    obj.OriginalLandedCost = GetReaderValue_NullableDouble(reader, "OriginalLandedCost", null);
                    obj.FirstStockProvisionDate = GetReaderValue_NullableDateTime(reader, "FirstStockProvisionDate", null);
                    obj.LastStockProvisionDate = GetReaderValue_NullableDateTime(reader, "LastStockProvisionDate", null);
                    obj.ManualStockSplitDate = GetReaderValue_NullableDateTime(reader, "ManualStockSplitDate", null);
                    obj.IsManual = GetReaderValue_NullableBoolean(reader, "IsManual", false);
                    //[005] code end
                    //[006] code start
                    obj.DivisionNo = GetReaderValue_NullableInt32(reader, "DivisionNo", null);
                    obj.DivisionName = GetReaderValue_String(reader, "DivisionName", "");
                    //[006] code end
                    obj.InternalPurchaseOrderNumber = GetReaderValue_Int32(reader, "InternalPurchaseOrderNumber", 0);
                    obj.InternalPurchaseOrderId = GetReaderValue_Int32(reader, "InternalPurchaseOrderId", 0);

                    obj.IPONo = GetReaderValue_NullableInt32(reader, "InternalPurchaseOrderNumber", null);
                    obj.IPOSupplier = GetReaderValue_NullableInt32(reader, "IPOSupplier", null);
                    obj.IPOSupplierName = GetReaderValue_String(reader, "IPOSupplierName", "");
                    obj.ClientLandedCost = GetReaderValue_NullableDouble(reader, "ClientLandedCost", null);
                    obj.ClientPurchasePrice = GetReaderValue_NullableDouble(reader, "ClientPurchasePrice", null);
                    obj.SalesOrderNumber = GetReaderValue_Int32(reader, "SalesOrderNo", 0);
                    obj.NPRNo = GetReaderValue_String(reader, "NPRNo", "");
                    obj.CustomerPO = GetReaderValue_String(reader, "CustomerPO", "");

                    obj.CustomerNo = GetReaderValue_NullableInt32(reader, "CustomerNo", null);
                    obj.CustomerName = GetReaderValue_String(reader, "CustomerName", "");
                    // obj.ReqSerialNo = GetReaderValue_NullableBoolean(reader, "ReqSerialNo", false);
                    //[007] code start
                    obj.MSLLevel = GetReaderValue_String(reader, "MSLLevel", "");
                    //[007] code end
                    //[008] code start
                    obj.CurrencyNo = GetReaderValue_NullableInt32(reader, "CurrencyNo", null);
                    //[008] code end

                    //[009] code start
                    obj.IsProdHazardous = GetReaderValue_NullableBoolean(reader, "IsProdHazardous", false);
                    obj.IsOrderViaIPOonly = GetReaderValue_NullableBoolean(reader, "IsOrderViaIPOonly", false);
                    obj.GeneralInspectionNotes = GetReaderValue_String(reader, "GeneralInspectionNotes", "");
                    //[009] code end

                    obj.ActeoneTestStatus = GetReaderValue_String(reader, "ActeoneTestStatus", "");
                    obj.IsopropryleStatus = GetReaderValue_String(reader, "IsopropryleStatus", "");
                    obj.ActeoneTest = GetReaderValue_String(reader, "ActeoneTest", "");
                    obj.Isopropryle = GetReaderValue_String(reader, "Isopropryle", "");
                    obj.HICStatusName = GetReaderValue_String(reader, "HICStatusName", "");
                    obj.ReqSerialNo = GetReaderValue_NullableBoolean(reader, "ReqSerialNo", false);
                    obj.IsLotCodesReq = GetReaderValue_NullableBoolean(reader, "IsLotCodesReq", null);
                    obj.BakingLevelAdded = GetReaderValue_String(reader, "BakingLevelAdded", "");

                    obj.IsRestrictedProduct = GetReaderValue_NullableBoolean(reader, "IsRestrictedProduct", false);

                    obj.IsImport = GetReaderValue_NullableBoolean(reader, "IsImport", false);
                    obj.ImportSupplierNo = GetReaderValue_NullableInt32(reader, "ImportSupplierNo", null);
                    obj.ImportupplierName = GetReaderValue_String(reader, "ImportupplierName", "");
                    obj.importCompanyType = GetReaderValue_String(reader, "importCompanyType", "");
                    obj.CompanyType = GetReaderValue_String(reader, "CompanyType", "");
                    obj.isLotOnHold = GetReaderValue_NullableBoolean(reader, "isLotOnHold", false);
                    obj.AS6081 = GetReaderValue_Boolean(reader, "AS6081", false); //[012]
                    obj.ClientUPLiftPrice = GetReaderValue_NullableDouble(reader, "ClientUPLiftPrice", null);
                    obj.HasSTO = GetReaderValue_NullableBoolean(reader, "HasSTO", false);
                    obj.CountryWarningMessage = GetReaderValue_String(reader, "WarningText", "");
                    obj.IsHasCountryMessage = GetReaderValue_Boolean(reader, "IsHasCountryMessage", false);
                    return obj;
                }
                else
                {
                    return null;
                }
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Stock", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// Get 
        /// Calls [USP_GetSTOIds]
        /// </summary>
        public override DataTable GetSTOIds(Boolean? IsHub, System.Int32? stockId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("USP_GetSTOIds", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@IsHub", SqlDbType.Bit).Value = IsHub;
                cmd.Parameters.Add("@StockId", SqlDbType.Int).Value = stockId;
                cn.Open();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                DataSet ds = new DataSet();
                da.Fill(ds);
                return ds.Tables[0];
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get STO Ids", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// Get 
        /// Calls [USP_GetSTO]
        /// </summary>
        public override DataTable GetSTO(System.Int32 STOId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("USP_GetSTO", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                //cmd.Parameters.Add("@IsHub", SqlDbType.Bit).Value = IsHub;
                cmd.Parameters.Add("@STOId", SqlDbType.BigInt).Value = STOId;
                cn.Open();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                DataSet ds = new DataSet();
                da.Fill(ds);
                return ds.Tables[0];
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get STO Data", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        /// <summary>
        /// GetForCustomerRMALine 
        /// Calls [usp_select_Stock_for_CustomerRMALine]
        /// </summary>
        public override StockDetails GetForCustomerRMALine(System.Int32? customerRmaLineId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_select_Stock_for_CustomerRMALine", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@CustomerRMALineId", SqlDbType.Int).Value = customerRmaLineId;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd, CommandBehavior.SingleRow);
                if (reader.Read())
                {
                    //return GetStockFromReader(reader);
                    StockDetails obj = new StockDetails();
                    obj.StockId = GetReaderValue_Int32(reader, "StockId", 0);
                    obj.FullPart = GetReaderValue_String(reader, "FullPart", "");
                    obj.Part = GetReaderValue_String(reader, "Part", "");
                    obj.ManufacturerNo = GetReaderValue_NullableInt32(reader, "ManufacturerNo", null);
                    obj.DateCode = GetReaderValue_String(reader, "DateCode", "");
                    obj.PackageNo = GetReaderValue_NullableInt32(reader, "PackageNo", null);
                    obj.WarehouseNo = GetReaderValue_NullableInt32(reader, "WarehouseNo", null);
                    obj.ClientNo = GetReaderValue_Int32(reader, "ClientNo", 0);
                    obj.QualityControlNotes = GetReaderValue_String(reader, "QualityControlNotes", "");
                    obj.PurchaseOrderNo = GetReaderValue_NullableInt32(reader, "PurchaseOrderNo", null);
                    obj.PurchaseOrderLineNo = GetReaderValue_NullableInt32(reader, "PurchaseOrderLineNo", null);
                    obj.QuantityInStock = GetReaderValue_Int32(reader, "QuantityInStock", 0);
                    obj.QuantityOnOrder = GetReaderValue_Int32(reader, "QuantityOnOrder", 0);
                    obj.Location = GetReaderValue_String(reader, "Location", "");
                    obj.ProductNo = GetReaderValue_NullableInt32(reader, "ProductNo", null);
                    obj.ResalePrice = GetReaderValue_NullableDouble(reader, "ResalePrice", null);
                    obj.Unavailable = GetReaderValue_Boolean(reader, "Unavailable", false);
                    obj.LotNo = GetReaderValue_NullableInt32(reader, "LotNo", null);
                    obj.LandedCost = GetReaderValue_NullableDouble(reader, "LandedCost", null);
                    obj.SupplierPart = GetReaderValue_String(reader, "SupplierPart", "");
                    obj.ROHS = GetReaderValue_Byte(reader, "ROHS", (byte)0);
                    obj.PackageUnit = GetReaderValue_NullableInt32(reader, "PackageUnit", null);
                    obj.StockKeepingUnit = GetReaderValue_NullableInt32(reader, "StockKeepingUnit", null);
                    obj.CustomerRMANo = GetReaderValue_NullableInt32(reader, "CustomerRMANo", null);
                    obj.CustomerRMALineNo = GetReaderValue_NullableInt32(reader, "CustomerRMALineNo", null);
                    obj.GoodsInLineNo = GetReaderValue_NullableInt32(reader, "GoodsInLineNo", null);
                    obj.UpdatedBy = GetReaderValue_NullableInt32(reader, "UpdatedBy", null);
                    obj.DLUP = GetReaderValue_DateTime(reader, "DLUP", DateTime.MinValue);
                    obj.FullSupplierPart = GetReaderValue_String(reader, "FullSupplierPart", "");
                    obj.CountryOfManufacture = GetReaderValue_NullableInt32(reader, "CountryOfManufacture", null);
                    obj.PartMarkings = GetReaderValue_String(reader, "PartMarkings", "");
                    obj.CountingMethodNo = GetReaderValue_NullableInt32(reader, "CountingMethodNo", null);
                    obj.ManufacturerCode = GetReaderValue_String(reader, "ManufacturerCode", "");
                    obj.QuantityAllocated = GetReaderValue_Int32(reader, "QuantityAllocated", 0);
                    obj.WarehouseName = GetReaderValue_String(reader, "WarehouseName", "");
                    obj.LotName = GetReaderValue_String(reader, "LotName", "");
                    obj.SupplierNo = GetReaderValue_NullableInt32(reader, "SupplierNo", null);
                    obj.SupplierName = GetReaderValue_String(reader, "SupplierName", "");
                    obj.QuantityAvailable = GetReaderValue_NullableInt32(reader, "QuantityAvailable", null);
                    obj.PurchaseOrderNumber = GetReaderValue_NullableInt32(reader, "PurchaseOrderNumber", null);
                    obj.CustomerRMANumber = GetReaderValue_NullableInt32(reader, "CustomerRMANumber", null);
                    obj.PackageName = GetReaderValue_String(reader, "PackageName", "");
                    obj.PackageDescription = GetReaderValue_String(reader, "PackageDescription", "");
                    obj.ProductName = GetReaderValue_String(reader, "ProductName", "");
                    obj.ProductDescription = GetReaderValue_String(reader, "ProductDescription", "");
                    obj.ManufacturerName = GetReaderValue_String(reader, "ManufacturerName", "");
                    obj.CurrencyCode = GetReaderValue_String(reader, "CurrencyCode", "");
                    obj.LotCode = GetReaderValue_String(reader, "LotCode", "");
                    obj.Buyer = GetReaderValue_NullableInt32(reader, "Buyer", null);
                    obj.BuyerName = GetReaderValue_String(reader, "BuyerName", "");
                    obj.GoodsInNo = GetReaderValue_NullableInt32(reader, "GoodsInNo", null);
                    obj.GoodsInPrice = GetReaderValue_NullableDouble(reader, "GoodsInPrice", null);
                    obj.GoodsInShipInCost = GetReaderValue_NullableDouble(reader, "GoodsInShipInCost", null);
                    obj.GoodsInNumber = GetReaderValue_NullableInt32(reader, "GoodsInNumber", null);
                    obj.GoodsInCurrencyNo = GetReaderValue_NullableInt32(reader, "GoodsInCurrencyNo", null);
                    obj.StockDate = GetReaderValue_DateTime(reader, "StockDate", DateTime.MinValue);
                    obj.ROHSStatus = GetReaderValue_String(reader, "ROHSStatus", "");
                    obj.CountryOfManufactureName = GetReaderValue_String(reader, "CountryOfManufactureName", "");
                    obj.PurchasePrice = GetReaderValue_NullableDouble(reader, "PurchasePrice", null);
                    obj.CountingMethodDescription = GetReaderValue_String(reader, "CountingMethodDescription", "");
                    return obj;
                }
                else
                {
                    return null;
                }
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Stock", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        /// <summary>
        /// GetForPage 
        /// Calls [usp_select_Stock_for_Page]
        /// </summary>
        public override StockDetails GetForPage(System.Int32? stockId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_select_Stock_for_Page", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@StockId", SqlDbType.Int).Value = stockId;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd, CommandBehavior.SingleRow);
                if (reader.Read())
                {
                    //return GetStockFromReader(reader);
                    StockDetails obj = new StockDetails();
                    obj.StockId = GetReaderValue_Int32(reader, "StockId", 0);
                    obj.Part = GetReaderValue_String(reader, "Part", "");
                    obj.ClientNo = GetReaderValue_Int32(reader, "ClientNo", 0);
                    obj.IsImageAvailable = GetReaderValue_Boolean(reader, "IsImageAvailable", false);
                    // [002] code start
                    obj.IsPDFAvailable = GetReaderValue_Boolean(reader, "IsPDFAvailable", false);
                    // [002] code end
                    obj.POClientNo = GetReaderValue_Int32(reader, "POClientNo", 0);
                    // [003] code start
                    obj.ClientName = GetReaderValue_String(reader, "ClientName", "");
                    // [003] code end
                    obj.ClientBaseCurrencyCode = GetReaderValue_String(reader, "ClientBaseCurrencyCode", "");
                    obj.ClientBaseCurrencyID = GetReaderValue_Int32(reader, "ClientBaseCurrencyID", 0);
                    return obj;
                }
                else
                {
                    return null;
                }
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Stock", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        /// <summary>
        /// GetForPurchaseOrderLine 
        /// Calls [usp_select_Stock_for_PurchaseOrderLine]
        /// </summary>
        public override StockDetails GetForPurchaseOrderLine(System.Int32? purchaseOrderLineId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_select_Stock_for_PurchaseOrderLine", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@PurchaseOrderLineId", SqlDbType.Int).Value = purchaseOrderLineId;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd, CommandBehavior.SingleRow);
                if (reader.Read())
                {
                    //return GetStockFromReader(reader);
                    StockDetails obj = new StockDetails();
                    obj.StockId = GetReaderValue_Int32(reader, "StockId", 0);
                    obj.FullPart = GetReaderValue_String(reader, "FullPart", "");
                    obj.Part = GetReaderValue_String(reader, "Part", "");
                    obj.ManufacturerNo = GetReaderValue_NullableInt32(reader, "ManufacturerNo", null);
                    obj.DateCode = GetReaderValue_String(reader, "DateCode", "");
                    obj.PackageNo = GetReaderValue_NullableInt32(reader, "PackageNo", null);
                    obj.WarehouseNo = GetReaderValue_NullableInt32(reader, "WarehouseNo", null);
                    obj.ClientNo = GetReaderValue_Int32(reader, "ClientNo", 0);
                    obj.QualityControlNotes = GetReaderValue_String(reader, "QualityControlNotes", "");
                    obj.PurchaseOrderNo = GetReaderValue_NullableInt32(reader, "PurchaseOrderNo", null);
                    obj.PurchaseOrderLineNo = GetReaderValue_NullableInt32(reader, "PurchaseOrderLineNo", null);
                    obj.QuantityInStock = GetReaderValue_Int32(reader, "QuantityInStock", 0);
                    obj.QuantityOnOrder = GetReaderValue_Int32(reader, "QuantityOnOrder", 0);
                    obj.Location = GetReaderValue_String(reader, "Location", "");
                    obj.ProductNo = GetReaderValue_NullableInt32(reader, "ProductNo", null);
                    obj.ResalePrice = GetReaderValue_NullableDouble(reader, "ResalePrice", null);
                    obj.Unavailable = GetReaderValue_Boolean(reader, "Unavailable", false);
                    obj.LotNo = GetReaderValue_NullableInt32(reader, "LotNo", null);
                    obj.LandedCost = GetReaderValue_NullableDouble(reader, "LandedCost", null);
                    obj.SupplierPart = GetReaderValue_String(reader, "SupplierPart", "");
                    obj.ROHS = GetReaderValue_Byte(reader, "ROHS", (byte)0);
                    obj.PackageUnit = GetReaderValue_NullableInt32(reader, "PackageUnit", null);
                    obj.StockKeepingUnit = GetReaderValue_NullableInt32(reader, "StockKeepingUnit", null);
                    obj.CustomerRMANo = GetReaderValue_NullableInt32(reader, "CustomerRMANo", null);
                    obj.CustomerRMALineNo = GetReaderValue_NullableInt32(reader, "CustomerRMALineNo", null);
                    obj.GoodsInLineNo = GetReaderValue_NullableInt32(reader, "GoodsInLineNo", null);
                    obj.UpdatedBy = GetReaderValue_NullableInt32(reader, "UpdatedBy", null);
                    obj.DLUP = GetReaderValue_DateTime(reader, "DLUP", DateTime.MinValue);
                    obj.FullSupplierPart = GetReaderValue_String(reader, "FullSupplierPart", "");
                    obj.CountryOfManufacture = GetReaderValue_NullableInt32(reader, "CountryOfManufacture", null);
                    obj.PartMarkings = GetReaderValue_String(reader, "PartMarkings", "");
                    obj.CountingMethodNo = GetReaderValue_NullableInt32(reader, "CountingMethodNo", null);
                    obj.ManufacturerCode = GetReaderValue_String(reader, "ManufacturerCode", "");
                    obj.QuantityAllocated = GetReaderValue_Int32(reader, "QuantityAllocated", 0);
                    obj.WarehouseName = GetReaderValue_String(reader, "WarehouseName", "");
                    obj.LotName = GetReaderValue_String(reader, "LotName", "");
                    obj.SupplierNo = GetReaderValue_NullableInt32(reader, "SupplierNo", null);
                    obj.SupplierName = GetReaderValue_String(reader, "SupplierName", "");
                    obj.QuantityAvailable = GetReaderValue_NullableInt32(reader, "QuantityAvailable", null);
                    obj.PurchaseOrderNumber = GetReaderValue_NullableInt32(reader, "PurchaseOrderNumber", null);
                    obj.CustomerRMANumber = GetReaderValue_NullableInt32(reader, "CustomerRMANumber", null);
                    obj.PackageName = GetReaderValue_String(reader, "PackageName", "");
                    obj.PackageDescription = GetReaderValue_String(reader, "PackageDescription", "");
                    obj.ProductName = GetReaderValue_String(reader, "ProductName", "");
                    obj.ProductDescription = GetReaderValue_String(reader, "ProductDescription", "");
                    obj.ManufacturerName = GetReaderValue_String(reader, "ManufacturerName", "");
                    obj.CurrencyCode = GetReaderValue_String(reader, "CurrencyCode", "");
                    obj.LotCode = GetReaderValue_String(reader, "LotCode", "");
                    obj.Buyer = GetReaderValue_NullableInt32(reader, "Buyer", null);
                    obj.BuyerName = GetReaderValue_String(reader, "BuyerName", "");
                    obj.GoodsInNo = GetReaderValue_NullableInt32(reader, "GoodsInNo", null);
                    obj.GoodsInPrice = GetReaderValue_NullableDouble(reader, "GoodsInPrice", null);
                    obj.GoodsInShipInCost = GetReaderValue_NullableDouble(reader, "GoodsInShipInCost", null);
                    obj.GoodsInNumber = GetReaderValue_NullableInt32(reader, "GoodsInNumber", null);
                    obj.GoodsInCurrencyNo = GetReaderValue_NullableInt32(reader, "GoodsInCurrencyNo", null);
                    obj.StockDate = GetReaderValue_DateTime(reader, "StockDate", DateTime.MinValue);
                    obj.ROHSStatus = GetReaderValue_String(reader, "ROHSStatus", "");
                    obj.CountryOfManufactureName = GetReaderValue_String(reader, "CountryOfManufactureName", "");
                    obj.PurchasePrice = GetReaderValue_NullableDouble(reader, "PurchasePrice", null);
                    obj.CountingMethodDescription = GetReaderValue_String(reader, "CountingMethodDescription", "");
                    return obj;
                }
                else
                {
                    return null;
                }
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Stock", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        /// <summary>
        /// GetListForLot 
        /// Calls [usp_selectAll_Stock_for_Lot]
        /// </summary>
        public override List<StockDetails> GetListForLot(System.Int32? lotId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_selectAll_Stock_for_Lot", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@LotId", SqlDbType.Int).Value = lotId;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<StockDetails> lst = new List<StockDetails>();
                while (reader.Read())
                {
                    StockDetails obj = new StockDetails();
                    obj.StockId = GetReaderValue_Int32(reader, "StockId", 0);
                    obj.FullPart = GetReaderValue_String(reader, "FullPart", "");
                    obj.Part = GetReaderValue_String(reader, "Part", "");
                    obj.ManufacturerNo = GetReaderValue_NullableInt32(reader, "ManufacturerNo", null);
                    obj.DateCode = GetReaderValue_String(reader, "DateCode", "");
                    obj.PackageNo = GetReaderValue_NullableInt32(reader, "PackageNo", null);
                    obj.WarehouseNo = GetReaderValue_NullableInt32(reader, "WarehouseNo", null);
                    obj.ClientNo = GetReaderValue_Int32(reader, "ClientNo", 0);
                    obj.QualityControlNotes = GetReaderValue_String(reader, "QualityControlNotes", "");
                    obj.PurchaseOrderNo = GetReaderValue_NullableInt32(reader, "PurchaseOrderNo", null);
                    obj.PurchaseOrderLineNo = GetReaderValue_NullableInt32(reader, "PurchaseOrderLineNo", null);
                    obj.QuantityInStock = GetReaderValue_Int32(reader, "QuantityInStock", 0);
                    obj.QuantityOnOrder = GetReaderValue_Int32(reader, "QuantityOnOrder", 0);
                    obj.Location = GetReaderValue_String(reader, "Location", "");
                    obj.ProductNo = GetReaderValue_NullableInt32(reader, "ProductNo", null);
                    obj.ResalePrice = GetReaderValue_NullableDouble(reader, "ResalePrice", null);
                    obj.Unavailable = GetReaderValue_Boolean(reader, "Unavailable", false);
                    obj.LotNo = GetReaderValue_NullableInt32(reader, "LotNo", null);
                    obj.LandedCost = GetReaderValue_NullableDouble(reader, "LandedCost", null);
                    obj.SupplierPart = GetReaderValue_String(reader, "SupplierPart", "");
                    obj.ROHS = GetReaderValue_Byte(reader, "ROHS", (byte)0);
                    obj.PackageUnit = GetReaderValue_NullableInt32(reader, "PackageUnit", null);
                    obj.StockKeepingUnit = GetReaderValue_NullableInt32(reader, "StockKeepingUnit", null);
                    obj.CustomerRMANo = GetReaderValue_NullableInt32(reader, "CustomerRMANo", null);
                    obj.CustomerRMALineNo = GetReaderValue_NullableInt32(reader, "CustomerRMALineNo", null);
                    obj.GoodsInLineNo = GetReaderValue_NullableInt32(reader, "GoodsInLineNo", null);
                    obj.UpdatedBy = GetReaderValue_NullableInt32(reader, "UpdatedBy", null);
                    obj.DLUP = GetReaderValue_DateTime(reader, "DLUP", DateTime.MinValue);
                    obj.FullSupplierPart = GetReaderValue_String(reader, "FullSupplierPart", "");
                    obj.CountryOfManufacture = GetReaderValue_NullableInt32(reader, "CountryOfManufacture", null);
                    obj.PartMarkings = GetReaderValue_String(reader, "PartMarkings", "");
                    obj.CountingMethodNo = GetReaderValue_NullableInt32(reader, "CountingMethodNo", null);
                    obj.ManufacturerCode = GetReaderValue_String(reader, "ManufacturerCode", "");
                    obj.QuantityAllocated = GetReaderValue_Int32(reader, "QuantityAllocated", 0);
                    obj.WarehouseName = GetReaderValue_String(reader, "WarehouseName", "");
                    obj.LotName = GetReaderValue_String(reader, "LotName", "");
                    obj.SupplierNo = GetReaderValue_NullableInt32(reader, "SupplierNo", null);
                    obj.SupplierName = GetReaderValue_String(reader, "SupplierName", "");
                    obj.QuantityAvailable = GetReaderValue_NullableInt32(reader, "QuantityAvailable", null);
                    obj.PurchaseOrderNumber = GetReaderValue_NullableInt32(reader, "PurchaseOrderNumber", null);
                    obj.CustomerRMANumber = GetReaderValue_NullableInt32(reader, "CustomerRMANumber", null);
                    obj.PackageName = GetReaderValue_String(reader, "PackageName", "");
                    obj.PackageDescription = GetReaderValue_String(reader, "PackageDescription", "");
                    obj.ProductName = GetReaderValue_String(reader, "ProductName", "");
                    obj.ProductDescription = GetReaderValue_String(reader, "ProductDescription", "");
                    obj.ManufacturerName = GetReaderValue_String(reader, "ManufacturerName", "");
                    obj.CurrencyCode = GetReaderValue_String(reader, "CurrencyCode", "");
                    obj.LotCode = GetReaderValue_String(reader, "LotCode", "");
                    obj.Buyer = GetReaderValue_NullableInt32(reader, "Buyer", null);
                    obj.BuyerName = GetReaderValue_String(reader, "BuyerName", "");
                    obj.GoodsInNo = GetReaderValue_NullableInt32(reader, "GoodsInNo", null);
                    obj.GoodsInPrice = GetReaderValue_NullableDouble(reader, "GoodsInPrice", null);
                    obj.GoodsInShipInCost = GetReaderValue_NullableDouble(reader, "GoodsInShipInCost", null);
                    obj.GoodsInNumber = GetReaderValue_NullableInt32(reader, "GoodsInNumber", null);
                    obj.GoodsInCurrencyNo = GetReaderValue_NullableInt32(reader, "GoodsInCurrencyNo", null);
                    obj.StockDate = GetReaderValue_DateTime(reader, "StockDate", DateTime.MinValue);
                    obj.ROHSStatus = GetReaderValue_String(reader, "ROHSStatus", "");
                    obj.CountryOfManufactureName = GetReaderValue_String(reader, "CountryOfManufactureName", "");
                    obj.PurchasePrice = GetReaderValue_NullableDouble(reader, "PurchasePrice", null);
                    obj.CountingMethodDescription = GetReaderValue_String(reader, "CountingMethodDescription", "");
                    //[003] code start
                    obj.StockUnallocatedCount = GetReaderValue_Int32(reader, "CountUnallocatedStock", 0);
                    //[003] code end
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Stocks", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        /// <summary>
        /// Calls [usp_selectNonZero_Stock_for_Lot]
        /// </summary>
        /// <param name="lotId"></param>
        /// <returns></returns>
        public override List<StockDetails> GetListForNonZeroStockLot(System.Int32? lotId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_selectNonZero_Stock_for_Lot", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@LotId", SqlDbType.Int).Value = lotId;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<StockDetails> lst = new List<StockDetails>();
                while (reader.Read())
                {
                    StockDetails obj = new StockDetails();
                    obj.StockId = GetReaderValue_Int32(reader, "StockId", 0);
                    //obj.FullPart = GetReaderValue_String(reader, "FullPart", "");
                    obj.Part = GetReaderValue_String(reader, "Part", "");
                    obj.ManufacturerNo = GetReaderValue_NullableInt32(reader, "ManufacturerNo", null);
                    obj.WarehouseNo = GetReaderValue_NullableInt32(reader, "WarehouseNo", null);
                    obj.QuantityInStock = GetReaderValue_Int32(reader, "QuantityInStock", 0);
                    obj.Location = GetReaderValue_String(reader, "Location", "");
                    obj.ProductNo = GetReaderValue_NullableInt32(reader, "ProductNo", null);
                    obj.LotNo = GetReaderValue_NullableInt32(reader, "LotNo", null);
                    obj.ROHS = GetReaderValue_Byte(reader, "ROHS", (byte)0);
                    obj.ManufacturerCode = GetReaderValue_String(reader, "ManufacturerCode", "");
                    obj.WarehouseName = GetReaderValue_String(reader, "WarehouseName", "");
                    obj.ProductName = GetReaderValue_String(reader, "ProductName", "");
                    obj.StockUnallocatedCount = GetReaderValue_Int32(reader, "CountUnallocatedStock", 0);
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Stocks", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }




        /// <summary>
        /// GetListRelatedStock 
        /// Calls [usp_selectAll_Stock_RelatedStock]
        /// </summary>
        public override List<StockDetails> GetListRelatedStock(System.Int32? stockNo)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_selectAll_Stock_RelatedStock", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@StockNo", SqlDbType.Int).Value = stockNo;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<StockDetails> lst = new List<StockDetails>();
                while (reader.Read())
                {
                    StockDetails obj = new StockDetails();
                    obj.StockId = GetReaderValue_Int32(reader, "StockId", 0);
                    obj.Part = GetReaderValue_String(reader, "Part", "");
                    obj.ManufacturerNo = GetReaderValue_NullableInt32(reader, "ManufacturerNo", null);
                    obj.PackageNo = GetReaderValue_NullableInt32(reader, "PackageNo", null);
                    obj.QuantityInStock = GetReaderValue_Int32(reader, "QuantityInStock", 0);
                    obj.QuantityOnOrder = GetReaderValue_Int32(reader, "QuantityOnOrder", 0);
                    obj.ProductNo = GetReaderValue_NullableInt32(reader, "ProductNo", null);
                    obj.ROHS = GetReaderValue_Byte(reader, "ROHS", (byte)0);
                    obj.QuantityAllocated = GetReaderValue_Int32(reader, "QuantityAllocated", 0);
                    obj.QuantityAvailable = GetReaderValue_NullableInt32(reader, "QuantityAvailable", null);
                    obj.PackageName = GetReaderValue_String(reader, "PackageName", "");
                    obj.ProductName = GetReaderValue_String(reader, "ProductName", "");
                    obj.ManufacturerName = GetReaderValue_String(reader, "ManufacturerName", "");
                    obj.RelationType = GetReaderValue_String(reader, "RelationType", "");
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Stocks", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        /// <summary>
        /// Source 
        /// Calls [usp_source_Stock]
        /// </summary>
        public override List<StockDetails> Source(System.Int32? clientId, System.String partSearch, bool hasServerLocal)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                if (!hasServerLocal)
                {
                    cn = new SqlConnection(this.GTConnectionString);
                    cmd = new SqlCommand("usp_source_Stock_Without_ClientId", cn);
                }
                else
                {
                    cn = new SqlConnection(this.ConnectionString);
                    cmd = new SqlCommand("usp_source_Stock", cn);
                }


                cmd.CommandType = CommandType.StoredProcedure;
                //[011] Start
                //cmd.CommandTimeout = 30;
                cmd.CommandTimeout = 120;  //sooryawin32
                //[011] End
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@PartSearch", SqlDbType.NVarChar).Value = partSearch;




                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<StockDetails> lst = new List<StockDetails>();
                while (reader.Read())
                {
                    StockDetails obj = new StockDetails();
                    obj.StockId = GetReaderValue_Int32(reader, "StockId", 0);
                    obj.Part = GetReaderValue_String(reader, "Part", "");
                    obj.ManufacturerNo = GetReaderValue_NullableInt32(reader, "ManufacturerNo", null);
                    obj.DateCode = GetReaderValue_String(reader, "DateCode", "");
                    obj.ClientNo = GetReaderValue_Int32(reader, "ClientNo", 0);
                    obj.QuantityInStock = GetReaderValue_Int32(reader, "QuantityInStock", 0);
                    obj.QuantityOnOrder = GetReaderValue_Int32(reader, "QuantityOnOrder", 0);
                    obj.Location = GetReaderValue_String(reader, "Location", "");
                    obj.ResalePrice = GetReaderValue_NullableDouble(reader, "ResalePrice", null);
                    obj.SupplierPart = GetReaderValue_String(reader, "SupplierPart", "");
                    obj.ROHS = GetReaderValue_NullableByte(reader, "ROHS", null);
                    obj.ManufacturerCode = GetReaderValue_String(reader, "ManufacturerCode", "");
                    obj.WarehouseName = GetReaderValue_String(reader, "WarehouseName", "");
                    obj.SupplierNo = GetReaderValue_NullableInt32(reader, "SupplierNo", null);
                    obj.SupplierName = GetReaderValue_String(reader, "SupplierName", "");
                    obj.PackageName = GetReaderValue_String(reader, "PackageName", "");
                    obj.ProductName = GetReaderValue_String(reader, "ProductName", "");
                    obj.ClientName = GetReaderValue_String(reader, "ClientName", "");
                    obj.ClientDataVisibleToOthers = GetReaderValue_NullableBoolean(reader, "ClientDataVisibleToOthers", null);
                    //[004] code start
                    obj.SupplierType = GetReaderValue_String(reader, "SupplierType", "");
                    //[004] code end
                    obj.QuantityAvailable = GetReaderValue_NullableInt32(reader, "QuantityAvailable", null);
                    obj.ClientBaseCurrencyCode = GetReaderValue_String(reader, "ClientBaseCurrencyCode", "");
                    obj.ClientCode = GetReaderValue_String(reader, "ClientCode", "");
                    obj.DivisionName = GetReaderValue_String(reader, "DivisionName", "");
                    obj.DivisionNo = GetReaderValue_Int32(reader, "DivisionNo", 0);
                    obj.DivisionStatus = GetReaderValue_Int32(reader, "DivisionStatus", 0);
                    //obj.QuantityAvailable = GetReaderValue_NullableInt32(reader, "InternalPurchaseOrderId", null);
                    //obj.QuantityAvailable = GetReaderValue_NullableInt32(reader, "ClientCompanyNo", null);
                    //obj.ClientBaseCurrencyCode = GetReaderValue_String(reader, "ClientBaseCurrencyCode", "");
                    obj.ClientUPLiftPrice = GetReaderValue_NullableDouble(reader, "ClientUpLiftPrice", null);

                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Stocks", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        /// <summary>
        /// Update Stock
        /// Calls [usp_update_Stock]
        /// </summary>
        public override bool Update(System.Int32? stockId, System.String part, System.Int32? manufacturerNo, System.String dateCode, System.Int32? packageNo, System.Int32? warehouseNo, System.Int32? clientNo, System.String qualityControlNotes, System.Int32? purchaseOrderNo, System.Int32? purchaseOrderLineNo, System.Int32? customerRmaNo, System.Int32? customerRmaLineNo, System.Int32? quantityInStock, System.Int32? quantityOnOrder, System.Int32? productNo, System.Double? resalePrice, System.Boolean? unavailable, System.Int32? lotNo, System.Double? landedCost, System.String supplierPart, System.Byte? rohs, System.Int32? packageUnit, System.Int32? stockKeepingUnit, System.Int32? updatedBy, System.String stockLogDetail, System.String stockLogChangeNotes, System.Int32? stockLogReasonNo, System.String location, System.Int32? countryOfManufacture, System.Boolean? updateShipments, System.String partMarkings, System.Int32? countingMethodNo, System.Int32? divisionNo, System.Boolean? IsUpdateClient, System.String mslLevel, System.Double? ClientUPLiftPrice, out System.String strLotMessage, System.Boolean AS6081)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            strLotMessage = "";
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_update_Stock", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@StockId", SqlDbType.Int).Value = stockId;
                cmd.Parameters.Add("@Part", SqlDbType.NVarChar).Value = part;
                cmd.Parameters.Add("@ManufacturerNo", SqlDbType.Int).Value = manufacturerNo;
                cmd.Parameters.Add("@DateCode", SqlDbType.NVarChar).Value = dateCode;
                cmd.Parameters.Add("@PackageNo", SqlDbType.Int).Value = packageNo;
                cmd.Parameters.Add("@WarehouseNo", SqlDbType.Int).Value = warehouseNo;
                cmd.Parameters.Add("@ClientNo", SqlDbType.Int).Value = clientNo;
                cmd.Parameters.Add("@QualityControlNotes", SqlDbType.NVarChar).Value = qualityControlNotes;
                cmd.Parameters.Add("@PurchaseOrderNo", SqlDbType.Int).Value = purchaseOrderNo;
                cmd.Parameters.Add("@PurchaseOrderLineNo", SqlDbType.Int).Value = purchaseOrderLineNo;
                cmd.Parameters.Add("@CustomerRMANo", SqlDbType.Int).Value = customerRmaNo;
                cmd.Parameters.Add("@CustomerRMALineNo", SqlDbType.Int).Value = customerRmaLineNo;
                cmd.Parameters.Add("@QuantityInStock", SqlDbType.Int).Value = quantityInStock;
                cmd.Parameters.Add("@QuantityOnOrder", SqlDbType.Int).Value = quantityOnOrder;
                cmd.Parameters.Add("@ProductNo", SqlDbType.Int).Value = productNo;
                cmd.Parameters.Add("@ResalePrice", SqlDbType.Float).Value = resalePrice;
                cmd.Parameters.Add("@Unavailable", SqlDbType.Bit).Value = unavailable;
                cmd.Parameters.Add("@LotNo", SqlDbType.Int).Value = lotNo;
                cmd.Parameters.Add("@LandedCost", SqlDbType.Float).Value = landedCost;
                cmd.Parameters.Add("@SupplierPart", SqlDbType.NVarChar).Value = supplierPart;
                cmd.Parameters.Add("@ROHS", SqlDbType.TinyInt).Value = rohs;
                cmd.Parameters.Add("@PackageUnit", SqlDbType.Int).Value = packageUnit;
                cmd.Parameters.Add("@StockKeepingUnit", SqlDbType.Int).Value = stockKeepingUnit;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@StockLogDetail", SqlDbType.NVarChar).Value = stockLogDetail;
                cmd.Parameters.Add("@StockLogChangeNotes", SqlDbType.NVarChar).Value = stockLogChangeNotes;
                cmd.Parameters.Add("@StockLogReasonNo", SqlDbType.Int).Value = stockLogReasonNo;
                cmd.Parameters.Add("@Location", SqlDbType.NVarChar).Value = location;
                cmd.Parameters.Add("@CountryOfManufacture", SqlDbType.Int).Value = countryOfManufacture;
                cmd.Parameters.Add("@UpdateShipments", SqlDbType.Bit).Value = updateShipments;
                cmd.Parameters.Add("@PartMarkings", SqlDbType.NVarChar).Value = partMarkings;
                cmd.Parameters.Add("@CountingMethodNo", SqlDbType.Int).Value = countingMethodNo;
                //[006] code start
                cmd.Parameters.Add("@DivisionNo", SqlDbType.Int).Value = divisionNo;
                //[006] code end
                cmd.Parameters.Add("@UpdateClientLandedCost", SqlDbType.Bit).Value = IsUpdateClient;
                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;

                //[007] code start
                cmd.Parameters.Add("@MSLLevel", SqlDbType.NVarChar).Value = mslLevel;
                cmd.Parameters.Add("@ClientUPLiftPrice", SqlDbType.Float).Value = ClientUPLiftPrice;
                cmd.Parameters.Add("@LotOnHoldMessage", SqlDbType.VarChar, 300).Direction = ParameterDirection.Output;
                //[007] code end
                cmd.Parameters.Add("@AS6081", SqlDbType.Bit).Value = AS6081;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                strLotMessage = (String)(cmd.Parameters["@LotOnHoldMessage"].Value == null ? "" : cmd.Parameters["@LotOnHoldMessage"].Value);
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                LogExceptionAzure(sqlex);
                throw new Exception("Failed to update Stock", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        /// <summary>
        /// Update Stock
        /// Calls [usp_update_Stock_Quarantined]
        /// </summary>
        public override bool UpdateQuarantined(System.Int32? stockId, System.Boolean? quarantine, System.String location, System.Int32? updatedBy)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_update_Stock_Quarantined", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@StockId", SqlDbType.Int).Value = stockId;
                cmd.Parameters.Add("@Quarantine", SqlDbType.Bit).Value = quarantine;
                cmd.Parameters.Add("@Location", SqlDbType.NVarChar).Value = location;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to update Stock", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        /// <summary>
        /// call [usp_update_GILine_Quarantined_From_Stock]
        /// </summary>
        /// <param name="giLineId"></param>
        /// <param name="logInID"></param>
        /// <param name="quarantine"></param>
        /// <param name="clientId"></param>
        /// <returns></returns>
        public override bool UpdateGILineQuarantineFromStock(int giLineId, int updatedBy, int? clientId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_update_GILine_Quarantined_From_Stock", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@GoodsInLineId", SqlDbType.Int).Value = giLineId;
                cmd.Parameters.Add("@ClientNo", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to update Stock", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="StockId"></param>
        /// <returns></returns>
        public override List<int> GetAllGILineIDsFromStock(System.Int32? StockId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_Get_All_GILine_ID_From_Stock", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@StockId", SqlDbType.Int).Value = StockId;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<int> lstId = new List<int>();
                while (reader.Read())
                {
                    int gILineID = GetReaderValue_Int32(reader, "GoodsInLineId", 0);

                    lstId.Add(gILineID);
                }
                return lstId;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get PDF list for stock", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        /// <summary>
        /// Update Stock
        /// Calls [usp_update_Stock_Transfer_Lot]
        /// </summary>
        public override bool UpdateTransferLot(System.Int32? oldNotNo, System.Int32? newLotNo)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_update_Stock_Transfer_Lot", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@OldNotNo", SqlDbType.Int).Value = oldNotNo;
                cmd.Parameters.Add("@NewLotNo", SqlDbType.Int).Value = newLotNo;
                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to update Stock", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        // [002] code start
        /// <summary>
        /// GetPDFListForStock 
        /// Calls [usp_selectAll_PDF_for_Stock]
        /// </summary>
        public override List<PDFDocumentDetails> GetPDFListForStock(System.Int32? StockId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_selectAll_PDF_for_Stock", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@StockNo", SqlDbType.Int).Value = StockId;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<PDFDocumentDetails> lstPDF = new List<PDFDocumentDetails>();
                while (reader.Read())
                {
                    PDFDocumentDetails obj = new PDFDocumentDetails();
                    obj.PDFDocumentId = GetReaderValue_Int32(reader, "StockPDFId", 0);
                    obj.PDFDocumentRefNo = GetReaderValue_Int32(reader, "StockNo", 0);
                    obj.Caption = GetReaderValue_String(reader, "Caption", "");
                    obj.FileName = GetReaderValue_String(reader, "FileName", "");
                    obj.UpdatedBy = GetReaderValue_NullableInt32(reader, "UpdatedBy", null);
                    obj.DLUP = GetReaderValue_NullableDateTime(reader, "DLUP", null);
                    lstPDF.Add(obj);
                    obj = null;
                }
                return lstPDF;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get PDF list for stock", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        /// <summary>
        /// Create a new row
        /// Calls [usp_insert_StockPDF]
        /// </summary>
        public override Int32 Insert(System.Int32? StockId, System.String Caption, System.String FileName, System.Int32? UpdatedBy)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_insert_StockPDF", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@StockNo", SqlDbType.Int).Value = StockId;
                cmd.Parameters.Add("@Caption", SqlDbType.NVarChar).Value = Caption;
                cmd.Parameters.Add("@FileName", SqlDbType.NVarChar).Value = FileName;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = UpdatedBy;
                cmd.Parameters.Add("@NewId", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (Int32)cmd.Parameters["@NewId"].Value;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to insert stock pdf", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        /// <summary>
        /// Delete stock pdf
        /// Calls[usp_delete_StockPDF]
        /// </summary>
        public override bool DeleteStockPDF(System.Int32? StockPdfId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_delete_StockPDF", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@StockPDFId", SqlDbType.Int).Value = StockPdfId;
                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to delete stock pdf", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        // [002] code end

        //[005] code start
        /// <summary>
        /// Update Stock
        /// Calls [usp_update_Stock_Provision]
        /// </summary>
        public override bool UpdateStockProvision(System.Int32? stockId, System.Double? newLandedCost, System.Int32? updatedBy, System.Int32? percentageValue)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_update_Stock_Provision", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@StockId", SqlDbType.Int).Value = stockId;
                cmd.Parameters.Add("@NewLandedCost", SqlDbType.Float).Value = newLandedCost;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@PercentageValue", SqlDbType.Int).Value = percentageValue;
                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to update Stock provision", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        //[005] code end


        // [002] code end

        /// <summary>
        /// Update Stock
        /// Calls [usp_update_Stock_Hub_LandedCost]
        /// </summary>
        public override bool UpdateHubLandedCost(System.Int32? stockId, System.Double? newLandedCost, System.Double? resalePrice, System.Int32? updatedBy)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_update_Stock_Hub_LandedCost", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@StockId", SqlDbType.Int).Value = stockId;
                cmd.Parameters.Add("@LandedCost", SqlDbType.Float).Value = newLandedCost;
                cmd.Parameters.Add("@ResalePrice", SqlDbType.Float).Value = resalePrice;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to update hub landed cost", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        #region Lost stock provision start
        /// <summary>
        /// Calls usp_selectAll_LotStockProvision
        /// </summary>
        /// <param name="lotId"></param>
        /// <returns></returns>
        public override List<StockDetails> GetListStockProvision(System.Int32? lotId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_selectAll_LotStockProvision", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@LotId", SqlDbType.Int).Value = lotId;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<StockDetails> lst = new List<StockDetails>();
                while (reader.Read())
                {
                    StockDetails obj = new StockDetails();
                    obj.StockId = GetReaderValue_Int32(reader, "StockId", 0);
                    obj.Part = GetReaderValue_String(reader, "Part", "");
                    obj.ManufacturerNo = GetReaderValue_NullableInt32(reader, "ManufacturerNo", null);
                    obj.WarehouseNo = GetReaderValue_NullableInt32(reader, "WarehouseNo", null);
                    obj.WarehouseName = GetReaderValue_String(reader, "WarehouseName", "");
                    obj.QuantityInStock = GetReaderValue_Int32(reader, "QuantityInStock", 0);
                    obj.ROHS = GetReaderValue_Byte(reader, "ROHS", (byte)0);
                    obj.ManufacturerCode = GetReaderValue_String(reader, "ManufacturerCode", "");
                    obj.Location = GetReaderValue_String(reader, "Location", "");
                    obj.ProductNo = GetReaderValue_NullableInt32(reader, "ProductNo", 0);
                    obj.ProductName = GetReaderValue_String(reader, "ProductName", "");
                    obj.OriginalLandedCost = (double?)GetReaderValue_NullableDecimal(reader, "OriginalLandedCost", null);
                    obj.CurrencyCode = GetReaderValue_String(reader, "CurrencyCode", "");
                    obj.ClientBaseCurrencyCode = GetReaderValue_String(reader, "ClientCurrency", "");
                    obj.CurrentLandedCost = GetReaderValue_NullableDouble(reader, "CurrentLandedCost", null);
                    obj.CurrencyId = GetReaderValue_Int32(reader, "CurrencyNo", 1);

                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Stocks", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// Insert Stock details against the lot id
        /// </summary>
        /// usp_insert_LotStockDetails
        /// <param name="LotId">LotId</param>
        /// <param name="Percentage"> Percentage</param>
        /// <param name="UpdatedBy">Inserted by</param>
        /// <returns></returns>
        public override Int32 InserLotStock(System.Int32? LotId, System.Double? Percentage, System.Int32? UpdatedBy, Double? TotalPrimaryLandedCost, Double? TotalCurrentLandedCost)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_insert_LotStockDetails", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@LotId", SqlDbType.Int).Value = LotId;
                cmd.Parameters.Add("@Percentage", SqlDbType.Decimal).Value = Percentage;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = UpdatedBy;
                cmd.Parameters.Add("@TotalPrimaryLandedCost", SqlDbType.Decimal).Value = TotalPrimaryLandedCost;
                cmd.Parameters.Add("@TotalCurrentLandedCost", SqlDbType.Decimal).Value = TotalCurrentLandedCost;
                cmd.Parameters.Add("@NewId", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (Int32)cmd.Parameters["@NewId"].Value;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to insert stock againest Lot", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        #endregion

        public override List<GoodsInLineDetails> GetSerial(System.Int32? stockId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_select_SerialNoByStock", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@StockId", SqlDbType.Int).Value = stockId;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<GoodsInLineDetails> lst = new List<GoodsInLineDetails>();
                while (reader.Read())
                {
                    GoodsInLineDetails obj = new GoodsInLineDetails();
                    obj.SerialNoId = GetReaderValue_Int32(reader, "SerialNumberId", 0);
                    obj.SerialNo = GetReaderValue_String(reader, "SerialNo", "");
                    obj.SubGroup = GetReaderValue_String(reader, "Group", null);
                    obj.GoodsInNo = GetReaderValue_Int32(reader, "GoodsInNo", 0);

                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Serial Details", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override DataTable GetClient(System.Int32? clientId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {

                //cn = new SqlConnection(this.GTImportConnectionString);
                //cn = new SqlConnection(ConfigurationManager.ConnectionStrings["GTImportSqlServer"].ToString());
                //GT connection string dependancy code start
                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                //code end
                cmd = new SqlCommand("usp_select_clients", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cn.Open();
                SqlDataAdapter objDaFecthClient = new SqlDataAdapter(cmd);
                DataTable dtClient = new DataTable();
                objDaFecthClient.Fill(dtClient);
                return dtClient;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get client", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override DataTable GetIndustryType(System.Int32? clientId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                //cn = new SqlConnection(this.GTImportConnectionString);
                //cn = new SqlConnection(ConfigurationManager.ConnectionStrings["GTImportSqlServer"].ToString());
                //GT connection string dependancy code start
                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                //code end
                cmd = new SqlCommand("usp_all_CompanyIndustryType", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;
                //cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cn.Open();
                SqlDataAdapter objDaFecthClient = new SqlDataAdapter(cmd);
                DataTable dtClient = new DataTable();
                objDaFecthClient.Fill(dtClient);
                return dtClient;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get client", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override DataTable GetClientName(System.Int32? clientId, System.Int32? clientType_con)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                // string ConnName = "LocalSqlServer";

                //if (clientType_con == 3)
                //{
                //    //ConnName = "GTSqlServer";
                //    ConnName = "LocalSqlServer";
                //}
                if (clientType_con == 1)
                    clientId = 0;


                //cn = new SqlConnection(ConfigurationManager.ConnectionStrings[ConnName].ToString());
                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_select_clients", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cn.Open();
                SqlDataAdapter objDaFecthClient = new SqlDataAdapter(cmd);
                DataTable dtClient = new DataTable();
                objDaFecthClient.Fill(dtClient);
                return dtClient;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get client", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override DataTable GetSupplier(System.Int32? clientId)
        {
            SqlConnection cn = null;
            SqlCommand cmdsup = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmdsup = new SqlCommand("usp_select_suppliers", cn);
                cmdsup.CommandType = CommandType.StoredProcedure;
                cmdsup.CommandTimeout = 60;
                cmdsup.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cn.Open();
                SqlDataAdapter objDaFecthClient = new SqlDataAdapter(cmdsup);
                DataTable dtClient = new DataTable();
                objDaFecthClient.Fill(dtClient);
                return dtClient;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get supplier", sqlex);
            }
            finally
            {
                cmdsup.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override DataTable GetCurrency(System.Int32? clientId, System.Int32? clientType_con)
        {
            SqlConnection cn = null;
            SqlCommand cmdcur = null;
            try
            {

                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmdcur = new SqlCommand("usp_select_currencies_buy", cn);
                cmdcur.CommandType = CommandType.StoredProcedure;
                cmdcur.CommandTimeout = 60;
                cmdcur.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cn.Open();
                SqlDataAdapter objDaFecthClient = new SqlDataAdapter(cmdcur);
                DataTable dtClient = new DataTable();
                objDaFecthClient.Fill(dtClient);
                return dtClient;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get currency", sqlex);
            }
            finally
            {
                cmdcur.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override void saveExcelData(DataTable tempStock, int clientType_con)
        {
            //SqlConnection cn = null;
            try
            {

                //using (SqlConnection dbConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString()))
                using (SqlConnection dbConnection = new SqlConnection(this.ConnectionString))
                {
                    dbConnection.Open();
                    if (dbConnection.State == ConnectionState.Open)
                    {

                        using (var tran = dbConnection.BeginTransaction(IsolationLevel.ReadCommitted))
                        {
                            using (SqlBulkCopy s = new SqlBulkCopy(dbConnection, SqlBulkCopyOptions.Default, tran))
                            {
                                s.DestinationTableName = tempStock.TableName;

                                foreach (var column in tempStock.Columns)
                                    s.ColumnMappings.Add(column.ToString(), column.ToString());

                                s.WriteToServer(tempStock);
                                tran.Commit();

                            }
                        }

                    }
                }

            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to insert stock data", sqlex);
            }

        }
        public override void saveExcelBulkSave(DataTable tempStock, DataTable dtData, string originalFilename, string generatedFilename, System.Int32 userId, System.Int32 clientId, int? SelectedclientId, int clientType_con)
        {

            SqlConnection cn = null;
            SqlCommand cmdcur = null;
            try
            {
                //string ConnName = "LocalSqlServer";
                //if (clientType_con == 3)
                //{
                //    //ConnName = "GTSqlServer";
                //    ConnName = "LocalSqlServer";
                //}

                //using (SqlConnection dbConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString()))
                //{
                //    dbConnection.Open();
                //    if (dbConnection.State == ConnectionState.Open)
                //    {


                //        using (var tran = dbConnection.BeginTransaction(IsolationLevel.ReadCommitted))
                //        {
                //            using (SqlBulkCopy s = new SqlBulkCopy(dbConnection, SqlBulkCopyOptions.Default, tran))
                //            {
                //                s.DestinationTableName = tempStock.TableName;
                //                int i = 1;
                //                foreach (var column in dtData.Columns)
                //                {

                //                    if (i <= 15)
                //                    {
                //                        s.ColumnMappings.Add(column.ToString(), "Column" + i.ToString());

                //                    }
                //                    i++;
                //                }

                //                DataColumn ClientId = new DataColumn();
                //                ClientId.DataType = typeof(int);
                //                ClientId.ColumnName = "ClientId";


                //                DataColumn SelectedClientId = new DataColumn();
                //                SelectedClientId.DataType = typeof(int);
                //                SelectedClientId.ColumnName = "SelectedClientId";

                //                DataColumn CreatedBy = new DataColumn();
                //                CreatedBy.DataType = typeof(int);
                //                CreatedBy.ColumnName = "CreatedBy";

                //                DataColumn orgFilename = new DataColumn();
                //                orgFilename.DataType = typeof(string);
                //                orgFilename.ColumnName = "OriginalFilename";

                //                DataColumn genFilename = new DataColumn();
                //                genFilename.DataType = typeof(string);
                //                genFilename.ColumnName = "GeneratedFilename";


                //                // Add the columns to the tempStock DataTable
                //                dtData.Columns.Add(ClientId);
                //                dtData.Columns.Add(SelectedClientId);
                //                dtData.Columns.Add(CreatedBy);
                //                dtData.Columns.Add(orgFilename);
                //                dtData.Columns.Add(genFilename);
                //                foreach (DataRow dr in dtData.Rows)
                //                {
                //                    //  DataRow appendStockfiled = dtData.NewRow();
                //                    dr["ClientId"] = clientId;
                //                    dr["SelectedClientId"] = SelectedclientId;
                //                    dr["CreatedBy"] = userId;
                //                    dr["OriginalFilename"] = originalFilename;
                //                    dr["GeneratedFilename"] = generatedFilename;

                //                }

                //                dtData.AcceptChanges();

                //                s.ColumnMappings.Add("ClientId", "ClientId");
                //                s.ColumnMappings.Add("SelectedClientId", "SelectedClientId");
                //                s.ColumnMappings.Add("CreatedBy", "CreatedBy");
                //                s.ColumnMappings.Add("OriginalFilename", "OriginalFilename");
                //                s.ColumnMappings.Add("GeneratedFilename", "GeneratedFilename");

                //                s.WriteToServer(dtData);
                //                tran.Commit();

                //            }
                //        }

                //    }
                //}

                //try
                //{
                //int i = 1;
                int fixedColumn = 15;
                //int totalColumn = 25;
                int columnCount = dtData.Columns.Count;
                // rename actual column according to excel file uploaded
                for (int i = 0; i < columnCount; i++)
                {
                    dtData.Columns[i].ColumnName = "Column" + (i + 1).ToString();
                }
                // add rest column to datatable to match fixed column to 15 range
                int leftOver = fixedColumn - columnCount;
                for (int j = 0; j < leftOver; j++)
                {
                    columnCount = columnCount + 1;
                    dtData.Columns.Add("Column" + (columnCount).ToString());
                }

                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmdcur = new SqlCommand("usp_saveExcelBulkSave", cn);
                cmdcur.CommandType = CommandType.StoredProcedure;
                cmdcur.CommandTimeout = 0;
                cmdcur.Parameters.AddWithValue("@UploadedData", dtData);
                cmdcur.Parameters.AddWithValue("@originalFilename", originalFilename);
                cmdcur.Parameters.AddWithValue("@generatedFilename", generatedFilename);
                cmdcur.Parameters.AddWithValue("@userId", userId);
                cmdcur.Parameters.AddWithValue("@clientId", clientId);
                cmdcur.Parameters.AddWithValue("@SelectedclientId", SelectedclientId);
                cmdcur.Parameters.AddWithValue("@Inactive", 0);
                cmdcur.Parameters.AddWithValue("@clientType_con", clientType_con);
                cn.Open();
                cmdcur.ExecuteNonQuery();
                //return dtClient;
                //}
                //catch (SqlException sqlex)
                //{
                //    //LogException(sqlex);
                //    throw new Exception("Failed to get currency", sqlex);
                //}

            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to insert stock data", sqlex);
            }
            finally
            {
                cmdcur.Dispose();
                cn.Close();
                cn.Dispose();
            }

        }

        public override void SaveExcelHeader(string columnList, string insertColumnList, System.Int32? clientId, int? SelectedclientId, int? loginId, int clientType_con)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {

                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_SaveStockExcelColumn", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 600;
                cmd.Parameters.Add("@SelectColumns", SqlDbType.NVarChar, 3000).Value = columnList;
                cmd.Parameters.Add("@InserColumnList", SqlDbType.NVarChar, 3000).Value = insertColumnList;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@SelectedClientId", SqlDbType.Int).Value = SelectedclientId;
                cmd.Parameters.Add("@UserId", SqlDbType.Int).Value = loginId;

                cn.Open();
                cmd.ExecuteNonQuery();

            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to insert header info", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override DataTable GetCustTableAllColumn()
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {

                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_GetAllColunStockReq", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                //cmd.CommandTimeout = 60;
                cmd.CommandTimeout = 600;

                cn.Open();
                DataSet ds = new DataSet();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);
                return ds.Tables[0];
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get column list", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override DataTable GetStockDetailFromTemp(int displayLength, int displayStart, int sortCol, string sortDir, string search, int userid, int Clientid, int SelectedclientId, int clientType_con)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {

                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_GetStockNewData", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@displayLength", SqlDbType.Int).Value = displayLength;
                cmd.Parameters.Add("@displayStart", SqlDbType.Int).Value = displayStart;
                cmd.Parameters.Add("@sortCol", SqlDbType.Int).Value = sortCol;
                cmd.Parameters.Add("@sortDir", SqlDbType.NVarChar, 10).Value = sortDir;//[004]  
                cmd.Parameters.Add("@search", SqlDbType.NVarChar, 255).Value = search;//[004]  
                cmd.Parameters.Add("@userid", SqlDbType.Int).Value = userid;//[003]
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = Clientid;//[003]
                cmd.Parameters.Add("@SelectedClientId", SqlDbType.Int).Value = SelectedclientId;//[003]
                //cmd.CommandTimeout = 60;
                cmd.CommandTimeout = 900;

                cn.Open();
                DataSet ds = new DataSet();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);
                return ds.Tables[0];
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get CustomerRequirements", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override DataTable GetStockGenrateTempData(int displayLength, int displayStart, int sortCol, string sortDir, string search, int userid, int Clientid, int SelectedclientId, string ColumnList, string ddlCurancy, string SupplierNameText, int clientType_con, string Column_Lable, string Column_Name)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {

                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_GetDynamicHeaderColumn", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@displayLength", SqlDbType.Int).Value = displayLength;
                cmd.Parameters.Add("@displayStart", SqlDbType.Int).Value = displayStart;
                cmd.Parameters.Add("@sortCol", SqlDbType.Int).Value = sortCol;
                cmd.Parameters.Add("@sortDir", SqlDbType.NVarChar, 10).Value = sortDir;//[004]  
                cmd.Parameters.Add("@search", SqlDbType.NVarChar, 255).Value = search;//[004]  
                cmd.Parameters.Add("@userid", SqlDbType.Int).Value = userid;//[003]
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = Clientid;//[003]
                cmd.Parameters.Add("@SelectedClientId", SqlDbType.Int).Value = SelectedclientId;//[003]
                cmd.Parameters.Add("@list_Label_name", SqlDbType.NVarChar).Value = Column_Lable;
                cmd.Parameters.Add("@list_column_name", SqlDbType.NVarChar).Value = Column_Name;
                cmd.Parameters.Add("@SelectColumns", SqlDbType.NVarChar).Value = ColumnList;
                cmd.Parameters.Add("@CurancyColumns", SqlDbType.NVarChar).Value = ddlCurancy;
                cmd.Parameters.Add("@SupplierNameText", SqlDbType.NVarChar).Value = SupplierNameText;

                //cmd.CommandTimeout = 60;
                cmd.CommandTimeout = 600;

                cn.Open();
                DataSet ds = new DataSet();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);
                // return ds.Tables[0];
                // changes done to catch exception from SP
                if (ds.Tables.Count == 1)   // if exception is thrown
                    return ds.Tables[0];
                else
                    return ds.Tables[ds.Tables.Count - 1];   // if no exception then multiple tables collection return so picking last table
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get CustomerRequirements", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override DataTable GetExcelHeader(System.Int32 clientId, System.Int32 userId, System.Int32 SelectedClientID, int clientType_con)//GetExcelHeaderFrom
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {

                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_GetExcelStockHeaderColumn", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                //cmd.CommandTimeout = 60;
                cmd.CommandTimeout = 600;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@UserId", SqlDbType.Int).Value = userId;
                cmd.Parameters.Add("@SelectedClientID", SqlDbType.Int).Value = SelectedClientID;


                cn.Open();
                DataSet ds = new DataSet();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);
                return ds.Tables[0];
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get excel header", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override DataTable GetExcelHeaderFrom(System.Int32 clientId, System.Int32 userId, System.Int32 SelectedClientID, int clientType_con)//GetExcelHeaderFrom
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {

                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                // cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_GetExcelStockHeaderColumnFrom", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                //cmd.CommandTimeout = 60;
                cmd.CommandTimeout = 600;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@UserId", SqlDbType.Int).Value = userId;
                cmd.Parameters.Add("@SelectedClientID", SqlDbType.Int).Value = SelectedClientID;


                cn.Open();
                DataSet ds = new DataSet();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);
                return ds.Tables[0];
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get excel header", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override DataTable GetImportExcelHeader(System.Int32 clientId, System.Int32 userId, System.Int32 SelectedClientID, string selectedColumnlist, int clientType_con)//GetExcelHeaderFrom
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {

                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_GetDynamicImportHeaderColumn", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                //cmd.CommandTimeout = 60;
                cmd.CommandTimeout = 600;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@UserId", SqlDbType.Int).Value = userId;
                cmd.Parameters.Add("@SelectedClientID", SqlDbType.Int).Value = SelectedClientID;
                cmd.Parameters.Add("@SelectColumns", SqlDbType.NVarChar).Value = selectedColumnlist;//[003]ColumnList


                cn.Open();
                DataSet ds = new DataSet();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);
                return ds.Tables[0];
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get excel header", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override int DeleteRecord(int SelectedClientID, System.Int32 userId, int ClientId, int clientType_con)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {

                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_deleteTempStockRecord", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 600;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = ClientId;
                cmd.Parameters.Add("@UserId", SqlDbType.Int).Value = userId;
                cmd.Parameters.Add("@SelectedClientID", SqlDbType.Int).Value = SelectedClientID;
                cn.Open();
                return ExecuteNonQuery(cmd);

            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to insert mapping detail", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override int DeleteTempMapping(int SupplierId, int clientType_con)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {

                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_delete_ImportMapping", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 600;
                cmd.Parameters.Add("@SupplierNo", SqlDbType.Int).Value = SupplierId;
                cn.Open();
                return ExecuteNonQuery(cmd);

            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to Delete mapping detail", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override int SaveSupplierColumnMapping(int SupplierId, string insertMapColumnList, int clientType_con)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {

                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_insert_ImportColumnMapping", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 600;
                cmd.Parameters.Add("@strInsertMappings", SqlDbType.NVarChar).Value = insertMapColumnList;//[003]ColumnList
                cn.Open();
                return ExecuteNonQuery(cmd);

            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to insert mapping Column detail", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override DataTable GetSupplierMappedColumn(int SupplierId, int clientType_con)
        {

            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {

                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_ImportSupplierColumnMapping", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                //cmd.CommandTimeout = 60;
                cmd.CommandTimeout = 600;
                cmd.Parameters.Add("@SupplierNo", SqlDbType.Int).Value = SupplierId;
                cn.Open();
                DataSet ds = new DataSet();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);
                return ds.Tables[0];
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to get Supplier Column Mapping", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override int saveStockImportData(int userId, int ClientId, int SelectedclientId, string Column_Lable, string Column_Name, string insertDataList, string fileColName, int SupplierId, string ddlCurrency, int recordType, int clientType_con, out System.String errorMessage)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            errorMessage = string.Empty;
            try
            {


                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_Select_ImportStockData", cn);
                //cmd = new SqlCommand("procname", cn);

                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 600;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = ClientId;
                cmd.Parameters.Add("@UserId", SqlDbType.Int).Value = userId;
                cmd.Parameters.Add("@SelectedClientID", SqlDbType.Int).Value = SelectedclientId;
                cmd.Parameters.Add("@list_Label_name", SqlDbType.NVarChar).Value = Column_Lable;
                cmd.Parameters.Add("@list_column_name", SqlDbType.NVarChar).Value = Column_Name;
                cmd.Parameters.Add("@insertDataList", SqlDbType.NVarChar).Value = insertDataList;
                cmd.Parameters.Add("@fileColName", SqlDbType.NVarChar).Value = fileColName;
                cmd.Parameters.Add("@SupplierId", SqlDbType.Int).Value = SupplierId;
                cmd.Parameters.Add("@CurrencyName", SqlDbType.NVarChar).Value = ddlCurrency;
                cmd.Parameters.Add("@recordType", SqlDbType.Int).Value = recordType;
                cmd.Parameters.Add("@RecordCount", SqlDbType.Int).Direction = ParameterDirection.Output;
                cmd.Parameters.Add("@ErrorMessage", SqlDbType.VarChar, 400).Direction = ParameterDirection.Output;

                cn.Open();
                //return ExecuteNonQuery(cmd);
                ExecuteNonQuery(cmd);
                errorMessage = Convert.ToString(cmd.Parameters["@ErrorMessage"].Value);
                return (Int32)cmd.Parameters["@RecordCount"].Value;


            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to insert mapping Column detail", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override int InsertUpdateStockImportData(int userId, int ClientId, int SelectedclientId, string Column_Lable, string Column_Name, string insertDataList, string fileColName, int SupplierId, string ddlCurrency, int recordType, int clientType_con, out System.String errorMessage)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            errorMessage = string.Empty;
            try
            {

                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_InsertOrUpdate_OfferImportdata", cn);
                //cmd = new SqlCommand("procname", cn);

                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 900;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = ClientId;
                cmd.Parameters.Add("@UserId", SqlDbType.Int).Value = userId;
                cmd.Parameters.Add("@SelectedClientID", SqlDbType.Int).Value = SelectedclientId;
                cmd.Parameters.Add("@list_Label_name", SqlDbType.NVarChar).Value = Column_Lable;
                cmd.Parameters.Add("@list_column_name", SqlDbType.NVarChar).Value = Column_Name;
                cmd.Parameters.Add("@insertDataList", SqlDbType.NVarChar).Value = insertDataList;
                cmd.Parameters.Add("@fileColName", SqlDbType.NVarChar).Value = fileColName;
                cmd.Parameters.Add("@SupplierId", SqlDbType.Int).Value = SupplierId;
                cmd.Parameters.Add("@CurrencyName", SqlDbType.NVarChar).Value = ddlCurrency;
                cmd.Parameters.Add("@recordType", SqlDbType.Int).Value = recordType;
                cmd.Parameters.Add("@RecordCount", SqlDbType.Int).Direction = ParameterDirection.Output;
                cmd.Parameters.Add("@ErrorMessage", SqlDbType.VarChar, 400).Direction = ParameterDirection.Output;

                cn.Open();
                //return ExecuteNonQuery(cmd);
                ExecuteNonQuery(cmd);
                errorMessage = Convert.ToString(cmd.Parameters["@ErrorMessage"].Value);
                return (Int32)cmd.Parameters["@RecordCount"].Value;


            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to insert mapping Column detail", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override DataTable GetStockImportactivity(int displayLength, int displayStart, int sortCol, string sortDir, string search, int Clientid, int clientType_con, int SelectedClientId, int userLoginid)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {

                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_select_importactivity", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@displayLength", SqlDbType.Int).Value = displayLength;
                cmd.Parameters.Add("@displayStart", SqlDbType.Int).Value = displayStart;
                cmd.Parameters.Add("@sortCol", SqlDbType.Int).Value = sortCol;
                cmd.Parameters.Add("@sortDir", SqlDbType.NVarChar, 10).Value = sortDir;
                cmd.Parameters.Add("@search", SqlDbType.NVarChar, 255).Value = search;
                cmd.Parameters.Add("@ClientType", SqlDbType.Int).Value = clientType_con;
                cmd.Parameters.Add("@selectedclientid", SqlDbType.Int).Value = SelectedClientId;
                cmd.Parameters.Add("@CreateBy", SqlDbType.Int).Value = userLoginid;
                cmd.CommandTimeout = 600;

                cn.Open();
                DataSet ds = new DataSet();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);
                return ds.Tables[0];
            }
            catch (SqlException sqlex)
            {

                throw new Exception("Failed to get Import Activity detail", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override DataTable GetImportActivity(System.Int32? clientId, System.Int32? clientType_con)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                // string ConnName = "LocalSqlServer";

                //if (clientType_con == 3)
                //{
                //    //ConnName = "GTSqlServer";
                //    ConnName = "LocalSqlServer";
                //}
                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_select_importactivity", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;


                cn.Open();
                SqlDataAdapter objDaFecthClient = new SqlDataAdapter(cmd);
                DataTable dtClient = new DataTable();
                objDaFecthClient.Fill(dtClient);
                return dtClient;
            }
            catch (SqlException sqlex)
            {

                throw new Exception("Failed to get client", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override int SaveImportActivity(string strInsertSummary, int clientType_con)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {

                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_insert_ImportActivity", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 600;
                cmd.Parameters.Add("@strInsertText", SqlDbType.NVarChar).Value = strInsertSummary;
                cn.Open();
                return ExecuteNonQuery(cmd);

            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to insert Import Activity detail", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override int Delete_OfferRecord(string OfferId, System.Int32 BomId, System.Int32 userId, int ClientId, string OfferType)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {

                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_deleteOfferRecord", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 600;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = ClientId;
                cmd.Parameters.Add("@UserId", SqlDbType.Int).Value = userId;
                cmd.Parameters.Add("@OfferType", SqlDbType.NVarChar).Value = OfferType;
                cmd.Parameters.Add("@OfferIds", SqlDbType.NVarChar).Value = OfferId;
                cmd.Parameters.Add("@BomId", SqlDbType.Int).Value = BomId;
                cmd.Parameters.Add("@DLUP", SqlDbType.DateTime).Value = DateTime.Now;


                cn.Open();
                return ExecuteNonQuery(cmd);

            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to insert mapping detail", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        //Delete_TrustedRecord
        public override int Delete_TrustedRecord(string TrustedId, System.Int32 BomId, System.Int32 userId, int ClientId, string OfferType)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {

                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_deleteTrustedRecord", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 600;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = ClientId;
                cmd.Parameters.Add("@UserId", SqlDbType.Int).Value = userId;
                cmd.Parameters.Add("@OfferType", SqlDbType.NVarChar).Value = OfferType;
                cmd.Parameters.Add("@TrustedIds", SqlDbType.NVarChar).Value = TrustedId;
                cmd.Parameters.Add("@BomId", SqlDbType.Int).Value = BomId;
                cmd.Parameters.Add("@DLUP", SqlDbType.DateTime).Value = DateTime.Now;
                cn.Open();
                return ExecuteNonQuery(cmd);

            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to insert mapping detail", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        ////Delete_POQuotesRecord
        public override int Delete_POQuotesRecord(string POQuotesId, System.Int32 BomId, System.Int32 userId, int ClientId, string OfferType)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {

                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_deletePriceRequestRecord", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 600;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = ClientId;
                cmd.Parameters.Add("@UserId", SqlDbType.Int).Value = userId;
                cmd.Parameters.Add("@OfferType", SqlDbType.NVarChar).Value = OfferType;
                cmd.Parameters.Add("@POQuotesIds", SqlDbType.NVarChar).Value = POQuotesId;
                cmd.Parameters.Add("@BomId", SqlDbType.Int).Value = BomId;
                cmd.Parameters.Add("@DLUP", SqlDbType.DateTime).Value = DateTime.Now;
                cn.Open();
                return ExecuteNonQuery(cmd);

            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to insert mapping detail", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        //Maintain Cross Match  Log Search Details
        public override int CrossMatchSearchLog(System.Int32 BomId, System.Int32 userId, int ClientId, string logdetails)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_CrossMatch_SearchLog_Insert", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@BomId", SqlDbType.Int).Value = BomId;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = ClientId;
                cmd.Parameters.Add("@UserId", SqlDbType.Int).Value = userId;
                cmd.Parameters.Add("@logdetails", SqlDbType.NVarChar).Value = logdetails;
                cmd.Parameters.Add("@DLUP", SqlDbType.DateTime).Value = DateTime.Now;
                cn.Open();
                return ExecuteNonQuery(cmd);

            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to insert Auto Search log detail", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        //codee add for CrossMatch Filter
        /// <summary>
        /// Insert
        /// Calls [usp_offer_Filter_CrossMatchRequirement]
        /// </summary>
        public override Int32 FilterOfferCrossMatchReq(System.Int32 offerId, out System.String errorMessage)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            errorMessage = string.Empty;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_offer_Filter_CrossMatchRequirement", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@OfferNo", SqlDbType.Int).Value = offerId;
                cmd.Parameters.Add("@RecordCount", SqlDbType.Int).Direction = ParameterDirection.Output;
                cmd.Parameters.Add("@ErrorMessage", SqlDbType.VarChar, 400).Direction = ParameterDirection.Output;

                cn.Open();
                ExecuteNonQuery(cmd);
                errorMessage = Convert.ToString(cmd.Parameters["@ErrorMessage"].Value);
                return (Int32)cmd.Parameters["@RecordCount"].Value;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to insert Filter Offer Save", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        //end

        //codee add for CrossMatch delete record
        /// <summary>
        /// Insert
        /// Calls [usp_offer_Delete_CrossMatchRequirement]
        /// </summary>
        public override Int32 DeleteFilterOfferCrossMatchReq(int offerId, out System.String errorMessage)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            errorMessage = string.Empty;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_offer_Delete_CrossMatchRequirement", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@OfferNo", SqlDbType.Int).Value = offerId;
                cmd.Parameters.Add("@RecordCount", SqlDbType.Int).Direction = ParameterDirection.Output;
                cmd.Parameters.Add("@ErrorMessage", SqlDbType.VarChar, 400).Direction = ParameterDirection.Output;

                cn.Open();
                ExecuteNonQuery(cmd);
                errorMessage = Convert.ToString(cmd.Parameters["@ErrorMessage"].Value);
                return (Int32)cmd.Parameters["@RecordCount"].Value;
            }
            catch (SqlException sqlex)
            {

                throw new Exception("Failed to Delete Offer Requirement", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        //end


        //codee add for CrossMatch Log Delete
        /// <summary>
        /// Insert
        /// Calls [usp_AutoLog_Delete_CrossMatch]
        /// </summary>
        public override Int32 DeleteAutoLogCrossMatchReq(int BomId, int userid, out System.String errorMessage)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            errorMessage = string.Empty;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_AutoLog_Delete_CrossMatch", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@BomId", SqlDbType.Int).Value = BomId;
                cmd.Parameters.Add("@UserId", SqlDbType.Int).Value = userid;
                cmd.Parameters.Add("@RecordCount", SqlDbType.Int).Direction = ParameterDirection.Output;
                cmd.Parameters.Add("@ErrorMessage", SqlDbType.VarChar, 400).Direction = ParameterDirection.Output;

                cn.Open();
                ExecuteNonQuery(cmd);
                errorMessage = Convert.ToString(cmd.Parameters["@ErrorMessage"].Value);
                return (Int32)cmd.Parameters["@RecordCount"].Value;
            }
            catch (SqlException sqlex)
            {

                throw new Exception("Failed to Delete Auto Log", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        //end

        //codee add for CrossMatch Export To excel
        /// <summary>
        /// Select for Export
        /// Calls [usp_ExportToExcel_CrossMatch]
        /// </summary>
        public override DataTable ExportToCSVCrossMatch(System.Int32? BomID, System.Int32? CustReqId, System.Int32? clientId, System.Int32? UserId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_select_Export_CrossMatch", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@UserId", SqlDbType.Int).Value = UserId;
                cmd.Parameters.Add("@BomId", SqlDbType.Int).Value = BomID;
                cmd.Parameters.Add("@CustomerRequirementId", SqlDbType.Int).Value = CustReqId;
                cmd.Parameters.Add("@IsPoHub", SqlDbType.Int).Value = 0;

                cn.Open();
                SqlDataAdapter objDaFecthExport = new SqlDataAdapter(cmd);
                DataTable dtExport = new DataTable();
                objDaFecthExport.Fill(dtExport);
                return dtExport;
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to Export Cross Match Data", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        //codee add for CrossMatch Export To excel
        /// <summary>
        /// Select for Export
        /// Calls [usp_ExportToExcel_CrossMatch_list]
        /// </summary>
        public override DataTable ExportToCSVCrossMatchlist(System.Int32? BomID, System.Int32? CustReqId, System.Int32? clientId, System.Int32? UserId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {

                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_ExportToExcel_CrossMatch_list", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@UserId", SqlDbType.Int).Value = UserId;
                cmd.Parameters.Add("@BomId", SqlDbType.Int).Value = BomID;
                cmd.Parameters.Add("@CustomerRequirementId", SqlDbType.Int).Value = CustReqId;
                cmd.Parameters.Add("@IsPoHub", SqlDbType.Int).Value = 0;

                cn.Open();
                SqlDataAdapter objDaFecthExport = new SqlDataAdapter(cmd);
                DataTable dtExport = new DataTable();
                objDaFecthExport.Fill(dtExport);
                return dtExport;
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to Export Cross Match Data", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override List<StockDetails> GetCustreqtest(System.Int32? BomNo, System.Int32? clientId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_select_BomCust_Requirement", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@BomNO", SqlDbType.Int).Value = BomNo;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = 0;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<StockDetails> lst = new List<StockDetails>();
                while (reader.Read())
                {
                    StockDetails obj = new StockDetails();
                    obj.CustomerRequirementId = GetReaderValue_Int32(reader, "CustomerRequirementId", 0);
                    obj.CustomerRequirementNumber = GetReaderValue_Int32(reader, "CustomerRequirementNumber", 0);


                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Serial Details", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override DataTable GetBomClient(System.Int32? clientId, System.Int32? UserId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {

                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_select_BomClients", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = UserId;
                cn.Open();
                SqlDataAdapter objDaFecthBomClient = new SqlDataAdapter(cmd);
                DataTable dtBomClient = new DataTable();
                objDaFecthBomClient.Fill(dtBomClient);
                return dtBomClient;
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to get client", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override DataTable GetBomCusReq(System.Int32? clientId, System.Int32? BomNo, System.Int32? UserId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_select_BomCust_Requirement", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;
                cmd.Parameters.Add("@BomNO", SqlDbType.Int).Value = BomNo;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = UserId;
                cn.Open();
                SqlDataAdapter objDaFecthBomClient = new SqlDataAdapter(cmd);
                DataTable dtBomClient = new DataTable();
                objDaFecthBomClient.Fill(dtBomClient);
                return dtBomClient;
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to get client", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }



        ////////////////////////EPO///File///// Upload/////////////////////Start//////////
        public override void saveExcelEpoBulkSave(DataTable tempEpo, DataTable dtData, string originalFilename, string generatedFilename, System.Int32 userId, System.Int32 clientId, int? SelectedclientId, int clientType_con)
        {

            try
            {

                dtData.Columns["Lead Time"].ColumnName = "LeadTime";
                dtData.Columns["Virtual Cost Price"].ColumnName = "VirtualCostPrice";
                dtData.Columns["Date Code"].ColumnName = "DateCode";
                dtData.Columns["Vendor Type"].ColumnName = "VendorType";
                dtData.Columns["Factory Sealed"].ColumnName = "FactorySealed";

                using (SqlConnection dbConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString()))
                {
                    dbConnection.Open();
                    if (dbConnection.State == ConnectionState.Open)
                    {


                        using (var tran = dbConnection.BeginTransaction(IsolationLevel.ReadCommitted))
                        {
                            using (SqlBulkCopy s = new SqlBulkCopy(dbConnection, SqlBulkCopyOptions.Default, tran))
                            {
                                //Part,ManufacturerName,Quantity,Price,VirtualCostPrice,LeadTime,DateCode,SPQ,SupplierMOQ,SupplierName
                                //,SupplierType,FactorySealed,SupplierLTB,OriginalEntryDate,Description
                                s.DestinationTableName = tempEpo.TableName;
                                s.ColumnMappings.Add("MPN", "Part");
                                s.ColumnMappings.Add("MFR", "ManufacturerName");
                                s.ColumnMappings.Add("Quantity", "Quantity");
                                s.ColumnMappings.Add("COST", "Price");
                                s.ColumnMappings.Add("VirtualCostPrice", "VirtualCostPrice");
                                s.ColumnMappings.Add("LeadTime", "LeadTime");
                                s.ColumnMappings.Add("DateCode", "DateCode");
                                s.ColumnMappings.Add("SPQ", "SPQ");
                                s.ColumnMappings.Add("MOQ", "SupplierMOQ");
                                s.ColumnMappings.Add("Vendor", "SupplierName");
                                s.ColumnMappings.Add("VendorType", "SupplierType");
                                s.ColumnMappings.Add("FactorySealed", "FactorySealed");
                                s.ColumnMappings.Add("LTB", "SupplierLTB");
                                s.ColumnMappings.Add("Date", "OriginalEntryDate");
                                s.ColumnMappings.Add("Description", "Description");



                                DataColumn ClientId = new DataColumn();
                                ClientId.DataType = typeof(int);
                                ClientId.ColumnName = "ClientId";


                                DataColumn SelectedClientId = new DataColumn();
                                SelectedClientId.DataType = typeof(int);
                                SelectedClientId.ColumnName = "SelectedClientId";

                                DataColumn CreatedBy = new DataColumn();
                                CreatedBy.DataType = typeof(int);
                                CreatedBy.ColumnName = "CreatedBy";

                                DataColumn orgFilename = new DataColumn();
                                orgFilename.DataType = typeof(string);
                                orgFilename.ColumnName = "OriginalFilename";

                                DataColumn genFilename = new DataColumn();
                                genFilename.DataType = typeof(string);
                                genFilename.ColumnName = "GeneratedFilename";


                                // Add the columns to the tempStock DataTable
                                dtData.Columns.Add(ClientId);
                                dtData.Columns.Add(SelectedClientId);
                                dtData.Columns.Add(CreatedBy);
                                dtData.Columns.Add(orgFilename);
                                dtData.Columns.Add(genFilename);
                                foreach (DataRow dr in dtData.Rows)
                                {
                                    //  DataRow appendStockfiled = dtData.NewRow();
                                    dr["ClientId"] = clientId;
                                    dr["SelectedClientId"] = SelectedclientId;
                                    dr["CreatedBy"] = userId;
                                    dr["OriginalFilename"] = originalFilename;
                                    dr["GeneratedFilename"] = generatedFilename;

                                }

                                dtData.AcceptChanges();

                                s.ColumnMappings.Add("ClientId", "ClientId");
                                s.ColumnMappings.Add("SelectedClientId", "SelectedClientId");
                                s.ColumnMappings.Add("CreatedBy", "CreatedBy");
                                s.ColumnMappings.Add("OriginalFilename", "OriginalFilename");
                                s.ColumnMappings.Add("GeneratedFilename", "GeneratedFilename");

                                s.WriteToServer(dtData);
                                tran.Commit();

                            }
                        }

                    }
                }

            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to insert stock data", sqlex);
            }

        }
        public override void SaveExcelEpoHeader(string columnList, string insertColumnList, System.Int32? clientId, int? SelectedclientId, int? loginId, int clientType_con)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                //string ConnName = "LocalSqlServer";

                //if (clientType_con == 3)
                //{
                //    // ConnName = "GTSqlServer";
                //    ConnName = "LocalSqlServer";
                //}
                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_SaveEpoExcelColumnHeading", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 600;
                cmd.Parameters.Add("@SelectColumns", SqlDbType.NVarChar, 3000).Value = columnList;
                cmd.Parameters.Add("@InserColumnList", SqlDbType.NVarChar, 3000).Value = insertColumnList;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@SelectedClientId", SqlDbType.Int).Value = SelectedclientId;
                cmd.Parameters.Add("@UserId", SqlDbType.Int).Value = loginId;

                cn.Open();
                cmd.ExecuteNonQuery();

            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to insert header info", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override DataTable GetEpoExcelHeader(System.Int32 clientId, System.Int32 userId, System.Int32 SelectedClientID, int clientType_con)//GetExcelHeaderFrom
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {

                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_GetEpoExcelHeaderColumn", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                //cmd.CommandTimeout = 60;
                cmd.CommandTimeout = 600;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@UserId", SqlDbType.Int).Value = userId;
                cmd.Parameters.Add("@SelectedClientID", SqlDbType.Int).Value = SelectedClientID;


                cn.Open();
                DataSet ds = new DataSet();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);
                return ds.Tables[0];
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get excel header", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override DataTable GetEpoDetailFromTemp(int displayLength, int displayStart, int sortCol, string sortDir, string search, int userid, int Clientid, int SelectedclientId, int clientType_con)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {

                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_GetEpobindTempData", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@displayLength", SqlDbType.Int).Value = displayLength;
                cmd.Parameters.Add("@displayStart", SqlDbType.Int).Value = displayStart;
                cmd.Parameters.Add("@sortCol", SqlDbType.Int).Value = sortCol;
                cmd.Parameters.Add("@sortDir", SqlDbType.NVarChar, 10).Value = sortDir;//[004]  
                cmd.Parameters.Add("@search", SqlDbType.NVarChar, 255).Value = search;//[004]  
                cmd.Parameters.Add("@userid", SqlDbType.Int).Value = userid;//[003]
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = Clientid;//[003]
                cmd.Parameters.Add("@SelectedClientId", SqlDbType.Int).Value = SelectedclientId;//[003]
                //cmd.CommandTimeout = 60;
                cmd.CommandTimeout = 900;

                cn.Open();
                DataSet ds = new DataSet();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);
                return ds.Tables[0];
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get CustomerRequirements", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override int DeleteEpoRecord(int SelectedClientID, System.Int32 userId, int ClientId, int clientType_con)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {

                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_deleteTempEpoRecord", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 600;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = ClientId;
                cmd.Parameters.Add("@UserId", SqlDbType.Int).Value = userId;
                cmd.Parameters.Add("@SelectedClientID", SqlDbType.Int).Value = SelectedClientID;
                cn.Open();
                return ExecuteNonQuery(cmd);

            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to insert mapping detail", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override DataTable GetEpoGenrateTempData(int displayLength, int displayStart, int sortCol, string sortDir, string search, int userid, int Clientid, int SelectedclientId, string SupplierNameText, int clientType_con, System.Double? UpliftpValue, int NoChangesToSourcingSheet)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_GenrateDynamicEpoData", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@displayLength", SqlDbType.Int).Value = displayLength;
                cmd.Parameters.Add("@displayStart", SqlDbType.Int).Value = displayStart;
                cmd.Parameters.Add("@sortCol", SqlDbType.Int).Value = sortCol;
                cmd.Parameters.Add("@sortDir", SqlDbType.NVarChar, 10).Value = sortDir;//[004]  
                cmd.Parameters.Add("@search", SqlDbType.NVarChar, 255).Value = search;//[004]  
                cmd.Parameters.Add("@userid", SqlDbType.Int).Value = userid;//[003]
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = Clientid;//[003]
                cmd.Parameters.Add("@SelectedClientId", SqlDbType.Int).Value = SelectedclientId;//[003]
                                                                                                // cmd.Parameters.Add("@SupplierNameText", SqlDbType.NVarChar).Value = SupplierNameText;
                cmd.Parameters.Add("@UpliftPercentage", SqlDbType.Float).Value = UpliftpValue;
                cmd.Parameters.Add("@NoChangesToSourcingSheet", SqlDbType.Int).Value = NoChangesToSourcingSheet;



                //cmd.CommandTimeout = 60;
                cmd.CommandTimeout = 600;

                cn.Open();
                DataSet ds = new DataSet();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);
                return ds.Tables[0];
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get CustomerRequirements", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override int saveEpoImportData(int userId, int ClientId, int SelectedclientId, int SupplierId, int recordType, int clientType_con, System.Double? UpliftPercentage, int NoChangesToSourcingSheet, out System.String errorMessage)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            errorMessage = string.Empty;
            try
            {
                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_Select_ImportEpoData", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 600;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = ClientId;
                cmd.Parameters.Add("@UserId", SqlDbType.Int).Value = userId;
                cmd.Parameters.Add("@SelectedClientID", SqlDbType.Int).Value = SelectedclientId;
                //cmd.Parameters.Add("@SupplierId", SqlDbType.Int).Value = SupplierId;
                cmd.Parameters.Add("@recordType", SqlDbType.Int).Value = recordType;
                cmd.Parameters.Add("@UpliftPercentage", SqlDbType.Float).Value = UpliftPercentage;
                cmd.Parameters.Add("@NoChangesToSourcingSheet", SqlDbType.Int).Value = NoChangesToSourcingSheet;
                cmd.Parameters.Add("@RecordCount", SqlDbType.Int).Direction = ParameterDirection.Output;
                cmd.Parameters.Add("@ErrorMessage", SqlDbType.VarChar, 400).Direction = ParameterDirection.Output;

                cn.Open();
                //return ExecuteNonQuery(cmd);
                ExecuteNonQuery(cmd);
                errorMessage = Convert.ToString(cmd.Parameters["@ErrorMessage"].Value);
                return (Int32)cmd.Parameters["@RecordCount"].Value;


            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to insert mapping Column detail", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override int SaveEpoImportActivity(string strInsertSummary, int clientType_con)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_insert_EpoImportActivity", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 600;
                cmd.Parameters.Add("@strInsertText", SqlDbType.NVarChar).Value = strInsertSummary;
                cn.Open();
                return ExecuteNonQuery(cmd);

            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to insert Import Activity detail", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override DataTable GetEpoImportactivity(int displayLength, int displayStart, int sortCol, string sortDir, string search, int Clientid, int clientType_con, int SelectedClientId, int userLoginid, int isInactive)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {

                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_select_Epoimportactivity", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@displayLength", SqlDbType.Int).Value = displayLength;
                cmd.Parameters.Add("@displayStart", SqlDbType.Int).Value = displayStart;
                cmd.Parameters.Add("@sortCol", SqlDbType.Int).Value = sortCol;
                cmd.Parameters.Add("@sortDir", SqlDbType.NVarChar, 10).Value = sortDir;
                cmd.Parameters.Add("@search", SqlDbType.NVarChar, 255).Value = search;
                cmd.Parameters.Add("@ClientType", SqlDbType.Int).Value = clientType_con;
                cmd.Parameters.Add("@selectedclientid", SqlDbType.Int).Value = SelectedClientId;
                cmd.Parameters.Add("@CreateBy", SqlDbType.Int).Value = userLoginid;
                cmd.Parameters.Add("@IsInactive", SqlDbType.Int).Value = isInactive;
                cmd.CommandTimeout = 600;

                cn.Open();
                DataSet ds = new DataSet();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);
                foreach(DataRow row in ds.Tables[0].Rows)
                {
                    row["ImportedBy"] = (row["ImportedBy"].ToString() == "") ? "N/A" : row["ImportedBy"];
                }    
                return ds.Tables[0];
            }
            catch (SqlException sqlex)
            {

                throw new Exception("Failed to get Import Activity detail", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        ////////////////////////EPO///File///// Upload/////////////////////End//////////

        private bool saveSourcingImportCSV(int bomid, string caption, string filename, int updatedBy, string importtype)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;

            try
            {
                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_Insert_Sourcing_CSV", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 600;
                cmd.Parameters.Add("@BOMNo", SqlDbType.Int).Value = bomid;
                cmd.Parameters.Add("@Caption", SqlDbType.NVarChar).Value = caption;
                cmd.Parameters.Add("@FileName", SqlDbType.VarChar).Value = filename;
                //cmd.Parameters.Add("@SupplierId", SqlDbType.Int).Value = SupplierId;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@ImportType", SqlDbType.VarChar).Value = importtype;

                cn.Open();
                //return ExecuteNonQuery(cmd);
                ExecuteNonQuery(cmd);
                return true;

            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to insert sourcing import", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        ////////////////////////Sourcing Resule///File///// Upload/////////////////////Start//////////
        /// <summary>
        /// 
        /// </summary>
        /// <param name="tempEpo"></param>
        /// <param name="dtData"></param>
        /// <param name="originalFilename"></param>
        /// <param name="generatedFilename"></param>
        /// <param name="userId"></param>
        /// <param name="clientId"></param>
        /// <param name="bomId"></param>
        public override void LeftSideExcelSourcingResuleBulkSave(DataTable tempEpo, DataTable dtData, string originalFilename, string generatedFilename, System.Int32 userId, System.Int32 clientId, int bomId)
        {

            try
            {
                this.saveSourcingImportCSV(bomId, "Import Sourcing", generatedFilename, userId, "SRCIMPORT");
                //string ConnName = "LocalSqlServer";
                using (SqlConnection dbConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString()))
                {
                    dbConnection.Open();
                    if (dbConnection.State == ConnectionState.Open)
                    {
                        using (var tran = dbConnection.BeginTransaction(IsolationLevel.ReadCommitted))
                        {
                            using (SqlBulkCopy s = new SqlBulkCopy(dbConnection, SqlBulkCopyOptions.Default, tran))
                            {
                                s.DestinationTableName = tempEpo.TableName;
                                s.ColumnMappings.Add("Mfr", "Mfr");
                                s.ColumnMappings.Add("Part", "Part");
                                s.ColumnMappings.Add("QTY", "QTY");
                                s.ColumnMappings.Add("Price", "Price");

                                DataColumn ClientId = new DataColumn();
                                ClientId.DataType = typeof(int);
                                ClientId.ColumnName = "ClientId";


                                DataColumn BomId = new DataColumn();
                                BomId.DataType = typeof(int);
                                BomId.ColumnName = "BomId";

                                DataColumn CreatedBy = new DataColumn();
                                CreatedBy.DataType = typeof(int);
                                CreatedBy.ColumnName = "CreatedBy";

                                DataColumn orgFilename = new DataColumn();
                                orgFilename.DataType = typeof(string);
                                orgFilename.ColumnName = "OriginalFilename";

                                DataColumn genFilename = new DataColumn();
                                genFilename.DataType = typeof(string);
                                genFilename.ColumnName = "GeneratedFilename";


                                // Add the columns to the tempStock DataTable
                                dtData.Columns.Add(ClientId);
                                dtData.Columns.Add(BomId);
                                dtData.Columns.Add(CreatedBy);
                                dtData.Columns.Add(orgFilename);
                                dtData.Columns.Add(genFilename);
                                foreach (DataRow dr in dtData.Rows)
                                {
                                    DataRow appendStockfiled = dtData.NewRow();
                                    dr["ClientId"] = clientId;
                                    dr["BomId"] = bomId;
                                    dr["CreatedBy"] = userId;
                                    dr["OriginalFilename"] = originalFilename;
                                    dr["GeneratedFilename"] = generatedFilename;

                                }

                                dtData.AcceptChanges();

                                s.ColumnMappings.Add("ClientId", "ClientId");
                                s.ColumnMappings.Add("BomId", "BomId");
                                s.ColumnMappings.Add("CreatedBy", "CreatedBy");
                                s.ColumnMappings.Add("OriginalFilename", "OriginalFilename");
                                s.ColumnMappings.Add("GeneratedFilename", "GeneratedFilename");

                                s.WriteToServer(dtData);
                                tran.Commit();

                            }
                        }

                    }
                }

            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to insert stock data", sqlex);
            }

        }
        public override void RightSideExcelSourcingResuleBulkSave(DataTable tempEpo, DataTable dtData, string originalFilename, string generatedFilename, System.Int32 userId, System.Int32 clientId, int bomId)
        {

            try
            {
                //string ConnName = "LocalSqlServer";
                dtData.Columns["Quoted MPN"].ColumnName = "QuotedMPN";
                dtData.Columns["Quoted MFR"].ColumnName = "QuotedMFR";
                dtData.Columns["Cost $"].ColumnName = "Cost$";
                dtData.Columns["Lead Time"].ColumnName = "LeadTime";
                dtData.Columns["Quoted Cost"].ColumnName = "QuotedCost";
                dtData.Columns["Date Quoted"].ColumnName = "DateQuoted";

                using (SqlConnection dbConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString()))
                {
                    dbConnection.Open();
                    if (dbConnection.State == ConnectionState.Open)
                    {


                        using (var tran = dbConnection.BeginTransaction(IsolationLevel.ReadCommitted))
                        {
                            using (SqlBulkCopy s = new SqlBulkCopy(dbConnection, SqlBulkCopyOptions.Default, tran))
                            {
                                s.DestinationTableName = tempEpo.TableName;
                                s.ColumnMappings.Add("QuotedMPN", "QuotedMPN");
                                s.ColumnMappings.Add("QuotedMFR", "QuotedMFR");
                                s.ColumnMappings.Add("Cost$", "Cost$");
                                s.ColumnMappings.Add("LeadTime", "LeadTime");
                                s.ColumnMappings.Add("SPQ", "SPQ");
                                s.ColumnMappings.Add("MOQ", "MOQ");
                                s.ColumnMappings.Add("Pack", "Pack");
                                s.ColumnMappings.Add("Vendor", "Vendor");
                                s.ColumnMappings.Add("IF", "IF");
                                s.ColumnMappings.Add("%", "%");
                                s.ColumnMappings.Add("QuotedCost", "QuotedCost");
                                s.ColumnMappings.Add("DateQuoted", "DateQuoted");
                                s.ColumnMappings.Add("ProjectSite", "Project/Site");

                                DataColumn ClientId = new DataColumn();
                                ClientId.DataType = typeof(int);
                                ClientId.ColumnName = "ClientId";


                                DataColumn BomId = new DataColumn();
                                BomId.DataType = typeof(int);
                                BomId.ColumnName = "BomId";

                                DataColumn CreatedBy = new DataColumn();
                                CreatedBy.DataType = typeof(int);
                                CreatedBy.ColumnName = "CreatedBy";

                                DataColumn orgFilename = new DataColumn();
                                orgFilename.DataType = typeof(string);
                                orgFilename.ColumnName = "OriginalFilename";

                                DataColumn genFilename = new DataColumn();
                                genFilename.DataType = typeof(string);
                                genFilename.ColumnName = "GeneratedFilename";


                                //Add the columns to the tempStock DataTable
                                dtData.Columns.Add(ClientId);
                                dtData.Columns.Add(BomId);
                                dtData.Columns.Add(CreatedBy);
                                dtData.Columns.Add(orgFilename);
                                dtData.Columns.Add(genFilename);
                                foreach (DataRow dr in dtData.Rows)
                                {
                                    DataRow appendStockfiled = dtData.NewRow();
                                    dr["ClientId"] = clientId;
                                    dr["BomId"] = bomId;
                                    dr["CreatedBy"] = userId;
                                    dr["OriginalFilename"] = originalFilename;
                                    dr["GeneratedFilename"] = generatedFilename;

                                }

                                dtData.AcceptChanges();

                                s.ColumnMappings.Add("ClientId", "ClientId");
                                s.ColumnMappings.Add("BomId", "BomId");
                                s.ColumnMappings.Add("CreatedBy", "CreatedBy");
                                s.ColumnMappings.Add("OriginalFilename", "OriginalFilename");
                                s.ColumnMappings.Add("GeneratedFilename", "GeneratedFilename");

                                s.WriteToServer(dtData);
                                tran.Commit();

                            }
                        }

                    }
                }

            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to insert stock data", sqlex);
            }

        }
        public override DataTable GetLeftSideExcelSourcingResult(int displayLength, int displayStart, int sortCol, string sortDir, string search, int Clientid, int userLoginid, System.Int32 bomId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {

                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_select_LeftOfferDetails", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@displayLength", SqlDbType.Int).Value = displayLength;
                cmd.Parameters.Add("@displayStart", SqlDbType.Int).Value = displayStart;
                cmd.Parameters.Add("@sortCol", SqlDbType.Int).Value = sortCol;
                cmd.Parameters.Add("@sortDir", SqlDbType.NVarChar, 10).Value = sortDir;
                cmd.Parameters.Add("@search", SqlDbType.NVarChar, 255).Value = search;
                cmd.Parameters.Add("@clientid", SqlDbType.Int).Value = Clientid;
                cmd.Parameters.Add("@CreateBy", SqlDbType.Int).Value = userLoginid;
                cmd.Parameters.Add("@BomNo", SqlDbType.Int).Value = bomId;
                cmd.CommandTimeout = 600;

                cn.Open();
                DataSet ds = new DataSet();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);
                return ds.Tables[0];
            }
            catch (SqlException sqlex)
            {

                throw new Exception("Failed to get Import Left Side Excel Sourcing Resulet detail", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override DataTable GetRightSideExcelSourcingResult(int displayLength, int displayStart, int sortCol, string sortDir, string search, int Clientid, int userLoginid, System.Int32 bomId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_select_RightOfferDetails", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@displayLength", SqlDbType.Int).Value = displayLength;
                cmd.Parameters.Add("@displayStart", SqlDbType.Int).Value = displayStart;
                cmd.Parameters.Add("@sortCol", SqlDbType.Int).Value = sortCol;
                cmd.Parameters.Add("@sortDir", SqlDbType.NVarChar, 10).Value = sortDir;
                cmd.Parameters.Add("@search", SqlDbType.NVarChar, 255).Value = search;
                cmd.Parameters.Add("@clientid", SqlDbType.Int).Value = Clientid;
                cmd.Parameters.Add("@CreateBy", SqlDbType.Int).Value = userLoginid;
                cmd.Parameters.Add("@BomNo", SqlDbType.Int).Value = bomId;
                cmd.CommandTimeout = 600;

                cn.Open();
                DataSet ds = new DataSet();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);
                return ds.Tables[0];
            }
            catch (SqlException sqlex)
            {

                throw new Exception("Failed to get Import Right Side Excel Sourcing Resulet detail", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override int DeleteLeftAndRightData(System.Int32 userId, int ClientId, System.Int32 bomId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {

                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_deleteTempRecordfromLeftandRight", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 600;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = ClientId;
                cmd.Parameters.Add("@UserId", SqlDbType.Int).Value = userId;
                cmd.Parameters.Add("@BomNo", SqlDbType.Int).Value = bomId;
                cn.Open();
                return ExecuteNonQuery(cmd);

            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to Delete the record from sourcing temp table detail", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override int BomReProcessData(System.Int32 bomId, System.Int32 userId, int ClientId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {

                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_Multivendor_Process_Offers", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 600;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = ClientId;
                cmd.Parameters.Add("@UserId", SqlDbType.Int).Value = userId;
                cmd.Parameters.Add("@BomNo", SqlDbType.Int).Value = bomId;
                cn.Open();
                return ExecuteNonQuery(cmd);

            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to Re-ProcessData the Bom Offer and Requerment Details", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override DataTable GetStatusFileExitOrNot(System.Int32 bomId, int ClientId, int userLoginid, string originalFilename)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_select_StatusFileExitOrNot", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = ClientId;
                cmd.Parameters.Add("@UserId", SqlDbType.Int).Value = userLoginid;
                cmd.Parameters.Add("@BomNo", SqlDbType.Int).Value = bomId;
                cmd.Parameters.Add("@originalFilename", SqlDbType.NVarChar, 255).Value = originalFilename;
                cmd.CommandTimeout = 600;

                cn.Open();
                DataSet ds = new DataSet();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);
                return ds.Tables[0];
            }
            catch (SqlException sqlex)
            {

                throw new Exception("Failed to get Status File Exit Or Not in Database", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override int updatecellvalueMfrVender(System.Int32 userId, int ClientId, System.Int32? SourcingResultId, string updatestatus, string manufacturename, string Vendor)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {

                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_SaveCellUpdateTempoffer", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 600;
                cmd.Parameters.Add("@SourcingResultId", SqlDbType.Int).Value = SourcingResultId;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = ClientId;
                cmd.Parameters.Add("@UserId", SqlDbType.Int).Value = userId;
                cmd.Parameters.Add("@QuotedMFR", SqlDbType.NVarChar, 3000).Value = manufacturename;
                cmd.Parameters.Add("@Vendor", SqlDbType.NVarChar, 100).Value = Vendor;
                cmd.Parameters.Add("@Updatestatus", SqlDbType.NVarChar, 100).Value = updatestatus;
                cn.Open();
                return ExecuteNonQuery(cmd);

            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to Delete the record from sourcing temp table detail", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override int updatecellvalueMfr(System.Int32 userId, int ClientId, System.Int32? SourcingResultId, string manufacturename)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {

                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_SaveCellUpdateTempleft", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 600;
                cmd.Parameters.Add("@SourcingResultId", SqlDbType.Int).Value = SourcingResultId;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = ClientId;
                cmd.Parameters.Add("@UserId", SqlDbType.Int).Value = userId;
                cmd.Parameters.Add("@MFR", SqlDbType.NVarChar, 3000).Value = manufacturename;
                cn.Open();
                return ExecuteNonQuery(cmd);

            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to Delete the record from sourcing temp table detail", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        //////////////////////Sourcing Resule///File///// Upload/////////////////////end//////////
        public override DataTable GetBOMGenrateTempData(int displayLength, int displayStart, int sortCol, string sortDir, string search, int userid, int Clientid, int SelectedclientId, string ColumnList, string ddlCurancy, string CompanyNameText, int clientType_con, string Column_Lable, string Column_Name, string ContactText, bool OverRideCurrency, string DefaultCurrencyName, string CurrencyColumnName, int DefaultCurrencyId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_GetDynamicBOMHeaderColumn", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@displayLength", SqlDbType.Int).Value = displayLength;
                cmd.Parameters.Add("@displayStart", SqlDbType.Int).Value = displayStart;
                cmd.Parameters.Add("@sortCol", SqlDbType.Int).Value = sortCol;
                cmd.Parameters.Add("@sortDir", SqlDbType.NVarChar, 10).Value = sortDir;//[004]  
                cmd.Parameters.Add("@search", SqlDbType.NVarChar, 255).Value = search;//[004]  
                cmd.Parameters.Add("@userid", SqlDbType.Int).Value = userid;//[003]
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = Clientid;//[003]
                cmd.Parameters.Add("@SelectedClientId", SqlDbType.Int).Value = SelectedclientId;//[003]
                cmd.Parameters.Add("@list_Label_name", SqlDbType.NVarChar).Value = Column_Lable;
                cmd.Parameters.Add("@list_column_name", SqlDbType.NVarChar).Value = Column_Name;
                cmd.Parameters.Add("@SelectColumns", SqlDbType.NVarChar).Value = ColumnList;
                cmd.Parameters.Add("@CurancyColumns", SqlDbType.NVarChar).Value = ddlCurancy;
                cmd.Parameters.Add("@CompanyNameText", SqlDbType.NVarChar).Value = CompanyNameText;
                cmd.Parameters.Add("@ContactNameText", SqlDbType.NVarChar).Value = ContactText;
                cmd.Parameters.Add("@OverRideCurrency", SqlDbType.Bit).Value = OverRideCurrency;
                cmd.Parameters.Add("@DefaultCurrecy", SqlDbType.NVarChar).Value = DefaultCurrencyName;
                cmd.Parameters.Add("@CurrencyColumn", SqlDbType.NVarChar).Value = CurrencyColumnName;
                cmd.Parameters.Add("@DefaultCurrecyNo", SqlDbType.Int).Value = DefaultCurrencyId;


                //cmd.CommandTimeout = 60;
                cmd.CommandTimeout = 600;

                cn.Open();
                DataSet ds = new DataSet();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);
                // changes done to catch exception from SP
                if (ds.Tables.Count == 1)   // if exception is thrown
                    return ds.Tables[0];
                else
                    return ds.Tables[ds.Tables.Count - 1];   // if no exception then multiple tables collection return so picking last table
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception(sqlex.Message);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override DataTable GetBOMManagerGenrateTempData(int displayLength, int displayStart, int sortCol, string sortDir, string search, int userid, int Clientid, int SelectedclientId, string ColumnList, string ddlCurancy, string CompanyNameText, int clientType_con, string Column_Lable, string Column_Name, string ContactText, bool OverRideCurrency, string DefaultCurrencyName, string CurrencyColumnName, int DefaultCurrencyId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_GetDynamicBOMManagerHeaderColumn", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@displayLength", SqlDbType.Int).Value = displayLength;
                cmd.Parameters.Add("@displayStart", SqlDbType.Int).Value = displayStart;
                cmd.Parameters.Add("@sortCol", SqlDbType.Int).Value = sortCol;
                cmd.Parameters.Add("@sortDir", SqlDbType.NVarChar, 10).Value = sortDir;//[004]  
                cmd.Parameters.Add("@search", SqlDbType.NVarChar, 255).Value = search;//[004]  
                cmd.Parameters.Add("@userid", SqlDbType.Int).Value = userid;//[003]
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = Clientid;//[003]
                cmd.Parameters.Add("@SelectedClientId", SqlDbType.Int).Value = SelectedclientId;//[003]
                cmd.Parameters.Add("@list_Label_name", SqlDbType.NVarChar).Value = Column_Lable;
                cmd.Parameters.Add("@list_column_name", SqlDbType.NVarChar).Value = Column_Name;
                cmd.Parameters.Add("@SelectColumns", SqlDbType.NVarChar).Value = ColumnList;
                cmd.Parameters.Add("@CurancyColumns", SqlDbType.NVarChar).Value = ddlCurancy;
                cmd.Parameters.Add("@CompanyNameText", SqlDbType.NVarChar).Value = CompanyNameText;
                cmd.Parameters.Add("@ContactNameText", SqlDbType.NVarChar).Value = ContactText;
                cmd.Parameters.Add("@OverRideCurrency", SqlDbType.Bit).Value = OverRideCurrency;
                cmd.Parameters.Add("@DefaultCurrecy", SqlDbType.NVarChar).Value = DefaultCurrencyName;
                cmd.Parameters.Add("@CurrencyColumn", SqlDbType.NVarChar).Value = CurrencyColumnName;
                cmd.Parameters.Add("@DefaultCurrecyNo", SqlDbType.Int).Value = DefaultCurrencyId;


                //cmd.CommandTimeout = 60;
                cmd.CommandTimeout = 600;

                cn.Open();
                DataSet ds = new DataSet();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);
                // changes done to catch exception from SP
                if (ds.Tables.Count == 1)   // if exception is thrown
                    return ds.Tables[0];
                else
                    return ds.Tables[ds.Tables.Count - 1];   // if no exception then multiple tables collection return so picking last table
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception(sqlex.Message);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override List<HUBOfferImportLargeFileTempDetails> GetGTVendorMFRHUBOfferImportLargeFileTemp(string ids)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_select_GTVendorMFR_tbOfferImportByExcelTemp", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;
                cmd.Parameters.Add("@OfferedIds", SqlDbType.NVarChar).Value = ids;

                cn.Open();
                SqlDataReader reader = cmd.ExecuteReader();
                List<HUBOfferImportLargeFileTempDetails> lst = new List<HUBOfferImportLargeFileTempDetails>();

                while (reader.Read())
                {
                    HUBOfferImportLargeFileTempDetails obj = new HUBOfferImportLargeFileTempDetails();
                    obj.ClientNo = GetReaderValue_Int32(reader, "ClientNo", 0);
                    obj.COST = GetReaderValue_String(reader, "COST", "");
                    obj.DLUP = GetReaderValue_DateTime(reader, "DLUP", DateTime.MinValue);
                    // obj.GoodsInNo = GetReaderValue_Int32(reader, "GoodsInNo", 0);
                    obj.GeneratedFileName = GetReaderValue_String(reader, "GeneratedFileName", "");
                    obj.HUBOfferImportLargeFileID = GetReaderValue_Int32(reader, "HUBOfferImportLargeFileID", 0);
                    obj.LeadTime = GetReaderValue_String(reader, "LeadTime", "");
                    obj.MFR = GetReaderValue_String(reader, "MFR", "");
                    obj.MOQ = GetReaderValue_String(reader, "MOQ", "");
                    obj.MPN = GetReaderValue_String(reader, "MPN", "");
                    obj.OfferedDate = GetReaderValue_String(reader, "OfferedDate", "");
                    obj.OfferTempId = GetReaderValue_Int32(reader, "OfferTempId", 0);
                    obj.SPQ = GetReaderValue_String(reader, "SPQ", "");
                    obj.Remarks = GetReaderValue_String(reader, "Remarks", "");
                    obj.Vendor = GetReaderValue_String(reader, "Vendor", "");
                    obj.GTMFR = GetReaderValue_String(reader, "GTMFR", "");
                    obj.GTVendor = GetReaderValue_String(reader, "GTVendor", "");

                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get HUBOfferImportLargeFileTemp", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        /// <summary>
        /// call usp_select_HUBOfferImportLargeFile_Temp_Invalid
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public override int CountInvalidHUBOfferImportLargeFileTemp(int id)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_select_HUBOfferImportLargeFile_Temp_Invalid", cn);
                //cmd = new SqlCommand("procname", cn);

                cmd.CommandType = CommandType.StoredProcedure;
                //code start [010]
                cmd.CommandTimeout = 900;
                //code end [010]
                cmd.Parameters.Add("@FileId", SqlDbType.Int).Value = id;
                cmd.Parameters.Add("@RecordCount", SqlDbType.Int).Direction = ParameterDirection.Output;

                cn.Open();

                //return ExecuteNonQuery(cmd);
                ExecuteNonQuery(cmd);
                return (Int32)cmd.Parameters["@RecordCount"].Value;
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to Generate GT Vendor MFR HUB Offer Import Large File Temp", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override int UpdateHUBOfferImportLargeFileStatus(int id, string status, int updatedBy)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_update_HUBOfferImportLargeFile_Status", cn);
                //cmd = new SqlCommand("procname", cn);

                cmd.CommandType = CommandType.StoredProcedure;
                //code start [010]
                cmd.CommandTimeout = 900;
                //code end [010]
                cmd.Parameters.Add("@FileId", SqlDbType.Int).Value = id;
                cmd.Parameters.Add("@Status", SqlDbType.NVarChar).Value = status;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.NVarChar).Value = updatedBy;
                cmd.Parameters.Add("@RecordCount", SqlDbType.Int).Direction = ParameterDirection.Output;

                cn.Open();

                //return ExecuteNonQuery(cmd);
                ExecuteNonQuery(cmd);
                return (Int32)cmd.Parameters["@RecordCount"].Value;
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to Generate GT Vendor MFR HUB Offer Import Large File Temp", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override int GenerateGTVendorMFRHUBOfferImportLargeFileTemp(int id)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_update_GTVendorMFR_HUBOfferImportLargeFile", cn);
                //cmd = new SqlCommand("procname", cn);

                cmd.CommandType = CommandType.StoredProcedure;
                //code start [010]
                cmd.CommandTimeout = 900;
                //code end [010]
                cmd.Parameters.Add("@FileId", SqlDbType.NVarChar).Value = id;
                cmd.Parameters.Add("@RecordCount", SqlDbType.Int).Direction = ParameterDirection.Output;

                cn.Open();

                //return ExecuteNonQuery(cmd);
                ExecuteNonQuery(cmd);
                return (Int32)cmd.Parameters["@RecordCount"].Value;
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to Generate GT Vendor MFR HUB Offer Import Large File Temp", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override int CheckExistHUBOfferImportLarge(string originalFileName)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_check_exist_HUBOfferImportLargeFile", cn);
                //cmd = new SqlCommand("procname", cn);

                cmd.CommandType = CommandType.StoredProcedure;
                //code start [010]
                cmd.CommandTimeout = 900;
                //code end [010]
                cmd.Parameters.Add("@original_file_name", SqlDbType.NVarChar).Value = originalFileName;
                cmd.Parameters.Add("@RecordCount", SqlDbType.Int).Direction = ParameterDirection.Output;

                cn.Open();

                //return ExecuteNonQuery(cmd);
                ExecuteNonQuery(cmd);
                return (Int32)cmd.Parameters["@RecordCount"].Value;
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to Check Exist HUB Offer Import Large", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override int saveHUBOfferImportLarge(int userId, int ClientId, string originalFileName, string generatedFileName, string generatedErrorFileName,string status)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_insert_HUBOfferImportLargeFile", cn);
                //cmd = new SqlCommand("procname", cn);

                cmd.CommandType = CommandType.StoredProcedure;
                //code start [010]
                cmd.CommandTimeout = 900;
                //code end [010]
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = ClientId;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = userId;
                cmd.Parameters.Add("@original_file_name", SqlDbType.NVarChar).Value = originalFileName;
                cmd.Parameters.Add("@generated_file_name", SqlDbType.NVarChar).Value = generatedFileName;
                cmd.Parameters.Add("@generated_error_file", SqlDbType.NVarChar).Value = generatedErrorFileName;
                cmd.Parameters.Add("@status", SqlDbType.NVarChar).Value = status;
                cmd.Parameters.Add("@RecordCount", SqlDbType.Int).Direction = ParameterDirection.Output;
                cmd.Parameters.Add("@IDOut", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();

                //return ExecuteNonQuery(cmd);
                ExecuteNonQuery(cmd);
                return (Int32)cmd.Parameters["@IDOut"].Value;
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to save HUB Offer Import Large", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override int UpdateHUBOfferImportLargeFileTempList(string jsonData, int updatedBy)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_update_HUBOfferImportLargeFile_Temp", cn);

                cmd.CommandType = CommandType.StoredProcedure;
                //code start [010]
                cmd.CommandTimeout = 900;
                //code end [010]
                cmd.Parameters.Add("@JsonData", SqlDbType.NVarChar).Value = jsonData;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@RecordCount", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();

                //return ExecuteNonQuery(cmd);
                ExecuteNonQuery(cmd);
                return (Int32)cmd.Parameters["@RecordCount"].Value;
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to Update HUB Offer Import Large File Temp List", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override HUBOfferImportLargeFileTempListDetails GetHUBOfferImportLargeFileTempList(int fileId, bool isShowMismatchOnly, int pageNumber, int pageSize)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_select_tbOfferImportByExcelTemp", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;
                cmd.Parameters.Add("@FileID", SqlDbType.Int).Value = fileId;
                cmd.Parameters.Add("@PageNumber", SqlDbType.Int).Value = pageNumber;
                cmd.Parameters.Add("@PageSize", SqlDbType.Int).Value = pageSize;
                cmd.Parameters.Add("@ShowMismatchOnly", SqlDbType.Bit).Value = isShowMismatchOnly;

                cn.Open();
                SqlDataReader reader = cmd.ExecuteReader();
                HUBOfferImportLargeFileTempListDetails lst = new HUBOfferImportLargeFileTempListDetails();
                lst.lstHUBOfferImportLargeFileTemp = new List<HUBOfferImportLargeFileTempDetails>();

                while (reader.Read())
                {
                    HUBOfferImportLargeFileTempDetails obj = new HUBOfferImportLargeFileTempDetails();
                    obj.ClientNo = GetReaderValue_Int32(reader, "ClientNo", 0);
                    obj.COST = GetReaderValue_String(reader, "COST", "");
                    obj.DLUP = GetReaderValue_DateTime(reader, "DLUP", DateTime.MinValue);
                    // obj.GoodsInNo = GetReaderValue_Int32(reader, "GoodsInNo", 0);
                    obj.GeneratedFileName = GetReaderValue_String(reader, "GeneratedFileName", "");
                    obj.HUBOfferImportLargeFileID = GetReaderValue_Int32(reader, "HUBOfferImportLargeFileID", 0);
                    obj.LeadTime = GetReaderValue_String(reader, "LeadTime", "");
                    obj.MFR = GetReaderValue_String(reader, "MFR", "");
                    obj.MOQ = GetReaderValue_String(reader, "MOQ", "");
                    obj.MPN = GetReaderValue_String(reader, "MPN", "");
                    obj.OfferedDate = GetReaderValue_String(reader, "OfferedDate", "");
                    obj.OfferTempId = GetReaderValue_Int32(reader, "OfferTempId", 0);
                    obj.SPQ = GetReaderValue_String(reader, "SPQ", "");
                    obj.Remarks = GetReaderValue_String(reader, "Remarks", "");
                    obj.Vendor = GetReaderValue_String(reader, "Vendor", "");
                    obj.GTMFR = GetReaderValue_String(reader, "GTMFR", "");
                    obj.GTVendor = GetReaderValue_String(reader, "GTVendor", "");

                    obj.LeadTimeMessage = GetReaderValue_String(reader, "LeadTimeMessage", "");
                    obj.MFRMessage = GetReaderValue_String(reader, "MFRMessage", "");
                    obj.MOQMessage = GetReaderValue_String(reader, "MOQMessage", "");
                    obj.MPNMessage = GetReaderValue_String(reader, "MPNMessage", "");
                    obj.OfferedDateMessage = GetReaderValue_String(reader, "OfferedDateMessage", "");
                    obj.SPQMessage = GetReaderValue_String(reader, "SPQMessage", "");
                    obj.RemarksMessage = GetReaderValue_String(reader, "RemarksMessage", "");
                    obj.VendorMessage = GetReaderValue_String(reader, "VendorMessage", "");
                    obj.COSTMessage = GetReaderValue_String(reader, "COSTMessage", "");
                    lst.lstHUBOfferImportLargeFileTemp.Add(obj);
                    obj = null;
                }
                if (reader.NextResult())
                {
                    // Second result set: Total count
                    while (reader.Read())
                    {
                        lst.TotalRecords = GetReaderValue_Int32(reader, "TotalCount", 0);
                    }
                }
                if (reader.NextResult())
                {
                    // Third result set: Total count
                    while (reader.Read())
                    {
                        lst.TotalRecordsError = GetReaderValue_Int32(reader, "TotalRecordsError", 0);
                    }
                }
                
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get HUBOfferImportLargeFileTemp", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override int SaveHUBOfferImportLargeTemp(DataTable datatable)
        {
            int timeOut = 900;
            string ConString = ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString();

            using (SqlBulkCopy bulkCopy = new SqlBulkCopy(ConString, SqlBulkCopyOptions.FireTriggers))
            {
                foreach (DataColumn c in datatable.Columns)
                {
                    var columnName = c.ColumnName;
                    switch (columnName.ToUpper())
                    {
                        case "MPN": columnName = "MPN"; break;
                        case "MFR": columnName = "MFR"; break;
                        case "COST": columnName = "COST"; break;
                        case "LEADTIME": columnName = "LeadTime"; break;
                        case "SPQ": columnName = "SPQ"; break;
                        case "MOQ": columnName = "MOQ"; break;
                        case "REMARKS": columnName = "Remarks"; break;
                        case "OFFEREDDATE": columnName = "OfferedDate"; break;
                        case "VENDOR": columnName = "Vendor"; break;
                    }
                    bulkCopy.ColumnMappings.Add(columnName, columnName);
                }
                    

                bulkCopy.DestinationTableName = "BorisGlobalTraderImports.dbo.tbOfferImportByExcelTemp";
                bulkCopy.BulkCopyTimeout = timeOut;
                try
                {
                    bulkCopy.WriteToServer(datatable);
                }
                catch (SqlException sqlex)
                {
                    throw new Exception("Failed to insert bulk upload tbOfferImportByExcelTemp", sqlex);
                }

            }
            return datatable.Rows.Count;
        }

        public override int saveBOMImportData(int userId,
                                              int ClientId,
                                              int SelectedclientId,
                                              string Column_Lable,
                                              string Column_Name,
                                              string insertDataList,
                                              string fileColName,
                                              string ddlCurrency,
                                              out System.String errorMessage,
                                              string BomName,
                                              string CompanyName,
                                              string ContactName,
                                              int SalesmanId,
                                              int CompanyId,
                                              int ContactId,
                                              bool PartWatch,
                                              int DefaultCurrencyId,
                                              bool OverRideCurrency,
                                              string SaveImportOrHubRFQ,
                                              out System.String NewBomCode,
                                              out System.Int32 NewBomid,
                                              System.Int32? ReqforTraceabilityId,
                                              System.Int32? TypeId,
                                              System.DateTime DateRequired,
                                              int? updateBomId,
                                              string originalFileName,
                                              string generatedFileName)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            errorMessage = string.Empty;
            NewBomCode = string.Empty;
            NewBomid = 0;
            try
            {

                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_Select_ImportBOMData", cn);

                cmd.CommandType = CommandType.StoredProcedure;
                //code start [010]
                cmd.CommandTimeout = 900;
                //code end [010]
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = ClientId;
                cmd.Parameters.Add("@UserId", SqlDbType.Int).Value = userId;
                cmd.Parameters.Add("@SelectedClientID", SqlDbType.Int).Value = SelectedclientId;
                cmd.Parameters.Add("@list_Label_name", SqlDbType.NVarChar).Value = Column_Lable;
                cmd.Parameters.Add("@list_column_name", SqlDbType.NVarChar).Value = Column_Name;
                cmd.Parameters.Add("@insertDataList", SqlDbType.NVarChar).Value = insertDataList;
                cmd.Parameters.Add("@fileColName", SqlDbType.NVarChar).Value = fileColName;
                cmd.Parameters.Add("@CurrencyName", SqlDbType.NVarChar).Value = ddlCurrency;
                cmd.Parameters.Add("@RecordCount", SqlDbType.Int).Direction = ParameterDirection.Output;
                cmd.Parameters.Add("@ErrorMessage", SqlDbType.VarChar, 400).Direction = ParameterDirection.Output;
                cmd.Parameters.Add("@BomName", SqlDbType.NVarChar).Value = BomName;
                cmd.Parameters.Add("@CompanyName", SqlDbType.NVarChar).Value = CompanyName;
                cmd.Parameters.Add("@ContactName", SqlDbType.NVarChar).Value = ContactName;
                cmd.Parameters.Add("@SalesmanId", SqlDbType.Int).Value = SalesmanId;
                cmd.Parameters.Add("@CompanyId", SqlDbType.Int).Value = CompanyId;
                cmd.Parameters.Add("@ContactId", SqlDbType.Int).Value = ContactId;
                cmd.Parameters.Add("@PartWatch", SqlDbType.Bit).Value = PartWatch;
                cmd.Parameters.Add("@DefaultCurrencyId", SqlDbType.Int).Value = DefaultCurrencyId;
                cmd.Parameters.Add("@OverRideCurrency", SqlDbType.Bit).Value = OverRideCurrency;
                cmd.Parameters.Add("@ImportOrHubRFQ", SqlDbType.NVarChar).Value = SaveImportOrHubRFQ;
                cmd.Parameters.Add("@ReqforTraceabilityId", SqlDbType.Int).Value = ReqforTraceabilityId;
                cmd.Parameters.Add("@TypeId", SqlDbType.Int).Value = TypeId;
                cmd.Parameters.Add("@DateRequired", SqlDbType.DateTime).Value = DateRequired;
                cmd.Parameters.Add("@UpdateBomID", SqlDbType.Int).Value = updateBomId;
                cmd.Parameters.Add("@OriginalFileName", SqlDbType.NVarChar).Value = originalFileName;
                cmd.Parameters.Add("@GeneratedFilename", SqlDbType.NVarChar).Value = generatedFileName;
                cmd.Parameters.Add("@NewBomID", SqlDbType.Int).Direction = ParameterDirection.Output;
                cmd.Parameters.Add("@NewBomCode", SqlDbType.VarChar, 100).Direction = ParameterDirection.Output;

                cn.Open();
                //return ExecuteNonQuery(cmd);
                ExecuteNonQuery(cmd);
                errorMessage = Convert.ToString(cmd.Parameters["@ErrorMessage"].Value);
                NewBomCode = Convert.ToString(cmd.Parameters["@NewBomCode"].Value);
                NewBomid = Convert.ToInt32(cmd.Parameters["@NewBomID"].Value);
                return (Int32)cmd.Parameters["@RecordCount"].Value;


            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to insert mapping Column detail", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        //Bom Manager Final insert
        public override int saveBOMManagerImportData(int userId, int ClientId, int SelectedclientId, string Column_Lable, string Column_Name, string insertDataList, string fileColName, string ddlCurrency, out System.String errorMessage, string BomName, string CompanyName, string ContactName, int SalesmanId, int CompanyId, int ContactId, bool PartWatch, int DefaultCurrencyId, bool OverRideCurrency, string SaveImportOrHubRFQ, out System.String NewBomCode, out System.Int32 NewBomid, System.Int32? ReqforTraceabilityId, System.Int32? TypeId, System.DateTime DateRequired, System.Int32? LastMappingId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            errorMessage = string.Empty;
            NewBomCode = string.Empty;
            NewBomid = 0;
            try
            {

                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_Insert_BomManagerData", cn);
                //cmd = new SqlCommand("procname", cn);

                cmd.CommandType = CommandType.StoredProcedure;
                //code start [010]
                cmd.CommandTimeout = 900;
                //code end [010]
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = ClientId;
                cmd.Parameters.Add("@UserId", SqlDbType.Int).Value = userId;
                cmd.Parameters.Add("@SelectedClientID", SqlDbType.Int).Value = SelectedclientId;
                cmd.Parameters.Add("@list_Label_name", SqlDbType.NVarChar).Value = Column_Lable;
                cmd.Parameters.Add("@list_column_name", SqlDbType.NVarChar).Value = Column_Name;
                cmd.Parameters.Add("@insertDataList", SqlDbType.NVarChar).Value = insertDataList;
                cmd.Parameters.Add("@fileColName", SqlDbType.NVarChar).Value = fileColName;
                cmd.Parameters.Add("@CurrencyName", SqlDbType.NVarChar).Value = ddlCurrency;
                cmd.Parameters.Add("@RecordCount", SqlDbType.Int).Direction = ParameterDirection.Output;
                cmd.Parameters.Add("@ErrorMessage", SqlDbType.VarChar, 400).Direction = ParameterDirection.Output;
                cmd.Parameters.Add("@BomName", SqlDbType.NVarChar).Value = BomName;
                cmd.Parameters.Add("@CompanyName", SqlDbType.NVarChar).Value = CompanyName;
                cmd.Parameters.Add("@ContactName", SqlDbType.NVarChar).Value = ContactName;
                cmd.Parameters.Add("@SalesmanId", SqlDbType.Int).Value = SalesmanId;
                cmd.Parameters.Add("@CompanyId", SqlDbType.Int).Value = CompanyId;
                cmd.Parameters.Add("@ContactId", SqlDbType.Int).Value = ContactId;
                cmd.Parameters.Add("@PartWatch", SqlDbType.Bit).Value = PartWatch;
                cmd.Parameters.Add("@DefaultCurrencyId", SqlDbType.Int).Value = DefaultCurrencyId;
                cmd.Parameters.Add("@OverRideCurrency", SqlDbType.Bit).Value = OverRideCurrency;
                cmd.Parameters.Add("@ImportOrHubRFQ", SqlDbType.NVarChar).Value = SaveImportOrHubRFQ;
                cmd.Parameters.Add("@ReqforTraceabilityId", SqlDbType.Int).Value = ReqforTraceabilityId;
                cmd.Parameters.Add("@TypeId", SqlDbType.Int).Value = TypeId;
                cmd.Parameters.Add("@DateRequired", SqlDbType.DateTime).Value = DateRequired;
                cmd.Parameters.Add("@LastMappingId", SqlDbType.Int).Value = LastMappingId;
                cmd.Parameters.Add("@NewBomID", SqlDbType.Int).Direction = ParameterDirection.Output;
                cmd.Parameters.Add("@NewBomCode", SqlDbType.VarChar, 100).Direction = ParameterDirection.Output;

                cn.Open();
                //return ExecuteNonQuery(cmd);
                ExecuteNonQuery(cmd);
                errorMessage = Convert.ToString(cmd.Parameters["@ErrorMessage"].Value);
                NewBomCode = Convert.ToString(cmd.Parameters["@NewBomCode"].Value);
                NewBomid = Convert.ToInt32(cmd.Parameters["@NewBomID"].Value);
                return (Int32)cmd.Parameters["@RecordCount"].Value;


            }
            catch (SqlException sqlex)
            {
                throw new Exception(sqlex.Message, sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override DataTable GetBOMDetailFromTemp(int displayLength, int displayStart, int sortCol, string sortDir, string search, int userid, int Clientid, int SelectedclientId, int clientType_con)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {

                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_GetBOMNewData", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@displayLength", SqlDbType.Int).Value = displayLength;
                cmd.Parameters.Add("@displayStart", SqlDbType.Int).Value = displayStart;
                cmd.Parameters.Add("@sortCol", SqlDbType.Int).Value = sortCol;
                cmd.Parameters.Add("@sortDir", SqlDbType.NVarChar, 10).Value = sortDir;//[004]  
                cmd.Parameters.Add("@search", SqlDbType.NVarChar, 255).Value = search;//[004]  
                cmd.Parameters.Add("@userid", SqlDbType.Int).Value = userid;//[003]
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = Clientid;//[003]
                cmd.Parameters.Add("@SelectedClientId", SqlDbType.Int).Value = SelectedclientId;//[003]
                //cmd.CommandTimeout = 60;
                cmd.CommandTimeout = 900;

                cn.Open();
                DataSet ds = new DataSet();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);
                return ds.Tables[0];
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get CustomerRequirements", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override DataTable GetBOMManagerDetailFromTemp(int displayLength, int displayStart, int sortCol, string sortDir, string search, int userid, int Clientid, int SelectedclientId, int clientType_con)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {

                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_GetBOMManagerNewData", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@displayLength", SqlDbType.Int).Value = displayLength;
                cmd.Parameters.Add("@displayStart", SqlDbType.Int).Value = displayStart;
                cmd.Parameters.Add("@sortCol", SqlDbType.Int).Value = sortCol;
                cmd.Parameters.Add("@sortDir", SqlDbType.NVarChar, 10).Value = sortDir;//[004]  
                cmd.Parameters.Add("@search", SqlDbType.NVarChar, 255).Value = search;//[004]  
                cmd.Parameters.Add("@userid", SqlDbType.Int).Value = userid;//[003]
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = Clientid;//[003]
                cmd.Parameters.Add("@SelectedClientId", SqlDbType.Int).Value = SelectedclientId;//[003]
                //cmd.CommandTimeout = 60;
                cmd.CommandTimeout = 900;

                cn.Open();
                DataSet ds = new DataSet();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);
                return ds.Tables[0];
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get CustomerRequirements", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override void SaveBOMExcelHeader(string columnList, string insertColumnList, System.Int32? clientId, int? SelectedclientId, int? loginId, int clientType_con)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_SaveBOMExcelColumn", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 600;
                cmd.Parameters.Add("@SelectColumns", SqlDbType.NVarChar, 3000).Value = columnList;
                cmd.Parameters.Add("@InserColumnList", SqlDbType.NVarChar, 3000).Value = insertColumnList;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@SelectedClientId", SqlDbType.Int).Value = SelectedclientId;
                cmd.Parameters.Add("@UserId", SqlDbType.Int).Value = loginId;

                cn.Open();
                cmd.ExecuteNonQuery();

            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to insert header info", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override void SaveBOMExcelHeaderManager(string columnList, string insertColumnList, System.Int32? clientId, int? SelectedclientId, int? loginId, int clientType_con)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_SaveBOMManagerExcelColumn", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 600;
                cmd.Parameters.Add("@SelectColumns", SqlDbType.NVarChar, 3000).Value = columnList;
                cmd.Parameters.Add("@InserColumnList", SqlDbType.NVarChar, 3000).Value = insertColumnList;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@SelectedClientId", SqlDbType.Int).Value = SelectedclientId;
                cmd.Parameters.Add("@UserId", SqlDbType.Int).Value = loginId;

                cn.Open();
                cmd.ExecuteNonQuery();

            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to insert header info", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override void saveBOMExcelBulkSave(DataTable tempStock, DataTable dtData, string originalFilename, string generatedFilename, System.Int32 userId, System.Int32 clientId, int? SelectedclientId, int clientType_con, string DefaultCurrency)
        {

            try
            {

                using (SqlConnection dbConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString()))
                {
                    dbConnection.Open();
                    if (dbConnection.State == ConnectionState.Open)
                    {


                        using (var tran = dbConnection.BeginTransaction(IsolationLevel.ReadCommitted))
                        {
                            using (SqlBulkCopy s = new SqlBulkCopy(dbConnection, SqlBulkCopyOptions.Default, tran))
                            {
                                s.DestinationTableName = tempStock.TableName;
                                int i = 1;
                                foreach (var column in dtData.Columns)
                                {

                                    if (i <= 15)
                                    {
                                        s.ColumnMappings.Add(column.ToString(), "Column" + i.ToString());

                                    }
                                    i++;
                                }

                                DataColumn ClientId = new DataColumn();
                                ClientId.DataType = typeof(int);
                                ClientId.ColumnName = "ClientId";


                                DataColumn SelectedClientId = new DataColumn();
                                SelectedClientId.DataType = typeof(int);
                                SelectedClientId.ColumnName = "SelectedClientId";

                                DataColumn CreatedBy = new DataColumn();
                                CreatedBy.DataType = typeof(int);
                                CreatedBy.ColumnName = "CreatedBy";

                                DataColumn orgFilename = new DataColumn();
                                orgFilename.DataType = typeof(string);
                                orgFilename.ColumnName = "OriginalFilename";

                                DataColumn genFilename = new DataColumn();
                                genFilename.DataType = typeof(string);
                                genFilename.ColumnName = "GeneratedFilename";

                                DataColumn DefaultCurrencyname = new DataColumn();
                                DefaultCurrencyname.DataType = typeof(string);
                                DefaultCurrencyname.ColumnName = "DefaultCurrency";


                                // Add the columns to the tempStock DataTable
                                dtData.Columns.Add(ClientId);
                                dtData.Columns.Add(SelectedClientId);
                                dtData.Columns.Add(CreatedBy);
                                dtData.Columns.Add(orgFilename);
                                dtData.Columns.Add(genFilename);
                                dtData.Columns.Add(DefaultCurrencyname);
                                foreach (DataRow dr in dtData.Rows)
                                {
                                    //  DataRow appendStockfiled = dtData.NewRow();
                                    dr["ClientId"] = clientId;
                                    dr["SelectedClientId"] = SelectedclientId;
                                    dr["CreatedBy"] = userId;
                                    dr["OriginalFilename"] = originalFilename;
                                    dr["GeneratedFilename"] = generatedFilename;
                                    dr["DefaultCurrency"] = DefaultCurrency;

                                }

                                dtData.AcceptChanges();

                                s.ColumnMappings.Add("ClientId", "ClientId");
                                s.ColumnMappings.Add("SelectedClientId", "SelectedClientId");
                                s.ColumnMappings.Add("CreatedBy", "CreatedBy");
                                s.ColumnMappings.Add("OriginalFilename", "OriginalFilename");
                                s.ColumnMappings.Add("GeneratedFilename", "GeneratedFilename");
                                s.ColumnMappings.Add("DefaultCurrency", "DefaultCurrency");

                                s.WriteToServer(dtData);
                                tran.Commit();

                            }
                        }

                    }
                }

            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to insert BOM data", sqlex);
            }

        }


        public override void saveBOMExcelBulkSaveManager(DataTable tempStock, DataTable dtData, string originalFilename, string generatedFilename, System.Int32 userId, System.Int32 clientId, int? SelectedclientId, int clientType_con, string DefaultCurrency)
        {

            try
            {

                using (SqlConnection dbConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString()))
                {
                    dbConnection.Open();
                    if (dbConnection.State == ConnectionState.Open)
                    {


                        using (var tran = dbConnection.BeginTransaction(IsolationLevel.ReadCommitted))
                        {
                            using (SqlBulkCopy s = new SqlBulkCopy(dbConnection, SqlBulkCopyOptions.Default, tran))
                            {
                                s.DestinationTableName = tempStock.TableName;
                                int i = 1;
                                foreach (var column in dtData.Columns)
                                {

                                    if (i <= 30)
                                    {
                                        s.ColumnMappings.Add(column.ToString(), "Column" + i.ToString());

                                    }
                                    i++;
                                }

                                DataColumn ClientId = new DataColumn();
                                ClientId.DataType = typeof(int);
                                ClientId.ColumnName = "ClientId";


                                DataColumn SelectedClientId = new DataColumn();
                                SelectedClientId.DataType = typeof(int);
                                SelectedClientId.ColumnName = "SelectedClientId";

                                DataColumn CreatedBy = new DataColumn();
                                CreatedBy.DataType = typeof(int);
                                CreatedBy.ColumnName = "CreatedBy";

                                DataColumn orgFilename = new DataColumn();
                                orgFilename.DataType = typeof(string);
                                orgFilename.ColumnName = "OriginalFilename";

                                DataColumn genFilename = new DataColumn();
                                genFilename.DataType = typeof(string);
                                genFilename.ColumnName = "GeneratedFilename";

                                DataColumn DefaultCurrencyname = new DataColumn();
                                DefaultCurrencyname.DataType = typeof(string);
                                DefaultCurrencyname.ColumnName = "DefaultCurrency";


                                // Add the columns to the tempStock DataTable
                                dtData.Columns.Add(ClientId);
                                dtData.Columns.Add(SelectedClientId);
                                dtData.Columns.Add(CreatedBy);
                                dtData.Columns.Add(orgFilename);
                                dtData.Columns.Add(genFilename);
                                dtData.Columns.Add(DefaultCurrencyname);
                                foreach (DataRow dr in dtData.Rows)
                                {
                                    //  DataRow appendStockfiled = dtData.NewRow();
                                    dr["ClientId"] = clientId;
                                    dr["SelectedClientId"] = SelectedclientId;
                                    dr["CreatedBy"] = userId;
                                    dr["OriginalFilename"] = originalFilename;
                                    dr["GeneratedFilename"] = generatedFilename;
                                    dr["DefaultCurrency"] = DefaultCurrency;

                                }

                                dtData.AcceptChanges();

                                s.ColumnMappings.Add("ClientId", "ClientId");
                                s.ColumnMappings.Add("SelectedClientId", "SelectedClientId");
                                s.ColumnMappings.Add("CreatedBy", "CreatedBy");
                                s.ColumnMappings.Add("OriginalFilename", "OriginalFilename");
                                s.ColumnMappings.Add("GeneratedFilename", "GeneratedFilename");
                                s.ColumnMappings.Add("DefaultCurrency", "DefaultCurrency");

                                s.WriteToServer(dtData);
                                tran.Commit();

                            }
                        }

                    }
                }

            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to insert BOM data", sqlex);
            }

        }

        public override DataTable GetBOMExcelHeaderFrom(System.Int32 clientId, System.Int32 userId, System.Int32 SelectedClientID, int clientType_con)//GetExcelHeaderFrom
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {

                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                // cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_GetBOMExcelStockHeaderColumnFrom", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                //cmd.CommandTimeout = 60;
                cmd.CommandTimeout = 600;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@UserId", SqlDbType.Int).Value = userId;
                cmd.Parameters.Add("@SelectedClientID", SqlDbType.Int).Value = SelectedClientID;


                cn.Open();
                DataSet ds = new DataSet();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);
                return ds.Tables[0];
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get excel header", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override DataTable GetBOMManagerExcelHeaderFrom(System.Int32 clientId, System.Int32 userId, System.Int32 SelectedClientID, int clientType_con)//GetExcelHeaderFrom
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {

                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                // cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_GetBOMManagerExcelStockHeaderColumnFrom", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                //cmd.CommandTimeout = 60;
                cmd.CommandTimeout = 600;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@UserId", SqlDbType.Int).Value = userId;
                cmd.Parameters.Add("@SelectedClientID", SqlDbType.Int).Value = SelectedClientID;


                cn.Open();
                DataSet ds = new DataSet();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);
                return ds.Tables[0];
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get excel header", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override DataTable GetBOMManagerExcelHeaderFromNew(System.Int32 clientId, System.Int32 userId, System.Int32 SelectedClientID, int clientType_con)//GetExcelHeaderFrom
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {

                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                // cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_GetBOMManagerExcelStockHeaderColumnFromNew", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                //cmd.CommandTimeout = 60;
                cmd.CommandTimeout = 600;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@UserId", SqlDbType.Int).Value = userId;
                cmd.Parameters.Add("@SelectedClientID", SqlDbType.Int).Value = SelectedClientID;


                cn.Open();
                DataSet ds = new DataSet();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);
                return ds.Tables[0];
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get excel header", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override DataTable GetBOMExcelHeader(System.Int32 clientId, System.Int32 userId, System.Int32 SelectedClientID, int clientType_con)//GetExcelHeaderFrom
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {

                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_GetBOMExcelStockHeaderColumn", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                //cmd.CommandTimeout = 60;
                cmd.CommandTimeout = 600;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@UserId", SqlDbType.Int).Value = userId;
                cmd.Parameters.Add("@SelectedClientID", SqlDbType.Int).Value = SelectedClientID;


                cn.Open();
                DataSet ds = new DataSet();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);
                return ds.Tables[0];
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get excel header", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override DataTable GetBOMManagerExcelHeader(System.Int32 clientId, System.Int32 userId, System.Int32 SelectedClientID, int clientType_con)//GetExcelHeaderFrom
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {

                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_GetBOMManagerExcelStockHeaderColumn", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                //cmd.CommandTimeout = 60;
                cmd.CommandTimeout = 600;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@UserId", SqlDbType.Int).Value = userId;
                cmd.Parameters.Add("@SelectedClientID", SqlDbType.Int).Value = SelectedClientID;


                cn.Open();
                DataSet ds = new DataSet();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);
                return ds.Tables[0];
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get excel header", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override int DeleteBOMRecord(int SelectedClientID, System.Int32 userId, int ClientId, int clientType_con)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {

                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_deleteTempBOMRecord", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 600;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = ClientId;
                cmd.Parameters.Add("@UserId", SqlDbType.Int).Value = userId;
                cmd.Parameters.Add("@SelectedClientID", SqlDbType.Int).Value = SelectedClientID;
                cn.Open();
                return ExecuteNonQuery(cmd);

            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to insert mapping detail", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override int DeleteBOMManagerRecord(int SelectedClientID, System.Int32 userId, int ClientId, int clientType_con)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {

                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_deleteTempBOMManagerRecord", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 600;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = ClientId;
                cmd.Parameters.Add("@UserId", SqlDbType.Int).Value = userId;
                cmd.Parameters.Add("@SelectedClientID", SqlDbType.Int).Value = SelectedClientID;
                cn.Open();
                return ExecuteNonQuery(cmd);

            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to insert mapping detail", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override DataTable GetContactWithCurrency(int companyId)//GetExcelHeaderFrom
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_GetContactWithCurrency", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@CompanyId", SqlDbType.Int).Value = companyId;
                cmd.CommandTimeout = 60;

                cn.Open();
                DataSet ds = new DataSet();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);
                return ds.Tables[0];
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get master data", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override DataTable GetMasterDataApi(int companyId)//GetExcelHeaderFrom
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_GetMasterDataApi", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@CompanyId", SqlDbType.Int).Value = companyId;
                cmd.CommandTimeout = 60;

                cn.Open();
                DataSet ds = new DataSet();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);
                return ds.Tables[0];
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get master data", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override DataTable GetDivisionKpi(System.Int32? clientId, System.Int32? userId)
        {
            SqlConnection cn = null;
            SqlCommand cmdcur = null;
            try
            {

                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmdcur = new SqlCommand("usp_dropdown_DivisionKpi_for_Client", cn);
                cmdcur.CommandType = CommandType.StoredProcedure;
                cmdcur.CommandTimeout = 60;
                cmdcur.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmdcur.Parameters.Add("@UserId", SqlDbType.Int).Value = userId;
                cn.Open();
                SqlDataAdapter objDaFecthClient = new SqlDataAdapter(cmdcur);
                DataTable dtClient = new DataTable();
                objDaFecthClient.Fill(dtClient);
                return dtClient;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get KPI Division", sqlex);
            }
            finally
            {
                cmdcur.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override DataTable GetTeamKpi(System.Int32? clientId, System.Int32? userId)
        {
            SqlConnection cn = null;
            SqlCommand cmdcur = null;
            try
            {

                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmdcur = new SqlCommand("usp_dropdown_TeamKpi_for_Client", cn);
                cmdcur.CommandType = CommandType.StoredProcedure;
                cmdcur.CommandTimeout = 60;
                cmdcur.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmdcur.Parameters.Add("@UserId", SqlDbType.Int).Value = userId;
                cn.Open();
                SqlDataAdapter objDaFecthClient = new SqlDataAdapter(cmdcur);
                DataTable dtClient = new DataTable();
                objDaFecthClient.Fill(dtClient);
                return dtClient;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get KPI Team", sqlex);
            }
            finally
            {
                cmdcur.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        #region Editable KPI Grid Soorya
        public override DataTable GetCurrencySymbol(System.Int32? clientId, System.String CurrencyCode)
        {
            SqlConnection cn = null;
            SqlCommand cmdcur = null;
            try
            {

                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmdcur = new SqlCommand("usp_KPIGetCurrencySymbol", cn);
                cmdcur.CommandType = CommandType.StoredProcedure;
                cmdcur.CommandTimeout = 60;
                cmdcur.Parameters.Add("@ClientNo", SqlDbType.Int).Value = clientId;
                cmdcur.Parameters.Add("@CurrencyCode", SqlDbType.NVarChar).Value = CurrencyCode;
                cn.Open();
                SqlDataAdapter objDaFecthClient = new SqlDataAdapter(cmdcur);
                DataTable dtClient = new DataTable();
                objDaFecthClient.Fill(dtClient);
                return dtClient;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get symbol", sqlex);
            }
            finally
            {
                cmdcur.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override DataTable GetDivisionTeamNameByLoginid(System.Int32? clientId, System.Int32? userId, string selectKPIName)
        {
            SqlConnection cn = null;
            SqlCommand cmdcur = null;
            try
            {

                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmdcur = new SqlCommand("usp_GetDivisionTeamNameByLoginid", cn);
                cmdcur.CommandType = CommandType.StoredProcedure;
                cmdcur.CommandTimeout = 60;
                cmdcur.Parameters.Add("@ClientNo", SqlDbType.Int).Value = clientId;
                cmdcur.Parameters.Add("@loginId", SqlDbType.Int).Value = userId;
                cmdcur.Parameters.Add("@selectKPIName", SqlDbType.NVarChar).Value = selectKPIName;
                cn.Open();
                SqlDataAdapter objDaFecthClient = new SqlDataAdapter(cmdcur);
                DataTable dtClient = new DataTable();
                objDaFecthClient.Fill(dtClient);
                return dtClient;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get KPI Division", sqlex);
            }
            finally
            {
                cmdcur.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override DataTable GetDDLDivisionKpi(System.Int32? clientId, System.Int32? userId)
        {
            SqlConnection cn = null;
            SqlCommand cmdcur = null;
            try
            {

                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmdcur = new SqlCommand("KPI_GetDivisionDropdownEditGrid", cn);
                cmdcur.CommandType = CommandType.StoredProcedure;
                cmdcur.CommandTimeout = 60;
                cmdcur.Parameters.Add("@ClientNo", SqlDbType.Int).Value = clientId;
                cmdcur.Parameters.Add("@loginId", SqlDbType.Int).Value = userId;
                cn.Open();
                SqlDataAdapter objDaFecthClient = new SqlDataAdapter(cmdcur);
                DataTable dtClient = new DataTable();
                objDaFecthClient.Fill(dtClient);
                return dtClient;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get KPI Division", sqlex);
            }
            finally
            {
                cmdcur.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override DataTable GetDDLTeamKpi(System.Int32? clientId, System.Int32? userId, System.Int32? DivisionTargetNo)
        {
            SqlConnection cn = null;
            SqlCommand cmdcur = null;
            try
            {

                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmdcur = new SqlCommand("KPI_GetTeamDropdownEditGrid", cn);
                cmdcur.CommandType = CommandType.StoredProcedure;
                cmdcur.CommandTimeout = 60;
                cmdcur.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmdcur.Parameters.Add("@UserId", SqlDbType.Int).Value = userId;
                cmdcur.Parameters.Add("@DivisionTargetNo", SqlDbType.Int).Value = DivisionTargetNo;
                cn.Open();
                SqlDataAdapter objDaFecthClient = new SqlDataAdapter(cmdcur);
                DataTable dtClient = new DataTable();
                objDaFecthClient.Fill(dtClient);
                return dtClient;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get KPI Division", sqlex);
            }
            finally
            {
                cmdcur.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override DataTable GetDDLSalesKpi(System.Int32? clientId, System.Int32? userId, System.Int32? Teamtargetno)
        {
            SqlConnection cn = null;
            SqlCommand cmdcur = null;
            try
            {

                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmdcur = new SqlCommand("KPI_GetSalesDropdownEditGrid", cn);
                cmdcur.CommandType = CommandType.StoredProcedure;
                cmdcur.CommandTimeout = 60;
                cmdcur.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmdcur.Parameters.Add("@UserId", SqlDbType.Int).Value = userId;
                cmdcur.Parameters.Add("@Teamtargetno", SqlDbType.Int).Value = Teamtargetno;
                cn.Open();
                SqlDataAdapter objDaFecthClient = new SqlDataAdapter(cmdcur);
                DataTable dtClient = new DataTable();
                objDaFecthClient.Fill(dtClient);
                return dtClient;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get KPI Division", sqlex);
            }
            finally
            {
                cmdcur.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override DataTable GetTeamKpiDivision(int DivisionID)
        {
            SqlConnection cn = null;
            SqlCommand cmdcur = null;
            try
            {

                cn = new SqlConnection(this.ConnectionString);
                cmdcur = new SqlCommand("usp_dropdown_TeamKpiDivision", cn);
                cmdcur.CommandType = CommandType.StoredProcedure;
                cmdcur.CommandTimeout = 60;
                cmdcur.Parameters.Add("@DivisionID", SqlDbType.Int).Value = DivisionID;
                cn.Open();
                SqlDataAdapter objDaFecthClient = new SqlDataAdapter(cmdcur);
                DataTable dtClient = new DataTable();
                objDaFecthClient.Fill(dtClient);
                return dtClient;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get KPI Team", sqlex);
            }
            finally
            {
                cmdcur.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override DataTable GetTeamKpiSalesman(int TeamTargetID)
        {
            SqlConnection cn = null;
            SqlCommand cmdcur = null;
            try
            {

                cn = new SqlConnection(this.ConnectionString);
                cmdcur = new SqlCommand("usp_dropdown_SalesmanKpiDivision", cn);
                cmdcur.CommandType = CommandType.StoredProcedure;
                cmdcur.CommandTimeout = 60;
                cmdcur.Parameters.Add("@TeamTargetID", SqlDbType.Int).Value = @TeamTargetID;
                cn.Open();
                SqlDataAdapter objDaFecthClient = new SqlDataAdapter(cmdcur);
                DataTable dtClient = new DataTable();
                objDaFecthClient.Fill(dtClient);
                return dtClient;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get KPI Team", sqlex);
            }
            finally
            {
                cmdcur.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        #endregion

        //1 Start
        public override void SaveStockExcelHeader(string columnList, string insertColumnList, System.Int32? clientId, int? SelectedclientId, int? loginId, int clientType_con)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_SaveStockImportExcelColumn", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 600;
                cmd.Parameters.Add("@SelectColumns", SqlDbType.NVarChar, 3000).Value = columnList;
                cmd.Parameters.Add("@InserColumnList", SqlDbType.NVarChar, 3000).Value = insertColumnList;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@SelectedClientId", SqlDbType.Int).Value = SelectedclientId;
                cmd.Parameters.Add("@UserId", SqlDbType.Int).Value = loginId;

                cn.Open();
                cmd.ExecuteNonQuery();

            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to insert header info", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override void saveStockExcelBulkSave(DataTable tempStock, DataTable dtData, string originalFilename, string generatedFilename, System.Int32 userId, System.Int32 clientId, int? SelectedclientId, int clientType_con, string DefaultCurrency)
        {

            try
            {

                using (SqlConnection dbConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString()))
                {
                    dbConnection.Open();
                    if (dbConnection.State == ConnectionState.Open)
                    {


                        using (var tran = dbConnection.BeginTransaction(IsolationLevel.ReadCommitted))
                        {
                            using (SqlBulkCopy s = new SqlBulkCopy(dbConnection, SqlBulkCopyOptions.Default, tran))
                            {
                                s.DestinationTableName = tempStock.TableName;
                                int i = 1;
                                foreach (var column in dtData.Columns)
                                {

                                    if (i <= 15)
                                    {
                                        s.ColumnMappings.Add(column.ToString(), "Column" + i.ToString());

                                    }
                                    i++;
                                }

                                DataColumn ClientId = new DataColumn();
                                ClientId.DataType = typeof(int);
                                ClientId.ColumnName = "ClientId";


                                DataColumn SelectedClientId = new DataColumn();
                                SelectedClientId.DataType = typeof(int);
                                SelectedClientId.ColumnName = "SelectedClientId";

                                DataColumn CreatedBy = new DataColumn();
                                CreatedBy.DataType = typeof(int);
                                CreatedBy.ColumnName = "CreatedBy";

                                DataColumn orgFilename = new DataColumn();
                                orgFilename.DataType = typeof(string);
                                orgFilename.ColumnName = "OriginalFilename";

                                DataColumn genFilename = new DataColumn();
                                genFilename.DataType = typeof(string);
                                genFilename.ColumnName = "GeneratedFilename";

                                //DataColumn DefaultCurrencyname = new DataColumn();
                                //DefaultCurrencyname.DataType = typeof(string);
                                //DefaultCurrencyname.ColumnName = "DefaultCurrency";


                                // Add the columns to the tempStock DataTable
                                dtData.Columns.Add(ClientId);
                                dtData.Columns.Add(SelectedClientId);
                                dtData.Columns.Add(CreatedBy);
                                dtData.Columns.Add(orgFilename);
                                dtData.Columns.Add(genFilename);
                                // dtData.Columns.Add(DefaultCurrencyname);
                                foreach (DataRow dr in dtData.Rows)
                                {
                                    //  DataRow appendStockfiled = dtData.NewRow();
                                    //dr["ClientId"] = clientId;
                                    dr["ClientId"] = SelectedclientId;
                                    dr["SelectedClientId"] = SelectedclientId;
                                    dr["CreatedBy"] = userId;
                                    dr["OriginalFilename"] = originalFilename;
                                    dr["GeneratedFilename"] = generatedFilename;
                                    //dr["DefaultCurrency"] = DefaultCurrency;

                                }

                                dtData.AcceptChanges();

                                s.ColumnMappings.Add("ClientId", "ClientId");
                                s.ColumnMappings.Add("SelectedClientId", "SelectedClientId");
                                s.ColumnMappings.Add("CreatedBy", "CreatedBy");
                                s.ColumnMappings.Add("OriginalFilename", "OriginalFilename");
                                s.ColumnMappings.Add("GeneratedFilename", "GeneratedFilename");
                                //s.ColumnMappings.Add("DefaultCurrency", "DefaultCurrency");

                                s.WriteToServer(dtData);
                                tran.Commit();

                            }
                        }

                    }
                }

            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to insert Stock Import data", sqlex);
            }

        }
        public override DataTable GetStockExcelHeaderFrom(System.Int32 clientId, System.Int32 userId, System.Int32 SelectedClientID, int clientType_con)//GetExcelHeaderFrom
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {

                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                // cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_GetExcelStockImportHeaderColumnFrom", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                //cmd.CommandTimeout = 60;
                cmd.CommandTimeout = 600;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = SelectedClientID;
                cmd.Parameters.Add("@UserId", SqlDbType.Int).Value = userId;
                cmd.Parameters.Add("@SelectedClientID", SqlDbType.Int).Value = SelectedClientID;


                cn.Open();
                DataSet ds = new DataSet();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);
                return ds.Tables[0];
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get excel header", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override DataTable GetStockExcelHeader(System.Int32 clientId, System.Int32 userId, System.Int32 SelectedClientID, int clientType_con)//GetExcelHeaderFrom
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {

                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_GetExcelStockImportHeaderColumn", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                //cmd.CommandTimeout = 60;
                cmd.CommandTimeout = 600;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = SelectedClientID;
                cmd.Parameters.Add("@UserId", SqlDbType.Int).Value = userId;
                cmd.Parameters.Add("@SelectedClientID", SqlDbType.Int).Value = SelectedClientID;


                cn.Open();
                DataSet ds = new DataSet();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);
                return ds.Tables[0];
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get excel header", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override int DeleteStockRecord(int SelectedClientID, System.Int32 userId, int ClientId, int clientType_con)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {

                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_deleteTempStockImportRecord", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 600;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = SelectedClientID;
                cmd.Parameters.Add("@UserId", SqlDbType.Int).Value = userId;
                cmd.Parameters.Add("@SelectedClientID", SqlDbType.Int).Value = SelectedClientID;
                cn.Open();
                return ExecuteNonQuery(cmd);

            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to insert mapping detail", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override DataTable GetStockGenrateTempDataFromDB(int displayLength, int displayStart, int sortCol, string sortDir, string search, int userid, int Clientid, int SelectedclientId, string ColumnList, string ddlCurancy, string CompanyNameText, int clientType_con, string Column_Lable, string Column_Name, string ContactText, bool OverRideCurrency, string DefaultCurrencyName, string CurrencyColumnName, int DefaultCurrencyId, string SupplierNameText)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_GetDynamicStockHeaderColumn", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@displayLength", SqlDbType.Int).Value = displayLength;
                cmd.Parameters.Add("@displayStart", SqlDbType.Int).Value = displayStart;
                cmd.Parameters.Add("@sortCol", SqlDbType.Int).Value = sortCol;
                cmd.Parameters.Add("@sortDir", SqlDbType.NVarChar, 10).Value = sortDir;//[004]  
                cmd.Parameters.Add("@search", SqlDbType.NVarChar, 255).Value = search;//[004]  
                cmd.Parameters.Add("@userid", SqlDbType.Int).Value = userid;//[003]
                //cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = Clientid;//[003]
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = SelectedclientId;//[003]
                cmd.Parameters.Add("@SelectedClientId", SqlDbType.Int).Value = SelectedclientId;//[003]
                cmd.Parameters.Add("@list_Label_name", SqlDbType.NVarChar).Value = Column_Lable;
                cmd.Parameters.Add("@list_column_name", SqlDbType.NVarChar).Value = Column_Name;
                cmd.Parameters.Add("@SelectColumns", SqlDbType.NVarChar).Value = ColumnList;
                cmd.Parameters.Add("@SupplierNameText", SqlDbType.NVarChar).Value = SupplierNameText;
                cmd.CommandTimeout = 600;

                cn.Open();
                DataSet ds = new DataSet();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);
                //return ds.Tables[0];
                // changes done to catch exception from SP
                if (ds.Tables.Count == 1)   // if exception is thrown
                    return ds.Tables[0];
                else
                    return ds.Tables[ds.Tables.Count - 1];   // if no exception then multiple tables collection return so picking last table
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to get Stock Data", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override int saveStockImportDataFromDB(int userId, int ClientId, int SelectedclientId, string Column_Lable, string Column_Name, string insertDataList, string fileColName, string ddlCurrency, out System.String errorMessage, string BomName, string LotNameText, string OtherLocationText, int WarehouseId, int CompanyId, int ContactId, bool PartWatch, int DefaultCurrencyId, bool OverRideCurrency, string SaveImportOrHubRFQ, System.Int32? LotId, System.Int32? DivisionId, System.String MSLLevel, System.Int32? SupplierId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            errorMessage = string.Empty;
            try
            {

                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_Select_ImportStockUtilityData", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 900;
                //cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = ClientId;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = SelectedclientId;
                cmd.Parameters.Add("@UserId", SqlDbType.Int).Value = userId;
                cmd.Parameters.Add("@SelectedClientID", SqlDbType.Int).Value = SelectedclientId;
                cmd.Parameters.Add("@list_Label_name", SqlDbType.NVarChar).Value = Column_Lable;
                cmd.Parameters.Add("@list_column_name", SqlDbType.NVarChar).Value = Column_Name;
                cmd.Parameters.Add("@insertDataList", SqlDbType.NVarChar).Value = insertDataList;
                cmd.Parameters.Add("@LotId", SqlDbType.Int).Value = LotId;
                cmd.Parameters.Add("@DivisionId", SqlDbType.Int).Value = DivisionId;
                cmd.Parameters.Add("@MSLLevel", SqlDbType.NVarChar).Value = MSLLevel;
                cmd.Parameters.Add("@WarehouseId", SqlDbType.Int).Value = WarehouseId;
                cmd.Parameters.Add("@OtherLocation", SqlDbType.NVarChar).Value = OtherLocationText;
                cmd.Parameters.Add("@SupplierId", SqlDbType.Int).Value = SupplierId;
                cmd.Parameters.Add("@RecordCount", SqlDbType.Int).Direction = ParameterDirection.Output;
                cmd.Parameters.Add("@ErrorMessage", SqlDbType.VarChar, 400).Direction = ParameterDirection.Output;

                cn.Open();
                ExecuteNonQuery(cmd);
                errorMessage = Convert.ToString(cmd.Parameters["@ErrorMessage"].Value);
                return (Int32)cmd.Parameters["@RecordCount"].Value;


            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to insert mapping Column detail", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override DataTable GetStocksDetailFromTemp(int displayLength, int displayStart, int sortCol, string sortDir, string search, int userid, int Clientid, int SelectedclientId, int clientType_con)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {

                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_GetStockImportCount", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@displayLength", SqlDbType.Int).Value = displayLength;
                cmd.Parameters.Add("@displayStart", SqlDbType.Int).Value = displayStart;
                cmd.Parameters.Add("@sortCol", SqlDbType.Int).Value = sortCol;
                cmd.Parameters.Add("@sortDir", SqlDbType.NVarChar, 10).Value = sortDir;//[004]  
                cmd.Parameters.Add("@search", SqlDbType.NVarChar, 255).Value = search;//[004]  
                cmd.Parameters.Add("@userid", SqlDbType.Int).Value = userid;//[003]
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = SelectedclientId;//[003]
                cmd.Parameters.Add("@SelectedClientId", SqlDbType.Int).Value = SelectedclientId;//[003]
                //cmd.CommandTimeout = 60;
                cmd.CommandTimeout = 900;

                cn.Open();
                DataSet ds = new DataSet();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);
                return ds.Tables[0];
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get CustomerRequirements", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override DataTable GetSupplierSearch(System.Int32? mfrid, System.Int32? proCatgdid, System.Int32? suppid, System.String industryType
            , System.String supplierType, System.Int32? lastOrderDate
            , System.Int32? prodId
            , System.Int32? GlobalProductNameId
            , System.Int32? MfrGroupId
            , System.Int32? clientId
            )
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {

                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("USP_GetSourcingData", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@MfrId", SqlDbType.Int).Value = mfrid;//[003]
                cmd.Parameters.Add("@ProductTypeId", SqlDbType.Int).Value = proCatgdid;//[003]
                cmd.Parameters.Add("@Franchised", SqlDbType.Int).Value = suppid;//[003]
                cmd.Parameters.Add("@IndustryType", SqlDbType.VarChar).Value = industryType;//[003]
                cmd.Parameters.Add("@SupplierType", SqlDbType.VarChar).Value = supplierType;//[003]
                cmd.Parameters.Add("@Orderdate", SqlDbType.Int).Value = lastOrderDate;//[003]
                cmd.Parameters.Add("@ProductId", SqlDbType.Int).Value = prodId;//[003]
                cmd.Parameters.Add("@GlobalProductId", SqlDbType.Int).Value = GlobalProductNameId;
                cmd.Parameters.Add("@MfrGroupId", SqlDbType.Int).Value = MfrGroupId;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;//[003]


                //cmd.Parameters.Add("@displayLength", SqlDbType.Int).Value = displayLength;
                //cmd.Parameters.Add("@displayStart", SqlDbType.Int).Value = displayStart;
                //cmd.Parameters.Add("@sortCol", SqlDbType.Int).Value = sortCol;
                //cmd.Parameters.Add("@sortDir", SqlDbType.NVarChar, 10).Value = sortDir;//[004]  
                //cmd.Parameters.Add("@search", SqlDbType.NVarChar, 255).Value = search;//[004]  
                //cmd.Parameters.Add("@userid", SqlDbType.Int).Value = userid;//[003]
                //cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = SelectedclientId;//[003]
                //cmd.Parameters.Add("@SelectedClientId", SqlDbType.Int).Value = SelectedclientId;//[003]
                //cmd.CommandTimeout = 60;
                cmd.CommandTimeout = 900;

                cn.Open();
                DataSet ds = new DataSet();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);
                return ds.Tables[0];
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get CustomerRequirements", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        //1 End
        public override DataTable GetSupplierType(System.Int32? ClientNo, System.Int32? MfrId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {

                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("USP_GETSUPPLIERTYPE", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@CLIENTNO", SqlDbType.Int).Value = ClientNo;
                cmd.Parameters.Add("@ManufacturerId", SqlDbType.Int).Value = MfrId;
                cmd.CommandTimeout = 900;

                cn.Open();
                DataSet ds = new DataSet();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);
                return ds.Tables[0];
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get CustomerRequirements", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override DataTable ProductCategory()
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {

                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_dropdown_ProductCategory", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                //cmd.Parameters.Add("@CLIENTNO", SqlDbType.Int).Value = ClientNo;
                cmd.CommandTimeout = 900;

                cn.Open();
                DataSet ds = new DataSet();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);
                return ds.Tables[0];
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get ProductCategory", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override DataTable GetProductByCategory(System.Int32? ClientNo, System.Int32? ProdId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {

                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_dropdown_Product_for_Sourcing", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = ClientNo;
                cmd.Parameters.Add("@ProductCatorygoryId", SqlDbType.Int).Value = ProdId;
                cmd.CommandTimeout = 900;

                cn.Open();
                DataSet ds = new DataSet();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);
                return ds.Tables[0];
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get ProductCategory", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override DataTable GetGlobalProductNameByCategory(System.Int32? ProductCategoryId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {

                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_dropdown_global_product_name", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@product_category_id", SqlDbType.Int).Value = ProductCategoryId;
                cmd.CommandTimeout = 900;

                cn.Open();
                DataSet ds = new DataSet();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);
                return ds.Tables[0];
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get ProductCategory", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        //code for save bom import mapping
        public override int SaveCompanyColumnMapping(int CompanyId, string insertMapColumnList, int clientType_con)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {

                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_insert_BomImportColumnMapping", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 600;
                cmd.Parameters.Add("@strInsertMappings", SqlDbType.NVarChar).Value = insertMapColumnList;//[003]ColumnList
                cn.Open();
                return ExecuteNonQuery(cmd);

            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to insert Bom import mapping Column detail", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        //code for save BOMManager import mapping
        public override int SaveBOMManagerExcelColumnHeaderDetails(string insertMapColumnList, string MappingHeaderColumns, int? clientId, int CompanyId, int? LoggedId, Boolean mappingType)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {

                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_SaveBOMManagerExcelColumnHeaderDetails", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 600;
                cmd.Parameters.Add("@InserColumnList", SqlDbType.NVarChar).Value = insertMapColumnList;//[003]ColumnList
                cmd.Parameters.Add("@MappingHeaderColumns", SqlDbType.NVarChar).Value = MappingHeaderColumns;//[003]ColumnList
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;//[003]ColumnList
                cmd.Parameters.Add("@CompanyId", SqlDbType.Int).Value = CompanyId;//[003]ColumnList
                cmd.Parameters.Add("@UserId", SqlDbType.Int).Value = LoggedId;//[003]ColumnList
                cmd.Parameters.Add("@ManualMapping", SqlDbType.Int).Value = mappingType;
                cn.Open();
                return (Int32)ExecuteScalar(cmd);

                //return ExecuteNonQuery(cmd);

            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to insert BomManager import mapping Column Header detail", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override int SaveBOMManagerAPI(string insertMapColumnList, string MappingHeaderColumns, int? clientId, int CompanyId, int? LoggedId, Boolean mappingType)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {

                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_SaveBOMManagerExcelColumnHeaderDetails", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 600;
                cmd.Parameters.Add("@InserColumnList", SqlDbType.NVarChar).Value = insertMapColumnList;//[003]ColumnList
                cmd.Parameters.Add("@MappingHeaderColumns", SqlDbType.NVarChar).Value = MappingHeaderColumns;//[003]ColumnList
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;//[003]ColumnList
                cmd.Parameters.Add("@CompanyId", SqlDbType.Int).Value = CompanyId;//[003]ColumnList
                cmd.Parameters.Add("@UserId", SqlDbType.Int).Value = LoggedId;//[003]ColumnList
                cmd.Parameters.Add("@ManualMapping", SqlDbType.Int).Value = mappingType;
                cmd.Parameters.Add("@API", SqlDbType.Int).Value = 1;
                cn.Open();
                return (Int32)ExecuteScalar(cmd);

                //return ExecuteNonQuery(cmd);

            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to insert BomManager import mapping Column Header detail", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override int SaveCompanyColumnMappingBOMManager(int CompanyId, string insertMapColumnList, int clientType_con)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {

                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_insert_BomManagerImportColumnMapping", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 600;
                cmd.Parameters.Add("@strInsertMappings", SqlDbType.NVarChar).Value = insertMapColumnList;//[003]ColumnList
                cn.Open();
                return ExecuteNonQuery(cmd);

            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to insert Bom import mapping Column detail", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override int DeleteCompanyTempMapping(int CompanyId, int clientType_con)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {

                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_delete_BomImportMapping", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 600;
                cmd.Parameters.Add("@CompanyNo", SqlDbType.Int).Value = CompanyId;
                cn.Open();
                return ExecuteNonQuery(cmd);

            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to Delete mapping detail", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override int DeleteCompanyTempMappingBOMManager(int CompanyId, int clientType_con)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {

                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_delete_BomManagerImportMapping", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 600;
                cmd.Parameters.Add("@CompanyNo", SqlDbType.Int).Value = CompanyId;
                cn.Open();
                return ExecuteNonQuery(cmd);

            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to Delete mapping detail", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override DataTable GetCompanyMappedColumn(int CompanyId, int clientType_con)
        {

            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {

                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_ImportCompany_ColumnMapping", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                //cmd.CommandTimeout = 60;
                cmd.CommandTimeout = 600;
                cmd.Parameters.Add("@CompanyNo", SqlDbType.Int).Value = CompanyId;
                cn.Open();
                DataSet ds = new DataSet();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);
                return ds.Tables[0];
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to get Supplier Column Mapping", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override DataTable GetCompanyMappedColumnBOMManager(int CompanyId, int clientType_con)
        {

            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {

                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_ImportCompany_ColumnMappingBOMManager", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                //cmd.CommandTimeout = 60;
                cmd.CommandTimeout = 600;
                cmd.Parameters.Add("@CompanyNo", SqlDbType.Int).Value = CompanyId;
                cn.Open();
                DataSet ds = new DataSet();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);
                return ds.Tables[0];
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to get BOM Manager Column Mapping", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override DataTable GetClientDivisionName(System.Int32? ClientID, System.Int32? clientType_con)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {

                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_dropdown_Division_for_Client", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;
                cmd.Parameters.Add("@ClientID", SqlDbType.Int).Value = ClientID;
                cn.Open();
                SqlDataAdapter objDaFecthClient = new SqlDataAdapter(cmd);
                DataTable dtClient = new DataTable();
                objDaFecthClient.Fill(dtClient);
                return dtClient;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get client Division", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override DataTable GetClientWarehouse(System.Int32? ClientID, System.Int32? clientType_con)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {

                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_dropdown_Warehouse_for_Client", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = ClientID;
                cn.Open();
                SqlDataAdapter objDaFecthClient = new SqlDataAdapter(cmd);
                DataTable dtClient = new DataTable();
                objDaFecthClient.Fill(dtClient);
                return dtClient;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get client Warehouse", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override DataTable GetClientLot(System.Int32? ClientID, System.Int32? clientType_con)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {

                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_dropdown_Lot_for_Client", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = ClientID;
                cn.Open();
                SqlDataAdapter objDaFecthClient = new SqlDataAdapter(cmd);
                DataTable dtClient = new DataTable();
                objDaFecthClient.Fill(dtClient);
                return dtClient;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get client Lot", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override DataTable GetCompanyType(System.Int32? SelectedCompanyId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {

                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_select_Company_for_CompanyType", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;
                cmd.Parameters.Add("@SelectedCompanyId", SqlDbType.Int).Value = SelectedCompanyId;
                cn.Open();
                SqlDataAdapter objDaFecthClient = new SqlDataAdapter(cmd);
                DataTable dtClient = new DataTable();
                objDaFecthClient.Fill(dtClient);
                return dtClient;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get client Lot", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        public override int Delete_EPORecord(string OfferId, System.Int32 BomId, System.Int32 userId, int ClientId, string OfferType)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {

                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_deleteSourcingAvailability", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 600;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = ClientId;
                cmd.Parameters.Add("@UserId", SqlDbType.Int).Value = userId;
                cmd.Parameters.Add("@EPOTempIds", SqlDbType.NVarChar).Value = OfferId;
                cmd.Parameters.Add("@SekectedclientId", SqlDbType.Int).Value = BomId;
                cn.Open();
                return ExecuteNonQuery(cmd);

            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to insert mapping detail", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        //code end
        public override DataTable GetLogImportactivity(int displayLength, int displayStart, int sortCol, string sortDir, string search, int Clientid, int clientType_con, int SelectedClientId, int userLoginid)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {

                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_select_importUtilityLog", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@displayLength", SqlDbType.Int).Value = displayLength;
                cmd.Parameters.Add("@displayStart", SqlDbType.Int).Value = displayStart;
                cmd.Parameters.Add("@sortCol", SqlDbType.Int).Value = sortCol;
                cmd.Parameters.Add("@sortDir", SqlDbType.NVarChar, 10).Value = sortDir;
                cmd.Parameters.Add("@search", SqlDbType.NVarChar, 255).Value = search;
                cmd.Parameters.Add("@UtilityType", SqlDbType.Int).Value = clientType_con;
                cmd.Parameters.Add("@selectedclientid", SqlDbType.Int).Value = SelectedClientId;
                cmd.Parameters.Add("@CreateBy", SqlDbType.Int).Value = userLoginid;
                cmd.CommandTimeout = 600;

                cn.Open();
                DataSet ds = new DataSet();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);
                return ds.Tables[0];
            }
            catch (SqlException sqlex)
            {

                throw new Exception("Failed to get Import Activity detail", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        //Reverse Logistic code start
        public override void saveExcelReverseLogisticBulkSave(DataTable tempEpo, DataTable dtData, string originalFilename, string generatedFilename, System.Int32 userId, System.Int32 clientId, int? SelectedclientId, int clientType_con)
        {

            try
            {

                dtData.Columns["Lead Time"].ColumnName = "LeadTime";
                dtData.Columns["Virtual Cost Price"].ColumnName = "VirtualCostPrice";
                dtData.Columns["Date Code"].ColumnName = "DateCode";
                dtData.Columns["Vendor Type"].ColumnName = "VendorType";
                dtData.Columns["Factory Sealed"].ColumnName = "FactorySealed";

                using (SqlConnection dbConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString()))
                {
                    dbConnection.Open();
                    if (dbConnection.State == ConnectionState.Open)
                    {


                        using (var tran = dbConnection.BeginTransaction(IsolationLevel.ReadCommitted))
                        {
                            using (SqlBulkCopy s = new SqlBulkCopy(dbConnection, SqlBulkCopyOptions.Default, tran))
                            {
                                //Part,ManufacturerName,Quantity,Price,VirtualCostPrice,LeadTime,DateCode,SPQ,SupplierMOQ,SupplierName
                                //,SupplierType,FactorySealed,SupplierLTB,OriginalEntryDate,Description
                                s.DestinationTableName = tempEpo.TableName;
                                s.ColumnMappings.Add("MPN", "Part");
                                s.ColumnMappings.Add("MFR", "ManufacturerName");
                                s.ColumnMappings.Add("Quantity", "Quantity");
                                s.ColumnMappings.Add("COST", "Price");
                                s.ColumnMappings.Add("VirtualCostPrice", "VirtualCostPrice");
                                s.ColumnMappings.Add("LeadTime", "LeadTime");
                                s.ColumnMappings.Add("DateCode", "DateCode");
                                s.ColumnMappings.Add("SPQ", "SPQ");
                                s.ColumnMappings.Add("MOQ", "SupplierMOQ");
                                s.ColumnMappings.Add("Vendor", "SupplierName");
                                s.ColumnMappings.Add("VendorType", "SupplierType");
                                s.ColumnMappings.Add("FactorySealed", "FactorySealed");
                                s.ColumnMappings.Add("LTB", "SupplierLTB");
                                s.ColumnMappings.Add("Date", "OriginalEntryDate");
                                s.ColumnMappings.Add("Description", "Description");



                                DataColumn ClientId = new DataColumn();
                                ClientId.DataType = typeof(int);
                                ClientId.ColumnName = "ClientId";


                                DataColumn SelectedClientId = new DataColumn();
                                SelectedClientId.DataType = typeof(int);
                                SelectedClientId.ColumnName = "SelectedClientId";

                                DataColumn CreatedBy = new DataColumn();
                                CreatedBy.DataType = typeof(int);
                                CreatedBy.ColumnName = "CreatedBy";

                                DataColumn orgFilename = new DataColumn();
                                orgFilename.DataType = typeof(string);
                                orgFilename.ColumnName = "OriginalFilename";

                                DataColumn genFilename = new DataColumn();
                                genFilename.DataType = typeof(string);
                                genFilename.ColumnName = "GeneratedFilename";


                                // Add the columns to the tempStock DataTable
                                dtData.Columns.Add(ClientId);
                                dtData.Columns.Add(SelectedClientId);
                                dtData.Columns.Add(CreatedBy);
                                dtData.Columns.Add(orgFilename);
                                dtData.Columns.Add(genFilename);
                                foreach (DataRow dr in dtData.Rows)
                                {
                                    //  DataRow appendStockfiled = dtData.NewRow();
                                    dr["ClientId"] = clientId;
                                    dr["SelectedClientId"] = SelectedclientId;
                                    dr["CreatedBy"] = userId;
                                    dr["OriginalFilename"] = originalFilename;
                                    dr["GeneratedFilename"] = generatedFilename;

                                }

                                dtData.AcceptChanges();

                                s.ColumnMappings.Add("ClientId", "ClientId");
                                s.ColumnMappings.Add("SelectedClientId", "SelectedClientId");
                                s.ColumnMappings.Add("CreatedBy", "CreatedBy");
                                s.ColumnMappings.Add("OriginalFilename", "OriginalFilename");
                                s.ColumnMappings.Add("GeneratedFilename", "GeneratedFilename");

                                s.WriteToServer(dtData);
                                tran.Commit();

                            }
                        }

                    }
                }

            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to insert Reverse Logistic data", sqlex);
            }

        }
        public override void SaveExcelReverseLogisticHeader(string columnList, string insertColumnList, System.Int32? clientId, int? SelectedclientId, int? loginId, int clientType_con)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {

                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_SaveReverseLogistic_ColumnHeading", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 600;
                cmd.Parameters.Add("@SelectColumns", SqlDbType.NVarChar, 3000).Value = columnList;
                cmd.Parameters.Add("@InserColumnList", SqlDbType.NVarChar, 3000).Value = insertColumnList;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@SelectedClientId", SqlDbType.Int).Value = SelectedclientId;
                cmd.Parameters.Add("@UserId", SqlDbType.Int).Value = loginId;

                cn.Open();
                cmd.ExecuteNonQuery();

            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to insert Reverse Logistic header info", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override DataTable GetReverseLogisticExcelHeader(System.Int32 clientId, System.Int32 userId, System.Int32 SelectedClientID, int clientType_con)//GetExcelHeaderFrom
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {

                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_Get_ReverseLogistic_HeaderColumn", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                //cmd.CommandTimeout = 60;
                cmd.CommandTimeout = 600;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@UserId", SqlDbType.Int).Value = userId;
                cmd.Parameters.Add("@SelectedClientID", SqlDbType.Int).Value = SelectedClientID;


                cn.Open();
                DataSet ds = new DataSet();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);
                return ds.Tables[0];
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Reverse Logistic excel header", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override DataTable GetReverseLogisticDetailFromTemp(int displayLength, int displayStart, int sortCol, string sortDir, string search, int userid, int Clientid, int SelectedclientId, int clientType_con)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {

                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_Get_ReverseLogistic_TempData", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@displayLength", SqlDbType.Int).Value = displayLength;
                cmd.Parameters.Add("@displayStart", SqlDbType.Int).Value = displayStart;
                cmd.Parameters.Add("@sortCol", SqlDbType.Int).Value = sortCol;
                cmd.Parameters.Add("@sortDir", SqlDbType.NVarChar, 10).Value = sortDir;//[004]  
                cmd.Parameters.Add("@search", SqlDbType.NVarChar, 255).Value = search;//[004]  
                cmd.Parameters.Add("@userid", SqlDbType.Int).Value = userid;//[003]
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = Clientid;//[003]
                cmd.Parameters.Add("@SelectedClientId", SqlDbType.Int).Value = SelectedclientId;//[003]
                //cmd.CommandTimeout = 60;
                cmd.CommandTimeout = 900;

                cn.Open();
                DataSet ds = new DataSet();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);
                return ds.Tables[0];
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Reverse Logistic data", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override int DeleteReverseLogisticRecord(int SelectedClientID, System.Int32 userId, int ClientId, int clientType_con)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {

                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_delete_ReverseLogistic_TempRecord", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 600;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = ClientId;
                cmd.Parameters.Add("@UserId", SqlDbType.Int).Value = userId;
                cmd.Parameters.Add("@SelectedClientID", SqlDbType.Int).Value = SelectedClientID;
                cn.Open();
                return ExecuteNonQuery(cmd);

            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to Reverse Logistic record", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override DataTable GetReverseLogisticGenrateTempData(int displayLength, int displayStart, int sortCol, string sortDir, string search, int userid, int Clientid, int SelectedclientId, string SupplierNameText, int clientType_con, System.Double? UpliftpValue, int NoChangesToSourcingSheet)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_Get_ReverseLogistic_GenrateData", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@displayLength", SqlDbType.Int).Value = displayLength;
                cmd.Parameters.Add("@displayStart", SqlDbType.Int).Value = displayStart;
                cmd.Parameters.Add("@sortCol", SqlDbType.Int).Value = sortCol;
                cmd.Parameters.Add("@sortDir", SqlDbType.NVarChar, 10).Value = sortDir;//[004]  
                cmd.Parameters.Add("@search", SqlDbType.NVarChar, 255).Value = search;//[004]  
                cmd.Parameters.Add("@userid", SqlDbType.Int).Value = userid;//[003]
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = Clientid;//[003]
                cmd.Parameters.Add("@SelectedClientId", SqlDbType.Int).Value = SelectedclientId;//[003]
                                                                                                // cmd.Parameters.Add("@SupplierNameText", SqlDbType.NVarChar).Value = SupplierNameText;
                cmd.Parameters.Add("@UpliftPercentage", SqlDbType.Float).Value = UpliftpValue;
                cmd.Parameters.Add("@NoChangesToSourcingSheet", SqlDbType.Int).Value = NoChangesToSourcingSheet;



                //cmd.CommandTimeout = 60;
                cmd.CommandTimeout = 600;

                cn.Open();
                DataSet ds = new DataSet();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);
                return ds.Tables[0];
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Reverse Logistic details", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override int saveReverseLogisticImportData(int userId, int ClientId, int SelectedclientId, int SupplierId, int recordType, int clientType_con, System.Double? UpliftPercentage, int NoChangesToSourcingSheet, out System.String errorMessage)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            errorMessage = string.Empty;
            try
            {
                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_Select_Insert_ReverseLogisticData", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 600;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = ClientId;
                cmd.Parameters.Add("@UserId", SqlDbType.Int).Value = userId;
                cmd.Parameters.Add("@SelectedClientID", SqlDbType.Int).Value = SelectedclientId;
                //cmd.Parameters.Add("@SupplierId", SqlDbType.Int).Value = SupplierId;
                cmd.Parameters.Add("@recordType", SqlDbType.Int).Value = recordType;
                cmd.Parameters.Add("@UpliftPercentage", SqlDbType.Float).Value = UpliftPercentage;
                cmd.Parameters.Add("@NoChangesToSourcingSheet", SqlDbType.Int).Value = NoChangesToSourcingSheet;
                cmd.Parameters.Add("@RecordCount", SqlDbType.Int).Direction = ParameterDirection.Output;
                cmd.Parameters.Add("@ErrorMessage", SqlDbType.VarChar, 400).Direction = ParameterDirection.Output;

                cn.Open();
                //return ExecuteNonQuery(cmd);
                ExecuteNonQuery(cmd);
                errorMessage = Convert.ToString(cmd.Parameters["@ErrorMessage"].Value);
                return (Int32)cmd.Parameters["@RecordCount"].Value;


            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to insert Reverse Logistic Data", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override int SaveReverseLogisticImportActivity(string strInsertSummary, int clientType_con)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_insert_EpoImportActivity", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 600;
                cmd.Parameters.Add("@strInsertText", SqlDbType.NVarChar).Value = strInsertSummary;
                cn.Open();
                return ExecuteNonQuery(cmd);

            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to insert Import Activity detail", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override DataTable GetReverseLogisticImportactivity(int displayLength, int displayStart, int sortCol, string sortDir, string search, int Clientid, int clientType_con, int SelectedClientId, int userLoginid, Boolean InactiveId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {

                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_select_ReverseLogistic_ImportHistory", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@displayLength", SqlDbType.Int).Value = displayLength;
                cmd.Parameters.Add("@displayStart", SqlDbType.Int).Value = displayStart;
                cmd.Parameters.Add("@sortCol", SqlDbType.Int).Value = sortCol;
                cmd.Parameters.Add("@sortDir", SqlDbType.NVarChar, 10).Value = sortDir;
                cmd.Parameters.Add("@search", SqlDbType.NVarChar, 255).Value = search;
                cmd.Parameters.Add("@ClientType", SqlDbType.Int).Value = clientType_con;
                cmd.Parameters.Add("@selectedclientid", SqlDbType.Int).Value = SelectedClientId;
                cmd.Parameters.Add("@CreateBy", SqlDbType.Int).Value = userLoginid;
                cmd.Parameters.Add("@InactiveId", SqlDbType.Bit).Value = InactiveId;

                cmd.CommandTimeout = 600;

                cn.Open();
                DataSet ds = new DataSet();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);
                return ds.Tables[0];
            }
            catch (SqlException sqlex)
            {

                throw new Exception("Failed to get Reverse Logistic History detail", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override int Delete_ReverseLogisticRecord(string OfferId, System.Int32 BomId, System.Int32 userId, int ClientId, string OfferType)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {

                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_delete_ReverseLogistic_SelectedRecord", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 600;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = ClientId;
                cmd.Parameters.Add("@UserId", SqlDbType.Int).Value = userId;
                cmd.Parameters.Add("@ReverseLogisticIds", SqlDbType.NVarChar).Value = OfferId;
                cmd.Parameters.Add("@SekectedclientId", SqlDbType.Int).Value = BomId;
                cn.Open();
                return ExecuteNonQuery(cmd);

            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to delete Reverse Logistic Selected checkbox Record", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override DataTable GetExportToExcelError(int userid, int ClientId, int SelectedclientId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_ExcelUpload_Error_ReverseLogistics", cn);
                cmd.CommandType = CommandType.StoredProcedure;

                cmd.Parameters.Add("@userid", SqlDbType.Int).Value = userid;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = ClientId;
                cmd.Parameters.Add("@SelectedClientId", SqlDbType.Int).Value = SelectedclientId;
                cmd.CommandTimeout = 600;

                cn.Open();
                DataSet ds = new DataSet();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);
                return ds.Tables[0];
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Reverse Logistic details", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        //Reverse Logistic code end
        public override DataTable InactivateReverseLogistics(int userid, int ClientId, int SelectedImportId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_InactivateReverseLogistics", cn);
                cmd.CommandType = CommandType.StoredProcedure;

                cmd.Parameters.Add("@userid", SqlDbType.Int).Value = userid;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = ClientId;
                cmd.Parameters.Add("@SelectedImportId", SqlDbType.Int).Value = SelectedImportId;
                cmd.CommandTimeout = 600;

                cn.Open();
                DataSet ds = new DataSet();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);
                return ds.Tables[0];
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to Inactivate Reverse Logistic details", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override DataTable GetExportToExcelError_BomManager(int userid, int ClientId, int SelectedclientId, string mappingcolumlist, string fileHeaderCheck)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_ExcelUpload_Error_BomManager", cn);
                cmd.CommandType = CommandType.StoredProcedure;

                cmd.Parameters.Add("@userid", SqlDbType.Int).Value = userid;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = ClientId;
                cmd.Parameters.Add("@SelectedClientId", SqlDbType.Int).Value = SelectedclientId;
                cmd.Parameters.Add("@mappingcolumlist", SqlDbType.VarChar).Value = mappingcolumlist;
                cmd.Parameters.Add("@fileHeaderCheck", SqlDbType.VarChar).Value = fileHeaderCheck;
                cmd.CommandTimeout = 600;

                cn.Open();
                DataSet ds = new DataSet();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);
                return ds.Tables[0];
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Bom Manager details", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        // Price Quote Import Tool Code Start
        public override DataTable GetExportToExcelError_PriceQuote(int userid, int clientId, string targetColumns, string selectedColumns)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_ExcelUpload_Error_PriceQuote", cn)
                {
                    CommandType = CommandType.StoredProcedure,
                    CommandTimeout = 600
                };

                cmd.Parameters.Add("@UserId", SqlDbType.Int).Value = userid;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@TargetColumns", SqlDbType.NVarChar).Value = targetColumns;
                cmd.Parameters.Add("@SelectedColumns", SqlDbType.NVarChar).Value = selectedColumns;

                cn.Open();
                DataSet ds = new DataSet();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);
                return ds.Tables[0];
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Reverse Logistic details", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override DataTable GetPriceQuoteDetailFromTemp(int displayLength, int displayStart, int sortCol, string sortDir, string search, int userid, int Clientid, int SelectedclientId, int clientType_con)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {

                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_Get_PriceQuoteImport_TempData", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@displayLength", SqlDbType.Int).Value = displayLength;
                cmd.Parameters.Add("@displayStart", SqlDbType.Int).Value = displayStart;
                cmd.Parameters.Add("@sortCol", SqlDbType.Int).Value = sortCol;
                cmd.Parameters.Add("@sortDir", SqlDbType.NVarChar, 10).Value = sortDir;//[004]  
                cmd.Parameters.Add("@search", SqlDbType.NVarChar, 255).Value = search;//[004]  
                cmd.Parameters.Add("@userid", SqlDbType.Int).Value = userid;//[003]
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = Clientid;//[003]
                cmd.Parameters.Add("@SelectedClientId", SqlDbType.Int).Value = SelectedclientId;//[003]
                //cmd.CommandTimeout = 60;
                cmd.CommandTimeout = 900;

                cn.Open();
                DataSet ds = new DataSet();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);
                return ds.Tables[0];
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Reverse Logistic data", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override DataTable GetExcelHeader_PriceQuote(System.Int32 clientId, System.Int32 userId, System.Int32 SelectedClientID, int clientType_con)//GetExcelHeaderFrom
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {

                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_Get_PriceQuote_HeaderColumn", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                //cmd.CommandTimeout = 60;
                cmd.CommandTimeout = 600;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@UserId", SqlDbType.Int).Value = userId;
                cmd.Parameters.Add("@SelectedClientID", SqlDbType.Int).Value = SelectedClientID;


                cn.Open();
                DataSet ds = new DataSet();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);
                return ds.Tables[0];
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get excel header", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override void SaveExcelHeader_PriceQuote(string columnList, string insertColumnList, System.Int32? clientId, int? SelectedclientId, int? loginId, int clientType_con)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {

                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_SavePriceQuoteImport_ColumnHeading", cn)
                {
                    CommandType = CommandType.StoredProcedure,
                    CommandTimeout = 600
                };
                cmd.Parameters.Add("@SelectColumns", SqlDbType.NVarChar, 3000).Value = columnList;
                cmd.Parameters.Add("@InserColumnList", SqlDbType.NVarChar, 3000).Value = insertColumnList;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@SelectedClientId", SqlDbType.Int).Value = SelectedclientId;
                cmd.Parameters.Add("@UserId", SqlDbType.Int).Value = loginId;

                cn.Open();
                cmd.ExecuteNonQuery();

            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to insert header info", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override DataTable GetExcelHeaderFrom_PriceQuote(System.Int32 clientId, System.Int32 userId, System.Int32 SelectedClientID, int clientType_con)//GetExcelHeaderFrom
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {

                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                // cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_GetExcelStockHeaderColumnFrom_PriceQuote", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                //cmd.CommandTimeout = 60;
                cmd.CommandTimeout = 600;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@UserId", SqlDbType.Int).Value = userId;
                cmd.Parameters.Add("@SelectedClientID", SqlDbType.Int).Value = SelectedClientID;


                cn.Open();
                DataSet ds = new DataSet();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);
                return ds.Tables[0];
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get excel header", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override void saveExcelBulkSave_PriceQuote(DataTable tempStock, DataTable dtData, string originalFilename, string generatedFilename, System.Int32 userId, System.Int32 clientId, int? SelectedclientId, int clientType_con)
        {

            SqlConnection cn = null;
            SqlCommand cmdcur = null;
            try
            {
                int columnCount = dtData.Columns.Count;
                // rename actual column according to excel file uploaded
                for (int i = 0; i < columnCount; i++)
                {
                    dtData.Columns[i].ColumnName = "Column" + (i + 1).ToString();
                }

                //add column LineNumber to DataTable
                dtData.Columns.Add("LineNumber", typeof(int));

                for (int i = 0; i < dtData.Rows.Count; i++)
                {
                    dtData.Rows[i]["LineNumber"] = i + 1;
                }

                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmdcur = new SqlCommand("usp_saveExcelBulkSave_PriceQuote", cn)
                {
                    CommandType = CommandType.StoredProcedure,
                    CommandTimeout = 0
                };
                cmdcur.Parameters.AddWithValue("@UploadedData", dtData);
                cmdcur.Parameters.AddWithValue("@originalFilename", originalFilename);
                cmdcur.Parameters.AddWithValue("@generatedFilename", generatedFilename);
                cmdcur.Parameters.AddWithValue("@userId", userId);
                cmdcur.Parameters.AddWithValue("@clientId", clientId);
                cmdcur.Parameters.AddWithValue("@SelectedclientId", SelectedclientId);
                cn.Open();
                cmdcur.ExecuteNonQuery();
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to insert stock data", sqlex);
            }
            finally
            {
                cmdcur.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override DataTable GetPriceQuoteGenrateTempData(int displayLength, int displayStart, int sortCol, string sortDir, string search, int userid, int Clientid, int SelectedclientId, string ColumnList, string ddlCurancy, string SupplierNameText, int clientType_con, string Column_Lable, string Column_Name)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {

                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_GetDynamicHeaderColumn_PriceQuote", cn); //  usp_Get_PriceQuoteImport_GenrateData
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@displayLength", SqlDbType.Int).Value = displayLength;
                cmd.Parameters.Add("@displayStart", SqlDbType.Int).Value = displayStart;
                cmd.Parameters.Add("@sortCol", SqlDbType.Int).Value = sortCol;
                cmd.Parameters.Add("@sortDir", SqlDbType.NVarChar, 10).Value = sortDir;//[004]  
                cmd.Parameters.Add("@search", SqlDbType.NVarChar, 255).Value = search;//[004]  
                cmd.Parameters.Add("@userid", SqlDbType.Int).Value = userid;//[003]
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = Clientid;//[003]
                cmd.Parameters.Add("@SelectedClientId", SqlDbType.Int).Value = SelectedclientId;//[003]
                cmd.Parameters.Add("@list_Label_name", SqlDbType.NVarChar).Value = Column_Lable;
                cmd.Parameters.Add("@list_column_name", SqlDbType.NVarChar).Value = Column_Name;
                cmd.Parameters.Add("@SelectColumns", SqlDbType.NVarChar).Value = ColumnList;
                cmd.Parameters.Add("@CurancyColumns", SqlDbType.NVarChar).Value = ddlCurancy;
                cmd.Parameters.Add("@SupplierNameText", SqlDbType.NVarChar).Value = SupplierNameText;

                //cmd.CommandTimeout = 60;
                cmd.CommandTimeout = 600;

                cn.Open();
                DataSet ds = new DataSet();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);
                // return ds.Tables[0];
                // changes done to catch exception from SP
                if (ds.Tables.Count == 1)   // if exception is thrown
                    return ds.Tables[0];
                else
                    return ds.Tables[ds.Tables.Count - 1];   // if no exception then multiple tables collection return so picking last table
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get PriceQuote", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override DataTable GetStockDetailFromTemp_PriceQuote(int displayLength, int displayStart, int sortCol, string sortDir, string search, int userid, int Clientid, int SelectedclientId, int clientType_con)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {

                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_GetStockNewData_PriceQuote", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@displayLength", SqlDbType.Int).Value = displayLength;
                cmd.Parameters.Add("@displayStart", SqlDbType.Int).Value = displayStart;
                cmd.Parameters.Add("@sortCol", SqlDbType.Int).Value = sortCol;
                cmd.Parameters.Add("@sortDir", SqlDbType.NVarChar, 10).Value = sortDir;//[004]  
                cmd.Parameters.Add("@search", SqlDbType.NVarChar, 255).Value = search;//[004]  
                cmd.Parameters.Add("@userid", SqlDbType.Int).Value = userid;//[003]
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = Clientid;//[003]
                cmd.Parameters.Add("@SelectedClientId", SqlDbType.Int).Value = SelectedclientId;//[003]
                //cmd.CommandTimeout = 60;
                cmd.CommandTimeout = 900;

                cn.Open();
                DataSet ds = new DataSet();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);
                return ds.Tables[0];
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get CustomerRequirements", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override int InsertUpdatePriceQuoteImportData(int userId, int ClientId, int SelectedclientId, string Column_Lable, string Column_Name, string insertDataList, string fileColName, int SupplierId, string ddlCurrency, int recordType, int clientType_con, out System.String errorMessage, out System.Int32 PriceQuoteNo)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            errorMessage = string.Empty;
            PriceQuoteNo = 0;
            try
            {

                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_InsertOrUpdate_PriceQuoteImportdata", cn);
                //cmd = new SqlCommand("procname", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 900;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = ClientId;
                cmd.Parameters.Add("@UserId", SqlDbType.Int).Value = userId;
                cmd.Parameters.Add("@SelectedClientID", SqlDbType.Int).Value = SelectedclientId;
                cmd.Parameters.Add("@list_Label_name", SqlDbType.NVarChar).Value = Column_Lable;
                cmd.Parameters.Add("@list_column_name", SqlDbType.NVarChar).Value = Column_Name;
                cmd.Parameters.Add("@insertDataList", SqlDbType.NVarChar).Value = insertDataList;
                cmd.Parameters.Add("@fileColName", SqlDbType.NVarChar).Value = fileColName;
                cmd.Parameters.Add("@SupplierId", SqlDbType.Int).Value = SupplierId;
                cmd.Parameters.Add("@CurrencyName", SqlDbType.NVarChar).Value = ddlCurrency;
                cmd.Parameters.Add("@recordType", SqlDbType.Int).Value = recordType;
                cmd.Parameters.Add("@RecordCount", SqlDbType.Int).Direction = ParameterDirection.Output;
                cmd.Parameters.Add("@ErrorMessage", SqlDbType.VarChar, 400).Direction = ParameterDirection.Output;
                cmd.Parameters.Add("@PriceQuoteNo", SqlDbType.Int).Direction = ParameterDirection.Output;

                cn.Open();
                //return ExecuteNonQuery(cmd);
                ExecuteNonQuery(cmd);
                errorMessage = Convert.ToString(cmd.Parameters["@ErrorMessage"].Value);
                PriceQuoteNo = Convert.ToInt32(cmd.Parameters["@PriceQuoteNo"].Value);
                return (Int32)cmd.Parameters["@RecordCount"].Value;



            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to insert mapping Column detail", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        public override int DeleteRecord_PriceQuote(int SelectedClientID, System.Int32 userId, int ClientId, int clientType_con)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {

                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_delete_PriceQuote_TempRecord", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 600;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = ClientId;
                cmd.Parameters.Add("@UserId", SqlDbType.Int).Value = userId;
                cmd.Parameters.Add("@SelectedClientID", SqlDbType.Int).Value = SelectedClientID;
                cn.Open();
                return ExecuteNonQuery(cmd);

            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to insert mapping detail", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        public override DataTable GetSupplierMappedColumn_PriceQuote(int userId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_ImportSupplierColumnMapping_PriceQuote", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                //cmd.CommandTimeout = 60;
                cmd.CommandTimeout = 600;
                //cmd.Parameters.Add("@SupplierNo", SqlDbType.Int).Value = SupplierId;
                cmd.Parameters.Add("@UserId", SqlDbType.Int).Value = userId;
                cn.Open();
                DataSet ds = new DataSet();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);
                return ds.Tables[0];
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to get Supplier Column Mapping", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override int SaveSupplierColumnMapping_PriceQuote(int userId, string insertMapColumnList)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {

                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_insert_ImportColumnMapping_PriceQuoteImport", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 600;
                cmd.Parameters.Add("@strInsertMappings", SqlDbType.NVarChar).Value = insertMapColumnList;//[003]ColumnList
                cmd.Parameters.Add("@UserId", SqlDbType.Int).Value = userId;//[003]ColumnList
                cn.Open();
                return ExecuteNonQuery(cmd);

            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to insert mapping Column detail", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override DataTable GetStockImportactivity_PriceQuote(int displayLength, int displayStart, int sortCol, string sortDir, string search, int Clientid, int clientType_con, int SelectedClientId, int userLoginid)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {

                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_select_importactivity_PriceQuote", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@displayLength", SqlDbType.Int).Value = displayLength;
                cmd.Parameters.Add("@displayStart", SqlDbType.Int).Value = displayStart;
                cmd.Parameters.Add("@sortCol", SqlDbType.Int).Value = sortCol;
                cmd.Parameters.Add("@sortDir", SqlDbType.NVarChar, 10).Value = sortDir;
                cmd.Parameters.Add("@search", SqlDbType.NVarChar, 255).Value = search;
                cmd.Parameters.Add("@ClientType", SqlDbType.Int).Value = clientType_con;
                cmd.Parameters.Add("@selectedclientid", SqlDbType.Int).Value = SelectedClientId;
                cmd.Parameters.Add("@CreateBy", SqlDbType.Int).Value = userLoginid;
                cmd.CommandTimeout = 600;

                cn.Open();
                DataSet ds = new DataSet();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);
                return ds.Tables[0];
            }
            catch (SqlException sqlex)
            {

                throw new Exception("Failed to get Import Activity detail", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        //[012] code start
        //for Altranative Tool  code start
        /// <summary>
        /// Calls [usp_Insert_AltranativeHeaderExcelColumn]
        /// </summary>
        public override void SaveAltranativeHeader(string columnList, string insertColumnList, System.Int32? clientId, int? SelectedclientId, int? loginId, int clientType_con)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {

                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_Insert_AltranativeHeaderExcelColumn", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 600;
                cmd.Parameters.Add("@SelectColumns", SqlDbType.NVarChar, 3000).Value = columnList;
                cmd.Parameters.Add("@InserColumnList", SqlDbType.NVarChar, 3000).Value = insertColumnList;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@SelectedClientId", SqlDbType.Int).Value = SelectedclientId;
                cmd.Parameters.Add("@UserId", SqlDbType.Int).Value = loginId;

                cn.Open();
                cmd.ExecuteNonQuery();

            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to insert excel header columne for Altranative Import  ", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        /// <summary>
        /// Calls [usp_Insert_AltranativeExcelRows]
        /// </summary>
        public override void InsertAltranativeExcelRows(DataTable tempStock, DataTable dtData, string originalFilename, string generatedFilename, System.Int32 userId, System.Int32 clientId, int? SelectedclientId, int clientType_con)
        {
            SqlConnection cn = null;
            SqlCommand cmdcur = null;
            try
            {
                int fixedColumn = 15;
                //int totalColumn = 25;
                int columnCount = dtData.Columns.Count;
                // rename actual column according to excel file uploaded
                for (int i = 0; i < columnCount; i++)
                {
                    dtData.Columns[i].ColumnName = "Column" + (i + 1).ToString();
                }
                // add rest column to datatable to match fixed column to 15 range
                int leftOver = fixedColumn - columnCount;
                for (int j = 0; j < leftOver; j++)
                {
                    columnCount = columnCount + 1;
                    dtData.Columns.Add("Column" + (columnCount).ToString());
                }

                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmdcur = new SqlCommand("usp_Insert_AltranativeExcelRows", cn);
                cmdcur.CommandType = CommandType.StoredProcedure;
                cmdcur.CommandTimeout = 0;
                cmdcur.Parameters.AddWithValue("@UploadedData", dtData);
                cmdcur.Parameters.AddWithValue("@originalFilename", originalFilename);
                cmdcur.Parameters.AddWithValue("@generatedFilename", generatedFilename);
                cmdcur.Parameters.AddWithValue("@userId", userId);
                cmdcur.Parameters.AddWithValue("@clientId", clientId);
                cmdcur.Parameters.AddWithValue("@SelectedclientId", SelectedclientId);
                cmdcur.Parameters.AddWithValue("@Inactive", 0);
                cmdcur.Parameters.AddWithValue("@clientType_con", clientType_con);
                cn.Open();
                cmdcur.ExecuteNonQuery();


            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to insert excel Rows for Altranative Import", sqlex);
            }
            finally
            {
                cmdcur.Dispose();
                cn.Close();
                cn.Dispose();
            }

        }
        /// <summary>
        /// Calls [usp_Select_All_AltranativeRowsFromTemp]
        /// </summary>
        public override DataTable Get_All_AltranativeRowsFromTemp(int displayLength, int displayStart, int sortCol, string sortDir, string search, int userid, int Clientid, int SelectedclientId, int clientType_con)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {

                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_Select_All_AltranativeRowsFromTemp", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@displayLength", SqlDbType.Int).Value = displayLength;
                cmd.Parameters.Add("@displayStart", SqlDbType.Int).Value = displayStart;
                cmd.Parameters.Add("@sortCol", SqlDbType.Int).Value = sortCol;
                cmd.Parameters.Add("@sortDir", SqlDbType.NVarChar, 10).Value = sortDir;//[004]  
                cmd.Parameters.Add("@search", SqlDbType.NVarChar, 255).Value = search;//[004]  
                cmd.Parameters.Add("@userid", SqlDbType.Int).Value = userid;//[003]
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = Clientid;//[003]
                cmd.Parameters.Add("@SelectedClientId", SqlDbType.Int).Value = SelectedclientId;//[003]
                //cmd.CommandTimeout = 60;
                cmd.CommandTimeout = 900;

                cn.Open();
                DataSet ds = new DataSet();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);
                return ds.Tables[0];
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to get data from Altranative Table", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// Calls [usp_Select_ALL_AltranativeHeaderColumnTemp]
        /// </summary>
        public override DataTable Get_ALL_AltranativeHeaderColumnTemp(System.Int32 clientId, System.Int32 userId, System.Int32 SelectedClientID, int clientType_con)//GetExcelHeaderFrom
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {

                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_Select_ALL_AltranativeHeaderColumnTemp", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                //cmd.CommandTimeout = 60;
                cmd.CommandTimeout = 600;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@UserId", SqlDbType.Int).Value = userId;
                cmd.Parameters.Add("@SelectedClientID", SqlDbType.Int).Value = SelectedClientID;


                cn.Open();
                DataSet ds = new DataSet();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);
                return ds.Tables[0];
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get excel header", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        /// <summary>
        /// Calls [usp_Select_ALL_AltHeaderColumnDropdownBind]
        /// </summary>
        public override DataTable Get_Select_ALL_AltHeaderColumnDropdownBind(System.Int32 clientId, System.Int32 userId, System.Int32 SelectedClientID, int clientType_con)//GetExcelHeaderFrom
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {

                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_Select_ALL_AltHeaderColumnDropdownBind", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                //cmd.CommandTimeout = 60;
                cmd.CommandTimeout = 600;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@UserId", SqlDbType.Int).Value = userId;
                cmd.Parameters.Add("@SelectedClientID", SqlDbType.Int).Value = SelectedClientID;


                cn.Open();
                DataSet ds = new DataSet();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);
                return ds.Tables[0];
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get excel header", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        /// <summary>
        /// Calls [usp_Delete_AltranativeTempRecord]
        /// </summary>
        public override int DeleteAltranativeTempRecord(int SelectedClientID, System.Int32 userId, int ClientId, int clientType_con)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {

                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_Delete_AltranativeTempRecord", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 600;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = ClientId;
                cmd.Parameters.Add("@UserId", SqlDbType.Int).Value = userId;
                cmd.Parameters.Add("@SelectedClientID", SqlDbType.Int).Value = SelectedClientID;
                cn.Open();
                return ExecuteNonQuery(cmd);

            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to insert mapping detail", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        /// <summary>
        /// Calls [usp_Delete_AltranativeTempMapping]
        /// </summary>
        public override int DeleteAltranativeTempMapping(int SupplierId, int clientType_con)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {

                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_Delete_AltranativeTempMapping", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 600;
                cmd.Parameters.Add("@SupplierNo", SqlDbType.Int).Value = SupplierId;
                cn.Open();
                return ExecuteNonQuery(cmd);

            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to Delete mapping detail", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        /// <summary>
        /// Calls [usp_Insert_AltranativeSupplierColumnMapping]
        /// </summary>
        public override int InsertAltranativeSupplierColumnMapping(int SupplierId, string insertMapColumnList, int clientType_con)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {

                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_Insert_AltranativeSupplierColumnMapping", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 600;
                cmd.Parameters.Add("@strInsertMappings", SqlDbType.NVarChar).Value = insertMapColumnList;//[003]ColumnList
                cn.Open();
                return ExecuteNonQuery(cmd);

            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to insert mapping Column detail", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        /// <summary>
        /// Calls [usp_Select_AltranativeSupplierMappedColumn]
        /// </summary>
        public override DataTable GetAltranativeSupplierMappedColumn(int SupplierId, int clientType_con)
        {

            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {

                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_Select_AltranativeSupplierMappedColumn", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                //cmd.CommandTimeout = 60;
                cmd.CommandTimeout = 600;
                cmd.Parameters.Add("@SupplierNo", SqlDbType.Int).Value = SupplierId;
                cn.Open();
                DataSet ds = new DataSet();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);
                return ds.Tables[0];
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to get Supplier Column Mapping", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// Calls [usp_Select_GenerateAlternativeGridBind]
        /// </summary>
        public override DataTable GenerateAlternativeGridBind(int displayLength, int displayStart, int sortCol, string sortDir, string search, int userid, int Clientid, int SelectedclientId, string ColumnList, string ddlCurancy, string SupplierNameText, int clientType_con, string Column_Lable, string Column_Name)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {

                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_Select_GenerateAlternativeGridBind", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@displayLength", SqlDbType.Int).Value = displayLength;
                cmd.Parameters.Add("@displayStart", SqlDbType.Int).Value = displayStart;
                cmd.Parameters.Add("@sortCol", SqlDbType.Int).Value = sortCol;
                cmd.Parameters.Add("@sortDir", SqlDbType.NVarChar, 10).Value = sortDir;//[004]  
                cmd.Parameters.Add("@search", SqlDbType.NVarChar, 255).Value = search;//[004]  
                cmd.Parameters.Add("@userid", SqlDbType.Int).Value = userid;//[003]
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = Clientid;//[003]
                cmd.Parameters.Add("@SelectedClientId", SqlDbType.Int).Value = SelectedclientId;//[003]
                cmd.Parameters.Add("@list_Label_name", SqlDbType.NVarChar).Value = Column_Lable;
                cmd.Parameters.Add("@list_column_name", SqlDbType.NVarChar).Value = Column_Name;
                cmd.Parameters.Add("@SelectColumns", SqlDbType.NVarChar).Value = ColumnList;
                cmd.Parameters.Add("@CurancyColumns", SqlDbType.NVarChar).Value = ddlCurancy;
                cmd.Parameters.Add("@SupplierNameText", SqlDbType.NVarChar).Value = SupplierNameText;

                //cmd.CommandTimeout = 60;
                cmd.CommandTimeout = 600;

                cn.Open();
                DataSet ds = new DataSet();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);
                // return ds.Tables[0];
                // changes done to catch exception from SP
                if (ds.Tables.Count == 1)   // if exception is thrown
                    return ds.Tables[0];
                else
                    return ds.Tables[ds.Tables.Count - 1];   // if no exception then multiple tables collection return so picking last table
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get GetStockGenrateTempData", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        /// <summary>
        /// Calls [usp_Insert_Alternative_ImportData]
        /// </summary>
        public override int InsertAlternativeImportData(int userId, int ClientId, int SelectedclientId, string Column_Lable, string Column_Name, string insertDataList, string fileColName, int SupplierId, string ddlCurrency, int recordType, int clientType_con, out System.String errorMessage)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            errorMessage = string.Empty;
            try
            {

                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_Insert_Alternative_ImportData", cn);
                //cmd = new SqlCommand("procname", cn);

                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 1000;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = ClientId;
                cmd.Parameters.Add("@UserId", SqlDbType.Int).Value = userId;
                cmd.Parameters.Add("@SelectedClientID", SqlDbType.Int).Value = SelectedclientId;
                cmd.Parameters.Add("@list_Label_name", SqlDbType.NVarChar).Value = Column_Lable;
                cmd.Parameters.Add("@list_column_name", SqlDbType.NVarChar).Value = Column_Name;
                cmd.Parameters.Add("@insertDataList", SqlDbType.NVarChar).Value = insertDataList;
                cmd.Parameters.Add("@fileColName", SqlDbType.NVarChar).Value = fileColName;
                cmd.Parameters.Add("@SupplierId", SqlDbType.Int).Value = SupplierId;
                cmd.Parameters.Add("@CurrencyName", SqlDbType.NVarChar).Value = ddlCurrency;
                cmd.Parameters.Add("@recordType", SqlDbType.Int).Value = recordType;
                cmd.Parameters.Add("@RecordCount", SqlDbType.Int).Direction = ParameterDirection.Output;
                cmd.Parameters.Add("@ErrorMessage", SqlDbType.VarChar, 400).Direction = ParameterDirection.Output;

                cn.Open();
                //return ExecuteNonQuery(cmd);
                ExecuteNonQuery(cmd);
                errorMessage = Convert.ToString(cmd.Parameters["@ErrorMessage"].Value);
                return (Int32)cmd.Parameters["@RecordCount"].Value;


            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to insert mapping Column detail", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        /// <summary>
        /// Calls [usp_Select_GenerateDataAlternativeForCount]
        /// </summary>
        public override DataTable GenerateDataAlternativeForCount(int displayLength, int displayStart, int sortCol, string sortDir, string search, int userid, int Clientid, int SelectedclientId, int clientType_con)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {

                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_Select_GenerateDataAlternativeForCount", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@displayLength", SqlDbType.Int).Value = displayLength;
                cmd.Parameters.Add("@displayStart", SqlDbType.Int).Value = displayStart;
                cmd.Parameters.Add("@sortCol", SqlDbType.Int).Value = sortCol;
                cmd.Parameters.Add("@sortDir", SqlDbType.NVarChar, 10).Value = sortDir;//[004]  
                cmd.Parameters.Add("@search", SqlDbType.NVarChar, 255).Value = search;//[004]  
                cmd.Parameters.Add("@userid", SqlDbType.Int).Value = userid;//[003]
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = Clientid;//[003]
                cmd.Parameters.Add("@SelectedClientId", SqlDbType.Int).Value = SelectedclientId;//[003]
                //cmd.CommandTimeout = 60;
                cmd.CommandTimeout = 900;

                cn.Open();
                DataSet ds = new DataSet();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);
                return ds.Tables[0];
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get CustomerRequirements", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        //[012] code end,s hear

        public override DataTable GetExportToExcelError_StockImport(int userid, int ClientId, int SelectedclientId, string ColumnList, string Column_Lable, string Column_Name)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_ExcelUpload_Error_StockImport", cn);
                cmd.CommandType = CommandType.StoredProcedure;

                cmd.Parameters.Add("@userid", SqlDbType.Int).Value = userid;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = ClientId;
                cmd.Parameters.Add("@SelectedClientId", SqlDbType.Int).Value = SelectedclientId;
                cmd.Parameters.Add("@list_Label_name", SqlDbType.NVarChar).Value = Column_Lable;
                cmd.Parameters.Add("@list_column_name", SqlDbType.NVarChar).Value = Column_Name;
                cmd.Parameters.Add("@SelectColumns", SqlDbType.NVarChar).Value = ColumnList;
                cmd.CommandTimeout = 600;

                cn.Open();
                DataSet ds = new DataSet();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);
                return ds.Tables[0];
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Stock Import Error Excel", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override DataTable GetExportToExcelError_StrategicOffer(int userid, int ClientId, int SelectedclientId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_ExcelUpload_Error_StrategicOffer", cn);
                cmd.CommandType = CommandType.StoredProcedure;

                cmd.Parameters.Add("@userid", SqlDbType.Int).Value = userid;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = ClientId;
                cmd.Parameters.Add("@SelectedClientId", SqlDbType.Int).Value = SelectedclientId;
                cmd.CommandTimeout = 600;

                cn.Open();
                DataSet ds = new DataSet();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);
                return ds.Tables[0];
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Strategic Offer details", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override DataTable GetExportToExcelError_BomImport(int userid, int ClientId, int SelectedclientId, string ColumnList, string Column_Lable, string Column_Name)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_ExcelUpload_Error_BomImport", cn);
                cmd.CommandType = CommandType.StoredProcedure;

                cmd.Parameters.Add("@userid", SqlDbType.Int).Value = userid;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = ClientId;
                cmd.Parameters.Add("@SelectedClientId", SqlDbType.Int).Value = SelectedclientId;
                cmd.Parameters.Add("@list_Label_name", SqlDbType.NVarChar).Value = Column_Lable;
                cmd.Parameters.Add("@list_column_name", SqlDbType.NVarChar).Value = Column_Name;
                cmd.Parameters.Add("@SelectColumns", SqlDbType.NVarChar).Value = ColumnList;
                cmd.CommandTimeout = 600;

                cn.Open();
                DataSet ds = new DataSet();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);
                return ds.Tables[0];
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Bom Import Tool details", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override InsertedData InactivateEpoImportActivity(int clientId, int selectedImportId, int userId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_Inactivate_EpoImportActivity", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@ClientId", SqlDbType.VarChar).Value = clientId;
                cmd.Parameters.Add("@SelectedImportId", SqlDbType.VarChar).Value = selectedImportId;
                cmd.Parameters.Add("@InactiveBy", SqlDbType.Int).Value = userId;
                cmd.Parameters.Add("@IsSuccess", SqlDbType.Bit).Direction = ParameterDirection.Output;
                cmd.Parameters.Add("@OuputMessage", SqlDbType.NVarChar, 500).Direction = ParameterDirection.Output;
                cmd.CommandTimeout = 600;

                cn.Open();
                cmd.ExecuteNonQuery();
                return new InsertedData()
                {
                    ErrorMsg = (string)cmd.Parameters["@OuputMessage"].Value,
                    IsSuccess = (bool)cmd.Parameters["@IsSuccess"].Value
                };
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to get Inactivate Epo Import Activity", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override DataTable GetPriceQuoteDataTobeImported(int displayLength, int userId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_get_PriceQuote_DataToBeImported", cn)
                {
                    CommandType = CommandType.StoredProcedure,
                    CommandTimeout = 600
                };
                cmd.Parameters.Add("@DisplayLength", SqlDbType.Int).Value = displayLength;
                cmd.Parameters.Add("@UserId", SqlDbType.Int).Value = userId;

                cn.Open();
                DataSet ds = new DataSet();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);
                if (ds.Tables.Count == 1)   // if exception is thrown
                    return ds.Tables[0];
                else
                    return ds.Tables[ds.Tables.Count - 1];   // if no exception then multiple tables collection return so picking last table
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to get GetPriceQuoteDataTobeImported", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override int ImportPriceQuote(int userId, out int priceQuoteId, out int priceQuoteNumber, out string message)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            priceQuoteId = 0;
            priceQuoteNumber = 0;
            int recordCount = 0;
            message = string.Empty;
            try
            {
                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_Import_PriceQuote", cn)
                {
                    CommandType = CommandType.StoredProcedure,
                    CommandTimeout = 900,
                };
                cmd.Parameters.Add("@UserID", SqlDbType.Int).Value = userId;

                cn.Open();
                DataSet ds = new DataSet();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);

                if(ds.Tables != null && ds.Tables[ds.Tables.Count - 1].Rows != null)
                {
                    var dataRow = ds.Tables[ds.Tables.Count - 1].Rows[0];   //get last result table
                    priceQuoteId = Convert.ToInt32(dataRow["PriceQuoteNo"]);
                    priceQuoteNumber = Convert.ToInt32(dataRow["PriceQuoteNumber"]);
                    recordCount = Convert.ToInt32(dataRow["RecordCount"]);
                    message = dataRow["ImportMessage"].ToString();
                }

                return recordCount;
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to import price quotes", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override void SaveBOMImportSourcingData(DataTable dtData, string originalFilename, string generatedFilename, int hubrfqId, int loginId)
        {
            int timeOut = 900;
            string ConString = ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString();

            using (SqlBulkCopy bulkCopy = new SqlBulkCopy(ConString, SqlBulkCopyOptions.FireTriggers))
            {
                foreach (DataColumn c in dtData.Columns)
                {
                    var transformed = new string(c.ColumnName.Where(char.IsLetter).ToArray()).ToUpper();
                    switch (transformed)
                    {
                        case "MOQ": bulkCopy.ColumnMappings.Add(c.ColumnName, "MOQ"); break;
                        case "MSL": bulkCopy.ColumnMappings.Add(c.ColumnName, "MSL"); break;
                        case "SPQ": bulkCopy.ColumnMappings.Add(c.ColumnName, "SPQ"); break;
                        case "ROHS": bulkCopy.ColumnMappings.Add(c.ColumnName, "ROHS"); break;
                        case "BOMNO": bulkCopy.ColumnMappings.Add(c.ColumnName, "BOMNo"); break;
                        case "NOTES": bulkCopy.ColumnMappings.Add(c.ColumnName, "Notes"); break;
                        case "REGION": bulkCopy.ColumnMappings.Add(c.ColumnName, "Region"); break;
                        case "PACKAGE": bulkCopy.ColumnMappings.Add(c.ColumnName, "Package"); break;
                        case "BUYPRICE": bulkCopy.ColumnMappings.Add(c.ColumnName, "BuyPrice"); break;
                        case "CURRENCY": bulkCopy.ColumnMappings.Add(c.ColumnName, "Currency"); break;
                        case "DATECODE": bulkCopy.ColumnMappings.Add(c.ColumnName, "DateCode"); break;
                        case "LEADTIME": bulkCopy.ColumnMappings.Add(c.ColumnName, "LeadTime"); break;
                        case "CREATEDBY": bulkCopy.ColumnMappings.Add(c.ColumnName, "CreatedBy"); break;
                        case "SELLPRICE": bulkCopy.ColumnMappings.Add(c.ColumnName, "SellPrice"); break;
                        case "QTYINSTOCK": bulkCopy.ColumnMappings.Add(c.ColumnName, "QtyInStock"); break;
                        case "LASTTIMEBUY": bulkCopy.ColumnMappings.Add(c.ColumnName, "LastTimeBuy"); break;
                        case "OFFERSTATUS": bulkCopy.ColumnMappings.Add(c.ColumnName, "OfferStatus"); break;
                        case "DELIVERYDATE": bulkCopy.ColumnMappings.Add(c.ColumnName, "DeliveryDate"); break;
                        case "MANUFACTURER": bulkCopy.ColumnMappings.Add(c.ColumnName, "Manufacturer"); break;
                        case "SUPPLIERNAME": bulkCopy.ColumnMappings.Add(c.ColumnName, "SupplierName"); break;
                        case "SUPPLIERCOST": bulkCopy.ColumnMappings.Add(c.ColumnName, "SupplierCost"); break;
                        case "SHIPPINGCOST": bulkCopy.ColumnMappings.Add(c.ColumnName, "ShippingCost"); break;
                        case "OFFEREDQTY": bulkCopy.ColumnMappings.Add(c.ColumnName, "OfferedQuantity"); break;
                        case "FACTORYSEALED": bulkCopy.ColumnMappings.Add(c.ColumnName, "FactorySealed"); break;
                        case "CUSTOMERREFNO": bulkCopy.ColumnMappings.Add(c.ColumnName, "CustomerRefNo"); break;
                        case "SUPPLIERPARTNO": bulkCopy.ColumnMappings.Add(c.ColumnName, "SupplierPart"); break;
                        case "ORIGINALFILENAME": bulkCopy.ColumnMappings.Add(c.ColumnName, "OriginalFilename"); break;
                        case "GENERATEDFILENAME": bulkCopy.ColumnMappings.Add(c.ColumnName, "GeneratedFilename"); break;
                        case "REQUIREMENT": bulkCopy.ColumnMappings.Add(c.ColumnName, "CustomerRequirementNumber"); break;
                    }
                }

                bulkCopy.DestinationTableName = "BorisGlobalTraderImports.dbo.tbBomImportSourcingTemp";
                bulkCopy.BulkCopyTimeout = timeOut;
                try
                {
                    bulkCopy.WriteToServer(dtData);
                }
                catch (SqlException sqlex)
                {
                    throw new Exception("Failed to insert bulk upload to tbBomImportSourcingTemp", sqlex);
                }
            }
        }

        public override int ImportBOMSourcingResults(int loginId, out string outputMessage)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_import_BOMSourcingResults", cn)
                {
                    CommandType = CommandType.StoredProcedure,
                    CommandTimeout = 900
                };
                cmd.Parameters.Add("@UserId", SqlDbType.Int).Value = loginId;
                cmd.Parameters.Add("@ImportCount", SqlDbType.Int).Direction = ParameterDirection.Output;
                cmd.Parameters.Add("@ImportMessage", SqlDbType.NVarChar, 2000).Direction = ParameterDirection.Output;
                cn.Open();
                ExecuteNonQuery(cmd);

                outputMessage = Convert.ToString(cmd.Parameters["@ImportMessage"].Value);

                return Convert.ToInt32(cmd.Parameters["@ImportCount"].Value);
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to import BOM Sourcing Results", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override DataTable GetHUBOfferImportLargeHistory(int displayLength, int displayStart, int sortCol, string sortDir)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_get_HUBOfferImportLarge_History", cn)
                {
                    CommandType = CommandType.StoredProcedure,
                    CommandTimeout = 600
                };
                cmd.Parameters.Add("@DisplayLength", SqlDbType.Int).Value = displayLength;
                cmd.Parameters.Add("@DisplayStart", SqlDbType.Int).Value = displayStart;
                cmd.Parameters.Add("@SortCol", SqlDbType.Int).Value = sortCol;
                cmd.Parameters.Add("@SortDir", SqlDbType.NVarChar).Value = sortDir;
                cn.Open();
                DataSet ds = new DataSet();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);
                return ds.Tables[0];
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed in usp_get_HUBOfferImportLarge_History", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override void UpdateHUBOfferImportLargeCount(int logindId, int importId, int total)
        {
            SqlConnection cn = null;
            SqlCommand cmdcur = null;
            try
            {
                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmdcur = new SqlCommand("usp_update_HUBOfferImportLarge_Count", cn)
                {
                    CommandType = CommandType.StoredProcedure,
                    CommandTimeout = 600
                };
                cmdcur.Parameters.AddWithValue("@LoginID", logindId);
                cmdcur.Parameters.AddWithValue("@ImportID", importId);
                cmdcur.Parameters.AddWithValue("@Total", total);
                cn.Open();
                cmdcur.ExecuteNonQuery();
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to update HUB Offer Import Large Count", sqlex);
            }
            finally
            {
                cmdcur.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override void DeleteHUBOfferImportLarge(int logindId, int importId)
        {
            SqlConnection cn = null;
            SqlCommand cmdcur = null;
            try
            {
                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmdcur = new SqlCommand("usp_delete_HUBOfferImportLarge", cn)
                {
                    CommandType = CommandType.StoredProcedure,
                    CommandTimeout = 600
                };
                cmdcur.Parameters.AddWithValue("@LoginID", logindId);
                cmdcur.Parameters.AddWithValue("@ImportID", importId);
                cn.Open();
                cmdcur.ExecuteNonQuery();
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to delete HUB Offer Import Large record", sqlex);
            }
            finally
            {
                cmdcur.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override int UpdateVendorsOfferImportByExcelTemp(int importFileId, string incorrectVendor, string newVendor, int clientNo, int updatedBy)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_update_Vendors_OfferImportByExcelTemp", cn);

                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 900;
                cmd.Parameters.Add("@ImportFileId", SqlDbType.Int).Value = importFileId;
                cmd.Parameters.Add("@IncorrectVendor", SqlDbType.NVarChar).Value = incorrectVendor;
                cmd.Parameters.Add("@NewVendor", SqlDbType.NVarChar).Value = newVendor;
                cmd.Parameters.Add("@ClientNo", SqlDbType.Int).Value = clientNo;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@RecordCount", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();

                ExecuteNonQuery(cmd);
                return (Int32)cmd.Parameters["@RecordCount"].Value;
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to Update Vendor OfferImportByExcelTemp", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override int UpdateMFRsOfferImportByExcelTemp(int importFileId, string incorrectMFR, string newMFR, int clientNo, int updatedBy)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_update_MFR_OfferImportByExcelTemp", cn);

                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 900;
                cmd.Parameters.Add("@ImportFileId", SqlDbType.Int).Value = importFileId;
                cmd.Parameters.Add("@IncorrectMFR", SqlDbType.NVarChar).Value = incorrectMFR;
                cmd.Parameters.Add("@NewMFR", SqlDbType.NVarChar).Value = newMFR;
                cmd.Parameters.Add("@ClientNo", SqlDbType.Int).Value = clientNo;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@RecordCount", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();

                ExecuteNonQuery(cmd);
                return (Int32)cmd.Parameters["@RecordCount"].Value;
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to Update MFR OfferImportByExcelTemp", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override bool DeleteBomTempData(int loginId, string generatedFileName, int clientId, int selectedClientId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_delete_BOMTempData", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@UserID", SqlDbType.Int).Value = loginId;
                cmd.Parameters.Add("@ClientID", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@SelectedClientID", SqlDbType.Int).Value = selectedClientId;
                cmd.Parameters.Add("@GeneratedFileName", SqlDbType.NVarChar).Value = generatedFileName;
                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to delete BOM temp data", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override HubSourcingResultImportTempDetails GetHubSourcingTempData(int loginId, int pageNumber, int pageSize, bool showMismatchOnly)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_get_BOMRawData_ForCorrection", cn)
                {
                    CommandType = CommandType.StoredProcedure,
                    CommandTimeout = 60
                };
                cmd.Parameters.Add("@UserId", SqlDbType.Int).Value = loginId;
                cmd.Parameters.Add("@PageNumber", SqlDbType.Int).Value = pageNumber;
                cmd.Parameters.Add("@PageSize", SqlDbType.Int).Value = pageSize;
                cmd.Parameters.Add("@ShowMismatchOnly", SqlDbType.Bit).Value = showMismatchOnly;

                cn.Open();
                SqlDataReader reader = cmd.ExecuteReader();
                HubSourcingResultImportTempDetails details = new HubSourcingResultImportTempDetails()
                {
                    RawDataList = new List<SourcingResultImportTempDetails>()
                };
                while (reader.Read())
                {
                    SourcingResultImportTempDetails obj = new SourcingResultImportTempDetails()
                    {
                        BomImportSourcingId = GetReaderValue_Int32(reader, "BomImportSourcingId", 0),
                        Requirement = GetReaderValue_String(reader, "Requirement", ""),
                        RequirementMessage = GetReaderValue_String(reader, "RequirementMessage", ""),
                        CustomerRefNo = GetReaderValue_String(reader, "CustomerRefNo", ""),
                        CustomerRefNoMessage = GetReaderValue_String(reader, "CustomerRefNoMessage", ""),
                        Supplier = GetReaderValue_String(reader, "Supplier", ""),
                        SupplierMessage = GetReaderValue_String(reader, "SupplierMessage", ""),
                        SupplierPart = GetReaderValue_String(reader, "SupplierPart", ""),
                        SupplierPartMessage = GetReaderValue_String(reader, "SupplierPartMessage", ""),
                        SupplierCost = GetReaderValue_String(reader, "SupplierCost", ""),
                        SupplierCostMessage = GetReaderValue_String(reader, "SupplierCostMessage", ""),
                        ROHS = GetReaderValue_String(reader, "ROHS", ""),
                        ROHSMessage = GetReaderValue_String(reader, "ROHSMessage", ""),
                        MFR = GetReaderValue_String(reader, "MFR", ""),
                        MFRMessage = GetReaderValue_String(reader, "MFRMessage", ""),
                        DateCode = GetReaderValue_String(reader, "DateCode", ""),
                        DateCodeMessage = GetReaderValue_String(reader, "DateCodeMessage", ""),
                        Package = GetReaderValue_String(reader, "Package", ""),
                        PackageMessage = GetReaderValue_String(reader, "PackageMessage", ""),
                        OfferedQuantity = GetReaderValue_String(reader, "OfferedQuantity", ""),
                        OfferedQuantityMessage = GetReaderValue_String(reader, "OfferedQuantityMessage", ""),
                        OfferStatus = GetReaderValue_String(reader, "OfferStatus", ""),
                        OfferStatusMessage = GetReaderValue_String(reader, "OfferStatusMessage", ""),
                        SPQ = GetReaderValue_String(reader, "SPQ", ""),
                        SPQMessage = GetReaderValue_String(reader, "SPQMessage", ""),
                        FactorySealed = GetReaderValue_String(reader, "FactorySealed", ""),
                        FactorySealedMessage = GetReaderValue_String(reader, "FactorySealedMessage", ""),
                        QtyInStock = GetReaderValue_String(reader, "QtyInStock", ""),
                        QtyInStockMessage = GetReaderValue_String(reader, "QtyInStockMessage", ""),
                        MOQ = GetReaderValue_String(reader, "MOQ", ""),
                        MOQMessage = GetReaderValue_String(reader, "MOQMessage", ""),
                        LastTimeBuy = GetReaderValue_String(reader, "LastTimeBuy", ""),
                        LastTimeBuyMessage = GetReaderValue_String(reader, "LastTimeBuyMessage", ""),
                        Currency = GetReaderValue_String(reader, "Currency", ""),
                        CurrencyMessage = GetReaderValue_String(reader, "CurrencyMessage", ""),
                        BuyPrice = GetReaderValue_String(reader, "BuyPrice", ""),
                        BuyPriceMessage = GetReaderValue_String(reader, "BuyPriceMessage", ""),
                        SellPrice = GetReaderValue_String(reader, "SellPrice", ""),
                        SellPriceMessage = GetReaderValue_String(reader, "SellPriceMessage", ""),
                        ShippingCost = GetReaderValue_String(reader, "ShippingCost", ""),
                        ShippingCostMessage = GetReaderValue_String(reader, "ShippingCostMessage", ""),
                        LeadTime = GetReaderValue_String(reader, "LeadTime", ""),
                        LeadTimeMessage = GetReaderValue_String(reader, "LeadTimeMessage", ""),
                        Region = GetReaderValue_String(reader, "Region", ""),
                        RegionMessage = GetReaderValue_String(reader, "RegionMessage", ""),
                        DeliveryDate = GetReaderValue_String(reader, "DeliveryDate", ""),
                        DeliveryDateMessage = GetReaderValue_String(reader, "DeliveryDateMessage", ""),
                        Notes = GetReaderValue_String(reader, "Notes", ""),
                        NotesMessage = GetReaderValue_String(reader, "NotesMessage", ""),
                        MSL = GetReaderValue_String(reader, "MSL", ""),
                        MSLMessage = GetReaderValue_String(reader, "MSLMessage", ""),
                    };
                    details.RawDataList.Add(obj);
                    obj = null;
                }
                if (reader.NextResult())
                {
                    // Second result set: Total count
                    while (reader.Read())
                    {
                        details.TotalRecords = GetReaderValue_Int32(reader, "TotalCount", 0);
                    }
                }
                if (reader.NextResult())
                {
                    // Third result set: Total error count
                    while (reader.Read())
                    {
                        details.TotalRecordsError = GetReaderValue_Int32(reader, "TotalRecordsError", 0);
                    }
                }
                if (reader.NextResult())
                {
                    while (reader.Read())
                    {
                        SRImportTempIncorrectDetails obj = new SRImportTempIncorrectDetails()
                        {
                            ID = GetReaderValue_Int32(reader, "ID", 0),
                            Value = GetReaderValue_String(reader, "Value", ""),
                            Type = GetReaderValue_String(reader, "Type", ""),
                            Count = GetReaderValue_Int32(reader, "Count", 0)
                        };
                        details.IncorrectDataList.Add(obj);
                        obj = null;
                    }
                }

                return details;
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to get GetHubSourcingTempData", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override int DeleteHubSourcingTempData(int loginId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_delete_HubSourcingTempData", cn)
                {
                    CommandType = CommandType.StoredProcedure,
                    CommandTimeout = 600
                };
                cmd.Parameters.Add("@UserID", SqlDbType.Int).Value = loginId;
                cmd.Parameters.Add("@DeleteCount", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                cmd.ExecuteNonQuery();
                return (Int32)cmd.Parameters["@DeleteCount"].Value;
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to Delete Hub Sourcing TempData", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override int CorrectHubSourcingTempData(string incorrectValue, string newValue, string type, int loginId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_correct_HubSourcingTempData", cn)
                {
                    CommandType = CommandType.StoredProcedure,
                    CommandTimeout = 600
                };
                cmd.Parameters.Add("@UserId", SqlDbType.Int).Value = loginId;
                cmd.Parameters.Add("@IncorrectValue", SqlDbType.NVarChar).Value = incorrectValue;
                cmd.Parameters.Add("@NewValue", SqlDbType.NVarChar).Value = newValue;
                cmd.Parameters.Add("@Type", SqlDbType.NVarChar).Value = type;
                cmd.Parameters.Add("@RecordCount", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                cmd.ExecuteNonQuery();
                return (Int32)cmd.Parameters["@RecordCount"].Value;
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to CorrectHubSourcingTempData", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override int BulkSaveHubSourcingTempData(string jsonData, int loginId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_bulksave_HubSourcingTempData", cn)
                {
                    CommandType = CommandType.StoredProcedure,
                    CommandTimeout = 900
                };
                cmd.Parameters.Add("@JsonData", SqlDbType.NVarChar).Value = jsonData;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = loginId;
                cmd.Parameters.Add("@RecordCount", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();

                ExecuteNonQuery(cmd);
                return (Int32)cmd.Parameters["@RecordCount"].Value;
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to BulkSaveHubSourcingTempData", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override List<string> GetBOMPartsForIHS(int loginId, int clientId, int selectedClientId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_get_BOMParts_ForIHS", cn)
                {
                    CommandType = CommandType.StoredProcedure,
                    CommandTimeout = 60
                };
                cmd.Parameters.Add("@UserId", SqlDbType.Int).Value = loginId;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@SelectedClientId", SqlDbType.Int).Value = selectedClientId;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<string> lst = new List<string>();
                while (reader.Read())
                {
                    string part = GetReaderValue_String(reader, "Part", "");
                    if (!string.IsNullOrEmpty(part))
                    {
                        lst.Add(part);
                    }
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to get BOM Import Parts to trigger IHS API", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
    }
}
