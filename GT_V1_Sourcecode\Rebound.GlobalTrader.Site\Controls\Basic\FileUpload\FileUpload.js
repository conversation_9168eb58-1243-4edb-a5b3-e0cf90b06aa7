Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.FileUpload");Rebound.GlobalTrader.Site.Controls.FileUpload=function(n){Rebound.GlobalTrader.Site.Controls.FileUpload.initializeBase(this,[n]);this._win=null;this._doc=null;this._strErrorMessage="";this._strUploadedFilename="";this._intTimeoutSeconds=10;this._aryAllowedExtensions=[];this._intTimeoutID=-1;this._strSectionName="";this._intDocumentId=0;this._strExcel=""};Rebound.GlobalTrader.Site.Controls.FileUpload.prototype={get_ifmUpload:function(){return this._ifmUpload},set_ifmUpload:function(n){this._ifmUpload!==n&&(this._ifmUpload=n)},get_strAllowedExtensions:function(){return this._strAllowedExtensions},set_strAllowedExtensions:function(n){this._strAllowedExtensions!==n&&(this._strAllowedExtensions=n)},get_dblMaxFileSizeInMb:function(){return this._dblMaxFileSizeInMb},set_dblMaxFileSizeInMb:function(n){this._dblMaxFileSizeInMb!==n&&(this._dblMaxFileSizeInMb=n)},get_strSectionName:function(){return this._strSectionName},set_strSectionName:function(n){this._strSectionName!==n&&(this._strSectionName=n)},get_strExcel:function(){return this._strExcel},set_strExcel:function(n){this._strExcel!==n&&(this._strExcel=n)},addFileNotSelected:function(n){this.get_events().addHandler("FileNotSelected",n)},removeFileNotSelected:function(n){this.get_events().removeHandler("FileNotSelected",n)},onFileNotSelected:function(){var n=this.get_events().getHandler("FileNotSelected");n&&n(this,Sys.EventArgs.Empty)},addFileTooBig:function(n){this.get_events().addHandler("FileTooBig",n)},removeFileTooBig:function(n){this.get_events().removeHandler("FileTooBig",n)},onFileTooBig:function(){var n=this.get_events().getHandler("FileTooBig");n&&n(this,Sys.EventArgs.Empty)},addUploadComplete:function(n){this.get_events().addHandler("UploadComplete",n)},removeUploadComplete:function(n){this.get_events().removeHandler("UploadComplete",n)},onUploadComplete:function(){var n=this.get_events().getHandler("UploadComplete");n&&n(this,Sys.EventArgs.Empty)},addUploadFailed:function(n){this.get_events().addHandler("UploadFailed",n)},removeUploadFailed:function(n){this.get_events().removeHandler("UploadFailed",n)},onUploadFailed:function(){var n=this.get_events().getHandler("UploadFailed");n&&n(this,Sys.EventArgs.Empty)},addTimeout:function(n){this.get_events().addHandler("Timeout",n)},removeTimeout:function(n){this.get_events().removeHandler("Timeout",n)},onTimeout:function(){var n=this.get_events().getHandler("Timeout");n&&n(this,Sys.EventArgs.Empty)},addFileNotAllowedType:function(n){this.get_events().addHandler("FileNotAllowedType",n)},removeFileNotAllowedType:function(n){this.get_events().removeHandler("FileNotAllowedType",n)},onFileNotAllowedType:function(){var n=this.get_events().getHandler("FileNotAllowedType");n&&n(this,Sys.EventArgs.Empty)},initialize:function(){Rebound.GlobalTrader.Site.Controls.FileUpload.callBaseMethod(this,"initialize");this._win=this._ifmUpload.contentWindow;this._doc=this._ifmUpload.contentDocument;this._aryAllowedExtensions=$R_FN.singleStringToArray(this._strAllowedExtensions)},dispose:function(){this.isDisposed||(this.get_element()&&$clearHandlers(this.get_element()),this.clearTimeout(),this._win=null,this._doc=null,this._aryAllowedExtensions=null,this._ifmUpload=null,this._strErrorMessage=null,this._strUploadedFilename=null,this._intTimeoutSeconds=null,this._intTimeoutID=null,this._strSectionName=null,this._strExcel=null,this._intDocumentId=null,Rebound.GlobalTrader.Site.Controls.FileUpload.callBaseMethod(this,"dispose"),this.isDisposed=!0)},setupFrame:function(){this._win=this._ifmUpload.contentWindow;this._doc=this._ifmUpload.contentDocument;this._win.setFileUploadID(this._element.id)},doUpload:function(){this.setupFrame();this.checkAllowedType()?(this._win.doSubmit(),this._intTimeoutID=setTimeout(Function.createDelegate(this,this.checkPageLoaded),this._intTimeoutSeconds*1e3)):(this._strErrorMessage=$R_RES.FileUploadNotAllowedType,this.onFileNotAllowedType())},checkPageLoaded:function(){this.clearTimeout();var n=!0;try{n=this._win.blnPageLoaded}catch(t){n=!1}n||(this._strErrorMessage=$R_RES.FileUploadFailed,this.onTimeout())},getValue:function(){var n=this.getFileControl(),t=n?n.value:"";return n=null,t},setValue:function(n){var t=this.getFileControl();t.value=n;t=null},reset:function(){this._win.location=String.format("FileUpload.aspx?mxs={0}&section={1}&docId={2}&excel={3}",this._dblMaxFileSizeInMb,this._strSectionName,this._intDocumentId,this._strExcel)},checkEntered:function(){this.setupFrame();var n=this.getFileControl(),t=n?n.value.length>0:!1;return n=null,t},getFileControl:function(){return this.setupFrame(),this._doc.getElementById("filFile")},showFieldError:function(n){try{this._win.setError(n)}catch(t){}},checkAllowedType:function(){for(var t=!1,i=this.getFileControl(),n=0,r=this._aryAllowedExtensions.length;n<r;n++)if(i.value.toString().toUpperCase().endsWith("."+this._aryAllowedExtensions[n].toString().toUpperCase())){t=!0;break}return i=null,t},clearTimeout:function(){this._intTimeoutID!=-1&&clearTimeout(this._intTimeoutID)}};Rebound.GlobalTrader.Site.Controls.FileUpload.registerClass("Rebound.GlobalTrader.Site.Controls.FileUpload",Sys.UI.Control,Sys.IDisposable);