Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DataListNuggets");Rebound.GlobalTrader.Site.Controls.DataListNuggets.CustomerRequirementsPrint=function(n){Rebound.GlobalTrader.Site.Controls.DataListNuggets.CustomerRequirementsPrint.initializeBase(this,[n]);this._blnGet=!0;this._intCompanyNo=-1;this._intContactNo=-1};Rebound.GlobalTrader.Site.Controls.DataListNuggets.CustomerRequirementsPrint.prototype={get_intCompanyID:function(){return this._intCompanyID},set_intCompanyID:function(n){this._intCompanyID!==n&&(this._intCompanyID=n)},get_strCompanyName:function(){return this._strCompanyName},set_strCompanyName:function(n){this._strCompanyName!==n&&(this._strCompanyName=n)},get_cmbCustomer:function(){return this._cmbCustomer},set_cmbCustomer:function(n){this._cmbCustomer!==n&&(this._cmbCustomer=n)},get_ddlContact:function(){return this._ddlContact},set_ddlContact:function(n){this._ddlContact!==n&&(this._ddlContact=n)},get_intContactID:function(){return this._intContactID},set_intContactID:function(n){this._intContactID!==n&&(this._intContactID=n)},initialize:function(){this.addSetupDataCallEvent(Function.createDelegate(this,this.setupDataCall));this.addGetDataOKEvent(Function.createDelegate(this,this.getDataOK));this.addInitCompleteEvent(Function.createDelegate(this,this.initAfterBaseIsReady));this.addSortDataEvent(Function.createDelegate(this,this.getSorting));this._ibtnPrint=$get(this._aryButtonIDs[0]);this._strPathToData="controls/DataListNuggets/CustomerRequirementsPrint";this._strDataObject="CustomerRequirementsPrint";this.enableBulkButtons(!1);this._table.addMultipleSelectionChanged(Function.createDelegate(this,this.selectionMade));this._strCK="Req";this._strCKExp=1;this._lnsSeperator="|";this._ibtnPrint&&$R_IBTN.addClick(this._ibtnPrint,Function.createDelegate(this,this.printCustReq));Rebound.GlobalTrader.Site.Controls.DataListNuggets.CustomerRequirementsPrint.callBaseMethod(this,"initialize")},initAfterBaseIsReady:function(){this._cmbCustomer&&(this._cmbCustomer.setValue(this._intCompanyID,this._strCompanyName),this._cmbCustomer._aut.addSelectionMadeEvent(Function.createDelegate(this,this.selectedCustomer)),$addHandler($get("ctl00_cphMain_ctlCustReqPrint_ctlDB_ctl19_ctlFilter_ctlCustomer_ctl04_aut_ctl04"),"click",Function.createDelegate(this,this.reselectData)));this._intCompanyID&&this._intCompanyID!=-1&&this._intContactID&&this._intContactID!=-1?(this._intCompanyNo=this._intCompanyID,this._intContactNo=this._intContactID,this.selectedCustomer(),this._ddlContact.setValue(this._intContactNo),this.getData()):this.showLoading(!1)},dispose:function(){this.isDisposed||(this._intCompanyID=null,this._strCompanyName=null,this._cmbCustomer=null,this._ddlContact=null,this._intContactID=null,this._blnGet=null,this._intCompanyNo=null,this._intContactNo=null,this._ibtnPrint&&(this._ibtnPrint=null),Rebound.GlobalTrader.Site.Controls.DataListNuggets.CustomerRequirementsPrint.callBaseMethod(this,"dispose"))},enableBulkButtons:function(n){this._ibtnPrint&&$R_IBTN.enableButton(this._ibtnPrint,n)},setupDataCall:function(){this._objData.addParameter("CmpID",this._intCompanyNo);this._objData.addParameter("ConID",this._intContactNo);this._objData.addParameter("IsGet",this._blnGet);this._blnGet=!1;this._objData.addParameter("PageLimit",this._txtLimitResults.value)},printCustReq:function(){if(this._cmbCustomer&&(this._intCompanyID=this._cmbCustomer.getValue()),!this._intCompanyID){alert("Please select customer");return}if(!this._ddlContact.getValue()){alert("Please select contact.");return}var n=$R_FN.arrayToSingleString(this._table._aryCurrentValues,this._lnsSeperator);$R_FN.setCookie(this._strCK,n,this._strCKExp);n="";$R_FN.openPrintWindowCustReqWithMultiples($R_ENUM$PrintObject.CustomerRequirement,this._intCompanyID);n=null},selectionMade:function(){this.enableBulkButtons(this._table._arySelectedIndexes.length>0)},getDataOK:function(){for(var n,i,t=0,r=this._objResult.Results.length;t<r;t++)n=this._objResult.Results[t],i=[$RGT_nubButton_CustomerRequirement(n.ID,n.No),$R_FN.writeDoubleCellValue($R_FN.writePartNo(n.Part,n.ROHS),$RGT_nubButton_Manufacturer(n.MfrNo,n.Mfr)),n.Quantity,$R_FN.writeDoubleCellValue($RGT_nubButton_Company(n.CMNo,n.CM),$RGT_nubButton_Contact(n.ContactNo,n.Contact)),$R_FN.setCleanTextValue(n.Salesman),$R_FN.writeDoubleCellValue(n.Received,n.Promised)],this._table.addRow(i,n.ID,!1),i=null,n=null},enableApplyFilter:function(n){this._ibtnApply&&$R_IBTN.enableButton(this._ibtnApply,n)},applyFilter:function(){if(this._cmbCustomer&&(this._intCompanyNo=this._cmbCustomer.getValue()),!this._intCompanyNo){alert("Please select customer.");return}if(this._ddlContact&&(this._intContactNo=this._ddlContact.getValue()),!this._intContactNo){alert("Please select contact.");return}(this._blnGet=!0,this._blnGettingData)||this.onFilterData()},selectedCustomer:function(){this._table.clearTable();this._table.resizeColumns();this._table.clearSelection(!0);this._cmbCustomer&&(this._intCompanyNo=this._cmbCustomer.getValue());this._intCompanyNo||(this._intCompanyNo=0);this._ddlContact._intCompanyID=this._intCompanyNo;this._ddlContact.getData();this.selectionMade()},resetFilter:function(){for(var t,n=0,i=this._aryFilterFieldIDs.length;n<i;n++)t=$find(this._aryFilterFieldIDs[n]),t.reset(),t.resetToDefault(),t=null;this._blnAllowSelection&&(this._txtLimitResults.value=50,this._intResultsLimit=50);this._cmbCustomer.setValue("","")},reselectData:function(){this._table.clearTable();this._table.resizeColumns();this._table.clearSelection(!0);this._blnGet=!1;this._ddlContact._intCompanyID=-1;this._ddlContact.getData();this.getData()},getSorting:function(){this._blnGet=!0}};Rebound.GlobalTrader.Site.Controls.DataListNuggets.CustomerRequirementsPrint.registerClass("Rebound.GlobalTrader.Site.Controls.DataListNuggets.CustomerRequirementsPrint",Rebound.GlobalTrader.Site.Controls.DataListNuggets.Base,Sys.IDisposable);