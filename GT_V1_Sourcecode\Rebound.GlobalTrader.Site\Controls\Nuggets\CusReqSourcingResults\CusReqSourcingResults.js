Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Nuggets");Rebound.GlobalTrader.Site.Controls.Nuggets.CustomerRequirementSourcingResults=function(n){Rebound.GlobalTrader.Site.Controls.Nuggets.CustomerRequirementSourcingResults.initializeBase(this,[n]);this._intCustomerRequirementID=-1;this._intCompanyID=-1;this._blnRequirementClosed=!1;this._blnFromPOHub=!1;this._HasQuote=!1;this._IsSoCreated=!1;this._IsClosed=!1;this._IsDifferentClient=!1;this._msl=!0;this._mfrAdvisoryNotes=""};Rebound.GlobalTrader.Site.Controls.Nuggets.CustomerRequirementSourcingResults.prototype={get_intCustomerRequirementID:function(){return this._intCustomerRequirementID},set_intCustomerRequirementID:function(n){this._intCustomerRequirementID!==n&&(this._intCustomerRequirementID=n)},get_tbl:function(){return this._tbl},set_tbl:function(n){this._tbl!==n&&(this._tbl=n)},get_ibtnAdd:function(){return this._ibtnAdd},set_ibtnAdd:function(n){this._ibtnAdd!==n&&(this._ibtnAdd=n)},get_ibtnEdit:function(){return this._ibtnEdit},set_ibtnEdit:function(n){this._ibtnEdit!==n&&(this._ibtnEdit=n)},get_ibtnQuote:function(){return this._ibtnQuote},set_ibtnQuote:function(n){this._ibtnQuote!==n&&(this._ibtnQuote=n)},get_ctlMultiSelectionCount:function(){return this._ctlMultiSelectionCount},set_ctlMultiSelectionCount:function(n){this._ctlMultiSelectionCount!==n&&(this._ctlMultiSelectionCount=n)},get_ibtnDelete:function(){return this._ibtnDelete},set_ibtnDelete:function(n){this._ibtnDelete!==n&&(this._ibtnDelete=n)},addAddFormShown:function(n){this.get_events().addHandler("AddFormShown",n)},removeAddFormShown:function(n){this.get_events().removeHandler("AddFormShown",n)},onAddFormShown:function(){var n=this.get_events().getHandler("AddFormShown");n&&n(this,Sys.EventArgs.Empty)},addSourcingResultAdded:function(n){this.get_events().addHandler("SourcingResultAdded",n)},removeSourcingResultAdded:function(n){this.get_events().removeHandler("SourcingResultAdded",n)},onSourcingResultAdded:function(){var n=this.get_events().getHandler("SourcingResultAdded");n&&n(this,Sys.EventArgs.Empty)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Nuggets.CustomerRequirementSourcingResults.callBaseMethod(this,"initialize");this.addRefreshEvent(Function.createDelegate(this,this.getData));this._tbl.addMultipleSelectionChanged(Function.createDelegate(this,this.selectResult));this._ctlMultiSelectionCount.registerTable(this._tbl);this._ibtnQuote&&$R_IBTN.addClick(this._ibtnQuote,Function.createDelegate(this,this.doQuote));this._ibtnAdd&&($R_IBTN.addClick(this._ibtnAdd,Function.createDelegate(this,this.showAddForm)),this._frmAdd=$find(this._aryFormIDs[0]),this._frmAdd.addCancel(Function.createDelegate(this,this.cancelAdd)),this._frmAdd.addSaveComplete(Function.createDelegate(this,this.saveAddComplete)));this._ibtnEdit&&($R_IBTN.addClick(this._ibtnEdit,Function.createDelegate(this,this.showEditForm)),this._frmEdit=$find(this._aryFormIDs[1]),this._frmEdit.addCancel(Function.createDelegate(this,this.cancelEdit)),this._frmEdit.addSaveComplete(Function.createDelegate(this,this.saveEditComplete)));this._ibtnDelete&&($R_IBTN.addClick(this._ibtnDelete,Function.createDelegate(this,this.showPartWatchDeleteForm)),this._frmDelete=$find(this._aryFormIDs[2]),this._frmDelete.addCancel(Function.createDelegate(this,this.cancelPartWatchDelete)),this._frmDelete.addNotConfirmed(Function.createDelegate(this,this.cancelPartWatchDelete)),this._frmDelete.addSaveComplete(Function.createDelegate(this,this.savePartWatchDeleteComplete)));this.getData()},dispose:function(){this.isDisposed||(this._ibtnAdd&&$R_IBTN.clearHandlers(this._ibtnAdd),this._ibtnEdit&&$R_IBTN.clearHandlers(this._ibtnEdit),this._ibtnQuote&&$R_IBTN.clearHandlers(this._ibtnQuote),this._tbl&&this._tbl.dispose(),this._ctlMultiSelectionCount&&this._ctlMultiSelectionCount.dispose(),this._intCustomerRequirementID=null,this._intCompanyID=null,this._tbl=null,this._ibtnAdd=null,this._ibtnEdit=null,this._ibtnQuote=null,this._ctlMultiSelectionCount=null,this._blnRequirementClosed=null,this._blnFromPOHub=null,this._msl=null,this._ibtnDelete&&$R_IBTN.clearHandlers(this._ibtnDelete),this._frmDelete&&this._frmDelete.dispose(),this._frmDelete=null,this._ibtnDelete=null,this._mfrAdvisoryNotes=null,Rebound.GlobalTrader.Site.Controls.Nuggets.CustomerRequirementSourcingResults.callBaseMethod(this,"dispose"))},getData:function(){this._ctlMultiSelectionCount.clearAll();this.getData_Start();this.enableButtons(!1);var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/CusReqSourcingResults");n.set_DataObject("CusReqSourcingResults");n.set_DataAction("GetData");n.addParameter("id",this._intCustomerRequirementID);n.addDataOK(Function.createDelegate(this,this.getDataOK));n.addError(Function.createDelegate(this,this.getDataError));n.addTimeout(Function.createDelegate(this,this.getDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getDataOK:function(n){var u,e,f,o,r,h,c,l;for(this._blnFromPOHub=!1,u=n._result,this._tbl.clearTable(),e="",f=0,o=u.Results.length;f<o;f++){var t=u.Results[f],i="",s="";for(t.IsHub&&(s="sourcingNotesIsHUB"),r=0,h=t.Quotes.length;r<h;r++)i+=$RGT_nubButton_Quote(t.Quotes[r].ID,t.Quotes[r].No);c=[t.IsHub?$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(t.Supplier)+$R_FN.createAdvisoryNotesIcon(t.SupplierAdvisoryNotes,"margin-left-10")+"<br />"+t.SupplierType,i):t.DiffrentClientOffer?$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(t.Supplier)+$R_FN.createAdvisoryNotesIcon(t.SupplierAdvisoryNotes,"margin-left-10")+"<br />"+t.ClientCode,i):$R_FN.writeDoubleCellValue($RGT_nubButton_Company(t.SupplierNo,t.Supplier,null,null,null,t.SupplierAdvisoryNotes),i),$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(t.Part),"<span class ="+s+">"+$R_FN.setCleanTextValue(t.NotesMSL)+"<\/span>"),$R_FN.writeDoubleCellValue($RGT_nubButton_Manufacturer(t.MfrNo,t.Mfr,t.MfrAdvisoryNotes),$R_FN.setCleanTextValue(t.DC)),$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(t.Product),$R_FN.setCleanTextValue(t.Package)),$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(t.Date),$R_FN.setCleanTextValue(t.By)),$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(t.Qty),$R_FN.setCleanTextValue(t.Status)),$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(t.Price),$R_FN.setCleanTextValue(t.PriceInBase)),$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(t.RegionName),$R_FN.setCleanTextValue(t.TermsName)),$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(t.EstimatedShippingCost),$R_FN.setCleanTextValue(t.EstimatedShippingCostInBase)),$R_FN.setCleanTextValue(t.PartWatchMatch==!0?"<input type='checkbox' checked onclick='return false'/>":"<input type='checkbox' onclick='return false'/>")];l={POHub:t.IsHub,HasQuote:t.Quotes.length>0?!1:!0,IsClosed:t.IsClosed,IsSoCreated:t.IsSoCreated,MSL:t.MSL,QLineID:t.ID,IsPartWatchMatch:t.PartWatchMatch,IsDifferentClient:t.DiffrentClientOffer,SupplierAdvisoryNotes:t.SupplierAdvisoryNotes,MfrAdvisoryNotes:t.MfrAdvisoryNotes};e=t.PartWatchMatch==!0?"rowGreyBackground":"";this._tbl.addRowRowColor(c,t.ID,!1,l,null,null,t.PartWatchMatch,e);t=null;i=null}this._tbl.resizeColumns();this.getDataOK_End();this.showNoData(u.Results.length==0)},getDataError:function(n){this.showError(!0,n.get_ErrorMessage())},selectResult:function(){this.clearMessages();var n=this._tbl.getSelectedExtraData(this._tbl._arySelectedIndexes[0]);n&&(this._blnFromPOHub=n.POHub,this._IsSoCreated=n.IsSoCreated,this._IsClosed=n.IsClosed,this._IsDifferentClient=n.IsDifferentClient,this._HasQuote=n.HasQuote==!1&&this._blnFromPOHub==!0?!1:!0);this.enableButtons(!0)},checkMSLValue:function(){var t=!0,r=this._tbl._aryCurrentValues,n=this._tbl._aryExtraData;for(i=0;i<r.length;i++)for(j=0;j<n.length;j++)n[j].QLineID===r[i]&&n[j].MSL===""&&(this._msl=!1,t=!1);return t},enableButtons:function(n){var t,i;if(n){for(t=!1,i=0;i<this._tbl._aryCurrentValues.length;i++)if(this._tbl.getSelectedExtraData(this._tbl._arySelectedIndexes[i]).IsPartWatchMatch==!0)t=!0;else{t=!1;break}this._ibtnAdd&&$R_IBTN.enableButton(this._ibtnAdd,!this._blnRequirementClosed);this._ibtnEdit&&$R_IBTN.enableButton(this._ibtnEdit,!this._blnRequirementClosed&&this._tbl._aryCurrentValues.length==1&&!this._blnFromPOHub&&this._IsDifferentClient==!1);this._ibtnQuote&&$R_IBTN.enableButton(this._ibtnQuote,!this._blnRequirementClosed&&this._tbl._aryCurrentValues.length>0&&!(this._IsSoCreated||this._IsClosed)&&this._IsDifferentClient==!1);this._ibtnDelete&&$R_IBTN.enableButton(this._ibtnDelete,this._tbl._aryCurrentValues.length>0&&!this._blnRequirementClosed&&t==!0)}else this._ibtnEdit&&$R_IBTN.enableButton(this._ibtnEdit,!1),this._ibtnQuote&&$R_IBTN.enableButton(this._ibtnQuote,!1),this._ibtnDelete&&$R_IBTN.enableButton(this._ibtnDelete,!1)},doQuote:function(){if(this.clearMessages(),this.checkMSLValue())location.href=$RGT_gotoURL_QuoteAdd(this._intCompanyID,null,this._intCustomerRequirementID,$R_FN.arrayToSingleString(this._tbl._aryCurrentValues));else return this.addMessage("Kindly update MSL",$R_ENUM$MessageTypeList.Error),!1},showAddForm:function(){this._frmAdd.setFormFieldsToDefaults();this.onAddFormShown();this._frmAdd._intCustomerRequirementID=this._intCustomerRequirementID;this._frmAdd._strPartNo=this._strPartNo;this._frmAdd._intSupplierId=-1;this._frmAdd._mfrAdvisoryNotes=$("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl13_hidMfrNotes_hid").val();this.showForm(this._frmAdd,!0)},hideAddForm:function(){this.showForm(this._frmAdd,!1);this._tbl.resizeColumns()},cancelAdd:function(){this.hideAddForm()},saveAddComplete:function(){this.getData();this.hideAddForm();this.showSavedOK(!0,$R_RES.ChangesSavedSuccessfully);this.onSourcingResultAdded()},showEditForm:function(){this._frmEdit._intSourcingResultID=this._tbl._aryCurrentValues[0];var n=this._tbl.getSelectedExtraData(this._tbl._arySelectedIndexes[0]);this._frmEdit._supplierAdvisoryNotes=n.SupplierAdvisoryNotes;this._frmEdit._mfrAdvisoryNotes=n.MfrAdvisoryNotes;this.showForm(this._frmEdit,!0)},hideEditForm:function(){this.showForm(this._frmEdit,!1);this._tbl.resizeColumns()},cancelEdit:function(){this.hideEditForm()},saveEditComplete:function(){this.getData();this.hideEditForm();this.showSavedOK(!0,$R_RES.ChangesSavedSuccessfully);this.onSaveEditComplete()},showPartWatchDeleteForm:function(){this._frmDelete._intSourcingResultID=this._tbl._aryCurrentValues[0];this._frmDelete._aryCurrentValues=$R_FN.arrayToSingleString(this._tbl._aryCurrentValues);this.showForm(this._frmDelete,!0)},cancelPartWatchDelete:function(){this.showForm(this._frmDelete,!1);this._tbl.resizeColumns()},savePartWatchDeleteComplete:function(){this.getData();this.hideEditForm();this.showSavedOK(!0,$R_RES.ChangesSavedSuccessfully);this.onSaveEditComplete()}};Rebound.GlobalTrader.Site.Controls.Nuggets.CustomerRequirementSourcingResults.registerClass("Rebound.GlobalTrader.Site.Controls.Nuggets.CustomerRequirementSourcingResults",Rebound.GlobalTrader.Site.Controls.Nuggets.Base);