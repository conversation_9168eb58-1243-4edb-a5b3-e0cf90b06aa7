﻿SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER OFF
GO
/*   
===========================================================================================  
TASK			UPDATED BY      DATE			ACTION		DESCRIPTION  
[US-204867]		An.TranTan		25-Sep-2024		Create		Insert prospective offer import data
[US-215028]		Trung Pham		29-Oct-2024		UPDATE		Add Notes columns
[US-215028]		Trung Pham 		12-Nov-2024		UPDATE		Add FullPart columns
===========================================================================================  
*/
CREATE OR ALTER PROCEDURE [dbo].[usp_insert_ProspectiveOfferImport]
	@UserId INT,
	@SupplierId INT,
	@RecordCount INT OUTPUT,                                                  
	@OutputMessage VARCHAR(2000) Output
AS
BEGIN
	SET NOCOUNT ON;
	SET @OutputMessage = NULL;
	SET @RecordCount = 0;
	DECLARE @InsertedProsOfferId INT,
			@OriginalFilename NVARCHAR(100) = NULL;

	BEGIN TRY
		BEGIN TRANSACTION
		IF EXISTS (SELECT TOP 1 1 FROM BorisGlobalTraderImports.dbo.tbProspectiveOffer_ToBeImported 
					WHERE SupplierNo = @SupplierId AND CreatedBy = @UserId)
		BEGIN
			SELECT TOP 1 @OriginalFilename = OriginalFilename
			FROM BorisGlobalTraderImports.dbo.tbProspectiveOffer_ToBeImported 
			WHERE SupplierNo = @SupplierId AND CreatedBy = @UserId;

			INSERT INTO dbo.tbProspectiveOffers
			(
				SupplierNo,
				SourceFileName,
				ImportRowCount,
				ImportStatus,
				CreatedBy,
				DLUP
			)VALUES(@SupplierId, @OriginalFilename, 0, '', @UserId, GETDATE());
			SET @InsertedProsOfferId = SCOPE_IDENTITY();

			--insert lines
			INSERT INTO dbo.tbProspectiveOfferLines
			(
				ProspectiveOfferNo,
				ManufacturerNo,
				Part,
				FullPart,
				AlternativePart,
				Quantity,
				Price,
				[Description],
				DateCode,
				ProductNo,
				PackageId,
				ROHS,
				CurrencyNo,
				SupplierPart,
				CreatedBy,
				DLUP,
				Notes
			)
			SELECT 
				@InsertedProsOfferId,
				ManufacturerNo,
				Part,
				dbo.ufn_get_fullpart(Part) AS FullPart,
				AlternativePart,
				Quantity,
				Price,
				[Description],
				DateCode,
				ProductNo,
				PackageNo,
				ROHS,
				CurrencyNo,
				SupplierPart,
				@UserId,
				GETDATE(),
				Notes
			FROM BorisGlobalTraderImports.dbo.tbProspectiveOffer_ToBeImported
			WHERE SupplierNo = @SupplierId AND CreatedBy = @UserId

			SET @RecordCount = @@ROWCOUNT;
			SET @OutputMessage = 'Import Success';
			UPDATE dbo.tbProspectiveOffers 
			SET ImportRowCount = @RecordCount,
				ImportStatus = @OutputMessage
			WHERE ProspectiveOfferId = @InsertedProsOfferId;

			--insert log
			INSERT INTO BorisGlobalTraderImports.dbo.tbUtilityLog 
			(
				FileName,
				UtilityType,
				Clientid,
				LoginNo,
				DLUP,
				iRowCount
			)       
			VALUES (@OriginalFilename, 10, 114, @UserId, GETDATE(), @RecordCount)  

			--insert import activity
			INSERT INTO BorisGlobalTraderImports.dbo.tbImportActivity 
			(
				SupplierName, 
				ImportDate, 
				ImportName, 
				RowsAffected, 
				Target
			)                
			SELECT LEFT(CompanyName, 50), GETDATE(), 'tbProspectiveOffers', @RecordCount, @OutputMessage                
			FROM dbo.tbCompany                
			WHERE CompanyId = @SupplierId

			--clear imported data
			DELETE BorisGlobalTraderImports.dbo.tbProspectiveOffer_ToBeImported
			WHERE SupplierNo = @SupplierId AND CreatedBy = @UserId
		END
		COMMIT TRANSACTION
	END TRY
	BEGIN CATCH
		SET @RecordCount = 0
		SELECT @OutputMessage = ERROR_MESSAGE();
		ROLLBACK TRANSACTION
	END CATCH
END
/*
	DECLARE @RecordCount INT,                                                  
			@OutputMessage VARCHAR(2000);
	EXEC usp_insert_ProspectiveOfferImport
		@UserId = 6670,
		@SupplierId = 234399,
		@RecordCount = @RecordCount OUTPUT,
		@OutputMessage = @OutputMessage OUTPUT
	SELECT @RecordCount, @OutputMessage

*/
