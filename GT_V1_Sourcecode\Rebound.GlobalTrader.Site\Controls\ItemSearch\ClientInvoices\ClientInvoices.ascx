<%@ Control Language="C#" CodeBehind="ClientInvoices.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.ItemSearch.ClientInvoices" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_ItemSearch:DesignBase id="ctlDB" runat="server" InitialSortDirection="DESC">
	<FieldsLeft>
		<ReboundUI_FilterDataItemRow:Numerical id="ctlClientInvoiceNo" runat="server" ResourceTitle="ClientInvoiceNo" TextBoxMaxLength="10" />
		<%--<ReboundUI_FilterDataItemRow:TextBox id="ctlCompany" runat="server" ResourceTitle="Company"  />
		<ReboundUI_FilterDataItemRow:TextBox id="ctlContact" runat="server" ResourceTitle="Contact" />
		<ReboundUI_FilterDataItemRow:CheckBox id="ctlIncludePaid" runat="server" ResourceTitle="IncludePaid" />
		<ReboundUI_FilterDataItemRow:DropDown id="ctlSalesman" runat="server" ResourceTitle="Salesman" DropDownType="Employee" DropDownAssembly="Rebound.GlobalTrader.Site" />--%>
	<ReboundUI_FilterDataItemRow:Numerical id="ctlGoodsIn" runat="server" ResourceTitle="GoodsIn" />
		<%--[001]Code Start--%>
				<ReboundUI_FilterDataItemRow:DropDown id="ctlClientName" runat="server" ResourceTitle="ClientName" DropDownType="Client" DropDownAssembly="Rebound.GlobalTrader.Site" FilterField="Client"  />				
		<%--[001]Code End--%>	</FieldsLeft>
	<FieldsRight>
		<ReboundUI_FilterDataItemRow:Numerical id="ctlClientDebitNo" runat="server" ResourceTitle="ClientDebitNo" />
		<ReboundUI_FilterDataItemRow:DateSelect id="ctlDateInvoicedFrom" runat="server" ResourceTitle="DateInvoicedFrom" />
		<ReboundUI_FilterDataItemRow:DateSelect id="ctlDateInvoicedTo" runat="server" ResourceTitle="DateInvoicedTo" />
	</FieldsRight>
</ReboundUI_ItemSearch:DesignBase>
