<%@ Control Language="C#" CodeBehind="CustomerRequirements.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.DataListNuggets.CustomerRequirements" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>

<link href="https://fonts.googleapis.com/css?family=Nunito:400,600,700" rel="stylesheet">
<link href="../../../css/Kub/RequirementSearchPage/main.css" rel="stylesheet" type="text/css" />
<link href="../../../css/Kub/RequirementSearchPage/select2.min.css" rel="stylesheet" type="text/css" />
<link href="../../../css/Kub/RequirementSearchPage/chatBot.css" rel="stylesheet" type="text/css" />

<style>


    #TableSearchListID tr td a{
    background-repeat: no-repeat;
    background-position: left 2px;
    margin-left: 20px;
    padding-left: 13px;
    height: 12px;
    text-decoration: none;
    color: #5E6EFF;}

    .table_head thead th:nth-child(8), .table_head tbody tr td:nth-child(8) {
        border-right: none !important;
    }

    .text_down {
        font-size: 10px;
    }

    table > tbody > tr.selected {
        background-color: #000066;
        color: #fff;
    }

        table > tbody > tr.selected a {
            color: #66ccFF !important;
            background-image: url(../../../App_Themes/Original/images/FlexiDataTable/nub_tb_selected.gif);
        }

    #setectedid {
        background-color: #000066;
        color: #fff;
    }



        #setectedid a {
            color: #66ccFF !important;
            background-image: url(../../../App_Themes/Original/images/FlexiDataTable/nub_tb_selected.gif);
        }

        #setectedid td {
            border-right: 1px solid #a9a9a9;
            padding-left: 2px;
            font-size: 10px;
        }

        #setectedid .doubleValueTop {
            font-size: 10px !important;
        }


    .close_icon {
        background-image: url(../../../images/kub/RequirementSearchPage/close_icon.svg);
        background-repeat: no-repeat;
        width: 16px;
        height: 16px;
        float: right;
        margin: 0px;
        cursor: pointer;
    }

    .table_content table {
        width: 100%;
        color: #fff;
    }

        .table_content table thead th {
            background-color: #47863F;
            padding: 6px 0px;
            text-align: center;
        }

    .table_content tbody tr:nth-child(even) {
        background: #EEFFEC
    }

    .table_content tbody tr:nth-child(odd) {
        background: #FFF
    }

    .table_content table tbody {
        color: #2D2D2D;
    }

        .table_content table tbody tr td {
            padding: 6px 10px;
            font-size: 10px;
            font-weight: bold;
            font-family: Tahoma
        }

            .table_content table tbody tr td input {
                border: none;
                background-color: #488740;
                color: #fff;
                padding: 5px 10px;
                font-weight: bold;
                font-family: Tahoma;
                border-radius: 3px;
                letter-spacing: 1px;
            }


    .accordion {
        background: none !important;
        color: #444;
        cursor: pointer;
        width: 96%;
        border: none;
        text-align: left;
        outline: none;
        font-size: 10px;
        transition: 0.4s;
        font-weight: bold;
        font-family: Tahoma;
        padding: 0px !important;
        margin-top: 3px;
    }
        .accordion:after {
            content: ' View Details ';
            color: #5E6EFF;
            font-weight: bold;
            float: left;
            margin-left: 5px;
            background-image: url(../../images/KubSearchPages/icon_down.svg);
            background-position: right center;
            padding-right: 18px;
            background-repeat: no-repeat;
        }

    .active:after {
        content: " View Details";
        background-image: url(../../../images/kub/RequirementSearchPage/icon_up.svg);
        background-position: right center;
        background-repeat: no-repeat;
    }

    .panel {
        padding: 0 10px;
        background-color: #EEEEEE;
        max-height: 0;
        overflow: hidden;
        transition: max-height 0.2s ease-out;
        margin-left: -9px;
        width: 102.5%;
    }

    .text_left {
        width: 60%;
        float: left;
    }

    .text_right {
        width: 30%;
        float: right;
    }

    .data_tb {
        padding: 5px 5px;
        margin: 10px 0px;
    }

        .data_tb thead tr th {
            text-align: left;
            padding: 6px 10px;
        }

        .data_tb thead tr th, .data_tb tbody tr td {
            border-right: 1px dashed #BFBFBF;
        }

            .data_tb thead tr th:nth-child(3), .data_tb tbody tr td:nth-child(3) {
                border-right: none;
            }



    .table_header thead tr th {
        padding: 7px;
        color: #171717;
        font-size: 10px;
        background-color: #EEEEEE;
        width: 12.5%;
        font-weight: bold;
        font-family: Tahoma;
        word-break: break-word;
        letter-spacing: normal !important;
        border-right: 1px dashed #959595;
    }

    .table_header tbody tr td {
        padding: 5px;
        color: #fff;
        font-size: 10px;
        background-color: #000066;
        width: 12.5%;
        word-break: break-word;
        letter-spacing: normal !important;
        border-right: 1px dashed #bfbfbf;
    }

    .chat-screen .chat-body {
        padding: 0px !important;
        /*min-height: 382px;*/
        margin: 0px;
        background-color: #fff !important
    }

    .table_data {
     width: 97.6%;
    padding: 10px;
    letter-spacing: normal !important;
    font-family: Tahoma;
    float: left;
    max-height: 350px;
    overflow-y: auto;
}
    

    .aver_text {
        float: left;
        width: 100%;
        color: #1E1E1E;
        font-size: 10px;
    }

        .aver_text span {
            font-weight: bold;
        }

    .latest_data {
        width: 80%;
        float: left;
        font-size: 10px;
        font-family: Tahoma;
        padding: 8px;
        background-color: #F9F9F9;
    }

    .latest_content thead tr th {
        background-color: #47863F;
        color: #fff;
        border-right: 1px solid #4AB83C;
    }

    .latest_content thead tr th, .latest_content tbody tr td {
        padding: 5px;
    }

    .latest_content tr td {
        color: #393939;
        font-size: 11px;
    }


    .latest_content {
        width: 100%;
        border-collapse: collapse;
    }

        .latest_content tbody tr:nth-child(even) {
            background: #EEFFEC
        }

        .latest_content tbody tr:nth-child(odd) {
            background: #FFF
        }

    .trade_group {
        width: 100%;
        float: left;
        font-size: 10px;
    }

        .trade_group ul {
            padding: 0px;
            list-style-type: none;
            color: #1E1E1E;
            line-height: 15px;
        }

            .trade_group ul li:before {
                content: "";
                background-image: url(../../../images/kub/RequirementSearchPage/list_style.svg);
                background-size: contain;
                display: inline-block;
                width: 1em;
                height: 0.6em;
                position: relative;
                top: -1px;
                margin-right: 0.4rem;
                background-repeat: no-repeat;
            }




    .total_value {
        width: 98%;
        padding: 4px 0px;
        border-top: 1px dashed #bfbfbf;
        border-bottom: 1px dashed #bfbfbf;
        float: left;
        margin-bottom: 10px;
    }

    .total_valuetext {
        width: 50%;
        float: left;
        color: #1E1E1E;
        font-weight: bold;
        font-size: 10px;
        padding-right: 10px;
        border-right: 1px dashed #bfbfbf;
    }

    .value_number {
        float: left;
        color: #1E1E1E;
        font-weight: bold;
        font-size: 10px;
        padding-left: 10px;
    }

    .quotes_details td a {
        background-image: url(../../../images/kub/RequirementSearchPage/nub.gif);
        background-position: left center;
        background-repeat: no-repeat;
        padding-left: 15px;
        color: #0000FF;
    }
            #moreData {
            display: none;
        }

        .read_btn {
            border: none;
            border-radius: 4px;
            color: #fff;
            background-color: #006600;
            margin-top:8px;
            font-family:Tahoma;
            font-size:11px;
            padding:4px 8px 4px 4px;
            cursor:pointer;
        }
    .LastUpdate {
    color: #999999;
    font-size: 10px;
    font-style: italic;
    text-align: right;
    justify-content: flex-end;
    display: flex;
    width: 99%;
}
</style>

<!-- BEGIN GLOBAL MANDATORY SCRIPTS -->
<script src="../../../js/Kub/RequirementSearchPage/jquery-3.1.1.min.js"></script>
<script src="../../../js/Kub/RequirementSearchPage/popper.min.js"></script>
<script src="../../../js/Kub/RequirementSearchPage/bootstrap.min.js"></script>
<script src="../../../js/Kub/RequirementSearchPage/select2.min.js"></script>
<script src="../../../js/Kub/RequirementSearchPage/KUBRowSelect.js"></script>
<script src="../../../js/Kub/ConfigureKub.js"></script>
<script>
    var acc = document.getElementsByClassName("accordion");
    var i;
    for (i = 0; i < acc.length; i++) {
        acc[i].addEventListener("click", function () {
            this.classList.toggle("active");
            var panel = this.nextElementSibling;
            if (panel.style.maxHeight) {
                panel.style.maxHeight = null;
            } else {
                panel.style.maxHeight = "250px";
            }
        });
    }
</script>


<%-- Changes --%>
<%--Changecode  Date            Ticket no   DevnName             DESCRIPTION 

    [001]                       RP-37       Tanbir akhtar        Filter Added for industry type and Also added industry type on grid of UI
    [002]      19-09-2023       RP-2339     Ravi                 AS6081 Search/Filter functionality on different pages 

--%>
<%-- code start[001] --%>
<style>
	#ctl00_cphMain_ctlCustomerRequirements_ctlDB_ctl15_ctlFilter_ctlStatus_ddl_ddl{
		width: 200px;
	}

</style>
<%-- code end --%>

<ReboundUI_Nugget:DesignBase ID="ctlDB" runat="server" BoxType="List">
	<Filters>
		<ReboundUI_DataListNugget:Filter ID="ctlFilter" runat="server">
			<FieldsLeft>
				<ReboundUI_FilterDataItemRow:Numerical id="ctlCReqNo" runat="server" ResourceTitle="CusReqNo" FilterField="CReqNo" />
                <%-- Code start [001] --%>
				<ReboundUI_FilterDataItemRow:DropDown id="ctlStatus"  runat="server" DropDownType="IndustryType" ResourceTitle="IndustryType" DropDownAssembly="Rebound.GlobalTrader.Site" FilterField="IndustryType" />   
				<%-- Code End [001] --%>
				<ReboundUI_FilterDataItemRow:TextBox id="ctlPart" runat="server" ResourceTitle="PartNo" FilterField="Part" />
		        <ReboundUI_FilterDataItemRow:CheckBox id="ctlIncludeClosed" runat="server" ResourceTitle="IncludeClosed" FilterField="IncludeClosed" />
		        <ReboundUI_FilterDataItemRow:CheckBox id="ctlRecentOnly" runat="server" ResourceTitle="RecentOnly" FilterField="RecentOnly" DefaultValue="true" />
				<ReboundUI_FilterDataItemRow:TextBox id="ctlCompanyName" runat="server" ResourceTitle="CompanyName" FilterField="CMName"  />
				<ReboundUI_FilterDataItemRow:TextBox id="ctlContactName" runat="server" ResourceTitle="ContactName" FilterField="Contact"  />
		        <ReboundUI_FilterDataItemRow:TextBox id="ctlBOMName" runat="server" ResourceTitle="BOMName" FilterField="BOMName" />
                <ReboundUI_FilterDataItemRow:Numerical id="ctlTotalValue" runat="server" ResourceTitle="TotalValue" FilterField="Total" CanShowDecimal="true" />
                
                
			</FieldsLeft>
			<FieldsRight>
				<ReboundUI_FilterDataItemRow:DropDown id="ctlSalesman" runat="server" ResourceTitle="Salesman" DropDownType="Employee" DropDownAssembly="Rebound.GlobalTrader.Site" FilterField="Salesman" />
				<ReboundUI_FilterDataItemRow:DateSelect id="ctlDatePromisedFrom" runat="server" ResourceTitle="DatePromisedFrom" FilterField="DatePromisedFrom" />
				<ReboundUI_FilterDataItemRow:DateSelect id="ctlDatePromisedTo" runat="server" ResourceTitle="DatePromisedTo" FilterField="DatePromisedTo" />
				<ReboundUI_FilterDataItemRow:DateSelect id="ctlReceivedDateFrom" runat="server" ResourceTitle="ReceivedDateFrom" FilterField="ReceivedDateFrom" />
				<ReboundUI_FilterDataItemRow:DateSelect id="ctlReceivedDateTo" runat="server" ResourceTitle="ReceivedDateTo" FilterField="ReceivedDateTo" />
		        <ReboundUI_FilterDataItemRow:CheckBox id="ctlPartWatch" runat="server" ResourceTitle="PartWatch" FilterField="PartWatch" />
		        <ReboundUI_FilterDataItemRow:TextBox id="ctlBOMHeader" runat="server" ResourceTitle="IPOBOM" FilterField="BOMCode" />
                <ReboundUI_FilterDataItemRow:DropDown id="ctlStatusREQs" runat="server" DropDownType="REQStatus" ResourceTitle="REQStatus" DropDownAssembly="Rebound.GlobalTrader.Site" FilterField="StatusREQ" />                
			    <ReboundUI_FilterDataItemRow:DropDown id="ctlAS6081" runat="server" ResourceTitle="AS6081Filter" DropDownType="CounterfeitElectronicParts" DropDownAssembly="Rebound.GlobalTrader.Site" FilterField="AS6081" /> <%--[002]--%>
                <ReboundUI_FilterDataItemRow:DropDown id="ctlClientName" runat="server" ResourceTitle="ClientName" DropDownType="Client" DropDownAssembly="Rebound.GlobalTrader.Site" FilterField="Client"  />
            </FieldsRight>
		</ReboundUI_DataListNugget:Filter>
	</Filters>
</ReboundUI_Nugget:DesignBase>

<!-- Chat bot UI start -->
<div class="chat-screen" id="close_modal">
    <div class="chat-header">
        <div class="chat-header-title">
            <span class="head_text">KUB Assistant</span>
        </div>
        <div class="close_icon" id="hide"></div>
    </div>
    <div class="chat-body">
        <div class="table_header">
            <table id="table_header1090" class="table_head" style="border-collapse: collapse;">
                <thead>
                    <tr>
                        <th>No.</th>
                        <th>Part No.
                                    <div class="text_down">Manufacturer</div>

                        </th>
                        <th>Quantity</th>
                        <th>Company Name
                                    <div class="text_down">Company Contact</div>
                        </th>
                        <th>Salesperson</th>
                        <th>Received Date
                                    <div class="text_down">Promised Date</div>
                        </th>
                        <th>HUBRFQ
                                    <div class="text_down">HUB Status</div>
                        </th>
                        <th>Total Value

                                    <div class="text_down">Total Base Value</div>
                        </th>

                    </tr>

                </thead>
            </table>

        </div>
        <div class="table_data">
             <div class="total_value">
                 <div class="total_valuetext">Average price of part last 12 months for this client</div>
                 <div class="value_number"><span style="font-weight:normal;" id="averagePriceid"></span></div>

             </div>

        <div class="total_value">
                    <div class="total_valuetext">Total number of lines invoiced with selected customer in 12 months (Rolling)</div>
                    <div class="value_number" id="TotalNoOfLinesInvoicedIn12Monthsid"></div>
         </div>    
            <span id="dotsData"> </span>
            <div class="lates_quote" id="moreData">
                <div class="aver_text" style="margin-top: 10px; font-weight: bold;">Last 10 quotes for part no: <span style="background-color: yellow;"  id="part1"></span> for <span style="background-color: yellow;" id="companyName"></span> </div>
                 <div class="latest_data">
                    <table class="latest_content">
                        <thead>
                            <tr>
                                <th>Quote</th>
                                <th>Quote Date</th>
                                <th>Quantity</th>
                                <th>Unit price</th>
                                <th>Buy price</th>
                                <th>Total profit</th>
                            </tr>
                        </thead>
                        <tbody id="TableSearchListID" class="data-Breakdown-GI"></tbody>
                     </table>
                </div>
                <div class="trade_group">
                    <div class="aver_text" style="margin-top: 10px; font-weight: bold;">Main product groups traded in 12 months based on Part Number - <span style="margin-top: 10px; font-size:8.3px;" id="part2"></span></div>
                    <div > 
                        <ul class="aver_text" id="listview"></ul>
                    </div>
                </div>
                
            </div>

             <div class="clearing" style="clear:both;"></div>
                        <button onclick="return false" id="BtnReadMore" class="read_btn">+ Read more</button>
        </div>
    </div>
    <span class="LastUpdate" id="spnLastUpdated"></span>
</div>
<div class="chat-bot-icon" id="show" style="display: none">
    <div class="container">
        <div id="chatbot">
            <div class="dot"></div>
            <div class="dot"></div>
            <div class="dot"></div>
        </div>
        <div id="antenna">
            <div id="beam-pulsar"></div>
        </div>
    </div>
</div>
<div class="chat-bot-icon1" id="fixicon" style="display:none;">
    <div class="container">
        <div>
            <div class="dot1"></div>
            <div class="dot1"></div>
            <div class="dot1"></div>
        </div>
    </div>
</div>
<!-- Chat Bot UI Ends -->


