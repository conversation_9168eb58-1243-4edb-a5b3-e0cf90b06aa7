///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.HomeNuggets");

Rebound.GlobalTrader.Site.Controls.HomeNuggets.ReceivedOrders = function(element) { 
	Rebound.GlobalTrader.Site.Controls.HomeNuggets.ReceivedOrders.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.HomeNuggets.ReceivedOrders.prototype = {
	get_tblReceived: function() { return this._tblReceived; }, 	set_tblReceived: function(value) { if (this._tblReceived !== value)  this._tblReceived = value; }, 

	initialize: function() 	{
		Rebound.GlobalTrader.Site.Controls.HomeNuggets.ReceivedOrders.callBaseMethod(this, "initialize");
		this.addRefreshEvent(Function.createDelegate(this, this.getData));
	},

	dispose: function() { 
		if (this.isDisposed) return;
		if (this._tblReceived) this._tblReceived.dispose();
		this._tblReceived = null;
		Rebound.GlobalTrader.Site.Controls.HomeNuggets.ReceivedOrders.callBaseMethod(this, "dispose");
	},
	
	setupLoadingState: function() {
		this._tblReceived.show(false);
		Rebound.GlobalTrader.Site.Controls.HomeNuggets.ReceivedOrders.callBaseMethod(this, "setupLoadingState");
	},
	
	getData: function() {
		this.setupLoadingState();
		var obj = new Rebound.GlobalTrader.Site.Data();
		obj.set_PathToData("controls/HomeNuggets/ReceivedOrders");
		obj.set_DataObject("ReceivedOrders");
		obj.set_DataAction("GetData");
		obj.addParameter("rowcount", this._intRowCount);
		obj.addDataOK(Function.createDelegate(this, this.getDataComplete));
		obj.addError(Function.createDelegate(this, this.homeNuggetDataError));
		obj.addTimeout(Function.createDelegate(this, this.homeNuggetDataError));
		$R_DQ.addToQueue(obj);
		$R_DQ.processQueue();
		obj = null;
	},
	
	getDataComplete: function(args) {
		this.showNoneFoundOrContent(args._result.Count);
		var result = args._result;
		//received
		this._tblReceived.clearTable();
		for (var i = 0; i < result.Received.length; i++) {
			var row = result.Received[i];
			var aryData = [
				$RGT_nubButton_PurchaseOrder(row.ID, row.No),
				$RGT_nubButton_Company(row.CMNo, row.CM),
				row.Received
				];
			this._tblReceived.addRow(aryData, null);
		}
		this._tblReceived.show(result.Received.length > 0);
		this.hideLoading();
	}
};

Rebound.GlobalTrader.Site.Controls.HomeNuggets.ReceivedOrders.registerClass("Rebound.GlobalTrader.Site.Controls.HomeNuggets.ReceivedOrders", Rebound.GlobalTrader.Site.Controls.HomeNuggets.Base, Sys.IDisposable);
