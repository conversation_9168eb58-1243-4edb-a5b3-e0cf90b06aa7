Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.GILines_LotNo=function(n){Rebound.GlobalTrader.Site.Controls.Forms.GILines_LotNo.initializeBase(this,[n]);this._intLineID=-1;this._intGIID=-1;this._intLotID=-1;this._lotDetail=-1;this._status=null;this._intInvoiceLineNo=-1;this._invoiceExist=!1;this._Quantity=-1;this._countLotNo=-1;this._isDeleted=!1;this._blnSerNoRecorded=!1};Rebound.GlobalTrader.Site.Controls.Forms.GILines_LotNo.prototype={get_intLotID:function(){return this._intLotID},set_intLotID:function(n){this._intLotID!==n&&(this._intLotID=n)},get_intGIID:function(){return this._intGIID},set_intGIID:function(n){this._intGIID!==n&&(this._intGIID=n)},get_intLineID:function(){return this._intLineID},set_intLineID:function(n){this._intLineID!==n&&(this._intLineID=n)},get_btnAdd:function(){return this._btnAdd},set_btnAdd:function(n){this._btnAdd!==n&&(this._btnAdd=n)},get_btnRefresh:function(){return this._btnRefresh},set_btnRefresh:function(n){this._btnRefresh!==n&&(this._btnRefresh=n)},get_btnUpdate:function(){return this._btnUpdate},set_btnUpdate:function(n){this._btnUpdate!==n&&(this._btnUpdate=n)},get_lblDuplicateError:function(){return this._lblDuplicateError},set_lblDuplicateError:function(n){this._lblDuplicateError!==n&&(this._lblDuplicateError=n)},get_lblLotCount:function(){return this._lblLotCount},set_lblLotCount:function(n){this._lblLotCount!==n&&(this._lblLotCount=n)},get_intInvoiceLineNo:function(){return this._intInvoiceLineNo},set_intInvoiceLineNo:function(n){this._intInvoiceLineNo!==n&&(this._intInvoiceLineNo=n)},get_status:function(){return this._status},set_status:function(n){this._status!==n&&(this._status=n)},get_invoiceExist:function(){return this._invoiceExist},set_invoiceExist:function(n){this._invoiceExist!==n&&(this._invoiceExist=n)},get_isDeleted:function(){return this._isDeleted},set_isDeleted:function(n){this._isDeleted!==n&&(this._isDeleted=n)},get_btnRefGrid:function(){return this._btnRefGrid},set_btnRefGrid:function(n){this._btnRefGrid!==n&&(this._btnRefGrid=n)},get_ctlGiTempLotNo:function(){return this._ctlGiTempLotNo},set_ctlGiTempLotNo:function(n){this._ctlGiTempLotNo!==n&&(this._ctlGiTempLotNo=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Forms.GILines_LotNo.callBaseMethod(this,"initialize");this.addShown(Function.createDelegate(this,this.formShown));this.addSave(Function.createDelegate(this,this.saveLotClicked));$R_TXTBOX.addEnterPressedEvent(document.getElementById("ctl00_cphMain_ctlLines_ctlDB_ctl14_frmLotNo_ctlDB_ctlLotNo_ctl04_txtSerailNo"),Function.createDelegate(this,this.saveClicked));this._ctlGiTempLotNo.addItemSelected(Function.createDelegate(this,this.getLotDetail));$R_FN.showElement(this._btnUpdate,!1);$R_FN.showElement(this._btnAdd,!0);$R_FN.showElement(this._btnRefresh,!0);$R_FN.showElement(this._lblDuplicateError,!1);this.showField("ctlLotNoDetail",!1);this.setFieldValue("ctlLotNo","");this.setFieldValue("ctlQuantity",this._Quantity);this._btnAdd&&$addHandler(this._btnAdd,"click",Function.createDelegate(this,this.saveClicked));this._btnUpdate&&$addHandler(this._btnUpdate,"click",Function.createDelegate(this,this.updateClicked));this._btnRefresh&&$addHandler(this._btnRefresh,"click",Function.createDelegate(this,this.refreshClicked));this._btnRefGrid&&$addHandler(this._btnRefGrid,"click",Function.createDelegate(this,this.refreshGrid));this._ctlGiTempLotNo.setFieldValue("ctlLotNo","t");this._ctlGiTempLotNo._elementId=this._element.id;document.getElementsByClassName("dataFilter")[0].style.display="none";document.getElementsByClassName("itemSearchGo")[0]&&(document.getElementsByClassName("itemSearchGo")[0].style.display="none");this._ctlGiTempLotNo.addPotentialStatusChange(Function.createDelegate(this,this.ctlGiLotNumber_PotentialStatusChange));document.getElementById("ctl00_cphMain_ctlLines_ctlDB_ctl14_frmLotNo_ctlDB_ctlAddUpdate_ctl02_ctlGiTempLotNo_ctlDB_tblOuter").style.display="none";document.getElementById("ctl00_cphMain_ctlLines_ctlDB_ctl14_frmLotNo_ctlDB_ctlAddUpdate_ctl02_ctlGiTempLotNo_ctlDB_ctl03_hyp").style.display="none"},formShown:function(){this.setFieldValue("ctlQuantity",this._Quantity);this.showField("ctlLotNoDetail",!1);this.setFieldValue("ctlLotNo","");$R_FN.showElement(this._lblDuplicateError,!1);$R_FN.showElement(this._lblLotCount,!1);$R_FN.showElement(this._btnUpdate,!1);$R_FN.showElement(this._btnAdd,!0);$R_FN.showElement(this._btnRefresh,!1);$R_IBTN.showButton(document.getElementById("ctl00_cphMain_ctlLines_ctlDB_ctl31_ibtnSave_hyp"),!this._blnSerNoRecorded);$R_IBTN.showButton(document.getElementById("ctl00_cphMain_ctlLines_ctlDB_ctl32_ibtnSave_hyp"),!this._blnSerNoRecorded);this.refreshGrid()},dispose:function(){this.isDisposed||(this._ctlGiTempLotNo&&this._ctlGiTempLotNo.dispose(),this._intLotID=-1,this._btnAdd=null,this._btnRefresh=null,this._btnUpdate=null,this._intGIID=-1,this._lblDuplicateError=null,this._lblLotCount=null,this._intLineID=null,this._lotDetail=-1,this._status=null,this._invoiceExist=!1,this._intInvoiceLineNo=-1,this._isDeleted=!1,this._Quantity=null,this._countLotNo=null,this._blnSerNoRecorded=null,this._btnRefGrid=null,this._ctlGiTempLotNo=null,Rebound.GlobalTrader.Site.Controls.Forms.GILines_LotNo.callBaseMethod(this,"dispose"))},saveLotClicked:function(){if(this._lotDetail<=0){this._strErrorMessage="No Record Exist";this.showError(!0,this._strErrorMessage);return}var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/GILines");n.set_DataObject("GILines");n.set_DataAction("AddAllLotNo");n.addParameter("GoodsInId",this._intGIID);n.addParameter("GoodsInLineId",this._intLineID);n.addDataOK(Function.createDelegate(this,this.saveLotComplete));n.addError(Function.createDelegate(this,this.saveLotError));n.addTimeout(Function.createDelegate(this,this.saveLotError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},saveLotError:function(n){this._strErrorMessage=n._errorMessage;this.onSaveError()},saveLotComplete:function(n){if(n._result.Result==!0&&(this.setFieldValue("ctlLotNo",""),this.onSaveComplete()),n._result.Result==!1&&n._result.ValidateMessage!=null){this._strErrorMessage=n._result.ValidateMessage;this.showError(!0,this._strErrorMessage);$R_FN.showElement(this._lblDuplicateError,!1);return}},cancelClicked:function(){alert("sss")},saveClicked:function(){if(this.validateForm())if(this._countLotNo<this._Quantity){var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/GILines");n.set_DataObject("GILines");n.set_DataAction("AddLotNo");n.addParameter("SubGroup",this.getFieldValue("ctlSubGroup"));n.addParameter("LotNo",this.getFieldValue("ctlLotNo"));n.addParameter("GoodsInId",this._intGIID);n.addParameter("GoodsInLineId",this._intLineID);n.addDataOK(Function.createDelegate(this,this.saveComplete));n.addError(Function.createDelegate(this,this.saveError));n.addTimeout(Function.createDelegate(this,this.saveError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null}else{this.showError(!0,$R_RES.LotNoLimit);return}},saveError:function(n){this._strErrorMessage=n._errorMessage;this.onSaveError()},saveComplete:function(n){if(n._result&&(this._countLotNo=n._result.TCount),n._result.Result==!0){$R_FN.setInnerHTML(this._lblLotCount,"Lot No saved successfully. Kindly referesh to load data");$R_FN.showElement(this._lblLotCount,!0);this.setFieldValue("ctlLotNo","");this._strErrorMessage="";this.showError(!1,this._strErrorMessage);$R_FN.showElement(this._lblDuplicateError,!1);this.refreshGrid();return}if(n._result.Result==!1&&n._result.ValidateMessage!=null){this._strErrorMessage=n._result.ValidateMessage;this.showError(!0,this._strErrorMessage);$R_FN.showElement(this._lblDuplicateError,!1);$R_FN.setInnerHTML(this._lblLotCount,"");$R_FN.showElement(this._lblLotCount,!1);return}if(n._result.Result==!1&&n._result.ValidateMessage!=null){$R_FN.showElement(this._lblDuplicateError,!0);$R_FN.setInnerHTML(this._lblLotCount,"");$R_FN.showElement(this._lblLotCount,!1);return}},updateClicked:function(){if(this.validateForm()){var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/GILines");n.set_DataObject("GILines");n.set_DataAction("UpdateLotNo");n.addParameter("LotID",this._intLotID);n.addParameter("SubGroup",this.getFieldValue("ctlSubGroup"));n.addParameter("LotNo",this.getFieldValue("ctlLotNo"));n.addParameter("GoodsInId",this._intGIID);n.addParameter("Status",this._status);n.addParameter("GoodsInLineId",this._intLineID);n.addDataOK(Function.createDelegate(this,this.updateComplete));n.addError(Function.createDelegate(this,this.updateError));n.addTimeout(Function.createDelegate(this,this.updateError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null}},updateError:function(n){this._strErrorMessage=n._errorMessage;this.onSaveError()},updateComplete:function(n){if(n._result.Result==!0){this.showField("ctlLotNoDetail",!0);this.setFieldValue("ctlLotNo","");$R_FN.showElement(this._btnUpdate,!1);$R_FN.showElement(this._btnAdd,!0);$R_FN.showElement(this._lblDuplicateError,!1);$R_FN.showElement(this._btnRefresh,!1);this.refreshGrid();return}if(n._result.Result==!1&&n._result.ValidateMessage!=null){$R_FN.showElement(this._lblDuplicateError,!0);return}this._strErrorMessage=n._errorMessage;this.onSaveError()},LoadSerailNoGrid:function(){var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/GILines");n.set_DataObject("GILines");n.set_DataAction("GetDataGrid");n.addParameter("GoodsInId",this._intGIID);n.addParameter("GoodsInLineId",this._intLineID);n.addDataOK(Function.createDelegate(this,this.getDataGrid));n.addError(Function.createDelegate(this,this.getDataGridError));n.addTimeout(Function.createDelegate(this,this.getDataGridError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getDataGridError:function(){},getDataGrid:function(n){var i,t,r,u,f;for(this._invoiceExist=!1,this._countLotNo=0,res=n._result,$R_FN.setInnerHTML(this._lblLotCount,"Lot No. Count: "+res.Count+(this._blnSerNoRecorded?" All required lot numbers are recorded":"")),$R_FN.showElement(this._lblLotCount,res.Count>0),this._lotDetail=res.LotNoDetails.length,this.showField("ctlLotNoDetail",res.LotNoDetails.length>0),this._tblLotNodetails.clearTable(),this._countLotNo=res.Count,i=0;i<res.LotNoDetails.length;i++)t=res.LotNoDetails[i],this._invoiceExist=t.InvoiceLineNo>0?!0:!1,r="",this._invoiceExist=!0,r=this._invoiceExist==!1?[t.SubGroup,t.LotNo,t.GoodsInNo,String.format("<a href=\"javascript:void(0);\" Style='color:#006600' class='Delete'  onclick=\"$find('{0}').removeItem({1});\" class=\"quickSearchReselect\">Delete<\/a>",this._element.id,t.LotNoId)]:[t.SubGroup,t.LotNo,t.GoodsInNo,String.format('<a href="javascript:void(0);" Style=\'color: #006600\' Hidden=\'true\'  onclick="return false;" class="quickSearchReselect">Delete<\/a>',this._element.id,t.LotNoId)],u={LotNoId:t.LotNoId,SubGroup:t.SubGroup,LotNo:t.LotNo,GoodsInNo:t.GoodsInNo,Status:t.Status,InvoiceLineNo:t.InvoiceLineNo},f=t.Inactive?"ceased":"",this._tblLotNodetails.addRow(r,t.LotNoId,!1,u,f),r=null,t=null;this._tblLotNodetails.resizeColumns();this._blnSerNoRecorded=this._countLotNo>=this._Quantity;this._btnAdd&&$R_IBTN.showButton(this._btnAdd,!this._blnSerNoRecorded)},removeItem:function(n,t){var r=confirm("Are you sure you want to delete"),i;if(r==!0)this.getValuesByLot(),i=new Rebound.GlobalTrader.Site.Data,i.set_PathToData("controls/Nuggets/GILines"),i.set_DataObject("GILines"),i.set_DataAction("DeleteLotNo"),i.addParameter("LotNoId",n),i.addParameter("Status",t),i.addParameter("GoodsInId",this._intGIID),i.addParameter("GoodsInLineId",this._intLineID),i.addDataOK(Function.createDelegate(this,this.deleteComplete)),i.addError(Function.createDelegate(this,this.deleteError)),i.addTimeout(Function.createDelegate(this,this.deleteError)),$R_DQ.addToQueue(i),$R_DQ.processQueue(),i=null;else return this.showField("ctlLotNoDetail",!0),this.setFieldValue("ctlLotNo",""),$R_FN.showElement(this._btnUpdate,!1),$R_FN.showElement(this._btnAdd,!0),$R_FN.showElement(this._lblDuplicateError,!1),$R_FN.showElement(this._btnRefresh,!1),!1},deleteError:function(n){this._strErrorMessage=n._errorMessage;this.onSaveError()},deleteComplete:function(n){if(n._result.Result==!0){this.showField("ctlLotNoDetail",!0);this.setFieldValue("ctlLotNo","");$R_FN.showElement(this._btnUpdate,!1);$R_FN.showElement(this._btnAdd,!0);$R_FN.showElement(this._lblDuplicateError,!1);$R_FN.showElement(this._btnRefresh,!1);this.refreshGrid();return}this._strErrorMessage=n._errorMessage;this.onSaveError()},getLotDetail:function(){$R_FN.showElement(this._lblDuplicateError,!1);this._strErrorMessage="";this.showError(!1,this._strErrorMessage);this.setFieldValue("ctlLotNo","");this.getValuesByLot()},getValuesByLot:function(){this._intLotID=-1;var n=this._ctlGiTempLotNo._tblResults.getSelectedExtraData();n&&(n.InvoiceLineNo<=0&&(this.setFieldValue("ctlSubGroup",n.SubGroup),this.setFieldValue("ctlLotNo",n.LotNo),$R_FN.showElement(this._btnUpdate,!0),$R_FN.showElement(this._btnAdd,!1)),this._intLotID=n.LotNoId,this._status=n.Status,this._intInvoiceLineNo=n.InvoiceLineNo,$R_FN.showElement(this._btnRefresh,!0))},validateForm:function(){this.onValidate();return this.autoValidateFields()},refreshClicked:function(){this.setFieldValue("ctlLotNo","");$R_FN.showElement(this._btnUpdate,!1);$R_FN.showElement(this._btnAdd,!0);this._strErrorMessage="";this.showError(!1,this._strErrorMessage);$R_FN.showElement(this._lblDuplicateError,!1);$R_FN.showElement(this._btnRefresh,!1)},refreshGrid:function(){this._ctlGiTempLotNo._intGoodsInNo=this._intGIID;this._ctlGiTempLotNo._intGoodsInLineNo=this._intLineID;this._ctlGiTempLotNo.getData()},ctlGiLotNumber_PotentialStatusChange:function(){this._countLotNo=this._ctlGiTempLotNo._objResult.Count;this._blnSerNoRecorded=this._countLotNo>=this._Quantity;$R_FN.setInnerHTML(this._lblLotCount,"Lot No. Count: "+this._countLotNo+(this._blnSerNoRecorded?" All required lot numbers are recorded":""));$R_FN.showElement(this._lbllotCount,this._countLotNo>0);this._lotDetail=this._countLotNo;this.showField("ctlLotNoDetail",this._countLotNo>0);this._btnAdd&&$R_IBTN.showButton(this._btnAdd,!this._blnSerNoRecorded)}};Rebound.GlobalTrader.Site.Controls.Forms.GILines_LotNo.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.GILines_LotNo",Rebound.GlobalTrader.Site.Controls.Forms.Base,Sys.IDisposable);