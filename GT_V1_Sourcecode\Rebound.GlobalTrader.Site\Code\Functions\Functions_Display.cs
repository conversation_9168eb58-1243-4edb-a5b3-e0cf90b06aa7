using System;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Reflection;
using System.ComponentModel;
using System.Resources;
using System.Collections.Generic;
using System.Text.RegularExpressions;
using System.Text;
using System.IO;
using System.Web.UI.WebControls;
using System.Collections;
using System.Threading;
using System.Globalization;
using System.Diagnostics;


/// <summary>
/// Functions, static class
/// </summary>
namespace Rebound.GlobalTrader.Site {
	public static partial class Functions {

		/// <summary>
		/// Checks if a global resource object exists and returns it
		/// </summary>
		/// <param name="strFile">The resource file to look in</param>
		/// <param name="obj">Any object that has the key text - this is converted to string</param>
		/// <returns>Required string or empty string</returns>
		public static string GetGlobalResource(string strFile, string strKey) {
			string strReturn = string.Format("Res({0}:{1})", strFile, strKey);
			CultureInfo ci = new CultureInfo(SessionManager.Culture);
			if (strKey != null && strFile != null) {
				if (System.Web.HttpContext.GetGlobalResourceObject(strFile, strKey, ci) != null) strReturn = (string)System.Web.HttpContext.GetGlobalResourceObject(strFile, strKey, ci);
			}
			ci = null;
			return strReturn;
		}
		public static string GetGlobalResource(string strFile, Enum enm) {
			return GetGlobalResource(strFile, enm.ToString());
		}
		public static string GetGlobalResource(SitePage obj) {
			return GetGlobalResource("PageTitles", obj.Name);
		}
		public static string GetGlobalResource(SiteSection obj) {
			return GetGlobalResource("SectionTitles", obj.Name);
		}

		/// <summary>
		/// Returns the path to an image within the App_Themes structure
		/// </summary>
		/// <param name="strImage">Image path (not including 'images')</param>
		/// <param name="strTheme">Theme name</param>
		/// <returns></returns>
		public static string GetLocalThemeImage(string strImage, string strTheme, bool blnForJavascript) {
			return string.Format("{0}/App_Themes/{1}/images/{2}", (blnForJavascript) ? "" : "~", strTheme, strImage);
		}
		public static string GetLocalThemeImage(string strImage, string strTheme) {
			return GetLocalThemeImage(strImage, strTheme, false);
		}

		/// <summary>
		/// Sets a CSS style on a control to make it visible/invisible
		/// </summary>
		/// <param name="ctl"></param>
		/// <param name="blnVisible"></param>
		public static void SetCSSVisibility(System.Web.UI.WebControls.WebControl ctl, bool blnVisible) {
			ctl.CssClass = AddOrRemoveInvisibleClass(ctl.CssClass, !blnVisible);
		}
		public static void SetCSSVisibility(UserControl ctl, bool blnVisible) {
			ctl.Attributes["class"] = AddOrRemoveInvisibleClass(ctl.Attributes["class"], !blnVisible);
		}
		public static void SetCSSVisibility(System.Web.UI.HtmlControls.HtmlControl ctl, bool blnVisible) {
			ctl.Attributes["class"] = AddOrRemoveInvisibleClass(ctl.Attributes["class"], !blnVisible);
		}
		public static void SetCSSVisibility(Control ctl, bool blnVisible) {
			if (ctl is WebControl) SetCSSVisibility((WebControl)ctl, blnVisible);
			if (ctl is UserControl) SetCSSVisibility((UserControl)ctl, blnVisible);
			if (ctl is System.Web.UI.HtmlControls.HtmlControl) SetCSSVisibility((System.Web.UI.HtmlControls.HtmlControl)ctl, blnVisible);
		}

		private static string AddOrRemoveInvisibleClass(string strCss, bool blnAdd) {
			if (strCss == null) strCss = "";
			if (blnAdd) {
				bool blnHasInvisibleAlready = false;
				string[] aryCss = strCss.Split(' ');
				for (int i = 0; i < aryCss.Length; i++) {
					if (aryCss[i] == "invisible") {
						blnHasInvisibleAlready = true;
						break;
					}
				}
				if (!blnHasInvisibleAlready) strCss += " invisible";
			} else {
				strCss = strCss.Replace("invisible", "").Trim();
			}
			return strCss.Trim();
		}


		/// <summary>
		/// Renders a control to string output
		/// </summary>
		/// <param name="ctl"></param>
		public static string RenderControlToString(Control ctl) {
			StringBuilder sb = new StringBuilder();
			StringWriter sw = new StringWriter(sb);
			HtmlTextWriter hw = new HtmlTextWriter(sw);
			ctl.RenderControl(hw);
			try {
				return sb.ToString().Replace("\r\n", "");
			} finally {
				hw.Dispose(); hw.Close();
				sw.Dispose(); sw.Close();
			}
		}

		public static string GetYesOrNo(int intTrueOrFalse) {
			return Functions.GetGlobalResource("Misc", (intTrueOrFalse == 1) ? "Yes" : "No");
		}

		public static string GetYesOrNo(Boolean blnTrueOrFalse) {
			return Functions.GetGlobalResource("Misc", (blnTrueOrFalse) ? "Yes" : "No");
		}

		public static string WriteNubButtonString(string strHref, object objTitle) {
			return string.Format(@"<a href=""{0}"" class=""nubButton nubButtonInline"">{1}</a>", strHref.Replace("~/", ""), objTitle);
		}

		public static void AddBackgroundImageToHead(System.Web.UI.HtmlControls.HtmlHead hdHead, string strTheme) {
			string strImage = (String.IsNullOrEmpty(SessionManager.BackgroundImage)) ? "plants" : SessionManager.BackgroundImage;
			if (String.IsNullOrEmpty(strTheme)) strTheme = "Original";
			ControlBuilders.CreateLiteralInsideParent(hdHead, String.Format(@"<style type=""text/css"">body {{ background-image:url(App_Themes/{0}/images/backgrounds/{1}.jpg); }}</style>", strTheme, strImage));
		}

        public static string PurchaseHubLogo(bool isShow)
        {
            string sb = string.Empty;
            if (isShow == true && SessionManager.IsPOHub==true)
            {
                sb= string.Format(@"<span class=""margin-left10px float-right""><img src=""App_Themes/Original/images/GPOLogo/gear.png""><span>");
            }
            else
            {
                string strImage = "App_Themes/Original/images/GPOLogo/" + Convert.ToString(SessionManager.ClientID) + ".png";
                sb= string.Format(@"<span class=""margin-left10px float-right""><img src=""{0}""><span>", strImage);
            }
            return sb.ToString();
        }

        public static string ShowGlobalUserLogo()
        {
            string sb = string.Empty;
            if (SessionManager.IsPOHub == false && SessionManager.IsGlobalUser.Value==true)
            {
                return string.Format(@"<span class=""margin-left10px float-right""><img width=""20px"" height=""20px"" src=""App_Themes/Original/images/GPOLogo/globaluser.png""><span>");
            }
            return sb.ToString();
        }

        public static string AppendMSL(string strMSL)
        {
            if (!string.IsNullOrEmpty(strMSL))
                strMSL = Environment.NewLine + strMSL;
            else
                strMSL = "";
            return strMSL;
        }


	}
}