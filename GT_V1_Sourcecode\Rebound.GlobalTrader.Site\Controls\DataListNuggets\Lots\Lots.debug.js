///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//
// RP 23.12.2009:
// - render state on server
//
// RP 16.10.2009:
// - Changes due to changes in base class
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DataListNuggets");

Rebound.GlobalTrader.Site.Controls.DataListNuggets.Lots = function(element) { 
	Rebound.GlobalTrader.Site.Controls.DataListNuggets.Lots.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.DataListNuggets.Lots.prototype = {

    initialize: function() {
        this.addGetDataOKEvent(Function.createDelegate(this, this.getDataOK));
        this.addInitCompleteEvent(Function.createDelegate(this, this.initAfterBaseIsReady));
        this._strPathToData = "controls/DataListNuggets/Lots";
        this._strDataObject = "Lots";
        Rebound.GlobalTrader.Site.Controls.DataListNuggets.Lots.callBaseMethod(this, "initialize");
    },

    initAfterBaseIsReady: function() {
        this.getData();
    },

    dispose: function() {
        if (this.isDisposed) return;
        Rebound.GlobalTrader.Site.Controls.DataListNuggets.Lots.callBaseMethod(this, "dispose");
    },

    getDataOK: function() {
        for (var i = 0, l = this._objResult.Results.length; i < l; i++) {
            var row = this._objResult.Results[i];
            var aryData = [
				$RGT_nubButton_Lot(row.ID, row.Name)
				, $R_FN.setCleanTextValue(row.Code)
				, row.StockCount
				, row.Consignment
				, row.OnHold
			];
            var strCSS = (row.Inactive) ? "ceased" : "";
            this._table.addRow(aryData, row.ID, false, null, strCSS);
            aryData = null; row = null;
        }
    }

};
Rebound.GlobalTrader.Site.Controls.DataListNuggets.Lots.registerClass("Rebound.GlobalTrader.Site.Controls.DataListNuggets.Lots", Rebound.GlobalTrader.Site.Controls.DataListNuggets.Base, Sys.IDisposable);
