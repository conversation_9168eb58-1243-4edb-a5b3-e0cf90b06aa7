﻿-- Drop the procedure if it already exists
IF OBJECT_ID('dbo.usp_get_all_ContactGroup_by_GroupType', 'P') IS NOT NULL
BEGIN
    DROP PROCEDURE dbo.usp_get_all_ContactGroup_by_GroupType;
END

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

/* 
===========================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-201556]		Phuc Hoang			30-May-2024		CREATE			Implement Group Code Functionality for Premier Accounts
[US-203247]		Trung Pham			09-Aug-2024		UPDATE			Change order
===========================================================================================
*/

CREATE PROCEDURE [dbo].[usp_get_all_ContactGroup_by_GroupType]

@ContactGroupType NVARCHAR (100) = NULL,
@IsForDropdown BIT = NULL

AS

BEGIN
	SELECT * FROM dbo.tbContactGroup
	WHERE ContactGroupType = @ContactGroupType AND ((@IsForDropdown = 1 AND Inactive = 0) OR (@IsForDropdown = 0))
	ORDER BY Inactive, ContactName
END;
GO