///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//Marker     Changed by      Date         Remarks
//[001]      Vinay           30/07/2012   Add compulsory incoterms field when create Credit and debit note. :ESMS No:- 105
//[002]     Shashi Keshar    07/10/2016   Lock update from Client
//[003]      Umendra Gupta   21-Jan-2019  Add View Tree Button.
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Nuggets");

Rebound.GlobalTrader.Site.Controls.Nuggets.DebitMainInfo = function(element) { 
	Rebound.GlobalTrader.Site.Controls.Nuggets.DebitMainInfo.initializeBase(this, [element]);
	this._intDebitID = -1;
	this._IsPOHub = false;
	this._isAutoGenerated = false;
    this._blnExported = false;
};

Rebound.GlobalTrader.Site.Controls.Nuggets.DebitMainInfo.prototype = {

    get_intDebitID: function() { return this._intDebitID; }, set_intDebitID: function(value) { if (this._intDebitID !== value) this._intDebitID = value; },
    get_ibtnEdit: function () { return this._ibtnEdit; }, set_ibtnEdit: function (value) { if (this._ibtnEdit !== value) this._ibtnEdit = value; },
    get_ibtnExport: function () { return this._ibtnExport; }, set_ibtnExport: function (value) { if (this._ibtnExport !== value) this._ibtnExport = value; },
	get_ibtnRelease: function () { return this._ibtnRelease; }, set_ibtnRelease: function (value) { if (this._ibtnRelease !== value) this._ibtnRelease = value; },
    get_blnGlobalLogin: function () { return this._blnGlobalLogin; }, set_blnGlobalLogin: function (value) { if (this._blnGlobalLogin !== value) this._blnGlobalLogin = value; },
    //[003] start
    get_ibtnViewTree: function () { return this._ibtnViewTree; }, set_ibtnViewTree: function (value) { if (this._ibtnViewTree !== value) this._ibtnViewTree = value; },
    //[003] end
    get_IsDiffrentClient: function () { return this._IsDiffrentClient; }, set_IsDiffrentClient: function (value) { if (this._IsDiffrentClient !== value) this._IsDiffrentClient = value; },
    get_IsGSAEditPermission: function () { return this._IsGSAEditPermission; }, set_IsGSAEditPermission: function (value) { if (this._IsGSAEditPermission !== value) this._IsGSAEditPermission = value; },
    get_IsGSA: function () { return this._IsGSA; }, set_IsGSA: function (value) { if (this._IsGSA !== value) this._IsGSA = value; },

    initialize: function() {
        Rebound.GlobalTrader.Site.Controls.Nuggets.DebitMainInfo.callBaseMethod(this, "initialize");

        //nugget events
        this.addRefreshEvent(Function.createDelegate(this, this.getData));
        if (this._IsDiffrentClient == true) {
            if (this._IsGSA == true) {
                if (this._IsGSAEditPermission == true) {
                    $("#ctl00_cphMain_ctlLines_ctlDB_pnlLinks").show();
                }
                else {
                    $("#ctl00_cphMain_ctlLines_ctlDB_pnlLinks").hide();
                }
            }
            else {
                $("#ctl00_cphMain_ctlLines_ctlDB_pnlLinks").show();
            }

        }
        else {
            $("#ctl00_cphMain_ctlLines_ctlDB_pnlLinks").show();
        }
        //edit form
        if (this._ibtnEdit) {
            $R_IBTN.addClick(this._ibtnEdit, Function.createDelegate(this, this.showEditForm));
            this._frmEdit = $find(this._aryFormIDs[0]);
            this._frmEdit._intDebitID = this._intDebitID;
            this._frmEdit.addCancel(Function.createDelegate(this, this.cancelEdit));
            this._frmEdit.addSaveComplete(Function.createDelegate(this, this.saveEditComplete));
        }
        if (this._ibtnExport || this._ibtnRelease) {
		    if (this._ibtnExport) $R_IBTN.addClick(this._ibtnExport, Function.createDelegate(this, this.showExportForm));
		    if (this._ibtnRelease) $R_IBTN.addClick(this._ibtnRelease, Function.createDelegate(this, this.showReleaseForm));
		    this._frmDebitExport = $find(this._aryFormIDs[1]);
		    this._frmDebitExport.addCancel(Function.createDelegate(this, this.hideExportForm));
		    this._frmDebitExport.addSaveComplete(Function.createDelegate(this, this.saveExportComplete));
		    this._frmDebitExport.addNotConfirmed(Function.createDelegate(this, this.hideExportForm));
		}
        //[003] end
        if (this._ibtnViewTree)
            $R_IBTN.addClick(this._ibtnViewTree, Function.createDelegate(this, this.OpenDocTree));
        //[003] code end
        //initial action
        if (!this._blnIsNoDataFound && !this._blnHasInitialData) this.getData();
    },

    dispose: function() {
        if (this.isDisposed) return;
        if (this._ibtnEdit) $R_IBTN.clearHandlers(this._ibtnEdit);
        if (this._frmEdit) this._frmEdit.dispose();
        this._frmEdit = null;
        this._intDebitID = null;
        this._blnGlobalLogin = null;
        this._ibtnExport = null;
        this._ibtnRelease = null;
        this._IsDiffrentClient = null;
        this._IsGSAEditPermission = null;
        this._IsGSA = null;
        Rebound.GlobalTrader.Site.Controls.Nuggets.DebitMainInfo.callBaseMethod(this, "dispose");
    },

    getData: function() {
        this.getData_Start();
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/DebitMainInfo");
        obj.set_DataObject("DebitMainInfo");
        obj.set_DataAction("GetData");
        obj.addParameter("id", this._intDebitID);
        obj.addDataOK(Function.createDelegate(this, this.getDataOK));
        obj.addError(Function.createDelegate(this, this.getDataError));
        obj.addTimeout(Function.createDelegate(this, this.getDataError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    getDataOK: function(args) {
        var res = args._result;
        this.setFieldValue("ctlSupplier", $R_FN.showSupplierMessage($RGT_nubButton_Company(res.SupplierNo, res.SupplierName, null, null, null, res.SupplierAdvisoryNotes), res.SuppMessage));
        this.setFieldValue("ctlContact", res.Contact, $RGT_nubButton_Contact(res.ContactNo, res.Contact));
        this.setFieldValue("ctlBuyer", $R_FN.setCleanTextValue(res.Buyer));
        this.setFieldValue("ctlRaiser", $R_FN.setCleanTextValue(res.Raiser));
        this.setFieldValue("ctlDivision", $R_FN.setCleanTextValue(res.Division));
        this.setFieldValue("ctlDebitDate", res.DebitDate);
        this.setFieldValue("ctlReferenceDate", res.ReferenceDate);
        this.setFieldValue("ctlPurchaseOrder", $RGT_nubButton_PurchaseOrder(res.PurchaseOrderNo, res.PurchaseOrder));
        this.setFieldValue("ctlSupplierRMA", $RGT_nubButton_SRMA(res.SupplierRMANo, res.SupplierRMA));
        this.setFieldValue("ctlTax", $R_FN.setCleanTextValue(res.Tax));
        this.setFieldValue("ctlFreight", res.Freight);
        this.setFieldValue("ctlCurrency", $R_FN.setCleanTextValue(res.Currency));
        this.setFieldValue("ctlSupplierInvoice", $R_FN.setCleanTextValue(res.SupplierInvoice));
        this.setFieldValue("ctlSupplierCredit", $R_FN.setCleanTextValue(res.SupplierCredit));
        this.setFieldValue("ctlSupplierReturn", $R_FN.setCleanTextValue(res.SupplierReturn));
        this.setFieldValue("ctlNotes", $R_FN.setCleanTextValue(res.Notes));
        this.setFieldValue("ctlInstructions", $R_FN.setCleanTextValue(res.Instructions));
        if (res.InternalPurchaseOrderNo > 0) {
            this.setFieldValue("ctlInternalPurchaseOrder", $RGT_nubButton_InternalPurchaseOrder(res.InternalPurchaseOrderNo, res.InternalPurchaseOrderNumber));
        }
        else {
            this.showField("ctlInternalPurchaseOrder", false);
        }
        this.setFieldValue("hidDebitNumber", res.DebitNumber);
        this.setFieldValue("hidCurrencyNo", res.CurrencyNo);
        this.setFieldValue("hidCurrencyCode", res.CurrencyCode);
        this.setFieldValue("hidFreight", res.FreightVal);
        this.setFieldValue("hidPO", res.PurchaseOrder);
        this.setFieldValue("hidPONo", res.PurchaseOrderNo);
        this.setFieldValue("hidSupplierName", $R_FN.setCleanTextValue(res.SupplierName));
        this.setFieldValue("hidContactName", $R_FN.setCleanTextValue(res.Contact));
        this.setFieldValue("hidRaisedByNo", $R_FN.setCleanTextValue(res.RaisedBy));
        this.setFieldValue("hidBuyerNo", $R_FN.setCleanTextValue(res.BuyerNo));
        this.setFieldValue("hidSRMA", $R_FN.setCleanTextValue(res.SupplierRMA));
        this.setFieldValue("hidSRMANo", $R_FN.setCleanTextValue(res.SupplierRMANo));
        this.setFieldValue("hidTaxNo", $R_FN.setCleanTextValue(res.TaxNo));
        this.setFieldValue("hidDivisionNo", res.DivisionNo);
        //[001] code start
        this.setFieldValue("ctlIncotermName", $R_FN.setCleanTextValue(res.Incoterm));
        this.setFieldValue("hidIncotermNo", res.IncotermNo);
        this.setFieldValue("ctlRefNo", res.RefNumber);
        this.showField("ctlRefNo", res.RefNumber > 0);

        //[001] code end
       
        //[002] Start Here
        this.setFieldValue("ctlLockUpdateClient", res.ishublocked);
        this.setFieldValue("ctlExported", res.isExport);
        this.showField("ctlLockUpdateClient", res.isAutoGenerated);
        
        this.showField("ctlExportedDate", res.isExport);
        this.setFieldValue("ctlExportedDate", res.DateExported);
        
        this._isAutoGenerated = res.isAutoGenerated; 
        //[002] End Here
        this._IsPOHub = res.IsPoHub;
        this.setFieldValue("hidGlobalClientNo", res.DebClientNo);
        this.setFieldValue("ctlURNnumber", res.URNNumber);
        this.setFieldValue("ctlApprovedforExport", res.CanBeExported);
        this._blnExported = res.isExport;
        this.enableEditButtons(true);
        this.setDLUP(res.DLUP);
        this.getDataOK_End();
    },

    getDataError: function(args) {
        this.showError(true, args.get_ErrorMessage());
    },

    enableEditButtons: function (bln) {
	    if (bln) {
	        if (this._ibtnEdit) $R_IBTN.enableButton(this._ibtnEdit, !this._blnExported);
	        if (this._ibtnExport) $R_IBTN.enableButton(this._ibtnExport, !this._blnExported);
	        if (this._ibtnRelease) $R_IBTN.enableButton(this._ibtnRelease, this._blnExported);
	    } //else {
	    //    if (this._ibtnEdit) $R_IBTN.enableButton(this._ibtnEdit, false);
	    //    if (this._ibtnExport) $R_IBTN.enableButton(this._ibtnExport, false);
	    //    if (this._ibtnRelease) $R_IBTN.enableButton(this._ibtnRelease, false);
	    //}
	},
	showExportForm: function () {
	    this.doShowExportForm("EXPORT");
	},

	showReleaseForm: function () {
	    this.doShowExportForm("RELEASE");
	},

	doShowExportForm: function (strMode) {
	    this._frmDebitExport.changeMode(strMode);
	    this._frmDebitExport._intDebitID = this._intDebitID; 
	    this._frmDebitExport.setFieldValue("ctlNotes", this.getFieldValue("hidDebitNumber"));
	    this._frmDebitExport.setFieldValue("ctlSupplier", this.getFieldValue("hidSupplierName"));
	    this.showForm(this._frmDebitExport, true);
	},

	hideExportForm: function () {
	    this.showForm(this._frmDebitExport, false);
	},

	saveExportComplete: function () {
	    this.showForm(this._frmDebitExport, false);
	    this.showContentLoading(false);
	    this.getData();
	    this.showSavedOK(true, $R_RES.ChangesSavedSuccessfully);
	   // this.onPotentialStatusChange();
	},

    showEditForm: function() {
        this._frmEdit._intLineID = this._intLineID;
        this._frmEdit.setFieldValue("ctlSupplier", this.getFieldValue("hidSupplierName"));
        this._frmEdit.setFieldValue("ctlContact", this.getFieldValue("hidContactName"));
        this._frmEdit.setFieldValue("ctlDivision", this.getFieldValue("hidDivisionNo"));
        this._frmEdit.setFieldValue("ctlBuyer", this.getFieldValue("hidBuyerNo"));
        this._frmEdit.setFieldValue("ctlRaisedBy", this.getFieldValue("hidRaisedByNo"));
        this._frmEdit.setFieldValue("ctlDebitDate", this.getFieldValue("ctlDebitDate"));
        this._frmEdit.setFieldValue("ctlReferenceDate", this.getFieldValue("ctlReferenceDate"));
        this._frmEdit.setFieldValue("ctlSupplierInvoice", this.getFieldValue("ctlSupplierInvoice"));
        this._frmEdit.setFieldValue("ctlSupplierReturn", this.getFieldValue("ctlSupplierReturn"));
        this._frmEdit.setFieldValue("ctlSupplierCredit", this.getFieldValue("ctlSupplierCredit"));
        this._frmEdit.setFieldValue("ctlPurchaseOrder", this.getFieldValue("hidPO"));
        this._frmEdit.setFieldValue("ctlSupplierRMA", this.getFieldValue("hidSRMA"));
        this._frmEdit.setFieldValue("ctlTax", this.getFieldValue("hidTaxNo"));
        this._frmEdit.setFieldValue("ctlCurrency", this.getFieldValue("hidCurrencyNo"));
        this._frmEdit.setFieldValue("ctlFreight", this.getFieldValue("hidFreight"));
        this._frmEdit.setFieldValue("ctlNotes", this.getFieldValue("ctlNotes"));
        this._frmEdit.setFieldValue("ctlInstructions", this.getFieldValue("ctlInstructions"));
        this._frmEdit.setCurrency(this.getFieldValue("hidCurrencyCode"));
        //[001] code start
        this._frmEdit.setFieldValue("ctlIncoterm", this.getFieldValue("hidIncotermNo"));

        this._frmEdit.showField("ctlURNNumber", this.getFieldValue("ctlURNnumber"));
        this._frmEdit.setFieldValue("ctlURNNumber", this.getFieldValue("ctlURNnumber"));
        //[001] code end
        this._frmEdit.showField("ctlRaisedByLbl",!this._IsPOHub);
        this._frmEdit.showField("ctlRaisedBy",this._IsPOHub);
        this._frmEdit._IsPOHub=this._IsPOHub;
        this._frmEdit._hidRaisedByNo=this.getFieldValue("hidRaisedByNo")
        this._frmEdit.setFieldValue("ctlRaisedByLbl", this.getFieldValue("ctlRaiser"));
        //[002] Start Here
        this._frmEdit.setFieldValue("ctlLockUpdateClient", this.getFieldValue("ctlLockUpdateClient"));

        this._frmEdit.showField("ctlLockUpdateClient", this._isAutoGenerated);
        //this._frmEdit.setFieldValue("ctlExported", this.getFieldValue("ctlExported"));
        //this._frmEdit.showField("ctlExported", this._isAutoGenerated);
        this._frmEdit._intGlobalClientNo = (this._blnGlobalLogin == true) ? this.getFieldValue("hidGlobalClientNo") : null;
        //[002] End Here
        this.showForm(this._frmEdit, true);
    },

    hideEditForm: function() {
        this.showForm(this._frmEdit, false);
    },

    cancelEdit: function() {
        this.hideEditForm();
    },

    saveEditComplete: function() {
        this.hideEditForm();
        this.showSavedOK(true, $R_RES.ChangesSavedSuccessfully);
        this.onSaveEditComplete();
        this.getData();
    },
    //[002] code start
    OpenDocTree: function () {
        //$R_FN.openDocumentTree(this.getFieldValue("hidDebitNumber"), "DBT");
        $R_FN.openDocumentTree(this._intDebitID, "DBT", this.getFieldValue("hidDebitNumber"));
    }
    //[002] code end 

};

Rebound.GlobalTrader.Site.Controls.Nuggets.DebitMainInfo.registerClass("Rebound.GlobalTrader.Site.Controls.Nuggets.DebitMainInfo", Rebound.GlobalTrader.Site.Controls.Nuggets.Base);
