using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;

namespace Rebound.GlobalTrader.Site.Controls.HomeNuggets.Data {
	[WebService(Namespace = "http://tempuri.org/")]
	[WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    public class UnProcessSalesOrders : Rebound.GlobalTrader.Site.Data.Base
    {
		public override void ProcessRequest(HttpContext context) {
			if (base.init(context)) {
				switch (Action) {
					case "GetData": GetData(); break;
					default: WriteErrorActionNotFound(); break;
				}
			}
		}

		/// <summary>
		/// Gets the main data
		/// </summary>
		private void GetData() {
            try
            {
                int intLoginID = LoginID;
                if (GetFormValue_Int("OtherLoginID") > 0) intLoginID = GetFormValue_Int("OtherLoginID");
                List<SalesOrder> lstOpen = SalesOrder.GetListOpenForLoginUnProcess(intLoginID, RowCount);
                if (lstOpen == null)
                {
                    WriteErrorDataNotFound();
                }
                else
                {
                    //open
                    JsonObject jsn = new JsonObject();
                    JsonObject jsnItems = new JsonObject(true);
                    jsn.AddVariable("Count", lstOpen.Count);
                    for (int i = 0; i < lstOpen.Count; i++)
                    {
                        JsonObject jsnItem = new JsonObject();
                        jsnItem.AddVariable("ID", lstOpen[i].SalesOrderId);
                        jsnItem.AddVariable("No", lstOpen[i].SalesOrderNumber);
                        jsnItem.AddVariable("Due", Functions.FormatDate(lstOpen[i].DatePromised));
                        jsnItem.AddVariable("CM", lstOpen[i].CompanyName);
                        jsnItem.AddVariable("CMNo", lstOpen[i].CompanyNo);
                        jsnItems.AddVariable(jsnItem);
                        jsnItem.Dispose();
                        jsnItem = null;
                    }
                    jsn.AddVariable("OpenSO", jsnItems);
                    jsnItems.Dispose();
                    jsnItems = null;
                    OutputResult(jsn);
                    jsn.Dispose();
                    jsn = null;
                }
                lstOpen = null;
            }
            catch (Exception ex)
            {
                Errorlog obj = new Errorlog();
                obj.LogMessage(Convert.ToString("Exception- " + ex.Message + " " + ex.StackTrace));
            }
        }
	}
}


