<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="AddNotAllowed_Company" xml:space="preserve">
    <value>Sorry, you do not have permissions to add a new Company</value>
  </data>
  <data name="AddNotAllowed_Credit" xml:space="preserve">
    <value>Sorry, you do not have permissions to add a new Credit Note</value>
  </data>
  <data name="AddNotAllowed_CRMA" xml:space="preserve">
    <value>Sorry, you do not have permissions to add a new Customer RMA</value>
  </data>
  <data name="AddNotAllowed_Debit" xml:space="preserve">
    <value>Sorry, you do not have permissions to add a new Debit Note</value>
  </data>
  <data name="AddNotAllowed_GoodsIn" xml:space="preserve">
    <value>Sorry, you do not have permissions to add a new Goods In Note</value>
  </data>
  <data name="AddNotAllowed_Invoice" xml:space="preserve">
    <value>Sorry, you do not have permissions to add a new Invoice</value>
  </data>
  <data name="AddNotAllowed_Lot" xml:space="preserve">
    <value>Sorry, you do not have permissions to add a new Lot</value>
  </data>
  <data name="AddNotAllowed_Manufacturer" xml:space="preserve">
    <value>Sorry, you do not have permissions to add a new Manufacturer</value>
  </data>
  <data name="AddNotAllowed_PurchaseOrder" xml:space="preserve">
    <value>Sorry, you do not have permissions to add a new Purchase Order</value>
  </data>
  <data name="AddNotAllowed_Quote" xml:space="preserve">
    <value>Sorry, you do not have permissions to add a new Quote</value>
  </data>
  <data name="AddNotAllowed_Requirement" xml:space="preserve">
    <value>Sorry, you do not have permissions to add a new Customer Requirement</value>
  </data>
  <data name="AddNotAllowed_SalesOrder" xml:space="preserve">
    <value>Sorry, you do not have permissions to add a new Sales Order</value>
  </data>
  <data name="AddNotAllowed_Service" xml:space="preserve">
    <value>Traurig, haben Sie nicht Erlaubnis, einen neuen Service zu addieren</value>
  </data>
  <data name="AddNotAllowed_SRMA" xml:space="preserve">
    <value>Traurig, haben Sie nicht Erlaubnis, einen neuen Lieferanten RMA zu addieren</value>
  </data>
  <data name="AddNotAllowed_Stock" xml:space="preserve">
    <value>Traurig, haben Sie nicht Erlaubnis, ein neues auf lagereinzelteil zu addieren</value>
  </data>
  <data name="AllModulesVisible" xml:space="preserve">
    <value>Alle Module sind z.Z. sichtbar</value>
  </data>
  <data name="ApplicationError" xml:space="preserve">
    <value>Traurig, gab es ein Problem. Die folgenden Details sind in Ihrem Computer' gemerkt worden; s-Ereignismaschinenbordbuch und gemailt dem Rebound-Personal für Aufmerksamkeit.</value>
  </data>
  <data name="BrowserWarning" xml:space="preserve">
    <value>Sie verwenden {0} Version {1}&lt;br /&gt;Wir empfehlen Sie Gebrauch Firefox 2+, Internet Explorer 8+, Chrom 1+ oder Safari 3+.</value>
  </data>
  <data name="ConfirmAuthorise" xml:space="preserve">
    <value>Sind Sie Sie möchten diesen Auftrag autorisieren sicher?</value>
  </data>
  <data name="ConfirmDeauthorise" xml:space="preserve">
    <value>Sind Sie Sie möchten de-authorise diesen Auftrag sicher?</value>
  </data>
  <data name="ConfirmDeleteCompanyAddress" xml:space="preserve">
    <value>Sind Sie Sie möchten diese Adresse löschen sicher?</value>
  </data>
  <data name="DataNotFound" xml:space="preserve">
    <value>Störung: Keine Daten fanden</value>
  </data>
  <data name="DocumentSendError" xml:space="preserve">
    <value>Traurig, gab es ein Problem, das Ihre Mitteilung sendet, versucht bitte noch einmal oder bittet Ihren Systemverwalter, die eMail-Einstellungen zu überprüfen</value>
  </data>
  <data name="DocumentSentOK" xml:space="preserve">
    <value>Ihre Mitteilung wurde erfolgreich gesendet</value>
  </data>
  <data name="DoubleClickToViewMail" xml:space="preserve">
    <value>Double-click eine Mitteilung, um anzusehen</value>
  </data>
  <data name="HiddenModules" xml:space="preserve">
    <value>Versteckte Module</value>
  </data>
  <data name="LandedCostCalculationError" xml:space="preserve">
    <value>Traurig, gab es eine Störung</value>
  </data>
  <data name="LoginLicenceProblem" xml:space="preserve">
    <value>Traurig, gab es ein Problem mit Ihrer Anwendungslizenz</value>
  </data>
  <data name="LoginLicenceProblem_TooManyUsers" xml:space="preserve">
    <value>Traurig, gab es ein Problem mit Ihrer Anwendungslizenz&lt;br /&gt;Sie haben Ihre die Erlaubnis gehabte Zahl der Benutzer überstiegen.</value>
  </data>
  <data name="LoginPasswordNotRecognised" xml:space="preserve">
    <value>Traurig, erkannt dieses Kennwort nicht</value>
  </data>
  <data name="LoginUsernameNotRecognised" xml:space="preserve">
    <value>Traurig, wird dieses username nicht erkannt</value>
  </data>
  <data name="NewMailMessages" xml:space="preserve">
    <value>Sie haben neue Mitteilungen</value>
  </data>
  <data name="NoAccountingInfo" xml:space="preserve">
    <value>Es gibt keine anzuzeigen Abrechnungsinformationen</value>
  </data>
  <data name="OnStop" xml:space="preserve">
    <value>Diese Firma ist auf Anschlag</value>
  </data>
  <data name="ReportParameterValidationError" xml:space="preserve">
    <value>Tragen Sie bitte alle erforderlichen Parameter ein</value>
  </data>
  <data name="RFQSendError" xml:space="preserve">
    <value>Traurig, gab es ein Problem, das Ihre Mitteilung sendet, versucht bitte noch einmal oder bittet Ihren Systemverwalter, die eMail-Einstellungen zu überprüfen</value>
  </data>
  <data name="RFQSentOK" xml:space="preserve">
    <value>Ihre Mitteilung wurde erfolgreich gesendet</value>
  </data>
  <data name="SavedOK" xml:space="preserve">
    <value>Ihre Änderungen wurden erfolgreich gespart</value>
  </data>
  <data name="SelectAddress" xml:space="preserve">
    <value>Wählen Sie Adresse vor, um anzusehen oder zu redigieren</value>
  </data>
  <data name="SelectContactToView" xml:space="preserve">
    <value>Wählen Sie Kontakt vor, um anzusehen</value>
  </data>
  <data name="SOAuthorisation_OverCreditLimit" xml:space="preserve">
    <value>Die Gesamtmenge der geöffneten Aufträge nimmt die Firma über seiner Kreditlinie, es kann nicht autorisiert werden</value>
  </data>
  <data name="SOLines_OverCreditLimit" xml:space="preserve">
    <value>Diese Linie würde die Firma über seiner Kreditlinie, es kann nicht bekannt gegeben werden nehmen</value>
  </data>
  <data name="StockQuarantined" xml:space="preserve">
    <value>Dieser Vorrat wird unter Quarantäne gestellt</value>
  </data>
  <data name="SureDeleteAddress" xml:space="preserve">
    <value>Sind Sie Sie möchten diese Adresse löschen sicher?</value>
  </data>
  <data name="SureDeleteContact" xml:space="preserve">
    <value>Sind Sie Sie möchten diesen Kontakt löschen sicher?</value>
  </data>
  <data name="SureDeleteContactLogItem" xml:space="preserve">
    <value>Sind Sie Sie möchten dieses Kontaktmaschinenbordbucheinzelteil löschen sicher?</value>
  </data>
  <data name="YouSearchedFor" xml:space="preserve">
    <value>Sie suchten nach</value>
  </data>
</root>