﻿using System;
using System.Data;
using System.Web;
using System.Web.Services;
using System.Collections.Generic;
using System.Text;
using Rebound.GlobalTrader.BLL;
using System.IO;
using System.Data.OleDb;
using System.Web.Script.Serialization;
using System.Text.RegularExpressions;
using System.Linq;
using Rebound.GlobalTrader.Site.Enumerations;
using System.Collections.Concurrent;

namespace Rebound.GlobalTrader.Site.Controls.Nuggets.Data
{
    [WebService(Namespace = "http://tempuri.org/")]
    [WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    /// <summary>
    /// Summary description for StockImportTool
    /// </summary>
    public class CrossMatch : Rebound.GlobalTrader.Site.Data.Base
    {
        string conStr = string.Empty;
        DateTime? OutDate = null;
        public override void ProcessRequest(HttpContext context)
        {
            
            if (base.init(context))
            {
//                context.Session.Clear();
                Boolean blnNoSession = false;
                if (context.Session == null)
                {
                    blnNoSession = true;
                }
                //else
                //{
                //   // blnNoSession = !SessionManager.CheckLoggedIn();
                //}
                //if (blnNoSession)
                //{
                //    //context.Response.ContentType = "text/plain";
                //    // context.Response.Write("<script>location.href='~/Logout.aspx?ret=CrossMatch.aspx'</script>");
                    
                //    WriteErrorNoSession();
                //}

                
                    if (context.Request.QueryString["action"] != null)
                        Action = context.Request.QueryString["action"];
                    if (blnNoSession == true)
                    {
                        WriteErrorNoSession2(context);
                    }
                if (blnNoSession == false)
                {
                    switch (Action)
                    {

                        case "GetBOMClient": GetBOMClient(context); break;
                        case "GetBomCusReq": GetBomCusReq(context); break;
                        case "GetCompanyType": GetCompanyType(); break;
                        case "GetCurrency": GetCurrency(); break;

                        case "DeleteOfferRecord": DeleteOffer_Records(context); break;
                        case "DeleteTrusted_Records": DeleteTrusted_Records(context); break;
                        case "DeletePOQuotes_Records": DeletePOQuotes_Records(context); break;

                        case "GetBOMListForCustomerReqOffer": GetBOMListForCustomerReqOffer(context); break;
                        case "GetCustomerReqOfferList": GetCustomerReqOfferList(context); break;

                        case "AddSourcingResultFromOffer": AddSourcingResultFromOffer(context); break;
                        case "AddSourcingResultFromTrusted": AddSourcingResultFromTrusted(context); break;
                        case "AddSourcingResultFromPOQuote": AddSourcingResultFromPOQuote(context); break;

                        case "GetData_Offers": GetData_Offers(context); break;
                        case "GetData_Trusted": GetData_Trusted(context); break;
                        case "GetData_POQuotes": GetData_POQuotes(context); break;
                        case "GetData_History": GetData_History(context); break;
                        case "GetData_CustomerRequirements": GetData_CustomerRequirements(context); break;
                        case "GetData_Quotes": GetData_Quotes(context); break;
                        case "GetData_Sales": GetData_Sales(context); break;
                        case "GetData_Purchases": GetData_Purchases(context); break;

                        case "GetPackageType": GetPackageType(); break;
                        case "GetROHSStatus": GetROHSStatus(); break;
                        case "GetOfferStatus": GetOfferStatus(); break;
                        case "GetMSL": GetMSL(); break;
                        case "GetSupplier": GetSupplier(context); break;
                        case "GetPartNo": GetPartNo(context); break;
                        case "GetManufacturer": GetManufacturer(context); break;
                        case "GetProduct": GetProduct(context); break;
                        case "GetCountryOfOrigin": GetCountryOfOrigin(); break;
                        case "GetRegion": GetRegion(); break;

                        //for clone
                        case "GetOfferItem": GetOfferItem(context); break;
                        case "AddOfferAttachToSourcing": AddOfferAttachToSourcing(context); break;
                        case "GetTrustedItemForClone": GetTrustedItem(context); break;
                        case "AddTrustedAttachToSourcing": AddTrustedAttachToSourcing(context); break;

                        case "EditOffer": EditOffer(context); break;
                        case "EditTrusted": EditTrusted(context); break;

                        case "MaintainAutoSearchLog": MaintainAutoSearchLog(context); break;
                        case "GetMaintainAutoSearchLog": GetMaintainAutoSearchLog(context); break;

                        case "SaveOfferFilterRecord": SaveOfferFilterRecord(context); break;
                        case "DeleteOfferFilterRecord": DeleteOfferFilterRecord(context); break;

                        case "ResetSessionStateLog": ResetSessionStateLog(context); break;

                        case "ExportToCSV": ExportToCSV(context); break;


                        default: WriteErrorActionNotFound(); break;
                   }
                    
                }
               
            }
        }


        protected void WriteErrorNoSession2(HttpContext context)
        {
            string strError = string.Empty;
            strError = "LOGGED_OUT";
            JsonObject jsn = new JsonObject();
            jsn.AddVariable("Error", true);
            jsn.AddVariable("Message", strError);
            OutputResult(jsn);
            jsn.Dispose(); jsn = null;
        }
        public void MaintainAutoSearchLog(HttpContext context)
        {
            try
            {
                int BomId = string.IsNullOrEmpty(context.Request.QueryString["BOMId"]) ? 0 : int.Parse(context.Request.QueryString["BOMId"]);
                string logDetails = string.IsNullOrEmpty(context.Request.QueryString["logDetails"]) ? "0" : Convert.ToString((context.Request.QueryString["logDetails"]));
                Stock.CrossMatchSearchLog(BomId, SessionManager.LoginID ?? 0, SessionManager.ClientID ?? 0, logDetails.TrimEnd(','));

            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }
        public void GetMaintainAutoSearchLog(HttpContext context)
        {
            try
            {
                int BomId = string.IsNullOrEmpty(context.Request.QueryString["BOMId"]) ? 0 : int.Parse(context.Request.QueryString["BOMId"]);

                Offer of = Offer.GetCrossMatchAutoSearch(BomId, SessionManager.LoginID ?? 0, SessionManager.ClientID ?? 0);
                JsonObject jsn = new JsonObject();
                if (of == null)
                    jsn.AddVariableNew("logdetails", null);
                else
                jsn.AddVariableNew("logdetails", of.LogDetails);
                
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }

        //usp_CrossMatch_SearchLog_select
        public void GetBOMClient(HttpContext context)
        {
            try
            {
                DataTable dtClient = BLL.Stock.GetBomClient(SessionManager.ClientID, SessionManager.LoginID ?? 0);
                context.Response.Write(ConvertDataTableToJSON(dtClient));

            }
            catch (Exception ex)
            {
                WriteError(ex);
            }
        }
        public void GetBomCusReq(HttpContext context)
        {
            try
            {
                int BomNo = string.IsNullOrEmpty(context.Request.QueryString["BomNo"]) ? 0 : int.Parse(context.Request.QueryString["BomNo"]);
                DataTable dtClient = BLL.Stock.GetBomCusReq(SessionManager.ClientID, BomNo, SessionManager.LoginID ?? 0);
                context.Response.Write(ConvertDataTableToJSON(dtClient));

            }
            catch (Exception ex)
            {
                WriteError(ex);
            }
        }
        private string ConvertDataTableToJSON(DataTable dt)
        {
            System.Web.Script.Serialization.JavaScriptSerializer serializer = new System.Web.Script.Serialization.JavaScriptSerializer();
            serializer.MaxJsonLength = Int32.MaxValue;
            List<ConcurrentDictionary<string, object>> rows = new List<ConcurrentDictionary<string, object>>();
            ConcurrentDictionary<string, object> row;
            foreach (DataRow dr in dt.Rows)
            {
                row = new ConcurrentDictionary<string, object>();
                foreach (DataColumn col in dt.Columns)
                {
                    row.TryAdd(col.ColumnName, dr[col]);
                }
                rows.Add(row);
            }
            return serializer.Serialize(rows);
        }
        protected void GetCompanyType()
        {
            JsonObject jsn = new JsonObject();
            JsonObject jsnList = new JsonObject(true);
            List<BLL.CompanyType> lst = BLL.CompanyType.DropDown();
            for (int i = 0; i < lst.Count; i++)
            {
                JsonObject jsnItem = new JsonObject();
                jsnItem.AddVariableNew("ID", lst[i].CompanyTypeId);
                jsnItem.AddVariableNew("Name", lst[i].Name);
                jsnList.AddVariableNew(jsnItem);
                jsnItem.Dispose(); jsnItem = null;
            }
            lst.Clear(); lst = null;
            //jsn.AddVariable("Types", jsnList);
            jsn.AddVariableNew(jsnList);
            //jsnList.Dispose(); jsnList = null;
            OutputResult(jsnList);
            //jsn.Dispose(); jsn = null;
            jsnList.Dispose(); jsnList = null;
        }
        protected void GetCurrency()
        {
            int? intGlobalLoginClientNo;
            intGlobalLoginClientNo = GetFormValue_NullableInt("GlobalLoginClientNo");

            string strCacheOptions = CacheManager.SerializeOptions(new object[] { intGlobalLoginClientNo.HasValue ? intGlobalLoginClientNo.Value : SessionManager.ClientID });
            //string strCachedData = CacheManager.GetDropDownData(_objDropDown.ID, strCacheOptions);
            // if (string.IsNullOrEmpty(strCachedData))
            //{
            JsonObject jsn = new JsonObject();
            JsonObject jsnList = new JsonObject(true);
            List<BLL.Currency> lst = BLL.Currency.DropDownForClient((intGlobalLoginClientNo.HasValue && intGlobalLoginClientNo.Value > 0) ? intGlobalLoginClientNo.Value : SessionManager.ClientID);
            for (int i = 0; i < lst.Count; i++)
            {
                //CrossMatch.aspx     
                JsonObject jsnItem = new JsonObject();
                jsnItem.AddVariableNew("ID", lst[i].CurrencyId);
                jsnItem.AddVariableNew("Name", String.Format("{1} - {0}", lst[i].CurrencyDescription, lst[i].CurrencyCode));
                jsnItem.AddVariableNew("Code", lst[i].CurrencyCode);
                jsnList.AddVariableNew(jsnItem);
                jsnItem.Dispose(); jsnItem = null;
            }
            lst.Clear(); lst = null;
            jsn.AddVariableNew(jsnList);
            //  jsnList.Dispose(); jsnList = null;
            // CacheManager.StoreDropDown(_objDropDown.ID, strCacheOptions, jsn.Result, CacheManager.CacheExpiryType.OneWorkingDay);
            //OutputResult(jsn);
            OutputResult(jsnList);

            jsnList.Dispose(); jsnList = null;
            //}
            //else
            // {
            //    _context.Response.Write(strCachedData);
            //}
            //strCachedData = null;
        }

        public void GetBOMListForCustomerReqOffer(HttpContext context)
        {
            try
            {
                int BomNo = string.IsNullOrEmpty(context.Request.QueryString["BOMId"]) ? 0 : int.Parse(context.Request.QueryString["BOMId"]);
                int customerRequirementID = string.IsNullOrEmpty(context.Request.QueryString["customerRequirementNo"]) ? 0 : int.Parse(context.Request.QueryString["customerRequirementNo"]);



                List<CustomerRequirement> lst = CustomerRequirement.GetBOMListForCustomerRequirement_Offer(BomNo, customerRequirementID, SessionManager.ClientID);
                JsonObject jsn = new JsonObject();
                JsonObject jsnItems = new JsonObject(true);
                jsn.AddVariableNew("iTotalRecords", lst.Count);
                jsn.AddVariableNew("iTotalDisplayRecords", lst.Count);
                bool blnAllHasDeliveryDate = false;
                bool blnAllHasProduct = false;
                if (lst.Count > 0)
                {
                    blnAllHasDeliveryDate = lst[0].AllSorcingHasDelDate == 0;
                    blnAllHasProduct = lst[0].AllSorcingHasProduct == 0;
                }
                foreach (CustomerRequirement cReq in lst)
                {

                    JsonObject jsnItem = new JsonObject();
                    jsnItem.AddVariableNew("ID", cReq.CustomerRequirementId);
                    jsnItem.AddVariableNew("CustReqNo", cReq.CustomerRequirementNumber);
                    jsnItem.AddVariableNew("PartNo", cReq.Part);


                    //jsnItem.AddVariable("Part", (cReq.Alternate) ? String.Format("{0} ({1})", cReq.Part, Functions.GetGlobalResource("Misc", "Alternate")) : cReq.Part);
                    jsnItem.AddVariableNew("Part", Functions.GetPartWithAlternate(cReq.Part, cReq.AlternateStatus, cReq.Alternate));
                    jsnItem.AddVariableNew("Closed", cReq.Closed);
                    jsnItem.AddVariableNew("ClosedReason", cReq.ClosedReason);
                    jsnItem.AddVariableNew("MfrNo", cReq.ManufacturerNo);
                    jsnItem.AddVariableNew("Mfr", cReq.ManufacturerCode);
                    jsnItem.AddVariableNew("Product", cReq.ProductName);
                    jsnItem.AddVariableNew("DC", cReq.DateCode);
                    jsnItem.AddVariableNew("Package", cReq.PackageName);
                    jsnItem.AddVariableNew("Price", Functions.FormatCurrency(cReq.Price, cReq.CurrencyCode));
                    jsnItem.AddVariableNew("TPriceInBom", Functions.FormatCurrency(cReq.ConvertedTargetValue, cReq.BOMCurrencyCode));
                    if (cReq.CurrencyNo != SessionManager.ClientCurrencyID) jsnItem.AddVariableNew("PriceInBase", Functions.FormatCurrency(BLL.Currency.ConvertValueToBaseCurrency(cReq.Price, (int)cReq.CurrencyNo, DateTime.Now), SessionManager.ClientCurrencyCode));
                    jsnItem.AddVariableNew("Quantity", cReq.Quantity);
                    jsnItem.AddVariableNew("Alt", cReq.Alternate);
                    jsnItem.AddVariableNew("ROHS", cReq.ROHS);
                    jsnItem.AddVariableNew("Date", Functions.FormatDate(cReq.DatePromised));
                    jsnItem.AddVariableNew("CustomerPart", cReq.CustomerPart);
                    if (SessionManager.IsPOHub == true)
                    {
                        jsnItem.AddVariableNew("Company", cReq.ClientName);
                    }
                    else
                    {
                        jsnItem.AddVariableNew("Company", cReq.CompanyName);
                    }
                    jsnItem.AddVariableNew("CompanyNo", cReq.CompanyNo);
                    jsnItem.AddVariableNew("SalesmanName", cReq.SalesmanName);
                    jsnItem.AddVariableNew("Salesman", cReq.Salesman);
                    jsnItem.AddVariableNew("BOMCode", cReq.BOMCode);
                    jsnItem.AddVariableNew("BOMFullName", cReq.BOMFullName);
                    jsnItem.AddVariableNew("Instructions", Functions.ReplaceLineBreaks(cReq.Instructions));
                    if (!cReq.Alternate)
                    {
                        string strReason = (cReq.ReasonNo > 0) ? string.Format(" ({0})", cReq.ClosedReason) : "";
                        //jsn.AddVariableNew("DisplayStatus", String.Format("{0}{1}", Functions.GetGlobalResource("Status", cReq.DisplayStatus), strReason));
                    }
                    jsnItem.AddVariableNew("BOMNo", cReq.BOMNo);
                    jsnItem.AddVariableNew("Released", cReq.POHubReleaseBy > 0);
                    jsnItem.AddVariableNew("CMNo", cReq.CompanyNo);
                    //if(SessionManager.IsPOHub.Value)
                    //    jsnItem.AddVariable("HasSourcingResult", (cReq.SourcingResultId.HasValue && cReq.SourcingResultId > 0 && cReq.POHubCompany > 0));
                    //else
                    //    jsnItem.AddVariable("HasSourcingResult", (cReq.SourcingResultId.HasValue && cReq.SourcingResultId > 0));


                    if (SessionManager.IsPOHub.Value)
                        jsnItem.AddVariableNew("HasSourcingResult", cReq.HasHubSourcingResult.Value);
                    else
                        jsnItem.AddVariableNew("HasSourcingResult", cReq.HasClientSourcingResult.Value);


                    jsnItem.AddVariableNew("IsRequestToPurchaseQuote", (cReq.RequestToPOHubBy ?? 0) > 0);

                    jsnItem.AddVariableNew("PurchaseQuoteNumber", cReq.PurchaseQuoteNumber);
                    jsnItem.AddVariableNew("PurchaseQuoteId", cReq.PurchaseQuoteId);
                    jsnItem.AddVariableNew("IsPurchaseRequestCreated", ((cReq.PurchaseQuoteId ?? 0) > 0));
                    jsnItem.AddVariableNew("FactorySealed", cReq.FactorySealed == false ? "NO" : "YES");
                    jsnItem.AddVariableNew("MSL", cReq.MSL);
                    jsnItem.AddVariableNew("SourcingResult", cReq.SourcingResult > 0 ? false : true);
                    jsnItem.AddVariableNew("BOMStatus", cReq.BOMStatus);
                    jsnItem.AddVariableNew("IPOClientNo", cReq.ClientNo);
                    jsnItem.AddVariableNew("IsNoBid", cReq.IsNoBid);
                    jsnItem.AddVariableNew("IsExpeditDate", cReq.ExpeditDate.HasValue != false ? "Yes" : "");
                    jsnItem.AddVariableNew("UpdateByPH", cReq.UpdateByPH);
                    jsnItem.AddVariableNew("RequestToPOHubBy", cReq.RequestToPOHubBy);
                    jsnItems.AddVariableNew(jsnItem);
                    jsnItem.Dispose();

                    jsnItem = null;
                }
                jsn.AddVariableNew("data", jsnItems);
                //jsn.AddVariableNew("AllHasDelDate", blnAllHasDeliveryDate);
                // jsn.AddVariableNew("AllHasProduct", blnAllHasProduct);
                OutputResult(jsn);
                jsn.Dispose();
                jsn = null;
            }
            catch (Exception ex)
            {
                WriteError(ex);
            }
        }
        public void GetCustomerReqOfferList(HttpContext context)
        {
            int customerRequirementID = string.IsNullOrEmpty(context.Request.QueryString["customerRequirementNo"]) ? 0 : int.Parse(context.Request.QueryString["customerRequirementNo"]);
            List<SourcingResult> lst = SourcingResult.GetListForBOMCustomerRequirement_List(customerRequirementID, Convert.ToBoolean(SessionManager.IsPOHub));

            if (lst == null)
            {
                WriteErrorDataNotFound();
            }
            else
            {
                try
                {
                    JsonObject jsn = new JsonObject();
                    JsonObject jsnItems = new JsonObject(true);

                    bool blnAllHasDeliveryDate = true;
                    bool blnAllHasProduct = true;
                    foreach (SourcingResult ln in lst)
                    {

                        if (blnAllHasDeliveryDate == true && !ln.DeliveryDate.HasValue)
                            blnAllHasDeliveryDate = false;

                        if (blnAllHasProduct == true && (!ln.ProductNo.HasValue || ln.ProductNo == 0))
                        {
                            blnAllHasProduct = false;
                        }

                        JsonObject jsnItem = new JsonObject();
                        jsnItem.AddVariableNew("ID", ln.SourcingResultId);
                        jsnItem.AddVariableNew("IsClosed", ln.IsClosed);
                        if (ln.SourcingTable == "PQ" || ln.SourcingTable == "OFPH" || ln.SourcingTable == "EXPH")
                        {
                            jsnItem.AddVariableNew("SupplierNo", Convert.ToBoolean(SessionManager.IsPOHub) ? ln.POHubCompanyNo : ln.ClientCompanyNo);
                            jsnItem.AddVariableNew("Supplier", Convert.ToBoolean(SessionManager.IsPOHub) ? ln.POHubSupplierName : ln.ClientSupplierName);

                            if (ln.ClientCurrencyNo != SessionManager.ClientCurrencyID)
                            {
                                if (!SessionManager.IsPOHub.Value)
                                {
                                    jsnItem.AddVariableNew("PriceInBase", Functions.FormatCurrency(BLL.Currency.ConvertValueToBaseCurrency(ln.Price, ln.ClientCurrencyNo, DateTime.Now), SessionManager.ClientCurrencyCode));
                                    jsnItem.AddVariableNew("EstimatedShippingCostInBase", Functions.FormatCurrency(BLL.Currency.ConvertValueToBaseCurrency(ln.EstimatedShippingCost, ln.ClientCurrencyNo, DateTime.Now), SessionManager.ClientCurrencyCode));

                                }
                                else
                                {
                                    jsnItem.AddVariableNew("PriceInBase", Functions.FormatCurrency(BLL.Currency.ConvertValueToBaseCurrency(ln.Price, ln.CurrencyNo, DateTime.Now), SessionManager.ClientCurrencyCode));
                                }

                            }
                            
                        }
                        else
                        {
                            jsnItem.AddVariableNew("SupplierNo", ln.SupplierNo);
                            jsnItem.AddVariableNew("Supplier", ln.SupplierName);

                            if (ln.CurrencyNo != SessionManager.ClientCurrencyID)
                            {
                                if (!SessionManager.IsPOHub.Value)
                                {
                                    jsnItem.AddVariableNew("PriceInBase", Functions.FormatCurrency(BLL.Currency.ConvertValueToBaseCurrency(ln.Price, ln.CurrencyNo, DateTime.Now), SessionManager.ClientCurrencyCode));
                                    jsnItem.AddVariableNew("EstimatedShippingCostInBase", Functions.FormatCurrency(BLL.Currency.ConvertValueToBaseCurrency(ln.EstimatedShippingCost, ln.CurrencyNo, DateTime.Now), SessionManager.ClientCurrencyCode));

                                }

                            }
                            else
                            {
                                jsnItem.AddVariableNew("PriceInBase", Functions.FormatCurrency(BLL.Currency.ConvertValueToBaseCurrency(ln.Price, ln.CurrencyNo, DateTime.Now), SessionManager.ClientCurrencyCode));
                            }
                           
                        }


                        jsnItem.AddVariableNew("ManufacturerName", ln.ManufacturerName);
                        
                        jsnItem.AddVariableNew("PFPQ", ln.SourcingTable == "PQ" ? "YES" : "-");
                        jsnItem.AddVariableNew("Part", ln.Part);
                        jsnItem.AddVariableNew("Product", ln.ProductName);
                        jsnItem.AddVariableNew("Package", ln.PackageName);
                        jsnItem.AddVariableNew("Qty", ln.Quantity);
                        jsnItem.AddVariableNew("MfrNo", ln.ManufacturerNo);
                        jsnItem.AddVariableNew("Mfr", ln.ManufacturerCode);
                        jsnItem.AddVariableNew("DC", ln.DateCode);
                        jsnItem.AddVariableNew("Date", Functions.FormatDate(ln.OfferStatusChangeDate));
                        //jsnItem.AddVariable("By", (String.IsNullOrEmpty(ln.SalesmanName)) ? ln.OfferStatusChangeEmployeeName : ln.SalesmanName);
                        jsnItem.AddVariableNew("By", ln.OfferStatusChangeEmployeeName);
                        jsnItem.AddVariableNew("Notes", Functions.ReplaceLineBreaks(ln.Notes));
                        jsnItem.AddVariableNew("MslSpqFactorySealed", ln.MslSpqFactorySealed);

                        //jsnItem.AddVariable("SupplierManufacturerName", ln.ManufacturerName);
                        //jsnItem.AddVariable("SupplierDateCode", String.IsNullOrEmpty(ln.SupplierDateCode)?ln.DateCode:ln.SupplierDateCode);
                        //jsnItem.AddVariable("SupplierPackageType", String.IsNullOrEmpty(ln.SupplierPackageType)?ln.PackageName:ln.SupplierPackageType);
                        //jsnItem.AddVariable("SupplierProductType", String.IsNullOrEmpty(ln.SupplierProductType)?ln.ProductName:ln.SupplierProductType);
                        //[001] start
                        jsnItem.AddVariableNew("SupplierWarranty", ln.SupplierWarranty);
                        //[001] end
                        jsnItem.AddVariableNew("SupplierManufacturerName", ln.SupplierManufacturerName);
                        jsnItem.AddVariableNew("SupplierDateCode", ln.SupplierDateCode);
                        jsnItem.AddVariableNew("SupplierPackageType", ln.SupplierPackageType);
                        jsnItem.AddVariableNew("SupplierProductType", ln.SupplierProductType);

                        jsnItem.AddVariableNew("SupplierMOQ", ln.SupplierMOQ);
                        jsnItem.AddVariableNew("SupplierTotalQSA", ln.SupplierTotalQSA);
                        jsnItem.AddVariableNew("SupplierLTB", ln.SupplierLTB);
                        jsnItem.AddVariableNew("SupplierNotes", Functions.ReplaceLineBreaks(ln.SupplierNotes));

                        jsnItem.AddVariableNew("SupplierType", ln.SupplierType);
                        jsnItem.AddVariableNew("DeliveryDate", Functions.FormatDate(ln.DeliveryDate));
                        jsnItem.AddVariableNew("SourcRelease", ln.SourcingRelease);
                        //jsnItem.AddVariable("AllHasDelDate", blnAllHasDeliveryDate);

                        //get price in base currency too if it's not already
                        jsnItem.AddVariableNew("Price", Functions.FormatCurrency(ln.Price, ln.CurrencyCode));
                        jsnItem.AddVariableNew("EstimatedShippingCost", Functions.FormatCurrency(ln.EstimatedShippingCost, ln.CurrencyCode));
                        jsnItem.AddVariableNew("BuyPrice", Functions.FormatCurrency(ln.OriginalPrice, ln.ActualCurrencyCode));
                        jsnItem.AddVariableNew("ActualPrice", Functions.FormatCurrency(ln.ActualPrice, SessionManager.ClientCurrencyCode));
                        jsnItem.AddVariableNew("SupplierPercentage", ln.SupplierPercentage + Functions.GetGlobalResource("Misc", "PercentSymbol"));
                        jsnItem.AddVariableNew("SPQ", ln.SPQ);
                        jsnItem.AddVariableNew("LeadTime", ln.LeadTime);
                        jsnItem.AddVariableNew("ROHSStatus", ln.ROHSStatus);
                        jsnItem.AddVariableNew("FactorySealed", ln.FactorySealed);
                        jsnItem.AddVariableNew("MSL", ln.MSL);
                        jsnItem.AddVariableNew("ClientNo", ln.ClientNo);
                        jsnItem.AddVariableNew("IsClosed", ln.IsClosed);
                        jsnItem.AddVariableNew("IsSoCreated", ln.IsSoCreated);
                        // jsnItem.AddVariable("TermsName", ln.IsApplyPOBankFee==true?ln.TermsName:"");
                        jsnItem.AddVariableNew("TermsName", ln.TermsName);
                        jsnItem.AddVariableNew("ActBuyCurrencyNo", ln.ActualCurrencyNo);
                        jsnItem.AddVariableNew("MSLLevelNo", ln.MSLLevelNo);
                        jsnItem.AddVariableNew("MSLLevelText", ln.MSLLevelText);
                        jsnItem.AddVariableNew("Countlist", lst.Count);


                        jsnItem.AddVariableNew("IsSourcingReleasedCount", ln.SourcingReleasedCount > 0);
                        if (ln.SourceRef == "Q" || ln.SourceRef == "S")
                        {
                            jsnItem.AddVariableNew("SourceRef", false);
                        }
                        else
                        {
                            jsnItem.AddVariableNew("SourceRef", true);
                        }

                        //else
                        //{
                        //    jsnItem.AddVariable("PriceInBase", Functions.FormatCurrency(ln.Price, SessionManager.ClientCurrencyCode));
                        //}
                        //status
                        string strStatus = "";
                        if (ln.OfferStatusNo != null) strStatus = Functions.GetGlobalResource("OfferStatus", (OfferStatus.List)ln.OfferStatusNo);
                        jsnItem.AddVariableNew("Status", strStatus);

                        //related quotes
                        JsonObject jsnQuotes = new JsonObject(true);
                        foreach (BLL.Quote qt in Quote.GetListForSourcingResult(ln.SourcingResultId))
                        {
                            JsonObject jsnQuote = new JsonObject();
                            jsnQuote.AddVariableNew("ID", qt.QuoteId);
                            jsnQuote.AddVariableNew("No", qt.QuoteNumber);
                            jsnQuotes.AddVariableNew(jsnQuote);
                            jsnQuote.Dispose(); jsnQuote = null;
                        }
                        jsnItem.AddVariableNew("Quotes", jsnQuotes);
                        jsnItem.AddVariableNew("RegionName", ln.RegionName);
                        //[002] start
                        jsnItem.AddVariableNew("IsTestingRecommended", ln.IsTestingRecommended);
                        jsnItem.AddVariableNew("IsImageAvail", ln.IsImageAvailable);
                        //[002] end
                        jsnQuotes.Dispose(); jsnQuotes = null;

                        jsnItems.AddVariableNew(jsnItem);
                        jsnItem.Dispose(); jsnItem = null;
                    }
                   
                    jsn.AddVariableNew("data", jsnItems);
                    jsn.AddVariableNew("AllHasDelDate", blnAllHasDeliveryDate);
                    jsn.AddVariableNew("AllHasProduct", blnAllHasProduct);

                    OutputResult(jsn);
                    jsn.Dispose(); jsn = null;
                }
                catch (Exception e)
                {
                    WriteError(e);
                }
            }
        }
        private void SaveOfferFilterRecord(HttpContext context)
        {
            try
            {
               
                int iOfferId = string.IsNullOrEmpty(context.Request.QueryString["OfferId"]) ? 0 : int.Parse(context.Request.QueryString["OfferId"]);
                string strLinkMessage = string.Empty;
                string errorMessage = "";
                int recordCount = BLL.Stock.FilterOfferCrossMatchReq(iOfferId, out errorMessage);
                context.Response.Write(recordCount +  "," + errorMessage);
            }
            catch (Exception ex)
            {
                WriteError(ex);
            }
        }
        private void DeleteOfferFilterRecord(HttpContext context)
        {
            try
            {

                string OfferId = string.IsNullOrEmpty(context.Request.QueryString["OfferId"]) ? "0" : Convert.ToString((context.Request.QueryString["OfferId"]));
                string strLinkMessage = string.Empty;
                string errorMessage = "";
                int recordCount = 0;
                string[] arryval = OfferId.Split(',');//split values with ‘,’  
                int j = arryval.Length;
                int i = 0;
                for (i = 0; i < j; i++)
                {
                    recordCount = BLL.Stock.DeleteFilterOfferCrossMatchReq(Convert.ToInt32(arryval[i]), out errorMessage);
                    recordCount++;
                
                }
                context.Response.Write(recordCount +  "," + errorMessage);
            }
            catch (Exception ex)
            {
                WriteError(ex);
            }
        }
        private void ResetSessionStateLog(HttpContext context)
        {
            try
            {
                string errorMessage = "";
                int BOMId = string.IsNullOrEmpty(context.Request.QueryString["BOMId"]) ? 0 : int.Parse(context.Request.QueryString["BOMId"]);
                int recordCount = BLL.Stock.DeleteAutoLogCrossMatchReq(BOMId,SessionManager.LoginID ?? 0, out errorMessage);
                context.Response.Write(recordCount +  "," + errorMessage);
            }
            catch (Exception ex)
            {
                WriteError(ex);
            }
        }
        
        //Offer start
        public void GetData_Offers(HttpContext context)
        {
            try
            {
                System.Int32? TotalPageingRecord = 0;
                System.Int32? displayLength = Convert.ToInt32(GetQueryStringValue_NullableInt("NoOfTopRecord", 0)); //int.Parse(context.Request.Params["Length"]);//Convert.ToInt32(GetQueryStringValue_NullableInt("NoOfTopRecord", 0)); 
                System.Int32? displayStart = int.Parse(context.Request.Params["Start"]);
                int sortCol = 0;//int.Parse(context.Request.Params["order[0][column]"]);
                System.String sortDir = "asc";//context.Request.Params["order[0][dir]"];
                //string search = context.Request.Params["search[value]"];
                System.Int32? BomID = Convert.ToInt32(GetQueryStringValue_NullableInt("BOMId", 0));
                System.Int32? ReqID = Convert.ToInt32(GetQueryStringValue_NullableInt("customerRequirementNo", 0));
                List<Offer> lst = Offer.CrossMatch(SessionManager.ClientID, 
                    displayStart, 
                    displayLength, 
                    sortCol, 
                    sortDir,
                    GetQueryStringValue_String("PartSearch"),
                    GetQueryStringValue_String("PartMatch"),
                    GetQueryStringValue_String("sMonth"),
                    GetQueryStringValue_NullableInt("dtTime", null,true),
                    GetQueryStringValue_NullableInt("VendorType", null,true),
                    GetQueryStringValue_NullableInt("Currency", null,true),
                    GetQueryStringValue_NullableBoolean("Manufacture", true),
                    0
                    , IsServerLocal, SessionManager.IsPOHub, BomID,
                    GetQueryStringValue_NullableBoolean("IncludeAltPart", true),
                    ReqID
                    );
                
               
                JsonObject jsn = new JsonObject();
                JsonObject jsnItems = new JsonObject(true);
               
               // jsn.AddVariableNew(@"""OutDate""", Functions.FormatDate(OutDate));
                if (GetFormValue_Int("Index") == 1)
                    jsn.AddVariableNew("OutRefereshDate", Functions.FormatDate((OutDate.HasValue) ? OutDate.Value.AddMonths(6) : OutDate));
                foreach (Offer of in lst)
                {
                    JsonObject jsnItem = new JsonObject();
                    jsnItem.AddVariableNew("RowNum", of.RowNum);
                    jsnItem.AddVariableNew("TotalCount", of.TotalCount);
                    TotalPageingRecord = of.TotalCount;
                    if (of.ClientNo == SessionManager.ClientID)
                    {
                        //jsnItem.AddVariableNew("Price", Functions.FormatCurrency(of.Price, of.CurrencyCode));
                        jsnItem.AddVariableNew("SplrID", of.SupplierNo);
                        jsnItem.AddVariableNew("Splr", of.SupplierName);
                        jsnItem.AddVariableNew("By", (String.IsNullOrEmpty(of.SalesmanName)) ? of.OfferStatusChangeEmployeeName : of.SalesmanName);
                        jsnItem.AddVariableNew("MfrNo", of.ManufacturerNo);
                        jsnItem.AddVariableNew("ClientCode", "");
                    }
                    else
                    {
                        jsnItem.AddVariableNew("SplrID", "");
                        jsnItem.AddVariableNew("MfrNo", "");
                        //[001] code start
                        if ((bool)of.ClientDataVisibleToOthers || SessionManager.IsPOHub == true)
                        {
                            jsnItem.AddVariableNew("Splr", of.SupplierName);
                            jsnItem.AddVariableNew("By", (String.IsNullOrEmpty(of.SalesmanName)) ? of.OfferStatusChangeEmployeeName : of.SalesmanName);
                            //jsnItem.AddVariableNew("Price", Functions.FormatCurrency(of.Price, of.CurrencyCode));
                            jsnItem.AddVariableNew("ClientCode", " (" + of.ClientCode + ")");
                        }
                        else
                        {
                            jsnItem.AddVariableNew("Splr", of.ClientName);
                            jsnItem.AddVariableNew("By", "");
                            //jsnItem.AddVariableNew("Price", "");
                            jsnItem.AddVariableNew("ClientCode", "");
                        }
                        //[001] code end
                    }
                    jsnItem.AddVariableNew("Price", Functions.FormatCurrency(of.Price, of.CurrencyCode));
                    jsnItem.AddVariableNew("ID", of.OfferId);
                    jsnItem.AddVariableNew("Qty", of.Quantity);
                    jsnItem.AddVariableNew("PartNo", of.Part);
                    jsnItem.AddVariableNew("ROHS", of.ROHS);
                    jsnItem.AddVariableNew("Mfr", of.ManufacturerCode);
                    jsnItem.AddVariableNew("Product", of.ProductName);
                    jsnItem.AddVariableNew("Package", of.PackageName);
                    jsnItem.AddVariableNew("DC", of.DateCode);
                    jsnItem.AddVariableNew("SplrEmail", of.SupplierEmail);
                    jsnItem.AddVariableNew("Date", Functions.FormatDate(of.OfferStatusChangeDate));
                    jsnItem.AddVariableNew("EntrDate", Functions.FormatDate(of.OriginalEntryDate));
                    //if (of.CurrencyNo != ClientCurrencyNo) jsnItem.AddVariableNew("PriceInBase", Functions.FormatCurrency(BLL.Currency.ConvertValueToBaseCurrency(of.Price, (int)of.CurrencyNo, DateTime.Now), ClientCurrencyCode));
                    string strStatus = "";
                    if (of.OfferStatusNo != null) strStatus = Functions.GetGlobalResource("OfferStatus", (OfferStatus.List)of.OfferStatusNo);
                    jsnItem.AddVariableNew("Status", strStatus);
                    jsnItem.AddVariableNew("Notes", Functions.ReplaceLineBreaks(of.Notes));
                    jsnItem.AddVariableNew("CurrentClient", (SessionManager.ClientID == of.ClientNo));
                    //[001] code start
                    jsnItem.AddVariableNew("SupplierType", of.SupplierType);
                    
                    jsnItem.AddVariableNew("MSL", of.MSL);
                    jsnItem.AddVariableNew("SPQ", of.SPQ);
                    jsnItem.AddVariableNew("LeadTime", of.LeadTime);
                    jsnItem.AddVariableNew("RoHSStatus", of.RoHSStatus);
                    jsnItem.AddVariableNew("FactorySealed", of.FactorySealed);
                    jsnItem.AddVariableNew("blnIPO", of.IPOBOMNo > 0);
                    jsnItem.AddVariableNew("SupplierTotalQSA", of.SupplierTotalQSA);
                    jsnItem.AddVariableNew("SupplierLTB", of.SupplierLTB);
                    jsnItem.AddVariableNew("SupplierMOQ", of.SupplierMOQ);
                    jsnItem.AddVariableNew("IsSourcingHub", of.IsSourcingHub);
                    jsnItem.AddVariableNew("isIncludeAltPart", of.isIncludeAltPart);

                    
                    //[001] code end
                    jsnItems.AddVariableNew(jsnItem);
                }
                jsn.AddVariableNew("iTotalRecords", TotalPageingRecord);
                jsn.AddVariableNew("iTotalDisplayRecords", TotalPageingRecord);
                jsn.AddVariableNew("data", jsnItems);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
                
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }
        public void AddSourcingResultFromOffer(HttpContext context)
        {
            try
            {
                string OfferId = string.IsNullOrEmpty(context.Request.QueryString["OfferId"]) ? "0" : Convert.ToString((context.Request.QueryString["OfferId"]));
                int customerRequirementID = string.IsNullOrEmpty(context.Request.QueryString["customerRequirementNo"]) ? 0 : int.Parse(context.Request.QueryString["customerRequirementNo"]);

                bool bln = true;
                string strLinkMessage = string.Empty;
                string[] arryval = OfferId.Split(',');//split values with ‘,’  
                int j = arryval.Length;
                int i = 0;
                for (i = 0; i < j; i++)
                {

                    if (SourcingResult.InsertFromOfferCrossMatch(
                            customerRequirementID
                            , Convert.ToInt32(arryval[i])
                            , SessionManager.LoginID
                            , Convert.ToBoolean(SessionManager.IsPOHub)
                            , out strLinkMessage
                        ) < 1) bln = false;
                    if (!string.IsNullOrEmpty(strLinkMessage))
                        break;


                }
               
                JsonObject jsn = new JsonObject();
                jsn.AddVariableNew("Result", bln);
                jsn.AddVariableNew("Msg", strLinkMessage);
                //jsn.AddVariableNew("Result", bln);
                //jsn.AddVariableNew("Msg", "Cannot use USD currency for the 114 client. Kindly contact administrator.");
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            catch
            {
                WriteErrorSQLActionFailed("Insert");
            }
        }
        public void DeleteOffer_Records(HttpContext context)
        {
            try
            {
               
                int BomId = string.IsNullOrEmpty(context.Request.QueryString["BOMId"]) ? 0 : int.Parse(context.Request.QueryString["BOMId"]);
                string OfferId = string.IsNullOrEmpty(context.Request.QueryString["OfferId"]) ? "0" : Convert.ToString((context.Request.QueryString["OfferId"]));
                Stock.Delete_OfferRecord(OfferId, BomId, SessionManager.LoginID ?? 0, SessionManager.ClientID ?? 0, "OfferPH");
                
                
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }
        public void GetOfferItem(HttpContext context)
        {
            try
            {
                int OfferId = string.IsNullOrEmpty(context.Request.QueryString["OfferId"]) ? 0 : int.Parse(context.Request.QueryString["OfferId"]);
               
                Offer of = Offer.Get(OfferId, SessionManager.IsPOHub);
                JsonObject jsn = new JsonObject();
                jsn.AddVariableNew("Part", of.Part);
                jsn.AddVariableNew("ROHS", of.ROHS);
                jsn.AddVariableNew("Mfr", of.ManufacturerName);
                jsn.AddVariableNew("MfrNo", of.ManufacturerNo);
                jsn.AddVariableNew("DC", of.DateCode);
                jsn.AddVariableNew("Product", of.ProductNo);
                jsn.AddVariableNew("Package", of.PackageNo);
                jsn.AddVariableNew("OfferStatus", of.OfferStatusNo);
                jsn.AddVariableNew("Qty", of.Quantity);
                jsn.AddVariableNew("Price", Functions.FormatCurrency(of.Price));
                jsn.AddVariableNew("Currency", of.CurrencyNo);
                jsn.AddVariableNew("SupplierNo", of.SupplierNo);
                jsn.AddVariableNew("Supplier", of.SupplierName);
                jsn.AddVariableNew("Notes", Functions.ReplaceLineBreaks(of.Notes));
                jsn.AddVariableNew("MSL", of.MSL);
                jsn.AddVariableNew("SPQ", of.SPQ);
                jsn.AddVariableNew("LeadTime", of.LeadTime);
                jsn.AddVariableNew("RoHSStatus", of.RoHSStatus);
                jsn.AddVariableNew("FactorySealed", of.FactorySealed);
                jsn.AddVariableNew("blnIPO", of.IPOBOMNo > 0);
                jsn.AddVariableNew("SupplierTotalQSA", of.SupplierTotalQSA);
                jsn.AddVariableNew("SupplierLTB", of.SupplierLTB);
                jsn.AddVariableNew("SupplierMOQ", of.SupplierMOQ);
                jsn.AddVariableNew("ProductDescription", of.ProductDescription);
                if (!string.IsNullOrEmpty(of.ProductDescription) && of.ProductDescription.Length > 50)
                    jsn.AddVariableNew("ProductName", of.ProductDescription.Substring(0, 50));
                else
                    jsn.AddVariableNew("ProductName", of.ProductDescription);
                jsn.AddVariableNew("ProdInc", Convert.ToBoolean(of.ProductInactive));
                jsn.AddVariableNew("MSLLevelNo", of.MSLLevelNo);
                jsn.AddVariableNew("productNameDescrip", of.productNameDescrip);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }
        public void AddOfferAttachToSourcing(HttpContext context)
        {
            try
            {
                int iOfferId = string.IsNullOrEmpty(context.Request.QueryString["OfferId"]) ? 0 : int.Parse(context.Request.QueryString["OfferId"]);
                string strLinkMessage = string.Empty;
                int offerID = Offer.CloneOfferCrossMatchReq(
                    iOfferId
                    
                    , GetQueryStringValue_String("PartNo")
                    , GetQueryStringValue_NullableInt("ManufacturerNo")
                    , GetQueryStringValue_String("DateCode")
                    , GetQueryStringValue_NullableInt("ProductNo")
                    , GetQueryStringValue_NullableInt("PackageNo")
                    , GetFormValueQuery_Int("Quantity")
                    , GetFormValueQuery_Double("Price")
                    , GetQueryStringValue_NullableInt("Currency")
                    , DateTime.Now
                    , SessionManager.LoginID//null modified by Prakash on 16 6 2016
                    , GetQueryStringValue_NullableInt("Supplier")
                    , string.Empty
                    , GetFormValueQyery_NullableByte("ROHS")
                    
                    
                    , GetQueryStringValue_NullableInt("OfferStatus")
                    , GetQueryStringValue_String("Notes")
                    , LoginID
                    , SessionManager.ClientID
                    , SessionManager.IsPOHub
                    , GetQueryStringValue_String("SupplierTotalQSA")
                    , GetQueryStringValue_String("SupplierMOQ")
                    , GetQueryStringValue_String("SupplierLTB")
                    , GetQueryStringValue_String("MSL")
                    , GetQueryStringValue_String("SPQ")
                    , GetQueryStringValue_String("LeadTime")
                    , GetQueryStringValue_String("FactorySealed")
                    , GetQueryStringValue_String("ROHSStatus")
                    , GetQueryStringValue_NullableInt("CustReqNo")
                    , GetQueryStringValue_NullableInt("MSLNo")
                    , out strLinkMessage
                );
                JsonObject jsn = new JsonObject();
                jsn.AddVariableNew("Result", (offerID > 0));
                jsn.AddVariableNew("Msg", strLinkMessage);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            catch
            {
                WriteErrorSQLActionFailed("Insert");
            }
        }
        public void EditOffer(HttpContext context)
        {
            try
            {
                int OfferId = string.IsNullOrEmpty(context.Request.QueryString["OfferId"]) ? 0 : int.Parse(context.Request.QueryString["OfferId"]);
                bool blnOK = Offer.IPOBOMUpdate(
                    OfferId
                    , GetQueryStringValue_String("PartNo")
                    , GetQueryStringValue_NullableInt("ManufacturerNo")
                    , GetQueryStringValue_String("DateCode")
                    , GetQueryStringValue_NullableInt("ProductNo")
                    , GetQueryStringValue_NullableInt("PackageNo")
                     , GetFormValueQuery_Int("Quantity")
                    , GetFormValueQuery_Double("Price")
                    , GetQueryStringValue_NullableInt("Currency")
                    , LoginID
                    , GetQueryStringValue_NullableInt("OfferStatus")
                    , GetQueryStringValue_NullableInt("Supplier")
                    , GetFormValueQyery_NullableByte("ROHS")
                    , GetQueryStringValue_String("Notes")
                    , LoginID
                    , SessionManager.IsPOHub
                    , GetQueryStringValue_String("SupplierTotalQSA")
                    , GetQueryStringValue_String("SupplierMOQ")
                    , GetQueryStringValue_String("SupplierLTB")
                    , GetQueryStringValue_String("MSL")
                    , GetQueryStringValue_String("SPQ")
                    , GetQueryStringValue_String("LeadTime")
                    , GetQueryStringValue_String("FactorySealed")
                    , GetQueryStringValue_String("RoHSStatus")
                    , GetQueryStringValue_NullableInt("MSLNo")
                );
                JsonObject jsn = new JsonObject();
                jsn.AddVariable("Result", blnOK);
                jsn.AddVariable("CurrentDate", Functions.FormatDate(Functions.GetUKLocalTime()));
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            catch
            {
                WriteErrorSQLActionFailed("Update");
            }
        }
        //Offer end

        //trusted start
        public void GetData_Trusted(HttpContext context)
        {
            try
            {

                System.Int32? TotalPageingRecord = 0;
                int displayLength = Convert.ToInt32(GetQueryStringValue_NullableInt("NoOfTopRecord", 0));
                int displayStart = int.Parse(context.Request.Params["Start"]);
                int sortCol = 0;//int.Parse(context.Request.Params["order[0][column]"]);
                System.String sortDir = "asc";//context.Request.Params["order[0][dir]"];
                System.Int32? BomID = Convert.ToInt32(GetQueryStringValue_NullableInt("BOMId", 0));
                System.Int32? ReqID = Convert.ToInt32(GetQueryStringValue_NullableInt("customerRequirementNo", 0));
                //string search = context.Request.Params["search[value]"];
                List<Excess> lst = Excess.IPOBOMSourceTrusted(SessionManager.ClientID,
                    displayStart,
                    displayLength,
                    sortCol,
                    sortDir,
                    GetQueryStringValue_String("PartSearch"),
                    GetQueryStringValue_String("PartMatch"),
                    GetQueryStringValue_String("sMonth"),
                    GetQueryStringValue_NullableInt("dtTime", null, true),
                    GetQueryStringValue_NullableInt("VendorType", null, true),
                    GetQueryStringValue_NullableInt("Currency", null, true),
                     GetQueryStringValue_NullableBoolean("Manufacture", true),
                       0
                    , IsServerLocal, SessionManager.IsPOHub, BomID,
                    GetQueryStringValue_NullableBoolean("IncludeAltPart", true),
                    ReqID
                    );
                JsonObject jsn = new JsonObject();
                JsonObject jsnItems = new JsonObject(true);
                if (GetFormValue_Int("Index") == 1)
                    jsn.AddVariableNew("OutRefereshDate", Functions.FormatDate((OutDate.HasValue) ? OutDate.Value.AddMonths(6) : OutDate));
                foreach (Excess ex in lst)
                {
                    JsonObject jsnItem = new JsonObject();
                    jsnItem.AddVariableNew("RowNum", ex.RowNum);
                    jsnItem.AddVariableNew("TotalCount", ex.TotalCount);
                    TotalPageingRecord = ex.TotalCount;
                    if (ex.ClientNo == SessionManager.ClientID)
                    {
                        jsnItem.AddVariableNew("SplrID", ex.CompanyNo);
                        jsnItem.AddVariableNew("Splr", ex.SupplierName);
                        jsnItem.AddVariableNew("By", (String.IsNullOrEmpty(ex.SalesmanName)) ? ex.OfferStatusChangeEmployeeName : ex.SalesmanName);
                        jsnItem.AddVariableNew("MfrNo", ex.ManufacturerNo);
                        jsnItem.AddVariableNew("ClientCode", "");
                    }
                    else
                    {
                        jsnItem.AddVariableNew("SplrID", "");
                        jsnItem.AddVariableNew("MfrNo", "");
                        //[001] code start
                        if ((bool)ex.ClientDataVisibleToOthers || SessionManager.IsPOHub == true)
                        {
                            jsnItem.AddVariableNew("Splr", ex.SupplierName);
                            jsnItem.AddVariableNew("By", (String.IsNullOrEmpty(ex.SalesmanName)) ? ex.OfferStatusChangeEmployeeName : ex.SalesmanName);
                            jsnItem.AddVariableNew("ClientCode", " (" + ex.ClientCode + ")");
                        }
                        else
                        {
                            jsnItem.AddVariableNew("Splr", ex.ClientName);
                            jsnItem.AddVariableNew("By", "");
                            jsnItem.AddVariableNew("ClientCode", "");
                        }
                        //[001] code end
                    }
                    jsnItem.AddVariableNew("Price", Functions.FormatCurrency(ex.Price, ex.CurrencyCode));
                    jsnItem.AddVariableNew("ID", ex.ExcessId);
                    jsnItem.AddVariableNew("Qty", ex.Quantity);
                    jsnItem.AddVariableNew("PartNo", ex.Part);
                    jsnItem.AddVariableNew("ROHS", ex.ROHS);
                    jsnItem.AddVariableNew("Mfr", ex.ManufacturerCode);
                    jsnItem.AddVariableNew("Product", ex.ProductName);
                    jsnItem.AddVariableNew("DC", ex.DateCode);
                    jsnItem.AddVariableNew("SplrEmail", ex.SupplierEmail);
                    jsnItem.AddVariableNew("Date", Functions.FormatDate(ex.OfferStatusChangeDate));
                    jsnItem.AddVariableNew("EntrDate", Functions.FormatDate(ex.OriginalEntryDate));
                    //if (ex.CurrencyNo != ClientCurrencyNo) jsnItem.AddVariable("PriceInBase", Functions.FormatCurrency(BLL.Currency.ConvertValueToBaseCurrency(ex.Price, (int)ex.CurrencyNo, DateTime.Now), ClientCurrencyCode));
                    jsnItem.AddVariableNew("Package", ex.PackageName);
                    string strStatus = "";
                    if (ex.OfferStatusNo != null) strStatus = Functions.GetGlobalResource("OfferStatus", (OfferStatus.List)ex.OfferStatusNo);
                    jsnItem.AddVariableNew("Status", strStatus);
                    jsnItem.AddVariableNew("Notes", Functions.ReplaceLineBreaks(ex.Notes));
                    jsnItem.AddVariableNew("CurrentClient", (SessionManager.ClientID == ex.ClientNo));
                    //[001] code start
                    jsnItem.AddVariableNew("SupplierType", ex.SupplierType);
                    jsnItem.AddVariableNew("MSL", ex.MSL);
                    jsnItem.AddVariableNew("SPQ", ex.SPQ);
                    jsnItem.AddVariableNew("LeadTime", ex.LeadTime);
                    jsnItem.AddVariableNew("RoHSStatus", ex.RoHSStatus);
                    jsnItem.AddVariableNew("FactorySealed", ex.FactorySealed);
                    //jsnItem.AddVariable("blnIPO", of.IPOBOMNo > 0);
                    jsnItem.AddVariableNew("SupplierTotalQSA", ex.SupplierTotalQSA);
                    jsnItem.AddVariableNew("SupplierLTB", ex.SupplierLTB);
                    jsnItem.AddVariableNew("SupplierMOQ", ex.SupplierMOQ);
                    jsnItem.AddVariableNew("isIncludeAltPart", ex.isIncludeAltPart);
                    //[001] code end
                    jsnItems.AddVariableNew(jsnItem);
                }
                jsn.AddVariableNew("iTotalRecords", TotalPageingRecord);
                jsn.AddVariableNew("iTotalDisplayRecords", TotalPageingRecord);
                jsn.AddVariableNew("data", jsnItems);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }
        //for add trusted
        public void AddSourcingResultFromTrusted(HttpContext context)
        {
            try
            {

                string TrustedId = string.IsNullOrEmpty(context.Request.QueryString["TrustedId"]) ? "0" : Convert.ToString((context.Request.QueryString["TrustedId"]));
                int customerRequirementID = string.IsNullOrEmpty(context.Request.QueryString["customerRequirementNo"]) ? 0 : int.Parse(context.Request.QueryString["customerRequirementNo"]);

                bool bln = true;
                string strLinkMessage = string.Empty;
                string[] arryval = TrustedId.Split(',');//split values with ‘,’  
                int j = arryval.Length;
                int i = 0;
                for (i = 0; i < j; i++)
                {
                    if (SourcingResult.InsertFromTrustedCrossMatch(
                            customerRequirementID
                             , Convert.ToInt32(arryval[i])
                            , SessionManager.LoginID
                            , Convert.ToBoolean(SessionManager.IsPOHub)
                            , out strLinkMessage
                        ) < 1) bln = false;
                    if (!string.IsNullOrEmpty(strLinkMessage))
                        break;
                }
                JsonObject jsn = new JsonObject();
                jsn.AddVariableNew("Result", bln);
                jsn.AddVariableNew("Msg", strLinkMessage);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            catch
            {
                WriteErrorSQLActionFailed("Insert");
            }
        }
        //clone
        public void GetTrustedItem(HttpContext context)
        {
            try
            {
                int TrustedId = string.IsNullOrEmpty(context.Request.QueryString["TrustedId"]) ? 0 : int.Parse(context.Request.QueryString["TrustedId"]);
                Excess ex = Excess.Get(TrustedId, SessionManager.IsPOHub);
                JsonObject jsn = new JsonObject();
                jsn.AddVariableNew("Name", ex.ExcessName);
                jsn.AddVariableNew("Part", ex.Part);
                jsn.AddVariableNew("ROHS", ex.ROHS);
                jsn.AddVariableNew("Mfr", ex.ManufacturerName);
                jsn.AddVariableNew("MfrNo", ex.ManufacturerNo);
                jsn.AddVariableNew("DC", ex.DateCode);
                jsn.AddVariableNew("OfferStatus", ex.OfferStatusNo);
                jsn.AddVariableNew("Product", ex.ProductNo);
                jsn.AddVariableNew("Package", ex.PackageNo);
                jsn.AddVariableNew("Qty", ex.Quantity);
                jsn.AddVariableNew("Price", Functions.FormatCurrency(ex.Price));
                jsn.AddVariableNew("Currency", ex.CurrencyNo);
                jsn.AddVariableNew("SupplierNo", ex.CompanyNo);
                jsn.AddVariableNew("Supplier", ex.CompanyName);
                jsn.AddVariableNew("Notes", Functions.ReplaceLineBreaks(ex.Notes));
                jsn.AddVariableNew("MSL", ex.MSL);
                jsn.AddVariableNew("SPQ", ex.SPQ);
                jsn.AddVariableNew("LeadTime", ex.LeadTime);
                jsn.AddVariableNew("RoHSStatus", ex.RoHSStatus);
                jsn.AddVariableNew("FactorySealed", ex.FactorySealed);
                //jsn.AddVariable("blnIPO", ex.IPOBOMNo > 0);
                jsn.AddVariableNew("SupplierTotalQSA", ex.SupplierTotalQSA);
                jsn.AddVariableNew("SupplierLTB", ex.SupplierLTB);
                jsn.AddVariableNew("SupplierMOQ", ex.SupplierMOQ);
                jsn.AddVariableNew("ProductDescription", ex.ProductDescription);
                if (!string.IsNullOrEmpty(ex.ProductDescription) && ex.ProductDescription.Length > 50)
                    jsn.AddVariableNew("ProductName", ex.ProductDescription.Substring(0, 50));
                else
                    jsn.AddVariableNew("ProductName", ex.ProductDescription);
                jsn.AddVariableNew("ProdInc", Convert.ToBoolean(ex.ProductInactive));
                jsn.AddVariableNew("MSLLevelNo", ex.MSLLevelNo);
                jsn.AddVariableNew("productNameDescrip", ex.productNameDescrip);
                
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }
        public void EditTrusted(HttpContext context)
        {
            try
            {
                int TrustedId = string.IsNullOrEmpty(context.Request.QueryString["TrustedId"]) ? 0 : int.Parse(context.Request.QueryString["TrustedId"]);
                bool blnOK = Excess.IPOBOMUpdate(
                    TrustedId
                    , GetQueryStringValue_String("PartNo")
                    , GetQueryStringValue_NullableInt("ManufacturerNo")
                    , GetQueryStringValue_String("DateCode")
                    , GetQueryStringValue_NullableInt("ProductNo")
                    , GetQueryStringValue_NullableInt("PackageNo")
                    , GetFormValueQuery_Int("Quantity")
                    , GetFormValueQuery_Double("Price")
                    , GetQueryStringValue_NullableInt("Currency")
                    , GetQueryStringValue_NullableInt("OfferStatus")
                    , GetQueryStringValue_NullableInt("Supplier")
                    , GetFormValueQyery_NullableByte("ROHS")
                    , GetQueryStringValue_String("Notes")
                    , LoginID
                    , SessionManager.IsPOHub
                    , GetQueryStringValue_String("SupplierTotalQSA")
                    , GetQueryStringValue_String("SupplierMOQ")
                    , GetQueryStringValue_String("SupplierLTB")
                    , GetQueryStringValue_String("MSL")
                    , GetQueryStringValue_String("SPQ")
                    , GetQueryStringValue_String("LeadTime")
                    , GetQueryStringValue_String("FactorySealed")
                    , GetQueryStringValue_String("RoHSStatus")
                    , GetQueryStringValue_NullableInt("MSLNo")
                    
                    
                );
                JsonObject jsn = new JsonObject();
                jsn.AddVariable("Result", blnOK);
                jsn.AddVariable("CurrentDate", Functions.FormatDate(Functions.GetUKLocalTime()));
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            catch
            {
                WriteErrorSQLActionFailed("Update");
            }
        }
        public void AddTrustedAttachToSourcing(HttpContext context)
        {
            try
            {
                int TrustedId = string.IsNullOrEmpty(context.Request.QueryString["TrustedId"]) ? 0 : int.Parse(context.Request.QueryString["TrustedId"]);
                string strLinkMessage = string.Empty;
                int TrustedID = Excess.CloneTrustedCrossMatchReq(
                    TrustedId
                    , GetQueryStringValue_String("PartNo")
                    , GetQueryStringValue_NullableInt("ManufacturerNo")
                    , GetQueryStringValue_String("DateCode")
                    , GetQueryStringValue_NullableInt("ProductNo")
                    , GetQueryStringValue_NullableInt("PackageNo")
                    , GetFormValueQuery_Int("Quantity")
                    , GetFormValueQuery_Double("Price")
                    , GetQueryStringValue_NullableInt("Currency")
                    , DateTime.Now
                    , SessionManager.LoginID//null modified by Prakash on 16 6 2016
                    , GetQueryStringValue_NullableInt("Supplier")
                    , string.Empty
                    , GetFormValueQyery_NullableByte("ROHS")
                    , GetQueryStringValue_String("Notes")
                    , LoginID
                    , SessionManager.ClientID
                    , GetQueryStringValue_NullableInt("OfferStatus")
                    , SessionManager.IsPOHub
                    , GetQueryStringValue_String("SupplierTotalQSA")
                    , GetQueryStringValue_String("SupplierMOQ")
                    , GetQueryStringValue_String("SupplierLTB")

                    , GetQueryStringValue_String("MSL")
                    , GetQueryStringValue_String("SPQ")
                    , GetQueryStringValue_String("LeadTime")
                    , GetQueryStringValue_String("FactorySealed")
                    , GetQueryStringValue_String("ROHSStatus")
                    , GetQueryStringValue_NullableInt("CustReqNo")
                    , GetQueryStringValue_NullableInt("MSLNo")
                     , out strLinkMessage
                );
                JsonObject jsn = new JsonObject();
                jsn.AddVariableNew("Result", (TrustedID > 0));
                jsn.AddVariableNew("Msg", strLinkMessage);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            catch
            {
                WriteErrorSQLActionFailed("Insert");
            }
        }
        public void DeleteTrusted_Records(HttpContext context)
        {
            try
            {
                
                int BomId = string.IsNullOrEmpty(context.Request.QueryString["BOMId"]) ? 0 : int.Parse(context.Request.QueryString["BOMId"]);
                string TrustedId = string.IsNullOrEmpty(context.Request.QueryString["TrustedId"]) ? "0" : Convert.ToString((context.Request.QueryString["TrustedId"]));
                Stock.Delete_TrustedRecord(TrustedId, BomId, SessionManager.LoginID ?? 0, SessionManager.ClientID ?? 0,"TrustedPH");
              

            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }
        //trusted end
       
        //Price Request start
        public void GetData_POQuotes(HttpContext context)
        {
            try
            {
                System.Int32? TotalPageingRecord = 0;
                 int displayLength = Convert.ToInt32(GetQueryStringValue_NullableInt("NoOfTopRecord", 0));
                 int displayStart = int.Parse(context.Request.Params["Start"]);
                 int sortCol = 0;//int.Parse(context.Request.Params["order[0][column]"]);
                 System.String sortDir = "asc";//context.Request.Params["order[0][dir]"];
                System.Int32? BomID = Convert.ToInt32(GetQueryStringValue_NullableInt("BOMId", 0));
                System.Int32? ReqID = Convert.ToInt32(GetQueryStringValue_NullableInt("customerRequirementNo", 0));
                //string search = context.Request.Params["search[value]"];
                List<PurchaseQuoteLine> lst = PurchaseQuoteLine.SourcePriceRequest(SessionManager.ClientID,
                    displayStart,
                    displayLength,
                    sortCol,
                    sortDir,
                    GetQueryStringValue_String("PartSearch"),
                    GetQueryStringValue_String("PartMatch"),
                    GetQueryStringValue_String("sMonth"),
                    GetQueryStringValue_NullableInt("dtTime", null, true),
                    GetQueryStringValue_NullableInt("VendorType", null, true),
                    GetQueryStringValue_NullableInt("Currency", null, true),
                    GetQueryStringValue_NullableBoolean("Manufacture", true),
                       0
                    , IsServerLocal, SessionManager.IsPOHub, BomID,
                    GetQueryStringValue_NullableBoolean("IncludeAltPart", true),
                    ReqID
                    );
                

                JsonObject jsn = new JsonObject();
                JsonObject jsnItems = new JsonObject(true);
                //jsn.AddVariableNew("iTotalRecords", lst.Count);
                //jsn.AddVariableNew("iTotalDisplayRecords", lst.Count);
                //jsn.AddVariable("Count", lst.Count);
                //jsn.AddVariable("OutDate", Functions.FormatDate(OutDate));
                //if (GetFormValue_Int("Index") == 1)
                //    jsn.AddVariable("OutRefereshDate", Functions.FormatDate((OutDate.HasValue) ? OutDate.Value.AddMonths(6) : OutDate));

                foreach (PurchaseQuoteLine ln in lst)
                {
                    JsonObject jsnItem = new JsonObject();
                    jsnItem.AddVariableNew("RowNum", ln.RowNum);
                    jsnItem.AddVariableNew("TotalCount", ln.TotalCount);
                    TotalPageingRecord = ln.TotalCount;
                    jsnItem.AddVariableNew("ID", ln.PurchaseRequestLineDetailId);
                    jsnItem.AddVariableNew("CustID", ln.CompanyNo);
                    jsnItem.AddVariableNew("Cust", ln.CompanyName);
                    //jsnItem.AddVariable("Price", Functions.FormatCurrency(ln.Price, ln.CurrencyCode));
                    jsnItem.AddVariableNew("QuoteID", ln.PurchaseQuoteNo);
                    jsnItem.AddVariableNew("QuoteNo", ln.PurchaseQuoteNumber);
                    //jsnItem.AddVariable("MfrNo", ln.ManufacturerNo);
                    jsnItem.AddVariableNew("SalesmanNo", ln.Salesman);
                    jsnItem.AddVariableNew("SalesmanName", ln.SalesmanName);

                    //if (IsServerLocal && ln.ClientNo == SessionManager.ClientID)
                    //{
                    //    jsnItem.AddVariable("ID", ln.PurchaseQuoteLineId);
                    //    jsnItem.AddVariable("CustID", ln.CompanyNo);
                    //    jsnItem.AddVariable("Cust", ln.CompanyName);
                    //    //jsnItem.AddVariable("Price", Functions.FormatCurrency(ln.Price, ln.CurrencyCode));
                    //    jsnItem.AddVariable("QuoteID", ln.PurchaseQuoteNo);
                    //    jsnItem.AddVariable("QuoteNo", ln.PurchaseQuoteNumber);
                    //    //jsnItem.AddVariable("MfrNo", ln.ManufacturerNo);
                    //    jsnItem.AddVariable("SalesmanNo", ln.Salesman);
                    //    jsnItem.AddVariable("SalesmanName", ln.SalesmanName);
                    //}
                    //else
                    //{
                    //    jsnItem.AddVariable("ID", 0);
                    //    jsnItem.AddVariable("QuoteID", 0);
                    //    jsnItem.AddVariable("QuoteNo", ln.PurchaseQuoteNo);
                    //    jsnItem.AddVariable("CustID", 0);
                    //    jsnItem.AddVariable("Cust", ln.ClientName);
                    //    //jsnItem.AddVariable("Price", "");
                    //    jsnItem.AddVariable("MfrNo", "");
                    //    jsnItem.AddVariable("SalesmanNo", 0);
                    //    jsnItem.AddVariable("SalesmanName", ln.SalesmanName);
                    //}
                    jsnItem.AddVariableNew("Price", Functions.FormatCurrency(ln.UnitPrice, ln.CurrencyCode));
                    jsnItem.AddVariableNew("Qty", ln.Quantity);
                    jsnItem.AddVariableNew("PartNo", ln.Part);
                    jsnItem.AddVariableNew("ROHS", "");
                    jsnItem.AddVariableNew("Mfr", "");
                    jsnItem.AddVariableNew("Date", Functions.FormatDate(ln.DatePOQuoted));
                    jsnItem.AddVariableNew("Pack","");
                    jsnItem.AddVariableNew("Prod", "");
                    jsnItem.AddVariableNew("CustP", "");
                    jsnItem.AddVariableNew("DC", "");
                    jsnItem.AddVariableNew("CurrentClient", (SessionManager.ClientID == ln.ClientNo));
                    jsnItem.AddVariableNew("MSL", ln.MSL);
                    jsnItem.AddVariableNew("SPQ", ln.SPQ);
                    jsnItem.AddVariableNew("LeadTime", ln.LeadTime);
                    jsnItem.AddVariableNew("RoHSStatus", ln.RoHSStatus);
                    jsnItem.AddVariableNew("FactorySealed", ln.FactorySealed);
                    jsnItem.AddVariableNew("SupplierType", ln.SupplierType);
                    jsnItem.AddVariableNew("isIncludeAltPart", ln.isIncludeAltPart);
                    jsnItems.AddVariableNew(jsnItem);
                }
                jsn.AddVariableNew("iTotalRecords", TotalPageingRecord);
                jsn.AddVariableNew("iTotalDisplayRecords", TotalPageingRecord);
                jsn.AddVariableNew("data", jsnItems);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;


            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }
        //for add add Price Request
        public void AddSourcingResultFromPOQuote(HttpContext context)
        {
            try
            {
                string POQuoteId = string.IsNullOrEmpty(context.Request.QueryString["PrequestId"]) ? "0" : Convert.ToString((context.Request.QueryString["PrequestId"]));
                int customerRequirementID = string.IsNullOrEmpty(context.Request.QueryString["customerRequirementNo"]) ? 0 : int.Parse(context.Request.QueryString["customerRequirementNo"]);

                bool bln = true;
                string strLinkMessage = string.Empty;
                string[] arryval = POQuoteId.Split(',');//split values with ‘,’  
                int j = arryval.Length;
                int i = 0;
                for (i = 0; i < j; i++)
                {
                    if (SourcingResult.InsertFromPOQuoteCrossMatch(
                            customerRequirementID
                            , Convert.ToInt32(arryval[i])
                            , SessionManager.LoginID
                            , out strLinkMessage
                        ) < 1) bln = false;
                }
                JsonObject jsn = new JsonObject();
                jsn.AddVariableNew("Result", bln);
                jsn.AddVariableNew("Msg", strLinkMessage);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            catch
            {
                WriteErrorSQLActionFailed("Insert");
            }
        }
        //Price Request end
        public void DeletePOQuotes_Records(HttpContext context)
        {
            try
            {
                
                int BomId = string.IsNullOrEmpty(context.Request.QueryString["BOMId"]) ? 0 : int.Parse(context.Request.QueryString["BOMId"]);
                string POQuotesId = string.IsNullOrEmpty(context.Request.QueryString["PrequestId"]) ? "0" : Convert.ToString((context.Request.QueryString["PrequestId"]));
                Stock.Delete_POQuotesRecord(POQuotesId, BomId, SessionManager.LoginID ?? 0, SessionManager.ClientID ?? 0,"POQuote");


            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }


        //get offer history
        public void GetData_History(HttpContext context)
        {
            try
            {
                //[usp_CrossMatch_HistoryPH]
                System.Int32? TotalPageingRecord = 0;
                int displayLength = Convert.ToInt32(GetQueryStringValue_NullableInt("NoOfTopRecord", 0));
                int displayStart = int.Parse(context.Request.Params["Start"]);
                int sortCol = 0;//int.Parse(context.Request.Params["order[0][column]"]);
                System.String sortDir = "asc";//context.Request.Params["order[0][dir]"];
                System.Int32? BomID = Convert.ToInt32(GetQueryStringValue_NullableInt("BOMId", 0));
                System.Int32? ReqID = Convert.ToInt32(GetQueryStringValue_NullableInt("customerRequirementNo", 0));
                List<History> lst = History.SourceHistory(SessionManager.ClientID,
                    displayStart,
                    displayLength,
                    sortCol,
                    sortDir,
                    GetQueryStringValue_String("PartSearch"),
                    GetQueryStringValue_String("PartMatch"),
                    GetQueryStringValue_String("sMonth"),
                     GetQueryStringValue_NullableInt("dtTime", null, true),
                    GetQueryStringValue_NullableInt("VendorType", null, true),
                    GetQueryStringValue_NullableInt("Currency", null, true),
                      GetQueryStringValue_NullableBoolean("Manufacture", true),
                      0
                    , IsServerLocal, SessionManager.IsPOHub, BomID,
                    GetQueryStringValue_NullableBoolean("IncludeAltPart", true),
                    ReqID
                    );
                
                
                    //List<History> lst = History.SourceHistory(SessionManager.ClientID, displayStart, displayLength, sortCol, sortDir, PartSearch, PartMatch, sMonth, dtTime, null, null, null, 0, true, SessionManager.IsPOHub);
                 JsonObject jsn = new JsonObject();
                JsonObject jsnItems = new JsonObject(true);
                //jsn.AddVariableNew("iTotalRecords", lst.Count);
                //jsn.AddVariableNew("iTotalDisplayRecords", lst.Count);
                //jsn.AddVariable("Count", lst.Count);
                //jsn.AddVariable("OutDate", Functions.FormatDate(OutDate));
                //if (GetFormValue_Int("Index") == 1)
                //    jsn.AddVariable("OutRefereshDate", Functions.FormatDate((OutDate.HasValue) ? OutDate.Value.AddMonths(6) : OutDate));
                foreach (History hs in lst)
                {
                    JsonObject jsnItem = new JsonObject();
                    jsnItem.AddVariableNew("RowNum", hs.RowNum);
                    jsnItem.AddVariableNew("TotalCount", hs.TotalCount);
                    TotalPageingRecord = hs.TotalCount;
                    if (hs.ClientNo == SessionManager.ClientID)
                    {
                        //jsnItem.AddVariable("Price", Functions.FormatCurrency(hs.Price, hs.CurrencyCode));
                        jsnItem.AddVariableNew("SplrID", hs.SupplierNo);
                        jsnItem.AddVariableNew("Splr", hs.SupplierName);
                        jsnItem.AddVariableNew("By", (String.IsNullOrEmpty(hs.SalesmanName)) ? hs.OfferStatusChangeEmployeeName : hs.SalesmanName);
                        jsnItem.AddVariableNew("MfrNo", hs.ManufacturerNo);
                        jsnItem.AddVariableNew("ClientCode", "");
                    }
                    else
                    {
                        //jsnItem.AddVariable("Price", "");
                        jsnItem.AddVariableNew("SplrID", "");
                        jsnItem.AddVariableNew("MfrNo", "");
                        //[001] code start
                        if ((bool)hs.ClientDataVisibleToOthers || SessionManager.IsPOHub == true)
                        {
                            jsnItem.AddVariableNew("Splr", hs.SupplierName);
                            jsnItem.AddVariableNew("By", (String.IsNullOrEmpty(hs.SalesmanName)) ? hs.OfferStatusChangeEmployeeName : hs.SalesmanName);
                            jsnItem.AddVariableNew("ClientCode", " (" + hs.ClientCode + ")");
                        }
                        else
                        {
                            jsnItem.AddVariableNew("Splr", hs.ClientName);
                            jsnItem.AddVariableNew("By", "");
                            jsnItem.AddVariableNew("ClientCode", "");
                        }
                        //[001] code end
                    }

                    jsnItem.AddVariableNew("Price", Functions.FormatCurrency(hs.Price, hs.CurrencyCode));
                    jsnItem.AddVariableNew("ID", hs.HistoryId);
                    jsnItem.AddVariableNew("Qty", hs.Quantity);
                    jsnItem.AddVariableNew("PartNo", hs.Part);
                    jsnItem.AddVariableNew("ROHS", hs.ROHS);
                    jsnItem.AddVariableNew("Mfr", hs.ManufacturerCode);
                    jsnItem.AddVariableNew("Product", hs.ProductName);
                    jsnItem.AddVariableNew("DC", hs.DateCode);
                    jsnItem.AddVariableNew("SplrEmail", hs.SupplierEmail);
                    jsnItem.AddVariableNew("Date", Functions.FormatDate(hs.OfferStatusChangeDate));
                    jsnItem.AddVariableNew("Package", hs.PackageName);
                    string strStatus = "";
                    if (hs.OfferStatusNo != null) strStatus = Functions.GetGlobalResource("OfferStatus", (OfferStatus.List)hs.OfferStatusNo);
                    jsnItem.AddVariableNew("Status", strStatus);
                    jsnItem.AddVariableNew("Notes", Functions.ReplaceLineBreaks(hs.Notes));
                    jsnItem.AddVariableNew("CurrentClient", (SessionManager.ClientID == hs.ClientNo));
                    //[001] code start
                    jsnItem.AddVariableNew("SupplierType", hs.SupplierType);
                    jsnItem.AddVariableNew("isIncludeAltPart", hs.isIncludeAltPart);
                    //[001] code end
                    jsnItems.AddVariableNew(jsnItem);
                }
                jsn.AddVariableNew("iTotalRecords", TotalPageingRecord);
                jsn.AddVariableNew("iTotalDisplayRecords", TotalPageingRecord);
                jsn.AddVariableNew("data", jsnItems);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }
        //end

        //get customer req
        public void GetData_CustomerRequirements(HttpContext context)
        {
            try
            {
               

                //[usp_CrossMatch_CustomerRequirement]
                System.Int32? TotalPageingRecord = 0;
                int displayLength = Convert.ToInt32(GetQueryStringValue_NullableInt("NoOfTopRecord", 0));
                int displayStart = int.Parse(context.Request.Params["Start"]);
                int sortCol = 0;//int.Parse(context.Request.Params["order[0][column]"]);
                System.String sortDir = "asc";//context.Request.Params["order[0][dir]"];
                    
                System.Int32? BomID = Convert.ToInt32(GetQueryStringValue_NullableInt("BOMId", 0));
                System.Int32? ReqID = Convert.ToInt32(GetQueryStringValue_NullableInt("customerRequirementNo", 0));
                //string search = context.Request.Params["search[value]"];
                List<CustomerRequirement> lst = CustomerRequirement.SourceCustomerRequirement(SessionManager.ClientID,
                    displayStart,
                    displayLength,
                    sortCol,
                    sortDir,
                    GetQueryStringValue_String("PartSearch"),
                    GetQueryStringValue_String("PartMatch"),
                    GetQueryStringValue_String("sMonth"),
                    GetQueryStringValue_NullableInt("dtTime", null, true),
                    GetQueryStringValue_NullableInt("VendorType", null, true),
                    GetQueryStringValue_NullableInt("Currency", null, true),
                    GetQueryStringValue_NullableBoolean("Manufacture", true),
                          0
                    , IsServerLocal, SessionManager.IsPOHub, BomID,
                    GetQueryStringValue_NullableBoolean("IncludeAltPart", true),
                    ReqID
                    );
                
                
                //List<CustomerRequirement> lst = CustomerRequirement.SourceCustomerRequirement(SessionManager.ClientID, displayStart, displayLength, sortCol, sortDir, PartSearch, PartMatch, sMonth, dtTime, null, null, null, 0, true, SessionManager.IsPOHub);
                JsonObject jsn = new JsonObject();
                JsonObject jsnItems = new JsonObject(true);
                //jsn.AddVariableNew("iTotalRecords", lst.Count);
                //jsn.AddVariableNew("iTotalDisplayRecords", lst.Count);
                //jsn.AddVariableNew("Count", lst.Count);
                //jsn.AddVariable("OutDate", Functions.FormatDate(OutDate));
                //if (GetFormValue_Int("Index") == 1)
                //    jsn.AddVariable("OutRefereshDate", Functions.FormatDate((OutDate.HasValue) ? OutDate.Value.AddMonths(6) : OutDate));
                foreach (CustomerRequirement cr in lst)
                {
                    JsonObject jsnItem = new JsonObject();
                    jsnItem.AddVariableNew("RowNum", cr.RowNum);
                    jsnItem.AddVariableNew("TotalCount", cr.TotalCount);
                    TotalPageingRecord = cr.TotalCount;
                    if (IsServerLocal && cr.ClientNo == SessionManager.ClientID)
                    {
                        jsnItem.AddVariableNew("ID", cr.CustomerRequirementId);
                        jsnItem.AddVariableNew("CustID", cr.CompanyNo);
                        jsnItem.AddVariableNew("Cust", cr.CompanyName);
                        //jsnItem.AddVariable("Price", Functions.FormatCurrency(cr.Price, cr.CurrencyCode));
                        jsnItem.AddVariableNew("No", cr.CustomerRequirementNumber);
                        jsnItem.AddVariableNew("MfrNo", cr.ManufacturerNo);
                        jsnItem.AddVariableNew("SalesmanNo", cr.Salesman);
                        jsnItem.AddVariableNew("SalesmanName", cr.SalesmanName);
                    }
                    else
                    {
                        jsnItem.AddVariableNew("ID", 0);
                        jsnItem.AddVariableNew("No", cr.CustomerRequirementNumber);
                        jsnItem.AddVariableNew("CustID", 0);
                        // //[001] code start
                        jsnItem.AddVariableNew("Cust", SessionManager.IsPOHub == true ? cr.CompanyName + " (" + cr.ClientCode + ")" : cr.ClientName);
                        //[001] code end
                        //jsnItem.AddVariable("Price", "");
                        jsnItem.AddVariableNew("MfrNo", "");
                        jsnItem.AddVariableNew("Salesman", 0);
                        jsnItem.AddVariableNew("SalesmanName", cr.SalesmanName);
                    }
                    jsnItem.AddVariableNew("Price", Functions.FormatCurrency(cr.Price, cr.CurrencyCode));
                    jsnItem.AddVariableNew("Qty", Functions.FormatNumeric(cr.Quantity));
                    jsnItem.AddVariableNew("PartNo", cr.Part);
                    jsnItem.AddVariableNew("ROHS", cr.ROHS);
                    jsnItem.AddVariableNew("Mfr", cr.ManufacturerCode);
                    jsnItem.AddVariableNew("Date", Functions.FormatDate(cr.ReceivedDate));
                    jsnItem.AddVariableNew("Pack", cr.PackageName);
                    jsnItem.AddVariableNew("Prod", cr.ProductName);
                    jsnItem.AddVariableNew("CustP", cr.CustomerPart);
                    jsnItem.AddVariableNew("DC", cr.DateCode);
                    //[002] start
                    jsnItem.AddVariableNew("BOMId", cr.BOMNo);
                    jsnItem.AddVariableNew("BOMName", cr.BOMName);
                    jsnItem.AddVariableNew("isIncludeAltPart", cr.isIncludeAltPart);
                    //[002] end
                    jsnItems.AddVariableNew(jsnItem);
                }
                jsn.AddVariableNew("iTotalRecords", TotalPageingRecord);
                jsn.AddVariableNew("iTotalDisplayRecords", TotalPageingRecord);
                jsn.AddVariableNew("data", jsnItems);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }
        //end

        //get quotest
        public void GetData_Quotes(HttpContext context)
        {
            try
            {
                
                //[usp_CrossMatch_QuoteLine]
                System.Int32? TotalPageingRecord = 0;
                int displayLength = Convert.ToInt32(GetQueryStringValue_NullableInt("NoOfTopRecord", 0));
                int displayStart = int.Parse(context.Request.Params["Start"]);
                int sortCol = 0;//int.Parse(context.Request.Params["order[0][column]"]);
                System.String sortDir = "asc";//context.Request.Params["order[0][dir]"];
                    
                System.Int32? BomID = Convert.ToInt32(GetQueryStringValue_NullableInt("BOMId", 0));
                System.Int32? ReqID = Convert.ToInt32(GetQueryStringValue_NullableInt("customerRequirementNo", 0));
                //string search = context.Request.Params["search[value]"];
                List<QuoteLine> lst = QuoteLine.SourceQuoteLine(SessionManager.ClientID,
                    displayStart,
                    displayLength,
                    sortCol,
                    sortDir,
                    GetQueryStringValue_String("PartSearch"),
                    GetQueryStringValue_String("PartMatch"),
                    GetQueryStringValue_String("sMonth"),
                     GetQueryStringValue_NullableInt("dtTime", null, true),
                    GetQueryStringValue_NullableInt("VendorType", null, true),
                    GetQueryStringValue_NullableInt("Currency", null, true),
                    GetQueryStringValue_NullableBoolean("Manufacture", true),
                      0
                    , IsServerLocal, SessionManager.IsPOHub, BomID,
                    GetQueryStringValue_NullableBoolean("IncludeAltPart", true),
                    ReqID
                    );
                
                JsonObject jsn = new JsonObject();
                JsonObject jsnItems = new JsonObject(true);
                foreach (QuoteLine ln in lst)
                {
                    JsonObject jsnItem = new JsonObject();
                    jsnItem.AddVariableNew("RowNum", ln.RowNum);
                    jsnItem.AddVariableNew("TotalCount", ln.TotalCount);
                    TotalPageingRecord = ln.TotalCount;
                    if (IsServerLocal && ln.ClientNo == SessionManager.ClientID)
                    {
                        jsnItem.AddVariableNew("ID", ln.QuoteLineId);
                        jsnItem.AddVariableNew("CustID", ln.CompanyNo);
                        jsnItem.AddVariableNew("Cust", ln.CompanyName);
                        //jsnItem.AddVariable("Price", Functions.FormatCurrency(ln.Price, ln.CurrencyCode));
                        jsnItem.AddVariableNew("QuoteID", ln.QuoteNo);
                        jsnItem.AddVariableNew("QuoteNo", ln.QuoteNumber);
                        jsnItem.AddVariableNew("MfrNo", ln.ManufacturerNo);
                        jsnItem.AddVariableNew("SalesmanNo", ln.Salesman);
                        jsnItem.AddVariableNew("SalesmanName", ln.SalesmanName);
                    }
                    else
                    {
                        jsnItem.AddVariableNew("ID", 0);
                        jsnItem.AddVariableNew("QuoteID", 0);
                        jsnItem.AddVariableNew("QuoteNo", ln.QuoteNumber);
                        jsnItem.AddVariableNew("CustID", 0);
                        //[001] code start
                        jsnItem.AddVariableNew("Cust", SessionManager.IsPOHub == true ? ln.CompanyName + " (" + ln.ClientCode + ")" : ln.ClientName);
                        //[001] code end
                        //jsnItem.AddVariable("Price", "");
                        jsnItem.AddVariableNew("MfrNo", "");
                        jsnItem.AddVariableNew("SalesmanNo", 0);
                        jsnItem.AddVariableNew("SalesmanName", ln.SalesmanName);
                    }
                    jsnItem.AddVariableNew("Price", Functions.FormatCurrency(ln.Price, ln.CurrencyCode));
                    jsnItem.AddVariableNew("Qty", ln.Quantity);
                    jsnItem.AddVariableNew("PartNo", ln.Part);
                    jsnItem.AddVariableNew("ROHS", ln.ROHS);
                    jsnItem.AddVariableNew("Mfr", ln.ManufacturerCode);
                    jsnItem.AddVariableNew("Date", Functions.FormatDate(ln.DateQuoted));
                    jsnItem.AddVariableNew("Pack", ln.PackageName);
                    jsnItem.AddVariableNew("Prod", ln.ProductName);
                    jsnItem.AddVariableNew("CustP", ln.CustomerPart);
                    jsnItem.AddVariableNew("DC", ln.DateCode);
                    jsnItem.AddVariableNew("Reason", ln.Reason);
                    jsnItem.AddVariableNew("ReasonNote", ln.ReasonNote);
                    jsnItem.AddVariableNew("isIncludeAltPart", ln.isIncludeAltPart);
                    jsnItems.AddVariableNew(jsnItem);
                }
                jsn.AddVariableNew("iTotalRecords", TotalPageingRecord);
                jsn.AddVariableNew("iTotalDisplayRecords", TotalPageingRecord);
                jsn.AddVariableNew("data", jsnItems);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }
        //end

        //get sales
        public void GetData_Sales(HttpContext context)
        {
            try
            {
                

                //[usp_CrossMatch_SalesOrderLine]
                System.Int32? TotalPageingRecord = 0;
                int displayLength = Convert.ToInt32(GetQueryStringValue_NullableInt("NoOfTopRecord", 0));
                int displayStart = int.Parse(context.Request.Params["Start"]);
                int sortCol = 0;//int.Parse(context.Request.Params["order[0][column]"]);
                System.String sortDir = "asc";//context.Request.Params["order[0][dir]"];
                System.Int32? BomID = Convert.ToInt32(GetQueryStringValue_NullableInt("BOMId", 0));
                System.Int32? ReqID = Convert.ToInt32(GetQueryStringValue_NullableInt("customerRequirementNo", 0));
                //string search = context.Request.Params["search[value]"];
                List<SalesOrderLine> lst = SalesOrderLine.SourceSalesOrderLine(SessionManager.ClientID,
                    displayStart,
                    displayLength,
                    sortCol,
                    sortDir,
                    GetQueryStringValue_String("PartSearch"),
                    GetQueryStringValue_String("PartMatch"),
                    GetQueryStringValue_String("sMonth"),
                     GetQueryStringValue_NullableInt("dtTime", null, true),
                    GetQueryStringValue_NullableInt("VendorType", null, true),
                    GetQueryStringValue_NullableInt("Currency", null, true),
                    GetQueryStringValue_NullableBoolean("Manufacture", true),
                    0
                    , IsServerLocal, SessionManager.IsPOHub, BomID,
                    GetQueryStringValue_NullableBoolean("IncludeAltPart", true),
                    ReqID
                    );
                
                JsonObject jsn = new JsonObject();
                JsonObject jsnItems = new JsonObject(true);
                foreach (SalesOrderLine ln in lst)
                {
                    JsonObject jsnItem = new JsonObject();
                    jsnItem.AddVariableNew("RowNum", ln.RowNum);
                    jsnItem.AddVariableNew("TotalCount", ln.TotalCount);
                    TotalPageingRecord = ln.TotalCount;
                    if (IsServerLocal && ln.ClientNo == SessionManager.ClientID)
                    {
                        jsnItem.AddVariableNew("ID", ln.SalesOrderLineId);
                        jsnItem.AddVariableNew("SOID", ln.SalesOrderNo);
                        jsnItem.AddVariableNew("SONo", ln.SalesOrderNumber);
                        jsnItem.AddVariableNew("CustNo", ln.CompanyNo);
                        jsnItem.AddVariableNew("Cust", ln.CompanyName);
                        //jsnItem.AddVariable("Price", Functions.FormatCurrency(ln.Price, ln.CurrencyCode));
                        jsnItem.AddVariableNew("MfrNo", ln.ManufacturerNo);
                        jsnItem.AddVariableNew("SalesmanNo", ln.Salesman);
                        jsnItem.AddVariableNew("SalesmanName", ln.SalesmanName);
                    }
                    else
                    {
                        jsnItem.AddVariableNew("ID", 0);
                        jsnItem.AddVariableNew("SOID", "");
                        jsnItem.AddVariableNew("SONo", ln.SalesOrderNumber);
                        jsnItem.AddVariableNew("CustNo", 0);
                        //[001] code start
                        jsnItem.AddVariableNew("Cust", SessionManager.IsPOHub == true ? ln.CompanyName + " (" + ln.ClientCode + ")" : ln.ClientName);
                        //[001] code end
                        //jsnItem.AddVariable("Price", "");
                        jsnItem.AddVariableNew("MfrNo", "");
                        jsnItem.AddVariableNew("SalesmanNo", 0);
                        jsnItem.AddVariableNew("SalesmanName", ln.SalesmanName);
                    }
                    if (SessionManager.IsPOHub.Value)
                    {
                        jsnItem.AddVariableNew("IPONos", this.GetJsonObject(ln.IPOs, "IPONo", 0));
                        jsnItem.AddVariableNew("IPONumbers", this.GetJsonObject(ln.IPOs, "IPONumber", 1));
                        jsnItem.AddVariableNew("ShowIPOLink", true);
                        jsnItem.AddVariableNew("IPOPrice", Functions.FormatCurrency(ln.IPOPrice, ln.IPOPriceWithCurrency));

                    }
                    else
                    {
                        if ((IsServerLocal && ln.ClientNo == SessionManager.ClientID))
                        {
                            jsnItem.AddVariableNew("IPONos", this.GetJsonObject(ln.IPOs, "IPONo", 0));
                            jsnItem.AddVariableNew("IPONumbers", this.GetJsonObject(ln.IPOs, "IPONumber", 1));
                            jsnItem.AddVariableNew("ShowIPOLink", true);
                            jsnItem.AddVariableNew("IPOPrice", Functions.FormatCurrency(ln.IPOPrice, ln.IPOPriceWithCurrency));
                        }
                        else
                        {
                            jsnItem.AddVariableNew("IPONos", this.GetJsonObject(ln.IPOs, "IPONo", 0));
                            jsnItem.AddVariableNew("IPONumbers", this.GetJsonString(ln.IPOs, 1));
                            jsnItem.AddVariableNew("ShowIPOLink", false);
                            jsnItem.AddVariableNew("IPOPrice", Functions.FormatCurrency(ln.IPOPrice, ln.IPOPriceWithCurrency));
                        }
                    }
                    jsnItem.AddVariableNew("Price", Functions.FormatCurrency(ln.Price, ln.CurrencyCode));
                    jsnItem.AddVariableNew("Qty", ln.Quantity);
                    jsnItem.AddVariableNew("PartNo", ln.Part);
                    jsnItem.AddVariableNew("ROHS", ln.ROHS);
                    jsnItem.AddVariableNew("Mfr", ln.ManufacturerCode);
                    jsnItem.AddVariableNew("Date", Functions.FormatDate(ln.DateOrdered));
                    jsnItem.AddVariableNew("Prod", ln.ProductName);
                    jsnItem.AddVariableNew("Pack", ln.PackageName);
                    jsnItem.AddVariableNew("CustP", ln.CustomerPart);
                    jsnItem.AddVariableNew("DC", ln.DateCode);
                    jsnItem.AddVariableNew("blnCRMA", ln.blnCRMA);
                    jsnItem.AddVariableNew("isIncludeAltPart", ln.isIncludeAltPart);
                    jsnItems.AddVariableNew(jsnItem);
                }
                jsn.AddVariableNew("iTotalRecords", TotalPageingRecord);
                jsn.AddVariableNew("iTotalDisplayRecords", TotalPageingRecord);
                jsn.AddVariableNew("data", jsnItems);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }
        //end

        //get purchase
        public void GetData_Purchases(HttpContext context)
        {
            try
            {
               
                //[usp_CrossMatch_PurchaseOrderLine]
                System.Int32? TotalPageingRecord = 0;
                int displayLength = Convert.ToInt32(GetQueryStringValue_NullableInt("NoOfTopRecord", 0));
                int displayStart = int.Parse(context.Request.Params["Start"]);
                int sortCol = 0;//int.Parse(context.Request.Params["order[0][column]"]);
                System.String sortDir = "asc";//context.Request.Params["order[0][dir]"];
                System.Int32? BomID = Convert.ToInt32(GetQueryStringValue_NullableInt("BOMId", 0));
                System.Int32? ReqID = Convert.ToInt32(GetQueryStringValue_NullableInt("customerRequirementNo", 0));
                //string search = context.Request.Params["search[value]"];
                List<PurchaseOrderLine> lst = PurchaseOrderLine.SourcePurchaseOrderLine(SessionManager.ClientID,
                    displayStart,
                    displayLength,
                    sortCol,
                    sortDir,
                    GetQueryStringValue_String("PartSearch"),
                    GetQueryStringValue_String("PartMatch"),
                    GetQueryStringValue_String("sMonth"),
                    GetQueryStringValue_NullableInt("dtTime", null, true),
                    GetQueryStringValue_NullableInt("VendorType", null, true),
                    GetQueryStringValue_NullableInt("Currency", null, true),
                    GetQueryStringValue_NullableBoolean("Manufacture", true),
                    0
                    , IsServerLocal, SessionManager.IsPOHub, BomID,
                    GetQueryStringValue_NullableBoolean("IncludeAltPart", true),
                    ReqID
                    );
              
                JsonObject jsn = new JsonObject();
                JsonObject jsnItems = new JsonObject(true);
                foreach (PurchaseOrderLine ln in lst)
                {
                    JsonObject jsnItem = new JsonObject();
                    jsnItem.AddVariableNew("RowNum", ln.RowNum);
                    jsnItem.AddVariableNew("TotalCount", ln.TotalCount);
                    TotalPageingRecord = ln.TotalCount;
                    
                    if (IsServerLocal && ln.ClientNo == SessionManager.ClientID)
                    {
                        jsnItem.AddVariableNew("ID", ln.PurchaseOrderLineId);
                        jsnItem.AddVariableNew("CustNo", ln.CompanyNo);
                        //[001] code start
                        jsnItem.AddVariableNew("Cust", ln.CompanyName);
                        //[001] code end
                        //jsnItem.AddVariable("Price", Functions.FormatCurrency(ln.Price, ln.CurrencyCode));
                        jsnItem.AddVariableNew("PONo", ln.PurchaseOrderNumber);
                        jsnItem.AddVariableNew("POID", ln.PurchaseOrderNo);
                        jsnItem.AddVariableNew("MfrNo", ln.ManufacturerNo);
                        jsnItem.AddVariableNew("BuyerNo", ln.Buyer);
                        jsnItem.AddVariableNew("BuyerName", ln.BuyerName);
                    }
                    else
                    {
                        jsnItem.AddVariableNew("ID", 0);
                        jsnItem.AddVariableNew("PONo", ln.PurchaseOrderNumber);
                        jsnItem.AddVariableNew("POID", 0);
                        jsnItem.AddVariableNew("CustNo", 0);
                        jsnItem.AddVariableNew("Cust", SessionManager.IsPOHub == true ? ln.CompanyName + " (" + ln.ClientCode + ")" : ln.ClientName);
                        //jsnItem.AddVariable("Price", "");
                        jsnItem.AddVariableNew("MfrNo", "");
                        jsnItem.AddVariableNew("BuyerNo", 0);
                        jsnItem.AddVariableNew("BuyerName", ln.BuyerName);
                    }



                    jsnItem.AddVariableNew("Price", Functions.FormatCurrency(ln.Price, ln.CurrencyCode));
                    jsnItem.AddVariableNew("Qty", ln.Quantity);
                    jsnItem.AddVariableNew("PartNo", ln.Part);
                    jsnItem.AddVariableNew("ROHS", ln.ROHS);
                    jsnItem.AddVariableNew("Mfr", ln.ManufacturerCode);
                    jsnItem.AddVariableNew("Date", Functions.FormatDate(ln.DateOrdered));
                    jsnItem.AddVariableNew("Prod", ln.ProductName);
                    jsnItem.AddVariableNew("Pack", ln.PackageName);
                    jsnItem.AddVariableNew("SupP", ln.SupplierPart);
                    jsnItem.AddVariableNew("DC", ln.DateCode);
                    jsnItem.AddVariableNew("blnSRMA", ln.blnSRMA);
                    jsnItem.AddVariableNew("blnCanShowIPO", (ln.InternalPurchaseOrderNo.HasValue && ln.InternalPurchaseOrderNo.Value > 0));

                    if (SessionManager.IsPOHub.Value)
                    {
                        jsnItem.AddVariableNew("IPONo", ln.InternalPurchaseOrderNumber);
                        jsnItem.AddVariableNew("IPOID", ln.InternalPurchaseOrderNo);
                    }
                    else
                    {
                        if ((IsServerLocal && ln.IPOClientNo == SessionManager.ClientID))
                        {
                            jsnItem.AddVariableNew("IPONo", ln.InternalPurchaseOrderNumber);
                            jsnItem.AddVariableNew("IPOID", ln.InternalPurchaseOrderNo);
                        }
                        else
                        {
                            jsnItem.AddVariableNew("IPONo", ln.InternalPurchaseOrderNumber);
                            jsnItem.AddVariableNew("IPOID", 0);
                        }
                    }
                    jsnItem.AddVariableNew("isIncludeAltPart", ln.isIncludeAltPart);
                    jsnItems.AddVariableNew(jsnItem);
                }
                jsn.AddVariableNew("iTotalRecords", TotalPageingRecord);
                jsn.AddVariableNew("iTotalDisplayRecords", TotalPageingRecord);
                jsn.AddVariableNew("data", jsnItems);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }
        //end

        private string GetJsonString(string strValues, int index)
        {
            string strResult = "";

            if (!string.IsNullOrEmpty(strValues))
            {
                Array strArray1 = Functions.JavascriptStringToArray(strValues, new string[] { "||" });
                strResult = Convert.ToString(strArray1.GetValue(index));
            }
            return strResult;
        }
        private JsonObject GetJsonObject(string strValues, string strName, int index)
        {
            JsonObject jsnItems = new JsonObject(true);

            if (!string.IsNullOrEmpty(strValues))
            {
                Array strArray1 = Functions.JavascriptStringToArray(strValues, new string[] { "||" });
                Array strArray = Functions.JavascriptStringToArray(Convert.ToString(strArray1.GetValue(index)), new string[] { "," });
                for (int i = 0; i < strArray.Length; i++)
                {
                    JsonObject jsnItem = new JsonObject();
                    jsnItem.AddVariable(strName, strArray.GetValue(i));
                    jsnItems.AddVariable(jsnItem); jsnItem = null;
                }
                strArray = null;
            }
            return jsnItems;
        }

        public bool IsServerLocal { get; set; }

        //package dropdown
        protected void GetPackageType()
        {
            
            JsonObject jsn = new JsonObject();
            JsonObject jsnList = new JsonObject(true);
            List<BLL.Package> lst = BLL.Package.DropDown();
            for (int i = 0; i < lst.Count; i++)
            {
                JsonObject jsnItem = new JsonObject();
                jsnItem.AddVariableNew("ID", lst[i].PackageId);
                jsnItem.AddVariableNew("Name", lst[i].PackageDescription);
                jsnList.AddVariableNew(jsnItem);
                jsnItem.Dispose(); jsnItem = null;
            }
            lst.Clear(); lst = null;
            //jsn.AddVariable("Types", jsnList);
            jsn.AddVariableNew(jsnList);
            //jsnList.Dispose(); jsnList = null;
            OutputResult(jsnList);
            //jsn.Dispose(); jsn = null;
            jsnList.Dispose(); jsnList = null;
        }
        //end
        protected void GetROHSStatus()
        {

            JsonObject jsn = new JsonObject();
            JsonObject jsnList = new JsonObject(true);
            List<BLL.RohsStatus> lst = BLL.RohsStatus.DropDown();
            for (int i = 0; i < lst.Count; i++)
            {
                JsonObject jsnItem = new JsonObject();
                jsnItem.AddVariableNew("ID", lst[i].ROHSStatusId);
                jsnItem.AddVariableNew("Name", Functions.GetROHSStatusName(lst[i].ROHSStatusId));
                jsnList.AddVariableNew(jsnItem);
                jsnItem.Dispose(); jsnItem = null;
            }
            lst.Clear(); lst = null;
            //jsn.AddVariable("Types", jsnList);
            jsn.AddVariableNew(jsnList);
            //jsnList.Dispose(); jsnList = null;
            OutputResult(jsnList);
            //jsn.Dispose(); jsn = null;
            jsnList.Dispose(); jsnList = null;
        }

        protected void GetOfferStatus()
        {

            JsonObject jsn = new JsonObject();
            JsonObject jsnList = new JsonObject(true);
            List<BLL.OfferStatus> lst = BLL.OfferStatus.DropDown();
            for (int i = 0; i < lst.Count; i++)
            {
                JsonObject jsnItem = new JsonObject();
                jsnItem.AddVariableNew("ID", lst[i].OfferStatusId);
                jsnItem.AddVariableNew("Name", Functions.GetGlobalResource("OfferStatus", lst[i].Name));
                jsnList.AddVariableNew(jsnItem);
                jsnItem.Dispose(); jsnItem = null;
            }
            lst.Clear(); lst = null;
            //jsn.AddVariable("Types", jsnList);
            jsn.AddVariableNew(jsnList);
            //jsnList.Dispose(); jsnList = null;
            OutputResult(jsnList);
            //jsn.Dispose(); jsn = null;
            jsnList.Dispose(); jsnList = null;
        }

        protected void GetMSL()
        {

            JsonObject jsn = new JsonObject();
            JsonObject jsnList = new JsonObject(true);
            List<BLL.MSLLevel> lst = BLL.MSLLevel.DropDown();
            for (int i = 0; i < lst.Count; i++)
            {
                JsonObject jsnItem = new JsonObject();
                jsnItem.AddVariableNew("MSLLevelId", lst[i].MSLLevelId);
                jsnItem.AddVariableNew("MSLLevels", lst[i].MSLLevels);
                jsnList.AddVariableNew(jsnItem);
                jsnItem.Dispose(); jsnItem = null;
            }
            lst.Clear(); lst = null;
            //jsn.AddVariable("Types", jsnList);
            jsn.AddVariableNew(jsnList);
            //jsnList.Dispose(); jsnList = null;
            OutputResult(jsnList);
            //jsn.Dispose(); jsn = null;
            jsnList.Dispose(); jsnList = null;
        }

        protected void GetSupplier(HttpContext context)
        {

            List<Company> lst = null;
            try
            {
                string SuppSearch = string.IsNullOrEmpty(context.Request.QueryString["SuppSearch"]) ? "" : Convert.ToString((context.Request.QueryString["SuppSearch"]));
                //lst = Company.AutoSearchForAllSuppliers(SessionManager.ClientID, GetFormValueQueryCrossMatch_StringForNameSearch("SuppSearch"));
                lst = Company.AutoSearchForAllSuppliers(SessionManager.ClientID, SuppSearch+'%');
                JsonObject jsn = new JsonObject();
                //jsn.AddVariableNew("TotalRecords", lst.Count);
                JsonObject jsnRows = new JsonObject(true);
                for (int i = 0; i < lst.Count; i++)
                {
                    if (i < lst.Count)
                    {
                        JsonObject jsnRow = new JsonObject();
                        jsnRow.AddVariableNew("value", lst[i].CompanyId);
                        jsnRow.AddVariableNew("label", lst[i].CompanyName);
                       // jsnRow.AddVariableNew("SupNo", lst[i].CompanyId);
                       // jsnRow.AddVariableNew("Email", lst[i].EMail);
                        jsnRows.AddVariableNew(jsnRow);
                        jsnRow.Dispose();
                        jsnRow = null;
                    }
                }
                //jsn.AddVariable("Results", jsnRows);
               // jsn.AddVariableNew(jsnRows);
                OutputResult(jsnRows);
                jsnRows.Dispose(); jsnRows = null;
                jsn.Dispose(); jsn = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
            finally
            {
                lst = null;
            }
        }

        protected void GetPartNo(HttpContext context)
        {

            List<BLL.Part> lst = null;
            try
            {
                string PartSearch = string.IsNullOrEmpty(context.Request.QueryString["PartSearch"]) ? "" : Convert.ToString((context.Request.QueryString["PartSearch"]));
                //lst = BLL.Part.AutoSearch(SessionManager.ClientID, GetFormValueQueryCrossMatch_StringForNameSearch("PartSearch"));
                lst = BLL.Part.AutoSearch(SessionManager.ClientID, PartSearch+'%');
               JsonObject jsn = new JsonObject();
               // jsn.AddVariableNew("TotalRecords", lst.Count);
                JsonObject jsnRows = new JsonObject(true);
                for (int i = 0; i < lst.Count; i++)
                {
                    if (i < lst.Count)
                    {
                        JsonObject jsnRow = new JsonObject();
                        jsnRow.AddVariableNew("value", lst[i].PartName);
                        jsnRow.AddVariableNew("label", lst[i].PartName);
                        jsnRows.AddVariableNew(jsnRow);
                        jsnRow.Dispose();
                        jsnRow = null;
                    }
                }
                //jsn.AddVariable("Results", jsnRows);
               // jsn.AddVariableNew( jsnRows);
                OutputResult(jsnRows);
                jsnRows.Dispose(); jsnRows = null;
                jsn.Dispose(); jsn = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
            finally
            {
                lst = null;
            }
        }

        protected void GetManufacturer(HttpContext context)
        {
            List<Manufacturer> lst = null;
            try
            {
                string MrfSearch = string.IsNullOrEmpty(context.Request.QueryString["MrfSearch"]) ? "" : Convert.ToString((context.Request.QueryString["MrfSearch"]));
                lst = Manufacturer.AutoSearch(MrfSearch+'%', false);//GetFormValue_NullableBoolean("blnShowInactive")  GetFormValue_Boolean("ShowInactive")
                //lst = Manufacturer.AutoSearch(GetFormValueQueryCrossMatch_StringForNameSearch("MrfSearch"), false);//GetFormValue_NullableBoolean("blnShowInactive")  GetFormValue_Boolean("ShowInactive")
                JsonObject jsn = new JsonObject();
               // jsn.AddVariableNew("TotalRecords", lst.Count);
                JsonObject jsnRows = new JsonObject(true);
                for (int i = 0; i < lst.Count; i++)
                {
                    if (i < lst.Count)
                    {
                        JsonObject jsnRow = new JsonObject();
                        jsnRow.AddVariableNew("value", lst[i].ManufacturerId);
                        jsnRow.AddVariableNew("label", lst[i].ManufacturerName);
                        jsnRows.AddVariableNew(jsnRow);
                        jsnRow.Dispose();
                        jsnRow = null;
                    }
                }
                //jsn.AddVariable("Results", jsnRows);
               // jsn.AddVariableNew("Results", jsnRows);
                OutputResult(jsnRows);
                jsnRows.Dispose(); jsnRows = null;
                jsn.Dispose(); jsn = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
            finally
            {
                lst = null;
            }
        }

        public void GetProduct(HttpContext context)
        {
            
            List<Product> lst = null;
            try
            {
                string ProdSearch = string.IsNullOrEmpty(context.Request.QueryString["ProdSearch"]) ? "" : Convert.ToString((context.Request.QueryString["ProdSearch"]));
                lst = Product.AutoSearch(ProdSearch, SessionManager.ClientID);
                JsonObject jsn = new JsonObject();
                //jsn.AddVariableNew("TotalRecords", lst.Count);
                JsonObject jsnRows = new JsonObject(true);
                for (int i = 0; i < lst.Count; i++)
                {
                    if (i < lst.Count)
                    {
                        JsonObject jsnRow = new JsonObject();
                        jsnRow.AddVariableNew("value", lst[i].ProductId);
                        jsnRow.AddVariableNew("label", lst[i].ProductDescription);
                        //jsnRow.AddVariableNew("IsHa", lst[i].Hazarders);
                        jsnRows.AddVariableNew(jsnRow);
                        jsnRow.Dispose();
                        jsnRow = null;
                    }
                }

                //jsnRows.AddVariable("projects", jsnRows);
                //jsn.AddVariableNew(jsnRows);
                OutputResult(jsnRows);
                jsnRows.Dispose(); jsnRows = null;
                jsn.Dispose(); jsn = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
            finally
            {
                lst = null;
            }
        }

        protected void GetCountryOfOrigin()
        {
            JsonObject jsn = new JsonObject();
            JsonObject jsnList = new JsonObject(true);
            List<BLL.GlobalCountryList> lst = BLL.GlobalCountryList.DropDown(null, null);
            for (int i = 0; i < lst.Count; i++)
            {
                JsonObject jsnItem = new JsonObject();
                jsnItem.AddVariableNew("ID", lst[i].GlobalCountryId);
                jsnItem.AddVariableNew("Name", lst[i].GlobalCountryName);
                jsnList.AddVariableNew(jsnItem);
                jsnItem.Dispose();
            }
            lst.Clear();
            jsn.AddVariableNew(jsnList);
            OutputResult(jsnList);
            jsnList.Dispose();
        }

        protected void GetRegion()
        {
            JsonObject jsn = new JsonObject();
            JsonObject jsnList = new JsonObject(true);
            var lst = BLL.Country.DropDownForRegion();
            for (int i = 0; i < lst.Count; i++)
            {
                JsonObject jsnItem = new JsonObject();
                jsnItem.AddVariableNew("ID", lst[i].RegionId);
                jsnItem.AddVariableNew("Name", lst[i].RegionName);
                jsnList.AddVariableNew(jsnItem);
                jsnItem.Dispose();
            }
            lst.Clear();
            jsn.AddVariableNew(jsnList);
            OutputResult(jsnList);
            jsnList.Dispose();
        }


        /// <summary>
        /// Writes report to a CSV file and returns the filename 
        /// </summary>
        /// <returns></returns>
        public void ExportToCSV(HttpContext context)
        {
            JsonObject jsn = new JsonObject();
            try
            {
             
                string filePath = string.Empty;
                string strFilename = String.Format("report_u{0}r{1}.xlsx", SessionManager.LoginID ?? 0, 0);
                System.Int32? BomID = Convert.ToInt32(GetQueryStringValue_NullableInt("BOMId", 0));
                System.Int32? CustReqId = Convert.ToInt32(GetQueryStringValue_NullableInt("customerRequirementNo", 0));
                DataTable dtcrossmatchrequestlist = BLL.Stock.ExportToCSVCrossMatch(BomID, CustReqId, SessionManager.ClientID, SessionManager.LoginID ?? 0);
                DataTable dtcrossmatchrequest = BLL.Stock.ExportToCSVCrossMatchlist(BomID, CustReqId, SessionManager.ClientID, SessionManager.LoginID ?? 0);
                filePath = (new Rebound.GlobalTrader.Site.Code.Common.EPPlusExportUtility()).ExportDataTableToExcel(dtcrossmatchrequest, dtcrossmatchrequestlist, strFilename);
                //return saved filename to the page
                jsn.AddVariableNew("Filename", filePath);
                OutputResult(jsn);

            }
            catch (Exception e)
            {
                WriteError(e);
            }
            finally
            {
                jsn.Dispose();
                jsn = null;
            }
        }
        
    }

}