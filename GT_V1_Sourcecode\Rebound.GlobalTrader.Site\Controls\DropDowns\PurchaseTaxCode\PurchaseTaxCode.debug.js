//Marker     Changed by      Date               Remarks
//[001]      Vinay           24/06/2013         CR:- Supplier Invoice

Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");

Rebound.GlobalTrader.Site.Controls.DropDowns.PurchaseTaxCode = function(element) { 
	Rebound.GlobalTrader.Site.Controls.DropDowns.PurchaseTaxCode.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.DropDowns.PurchaseTaxCode.prototype = {

	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.DropDowns.PurchaseTaxCode.callBaseMethod(this, "initialize");
		this.addSetupDataCall(Function.createDelegate(this, this.setupDataCall));
		this.addGetDataOK(Function.createDelegate(this, this.dataCallOK));
	},
	
	dispose: function() { 
		if (this.isDisposed) return;
		Rebound.GlobalTrader.Site.Controls.DropDowns.PurchaseTaxCode.callBaseMethod(this, "dispose");
	},
	
	setupDataCall: function() { 
		this._objData.set_PathToData("controls/DropDowns/PurchaseTaxCode");
		this._objData.set_DataObject("PurchaseTaxCode");
		this._objData.set_DataAction("GetData");
	},
	
	dataCallOK: function() { 
		var result = this._objData._result;
		if (result.Taxes) {
			for (var i = 0; i < result.Taxes.length; i++) {
			    this.addOption(result.Taxes[i].Name, result.Taxes[i].ID, result.Taxes[i].Code);
			}
		}
	}
	
};

Rebound.GlobalTrader.Site.Controls.DropDowns.PurchaseTaxCode.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.PurchaseTaxCode", Rebound.GlobalTrader.Site.Controls.DropDowns.Base);
