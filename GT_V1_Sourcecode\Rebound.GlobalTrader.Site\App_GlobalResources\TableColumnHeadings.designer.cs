//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Resources {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option or rebuild the Visual Studio project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.VisualStudio.Web.Application.StronglyTypedResourceProxyBuilder", "16.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class TableColumnHeadings {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal TableColumnHeadings() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Resources.TableColumnHeadings", global::System.Reflection.Assembly.Load("App_GlobalResources"));
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Accounts.
        /// </summary>
        internal static string Accounts {
            get {
                return ResourceManager.GetString("Accounts", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Action.
        /// </summary>
        internal static string Action {
            get {
                return ResourceManager.GetString("Action", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Active.
        /// </summary>
        internal static string Active {
            get {
                return ResourceManager.GetString("Active", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Activity Date.
        /// </summary>
        internal static string ActivityDate {
            get {
                return ResourceManager.GetString("ActivityDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Actual Price.
        /// </summary>
        internal static string ActualPrice {
            get {
                return ResourceManager.GetString("ActualPrice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Buy Price In Base.
        /// </summary>
        internal static string ActualPriceInBase {
            get {
                return ResourceManager.GetString("ActualPriceInBase", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date Created.
        /// </summary>
        internal static string AddedDate {
            get {
                return ResourceManager.GetString("AddedDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Additional notes from supplier.
        /// </summary>
        internal static string Additionalnotesfromsupplier {
            get {
                return ResourceManager.GetString("Additionalnotesfromsupplier", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Address.
        /// </summary>
        internal static string Address {
            get {
                return ResourceManager.GetString("Address", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Name.
        /// </summary>
        internal static string AddressName {
            get {
                return ResourceManager.GetString("AddressName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Type.
        /// </summary>
        internal static string AddressType {
            get {
                return ResourceManager.GetString("AddressType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Adjustment.
        /// </summary>
        internal static string AdjustMent {
            get {
                return ResourceManager.GetString("AdjustMent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to AD Login Name.
        /// </summary>
        internal static string ADLoginName {
            get {
                return ResourceManager.GetString("ADLoginName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Air Way Bill.
        /// </summary>
        internal static string AirWayBill {
            get {
                return ResourceManager.GetString("AirWayBill", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allocated.
        /// </summary>
        internal static string Allocated {
            get {
                return ResourceManager.GetString("Allocated", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Alternate Part No.
        /// </summary>
        internal static string AlternatePartNo {
            get {
                return ResourceManager.GetString("AlternatePartNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Answer.
        /// </summary>
        internal static string Answer {
            get {
                return ResourceManager.GetString("Answer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Future Electronics.
        /// </summary>
        internal static string APIExternalLinks {
            get {
                return ResourceManager.GetString("APIExternalLinks", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to API Imported Data.
        /// </summary>
        internal static string APIImportedData {
            get {
                return ResourceManager.GetString("APIImportedData", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Actual price in Base Currency.
        /// </summary>
        internal static string APINBC {
            get {
                return ResourceManager.GetString("APINBC", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Source.
        /// </summary>
        internal static string ApiResponse {
            get {
                return ResourceManager.GetString("ApiResponse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ApiSourceName.
        /// </summary>
        internal static string ApiSourceName {
            get {
                return ResourceManager.GetString("ApiSourceName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Applied Date.
        /// </summary>
        internal static string AppliedDate {
            get {
                return ResourceManager.GetString("AppliedDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Division.
        /// </summary>
        internal static string AppliedDivision {
            get {
                return ResourceManager.GetString("AppliedDivision", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Apply.
        /// </summary>
        internal static string Apply {
            get {
                return ResourceManager.GetString("Apply", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Apply To.
        /// </summary>
        internal static string ApplyTo {
            get {
                return ResourceManager.GetString("ApplyTo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Apply To Catagory.
        /// </summary>
        internal static string ApplyToCatagory {
            get {
                return ResourceManager.GetString("ApplyToCatagory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to POs in the last 12 months.
        /// </summary>
        internal static string ApprovedOrdTwlMonth {
            get {
                return ResourceManager.GetString("ApprovedOrdTwlMonth", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to AS6081 testing required?.
        /// </summary>
        internal static string AS6081 {
            get {
                return ResourceManager.GetString("AS6081", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Inhouse AS6081 testing required.
        /// </summary>
        internal static string AS6081Required {
            get {
                return ResourceManager.GetString("AS6081Required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to AS6081 Required?.
        /// </summary>
        internal static string AS6081Requirednew {
            get {
                return ResourceManager.GetString("AS6081Requirednew", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Assigned By.
        /// </summary>
        internal static string AS6081_AssignedBy {
            get {
                return ResourceManager.GetString("AS6081_AssignedBy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Assigned To.
        /// </summary>
        internal static string AS6081_AssigneeName {
            get {
                return ResourceManager.GetString("AS6081_AssigneeName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Document No.
        /// </summary>
        internal static string AS6081_DocumentNo {
            get {
                return ResourceManager.GetString("AS6081_DocumentNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Assignment Type.
        /// </summary>
        internal static string AS6081_Level {
            get {
                return ResourceManager.GetString("AS6081_Level", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to AS9120.
        /// </summary>
        internal static string AS9120 {
            get {
                return ResourceManager.GetString("AS9120", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Assigned To.
        /// </summary>
        internal static string AssignedTo {
            get {
                return ResourceManager.GetString("AssignedTo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Assigned User.
        /// </summary>
        internal static string AssignedUser {
            get {
                return ResourceManager.GetString("AssignedUser", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to AssignedUser/PartNo.
        /// </summary>
        internal static string AssignedUserPartNo {
            get {
                return ResourceManager.GetString("AssignedUserPartNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Authorised By.
        /// </summary>
        internal static string AuthorisedBy {
            get {
                return ResourceManager.GetString("AuthorisedBy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Authoriser.
        /// </summary>
        internal static string Authoriser {
            get {
                return ResourceManager.GetString("Authoriser", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Available Credit.
        /// </summary>
        internal static string AvailableCredit {
            get {
                return ResourceManager.GetString("AvailableCredit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Average Price.
        /// </summary>
        internal static string AveragePrice {
            get {
                return ResourceManager.GetString("AveragePrice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Base Currency.
        /// </summary>
        internal static string BaseCurrency {
            get {
                return ResourceManager.GetString("BaseCurrency", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Batch No.
        /// </summary>
        internal static string BatchNo {
            get {
                return ResourceManager.GetString("BatchNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Batch Reference.
        /// </summary>
        internal static string BatchReference {
            get {
                return ResourceManager.GetString("BatchReference", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        internal static string Blank {
            get {
                return ResourceManager.GetString("Blank", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to BOM.
        /// </summary>
        internal static string BOM {
            get {
                return ResourceManager.GetString("BOM", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bom Code.
        /// </summary>
        internal static string BomCode {
            get {
                return ResourceManager.GetString("BomCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to BOM Code.
        /// </summary>
        internal static string BOMManagerCode {
            get {
                return ResourceManager.GetString("BOMManagerCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to BOM Name.
        /// </summary>
        internal static string BOMManagerName {
            get {
                return ResourceManager.GetString("BOMManagerName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to HUBRFQ Name.
        /// </summary>
        internal static string BomName {
            get {
                return ResourceManager.GetString("BomName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to BOX.
        /// </summary>
        internal static string BOX {
            get {
                return ResourceManager.GetString("BOX", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pack Size.
        /// </summary>
        internal static string BreakdownPackSize {
            get {
                return ResourceManager.GetString("BreakdownPackSize", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Buy.
        /// </summary>
        internal static string Buy {
            get {
                return ResourceManager.GetString("Buy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Buyer.
        /// </summary>
        internal static string Buyer {
            get {
                return ResourceManager.GetString("Buyer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Buy Exchange Rate.
        /// </summary>
        internal static string BuyExchangeRate {
            get {
                return ResourceManager.GetString("BuyExchangeRate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Buy Price.
        /// </summary>
        internal static string BuyPrice {
            get {
                return ResourceManager.GetString("BuyPrice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to By.
        /// </summary>
        internal static string By {
            get {
                return ResourceManager.GetString("By", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to By &lt;/br&gt; Section.
        /// </summary>
        internal static string BySection {
            get {
                return ResourceManager.GetString("BySection", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Category Name.
        /// </summary>
        internal static string CategoryName {
            get {
                return ResourceManager.GetString("CategoryName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to CC.
        /// </summary>
        internal static string CC {
            get {
                return ResourceManager.GetString("CC", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cease Date.
        /// </summary>
        internal static string CeaseDate {
            get {
                return ResourceManager.GetString("CeaseDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Certificate Name.
        /// </summary>
        internal static string CeriticateName {
            get {
                return ResourceManager.GetString("CeriticateName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Certificate Number.
        /// </summary>
        internal static string CeriticateNumber {
            get {
                return ResourceManager.GetString("CeriticateNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Certificate Category.
        /// </summary>
        internal static string CertificateCategory {
            get {
                return ResourceManager.GetString("CertificateCategory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Certificate No.
        /// </summary>
        internal static string CertificateNo {
            get {
                return ResourceManager.GetString("CertificateNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Charge.
        /// </summary>
        internal static string Charge {
            get {
                return ResourceManager.GetString("Charge", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Inspected by.
        /// </summary>
        internal static string CheckedBy {
            get {
                return ResourceManager.GetString("CheckedBy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to City.
        /// </summary>
        internal static string City {
            get {
                return ResourceManager.GetString("City", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Client.
        /// </summary>
        internal static string Client {
            get {
                return ResourceManager.GetString("Client", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Client Bill To.
        /// </summary>
        internal static string ClientBillTo {
            get {
                return ResourceManager.GetString("ClientBillTo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to BOM Name.
        /// </summary>
        internal static string ClientBOMName {
            get {
                return ResourceManager.GetString("ClientBOMName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to BOM No.
        /// </summary>
        internal static string ClientBOMNo {
            get {
                return ResourceManager.GetString("ClientBOMNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Client Code.
        /// </summary>
        internal static string ClientCode {
            get {
                return ResourceManager.GetString("ClientCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Client Invoice.
        /// </summary>
        internal static string ClientInvoice {
            get {
                return ResourceManager.GetString("ClientInvoice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Header Name.
        /// </summary>
        internal static string ClientInvoiceHeader {
            get {
                return ResourceManager.GetString("ClientInvoiceHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Client Invoice No.
        /// </summary>
        internal static string ClientInvoiceNo {
            get {
                return ResourceManager.GetString("ClientInvoiceNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Client.
        /// </summary>
        internal static string ClientName {
            get {
                return ResourceManager.GetString("ClientName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to   .
        /// </summary>
        internal static string ClientNameEmpty {
            get {
                return ResourceManager.GetString("ClientNameEmpty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Notify.
        /// </summary>
        internal static string Clientnotify {
            get {
                return ResourceManager.GetString("Clientnotify", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PartWatch Match.
        /// </summary>
        internal static string ClientPartWatchMatch {
            get {
                return ResourceManager.GetString("ClientPartWatchMatch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Uplift Price.
        /// </summary>
        internal static string ClientUpLiftPrice {
            get {
                return ResourceManager.GetString("ClientUpLiftPrice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Code.
        /// </summary>
        internal static string Code {
            get {
                return ResourceManager.GetString("Code", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Code/Quantity.
        /// </summary>
        internal static string CodeQuantity {
            get {
                return ResourceManager.GetString("CodeQuantity", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Comments.
        /// </summary>
        internal static string Comment {
            get {
                return ResourceManager.GetString("Comment", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Commodity Code.
        /// </summary>
        internal static string CommodityCode {
            get {
                return ResourceManager.GetString("CommodityCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Communication Log Type.
        /// </summary>
        internal static string CommunicationLogType {
            get {
                return ResourceManager.GetString("CommunicationLogType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Comm Note.
        /// </summary>
        internal static string Comnote {
            get {
                return ResourceManager.GetString("Comnote", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Company.
        /// </summary>
        internal static string Company {
            get {
                return ResourceManager.GetString("Company", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Name.
        /// </summary>
        internal static string CompanyName {
            get {
                return ResourceManager.GetString("CompanyName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Type.
        /// </summary>
        internal static string CompanyType {
            get {
                return ResourceManager.GetString("CompanyType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Completed By.
        /// </summary>
        internal static string CompletedBy {
            get {
                return ResourceManager.GetString("CompletedBy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Company Type.
        /// </summary>
        internal static string CompType {
            get {
                return ResourceManager.GetString("CompType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Conflict Resource.
        /// </summary>
        internal static string ConflictResource {
            get {
                return ResourceManager.GetString("ConflictResource", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Consolidate.
        /// </summary>
        internal static string Consildate {
            get {
                return ResourceManager.GetString("Consildate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Contact.
        /// </summary>
        internal static string Contact {
            get {
                return ResourceManager.GetString("Contact", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Contact.
        /// </summary>
        internal static string ContactLogContactName {
            get {
                return ResourceManager.GetString("ContactLogContactName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Notes.
        /// </summary>
        internal static string ContactLogDetails {
            get {
                return ResourceManager.GetString("ContactLogDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Type.
        /// </summary>
        internal static string ContactLogType {
            get {
                return ResourceManager.GetString("ContactLogType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Name.
        /// </summary>
        internal static string ContactName {
            get {
                return ResourceManager.GetString("ContactName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Contract No.
        /// </summary>
        internal static string ContractNo {
            get {
                return ResourceManager.GetString("ContractNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cost.
        /// </summary>
        internal static string Cost {
            get {
                return ResourceManager.GetString("Cost", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Counted Star.
        /// </summary>
        internal static string CountedStar {
            get {
                return ResourceManager.GetString("CountedStar", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Country.
        /// </summary>
        internal static string Country {
            get {
                return ResourceManager.GetString("Country", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Country Of Manufacture.
        /// </summary>
        internal static string CountryOfManufacture {
            get {
                return ResourceManager.GetString("CountryOfManufacture", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Country Of Origin.
        /// </summary>
        internal static string CountryOfOrigin {
            get {
                return ResourceManager.GetString("CountryOfOrigin", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Created By.
        /// </summary>
        internal static string CreatedBy {
            get {
                return ResourceManager.GetString("CreatedBy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Created Date From.
        /// </summary>
        internal static string CreatedDateFrom {
            get {
                return ResourceManager.GetString("CreatedDateFrom", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Created Date To.
        /// </summary>
        internal static string CreatedDateTo {
            get {
                return ResourceManager.GetString("CreatedDateTo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Credit Date.
        /// </summary>
        internal static string CreditDate {
            get {
                return ResourceManager.GetString("CreditDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Credit.
        /// </summary>
        internal static string CreditNote {
            get {
                return ResourceManager.GetString("CreditNote", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Req No.
        /// </summary>
        internal static string CreqNumber {
            get {
                return ResourceManager.GetString("CreqNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to CRMA.
        /// </summary>
        internal static string CRMA {
            get {
                return ResourceManager.GetString("CRMA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Current Landed Cost.
        /// </summary>
        internal static string CurrentLandedCost {
            get {
                return ResourceManager.GetString("CurrentLandedCost", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Current Rate.
        /// </summary>
        internal static string CurrentRate {
            get {
                return ResourceManager.GetString("CurrentRate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Current Rate 2.
        /// </summary>
        internal static string CurrentRate2 {
            get {
                return ResourceManager.GetString("CurrentRate2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer.
        /// </summary>
        internal static string Customer {
            get {
                return ResourceManager.GetString("Customer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer API No.
        /// </summary>
        internal static string CustomerAPINo {
            get {
                return ResourceManager.GetString("CustomerAPINo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer Code.
        /// </summary>
        internal static string CustomerCode {
            get {
                return ResourceManager.GetString("CustomerCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer Name.
        /// </summary>
        internal static string CustomerName {
            get {
                return ResourceManager.GetString("CustomerName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer Part.
        /// </summary>
        internal static string CustomerPart {
            get {
                return ResourceManager.GetString("CustomerPart", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer PO.
        /// </summary>
        internal static string CustomerPO {
            get {
                return ResourceManager.GetString("CustomerPO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer PO No.
        /// </summary>
        internal static string CustomerPurchaseOrderNo {
            get {
                return ResourceManager.GetString("CustomerPurchaseOrderNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Req.
        /// </summary>
        internal static string CustomerRequirement {
            get {
                return ResourceManager.GetString("CustomerRequirement", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to CRMA Date.
        /// </summary>
        internal static string CustomerRMADate {
            get {
                return ResourceManager.GetString("CustomerRMADate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date.
        /// </summary>
        internal static string Date {
            get {
                return ResourceManager.GetString("Date", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to DC.
        /// </summary>
        internal static string DateCode {
            get {
                return ResourceManager.GetString("DateCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date Codes.
        /// </summary>
        internal static string DateCodes {
            get {
                return ResourceManager.GetString("DateCodes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date Confirmed.
        /// </summary>
        internal static string DateConfirmed {
            get {
                return ResourceManager.GetString("DateConfirmed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delivered.
        /// </summary>
        internal static string DateDelivered {
            get {
                return ResourceManager.GetString("DateDelivered", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Due.
        /// </summary>
        internal static string DateDue {
            get {
                return ResourceManager.GetString("DateDue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invoiced.
        /// </summary>
        internal static string DateInvoiced {
            get {
                return ResourceManager.GetString("DateInvoiced", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Offered.
        /// </summary>
        internal static string DateOffered {
            get {
                return ResourceManager.GetString("DateOffered", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Offered Date.
        /// </summary>
        internal static string DateOffered1 {
            get {
                return ResourceManager.GetString("DateOffered1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date Offered.
        /// </summary>
        internal static string DateOffered2 {
            get {
                return ResourceManager.GetString("DateOffered2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ordered.
        /// </summary>
        internal static string DateOrdered {
            get {
                return ResourceManager.GetString("DateOrdered", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quoted Date.
        /// </summary>
        internal static string DatePOQuoted {
            get {
                return ResourceManager.GetString("DatePOQuoted", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Promised.
        /// </summary>
        internal static string DatePromised {
            get {
                return ResourceManager.GetString("DatePromised", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quoted.
        /// </summary>
        internal static string DateQuoted {
            get {
                return ResourceManager.GetString("DateQuoted", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Received.
        /// </summary>
        internal static string DateReceived {
            get {
                return ResourceManager.GetString("DateReceived", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Received Date.
        /// </summary>
        internal static string DateReceived1 {
            get {
                return ResourceManager.GetString("DateReceived1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date/ReceivedDate.
        /// </summary>
        internal static string DateReceivedDate {
            get {
                return ResourceManager.GetString("DateReceivedDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Required.
        /// </summary>
        internal static string DateRequired {
            get {
                return ResourceManager.GetString("DateRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shipped.
        /// </summary>
        internal static string DateShipped {
            get {
                return ResourceManager.GetString("DateShipped", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date Uploaded.
        /// </summary>
        internal static string DateUploaded {
            get {
                return ResourceManager.GetString("DateUploaded", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Days.
        /// </summary>
        internal static string Days {
            get {
                return ResourceManager.GetString("Days", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to DC.
        /// </summary>
        internal static string DC {
            get {
                return ResourceManager.GetString("DC", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Debit Amount.
        /// </summary>
        internal static string DebitAmount {
            get {
                return ResourceManager.GetString("DebitAmount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Debit Date.
        /// </summary>
        internal static string DebitDate {
            get {
                return ResourceManager.GetString("DebitDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Debit.
        /// </summary>
        internal static string DebitNote {
            get {
                return ResourceManager.GetString("DebitNote", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Debit Note Number.
        /// </summary>
        internal static string DebitNoteNumber {
            get {
                return ResourceManager.GetString("DebitNoteNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Debit Number.
        /// </summary>
        internal static string DebitNumber {
            get {
                return ResourceManager.GetString("DebitNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delivery Date.
        /// </summary>
        internal static string DeliveryDate {
            get {
                return ResourceManager.GetString("DeliveryDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delivery Status.
        /// </summary>
        internal static string DeliveryStatus {
            get {
                return ResourceManager.GetString("DeliveryStatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Description.
        /// </summary>
        internal static string Description {
            get {
                return ResourceManager.GetString("Description", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Descriptions.
        /// </summary>
        internal static string Descriptions {
            get {
                return ResourceManager.GetString("Descriptions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Detail.
        /// </summary>
        internal static string Detail {
            get {
                return ResourceManager.GetString("Detail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Details.
        /// </summary>
        internal static string Details {
            get {
                return ResourceManager.GetString("Details", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Division.
        /// </summary>
        internal static string Division {
            get {
                return ResourceManager.GetString("Division", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Document.
        /// </summary>
        internal static string Document {
            get {
                return ResourceManager.GetString("Document", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to File Size In Byte.
        /// </summary>
        internal static string DocumentSizeByte {
            get {
                return ResourceManager.GetString("DocumentSizeByte", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to File Size In MB.
        /// </summary>
        internal static string DocumentSizeMB {
            get {
                return ResourceManager.GetString("DocumentSizeMB", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Document Type.
        /// </summary>
        internal static string DocumentType {
            get {
                return ResourceManager.GetString("DocumentType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Due.
        /// </summary>
        internal static string Due {
            get {
                return ResourceManager.GetString("Due", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Due Date.
        /// </summary>
        internal static string DueDate {
            get {
                return ResourceManager.GetString("DueDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Duration(Days).
        /// </summary>
        internal static string Duration {
            get {
                return ResourceManager.GetString("Duration", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Duty Code.
        /// </summary>
        internal static string DutyCode {
            get {
                return ResourceManager.GetString("DutyCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ECCN.
        /// </summary>
        internal static string ECCN {
            get {
                return ResourceManager.GetString("ECCN", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ECCN Code.
        /// </summary>
        internal static string ECCNCode {
            get {
                return ResourceManager.GetString("ECCNCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ECCN Description.
        /// </summary>
        internal static string ECCNDescription {
            get {
                return ResourceManager.GetString("ECCNDescription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  Warning Message.
        /// </summary>
        internal static string ECCNWarning {
            get {
                return ResourceManager.GetString("ECCNWarning", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to EEC Member.
        /// </summary>
        internal static string EECMember {
            get {
                return ResourceManager.GetString("EECMember", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Assigned To.
        /// </summary>
        internal static string EI_AssignedTo {
            get {
                return ResourceManager.GetString("EI_AssignedTo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Booking Number.
        /// </summary>
        internal static string EI_BookingNo {
            get {
                return ResourceManager.GetString("EI_BookingNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Email.
        /// </summary>
        internal static string Email {
            get {
                return ResourceManager.GetString("Email", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Email Id.
        /// </summary>
        internal static string EmailId {
            get {
                return ResourceManager.GetString("EmailId", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Buyer.
        /// </summary>
        internal static string employeename {
            get {
                return ResourceManager.GetString("employeename", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Employee Name.
        /// </summary>
        internal static string EmployeeNames {
            get {
                return ResourceManager.GetString("EmployeeNames", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        internal static string Empty {
            get {
                return ResourceManager.GetString("Empty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enhanced Inspection.
        /// </summary>
        internal static string EnhancedInspection {
            get {
                return ResourceManager.GetString("EnhancedInspection", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Entered By.
        /// </summary>
        internal static string EnteredBy {
            get {
                return ResourceManager.GetString("EnteredBy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Entertain Date.
        /// </summary>
        internal static string EntertainDate {
            get {
                return ResourceManager.GetString("EntertainDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Entertain Types.
        /// </summary>
        internal static string EntertainTypes {
            get {
                return ResourceManager.GetString("EntertainTypes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to EST Shipping Cost.
        /// </summary>
        internal static string EstimatedShippingCost {
            get {
                return ResourceManager.GetString("EstimatedShippingCost", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Base Currency.
        /// </summary>
        internal static string EstimatedShippingCostInBase {
            get {
                return ResourceManager.GetString("EstimatedShippingCostInBase", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to EUU Form Required?.
        /// </summary>
        internal static string EUUFormReq {
            get {
                return ResourceManager.GetString("EUUFormReq", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to EUU Form Required?.
        /// </summary>
        internal static string EUUFormRequired {
            get {
                return ResourceManager.GetString("EUUFormRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Exchange Rate.
        /// </summary>
        internal static string ExchangeRate {
            get {
                return ResourceManager.GetString("ExchangeRate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Expedite Date.
        /// </summary>
        internal static string ExpediteDate {
            get {
                return ResourceManager.GetString("ExpediteDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Exp Note.
        /// </summary>
        internal static string Expeditenote {
            get {
                return ResourceManager.GetString("Expeditenote", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Expiry Date.
        /// </summary>
        internal static string ExpiryDate {
            get {
                return ResourceManager.GetString("ExpiryDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Export Approval Status.
        /// </summary>
        internal static string ExportApprovalStatus {
            get {
                return ResourceManager.GetString("ExportApprovalStatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ext.
        /// </summary>
        internal static string Extension {
            get {
                return ResourceManager.GetString("Extension", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Factory Sealed.
        /// </summary>
        internal static string FactorySealed {
            get {
                return ResourceManager.GetString("FactorySealed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Fax.
        /// </summary>
        internal static string Fax {
            get {
                return ResourceManager.GetString("Fax", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to File Name.
        /// </summary>
        internal static string FileName {
            get {
                return ResourceManager.GetString("FileName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Footer Text.
        /// </summary>
        internal static string FooterText {
            get {
                return ResourceManager.GetString("FooterText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Franchised?.
        /// </summary>
        internal static string Franchise {
            get {
                return ResourceManager.GetString("Franchise", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to From.
        /// </summary>
        internal static string From {
            get {
                return ResourceManager.GetString("From", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Full Part.
        /// </summary>
        internal static string FullPart {
            get {
                return ResourceManager.GetString("FullPart", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Goods In.
        /// </summary>
        internal static string GI {
            get {
                return ResourceManager.GetString("GI", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Approvers.
        /// </summary>
        internal static string GIApprovers {
            get {
                return ResourceManager.GetString("GIApprovers", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to GI Date.
        /// </summary>
        internal static string GIDate {
            get {
                return ResourceManager.GetString("GIDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Goods In Number.
        /// </summary>
        internal static string GIGoodInNo {
            get {
                return ResourceManager.GetString("GIGoodInNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to GI Number.
        /// </summary>
        internal static string GINumber {
            get {
                return ResourceManager.GetString("GINumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Status.
        /// </summary>
        internal static string GIStatus {
            get {
                return ResourceManager.GetString("GIStatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier Type.
        /// </summary>
        internal static string GIsupplierType {
            get {
                return ResourceManager.GetString("GIsupplierType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Global Avg Price.
        /// </summary>
        internal static string GlobalAvgPrice {
            get {
                return ResourceManager.GetString("GlobalAvgPrice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Master Country.
        /// </summary>
        internal static string GlobalCountryList {
            get {
                return ResourceManager.GetString("GlobalCountryList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Master Currency.
        /// </summary>
        internal static string GlobalCurrencyList {
            get {
                return ResourceManager.GetString("GlobalCurrencyList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Product Category.
        /// </summary>
        internal static string GlobalProductMainCategory {
            get {
                return ResourceManager.GetString("GlobalProductMainCategory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Product Name.
        /// </summary>
        internal static string GlobalProductName {
            get {
                return ResourceManager.GetString("GlobalProductName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Goods In.
        /// </summary>
        internal static string GoodsIn {
            get {
                return ResourceManager.GetString("GoodsIn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Goods In Date.
        /// </summary>
        internal static string GoodsInDate {
            get {
                return ResourceManager.GetString("GoodsInDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Goods In Note.
        /// </summary>
        internal static string GoodsInNo {
            get {
                return ResourceManager.GetString("GoodsInNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to GP (%).
        /// </summary>
        internal static string GP {
            get {
                return ResourceManager.GetString("GP", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to GP Detail.
        /// </summary>
        internal static string GPDetail {
            get {
                return ResourceManager.GetString("GPDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Gross Profit.
        /// </summary>
        internal static string GrossProfit {
            get {
                return ResourceManager.GetString("GrossProfit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Group.
        /// </summary>
        internal static string Group {
            get {
                return ResourceManager.GetString("Group", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Group Code.
        /// </summary>
        internal static string GroupCode {
            get {
                return ResourceManager.GetString("GroupCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Grouped.
        /// </summary>
        internal static string Grouped {
            get {
                return ResourceManager.GetString("Grouped", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Group Name.
        /// </summary>
        internal static string GroupName {
            get {
                return ResourceManager.GetString("GroupName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to GT.
        /// </summary>
        internal static string GT {
            get {
                return ResourceManager.GetString("GT", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to GT Date Created.
        /// </summary>
        internal static string GTDateCreated {
            get {
                return ResourceManager.GetString("GTDateCreated", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to HTS Code.
        /// </summary>
        internal static string HTCCode {
            get {
                return ResourceManager.GetString("HTCCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to HTS Code.
        /// </summary>
        internal static string HTSCode {
            get {
                return ResourceManager.GetString("HTSCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to HUB PartWatch.
        /// </summary>
        internal static string HUBPartwatch {
            get {
                return ResourceManager.GetString("HUBPartwatch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to HUBRFQ.
        /// </summary>
        internal static string HUBRFQ {
            get {
                return ResourceManager.GetString("HUBRFQ", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to HUBRFQ Code.
        /// </summary>
        internal static string HUBRFQCode {
            get {
                return ResourceManager.GetString("HUBRFQCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to HUBRFQ Name.
        /// </summary>
        internal static string HUBRFQName {
            get {
                return ResourceManager.GetString("HUBRFQName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to HUBRFQ No.
        /// </summary>
        internal static string HUBRFQNo {
            get {
                return ResourceManager.GetString("HUBRFQNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to HUB STATUS.
        /// </summary>
        internal static string HUBSTATUS {
            get {
                return ResourceManager.GetString("HUBSTATUS", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to IHS ID.
        /// </summary>
        internal static string IHSID {
            get {
                return ResourceManager.GetString("IHSID", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Import Date.
        /// </summary>
        internal static string ImportDate {
            get {
                return ResourceManager.GetString("ImportDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Imported By.
        /// </summary>
        internal static string ImportedBy {
            get {
                return ResourceManager.GetString("ImportedBy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Import Status.
        /// </summary>
        internal static string ImportStatus {
            get {
                return ResourceManager.GetString("ImportStatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Inactivate Restricted MFR.
        /// </summary>
        internal static string InactivateRestrictedMFR {
            get {
                return ResourceManager.GetString("InactivateRestrictedMFR", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to InActive.
        /// </summary>
        internal static string InActive {
            get {
                return ResourceManager.GetString("InActive", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to In Advance.
        /// </summary>
        internal static string InAdvance {
            get {
                return ResourceManager.GetString("InAdvance", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Include.
        /// </summary>
        internal static string Include {
            get {
                return ResourceManager.GetString("Include", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Industry Type.
        /// </summary>
        internal static string IndustryType {
            get {
                return ResourceManager.GetString("IndustryType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Inspection Status.
        /// </summary>
        internal static string InspectionStatus {
            get {
                return ResourceManager.GetString("InspectionStatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Insurance Ceriticate Name.
        /// </summary>
        internal static string InsuranceCeriticateName {
            get {
                return ResourceManager.GetString("InsuranceCeriticateName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Insurance Ceriticate Number.
        /// </summary>
        internal static string InsuranceCeriticateNumber {
            get {
                return ResourceManager.GetString("InsuranceCeriticateNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to IPO.
        /// </summary>
        internal static string InternalPurchaseOrder {
            get {
                return ResourceManager.GetString("InternalPurchaseOrder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to IPO.
        /// </summary>
        internal static string InternalPurchaseOrderSerialNo {
            get {
                return ResourceManager.GetString("InternalPurchaseOrderSerialNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invoice.
        /// </summary>
        internal static string Invoice {
            get {
                return ResourceManager.GetString("Invoice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Amount.
        /// </summary>
        internal static string InvoiceAmount {
            get {
                return ResourceManager.GetString("InvoiceAmount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invoice Date.
        /// </summary>
        internal static string InvoiceDate {
            get {
                return ResourceManager.GetString("InvoiceDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invoice No.
        /// </summary>
        internal static string InvoiceNo {
            get {
                return ResourceManager.GetString("InvoiceNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invoice Total.
        /// </summary>
        internal static string InvoiceTotal {
            get {
                return ResourceManager.GetString("InvoiceTotal", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to IP Address.
        /// </summary>
        internal static string IPAddress {
            get {
                return ResourceManager.GetString("IPAddress", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to IPO.
        /// </summary>
        internal static string IPO {
            get {
                return ResourceManager.GetString("IPO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to HUBRFQ.
        /// </summary>
        internal static string IPOBOM {
            get {
                return ResourceManager.GetString("IPOBOM", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to IPO.
        /// </summary>
        internal static string IPOID {
            get {
                return ResourceManager.GetString("IPOID", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to IPO No.
        /// </summary>
        internal static string IPONo {
            get {
                return ResourceManager.GetString("IPONo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to IPO Number.
        /// </summary>
        internal static string IPONumber {
            get {
                return ResourceManager.GetString("IPONumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to IPO Price.
        /// </summary>
        internal static string IPOPrice {
            get {
                return ResourceManager.GetString("IPOPrice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Latest IPO Price.
        /// </summary>
        internal static string IPOPriceWithCur {
            get {
                return ResourceManager.GetString("IPOPriceWithCur", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to IPO Status.
        /// </summary>
        internal static string IPOStatus {
            get {
                return ResourceManager.GetString("IPOStatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Active?.
        /// </summary>
        internal static string IsActive {
            get {
                return ResourceManager.GetString("IsActive", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to IsCancel?.
        /// </summary>
        internal static string IsCancel {
            get {
                return ResourceManager.GetString("IsCancel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to IsClosed?.
        /// </summary>
        internal static string IsClosed {
            get {
                return ResourceManager.GetString("IsClosed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Consignment?.
        /// </summary>
        internal static string IsConsignment {
            get {
                return ResourceManager.GetString("IsConsignment", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Default?.
        /// </summary>
        internal static string IsDefault {
            get {
                return ResourceManager.GetString("IsDefault", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Default Bill?.
        /// </summary>
        internal static string IsDefaultBill {
            get {
                return ResourceManager.GetString("IsDefaultBill", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Default PO?.
        /// </summary>
        internal static string IsDefaultPO {
            get {
                return ResourceManager.GetString("IsDefaultPO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Default PO Ledger?.
        /// </summary>
        internal static string IsDefaultPOLedger {
            get {
                return ResourceManager.GetString("IsDefaultPOLedger", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Default Ship?.
        /// </summary>
        internal static string IsDefaultShip {
            get {
                return ResourceManager.GetString("IsDefaultShip", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Default SO?.
        /// </summary>
        internal static string IsDefaultSO {
            get {
                return ResourceManager.GetString("IsDefaultSO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Default SO Ledger?.
        /// </summary>
        internal static string IsDefaultSOLedger {
            get {
                return ResourceManager.GetString("IsDefaultSOLedger", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Finance Contact?.
        /// </summary>
        internal static string IsFinanceContact {
            get {
                return ResourceManager.GetString("IsFinanceContact", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Notified?.
        /// </summary>
        internal static string IsMailSent {
            get {
                return ResourceManager.GetString("IsMailSent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to On Hold?.
        /// </summary>
        internal static string IsOnHold {
            get {
                return ResourceManager.GetString("IsOnHold", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PoHub.
        /// </summary>
        internal static string IsPoHub {
            get {
                return ResourceManager.GetString("IsPoHub", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Printed?.
        /// </summary>
        internal static string IsPrinted {
            get {
                return ResourceManager.GetString("IsPrinted", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Credit refund given?.
        /// </summary>
        internal static string IsShortageRefundIssue {
            get {
                return ResourceManager.GetString("IsShortageRefundIssue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Virtual?.
        /// </summary>
        internal static string IsVirtual {
            get {
                return ResourceManager.GetString("IsVirtual", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Item.
        /// </summary>
        internal static string Item {
            get {
                return ResourceManager.GetString("Item", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Job Title.
        /// </summary>
        internal static string JobTitle {
            get {
                return ResourceManager.GetString("JobTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Nice Label Path.
        /// </summary>
        internal static string LabelPath {
            get {
                return ResourceManager.GetString("LabelPath", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Status name.
        /// </summary>
        internal static string LabelSetupName {
            get {
                return ResourceManager.GetString("LabelSetupName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Landed Cost.
        /// </summary>
        internal static string LandedCost {
            get {
                return ResourceManager.GetString("LandedCost", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Last Contacted.
        /// </summary>
        internal static string LastContacted {
            get {
                return ResourceManager.GetString("LastContacted", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Last Received.
        /// </summary>
        internal static string LastReceived {
            get {
                return ResourceManager.GetString("LastReceived", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Last Run.
        /// </summary>
        internal static string LastRun {
            get {
                return ResourceManager.GetString("LastRun", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Last Visited.
        /// </summary>
        internal static string LastVisited {
            get {
                return ResourceManager.GetString("LastVisited", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lead Time (Weeks).
        /// </summary>
        internal static string LeadTime {
            get {
                return ResourceManager.GetString("LeadTime", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lead Time.
        /// </summary>
        internal static string LeatTime {
            get {
                return ResourceManager.GetString("LeatTime", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Life Cycle Stage.
        /// </summary>
        internal static string LifeCycleStage {
            get {
                return ResourceManager.GetString("LifeCycleStage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Line Mgr.
        /// </summary>
        internal static string LineMgrApprovalStatus {
            get {
                return ResourceManager.GetString("LineMgrApprovalStatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Line No.
        /// </summary>
        internal static string LineNo {
            get {
                return ResourceManager.GetString("LineNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        internal static string Link {
            get {
                return ResourceManager.GetString("Link", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to List Name.
        /// </summary>
        internal static string ListName {
            get {
                return ResourceManager.GetString("ListName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Local Currency.
        /// </summary>
        internal static string LocalCurrency {
            get {
                return ResourceManager.GetString("LocalCurrency", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Location.
        /// </summary>
        internal static string Location {
            get {
                return ResourceManager.GetString("Location", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Log By User.
        /// </summary>
        internal static string LogByUser {
            get {
                return ResourceManager.GetString("LogByUser", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Log Date.
        /// </summary>
        internal static string LogDate {
            get {
                return ResourceManager.GetString("LogDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Login.
        /// </summary>
        internal static string Login {
            get {
                return ResourceManager.GetString("Login", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Login Name.
        /// </summary>
        internal static string LoginName {
            get {
                return ResourceManager.GetString("LoginName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier Name.
        /// </summary>
        internal static string LogMessageSupplier {
            get {
                return ResourceManager.GetString("LogMessageSupplier", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lot.
        /// </summary>
        internal static string Lot {
            get {
                return ResourceManager.GetString("Lot", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to &lt;input type=&apos;checkbox&apos; id=&apos;chkdtoffer1&apos; name=&apos;select_offer&apos;/&gt;.
        /// </summary>
        internal static string LotCheckboxHeader {
            get {
                return ResourceManager.GetString("LotCheckboxHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lot Code.
        /// </summary>
        internal static string LotNo {
            get {
                return ResourceManager.GetString("LotNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to LTB.
        /// </summary>
        internal static string LTB {
            get {
                return ResourceManager.GetString("LTB", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Manager.
        /// </summary>
        internal static string Manager {
            get {
                return ResourceManager.GetString("Manager", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Manufacturer.
        /// </summary>
        internal static string Manufacturer {
            get {
                return ResourceManager.GetString("Manufacturer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mfr.
        /// </summary>
        internal static string ManufacturerAbbreviation {
            get {
                return ResourceManager.GetString("ManufacturerAbbreviation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Manufacturer Name.
        /// </summary>
        internal static string ManufacturerName {
            get {
                return ResourceManager.GetString("ManufacturerName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Manufacturer.
        /// </summary>
        internal static string ManufacturerNo {
            get {
                return ResourceManager.GetString("ManufacturerNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Margin.
        /// </summary>
        internal static string Margin {
            get {
                return ResourceManager.GetString("Margin", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Margin %.
        /// </summary>
        internal static string MarginPercentage {
            get {
                return ResourceManager.GetString("MarginPercentage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Margin Value.
        /// </summary>
        internal static string MarginValue {
            get {
                return ResourceManager.GetString("MarginValue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Members.
        /// </summary>
        internal static string Members {
            get {
                return ResourceManager.GetString("Members", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Log Message.
        /// </summary>
        internal static string Message {
            get {
                return ResourceManager.GetString("Message", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to MFR.
        /// </summary>
        internal static string MFR {
            get {
                return ResourceManager.GetString("MFR", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to MFR Name Suffix.
        /// </summary>
        internal static string MFRNameSuffix {
            get {
                return ResourceManager.GetString("MFRNameSuffix", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mobile.
        /// </summary>
        internal static string Mobile {
            get {
                return ResourceManager.GetString("Mobile", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to MOQ.
        /// </summary>
        internal static string MOQ {
            get {
                return ResourceManager.GetString("MOQ", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to MSL.
        /// </summary>
        internal static string MSL {
            get {
                return ResourceManager.GetString("MSL", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Name.
        /// </summary>
        internal static string Name {
            get {
                return ResourceManager.GetString("Name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Name/Number.
        /// </summary>
        internal static string NameNumber {
            get {
                return ResourceManager.GetString("NameNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Narrative.
        /// </summary>
        internal static string Narrative {
            get {
                return ResourceManager.GetString("Narrative", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to New Landed Cost.
        /// </summary>
        internal static string NewLandedCost {
            get {
                return ResourceManager.GetString("NewLandedCost", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Next Number.
        /// </summary>
        internal static string NextNumber {
            get {
                return ResourceManager.GetString("NextNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No Of Requirement.
        /// </summary>
        internal static string NoOfRequirement {
            get {
                return ResourceManager.GetString("NoOfRequirement", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Notes.
        /// </summary>
        internal static string Notes {
            get {
                return ResourceManager.GetString("Notes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Notes/MSL.
        /// </summary>
        internal static string NotesMSL {
            get {
                return ResourceManager.GetString("NotesMSL", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to NPR.
        /// </summary>
        internal static string NPR {
            get {
                return ResourceManager.GetString("NPR", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to NPR Number.
        /// </summary>
        internal static string NprNo {
            get {
                return ResourceManager.GetString("NprNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to NPR Raised Date.
        /// </summary>
        internal static string NprRaisedDateFrom {
            get {
                return ResourceManager.GetString("NprRaisedDateFrom", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No.
        /// </summary>
        internal static string Number {
            get {
                return ResourceManager.GetString("Number", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Number of Packs.
        /// </summary>
        internal static string NumberOfPacks {
            get {
                return ResourceManager.GetString("NumberOfPacks", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Users.
        /// </summary>
        internal static string NumberOfUsers {
            get {
                return ResourceManager.GetString("NumberOfUsers", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Number of Purchase Order.
        /// </summary>
        internal static string NumOfPO {
            get {
                return ResourceManager.GetString("NumOfPO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Offer Price.
        /// </summary>
        internal static string OfferPrice {
            get {
                return ResourceManager.GetString("OfferPrice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to OGEL.
        /// </summary>
        internal static string OGEL {
            get {
                return ResourceManager.GetString("OGEL", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to OGEL ID.
        /// </summary>
        internal static string OgelId {
            get {
                return ResourceManager.GetString("OgelId", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to OGEL License Required?.
        /// </summary>
        internal static string OgelLicReq {
            get {
                return ResourceManager.GetString("OgelLicReq", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to OGEL Number.
        /// </summary>
        internal static string OGELNumber {
            get {
                return ResourceManager.GetString("OGELNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Destination Country.
        /// </summary>
        internal static string OGEL_EndDestinationCountry {
            get {
                return ResourceManager.GetString("OGEL_EndDestinationCountry", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to IsActive.
        /// </summary>
        internal static string OGEL_InActive {
            get {
                return ResourceManager.GetString("OGEL_InActive", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Military Use.
        /// </summary>
        internal static string OGEL_MilitaryUse {
            get {
                return ResourceManager.GetString("OGEL_MilitaryUse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Old Value.
        /// </summary>
        internal static string OldValue {
            get {
                return ResourceManager.GetString("OldValue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open Orders Repriced?.
        /// </summary>
        internal static string OpenOrdersRepriced {
            get {
                return ResourceManager.GetString("OpenOrdersRepriced", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Order Value.
        /// </summary>
        internal static string OrderValue {
            get {
                return ResourceManager.GetString("OrderValue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Original Landed Cost.
        /// </summary>
        internal static string OriginalLandedCost {
            get {
                return ResourceManager.GetString("OriginalLandedCost", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pack.
        /// </summary>
        internal static string Pack {
            get {
                return ResourceManager.GetString("Pack", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Package.
        /// </summary>
        internal static string Package {
            get {
                return ResourceManager.GetString("Package", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Package Method.
        /// </summary>
        internal static string PackageMethod {
            get {
                return ResourceManager.GetString("PackageMethod", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Packaging.
        /// </summary>
        internal static string Packaging {
            get {
                return ResourceManager.GetString("Packaging", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Packaging Breakdown Type.
        /// </summary>
        internal static string PackagingBreakdownType {
            get {
                return ResourceManager.GetString("PackagingBreakdownType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Packaging Code.
        /// </summary>
        internal static string PackagingCode {
            get {
                return ResourceManager.GetString("PackagingCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Packaging Size.
        /// </summary>
        internal static string PackagingSize {
            get {
                return ResourceManager.GetString("PackagingSize", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Packaging Type.
        /// </summary>
        internal static string PackagingType {
            get {
                return ResourceManager.GetString("PackagingType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Packing Code.
        /// </summary>
        internal static string PackingCode {
            get {
                return ResourceManager.GetString("PackingCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Size.
        /// </summary>
        internal static string PackSize {
            get {
                return ResourceManager.GetString("PackSize", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Part.
        /// </summary>
        internal static string Part {
            get {
                return ResourceManager.GetString("Part", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Part No.
        /// </summary>
        internal static string PartNo {
            get {
                return ResourceManager.GetString("PartNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Part Status.
        /// </summary>
        internal static string PartStatus {
            get {
                return ResourceManager.GetString("PartStatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to HUB PartWatch Match.
        /// </summary>
        internal static string PartWatchMatch {
            get {
                return ResourceManager.GetString("PartWatchMatch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Document.
        /// </summary>
        internal static string PDFDocument {
            get {
                return ResourceManager.GetString("PDFDocument", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to (Click to view &amp; add docs).
        /// </summary>
        internal static string PDFIHSDOCtitle {
            get {
                return ResourceManager.GetString("PDFIHSDOCtitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Physical Inspected By.
        /// </summary>
        internal static string PIBy {
            get {
                return ResourceManager.GetString("PIBy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PO | IPO No..
        /// </summary>
        internal static string POIPONo {
            get {
                return ResourceManager.GetString("POIPONo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PO Line Count.
        /// </summary>
        internal static string POLineCount {
            get {
                return ResourceManager.GetString("POLineCount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PO Line No.
        /// </summary>
        internal static string POLineNos {
            get {
                return ResourceManager.GetString("POLineNos", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Purchase Order Number.
        /// </summary>
        internal static string PONo {
            get {
                return ResourceManager.GetString("PONo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PO|IPO (Line No).
        /// </summary>
        internal static string PoOrIpo {
            get {
                return ResourceManager.GetString("PoOrIpo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PO Request.
        /// </summary>
        internal static string POQuote {
            get {
                return ResourceManager.GetString("POQuote", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Posted Lines Value.
        /// </summary>
        internal static string PostedValue {
            get {
                return ResourceManager.GetString("PostedValue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PQ Id.
        /// </summary>
        internal static string PQID {
            get {
                return ResourceManager.GetString("PQID", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unit Price.
        /// </summary>
        internal static string Price {
            get {
                return ResourceManager.GetString("Price", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Price To Client.
        /// </summary>
        internal static string PriceToClient {
            get {
                return ResourceManager.GetString("PriceToClient", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Printable DC.
        /// </summary>
        internal static string PrintDateCode {
            get {
                return ResourceManager.GetString("PrintDateCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Printer.
        /// </summary>
        internal static string Printer {
            get {
                return ResourceManager.GetString("Printer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Priority.
        /// </summary>
        internal static string Priority {
            get {
                return ResourceManager.GetString("Priority", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Product.
        /// </summary>
        internal static string Product {
            get {
                return ResourceManager.GetString("Product", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Prod / Pack.
        /// </summary>
        internal static string ProductAndPackage {
            get {
                return ResourceManager.GetString("ProductAndPackage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Profit.
        /// </summary>
        internal static string Profit {
            get {
                return ResourceManager.GetString("Profit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Profit (%).
        /// </summary>
        internal static string ProfitPercentage {
            get {
                return ResourceManager.GetString("ProfitPercentage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Promise Date.
        /// </summary>
        internal static string PromiseDate {
            get {
                return ResourceManager.GetString("PromiseDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Source.
        /// </summary>
        internal static string ProspectiveOfferSource {
            get {
                return ResourceManager.GetString("ProspectiveOfferSource", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Prospects Qualification.
        /// </summary>
        internal static string ProspectQualification {
            get {
                return ResourceManager.GetString("ProspectQualification", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Publish Date.
        /// </summary>
        internal static string PublishDate {
            get {
                return ResourceManager.GetString("PublishDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Purchase Code.
        /// </summary>
        internal static string PurchaseCode {
            get {
                return ResourceManager.GetString("PurchaseCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PO No.
        /// </summary>
        internal static string PurchaseNo {
            get {
                return ResourceManager.GetString("PurchaseNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PO.
        /// </summary>
        internal static string PurchaseOrder {
            get {
                return ResourceManager.GetString("PurchaseOrder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PO Date.
        /// </summary>
        internal static string PurchaseOrderDate {
            get {
                return ResourceManager.GetString("PurchaseOrderDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PurchaseOrder(IPO).
        /// </summary>
        internal static string PurchaseOrderIPO {
            get {
                return ResourceManager.GetString("PurchaseOrderIPO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PO No.
        /// </summary>
        internal static string PurchaseOrderNo {
            get {
                return ResourceManager.GetString("PurchaseOrderNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PO Number.
        /// </summary>
        internal static string PurchaseOrderNumber {
            get {
                return ResourceManager.GetString("PurchaseOrderNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PO (Line No).
        /// </summary>
        internal static string PurchaseOrderSerialNo {
            get {
                return ResourceManager.GetString("PurchaseOrderSerialNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Purchase Request.
        /// </summary>
        internal static string PurchasePrice {
            get {
                return ResourceManager.GetString("PurchasePrice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Price Request.
        /// </summary>
        internal static string PurchaseRequestNo {
            get {
                return ResourceManager.GetString("PurchaseRequestNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Purchase Requisition.
        /// </summary>
        internal static string PurchaseRequisition {
            get {
                return ResourceManager.GetString("PurchaseRequisition", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Qty Unallocated (In Stock/On Order).
        /// </summary>
        internal static string QtyUnallocatedAll {
            get {
                return ResourceManager.GetString("QtyUnallocatedAll", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Qty Unallocated (On Order).
        /// </summary>
        internal static string QtyUnallocatedOnOrder {
            get {
                return ResourceManager.GetString("QtyUnallocatedOnOrder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Qty Unallocated (In Stock).
        /// </summary>
        internal static string QtyUnallocatedStock {
            get {
                return ResourceManager.GetString("QtyUnallocatedStock", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quality .
        /// </summary>
        internal static string QualityApprovalStatus {
            get {
                return ResourceManager.GetString("QualityApprovalStatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Notes.
        /// </summary>
        internal static string QualityControlNotes {
            get {
                return ResourceManager.GetString("QualityControlNotes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quantity.
        /// </summary>
        internal static string Quantity {
            get {
                return ResourceManager.GetString("Quantity", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Qty Allocated.
        /// </summary>
        internal static string QuantityAllocated {
            get {
                return ResourceManager.GetString("QuantityAllocated", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Qty Authorised.
        /// </summary>
        internal static string QuantityAuthorised {
            get {
                return ResourceManager.GetString("QuantityAuthorised", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Qty Available.
        /// </summary>
        internal static string QuantityAvailable {
            get {
                return ResourceManager.GetString("QuantityAvailable", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Qty BackOrder.
        /// </summary>
        internal static string QuantityBackOrder {
            get {
                return ResourceManager.GetString("QuantityBackOrder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Qty In Stock(FREE).
        /// </summary>
        internal static string QuantityInStock {
            get {
                return ResourceManager.GetString("QuantityInStock", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Qty In Stock.
        /// </summary>
        internal static string QuantityInStockForSO {
            get {
                return ResourceManager.GetString("QuantityInStockForSO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Qty On Order.
        /// </summary>
        internal static string QuantityOnOrder {
            get {
                return ResourceManager.GetString("QuantityOnOrder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Qty Ordered.
        /// </summary>
        internal static string QuantityOrdered {
            get {
                return ResourceManager.GetString("QuantityOrdered", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Qty Outstanding.
        /// </summary>
        internal static string QuantityOutstanding {
            get {
                return ResourceManager.GetString("QuantityOutstanding", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Qty Received.
        /// </summary>
        internal static string QuantityReceived {
            get {
                return ResourceManager.GetString("QuantityReceived", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quantity Rejected.
        /// </summary>
        internal static string QuantityRejected {
            get {
                return ResourceManager.GetString("QuantityRejected", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Qty Remaining.
        /// </summary>
        internal static string QuantityRemaining {
            get {
                return ResourceManager.GetString("QuantityRemaining", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Qty Shipped.
        /// </summary>
        internal static string QuantityShipped {
            get {
                return ResourceManager.GetString("QuantityShipped", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quarantined.
        /// </summary>
        internal static string Quarantined {
            get {
                return ResourceManager.GetString("Quarantined", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Qtnd?.
        /// </summary>
        internal static string QuarantinedAbbreviation {
            get {
                return ResourceManager.GetString("QuarantinedAbbreviation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Query Date.
        /// </summary>
        internal static string QueryDate {
            get {
                return ResourceManager.GetString("QueryDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Question.
        /// </summary>
        internal static string Question {
            get {
                return ResourceManager.GetString("Question", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quote.
        /// </summary>
        internal static string Quote {
            get {
                return ResourceManager.GetString("Quote", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quote Offered Date.
        /// </summary>
        internal static string QuoteOfferedDate {
            get {
                return ResourceManager.GetString("QuoteOfferedDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quote Number.
        /// </summary>
        internal static string QuoteNumber {
            get {
                return ResourceManager.GetString("QuoteNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quote Required.
        /// </summary>
        internal static string QuoteRequired {
            get {
                return ResourceManager.GetString("QuoteRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Raised By.
        /// </summary>
        internal static string RaisedBy {
            get {
                return ResourceManager.GetString("RaisedBy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Rate.
        /// </summary>
        internal static string Rate {
            get {
                return ResourceManager.GetString("Rate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Rate 2.
        /// </summary>
        internal static string Rate2 {
            get {
                return ResourceManager.GetString("Rate2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reason.
        /// </summary>
        internal static string Reason {
            get {
                return ResourceManager.GetString("Reason", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reason Note.
        /// </summary>
        internal static string ReasonNote {
            get {
                return ResourceManager.GetString("ReasonNote", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Received.
        /// </summary>
        internal static string Received {
            get {
                return ResourceManager.GetString("Received", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Received By.
        /// </summary>
        internal static string ReceivedBy {
            get {
                return ResourceManager.GetString("ReceivedBy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Received Date.
        /// </summary>
        internal static string ReceivedDate {
            get {
                return ResourceManager.GetString("ReceivedDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Received PO Status.
        /// </summary>
        internal static string ReceivePOStatus {
            get {
                return ResourceManager.GetString("ReceivePOStatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Received By.
        /// </summary>
        internal static string Receiver {
            get {
                return ResourceManager.GetString("Receiver", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Records Processed.
        /// </summary>
        internal static string RecordsProcessed {
            get {
                return ResourceManager.GetString("RecordsProcessed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Records Remaining.
        /// </summary>
        internal static string RecordsRemaining {
            get {
                return ResourceManager.GetString("RecordsRemaining", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reference.
        /// </summary>
        internal static string Reference {
            get {
                return ResourceManager.GetString("Reference", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Region.
        /// </summary>
        internal static string Region {
            get {
                return ResourceManager.GetString("Region", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Region Name.
        /// </summary>
        internal static string RegionName {
            get {
                return ResourceManager.GetString("RegionName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Related Quotes.
        /// </summary>
        internal static string RelatedQuotes {
            get {
                return ResourceManager.GetString("RelatedQuotes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Release Price.
        /// </summary>
        internal static string ReleasePrice {
            get {
                return ResourceManager.GetString("ReleasePrice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Report Name.
        /// </summary>
        internal static string ReportName {
            get {
                return ResourceManager.GetString("ReportName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to BOM Name.
        /// </summary>
        internal static string ReqBOMName {
            get {
                return ResourceManager.GetString("ReqBOMName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Req No.
        /// </summary>
        internal static string ReqNo {
            get {
                return ResourceManager.GetString("ReqNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to REQ Line Status.
        /// </summary>
        internal static string REQStatus {
            get {
                return ResourceManager.GetString("REQStatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Requested.
        /// </summary>
        internal static string Requested {
            get {
                return ResourceManager.GetString("Requested", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Requested By.
        /// </summary>
        internal static string RequestedBy {
            get {
                return ResourceManager.GetString("RequestedBy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Requested Date.
        /// </summary>
        internal static string RequestedDate {
            get {
                return ResourceManager.GetString("RequestedDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Request No .
        /// </summary>
        internal static string RequestNo {
            get {
                return ResourceManager.GetString("RequestNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Require ASAP.
        /// </summary>
        internal static string RequireASAP {
            get {
                return ResourceManager.GetString("RequireASAP", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Required.
        /// </summary>
        internal static string Required {
            get {
                return ResourceManager.GetString("Required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to HUBRFQ Required Date.
        /// </summary>
        internal static string RequiredDate {
            get {
                return ResourceManager.GetString("RequiredDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Req.
        /// </summary>
        internal static string Requirement {
            get {
                return ResourceManager.GetString("Requirement", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Resale.
        /// </summary>
        internal static string Resale {
            get {
                return ResourceManager.GetString("Resale", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Resale Price.
        /// </summary>
        internal static string ResalePrice {
            get {
                return ResourceManager.GetString("ResalePrice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Return Date.
        /// </summary>
        internal static string ReturnDate {
            get {
                return ResourceManager.GetString("ReturnDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to RMAs Last 12 Months.
        /// </summary>
        internal static string RMAsTwlMonths {
            get {
                return ResourceManager.GetString("RMAsTwlMonths", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ROHS.
        /// </summary>
        internal static string ROHS {
            get {
                return ResourceManager.GetString("ROHS", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ROHS Status.
        /// </summary>
        internal static string RoHSStatus {
            get {
                return ResourceManager.GetString("RoHSStatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Row.
        /// </summary>
        internal static string RowCount {
            get {
                return ResourceManager.GetString("RowCount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Approvals Status.
        /// </summary>
        internal static string SAApprovalsStatus {
            get {
                return ResourceManager.GetString("SAApprovalsStatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to By (Date Time).
        /// </summary>
        internal static string SABy {
            get {
                return ResourceManager.GetString("SABy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PO Date Ordered.
        /// </summary>
        internal static string SaDateOrdered {
            get {
                return ResourceManager.GetString("SaDateOrdered", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Device Pictures Attachment.
        /// </summary>
        internal static string SADevicePicture {
            get {
                return ResourceManager.GetString("SADevicePicture", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sales Code.
        /// </summary>
        internal static string SalesCode {
            get {
                return ResourceManager.GetString("SalesCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Salesperson.
        /// </summary>
        internal static string Salesman {
            get {
                return ResourceManager.GetString("Salesman", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SO.
        /// </summary>
        internal static string SalesOrder {
            get {
                return ResourceManager.GetString("SalesOrder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SO (Line No).
        /// </summary>
        internal static string SalesOrderSerialNo {
            get {
                return ResourceManager.GetString("SalesOrderSerialNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Salesperson.
        /// </summary>
        internal static string Salesperson {
            get {
                return ResourceManager.GetString("Salesperson", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sales Value.
        /// </summary>
        internal static string SalesValue {
            get {
                return ResourceManager.GetString("SalesValue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Line Manager Approval.
        /// </summary>
        internal static string SALnMngrApproval {
            get {
                return ResourceManager.GetString("SALnMngrApproval", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Manufacturer Lebel Picture Attachment.
        /// </summary>
        internal static string SAManufaturerPicture {
            get {
                return ResourceManager.GetString("SAManufaturerPicture", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Previous Supplier.
        /// </summary>
        internal static string SAPreviousSupplier {
            get {
                return ResourceManager.GetString("SAPreviousSupplier", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Purchasing Method.
        /// </summary>
        internal static string SAPurchaseMethod {
            get {
                return ResourceManager.GetString("SAPurchaseMethod", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quality Approval.
        /// </summary>
        internal static string SAQtyApproved {
            get {
                return ResourceManager.GetString("SAQtyApproved", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Attachment.
        /// </summary>
        internal static string SARefAttachment {
            get {
                return ResourceManager.GetString("SARefAttachment", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Traceablity Picture Attachment.
        /// </summary>
        internal static string SATracablityPicture {
            get {
                return ResourceManager.GetString("SATracablityPicture", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Trade Reference 1.
        /// </summary>
        internal static string SATradeRefOne {
            get {
                return ResourceManager.GetString("SATradeRefOne", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Trade Reference 3.
        /// </summary>
        internal static string SATradeRefThree {
            get {
                return ResourceManager.GetString("SATradeRefThree", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Trade Reference 2.
        /// </summary>
        internal static string SATradeRefTwo {
            get {
                return ResourceManager.GetString("SATradeRefTwo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Franchise Weblink or Evidence.
        /// </summary>
        internal static string SAWebLnk {
            get {
                return ResourceManager.GetString("SAWebLnk", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Start Date.
        /// </summary>
        internal static string SchedularStartDate {
            get {
                return ResourceManager.GetString("SchedularStartDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SecondRef.
        /// </summary>
        internal static string SecondRef {
            get {
                return ResourceManager.GetString("SecondRef", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Group.
        /// </summary>
        internal static string SecurityGroup {
            get {
                return ResourceManager.GetString("SecurityGroup", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Group Description.
        /// </summary>
        internal static string SecurityGroupDiscription {
            get {
                return ResourceManager.GetString("SecurityGroupDiscription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to                .
        /// </summary>
        internal static string Select {
            get {
                return ResourceManager.GetString("Select", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sell.
        /// </summary>
        internal static string Sell {
            get {
                return ResourceManager.GetString("Sell", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sell Exchange Rate.
        /// </summary>
        internal static string SellExchangeRate {
            get {
                return ResourceManager.GetString("SellExchangeRate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Selling Price.
        /// </summary>
        internal static string SellingPrice {
            get {
                return ResourceManager.GetString("SellingPrice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sell Price.
        /// </summary>
        internal static string SellPrice {
            get {
                return ResourceManager.GetString("SellPrice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Send To Group.
        /// </summary>
        internal static string SendToGroup {
            get {
                return ResourceManager.GetString("SendToGroup", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Serial No..
        /// </summary>
        internal static string SerialNo {
            get {
                return ResourceManager.GetString("SerialNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Server IP.
        /// </summary>
        internal static string ServerIP {
            get {
                return ResourceManager.GetString("ServerIP", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Service.
        /// </summary>
        internal static string Service {
            get {
                return ResourceManager.GetString("Service", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Warning Name.
        /// </summary>
        internal static string SetWarnings {
            get {
                return ResourceManager.GetString("SetWarnings", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Warning Message.
        /// </summary>
        internal static string SetWarningText {
            get {
                return ResourceManager.GetString("SetWarningText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ship ASAP.
        /// </summary>
        internal static string ShipAsap {
            get {
                return ResourceManager.GetString("ShipAsap", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ship Date.
        /// </summary>
        internal static string ShipDate {
            get {
                return ResourceManager.GetString("ShipDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ship In Cost.
        /// </summary>
        internal static string ShipInCost {
            get {
                return ResourceManager.GetString("ShipInCost", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shipped By.
        /// </summary>
        internal static string ShippedBy {
            get {
                return ResourceManager.GetString("ShippedBy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shipper.
        /// </summary>
        internal static string Shipper {
            get {
                return ResourceManager.GetString("Shipper", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shipping Cost.
        /// </summary>
        internal static string ShippingCost {
            get {
                return ResourceManager.GetString("ShippingCost", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shipping Instructions.
        /// </summary>
        internal static string ShippingInstructions {
            get {
                return ResourceManager.GetString("ShippingInstructions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ship Status.
        /// </summary>
        internal static string ShipStatus {
            get {
                return ResourceManager.GetString("ShipStatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shipping Method.
        /// </summary>
        internal static string ShipVia {
            get {
                return ResourceManager.GetString("ShipVia", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ship Via .
        /// </summary>
        internal static string ShipViaName {
            get {
                return ResourceManager.GetString("ShipViaName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shortage Qty.
        /// </summary>
        internal static string ShortageQuantity {
            get {
                return ResourceManager.GetString("ShortageQuantity", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Short Shipment Id.
        /// </summary>
        internal static string ShortShipmentId {
            get {
                return ResourceManager.GetString("ShortShipmentId", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shipment Number.
        /// </summary>
        internal static string ShortShipmentNo {
            get {
                return ResourceManager.GetString("ShortShipmentNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shortage Value.
        /// </summary>
        internal static string ShortValue {
            get {
                return ResourceManager.GetString("ShortValue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Show Exchange Rate.
        /// </summary>
        internal static string ShowExchangeRate {
            get {
                return ResourceManager.GetString("ShowExchangeRate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SO Line No.
        /// </summary>
        internal static string SOLineNo {
            get {
                return ResourceManager.GetString("SOLineNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SO Line Number.
        /// </summary>
        internal static string SOLineNumber {
            get {
                return ResourceManager.GetString("SOLineNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SO Number.
        /// </summary>
        internal static string SONumber {
            get {
                return ResourceManager.GetString("SONumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Prevent SOR sign OFF.
        /// </summary>
        internal static string SOR {
            get {
                return ResourceManager.GetString("SOR", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Source Client.
        /// </summary>
        internal static string SourceClient {
            get {
                return ResourceManager.GetString("SourceClient", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sourcing Type.
        /// </summary>
        internal static string SourcingType {
            get {
                return ResourceManager.GetString("SourcingType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SPQ.
        /// </summary>
        internal static string SPQ {
            get {
                return ResourceManager.GetString("SPQ", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SRMA.
        /// </summary>
        internal static string SRMA {
            get {
                return ResourceManager.GetString("SRMA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SRMA No.
        /// </summary>
        internal static string SRMANo {
            get {
                return ResourceManager.GetString("SRMANo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PO Line No.
        /// </summary>
        internal static string SRMPPOSerialNo {
            get {
                return ResourceManager.GetString("SRMPPOSerialNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Start Date.
        /// </summary>
        internal static string StartDate {
            get {
                return ResourceManager.GetString("StartDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Start Time.
        /// </summary>
        internal static string StartTime {
            get {
                return ResourceManager.GetString("StartTime", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Status.
        /// </summary>
        internal static string Status {
            get {
                return ResourceManager.GetString("Status", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Status/Salesman.
        /// </summary>
        internal static string StatusSalesman {
            get {
                return ResourceManager.GetString("StatusSalesman", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Stock Count.
        /// </summary>
        internal static string StockCount {
            get {
                return ResourceManager.GetString("StockCount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Box #.
        /// </summary>
        internal static string SubGroup {
            get {
                return ResourceManager.GetString("SubGroup", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Subject.
        /// </summary>
        internal static string Subject {
            get {
                return ResourceManager.GetString("Subject", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier.
        /// </summary>
        internal static string Supplier {
            get {
                return ResourceManager.GetString("Supplier", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier Code.
        /// </summary>
        internal static string SupplierCode {
            get {
                return ResourceManager.GetString("SupplierCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier Invoice.
        /// </summary>
        internal static string SupplierInvoice {
            get {
                return ResourceManager.GetString("SupplierInvoice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Action.
        /// </summary>
        internal static string SupplierInvoiceAction {
            get {
                return ResourceManager.GetString("SupplierInvoiceAction", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Decision.
        /// </summary>
        internal static string SupplierInvoiceDecision {
            get {
                return ResourceManager.GetString("SupplierInvoiceDecision", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Item Count.
        /// </summary>
        internal static string SupplierInvoiceItemCount {
            get {
                return ResourceManager.GetString("SupplierInvoiceItemCount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to &lt;input type=&quot;checkbox&quot; id=&quot;SelectALLSupplierInvoice&quot;&gt;.
        /// </summary>
        internal static string SupplierInvoiceSelectAll {
            get {
                return ResourceManager.GetString("SupplierInvoiceSelectAll", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Summary.
        /// </summary>
        internal static string SupplierInvoiceSummary {
            get {
                return ResourceManager.GetString("SupplierInvoiceSummary", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier Notes.
        /// </summary>
        internal static string SupplierNotes {
            get {
                return ResourceManager.GetString("SupplierNotes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier Part.
        /// </summary>
        internal static string SupplierPart {
            get {
                return ResourceManager.GetString("SupplierPart", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Rating.
        /// </summary>
        internal static string SupplierRating {
            get {
                return ResourceManager.GetString("SupplierRating", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier RMA Date.
        /// </summary>
        internal static string SupplierRMADate {
            get {
                return ResourceManager.GetString("SupplierRMADate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier RMA No.
        /// </summary>
        internal static string SupplierRMANo {
            get {
                return ResourceManager.GetString("SupplierRMANo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier Type.
        /// </summary>
        internal static string SupplierType {
            get {
                return ResourceManager.GetString("SupplierType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier ( Type ).
        /// </summary>
        internal static string SupplierTypeHeading {
            get {
                return ResourceManager.GetString("SupplierTypeHeading", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Support Team Member.
        /// </summary>
        internal static string SupportTeamMember {
            get {
                return ResourceManager.GetString("SupportTeamMember", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Symbol.
        /// </summary>
        internal static string Symbol {
            get {
                return ResourceManager.GetString("Symbol", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to API Imported Data.
        /// </summary>
        internal static string SystemManufacturer {
            get {
                return ResourceManager.GetString("SystemManufacturer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Activity.
        /// </summary>
        internal static string Table {
            get {
                return ResourceManager.GetString("Table", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Target Price.
        /// </summary>
        internal static string TargetPrice {
            get {
                return ResourceManager.GetString("TargetPrice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Task.
        /// </summary>
        internal static string Task {
            get {
                return ResourceManager.GetString("Task", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Task Category.
        /// </summary>
        internal static string TaskCategory {
            get {
                return ResourceManager.GetString("TaskCategory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Task Date From.
        /// </summary>
        internal static string TaskDateFrom {
            get {
                return ResourceManager.GetString("TaskDateFrom", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Task Date To.
        /// </summary>
        internal static string TaskDateTo {
            get {
                return ResourceManager.GetString("TaskDateTo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reference.
        /// </summary>
        internal static string TaskReference {
            get {
                return ResourceManager.GetString("TaskReference", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Task Reminder Date.
        /// </summary>
        internal static string TaskReminderDate {
            get {
                return ResourceManager.GetString("TaskReminderDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Task Status.
        /// </summary>
        internal static string TaskStatus {
            get {
                return ResourceManager.GetString("TaskStatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Task Title.
        /// </summary>
        internal static string TaskTitle {
            get {
                return ResourceManager.GetString("TaskTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Task Type.
        /// </summary>
        internal static string TaskType {
            get {
                return ResourceManager.GetString("TaskType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tax.
        /// </summary>
        internal static string Tax {
            get {
                return ResourceManager.GetString("Tax", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tax.
        /// </summary>
        internal static string TaxName {
            get {
                return ResourceManager.GetString("TaxName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Team.
        /// </summary>
        internal static string Team {
            get {
                return ResourceManager.GetString("Team", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tel.
        /// </summary>
        internal static string Tel {
            get {
                return ResourceManager.GetString("Tel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Telephone.
        /// </summary>
        internal static string Telephone {
            get {
                return ResourceManager.GetString("Telephone", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dialling Code.
        /// </summary>
        internal static string TelephonePrefix {
            get {
                return ResourceManager.GetString("TelephonePrefix", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Terms.
        /// </summary>
        internal static string Terms {
            get {
                return ResourceManager.GetString("Terms", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Terms.
        /// </summary>
        internal static string TermsName {
            get {
                return ResourceManager.GetString("TermsName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Terms Warning.
        /// </summary>
        internal static string Termswarning {
            get {
                return ResourceManager.GetString("Termswarning", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Testing Recommended.
        /// </summary>
        internal static string TestingRecommended {
            get {
                return ResourceManager.GetString("TestingRecommended", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Text.
        /// </summary>
        internal static string Text {
            get {
                return ResourceManager.GetString("Text", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Time.
        /// </summary>
        internal static string Time {
            get {
                return ResourceManager.GetString("Time", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Title.
        /// </summary>
        internal static string Title {
            get {
                return ResourceManager.GetString("Title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to To.
        /// </summary>
        internal static string To {
            get {
                return ResourceManager.GetString("To", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to To Do List.
        /// </summary>
        internal static string ToDoList {
            get {
                return ResourceManager.GetString("ToDoList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to To Do List Type.
        /// </summary>
        internal static string ToDoListType {
            get {
                return ResourceManager.GetString("ToDoListType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Total.
        /// </summary>
        internal static string Total {
            get {
                return ResourceManager.GetString("Total", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Total In Base.
        /// </summary>
        internal static string TotalInBase {
            get {
                return ResourceManager.GetString("TotalInBase", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Total (system calculate) Number of Packs x Size.
        /// </summary>
        internal static string TotalNoPackSize {
            get {
                return ResourceManager.GetString("TotalNoPackSize", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Total Value.
        /// </summary>
        internal static string TotalValue {
            get {
                return ResourceManager.GetString("TotalValue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to TotalValue/IPOBOM.
        /// </summary>
        internal static string TotalValueIPOBOM {
            get {
                return ResourceManager.GetString("TotalValueIPOBOM", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to TQSA.
        /// </summary>
        internal static string TQSA {
            get {
                return ResourceManager.GetString("TQSA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SP Turnover/ Profit YTD.
        /// </summary>
        internal static string Turnover {
            get {
                return ResourceManager.GetString("Turnover", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Type.
        /// </summary>
        internal static string Type {
            get {
                return ResourceManager.GetString("Type", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unit Cost Price.
        /// </summary>
        internal static string UnitCostPrice {
            get {
                return ResourceManager.GetString("UnitCostPrice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unit Price.
        /// </summary>
        internal static string UnitPrice {
            get {
                return ResourceManager.GetString("UnitPrice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unit Sell Price.
        /// </summary>
        internal static string UnitSellPrice {
            get {
                return ResourceManager.GetString("UnitSellPrice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Updated By.
        /// </summary>
        internal static string UpdatedBy {
            get {
                return ResourceManager.GetString("UpdatedBy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Updated Date.
        /// </summary>
        internal static string UpdatedDate {
            get {
                return ResourceManager.GetString("UpdatedDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Uplift Price.
        /// </summary>
        internal static string UpliftPrice {
            get {
                return ResourceManager.GetString("UpliftPrice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Uploaded?.
        /// </summary>
        internal static string Uploaded {
            get {
                return ResourceManager.GetString("Uploaded", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Uploaded By.
        /// </summary>
        internal static string UploadedBy {
            get {
                return ResourceManager.GetString("UploadedBy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to URL.
        /// </summary>
        internal static string URL {
            get {
                return ResourceManager.GetString("URL", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to URN Number.
        /// </summary>
        internal static string URNNumber {
            get {
                return ResourceManager.GetString("URNNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to User.
        /// </summary>
        internal static string User {
            get {
                return ResourceManager.GetString("User", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Value.
        /// </summary>
        internal static string Value {
            get {
                return ResourceManager.GetString("Value", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Virtual Cost Price.
        /// </summary>
        internal static string VirtualCostPrice {
            get {
                return ResourceManager.GetString("VirtualCostPrice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Warehouse.
        /// </summary>
        internal static string Warehouse {
            get {
                return ResourceManager.GetString("Warehouse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PO Line No.
        /// </summary>
        internal static string WHSPOSerialNo {
            get {
                return ResourceManager.GetString("WHSPOSerialNo", resourceCulture);
            }
        }
    }
}
