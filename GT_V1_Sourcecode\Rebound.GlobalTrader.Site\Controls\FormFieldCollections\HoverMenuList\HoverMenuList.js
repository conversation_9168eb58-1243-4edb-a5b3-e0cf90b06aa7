Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.FormFieldCollections");Rebound.GlobalTrader.Site.Controls.FormFieldCollections.HoverMenuList=function(n){Rebound.GlobalTrader.Site.Controls.FormFieldCollections.HoverMenuList.initializeBase(this,[n]);this._aryComponents=[];this._intCheckBoxNo=0;this._intHeadingNo=-1;this._intMyTabHeading=-1;this._str='<div class="sidebarmenu"><ul id="sidebarmenu1">';this._isCall=!1;this._prefix="";this._isULcreated=!1;this._txtReason="";this._txtReasonHidden=""};Rebound.GlobalTrader.Site.Controls.FormFieldCollections.HoverMenuList.prototype={get_pnlSubMenu:function(){return this._pnlSubMenu},set_pnlSubMenu:function(n){this._pnlSubMenu!==n&&(this._pnlSubMenu=n)},get_txtReason:function(){return this._txtReason},set_txtReason:function(n){this._txtReason!==n&&(this._txtReason=n)},get_txtReasonHidden:function(){return this._txtReasonHidden},set_txtReasonHidden:function(n){this._txtReasonHidden!==n&&(this._txtReasonHidden=n)},get_ibtnIcon:function(){return this._ibtnIcon},set_ibtnIcon:function(n){this._ibtnIcon!==n&&(this._ibtnIcon=n)},initialize:function(){this.getCategory();$R_FN.showElement(this._pnlSubMenu,!1);this._ibtnIcon&&($R_IBTN.addClick(this._ibtnIcon,Function.createDelegate(this,this.showMenuList)),this._pnlSubMenu&&$addHandler(this._pnlSubMenu,"click",Function.createDelegate(this,this.hideMenuList)));Rebound.GlobalTrader.Site.Controls.FormFieldCollections.HoverMenuList.callBaseMethod(this,"initialize")},dispose:function(){this.isDisposed||(this.get_element()&&$clearHandlers(this.get_element()),this._tbl=null,this._aryComponents=null,this._intCheckBoxNo=null,this._intHeadingNo=null,this._intMyTabHeading=null,this._txtReason=null,this._txtReasonHidden=null,this._ibtnIcon=null,Rebound.GlobalTrader.Site.Controls.FormFieldCollections.HoverMenuList.callBaseMethod(this,"dispose"))},initmenu:function(){var f,i,t,e,r,o,u,n;for(this._isCall=!0,f=["sidebarmenu1"],i=0;i<f.length;i++){for(t=[],e=document.getElementsByClassName("sidebarmenu"),r=0;r<e.length-1;r++)for(o=e[r].getElementsByTagName("ul")[0].getElementsByTagName("ul"),u=0;u<o.length;u++)t.push(o[u]);for(n=0;n<t.length;n++)t[n].parentNode.getElementsByTagName("a")[0].className+=" subfolderstyle",t[n].parentNode.parentNode.id==f[i]?(t[n].style.left=t[n].parentNode.offsetWidth+"120px",t[n].parentNode.onclick=function(){this.getElementsByTagName("ul")[0].style.display="none"}):t[n].style.left=t[n-1].getElementsByTagName("a")[0].offsetWidth+"px",t[n].parentNode.onmouseover=function(){this.getElementsByTagName("ul")[0].style.display="block"},t[n].parentNode.onmouseout=function(){this.getElementsByTagName("ul")[0].style.display="none"};for(n=t.length-1;n>-1;n--)t[n].style.visibility="visible",t[n].style.display="none"}},getCategory:function(){var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/CRMALines");n.set_DataObject("CRMALines");n.set_DataAction("GetCategory");n.addDataOK(Function.createDelegate(this,this.getCategoryOK));n.addError(Function.createDelegate(this,this.getCategoryError));n.addTimeout(Function.createDelegate(this,this.getCategoryError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getCategoryOK:function(n){var t=n._result;t!=null&&this.addItem(t.EightDCode,0)},addItem:function(n,t){var t,i;if(n!=null)for(t=0;t<n.length;t++)i=!1,row=n[t],t==0||t>0&&n[t].Prefix!=n[t-1].Prefix?(t!=0&&(this._str+="<\/ul><\/li>"),this._str+='<li><a href="javascript:void(0);">'+row.CategoryName+"<\/a><ul>",this._str+=String.format("<li><a href=\"javascript:void(0);\" onclick=\"$find('{0}').setSubCategory('{1}',{2});return false;\">{3}<\/a><\/li>",this._element.id,row.CatSubCode,row.SubCatId,row.SubCategory)):this._str+=String.format("<li><a href=\"javascript:void(0);\" onclick=\"$find('{0}').setSubCategory('{1}',{2});return false;\">{3}<\/a><\/li>",this._element.id,row.CatSubCode,row.SubCatId,row.SubCategory),t==n.length-1&&(this._str+="<\/ul><\/li><\/ul><\/div>",this._pnlSubMenu.innerHTML=this._str,this.initmenu())},getCategoryError:function(n){this.showError(!0,n.get_ErrorMessage())},setSubCategory:function(n,t){this._txtReason.value=n;this._txtReasonHidden.value=t},getSubCategory:function(){return this._txtReasonHidden.value},showMenuList:function(){$R_FN.showElement(this._pnlSubMenu,!0)},hideMenuList:function(){$R_FN.showElement(this._pnlSubMenu,!1)},blankReason:function(){this._txtReason.value="";this._txtReasonHidden.value="";$R_FN.showElement(this._pnlSubMenu,!1)},hidMenuPanel:function(){$R_FN.showElement(this._pnlSubMenu,!1)}};Rebound.GlobalTrader.Site.Controls.FormFieldCollections.HoverMenuList.registerClass("Rebound.GlobalTrader.Site.Controls.FormFieldCollections.HoverMenuList",Rebound.GlobalTrader.Site.Controls.Forms.LabelFormField,Sys.IDisposable);