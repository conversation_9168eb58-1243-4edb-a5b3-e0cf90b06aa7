﻿
GO

/****** Object:  StoredProcedure [dbo].[usp_upsCancelShipment]    Script Date: 12/18/2024 3:15:57 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].[usp_upsCancelShipment]                          
(                         
  @vInvoiceIDs Varchar(Max),        
  @vClientID int ,                          
  @vRetMsg Varchar(100) Out,                          
  @vIsSuccess bit Out                          
)                          
/*                          
CREATED BY : PANKAJ KUMAR                              
CREATED ON : 28-11-2011                              
PURPOSE    : To cancel/void shipment                        
*/                          
AS                                  
BEGIN                                
  BEGIN TRY                          
 Update tbInvoice Set                         
        [AirWayBill] = NULL,                
        [ShippingCost] = ShippingSurchargeValue,                       
        [Boxes] = NULL,                       
        [Weight] = NULL,                       
        [DimensionalWeight] = NULL,                
        [IsUpsInvoiceExported] = 0,                
        [Notes]=CASE WHEN (CHARINDEX('|#',isnull(Notes,''))>0) AND   (CHARINDEX('#|',isnull(Notes,'')) >0)           
                THEN STUFF(isnull(Notes,''), CHARINDEX('|#',isnull(Notes,'')), (CHARINDEX('#|',isnull(Notes,''))-CHARINDEX('|#',isnull(Notes,'')))+2, '')           
                ELSE isnull(Notes,'')  END          
 Where InvoiceNumber In (select String from dbo.[ufn_splitString](@vInvoiceIDs,','))           
  and ClientNo = @vClientID        
      
    -- Start : 27 Jan 2015      
         
   -- Update invoice freight to same as shipping cost when shipping method      
   -- (Match Shipping cost value to freight field) of this invoice is checked       
    DECLARE @MyCursor CURSOR      
    DECLARE @InvoiceID INT    
    SET @MyCursor = CURSOR FAST_FORWARD        
    FOR     
        
    SELECT InvoiceId FROM tbInvoice WHERE  InvoiceNumber In (select String from dbo.[ufn_splitString](@vInvoiceIDs,','))           
    and ClientNo = @vClientID     
        
    OPEN @MyCursor        
   FETCH NEXT FROM @MyCursor        
   INTO @InvoiceID        
    WHILE @@FETCH_STATUS = 0        
      BEGIN     
  DECLARE @IsSameAsShipping BIT      
      
  SELECT  @IsSameAsShipping = ISNULL(IsSameAsShipCost,0) FROM tbShipVia  WHERE ShipViaId =(SELECT ShipViaNo FROM   tbInvoice       
        Where InvoiceId = @InvoiceID)        
                          
  IF @IsSameAsShipping = 1      
  BEGIN      
     UPDATE tbInvoice SET Freight=NULL WHERE InvoiceId = @InvoiceID     
  END      
         
        FETCH NEXT FROM @MyCursor        
        INTO @InvoiceID        
      END        
    CLOSE @MyCursor        
    DEALLOCATE @MyCursor     
       
       
   -- End : 27 Jan 2015      
                         
                         
 IF @@ERROR > 0                  
 BEGIN                          
  Set @vRetMsg = 'Shipment Cancellation Failed!'                        
  Set @vIsSuccess = 0                          
 END                        
 ELSE                    
 BEGIN                          
  Set @vRetMsg = 'Shipment Cancelled Successfully!'                          
  Set @vIsSuccess = 1                          
 END                          
                     
  END TRY                        
  BEGIN CATCH                        
 Set @vRetMsg = ERROR_MESSAGE()                        
 Set @vIsSuccess = 0                        
  END CATCH                        
                                
END

GO


