﻿                  
CREATE OR ALTER PROCEDURE [dbo].[usp_select_Stock]                
--**********************************************************************************************                
-- FOR RP-31 by <PERSON><PERSON><PERSON>av S On 18-10-2022             
-- MARKER   OWNER   Date   Remarks        
--[002]    Ravi   13/09/2033  RP-2340 AS6081        
--**********************************************************************************************                
    @StockId int,          
 @LoginNo int=0,          
 @ClientNo int=0              
AS              
   DECLARE @AllowViewPurchasePrice BIT=0          
   SELECT @AllowViewPurchasePrice= dbo.ufn_GetSecurityPermissions(@LoginNo,@ClientNo,20010035)               
   Declare @CUSTOMERPO  NVARCHAR(60), @CustomerNo int , @CustomerName nvarchar(60)             
     SELECT TOP 1  @CUSTOMERPO=CustomerPO   , @CustomerNo = CompanyNo,   @CustomerName = CompanyName                  
      FROM vwAllocation_Outward WHERE StockNo=@StockId             
               
    SELECT  vw.*                
         , dbo.ufn_get_stock_statusNo(@StockId) AS StatusNo                
          , convert(nvarchar, NULL) AS StockLogDetail --dummy field to make insert work                
          , convert(nvarchar, NULL) AS StockLogChangeNotes --dummy field to make insert work                
          , convert(int, NULL) AS StockLogReasonNo --dummy field to make insert work                
          , convert(bit, NULL) AS UpdateShipments --dummy field to make insert work                
          , d.DivisionName              
          ,(select isNull(SalesOrderno,0) as SalesOrderNo from tbSalesOrderLine  where SalesOrderLineId in (            
select top 1 SalesOrderLineNo  from tbAllocation where StockNo =@StockId )) as SalesOrderNo            
,  @CUSTOMERPO AS CustomerPO            
, @CustomerNo as CustomerNo            
, @CustomerName as CustomerName           
, @AllowViewPurchasePrice AS AllowPurchasePriceView        
, ISNULL(AS6081,0) AS AS6081 --[002]        
, case when vw.clientno = 114 then     
   case when (select count(1) from tbStockTransferOrder where hubstockno = @stockId)>0 then convert(bit,1) else convert(bit,0) end    
  when vw.clientno <>114 then      
   case when (select count(1) from tbStockTransferOrder where clientStockNo = @stockId)>0 then convert(bit,1) else convert(bit,0) end     
 else convert(bit,0) end as HasSTO    
    FROM    dbo.vwStock  vw               
    LEFT JOIN tbDivision d on vw.DivisionNo = d.DivisionId              
    WHERE   StockId = @StockId     