﻿using System;
using System.Collections.Generic;

namespace Rebound.GlobalTrader.DAL
{
    public abstract class StarRatingProvider : DataAccess
    {
        static private StarRatingProvider _instance = null;
        /// <summary>
        /// Returns an instance of the provider type specified in the config file
        /// </summary>       
        static public StarRatingProvider Instance
        {
            get
            {
                if (_instance == null) _instance = (StarRatingProvider)Activator.CreateInstance(Type.GetType(Globals.Settings.StarRating.ProviderType));
                return _instance;
            }
        }
        public StarRatingProvider()
        {
            this.ConnectionString = Globals.Settings.StarRating.ConnectionString;
        }

        #region Method Registrations

        /// <summary>
        /// Insert Star Rating Configuration
        /// Calls [usp_insert_StarRating_Config]
        /// </summary>
        /// <param name="numOfPO"></param>
        /// <param name="createdBy"></param>
        /// <returns></returns>
        public abstract void Insert(System.Int32 numOfPO, System.Int32? createdBy);
        /// <summary>
        /// Call [usp_selectAll_StarRating_Configurations]
        /// </summary>
        /// <returns></returns>
        public abstract List<StarRatingDetails> GetList();
        #endregion
    }
}
