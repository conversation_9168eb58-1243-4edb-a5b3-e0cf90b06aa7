﻿
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

/* 
==========================================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-216601]	    Phuc Hoang			12-Dec-2024		Update			216601: Quote - New status matrix
==========================================================================================================
*/

CREATE OR ALTER PROCEDURE [dbo].[usp_update_AllQuoteLine_Close] (
    @QuoteId int
  , @ReasonNo int
  , @UpdatedBy int
  , @Reasons NVARCHAR(255)=NULL 
  , @RowsAffected int = NULL OUTPUT
)
AS 
    UPDATE  dbo.tbQuoteLine
    SET     ReasonNo = @ReasonNo
          , Closed = 1
          , UpdatedBy = @UpdatedBy
          , NotQuoted = (SELECT NotQuoted FROM tbReason WHERE ReasonId = @ReasonNo)
          , DLUP = CURRENT_TIMESTAMP
		  , Reasons = @Reasons 
    WHERE   QuoteNo = @QuoteId

    SELECT  @RowsAffected = @@rowcount;

	UPDATE  [dbo].[tbQuote]                                              
	SET     QuoteStatus = 3 --Declined
	WHERE   QuoteId = @QuoteId;

    EXEC usp_update_Quote_CheckClosed @QuoteId

	SELECT SourcingResultNo 
	INTO #tbSourcingResultTemp
	FROM dbo.tbQuoteLine WHERE QuoteNo = @QuoteId;

	UPDATE tbSourcingResult set Closed = NULL where SourcingResultId IN (SELECT SourcingResultNo FROM #tbSourcingResultTemp);

	DROP TABLE #tbSourcingResultTemp;
GO


