﻿using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;

namespace Rebound.GlobalTrader.DAL
{

	public class AutoSourcing
	{
		public int? OfferId { get; set; }
		public String VendorName { get; set; }
		public String ClientCurrencyCode { get; set; }
		public String CurrencyCode { get; set; }
		public String SupplierName { get; set; }
		public String ProductName { get; set; }
		public String PackageName { get; set; }
		public String SalesmanName { get; set; }
		public String VendorCategory { get; set; }
		public String VendorType { get; set; }
		public String FullPart { get; set; }
		public String Part { get; set; }
		public int? ProductNo { get; set; }
		public int? PackageNo { get; set; }
		public int? ManufacturerNo { get; set; }
		public String ManufacturerName { get; set; }
		public int? SourceId { get; set; }
		public Double? Cost { get; set; }
		public Double? Resale { get; set; }
		public Double? Profit { get; set; }
		public Double? Margin { get; set; }
		public DateTime? SourceDate { get; set; }
		public DateTime? OriginalEntryDate { get; set; }
		public String MOQ { get; set; }
		public String SPQ { get; set; }
		public String MSL { get; set; }
		public Byte? ROHS { get; set; }
		public int? ADJQty { get; set; }
		public float? Excess { get; set; }
		public String SourceValue { get; set; }
		public int? StockQty { get; set; }
		public String DateCode { get; set; }
		public String LT { get; set; }
		public String Risk { get; set; }
		public String Notes { get; set; }
		public int? UpdatedBy { get; set; }
		public DateTime? DLUP { get; set; }
		public DateTime? DeliveryDate { get; set; }
		public DateTime? OfferDate { get; set; }
		public int? BOMManagerNo { get; set; }
		public int Quantity { get; set; }
		public int? CustomerRequirementId { get; set; }
		public Boolean? Islock { get; set; }
		public int? ClientCompanyNo { get; set; }
		public int? ClientCurrencyNo { get; set; }
		public int? ClientNo { get; set; }
		public int? CurrencyNo { get; set; }
		public int? Salesman { get; set; }
		public int? BOMStatus { get; set; }
		public Double? UnitPrice { get; set; }
		public string BasePrice { get; set; }
		public int? QuoteID { get; set; }
		public int? QuoteNumber { get; set; }
		public Double? UpliftPercentage { get; set; }
		public Double? UpliftPrice { get; set; }
		public string Reason { get; set; }
		public int REQStatus { get; set; }
		public Double BasePriceFigure { get; set; }

		public System.Boolean? IsPrimarySource { get; set; }
		public System.Boolean? IsPrimarySourceActual { get; set; }
		public System.Boolean? ItemReleased { get; set; }
		public int TotalRecords { get; set; }
		public int curpage { get; set; }
		public int SupplierNo { get; set; }
		public string SupplierType { get; set; }
		public int? RegionNo { get; set; }
		public string Region { get; set; }
		public double ActualPrice { get; set; }
		public int ActualCurrencyNo { get; set; }
		public string ActualCurrencyCode { get; set; }
		public double Price { get; set; }
		public System.Boolean? AlternateOfferFlag { get; set; }
		public System.Int32? SupplierWarranty { get; set; }
		public System.Boolean? IsTestingRecommended { get; set; }
		public int? CountryOfOriginNo { get; set; }
		public System.String CountryOfOrigin { get; set; }
		public System.String SupplierTotalQSA { get; set; }
		public System.String SupplierLTB { get; set; }
		public System.Boolean? IsImageAvailable { get; set; }
		public System.String LeadTime { get; set; }
		public System.String ROHSDescription { get; set; }
		public System.String FactorySealed { get; set; }
		public System.Int32? MSLLevelNo { get; set; }
		public System.String MSLLevelText { get; set; }
		public string ROHSStatus { get; set; }
		public int? OfferStatusId { get; set; }
		public int? ShippingCost { get; set; }
	}
}
