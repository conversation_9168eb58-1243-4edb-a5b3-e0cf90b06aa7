///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//Marker     Changed by      Date               Remarks
//[001]      Vinay           12/06/2013         CR:- Client Invoice
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.ClientInvoiceMainInfo_Notify = function(element) { 
	Rebound.GlobalTrader.Site.Controls.Forms.ClientInvoiceMainInfo_Notify.initializeBase(this, [element]);
	this._strClientInvoiceNumber = "";
};

Rebound.GlobalTrader.Site.Controls.Forms.ClientInvoiceMainInfo_Notify.prototype = {

    get_strClientInvoiceNumber: function() { return this._strClientInvoiceNumber; }, set_strClientInvoiceNumber: function(v) { if (this._strClientInvoiceNumber !== v) this._strClientInvoiceNumber = v; },
    get_intCompanyID: function() { return this._intCompanyID; }, set_intCompanyID: function(v) { if (this._intCompanyID !== v) this._intCompanyID = v; },
    get_intClientInvoiceID: function() { return this._intClientInvoiceID; }, set_intClientInvoiceID: function(v) { if (this._intClientInvoiceID !== v) this._intClientInvoiceID = v; },
    get_ibtnSend: function() { return this._ibtnSend; }, set_ibtnSend: function(value) { if (this._ibtnSend !== value) this._ibtnSend = value; },
    get_ibtnSend_Footer: function() { return this._ibtnSend_Footer; }, set_ibtnSend_Footer: function(v) { if (this._ibtnSend_Footer !== v) this._ibtnSend_Footer = v; },

    initialize: function() {
        Rebound.GlobalTrader.Site.Controls.Forms.ClientInvoiceMainInfo_Notify.callBaseMethod(this, "initialize");
        this.addShown(Function.createDelegate(this, this.formShown));
    },

    formShown: function() {
        if (this._blnFirstTimeShown) {
            $R_IBTN.addClick(this._ibtnSend, Function.createDelegate(this, this.sendMail));
            $R_IBTN.addClick(this._ibtnSend_Footer, Function.createDelegate(this, this.sendMail));
            this._ctlMail = $find(this.getField("ctlSendMailMessage").ID);
            this._ctlMail._ctlRelatedForm = this;
            this.getMessageText();
        }
    },

    dispose: function() {
        if (this.isDisposed) return;
        if (this._ibtnSend) $R_IBTN.clearHandlers(this._ibtnSend);
        if (this._ibtnSend_Footer) $R_IBTN.clearHandlers(this._ibtnSend_Footer);
        if (this._ctlMail) this._ctlMail.dispose();
        this._ctlMail = null;
        this._ibtnSend = null;
        this._ibtnSend_Footer = null;
        this._strClientInvoiceNumber = null;
        this._intCompanyID = null;
        this._intClientInvoiceID = null;
        Rebound.GlobalTrader.Site.Controls.Forms.ClientInvoiceMainInfo_Notify.callBaseMethod(this, "dispose");
    },

    getMessageText: function() {
        Rebound.GlobalTrader.Site.WebServices.GetMailMessage_NotifyClientInvoice(this._intClientInvoiceID, Function.createDelegate(this, this.getMessageTextComplete));
    },

    getMessageTextComplete: function(strMsg) {
        this._ctlMail.setValue_Body(strMsg);
        this._ctlMail.setValue_Subject(String.format($R_RES.NotifyClientInvoice, this._strClientInvoiceNumber));
    },

    sendMail: function() {
        if (!this.validateForm()) return;
        Rebound.GlobalTrader.Site.WebServices.NotifyMessage($R_FN.arrayToSingleString(this._ctlMail._aryRecipientLoginIDs), $R_FN.arrayToSingleString(this._ctlMail._aryRecipientGroupIDs), this._ctlMail.getValue_Subject(), this._ctlMail.getValue_Body(), this._intCompanyID, Function.createDelegate(this, this.sendMailComplete));
    },

    validateForm: function() {
        var blnOK = this._ctlMail.validateFields();
        if (!blnOK) this.showError(true);
        return blnOK;
    },

    sendMailComplete: function() {
        this.showSavedOK(true);
        this.onSaveComplete();
    }

};

Rebound.GlobalTrader.Site.Controls.Forms.ClientInvoiceMainInfo_Notify.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.ClientInvoiceMainInfo_Notify", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
