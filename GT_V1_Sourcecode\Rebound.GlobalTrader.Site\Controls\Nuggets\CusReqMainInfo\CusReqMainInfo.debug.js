///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 30.03.2010:
// - ensure line is fully loaded before we allow edits (to stop the wrong data being
//   edited)
//
// RP 06.01.2010:
// - fully dispose everything
//Marker     Changed by      Date         Remarks
//[002]      Vinay          29/04/2015    ESMS Ticket Number. 	228
//[003]      Suhail          12/04/2018    changes MSL text to drop down list
//[004]      Aashu Singh    22/06/2018    make type field mandatory
//[003]      Suhail          12/04/2018   changes MSL text to drop down list
//[004]      Aashu          20/06/2018    [REB-11754]: MSL level
//[005]      Umendra Gupta  28/08/2018    Add All Alternate button for adding all requiremnt.
//[005]      Aashu Singh    04-Sep-2018   Show main cust req salesman on add alternate screen.
//[006]      Aashu Singh    04-Dec-2018   [REB-13584]: Link Requirement to SO Line
//[006]      Umendra Gupta  16-Jan-2019   Add View Tree Button.
//[007]      Anand Gupta  12-Jan-2019   Add IHS.
//[008]      Anand Gupta  28-may-2021    Clone requirement with HUBRFQ.
//[009]      Anand Gupta     17/08/2021   (GTDP-198) Controlling specific product groups for purchasing
//[010]      Anand Gupta     11/01/2022   (GTDP-240)  Add function to add warning message to show on all screens REQ - Quote - SO - PO related to ECCN field
//[011]      Ravi Bhushan    29-08-2023     RP-2227 - AS6081
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Nuggets");

Rebound.GlobalTrader.Site.Controls.Nuggets.CustomerRequirementMainInfo = function (element) {
    Rebound.GlobalTrader.Site.Controls.Nuggets.CustomerRequirementMainInfo.initializeBase(this, [element]);
    this._intCustomerRequirementID = -1;
    this._intOriginalCustomerRequirementID = -1;
    this._strCustomerRequirementNumber = "";
    this._intLineCount = 0;
    this._blnLineLoaded = false;
    this._blnRequirementClosed = false;
    this._intCompanyID = 0;
    this._isPoRequest = false;
    this._blnProdInactive = false;
    //[005] start
    this._salesmanNo = -1;
    //[005] end
    this._altStatus = 0;
    this._changeAltStatus = 0;
    this._aryCheckedLineIDs = [];
    this._AlternateStatus = 0;

    //this._strCompanyName = "";
    //this._strContact = "";
    //this._strddlROHS = -1;
    //this._strddlType = -1;
    //this._strQuantity="";
    //this._strPart = "";

    //[007] code star
    this._ctlCompany = "";
    this._hidCompanyName = "";
    this._hidCompanyID = 0;
    this._ctlContact = -1;//res.ContactNo, res.Contact
    this._hidContactID = -1;
    this._ctlQuantity = 0;
    this._ctlPartNo = "";
    this._ctlAS6081 = false; //[011]
    this._ctlCustomerPart = "";
    this._ctlManufacturer = "";//res.ManufacturerNo, res.Manufacturer
    this._hidManufacturer = "";
    this._hidManufacturerNo = "";
    this._ctlDateCode = "";
    this._ctlProduct = "";
    this._ctlProductDis = false;//(res.Product, res.IsProdHaz)
    this._ctlPrdDutyCodeRate = "";
    this._hidProductID = 0;
    this._ctlPackage = "";
    this._hidPackageID = -1;
    this._ctlTargetPrice = 0;
    this._hidPrice = 0;
    this._ctlCurrency = "";
    this._hidCurrencyID = 0;
    this._ctlDateRequired = "";
    this._ctlUsage = "";
    this._hidUsageID = -1;
    this._ctlNotes = "";
    this._ctlInstructions = "";
    this._ctlROHS = "";
    this._hidROHS = 0;
    this._ctlClosed = "";
    this._ctlClosedReason = "";
    this._hidDisplayStatus = "";
    this._ctlPartWatch = "";
    this._ctlBOM = false;
    this._ctlBOMName = "";
    this._ctlBOMHeader = "";//res.BOMId, $R_FN.setCleanTextValue(res.BOMHeader
    this._hidBOMID = 0;
    this._hidBOMHeaderDisplayStatus = false;
    this._ctlMSL = -1;
    this._ctlFactorySealed = false;
    this._ctlPQA = false;
    this._ctlObsolete = false;
    this._ctlLastTimeBuy = false;
    this._ctlRefirbsAcceptable = false;
    this._ctlTestingRequired = false;
    this._ctlTargetSellPrice = 0;
    this._ctlCompetitorBestoffer = "";
    this._ctlCustomerDecisionDate = "";
    this._ctlRFQClosingDate = "";
    this._ctlQuoteValidityRequiredHid = "";
    this._ctlQuoteValidityRequired = "";
    this._ctlTypeHid = -1;
    this._ctlType = "";
    this._ctlOrderToPlace = false;
    this._ctlRequirementforTraceability = "";
    this._ctlRequirementforTraceabilityHid = "";
    this._ctlTargetSellPriceHidden = 0;
    this._ctlCompetitorBestofferHidden = "";
    this._ctlEAU = "";
    this._hidCustGCNo = null;
    this._ctlAlternativesAccepted = false;
    this._ctlRepeatBusiness = false;
    this._blnProdInactive = false;
    this._strhidMSL = 0;
    this._ctlSalespersion = null;
    this._hidSalesPersion = null;
    this._hidCountryOfOrigin = "";
    this._hidCountryOfOriginNo = 0;
    this._hidLifeCycleStage = "";
    this._hidHTSCode = "";
    this._hidAveragePrice = 0;
    this._hidPackaging = "";
    this._hidPackagingSize = "";
    this._IsPOHub = false;
    this._IHSProductNo = 0;
    this._IHSProduct = "";
    this._IHSHTSCode = "";
    this._IHSDutyCode = "";
    this._ECCNCode = "";
    this._blnIsRestMFR = false;
    this._PartEditStatus = 0;
    this._ibtnReqEccnPrint = null;
    this._ctlAS6081 = "0"; /*[011] 2 = No, 1 = Yes, 0 = --select-- */
    this.handlerUrl = window.location.origin + "" + "/controls/Nuggets/CusReqAdd/CusReqAdd.ashx";

    //[007] code end
    // 1-Alternate,2-Possible Alternate,3-Firm Alternate


};

Rebound.GlobalTrader.Site.Controls.Nuggets.CustomerRequirementMainInfo.prototype = {
    get_intCustomerRequirementID: function () { return this._intCustomerRequirementID; }, set_intCustomerRequirementID: function (v) { if (this._intCustomerRequirementID !== v) this._intCustomerRequirementID = v; },
    get_intOriginalCustomerRequirementID: function () { return this._intOriginalCustomerRequirementID; }, set_intOriginalCustomerRequirementID: function (v) { if (this._intOriginalCustomerRequirementID !== v) this._intOriginalCustomerRequirementID = v; },
    get_tbl: function () { return this._tbl; }, set_tbl: function (v) { if (this._tbl !== v) this._tbl = v; },
    get_strCustomerRequirementNumber: function () { return this._strCustomerRequirementNumber; }, set_strCustomerRequirementNumber: function (v) { if (this._strCustomerRequirementNumber !== v) this._strCustomerRequirementNumber = v; },
    get_ibtnAdd: function () { return this._ibtnAdd; }, set_ibtnAdd: function (v) { if (this._ibtnAdd !== v) this._ibtnAdd = v; },
    get_ibtnAddAll: function () { return this._ibtnAddAll; }, set_ibtnAddAll: function (v) { if (this._ibtnAddAll !== v) this._ibtnAddAll = v; },//[005] 
    get_ibtnEdit: function () { return this._ibtnEdit; }, set_ibtnEdit: function (value) { if (this._ibtnEdit !== value) this._ibtnEdit = value; },
    get_ibtnQuote: function () { return this._ibtnQuote; }, set_ibtnQuote: function (v) { if (this._ibtnQuote !== v) this._ibtnQuote = v; },
    get_ibtnClose: function () { return this._ibtnClose; }, set_ibtnClose: function (v) { if (this._ibtnClose !== v) this._ibtnClose = v; },
    get_hypPrev: function () { return this._hypPrev; }, set_hypPrev: function (v) { if (this._hypPrev !== v) this._hypPrev = v; },
    get_hypNext: function () { return this._hypNext; }, set_hypNext: function (v) { if (this._hypNext !== v) this._hypNext = v; },
    get_lblLineTitle: function () { return this._lblLineTitle; }, set_lblLineTitle: function (v) { if (this._lblLineTitle !== v) this._lblLineTitle = v; },
    get_pnlLineDetail: function () { return this._pnlLineDetail; }, set_pnlLineDetail: function (v) { if (this._pnlLineDetail !== v) this._pnlLineDetail = v; },
    get_pnlLoadingLineDetail: function () { return this._pnlLoadingLineDetail; }, set_pnlLoadingLineDetail: function (v) { if (this._pnlLoadingLineDetail !== v) this._pnlLoadingLineDetail = v; },
    get_pnlLineDetailError: function () { return this._pnlLineDetailError; }, set_pnlLineDetailError: function (v) { if (this._pnlLineDetailError !== v) this._pnlLineDetailError = v; },
    //[002]  code start
    get_ibtnReqPrint: function () { return this._ibtnReqPrint; }, set_ibtnReqPrint: function (v) { if (this._ibtnReqPrint !== v) this._ibtnReqPrint = v; },
    get_ibtnReqEccnPrint: function () { return this._ibtnReqEccnPrint; }, set_ibtnReqEccnPrint: function (v) { if (this._ibtnReqEccnPrint !== v) this._ibtnReqEccnPrint = v; },
    
    //[002]  code end
    //[003] BOM code start
    get_ibtnExportPurchaseHUB: function () { return this._ibtnExportPurchaseHUB; }, set_ibtnExportPurchaseHUB: function (value) { if (this._ibtnExportPurchaseHUB !== value) this._ibtnExportPurchaseHUB = value; },
    //[003] BOM code end
    //[006] code start
    get_ibtnViewTree: function () { return this._ibtnViewTree; }, set_ibtnViewTree: function (v) { if (this._ibtnViewTree !== v) this._ibtnViewTree = v; },
    //[006] code end
    get_intCompanyID: function () { return this._intCompanyID; }, set_intCompanyID: function (value) { if (this._intCompanyID !== value) this._intCompanyID = value; },
    get_ibtnMarkPossible: function () { return this._ibtnMarkPossible; }, set_ibtnMarkPossible: function (value) { if (this._ibtnMarkPossible !== value) this._ibtnMarkPossible = value; },
    get_ibtnDelete: function () { return this._ibtnDelete; }, set_ibtnDelete: function (v) { if (this._ibtnDelete !== v) this._ibtnDelete = v; },
    //get_strCompanyName: function () { return this._strCompanyName; }, _strCompanyName: function (v) { if (this._strCompanyName !== v) this._strCompanyName = v; },
    //get_strContact: function () { return this._strContact; }, _strContact: function (v) { if (this._strContact !== v) this._strContact = v; },

    //[006] Code Clone Requiremnt with HUBRFQ
    //get_ibtnCloneHUBRFQ: function () { return this._ibtnCloneHUBRFQ; }, set_ibtnCloneHUBRFQ: function (value) { if (this._ibtnCloneHUBRFQ !== value) this._ibtnCloneHUBRFQ = value; },
    //get_ibtnCloneHUB: function () { return this._ibtnCloneHUB; }, set_ibtnCloneHUB: function (value) { if (this._ibtnCloneHUB !== value) this._ibtnCloneHUB = value; },


    get_ibtnPrintLabel: function () { return this._ibtnPrintLabel; }, set_ibtnPrintLabel: function (v) { if (this._ibtnPrintLabel !== v) this._ibtnPrintLabel = v; },
    get_pnlLabelTootTip: function () { return this._pnlLabelTootTip; }, set_pnlLabelTootTip: function (v) { if (this._pnlLabelTootTip !== v) this._pnlLabelTootTip = v; },
    get_pnlLabel: function () { return this._pnlLabel; }, set_pnlLabel: function (v) { if (this._pnlLabel !== v) this._pnlLabel = v; },
    get_hypCloneHUBRFQ: function () { return this._hypCloneHUBRFQ; }, set_hypCloneHUBRFQ: function (v) { if (this._hypCloneHUBRFQ !== v) this._hypCloneHUBRFQ = v; },
    get_hypCloneHUB: function () { return this._hypCloneHUB; }, set_hypCloneHUB: function (v) { if (this._hypCloneHUB !== v) this._hypCloneHUB = v; },
    get_IsDiffrentClient: function () { return this._IsDiffrentClient; }, set_IsDiffrentClient: function (value) { if (this._IsDiffrentClient !== value) this._IsDiffrentClient = value; },
    get_IsGSAEditPermission: function () { return this._IsGSAEditPermission; }, set_IsGSAEditPermission: function (value) { if (this._IsGSAEditPermission !== value) this._IsGSAEditPermission = value; },
    get_IsGSA: function () { return this._IsGSA; }, set_IsGSA: function (value) { if (this._IsGSA !== value) this._IsGSA = value; },


    //[006] Code End
    addStartGetData: function (handler) { this.get_events().addHandler("StartGetData", handler); },
    removeStartGetData: function (handler) { this.get_events().removeHandler("StartGetData", handler); },
    onStartGetData: function () {
        var handler = this.get_events().getHandler("StartGetData");
        if (handler) handler(this, Sys.EventArgs.Empty);
    },

    addGotDataOK: function (handler) { this.get_events().addHandler("GotDataOK", handler); },
    removeGotDataOK: function (handler) { this.get_events().removeHandler("GotDataOK", handler); },
    onGotDataOK: function () {
        var handler = this.get_events().getHandler("GotDataOK");
        if (handler) handler(this, Sys.EventArgs.Empty);
    },

    addPartSelected: function (handler) { this.get_events().addHandler("PartSelected", handler); },
    removePartSelected: function (handler) { this.get_events().removeHandler("PartSelected", handler); },
    onPartSelected: function () {
        var handler = this.get_events().getHandler("PartSelected");
        if (handler) handler(this, Sys.EventArgs.Empty);
    },

    initialize: function () {
        Rebound.GlobalTrader.Site.Controls.Nuggets.CustomerRequirementMainInfo.callBaseMethod(this, "initialize");
        this.addRefreshEvent(Function.createDelegate(this, this.getData));
        this._tbl.addSelectedIndexChanged(Function.createDelegate(this, this.selectPart));

        if (this._IsDiffrentClient == true) {
            if (this._IsGSA == true) {
                if (this._IsGSAEditPermission == true) {
                    $("#ctl00_cphMain_ctlSourcingResults_ctlDB_pnlLinks").show();
                    $("#ctl00_cphMain_ctlSourcing_ctlDB_ctl13_ibtnSearch").show();
                    $("#ctl00_cphMain_ctlSourcing_ctlDB_ctl13_ibtnAddStockInfo").show();
                    $("#ctl00_cphMain_ctlSourcing_ctlDB_ctl13_ibtnAddOffer").show();
                    $("#ctl00_cphMain_ctlSourcing_ctlDB_ctl13_ibtnAddTrusted").show();
                    $("#ctl00_cphMain_ctlSourcing_ctlDB_ctl13_hypNewNPR").show();
                }
                else {
                    $("#ctl00_cphMain_ctlSourcingResults_ctlDB_pnlLinks").hide();
                    $("#ctl00_cphMain_ctlSourcing_ctlDB_ctl13_ibtnSearch").hide();
                    $("#ctl00_cphMain_ctlSourcing_ctlDB_ctl13_ibtnAddStockInfo").hide();
                    $("#ctl00_cphMain_ctlSourcing_ctlDB_ctl13_ibtnAddOffer").hide();
                    $("#ctl00_cphMain_ctlSourcing_ctlDB_ctl13_ibtnAddTrusted").hide();
                    $("#ctl00_cphMain_ctlSourcing_ctlDB_ctl13_hypNewNPR").hide();
                }
            }
            else {
                $("#ctl00_cphMain_ctlSourcingResults_ctlDB_pnlLinks").show();
                $("#ctl00_cphMain_ctlSourcing_ctlDB_ctl13_ibtnSearch").show();
                $("#ctl00_cphMain_ctlSourcing_ctlDB_ctl13_ibtnAddStockInfo").show();
                $("#ctl00_cphMain_ctlSourcing_ctlDB_ctl13_ibtnAddOffer").show();
                $("#ctl00_cphMain_ctlSourcing_ctlDB_ctl13_ibtnAddTrusted").show();
                $("#ctl00_cphMain_ctlSourcing_ctlDB_ctl13_hypNewNPR").show();

            }

        }
        else {
            $("#ctl00_cphMain_ctlSourcingResults_ctlDB_pnlLinks").show();
            $("#ctl00_cphMain_ctlSourcing_ctlDB_ctl13_ibtnSearch").show();
            $("#ctl00_cphMain_ctlSourcing_ctlDB_ctl13_ibtnAddStockInfo").show();
            $("#ctl00_cphMain_ctlSourcing_ctlDB_ctl13_ibtnAddOffer").show();
            $("#ctl00_cphMain_ctlSourcing_ctlDB_ctl13_ibtnAddTrusted").show();
            $("#ctl00_cphMain_ctlSourcing_ctlDB_ctl13_hypNewNPR").show();
        }
        $addHandler(this._hypPrev, "click", Function.createDelegate(this, this.prevLine));
        $addHandler(this._hypNext, "click", Function.createDelegate(this, this.nextLine));
        //if (this._ibtnExportPurchaseHUB) $R_IBTN.addClick(this._ibtnExportPurchaseHUB, Function.createDelegate(this, this.saveRequirementData));
        //Edit
        if (this._ibtnEdit) {
            $R_IBTN.addClick(this._ibtnEdit, Function.createDelegate(this, this.showEditForm));
            this._frmEdit = $find(this._aryFormIDs[0]);
            this._frmEdit._intCustomerRequirementID = this._intCustomerRequirementID;
            this._frmEdit.addCancel(Function.createDelegate(this, this.cancelEdit));
            this._frmEdit.addSaveComplete(Function.createDelegate(this, this.saveEditComplete));
            //this._frmEdit._intCompanyID = this._intCompanyID;
        }

        //add alternate
        if (this._ibtnAdd) {
            $R_IBTN.addClick(this._ibtnAdd, Function.createDelegate(this, this.showAddForm));
            this._frmAdd = $find(this._aryFormIDs[1]);
            this._frmAdd._intCustomerRequirementID = this._intCustomerRequirementID;
            this._frmAdd.addCancel(Function.createDelegate(this, this.cancelAdd));
            this._frmAdd.addSaveComplete(Function.createDelegate(this, this.saveAddComplete));
        }
        //add all alternate
        //start [005] 
        if (this._ibtnAddAll) {
            $R_IBTN.addClick(this._ibtnAddAll, Function.createDelegate(this, this.saveAllAlternate));
        }
        //end [005] 
        //quote
        if (this._ibtnQuote) $R_IBTN.addClick(this._ibtnQuote, Function.createDelegate(this, this.doQuote));

        //Close
        if (this._ibtnClose) {
            $R_IBTN.addClick(this._ibtnClose, Function.createDelegate(this, this.showCloseForm));
            this._frmClose = $find(this._aryFormIDs[2]);
            this._frmClose._intCustomerRequirementID = this._intCustomerRequirementID;
            this._frmClose.addCancel(Function.createDelegate(this, this.cancelClose));
            this._frmClose.addNotConfirmed(Function.createDelegate(this, this.cancelClose));
            this._frmClose.addSaveComplete(Function.createDelegate(this, this.saveCloseComplete));
        }


        if (this._ibtnExportPurchaseHUB) {
            $R_IBTN.addClick(this._ibtnExportPurchaseHUB, Function.createDelegate(this, this.showConfirmForm));
            this._frmConfirm = $find(this._aryFormIDs[3]);
            this._frmConfirm._intCustomerRequirementID = this._intCustomerRequirementID;
            this._frmConfirm.addNotConfirmed(Function.createDelegate(this, this.cancelConfirm));
            this._frmConfirm.addSaveComplete(Function.createDelegate(this, this.saveConfirmComplete));
            this._frmConfirm.addCancel(Function.createDelegate(this, this.cancelConfirm));
        }
        //ihs alt delete
        //Close
        if (this._ibtnDelete) {
            $R_IBTN.addClick(this._ibtnDelete, Function.createDelegate(this, this.showAltDeleteForm));
            this._frmDelete = $find(this._aryFormIDs[4]);
            //this._frmDelete._intCustomerRequirementID = this._intCustomerRequirementID;
            this._frmDelete.addCancel(Function.createDelegate(this, this.cancelAltDelete));
            this._frmDelete.addNotConfirmed(Function.createDelegate(this, this.cancelAltDelete));
            this._frmDelete.addSaveComplete(Function.createDelegate(this, this.saveAltDeleteComplete));
        }
        //code end

        if (this._ibtnReqPrint)
            $R_IBTN.addClick(this._ibtnReqPrint, Function.createDelegate(this, this.printCustRequirement));

        if (this._ibtnMarkPossible)
            $R_IBTN.addClick(this._ibtnMarkPossible, Function.createDelegate(this, this.markedFirmAlt));
        //[006] code start
        if (this._ibtnViewTree)
            $R_IBTN.addClick(this._ibtnViewTree, Function.createDelegate(this, this.OpenTree));
        //[006] code end
        if (this._ibtnReqEccnPrint)
            $R_IBTN.addClick(this._ibtnReqEccnPrint, Function.createDelegate(this, this.printCustRequirementEccn));

        if (this._ibtnPrintLabel) {
            if (this._hypCloneHUBRFQ) {
                $addHandler(this._hypCloneHUBRFQ, "click", Function.createDelegate(this, this.showEditCloneHUBRFQForm));
                this._frmEditCloneHUBRFQ = $find(this._aryFormIDs[5]);
                this._frmEditCloneHUBRFQ._intCustomerRequirementID = this._intCustomerRequirementID;
                this._frmEditCloneHUBRFQ.addCancel(Function.createDelegate(this, this.cancelEditCloneHUBRFQ));
                this._frmEditCloneHUBRFQ.addSaveComplete(Function.createDelegate(this, this.saveEditCloneHUBRFQComplete));

            }
        }
        if (this._ibtnPrintLabel) {
            if (this._hypCloneHUB) {
                $addHandler(this._hypCloneHUB, "click", Function.createDelegate(this, this.showEditCloneHUBForm));
                this._frmEditCloneHUB = $find(this._aryFormIDs[6]);
                this._frmEditCloneHUB._intCustomerRequirementID = this._intCustomerRequirementID;
                this._frmEditCloneHUB.addCancel(Function.createDelegate(this, this.cancelEditCloneHUB));
                this._frmEditCloneHUB.addSaveComplete(Function.createDelegate(this, this.saveEditCloneHUBComplete));
            }


        }
        //code ends

        this.getData();
    },


    dispose: function () {
        if (this.isDisposed) return;
        if (this._ibtnEdit) $R_IBTN.clearHandlers(this._ibtnEdit);
        if (this._ibtnQuote) $R_IBTN.clearHandlers(this._ibtnQuote);
        if (this._ibtnClose) $R_IBTN.clearHandlers(this._ibtnClose);
        if (this._ibtnAdd) $R_IBTN.clearHandlers(this._ibtnAdd);
        if (this._ibtnAddAll) $R_IBTN.clearHandlers(this._ibtnAddAll);
        if (this._hypPrev) $clearHandlers(this._hypPrev);
        if (this._hypNext) $clearHandlers(this._hypNext);
        //if (this._ibtnCloneHUBRFQ) $R_IBTN.clearHandlers(this._ibtnCloneHUBRFQ);
        //if (this._ibtnCloneHUB) $R_IBTN.clearHandlers(this._ibtnCloneHUB);
        if (this._frmEdit) this._frmEdit.dispose();
        if (this._frmAdd) this._frmAdd.dispose();
        if (this._frmClose) this._frmClose.dispose();
        if (this._ibtnDelete) $R_IBTN.clearHandlers(this._ibtnDelete);
        if (this._frmDelete) this._frmDelete.dispose();
        if (this._tbl) this._tbl.dispose();
        //if (this._ibtnCloneHUB) this._ibtnCloneHUB.dispose();
        //if (this._ibtnCloneHUBRFQ) this._ibtnCloneHUBRFQ.dispose();
        if (this._ibtnPrintLabel) $R_IBTN.clearHandlers(this._ibtnPrintLabel);
        this._aryCheckedLineIDs = null;
        this._AlternateStatus = null;
        this._frmDelete = null;
        this._ibtnDelete = null;
        this._IsDiffrentClient = null;
        this._IsGSAEditPermission = null;
        this._IsGSA = null;
        this._frmEdit = null;
        this._frmAdd = null;
        this._frmClose = null;
        this._intCustomerRequirementID = null;
        this._strCompanyName = null;
        this._strContact = null;
        this._strddlROHS = null;
        this._strddlType = null;
        this._strQuantity = null;
        this._strPart = null;
        this._intOriginalCustomerRequirementID = null;
        this._tbl = null;
        this._strCustomerRequirementNumber = null;
        //this._intContactID = null;
        //this._intCompanyID = null;
        this._ibtnAdd = null;
        this._ibtnAddAll = null;//[005] 
        this._ibtnEdit = null;
        this._ibtnQuote = null;
        this._ibtnClose = null;
        this._hypPrev = null;
        this._hypNext = null;
        this._lblLineTitle = null;
        this._pnlLineDetail = null;
        this._pnlLoadingLineDetail = null;
        this._pnlLineDetailError = null;
        this._blnRequirementClosed = null;
        this._ibtnReqPrint = null;
        this._blnProdInactive = null;
        //[005] start
        this._salesmanNo = null;
        //[005] end
        this._ibtnMarkPossible = null;
        this._altStatus = null;
        this._changeAltStatus = null;
        //[007] code star
        this._ctlCompany = null
        this._hidCompanyName = null
        this._hidCompanyID = null;
        this._ctlContact = null;//res.ContactNo, res.Contact
        this._hidContactID = null;
        this._ctlQuantity = null;
        this._ctlPartNo = null;
        this._ctlAS6081 = null;
        this._ctlCustomerPart = null;
        this._ctlManufacturer = null; //res.ManufacturerNo, res.Manufacturer
        this._hidManufacturer = null;
        this._hidManufacturerNo = null;
        this._ctlDateCode = null;
        this._ctlProduct = null;
        this._ctlProductDis = null;//(res.Product, res.IsProdHaz)
        this._ctlPrdDutyCodeRate = null;
        this._hidProductID = null;
        this._ctlPackage = null;
        this._hidPackageID = null;
        this._ctlTargetPrice = null;
        this._hidPrice = null;
        this._ctlCurrency = null;
        this._hidCurrencyID = null;
        this._ctlDateRequired = null;
        this._ctlUsage = null;
        this._hidUsageID = null;
        this._ctlNotes = null;
        this._ctlInstructions = null;
        this._ctlROHS = null;
        this._hidROHS = null;
        this._ctlClosed = null;
        this._ctlClosedReason = null;
        this._hidDisplayStatus = null;
        this._ctlPartWatch = null;
        this._ctlBOM = null;
        this._ctlBOMName = null;
        this._ctlBOMHeader = null; //res.BOMId, $R_FN.setCleanTextValue(res.BOMHeader
        this._hidBOMID = null;
        this._hidBOMHeaderDisplayStatus = null;;
        this._ctlMSL = null;
        this._ctlFactorySealed = null;
        this._ctlPQA = null;
        this._ctlObsolete = null;
        this._ctlLastTimeBuy = null;
        this._ctlRefirbsAcceptable = null;
        this._ctlTestingRequired = null;
        this._ctlTargetSellPrice = null;
        this._ctlCompetitorBestoffer = null;
        this._ctlCustomerDecisionDate = null;
        this._ctlRFQClosingDate = null;
        this._ctlQuoteValidityRequiredHid = null;
        this._ctlQuoteValidityRequired = null;
        this._ctlTypeHid = null;
        this._ctlType = null;
        this._ctlOrderToPlace = null;
        this._ctlRequirementforTraceability = null;
        this._ctlRequirementforTraceabilityHid = null;
        this._ctlTargetSellPriceHidden = null;
        this._ctlCompetitorBestofferHidden = null;
        this._ctlEAU = null;
        this._hidCustGCNo = null;
        this._ctlAlternativesAccepted = null;
        this._ctlRepeatBusiness = null;
        this._strhidMSL = null;
        this._ctlSalespersion = null;
        this._hidSalesPersion = null;
        this._hidCountryOfOrigin = null;
        this._hidCountryOfOriginNo = null;
        this._hidLifeCycleStage = null;
        this._hidHTSCode = null;
        this._hidAveragePrice = null;
        this._hidPackaging = null;
        this._hidPackagingSize = null;
        this._IsPOHub = null;
        this._IHSProductNo = null;
        this._IHSProduct = null;
        this._IHSHTSCode = null;
        this._IHSDutyCode = null;
        this._ECCNCode = null;
        //this._ibtnCloneHUBRFQ = null;
        //this._ibtnCloneHUB = null;

        //tool tip links
        this._ibtnPrintLabel = null;
        this._pnlLabelTootTip = null;
        this._pnlLabel = null;
        this._hypCloneHUBRFQ = null;
        this._hypCloneHUB = null;
        //cod ends
        this._blnIsRestMFR = null;
        this._PartEditStatus = null;
        this._ibtnReqEccnPrint = null;


        //[007] code star
        Rebound.GlobalTrader.Site.Controls.Nuggets.CustomerRequirementMainInfo.callBaseMethod(this, "dispose");
    },


    getData: function () {
        this.enableButtons(false);
        this.getData_Start();
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/CusReqMainInfo");
        obj.set_DataObject("CusReqMainInfo");
        obj.set_DataAction("GetData");
        obj.addParameter("id", this._intCustomerRequirementID);
        obj.addParameter("CustomerRequirementNumber", this._strCustomerRequirementNumber);
        obj.addDataOK(Function.createDelegate(this, this.getDataOK));
        obj.addError(Function.createDelegate(this, this.getDataError));
        obj.addTimeout(Function.createDelegate(this, this.getDataError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
        this.onStartGetData();
    },

    getDataOK: function (args) {
        if (this._ibtnDelete) {
            $R_IBTN.enableButton(this._ibtnDelete, false);
        }

        var res = args._result;
        this._tbl.clearTable();
        for (var i = 0, l = res.Items.length; i < l; i++) {
            var row = res.Items[i];
            this._AlternateStatus = row.AlternateStatus;
            var aryData = [
                this.writeCheckbox(row.ID, i, this._tbl),
                $R_FN.writeDoubleCellValue($R_FN.writePartNo(row.Part, row.ROHS), $R_FN.setCleanTextValue(row.CustomerPart))
                , $R_FN.writeDoubleCellValue($RGT_nubButton_Manufacturer(row.MfrNo, row.Mfr, row.MfrAdvisoryNotes), $R_FN.setCleanTextValue(row.DC))
                , $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.Product), $R_FN.setCleanTextValue(row.Package))
                , $R_FN.writeDoubleCellValue(row.Quantity, $R_FN.setCleanTextValue(row.Date))
                , $R_FN.writeDoubleCellValue(row.Price, row.PriceInBase)
            ];
            if (!row.Alt) this._intOriginalCustomerRequirementID = row.ID;
            var objExtraData = {
                Part: $R_FN.setCleanTextValue(row.PartNo)
                , Closed: row.Closed


            };
            this._tbl.addRow(aryData, row.ID, (row.ID == this._intCustomerRequirementID), objExtraData, ((row.Alt) ? "cusReqAlternatePart" : "cusReqMainPart"));
            this.registerCheckBox(row.ID, i, false, true, this._tbl);
            var chk = this.getCheckBox(i, this._tbl);
            chk._element.setAttribute("onClick", String.format("$find(\"{0}\").getCheckedCellValue({1},{2});", this._element.id, i, row.ID));
            $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl13_tbl_chkImg0").removeClass("on");
            $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl13_tbl_chkImg0").removeClass("off");
            $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl13_tbl_chkImg0").hide();
            this.StartRefreshLylicaAPIData(row.Part, row.Mfr, row.MfrNo);
            chk = null;
            row = null;
        }
        this._intLineCount = res.Items.length;
        this._tbl.resizeColumns();
        this.setFieldValue("hidDisplayStatus", $R_FN.setCleanTextValue(res.DisplayStatus));
        //this.setFieldValue("ctlSalespersion", $R_FN.setCleanTextValue(res.SupportTeamMember));
        //this.setFieldValue("hidSalesPersion", res.SupportTeamMemberNo);

        this.enableButtons(true);
        this.getDataOK_End();
        if (this._AlternateStatus != 'undefined' && this._AlternateStatus > 0) {
            // $R_IBTN.enableButton(this._ibtnDelete, true);
        }
        else {
            if (this._ibtnDelete) {
                $R_IBTN.enableButton(this._ibtnDelete, false);
            } 
        }
    },

    getDataError: function (args) {
        this.showError(true, args.get_ErrorMessage());
    },

    //ihs code start
    getCheckedCellValue: function (intIndex, poLinID) {

        var chk = this.getCheckBox(intIndex, this._tbl);
        var IsChecked = chk._blnChecked;
        var tr = this._tbl._tbl.rows[intIndex];
        if (!tr) return;
        if (IsChecked == true) {
            Array.add(this._aryCheckedLineIDs, poLinID);

        }
        else {
            Array.remove(this._aryCheckedLineIDs, poLinID);

        }
        if (this._aryCheckedLineIDs.length > 0) {
            $R_IBTN.enableButton(this._ibtnDelete, true);
        }
        else {
            $R_IBTN.enableButton(this._ibtnDelete, false);
        }


    },


    writeCheckbox: function (varID, intIndex, tbl) {
        var strChkID = this.getControlID("chk", intIndex, tbl);
        var strChkImageID = this.getControlID("chkImg", intIndex, tbl);
        var str = String.format("<div class=\"imageCheckBoxDisabled\"   name=\"select_altranativepart\" id=\"{0}\" ><img id=\"{1}\" class=\"{2}\" src=\"images/x.gif\" style=\"border-width: 0px;cursor: alias;\" /> </div>", strChkID, strChkImageID, "off");


        return str;


    },
    getControlID: function (str, i, tbl) {
        return String.format("{0}_{1}{2}", tbl._element.id, str, i);
    },
    getCheckBox: function (intCheckBox, tbl) {
        return $find(this.getControlID("chk", intCheckBox, tbl));
    },
    registerCheckBox: function (varID, intIndex, blnChecked, blnEnabled, tbl) {

        var strChkID = this.getControlID("chk", intIndex, tbl);

        var strChkImageID = this.getControlID("chkImg", intIndex, tbl);

        var chk = this.getCheckBox(intIndex, tbl);

        if (chk) {
            chk.dispose();
            chk = null;
        }

        eval($R_FN.getCreateStatement("Rebound.GlobalTrader.Site.Controls.ImageCheckBox", [["blnChecked", blnChecked], ["blnEnabled", blnEnabled], ["img", String.format("$get(\"{0}\")", strChkImageID)]], strChkID));

    },
    //ihs code end

    doQuote: function () {
        this.clearMessages();
        if (this.getFieldValue("ctlMSL") === "") {
            this.addMessage("Kindly update MSL", $R_ENUM$MessageTypeList.Error);
            return false;
        }
        else
            location.href = $RGT_gotoURL_QuoteAdd(null, null, this._intCustomerRequirementID, null);

    },
    //tool tip code
    setToolTipLabelLocation: function () {
        if (this._pnlLabelTootTip) {
            if (this._ibtnPrintLabel) this._pnlLabelTootTip.style.top = String.format("{0}px", (Sys.UI.DomElement.getBounds(this._ibtnPrintLabel).y - Sys.UI.DomElement.getBounds(this._pnlLabelTootTip).y) + 15);
            if (this._ibtnPrintLabel) this._pnlLabelTootTip.style.left = String.format("{0}px", (Sys.UI.DomElement.getBounds(this._ibtnPrintLabel).x - Sys.UI.DomElement.getBounds(this._pnlLabelTootTip).x));
        }
    },
    showLabelTopIcons: function () {
        clearTimeout(this._intTimeout);
        $R_FN.showElement(this._pnlLabelTootTip, true);
        this._pnlLabelTootTip.style.top = "0px";
        this._pnlLabelTootTip.style.left = "0px";
        this.setToolTipLabelLocation();
    },
    hideLabelTopIcons: function () {
        clearTimeout(this._intTimeout);
        this._intTimeout = setTimeout(Function.createDelegate(this, this.finishHideLabelTopIcons), 100);
    },

    finishHideLabelTopIcons: function () {
        $R_FN.showElement(this._pnlLabelTootTip, false);
    },

    enableButtons: function (bln) {
        if (bln) {

            if (this._ibtnClose) $R_IBTN.enableButton(this._ibtnClose, !this._blnRequirementClosed && this._blnLineLoaded);
            if (this._ibtnAdd) $R_IBTN.enableButton(this._ibtnAdd, !this._blnRequirementClosed && this._blnLineLoaded);
            if (this._ibtnAddAll) $R_IBTN.enableButton(this._ibtnAddAll, !this._blnRequirementClosed && this._blnLineLoaded);
            if (this._ibtnEdit) $R_IBTN.enableButton(this._ibtnEdit, !this._blnRequirementClosed && this._blnLineLoaded);
            if (this._ibtnQuote) $R_IBTN.enableButton(this._ibtnQuote, !this._blnRequirementClosed && !this._blnIsRestMFR && this._blnLineLoaded);
            if (this._ibtnQuote) $R_IBTN.enableButton(this._ibtnMarkPossible, !this._blnRequirementClosed && this._blnLineLoaded && (this._altStatus == 2 || this._altStatus == 3));
            //  if (this._ibtnCloneHUBRFQ) $R_IBTN.enableButton(this._ibtnCloneHUBRFQ, !this._blnRequirementClosed && this._blnLineLoaded);
            // if (this._ibtnCloneHUB) $R_IBTN.enableButton(this._ibtnCloneHUB, !this._blnRequirementClosed && this._blnLineLoaded);

            if (this._ibtnPrintLabel) {
                $R_IBTN.enableButton(this._ibtnPrintLabel, !this._blnRequirementClosed && this._blnLineLoaded);

                if (!this._blnRequirementClosed && this._blnLineLoaded) $addHandler(this._ibtnPrintLabel, "mouseover", Function.createDelegate(this, this.showLabelTopIcons));
                if (!this._blnRequirementClosed && this._blnLineLoaded) $addHandler(this._pnlLabelTootTip, "mouseover", Function.createDelegate(this, this.showLabelTopIcons));
                if (!this._blnRequirementClosed && this._blnLineLoaded) $addHandler(this._ibtnPrintLabel, "mouseout", Function.createDelegate(this, this.hideLabelTopIcons));
                if (!this._blnRequirementClosed && this._blnLineLoaded) $addHandler(this._pnlLabelTootTip, "mouseout", Function.createDelegate(this, this.hideLabelTopIcons));
            }
        } else {
            if (this._ibtnAdd) $R_IBTN.enableButton(this._ibtnAdd, false);
            if (this._ibtnAddAll) $R_IBTN.enableButton(this._ibtnAddAll, false);
            if (this._ibtnEdit) $R_IBTN.enableButton(this._ibtnEdit, false);
            if (this._ibtnQuote) $R_IBTN.enableButton(this._ibtnQuote, false);
            if (this._ibtnClose) $R_IBTN.enableButton(this._ibtnClose, false);
            if (this._ibtnQuote) $R_IBTN.enableButton(this._ibtnMarkPossible, false);
            // if (this._ibtnCloneHUBRFQ) $R_IBTN.enableButton(this._ibtnCloneHUBRFQ, false);
            // if (this._ibtnCloneHUB) $R_IBTN.enableButton(this._ibtnCloneHUB, false);
            if (this._ibtnPrintLabel) $R_IBTN.enableButton(this._ibtnPrintLabel, false);

            //if (this._ibtnPrintLabel) {
            //$R_IBTN.enableButton(this._ibtnPrintLabel,  this._blnLineLoaded);

            //if (this._blnLineLoaded) $addHandler(this._ibtnPrintLabel, "mouseover", Function.createDelegate(this, this.showLabelTopIcons));
            //if (this._blnLineLoaded) $addHandler(this._pnlLabelTootTip, "mouseover", Function.createDelegate(this, this.showLabelTopIcons));
            //if (this._blnLineLoaded) $addHandler(this._ibtnPrintLabel, "mouseout", Function.createDelegate(this, this.hideLabelTopIcons));
            //if (this._blnLineLoaded) $addHandler(this._pnlLabelTootTip, "mouseout", Function.createDelegate(this, this.hideLabelTopIcons));
            //}
        }
    },

    getBomNo: function () {
        var obj = new Rebound.GlobalTrader.Site.Data();
        this.showLoading(true);
        $R_FN.showElement(this._pnlLineDetail, false);
        $R_FN.showElement(this._pnlLoadingLineDetail, true);
        $R_FN.showElement(this._pnlLineDetailError, false);
        obj.set_PathToData("controls/Nuggets/CusReqMainInfo");
        obj.set_DataObject("CusReqMainInfo");
        obj.set_DataAction("GetBomID");
        obj.addParameter("id", this._intCustomerRequirementID);
        obj.addParameter("CustomerRequirementNumber", this._strCustomerRequirementNumber);
        obj.addDataOK(Function.createDelegate(this, this.getBomDataOK));
        obj.addError(Function.createDelegate(this, this.getLineDataError));
        obj.addTimeout(Function.createDelegate(this, this.getLineDataError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    getFormControlID: function (ParentId, controlID) {
        var str = "";
        str = String.format("{0}_{1}", ParentId, controlID);
        return str;
    },
    getBomDataOK: function (args) {
        var res = args._result;
        this._frmEdit._intBOMID = res.BOMNo;
        this._frmEdit._isPoRequest = this._isPoRequest;
        //[007] code star
        this._frmEdit._ctlCompany = this.getFieldValue("hidCompanyName");//this._ctlCompany;
        this._frmEdit._ctlContact = this.getFieldValue("hidContactName");//this._ctlContact;
        this._frmEdit._salesmanNo = this.getFieldValue("hidSalesman")//this._salesmanNo;
        this._frmEdit._ctlROHS = this.getFieldValue("hidROHS");//this._ctlROHS;
        this._frmEdit._ctlQuantity = this.getFieldValue("ctlQuantity");//this._ctlQuantity;
        this._frmEdit._ctlPartNo = this.getFieldValue("ctlPartNo");//this._ctlPartNo;
        this._frmEdit._ctlAS6081 = (this._ctlAS6081 == "0" ? "2":"1") ;//[011] (it as60 is false i.e. in db value is 0, than assign 2 to _frmEdiit._ctlAS6081 because in ddl, No = 2)
        this._frmEdit._ctlCustomerPart = this.getFieldValue("ctlCustomerPart");//this._ctlCustomerPart;
        this._frmEdit._ctlManufacturer = this._ctlManufacturer;//res.ManufacturerNo, res.Manufacturer
        this._frmEdit._hidManufacturer = this._hidManufacturer;
        this._frmEdit._hidManufacturerNo = this._hidManufacturerNo;
        this._frmEdit._ctlDateCode = this.getFieldValue("ctlDateCode"); //this._ctlDateCode;
        this._frmEdit._ctlProduct = this.getFieldValue("ctlProduct");
        this._frmEdit._hidProductID = this.getFieldValue("hidProductID");//this._hidProductID;
        this._frmEdit._ctlProductDis = this._ctlProductDis;//(res.Product, res.IsProdHaz)
        this._frmEdit._ctlPrdDutyCodeRate = this._ctlPrdDutyCodeRate;
        this._frmEdit._ctlPackage = this._ctlPackage;
        this._frmEdit._hidPackageID = this.getFieldValue("hidPackageID");//this._hidPackageID;
        this._frmEdit._ctlTargetPrice = this._ctlTargetPrice;
        this._frmEdit._hidPrice = this.getFieldValue("hidPrice");//this._hidPrice;
        this._frmEdit._ctlCurrency = this._ctlCurrency;
        this._frmEdit._hidCurrencyID = this.getFieldValue("hidCurrencyID")
        this._frmEdit._ctlDateRequired = this.getFieldValue("ctlDateRequired");//this._ctlDateRequired;
        this._frmEdit._ctlUsage = this._ctlUsage;
        this._frmEdit._hidUsageID = this.getFieldValue("hidUsageID");//this._hidUsageID;
        this._frmEdit._ctlNotes = this.getFieldValue("ctlNotes");//this._ctlNotes;

        this._frmEdit._ctlInstructions = this.getFieldValue("ctlInstructions");//this._ctlInstructions;
        //this._frmEdit._ctlROHS = this._ctlROHS;
        this._frmEdit._hidROHS = this._hidROHS;
        this._frmEdit._ctlClosed = this._ctlClosed;
        this._frmEdit._ctlClosedReason = this._ctlClosedReason;
        this._frmEdit._hidDisplayStatus = this._hidDisplayStatus;
        this._frmEdit._ctlPartWatch = this.getFieldValue("ctlPartWatch");//this._ctlPartWatch;
        this._frmEdit._ctlBOM = this.getFieldValue("ctlBOM");//this._ctlBOM;
        this._frmEdit._ctlBOMName = this.getFieldValue("ctlBOMName");//this._ctlBOMName;
        this._frmEdit._ctlBOMHeader = this._ctlBOMHeader;//res.BOMId, $R_FN.setCleanTextValue(res.BOMHeader
        this._frmEdit._hidBOMID = this._hidBOMID;
        // this._frmEdit._hidBOMHeaderDisplayStatus = this._hidBOMHeaderDisplayStatus;
        this._frmEdit._ctlMSL = this._ctlMSL;
        this._frmEdit._ctlFactorySealed = this.getFieldValue("ctlFactorySealed");//this._ctlFactorySealed;
        this._frmEdit._ctlPQA = this.getFieldValue("ctlPQA");//this._ctlPQA;
        this._frmEdit._ctlTargetSellPrice = this._ctlTargetSellPrice;
        this._frmEdit._ctlCompetitorBestoffer = this._ctlCompetitorBestoffer;
        this._frmEdit._ctlCustomerDecisionDate = this._ctlCustomerDecisionDate;
        this._frmEdit._ctlRFQClosingDate = this._ctlRFQClosingDate;
        this._frmEdit._ctlQuoteValidityRequiredHid = this._ctlQuoteValidityRequiredHid;
        this._frmEdit._ctlQuoteValidityRequired = this._ctlQuoteValidityRequired;
        this._frmEdit._ctlTypeHid = this.getFieldValue("ctlTypeHid");//this._ctlTypeHid;
        //this._frmEdit._ctlType = this._ctlType;
        this._frmEdit._ctlOrderToPlace = this._ctlOrderToPlace;
        // this._frmEdit._ctlRequirementforTraceability = this._ctlRequirementforTraceability;
        this._frmEdit._ctlRequirementforTraceabilityHid = this.getFieldValue("ctlRequirementforTraceabilityHid");//this._ctlRequirementforTraceabilityHid;
        this._frmEdit._ctlTargetSellPriceHidden = this.getFieldValue("ctlTargetSellPriceHidden");//this._ctlTargetSellPriceHidden;
        this._frmEdit._ctlCompetitorBestofferHidden = this.getFieldValue("ctlCompetitorBestofferHidden");//this._ctlCompetitorBestofferHidden;
        this._frmEdit._ctlEAU = this.getFieldValue("ctlEAU");//this._ctlEAU;
        this._frmEdit._hidCustGCNo = this.getFieldValue("hidCustGCNo");
        //this._frmEdit._ctlAlternativesAccepted = this._ctlAlternativesAccepted;
        //this._frmEdit._ctlRepeatBusiness = this._ctlRepeatBusiness;
        this._frmEdit._blnProdInactive = this._blnProdInactive;
        //this._frmEdit._strhidMSL = this._strhidMSL;
        this._frmEdit._ctlSalespersion = this.getFieldValue("ctlSalespersion");
        this._frmEdit._hidSalesPersion = this.getFieldValue("hidSalesPersion")
        //this._frmEdit._strhidMSL = this._strhidMS;
        this._frmEdit._hidCountryOfOrigin = this._hidCountryOfOrigin;
        this._frmEdit._hidCountryOfOriginNo = this._hidCountryOfOriginNo;
        this._frmEdit._hidLifeCycleStage = this._hidLifeCycleStage;
        this._frmEdit._hidHTSCode = this._hidHTSCode;
        this._frmEdit._hidAveragePrice = this._hidAveragePrice;
        this._frmEdit._hidPackaging = this._hidPackaging;
        this._frmEdit._hidPackagingSize = this._hidPackagingSize;
        this._frmEdit._intCompanyID = this.getFieldValue("hidCompanyID");
        this._frmEdit._radioCheck = this.getFieldValue("ctlFactorySealed");
        this._frmEdit._radObsoleteChk = this.getFieldValue("ctlObsolete");
        this._frmEdit._radLastTimeBuyChk = this.getFieldValue("ctlLastTimeBuy");
        this._frmEdit._radRefirbsAcceptableChk = this.getFieldValue("ctlRefirbsAcceptable");
        this._frmEdit._radTestingRequiredChk = this.getFieldValue("ctlTestingRequired");
        this._frmEdit._radAlternativesAcceptedChK = this.getFieldValue("ctlAlternativesAccepted");
        this._frmEdit._radRepeatBusinessChk = this.getFieldValue("ctlRepeatBusiness");
        //[007] code star
        this._frmEdit._intCustomerRequirementID = this._intCustomerRequirementID;
        this._intBOMID = res.BOMNo;
        this._frmEdit._BOMHeaderDisplayStatus = Boolean.parse(this.getFieldValue("hidBOMHeaderDisplayStatus"));
        this._frmEdit._blnCurInSameFaimly = res.IsSameCurFam;
        this._frmEdit._intCurrencyNo = this.getFieldValue("hidCurrencyID")
        this._frmEdit._IHSProductNo = this._IHSProductNo;
        this._frmEdit._IHSProduct = this._IHSProduct;
        this._frmEdit._IHSHTSCode = this._IHSHTSCode;
        this._frmEdit._IHSDutyCode = this._IHSDutyCode;
        this._frmEdit._AlternateStatus = this._AlternateStatus;
        this._frmEdit._ECCNCode = this._ECCNCode;
        this._frmEdit._PartEditStatus = this._PartEditStatus




        this.showLoading(false);
        this.showForm(this._frmEdit, true);
        this.onGotDataOK();

        //this._frmEdit.getFieldControl("ctlCurrency")._intGlobalCurrencyNo = this.getFieldValue("hidCustGCNo");
        //this._frmEdit.getFieldControl("ctlCurrency")._blnIsBuy = false;
        // this._frmEdit.showField("ctlTargetSellPrice", false);
        // this._frmEdit.setFieldValue("ctlCustomer", this.getFieldValue("hidCompanyName"));
        //this._frmEdit.setFieldValue("ctlContact", this.getFieldValue("hidContactName"));
        //this._frmEdit.setFieldValue("ctlQuantity", this.getFieldValue("ctlQuantity"));
        //this._frmEdit.setFieldValue("ctlPartNo", this.getFieldValue("ctlPartNo"));
        //this._frmEdit.setFieldValue("ctlROHS", this.getFieldValue("hidROHS"));
        //this._frmEdit.setFieldValue("ctlCustomerPartNo", this.getFieldValue("ctlCustomerPart"));
        // this._frmEdit.setFieldValue("ctlManufacturer", this.getFieldValue("hidManufacturerNo"), null, this.getFieldValue("hidManufacturer"));
        //this._frmEdit.setFieldValue("ctlDateCode", this.getFieldValue("ctlDateCode"));
        //this._frmEdit.setFieldValue("ctlProduct", this.getFieldValue("hidProductID"), null, this.getFieldValue("ctlProduct"));
        //if (this._blnProdInactive)
        //    this._frmEdit.setFieldValue("ctlProduct", 0, null, "");
        //else
        //    this._frmEdit.setFieldValue("ctlProduct", this.getFieldValue("hidProductID"), null, this.getFieldValue("ctlProduct"));
        // this._frmEdit.setFieldValue("ctlPackage", this.getFieldValue("hidPackageID"));
        // this._frmEdit.setFieldValue("ctlTargetPrice", this.getFieldValue("hidPrice"));
        //this._frmEdit.setFieldValue("ctlCurrency", this.getFieldValue("hidCurrencyID"));
        //this._frmEdit.setFieldValue("ctlDateRequired", this.getFieldValue("ctlDateRequired"));
        //this._frmEdit.setFieldValue("ctlUsage", this.getFieldValue("hidUsageID"));
        //this._frmEdit.setFieldValue("ctlPartWatch", this.getFieldValue("ctlPartWatch"));
        //this._frmEdit.setFieldValue("ctlBOM", this.getFieldValue("ctlBOM"));
        //this._frmEdit.setFieldValue("ctlBOMName", this.getFieldValue("ctlBOMName"));
        //this._frmEdit.setFieldValue("ctlNotes", this.getFieldValue("ctlNotes"));
        //this._frmEdit.setFieldValue("ctlInstructions", this.getFieldValue("ctlInstructions"));
        // this._frmEdit.showField("ctlBOMHeader", Boolean.parse(this.getFieldValue("hidBOMHeaderDisplayStatus")));
        // this._frmEdit.setFieldValue("ctlBOMHeader", this.getFieldValue("hidBOMID"));
        // this._intBOMID = this.getFieldValue("hidBOMID");
        // alert(res.BOMNo);
        // this._frmEdit.setFieldValue("ctlBOMHeader", res.BOMNo);
        //this._frmEdit.setFieldValue("ctlPQA", this.getFieldValue("ctlPQA"));
        //        this._frmEdit.setFieldValue("ctlObsolete", this.getFieldValue("ctlObsolete"));
        //        this._frmEdit.setFieldValue("ctlLastTimeBuy", this.getFieldValue("ctlLastTimeBuy"));
        //        this._frmEdit.setFieldValue("ctlRefirbsAcceptable", this.getFieldValue("ctlRefirbsAcceptable"));
        //        this._frmEdit.setFieldValue("ctlTestingRequired", this.getFieldValue("ctlTestingRequired"));

        //this._frmEdit.setFieldValue("ctlTargetSellPrice", this.getFieldValue("ctlTargetSellPriceHidden"));
        //this._frmEdit.setFieldValue("ctlCompetitorBestoffer", this.getFieldValue("ctlCompetitorBestofferHidden"));
        //this._frmEdit.setFieldValue("ctlCustomerDecisionDate", this.getFieldValue("ctlCustomerDecisionDate"));
        //this._frmEdit.setFieldValue("ctlRFQClosingDate", this.getFieldValue("ctlRFQClosingDate"));
        //this._frmEdit.setFieldValue("ctlQuoteValidityRequired", this.getFieldValue("ctlQuoteValidityRequiredHid"));
        //this._frmEdit.setFieldValue("ctlType", this.getFieldValue("ctlTypeHid"));
        //this._frmEdit.setFieldValue("ctlOrderToPlace", this.getFieldValue("ctlOrderToPlace"));
        //this._frmEdit.setFieldValue("ctlRequirementforTraceability", this.getFieldValue("ctlRequirementforTraceabilityHid"));
        //  this._frmEdit.setFieldValue("ctlFactorySealed", this.getFieldValue("ctlFactorySealed")); 
        //  [003] code start
        //this._frmEdit.setFieldValue("ctlMsl", this.getFieldValue("ctlMSL"));
        //  [003] code end
        //this._frmEdit.setFieldValue("ctlEau", this.getFieldValue("ctlEAU"));
        //this._frmEdit.setFieldValue("ctlSalesman", this.getFieldValue("hidSalesman"));
        //this._frmEdit.setFieldValue("ctlSalespersion", this.getFieldValue("hidSalesPersion"), null, this.getFieldValue("ctlSalespersion"));

        //    this._frmEdit.setFieldValue("ctlAlternativesAccepted", this.getFieldValue("ctlAlternativesAccepted"));
        // this._frmEdit.setFieldValue("ctlRepeatBusiness", this.getFieldValue("ctlRepeatBusiness"));




    },
    showEditForm: function () {
        this.getBomNo();
    },




    hideEditForm: function () {
        this.showForm(this._frmEdit, false);
    },

    cancelEdit: function () {
        this.hideEditForm();
        this.getData();
    },

    saveEditComplete: function () {
        this.hideEditForm();
        this.showSavedOK(true, $R_RES.ChangesSavedSuccessfully);
        this.onSaveEditComplete();
        this.getData();
    },

    selectPart: function () {
        this._intCustomerRequirementID = this._tbl._varSelectedValue;
        this._blnRequirementClosed = this._tbl.getSelectedExtraData().Closed;

        this.enableButtons(true);
        this.getLineData();
        this.onPartSelected();
    },
    printCustRequirementEccn: function () {
        $R_FN.openPrintLogWindow($R_ENUM$PrintObject.PrintLog, this._intCustomerRequirementID, false, "CustomerRequirementECCN");
    },
    getSelectedPartNo: function () {
        return this._tbl.getSelectedExtraData().Part;
    },

    prevLine: function () {
        var intNewIndex = this._tbl._intSelectedIndex - 1;
        if (intNewIndex < 0) return;
        this._tbl.selectRow(intNewIndex, true);
    },

    nextLine: function () {
        var intNewIndex = this._tbl._intSelectedIndex + 1;
        if (intNewIndex >= this._intLineCount) return;
        this._tbl.selectRow(intNewIndex, true);
    },

    getLineData: function () {
        this._blnLineLoaded = false;
        this._altStatus = 0;
        this.enableButtons(false);
        var obj = new Rebound.GlobalTrader.Site.Data();
        this.showLoading(true);
        $R_FN.showElement(this._pnlLineDetail, false);
        $R_FN.showElement(this._pnlLoadingLineDetail, true);
        $R_FN.showElement(this._pnlLineDetailError, false);
        obj.set_PathToData("controls/Nuggets/CusReqMainInfo");
        obj.set_DataObject("CusReqMainInfo");
        obj.set_DataAction("GetItem");
        obj.addParameter("id", this._intCustomerRequirementID);
        obj.addDataOK(Function.createDelegate(this, this.getLineDataOK));
        obj.addError(Function.createDelegate(this, this.getLineDataError));
        obj.addTimeout(Function.createDelegate(this, this.getLineDataError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    getLineDataOK: function (args) {
        var res = args._result;
        //ihs changes start
        this._ctlCompany = res.CustomerName;//res.CustomerNo, res.CustomerName, null, null, res.CustomerOnStop
        this._hidCompanyName = res.CustomerName;
        this._hidCompanyID = res.CustomerNo;
        this._ctlContact = res.Contact;//res.ContactNo, res.Contact
        this._hidContactID = res.ContactNo;
        this._ctlQuantity = res.Quantity;
        this._ctlPartNo = res.Part;
        this._ctlAS6081 = res.AS6081; //[011]
        this._ctlCustomerPart = res.CustomerPart;
        this._ctlManufacturer = res.ManufacturerNo;//res.ManufacturerNo, res.Manufacturer
        this._hidManufacturer = res.ManufacturerNo;
        this._hidManufacturerNo = res.Manufacturer;
        this._ctlDateCode = res.DateCode;
        this._ctlProduct = res.Product;
        this._ctlProductDis = res.IsProdHaz;//(res.Product, res.IsProdHaz)
        this._ctlPrdDutyCodeRate = res.DutyCodeAndRate;
        this._hidProductID = res.ProductNo;
        this._ctlPackage = res.Package;
        this._hidPackageID = res.PackageNo;
        this._ctlTargetPrice = res.Price;
        this._hidPrice = res.PriceRaw;
        this._ctlCurrency = res.Currency;
        this._hidCurrencyID = res.CurrencyNo;
        this._ctlDateRequired = res.DatePromised;
        this._ctlUsage = res.Usage;
        this._hidUsageID = res.UsageNo;
        this._ctlNotes = res.Notes;
        this._ctlInstructions = res.Instructions;
        this._ctlROHS = res.ROHS;
        this._hidROHS = res.ROHS;
        this._ctlClosed = res.Closed;
        this._ctlClosedReason = res.ClosedReason;
        this._hidDisplayStatus = res.DisplayStatus;
        this._ctlPartWatch = res.PartWatch;
        this._ctlBOM = res.BOM;
        this._ctlBOMName = res.BOMName;
        this._ctlBOMHeader = res.BOMHeader;//res.BOMId, $R_FN.setCleanTextValue(res.BOMHeader
        this._hidBOMID = res.BOMId;
        this._hidBOMHeaderDisplayStatus = res.RequestToPOHubBy == null ? true : false;
        this._ctlMSL = res.MSL;
        this._ctlFactorySealed = res.FactorySealed;
        this._ctlPQA = res.PQA;
        this._ctlObsolete = res.Obsolete;
        this._ctlLastTimeBuy = res.LastTimeBuy;
        this._ctlRefirbsAcceptable = res.RefirbsAcceptable;
        this._ctlTestingRequired = res.TestingRequired;
        this._ctlTargetSellPrice = res.TargetSellPrice;
        this._ctlCompetitorBestoffer = res.CompetitorBestOffer;
        this._ctlCustomerDecisionDate = res.CustomerDecisionDate;
        this._ctlRFQClosingDate = res.RFQClosingDate;
        this._ctlQuoteValidityRequiredHid = res.QuoteValidityRequired;
        this._ctlQuoteValidityRequired = res.QuoteValidityText;
        this._ctlTypeHid = res.Type;
        this._ctlType = res.ReqTypeText;
        this._ctlOrderToPlace = res.OrderToPlace;
        this._ctlRequirementforTraceability = res.ReqForTraceabilityText;
        this._ctlRequirementforTraceabilityHid = res.RequirementforTraceability;
        this._ctlTargetSellPriceHidden = res.hidTargetSellPrice;
        this._ctlCompetitorBestofferHidden = res.hidCompetitorBestOffer;
        this._ctlEAU = res.EAU;
        this._hidCustGCNo = res.CustGCNo;
        this._ctlAlternativesAccepted = res.AlternativesAccepted;
        this._ctlRepeatBusiness = res.RepeatBusiness;
        // this._blnProdInactive = res.ProdInactive;
        this._strhidMSL = res.MSLLevelNo;
        this._hidCountryOfOrigin = res.CountryOfOrigin;
        this._hidCountryOfOriginNo = res.CountryOfOriginNo;
        this._hidLifeCycleStage = res.LifeCycleStage;
        this._hidHTSCode = res.HTSCode;
        this._hidAveragePrice = res.AveragePrice;
        this._hidPackaging = res.Packaging;
        this._hidPackagingSize = res.PackagingSize;
        
        //alert(res.IsProdHaz);
        //ihs changes end
        this.setFieldValue("ctlCompany", $RGT_nubButton_Company(res.CustomerNo, res.CustomerName, null, null, res.CustomerOnStop, res.CompanyAdvisoryNotes));
        this.setFieldValue("ctlContact", $RGT_nubButton_Contact(res.ContactNo, res.Contact));
        this.setFieldValue("hidCompanyID", res.CustomerNo);
        this.setFieldValue("hidCompanyName", $R_FN.setCleanTextValue(res.CustomerName));
        this.setFieldValue("hidContactID", res.ContactNo);
        this.setFieldValue("hidContactName", $R_FN.setCleanTextValue(res.Contact));
        this.setFieldValue("ctlQuantity", res.Quantity);
        this.setFieldValue("ctlPartNo", res.Part);
        this.setFieldValue("ctlAS6081", (res.AS6081 == false ? 'No':'Yes')); //[011]
        
        if (res.StockAvailableDetail != null && res.StockAvailableDetail != '') {
            var stkDetailArr = res.StockAvailableDetail.split('-');
            var QuantityInStock = stkDetailArr[0];
            var QuantityOnOrder = stkDetailArr[1];
            var QuantityAllocated = stkDetailArr[2];
            var QuantityAvailable = stkDetailArr[3];
            var StockNo = stkDetailArr[4];
            var strStockAvailableDetail = "";//"Stock Available Detail";//res.StockAvailableDetail;
            var stockURL = "Whs_StockDetail.aspx?stk=" + StockNo+""
            this.setFieldValue("ctlPartNoDis", $R_FN.showStockAvailableNew(res.Part, QuantityInStock, QuantityOnOrder, QuantityAllocated, QuantityAvailable, stockURL, strStockAvailableDetail));
        }
        else {
            this.setFieldValue("ctlPartNoDis", res.Part);
            //this.setFieldValue("ctlPartNoDis", $R_FN.showStockAvailableNA(res.Part,"N/A"));
            
        }
        this.setFieldValue("ctlCustomerPart", $R_FN.setCleanTextValue(res.CustomerPart));
        this.setFieldValue("ctlManufacturer", $RGT_nubButton_Manufacturer(res.ManufacturerNo, res.Manufacturer, res.MfrAdvisoryNotes));
        this.setFieldValue("hidManufacturer", $R_FN.setCleanTextValue(res.Manufacturer));
        this.setFieldValue("hidManufacturerNo", res.ManufacturerNo);
        this.setFieldValue("hidMfrNotes", res.MfrAdvisoryNotes);
        this.setFieldValue("ctlDateCode", res.DateCode);
        //this.setFieldValue("ctlProduct", $R_FN.setCleanTextValue(res.Product));
        this.setFieldValue("ctlProduct", $R_FN.setCleanTextValue(res.Product));
        //this.setFieldValue("ctlProductDis", $R_FN.showHazardous(res.Product, res.IsProdHaz));
        //[009] code start
        //this.setFieldValue("ctlProductDis", $R_FN.showHazardousandOrdViaIPONew(res.Product, res.IsProdHaz, res.IsOrderViaIPOonly, $R_FN.setCleanTextValue(res.ProductMessage), res.IsRestrictedProduct));
        //this.setFieldValue("ctlProductDis", $R_FN.showProductWarning(res.Product, res.IsProdHaz, res.IsOrderViaIPOonly, $R_FN.setCleanTextValue(res.ProductMessage), res.IsRestrictedProduct));
        this.setFieldValue("ctlProductDis", $R_FN.showProductWarningIndividual(res.Product, res.IsProdHaz, res.IsOrderViaIPOonly, res.IsRestrictedProduct, $R_FN.setCleanTextValue(res.MsgHazardous), $R_FN.setCleanTextValue(res.MsgIPO), $R_FN.setCleanTextValue(res.MsgRestricted)));
        //[009] code end


        this.setFieldValue("ctlPrdDutyCodeRate", res.DutyCodeAndRate);
        this.setFieldValue("hidProductID", res.ProductNo);
        this.setFieldValue("ctlPackage", $R_FN.setCleanTextValue(res.Package));
        this.setFieldValue("hidPackageID", res.PackageNo);
        this.setFieldValue("ctlTargetPrice", res.Price);
        this.setFieldValue("hidPrice", res.PriceRaw);
        this.setFieldValue("ctlCurrency", $R_FN.setCleanTextValue(res.Currency));
        this.setFieldValue("hidCurrencyID", res.CurrencyNo);
        this.setFieldValue("ctlDateRequired", res.DatePromised);
        this.setFieldValue("ctlUsage", $R_FN.setCleanTextValue(res.Usage));
        this.setFieldValue("hidUsageID", $R_FN.setCleanTextValue(res.UsageNo));
        this.setFieldValue("ctlNotes", $R_FN.setCleanTextValue(res.Notes));
        this.setFieldValue("ctlInstructions", $R_FN.setCleanTextValue(res.Instructions));
        this.setFieldValue("ctlROHS", $R_FN.writeROHS(res.ROHS));
        this.setFieldValue("hidROHS", res.ROHS);
        this.setFieldValue("ctlClosed", res.Closed);
        this.setFieldValue("ctlClosedReason", res.ClosedReason);
        this.setFieldValue("hidDisplayStatus", $R_FN.setCleanTextValue(res.DisplayStatus));
        this.setFieldValue("ctlPartWatch", res.PartWatch);
        this.setFieldValue("ctlBOM", res.BOM);
        this.setFieldValue("ctlBOMName", res.BOMName);
        //this.setFieldValue("ctlBOMHeader", res.BOMHeader);
        this.setFieldValue("ctlBOMHeader", $RGT_nubButton_BOM(res.BOMId, $R_FN.setCleanTextValue(res.BOMHeader)));
        this.setFieldValue("hidBOMID", res.BOMId);
        this.setFieldValue("hidBOMHeaderDisplayStatus", res.RequestToPOHubBy == null ? true : false);
        this.setFieldValue("ctlMSL", res.MSL);

        this.setFieldValue("ctlFactorySealed", res.FactorySealed);

        this.setFieldValue("ctlPQA", res.PQA);

        this.setFieldValue("ctlObsolete", res.Obsolete);
        this.setFieldValue("ctlLastTimeBuy", res.LastTimeBuy);
        this.setFieldValue("ctlRefirbsAcceptable", res.RefirbsAcceptable);
        this.setFieldValue("ctlTestingRequired", res.TestingRequired);

        this.setFieldValue("ctlTargetSellPrice", res.TargetSellPrice);
        this.setFieldValue("ctlCompetitorBestoffer", res.CompetitorBestOffer);
        this.setFieldValue("ctlCustomerDecisionDate", res.CustomerDecisionDate);
        this.setFieldValue("ctlRFQClosingDate", res.RFQClosingDate);
        this.setFieldValue("ctlQuoteValidityRequiredHid", res.QuoteValidityRequired);
        this.setFieldValue("ctlQuoteValidityRequired", res.QuoteValidityText);
        this.setFieldValue("ctlTypeHid", res.Type);
        this.setFieldValue("ctlType", res.ReqTypeText);
        this.setFieldValue("ctlOrderToPlace", res.OrderToPlace);
        this.setFieldValue("ctlRequirementforTraceability", res.ReqForTraceabilityText);
        this.setFieldValue("ctlRequirementforTraceabilityHid", res.RequirementforTraceability);

        this.setFieldValue("ctlTargetSellPriceHidden", res.hidTargetSellPrice);
        this.setFieldValue("ctlCompetitorBestofferHidden", res.hidCompetitorBestOffer);
        this.setFieldValue("ctlEAU", res.EAU);
        this.setFieldValue("hidCustGCNo", res.CustGCNo);

        this.setFieldValue("ctlAlternativesAccepted", res.AlternativesAccepted);
        this.setFieldValue("ctlRepeatBusiness", res.RepeatBusiness);
        this._blnProdInactive = res.ProdInactive;
        this.setFieldValue("hidSalesman", res.SalesmanNo);
        this.setFieldValue("hidMSL", res.MSLLevelNo);

        this.setFieldValue("ctlCountryOfOrigin", res.CountryOfOrigin);
        this.setFieldValue("ctlLifeCycleStage", $R_FN.showIHSstatusDefi(res.LifeCycleStage, res.IHSStatusDefination));
        //this.setFieldValue("ctlIHSPartstatusdesc", $R_FN.showHazardous(res.IHSStatusDefination, true));
       
        //this.setFieldValue("ctlPacking", res.Packaging);
        this.setFieldValue("ctlPackagingSize", res.PackagingSize);
        this.setFieldValue("ctlDescriptions", res.Descriptions);

        // if (res.IHSProductNo != 0 && res.ProductNo != 0) {
        this.setFieldValue("ctlIHSProduct", res.IHSProduct);
        //  $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl13_ctlIHSProduct_lbl").css("color", "");
        // }
        // else {
        //this.setFieldValue("ctlIHSProduct", res.IHSProduct);


        // $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl13_ctlIHSProduct_lbl").css("color", "red");
        //}
        if (res.IHSHTSCode != "") {
            this.setFieldValue("ctlHTSCode", res.HTSCode);

            $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl13_ctlHTSCode_lbl").css("color", "");
            //
        }
        else {
            this.setFieldValue("ctlHTSCode", res.HTSCode);

            // $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl13_ctlHTSCode_lbl").css("color", "red");
            //
        }
        if (res.IHSDutyCode != null) {

        }

        //  this._IHSProductNo = res.IHSProductNo;
        this._IHSProduct = res.IHSProduct;
        this._IHSHTSCode = res.IHSHTSCode;
        this._IHSDutyCode = res.IHSDutyCode;


        this._IsPOHub = res.IsPOHub;
        if (this._IsPOHub == true) {
            //$("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl13_ctlAveragePrice").show();
            this.showField("ctlAveragePrice", false);
            this.setFieldValue("ctlAveragePrice", res.AveragePrice);
        }
        else {
            this.showField("ctlAveragePrice", false);
            //$("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl13_ctlAveragePrice").hide();
        }
        //[010] code start
        //ECCN Code Defination
        if (res.IHSECCNSCodeDefination != null && res.IHSECCNSCodeDefination != '') {
            this.setFieldValue("ctlECCNCode", $R_FN.showIHSECCNCodeDefi(res.ECCNCode, res.IHSECCNSCodeDefination));
        }
        else {
            this.setFieldValue("ctlECCNCode", res.ECCNCode);
        }

       
        //[010] code end
        this._ECCNCode = res.ECCNCode;
        //[005] start
        this._salesmanNo = res.SalesmanNo;
        //[005] end
        this.setDLUP(res.DLUP);
        //REQ test
        this.setFieldValue("ctlParentRequirementNo", $RGT_nubButton_CustomerRequirement(res.ParentRequirementId, res.ParentRequirementNo));

        if (res.PurchasingNotes != null && res.PurchasingNotes != '')
        {
            this.setFieldValue("ctlPurchasingNotes", $R_FN.setCleanTextValue($R_FN.showYellowText(res.PurchasingNotes)));
        }
        else
        {
            this.setFieldValue("ctlPurchasingNotes", $R_FN.setCleanTextValue(res.PurchasingNotes));
        }

        $R_FN.setInnerHTML(this._lblLineTitle, this._tbl.getSelectedExtraData().Part);

        $R_FN.showElement(this._pnlLineDetail, true);
        $R_FN.showElement(this._pnlLoadingLineDetail, false);
        this.showLoading(false);
        this._blnLineLoaded = true;
        // 1-Alternate,2-Possible Alternate,3-Firm Alternate
        if (res.AltStatus == 2) {
            if (this._ibtnMarkPossible) {
                $R_IBTN.updateText(this._ibtnMarkPossible, "Mark as Firm Alternate");
            }

            this._changeAltStatus = 3;
        }
        else if (res.AltStatus == 3) {
            if (this._ibtnMarkPossible) {
                $R_IBTN.updateText(this._ibtnMarkPossible, "Mark as Possible Alternate");
            }

            this._changeAltStatus = 2;
        }
        else {
            if (this._ibtnMarkPossible) {
                $R_IBTN.updateText(this._ibtnMarkPossible, "Mark as Firm Alternate");
            }

            this._changeAltStatus = 0;
        }
        this._altStatus = res.AltStatus;
        this._blnIsRestMFR = res.IsRestMFR;
        this._PartEditStatus = res.PartEditStatus;


        this.enableButtons(true);
        this.onGotDataOK();
        // alert(res.RequestToPOHubBy);
        if (this.getFieldValue("hidBOMID") > 0) {
            //  this._isPoRequest=res.IsPOHubReleased;
            this._isPoRequest = Number(res.RequestToPOHubBy) > 0 ? true : false;
            //  alert(this._isPoRequest);
            if (this._ibtnExportPurchaseHUB) {
                $R_IBTN.enableButton(this._ibtnExportPurchaseHUB, false);
            }

            // $R_IBTN.enableButton(this._ibtnEdit, false);
            if (this._ibtnQuote) {
                $R_IBTN.enableButton(this._ibtnQuote, false);
            }

            if (this._ibtnAdd) {
                $R_IBTN.enableButton(this._ibtnAdd, !res.IsPOHubReleased);
            }

        } else {
            if (this._ibtnExportPurchaseHUB) {
                $R_IBTN.enableButton(this._ibtnExportPurchaseHUB, true);
            }

        }



        //Check BOM Status-NEW,OPEN then enable edit button.
        //if (this.getFieldValue("ctlBOMHeader").contains("NEW") || this.getFieldValue("ctlBOMHeader").contains("OPEN")) {
        if (this.getFieldValue("ctlBOMHeader").indexOf("NEW") != -1 || this.getFieldValue("ctlBOMHeader").indexOf("OPEN") != -1) {
            // alert(this.getFieldValue("hidBOMID"));
            $R_IBTN.enableButton(this._ibtnEdit, true);
            $R_IBTN.enableButton(this._ibtnAdd, true);

            //$R_IBTN.enableButton(this._ibtnExportPurchaseHUB, true);
        }
        //[006] start
        var soLink = '';
        if (res.SalesOrderNumber != '') {
            var arSalesOrderNumber = res.SalesOrderNumber.split(",");

            for (i = 0; i < arSalesOrderNumber.length; i++) {
                var SalesOrderNumber = arSalesOrderNumber[i].split(":");
                var soId = SalesOrderNumber[0];
                var soNo = SalesOrderNumber[1];
                soLink = soLink + $RGT_nubButton_SalesOrder(soId, soNo) + ' ';
            }
        }
        this.setFieldValue("ctlSalesOrderNo", soLink);
        this.setFieldValue("ctlSalespersion", $R_FN.setCleanTextValue(res.SupportTeamMember));
        this.setFieldValue("hidSalesPersion", res.SupportTeamMemberNo);
        //[006] end

        //[011] Start
        $R_FN.highlightBackgroundColorOfText("ctl00_cphMain_ctlMainInfo_ctlDB_ctl13_ctlAS6081_lbl", res.AS6081);
        // setting warning label in header of the page [start]
        if (res.AS6081) {
            $("#ctl00_cphMain_ctlPageTitle_ctl20_lblAS6081Status").text("This part requires AS6081 compliance, please ensure the appropriate supplier is chosen to fulfil this requirement.");
            $R_FN.highlightBackgroundColorOfText("ctl00_cphMain_ctlPageTitle_ctl20_lblAS6081Status", res.AS6081);
        } else {
            $("#ctl00_cphMain_ctlPageTitle_ctl20_lblAS6081Status").text("");
        }
        // setting warning label in header of the page [end]
        
        //[011] End
    },

    getLineDataError: function (args) {
        this.showLoading(false);
        $R_FN.showElement(this._pnlLoadingLineDetail, false);
        $R_FN.showElement(this._pnlLineDetailError, true);
        $R_FN.setInnerHTML(this._pnlLineDetailError, args.get_ErrorMessage());
    },

    showAddForm: function () {
        this.getBomNoForAdd();
    },
    getBomNoForAdd: function () {
        var obj = new Rebound.GlobalTrader.Site.Data();
        this.showLoading(true);
        $R_FN.showElement(this._pnlLineDetail, false);
        $R_FN.showElement(this._pnlLoadingLineDetail, true);
        $R_FN.showElement(this._pnlLineDetailError, false);
        obj.set_PathToData("controls/Nuggets/CusReqMainInfo");
        obj.set_DataObject("CusReqMainInfo");
        obj.set_DataAction("GetBomID");
        obj.addParameter("id", this._intCustomerRequirementID);
        obj.addParameter("CustomerRequirementNumber", this._strCustomerRequirementNumber);
        obj.addDataOK(Function.createDelegate(this, this.getBomNoForAddOK));
        obj.addError(Function.createDelegate(this, this.getLineDataError));
        obj.addTimeout(Function.createDelegate(this, this.getLineDataError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },
    getBomNoForAddOK: function (args) {
        var res = args._result;
        this._frmAdd._intBOMID = res.BOMNo;
        this._frmAdd._intOriginalCustomerRequirementID = this._intOriginalCustomerRequirementID;
        this._frmAdd._isPoRequest = this._isPoRequest;
        //[007] code star
        this._frmAdd._ctlCompany = this.getFieldValue("hidCompanyName");//this._ctlCompany;
        this._frmAdd._ctlContact = this.getFieldValue("hidContactName");//this._ctlContact;
        this._frmAdd._salesmanNo = this.getFieldValue("hidSalesman")//this._salesmanNo;
        this._frmAdd._ctlROHS = this.getFieldValue("hidROHS");//this._ctlROHS;
        this._frmAdd._ctlQuantity = this.getFieldValue("ctlQuantity");//this._ctlQuantity;
        this._frmAdd._ctlPartNo = this.getFieldValue("ctlPartNo");//this._ctlPartNo;
        this._frmAdd._ctlCustomerPart = this.getFieldValue("ctlCustomerPart");//this._ctlCustomerPart;
        this._frmAdd._ctlManufacturer = this._ctlManufacturer;//res.ManufacturerNo, res.Manufacturer
        this._frmAdd._hidManufacturer = this._hidManufacturer;
        this._frmAdd._hidManufacturerNo = this._hidManufacturerNo;
        this._frmAdd._ctlDateCode = this.getFieldValue("ctlDateCode"); //this._ctlDateCode;
        this._frmAdd._ctlProduct = this.getFieldValue("ctlProduct");
        this._frmAdd._hidProductID = this.getFieldValue("hidProductID");//this._hidProductID;
        this._frmAdd._ctlProductDis = this._ctlProductDis;//(res.Product, res.IsProdHaz)
        this._frmAdd._ctlPrdDutyCodeRate = this._ctlPrdDutyCodeRate;
        this._frmAdd._ctlPackage = this._ctlPackage;
        this._frmAdd._hidPackageID = this.getFieldValue("hidPackageID");//this._hidPackageID;
        this._frmAdd._ctlTargetPrice = this._ctlTargetPrice;
        this._frmAdd._hidPrice = this.getFieldValue("hidPrice");//this._hidPrice;
        this._frmAdd._ctlCurrency = this._ctlCurrency;
        this._frmAdd._hidCurrencyID = this.getFieldValue("hidCurrencyID")
        this._frmAdd._ctlDateRequired = this.getFieldValue("ctlDateRequired");//this._ctlDateRequired;
        this._frmAdd._ctlUsage = this._ctlUsage;
        this._frmAdd._hidUsageID = this.getFieldValue("hidUsageID");//this._hidUsageID;
        this._frmAdd._ctlNotes = this.getFieldValue("ctlNotes");//this._ctlNotes;

        this._frmAdd._ctlInstructions = this.getFieldValue("ctlInstructions");//this._ctlInstructions;
        //this._frmAdd._ctlROHS = this._ctlROHS;
        this._frmAdd._hidROHS = this._hidROHS;
        this._frmAdd._ctlClosed = this._ctlClosed;
        this._frmAdd._ctlClosedReason = this._ctlClosedReason;
        this._frmAdd._hidDisplayStatus = this._hidDisplayStatus;
        this._frmAdd._ctlPartWatch = this.getFieldValue("ctlPartWatch");//this._ctlPartWatch;
        this._frmAdd._ctlBOM = this.getFieldValue("ctlBOM");//this._ctlBOM;
        this._frmAdd._ctlBOMName = this.getFieldValue("ctlBOMName");//this._ctlBOMName;
        this._frmAdd._ctlBOMHeader = this._ctlBOMHeader;//res.BOMId, $R_FN.setCleanTextValue(res.BOMHeader
        this._frmAdd._hidBOMID = this._hidBOMID;
        // this._frmAdd._hidBOMHeaderDisplayStatus = this._hidBOMHeaderDisplayStatus;
        this._frmAdd._ctlMSL = this._ctlMSL;
        this._frmAdd._ctlFactorySealed = this.getFieldValue("ctlFactorySealed");//this._ctlFactorySealed;
        this._frmAdd._ctlPQA = this.getFieldValue("ctlPQA");//this._ctlPQA;
        this._frmAdd._ctlTargetSellPrice = this._ctlTargetSellPrice;
        this._frmAdd._ctlCompetitorBestoffer = this._ctlCompetitorBestoffer;
        this._frmAdd._ctlCustomerDecisionDate = this._ctlCustomerDecisionDate;
        this._frmAdd._ctlRFQClosingDate = this._ctlRFQClosingDate;
        this._frmAdd._ctlQuoteValidityRequiredHid = this._ctlQuoteValidityRequiredHid;
        this._frmAdd._ctlQuoteValidityRequired = this._ctlQuoteValidityRequired;
        this._frmAdd._ctlTypeHid = this.getFieldValue("ctlTypeHid");//this._ctlTypeHid;
        //this._frmAdd._ctlType = this._ctlType;
        this._frmAdd._ctlOrderToPlace = this._ctlOrderToPlace;
        // this._frmAdd._ctlRequirementforTraceability = this._ctlRequirementforTraceability;
        this._frmAdd._ctlRequirementforTraceabilityHid = this.getFieldValue("ctlRequirementforTraceabilityHid");//this._ctlRequirementforTraceabilityHid;
        this._frmAdd._ctlTargetSellPriceHidden = this.getFieldValue("ctlTargetSellPriceHidden");//this._ctlTargetSellPriceHidden;
        this._frmAdd._ctlCompetitorBestofferHidden = this.getFieldValue("ctlCompetitorBestofferHidden");//this._ctlCompetitorBestofferHidden;
        this._frmAdd._ctlEAU = this.getFieldValue("ctlEAU");//this._ctlEAU;
        this._frmAdd._hidCustGCNo = this.getFieldValue("hidCustGCNo");
        //this._frmAdd._ctlAlternativesAccepted = this._ctlAlternativesAccepted;
        //this._frmAdd._ctlRepeatBusiness = this._ctlRepeatBusiness;
        this._frmAdd._blnProdInactive = this._blnProdInactive;
        //this._frmAdd._strhidMSL = this._strhidMSL;
        this._frmAdd._ctlSalespersion = this.getFieldValue("ctlSalespersion");
        this._frmAdd._hidSalesPersion = this.getFieldValue("hidSalesPersion")
        //this._frmAdd._strhidMSL = this._strhidMS;
        this._frmAdd._hidCountryOfOrigin = this._hidCountryOfOrigin;
        this._frmAdd._hidCountryOfOriginNo = this._hidCountryOfOriginNo;
        this._frmAdd._hidLifeCycleStage = this._hidLifeCycleStage;
        this._frmAdd._hidHTSCode = this._hidHTSCode;
        this._frmAdd._hidAveragePrice = this._hidAveragePrice;
        this._frmAdd._hidPackaging = this._hidPackaging;
        this._frmAdd._hidPackagingSize = this._hidPackagingSize;
        this._frmAdd._intCompanyID = this.getFieldValue("hidCompanyID");
        this._frmAdd._radioCheck = this.getFieldValue("ctlFactorySealed");
        this._frmAdd._radObsoleteChk = this.getFieldValue("ctlObsolete");
        this._frmAdd._radLastTimeBuyChk = this.getFieldValue("ctlLastTimeBuy");
        this._frmAdd._radRefirbsAcceptableChk = this.getFieldValue("ctlRefirbsAcceptable");
        this._frmAdd._radTestingRequiredChk = this.getFieldValue("ctlTestingRequired");
        this._frmAdd._radAlternativesAcceptedChK = this.getFieldValue("ctlAlternativesAccepted");
        this._frmAdd._radRepeatBusinessChk = this.getFieldValue("ctlRepeatBusiness");
        //[007] code star
        this._frmAdd._intCustomerRequirementID = this._intCustomerRequirementID;
        this._intBOMID = res.BOMNo;
        this._frmAdd._BOMHeaderDisplayStatus = Boolean.parse(this.getFieldValue("hidBOMHeaderDisplayStatus"));
        this._frmAdd._blnCurInSameFaimly = res.IsSameCurFam;
        this._frmAdd._intCurrencyNo = this.getFieldValue("hidCurrencyID")
        this._frmAdd._IHSProductNo = this._IHSProductNo;
        this._frmAdd._IHSProduct = this._IHSProduct;
        this._frmAdd._IHSHTSCode = this._IHSHTSCode;
        this._frmAdd._IHSDutyCode = this._IHSDutyCode;
        this._frmAdd._AlternateStatus = this._AlternateStatus;
        this._frmAdd._ECCNCode = this._ECCNCode;
        this._frmAdd._PartEditStatus = 1;//this._PartEditStatus;

        this.showLoading(false);
        this.showForm(this._frmAdd, true);
        this.onGotDataOK();
    },
    //start [005] 
    saveAllAlternate: function () {
        this.showLoading(true);
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/CusReqMainInfo");
        obj.set_DataObject("CusReqMainInfo");
        obj.set_DataAction("AddAlternateAll");
        obj.addParameter("Part", this.getFieldValue("ctlPartNo"));
        obj.addParameter("id", this._intCustomerRequirementID);
        //._frmAdd._strCustomerRequirementNumber = this._strCustomerRequirementNumber;
        obj.addDataOK(Function.createDelegate(this, this.saveAllAlternateAddComplete));
        obj.addError(Function.createDelegate(this, this.saveAllAlternateEditError));
        obj.addTimeout(Function.createDelegate(this, this.saveAllAlternateEditError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;

    },

    saveAllAlternateAddComplete: function (args) {
        this.showLoading(false);
        if (args._result.NewID > 0) {
            //this.showSavedOK(true, $R_RES.ChangesSavedSuccessfully);
            this.showSavedOK(true, String.format("{0} alternate(s) added successfully in this requirement", args._result.NewID));
            this.getData();
            this.showSaving(false);
            this.showError(false);

        } else {
            alert("This part doesn't have any Alternate.");

        }

    },
    saveAllAlternateEditError: function (args) {
        this._strErrorMessage = args._errorMessage;
        this.showLoading(false);
        this.showError(false);
        this.showNuggetError(true, this._strErrorMessage);
        var handler = this.get_events().getHandler("SaveError");
        if (handler) handler(this, Sys.EventArgs.Empty);
    },
    //end [005] 
    saveEditError: function (args) {
        this._strErrorMessage = args._errorMessage;
        this.onSaveError();
    },


    cancelAdd: function () {
        this.showForm(this._frmAdd, false);
        this.getData();
    },

    saveAddComplete: function () {
        this.showForm(this._frmAdd, false);
        this._intCustomerRequirementID = this._frmAdd._intNewID;
        this.showSavedOK(true, $R_RES.ChangesSavedSuccessfully);
        this.getData();
    },

    showCloseForm: function () {
        this._frmClose._intCustomerRequirementID = this._intCustomerRequirementID;
        this.showForm(this._frmClose, true);
    },

    cancelClose: function () {
        this.showForm(this._frmClose, false);
        this._tbl.resizeColumns();
    },

    saveCloseComplete: function () {
        this.showForm(this._frmClose, false);
        this.showSavedOK(true, $R_RES.ChangesSavedSuccessfully);
        this.getData();
    },

    //ihs code start
    showAltDeleteForm: function () {
        if (this._aryCheckedLineIDs != "" && this._aryCheckedLineIDs != null) {
            // this._frmDelete._intCustomerRequirementID = this._intCustomerRequirementID;
            this._frmDelete._aryCheckedLineIDs = this._aryCheckedLineIDs;
            this.showForm(this._frmDelete, true);
        }
        else {
            alert("please select alternate part for delete!");
        }
    },

    cancelAltDelete: function () {
        this.showForm(this._frmDelete, false);
        this._tbl.resizeColumns();
    },

    saveAltDeleteComplete: function () {
        this._intCustomerRequirementID = this._intOriginalCustomerRequirementID;
        this.showForm(this._frmDelete, false);
        this._tbl.addRow(this._intCustomerRequirementID);
        this.showSavedOK(true, $R_RES.ChangesSavedSuccessfully);
        this.getData();
    },
    //ihs code end

    printCustRequirement: function () {
        var strIDs = this._strCustomerRequirementNumber; // $R_FN.arrayToSingleString(this._intCustomerRequirementID, "|");
        $R_FN.setCookie("Req", strIDs, 1); strIDs = "";
        $R_FN.openPrintWindowCustReqWithMultiples($R_ENUM$PrintObject.SingleRequirement, this.getFieldValue("hidCompanyID"));
        strIDs = null;
      
    },
    
    showConfirmForm: function () {
        this._frmConfirm._intCustomerRequirementID = this._intCustomerRequirementID;
        this._frmConfirm._intCurrencyID = this.getFieldValue("hidCurrencyID");
        this._frmConfirm._intCompanyID = this.getFieldValue("hidCompanyID");

        var ReqValidated = true;
        if (this.getFieldValue("hidManufacturerNo") == null || this.getFieldValue("hidManufacturerNo") <= 0)
            ReqValidated = false;

        if (this.getFieldValue("ctlQuantity") < 0)
            ReqValidated = false;

        if (this.getFieldValue("hidContactID") <= 0)
            ReqValidated = false;

        if (this.getFieldValue("ctlPartNo") == null || this.getFieldValue("ctlPartNo").length <= 0)
            ReqValidated = false;

        if (this.getFieldValue("hidCurrencyID") == null || this.getFieldValue("hidCurrencyID") <= 0)
            ReqValidated = false;

        if (this.getFieldValue("hidProductID") == null || this.getFieldValue("hidProductID") <= 0)
            ReqValidated = false;

        if (this.getFieldValue("ctlRequirementforTraceabilityHid") == null || this.getFieldValue("ctlRequirementforTraceabilityHid") <= 0)
            ReqValidated = false;
        //[004] start
        //if (this.getFieldValue("ctlMSL") ==="" ||this.getFieldValue("ctlMSL") == null)
        //    ReqValidated = false;
        if (this.getFieldValue("ctlTypeHid") === "" || this.getFieldValue("ctlTypeHid") == null)
            ReqValidated = false;
        //[004] end
        this._frmConfirm._ctlPartNo = this.getFieldValue("ctlPartNo");
        this._frmConfirm._blnReqValidated = ReqValidated;

        this.showForm(this._frmConfirm, true);
    },
    hideConfirmForm: function () {
        this._frmConfirm.setFieldValue("ctlSalesperson", "");
        this.showForm(this._frmConfirm, false);
    },

    cancelConfirm: function () {
        this.hideConfirmForm();
    },
    saveConfirmComplete: function () {
        this.hideConfirmForm();
        // this.enableButtons(true);
        //this.showSavedOK(true, $R_RES.ChangesSavedSuccessfully);
        this.showSavedOK(true, "HUBRFQ has been sent for Price Request successfully.");
        this.getData();
    },

    saveRequirementData: function () {
        this.getData_Start();
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/CusReqMainInfo");
        obj.set_DataObject("CusReqMainInfo");
        obj.set_DataAction("SaveRequirementData");
        obj.addParameter("id", this._intCustomerRequirementID);
        obj.addParameter("CompanyNo", this.getFieldValue("hidCompanyID"));
        obj.addParameter("ContactNo", this.getFieldValue("hidContactID"));
        obj.addDataOK(Function.createDelegate(this, this.getDataOK));
        obj.addError(Function.createDelegate(this, this.getDataError));
        obj.addTimeout(Function.createDelegate(this, this.getDataError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },
    markedFirmAlt: function () {
        this.showLoading(true);
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/CusReqMainInfo");
        obj.set_DataObject("CusReqMainInfo");
        obj.set_DataAction("ChangeAlternateStatus");
        obj.addParameter("id", this._intCustomerRequirementID);
        obj.addParameter("ChangeStatus", this._changeAltStatus);
        obj.addDataOK(Function.createDelegate(this, this.altStatusComplete));
        obj.addError(Function.createDelegate(this, this.altStatusError));
        obj.addTimeout(Function.createDelegate(this, this.altStatusError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    altStatusError: function (args) {
        this.showLoading(false);
        this._strErrorMessage = args._errorMessage;
        this.onSaveError();
    },

    altStatusComplete: function (args) {
        this.showLoading(false);
        if (args._result.Result == true) {
            this.showSavedOK(true, "Alternate status has been changed successfully.");
            this.getData();
            // this.showSaving(false);
            this.showError(false);
        } else {
            this._strErrorMessage = args._errorMessage;
            this.onSaveError();
        }
    },
    //[006] code start
    OpenTree: function () {
        //$R_FN.openDocumentTree(this._strCustomerRequirementNumber, "REQ");
        $R_FN.openDocumentTree(this._intCustomerRequirementID, "REQ", this._strCustomerRequirementNumber);

    },
    //[006] code end 
    //showCloneHUBRFQForm: function () {
    //    this._frmCloneHUBRFQ._intCustomerRequirementID = this._intCustomerRequirementID;
    //    //this._frmCloneHUBRFQ._blnReqValidated = ReqValidated;
    //    this._frmCloneHUBRFQ._intCompanyID = this.getFieldValue("hidCompanyID");
    //    this._frmCloneHUBRFQ._ctlCompany = this.getFieldValue("hidCompanyName");
    //    this._frmCloneHUBRFQ._strCustomerRequirementNumber = this._strCustomerRequirementNumber;
    //    this.showForm(this._frmCloneHUBRFQ, true);
    //},
    //saveCloneHUBRFQComplete: function () {
    //    this.hideCloneHUBRFQForm();
    //    // this.enableButtons(true);
    //    //this.showSavedOK(true, $R_RES.ChangesSavedSuccessfully);
    //    this.showSavedOK(true, "HUBRFQ has been added and clone successfully.");
    //    this.getData();
    //},
    //hideCloneHUBRFQForm: function () {
    //    this._frmCloneHUBRFQ.setFieldValue("ctlBOMHeader", "");
    //    this.showForm(this._frmConfirm, false);
    //},
    //cancelCloneHUBRFQ: function () {
    //    this.hideCloneHUBRFQForm();
    //},
    //
    //showCloneHUBForm: function () {
    //    this._frmCloneHUB._intCustomerRequirementID = this._intCustomerRequirementID;
    //    this._frmCloneHUB._intCompanyID = this.getFieldValue("hidCompanyID");
    //    this._frmCloneHUB._ctlCompany = this.getFieldValue("hidCompanyName");
    //    this._frmCloneHUB._strCustomerRequirementNumber = this._strCustomerRequirementNumber;
    //    this.showForm(this._frmCloneHUB, true);
    //},
    //saveCloneHUBComplete: function () {
    //    this.hideCloneHUBForm();
    //    // this.enableButtons(true);
    //    //this.showSavedOK(true, $R_RES.ChangesSavedSuccessfully);
    //    this.showSavedOK(true, "Clone successfully and send to HUB.");
    //    this.getData();
    //},
    //hideCloneHUBForm: function () {
    //    this.showForm(this._frmConfirm, false);
    //},
    //cancelCloneHUB: function () {
    //    this.hideCloneHUBForm();
    //},
    //code adeded for clone HUBRFQ
    getEditCloneHUBRFQBomDataOK: function (args) {
        var res = args._result;
        this._frmEditCloneHUBRFQ._intBOMID = res.BOMNo;
        this._frmEditCloneHUBRFQ._isPoRequest = this._isPoRequest;
        //[007] code star
        this._frmEditCloneHUBRFQ._ctlCompany = this.getFieldValue("hidCompanyName");//this._ctlCompany;
        this._frmEditCloneHUBRFQ._ctlContact = this.getFieldValue("hidContactName");//this._ctlContact;
        this._frmEditCloneHUBRFQ._salesmanNo = this.getFieldValue("hidSalesman")//this._salesmanNo;
        this._frmEditCloneHUBRFQ._ctlROHS = this.getFieldValue("hidROHS");//this._ctlROHS;
        this._frmEditCloneHUBRFQ._ctlQuantity = this.getFieldValue("ctlQuantity");//this._ctlQuantity;
        this._frmEditCloneHUBRFQ._ctlPartNo = this.getFieldValue("ctlPartNo");//this._ctlPartNo;
        this._frmEditCloneHUBRFQ._ctlCustomerPart = this.getFieldValue("ctlCustomerPart");//this._ctlCustomerPart;
        this._frmEditCloneHUBRFQ._ctlManufacturer = this._ctlManufacturer;//res.ManufacturerNo, res.Manufacturer
        this._frmEditCloneHUBRFQ._hidManufacturer = this._hidManufacturer;
        this._frmEditCloneHUBRFQ._hidManufacturerNo = this._hidManufacturerNo;
        this._frmEditCloneHUBRFQ._ctlDateCode = this.getFieldValue("ctlDateCode"); //this._ctlDateCode;
        this._frmEditCloneHUBRFQ._ctlProduct = this.getFieldValue("ctlProduct");
        this._frmEditCloneHUBRFQ._hidProductID = this.getFieldValue("hidProductID");//this._hidProductID;
        this._frmEditCloneHUBRFQ._ctlProductDis = this._ctlProductDis;//(res.Product, res.IsProdHaz)
        this._frmEditCloneHUBRFQ._ctlPrdDutyCodeRate = this._ctlPrdDutyCodeRate;
        this._frmEditCloneHUBRFQ._ctlPackage = this._ctlPackage;
        this._frmEditCloneHUBRFQ._hidPackageID = this.getFieldValue("hidPackageID");//this._hidPackageID;
        this._frmEditCloneHUBRFQ._ctlTargetPrice = this._ctlTargetPrice;
        this._frmEditCloneHUBRFQ._hidPrice = this.getFieldValue("hidPrice");//this._hidPrice;
        this._frmEditCloneHUBRFQ._ctlCurrency = this._ctlCurrency;
        this._frmEditCloneHUBRFQ._hidCurrencyID = this.getFieldValue("hidCurrencyID")
        this._frmEditCloneHUBRFQ._ctlDateRequired = this.getFieldValue("ctlDateRequired");//this._ctlDateRequired;
        this._frmEditCloneHUBRFQ._ctlUsage = this._ctlUsage;
        this._frmEditCloneHUBRFQ._hidUsageID = this.getFieldValue("hidUsageID");//this._hidUsageID;
        this._frmEditCloneHUBRFQ._ctlNotes = this.getFieldValue("ctlNotes");//this._ctlNotes;
        this._frmEditCloneHUBRFQ._ctlInstructions = this.getFieldValue("ctlInstructions");//this._ctlInstructions;
        this._frmEditCloneHUBRFQ._hidROHS = this._hidROHS;
        this._frmEditCloneHUBRFQ._ctlClosed = this._ctlClosed;
        this._frmEditCloneHUBRFQ._ctlClosedReason = this._ctlClosedReason;
        this._frmEditCloneHUBRFQ._hidDisplayStatus = this._hidDisplayStatus;
        this._frmEditCloneHUBRFQ._ctlPartWatch = this.getFieldValue("ctlPartWatch");//this._ctlPartWatch;
        this._frmEditCloneHUBRFQ._ctlBOM = this.getFieldValue("ctlBOM");//this._ctlBOM;
        this._frmEditCloneHUBRFQ._ctlBOMName = this.getFieldValue("ctlBOMName");//this._ctlBOMName;
        this._frmEditCloneHUBRFQ._ctlBOMHeader = this._ctlBOMHeader;//res.BOMId, $R_FN.setCleanTextValue(res.BOMHeader
        this._frmEditCloneHUBRFQ._hidBOMID = this._hidBOMID;
        this._frmEditCloneHUBRFQ._ctlMSL = this._ctlMSL;
        this._frmEditCloneHUBRFQ._ctlFactorySealed = this.getFieldValue("ctlFactorySealed");//this._ctlFactorySealed;
        this._frmEditCloneHUBRFQ._ctlPQA = this.getFieldValue("ctlPQA");//this._ctlPQA;
        this._frmEditCloneHUBRFQ._ctlTargetSellPrice = this._ctlTargetSellPrice;
        this._frmEditCloneHUBRFQ._ctlCompetitorBestoffer = this._ctlCompetitorBestoffer;
        this._frmEditCloneHUBRFQ._ctlCustomerDecisionDate = this._ctlCustomerDecisionDate;
        this._frmEditCloneHUBRFQ._ctlRFQClosingDate = this._ctlRFQClosingDate;
        this._frmEditCloneHUBRFQ._ctlQuoteValidityRequiredHid = this._ctlQuoteValidityRequiredHid;
        this._frmEditCloneHUBRFQ._ctlQuoteValidityRequired = this._ctlQuoteValidityRequired;
        this._frmEditCloneHUBRFQ._ctlTypeHid = this.getFieldValue("ctlTypeHid");//this._ctlTypeHid;
        this._frmEditCloneHUBRFQ._ctlOrderToPlace = this._ctlOrderToPlace;
        this._frmEditCloneHUBRFQ._ctlRequirementforTraceabilityHid = this.getFieldValue("ctlRequirementforTraceabilityHid");//this._ctlRequirementforTraceabilityHid;
        this._frmEditCloneHUBRFQ._ctlTargetSellPriceHidden = this.getFieldValue("ctlTargetSellPriceHidden");//this._ctlTargetSellPriceHidden;
        this._frmEditCloneHUBRFQ._ctlCompetitorBestofferHidden = this.getFieldValue("ctlCompetitorBestofferHidden");//this._ctlCompetitorBestofferHidden;
        this._frmEditCloneHUBRFQ._ctlEAU = this.getFieldValue("ctlEAU");//this._ctlEAU;
        this._frmEditCloneHUBRFQ._hidCustGCNo = this.getFieldValue("hidCustGCNo");
        this._frmEditCloneHUBRFQ._blnProdInactive = this._blnProdInactive;
        this._frmEditCloneHUBRFQ._ctlSalespersion = this.getFieldValue("ctlSalespersion");
        this._frmEditCloneHUBRFQ._hidSalesPersion = this.getFieldValue("hidSalesPersion")
        this._frmEditCloneHUBRFQ._hidCountryOfOrigin = this._hidCountryOfOrigin;
        this._frmEditCloneHUBRFQ._hidCountryOfOriginNo = this._hidCountryOfOriginNo;
        this._frmEditCloneHUBRFQ._hidLifeCycleStage = this._hidLifeCycleStage;
        this._frmEditCloneHUBRFQ._hidHTSCode = this._hidHTSCode;
        this._frmEditCloneHUBRFQ._hidAveragePrice = this._hidAveragePrice;
        this._frmEditCloneHUBRFQ._hidPackaging = this._hidPackaging;
        this._frmEditCloneHUBRFQ._hidPackagingSize = this._hidPackagingSize;
        this._frmEditCloneHUBRFQ._intCompanyID = this.getFieldValue("hidCompanyID");
        this._frmEditCloneHUBRFQ._radioCheck = this.getFieldValue("ctlFactorySealed");
        this._frmEditCloneHUBRFQ._radObsoleteChk = this.getFieldValue("ctlObsolete");
        this._frmEditCloneHUBRFQ._radLastTimeBuyChk = this.getFieldValue("ctlLastTimeBuy");
        this._frmEditCloneHUBRFQ._radRefirbsAcceptableChk = this.getFieldValue("ctlRefirbsAcceptable");
        this._frmEditCloneHUBRFQ._radTestingRequiredChk = this.getFieldValue("ctlTestingRequired");
        this._frmEditCloneHUBRFQ._radAlternativesAcceptedChK = this.getFieldValue("ctlAlternativesAccepted");
        this._frmEditCloneHUBRFQ._radRepeatBusinessChk = this.getFieldValue("ctlRepeatBusiness");
        this._frmEditCloneHUBRFQ._intCustomerRequirementID = this._intCustomerRequirementID;
        this._intBOMID = res.BOMNo;
        this._frmEditCloneHUBRFQ._BOMHeaderDisplayStatus = Boolean.parse(this.getFieldValue("hidBOMHeaderDisplayStatus"));
        this._frmEditCloneHUBRFQ._blnCurInSameFaimly = res.IsSameCurFam;
        this._frmEditCloneHUBRFQ._intCurrencyNo = this.getFieldValue("hidCurrencyID")
        this._frmEditCloneHUBRFQ._IHSProductNo = this._IHSProductNo;
        this._frmEditCloneHUBRFQ._IHSProduct = this._IHSProduct;
        this._frmEditCloneHUBRFQ._IHSHTSCode = this._IHSHTSCode;
        this._frmEditCloneHUBRFQ._IHSDutyCode = this._IHSDutyCode;
        this._frmEditCloneHUBRFQ._AlternateStatus = this._AlternateStatus;
        this._frmEditCloneHUBRFQ._ECCNCode = this._ECCNCode;
        this._frmEditCloneHUBRFQ._PartEditStatus = this._PartEditStatus
        this.showLoading(false);
        this.showForm(this._frmEditCloneHUBRFQ, true);
        this.onGotDataOK();





    },
    getCloneHUBRFQBomNo: function () {
        var obj = new Rebound.GlobalTrader.Site.Data();
        this.showLoading(true);
        $R_FN.showElement(this._pnlLineDetail, false);
        $R_FN.showElement(this._pnlLoadingLineDetail, true);
        $R_FN.showElement(this._pnlLineDetailError, false);
        obj.set_PathToData("controls/Nuggets/CusReqMainInfo");
        obj.set_DataObject("CusReqMainInfo");
        obj.set_DataAction("GetBomID");
        obj.addParameter("id", this._intCustomerRequirementID);
        obj.addParameter("CustomerRequirementNumber", this._strCustomerRequirementNumber);
        obj.addDataOK(Function.createDelegate(this, this.getEditCloneHUBRFQBomDataOK));
        obj.addError(Function.createDelegate(this, this.getLineDataError));
        obj.addTimeout(Function.createDelegate(this, this.getLineDataError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },
    showEditCloneHUBRFQForm: function () {
        this.getCloneHUBRFQBomNo();
    },
    hideEditCloneHUBRFQForm: function () {
        this.showForm(this._frmEditCloneHUBRFQ, false);
    },
    cancelEditCloneHUBRFQ: function () {
        this.hideEditCloneHUBRFQForm();
        this.getData();
    },
    saveEditCloneHUBRFQComplete: function () {
        this.hideEditCloneHUBRFQForm();
        // this.showSavedOK(true, $R_RES.ChangesSavedSuccessfully);
        this.showSavedOK(true, "HUBRFQ has been added and clone successfully.");
        this.onSaveEditComplete();
        this.getData();
    },
    //code end

    //code added for clone HUB
    getEditCloneHUBBomDataOK: function (args) {
        var res = args._result;
        this._frmEditCloneHUB._intBOMID = res.BOMNo;
        this._frmEditCloneHUB._isPoRequest = this._isPoRequest;
        //[007] code star
        this._frmEditCloneHUB._ctlCompany = this.getFieldValue("hidCompanyName");//this._ctlCompany;
        this._frmEditCloneHUB._ctlContact = this.getFieldValue("hidContactName");//this._ctlContact;
        this._frmEditCloneHUB._salesmanNo = this.getFieldValue("hidSalesman")//this._salesmanNo;
        this._frmEditCloneHUB._ctlROHS = this.getFieldValue("hidROHS");//this._ctlROHS;
        this._frmEditCloneHUB._ctlQuantity = this.getFieldValue("ctlQuantity");//this._ctlQuantity;
        this._frmEditCloneHUB._ctlPartNo = this.getFieldValue("ctlPartNo");//this._ctlPartNo;
        this._frmEditCloneHUB._ctlCustomerPart = this.getFieldValue("ctlCustomerPart");//this._ctlCustomerPart;
        this._frmEditCloneHUB._ctlManufacturer = this._ctlManufacturer;//res.ManufacturerNo, res.Manufacturer
        this._frmEditCloneHUB._hidManufacturer = this._hidManufacturer;
        this._frmEditCloneHUB._hidManufacturerNo = this._hidManufacturerNo;
        this._frmEditCloneHUB._ctlDateCode = this.getFieldValue("ctlDateCode"); //this._ctlDateCode;
        this._frmEditCloneHUB._ctlProduct = this.getFieldValue("ctlProduct");
        this._frmEditCloneHUB._hidProductID = this.getFieldValue("hidProductID");//this._hidProductID;
        this._frmEditCloneHUB._ctlProductDis = this._ctlProductDis;//(res.Product, res.IsProdHaz)
        this._frmEditCloneHUB._ctlPrdDutyCodeRate = this._ctlPrdDutyCodeRate;
        this._frmEditCloneHUB._ctlPackage = this._ctlPackage;
        this._frmEditCloneHUB._hidPackageID = this.getFieldValue("hidPackageID");//this._hidPackageID;
        this._frmEditCloneHUB._ctlTargetPrice = this._ctlTargetPrice;
        this._frmEditCloneHUB._hidPrice = this.getFieldValue("hidPrice");//this._hidPrice;
        this._frmEditCloneHUB._ctlCurrency = this._ctlCurrency;
        this._frmEditCloneHUB._hidCurrencyID = this.getFieldValue("hidCurrencyID")
        this._frmEditCloneHUB._ctlDateRequired = this.getFieldValue("ctlDateRequired");//this._ctlDateRequired;
        this._frmEditCloneHUB._ctlUsage = this._ctlUsage;
        this._frmEditCloneHUB._hidUsageID = this.getFieldValue("hidUsageID");//this._hidUsageID;
        this._frmEditCloneHUB._ctlNotes = this.getFieldValue("ctlNotes");//this._ctlNotes;
        this._frmEditCloneHUB._ctlInstructions = this.getFieldValue("ctlInstructions");//this._ctlInstructions;
        this._frmEditCloneHUB._hidROHS = this._hidROHS;
        this._frmEditCloneHUB._ctlClosed = this._ctlClosed;
        this._frmEditCloneHUB._ctlClosedReason = this._ctlClosedReason;
        this._frmEditCloneHUB._hidDisplayStatus = this._hidDisplayStatus;
        this._frmEditCloneHUB._ctlPartWatch = this.getFieldValue("ctlPartWatch");//this._ctlPartWatch;
        this._frmEditCloneHUB._ctlBOM = this.getFieldValue("ctlBOM");//this._ctlBOM;
        this._frmEditCloneHUB._ctlBOMName = this.getFieldValue("ctlBOMName");//this._ctlBOMName;
        this._frmEditCloneHUB._ctlBOMHeader = this._ctlBOMHeader;//res.BOMId, $R_FN.setCleanTextValue(res.BOMHeader
        this._frmEditCloneHUB._hidBOMID = this._hidBOMID;
        this._frmEditCloneHUB._ctlMSL = this._ctlMSL;
        this._frmEditCloneHUB._ctlFactorySealed = this.getFieldValue("ctlFactorySealed");//this._ctlFactorySealed;
        this._frmEditCloneHUB._ctlPQA = this.getFieldValue("ctlPQA");//this._ctlPQA;
        this._frmEditCloneHUB._ctlTargetSellPrice = this._ctlTargetSellPrice;
        this._frmEditCloneHUB._ctlCompetitorBestoffer = this._ctlCompetitorBestoffer;
        this._frmEditCloneHUB._ctlCustomerDecisionDate = this._ctlCustomerDecisionDate;
        this._frmEditCloneHUB._ctlRFQClosingDate = this._ctlRFQClosingDate;
        this._frmEditCloneHUB._ctlQuoteValidityRequiredHid = this._ctlQuoteValidityRequiredHid;
        this._frmEditCloneHUB._ctlQuoteValidityRequired = this._ctlQuoteValidityRequired;
        this._frmEditCloneHUB._ctlTypeHid = this.getFieldValue("ctlTypeHid");//this._ctlTypeHid;
        this._frmEditCloneHUB._ctlOrderToPlace = this._ctlOrderToPlace;
        this._frmEditCloneHUB._ctlRequirementforTraceabilityHid = this.getFieldValue("ctlRequirementforTraceabilityHid");//this._ctlRequirementforTraceabilityHid;
        this._frmEditCloneHUB._ctlTargetSellPriceHidden = this.getFieldValue("ctlTargetSellPriceHidden");//this._ctlTargetSellPriceHidden;
        this._frmEditCloneHUB._ctlCompetitorBestofferHidden = this.getFieldValue("ctlCompetitorBestofferHidden");//this._ctlCompetitorBestofferHidden;
        this._frmEditCloneHUB._ctlEAU = this.getFieldValue("ctlEAU");//this._ctlEAU;
        this._frmEditCloneHUB._hidCustGCNo = this.getFieldValue("hidCustGCNo");
        this._frmEditCloneHUB._blnProdInactive = this._blnProdInactive;
        this._frmEditCloneHUB._ctlSalespersion = this.getFieldValue("ctlSalespersion");
        this._frmEditCloneHUB._hidSalesPersion = this.getFieldValue("hidSalesPersion")
        this._frmEditCloneHUB._hidCountryOfOrigin = this._hidCountryOfOrigin;
        this._frmEditCloneHUB._hidCountryOfOriginNo = this._hidCountryOfOriginNo;
        this._frmEditCloneHUB._hidLifeCycleStage = this._hidLifeCycleStage;
        this._frmEditCloneHUB._hidHTSCode = this._hidHTSCode;
        this._frmEditCloneHUB._hidAveragePrice = this._hidAveragePrice;
        this._frmEditCloneHUB._hidPackaging = this._hidPackaging;
        this._frmEditCloneHUB._hidPackagingSize = this._hidPackagingSize;
        this._frmEditCloneHUB._intCompanyID = this.getFieldValue("hidCompanyID");
        this._frmEditCloneHUB._radioCheck = this.getFieldValue("ctlFactorySealed");
        this._frmEditCloneHUB._radObsoleteChk = this.getFieldValue("ctlObsolete");
        this._frmEditCloneHUB._radLastTimeBuyChk = this.getFieldValue("ctlLastTimeBuy");
        this._frmEditCloneHUB._radRefirbsAcceptableChk = this.getFieldValue("ctlRefirbsAcceptable");
        this._frmEditCloneHUB._radTestingRequiredChk = this.getFieldValue("ctlTestingRequired");
        this._frmEditCloneHUB._radAlternativesAcceptedChK = this.getFieldValue("ctlAlternativesAccepted");
        this._frmEditCloneHUB._radRepeatBusinessChk = this.getFieldValue("ctlRepeatBusiness");
        this._frmEditCloneHUB._intCustomerRequirementID = this._intCustomerRequirementID;
        this._intBOMID = res.BOMNo;
        this._frmEditCloneHUB._BOMHeaderDisplayStatus = Boolean.parse(this.getFieldValue("hidBOMHeaderDisplayStatus"));
        this._frmEditCloneHUB._blnCurInSameFaimly = res.IsSameCurFam;
        this._frmEditCloneHUB._intCurrencyNo = this.getFieldValue("hidCurrencyID")
        this._frmEditCloneHUB._IHSProductNo = this._IHSProductNo;
        this._frmEditCloneHUB._IHSProduct = this._IHSProduct;
        this._frmEditCloneHUB._IHSHTSCode = this._IHSHTSCode;
        this._frmEditCloneHUB._IHSDutyCode = this._IHSDutyCode;
        this._frmEditCloneHUB._AlternateStatus = this._AlternateStatus;
        this._frmEditCloneHUB._ECCNCode = this._ECCNCode;
        this._frmEditCloneHUB._PartEditStatus = this._PartEditStatus
        this.showLoading(false);
        this.showForm(this._frmEditCloneHUB, true);
        this.onGotDataOK();





    },
    getCloneHUBBomNo: function () {
        var obj = new Rebound.GlobalTrader.Site.Data();
        this.showLoading(true);
        $R_FN.showElement(this._pnlLineDetail, false);
        $R_FN.showElement(this._pnlLoadingLineDetail, true);
        $R_FN.showElement(this._pnlLineDetailError, false);
        obj.set_PathToData("controls/Nuggets/CusReqMainInfo");
        obj.set_DataObject("CusReqMainInfo");
        obj.set_DataAction("GetBomID");
        obj.addParameter("id", this._intCustomerRequirementID);
        obj.addParameter("CustomerRequirementNumber", this._strCustomerRequirementNumber);
        obj.addDataOK(Function.createDelegate(this, this.getEditCloneHUBBomDataOK));
        obj.addError(Function.createDelegate(this, this.getLineDataError));
        obj.addTimeout(Function.createDelegate(this, this.getLineDataError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },
    showEditCloneHUBForm: function () {
        this.getCloneHUBBomNo();
    },
    hideEditCloneHUBForm: function () {
        this.showForm(this._frmEditCloneHUB, false);
    },
    cancelEditCloneHUB: function () {
        this.hideEditCloneHUBForm();
        this.getData();
    },
    saveEditCloneHUBComplete: function () {
        this.hideEditCloneHUBForm();
        // this.showSavedOK(true, $R_RES.ChangesSavedSuccessfully);
        this.showSavedOK(true, "Clone successfully and send to HUB.");
        this.onSaveEditComplete();
        this.getData();
    },
    //code end
    //[011] start
    getAS6081BannerMessage: function () {
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("Controls/SetupNuggets/AS6081");
        obj.set_DataObject("AS6081");
        obj.set_DataAction("GetAlertMessageByOperationType");
        obj.addParameter("operationType", "ReceivePoBanner");
        obj.addDataOK(Function.createDelegate(this, this.getAS6081BannerMessageOK));
        obj.addError(Function.createDelegate(this, this.getAS6081BannerMessageError));
        obj.addTimeout(Function.createDelegate(this, this.getAS6081BannerMessageError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },
    getAS6081BannerMessageOK: function (args) {
        //this.disableSaveButton();
        var result = args._result;
        this.clearMessages();
        this.addMessage(result.Message, $R_ENUM$MessageTypeList.Warning);
    },
    getAS6081BannerMessageError: function (args) {
        console.error(`error occured while trying to fetch 'banner for AS6081'`);
    },
    //[011] end

    StartRefreshLylicaAPIData: function (partNumber, mfrCode, mfrNo) {
        partNumber = this.beautifyPartNumber(partNumber);

        $.ajax({
            type: 'POST',
            contentType: 'application/json',
            url: this.handlerUrl + '?action=RefreshLyticaAPIAfter3Days&PartNumber=' + partNumber + '&mfr=' + mfrCode + '&mfrNo=' + mfrNo,
            async: true,
            error: function () { /*alert("something went wrong");*/ }
        });
    },
    beautifyPartNumber: function (partNumber) {
        partNumber = partNumber.replace(" (Alternate)", "");
        partNumber = partNumber.replace("&", "_AMPERSAND_");
        partNumber = partNumber.replace("#", "_HASH_");
        partNumber = partNumber.replace("=", "_EQUALS_");

        return partNumber;
    }
};

Rebound.GlobalTrader.Site.Controls.Nuggets.CustomerRequirementMainInfo.registerClass("Rebound.GlobalTrader.Site.Controls.Nuggets.CustomerRequirementMainInfo", Rebound.GlobalTrader.Site.Controls.Nuggets.Base);
