
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE PROCEDURE [dbo].[usp_KubInsertAveragePriceDetailsCache]          
@PartNo NVARCHAR(100)=null,          
@ClientID int=null               
AS         
/*        
 * Action: Created  By: <PERSON><PERSON><PERSON><PERSON>  Date:03/08/2023  Comment: Add new table for holding the average price cache for KUB.        
 */                
BEGIN              
SET NOCOUNT ON;       
      
Declare @DefaultCurrency int = 0;      
Declare @DefaultCurrencyCode nvarchar(max);      
        
select @DefaultCurrency= CurrencyNo, @DefaultCurrencyCode= cr.CurrencyCode from tbClient c       
left join tbCurrency cr on c.CurrencyNo = cr.CurrencyId      
where ClientId = @ClientID;      
CREATE TABLE #tmp_AvgPrice  
(  
 ClientNo INT,  
 Part     NVARCHAR(100),  
 Price   Float,  
 Quantity INT    
 )  
      
IF((SELECT COUNT(1) FROM tb_KubAveragePrice_CacheDetails         
WHERE ClientNo=@ClientID AND Part=@PartNo AND DLUP>DATEAdd(DAY,-3,GETDATE()))=0)          
BEGIN          
DELETE FROM tb_KubAveragePrice_CacheDetails         
WHERE ClientNo=@ClientID AND Part=@PartNo         
                              
INSERT INTO #tmp_AvgPrice       
SELECT i.ClientNo, il.FullPart ,                
 CONVERT(DECIMAL(16,2)      
  ,SUM(dbo.ufn_convert_currency_value((il.Price),i.CurrencyNo,@DefaultCurrency,GETDATE()))*ila.Quantity      
  )      
  as Price   
  
  , SUM(ila.Quantity) AS  Quantity        
FROM tbInvoice AS i LEFT JOIN         
tbInvoiceLine AS il ON il.InvoiceNo = i.InvoiceId 
LEFT JOIN dbo.tbInvoiceLineAllocation ila  ON
il.InvoiceLineId=ila.InvoiceLineNo          
WHERE i.InvoiceDate > DATEADD(m, -12, current_timestamp)     
AND il.FullPart=@PartNo          
AND i.ClientNo=@ClientID    
GROUP BY  i.ClientNo,il.FullPart,i.CurrencyNo,ila.Quantity,il.Price  
  
INSERT INTO tb_KubAveragePrice_CacheDetails          
(          
ClientNo,          
Part,          
AveragePriceOfPartLast12Months,        
DLUP          
)  
  
SELECT  
@ClientID,   
@PartNo,   
CAST(CONVERT(DECIMAL(16,5),SUM(price)/SUM(Quantity)) AS NVARCHAR(100))+' '+@DefaultCurrencyCode+' (Invoiced)',  
GetDATE() FROM #tmp_AvgPrice          
END       
DROP TABLE #tmp_AvgPrice      
SET NOCOUNT OFF;              
END 
GO


