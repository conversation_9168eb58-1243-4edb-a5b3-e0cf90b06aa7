SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

IF OBJECT_ID('usp_IPOBOM_Source_OfferPH_BOMManager', 'P') IS NOT NULL
	DROP PROC dbo.usp_IPOBOM_Source_OfferPH_BOMManager
GO

CREATE PROCEDURE [dbo].[usp_IPOBOM_Source_OfferPH_BOMManager] 
--********************************************************************************************
--* Revision History:
--* 21.06.2024 (An Tran Tan): Add customer requirement for each item in results
--						 - To detect offer added or not using CustomerRequirementId instead of compare FullPart
--* 21.06.2024 (An Tran Tan): Filter Add flag by CustomerRequirementId because multiple CR can have same Part No within a BOM
--* 26.05.2024 (An Tran Tan): Update partsearch to show all available sourcing when not select BOM line yet.
--*						 - If not select a part, limit date range within 6 months base on current date.						
 --* 22.05.2024 (Hau Nguyen Hoang Trung): Thoroughly reviewed and corrected various errors and inconsistencies.
 --*                      - Corrected the CASE statement for OfferAddFlag to accurately reference the outer query's CustomerRequirementId.
 --*                      - Added a NULL check for CustomerRequirementId in the OfferAddFlag subquery.
 --*                      - Standardized column names (IsSourcingHub) and added the missing REQStatus property.
 --* 16.05.2024 (Hau Nguyen Hoang Trung): Fixed the error by using a temporary table instead of a CTE.
 --* 15.05.2024 (Hau Nguyen Hoang Trung): Added TotalRecords count to the result set.
 --* 14.05.2024 (Hau Nguyen Hoang Trung): Refactored the stored procedure for better readability and maintainability.
 --*                      - Reorganized the code structure and added comments for clarity.
 --*                      - Optimized the date range filtering logic.
 --*                      - Simplified the pagination logic using OFFSET and FETCH.
 --* 13.05.2024 (Hau Nguyen Hoang Trung): Added pagination functionality using the @curPage and @Rpp parameters.
 --*                      - Added @curPage and @Rpp parameters with default values of 1 and 10, respectively.
 --*                      - Added the date range filtering conditions from the WHERE clauses in the CTEs.
 --*                      - Added the 6-month date range filter and modified the stored procedure to return all records matching the @PartSearch parameter.
 --*                      - Modified the date range filtering in the IF @Index = 1 block to use a range.
 --*                      - Modified the SELECT statements to include pagination using OFFSET and FETCH NEXT clauses.
 --*                      - Removed the derived table and ORDER BY clause from the main SELECT statements.
 --* 09.03.2011 (RP): Added RECOMPILE option for query plan optimization.
 --* 25.05.2010 (RP): Removed UNIONS and implemented client processing within the code.
 --* 17.02.2010 (SK): Adjusted the display of external client data.
 --* 20.01.2010 (SK): Added @ClientId parameter and modified the predicate for client-specific behavior.
 --*                  - If @ClientId matches, display data as usual.
 --*                  - If @ClientId does not match, show ClientName as "customer" (no hyperlink) and hide prices.
 --* 18.01.2010 (RP): Used COALESCE function to handle null values for SupplierName and ManufacturerName.
 --* 05.06.2009 (RP): Modified the search condition to use LIKE operator for partial or wildcard searches.
 --* 01.06.2009 (SK): Added ORDER BY clause for sorting the result set.
 --* 16.10.2012 (Vinay) [001]: Displayed supplier type in the stock grid.
 --********************************************************************************************
    @ClientId INT,
    @PartSearch VARCHAR(MAX) = null,
    @Index INT = 1,
    @StartDate DATETIME = NULL,
    @FinishDate DATETIME = NULL,
    @OrderBy INT = 1,
    @SortDir INT = 1,
    @curPage INT = 1,
    @Rpp INT = 10,
    @BOMManagerNo INT = 1,
	@CustomerRequirementId INT = NULL
WITH RECOMPILE AS 
BEGIN 
DECLARE @Month INT = 6;
DECLARE @FROMDATE DATETIME,
@ENDDATE DATETIME,
@OutPutDate DATETIME;

DECLARE @FROMDATEVW DATETIME,
@ENDDATEVW DATETIME;

DECLARE @TotalRecords INT;

SET @PartSearch = ISNULL(@PartSearch, '');

IF @Index = 1 BEGIN -- Fetch the overall maximum OfferStatusChangeDate or OriginalEntryDate for the given @PartSearch
DECLARE @MaxDate DATETIME;

SELECT
    @MaxDate = MAX(ISNULL(OfferStatusChangeDate, OriginalEntryDate))
FROM
    (
        SELECT
            OfferStatusChangeDate,
            OriginalEntryDate
        FROM
            tbSourcingResult
        WHERE
            SourcingTable IN ('PQ', 'EXPH', 'OFPH')
            AND FullPart LIKE @PartSearch
        UNION
        SELECT
            OfferStatusChangeDate,
            OriginalEntryDate
        FROM
            [BorisGlobalTraderImports].dbo.tbOffer
            JOIN tbClient ON tbOffer.ClientNo = tbClient.ClientId
        WHERE
            FullPart LIKE @PartSearch
    ) AS AllDates;

-- Set the date range based on the overall maximum date and @Month
SET
    @FROMDATE = dbo.ufn_get_date_from_datetime(DATEADD(MONTH, - @Month, @MaxDate));

SET
    @ENDDATE = dbo.ufn_get_date_from_datetime(@MaxDate);

SET
    @FROMDATEVW = @FROMDATE;

SET
    @ENDDATEVW = @ENDDATE;

END
ELSE BEGIN -- Set the date range based on the provided @StartDate and @FinishDate
SET
    @FROMDATE = dbo.ufn_get_date_from_datetime(@StartDate);

SET
    @ENDDATE = dbo.ufn_get_date_from_datetime(@FinishDate);

SET
    @FROMDATEVW = dbo.ufn_get_date_from_datetime(@StartDate);

SET
    @ENDDATEVW = dbo.ufn_get_date_from_datetime(@FinishDate);

END;

-- Set the @OutPutDate based on @FinishDate and @Month
SET
    @OutPutDate = DATEADD(MONTH, - @Month, @FinishDate);

-- Set @Index to 3 if @FinishDate is NULL
IF @FinishDate IS NULL BEGIN
SET
    @Index = 3;

END;

-- Create a temporary table to store the offer data
CREATE TABLE #TempOffers (
OfferId INT,
FullPart NVARCHAR(MAX),
Part NVARCHAR(MAX),
ManufacturerNo INT,
DateCode NVARCHAR(MAX),
ProductNo INT,
PackageNo INT,
Quantity INT,
Price DECIMAL(18, 2),
OriginalEntryDate DATETIME,
Salesman INT,
SupplierNo INT,
CurrencyNo INT,
ROHS BIT,
UpdatedBy INT,
DLUP DATETIME,
OfferStatusNo INT,
OfferStatusChangeDate DATETIME,
OfferStatusChangeLoginNo INT,
SupplierName NVARCHAR(100),
Notes NVARCHAR(MAX),
ClientNo INT,
SPQ NVARCHAR(MAX),
LeadTime NVARCHAR(MAX),
ROHSStatus NVARCHAR(MAX),
FactorySealed NVARCHAR(MAX),
MSL NVARCHAR(MAX),
IPOBOMNo INT,
SupplierTotalQSA NVARCHAR(MAX),
SupplierLTB NVARCHAR(MAX),
SupplierMOQ NVARCHAR(MAX),
RefId INT,
ActionType INT,
RefIdHK INT,
NewRecord BIT,
IsBomManager BIT,
SupplierWarranty NVARCHAR(MAX),
CountryOfOriginNo INT,
SellPrice DECIMAL(18, 2),
SellPriceLessReason NVARCHAR(MAX),
ShippingCost DECIMAL(18, 2),
RegionNo INT,
DeliveryDate DATETIME,
TestingRecommended BIT,
IsSourcingHub BIT,
CustomerRequirementId INT,
REQStatus INT
);

IF (@PartSearch = '') 
BEGIN -- Create a temporary table to store the customer requirements
CREATE TABLE #CustomerRequirements (
FullPart NVARCHAR(MAX),
CustomerRequirementId INT,
REQStatus INT
);

-- Insert customer requirements for the given @BOMManagerNo
INSERT INTO
    #CustomerRequirements (FullPart, CustomerRequirementId, REQStatus)
SELECT
    cus.FullPart,
    cus.CustomerRequirementId,
    cus.REQStatus
FROM
    dbo.tbCustomerRequirement cus
WHERE
    cus.BOMManagerNo = @BOMManagerNo;

-- Insert offer data from [BorisGlobalTraderImports].dbo.tbOffer into the temporary table
INSERT INTO
    #TempOffers (
    OfferId,
    FullPart,
    Part,
    ManufacturerNo,
    DateCode,
    ProductNo,
    PackageNo,
    Quantity,
    Price,
    OriginalEntryDate,
    Salesman,
    SupplierNo,
    CurrencyNo,
    ROHS,
    UpdatedBy,
    DLUP,
    OfferStatusNo,
    OfferStatusChangeDate,
    OfferStatusChangeLoginNo,
    SupplierName,
    Notes,
    ClientNo,
    SPQ,
    LeadTime,
    ROHSStatus,
    FactorySealed,
    MSL,
    IPOBOMNo,
    SupplierTotalQSA,
    SupplierLTB,
    SupplierMOQ,
    RefId,
    ActionType,
    RefIdHK,
    NewRecord,
    IsBomManager,
    SupplierWarranty,
    CountryOfOriginNo,
    SellPrice,
    SellPriceLessReason,
    ShippingCost,
    RegionNo,
    DeliveryDate,
    TestingRecommended,
    CustomerRequirementId,
    IsSourcingHub,
    REQStatus
)
SELECT
    o.OfferId,
    o.FullPart COLLATE DATABASE_DEFAULT,
    o.Part COLLATE DATABASE_DEFAULT,
    o.ManufacturerNo,
    o.DateCode COLLATE DATABASE_DEFAULT,
    o.ProductNo,
    o.PackageNo,
    o.Quantity,
    o.Price,
    o.OriginalEntryDate,
    o.Salesman,
    o.SupplierNo,
    o.CurrencyNo,
    o.ROHS,
    o.UpdatedBy,
    o.DLUP,
    o.OfferStatusNo,
    ISNULL(o.OfferStatusChangeDate, o.OriginalEntryDate),
    o.OfferStatusChangeLoginNo,
    ISNULL(s.CompanyName, o.SupplierName),
    o.Notes COLLATE DATABASE_DEFAULT,
    o.ClientNo,
    o.SPQ,
    o.LeadTime,
    o.ROHSStatus,
    o.FactorySealed,
    ml.MSLLevel,
    0,
    o.SupplierTotalQSA,
    o.SupplierLTB,
    o.SupplierMOQ,
    o.RefId,
    o.ActionType,
    o.RefIdHK,
    o.NewRecord,
    o.IsBomManager,
    o.SupplierWarranty,
    o.CountryOfOriginNo,
    o.SellPrice,
    o.SellPriceLessReason,
    o.ShippingCost,
    o.RegionNo,
    o.DeliveryDate,
    o.TestingRecommended,
    cr.CustomerRequirementId,
    0,
    -- IsSourcingHub set to 0 for tbOffer
    cr.REQStatus -- Fetched REQStatus from tbCustomerRequirement
FROM
    [BorisGlobalTraderImports].dbo.tbOffer o
    LEFT JOIN dbo.tbManufacturer m ON o.ManufacturerNo = m.ManufacturerId
    LEFT JOIN dbo.tbProduct p ON o.ProductNo = p.ProductId
    LEFT JOIN dbo.tbCurrency c ON o.CurrencyNo = c.CurrencyId
    LEFT JOIN dbo.tbCompany s ON o.SupplierNo = s.CompanyId
    LEFT JOIN dbo.tbLogin l ON o.Salesman = l.LoginId
    LEFT JOIN dbo.tbLogin l2 ON o.OfferStatusChangeLoginNo = l2.LoginId
    LEFT JOIN dbo.tbPackage g ON o.PackageNo = g.PackageId
    LEFT JOIN tbClient cl ON o.ClientNo = cl.ClientId
    LEFT JOIN dbo.tbCompanyType cotype ON s.TypeNo = cotype.CompanyTypeId
    LEFT JOIN tbMSLLevel ml ON o.MSLLevelNo = ml.MSLLevelId
    LEFT JOIN #CustomerRequirements cr ON o.FullPart = cr.FullPart
WHERE
    (
        @Index = 3
        OR (
            dbo.ufn_get_date_from_datetime(
                ISNULL(o.OfferStatusChangeDate, o.OriginalEntryDate)
            ) BETWEEN @StartDate AND @FinishDate
        )
    )
    AND EXISTS (
        SELECT
            1
        FROM
            #CustomerRequirements WHERE FullPart = o.FullPart);
            -- Insert offer data from [vwSourcingData] into the temporary table
        INSERT INTO
            #TempOffers (
            OfferId,
            FullPart,
            Part,
            ManufacturerNo,
            DateCode,
            ProductNo,
            PackageNo,
            Quantity,
            Price,
            OriginalEntryDate,
            Salesman,
            SupplierNo,
            CurrencyNo,
            ROHS,
            UpdatedBy,
            DLUP,
            OfferStatusNo,
            OfferStatusChangeDate,
            OfferStatusChangeLoginNo,
            SupplierName,
            Notes,
            ClientNo,
            SPQ,
            LeadTime,
            ROHSStatus,
            FactorySealed,
            MSL,
            IPOBOMNo,
            SupplierTotalQSA,
            SupplierLTB,
            SupplierMOQ,
            IsSourcingHub,
            CustomerRequirementId,
            REQStatus
    )
SELECT
    v.OfferId,
    v.FullPart,
    v.Part,
    v.ManufacturerNo,
    v.DateCode,
    v.ProductNo,
    v.PackageNo,
    v.Quantity,
    v.Price,
    v.OriginalEntryDate,
    v.Salesman,
    v.SupplierNo,
    v.CurrencyNo,
    v.ROHS,
    v.UpdatedBy,
    v.DLUP,
    v.OfferStatusNo,
    v.OfferStatusChangeDate,
    v.OfferStatusChangeLoginNo,
    v.SupplierName,
    REPLACE(v.Notes, CHAR(10), ' '),
    v.ClientNo,
    '',
    '',
    '',
    '',
    '',
    0,
    '',
    '',
    '',
    1,
    -- IsSourcingHub set to 1 for vwSourcingData
    cr.CustomerRequirementId,
    cr.REQStatus -- Fetched REQStatus from tbCustomerRequirement
FROM
    [vwSourcingData] v
    LEFT JOIN #CustomerRequirements cr ON v.FullPart = cr.FullPart
WHERE
    (
        @Index = 3
        OR (
            dbo.ufn_get_date_from_datetime(
                ISNULL(v.OfferStatusChangeDate, v.OriginalEntryDate)
            ) BETWEEN @StartDate AND @FinishDate
        )
    )
    AND EXISTS (
        SELECT
            1
        FROM
            #CustomerRequirements WHERE FullPart = v.FullPart);
            -- Drop the temporary table
            DROP TABLE #CustomerRequirements;
    END
    ELSE BEGIN -- Insert offer data from [BorisGlobalTraderImports].dbo.tbOffer into the temporary table
INSERT INTO
    #TempOffers (
    OfferId,
    FullPart,
    Part,
    ManufacturerNo,
    DateCode,
    ProductNo,
    PackageNo,
    Quantity,
    Price,
    OriginalEntryDate,
    Salesman,
    SupplierNo,
    CurrencyNo,
    ROHS,
    UpdatedBy,
    DLUP,
    OfferStatusNo,
    OfferStatusChangeDate,
    OfferStatusChangeLoginNo,
    SupplierName,
    Notes,
    ClientNo,
    SPQ,
    LeadTime,
    ROHSStatus,
    FactorySealed,
    MSL,
    IPOBOMNo,
    SupplierTotalQSA,
    SupplierLTB,
    SupplierMOQ,
    RefId,
    ActionType,
    RefIdHK,
    NewRecord,
    IsBomManager,
    SupplierWarranty,
    CountryOfOriginNo,
    SellPrice,
    SellPriceLessReason,
    ShippingCost,
    RegionNo,
    DeliveryDate,
    TestingRecommended,
    CustomerRequirementId,
    IsSourcingHub,
    REQStatus
)
SELECT
    o.OfferId,
    o.FullPart COLLATE DATABASE_DEFAULT,
    o.Part COLLATE DATABASE_DEFAULT,
    o.ManufacturerNo,
    o.DateCode COLLATE DATABASE_DEFAULT,
    o.ProductNo,
    o.PackageNo,
    o.Quantity,
    o.Price,
    o.OriginalEntryDate,
    o.Salesman,
    o.SupplierNo,
    o.CurrencyNo,
    o.ROHS,
    o.UpdatedBy,
    o.DLUP,
    o.OfferStatusNo,
    ISNULL(o.OfferStatusChangeDate, o.OriginalEntryDate),
    o.OfferStatusChangeLoginNo,
    ISNULL(s.CompanyName, o.SupplierName),
    o.Notes COLLATE DATABASE_DEFAULT,
    o.ClientNo,
    o.SPQ,
    o.LeadTime,
    o.ROHSStatus,
    o.FactorySealed,
    ml.MSLLevel,
    0,
    o.SupplierTotalQSA,
    o.SupplierLTB,
    o.SupplierMOQ,
    o.RefId,
    o.ActionType,
    o.RefIdHK,
    o.NewRecord,
    o.IsBomManager,
    o.SupplierWarranty,
    o.CountryOfOriginNo,
    o.SellPrice,
    o.SellPriceLessReason,
    o.ShippingCost,
    o.RegionNo,
    o.DeliveryDate,
    o.TestingRecommended,
    cr.CustomerRequirementId,
    0,
    -- IsSourcingHub set to 0 for tbOffer
    cr.REQStatus -- Fetched REQStatus from tbCustomerRequirement
FROM
    [BorisGlobalTraderImports].dbo.tbOffer o
    LEFT JOIN dbo.tbManufacturer m ON o.ManufacturerNo = m.ManufacturerId
    LEFT JOIN dbo.tbProduct p ON o.ProductNo = p.ProductId
    LEFT JOIN dbo.tbCurrency c ON o.CurrencyNo = c.CurrencyId
    LEFT JOIN dbo.tbCompany s ON o.SupplierNo = s.CompanyId
    LEFT JOIN dbo.tbLogin l ON o.Salesman = l.LoginId
    LEFT JOIN dbo.tbLogin l2 ON o.OfferStatusChangeLoginNo = l2.LoginId
    LEFT JOIN dbo.tbPackage g ON o.PackageNo = g.PackageId
    LEFT JOIN tbClient cl ON o.ClientNo = cl.ClientId
    LEFT JOIN dbo.tbCompanyType cotype ON s.TypeNo = cotype.CompanyTypeId
    LEFT JOIN tbMSLLevel ml ON o.MSLLevelNo = ml.MSLLevelId
    LEFT JOIN tbCustomerRequirement cr ON  cr.CustomerRequirementId = @CustomerRequirementId
WHERE
    (
        @Index = 3
        OR (
            dbo.ufn_get_date_from_datetime(
                ISNULL(o.OfferStatusChangeDate, o.OriginalEntryDate)
            ) BETWEEN @FROMDATE
            AND @ENDDATE
        )
    )
    AND o.FullPart LIKE @PartSearch;

-- Insert offer data from [vwSourcingData] into the temporary table
INSERT INTO
    #TempOffers (
    OfferId,
    FullPart,
    Part,
    ManufacturerNo,
    DateCode,
    ProductNo,
    PackageNo,
    Quantity,
    Price,
    OriginalEntryDate,
    Salesman,
    SupplierNo,
    CurrencyNo,
    ROHS,
    UpdatedBy,
    DLUP,
    OfferStatusNo,
    OfferStatusChangeDate,
    OfferStatusChangeLoginNo,
    SupplierName,
    Notes,
    ClientNo,
    SPQ,
    LeadTime,
    ROHSStatus,
    FactorySealed,
    MSL,
    IPOBOMNo,
    SupplierTotalQSA,
    SupplierLTB,
    SupplierMOQ,
    IsSourcingHub,
    CustomerRequirementId,
    REQStatus
)
SELECT
    v.OfferId,
    v.FullPart,
    v.Part,
    v.ManufacturerNo,
    v.DateCode,
    v.ProductNo,
    v.PackageNo,
    v.Quantity,
    v.Price,
    v.OriginalEntryDate,
    v.Salesman,
    v.SupplierNo,
    v.CurrencyNo,
    v.ROHS,
    v.UpdatedBy,
    v.DLUP,
    v.OfferStatusNo,
    v.OfferStatusChangeDate,
    v.OfferStatusChangeLoginNo,
    v.SupplierName,
    REPLACE(v.Notes, CHAR(10), ' '),
    v.ClientNo,
    '',
    '',
    '',
    '',
    '',
    0,
    '',
    '',
    '',
    1,
    -- IsSourcingHub set to 1 for vwSourcingData
    cr.CustomerRequirementId,
    cr.REQStatus -- Fetched REQStatus from tbCustomerRequirement
FROM
    [vwSourcingData] v
    LEFT JOIN tbCustomerRequirement cr ON  cr.CustomerRequirementId = @CustomerRequirementId
WHERE
    (
        @Index = 3
        OR (
            dbo.ufn_get_date_from_datetime(
                ISNULL(v.OfferStatusChangeDate, v.OriginalEntryDate)
            ) BETWEEN @FROMDATEVW
            AND @ENDDATEVW
        )
    )
    AND v.FullPart LIKE @PartSearch;

END;

-- Calculate the total count of offers
SELECT
    @TotalRecords = COUNT(*)
FROM
    #TempOffers;
    -- Retrieve the paginated result set with the total count and missing properties
SELECT
    PaginatedResult.OfferId,
    PaginatedResult.FullPart,
    PaginatedResult.Part,
    PaginatedResult.ManufacturerNo,
    PaginatedResult.DateCode,
    PaginatedResult.ProductNo,
    PaginatedResult.PackageNo,
    PaginatedResult.Quantity,
    PaginatedResult.Price,
    PaginatedResult.OriginalEntryDate,
    PaginatedResult.Salesman,
    PaginatedResult.SupplierNo,
    PaginatedResult.CurrencyNo,
    PaginatedResult.ROHS,
    PaginatedResult.UpdatedBy,
    PaginatedResult.DLUP,
    PaginatedResult.OfferStatusNo,
    PaginatedResult.OfferStatusChangeDate,
    PaginatedResult.OfferStatusChangeLoginNo,
    PaginatedResult.SupplierName,
    PaginatedResult.Notes,
    PaginatedResult.ClientNo,
    PaginatedResult.SPQ,
    PaginatedResult.LeadTime,
    PaginatedResult.ROHSStatus,
    PaginatedResult.FactorySealed,
    PaginatedResult.MSL,
    PaginatedResult.IPOBOMNo,
    PaginatedResult.SupplierTotalQSA,
    PaginatedResult.SupplierLTB,
    PaginatedResult.SupplierMOQ,
    PaginatedResult.RefId,
    PaginatedResult.ActionType,
    PaginatedResult.RefIdHK,
    PaginatedResult.NewRecord,
    PaginatedResult.IsBomManager,
    PaginatedResult.SupplierWarranty,
    PaginatedResult.CountryOfOriginNo,
    PaginatedResult.SellPrice,
    PaginatedResult.SellPriceLessReason,
    PaginatedResult.ShippingCost,
    PaginatedResult.RegionNo,
    PaginatedResult.DeliveryDate,
    PaginatedResult.TestingRecommended,
    PaginatedResult.IsSourcingHub,
    PaginatedResult.CustomerRequirementId,
    PaginatedResult.REQStatus,
    m.ManufacturerName,
    p.ProductName,
    g.PackageName,
    m.ManufacturerCode,
    c.CurrencyCode,
    c.CurrencyDescription,
    s.Email AS SupplierEmail,
    l.EmployeeName AS SalesmanName,
    l2.EmployeeName AS OfferStatusChangeEmployeeName,
    cl.ClientId,
    cl.ClientName,
    isnull(cl.OwnDataVisibleToOthers, 0) AS ClientDataVisibleToOthers,
    isnull(cotype.Name, '') as SupplierType,
    cl.ClientCode,
    @TotalRecords AS TotalRecords,
    dbo.ufn_GetSupplierMessage(SupplierNo) AS SupplierMessage,
    CASE
        WHEN EXISTS (
            SELECT
                1
            FROM
                tbAutoSource tbs
            WHERE
                tbs.OfferId = PaginatedResult.OfferId
                AND tbs.FullPart = PaginatedResult.FullPart
                AND tbs.BOMManagerNo = @BOMManagerNo
                AND tbs.VendorCategory = 'Offers'
                AND ISNULL(tbs.isDeleted, 0) = 0
				AND tbs.CustomerRequirementId = PaginatedResult.CustomerRequirementId
        ) THEN 1
        ELSE 0
    END AS OfferAddFlag
FROM
    (
        SELECT
            *,
            ROW_NUMBER() OVER (
                ORDER BY
                    CASE
                        WHEN @OrderBy = 1
                        AND @SortDir = 2 THEN Quantity
                    END DESC,
                    CASE
                        WHEN @OrderBy = 1 THEN Quantity
                    END,
                    CASE
                        WHEN @OrderBy = 6
                        AND @SortDir = 2 THEN ISNULL(OfferStatusChangeDate, OriginalEntryDate)
                    END DESC,
                    CASE
                        WHEN @OrderBy = 6 THEN ISNULL(OfferStatusChangeDate, OriginalEntryDate)
                    END,
                    CASE
                        WHEN @OrderBy = 7
                        AND @SortDir = 2 THEN Price
                    END DESC,
                    CASE
                        WHEN @OrderBy = 7 THEN Price
                    END
            ) AS RowNum
        FROM
            #TempOffers
    ) AS PaginatedResult
    LEFT JOIN dbo.tbManufacturer m ON PaginatedResult.ManufacturerNo = m.ManufacturerId
    LEFT JOIN dbo.tbProduct p ON PaginatedResult.ProductNo = p.ProductId
    LEFT JOIN dbo.tbCurrency c ON PaginatedResult.CurrencyNo = c.CurrencyId
    LEFT JOIN dbo.tbCompany s ON PaginatedResult.SupplierNo = s.CompanyId
    LEFT JOIN dbo.tbLogin l ON PaginatedResult.Salesman = l.LoginId
    LEFT JOIN dbo.tbLogin l2 ON PaginatedResult.OfferStatusChangeLoginNo = l2.LoginId
    LEFT JOIN dbo.tbPackage g ON PaginatedResult.PackageNo = g.PackageId
    LEFT JOIN tbClient cl ON PaginatedResult.ClientNo = cl.ClientId
    LEFT JOIN dbo.tbCompanyType cotype ON s.TypeNo = cotype.CompanyTypeId
WHERE
    RowNum BETWEEN (@curPage - 1) * @Rpp + 1
    AND @curPage * @Rpp
ORDER BY
    RowNum;

-- Drop the temporary table
DROP TABLE #TempOffers;
-- Return the @OutPutDate as the result
SELECT
    @OutPutDate AS OutPutDate;

END;
GO