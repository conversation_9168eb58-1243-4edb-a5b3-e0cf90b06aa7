﻿GO

CREATE OR ALTER TRIGGER [dbo].[utrg_update_Company_IsSanctioned] ON [dbo].[tbCompany]
/* 
===========================================================================================
TASK      		UPDATED BY     	DATE         ACTION		DESCRIPTION
[US-212219]		Phuc Hoang		11-Sep-2024  UPDATE		Company Detail - Adding Sanctions Trigger
===========================================================================================
*/
AFTER UPDATE
AS

SET NOCOUNT ON;

DECLARE @OldIsSanctioned		int
DECLARE @NewIsSanctioned	int

-- Create a log if IsSanctioned changed:
IF	UPDATE(IsSanctioned)
	BEGIN
	SELECT  @OldIsSanctioned	= IsSanctioned
	FROM    Deleted

	SELECT  @NewIsSanctioned	= IsSanctioned
	FROM    Inserted

	IF ISNULL(@OldIsSanctioned, -1) <> ISNULL(@NewIsSanctioned, -1)
		BEGIN
		INSERT
		INTO	dbo.tbAudit
		(	
				TableName
		,		HeaderNo
		,		CompanyNo
		,		IsSanctioned
		,		Note
		,		UpdatedBy
		,       DLUP
		)
		SELECT	'Company'
		,		Deleted.CompanyId
		,		Deleted.CompanyId
		,		Deleted.IsSanctioned
		,       'IsSanctioned: ' + ISNULL(CAST(@OldIsSanctioned AS NVARCHAR(4)), 'NULL') + ' to ' + ISNULL(CAST(@NewIsSanctioned AS NVARCHAR(4)), 'NULL')
		,		Inserted.UpdatedBy 
		,       GETDATE()
		FROM	Deleted
		JOIN	Inserted
			on	Deleted.CompanyId = Inserted.CompanyId
		END
	END


GO

ALTER TABLE [dbo].[tbCompany] ENABLE TRIGGER [utrg_update_Company_IsSanctioned]
GO


