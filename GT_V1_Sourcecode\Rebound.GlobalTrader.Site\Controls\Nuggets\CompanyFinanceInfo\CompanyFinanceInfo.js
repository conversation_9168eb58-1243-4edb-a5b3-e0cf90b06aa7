Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Nuggets");Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyFinanceInfo=function(n){Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyFinanceInfo.initializeBase(this,[n]);this._intCompanyID=-1;this._warehouseNo=-1};Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyFinanceInfo.prototype={get_intCompanyID:function(){return this._intCompanyID},set_intCompanyID:function(n){this._intCompanyID!==n&&(this._intCompanyID=n)},get_ibtnLink:function(){return this._ibtnLink},set_ibtnLink:function(n){this._ibtnLink!==n&&(this._ibtnLink=n)},get_Status:function(){return this._Status},set_Status:function(n){this._Status!==n&&(this._Status=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyFinanceInfo.callBaseMethod(this,"initialize");this._strPathToData="controls/Nuggets/CompanyFinanceInfo";this._strDataObject="CompanyFinanceInfo";this.addRefreshEvent(Function.createDelegate(this,this.getData));this._ibtnLink&&($R_IBTN.addClick(this._ibtnLink,Function.createDelegate(this,this.showEditForm)),this._frmEdit=$find(this._aryFormIDs[0]),this._frmEdit.addCancel(Function.createDelegate(this,this.cancelEdit)),this._frmEdit.addSaveComplete(Function.createDelegate(this,this.saveEditComplete)),this._frmEdit.addSaveError(Function.createDelegate(this,this.saveEditError)));var n=this;$("#RefreshCompanyFinanceInfoCurrencyDropdown").click(function(){n.getCurrencyDropdownData()});this.getCurrencyDropdownData();this.getData();this.getCompanyInactive()},dispose:function(){this.isDisposed||(this._intCompanyID=null,this._ibtnLink=null,Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyFinanceInfo.callBaseMethod(this,"dispose"))},getData:function(){this.getData_Start();var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData(this._strPathToData);n.set_DataObject(this._strDataObject);n.set_DataAction("GetData");n.addParameter("id",this._intCompanyID);n.addDataOK(Function.createDelegate(this,this.getDataOK));n.addError(Function.createDelegate(this,this.getDataError));n.addTimeout(Function.createDelegate(this,this.getDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getDataOK:function(n){var r=n._result,t,u,f;if(r!=null&&r.SelectedCompanies.length>0){for(r=r.SelectedCompanies,console.log(r),$("#CompanyFinanceInfoCurrencyDropdown option").each(function(){this.value==r[0].CurrencyNo&&$("#CompanyFinanceInfoCurrencyDropdown").val(r[0].CurrencyNo)}),t="<table class='LinkedItems'>",t+="<tr>",i=0;i<r.length;i++){for(t+='<td class="LinkedItemTD">',t+='<table class="dataItems">',t+='<tr><td class="desc">Company Name<\/td><td class="item"><a href="/Con_CompanyDetail.aspx?cm='+r[i].CompanyID+'">'+$R_FN.setCleanTextValue(r[i].CompanyName)+"<\/a><\/td><\/tr>",t+='<tr><td class="desc">Sales Person<\/td><td class="item">'+r[i].Salesman+"<\/td><\/tr>",t+='<tr><td class="desc">Approved By<\/td><td class="item"><div class="imageCheckBoxDisabled"><img style="border-width: 0px;width:12px;height:12px;border:none !important;" class="',t+=r[i].IsApproved?'on" src="../../../App_Themes/Original/images/ImageCheckBox/on_dis.gif"':'off" src="../../../App_Themes/Original/images/ImageCheckBox/off_dis.gif"',t+='>&nbsp;&nbsp;<span style="font-size:9px;vertical-align:top;color:#808080;">'+r[i].ApprovedByAndDate+"<\/span><\/div><\/td><\/tr>",t+='<tr><td class="desc">Stop Status<\/td><td class="item">'+r[i].StopStatus+"<\/td><\/tr>",t+='<tr><td class="desc">Currency<\/td><td class="item">'+r[i].Currency+"<\/td><\/tr>",t+='<tr><td class="desc">Customer No<\/td><td class="item">'+r[i].CustomerNo+"<\/td><\/tr>",t+='<tr><td class="desc">Terms<\/td><td class="item">'+r[i].Terms+"<\/td><\/tr>",t+='<tr><td class="desc">Rating<\/td><td class="item"><span class="starsOuter starsOuterReadOnly">',u=1;u<6;u++)t+=u<=r[i].Rating?'<img class="StarSaved" style="height:12px;width:12px;border-width:0px;margin:1px;" src="../../../App_Themes/Original/images/StarRating/saved.png">':'<img class="StarEmpty" style="height:12px;width:12px;border-width:0px;margin:1px;" src="../../../App_Themes/Original/images/StarRating/empty.png">';t+="<\/span><\/td><\/tr>";t+='<tr><td class="desc">On Stop?<\/td><td class="item"><div class="imageCheckBoxDisabled"><img style="border-width: 0px;width:12px;height:12px;border:none !important;" class="';t+=r[i].OnStop?'on" src="../../../App_Themes/Original/images/ImageCheckBox/on_dis.gif"':'off" src="../../../App_Themes/Original/images/ImageCheckBox/off_dis.gif"';t+="><\/div><\/td><\/tr>";t+='<tr><td class="desc">Waive Shipping & Shipping Surcharge?<\/td><td class="item"><div class="imageCheckBoxDisabled"><img style="border-width: 0px;width:12px;height:12px;border:none !important;" class="';t+=r[i].IsShippingWaived?'on" src="../../../App_Themes/Original/images/ImageCheckBox/on_dis.gif"':'off" src="../../../App_Themes/Original/images/ImageCheckBox/off_dis.gif"';t+="><\/div><\/td><\/tr>";t+='<tr><td class="desc">Default Contact<\/td><td class="item">';t+=r[i].ContactNo!=undefined?'<a href = "Con_ContactDetail.aspx?con='+r[i].ContactNo+'" class="nubButton nubButtonAlignLeft" >'+r[i].ContactName+"<\/a><\/td ><\/tr > ":"<\/td ><\/tr >";t+='<tr><td class="desc">Notes to Invoice<\/td><td class="item">'+$R_FN.setCleanTextValue(r[i].NotesToInvoice)+"<\/td><\/tr>";t+='<td colspan="2"><div class="line"><\/div><\/td>';t+='<tr><td class="desc">Preferred Warehouse<\/td><td class="item">'+r[i].WarehouseName+"<\/td><\/tr>";t+='<tr><td class="desc">Max Exposure Credit Limit<\/td><td class="item">'+r[i].CreditLimit+"<\/td><\/tr>";t+='<tr><td class="desc">Credit Limit<\/td><td class="item">'+r[i].ActualCreditLimit+"<\/td><\/tr>";t+='<tr><td class="desc">Year To Date<\/td><td class="item">'+r[i].ThisYearValue+"<\/td><\/tr>";t+='<tr><td class="desc">Last Year<\/td><td class="item">'+r[i].LastYearValue+"<\/td><\/tr>";t+='<tr><td class="desc">Balance<\/td><td class="item">'+r[i].Balance+"<\/td><\/tr>";t+='<tr><td class="desc">Not Overdue<\/td><td class="item">'+r[i].Current+"<\/td><\/tr>";t+='<tr><td class="desc">01- 29 Days<\/td><td class="item">'+r[i].Days1+"<\/td><\/tr>";t+='<tr><td class="desc">30 - 59 days<\/td><td class="item">'+r[i].Days30+"<\/td><\/tr>";t+='<tr><td class="desc">60 - 89 days<\/td><td class="item">'+r[i].Days60+"<\/td><\/tr>";t+='<tr><td class="desc">90 - 119 days<\/td><td class="item">'+r[i].Days90+"<\/td><\/tr>";t+='<tr><td class="desc">120+ days<\/td><td class="item">'+r[i].Days120+"<\/td><\/tr>";t+='<tr><td class="desc">Balance with all Posted Lines<\/td><td class="item">'+r[i].BalanceWithOpenSalesOrders+"<\/td><\/tr>";t+='<tr><td class="desc">Invoice Not Exported<\/td><td class="item">'+r[i].InvoiceNotExport+"<\/td><\/tr>";t+='<tr><td class="desc">Year to date [spend | profit (%)]:<\/td><td class="item">'+r[i].YearToDate+"<\/td><\/tr>";t+='<tr><td class="desc">Last year [spend | profit (%)]:<\/td><td class="item">'+r[i].LastYear+"<\/td><\/tr>";t+='<td colspan="2"><div class="line"><\/div><\/td>';t+='<tr><td class="desc">Insurance File No<\/td><td class="item">'+r[i].InsuranceFileNo+"<\/td><\/tr>";t+='<tr><td class="desc">Insured Amount<\/td><td class="item">'+r[i].InsuredAmount+"<\/td><\/tr>";t+="<\/table>";t+="<\/td>"}t+="<\/tr>";t+="<\/table>";console.log(t);$("#LinkedCompanyInfo").html(t)}this.getDataOK_End();f=$("#CompanyFinanceInfoCurrencyDropdown option:selected").text();this.getLinkedAccountsCombinedInfo($("#CompanyFinanceInfoCurrencyDropdown").val(),f.substr(0,f.indexOf("-")-1))},getDataError:function(n){this.showError(!0,n.get_ErrorMessage())},getCompanyInactive:function(){var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData(this._strPathToData);n.set_DataObject(this._strDataObject);n.set_DataAction("GetCompanyDetailInactive");n.addParameter("id",this._intCompanyID);n.addDataOK(Function.createDelegate(this,this.getCompanyInactiveOK));n.addError(Function.createDelegate(this,this.getCompanyInactiveError));n.addTimeout(Function.createDelegate(this,this.getCompanyInactiveError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getCompanyInactiveOK:function(n){var r=n._result,t,i;this._ibtnLink&&$R_IBTN.enableButton(this._ibtnLink,!r.Inactive);t=document.getElementById("CompanyFinanceInfoCurrencyDropdown");i=document.getElementById("RefreshCompanyFinanceInfoCurrencyDropdown");r.Inactive?(t.style.pointerEvents="none",t.style.opacity=.8,i.style.display="none"):(t.style.pointerEvents="auto",t.style.opacity=1,i.style.display="")},getCompanyInactiveError:function(n){this.showError(!0,n.get_ErrorMessage())},showEditForm:function(){this._frmEdit._globalLoginClientNo=this._globalLoginClientNo;$R_FN.showElement(this._ibtnLink,!1);this.showForm(this._frmEdit,!0);$R_FN.showElement(this._ibtnLink,!0)},cancelEdit:function(){this.showContent(!0);this.showForm(this._frmEdit,!1)},saveEditComplete:function(){this.showForm(this._frmEdit,!1);this.getData();this.showSavedOK(!0,$R_RES.ChangesSavedSuccessfully);this.onSaveEditComplete()},saveEditError:function(){this.showForm(this._frmEdit,!1);this.showError(!0,this._frmEdit._strErrorMessage)},getCurrencyDropdownData:function(){var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData(this._strPathToData);n.set_DataObject(this._strDataObject);n.set_DataAction("CurrencyDropdownData");n.addDataOK(Function.createDelegate(this,this.getCurrencyDropdownDataOK));n.addError(Function.createDelegate(this,this.getCurrencyDropdownDataError));n.addTimeout(Function.createDelegate(this,this.getCurrencyDropdownDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getCurrencyDropdownDataOK:function(n){var i=n._result.Currencies,r="",f=$("#CompanyFinanceInfoCurrencyDropdown").val(),t,u;for($("#CompanyFinanceInfoCurrencyDropdown").html(""),t=0;t<i.length;t++)r+="<option value='"+i[t].ID+"'>"+i[t].Name+"<\/option>";$("#CompanyFinanceInfoCurrencyDropdown").html(r);$("#CompanyFinanceInfoCurrencyDropdown").val(f);u=this;$("#CompanyFinanceInfoCurrencyDropdown").change(function(){var n=$("#CompanyFinanceInfoCurrencyDropdown option:selected").text();u.getLinkedAccountsCombinedInfo($("#CompanyFinanceInfoCurrencyDropdown").val(),n.substr(0,n.indexOf("-")-1))})},getCurrencyDropdownDataError:function(n){this.showError(!0,n.get_ErrorMessage())},getLinkedAccountsCombinedInfo:function(n,t){this.getData_Start();var i=new Rebound.GlobalTrader.Site.Data;i.set_PathToData(this._strPathToData);i.set_DataObject(this._strDataObject);i.set_DataAction("GetLinkedAccountsCombinedInfo");i.addParameter("SelectedCurrencyNo",n);i.addParameter("id",this._intCompanyID);i.addParameter("CurrencyCode",t);i.addDataOK(Function.createDelegate(this,this.getLinkedAccountsCombinedInfoOK));i.addError(Function.createDelegate(this,this.getLinkedAccountsCombinedInfoError));i.addTimeout(Function.createDelegate(this,this.getLinkedAccountsCombinedInfoError));$R_DQ.addToQueue(i);$R_DQ.processQueue();i=null},getLinkedAccountsCombinedInfoOK:function(n){var i=n._result,t="<table class='LinkedItemsCreditLimit dataItems' style='width:40%;'>";t+="<tr>";t+="<td class='desc'>Exchange Rate<\/td><td class='item'>"+i.FromConversionRate+" = "+i.ToConversionRate+"<\/td>";t+="<\/tr>";t+="<tr>";t+="<td class='desc'>Total Credit Limit<\/td><td class='item'>"+i.ActualCreditLimit+"<\/td>";t+="<\/tr>";t+="<tr>";t+="<td class='desc'>Total Max Exposure Credit Limit<\/td><td class='item'>"+i.CreditLimit+"<\/td>";t+="<\/tr>";t+="<tr>";t+="<td class='desc'>Total Insured Amount<\/td><td class='item'>"+i.InsuredAmount+"<\/td>";t+="<\/tr>";t+="<\/table>";$("#LinkedCompanyCreditInfo").html(t);this.getDataOK_End()},getLinkedAccountsCombinedInfoError:function(n){this.showError(!0,n.get_ErrorMessage())}};Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyFinanceInfo.registerClass("Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyFinanceInfo",Rebound.GlobalTrader.Site.Controls.Nuggets.Base);