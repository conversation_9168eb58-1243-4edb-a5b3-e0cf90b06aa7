using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;
using Rebound.GlobalTrader.Site.Enumerations;

namespace Rebound.GlobalTrader.Site.Controls.DataListNuggets.Data {

    public class CommunicationLog : Base {


        public override void ProcessRequest(HttpContext context) {
            base.ProcessRequest(context);
            switch (Action) {
                case "AddLogItem": AddLogItem(); break;
                case "UpdateLogItem": UpdateLogItem(); break;
                case "GetCompanyDetailInactive": GetCompanyDetailInactive(); break;
            }
        }

        protected override void GetData()
        {
            //check if we are in "Contact" mode
            bool blnForContact = GetFormValue_Boolean("ForContact");

            //get data	
            List<BLL.CommunicationLog> lst = BLL.CommunicationLog.DataListNugget(
                SessionManager.ClientID
                , GetFormValue_Int("CompanyNo")
                , GetFormValue_NullableInt("SortIndex")
                , GetFormValue_NullableInt("SortDir", SortColumnDirection.ASC)
                , GetFormValue_NullableInt("PageIndex", 0)
                , GetFormValue_NullableInt("PageSize", 10)
                , GetFormValue_NullableInt("LoginNo")
                , Functions.UrlDecodeRetainingPercentSigns(GetFormValue_String("Details"))
                , GetFormValue_NullableInt("ContactNo")
                , GetFormValue_NullableInt("CommunicationLogTypeNo")
                , GetFormValue_String("CallType")
                , GetFormValue_NullableDateTime("LogDateFrom", null)
                , GetFormValue_NullableDateTime("LogDateTo", null)
            );
            JsonObject jsn = new JsonObject();
            JsonObject jsnItems = new JsonObject(true);
            foreach (BLL.CommunicationLog cl in lst)
            {
                JsonObject jsnItem = new JsonObject();
                jsnItem.AddVariable("ID", cl.CommunicationLogId);
                jsnItem.AddVariable("Date", Functions.FormatDate(cl.LogDate, false, true));
                if (cl.SystemDocumentNo == null)
                {
                    jsnItem.AddVariable("Type", cl.CommunicationLogTypeDescription);
                }
                else
                {
                    jsnItem.AddVariable("Type", Functions.GetGlobalResource("Misc", string.Format("New{0}", ((SystemDocument.List)cl.SystemDocumentNo).ToString())));
                    cl.Frozen = true;
                }
                jsnItem.AddVariable("TypeID", cl.CommunicationLogTypeNo);
                jsnItem.AddVariable("SysDocNo", cl.SystemDocumentNo);
                jsnItem.AddVariable("SysDocNumber", cl.DocumentNumber);
                jsnItem.AddVariable("KeyNo", cl.KeyNo);
                jsnItem.AddVariable("Contact", cl.ContactName);
                jsnItem.AddVariable("ContactID", cl.ContactNo);
                jsnItem.AddVariable("Details", Functions.ReplaceLineBreaks(cl.Notes));
                jsnItem.AddVariable("Frozen", cl.Frozen);
                jsnItem.AddVariable("EnteredBy", cl.EnteredBy);
                jsnItems.AddVariable(jsnItem);
                jsnItem.Dispose(); jsnItem = null;
            }
            jsn.AddVariable("TotalRecords", (lst.Count > 0) ? lst[0].RowCnt : 0);
            jsn.AddVariable("Items", jsnItems);
            OutputResult(jsn);
            jsnItems.Dispose(); jsnItems = null;
            jsn.Dispose(); jsn = null;
            lst = null;
            base.GetData();
        }

        protected override void AddFilterStates() {
            base.AddFilterStates();
        }

        private void UpdateLogItem() {
            try {
                BLL.CommunicationLog log = BLL.CommunicationLog.Get(ID);
                log.CommunicationLogTypeNo = GetFormValue_Int("TypeNo");
                log.Notes = GetFormValue_String("Notes");
                log.ContactNo = GetFormValue_NullableInt("ContactNo", 0);
                log.CompanyNo = GetFormValue_NullableInt("CMNo", 0);
                log.UpdatedBy = LoginID;
                JsonObject jsn = new JsonObject();
                jsn.AddVariable("Result", log.Update());
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
                log = null;
            } catch (Exception e) {
                WriteError(e);
            }
        }

        /// <summary>
        /// Add a log item
        /// </summary>
        private void AddLogItem() {
            try {
                BLL.CommunicationLog cl = new BLL.CommunicationLog();
                cl.CommunicationLogTypeNo = GetFormValue_Int("TypeNo");
                cl.Notes = GetFormValue_String("Notes");
                cl.ContactNo = GetFormValue_NullableInt("ContactNo");
                cl.CompanyNo = GetFormValue_NullableInt("CMNo");
                cl.LogDate = DateTime.Now;
                cl.Frozen = false;
                cl.UpdatedBy = LoginID;
                cl.CommunicationLogId = cl.Insert();
                JsonObject jsn = new JsonObject();
                jsn.AddVariable("Result", (cl.CommunicationLogId > 0));
                jsn.AddVariable("NewId", cl.CommunicationLogId);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            } catch (Exception e) {
                WriteError(e);
            }
        }

        private void GetCompanyDetailInactive()
        {
            Company cm = Company.GetCompanyDetailInactive(ID);
            if (cm == null)
            {
                WriteErrorDataNotFound();
            }
            else
            {
                JsonObject jsn = null;
                if (cm != null)
                {
                    jsn = new JsonObject();
                    jsn.AddVariable("CompanyId", cm.CompanyId);
                    jsn.AddVariable("Inactive", cm.Inactive);
                }
                cm = null;
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            cm = null;
        }
    }
}
