Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DataListNuggets");Rebound.GlobalTrader.Site.Controls.DataListNuggets.SalesOrdersShip=function(n){Rebound.GlobalTrader.Site.Controls.DataListNuggets.SalesOrdersShip.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.DataListNuggets.SalesOrdersShip.prototype={get_blnShowAllOrders:function(){return this._blnShowAllOrders},set_blnShowAllOrders:function(n){this._blnShowAllOrders!==n&&(this._blnShowAllOrders=n)},get_IsGlobalLogin:function(){return this._IsGlobalLogin},set_IsGlobalLogin:function(n){this._IsGlobalLogin!==n&&(this._IsGlobalLogin=n)},get_ibtnExportCSV:function(){return this._ibtnExportCSV},set_ibtnExportCSV:function(n){this._ibtnExportCSV!==n&&(this._ibtnExportCSV=n)},get_sortIndex:function(){return this._sortIndex},set_sortIndex:function(n){this._sortIndex!==n&&(this._sortIndex=n)},get_sortDir:function(){return this._sortDir},set_sortDir:function(n){this._sortDir!==n&&(this._sortDir=n)},get_pageIndex:function(){return this._pageIndex},set_pageIndex:function(n){this._pageIndex!==n&&(this._pageIndex=n)},get_pageSize:function(){return this._pageSize},set_pageSize:function(n){this._pageSize!==n&&(this._pageSize=n)},initialize:function(){this.addInitCompleteEvent(Function.createDelegate(this,this.initAfterBaseIsReady));this.addSetupDataCallEvent(Function.createDelegate(this,this.setupDataCall));this.addGetDataOKEvent(Function.createDelegate(this,this.getDataOK));this._strPathToData="controls/DataListNuggets/SalesOrdersShip";this._strDataObject="SalesOrdersShip";Rebound.GlobalTrader.Site.Controls.DataListNuggets.SalesOrdersShip.callBaseMethod(this,"initialize");$("#ctl00_cphMain_ctlSalesOrders_ctlDB_ctl16_ctlFilter_ctlShipVia_ddl_ddl").css("width","80%");this._ibtnExportCSV&&$R_IBTN.addClick(this._ibtnExportCSV,Function.createDelegate(this,this.exportCSV))},initAfterBaseIsReady:function(){this.addPageTabChangedEvent(Function.createDelegate(this,this.pageTabChanged));this.updateFilterVisibility();this.getData()},dispose:function(){this.isDisposed||(this._blnShowAllOrders=null,this._IsGlobalLogin=null,this._ibtnExportCSV&&$R_IBTN.clearHandlers(this._ibtnExportCSV),this._ibtnExportCSV=null,Rebound.GlobalTrader.Site.Controls.DataListNuggets.SalesOrdersShip.callBaseMethod(this,"dispose"))},pageTabChanged:function(){this._table._intCurrentPage=1;this._blnShowAllOrders=this._intCurrentTab==1;this.updateFilterVisibility();this.getData()},setupDataCall:function(){var n="GetData";this._blnShowAllOrders&&(n+="_All");this._objData.set_DataAction(n);this._objData.addParameter("IsGlobalLogin",this._IsGlobalLogin)},getDataOK:function(){for(var n,i,t=0,r=this._objResult.Results.length;t<r;t++)n=this._objResult.Results[t],i=this._IsGlobalLogin?[$RGT_nubButton_ShipSalesOrder(n.ID,n.No),$R_FN.writeDoubleCellValue($R_FN.writePartNo(n.Part,n.ROHS),$R_FN.setCleanTextValue(n.CustomerPart)),n.Allocated,$R_FN.writeDoubleCellValue($RGT_nubButton_Company(n.CMNo,n.CM),$R_FN.setCleanTextValue(n.CustPONO)),$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(n.DateOrdered),$R_FN.setCleanTextValue(n.DatePromised)),$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(n.RequiredDate),n.ShipASAP),n.Status,n.OGELRequired?$R_FN.setCleanTextValue(n.OGELApprovalStatus):"",n.SOSerialNumber,$R_FN.setCleanTextValue(n.ClientName)]:[$RGT_nubButton_ShipSalesOrder(n.ID,n.No),$R_FN.writeDoubleCellValue($R_FN.writePartNo(n.Part,n.ROHS),$R_FN.setCleanTextValue(n.CustomerPart)),n.Allocated,$R_FN.writeDoubleCellValue($RGT_nubButton_Company(n.CMNo,n.CM),$R_FN.setCleanTextValue(n.CustPONO)),$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(n.DateOrdered),$R_FN.setCleanTextValue(n.DatePromised)),$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(n.RequiredDate),n.ShipASAP),n.Status,n.OGELRequired?$R_FN.setCleanTextValue(n.OGELApprovalStatus):"Not Required",n.SOSerialNumber],this._sortIndex=n.ExpSortIndex,this._sortDir=n.ExpSortDir,this._pageIndex=n.ExpPageIndex,this._pageSize=n.ExpPageSize,this._table.addRow(i,n.ID,!1),i=null,n=null},updateFilterVisibility:function(){this.getFilterField("ctlRecentOnly").show(this._blnShowAllOrders);this.getFilterField("ctlIncludeClosed").show(this._blnShowAllOrders);this.getFilterField("ctlClientName").show(this._IsGlobalLogin)},exportCSV:function(){this.getData_Start();var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/DataListNuggets/SalesOrdersShip");n.set_DataObject("SalesOrdersShip");n.set_DataAction("ExportToCSV");this._objData.addParameter("IsGlobalLogin",this._IsGlobalLogin);this._blnShowAllOrders?n.addParameter("Type","All"):n.addParameter("Type","Ready");n.addParameter("EXPSortIndex",this._sortIndex);n.addParameter("ExpSortDir",this._sortDir);n.addParameter("ExpPageIndex",this._pageIndex);n.addParameter("ExpPageSize",this._pageSize);n.addParameter("ExpPart",this.getFilterFieldValue("ctlPart"));n.addParameter("ExpContact",null);n.addParameter("ExpCMName",this.getFilterFieldValue("ctlCompanyName"));n.addParameter("ExpSalesman",this.getFilterFieldValue("ctlSalesman"));n.addParameter("ExpCustPO",null);n.addParameter("ExpSONoLo",this.getFilterFieldValue("ctlSONo"));n.addParameter("ExpSONoHi",this.getFilterFieldValue("ctlSONo"));n.addParameter("ExpDateOrderedFrom",null);n.addParameter("ExpDateOrderedTo",null);n.addParameter("ExpDatePromisedFrom",this.getFilterFieldValue("ctlDatePromisedFrom"));n.addParameter("ExpDatePromisedTo",this.getFilterFieldValue("ctlDatePromisedTo"));n.addParameter("ExpClient",this.getFilterFieldValue("ctlClientName"));n.addParameter("ExpWarehouse",this.getFilterFieldValue("ctlWarehouse"));n.addParameter("ExpBuyShipMethod",this.getFilterFieldValue("ctlShipVia"));n.addParameter("ExpShipASAP",this.getFilterFieldValue("ctlIsShipASAP"));n.addParameter("ExpLocation",this.getFilterFieldValue("ctlLocation"));n.addParameter("ExpEnhancedInspection",this.getFilterFieldValue("ctlEnhancedInspection"));n.addParameter("ExpCheckedBy",this.getFilterFieldValue("ctlCheckedBy"));n.addParameter("ExpReadyStatus",this.getFilterFieldValue("ctlReadyStatus"));n.addParameter("ExpStatus",this.getFilterFieldValue("ctlStatus"));n.addParameter("ExpIncludeClosed",this.getFilterFieldValue("ctlIncludeClosed"));n.addParameter("ExpRecentOnly",this.getFilterFieldValue("ctlRecentOnly"));n.addDataOK(Function.createDelegate(this,this.exportCSV_OK));n.addError(Function.createDelegate(this,this.exportCSV_Error));n.addTimeout(Function.createDelegate(this,this.exportCSV_Error));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},exportCSV_OK:function(n){var i=n._result,t;i.Filename&&(t=new Date,location.href=String.format("{0}?t={1}",i.Filename,t.getTime()),t=null);this.getDataOK_End()},exportCSV_Error:function(n){this.showError(!0,n.get_ErrorMessage())}};Rebound.GlobalTrader.Site.Controls.DataListNuggets.SalesOrdersShip.registerClass("Rebound.GlobalTrader.Site.Controls.DataListNuggets.SalesOrdersShip",Rebound.GlobalTrader.Site.Controls.DataListNuggets.Base,Sys.IDisposable);