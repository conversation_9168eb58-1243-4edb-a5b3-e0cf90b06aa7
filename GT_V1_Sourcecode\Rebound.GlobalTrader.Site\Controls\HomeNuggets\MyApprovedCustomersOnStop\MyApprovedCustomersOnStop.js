Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.HomeNuggets");Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyApprovedCustomersOnStop=function(n){Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyApprovedCustomersOnStop.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyApprovedCustomersOnStop.prototype={get_pnlItems:function(){return this._pnlItems},set_pnlItems:function(n){this._pnlItems!==n&&(this._pnlItems=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyApprovedCustomersOnStop.callBaseMethod(this,"initialize");this.addRefreshEvent(Function.createDelegate(this,this.getData))},dispose:function(){this.isDisposed||(this._pnlItems=null,Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyApprovedCustomersOnStop.callBaseMethod(this,"dispose"))},setupLoadingState:function(){$R_FN.showElement(this._pnlItems,!1);Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyApprovedCustomersOnStop.callBaseMethod(this,"setupLoadingState")},getData:function(){this.setupLoadingState();$R_FN.setInnerHTML(this._pnlItems,"");var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/HomeNuggets/MyApprovedCustomersOnStop");n.set_DataObject("MyApprovedCustomersOnStop");n.set_DataAction("GetData");n.addParameter("rowcount",this._intRowCount);n.addParameter("OtherLoginID",this._intLoginID_Other);n.addDataOK(Function.createDelegate(this,this.getDataComplete));n.addError(Function.createDelegate(this,this.homeNuggetDataError));n.addTimeout(Function.createDelegate(this,this.homeNuggetDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getDataComplete:function(n){var t=n._result,i;for(this.showNoneFoundOrContent(t.Count),i=0;i<t.Items.length;i++)this.addHTMLContentItem($RGT_nubButton_Company(t.Items[i].CompanyID,t.Items[i].CompanyName),this._pnlItems);$R_FN.showElement(this._pnlItems,t.Count>0);this.hideLoading()}};Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyApprovedCustomersOnStop.registerClass("Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyApprovedCustomersOnStop",Rebound.GlobalTrader.Site.Controls.HomeNuggets.Base,Sys.IDisposable);