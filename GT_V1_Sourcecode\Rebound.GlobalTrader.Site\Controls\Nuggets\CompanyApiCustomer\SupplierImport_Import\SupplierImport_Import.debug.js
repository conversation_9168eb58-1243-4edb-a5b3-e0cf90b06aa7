///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------

Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.SupplierImport_Import = function(element) {
    Rebound.GlobalTrader.Site.Controls.Forms.SupplierImport_Import.initializeBase(this, [element]);
    this._intCompanyID = 0;
};

Rebound.GlobalTrader.Site.Controls.Forms.SupplierImport_Import.prototype = {
    get_ibtnSend: function() { return this._ibtnSend; }, set_ibtnSend: function(value) { if (this._ibtnSend !== value) this._ibtnSend = value; },
    get_ibtnSend_Footer: function() { return this._ibtnSend_Footer; }, set_ibtnSend_Footer: function(v) { if (this._ibtnSend_Footer !== v) this._ibtnSend_Footer = v; },
 
    initialize: function () {
        debugger;
        Rebound.GlobalTrader.Site.Controls.Forms.SupplierImport_Import.callBaseMethod(this, "initialize");
        //this.addShown(Function.createDelegate(this, this.formShown));
        //this.addCancel(Function.createDelegate(this, this.cancelClicked));
        
    },

    formShown: function() {
        if (this._blnFirstTimeShown) {
            $R_IBTN.addClick(this._ibtnSend, Function.createDelegate(this, this.sendMail));
            $R_IBTN.addClick(this._ibtnSend_Footer, Function.createDelegate(this, this.sendMail));
            this.addCancel(Function.createDelegate(this, this.cancelClicked));
        }
              
    },

    dispose: function() {
        if (this.isDisposed) return;
        Rebound.GlobalTrader.Site.Controls.Forms.SupplierImport_Import.callBaseMethod(this, "dispose");
    },

    getMessageText: function() {
        Rebound.GlobalTrader.Site.WebServices.GetMailMessage_NotifyNPR(this._intNPRID, Function.createDelegate(this, this.getMessageTextComplete));
    },

    getMessageTextComplete: function(strMsg) {
        this._ctlMail.setValue_Body(strMsg);
        this._ctlMail.setValue_Subject(String.format($R_RES.NotifyNPR, this._strNPRNo));
        this._ctlMail.addNewLoginRecipient(this._intBuyerId, this._strBuyerName);
    },
    //[001] code start
    sendMail: function() {
        if (!this.validateForm()) return;
        this.showLoading(true);
        this.enableButton(false);
        Rebound.GlobalTrader.Site.WebServices.NotifyNPRMessage($R_FN.arrayToSingleString(this._ctlMail._aryRecipientLoginIDs), $R_FN.arrayToSingleString(this._ctlMail._aryRecipientGroupIDs), this._ctlMail.getValue_Subject(), this._ctlMail.getValue_Body(), "", this._intNPRID, this._intGoodsInLineId, $R_FN.arrayToSingleString(this._ctlMail._aryRecipientLoginNames,"/"), $R_FN.arrayToSingleString(this._ctlMail._aryRecipientGroupNames,"/"), Function.createDelegate(this, this.sendMailComplete));
    },
    //[001] code end
    validateForm: function() {
        var blnOK = this._ctlMail.validateFields();
        if (!blnOK) this.showError(true);
        return blnOK;
    },
    cancelClicked: function () {
        //$R_FN.navigateBack();
        alert('abc11');
        debugger;
        this.showForm(this.frmSupplierImport, false);
        //window.location.href = 'Default.aspx';
       
    },
    sendMailComplete: function() {
        this.showLoading(false);
        this.showSavedOK(true);
        location.href = $RGT_gotoURL_GoodsIn(this._intGoodsIn);
        this.enableButton(true);
    },
    impotExcelData: function (originalFilename, generatedFilename, clientId, iscolumnheaderchk, Client_type) {
        debugger;
        $('#divLoader').show();
        //alert(clientId + Client_type);
        var cmdclient = $("#ddlClient");
        if (cmdclient.val() == 0) {
            alert("Please select client!");
            $('#divLoader').hide();
            return false;
        }
        else {
            var obj = new Rebound.GlobalTrader.Site.Data();
            obj._intTimeoutMilliseconds = 200000;
            obj.set_PathToData("controls/Nuggets/CompanyApiCustomer");
            obj.set_DataObject("SupplierImport");
            obj.set_DataAction("ImportExcelData");
            obj.addParameter("originalFilename", originalFilename);
            obj.addParameter("generatedFilename", generatedFilename);
            if (Client_type == 1) obj.addParameter("ClientId", clientId);
            else  obj.addParameter("ClientId", clientId);
            obj.addParameter("ColumnHeader", iscolumnheaderchk);
            obj.addParameter("SelectedClientType", Client_type);
            obj.addDataOK(Function.createDelegate(this, this.importExcelDataOK));
            obj.addError(Function.createDelegate(this, this.importExcelDataError));
            obj.addTimeout(Function.createDelegate(this, this.importExcelDataError));
            $R_DQ.addToQueue(obj);
            $R_DQ.processQueue();
            obj = null;

            return true;
        }
        
    },
    importExcelDataOK: function (args) {
        flogId = args._result.FileLogId;
        $('#divLoader').hide();
        $("#btnDisplayCsvDataSupp").prop('disabled', false).css('opacity', 5.5);
        $("#excelipload").prop('disabled', true).css('opacity', 0.5);
        $('input:checkbox[id="chkFileCCH"]').prop('disabled', true);
        $('input:file').filter(function () {
            return this.files.length == 0
        }).prop('disabled', true);
        var IsLimitExceeded = args._result.IsLimitExceeded;
        if (IsLimitExceeded) {
            alert(args._result.LimitErrorMessage);
        }
        
    },
    importExcelDataError: function (args) {
        alert(args._errorMessage.split('<br/>')[0]);
        $('#divLoader').hide();
    },
    
    enableButton: function(bln) {
        $R_IBTN.enableButton(this._ibtnSend, bln);
        $R_IBTN.enableButton(this._ibtnSend_Footer, bln);
    }

};

Rebound.GlobalTrader.Site.Controls.Forms.SupplierImport_Import.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.SupplierImport_Import", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
