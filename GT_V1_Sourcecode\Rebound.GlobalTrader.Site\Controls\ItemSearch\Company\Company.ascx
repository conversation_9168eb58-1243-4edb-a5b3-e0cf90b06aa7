<%@ Control Language="C#" CodeBehind="Company.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.ItemSearch.Company" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_ItemSearch:DesignBase id="ctlDB" runat="server">
	<FieldsLeft>
		<ReboundUI_FilterDataItemRow:TextBox id="ctlCompanyName" runat="server" ResourceTitle="CompanyName" />
	</FieldsLeft>
</ReboundUI_ItemSearch:DesignBase>
