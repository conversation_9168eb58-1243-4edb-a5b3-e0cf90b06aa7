﻿using Rebound.GlobalTrader.DAL;
using Rebound.GlobalTrader.DAL.SQLClient;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Rebound.GlobalTrader.BLL
{
    //Anuj
    public partial class SendToMemberList : BizObject
    {
        #region Properties
        protected static DAL.SendToMemberListElement Settings
        {
            get { return Globals.Settings.SendToMemberList; }
        }

        public System.String LoginNo { get; set; }
        public System.String SecurityGroupNo { get; set; }
        #endregion

        #region Methods

        public static List<SendToMemberList> GetMemberList(System.Int32 SecurityGroupNo)
        {
            List<SendToMemberListDetails> lstDetails = new List<SendToMemberListDetails>();
            SQLSendToMemberListProvider SQLp = new SQLSendToMemberListProvider();
            try
            {
                lstDetails = SQLp.GetMembersLoginNoList(SecurityGroupNo);
                if (lstDetails == null)
                {
                    return new List<SendToMemberList>();
                }
                else
                {
                    List<SendToMemberList> lst = new List<SendToMemberList>();
                    foreach (SendToMemberListDetails objDetails in lstDetails)
                    {
                        SendToMemberList obj = new SendToMemberList();
                        obj.LoginNo = objDetails.LoginNo;
                        lst.Add(obj);
                        obj = null;
                    }
                    return lst;
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
            finally
            {
                lstDetails = null;
            }
        }

        public static List<SendToMemberList> GetMembersLoginNoListForpo(System.Int32 SecurityGroupNo)
        {
            List<SendToMemberListDetails> lstDetails = new List<SendToMemberListDetails>();
            SQLSendToMemberListProvider SQLp = new SQLSendToMemberListProvider();
            try
            {
                lstDetails = SQLp.GetMembersLoginNoListForpo(SecurityGroupNo);
                if (lstDetails == null)
                {
                    return new List<SendToMemberList>();
                }
                else
                {
                    List<SendToMemberList> lst = new List<SendToMemberList>();
                    foreach (SendToMemberListDetails objDetails in lstDetails)
                    {
                        SendToMemberList obj = new SendToMemberList();
                        obj.LoginNo = objDetails.LoginNo;
                        lst.Add(obj);
                        obj = null;
                    }
                    return lst;
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
            finally
            {
                lstDetails = null;
            }
        }

        public static List<SendToMemberList> GetMembersLoginNoListForOGELECCN(System.Int32 SecurityGroupNo)
        {
            List<SendToMemberListDetails> lstDetails = new List<SendToMemberListDetails>();
            SQLSendToMemberListProvider SQLp = new SQLSendToMemberListProvider();
            try
            {
                lstDetails = SQLp.GetMembersLoginNoListForOGELECCN(SecurityGroupNo);
                if (lstDetails == null)
                {
                    return new List<SendToMemberList>();
                }
                else
                {
                    List<SendToMemberList> lst = new List<SendToMemberList>();
                    foreach (SendToMemberListDetails objDetails in lstDetails)
                    {
                        SendToMemberList obj = new SendToMemberList();
                        obj.LoginNo = objDetails.LoginNo;
                        lst.Add(obj);
                        obj = null;
                    }
                    return lst;
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
            finally
            {
                lstDetails = null;
            }
        }

        public static List<SendToMemberList> GetSecurityGroupList()
        {
            List<SendToMemberListDetails> lstDetails = new List<SendToMemberListDetails>();
            SQLSendToMemberListProvider SQLp = new SQLSendToMemberListProvider();
            try
            {
                lstDetails = SQLp.GetRequiredSecurityGroupList();
                if (lstDetails == null)
                {
                    return new List<SendToMemberList>();
                }
                else
                {
                    List<SendToMemberList> lst = new List<SendToMemberList>();
                    foreach (SendToMemberListDetails objDetails in lstDetails)
                    {
                        SendToMemberList obj = new SendToMemberList();
                        obj.SecurityGroupNo = objDetails.SecurityGroupNo;
                        lst.Add(obj);
                        obj = null;
                    }
                    return lst;
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
            finally
            {
                lstDetails = null;
            }
        }


        #endregion
    }
}
