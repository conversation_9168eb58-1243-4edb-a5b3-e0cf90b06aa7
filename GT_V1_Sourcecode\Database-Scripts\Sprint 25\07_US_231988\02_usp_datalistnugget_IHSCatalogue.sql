﻿/* 
===========================================================================================
TASK      			UPDATED BY     		DATE         		ACTION 			DESCRIPTION
[BUG-231988]		Cuong.DoX			09-April-2025		Create			Add APIImportedData
===========================================================================================
*/
CREATE OR ALTER PROCEDURE [dbo].[usp_datalistnugget_IHSCatalogue]    
    @ClientId int  =NULL                                                                        
  , @OrderBy int = 1                                                                      
  , @SortDir int = 1                                                                      
  , @PageIndex int = 0                                                                        
  , @PageSize int = 10                                                                        
  , @PartSearch nvarchar(50) = null                                          
  , @Manufacturer nvarchar(50) = NULL                                              
  , @CountryOfOriginNo int  =NULL                                                                           
  , @MSL nvarchar(50) = NULL                                   
  , @HTSCode nvarchar(50) = NULL                      
  , @Description nvarchar(Max) = NULL                      
  , @RecentOnly BIT = 1                                    
  , @IsPoHub BIT = 0                                                                              
   WITH RECOMPILE                                          
   AS          
BEGIN                                       
   DECLARE @RecentDate datetime                                          
  , @StartPage int                                          
  , @EndPage int                     
   SET @RecentDate = dbo.ufn_get_date_from_datetime(dateadd(mm, -3, getdate()))                            
   SET @StartPage = (@PageIndex * @PageSize + 1)                                          
   SET @EndPage = ((@PageIndex + 1) * @PageSize)        
         
                                           
  ;WITH    cteSearch                                    
              AS (      
     SELECT  cr.IHSPartsId        
      ,cr.Part                                  
      ,cr.ManufacturerName                                  
      ,cr.ManufacturerNo                                  
      ,mf.ManufacturerCode                                    
      ,cr.Descriptions                                  
      ,col.GlobalCountryName as   CountryOfOrigin                                 
      ,cr.CountryOfOriginNo                                  
      ,cr.Partstatus as LifeCycleStage                                  
      ,cr.MSL                                  
      ,ml.MSLLevel as  MSLName                             
      ,cr.MSLNo                                  
      ,cr.HTSCode                                  
      ,isnull(cr.AveragePrice, 0)  as AveragePrice                              
      ,cr.packagecode                                
      ,cr.packaging                                  
      --,cr.PackagingSize                                  
      ,cr.UpdatedBy                                  
      ,cr.OriginalEntryDate                              
      ,cr.DLUP                                  
      ,cr.Inactive                                            
      --,c.cnt as RowCnt                           
   ,isnull(cr.ColPriceCurrency,' ') as ColPriceCurrency                  
   ,cr.ECCNCode        
   ,cr.IsPDFAvailable 
   ,cr.APIImportedData
    , ROW_NUMBER() OVER      
 ( ORDER BY --                                          
      case WHEN @OrderBy = 1                                        
      AND @SortDir = 2 THEN Part                                        
      END DESC                                         
      , case WHEN @OrderBy = 1 THEN Part                                        
      END                                        
      , case WHEN @OrderBy = 2                                        
      AND @SortDir = 2 THEN mf.ManufacturerCode                                        
      END DESC                                        
      , case WHEN @OrderBy = 2 THEN mf.ManufacturerCode                                         
      END                                  
                                        
      ,case WHEN @OrderBy = 3                                        
      AND @SortDir = 2 THEN CountryOfOriginNo                                      
      END DESC                                        
      ,case WHEN @OrderBy = 3 THEN CountryOfOriginNo                                        
      END                        
    ,case WHEN @OrderBy = 4                                        
      AND @SortDir = 2 THEN packaging                                        
      END DESC                                        
      ,case WHEN @OrderBy = 4 THEN packaging                                        
      END                                    
      , case WHEN @OrderBy = 5                                        
      AND @SortDir = 2 THEN OriginalEntryDate                                        
      END DESC                                        
      ,case WHEN @OrderBy = 5 THEN OriginalEntryDate                                        
      END                                        
      , case WHEN @OrderBy = 6                                        
      AND @SortDir = 2 THEN Descriptions                                        
      END DESC                    
   , case WHEN @OrderBy = 6 THEN Descriptions                                        
      END  
	  ,case WHEN @OrderBy = 7 THEN cr.DLUP                                        
      END  
   ) AS RowNum     
   --into #tmpcteSearch  
      FROM    dbo.tbIHSparts cr                                         
    left  JOIN dbo.tbManufacturer mf ON cr.ManufacturerNo = mf.ManufacturerId                                      
    left  join dbo.tbGlobalCountryList col ON cr.CountryOfOriginNo = col.GlobalCountryId                        
   --left  join dbo.tbMSLLevel ml ON cr.msl = ml.MSLLevelId                
   --left  join dbo.tbMSLLevel ml on case when cr.msl like '%[^0-9]%' then 0  else cr.msl end = ml.MSLLevelId                                 
   left join tbMSLLevel ml on case when (cr.MSL='Exempt' or cr.MSL= 'N/A') then cr.MSL else 'MSL '+ cr.msl end = MSLLevel         
    WHERE                                   
     ((@RecentOnly = 0)                      
                         OR (@RecentOnly = 1                      
                             AND cr.DLUP >= @RecentDate))                                                                                   
   and   ((@PartSearch IS NULL) OR (NOT @PartSearch IS NULL AND (cr.Part LIKE @PartSearch OR cr.Part LIKE @PartSearch)))                                  
   AND   ((@Manufacturer IS NULL) OR (NOT @Manufacturer IS NULL                                          
   AND   (mf.ManufacturerCode LIKE @Manufacturer)) OR (NOT @Manufacturer IS NULL--[001]                                          
   AND   (cr.ManufacturerName LIKE @Manufacturer)))                                           
   AND   ((@CountryOfOriginNo IS NULL) OR (NOT @CountryOfOriginNo IS NULL AND (cr.CountryOfOriginNo LIKE @CountryOfOriginNo OR cr.CountryOfOriginNo LIKE @CountryOfOriginNo)))                                  
   AND   ((@MSL IS NULL) OR (NOT @MSL IS NULL                                        
   AND   (ml.MSLLevel LIKE @MSL)) OR (NOT @MSL IS NULL--[001]                                        
   AND   (cr.msl LIKE @MSL)))                                  
   AND   ((@HTSCode IS NULL) OR (NOT @HTSCode IS NULL AND (cr.HTSCode LIKE @HTSCode OR cr.HTSCode LIKE @HTSCode)))                    
   AND   ((@Description IS NULL) OR (NOT @Description IS NULL AND (cr.Descriptions LIKE @Description OR cr.Descriptions LIKE @Description)))       
   )      
                                   
   SELECT  *                                    
      , (SELECT count(IHSPartsId)                                    
   FROM   cteSearch ) AS RowCnt                                    
   FROM    cteSearch                                           
   WHERE   RowNum BETWEEN @StartPage AND @EndPage          
       
END                   