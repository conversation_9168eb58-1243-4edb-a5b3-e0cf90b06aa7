﻿GO

IF OBJECT_ID('dbo.usp_HandleMultileApiJson', 'P') IS NOT NULL
BEGIN
    DROP PROCEDURE dbo.usp_HandleMultileApiJson;
END

GO
	/****** Object:  StoredProcedure [dbo].[usp_HandleMultileApiJson]    Script Date: 7/26/2024 11:42:06 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


/*
===========================================================================================
TASK      			UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-204486]			Phuc Hoang			26-Jul-2024		Create			Sourcing - the Digikey API to be added to the supplier data feeds section
===========================================================================================
*/

CREATE PROCEDURE [dbo].[usp_HandleMultileApiJson] (
		@ResultJson NVARCHAR(MAX),
		--@ApiName varchar(200),          
		@ClientId INT,
		@PartSearch NVARCHAR(MAX),
		@ApiURLKeyId INT,
		@UserId INT = NULL
	) 
AS 
BEGIN

DECLARE @ApiFullName varchar(100);

SET @ApiFullName =(
		SELECT ApiName
		FROM tbApiURLKey
		WHERE ApiURLKeyId = @ApiURLKeyId
	) 
	IF(@ApiURLKeyId = 1) 
		BEGIN
			EXEC Usp_InsertSupplierAPIDataFE @ClientId, @ResultJson, @PartSearch, @ApiURLKeyId, @UserId;
		END

	ELSE IF(@ApiURLKeyId = 2) 
		BEGIN
			EXEC Usp_InsertSupplierAPIDataDigikey @ClientId, @ResultJson, @PartSearch, @ApiURLKeyId, @UserId;
		END
		
END
GO
