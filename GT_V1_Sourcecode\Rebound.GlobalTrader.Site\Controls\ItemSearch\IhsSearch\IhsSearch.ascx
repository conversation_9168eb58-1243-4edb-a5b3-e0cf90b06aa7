<%@ Control Language="C#" CodeBehind="IhsSearch.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.ItemSearch.IhsSearch" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_ItemSearch:DesignBase id="ctlDB" runat="server"  InitialSortDirection="DESC">
	<FieldsLeft>
		<%--<ReboundUI_FilterDataItemRow:DropDown id="ctlGroup"  runat="server" ResourceTitle="SubGroup" DropDownType="Group"  DropDownAssembly="Rebound.GlobalTrader.Site" />--%>
        <ReboundUI_FilterDataItemRow:TextBox id="ctlSearchtxtPartNo" runat="server"  ResourceTitle="SearchtxtPartNo" />
       <%--  <ReboundUI_FilterDataItemRow:Numerical id="ctlSerialNo" runat="server" ResourceTitle="SerialNo" />--%>
	</FieldsLeft>
	<FieldsRight>
      
	</FieldsRight>
</ReboundUI_ItemSearch:DesignBase>
