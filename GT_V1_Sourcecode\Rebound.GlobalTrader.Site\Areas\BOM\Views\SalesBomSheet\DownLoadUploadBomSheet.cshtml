﻿@model Rebound.GlobalTrader.Site.Areas.BOM.Models.SalesBomSheetModel
@{
    Layout = null;

}

<!DOCTYPE html>
<script type="text/javascript" src="http://ajax.googleapis.com/ajax/libs/jquery/1.4.2/jquery.min.js"></script>
<script type="text/javascript" src="http://ajax.googleapis.com/ajax/libs/jqueryui/1.8.1/jquery-ui.min.js"></script>

<link rel="stylesheet" href="//code.jquery.com/ui/1.11.4/themes/smoothness/jquery-ui.css">
<script src="//code.jquery.com/jquery-3.6.1.js"></script>
<script src="//code.jquery.com/ui/1.12.0/jquery-ui.js"></script>

<!--ParamQuery Grid css files-->
<link rel="stylesheet" href="~/paramquery-8.1.0/pqgrid.dev.css" />

<!--add pqgrid.ui.css for jQueryUI theme support-->
<link rel="stylesheet" href="~/paramquery-8.1.0/pqgrid.ui.dev.css" />

<!--ParamQuery Grid custom theme e.g., office, bootstrap, rosy, chocolate, etc (optional)-->
<link rel="stylesheet" href="~/paramquery-8.1.0/themes/bootstrap/pqgrid.css" />

<!--ParamQuery Grid js files-->
<script type="text/javascript" src="~/paramquery-8.1.0/pqgrid.dev.js"></script>

<!--ParamQuery Grid localization file-->
<script src="~/paramquery-8.1.0/localize/pq-localize-en.js"></script>

<!--Include pqTouch file to provide support for touch devices (optional)-->
<script type="text/javascript" src="~/paramquery-8.1.0/pqTouch/pqtouch.min.js"></script>

<!--Include jsZip file to support xlsx and zip export (optional)-->
<script type="text/javascript" src="~/paramquery-8.1.0/jsZip-2.5.0/jszip.min.js"></script>

<script src="/Areas/BOM/js/salesBomImport.js"></script>
<link href="~/Areas/BOM/css/BOMManagerImport.css" rel="stylesheet" />
<html lang="en" xmlns="http://www.w3.org/1999/xhtml">
<Style>
    
</Style>

<head>
    <meta name="viewport" content="width=device-width" />
    <title>SalesBomManagerSheet</title>
</head>
<body>
    @using (Html.BeginForm("DownLoadUploadBomSheet", "SalesBomSheet", FormMethod.Post, new { id = "downLoadUploadBomSheet" }))
    {
        <div class="main_container">
            <div class="formHeader">
                <div class="download_section">
                    <table>
                        <tr>
                            <td>DownLoad Source File :</td>
                            <td><a href="\User\CSV\Sample file format customer BOM.xlsx">Download Excel Sheet</a></td>
                        </tr>
                    </table>
                </div>
                <h4>SALES BOM IMPORT</h4>
            </div>

            <div class="container-greenbase">
                <table>
                    <tr>
                        <td class="firstcol">
                            <fieldset class="space" style=" border: 1px #6cab63 solid; margin-bottom:0px;">
                                <legend>Details</legend>
                                <table>
                                    <tbody>
                                        <tr>
                                            <td>
                                                <div class="col3">
                                                    <label>Client</label>
                                                    @Html.DropDownListFor(m => m.ClientId, new SelectList((System.Collections.IEnumerable)ViewData["clients"], "ClientId", "ClientName"), "-- Please select --", new { @class = "ddlClient" })
                                                </div>
                                            </td>
                                            <td>
                                                <div class="col3">
                                                    <label>Digital Currency</label>
                                                    @Html.DropDownListFor(m => m.DigitalCurrency, new SelectList((System.Collections.IEnumerable)ViewData["currency"], "CurrencyId", "CurrencyDescription"), "-- Please select --", new { @class = "ddlClient" })
                                                </div>
                                            </td>
                                            <td>
                                                <div class="col3">
                                                    <label>
                                                        Use default currency for all lines?
                                                        @Html.CheckBoxFor(m => m.Defaultcurrency, new { @checked = "checked" })
                                                    </label>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <div class="col3 ddlcompany">
                                                    <label>Company</label>
                                                    @Html.TextBoxFor(m => m.Company, new { placeholder = "Enter Company Name", @class = "input_cont" })
                                                </div>
                                            </td>
                                            <td>
                                                <div class="col3">
                                                    <label>Salesperson</label>
                                                    @Html.TextBoxFor(m => m.Salesperson, new { placeholder = "Enter Salesperson Name", @class = "input_cont" })
                                                </div>
                                            </td>
                                            <td>
                                                <div class="col3">
                                                    <label>
                                                        Apply Partwatch to all Requirements?
                                                        @Html.CheckBoxFor(m => m.ApplyPartwatch, new { @checked = "checked" })
                                                    </label>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <div class="col3">
                                                    <label>Contact</label>
                                                    @Html.DropDownListFor(m => m.Contact, new SelectList(string.Empty, "Value", "Text"), "-- Please select --", new { @class = "ddlClient" })
                                                </div>
                                            </td>
                                            <td>
                                                <div class="col3">
                                                    <label>BOM Name</label>
                                                    @Html.TextBoxFor(m => m.BOMName, new { placeholder = "Enter BOM Name", @class = "input_cont" })
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </fieldset>
                        </td>
                        <td> <input type="button" class="btn reset" onclick="this.form.reset();" id="btnReset" value="Reset"></td>
                    </tr>
                </table>

                <table style="width: 100%;">
                    <tr>
                        <td class="firstcol">
                            <fieldset class="space">
                                <legend>CSV Data</legend>
                                <div class="row">
                                    <div class="row"><label for="file contains">@Html.CheckBoxFor(m => m.Filecolumnheader, new { @checked = "checked" }) 
                                        File contains column header</label></div>
                                    <div class="label">Files</div>
                                    <label class="file" style="margin-top:10px">
                                        @*<button class="upload" onclick="document.getElementById('getFile').click()">Upload</button>
                                            <input type='file' id="getFile" name="getFile" style="display:none" onselect="DisplayRawBOMData();" />
                                            <hr />*@
                                        @Html.TextBox("myfile", "", new { type = "file" })
                                        <input type="button" id="btnUploadExcelFile" onclick="DisplayRawBOMData();" value="Upload File" class="btn btn-primary" />
                                    </label>

                                    @*<label class="upload_txt">Drag and drop file here</label>*@
                                    @*<form method="POST" action="DocImage.ashx?mxs=1&amp;type=EXCELUPLOAD&amp;IsDragDrop=true" enctype="multipart/form-data" style="margin: 0px; padding: 0px;">
                                        <input type="file" id="ajax-upload-id-1668700461419" name="myfile" style="position: absolute; cursor: pointer; top: 0px; width: 0px;
                                        height: 20px; left: 0px; z-index: 100; opacity: 0;"></form>*@
                                    @*<label class="file">
                                            <div class="lableopt" style="width: 250px;">
                                                <div id="Imagesingleupload4">Upload</div>
                                            </div>
                                        </label>*@

                                    <div class="exlsfile">
                                        <div class="header" id="myAlert">Raw CSV data</div>
                                        <div class="boxContent">
                                            <div class="resulttable">
                                                <div id="grid_json" style="margin:auto;"></div>
                                                <div Id="UtilityBOM_BOMImport_STKI"></div>
                                                <center>
                                                    <div id="Messageid" style="background-color: #BBF2B3;height: 36px; font-size: 13px;color:red;">
                                                        <span style="color:red;"></span>
                                                    </div>
                                                </center>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </fieldset>
                        </td>
                        <td><input type="button" class="btn right .inputDisabled" onclick="GenerateImportData();" value="Generate Import Data"></td>
                    </tr>
                </table>

                <fieldset class="field_sec">
                    <table>
                        <tbody>
                            <tr>
                                <td>
                                    <div class="row">
                                        <fieldset>
                                            <legend>File colum mapping</legend>
                                            <table class="three-col">
                                                <tr>
                                                    <td>
                                                        <div class="col3">
                                                            <label>Stock code</label><input type="checkbox" id="chbStockCode" value="StockCode">
                                                            @Html.DropDownListFor(m => m.StockCode, new SelectList(string.Empty, "Value", "Text"), "-- Please select --", new { @class = "dropdown-control" })
                                                        </div>
                                                        <div class="col3">
                                                            <label>Part</label><input type="checkbox" id="chbPart" value="Part">
                                                            @*@Html.DropDownListFor(m => m.Part, new SelectList((System.Collections.IEnumerable)ViewData["excelColumnName"], "Id", "ColumnsName"))*@
                                                            @Html.DropDownListFor(m => m.Part, new SelectList(string.Empty, "Value", "Text"), "-- Please select --", new { @class = "dropdown-control" })
                                                        </div>
                                                        <div class="col3">
                                                            <label>Unit Price</label><input type="checkbox" id="chbUnitPrice" value="UnitPrice">
                                                            @*@Html.DropDownListFor(m => m.Quantity, new SelectList((System.Collections.IEnumerable)ViewData["excelColumnName"], "Id", "ColumnsName"))*@
                                                            @Html.DropDownListFor(m => m.UnitPrice, new SelectList(string.Empty, "Value", "Text"), "-- Please select --", new { @class = "dropdown-control" })
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <div class="col3">
                                                            <label>Description</label><input type="checkbox" id="chbDescription" value="Description">
                                                            @Html.DropDownListFor(m => m.Description, new SelectList(string.Empty, "Value", "Text"), "-- Please select --", new { @class = "dropdown-control" })
                                                        </div>
                                                        <div class="col3">
                                                            <label>RFQ</label><input type="checkbox" id="chbRFQ" value="RFQ">
                                                            @*@Html.DropDownListFor(m => m.Price, new SelectList((System.Collections.IEnumerable)ViewData["excelColumnName"], "Id", "ColumnsName"))*@
                                                            @Html.DropDownListFor(m => m.RFQ, new SelectList(string.Empty, "Value", "Text"), "-- Please select --", new { @class = "dropdown-control" })
                                                        </div>
                                                        <div class="col3">
                                                            <label>Line Total</label><input type="checkbox" id="chbLineTotal" value="LineTotal">
                                                            @*@Html.DropDownListFor(m => m.Price, new SelectList((System.Collections.IEnumerable)ViewData["excelColumnName"], "Id", "ColumnsName"))*@
                                                            @Html.DropDownListFor(m => m.LineTotal, new SelectList(string.Empty, "Value", "Text"), "-- Please select --", new { @class = "dropdown-control" })
                                                        </div>
                                                    </td>
                                                </tr>
                                            </table>
                                        </fieldset>
                                    </div>
                                </td>
                                <td style="width:9%;">
                                    <input type="button" id="RetrieveSupplierMappings" name="RetrieveSupplierMappings" onclick="RetrieveSupplierMappingsSave();" class="btn right" value="Save as RFQ for Import">
                                    <input type="button" id="SaveSupplierMappings" name="SaveSupplierMappings" onclick="SaveSupplierMappingsSave();" class="btn right" value="Save as HUBRFQ">
                                </td>
                            </tr>

                            <tr>
                                <td>
                                    <div>
                                        <fieldset>
                                            <table class="three-col">
                                                <tr>
                                                    <td>
                                                        <div>
                                                            <label>Requirement for Traceability</label>
                                                            @Html.DropDownListFor(m => m.RequiremenTraceability, new SelectList((System.Collections.IEnumerable)ViewData["RequiremenTraceability"], "Id", "RequiremenTraceabilityName"), "-- Please select --", new { @class = "ddlClient" })
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <div class="col3">
                                                            <label>Type</label>
                                                            @Html.DropDownListFor(m => m.Type, new SelectList((System.Collections.IEnumerable)ViewData["UploadType"], "Id", "DataType"), "-- Please select --", new { @class = "ddlClient" })
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <div class="col3 date_box calendarControls">
                                                            <label>Delivery Date Required</label>
                                                            @*@Html.DropDownListFor(m => m.CurrentDateTime,new DateTime(DateTime.Now.ToString()))*@
                                                            @Html.TextBoxFor(x => x.CurrentDateTime, "{0:dd MMM yyyy}", new
                                                            {
                                                                @type = "date",
                                                                Value = (Model.CurrentDateTime == null ?
                                                                DateTime.Now : DateTime.Parse
                                                                (Model.CurrentDateTime.ToString())), data_date_format = "dd/mm/yyyy"
                                                            })
                                                        </div>
                                                    </td>
                                                </tr>
                                            </table>
                                        </fieldset>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <div class="row">
                                        <fieldset>
                                            <div class="exlsfile">
                                                <div class="header" id="myAlert">Data to import</div>
                                            </div>
                                            <div class="boxContent">
                                                <div class="resulttable">
                                                    <div id="gridDataImport_json"></div>
                                                    <div Id="UtilityBOM_BOMImport_STKI"></div>
                                                    <center>
                                                        <div id="Messageid" style="background-color: #BBF2B3;height: 36px; font-size: 13px;color:red;">
                                                            <span style="color:red;"></span>
                                                        </div>
                                                    </center>
                                                </div>
                                            </div>
                                        </fieldset>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </fieldset>
            </div>
        </div>
    }
</body>
</html>
