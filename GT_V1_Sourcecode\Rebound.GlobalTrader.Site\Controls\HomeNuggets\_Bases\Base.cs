using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;

namespace Rebound.GlobalTrader.Site.Controls.HomeNuggets {
	/// <summary>
	/// Common functionality for homepage nuggets
	/// </summary>
	public class Base : Nuggets.Base {

		#region Controls

		#endregion

		#region Properties

		public string HomePageNuggetType { get; set; }

		private int _intRowCount = 5;
		/// <summary>
		/// Number of data rows to show
		/// </summary>
		public int RowCount {
			get { return _intRowCount; }
			set { _intRowCount = value; }
		}

		private bool _blnCanShowForAnotherUser = false;
		/// <summary>
		/// Can this nugget show data for user other than the current logged in user?
		/// </summary>
		public bool CanShowForAnotherUser {
			get { return _blnCanShowForAnotherUser; }
			set { _blnCanShowForAnotherUser = value; }
		}


		#endregion

		/// <summary>
		/// On init
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
			TitleText = Functions.GetGlobalResource("HomeNuggets", HomePageNuggetType);
			NoDataMessageResource = HomePageNuggetType;
			base.OnInit(e);
			AddScriptReference("Controls.HomeNuggets._Bases.Base.js");
		}

		protected override void OnLoad(EventArgs e) {
			EnsureChildControls();
			_scScriptControlDescriptor.AddProperty("intCompanyID", SessionManager.ClientID);
			_scScriptControlDescriptor.AddProperty("intRowCount", _intRowCount);
			_scScriptControlDescriptor.AddProperty("blnCanShowForAnotherUser", _blnCanShowForAnotherUser);
			if (_blnCanShowForAnotherUser) {
				_scScriptControlDescriptor.AddProperty("strTitle_My", Functions.GetGlobalResource("HomeNuggets", HomePageNuggetType));
				_scScriptControlDescriptor.AddProperty("strTitle_Other", Functions.GetGlobalResource("HomeNuggets", String.Format("{0}_ForUser", HomePageNuggetType)));
				_scScriptControlDescriptor.AddProperty("strNoData_My", Functions.GetGlobalResource("NotFound", HomePageNuggetType));
				_scScriptControlDescriptor.AddProperty("strNoData_Other", Functions.GetGlobalResource("NotFound", String.Format("{0}_ForUser", HomePageNuggetType)));
				_scScriptControlDescriptor.AddProperty("intLoginID_My", SessionManager.LoginID);
			}
			base.OnLoad(e);
		}

		protected override void CreateChildControls() {
			base.CreateChildControls();
		}
	}
}