﻿<%@ Control Language="C#" AutoEventWireup="true" CodeBehind="ShortShipment.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.DataListNuggets.ShortShipment.ShortShipment" %>

<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Nugget:DesignBase ID="ctlDB" runat="server" BoxType="List">
     <%--[001] code start--%>
    <Links>
        <ReboundUI:IconButton ID="ibtnExportCSV" runat="server" Style="margin-left:8px;" IconButtonMode="hyperlink" IconGroup="Nugget" IconTitleResource="ExportToExcel" />
    </Links>
    <%--[001] code end--%>
	<Filters>
		<ReboundUI_DataListNugget:Filter ID="ctlFilter" runat="server">
			<FieldsLeft>
                <ReboundUI_FilterDataItemRow:Numerical id="ctlGINumber" runat="server" ResourceTitle="GINoNpr" FilterField="GINumber" />
                <ReboundUI_FilterDataItemRow:CheckBox id="ctlRecentOnly" runat="server" ResourceTitle="RecentOnly" FilterField="RecentOnly" DefaultValue="true" />
				<ReboundUI_FilterDataItemRow:TextBox id="ctlSupplier" runat="server" ResourceTitle="Supplier" FilterField="Supplier" />
                <ReboundUI_FilterDataItemRow:DateSelect id="ctlDateReceived" runat="server" ResourceTitle="DateReceived" FilterField="DateReceived" />
                <ReboundUI_FilterDataItemRow:Numerical id="ctlPurchaseOrderNo" runat="server" ResourceTitle="PurchaseOrderNo" FilterField="PurchaseOrderNo" />
                <ReboundUI_FilterDataItemRow:Numerical id="ctlQuantityOrdered" runat="server" ResourceTitle="QuantityOrdered" FilterField="QuantityOrdered" />
				<ReboundUI_FilterDataItemRow:DropDown id="ctlClient" runat="server" ResourceTitle="Client" FilterField="ClientByMaster" DropDownType="ClientByMaster" DropDownAssembly="Rebound.GlobalTrader.Site" />
			</FieldsLeft>
			<FieldsRight>
                <ReboundUI_FilterDataItemRow:DropDown id="ctlBuyer" runat="server" ResourceTitle="Buyer" FilterField="Buyer" DropDownType="Employee" DropDownAssembly="Rebound.GlobalTrader.Site" />
                <ReboundUI_FilterDataItemRow:Numerical id="ctlQuantityReceived" runat="server" ResourceTitle="QuantityReceived" FilterField="QuantityReceived" />
				<ReboundUI_FilterDataItemRow:Numerical id="ctlShortageQuantity" runat="server" ResourceTitle="ShortageQuantity" FilterField="ShortageQuantity" />
                <ReboundUI_FilterDataItemRow:Numerical id="ctlShortageValue" runat="server" ResourceTitle="ShortageValue" FilterField="ShortageValue" />
                <ReboundUI_FilterDataItemRow:CheckBox id="ctlIsShortageRefundIssue" runat="server" ResourceTitle="IsShortageRefundIssue" FilterField="IsShortageRefundIssue" />
                <ReboundUI_FilterDataItemRow:DropDown id="ctlStatus" runat="server" ResourceTitle="Status" FilterField="ShortShipmentStatus" DropDownType="ShortShipmentStatus" DropDownAssembly="Rebound.GlobalTrader.Site" />
			</FieldsRight>
		</ReboundUI_DataListNugget:Filter>
	</Filters>
</ReboundUI_Nugget:DesignBase>