﻿/* Marker    changed by      date           Remarks
  [001]      Vinay           11/08/2014     ESMS  Ticket Number: 	200
 */
using System;
using System.Data;
using System.Data.SqlClient;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;
using System.Web.Caching;
using System.Data.Common;

namespace Rebound.GlobalTrader.DAL.SqlClient {
	public class SqlGlobalSalesPersonProvider : GlobalSalesPersonProvider
	{
		
		
        /// <summary>
        /// GetListForSupplier 
		/// Calls [usp_selectAll_ManufacturerLink_for_Supplier]
        /// </summary>
		public override List<GlobalSalesPersonDetails> GetListForGlobalSalesPerson(System.Int32? supplierCompanyNo, System.Int32? ClientNo=0) {
			SqlConnection cn = null;
			SqlCommand cmd = null;
			try {
				cn = new SqlConnection(this.ConnectionString);
				cmd = new SqlCommand("usp_GSA_GetGlobalSalesPerson", cn);
				cmd.CommandType = CommandType.StoredProcedure;
				cmd.CommandTimeout = 30;
				cmd.Parameters.Add("@CompanyNo", SqlDbType.Int).Value = supplierCompanyNo;
				cmd.Parameters.Add("@ClientNo", SqlDbType.Int).Value = ClientNo;
				cn.Open();
				DbDataReader reader = ExecuteReader(cmd);
				List<GlobalSalesPersonDetails> lst = new List<GlobalSalesPersonDetails>();
				while (reader.Read()) {
					GlobalSalesPersonDetails obj = new GlobalSalesPersonDetails();
					obj.GlobalSalesPersonId = GetReaderValue_Int32(reader, "GlobalSalesPersonId", 0);
					obj.SalesPersonName = GetReaderValue_String(reader, "SalesPersonName", "");
					obj.ClientName = GetReaderValue_String(reader, "ClientName", "");
					obj.CompanyNo = GetReaderValue_NullableInt32(reader, "CompanyNo", null);
					obj.LoginNo = GetReaderValue_NullableInt32(reader, "LoginNo", null);
					lst.Add(obj);
					obj = null;
				}
				return lst;
			} catch (SqlException sqlex) {
				//LogException(sqlex);
				throw new Exception("Failed to get Global sales persons.", sqlex);
			} finally {
				cmd.Dispose();
				cn.Close();
				cn.Dispose();
			}
		}


		/// <summary>
		/// Create a new row
		/// Calls [usp_GSA_InsertGlobalSalesPerson]
		/// </summary>
		public override Int32 Insert(System.Int32? CompanyNo, System.Int32? loginNo, System.Int32? updatedBy, System.Int32? ClientNo)
		{
			SqlConnection cn = null;
			SqlCommand cmd = null;
			try
			{
				cn = new SqlConnection(this.ConnectionString);
				cmd = new SqlCommand("usp_GSA_InsertGlobalSalesPerson", cn);
				cmd.CommandType = CommandType.StoredProcedure;
				cmd.Parameters.Add("@CompanyNo", SqlDbType.Int).Value = CompanyNo;
				cmd.Parameters.Add("@LoginNo", SqlDbType.Int).Value = loginNo;
				cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
				cmd.Parameters.Add("@ClientNo", SqlDbType.Int).Value = ClientNo;
				cmd.Parameters.Add("@NewId", SqlDbType.Int).Direction = ParameterDirection.Output;
				cn.Open();
				int ret = ExecuteNonQuery(cmd);
				return (Int32)cmd.Parameters["@NewId"].Value;
			}
			catch (SqlException sqlex)
			{
				//LogException(sqlex);
				throw new Exception("Failed to insert Global sales persons.", sqlex);
			}
			finally
			{
				cmd.Dispose();
				cn.Close();
				cn.Dispose();
			}
		}

		/// <summary>
		/// Delete SecurityGroupLogin
		/// Calls [usp_GSA_DeleteGlobalSalesPerson]
		/// </summary>
		public override bool Delete(System.Int32? loginNo, System.Int32? CompannyNo, System.Int32? ClientNo)
		{
			SqlConnection cn = null;
			SqlCommand cmd = null;
			try
			{
				cn = new SqlConnection(this.ConnectionString);
				cmd = new SqlCommand("usp_GSA_DeleteGlobalSalesPerson", cn);
				cmd.CommandType = CommandType.StoredProcedure;
				cmd.Parameters.Add("@LoginNo", SqlDbType.Int).Value = loginNo;
				cmd.Parameters.Add("@Companyno", SqlDbType.Int).Value = CompannyNo;
				cmd.Parameters.Add("@ClientNo", SqlDbType.Int).Value = ClientNo;
				cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
				cn.Open();
				int ret = ExecuteNonQuery(cmd);
				return (ret > 0);
			}
			catch (SqlException sqlex)
			{
				//LogException(sqlex);
				throw new Exception("Failed to delete Global sales persons.", sqlex);
			}
			finally
			{
				cmd.Dispose();
				cn.Close();
				cn.Dispose();
			}
		}


	}
}