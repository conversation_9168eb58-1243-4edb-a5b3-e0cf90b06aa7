Type.registerNamespace("Rebound.GlobalTrader.Site.Controls");Rebound.GlobalTrader.Site.Controls.StarRating=function(n){Rebound.GlobalTrader.Site.Controls.StarRating.initializeBase(this,[n]);this._aryStars=[]};Rebound.GlobalTrader.Site.Controls.StarRating.prototype={get_intMaxRating:function(){return this._intMaxRating},set_intMaxRating:function(n){this._intMaxRating!==n&&(this._intMaxRating=n)},get_intInitialRating:function(){return this._intInitialRating},set_intInitialRating:function(n){this._intInitialRating!==n&&(this._intInitialRating=n)},get_intCurrentRating:function(){return this._intCurrentRating},set_intCurrentRating:function(n){this._intCurrentRating!==n&&(this._intCurrentRating=n)},get_blnReadOnly:function(){return this._blnReadOnly},set_blnReadOnly:function(n){this._blnReadOnly!==n&&(this._blnReadOnly=n)},addChanged:function(n){this.get_events().addHandler("Changed",n)},removeChanged:function(n){this.get_events().removeHandler("Changed",n)},onChanged:function(){var n=this.get_events().getHandler("Changed");n&&n(this,Sys.EventArgs.Empty)},initialize:function(){Rebound.GlobalTrader.Site.Controls.StarRating.callBaseMethod(this,"initialize");this._blnReadOnly||$addHandler(this.get_element(),"mouseout",Function.createDelegate(this,this.onMouseOut));this._intCurrentRating=this._intInitialRating;this.setupStars()},dispose:function(){this.isDisposed||(this.get_element()&&$clearHandlers(this.get_element()),this._aryStars=null,this._intMaxRating=null,this._intInitialRating=null,this._intCurrentRating=null,this._blnReadOnly=null,Rebound.GlobalTrader.Site.Controls.StarRating.callBaseMethod(this,"dispose"),this.isDisposed=!0)},setupStars:function(){this._aryStars=this.get_element().childNodes},onMouseOut:function(){this.setRating(this._intCurrentRating)},starMouseOver:function(n){for(var t=0,i=this._intMaxRating;t<i;t++)this.setStar(t,t<=n,"starsFilled")},starClick:function(n){n=n==this._intMaxRating?0:n+1;this.setRating(n)},setRating:function(n){this._intCurrentRating=n;for(var t=0,i=this._intMaxRating;t<i;t++)this.setStar(t,t<n,"starsSaved");this.onChanged()},resetRating:function(){this.setRating(this._intInitialRating)},setStar:function(n,t,i){this._aryStars[n].className=t?"stars "+i:"stars starsEmpty"}};Rebound.GlobalTrader.Site.Controls.StarRating.registerClass("Rebound.GlobalTrader.Site.Controls.StarRating",Sys.UI.Control,Sys.IDisposable);