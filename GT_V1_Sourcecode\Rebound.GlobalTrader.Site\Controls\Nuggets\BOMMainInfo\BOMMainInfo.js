Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Nuggets");Rebound.GlobalTrader.Site.Controls.Nuggets.BOMMainInfo=function(n){Rebound.GlobalTrader.Site.Controls.Nuggets.BOMMainInfo.initializeBase(this,[n,]);this._intBOMID=-1;this._blnHasRequirement=!1;this._blnRequestedToPoHub=!1;this._blnRelease=!1;this._isAddButtonEnable=!0;this._isPurchaseHub=!1;this._intCurrencyNo=-1;this._BomCode="";this._BomName="";this._BomCompanyName="";this._BomCompanyNo=0;this._intContact2No=-1;this._stringCurrency=null;this._inActive=!1;this._BomContactname="";this._BomContactNo=0;this._CurrentSupplier="";this._QuoteRequired="";this._blnAllHasDelDate=!1;this._blnAllHasProduct=!1;this._blnCanReleaseAll=!1;this._blnAllItemHasSourcing=!1;this.BOMStatus="";this._isClosed=!1;this._UpdatedBy=null;this._blnCanNoBidAll=!0;this._isNoBidCount=!1;this._RequestToPOHubBy=-1;this._UpdateByPH=-1;this._blnReqInValid=!1;this._ValidMessage="";this._BomClientNo=-1;this._blnMerginCanReleaseAll=!1;this._UnitBuyPrice=null;this._UnitSellPrice=null;this._IsAssignToMe=!1;this._blnPVVBOMValidateMessage="";this._PVVBOMCountValid=!1};Rebound.GlobalTrader.Site.Controls.Nuggets.BOMMainInfo.prototype={get_intBOMID:function(){return this._intBOMID},set_intBOMID:function(n){this._intBOMID!==n&&(this._intBOMID=n)},get_ibtnEdit:function(){return this._ibtnEdit},set_ibtnEdit:function(n){this._ibtnEdit!==n&&(this._ibtnEdit=n)},get_ibtnDelete:function(){return this._ibtnDelete},set_ibtnDelete:function(n){this._ibtnDelete!==n&&(this._ibtnDelete=n)},get_ibtnExportCSV:function(){return this._ibtnExportCSV},set_ibtnExportCSV:function(n){this._ibtnExportCSV!==n&&(this._ibtnExportCSV=n)},get_ibtnExportPurchaseHUB:function(){return this._ibtnExportPurchaseHUB},set_ibtnExportPurchaseHUB:function(n){this._ibtnExportPurchaseHUB!==n&&(this._ibtnExportPurchaseHUB=n)},get_ibtnNotify:function(){return this._ibtnNotify},set_ibtnNotify:function(n){this._ibtnNotify!==n&&(this._ibtnNotify=n)},get_ibtnRelease:function(){return this._ibtnRelease},set_ibtnRelease:function(n){this._ibtnRelease!==n&&(this._ibtnRelease=n)},get_blnPOHub:function(){return this._blnPOHub},set_blnPOHub:function(n){this._blnPOHub!==n&&(this._blnPOHub=n)},get_ibtnClose:function(){return this._ibtnClose},set_ibtnClose:function(n){this._ibtnClose!==n&&(this._ibtnClose=n)},get_ibtnNoBid:function(){return this._ibtnNoBid},set_ibtnNoBid:function(n){this._ibtnNoBid!==n&&(this._ibtnNoBid=n)},get_ibtnNote:function(){return this._ibtnNote},set_ibtnNote:function(n){this._ibtnNote!==n&&(this._ibtnNote=n)},get_ibtnViewTree:function(){return this._ibtnViewTree},set_ibtnViewTree:function(n){this._ibtnViewTree!==n&&(this._ibtnViewTree=n)},get_ibtnCrossMatch:function(){return this._ibtnCrossMatch},set_ibtnCrossMatch:function(n){this._ibtnCrossMatch!==n&&(this._ibtnCrossMatch=n)},get_IsDiffrentClient:function(){return this._IsDiffrentClient},set_IsDiffrentClient:function(n){this._IsDiffrentClient!==n&&(this._IsDiffrentClient=n)},get_IsGSAEditPermission:function(){return this._IsGSAEditPermission},set_IsGSAEditPermission:function(n){this._IsGSAEditPermission!==n&&(this._IsGSAEditPermission=n)},get_IsGSA:function(){return this._IsGSA},set_IsGSA:function(n){this._IsGSA!==n&&(this._IsGSA=n)},get_ClientId:function(){return this._ClientId},set_ClientId:function(n){this._ClientId!==n&&(this._ClientId=n)},addGotData:function(n){this.get_events().addHandler("GotData",n)},removeGotData:function(n){this.get_events().removeHandler("GotData",n)},onGotData:function(){var n=this.get_events().getHandler("GotData");n&&n(this,Sys.EventArgs.Empty)},addCallBeforeRelease:function(n){this.get_events().addHandler("CallBeforeRelease",n)},removeCallBeforeRelease:function(n){this.get_events().removeHandler("CallBeforeRelease",n)},onCallBeforeRelease:function(){var n=this.get_events().getHandler("CallBeforeRelease");n&&n(this,Sys.EventArgs.Empty)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Nuggets.BOMMainInfo.callBaseMethod(this,"initialize");this.addRefreshEvent(Function.createDelegate(this,this.getData));this._ibtnExportCSV&&$R_IBTN.addClick(this._ibtnExportCSV,Function.createDelegate(this,this.showExportCSV));this._ClientId!=114&&(this._IsDiffrentClient==!0?this._IsGSA==!0?this._IsGSAEditPermission==!0?($("#ctl00_cphMain_ctlLines_ctlDB_pnlLinks").show(),$("#ctl00_cphMain_POApprovals_ctlDB_pnlLinks").show(),$("#ctl00_cphMain_ctlPOPDFDragDrop_ctlDB_pnlLinks").show(),$("#ctl00_cphMain_ctlBOMItems_ctlDB_pnlLinks").show(),$("#ctl00_cphMain_ctlBomCSV_ctlDB_pnlLinks").show(),$("#ctl00_cphMain_ctlSourcingResults_ctlDB_pnlLinks").show(),$("#ctl00_cphMain_ctlPOHubSourcing_ctlDB_ctl13_ibtnSearch").show(),$("#ctl00_cphMain_ctlPOHubSourcing_ctlDB_ctl13_ibtnAddStockInfo").show(),$("#ctl00_cphMain_ctlPOHubSourcing_ctlDB_ctl13_ibtnAddOffer").show(),$("#ctl00_cphMain_ctlPOHubSourcing_ctlDB_ctl13_ibtnAddTrusted").show()):($("#ctl00_cphMain_ctlLines_ctlDB_pnlLinks").hide(),$("#ctl00_cphMain_POApprovals_ctlDB_pnlLinks").hide(),$("#ctl00_cphMain_ctlPOPDFDragDrop_ctlDB_pnlLinks").hide(),$("#ctl00_cphMain_ctlBOMItems_ctlDB_pnlLinks").hide(),$("#ctl00_cphMain_ctlBomCSV_ctlDB_pnlLinks").hide(),$("#ctl00_cphMain_ctlSourcingResults_ctlDB_pnlLinks").hide(),$("#ctl00_cphMain_ctlPOHubSourcing_ctlDB_ctl13_ibtnSearch").hide(),$("#ctl00_cphMain_ctlPOHubSourcing_ctlDB_ctl13_ibtnAddStockInfo").hide(),$("#ctl00_cphMain_ctlPOHubSourcing_ctlDB_ctl13_ibtnAddOffer").hide(),$("#ctl00_cphMain_ctlPOHubSourcing_ctlDB_ctl13_ibtnAddTrusted").hide()):($("#ctl00_cphMain_ctlLines_ctlDB_pnlLinks").show(),$("#ctl00_cphMain_POApprovals_ctlDB_pnlLinks").show(),$("#ctl00_cphMain_ctlPOPDFDragDrop_ctlDB_pnlLinks").show(),$("#ctl00_cphMain_ctlBOMItems_ctlDB_pnlLinks").show(),$("#ctl00_cphMain_ctlBomCSV_ctlDB_pnlLinks").show(),$("#ctl00_cphMain_ctlSourcingResults_ctlDB_pnlLinks").show(),$("#ctl00_cphMain_ctlPOHubSourcing_ctlDB_ctl13_ibtnSearch").show(),$("#ctl00_cphMain_ctlPOHubSourcing_ctlDB_ctl13_ibtnAddStockInfo").show(),$("#ctl00_cphMain_ctlPOHubSourcing_ctlDB_ctl13_ibtnAddOffer").show(),$("#ctl00_cphMain_ctlPOHubSourcing_ctlDB_ctl13_ibtnAddTrusted").show()):($("#ctl00_cphMain_ctlLines_ctlDB_pnlLinks").show(),$("#ctl00_cphMain_POApprovals_ctlDB_pnlLinks").show(),$("#ctl00_cphMain_ctlPOPDFDragDrop_ctlDB_pnlLinks").show(),$("#ctl00_cphMain_ctlBOMItems_ctlDB_pnlLinks").show(),$("#ctl00_cphMain_ctlBomCSV_ctlDB_pnlLinks").show(),$("#ctl00_cphMain_ctlSourcingResults_ctlDB_pnlLinks").show(),$("#ctl00_cphMain_ctlPOHubSourcing_ctlDB_ctl13_ibtnSearch").show(),$("#ctl00_cphMain_ctlPOHubSourcing_ctlDB_ctl13_ibtnAddStockInfo").show(),$("#ctl00_cphMain_ctlPOHubSourcing_ctlDB_ctl13_ibtnAddOffer").show(),$("#ctl00_cphMain_ctlPOHubSourcing_ctlDB_ctl13_ibtnAddTrusted").show()));this._ibtnEdit&&($R_IBTN.addClick(this._ibtnEdit,Function.createDelegate(this,this.showEditForm)),this._frmEdit=$find(this._aryFormIDs[0]),this._frmEdit._intBOMID=this._intBOMID,this._frmEdit._BomCode=this._BomCode,this._frmEdit._BomName=this._BomName,this._frmEdit._BomCompanyName=this._BomCompanyName,this._frmEdit._BomCompanyNo=this._BomCompanyNo,this._frmEdit.addCancel(Function.createDelegate(this,this.cancelEdit)),this._frmEdit.addSaveComplete(Function.createDelegate(this,this.saveEditComplete)));this._ibtnDelete&&($R_IBTN.addClick(this._ibtnDelete,Function.createDelegate(this,this.showDeleteForm)),this._frmDelete=$find(this._aryFormIDs[1]),this._frmDelete._intBOMID=this._intBOMID,this._frmDelete._BomCode=this._BomCode,this._frmDelete._BomName=this._BomName,this._frmDelete._BomCompanyName=this._BomCompanyName,this._frmDelete._BomCompanyNo=this._BomCompanyNo,this._frmDelete.addNotConfirmed(Function.createDelegate(this,this.cancelDelete)),this._frmDelete.addSaveComplete(Function.createDelegate(this,this.saveDeleteComplete)));this._ibtnNotify&&($R_IBTN.addClick(this._ibtnNotify,Function.createDelegate(this,this.showNotifyForm)),this._frmNotify=$find(this._aryFormIDs[2]),this._frmNotify._BomCode=this._BomCode,this._frmNotify._BomName=this._BomName,this._frmNotify._BomCompanyName=this._BomCompanyName,this._frmNotify._BomCompanyNo=this._BomCompanyNo,this._frmNotify.addCancel(Function.createDelegate(this,this.cancelNotifyForm)),this._frmNotify.addSaveComplete(Function.createDelegate(this,this.saveNotifyComplete)),this._frmNotify.addNotConfirmed(Function.createDelegate(this,this.hideNotifyForm)));this._ibtnExportPurchaseHUB&&($R_IBTN.addClick(this._ibtnExportPurchaseHUB,Function.createDelegate(this,this.getValidation)),this._frmConfirm=$find(this._aryFormIDs[3]),this._frmConfirm._intBOMID=this._intBOMID,this._frmConfirm._BomCode=this._BomCode,this._frmConfirm._BomName=this._BomName,this._frmConfirm._BomCompanyName=this._BomCompanyName,this._frmConfirm._BomCompanyNo=this._BomCompanyNo,this._frmConfirm._intContact2No=this._intContact2No,this._frmConfirm.addNotConfirmed(Function.createDelegate(this,this.cancelConfirm)),this._frmConfirm.addSaveComplete(Function.createDelegate(this,this.saveConfirmComplete)));this._ibtnRelease&&($R_IBTN.addClick(this._ibtnRelease,Function.createDelegate(this,this.showReleaseForm)),this._frmRelease=$find(this._aryFormIDs[4]),this._frmRelease._intBOMID=this._intBOMID,this._frmRelease._BomCode=this._BomCode,this._frmRelease._BomName=this._BomName,this._frmRelease._BomCompanyName=this._BomCompanyName,this._frmRelease._BomCompanyNo=this._BomCompanyNo,this._frmRelease._UpdatedBy=this._UpdatedBy,this._frmRelease.addNotConfirmed(Function.createDelegate(this,this.cancelRelease)),this._frmRelease.addSaveComplete(Function.createDelegate(this,this.saveReleaseComplete)));this._ibtnClose&&($R_IBTN.addClick(this._ibtnClose,Function.createDelegate(this,this.showConfirmCloseForm)),this._frmConfirmClose=$find(this._aryFormIDs[5]),this._frmConfirmClose._intBOMID=this._intBOMID,this._frmConfirmClose.addNotConfirmed(Function.createDelegate(this,this.cancelConfirmClose)),this._frmConfirmClose.addSaveComplete(Function.createDelegate(this,this.saveConfirmCloseComplete)));this._ibtnNoBid&&($R_IBTN.addClick(this._ibtnNoBid,Function.createDelegate(this,this.showNoBidForm)),this._frmNoBid=$find(this._aryFormIDs[6]),this._frmNoBid._intBOMID=this._intBOMID,this._frmNoBid._BomCode=this._BomCode,this._frmNoBid._BomName=this._BomName,this._frmNoBid._BomCompanyName=this._BomCompanyName,this._frmNoBid._BomCompanyNo=this._BomCompanyNo,this._frmNoBid._UpdatedBy=this._UpdatedBy,this._frmNoBid.addNotConfirmed(Function.createDelegate(this,this.cancelNoBid)),this._frmNoBid.addSaveComplete(Function.createDelegate(this,this.saveNoBidComplete)));this._ibtnNote&&($R_IBTN.addClick(this._ibtnNote,Function.createDelegate(this,this.showExpediteNoteForm)),this._frmAddExpediteNote=$find(this._aryFormIDs[7]),this._frmAddExpediteNote.addCancel(Function.createDelegate(this,this.cancelAddExpediteNoteForm)),this._frmAddExpediteNote.addSaveComplete(Function.createDelegate(this,this.saveAddExpediteNoteComplete)));this._ibtnViewTree&&$R_IBTN.addClick(this._ibtnViewTree,Function.createDelegate(this,this.OpenDocTree));this._ibtnCrossMatch&&$R_IBTN.addClick(this._ibtnCrossMatch,Function.createDelegate(this,this.OpenCrossMatch));this.getData()},dispose:function(){this.isDisposed||(this._ibtnEdit&&$R_IBTN.clearHandlers(this._ibtnEdit),this._ibtnNotify&&$R_IBTN.clearHandlers(this._ibtnNotify),this._ibtnDelete&&$R_IBTN.clearHandlers(this._ibtnDelete),this._ibtnClose&&$R_IBTN.clearHandlers(this._ibtnClose),this._frmEdit&&this._frmEdit.dispose(),this._frmDelete&&this._frmDelete.dispose(),this._ibtnNotify=null,this._intBOMID=null,this._ibtnEdit=null,this._ibtnDelete=null,this._frmEdit=null,this._frmDelete=null,this._ibtnExportCSV=null,this._IsDiffrentClient=null,this._ClientId=null,this._IsGSAEditPermission=null,this._IsGSA=null,this._blnHasRequirement=null,this._blnPOHub=null,this._blnRequestedToPoHub=null,this._blnRelease=null,this._intCurrencyNo=null,this._blnAllHasDelDate=null,this._blnAllHasProduct=null,this._blnCanReleaseAll=null,this._blnAllItemHasSourcing=null,this._ibtnClose=null,this._ibtnNoBid=null,this._ibtnNote=null,this._ValidMessage=null,this._intContact2No=null,this._ibtnCrossMatch=null,this._blnMerginCanReleaseAll=null,this._UnitBuyPrice=null,this._UnitSellPrice=null,this._blnPVVBOMValidateMessage=null,this._PVVBOMCountValid=null,Rebound.GlobalTrader.Site.Controls.Nuggets.BOMMainInfo.callBaseMethod(this,"dispose"))},getData:function(){$("#ctl00_cphMain_ctlMainInfo_ctlDB_BuyAndPriceMergine").hide();this._blnRequestedToPoHub=!1;this.getData_Start();var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/BOMMainInfo");n.set_DataObject("BOMMainInfo");n.set_DataAction("GetData");n.addParameter("id",this._intBOMID);n.addDataOK(Function.createDelegate(this,this.getDataOK));n.addError(Function.createDelegate(this,this.getDataError));n.addTimeout(Function.createDelegate(this,this.getDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getDataOK:function(n){var t=n._result;this.BOMStatus=t.BOMStatus;t.IsPoHub&&(this._isPurchaseHub=t.IsPoHub,this.disableNotifyAndExportButton(!t.IsPoHub));this._BomCode=t.Code;this._BomName=t.Name;this._BomCompanyName=t.Company;this._BomCompanyNo=t.CompanyNo;this.setFieldValue("ctlCode",$R_FN.setCleanTextValue(t.Code));this.setFieldValue("ctlName",$R_FN.setCleanTextValue(t.Name));this.setFieldValue("ctlNotes",$R_FN.setCleanTextValue(t.Notes,!0));this.setFieldValue("ctlAS6081",$R_FN.setCleanTextValue(t.AS6081));t.AS6081=="Yes"?$("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl13_ctlAS6081_lbl").css("background-color","yellow"):$("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl13_ctlAS6081_lbl").css("background-color","white");this.setFieldValue("ctlInActive",t.InActive);this.setFieldValue("ctlCompany",$R_FN.setCleanTextValue(t.Company)+" ("+$R_FN.setCleanTextValue(t.CompanyType)+")"+$R_FN.createAdvisoryNotesIcon(t.CompanyAdvisoryNotes));this.setFieldValue("ctlContact",$R_FN.setCleanTextValue(t.Contact));this.setFieldValue("hidCompanyNo",t.CompanyNo);this.setFieldValue("hidContactNo",t.ContactNo);this._blnRequestedToPoHub=t.blnReqToPoHub;this._blnRelease=t.blnRelease;this._blnRelease==!0&&($("#ctl00_cphMain_ctlBOMItems_ctlDB_ctl12_ibtnImportSrcReslt_hyp").prop("disabled",!0).css("opacity",.5),$("#ctl00_cphMain_ctlBOMItems_ctlDB_ctl12_ibtnImportSrcReslt_hyp").addClass("disable-click"));this._blnRelease==!1&&($("#ctl00_cphMain_ctlBOMItems_ctlDB_ctl12_ibtnImportSrcReslt_hyp").prop("disabled",!1).css("opacity",5.5),$("#ctl00_cphMain_ctlBOMItems_ctlDB_ctl12_ibtnImportSrcReslt_hyp").removeClass("disable-click"));t.IsPoHub==!0?$("#ctl00_cphMain_ctlBOMItems_ctlDB_ctl12_ibtnImportSrcReslt_hyp").show():t.IsPoHub==!1&&$("#ctl00_cphMain_ctlBOMItems_ctlDB_ctl12_ibtnImportSrcReslt_hyp").hide();this.setFieldValue("hidDisplayStatus",$R_FN.setCleanTextValue(t.BOMStatus));this.setFieldValue("ctlCurrency",$R_FN.setCleanTextValue(t.CurrencyCode));this.setFieldValue("ctlAS9120",t.AS9120);t.UploadedBy!=""?(this.setFieldValue("ctlIsFromPrOffer",t.IsFromProspectiveOffer),this.setFieldValue("ctlPrOUploadedBy",t.UploadedBy)):($("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl13_ctlIsFromPrOffer").hide(),$("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl13_ctlPrOUploadedBy").hide());this._intCurrencyNo=t.CurrencyNo;this._stringCurrency=t.Currency_Code;this._intCurrencyNo=t.CurrencyNo;this.setDLUP(t.DLUP);this._inActive=t.InActive;this._CurrentSupplier=t.CurrentSupplier;this._QuoteRequired=t.QuoteRequired;this._blnAllItemHasSourcing=t.AllItemHasSourcing;this._UpdatedBy=t.UpdatedBy;this._isNoBidCount=t.isNoBidCount;this.setFieldValue("ctlCurrentSupplier",$R_FN.setCleanTextValue(t.CurrentSupplier));this.setFieldValue("ctlQuoteRequired",$R_FN.setCleanTextValue(t.QuoteRequired));this.setFieldValue("ctlRequestedby",$R_FN.setCleanTextValue(t.Requestedby));this.setFieldValue("ctlReleasedby",$R_FN.setCleanTextValue(t.Releasedby));this._isClosed=t.IsClosed;this.setFieldValue("ctlAssignTo",$R_FN.setCleanTextValue(t.AssignedUser));this._RequestToPOHubBy=t.RequestToPOHubBy;this._UpdateByPH=t.UpdateByPH;this._blnReqInValid=t.IsReqInValid;this._ValidMessage=t.ValidMessage;this._blnPVVBOMValidateMessage=t.PVVBOMValidateMessage;this._PVVBOMCountValid=t.PVVBOMCountValid;this.setFieldValue("ctlContact2",$R_FN.setCleanTextValue(t.Contact2Name));this.setFieldValue("hidContact2No",t.Contact2Id);this._intContact2No=t.Contact2Id;this.setFieldValue("hidReqSalesperson",t.ReqSalesPerson);this.setFieldValue("hidSupportTeamMemberNo",t.SupportTeamMemberNo);t.PurchasingNotes!=null&&t.PurchasingNotes!=""?this.setFieldValue("ctlPurchasingNotes",$R_FN.setCleanTextValue($R_FN.showYellowText(t.PurchasingNotes))):this.setFieldValue("ctlPurchasingNotes",$R_FN.setCleanTextValue(t.PurchasingNotes));this._isAddButtonEnable=!t.blnReqToPoHub&&!t.InActive;this._BomClientNo=t.ClientNo;this._IsAssignToMe=t.IsAssignToMe;this.getDataOK_End();this.onGotData();this.enableButtons(!t.InActive)},getDataError:function(n){this.showError(!0,n.get_ErrorMessage())},enableButtons:function(n){n?(this._ibtnEdit&&$R_IBTN.enableButton(this._ibtnEdit,!this._isClosed&&!this._blnPOHub&&this._IsAssignToMe==!0),this._isPurchaseHub!=!0?this._ibtnNotify&&$R_IBTN.enableButton(this._ibtnNotify,this._blnHasRequirement&&!this._blnRequestedToPoHub&&!this._inActive&&!this._isClosed&&this._IsAssignToMe==!0):this._ibtnNotify&&$R_IBTN.enableButton(this._ibtnNotify,this._blnHasRequirement&&this._blnPOHub&&this._blnRequestedToPoHub&&!this._inActive&&!this._isClosed&&this._IsAssignToMe==!0),this._ibtnExportPurchaseHUB&&$R_IBTN.enableButton(this._ibtnExportPurchaseHUB,this._blnHasRequirement&&!this._blnPOHub&&!this._blnRequestedToPoHub&&!this._inActive&&!this._isClosed&&this._IsAssignToMe==!0),this._ibtnRelease&&$R_IBTN.enableButton(this._ibtnRelease,this._blnHasRequirement&&this._blnPOHub&&!this._blnRelease&&!this._inActive&&this._blnAllItemHasSourcing&&!this._isClosed&&this._IsAssignToMe==!0),this._ibtnExportCSV&&$R_IBTN.enableButton(this._ibtnExportCSV,this._IsAssignToMe==!0),this._ibtnClose&&$R_IBTN.enableButton(this._ibtnClose,!this._isClosed&&this._IsAssignToMe==!0),this._ibtnNoBid&&$R_IBTN.enableButton(this._ibtnNoBid,this._blnHasRequirement&&this._blnPOHub&&!this._inActive&&this._isNoBidCount&&!this._isClosed&&this._IsAssignToMe==!0),this._ibtnNote&&$R_IBTN.enableButton(this._ibtnNote,this._IsAssignToMe==!0)):(this._ibtnEdit&&$R_IBTN.enableButton(this._ibtnEdit,!1),this._ibtnNotify&&$R_IBTN.enableButton(this._ibtnNotify,!1),this._ibtnExportPurchaseHUB&&$R_IBTN.enableButton(this._ibtnExportPurchaseHUB,!1),this._ibtnRelease&&$R_IBTN.enableButton(this._ibtnRelease,!1),this._ibtnClose&&$R_IBTN.enableButton(this._ibtnClose,this._isClosed))},showEditForm:function(){var n;this._BomName.includes(".xlsx")&&this._BomName.split("-").pop()!=this._BomClientNo||this._BomName.includes(".xls")&&this._BomName.split("-").pop()!=this._BomClientNo||this._BomName.includes(".csv")&&this._BomName.split("-").pop()!=this._BomClientNo?this._frmEdit.setFieldValue("ctlName",this._BomName):this._BomName.split("-").pop()==this._BomClientNo?(n=this._BomName.lastIndexOf("-"),this._frmEdit._BomName=this._BomName.substring(0,n),this._frmEdit.setFieldValue("ctlName",this._BomName.substring(0,n))):(this._frmEdit._BomName=this._BomName,this._frmEdit.setFieldValue("ctlName",this._BomName));n=this._BomName.lastIndexOf("-");this._frmEdit._intBOMID=this._intBOMID;this._frmEdit._BomCode=this._BomCode;this._frmEdit._BomCompanyName=this._BomCompanyName;this._frmEdit._BomCompanyNo=this._BomCompanyNo;this._frmEdit.setFieldValue("ctlCode",this.getFieldValue("ctlCode"));this._frmEdit.setFieldValue("ctlNotes",this.getFieldValue("ctlNotes"));this._frmEdit.setFieldValue("ctlInActive",this.getFieldValue("ctlInActive"));this._frmEdit.getFieldDropDownData("ctlCurrency");this._frmEdit.setFieldValue("ctlCurrency",this._intCurrencyNo);this._frmEdit.setFieldValue("ctlCompany",this.getFieldValue("ctlCompany"));this._frmEdit.getFieldControl("ctlContact")._intCompanyID=this.getFieldValue("hidCompanyNo");this._frmEdit.getFieldDropDownData("ctlContact");this._frmEdit.setFieldValue("ctlContact",this.getFieldValue("hidContactNo"));this._frmEdit.setFieldValue("ctlCurrentSupplier",this._CurrentSupplier);this._frmEdit.setFieldValue("ctlQuoteRequired",this._QuoteRequired);this._frmEdit.setFieldValue("ctlAS9120",this.getFieldValue("ctlAS9120"));this._frmEdit.getFieldDropDownData("ctlSalesman");this._frmEdit.setFieldValue("ctlSalesman",this._intContact2No);this._frmEdit._blnRequestedToPoHub=this._blnRequestedToPoHub;this.showForm(this._frmEdit,!0)},hideEditForm:function(){this.showForm(this._frmEdit,!1)},cancelEdit:function(){this.hideEditForm()},saveEditComplete:function(){this.hideEditForm();this.showSavedOK(!0,$R_RES.ChangesSavedSuccessfully);this.getData()},saveConfirmComplete:function(){this.hideConfirmForm();this.showSavedOK(!0,"HUBRFQ has been sent for Price Request successfully.");this.getData()},saveConfirmCloseComplete:function(){this.hideConfirmCloseForm();this.showSavedOK(!0,"HUBRFQ has been closed successfully.");this.getData()},showDeleteForm:function(){this._frmDelete._intBOMID=this._intBOMID;this._frmDelete._BomCode=this._BomCode;this._frmDelete._BomName=this._BomName;this._frmDelete._BomCompanyName=this._BomCompanyName;this._frmDelete._BomCompanyNo=this._BomCompanyNo;this._frmDelete.setFieldValue("ctlName",this.getFieldValue("ctlName"));this.showForm(this._frmDelete,!0)},showConfirmForm:function(){this._frmConfirm._intBOMID=this._intBOMID;this._frmConfirm._BomCode=this._BomCode;this._frmConfirm._BomName=this._BomName;this._frmConfirm._BomCompanyName=this._BomCompanyName;this._frmConfirm._BomCompanyNo=this._BomCompanyNo;this._frmConfirm._blnReqInValid=this._blnReqInValid;this._frmConfirm._ValidMessage=this._ValidMessage;this._frmConfirm._intContact2No=this._intContact2No;this._frmConfirm._PVVBOMCountValid=this._PVVBOMCountValid;this._frmConfirm._blnPVVBOMValidateMessage=this._blnPVVBOMValidateMessage;this.showForm(this._frmConfirm,!0)},hideConfirmForm:function(){this.showForm(this._frmConfirm,!1)},cancelConfirm:function(){this.hideConfirmForm()},hideDeleteForm:function(){this.showForm(this._frmDelete,!1)},showReleaseForm:function(){this.onCallBeforeRelease();this._blnCanReleaseAll==!0?(this.getSerialDetail(),this._frmRelease._intBOMID=this._intBOMID,this._frmRelease._BomCode=this._BomCode,this._frmRelease._BomName=this._BomName,this._frmRelease._BomCompanyName=this._BomCompanyName,this._frmRelease._BomCompanyNo=this._BomCompanyNo,this._frmRelease._UpdatedBy=this._UpdatedBy,this._frmRelease._RequestToPOHubBy=this._RequestToPOHubBy,this._frmRelease._reqSalespeson=this.getFieldValue("hidReqSalesperson"),this._frmRelease._SupportTeamMemberNo=this.getFieldValue("hidSupportTeamMemberNo"),this.showForm(this._frmRelease,!0)):$("#ctl00_cphMain_ctlMainInfo_ctlDB_BuyAndPriceMergine").hide()},getSerialDetail:function(){var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/BOMMainInfo");n.set_DataObject("BOMMainInfo");n.set_DataAction("GetAllSourcingResult");n.addParameter("Bomid",this._intBOMID);n.addDataOK(Function.createDelegate(this,this.getAllReleaseOK));n.addError(Function.createDelegate(this,this.getAllReleaseError));n.addTimeout(Function.createDelegate(this,this.getAllReleaseError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getAllReleaseOK:function(n){var u,r,t,i;if(this._UnitBuyPrice=null,this._UnitSellPrice=null,res=n._result,u=0,res.Results.length>0){for(r=null,t=null,this._blnMerginCanReleaseAll=!1,i=0;i<res.Results.length;i++)t=res.Results[i],this._UnitBuyPrice=t.UnitBuyPrice,this._UnitSellPrice=t.UnitSellPrice,this._UnitBuyPrice!=null&&this._UnitSellPrice!=null&&this._UnitBuyPrice>=this._UnitSellPrice&&(this._blnMerginCanReleaseAll=!0),t=null,r=null;this._blnMerginCanReleaseAll==!1?($("#ctl00_cphMain_ctlMainInfo_ctlDB_BuyAndPriceMergine").hide(),this._frmRelease._intBOMID=this._intBOMID,this._frmRelease._BomCode=this._BomCode,this._frmRelease._BomName=this._BomName,this._frmRelease._BomCompanyName=this._BomCompanyName,this._frmRelease._BomCompanyNo=this._BomCompanyNo,this._frmRelease._UpdatedBy=this._UpdatedBy,this._frmRelease._RequestToPOHubBy=this._RequestToPOHubBy,this._frmRelease._reqSalespeson=this.getFieldValue("hidReqSalesperson"),this._frmRelease._SupportTeamMemberNo=this.getFieldValue("hidSupportTeamMemberNo"),this.showForm(this._frmRelease,!0)):($("#ctl00_cphMain_ctlBOMItems_ctlDB_imgRefresh").trigger("click"),$("#ctl00_cphMain_ctlMainInfo_ctlDB_BuyAndPriceMergine").show(),this._frmRelease._intBOMID=this._intBOMID,this._frmRelease._BomCode=this._BomCode,this._frmRelease._BomName=this._BomName,this._frmRelease._BomCompanyName=this._BomCompanyName,this._frmRelease._BomCompanyNo=this._BomCompanyNo,this._frmRelease._UpdatedBy=this._UpdatedBy,this._frmRelease._RequestToPOHubBy=this._RequestToPOHubBy,this._frmRelease._reqSalespeson=this.getFieldValue("hidReqSalesperson"),this._frmRelease._SupportTeamMemberNo=this.getFieldValue("hidSupportTeamMemberNo"),this.showForm(this._frmRelease,!0))}else $("#ctl00_cphMain_ctlMainInfo_ctlDB_BuyAndPriceMergine").hide()},getAllReleaseError:function(){},showLoadingAllRelease:function(){},showAllReleaseError:function(){},showNoBidForm:function(){this._blnCanNoBidAll&&(this._frmNoBid._intBOMID=this._intBOMID,this._frmNoBid._BomCode=this._BomCode,this._frmNoBid._BomName=this._BomName,this._frmNoBid._BomCompanyName=this._BomCompanyName,this._frmNoBid._BomCompanyNo=this._BomCompanyNo,this._frmNoBid._UpdatedBy=this._UpdatedBy,this._frmNoBid.setFieldValue("ctlNotes",""),this._frmNoBid._SalesmanNo=this.getFieldValue("hidReqSalesperson"),this.showForm(this._frmNoBid,!0))},saveNoBidComplete:function(){this.hideNoBidForm();this.showSavedOK(!0,$R_RES.ChangesSavedSuccessfully);this.getData()},hideNoBidForm:function(){this.showForm(this._frmNoBid,!1)},cancelNoBid:function(){this.hideNoBidForm()},showConfirmCloseForm:function(){this._frmConfirmClose._intBOMID=this._intBOMID;this.showForm(this._frmConfirmClose,!0)},hideConfirmCloseForm:function(){this.showForm(this._frmConfirmClose,!1)},cancelConfirmClose:function(){this.hideConfirmCloseForm()},saveReleaseComplete:function(){this.hideReleaseForm();this.showSavedOK(!0,$R_RES.ChangesSavedSuccessfully);this.getData()},hideReleaseForm:function(){this.showForm(this._frmRelease,!1)},cancelRelease:function(){this.hideReleaseForm()},cancelDelete:function(){this.hideDeleteForm()},showExportCSV:function(){this.getData_Start();var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/BOMItems");n.set_DataObject("BOMItems");n.set_DataAction("ExportToCSV");n.addParameter("id",this._intBOMID);n.addParameter("currency_Code",this._stringCurrency);n.addDataOK(Function.createDelegate(this,this.exportCSV_OK));n.addError(Function.createDelegate(this,this.exportCSV_Error));n.addTimeout(Function.createDelegate(this,this.exportCSV_Error));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},exportCSV_OK:function(n){var i=n._result,t;i.Filename&&(t=new Date,location.href=String.format("{0}?t={1}",i.Filename,t.getTime()),t=null);this.getDataOK_End()},exportCSV_Error:function(n){this.showError(!0,n.get_ErrorMessage())},saveDeleteComplete:function(){$R_FN.navigateBack()},savePurchaseHUBData:function(){this.getData_Start();var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/BOMMainInfo");n.set_DataObject("BOMMainInfo");n.set_DataAction("savePurchaseHUBData");n.addParameter("id",this._intBOMID);n.addParameter("BomCode",this._BomCode);n.addParameter("BomName",this._BomName);n.addParameter("BomCompanyName",this._BomCompanyName);n.addParameter("BomCompanyNo",this._BomCompanyNo);n.addDataOK(Function.createDelegate(this,this.getDataOK));n.addError(Function.createDelegate(this,this.getDataError));n.addTimeout(Function.createDelegate(this,this.getDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},showNotifyForm:function(){this._frmNotify._intBOMID=this._intBOMID;this._frmNotify._BomCode=this._BomCode;this._frmNotify._BomName=this._BomName;this._frmNotify._BomCompanyName=this._BomCompanyName;this._frmNotify._BomCompanyNo=this._BomCompanyNo;this._frmNotify._intCompanyID=this.getFieldValue("hidCompanyNo");this._frmNotify._stringCurrency=this._stringCurrency;this.showForm(this._frmNotify,!0)},hideNotifyForm:function(){this.showForm(this._frmNotify,!1)},cancelNotifyForm:function(){this.showForm(this._frmNotify,!1);this.showContent(!0)},saveNotifyComplete:function(){this.showForm(this._frmNotify,!1);this.showSavedOK(!0,"BOM notification sent successfully")},disableNotifyAndExportButton:function(){},showExpediteNoteForm:function(){this._frmAddExpediteNote._intBOMID=this._intBOMID;this._frmAddExpediteNote._HUBRFQName=this.getFieldValue("ctlName");this._frmAddExpediteNote._intRequestedby=this._RequestToPOHubBy;this._frmAddExpediteNote._intUpdateByPH=this._UpdateByPH;this._frmAddExpediteNote._HubrfqCode=this.getFieldValue("ctlCode");this._frmAddExpediteNote._CompanyNo=this.getFieldValue("hidCompanyNo");this._frmAddExpediteNote.setFieldValue("ctlExpediteNotes","");this._frmAddExpediteNote._intContact2No=this._intContact2No;this._frmAddExpediteNote._companyname=this.getFieldValue("ctlCompany");this._frmAddExpediteNote._contactname=this.getFieldValue("ctlContact");this._frmAddExpediteNote._reqSalespeson=this.getFieldValue("hidReqSalesperson");this.showForm(this._frmAddExpediteNote,!0)},saveAddExpediteNoteComplete:function(){this.showForm(this._frmAddExpediteNote,!1);this.showSavedOK(!0,$R_RES.ChangesSavedSuccessfully);this.getData()},cancelAddExpediteNoteForm:function(){this.showForm(this._frmAddExpediteNote,!1);this.showContent(!0)},enableDisableReleaseButton:function(){},getValidation:function(){this.getData_Start();var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/BOMMainInfo");n.set_DataObject("BOMMainInfo");n.set_DataAction("GetData");n.addParameter("id",this._intBOMID);n.addDataOK(Function.createDelegate(this,this.getValidationOK));n.addError(Function.createDelegate(this,this.getValidationError));n.addTimeout(Function.createDelegate(this,this.getValidationError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getValidationOK:function(n){var t=n._result;this._blnReqInValid=t.IsReqInValid;this._ValidMessage=t.ValidMessage;this._blnPVVBOMValidateMessage=t.PVVBOMValidateMessage;this._PVVBOMCountValid=t.PVVBOMCountValid;this.getDataOK_End();this.onGotData();this.showConfirmForm()},getValidationError:function(n){this.showError(!0,n.get_ErrorMessage())},OpenDocTree:function(){$R_FN.openDocumentTree(this._intBOMID,"BOM",this._BomName)},OpenCrossMatch:function(){$R_FN.openCrossMatch(this._intBOMID,this._BomCode)}};Rebound.GlobalTrader.Site.Controls.Nuggets.BOMMainInfo.registerClass("Rebound.GlobalTrader.Site.Controls.Nuggets.BOMMainInfo",Rebound.GlobalTrader.Site.Controls.Nuggets.Base);