﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Add" xml:space="preserve">
    <value>Add</value>
  </data>
  <data name="AddAlternate" xml:space="preserve">
    <value>Add Alternate</value>
  </data>
  <data name="AddAllAlternate" xml:space="preserve">
    <value>Add All Alternate</value>
  </data>
  <data name="AddContact" xml:space="preserve">
    <value>Add Contact</value>
  </data>
  <data name="AddFolder" xml:space="preserve">
    <value>New Folder</value>
  </data>
  <data name="AddNew" xml:space="preserve">
    <value>Add New</value>
  </data>
  <data name="AddScheduledCall" xml:space="preserve">
    <value>Add Scheduled Call</value>
  </data>
  <data name="AddSuggestion" xml:space="preserve">
    <value>Add Suggestion</value>
  </data>
  <data name="AddToRequirement" xml:space="preserve">
    <value>Add To Requirement</value>
  </data>
  <data name="AllCompanies" xml:space="preserve">
    <value>All Companies</value>
  </data>
  <data name="Allocate" xml:space="preserve">
    <value>Allocate</value>
  </data>
  <data name="Apply" xml:space="preserve">
    <value>Apply</value>
  </data>
  <data name="Authorise" xml:space="preserve">
    <value>Authorise</value>
  </data>
  <data name="Browse" xml:space="preserve">
    <value>Browse</value>
  </data>
  <data name="Calculate" xml:space="preserve">
    <value>Calculate</value>
  </data>
  <data name="Cancel" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="Cease" xml:space="preserve">
    <value>Cease</value>
  </data>
  <data name="ChangeCompany" xml:space="preserve">
    <value>Change Company</value>
  </data>
  <data name="ChangePassword" xml:space="preserve">
    <value>Change Password</value>
  </data>
  <data name="Close" xml:space="preserve">
    <value>Close</value>
  </data>
  <data name="Company" xml:space="preserve">
    <value>Company</value>
  </data>
  <data name="CompanyContacts" xml:space="preserve">
    <value>Contacts</value>
  </data>
  <data name="Contacts" xml:space="preserve">
    <value>Contacts</value>
  </data>
  <data name="Continue" xml:space="preserve">
    <value>Continue</value>
  </data>
  <data name="CreatePDF" xml:space="preserve">
    <value>Create PDF</value>
  </data>
  <data name="ViewPDF" xml:space="preserve">
    <value>View saved PDF</value>
  </data>
  <data name="CreateSalesOrder" xml:space="preserve">
    <value>Create SO</value>
  </data>
  <data name="Customers" xml:space="preserve">
    <value>Customers</value>
  </data>
  <data name="Data" xml:space="preserve">
    <value>Data</value>
  </data>
  <data name="Deallocate" xml:space="preserve">
    <value>Deallocate</value>
  </data>
  <data name="Deauthorise" xml:space="preserve">
    <value>Deauthorise</value>
  </data>
  <data name="Default" xml:space="preserve">
    <value>Make Default</value>
  </data>
  <data name="DefaultBilling" xml:space="preserve">
    <value>Make Default Billing</value>
  </data>
  <data name="DefaultShipping" xml:space="preserve">
    <value>Make Default Shipping</value>
  </data>
  <data name="Delete" xml:space="preserve">
    <value>Delete</value>
  </data>
  <data name="DeleteAlternate" xml:space="preserve">
    <value>Delete Alternate</value>
  </data>
  <data name="DeleteFolder" xml:space="preserve">
    <value>Delete Folder</value>
  </data>
  <data name="DeleteMessage" xml:space="preserve">
    <value>Delete Message(s)</value>
  </data>
  <data name="DeleteReceipt" xml:space="preserve">
    <value>Delete Receipt</value>
  </data>
  <data name="DeleteUnallocatedService" xml:space="preserve">
    <value>Delete Unallocated Service</value>
  </data>
  <data name="DeleteUnallocatedServices" xml:space="preserve">
    <value>Delete Unallocated Services</value>
  </data>
  <data name="DeleteUnallocatedStock" xml:space="preserve">
    <value>Delete Unallocated Stock</value>
  </data>
  <data name="Deselect" xml:space="preserve">
    <value>Deselect</value>
  </data>
  <data name="Disable" xml:space="preserve">
    <value>Disable</value>
  </data>
  <data name="Dismiss" xml:space="preserve">
    <value>Dismiss</value>
  </data>
  <data name="Edit" xml:space="preserve">
    <value>Edit</value>
  </data>
  <data name="EditFolder" xml:space="preserve">
    <value>Edit Folder</value>
  </data>
  <data name="EditMembers" xml:space="preserve">
    <value>Edit Members</value>
  </data>
  <data name="EditOffer" xml:space="preserve">
    <value>Edit Offer</value>
  </data>
  <data name="EditAltPart" xml:space="preserve">
    <value>Edit Alternative Part</value>
  </data>
  <data name="EditCurrentRates" xml:space="preserve">
    <value>Edit Current Rates</value>
  </data>
  <data name="EditReceipt" xml:space="preserve">
    <value>Edit Receipt</value>
  </data>
  <data name="Enable" xml:space="preserve">
    <value>Enable</value>
  </data>
  <data name="Forward" xml:space="preserve">
    <value>Forward</value>
  </data>
  <data name="GetCounts" xml:space="preserve">
    <value>Get Counts</value>
  </data>
  <data name="GetData" xml:space="preserve">
    <value>Get Data</value>
  </data>
  <data name="Go" xml:space="preserve">
    <value>Go</value>
  </data>
  <data name="Help" xml:space="preserve">
    <value>Help</value>
  </data>
  <data name="Home" xml:space="preserve">
    <value>Home</value>
  </data>
  <data name="Inspect" xml:space="preserve">
    <value>Physical Inspect</value>
  </data>
  <data name="Login" xml:space="preserve">
    <value>Login</value>
  </data>
  <data name="Logout" xml:space="preserve">
    <value>Logout</value>
  </data>
  <data name="MakeAvailable" xml:space="preserve">
    <value>Release from Quarantine</value>
  </data>
  <data name="MakeDefaultBilling" xml:space="preserve">
    <value>Make Default Billing</value>
  </data>
  <data name="MakeDefaultPO" xml:space="preserve">
    <value>Make Default for POs</value>
  </data>
  <data name="MakeDefaultShipping" xml:space="preserve">
    <value>Make Default Shipping</value>
  </data>
  <data name="MakeDefaultSO" xml:space="preserve">
    <value>Make Default for SOs</value>
  </data>
  <data name="Manufacturers" xml:space="preserve">
    <value>Manufacturers</value>
  </data>
  <data name="MarkAsToDo" xml:space="preserve">
    <value>Create To Do Item</value>
  </data>
  <data name="MarkComplete" xml:space="preserve">
    <value>Mark Complete</value>
  </data>
  <data name="MarkIncomplete" xml:space="preserve">
    <value>Mark Incomplete</value>
  </data>
  <data name="MoveMessage" xml:space="preserve">
    <value>Move Message(s)</value>
  </data>
  <data name="New" xml:space="preserve">
    <value>New</value>
  </data>
  <data name="NewFolder" xml:space="preserve">
    <value>New Folder</value>
  </data>
  <data name="NewMessage" xml:space="preserve">
    <value>New Message</value>
  </data>
  <data name="No" xml:space="preserve">
    <value>No</value>
  </data>
  <data name="Notify" xml:space="preserve">
    <value>Notify</value>
  </data>
  <data name="Off" xml:space="preserve">
    <value>Off</value>
  </data>
  <data name="OK" xml:space="preserve">
    <value>OK</value>
  </data>
  <data name="OpenItem" xml:space="preserve">
    <value>Open Item</value>
  </data>
  <data name="Orders" xml:space="preserve">
    <value>Orders</value>
  </data>
  <data name="Post" xml:space="preserve">
    <value>Post</value>
  </data>
  <data name="Print" xml:space="preserve">
    <value>Print</value>
  </data>
  <data name="Prospects" xml:space="preserve">
    <value>Prospects</value>
  </data>
  <data name="Quarantine" xml:space="preserve">
    <value>Quarantine</value>
  </data>
  <data name="QuickAdd" xml:space="preserve">
    <value>QuickAdd</value>
  </data>
  <data name="Quote" xml:space="preserve">
    <value>Quote</value>
  </data>
  <data name="Receive" xml:space="preserve">
    <value>Receive</value>
  </data>
  <data name="ReceiveSelected" xml:space="preserve">
    <value>Receive Selected</value>
  </data>
  <data name="ReplaceImage" xml:space="preserve">
    <value>Replace Image</value>
  </data>
  <data name="Reply" xml:space="preserve">
    <value>Reply</value>
  </data>
  <data name="Report" xml:space="preserve">
    <value>Report</value>
  </data>
  <data name="RequestForQuote" xml:space="preserve">
    <value>Request For Quote</value>
  </data>
  <data name="Reset" xml:space="preserve">
    <value>Reset</value>
  </data>
  <data name="ResetPassword" xml:space="preserve">
    <value>Reset Password</value>
  </data>
  <data name="RunReport" xml:space="preserve">
    <value>Run Report</value>
  </data>
  <data name="Save" xml:space="preserve">
    <value>Save</value>
  </data>
  <data name="SaveAsDefault" xml:space="preserve">
    <value>Save as Default</value>
  </data>
  <data name="Search" xml:space="preserve">
    <value>Search</value>
  </data>
  <data name="Select" xml:space="preserve">
    <value>Select</value>
  </data>
  <data name="SelectAllAvailableForShipping" xml:space="preserve">
    <value>Select All Available For Shipping</value>
  </data>
  <data name="Send" xml:space="preserve">
    <value>Send</value>
  </data>
  <data name="Ship" xml:space="preserve">
    <value>Ship</value>
  </data>
  <data name="ShipAll" xml:space="preserve">
    <value>Ship All</value>
  </data>
  <data name="ShipSelected" xml:space="preserve">
    <value>Ship Selected</value>
  </data>
  <data name="Show" xml:space="preserve">
    <value>Show</value>
  </data>
  <data name="Snooze" xml:space="preserve">
    <value>Snooze</value>
  </data>
  <data name="Source" xml:space="preserve">
    <value>Source</value>
  </data>
  <data name="Split" xml:space="preserve">
    <value>Split</value>
  </data>
  <data name="Suppliers" xml:space="preserve">
    <value>Suppliers</value>
  </data>
  <data name="Transactions" xml:space="preserve">
    <value>Transactions</value>
  </data>
  <data name="TransferService" xml:space="preserve">
    <value>Transfer Service</value>
  </data>
  <data name="TransferServicesToDifferentLot" xml:space="preserve">
    <value>Transfer Services to Different Lot</value>
  </data>
  <data name="TransferStock" xml:space="preserve">
    <value>Transfer Stock</value>
  </data>
  <data name="TransferStockToDifferentLot" xml:space="preserve">
    <value>Transfer Stock to Different Lot</value>
  </data>
  <data name="Unpost" xml:space="preserve">
    <value>Unpost</value>
  </data>
  <data name="Update" xml:space="preserve">
    <value>Update</value>
  </data>
  <data name="Yes" xml:space="preserve">
    <value>Yes</value>
  </data>
  <data name="ExportToCSV" xml:space="preserve">
    <value>Export to CSV</value>
  </data>
  <data name="PrintAll" xml:space="preserve">
    <value>Print All</value>
  </data>
  <data name="PostAll" xml:space="preserve">
    <value>Post All</value>
  </data>
  <data name="UnpostAll" xml:space="preserve">
    <value>Unpost All</value>
  </data>
  <data name="CollapseAll" xml:space="preserve">
    <value>Collapse All</value>
  </data>
  <data name="ExpandAll" xml:space="preserve">
    <value>Expand All</value>
  </data>
  <data name="PrintProForma" xml:space="preserve">
    <value>ProForma</value>
  </data>
  <data name="CreatePurchaseOrder" xml:space="preserve">
    <value>Create PO</value>
  </data>
  <data name="Clone" xml:space="preserve">
    <value>Clone</value>
  </data>
  <data name="TransferAccounts" xml:space="preserve">
    <value>Transfer Accounts</value>
  </data>
  <data name="Approve" xml:space="preserve">
    <value>Approve</value>
  </data>
  <data name="Disapprove" xml:space="preserve">
    <value>Unapprove</value>
  </data>
  <data name="Export" xml:space="preserve">
    <value>Export</value>
  </data>
  <data name="Release" xml:space="preserve">
    <value>Release</value>
  </data>
  <data name="ClearDefault" xml:space="preserve">
    <value>Clear Default</value>
  </data>
  <data name="DeleteFutureRate" xml:space="preserve">
    <value>Delete Future Rate</value>
  </data>
  <data name="EditRates" xml:space="preserve">
    <value>Edit Rates</value>
  </data>
  <data name="ViewImage" xml:space="preserve">
    <value>View Image</value>
  </data>
  <data name="PrintLabel" xml:space="preserve">
    <value>Print Label</value>
  </data>
  <data name="Checked" xml:space="preserve">
    <value>Checked</value>
  </data>
  <data name="UnChecked" xml:space="preserve">
    <value>Unchecked</value>
  </data>
  <data name="PrintNiceLabel" xml:space="preserve">
    <value>Print Label</value>
  </data>
  <data name="ReportNPR" xml:space="preserve">
    <value>Report NPR</value>
  </data>
  <data name="Email" xml:space="preserve">
    <value>Email</value>
  </data>
  <data name="MakeDefaultPOLedger" xml:space="preserve">
    <value>Make Default PO Ledger</value>
  </data>
  <data name="MakeDefaultSOLedger" xml:space="preserve">
    <value>Make Default SO Ledger</value>
  </data>
  <data name="EditBankFee" xml:space="preserve">
    <value>Edit Bank Charge Fee</value>
  </data>
  <data name="AddStockInfo" xml:space="preserve">
    <value>Add Sourcing Info</value>
  </data>
  <data name="AddOffer" xml:space="preserve">
    <value>Add Offer</value>
  </data>
  <data name="AddTrusted" xml:space="preserve">
    <value>Add Trusted</value>
  </data>
  <data name="More" xml:space="preserve">
    <value>Further 6 Months</value>
  </data>
  <data name="NPRPrintStatus" xml:space="preserve">
    <value>Edit NPR Status</value>
  </data>
  <data name="PhysicalInspect" xml:space="preserve">
    <value>Inspect</value>
  </data>
  <data name="StockProvision" xml:space="preserve">
    <value>Stock Provision</value>
  </data>
  <data name="Calculate_StckP" xml:space="preserve">
    <value>Calculate</value>
  </data>
  <data name="EPR" xml:space="preserve">
    <value>EPR</value>
  </data>
  <data name="EditShippingInfo" xml:space="preserve">
    <value>Edit Shipping Info</value>
  </data>
  <data name="Arrow" xml:space="preserve">
    <value />
  </data>
  <data name="PrintEnqForm" xml:space="preserve">
    <value>Print Enquiry Form</value>
  </data>
  <data name="AddExpediteNote" xml:space="preserve">
    <value>Add Expedite Note</value>
  </data>
  <data name="ForgotPassword" xml:space="preserve">
    <value>Send email</value>
  </data>
  <data name="FWDPassword" xml:space="preserve">
    <value>Forgotten login details</value>
  </data>
  <data name="ForgotUserName" xml:space="preserve">
    <value>Send email</value>
  </data>
  <data name="Warehouse" xml:space="preserve">
    <value>Create IPO</value>
  </data>
  <data name="ContinueAfterSelectWarehouse" xml:space="preserve">
    <value>Save</value>
  </data>
  <data name="ExportToPurchaseHUB" xml:space="preserve">
    <value>Send to Purchase Hub</value>
  </data>
  <data name="Calculator" xml:space="preserve">
    <value>Sales Calculator</value>
  </data>
  <data name="ReleaseAll" xml:space="preserve">
    <value>Release All</value>
  </data>
  <data name="UnRelease" xml:space="preserve">
    <value>ReCall</value>
  </data>
  <data name="AssignToMe" xml:space="preserve">
    <value>Assign</value>
  </data>
  <data name="SendToSupplier" xml:space="preserve">
    <value>Send To Supplier</value>
  </data>
  <data name="EditTrust" xml:space="preserve">
    <value>Edit Trust</value>
  </data>
  <data name="CreateCreditNoteForPOHUB" xml:space="preserve">
    <value>Add Credit Note For POHUB</value>
  </data>
  <data name="POR" xml:space="preserve">
    <value>Print POR</value>
  </data>
  <data name="CreateClone" xml:space="preserve">
    <value>Clone this line</value>
  </data>
  <data name="NoBid" xml:space="preserve">
    <value>No-Bid</value>
  </data>
  <data name="NoBidAll" xml:space="preserve">
    <value>No-Bid All</value>
  </data>
  <data name="RecallNoBid" xml:space="preserve">
    <value>Recall No-Bid</value>
  </data>
  <data name="PrintPreview" xml:space="preserve">
    <value>Print Preview</value>
  </data>
  <data name="PrintLabelForNiceWatch" xml:space="preserve">
    <value>Print Nice Label</value>
  </data>
  <data name="CloneAddToRequirement" xml:space="preserve">
    <value>Clone this line and add to Requirement</value>
  </data>
  <data name="Back" xml:space="preserve">
    <value>Back</value>
  </data>
  <data name="ResetConsolidate" xml:space="preserve">
    <value>Reset current print setting</value>
  </data>
  <data name="AddNewHUBFRQNote" xml:space="preserve">
    <value>Add New Communication Note</value>
  </data>
  <data name="RecordSerialNo" xml:space="preserve">
    <value>Add/Edit Serial No</value>
  </data>
  <data name="CompleteSave" xml:space="preserve">
    <value>Complete &amp; Save</value>
  </data>
  <data name="SelectAll" xml:space="preserve">
    <value>Select All</value>
  </data>
  <data name="All" xml:space="preserve">
    <value> Add All</value>
  </data>
  <data name="Cancelupdate" xml:space="preserve">
    <value>Cancel update</value>
  </data>
  <data name="InActive" xml:space="preserve">
    <value>Make contact Inactive</value>
  </data>
  <data name="SentOrder" xml:space="preserve">
    <value>Confirm SO sent</value>
  </data>
  <data name="InternalLog" xml:space="preserve">
    <value>Internal Log</value>
  </data>
  <data name="Confirm" xml:space="preserve">
    <value>Confirm</value>
  </data>
  <data name="ConfirmAll" xml:space="preserve">
    <value>Confirm All</value>
  </data>
  <data name="ReleaseLine" xml:space="preserve">
    <value>Release</value>
  </data>
  <data name="UnReleaseLine" xml:space="preserve">
    <value>UnRelease </value>
  </data>
  <data name="PayByCreditCard" xml:space="preserve">
    <value>Pay By Credit Card</value>
  </data>
  <data name="MarkFirmAlt" xml:space="preserve">
    <value>Mark as Firm Alternate</value>
  </data>
  <data name="ExportToExcel" xml:space="preserve">
    <value>Export To Excel</value>
  </data>
  <data name="RefGrid" xml:space="preserve">
    <value>Refresh to load data</value>
  </data>
  <data name="ShowRelDocument" xml:space="preserve">
    <value>View</value>
  </data>
  <data name="ViewTree" xml:space="preserve">
    <value>View Tree</value>
  </data>
  <data name="AddLineItem" xml:space="preserve">
    <value>Import Data</value>
  </data>
  <data name="AddToHUBRFQ" xml:space="preserve">
    <value>Add to HUBRFQ</value>
  </data>
  <data name="SaveasHUBRFQ" xml:space="preserve">
    <value>Save as HUBRFQ</value>
  </data>
  <data name="MarkasComplete" xml:space="preserve">
    <value>Mark Complete</value>
  </data>
  <data name="AddUsers" xml:space="preserve">
    <value>Add Users</value>
  </data>
  <data name="GenerateLogin" xml:space="preserve">
    <value>GenerateLogin</value>
  </data>
  <data name="StockImportTool" xml:space="preserve">
    <value>Offers Import Tool</value>
  </data>
  <data name="EditAll" xml:space="preserve">
    <value>Edit-All</value>
  </data>
  <data name="SplitGI" xml:space="preserve">
    <value>Split GI Line</value>
  </data>
  <data name="OnHold" xml:space="preserve">
    <value>Hold Export</value>
  </data>
  <data name="UnHoldInvoice" xml:space="preserve">
    <value>Un Hold Export</value>
  </data>
  <data name="CrossMatch" xml:space="preserve">
    <value>Cross Match</value>
  </data>
  <data name="StockImportEPOTool" xml:space="preserve">
    <value>Import to Sourcing Strategic Offers</value>
  </data>
  <data name="EditEpo" xml:space="preserve">
    <value>Edit Strategic Offers</value>
  </data>
  <data name="DeleteAlternatePart" xml:space="preserve">
    <value>Delete Alternate Part</value>
  </data>
  <data name="ImportSourcingResult" xml:space="preserve">
    <value>Import Sourcing Result</value>
  </data>
  <data name="EditinfoAfterRelease" xml:space="preserve">
    <value>Edit info - After Release</value>
  </data>
  <data name="CloneAndAddHUBRFQ" xml:space="preserve">
    <value>Clone &amp; Add HUBRFQ</value>
  </data>
  <data name="CloneAndSendHUB" xml:space="preserve">
    <value>Clone &amp; Send HUB</value>
  </data>
  <data name="AddShortShipment" xml:space="preserve">
    <value>Short Shipment</value>
  </data>
  <data name="DeletePartWatchMatch" xml:space="preserve">
    <value>Delete HUB Partwatch</value>
  </data>
  <data name="LineManagerApproval" xml:space="preserve">
    <value>Line Manager Approve</value>
  </data>
  <data name="QualityApproval" xml:space="preserve">
    <value>Quality Approve</value>
  </data>
  <data name="QualityDecline" xml:space="preserve">
    <value>Quality Decline</value>
  </data>
  <data name="LineManagerDecline" xml:space="preserve">
    <value>Line Manager Decline</value>
  </data>
  <data name="LineManagerIndp" xml:space="preserve">
    <value>Approve Independent Testing Recommended</value>
  </data>
  <data name="SAUpload" xml:space="preserve">
    <value>+</value>
  </data>
  <data name="NPR" xml:space="preserve">
    <value>NPR</value>
  </data>
  <data name="GIImage" xml:space="preserve">
    <value>Click Here to Add Photos or View</value>
  </data>
  <data name="GIButtons" xml:space="preserve">
    <value>Section</value>
  </data>
  <data name="RecordLotNo" xml:space="preserve">
    <value>Add/Edit Lot Code</value>
  </data>
  <data name="ExportPDFWord" xml:space="preserve">
    <value>Download</value>
  </data>
  <data name="CancelShortShipment" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="CloseShortShipment" xml:space="preserve">
    <value>Close</value>
  </data>
  <data name="SaveExit" xml:space="preserve">
    <value>Save and exit</value>
  </data>
  <data name="EmptyShortShipment" xml:space="preserve">
    <value>Empty Shipment</value>
  </data>
  <data name="Draft" xml:space="preserve">
    <value>Draft</value>
  </data>
  <data name="SATermAndCondetion" xml:space="preserve">
    <value>General Terms and Condition of purchase.pdf</value>
  </data>
  <data name="QualityEscalate" xml:space="preserve">
    <value>Escalate</value>
  </data>
  <data name="SAEmailLog" xml:space="preserve">
    <value>Email Log</value>
  </data>
  <data name="GIPrintBtn" xml:space="preserve">
    <value>Print</value>
  </data>
  <data name="GiQueryMessage" xml:space="preserve">
    <value>Query</value>
  </data>
  <data name="EndQuarantine" xml:space="preserve">
    <value>End Quarantine</value>
  </data>
  <data name="ReleaseStock" xml:space="preserve">
    <value>Release Stock</value>
  </data>
  <data name="QuarantineProduct" xml:space="preserve">
    <value>Quarantine</value>
  </data>
  <data name="saBtnExit" xml:space="preserve">
    <value>Exit</value>
  </data>
  <data name="SaveAndExit" xml:space="preserve">
    <value>Save and exit</value>
  </data>
  <data name="SendQuery" xml:space="preserve">
    <value>Send query</value>
  </data>
  <data name="SaveAndRelease" xml:space="preserve">
    <value>Save and Release</value>
  </data>
  <data name="SaveAndSend" xml:space="preserve">
    <value>Save and Send</value>
  </data>
  <data name="saBtnNotify" xml:space="preserve">
    <value>Notify</value>
  </data>
  <data name="AddTask" xml:space="preserve">
    <value>Add Task</value>
  </data>
  <data name="ViewTask" xml:space="preserve">
    <value>View Task</value>
  </data>
  <data name="GT" xml:space="preserve">
    <value>GT</value>
  </data>
  <data name="SendPowerSOR" xml:space="preserve">
    <value>Request Approval</value>
  </data>
  <data name="Map" xml:space="preserve">
    <value>Map product to category</value>
  </data>
  <data name="PrintEnqEccnForm" xml:space="preserve">
    <value>ECCN Log</value>
  </data>
  <data name="ShowStockAvailable" xml:space="preserve">
    <value>Show Stock As Available</value>
  </data>
  <data name="EI_FetchSlot" xml:space="preserve">
    <value>Book Slot</value>
  </data>
  <data name="SIGISave" xml:space="preserve">
    <value>Save</value>
  </data>
  <data name="GISplitSave" xml:space="preserve">
    <value>Save Split</value>
  </data>
  <data name="ApplyPartwatch" xml:space="preserve">
    <value>Apply Partwatch</value>
  </data>
  <data name="RemovePartwatch" xml:space="preserve">
    <value>Remove Partwatch</value>
  </data>
  <data name="Approval" xml:space="preserve">
    <value>Approval</value>
  </data>
  <data name="Reject" xml:space="preserve">
    <value>Reject</value>
  </data>
  <data name="OGELEUUForm" xml:space="preserve">
    <value>EUU Form.pdf</value>
  </data>
  <data name="EditReverseLogistic" xml:space="preserve">
    <value>Edit Reverse Logistic</value>
  </data>
  <data name="EditExportApproval" xml:space="preserve">
    <value>Edit Export Approval</value>
  </data>
  <data name="EditAllExportApproval" xml:space="preserve">
    <value>Edit All Export Approval</value>
  </data>
  <data name="EditAll_Eccn" xml:space="preserve">
    <value>Edit All</value>
  </data>
  <data name="DownloadEEUFormTemplate" xml:space="preserve">
    <value>Download EUU Form Template</value>
  </data>
  <data name="DeleteClientPartWatch" xml:space="preserve">
    <value>Delete PartWatch</value>
  </data>
  <data name="GI_CloseInspection" xml:space="preserve">
    <value>Complete Inspection</value>
  </data>
  <data name="GI_StartInspection" xml:space="preserve">
    <value>Start Inspection</value>
  </data>
  <data name="checkSupplierMf" xml:space="preserve">
    <value>Check Supplier / Manufacturer Data</value>
  </data>
  <data name="BomMapping" xml:space="preserve">
    <value>BomMapping</value>
  </data>
  <data name="SupplierMapping" xml:space="preserve">
    <value>SupplierMapping</value>
  </data>
  <data name="AddStockRoutingCard" xml:space="preserve">
    <value>Stock Routing Card</value>
  </data>
  <data name="ReportSTO" xml:space="preserve">
    <value>STO</value>
  </data>
  <data name="GSAManageMember" xml:space="preserve">
    <value>Manage Members</value>
  </data>
  <data name="Assign" xml:space="preserve">
    <value>Assign</value>
  </data>
  <data name="ReadyToShip" xml:space="preserve">
    <value>Ready To Ship</value>
  </data>
  <data name="AssignLead" xml:space="preserve">
    <value>Assign Lead</value>
  </data>
  <data name="EditReverseLogisticsBulk" xml:space="preserve">
    <value>Bulk Edit</value>
  </data>
  <data name="SearchCompany" xml:space="preserve">
    <value>Go</value>
  </data>
  <data name="Link Account" xml:space="preserve">
    <value>Link Account</value>
  </data>
  <data name="LinkAccount" xml:space="preserve">
    <value>Link/Unlink</value>
  </data>
  <data name="ReverseLogisticBulkEditHistory" xml:space="preserve">
    <value>Bulk Edit History</value>
  </data>
  <data name="Header" xml:space="preserve">
    <value>Manage Header</value>
  </data>
  <data name="Upload" xml:space="preserve">
    <value>Upload</value>
  </data>
  <data name="HeaderDelete" xml:space="preserve">
    <value>Remove Header</value>
  </data>
  <data name="View" xml:space="preserve">
    <value>View</value>
  </data>
  <data name="CreditApplicationForm" xml:space="preserve">
    <value>New Account/ Credit Application Form</value>
  </data>
  <data name="AddEdit" xml:space="preserve">
    <value>Add/Edit</value>
  </data>
  <data name="SaveForExport" xml:space="preserve">
    <value>Export</value>
  </data>
  <data name="EditBulkEpo" xml:space="preserve">
    <value>Bulk Edit</value>
  </data>
  <data name="BulkActivate" xml:space="preserve">
    <value>Inactivate/ Bulk Inactivate</value>
  </data>
  <data name="BulkAdd" xml:space="preserve">
    <value>Add/ Bulk Add</value>
  </data>
  <data name="BulkEdit" xml:space="preserve">
    <value>Edit/ Bulk Edit</value>
  </data>
  <data name="BulkEditLog" xml:space="preserve">
    <value>Bulk Edit Log</value>
  </data>
  <data name="ActivateCompany" xml:space="preserve">
    <value>Activate Company</value>
  </data>
  <data name="InactivateCompany" xml:space="preserve">
    <value>Inactivate Company</value>
  </data>
  <data name="ViewLogSubscription" xml:space="preserve">
    <value>View Email Campaign Subscription Logs</value>
  </data>
  <data name="MarkFranchised" xml:space="preserve">
    <value>Mark Franchised</value>
  </data>
  <data name="MarkAllFranchised" xml:space="preserve">
    <value>Mark All Franchised</value>
  </data>
</root>