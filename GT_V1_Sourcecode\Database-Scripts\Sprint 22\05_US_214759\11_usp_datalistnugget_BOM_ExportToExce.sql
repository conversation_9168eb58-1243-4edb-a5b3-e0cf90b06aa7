﻿
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO
/* 
====================================================================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-214760]		Phuc Hoang			08-Jan-2025		CREATE			Overhaul of BOM Import tool - Export to Excel on HUBRFQ (Part 1)
====================================================================================================================================
*/

CREATE OR ALTER PROCEDURE [dbo].[usp_datalistnugget_BOM_ExportToExcel] 
(                                                                                      
   @ClientId INT 
  ,@BOMId INT = NULL 
)
AS 

BEGIN 
	SELECT 
	bom.BOMName AS 'HUBRFQ No' 
	, cr.CustomerRequirementNumber AS REQUIREMENT 
	, bom.ClientNo AS 'CLIENT NO.' 
	, cr.Part AS 'REQ. PART' 
	, pr.ProductName AS Product 
	, mfr.ManufacturerName AS 'REQ. MANUFACTURER' 
	, cr.Quantity AS 'REQ. QTY' 
	, '' AS 'CUSTOMER REF NO.' 
	, '' AS '*SUPPLIER NAME' 
	, '' AS '*SUPPLIER PART NO.' 
	, '' AS '*Supplier Cost' 
	, '' AS 'ROHS' 
	, '' AS '*MANUFACTURER' 
	, '' AS 'DATE CODE' 
	, '' AS 'PACKAGE' 
	, '' AS '*OFFERED QTY.' 
	, '' AS 'OFFER STATUS' 
	, '' AS 'SPQ' 
	, '' AS 'FACTORY SEALED' 
	, '' AS 'QTY IN STOCK' 
	, '' AS 'MOQ' 
	, '' AS 'LAST TIME BUY'
	, '' AS '*CURRENCY' 
	, '' AS 'BUY PRICE' 
	, '' AS 'SELL PRICE' 
	, '' AS 'SHIPPING COST' 
	, '' AS 'LEADTIME' 
	, '' AS 'REGION' 
	, '' AS 'DELIVERY DATE' 
	, '' AS 'NOTES' 
	, '' AS 'MSL' 
	FROM [dbo].[tbBom] bom
	JOIN [dbo].[tbCustomerRequirement] cr ON bom.BOMId = cr.BOMNo
	LEFT JOIN [dbo].[tbProduct] pr ON pr.ProductId = cr.ProductNo 
	LEFT JOIN [dbo].[tbManufacturer] mfr ON mfr.ManufacturerId = cr.ManufacturerNo
	WHERE bom.BOMId = @BOMId AND ISNULL(cr.REQStatus, 0) < 4
END 

GO

