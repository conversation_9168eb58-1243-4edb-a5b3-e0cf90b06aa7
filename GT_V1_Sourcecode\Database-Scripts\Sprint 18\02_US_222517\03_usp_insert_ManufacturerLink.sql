﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


/*   
===========================================================================================  
TASK         UPDATED BY   DATE          ACTION    DESCRIPTION  
[US-214629]  Trung Pham   16-Dec-2024	update    Renew add relationship manually flow and calculate Star Rating after that
[US-222517]  An.TranTan   07-Jan-2025	update    Cover case star config has no init value
===========================================================================================  
*/ 
CREATE OR ALTER   PROCEDURE [dbo].[usp_insert_ManufacturerLink] 
--
@ManufacturerNo			int ,
@SupplierCompanyNo		int ,
@StarRating		int		= null ,
@UpdatedBy				int			= Null ,
@ManufacturerLinkId		int	Output
--
AS
--
BEGIN

--check there's not one already
DECLARE 	@Count 	integer

SELECT  	@Count = COUNT (*)
FROM  		dbo.tbManufacturerLink
WHERE   	ManufacturerNo		= @ManufacturerNo
AND			SupplierCompanyNo	= @SupplierCompanyNo

IF	@Count > 0 
	BEGIN
	UPDATE	dbo.tbManufacturerLink
	SET		StarRating	= @StarRating
		,	UpdatedBy			= @UpdatedBy
		,	DLUP				= getdate()
	WHERE   ManufacturerNo		= @ManufacturerNo
	AND		SupplierCompanyNo	= @SupplierCompanyNo
	END 
	ELSE 
	BEGIN
	INSERT
	INTO	dbo.tbManufacturerLink
		(	
			ManufacturerNo	
		,	SupplierCompanyNo		
		,	StarRating
		,	UpdatedBy	
		)
	VALUES
		(
			@ManufacturerNo	
		,	@SupplierCompanyNo		
		,	@StarRating	
		,	@UpdatedBy
		)	
	END	

END
-- Recalculate the Star for all Supplier - Manufacturer relationship based on Purchase Order every time a new relationship manually added
IF EXISTS(SELECT TOP 1 1 FROM tbStarRatingConfig)
BEGIN
	DECLARE @LatestNumOfPO INT;
	SELECT TOP 1 @LatestNumOfPO = NumOfPO 
	FROM tbStarRatingConfig 
	ORDER BY CreatedDate DESC
	
	UPDATE l
	SET StarRating = CASE 
	    WHEN POLineCount / @LatestNumOfPO >= 5 THEN 5
	    ELSE FLOOR(POLineCount / @LatestNumOfPO) 
	END
	FROM tbManufacturerLink l
	CROSS APPLY (
	    SELECT COUNT(DISTINCT pol.PurchaseOrderLineId) AS POLineCount
	    FROM tbCompany c
	    JOIN tbPurchaseOrder po ON po.CompanyNo = l.SupplierCompanyNo
	    JOIN tbPurchaseOrderLine pol ON pol.ManufacturerNo = l.ManufacturerNo
	    WHERE c.CompanyId = l.SupplierCompanyNo
	      AND pol.PurchaseOrderNo = po.PurchaseOrderId
		  AND c.CompanyId = @SupplierCompanyNo
		  AND pol.ManufacturerNo = @ManufacturerNo
	) AS SubQuery
	WHERE l.StarRating IS NOT NULL AND l.ManufacturerNo = @ManufacturerNo 
		  AND l.SupplierCompanyNo = @SupplierCompanyNo;
END
--
SET @ManufacturerLinkId = SCOPE_IDENTITY()
GO


