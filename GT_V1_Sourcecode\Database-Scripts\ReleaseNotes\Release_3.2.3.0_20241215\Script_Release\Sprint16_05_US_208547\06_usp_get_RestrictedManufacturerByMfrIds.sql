﻿
GO

/****** Object:  StoredProcedure [dbo].[usp_get_RestrictedManufacturerByMfrIds]    Script Date: 12/5/2024 12:00:51 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


  
/*  
=============================================================================================================================   
TASK            UPDATED BY       DATE         ACTION     DESCRIPTION    
[US-208547]     Phuc Hoang       02-Dec-2024  CREATE     Company Settings - Mass Restricted/ Unrestricted Manufacturer  
=============================================================================================================================    
*/  
  
                  
CREATE OR ALTER PROCEDURE [dbo].[usp_get_RestrictedManufacturerByMfrIds] (          
	@ClientId int,
	@ManufacturerNos NVARCHAR(2000)
)
AS 
BEGIN

	SELECT DISTINCT String as ManufacturerNo
	INTO #tempManufacturerNo
	FROM dbo.[ufn_splitString](@ManufacturerNos, '||');

	SELECT DISTINCT m.ManufacturerName
	INTO #tempRestrictedManufacturerName
	FROM #tempManufacturerNo temp
	INNER JOIN tbManufacturer m ON temp.ManufacturerNo = m.ManufacturerId;
 
	SELECT            
	rm.ManufacturerName 
	,count(rm.ManufacturerName) AS ManufacturerCount 
  
	FROM tbManufacturer rm 
	WHERE rm.ManufacturerName IN (SELECT ManufacturerName FROM #tempRestrictedManufacturerName) AND ISNULL(rm.Inactive, 0) = 0
	GROUP BY rm.ManufacturerName;

	DROP table #tempManufacturerNo, #tempRestrictedManufacturerName;

END
GO


