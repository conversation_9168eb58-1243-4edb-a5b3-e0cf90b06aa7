<%@ Control Language="C#" CodeBehind="TableActivity.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.HomeNuggets.TableActivity" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>

<ReboundUI_Nugget:DesignBase ID="ctlDB" runat="server" BoxType="allwhite">
	<Content>
		<div class="homepageNugget">
		    <asp:Panel ID="pnlCR" runat="server" CssClass="homepageNugget">
	            <h5><%=Functions.GetGlobalResource("misc", "CustomerRequirements")%></h5>
			    <ReboundUI:FlexiDataTable ID="tblCR" runat="server" AllowSelection="false" />
		    </asp:Panel>
		    <asp:Panel ID="pnlGI" runat="server" CssClass="homepageNugget">
	            <h5><%=Functions.GetGlobalResource("misc", "GoodsIn")%></h5>
			    <ReboundUI:FlexiDataTable ID="tblGI" runat="server" AllowSelection="false" />
		    </asp:Panel>
		    <asp:Panel ID="pnlQU" runat="server" CssClass="homepageNugget">
	            <h5><%=Functions.GetGlobalResource("misc", "Quotes")%></h5>
			    <ReboundUI:FlexiDataTable ID="tblQU" runat="server" AllowSelection="false" />
		    </asp:Panel>		
		    <asp:Panel ID="pnlPO" runat="server" CssClass="homepageNugget">
	            <h5><%=Functions.GetGlobalResource("misc", "PurchaseOrders")%></h5>
			    <ReboundUI:FlexiDataTable ID="tblPO" runat="server" AllowSelection="false" />
		    </asp:Panel>		
		    <asp:Panel ID="pnlSO" runat="server" CssClass="homepageNugget">
	            <h5><%=Functions.GetGlobalResource("misc", "SalesOrders")%></h5>
			    <ReboundUI:FlexiDataTable ID="tblSO" runat="server" AllowSelection="false" />
		    </asp:Panel>		
		    <asp:Panel ID="pnlCRMA" runat="server" CssClass="homepageNugget">
	            <h5><%=Functions.GetGlobalResource("misc", "CustomerRMAs")%></h5>
			    <ReboundUI:FlexiDataTable ID="tblCRMA" runat="server" AllowSelection="false" />
		    </asp:Panel>		
		    <asp:Panel ID="pnlSRMA" runat="server" CssClass="homepageNugget">
	            <h5><%=Functions.GetGlobalResource("misc", "SupplierRMAs")%></h5>
			    <ReboundUI:FlexiDataTable ID="tblSRMA" runat="server" AllowSelection="false" />
		    </asp:Panel>		
 		    <asp:Panel ID="pnlCredit" runat="server" CssClass="homepageNugget">
	            <h5><%=Functions.GetGlobalResource("misc", "Credits")%></h5>
			    <ReboundUI:FlexiDataTable ID="tblCredit" runat="server" AllowSelection="false" />
		    </asp:Panel>		
		    <asp:Panel ID="pnlDebit" runat="server" CssClass="homepageNugget">
	            <h5><%=Functions.GetGlobalResource("misc", "Debits")%></h5>
			    <ReboundUI:FlexiDataTable ID="tblDebit" runat="server" AllowSelection="false" />
		    </asp:Panel>		
		    <asp:Panel ID="pnlInv" runat="server" CssClass="homepageNugget">
	            <h5><%=Functions.GetGlobalResource("misc", "Invoices")%></h5>
			    <ReboundUI:FlexiDataTable ID="tblInv" runat="server" AllowSelection="false" />
		    </asp:Panel>		
       </div>
	</Content>
</ReboundUI_Nugget:DesignBase>
