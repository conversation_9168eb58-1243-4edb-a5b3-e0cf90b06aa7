using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;
using System.Configuration;

namespace Rebound.GlobalTrader.Site.Controls.HomeNuggets.Data
{
    [WebService(Namespace = "http://tempuri.org/")]
    [WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    public class PowerBiSalesDashboard : Rebound.GlobalTrader.Site.Data.Base
    {

        public override void ProcessRequest(HttpContext context)
        {
            if (base.init(context))
            {
                if (context.Request.QueryString["action"] != null)
                    Action = context.Request.QueryString["action"];

                switch (Action)
                {
                    case "GetData": GetData(); break;
                    case "ReturnURL": ReturnURL(); break;
                    default: WriteErrorActionNotFound(); break;
                }
            }
        }

        /// <summary>
        /// Gets the main data
        /// </summary>
        private void GetData()
        {
            try
            {
                
               bool PowerBiPermission = SecurityManager.CheckGeneralPermission(Rebound.GlobalTrader.BLL.SecurityFunction.List.DashboardPowerBI_ReportAllow) || SecurityManager.CheckGeneralPermission(Rebound.GlobalTrader.BLL.SecurityFunction.List.DashboardPowerBISales_ReportAllow);
                JsonObject jsn = new JsonObject();
                    
                    jsn.AddVariable("PowerBiPermission", PowerBiPermission);
                    OutputResult(jsn);
            }
            catch (Exception ex)
            {
                Errorlog obj = new Errorlog();
                obj.LogMessage(Convert.ToString("Exception- " + ex.Message + " " + ex.StackTrace));
            }
        }

        private string ReturnURL()
        {
            string url = ConfigurationManager.AppSettings["PowerBiSalesDashboardUrl"]+"";
            JsonObject jsn = new JsonObject();
            jsn.AddVariable("'url'", url);
            OutputResult(jsn);
            jsn.Dispose();
            jsn = null;
            return url;
        }


    }
}
