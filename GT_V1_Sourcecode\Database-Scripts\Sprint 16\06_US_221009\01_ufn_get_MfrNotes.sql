﻿SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
-- =============================================
-- Author:		An.TranTan
-- Create date: 29-Nov-2024
-- Description:	Get manufacturer advisory notes/restrict message
-- =============================================
CREATE OR ALTER FUNCTION dbo.ufn_get_MfrNotes
(
	@ManufacturerNo INT = NULL,
	@ClientID INT = 0
)
RETURNS NVARCHAR(500)
AS
BEGIN
	IF @ManufacturerNo IS NULL RETURN '';

	DECLARE @Result NVARCHAR(500) = ''
			,@RestrictedMessage NVARCHAR(MAX) = '';

	SELECT @RestrictedMessage = WarningText 
	FROM tbSystemWarningMessage WITH(NOLOCK) 
	WHERE WarningNo = 5	--Restricted manufacturer
		AND ApplyToCatagoryNo = 1	
		AND ClientNo = 0	
		AND InActive = 0;

	SELECT @Result = CASE 
						WHEN rm.RestrictedManufacturerId IS NOT NULL THEN @RestrictedMessage
						WHEN ISNULL(m.IsDisplayAdvisory,0) = 1 THEN m.AdvisoryNotes
						ELSE ''
					END
	FROM dbo.tbManufacturer m WITH(NOLOCK) 
	LEFT JOIN dbo.tbRestrictedManufacturer rm WITH(NOLOCK)
		ON rm.ManufacturerNo = m.ManufacturerId
		AND rm.Inactive = 0
		AND rm.ClientNo = @ClientID
	WHERE m.ManufacturerId = @ManufacturerNo;

	RETURN @Result;
END
GO

/*
SELECT dbo.ufn_get_MfrNotes(9632,101)
*/