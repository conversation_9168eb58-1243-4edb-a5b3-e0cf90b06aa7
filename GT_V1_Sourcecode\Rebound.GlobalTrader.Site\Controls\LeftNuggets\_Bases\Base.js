Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.LeftNuggets");Rebound.GlobalTrader.Site.Controls.LeftNuggets.Base=function(n){Rebound.GlobalTrader.Site.Controls.LeftNuggets.Base.initializeBase(this,[n]);this._blnIsExpanded=!0};Rebound.GlobalTrader.Site.Controls.LeftNuggets.Base.prototype={get_ancShowHideClick:function(){return this._ancShowHideClick},set_ancShowHideClick:function(n){this._ancShowHideClick!==n&&(this._ancShowHideClick=n)},get_pnlOuter:function(){return this._pnlOuter},set_pnlOuter:function(n){this._pnlOuter!==n&&(this._pnlOuter=n)},get_blnIsExpanded:function(){return this._blnIsExpanded},set_blnIsExpanded:function(n){this._blnIsExpanded!==n&&(this._blnIsExpanded=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.LeftNuggets.Base.callBaseMethod(this,"initialize");$addHandler(this._ancShowHideClick,"click",Function.createDelegate(this,this.toggleRollup))},dispose:function(){this.isDisposed||($clearHandlers(this.get_element()),this._ancShowHideClick&&$clearHandlers(this._ancShowHideClick),this._blnIsExpanded=null,this._ancShowHideClick=null,this._pnlOuter=null,this._blnIsExpanded=null,Rebound.GlobalTrader.Site.Controls.LeftNuggets.Base.callBaseMethod(this,"dispose"),this.isDisposed=!0)},toggleRollup:function(){this._blnIsExpanded=!this._blnIsExpanded;this.rollup(!this._blnIsExpanded)},rollup:function(n){this._blnIsExpanded=!n;this._blnIsExpanded?Sys.UI.DomElement.removeCssClass(this._pnlOuter,"leftNuggetCollapsed"):Sys.UI.DomElement.addCssClass(this._pnlOuter,"leftNuggetCollapsed")}};Rebound.GlobalTrader.Site.Controls.LeftNuggets.Base.registerClass("Rebound.GlobalTrader.Site.Controls.LeftNuggets.Base",Sys.UI.Control,Sys.IDisposable);