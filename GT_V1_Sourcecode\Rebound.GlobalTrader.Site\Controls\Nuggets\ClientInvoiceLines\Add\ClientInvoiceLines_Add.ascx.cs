using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site;
using Rebound.GlobalTrader.Site.Controls;

namespace Rebound.GlobalTrader.Site.Controls.Forms {
    public partial class ClientInvoiceLines_Add : Base
    {

		#region Locals

		#endregion

		#region Overrides

		/// <summary>
		/// OnInit
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
			base.OnInit(e);
            TitleText = Functions.GetGlobalResource("FormTitles", "ClientInvoiceLines_Add");
            AddScriptReference("Controls.Nuggets.ClientInvoiceLines.Add.ClientInvoiceLines_Add.js");
		}

		/// <summary>
		/// OnPreRender
		/// </summary>
		/// <param name="e"></param>
		protected override void OnPreRender(EventArgs e) {
			SetupScriptDescriptors();
			base.OnPreRender(e);
		}

		#endregion

		/// <summary>
		/// Setup Script Descriptors
		/// </summary>
		private void SetupScriptDescriptors() {
            _scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.Forms.ClientInvoiceLines_Add", ctlDesignBase.ClientID);
            _scScriptControlDescriptor.AddComponentProperty("ctlSelectSIGILines", ((ItemSearch.Base)FindContentControl("ctlSelectSIGILines")).ctlDesignBase.ClientID);
            _scScriptControlDescriptor.AddProperty("dtFromDate", Functions.FormatDate(Functions.GetUKLocalTime().AddDays(-30)));
            _scScriptControlDescriptor.AddProperty("dtToDate", Functions.FormatDate(Functions.GetUKLocalTime()));
            _scScriptControlDescriptor.AddProperty("SelectedGI", FindFieldControl("ctlGoodsValue", "txtSelectedGI").ClientID);
		}

	}
}