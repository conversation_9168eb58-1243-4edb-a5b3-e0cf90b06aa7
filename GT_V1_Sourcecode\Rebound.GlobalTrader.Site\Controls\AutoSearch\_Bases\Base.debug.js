///<reference name="MicrosoftAjax.js" />
///<reference path="~/Common/Data/Data.js" />
///<reference path="~/Common/Functions/Functions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - dispose everything fully
//Marker     changed by     Date         Remarks
//[001]      Vinay         16/11/2016    Logout, if user change login from other tab
//[002]      Shashi Keshar 14/11/2017    Added for combo box Product filter according to Client No
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.AutoSearch");

Rebound.GlobalTrader.Site.Controls.AutoSearch.Base = function(element) {
    Rebound.GlobalTrader.Site.Controls.AutoSearch.Base.initializeBase(this, [element]);
    this._intTimeoutID = -1;
    this._intCharactersToEnterBeforeSearch = -1;
    this._dataAction = "GetData";
    this._strLastSearch = "";
    this._strCurrentSearch = "";
    this._aryValues = [];
    this._aryIDs = [];
    this._aryExtras = [];
    this._aryDataParams = [];
    this._hypReselect = null;
    this._intCurrentSelection = -1;
    this._blnHasResults = false;
    this._strPathToData = "";
    this._strDataObject = "";
    this._varComboExtraText = "";
    this._blnIsSelectionAllowed = true;
    this._evtTriggerByClickEvent = false;
};

Rebound.GlobalTrader.Site.Controls.AutoSearch.Base.prototype = {

    get_lblResults: function() { return this._lblResults; }, set_lblResults: function(v) { if (this._lblResults !== v) this._lblResults = v; },
    get_hypClose: function() { return this._hypClose; }, set_hypClose: function(v) { if (this._hypClose !== v) this._hypClose = v; },
    get_pnlResults: function() { return this._pnlResults; }, set_pnlResults: function(v) { if (this._pnlResults !== v) this._pnlResults = v; },
    get_pnlContainer: function() { return this._pnlContainer; }, set_pnlContainer: function(v) { if (this._pnlContainer !== v) this._pnlContainer = v; },
    get_txtRelatedTextBox: function() { return this._txtRelatedTextBox; }, set_txtRelatedTextBox: function(v) { if (this._txtRelatedTextBox !== v) this._txtRelatedTextBox = v; },
    get_intTimeoutID: function() { return this._intTimeoutID; }, set_intTimeoutID: function(v) { if (this._intTimeoutID !== v) this._intTimeoutID = v; },
    get_dblSearchDelay: function() { return this._dblSearchDelay; }, set_dblSearchDelay: function(v) { if (this._dblSearchDelay !== v) this._dblSearchDelay = v; },
    get_intCharactersToEnterBeforeSearch: function() { return this._intCharactersToEnterBeforeSearch; }, set_intCharactersToEnterBeforeSearch: function(v) { if (this._intCharactersToEnterBeforeSearch !== v) this._intCharactersToEnterBeforeSearch = v; },
    get_DataAction: function() { return this._dataAction; }, set_DataAction: function(v) { if (this._dataAction !== v) this._dataAction = v; },
    get_result: function() { return this._result; }, set_result: function(v) { if (this._result !== v) this._result = v; },
    get_pnlLoading: function() { return this._pnlLoading; }, set_pnlLoading: function(v) { if (this._pnlLoading !== v) this._pnlLoading = v; },
    get_pnlResultsOuter: function() { return this._pnlResultsOuter; }, set_pnlResultsOuter: function(v) { if (this._pnlResultsOuter !== v) this._pnlResultsOuter = v; },
    get_lblInstructions: function() { return this._lblInstructions; }, set_lblInstructions: function(v) { if (this._lblInstructions !== v) this._lblInstructions = v; },
    get_lblSelectedValue: function() { return this._lblSelectedValue; }, set_lblSelectedValue: function(v) { if (this._lblSelectedValue !== v) this._lblSelectedValue = v; },
    get_enmResultsActionType: function() { return this._enmResultsActionType; }, set_enmResultsActionType: function(v) { if (this._enmResultsActionType !== v) this._enmResultsActionType = v; },
    get_varSelectedValue: function() { return this._varSelectedValue; }, set_varSelectedValue: function(v) { if (this._varSelectedValue !== v) this._varSelectedValue = v; },
    get_varSelectedID: function() { return this._varSelectedID; }, set_varSelectedID: function(v) { if (this._varSelectedID !== v) this._varSelectedID = v; },
    get_hypReselect: function() { return this._hypReselect; }, set_hypReselect: function(v) { if (this._hypReselect !== v) this._hypReselect = v; },
    get_hypButtonTrigger: function() { return this._hypButtonTrigger; }, set_hypButtonTrigger: function(v) { if (this._hypButtonTrigger !== v) this._hypButtonTrigger = v; },
    get_blnTriggerByButton: function () { return this._blnTriggerByButton; }, set_blnTriggerByButton: function (v) { if (this._blnTriggerByButton !== v) this._blnTriggerByButton = v; },
    get_evtTriggerByClickEvent: function () { return this._evtTriggerByClickEvent; }, set_evtTriggerByClickEvent: function (v) { if (this._evtTriggerByClickEvent !== v) this._evtTriggerByClickEvent = v; },
    //[001] code start
    get_intCurrentLogin: function () { return this._intCurrentLogin; }, set_intCurrentLogin: function (value) { if (this._intCurrentLogin !== value) this._intCurrentLogin = value; },
    //[001] code end
    get_intPOHubClientNo: function () { return this._intPOHubClientNo; }, set_intPOHubClientNo: function (value) { if (this._intPOHubClientNo !== value) this._intPOHubClientNo = value; },
    get_intGlobalLoginClientNo: function () { return this._intGlobalLoginClientNo; }, set_intGlobalLoginClientNo: function (value) { if (this._intGlobalLoginClientNo !== value) this._intGlobalLoginClientNo = value; },
    ///[001-Arpit]
    get_blnIsSelectionAllowed: function () { return this._blnIsSelectionAllowed; }, set_blnIsSelectionAllowed: function (value) { if (this._blnIsSelectionAllowed !== value) this._blnIsSelectionAllowed = value; },
    get_strApprovalType: function () { return this._strApprovalType; }, set_strApprovalType: function (value) { if (this._strApprovalType !== value) this._strApprovalType = value; },
    get_intDocmentId: function () { return this._intDocmentId; }, set_intDocmentId: function (value) { if (this._intDocmentId !== value) this._intDocmentId = value; },
    get_txtGroup: function () { return this._txtGroup; }, set_txtGroup: function (v) { if (this._txtGroup !== v) this._txtGroup = v; },
    addDataReturnedEvent: function(handler) { this.get_events().addHandler("DataReturned", handler); },
    removeDataReturnedEvent: function(handler) { this.get_events().removeHandler("DataReturned", handler); },
    onDataReturned: function() {
        var handler = this.get_events().getHandler("DataReturned");
        if (handler) handler(this, Sys.EventArgs.Empty);
    },

    addSelectionMadeEvent: function(handler) { this.get_events().addHandler("SelectionMade", handler); },
    removeSelectionMadeEvent: function(handler) { this.get_events().removeHandler("SelectionMade", handler); },
    onSelectionMade: function() {
        var handler = this.get_events().getHandler("SelectionMade");
        if (handler) handler(this, Sys.EventArgs.Empty);
    },

    addSetupParametersEvent: function(handler) { this.get_events().addHandler("SetupParameters", handler); },
    removeSetupParametersEvent: function(handler) { this.get_events().removeHandler("SetupParameters", handler); },
    onSetupParameters: function() {
        var handler = this.get_events().getHandler("SetupParameters");
        if (handler) handler(this, Sys.EventArgs.Empty);
    },

    initialize: function() {
        Rebound.GlobalTrader.Site.Controls.AutoSearch.Base.callBaseMethod(this, "initialize");
        $addHandler(this._hypClose, "click", Function.createDelegate(this, this.closeResults));
        if (this._blnTriggerByButton) {
            $addHandler(this._hypButtonTrigger, "click", Function.createDelegate(this, this.initiateSearch));
        } else {
            $addHandler(this._txtRelatedTextBox, "keyup", Function.createDelegate(this, this.textboxKeyUp));
        }
        if (this._evtTriggerByClickEvent) {
            $addHandler(this._txtRelatedTextBox, "click", Function.createDelegate(this, this.initiateClickSearch));
        }
        $addHandler(this._hypReselect, "click", Function.createDelegate(this, this.reselect));
        if (this._varSelectedID) this.doItemClick(this._txtRelatedTextBox.value, this._varSelectedID);
    },

    dispose: function() {
        if (this.get_element()) $clearHandlers(this.get_element());
        if (this._hypClose) $clearHandlers(this._hypClose);
        if (this._hypReselect) $clearHandlers(this._hypReselect);
        if (this._txtRelatedTextBox) $clearHandlers(this._txtRelatedTextBox);
        if (this._hypButtonTrigger) $clearHandlers(this._hypButtonTrigger);

        this._intPOHubClientNo = null;
        this._intGlobalLoginClientNo = null;
        this._strApprovalType = null;
        this._intDocmentId = null;
        this._txtGroup = null;
        this._blnIsSelectionAllowed = null;
        Rebound.GlobalTrader.Site.Controls.AutoSearch.Base.callBaseMethod(this, "dispose");
    },

    setupDataObject: function (strObject) {
        this._strPathToData = String.format("Controls/AutoSearch/{0}", strObject);
        this._strDataObject = strObject;
    },
    closeResults: function(blnEnable) {
        $R_FN.showElement(this._pnlContainer, false);
    },

    textboxKeyUp: function(e) {
        var key;
        if (e.keyCode) key = e.keyCode;
        if (e.which) key = e.which;

        if (key == 27) {
            //escape pressed - close results
            this.closeResults();
            return;
        }

        //allow use of keys to move up and down and select
        if (this._enmResultsActionType == $R_ENUM$AutoSearchResultsActionType.RaiseEvent) {
            if (this._blnHasResults) {
                if (key == Sys.UI.Key.enter) {
                    if (this._blnIsSelectionAllowed == false) {
                        this.closeResults();
                        return;
                    }
                    if (this._intCurrentSelection < 0) {
                        this.closeResults();
                        return;
                    }
                    var div = $get(this.getDivID(this._intCurrentSelection));
                    this.doItemClick(this._aryValues[this._intCurrentSelection], this._aryIDs[this._intCurrentSelection], this._aryExtras[this._intCurrentSelection]);
                    return;
                }
                if (key == Sys.UI.Key.up) {
                    if (this._blnIsSelectionAllowed == false) {
                        this.closeResults();
                        return;
                    }
                    this._intCurrentSelection -= 1;
                    if (this._intCurrentSelection < 0) this._intCurrentSelection = 0;
                    this.showSelection();
                    return;
                }
                if (key == Sys.UI.Key.down) {
                    if (this._blnIsSelectionAllowed == false) {
                        this.closeResults();
                        return;
                    }
                    this._intCurrentSelection += 1;
                    if (this._intCurrentSelection >= this._aryValues.length) this._intCurrentSelection = this._aryValues.length - 1;
                    this.showSelection();
                    return;
                }
            }
        }

        this.initiateSearch();
    },

    initiateSearch: function() {
        $R_FN.showElement(this._pnlContainer, true);
        //work out whether to do the search
        this._strCurrentSearch = this._txtRelatedTextBox.value.trim();
        if (this._strCurrentSearch == this._strLastSearch && this._intCharactersToEnterBeforeSearch > 0) return;
        this._strLastSearch = this._strCurrentSearch;
        if (this._strCurrentSearch.length >= this._intCharactersToEnterBeforeSearch) {
            clearTimeout(this._intTimeoutID);
            var strFN = String.format("$find('{0}').startSearch()", this.get_element().id);
            this._intTimeoutID = setTimeout(strFN, this._dblSearchDelay * 1000);
            strFN = null;
            $R_FN.showElement(this._lblInstructions, false);
            $R_FN.showElement(this._lblResults, true);
            $R_FN.setInnerHTML(this._lblResults, this._pnlLoading.innerHTML);
            $R_FN.setInnerHTML(this._pnlResults, "");
        } else {
            $R_FN.showElement(this._lblResults, false);
            $R_FN.showElement(this._lblInstructions, true);
            $R_FN.showElement(this._pnlResultsOuter, false);
            $R_FN.showElement(this._pnlResults, false);
        }
    },

    initiateClickSearch: function () {
        $R_FN.showElement(this._pnlContainer, true);
        //work out whether to do the search
        this._strCurrentSearch = this._txtRelatedTextBox.value.trim();

        if (this._strCurrentSearch.length == 0) {
            clearTimeout(this._intTimeoutID);
            var strFN = String.format("$find('{0}').startSearch()", this.get_element().id);
            this._intTimeoutID = setTimeout(strFN, this._dblSearchDelay * 1000);
            strFN = null;
            $R_FN.showElement(this._lblInstructions, false);
            $R_FN.showElement(this._lblResults, true);
            $R_FN.setInnerHTML(this._lblResults, this._pnlLoading.innerHTML);
            $R_FN.setInnerHTML(this._pnlResults, "");
        } else {
            $R_FN.showElement(this._lblResults, false);
            $R_FN.showElement(this._lblInstructions, true);
            $R_FN.showElement(this._pnlResultsOuter, false);
            $R_FN.showElement(this._pnlResults, false);
        }
    },

    startSearch: function () {
        var strValue = this._txtRelatedTextBox.value.trim();
        if (strValue.length < this._intCharactersToEnterBeforeSearch) return;
        $R_FN.showElement(this._pnlResultsOuter, true);
        $R_FN.showElement(this._pnlLoading, true);
        $R_FN.showElement(this._pnlResults, false);
        $R_FN.setInnerHTML(this._lblResults, this._pnlLoading.innerHTML);
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData(this._strPathToData);
        obj.set_DataObject(this._strDataObject);
        obj.set_DataAction(this._dataAction);
        obj.addParameter("search", strValue + '%');
        
        if (this._strDataObject == 'LyticaManufacturers') {
            var partNumber = document.getElementById('ctl00_cphMain_ctlBOMItems_ctlDB_ctl13_ctlPartNo_lbl').textContent;
            if (partNumber.includes('Stock Alert') || partNumber.includes('In Stock')) {
                partNumber = document.querySelector('#ctl00_cphMain_ctlBOMItems_ctlDB_ctl13_ctlPartNo_lbl .dropdown').firstChild.data;
            }
            partNumber = this.beautifyPartNumber(partNumber);
            obj.addParameter("LyticaPartNo", partNumber);
        }
        
        for (var i = 0, l = this._aryDataParams.length; i < l; i++) {
            obj.addParameter(this._aryDataParams[i].name, this._aryDataParams[i].value);
        }
        //[001] code start
        obj.addParameter("cLogIn", this._intCurrentLogin);
        //[001] code end
        //[002] code start
        obj.addParameter("intPOHubClientNo", this._intPOHubClientNo);
        obj.addParameter("GlobalLoginClientNo", this._intGlobalLoginClientNo);
        obj.addParameter("txtGroup", this._txtGroup);
        obj.addParameter("ApprovalType", this._strApprovalType);
        obj.addParameter("DocmentId", this._intDocmentId);
        //[002] code end
        //this._intGlobalLoginClientNo

        obj.addDataOK(Function.createDelegate(this, this.searchDataComplete));
        obj.addError(Function.createDelegate(this, this.searchDataError));
        obj.addTimeout(Function.createDelegate(this, this.searchDataError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    searchDataError: function(args) {
        //args.get_ErrorMessage();
        this.closeResults();
    },

    searchDataComplete: function(args) {
        this._result = args._result;
        $R_FN.setInnerHTML(this._pnlResults, "");
        $R_FN.setInnerHTML(this._lblResults, String.format("{0} result(s)", this._result.TotalRecords));
        Array.clear(this._aryValues);
        Array.clear(this._aryIDs);
        Array.clear(this._aryExtras);
        this._intCurrentSelection = -1;
        this.onDataReturned();
        $R_FN.showElement(this._pnlContainer, true);
        $R_FN.showElement(this._lblInstructions, false);
        $R_FN.showElement(this._pnlLoading, false);
        $R_FN.showElement(this._pnlResults, true);
        this._blnHasResults = true;
    },

    addResultItem: function(strInnerHTML, varValue, varID, varExtraData) {
        var div = document.createElement("div");
        div.className = "item";
        $R_FN.setInnerHTML(div, strInnerHTML);
        if (this._enmResultsActionType == $R_ENUM$AutoSearchResultsActionType.RaiseEvent) {
            if (this._blnIsSelectionAllowed) {
                div.className += " itemRaiseEvent";
            }
            var strID = this.get_element().id;
            if (this._blnIsSelectionAllowed) {
                div.onclick = function () { $find(strID).doItemClick(varValue, varID, varExtraData); };
            }
            div.id = this.getDivID(this._aryValues.length);
            Array.add(this._aryValues, varValue);
            Array.add(this._aryIDs, varID);
            Array.add(this._aryExtras, varExtraData);
        }
        this._pnlResults.appendChild(div);
        div = null;
    },

    doItemClick: function(varValue, varID, varExtraData) {
        $R_FN.showElement(this._pnlContainer, false);
        this._varSelectedValue = varValue;
        this._varSelectedID = varID;
        this._varSelectedExtraData = varExtraData;
        this._varComboExtraText = varExtraData;
        this._txtRelatedTextBox.value = this._varSelectedValue;
        $R_FN.setInnerHTML(this._lblSelectedValue, this._varSelectedValue);
        var blnShowSelected = (varID > 0);
        $R_FN.showElement(this._txtRelatedTextBox, !blnShowSelected);
        $R_FN.showElement(this._lblSelectedValue, blnShowSelected);
        $R_FN.showElement(this._hypReselect, blnShowSelected);
        this.onSelectionMade();
    },

    addDataParameter: function(strKey, strValue) {
        if (!strKey) return;
        if (!strValue) return;
        Array.add(this._aryDataParams, { name: strKey, value: strValue });
    },

    reselect: function() {
        $R_FN.showElement(this._hypReselect, false);
        $R_FN.showElement(this._lblSelectedValue, false);
        this._varSelectedValue = null;
        this._varComboExtraText = null;
        this._varSelectedID = null;
        this._varSelectedExtraData = null;
        this._intCurrentSelection = -1;
        this._blnHasResults = false;
        $R_FN.setInnerHTML(this._lblSelectedValue, "");
        $R_FN.showElement(this._txtRelatedTextBox, true);
        this._txtRelatedTextBox.value = "";
        //		this._txtRelatedTextBox.focus(); //removed because of incompatibility in IE7 onwards
    },

    showSelection: function() {
        var div = $get(this.getDivID(this._intCurrentSelection));
        var divPrev = $get(this.getDivID(this._intCurrentSelection - 1));
        var divNext = $get(this.getDivID(this._intCurrentSelection + 1));
        if (div) Sys.UI.DomElement.addCssClass(div, "itemSelected");
        if (divPrev) Sys.UI.DomElement.removeCssClass(divPrev, "itemSelected");
        if (divNext) Sys.UI.DomElement.removeCssClass(divNext, "itemSelected");
        var posItem = Sys.UI.DomElement.getBounds(div);
        this._pnlResultsOuter.scrollTop = posItem.height * this._intCurrentSelection;
        //if (div) this._txtRelatedTextBox.value = this._aryValues[this._intCurrentSelection];
    },

    getDivID: function(i) {
        return String.format("{0}_div{1}", this._element.id, i);
    },
    beautifyPartNumber: function (partNumber) {
        partNumber = partNumber.replace(" (Alternate)", "");
        partNumber = partNumber.replace("&", "_AMPERSAND_");
        partNumber = partNumber.replace("#", "_HASH_");
        partNumber = partNumber.replace("=", "_EQUALS_");
        partNumber = partNumber.trim();
        return partNumber;
    }
};

Rebound.GlobalTrader.Site.Controls.AutoSearch.Base.registerClass("Rebound.GlobalTrader.Site.Controls.AutoSearch.Base", Sys.UI.Control, Sys.IDisposable);