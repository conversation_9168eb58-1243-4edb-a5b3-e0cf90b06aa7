﻿/*
 Marker     ChangedBy       ChangedDate     Remarks
 [001]      <PERSON><PERSON><PERSON>     25-Jan-2019     Sales Dashboard Changes/ Req Dashboard Headings
 */
using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;
using Rebound.GlobalTrader.DAL;
using Rebound.GlobalTrader.BLL;

namespace Rebound.GlobalTrader.BLL
{
    public partial class Part : BizObject
    {

        #region Properties

        protected static DAL.PartElement Settings
        {
            get { return Globals.Settings.Parts; }
        }

        /// <summary>
        /// PartId
        /// </summary>
        public System.Int32 PartId { get; set; }
        /// <summary>
        /// FullPart
        /// </summary>
        public System.String FullPart { get; set; }
        /// <summary>
        /// ManufacturerNo
        /// </summary>
        public System.Int32? ManufacturerNo { get; set; }
        /// <summary>
        /// PackageNo
        /// </summary>
        public System.Int32? PackageNo { get; set; }
        /// <summary>
        /// ProductNo
        /// </summary>
        public System.Int32? ProductNo { get; set; }
        /// PackageId
        /// </summary>
        public System.Int32? PackageId { get; set; }
        
        /// <summary>
        /// MinimumQuantity
        /// </summary>
        public System.Int32? MinimumQuantity { get; set; }
        /// <summary>
        /// ReOrderQuantity
        /// </summary>
        public System.Int32? ReOrderQuantity { get; set; }
        /// <summary>
        /// LeadTime
        /// </summary>
        public System.Int32? LeadTime { get; set; }
        /// <summary>
        /// ClientNo
        /// </summary>
        public System.Int32 ClientNo { get; set; }
        /// <summary>
        /// ResalePrice
        /// </summary>
        public System.Double? ResalePrice { get; set; }
        /// <summary>
        /// ROHSCompliant
        /// </summary>
        public System.Boolean ROHSCompliant { get; set; }
        /// <summary>
        /// MasterPart
        /// </summary>
        public System.Boolean? MasterPart { get; set; }
        /// <summary>
        /// GoldenPart
        /// </summary>
        public System.Boolean? GoldenPart { get; set; }
        public System.Int32 ECCNNo { get; set; }
        public System.Int32 PartEccnMappedId { get; set; }
        
        /// <summary>
        /// UpdatedBy
        /// </summary>
        public System.Int32? UpdatedBy { get; set; }
        /// <summary>
        /// DLUP
        /// </summary>
        public System.DateTime DLUP { get; set; }
        /// <summary>
        /// PartTitle
        /// </summary>
        public System.String PartTitle { get; set; }
        /// <summary>
        /// PartName
        /// </summary>
        public System.String PartName { get; set; }


        public System.String PartNameWithManufacture { get; set; }


        public System.String Productname { get; set; }
        public System.String DateCode { get; set; }
        public System.String Packagename { get; set; }
        public System.Int32 ROHSNo { get; set; }
        public System.String ManufacturerName { get; set; }
        public System.String ManufacturerFullName { get; set; }
        public System.String IHSProduct { get; set; }
        
        public System.String DateCodeOriginal { get; set; }
        public System.String ProductDescription { get; set; }
        public System.String IHSProductDescription { get; set; }
        public System.String IHSDutyCode { get; set; }
        public System.String ECCNCode { get; set; }
        /// <summary>
        /// RowNum
        /// </summary>
        public System.Int64? RowNum { get; set; }
        /// <summary>
        /// RowCnt
        /// </summary>
        public System.Int32? RowCnt { get; set; }	
        
        //[001] start
        public int? StockId { get; set; }
        public string ResultType { get; set; }

        public int? IHSPartsId { get; set; }
        public System.String PartStatus { get; set; }
        public System.String IHSCurrencyCode { get; set; }
        
        /// <summary>
        /// CountryOfOrigin
        /// </summary>
        public System.String CountryOfOrigin { get; set; }
        /// <summary>
        /// CountryOfOriginNo
        /// </summary>
        public System.Int32? CountryOfOriginNo { get; set; }
        /// <summary>
        /// LifeCycleStage
        /// </summary>
        public System.String LifeCycleStage { get; set; }
        /// <summary>
        /// HTSCode
        /// </summary>
        public System.String HTSCode { get; set; }
        /// <summary>
        /// AveragePrice
        /// </summary>
        public System.Double? AveragePrice { get; set; }
        /// <summary>
        /// ManufacturerNo
        /// </summary>
        public System.String Packaging { get; set; }
        /// <summary>
        /// PackagingSize
        /// </summary>
        public System.String PackagingSize { get; set; }

        public System.String ROHSName { get; set; }
        public System.String Descriptions { get; set; }

          

          public System.Double? LastPricePaidByCust { get; set; }
          public System.String PaidByCustCurrencyCode { get; set; }
          public System.String LastSoldtoCustomer { get; set; }
          public System.Double? LastAverageReboundPriceSold { get; set; }
          public System.DateTime LastSoldOn { get; set; }

          public System.Int32? LastQuantity { get; set; }
          public System.String LastSupplierType { get; set; }
          public System.String LastDatecode { get; set; }
          public System.DateTime LastDatePurchased { get; set; }
          public System.String LastCustomerRegion { get; set; }

        
          public System.Double? CustLastPricePaidByCust { get; set; }
          public System.String CustPaidByCustCurrencyCode { get; set; }
          public System.Double? CustLastAvgReboundPriceSold { get; set; }
          public System.String CustLastSoldtoCustomer { get; set; }
          public System.DateTime CustLastSoldOn { get; set; }


          public System.Int32? CustQuantity { get; set; }
          public System.String CustSupplierType { get; set; }
          public System.String CustDatecode { get; set; }
          public System.DateTime CustDatePurchased { get; set; }
          public System.String CustomerRegion { get; set; }
          public System.Double? CleintBestPricePaid12 { get; set; }
          public System.Double? BestLastPricePaid12 { get; set; }
          public System.String PackageDescription { get; set; }
          public System.Boolean? ECCNStatus { get; set; }
          public System.String ECCNWarning { get; set; }

        public System.String ItemFound { get; set; }
        public int TotalRecords { get; set; }











        //[001] end
        #endregion

        #region Methods

        /// <summary>
        /// AutoSearch
        /// Calls [usp_autosearch_Part]
        /// </summary>
        public static List<Part> AutoSearch(System.Int32? clientId, System.String partSearch)
        {
            List<PartDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.Part.AutoSearch(clientId, partSearch);
            if (lstDetails == null)
            {
                return new List<Part>();
            }
            else
            {
                List<Part> lst = new List<Part>();
                foreach (PartDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.Part obj = new Rebound.GlobalTrader.BLL.Part();
                    obj.PartName = objDetails.PartName;

                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }
        /// <summary>
        /// AutoSearch
        /// Calls [usp_autosearch_Part_BOMManager]
        /// </summary>
        public static List<Part> AutoSearchBomManager(System.Int32? clientId, System.String partSearch)
        {
            List<PartDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.Part.AutoSearchBomManager(clientId, partSearch);
            if (lstDetails == null)
            {
                return new List<Part>();
            }
            else
            {
                List<Part> lst = new List<Part>();
                foreach (PartDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.Part obj = new Rebound.GlobalTrader.BLL.Part();
                    obj.PartName = objDetails.PartName;
                    obj.ProductNo = objDetails.ProductNo;
                    obj.ProductDescription = objDetails.ProductDescription;
                    obj.ManufacturerNo = objDetails.ManufacturerNo;
                    obj.ManufacturerName = objDetails.ManufacturerName;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }



        /// <summary>
        /// AutoSearch
        /// Calls [usp_PartSearch_GRID]
        /// </summary>
        public static List<Part> CustReqPartsGRID(System.Int32? clientId, System.String partSearch)
        {
            List<PartDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.Part.CustReqPartsGRID(clientId, partSearch);
            if (lstDetails == null)
            {
                return new List<Part>();
            }
            else
            {
                List<Part> lst = new List<Part>();
                foreach (PartDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.Part obj = new Rebound.GlobalTrader.BLL.Part();
                    obj.PartName = objDetails.PartName;
                    obj.ManufacturerNo = objDetails.ManufacturerNo;
                    obj.PartNameWithManufacture = objDetails.PartNameWithManufacture;
                    obj.ProductNo = objDetails.ProductNo;
                    obj.PackageNo = objDetails.PackageNo;
                    obj.ManufacturerName = objDetails.ManufacturerName;
                    obj.Productname = objDetails.Productname;
                    obj.Packagename = objDetails.Packagename;
                    obj.DateCode = objDetails.DateCode;
                    obj.DateCodeOriginal = objDetails.DateCodeOriginal;
                    obj.ROHSNo = objDetails.ROHSNo;
                    obj.ProductDescription = objDetails.ProductDescription;
                    obj.ProductInactive = objDetails.ProductInactive;
                    //[001] start
                    obj.StockId = objDetails.StockId;
                    obj.ResultType = objDetails.ResultType;
                    //[001] end
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }


        /// <summary>
        /// AutoSearch From IHS API Service
        /// Calls [usp_PartSearch_GRIDFromIHSAPI]
        /// </summary>
        public static List<Part> CustReqPartsGRIDIHSAPI(System.Int32? clientId, System.String partSearch, System.String searchType, System.Int32? orderBy, System.Int32? sortDir, System.Int32? pageIndex, System.Int32? pageSize)
        {
            List<PartDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.Part.CustReqPartsGRIDIHSAPI(clientId, partSearch, searchType, orderBy, sortDir, pageIndex, pageSize);
            if (lstDetails == null)
            {
                return new List<Part>();
            }
            else
            {
                List<Part> lst = new List<Part>();
                foreach (PartDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.Part obj = new Rebound.GlobalTrader.BLL.Part();
                    obj.PartName = objDetails.PartName;
                    obj.ManufacturerNo = objDetails.ManufacturerNo;
                    obj.PartNameWithManufacture = objDetails.PartNameWithManufacture;
                    obj.ManufacturerName = objDetails.ManufacturerName;
                    obj.ManufacturerFullName = objDetails.ManufacturerFullName;
                    obj.ROHSNo = objDetails.ROHSNo;
                    obj.IHSPartsId = objDetails.IHSPartsId;
                    obj.CountryOfOrigin = objDetails.CountryOfOrigin;
                    obj.CountryOfOriginNo = objDetails.CountryOfOriginNo;
                    obj.HTSCode = objDetails.HTSCode;
                    obj.AveragePrice = objDetails.AveragePrice;
                    obj.Packaging = objDetails.Packaging;
                    obj.PackagingSize = objDetails.PackagingSize;
                    obj.ROHSName = objDetails.ROHSName;
                    obj.Descriptions = objDetails.Descriptions;
                    obj.ResultType = objDetails.ResultType;
                    obj.PartStatus = objDetails.PartStatus;
                    obj.IHSCurrencyCode = objDetails.IHSCurrencyCode;
                    obj.ProductDescription = objDetails.ProductDescription;
                    obj.ProductNo = objDetails.ProductNo;
                    obj.IHSProductDescription = objDetails.IHSProductDescription;
                    obj.IHSDutyCode = objDetails.IHSDutyCode;
                    obj.RowNum = objDetails.RowNum;
                    obj.RowCnt = objDetails.RowCnt;
                    obj.IHSProduct = objDetails.IHSProduct;
                    obj.ECCNCode = objDetails.ECCNCode;

                    //[001] end
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }

        /// <summary>
        /// AutoSearch From IHS API Service
        /// Calls [usp_PartSearch_GRIDFromIHSAPI]
        /// </summary>
        public static List<Part> CustReqPartsGRIDIHSAPIBOMManager(System.Int32? clientId, int BOMManagerID, int? CustomerReqID, out DataTable dtCheckParts, int curPage = 1, int Rpp = 5)
        {
            List<PartDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.Part.CustReqPartsGRIDIHSAPIBOMManager(clientId, BOMManagerID, CustomerReqID, out dtCheckParts, curPage, Rpp);
            if (lstDetails == null)
            {
                return new List<Part>();
            }
            else
            {
                List<Part> lst = new List<Part>();
                foreach (PartDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.Part obj = new Rebound.GlobalTrader.BLL.Part();
                    obj.PartName = objDetails.PartName;
                    obj.ManufacturerNo = objDetails.ManufacturerNo;
                    obj.PartNameWithManufacture = objDetails.PartNameWithManufacture;
                    obj.ManufacturerName = objDetails.ManufacturerName;
                    obj.ManufacturerFullName = objDetails.ManufacturerFullName;
                    obj.ROHSNo = objDetails.ROHSNo;
                    obj.IHSPartsId = objDetails.IHSPartsId;
                    obj.CountryOfOrigin = objDetails.CountryOfOrigin;
                    obj.CountryOfOriginNo = objDetails.CountryOfOriginNo;
                    obj.HTSCode = objDetails.HTSCode;
                    obj.AveragePrice = objDetails.AveragePrice;
                    obj.Packaging = objDetails.Packaging;
                    obj.PackagingSize = objDetails.PackagingSize;
                    obj.ROHSName = objDetails.ROHSName;
                    obj.Descriptions = objDetails.Descriptions;
                    obj.ResultType = objDetails.ResultType;
                    obj.PartStatus = objDetails.PartStatus;
                    obj.IHSCurrencyCode = objDetails.IHSCurrencyCode;
                    obj.ProductDescription = objDetails.ProductDescription;
                    obj.ProductNo = objDetails.ProductNo;
                    obj.IHSProductDescription = objDetails.IHSProductDescription;
                    obj.IHSDutyCode = objDetails.IHSDutyCode;
                    obj.RowNum = objDetails.RowNum;
                    obj.RowCnt = objDetails.RowCnt;
                    obj.IHSProduct = objDetails.IHSProduct;
                    obj.ECCNCode = objDetails.ECCNCode;
                    obj.ItemFound = objDetails.ItemFound;
                    obj.TotalRecords = objDetails.TotalRecords;
                    //[001] end
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }

        /// <summary>
        /// IHS Come from tblihspart table
        /// Calls [usp_Search_for_IHSPartDetails]
        /// </summary>
        public static List<Part> CustReqIHSPartsDetails(System.Int32? clientId, System.Int32? IHSPartsId, System.String MSLName, System.Int32? MSLNo, System.String Manufacturer, System.String IHSProdDesc, System.String Packaging, System.String HTSCode, System.String IHSDutyCode, System.String CountryOfOrigin, System.Int32? CountryOfOriginNo, System.String PackagingSize)
        {
            List<PartDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.Part.CustReqIHSPartsDetails(clientId,  IHSPartsId,  MSLName,  MSLNo,  Manufacturer,  IHSProdDesc,    Packaging,  HTSCode,  IHSDutyCode,  CountryOfOrigin,  CountryOfOriginNo,  PackagingSize);
            if (lstDetails == null)
            {
                return new List<Part>();
            }
            else
            {
                List<Part> lst = new List<Part>();
                foreach (PartDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.Part obj = new Rebound.GlobalTrader.BLL.Part();
                    obj.PartName = objDetails.PartName;
                    obj.ManufacturerNo = objDetails.ManufacturerNo;
                    obj.PartNameWithManufacture = objDetails.PartNameWithManufacture;
                    obj.ManufacturerName = objDetails.ManufacturerName;
                    obj.ManufacturerFullName = objDetails.ManufacturerFullName;
                    obj.ROHSNo = objDetails.ROHSNo;
                    obj.IHSPartsId = objDetails.IHSPartsId;
                    obj.CountryOfOrigin = objDetails.CountryOfOrigin;
                    obj.CountryOfOriginNo = objDetails.CountryOfOriginNo;
                    obj.HTSCode = objDetails.HTSCode;
                    obj.AveragePrice = objDetails.AveragePrice;
                    obj.Packaging = objDetails.Packaging;
                    obj.PackagingSize = objDetails.PackagingSize;
                    obj.ROHSName = objDetails.ROHSName;
                    obj.Descriptions = objDetails.Descriptions;
                    obj.ResultType = objDetails.ResultType;
                    obj.PartStatus = objDetails.PartStatus;
                    obj.IHSCurrencyCode = objDetails.IHSCurrencyCode;
                    obj.ProductDescription = objDetails.ProductDescription;
                    obj.ProductNo = objDetails.ProductNo;
                    obj.IHSProductDescription = objDetails.IHSProductDescription;
                    obj.IHSDutyCode = objDetails.IHSDutyCode;
                    obj.RowNum = objDetails.RowNum;
                    //obj.RowCnt = objDetails.RowCnt;
                    obj.IHSProduct = objDetails.IHSProduct;
                    obj.PackageId = objDetails.PackageId;
                    obj.ECCNCode = objDetails.ECCNCode;
                    obj.PackageDescription = objDetails.PackageDescription;



                    //[001] end
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }


        /// <summary>
        /// Insert-Select
        /// Calls [usp_insert_IHSApiXML_Or_select]
        /// </summary>
        public static List<Part> InsertIHSApiXMLOrSelect(System.Int32? clientNo, System.Int32? updatedBy, System.String strXMLData)
        {
            List<PartDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.Part.InsertIHSApiXMLOrSelect(clientNo, updatedBy, strXMLData);
            if (lstDetails == null)
            {
                return new List<Part>();
            }
            else
            {
                List<Part> lst = new List<Part>();
                foreach (PartDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.Part obj = new Rebound.GlobalTrader.BLL.Part();
                    obj.PartName = objDetails.PartName;
                    obj.ManufacturerNo = objDetails.ManufacturerNo;
                    obj.PartNameWithManufacture = objDetails.PartNameWithManufacture;
                    obj.ManufacturerName = objDetails.ManufacturerName;
                    obj.ManufacturerFullName = objDetails.ManufacturerFullName;
                    obj.ROHSNo = objDetails.ROHSNo;
                    obj.IHSPartsId = objDetails.IHSPartsId;
                    obj.CountryOfOrigin = objDetails.CountryOfOrigin;
                    obj.CountryOfOriginNo = objDetails.CountryOfOriginNo;
                    obj.HTSCode = objDetails.HTSCode;
                    obj.AveragePrice = objDetails.AveragePrice;
                    obj.Packaging = objDetails.Packaging;
                    obj.PackagingSize = objDetails.PackagingSize;
                    obj.ROHSName = objDetails.ROHSName;
                    obj.Descriptions = objDetails.Descriptions;
                    obj.ResultType = objDetails.ResultType;
                    obj.PartStatus = objDetails.PartStatus;
                    obj.IHSCurrencyCode = objDetails.IHSCurrencyCode;
                    obj.ProductDescription = objDetails.ProductDescription;
                    obj.ProductNo = objDetails.ProductNo;
                    obj.IHSProductDescription = objDetails.IHSProductDescription;
                    obj.IHSDutyCode = objDetails.IHSDutyCode;
                    obj.RowNum = objDetails.RowNum;
                    //obj.RowCnt = objDetails.RowCnt;
                    obj.IHSProduct = objDetails.IHSProduct;
                    obj.PackageId = objDetails.PackageId;
                    obj.ECCNCode = objDetails.ECCNCode;
                    obj.PackageDescription = objDetails.PackageDescription;



                    //[001] end
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }


        //ihs token code start hear
        public static int GetIHSTokenData(out System.String TokenNumber)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Part.GetIHSTokenData(out TokenNumber);
        }
        //ihs token code ended hear


        //[001] start
        /// <summary>
        /// Last price sold details by customer
        /// Calls [usp_LastGetCustPartDetail]
        /// </summary>
        //public static DataSet GetPartDetail(string partNo, Int32? ClientID, System.Int32? companyNo)
        //{
        //    return Rebound.GlobalTrader.DAL.SiteProvider.Part.GetPartDetail(partNo, ClientID, companyNo);
            
        //}
        /// <summary>
        /// Last price sold details by customer
        /// Calls [usp_LastGetCustPartDetail]
        /// </summary>
        public static List<Part> GetPartDetail(string partNo, Int32? ClientID, System.Int32? companyNo)
        {
            List<PartDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.Part.GetPartDetail(partNo, ClientID, companyNo);
            if (lstDetails == null)
            {
                return new List<Part>();
            }
            else
            {
                List<Part> lst = new List<Part>();
                foreach (PartDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.Part obj = new Rebound.GlobalTrader.BLL.Part();
                    obj.LastPricePaidByCust = objDetails.LastPricePaidByCust;
                    obj.PaidByCustCurrencyCode = objDetails.PaidByCustCurrencyCode;
                    obj.LastSoldtoCustomer = objDetails.LastSoldtoCustomer;
                    obj.LastAverageReboundPriceSold = objDetails.LastAverageReboundPriceSold;
                    obj.LastSoldOn = objDetails.LastSoldOn;
                    obj.LastQuantity = objDetails.LastQuantity;
                    obj.LastSupplierType = objDetails.LastSupplierType;
                    obj.LastDatecode = objDetails.LastDatecode;
                    obj.LastDatePurchased = objDetails.LastDatePurchased;
                    obj.LastCustomerRegion = objDetails.LastCustomerRegion;

                    obj.CustLastPricePaidByCust = objDetails.CustLastAvgReboundPriceSold;
                    obj.CustPaidByCustCurrencyCode = objDetails.CustPaidByCustCurrencyCode;
                    obj.CustLastAvgReboundPriceSold = objDetails.CustLastAvgReboundPriceSold;
                    obj.CustLastSoldtoCustomer = objDetails.CustLastSoldtoCustomer;
                    obj.CustLastSoldOn = objDetails.CustLastSoldOn;
                    obj.CustQuantity = objDetails.CustQuantity;
                    obj.CustSupplierType = objDetails.CustSupplierType;
                    obj.CustDatecode = objDetails.CustDatecode;
                    obj.CustDatePurchased = objDetails.CustDatePurchased;
                    obj.CustomerRegion = objDetails.CustomerRegion;
                    obj.CleintBestPricePaid12 = objDetails.CleintBestPricePaid12;
                    obj.BestLastPricePaid12 = objDetails.BestLastPricePaid12;



                    //[001] end
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }

        /// <summary>
        /// AutoSearch
        /// Calls [usp_autosearch_Part_New]
        /// </summary>
        public static List<Part> CustReqPartSearch(System.Int32? clientId, System.String partSearch)
        {
            List<PartDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.Part.CustReqPartSearch(clientId, partSearch);
            if (lstDetails == null)
            {
                return new List<Part>();
            }
            else
            {
                List<Part> lst = new List<Part>();
                foreach (PartDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.Part obj = new Rebound.GlobalTrader.BLL.Part();
                    obj.PartName = objDetails.PartName;
                    obj.ManufacturerNo = objDetails.ManufacturerNo;
                    obj.PartNameWithManufacture = objDetails.PartNameWithManufacture;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }
        /// <summary>
        /// Customer Requirements Fill data by Part Search
        /// Calls [usp_autosearch_CustReqPart]
        /// </summary>
        public static List<Part> CustReqPart(System.Int32? clientId, System.String partSearch, System.String ids, System.String DateCode)
        {
            List<PartDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.Part.CustReqPart(clientId, partSearch, ids, DateCode);
            if (lstDetails == null)
            {
                return new List<Part>();
            }
            else
            {
                List<Part> lst = new List<Part>();
                foreach (PartDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.Part obj = new Rebound.GlobalTrader.BLL.Part();
                    obj.PartName = objDetails.PartName;
                    obj.ManufacturerNo = objDetails.ManufacturerNo;
                    obj.PartNameWithManufacture = objDetails.PartNameWithManufacture;

                    obj.Productname = objDetails.Productname;
                    obj.Packagename = objDetails.Packagename;
                    obj.DateCode = objDetails.DateCode;
                    obj.ProductNo = objDetails.ProductNo;
                    obj.PackageNo = objDetails.PackageNo;
                    obj.ManufacturerName = objDetails.ManufacturerName;
                    obj.ROHSNo = objDetails.ROHSNo;


                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }
        /// <summary>
        /// Insert
        /// Calls [usp_insert_Part]
        /// </summary>
        public static Int32 Insert(System.String fullPart, System.Int32? manufacturerNo, System.Int32? packageNo, System.Int32? productNo, System.Int32? minimumQuantity, System.Int32? reOrderQuantity, System.Int32? leadTime, System.Int32? clientNo, System.Double? resalePrice, System.String partTitle, System.Boolean? masterPart, System.Boolean? goldenPart, System.Boolean? rohsCompliant, System.Int32? updatedBy)
        {
            Int32 objReturn = Rebound.GlobalTrader.DAL.SiteProvider.Part.Insert(fullPart, manufacturerNo, packageNo, productNo, minimumQuantity, reOrderQuantity, leadTime, clientNo, resalePrice, partTitle, masterPart, goldenPart, rohsCompliant, updatedBy);
            return objReturn;
        }
        /// <summary>
        /// Insert (without parameters)
        /// Calls [usp_insert_Part]
        /// </summary>
        public Int32 Insert()
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Part.Insert(FullPart, ManufacturerNo, PackageNo, ProductNo, MinimumQuantity, ReOrderQuantity, LeadTime, ClientNo, ResalePrice, PartTitle, MasterPart, GoldenPart, ROHSCompliant, UpdatedBy);
        }

        private static Part PopulateFromDBDetailsObject(PartDetails obj)
        {
            Part objNew = new Part();
            objNew.PartId = obj.PartId;
            objNew.FullPart = obj.FullPart;
            objNew.ManufacturerNo = obj.ManufacturerNo;
            objNew.PackageNo = obj.PackageNo;
            objNew.ProductNo = obj.ProductNo;
            objNew.MinimumQuantity = obj.MinimumQuantity;
            objNew.ReOrderQuantity = obj.ReOrderQuantity;
            objNew.LeadTime = obj.LeadTime;
            objNew.ClientNo = obj.ClientNo;
            objNew.ResalePrice = obj.ResalePrice;
            objNew.ROHSCompliant = obj.ROHSCompliant;
            objNew.MasterPart = obj.MasterPart;
            objNew.GoldenPart = obj.GoldenPart;
            objNew.UpdatedBy = obj.UpdatedBy;
            objNew.DLUP = obj.DLUP;
            objNew.PartTitle = obj.PartTitle;
            objNew.PartName = obj.PartName;
            return objNew;
        }

        //Part Eccn Mapped
        /// <summary>
        /// IHS Come from tblihspart table
        /// Calls [usp_Search_for_IHSPartDetails]
        /// </summary>
        public static List<Part> CustReqIHSPartEccnDetails(System.Int32? clientId,  System.String ECCNCode,System.Int32 UpdatedBy,System.String PartNo)
        {
            List<PartDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.Part.CustReqIHSPartEccnDetails(clientId, ECCNCode, UpdatedBy,PartNo);
            if (lstDetails == null)
            {
                return new List<Part>();
            }
            else
            {
                List<Part> lst = new List<Part>();
                foreach (PartDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.Part obj = new Rebound.GlobalTrader.BLL.Part();
                    obj.ECCNCode = objDetails.ECCNCode;
                    obj.ECCNNo = objDetails.ECCNNo;
                    obj.ECCNStatus = objDetails.ECCNStatus;
                    obj.ECCNWarning = objDetails.ECCNWarning;
                    obj.PartEccnMappedId = objDetails.PartEccnMappedId;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }
        //code end

        public static List<Part> CustReqIHSEccnCode(System.Int32? clientId, System.String ECCNCode, System.Int32 UpdatedBy)
        {
            List<PartDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.Part.CustReqIHSEccnCode(clientId, ECCNCode, UpdatedBy);
            if (lstDetails == null)
            {
                return new List<Part>();
            }
            else
            {
                List<Part> lst = new List<Part>();
                foreach (PartDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.Part obj = new Rebound.GlobalTrader.BLL.Part();
                    obj.ECCNCode = objDetails.ECCNCode;
                    obj.ECCNNo = objDetails.ECCNNo;
                    obj.ECCNStatus = objDetails.ECCNStatus;
                    obj.ECCNWarning = objDetails.ECCNWarning;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }

        //Find SO ECCN Detials
        public static List<Part> SOIHSEccnDetial(System.Int32? clientId, System.String Part, System.Int32 UpdatedBy)
        {
            List<PartDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.Part.SOIHSEccnDetial(clientId, Part, UpdatedBy);
            if (lstDetails == null)
            {
                return new List<Part>();
            }
            else
            {
                List<Part> lst = new List<Part>();
                foreach (PartDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.Part obj = new Rebound.GlobalTrader.BLL.Part();
                    obj.ECCNCode = objDetails.ECCNCode;
                    obj.ECCNNo = objDetails.ECCNNo;
                    obj.ECCNWarning = objDetails.ECCNWarning;
                    obj.ECCNStatus = objDetails.ECCNStatus;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }
        //end
        #endregion

    }
}