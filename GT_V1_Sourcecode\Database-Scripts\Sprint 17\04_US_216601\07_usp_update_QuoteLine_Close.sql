﻿
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

/* 
==========================================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-216601]	    Phuc Hoang			12-Dec-2024		Update			216601: Quote - New status matrix
==========================================================================================================
*/

CREATE OR ALTER PROCEDURE [dbo].[usp_update_QuoteLine_Close] 
--*-----------------------------------------------------------------------------------------------
--* RP 30.11.2009:
--* - copy NotQuoted flag into QuoteLine from Reason
--*-----------------------------------------------------------------------------------------------
    @QuoteLineId int
  , @ReasonNo int
  , @UpdatedBy int
  , @Reasons NVARCHAR(200)=null -- Added as per client requirement 14-3-2018
  , @RowsAffected int = NULL OUTPUT
AS 
    DECLARE @SourcingResultNo INT 
    SELECT @SourcingResultNo=SourcingResultNo FROM dbo.tbQuoteLine WHERE   QuoteLineId = @QuoteLineId
    UPDATE  dbo.tbQuoteLine
    SET     ReasonNo = @ReasonNo
          , Closed = 1
          , UpdatedBy = @UpdatedBy
          , NotQuoted = (SELECT NotQuoted FROM tbReason WHERE ReasonId = @ReasonNo)
          , DLUP = CURRENT_TIMESTAMP
		  , Reasons = @Reasons  -- Added as per client requirement 14-3-2018
    WHERE   QuoteLineId = @QuoteLineId

    SELECT  @RowsAffected = @@rowcount
    
    --now check to see if quote should be closed
    DECLARE @QuoteNo int;

    SELECT  @QuoteNo = QuoteNo
    FROM    dbo.tbQuoteLine
    WHERE   QuoteLineId = @QuoteLineId;
	-----------------------------------Set QuoteStatus-----------------------------------

	DECLARE @QuoteStatus INT = 0, @QuoteLineNotClosed INT = 0, @QuoteLineTotal INT = 0, @QuoteLineClosedCount INT = 0;

	SELECT @QuoteStatus = QuoteStatus FROM [dbo].[tbQuote] WHERE QuoteId = @QuoteNo;

	SELECT @QuoteLineNotClosed = COUNT(*) FROM [dbo].[tbQuoteLine] ql
		INNER JOIN [dbo].[tbSalesOrderLine] sl ON sl.QuoteLineNo = ql.QuoteLineId
		WHERE QuoteNo=@QuoteNo AND ql.QuoteLineId <> @QuoteLineId;

	IF (@QuoteLineNotClosed > 0) --Accepted OR Partially Accepted
	BEGIN
		UPDATE  [dbo].[tbQuote]
		SET     QuoteStatus = 7 --Partially Accepted
		WHERE   QuoteId = @QuoteNo;
	END
	ELSE
	BEGIN
		SELECT  @QuoteLineTotal=COUNT(*) FROM [dbo].[tbQuoteLine] WHERE QuoteNo=@QuoteNo;
		SELECT  @QuoteLineClosedCount=COUNT(*) FROM [dbo].[tbQuoteLine] WHERE QuoteNo=@QuoteNo AND Closed = 1;

		IF (@QuoteLineClosedCount = @QuoteLineTotal AND @QuoteLineNotClosed = 0)
		BEGIN
			UPDATE  [dbo].[tbQuote]                                              
			SET     QuoteStatus = 3 --Declined
			WHERE   QuoteId = @QuoteNo;
		END
		ELSE
		BEGIN
			UPDATE  [dbo].[tbQuote]
			SET     QuoteStatus = 8 --Partially Declined
			WHERE   QuoteId = @QuoteNo;
		END
	END

    ---------------------------------END Set QuoteStatus---------------------------------
    EXEC usp_update_Quote_CheckClosed @QuoteNo

	UPDATE tbSourcingResult set Closed = null where SourcingResultId = @SourcingResultNo

GO


