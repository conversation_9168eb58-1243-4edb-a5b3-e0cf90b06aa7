using System;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Reflection;
using System.ComponentModel;
using System.Resources;
using System.Collections.Generic;
using System.Text.RegularExpressions;
using System.Text;
using System.IO;
using System.Web.UI.WebControls;
using System.Collections;
using System.Threading;
using System.Globalization;
using System.Diagnostics;


/// <summary>
/// Functions, static class
/// </summary>
namespace Rebound.GlobalTrader.Site {
	public static partial class Functions {

		public static DateTime GetFirstDayOfMonth(DateTime dtmIn) {
			return new DateTime(dtmIn.Year, dtmIn.Month, 1);
		}

		public static DateTime GetLastDayOfMonth(DateTime dtmIn) {
			return GetFirstDayOfMonth(dtmIn).AddMonths(1).AddDays(-1);
		}

        public static DateTime GetOneWeekAgo(DateTime dtmIn) {
            return dtmIn.AddDays(-7);
        }

        public static DateTime GetOneWeekAhead(DateTime dtmIn) {
            return dtmIn.AddDays(7);
        }


	}
}