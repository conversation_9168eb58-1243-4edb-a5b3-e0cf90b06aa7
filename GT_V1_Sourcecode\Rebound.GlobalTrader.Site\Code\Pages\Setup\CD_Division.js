Type.registerNamespace("Rebound.GlobalTrader.Site.Pages.Setup");Rebound.GlobalTrader.Site.Pages.Setup.CD_Division=function(n){Rebound.GlobalTrader.Site.Pages.Setup.CD_Division.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Pages.Setup.CD_Division.prototype={get_ctlDivision:function(){return this._ctlDivision},set_ctlDivision:function(n){this._ctlDivision!==n&&(this._ctlDivision=n)},get_ctlDivisionMembers:function(){return this._ctlDivisionMembers},set_ctlDivisionMembers:function(n){this._ctlDivisionMembers!==n&&(this._ctlDivisionMembers=n)},get_ctlDocHeaderImage:function(){return this._ctlDocHeaderImage},set_ctlDocHeaderImage:function(n){this._ctlDocHeaderImage!==n&&(this._ctlDocHeaderImage=n)},initialize:function(){Rebound.GlobalTrader.Site.Pages.Setup.CD_Division.callBaseMethod(this,"initialize")},goInit:function(){this._ctlDivision.addSelectDivision(Function.createDelegate(this,this.ctlDivision_SelectDivision));Rebound.GlobalTrader.Site.Pages.Setup.CD_Division.callBaseMethod(this,"goInit")},dispose:function(){this.isDisposed||(this._ctlDivision&&this._ctlDivision.dispose(),this._ctlDivisionMembers&&this._ctlDivisionMembers.dispose(),this._ctlDocHeaderImage&&this._ctlDocHeaderImage.dispose(),this._ctlDivision=null,this._ctlDivisionMembers=null,this._ctlDocHeaderImage=null,Rebound.GlobalTrader.Site.Pages.Setup.CD_Division.callBaseMethod(this,"dispose"))},ctlDivision_SelectDivision:function(){this._ctlDivisionMembers._intDivisionID=this._ctlDivision._intDivisionID;this._ctlDivisionMembers.refresh();this._ctlDocHeaderImage._intDivisionID=this._ctlDivision._intDivisionID;this._ctlDocHeaderImage.refresh();this._ctlDivision._tbl.resizeColumns();this.showNuggets(!0)},showNuggets:function(n){this._ctlDivisionMembers.show(n);this._ctlDocHeaderImage.show(n)}};Rebound.GlobalTrader.Site.Pages.Setup.CD_Division.registerClass("Rebound.GlobalTrader.Site.Pages.Setup.CD_Division",Rebound.GlobalTrader.Site.Pages.Content,Sys.IDisposable);