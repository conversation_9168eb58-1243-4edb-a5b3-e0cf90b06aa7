﻿
GO

/****** Object:  StoredProcedure [dbo].[usp_select_Company_PurchaseInfo]    Script Date: 1/8/2025 7:37:52 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO



CREATE OR ALTER PROCEDURE [dbo].[usp_select_Company_PurchaseInfo]                
--******************************************************************************************                
--* Gets the Purchase info for a company, checking that tax and currency are valid                
--* (through table joins)                
--*                 
--* RP 12.01.2010:                
--* - get exchange rate                
--*                 
--* RP 17.11.2009:                
--* - new proc                
--*Marker     Changed by      Date          Remarks                    
--*[001]      Vinay           03/07/2013    CR:- Supplier Invoice                     
--******************************************************************************************                
    @CompanyId int                
AS    
   DECLARE @TaxByAddrss int 
   DECLARE @ShipViaNo int 
   DECLARE @ShipViaName nvarchar(max)  
   select top 1 @TaxByAddrss = TaxbyAddress from tbCompanyAddress where CompanyNo = @CompanyId and isnull(DefaultShipping,0) = 1  
   SELECT @ShipViaNo=ShipViaNo,@ShipViaName=ShipViaName  FROM  vwCompanyAddress  WHERE CompanyNo = @CompanyId  and  DefaultShipping=1                
    SELECT  co.CompanyId                
          , co.POApproved                
          , co.SupplierCode                
          , cu.CurrencyId AS POCurrencyNo                
          , cu.CurrencyCode AS POCurrencyCode                
          , cu.CurrencyDescription AS POCurrencyDescription                
          , cn.ContactId AS DefaultPOContactNo                
          , cn.ContactName AS DefaultPOContactName                
          , tm.TermsId AS POTermsNo                
          , tm.TermsName AS POTermsName                
          , tx.TaxId AS POTaxNo                
          , tx.TaxName AS POTaxName                
          --, sv.ShipViaId AS DefaultPurchaseShipViaNo                
          --, sv.ShipViaName AS DefaultPurchaseShipViaName              
		  , @ShipViaNo AS DefaultPurchaseShipViaNo                
          , @ShipViaName AS DefaultPurchaseShipViaName               
          , sv.Charge AS DefaultPurchaseFreightCharge                
          , sv.Cost AS DefaultPurchaseShippingCost                
          , co.DefaultPurchaseShipViaAccount                
          , co.PORating                
          , co.DLUP                
          , dbo.ufn_get_exchange_rate(cu.CurrencyId, getdate()) AS ExchangeRate               
          --[001] code start             
          , gcu.GlobalCurrencyId as GlobalCurrencyNo            
          , gcu.GlobalCurrencyName             
          --[001] code end            
          , co.ERAIReported          
          , co.DefaultPOShipCountryNo        
          , cl.CountryName as DefaultPOShipCountry       
          , co.CompanyName     
          , co.POApprovedBy    
          , co.POApprovedDate      
          , ISNULL(co.SupplierOnStop,0) AS SupplierOnStop  
		, @TaxByAddrss as TaxByAddrssNo  
		, ISNULL(co.WarehouseNo,0) AS WarehouseNo 
		, ISNULL(co.Inactive, 0) AS Inactive
    FROM    tbCompany co                
    LEFT JOIN dbo.tbCurrency cu ON co.POCurrencyNo = cu.CurrencyId                
                                   AND cu.Buy = 1                
                                   AND NOT cu.Inactive = 1                
    LEFT JOIN dbo.tbShipVia sv ON co.DefaultPurchaseShipViaNo = sv.ShipViaId                
    LEFT JOIN dbo.tbTax tx ON co.POTaxNo = tx.TaxId                
                              AND NOT tx.Inactive = 1                
    LEFT JOIN dbo.tbTerms tm ON co.POTermsNo = tm.TermsId                
                                AND NOT tm.Inactive = 1                
    LEFT JOIN dbo.tbContact cn ON co.DefaultPOContactNo = cn.ContactId                
                                  AND NOT cn.Inactive = 1                
    LEFT JOIN dbo.tbLogin lg ON co.Salesman = lg.LoginId              
                
    --[001] code start            
    LEFT JOIN tbGlobalCurrencyList gcu on cu.GlobalCurrencyNo=gcu.GlobalCurrencyId                 
    --[001] code end            
    LEFT JOIN tbCountry cl on cl.CountryId=co.DefaultPOShipCountryNo        
                  
    WHERE   CompanyId = @CompanyId  
  

GO


