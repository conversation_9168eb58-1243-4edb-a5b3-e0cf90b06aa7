﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE OR ALTER PROCEDURE [dbo].[usp_insert_ManufacturerLink] 
--
@ManufacturerNo			int ,
@SupplierCompanyNo		int ,
@ManufacturerRating		int		= Null ,
@SupplierRating			int		= Null ,
@UpdatedBy				int			= Null ,
@ManufacturerLinkId		int	Output
--
AS
--
BEGIN

--check there's not one already
DECLARE 	@Count 	integer

SELECT  	@Count = COUNT (*)
FROM  		dbo.tbManufacturerLink
WHERE   	ManufacturerNo		= @ManufacturerNo
AND			SupplierCompanyNo	= @SupplierCompanyNo

IF	@Count > 0 
	BEGIN
	UPDATE	dbo.tbManufacturerLink
	SET		ManufacturerRating	= @ManufacturerRating	
		,	SupplierRating		= @SupplierRating	
		,	UpdatedBy			= @UpdatedBy
		,	DLUP				= getdate()
	WHERE   ManufacturerNo		= @ManufacturerNo
	AND		SupplierCompanyNo	= @SupplierCompanyNo
	END 
	ELSE 
	BEGIN
	INSERT
	INTO	dbo.tbManufacturerLink
		(	
			ManufacturerNo	
		,	SupplierCompanyNo		
		,	ManufacturerRating	
		,	SupplierRating	
		,	UpdatedBy	
		)
	VALUES
		(
			@ManufacturerNo	
		,	@SupplierCompanyNo		
		,	@ManufacturerRating	
		,	@SupplierRating	
		,	@UpdatedBy
		)	
	END	

END
--
SET @ManufacturerLinkId = SCOPE_IDENTITY()
GO


