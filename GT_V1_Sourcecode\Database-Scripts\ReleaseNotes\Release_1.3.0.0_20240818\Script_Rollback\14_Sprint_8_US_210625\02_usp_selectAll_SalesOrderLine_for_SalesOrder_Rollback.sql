﻿
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

/*
===========================================================================================
TASK      			UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-210625]			Phuc Hoang			09-Aug-2024		CREATE          Sanctioned manufacturers need to be highlighted in red on the SOR PDF and SO screens.
===========================================================================================
*/
CREATE OR ALTER PROCEDURE [dbo].[usp_selectAll_SalesOrderLine_for_SalesOrder]             
--***************************************************************************************************                                                                                    
--[001]   Abhinav Saxena  03-June-2022  Add new column for BookingDetailId.       
--[002]   Ravi Bhushan    18-Sep-2023 RP-2339  AS6081 (Show AS6081 on detail screens)    
--***************************************************************************************************                                                                                    
    @SalesOrderId int                                                                                    
  , @IncludeInactive bit = 1 -- nota bene: default is to include inactive rows - this means the app will show them - but the SO Report will not                                                                                    
AS                           
 IF OBJECT_ID('tempdb..#TempPromiseReason') IS NOT NULL DROP TABLE #TempPromiseReason                          
 SELECT *  INTO  #TempPromiseReason FROM  tbStatusReason WHERE  Section = 'PS'                                                                                              
    SELECT   sol.SalesOrderLineId                                                                                    
          , sol.SalesOrderNo                                                                                    
          , sol.Taxable                                                                                    
          , sol.Quantity                                                                                    
          , sol.Price                                                                                    
          , sol.ROHS                                                                                    
          , sol.CustomerPart                                                                                    
          , sol.Part                                                                                    
          , sol.ManufacturerNo                                                                                    
          , ManufacturerCode                                                    
          , DateCode                                                                  
          , sol.ProductNo                                                                                 
          , pr.ProductName                    
    , pr.ProductDescription                                                                                    
, sol.PackageNo                                                                                    
 , pk.PackageName                                                                                    
          , isnull((SELECT  sum(x.Quantity)                                                
                    FROM    dbo.tbInvoicelineAllocation x                                                                               
                    WHERE   sol.SalesOrderLineId = x.SalesOrderLineNo                                                                                    
                  ), 0) AS QuantityShipped                                   
          , case WHEN isnull(sol.ServiceNo, 0) = 0 THEN isnull((SELECT  sum(y.QuantityAllocated)                                                                                    
                                FROM    dbo.tbAllocation y                                                     
                                                                WHERE   sol.SalesOrderLineId = y.SalesOrderLineNo                                                                                    
                                                ), 0)                           
                ELSE sol.Quantity                            
            END AS QuantityAllocated                                                            
          , case WHEN isnull(sol.ServiceNo, 0) = 0 THEN isnull((SELECT  sum(z.Quantity)                
                                  FROM    dbo.tbBackOrder z                                                                                    
                                                     WHERE   sol.SalesOrderLineId = z.SalesOrderLineNo                                                                                   
                                                               ), 0)                                                                           
                 ELSE 0                                                                                    
            END AS BackOrderQuantity                                                                               
          , sol.ServiceNo                                                                                    
          , sol.Closed                                                                
          , sol.Posted                                                                                    
          , sol.Inactive                                                                                    
          , sol.ServiceShipped                                                                                    
          , sv.Cost AS ServiceCost                                                                                    
          , sv.Price AS ServicePrice                                                                                  
          , sol.DatePromised                                                                                    
          , sol.Notes                                                                                    
          , sol.ProductSource                                                                                 
          , sol.ShipASAP                                                                                
          , sol.SOSerialNo                                                         
          , ISNULL(sol.IsIPO,0)   AS IsIPO                                                    
          , ISNULL(sol.IsChecked,0)  AS IsChecked                                                                     
          , CASE WHEN (Select Count(*) From tbSourcingResult where                                                                       
          (POHubCompanyNo IS NOT NULL)                                                                       
          AND (sol.SourcingResultNo  IS NOT NULL)                                                                      
          AND (sol.SourcingResultNo >0)                                                
          AND (SourcingResultId=sol.SourcingResultNo)                                                                       
          AND (sol.SourcingResultNo IS NOT NULL)                                                                      
          AND (sol.SourcingResultNo>0)                                                                      
          AND (SourcingTable='PQ' OR SourcingTable='OFPH' OR SourcingTable='EXPH'))=0 THEN NULL ELSE sol.SourcingResultNo END AS SourcingResultNo                                                   
         ,CASE WHEN (SELECT COUNT(InternalPurchaseOrderId) FROM tbInternalPurchaseOrder WHERE SalesOrderNo=@SalesOrderId)>0 THEN 1 ELSE 0 END AS IsIPOHeaderCreated                                            
                                                                  
         --,(SELECT top 1 InternalPurchaseOrderNumber FROM tbInternalPurchaseOrder ipo JOIN tbInternalPurchaseOrderLine ipol                                          
         --       ON ipo.InternalPurchaseOrderId=ipol.InternalPurchaseOrderNo                                  
         --         WHERE ipo.SalesOrderNo=@SalesOrderId AND ipol.SourcingResultNo=sol.SourcingResultNo          
         --) AS InternalPurchaseOrderNumber                                  
                                          
    ,( SELECT TOP 1 InternalPurchaseOrderNumber FROM tbInternalPurchaseOrder ipo JOIN tbInternalPurchaseOrderLine ipol                                          
        ON ipo.InternalPurchaseOrderId=ipol.InternalPurchaseOrderNo                                          
    WHERE  ipol.SourcingResultNo=sol.SourcingResultNo                        
         ) AS InternalPurchaseOrderNumber                                           
   , sol.ClonedID                                             
   , pr.DutyCode                                                  
   , sol.PrintHazardous                                          
   , sol.MSLLevel                                          
   , sol.PrintHazardous                                          
   , convert(varchar,sol.ContractNo) as ContractNo                                         
   ,case when sol.DateConfirmed is null then cast(0 as bit) else cast(1 as bit) end as IsConfirmed                                   
   ,sol.DateConfirmed                                  
   ,sol.DateChanged                                 
    ,ISNULL(sol.PromiseReasonNo,0) AS PromiseReasonNo                              
    ,ISNULL(statusReason.Name,'') AS PromiseReason                             
    ,ISNULL(c.WarehouseNo,0) AS    WarehouseNo                           
    ,ISNULL(W.WarehouseName,'') AS    WarehouseName                          
 ,dbo.ufn_get_reason_disableSalesOrderPost(sol.SalesOrderLineId,sol.SalesOrderNo,ISNULL(sol.IsIPO,0),ISNULL(sol.Posted,0)                        
 ,CASE WHEN(case WHEN isnull(sol.ServiceNo, 0) = 0 THEN isnull((SELECT  sum(y.QuantityAllocated)                                                                                      
                                FROM    dbo.tbAllocation y                                                       
                                                                WHERE   sol.SalesOrderLineId = y.SalesOrderLineNo                                                                                      
                                                ), 0)                                                                                      
                ELSE sol.Quantity                                                                                      
            END)>=(sol.Quantity-(isnull((SELECT  sum(x.Quantity)                                                  
                    FROM    dbo.tbInvoicelineAllocation x                                                                                 
                    WHERE   sol.SalesOrderLineId = x.SalesOrderLineNo                                                                                      
                  ), 0))) THEN 1 ELSE 0 END                        
   ,                        
   case when (isnull((SELECT  sum(x.Quantity)                                                  
                    FROM    dbo.tbInvoicelineAllocation x                                                                                 
                    WHERE   sol.SalesOrderLineId = x.SalesOrderLineNo                             
                  ), 0))>= sol.Quantity THEN 1 ELSE 0 END                        
   ,                        
 sol.Inactive,sol.Closed) PostDisableReason                      
 , isnull(pr.IsHazardous,0) as IsProdHazardous                          
 ,isnull(pr.IsOrderViaIPOonly,0) as IsOrderViaIPOonly                            
     ,sol.ECCNCode                   
  ,(select top 1 ECCNId  from dbo.tbECCN m   where m.ECCNCode= sol.ECCNCode )  as ECCNCodeNo                
  --, bdi.BookingDetailId             
  --, ISNULL(sol.ISEIToken,0) AS ISEIToken          
  ,isnull((select top 1 m.ECCNStatus from dbo.tbECCN m   where m.ECCNCode= sol.ECCNCode ),0)  as IsECCNWarning        
  ,sol.IsEUUPDFAvailable      
  ,EUUPDF.DLUP as EUUPDFUploadDate      
  ,(select top 1 EmployeeName from tbLogin where LoginId = EUUPDF.UpdatedBy) as EUUUPDFploadName     , ISNULL(sol.AS6081,0) AS AS6081 --[002]    
    FROM  dbo.tbSalesOrderLine sol                                                                           
    LEFT JOIN dbo.tbProduct pr ON sol.ProductNo = pr.ProductId                                                                                    
    LEFT JOIN dbo.tbPackage pk ON sol.PackageNo = pk.PackageId                                           
    LEFT JOIN dbo.tbManufacturer mf ON sol.ManufacturerNo = mf.ManufacturerId                        
    LEFT JOIN dbo.tbService sv ON sv.ServiceId = sol.ServiceNo                                
    --LEFT JOIN [dbo].[tbStatusReason]  statusReason ON sol.PromiseReasonNo = statusReason. StatusReasonId                            
    LEFT JOIN #TempPromiseReason  statusReason ON sol.PromiseReasonNo = statusReason. StatusReasonId                             
    LEFT JOIN tbSalesOrder so on so.SalesOrderId   = sol.SalesOrderNo                          
    LEFT JOIN tbCompany c ON c.CompanyId = so.CompanyNo                            
    LEFT JOIN dbo.tbWarehouse w ON W.WarehouseId = c.WarehouseNo                
 --LEFT JOIN tbEI_BookingDetailsInfo bdi ON bdi.SOLineNo=sol.SalesOrderLineId      
 LEFT JOIN tbSOLineEUUPDF EUUPDF on EUUPDF.SOLineNo = sol.SalesOrderLineId      
   WHERE   sol.SalesOrderNo = @SalesOrderId                                                                                    
            AND sol.Inactive IN (0, @IncludeInactive)                            
            --AND ( ISNULL(sol.PromiseReasonNo,0)=0 OR statusReason.Section = 'PS')                                                                         
    ORDER BY SalesOrderLineId 
GO
