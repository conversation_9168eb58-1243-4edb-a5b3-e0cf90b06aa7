﻿<%@ Control Language="C#" AutoEventWireup="true" CodeBehind="EXCELDocuments_Add.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Forms.EXCELDocuments_Add" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Form:DesignBase ID="ctlDB" runat="server" ShowRequiredFieldsExplanation="true" ShowQuickHelp="false">
	<Links>
		<ReboundUI:IconButton ID="ibtnSave" runat="server" IconGroup="Nugget" IconTitleResource="Save" IconCSSType="Save" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
		<ReboundUI:IconButton ID="ibtnCancel" runat="server" IconGroup="Nugget" IconTitleResource="Cancel" IconCSSType="Cancel" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
	</Links>
	<Explanation><%=String.Format(Functions.GetGlobalResource("FormExplanations", "EXCEL_Add"), "6")%></Explanation>
	<Content>
		<ReboundUI_Table:Form id="frm" runat="server" >
			<ReboundUI_Form:FormField id="ctlFile" runat="server" FieldID="flFileUpload" ResourceTitle="File" IsRequiredField="true" >
				<Field><ReboundUI:FileUpload ID="flFileUpload" runat="server" Size="50" AllowedExtensions="xlsx||xls||csv"  MaxFileSizeInMb="6.3" /></Field>				
			</ReboundUI_Form:FormField>			
			<ReboundUI_Form:FormField id="ctlCaption" IsRequiredField="true" runat="server" FieldID="txtCaption" ResourceTitle="Caption">
				<Field><ReboundUI:ReboundTextBox ID="txtCaption" TextMode="multiLine" runat="server" Width="400" Rows="2" /></Field>
			</ReboundUI_Form:FormField>
		</ReboundUI_Table:Form>
	</Content>
</ReboundUI_Form:DesignBase>
