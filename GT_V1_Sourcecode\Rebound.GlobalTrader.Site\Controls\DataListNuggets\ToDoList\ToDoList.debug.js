﻿//Marker Changed by Date               Remarks
//[001] Bhooma          18/Feb/2022        CR:- Create New DataList Nugget for view to do list
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DataListNuggets");
Rebound.GlobalTrader.Site.Controls.DataListNuggets.ToDoList = function (element) {
    Rebound.GlobalTrader.Site.Controls.DataListNuggets.ToDoList.initializeBase(this, [element]);
    this._frmConfirm = null;
    this._frmAdd = null;
    this._frmEdit = null;
    this._intQuoteID = null;
    this._intCompanyID = 0;
    this._intToDoID = -1;
    this._intTaskCategoryID = 1;
    this._strCompanyName = "";
    this._companyCategoryId = 1;
    this._quoteCategoryId = 2;
    this._quoteMinReminderDate = null;
};
Rebound.GlobalTrader.Site.Controls.DataListNuggets.ToDoList.prototype = {
    get_ibtnExportCSV: function () { return this._ibtnExportCSV; }, set_ibtnExportCSV: function (v) { if (this._ibtnExportCSV !== v) this._ibtnExportCSV = v; },
    get_sortIndex: function () { return this._sortIndex; }, set_sortIndex: function (v) { if (this._sortIndex !== v) this._sortIndex = v; },
    get_sortDir: function () { return this._sortDir; }, set_sortDir: function (v) { if (this._sortDir !== v) this._sortDir = v; },
    get_pageIndex: function () { return this._pageIndex; }, set_pageIndex: function (v) { if (this._pageIndex !== v) this._pageIndex = v; },
    get_pageSize: function () { return this._pageSize; }, set_pageSize: function (v) { if (this._pageSize !== v) this._pageSize = v; },
    get_ibtnAdd: function () { return this._ibtnAdd; }, set_ibtnAdd: function (v) { if (this._ibtnAdd !== v) this._ibtnAdd = v; },
    get_ibtnEdit: function () { return this._ibtnEdit; }, set_ibtnEdit: function (v) { if (this._ibtnEdit !== v) this._ibtnEdit = v; },

    get_ibtnDelete: function () { return this._ibtnDelete; }, set_ibtnDelete: function (v) { if (this._ibtnDelete !== v) this._ibtnDelete = v; },
    get_ibtnMarkComplete: function () { return this._ibtnMarkComplete; }, set_ibtnMarkComplete: function (v) { if (this._ibtnMarkComplete !== v) this._ibtnMarkComplete = v; },
    get_ibtnMarkIncomplete: function () { return this._ibtnMarkIncomplete; }, set_ibtnMarkIncomplete: function (v) { if (this._ibtnMarkIncomplete !== v) this._ibtnMarkIncomplete = v; },
    get_intCompanyID: function () { return this._intCompanyID; }, set_intCompanyID: function (v) { if (this._intCompanyID !== v) this._intCompanyID = v; },
    get_strCompanyName: function () { return this._strCompanyName; }, set_strCompanyName: function (v) { if (this._strCompanyName !== v) this._strCompanyName = v; },
    get_ctlSelect: function () { return this._ctlSelect; }, set_ctlSelect: function (v) { if (this._ctlSelect !== v) this._ctlSelect = v; },
    get_intToDoID: function () { return this._intToDoID; }, set_intToDoID: function (v) { if (this._intToDoID !== v) this._intToDoID = v; },
    //get_table: function () { return this._table; }, set_table: function (v) { if (this._table !== v) this._table = v; },
    initialize: function () {
        //this._table.addSelectedIndexChanged(Function.createDelegate(this, this.tableSelect));
        this._table.addMultipleSelectionChanged(Function.createDelegate(this, this.tableSelect));
        this.addSetupDataCallEvent(Function.createDelegate(this, this.setupDataCall));
        this.addGetDataOKEvent(Function.createDelegate(this, this.getDataOK));
        this.addInitCompleteEvent(Function.createDelegate(this, this.initAfterBaseIsReady));
        this._strPathToData = "controls/DataListNuggets/ToDoList";
        this._strDataObject = "ToDoList";
        Rebound.GlobalTrader.Site.Controls.DataListNuggets.ToDoList.callBaseMethod(this, "initialize");
        if (this._ibtnExportCSV) $R_IBTN.addClick(this._ibtnExportCSV, Function.createDelegate(this, this.exportCSV));//[001]

        if (this._ibtnAdd) $R_IBTN.addClick(this._ibtnAdd, Function.createDelegate(this, this.showAddForm));
        this._frmAdd = $find(this._aryFormIDs[2]);
        this._frmAdd.addCancel(Function.createDelegate(this, this.hideAddForm));
        this._frmAdd.addSaveComplete(Function.createDelegate(this, this.addComplete));
        //this._table.addMultipleSelectionChanged(Function.createDelegate(this, this.selectionMade));
        //edit
        if (this._ibtnEdit) $R_IBTN.addClick(this._ibtnEdit, Function.createDelegate(this, this.showEditForm));
        this._frmEdit = $find(this._aryFormIDs[0]);
        this._frmEdit.addCancel(Function.createDelegate(this, this.hideEditForm));
        this._frmEdit.addSaveComplete(Function.createDelegate(this, this.editComplete));

        //confirm
        if (this._ibtnDelete) $R_IBTN.addClick(this._ibtnDelete, Function.createDelegate(this, this.showDeleteForm));
        if (this._ibtnMarkComplete) $R_IBTN.addClick(this._ibtnMarkComplete, Function.createDelegate(this, this.showMarkCompleteForm));
        if (this._ibtnMarkIncomplete) $R_IBTN.addClick(this._ibtnMarkIncomplete, Function.createDelegate(this, this.showMarkIncompleteForm));
        this._frmConfirm = $find(this._aryFormIDs[1]);
        this._frmConfirm.addNotConfirmed(Function.createDelegate(this, this.hideConfirmForm));
        this._frmConfirm.addSaveComplete(Function.createDelegate(this, this.confirmComplete));

        //this.setFieldValue("ctlCustomerName", this._strCompanyName);
        if (this._intCompanyID > 0) {
            $('#ctl00_cphMain_ctlToDoList_ctlDB_ctl17_ctlFilter_ctlCustomerName_txt').val(this._strCompanyName);
            $("#ctl00_cphMain_ctlToDoList_ctlDB_ctl17_ctlFilter_ctlCustomerName_chkOn").prop('checked', true);
            $('#ctl00_cphMain_ctlToDoList_ctlDB_ctl17_ctlFilter_ctlCustomerName_lblField').removeClass();
            $("#ctl00_cphMain_ctlToDoList_ctlDB_ctl17_ctlFilter_ctlCustomerName_txt").focus();
        }
        this._ctlSelect.registerTable(this._table);
        this.addRefreshEvent(Function.createDelegate(this, this.initAfterBaseIsReady));
        //register dropdown Task Category change
        $find(this.getFilterField("ctlTaskCategory").get_id())._element.setAttribute("onchange", String.format("$find(\"{0}\").onCategoryChange()", this._element.id));
    },

    initAfterBaseIsReady: function () {
        //trigger event change for first load
        this.onCategoryChange();
        this.getData();
    },
    getDataError: function (args) {
        this.showError(true, args.get_ErrorMessage());
    },
    dispose: function () {
        if (this.isDisposed) return;
        if (this._ibtnExportCSV) $R_IBTN.clearHandlers(this._ibtnExportCSV);//[001]
        this._ibtnExportCSV = null;//[001]
        this._intCompanyID = null;
        this._strCompanyName = null;
        this._intQuoteID = null;
        if (this._ctlSelect) this._ctlSelect.dispose();
        this._ctlSelect = null;
        Rebound.GlobalTrader.Site.Controls.DataListNuggets.ToDoList.callBaseMethod(this, "dispose");
    },

    setupDataCall: function () {
        var strAction = "GetData";
        this._objData.set_DataAction(strAction);
        this._objData.addParameter("id", this._intToDoID);
        //[002] end
    },
    getDataOK: function () {
        this._ctlSelect.resetCount();
        for (var i = 0, l = this._objResult.Results.length; i < l; i++) {
            var row = this._objResult.Results[i];
            let ref = '';
            if (row.TaskCategoryNo == 2) {
                ref = $RGT_nubButton_Quote(row.QuoteNo, row.QuoteNumber);
            }
            var aryData = [
                //"&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;" + $R_FN.setCleanTextValue(row.CreatedDateFrom)
                //, $R_FN.setCleanTextValue(row.CreatedDateTo)
                "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;" + $R_FN.setCleanTextValue(row.TaskDateFrom)
                , $R_FN.setCleanTextValue(row.TaskDateTo)
                , row.HasReminder == true ? $R_FN.setCleanTextValue(row.TaskReminderDate) : ""
                , $R_FN.setCleanTextValue(row.TaskTitle)
                //, $R_FN.setCleanTextValue(row.TaskType)
                , $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.TaskType), $R_FN.setCleanTextValue(row.TaskCategory))
                , $R_FN.setCleanTextValue(row.TaskStatus)
                , $R_FN.setCleanTextValue(row.CustomerName)
                , $R_FN.setCleanTextValue(row.SalesPersonName)
                , ref
            ];
            //var strCSS = (row.IsComplete) ? "toDoCompleteNoReminder " : "";
            //strCSS += (row.HasReminder && !row.IsComplete) ? "toDoCompleteNoReminder" : "NoReminder";
            var strCSS = (row.IsComplete) ? "toDoComplete" : "toDoIncomplete";
            strCSS += (row.HasReminder && !row.IsComplete) ? "Reminder" : "NoReminder";
            var objExtra = { Complete: row.IsComplete, TaskCategoryNo: row.TaskCategoryNo, QuoteNo: row.QuoteNo };
            this._table.addRow(aryData, row.ToDoListId, false, objExtra, strCSS);
            aryData = null; row = null;
            this._table._intCountSelected = 0;
        }
    },
    //start [001]
    exportCSV: function () {
        this.getData_Start();
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/DataListNuggets/ToDoList");
        obj.set_DataObject("ToDoList");
        obj.set_DataAction("ExportToCSV");
        obj._intTimeoutMilliseconds = 500 * 1000;
        obj.addParameter("SortIndex", this._sortIndex);
        obj.addParameter("SortDir", this._sortDir);
        obj.addParameter("PageIndex", this._pageIndex);
        obj.addParameter("PageSize", this._pageSize);
        //obj.addParameter("CreatedDateFrom", this.getFilterFieldValue("ctlCreatedDateFrom"));
        //obj.addParameter("CreatedDateTo", this.getFilterFieldValue("ctlCreatedDateTo"));
        obj.addParameter("TaskReminderDate", this.getFilterFieldValue("ctlTaskReminderDate"));
        obj.addParameter("TaskDateFrom", this.getFilterFieldValue("ctlTaskDateFrom"));
        obj.addParameter("TaskDateTo", this.getFilterFieldValue("ctlTaskDateTo"));
        obj.addParameter("TaskType", this.getFilterFieldValue("ctlTaskType"));
        obj.addParameter("TaskStatus", this.getFilterFieldValue("ctlTaskStatus"));
        obj.addParameter("CustomerName", this.getFilterFieldValue("ctlCustomerName"));
        obj.addParameter("Salesman", this.getFilterFieldValue("ctlSalesperson"));

        obj.addDataOK(Function.createDelegate(this, this.exportCSV_OK));
        obj.addError(Function.createDelegate(this, this.exportCSV_Error));
        obj.addTimeout(Function.createDelegate(this, this.exportCSV_Error));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;

    },

    exportCSV_OK: function (args) {
        var res = args._result;
        if (res.Filename) {
            //add the date to the file to force the latest version to be returned (not from cache)
            var dt = new Date();
            location.href = String.format("{0}?t={1}", res.Filename, dt.getTime());
            dt = null;
        }
        this.getDataOK_End();
    },

    exportCSV_Error: function (args) {
        this.showError(true, args.get_ErrorMessage());
    },
    tableSelect: function () {
        this._intToDoID = this._table._aryCurrentValues[0];
        this._intTaskCategoryID = this._table.getSelectedExtraData(this._table._arySelectedIndexes[0]).TaskCategoryNo;
        this._intQuoteID = this._table.getSelectedExtraData(this._table._arySelectedIndexes[0]).QuoteNo;
        this.enableButtons(true);
    },

    enableButtons: function (bln) {
        if (bln) {
            $R_IBTN.enableButton(this._ibtnEdit, this._table._intCountSelected == 1);
            $R_IBTN.enableButton(this._ibtnDelete, this._table._intCountSelected > 0);
            if (this._table._intCountSelected > 1) {
                $R_IBTN.showButton(this._ibtnMarkComplete, true);
                $R_IBTN.showButton(this._ibtnMarkIncomplete, true);
            } else if (this._table._intCountSelected == 1) {
                if (this._table._arySelectedIndexes.length > 0) {
                    $R_IBTN.showButton(this._ibtnMarkComplete, !this._table.getSelectedExtraData(this._table._arySelectedIndexes[0]).Complete);
                    $R_IBTN.showButton(this._ibtnMarkIncomplete, this._table.getSelectedExtraData(this._table._arySelectedIndexes[0]).Complete);
                }

            }
            $R_IBTN.enableButton(this._ibtnMarkComplete, this._table._intCountSelected > 0);
            $R_IBTN.enableButton(this._ibtnMarkIncomplete, this._table._intCountSelected > 0);
        } else {
            $R_IBTN.enableButton(this._ibtnEdit, false);
            $R_IBTN.enableButton(this._ibtnDelete, false);
            $R_IBTN.enableButton(this._ibtnMarkComplete, false);
            $R_IBTN.enableButton(this._ibtnMarkIncomplete, false);
        }
    },

    showEditForm: function () {
        this._frmEdit._intToDoID = this._intToDoID;
        this._frmEdit._intCategoryID = this._intTaskCategoryID;
        this.showForm(this._frmEdit, true);
    },

    hideEditForm: function () {

        this.showForm(this._frmEdit, false);
    },

    editComplete: function () {
        this.hideEditForm();
        this.showSavedOK(true, $R_RES.ChangesSavedSuccessfully);
        this._intToDoID = 0;
        this.getData();
    },

    showDeleteForm: function () {
        this._frmConfirm.changeMode("DELETE");
        this.showConfirmForm();
    },

    showMarkCompleteForm: function () {
        this._frmConfirm.changeMode("MARK_COMPLETE");
        this.showConfirmForm();
    },

    showMarkIncompleteForm: function () {
        this._frmConfirm.changeMode("MARK_INCOMPLETE");
        this.showConfirmForm();
    },

    showConfirmForm: function () {
        this._frmConfirm._aryToDoIDs = this._table._aryCurrentValues;
        this.showForm(this._frmConfirm, true);
    },

    hideConfirmForm: function () {
        this.showForm(this._frmConfirm, false);
    },

    confirmComplete: function () {
        this.hideConfirmForm();
        this._intToDoID = 0;
        this.getData();
    },

    showAddForm: function () {
        this._frmAdd.setFormFieldsToDefaults();
        this._frmAdd.setFieldValue("ctlDueTime", "09:00");
        this._frmAdd.setFieldValue("ctlReminderTime", "09:00");
        this.showForm(this._frmAdd, true);
    },

    hideAddForm: function () {
        this._frmAdd.resetFormData();
        this.showForm(this._frmAdd, false);
    },

    addComplete: function () {
        this.hideAddForm();
        this.showSavedOK(true, $R_RES.ChangesSavedSuccessfully);
        this._intToDoID = 0; // reset ToDo ID once set not getting cleared
        this.getData();
    },
    //end [001]
    onCategoryChange: function () {
        let selectedCategory = this.getFilterFieldValue("ctlTaskCategory");
        this.getFilterField("ctlQuoteNumber").show(selectedCategory == this._quoteCategoryId);
    }
};




Rebound.GlobalTrader.Site.Controls.DataListNuggets.ToDoList.registerClass("Rebound.GlobalTrader.Site.Controls.DataListNuggets.ToDoList", Rebound.GlobalTrader.Site.Controls.DataListNuggets.Base, Sys.IDisposable);
