///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//Marker     Date           Changed By      Remarks
//[001]      06/06/2018     Aashu <PERSON>     Save expedite notes for CRMA
//[002]      Umendra Gupta   21-Jan-2019  Add View Tree Button.
//[RP-2339]	Ravi		11-10-2023		AS6081 GT Documents - Show AS6081 on detail screens
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Nuggets");

Rebound.GlobalTrader.Site.Controls.Nuggets.CRMAMainInfo = function(element) { 
	Rebound.GlobalTrader.Site.Controls.Nuggets.CRMAMainInfo.initializeBase(this, [element]);
	this._intCRMAID = -1;
	this._IsHubAutoCRMA=false;
};

Rebound.GlobalTrader.Site.Controls.Nuggets.CRMAMainInfo.prototype = {

	get_intCRMAID: function() { return this._intCRMAID; }, set_intCRMAID: function(value) { if (this._intCRMAID !== value) this._intCRMAID = value; },
	get_intContactID: function() { return this._intContactID; }, set_intContactID: function(value) { if (this._intContactID !== value) this._intContactID = value; },
	get_ibtnEdit: function () { return this._ibtnEdit; }, set_ibtnEdit: function (value) { if (this._ibtnEdit !== value) this._ibtnEdit = value; },
    //[001] start
	get_ibtnAdd: function () { return this._ibtnAdd; }, set_ibtnAdd: function (v) { if (this._ibtnAdd !== v) this._ibtnAdd = v; },

	get_tblExpHist: function () { return this._tblExpHist; }, set_tblExpHist: function (value) { if (this._tblExpHist !== value) this._tblExpHist = value; },
	get_pnlExpHist: function () { return this._pnlExpHist; }, set_pnlExpHist: function (value) { if (this._pnlExpHist !== value) this._pnlExpHist = value; },
	get_pnlLoadingExpHist: function () { return this._pnlLoadingExpHist; }, set_pnlLoadingExpHist: function (value) { if (this._pnlLoadingExpHist !== value) this._pnlLoadingExpHist = value; },
	get_pnlExpHistError: function () { return this._pnlExpHistError; }, set_pnlExpHistError: function (value) { if (this._pnlExpHistError !== value) this._pnlExpHistError = value; },
    //[001] end
    //[002] start
	get_ibtnViewTree: function () { return this._ibtnViewTree; }, set_ibtnViewTree: function (value) { if (this._ibtnViewTree !== value) this._ibtnViewTree = value; },
    //[002] end
	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.Nuggets.CRMAMainInfo.callBaseMethod(this, "initialize");	
		
		//nugget events
		this.addRefreshEvent(Function.createDelegate(this, this.getData));
		
		//edit
		if (this._ibtnEdit) {
			$R_IBTN.addClick(this._ibtnEdit, Function.createDelegate(this, this.showEditForm));
			this._frmEdit = $find(this._aryFormIDs[0]);
			this._frmEdit._intCRMAID = this._intCRMAID;
			this._frmEdit.addCancel(Function.createDelegate(this, this.cancelEdit));
			this._frmEdit.addSaveComplete(Function.createDelegate(this, this.saveEditComplete));
		}
	    //[001] start
		if (this._ibtnAdd) {
		    $R_IBTN.addClick(this._ibtnAdd, Function.createDelegate(this, this.showAddForm));
		    this._frmAdd = $find(this._aryFormIDs[1]);
		    this._frmAdd.addCancel(Function.createDelegate(this, this.cancelAddForm));
		    this._frmAdd.addSaveComplete(Function.createDelegate(this, this.saveAddComplete));

		}
	    //[001] end
	    //[002] code start
		if (this._ibtnViewTree)
		    $R_IBTN.addClick(this._ibtnViewTree, Function.createDelegate(this, this.OpenDocTree));
	    //[002] code end
		//initial action
		if (!this._blnIsNoDataFound && !this._blnHasInitialData) this.getData();
	},

	dispose: function() { 
		if (this.isDisposed) return;
		if (this._ibtnEdit) $R_IBTN.clearHandlers(this._ibtnEdit);
	    //[001] start
		if (this._ibtnAdd) $R_IBTN.clearHandlers(this._ibtnAdd);
		this._ibtnAdd = null;
        //[001] end
		if (this._frmEdit) this._frmEdit.dispose();
		this._frmEdit = null;
		this._ibtnEdit = null;
		this._intCRMAID = null;
		this._intContactID = null;
		Rebound.GlobalTrader.Site.Controls.Nuggets.CRMAMainInfo.callBaseMethod(this, "dispose");
	},

	getData: function() { 
		this.getData_Start();
		var obj = new Rebound.GlobalTrader.Site.Data();
		obj.set_PathToData("controls/Nuggets/CRMAMainInfo");
		obj.set_DataObject("CRMAMainInfo");
		obj.set_DataAction("GetData");
		obj.addParameter("id", this._intCRMAID);
		obj.addDataOK(Function.createDelegate(this, this.getDataOK));
		obj.addError(Function.createDelegate(this, this.getDataError));
		obj.addTimeout(Function.createDelegate(this, this.getDataError));
		$R_DQ.addToQueue(obj);
		$R_DQ.processQueue();
		this.getExpHist();
		obj = null;
	},

	getDataOK: function(args) { 
		var res = args._result;
		this.setFieldValue("ctlCustomerName", $RGT_nubButton_Company(res.CustomerNo, res.Customer, null, null, null, res.CustomerAdvisoryNotes));
		this.setFieldValue("ctlWarehouse", $R_FN.setCleanTextValue(res.Warehouse));
		if(this._frmEdit)this._frmEdit.setFieldValue("ctlWarehouse_Label", $R_FN.setCleanTextValue(res.Warehouse));
		this.setFieldValue("ctlContact", $RGT_nubButton_Contact(res.ContactNo, res.Contact));
		this.setFieldValue("ctlAuthoriser", $R_FN.setCleanTextValue(res.Authoriser));
		this.setFieldValue("ctlDivision", $R_FN.setCleanTextValue(res.Division));
		this.setFieldValue("ctlRMADate", $R_FN.setCleanTextValue(res.RMADate));
		this.setFieldValue("ctlInvoice", $RGT_nubButton_Invoice(res.InvoiceNo, res.Invoice));
		this.setFieldValue("ctlSalesOrder", $RGT_nubButton_SalesOrder(res.SalesOrderNo, res.SalesOrder));
		this.setFieldValue("ctlShipVia", $R_FN.setCleanTextValue(res.ShipVia));
		this.setFieldValue("ctlIncoterm", $R_FN.setCleanTextValue(res.Incoterm));
		this.setFieldValue("ctlShippingAccountNo", $R_FN.setCleanTextValue(res.ShippingAccountNo));
		this.setFieldValue("ctlNotes", $R_FN.setCleanTextValue(res.Notes));
		this.setFieldValue("ctlInstructions", $R_FN.setCleanTextValue(res.Instructions));
		this.setFieldValue("ctlRefNo", res.RefNumber);
		this.showField("ctlRefNo", res.RefNumber>0);

		this.setFieldValue("hidNo", res.CRMANumber);
		this.setFieldValue("hidCustomer", $R_FN.setCleanTextValue(res.Customer));
		this.setFieldValue("hidWarehouseNo", res.WarehouseNo);
		this.setFieldValue("hidCustomerNo", res.CustomerNo);
		this.setFieldValue("hidShipViaNo", res.ShipViaNo);
		this.setFieldValue("hidSalesOrder", res.SalesOrder);
		this.setFieldValue("hidSalesOrderNo", res.SalesOrderNo);
		this.setFieldValue("hidContact", $R_FN.setCleanTextValue(res.Contact));
		this.setFieldValue("hidContactNo", res.ContactNo);
		this.setFieldValue("hidInvoice", res.Invoice);
		this.setFieldValue("hidInvoiceNo", res.InvoiceNo);
		this.setFieldValue("hidDivisionNo", res.DivisionNo);
		this.setFieldValue("hidAuthorisedBy", res.AuthorisedBy);
		this.setFieldValue("hidIncotermNo", res.IncotermNo);
		this.setFieldValue("ctlCustomerRejectionNo", res.CustomerRejectionNo);
		 this._IsHubAutoCRMA=res.IsHubAutoCRMA;
		//[RP-2339] start
		this.setFieldValue("ctlAS6081", res.AS6081 == 1 ? "Yes" : "No");
		$R_FN.highlightBackgroundColorOfText("ctl00_cphMain_ctlMainInfo_ctlDB_ctl13_ctlAS6081_lbl", res.AS6081);
		//[RP - 2339] end
		this.setDLUP(res.DLUP);
		this.getDataOK_End();
	},

	getDataError: function(args) {
		this.showError(true, args.get_ErrorMessage());
	},

	showEditForm: function() {
		this._frmEdit._intLineID = this._intLineID;
		this._frmEdit.setFieldValue("ctlCustomer", this.getFieldValue("hidCustomer"));
		this._frmEdit.setFieldValue("ctlContact", this.getFieldValue("hidContact"));
		this._frmEdit.setFieldValue("ctlDivision", this.getFieldValue("hidDivisionNo"));
		this._frmEdit.setFieldValue("ctlWarehouse", this.getFieldValue("hidWarehouseNo"));
		this._frmEdit.setFieldValue("ctlAuthorisedBy", this.getFieldValue("hidAuthorisedBy"));
		this._frmEdit.setFieldValue("ctlRMADate", this.getFieldValue("ctlRMADate"));
		this._frmEdit.setFieldValue("ctlInvoice", this.getFieldValue("hidInvoice"));
		this._frmEdit.setFieldValue("ctlSalesOrder", this.getFieldValue("hidSalesOrder"));
		this._frmEdit.setFieldValue("ctlShipVia", this.getFieldValue("hidShipViaNo"));
		this._frmEdit.setFieldValue("ctlShippingAccount", this.getFieldValue("ctlShippingAccountNo"));
		this._frmEdit.setFieldValue("ctlNotes", this.getFieldValue("ctlNotes"));
		this._frmEdit.setFieldValue("ctlInstructions", this.getFieldValue("ctlInstructions"));
		this._frmEdit.setFieldValue("ctlIncoterm", this.getFieldValue("hidIncotermNo"));
		
		
		this._frmEdit.showField("ctlRaisedByLbl",this._IsHubAutoCRMA);
        this._frmEdit.showField("ctlAuthorisedBy",!this._IsHubAutoCRMA);
        this._frmEdit._IsHubAutoCRMA=this._IsHubAutoCRMA;
        if(this._IsHubAutoCRMA)
        {
        this._frmEdit._hidRaisedByNo=this.getFieldValue("hidAuthorisedBy");
        }
        this._frmEdit.setFieldValue("ctlAuthorisedBy",this.getFieldValue("hidAuthorisedBy"));
        this._frmEdit.setFieldValue("ctlRaisedByLbl", this.getFieldValue("ctlAuthoriser"));
        this._frmEdit.showField("ctlShipViaLbl",this._IsHubAutoCRMA);
        this._frmEdit.showField("ctlShipVia",!this._IsHubAutoCRMA);
        
        this._frmEdit.showField("ctlWarehouse_Label",this._IsHubAutoCRMA);
        this._frmEdit.showField("ctlWarehouse",!this._IsHubAutoCRMA);
         if(this._IsHubAutoCRMA)
        {
        this._frmEdit._clientWarehouseNo=this.getFieldValue("hidWarehouseNo");
        }
        
        if(this._IsHubAutoCRMA)
        {
        this._frmEdit._hidShipViaNo=this.getFieldValue("hidShipViaNo");
        }
       
        this._frmEdit.setFieldValue("ctlShipViaLbl", this.getFieldValue("ctlShipVia"));
        this._frmEdit.setFieldValue("ctlCustomerRejectionNo", this.getFieldValue("ctlCustomerRejectionNo"));
		this.showForm(this._frmEdit, true);
	},
	
	hideEditForm: function() {
		this.showForm(this._frmEdit, false);
	},
	
	cancelEdit: function() {
		this.hideEditForm();
	},
	
	editFormShown: function() {
	},
	
	saveEditComplete: function() {
		this.hideEditForm();
		this.showSavedOK(true, $R_RES.ChangesSavedSuccessfully);
		this.getData();
	},
	showAddForm: function () {
	
	    this._frmAdd._intCustomerRMAID = this._intCRMAID;
        this._frmAdd.setFieldValue("ctlExpediteNotes", "");
	    this.showForm(this._frmAdd, true);
	},
	cancelAddForm: function () {
	    this.showForm(this._frmAdd, false);
	    this.showContent(true);
	},

	saveAddComplete: function () {
	    this.showForm(this._frmAdd, false);
	    this.showSavedOK(true, $R_RES.ChangesSavedSuccessfully);
	    this.getData();

	},
    //[001] start
	getExpHist: function () {
	    this.showExpHistError(false);
	    this.showLoadingExpHist(true);
	    var obj = new Rebound.GlobalTrader.Site.Data();
	    obj.set_PathToData("controls/Nuggets/CRMAMainInfo");
	    obj.set_DataObject("CRMAMainInfo");
	    obj.set_DataAction("GetExpediteHistory");
	    obj.addParameter("id", this._intCRMAID);
	    obj.addDataOK(Function.createDelegate(this, this.getExpHistOK));
	    obj.addError(Function.createDelegate(this, this.getExpHistError));
	    obj.addTimeout(Function.createDelegate(this, this.getExpHistError));
	    $R_DQ.addToQueue(obj);
	    $R_DQ.processQueue();
	    obj = null;
	},

	getExpHistOK: function (args) {
	    
	    res = args._result;
	    this.showLoadingExpHist(false);
	    this.showExpHistError(false);
	    this._tblExpHist.clearTable();
	    this.processExpHistList(this._tblExpHist);
	    this._tblExpHist.resizeColumns();
	},

	getExpHistError: function (args) {
	    this.showLoadingExpHist(false);
	    this.showExpHistError(true, args.get_ErrorMessage());
	},

	showLoadingExpHist: function (blnShow) {
	    $R_FN.showElement(this._pnlLoadingExpHist, blnShow);
	    $R_FN.showElement(this._pnlExpHist, !blnShow);
	    this.showLoading(blnShow);
	    if (blnShow) $R_FN.showElement(this._pnlGetExpHist, false);
	},

	showExpHistError: function (blnShow, strMessage) {
	    $R_FN.showElement(this._pnlExpHistError, blnShow);
	    if (blnShow) {
	        this.showLoading(false);
	        $R_FN.showElement(this._pnlExpHist, false);
	        $R_FN.showElement(this._pnlGetExpHist, false);
	        $R_FN.setInnerHTML(this._pnlExpHistError, strMessage);
	    }
	},

	showExpHistGetData: function (blnShow) {
	    $R_FN.showElement(this._pnlGetExpHist, blnShow);
	    if (blnShow) {
	        this.showLoading(false);
	        $R_FN.showElement(this._pnlLoadingExpHist, false);
	        $R_FN.showElement(this._pnlExpHist, false);
	        $R_FN.setInnerHTML(this._pnlExpHistError, false);
	    }
	},

	processExpHistList: function (tbl) {
	    if (res.ExpHist) {
	        for (var i = 0; i < res.ExpHist.length; i++) {
	            var row = res.ExpHist[i];
	            var aryData = [
					$R_FN.setCleanTextValue(row.Notes),
					$R_FN.writeDoubleCellValue(row.Date + " " + row.Time)
                    ,row.EmployeeName
				];
	            tbl.addRow(aryData, row.ID, false);
	            row = null; aryData = null;
	        }
	    }
	},
    //[002] code start
    OpenDocTree: function () {
        //$R_FN.openDocumentTree(this.getFieldValue("hidNo"), "CRMA");
        $R_FN.openDocumentTree(this._intCRMAID, "CRMA", this.getFieldValue("hidNo"));
    }
    //[002] code end 
	
};

Rebound.GlobalTrader.Site.Controls.Nuggets.CRMAMainInfo.registerClass("Rebound.GlobalTrader.Site.Controls.Nuggets.CRMAMainInfo", Rebound.GlobalTrader.Site.Controls.Nuggets.Base);
