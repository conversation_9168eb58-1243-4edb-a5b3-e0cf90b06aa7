﻿SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE OR ALTER PROCEDURE [dbo].[usp_Import_Epo]                        
@UserID int              
/*  
Updated By : *[001] Devendra for RP-2212
===========================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-202689]		Trung Pham			12-SEP-2024		UPDATE			Add ImportId when inserting
===========================================================================================

*/                            
AS                                               
BEGIN                            
                                              
                                                
IF EXISTS (SELECT (1) FROM BorisGlobalTraderImports.dbo.tbEpoToBeImported  )                                                
 BEGIN                                                
                                                
 SET NOCOUNT ON                   
   DECLARE       @Msg NVARCHAR(100)         
-- declare  @RowCount int = 0                     
--declare @OriginalFilename nvarchar(200)=null                  
                  
-- SELECT @RowCount=count(*),@OriginalFilename=OriginalFilename FROM BorisGlobalTraderImports.dbo.tbStockDataToBeImported where UserId=@UserID  group by OriginalFilename                                             
 CREATE TABLE #tmpEpoToBeImported                              
 (                              
[EpoName]   [nvarchar](300) NULL,                                           
[Part] [nvarchar](30) NOT NULL,              
[ManufacturerName]  [nvarchar](300) NULL,                
[Price] [float] NULL,              
[LeadTime] [nvarchar](300) NULL,              
[SPQ] [nvarchar](300) NULL,              
[SupplierMOQ] [nvarchar](300) NULL,              
[SupplierNo] int null,              
[SupplierName] [nvarchar](300) NULL,              
[OriginalEntryDate] datetime,              
[UpliftPercentage] [float] NULL,              
[UpliftPrice] [float] NULL,              
[Quantity] int null,              
[DateCode]   [nvarchar](5) NULL,              
[ProductName] [nvarchar](300) NULL,                
[PackageName] [nvarchar](300) NULL,                
[ImportDate] datetime,              
[CurrencyCode] [nvarchar](30) NULL,              
[Description] nvarchar(max),              
[ClientNo] int null,              
[ROHS] int null,              
[Id] int null,              
[EpoStatusNo] int null,              
[SupplierTotalQSA] nvarchar (100) NULL,              
[SupplierLTB] nvarchar (100) NULL,              
[ROHSStatus] nvarchar (100) NULL,              
[FactorySealed] nvarchar (100) NULL,              
[MSLLevelNo]  int null,              
[ManufacturerNo] [int] NULL,                            
[PackageNo] [int] NULL,                            
[ProductNo] [int] NULL,       
[SupplierType] [int] NULL,      
                          
                               
 )                                              
                                       
                                                 
                                                
INSERT INTO #tmpEpoToBeImported                            
 SELECT                  
                  
[EpoName],                                           
[Part] ,              
[ManufacturerName],                
[Price] ,              
[LeadTime] ,              
[SPQ] ,              
[SupplierMOQ],              
[SupplierNo] ,              
[SupplierName] ,              
[OriginalEntryDate],              
[UpliftPercentage] ,              
[UpliftPrice] ,              
[Quantity] ,              
[DateCode] ,              
[ProductName] ,                
[PackageName] ,                
[ImportDate] ,              
[CurrencyCode] ,              
[Description] ,              
[ClientNo] ,              
[ROHS] ,              
[Id] ,              
[EpoStatusNo] ,              
[SupplierTotalQSA] ,              
[SupplierLTB] ,              
[ROHSStatus] ,              
[FactorySealed] ,              
[MSLLevelNo]  ,              
( SELECT TOP 1 ManufacturerId                                                
   FROM tbManufacturer AS mf WITH (NoLock) 
    WHERE  (mf.ManufacturerName = Epo.ManufacturerName                                                
     OR mf.FullName = Epo.ManufacturerName         
     OR mf.ManufacturerCode = Epo.ManufacturerName                                                
     OR mf.FullName = dbo.ufn_get_fullname(Epo.ManufacturerName))                                                
    AND  NOT mf.Inactive = 1                                                
   ) AS ManufacturerNo ,                            
 ( SELECT TOP 1 PackageId                                                
    FROM dbo.tbPackage AS pk WITH (NoLock)                                                
    WHERE (pk.PackageDescription = Epo.PackageName                                                
     OR pk.PackageName = Epo.PackageName)                                        
    AND  NOT pk.Inactive = 1                                                
   ) AS PackageNo               
                          
  , ( SELECT TOP 1 ProductId                                                
    FROM dbo.tbProduct AS pr WITH (NoLock)                                                
     WHERE (pr.ProductDescription = Epo.ProductName                                                
      OR pr.ProductName = Epo.ProductName)                         
   AND  pr.ClientNo = Epo.ClientNo                                              
     AND NOT pr.Inactive = 1                                              
                              
   ) AS ProductNo ,         
       
         
(SELECT top 1 st.CompanyTypeId      
FROM dbo.tbCompanyType  AS st WITH (NoLock)    
WHERE st.Name=Epo.SupplierType and    
NOT Inactive = 1      
) as SupplierType      
                                                
 FROM BorisGlobalTraderImports.dbo.tbEpoToBeImported AS Epo                                                
                                            
 WHERE  ISNULL(LEN(Epo.Part), 0) > 0                              
                            
      INSERT INTO BorisGlobalTraderImports.dbo.tbImportActivity_Epo ( ImportDate, ImportName, RowsAffected, Target,ClientType,CreateBy)
    select getdate(),a.EpoName,count(*),'Import Success',1, @UserID from #tmpEpoToBeImported a
    group by a.EpoName

	Declare @importId int
	 set @importId = scope_identity()
                            
 ------------------------------                            
 INSERT INTO BorisGlobalTraderImports.dbo.tbEpo                                       
   (                            
    SupplierNo                              
  , Part                              
  , FullPart                              
  , DateCode                              
  , Quantity                              
  , Price                              
  , OriginalEntryDate                              
  , ManufacturerNo                              
  , CurrencyNo                              
  , ProductNo                              
  , PackageNo                              
  , Notes                              
  , ManufacturerName                              
  , ProductName                               
  , PackageName                              
  , ClientNo                              
  , ActionType                              
  , SupplierName                              
  , EpoStatusNo                                      
  , SupplierTotalQSA                                   
  , SupplierLTB                                 
  , SupplierMOQ                                 
  , LeadTime                              
  , ROHSStatus                              
  , FactorySealed                              
  , MSLLevelNo                              
  , SPQ                              
  ,ROHS                              
  ,DLUP                              
  ,UpliftPercentage                              
  ,UpliftPrice                      
  ,Description          
  ,UpdatedBy       
  ,SupplierType  
  /* [001]*/  
  ,EpoStatusChangeDate  
  /* [001]*/
  ,ImportId
   )                   
     SELECT isnull(Epo.SupplierNo,0)                            
      , isnull(Epo.Part,'')                            
      , dbo.ufn_get_fullpart(isnull(Epo.Part,''))                              
      , Epo.DateCode                              
      , isnull(Epo.Quantity,0) as Quantity                              
      , isnull(Epo.Price,0) as Price  
   /* [001]*/  
      , ISNULL(Epo.OriginalEntryDate,GETDATE())    
   /* [001]*/  
      , epo.ManufacturerNo                              
      , 269                              
      , epo.ProductNo              
      , epo.PackageNo              
     , (Epo.LeadTime +'  '+ convert(nvarchar(25),Epo.OriginalEntryDate, 121) + CONVERT(varchar(12), 9))   AS Notes                                                             
      , Epo.ManufacturerName             
      , Epo.ProductName                              
      , Epo.PackageName                              
      , Epo.ClientNo                              
      , 'strategic vendor'                              
      , Epo.SupplierName                              
      , Epo.EpoStatusNo                                      
      , Epo.SupplierTotalQSA                                   
      , Epo.SupplierLTB                                  
      , Epo.SupplierMOQ                                  
      , Epo.LeadTime          
      , Epo.ROHSStatus                              
      , Epo.FactorySealed                              
      , Epo.MSLLevelNo                              
      , Epo.SPQ                              
      , Epo.ROHS                              
      , GETDATE()                              
  ,Epo.UpliftPercentage                              
  ,Epo.UpliftPrice                      
  ,Epo.Description           
  ,@UserID      
  ,SupplierType   
  /* [001]*/  
  ,GETDATE()  
  /* [001]*/
  ,@importId
  from #tmpEpoToBeImported Epo              
                            
           
  ---------------------------Import History------------------------------------                     
   declare  @RowCount int = 0                           
   declare  @ClientNo int = 0                           
   declare  @OriginalFilename nvarchar(200)= null                           
   select  @RowCount=count(*),@OriginalFilename=imp.EpoName,@ClientNo=imp.ClientNo from  BorisGlobalTraderimports.dbo.tbEpoToBeImported imp                    
   INNER JOIN    #tmpEpoToBeImported  AS tmp                            
     ON            tmp.id = imp.id  group by imp.EpoName,imp.ClientNo                        
   insert into BorisGlobalTraderImports.dbo.tbUtilityLog (FileName,UtilityType,Clientid,LoginNo,DLUP,iRowCount)                     
   values (@OriginalFilename,5,@ClientNo,@UserID,getdate(),@RowCount)                          
  --   --------------------------Import History End--------------------------------                         
                        
                                              
   DELETE BorisGlobalTraderImports.dbo.tbEpoToBeImported                            
              FROM   BorisGlobalTraderImports.dbo.tbEpoToBeImported AS imp                            
              INNER JOIN    #tmpEpoToBeImported  AS tmp                            
     ON            tmp.id = imp.id                            
                            
       --IF @@ERROR = 0                            
       --BEGIN                            
       --       COMMIT TRANSACTION                            
       --       SET @Msg = 'Import Success'                                                                         
       --END                            
       --ELSE                            
       --BEGIN                            
       --       ROLLBACK TRANSACTION                            
       --       SET @Msg = 'Import Failed'                            
       --END
	   
 DROP TABLE #tmpEpoToBeImported                      
 SET NOCOUNT OFF                                                
 END                                                
END 


