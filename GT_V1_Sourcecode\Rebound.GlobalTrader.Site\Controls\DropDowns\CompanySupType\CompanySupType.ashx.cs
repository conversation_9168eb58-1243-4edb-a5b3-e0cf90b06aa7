﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json.Nodes;
using System.Web;
using System.Web.Services;

namespace Rebound.GlobalTrader.Site.Controls.DropDowns.Data
{
    [WebService(Namespace = "http://tempuri.org/")]
    [WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    public class CompanySupType : Rebound.GlobalTrader.Site.Data.DropDowns.Base
    {
        public override void ProcessRequest(HttpContext context)
        {
            SetDropDownType("CompanySupType");
            base.ProcessRequest(context);
        }

        protected override void GetData()
        {
            string strCacheOptions = CacheManager.SerializeOptions(new object[] { SessionManager.Culture });
            string strCachedData = CacheManager.GetDropDownData(_objDropDown.ID, strCacheOptions);
            if (string.IsNullOrEmpty(strCachedData))
            {
                JsonObject jsn = new JsonObject();
                JsonObject jsnList = new JsonObject(true);
                JsonObject jsnItem = new JsonObject();
                jsnItem.AddVariable("ID", "1");
                jsnItem.AddVariable("Name", "All");
                jsnList.AddVariable(jsnItem);
                jsnItem = new JsonObject();
                jsnItem.AddVariable("ID", "2");
                jsnItem.AddVariable("Name", "Supplier");
                jsnList.AddVariable(jsnItem);
                jsnItem = new JsonObject();
                jsnItem.AddVariable("ID", "3");
                jsnItem.AddVariable("Name", "Customer");
                jsnList.AddVariable(jsnItem);
                jsn.AddVariable("Types", jsnList);
                jsnItem.Dispose(); jsnItem = null;
                jsnList.Dispose(); jsnList = null;
                CacheManager.StoreDropDown(_objDropDown.ID, strCacheOptions, jsn.Result, CacheManager.CacheExpiryType.OneWorkingDay);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            else
            {
                _context.Response.Write(strCachedData);
            }
            strCachedData = null;
        }
    }
}