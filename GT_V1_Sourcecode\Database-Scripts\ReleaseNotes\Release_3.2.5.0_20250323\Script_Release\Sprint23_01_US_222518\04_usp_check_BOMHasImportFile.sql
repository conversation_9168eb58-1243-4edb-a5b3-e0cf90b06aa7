﻿/* 
===========================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-222518]		An.TranTan			06-Mar-2025		Create			Check if HUBRFQ has invalid import file during creation
===========================================================================================
*/
CREATE OR ALTER PROCEDURE [dbo].[usp_check_BOMHasImportFile]    
      @BOMId int    
    , @HasImportFile int OUTPUT    
AS     
BEGIN
	SET @HasImportFile = 0;
	IF EXISTS(SELECT TOP 1 1
		FROM tbBOMCSV c WITH(NOLOCK)
		JOIN tbBomCsvLog l WITH(NOLOCK) 
			ON l.BOMNo = c.BOMNo AND c.FileName = l.FileName
		WHERE c.BOMNo = @BOMId 
			AND c.ImportType = 'CreateHUBRFQ'
	)BEGIN
		SET @HasImportFile = 1;
	END
END    
    