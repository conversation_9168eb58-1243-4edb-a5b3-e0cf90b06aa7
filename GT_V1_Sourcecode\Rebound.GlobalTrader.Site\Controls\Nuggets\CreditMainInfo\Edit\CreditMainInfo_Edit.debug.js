///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.CreditMainInfo_Edit = function(element) { 
	Rebound.GlobalTrader.Site.Controls.Forms.CreditMainInfo_Edit.initializeBase(this, [element]);
	this._intCreditID = -1;
	this._hidRaisedByNo=0;
	this._IsPOHub=false;
	this._hidShipViaNo=0;
	this._ShipVia=-1;
	this._RaisedBy=-1;
};

Rebound.GlobalTrader.Site.Controls.Forms.CreditMainInfo_Edit.prototype = {

	get_intCreditID: function() { return this._intCreditID; }, 	set_intCreditID: function(value) { if (this._intCreditID !== value)  this._intCreditID = value; }, 
	get_lblFreight_Currency: function() { return this._lblFreight_Currency; }, 	set_lblFreight_Currency: function(value) { if (this._lblFreight_Currency !== value)  this._lblFreight_Currency = value; }, 

	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.Forms.CreditMainInfo_Edit.callBaseMethod(this, "initialize");
		this.addShown(Function.createDelegate(this, this.formShown));
	},

	formShown: function() {
		if (this._blnFirstTimeShown) {
			this.addSave(Function.createDelegate(this, this.saveClicked));
			$find(this.getField("ctlSalesman2").ControlID).addChanged(Function.createDelegate(this, this.changedSalesman2));
			$find(this.getField("ctlCurrency").ControlID).addChanged(Function.createDelegate(this, this.currencyChanged));
		}
		
		this.getFieldDropDownData("ctlDivision");
		this.getFieldDropDownData("ctlSalesman");
		this.getFieldDropDownData("ctlSalesman2");
		this.getFieldDropDownData("ctlRaisedBy");
		this.getFieldDropDownData("ctlTax");
		this.getFieldDropDownData("ctlCurrency");
		this.getFieldDropDownData("ctlShipVia");
		this.getFieldDropDownData("ctlIncoterm");
		this.getFieldDropDownData("ctlDivisionHeader");
		
		//alert(this._hidRaisedByNo);
	},

	dispose: function() { 
		if (this.isDisposed) return;
		this._intCreditID = null;
		this._lblFreight_Currency = null;
		Rebound.GlobalTrader.Site.Controls.Forms.CreditMainInfo_Edit.callBaseMethod(this, "dispose");
	},

	saveClicked: function() {
		if (!this.validateForm()) return;
		var obj = new Rebound.GlobalTrader.Site.Data();
		this._ShipVia=this.getFieldValue("ctlShipVia");
		if(this._ShipVia<=0){
		this._ShipVia=this._hidShipViaNo;
		}
		this._RaisedBy=this.getFieldValue("ctlRaisedBy")
		if(this._RaisedBy<=0){
		this._RaisedBy=this._hidRaisedByNo;
		}
		
		obj.set_PathToData("controls/Nuggets/CreditMainInfo");
		obj.set_DataObject("CreditMainInfo");
		obj.set_DataAction("SaveEdit");
		obj.addParameter("id", this._intCreditID);
		obj.addParameter("CustomerPO", this.getFieldValue("ctlCustomerPO"));
		obj.addParameter("CustomerReturn", this.getFieldValue("ctlCustomerReturn"));
		obj.addParameter("CustomerDebit", this.getFieldValue("ctlCustomerDebit"));
		obj.addParameter("Notes", this.getFieldValue("ctlNotes"));
		obj.addParameter("Instructions", this.getFieldValue("ctlInstructions"));
		obj.addParameter("DivisionNo", this.getFieldValue("ctlDivision"));
		obj.addParameter("SalesmanNo", this.getFieldValue("ctlSalesman"));
		obj.addParameter("RaisedBy", this._RaisedBy);
		obj.addParameter("CreditDate", this.getFieldValue("ctlCreditDate"));
		obj.addParameter("ReferenceDate", this.getFieldValue("ctlReferenceDate"));
		obj.addParameter("TaxNo", this.getFieldValue("ctlTax"));
		obj.addParameter("CurrencyNo", this.getFieldValue("ctlCurrency"));
		obj.addParameter("ShipViaNo", this._ShipVia);
		obj.addParameter("ShippingAccount", this.getFieldValue("ctlShippingAccount"));
		obj.addParameter("ShippingCost", this.getFieldValue("ctlShippingCost"));
		obj.addParameter("Freight", this.getFieldValue("ctlFreight"));
        obj.addParameter("Salesman2No", this.getFieldValue("ctlSalesman2"));
		obj.addParameter("Salesman2Percent", this.getFieldValue("ctlSalesman2Percent"));
		obj.addParameter("Incoterm", this.getFieldValue("ctlIncoterm"));
		//[001] start code
		obj.addParameter("CreditNoteBankFee", this.getFieldValue("ctlCreditNoteBankFee"));
	    //[001] end code
		obj.addParameter("ExchangeRate", this.getFieldValue("ctlExchangeRate"));
		obj.addParameter("DivisionHeader", this.getFieldValue("ctlDivisionHeader"));
        obj.addDataOK(Function.createDelegate(this, this.saveEditComplete));
        obj.addError(Function.createDelegate(this, this.saveEditError));
		obj.addTimeout(Function.createDelegate(this, this.saveEditError));
		$R_DQ.addToQueue(obj);
		$R_DQ.processQueue();
		obj = null;
	},

	saveEditError: function(args) {
		this._strErrorMessage = args._errorMessage;
		this.onSaveError();
	},
	
	saveEditComplete: function(args) {
		if (args._result.Result == true) {
			this.onSaveComplete();
		} else {
			this._strErrorMessage = args._errorMessage;
			this.onSaveError();
		}
	},	

	validateForm: function() {
		this.onValidate();
		var blnOK = this.autoValidateFields();
		
		return blnOK;
	},
	
	changedSalesman2: function() {
		if ($find(this.getField("ctlSalesman2").ControlID).isSetAsNoValue()) {
			this.setFieldValue("ctlSalesman2Percent", "");
		} else {
			if (this.getFieldValue("ctlSalesman2Percent") == 0) this.setFieldValue("ctlSalesman2Percent", 50);
		}
	},
	
	currencyChanged: function() {
		$R_FN.setInnerHTML(this._lblFreight_Currency, this.getFieldDropDownExtraText("ctlCurrency"));
	}

};

Rebound.GlobalTrader.Site.Controls.Forms.CreditMainInfo_Edit.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.CreditMainInfo_Edit", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
