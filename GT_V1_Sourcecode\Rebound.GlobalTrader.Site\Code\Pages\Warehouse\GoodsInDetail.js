Type.registerNamespace("Rebound.GlobalTrader.Site.Pages.Warehouse");Rebound.GlobalTrader.Site.Pages.Warehouse.GoodsInDetail=function(n){Rebound.GlobalTrader.Site.Pages.Warehouse.GoodsInDetail.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Pages.Warehouse.GoodsInDetail.prototype={get_intGIID:function(){return this._intGIID},set_intGIID:function(n){this._intGIID!==n&&(this._intGIID=n)},get_ctlMainInfo:function(){return this._ctlMainInfo},set_ctlMainInfo:function(n){this._ctlMainInfo!==n&&(this._ctlMainInfo=n)},get_ctlLines:function(){return this._ctlLines},set_ctlLines:function(n){this._ctlLines!==n&&(this._ctlLines=n)},get_btnPrint:function(){return this._btnPrint},set_btnPrint:function(n){this._btnPrint!==n&&(this._btnPrint=n)},get_lblStatus:function(){return this._lblStatus},set_lblStatus:function(n){this._lblStatus!==n&&(this._lblStatus=n)},get_pnlStatus:function(){return this._pnlStatus},set_pnlStatus:function(n){this._pnlStatus!==n&&(this._pnlStatus=n)},get_ctlGIDocuments:function(){return this._ctlGIDocuments},set_ctlGIDocuments:function(n){this._ctlGIDocuments!==n&&(this._ctlGIDocuments=n)},get_ctlGIPDFDragDrop:function(){return this._ctlGIPDFDragDrop},set_ctlGIPDFDragDrop:function(n){this._ctlGIPDFDragDrop!==n&&(this._ctlGIPDFDragDrop=n)},get_IsGlobalLogin:function(){return this._IsGlobalLogin},set_IsGlobalLogin:function(n){this._IsGlobalLogin!==n&&(this._IsGlobalLogin=n)},get_ctlGILineImageDragDrop:function(){return this._ctlGILineImageDragDrop},set_ctlGILineImageDragDrop:function(n){this._ctlGILineImageDragDrop!==n&&(this._ctlGILineImageDragDrop=n)},initialize:function(){Rebound.GlobalTrader.Site.Pages.Warehouse.GoodsInDetail.callBaseMethod(this,"initialize")},goInit:function(){this._ctlLines.addShowEditForm(Function.createDelegate(this,this.ctlLines_ShowEditForm));this._btnPrint&&this._btnPrint.addPrint(Function.createDelegate(this,this.printGI));this._ctlMainInfo&&this._ctlMainInfo.addGetDataComplete(Function.createDelegate(this,this.ctlMainInfo_GetDataComplete));this._ctlLines&&this._ctlLines.addPotentialStatusChange(Function.createDelegate(this,this.ctlLines_PotentialStatusChange));this._ctlGIDocuments&&this._ctlGIDocuments.getData();this._ctlGIPDFDragDrop&&this._ctlGIPDFDragDrop.getData();this._ctlGILineImageDragDrop&&this._ctlGILineImageDragDrop.getData();this._btnPrint&&this._btnPrint.addExtraButtonClick(Function.createDelegate(this,this.printOtherDocs));Rebound.GlobalTrader.Site.Pages.Warehouse.GoodsInDetail.callBaseMethod(this,"goInit")},dispose:function(){this.isDisposed||(this._element&&$clearHandlers(this._element),this._ctlMainInfo&&this._ctlMainInfo.dispose(),this._ctlLines&&this._ctlLines.dispose(),this._btnPrint&&this._btnPrint.dispose(),this._ctlGIDocuments&&this._ctlGIDocuments.dispose(),this._ctlMainInfo=null,this._ctlLines=null,this._btnPrint=null,this._lblStatus=null,this._pnlStatus=null,this._ctlGIDocuments=null,this._IsGlobalLogin=null,Rebound.GlobalTrader.Site.Pages.Warehouse.GoodsInDetail.callBaseMethod(this,"dispose"))},printGI:function(){$R_FN.openPrintWindow($R_ENUM$PrintObject.GoodsIn,this._intGIID)},printOtherDocs:function(){this._btnPrint._strExtraButtonClickCommand=="PrintLog"&&$R_FN.openPrintLogWindow($R_ENUM$PrintObject.PrintLog,this._intGIID,!1,"GoodsIn")},ctlMainInfo_GetDataComplete:function(){$R_FN.setInnerHTML(this._lblStatus,this._ctlMainInfo.getFieldValue("hidStatus"));this._ctlLines.updateStatus(this._ctlMainInfo.getFieldValue("hidStatusNo"));this._ctlLines._intPurchaseOrderNo=this._ctlMainInfo.getFieldValue("hidPoNumber");this._ctlLines._intGlobalClientNo=this._IsGlobalLogin==!0?this._ctlMainInfo.getFieldValue("hidGlobalClientNo"):null;this._ctlMainInfo._intGlobalClientNo=this._IsGlobalLogin==!0?this._ctlMainInfo.getFieldValue("hidGlobalClientNo"):null;this._ctlMainInfo._IsGlobalLogin=this._IsGlobalLogin},ctlLines_PotentialStatusChange:function(){this._ctlMainInfo.getData();this._ctlMainInfo.showField("ctlSupplierInvoiceAdd",this._ctlLines._blnAnyLineInspected)},getStatusError:function(){$R_FN.showElement(this._pnlStatus,!1)},ctlLines_ShowEditForm:function(){this._ctlLines._objGIHeaderFields={GoodsInNumber:this._ctlMainInfo.getFieldValue("hidGoodsInNumber"),SupplierName:this._ctlMainInfo.getFieldValue("hidSupplierName"),AirWayBill:this._ctlMainInfo.getFieldValue("ctlAirWayBill"),Reference:this._ctlMainInfo.getFieldValue("ctlReference"),Currency:this._ctlMainInfo.getFieldValue("ctlCurrency"),CurrencyCode:this._ctlMainInfo.getFieldValue("hidCurrencyCode"),CurrencyID:this._ctlMainInfo.getFieldValue("hidCurrencyNo"),ReceivingNotes:this._ctlMainInfo.getFieldValue("ctlNotes"),ClientCurCode:this._ctlMainInfo.getFieldValue("hidClientCurrency"),SupplierNo:this._ctlMainInfo.getFieldValue("hidSupplierNo"),Buyer:this._ctlMainInfo.getFieldValue("ctlBuyer"),BuyerNo:this._ctlMainInfo.getFieldValue("hidBuyerNo"),PurchaseOrder:this._ctlMainInfo.getFieldValue("hidPoNumber"),PurchaseOrderNo:this._ctlMainInfo.getFieldValue("hidPurchaseOrderNo"),ReceivedDate:this._ctlMainInfo.getFieldValue("ctlReceivedDate"),IsPoHubLogin:this._ctlMainInfo.getFieldValue("hidIsPoHubLogin"),InternalPurchaseOrderId:this._ctlMainInfo.getFieldValue("hidInternalPurchaseOrderId"),IPOSupplier:this._ctlMainInfo.getFieldValue("hidIPOSupplier"),IPOSupplierName:this._ctlMainInfo.getFieldValue("hidIPOSupplierName")}}};Rebound.GlobalTrader.Site.Pages.Warehouse.GoodsInDetail.registerClass("Rebound.GlobalTrader.Site.Pages.Warehouse.GoodsInDetail",Rebound.GlobalTrader.Site.Pages.Content,Sys.IDisposable);