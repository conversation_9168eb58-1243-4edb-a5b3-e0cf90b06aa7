﻿using System;
using System.Data;
using System.Data.SqlClient;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;
using System.Web.Caching;
using System.Data.Common;

namespace Rebound.GlobalTrader.DAL.SqlClient {
	public class SqlOGELLicenseProvider : OGELLicenseProvider
    {
        
        /// <summary>
        /// usp_insert_OGELLicense
        /// </summary>
        /// <param name="ogelNumber"></param>
        /// <param name="description"></param>
        /// <param name="inActive"></param>
        /// <param name="updatedBy"></param>
        /// <returns></returns>
        public override int Insert(string ogelNumber, string description, bool? inActive, int? updatedBy)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_insert_OGELLicense", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@OgelNumber", SqlDbType.NVarChar).Value = ogelNumber;
                cmd.Parameters.Add("@Description", SqlDbType.NVarChar).Value = description;
                cmd.Parameters.Add("@InActive", SqlDbType.Bit).Value = inActive;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@OgelId", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (Int32)cmd.Parameters["@OgelId"].Value;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to insert OGEL License", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        /// <summary>
        /// usp_update_OGELLicense
        /// </summary>
        /// <param name="ogelId"></param>
        /// <param name="ogelNumber"></param>
        /// <param name="description"></param>
        /// <param name="inActive"></param>
        /// <param name="updatedBy"></param>
        /// <returns></returns>
        public override int Update(int? ogelId, string ogelNumber, string description, bool? inActive, int? updatedBy)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_update_OGELLicense", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@OgelId", SqlDbType.Int).Value = ogelId;
                cmd.Parameters.Add("@OgelNumber", SqlDbType.NVarChar).Value = ogelNumber;
                cmd.Parameters.Add("@Description", SqlDbType.NVarChar).Value = description;
                cmd.Parameters.Add("@InActive", SqlDbType.Bit).Value = inActive;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (int)cmd.Parameters["@RowsAffected"].Value;
                //return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to update OGEL License", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        /// <summary>
        /// usp_selectAll_OGELLicenses
        /// </summary>
        /// <returns></returns>
        public override List<OGELLicenseDetails> GetList()
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_selectAll_OGELLicenses", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<OGELLicenseDetails> lst = new List<OGELLicenseDetails>();
                while (reader.Read())
                {
                    OGELLicenseDetails obj = new OGELLicenseDetails();
                    obj.OgelId = GetReaderValue_Int32(reader, "OgelId", 0);
                    obj.OgelNumber = GetReaderValue_String(reader, "OgelNumber", "");
                    obj.Description = GetReaderValue_String(reader, "Description", "");
                    obj.Inactive = GetReaderValue_Boolean(reader, "Inactive", false);
                    obj.UpdatedBy = GetReaderValue_Int32(reader, "UpdatedBy", 0);
                    obj.DLUP = GetReaderValue_DateTime(reader, "DLUP", DateTime.MinValue);
                    obj.EmployeeName = GetReaderValue_String(reader, "EmployeeName", "");
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }

            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get OGEL Licenses", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
    }
}