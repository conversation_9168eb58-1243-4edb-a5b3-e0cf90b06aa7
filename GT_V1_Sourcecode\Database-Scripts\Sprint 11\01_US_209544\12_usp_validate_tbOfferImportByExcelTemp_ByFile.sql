﻿/*===========================================================================================  
TASK			UPDATED BY       DATE				ACTION		DESCRIPTION  
[US-209544]     CuongDox		 17-Sep-2024		CREATE		Validate imported offers
===========================================================================================  
*/

CREATE OR ALTER PROC [dbo].[usp_validate_tbOfferImportByExcelTemp_ByFile](
@generated_file_name VARCHAR(255)
)
AS

BEGIN

	CREATE TABLE #tbOfferImportErrors (
		OfferredID INT,                  
		[message_MPN] NVARCHAR(255),   
		[message_COST] NVARCHAR(255),   
		[message_LeadTime] NVARCHAR(255),  
		[message_SPQ] NVARCHAR(255),   
		[message_MOQ] NVARCHAR(255),   
		[message_OfferedDate] NVARCHAR(255),
		[message_Vendor] NVARCHAR(255)  
	);
	
	INSERT INTO #tbOfferImportErrors (OfferredID, [message_MPN],[message_COST],[message_LeadTime],[message_SPQ],[message_MOQ], [message_OfferedDate], [message_Vendor])
	SELECT 
		[OfferTempId] AS OfferredID,   
		CASE 
			WHEN MPN IS NULL THEN 'MPN is NULL'  
			WHEN LEN(MPN) > 256 THEN 'MPN length > 256' 
			ELSE NULL  
		END AS [message_MPN],
		CASE 
			WHEN TRY_CAST(COST AS FLOAT) IS NULL THEN 'COST is invalid'
			ELSE NULL                                          
		END AS [message_COST],
		CASE 
			WHEN LEN([LeadTime]) > 256 THEN 'LeadTime length > 256'  
			ELSE NULL  
		END AS [message_LeadTime],
		CASE 
			WHEN LEN([SPQ]) > 256 THEN 'SPQ length > 256'  
			ELSE NULL  
		END AS [message_SPQ],
		CASE 
			WHEN MOQ IS NULL THEN 'MOQ is NULL'
			WHEN LEN(MOQ) = 0 THEN 'MOQ is NULL'
			WHEN TRY_CAST([MOQ] AS INT) IS NULL THEN 'MOQ is invalid'  
			ELSE NULL                                          
		END AS [message_MOQ],
		CASE 
       
        WHEN TRY_CONVERT(DATE, OfferedDate, 103) IS NULL OR
             (OfferedDate NOT LIKE '[0-3][0-9]/[0-1][0-9]/[1-9][0-9][0-9][0-9]' 
              AND OfferedDate NOT LIKE '[1-9]/[1-9]/[1-9][0-9][0-9][0-9]') THEN
             'OfferedDate is not in dd/MM/yyyy or d/M/yyyy format'
        ELSE NULL                                          
		END AS [message_OfferedDate],
		CASE 
        WHEN cmp.CompanyName IS NULL THEN 'Vendor does not exist'  
        ELSE NULL
    END AS [message_Vendor]
	FROM 
		BorisGlobalTraderImports.dbo.[tbOfferImportByExcelTemp] temp
	LEFT JOIN dbo.tbCompany cmp ON (cmp.CompanyName = temp.Vendor AND cmp.ClientNo = 114 AND cmp.IsSupplier = 1)
	WHERE 
		GeneratedFileName = @generated_file_name 
		AND 
		(MPN IS NULL 
		OR LEN(MPN) > 256
		OR LEN([LeadTime]) > 256
		OR LEN([SPQ]) > 256
		OR TRY_CAST([MOQ] AS INT) IS NULL
		OR MOQ IS NULL 
		OR LEN(MOQ) = 0
		OR (OfferedDate NOT LIKE '[0-3][0-9]/[0-1][0-9]/[1-9][0-9][0-9][0-9]' 
          AND OfferedDate NOT LIKE '[1-9]/[1-9]/[1-9][0-9][0-9][0-9]')
		OR TRY_CAST(COST AS FLOAT) IS NULL
		OR cmp.CompanyName IS NULL
		)
	ORDER BY [OfferTempId];

	WITH RankedIDs AS (
		SELECT 
			[OfferTempId], 
			ROW_NUMBER() OVER (ORDER BY [OfferTempId]) AS [Index]  -- Create an index for each ID in the table
		FROM 
			BorisGlobalTraderImports.dbo.[tbOfferImportByExcelTemp] temp
			WHERE 
			GeneratedFileName = @generated_file_name 
			
			)

	SELECT 
		r.[Index] +1 as [Uploaded Row], 
		[message_MPN] as [Error MPN],
		'' as [Error MFR],
		[message_COST] as [Error Cost],
		[message_LeadTime] as [Error Lead Time],
		[message_SPQ] as [Error SPQ],
		[message_MOQ] as [Error MOQ],
		[message_OfferedDate] as [Error Offered Date],
		[message_Vendor] as [Error Vendor]
	FROM 
		RankedIDs r

	JOIN #tbOfferImportErrors ote ON  r.[OfferTempId] = ote.OfferredID
	WHERE 
		r.[OfferTempId] IN (select OfferredID from #tbOfferImportErrors);

	Drop table #tbOfferImportErrors
END

GO

