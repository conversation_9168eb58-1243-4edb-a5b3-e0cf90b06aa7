﻿
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


/* 
===========================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-203247]		Trung Pham			9-Aug-2024		CREATE			Active/Inactive Customer Group Code by ID
===========================================================================================
*/

CREATE OR ALTER PROCEDURE [dbo].[usp_Inactive_GroupCode_by_Id] 
	    @GroupCodeID INT,
		@Inactive INT,
		@UpdatedBy INT,
		@RowsAffected INT OUTPUT
AS
BEGIN
	SET NOCOUNT ON;

    IF (NOT EXISTS(SELECT 1 FROM tbContactGroup cg
		INNER JOIN tbCompany co ON co.GroupCodeNo = cg.ItemId
		WHERE cg.ContactGroupType = 'customer' AND cg.ItemId = @GroupCodeID))
	BEGIN
		UPDATE tbContactGroup
		SET Inactive = @Inactive,
			DLUP = GETDATE(),
			Updatedby = @UpdatedBy
		WHERE ContactGroupType = 'customer' AND ItemId = @GroupCodeID
		SET @RowsAffected = @@ROWCOUNT
	END
	ELSE
	BEGIN
		SET @RowsAffected = -1
	END
	SELECT @RowsAffected
END
GO


