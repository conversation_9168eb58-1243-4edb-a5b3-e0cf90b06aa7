Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.HomeNuggets");Rebound.GlobalTrader.Site.Controls.HomeNuggets.BomManager=function(n){Rebound.GlobalTrader.Site.Controls.HomeNuggets.BomManager.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.HomeNuggets.BomManager.prototype={get_pnlReady:function(){return this._pnlReady},set_pnlReady:function(n){this._pnlReady!==n&&(this._pnlReady=n)},get_tblReady:function(){return this._tblReady},set_tblReady:function(n){this._tblReady!==n&&(this._tblReady=n)},get_pnlPartial:function(){return this._pnlPartial},set_pnlPartial:function(n){this._pnlPartial!==n&&(this._pnlPartial=n)},get_tblPartial:function(){return this._tblPartial},set_tblPartial:function(n){this._tblPartial!==n&&(this._tblPartial=n)},get_pnlNew:function(){return this._pnlNew},set_pnlNew:function(n){this._pnlNew!==n&&(this._pnlNew=n)},get_tblNew:function(){return this._tblNew},set_tblNew:function(n){this._tblNew!==n&&(this._tblNew=n)},get_pnlRFQ:function(){return this._pnlRFQ},set_pnlRFQ:function(n){this._pnlRFQ!==n&&(this._pnlRFQ=n)},get_tblRFQ:function(){return this._tblRFQ},set_tblRFQ:function(n){this._tblRFQ!==n&&(this._tblRFQ=n)},get_pnlClosed:function(){return this._pnlClosed},set_pnlClosed:function(n){this._pnlClosed!==n&&(this._pnlClosed=n)},get_tblClosed:function(){return this._tblClosed},set_tblClosed:function(n){this._tblClosed!==n&&(this._tblClosed=n)},get_pnlMore:function(){return this._pnlMore},set_pnlMore:function(n){this._pnlMore!==n&&(this._pnlMore=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.HomeNuggets.BomManager.callBaseMethod(this,"initialize");this.addRefreshEvent(Function.createDelegate(this,this.getData))},dispose:function(){this.isDisposed||(this._tblReady&&this._tblReady.dispose(),this._pnlReady=null,this._tblReady=null,this._tblPartial&&this._tblPartial.dispose(),this._pnlPartial=null,this._tblPartial=null,this._tblNew&&this._tblNew.dispose(),this._pnlNew=null,this._tblNew=null,this._tblRFQ&&this._tblRFQ.dispose(),this._pnlRFQ=null,this._tblRFQ=null,this._tblClosed&&this._tblClosed.dispose(),this._pnlClosed=null,this._tblClosed=null,this._pnlMore=null,Rebound.GlobalTrader.Site.Controls.HomeNuggets.BomManager.callBaseMethod(this,"dispose"))},setupLoadingState:function(){$R_FN.showElement(this._pnlMore,!1);$R_FN.showElement(this._pnlReady,!1);$R_FN.showElement(this._pnlNew,!1);$R_FN.showElement(this._pnlRFQ,!1);$R_FN.showElement(this._pnlClosed,!1);Rebound.GlobalTrader.Site.Controls.HomeNuggets.BomManager.callBaseMethod(this,"setupLoadingState")},showNoData:function(n){this.showContent(!0);$R_FN.showElement(this._pnlNoData,n)},getData:function(){this.setupLoadingState();var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/HomeNuggets/BomManager");n.set_DataObject("BomManager");n.set_DataAction("GetData");n.addParameter("rowcount",this._intRowCount);n.addDataOK(Function.createDelegate(this,this.getDataComplete));n.addError(Function.createDelegate(this,this.homeNuggetDataError));n.addTimeout(Function.createDelegate(this,this.homeNuggetDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getDataComplete:function(n){var i,u,t,r,f;for(this.showNoData(n._result.Count==0),$R_FN.showElement(this._pnlMore,n._result.Count>0),i=n._result,this._tblReady.clearTable(),r=0;r<i.Ready.length;r++)t=i.Ready[r],f="",u=[i.IsHub==!1?$RGT_nubButton_BMM(t.BOMManagerId,t.BOMManagerCode):$RGT_nubButton_BMMPO(t.BOMManagerId,t.BOMManagerCode),$R_FN.setCleanTextValue(t.Name),t.Date,t.QuoteRequired],this._tblReady.addRow(u,null,null,null,f);for($R_FN.showElement(this._pnlReady,i.Ready.length>0),this._tblPartial.clearTable(),r=0;r<i.Partial.length;r++)t=i.Partial[r],f="",u=[i.IsHub==!1?$RGT_nubButton_BMM(t.BOMManagerId,t.BOMManagerCode):$RGT_nubButton_BMMPO(t.BOMManagerId,t.BOMManagerCode),$R_FN.setCleanTextValue(t.Name),t.Date,t.QuoteRequired],this._tblPartial.addRow(u,null,null,f);for($R_FN.showElement(this._pnlPartial,i.Partial.length>0),this._tblNew.clearTable(),r=0;r<i.New.length;r++)t=i.New[r],u=[i.IsHub==!1?$RGT_nubButton_BMM(t.BOMManagerId,t.BOMManagerCode):$RGT_nubButton_BMMPO(t.BOMManagerId,t.BOMManagerCode),$R_FN.setCleanTextValue(t.Name),t.Date,t.QuoteRequired],this._tblNew.addRow(u,null),t=null;for($R_FN.showElement(this._pnlNew,i.New.length>0),this._tblRFQ.clearTable(),r=0;r<i.RFQ.length;r++)t=i.RFQ[r],u=[i.IsHub==!1?$RGT_nubButton_BMM(t.BOMManagerId,t.BOMManagerCode):$RGT_nubButton_BMMPO(t.BOMManagerId,t.BOMManagerCode),$R_FN.setCleanTextValue(t.Name),t.Date,t.QuoteRequired],this._tblRFQ.addRow(u,null),t=null;for($R_FN.showElement(this._pnlRFQ,i.RFQ.length>0),this._tblClosed.clearTable(),r=0;r<i.Closed.length;r++)t=i.Closed[r],f="",u=[i.IsHub==!1?$RGT_nubButton_BMM(t.BOMManagerId,t.BOMManagerCode):$RGT_nubButton_BMMPO(t.BOMManagerId,t.BOMManagerCode),$R_FN.setCleanTextValue(t.Name),t.Date,t.QuoteRequired],this._tblClosed.addRow(u,null,null,null,f),t=null;$R_FN.showElement(this._pnlClosed,i.Closed.length>0);this.hideLoading()}};Rebound.GlobalTrader.Site.Controls.HomeNuggets.BomManager.registerClass("Rebound.GlobalTrader.Site.Controls.HomeNuggets.BomManager",Rebound.GlobalTrader.Site.Controls.HomeNuggets.Base,Sys.IDisposable);