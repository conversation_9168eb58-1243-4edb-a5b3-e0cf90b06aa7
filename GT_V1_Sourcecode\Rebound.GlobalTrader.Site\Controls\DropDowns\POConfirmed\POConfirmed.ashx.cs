//--------------------------------------------------------------------------------------------------------
// RP 11.12.2009:
// - cache data
//--------------------------------------------------------------------------------------------------------
using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;

namespace Rebound.GlobalTrader.Site.Controls.DropDowns.Data {
    [WebService(Namespace = "http://tempuri.org/")]
    [WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    public class POConfirmed : Rebound.GlobalTrader.Site.Data.DropDowns.Base {

        public override void ProcessRequest(HttpContext context) {
            SetDropDownType("POConfirmed");
            base.ProcessRequest(context);
        }

        protected override void GetData() {
            string strCacheOptions = CacheManager.SerializeOptions(new object[] { SessionManager.Culture });
            string strCachedData = CacheManager.GetDropDownData(_objDropDown.ID, strCacheOptions);
            if (string.IsNullOrEmpty(strCachedData)) {
                JsonObject jsn = new JsonObject();
                JsonObject jsnList = new JsonObject(true);
                jsnList.AddVariable(GetPOConfirmedJsonItem("conf"));
                jsnList.AddVariable(GetPOConfirmedJsonItem("unco"));
                jsnList.AddVariable(GetPOConfirmedJsonItem("both"));
                jsn.AddVariable("Types", jsnList);
                jsnList.Dispose(); jsnList = null;
                CacheManager.StoreDropDown(_objDropDown.ID, strCacheOptions, jsn.Result, CacheManager.CacheExpiryType.OneWorkingDay);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            } else {
                _context.Response.Write(strCachedData);
            }
            strCachedData = null;
        }

        private JsonObject GetPOConfirmedJsonItem(string strValue) {
            JsonObject jsnItem = new JsonObject();
            jsnItem.AddVariable("ID", strValue);
            jsnItem.AddVariable("Name", Functions.GetGlobalResource("POConfirmed", strValue));
            return jsnItem;
        }
    }
}
