Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.DabitBulk_Confirm=function(n){Rebound.GlobalTrader.Site.Controls.Forms.DabitBulk_Confirm.initializeBase(this,[n]);this._strDebits="";this._blnCOC=!1;this._blnPackagingSlip=!1};Rebound.GlobalTrader.Site.Controls.Forms.DabitBulk_Confirm.prototype={initialize:function(){Rebound.GlobalTrader.Site.Controls.Forms.DabitBulk_Confirm.callBaseMethod(this,"initialize");this.addShown(Function.createDelegate(this,this.formShown))},dispose:function(){this.isDisposed||(this._ctlConfirm&&this._ctlConfirm.dispose(),this._ctlConfirm=null,this._strCredits=null,this._blnCOC=null,Rebound.GlobalTrader.Site.Controls.Forms.DabitBulk_Confirm.callBaseMethod(this,"dispose"))},formShown:function(){this._blnFirstTimeShown&&(this._ctlConfirm=this.getFieldComponent("ctlConfirm"),this._ctlConfirm.addClickYesEvent(Function.createDelegate(this,this.yesClicked)),this._ctlConfirm.addClickNoEvent(Function.createDelegate(this,this.noClicked)))},yesClicked:function(){this.showSaving(!0);var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/DebitMainInfo");n.set_DataObject("DebitMainInfo");n.set_DataAction("SaveDebitEmail");n.addParameter("Debits",this._strDebits);n.addDataOK(Function.createDelegate(this,this.saveComplete));n.addError(Function.createDelegate(this,this.saveError));n.addTimeout(Function.createDelegate(this,this.saveError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null;this.onSave()},noClicked:function(){this.showSaving(!1);this.onNotConfirmed()},saveError:function(n){this.showSaving(!1);this._strErrorMessage=n._errorMessage;this.onSaveError()},saveComplete:function(n){this.showSaving(!1);n._result.Result>=1?(this.onSaveComplete(),n._result.Result==1?alert($R_RES.DebitFinanceContactNotFoundMessage+" "+n._result.Credits):n._result.Result==2?alert($R_RES.DebitProgressMessage+"\n\n"+$R_RES.DebitFinanceContactNotFoundMessage+" "+n._result.Credits):alert($R_RES.DebitProgressMessage)):(this._strErrorMessage=n._errorMessage,this.onSaveError())}};Rebound.GlobalTrader.Site.Controls.Forms.DabitBulk_Confirm.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.DabitBulk_Confirm",Rebound.GlobalTrader.Site.Controls.Forms.Base,Sys.IDisposable);