Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.GILines_Export=function(n){Rebound.GlobalTrader.Site.Controls.Forms.GILines_Export.initializeBase(this,[n]);this._intGIID=0;this._intLineID=-1;this._selectedImageList=[];this._Lot="";this._SupplierType="";this._Quantity="";this._PartNo="";this._Manufacturer="";this._DateCode="";this._Package="";this._MSL="";this._HICStatus="";this._RohsStatus="";this._CountryOfManufacture="";this._ReqSerailNo="";this._LotCodeReq="";this._GeneralInspectionNotes="";this._BakingAdded="";this._CountryOfOrigin="";this._StockNo=-1;this._Mode="";this._ActeoneTestStatus=0;this._IsopropryleStatus=0;this._ActeoneTest="";this._Isopropryle="";this._CountingMethodName="";this._IsInspectionConducted=!1};Rebound.GlobalTrader.Site.Controls.Forms.GILines_Export.prototype={get_strMedium:function(){return this._strMedium},set_strMedium:function(n){this._strMedium!==n&&(this._strMedium=n)},get_strLarge:function(){return this._strLarge},set_strLarge:function(n){this._strLarge!==n&&(this._strLarge=n)},get_pnlImages:function(){return this._pnlImages},set_pnlImages:function(n){this._pnlImages!==n&&(this._pnlImages=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Forms.GILines_Export.callBaseMethod(this,"initialize");this.addShown(Function.createDelegate(this,this.formShown));this.addSave(Function.createDelegate(this,this.saveClicked))},formShown:function(){this.storeOriginalFieldValues();this.getData();this._chkSelectAllImage=$find("ctl00_cphMain_ctlLines_ctlDB_ctl14_frmExport_ctlDB_ctlSelectAllImage_ctl03_chkSelectAllImage");this._chkSelectAllImage.addClick(Function.createDelegate(this,this.SelectAllImage))},SelectAllImage:function(){var i=this.getFieldValue("ctlSelectAllImage"),n,t;for(this._selectedImageList=[],n=0;n<this._intCountImages;n++)i==!0?(t="ctl00_cphMain_ctlLines_ctlDB_ctl14_frmExport_ctlDB_chk"+n,$("#"+t).prop("checked",!0).trigger("change")):(t="ctl00_cphMain_ctlLines_ctlDB_ctl14_frmExport_ctlDB_chk"+n,$("#"+t).prop("checked",!1).trigger("change"))},getFormControlID:function(n,t){return String.format("{0}_{1}",n,t)},dispose:function(){this.isDisposed||(this._intGIID=null,this._intLineID=null,this._selectedImageList=null,this._Lot=null,this._SupplierType=null,this._Quantity=null,this._PartNo=null,this._Manufacturer=null,this._DateCode=null,this._Package=null,this._MSL=null,this._HICStatus=null,this._RohsStatus=null,this._CountryOfManufacture=null,this._ReqSerailNo=null,this._LotCodeReq=null,this._GeneralInspectionNotes=null,this._BakingAdded=null,this._CountryOfOrigin=null,this._Mode=null,Rebound.GlobalTrader.Site.Controls.Forms.GILines_Export.callBaseMethod(this,"dispose"))},saveClicked:function(){if(this.validateForm()){var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/GILines");n.set_DataObject("GILines");n.set_DataAction("ExportPDFWord");n.addParameter("id",this._intLineID);n.addParameter("GoodsIn",this.getFieldValue("ctlGoodsIn"));n.addParameter("Lot",this._Lot);n.addParameter("SupplierType",this._SupplierType);n.addParameter("Quantity",this._Quantity);n.addParameter("PartNo",this._PartNo);n.addParameter("Manufacturer",this._Manufacturer);n.addParameter("DateCode",this._DateCode);n.addParameter("Package",this._Package);n.addParameter("MSL",this._MSL);n.addParameter("HICStatus",this._HICStatus);n.addParameter("RohsStatus",this._RohsStatus);n.addParameter("CountryOfManufacture",this._CountryOfManufacture);n.addParameter("ReqSerailNo",this._ReqSerailNo);n.addParameter("LotCodeReq",this._LotCodeReq);n.addParameter("GeneralInspectionNotes",this._GeneralInspectionNotes);n.addParameter("BakingAdded",this._BakingAdded);n.addParameter("SelectedImageList",this._selectedImageList);n.addParameter("CountryOfOrigin",this._CountryOfOrigin);n.addParameter("ActeoneTestStatus",this._ActeoneTestStatus);n.addParameter("IsopropryleStatus",this._IsopropryleStatus);n.addParameter("ActeoneTest",this._ActeoneTest);n.addParameter("Isopropryle",this._Isopropryle);n.addParameter("CountingMethodName",this._CountingMethodName);n.addParameter("IsInspectionConducted",this._IsInspectionConducted);n.addParameter("IsPDFExport",this._Mode=="PDF"?!0:!1);n.addParameter("IsWordExport",this._Mode=="WORD"?!0:!1);n.addParameter("IsGeneralNotesAdd",this.getFieldValue("ctlIsGeneralNotesAdd"));n.addParameter("ExternalNotes",this.getFieldValue("ctlExternalNotes"));n.addDataOK(Function.createDelegate(this,this.saveExportComplete));n.addError(Function.createDelegate(this,this.saveExportError));n.addTimeout(Function.createDelegate(this,this.saveExportError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null}},saveExportError:function(n){this._strErrorMessage=n._errorMessage;this.onSaveError()},saveExportComplete:function(n){var t,i;if(n._result.Result==!0)t=window.location.origin,n._result.PDFFilePath!=""&&window.open(t+n._result.PDFFilePath,"winPrint","left=20,top=20,width=770,height=574,toolbar=no,resizable=yes,status=no,directories=no,location=no,menubar=yes,scrollbars=yes"),n._result.WordFilePath!=""&&window.open(t+n._result.WordFilePath,"_blank"),this.onSaveComplete();else return i=n._result.Message==""?n._errorMessage:n._result.Message,this.showMessage(!0,i),!1},validateForm:function(){this.onValidate();return this.autoValidateFields()},showMessage:function(n,t){$R_FN.showElement(this._pnlValidateError,n);n&&($R_FN.showElement(this._pnlSaving,!1),$R_FN.showElement(this._pnlSavedOK,!1),$R_FN.showElement(this._pnlLoading,!1),$R_FN.showElement(this._pnlContentInner,!0),$R_FN.showElement(this._pnlLinksHolder,!0),$R_FN.showElement(this._pnlFooterLinksHolder,!0),this._ctlRelatedNugget&&(this._ctlRelatedNugget.control.showLoading(!1),this._ctlRelatedNugget.control.showRefresh(!0)),t||(t=""),this._pnlValidateErrorText.innerHTML=t)},getData:function(){this._intCountImages==0;var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/StockMultipleImageDragDrop");n.set_DataObject("StockMultipleImageDragDrop");n.set_DataAction("GetData");n.addParameter("id",this._StockNo);n.addDataOK(Function.createDelegate(this,this.getDataOK));n.addError(Function.createDelegate(this,this.getDataError));n.addTimeout(Function.createDelegate(this,this.getDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getDataOK:function(n){var f=n._result,u=n._result,i,r,t;if($R_FN.setInnerHTML(this._pnlImages,""),i="",u.Items){for(this.intCountImages=u.Items.length,r=0;r<u.Items.length;r++)t=u.Items[r],i+='<div class="stockImage">',i+=String.format('<img id="{0}_img{1}" src="{2}" border="0" />',this._element.id,r,this.getImageSource(t.ID,"t",""+t.ImageName+"","STOCKIMGFORSAN")),i+='<div class="stockImageCaption">',i+=String.format("<input type=\"checkbox\" id=\"{0}_chk{1}\" value=\"{2}\" onchange=\"$find('{3}').getImageValue('{4}','{5}_chk{6}','{7}');\" />",this._element.id,r,this.getImageSource(t.ID,"f",""+t.ImageName+"","STOCKIMGFORSAN"),this._element.id,this.getImageSource(t.ID,"f",""+t.ImageName+"","STOCKIMGFORSAN"),this._element.id,r,t.Caption),t.Caption&&(i+=t.Caption+"<br />"),i+=t.Date,t.By&&(i+="<br />"+t.By),i+=String.format("<br /><a href=\"javascript:void(0);\" onclick=\"$find('{0}').popupImage({1}, 'm','{4}','STOCKIMGFORSAN');\">{2}<\/a> | <a href=\"javascript:void(0);\" onclick=\"$find('{0}').popupImage({1}, 'f','{4}','STOCKIMGFORSAN');\">{3}<\/a>",this._element.id,t.ID,this._strMedium,this._strLarge,t.ImageName),i+="<\/div>",i+="<\/div>",t=null;this._intCountImages=u.Items.length}$R_FN.setInnerHTML(this._pnlImages,i)},getDataError:function(n){this.showError(!0,n.get_ErrorMessage())},getDataCount:function(){this.getData_Start();this._intCountImages==0;var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/StockMultipleImageDragDrop");n.set_DataObject("StockMultipleImageDragDrop");n.set_DataAction("GetData");n.addParameter("id",this._intStockID);n.addDataOK(Function.createDelegate(this,this.getDataCountOK));n.addError(Function.createDelegate(this,this.getDataError));n.addTimeout(Function.createDelegate(this,this.getDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getDataCountOK:function(n){var r=n._result,i=n._result,t;$R_FN.setInnerHTML(this._pnlImages,"");t="";i.Items&&(this._intCountImages=i.Items.length,t+='<div style="height:50px;"><\/div>');$R_FN.setInnerHTML(this._pnlImages,t);this.getDataOK_End();this.enableViewButton();this.viewButtonAfterDelete(this._intCountImages>0)},getImageSource:function(n,t,i,r){return String.format("StockImage.ashx?img={0}&typ={1}&imagename={2}&imagesourcefrom={3}",n,t,i,r)},popupImage:function(n,t,i,r){t=="m"?window.open(this.getImageSource(n,t,i,r),"","left=300,top=250,width=654,height=488,toolbar=no,resizable=yes,status=no,directories=no,location=no,menubar=no,scrollbars=yes"):window.open(this.getImageSource(n,t,i,r),"","left=300,top=170,width=802,height=602,toolbar=no,resizable=yes,status=no,directories=no,location=no,menubar=no,scrollbars=yes")},getImageValue:function(n,t,i){var r=document.getElementById(t).checked;r==!0?this._selectedImageList.push(n+"#"+i):this._selectedImageList.pop(n+"#"+i)},readTextFile:function(n){var t=new XMLHttpRequest;t.open("GET",n,!1);t.onreadystatechange=function(){if(t.readyState===4&&(t.status===200||t.status==0)){var n=t.responseText;alert(n)}};t.send(null)}};Rebound.GlobalTrader.Site.Controls.Forms.GILines_Export.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.GILines_Export",Rebound.GlobalTrader.Site.Controls.Forms.Base,Sys.IDisposable);