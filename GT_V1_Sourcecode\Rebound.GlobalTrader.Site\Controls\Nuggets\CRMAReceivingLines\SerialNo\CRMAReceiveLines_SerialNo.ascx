<%@ Control Language="C#" CodeBehind="CRMAReceiveLines_SerialNo.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Forms.CRMAReceiveLines_SerialNo" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>

<ReboundUI_Form:DesignBase ID="ctlDB" runat="server" ShowRequiredFieldsExplanation="true" ShowQuickHelp="false">
	<Links>
		<ReboundUI:IconButton ID="ibtnSave" runat="server" IconGroup="Nugget" IconTitleResource="Save" IconCSSType="Save" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
		<ReboundUI:IconButton ID="ibtnCancel" runat="server" IconGroup="Nugget" IconTitleResource="Cancel" IconCSSType="Cancel" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
	</Links>

	<%--<Explanation><%=Functions.GetGlobalResource("FormExplanations", "CusReqMainInfo_Edit")%></Explanation>--%>
	
	<Content>
		<ReboundUI_Table:Form id="frm" runat="server">
			                    
             <ReboundUI_Form:FormField id="ctlSubGroup" runat="server" FieldID="ddlGroup" ResourceTitle="SubGroup"  IsRequiredField="true">
				<Field><ReboundDropDown:Group ID="ddlGroup" runat="server"  NoValue_Value="" InitialValue=""  Width="200"/></Field>
			</ReboundUI_Form:FormField>

            <ReboundUI_Form:FormField id="ctlSerailNo" runat="server" FieldID="cmbSerialNo" ResourceTitle="SerialNo" IsRequiredField="true">
				<Field><ReboundUI:Combo ID="cmbSerialNo" runat="server" AutoSearchControlAssembly="Rebound.GlobalTrader.Site" PanelWidth="250" PanelHeight="250"  AutoSearchControlType="SerialNo" />
                     </Field>
			</ReboundUI_Form:FormField>

           
            			
            <ReboundUI_Form:FormField id="ctlAddUpdate" runat="server"  FieldID="btnAdd" >
				<Field>
                 <ReboundUI:IconButton ID="btnAdd" runat="server" IconGroup="Nugget" IconButtonMode="HyperLink" IconCSSType="Add"  ForeColor="#ffff99" BackColor="White" IconTitleResource="Add" CssClass="btnAddReset" />
               <%-- <ReboundUI:IconButton ID="btnUpdate" runat="server" IconGroup="Nugget" IconButtonMode="HyperLink" IconCSSType="Edit" ForeColor="#ffff99" BackColor="White" IconTitleResource="Update" />--%>
                <ReboundUI:IconButton ID="btnRefresh" runat="server" IconButtonMode="HyperLink" IconCSSType="AddUpdate" ForeColor="#ffff99" BackColor="White" IconTitleResource="Reset" CssClass="btnAddReset"  />
                <asp:Label ID="lblDuplicateError" Text="Duplicate Serial Numbers not allowed"  runat="server" CssClass="PartDetailsGridGoError" />
				</Field>
                   
			</ReboundUI_Form:FormField>

		
        <ReboundUI_Form:FormField id="ctlSerialNoDetail" runat="server" FieldID="tblSerialNodetails">
				<Field>	
                    <asp:Panel ID="pnlSerialNodetails" runat="server" CssClass="GridPartdetails">
                    <Reboundui:FlexiDataTable id="tblSerialNodetails"  runat="server"  PanelHeight="200"  />
                        </asp:Panel>

				</Field>
			</ReboundUI_Form:FormField>
			
		</ReboundUI_Table:Form>
	
	</Content>
</ReboundUI_Form:DesignBase>
