<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Action" xml:space="preserve">
    <value>Action</value>
  </data>
  <data name="ActivityDate" xml:space="preserve">
    <value>Activity Date</value>
  </data>
  <data name="Address" xml:space="preserve">
    <value>Address</value>
  </data>
  <data name="AddressName" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="AddressType" xml:space="preserve">
    <value>Type</value>
  </data>
  <data name="AirWayBill" xml:space="preserve">
    <value>Air Way Bill</value>
  </data>
  <data name="Allocated" xml:space="preserve">
    <value>Allocated</value>
  </data>
  <data name="Authoriser" xml:space="preserve">
    <value>Authoriser</value>
  </data>
  <data name="Buy" xml:space="preserve">
    <value>Buy</value>
  </data>
  <data name="Buyer" xml:space="preserve">
    <value>Buyer</value>
  </data>
  <data name="By" xml:space="preserve">
    <value>By</value>
  </data>
  <data name="CeaseDate" xml:space="preserve">
    <value>Cease Date</value>
  </data>
  <data name="Charge" xml:space="preserve">
    <value>Charge</value>
  </data>
  <data name="City" xml:space="preserve">
    <value>City</value>
  </data>
  <data name="Code" xml:space="preserve">
    <value>Code</value>
  </data>
  <data name="CommunicationLogType" xml:space="preserve">
    <value>Communication Log Type</value>
  </data>
  <data name="Company" xml:space="preserve">
    <value>Company</value>
  </data>
  <data name="CompanyName" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="CompanyType" xml:space="preserve">
    <value>Type</value>
  </data>
  <data name="Contact" xml:space="preserve">
    <value>Contact</value>
  </data>
  <data name="ContactLogContactName" xml:space="preserve">
    <value>Contact</value>
  </data>
  <data name="EntertainTypes" xml:space="preserve">
    <value>Entertain Types</value>
  </data>
  <data name="ContactLogDetails" xml:space="preserve">
    <value>Notes</value>
  </data>
  <data name="EntertainDate" xml:space="preserve">
    <value>Entertain Date</value>
  </data>
  <data name="ContactLogType" xml:space="preserve">
    <value>Type</value>
  </data>
  <data name="ContactName" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="Cost" xml:space="preserve">
    <value>Cost</value>
  </data>
  <data name="Country" xml:space="preserve">
    <value>Country</value>
  </data>
  <data name="CountryOfManufacture" xml:space="preserve">
    <value>Country Of Manufacture</value>
  </data>
  <data name="CreditDate" xml:space="preserve">
    <value>Credit Date</value>
  </data>
  <data name="CreditNote" xml:space="preserve">
    <value>Credit</value>
  </data>
  <data name="CRMA" xml:space="preserve">
    <value>CRMA</value>
  </data>
  <data name="CurrentRate" xml:space="preserve">
    <value>Current Rate</value>
  </data>
  <data name="CurrentRate2" xml:space="preserve">
    <value>Current Rate 2</value>
  </data>
  <data name="Customer" xml:space="preserve">
    <value>Customer</value>
  </data>
  <data name="CustomerPart" xml:space="preserve">
    <value>Customer Part</value>
  </data>
  <data name="CustomerPO" xml:space="preserve">
    <value>Customer PO</value>
  </data>
  <data name="CustomerPurchaseOrderNo" xml:space="preserve">
    <value>Customer PO No</value>
  </data>
  <data name="CustomerRequirement" xml:space="preserve">
    <value>Req</value>
  </data>
  <data name="CustomerRMADate" xml:space="preserve">
    <value>CRMA Date</value>
  </data>
  <data name="Date" xml:space="preserve">
    <value>Date</value>
  </data>
  <data name="DateCode" xml:space="preserve">
    <value>DC</value>
  </data>
  <data name="DateDelivered" xml:space="preserve">
    <value>Delivered</value>
  </data>
  <data name="DateDue" xml:space="preserve">
    <value>Due</value>
  </data>
  <data name="DateInvoiced" xml:space="preserve">
    <value>Invoiced</value>
  </data>
  <data name="DateOffered" xml:space="preserve">
    <value>Offered</value>
  </data>
  <data name="DateOrdered" xml:space="preserve">
    <value>Ordered</value>
  </data>
  <data name="DatePromised" xml:space="preserve">
    <value>Promised</value>
  </data>
  <data name="DateQuoted" xml:space="preserve">
    <value>Quoted</value>
  </data>
  <data name="DateReceived" xml:space="preserve">
    <value>Received</value>
  </data>
  <data name="DateReceived1" xml:space="preserve">
    <value>Received Date</value>
  </data>
  <data name="Days" xml:space="preserve">
    <value>Days</value>
  </data>
  <data name="DebitDate" xml:space="preserve">
    <value>Debit Date</value>
  </data>
  <data name="DebitNote" xml:space="preserve">
    <value>Debit</value>
  </data>
  <data name="DeliveryDate" xml:space="preserve">
    <value>Delivery Date</value>
  </data>
  <data name="Description" xml:space="preserve">
    <value>Description</value>
  </data>
  <data name="Detail" xml:space="preserve">
    <value>Detail</value>
  </data>
  <data name="Details" xml:space="preserve">
    <value>Details</value>
  </data>
  <data name="Division" xml:space="preserve">
    <value>Division</value>
  </data>
  <data name="Document" xml:space="preserve">
    <value>Document</value>
  </data>
  <data name="Due" xml:space="preserve">
    <value>Due</value>
  </data>
  <data name="DueDate" xml:space="preserve">
    <value>Due Date</value>
  </data>
  <data name="DutyCode" xml:space="preserve">
    <value>Duty Code</value>
  </data>
  <data name="EECMember" xml:space="preserve">
    <value>EEC Member</value>
  </data>
  <data name="Email" xml:space="preserve">
    <value>Email</value>
  </data>
  <data name="EnteredBy" xml:space="preserve">
    <value>Entered By</value>
  </data>
  <data name="ExpediteDate" xml:space="preserve">
    <value>Expedite Date</value>
  </data>
  <data name="Extension" xml:space="preserve">
    <value>Ext</value>
  </data>
  <data name="Fax" xml:space="preserve">
    <value>Fax</value>
  </data>
  <data name="FooterText" xml:space="preserve">
    <value>Footer Text</value>
  </data>
  <data name="From" xml:space="preserve">
    <value>From</value>
  </data>
  <data name="FullPart" xml:space="preserve">
    <value>Full Part</value>
  </data>
  <data name="GlobalCountryList" xml:space="preserve">
    <value>Master Country</value>
  </data>
  <data name="GlobalCurrencyList" xml:space="preserve">
    <value>Master Currency</value>
  </data>
  <data name="GoodsIn" xml:space="preserve">
    <value>Goods In</value>
  </data>
  <data name="GoodsInDate" xml:space="preserve">
    <value>Goods In Date</value>
  </data>
  <data name="GoodsInNo" xml:space="preserve">
    <value>Goods In Note</value>
  </data>
  <data name="GP" xml:space="preserve">
    <value>GP (%)</value>
  </data>
  <data name="GPDetail" xml:space="preserve">
    <value>GP Detail</value>
  </data>
  <data name="InAdvance" xml:space="preserve">
    <value>In Advance</value>
  </data>
  <data name="Include" xml:space="preserve">
    <value>Include</value>
  </data>
  <data name="IndustryType" xml:space="preserve">
    <value>Industry Type</value>
  </data>
  <data name="Invoice" xml:space="preserve">
    <value>Invoice</value>
  </data>
  <data name="InvoiceAmount" xml:space="preserve">
    <value>Amount</value>
  </data>
  <data name="InvoiceDate" xml:space="preserve">
    <value>Invoice Date</value>
  </data>
  <data name="InvoiceNo" xml:space="preserve">
    <value>Invoice No</value>
  </data>
  <data name="InvoiceTotal" xml:space="preserve">
    <value>Invoice Total</value>
  </data>
  <data name="IPAddress" xml:space="preserve">
    <value>IP Address</value>
  </data>
  <data name="IsConsignment" xml:space="preserve">
    <value>Consignment?</value>
  </data>
  <data name="IsDefault" xml:space="preserve">
    <value>Default?</value>
  </data>
  <data name="IsDefaultBill" xml:space="preserve">
    <value>Default Bill?</value>
  </data>
  <data name="IsDefaultPO" xml:space="preserve">
    <value>Default PO?</value>
  </data>
  <data name="IsDefaultShip" xml:space="preserve">
    <value>Default Ship?</value>
  </data>
  <data name="IsDefaultSO" xml:space="preserve">
    <value>Default SO?</value>
  </data>
  <data name="IsOnHold" xml:space="preserve">
    <value>On Hold?</value>
  </data>
  <data name="IsPrinted" xml:space="preserve">
    <value>Printed?</value>
  </data>
  <data name="Item" xml:space="preserve">
    <value>Item</value>
  </data>
  <data name="JobTitle" xml:space="preserve">
    <value>Job Title</value>
  </data>
  <data name="LandedCost" xml:space="preserve">
    <value>Landed Cost</value>
  </data>
  <data name="LastContacted" xml:space="preserve">
    <value>Last Contacted</value>
  </data>
  <data name="LastReceived" xml:space="preserve">
    <value>Last Received</value>
  </data>
  <data name="ListName" xml:space="preserve">
    <value>List Name</value>
  </data>
  <data name="Location" xml:space="preserve">
    <value>Location</value>
  </data>
  <data name="Login" xml:space="preserve">
    <value>Login</value>
  </data>
  <data name="Lot" xml:space="preserve">
    <value>Lot</value>
  </data>
  <data name="Manufacturer" xml:space="preserve">
    <value>Manufacturer</value>
  </data>
  <data name="ManufacturerAbbreviation" xml:space="preserve">
    <value>Mfr</value>
  </data>
  <data name="ManufacturerName" xml:space="preserve">
    <value>Manufacturer Name</value>
  </data>
  <data name="Members" xml:space="preserve">
    <value>Members</value>
  </data>
  <data name="Name" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="NextNumber" xml:space="preserve">
    <value>Next Number</value>
  </data>
  <data name="Notes" xml:space="preserve">
    <value>Notes</value>
  </data>
  <data name="Number" xml:space="preserve">
    <value>No</value>
  </data>
  <data name="NumberOfUsers" xml:space="preserve">
    <value>Users</value>
  </data>
  <data name="OfferPrice" xml:space="preserve">
    <value>Offer Price</value>
  </data>
  <data name="OpenOrdersRepriced" xml:space="preserve">
    <value>Open Orders Repriced?</value>
  </data>
  <data name="OrderValue" xml:space="preserve">
    <value>Order Value</value>
  </data>
  <data name="Package" xml:space="preserve">
    <value>Package</value>
  </data>
  <data name="Part" xml:space="preserve">
    <value>Part</value>
  </data>
  <data name="PartNo" xml:space="preserve">
    <value>Part No</value>
  </data>
  <data name="PostedValue" xml:space="preserve">
    <value>Posted Lines Value</value>
  </data>
  <data name="Price" xml:space="preserve">
    <value>Unit Price</value>
  </data>
  <data name="Priority" xml:space="preserve">
    <value>Priority</value>
  </data>
  <data name="Product" xml:space="preserve">
    <value>Product</value>
  </data>
  <data name="ProductAndPackage" xml:space="preserve">
    <value>Prod / Pack</value>
  </data>
  <data name="PurchaseOrder" xml:space="preserve">
    <value>PO</value>
  </data>
  <data name="PurchaseOrderDate" xml:space="preserve">
    <value>PO Date</value>
  </data>
  <data name="PurchasePrice" xml:space="preserve">
    <value>Purchase Request</value>
  </data>
  <data name="QualityControlNotes" xml:space="preserve">
    <value>Notes</value>
  </data>
  <data name="Quantity" xml:space="preserve">
    <value>Quantity</value>
  </data>
  <data name="QuantityAllocated" xml:space="preserve">
    <value>Qty Allocated</value>
  </data>
  <data name="QuantityAuthorised" xml:space="preserve">
    <value>Qty Authorised</value>
  </data>
  <data name="QuantityAvailable" xml:space="preserve">
    <value>Qty Available</value>
  </data>
  <data name="QuantityBackOrder" xml:space="preserve">
    <value>Qty BackOrder</value>
  </data>
  <data name="QuantityInStock" xml:space="preserve">
    <value>Qty In Stock(FREE)</value>
  </data>
  <data name="QuantityOnOrder" xml:space="preserve">
    <value>Qty On Order</value>
  </data>
  <data name="QuantityOrdered" xml:space="preserve">
    <value>Qty Ordered</value>
  </data>
  <data name="QuantityOutstanding" xml:space="preserve">
    <value>Qty Outstanding</value>
  </data>
  <data name="QuantityReceived" xml:space="preserve">
    <value>Qty Received</value>
  </data>
  <data name="QuantityRemaining" xml:space="preserve">
    <value>Qty Remaining</value>
  </data>
  <data name="QuantityShipped" xml:space="preserve">
    <value>Qty Shipped</value>
  </data>
  <data name="Quarantined" xml:space="preserve">
    <value>Quarantined</value>
  </data>
  <data name="QuarantinedAbbreviation" xml:space="preserve">
    <value>Qtnd?</value>
  </data>
  <data name="Quote" xml:space="preserve">
    <value>Quote</value>
  </data>
  <data name="RaisedBy" xml:space="preserve">
    <value>Raised By</value>
  </data>
  <data name="Rate" xml:space="preserve">
    <value>Rate</value>
  </data>
  <data name="Rate2" xml:space="preserve">
    <value>Rate 2</value>
  </data>
  <data name="Reason" xml:space="preserve">
    <value>Reason</value>
  </data>
  <data name="Received" xml:space="preserve">
    <value>Received</value>
  </data>
  <data name="ReceivedBy" xml:space="preserve">
    <value>Received By</value>
  </data>
  <data name="ReceivedDate" xml:space="preserve">
    <value>Received Date</value>
  </data>
  <data name="Receiver" xml:space="preserve">
    <value>Received By</value>
  </data>
  <data name="Reference" xml:space="preserve">
    <value>Reference</value>
  </data>
  <data name="RequiredDate" xml:space="preserve">
    <value>HUBRFQ Required Date</value>
  </data>
  <data name="Requirement" xml:space="preserve">
    <value>Req</value>
  </data>
  <data name="Resale" xml:space="preserve">
    <value>Resale</value>
  </data>
  <data name="ResalePrice" xml:space="preserve">
    <value>Resale Price</value>
  </data>
  <data name="ReturnDate" xml:space="preserve">
    <value>Return Date</value>
  </data>
  <data name="ROHS" xml:space="preserve">
    <value>ROHS</value>
  </data>
  <data name="Salesman" xml:space="preserve">
    <value>Salesperson</value>
  </data>
  <data name="SalesOrder" xml:space="preserve">
    <value>SO</value>
  </data>
  <data name="Salesperson" xml:space="preserve">
    <value>Salesperson</value>
  </data>
  <data name="SalesValue" xml:space="preserve">
    <value>Sales Value</value>
  </data>
  <data name="SecurityGroup" xml:space="preserve">
    <value>Group</value>
  </data>
  <data name="Sell" xml:space="preserve">
    <value>Sell</value>
  </data>
  <data name="SellingPrice" xml:space="preserve">
    <value>Selling Price</value>
  </data>
  <data name="Service" xml:space="preserve">
    <value>Service</value>
  </data>
  <data name="ShipDate" xml:space="preserve">
    <value>Ship Date</value>
  </data>
  <data name="ShipInCost" xml:space="preserve">
    <value>Ship In Cost</value>
  </data>
  <data name="ShippedBy" xml:space="preserve">
    <value>Shipped By</value>
  </data>
  <data name="Shipper" xml:space="preserve">
    <value>Shipper</value>
  </data>
  <data name="ShippingCost" xml:space="preserve">
    <value>Shipping Cost</value>
  </data>
  <data name="ShipStatus" xml:space="preserve">
    <value>Ship Status</value>
  </data>
  <data name="ExportApprovalStatus" xml:space="preserve">
    <value>Export Approval Status</value>
  </data>
  <data name="SOLineNumber" xml:space="preserve">
    <value>SO Line Number</value>
  </data>
  <data name="ShipVia" xml:space="preserve">
    <value>Shipping Method</value>
  </data>
  <data name="SRMA" xml:space="preserve">
    <value>SRMA</value>
  </data>
  <data name="StartDate" xml:space="preserve">
    <value>Start Date</value>
  </data>
  <data name="StartTime" xml:space="preserve">
    <value>Start Time</value>
  </data>
  <data name="Status" xml:space="preserve">
    <value>Status</value>
  </data>
  <data name="StockCount" xml:space="preserve">
    <value>Stock Count</value>
  </data>
  <data name="Subject" xml:space="preserve">
    <value>Subject</value>
  </data>
  <data name="Supplier" xml:space="preserve">
    <value>Supplier</value>
  </data>
  <data name="SupplierInvoice" xml:space="preserve">
    <value>Supplier Invoice</value>
  </data>
  <data name="SupplierPart" xml:space="preserve">
    <value>Supplier Part</value>
  </data>
  <data name="SupplierRating" xml:space="preserve">
    <value>Rating</value>
  </data>
  <data name="SupplierRMADate" xml:space="preserve">
    <value>Supplier RMA Date</value>
  </data>
  <data name="Symbol" xml:space="preserve">
    <value>Symbol</value>
  </data>
  <data name="Table" xml:space="preserve">
    <value>Activity</value>
  </data>
  <data name="Task" xml:space="preserve">
    <value>Task</value>
  </data>
  <data name="Tax" xml:space="preserve">
    <value>Tax</value>
  </data>
  <data name="Team" xml:space="preserve">
    <value>Team</value>
  </data>
  <data name="Tel" xml:space="preserve">
    <value>Tel</value>
  </data>
  <data name="TelephonePrefix" xml:space="preserve">
    <value>Dialling Code</value>
  </data>
  <data name="Terms" xml:space="preserve">
    <value>Terms</value>
  </data>
  <data name="Text" xml:space="preserve">
    <value>Text</value>
  </data>
  <data name="Time" xml:space="preserve">
    <value>Time</value>
  </data>
  <data name="To" xml:space="preserve">
    <value>To</value>
  </data>
  <data name="Total" xml:space="preserve">
    <value>Total</value>
  </data>
  <data name="Type" xml:space="preserve">
    <value>Type</value>
  </data>
  <data name="URL" xml:space="preserve">
    <value>URL</value>
  </data>
  <data name="User" xml:space="preserve">
    <value>User</value>
  </data>
  <data name="Value" xml:space="preserve">
    <value>Value</value>
  </data>
  <data name="Warehouse" xml:space="preserve">
    <value>Warehouse</value>
  </data>
  <data name="PurchaseRequisition" xml:space="preserve">
    <value>Purchase Requisition</value>
  </data>
  <data name="GrossProfit" xml:space="preserve">
    <value>Gross Profit</value>
  </data>
  <data name="Margin" xml:space="preserve">
    <value>Margin</value>
  </data>
  <data name="IsVirtual" xml:space="preserve">
    <value>Virtual?</value>
  </data>
  <data name="RelatedQuotes" xml:space="preserve">
    <value>Related Quotes</value>
  </data>
  <data name="TargetPrice" xml:space="preserve">
    <value>Target Price</value>
  </data>
  <data name="Title" xml:space="preserve">
    <value>Title</value>
  </data>
  <data name="IsFinanceContact" xml:space="preserve">
    <value>Finance Contact?</value>
  </data>
  <data name="IsDefaultPOLedger" xml:space="preserve">
    <value>Default PO Ledger?</value>
  </data>
  <data name="IsDefaultSOLedger" xml:space="preserve">
    <value>Default SO Ledger?</value>
  </data>
  <data name="AlternatePartNo" xml:space="preserve">
    <value>Alternate Part No</value>
  </data>
  <data name="Additionalnotesfromsupplier" xml:space="preserve">
    <value>Additional notes from supplier</value>
  </data>
  <data name="Apply" xml:space="preserve">
    <value>Apply</value>
  </data>
  <data name="ExchangeRate" xml:space="preserve">
    <value>Exchange Rate</value>
  </data>
  <data name="QuantityInStockForSO" xml:space="preserve">
    <value>Qty In Stock</value>
  </data>
  <data name="NPR" xml:space="preserve">
    <value>NPR</value>
  </data>
  <data name="Select" xml:space="preserve">
    <value>               </value>
  </data>
  <data name="UnitPrice" xml:space="preserve">
    <value>Unit Price</value>
  </data>
  <data name="PurchaseCode" xml:space="preserve">
    <value>Purchase Code</value>
  </data>
  <data name="URNNumber" xml:space="preserve">
    <value>URN Number</value>
  </data>
  <data name="GIDate" xml:space="preserve">
    <value>GI Date</value>
  </data>
  <data name="SupplierCode" xml:space="preserve">
    <value>Supplier Code</value>
  </data>
  <data name="SalesCode" xml:space="preserve">
    <value>Sales Code</value>
  </data>
  <data name="ShippingInstructions" xml:space="preserve">
    <value>Shipping Instructions</value>
  </data>
  <data name="Printer" xml:space="preserve">
    <value>Printer</value>
  </data>
  <data name="SRMANo" xml:space="preserve">
    <value>SRMA No</value>
  </data>
  <data name="CurrentLandedCost" xml:space="preserve">
    <value>Current Landed Cost</value>
  </data>
  <data name="OriginalLandedCost" xml:space="preserve">
    <value>Original Landed Cost</value>
  </data>
  <data name="NewLandedCost" xml:space="preserve">
    <value>New Landed Cost</value>
  </data>
  <data name="Accounts" xml:space="preserve">
    <value>Accounts</value>
  </data>
  <data name="CeriticateName" xml:space="preserve">
    <value>Certificate Name</value>
  </data>
  <data name="ExpiryDate" xml:space="preserve">
    <value>Expiry Date</value>
  </data>
  <data name="CategoryName" xml:space="preserve">
    <value>Category Name</value>
  </data>
  <data name="CeriticateNumber" xml:space="preserve">
    <value>Certificate Number</value>
  </data>
  <data name="CompType" xml:space="preserve">
    <value>Company Type</value>
  </data>
  <data name="LabelPath" xml:space="preserve">
    <value>Nice Label Path</value>
  </data>
  <data name="LineNo" xml:space="preserve">
    <value>Line No</value>
  </data>
  <data name="PurchaseOrderSerialNo" xml:space="preserve">
    <value>PO (Line No)</value>
  </data>
  <data name="SalesOrderSerialNo" xml:space="preserve">
    <value>SO (Line No)</value>
  </data>
  <data name="SOLineNo" xml:space="preserve">
    <value>SO Line No</value>
  </data>
  <data name="UpdatedBy" xml:space="preserve">
    <value>Updated By</value>
  </data>
  <data name="ConflictResource" xml:space="preserve">
    <value>Conflict Resource</value>
  </data>
  <data name="SRMPPOSerialNo" xml:space="preserve">
    <value>PO Line No</value>
  </data>
  <data name="WHSPOSerialNo" xml:space="preserve">
    <value>PO Line No</value>
  </data>
  <data name="AuthorisedBy" xml:space="preserve">
    <value>Authorised By</value>
  </data>
  <data name="CompletedBy" xml:space="preserve">
    <value>Completed By</value>
  </data>
  <data name="NprNo" xml:space="preserve">
    <value>NPR Number</value>
  </data>
  <data name="NprRaisedDateFrom" xml:space="preserve">
    <value>NPR Raised Date</value>
  </data>
  <data name="PONo" xml:space="preserve">
    <value>Purchase Order Number</value>
  </data>
  <data name="LogByUser" xml:space="preserve">
    <value>Log By User</value>
  </data>
  <data name="LogDate" xml:space="preserve">
    <value>Log Date</value>
  </data>
  <data name="QuantityRejected" xml:space="preserve">
    <value>Quantity Rejected</value>
  </data>
  <data name="ServerIP" xml:space="preserve">
    <value>Server IP</value>
  </data>
  <data name="IsActive" xml:space="preserve">
    <value>Active?</value>
  </data>
  <data name="ClientName" xml:space="preserve">
    <value>Client</value>
  </data>
  <data name="ClientNameEmpty" xml:space="preserve">
    <value>  </value>
  </data>
  <data name="BOM" xml:space="preserve">
    <value>BOM</value>
  </data>
  <data name="ReqNo" xml:space="preserve">
    <value>Req No</value>
  </data>
  <data name="CC" xml:space="preserve">
    <value>CC</value>
  </data>
  <data name="SendToGroup" xml:space="preserve">
    <value>Send To Group</value>
  </data>
  <data name="DatePOQuoted" xml:space="preserve">
    <value>Quoted Date</value>
  </data>
  <data name="BomCode" xml:space="preserve">
    <value>Bom Code</value>
  </data>
  <data name="BomName" xml:space="preserve">
    <value>HUBRFQ Name</value>
  </data>
  <data name="IPOID" xml:space="preserve">
    <value>IPO</value>
  </data>
  <data name="POQuote" xml:space="preserve">
    <value>PO Request</value>
  </data>
  <data name="IsPoHub" xml:space="preserve">
    <value>PoHub</value>
  </data>
  <data name="IPOBOM" xml:space="preserve">
    <value>HUBRFQ</value>
  </data>
  <data name="InternalPurchaseOrderSerialNo" xml:space="preserve">
    <value>IPO</value>
  </data>
  <data name="InternalPurchaseOrder" xml:space="preserve">
    <value>IPO</value>
  </data>
  <data name="Message" xml:space="preserve">
    <value>Log Message</value>
  </data>
  <data name="LogMessageSupplier" xml:space="preserve">
    <value>Supplier Name</value>
  </data>
  <data name="RequestNo" xml:space="preserve">
    <value>Request No </value>
  </data>
  <data name="Client" xml:space="preserve">
    <value>Client</value>
  </data>
  <data name="BaseCurrency" xml:space="preserve">
    <value>Base Currency</value>
  </data>
  <data name="PurchaseRequestNo" xml:space="preserve">
    <value>Price Request</value>
  </data>
  <data name="FactorySealed" xml:space="preserve">
    <value>Factory Sealed</value>
  </data>
  <data name="LeadTime" xml:space="preserve">
    <value>Lead Time (Weeks)</value>
  </data>
  <data name="MSL" xml:space="preserve">
    <value>MSL</value>
  </data>
  <data name="RoHSStatus" xml:space="preserve">
    <value>ROHS Status</value>
  </data>
  <data name="SPQ" xml:space="preserve">
    <value>SPQ</value>
  </data>
  <data name="Requested" xml:space="preserve">
    <value>Requested</value>
  </data>
  <data name="QuoteRequired" xml:space="preserve">
    <value>Quote Required</value>
  </data>
  <data name="RequestedDate" xml:space="preserve">
    <value>Requested Date</value>
  </data>
  <data name="ClientInvoice" xml:space="preserve">
    <value>Client Invoice</value>
  </data>
  <data name="Active" xml:space="preserve">
    <value>Active</value>
  </data>
  <data name="Duration" xml:space="preserve">
    <value>Duration(Days)</value>
  </data>
  <data name="Grouped" xml:space="preserve">
    <value>Grouped</value>
  </data>
  <data name="SchedularStartDate" xml:space="preserve">
    <value>Start Date</value>
  </data>
  <data name="LeatTime" xml:space="preserve">
    <value>Lead Time</value>
  </data>
  <data name="DC" xml:space="preserve">
    <value>DC</value>
  </data>
  <data name="MFR" xml:space="preserve">
    <value>MFR</value>
  </data>
  <data name="Pack" xml:space="preserve">
    <value>Pack</value>
  </data>
  <data name="DateRequired" xml:space="preserve">
    <value>Required</value>
  </data>
  <data name="EstimatedShippingCost" xml:space="preserve">
    <value>EST Shipping Cost</value>
  </data>
  <data name="EstimatedShippingCostInBase" xml:space="preserve">
    <value>Base Currency</value>
  </data>
  <data name="ActualPrice" xml:space="preserve">
    <value>Actual Price</value>
  </data>
  <data name="PoOrIpo" xml:space="preserve">
    <value>PO|IPO (Line No)</value>
  </data>
  <data name="SupplierType" xml:space="preserve">
    <value>Supplier Type</value>
  </data>
  <data name="TotalValue" xml:space="preserve">
    <value>Total Value</value>
  </data>
  <data name="HUBRFQ" xml:space="preserve">
    <value>HUBRFQ</value>
  </data>
  <data name="DateOffered1" xml:space="preserve">
    <value>Offered Date</value>
  </data>
  <data name="LTB" xml:space="preserve">
    <value>LTB</value>
  </data>
  <data name="MOQ" xml:space="preserve">
    <value>MOQ</value>
  </data>
  <data name="TQSA" xml:space="preserve">
    <value>TQSA</value>
  </data>
  <data name="AS9120" xml:space="preserve">
    <value>AS9120</value>
  </data>
  <data name="AssignedUser" xml:space="preserve">
    <value>Assigned User</value>
  </data>
  <data name="PQID" xml:space="preserve">
    <value>PQ Id</value>
  </data>
  <data name="Region" xml:space="preserve">
    <value>Region</value>
  </data>
  <data name="RegionName" xml:space="preserve">
    <value>Region Name</value>
  </data>
  <data name="SupplierTypeHeading" xml:space="preserve">
    <value>Supplier ( Type )</value>
  </data>
  <data name="Consildate" xml:space="preserve">
    <value>Consolidate</value>
  </data>
  <data name="LastRun" xml:space="preserve">
    <value>Last Run</value>
  </data>
  <data name="Narrative" xml:space="preserve">
    <value>Narrative</value>
  </data>
  <data name="SecondRef" xml:space="preserve">
    <value>SecondRef</value>
  </data>
  <data name="TermsName" xml:space="preserve">
    <value>Terms</value>
  </data>
  <data name="FileName" xml:space="preserve">
    <value>File Name</value>
  </data>
  <data name="LoginName" xml:space="preserve">
    <value>Login Name</value>
  </data>
  <data name="IPO" xml:space="preserve">
    <value>IPO</value>
  </data>
  <data name="DebitNumber" xml:space="preserve">
    <value>Debit Number</value>
  </data>
  <data name="APINBC" xml:space="preserve">
    <value>Actual price in Base Currency</value>
  </data>
  <data name="ClientInvoiceNo" xml:space="preserve">
    <value>Client Invoice No</value>
  </data>
  <data name="AdjustMent" xml:space="preserve">
    <value>Adjustment</value>
  </data>
  <data name="PurchaseOrderIPO" xml:space="preserve">
    <value>PurchaseOrder(IPO)</value>
  </data>
  <data name="UnitSellPrice" xml:space="preserve">
    <value>Unit Sell Price</value>
  </data>
  <data name="ActualPriceInBase" xml:space="preserve">
    <value>Buy Price In Base</value>
  </data>
  <data name="BuyPrice" xml:space="preserve">
    <value>Buy Price</value>
  </data>
  <data name="ReqBOMName" xml:space="preserve">
    <value>BOM Name</value>
  </data>
  <data name="AddedDate" xml:space="preserve">
    <value>Date Created</value>
  </data>
  <data name="AssignedUserPartNo" xml:space="preserve">
    <value>AssignedUser/PartNo</value>
  </data>
  <data name="CodeQuantity" xml:space="preserve">
    <value>Code/Quantity</value>
  </data>
  <data name="DateReceivedDate" xml:space="preserve">
    <value>Date/ReceivedDate</value>
  </data>
  <data name="NameNumber" xml:space="preserve">
    <value>Name/Number</value>
  </data>
  <data name="StatusSalesman" xml:space="preserve">
    <value>Status/Salesman</value>
  </data>
  <data name="TotalValueIPOBOM" xml:space="preserve">
    <value>TotalValue/IPOBOM</value>
  </data>
  <data name="employeename" xml:space="preserve">
    <value>Buyer</value>
  </data>
  <data name="IPONo" xml:space="preserve">
    <value>IPO No</value>
  </data>
  <data name="Blank" xml:space="preserve">
    <value />
  </data>
  <data name="PurchaseNo" xml:space="preserve">
    <value>PO No</value>
  </data>
  <data name="CreqNumber" xml:space="preserve">
    <value>Req No</value>
  </data>
  <data name="HUBRFQCode" xml:space="preserve">
    <value>HUBRFQ Code</value>
  </data>
  <data name="HUBRFQName" xml:space="preserve">
    <value>HUBRFQ Name</value>
  </data>
  <data name="IPOStatus" xml:space="preserve">
    <value>IPO Status</value>
  </data>
  <data name="POLineNos" xml:space="preserve">
    <value>PO Line No</value>
  </data>
  <data name="Expeditenote" xml:space="preserve">
    <value>Exp Note</value>
  </data>
  <data name="Comnote" xml:space="preserve">
    <value>Comm Note</value>
  </data>
  <data name="GlobalProductName" xml:space="preserve">
    <value>Product Name</value>
  </data>
  <data name="IPOPrice" xml:space="preserve">
    <value>IPO Price</value>
  </data>
  <data name="SubGroup" xml:space="preserve">
    <value>Box #</value>
  </data>
  <data name="Group" xml:space="preserve">
    <value>Group</value>
  </data>
  <data name="SerialNo" xml:space="preserve">
    <value>Serial No.</value>
  </data>
  <data name="GI" xml:space="preserve">
    <value>Goods In</value>
  </data>
  <data name="IPOPriceWithCur" xml:space="preserve">
    <value>Latest IPO Price</value>
  </data>
  <data name="BOX" xml:space="preserve">
    <value>BOX</value>
  </data>
  <data name="ReasonNote" xml:space="preserve">
    <value>Reason Note</value>
  </data>
  <data name="ContractNo" xml:space="preserve">
    <value>Contract No</value>
  </data>
  <data name="Turnover" xml:space="preserve">
    <value>SP Turnover/ Profit YTD</value>
  </data>
  <data name="PromiseDate" xml:space="preserve">
    <value>Promise Date</value>
  </data>
  <data name="NotesMSL" xml:space="preserve">
    <value>Notes/MSL</value>
  </data>
  <data name="AvailableCredit" xml:space="preserve">
    <value>Available Credit</value>
  </data>
  <data name="CustomerName" xml:space="preserve">
    <value>Customer Name</value>
  </data>
  <data name="IsMailSent" xml:space="preserve">
    <value>Notified?</value>
  </data>
  <data name="HUBRFQNo" xml:space="preserve">
    <value>HUBRFQ No</value>
  </data>
  <data name="DateConfirmed" xml:space="preserve">
    <value>Date Confirmed</value>
  </data>
  <data name="TestingRecommended" xml:space="preserve">
    <value>Testing Recommended</value>
  </data>
  <data name="TotalInBase" xml:space="preserve">
    <value>Total In Base</value>
  </data>
  <data name="ClientCode" xml:space="preserve">
    <value>Client Code</value>
  </data>
  <data name="RequestedBy" xml:space="preserve">
    <value>Requested By</value>
  </data>
  <data name="DateShipped" xml:space="preserve">
    <value>Shipped</value>
  </data>
  <data name="DeliveryStatus" xml:space="preserve">
    <value>Delivery Status</value>
  </data>
  <data name="POIPONo" xml:space="preserve">
    <value>PO | IPO No.</value>
  </data>
  <data name="UpdatedDate" xml:space="preserve">
    <value>Updated Date</value>
  </data>
  <data name="LabelSetupName" xml:space="preserve">
    <value>Status name</value>
  </data>
  <data name="ClientBOMNo" xml:space="preserve">
    <value>BOM No</value>
  </data>
  <data name="ClientBOMName" xml:space="preserve">
    <value>BOM Name</value>
  </data>
  <data name="ImportDate" xml:space="preserve">
    <value>Import Date</value>
  </data>
  <data name="NoOfRequirement" xml:space="preserve">
    <value>No Of Requirement</value>
  </data>
  <data name="RecordsProcessed" xml:space="preserve">
    <value>Records Processed</value>
  </data>
  <data name="RecordsRemaining" xml:space="preserve">
    <value>Records Remaining</value>
  </data>
  <data name="Empty" xml:space="preserve">
    <value />
  </data>
  <data name="RequireASAP" xml:space="preserve">
    <value>Require ASAP</value>
  </data>
  <data name="LocalCurrency" xml:space="preserve">
    <value>Local Currency</value>
  </data>
  <data name="AppliedDivision" xml:space="preserve">
    <value>Division</value>
  </data>
  <data name="EmailId" xml:space="preserve">
    <value>Email Id</value>
  </data>
  <data name="Telephone" xml:space="preserve">
    <value>Telephone</value>
  </data>
  <data name="ClientBillTo" xml:space="preserve">
    <value>Client Bill To</value>
  </data>
  <data name="CustomerCode" xml:space="preserve">
    <value>Customer Code</value>
  </data>
  <data name="ShipViaName" xml:space="preserve">
    <value>Ship Via </value>
  </data>
  <data name="TaxName" xml:space="preserve">
    <value>Tax</value>
  </data>
  <data name="UpliftPrice" xml:space="preserve">
    <value>Uplift Price</value>
  </data>
  <data name="AveragePrice" xml:space="preserve">
    <value>Average Price</value>
  </data>
  <data name="CountryOfOrigin" xml:space="preserve">
    <value>Country Of Origin</value>
  </data>
  <data name="HTSCode" xml:space="preserve">
    <value>HTS Code</value>
  </data>
  <data name="LifeCycleStage" xml:space="preserve">
    <value>Life Cycle Stage</value>
  </data>
  <data name="Packaging" xml:space="preserve">
    <value>Packaging</value>
  </data>
  <data name="PackagingSize" xml:space="preserve">
    <value>Packaging Size</value>
  </data>
  <data name="IHSID" xml:space="preserve">
    <value>IHS ID</value>
  </data>
  <data name="Descriptions" xml:space="preserve">
    <value>Descriptions</value>
  </data>
  <data name="HTCCode" xml:space="preserve">
    <value>HTS Code</value>
  </data>
  <data name="PackSize" xml:space="preserve">
    <value>Size</value>
  </data>
  <data name="PartStatus" xml:space="preserve">
    <value>Part Status</value>
  </data>
  <data name="PackagingCode" xml:space="preserve">
    <value>Packaging Code</value>
  </data>
  <data name="GlobalAvgPrice" xml:space="preserve">
    <value>Global Avg Price</value>
  </data>
  <data name="PackageMethod" xml:space="preserve">
    <value>Package Method</value>
  </data>
  <data name="PackingCode" xml:space="preserve">
    <value>Packing Code</value>
  </data>
  <data name="QtyUnallocatedAll" xml:space="preserve">
    <value>Qty Unallocated (In Stock/On Order)</value>
  </data>
  <data name="QtyUnallocatedOnOrder" xml:space="preserve">
    <value>Qty Unallocated (On Order)</value>
  </data>
  <data name="QtyUnallocatedStock" xml:space="preserve">
    <value>Qty Unallocated (In Stock)</value>
  </data>
  <data name="SupportTeamMember" xml:space="preserve">
    <value>Support Team Member</value>
  </data>
  <data name="ADLoginName" xml:space="preserve">
    <value>AD Login Name</value>
  </data>
  <data name="EmployeeNames" xml:space="preserve">
    <value>Employee Name</value>
  </data>
  <data name="ECCNCode" xml:space="preserve">
    <value>ECCN Code</value>
  </data>
  <data name="REQStatus" xml:space="preserve">
    <value>REQ Line Status</value>
  </data>
  <data name="PDFDocument" xml:space="preserve">
    <value>Document</value>
  </data>
  <data name="PriceToClient" xml:space="preserve">
    <value>Price To Client</value>
  </data>
  <data name="ProfitPercentage" xml:space="preserve">
    <value>Profit (%)</value>
  </data>
  <data name="HUBSTATUS" xml:space="preserve">
    <value>HUB STATUS</value>
  </data>
  <data name="PDFIHSDOCtitle" xml:space="preserve">
    <value>(Click to view &amp; add docs)</value>
  </data>
  <data name="Profit" xml:space="preserve">
    <value>Profit</value>
  </data>
  <data name="IsShortageRefundIssue" xml:space="preserve">
    <value>Credit refund given?</value>
  </data>
  <data name="ManufacturerNo" xml:space="preserve">
    <value>Manufacturer</value>
  </data>
  <data name="PurchaseOrderNo" xml:space="preserve">
    <value>PO No</value>
  </data>
  <data name="ShortageQuantity" xml:space="preserve">
    <value>Shortage Qty</value>
  </data>
  <data name="ShortShipmentId" xml:space="preserve">
    <value>Short Shipment Id</value>
  </data>
  <data name="ShortValue" xml:space="preserve">
    <value>Shortage Value</value>
  </data>
  <data name="PartWatchMatch" xml:space="preserve">
    <value>HUB PartWatch Match</value>
  </data>
  <data name="BuyExchangeRate" xml:space="preserve">
    <value>Buy Exchange Rate</value>
  </data>
  <data name="SellExchangeRate" xml:space="preserve">
    <value>Sell Exchange Rate</value>
  </data>
  <data name="MarginPercentage" xml:space="preserve">
    <value>Margin %</value>
  </data>
  <data name="MarginValue" xml:space="preserve">
    <value>Margin Value</value>
  </data>
  <data name="ApprovedOrdTwlMonth" xml:space="preserve">
    <value>POs in the last 12 months</value>
  </data>
  <data name="RMAsTwlMonths" xml:space="preserve">
    <value>RMAs Last 12 Months</value>
  </data>
  <data name="SALnMngrApproval" xml:space="preserve">
    <value>Line Manager Approval</value>
  </data>
  <data name="SAPurchaseMethod" xml:space="preserve">
    <value>Purchasing Method</value>
  </data>
  <data name="SAQtyApproved" xml:space="preserve">
    <value>Quality Approval</value>
  </data>
  <data name="SAApprovalsStatus" xml:space="preserve">
    <value>Approvals Status</value>
  </data>
  <data name="SAPreviousSupplier" xml:space="preserve">
    <value>Previous Supplier</value>
  </data>
  <data name="SARefAttachment" xml:space="preserve">
    <value>Attachment</value>
  </data>
  <data name="SATradeRefOne" xml:space="preserve">
    <value>Trade Reference 1</value>
  </data>
  <data name="SATradeRefThree" xml:space="preserve">
    <value>Trade Reference 3</value>
  </data>
  <data name="SATradeRefTwo" xml:space="preserve">
    <value>Trade Reference 2</value>
  </data>
  <data name="LineMgrApprovalStatus" xml:space="preserve">
    <value>Line Mgr</value>
  </data>
  <data name="QualityApprovalStatus" xml:space="preserve">
    <value>Quality </value>
  </data>
  <data name="BreakdownPackSize" xml:space="preserve">
    <value>Pack Size</value>
  </data>
  <data name="NumberOfPacks" xml:space="preserve">
    <value>Number of Packs</value>
  </data>
  <data name="PackagingBreakdownType" xml:space="preserve">
    <value>Packaging Breakdown Type</value>
  </data>
  <data name="TotalNoPackSize" xml:space="preserve">
    <value>Total (system calculate) Number of Packs x Size</value>
  </data>
  <data name="DateCodes" xml:space="preserve">
    <value>Date Codes</value>
  </data>
  <data name="LotNo" xml:space="preserve">
    <value>Lot Code</value>
  </data>
  <data name="Manager" xml:space="preserve">
    <value>Manager</value>
  </data>
  <data name="CheckedBy" xml:space="preserve">
    <value>Inspected by</value>
  </data>
  <data name="EnhancedInspection" xml:space="preserve">
    <value>Enhanced Inspection</value>
  </data>
  <data name="ShipAsap" xml:space="preserve">
    <value>Ship ASAP</value>
  </data>
  <data name="SetWarnings" xml:space="preserve">
    <value>Warning Name</value>
  </data>
  <data name="SetWarningText" xml:space="preserve">
    <value>Warning Message</value>
  </data>
  <data name="ApplyTo" xml:space="preserve">
    <value>Apply To</value>
  </data>
  <data name="ApplyToCatagory" xml:space="preserve">
    <value>Apply To Catagory</value>
  </data>
  <data name="ReceivePOStatus" xml:space="preserve">
    <value>Received PO Status</value>
  </data>
  <data name="ECCNWarning" xml:space="preserve">
    <value> Warning Message</value>
  </data>
  <data name="BySection" xml:space="preserve">
    <value>By &lt;/br&gt; Section</value>
  </data>
  <data name="ShowExchangeRate" xml:space="preserve">
    <value>Show Exchange Rate</value>
  </data>
  <data name="SABy" xml:space="preserve">
    <value>By (Date Time)</value>
  </data>
  <data name="IsCancel" xml:space="preserve">
    <value>IsCancel?</value>
  </data>
  <data name="IsClosed" xml:space="preserve">
    <value>IsClosed?</value>
  </data>
  <data name="ShortShipmentNo" xml:space="preserve">
    <value>Shipment Number</value>
  </data>
  <data name="ReleasePrice" xml:space="preserve">
    <value>Release Price</value>
  </data>
  <data name="SADevicePicture" xml:space="preserve">
    <value>Device Pictures Attachment</value>
  </data>
  <data name="SAManufaturerPicture" xml:space="preserve">
    <value>Manufacturer Lebel Picture Attachment</value>
  </data>
  <data name="SATracablityPicture" xml:space="preserve">
    <value>Traceablity Picture Attachment</value>
  </data>
  <data name="SAWebLnk" xml:space="preserve">
    <value>Franchise Weblink or Evidence</value>
  </data>
  <data name="SaDateOrdered" xml:space="preserve">
    <value>PO Date Ordered</value>
  </data>
  <data name="GINumber" xml:space="preserve">
    <value>GI Number</value>
  </data>
  <data name="SONumber" xml:space="preserve">
    <value>SO Number</value>
  </data>
  <data name="QueryDate" xml:space="preserve">
    <value>Query Date</value>
  </data>
  <data name="ToDoList" xml:space="preserve">
    <value>To Do List</value>
  </data>
  <data name="ProspectQualification" xml:space="preserve">
    <value>Prospects Qualification</value>
  </data>
  <data name="CreatedDateFrom" xml:space="preserve">
    <value>Created Date From</value>
  </data>
  <data name="CreatedDateTo" xml:space="preserve">
    <value>Created Date To</value>
  </data>
  <data name="TaskDateFrom" xml:space="preserve">
    <value>Task Date From</value>
  </data>
  <data name="TaskDateTo" xml:space="preserve">
    <value>Task Date To</value>
  </data>
  <data name="TaskStatus" xml:space="preserve">
    <value>Task Status</value>
  </data>
  <data name="TaskType" xml:space="preserve">
    <value>Task Type</value>
  </data>
  <data name="ToDoListType" xml:space="preserve">
    <value>To Do List Type</value>
  </data>
  <data name="TaskTitle" xml:space="preserve">
    <value>Task Title</value>
  </data>
  <data name="TaskReminderDate" xml:space="preserve">
    <value>Task Reminder Date</value>
  </data>
  <data name="GT" xml:space="preserve">
    <value>GT</value>
  </data>
  <data name="PrintDateCode" xml:space="preserve">
    <value>Printable DC</value>
  </data>
  <data name="GIGoodInNo" xml:space="preserve">
    <value>Goods In Number</value>
  </data>
  <data name="GIsupplierType" xml:space="preserve">
    <value>Supplier Type</value>
  </data>
  <data name="PIBy" xml:space="preserve">
    <value>Physical Inspected By</value>
  </data>
  <data name="Comment" xml:space="preserve">
    <value>Comments</value>
  </data>
  <data name="GlobalProductMainCategory" xml:space="preserve">
    <value>Product Category</value>
  </data>
  <data name="EI_BookingNo" xml:space="preserve">
    <value>Booking Number</value>
  </data>
  <data name="EI_AssignedTo" xml:space="preserve">
    <value>Assigned To</value>
  </data>
  <data name="GroupCode" xml:space="preserve">
    <value>Group Code</value>
  </data>
  <data name="GroupName" xml:space="preserve">
    <value>Group Name</value>
  </data>
  <data name="Clientnotify" xml:space="preserve">
    <value>Notify</value>
  </data>
  <data name="SecurityGroupDiscription" xml:space="preserve">
    <value>Group Description</value>
  </data>
  <data name="GIApprovers" xml:space="preserve">
    <value>Approvers</value>
  </data>
  <data name="GIStatus" xml:space="preserve">
    <value>Status</value>
  </data>
  <data name="LotCheckboxHeader" xml:space="preserve">
    <value>&lt;input type='checkbox' id='chkdtoffer1' name='select_offer'/&gt;</value>
  </data>
  <data name="BOMManagerCode" xml:space="preserve">
    <value>BOM Code</value>
  </data>
  <data name="BOMManagerName" xml:space="preserve">
    <value>BOM Name</value>
  </data>
  <data name="VirtualCostPrice" xml:space="preserve">
    <value>Virtual Cost Price</value>
  </data>
  <data name="HUBPartwatch" xml:space="preserve">
    <value>HUB PartWatch</value>
  </data>
  <data name="LastVisited" xml:space="preserve">
    <value>Last Visited</value>
  </data>
  <data name="ReportName" xml:space="preserve">
    <value>Report Name</value>
  </data>
  <data name="SourceClient" xml:space="preserve">
    <value>Source Client</value>
  </data>
  <data name="OGEL" xml:space="preserve">
    <value>OGEL</value>
  </data>
  <data name="EUUFormReq" xml:space="preserve">
    <value>EUU Form Required?</value>
  </data>
  <data name="OgelLicReq" xml:space="preserve">
    <value>OGEL License Required?</value>
  </data>
  <data name="CommodityCode" xml:space="preserve">
    <value>Commodity Code</value>
  </data>
  <data name="ECCNDescription" xml:space="preserve">
    <value>ECCN Description</value>
  </data>
  <data name="OGELNumber" xml:space="preserve">
    <value>OGEL Number</value>
  </data>
  <data name="OGEL_EndDestinationCountry" xml:space="preserve">
    <value>Destination Country</value>
  </data>
  <data name="OGEL_MilitaryUse" xml:space="preserve">
    <value>Military Use</value>
  </data>
  <data name="DateUploaded" xml:space="preserve">
    <value>Date Uploaded</value>
  </data>
  <data name="EUUFormRequired" xml:space="preserve">
    <value>EUU Form Required?</value>
  </data>
  <data name="Link" xml:space="preserve">
    <value />
  </data>
  <data name="Uploaded" xml:space="preserve">
    <value>Uploaded?</value>
  </data>
  <data name="UploadedBy" xml:space="preserve">
    <value>Uploaded By</value>
  </data>
  <data name="ClientPartWatchMatch" xml:space="preserve">
    <value>PartWatch Match</value>
  </data>
  <data name="BatchReference" xml:space="preserve">
    <value>Batch Reference</value>
  </data>
  <data name="SystemManufacturer" xml:space="preserve">
    <value>API Imported Data</value>
  </data>
  <data name="CustomerAPINo" xml:space="preserve">
    <value>Customer API No</value>
  </data>
  <data name="InActive" xml:space="preserve">
    <value>InActive</value>
  </data>
  <data name="Mobile" xml:space="preserve">
    <value>Mobile</value>
  </data>
  <data name="APIExternalLinks" xml:space="preserve">
    <value>Future Electronics</value>
  </data>
  <data name="ApiResponse" xml:space="preserve">
    <value>Source</value>
  </data>
  <data name="ApiSourceName" xml:space="preserve">
    <value>ApiSourceName</value>
  </data>
  <data name="ECCN" xml:space="preserve">
    <value>ECCN</value>
  </data>
  <data name="GTDateCreated" xml:space="preserve">
    <value>GT Date Created</value>
  </data>
  <data name="PackagingType" xml:space="preserve">
    <value>Packaging Type</value>
  </data>
  <data name="PublishDate" xml:space="preserve">
    <value>Publish Date</value>
  </data>
  <data name="UnitCostPrice" xml:space="preserve">
    <value>Unit Cost Price</value>
  </data>
  <data name="AS6081" xml:space="preserve">
    <value>AS6081 testing required?</value>
  </data>
  <data name="Required" xml:space="preserve">
    <value>Required</value>
  </data>
  <data name="AS6081Required" xml:space="preserve">
    <value>Inhouse AS6081 testing required</value>
  </data>
  <data name="AssignedTo" xml:space="preserve">
    <value>Assigned To</value>
  </data>
  <data name="AS6081_AssigneeName" xml:space="preserve">
    <value>Assigned To</value>
  </data>
  <data name="AS6081_DocumentNo" xml:space="preserve">
    <value>Document No</value>
  </data>
  <data name="AS6081_Level" xml:space="preserve">
    <value>Assignment Type</value>
  </data>
  <data name="AS6081_AssignedBy" xml:space="preserve">
    <value>Assigned By</value>
  </data>
  <data name="ClientUpLiftPrice" xml:space="preserve">
    <value>Uplift Price</value>
  </data>
  <data name="DocumentSizeByte" xml:space="preserve">
    <value>File Size In Byte</value>
  </data>
  <data name="DocumentSizeMB" xml:space="preserve">
    <value>File Size In MB</value>
  </data>
  <data name="DocumentType" xml:space="preserve">
    <value>Document Type</value>
  </data>
  <data name="ClientInvoiceHeader" xml:space="preserve">
    <value>Header Name</value>
  </data>
  <data name="SOR" xml:space="preserve">
    <value>Prevent SOR sign OFF</value>
  </data>
  <data name="Termswarning" xml:space="preserve">
    <value>Terms Warning</value>
  </data>
  <data name="InsuranceCeriticateName" xml:space="preserve">
    <value>Insurance Ceriticate Name</value>
  </data>
  <data name="InsuranceCeriticateNumber" xml:space="preserve">
    <value>Insurance Ceriticate Number</value>
  </data>
  <data name="CertificateCategory" xml:space="preserve">
    <value>Certificate Category</value>
  </data>
  <data name="CertificateNo" xml:space="preserve">
    <value>Certificate No</value>
  </data>
  <data name="AS6081Requirednew" xml:space="preserve">
    <value>AS6081 Required?</value>
  </data>
  <data name="Answer" xml:space="preserve">
    <value>Answer</value>
  </data>
  <data name="Question" xml:space="preserve">
    <value>Question</value>
  </data>
  <data name="SupplierInvoiceSelectAll" xml:space="preserve">
    <value>&lt;input type="checkbox" id="SelectALLSupplierInvoice"&gt;</value>
  </data>
  <data name="SupplierInvoiceAction" xml:space="preserve">
    <value>Action</value>
  </data>
  <data name="SupplierInvoiceDecision" xml:space="preserve">
    <value>Decision</value>
  </data>
  <data name="SupplierInvoiceItemCount" xml:space="preserve">
    <value>Item Count</value>
  </data>
  <data name="SupplierInvoiceSummary" xml:space="preserve">
    <value>Summary</value>
  </data>
  <data name="InspectionStatus" xml:space="preserve">
    <value>Inspection Status</value>
  </data>
  <data name="DebitNoteNumber" xml:space="preserve">
    <value>Debit Note Number</value>
  </data>
  <data name="IPONumber" xml:space="preserve">
    <value>IPO Number</value>
  </data>
  <data name="PurchaseOrderNumber" xml:space="preserve">
    <value>PO Number</value>
  </data>
  <data name="SupplierNotes" xml:space="preserve">
    <value>Supplier Notes</value>
  </data>
  <data name="SupplierRMANo" xml:space="preserve">
    <value>Supplier RMA No</value>
  </data>
  <data name="DebitAmount" xml:space="preserve">
    <value>Debit Amount</value>
  </data>
  <data name="ImportedBy" xml:space="preserve">
    <value>Imported By</value>
  </data>
  <data name="ImportStatus" xml:space="preserve">
    <value>Import Status</value>
  </data>
  <data name="ProspectiveOfferSource" xml:space="preserve">
    <value>Source</value>
  </data>
  <data name="RowCount" xml:space="preserve">
    <value>Row</value>
  </data>
  <data name="SellPrice" xml:space="preserve">
    <value>Sell Price</value>
  </data>
  <data name="SourcingType" xml:space="preserve">
    <value>Sourcing Type</value>
  </data>
  <data name="DateOffered2" xml:space="preserve">
    <value>Date Offered</value>
  </data>
  <data name="OgelId" xml:space="preserve">
    <value>OGEL ID</value>
  </data>
  <data name="OGEL_InActive" xml:space="preserve">
    <value>IsActive</value>
  </data>
  <data name="InactivateRestrictedMFR" xml:space="preserve">
    <value>Inactivate Restricted MFR</value>
  </data>
  <data name="MFRNameSuffix" xml:space="preserve">
    <value>MFR Name Suffix</value>
  </data>
  <data name="BatchNo" xml:space="preserve">
    <value>Batch No</value>
  </data>
  <data name="OldValue" xml:space="preserve">
    <value>Old Value</value>
  </data>
  <data name="AppliedDate" xml:space="preserve">
    <value>Applied Date</value>
  </data>
  <data name="CountedStar" xml:space="preserve">
    <value>Counted Star</value>
  </data>
  <data name="NumOfPO" xml:space="preserve">
    <value>Number of Purchase Order</value>
  </data>
  <data name="POLineCount" xml:space="preserve">
    <value>PO Line Count</value>
  </data>
  <data name="Franchise" xml:space="preserve">
    <value>Franchised?</value>
  </data>
  <data name="CreatedBy" xml:space="preserve">
    <value>Created By</value>
  </data>
  <data name="QuoteOfferedDate" xml:space="preserve">
    <value>Quote Offered Date</value>
  </data>
  <data name="TaskCategory" xml:space="preserve">
    <value>Task Category</value>
  </data>
  <data name="TaskReference" xml:space="preserve">
    <value>Reference</value>
  </data>
  <data name="QuoteNumber" xml:space="preserve">
    <value>Quote Number</value>
  </data>
</root>