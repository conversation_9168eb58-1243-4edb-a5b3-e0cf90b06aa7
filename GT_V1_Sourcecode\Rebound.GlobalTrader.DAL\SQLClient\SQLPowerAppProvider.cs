﻿/* Marker     changed by      date         Remarks  
   [001]      A<PERSON><PERSON><PERSON>  25-08-2021    Add for supplier PO approval.
   
*/
using System;
using System.Data;
using System.Data.SqlClient;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;
using System.Web.Caching;
using System.Data.Common;

namespace Rebound.GlobalTrader.DAL.SqlClient
{
    public class SqlPowerAppProvider : PowerAppProvider
    {
        /// <summary>
        /// Line Manager Approvals.
        /// Calls [usp_PowerApp_update_SO_Authorise]
        /// </summary>
        public override List<PowerAppDetails> SOCheckedUnchecked(System.Int32? SOId, System.String AuthorisedByEmail, System.Boolean? Authorise, System.String ApproverNote, System.Int32? RequestId, System.String TokenValue)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
          //  int RowAffected = 0;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_PowerApp_update_SO_Authorise", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@SalesOrderId", SqlDbType.Int).Value = SOId;
                cmd.Parameters.Add("@AuthorisedByEmail", SqlDbType.NVarChar, 1000).Value = AuthorisedByEmail;
                cmd.Parameters.Add("@Authorise", SqlDbType.Bit).Value = Authorise;
                cmd.Parameters.Add("@ApproverNote", SqlDbType.NVarChar, 1500).Value = ApproverNote;
                cmd.Parameters.Add("@RequestId", SqlDbType.Int).Value = RequestId;
                cmd.Parameters.Add("@TokenValue", SqlDbType.NVarChar, 1500).Value = TokenValue;
                cn.Open();
                SqlDataReader reader = cmd.ExecuteReader();
                List<PowerAppDetails> lst = new List<PowerAppDetails>();

                while (reader.Read())
                {
                    PowerAppDetails obj = new PowerAppDetails();
                    obj.SOId = GetReaderValue_Int32(reader, "SoId", 0);
                    obj.IsNotifySO= GetReaderValue_NullableBoolean(reader, "IsNotifySO", false);
                    obj.LoginId= GetReaderValue_Int32(reader, "LoginId", 0);
                    obj.LoginName = GetReaderValue_String(reader,"LoginName", "");
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to update line manager approval", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        /// <summary>
        /// calls [usp_select_PowerBIActivity]
        /// </summary>
        /// <param name="loginId"></param>
        /// <param name="clientId"></param>
        /// <returns></returns>
        public override List<PowerAppDetails> GetPowerBIActivity(System.Int32? loginId, System.Int32? clientId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_select_PowerBIActivity", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 90;
                cmd.Parameters.Add("@LoginId", SqlDbType.Int).Value = loginId;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<PowerAppDetails> lst = new List<PowerAppDetails>();
                while (reader.Read())
                {
                    PowerAppDetails obj = new PowerAppDetails();
                    obj.LoginName = GetReaderValue_String(reader, "LoginName", "");
                    obj.LastVisited = GetReaderValue_NullableDateTime(reader, "LastVisited", null);
                    obj.ReportName = GetReaderValue_String(reader, "ReportName", "");
                    
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Power BI report activity log.", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// calls [usp_select_PowerBIActivity]
        /// </summary>
        /// <param name="loginId"></param>
        /// <param name="clientId"></param>
        /// <returns></returns>
        public override string LastVisitedBI(int? LoginId,int? clientId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            string LastVisited = "";
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_select_UserLastVisited", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 90;
                cmd.Parameters.Add("@LoginId", SqlDbType.Int).Value = LoginId;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<PowerAppDetails> lst = new List<PowerAppDetails>();
                while (reader.Read())
                {
                    LastVisited = GetReaderValue_String(reader, "LastVisited", "");
                }
                return LastVisited;
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to get Power BI report activity log.", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// Export Approvals.
        /// Calls [usp_PowerAppExportApproveRejectById]
        /// </summary>
        public override List<PowerAppDetails> ApproveRejectExportApproval(System.Int32? ExportApprovalId, System.String AuthorisedByEmail, System.String ApproverNote, System.Int32? RequestId, System.String TokenValue, System.Int32? ApprovalOption,System.Int32? OgelNumber)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            //int RowAffected = 0;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_PowerAppExportApproveRejectById", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@ExportApprovalId", SqlDbType.Int).Value = ExportApprovalId;
                cmd.Parameters.Add("@OgelNumber", SqlDbType.NVarChar, 2000).Value = OgelNumber;
                cmd.Parameters.Add("@Comment", SqlDbType.NChar, 2000).Value = ApproverNote;
                cmd.Parameters.Add("@AuthorisedByEmail", SqlDbType.NVarChar,2000).Value = AuthorisedByEmail;
                cmd.Parameters.Add("@ActionOption", SqlDbType.Int).Value = ApprovalOption;
                cmd.Parameters.Add("@RequestId", SqlDbType.Int).Value = RequestId;
                cmd.Parameters.Add("@TokenValue", SqlDbType.NVarChar,2000).Value = TokenValue;
                cn.Open();
                SqlDataReader reader = cmd.ExecuteReader();
                List<PowerAppDetails> lst = new List<PowerAppDetails>();
                while (reader.Read())
                {
                    PowerAppDetails obj = new PowerAppDetails();
                    obj.SalesOrderNo = GetReaderValue_Int32(reader, "SalesOrderNo", 0);
                    obj.SalesOrderNumber = GetReaderValue_Int32(reader, "SalesOrderNumber", 0);
                    obj.Salesman = GetReaderValue_Int32(reader, "Salesman", 0);
                    obj.SalesmanName = GetReaderValue_String(reader, "SalesmanName", "");
                    obj.SOSerialNo = GetReaderValue_Int32(reader, "SOSerialNo", 0);
                    obj.ExportApprovalStatusId = GetReaderValue_Int32(reader, "ExportApprovalStatusId", 0);
                    obj.ExportApprovalStatus = GetReaderValue_String(reader, "ExportApprovalStatus", "");
                    obj.ExportApprovalId = GetReaderValue_Int32(reader, "ExportApprovalId", 0);
                    obj.Result = GetReaderValue_Boolean(reader, "IsApproved", false);
                    obj.IsNotifySO= GetReaderValue_Boolean(reader, "IsNotifySO", false);
                    obj.LoginId= GetReaderValue_Int32(reader, "LoginId", 0);
                    obj.LoginName= GetReaderValue_String(reader, "LoginName", "");

                    lst.Add(obj);
                    obj = null;
                }
                return lst;

            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to update quality approval", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// Notify changes of RL Stock.
        /// Calls [usp_PowerApp_notify_RL_Stock]
        /// </summary>
        public override PowerAppDetails GetFlowUrlByFlowName(System.String flowName)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_PowerApp_notify_RL_Stock", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@FlowName", SqlDbType.NVarChar).Value = flowName;
                cn.Open();
                SqlDataReader reader = cmd.ExecuteReader();

                if (reader.Read())
                {
                    PowerAppDetails obj = new PowerAppDetails();
                    obj.FlowUrl = GetReaderValue_String(reader, "FlowUrl", "");
                    return obj;
                }
                else { return null; }
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get FlowUrl by FlowName", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
    }
}
