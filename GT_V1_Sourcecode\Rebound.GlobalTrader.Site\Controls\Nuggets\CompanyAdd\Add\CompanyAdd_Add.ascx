<%--
********************************************************************************************

Marker     changed by      date         Remarks

[001]      Abhinav       02/09/20011   ESMS Ref:12 - Add new field "Company Registration No" 
[002]      Abhinav        02/17/2014   ESMS Ref:100 - Add new field to Compnay Form

**********************************************************************************************
--%>
<%@ Control Language="C#" CodeBehind="CompanyAdd_Add.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Forms.CompanyAdd_Add" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<%@ Register TagPrefix="cc1" Namespace="Rebound.GlobalTrader.Site" Assembly="Rebound.GlobalTrader.Site" %>
<ReboundUI_Form:DesignBase ID="ctlDB" runat="server" ShowRequiredFieldsExplanation="true" ShowQuickHelp="false">
	<Links>
		<ReboundUI:IconButton ID="ibtnSave" runat="server" IconGroup="Nugget" IconTitleResource="Save" IconCSSType="Save" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
		<ReboundUI:IconButton ID="ibtnCancel" runat="server" IconGroup="Nugget" IconTitleResource="Cancel" IconCSSType="Cancel" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
	</Links>

	<Explanation><%=Functions.GetGlobalResource("FormExplanations", "CompanyAdd_Add")%></Explanation>

	<Content>
		<ReboundUI_Table:Form id="frm" runat="server">
			<ReboundUI_Form:FormField id="ctlCompanyName" runat="server" FieldID="txtCompanyName" ResourceTitle="CompanyName" IsRequiredField="true">
				<Field><ReboundUI:ReboundTextBox ID="txtCompanyName" runat="server" Width="300" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlSalesman" runat="server" FieldID="ddlSalesman" ResourceTitle="Salesperson" IsRequiredField="true">
				<Field><ReboundDropDown:Employee ID="ddlSalesman" runat="server" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlFirstName" runat="server" FieldID="txtFirstName" ResourceTitle="ContactFirstName" IsRequiredField="true">
				<Field><ReboundUI:ReboundTextBox ID="txtFirstName" runat="server" Width="300" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlLastName" runat="server" FieldID="txtLastName" ResourceTitle="ContactLastName" IsRequiredField="true">
				<Field><ReboundUI:ReboundTextBox ID="txtLastName" runat="server" Width="300" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundFormFieldCollection:Address id="ctlAddress" runat="server" RequireFields="true" />

			<ReboundUI_Form:FormField id="ctlTelephone" runat="server" FieldID="txtTelephone" ResourceTitle="Telephone">
				<Field><ReboundUI:ReboundTextBox ID="txtTelephone" runat="server" Width="200" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlTelephone800" runat="server" FieldID="txtTelephone800" ResourceTitle="Telephone800">
				<Field><ReboundUI:ReboundTextBox ID="txtTelephone800" runat="server" Width="200" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlFax" runat="server" FieldID="txtFax" ResourceTitle="Fax">
				<Field><ReboundUI:ReboundTextBox ID="txtFax" runat="server" Width="200" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlEMail" runat="server" FieldID="txtEMail" ResourceTitle="EMail">
				<Field><ReboundUI:ReboundTextBox ID="txtEMail" runat="server" Width="400" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlURL" runat="server" FieldID="txtURL" ResourceTitle="URL" IsRequiredField="true">
				<Field><ReboundUI:ReboundTextBox ID="txtURL" runat="server" Width="400" /></Field>
			</ReboundUI_Form:FormField>
			<ReboundUI_Form:FormField id="ctlVATNumber" runat="server" FieldID="txtVATNumber" ResourceTitle="VATNumber">
				<Field><ReboundUI:ReboundTextBox ID="txtVATNumber" runat="server" Width="150" /></Field>
			</ReboundUI_Form:FormField>
        <%--// [001] code start--%>
	<ReboundUI_Form:FormField id="ctlCmpRegNO" runat="server" FieldID="txtCmpRegNo" ResourceTitle="CompanyRegNo">
				<Field><ReboundUI:ReboundTextBox ID="txtCmpRegNo" runat="server" Width="300"  /></Field>
			<%--// [001] code end--%>	
			</ReboundUI_Form:FormField>
			<ReboundUI_Form:FormField id="ctlNotes" runat="server" FieldID="txtNotes" ResourceTitle="Notes">
				<Field><ReboundUI:ReboundTextBox ID="txtNotes" runat="server" Width="450" TextMode="MultiLine" Rows="2" /></Field>
			</ReboundUI_Form:FormField>
		<%--// [002] code start--%>	
			<ReboundUI_Form:FormField id="ctlCertificateNotes" runat="server" FieldID="txtCertificateNotes" ResourceTitle="CertificationNotes">
				<Field><ReboundUI:ReboundTextBox ID="txtCertificateNotes" runat="server" Width="450" TextMode="MultiLine" Rows="2" /></Field>
			</ReboundUI_Form:FormField>
			<ReboundUI_Form:FormField id="ctlqualityNotes" runat="server" FieldID="txtqualityNotes" ResourceTitle="QualityNotes">
				<Field><ReboundUI:ReboundTextBox ID="txtqualityNotes" runat="server" Width="450" TextMode="MultiLine" Rows="2" /></Field>
			</ReboundUI_Form:FormField>
			<%--// [002] code end--%>

            <ReboundUI_Form:FormField id="ctlEORINumber" runat="server" FieldID="txtEORINumber" ResourceTitle="EORINumber" IsRequiredField="false">
				<Field><ReboundUI:ReboundTextBox ID="txtEORINumber" runat="server" Width="150" /></Field>
			</ReboundUI_Form:FormField>
		

		</ReboundUI_Table:Form>
	</Content>
</ReboundUI_Form:DesignBase>
