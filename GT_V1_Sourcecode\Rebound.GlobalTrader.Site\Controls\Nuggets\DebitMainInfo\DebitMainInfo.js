Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Nuggets");Rebound.GlobalTrader.Site.Controls.Nuggets.DebitMainInfo=function(n){Rebound.GlobalTrader.Site.Controls.Nuggets.DebitMainInfo.initializeBase(this,[n]);this._intDebitID=-1;this._IsPOHub=!1;this._isAutoGenerated=!1;this._blnExported=!1};Rebound.GlobalTrader.Site.Controls.Nuggets.DebitMainInfo.prototype={get_intDebitID:function(){return this._intDebitID},set_intDebitID:function(n){this._intDebitID!==n&&(this._intDebitID=n)},get_ibtnEdit:function(){return this._ibtnEdit},set_ibtnEdit:function(n){this._ibtnEdit!==n&&(this._ibtnEdit=n)},get_ibtnExport:function(){return this._ibtnExport},set_ibtnExport:function(n){this._ibtnExport!==n&&(this._ibtnExport=n)},get_ibtnRelease:function(){return this._ibtnRelease},set_ibtnRelease:function(n){this._ibtnRelease!==n&&(this._ibtnRelease=n)},get_blnGlobalLogin:function(){return this._blnGlobalLogin},set_blnGlobalLogin:function(n){this._blnGlobalLogin!==n&&(this._blnGlobalLogin=n)},get_ibtnViewTree:function(){return this._ibtnViewTree},set_ibtnViewTree:function(n){this._ibtnViewTree!==n&&(this._ibtnViewTree=n)},get_IsDiffrentClient:function(){return this._IsDiffrentClient},set_IsDiffrentClient:function(n){this._IsDiffrentClient!==n&&(this._IsDiffrentClient=n)},get_IsGSAEditPermission:function(){return this._IsGSAEditPermission},set_IsGSAEditPermission:function(n){this._IsGSAEditPermission!==n&&(this._IsGSAEditPermission=n)},get_IsGSA:function(){return this._IsGSA},set_IsGSA:function(n){this._IsGSA!==n&&(this._IsGSA=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Nuggets.DebitMainInfo.callBaseMethod(this,"initialize");this.addRefreshEvent(Function.createDelegate(this,this.getData));this._IsDiffrentClient==!0?this._IsGSA==!0?this._IsGSAEditPermission==!0?$("#ctl00_cphMain_ctlLines_ctlDB_pnlLinks").show():$("#ctl00_cphMain_ctlLines_ctlDB_pnlLinks").hide():$("#ctl00_cphMain_ctlLines_ctlDB_pnlLinks").show():$("#ctl00_cphMain_ctlLines_ctlDB_pnlLinks").show();this._ibtnEdit&&($R_IBTN.addClick(this._ibtnEdit,Function.createDelegate(this,this.showEditForm)),this._frmEdit=$find(this._aryFormIDs[0]),this._frmEdit._intDebitID=this._intDebitID,this._frmEdit.addCancel(Function.createDelegate(this,this.cancelEdit)),this._frmEdit.addSaveComplete(Function.createDelegate(this,this.saveEditComplete)));(this._ibtnExport||this._ibtnRelease)&&(this._ibtnExport&&$R_IBTN.addClick(this._ibtnExport,Function.createDelegate(this,this.showExportForm)),this._ibtnRelease&&$R_IBTN.addClick(this._ibtnRelease,Function.createDelegate(this,this.showReleaseForm)),this._frmDebitExport=$find(this._aryFormIDs[1]),this._frmDebitExport.addCancel(Function.createDelegate(this,this.hideExportForm)),this._frmDebitExport.addSaveComplete(Function.createDelegate(this,this.saveExportComplete)),this._frmDebitExport.addNotConfirmed(Function.createDelegate(this,this.hideExportForm)));this._ibtnViewTree&&$R_IBTN.addClick(this._ibtnViewTree,Function.createDelegate(this,this.OpenDocTree));this._blnIsNoDataFound||this._blnHasInitialData||this.getData()},dispose:function(){this.isDisposed||(this._ibtnEdit&&$R_IBTN.clearHandlers(this._ibtnEdit),this._frmEdit&&this._frmEdit.dispose(),this._frmEdit=null,this._intDebitID=null,this._blnGlobalLogin=null,this._ibtnExport=null,this._ibtnRelease=null,this._IsDiffrentClient=null,this._IsGSAEditPermission=null,this._IsGSA=null,Rebound.GlobalTrader.Site.Controls.Nuggets.DebitMainInfo.callBaseMethod(this,"dispose"))},getData:function(){this.getData_Start();var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/DebitMainInfo");n.set_DataObject("DebitMainInfo");n.set_DataAction("GetData");n.addParameter("id",this._intDebitID);n.addDataOK(Function.createDelegate(this,this.getDataOK));n.addError(Function.createDelegate(this,this.getDataError));n.addTimeout(Function.createDelegate(this,this.getDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getDataOK:function(n){var t=n._result;this.setFieldValue("ctlSupplier",$R_FN.showSupplierMessage($RGT_nubButton_Company(t.SupplierNo,t.SupplierName,null,null,null,t.SupplierAdvisoryNotes),t.SuppMessage));this.setFieldValue("ctlContact",t.Contact,$RGT_nubButton_Contact(t.ContactNo,t.Contact));this.setFieldValue("ctlBuyer",$R_FN.setCleanTextValue(t.Buyer));this.setFieldValue("ctlRaiser",$R_FN.setCleanTextValue(t.Raiser));this.setFieldValue("ctlDivision",$R_FN.setCleanTextValue(t.Division));this.setFieldValue("ctlDebitDate",t.DebitDate);this.setFieldValue("ctlReferenceDate",t.ReferenceDate);this.setFieldValue("ctlPurchaseOrder",$RGT_nubButton_PurchaseOrder(t.PurchaseOrderNo,t.PurchaseOrder));this.setFieldValue("ctlSupplierRMA",$RGT_nubButton_SRMA(t.SupplierRMANo,t.SupplierRMA));this.setFieldValue("ctlTax",$R_FN.setCleanTextValue(t.Tax));this.setFieldValue("ctlFreight",t.Freight);this.setFieldValue("ctlCurrency",$R_FN.setCleanTextValue(t.Currency));this.setFieldValue("ctlSupplierInvoice",$R_FN.setCleanTextValue(t.SupplierInvoice));this.setFieldValue("ctlSupplierCredit",$R_FN.setCleanTextValue(t.SupplierCredit));this.setFieldValue("ctlSupplierReturn",$R_FN.setCleanTextValue(t.SupplierReturn));this.setFieldValue("ctlNotes",$R_FN.setCleanTextValue(t.Notes));this.setFieldValue("ctlInstructions",$R_FN.setCleanTextValue(t.Instructions));t.InternalPurchaseOrderNo>0?this.setFieldValue("ctlInternalPurchaseOrder",$RGT_nubButton_InternalPurchaseOrder(t.InternalPurchaseOrderNo,t.InternalPurchaseOrderNumber)):this.showField("ctlInternalPurchaseOrder",!1);this.setFieldValue("hidDebitNumber",t.DebitNumber);this.setFieldValue("hidCurrencyNo",t.CurrencyNo);this.setFieldValue("hidCurrencyCode",t.CurrencyCode);this.setFieldValue("hidFreight",t.FreightVal);this.setFieldValue("hidPO",t.PurchaseOrder);this.setFieldValue("hidPONo",t.PurchaseOrderNo);this.setFieldValue("hidSupplierName",$R_FN.setCleanTextValue(t.SupplierName));this.setFieldValue("hidContactName",$R_FN.setCleanTextValue(t.Contact));this.setFieldValue("hidRaisedByNo",$R_FN.setCleanTextValue(t.RaisedBy));this.setFieldValue("hidBuyerNo",$R_FN.setCleanTextValue(t.BuyerNo));this.setFieldValue("hidSRMA",$R_FN.setCleanTextValue(t.SupplierRMA));this.setFieldValue("hidSRMANo",$R_FN.setCleanTextValue(t.SupplierRMANo));this.setFieldValue("hidTaxNo",$R_FN.setCleanTextValue(t.TaxNo));this.setFieldValue("hidDivisionNo",t.DivisionNo);this.setFieldValue("ctlIncotermName",$R_FN.setCleanTextValue(t.Incoterm));this.setFieldValue("hidIncotermNo",t.IncotermNo);this.setFieldValue("ctlRefNo",t.RefNumber);this.showField("ctlRefNo",t.RefNumber>0);this.setFieldValue("ctlLockUpdateClient",t.ishublocked);this.setFieldValue("ctlExported",t.isExport);this.showField("ctlLockUpdateClient",t.isAutoGenerated);this.showField("ctlExportedDate",t.isExport);this.setFieldValue("ctlExportedDate",t.DateExported);this._isAutoGenerated=t.isAutoGenerated;this._IsPOHub=t.IsPoHub;this.setFieldValue("hidGlobalClientNo",t.DebClientNo);this.setFieldValue("ctlURNnumber",t.URNNumber);this.setFieldValue("ctlApprovedforExport",t.CanBeExported);this._blnExported=t.isExport;this.enableEditButtons(!0);this.setDLUP(t.DLUP);this.getDataOK_End()},getDataError:function(n){this.showError(!0,n.get_ErrorMessage())},enableEditButtons:function(n){n&&(this._ibtnEdit&&$R_IBTN.enableButton(this._ibtnEdit,!this._blnExported),this._ibtnExport&&$R_IBTN.enableButton(this._ibtnExport,!this._blnExported),this._ibtnRelease&&$R_IBTN.enableButton(this._ibtnRelease,this._blnExported))},showExportForm:function(){this.doShowExportForm("EXPORT")},showReleaseForm:function(){this.doShowExportForm("RELEASE")},doShowExportForm:function(n){this._frmDebitExport.changeMode(n);this._frmDebitExport._intDebitID=this._intDebitID;this._frmDebitExport.setFieldValue("ctlNotes",this.getFieldValue("hidDebitNumber"));this._frmDebitExport.setFieldValue("ctlSupplier",this.getFieldValue("hidSupplierName"));this.showForm(this._frmDebitExport,!0)},hideExportForm:function(){this.showForm(this._frmDebitExport,!1)},saveExportComplete:function(){this.showForm(this._frmDebitExport,!1);this.showContentLoading(!1);this.getData();this.showSavedOK(!0,$R_RES.ChangesSavedSuccessfully)},showEditForm:function(){this._frmEdit._intLineID=this._intLineID;this._frmEdit.setFieldValue("ctlSupplier",this.getFieldValue("hidSupplierName"));this._frmEdit.setFieldValue("ctlContact",this.getFieldValue("hidContactName"));this._frmEdit.setFieldValue("ctlDivision",this.getFieldValue("hidDivisionNo"));this._frmEdit.setFieldValue("ctlBuyer",this.getFieldValue("hidBuyerNo"));this._frmEdit.setFieldValue("ctlRaisedBy",this.getFieldValue("hidRaisedByNo"));this._frmEdit.setFieldValue("ctlDebitDate",this.getFieldValue("ctlDebitDate"));this._frmEdit.setFieldValue("ctlReferenceDate",this.getFieldValue("ctlReferenceDate"));this._frmEdit.setFieldValue("ctlSupplierInvoice",this.getFieldValue("ctlSupplierInvoice"));this._frmEdit.setFieldValue("ctlSupplierReturn",this.getFieldValue("ctlSupplierReturn"));this._frmEdit.setFieldValue("ctlSupplierCredit",this.getFieldValue("ctlSupplierCredit"));this._frmEdit.setFieldValue("ctlPurchaseOrder",this.getFieldValue("hidPO"));this._frmEdit.setFieldValue("ctlSupplierRMA",this.getFieldValue("hidSRMA"));this._frmEdit.setFieldValue("ctlTax",this.getFieldValue("hidTaxNo"));this._frmEdit.setFieldValue("ctlCurrency",this.getFieldValue("hidCurrencyNo"));this._frmEdit.setFieldValue("ctlFreight",this.getFieldValue("hidFreight"));this._frmEdit.setFieldValue("ctlNotes",this.getFieldValue("ctlNotes"));this._frmEdit.setFieldValue("ctlInstructions",this.getFieldValue("ctlInstructions"));this._frmEdit.setCurrency(this.getFieldValue("hidCurrencyCode"));this._frmEdit.setFieldValue("ctlIncoterm",this.getFieldValue("hidIncotermNo"));this._frmEdit.showField("ctlURNNumber",this.getFieldValue("ctlURNnumber"));this._frmEdit.setFieldValue("ctlURNNumber",this.getFieldValue("ctlURNnumber"));this._frmEdit.showField("ctlRaisedByLbl",!this._IsPOHub);this._frmEdit.showField("ctlRaisedBy",this._IsPOHub);this._frmEdit._IsPOHub=this._IsPOHub;this._frmEdit._hidRaisedByNo=this.getFieldValue("hidRaisedByNo");this._frmEdit.setFieldValue("ctlRaisedByLbl",this.getFieldValue("ctlRaiser"));this._frmEdit.setFieldValue("ctlLockUpdateClient",this.getFieldValue("ctlLockUpdateClient"));this._frmEdit.showField("ctlLockUpdateClient",this._isAutoGenerated);this._frmEdit._intGlobalClientNo=this._blnGlobalLogin==!0?this.getFieldValue("hidGlobalClientNo"):null;this.showForm(this._frmEdit,!0)},hideEditForm:function(){this.showForm(this._frmEdit,!1)},cancelEdit:function(){this.hideEditForm()},saveEditComplete:function(){this.hideEditForm();this.showSavedOK(!0,$R_RES.ChangesSavedSuccessfully);this.onSaveEditComplete();this.getData()},OpenDocTree:function(){$R_FN.openDocumentTree(this._intDebitID,"DBT",this.getFieldValue("hidDebitNumber"))}};Rebound.GlobalTrader.Site.Controls.Nuggets.DebitMainInfo.registerClass("Rebound.GlobalTrader.Site.Controls.Nuggets.DebitMainInfo",Rebound.GlobalTrader.Site.Controls.Nuggets.Base);