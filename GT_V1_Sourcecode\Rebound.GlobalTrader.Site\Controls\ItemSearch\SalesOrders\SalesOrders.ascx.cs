using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site.Controls;

namespace Rebound.GlobalTrader.Site.Controls.ItemSearch {
	public partial class SalesOrders : Base {

		protected override void OnInit(EventArgs e) {
			base.OnInit(e);
			SetItemSearchType("SalesOrders");
			AddScriptReference("Controls.ItemSearch.SalesOrders.SalesOrders.js");
		}

		protected override void OnLoad(EventArgs e) {
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.ItemSearch.SalesOrders", ctlDesignBase.ClientID);
			base.OnLoad(e);
		}

		protected override void OnPreRender(EventArgs e) {
			ctlDesignBase.MakeChildControls();
			ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("SalesOrder", WidthManager.GetWidth(WidthManager.ColumnWidth.SystemDocumentNumber), true));
			ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("Partno", Unit.Empty, true));
			ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("Company", Unit.Empty, true));
			ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("Contact", WidthManager.GetWidth(WidthManager.ColumnWidth.ContactName), true));
			ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("DateOrdered", WidthManager.GetWidth(WidthManager.ColumnWidth.Date), true));
			ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("Salesman", WidthManager.GetWidth(WidthManager.ColumnWidth.LoginName), true));
			ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("CustomerPO", WidthManager.GetWidth(WidthManager.ColumnWidth.ExternalCompanyDocument), true));
			base.OnPreRender(e);
		}
	}
}