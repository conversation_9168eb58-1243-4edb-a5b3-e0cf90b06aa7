Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.CreditAdd_Add=function(n){Rebound.GlobalTrader.Site.Controls.Forms.CreditAdd_Add.initializeBase(this,[n]);this._intNewID=0;this._intCompanyID=0;this._intLoginID=0;this._intContactID=0;this._intDivisionID=0;this._intInvoiceID=0;this._intCRMAID=0;this._intSalesmanID=0;this._intCurrencyID=0;this._intTaxID=0;this._strCompanyName="";this._strReferenceDate="";this._strSearchCompanyName="";this._intClientInvoiceID=0;this._intClientInvoiceLineNo=0;this._isClientInvoice=!1;this._intDivisionHeaderNo=0};Rebound.GlobalTrader.Site.Controls.Forms.CreditAdd_Add.prototype={get_intCompanyID:function(){return this._intCompanyID},set_intCompanyID:function(n){this._intCompanyID!==n&&(this._intCompanyID=n)},get_strCompanyName:function(){return this._strCompanyName},set_strCompanyName:function(n){this._strCompanyName!==n&&(this._strCompanyName=n)},get_intLoginID:function(){return this._intLoginID},set_intLoginID:function(n){this._intLoginID!==n&&(this._intLoginID=n)},get_intContactID:function(){return this._intContactID},set_intContactID:function(n){this._intContactID!==n&&(this._intContactID=n)},get_strContactName:function(){return this._strContactName},set_strContactName:function(n){this._strContactName!==n&&(this._strContactName=n)},get_ibtnSend:function(){return this._ibtnSend},set_ibtnSend:function(n){this._ibtnSend!==n&&(this._ibtnSend=n)},get_ibtnSend_Footer:function(){return this._ibtnSend_Footer},set_ibtnSend_Footer:function(n){this._ibtnSend_Footer!==n&&(this._ibtnSend_Footer=n)},get_ibtnContinue:function(){return this._ibtnContinue},set_ibtnContinue:function(n){this._ibtnContinue!==n&&(this._ibtnContinue=n)},get_ibtnContinue_Footer:function(){return this._ibtnContinue_Footer},set_ibtnContinue_Footer:function(n){this._ibtnContinue_Footer!==n&&(this._ibtnContinue_Footer=n)},get_radSelectSource:function(){return this._radSelectSource},set_radSelectSource:function(n){this._radSelectSource!==n&&(this._radSelectSource=n)},get_ctlSelectFromInvoice:function(){return this._ctlSelectFromInvoice},set_ctlSelectFromInvoice:function(n){this._ctlSelectFromInvoice!==n&&(this._ctlSelectFromInvoice=n)},get_trSelectFromInvoice:function(){return this._trSelectFromInvoice},set_trSelectFromInvoice:function(n){this._trSelectFromInvoice!==n&&(this._trSelectFromInvoice=n)},get_ctlSelectFromCRMA:function(){return this._ctlSelectFromCRMA},set_ctlSelectFromCRMA:function(n){this._ctlSelectFromCRMA!==n&&(this._ctlSelectFromCRMA=n)},get_trSelectFromCRMA:function(){return this._trSelectFromCRMA},set_trSelectFromCRMA:function(n){this._trSelectFromCRMA!==n&&(this._trSelectFromCRMA=n)},get_lblCurrency_Freight:function(){return this._lblCurrency_Freight},set_lblCurrency_Freight:function(n){this._lblCurrency_Freight!==n&&(this._lblCurrency_Freight=n)},get_arySources:function(){return this._arySources},set_arySources:function(n){this._arySources!==n&&(this._arySources=n)},get_strSearchCompanyName:function(){return this._strSearchCompanyName},set_strSearchCompanyName:function(n){this._strSearchCompanyName!==n&&(this._strSearchCompanyName=n)},get_intQSInvoiceID:function(){return this._intQSInvoiceID},set_intQSInvoiceID:function(n){this._intQSInvoiceID!==n&&(this._intQSInvoiceID=n)},get_ctlSelectFromClientInvoice:function(){return this._ctlSelectFromClientInvoice},set_ctlSelectFromClientInvoice:function(n){this._ctlSelectFromClientInvoice!==n&&(this._ctlSelectFromClientInvoice=n)},get_trSelectFromClientInvoice:function(){return this._trSelectFromClientInvoice},set_trSelectFromClientInvoice:function(n){this._trSelectFromClientInvoice!==n&&(this._trSelectFromClientInvoice=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Forms.CreditAdd_Add.callBaseMethod(this,"initialize");this.addShown(Function.createDelegate(this,this.formShown))},dispose:function(){this.isDisposed||(this._ibtnContinue&&$R_IBTN.clearHandlers(this._ibtnContinue),this._ibtnContinue_Footer&&$R_IBTN.clearHandlers(this._ibtnContinue_Footer),this._ibtnSend&&$R_IBTN.clearHandlers(this._ibtnSend),this._ibtnSend_Footer&&$R_IBTN.clearHandlers(this._ibtnSend_Footer),this._ctlSelectFromInvoice&&this._ctlSelectFromInvoice.dispose(),this._ctlSelectFromCRMA&&this._ctlSelectFromCRMA.dispose(),this._ctlMultiStep&&this._ctlMultiStep.dispose(),this._ctlMail&&this._ctlMail.dispose(),this._ctlSelectFromClientInvoice&&this._ctlSelectFromClientInvoice.dispose(),this._intCompanyID=null,this._strCompanyName=null,this._intLoginID=null,this._intContactID=null,this._strContactName=null,this._ibtnSend=null,this._ibtnSend_Footer=null,this._ibtnContinue=null,this._ibtnContinue_Footer=null,this._radSelectSource=null,this._ctlSelectFromInvoice=null,this._trSelectFromInvoice=null,this._ctlSelectFromCRMA=null,this._trSelectFromCRMA=null,this._lblCurrency_Freight=null,this._arySources=null,this._intNewID=null,this._intCompanyID=null,this._intLoginID=null,this._intContactID=null,this._intDivisionID=null,this._intDivisionHeaderNo=null,this._intInvoiceID=null,this._intCRMAID=null,this._intSalesmanID=null,this._intCurrencyID=null,this._intTaxID=null,this._strCompanyName=null,this._strReferenceDate=null,this._strSearchCompanyName=null,this._ctlMail=null,this._intQSInvoiceID=null,this._isClientInvoice=null,this._ctlSelectFromClientInvoice=null,this._trSelectFromClientInvoice=null,this._intClientInvoiceLineNo=null,Rebound.GlobalTrader.Site.Controls.Forms.CreditAdd_Add.callBaseMethod(this,"dispose"))},formShown:function(){var n,t;this._blnFirstTimeShown&&($find(this.getField("ctlShipVia").ControlID).addChanged(Function.createDelegate(this,this.getShipVia)),this._ctlMail=$find(this.getField("ctlSendMailMessage").ID),this._ctlMail._ctlRelatedForm=this,this.addCancel(Function.createDelegate(this,this.cancelClicked)),this.addSave(Function.createDelegate(this,this.saveClicked)),this._ctlMultiStep.addStepChanged(Function.createDelegate(this,this.stepChanged)),this._ctlSelectFromInvoice.addItemSelected(Function.createDelegate(this,this.selectInvoice)),this._ctlSelectFromClientInvoice.addItemSelected(Function.createDelegate(this,this.selectClientInvoice)),this._ctlSelectFromCRMA.addItemSelected(Function.createDelegate(this,this.selectCRMA)),this.addFieldCheckBoxClickEvent("ctlSendMail",Function.createDelegate(this,this.chooseIfSendMail)),n=Function.createDelegate(this,this.continueClicked),$R_IBTN.addClick(this._ibtnContinue,n),$R_IBTN.addClick(this._ibtnContinue_Footer,n),t=Function.createDelegate(this,this.sendMail),$R_IBTN.addClick(this._ibtnSend,t),$R_IBTN.addClick(this._ibtnSend_Footer,t),$find(this.getField("ctlSalesman2").ControlID).addChanged(Function.createDelegate(this,this.changedSalesman2)),this._ctlSelectFromInvoice._ShipExported=1);this._strSearchCompanyName&&(this._ctlSelectFromInvoice.setFieldValue("ctlCompany",this._strSearchCompanyName),this._ctlSelectFromCRMA.setFieldValue("ctlCompany",this._strSearchCompanyName));this.resetSteps();this._intQSInvoiceID!=null&&this._intQSInvoiceID>0?this.getInvoiceFromInvoiceDetail():this.gotoStep(1)},continueClicked:function(){switch(this._ctlMultiStep._intCurrentStep){case 1:this._strSourceSelected=this.findWhichTypeSelected();this.gotoStep(2);break;case 4:this.finishedForm()}},findWhichTypeSelected:function(){for(var t,n=0;n<this._arySources.length;n++)if(t=$get(String.format("{0}_{1}",this._radSelectSource.id,n)),t.checked)return this._arySources[n]},setSourceSelectRadio:function(){for(var t,n=0;n<2;n++){t=$get(String.format("{0}_{1}",this._radSelectSource.id,n));switch(n){case 0:t.checked=this._strSourceSelected=="INVOICE";break;case 1:t.checked=this._strSourceSelected=="CRMA";break;case 2:t.checked=this._strSourceSelected=="CLIENTINVOICE"}}},cancelClicked:function(){$R_FN.navigateBack()},stepChanged:function(){var n=this._ctlMultiStep._intCurrentStep,t;$R_IBTN.showButton(this._ibtnSend,n==4);$R_IBTN.showButton(this._ibtnSend_Footer,n==4);$R_IBTN.enableButton(this._ibtnSave,n==3);$R_IBTN.enableButton(this._ibtnSave_Footer,n==3);$R_IBTN.showButton(this._ibtnSave,n!=4);$R_IBTN.showButton(this._ibtnSave_Footer,n!=4);$R_IBTN.showButton(this._ibtnCancel,n!=4);$R_IBTN.showButton(this._ibtnCancel_Footer,n!=4);this._ctlMultiStep.showSteps(n!=4);this._ctlMultiStep.showExplainLabel(n!=2);t=n==1;$R_IBTN.showButton(this._ibtnContinue,t);$R_IBTN.showButton(this._ibtnContinue_Footer,t);$R_FN.showElement(this._trSelectFromInvoice,n==2&&this._strSourceSelected=="INVOICE");$R_FN.showElement(this._trSelectFromCRMA,n==2&&this._strSourceSelected=="CRMA");$R_FN.showElement(this._trSelectFromClientInvoice,n==2&&this._strSourceSelected=="CLIENTINVOICE");n==2&&(this.getFieldDropDownData("ctlIncoterm"),this.getFieldDropDownData("ctlCreditNoteBankFee"),this._strSourceSelected=="INVOICE"&&this._ctlSelectFromInvoice.resizeColumns(),this._strSourceSelected=="CRMA"&&this._ctlSelectFromCRMA.resizeColumns(),this._strSourceSelected=="CLIENTINVOICE"&&this._ctlSelectFromClientInvoice.resizeColumns(),this._intCompanyID>0&&(this._strSourceSelected=="INVOICE"?(this._strCompanyName&&this._ctlSelectFromInvoice.setFieldValue("ctlCompany",$R_FN.setCleanTextValue(this._strCompanyName)),this._strContactName&&this._ctlSelectFromInvoice.setFieldValue("ctlContact",$R_FN.setCleanTextValue(this._strContactName)),this._ctlSelectFromInvoice.getData()):this._strSourceSelected=="CLIENTINVOICE"?(this._strContactName&&this._ctlSelectFromClientInvoice.setFieldValue("ctlContact",$R_FN.setCleanTextValue(this._strContactName)),this._isClientInvoice=!0,this._ctlSelectFromClientInvoice.getData()):(this._strCompanyName&&this._ctlSelectFromCRMA.setFieldValue("ctlCompany",$R_FN.setCleanTextValue(this._strCompanyName)),this._strContactName&&this._ctlSelectFromCRMA.setFieldValue("ctlContact",$R_FN.setCleanTextValue(this._strContactName)),this._ctlSelectFromCRMA.getData())),this.showFormField("ctlAuthorisedBy",this._strSourceSelected=="CRMA"),this.showFormField("ctlCRMANumber",this._strSourceSelected=="CRMA"));n==4&&(this.getMessageText(),this.setFieldValue("ctlSendMail",!1),this.showMailButtons())},selectClientInvoice:function(){this._intInvoiceID=this._ctlSelectFromClientInvoice.getSelectedID();this._intClientInvoiceLineNo=this._ctlSelectFromClientInvoice._tblResults.getSelectedExtraData().ClientInvLineNo;this.getClientInvoice();this.gotoStep(3)},getClientInvoice:function(){this._isClientInvoice=!0;this.showClientInvoiceFieldsLoading(!0);$R_FN.showElement(this._pnlLines,!1);var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/ClientInvoiceMainInfo");n.set_DataObject("ClientInvoiceMainInfo");n.set_DataAction("GetDataItem");n.addParameter("ID",this._intInvoiceID);n.addParameter("LineNo",this._intClientInvoiceLineNo);n.addDataOK(Function.createDelegate(this,this.getClientInvoiceOK));n.addError(Function.createDelegate(this,this.getClientInvoiceError));n.addTimeout(Function.createDelegate(this,this.getClientInvoiceError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getClientInvoiceOK:function(n){var t=n._result,i;t&&(this.checkCreateCreditNote(t.Exported,t.ClientInvoiceNumber),this.setFieldValue("ctlCompany",$R_FN.setCleanTextValue(t.SupplierName)),this.setFieldValue("ctlClientInvoiceNumber",t.ClientInvoiceNumber),this.setFieldValue("ctlContact",t.Contact),i=$R_FN.setCleanTextValue(t.Salesman),t.Salesman2&&(i+=", "+$R_FN.setCleanTextValue(t.Salesman2)),this.setFieldValue("ctlSalesman",$R_FN.setCleanTextValue(t.Salesman)),this.setFieldValue("ctlSalesman2",t.Salesman2No),this.setFieldValue("ctlSalesman2Percent",t.Salesman2Percent),this.setFieldValue("ctlCurrency",$R_FN.setCleanTextValue(t.CurrencyCode)),this.setFieldValue("ctlTax",$R_FN.setCleanTextValue(t.TaxName)),this.setFieldValue("ctlDivision",$R_FN.setCleanTextValue(t.DivisionName)),this.setFieldValue("ctlDivisionHeader",$R_FN.setCleanTextValue(t.DivisionHeaderName)),this.setFieldValue("ctlShippingCost",t.ShippingCostVal),this.setFieldValue("ctlFreight",t.FreightVal),this.setFieldValue("ctlCreditDate",$R_FN.shortDate()),this.setFieldValue("ctlReferenceDate",t.ClientInvoiceDate),this.setFieldValue("ctlRaisedBy",this._intLoginID),this.setFieldValue("ctlShipVia",t.ShipViaNo),this.setFieldValue("ctlAccount",t.ShippingAccountNo),this.setFieldValue("ctlIncoterm",t.IncotermNo),this.setFieldValue("ctlCreditNoteBankFee",t.CreditNoteBankFee),this.getFieldDropDownData("ctlSalesman2"),this.getFieldDropDownData("ctlShipVia"),this.getFieldDropDownData("ctlRaisedBy"),this._intCompanyID=t.CustomerNo,this._intContactID=t.ContactNo,this._intDivisionID=t.DivisionNo,this._intTaxID=t.TaxNo,this._intSalesmanID=t.SalesmanNo,this._intCurrencyID=t.CurrencyNo,this._strReferenceDate=t.InvoiceDateRaw,this._intDivisionHeaderNo=t.DivisionHeaderNo,$R_FN.setInnerHTML(this._lblCurrency_Freight,t.CurrencyCode),this.showClientInvoiceFieldsLoading(!1))},getClientInvoiceError:function(n){this.showClientInvoiceFieldsLoading(!1);this._strErrorMessage=n._errorMessage;this.onSaveError()},showClientInvoiceFieldsLoading:function(n){this.showField("ctlContact",!1);this.showField("ctlSalesman2",!1);this.showField("ctlSalesman2Percent",!1);this.showField("ctlShippingCost",!1);this.showField("ctlShipVia",!1);this.showField("ctlAccount",!1);this.showField("ctlClientInvoiceNumber",!0);this.showField("ctlInvoiceNumber",!1);this.showFieldLoading("ctlCompany",n);this.showFieldLoading("ctlInvoiceNumber",n);this.showFieldLoading("ctlContact",n);this.showFieldLoading("ctlSalesman",n);this.showFieldLoading("ctlSalesman2",n);this.showFieldLoading("ctlCurrency",n);this.showFieldLoading("ctlTax",n);this.showFieldLoading("ctlDivision",n);this.showFieldLoading("ctlAccount",n);this.showFieldLoading("ctlShippingCost",n);this.showFieldLoading("ctlFreight",n);this.showFieldLoading("ctlRaisedBy",n);this.showFieldLoading("ctlShipVia",n);this.showFieldLoading("ctlCustomerPO",n);this.showFieldLoading("ctlAccount",n)},selectInvoice:function(){this._intInvoiceID=this._ctlSelectFromInvoice.getSelectedID();this.getInvoice();this.gotoStep(3)},getInvoice:function(){this.showInvoiceFieldsLoading(!0);$R_FN.showElement(this._pnlLines,!1);var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/InvoiceMainInfo");n.set_DataObject("InvoiceMainInfo");n.set_DataAction("GetData");n.addParameter("ID",this._intInvoiceID);n.addDataOK(Function.createDelegate(this,this.getInvoiceOK));n.addError(Function.createDelegate(this,this.getInvoiceError));n.addTimeout(Function.createDelegate(this,this.getInvoiceError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getInvoiceOK:function(n){var t=n._result,i;t&&(this.checkCreateCreditNote(t.Exported,t.InvoiceNumber),this.setFieldValue("ctlCompany",$R_FN.setCleanTextValue(t.Customer)),this.setFieldValue("ctlInvoiceNumber",t.InvoiceNumber),this.setFieldValue("ctlContact",t.Contact),i=$R_FN.setCleanTextValue(t.Salesman),t.Salesman2&&(i+=", "+$R_FN.setCleanTextValue(t.Salesman2)),this.setFieldValue("ctlSalesman",$R_FN.setCleanTextValue(t.Salesman)),this.setFieldValue("ctlSalesman2",t.Salesman2No),this.setFieldValue("ctlSalesman2Percent",t.Salesman2Percent),this.setFieldValue("ctlCurrency",$R_FN.setCleanTextValue(t.Currency)),this.setFieldValue("ctlTax",$R_FN.setCleanTextValue(t.TaxName)),this.setFieldValue("ctlDivision",$R_FN.setCleanTextValue(t.DivisionName)),this.setFieldValue("ctlDivisionHeader",$R_FN.setCleanTextValue(t.DivisionHeaderName)),this.setFieldValue("ctlShippingCost",t.ShippingCostVal),this.setFieldValue("ctlFreight",t.FreightVal),this.setFieldValue("ctlCreditDate",$R_FN.shortDate()),this.setFieldValue("ctlReferenceDate",t.InvoiceDate),this.setFieldValue("ctlRaisedBy",this._intLoginID),this.setFieldValue("ctlShipVia",t.ShipViaNo),this.setFieldValue("ctlCustomerPO",t.CustomerPO),this.setFieldValue("ctlAccount",t.ShippingAccountNo),this.setFieldValue("ctlIncoterm",t.IncotermNo),this.setFieldValue("ctlCreditNoteBankFee",t.CreditNoteBankFee),this.getFieldDropDownData("ctlSalesman2"),this.getFieldDropDownData("ctlShipVia"),this.getFieldDropDownData("ctlRaisedBy"),this._intCompanyID=t.CustomerNo,this._intContactID=t.ContactNo,this._intDivisionID=t.DivisionNo,this._intTaxID=t.TaxNo,this._intSalesmanID=t.SalesmanNo,this._intCurrencyID=t.CurrencyNo,this._strReferenceDate=t.InvoiceDateRaw,this._intDivisionHeaderNo=t.DivisionHeaderNo,$R_FN.setInnerHTML(this._lblCurrency_Freight,t.CurrencyCode),this.showInvoiceFieldsLoading(!1))},getInvoiceError:function(n){this.showInvoiceFieldsLoading(!1);this._strErrorMessage=n._errorMessage;this.onSaveError()},showInvoiceFieldsLoading:function(n){this.showFieldLoading("ctlCompany",n);this.showFieldLoading("ctlInvoiceNumber",n);this.showFieldLoading("ctlContact",n);this.showFieldLoading("ctlSalesman",n);this.showFieldLoading("ctlSalesman2",n);this.showFieldLoading("ctlCurrency",n);this.showFieldLoading("ctlTax",n);this.showFieldLoading("ctlDivision",n);this.showFieldLoading("ctlAccount",n);this.showFieldLoading("ctlShippingCost",n);this.showFieldLoading("ctlFreight",n);this.showFieldLoading("ctlRaisedBy",n);this.showFieldLoading("ctlShipVia",n);this.showFieldLoading("ctlCustomerPO",n);this.showFieldLoading("ctlAccount",n);this.showField("ctlClientInvoiceNumber",!1);this.showField("ctlInvoiceNumber",!0)},selectCRMA:function(){this._intCRMAID=this._ctlSelectFromCRMA.getSelectedID();this.getCRMA();this.gotoStep(3)},getCRMA:function(){this.showCRMAFieldsLoading(!0);$R_FN.showElement(this._pnlLines,!1);var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/CreditAdd");n.set_DataObject("CreditAdd");n.set_DataAction("GetCRMA");n.addParameter("ID",this._intCRMAID);n.addDataOK(Function.createDelegate(this,this.getCRMAOK));n.addError(Function.createDelegate(this,this.getCRMAError));n.addTimeout(Function.createDelegate(this,this.getCRMAError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getCRMAOK:function(n){var t=n._result;this._intCompanyID=t.CustomerNo;this._intContactID=t.ContactNo;this._intDivisionID=t.DivisionNo;this._intDivisionHeaderNo=t.DivisionHeaderNo;this._intTaxID=t.TaxNo;this._intSalesmanID=t.SalesmanNo;this._intCurrencyID=t.CurrencyNo;this._intInvoiceID=t.InvoiceNo;this._strReferenceDate=t.InvoiceDateRaw;this.setFieldValue("ctlCompany",$R_FN.setCleanTextValue(t.Customer));this.setFieldValue("ctlInvoiceNumber",t.Invoice);this.setFieldValue("ctlCRMANumber",t.CRMANumber);this.setFieldValue("ctlContact",$R_FN.setCleanTextValue(t.Contact));this.setFieldValue("ctlSalesman",$R_FN.setCleanTextValue(t.Salesman));this.setFieldValue("ctlSalesman2",t.Salesman2);this.setFieldValue("ctlSalesman2Percent","");t.Salesman2Percent>0&&this.setFieldValue("ctlSalesman2Percent",t.Salesman2Percent);this.setFieldValue("ctlAuthorisedBy",$R_FN.setCleanTextValue(t.Authoriser));this.setFieldValue("ctlCurrency",$R_FN.setCleanTextValue(t.Currency));this.setFieldValue("ctlTax",$R_FN.setCleanTextValue(t.Tax));this.setFieldValue("ctlDivision",$R_FN.setCleanTextValue(t.Division));this.setFieldValue("ctlDivisionHeader",$R_FN.setCleanTextValue(t.DivisionHeaderName));this.setFieldValue("ctlAccount",$R_FN.setCleanTextValue(t.ShippingAccountNo));this.setFieldValue("ctlCreditDate",$R_FN.shortDate());this.setFieldValue("ctlReferenceDate",t.InvoiceDate);this.setFieldValue("ctlRaisedBy",this._intLoginID);this.setFieldValue("ctlCustomerPO",$R_FN.setCleanTextValue(t.CustomerPO));this.setFieldValue("ctlShippingCost",t.ShippingCostVal);this.setFieldValue("ctlFreight",t.FreightVal);this.setFieldValue("ctlShipVia",t.ShipViaNo);this.setFieldValue("ctlIncoterm",t.IncotermNo);this.setFieldValue("ctlCreditNoteBankFee",t.CreditNoteBankFee);this.showCRMAFieldsLoading(!1);this.getFieldDropDownData("ctlShipVia");this.getFieldDropDownData("ctlRaisedBy");this.getFieldDropDownData("ctlSalesman2");$R_FN.setInnerHTML(this._lblCurrency_Freight,t.CurrencyCode)},getCRMAError:function(n){this.showCRMAFieldsLoading(!1);this._strErrorMessage=n._errorMessage;this.onSaveError()},showCRMAFieldsLoading:function(n){this.showFieldLoading("ctlCompany",n);this.showFieldLoading("ctlInvoiceNumber",n);this.showFieldLoading("ctlCRMANumber",n);this.showFieldLoading("ctlContact",n);this.showFieldLoading("ctlSalesman",n);this.showFieldLoading("ctlSalesman2",n);this.showFieldLoading("ctlAuthorisedBy",n);this.showFieldLoading("ctlCurrency",n);this.showFieldLoading("ctlTax",n);this.showFieldLoading("ctlDivision",n);this.showFieldLoading("ctlAccount",n);this.showFieldLoading("ctlShippingCost",n);this.showFieldLoading("ctlFreight",n);this.showFieldLoading("ctlReferenceDate",n);this.showFieldLoading("ctlRaisedBy",n);this.showFieldLoading("ctlShipVia",n);this.showFieldLoading("ctlCustomerPO",n);this.showField("ctlClientInvoiceNumber",!1);this.showField("ctlInvoiceNumber",!0)},saveClicked:function(){if(this.validateForm()){this.showSaving(!0);var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/CreditAdd");n.set_DataObject("CreditAdd");n.set_DataAction("AddNew");n.addParameter("RaisedBy",this.getFieldValue("ctlRaisedBy"));n.addParameter("ShipViaNo",this.getFieldValue("ctlShipVia"));n.addParameter("Notes",this.getFieldValue("ctlNotes"));n.addParameter("Instructions",this.getFieldValue("ctlInstructions"));n.addParameter("Account",this.getFieldValue("ctlAccount"));n.addParameter("ReferenceDate",this._strReferenceDate);n.addParameter("CreditDate",this.getFieldValue("ctlCreditDate"));n.addParameter("ShippingCost",this.getFieldValue("ctlShippingCost"));n.addParameter("Freight",this.getFieldValue("ctlFreight"));n.addParameter("TaxNo",this._intTaxID);n.addParameter("InvoiceNo",this._intInvoiceID);n.addParameter("SalesOrderNo",this._intSalesOrderID);n.addParameter("CustomerRMANo",this._intCRMAID);n.addParameter("Salesman",this._intSalesmanID);n.addParameter("CurrencyNo",this._intCurrencyID);n.addParameter("CMNo",this._intCompanyID);n.addParameter("ContactNo",this._intContactID);n.addParameter("DivisionNo",this._intDivisionID);n.addParameter("CustomerPO",this.getFieldValue("ctlCustomerPO"));n.addParameter("CustomerReturn",this.getFieldValue("ctlCustomerReturn"));n.addParameter("CustomerDebit",this.getFieldValue("ctlCustomerDebit"));n.addParameter("Salesman2No",this.getFieldValue("ctlSalesman2"));n.addParameter("Salesman2Percent",this.getFieldValue("ctlSalesman2Percent"));n.addParameter("Incoterm",this.getFieldValue("ctlIncoterm"));n.addParameter("CreditNoteBankFee",this.getFieldValue("ctlCreditNoteBankFee"));n.addParameter("isClientInvoice",this._isClientInvoice);n.addParameter("ClientInvLineNo",this._intClientInvoiceLineNo);n.addParameter("DivisionHeaderNo",this._intDivisionHeaderNo);n.addDataOK(Function.createDelegate(this,this.saveEditComplete));n.addError(Function.createDelegate(this,this.saveEditError));n.addTimeout(Function.createDelegate(this,this.saveEditError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null}},saveEditError:function(n){this._strErrorMessage=n._errorMessage;this.onSaveError()},saveEditComplete:function(n){n._result.NewID>0?(this._intNewID=n._result.NewID,this.showSaving(!1),this.showInnerContent(!0),this.nextStep()):(this._strErrorMessage=n._errorMessage,this.onSaveError())},validateForm:function(){this.onValidate();var n=!0;return this._ctlMultiStep._intCurrentStep==3&&(this.checkFieldEntered("ctlRaisedBy")||(n=!1),this.checkFieldEntered("ctlCreditDate")||(n=!1),this.checkFieldEntered("ctlIncoterm")||(n=!1)),this._ctlMultiStep._intCurrentStep==4&&(this._ctlMail.validateFields()||(n=!1)),n||this.showError(!0),n},getShipVia:function(){if(this.showShipViaFieldsLoading(!0),!this.checkFieldEntered("ctlShipVia")){this.getShipViaError();return}var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/CreditAdd");n.set_DataObject("CreditAdd");n.set_DataAction("GetShipVia");n.addParameter("ID",this.getFieldValue("ctlShipVia"));n.addParameter("CreditCurrencyNo",this._intCurrencyID);n.addParameter("CreditDate",this.getFieldValue("ctlReferenceDate"));n.addDataOK(Function.createDelegate(this,this.getShipViaOK));n.addError(Function.createDelegate(this,this.getShipViaError));n.addTimeout(Function.createDelegate(this,this.getShipViaError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getShipViaOK:function(n){var t=n._result;this.setFieldValue("ctlShippingCost",t.Cost);this.setFieldValue("ctlFreight",t.Charge);this.showShipViaFieldsLoading(!1)},getShipViaError:function(){this.setFieldValue("ctlShippingCost",$R_FN.formatCurrency(0,"",2));this.setFieldValue("ctlFreight",$R_FN.formatCurrency(0,"",2));this.showShipViaFieldsLoading(!1)},showShipViaFieldsLoading:function(n){this.showFieldLoading("ctlShippingCost",n);this.showFieldLoading("ctlFreight",n)},changedSalesman2:function(){$find(this.getField("ctlSalesman2").ControlID).isSetAsNoValue()?this.setFieldValue("ctlSalesman2Percent",""):this.getFieldValue("ctlSalesman2Percent")==0&&this.setFieldValue("ctlSalesman2Percent",50)},showMailButtons:function(){var n=this.getFieldValue("ctlSendMail");this.showField("ctlSendMailMessage",n);$R_IBTN.showButton(this._ibtnSend,n);$R_IBTN.showButton(this._ibtnSend_Footer,n);$R_IBTN.showButton(this._ibtnContinue,!n);$R_IBTN.showButton(this._ibtnContinue_Footer,!n)},chooseIfSendMail:function(){this.showMailButtons()},getMessageText:function(){Rebound.GlobalTrader.Site.WebServices.GetMailMessage_NewCreditNote(this._intNewID,Function.createDelegate(this,this.getMessageTextComplete))},getMessageTextComplete:function(n){this._ctlMail.setValue_Body(n);this._ctlMail.setValue_Subject($R_RES.NewCreditNoteAdded)},validateMailForm:function(){var n=this._ctlMail.validateFields();return n||this.showError(!0),n},sendMail:function(){this.validateMailForm()&&(Rebound.GlobalTrader.Site.WebServices.SendMessage($R_FN.arrayToSingleString(this._ctlMail._aryRecipientLoginIDs),$R_FN.arrayToSingleString(this._ctlMail._aryRecipientGroupIDs),this._ctlMail.getValue_Subject(),this._ctlMail.getValue_Body(),this._intNewID,Function.createDelegate(this,this.sendMailComplete)),$R_IBTN.showButton(this._ibtnSave,!1),$R_IBTN.showButton(this._ibtnSave_Footer,!1),$R_IBTN.showButton(this._ibtnSend,!1),$R_IBTN.showButton(this._ibtnSend_Footer,!1))},sendMailComplete:function(){this.finishedForm()},finishedForm:function(){this._ctlMultiStep.showExplainLabel(!1);this._ctlMultiStep.showSteps(!1);$R_IBTN.showButton(this._ibtnSave,!1);$R_IBTN.showButton(this._ibtnSave_Footer,!1);$R_IBTN.showButton(this._ibtnSend,!1);$R_IBTN.showButton(this._ibtnSend_Footer,!1);this.showSavedOK(!0);this.onSaveComplete()},getInvoiceFromInvoiceDetail:function(){this._intInvoiceID=this._intQSInvoiceID;this.getInvoice()},checkCreateCreditNote:function(n,t){this.getFieldDropDownData("ctlIncoterm");this._intQSInvoiceID!=null&&this._intQSInvoiceID>0&&(n?this.gotoStep(3):(this.gotoStep(2),$R_FN.showElement(this._trSelectFromInvoice,!0),$R_FN.showElement(this._trSelectFromCRMA,!1),this._ctlSelectFromInvoice.setFieldValue("ctlInvoiceNo",t),this._ctlSelectFromInvoice.getData()))}};Rebound.GlobalTrader.Site.Controls.Forms.CreditAdd_Add.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.CreditAdd_Add",Rebound.GlobalTrader.Site.Controls.Forms.Base,Sys.IDisposable);