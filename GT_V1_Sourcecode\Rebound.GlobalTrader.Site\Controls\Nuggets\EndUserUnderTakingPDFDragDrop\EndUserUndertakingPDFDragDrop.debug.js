﻿///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
//Espire
//
//Marker     Changed by      Date         Remarks
//[001]      Vinay           07/05/2012   This need to upload pdf document for different section
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Nuggets");
Rebound.GlobalTrader.Site.Controls.Nuggets.EndUserUndertakingPDFDragDrop = function (element) {
    Rebound.GlobalTrader.Site.Controls.Nuggets.EndUserUndertakingPDFDragDrop.initializeBase(this, [element]);
    this._sectionID = 0;
    this._intCountPDF == 0;

};
Rebound.GlobalTrader.Site.Controls.Nuggets.EndUserUndertakingPDFDragDrop.prototype = {
    get_sectionID: function() { return this._sectionID; }, set_sectionID: function(value) { if (this._sectionID !== value) this._sectionID = value; },
    get_pnlPDFDocuments: function() { return this._pnlPDFDocuments; }, set_pnlPDFDocuments: function(value) { if (this._pnlPDFDocuments !== value) this._pnlPDFDocuments = value; },
    //    get_ibtnAdd: function() { return this._ibtnAdd; }, set_ibtnAdd: function(value) { if (this._ibtnAdd !== value) this._ibtnAdd = value; },
    get_blnCanDelete: function() { return this._blnCanDelete; }, set_blnCanDelete: function(value) { if (this._blnCanDelete !== value) this._blnCanDelete = value; },
    get_intMaxPDFDocuments: function() { return this._intMaxPDFDocuments; }, set_intMaxPDFDocuments: function(value) { if (this._intMaxPDFDocuments !== value) this._intMaxPDFDocuments = value; },
    get_blnCanAdd: function() { return this._blnCanAdd; }, set_blnCanAdd: function(value) { if (this._blnCanAdd !== value) this._blnCanAdd = value; },
    get_IsPDFAvailable: function() { return this._IsPDFAvailable; }, set_IsPDFAvailable: function(value) { if (this._IsPDFAvailable !== value) this._IsPDFAvailable = value; },
    get_strSectionName: function() { return this._strSectionName; }, set_strSectionName: function(value) { if (this._strSectionName !== value) this._strSectionName = value; },


    initialize: function () {
        Rebound.GlobalTrader.Site.Controls.Nuggets.EndUserUndertakingPDFDragDrop.callBaseMethod(this, "initialize");
        this.addRefreshEvent(Function.createDelegate(this, this.showPDFPanel));
        //        if (this._ibtnAdd) {

        //            $R_IBTN.addClick(this._ibtnAdd, Function.createDelegate(this, this.showAddForm));
        this._frmAdd = $find(this._aryFormIDs[0]);
        this._frmAdd._strSectionName = this._strSectionName;
        this._frmAdd._intSectionID = this._sectionID;
        this._frmAdd.addCancel(Function.createDelegate(this, this.hideAddForm));
        this._frmAdd.addSaveComplete(Function.createDelegate(this, this.saveAddComplete));
        this._frmAdd.addSaveError(Function.createDelegate(this, this.saveAddError));
        this._intMaxPDFDocuments = 1;
        //        }

        if (this._blnCanDelete) {
            this._frmDelete = $find(this._aryFormIDs[1]);
            this._frmDelete._strSectionName = this._strSectionName;
            this._frmDelete.addNotConfirmed(Function.createDelegate(this, this.cancelDelete));
            this._frmDelete.addSaveComplete(Function.createDelegate(this, this.saveDeleteComplete));
        }
        this.getData();
    },
    dispose: function() {
        if (this.isDisposed) return;
        //        if (this._ibtnAdd) $R_IBTN.clearHandlers(this._ibtnAdd);
        if (this._frmAdd) this._frmAdd.dispose();
        if (this._frmDelete) this._frmDelete.dispose();
        this._frmAdd = null;
        this._frmDelete = null;
        //this._ibtnAdd = null;
        this._pnlPDFDocuments = null;
        this._sectionID = null;
        this._blnCanAdd = null;
        this._IsPDFAvailable = null;
        this._intMaxPDFDocuments = 1;
        this._blnCanDelete = null;
        this._intCountPDF = null;
        this._strSectionName = null;
        Rebound.GlobalTrader.Site.Controls.Nuggets.EndUserUndertakingPDFDragDrop.callBaseMethod(this, "dispose");
    },
    getData: function() {

        //if (!this._IsPDFAvailable) { this.pdfNotAvailable(true); return; }
        this.getData_Start();
        this._intCountPDF == 0;
        // this.enableButtons(false);
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/EndUserUndertakingPDFDragDrop");
        obj.set_DataObject("EndUserUndertakingPDFDragDrop");
        obj.set_DataAction("GetData");
        obj.addParameter("id", this._sectionID);
        obj.addParameter("section", this._strSectionName);
        obj.addDataOK(Function.createDelegate(this, this.getDataOK));
        obj.addError(Function.createDelegate(this, this.getDataError));
        obj.addTimeout(Function.createDelegate(this, this.getDataError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },
    getMaxPDFDocument: function() {
        this.getData_Start();
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/EndUserUndertakingPDFDragDrop");
        obj.set_DataObject("EndUserUndertakingPDFDragDrop");
        obj.set_DataAction("MaxPDFDoc");
        obj.addDataOK(Function.createDelegate(this, this.getMaxPDFDocumentOK));
        obj.addError(Function.createDelegate(this, this.getDataError));
        obj.addTimeout(Function.createDelegate(this, this.getDataError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },
    getMaxPDFDocumentOK: function(args) {
        var result = args._result;
        if (result.MaxPDFDocument > 0)
            this._intMaxPDFDocuments = result.MaxPDFDocument;
    },
    getDataOK: function(args) {
        var res = args._result;
        var result = args._result;
        $R_FN.setInnerHTML(this._pnlPDFDocuments, "");
        var iconpath = result.IconPath;
        var strPDF = "";
        if (result.Items) {
            for (var i = 0; i < result.Items.length; i++) {
                var row = result.Items[i];
                strPDF += "<div class=\"pdfDocument\">";
                strPDF += String.format("<div class=\"pdfDocumentDelete\" onclick=\"$find('{0}').deletePDF({1},'{2}');\">&nbsp;</div>", this._element.id, row.ID, row.FileName);
                strPDF += String.format("<a href=\"{0}\" ><img width=\"80px\" id=\"{1}_img{2}\" src=\"{3}\" border=\"0\" onclick=\"$find('{4}').OpenPDF('{5}','{6}');\" /></a>", "javascript:void(0);", this._element.id, i, iconpath, this._element.id, row.Section, row.FileName);
                strPDF += "<div class=\"pdfDocumentCaption\">";
                if (row.Caption) strPDF += row.Caption + "<br />";
                strPDF += row.Date;
                if (row.By) strPDF += "<br />" + row.By;
                strPDF += "</div>";
                strPDF += "</div>";
                row = null;
            }
            this._intCountPDF = result.Items.length;
        }
        $R_FN.setInnerHTML(this._pnlPDFDocuments, strPDF);
        this.getDataOK_End();

        // this.enableButtons(this._blnCanAdd);
        //alert(this._intCountPDF);
        if(this._intCountPDF == 0)
            this.showPDFNoData(this._intCountPDF == 0);
        else
            this.showNoData(this._intCountPDF == 0);
        this.showPanel(true)
        this._blnCanDelete = result.Items[0].CanDeleteDocument;
        //if (this._intCountPDF < this._intMaxPDFDocuments) {
        //    $('[name="EUUFileUpload"]').removeAttr('disabled');
        //}
        //else {
        //    $('[name="EUUFileUpload"]').attr('disabled', 'disabled');
        //}
    },
    getDataError: function(args) {
        this.showError(true, args.get_ErrorMessage());
    },
    getDataCount: function() {
        this.getData_Start();
        this._intCountPDF == 0;
        // this.enableButtons(false);
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/EndUserUndertakingPDFDragDrop");
        obj.set_DataObject("EndUserUndertakingPDFDragDrop");
        obj.set_DataAction("GetData");
        obj.addParameter("id", this._sectionID);
        obj.addParameter("section", this._strSectionName);
        obj.addDataOK(Function.createDelegate(this, this.getDataCountOK));
        obj.addError(Function.createDelegate(this, this.getDataError));
        obj.addTimeout(Function.createDelegate(this, this.getDataError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },
    getDataCountOK: function(args) {
        var res = args._result;
        var result = args._result;
        $R_FN.setInnerHTML(this._pnlPDFDocuments, "");
        var strPDF = "";
        if (result.Items) {
            this._intCountPDF = result.Items.length;
            strPDF += "<div style=\"height:50px;\"></div>";
        }
        $R_FN.setInnerHTML(this._pnlPDFDocuments, strPDF);
        this.getDataOK_End();
        //this.showNoData(this._intCountPDF == 0);
        //this.enableButtons(this._blnCanAdd);
        //this.showPanel(this._intCountPDF > 0)

    },
    enableButtons: function(bln) {
        //  if (bln) {
        //  if (this._ibtnAdd) $R_IBTN.enableButton(this._ibtnAdd, ((this._intCountPDF == 0) || (this._intCountPDF < this._intMaxPDFDocuments)));
        //            
        //  } else {
        //  if (this._ibtnAdd) $R_IBTN.enableButton(this._ibtnAdd, false);
        //       
        //  }
    },
    deletePDF: function (intPDF, FileName) {
        if (this._blnCanDelete) {
            this._frmDelete._intPDFDocumentID = intPDF;
            this._frmDelete._pdfFileName = FileName;
            this.showDeleteForm();
        }
        else {
            alert("Kindly Uncheck SO to delete this EUU Form");
        }
    },
    showAddForm: function () {
        this.showForm(this._frmAdd, true);
    },
    hideAddForm: function() {
        // if (this._frmAdd._dragobj) this._frmAdd._dragobj.form.remove();
        $(".ajax-file-upload-red").each(function(i, items) {
            $(this).click();
        });

        this.showForm(this._frmAdd, false);
        // this._frmAdd._dragobj.stopUpload();
        this.showNoData(this._intCountPDF == 0);
    },
    saveAddComplete: function() {
        this.hideAddForm();
        this.showSavedOK(true, $R_RES.ChangesSavedSuccessfully);
        this._IsPDFAvailable = true;
        this.getData();
        setTimeout(function () {
            window.opener.$("#ctl00_cphMain_ctlExportApprovalStatus_ctlDB_imgRefresh").trigger('click');
            window.opener.$("#ctl00_cphMain_SOEndUserUndertakingForm_ctlDB_imgRefresh").trigger('click');
        }, 2000);
        
    },

    saveAddError: function() {
        this.showError(true, this._frmAdd._strErrorMessage);
    },

    showDeleteForm: function() {
        this.showForm(this._frmDelete, true);
    },

    hideDeleteForm: function() {
        this.showForm(this._frmDelete, false);
        this.showNoData(this._intCountPDF == 0);
    },

    cancelDelete: function() {
        this.hideDeleteForm();
    },

    saveDeleteComplete: function() {
        this.hideDeleteForm();
        this.showSavedOK(true, $R_RES.ChangesSavedSuccessfully);
        this.getData();
        $('.ajax-file-upload-statusbar').remove();
        setTimeout(function () {
            window.opener.$("#ctl00_cphMain_ctlExportApprovalStatus_ctlDB_imgRefresh").trigger('click');
            window.opener.$("#ctl00_cphMain_SOEndUserUndertakingForm_ctlDB_imgRefresh").trigger('click');
            window.close();
        }, 2000);
    },

    showPDFPanel: function() {
        this._IsPDFAvailable = true;
        this.showPanel(true);
        this.getMaxPDFDocument();
        this.getData();
    },

    showPanel: function(bln) {
        $R_FN.showElement(this._pnlPDFDocuments, bln);
    },

    pdfNotAvailable: function(bln) {
        if (bln) {
            $R_FN.setInnerHTML(this._pnlPDFDocuments, "");
            this.getDataOK_End();
            this._intCountPDF = 0;
            this.showNoData(true);
            //this.enableButtons(this._blnCanAdd);
        }
    },
    showAddFormFromDrag: function (strFile, obj) {
        
        if (this._intCountPDF < this._intMaxPDFDocuments) {
            
            this._frmAdd._strFileName = strFile;
            this._frmAdd._dragobj = obj;
            this._frmAdd._strDBFileName = "";
            this._frmAdd.setFieldValue("ctlFile", strFile);
        }
        this.showForm(this._frmAdd, true);
    },
   OpenPDF: function (strsection, FileName) {
       var obj = new Rebound.GlobalTrader.Site.Data();
       obj.set_PathToData("controls/Nuggets/EndUserUndertakingPDFDragDrop");
       obj.set_DataObject("EndUserUndertakingPDFDragDrop");
       obj.set_DataAction("GetPDFAccessURL");
       obj.addParameter("section", strsection);
       obj.addParameter("filename", FileName);
       obj.addDataOK(Function.createDelegate(this, this.getOpenPDFOK));
       obj.addError(Function.createDelegate(this, this.getOpenPDFError));
       obj.addTimeout(Function.createDelegate(this, this.getOpenPDFError));
       $R_DQ.addToQueue(obj);
       $R_DQ.processQueue();
       obj = null;
    },
    getOpenPDFOK: function (args) {
        var res = args._result;
        var result = args._result;
        // alert(result.bothirl);
        window.open(this.setCleanTextBlobURL(result.bothirl), '_blank');

    },
    getOpenPDFError: function (args) {
        this.showError(true, args.get_ErrorMessage());
    },
    setCleanTextBlobURL: function (strIn, blnReplaceLineBreaks) {
        if (typeof (strIn) == "undefined") strIn = "";
        strIn = (strIn + "").trim();
        strIn = strIn.replace(/(:PLUS:)/g, "+");
        strIn = strIn.replace(/(:AND:)/g, "&");
        strIn = strIn.replace(/[+]/g, "%2B");

        return strIn;
    },
    test: function () {

    }

};
Rebound.GlobalTrader.Site.Controls.Nuggets.EndUserUndertakingPDFDragDrop.registerClass("Rebound.GlobalTrader.Site.Controls.Nuggets.EndUserUndertakingPDFDragDrop", Rebound.GlobalTrader.Site.Controls.Nuggets.Base);
