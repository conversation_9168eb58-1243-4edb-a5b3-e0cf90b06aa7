﻿using System;
using System.Data;
using System.Data.SqlClient;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;
using System.Web.Caching;
using System.Data.Common;
using System.Globalization;

namespace Rebound.GlobalTrader.DAL.SqlClient
{
    public class SQLBOMManagerProvider : BOMManagerProvider
    {
        /// <summary>
        /// DataListNugget 
		/// Calls [usp_datalistnugget_BOM]
        /// </summary>
        /// <summary>
        /// DataListNugget 
		/// Calls [usp_datalistnugget_BOM]
        /// </summary>
        public override List<BOMManager> DataListNuggetOld(System.Int32? clientId, System.Int32? teamId, System.Int32? divisionId, System.Int32? loginId, System.Int32? orderBy, System.Int32? sortDir, System.Int32? pageIndex, System.Int32? pageSize, System.String bomManagerCode, System.String bomManagerName, System.Boolean? isPOHub, System.Int32? selectedClientNo, int? bomManagerStatus, int? IsAssignToMe, int? assignedUser, System.Int32? intDivision, System.Int32? salesPerson, System.DateTime? startdate, System.DateTime? enddate, string CompanyName, System.Int32? MailGroupId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                if (isPOHub == true)
                {
                    if (IsAssignToMe == 0)
                    {
                        cmd = new SqlCommand("usp_datalistnugget_PHBOMManager", cn);
                    }
                    if (IsAssignToMe == 1)
                    {
                        pageSize = 1000;//Set the max page size dynamically.
                        cmd = new SqlCommand("usp_datalistnugget_PHBOMManagerAssign", cn);
                    }
                }

                else
                {
                    cmd = new SqlCommand("usp_datalistnugget_BOMManager", cn);
                }
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 120;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@TeamId", SqlDbType.Int).Value = teamId;
                cmd.Parameters.Add("@DivisionId", SqlDbType.Int).Value = divisionId;
                cmd.Parameters.Add("@LoginId", SqlDbType.Int).Value = loginId;
                cmd.Parameters.Add("@OrderBy", SqlDbType.Int).Value = orderBy;
                cmd.Parameters.Add("@SortDir", SqlDbType.Int).Value = sortDir;
                cmd.Parameters.Add("@PageIndex", SqlDbType.Int).Value = pageIndex;
                cmd.Parameters.Add("@PageSize", SqlDbType.Int).Value = pageSize;
                cmd.Parameters.Add("@BOMManagerCode", SqlDbType.NVarChar).Value = bomManagerCode;
                //cmd.Parameters.Add("@CodeHi", SqlDbType.NVarChar).Value = CodeHi;
                cmd.Parameters.Add("@BOMManagerName", SqlDbType.NVarChar).Value = bomManagerName;
                cmd.Parameters.Add("@IsPoHub", SqlDbType.Bit).Value = isPOHub;
                cmd.Parameters.Add("@ClientNo", SqlDbType.Int).Value = selectedClientNo;
                cmd.Parameters.Add("@BomManagerStatus", SqlDbType.Int).Value = bomManagerStatus;
                cmd.Parameters.Add("@SalesPerson", SqlDbType.NVarChar).Value = salesPerson;//[003]
                cmd.Parameters.Add("@StartDate", SqlDbType.DateTime).Value = startdate;
                cmd.Parameters.Add("@EndDate", SqlDbType.DateTime).Value = enddate;//[003]
                cmd.Parameters.Add("@CompanyNo", SqlDbType.VarChar).Value = CompanyName;

                if (assignedUser > 0)
                {
                    cmd.Parameters.Add("@AssignedUser", SqlDbType.Int).Value = assignedUser;
                }
                cmd.Parameters.Add("@ClientDivisionNo", SqlDbType.Int).Value = intDivision;
                cmd.Parameters.Add("@MailGroupId", SqlDbType.Int).Value = MailGroupId;
                cn.Open();


                DbDataReader reader = ExecuteReader(cmd);
                List<BOMManager> lst = new List<BOMManager>();
                while (reader.Read())
                {
                    BOMManager obj = new BOMManager();
                    obj.BOMManagerId = GetReaderValue_Int32(reader, "BOMManagerId", 0);
                    obj.BOMManagerCode = GetReaderValue_String(reader, "BOMManagerCode", "");
                    obj.BOMManagerName = GetReaderValue_String(reader, "BOMManagerName", "");
                    obj.RowCnt = GetReaderValue_NullableInt32(reader, "RowCnt", null);
                    obj.CompanyName = GetReaderValue_String(reader, "CompanyName", "");
                    obj.CompanyNo = GetReaderValue_NullableInt32(reader, "CompanyNo", null);
                    obj.Inactive = GetReaderValue_Boolean(reader, "Inactive", false);
                    obj.BOMManagerStatus = GetReaderValue_String(reader, "Status", "");
                    obj.DLUP = GetReaderValue_DateTime(reader, "DLUP", DateTime.MinValue);
                    obj.CurrencyNo = GetReaderValue_Int32(reader, "CurrencyId", 0);
                    obj.TotalBomManagerLinePrice = GetReaderValue_Double(reader, "TotalBomManagerLinePrice", 0.00);
                    obj.POCurrencyNo = GetReaderValue_Int32(reader, "POCurrencyNo", 0);
                    obj.DateRequestToPOHub = GetReaderValue_NullableDateTime(reader, "DateRequestToPOHub", null);
                    obj.AssignedUser = GetReaderValue_String(reader, "AssignedUser", "");
                    obj.DivisionName = GetReaderValue_String(reader, "DivisionName", "");
                    obj.ClientCode = GetReaderValue_String(reader, "ClientCode", "");
                    obj.Requestedby = GetReaderValue_String(reader, "Requestedby", "");
                    obj.SalesmanName = GetReaderValue_String(reader, "SalesmanName", "");
                    obj.ContactName = GetReaderValue_String(reader, "ContactName", "");
                    obj.ContactNo = GetReaderValue_NullableInt32(reader, "ContactNo", null);
                    obj.ReceivedDate = GetReaderValue_DateTime(reader, "ReceivedDate", DateTime.MinValue);
                    obj.DatePromised = GetReaderValue_DateTime(reader, "DatePromised", DateTime.MinValue);
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get BOM", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// DataListNugget 
        /// Calls [usp_datalistnugget_BOM]
        /// </summary>
        /// <summary>
        /// DataListNugget 
        /// Calls [usp_datalistnugget_BOM]
        /// </summary>
        public override List<BOMManager> DataListNuggetOld_ForAssign(System.Int32? clientId, System.Int32? teamId, System.Int32? divisionId, System.Int32? loginId, System.Int32? orderBy, System.Int32? sortDir, System.Int32? pageIndex, System.Int32? pageSize, System.String bomManagerCode, System.String bomManagerName, System.Boolean? isPOHub, System.Int32? selectedClientNo, int? bomManagerStatus, int? IsAssignToMe, int? assignedUser, System.Int32? intDivision, System.Int32? salesPerson, System.DateTime? startdate, System.DateTime? enddate, string CompanyName)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                if (isPOHub == true)
                {
                    if (IsAssignToMe == 0)
                    {
                        cmd = new SqlCommand("usp_datalistnugget_PHBOMManager_ForAssign", cn);
                    }
                    if (IsAssignToMe == 1)
                    {
                        pageSize = 1000;//Set the max page size dynamically.
                        cmd = new SqlCommand("usp_datalistnugget_PHBOMManagerAssign", cn);
                    }
                }

                else
                {
                    cmd = new SqlCommand("usp_datalistnugget_BOMManager_ForAssign", cn);
                }
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 120;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@TeamId", SqlDbType.Int).Value = teamId;
                cmd.Parameters.Add("@DivisionId", SqlDbType.Int).Value = divisionId;
                cmd.Parameters.Add("@LoginId", SqlDbType.Int).Value = loginId;
                cmd.Parameters.Add("@OrderBy", SqlDbType.Int).Value = orderBy;
                cmd.Parameters.Add("@SortDir", SqlDbType.Int).Value = sortDir;
                cmd.Parameters.Add("@PageIndex", SqlDbType.Int).Value = pageIndex;
                cmd.Parameters.Add("@PageSize", SqlDbType.Int).Value = pageSize;
                cmd.Parameters.Add("@BOMManagerCode", SqlDbType.NVarChar).Value = bomManagerCode;
                //cmd.Parameters.Add("@CodeHi", SqlDbType.NVarChar).Value = CodeHi;
                cmd.Parameters.Add("@BOMManagerName", SqlDbType.NVarChar).Value = bomManagerName;
                cmd.Parameters.Add("@IsPoHub", SqlDbType.Bit).Value = isPOHub;
                cmd.Parameters.Add("@ClientNo", SqlDbType.Int).Value = selectedClientNo;
                cmd.Parameters.Add("@BomManagerStatus", SqlDbType.Int).Value = bomManagerStatus;
                cmd.Parameters.Add("@SalesPerson", SqlDbType.NVarChar).Value = salesPerson;//[003]
                cmd.Parameters.Add("@StartDate", SqlDbType.DateTime).Value = startdate;
                cmd.Parameters.Add("@EndDate", SqlDbType.DateTime).Value = enddate;//[003]
                cmd.Parameters.Add("@CompanyNo", SqlDbType.VarChar).Value = CompanyName;

                if (assignedUser != 0)
                {
                    if (assignedUser == -1)
                        assignedUser = 0;
                    cmd.Parameters.Add("@AssignedUser", SqlDbType.Int).Value = assignedUser;
                }
                cmd.Parameters.Add("@ClientDivisionNo", SqlDbType.Int).Value = intDivision;
                cn.Open();


                DbDataReader reader = ExecuteReader(cmd);
                List<BOMManager> lst = new List<BOMManager>();
                while (reader.Read())
                {
                    BOMManager obj = new BOMManager();
                    obj.BOMManagerId = GetReaderValue_Int32(reader, "BOMManagerId", 0);
                    obj.BOMManagerCode = GetReaderValue_String(reader, "BOMManagerCode", "");
                    obj.BOMManagerName = GetReaderValue_String(reader, "BOMManagerName", "");
                    obj.RowCnt = GetReaderValue_NullableInt32(reader, "RowCnt", null);
                    obj.CompanyName = GetReaderValue_String(reader, "CompanyName", "");
                    obj.CompanyNo = GetReaderValue_NullableInt32(reader, "CompanyNo", null);
                    obj.Inactive = GetReaderValue_Boolean(reader, "Inactive", false);
                    obj.BOMManagerStatus = GetReaderValue_String(reader, "Status", "");
                    obj.DLUP = GetReaderValue_DateTime(reader, "DLUP", DateTime.MinValue);
                    obj.CurrencyNo = GetReaderValue_Int32(reader, "CurrencyId", 0);
                    obj.TotalBomManagerLinePrice = GetReaderValue_Double(reader, "TotalBomManagerLinePrice", 0.00);
                    obj.POCurrencyNo = GetReaderValue_Int32(reader, "POCurrencyNo", 0);
                    obj.DateRequestToPOHub = GetReaderValue_NullableDateTime(reader, "DateRequestToPOHub", null);
                    obj.AssignedUser = GetReaderValue_String(reader, "AssignedUser", "");
                    obj.DivisionName = GetReaderValue_String(reader, "DivisionName", "");
                    obj.ClientCode = GetReaderValue_String(reader, "ClientCode", "");
                    obj.Requestedby = GetReaderValue_String(reader, "Requestedby", "");
                    obj.SalesmanName = GetReaderValue_String(reader, "SalesmanName", "");
                    obj.ContactName = GetReaderValue_String(reader, "ContactName", "");
                    obj.ContactNo = GetReaderValue_NullableInt32(reader, "ContactNo", null);
                    obj.ReceivedDate = GetReaderValue_DateTime(reader, "ReceivedDate", DateTime.MinValue);
                    obj.DatePromised = GetReaderValue_DateTime(reader, "DatePromised", DateTime.MinValue);
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get BOM", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// DataListNugget  for HURFQ
        /// Calls [usp_datalistnugget_CustomerRequirementForHUBRFQ]
        ///  add start date and end date  for searching by umendra
        /// </summary>
        public override List<CustomerRequirementDetails> DataListNuggetHUBRFQBOMManager(System.Int32? clientId, System.Int32? teamId, System.Int32? divisionId, System.Int32? loginId, System.Int32? orderBy, System.Int32? sortDir, System.Int32? pageIndex, System.Int32? pageSize, System.String BOMCode, System.String bomName, System.Boolean? isPOHub, System.Int32? selectedClientNo, int? bomStatus, int? isAssignToMe, int? assignedUser, System.String Manufacturer, System.String Part, System.Int32? intDivision, System.DateTime? startdate, System.DateTime? enddate, System.Int32? salesPerson)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_datalistnugget_CustomerRequirementForHUBRFQBOMManager", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;

                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@TeamId", SqlDbType.Int).Value = teamId;
                cmd.Parameters.Add("@DivisionId", SqlDbType.Int).Value = divisionId;
                cmd.Parameters.Add("@LoginId", SqlDbType.Int).Value = loginId;
                cmd.Parameters.Add("@OrderBy", SqlDbType.Int).Value = orderBy;
                cmd.Parameters.Add("@SortDir", SqlDbType.Int).Value = sortDir;
                cmd.Parameters.Add("@PageIndex", SqlDbType.Int).Value = pageIndex;
                cmd.Parameters.Add("@PageSize", SqlDbType.Int).Value = pageSize;
                cmd.Parameters.Add("@BOMManagerCode", SqlDbType.NVarChar).Value = BOMCode;
                cmd.Parameters.Add("@BOMManagerName", SqlDbType.NVarChar).Value = bomName;
                cmd.Parameters.Add("@IsPoHub", SqlDbType.Bit).Value = isPOHub;
                cmd.Parameters.Add("@ClientNo", SqlDbType.Int).Value = selectedClientNo;
                cmd.Parameters.Add("@BOMManagerStatus", SqlDbType.Int).Value = bomStatus;
                if (assignedUser > 0)
                {
                    cmd.Parameters.Add("@AssignedUser", SqlDbType.Int).Value = assignedUser;
                }

                cmd.Parameters.Add("@Manufacturer", SqlDbType.NVarChar).Value = Manufacturer;
                cmd.Parameters.Add("@PartSearch", SqlDbType.NVarChar).Value = Part;
                cmd.Parameters.Add("@ClientDivisionNo", SqlDbType.Int).Value = intDivision;
                cmd.Parameters.Add("@StartDate", SqlDbType.DateTime).Value = startdate;//[004]  
                cmd.Parameters.Add("@EndDate", SqlDbType.DateTime).Value = enddate;//[004]  
                cmd.Parameters.Add("@SalesPerson", SqlDbType.NVarChar).Value = salesPerson;//[003]

                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<CustomerRequirementDetails> lst = new List<CustomerRequirementDetails>();
                while (reader.Read())
                {
                    CustomerRequirementDetails obj = new CustomerRequirementDetails();
                    obj.CustomerRequirementId = GetReaderValue_Int32(reader, "CustomerRequirementId", 0);
                    obj.CustomerRequirementNumber = GetReaderValue_Int32(reader, "CustomerRequirementNumber", 0);
                    obj.Part = GetReaderValue_String(reader, "Part", "");
                    obj.ManufacturerNo = GetReaderValue_NullableInt32(reader, "ManufacturerNo", null);
                    obj.Quantity = GetReaderValue_Int32(reader, "Quantity", 0);
                    obj.ReceivedDate = GetReaderValue_DateTime(reader, "ReceivedDate", DateTime.MinValue);
                    obj.Salesman = GetReaderValue_Int32(reader, "Salesman", 0);
                    obj.DatePromised = GetReaderValue_DateTime(reader, "DatePromised", DateTime.MinValue);
                    obj.CompanyNo = GetReaderValue_Int32(reader, "CompanyNo", 0);
                    obj.ContactNo = GetReaderValue_Int32(reader, "ContactNo", 0);
                    obj.ROHS = GetReaderValue_Byte(reader, "ROHS", (byte)0);
                    obj.SalesmanName = GetReaderValue_String(reader, "SalesmanName", "");
                    obj.ManufacturerCode = GetReaderValue_String(reader, "ManufacturerCode", "");
                    obj.CompanyName = GetReaderValue_String(reader, "CompanyName", "");
                    obj.ContactName = GetReaderValue_String(reader, "ContactName", "");
                    obj.RowCnt = GetReaderValue_NullableInt32(reader, "RowCnt", null);
                    obj.BOMCode = GetReaderValue_String(reader, "BOMCode", "");
                    obj.BOMNo = GetReaderValue_NullableInt32(reader, "BOMId", 0);
                    obj.BOMName = GetReaderValue_String(reader, "BOMName", "");
                    obj.BOMStatus = GetReaderValue_String(reader, "Status", "");
                    obj.Price = GetReaderValue_Double(reader, "Price", 0.00);
                    obj.PHPrice = GetReaderValue_Double(reader, "PHPrice", 0.00);
                    obj.CurrencyNo = GetReaderValue_Int32(reader, "CurrencyId", 0);
                    obj.CurrencyCode = GetReaderValue_String(reader, "CurrencyCode", "");
                    obj.DateRequestToPOHub = GetReaderValue_DateTime(reader, "DateRequestToPOHub", DateTime.MinValue);
                    obj.POCurrencyNo = GetReaderValue_Int32(reader, "POCurrencyNo", 0);
                    obj.DLUP = GetReaderValue_DateTime(reader, "DLUP", DateTime.MinValue);
                    obj.DivisionName = GetReaderValue_String(reader, "DivisionName", "");
                    obj.AlternateStatus = GetReaderValue_NullableByte(reader, "AlternateStatus", (byte)0);
                    obj.Alternate = GetReaderValue_Boolean(reader, "Alternate", false);
                    obj.POCurrencyNo = GetReaderValue_Int32(reader, "POCurrencyNo", 0);
                    obj.SupportTeamMemberNo = GetReaderValue_NullableInt32(reader, "SupportTeamMemberNo", null);
                    obj.SupportTeamMemberName = GetReaderValue_String(reader, "SupportTeamMemberName", "");
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get CustomerRequirements", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override List<BOMManager> DataListNugget(System.Int32? clientId, System.Int32? teamId, System.Int32? divisionId, System.Int32? loginId, System.Int32? orderBy, System.Int32? sortDir, System.Int32? pageIndex, System.Int32? pageSize, System.String bomCode, System.String bomName, System.Boolean? isPOHub, System.Int32? selectedClientNo, int? bomStatus, int? IsAssignToMe, int? assignedUser, System.Int32? intDivision, System.Int32? salesPerson, System.DateTime? startdate, System.DateTime? enddate, string CompanyNo)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                if (isPOHub == true)
                {
                    if (IsAssignToMe == 0)
                    {
                        cmd = new SqlCommand("usp_datalistnugget_PHBOMManager", cn);
                    }
                    if (IsAssignToMe == 1)
                    {
                        pageSize = 1000;//Set the max page size dynamically.
                        cmd = new SqlCommand("usp_datalistnugget_PHBOMManagerAssign", cn);
                    }
                }

                else
                {
                    cmd = new SqlCommand("usp_datalistnugget_BOMManager", cn);
                }
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@TeamId", SqlDbType.Int).Value = teamId;
                cmd.Parameters.Add("@DivisionId", SqlDbType.Int).Value = divisionId;
                cmd.Parameters.Add("@LoginId", SqlDbType.Int).Value = loginId;
                cmd.Parameters.Add("@OrderBy", SqlDbType.Int).Value = orderBy;
                cmd.Parameters.Add("@SortDir", SqlDbType.Int).Value = sortDir;
                cmd.Parameters.Add("@PageIndex", SqlDbType.Int).Value = pageIndex;
                cmd.Parameters.Add("@PageSize", SqlDbType.Int).Value = pageSize;
                cmd.Parameters.Add("@BOMManagerCode", SqlDbType.NVarChar).Value = bomCode;
                //cmd.Parameters.Add("@CodeHi", SqlDbType.NVarChar).Value = CodeHi;
                cmd.Parameters.Add("@BOMManagerName", SqlDbType.NVarChar).Value = bomName;
                cmd.Parameters.Add("@IsPoHub", SqlDbType.Bit).Value = isPOHub;
                cmd.Parameters.Add("@ClientNo", SqlDbType.Int).Value = selectedClientNo;
                cmd.Parameters.Add("@BomManagerStatus", SqlDbType.Int).Value = bomStatus;
                cmd.Parameters.Add("@SalesPerson", SqlDbType.NVarChar).Value = salesPerson;//[003]
                cmd.Parameters.Add("@StartDate", SqlDbType.DateTime).Value = startdate;
                cmd.Parameters.Add("@EndDate", SqlDbType.DateTime).Value = enddate;//[003]
                cmd.Parameters.Add("@CompanyNo", SqlDbType.NVarChar).Value = CompanyNo;//[003]


                if (assignedUser > 0)
                {
                    cmd.Parameters.Add("@AssignedUser", SqlDbType.Int).Value = assignedUser;
                }
                cmd.Parameters.Add("@ClientDivisionNo", SqlDbType.Int).Value = intDivision;
                cn.Open();


                DbDataReader reader = ExecuteReader(cmd);
                List<BOMManager> lst = new List<BOMManager>();
                while (reader.Read())
                {
                    BOMManager obj = new BOMManager();
                    obj.BOMManagerId = GetReaderValue_Int32(reader, "BOMManagerId", 0);
                    obj.BOMManagerCode = GetReaderValue_String(reader, "BOMManagerCode", "");
                    obj.BOMManagerName = GetReaderValue_String(reader, "BOMManagerName", "");
                    obj.RowCnt = GetReaderValue_NullableInt32(reader, "RowCnt", null);
                    obj.CompanyName = GetReaderValue_String(reader, "CompanyName", "");
                    obj.CompanyNo = GetReaderValue_NullableInt32(reader, "CompanyNo", null);
                    obj.Inactive = GetReaderValue_Boolean(reader, "Inactive", false);
                    obj.BOMManagerStatus = GetReaderValue_String(reader, "Status", "");
                    obj.DLUP = GetReaderValue_DateTime(reader, "DLUP", DateTime.MinValue);
                    obj.CurrencyNo = GetReaderValue_Int32(reader, "CurrencyId", 0);
                    obj.TotalBomManagerLinePrice = GetReaderValue_Double(reader, "TotalBomManagerLinePrice", 0.00);
                    obj.POCurrencyNo = GetReaderValue_Int32(reader, "POCurrencyNo", 0);
                    obj.DateRequestToPOHub = GetReaderValue_NullableDateTime(reader, "DateRequestToPOHub", null);
                    obj.AssignedUser = GetReaderValue_String(reader, "AssignedUser", "");
                    obj.DivisionName = GetReaderValue_String(reader, "DivisionName", "");
                    obj.ClientCode = GetReaderValue_String(reader, "ClientCode", "");
                    obj.Requestedby = GetReaderValue_String(reader, "Requestedby", "");
                    obj.SalesmanName = GetReaderValue_String(reader, "SalesmanName", "");
                    obj.ContactName = GetReaderValue_String(reader, "ContactName", "");
                    obj.ContactNo = GetReaderValue_NullableInt32(reader, "ContactNo", null);
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get BOMManager", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override List<BOMManager> BOMSearch(System.Int32 BOMId, System.Int32? clientId, DateTime? FromDate, DateTime? ToDate, System.Int32? stageId, bool? isLock, System.Int32? teamId, System.Int32? divisionId, System.Int32? loginId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            SqlDataAdapter da = null;
            DataSet ds = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_datalistnugget_CustomerRequirement_BOMManager", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@TeamId", SqlDbType.Int).Value = teamId;
                cmd.Parameters.Add("@DivisionId", SqlDbType.Int).Value = divisionId;
                cmd.Parameters.Add("@LoginId", SqlDbType.Int).Value = loginId;
                cmd.Parameters.Add("@BOMId", SqlDbType.Int).Value = BOMId;
                cmd.Parameters.Add("@ReceivedDateFrom", SqlDbType.DateTime).Value = FromDate;
                cmd.Parameters.Add("@ReceivedDateTo", SqlDbType.DateTime).Value = ToDate;
                //cmd.Parameters.Add("@STAGEID", SqlDbType.Int).Value = stageId;
                //cmd.Parameters.Add("@ISLOCK", SqlDbType.Bit).Value = isLock;
                cn.Open();
                da = new SqlDataAdapter(cmd);
                ds = new DataSet();
                da.Fill(ds);
                List<BOMManager> lst = new List<BOMManager>();
                if (ds.Tables[0].Rows.Count > 0)
                {
                    foreach (DataRow dr in ds.Tables[0].Rows)
                    {
                        BOMManager BOM = new BOMManager();
                        BOM.CustomerRequirementId = ConvertIntVal(dr["CustomerRequirementId"].ToString());
                        BOM.CustomerRequirementNumber = ConvertIntVal(dr["CustomerRequirementNumber"].ToString());
                        BOM.Part = dr["Part"].ToString();
                        BOM.ROHS = ConvertByteValNullable(dr["ROHS"].ToString());
                        BOM.ManufacturerNo = ConvertIntValNullable(dr["ManufacturerNo"].ToString());
                        BOM.ManufacturerCode = dr["ManufacturerCode"].ToString();
                        BOM.Quantity = ConvertIntVal(dr["Quantity"].ToString());
                        BOM.CompanyName = dr["CompanyName"].ToString();
                        BOM.CompanyNo = ConvertIntValNullable(dr["CompanyNo"].ToString());
                        BOM.ContactName = dr["ContactName"].ToString();
                        BOM.ContactNo = ConvertIntValNullable(dr["ContactNo"].ToString());
                        BOM.SalesmanName = dr["SalesmanName"].ToString();
                        BOM.ReceivedDate = ConvertDateTimeVal(dr["ReceivedDate"].ToString());
                        BOM.DatePromised = ConvertDateTimeVal(dr["DatePromised"].ToString());
                        BOM.BOMManagerCode = dr["BOMManagerCode"].ToString();
                        BOM.BOMManagerNo = ConvertIntValNullable(dr["BOMManagerId"].ToString());
                        BOM.BOMManagerName = dr["BOMManagerName"].ToString();
                        //BOMMgr.TotalInBase = Functions.FormatCurrency( bomItem.TotalInBase SessionManager.ClientCurrencyCode, 2);
                        BOM.TotalInBase = ConvertDoubleValNullable(dr["TotalInBase"].ToString());// need to concatenate client currency in site project
                        //BOMMgr.TotalValue = Functions.FormatCurrency(bomItem.TotalValue, bomItem.CurrencyCode, 2);
                        BOM.TotalValue = ConvertDoubleValNullable(dr["TotalValue"].ToString());// need to concatenate bomItem currency in site project
                        BOM.CurrencyCode = dr["CurrencyCode"].ToString();// need to concatenate bomItem currency in site project
                        BOM.REQStatusName = dr["REQStatus"].ToString();
                        BOM.BOMManagerStatus = dr["Status"].ToString();
                        //BOM.BOMNo = Convert.ToInt32(dr["SalesBomManagerImportId"].ToString());
                        //BOM.stockId = Convert.ToInt32(dr["StockCode"].ToString());
                        //BOM.Descriptions = dr["Description"].ToString();
                        //BOM.Part = dr["Part"].ToString();
                        //BOM.RFQ = Convert.ToDouble(dr["RFQ"].ToString());
                        //BOM.UnitPrice = Convert.ToDouble(dr["UnitPrice"].ToString());
                        //BOM.line = Convert.ToDouble(dr["LineTotal"].ToString());
                        //BOM.Clientid = Convert.ToInt32(dr["Clientid"].ToString());
                        //BOM.ClientName = dr["ClientName"].ToString();
                        //BOM.StatusId = Convert.ToInt32(dr["StatusId"].ToString());
                        //BOM.Islock = Convert.ToBoolean(dr["Islock"].ToString());
                        lst.Add(BOM);
                        BOM = null;
                    }

                }
                return lst;

            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get BOM Search", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// Get 
        /// Calls [usp_select_BOM]
        /// </summary>
        public override BOMManager Get(System.Int32? BOMManagerId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_select_BOMManager", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@BOMManagerId", SqlDbType.Int).Value = BOMManagerId;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd, CommandBehavior.SingleRow);
                if (reader.Read())
                {
                    //return GetBOMFromReader(reader);
                    Rebound.GlobalTrader.DAL.BOMManager obj = new Rebound.GlobalTrader.DAL.BOMManager();
                    obj.BOMManagerId = GetReaderValue_Int32(reader, "BOMManagerId", 0);
                    obj.ClientNo = GetReaderValue_Int32(reader, "ClientNo", 0);
                    obj.BOMManagerName = GetReaderValue_String(reader, "BOMManagerName", "");
                    obj.Notes = GetReaderValue_String(reader, "Notes", "");
                    obj.BOMManagerCode = GetReaderValue_String(reader, "BOMManagerCode", "");
                    obj.Inactive = GetReaderValue_Boolean(reader, "Inactive", false);
                    obj.UpdatedBy = GetReaderValue_NullableInt32(reader, "UpdatedBy", null);
                    obj.UpdatedByName = GetReaderValue_String(reader, "UpdateByName", "");
                    obj.CompanyNo = GetReaderValue_Int32(reader, "companyId", 0);
                    obj.CompanyName = GetReaderValue_String(reader, "CompanyName", "");
                    obj.ContactNo = GetReaderValue_Int32(reader, "contactId", 0);
                    obj.ContactName = GetReaderValue_String(reader, "contactName", "");
                    obj.RequestToPOHubBy = GetReaderValue_NullableInt32(reader, "RequestToPOHubBy", null);
                    obj.DateRequestToPOHub = GetReaderValue_NullableDateTime(reader, "DateRequestToPOHub", null);
                    obj.ReleaseBy = GetReaderValue_NullableInt32(reader, "ReleaseBy", null);
                    obj.DateRelease = GetReaderValue_NullableDateTime(reader, "DateRelease", null);
                    obj.BomManagerCount = GetReaderValue_Int32(reader, "BomManagerCount", 0);
                    obj.UpdatedBy = GetReaderValue_NullableInt32(reader, "UpdatedBy", null);
                    obj.DLUP = GetReaderValue_DateTime(reader, "DLUP", DateTime.MinValue);
                    obj.BOMManagerStatus = GetReaderValue_String(reader, "Status", "");
                    obj.StatusValue = GetReaderValue_NullableInt32(reader, "StatusValue", null);
                    obj.CurrencyCode = GetReaderValue_String(reader, "CurrencyCode", "");
                    obj.CurrencyNo = GetReaderValue_NullableInt32(reader, "CurrencyId", null);
                    obj.Currency_Code = GetReaderValue_String(reader, "Currency_Code", "");
                    obj.CurrentSupplier = GetReaderValue_String(reader, "CurrentSupplier", "");
                    obj.QuoteRequired = GetReaderValue_DateTime(reader, "QuoteRequired", DateTime.MinValue);
                    obj.AllItemHasSourcing = GetReaderValue_NullableInt32(reader, "AllHasSourcing", 0);
                    obj.AS9120 = GetReaderValue_Boolean(reader, "AS9120", false);
                    obj.Requestedby = GetReaderValue_String(reader, "Requestedby", "");
                    obj.Releasedby = GetReaderValue_String(reader, "Releasedby", "");
                    obj.NoBidCount = GetReaderValue_Int32(reader, "NoBidCount", 0);
                    obj.UpdateByPH = GetReaderValue_Int32(reader, "UpdateByPH", 0);
                    obj.AssignedUser = GetReaderValue_String(reader, "AssignTo", "");

                    obj.Contact2Id = GetReaderValue_Int32(reader, "Contact2Id", 0);
                    obj.Contact2Name = GetReaderValue_String(reader, "Contact2Name", "");
                    obj.ValidationMessage = GetReaderValue_String(reader, "ValidateMessage", "");
                    obj.IsReqInValid = GetReaderValue_Int32(reader, "IsReqInValid", 0) > 0 ? true : false;
                    obj.ReqSalesPerson = GetReaderValue_String(reader, "ReqSalesPerson", "");
                    obj.ReqSalesPersonName = GetReaderValue_String(reader, "ReqSalesPersonName", "");
                    obj.SupportTeamMemberNoAsString = GetReaderValue_String(reader, "SupportTeamMemberNo", "");
                    obj.ClientNo = GetReaderValue_Int32(reader, "ClientNo", 0);
                    obj.BOMItemsCount = GetReaderValue_Int32(reader, "BOMItemsCount", 0);
                    return obj;
                }
                else
                {
                    return null;
                }
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get BOMManager", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        public int? ConvertIntValNullable(string a)
        {
            int outint;
            bool rslt;
            rslt = int.TryParse(a, out outint);
            if (rslt == true)
                return Convert.ToInt32(a);
            else
                return null;

        }
        public int ConvertIntVal(string a)
        {
            int outint;
            bool rslt;
            rslt = int.TryParse(a, out outint);
            if (rslt == true)
                return Convert.ToInt32(a);
            else
                return 0;

        }
        public Double? ConvertDoubleValNullable(string a)
        {
            double outint;
            bool rslt;
            rslt = double.TryParse(a, out outint);
            if (rslt == true)
                return Convert.ToDouble(a);
            else
                return null;

        }
        public Double ConvertDoubleVal(string a)
        {
            double outint;
            bool rslt;
            rslt = double.TryParse(a, out outint);
            if (rslt == true)
                return Convert.ToDouble(a);
            else
                return 0;

        }
        public Byte? ConvertByteValNullable(string a)
        {
            Byte outint;
            bool rslt;
            rslt = Byte.TryParse(a, out outint);
            if (rslt == true)
                return Convert.ToByte(a);
            else
                return null;

        }

        public Byte ConvertByteVal(string a)
        {
            Byte outint;
            bool rslt;
            rslt = Byte.TryParse(a, out outint);
            if (rslt == true)
                return Convert.ToByte(a);
            else
                return 0;

        }

        public DateTime? ConvertDateTimeValNullable(string a)
        {
            DateTime outint;
            bool rslt;
            rslt = DateTime.TryParse(a, out outint);
            if (rslt == true)
                return Convert.ToDateTime(a);
            else
                return null;

        }
        public DateTime ConvertDateTimeVal(string a)
        {
            DateTime outint;
            bool rslt;
            rslt = DateTime.TryParse(a, out outint);
            if (rslt == true)
                return Convert.ToDateTime(a);
            else
                return DateTime.Now;

        }
        public Boolean? ConvertBooleanValNullable(string a)
        {
            Boolean outint;
            bool rslt;
            rslt = Boolean.TryParse(a, out outint);
            if (rslt == true)
                return Convert.ToBoolean(a);
            else
                return null;

        }
        public Boolean ConvertBooleanVal(string a)
        {
            Boolean outint;
            bool rslt;
            rslt = Boolean.TryParse(a, out outint);
            if (rslt == true)
                return Convert.ToBoolean(a);
            else
                return false;

        }

        /// <summary>
        /// GetBOMListForCustomerRequirement 
        /// Calls [usp_selectAll_CustomerRequirement_for_BOM]
        /// </summary>
        public override List<BOMManager> GetBOMListForCustomerRequirement(System.Int32? BOMNo, System.Int32? clientID, int curPage, int Rpp, System.String Part, System.Int32? ReqType = 0)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            SqlDataAdapter da = null;
            DataSet ds = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_selectAll_CustomerRequirement_for_BOMManager", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;
                cmd.Parameters.Add("@BOMManagerNo", SqlDbType.Int).Value = BOMNo;
                cmd.Parameters.Add("@ClientID", SqlDbType.Int).Value = clientID;
                cmd.Parameters.Add("@Part", SqlDbType.VarChar).Value = Part;
                cmd.Parameters.Add("@curPage", SqlDbType.Int).Value = curPage;
                cmd.Parameters.Add("@Rpp", SqlDbType.Int).Value = Rpp;
                cmd.Parameters.Add("@ReqType", SqlDbType.Int).Value = ReqType;

                cn.Open();
                da = new SqlDataAdapter(cmd);
                ds = new DataSet();
                da.Fill(ds);
                List<BOMManager> lst = new List<BOMManager>();
                if (ds.Tables[0].Rows.Count > 0)
                {
                    foreach (DataRow dr in ds.Tables[0].Rows)
                    {

                        BOMManager obj = new BOMManager();
                        obj.PackageNo = ConvertIntValNullable(dr["PackageNo"].ToString());
                        obj.PackageName = dr["PackageName"].ToString();
                        obj.ProductName = dr["ProductName"].ToString();
                        obj.ProductNo = ConvertIntValNullable(dr["Productid"].ToString());
                        obj.ProductDescription = dr["ProductDescription"].ToString();
                        obj.CurrencyCode = dr["CurrencyCode"].ToString();
                        obj.SalesmanName = dr["SalesmanName"].ToString();
                        obj.ManufacturerCode = (dr["ManufacturerCode"].ToString());
                        obj.CompanyName = (dr["CompanyName"].ToString());
                        obj.CustomerPart = (dr["CustomerPart"].ToString());
                        obj.Closed = ConvertBooleanVal(dr["Closed"].ToString());
                        obj.ROHS = ConvertByteValNullable(dr["ROHS"].ToString());
                        obj.UpdatedBy = ConvertIntVal(dr["UpdatedBy"].ToString());
                        obj.DLUP = ConvertDateTimeVal(dr["DLUP"].ToString());
                        obj.CompanyNo = ConvertIntValNullable(dr["CompanyNo"].ToString());
                        obj.Alternate = ConvertBooleanVal(dr["Alternate"].ToString());
                        obj.Instructions = dr["Instructions"].ToString();
                        obj.Salesman = ConvertIntVal(dr["Salesman"].ToString());
                        obj.DatePromised = ConvertDateTimeVal(dr["DatePromised"].ToString());
                        obj.BOMManagerHeader = dr["BOMManagerHeader"].ToString();
                        obj.BOMManagerName = dr["BOMName"].ToString();
                        obj.BOMManagerNo = ConvertIntValNullable(dr["BOMManagerNo"].ToString());
                        obj.POHubReleaseBy = ConvertIntValNullable(dr["POHubReleaseBy"].ToString());
                        obj.SourcingResultId = ConvertIntValNullable(dr["SourcingResult"].ToString());
                        obj.RequestToPOHubBy = ConvertIntValNullable(dr["RequestToPOHubBy"].ToString());
                        obj.BOMManagerFullName = dr["BOMManagerFullName"].ToString();
                        obj.BOMManagerCode = dr["BOMManagerCode"].ToString();
                        obj.ConvertedTargetValue = ConvertDoubleVal(dr["ConvertedTargetValue"].ToString());
                        obj.BOMManagerCurrencyCode = dr["BOMManagerCurrencyCode"].ToString();
                        //obj.PurchaseQuoteId = ConvertIntVal(dr["PurchaseQuoteId"].ToString());
                        obj.ClientName = dr["ClientName"].ToString();
                        //obj.POHubCompany = ConvertIntVal(dr["POHubCompany"].ToString());
                        obj.MSL = dr["MSL"].ToString();
                        obj.FactorySealed = ConvertBooleanValNullable(dr["FactorySealed"].ToString());
                        obj.AllSorcingHasDelDate = ConvertIntVal(dr["AllSorcingHasDelDate"].ToString());
                        obj.AllSorcingHasProduct = ConvertIntVal(dr["AllSorcingHasProduct"].ToString());
                        obj.SourcingResult = ConvertIntVal(dr["SourcingResult"].ToString());
                        obj.BOMManagerStatus = dr["BOMManagerStatus"].ToString();
                        obj.CustomerRequirementId = ConvertIntVal(dr["CustomerRequirementId"].ToString());
                        obj.CustomerRequirementNumber = ConvertIntVal(dr["CustomerRequirementNumber"].ToString());
                        obj.ClientNo = ConvertIntVal(dr["ClientNo"].ToString());
                        obj.FullPart = dr["FullPart"].ToString();
                        obj.Part = dr["Part"].ToString();
                        obj.ManufacturerNo = ConvertIntValNullable(dr["ManufacturerNo"].ToString());
                        obj.TargetSellPrice = ConvertIntVal(dr["TargetSellPrice"].ToString());
                        obj.Line = ConvertIntVal(dr["Line"].ToString());
                        obj.LineValue = ConvertDoubleVal(dr["LineValue"].ToString());
                        obj.ManufacturerName = dr["ManufacturerName"].ToString();
                        obj.DateCode = dr["DateCode"].ToString();
                        obj.Quantity = ConvertIntVal(dr["Quantity"].ToString());
                        obj.Price = ConvertDoubleVal(dr["Price"].ToString());
                        obj.CurrencyNo = ConvertIntValNullable(dr["CurrencyNo"].ToString());
                        //obj.HasClientSourcingResult = Convert.ToBoolean(dr["HasClientSourcingResult"].ToString());
                        obj.HasHubSourcingResult = ConvertByteValNullable(dr["HasHubSourcingResult"].ToString()) != null ? true : false;
                        //obj.IsNoBid = Convert.ToBoolean(dr["IsNoBid"].ToString());
                        //obj.ExpeditDate = Convert.ToDateTime(dr["ExpediteDate"].ToString());
                        obj.UpdateByPH = ConvertIntValNullable(dr["UpdateByPH"].ToString());
                        //obj.AlternateStatus = Convert.ToByte(dr["AlternateStatus"].ToString());
                        obj.SupportTeamMemberNo = ConvertIntValNullable(dr["SupportTeamMemberNo"].ToString());
                        obj.SupportTeamMemberName = dr["SupportTeamMemberName"].ToString();
                        obj.IsPrimarySource = ConvertBooleanValNullable(dr["IsPrimarySourcing"].ToString());
                        obj.IsPrimarySourceActual = ConvertBooleanValNullable(dr["IsPrimarySourcing"].ToString());
                        obj.QuoteGenerated = ConvertBooleanValNullable(dr["QuoteGenerated"].ToString());
                        obj.QuoteId = ConvertIntValNullable(dr["QuoteId"].ToString());
                        obj.QuoteNumber = ConvertIntValNullable(dr["QuoteNo"].ToString());
                        obj.Notes = dr["Notes"].ToString();
                        obj.ReqStatus = ConvertIntValNullable(dr["ReqStatus"].ToString());
                        obj.AutosourcingStatus = ConvertBooleanValNullable(dr["AutoSourcingStatus"].ToString());
                        obj.ReqStatusText = dr["ReqStatusText"].ToString();
                        obj.IsNoBid = ConvertBooleanValNullable(dr["IsNoBid"].ToString());
                        obj.TotalCount = ConvertIntValNullable(dr["TotalRecords"].ToString());
                        obj.OfferCount = ConvertIntVal(dr["OfferCount"].ToString());
                        obj.GeneratedFilename = dr["GeneratedFilename"].ToString();
                        obj.Releasedby = dr["POHubReleaseName"].ToString();
                        obj.ReleaseNote = dr["ReleaseNote"].ToString();
                        obj.MfrAdvisoryNotes = dr["MfrAdvisoryNotes"].ToString();
                        lst.Add(obj);
                        obj = null;
                    }

                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get CustomerRequirements", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// Update BOM
        /// Calls [usp_update_BOM]
        /// </summary>
        public override bool UpdateBOMManager(System.Int32? bomId, System.Int32? clientNo, System.String bomName, System.String notes, System.String bomCode, System.Boolean? inactive, System.Int32? updatedBy, System.Int32? companyId, System.Int32? contactId, System.Int32? currencyNo, System.String currentSupplier, System.DateTime? quoteRequired, System.Boolean? AS9120, System.Int32? contact2Id, System.Int32? salespersonId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_update_BOMManager", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@BOMManagerId", SqlDbType.Int).Value = bomId;
                cmd.Parameters.Add("@ClientNo", SqlDbType.Int).Value = clientNo;
                cmd.Parameters.Add("@BOMManagerName", SqlDbType.NVarChar).Value = bomName;
                cmd.Parameters.Add("@Notes", SqlDbType.NVarChar).Value = notes;
                cmd.Parameters.Add("@BOMManagerCode", SqlDbType.NVarChar).Value = bomCode;
                cmd.Parameters.Add("@Inactive", SqlDbType.Bit).Value = inactive;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@CompanyNo", SqlDbType.Int).Value = companyId;
                cmd.Parameters.Add("@ContactNo", SqlDbType.Int).Value = contactId;
                cmd.Parameters.Add("@CurrencyNo", SqlDbType.Int).Value = currencyNo;
                cmd.Parameters.Add("@CurrentSupplier", SqlDbType.VarChar).Value = currentSupplier;
                cmd.Parameters.Add("@QuoteRequired", SqlDbType.DateTime).Value = quoteRequired;
                cmd.Parameters.Add("@AS9120", SqlDbType.Bit).Value = AS9120;
                cmd.Parameters.Add("@Contact2Id", SqlDbType.Int).Value = contact2Id;
                cmd.Parameters.Add("@SalespersonId", SqlDbType.Int).Value = salespersonId;
                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to update BOM", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// Update BOM
        /// Calls [usp_update_BOM_POHubQuote]
        /// </summary>
        public override bool UpdatePurchaseQuote(System.Int32? bomId, System.Int32? updatedBy, System.Int32? bomStatus, System.Int32 AssignUserNo, out System.String ValidateMessage, string AssignedUserType)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_update_BOMManager_POHubQuote", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@BOMManagerId", SqlDbType.Int).Value = bomId;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@BOMManagerStatus", SqlDbType.Int).Value = bomStatus;
                cmd.Parameters.Add("@AssignUserNo", SqlDbType.Int).Value = AssignUserNo;
                cmd.Parameters.Add("@AssignedUserType", SqlDbType.VarChar).Value = AssignedUserType;
                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cmd.Parameters.Add("@ValidateMessage", SqlDbType.VarChar, 250).Direction = ParameterDirection.Output;
                cn.Open();
                //int ret = ExecuteNonQuery(cmd);
                // ExecuteNonQuery(cmd);
                ExecuteScalar(cmd);
                int ret = (int)(cmd.Parameters["@RowsAffected"].Value == null ? 0 : cmd.Parameters["@RowsAffected"].Value);
                ValidateMessage = (String)(cmd.Parameters["@ValidateMessage"].Value == null ? null : cmd.Parameters["@ValidateMessage"].Value);
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to update BOMManager", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override DataTable GetBOMManagerDetailFromTemp(int displayLength, int displayStart, int sortCol, string sortDir, string search, int userid, int Clientid, int SelectedclientId, int clientType_con)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {

                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_GetBOMManagerNewData", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@displayLength", SqlDbType.Int).Value = displayLength;
                cmd.Parameters.Add("@displayStart", SqlDbType.Int).Value = displayStart;
                cmd.Parameters.Add("@sortCol", SqlDbType.Int).Value = sortCol;
                cmd.Parameters.Add("@sortDir", SqlDbType.NVarChar, 10).Value = sortDir;//[004]  
                cmd.Parameters.Add("@search", SqlDbType.NVarChar, 255).Value = search;//[004]  
                cmd.Parameters.Add("@userid", SqlDbType.Int).Value = userid;//[003]
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = Clientid;//[003]
                cmd.Parameters.Add("@SelectedClientId", SqlDbType.Int).Value = SelectedclientId;//[003]
                //cmd.CommandTimeout = 60;
                cmd.CommandTimeout = 900;

                cn.Open();
                DataSet ds = new DataSet();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);
                return ds.Tables[0];
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get CustomerRequirements", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override DataTable GenerateCustomTemplateData(int QuoteId, string ColumnString)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {

                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_GenerateCustomTemplateData", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@QuoteId", SqlDbType.Int).Value = QuoteId;
                cmd.Parameters.Add("@ColumnString", SqlDbType.VarChar).Value = ColumnString;
                //cmd.CommandTimeout = 60;
                cmd.CommandTimeout = 900;

                cn.Open();
                DataSet ds = new DataSet();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);
                return ds.Tables[0];
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get CustomerRequirements", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override DataTable SaveCustomTemplateMapping(int QuoteId, string MappingDetails, int? clientNo, int CompanyNo, int? updatedBy)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {

                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_SaveCustomTemplateMapping", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@QuoteId", SqlDbType.Int).Value = QuoteId;
                cmd.Parameters.Add("@ClientNo", SqlDbType.Int).Value = clientNo;
                cmd.Parameters.Add("@CompanyNo", SqlDbType.Int).Value = CompanyNo;
                cmd.Parameters.Add("@MappingDetails", SqlDbType.VarChar).Value = MappingDetails;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                //cmd.CommandTimeout = 60;
                cmd.CommandTimeout = 900;

                cn.Open();
                DataSet ds = new DataSet();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);
                return ds.Tables[0];
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to save Custom Template Mapping.", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override DataTable GetBOMManagerUploadMapping(int QuoteId, int? clientNo)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {

                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_GetBOMManagerUploadMapping", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@QuoteId", SqlDbType.Int).Value = QuoteId;
                cmd.Parameters.Add("@ClientNo", SqlDbType.Int).Value = clientNo;

                //cmd.CommandTimeout = 60;
                cmd.CommandTimeout = 900;

                cn.Open();
                DataSet ds = new DataSet();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);
                return ds.Tables[0];
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to save Custom Template Mapping.", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override DataTable GetCustomTemplateMapping(int QuoteId, int? clientNo)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {

                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_GetCustomTemplateMapping", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@QuoteId", SqlDbType.Int).Value = QuoteId;
                cmd.Parameters.Add("@ClientNo", SqlDbType.Int).Value = clientNo;
                //cmd.CommandTimeout = 60;
                cmd.CommandTimeout = 900;

                cn.Open();
                DataSet ds = new DataSet();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);
                return ds.Tables[0];
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to Get Custom Template Mapping.", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        public override DataTable GetBOMManagerGenrateTempData(int displayLength, int displayStart, int sortCol, string sortDir, string search, int userid, int Clientid, int SelectedclientId, string ColumnList, string ddlCurancy, string CompanyNameText, int clientType_con, string Column_Lable, string Column_Name, string ContactText, bool OverRideCurrency, string DefaultCurrencyName, string CurrencyColumnName, int DefaultCurrencyId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_GetDynamicBOMManagerHeaderColumn", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@displayLength", SqlDbType.Int).Value = displayLength;
                cmd.Parameters.Add("@displayStart", SqlDbType.Int).Value = displayStart;
                cmd.Parameters.Add("@sortCol", SqlDbType.Int).Value = sortCol;
                cmd.Parameters.Add("@sortDir", SqlDbType.NVarChar, 10).Value = sortDir;//[004]  
                cmd.Parameters.Add("@search", SqlDbType.NVarChar, 255).Value = search;//[004]  
                cmd.Parameters.Add("@userid", SqlDbType.Int).Value = userid;//[003]
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = Clientid;//[003]
                cmd.Parameters.Add("@SelectedClientId", SqlDbType.Int).Value = SelectedclientId;//[003]
                cmd.Parameters.Add("@list_Label_name", SqlDbType.NVarChar).Value = Column_Lable;
                cmd.Parameters.Add("@list_column_name", SqlDbType.NVarChar).Value = Column_Name;
                cmd.Parameters.Add("@SelectColumns", SqlDbType.NVarChar).Value = ColumnList;
                cmd.Parameters.Add("@CurancyColumns", SqlDbType.NVarChar).Value = ddlCurancy;
                cmd.Parameters.Add("@CompanyNameText", SqlDbType.NVarChar).Value = CompanyNameText;
                cmd.Parameters.Add("@ContactNameText", SqlDbType.NVarChar).Value = ContactText;
                cmd.Parameters.Add("@OverRideCurrency", SqlDbType.Bit).Value = OverRideCurrency;
                cmd.Parameters.Add("@DefaultCurrecy", SqlDbType.NVarChar).Value = DefaultCurrencyName;
                cmd.Parameters.Add("@CurrencyColumn", SqlDbType.NVarChar).Value = CurrencyColumnName;
                cmd.Parameters.Add("@DefaultCurrecyNo", SqlDbType.Int).Value = DefaultCurrencyId;

                //cmd.CommandTimeout = 60;
                cmd.CommandTimeout = 600;

                cn.Open();
                DataSet ds = new DataSet();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);
                // changes done to catch exception from SP
                if (ds.Tables.Count == 1)   // if exception is thrown
                    return ds.Tables[0];
                else
                {
                    int counttsble = (ds.Tables.Count - 1);
                    return ds.Tables[counttsble];   // if no exception then multiple tables collection return so picking last table
                }

            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception(sqlex.Message);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override void saveBOMManagerExcelBulkSave(DataTable tempStock, DataTable dtData, string originalFilename, string generatedFilename, int userId, int clientId, int? SelectedclientId, int clientType_con, string DefaultCurrency)
        {
            try
            {
                using (SqlConnection dbConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString()))
                {
                    dbConnection.Open();
                    if (dbConnection.State == ConnectionState.Open)
                    {
                        using (var tran = dbConnection.BeginTransaction(IsolationLevel.ReadCommitted))
                        {
                            using (SqlBulkCopy s = new SqlBulkCopy(dbConnection, SqlBulkCopyOptions.Default, tran))
                            {
                                s.DestinationTableName = tempStock.TableName;
                                int i = 1;
                                foreach (var column in dtData.Columns)
                                {
                                    if (i <= 15)
                                    {
                                        s.ColumnMappings.Add(column.ToString(), "Column" + i.ToString());
                                    }
                                    i++;
                                }

                                DataColumn ClientId = new DataColumn();
                                ClientId.DataType = typeof(int);
                                ClientId.ColumnName = "ClientId";

                                DataColumn SelectedClientId = new DataColumn();
                                SelectedClientId.DataType = typeof(int);
                                SelectedClientId.ColumnName = "SelectedClientId";

                                DataColumn CreatedBy = new DataColumn();
                                CreatedBy.DataType = typeof(int);
                                CreatedBy.ColumnName = "CreatedBy";

                                DataColumn orgFilename = new DataColumn();
                                orgFilename.DataType = typeof(string);
                                orgFilename.ColumnName = "OriginalFilename";

                                DataColumn genFilename = new DataColumn();
                                genFilename.DataType = typeof(string);
                                genFilename.ColumnName = "GeneratedFilename";

                                DataColumn DefaultCurrencyname = new DataColumn();
                                DefaultCurrencyname.DataType = typeof(string);
                                DefaultCurrencyname.ColumnName = "DefaultCurrency";

                                // Add the columns to the tempStock DataTable
                                dtData.Columns.Add(ClientId);
                                dtData.Columns.Add(SelectedClientId);
                                dtData.Columns.Add(CreatedBy);
                                dtData.Columns.Add(orgFilename);
                                dtData.Columns.Add(genFilename);
                                dtData.Columns.Add(DefaultCurrencyname);
                                foreach (DataRow dr in dtData.Rows)
                                {
                                    //  DataRow appendStockfiled = dtData.NewRow();
                                    dr["ClientId"] = clientId;
                                    dr["SelectedClientId"] = SelectedclientId;
                                    dr["CreatedBy"] = userId;
                                    dr["OriginalFilename"] = originalFilename;
                                    dr["GeneratedFilename"] = generatedFilename;
                                    dr["DefaultCurrency"] = DefaultCurrency;
                                }

                                dtData.AcceptChanges();

                                s.ColumnMappings.Add("ClientId", "ClientId");
                                s.ColumnMappings.Add("SelectedClientId", "SelectedClientId");
                                s.ColumnMappings.Add("CreatedBy", "CreatedBy");
                                s.ColumnMappings.Add("OriginalFilename", "OriginalFilename");
                                s.ColumnMappings.Add("GeneratedFilename", "GeneratedFilename");
                                s.ColumnMappings.Add("DefaultCurrency", "DefaultCurrency");

                                s.WriteToServer(dtData);
                                tran.Commit();
                            }
                        }
                    }
                }
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to insert BOM data", sqlex);
            }
        }

        public override void SaveBOMManagerExcelHeader(string columnList, string insertColumnList, System.Int32? clientId, int? SelectedclientId, int? loginId, int clientType_con)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_SaveBOMmanagerExcelColumn", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 600;
                cmd.Parameters.Add("@InserColumnList", SqlDbType.NVarChar, 3000).Value = insertColumnList;
                cmd.Parameters.Add("@SelectColumns", SqlDbType.NVarChar, 3000).Value = columnList;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@SelectedClientId", SqlDbType.Int).Value = SelectedclientId;
                cmd.Parameters.Add("@UserId", SqlDbType.Int).Value = loginId;

                cn.Open();
                cmd.ExecuteNonQuery();
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to insert header info", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override string SaveBOMManagerImportData(int userId, int ClientId, int SelectedclientId, string Column_Lable, string Column_Name, string insertDataList, string fileColName, string ddlCurrency, out string errorMessage, string BomName, string CompanyName, string ContactName, int SalesmanId, int CompanyId, int ContactId, bool PartWatch, int DefaultCurrencyId, bool OverRideCurrency, string SaveImportOrHubRFQ, out string NewBomCode, out int NewBomid, int? ReqforTraceabilityId, int? TypeId, DateTime DateRequired)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            errorMessage = string.Empty;
            NewBomCode = string.Empty;
            NewBomid = 0;
            try
            {
                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                //cmd = new SqlCommand("usp_Insert_BomManagerData", cn);
                cmd = new SqlCommand("usp_InsertBomManagerData", cn);
                //cmd = new SqlCommand("procname", cn);

                cmd.CommandType = CommandType.StoredProcedure;
                //code start [010]
                cmd.CommandTimeout = 900;
                //code end [010]
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = ClientId;
                cmd.Parameters.Add("@UserId", SqlDbType.Int).Value = userId;
                cmd.Parameters.Add("@SelectedClientID", SqlDbType.Int).Value = SelectedclientId;
                cmd.Parameters.Add("@list_Label_name", SqlDbType.NVarChar).Value = Column_Lable;
                cmd.Parameters.Add("@list_column_name", SqlDbType.NVarChar).Value = Column_Name;
                cmd.Parameters.Add("@insertDataList", SqlDbType.NVarChar).Value = insertDataList;
                cmd.Parameters.Add("@fileColName", SqlDbType.NVarChar).Value = fileColName;
                cmd.Parameters.Add("@CurrencyName", SqlDbType.NVarChar).Value = ddlCurrency;
                cmd.Parameters.Add("@RecordCount", SqlDbType.Int).Direction = ParameterDirection.Output;
                cmd.Parameters.Add("@ErrorMessage", SqlDbType.VarChar, 400).Direction = ParameterDirection.Output;
                cmd.Parameters.Add("@BomName", SqlDbType.NVarChar).Value = BomName;
                cmd.Parameters.Add("@CompanyName", SqlDbType.NVarChar).Value = CompanyName;
                cmd.Parameters.Add("@ContactName", SqlDbType.NVarChar).Value = ContactName;
                cmd.Parameters.Add("@SalesmanId", SqlDbType.Int).Value = SalesmanId;
                cmd.Parameters.Add("@CompanyId", SqlDbType.Int).Value = CompanyId;
                cmd.Parameters.Add("@ContactId", SqlDbType.Int).Value = ContactId;
                cmd.Parameters.Add("@PartWatch", SqlDbType.Bit).Value = PartWatch;
                cmd.Parameters.Add("@DefaultCurrencyId", SqlDbType.Int).Value = DefaultCurrencyId;
                cmd.Parameters.Add("@OverRideCurrency", SqlDbType.Bit).Value = OverRideCurrency;
                cmd.Parameters.Add("@ImportOrHubRFQ", SqlDbType.NVarChar).Value = SaveImportOrHubRFQ;
                cmd.Parameters.Add("@ReqforTraceabilityId", SqlDbType.Int).Value = ReqforTraceabilityId;
                cmd.Parameters.Add("@TypeId", SqlDbType.Int).Value = TypeId;
                cmd.Parameters.Add("@DateRequired", SqlDbType.DateTime).Value = DateRequired;
                cmd.Parameters.Add("@NewBomID", SqlDbType.Int).Direction = ParameterDirection.Output;
                cmd.Parameters.Add("@NewBomCode", SqlDbType.VarChar, 100).Direction = ParameterDirection.Output;

                cn.Open();
                //return ExecuteNonQuery(cmd);
                ExecuteNonQuery(cmd);
                errorMessage = Convert.ToString(cmd.Parameters["@ErrorMessage"].Value);
                NewBomCode = Convert.ToString(cmd.Parameters["@NewBomCode"].Value);
                string recorddetails = "NewBomCode :" + NewBomCode + ",RecordCount:" + (Int32)cmd.Parameters["@RecordCount"].Value + ",NewBomid:" + NewBomid;
                return recorddetails;


            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to insert mapping Column detail", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        /// <summary>
        /// Update BOM Status to Close
        /// Calls [usp_update_BOMStatusToClosed]
        /// </summary>
        public override DataTable UpdateBOMStatusToClosed(System.Int32? bomId, System.Int32? updatedBy, System.Int32? bomStatus)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_update_BOMManagerStatusToClosed", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@BOMManagerId", SqlDbType.Int).Value = bomId;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@BOMManagerStatus", SqlDbType.Int).Value = bomStatus;
                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                DataSet ds = new DataSet();
                da.Fill(ds);
                return ds.Tables[0];

                //int ret = ExecuteNonQuery(cmd);
                //return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to update BOM Status", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// Source 
        /// Calls [[usp_ipobom_source_Offer]]
        /// 
        /// </summary>
        public override List<OfferDetails> IPOBOMAutoSource(int? clientId, string partSearch, int? index, DateTime? startDate, DateTime? endDate, out DateTime? outDate, bool hasServerLocal, int? BOM, int? CallType, int? customerRequirementId, int curPage, int Rpp)
        {
            outDate = null;

            if (!string.IsNullOrWhiteSpace(partSearch))
            {
                partSearch = $"{partSearch}%";
            }

            try
            {
                using (var cn = new SqlConnection(hasServerLocal ? this.ConnectionString : this.GTConnectionString))
                using (var cmd = new SqlCommand("usp_IPOBOM_Source_OfferPH_BOMManager", cn))
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandTimeout = 180;

                    cmd.Parameters.AddWithValue("@ClientId", (object)clientId ?? DBNull.Value);
                    cmd.Parameters.AddWithValue("@PartSearch", partSearch);
                    cmd.Parameters.AddWithValue("@Index", (object)index ?? DBNull.Value);
                    cmd.Parameters.AddWithValue("@StartDate", (object)startDate ?? DBNull.Value);
                    cmd.Parameters.AddWithValue("@FinishDate", (object)endDate ?? DBNull.Value);
                    cmd.Parameters.AddWithValue("@curPage", curPage);
                    cmd.Parameters.AddWithValue("@Rpp", Rpp);
                    cmd.Parameters.AddWithValue("@BOMManagerNo", SqlDbType.Int).Value = BOM;
                    cmd.Parameters.AddWithValue("@CustomerRequirementId", SqlDbType.Int).Value = customerRequirementId;

                    cn.Open();

                    using (var reader = cmd.ExecuteReader())
                    {
                        var lst = new List<OfferDetails>();

                        while (reader.Read())
                        {
                            var obj = new OfferDetails
                            {
                                OfferId = GetReaderValue_Int32(reader, "OfferId", 0),
                                FullPart = GetReaderValue_String(reader, "FullPart", ""),
                                CustomerRequirementId = GetReaderValue_NullableInt32(reader, "CustomerRequirementId", null),
                                Part = GetReaderValue_String(reader, "Part", ""),
                                ManufacturerNo = GetReaderValue_NullableInt32(reader, "ManufacturerNo", null),
                                DateCode = GetReaderValue_String(reader, "DateCode", ""),
                                ProductNo = GetReaderValue_NullableInt32(reader, "ProductNo", null),
                                PackageNo = GetReaderValue_NullableInt32(reader, "PackageNo", null),
                                Quantity = GetReaderValue_Int32(reader, "Quantity", 0),
                                Price = GetReaderValue_Double(reader, "Price", 0),
                                OriginalEntryDate = GetReaderValue_NullableDateTime(reader, "OriginalEntryDate", null),
                                Salesman = GetReaderValue_NullableInt32(reader, "Salesman", null),
                                SupplierNo = GetReaderValue_Int32(reader, "SupplierNo", 0),
                                CurrencyNo = GetReaderValue_NullableInt32(reader, "CurrencyNo", null),
                                ROHS = GetReaderValue_NullableByte(reader, "ROHS", (byte)0),
                                UpdatedBy = GetReaderValue_NullableInt32(reader, "UpdatedBy", null),
                                DLUP = GetReaderValue_DateTime(reader, "DLUP", DateTime.MinValue),
                                OfferStatusNo = GetReaderValue_NullableInt32(reader, "OfferStatusNo", null),
                                OfferStatusChangeDate = GetReaderValue_NullableDateTime(reader, "OfferStatusChangeDate", null),
                                OfferStatusChangeLoginNo = GetReaderValue_NullableInt32(reader, "OfferStatusChangeLoginNo", null),
                                SupplierName = GetReaderValue_String(reader, "SupplierName", ""),
                                Notes = GetReaderValue_String(reader, "Notes", ""),
                                ManufacturerName = GetReaderValue_String(reader, "ManufacturerName", ""),
                                ProductName = GetReaderValue_String(reader, "ProductName", ""),
                                PackageName = GetReaderValue_String(reader, "PackageName", ""),
                                ClientNo = GetReaderValue_NullableInt32(reader, "ClientNo", null),
                                ManufacturerCode = GetReaderValue_String(reader, "ManufacturerCode", ""),
                                CurrencyCode = GetReaderValue_String(reader, "CurrencyCode", ""),
                                CurrencyDescription = GetReaderValue_String(reader, "CurrencyDescription", ""),
                                SupplierEmail = GetReaderValue_String(reader, "SupplierEmail", ""),
                                SalesmanName = GetReaderValue_String(reader, "SalesmanName", ""),
                                OfferStatusChangeEmployeeName = GetReaderValue_String(reader, "OfferStatusChangeEmployeeName", ""),
                                ClientId = GetReaderValue_Int32(reader, "ClientId", 0),
                                ClientName = GetReaderValue_String(reader, "ClientName", ""),
                                ClientDataVisibleToOthers = GetReaderValue_NullableBoolean(reader, "ClientDataVisibleToOthers", null),
                                SupplierType = GetReaderValue_String(reader, "SupplierType", ""),
                                ClientCode = GetReaderValue_String(reader, "ClientCode", ""),
                                MSL = GetReaderValue_String(reader, "MSL", ""),
                                SPQ = GetReaderValue_String(reader, "SPQ", ""),
                                LeadTime = GetReaderValue_String(reader, "LeadTime", ""),
                                RoHSStatus = GetReaderValue_String(reader, "RoHSStatus", ""),
                                FactorySealed = GetReaderValue_String(reader, "FactorySealed", ""),
                                IPOBOMNo = GetReaderValue_Int32(reader, "IPOBOMNo", 0),
                                SupplierTotalQSA = GetReaderValue_String(reader, "SupplierTotalQSA", ""),
                                SupplierLTB = GetReaderValue_String(reader, "SupplierLTB", ""),
                                SupplierMOQ = GetReaderValue_String(reader, "SupplierMOQ", ""),
                                IsSourcingHub = GetReaderValue_Boolean(reader, "IsSourcingHub", false),
                                SupplierMessage = GetReaderValue_String(reader, "SupplierMessage", ""),
                                TotalCount = GetReaderValue_NullableInt32(reader, "TotalRecords", 0),
                                REQStatus = GetReaderValue_Int32(reader, "REQStatus", 0),
                                OfferAddFlag = GetReaderValue_Boolean(reader, "OfferAddFlag", false)
                            };

                            lst.Add(obj);
                        }

                        if (reader.NextResult())
                        {
                            while (reader.Read())
                            {
                                outDate = GetReaderValue_NullableDateTime(reader, "OutPutDate", null);
                            }
                        }

                        return lst;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception("Error executing IPOBOMAutoSource", ex);
            }
        }

        public override List<AutoSourcing> GetAutoSourcingResult(System.Int32? BOM, int? CustomerReqID, int curPage, int Rpp)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("Usp_AutoSourcing", cn)
                {
                    CommandType = CommandType.StoredProcedure,
                    CommandTimeout = 180
                };
                cmd.Parameters.Add("@BOMManagerNo", SqlDbType.Int).Value = BOM;
                cmd.Parameters.Add("@curPage", SqlDbType.Int).Value = curPage;
                cmd.Parameters.Add("@Rpp", SqlDbType.Int).Value = Rpp;
                cmd.Parameters.Add("@CustomerRequirementID", SqlDbType.Int).Value = CustomerReqID;
                cn.Open();
                SqlDataReader reader = cmd.ExecuteReader();
                List<AutoSourcing> lst = new List<AutoSourcing>();
                while (reader.Read())
                {
                    AutoSourcing obj = new AutoSourcing();
                    obj.OfferId = GetReaderValue_Int32(reader, "OfferId", 0);
                    obj.SourceId = GetReaderValue_Int32(reader, "SourceId", 0);
                    obj.FullPart = GetReaderValue_String(reader, "FullPart", "");
                    obj.Part = GetReaderValue_String(reader, "Part", "");
                    obj.ManufacturerNo = GetReaderValue_NullableInt32(reader, "ManufacturerNo", null);
                    obj.ManufacturerName = GetReaderValue_String(reader, "ManufacturerName", "");
                    obj.DateCode = GetReaderValue_String(reader, "DateCode", "");
                    obj.ProductNo = GetReaderValue_NullableInt32(reader, "ProductNo", null);
                    obj.PackageNo = GetReaderValue_NullableInt32(reader, "PackageNo", null);
                    obj.ProductName = GetReaderValue_String(reader, "ProductName", "");
                    obj.PackageName = GetReaderValue_String(reader, "PackageName", "");
                    obj.Quantity = GetReaderValue_Int32(reader, "Quantity", 0);
                    obj.Resale = GetReaderValue_Double(reader, "Resale", 0);
                    obj.Cost = GetReaderValue_Double(reader, "Cost", 0);
                    obj.Profit = GetReaderValue_Double(reader, "Profit", 0);
                    obj.Margin = GetReaderValue_Double(reader, "Margin", 0);
                    obj.OriginalEntryDate = GetReaderValue_NullableDateTime(reader, "DLUP", null);
                    obj.Salesman = GetReaderValue_NullableInt32(reader, "Salesman", null);
                    obj.SupplierNo = GetReaderValue_Int32(reader, "SupplierNo", 0);
                    obj.CurrencyNo = GetReaderValue_NullableInt32(reader, "CurrencyNo", null);
                    obj.ROHS = GetReaderValue_Byte(reader, "ROHS", (byte)0);
                    obj.ROHSDescription = GetReaderValue_String(reader, "ROHSDescription", "");
                    obj.UpdatedBy = GetReaderValue_NullableInt32(reader, "UpdatedBy", null);
                    obj.VendorName = GetReaderValue_String(reader, "VendorName", "");
                    obj.VendorCategory = GetReaderValue_String(reader, "VendorCategory", ""); ;
                    obj.Notes = GetReaderValue_String(reader, "Notes", "");
                    obj.CustomerRequirementId = GetReaderValue_NullableInt32(reader, "CustomerRequirementId", null);
                    obj.CurrencyCode = GetReaderValue_String(reader, "CurrencyCode", "");
                    obj.MSL = GetReaderValue_String(reader, "MSL", "");
                    obj.MOQ = GetReaderValue_String(reader, "MOQ", "");
                    obj.SPQ = GetReaderValue_String(reader, "SPQ", "");
                    obj.ADJQty = GetReaderValue_NullableInt32(reader, "ADJQty", null);
                    obj.LT = GetReaderValue_String(reader, "LT", "");
                    obj.Reason = GetReaderValue_String(reader, "Reason", "");
                    obj.REQStatus = GetReaderValue_Int32(reader, "REQStatus", -1);
                    obj.ItemReleased = GetReaderValue_Boolean(reader, "ItemReleased", false);
                    obj.TotalRecords = GetReaderValue_Int32(reader, "TotalRecords", 0);
                    obj.AlternateOfferFlag = GetReaderValue_Boolean(reader, "AlternateOfferFlag", false);
                    obj.SupplierWarranty = GetReaderValue_Int32(reader, "SupplierWarranty", 0);
                    obj.IsTestingRecommended = GetReaderValue_Boolean(reader, "TestRecommended", false);
                    obj.CountryOfOriginNo = GetReaderValue_Int32(reader, "IHSCountryOfOriginNo", -1);
                    obj.CountryOfOrigin = GetReaderValue_String(reader, "CountryOfOrigin", "");
                    obj.SupplierTotalQSA = GetReaderValue_String(reader, "SupplierTotalQSA", "");
                    obj.SupplierLTB = GetReaderValue_String(reader, "SupplierLTB", "");
                    obj.IsImageAvailable = GetReaderValue_Boolean(reader, "IsImageAvailable", false);
                    obj.FactorySealed = GetReaderValue_String(reader, "FactorySealed", "");
                    obj.DeliveryDate = GetReaderValue_NullableDateTime(reader, "DeliveryDate", null);
                    obj.ROHSStatus = GetReaderValue_String(reader, "ROHSStatus", "");
                    obj.RegionNo = GetReaderValue_Int32(reader, "RegionNo", -1);
                    obj.MSLLevelNo = GetReaderValue_Int32(reader, "MSLLevelNo", -1);
                    obj.OfferStatusId = GetReaderValue_Int32(reader, "OfferStatusNo", -1);
                    obj.ShippingCost = GetReaderValue_Int32(reader, "EstimatedShippingCost", 0);

                    lst.Add(obj);
                    obj = null;
                }


                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Offers", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override DataTable UpdateAutoSourcing(int BOM,
                                                     int SourceId,
                                                     int? SPQ,
                                                     double ReSell,
                                                     double Cost,
                                                     int EditMfrId,
                                                     int EditProdId,
                                                     string Reason,
                                                     string part,
                                                     int? rohs,
                                                     int? countryOfOrigin,
                                                     string dateCode,
                                                     int? packageNo,
                                                     int quantity,
                                                     int? offerStatusNo,
                                                     string factorySealed,
                                                     int? mslNo,
                                                     string totalQSA,
                                                     string moq,
                                                     string ltb,
                                                     int currencyNo,
                                                     double? shippingCost,
                                                     string leadTime,
                                                     int? regionNo,
                                                     DateTime? deliveryDate,
                                                     int? supplierWarranty,
                                                     string rohsStatus,
                                                     string notes,
                                                     bool? isTestingRecommened,
                                                     int? UpdatedBy)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            SqlDataAdapter da = null;
            DataSet ds = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("Usp_UpdateAutoSourcing", cn)
                {
                    CommandType = CommandType.StoredProcedure,
                    CommandTimeout = 180
                };
                cmd.Parameters.Add("@BOMManagerNo", SqlDbType.Int).Value = BOM;
                cmd.Parameters.Add("@SourceId", SqlDbType.Int).Value = SourceId;
                cmd.Parameters.Add("@SPQ", SqlDbType.Int).Value = SPQ;
                cmd.Parameters.Add("@ReSell", SqlDbType.Decimal).Value = ReSell;
                cmd.Parameters.Add("@Cost", SqlDbType.Decimal).Value = Cost;
                cmd.Parameters.Add("@Reason", SqlDbType.NVarChar).Value = Reason;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = UpdatedBy;
                cmd.Parameters.Add("@EditMfrId", SqlDbType.Int).Value = EditMfrId;
                cmd.Parameters.Add("@EditProdId", SqlDbType.Int).Value = EditProdId;
                cmd.Parameters.Add("@Part", SqlDbType.NVarChar).Value = part;
                cmd.Parameters.Add("@ROHS", SqlDbType.TinyInt).Value = rohs;
                cmd.Parameters.Add("@CountryOfOriginNo", SqlDbType.Int).Value = countryOfOrigin;
                cmd.Parameters.Add("@DateCode", SqlDbType.NVarChar).Value = dateCode;
                cmd.Parameters.Add("@PackageNo", SqlDbType.Int).Value = packageNo;
                cmd.Parameters.Add("@Quantity", SqlDbType.Int).Value = quantity;
                cmd.Parameters.Add("@OfferStatusNo", SqlDbType.Int).Value = offerStatusNo;
                cmd.Parameters.Add("@FactorySealed", SqlDbType.NVarChar).Value = factorySealed;
                cmd.Parameters.Add("@MSLNo", SqlDbType.Int).Value = mslNo;
                cmd.Parameters.Add("@SupplierTotalQSA", SqlDbType.NVarChar).Value = totalQSA;
                cmd.Parameters.Add("@SupplierMOQ", SqlDbType.NVarChar).Value = moq;
                cmd.Parameters.Add("@SupplierLTB", SqlDbType.NVarChar).Value = ltb;
                cmd.Parameters.Add("@CurrencyNo", SqlDbType.Int).Value = currencyNo;
                cmd.Parameters.Add("@ShippingCost", SqlDbType.Decimal).Value = shippingCost;
                cmd.Parameters.Add("@LeadTime", SqlDbType.NVarChar).Value = leadTime;
                cmd.Parameters.Add("@RegionNo", SqlDbType.Int).Value = regionNo;
                cmd.Parameters.Add("@DeliveryDate", SqlDbType.DateTime).Value = deliveryDate;
                cmd.Parameters.Add("@SupplierWarranty", SqlDbType.Int).Value = supplierWarranty;
                cmd.Parameters.Add("@RoHSStatus", SqlDbType.NVarChar).Value = rohsStatus;
                cmd.Parameters.Add("@Notes", SqlDbType.NVarChar).Value = notes;
                cmd.Parameters.Add("@TestingRecommended", SqlDbType.Bit).Value = isTestingRecommened;

                cn.Open();
                da = new SqlDataAdapter(cmd);
                ds = new DataSet();
                da.Fill(ds);
                return ds.Tables[0];

            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Offers", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
                da.Dispose();
                ds.Dispose();
            }
        }

        public override DataTable UpdateReasonForAutoSourcing(System.Int32? BOM, System.Int32? SourceId, string Reason, int? UpdatedBy)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            SqlDataAdapter da = null;
            DataSet ds = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("Usp_Update_Reason_For_AutoSourcing", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 180;
                cmd.Parameters.Add("@BOMManagerNo", SqlDbType.Int).Value = BOM;
                cmd.Parameters.Add("@SourceId", SqlDbType.Int).Value = SourceId;
                cmd.Parameters.Add("@Reason", SqlDbType.NVarChar).Value = Reason;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = UpdatedBy;
                cn.Open();
                da = new SqlDataAdapter(cmd);
                ds = new DataSet();
                da.Fill(ds);
                return ds.Tables[0];

            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Offers", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
                da.Dispose();
                ds.Dispose();
            }
        }
        public override DataTable ReplaceSourcingBOMManager(System.Int32 BOM, System.Int32 CustomerRequirementId, System.Int32 UpdatedBy, System.Int32 ReplaceSourceType)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            SqlDataAdapter da = null;
            DataSet ds = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("Usp_ReplaceSourcingBOMManager", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 180;
                cmd.Parameters.Add("@BOMManagerNo", SqlDbType.Int).Value = BOM;
                cmd.Parameters.Add("@CustomerRequirementId", SqlDbType.Int).Value = CustomerRequirementId;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = UpdatedBy;
                cmd.Parameters.Add("@ReplaceSourceType", SqlDbType.Int).Value = ReplaceSourceType;
                cn.Open();
                da = new SqlDataAdapter(cmd);
                ds = new DataSet();
                da.Fill(ds);
                return ds.Tables[0];

            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to replace EMS offer.", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
                da.Dispose();
                ds.Dispose();
            }
        }
        public override DataTable RemoveOfferBOMManager(System.Int32 BOM, System.Int32 SourceId, System.Int32 UpdatedBy)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            SqlDataAdapter da = null;
            DataSet ds = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("Usp_RemoveOfferBOMManager", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 180;
                cmd.Parameters.Add("@BOMManagerNo", SqlDbType.Int).Value = BOM;
                cmd.Parameters.Add("@SourceId", SqlDbType.Int).Value = SourceId;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = UpdatedBy;
                //cmd.Parameters.Add("@ReplaceSourceType", SqlDbType.Int).Value = ReplaceSourceType;
                cn.Open();
                da = new SqlDataAdapter(cmd);
                ds = new DataSet();
                da.Fill(ds);
                return ds.Tables[0];

            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to replace EMS offer.", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
                da.Dispose();
                ds.Dispose();
            }
        }
        public override DataTable AddNewOffer(int BOM,
                                              int sourceId,
                                              int CustomerRequirementId,
                                              int UpdatedBy,
                                              int ReplaceSourceType,
                                              string OfferSource)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            SqlDataAdapter da = null;
            DataSet ds = null;
            try
            {

                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("USP_AddOfferBomManager", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 180;
                cmd.Parameters.Add("@BOMManagerNo", SqlDbType.Int).Value = BOM;
                cmd.Parameters.Add("@SourcingId", SqlDbType.Int).Value = sourceId;
                cmd.Parameters.Add("@CustomerRequirementId", SqlDbType.Int).Value = CustomerRequirementId;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = UpdatedBy;
                cmd.Parameters.Add("@SourceType", SqlDbType.Int).Value = ReplaceSourceType;
                cmd.Parameters.Add("@OfferSource", SqlDbType.NVarChar).Value = OfferSource;
                cn.Open();
                da = new SqlDataAdapter(cmd);
                ds = new DataSet();
                da.Fill(ds);
                return ds.Tables[0];

            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to replace EMS offer.", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
                da.Dispose();
                ds.Dispose();
            }
        }
        public override DataTable Save_PrimarySourcing(System.Int32 ClientId, System.Int32 SourcingResultId, System.Int32 CustomerRequirementId, System.Int32 UpdatedBy)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            SqlDataAdapter da = null;
            DataSet ds = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("USP_Save_PrimarySourcing", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 180;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = ClientId;
                cmd.Parameters.Add("@SourcingResultId", SqlDbType.Int).Value = SourcingResultId;
                cmd.Parameters.Add("@CustomerRequirementId", SqlDbType.Int).Value = CustomerRequirementId;
                cmd.Parameters.Add("@UserId", SqlDbType.Int).Value = UpdatedBy;
                cn.Open();
                da = new SqlDataAdapter(cmd);
                ds = new DataSet();
                da.Fill(ds);
                return ds.Tables[0];

            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to Save Primary Sourcing.", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
                da.Dispose();
                ds.Dispose();
            }
        }
        public override DataTable Reset_PrimarySourcing(System.Int32 ClientId, System.Int32 SourcingResultId, System.Int32 CustomerRequirementId, System.Int32 UpdatedBy)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            SqlDataAdapter da = null;
            DataSet ds = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("USP_Reset_PrimarySourcing", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 180;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = ClientId;
                cmd.Parameters.Add("@SourcingResultId", SqlDbType.Int).Value = SourcingResultId;
                cmd.Parameters.Add("@CustomerRequirementId", SqlDbType.Int).Value = CustomerRequirementId;
                cmd.Parameters.Add("@UserId", SqlDbType.Int).Value = UpdatedBy;
                cn.Open();
                da = new SqlDataAdapter(cmd);
                ds = new DataSet();
                da.Fill(ds);
                return ds.Tables[0];

            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to Reset Primary Sourcing.", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
                da.Dispose();
                ds.Dispose();
            }
        }
        public override DataTable AssignUser(string BOMManagerids, System.Int32 AssignUserId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            SqlDataAdapter da = null;
            DataSet ds = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_AssignBomManager", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 180;
                cmd.Parameters.Add("@BOMMANAGERID", SqlDbType.VarChar).Value = BOMManagerids;
                cmd.Parameters.Add("@USERID", SqlDbType.Int).Value = AssignUserId;
                cn.Open();
                da = new SqlDataAdapter(cmd);
                ds = new DataSet();
                da.Fill(ds);
                return ds.Tables[0];

            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to Assign User for BOM Manager.", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
                da.Dispose();
                ds.Dispose();
            }
        }

        public override DataTable GetBOMManagerStatus(System.Int32 BOM)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            SqlDataAdapter da = null;
            DataSet ds = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("USP_GET_BOMSTATUS", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 180;
                cmd.Parameters.Add("@BOMManagerId", SqlDbType.Int).Value = BOM;
                cn.Open();
                da = new SqlDataAdapter(cmd);
                ds = new DataSet();
                da.Fill(ds);
                return ds.Tables[0];

            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to replace EMS offer.", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
                da.Dispose();
                ds.Dispose();
            }
        }

        /// <summary>
        /// GetListReadyForClient 
        /// Calls [usp_selectAll_BOM]
        /// </summary>
        public override List<BOMManager> GetBomManagerList(System.Int32? clientId, System.Boolean? isPoHUB, System.Int32? topToSelect, System.Int32? bomStatus, System.Int32? updatedBy)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_selectAll_BOMManager", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 50;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@TopToSelect", SqlDbType.Int).Value = topToSelect;
                cmd.Parameters.Add("@IsPoHUB", SqlDbType.Bit).Value = isPoHUB;
                cmd.Parameters.Add("@BOMStatus", SqlDbType.Int).Value = bomStatus;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<BOMManager> lst = new List<BOMManager>();
                while (reader.Read())
                {
                    BOMManager obj = new BOMManager();
                    obj.BOMManagerId = GetReaderValue_Int32(reader, "BOMManagerId", 0);
                    obj.BOMManagerCode = GetReaderValue_String(reader, "BOMManagerCode", "");
                    obj.BOMManagerName = GetReaderValue_String(reader, "BOMManagerName", "");
                    obj.ClientName = GetReaderValue_String(reader, "ClientName", "");
                    obj.CompanyName = GetReaderValue_String(reader, "CompanyName", "");
                    obj.DLUP = GetReaderValue_DateTime(reader, "DLUP", DateTime.MinValue);
                    obj.StatusValue = GetReaderValue_Int32(reader, "StatusValue", 0);
                    obj.RequestToPOHubBy = GetReaderValue_Int32(reader, "RequestToPOHubBy", 0);
                    obj.UpdatedBy = GetReaderValue_Int32(reader, "UpdatedBy", 0);
                    obj.QuoteRequired = GetReaderValue_NullableDateTime(reader, "QuoteRequired", null);
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get BOMManager", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override List<SourcingResultDetails> GetListForBOMManagerReleaseAll(System.Int32? BOMManagerID, System.Boolean isPOHub, int reqStatus)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_selectAllBOMManager_SourcingResult_for_Items", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@BOMManagerID", SqlDbType.Int).Value = BOMManagerID;
                cmd.Parameters.Add("@IsPoHub", SqlDbType.Bit).Value = isPOHub;
                cmd.Parameters.Add("@ItemsFor", SqlDbType.Int).Value = reqStatus;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<SourcingResultDetails> lst = new List<SourcingResultDetails>();
                while (reader.Read())
                {
                    SourcingResultDetails obj = new SourcingResultDetails();
                    obj.SourcingResultId = GetReaderValue_Int32(reader, "SourcingResultId", 0);
                    obj.CustomerRequirementNo = GetReaderValue_Int32(reader, "CustomerRequirementNo", 0);
                    obj.FullPart = GetReaderValue_String(reader, "FullPart", "");
                    obj.Part = GetReaderValue_String(reader, "Part", "");
                    obj.ProductNo = GetReaderValue_NullableInt32(reader, "ProductNo", null);
                    obj.PackageNo = GetReaderValue_NullableInt32(reader, "PackageNo", null);
                    obj.Quantity = GetReaderValue_Int32(reader, "Quantity", 0);
                    obj.Price = GetReaderValue_Double(reader, "Price", 0);
                    obj.CurrencyCode = GetReaderValue_String(reader, "CurrencyCode", "");
                    obj.DeliveryDate = GetReaderValue_NullableDateTime(reader, "DeliveryDate", null);
                    obj.OriginalPrice = GetReaderValue_NullableDouble(reader, "BuyPrice", 0);
                    obj.ActualCurrencyCode = GetReaderValue_String(reader, "BuyCurrencyCode", "");
                    obj.ActualCurrencyNo = GetReaderValue_Int32(reader, "BuyCurrencyNo", -1);
                    obj.Status1 = GetReaderValue_NullableBoolean(reader, "Status1", null);
                    obj.Status2 = GetReaderValue_NullableBoolean(reader, "Status2", null);
                    obj.CustomerRequirementNumber = GetReaderValue_Int32(reader, "CustomerRequirementNumber", 0);
                    obj.SupplierName = GetReaderValue_String(reader, "VendorName", "");
                    obj.HeaderFlag = GetReaderValue_NullableBoolean(reader, "HeaderFlag", null);
                    obj.LineNumber = GetReaderValue_Int32(reader, "LineNumber", 0);
                    obj.SourceId = GetReaderValue_Int32(reader, "sourceid", 0);
                    obj.MissingRequiredFeilds = GetReaderValue_NullableBoolean(reader, "MissingRequiredFeilds", null);
                    obj.ReleaseNote = GetReaderValue_String(reader, "ReleaseNote", "");
                    obj.Reason = GetReaderValue_String(reader, "Reason", "");
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get BOMManager SourcingResults", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override List<String> GetUnsourcedParts(System.Int32? BOMManagerID)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_selectAllBOMManager_UnSourcedParts", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@BOMManagerID", SqlDbType.Int).Value = BOMManagerID;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<String> lst = new List<String>();
                while (reader.Read())
                {
                    lst.Add(GetReaderValue_String(reader, "FullPart", ""));
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get BOMManager UnSourced Parts", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override DataTable BOMManagerReleaseRequirement(System.Int32? BOMManagerID, System.Int32? updatedBy, System.String CIds, System.Int32? ReqType, System.String NoBidNotes, System.String ASIds)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_update_BOMManager_Items_ByHub", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@BOMManagerID", SqlDbType.Int).Value = BOMManagerID;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@CustomerRequirementIds", SqlDbType.VarChar).Value = CIds;
                cmd.Parameters.Add("@ReqType", SqlDbType.Int).Value = ReqType;
                cmd.Parameters.Add("@NoBidNotes", SqlDbType.NVarChar).Value = NoBidNotes;
                cmd.Parameters.Add("@AutoSourceId", SqlDbType.NVarChar).Value = ASIds;
                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                ExecuteNonQuery(cmd);
                //int ret = 1;
                //return (ret > 0);
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                DataSet ds = new DataSet();
                da.Fill(ds);
                return ds.Tables[0];
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to update BOMManager CustomerRequirement", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        /// <summary>
        /// Update release note for customer requirement
        /// </summary>
        /// <param name="CustomerRequirementID">ID of the customer requirement</param>
        /// <param name="ReleaseNote">release note to update</param>
        /// <returns></returns>
        public override DataTable BOMManagerUpdateCustomerRequirementReleaseNotes(System.Int32 CustomerRequirementID, System.String ReleaseNote)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_update_BOMManager_Items_CustomerR_ReleaseNotes", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@CustomerRequirementId", SqlDbType.Int).Value = CustomerRequirementID;
                cmd.Parameters.Add("@ReleaseNote", SqlDbType.VarChar).Value = ReleaseNote;
                cn.Open();
                ExecuteNonQuery(cmd);

                SqlDataAdapter da = new SqlDataAdapter(cmd);
                DataSet ds = new DataSet();
                da.Fill(ds);
                return ds.Tables[0];
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to update BOMManager CustomerRequirement", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override List<AutoSourcing> GetBOMManagerAutoSourcing(System.Int32 BOMManagerId, bool IsPOHUB, int? CustomerReqID, int curPage, int Rpp)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);

                cmd = new SqlCommand("usp_selectAll_AutoSourcingResult_for_BOMManagerID", cn)
                {
                    CommandType = CommandType.StoredProcedure,
                    CommandTimeout = 180
                };
                cmd.Parameters.Add("@BOMManagerID", SqlDbType.Int).Value = BOMManagerId;
                cmd.Parameters.Add("@CustomerReqID", SqlDbType.Int).Value = CustomerReqID;
                cmd.Parameters.Add("@IsPoHUB", SqlDbType.Bit).Value = IsPOHUB;
                cmd.Parameters.Add("@curPage", SqlDbType.Int).Value = curPage;
                cmd.Parameters.Add("@Rpp", SqlDbType.Int).Value = Rpp;
                cn.Open();

                //DbDataReader reader = ExecuteReader(cmd);
                SqlDataReader reader = cmd.ExecuteReader();
                List<AutoSourcing> lst = new List<AutoSourcing>();
                while (reader.Read())
                {
                    AutoSourcing obj = new AutoSourcing();
                    obj.SourceId = GetReaderValue_Int32(reader, "SourcingResultId", 0);
                    obj.CustomerRequirementId = GetReaderValue_NullableInt32(reader, "CustomerRequirementId", null);
                    obj.FullPart = GetReaderValue_String(reader, "FullPart", "");
                    obj.Part = GetReaderValue_String(reader, "Part", "");
                    obj.ManufacturerNo = GetReaderValue_NullableInt32(reader, "ManufacturerNo", null);
                    obj.DateCode = GetReaderValue_String(reader, "DateCode", "");
                    obj.ProductNo = GetReaderValue_NullableInt32(reader, "ProductNo", null);
                    obj.PackageNo = GetReaderValue_NullableInt32(reader, "PackageNo", null);
                    obj.Quantity = GetReaderValue_Int32(reader, "Quantity", 0);
                    obj.UnitPrice = GetReaderValue_Double(reader, "UnitPrice", 0);
                    obj.CurrencyNo = GetReaderValue_NullableInt32(reader, "CurrencyNo", null);
                    obj.DLUP = GetReaderValue_NullableDateTime(reader, "DLUP", null);
                    obj.DeliveryDate = GetReaderValue_NullableDateTime(reader, "DeliveryDate", null);
                    obj.Salesman = GetReaderValue_NullableInt32(reader, "Salesman", null);
                    obj.SupplierNo = GetReaderValue_Int32(reader, "SupplierNo", 0);
                    obj.ROHS = GetReaderValue_Byte(reader, "ROHS", (byte)0);
                    obj.SupplierName = GetReaderValue_String(reader, "SupplierName", "");
                    obj.ManufacturerName = GetReaderValue_String(reader, "ManufacturerName", "");
                    obj.Notes = GetReaderValue_String(reader, "Notes", "");
                    obj.ManufacturerName = GetReaderValue_String(reader, "ManufacturerName", "");
                    obj.ProductName = GetReaderValue_String(reader, "ProductName", "");
                    obj.PackageName = GetReaderValue_String(reader, "PackageName", "");
                    obj.ClientNo = GetReaderValue_NullableInt32(reader, "ClientNo", null);
                    obj.ClientCurrencyNo = GetReaderValue_NullableInt32(reader, "ClientCurrencyNo", null);
                    obj.ClientCurrencyCode = GetReaderValue_String(reader, "ClientCurrencyCode", "");
                    obj.CurrencyCode = GetReaderValue_String(reader, "CurrencyCode", "");
                    obj.SalesmanName = GetReaderValue_String(reader, "SalesmanName", "");
                    obj.OfferDate = GetReaderValue_NullableDateTime(reader, "OfferDate", null);
                    obj.ADJQty = GetReaderValue_NullableInt32(reader, "ADJQty", null);
                    obj.BOMStatus = GetReaderValue_Int32(reader, "REQStatus", -1);
                    obj.QuoteID = GetReaderValue_NullableInt32(reader, "QuoteID", null);
                    obj.QuoteNumber = GetReaderValue_NullableInt32(reader, "QuoteNumber", null);
                    obj.UpliftPercentage = GetReaderValue_Double(reader, "UpliftPercentage", 0);
                    obj.IsPrimarySource = GetReaderValue_NullableBoolean(reader, "IsPrimarySourcing", null);
                    obj.IsPrimarySourceActual = GetReaderValue_NullableBoolean(reader, "IsPrimarySourcing", null);
                    obj.TotalRecords = GetReaderValue_Int32(reader, "TotalRecords", 0);
                    obj.SupplierType = GetReaderValue_String(reader, "SupplierType", "");
                    obj.Region = GetReaderValue_String(reader, "RegionName", "");
                    obj.ActualPrice = GetReaderValue_Double(reader, "ActualPrice", 0);
                    obj.ActualCurrencyNo = GetReaderValue_Int32(reader, "ActualCurrencyNo", -1);
                    obj.ActualCurrencyCode = GetReaderValue_String(reader, "ActualCurrencyCode", "");
                    obj.Price = GetReaderValue_Double(reader, "Price", 0);
                    obj.CurrencyNo = GetReaderValue_Int32(reader, "CurrencyNo", -1);
                    obj.CurrencyCode = GetReaderValue_String(reader, "CurrencyCode", "");
                    obj.SupplierWarranty = GetReaderValue_Int32(reader, "SupplierWarranty", 0);
                    obj.IsTestingRecommended = GetReaderValue_Boolean(reader, "IsTestingRecommended", false);
                    obj.CountryOfOrigin = GetReaderValue_String(reader, "CountryOfOrigin", "");
                    obj.MOQ = GetReaderValue_String(reader, "SupplierMOQ", "");
                    obj.SupplierTotalQSA = GetReaderValue_String(reader, "SupplierTotalQSA", "");
                    obj.SupplierLTB = GetReaderValue_String(reader, "SupplierLTB", "");
                    obj.IsImageAvailable = GetReaderValue_NullableBoolean(reader, "IsImageAvailable", false);
                    obj.SPQ = GetReaderValue_String(reader, "SPQ", "");
                    obj.LeadTime = GetReaderValue_String(reader, "LeadTime", "");
                    obj.ROHSDescription = GetReaderValue_String(reader, "ROHSDescription", "");
                    obj.FactorySealed = GetReaderValue_String(reader, "FactorySealed", "");
                    obj.MSL = GetReaderValue_String(reader, "MSL", "");
                    obj.MSLLevelNo = GetReaderValue_Int32(reader, "MSLLevelNo", 0);
                    obj.MSLLevelText = GetReaderValue_String(reader, "MSLLevelText", "");
                    obj.SupplierType = GetReaderValue_String(reader, "SupplierType", "");

                    lst.Add(obj);
                    obj = null;
                }


                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Offers", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override bool EditSourcingResultsSalesman(System.Int32 CRID, Double UnitPrice, string Notes, System.Int32? updatedBy)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_update_SourcingResultsSalesman", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@CRID", SqlDbType.Int).Value = CRID;
                cmd.Parameters.Add("@Notes", SqlDbType.NVarChar).Value = Notes;
                cmd.Parameters.Add("@UnitPrice", SqlDbType.Decimal).Value = UnitPrice;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to update Sourcing Results", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override QuoteDetails LoadQuoteGenerationDataBOMManager(System.Int32 CustomerRequirementID, System.Int32 BOMManagerNo)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);


                cmd = new SqlCommand("USP_QuoteGenerationData_BOMManager", cn);


                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 180;
                cmd.Parameters.Add("@CustomerRequirementID", SqlDbType.Int).Value = CustomerRequirementID;
                cmd.Parameters.Add("@BOMManagerNo", SqlDbType.Int).Value = BOMManagerNo;
                //cmd.Parameters.Add("@IsPoHUB", SqlDbType.Bit).Value = isPOHub;
                cn.Open();
                //DbDataReader reader = ExecuteReader(cmd);
                SqlDataReader reader = cmd.ExecuteReader();
                QuoteDetails obj = new QuoteDetails();
                while (reader.Read())
                {
                    obj.ContactNo = GetReaderValue_Int32(reader, "ContactNo", -1);
                    obj.ContactName = GetReaderValue_String(reader, "ContactName", "");
                    obj.Salesman = GetReaderValue_Int32(reader, "Salesman", -1);
                    obj.SalesmanName = GetReaderValue_String(reader, "SalesmanName", "");
                    obj.CompanyNo = GetReaderValue_Int32(reader, "CompanyNo", -1);
                    obj.CompanyName = GetReaderValue_String(reader, "CompanyName", "");
                    obj.CurrencyNo = GetReaderValue_Int32(reader, "CurrencyNo", -1);
                    obj.CurrencyCode = GetReaderValue_String(reader, "CurrencyCode", "");
                    obj.DivisionNo = GetReaderValue_Int32(reader, "DivisionNo", -1);
                    obj.DivisionName = GetReaderValue_String(reader, "DivisionName", "");
                    obj.HeaderImageNameQuote = GetReaderValue_String(reader, "DocumentHeaderImageName", "");
                    obj.SourcingResultId = GetReaderValue_Int32(reader, "SourcingResultId", -1);
                }

                return obj;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to Load Quote Generation Data", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override Int32 InsertFromSourcingResultBOMManager(System.Int32? sourcingResultId, System.Int32? quoteNo, System.DateTime? dateQuoted, int BOMManagerID)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_insert_QuoteLine_from_SourcingResult_BOMManager", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@SourcingResultId", SqlDbType.Int).Value = sourcingResultId;
                cmd.Parameters.Add("@QuoteNo", SqlDbType.Int).Value = quoteNo;
                cmd.Parameters.Add("@DateQuoted", SqlDbType.DateTime).Value = dateQuoted;
                cmd.Parameters.Add("@BOMManagerID", SqlDbType.Int).Value = BOMManagerID;
                cmd.Parameters.Add("@QuoteLineId", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (Int32)cmd.Parameters["@QuoteLineId"].Value;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to insert QuoteLine", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override DataTable GetCustomTemplateData(int QuoteID)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            SqlDataAdapter da = null;
            DataSet ds = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("GetCustomTemplateData", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 180;
                cmd.Parameters.Add("@QuoteId", SqlDbType.Int).Value = QuoteID;
                cn.Open();
                da = new SqlDataAdapter(cmd);
                ds = new DataSet();
                da.Fill(ds);
                return ds.Tables[0];

            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Custom Template Data.", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
                da.Dispose();
                ds.Dispose();
            }
        }
        public override bool DeleteBOMManagerItem(int BOMManagerID, string CustReqIDs, int? UpdatedBy)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("USP_Delete_BOMManagerItem", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 180;
                cmd.Parameters.Add("@BOMManagerId", SqlDbType.Int).Value = BOMManagerID;
                cmd.Parameters.Add("@CustReqIDs", SqlDbType.NVarChar).Value = CustReqIDs.Substring(0, CustReqIDs.Length - 1);
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = UpdatedBy;
                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (ret > 0);

            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to delete BOM Item.", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override bool SaveEditBOMItemData(int customerRequirementId, int RequirementforTraceability, int salesman, int quantity, System.Int32? Usage, int Type, System.String EAU, int manufacturerNo, string customerPart, Double TargetSellPrice, int currencyNo, System.Byte? rohs, string dateCode, int productNo, System.Int32? PackageNo, string MSL, DateTime DatePromised, System.DateTime? CustomerDecisionDate, System.DateTime? RFQClosingDate, System.Int32? QuoteValidityRequired, string BOMManagerName, System.String notes, System.String instructions, System.Int32? SupportTeamMemberNo, int BOMManagerID, System.Int32? UpdatedBy)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("USP_Update_BOMManagerItem", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 180;
                cmd.Parameters.Add("@customerRequirementId", SqlDbType.Int).Value = customerRequirementId;
                cmd.Parameters.Add("@RequirementforTraceability", SqlDbType.Int).Value = RequirementforTraceability;
                cmd.Parameters.Add("@salesman", SqlDbType.Int).Value = salesman;
                cmd.Parameters.Add("@quantity", SqlDbType.Int).Value = quantity;
                cmd.Parameters.Add("@Usage", SqlDbType.Int).Value = Usage;
                cmd.Parameters.Add("@Type", SqlDbType.Int).Value = Type;
                cmd.Parameters.Add("@EAU", SqlDbType.NVarChar).Value = EAU;
                cmd.Parameters.Add("@manufacturerNo", SqlDbType.Int).Value = manufacturerNo;
                cmd.Parameters.Add("@customerPart", SqlDbType.NVarChar).Value = customerPart;
                cmd.Parameters.Add("@TargetSellPrice", SqlDbType.Float).Value = TargetSellPrice;
                cmd.Parameters.Add("@currencyNo", SqlDbType.Int).Value = currencyNo;
                cmd.Parameters.Add("@rohs", SqlDbType.Int).Value = rohs;
                cmd.Parameters.Add("@dateCode", SqlDbType.NVarChar).Value = dateCode;
                cmd.Parameters.Add("@productNo", SqlDbType.Int).Value = productNo;
                cmd.Parameters.Add("@PackageNo", SqlDbType.Int).Value = PackageNo;
                cmd.Parameters.Add("@MSL", SqlDbType.NVarChar).Value = MSL;
                cmd.Parameters.Add("@DatePromised", SqlDbType.DateTime).Value = DatePromised;
                cmd.Parameters.Add("@CustomerDecisionDate", SqlDbType.DateTime).Value = CustomerDecisionDate;
                cmd.Parameters.Add("@RFQClosingDate", SqlDbType.DateTime).Value = RFQClosingDate;
                cmd.Parameters.Add("@QuoteValidityRequired", SqlDbType.Int).Value = QuoteValidityRequired;
                cmd.Parameters.Add("@BOMManagerName", SqlDbType.NVarChar).Value = BOMManagerName;
                cmd.Parameters.Add("@notes", SqlDbType.NVarChar).Value = notes;
                cmd.Parameters.Add("@instructions", SqlDbType.NVarChar).Value = instructions;
                cmd.Parameters.Add("@SupportTeamMemberNo", SqlDbType.Int).Value = SupportTeamMemberNo;
                cmd.Parameters.Add("@BOMManagerID", SqlDbType.Int).Value = BOMManagerID;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = UpdatedBy;
                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (ret > 0);

            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to Update Edited BOM Item.", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override bool SaveUpliftAllPrice(int BOMManagerID, float UpliftPercentage, System.Int32? UpdatedBy)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("USP_UpliftPriceAll_BOMManager", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 180;
                cmd.Parameters.Add("@UpliftPercentage", SqlDbType.Decimal).Value = UpliftPercentage;
                cmd.Parameters.Add("@BOMManagerID", SqlDbType.Int).Value = BOMManagerID;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = UpdatedBy;
                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (ret > 0);

            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to Insert/Update Uplift All Percentage", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override DataTable GetUpliftPercentageAll(System.Int32 BOM)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            SqlDataAdapter da = null;
            DataSet ds = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("USP_GetUpliftPercentageAll", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 180;
                cmd.Parameters.Add("@BOMManagerId", SqlDbType.Int).Value = BOM;
                cn.Open();
                da = new SqlDataAdapter(cmd);
                ds = new DataSet();
                da.Fill(ds);
                return ds.Tables[0];

            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to Get Uplift Percentage.", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
                da.Dispose();
                ds.Dispose();
            }
        }
        public override bool RemoveUpliftPriceAll(int BOMManagerID, System.Int32? UpdatedBy)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("USP_RemoveUpliftPriceAll_BOMManager", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 180;
                cmd.Parameters.Add("@BOMManagerID", SqlDbType.Int).Value = BOMManagerID;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = UpdatedBy;
                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (ret > 0);

            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to Remove Uplift All Percentage", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override List<AutoSourcing> GetBOMManagerSourcingForUplift(System.Int32 BOMManagerId, System.String CustomerRequirementIds = "")
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);


                cmd = new SqlCommand("usp_GetBOMManagerSourcingForUplift_BOMManager", cn);


                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 180;
                cmd.Parameters.Add("@BOMManagerID", SqlDbType.Int).Value = BOMManagerId;
                cmd.Parameters.Add("@CustomerRequirementIds", SqlDbType.VarChar).Value = CustomerRequirementIds;

                cn.Open();

                //DbDataReader reader = ExecuteReader(cmd);
                SqlDataReader reader = cmd.ExecuteReader();
                List<AutoSourcing> lst = new List<AutoSourcing>();
                while (reader.Read())
                {
                    AutoSourcing obj = new AutoSourcing();
                    //obj.OfferId = GetReaderValue_Int32(reader, "OfferId", 0);
                    obj.SourceId = GetReaderValue_Int32(reader, "SourceId", 0);
                    obj.CustomerRequirementId = GetReaderValue_NullableInt32(reader, "CustomerRequirementId", null);
                    obj.FullPart = GetReaderValue_String(reader, "FullPart", "");
                    obj.Part = GetReaderValue_String(reader, "Part", "");
                    obj.ManufacturerNo = GetReaderValue_NullableInt32(reader, "ManufacturerNo", null);
                    obj.DateCode = GetReaderValue_String(reader, "DateCode", "");
                    obj.ProductNo = GetReaderValue_NullableInt32(reader, "ProductNo", null);
                    obj.PackageNo = GetReaderValue_NullableInt32(reader, "PackageNo", null);
                    obj.Quantity = GetReaderValue_Int32(reader, "Quantity", 0);
                    obj.UnitPrice = GetReaderValue_Double(reader, "UnitPrice", 0);
                    obj.CurrencyNo = GetReaderValue_NullableInt32(reader, "CurrencyNo", null);
                    //obj.Cost = GetReaderValue_Double(reader, "Cost", 0);
                    //obj.Profit = GetReaderValue_Double(reader, "Profit", 0);
                    //obj.Margin = GetReaderValue_Double(reader, "Margin", 0);
                    obj.DLUP = GetReaderValue_NullableDateTime(reader, "DLUP", null);
                    obj.DeliveryDate = GetReaderValue_NullableDateTime(reader, "DeliveryDate", null);
                    obj.Salesman = GetReaderValue_NullableInt32(reader, "Salesman", null);
                    //obj.SupplierNo = GetReaderValue_Int32(reader, "SupplierNo", 0);
                    obj.ROHS = GetReaderValue_Byte(reader, "ROHS", (byte)0);
                    //obj.UpdatedBy = GetReaderValue_NullableInt32(reader, "UpdatedBy", null);
                    //obj.VendorName = GetReaderValue_String(reader, "VendorName", "");
                    //obj.OfferStatusNo = GetReaderValue_NullableInt32(reader, "OfferStatusNo", null);
                    //obj.OfferStatusChangeDate = GetReaderValue_NullableDateTime(reader, "OfferStatusChangeDate", null);
                    //obj.OfferStatusChangeLoginNo = GetReaderValue_NullableInt32(reader, "OfferStatusChangeLoginNo", null);
                    obj.SupplierName = GetReaderValue_String(reader, "SupplierName", "");
                    obj.ManufacturerName = GetReaderValue_String(reader, "ManufacturerName", "");
                    obj.Notes = GetReaderValue_String(reader, "Notes", "");
                    obj.ManufacturerName = GetReaderValue_String(reader, "ManufacturerName", "");
                    obj.ProductName = GetReaderValue_String(reader, "ProductName", "");
                    obj.PackageName = GetReaderValue_String(reader, "PackageName", "");
                    obj.ClientNo = GetReaderValue_NullableInt32(reader, "ClientNo", null);
                    obj.ClientCurrencyNo = GetReaderValue_NullableInt32(reader, "ClientCurrencyNo", null);
                    obj.ClientCurrencyCode = GetReaderValue_String(reader, "ClientCurrencyCode", "");
                    //obj.ManufacturerCode = GetReaderValue_String(reader, "ManufacturerCode", "");
                    obj.CurrencyCode = GetReaderValue_String(reader, "CurrencyCode", "");
                    //obj.CurrencyDescription = GetReaderValue_String(reader, "CurrencyDescription", "");
                    //obj.SupplierEmail = GetReaderValue_String(reader, "SupplierEmail", "");
                    obj.SalesmanName = GetReaderValue_String(reader, "SalesmanName", "");
                    obj.OfferDate = GetReaderValue_NullableDateTime(reader, "OfferDate", null);
                    //obj.OfferStatusChangeEmployeeName = GetReaderValue_String(reader, "OfferStatusChangeEmployeeName", "");
                    //obj.ClientId = GetReaderValue_Int32(reader, "ClientId", 0);
                    //obj.ClientName = GetReaderValue_String(reader, "ClientName", "");
                    //obj.ClientDataVisibleToOthers = GetReaderValue_NullableBoolean(reader, "ClientDataVisibleToOthers", null);
                    ////[001] code start
                    //obj.SupplierType = GetReaderValue_String(reader, "SupplierType", "");
                    ////[001] code end
                    //obj.ClientCode = GetReaderValue_String(reader, "ClientCode", "");

                    //obj.MSL = GetReaderValue_String(reader, "MSL", "");
                    //obj.MOQ = GetReaderValue_String(reader, "MOQ", "");
                    //obj.SPQ = GetReaderValue_String(reader, "SPQ", "");
                    obj.ADJQty = GetReaderValue_NullableInt32(reader, "ADJQty", null);
                    //obj.LT = GetReaderValue_String(reader, "LT", "");
                    //obj.RoHSStatus = GetReaderValue_String(reader, "RoHSStatus", "");
                    //obj.FactorySealed = GetReaderValue_String(reader, "FactorySealed", "");
                    //obj.IPOBOMNo = GetReaderValue_Int32(reader, "IPOBOMNo", 0);
                    //obj.SupplierTotalQSA = GetReaderValue_String(reader, "SupplierTotalQSA", "");
                    //obj.SupplierLTB = GetReaderValue_String(reader, "SupplierLTB", "");
                    //obj.SupplierMOQ = GetReaderValue_String(reader, "SupplierMOQ", "");
                    //obj.IsSourcingHub = GetReaderValue_Boolean(reader, "ishub", false);
                    //obj.SupplierMessage = GetReaderValue_String(reader, "SupplierMessage", "");
                    obj.BOMStatus = GetReaderValue_Int32(reader, "REQStatus", -1);
                    obj.QuoteID = GetReaderValue_NullableInt32(reader, "QuoteID", null);
                    obj.QuoteNumber = GetReaderValue_NullableInt32(reader, "QuoteNumber", null);
                    obj.UpliftPercentage = GetReaderValue_Double(reader, "UpliftPercentage", 0);

                    lst.Add(obj);
                    obj = null;
                }


                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Offers", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override bool SaveBOMItemNoBid(int BOMManagerID, int CustomerRequirementId, int? UpdatedBy, string NoBidReason)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_update_CustomerRequirement_NoBid_BOMManager", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 180;
                cmd.Parameters.Add("@CustomerRequirementId", SqlDbType.Int).Value = CustomerRequirementId;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = UpdatedBy;
                cmd.Parameters.Add("@BomManagerID", SqlDbType.Int).Value = BOMManagerID;
                cmd.Parameters.Add("@NoBidNotes", SqlDbType.NVarChar).Value = NoBidReason;
                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (ret > 0);

            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to Insert No-Bid Bom Manager", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override bool RecallNoBidRequirement(System.Int32? customerRequirementId, System.Int32? updatedBy)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_update_CustomerRequirement_RecallNoBid_BOMManager", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@CustomerRequirementId", SqlDbType.Int).Value = customerRequirementId;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to update CustomerRequirement", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override bool ResetUpliftPriceAll(int BOMManagerID, int? ClientID)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("USP_ResetUplift", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 180;
                cmd.Parameters.Add("@BommanagerNo", SqlDbType.Int).Value = BOMManagerID;
                cmd.Parameters.Add("@ClientNo", SqlDbType.Int).Value = ClientID;
                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (ret > 0);

            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to reset uplift price.", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override bool PHSaveEditBOMItemData(int customerRequirementId, int manufacturerNo, int productNo, int BOMManagerID, System.Int32? UpdatedBy)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("USP_PH_Update_BOMManagerItem", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 180;
                cmd.Parameters.Add("@customerRequirementId", SqlDbType.Int).Value = customerRequirementId;
                cmd.Parameters.Add("@manufacturerNo", SqlDbType.Int).Value = manufacturerNo;
                cmd.Parameters.Add("@productNo", SqlDbType.Int).Value = productNo;
                cmd.Parameters.Add("@BOMManagerID", SqlDbType.Int).Value = BOMManagerID;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = UpdatedBy;
                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (ret > 0);

            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to Update Edited BOM Item.", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override List<BOMManager> GetListForCompany(int? clientId, int? companyId, bool? includeClosed)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_selectAll_BOMManager_for_Company", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 120;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@CompanyId", SqlDbType.Int).Value = companyId;
                cmd.Parameters.Add("@IncludeClosed", SqlDbType.Int).Value = includeClosed;

                cn.Open();


                DbDataReader reader = ExecuteReader(cmd);
                List<BOMManager> lst = new List<BOMManager>();
                while (reader.Read())
                {
                    BOMManager obj = new BOMManager();
                    obj.BOMManagerId = GetReaderValue_Int32(reader, "BOMManagerId", 0);
                    obj.BOMManagerCode = GetReaderValue_String(reader, "BOMManagerCode", "");
                    obj.BOMManagerName = GetReaderValue_String(reader, "BOMManagerName", "");
                    obj.ReceivedDate = GetReaderValue_DateTime(reader, "ReceivedDate", DateTime.MinValue);
                    obj.SalesmanName = GetReaderValue_String(reader, "SalesmanName", "");
                    obj.BOMManagerStatus = GetReaderValue_String(reader, "Status", "");
                    obj.TotalBomManagerLinePrice = GetReaderValue_Double(reader, "TotalBomManagerLinePrice", 0.00);
                    obj.DLUP = GetReaderValue_DateTime(reader, "DLUP", DateTime.MinValue);
                    obj.DateRequestToPOHub = GetReaderValue_NullableDateTime(reader, "DateRequestToPOHub", null);
                    obj.POCurrencyNo = GetReaderValue_Int32(reader, "POCurrencyNo", 0);
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to get BOM", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override int CountForCompany(int? companyId, bool? includeClosed)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_count_BOMManager_for_Company", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@CompanyId", SqlDbType.Int).Value = companyId;
                cmd.Parameters.Add("@IncludeClosed", SqlDbType.Bit).Value = includeClosed;
                cn.Open();
                return (Int32)ExecuteScalar(cmd);
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to count CustomerRequirement", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
    }
}
