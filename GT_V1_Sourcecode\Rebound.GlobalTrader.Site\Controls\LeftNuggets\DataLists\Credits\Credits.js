Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists");Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Credits=function(n){Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Credits.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Credits.prototype={initialize:function(){this.addGetDataOKEvent(Function.createDelegate(this,this.getDataOK));this._strPathToData="controls/DataListNuggets/Credits";this._strDataObject="Credits";Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Credits.callBaseMethod(this,"initialize")},dispose:function(){this.isDisposed||Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Credits.callBaseMethod(this,"dispose")},getDataOK:function(){for(var n,t,i=0,r=this._objResult.Results.length;i<r;i++)n=this._objResult.Results[i],t=String.format('<a href="{0}"><b>{1}<\/b> - {2}',$RGT_gotoURL_CreditNote(n.ID),n.No,$R_FN.setCleanTextValue(n.CM)),n.Part.length>0&&(t+=String.format("<br />{0}<\/a>",$R_FN.writePartNo(n.Part,n.ROHS))),t+="<\/a>",this._tbl.addRow([t],n.ID,!1),t=null,n=null}};Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Credits.registerClass("Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Credits",Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Base,Sys.IDisposable);