﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

/* 
===========================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-210037]		An.TranTan			23-Oct-2024		UPDATE			Update Manufacturer advisory notes
===========================================================================================
*/
CREATE OR ALTER PROCEDURE  [dbo].[usp_update_Manufacturer]   
--********************************************************************************************  
--* SK 29.10.2009:  
--* - allow for new column - FullName - used for searching  
--********************************************************************************************  
@ManufacturerId     int ,  
@ManufacturerName   nvarchar(128) ,  
@Notes    nvarchar(MAX) = Null ,  
@ManufacturerCode   nvarchar(5)  = Null ,  
@Inactive   bit ,   
@UpdatedBy   int    = Null ,  
@URL    nvarchar(128) = Null ,  
@ConflictResource    nvarchar(MAX) = Null ,
@AdvisoryNotes nvarchar(50) = null,
@IsDisplayAdvisory bit = null,
@RowsAffected  int = NULL Output  
  
AS  
  
DECLARE  @Count  integer  
  
SELECT   @Count     = COUNT (*)  
FROM    dbo.tbManufacturer  
WHERE    ManufacturerId = @ManufacturerId  
  
IF  @Count  > 0  
 BEGIN  
 UPDATE dbo.tbManufacturer  
 SET  ManufacturerName = @ManufacturerName  
  , Notes     = @Notes   
  , ManufacturerCode  = @ManufacturerCode   
  , Inactive    = @Inactive   
  , UpdatedBy   = @UpdatedBy  
  , DLUP    = current_timestamp  
  , URL     = @URL  
  , FullName   = dbo.ufn_get_fullname(@ManufacturerName)  
  , ConflictResource=@ConflictResource
  , AdvisoryNotes = @AdvisoryNotes
  , IsDisplayAdvisory = ISNULL(@IsDisplayAdvisory, 0)
 WHERE ManufacturerId  = @ManufacturerId  
 END  
  
SELECT  @RowsAffected = @@ROWCOUNT


GO


