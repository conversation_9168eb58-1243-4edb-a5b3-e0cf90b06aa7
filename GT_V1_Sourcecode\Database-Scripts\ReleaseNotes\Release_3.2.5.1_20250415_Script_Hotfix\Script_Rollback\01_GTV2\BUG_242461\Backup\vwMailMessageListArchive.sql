﻿GO

/****** Object:  View [dbo].[vwMailMessageListArchive]    Script Date: 4/15/2025 7:55:18 AM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

---------<PERSON><PERSON>----- 
CREATE OR ALTER VIEW [dbo].[vwMailMessageListArchive]    
AS    
SELECT m.MailMessageId, m.MailMessageFolderNo, m.<PERSON>o, m.To<PERSON>ogin<PERSON>o, m.<PERSON>ject, m.<PERSON>, m.Date<PERSON>, m.Recipient<PERSON>asBeenNotified,     
  m.<PERSON>, m.<PERSON>, m.DLUP, lf.EmployeeName AS FromLoginName, lt.EmployeeName AS ToLoginName, m.CompanyNo,     
  co.CompanyName--,ISNULL(m.DisableReplyButton,0) AS DisableReplyButton    
FROM dbo.tbMailMessageArchive AS m with (nolock) LEFT OUTER JOIN    
  dbo.tbCompany AS co with (nolock) ON m.CompanyNo = co.CompanyId LEFT OUTER JOIN    
  dbo.tbLogin AS lt with (nolock) ON m.ToLoginNo = lt.LoginId LEFT OUTER JOIN    
  dbo.tbLogin AS lf with (nolock) ON m.FromLoginNo = lf.LoginId   
  
GO
