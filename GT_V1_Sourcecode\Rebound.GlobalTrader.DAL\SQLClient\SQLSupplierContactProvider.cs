﻿//Marker     Changed by         Date         Remarks
//[001]      <PERSON><PERSON><PERSON><PERSON>     20/09/2021    Added new class for Supplier Contact.
using System;
using System.Data;
using System.Data.SqlClient;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;
using System.Web.Caching;
using System.Data.Common;

namespace Rebound.GlobalTrader.DAL.SqlClient
{
    public class SqlSupplierContactProvider : SupplierContactProvider
    {



        /// <summary>
        /// DropDown 
        /// Calls [usp_dropdown_SupplierContact]
        /// </summary>
        public override List<SupplierContactDetails> DropDown(System.Int32 SupplierId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_dropdown_SupplierContact", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@SupplierId", SqlDbType.Int).Value = SupplierId;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<SupplierContactDetails> lst = new List<SupplierContactDetails>();
                while (reader.Read())
                {
                    SupplierContactDetails obj = new SupplierContactDetails();
                    obj.SupplierContactEmail = GetReaderValue_String(reader, "SupplierContactId", "");
                    obj.Name = GetReaderValue_String(reader, "Name", "");
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get RohsStatuss", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

    }
}
