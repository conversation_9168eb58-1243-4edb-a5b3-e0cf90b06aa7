///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference name="~/Controls/IconButton/IconButton.js" />
///<reference name="~/Controls/ReboundTextBox/ReboundTextBox.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.CompanyManufacturers_Delete = function(element) { 
	Rebound.GlobalTrader.Site.Controls.Forms.CompanyManufacturers_Delete.initializeBase(this, [element]);
	this._intManufacturerLinkID = -1;
};

Rebound.GlobalTrader.Site.Controls.Forms.CompanyManufacturers_Delete.prototype = {

	get_intManufacturerLinkID: function() { return this._intManufacturerLinkID; }, 	set_intManufacturerLinkID: function(value) { if (this._intManufacturerLinkID !== value)  this._intManufacturerLinkID = value; }, 

	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.Forms.CompanyManufacturers_Delete.callBaseMethod(this, "initialize");
		this.addShown(Function.createDelegate(this, this.setupEvents));
	},

	dispose: function() { 
		if (this.isDisposed) return;
		this._intManufacturerLinkID = null;
		Rebound.GlobalTrader.Site.Controls.Forms.CompanyManufacturers_Delete.callBaseMethod(this, "dispose");
	},
	
	setupEvents: function() {
		this._ctlConfirm = this.getFieldComponent("ctlConfirm");
		this._ctlConfirm.addClickYesEvent(Function.createDelegate(this, this.yesClicked));
		this._ctlConfirm.addClickNoEvent(Function.createDelegate(this, this.noClicked));
	},

	yesClicked: function() {
		var obj = new Rebound.GlobalTrader.Site.Data();
		obj.set_PathToData("controls/Nuggets/CompanyManufacturers");
		obj.set_DataObject("CompanyManufacturers");
		obj.set_DataAction("Delete");
		obj.addParameter("id", this._intManufacturerLinkID);
		obj.addDataOK(Function.createDelegate(this, this.saveDeleteComplete));
		obj.addError(Function.createDelegate(this, this.saveDeleteError));
		obj.addTimeout(Function.createDelegate(this, this.saveDeleteError));
		$R_DQ.addToQueue(obj);
		$R_DQ.processQueue();
		obj = null;
		this.onSave();
	},
	
	noClicked: function() {
		this.showSaving(false);
		this.onNotConfirmed();
	},

	saveDeleteError: function(args) {
		this._strErrorMessage = args._errorMessage;
		this.onSaveError();
	},
	
	saveDeleteComplete: function(args) {
		this.showSaving(false);
		if (args._result.Result == true) {
			this.onSaveComplete();
		} else {
			this._strErrorMessage = args._errorMessage;
			this.onSaveError();
		}
	}
	
};

Rebound.GlobalTrader.Site.Controls.Forms.CompanyManufacturers_Delete.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.CompanyManufacturers_Delete", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
