using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Collections;
using Rebound.GlobalTrader.Site;
using System.Configuration;
using Rebound.GlobalTrader.Site.Enumerations;

namespace Rebound.GlobalTrader.Site.Controls {
	[ToolboxData("<{0}:AjaxSettings runat=server></{0}:AjaxSettings>")]
	public class AjaxSettings : Literal {

		protected StringBuilder _sbScript = new StringBuilder("");
		public double SuccessfulSaveMessageTime { get; set; }
		public double MessageCheckTimeout { get; set; }

		protected override void OnLoad(EventArgs e) {
			_sbScript.AppendLine("<script language=\"javascript\" type=\"text/javascript\">");
			_sbScript.AppendLine("Sys.Application.add_init(function() {");
			CreateSettings();
			_sbScript.AppendLine("});");
			_sbScript.AppendLine("</script>");
			base.OnLoad(e);
		}

		protected override void OnPreRender(EventArgs e) {
			Text = _sbScript.ToString();
			base.OnPreRender(e);
		}

		protected void CreateSettings() {
			SuccessfulSaveMessageTime = Convert.ToDouble(ConfigurationManager.AppSettings["SuccessfulSaveMessageTime"]);
			MessageCheckTimeout = Convert.ToDouble(ConfigurationManager.AppSettings["MessageCheckTimeout"]);

			//misc variables
			_sbScript.Append(@"$R_DQ = new Rebound.GlobalTrader.Site.DataQueueManager();");

			//Data queue constants
			_sbScript.AppendFormat(@"$R_DQ._intAllowedSimultaneousCalls = {0};", 5);
			_sbScript.AppendFormat(@"$R_DQ._intTimeBetweenCalls = {0};", 10);
			_sbScript.AppendFormat(@"$R_DQ._intTimeToWaitForLowPriorityCalls = {0};", 50);

			//Misc constants
			_sbScript.AppendLine("");
			_sbScript.AppendFormat(@"$R_TIME_TO_WAIT_AFTER_DROPDOWN_REFRESH = {0};", 0);
			_sbScript.AppendFormat(@"$R_SUCCESSFUL_SAVE_MESSAGE_TIME = {0};", SuccessfulSaveMessageTime);
			_sbScript.AppendFormat(@"$R_ELEMENT_FADE_TIME = {0};", 0.5);
			_sbScript.AppendFormat(@"$R_ANIMATE_FPS = {0};", 24);
			_sbScript.AppendFormat(@"$R_TABLE_RESIZE_COLUMNS_PAUSE = {0};", 0);
			_sbScript.AppendFormat(@"$R_LOGIN_TIMEOUT = {0};", HttpContext.Current.Session.Timeout);
			_sbScript.AppendFormat(@"$R_MESSAGE_CHECK_TIMEOUT = {0};", MessageCheckTimeout);
			_sbScript.AppendFormat(@"$R_DOUBLE_CLICK_TIME = {0};", 500);
			_sbScript.AppendFormat(@"$R_GENERIC_DATABASE_TIMEOUT = {0};", 30);
			_sbScript.AppendFormat(@"$R_MIN = {0};", int.MinValue);
			_sbScript.AppendFormat(@"$R_MAX = {0};", int.MaxValue);
            _sbScript.AppendFormat(@"$R_LOGGIN_USER = {0};", SessionManager.LoginID.HasValue ? SessionManager.LoginID.Value : 0);

			//User settings
			_sbScript.AppendFormat(@"$R_SHOW_MESSAGE_ALERT = {0};", "true");

			//standard querystring constants
			_sbScript.AppendLine("");
			_sbScript.Append(FormatQueryStringVariable(Rebound.GlobalTrader.Site.QueryStringManager.QueryStringVariables.Tab));
			_sbScript.Append(FormatQueryStringVariable(Rebound.GlobalTrader.Site.QueryStringManager.QueryStringVariables.CompanyID));
			_sbScript.Append(FormatQueryStringVariable(Rebound.GlobalTrader.Site.QueryStringManager.QueryStringVariables.CompanyName));
			_sbScript.Append(FormatQueryStringVariable(Rebound.GlobalTrader.Site.QueryStringManager.QueryStringVariables.ContactID));
			_sbScript.Append(FormatQueryStringVariable(Rebound.GlobalTrader.Site.QueryStringManager.QueryStringVariables.ContactName));
			_sbScript.Append(FormatQueryStringVariable(Rebound.GlobalTrader.Site.QueryStringManager.QueryStringVariables.ReturnURL));
			_sbScript.Append(FormatQueryStringVariable(Rebound.GlobalTrader.Site.QueryStringManager.QueryStringVariables.LoginID));
			_sbScript.Append(FormatQueryStringVariable(Rebound.GlobalTrader.Site.QueryStringManager.QueryStringVariables.GenericID));
			_sbScript.Append(FormatQueryStringVariable(Rebound.GlobalTrader.Site.QueryStringManager.QueryStringVariables.EmailMode));

			//Page URL constants
			Rebound.GlobalTrader.Site.Site objSite = Rebound.GlobalTrader.Site.Site.GetInstance();
			_sbScript.AppendLine("");
			foreach (SitePage pg in objSite.Pages) {
				_sbScript.Append(FormatPageURL(pg));
			}
			objSite = null;

			//enumerations
			_sbScript.AppendLine(CopyEnumeration(typeof(HorizontalAlign), "HorizontalAlign"));
			_sbScript.AppendLine(CopyEnumeration(typeof(Enumerations.SortColumnDirection), "SortColumnDirection"));
			_sbScript.AppendLine(CopyEnumeration(typeof(Forms.FormField.FormFieldControlType), "FormFieldControlType"));
			_sbScript.AppendLine(CopyEnumeration(typeof(TextFilterSearchType), "TextFilterSearchType"));
			_sbScript.AppendLine(CopyEnumeration(typeof(Controls.DropDowns.NumericalComparison.NumericalComparisonType), "NumericalComparisonType"));
			_sbScript.AppendLine(CopyEnumeration(typeof(Controls.AutoSearch.Base.AutoSearchResultsActionType), "AutoSearchResultsActionType"));
			_sbScript.AppendLine(CopyEnumeration(typeof(ReboundTextBox.TextBoxModeList), "TextBoxMode"));
			_sbScript.AppendLine(CopyEnumeration(typeof(MessageBox.MessageTypeList), "MessageTypeList"));
			_sbScript.AppendLine(CopyEnumeration(typeof(Controls.FlexiDataColumn.ClientSortFormat), "ClientSortFormat"));
			//misc variables
			_sbScript.Append(@"$R_RES = new Rebound.GlobalTrader.Site.Resources();");
			_sbScript.Append(@"$RGT_FN = Rebound.GlobalTrader.Site.Functions;");
			_sbScript.AppendFormat(@"$RGT_STAR_IMG = ""{0}"";", Functions.GetLocalThemeImage("StarRating/saved.png", Page.Theme, true));
			_sbScript.AppendFormat(@"$RGT_STAR_EMPTY_IMG = ""{0}"";", Functions.GetLocalThemeImage("StarRating/empty.png", Page.Theme, true));

			_sbScript.AppendLine("");
			QueryStringManager qs = new QueryStringManager();
			foreach (QueryStringVariable var in qs.Variables) {
				_sbScript.Append(FormatQueryStringVariable("$R_", var));
			}

			//GlobalTrader enumerations
			_sbScript.AppendLine(CopyEnumeration(typeof(Controls.LeftNuggets.QuickJump.QuickJumpType), "QuickJumpType"));
			_sbScript.AppendLine(CopyEnumeration(typeof(Controls.LeftNuggets.QuickJump.QuickJumpSection), "QuickJumpSection"));
			_sbScript.AppendLine(CopyEnumeration(typeof(Controls.DataListNuggets.CompanyListType), "CompanyListType"));
			_sbScript.AppendLine(CopyEnumeration(typeof(BLL.SystemDocument.ListForPrint), "PrintObject"));
			_sbScript.AppendLine(CopyEnumeration(typeof(BLL.SystemDocument.List), "SystemDocument"));
			_sbScript.AppendLine(CopyEnumeration(typeof(BLL.SalesOrderStatus.List), "SalesOrderStatus"));
			_sbScript.AppendLine(CopyEnumeration(typeof(BLL.PurchaseOrderStatus.List), "PurchaseOrderStatus"));
			_sbScript.AppendLine(CopyEnumeration(typeof(BLL.StockStatus.List), "StockStatus"));
			_sbScript.AppendLine(CopyEnumeration(typeof(BLL.SettingItem.List), "SettingItem"));
			_sbScript.AppendLine(CopyEnumeration(typeof(Pages.Default.TabList), "HomeTabList"));
		}

		protected string CopyEnumeration(string strAssembly, Type typEnum, string strName) {
			StringBuilder sb = new StringBuilder("");
			sb.AppendFormat("{0}.{1} = function() {{ throw Error.invalidOperation(); }};", strAssembly, strName);
			sb.AppendFormat("{0}.{1}.prototype = {{", strAssembly, strName);

			//step through items
			bool blnFirst = true;
			IEnumerator en = Enum.GetValues(typEnum).GetEnumerator();
			while (en.MoveNext()) {
				if (!blnFirst) sb.Append(", ");
				sb.AppendFormat(@"{0}:{1}", en.Current.ToString(), (int)en.Current);
				blnFirst = false;
			}
			en = null;
			sb.Append("};");
			sb.AppendFormat("$R_ENUM${1}={0}.{1};", strAssembly, strName);
			sb.AppendFormat(@"{0}.{1}.registerEnum(""{0}.{1}"");", strAssembly, strName);
			return sb.ToString();
		}
		protected string CopyEnumeration(Type typEnum, string strName) {
			return CopyEnumeration("Rebound.GlobalTrader.Site", typEnum, strName);
		}

		protected string FormatPageURL(string strPrefix, SitePage objPage) {
			return (string.Format(@"{2}URL_{0}=""{1}"";", objPage.Name, objPage.RelativeUrl, strPrefix));
		}

		protected string FormatPageURL(SitePage objPage) {
			return FormatPageURL("$R_", objPage);
		}

		protected string FormatQueryStringVariable(string strPrefix, QueryStringVariable objVar) {
			return String.Format(@"{2}QS_{0}=""{1}"";", objVar.Name, objVar.VariableQSName, strPrefix);
		}

		protected string FormatQueryStringVariable(QueryStringVariable objVar) {
			return FormatQueryStringVariable("$R_", objVar);
		}
	}
}
