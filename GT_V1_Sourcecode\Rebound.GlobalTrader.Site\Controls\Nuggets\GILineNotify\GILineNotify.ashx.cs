//Marker     Changed by      Date         Remarks
//[001]      Soorya          03/03/2023   RP-1048 Remove AI code
using System;
using System.Web;
using System.Web.Services;
using Rebound.GlobalTrader.BLL;
//using Microsoft.ApplicationInsights;  //[001]
using System.Collections.Generic;

namespace Rebound.GlobalTrader.Site.Controls.Nuggets.Data
{
    [WebService(Namespace = "http://tempuri.org/")]
    [WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    public class GILineNotify : Rebound.GlobalTrader.Site.Data.Base
    {
        public override void ProcessRequest(HttpContext context)
        {
            if (base.init(context))
            {
                switch (Action)
                {
                    case "NotifyQuery": NotifyQuery(); break;
                    //case "CreatePDF": CreatePDF(); break;
                    default: WriteErrorActionNotFound(); break;
                }
            }
        }
        public void NotifyQuery()
        {
            try
            {
                int LoginType = GetFormValue_Int("LoginType"); // (1 for Sales) (2 for Purchase) (3 for Quality) (4 for Sales & Purchase) (5 for Sales , Purchase & Quality) (6 for Purchase & Quality)  (7 for Sales & Quality) 
                int? QueryApprovedStatusSales = GetFormValue_NullableInt("QueryApprovedStatusSales");
                int? QueryApprovedStatusPurchase = GetFormValue_NullableInt("QueryApprovedStatusPurchase");
                int? QueryApprovedStatusQuality = GetFormValue_NullableInt("QueryApprovedStatusQuality");
                string QueryReply = GetFormValue_String("QueryReply");
                bool blnOK = GoodsInLine.NotifyQuery(
                      ID
                    //, GetFormValue_Boolean("IsQueryApproved")
                    //, GetFormValue_Boolean("IsBySales")
                    //, GetFormValue_Boolean("IsByPurchasing")
                    //, GetFormValue_Boolean("IsByQuality")
                    , QueryApprovedStatusSales
                    , QueryApprovedStatusPurchase
                    , QueryApprovedStatusQuality
                    , GetFormValue_Boolean("IsPDFReportRequired")
                    , GetFormValue_Boolean("IsQuarantineProduct")
                    , QueryReply
                    , LoginID
                    , LoginType
                );
                int GINumber = GetFormValue_Int("GINumber");
                int UpdatedBy = GetFormValue_Int("UpdatedBy");
                string LoginStatus = QueryApprovedStatusSales == 1 ? "Approved" : QueryApprovedStatusPurchase == 1 ? "Approved" : QueryApprovedStatusQuality == 1 ? "Approved" : "Decline";
                string GroupName = LoginType == 1 ? "Sales" : LoginType == 2 ? "Purchase" : LoginType == 3 ? "Quality" : LoginType == 4 ? "Sales & Purchase" : LoginType == 5 ? "Sales , Purchase & Quality" : LoginType == 6 ? "Purchase & Quality" : LoginType == 7 ? "Sales & Quality" : "";
                if (blnOK == true)
                {
                    if (UpdatedBy > 0)
                    {
                        WebServices service = new WebServices();
                        string message = string.Format(Functions.GetGlobalResource("MailTemplates", "GIQueryScreenReply"), GINumber, LoginStatus, GroupName, QueryReply, SessionManager.LoginFullName);
                        service.NotifyMessage(UpdatedBy.ToString(), "", string.Format(Functions.GetGlobalResource("Messages", "GIQuerySales"), GINumber), message, null);
                        service = null;

                    }
                }
                JsonObject jsn = new JsonObject();
                jsn.AddVariable("Result", blnOK);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            catch (Exception e)
            {
                //[001]
                //var ai = new TelemetryClient();
                //ai.TrackTrace("Exception GILineNotify: NotifyQuery Action");
                //ai.TrackException(e);
                new Errorlog().LogMessage("Error at AddNew() in GILineNotify.ashx.cs : " + e.Message);

                WriteError(e);
            }
        }
        //public void CreatePDF()
        //{
        //    try
        //    {
        //        string strPdfPath = "";
        //        bool blnOK = false;
        //        GeneratePDF obj = new GeneratePDF();
        //        GoodsInLine ln = null;
        //        ln = GoodsInLine.GetGILineData(ID);
        //        if (ln != null)
        //        {
        //            obj.PrintGIQueryResponseReport(ln, out strPdfPath);
        //            blnOK = true;
        //        }
        //        if (!string.IsNullOrEmpty(strPdfPath))
        //        {
        //            //BLL.SalesOrder.Insert(ID, " ", strPdfPath, LoginID, "GIQueryResponsePDFNEW");
        //        }
        //        obj = null;
        //        JsonObject jsn = new JsonObject();
        //        jsn.AddVariable("Result", blnOK);
        //        OutputResult(jsn);
        //        jsn.Dispose(); jsn = null;
        //    }
        //    catch (Exception e)
        //    {
        //         [001]
        //        var ai = new TelemetryClient();
        //        ai.TrackTrace("Exception GILineNotify: CreatePDF Action");
        //        ai.TrackException(e);

        //        WriteError(e);
        //    }
        //}
    }
}
