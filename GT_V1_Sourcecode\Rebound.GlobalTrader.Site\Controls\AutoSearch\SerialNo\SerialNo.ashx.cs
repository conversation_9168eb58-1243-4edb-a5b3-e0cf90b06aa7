using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;

namespace Rebound.GlobalTrader.Site.Controls.AutoSearch.Data {

	[WebService(Namespace = "http://tempuri.org/")]
	[WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    public class SerialNo : Rebound.GlobalTrader.Site.Controls.AutoSearch.Data.Base
    {

		protected override void GetData() {
            string txtGroup= null;
            txtGroup =  GetFormValue_String("txtGroup");
			try {
                JsonObject jsn = new JsonObject();
                JsonObject jsnItems = new JsonObject(true);
                //string groupName = "w2";
                List<BLL.GoodsInLine> lst = BLL.GoodsInLine.AutoSearch(GetFormValue_StringForNameSearch("search"), txtGroup);
                jsn.AddVariable("TotalRecords", lst.Count);
                for (int i = 0; i < lst.Count; i++)            
                {
                    JsonObject jsnItem = new JsonObject();
                    jsnItem.AddVariable("ID", lst[i].SerialNoId);
                    jsnItem.AddVariable("SerialNo", lst[i].SerialNo);
                    
                    jsnItems.AddVariable(jsnItem);
                    jsnItem.Dispose(); jsnItem = null;
                   
                }                
                jsn.AddVariable("Results", jsnItems);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
			} catch (Exception e) {
				WriteError(e);
			} finally {
				
			}
			base.GetData();
		}
	}
}