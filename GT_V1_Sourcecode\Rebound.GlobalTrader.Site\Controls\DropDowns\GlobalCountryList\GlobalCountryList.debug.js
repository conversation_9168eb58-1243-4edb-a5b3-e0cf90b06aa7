///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//
// RP 06.10.2009:
// - Changes due to changes in base class
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");

Rebound.GlobalTrader.Site.Controls.DropDowns.GlobalCountryList = function (element) {
    Rebound.GlobalTrader.Site.Controls.DropDowns.GlobalCountryList.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.DropDowns.GlobalCountryList.prototype = {

    get_blnIncludeSelected: function () { return this._blnIncludeSelected; }, set_blnIncludeSelected: function (v) { if (this._blnIncludeSelected !== v) this._blnIncludeSelected = v; },
    get_intGlobalLoginClientNo: function () { return this._intGlobalLoginClientNo; }, set_intGlobalLoginClientNo: function (v) { if (this._intGlobalLoginClientNo !== v) this._intGlobalLoginClientNo = v; },
    initialize: function () {
        Rebound.GlobalTrader.Site.Controls.DropDowns.GlobalCountryList.callBaseMethod(this, "initialize");
        this.addSetupDataCall(Function.createDelegate(this, this.setupDataCall));
        this.addGetDataOK(Function.createDelegate(this, this.dataCallOK));
    },

    dispose: function () {
        if (this.isDisposed) return;
        this._blnIncludeSelected = null;
        this._intGlobalLoginClientNo = null;
        Rebound.GlobalTrader.Site.Controls.DropDowns.GlobalCountryList.callBaseMethod(this, "dispose");
    },

    setupDataCall: function () {
        //alert(this._intGlobalLoginClientNo);
        this._objData.set_PathToData("controls/DropDowns/GlobalCountryList");
        this._objData.set_DataObject("GlobalCountryList");
        this._objData.set_DataAction("GetData");
        this._objData.addParameter("IncludeSelected", this._blnIncludeSelected);
        this._objData.addParameter("GlobalLoginClientNo", this._intGlobalLoginClientNo);
    },

    dataCallOK: function () {
        var result = this._objData._result;
        if (result.GlobalCountryLists) {
            for (var i = 0; i < result.GlobalCountryLists.length; i++) {
                this.addOption(result.GlobalCountryLists[i].Name, result.GlobalCountryLists[i].ID, result.GlobalCountryLists[i].HighRiskContent);
            }
        }
    }

};

Rebound.GlobalTrader.Site.Controls.DropDowns.GlobalCountryList.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.GlobalCountryList", Rebound.GlobalTrader.Site.Controls.DropDowns.Base);
