﻿///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//Marker     changed by      date         Remarks
//[001]      Vinay          09/07/2012   This need for Rebound- Invoice bulk Emailer
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.ContactGroup_Add = function (element) {
    Rebound.GlobalTrader.Site.Controls.Forms.ContactGroup_Add.initializeBase(this, [element]);
    //this._intCompanyID = -1;
    this._strContactType = "";
    this._arrPOLineIds = [];
};

Rebound.GlobalTrader.Site.Controls.Forms.ContactGroup_Add.prototype = {

    //get_intCompanyID: function () { return this._intCompanyID; }, set_intCompanyID: function (value) { if (this._intCompanyID !== value) this._intCompanyID = value; },
    //get_intContactID: function () { return this._intContactID; }, set_intContactID: function (value) { if (this._intContactID !== value) this._intContactID = value; },
    //get_ibtnEdit: function () { return this._ibtnEdit; }, set_ibtnEdit: function (value) { if (this._ibtnEdit !== value) this._ibtnEdit = value; },
    get_ibtnSearch: function () { return this._ibtnSearch; }, set_ibtnSearch: function (v) { if (this._ibtnSearch !== v) this._ibtnSearch = v; },
    get_ibtnReset: function () { return this._ibtnReset; }, set_ibtnReset: function (v) { if (this._ibtnReset !== v) this._ibtnReset = v; },

    get_strContactType: function () { return this._strContactType; }, set_strContactType: function (value) { if (this._strContactType !== value) this._strContactType = value; },
    get_tbl: function () { return this._tbl; }, set_tbl: function (v) { if (this._tbl !== v) this._tbl = v; },

    initialize: function () {
        Rebound.GlobalTrader.Site.Controls.Forms.ContactGroup_Add.callBaseMethod(this, "initialize");
        //this.addRefreshEvent(Function.createDelegate(this, this.getData));
        this.addCancel(Function.createDelegate(this, this.cancelClicked));
        ////setup forms and their events
        //if (this._ibtnEdit) {
        //    $R_IBTN.addClick(this._ibtnEdit, Function.createDelegate(this, this.showEditForm));
        //    this._frmEdit = $find(this._aryFormIDs[0]);
        //    this._frmEdit.addSave(Function.createDelegate(this, this.saveEdit));
        //    this._frmEdit.addCancel(Function.createDelegate(this, this.cancelEdit));
        //    this._frmEdit.addSaveComplete(Function.createDelegate(this, this.saveEditComplete));
        //}
    },

    cancelClicked: function () {
        // $R_FN.navigateBack();
        window.location.href = 'Con_ManufacturerBrowse.aspx';
        //this.showLeftMenu();
    },

    dispose: function () {
        if (this.isDisposed) return;
        //if (this._ibtnEdit) $R_IBTN.clearHandlers(this._ibtnEdit);
        //if (this._ibtnAdd) $R_IBTN.clearHandlers(this._ibtnAdd);
        //if (this._frmAdd) this._frmAdd.dispose();
        //if (this._frmEdit) this._frmEdit.dispose();
        //if (this._frmMap) this._frmMap.dispose();
        if (this._tbl) this._tbl.dispose();
        //this._frmAdd = null;
        //this._frmEdit = null;
        //this._frmMap = null;
        //this._ibtnAdd = null;
        //this._ibtnEdit = null;
        this._tbl = null;
        //this._intProductNameID = null;
        this._strContactType = null;
        //this._intGbProductNameID = null;
        Rebound.GlobalTrader.Site.Controls.Forms.ContactGroup_Add.callBaseMethod(this, "dispose");
    },

    getData: function () {
        //this.getData_Start();
        //var obj = new Rebound.GlobalTrader.Site.Data();
        //obj.set_PathToData("controls/Nuggets/ContactGroup");
        //obj.set_DataObject("ContactGroup");
        //obj.set_DataAction("GetData");
        //var name = $("#ctl00_cphMain_ctlResults_ctlDB_ctl13_ctlContactGroup_ctlDB_txtName").val();
        //var code = $("#ctl00_cphMain_ctlResults_ctlDB_ctl13_ctlContactGroup_ctlDB_txtCode").val();
        //obj.addParameter("Name", name);
        //obj.addParameter("Code", code);
        //obj.addParameter("Type", this._strContactType);
        //obj.addDataOK(Function.createDelegate(this, this.getDataOK));
        //obj.addError(Function.createDelegate(this, this.getDataError));
        //obj.addTimeout(Function.createDelegate(this, this.getDataError));
        //$R_DQ.addToQueue(obj);
        //$R_DQ.processQueue();
        //obj = null;
        alert('inside click');
    },

    getDataOK: function (args) {
        var res = args._result;
        //this.setFieldValue("ctlFirstName", res.FirstName);
        //this.setFieldValue("ctlSurname", res.Surname);
        //this.setFieldValue("ctlJobTitle", res.JobTitle);
        //this.setFieldValue("ctlTel", res.Tel);
        //this.setFieldValue("ctlFax", res.Fax);
        //this.setFieldValue("ctlTelExt", res.TelExt);
        //this.setFieldValue("ctlHomeTel", res.HomeTel);
        //this.setFieldValue("ctlMobileTel", res.MobileTel);
        //this.setFieldValueEmail("ctlEmail", res.Email);
        //this.setFieldValue("ctlIsEmailTextOnly", res.IsEmailTextOnly);
        //this.setFieldValue("ctlNickname", res.Nickname);
        //this.setFieldValue("ctlPersonalAddress", res.CompanyAdd); // Have to Show Address on Main Contact Info Tab
        ////this.setFieldValue("ctlPersonalAddress", res.PersonalAddress);
        ////this.setFieldValue("hidAddressName", res.AddressName);
        ////this.setFieldValue("hidAddressID", res.AddressID);
        ////this.setFieldValue("hidAddress1", res.Address1);
        ////this.setFieldValue("hidAddress2", res.Address2);
        ////this.setFieldValue("hidAddress3", res.Address3);
        ////this.setFieldValue("hidTown", res.Town);
        ////this.setFieldValue("hidCounty", res.County);
        ////this.setFieldValue("hidCountryNo", res.CountryNo);
        ////this.setFieldValue("hidPostcode", res.Postcode);
        ////[001] code start
        //this.setFieldValue("hidFinanceContacts", res.FinanceContact);
        //this.setFieldValue("hidCompanyAddress", res.CompanyAddress);
        //this.setFieldValue("hidInactive", res.Inactive);
        ////this.setFieldValue("hidComapnyNo", res.CompanyNo);
        ////[001] code end
        //this.setFieldValue("ctlIsSendShipmentNotification", res.IsSendShipmentNotification);
        //this.setDLUP(res.DLUP);
        //this.getDataOK_End();
        var result = args._result;
        if (result.Items) {
            for (var i = 0; i < result.Items.length; i++) {
                var row = result.Items[i];
                var aryData = [
                    this.writeCheckbox(row.LineID, i, tbl)
                    , $R_FN.setCleanTextValue(row.Name)
                    , $R_FN.setCleanTextValue(row.Code)
                ];
                var xtraData = { Inactive: row.Inactive };
                var strCSS = (row.Inactive) ? "ceased" : "";
                // selecting the record when navigate from warning screen
                this._tbl.addRow(aryData, row.LineID, null, null, strCSS);

                this.registerCheckBox(row.LineID, i, false, true, tbl);
                var chk = this.getCheckBox(i, tbl);
                chk._element.setAttribute("onClick", String.format("$find(\"{0}\").getCheckedCellValue({1},{2});", this._element.id, i, row.LineID));

                row = null;
                //blnHasData = true;
            }
        }
        this._tbl.resizeColumns();
        this.showContent(true);
        this.showContentLoading(false);
    },

    writeCheckbox: function (varID, intIndex, tbl) {
        var strChkID = this.getControlID("chk", intIndex, tbl);
        var strChkImageID = this.getControlID("chkImg", intIndex, tbl);
        var str = String.format("<div class=\"imageCheckBoxDisabled\" id=\"{0}\" ><img id=\"{1}\" class=\"{2}\" src=\"images/x.gif\" style=\"border-width: 0px;\" /> </div>", strChkID, strChkImageID, "off");
        return str;
    },

    getControlID: function (str, i, tbl) {
        return String.format("{0}_{1}{2}", tbl._element.id, str, i);
    },
    getCheckBox: function (intCheckBox, tbl) {
        return $find(this.getControlID("chk", intCheckBox, tbl));
    },
    getCheckedCellValue: function (intIndex, rowId) {

        var tbl = this._tbl;
        var chk = this.getCheckBox(intIndex, tbl);
        var IsChecked = chk._blnChecked;
        var tr = tbl._tbl.rows[intIndex];
        // alert(tr);
        if (!tr) return;

        if (IsChecked == true) {
            //  var totalPrice = this.getFieldValue("ctlQuantityOrdered") * this.getFieldValue("hidPrice");
            this.clearMessages();
            //    if (this.validateCreditLimitIPO(totalPrice + this._selectedTaxValue + ((this._anyLinePosted) ? 0 : this._totalLinePrice))) {
            //this._arrPOLineIds.push(poLinID);
            Array.add(this._arrPOLineIds, rowId);
            //[005] start
            //Array.add(this._POLineSerialNo, poSerialNo);
            //[005] end
            //$R_IBTN.enableButton(this._ibtnAddExpditeNote, true);
            // }
        }
        else {
            //$R_IBTN.enableButton(this._ibtnAddExpditeNote, false);
            //this._arrPOLineIds.remove(poLinID);
            Array.remove(this._arrPOLineIds, rowId)
            //[005] start
            //Array.remove(this._POLineSerialNo, poSerialNo);
            //[005] end
        }
        //if (this._arrPOLineIds.length == 0)
        //    $R_IBTN.enableButton(this._ibtnAddExpditeNote, false);
        //else
        //    $R_IBTN.enableButton(this._ibtnAddExpditeNote, true);
        ////[005] start
        //this.enableReleaseButton();
        //[005] end

    },

    getDataError: function (args) {
        this.showError(true, args.get_ErrorMessage());
    },

    saveEdit: function () {
    },

    cancelEdit: function () {
        this.hideEditForm();
        this.showContent(true);
    },

    showEditForm: function () {
        //this._frmEdit._intCompanyNo = this._intCompanyID;

        //this._frmEdit._intContactID = this._intContactID;
        //this._frmEdit.setFieldValue("ctlFirstName", this.getFieldValue("ctlFirstName"));
        //this._frmEdit.setFieldValue("ctlSurname", this.getFieldValue("ctlSurname"));
        //this._frmEdit.setFieldValue("ctlJobTitle", this.getFieldValue("ctlJobTitle"));
        //this._frmEdit.setFieldValue("ctlTel", this.getFieldValue("ctlTel"));
        //this._frmEdit.setFieldValue("ctlFax", this.getFieldValue("ctlFax"));
        //this._frmEdit.setFieldValue("ctlExtension", this.getFieldValue("ctlTelExt"));
        //this._frmEdit.setFieldValue("ctlHomeTel", this.getFieldValue("ctlHomeTel"));
        //this._frmEdit.setFieldValue("ctlMobileTel", this.getFieldValue("ctlMobileTel"));
        //this._frmEdit.setFieldValue("ctlEmail", this.getFieldValue("ctlEmail"));
        //this._frmEdit.setFieldValue("ctlTextOnlyEmail", this.getFieldValue("ctlIsEmailTextOnly"));
        //this._frmEdit.setFieldValue("ctlNickname", this.getFieldValue("ctlNickname"));
        //this._frmEdit.setFieldValue("ctlCompanyAddress", this.getFieldValue("hidCompanyAddress"));
        ////this._frmEdit._ctlAddress._intAddressID = this.getFieldValue("hidAddressID");
        ////this._frmEdit.setFieldValue("ctlAddressName", this.getFieldValue("hidAddressName"));
        ////this._frmEdit.setFieldValue("ctlLine1", this.getFieldValue("hidAddress1"));
        ////this._frmEdit.setFieldValue("ctlLine2", this.getFieldValue("hidAddress2"));
        ////this._frmEdit.setFieldValue("ctlLine3", this.getFieldValue("hidAddress3"));
        ////this._frmEdit.setFieldValue("ctlTown", this.getFieldValue("hidTown"));
        ////this._frmEdit.setFieldValue("ctlCounty", this.getFieldValue("hidCounty"));
        ////this._frmEdit.setFieldValue("ctlCountry", this.getFieldValue("hidCountryNo"));
        ////this._frmEdit.setFieldValue("ctlPostcode", this.getFieldValue("hidPostcode"));
        ////[001] code start
        ////alert(this._intCompanyID);
        //this._frmEdit.setFieldValue("ctlFinanceContacts", Boolean.parse(this.getFieldValue("hidFinanceContacts")));
        //this._frmEdit.setFieldValue("ctlInactive", Boolean.parse(this.getFieldValue("hidInactive")));
        //this._frmEdit.setFieldValue("ctlSendShipmentNotification", this.getFieldValue("ctlIsSendShipmentNotification"));
        //this.showForm(this._frmEdit, true);
        //[001] code end
    },

    hideEditForm: function () {
        this.showForm(this._frmEdit, false);
    },

    saveEditComplete: function () {
        //this.hideEditForm();
        //this.showContentLoading(false);
        //this.getData();
        //this.showSavedOK(true, $R_RES.ChangesSavedSuccessfully);
        //this.onSaveEditComplete();
    },

    saveEditError: function () {
        this.showError(true, this._frmEdit._strErrorMessage);
    }



};

Rebound.GlobalTrader.Site.Controls.Forms.ContactGroup_Add.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.ContactGroup_Add", Rebound.GlobalTrader.Site.Controls.Forms.Base);