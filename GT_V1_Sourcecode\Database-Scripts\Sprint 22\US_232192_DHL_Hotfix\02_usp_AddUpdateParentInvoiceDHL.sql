﻿
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

/* 
============================================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-211896]		Phuc Hoang			18-Sep-2024		UPDATE			[PROD Bug] DHL - Rebound API invoice BBX
[US-232192]		Phuc Hoang			20-Feb-2025		UPDATE			[PROD Bug] DHL BBX Shipments
============================================================================================================
*/
CREATE OR ALTER PROCEDURE [dbo].[usp_AddUpdateParentInvoiceDHL] (
    @StatementId Int = 0,
    @AWBNo bigint,
    @ParentID varchar(100),
    @ChildID bigint,
    @InvoiceLabel varchar(500),
    @ParentLabel varchar(500),
    @InvoiceNo varchar(500),
    @ClientNo INT = 0,
	@vWeight Decimal = 0,
	@vShippingCost decimal=Null, 
	@vBoxes int=null, 
	@vDimensionalWeight decimal=null,                                   
	@vNotes varchar(max)=null,
    @WPXJson nvarchar(max),
    @Message varchar(300) OUTPUT,
    @IsValid bit OUTPUT,
    @ReturnId int OUTPUT
)
AS
--------25-04-2023--------------   
--Created By: Manoj Kumar                                              
--Created On: 07 oct 2022//Altered By Manoj :26-dec=2022                          
--Purpose: To add/update parent invoice                                           
BEGIN
	Declare @vLowestInvoice INT, @vCountInvoice INT;

	IF (RIGHT(@InvoiceNo, 1) = ',')
	BEGIN
		SET @InvoiceNo = reverse(stuff(reverse(@InvoiceNo), 1, 1, ''));
	END
                                       
	SET @vLowestInvoice = (SELECT Min(Cast(RTrim(LTrim(String)) AS INT)) FROM dbo.[ufn_splitString](@InvoiceNo,','))                                      
	SET @vCountInvoice = (SELECT COUNT(*) FROM dbo.[ufn_splitString](@InvoiceNo,','))

	Declare @ShipperNo varchar(50);

    IF EXISTS( SELECT 'X' FROM ParentInvoiceAWBDHL WHERE AWBNo = @AWBNo AND IsActive = 1) 
    BEGIN
      UPDATE ParentInvoiceAWBDHL
      SET IsActive = 0
      WHERE AWBNo = @AWBNo;
    END 

    IF EXISTS(SELECT 'X' FROM ParentInvoiceAWBDHL WHERE IsActive = 1) 
    BEGIN
      update ParentInvoiceAWBDHL
      set IsActive = 0
      WHERE ParentInvoiceAWBDHLId in (
          SELECT pia.ParentInvoiceAWBDHLId
          FROM ParentInvoiceAWBDHL pia
            JOIN ChildInvoiceAWBDHL cia ON pia.childId = cia.childId
          WHERE pia.IsActive = 1
            AND cia.SendStatus = 0
        )
    END

    SELECT @ShipperNo = ShipperNo FROM ChildInvoiceAWBDHL WHERE AWBNo = @AWBNo AND SendStatus = 1;

    INSERT INTO ParentInvoiceAWBDHL (
      AWBNo,
      ParentID,
      ChildID,
      InvoiceFile,
      InvoiceLabel,
      InvoiceNo
    )
    VALUES (
      @AWBNo,
      @ParentID,
      @ChildID,
      @invoiceLabel,
      @ParentLabel,
      @InvoiceNo
    )

    Declare @ParentInvoiceAWBDHLId int = 0;
    Set @ParentInvoiceAWBDHLId = SCOPE_IDENTITY();
    exec usp_AddUpdateChildInvoiceDHL 1,@AWBNo,@ParentID,@ChildID,'',0,@ParentInvoiceAWBDHLId,@InvoiceNo,@ParentLabel,@InvoiceLabel,
		@WPXJson,'',0,0,@ShipperNo,0,@Message,@IsValid,@ReturnId;	

    UPDATE t
    SET t.AirWayBill = @AWBNo,
		t.[Boxes] = @vBoxes, -- Number of boxes 
		t.[Weight] = @vWeight, -- Total weight of shipment
		t.[DimensionalWeight] = @vDimensionalWeight, -- Dimensional weight	
		t.[ShippingCost] = @vShippingCost, --Cost of shipment
		t.[Notes]= isnull([Notes],'') + ' ' + '<br>|#Packed By:'+ @vNotes  + '#|',
		t.IsUpsInvoiceExported = 1,
		t.DHLParentInvoiceNo = @ParentID
    FROM  tbInvoice t 
      JOIN ChildInvoiceAWBDHL ci ON ci.InvoiceNo = t.InvoiceNumber AND ci.ParentID = @ParentID
    WHERE ci.IsActive = 1 AND t.InvoiceNumber = @vLowestInvoice AND t.ClientNo = @ClientNo; 
	
	IF @vCountInvoice > 1 
	BEGIN
		--Update slave Invoice
		UPDATE tbInvoice 
		SET [AirWayBill] = @AWBNo, 
			[ShippingCost] = null, 
			[IsUpsInvoiceExported] = 1,  
			[IsGTServiceUpdated] = 1 
		WHERE ClientNo=@ClientNo 
			AND ISNULL([IsGTServiceUpdated], 0) = 0 
			AND InvoiceNumber IN ( SELECT LTRIM(RTRIM(String)) FROM dbo.[ufn_splitString](@InvoiceNo,',') WHERE String NOT IN (@vLowestInvoice) ) 
	END
	 

    SET @Message = 'Declare statement has been added successfully.'
    SET @IsValid = 1
    SET @ReturnId = @ParentInvoiceAWBDHLId

END
GO


