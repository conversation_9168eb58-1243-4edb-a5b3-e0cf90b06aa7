Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DataListNuggets");Rebound.GlobalTrader.Site.Controls.DataListNuggets.Invoices=function(n){Rebound.GlobalTrader.Site.Controls.DataListNuggets.Invoices.initializeBase(this,[n]);this._frmConfirm=null;this._InvoiceFormat="PDF"};Rebound.GlobalTrader.Site.Controls.DataListNuggets.Invoices.prototype={get_blnPageLoad:function(){return this._blnPageLoad},set_blnPageLoad:function(n){this._blnPageLoad!==n&&(this._blnPageLoad=n)},get_ClientNo:function(){return this._ClientNo},set_ClientNo:function(n){this._ClientNo!==n&&(this._ClientNo=n)},get_AllowGenerateXml:function(){return this._AllowGenerateXml},set_AllowGenerateXml:function(n){this._AllowGenerateXml!==n&&(this._AllowGenerateXml=n)},initialize:function(){this.addSetupDataCallEvent(Function.createDelegate(this,this.setupDataCall));this.addGetDataOKEvent(Function.createDelegate(this,this.getDataOK));this._ibtnPrint=$get(this._aryButtonIDs[0]);this._ibtnEmail=$get(this._aryButtonIDs[1]);this._frmConfirm=$find(this._aryFormIDs[0]);this._frmConfirm.addCancel(Function.createDelegate(this,this.hideConfirmForm));this._frmConfirm.addSaveComplete(Function.createDelegate(this,this.saveCeaseComplete));this._frmConfirm.addNotConfirmed(Function.createDelegate(this,this.hideConfirmForm));this._ibtnEmail&&$R_IBTN.addClick(this._ibtnEmail,Function.createDelegate(this,this.showConfirmForm));this._ibtnPrint&&$R_IBTN.addClick(this._ibtnPrint,Function.createDelegate(this,this.openFormatModal));document.getElementById("btnFormatAction").addEventListener("click",Function.createDelegate(this,this.selectInvoicePrintFormat));document.getElementById("btnFormatCancel").addEventListener("click",Function.createDelegate(this,this.closePopup));this.enableBulkButtons(!1);this._table.addMultipleSelectionChanged(Function.createDelegate(this,this.selectionMade));this._strPathToData="controls/DataListNuggets/Invoices";this._strDataObject="Invoices";this.getData();this._strCK="lns";this._strCKExp=1;this._lnsSeperator="|";this.getFilterField("ctlNotExported")&&this.getFilterField("ctlExportedOnly")&&($find(this.getFilterField("ctlNotExported").get_id())._element.setAttribute("onclick",String.format('$find("{0}").checkNotExport()',this._element.id)),$find(this.getFilterField("ctlExportedOnly").get_id())._element.setAttribute("onclick",String.format('$find("{0}").f()',this._element.id)));Rebound.GlobalTrader.Site.Controls.DataListNuggets.Invoices.callBaseMethod(this,"initialize")},dispose:function(){this.isDisposed||(this._ibtnEmail&&(this._ibtnEmail=null),this._frmConfirm&&this._frmConfirm.dispose(),this._frmConfirm=null,this._blnPageLoad=null,this._InvoiceFormat=null,this.closePopup(),Rebound.GlobalTrader.Site.Controls.DataListNuggets.Invoices.callBaseMethod(this,"dispose"))},enableBulkButtons:function(n){this._ibtnPrint&&$R_IBTN.enableButton(this._ibtnPrint,n);this._ibtnEmail&&$R_IBTN.enableButton(this._ibtnEmail,n)},printInvoices:function(){var t=Boolean.parse(this.getFilterField("ctlCOC").getValue().toString()),i=Boolean.parse(this.getFilterField("ctlPackaginSlip").getValue().toString()),n=$R_FN.arrayToSingleString(this._table._aryCurrentValues,this._lnsSeperator);$R_FN.setCookie(this._strCK,n,this._strCKExp);n="";this._InvoiceFormat=="XML"?$R_FN.openPrintWindowWithMultiples($R_ENUM$PrintObject.XmlInvoice,n):t&&i?($R_FN.setCookie("coc",this.getFilterField("ctlCOC").getValue(),this._strCKExp),$R_FN.setCookie("Packaging",this.getFilterField("ctlPackaginSlip").getValue(),this._strCKExp),$R_FN.openPrintWindowWithMultiples($R_ENUM$PrintObject.InvoiceIncludeCOCPackaging,n)):Boolean.parse(this.getFilterField("ctlCOC").getValue().toString())?($R_FN.setCookie("coc",this.getFilterField("ctlCOC").getValue(),this._strCKExp),$R_FN.openPrintWindowWithMultiples($R_ENUM$PrintObject.Invoice,n)):Boolean.parse(this.getFilterField("ctlPackaginSlip").getValue().toString())?($R_FN.setCookie("Packaging",this.getFilterField("ctlPackaginSlip").getValue(),this._strCKExp),$R_FN.openPrintWindowWithMultiples($R_ENUM$PrintObject.InvoiceIncludePackaging,n)):($R_FN.setCookie("coc",this.getFilterField("ctlCOC").getValue(),this._strCKExp),$R_FN.openPrintWindowWithMultiples($R_ENUM$PrintObject.Invoice,n));n=null},selectionMade:function(){this.enableBulkButtons(this._table._arySelectedIndexes.length>0)},setupDataCall:function(){this._blnPageLoad&&(this._objData.addParameter("ExportedOnly","true"),this._objData.addParameter("RecentOnly","true"));this.enableBulkButtons(!1);this._blnPageLoad=!1},getDataOK:function(){for(var n,i,t=0,r=this._objResult.Results.length;t<r;t++)n=this._objResult.Results[t],i=[$RGT_nubButton_Invoice(n.ID,n.No),$R_FN.writeDoubleCellValue($RGT_nubButton_Company(n.CMNo,n.CM),$R_FN.setCleanTextValue(n.CustPONO)),$RGT_nubButton_SalesOrder(n.SONo,n.SO),$R_FN.setCleanTextValue(n.Date),n.Value],this._table.addRow(i,n.ID,!1),i=null,n=null},showConfirmForm:function(){this._frmConfirm._strInvoices=this._table._aryCurrentValues;this._frmConfirm._blnCOC=this.getFilterField("ctlCOC").getValue();this._frmConfirm._blnPackagingSlip=this.getFilterField("ctlPackaginSlip").getValue();this._frmConfirm._ClientNo=this._ClientNo;this._frmConfirm._AllowGenerateXml=this._AllowGenerateXml;this.showForm(this._frmConfirm,!0)},hideConfirmForm:function(){this.showForm(this._frmConfirm,!1)},saveCeaseComplete:function(){this.hideConfirmForm()},checkExportOnly:function(){Boolean.parse(this.getFilterField("ctlNotExported").getValue().toString())==!0&&(this.getFilterField("ctlNotExported").enableField(!1),this.getFilterField("ctlExportedOnly").enableField(!0))},checkNotExport:function(){Boolean.parse(this.getFilterField("ctlExportedOnly").getValue().toString())==!0&&(this.getFilterField("ctlNotExported").enableField(!0),this.getFilterField("ctlExportedOnly").enableField(!1))},COCChecked:function(){Boolean.parse(this.getFilterField("ctlPackaginSlip").getValue().toString())==!0&&this.setFilterFieldValue("ctlPackaginSlip",!Boolean.parse(this.getFilterField("ctlCOC").getValue().toString()))},PackagingChecked:function(){Boolean.parse(this.getFilterField("ctlCOC").getValue().toString())==!0&&this.setFilterFieldValue("ctlCOC",!Boolean.parse(this.getFilterField("ctlPackaginSlip").getValue().toString()))},openFormatModal:function(){$("#overlay").css("display","block");let n=this._AllowGenerateXml&&this._ClientNo=="108"?"XML":"PDF";$("input:radio[name=rdInvoiceFormat]").val([n]);$("#optionXML").css("display",this._AllowGenerateXml?"block":"none");$("#formatModal").dialog("open")},selectInvoicePrintFormat:function(){this._InvoiceFormat=this._AllowGenerateXml?$('input[name="rdInvoiceFormat"]:checked').val():"PDF";this.printInvoices();this.closePopup()},closePopup:function(){$("#overlay").css("display","none");$("#formatModal").dialog("close")}};Rebound.GlobalTrader.Site.Controls.DataListNuggets.Invoices.registerClass("Rebound.GlobalTrader.Site.Controls.DataListNuggets.Invoices",Rebound.GlobalTrader.Site.Controls.DataListNuggets.Base,Sys.IDisposable);