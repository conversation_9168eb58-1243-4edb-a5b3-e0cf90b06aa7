Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.Invoices_Confirm=function(n){Rebound.GlobalTrader.Site.Controls.Forms.Invoices_Confirm.initializeBase(this,[n]);this._strInvoices="";this._blnCOC=!1;this._blnPackagingSlip=!1;this._ClientNo="";this._AllowGenerateXml=!1};Rebound.GlobalTrader.Site.Controls.Forms.Invoices_Confirm.prototype={initialize:function(){Rebound.GlobalTrader.Site.Controls.Forms.Invoices_Confirm.callBaseMethod(this,"initialize");this.addShown(Function.createDelegate(this,this.formShown))},dispose:function(){this.isDisposed||(this._ctlConfirm&&this._ctlConfirm.dispose(),this._ctlConfirm=null,this._strInvoices=null,this._blnCOC=null,this._ClientNo=null,this._AllowGenerateXml=null,Rebound.GlobalTrader.Site.Controls.Forms.Invoices_Confirm.callBaseMethod(this,"dispose"))},formShown:function(){this._blnFirstTimeShown&&(this._ctlConfirm=this.getFieldComponent("ctlConfirm"),this._ctlConfirm.addClickYesEvent(Function.createDelegate(this,this.yesClicked)),this._ctlConfirm.addClickNoEvent(Function.createDelegate(this,this.noClicked)));this.showFormatOptions()},yesClicked:function(){this.showSaving(!0);var t=this._AllowGenerateXml&&document.querySelector('.optionXML input[type="radio"]').checked?!0:!1,n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/InvoiceMainInfo");n.set_DataObject("InvoiceMainInfo");n.set_DataAction("SaveInvoiceEmail");n.addParameter("Invoices",this._strInvoices);n.addParameter("coc",this._blnCOC);n.addParameter("packagingSlip",this._blnPackagingSlip);n.addParameter("isXmlFormat",t);n.addDataOK(Function.createDelegate(this,this.saveComplete));n.addError(Function.createDelegate(this,this.saveError));n.addTimeout(Function.createDelegate(this,this.saveError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null;this.onSave()},noClicked:function(){this.showSaving(!1);this.onNotConfirmed()},saveError:function(n){this.showSaving(!1);this._strErrorMessage=n._errorMessage;this.onSaveError()},saveComplete:function(n){this.showSaving(!1);n._result.Result>=1?(this.onSaveComplete(),n._result.Result==1?alert($R_RES.FinanceContactNotFoundMessage+" "+n._result.Invoices):n._result.Result==2?alert($R_RES.InvoiceProgressMessage+"\n\n"+$R_RES.FinanceContactNotFoundMessage+" "+n._result.Invoices):alert($R_RES.InvoiceProgressMessage)):(this._strErrorMessage=n._errorMessage,this.onSaveError())},showFormatOptions:function(){if(!this._AllowGenerateXml){document.querySelector(".optionXML").classList.add("invisible");document.querySelector('.optionPDF input[type="radio"]').checked=!0;return}document.querySelector(".optionXML").classList.remove("invisible");this._ClientNo=="108"?document.querySelector('.optionXML input[type="radio"]').checked=!0:document.querySelector('.optionPDF input[type="radio"]').checked=!0}};Rebound.GlobalTrader.Site.Controls.Forms.Invoices_Confirm.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.Invoices_Confirm",Rebound.GlobalTrader.Site.Controls.Forms.Base,Sys.IDisposable);