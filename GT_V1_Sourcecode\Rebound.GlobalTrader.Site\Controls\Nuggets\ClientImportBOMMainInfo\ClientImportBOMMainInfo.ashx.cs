//--------------------------------------------------------------------------------------------------------
// RP 11.12.2009:
// - clear related dropdown cache
//--------------------------------------------------------------------------------------------------------
//Marker     Changed by      Date         Remarks
//[001]      Suhail       25/04/2018   Added contact and company name while sending mail via Add New Communication Note
using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;
using System.IO;
using System.Text;

namespace Rebound.GlobalTrader.Site.Controls.Nuggets.Data
{
    [WebService(Namespace = "http://tempuri.org/")]
    [WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    public class ClientImportBOMMainInfo : Rebound.GlobalTrader.Site.Data.Base
    {

        public override void ProcessRequest(HttpContext context)
        {
            if (base.init(context))
            {
                switch (Action)
                {
                    case "GetData": GetData(); break;
                    case "Delete": Delete(); break;
                    case "SaveEdit": SaveEdit(); break;
                    case "ExportToCSV": ExportToCSV(); break;
                    case "savePurchaseHUBData": savePurchaseHUBData(); break;
                    case "BOMReleaseRequirement": BOMReleaseRequirement(); break;
                    case "UpdateBOMByPH": UpdateBOMByPH(); break;
                    //case "updateBOMStatusToClosed": updateBOMStatusToClosed(); break;
                    case "BOMNoBidRequirement": BOMNoBidRequirement(); break;
                    case "MarkAsComplete": MarkAsComplete(); break;
                    case "SaveAsHUBRFQ": SaveAsHUBRFQ(); break;
                    default: WriteErrorActionNotFound(); break;
                }
            }
        }

        /// <summary>
        /// Gets data for a stock item
        /// </summary>
        public JsonObject GetData(BOM bom)
        {
            JsonObject jsn = null;
            try
            {
                if (bom != null)
                {
                    jsn = new JsonObject();
                    jsn.AddVariable("Code", bom.ClientBOMCode);
                    jsn.AddVariable("Name", bom.ClientBOMName);
                    jsn.AddVariable("Notes", Functions.ReplaceLineBreaks(bom.Notes));
                    jsn.AddVariable("DLUP", Functions.FormatDLUP(bom.DLUP, bom.UpdatedBy));
                    jsn.AddVariable("InActive", bom.Inactive);
                    jsn.AddVariable("Company", Functions.ReplaceLineBreaks(bom.CompanyName));
                    jsn.AddVariable("Contact", Functions.ReplaceLineBreaks(bom.ContactName));
                    jsn.AddVariable("CurrencyName", Functions.ReplaceLineBreaks(bom.CurrencyName));
                    jsn.AddVariable("CompanyNo", bom.CompanyNo);
                    jsn.AddVariable("ContactNo", bom.ContactNo);
                    jsn.AddVariable("Salesman", bom.Salesman);
                    jsn.AddVariable("SalesmanId", bom.SalesmanId);
                    jsn.AddVariable("blnReqToPoHub", bom.RequestToPOHubBy > 0);
                    jsn.AddVariable("blnRelease", bom.StatusValue == (int)BLL.BOMStatus.List.Released);
                    jsn.AddVariable("IsPoHub", SessionManager.IsPOHub);
                    jsn.AddVariable("blnBomCount", bom.BomCount);
                    jsn.AddVariable("BOMStatus", bom.BOMStatus);
                    jsn.AddVariable("StatusValue", bom.StatusValue);
                    jsn.AddVariable("CurrencyCode", bom.CurrencyCode);
                    jsn.AddVariable("CurrencyNo", bom.CurrencyNo);
                    jsn.AddVariable("Currency_Code", bom.Currency_Code);
                    jsn.AddVariable("CurrentSupplier", bom.CurrentSupplier);
                    jsn.AddVariable("QuoteRequired", Functions.FormatDate(bom.QuoteRequired));
                    jsn.AddVariable("AllItemHasSourcing", bom.AllItemHasSourcing > 0);
                    jsn.AddVariable("AS9120", bom.AS9120);
                    jsn.AddVariable("Requestedby", bom.Requestedby);
                    jsn.AddVariable("Releasedby", bom.Releasedby);
                    jsn.AddVariable("IsClosed", bom.StatusValue == (int)BLL.BOMStatus.List.Closed);
                    jsn.AddVariable("UpdatedBy", bom.UpdatedBy);
                    jsn.AddVariable("isNoBidCount", bom.NoBidCount>0?true:false);
                    jsn.AddVariable("UpdateByPH", bom.UpdateByPH);
                    jsn.AddVariable("RequestToPOHubBy", bom.RequestToPOHubBy );
                    jsn.AddVariable("AssignedUser", bom.AssignedUser);
                    jsn.AddVariable("Contact2Id", bom.Contact2Id);
                    jsn.AddVariable("Contact2Name", Functions.ReplaceLineBreaks(bom.Contact2Name));
                    jsn.AddVariable("ValidMessage", bom.ValidationMessage);
                    jsn.AddVariable("IsReqInValid", bom.IsReqInValid);
                   // jsn.AddVariable("RecordRemaining", bom.RecordRemaining);
                    jsn.AddVariable("ClientBmStatus", bom.Status);
                    jsn.AddVariable("RecordsProcessed", bom.RecordsProcessed);
                    jsn.AddVariable("RecordsRemaining", bom.RecordsRemaining);
                    jsn.AddVariable("BomId", bom.BOMId);
                    jsn.AddVariable("BomName", bom.BOMName);
                }
                bom = null;
            }
            catch (Exception ex)
            {
                WriteError(ex);
            }
            return jsn;
        }

        public void GetData()
        {
            BOM bom = BOM.GetClientBOMDetails(ID,SessionManager.LoginID??0);
            OutputResult(GetData(bom));
            bom = null;
        }

        private void Delete()
        {
            try
            {
                //CacheManager.ClearStoredDropDown("BOM", new object[] { SessionManager.ClientID });
                JsonObject jsn = new JsonObject();
                bool blnOK = BOM.UpdateDelete(ID, LoginID);
                jsn.AddVariable("Result", blnOK);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            catch (Exception ex)
            {
                WriteError(ex);
            }
        }

        private void SaveEdit()
        {
            try
            {
                //CacheManager.ClearStoredDropDown("BOM", new object[] { SessionManager.ClientID });
                JsonObject jsn = new JsonObject();
                bool blnOK = true;
                //BOM bom = BOM.Get(ID);
                BOM bom = BOM.GetClientBOMDetails(ID,0);
                if (bom != null)
                {
                    bom.ClientBOMCode = GetFormValue_String("Code");
                    bom.ClientBOMName = GetFormValue_String("Name");
                    bom.Notes = GetFormValue_String("Notes");
                    bom.UpdatedBy = LoginID;
                    bom.Inactive = GetFormValue_Boolean("Inactive");
                    bom.CompanyName = GetFormValue_String("Company");
                    bom.ContactNo = GetFormValue_Int("Contact");
                    bom.ClientNo = (int)SessionManager.ClientID;
                    bom.CurrencyNo = GetFormValue_Int("Currency");
                    bom.Salesman = GetFormValue_String("SalesPersion");
                    blnOK = bom.UpdateClientBOM();
                }
                jsn.AddVariable("Result", blnOK);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            catch (Exception ex)
            {
                WriteError(ex);
            }
        }

        /// <summary>
        /// Writes report to a CSV file and returns the filename 
        /// </summary>
        /// <returns></returns>
        public void ExportToCSV()
        {
            JsonObject jsn = new JsonObject();
            try
            {
                ////List<List<object>> lstData = CustomerRequirement.GetBOMListForCRList(ID, SessionManager.ClientID);currencyCode               
                JsonObject jsnItems = new JsonObject(true);
                string CurrencyCode = GetFormValue_String("Currency_Code");
                //return saved filename to the page
                string strFilename = FileUploadManager.ExportToCSV((int)Rebound.GlobalTrader.BLL.Report.List.RequirementWithBOM, ID, CurrencyCode,"E");
                jsn.AddVariable("Filename", String.Format("{0}/{1}", FileUploadManager.GetTemporaryUploadFilePath(), strFilename));
                OutputResult(jsn);
            }
            catch (Exception e)
            {
                WriteError(e);
            }
            finally
            {
                jsn.Dispose();
                jsn = null;
            }
        }


        /// <summary>
        /// Writes report to a CSV file and returns the filename 
        /// </summary>
        /// <returns></returns>
        public void savePurchaseHUBData()
        {
            JsonObject jsn = new JsonObject();
            try
            {
                String ValidateMessage = null;
                System.Int32 AssignUserNo = GetFormValue_Int("AssignUserNo");
                if (AssignUserNo > 0)
                {
                    bool blnOK = BOM.UpdatePurchaseQuote(ID, LoginID, (int)BOMStatus.List.RPQ, AssignUserNo,out ValidateMessage);
                    if (blnOK)
                    {
                        WebServices servic = new WebServices();
                        string BOMCode = GetFormValue_String("BOMCode");
                        string BOMName = GetFormValue_String("BOMName");
                        string BomCompanyName = GetFormValue_String("BomCompanyName");
                        int? BomCompanyNo = GetFormValue_Int("BomCompanyNo");
                        string aryRecipientLoginIDsCC = GetFormValue_String("aryRecipientLoginIDs");

                        System.Int32 Contact2No = GetFormValue_Int("Contact2No"); // Also send mail to  Added Contact 2  05-04-2018

                        servic.NotifyPurchaseRequestBom(AssignUserNo.ToString() , (SessionManager.POHubMailGroupId ?? 0).ToString(), string.Format(Functions.GetGlobalResource("Messages", "PurchaseRequest"), BOMName), BOMCode, BOMName, ID, BomCompanyName, BomCompanyNo, aryRecipientLoginIDsCC);
                    }
                    jsn.AddVariable("Result", blnOK);
                    jsn.AddVariable("ValidateMessage", ValidateMessage);
                    OutputResult(jsn);
                    jsn.Dispose(); jsn = null;
                }
            }
            catch (Exception ex)
            {
                WriteError(ex);
            }
        }


        /// <summary>
        /// Release customer requirement for client user
        /// </summary>
        public void BOMNoBidRequirement()
        {
            try
            {
                string BOMCode = GetFormValue_String("BOMCode");
                string BOMName = GetFormValue_String("BOMName");
                string BomCompanyName = GetFormValue_String("BomCompanyName");
                int? BomCompanyNo = GetFormValue_Int("BomCompanyNo");
                int? UpdatedBy = GetFormValue_Int("UpdatedBy");
                string Notes = GetFormValue_String("Notes");
                bool blnResult = CustomerRequirement.BOMNoBidRequirement(
                    ID,
                    LoginID,
                    Notes
                );
                if (blnResult)
                {
                    WebServices servic = new WebServices();
                    // BLL.BOM bom = BLL.BOM.Get(ID);
                    servic.NotifyNoBidBom((UpdatedBy ?? 0).ToString(), "", Functions.GetGlobalResource("Messages", "BOMNoBid"), ID, BOMCode, BOMName, BomCompanyNo, BomCompanyName, true, ID);
                }
                JsonObject jsn = new JsonObject();
                jsn.AddVariable("Result", blnResult);
                OutputResult(jsn);
                jsn.Dispose();
                jsn = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }

        /// <summary>BOMNoBidRequirement
        /// Release customer requirement for client user
        /// </summary>
        public void BOMReleaseRequirement()
        {
            try
            {
                string BOMCode = GetFormValue_String("BOMCode");
                string BOMName = GetFormValue_String("BOMName");
                string BomCompanyName = GetFormValue_String("BomCompanyName");
                int? BomCompanyNo = GetFormValue_Int("BomCompanyNo");
                int? UpdatedBy = GetFormValue_Int("UpdatedBy");
                bool blnResult = CustomerRequirement.BOMReleaseRequirement(
                    ID,
                    LoginID
                );
                if (blnResult)
                {
                    WebServices servic = new WebServices();
                   // BLL.BOM bom = BLL.BOM.Get(ID);
                    servic.NotifyReleaseBom((UpdatedBy ?? 0).ToString(), "", Functions.GetGlobalResource("Messages", "BOMReleased"), ID, BOMCode, BOMName, BomCompanyNo, BomCompanyName,true,ID);
                }
                JsonObject jsn = new JsonObject();
                jsn.AddVariable("Result", blnResult);
                OutputResult(jsn);
                jsn.Dispose();
                jsn = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }

        /// <summary>
        /// Assigning BOM Ids to Loggedin User.
        /// </summary>
        private void UpdateBOMByPH()
        {
            try
            {         
                string BOMIdList = GetFormValue_String("BOMIds");//2,3,4 etc
                string selectedUser = GetFormValue_String("SelectedUser");//2344
                string selectedUserName = GetFormValue_String("SelectedUserName");
                bool blnOK = BOM.UpdateBOMByPH(BOMIdList, Convert.ToInt32(selectedUser),SessionManager.LoginID);
                //bool blnOK = BOM.UpdateBOMByPH(BOMIdList, SessionManager.LoginID);
                string strToLoginsArray=string.Empty;
                if (blnOK)
                {
                   WebServices servic = new WebServices();
                   BLL.BOM bom = BLL.BOM.GetUpdatedByListFromBOMIdList(BOMIdList);
                   if (!string.IsNullOrEmpty(selectedUser))
                   {
                       strToLoginsArray = bom.UpdatedByList + "," + selectedUser;
                   }
                   else
                   {
                       strToLoginsArray = bom.UpdatedByList;
                   }

                   servic.NotifyAssignedToPOHUBUser(strToLoginsArray, "", Functions.GetGlobalResource("Messages", "BOMAssigned"), bom.BOMName, selectedUserName);
                }
                JsonObject jsn = new JsonObject();
                jsn.AddVariable("Result", blnOK);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            catch (Exception ex)
            {
                WriteError(ex);
            }
        }


        /// <summary>
        /// update BOM Status To Closed
        /// </summary>
        /// <returns></returns>
        public void MarkAsComplete()
        {
            JsonObject jsn = new JsonObject();
            try
            {
                bool blnOK = BOM.ClientBOMMarkComplete(ID, LoginID);
               
                jsn.AddVariable("Result", blnOK);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            catch (Exception ex)
            {
                WriteError(ex);
            }
        }

        /// <summary>
        /// update BOM Status To Closed
        /// </summary>
        /// <returns></returns>
        public void SaveAsHUBRFQ()
        {
            JsonObject jsn = new JsonObject();
            try
            {
                string errorMessage = string.Empty;
                bool blnOK = BOM.SaveAsHUBRFQ(ID, LoginID,out errorMessage);
                if (!string.IsNullOrEmpty(errorMessage))
                    throw new Exception(errorMessage);
                jsn.AddVariable("Result", blnOK);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            catch (Exception ex)
            {
                WriteError(ex);
            }
        }
    }
}
