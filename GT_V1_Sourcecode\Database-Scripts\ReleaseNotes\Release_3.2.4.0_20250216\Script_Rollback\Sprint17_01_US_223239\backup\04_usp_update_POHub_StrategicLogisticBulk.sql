﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

/* 
===========================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-220887]		An.TranTan			19-Nov-2024		Update			Bulk edit strategic logistics
===========================================================================================
*/
CREATE OR ALTER   PROCEDURE [dbo].[usp_update_POHub_StrategicLogisticBulk] 
	@EpoIDs NVARCHAR(MAX)
	,@Action VARCHAR(50)
	,@LoginID INT = NULL
	,@RowsAffected INT = NULL OUTPUT
AS
BEGIN
	DECLARE @Epo TABLE(ID INT);

	INSERT INTO @Epo(ID)
	SELECT CAST(VALUE AS INT)
	FROM STRING_SPLIT(@EpoIDs, ',')
	WHERE TRY_CAST(VALUE AS INT) IS NOT NULL

	IF @Action = 'Remove'
	BEGIN
		UPDATE epo
		SET epo.Inactive = 1
			,epo.InactiveBy = @LoginID
			,epo.InactiveDate = CURRENT_TIMESTAMP
			,epo.UpdatedBy = @LoginID
		FROM [BorisGlobalTraderImports].dbo.tbEpo epo
		JOIN @Epo t on t.ID = epo.EpoId
	END
	ELSE BEGIN --set quantity to zero
		UPDATE epo
		SET epo.Quantity = 0
			,epo.UpdatedBy = @LoginID
			,epo.DLUP = CURRENT_TIMESTAMP
		FROM [BorisGlobalTraderImports].dbo.tbEpo epo
		JOIN @Epo temp on temp.ID = epo.EpoId
	END

	SELECT @RowsAffected = @@ROWCOUNT
END
GO


