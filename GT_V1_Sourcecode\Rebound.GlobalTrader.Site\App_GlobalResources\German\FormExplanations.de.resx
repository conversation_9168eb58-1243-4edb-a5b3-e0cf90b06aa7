﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="CommunicationLogType_Add" xml:space="preserve">
    <value>Redigieren Sie die Sonderkommandos und die Presse &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="CommunicationLogType_Edit" xml:space="preserve">
    <value>Tragen Sie alle Details des neuen Einzelteils ein und betätigen Sie sich &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="CompanyType_Add" xml:space="preserve">
    <value>Tragen Sie alle Details des neuen Einzelteils ein und betätigen Sie sich &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="CompanyType_Edit" xml:space="preserve">
    <value>Redigieren Sie die Sonderkommandos und die Presse &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="CountryAdd_EnterDetail" xml:space="preserve">
    <value>Tragen Sie alle Details des neuen Einzelteils ein und betätigen Sie sich &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="Country_Edit" xml:space="preserve">
    <value>Redigieren Sie die Sonderkommandos und die Presse &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="Currency_Add" xml:space="preserve">
    <value>Tragen Sie alle Details des neuen Einzelteils ein und betätigen Sie sich &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="Currency_Edit" xml:space="preserve">
    <value>Redigieren Sie die Sonderkommandos und die Presse &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="Division_Add" xml:space="preserve">
    <value>Tragen Sie alle Details des neuen Einzelteils ein und betätigen Sie sich &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="Division_Edit" xml:space="preserve">
    <value>Redigieren Sie die Sonderkommandos und die Presse &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="EnterDetailsAndPressSave" xml:space="preserve">
    <value>Melden Sie alle Sonderkommandos und Presse an  &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="ManufacturerMainInfo_Edit" xml:space="preserve">
    <value>Redigieren Sie die Sonderkommandos und die Presse &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="GlobalCountryList_Edit" xml:space="preserve">
    <value>Redigieren Sie die Sonderkommandos und die Presse &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="GlobalCurrencyList_Edit" xml:space="preserve">
    <value>Redigieren Sie die Sonderkommandos und die Presse &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="IndustryType_Add" xml:space="preserve">
    <value>Tragen Sie alle Details des neuen Einzelteils ein und betätigen Sie sich &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="IndustryType_Edit" xml:space="preserve">
    <value>Redigieren Sie die Sonderkommandos und die Presse &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="Package_Add" xml:space="preserve">
    <value>Tragen Sie alle Details des neuen Einzelteils ein und betätigen Sie sich &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="Package_Edit" xml:space="preserve">
    <value>Redigieren Sie die Sonderkommandos und die Presse &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="POAdd_EnterDetail" xml:space="preserve">
    <value>Tragen Sie alle Details des neuen Einzelteils ein und betätigen Sie sich &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="Product_Add" xml:space="preserve">
    <value>Tragen Sie alle Details des neuen Einzelteils ein und betätigen Sie sich &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="Product_Edit" xml:space="preserve">
    <value>Redigieren Sie die Sonderkommandos und die Presse &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="QuoteAdd_EnterDetail" xml:space="preserve">
    <value>Tragen Sie alle Details des neuen Einzelteils ein und betätigen Sie sich &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="QuoteAdd_SelectCompany" xml:space="preserve">
    <value>Wählen Sie die Firma vor, für die Sie diesen Anführungsstrich hinzufügen möchten</value>
  </data>
  <data name="QuoteAdd_SelectLines" xml:space="preserve">
    <value>Wählen Sie die Linien von der Anforderung vor, die Sie in den neuen Anführungsstrich tragen und bedrängen möchten &lt;b&gt;Fortfahren&lt;/b&gt;</value>
  </data>
  <data name="QuoteAdd_SelectRequirement" xml:space="preserve">
    <value>Wählen Sie die Anforderungs-und Auftreten-Resultate vor, auf denen Sie diesen neuen Anführungsstrich gründen und bedrängen möchten &lt;b&gt;Fortfahren&lt;/b&gt;</value>
  </data>
  <data name="QuoteAdd_SelectSource" xml:space="preserve">
    <value>Wählen Sie die Quelle für das neue Einzelteil vor &lt;b&gt;Fortfahren&lt;/b&gt;</value>
  </data>
  <data name="ShipVia_Add" xml:space="preserve">
    <value>Tragen Sie alle Details des neuen Einzelteils ein und betätigen Sie sich &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="ShipVia_Edit" xml:space="preserve">
    <value>Redigieren Sie die Sonderkommandos und die Presse &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="SOAdd_EnterDetail" xml:space="preserve">
    <value>Tragen Sie alle Details des neuen Einzelteils ein und betätigen Sie sich &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="SOAdd_SelectCompany" xml:space="preserve">
    <value>Wählen Sie die Firma vor, für die Sie diesen Verkaufs-Auftrag hinzufügen möchten</value>
  </data>
  <data name="SOLine_EditDetails" xml:space="preserve">
    <value>Redigieren Sie die Sonderkommandos und die Presse &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="SOLine_SelectItem" xml:space="preserve">
    <value>Wählen Sie die Quelle für das neue Einzelteil vor &lt;b&gt;Fortfahren&lt;/b&gt;</value>
  </data>
  <data name="SOLine_SelectSource" xml:space="preserve">
    <value>Wählen Sie die Quelle für das neue Einzelteil vor &lt;b&gt;Fortfahren&lt;/b&gt;</value>
  </data>
  <data name="SOLines_Deallocate" xml:space="preserve">
    <value>Sind Sie Sie möchten die vorgewählten Verteilungen entfernen sicher?</value>
  </data>
  <data name="SOLines_Delete" xml:space="preserve">
    <value>Sind Sie Sie möchten die vorgewählten Einzelteile löschen sicher?</value>
  </data>
  <data name="StockLogReason_Add" xml:space="preserve">
    <value>Tragen Sie alle Details des neuen Einzelteils ein und betätigen Sie sich &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="StockLogReason_Edit" xml:space="preserve">
    <value>Redigieren Sie die Sonderkommandos und die Presse &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="Tax_Add" xml:space="preserve">
    <value>Tragen Sie alle Details des neuen Einzelteils ein und betätigen Sie sich &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="Tax_Edit" xml:space="preserve">
    <value>Redigieren Sie die Sonderkommandos und die Presse &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="Team_Add" xml:space="preserve">
    <value>Tragen Sie alle Details des neuen Einzelteils ein und betätigen Sie sich &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="Team_Edit" xml:space="preserve">
    <value>Redigieren Sie die Sonderkommandos und die Presse &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="Terms_Add" xml:space="preserve">
    <value>Tragen Sie alle Details des neuen Einzelteils ein und betätigen Sie sich &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="Terms_Edit" xml:space="preserve">
    <value>Redigieren Sie die Sonderkommandos und die Presse &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="User_Add" xml:space="preserve">
    <value>Tragen Sie alle Details des neuen Einzelteils ein und betätigen Sie sich &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="User_Edit" xml:space="preserve">
    <value>Redigieren Sie die Sonderkommandos und die Presse &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="Warehouse_Add" xml:space="preserve">
    <value>Tragen Sie alle Details des neuen Lagers ein und betätigen Sie sich &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="CompanyManufacturers_Delete" xml:space="preserve">
    <value>Sind Sie Sie möchten die vorgewählten Einzelteile löschen sicher?</value>
  </data>
  <data name="ManufacturerSuppliers_Delete" xml:space="preserve">
    <value>Sind Sie Sie möchten die vorgewählten Einzelteile löschen sicher?</value>
  </data>
  <data name="Reason_Add" xml:space="preserve">
    <value>Tragen Sie alle Details des neuen Einzelteils ein und betätigen Sie sich &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="Reason_Edit" xml:space="preserve">
    <value>Redigieren Sie die Sonderkommandos und die Presse &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="MailMessages_DeleteFolder" xml:space="preserve">
    <value>Sind Sie Sie möchten die vorgewählten Einzelteile löschen sicher?</value>
  </data>
  <data name="MailMessages_DeleteMessage" xml:space="preserve">
    <value>Sind Sie Sie möchten die vorgewählten Einzelteile löschen sicher?</value>
  </data>
  <data name="MailMessages_EditFolder" xml:space="preserve">
    <value>Tragen Sie einen neuen Namen für dieses Faltblatt ein und betätigen Sie sich &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="MailMessages_MoveMessage" xml:space="preserve">
    <value>Select a destination folder for this/these message(s) and press &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="MailMessages_NewFolder" xml:space="preserve">
    <value>Tragen Sie alle Details des neuen Einzelteils ein und betätigen Sie sich &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="MailMessages_NewMessage" xml:space="preserve">
    <value>Tragen Sie alle Details des neuen Einzelteils ein und betätigen Sie sich &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="ManufacturerCompanies_Add" xml:space="preserve">
    <value>Tragen Sie alle Details des neuen Einzelteils ein und betätigen Sie sich &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="ManufacturerCompanies_Delete" xml:space="preserve">
    <value>Sind Sie Sie möchten die vorgewählten Einzelteile löschen sicher?</value>
  </data>
  <data name="Sourcing_AddToReq" xml:space="preserve">
    <value>Sind Sie Sie möchten die vorgewählten Einzelteile der Anforderung hinzufügen sicher?</value>
  </data>
  <data name="Sourcing_EditOffer" xml:space="preserve">
    <value>Redigieren Sie die Sonderkommandos und die Presse &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="QuoteMainInfo_Edit" xml:space="preserve">
    <value>Redigieren Sie die Sonderkommandos und die Presse &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="CompanyAdd_Add" xml:space="preserve">
    <value>Tragen Sie alle Details des neuen Einzelteils ein und betätigen Sie sich &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="CompanyAddresses_Add" xml:space="preserve">
    <value>Tragen Sie alle Details des neuen Einzelteils ein und betätigen Sie sich &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="CompanyAddresses_Cease" xml:space="preserve">
    <value>Sind Sie Sie möchten diese Adresse aufhören sicher?</value>
  </data>
  <data name="CompanyAddresses_DefaultBill" xml:space="preserve">
    <value>Sind Sie Sie möchten diese Adresse die Rückstellung Gebührenzählungs-Rede für diese Firma halten sicher?</value>
  </data>
  <data name="CompanyAddresses_Edit" xml:space="preserve">
    <value>Redigieren Sie die Sonderkommandos und die Presse &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="CusReqAdd_EnterDetail" xml:space="preserve">
    <value>Tragen Sie alle Details des neuen Einzelteils ein und betätigen Sie sich &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="CusReqAdd_Notify" xml:space="preserve">
    <value>Melden Sie andere dieses Einzelteils</value>
  </data>
  <data name="CusReqAdd_SelectCompany" xml:space="preserve">
    <value>Wählen Sie die Firma für die neue Anforderung vor</value>
  </data>
  <data name="ManufacturerAdd_Add" xml:space="preserve">
    <value>Tragen Sie alle Details des neuen Einzelteils ein und betätigen Sie sich &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="ManufacturerSuppliers_AddEdit" xml:space="preserve">
    <value>Tragen Sie alle Details des neuen Einzelteils ein und betätigen Sie sich &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="QuoteLine_Add_EditDetails" xml:space="preserve">
    <value>Tragen Sie alle Details des neuen Einzelteils ein und betätigen Sie sich &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="QuoteLine_Add_SelectItem" xml:space="preserve">
    <value>Wählen Sie die Quelle für das neue Einzelteil vor &lt;b&gt;Fortfahren&lt;/b&gt;</value>
  </data>
  <data name="QuoteLine_Add_SelectSource" xml:space="preserve">
    <value>Wählen Sie die Quelle für die neue Linie vor</value>
  </data>
  <data name="QuoteLines_Close" xml:space="preserve">
    <value>Sind Sie Sie möchten diese Linie schließen sicher?</value>
  </data>
  <data name="QuoteLines_Edit" xml:space="preserve">
    <value>Redigieren Sie die Sonderkommandos und die Presse &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="SOLine_Add_EditDetails" xml:space="preserve">
    <value>Redigieren Sie die Sonderkommandos und die Presse &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="SOLine_Add_SelectItem" xml:space="preserve">
    <value>Wählen Sie die Quelle für das neue Einzelteil vor &lt;b&gt;Fortfahren&lt;/b&gt;</value>
  </data>
  <data name="SOLine_Add_SelectSource" xml:space="preserve">
    <value>Wählen Sie die Quelle für die neue Linie vor</value>
  </data>
  <data name="SOLine_Allocate_SelectStock" xml:space="preserve">
    <value>Wählen Sie das auf lagereinzelteil vor, um dieser Linie zuzuteilen und sich zu betätigen &lt;b&gt;Fortfahren&lt;/b&gt;</value>
  </data>
  <data name="SOLine_Alocate_EditDetails" xml:space="preserve">
    <value>Redigieren Sie die Sonderkommandos und die Presse &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="MailMessages_MarkAsToDo" xml:space="preserve">
    <value>Tragen Sie alle Details des neuen Einzelteils ein und betätigen Sie sich &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="ToDo_Delete" xml:space="preserve">
    <value>Sind Sie Sie möchten die vorgewählten Einzelteile löschen sicher?</value>
  </data>
  <data name="ToDo_Edit" xml:space="preserve">
    <value>Redigieren Sie die Sonderkommandos und die Presse &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="ToDo_MarkComplete" xml:space="preserve">
    <value>Sind Sie Sie möchten markieren vorgewählt, um Einzelteile zu tun sicher, wie komplett?</value>
  </data>
  <data name="ToDo_MarkIncomplete" xml:space="preserve">
    <value>Sind Sie Sie möchten markieren vorgewählt, um Einzelteile zu tun sicher, wie unvollständig?</value>
  </data>
  <data name="UserPreferences_Edit" xml:space="preserve">
    <value>Redigieren Sie die Sonderkommandos und die Presse &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="UserProfile_Edit" xml:space="preserve">
    <value>Redigieren Sie die Sonderkommandos und die Presse &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="Warehouse_Edit" xml:space="preserve">
    <value>Tragen Sie die geänderten Details für das Lager ein und betätigen Sie sich &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="UserProfile_ChangePassword" xml:space="preserve">
    <value>Tragen Sie Ihre neuen und alten Kennwörter ein und betätigen Sie sich &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="CusReqMainInfo_Edit" xml:space="preserve">
    <value>Redigieren Sie die Sonderkommandos und die Presse &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="CusReqMainInfo_AddAlternate" xml:space="preserve">
    <value>Tragen Sie alle Details des neuen Einzelteils ein und betätigen Sie sich &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="CusReqSourcingResults_Add" xml:space="preserve">
    <value>Redigieren Sie die Sonderkommandos und die Presse &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="CusReqSourcingResults_Edit" xml:space="preserve">
    <value>Redigieren Sie die Sonderkommandos und die Presse &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="QuoteAdd_Notify" xml:space="preserve">
    <value>Melden Sie andere dieses Einzelteils</value>
  </data>
  <data name="CRMALines_Add" xml:space="preserve">
    <value>Tragen Sie alle Details des neuen Einzelteils ein und betätigen Sie sich &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="CRMALines_Delete" xml:space="preserve">
    <value>Sind Sie Sie möchten die vorgewählten Einzelteile löschen sicher?</value>
  </data>
  <data name="CRMALines_Edit" xml:space="preserve">
    <value>Redigieren Sie die Sonderkommandos und die Presse &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="SRMALines_Add" xml:space="preserve">
    <value>Tragen Sie alle Details des neuen Einzelteils ein und betätigen Sie sich &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="SRMALines_Delete" xml:space="preserve">
    <value>Sind Sie Sie möchten die vorgewählten Einzelteile löschen sicher?</value>
  </data>
  <data name="SRMALines_Edit" xml:space="preserve">
    <value>Redigieren Sie die Sonderkommandos und die Presse &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="SRMAMainInfo_Edit" xml:space="preserve">
    <value>Redigieren Sie die Sonderkommandos und die Presse &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="SRMA_Add" xml:space="preserve">
    <value>Tragen Sie alle Details des neuen Einzelteils ein und betätigen Sie sich &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="SRMAAdd_EnterDetail" xml:space="preserve">
    <value>Tragen Sie alle Details des neuen Einzelteils ein und betätigen Sie sich &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="SRMAAdd_Notify" xml:space="preserve">
    <value>Melden Sie andere dieses Einzelteils</value>
  </data>
  <data name="SRMAAdd_SelectPO" xml:space="preserve">
    <value>Wählen Sie die Quelle für das neue Einzelteil vor &lt;b&gt;Fortfahren&lt;/b&gt;</value>
  </data>
  <data name="SRMALines_Add_EnterDetail" xml:space="preserve">
    <value>Redigieren Sie die Sonderkommandos und die Presse &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="SRMALines_Add_SelectItem" xml:space="preserve">
    <value>Wählen Sie die Kaufauftraglinie vor, die Sie als die Quelle für die neue Linie benutzen möchten </value>
  </data>
  <data name="CRMA_Add" xml:space="preserve">
    <value>Tragen Sie alle Details des neuen Einzelteils ein und betätigen Sie sich &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="CRMAAdd_EnterDetail" xml:space="preserve">
    <value>Tragen Sie alle Details des neuen Einzelteils ein und betätigen Sie sich &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="CRMAAdd_Notify" xml:space="preserve">
    <value>Melden Sie andere dieses Einzelteils</value>
  </data>
  <data name="CRMAAdd_SelectInvoice" xml:space="preserve">
    <value>Wählen Sie die Quelle für das neue Einzelteil vor &lt;b&gt;Fortfahren&lt;/b&gt;</value>
  </data>
  <data name="CRMALines_Add_EnterDetail" xml:space="preserve">
    <value>Redigieren Sie die Sonderkommandos und die Presse &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="CRMALines_Add_SelectItem" xml:space="preserve">
    <value>Wählen Sie die Quelle für das neue Einzelteil vor &lt;b&gt;Fortfahren&lt;/b&gt;</value>
  </data>
  <data name="CRMAMainInfo_Edit" xml:space="preserve">
    <value>Redigieren Sie die Sonderkommandos und die Presse &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="Feedback_Add" xml:space="preserve">
    <value>Schicken Sie uns Ihre Gedanken und Anmerkungen bitte ungefähr &lt;b&gt;Rebound Global:Trader&lt;/b&gt;
&lt;br /&gt;Wir werden an seiner kontinuierlichen Verbesserung festgelegt und würden lieben, alle mögliche Vorschläge zu hören, die Sie haben. 
&lt;br /&gt;Danke.</value>
  </data>
  <data name="SecurityGroupMembers_EditMembers" xml:space="preserve">
    <value>Wählen Sie die Mitglieder dieser Gruppe vor und betätigen Sie sich &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="SecurityGroupPermissionsGeneral_Edit" xml:space="preserve">
    <value>Redigieren Sie die Sonderkommandos und die Presse &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="SecurityGroupPermissionsReports_Edit" xml:space="preserve">
    <value>Redigieren Sie die Sonderkommandos und die Presse &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="SecurityGroups_Add" xml:space="preserve">
    <value>Tragen Sie alle Details des neuen Einzelteils ein und betätigen Sie sich &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="SecurityGroups_Delete" xml:space="preserve">
    <value>Sind Sie Sie möchten die vorgewählten Einzelteile löschen sicher?</value>
  </data>
  <data name="SecurityGroups_Edit" xml:space="preserve">
    <value>Redigieren Sie die Sonderkommandos und die Presse &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="CRMAReceivingLines_Receive" xml:space="preserve">
    <value>Bestätigen Sie den Empfang der Linie des Kunden-RMA und betätigen Sie sich &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="CompanyMainInfo_Edit" xml:space="preserve">
    <value>Redigieren Sie die Sonderkommandos und die Presse &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="POReceivingLines_Receive" xml:space="preserve">
    <value>Bestätigen Sie den Empfang der Kaufauftrag-Linie und betätigen Sie sich &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="POReceivingLines_Receive_NewOrExisting" xml:space="preserve">
    <value>Tragen Sie die empfangene Kaufauftrag-Linie gegen neue Waren in Anmerkung oder bestehende in ein und betätigen Sie sich &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="POReceivingLines_Receive_Detail" xml:space="preserve">
    <value>Tragen Sie alle Details des neuen Einzelteils ein und betätigen Sie sich &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="POReceivingLines_Receive_Header" xml:space="preserve">
    <value>Tragen Sie alle Details des neuen Einzelteils ein und betätigen Sie sich &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="POReceivingLines_Receive_Notify" xml:space="preserve">
    <value>Melden Sie andere dieses Einzelteils</value>
  </data>
  <data name="InvoiceLines_Add" xml:space="preserve">
    <value>Tragen Sie alle Details des neuen Einzelteils ein und betätigen Sie sich &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="InvoiceLines_Edit" xml:space="preserve">
    <value>Redigieren Sie die Sonderkommandos und die Presse &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="InvoiceLines_EditAllocation" xml:space="preserve">
    <value>Redigieren Sie die Sonderkommandos und die Presse &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="InvoiceMainInfo_Edit" xml:space="preserve">
    <value>Redigieren Sie die Sonderkommandos und die Presse &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="SecurityUserGroups_EditMembers" xml:space="preserve">
    <value>Wählen Sie die Mitglieder dieser Gruppe vor und betätigen Sie sich &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="SecurityUsers_Add" xml:space="preserve">
    <value>Tragen Sie alle Details des neuen Einzelteils ein und betätigen Sie sich &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="SecurityUsers_Disable" xml:space="preserve">
    <value>Sind Sie Sie möchten diese Sicherheits-Gruppe sperren sicher?</value>
  </data>
  <data name="SecurityUsers_Edit" xml:space="preserve">
    <value>Redigieren Sie die Sonderkommandos und die Presse &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="SecurityUsers_Enable" xml:space="preserve">
    <value>Sind Sie Sie möchten dieser Sicherheits-Gruppe ermöglichen sicher?</value>
  </data>
  <data name="SourcingLinks_Add" xml:space="preserve">
    <value>Tragen Sie alle Details des neuen Einzelteils ein und betätigen Sie sich &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="SourcingLinks_Delete" xml:space="preserve">
    <value>Sind Sie Sie möchten die vorgewählten Einzelteile löschen sicher?</value>
  </data>
  <data name="SourcingLinks_Edit" xml:space="preserve">
    <value>Redigieren Sie die Sonderkommandos und die Presse &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="UserProfile_ResetPassword" xml:space="preserve">
    <value>Sind Sie Sie möchten das Kennwort zurückstellen sicher? Es wird auf das username eingestellt.</value>
  </data>
  <data name="GlobalCurrencyList_Add" xml:space="preserve">
    <value>Tragen Sie alle Details des neuen Einzelteils ein und betätigen Sie sich &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="CurrencyAdd_EnterDetail" xml:space="preserve">
    <value>Redigieren Sie die Sonderkommandos und die Presse &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="CurrencyAdd_SelectSource" xml:space="preserve">
    <value>Wählen Sie die Quelle für das neue Einzelteil vor &lt;b&gt;Fortfahren&lt;/b&gt;</value>
  </data>
  <data name="POMainInfo_Edit" xml:space="preserve">
    <value>Redigieren Sie die Sonderkommandos und die Presse &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="POLines_Edit" xml:space="preserve">
    <value>Redigieren Sie die Sonderkommandos und die Presse &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="POLine_Add_EditDetails" xml:space="preserve">
    <value>Tragen Sie alle Details des neuen Einzelteils ein und betätigen Sie sich &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="POLine_Add_SelectItem" xml:space="preserve">
    <value>Wählen Sie die Quelle für das neue Einzelteil vor &lt;b&gt;Fortfahren&lt;/b&gt;</value>
  </data>
  <data name="POLine_Add_SelectSource" xml:space="preserve">
    <value>Wählen Sie die Quelle für die neue Linie vor</value>
  </data>
  <data name="POLines_Delete" xml:space="preserve">
    <value>Sind Sie Sie möchten die vorgewählten Einzelteile löschen sicher?</value>
  </data>
  <data name="POAdd_Notify" xml:space="preserve">
    <value>Melden Sie andere dieses Einzelteils</value>
  </data>
  <data name="SOAdd_Notify" xml:space="preserve">
    <value>Melden Sie andere dieses Einzelteils</value>
  </data>
  <data name="GIAdd_EnterDetail" xml:space="preserve">
    <value>Redigieren Sie die Sonderkommandos und die Presse &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="GIAdd_Notify" xml:space="preserve">
    <value>Melden Sie andere dieses Einzelteils</value>
  </data>
  <data name="GIAdd_SelectPO" xml:space="preserve">
    <value>Wählen Sie die Quelle für das neue Einzelteil vor &lt;b&gt;Fortfahren&lt;/b&gt;</value>
  </data>
  <data name="InvoiceAdd_EnterDetail" xml:space="preserve">
    <value>Redigieren Sie die Sonderkommandos und die Presse &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="InvoiceAdd_Notify" xml:space="preserve">
    <value>Melden Sie andere dieses Einzelteils</value>
  </data>
  <data name="InvoiceAdd_SelectSO" xml:space="preserve">
    <value>Select the Sales Order on which you would like to base this new Invoice and press &lt;b&gt;Continue&lt;/b&gt;</value>
  </data>
  <data name="ContactsForCompany_MakeDefaultPO" xml:space="preserve">
    <value>Stellen Sie als Company' ein; s fallen Kontakt für Kaufaufträge zurück</value>
  </data>
  <data name="ContactsForCompany_MakeDefaultSO" xml:space="preserve">
    <value>Stellen Sie als Company' ein; s fallen Kontakt für Verkaufs-Aufträge zurück</value>
  </data>
  <data name="SOShippingLines_Ship" xml:space="preserve">
    <value>Bestätigen Sie den Versand der Verkaufs-Auftrags-Linie und betätigen Sie sich &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="SOShippingLines_Ship_Detail" xml:space="preserve">
    <value>Tragen Sie alle Details des neuen Einzelteils ein und betätigen Sie sich &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="SOShippingLines_Ship_NewHeader" xml:space="preserve">
    <value>Tragen Sie Details der neuen Rechnung ein</value>
  </data>
  <data name="SOShippingLines_Ship_Notify" xml:space="preserve">
    <value>Melden Sie andere dieses Einzelteils</value>
  </data>
  <data name="Currency_EditRates" xml:space="preserve">
    <value>Redigieren Sie die Sonderkommandos und die Presse &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="CountryAdd_SelectSource" xml:space="preserve">
    <value>Wählen Sie die Quelle für das neue Einzelteil vor &lt;b&gt;Fortfahren&lt;/b&gt;</value>
  </data>
  <data name="SOMainInfo_Edit" xml:space="preserve">
    <value>Redigieren Sie die Sonderkommandos und die Presse &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="StockAllocations_Deallocate" xml:space="preserve">
    <value>Sind Sie Sie möchten die vorgewählten Einzelteile löschen sicher?</value>
  </data>
  <data name="StockMainInfo_Edit" xml:space="preserve">
    <value>Redigieren Sie die Sonderkommandos und die Presse &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="StockMainInfo_Quarantine" xml:space="preserve">
    <value>Sind Sie Sie möchten diesen Vorrat unter Quarantäne stellen sicher?</value>
  </data>
  <data name="StockMainInfo_Split" xml:space="preserve">
    <value>Redigieren Sie die Sonderkommandos und die Presse &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="CompanyPurchaseInfo_Edit" xml:space="preserve">
    <value>Redigieren Sie die Sonderkommandos und die Presse &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="CompanySalesInfo_Edit" xml:space="preserve">
    <value>Redigieren Sie die Sonderkommandos und die Presse &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="StockMainInfo_MakeAvailable" xml:space="preserve">
    <value>Sind Sie Sie möchten diesen Vorrat freigeben sicher?</value>
  </data>
  <data name="CompanyContactLog_AddEdit" xml:space="preserve">
    <value>Tragen Sie alle Details des neuen Einzelteils ein und betätigen Sie sich &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="Tax_EditRates" xml:space="preserve">
    <value>Redigieren Sie die Sonderkommandos und die Presse &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="LotItems_Delete_Service" xml:space="preserve">
    <value>Sind Sie Sie möchten die vorgewählten Einzelteile löschen sicher?</value>
  </data>
  <data name="LotItems_Delete_Stock" xml:space="preserve">
    <value>Sind Sie Sie möchten die vorgewählten Einzelteile löschen sicher?</value>
  </data>
  <data name="LotItems_Transfer_Service" xml:space="preserve">
    <value>Wählen Sie das neue Los vor, um die Dienstleistungen auf zu bringen und Sie zu bestätigen seien Sie sicher</value>
  </data>
  <data name="LotItems_Transfer_Stock" xml:space="preserve">
    <value>bringen und Sie zu bestätigen seien Sie sicher</value>
  </data>
  <data name="LotMainInfo_Edit" xml:space="preserve">
    <value>Redigieren Sie die Sonderkommandos und die Presse &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="ContactExtendedInfo_Edit" xml:space="preserve">
    <value>Geben Sie die ausgedehnten Informationen für den Kontakt ein und betätigen Sie sich &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="CreditLines_Add" xml:space="preserve">
    <value>Tragen Sie alle Details des neuen Einzelteils ein und betätigen Sie sich &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="CreditLines_Delete" xml:space="preserve">
    <value>Sind Sie Sie möchten die vorgewählten Einzelteile löschen sicher?</value>
  </data>
  <data name="CreditLines_Edit" xml:space="preserve">
    <value>Redigieren Sie die Sonderkommandos und die Presse &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="CreditMainInfo_Edit" xml:space="preserve">
    <value>Redigieren Sie die Sonderkommandos und die Presse &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="CreditLine_Add_EditDetails" xml:space="preserve">
    <value>Redigieren Sie die Sonderkommandos und die Presse &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="CreditLine_Add_SelectItem" xml:space="preserve">
    <value>Wählen Sie die Quelle für das neue Einzelteil vor &lt;b&gt;Fortfahren&lt;/b&gt;</value>
  </data>
  <data name="CreditLine_Add_SelectSource" xml:space="preserve">
    <value>Wählen Sie die Quelle für das neue Einzelteil vor</value>
  </data>
  <data name="CompanyManufacturers_AddEdit" xml:space="preserve">
    <value>Tragen Sie alle Details des neuen Einzelteils ein und betätigen Sie sich &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="LotAdd_Add" xml:space="preserve">
    <value>Tragen Sie alle Details des neuen Einzelteils ein und betätigen Sie sich &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="ServiceAdd_Add" xml:space="preserve">
    <value>Tragen Sie alle Details des neuen Einzelteils ein und betätigen Sie sich &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="SOLines_Edit" xml:space="preserve">
    <value>Redigieren Sie die Sonderkommandos und die Presse &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="Credit_Add" xml:space="preserve">
    <value>Tragen Sie alle Details des neuen Einzelteils ein und betätigen Sie sich &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="CreditAdd_EnterDetail" xml:space="preserve">
    <value>Tragen Sie alle Details des neuen Einzelteils ein und betätigen Sie sich &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="CreditAdd_Notify" xml:space="preserve">
    <value>Melden Sie andere dieses Einzelteils</value>
  </data>
  <data name="CreditAdd_SelectSource" xml:space="preserve">
    <value>Wählen Sie die Quelle für das neue Einzelteil vor &lt;b&gt;Fortfahren&lt;/b&gt;</value>
  </data>
  <data name="StockAdd_Add" xml:space="preserve">
    <value>Tragen Sie alle Details des neuen Einzelteils ein und betätigen Sie sich &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="Debit_Add" xml:space="preserve">
    <value>Tragen Sie alle Details des neuen Einzelteils ein und betätigen Sie sich &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="DebitAdd_EnterDetail" xml:space="preserve">
    <value>Tragen Sie alle Details des neuen Einzelteils ein und betätigen Sie sich &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="DebitAdd_Notify" xml:space="preserve">
    <value>Melden Sie andere dieses Einzelteils</value>
  </data>
  <data name="DebitAdd_SelectPO" xml:space="preserve">
    <value>Wählen Sie die Quelle für das neue Einzelteil vor &lt;b&gt;Fortfahren&lt;/b&gt;</value>
  </data>
  <data name="DebitLine_Add_EditDetails" xml:space="preserve">
    <value>Redigieren Sie die Sonderkommandos und die Presse &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="DebitLine_Add_SelectSource" xml:space="preserve">
    <value>Wählen Sie die Quelle für die neue Linie vor</value>
  </data>
  <data name="DebitLines_Add" xml:space="preserve">
    <value>Tragen Sie alle Details des neuen Einzelteils ein und betätigen Sie sich &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="DebitLines_Delete" xml:space="preserve">
    <value>Sind Sie Sie möchten die vorgewählten Einzelteile löschen sicher?</value>
  </data>
  <data name="DebitLines_Edit" xml:space="preserve">
    <value>Redigieren Sie die Sonderkommandos und die Presse &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="DebitMainInfo_Edit" xml:space="preserve">
    <value>Redigieren Sie die Sonderkommandos und die Presse &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="CRMAReceivingLines_Receive_Detail" xml:space="preserve">
    <value>Tragen Sie alle Details des neuen Einzelteils ein und betätigen Sie sich &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="CRMAReceivingLines_Receive_Notify" xml:space="preserve">
    <value>Melden Sie andere dieses Einzelteils</value>
  </data>
  <data name="MailMessageGroupMembers_EditMembers" xml:space="preserve">
    <value>Redigieren Sie die Sonderkommandos und die Presse &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="MailMessageGroups_Add" xml:space="preserve">
    <value>Tragen Sie alle Details des neuen Einzelteils ein und betätigen Sie sich &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="MailMessageGroups_Delete" xml:space="preserve">
    <value>Sind Sie Sie möchten die vorgewählten Einzelteile löschen sicher?</value>
  </data>
  <data name="MailMessageGroups_Edit" xml:space="preserve">
    <value>Redigieren Sie die Sonderkommandos und die Presse &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="Sourcing_AddToReq_Confirm" xml:space="preserve">
    <value>Sind Sie Sie möchten dieses/diese Einzelteile der Kunden-Anforderung hinzufügen sicher?</value>
  </data>
  <data name="Sourcing_AddToReq_SelectCusReq" xml:space="preserve">
    <value>Wählen Sie eine Kunden-Anforderung vor</value>
  </data>
  <data name="Sourcing_RFQ" xml:space="preserve">
    <value>Redigieren Sie die Sonderkommandos und die Presse &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="CreditAdd_SelectItem" xml:space="preserve">
    <value>Wählen Sie die Quelle für das neue Einzelteil vor &lt;b&gt;Fortfahren&lt;/b&gt;</value>
  </data>
  <data name="CreditAdd_SelectCRMA" xml:space="preserve">
    <value>Wählen Sie die Quelle für das neue Einzelteil vor &lt;b&gt;Fortfahren&lt;/b&gt;</value>
  </data>
  <data name="CreditAdd_SelectInvoice" xml:space="preserve">
    <value>Wählen Sie die Quelle für das neue Einzelteil vor &lt;b&gt;Fortfahren&lt;/b&gt;</value>
  </data>
  <data name="GILines_Delete" xml:space="preserve">
    <value>Sind Sie Sie möchten die vorgewählten Einzelteile löschen sicher?</value>
  </data>
  <data name="GILines_Edit" xml:space="preserve">
    <value>Redigieren Sie die Sonderkommandos und die Presse &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="GIMainInfo_Edit" xml:space="preserve">
    <value>Redigieren Sie die Sonderkommandos und die Presse &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="ContactMainInfo_Edit" xml:space="preserve">
    <value>Redigieren Sie die Sonderkommandos und die Presse &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="CompanyAddresses_DefaultShip" xml:space="preserve">
    <value>Sind Sie Sie möchten diese Adresse die Rückstellung Verschiffen-Rede für diese Firma halten sicher?</value>
  </data>
  <data name="StockImages_Add" xml:space="preserve">
    <value>Wählen Sie ein neues Bild (maximale Größe 4mb) vor und betätigen Sie sich &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="LotMainInfo_Delete" xml:space="preserve">
    <value>Sind Sie Sie möchten die vorgewählten Einzelteile löschen sicher?</value>
  </data>
  <data name="ServiceMainInfo_Edit" xml:space="preserve">
    <value>Redigieren Sie die Sonderkommandos und die Presse &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="StockImages_Delete" xml:space="preserve">
    <value>Sind Sie Sie möchten die vorgewählten Einzelteile löschen sicher?</value>
  </data>
  <data name="DocHeaderImage_Add" xml:space="preserve">
    <value>Select a new image for the Document Headers, 710 pixels wide and press &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="DocHeaderImage_Delete" xml:space="preserve">
    <value>Sind Sie Sie möchten die vorgewählten Einzelteile löschen sicher?</value>
  </data>
  <data name="DocFooters_Edit" xml:space="preserve">
    <value>Redigieren Sie die Sonderkommandos und die Presse &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="GILines_Inspect" xml:space="preserve">
    <value>Bestätigen Sie die Kontrollensonderkommandos und -presse &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="CompanyPurchasingInfo_Edit" xml:space="preserve">
    <value>Redigieren Sie die Sonderkommandos und die Presse &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="ContactsForCompany_Add" xml:space="preserve">
    <value>Tragen Sie alle Details des neuen Einzelteils ein und betätigen Sie sich &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="ContactsForCompany_Delete" xml:space="preserve">
    <value>Sind Sie Sie möchten die vorgewählten Einzelteile löschen sicher?</value>
  </data>
  <data name="POLines_Deallocate" xml:space="preserve">
    <value>Sind Sie Sie möchten diese Kaufauftrag-Linie freigeben sicher?</value>
  </data>
  <data name="SRMALine_Allocate_SelectStock" xml:space="preserve">
    <value>Beschließen Sie das auf lagereinzelteil, um diesem Lieferanten RMA zuzuteilen</value>
  </data>
  <data name="SRMALine_Alocate_EditDetails" xml:space="preserve">
    <value>Redigieren Sie die Sonderkommandos und die Presse &lt;b&gt;Save&lt;/b&gt;</value>
  </data>
  <data name="SRMALines_Deallocate" xml:space="preserve">
    <value>Sind Sie Sie möchten die vorgewählten Verteilungen entfernen sicher?</value>
  </data>
  <data name="ServiceMainInfo_Delete" xml:space="preserve">
    <value>Sind Sie Sie möchten die vorgewählten Einzelteile löschen sicher?</value>
  </data>
  <data name="POReceivingLines_Receive_Header_ExistingGI" xml:space="preserve">
    <value>Wählen Sie vorhandene Waren in der Anmerkung vor</value>
  </data>
  <data name="POReceivingLines_Receive_Header_NewGI" xml:space="preserve">
    <value>Tragen Sie alle Details des neuen Einzelteils ein und betätigen Sie sich &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="POReceivingLines_Receive_Target" xml:space="preserve">
    <value>Wählen Sie das Ziel für den neuen Empfang vor und betätigen Sie sich &lt;b&gt;Continue&lt;/b&gt;</value>
  </data>
  <data name="SOShippingLines_Ship_SelectInvoice" xml:space="preserve">
    <value>Wählen Sie die Rechnung vor, gegen die die versendeten Verkaufs-Auftrags-Linien addieren</value>
  </data>
  <data name="SOShippingLines_Ship_Target" xml:space="preserve">
    <value>Wählen Sie vor, ob die versendeten Verkaufs-Auftrags-Linien gegen eine neue Rechnung oder bestehende die und eine Presse eingeführt werden sollten  &lt;b&gt;Fortfahren&lt;/b&gt;</value>
  </data>
  <data name="EmailDocument" xml:space="preserve">
    <value>Tragen Sie die Details der eMail ein und betätigen Sie sich &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="SRMALine_Allocate_EditDetails" xml:space="preserve">
    <value>Redigieren Sie die Sonderkommandos und die Presse &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="POAdd_SelectCompany" xml:space="preserve">
    <value>Wählen Sie die Firma vor, von der Sie kaufen</value>
  </data>
  <data name="Sequencer_Edit" xml:space="preserve">
    <value>Redigieren Sie die Sonderkommandos und die Presse &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="DebitLine_Add_SelectPOLine" xml:space="preserve">
    <value>Wählen Sie die Quelle für die neue Linie vor</value>
  </data>
  <data name="DebitLine_Add_SelectService" xml:space="preserve">
    <value>Wählen Sie die Quelle für das neue Einzelteil vor &lt;b&gt;Fortfahren&lt;/b&gt;</value>
  </data>
  <data name="CRMAReceivingLines_Receive_Header" xml:space="preserve">
    <value>Tragen Sie alle Details des neuen Einzelteils ein und betätigen Sie sich &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="CRMAReceivingLines_Receive_SelectItem" xml:space="preserve">
    <value>Wählen Sie die Rechnungs-Linie Verteilung vor, die Sie für die Waren in der Linie verwenden möchten</value>
  </data>
  <data name="CRMAReceivingLines_Receive_Source" xml:space="preserve">
    <value>Wählen Sie die Quelle für das neue Einzelteil vor &lt;b&gt;Continue&lt;/b&gt;</value>
  </data>
  <data name="SRMAShippingLines_Ship_SelectSource" xml:space="preserve">
    <value>Wählen Sie die Quelle für das neue Einzelteil vor &lt;b&gt;Continue&lt;/b&gt;</value>
  </data>
  <data name="SRMAShippingLines_Ship_Detail" xml:space="preserve">
    <value>Tragen Sie alle Details des neuen Einzelteils ein und betätigen Sie sich &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="SRMAShippingLines_Ship_Header" xml:space="preserve">
    <value>Tragen Sie alle Details des neuen Einzelteils ein und betätigen Sie sich &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="SRMAShippingLines_Ship_Notify" xml:space="preserve">
    <value>Melden Sie andere dieses Einzelteils</value>
  </data>
  <data name="SOMainInfo_Notify" xml:space="preserve">
    <value>Melden Sie andere dieses Einzelteils</value>
  </data>
  <data name="InvoiceLines_Add_SelectItem" xml:space="preserve">
    <value>Wählen Sie die Quelle für das neue Einzelteil vor &lt;b&gt;Fortfahren&lt;/b&gt;</value>
  </data>
  <data name="InvoiceLines_Add_EnterDetail" xml:space="preserve">
    <value>Redigieren Sie die Sonderkommandos und die Presse &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="DocHeaderImage_Edit" xml:space="preserve">
    <value>Redigieren Sie die Sonderkommandos und die Presse &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="GIMainInfo_Notify" xml:space="preserve">
    <value>Melden Sie andere dieses Einzelteils</value>
  </data>
  <data name="InvoiceLines_Delete" xml:space="preserve">
    <value>Sind Sie Sie möchten die vorgewählten Einzelteile löschen sicher?</value>
  </data>
  <data name="CurrencyRateHistory_Delete" xml:space="preserve">
    <value>Sind Sie Sie möchten die vorgewählten Einzelteile löschen sicher?</value>
  </data>
  <data name="CurrencyRateHistory_Edit" xml:space="preserve">
    <value>Redigieren Sie die Sonderkommandos und die Presse &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="CusReqMainInfo_Close" xml:space="preserve">
    <value>Sind Sie Sie möchten diese Kunden-Anforderung schließen sicher?</value>
  </data>
  <data name="SOLines_PostAll" xml:space="preserve">
    <value>Sind Sie Sie möchten alle unposted Linien bekannt geben sicher?</value>
  </data>
  <data name="SOLines_UnpostAll" xml:space="preserve">
    <value>Sind Sie Sie möchten zum unpost alle informierten Linien sicher?</value>
  </data>
  <data name="ConfirmPost" xml:space="preserve">
    <value>Sind Sie Sie möchten diese Linie bekannt geben sicher?</value>
  </data>
  <data name="ConfirmPostAll" xml:space="preserve">
    <value>Sind Sie Sie möchten alle unposted Linien bekannt geben sicher?</value>
  </data>
  <data name="ConfirmUnpost" xml:space="preserve">
    <value>Sind Sie Sie möchten zum unpost diese Linie sicher?</value>
  </data>
  <data name="ConfirmUnpostAll" xml:space="preserve">
    <value>Sind Sie Sie möchten zu unpost allen informierten und nicht zugewiesenen Linien sicher?</value>
  </data>
  <data name="InvoiceLines_Add_SelectSource" xml:space="preserve">
    <value>Wählen Sie die Quelle für die neue Linie vor</value>
  </data>
  <data name="CountingMethod_Add" xml:space="preserve">
    <value>Tragen Sie alle Details des neuen Einzelteils ein und betätigen Sie sich &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="CountingMethod_Edit" xml:space="preserve">
    <value>Tragen Sie alle Details des neuen Einzelteils ein und betätigen Sie sich &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="POMainInfo_Close" xml:space="preserve">
    <value>Sind Sie Sie möchten diesen Kaufauftrag schließen sicher?</value>
  </data>
  <data name="SOMainInfo_Close" xml:space="preserve">
    <value>Sind Sie Sie möchten diesen Verkaufs-Auftrag schließen sicher?</value>
  </data>
  <data name="POLines_Close" xml:space="preserve">
    <value>Sind Sie Sie möchten diese Kaufauftrag-Linie schließen sicher?</value>
  </data>
  <data name="SOLines_Close" xml:space="preserve">
    <value>Sind Sie Sie möchten diese Verkaufs-Auftrags-Linie schließen sicher?</value>
  </data>
  <data name="CRMALines_Deallocate" xml:space="preserve">
    <value>Sind Sie Sie möchten die vorgewählten Verteilungen entfernen sicher?</value>
  </data>
  <data name="Incoterm_Add" xml:space="preserve">
    <value>Tragen Sie die geänderten Details für das Incoterm ein und betätigen Sie sich &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
  <data name="Incoterm_Edit" xml:space="preserve">
    <value>Tragen Sie alle Details des neuen Incoterm ein und betätigen Sie sich &lt;b&gt;Außer&lt;/b&gt;</value>
  </data>
</root>