Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.CompanyAddresses_Confirm=function(n){Rebound.GlobalTrader.Site.Controls.Forms.CompanyAddresses_Confirm.initializeBase(this,[n]);this._intAddressID=-1;this._intCompanyID=-1;this._intCompanyAddressID=-1};Rebound.GlobalTrader.Site.Controls.Forms.CompanyAddresses_Confirm.prototype={get_intAddressID:function(){return this._intAddressID},set_intAddressID:function(n){this._intAddressID!==n&&(this._intAddressID=n)},get_intCompanyID:function(){return this._intCompanyID},set_intCompanyID:function(n){this._intCompanyID!==n&&(this._intCompanyID=n)},get_strTitle_Cease:function(){return this._strTitle_Cease},set_strTitle_Cease:function(n){this._strTitle_Cease!==n&&(this._strTitle_Cease=n)},get_strTitle_DefaultBill:function(){return this._strTitle_DefaultBill},set_strTitle_DefaultBill:function(n){this._strTitle_DefaultBill!==n&&(this._strTitle_DefaultBill=n)},get_strTitle_DefaultShip:function(){return this._strTitle_DefaultShip},set_strTitle_DefaultShip:function(n){this._strTitle_DefaultShip!==n&&(this._strTitle_DefaultShip=n)},get_strExplanation_Cease:function(){return this._strExplanation_Cease},set_strExplanation_Cease:function(n){this._strExplanation_Cease!==n&&(this._strExplanation_Cease=n)},get_strExplanation_DefaultBill:function(){return this._strExplanation_DefaultBill},set_strExplanation_DefaultBill:function(n){this._strExplanation_DefaultBill!==n&&(this._strExplanation_DefaultBill=n)},get_strExplanation_DefaultShip:function(){return this._strExplanation_DefaultShip},set_strExplanation_DefaultShip:function(n){this._strExplanation_DefaultShip!==n&&(this._strExplanation_DefaultShip=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Forms.CompanyAddresses_Confirm.callBaseMethod(this,"initialize");this.addShown(Function.createDelegate(this,this.formShown));this.addModeChanged(Function.createDelegate(this,this.modeChanged))},dispose:function(){this.isDisposed||(this._ctlConfirm&&this._ctlConfirm.dispose(),this._ctlConfirm=null,this._intAddressID=null,this._intCompanyID=null,this._intCompanyAddressID=null,this._strTitle_Cease=null,this._strTitle_DefaultBill=null,this._strTitle_DefaultShip=null,this._strExplanation_Cease=null,this._strExplanation_DefaultBill=null,this._strExplanation_DefaultShip=null,Rebound.GlobalTrader.Site.Controls.Forms.CompanyAddresses_Confirm.callBaseMethod(this,"dispose"))},formShown:function(){this._blnFirstTimeShown&&(this._ctlConfirm=this.getFieldComponent("ctlConfirm"),this._ctlConfirm.addClickYesEvent(Function.createDelegate(this,this.yesClicked)),this._ctlConfirm.addClickNoEvent(Function.createDelegate(this,this.noClicked)))},yesClicked:function(){this.showSaving(!0);var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("Controls/Nuggets/CompanyAddresses");n.set_DataObject("CompanyAddresses");switch(this._mode){case"CEASE":n.set_DataAction("CeaseAddress");break;case"DEFAULT_BILL":n.set_DataAction("MakeDefaultBill");break;case"DEFAULT_SHIP":n.set_DataAction("MakeDefaultShip")}n.addParameter("id",this._intCompanyAddressID);n.addParameter("CMNo",this._intCompanyID);n.addDataOK(Function.createDelegate(this,this.saveComplete));n.addError(Function.createDelegate(this,this.saveError));n.addTimeout(Function.createDelegate(this,this.saveError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null;this.onSave()},noClicked:function(){this.showSaving(!1);this.onNotConfirmed()},saveError:function(n){this.showSaving(!1);this._strErrorMessage=n._errorMessage;this.onSaveError()},saveComplete:function(n){this.showSaving(!1);n._result.Result?this.onSaveComplete():(this._strErrorMessage=n._errorMessage,this.onSaveError())},modeChanged:function(){switch(this._mode){case"CEASE":this.changeTitle(this._strTitle_Cease);this.changeExplanation(this._strExplanation_Cease);break;case"DEFAULT_BILL":this.changeTitle(this._strTitle_DefaultBill);this.changeExplanation(this._strExplanation_DefaultBill);break;case"DEFAULT_SHIP":this.changeTitle(this._strTitle_DefaultShip);this.changeExplanation(this._strExplanation_DefaultShip)}}};Rebound.GlobalTrader.Site.Controls.Forms.CompanyAddresses_Confirm.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.CompanyAddresses_Confirm",Rebound.GlobalTrader.Site.Controls.Forms.Base,Sys.IDisposable);