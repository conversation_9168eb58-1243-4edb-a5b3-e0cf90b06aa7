///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//----------------------------------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//----------------------------------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.ItemSearch");

Rebound.GlobalTrader.Site.Controls.ItemSearch.Service = function(element) { 
	Rebound.GlobalTrader.Site.Controls.ItemSearch.Service.initializeBase(this, [element]);
	this._intMaxResults = 50;
	this._GlobalClientNo = -1;
};

Rebound.GlobalTrader.Site.Controls.ItemSearch.Service.prototype = {

	get_intMaxResults: function() { return this._intMaxResults; }, 	set_intMaxResults: function(v) { if (this._intMaxResults !== v)  this._intMaxResults = v; }, 

	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.ItemSearch.Service.callBaseMethod(this, "initialize");
		this.addSetupData(Function.createDelegate(this, this.doSetupData));
		this.addGetDataComplete(Function.createDelegate(this, this.doGetDataComplete));
	},

	dispose: function() { 
		if (this.isDisposed) return;
		this._intMaxResults = null;
		this._GlobalClientNo = null;
		Rebound.GlobalTrader.Site.Controls.ItemSearch.Service.callBaseMethod(this, "dispose");
	},

	doSetupData: function() {
		this._objData.set_PathToData("controls/ItemSearch/Service");
		this._objData.set_DataObject("Service");
		this._objData.set_DataAction("GetData");
		this._objData.addParameter("Name", this.getFieldValue("ctlName"));
		this._objData.addParameter("GlobalClientNo", this._GlobalClientNo);
	},
	
	doGetDataComplete: function() {
		for (var i = 0, l = this._objResult.Results.length; i < l && i < this._intMaxResults; i++) {
			var row = this._objResult.Results[i];
			var aryData = [
				$R_FN.setCleanTextValue(row.Name)
			,	$R_FN.setCleanTextValue(row.Desc)
			,	$R_FN.setCleanTextValue(row.Cost)
			,	$R_FN.setCleanTextValue(row.Price)
			];
			this._tblResults.addRow(aryData, row.ID);
			aryData = null; row = null;
		}
	}
		
};

Rebound.GlobalTrader.Site.Controls.ItemSearch.Service.registerClass("Rebound.GlobalTrader.Site.Controls.ItemSearch.Service", Rebound.GlobalTrader.Site.Controls.ItemSearch.Base, Sys.IDisposable);
