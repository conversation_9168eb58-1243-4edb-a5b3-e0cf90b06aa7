﻿
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO



/*   
===========================================================================================  
TASK         UPDATED BY   DATE          ACTION    DESCRIPTION  
[US-210037]  An.TranTan   23-Oct-2024	create    Get Manufacturer advisory notes  
[US-214629]  Trung Pham   16-Dec-2024	update    Get number of Purchase Order and star rating belong to manufacturer
[US-228674]  An.TranTan   08-Jan-2024	update    Get IsFranchised
[US-228888]  Trung Pham   09-Jan-2024	update    Star Rating Enhancements
===========================================================================================    
*/   
CREATE OR ALTER PROCEDURE [dbo].[usp_selectAll_ManufacturerLink_for_Manufacturer]        
--****************************************************************************************        
--* RP 12.11.09        
--* - add @ClientID to restrict display to current Client Company only        
--*Updated Date: 11-08-2014      
--*Description: Under Manufactures Screen,       
--*Supplier Who Distribute� nugget need to have another column named as Company Type.      
--* This column will show company type against each company showing in the grid      
--****************************************************************************************        
@ManufacturerId INT        
,@ClientID int        
,@SortDir int = 1 -- 1: ASC, 2: DESC  
AS        
  
WITH POLineCountData AS (  
    SELECT   
        COUNT(DISTINCT pol.PurchaseOrderLineId) AS POLineCount,  
        po.CompanyNo,  
        pol.ManufacturerNo  
    FROM tbManufacturer AS m  
    JOIN tbManufacturerLink AS ml ON ml.ManufacturerNo = m.ManufacturerId  
    JOIN tbPurchaseOrderLine AS pol ON pol.ManufacturerNo = m.ManufacturerId  
    JOIN tbPurchaseOrder AS po ON po.PurchaseOrderId = pol.PurchaseOrderNo  
    WHERE m.ManufacturerId = @ManufacturerId  
      AND pol.ManufacturerNo = ml.ManufacturerNo  
    GROUP BY po.CompanyNo, pol.ManufacturerNo  
)  
  
SELECT a.ManufacturerLinkId        
,  a.ManufacturerNo        
,  b.ManufacturerName        
,  a.SupplierCompanyNo         
,  c.CompanyName  As SupplierName         
,  a.ManufacturerRating        
,  a.SupplierRating        
,  a.UpdatedBy        
,  a.DLUP      
,  t.Name as CompanyType  
, CASE WHEN ISNULL(c.IsDisplayAdvisory, 0) = 1 THEN c.AdvisoryNotes ELSE '' END AS AdvisoryNotes  
, a.StarRating AS StarRating  
, ISNULL(pcd.POLineCount, 0) AS POLineCount  
, ISNULL(a.IsFranchised,0) AS IsFranchised

FROM  dbo.tbManufacturerLink a        
JOIN dbo.tbManufacturer b        
 ON a.ManufacturerNo = b.ManufacturerId        
JOIN dbo.tbCompany c        
 ON a.SupplierCompanyNo = c.CompanyId        
 AND c.ClientNo = @ClientID        
LEFT JOIN tbCompanyType t on c.TypeNo=t.CompanyTypeId      
LEFT JOIN POLineCountData AS pcd ON pcd.CompanyNo = c.CompanyId AND pcd.ManufacturerNo = a.ManufacturerNo  
WHERE a.ManufacturerNo = @ManufacturerId        
ORDER BY  
  CASE WHEN @SortDir = 1 THEN c.CompanyName END ASC,  
  CASE WHEN @SortDir = 2 THEN c.CompanyName END DESC  
GO


