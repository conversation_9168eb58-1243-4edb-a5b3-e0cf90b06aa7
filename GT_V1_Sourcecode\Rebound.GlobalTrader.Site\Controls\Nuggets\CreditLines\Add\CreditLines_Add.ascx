<%@ Control Language="C#" CodeBehind="CreditLines_Add.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Forms.CreditLines_Add" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>

<ReboundUI_Form:DesignBase ID="ctlDB" runat="server" ShowRequiredFieldsExplanation="true" ShowQuickHelp="false" NumberOfSteps="3" MultiStepControlID="ctlMultiStep">

	<Links>
		<ReboundUI:IconButton ID="ibtnSave" runat="server" IconGroup="Nugget" IconTitleResource="Save" IconCSSType="Save" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
		<ReboundUI:IconButton ID="ibtnCancel" runat="server" IconGroup="Nugget" IconTitleResource="Cancel" IconCSSType="Cancel" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
		<ReboundUI:IconButton ID="ibtnContinue" runat="server" IconGroup="Nugget" IconTitleResource="Continue" IconCSSType="Continue" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
	</Links>
	
	<Explanation>
		<ReboundUI:MultiStep ID="ctlMultiStep" runat="server">
			<Items>
				<ReboundUI:MultiStepItem ID="ctlItem1" runat="server" ResourceTitle="CreditLine_Add_SelectSource" IsSelected="true" />
				<ReboundUI:MultiStepItem ID="ctlItem2" runat="server" ResourceTitle="CreditLine_Add_SelectItem" />
				<ReboundUI:MultiStepItem ID="ctlItem3" runat="server" ResourceTitle="CreditLine_Add_EditDetails" />
			</Items>
		</ReboundUI:MultiStep>
	</Explanation>
	
	<Content>
	
		<!-- Step 1 ------------------------------------------------------------------->
		<ReboundUI_Table:Form id="frmStep1" runat="server">
			<ReboundUI_Form:FormField id="ctlSelectSource" runat="server" FieldID="radSelectSource" ResourceTitle="SelectSource">
				<Field><asp:RadioButtonList ID="radSelectSource" runat="server" RepeatDirection="Vertical" RepeatLayout="Flow" /></Field>
			</ReboundUI_Form:FormField>
		</ReboundUI_Table:Form>
		
		<!-- Step 2 ------------------------------------------------------------------->
		<ReboundUI_Table:Form id="frmStep2" runat="server">
		
			<asp:TableRow id="trSourceFromCustomerRMA" runat="server">
				<asp:TableCell id="tdSourceFromCustomerRMA" runat="server"><ReboundItemSearch:CRMALines id="ctlSourceFromCustomerRMA" runat="server" /></asp:TableCell>
			</asp:TableRow>
			
			<asp:TableRow id="trSourceFromInvoice" runat="server">
				<asp:TableCell id="tdSourceFromInvoice" runat="server"><ReboundItemSearch:InvoiceLines id="ctlSourceFromInvoice" runat="server" /></asp:TableCell>
			</asp:TableRow>
			
			<asp:TableRow id="trSourceFromService" runat="server">
				<asp:TableCell id="tdSourceFromService" runat="server"><ReboundItemSearch:Service id="ctlSourceFromService" runat="server" /></asp:TableCell>
			</asp:TableRow>
			
			<asp:TableRow id="trSourceFromClientInvoice" runat="server">
				<asp:TableCell id="tdSourceFromClientInvoice" runat="server">
				
				<asp:Panel id="pnlLines" runat="server" CssClass="itemSearch">
						
						<asp:Panel id="pnlLinesError" runat="server" CssClass="itemSearchError invisible"><asp:Label id="lblLinesError" runat="server" /></asp:Panel>
						<asp:Panel id="pnlLinesLoading" runat="server" CssClass="loading invisible"><%=Functions.GetGlobalResource("Misc", "Loading")%></asp:Panel>
						<asp:Panel id="pnlLinesNoneFound" runat="server" CssClass="noneFound invisible"><%=Functions.GetGlobalResource("NotFound", "Generic")%></asp:Panel>
					
			<%--	<ReboundItemSearch:ClientInvoiceLines id="ctlSourceFromClientInvoice" runat="server" />--%>
			<ReboundUI:FlexiDataTable ID="tblLines" runat="server" AllowSelection="true" />
				</asp:Panel>
				</asp:TableCell>
			</asp:TableRow>
			
		</ReboundUI_Table:Form>
		
		<!-- Step 3 ------------------------------------------------------------------->
		<ReboundUI_Table:Form id="frmStep3" runat="server">
		
            <ReboundUI_Form:FormField id="ctlCredit" runat="server" FieldID="lblCredit" ResourceTitle="CreditNo" >
	            <Field><asp:Label ID="lblCredit" runat="server" /></Field>
            </ReboundUI_Form:FormField>
            
            <ReboundUI_Form:FormField id="ctlCustomer" runat="server" FieldID="lblCustomer" ResourceTitle="Customer">
	            <Field><asp:Label ID="lblCustomer" runat="server" /></Field>
            </ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlPartNo" runat="server" FieldID="txtPartNo" ResourceTitle="PartNo" IsRequiredField="true">
				<Field>
					<ReboundUI:ReboundTextBox ID="txtPartNo" runat="server" Width="250" UppercaseOnly="true" />
					<ReboundAutoSearch:PartSearch ID="autPartNo" runat="server" RelatedTextBoxID="txtPartNo" ResultsHeight="150" Width="250" ResultsActionType="RaiseEvent" TriggerByButton="true" />
				</Field>
			</ReboundUI_Form:FormField>
			
			<ReboundUI_Form:FormField id="ctlService" runat="server" FieldID="txtService" ResourceTitle="Service" IsRequiredField="true">
				<Field><ReboundUI:ReboundTextBox ID="txtService" runat="server" Width="200" /></Field>
			</ReboundUI_Form:FormField>
			
			<ReboundUI_Form:FormField id="ctlServiceDescription" runat="server" FieldID="txtServiceDescription" ResourceTitle="Description">
				<Field><ReboundUI:ReboundTextBox ID="txtServiceDescription" runat="server" Width="300" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlCustomerPart" runat="server" FieldID="txtCustomerPart" ResourceTitle="CustomerPartNo">
				<Field><ReboundUI:ReboundTextBox ID="txtCustomerPart" runat="server" Width="150" UppercaseOnly="true" /></Field>
			</ReboundUI_Form:FormField>

            <ReboundUI_Form:FormField id="ctlROHSStatus" runat="server" FieldID="ddlROHSStatus" ResourceTitle="ROHS" DisplayRequiredFieldMarkerOnly="true">
                <Field><ReboundDropDown:ROHSStatus ID="ddlROHSStatus" runat="server" /></Field>
            </ReboundUI_Form:FormField>
            
			<ReboundUI_Form:FormField id="ctlDateCode" runat="server" FieldID="txtDateCode" ResourceTitle="DateCode">
				<Field><ReboundUI:ReboundTextBox ID="txtDateCode" runat="server" Width="60" MaxLength="5" UppercaseOnly="true" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlQuantity" runat="server" FieldID="txtQuantity" ResourceTitle="Quantity" IsRequiredField="true">
				<Field><ReboundUI:ReboundTextBox ID="txtQuantity" runat="server" Width="80" TextBoxMode="numeric" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlLandedCost" runat="server" FieldID="txtLandedCost" ResourceTitle="LandedCost">
				<Field><ReboundUI:ReboundTextBox ID="txtLandedCost" runat="server" TextBoxMode="currency" FormatDecimalPlaces="true" Width="100" /> <%=Rebound.GlobalTrader.Site.SessionManager.ClientCurrencyCode%> <asp:Label ID="lblLandedCost" runat="server" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlPrice" runat="server" FieldID="txtPrice" ResourceTitle="Price" IsRequiredField="true">
				<Field><ReboundUI:ReboundTextBox ID="txtPrice" runat="server" TextBoxMode="currency" FormatDecimalPlaces="true" Width="100" /> <asp:Label ID="lblCurrency_Price" runat="server" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlManufacturer" runat="server" FieldID="cmbManufacturer" ResourceTitle="Manufacturer">
				<Field><ReboundUI:Combo ID="cmbManufacturer" runat="server" AutoSearchControlAssembly="Rebound.GlobalTrader.Site" AutoSearchControlType="Manufacturers" /></Field>
			</ReboundUI_Form:FormField>
            <ReboundUI_Form:FormField id="ctlProduct" runat="server" FieldID="cmbProducts" ResourceTitle="Product" >
				<Field><ReboundUI:Combo ID="cmbProducts" runat="server" AutoSearchControlAssembly="Rebound.GlobalTrader.Site" PanelWidth="450" PanelHeight="250"  AutoSearchControlType="Products" /></Field>
			</ReboundUI_Form:FormField>
			<%--<ReboundUI_Form:FormField id="ctlProduct" runat="server" FieldID="ddlProduct" ResourceTitle="Product">
				<Field><ReboundDropDown:Product ID="ddlProduct" runat="server" /></Field>
			</ReboundUI_Form:FormField>--%>

			<%--<ReboundUI_Form:FormField id="ctlPackage" runat="server" FieldID="ddlPackage" ResourceTitle="Package">
				<Field><ReboundDropDown:Package ID="ddlPackage" runat="server" /></Field>
			</ReboundUI_Form:FormField>--%>
            <ReboundUI_Form:FormField id="ctlPackage" runat="server" FieldID="cmbPackage" ResourceTitle="Package">
				<Field>
                   <ReboundUI:Combo ID="cmbPackage" runat="server" AutoSearchControlAssembly="Rebound.GlobalTrader.Site"  AutoSearchControlType="Packages" />
				</Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlTaxable" runat="server" FieldID="chkTaxable" ResourceTitle="Taxable" DefaultValue="true">
				<Field><ReboundUI:ImageCheckBox ID="chkTaxable" runat="server" Enabled="true" /></Field>
			</ReboundUI_Form:FormField>
			
			<ReboundUI_Form:FormField id="ctlLineNotes" runat="server" FieldID="txtLineNotes" ResourceTitle="Notes">
				<Field><ReboundUI:ReboundTextBox ID="txtLineNotes" runat="server" Width="400" TextMode="multiLine" Rows="2" /></Field>
			</ReboundUI_Form:FormField>
			
		</ReboundUI_Table:Form>
		
	</Content>
	
</ReboundUI_Form:DesignBase>
