﻿CREATE OR ALTER PROCEDURE  [dbo].[usp_select_PurchaseOrder]                                       
--Marker     Changed by      Date         Remarks                                
--[001]      Vinay           01/11/2012   Add comma(,) seprated Debit and SRMA in purchase order section                                
@PurchaseOrderId int                                 
--                                
AS           
BEGIN                             
--                                
 Declare @vSupplierRMAIds Varchar(1000)          
                             
 Declare @vSupplierRMANumbers Varchar(1000)                               
 Declare @vDebitIds Varchar(1000)                              
 Declare @vDebitNumbers Varchar(1000)                  
 Declare @ClientNo int=0  ,@MailGroupId int=0                
 Declare @isChecked bit=1         
         
              
                           
 Execute usp_select_SupplierRMA_By_PurchaseOrder @PurchaseOrderId ,@vSupplierRMAIds Out,@vSupplierRMANumbers Out                              
 Execute usp_select_Debit_By_PurchaseOrder @PurchaseOrderId, @vDebitIds Out,@vDebitNumbers Out              
         
                       
                             
 -- For EPR Start                            
    Declare @vEPRIds Varchar(1000)                               
    Select @vEPRIds = COALESCE(@vEPRIds + ',','') + COALESCE(Cast(EPRId As Varchar),'')                                    
    From tbEPR                                
    Where PurchaseOrderId = @PurchaseOrderId AND isnull(Inactive,0) = 0 order by DLUP desc                            
 -- For EPR end               
 -- For PO line EPR Start           
 IF OBJECT_ID('tempdb..#Results') IS NOT NULL DROP TABLE #Results            
 select distinct eprno,CreatedOn  into #Results        
 From  tbPOLineEPR                                 
 Where PurchaseOrderNo = @PurchaseOrderId AND isnull(Inactive,0) = 0 order by CreatedOn desc                     
    Declare @POLineEPRIds Varchar(max)            
    Select  @POLineEPRIds = COALESCE(@POLineEPRIds + ',','') + Cast(eprno As Varchar)            
    from #Results          
             
 -- For PO line EPR end                
              
 declare @ClientCurrencyCode varchar(10)              
                 
 select @ClientNo = ipo.ClientNo,@ClientCurrencyCode = cu.CurrencyCode from tbInternalPurchaseOrder ipo         
 join tbCurrency cu on ipo.CurrencyNo = cu.CurrencyId where PurchaseOrderNo =  @PurchaseOrderId                   
                 
 select top 1 @MailGroupId=MailGroupId from tbMailGroup where Name='IPO Purchasing' and ClientNo=@ClientNo                
               
 IF EXISTS(SELECT  sk.PurchaseOrderNo              
FROM    tbStock sk                      
JOIN    tbAllocation al ON sk.StockId = al.StockNo                      
JOIN    tbSalesOrderLine sol ON sol.SalesOrderLineId = al.SalesOrderLineNo                      
JOIN    tbSalesOrder so ON so.SalesOrderId = sol.SalesOrderNo               
where sk.PurchaseOrderNo=@PurchaseOrderId and so.AuthorisedBy IS NULL AND so.DateAuthorised IS NULL)              
begin              
 set @isChecked=0              
end              
--Espire: 31st Oct 2017: Check if stock is not allocted              
else if not exists(SELECT  sk.PurchaseOrderNo              
FROM    tbStock sk                      
JOIN    tbAllocation al ON sk.StockId = al.StockNo                      
where sk.PurchaseOrderNo=@PurchaseOrderId)              
begin               
 set @isChecked=0              
end               
        
        
/*        
declare @LoginNo int        
        
select @LoginNo=UpdatedBy from tbPurchaseOrder where PurchaseOrderId=@PurchaseOrderId        
--Espire: 23 Feb 21: To approved PO fixed security group with name Strategic trade deal temporarily        
if exists(select * from tbSecurityGroupLogin sl where SecurityGroupNo in (select g.SecurityGroupId from tbSecurityGroup g where g.SecurityGroupName='Strategic trade deal') and sl.LoginNo=@LoginNo)        
begin        
 set @isChecked=1        
end        
*/        
                               
SELECT vw.*,                              
    @vSupplierRMAIds As 'SupplierRMAIds',                              
    @vSupplierRMANumbers As 'SupplierRMANumbers',                              
  @vDebitIds As 'DebitIds',                              
    @vDebitNumbers As 'DebitNumbers' ,                            
    @vEPRIds as 'EPRIds'                    
    --CAST(CASE WHEN ipo.InternalPurchaseOrderId IS NULL THEN 0 ELSE 1 END AS BIT) AS IsIPO                              
   --ipo.ClientNo as IPOClientNo                     
 , @MailGroupId  as MailGroupId               
 , @ClientCurrencyCode as ClientCurrencyCode              
 , @isChecked AS isChecked               
 , isnull((vw.CompanyName + '   -  ' + ct.Name), vw.CompanyName) AS CompanyNameType              
 ,@POLineEPRIds AS POLineEPRIds         
 , ct.Name as CompanyType        
 --[002] start          
 , (          
        select          
            case when c.IsSanctioned = 1 then 'For this company, Purchasing is \''On Stop.\''  '           
                else ''           
            end          
        from tbCompany c          
        where c.CompanyId = vw.CompanyNo) as IsCompanySantionedMessage          
--[002] end      
from dbo.vwPurchaseOrder vw                  
LEFT          
JOIN tbCompanyType ct                       
 ON vw.TypeNo  = ct.CompanyTypeId                           
--LEFT JOIN tbInternalPurchaseOrder ipo ON vw.PurchaseOrderId = ipo.PurchaseOrderNo                        
WHERE vw.PurchaseOrderId = @PurchaseOrderId               
              
 END             
  
  
   
   
   
   