﻿
.LoaderPopup {
    background: rgba(0,0,0,.4);
    cursor: pointer;
    display: none;
    /* height: auto; */
    height: 100%;
    position: absolute;
    text-align: center;
    top: -4%;
    width: 100%;
    z-index: 10000;
}

.cssload-loader {
    width: 244px;
    height: 49px;
    line-height: 49px;
    text-align: center;
    position: absolute;
    left: 50%;
    top: 20%;
    transform: translate(-50%, -50%);
    -o-transform: translate(-50%, -50%);
    -ms-transform: translate(-50%, -50%);
    -webkit-transform: translate(-50%, -50%);
    -moz-transform: translate(-50%, -50%);
    font-family: helvetica, arial, sans-serif;
    text-transform: uppercase;
    font-weight: 900;
    font-size: 18px;
    color: rgb(206,66,51);
    letter-spacing: 0.2em;
    background-color: aliceblue;
}

.chosen-container chosen-container-single {
    width: 200px;
}

a.quickSearchReselect {
    display: block;
    margin: 0px;
}

#ctl00_cphLeft_ctlLeft_QuickJump_ctlDB_ctl14_rad label {
    width: 100%;
    display: initial;
    margin: 5px 0;
}

#ctl00_cphMain_ctlLines_ctlDB_pnlBoxInner .boxHeader {
    height: 56px !important;
}

#ctl00_cphMain_ctlLines_ctlDB_pnlBoxInner .boxHeaderInner {
    height: 56px !important;
    background-color: #9fe994;
}
/*#dvPackBreakInfo{
        margin-left: 135px;
    width: 100%;
    }*/
/*body {
        background-color: #3a9653;
    }*/

.clearfix {
    display: block
}

/*body {
        margin: 0 auto;
        padding: 0;
        font-size: 12px;
        font-family: Tahoma !important;
        
        color: #fff;
        overflow-x: hidden;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale
    }*/

a:link,
a:visited,
div,
li,
p,
span,
ul {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale
}

ul {
    margin: 0px;
    padding: 0px;
}

label {
    width: 100%;
    display: block;
    margin: 5px 0;
}

select {
    padding: 1px;
    font-size: 11px;
}

.clearfix {
    clear: both;
}

.container-4tab {
    padding: 0px;
    margin: 0%;
    width: 100%;
    float: left;
    position: relative;
}

    .container-4tab .refstrip {
        background-color: #467d3f;
        padding: 5px;
        float: left;
        width: 99%;
    }

        .container-4tab .refstrip .refstripcol {
            float: left;
            min-width: 17.5%;
            border-right: 1px #ddd solid;
            padding-left: 10px;
            padding-right: 15px;
        }

            .container-4tab .refstrip .refstripcol label {
                width: auto;
                display: inline-block;
                margin: 5px;
            }

            .container-4tab .refstrip .refstripcol span {
                color: #e8d500;
                font-weight: 600;
                font-size: 14px;
                margin-left: 5px;
            }

                .container-4tab .refstrip .refstripcol span.redflag {
                    color: #fff;
                    background-color: red;
                    padding: 3px 6px;
                    border-radius: 4px
                }

.tabs-nav {
    float: left;
    width: 100%;
    margin: 6px 0px;
}

    .tabs-nav ul {
        margin: 0;
        padding: 0;
        display: flex;
        background-color: #fff;
    }

    .tabs-nav li {
        display: inline-block;
        background: rgba(255, 255, 255, 0.78);
        color: #c2c2c2;
        margin-right: 0px;
        border-right: 1px #c2c2c2 solid;
    }

    .tabs-nav a {
        display: block;
        padding: 10px 15px;
        font-weight: bold;
        color: #c2c2c2;
        text-decoration: none;
    }
    /* Active tab */

    .tabs-nav li.active {
        background: #2d6c3d;
        color: #fff;
    }

        .tabs-nav li.active a {
            color: inherit;
        }
/* Tab content */
.tabs-content {
    float: left;
    width: 100%
}

    .tabs-content h3 {
        background-color: #467d3f;
        padding: 15px 10px;
        margin: 0px;
        color: #18311d;
        text-transform: uppercase;
    }

.tab1data, .tab2data, .tab3data {
    display: block !important;
}

    .tab1data table, .tab2data table, .tab3data table {
        width: 100%;
        display: inline-table;
    }

    .tab1data table, .tab2data table, .tab3data table {
        border-collapse: collapse;
    }

        .tab1data table tr td, .tab2data table tr td, .tab3data table tr td {
            padding: 12px 2px 12px 10px;
            font-size: 11px;
            vertical-align: middle;
        }

        .tab1data table tr:nth-child(even), .tab2data table tr:nth-child(even), .tab3data table tr:nth-child(even) {
            background: #6aa363;
            border-bottom: 1px #2e6d3a solid;
        }

        .tab1data table tr:nth-child(odd), .tab2data table tr:nth-child(odd), .tab3data table tr:nth-child(odd) {
            background: #4f8b48;
            border-bottom: 1px #2e6d3a solid;
        }

        .tab2data table tr td:nth-child(1), .tab3data table tr td:nth-child(1) {
            font-weight: 600;
        }

    .tab2data tr th {
        background-color: #4f8b48;
        padding: 5px 10px;
        margin: 0px;
        color: #bdbdbd;
        text-transform: uppercase;
        font-size: 12px;
    }

        .tab2data tr th:nth-child(1), .tab2data tr td:nth-child(2) {
            border-right: 1px #2E6D38 solid;
        }

    .tab3data label {
        display: initial;
    }

    .tab3data table tr td:nth-child(1) {
        width: 20%;
    }

    .tab1data table tr td:nth-child(1) {
        width: 20%;
    }

    .tab1data table tr td:nth-child(2) {
        width: 45%;
    }

    .tab1data table tr td:nth-child(3) {
        width: 15%;
    }

    .tab2data table tr td:nth-child(1) {
        width: 17%;
    }

    .tab2data table tr td:nth-child(2) {
        width: 2%;
    }

    .tab2data table tr td:nth-child(3) {
        width: 14%;
        font-weight: 600;
    }

    .tab2data table tr td:nth-child(4) {
        width: 10%;
    }

    .tab2data table tr td:nth-child(5) {
        width: 15%;
    }

    .tab2data table input, select {
        width: auto !important;
    }

    .tab1data table tr td:nth-child(3), .tab2data table tr td:nth-child(5) {
        border-left: 1px #2E6D38 solid;
        font-weight: 600;
    }

.insidetable {
    text-align: left;
}

    .insidetable th {
        width: 50%;
        background-color: #016660;
        padding: 5px 5px 5px 5px;
    }

.tablescroll {
    display: block !important;
    float: left;
    width: 100%;
    height: 50px;
    overflow: auto;
}

    .tablescroll td {
        padding: 5px 5px 5px 5px !important;
        border-bottom: 1px #3f9f58 solid;
        background-color: #68ba92;
        width: 54% !important;
    }

.insidetable1 th {
    width: 32%;
    background-color: #016660;
    padding: 5px 2px;
    text-align: left;
}

.tablescroll1 {
    display: block !important;
}

    .tablescroll1 input {
        width: 75%;
        border-radius: 2px;
        border: none;
    }

.threecol {
    width: auto !important;
}

    .threecol table {
        /*width: 100%;*/
        width: 750px;
    }

.tablescroll1 td {
    width: auto !important;
    padding: 2px !important;
    border: 1px #3f9f58 solid;
    background-color: #68ba92;
}

#tbBreakdownTable input {
    width: 60% !important;
}

.addpackbtn {
    display: block;
    width: 100%;
    padding: 1px;
    background-color: #5b5b5b;
}

    .addpackbtn button {
        width: 100%;
        padding: 9px 3px;
        background-color: #5b5b5b;
        color: #fff;
        font-size: 12px;
        font-weight: 600;
        border: none;
        text-transform: uppercase;
        cursor: pointer;
    }

        .addpackbtn button:hover {
            background-color: #4b9f59;
        }

.inlinelable {
    display: inline-block;
    margin-right: 10px;
    width: 10%;
}

    .inlinelable input {
        vertical-align: middle;
    }

    .inlinelable label {
        display: inline-block;
        width: auto;
        vertical-align: bottom;
    }

.tabs-content IMG {
    margin-right: 10px;
}
/* Hide all but first content div */

/*.tabs-content div:not(:first-child) {
        display: none;
    }*/

#tab2 .discussionbox, #tab2 .discussionbox div, .replytoselect {
    display: block !important;
}

.rightcontentbox {
    float: right;
    width: 90%;
    padding: 15px;
    border: 1px #c1e5bc solid;
    background-color: #c1e5bc;
    clear: left;
    margin-bottom: 15px;
    border-radius: 3px;
}

.leftcontentbox {
    float: left;
    width: 90%;
    padding: 15px;
    border: 1px #b5f3c1 solid;
    background-color: #b5f3c1;
    clear: right;
    margin-bottom: 15px;
    border-radius: 3px;
}

.leftimgblock {
    float: left;
    width: 10%;
    margin-right: 20px;
    text-align: center;
    font-size: 12px;
    text-transform: uppercase;
    color: #10391b;
    font-weight: 600;
}

.remarkcol {
    background: #fafbf9;
    margin-top: 4px;
    width: 97%;
    padding: 10px;
    border: 1px #56954e solid;
    float: left;
}

.rlybtn {
    float: right;
    width: 100%;
    text-align: right;
}

    .rlybtn .iconButton_Nugget_PostAll {
        height: 24px;
        display: inline-block;
        width: auto;
        border-radius: 25px;
    }

        .rlybtn .iconButton_Nugget_PostAll img {
            vertical-align: middle;
        }

.contentarea {
    color: #12381e;
    float: right;
    width: 87%;
}

    .contentarea p {
        font-weight: 600;
        font-size: 12px;
    }

    .contentarea .title {
        width: 100%;
        font-size: 18px;
        font-weight: 600;
        display: block;
        margin-bottom: 5px;
    }

    .contentarea .datetime {
        width: 100%;
        font-size: 12px;
        font-weight: 400;
        display: block;
        margin-bottom: 10px;
    }

.replytoselect {
    float: left;
    width: 100%;
    margin-top: 10px;
}

    .replytoselect select {
        width: 150px;
        padding: 5px;
        border-radius: 3px;
    }

.commentarea {
    display: block !important;
    float: left;
    width: 98%;
    background: #fff;
    padding: 5px;
    margin-top: 25px;
    border-radius: 3px;
}

.mailoption {
    margin-top: 20px;
}

.commentarea textarea {
    border: none;
    width: 78% !important;
    padding-right: 50px;
}

.approvecategory {
    margin-top: 20px;
}

    .approvecategory tr td {
        width: 120px;
    }

.postbtn {
    float: right;
    /*border-left: 2px #316c3d solid;*/
    padding-left: 15px;
    min-height: 48px;
}

    .postbtn button {
        border: none;
        background: transparent;
        cursor: pointer;
    }

.approvaltable {
    width: 100%;
    border-collapse: collapse;
}

    .approvaltable tr th, .approvaltable tr td {
        padding: 10px 5px;
        color: #fff;
        text-align: left;
    }

    .approvaltable tr th {
        background-color: #2d703f;
    }

    .approvaltable tr:nth-child(even) {
        background-color: #378f4f;
        border-bottom: 1px #399753 solid;
    }

    .approvaltable tr:nth-child(odd) {
        background-color: #358b4d;
        border-bottom: 1px #399753 solid;
    }

.redtext {
    font-weight: 400;
    background: #fc4040;
    border-radius: 10px;
    padding: 5px;
}


.yellowtext {
    background: #ffc500;
    font-weight: 400;
    border-radius: 10px;
    padding: 5px;
}

.greentext {
    background: #5bc705;
    font-weight: 400;
    border-radius: 10px;
    padding: 5px;
}

#tab4 span {
    display: block;
}

.thumbimgblock {
    background-color: #56954e;
    float: left;
    width: 96%;
    /*border: 1px #61a369 solid;*/
    padding: 2%;
    /*height: 433px;*/
    overflow: auto;
    margin-bottom: 25px;
}

.uploadcontrol {
    float: left;
    width: auto;
    margin-top: 10px;
    margin-left: 10px;
}

hr {
    float: left;
    width: 100%;
    background: #4a9d53;
    border: 0px #4a9d53 solid;
    height: 1px;
    margin-bottom: 25px;
}

.discussionbox table, #dvGIAllQueries table {
    width: 99%;
    border-collapse: collapse;
    border-spacing: 0px;
    border-color: #538b62;
}
.ApprovalBox table, #QueryMessageP table {
    width: 100%;
    border-collapse: collapse;
    border-spacing: 0px;
    border-color: #538b62;
}

.discussionbox table tr td, #dvGIAllQueries table tr td {
    padding: 8px;
    word-wrap: break-word;
}
.ApprovalBox table tr td, #QueryMessageP table tr td {
    padding: 1px;
    word-wrap: break-word;
}
.mailbody label {
    display: initial !important;
}

.modal {
    display: none; /* Hidden by default */
    position: fixed; /* Stay in place */
    z-index: 1; /* Sit on top */
    padding-top: 5px; /* Location of the box */
    padding-bottom: 5px;
    left: 0;
    top: 0;
    width: 100%; /* Full width */
    height: 100%; /* Full height */
    overflow: auto; /* Enable scroll if needed */
    background-color: rgb(0,0,0); /* Fallback color */
    background-color: rgba(0,0,0,0.9); /* Black w/ opacity */
}

/* Modal Content */
.modal-content {
    background-color: #4caf50;
    margin: 0 auto;
    padding: 20px;
    border: 1px solid #4caf50;
    width: 73%;
    float: left;
    margin-left: 20%;
    border-radius: 6px;
    min-height: 200px;
}

    /* The Close Button */
    .modal-content .close {
        color: #316c3d;
        float: right;
        font-size: 28px !important;
        font-weight: bold;
    }

        .modal-content .close:hover,
        .modal-content .close:focus {
            color: #000;
            text-decoration: none;
            cursor: pointer;
        }

.iconButton_Nugget_Post, .iconButton_Nugget_PostAll {
    background-position: left;
    background-image: none !important;
}

.iconButton_Nugget_PostAll {
    color: #fff !important;
    margin-left: 15px;
    border: 1px #316c3d solid;
    border-radius: 4px;
    padding: 3px 18px;
    background-color: #316c3d;
    margin-left: 15px !important;
}

    .iconButton_Nugget_PostAll:hover {
        color: #467d3f;
        border: 1px #54b121 solid;
        background-color: #54b121;
    }

.replytoselect {
    float: none;
}

.modal-content h4 {
    font-size: 16px;
}

.mailoption label {
    margin: 1px 0;
    margin-right: 8px;
}

.totalq {
    padding: 5px 0px 5px 10px;
    background-color: yellow;
    /*width: 24%;*/
    text-align: center;
    float: right;
    color: red;
}

    .totalq span {
        font-size: 13px;
        border: 1px #444544 solid;
        padding: 2px 10px;
        margin-left: 5px;
        background-color: #444544;
        color: #fff;
        border-radius: 4px;
    }

.onerowcheckbox label {
    display: inline-block;
    width: auto;
    margin: 0px;
}

.onerowcheckbox img {
    vertical-align: middle;
}

.onerowcheckbox input {
    margin-top: 5px;
}

.Approverbtn {
    display: block !important;
    float: left;
    width: 98%;
    padding: 5px;
    margin-left: 300px;
    margin-top: 50px;
    border-radius: 3px;
}

.ManageApprovers {
    float: right;
    margin: 10px 0;
}

    .ManageApprovers a {
        color: #ffffff;
        font-weight: 400;
        color: #ffffff;
        font-weight: 400;
        background-color: #33713f;
        padding: 10px 20px;
        border-radius: 30px;
        /* float: right; */
        margin-bottom: 5px;
        border: 1px #33713f solid;
    }

        .ManageApprovers a:hover {
            border: 1px #54b121 solid;
            background-color: #54b121;
        }

    .ManageApprovers img {
        vertical-align: middle;
        margin-left: 6px;
        margin-right: 0px;
    }

.mright {
    margin-right: 20px;
}

.ConfigureApprovertable {
}

    .ConfigureApprovertable tr {
        background: #6abf68;
    }

        .ConfigureApprovertable tr td {
            padding: 6px 12px;
            border-bottom: 1px #62ab60 solid;
            border-right: 1px #62ab60 solid;
        }

.btnapprove {
    width: auto !important;
    padding: 0;
    margin-top: 15px;
    float: right;
}

    .btnapprove button {
        padding: 10px 20px;
        background-color: #33713f;
        border: 1px #33713f solid;
        border-radius: 4px;
        color: #fff;
        cursor: pointer;
    }

        .btnapprove button:hover {
            border: 1px #666 solid;
            background-color: #666;
        }

.thumbimgbox {
    width: 154px;
    height: 160px;
    background-color: #6aac7e;
    text-align: center;
    float: left;
    border: 1px #28793e solid;
    padding: 5px;
    margin-bottom: 25px;
    box-shadow: 0px 0px 4px 4px #388d4f;
    color: #010005;
    margin: 18px;
}


    .thumbimgbox img {
        margin: 0px;
        margin-bottom: 5px;
        max-width: 65px;
        max-height: 65px;
    }

    .thumbimgbox span {
        margin-bottom: 5px;
    }

        .thumbimgbox span a {
            color: #010005;
        }

.tabtitle {
    float: left;
    width: 97.1%;
    padding: 0px 15px;
    font-size: 13px;
    text-transform: uppercase;
    font-weight: 600;
}

.uploadcontrol {
    float: left;
    width: auto;
    margin-top: 10px;
    margin-left: 10px;
}

hr {
    float: left;
    width: 100%;
    background: #4a9d53;
    border: 0px #4a9d53 solid;
    height: 1px;
    margin-bottom: 25px;
}

.selectoptions {
    display: inline-block !important;
    width: 99%;
    background: #8abe98;
    padding: 6px 5px;
    margin-bottom: 15px;
}

.typeselect {
    float: left;
    width: 150px;
    line-height: 25px;
    padding: 5px;
    margin-top: 2px;
    border: 1px #ffffff solid;
    border-radius: 3px;
}

input[type="file" i] {
    border: 2px dashed #488559;
    padding: 2px;
    margin-top: -7px;
    color: #3d3e40;
}

#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_lblPONumber .nubButton {
    /*background-image: url(images/nubs/nub.gif);*/
    background-repeat: no-repeat;
    background-position: left 2px;
    margin-left: 5px;
    padding-left: 13px;
    height: 12px;
    text-decoration: none;
    color: #e7d510;
}

#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_lblSONumber .nubButton {
    /*background-image: url(images/nubs/nub.gif);*/
    background-repeat: no-repeat;
    background-position: left 2px;
    margin-left: 5px;
    padding-left: 13px;
    height: 12px;
    text-decoration: none;
    color: #e7d510;
}

.approverblock {
    width: 100%;
    text-align: center;
    border-spacing: 0px;
    margin-bottom: 10px;
}

    .approverblock td {
        border-bottom: 1px #3a9653 solid;
        background: #74b185;
        width: 25%;
        text-align: center;
        border-right: 1px #c6e2ce solid;
        padding: 10px 10px;
    }

        .approverblock td:last-child {
            border-right: 0px #c6e2ce solid;
        }

        .approverblock td span {
            color: #2d703f;
            font-size: 11px;
            font-weight: bold;
            padding-top: 4px;
            display: inline-block;
        }

        .approverblock td img {
            width: 15%;
        }

.even td {
    border-bottom: 1px #3a9653 solid;
    background: #a0cead;
    color: #266136;
    font-size: 12px;
    font-weight: bold;
}

.odd td {
    border-bottom: 0px #c6e2ce solid;
    background: #358b4d;
    color: #d3fbde;
    text-transform: uppercase;
    font-size: 12px;
    font-weight: bold;
}

.approverblock select {
    font-size: 11px;
    width: 77%;
    border: 0px;
    border-radius: 3px;
    padding: 7px;
    margin-bottom: 8px;
}

.btnmanage {
    float: right;
    background-color: #4e4e4e;
    border: 1px #2d703f solid;
    color: #fff;
    padding: 5px 10px;
    border-radius: 3px;
    box-shadow: 1px 1px 1px 0px #2b5c38;
    cursor: pointer;
}

    .btnmanage:hover {
        background-color: #2d703f;
    }

.spanBorder {
    color: #2b2b2b !important;
    border: 2px dotted #efeff4 !important;
    padding: 3px 5px 3px 2px !important;
}

.GIpdfDelete {
    position: absolute;
    /* top: 3px; */
    /* right: 3px; */
    background-image: url("../../../../images/imgdelete.png");
    background-repeat: no-repeat;
    background-position: center center;
    /*display: none;*/
    /*visibility: hidden;*/
    width: 19px;
    height: 19px;
    cursor: pointer;
    margin-left: 130px;
}

#tdSQLQueryMessage tr:nth-child(even) {
    background-color: Lightgreen;
}

div.mailRecipient {
    padding-bottom: 8px;
    float: left;
    border-bottom: 1px #008000 solid;
    margin-right: 5px;
    margin-bottom: 10px;
    min-width: 125px;
    background-color: #59965b;
    border-top: 1px #008000 solid;
    padding: 5px 4px;
}

/*a.quickSearchReselect {
    display: inline;
    margin-left: 0px;
    float: right;
}*/
#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_ddlCCUsers_ctlTo_tdTitle {
    width: 34px !important;
}

#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_ddlCCUsers_ctlTo_pnlSelected {
    display: inline-block;
    width: 100%;
}

.modalRename-content {
    background-color: #4caf50;
    margin: 0 auto;
    padding: 20px;
    border: 1px solid #4caf50;
    width: 40%;
    float: left;
    margin-left: 30%;
    border-radius: 6px;
    min-height: 200px;
}

    .modalRename-content .close {
        color: #316c3d;
        float: right;
        font-size: 28px !important;
        font-weight: bold;
    }

        .modalRename-content .close:hover,
        .modalRename-content .close:focus {
            color: #000;
            text-decoration: none;
            cursor: pointer;
        }

    .modalRename-content h4 {
        font-size: 16px;
    }

.BuklDelete {
    margin-left: 58%;
    padding: 5px 11px 5px;
    background-color: #2f8ab9;
    color: white;
    font-weight: bold;
    border-color: #8abe98;
    border-radius: 5px;
    margin-top: 2px;
    border: 1;
}

.btnAutoSplit {
    background-color: #5b5b5b;
    position: relative;
    padding: 0px 10px 0px;
    border-radius: 5px;
    border-width: 0.1px;
    color: #fff;
    border-color: #5b5b5b;
    box-shadow: inset 0 2px 4px rgb(0 0 0 / 15%), 0 1px 2px rgb(0 0 0 / 5%);
}

    .btnAutoSplit:hover {
        background-color: #4b9f59;
        border-color: #4b9f59;
    }

.Splitmodal {
    display: none; /* Hidden by default */
    position: fixed; /* Stay in place */
    z-index: 1; /* Sit on top */
    padding-top: 100px; /* Location of the box */
    left: 0;
    top: 0;
    width: 150%; /* Full width */
    height: 100%; /* Full height */
    overflow: auto; /* Enable scroll if needed */
    background-color: rgb(0,0,0); /* Fallback color */
    background-color: rgba(0,0,0,0.9); /* Black w/ opacity */
}

/* Modal Content */
.Splitmodal-content {
    background-color: #4caf50;
    margin: 0 auto;
    padding: 20px;
    border: 1px solid #4caf50;
    width: 14%;
    float: left;
    margin-left: 20%;
    border-radius: 6px;
    min-height: 160px;
}

    .Splitmodal-content .close {
        color: #316c3d;
        float: right;
        font-size: 28px !important;
        font-weight: bold;
    }

        .Splitmodal-content .close:hover,
        .Splitmodal-content .close:focus {
            color: #000;
            text-decoration: none;
            cursor: pointer;
        }

    .Splitmodal-content h4 {
        font-size: 16px;
    }

.btnSplitSave {
    margin-left: 100px;
    background-color: #5b5b5b;
    border-radius: 7px;
    border-width: 1px;
    margin-top: 3px;
    padding: 5px 10px 5px;
    color: white;
    font-weight: bold;
    border-color: #5b5b5b;
}

    .btnSplitSave:hover {
        background-color: #4b9f59;
        border-color: #4b9f59;
    }
/*[003] start */
.textArea {
    display: inline-block;
    vertical-align: middle;
}
.error-highlight {
    background-color: rgb(153 0 0)!important;
}

/*[003] end */

textarea {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    width: 100%;
    display:block;
}
#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_txtActeoneTest {
    margin-top: -22px;
    margin-left: 158px;
}
#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_txtIsopropryle {
    margin-top: -22px;
    margin-left: 158px;
}
.GIDeleteCheckBox {
    position: absolute;
    background-repeat: no-repeat;
    background-position: center center;
    background-image: url("../../../../images/imgdelete.png");
    width: 19px;
    height: 19px;
    /*cursor: pointer;*/
    margin-left: 117px;
}
.GIWarninglbl
{
    background-color:yellow;
}
.GIWarningtd{
    color:red;
}
