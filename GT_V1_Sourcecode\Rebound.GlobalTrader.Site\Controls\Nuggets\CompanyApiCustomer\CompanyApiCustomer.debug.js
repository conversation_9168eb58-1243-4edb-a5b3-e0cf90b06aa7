///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Nuggets");

Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyApiCustomer = function (element) {
    Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyApiCustomer.initializeBase(this, [element]);
    this._intCompanyID = -1;
    this._blnLineLoaded = false;
    this._clientId = 0;
    this._inactive = false;
};

Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyApiCustomer.prototype = {

    get_intCompanyID: function () { return this._intCompanyID; }, set_intCompanyID: function (v) { if (this._intCompanyID !== v) this._intCompanyID = v; },
    get_ibtnAdd: function () { return this._ibtnAdd; }, set_ibtnAdd: function (v) { if (this._ibtnAdd !== v) this._ibtnAdd = v; },
    get_ibtnEdit: function () { return this._ibtnEdit; }, set_ibtnEdit: function (v) { if (this._ibtnEdit !== v) this._ibtnEdit = v; },
    get_ibtnBomMapping: function () { return this._ibtnBomMapping; }, set_ibtnBomMapping: function (v) { if (this._ibtnBomMapping !== v) this._ibtnBomMapping = v; },
    get_ibtnSupplierImport: function () { return this._ibtnSupplierImport; }, set_ibtnSupplierImport: function (v) { if (this._ibtnSupplierImport !== v) this._ibtnSupplierImport = v; },
    get_tbl: function () { return this._tbl; }, set_tbl: function (v) { if (this._tbl !== v) this._tbl = v; },


    initialize: function () {
        Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyApiCustomer.callBaseMethod(this, "initialize");

        //data
        this._strDataPath = "controls/Nuggets/CompanyApiCustomer";
        this._strDataObject = "CompanyApiCustomer";

        //nugget events
        this.addRefreshEvent(Function.createDelegate(this, this.getData));

        //other controls
        this._tbl.addSelectedIndexChanged(Function.createDelegate(this, this.tbl_SelectedIndexChanged));


        //add form
        if (this._ibtnAdd) {
            $R_IBTN.addClick(this._ibtnAdd, Function.createDelegate(this, this.showAddForm));
            this._frmAdd = $find(this._aryFormIDs[0]);
            this._frmAdd.addCancel(Function.createDelegate(this, this.hideAddForm));
            this._frmAdd.addSaveComplete(Function.createDelegate(this, this.saveAddComplete));
        }

        //edit form
        if (this._ibtnEdit) {
            $R_IBTN.addClick(this._ibtnEdit, Function.createDelegate(this, this.showEditForm));
            this._frmEdit = $find(this._aryFormIDs[1]);
            this._frmEdit.addCancel(Function.createDelegate(this, this.hideEditForm));
            this._frmEdit.addSaveComplete(Function.createDelegate(this, this.saveEditComplete));
        }
        if (this._ibtnBomMapping) {
            debugger;
            $R_IBTN.addClick(this._ibtnBomMapping, Function.createDelegate(this, this.showAddMappingForm));
            this._frmAddMapping = $find(this._aryFormIDs[2]);
            this._frmAddMapping.addCancel(Function.createDelegate(this, this.hideAddMappingForm));
            this._frmAddMapping.addSaveComplete(Function.createDelegate(this, this.saveAddMappingComplete));
        }

        if (this._ibtnSupplierImport) {
            debugger;
            $R_IBTN.addClick(this._ibtnSupplierImport, Function.createDelegate(this, this.showOfferForm));
            this.frmSupplierImport = $find('ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmSupplierImport_ctlDB');
            this.frmSupplierImport.addCancel(Function.createDelegate(this, this.hideOfferForm));
            this.frmSupplierImport.addSaveComplete(Function.createDelegate(this, this.saveOfferComplete));
        }

        this.getData();
        this.getCompanyInactive();
    },

    dispose: function () {
        if (this.isDisposed) return;
        if (this._ibtnAdd) $R_IBTN.clearHandlers(this._ibtnAdd);
        if (this._ibtnEdit) $R_IBTN.clearHandlers(this._ibtnEdit);
        if (this._ibtnBomMapping) $R_IBTN.clearHandlers(this._ibtnBomMapping);
        if (this._ibtnSupplierImport) $R_IBTN.clearHandlers(this._ibtnSupplierImport);
        if (this._ibtnAdd) $R_IBTN.clearHandlers(this._ibtnAdd);
        if (this._frmAdd) this._frmAdd.dispose();
        if (this._frmEdit) this._frmEdit.dispose();
        if (this._ibtnBomMapping) this._frmAddMapping.dispose();
        if (this._ibtnSupplierImport) this.frmSupplierImport.dispose();
        //if (this._ibtnSupplierImport) this._ibtnSupplierImport.dispose();
        if (this._tbl) this._tbl.dispose();
        this._frmAdd = null;
        this._ibtnBomMapping = null;
        this._ibtnSupplierImport = null;
        this._intCompanyID = null;
        this._blnLineLoaded = null;
        this._inactive = null;
        Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyApiCustomer.callBaseMethod(this, "dispose");
    },

    enableEditButtons: function (bln) {
        if (bln) {

            var count = this._tbl.countRows();
            /* if (this._ibtnAdd) $R_IBTN.enableButton(this._ibtnAdd, true);*/
            if (this._ibtnEdit) $R_IBTN.enableButton(this._ibtnEdit, !this._inactive && this._blnLineLoaded);

        } else {
            if (this._ibtnEdit) $R_IBTN.enableButton(this._ibtnEdit, false);

        }
    },
    disableAddButtons: function (bln) {
        if (bln) {
            debugger;
            var count = this._tbl.countRows();
            if (count == 0) {
                if (this._ibtnAdd) $R_IBTN.enableButton(this._ibtnAdd, !this._inactive);


            } else {
                if (this._ibtnAdd) $R_IBTN.enableButton(this._ibtnAdd, false);

            }
        }
    },

    tbl_SelectedIndexChanged: function () {
        this._intLineID = this._tbl._varSelectedValue;
        if (this._intLineID)
            this._blnLineLoaded = true;
        this.enableEditButtons(true);
    },

    getData: function () {
        this._blnLineLoaded = false;
        $R_FN.showElement(this._pnlLineDetail, false);

        this.enableEditButtons(true);
        this.showLoading(true);
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData(this._strDataPath);
        obj.set_DataObject(this._strDataObject);
        obj.set_DataAction("GetCertificates");
        obj.addParameter("CompanyID", this._intCompanyID);
        obj.addDataOK(Function.createDelegate(this, this.getDataOK));
        obj.addError(Function.createDelegate(this, this.getDataError));
        obj.addTimeout(Function.createDelegate(this, this.getDataError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    getDataOK: function (args) {
        debugger;
        var result = args._result;
        this.showLoading(false);
        this._tbl.clearTable();
        if (result.Lines) {
            for (var i = 0; i < result.Lines.length; i++) {
                var row = result.Lines[i];
                var aryData = [
                    //row.ID
                    //,row.companyNo
                    $R_FN.setCleanTextValue(row.Email)
                    , $R_FN.setCleanTextValue(row.Mobile)
                    , $R_FN.setCleanTextValue(row.ContactName)
                    , $R_FN.setCleanTextValue((row.InActive == true) ? "Yes" : "No")
                    , $R_FN.setCleanTextValue((row.IsBomUser == true) ? "Yes" : "No")
                    , $R_FN.setCleanTextValue((row.InSupUser == true) ? "Yes" : "No")
                    

                ];

                var objExtra = {
                    Password: row.Password
                    , InActive: row.InActive
                    , IsBomUser: row.IsBomUser
                    , InSupUser: row.InSupUser
                    , InCountryCode: row.CountryCode

                };
                var strCSS = (row.InActive) ? "ceased" : "";
                debugger;
                this._tbl.addRow(aryData, row.ID, row.ID == this._intLineID, objExtra, strCSS);
                row = null; strCSS = null;
            }
        }
        this.disableAddButtons(true);
        this._intLineCount = this._tbl.countRows();
        this.showContent(true);
        this.showContentLoading(false);
        this._tbl.resizeColumns();
        if (this._intLineID > 0) this.tbl_SelectedIndexChanged();
        this.enableEditButtons(true);

    },

    getDataError: function (args) {
        this.showError(true, args.get_ErrorMessage());
    },

    getCompanyInactive: function () {
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData(this._strDataPath);
        obj.set_DataObject(this._strDataObject);
        obj.set_DataAction("GetCompanyDetailInactive");
        obj.addParameter("id", this._intCompanyID);
        obj.addDataOK(Function.createDelegate(this, this.getCompanyInactiveOK));
        obj.addError(Function.createDelegate(this, this.getCompanyInactiveError));
        obj.addTimeout(Function.createDelegate(this, this.getCompanyInactiveError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    getCompanyInactiveOK: function (args) {
        var result = args._result;
        this._inactive = result.Inactive;
        if (this._ibtnEdit) $R_IBTN.enableButton(this._ibtnAdd, !this._inactive);
        if (this._ibtnEdit) $R_IBTN.enableButton(this._ibtnEdit, !this._inactive);
        if (this._ibtnEdit) $R_IBTN.enableButton(this._ibtnSupplierImport, !this._inactive);
        if (this._ibtnEdit) $R_IBTN.enableButton(this._ibtnBomMapping, !this._inactive);
    },

    getCompanyInactiveError: function (args) {
        this.showError(true, args.get_ErrorMessage());
    },

    showEditForm: function () {

        debugger;
        //This code is making edit show 
        var sx = this._tbl.getSelectedExtraData().InCountryCode;
        this._frmEdit._intCompanyID = this._intCompanyID;
        this._frmEdit._intLineID = this._intLineID;
        this._frmEdit.setFieldValue("ctlEmail", this._tbl.getSelectedCellValue(0));
        this._frmEdit.setFieldValue("ctlMobile", this._tbl.getSelectedCellValue(1));
        this._frmEdit.setFieldValue("ctlContactPerson", this._tbl.getSelectedCellValue(2));
        $("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmEdit_ctlDB_ctlMobile_ctl03_txtCountryCode").val(this._tbl.getSelectedExtraData().InCountryCode);
        //this._frmEdit.setFieldValue("txtCountryCode", this._tbl.getSelectedExtraData().InCountryCode);
        //if (this._tbl.getSelectedExtraData().InActive == 1) {
        //    this._frmEdit.setFieldValue("ctlInActive", this._tbl.getSelectedExtraData().InActive).attr("checked", true);
        //} else {
        //    this._frmEdit.setFieldValue("ctlInActive", this._tbl.getSelectedExtraData().InActive).attr("checked", false);
        //}
        if (this._tbl.getSelectedExtraData().InActive) {
            this._frmEdit.setFieldValue("ctlInActive", this._tbl.getSelectedExtraData().InActive);
            // $("#ctlInActive").attr("checked", true);
        } 
        if (this._tbl.getSelectedExtraData().IsBomUser) {
           // $("#ctlBomUser").attr("checked", true);
            this._frmEdit.setFieldValue("ctlBomUser", this._tbl.getSelectedExtraData().IsBomUser);
        } 
        if (this._tbl.getSelectedExtraData().InSupUser) {
           // $("#ctlSupplierUser").attr("checked", true);
            this._frmEdit.setFieldValue("ctlSupplierUser", this._tbl.getSelectedExtraData().InSupUser)
        } 
        //if (this._tbl.getSelectedExtraData().IsBomUser) {
        //    this._frmEdit.setFieldValue("ctlBomUser", this._tbl.getSelectedExtraData().IsBomUser).attr("checked", true);
        //} else {
        //    this._frmEdit.setFieldValue("ctlBomUser", this._tbl.getSelectedExtraData().IsBomUser).attr("checked", false);
        //}
        //if (this._tbl.getSelectedExtraData().InSupUser) {
        //    this._frmEdit.setFieldValue("ctlSupplierUser", this._tbl.getSelectedExtraData().InSupUser).attr("checked", true);
        //} else {
        //    this._frmEdit.setFieldValue("ctlSupplierUser", this._tbl.getSelectedExtraData().InSupUser).attr("checked", false);
        //}
        this._frmEdit.setFieldValue("ctlPassword", this._tbl.getSelectedExtraData().Password);

        this.showForm(this._frmEdit, true);
    },

    hideEditForm: function () {
        $("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmEdit_ctlDB_ctlPassword").css("background-color", "#56954E");
        $("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmEdit_ctlDB_ctlConfirmPassword").css("background-color", "#56954E");
        $("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmEdit_ctlDB_ctlPassword_ctl03_txtPassword").val('');
        $("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmEdit_ctlDB_ctlConfirmPassword_ctl03_txtConfirmPassword").val('');
        $('#checkbox2').prop('checked', false);
        this.showForm(this._frmEdit, false);
    },

    saveEditComplete: function () {
        this.hideEditForm();
        this.showSavedOK(true, $R_RES.ChangesSavedSuccessfully);
        this.getData();
    },
    showAddMappingForm: function () {
        // debugger;
        this._clientId = $('#ctl00_ddlClientByMaster_ddl').val();
        this._frmAddMapping._intCompanyID = this._intCompanyID;
        this.showForm(this._frmAddMapping, true);
    },

    hideAddMappingForm: function () {
        resetField();
        this.showForm(this._frmAddMapping, false);
    },

    saveAddMappingComplete: function () {
        this.hideAddForm();
        this.showSavedOK(true, $R_RES.ChangesSavedSuccessfully);
        this._intLineID = this._frmAddMapping._intNewID;
        this.getData();
    },
    showOfferForm: function () {
        // debugger;
        this._clientId = $('#ctl00_ddlClientByMaster_ddl').val();
        //this.frmSupplierImport._intCompanyID = this._intCompanyID;
        this.showForm(this.frmSupplierImport, true);
    },

    hideOfferForm: function () {
        //window.location.href = window.location.href;
        resetFieldSupp();
        this.showForm(this._frmAddMapping, false);
    },

    saveOfferComplete: function () {
        this.hideAddForm();
        this.showSavedOK(true, $R_RES.ChangesSavedSuccessfully);
        this._intLineID = this.frmSupplierImport._intNewID;
        this.getData();
    },

    showAddForm: function () {
        this._frmAdd._intCompanyID = this._intCompanyID;
        this.showForm(this._frmAdd, true);
    },

    hideAddForm: function () {
        this.showForm(this._frmAdd, false);
    },

    saveAddComplete: function () {
        this.hideAddForm();
        this.showSavedOK(true, $R_RES.ChangesSavedSuccessfully);
        this._intLineID = this._frmAdd._intNewID;
        this.getData();
    }


};

Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyApiCustomer.registerClass("Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyApiCustomer", Rebound.GlobalTrader.Site.Controls.Nuggets.Base);
