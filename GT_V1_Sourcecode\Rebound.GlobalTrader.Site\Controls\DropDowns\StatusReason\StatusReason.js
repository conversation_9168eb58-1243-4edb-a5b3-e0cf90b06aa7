Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");Rebound.GlobalTrader.Site.Controls.DropDowns.StatusReason=function(n){Rebound.GlobalTrader.Site.Controls.DropDowns.StatusReason.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.DropDowns.StatusReason.prototype={get_section:function(){return this._section},set_section:function(n){this._section!==n&&(this._section=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.DropDowns.StatusReason.callBaseMethod(this,"initialize");this.addSetupDataCall(Function.createDelegate(this,this.setupDataCall));this.addGetDataOK(Function.createDelegate(this,this.dataCallOK))},dispose:function(){this.isDisposed||Rebound.GlobalTrader.Site.Controls.DropDowns.StatusReason.callBaseMethod(this,"dispose")},setupDataCall:function(){this._objData.set_PathToData("controls/DropDowns/StatusReason");this._objData.set_DataObject("StatusReason");this._objData.set_DataAction("GetData");this._objData.addParameter("section",this._section)},dataCallOK:function(){var t=this._objData._result,n;if(t.Types)for(n=0;n<t.Types.length;n++)this.addOption(t.Types[n].Name,t.Types[n].ID,t.Types[n].Email)}};Rebound.GlobalTrader.Site.Controls.DropDowns.StatusReason.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.StatusReason",Rebound.GlobalTrader.Site.Controls.DropDowns.Base);