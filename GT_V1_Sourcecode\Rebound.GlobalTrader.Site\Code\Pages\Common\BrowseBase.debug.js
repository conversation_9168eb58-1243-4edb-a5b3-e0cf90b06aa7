///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference name="~/Controls/IconButton/IconButton.js" />
///<reference name="~/Controls/ReboundTextBox/ReboundTextBox.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Pages");

Rebound.GlobalTrader.Site.Pages.BrowseBase = function(el) { 
	Rebound.GlobalTrader.Site.Pages.BrowseBase.initializeBase(this, [el]);
};

Rebound.GlobalTrader.Site.Pages.BrowseBase.prototype = {

    get_ctlDataListNugget: function() { return this._ctlDataListNugget; }, set_ctlDataListNugget: function(v) { if (this._ctlDataListNugget !== v) this._ctlDataListNugget = v; },
    get_ctlPageTitle: function() { return this._ctlPageTitle; }, set_ctlPageTitle: function(v) { if (this._ctlPageTitle !== v) this._ctlPageTitle = v; },
    get_intCurrentTab: function() { return this._intCurrentTab; }, set_intCurrentTab: function(v) { if (this._intCurrentTab !== v) this._intCurrentTab = v; },
    //[001] code start
    get_aryTabs: function() { return this._aryTabs; }, set_aryTabs: function(value) { if (this._aryTabs !== value) this._aryTabs = value; },
    //[001] code end

    //get_btnAssignToMe: function() { return this._btnAssignToMe; }, set_btnAssignToMe: function(v) { if (this._btnAssignToMe !== v) this._btnAssignToMe = v; },
    //get_ibtnAssignToMe: function() { return this._ibtnAssignToMe; }, set_ibtnAssignToMe: function(v) { if (this._ibtnAssignToMe !== v) this._ibtnAssignToMe = v; },
    initialize: function() {
        //
        //this._ibtnAssignToMe;
        //= $get(this._aryButtonIDs[0]);

        //        if (this._ibtnAssignToMe) {

        //            $R_IBTN.addClick(this._ibtnAssignToMe, Function.createDelegate(this, this.assignMe));
        //        }
        Rebound.GlobalTrader.Site.Pages.BrowseBase.callBaseMethod(this, "initialize");
    },
    //    assignToMe: function() {
    //        alert("Test");
    //    },
    goInit: function() {
        //
        if (this._ctlDataListNugget) this._ctlDataListNugget.addAskPageToChangeTab(Function.createDelegate(this, this.ctlDataListNugget_AskPageToChangeTab));
        //[001] code start
        //if (this._btnAssignToMe) this._btnAssignToMe.addbtnAssignToMe(Function.createDelegate(this, this.assignToMe));
        this.invisibleTabs();
        //[001] code end
        Rebound.GlobalTrader.Site.Pages.BrowseBase.callBaseMethod(this, "goInit");
    },

    dispose: function() {
        if (this.isDisposed) return;
        if (this._ctlPageTitle) this._ctlPageTitle.dispose();
        if (this._ctlDataListNugget) this._ctlDataListNugget.dispose();
        this._ctlPageTitle = null;
        this._ctlDataListNugget = null;
        this._intCurrentTab = null;
        Rebound.GlobalTrader.Site.Pages.BrowseBase.callBaseMethod(this, "dispose");
    },

//    assignToMe: function() {
//        alert("Prakash");
//        //$R_FN.openPrintWindow($R_ENUM$PrintObject.Invoice, this._intInvoiceID);
//    },

    changeTab: function(intTab) {
        if (this._ctlDataListNugget._blnGettingData) return;
        this._ctlPageTitle.selectTab(intTab);
        //[001] code start
        this.invisibleTabs();
        //[001] code end
        this._ctlDataListNugget._intCurrentTab = this._ctlPageTitle._intCurrentTab;
        this._ctlDataListNugget.onPageTabChanged();
    },

    ctlDataListNugget_AskPageToChangeTab: function() {
        this._ctlPageTitle.selectTab(this._ctlDataListNugget._intCurrentTab);
    },
    //    assignMe: function() {
    //        
    //        alert("Test");
    //    },
    //[001] code start
    invisibleTabs: function() {
        if (this._aryTabs == "undefined" || this._aryTabs == null) return;
        for (var i = 0, l = this._aryTabs.length; i < l; i++) {
            this._ctlPageTitle.showTab(this._aryTabs[i], false);
        }
    }
    //[001] code end


};

Rebound.GlobalTrader.Site.Pages.BrowseBase.registerClass("Rebound.GlobalTrader.Site.Pages.BrowseBase", Rebound.GlobalTrader.Site.Pages.Content, Sys.IDisposable);
