///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//
// RP 06.10.2009:
// - Changes due to changes in base class
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");

Rebound.GlobalTrader.Site.Controls.DropDowns.BomStatus = function(element) {
Rebound.GlobalTrader.Site.Controls.DropDowns.BomStatus.initializeBase(this, [element]);
this._intCompanyID = null;
};

Rebound.GlobalTrader.Site.Controls.DropDowns.BomStatus.prototype = {
get_intCompanyID: function() { return this._intCompanyID; }, set_intCompanyID: function(value) { if (this._intCompanyID !== value) this._intCompanyID = value; }, 

    initialize: function() {
        Rebound.GlobalTrader.Site.Controls.DropDowns.BomStatus.callBaseMethod(this, "initialize");
        this.addSetupDataCall(Function.createDelegate(this, this.setupDataCall));
        this.addGetDataOK(Function.createDelegate(this, this.dataCallOK));
    },

    dispose: function() {
        if (this.isDisposed) return;
        Rebound.GlobalTrader.Site.Controls.DropDowns.BomStatus.callBaseMethod(this, "dispose");
    },

    setupDataCall: function() {
    this._objData.set_PathToData("controls/DropDowns/BomStatus");
    this._objData.set_DataObject("BomStatus");
        this._objData.set_DataAction("GetData");
        this._objData.addParameter("id", this._intCompanyID);
    },

    dataCallOK: function() {
        //
        var result = this._objData._result;
        if (result.BomStatus) {
            for (var i = 0; i < result.BomStatus.length; i++) {
                this.addOption(result.BomStatus[i].Name, result.BomStatus[i].ID);
            }
        }
    }

};

Rebound.GlobalTrader.Site.Controls.DropDowns.BomStatus.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.BomStatus", Rebound.GlobalTrader.Site.Controls.DropDowns.Base);
