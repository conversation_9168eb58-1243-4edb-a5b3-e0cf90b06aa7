///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.HomeNuggets");

Rebound.GlobalTrader.Site.Controls.HomeNuggets.TopSalespersons = function(element) {
Rebound.GlobalTrader.Site.Controls.HomeNuggets.TopSalespersons.initializeBase(this, [element]);
	this._blnCanViewAllStatistics = true;
};

Rebound.GlobalTrader.Site.Controls.HomeNuggets.TopSalespersons.prototype = {

	get_tblTopSalespersons: function() { return this._tblTopSalespersons; }, set_tblTopSalespersons: function(value) { if (this._tblTopSalespersons !== value) this._tblTopSalespersons = value; },

    initialize: function() {
    Rebound.GlobalTrader.Site.Controls.HomeNuggets.TopSalespersons.callBaseMethod(this, "initialize");
        this.addRefreshEvent(Function.createDelegate(this, this.getData));
    },

    dispose: function() {
        if (this.isDisposed) return;
		if (this._tblTopSalespersons) this._tblTopSalespersons.dispose();
        this._tblTopSalespersons = null;
        Rebound.GlobalTrader.Site.Controls.HomeNuggets.TopSalespersons.callBaseMethod(this, "dispose");
    },

    setupLoadingState: function() {
        this._tblTopSalespersons.show(false);
        Rebound.GlobalTrader.Site.Controls.HomeNuggets.TopSalespersons.callBaseMethod(this, "setupLoadingState");
    },

    getData: function() {
        this.setupLoadingState();
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/HomeNuggets/TopSalespersons");
        obj.set_DataObject("TopSalespersons");
        obj.set_DataAction("GetData");
        obj.addParameter("rowcount", this._intRowCount);
        obj.addDataOK(Function.createDelegate(this, this.getDataComplete));
        obj.addError(Function.createDelegate(this, this.homeNuggetDataError));
        obj.addTimeout(Function.createDelegate(this, this.homeNuggetDataError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    getDataComplete: function(args) {
        this.showNoneFoundOrContent(args._result.Count);
        var result = args._result;
        //received
        this._tblTopSalespersons.clearTable();
        for (var i = 0; i < result.TopSalespersons.length; i++) {
            var row = result.TopSalespersons[i];
            var aryData = [
				row.Name
				, row.GrossProfit
				, row.Margin
			];
            this._tblTopSalespersons.addRow(aryData, null);
        }
        this._tblTopSalespersons.show(result.TopSalespersons.length > 0);
        this.hideLoading();
    }
};

Rebound.GlobalTrader.Site.Controls.HomeNuggets.TopSalespersons.registerClass("Rebound.GlobalTrader.Site.Controls.HomeNuggets.TopSalespersons", Rebound.GlobalTrader.Site.Controls.HomeNuggets.Base, Sys.IDisposable);
