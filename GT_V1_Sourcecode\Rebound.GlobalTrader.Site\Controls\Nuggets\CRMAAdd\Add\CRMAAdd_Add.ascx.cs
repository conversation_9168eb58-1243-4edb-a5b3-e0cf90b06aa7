//---------------------------------------------------------------------------------------------------------
// RP 18.12.2009:
// - allow passing a company name to initially search for (task 357)
//Marker     Changed by      Date         Remarks
//[001]      Vinay           12/10/2012   Upload PDF document for invoices
//[002]      Vinay           30/10/2012   Add link in the Invoice section to create CRMA and Credit

//---------------------------------------------------------------------------------------------------------
using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site;
using Rebound.GlobalTrader.Site.Controls;

namespace Rebound.GlobalTrader.Site.Controls.Forms {

	public partial class CRMAAdd_Add : Base {

		#region Locals

		#endregion

		#region Overrides

		/// <summary>
		/// OnInit
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
			base.OnInit(e);
			TitleText = Functions.GetGlobalResource("FormTitles", "CRMAAdd_Add");
			AddScriptReference("Controls.Nuggets.CRMAAdd.Add.CRMAAdd_Add.js");
		}

		/// <summary>
		/// OnPreRender
		/// </summary>
		/// <param name="e"></param>
		protected override void OnPreRender(EventArgs e) {
			SetupScriptDescriptors();
			base.OnPreRender(e);
		}

		#endregion

		/// <summary>
		/// Setup Script Descriptors
		/// </summary>
		private void SetupScriptDescriptors() {
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.Forms.CRMAAdd_Add", ctlDesignBase.ClientID);
			_scScriptControlDescriptor.AddElementProperty("ibtnSend", FindIconButton("ibtnSend").ClientID);
			if (FooterLinksHolder != null) _scScriptControlDescriptor.AddElementProperty("ibtnSend_Footer", FindFooterIconButton("ibtnSend").ClientID);
			_scScriptControlDescriptor.AddElementProperty("ibtnContinue", (FindIconButton("ibtnContinue").ClientID));
			if (FooterLinksHolder != null) _scScriptControlDescriptor.AddElementProperty("ibtnContinue_Footer", (FindFooterIconButton("ibtnContinue").ClientID));
			_scScriptControlDescriptor.AddComponentProperty("ctlSelectInvoice", ((ItemSearch.Base)FindContentControl("ctlSelectInvoice")).ctlDesignBase.ClientID);
			_scScriptControlDescriptor.AddProperty("intLoginID", SessionManager.LoginID);
			_scScriptControlDescriptor.AddProperty("intCompanyID", _objQSManager.CompanyID);
			_scScriptControlDescriptor.AddProperty("strCompanyName", _objQSManager.CompanyName);
			_scScriptControlDescriptor.AddProperty("intContactID", _objQSManager.ContactID);
			_scScriptControlDescriptor.AddProperty("strContactName", _objQSManager.ContactName);
			_scScriptControlDescriptor.AddProperty("strSearchCompanyName", _objQSManager.SearchCompanyName);
            //[001] code start
            _scScriptControlDescriptor.AddProperty("intQSInvoiceID", _objQSManager.InvoiceID);
            //[001] code start
			BLL.Warehouse whDefault = BLL.Warehouse.GetDefault(SessionManager.ClientID);
			if (whDefault != null) _scScriptControlDescriptor.AddProperty("intDefaultWarehouseID", whDefault.WarehouseId);
		}

	}
}
