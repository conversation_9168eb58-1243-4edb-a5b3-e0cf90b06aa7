﻿GO
/*
--===========================================================================================  
TASK			UPDATED BY       DATE				ACTION		DESCRIPTION  
[US-212758]     <PERSON>uc Hoang		 31-Oct-2024		CREATE		Prospect Qualification - Power Automate Function for Reminder
=============================================================================================  
*/

IF EXISTS(SELECT 1 FROM dbo.tbPowerApp_urls WITH(NOLOCK) WHERE FlowName = 'Prospect_Qualification_Notification')
    BEGIN
        DELETE FROM dbo.tbPowerApp_urls WHERE FlowName = 'Prospect_Qualification_Notification'
    END
GO


