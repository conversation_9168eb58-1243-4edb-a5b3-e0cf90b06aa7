﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

/* 
===========================================================================================
TASK      		UPDATED BY     	DATE         	ACTION 		DESCRIPTION
[US-214760]		An.TranTan		14-Jan-2025		CREATE		Import BOM Sourcing Results
[US-232568]		An.TranTan		17-Feb-2025		Update		Allow import for multiple req in single file
[US-232568]		An.TranTan		19-Feb-2025		Update		Update customer ref no for multiple req
[US-232568]		An.TranTan		28-Feb-2025		Update		Price of sourcing result should be Sell Price in template
===========================================================================================
*/
CREATE OR ALTER PROCEDURE [dbo].[usp_Import_BOMSourcingResults]
	@UserID INT
	,@ImportCount INT OUTPUT                                                
	,@ImportMessage NVARCHAR(2000) OUTPUT
AS
BEGIN
    SET NOCOUNT ON;
	
	DECLARE @OriginalFilename NVARCHAR(200)
			,@GeneratedFilename NVARCHAR(200)
			,@BOMNo INT
	DECLARE @InsertedOffers TABLE (OfferId INT);
	DECLARE @tbTargetRequirement TABLE(
		CustomerRequirementId INT
		, CustomerRequirementNumber INT
		, BOMNo INT
		, ClientNo INT
		, ProductNo INT
		, ManufacturerNo INT
		, GlobalProductNo INT
		, OfferProductNo INT
		, ClientCompanyNo INT	--represent company of DMCC in client side
	);

	BEGIN TRY
	BEGIN TRANSACTION
    IF EXISTS
    (
        SELECT TOP 1 1
        FROM BorisGlobalTraderImports.dbo.tbBOMSourcing_ToBeImported
        WHERE CreatedBy = @UserID
    )
    BEGIN
		/*================ Get unique data from table tbBOMSourcing_ToBeImported*/
		INSERT INTO @tbTargetRequirement
		(
			CustomerRequirementId
			, CustomerRequirementNumber
			, BOMNo 
			, ClientNo 
			, ProductNo 
			, ManufacturerNo 
			, GlobalProductNo 
			, OfferProductNo 
			, ClientCompanyNo
		)
		SELECT DISTINCT 
			cr.CustomerRequirementId
			,cr.CustomerRequirementNumber
			,cr.BOMNo
			,cr.ClientNo
			,cr.ProductNo
			,cr.ManufacturerNo
			,p.GlobalProductNo
			,p1.ProductId
			,com.CompanyId
		FROM BorisGlobalTraderImports.dbo.tbBOMSourcing_ToBeImported s WITH(NOLOCK)
		JOIN tbCustomerRequirement cr WITH(NOLOCK) ON cr.CustomerRequirementId = s.CustomerRequirementNo
		LEFT JOIN tbProduct p WITH(NOLOCK) ON p.ProductId = cr.ProductNo
		LEFT JOIN tbProduct p1 WITH(NOLOCK) ON p1.GlobalProductNo = p.GlobalProductNo
			AND p1.ClientNo = cr.ClientNo
			AND p1.Inactive = 0
		LEFT JOIN tbCompany com WITH(NOLOCK) on com.ClientNo = cr.ClientNo AND com.IsPOHub = 1
		WHERE s.CreatedBy = @UserID; 

		SELECT TOP 1 
			@OriginalFilename = OriginalFilename
			,@GeneratedFilename = GeneratedFilename
		FROM BorisGlobalTraderImports.dbo.tbBOMSourcing_ToBeImported WHERE CreatedBy = @UserID;
		/*==================================================================================*/

		--insert into offer, re-use logic from usp_insert_SourcingResultWithOffer
		INSERT INTO [BorisGlobalTraderImports].dbo.tboffer
		(
		    [FullPart]
			,[Part]
			,[SupplierNo]
			,[SupplierName]
			,[CurrencyNo]
			,[ManufacturerNo]
			,[ManufacturerName]
			,[ProductNo]
			,[PackageNo]
			,[DateCode]
			,[Quantity]
			,[Price]
			,[OriginalEntryDate]
			,[UpdatedBy]
			,[DLUP]
			,[OfferStatusNo]
			,[OfferStatusChangeDate]
			,[OfferStatusChangeLoginNo]
			,[Notes]
			,[SPQ]
			,[LeadTime]
			,[ROHSStatus]
			,[FactorySealed]
			,[MSL]
			,[SupplierManufacturerName]
			,[SupplierDateCode]
			,[SupplierPackageType]
			,[SupplierMOQ]
			,[SupplierTotalQSA]
			,[SupplierLTB]
			,[SupplierNotes]
			,[IsPoHub]
			,[MSLLevelNo]
			,[SellPrice]
			,[ShippingCost]
			,[RegionNo]
			,[DeliveryDate]
			,[Salesman]
			,[ReferenceRequirementNo]
		)
		OUTPUT Inserted.OfferId INTO @InsertedOffers(OfferId)
		SELECT 
			dbo.Ufn_get_fullpart(s.SupplierPart)
			,s.SupplierPart
			,s.SupplierNo
			,s.SupplierName
			,s.CurrencyNo
			,s.ManufacturerNo
			,s.ManufacturerName
			,tr.OfferProductNo--@ReqProductNo
			,s.PackageNo
			,s.DateCode
			,s.Quantity
			,s.BuyPrice
			,GETDATE()		--OriginalEntryDate
			,@UserId		--updated by
			,GETDATE()		--dlup
			,s.OfferStatusNo
			,GETDATE()		--OfferStatusChangeDate
			,@UserId		--OfferStatusChangeLoginNo
			,s.Notes
			,s.SPQ
			,s.LeadTime
			,s.ROHSStatus
			,s.FactorySealed
			,msl.MSLLevel
			,s.ManufacturerName
			,s.DateCode
			,p.PackageName
			,s.MOQ
			,s.QtyInStock
			,s.LastTimeBuy
			,s.Notes
			,1 --IsPOHub
			,s.MSLLevelNo
			,s.SellPrice
			,s.ShippingCost
			,s.RegionNo
			,s.DeliveryDate
			,@UserId --salesman
			,s.CustomerRequirementNo	--reference requirement no
		FROM BorisGlobalTraderImports.dbo.tbBOMSourcing_ToBeImported s
		JOIN @tbTargetRequirement tr on tr.CustomerRequirementId = s.CustomerRequirementNo
		LEFT JOIN tbPackage p WITH(NOLOCK) ON p.PackageId = s.PackageNo
		LEFT JOIN tbMSLLevel msl WITH(NOLOCK) ON msl.MSLLevelId = s.MSLLevelNo
		WHERE s.CreatedBy = @UserID;

		--INSERT to tbSourcingResult, re-use logic: usp_insert_SourcingResult_From_QuotesToClient
		--get offer currencies-------------
		SELECT 
			o.OfferId
			,ISNULL(o.CurrencyNo,0) AS OfferCurrencyNo
			,dbo.ufn_get_exchange_rate(ISNULL(o.CurrencyNo, 0), GETDATE()) AS BuyExchangeRate
			,l.LinkMultiCurrencyId AS LinkMultiCurrencyNo
			,l.SupplierCurrencyNo AS ClientCurrencyNo
		INTO #tempOfferCurrency
		FROM [BorisGlobalTraderImports].dbo.tboffer o WITH(NOLOCK)
		JOIN @InsertedOffers i on i.OfferId = o.OfferId
		JOIN @tbTargetRequirement req on o.ReferenceRequirementNo = req.CustomerRequirementId
		LEFT JOIN tbCurrency c WITH(NOLOCK) ON c.CurrencyId = o.CurrencyNo 
		LEFT JOIN tbLinkMultiCurrency l WITH(NOLOCK) on l.GlobalCurrencyNo = c.GlobalCurrencyNo
		WHERE l.ClientNo = req.ClientNo AND c.ClientNo = 114;

		-----------------------------------------
		INSERT INTO dbo.tbSourcingResult
        (
            CustomerRequirementNo,
            SourcingTable,
            SourcingTableItemNo,
            FullPart,
            Part,
            ManufacturerNo,
            DateCode,
            ProductNo,
            PackageNo,
            Quantity,
            Price,
            CurrencyNo,
            OriginalEntryDate,
            Salesman,
            OfferStatusNo,
            OfferStatusChangeDate,
            OfferStatusChangeLoginNo,
            SupplierNo,
            UpdatedBy,
            DLUP,
            TypeName,
            Notes,
            ROHS,
            POHubCompanyNo,
            SupplierPrice,
            ClientCompanyNo,
            EstimatedShippingCost,
            ClientCurrencyNo,
            SupplierManufacturerName,
            SupplierDateCode,
            SupplierPackageType,
            SupplierProductType,
            SupplierMOQ,
            SupplierTotalQSA,
            SupplierLTB,
            SupplierNotes,
            SPQ,
            LeadTime,
            ROHSStatus,
            FactorySealed,
            MSLLevelNo,
            MSL,
            Buyer,
            ActualPrice,
            ActualCurrencyNo,
            ExchangeRate,
            LinkMultiCurrencyNo,
            DeliveryDate,
            RegionNo
        )
		SELECT 
			req.CustomerRequirementId
			,'OFPH'
			,o.OfferId
			,o.FullPart
			,o.Part
			,ISNULL(o.ManufacturerNo,0)
			,o.DateCode
			,ISNULL(o.ProductNo, req.ProductNo)
			,ISNULL(o.PackageNo,0)
			,ISNULL(o.Quantity,0)
			,ISNULL(o.SellPrice,0)
			,dbo.ufn_get_HUB_DefaultCurrencyNo(isnull(o.CurrencyNo, company.POCurrencyNo), req.ClientNo, 114)
			,o.OriginalEntryDate
			,o.Salesman
			,o.OfferStatusNo
            ,o.OfferStatusChangeDate
            ,o.OfferStatusChangeLoginNo
			,o.SupplierNo
			,@UserID		--updated by
			,GETDATE()		--dlup
			,''	--type name
			,o.Notes
			,o.ROHS
			,o.SupplierNo		--POHubCompanyNo
			,((isnull(o.Price, 0) / oc.BuyExchangeRate))	--SupplierPrice
			,req.ClientCompanyNo
			,ISNULL(o.ShippingCost,0)
			,oc.ClientCurrencyNo
			,o.ManufacturerName
			,o.DateCode
			,o.SupplierPackageType
			,pd.ProductDescription
			,o.SupplierMOQ
			,ISNULL(TRY_CAST(o.SupplierTotalQSA AS INT),0)
			,o.SupplierLTB
			,o.Notes
			,o.SPQ
			,o.LeadTime
			,o.ROHSStatus
			,o.FactorySealed
			,o.MSLLevelNo
			,o.MSL
			,@UserId				--buyer
			,ISNULL(o.Price,0)		--ActualPrice
			,ISNULL(o.CurrencyNo, isnull(company.POCurrencyNo, 0))		--ActualCurrencyNo
			,dbo.ufn_get_exchange_rate(ISNULL(o.CurrencyNo, isnull(company.POCurrencyNo, 0)), GETDATE())		--ExchangeRate
			,oc.LinkMultiCurrencyNo
			,o.DeliveryDate
			,o.RegionNo
		FROM [BorisGlobalTraderImports].dbo.tbOffer o
		JOIN @InsertedOffers i on i.OfferId = o.OfferId
		LEFT JOIN @tbTargetRequirement req on req.CustomerRequirementId = o.ReferenceRequirementNo
		LEFT JOIN #tempOfferCurrency oc ON oc.OfferId = o.OfferId
		LEFT JOIN dbo.tbCompany company on o.SupplierNo = company.CompanyId
		LEFT JOIN dbo.tbProduct pd on pd.ProductId = req.ProductNo
		
		--update flags for requirement
		;with cteCustomerRef as(
			SELECT  CustomerRequirementNo
					, CustomerRefNo
					, ROW_NUMBER() OVER (PARTITION BY CustomerRequirementNo ORDER BY CustomerRefNo) AS row_num
			FROM BorisGlobalTraderImports.dbo.tbBOMSourcing_ToBeImported
			WHERE CreatedBy = @UserID
		)
		UPDATE cr
		SET cr.REQStatus = 3
			, cr.CustomerRefNo = cte.CustomerRefNo
			, cr.HasHubSourcingResult = 1
		FROM tbCustomerRequirement cr
		JOIN cteCustomerRef cte on cte.CustomerRequirementNo = cr.CustomerRequirementId
		WHERE cte.row_num = 1;

		--------------------------------------------------------------------
		SELECT @ImportCount = COUNT(1) FROM @InsertedOffers;
		DECLARE @CsvLogMessage NVARCHAR(1000);
		/********* Save imported file to HUBRFQ Uploaded Documents *********/
		INSERT INTO [tbBOMCSV] 
		(
			[BOMNo]
			,[Caption]
			,[FileName]
			,[UpdatedBy]
			,[DLUP]
			,[ImportType]
		)VALUES(@BOMNo, @OriginalFilename, @GeneratedFilename, @UserId, GETDATE(), 'SRCIMPORT')
		
		SET @CsvLogMessage = CONCAT('Import sourcing results successed: ' , CAST(@ImportCount AS NVARCHAR(10)), ' row(s).')
		INSERT INTO dbo.[tbBomCsvLog]
		(
			[BOMNo]
			,[FileName]
			,[Status]
			,[Message]
			,[DLUP]
		)VALUES(@BOMNo, @OriginalFilename, 1, @CsvLogMessage, GETDATE())
		--------------------------------------------------------------------
		/*============== Clear all temp data ================*/
		DELETE BorisGlobalTraderImports.dbo.tbBOMSourcing_tempData WHERE CreatedBy = @UserID;
		DELETE BorisGlobalTraderImports.dbo.tbBOMSourcing_ToBeImported WHERE CreatedBy = @UserID;

		DROP TABLE #tempOfferCurrency;
		SET @ImportMessage = 'Import success';
	END
	COMMIT TRANSACTION;
	END TRY
	BEGIN CATCH
		ROLLBACK TRANSACTION
		SET @ImportCount = -1;
		SET @ImportMessage = Error_message();
	END CATCH
END
GO


