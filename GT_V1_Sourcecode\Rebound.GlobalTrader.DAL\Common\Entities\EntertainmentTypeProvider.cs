﻿using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;
using System.Data.Common;

namespace Rebound.GlobalTrader.DAL {
	
	public abstract class EntertainmentTypeProvider : DataAccess {
		static private EntertainmentTypeProvider _instance = null;
		/// <summary>
		/// Returns an instance of the provider type specified in the config file
		/// </summary>       
		static public EntertainmentTypeProvider Instance {
			get {
				if (_instance == null) _instance = (EntertainmentTypeProvider)Activator.CreateInstance(Type.GetType(Globals.Settings.EntertainmentType.ProviderType));
				return _instance;
			}
		}
		public EntertainmentTypeProvider() {
			this.ConnectionString = Globals.Settings.EntertainmentType.ConnectionString;
		}

		#region Method Registrations
		
		/// <summary>
		/// Delete
		/// Calls [usp_delete_EntertainmentType]
		/// </summary>
		public abstract bool Delete(System.Int32? entertainmentTypeId);
		/// <summary>
		/// DropDown
		/// Calls [usp_dropdown_EntertainmentType]
		/// </summary>
		public abstract List<EntertainmentTypeDetails> DropDown();
		/// <summary>
		/// Insert
		/// Calls [usp_insert_EntertainmentType]
		/// </summary>
		public abstract Int32 Insert(System.String name);


		public abstract Int32 Insert2(System.String name);
		/// <summary>
		/// Get
		/// Calls [usp_select_EntertainmentType]
		/// </summary>
		public abstract EntertainmentTypeDetails Get(System.Int32? entertainmentTypeId);
		/// <summary>
		/// GetList
		/// Calls [usp_selectAll_EntertainmentType]
		/// </summary>
		public abstract List<EntertainmentTypeDetails> GetList();
		/// <summary>
		/// Update
		/// Calls [usp_update_EntertainmentType]
		/// </summary>
		public abstract bool Update(System.String name, System.Int32? entertainmentTypeId, System.Boolean? inactive, System.Int32? updatedBy);

		#endregion
				
		/// <summary>
		/// Returns a new EntertainmentTypeDetails instance filled with the DataReader's current record data
		/// </summary>        
		protected virtual EntertainmentTypeDetails GetEntertainmentTypeFromReader(DbDataReader reader) {
			EntertainmentTypeDetails entertainmentType = new EntertainmentTypeDetails();
			if (reader.HasRows) {
				entertainmentType.EntertainmentTypeId = GetReaderValue_Int32(reader, "EntertainmentTypeId", 0); //From: [Table]
				entertainmentType.Name = GetReaderValue_String(reader, "Name", ""); //From: [Table]
				entertainmentType.Inactive = GetReaderValue_Boolean(reader, "Inactive", false); //From: [Table]
				entertainmentType.UpdatedBy = GetReaderValue_NullableInt32(reader, "UpdatedBy", null); //From: [Table]
				entertainmentType.DLUP = GetReaderValue_NullableDateTime(reader, "DLUP", null); //From: [Table]
			}
			return entertainmentType;
		}
	
		/// <summary>
		/// Returns a collection of EntertainmentTypeDetails objects with the data read from the input DataReader
		/// </summary>                
		protected virtual List<EntertainmentTypeDetails> GetEntertainmentTypeCollectionFromReader(DbDataReader reader) {
			List<EntertainmentTypeDetails> entertainmentTypes = new List<EntertainmentTypeDetails>();
			while (reader.Read()) entertainmentTypes.Add(GetEntertainmentTypeFromReader(reader));
			return entertainmentTypes;
		}
		
		
	}
}