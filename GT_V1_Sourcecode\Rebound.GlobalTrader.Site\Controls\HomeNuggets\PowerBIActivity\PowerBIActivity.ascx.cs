using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site.Controls;

namespace Rebound.GlobalTrader.Site.Controls.HomeNuggets {
	public partial class PowerBIActivity : Base {

		#region Controls
		SimpleDataTable _tblPowerBIActivity;
		#endregion

		protected override void OnInit(EventArgs e) {
			this.HomePageNuggetType = "PowerBIActivity";
			base.OnInit(e);
            _tblPowerBIActivity = (SimpleDataTable)FindContentControl("tblPowerBIActivity");
			AddScriptReference("Controls.HomeNuggets.PowerBIActivity.PowerBIActivity.js");
		}

		protected override void OnLoad(EventArgs e) {
            //table headings
            _tblPowerBIActivity.Columns.Add(new SimpleDataColumn("Login"));
            _tblPowerBIActivity.Columns.Add(new SimpleDataColumn("LastVisited"));
            _tblPowerBIActivity.Columns.Add(new SimpleDataColumn("ReportName"));

			//setup javascript
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.HomeNuggets.PowerBIActivity", this.ctlDesignBase.ClientID);
			_scScriptControlDescriptor.AddComponentProperty("tblPowerBIActivity", _tblPowerBIActivity.ClientID);
			base.OnLoad(e);
		}


	}
}