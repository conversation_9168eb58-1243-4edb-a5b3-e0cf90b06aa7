<%@ Control Language="C#" CodeBehind="CRMAReceivingLines_Receive.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Forms.CRMAReceivingLines_Receive" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Form:DesignBase ID="ctlDB" runat="server" ShowRequiredFieldsExplanation="true" ShowQuickHelp="false">

    <Links>
        <ReboundUI:IconButton ID="ibtnSave" runat="server" IconGroup="Nugget" IconTitleResource="Save" IconCSSType="Save" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
        <ReboundUI:IconButton ID="ibtnCancel" runat="server" IconGroup="Nugget" IconTitleResource="Cancel" IconCSSType="Cancel" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
        <ReboundUI:IconButton ID="ibtnContinue" runat="server" IconGroup="Nugget" IconTitleResource="Continue" IconCSSType="Continue" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
        <ReboundUI:IconButton ID="ibtnSend" runat="server" IconGroup="Nugget" IconTitleResource="Send" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
    </Links>

    <Explanation><%=Functions.GetGlobalResource("FormExplanations", "CRMAReceivingLines_Receive")%></Explanation>
    <Explanation>
        <ReboundUI:MultiStep ID="ctlMultiStep" runat="server">

            <Items>
                <ReboundUI:MultiStepItem ID="ctlItem1" runat="server" ResourceTitle="CRMAReceivingLines_Receive_Source" IsSelected="true" />
                <ReboundUI:MultiStepItem ID="ctlItem2" runat="server" ResourceTitle="CRMAReceivingLines_Receive_Header" />
                <ReboundUI:MultiStepItem ID="ctlItem3" runat="server" ResourceTitle="CRMAReceivingLines_Receive_Detail" />
                <ReboundUI:MultiStepItem ID="ctlItem4" runat="server" ResourceTitle="CRMAReceivingLines_Receive_Notify" />
            </Items>

        </ReboundUI:MultiStep>
    </Explanation>

    <Content>

        <!-- Step 1 ------------------------------------------------------------------->
        <ReboundUI_Table:Form ID="frmStep1" runat="server">

            <ReboundUI_Form:FormField ID="ctlSelectNewOrExisting" runat="server" FieldID="radNewOrExisting" ResourceTitle="SelectSource">
                <Field>
                    <asp:RadioButtonList ID="radNewOrExisting" runat="server" RepeatDirection="Vertical" RepeatLayout="Flow" />
                </Field>
            </ReboundUI_Form:FormField>

        </ReboundUI_Table:Form>

        <!-- Step 2 ------------------------------------------------------------------->
        <ReboundUI_Table:Form ID="frmStep2" runat="server">

            <asp:TableRow ID="trNewGI" runat="server">
                <asp:TableCell ID="tdNewGI" runat="server">

                    <ReboundUI_Table:Form ID="frmNewGI" runat="server">

                        <ReboundUI_Form:FormField ID="ctlCustomerRMA" runat="server" FieldID="lblCustomerRMA" ResourceTitle="CustomerRMANo">
                            <Field>
                                <asp:Label ID="lblCustomerRMA" runat="server" />
                            </Field>
                        </ReboundUI_Form:FormField>

                        <ReboundUI_Form:FormField ID="ctlCustomer" runat="server" FieldID="lblCustomer" ResourceTitle="Customer">
                            <Field>
                                <asp:Label ID="lblCustomer" runat="server" />
                            </Field>
                        </ReboundUI_Form:FormField>

                        <ReboundUI_Form:FormField ID="ctlWarehouse" runat="server" FieldID="ddlWarehouse" ResourceTitle="Warehouse" IsRequiredField="true">
                            <Field>
                                <ReboundDropDown:Warehouse ID="ddlWarehouse" runat="server" IncludeVirtual="true" />
                            </Field>
                        </ReboundUI_Form:FormField>

                        <ReboundUI_Form:FormField ID="ctlShipVia" runat="server" FieldID="ddlShipVia" ResourceTitle="ShipViaNo" IsRequiredField="true">
                            <Field>
                                <ReboundDropDown:ShipMethod ID="ddlShipVia" runat="server" />
                            </Field>
                        </ReboundUI_Form:FormField>

                        <ReboundUI_Form:FormField ID="ctlCurrency" runat="server" FieldID="ddlCurrency" ResourceTitle="Currency" IsRequiredField="true">
                            <Field>
                                <ReboundDropDown:Currency ID="ddlCurrency" runat="server" />
                            </Field>
                        </ReboundUI_Form:FormField>

                        <ReboundUI_Form:FormField ID="ctlLblCurrency" runat="server" FieldID="LblCurrency" ResourceTitle="Currency" DisplayRequiredFieldMarkerOnly="true">
                            <Field>
                                <asp:Label ID="LblCurrency" runat="server" />
                            </Field>
                        </ReboundUI_Form:FormField>

                        <ReboundUI_Form:FormField ID="ctlAirWayBill" runat="server" FieldID="txtAirWayBill" ResourceTitle="AirWayBill">
                            <Field>
                                <ReboundUI:ReboundTextBox ID="txtAirWayBill" runat="server" Width="200" />
                            </Field>
                        </ReboundUI_Form:FormField>

                        <ReboundUI_Form:FormField ID="ctlReference" runat="server" FieldID="txtReference" ResourceTitle="Reference" IsRequiredField="true">
                            <Field>
                                <ReboundUI:ReboundTextBox ID="txtReference" runat="server" Width="200" UppercaseOnly="true" />
                            </Field>
                        </ReboundUI_Form:FormField>

                        <ReboundUI_Form:FormField ID="ctlReceivingNotes" runat="server" FieldID="txtReceivingNotes" ResourceTitle="ReceivingNotes">
                            <Field>
                                <ReboundUI:ReboundTextBox ID="txtReceivingNotes" runat="server" Width="400" TextMode="MultiLine" Rows="2" />
                            </Field>
                        </ReboundUI_Form:FormField>

                        <ReboundUI_Form:FormField ID="ctlReceivedBy" runat="server" FieldID="ddlReceivedBy" ResourceTitle="ReceivedBy">
                            <Field>
                                <ReboundDropDown:Employee ID="ddlReceivedBy" runat="server" />
                            </Field>
                        </ReboundUI_Form:FormField>

                        <ReboundUI_Form:FormField ID="ctlReceivedByLbl" runat="server" FieldID="ReceivedByLbl" ResourceTitle="ReceivedBy">
                            <Field>
                                <asp:Label ID="ReceivedByLbl" runat="server" />
                            </Field>
                        </ReboundUI_Form:FormField>

                        <ReboundUI_Form:FormField ID="ctlDateReceived" runat="server" FieldID="txtDateReceived" ResourceTitle="ReceivedDate" IsRequiredField="true">
                            <Field>
                                <ReboundUI:ReboundTextBox ID="txtDateReceived" runat="server" Width="140" /><ReboundUI:Calendar ID="calDateReceived" runat="server" RelatedTextBoxID="txtDateReceived" />
                            </Field>
                        </ReboundUI_Form:FormField>

                    </ReboundUI_Table:Form>

                </asp:TableCell>
            </asp:TableRow>

            <asp:TableRow ID="trGoodsIn" runat="server">
                <asp:TableCell ID="tdGoodsIn" runat="server">
                    <ReboundItemSearch:GoodsIn ID="ctlGoodsIn" runat="server" />
                </asp:TableCell>
            </asp:TableRow>

        </ReboundUI_Table:Form>

        <!-- Step 3 ------------------------------------------------------------------->
        <ReboundUI_Table:Form ID="frmStep3" runat="server">

            <ReboundUI_Form:FormField ID="ctlCustomerDetail" runat="server" FieldID="lblCustomerDetail" ResourceTitle="Customer">
                <Field>
                    <asp:Label ID="lblCustomerDetail" runat="server" />
                </Field>
            </ReboundUI_Form:FormField>

            <ReboundUI_Form:FormField ID="ctlGoodsInNumber" runat="server" FieldID="lblGoodsInNumber" ResourceTitle="GoodsInNo">
                <Field>
                    <asp:Label ID="lblGoodsInNumber" runat="server" />
                </Field>
            </ReboundUI_Form:FormField>

            <ReboundUI_Form:FormField ID="ctlAirWayBillDetail" runat="server" FieldID="lblAirWayBill" ResourceTitle="AirWayBill">
                <Field>
                    <asp:Label ID="lblAirWayBill" runat="server" />
                </Field>
            </ReboundUI_Form:FormField>

            <ReboundUI_Form:FormField ID="ctlReferenceDetail" runat="server" FieldID="lblReference" ResourceTitle="Reference">
                <Field>
                    <asp:Label ID="lblReference" runat="server" />
                </Field>
            </ReboundUI_Form:FormField>

            <ReboundUI_Form:FormField ID="ctlReceivingNotesDetail" runat="server" FieldID="lblReceivingNotes" ResourceTitle="ReceivingNotes">
                <Field>
                    <asp:Label ID="lblReceivingNotes" runat="server" />
                </Field>
            </ReboundUI_Form:FormField>

            <ReboundUI_Form:FormField ID="ctlPartNo" runat="server" FieldID="lblPartNo" ResourceTitle="PartNo">
                <Field>
                    <asp:Label ID="lblPartNo" runat="server" />
                </Field>
            </ReboundUI_Form:FormField>

            <ReboundUI_Form:FormField ID="ctlROHSStatus" runat="server" FieldID="lblROHSStatus" ResourceTitle="ROHS">
                <Field>
                    <asp:Label ID="lblROHSStatus" runat="server" />
                </Field>
            </ReboundUI_Form:FormField>

            <ReboundUI_Form:FormField ID="ctlDateCode" runat="server" FieldID="lblDateCode" ResourceTitle="DateCode">
                <Field>
                    <asp:Label ID="lblDateCode" runat="server" />
                </Field>
            </ReboundUI_Form:FormField>

            <ReboundUI_Form:FormField ID="ctlSupplierPart" runat="server" FieldID="lblSupplierPart" ResourceTitle="SupplierPartNo">
                <Field>
                    <asp:Label ID="lblSupplierPart" runat="server" />
                </Field>
            </ReboundUI_Form:FormField>

            <ReboundUI_Form:FormField ID="ctlProduct" runat="server" FieldID="lblProduct" ResourceTitle="Product">
                <Field>
                    <asp:Label ID="lblProduct" runat="server" />
                </Field>
            </ReboundUI_Form:FormField>

            <ReboundUI_Form:FormField ID="ctlPackage" runat="server" FieldID="lblPackage" ResourceTitle="Package">
                <Field>
                    <asp:Label ID="lblPackage" runat="server" />
                </Field>
            </ReboundUI_Form:FormField>

            <ReboundUI_Form:FormField ID="ctlManufacturer" runat="server" FieldID="lblManufacturer" ResourceTitle="Manufacturer">
                <Field>
                    <asp:Label ID="lblManufacturer" runat="server" />
                </Field>
            </ReboundUI_Form:FormField>

            <ReboundUI_Form:FormField ID="ctlPrice" runat="server" FieldID="lblPrice" ResourceTitle="Price">
                <Field>
                    <asp:Label ID="lblPrice" runat="server" />
                </Field>
            </ReboundUI_Form:FormField>

            <ReboundUI_Form:FormField ID="ctlAuthorised" runat="server" FieldID="lblAuthorised" ResourceTitle="QuantityAuthorised">
                <Field>
                    <asp:Label ID="lblAuthorised" runat="server" />
                </Field>
            </ReboundUI_Form:FormField>

            <ReboundUI_Form:FormField ID="ctlQuantity" runat="server" FieldID="txtQuantity" ResourceTitle="Quantity" IsRequiredField="true">
                <Field>
                    <ReboundUI:ReboundTextBox ID="txtQuantity" runat="server" Width="75" TextBoxMode="Numeric" />
                    <a href="javascript:void(0);" onclick="showSerialForm();" style="color: yellow; font-size: 13px">Select Serial No. to ship</a>
                    <%--<asp:HyperLink na ID="btnShow" runat="server" Text="Select Serial No. to ship" style="color:yellow;font-size: 13px"></asp:HyperLink>--%>
                </Field>
            </ReboundUI_Form:FormField>

            <ReboundUI_Form:FormField ID="ctlLocation" runat="server" FieldID="txtLocation" ResourceTitle="Location" IsRequiredField="true">
                <Field>
                    <ReboundUI:ReboundTextBox ID="txtLocation" runat="server" Width="75" MaxLength="10" UppercaseOnly="true" />
                </Field>
            </ReboundUI_Form:FormField>

            <ReboundUI_Form:FormField ID="ctlShipInCost" runat="server" FieldID="txtShipInCost" ResourceTitle="ShipInCost" IsRequiredField="true">
                <Field>
                    <ReboundUI:ReboundTextBox ID="txtShipInCost" runat="server" TextBoxMode="currency" FormatDecimalPlaces="true" Width="100" />
                    <%=Rebound.GlobalTrader.Site.SessionManager.ClientBaseCurrencyCode%>
                </Field>
            </ReboundUI_Form:FormField>

            <ReboundUI_Form:FormField ID="ctlQualityControlNotes" runat="server" FieldID="txtQualityControlNotes" ResourceTitle="QualityControlNotes">
                <Field>
                    <ReboundUI:ReboundTextBox ID="txtQualityControlNotes" runat="server" Width="400" TextMode="MultiLine" Rows="2" />
                </Field>
            </ReboundUI_Form:FormField>

            <%--<ReboundUI_Form:FormField id="ctlQuarantine" runat="server" FieldID="chkQuarantine" ResourceTitle="QuarantineThisItem">
				<Field><ReboundUI:ImageCheckBox ID="chkQuarantine" runat="server" Enabled="true" /></Field>
			</ReboundUI_Form:FormField>--%>

            <ReboundUI_Form:FormField ID="ctlCountingMethod" runat="server" FieldID="ddlCountingMethod" ResourceTitle="CountingMethod">
                <Field>
                    <ReboundDropDown:CountingMethod ID="ddlCountingMethod" runat="server" />
                </Field>
            </ReboundUI_Form:FormField>

            <ReboundUI_Form:FormField ID="ctlSerialNosRecorded" runat="server" FieldID="chkSerialNosRecorded" ResourceTitle="SerialNosRecorded">
                <Field>
                    <ReboundUI:ImageCheckBox ID="chkSerialNosRecorded" runat="server" Enabled="true" />
                </Field>
            </ReboundUI_Form:FormField>

            <ReboundUI_Form:FormField ID="ctlPartMarkings" runat="server" FieldID="txtPartMarkings" ResourceTitle="PartMarkings">
                <Field>
                    <ReboundUI:ReboundTextBox ID="txtPartMarkings" runat="server" Width="200" />
                </Field>
            </ReboundUI_Form:FormField>
            <%-- Record Serial No Start --%>
            <asp:TableRow>
                <asp:TableCell ID="TableCell1" ColumnSpan="2" Style="border-bottom: 1px dotted #CCCCCC; margin-top: 10px;">
                </asp:TableCell>
            </asp:TableRow>
            <ReboundUI_Form:FormField ID="ctlPart" runat="server" FieldID="lblPart" ResourceTitle="PartNo">
                <Field>
                    <asp:Label ID="lblPart" runat="server" />
                </Field>
            </ReboundUI_Form:FormField>
            <ReboundUI_Form:FormField ID="ctlQtyToShpped" runat="server" FieldID="lblQtyToshipped" ResourceTitle="Quantity">
                <Field>
                    <asp:Label ID="lblQtyToshipped" runat="server" />
                </Field>
            </ReboundUI_Form:FormField>

            <ReboundUI_Form:FormField ID="ctlComplete" runat="server" FieldID="lblComplete">
                <Field>
                    <asp:Label ID="lblComplete" Text="All the serial number are recorded for this group" Style="color: blue; font-size: 13px; background-color: #ffff99; font-weight: 900; padding-left: 2px; padding-right: 2px" runat="server" CssClass="Message" />
                </Field>
            </ReboundUI_Form:FormField>

            <asp:TableRow ID="trSerialNo" runat="server">

                <asp:TableCell ID="tdSerialNo" ColumnSpan="2" runat="server">



                    <asp:TableRow>
                        <asp:TableCell ID="TableCell2" ColumnSpan="2" BorderStyle="Dotted" BorderColor="#cccccc" BorderWidth="1px">
                            <ReboundUI:IconButton ID="btnAll" runat="server" IconGroup="Nugget" IconButtonMode="HyperLink" IconCSSType="Search" ForeColor="#ffff99" BackColor="White" IconTitleResource="All" Style="position: absolute; margin-left: 245px; margin-top: 8px;" CssClass="btnAddReset" />
                            <ReboundItemSearch:GISerialNumber ID="ctlGiSerialNumber" runat="server" />
                            <ReboundUI:MultiSelectionCount ID="ctlMultiSelectionCount" runat="server" Style="margin-top: 5px;" />

                        </asp:TableCell>
                    </asp:TableRow>



                    <ReboundUI_Form:FormField ID="ctlAddUpdate" runat="server" FieldID="btnAdd">
                        <Field>
                            <ReboundUI:IconButton ID="btnAdd" runat="server" IconGroup="Nugget" IconButtonMode="HyperLink" IconCSSType="Add" ForeColor="#ffff99" Style="margin-top: -22px; position: absolute; margin-left: -106px;" BackColor="White" IconTitleResource="Add" CssClass="btnAddReset" />
                            <ReboundUI:IconButton ID="btnRefresh" Visible="false" runat="server" IconButtonMode="HyperLink" IconCSSType="AddUpdate" ForeColor="#ffff99" BackColor="White" IconTitleResource="Reset" CssClass="btnAddReset" />

                        </Field>

                    </ReboundUI_Form:FormField>

                    <asp:TableRow ID="tblFinalGrid" BorderStyle="Dotted" BorderColor="#cccccc" BorderWidth="1px">
                        <asp:TableCell ID="TableCell4" Style="border-bottom: 1px dotted #CCCCCC; margin-top: 10px;" ColumnSpan="2" runat="server">
                            <asp:Label ID="Label1" runat="server" Font-Bold="true" color="White" Text="Selected Serial No." text-alignment="left" />
                        </asp:TableCell>
                    </asp:TableRow>

                    <ReboundUI_Form:FormField ID="ctlCountSerial" runat="server" FieldID="lblSerialCount" HorizontalAlign="Left">
                        <Field>
                            <asp:Label ID="lblSerialCount" runat="server" Font-Bold="true" Visible="false" color="White" />
                        </Field>
                    </ReboundUI_Form:FormField>

                    <asp:TableRow>
                        <asp:TableCell ID="TableCell3" ColumnSpan="2" runat="server">
                            <asp:Label ID="lblSerialCount1" runat="server" Text="" Font-Bold="true" color="White" /><br />
                            <ReboundUI:FlexiDataTable ID="tblSerialNoFinal" runat="server" PanelHeight="150" Width="500" />
                        </asp:TableCell>
                    </asp:TableRow>

                </asp:TableCell>
            </asp:TableRow>
            <%-- Record Serial No End --%>
            <asp:TableRow>
                <asp:TableCell>&nbsp
                <script type="text/javascript">
                    //var formId = $find("<%=this.ClientID%>");
                    function showSerialForm()
                    {
                        $find("<%=this.ClientID%>").OpenSerialForm();
                    }
                    $(document).ready(function () {
                        $("[id*=txtQuantity]").change(function () {
                            $find("<%=this.ClientID%>").refreshSerialForm();
                        });
                    });
                </script>
                </asp:TableCell>
            </asp:TableRow>
        </ReboundUI_Table:Form>

        <!-- Step 4 ------------------------------------------------------------------->
        <ReboundUI_Table:Form ID="frmStep4" runat="server">

            <ReboundUI_Form:FormField ID="ctlSendMail" runat="server" FieldID="chkSendMail" ResourceTitle="ShouldMailBeSent">
                <Field>
                    <ReboundUI:ImageCheckBox ID="chkSendMail" runat="server" Enabled="true" Checked="true" />
                </Field>
            </ReboundUI_Form:FormField>

            <ReboundFormFieldCollection:SendMailMessage ID="ctlSendMailMessage" runat="server" />
        </ReboundUI_Table:Form>

    </Content>

</ReboundUI_Form:DesignBase>
