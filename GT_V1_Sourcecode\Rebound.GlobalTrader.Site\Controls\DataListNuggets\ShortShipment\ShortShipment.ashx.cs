﻿using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;
using Rebound.GlobalTrader.Site.Enumerations;
using Rebound.GlobalTrader.Site.Code.Common;

namespace Rebound.GlobalTrader.Site.Controls.DataListNuggets.Data
{

    public class ShortShipment : Base
    {

        public override void ProcessRequest(HttpContext context)
        {
            base.ProcessRequest(context);
            if (Action == "ExportToCSV") ExportToCSV();
        }
        protected override void GetData()
        {
            List<Rebound.GlobalTrader.BLL.ShortShipment> lst = Rebound.GlobalTrader.BLL.ShortShipment.DataListNugget(
                 //SessionManager.ClientID,
                 GetFormValue_NullableInt("ClientByMaster"),
                 GetFormValue_NullableInt("SortIndex"),
                 GetFormValue_NullableInt("SortDir", SortColumnDirection.ASC),
                 GetFormValue_NullableInt("PageIndex", 0),
                 GetFormValue_NullableInt("PageSize", 10),
                 GetFormValue_StringForNameSearchDecode("Supplier"),
                 GetFormValue_NullableInt("PurchaseOrderNoLo"),
                 GetFormValue_NullableInt("PurchaseOrderNoHi"),
                 GetFormValue_NullableDateTime("DateReceived"),
                 GetFormValue_NullableInt("QuantityOrderedLo"),
                 GetFormValue_NullableInt("QuantityOrderedHi"),
                 GetFormValue_NullableInt("QuantityReceivedLo"),
                 GetFormValue_NullableInt("QuantityReceivedHi"),
                 GetFormValue_NullableInt("ShortageQuantityLo"),
                 GetFormValue_NullableInt("ShortageQuantityHi"),
                 GetFormValue_NullableDouble("ShortageValueLo"),
                 GetFormValue_NullableDouble("ShortageValueHi"),
                 GetFormValue_NullableBoolean("IsShortageRefundIssue"),
                 GetFormValue_NullableInt("ShortShipmentStatus"),
                 GetFormValue_NullableInt("GINumberLo"),
                 GetFormValue_NullableInt("GINumberHi"),
                 GetFormValue_NullableInt("Buyer"),
                 GetFormValue_Boolean("RecentOnly")
            );
            JsonObject jsn = new JsonObject();
            jsn.AddVariable("TotalRecords", (lst.Count > 0) ? lst[0].RowCnt : 0);
            JsonObject jsnRowsArray = new JsonObject(true);
            for (int i = 0; i < lst.Count; i++)
            {
                if (i < lst.Count)
                {
                    JsonObject jsnRow = new JsonObject();
                    jsnRow.AddVariable("ShortShipmentId", lst[i].ShortShipmentId);
                    jsnRow.AddVariable("Supplier", lst[i].Supplier);
                    jsnRow.AddVariable("PurchaseOrderNo", SessionManager.IsPOHub.Value ? lst[i].PurchaseOrderNo : lst[i].IPONo);
                    jsnRow.AddVariable("Salesman", lst[i].Salesman);
                    jsnRow.AddVariable("Reference", lst[i].Reference);
                    jsnRow.AddVariable("GoodsInNo", lst[i].GoodsInNo);
                    jsnRow.AddVariable("DateReceived", Functions.FormatDate(lst[i].DateReceived));
                    jsnRow.AddVariable("Raisedby", lst[i].Raisedby);
                    jsnRow.AddVariable("Buyer", lst[i].Buyer);
                    jsnRow.AddVariable("PartNo", lst[i].PartNo);
                    jsnRow.AddVariable("ManufacturerName", lst[i].ManufacturerName);
                    jsnRow.AddVariable("QuantityOrdered", lst[i].QuantityOrdered);
                    jsnRow.AddVariable("QuantityAdvised", lst[i].QuantityAdvised);
                    jsnRow.AddVariable("QuantityReceived", lst[i].QuantityReceived);
                    jsnRow.AddVariable("ShortageQuantity", lst[i].ShortageQuantity);
                    jsnRow.AddVariable("ShortageValue", lst[i].ShortageValue);
                    if (lst[i].IsShortageRefundIssue == true)
                    { jsnRow.AddVariable("IsShortageRefundIssue", "Yes"); }
                    else if (lst[i].IsShortageRefundIssue == false)
                    { jsnRow.AddVariable("IsShortageRefundIssue", "No"); }
                    else
                    { jsnRow.AddVariable("IsShortageRefundIssue", "NA"); }
                    jsnRow.AddVariable("ShortageRefundIssue", "Yes");
                    jsnRow.AddVariable("Completedby", lst[i].Completedby);
                    jsnRow.AddVariable("CompletedDate", Functions.FormatDate(lst[i].CompletedDate));
                    jsnRow.AddVariable("Status", lst[i].Status);
                    jsnRow.AddVariable("GoodsInId", lst[i].GoodsInId);
                    jsnRow.AddVariable("DebitNoteNo", lst[i].DebitNoteNo);
                    jsnRow.AddVariable("DebitNumber", lst[i].DebitNumber);
                    jsnRow.AddVariable("IsCancel", lst[i].IsCancel == true ? "Yes" : "No");
                    jsnRow.AddVariable("IsClosed", lst[i].IsClosed == true ? "Yes" : "No");
                    jsnRowsArray.AddVariable(jsnRow);
                    jsnRow.Dispose();
                    jsnRow = null;
                }
            }
            jsn.AddVariable("Results", jsnRowsArray);
            OutputResult(jsn);
            jsnRowsArray.Dispose();
            jsnRowsArray = null;
            jsn.Dispose();
            jsn = null;
            lst = null;
            base.GetData();
        }

        protected override void AddFilterStates()
        {
            
            AddFilterState("GINumber");
            AddFilterState("Supplier");
            AddFilterState("DateReceived");
            AddFilterState("PurchaseOrderNo");
            AddFilterState("QuantityOrdered");
            AddFilterState("QuantityReceived");
            AddFilterState("ShortageQuantity");
            AddFilterState("ShortValue");
            AddFilterState("IsShortageRefundIssue");
            AddFilterState("Status");
            AddFilterState("Buyer");
            AddFilterState("ClientByMaster");
            base.AddFilterStates();
        }
        public void ExportToCSV()
        {
            JsonObject jsn = new JsonObject();
            try
            {
                string filePath = string.Empty;
                string strFilename = String.Format("report_u{0}r{1}.xlsx", LoginID, 0);
                int? ClientId = null;
                if (GetFormValue_Boolean("IsCanViewClient") == true)
                {
                     ClientId = GetFormValue_NullableInt("ClientByMaster");
                }
                else
                {
                    ClientId = SessionManager.ClientID;
                }
                DataTable dtResult = BLL.ShortShipment.DataListNugget_Export(
                 ClientId,
                 GetFormValue_NullableInt("SortIndex"),
                 GetFormValue_NullableInt("SortDir", SortColumnDirection.ASC),
                 GetFormValue_StringForNameSearchDecode("Supplier"),
                 GetFormValue_NullableInt("PurchaseOrderNoLo"),
                 GetFormValue_NullableInt("PurchaseOrderNoHi"),
                 GetFormValue_NullableDateTime("DateReceived"),
                 GetFormValue_NullableInt("QuantityOrderedLo"),
                 GetFormValue_NullableInt("QuantityOrderedHi"),
                 GetFormValue_NullableInt("QuantityReceivedLo"),
                 GetFormValue_NullableInt("QuantityReceivedHi"),
                 GetFormValue_NullableInt("ShortageQuantityLo"),
                 GetFormValue_NullableInt("ShortageQuantityHi"),
                 GetFormValue_NullableDouble("ShortageValueLo"),
                 GetFormValue_NullableDouble("ShortageValueHi"),
                 GetFormValue_NullableBoolean("IsShortageRefundIssue"),
                 GetFormValue_NullableInt("ShortShipmentStatus"),
                 GetFormValue_NullableInt("GINumberLo"),
                 GetFormValue_NullableInt("GINumberHi"),
                 SessionManager.IsPOHub.Value ? true : false,
                 GetFormValue_NullableInt("Buyer"),
                 GetFormValue_Boolean("IsRecentOnly")
                );
                filePath = (new EPPlusExportUtility()).ExportDataTableToCSVShipSO(dtResult, strFilename,"Short Shipment");
                //return saved filename to the page
                jsn.AddVariable("Filename", filePath);
                OutputResult(jsn);

            }
            catch (Exception e)
            {
                WriteError(e);
            }
            finally
            {
                jsn.Dispose();
                jsn = null;
            }
        }
    }
}
