-- Drop the procedure if it already exists
IF OBJECT_ID('dbo.usp_selectCusReqForMail', 'P') IS NOT NULL
BEGIN
    DROP PROCEDURE dbo.usp_selectCusReqForMail;
END

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE PROCEDURE [dbo].[usp_selectCusReqForMail]        
@BOMNo int ,                                      
@ClientID int                                                
AS            
BEGIN         
select         
cr.<PERSON>er<PERSON>e<PERSON>umber,        
cr.CustomerRequirementId,        
cr.Quantity,        
cr.Customer<PERSON>art,        
cr.<PERSON>,        
cr.DateCode,        
cr.ManufacturerCode,        
cr.PackageName,        
cr.ProductName,        
cr.CompanyName,        
cr.ClientName,        
cr.DatePromised,        
dbo.ufn_convert_currency_value(cr.<PERSON>, cr.CurrencyNo, cr.BOMCurrencyNo, cr.BOMDate) AS ConvertedTargetValue,        
cr.<PERSON><PERSON><PERSON>cy<PERSON>o,        
cu.CurrencyCode as BOMCurrencyCode ,        
cr.<PERSON><PERSON>,        
case when cr.Obsolete=1 then 'Obsolete : Yes <br/>'  +ISNULL(cr.Instructions,'') else  'Obsolete : No <br/>' + ISNULL(cr.Instructions,'') end as Instructions,        
cr.MSL,        
cr.FactorySealed,        
TRD.ServiceName AS ReqTypeText,    
TRDT.ServiceName AS ReqForTraceability,
cr.AlternativesAccepted,
cr.RepeatBusiness  ,     
cr.AlternateStatus  ,
cr.Alternate      
 FROM  dbo.vwCustomerRequirement cr         
 LEFT JOIN DBO.[tbRequirementDropDownData] TRD ON cr.ReqType=TRD.Id       
 LEFT JOIN DBO.[tbRequirementDropDownData] TRDT ON cr.ReqForTraceability=TRDT.Id      
 LEFT JOIN dbo.tbCurrency cu  ON cr.CurrencyNo = cu.CurrencyId        
     
 where            
BOMNo = @BOMNo 
--and cr.ClientNo= @ClientID   
-- AND (@ClientID IS NULL        
--                                 OR (NOT @ClientID IS NULL        
--                                     AND ClientNo = @ClientID))   
end  






GO