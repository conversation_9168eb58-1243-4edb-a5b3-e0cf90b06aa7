<%--
Marker     Changed by      Date         Remarks
[001]      Vinay           09/07/2012   This need for Rebound- Invoice bulk Emailer
--%>
<%@ Control Language="C#" CodeBehind="ContactMainInfo_Edit.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Forms.ContactMainInfo_Edit" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Form:DesignBase ID="ctlDB" runat="server" ShowRequiredFieldsExplanation="true" ShowQuickHelp="false">
    <Links>
        <ReboundUI:IconButton ID="ibtnSave" runat="server" IconGroup="Nugget" IconTitleResource="Save" IconCSSType="Save" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
        <ReboundUI:IconButton ID="ibtnCancel" runat="server" IconGroup="Nugget" IconTitleResource="Cancel" IconCSSType="Cancel" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
    </Links>
    <Explanation><%=Functions.GetGlobalResource("FormExplanations", "ContactMainInfo_Edit")%></Explanation>
    <Content>
        <ReboundUI_Table:Form ID="frm" runat="server">
            <ReboundUI_Form:FormField ID="ctlFirstName" runat="server" FieldID="txtFirstName" ResourceTitle="FirstName" IsRequiredField="true">
                <Field>
                    <ReboundUI:ReboundTextBox ID="txtFirstName" runat="server" Width="300" /></Field>
            </ReboundUI_Form:FormField>

            <ReboundUI_Form:FormField ID="ctlSurname" runat="server" FieldID="txtSurname" ResourceTitle="Surname" IsRequiredField="true">
                <Field>
                    <ReboundUI:ReboundTextBox ID="txtSurname" runat="server" Width="300" /></Field>
            </ReboundUI_Form:FormField>

            <ReboundUI_Form:FormField ID="ctlJobTitle" runat="server" FieldID="txtJobTitle" ResourceTitle="JobTitle">
                <Field>
                    <ReboundUI:ReboundTextBox ID="txtJobTitle" runat="server" Width="300" /></Field>
            </ReboundUI_Form:FormField>

            <ReboundUI_Form:FormField ID="ctlTel" runat="server" FieldID="txtTel" ResourceTitle="Tel">
                <Field>
                    <ReboundUI:ReboundTextBox ID="txtTel" runat="server" Width="300" /></Field>
            </ReboundUI_Form:FormField>

            <ReboundUI_Form:FormField ID="ctlFax" runat="server" FieldID="txtFax" ResourceTitle="Fax">
                <Field>
                    <ReboundUI:ReboundTextBox ID="txtFax" runat="server" Width="150" /></Field>
            </ReboundUI_Form:FormField>

            <ReboundUI_Form:FormField ID="ctlExtension" runat="server" FieldID="txtExtension" ResourceTitle="Extension">
                <Field>
                    <ReboundUI:ReboundTextBox ID="txtExtension" runat="server" Width="150" /></Field>
            </ReboundUI_Form:FormField>

            <ReboundUI_Form:FormField ID="ctlHomeTel" runat="server" FieldID="txtHomeTel" ResourceTitle="HomeTel">
                <Field>
                    <ReboundUI:ReboundTextBox ID="txtHomeTel" runat="server" Width="150" /></Field>
            </ReboundUI_Form:FormField>

            <ReboundUI_Form:FormField ID="ctlMobileTel" runat="server" FieldID="txtMobileTel" ResourceTitle="MobileTel">
                <Field>
                    <ReboundUI:ReboundTextBox ID="txtMobileTel" runat="server" Width="150" /></Field>
            </ReboundUI_Form:FormField>

            <ReboundUI_Form:FormField ID="ctlEmail" runat="server" FieldID="txtEmail" ResourceTitle="Email">
                <Field>
                    <ReboundUI:ReboundTextBox ID="txtEmail" runat="server" Width="450" /></Field>
            </ReboundUI_Form:FormField>

            <ReboundUI_Form:FormField ID="ctlTextOnlyEmail" runat="server" FieldID="chkTextOnlyEmail" ResourceTitle="IsEmailTextOnly">
                <Field>
                    <ReboundUI:ImageCheckBox ID="chkTextOnlyEmail" runat="server" Enabled="true" />
                </Field>
            </ReboundUI_Form:FormField>

            <ReboundUI_Form:FormField ID="ctlNickname" runat="server" FieldID="txtNickname" ResourceTitle="Nickname">
                <Field>
                    <ReboundUI:ReboundTextBox ID="txtNickname" runat="server" Width="300" /></Field>
            </ReboundUI_Form:FormField>

            <ReboundUI_Form:FormField ID="ctlCompanyAddress" runat="server" FieldID="ddlCompanyAddress" ResourceTitle="CompanyAddress">
                <Field>
                    <ReboundDropDown:AddressesForCompany ID="ddlCompanyAddress" runat="server" />
                </Field>
            </ReboundUI_Form:FormField>

            <%--<ReboundFormFieldCollection:Address id="ctlAddress" runat="server" TitleResource="PersonalAddress" />--%>
            <%--[001] code start--%>
            <ReboundUI_Form:FormField ID="ctlFinanceContacts" runat="server" FieldID="chkFinanceContacts" ResourceTitle="FinanceContact">
                <Field>
                    <ReboundUI:ImageCheckBox ID="chkFinanceContacts" runat="server" Enabled="true" />
                </Field>
            </ReboundUI_Form:FormField>
            <%--[001] code end--%>
            <ReboundUI_Form:FormField ID="ctlInactive" runat="server" FieldID="chkInactive" ResourceTitle="IsInactive">
                <Field>
                    <ReboundUI:ImageCheckBox ID="chkInactive" runat="server" Enabled="true" />
                </Field>
            </ReboundUI_Form:FormField>
            <ReboundUI_Form:FormField ID="ctlSendShipmentNotification" runat="server" FieldID="chkShipmentNotification" ResourceTitle="IsSendShipmentNotification">
                <Field>
                    <ReboundUI:ImageCheckBox ID="chkShipmentNotification" runat="server" Enabled="true" />
                </Field>
            </ReboundUI_Form:FormField>
        </ReboundUI_Table:Form>
    </Content>
</ReboundUI_Form:DesignBase>

