Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.ItemSearch");Rebound.GlobalTrader.Site.Controls.ItemSearch.CRMALines=function(n){Rebound.GlobalTrader.Site.Controls.ItemSearch.CRMALines.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.ItemSearch.CRMALines.prototype={initialize:function(){Rebound.GlobalTrader.Site.Controls.ItemSearch.CRMALines.callBaseMethod(this,"initialize");this.addSetupData(Function.createDelegate(this,this.doSetupData));this.addGetDataComplete(Function.createDelegate(this,this.doGetDataComplete))},dispose:function(){this.isDisposed||Rebound.GlobalTrader.Site.Controls.ItemSearch.CRMALines.callBaseMethod(this,"dispose")},doSetupData:function(){this._objData.set_PathToData("controls/ItemSearch/CRMALines");this._objData.set_DataObject("CRMALines");this._objData.set_DataAction("GetData");this._objData.addParameter("CRMANoLo",this.getFieldValue_Min("ctlCRMANo"));this._objData.addParameter("CRMANoHi",this.getFieldValue_Max("ctlCRMANo"));this._objData.addParameter("Part",this.getFieldValue("ctlPart"));this._objData.addParameter("Contact",this.getFieldValue("ctlContact"));this._objData.addParameter("CMName",this.getFieldValue("ctlCompany"));this._objData.addParameter("Salesman",this.getFieldValue("ctlSalesman"));this._objData.addParameter("Notes",this.getFieldValue("ctlCRMANotes"));this._objData.addParameter("InvoiceNoLo",this.getFieldValue_Min("ctlInvoiceNo"));this._objData.addParameter("InvoiceNoHi",this.getFieldValue_Max("ctlInvoiceNo"));this._objData.addParameter("CRMADateFrom",this.getFieldValue("ctlCRMADateFrom"));this._objData.addParameter("CRMADateTo",this.getFieldValue("ctlCRMADateTo"))},doGetDataComplete:function(){for(var n,i,t=0,r=this._objResult.Results.length;t<r;t++)n=this._objResult.Results[t],i=[n.No,$R_FN.setCleanTextValue(n.CMName),$R_FN.writePartNo(n.Part,n.ROHS),$R_FN.setCleanTextValue(n.Date),n.Quantity,$R_FN.setCleanTextValue(n.Salesman),n.InvoiceNo],this._tblResults.addRow(i,n.ID,!1),i=null,n=null}};Rebound.GlobalTrader.Site.Controls.ItemSearch.CRMALines.registerClass("Rebound.GlobalTrader.Site.Controls.ItemSearch.CRMALines",Rebound.GlobalTrader.Site.Controls.ItemSearch.Base,Sys.IDisposable);