///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.HomeNuggets");

Rebound.GlobalTrader.Site.Controls.HomeNuggets.AllPurchaseOrdersDueIn = function(element) { 
	Rebound.GlobalTrader.Site.Controls.HomeNuggets.AllPurchaseOrdersDueIn.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.HomeNuggets.AllPurchaseOrdersDueIn.prototype = {

	get_pnlDueIn: function() { return this._pnlDueIn; }, 	set_pnlDueIn: function(value) { if (this._pnlDueIn !== value)  this._pnlDueIn = value; }, 
	get_tblDueIn: function() { return this._tblDueIn; }, 	set_tblDueIn: function(value) { if (this._tblDueIn !== value)  this._tblDueIn = value; }, 
	get_pnlMore: function() { return this._pnlMore; }, 	set_pnlMore: function(value) { if (this._pnlMore !== value)  this._pnlMore = value; }, 

	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.HomeNuggets.AllPurchaseOrdersDueIn.callBaseMethod(this, "initialize");
		this.addRefreshEvent(Function.createDelegate(this, this.getData));
	},

	dispose: function() { 
		if (this.isDisposed) return;
		if (this._tblDueIn) this._tblDueIn.dispose();
		this._pnlDueIn = null;
		this._tblDueIn = null;
		this._pnlMore = null;
		Rebound.GlobalTrader.Site.Controls.HomeNuggets.AllPurchaseOrdersDueIn.callBaseMethod(this, "dispose");
	},
	
	setupLoadingState: function() {
		$R_FN.showElement(this._pnlMore, false);
		$R_FN.showElement(this._pnlDueIn, false);
		Rebound.GlobalTrader.Site.Controls.HomeNuggets.AllPurchaseOrdersDueIn.callBaseMethod(this, "setupLoadingState");
	},
	
	showNoData: function(bln) {
		this.showContent(true);
		$R_FN.showElement(this._pnlNoData, bln);
	},
	
	getData: function() {
		this.setupLoadingState();
		var obj = new Rebound.GlobalTrader.Site.Data();
		obj.set_PathToData("controls/HomeNuggets/AllPurchaseOrdersDueIn");
		obj.set_DataObject("AllPurchaseOrdersDueIn");
		obj.set_DataAction("GetData");
		obj.addParameter("rowcount", this._intRowCount);
		obj.addDataOK(Function.createDelegate(this, this.getDataComplete));
		obj.addError(Function.createDelegate(this, this.homeNuggetDataError));
		obj.addTimeout(Function.createDelegate(this, this.homeNuggetDataError));
		$R_DQ.addToQueue(obj);
		$R_DQ.processQueue();
		obj = null;
	},
	
	getDataComplete: function(args) {
		this.showNoData(args._result.Count == 0);
		$R_FN.showElement(this._pnlMore, true);
		var result = args._result;
		//due in
		this._tblDueIn.clearTable();
		for (var i = 0; i < result.DueInPO.length; i++) {
			var row = result.DueInPO[i];
			var aryData = [
				$RGT_nubButton_ReceivePurchaseOrder(row.ID, row.No),
				$RGT_nubButton_Company(row.CMNo, row.CM),
				row.Due
				];
			this._tblDueIn.addRow(aryData, null);
		}
		$R_FN.showElement(this._pnlDueIn, result.DueInPO.length > 0);
		this.hideLoading();
	}
};

Rebound.GlobalTrader.Site.Controls.HomeNuggets.AllPurchaseOrdersDueIn.registerClass("Rebound.GlobalTrader.Site.Controls.HomeNuggets.AllPurchaseOrdersDueIn", Rebound.GlobalTrader.Site.Controls.HomeNuggets.Base, Sys.IDisposable);
