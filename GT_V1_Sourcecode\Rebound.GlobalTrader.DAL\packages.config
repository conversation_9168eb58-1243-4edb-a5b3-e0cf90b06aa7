﻿<?xml version="1.0" encoding="utf-8"?>
<!--//Marker     Changed by      Date         Remarks
//[001]      Soorya          03/03/2023   RP-1048 Remove AI code-->
<packages>
	<!--[001]-->
	<!--<package id="Microsoft.ApplicationInsights" version="2.17.0" targetFramework="net46" />
  <package id="Microsoft.ApplicationInsights.Agent.Intercept" version="2.4.0" targetFramework="net46" />
  <package id="Microsoft.ApplicationInsights.DependencyCollector" version="2.17.0" targetFramework="net46" />
  <package id="Microsoft.ApplicationInsights.PerfCounterCollector" version="2.17.0" targetFramework="net46" />
  <package id="Microsoft.ApplicationInsights.WindowsServer" version="2.17.0" targetFramework="net46" />
  <package id="Microsoft.ApplicationInsights.WindowsServer.TelemetryChannel" version="2.17.0" targetFramework="net46" />-->
  <package id="Microsoft.Azure.KeyVault.Core" version="1.0.0" targetFramework="net46" />
  <package id="Microsoft.Office.Interop.Excel" version="15.0.4795.1000" targetFramework="net35" />
  <package id="Microsoft.WindowsAzure.ConfigurationManager" version="3.2.3" targetFramework="net46" />
  <package id="Newtonsoft.Json" version="10.0.2" targetFramework="net46" />
  <package id="System.Buffers" version="4.5.1" targetFramework="net46" />
  <package id="System.Diagnostics.DiagnosticSource" version="5.0.0" targetFramework="net46" />
  <package id="System.Memory" version="4.5.4" targetFramework="net46" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="5.0.0" targetFramework="net46" />
  <package id="WindowsAzure.Storage" version="9.3.3" targetFramework="net46" />
</packages>