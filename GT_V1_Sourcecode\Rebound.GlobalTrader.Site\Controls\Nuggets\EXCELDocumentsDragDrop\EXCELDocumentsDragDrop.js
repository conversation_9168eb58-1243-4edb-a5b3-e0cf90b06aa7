Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Nuggets");Rebound.GlobalTrader.Site.Controls.Nuggets.EXCELDocumentsDragDrop=function(n){Rebound.GlobalTrader.Site.Controls.Nuggets.EXCELDocumentsDragDrop.initializeBase(this,[n]);this._sectionID=0;this._intCountPDF==0};Rebound.GlobalTrader.Site.Controls.Nuggets.EXCELDocumentsDragDrop.prototype={get_sectionID:function(){return this._sectionID},set_sectionID:function(n){this._sectionID!==n&&(this._sectionID=n)},get_pnlEXCELDocuments:function(){return this._pnlEXCELDocuments},set_pnlEXCELDocuments:function(n){this._pnlEXCELDocuments!==n&&(this._pnlEXCELDocuments=n)},get_blnCanDelete:function(){return this._blnCanDelete},set_blnCanDelete:function(n){this._blnCanDelete!==n&&(this._blnCanDelete=n)},get_intMaxPDFDocuments:function(){return this._intMaxPDFDocuments},set_intMaxPDFDocuments:function(n){this._intMaxPDFDocuments!==n&&(this._intMaxPDFDocuments=n)},get_blnCanAdd:function(){return this._blnCanAdd},set_blnCanAdd:function(n){this._blnCanAdd!==n&&(this._blnCanAdd=n)},get_IsPDFAvailable:function(){return this._IsPDFAvailable},set_IsPDFAvailable:function(n){this._IsPDFAvailable!==n&&(this._IsPDFAvailable=n)},get_strSectionName:function(){return this._strSectionName},set_strSectionName:function(n){this._strSectionName!==n&&(this._strSectionName=n)},get_strExcel:function(){return this._strExcel},set_strExcel:function(n){this._strExcel!==n&&(this._strExcel=n)},get_PrintID:function(){return this._PrintID},set_PrintID:function(n){this._PrintID!==n&&(this._PrintID=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Nuggets.EXCELDocumentsDragDrop.callBaseMethod(this,"initialize");this.addRefreshEvent(Function.createDelegate(this,this.showPDFPanel));this._frmAdd=$find(this._aryFormIDs[0]);this._frmAdd._strSectionName=this._strSectionName;this._frmAdd._intSectionID=this._sectionID;this._frmAdd._strExcel=this._strExcel;this._frmAdd.addCancel(Function.createDelegate(this,this.hideAddForm));this._frmAdd.addSaveComplete(Function.createDelegate(this,this.saveAddComplete));this._frmAdd.addSaveError(Function.createDelegate(this,this.saveAddError));this._blnCanDelete&&(this._frmDelete=$find(this._aryFormIDs[1]),this._frmDelete._strSectionName=this._strSectionName,this._frmDelete.addNotConfirmed(Function.createDelegate(this,this.cancelDelete)),this._frmDelete.addSaveComplete(Function.createDelegate(this,this.saveDeleteComplete)));document.getElementById("UpdateBomCsv").addEventListener("click",Function.createDelegate(this,function(n){n.preventDefault();this.navigateToBOMImport()}));this.getData()},dispose:function(){this.isDisposed||(this._frmAdd&&this._frmAdd.dispose(),this._frmDelete&&this._frmDelete.dispose(),this._frmAdd=null,this._frmDelete=null,this._pnlEXCELDocuments=null,this._sectionID=null,this._blnCanAdd=null,this._IsPDFAvailable=null,this._intMaxPDFDocuments=null,this._blnCanDelete=null,this._intCountPDF=null,this._strSectionName=null,this._PrintID=null,Rebound.GlobalTrader.Site.Controls.Nuggets.EXCELDocumentsDragDrop.callBaseMethod(this,"dispose"))},getData:function(){this.getData_Start();this._intCountPDF==0;var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/EXCELDocumentsDragDrop");n.set_DataObject("EXCELDocumentsDragDrop");n.set_DataAction("GetData");n.addParameter("id",this._sectionID);n.addParameter("section",this._strSectionName);n.addDataOK(Function.createDelegate(this,this.getDataOK));n.addError(Function.createDelegate(this,this.getDataError));n.addTimeout(Function.createDelegate(this,this.getDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getMaxPDFDocument:function(){this.getData_Start();var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/EXCELDocumentsDragDrop");n.set_DataObject("EXCELDocumentsDragDrop");n.set_DataAction("MaxPDFDoc");n.addDataOK(Function.createDelegate(this,this.getMaxPDFDocumentOK));n.addError(Function.createDelegate(this,this.getDataError));n.addTimeout(Function.createDelegate(this,this.getDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getMaxPDFDocumentOK:function(n){var t=n._result;t.MaxPDFDocument>0&&(this._intMaxPDFDocuments=t.MaxPDFDocument)},getDataOK:function(n){var e=n._result,r=n._result,f,t,u,i;if($R_FN.setInnerHTML(this._pnlEXCELDocuments,""),f=r.IconPath,t="",r.Items){for(u=0;u<r.Items.length;u++)i=r.Items[u],t+='<div class="pdfDocument">',t+=String.format("<div class=\"pdfDocumentDelete\" onclick=\"$find('{0}').deletePDF({1},'{2}');\">&nbsp;<\/div>",this._element.id,i.ID,i.FileName),t+=String.format('<a href="{0}" ><img width="80px" id="{1}_img{2}" src="{3}" border="0" onclick="$find(\'{4}\').OpenPDF(\'{5}\',\'{6}\');" /><\/a>',"javascript:void(0);",this._element.id,u,f,this._element.id,i.Section,i.FileName),t+='<div class="pdfDocumentCaption">',i.Caption&&(t+=i.Caption+"<br />"),t+=i.Date,i.By&&(t+="<br />"+i.By),t+="<\/div>",t+="<\/div>",i=null;this._intCountPDF=r.Items.length}$R_FN.setInnerHTML(this._pnlEXCELDocuments,t);this.getDataOK_End();this._intCountPDF==0?this.showPDFNoData(this._intCountPDF==0):this.showNoData(this._intCountPDF==0);this.showPanel(this._intCountPDF>0)},getDataError:function(n){this.showError(!0,n.get_ErrorMessage())},getDataCount:function(){this.getData_Start();this._intCountPDF==0;var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/EXCELDocumentsDragDrop");n.set_DataObject("EXCELDocumentsDragDrop");n.set_DataAction("GetData");n.addParameter("id",this._sectionID);n.addParameter("section",this._strSectionName);n.addParameter("excel",this._strExcel);n.addDataOK(Function.createDelegate(this,this.getDataCountOK));n.addError(Function.createDelegate(this,this.getDataError));n.addTimeout(Function.createDelegate(this,this.getDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getDataCountOK:function(n){var r=n._result,i=n._result,t;$R_FN.setInnerHTML(this._pnlEXCELDocuments,"");t="";i.Items&&(this._intCountPDF=i.Items.length,t+='<div style="height:50px;"><\/div>');$R_FN.setInnerHTML(this._pnlEXCELDocuments,t);this.getDataOK_End()},enableButtons:function(){},deletePDF:function(n,t){this._frmDelete._intPDFDocumentID=n;this._frmDelete._pdfFileName=t;this.showDeleteForm()},showAddForm:function(){this.showForm(this._frmAdd,!0)},hideAddForm:function(){$(".ajax-file-upload-red").each(function(){$(this).click()});this.showForm(this._frmAdd,!1);this.showNoData(this._intCountPDF==0)},saveAddComplete:function(){this.hideAddForm();this.showSavedOK(!0,$R_RES.ChangesSavedSuccessfully);this._IsPDFAvailable=!0;this.getData()},saveAddError:function(){this.showError(!0,this._frmAdd._strErrorMessage)},showDeleteForm:function(){this.showForm(this._frmDelete,!0)},hideDeleteForm:function(){this.showForm(this._frmDelete,!1);this.showNoData(this._intCountPDF==0)},cancelDelete:function(){this.hideDeleteForm()},saveDeleteComplete:function(){this.hideDeleteForm();this.showSavedOK(!0,$R_RES.ChangesSavedSuccessfully);this.getData()},showPDFPanel:function(){this._IsPDFAvailable=!0;this.showPanel(!0);this.getMaxPDFDocument();this.getData()},showPanel:function(n){$R_FN.showElement(this._pnlEXCELDocuments,n)},pdfNotAvailable:function(n){n&&($R_FN.setInnerHTML(this._pnlEXCELDocuments,""),this.getDataOK_End(),this._intCountPDF=0,this.showNoData(!0))},showAddFormFromDrag:function(n,t){this._frmAdd._strFileName=n;this._frmAdd._dragobj=t;this._frmAdd._strDBFileName="";this._frmAdd.setFieldValue("ctlFile",n);this.showForm(this._frmAdd,!0)},OpenPDF:function(n,t){var i=new Rebound.GlobalTrader.Site.Data;i.set_PathToData("controls/Nuggets/EXCELDocumentsDragDrop");i.set_DataObject("EXCELDocumentsDragDrop");i.set_DataAction("GetPDFAccessURL");i.addParameter("section",n);i.addParameter("filename",t);i.addDataOK(Function.createDelegate(this,this.getOpenPDFOK));i.addError(Function.createDelegate(this,this.getOpenPDFError));i.addTimeout(Function.createDelegate(this,this.getOpenPDFError));$R_DQ.addToQueue(i);$R_DQ.processQueue();i=null},getOpenPDFOK:function(n){var i=n._result,t=n._result;window.open(this.setCleanTextBlobURL(t.bothirl),"_blank")},getOpenPDFError:function(n){this.showError(!0,n.get_ErrorMessage())},setCleanTextBlobURL:function(n){return typeof n=="undefined"&&(n=""),n=(n+"").trim(),n=n.replace(/(:PLUS:)/g,"+"),n=n.replace(/(:AND:)/g,"&"),n.replace(/[+]/g,"%2B")},showUpdateBOMButton:function(n){n?$("#UpdateBomCsv").show():$("#UpdateBomCsv").hide()},navigateToBOMImport:function(){window.location.href="Utility_BOMImport.aspx?BOM="+this._sectionID}};Rebound.GlobalTrader.Site.Controls.Nuggets.EXCELDocumentsDragDrop.registerClass("Rebound.GlobalTrader.Site.Controls.Nuggets.EXCELDocumentsDragDrop",Rebound.GlobalTrader.Site.Controls.Nuggets.Base);