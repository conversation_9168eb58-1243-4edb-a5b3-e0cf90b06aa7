///<reference name="MicrosoftAjax.js" />
///<reference path="~/Common/Data/Data.js" />
///<reference path="~/Common/Functions/Functions.js" />
///<reference path="~/Controls/IconButton/IconButton.js" />
//-----------------------------------------------------------------------------------------
// RP 14.01.2010:
// - resize depending on position on screen, move over to right if needed
// - pause before showing
//
// RP 06.01.2010:
// - dispose everything fully
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls");

Rebound.GlobalTrader.Site.Controls.ToolTip = function(element) { 
	Rebound.GlobalTrader.Site.Controls.ToolTip.initializeBase(this, [element]);
	this._aryElements = [];
	this._intElementCount = 0;
	this._blnShown = false;
	this._intCurrentItem = null;
	this._intHideTimeoutID = -1;
	this._intShowTimeoutID = -1;
	this._intMillisecondsWaitBeforeShow = 1000;
	this._intLoadingWidth = 16;
	this._intOffsetRightX = 12;
	this._blnMoveToRight = false;
};

Rebound.GlobalTrader.Site.Controls.ToolTip.prototype = {

	get_pnlContent: function() { return this._pnlContent; }, 	set_pnlContent: function(v) { if (this._pnlContent !== v)  this._pnlContent = v; }, 
	get_pnlShadow: function() { return this._pnlShadow; }, 	set_pnlShadow: function(v) { if (this._pnlShadow !== v)  this._pnlShadow = v; }, 
	get_strLoading: function() { return this._strLoading; }, 	set_strLoading: function(v) { if (this._strLoading !== v)  this._strLoading = v; }, 
	get_intMillisecondsWaitBeforeShow: function() { return this._intMillisecondsWaitBeforeShow; }, 	set_intMillisecondsWaitBeforeShow: function(v) { if (this._intMillisecondsWaitBeforeShow !== v)  this._intMillisecondsWaitBeforeShow = v; }, 

	initialize: function() {
		$addHandler(this._element, "mouseover", Function.createDelegate(this, this.popupMouseOver));
		$addHandler(this._element, "mouseout", Function.createDelegate(this, this.mouseOut));
		Rebound.GlobalTrader.Site.Controls.ToolTip.callBaseMethod(this, "initialize");
	},

	dispose: function() { 
		if (this.isDisposed) return;
		if (this.get_element()) $clearHandlers(this._element);
		if (this._aryElements) this.clearAllElements();
		this._aryElements = null;
		this._pnlContent = null;
		this._pnlShadow = null;
		this._intElementCount = null;
		this._blnShown = null;
		this._intCurrentItem = null;
		this._intHideTimeoutID = null;
		this._intShowTimeoutID = null;
		this._intScreenWidth = null;
		this._intLoadingWidth = null;
		this._blnMoveToRight = null;
		Rebound.GlobalTrader.Site.Controls.ToolTip.callBaseMethod(this, "dispose");
		this.isDisposed = true;
	},

	registerElement: function(el, strContent, fn, fnParameters, intWidth) {
		if (!el) return;
		if (!this.checkElementOKToBeAdded(el)) return;
		if (!intWidth) intWidth = 300;
		Array.add(this._aryElements, {
			Element: el
			, HasBeenShown: false
			, Event: fn
			, EventParameters: fnParameters
			, Content: (strContent) ? strContent : this._strLoading
			, Width: intWidth
		});
		el.setAttribute("bui_mouseOverInfoID", this._intElementCount);
		$addHandler(el, "mouseout", Function.createDelegate(this, this.mouseOut));
		$addHandler(el, "mouseover", Function.createDelegate(this, this.mouseOver));
		this._intElementCount += 1;
		return (this._intElementCount - 1);
	},

	registerDynamicElement: function(el, strContent, fn, fnParameters, intWidth) {
		this.registerElement(el, strContent, fn, fnParameters, intWidth);
		this.mouseOver({target:el});
	},
	
	checkElementOKToBeAdded: function(el) {
		var blnOK = true;
		for (var i = 0, l = this._aryElements.length; i < l; i++) {
			if (this._aryElements[i].Element == el) {
				blnOK = false;
				break;
			}
		}
		return blnOK;
	},
	
	show: function() {
		this.clearShowTimeout();
		if (this._blnShown) return;
		this._intShowTimeoutID = setTimeout(Function.createDelegate(this, this.finishShow), this._intMillisecondsWaitBeforeShow);
	},
	
	finishShow: function() {
		this.clearShowTimeout();
		if (this._blnShown) return;
		this._blnShown = true;
		this.populate(this._intCurrentItem);
		$R_FN.showElement(this._element, true);
		var fnComplete = Function.createDelegate(this, this.getDataComplete);
		//raise event to go and get the data
		if (!this._aryElements[this._intCurrentItem].HasBeenShown) {
			if (this._aryElements[this._intCurrentItem].Event) this._aryElements[this._intCurrentItem].Event(this._intCurrentItem, this._aryElements[this._intCurrentItem].EventParameters, fnComplete, fnComplete);
		}
		this._aryElements[this._intCurrentItem].HasBeenShown = true;
	},

	hide: function() {
		this.clearShowTimeout();
		$R_FN.showElement(this._element, false);
		this._blnShown = false;
	},

	getDataComplete: function(obj) {
		if (!obj) return;
		obj = Sys.Serialization.JavaScriptSerializer.deserialize(obj);
		this.setContent(obj.TooltipID, $R_FN.setCleanTextValue(obj.Content));
	},

	clearAllElements: function() {
		if (!this._aryElements) return;
		for (var i = 0, l = this._aryElements.length; i < l; i++) {
			if (this._aryElements[i]) $clearHandlers(this._aryElements[i].Element);
		}
		Array.clear(this._aryElements);
		this._intElementCount = 0;
	},
	
	mouseOver: function(sender, eventArgs) {
		this.clearShowTimeout();
		this.clearHideTimeout();
		if (this._blnShown) return;
		this._pnlShadow.style.height = "1px";
		this._pnlShadow.style.width = "1px";
		this._intScreenWidth = Sys.UI.DomElement.getBounds(document.body).width;
		this._intCurrentItem = Number.parseInvariant(sender.target.getAttribute("bui_mouseOverInfoID").toString());
		this.checkMoveToRight();
		this.show();
	},
	
	checkMoveToRight: function() {
		this._blnMoveToRight = false;
		var obj = this._aryElements[this._intCurrentItem];
		var bnds = Sys.UI.DomElement.getBounds(obj.Element);
		if ((obj.Width + bnds.x) > this._intScreenWidth) {
			//tooltip width takes us over edge of screen
			if (bnds.x > (this._intScreenWidth / 2)) {
				//calling element is over halfway across the screen
				this._blnMoveToRight = true;
				obj.Width = this._intScreenWidth - (this._intScreenWidth - (bnds.x + bnds.width - this._intOffsetRightX)) - this._intOffsetRightX;
			} else {
				//calling element is in left half of screen - resize the tooltip to fit
				obj.Width = this._intScreenWidth - bnds.x - (2 * this._intOffsetRightX);
			}
		}
		if (this._blnMoveToRight) {
			Sys.UI.DomElement.removeCssClass(this._element, "ToolTip_Left");
			Sys.UI.DomElement.addCssClass(this._element, "ToolTip_Right");
		} else {
			Sys.UI.DomElement.addCssClass(this._element, "ToolTip_Left");
			Sys.UI.DomElement.removeCssClass(this._element, "ToolTip_Right");
			this.setPosition(bnds.x, bnds.y + bnds.height);
		}
		obj = null;
		bnds = null;
	},
	
	mouseOut: function(sender, eventArgs) {
		this.clearShowTimeout();
		this.clearHideTimeout();
		this._intHideTimeoutID = setTimeout(Function.createDelegate(this, this.hide), 100);
	},
	
	popupMouseOver: function() {
		this.clearHideTimeout();
	},
	
	setContent: function(i, str) {
		if (!this._aryElements[i].Content || this._aryElements[i].Content == this._strLoading) this._aryElements[i].Content = str;
		this.populate(i);
	},
	
	populate: function(i) {
		var objItem = this._aryElements[i];
		var bndsEl = Sys.UI.DomElement.getBounds(objItem.Element);
		$R_FN.setInnerHTML(this._pnlContent, objItem.Content);
		if (!objItem.Content || objItem.Content == this._strLoading) {
			//loading is displayed
			this._pnlContent.style.width = this._intLoadingWidth + "px";
			if (this._blnMoveToRight) this.setPosition(bndsEl.x + bndsEl.width - this._intLoadingWidth - this._intOffsetRightX, bndsEl.y + bndsEl.height);
		} else {
			//content is displayed
			this._pnlContent.style.width = objItem.Width + "px";
			if (this._blnMoveToRight) this.setPosition(bndsEl.x + bndsEl.width - objItem.Width - this._intOffsetRightX, bndsEl.y + bndsEl.height);
		}
		setTimeout(Function.createDelegate(this, this.setShadowDimensions), 0);
		objItem = null;
		bndsEl = null;
	},
	
	setPosition: function(x, y) {
		Sys.UI.DomElement.setLocation(this._element, x, y + 6);
	},
	
	setShadowDimensions: function() {
		var bndsContent = Sys.UI.DomElement.getBounds(this._pnlContent);
		this._pnlShadow.style.height = bndsContent.height + "px";
		this._pnlShadow.style.width = bndsContent.width + "px";
		bndsContent = null;
	},
	
	clearShowTimeout: function() {
		if (this._intShowTimeoutID != -1) clearTimeout(this._intShowTimeoutID);
	},
	
	clearHideTimeout: function() {
		if (this._intHideTimeoutID != -1) clearTimeout(this._intHideTimeoutID);
	}

};

Rebound.GlobalTrader.Site.Controls.ToolTip.registerClass("Rebound.GlobalTrader.Site.Controls.ToolTip", Sys.UI.Control, Sys.IDisposable);
