using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;

namespace Rebound.GlobalTrader.Site.Controls.HomeNuggets.Data {
	[WebService(Namespace = "http://tempuri.org/")]
	[WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    public class PartsBeingOrderedToday : Rebound.GlobalTrader.Site.Data.Base {

		public override void ProcessRequest(HttpContext context) {
			if (base.init(context)) {
				switch (Action) {
					case "GetData": GetData(); break;
					default: WriteErrorActionNotFound(); break;
				}
			}
		}

		/// <summary>
		/// Gets the main data
		/// </summary>
		private void GetData() {
            try
            {
                int intLoginID = LoginID;
                if (GetFormValue_Int("OtherLoginID") > 0) intLoginID = GetFormValue_Int("OtherLoginID");
                //List<PurchaseOrderLine> lstPO = PurchaseOrderLine.GetListTodayForClient(SessionManager.ClientID, null);
                List<PurchaseOrderLine> lstPO = PurchaseOrderLine.GetListTodayForClient(SessionManager.ClientID, intLoginID, RowCount);
                if (lstPO == null)
                {
                    WriteErrorDataNotFound();
                }
                else
                {
                    JsonObject jsn = new JsonObject();
                    JsonObject jsnItems = new JsonObject(true);
                    for (int i = 0; i < lstPO.Count; i++)
                    {
                        JsonObject jsnItem = new JsonObject();
                        jsnItem.AddVariable("ID", lstPO[i].PurchaseOrderId);
                        jsnItem.AddVariable("No", lstPO[i].PurchaseOrderNumber);
                        jsnItem.AddVariable("Part", lstPO[i].FullPart);
                        jsnItem.AddVariable("Buyer", lstPO[i].BuyerName);
                        double dblLineTotal = (double)lstPO[i].Quantity * (double)lstPO[i].Price;
                        jsnItem.AddVariable("LineValue", Functions.FormatCurrency(dblLineTotal, lstPO[i].CurrencyCode, 2));
                        jsnItem.AddVariable("PoApprovedBy", lstPO[i].POApprovedBy);
                        jsnItems.AddVariable(jsnItem);
                        jsnItem.Dispose();
                        jsnItem = null;
                    }
                    jsn.AddVariable("PartsOrderedToday", jsnItems);
                    OutputResult(jsn);
                    jsnItems.Dispose();
                    jsnItems = null;
                }
                lstPO = null;
            }
            catch (Exception ex)
            {
                Errorlog obj = new Errorlog();
                obj.LogMessage(Convert.ToString("Exception- " + ex.Message + " " + ex.StackTrace));
            }
        }


	}
}
