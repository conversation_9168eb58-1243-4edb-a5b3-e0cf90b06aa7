//[0001]     Arpit Mody      07/03/2023     //RP-25
using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;
using System.Text;
using System.IO;
using System.Data.OleDb;
using System.Web.Script.Serialization;
using System.Text.RegularExpressions;
using System.Linq;
//[001] add namespace for Azure Blob storage
using Microsoft.Azure;
using Microsoft.Azure.Storage;
using Microsoft.Azure.Storage.Blob;
using Microsoft.Azure.KeyVault;
using Microsoft.Azure.Storage.Shared;
using Microsoft.Azure.Storage.Auth;
using Microsoft.Azure.Storage.Auth.Protocol;
using Microsoft.Azure.Storage.RetryPolicies;
using Microsoft.Azure.KeyVault.Core;
using System.Net;
//[001]
using System.Configuration;
//using Microsoft.ApplicationInsights;

namespace Rebound.GlobalTrader.Site.Controls.Nuggets.Data
{
    [WebService(Namespace = "http://tempuri.org/")]
    [WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    public class SupplierImport : Rebound.GlobalTrader.Site.Data.Base
    {
        DateTime? OutDate = null;
        string dbServer = null;
        bool IsServerLocal = false;
        string conStr = string.Empty;
        public override void ProcessRequest(HttpContext context)
        {
            if (base.init(context))
            {
                if (GetFormValue_StringForPartSearch("dbServer") != null)
                {
                    dbServer = GetFormValue_StringForPartSearch("dbServer").ToUpper();
                    IsServerLocal = (dbServer == SessionManager.LocalInstanceName);
                }
                if (context.Request.QueryString["action"] != null)
                    Action = context.Request.QueryString["action"];
                switch (Action)
                {
                    case "GetData": GetData(context); break;
                    case "GetDataHeader": GetDataHeader(context); break;
                    case "GetClient": GetClient(context); break;
                    case "GetSupplier": GetSupplier(context); break;
                    case "GetCurrency": GetCurrency(context); break;
                    case "ImportExcelData": ImportExcelData(context); break;
                    case "GetExcelHeader": GetExcelHeader(context); break;
                    case "GenrateSupplierData": GenrateSupplierData(context); break;
                    case "ImportStockData": ImportStockData(context); break;
                    case "GetExcelHeaderFrom": GetExcelHeaderFrom(context); break;
                    case "GetImportExcelHeader": GetImportExcelHeader(context); break;
                    case "DeleteRecord": DeleteRecord(context); break;
                    case "DeleteTempMapping": DeleteTempMapping(context); break;
                    case "SaveSupplierColumnMapping": SaveSupplierColumnMapping(context); break;
                    case "GetSupplierMappedColumn": GetSupplierMappedColumn(context); break;
                    case "GetImportActivity": GetImportActivity(context); break;
                    case "SaveImportActivity": SaveImportActivity(context); break;
                    case "GetBOMClient": GetBOMClient(context); break;

                    default: WriteErrorActionNotFound(); break;
                }
            }
        }
        #region Stock Import Tool
        public void GetBOMClient(HttpContext context)
        {
            try
            {
                DataTable dtClient = BLL.Stock.GetBomClient(SessionManager.ClientID, SessionManager.LoginID ?? 0);
                context.Response.Write(ConvertDataTableToJSON(dtClient));

            }
            catch (Exception ex)
            {
                //var ai = new TelemetryClient();
                //ai.TrackTrace("Exception Header: GetBOMClient");
                //ai.TrackException(ex);

                new Errorlog().LogMessage("Inside Offer GetBOMClient : " + (ex.InnerException == null ? ex.Message : ex.InnerException.Message));
                WriteError(ex);
            }
        }
        public void GetData(HttpContext context)
        {

            try
            {
                int clientType_con = string.IsNullOrEmpty(context.Request.QueryString["clientType"]) ? 0 : int.Parse(context.Request.QueryString["clientType"]);
                int displayLength = int.Parse(context.Request.Params["Length"]);
                int displayStart = int.Parse(context.Request.Params["Start"]);
                int sortCol = 0;// int.Parse(context.Request.Params["order[0][column]"]);
                string sortDir = "asc";// context.Request.Params["order[0][dir]"];
                string search = context.Request.Params["search[value]"];
                int SelectedclientId = string.IsNullOrEmpty(context.Request.QueryString["ClientId"]) ? 0 : int.Parse(context.Request.QueryString["ClientId"]);
                DataTable dtStcokResult = Stock.GetStockDetailFromTemp(displayLength, displayStart, sortCol, sortDir, search, SessionManager.LoginID ?? 0, SessionManager.ClientID ?? 0, SelectedclientId, clientType_con);
                int total = Convert.ToInt32(dtStcokResult.Rows.Count == 0 ? "0" : dtStcokResult.Rows[0]["totalcount"].ToString());
                context.Response.Write(DataTableToJsonObj(dtStcokResult, total, total));
            }
            catch (Exception ex)
            {
                WriteError(ex);
            }
        }
        private void GetExcelHeader(HttpContext context)
        {
            try
            {
                int clientType_con = string.IsNullOrEmpty(context.Request.QueryString["clientType"]) ? 0 : int.Parse(context.Request.QueryString["clientType"]);
                int SelectedclientId = string.IsNullOrEmpty(context.Request.QueryString["ClientId"]) ? 0 : int.Parse(context.Request.QueryString["ClientId"]);
                DataTable dtColumnList = Stock.GetExcelHeader(SessionManager.ClientID ?? 0, SessionManager.LoginID ?? 0, SelectedclientId, clientType_con);
                JavaScriptSerializer js = new JavaScriptSerializer();
                context.Response.Write(ConvertHeaderToJsonObj(dtColumnList));
            }
            catch (Exception ex)
            {
                //var ai = new TelemetryClient();
                //ai.TrackTrace("Exception Header: GetExcelHeader");
                //ai.TrackException(ex);

                WriteError(ex);
            }
        }
        private void GetImportExcelHeader(HttpContext context)
        {
            try
            {
                int clientType_con = string.IsNullOrEmpty(context.Request.QueryString["clientType"]) ? 0 : int.Parse(context.Request.QueryString["clientType"]);
                int SelectedclientId = string.IsNullOrEmpty(context.Request.QueryString["ClientId"]) ? 0 : int.Parse(context.Request.QueryString["ClientId"]);
                string MeppedColumnList = string.IsNullOrEmpty(context.Request.QueryString["selectColumnList"]) ? "" : Convert.ToString((context.Request.QueryString["selectColumnList"]));
                DataTable dtColumnList = Stock.GetImportExcelHeader(SessionManager.ClientID ?? 0, SessionManager.LoginID ?? 0, SelectedclientId, MeppedColumnList.TrimEnd(','), clientType_con);
                JavaScriptSerializer js = new JavaScriptSerializer();
                context.Response.Write(ConvertHeaderToJsonObj(dtColumnList));
            }
            catch (Exception ex)
            {
                //var ai = new TelemetryClient();
                //ai.TrackTrace("Exception Header: GetImportExcelHeader");
                //ai.TrackException(ex);

                new Errorlog().LogMessage("Error at method GetImportExcelHeader : " + ex.Message);
                WriteError(ex);
            }
        }
        private void GetExcelHeaderFrom(HttpContext context)
        {
            try
            {
                int clientType_con = string.IsNullOrEmpty(context.Request.QueryString["clientType"]) ? 0 : int.Parse(context.Request.QueryString["clientType"]);
                int SelectedclientId = string.IsNullOrEmpty(context.Request.QueryString["ClientId"]) ? 0 : int.Parse(context.Request.QueryString["ClientId"]);
                DataTable dtColumnList = Stock.GetExcelHeaderFrom(SessionManager.ClientID ?? 0, SessionManager.LoginID ?? 0, SelectedclientId, clientType_con);
                JavaScriptSerializer js = new JavaScriptSerializer();
                context.Response.Write(ConvertHeaderToJsonObj(dtColumnList));
            }
            catch (Exception ex)
            {
                //var ai = new TelemetryClient();
                //ai.TrackTrace("Exception Header: GetExcelHeaderFrom");
                //ai.TrackException(ex);

                WriteError(ex);
            }
        }
        private void GetDataHeader(HttpContext context)
        {
            try
            {
                DataTable dtColumnList = Stock.GetCustTableAllColumn();
                JavaScriptSerializer js = new JavaScriptSerializer();
                context.Response.Write(ConvertHeaderToJsonObj(dtColumnList));
            }
            catch (Exception ex)
            {
                WriteError(ex);
            }
        }
        public void GetClient(HttpContext context)
        {
            try
            {
                int clientType_con = string.IsNullOrEmpty(context.Request.QueryString["clientType"]) ? 0 : int.Parse(context.Request.QueryString["clientType"]);
                if (clientType_con == 1)
                {
                    //(uk/hk)
                    DataTable dtClient = BLL.Stock.GetClientName(SessionManager.ClientID, clientType_con);
                    context.Response.Write(ConvertDataTableToJSON(dtClient));
                }
                else if (clientType_con == 2)
                {
                    //LocalSqlServer uk
                    DataTable dtClient = BLL.Stock.GetClientName(SessionManager.ClientID, clientType_con);
                    context.Response.Write(ConvertDataTableToJSON(dtClient));
                }
                else if (clientType_con == 3)
                {
                    //GTSqlServer hk
                    DataTable dtClient = BLL.Stock.GetClientName(SessionManager.ClientID, clientType_con);
                    context.Response.Write(ConvertDataTableToJSON(dtClient));
                }






                //jsn.Dispose();
                //jsn = null;
            }
            catch (Exception ex)
            {
                //var ai = new TelemetryClient();
                //ai.TrackTrace("Exception Header: GetClient");
                //ai.TrackException(ex);

                WriteError(ex);
            }
        }
        public void GetSupplier(HttpContext context)
        {
            try
            {
                int clientID = string.IsNullOrEmpty(context.Request.QueryString["ClientId"]) ? 0 : int.Parse(context.Request.QueryString["ClientId"]);
                DataTable dtClient = BLL.Stock.GetSupplier(clientID);
                context.Response.Write(ConvertDataTableToJSON(dtClient));
            }
            catch (Exception ex)
            {
                WriteError(ex);
            }
        }
        public void GetCurrency(HttpContext context)
        {
            try
            {
                int clientID = string.IsNullOrEmpty(context.Request.QueryString["ClientId"]) ? 0 : int.Parse(context.Request.QueryString["ClientId"]);
                int clientType_con = string.IsNullOrEmpty(context.Request.QueryString["clientType"]) ? 0 : int.Parse(context.Request.QueryString["clientType"]);
                DataTable dtClient = BLL.Stock.GetCurrency(clientID, clientType_con);
                context.Response.Write(ConvertDataTableToJSON(dtClient));
                //jsn.Dispose();
                //jsn = null;
            }
            catch (Exception ex)
            {
                WriteError(ex);
            }
        }
        public DataTable ReadCsvFile(string filepath, string chkhead, string FileName)
        {
            DataTable dtCsv = new DataTable();
            dtCsv.Clear();
            Regex CSVParser = new Regex(",(?=(?:[^\"]*\"[^\"]*\")*(?![^\"]*\"))");
            try

            {
                //Fulltext = sr.ReadToEnd().ToString().Replace("\"\n", ""); //read full file text  
                //FulltextCheck=Fulltext.Replace("\"", "");
                string Fulltext;
                WebClient web = new WebClient();
                System.IO.Stream stream = web.OpenRead(filepath);
                using (StreamReader sr = new StreamReader(stream, Encoding.Default))
                {
                    while (!sr.EndOfStream)
                    {
                        Fulltext = sr.ReadToEnd().ToString(); //read full file text  
                        //[0001] parsing for CSV file to parse "line-breaks-between-double-quotes"
                        Fulltext = Regex.Replace(Fulltext, "\"[^\"]*(?:\"\"[^\"]*)*\"", m => m.Value.Replace("\n", ""));
                        string[] rows = Fulltext.Split('\n'); //split full file text into rows  
                        if (chkhead == "YES")
                        {
                            for (int i = 0; i < rows.Length - 1; i++)
                            {
                                //string[] rowValues = rows[i].Split(','); //split each row with comma to get individual values  
                                string[] rowValues = CSVParser.Split(rows[i]); //split each row with comma to get individual values  
                                {
                                    if (i == 0)
                                    {
                                        for (int j = 0; j < rowValues.Length; j++)
                                        {
                                            dtCsv.Columns.Add(Functions.CleanDatabaseFilter(Functions.ReplaceLineBreaks(Functions.CleanJunkCharInCSV(Functions.FormatStringForDatabase((rowValues[j].ToString()))))));

                                        }

                                    }
                                    else
                                    {

                                        DataRow dr = dtCsv.NewRow();
                                        for (int k = 0; k < rowValues.Length; k++)
                                        {

                                            //dr[k] = Functions.CleanJunkCharInCSV(Functions.CleanDatabaseForStockTool((rowValues[k].ToString())));
                                            dr[k] = Functions.CleanJunkCharInCSV(Functions.CleanDatabaseForStockTool(Functions.CleanCarriageReturnTabAndNewLineCharacter(rowValues[k].ToString())));
                                            //dr[k] = Functions.FormatStringForDatabase(Functions.CleanDatabaseForStockTool(Functions.CleanJunkCharInCSV((rowValues[k].ToString()))));
                                        }
                                        dtCsv.Rows.Add(dr); //add other rows 

                                    }
                                }
                            }
                        }
                        else
                        {
                            for (int i = 0; i < rows.Length - 1; i++)
                            {
                                //string[] rowValues = rows[i].Split(','); //split each row with comma to get individual values  
                                string[] rowValues = CSVParser.Split(rows[i]); //split each row with comma to get individual values  
                                {
                                    if (i == 0)
                                    {
                                        int counter = 1;

                                        for (int j = 0; j < rowValues.Length; j++)
                                        {
                                            dtCsv.Columns.Add("F" + counter); //Add header if not have header like F1
                                            counter++;

                                        }
                                        DataRow dr = dtCsv.NewRow();
                                        for (int k = 0; k < rowValues.Length; k++)
                                        {

                                            dr[k] = Functions.CleanJunkCharInCSV(Functions.CleanDatabaseForStockTool((rowValues[k].ToString())));
                                            //dr[k] = Functions.FormatStringForDatabase(Functions.CleanDatabaseForStockTool(Functions.CleanJunkCharInCSV((rowValues[k].ToString()))));
                                        }
                                        dtCsv.Rows.Add(dr); //add other rows 

                                    }
                                    else
                                    {

                                        DataRow dr = dtCsv.NewRow();
                                        for (int k = 0; k < rowValues.Length; k++)
                                        {

                                            dr[k] = Functions.CleanJunkCharInCSV(Functions.CleanDatabaseForStockTool((rowValues[k].ToString())));
                                            //dr[k] = Functions.FormatStringForDatabase(Functions.CleanDatabaseForStockTool(Functions.CleanJunkCharInCSV((rowValues[k].ToString()))));
                                        }
                                        dtCsv.Rows.Add(dr); //add other rows 

                                    }
                                }
                            }
                        }
                    }
                }

                //Vinay: 05 May 2021: Dispose unused object
                stream.Dispose();
                web.Dispose();
                web = null;
                CSVParser = null;
                string Deletetempfolderfile = FileUploadManager.GetTemporaryUploadFilePath() + FileName;
                System.IO.File.Delete(Deletetempfolderfile);
            }
            catch (Exception)
            {
                //var ai = new TelemetryClient();
                //ai.TrackTrace("Exception Header: ReadCSVFile");
                //ai.TrackException(ex);

                string Deletetempfolderfile = FileUploadManager.GetTemporaryUploadFilePath() + FileName;
                System.IO.File.Delete(Deletetempfolderfile);
                throw new Exception("Error Occured while processing the csv file.Kindly review the data in the csv file.", new Exception("CSVDataError"));
            }
            return dtCsv;
        }


        public DataTable ConvertExcelToDataTable(string FilePath, string chkhead, string FileName)
        {
            DataTable dt = new DataTable();
            FileInfo fi = new FileInfo(FilePath);
            try
            {
                if (fi.Exists)
                {
                    string Extension = Path.GetExtension(FilePath);
                    var connectionString = "";
                    // connectionString = "Provider=Microsoft.ACE.OLEDB.12.0;Data Source=" + FilePath + ";" + "Extended Properties='Excel 12.0 Xml;HDR=" + chkhead + ";IMEX=1;MAXSCANROWS=0'";
                    switch (Extension)
                    {
                        case ".xls": //Excel 97-03

                            connectionString = "Provider=Microsoft.Jet.OLEDB.4.0;Data Source=" + FilePath + ";" + "Extended Properties='Excel 8.0;HDR=" + chkhead + ";'";
                            break;
                        case ".xlsx": //Excel 07
                            connectionString = "Provider=Microsoft.ACE.OLEDB.12.0;Data Source=" + FilePath + ";" + "Extended Properties='Excel 12.0 Xml;HDR=" + chkhead + ";IMEX=1;MAXSCANROWS=0'";
                            break;
                    }
                    // var connectionString = "Provider=Microsoft.ACE.OLEDB.12.0;Data Source=" + FilePath + ";" + "Extended Properties='Excel 12.0 Xml;HDR=YES;IMEX=1;MAXSCANROWS=0'";
                    using (var conn = new OleDbConnection(connectionString))
                    {
                        conn.Open();

                        var sheets = conn.GetOleDbSchemaTable(System.Data.OleDb.OleDbSchemaGuid.Tables, new object[] { null, null, null, "TABLE" });
                        using (var cmd = conn.CreateCommand())
                        {
                            cmd.CommandText = "SELECT * FROM [" + sheets.Rows[0]["TABLE_NAME"].ToString() + "] ";

                            var adapter = new OleDbDataAdapter(cmd);

                            adapter.Fill(dt);
                        }

                    }
                    string Deletetempfolderfile = FileUploadManager.GetTemporaryUploadFilePath() + FileName;
                    System.IO.File.Delete(Deletetempfolderfile);
                }

            }
            catch (Exception)
            {
                //var ai = new TelemetryClient();
                //ai.TrackTrace("Exception Header: ConvertExcelToDataTable");
                //ai.TrackException(ex);

                throw new Exception("Error Occured while processing the excel file.Kindly review the data in the excel file.", new Exception("ExcelDataError"));
            }
            return dt;
        }

        public DataTable ConvertExcelToDataTableNew(string FilePath, string chkhead, string FileName)
        {
            DataTable dt = new DataTable();
            FileInfo fi = new FileInfo(FilePath);
            try
            {
                if (fi.Exists)
                {
                    List<string> sheets = ExcelAdapter.GetSheet(FilePath);
                    if (sheets.Count > 0)
                    {
                        if (chkhead == "YES")
                        { dt = ExcelAdapter.ReadExcel(FilePath, sheets[0]); }
                        else
                        {
                            dt = ExcelAdapter.ReadExcel(FilePath, sheets[0], false);
                            int i = 1;
                            int c = 0;
                            foreach (DataColumn column in dt.Columns)
                            {
                                if (i <= 15)
                                {
                                    dt.Columns[c].ColumnName = "F" + i;
                                }
                                ++i;
                                ++c;

                            }
                        }

                    }



                }
                string Deletetempfolderfile = FileUploadManager.GetTemporaryUploadFilePath() + FileName;
                System.IO.File.Delete(Deletetempfolderfile);

            }
            catch (Exception ex)
            {
                //new Errorlog().LogMessage("Error at method Offer ConvertExcelToDataTableNew : " + ex.Message);
                string Deletetempfolderfile = FileUploadManager.GetTemporaryUploadFilePath() + FileName;
                System.IO.File.Delete(Deletetempfolderfile);
                //throw new Exception("Error Occured while processing the excel file.Kindly review the data in the excel file.", new Exception("ExcelDataError"));
                throw new Exception(ex.Message, new Exception("ExcelDataError"));
            }
            return dt;
        }

        private string ConvertDataTableToJSON(DataTable dt)
        {
            System.Web.Script.Serialization.JavaScriptSerializer serializer = new System.Web.Script.Serialization.JavaScriptSerializer();
            serializer.MaxJsonLength = Int32.MaxValue;
            List<Dictionary<string, object>> rows = new List<Dictionary<string, object>>();
            Dictionary<string, object> row;
            foreach (DataRow dr in dt.Rows)
            {
                row = new Dictionary<string, object>();
                foreach (DataColumn col in dt.Columns)
                {
                    row.Add(col.ColumnName, dr[col]);
                }
                rows.Add(row);
            }
            return serializer.Serialize(rows);
        }

        public void ImportExcelData(HttpContext context)
        {
            try
            {
                bool IsLimitExceeded = false;
                string LimitErrorMessage = "";
                string originalFilename = GetFormValue_String("originalFilename");
                string generatedFilename = GetFormValue_String("generatedFilename");
                string chkcolumnheader = GetFormValue_String("ColumnHeader");
                int clientType_con = GetFormValue_Int("SelectedClientType");


                string filepathtempfolder = FileUploadManager.GetTemporaryUploadFilePath() + generatedFilename;
                string filepath = ConfigurationManager.AppSettings["ExcelDocumentPhysicalURLForAzureBlob"].ToString() + @"STOCKIMPORT/" + generatedFilename;
                //String strorageconn = ConfigurationManager.AppSettings.Get("StorageConnectionString");
                //CloudStorageAccount storageacc = CloudStorageAccount.Parse(strorageconn);
                // CloudBlobClient client = storageacc.CreateCloudBlobClient();
                string accountname = ConfigurationManager.AppSettings.Get("StorageName");
                string accesskey = ConfigurationManager.AppSettings.Get("StorageKey1");
                StorageCredentials creden = new StorageCredentials(accountname, accesskey);
                CloudStorageAccount acc = new CloudStorageAccount(creden, useHttps: true);
                CloudBlobClient client = acc.CreateCloudBlobClient();
                CloudBlobContainer cont = client.GetContainerReference("gtdocmgmt");
                if (cont.Exists())
                {
                    CloudBlobDirectory directory = cont.GetDirectoryReference("STOCKIMPORT");
                    CloudBlockBlob cblob = directory.GetBlockBlobReference(GetFormValue_String("generatedFilename"));
                    if (cblob.Exists())
                    {
                        string fileExtension = Path.GetExtension(filepath);
                        int SelectedclientId = int.Parse(string.IsNullOrEmpty(GetFormValue_String("ClientId")) ? "0" : GetFormValue_String("ClientId"));
                        //DataTable dt = fileExtension == ".csv" ? ReadCsvFile(filepath, chkcolumnheader) : ConvertExcelToDataTable(filepathtempfolder, chkcolumnheader, generatedFilename);//ReadExcel(filepath)/or/ReadCsvFile(filepath);
                        //DataTable dt = fileExtension == ".csv" ? ConvertCSVToDataTableNew(filepathtempfolder, chkcolumnheader, generatedFilename) : ConvertExcelToDataTableNew(filepathtempfolder, chkcolumnheader, generatedFilename);//ReadExcel(filepath)/or/ReadCsvFile(filepath);
                        DataTable dt = fileExtension == ".csv" ? ReadCsvFile(filepathtempfolder, chkcolumnheader, generatedFilename) : ConvertExcelToDataTableNew(filepathtempfolder, chkcolumnheader, generatedFilename);

                        int filelogid = 0;
                        if (dt.Rows.Count<=Convert.ToInt32(ConfigurationManager.AppSettings["MaxUploadRowCount"]))
                        {
                            this.SaveExcelHeader(dt, SelectedclientId, clientType_con);
                            this.saveExcelData_BulkSave(dt, originalFilename, generatedFilename, SelectedclientId, clientType_con);
                            //Vinay: 05 May 2021: Dispose unused object

                            IsLimitExceeded = false;
                            LimitErrorMessage = "";
                        }
                        else
                        {
                            cblob.DeleteIfExists();
                            IsLimitExceeded = true;
                            //LimitErrorMessage = "Maximum limit should not exceed " + ConfigurationManager.AppSettings["MaxUploadRowCount"].ToString() + " rows.";
                            //[0001]
                            LimitErrorMessage = string.Format(ConfigurationManager.AppSettings["RowCountWarningMessage"].ToString(), ConfigurationManager.AppSettings["MaxUploadRowCount"].ToString());
                        }



                        dt.Dispose();

                        JsonObject jsn = new JsonObject();
                        jsn.AddVariable("FileLogId", filelogid);
                        jsn.AddVariable("IsLimitExceeded", IsLimitExceeded);
                        jsn.AddVariable("LimitErrorMessage", LimitErrorMessage);
                        OutputResult(jsn);
                        jsn.Dispose(); jsn = null;
                    }
                    else
                    {
                        throw new Exception("File not uploaded successfully.", new Exception("FileNotFound"));
                    }
                }

            }
            catch (Exception ex)
            {
                //var ai = new TelemetryClient();
                //ai.TrackTrace("Exception Header: ImportExcelData");
                //ai.TrackException(ex);

                WriteError(ex);
            }
        }
        private void SaveExcelHeader(DataTable dtData, int SelectedclientId, int clientType_con)
        {
            try
            {
                string columnList = string.Empty;
                string insertColumnList = string.Empty;
                int i = 1;
                foreach (DataColumn column in dtData.Columns)
                {
                    if (i <= 15)
                    {
                        columnList = columnList + "'" + column.ColumnName + "',";
                        insertColumnList = insertColumnList + "Column" + i.ToString() + ",";
                    }
                    ++i;
                }
                if (!string.IsNullOrEmpty(columnList))
                    columnList = columnList.Substring(0, columnList.Length - 1);
                if (!string.IsNullOrEmpty(insertColumnList))
                    insertColumnList = insertColumnList.Substring(0, insertColumnList.Length - 1);

                BLL.Stock.SaveExcelHeader(columnList, insertColumnList, SessionManager.ClientID, SelectedclientId, SessionManager.LoginID ?? 0, clientType_con);
            }
            catch (Exception ex)
            {
                //var ai = new TelemetryClient();
                //ai.TrackTrace("Exception Header: SaveExcelHeader");
                //ai.TrackException(ex);

                new Errorlog().LogMessage("Error at method SaveExcelHeader : " + ex.Message);
                WriteError(ex);
            }
        }

        private void saveExcelData_BulkSave(DataTable dtData, string originalFilename, string generatedFilename, int SelectedclientId, int clientType_con)
        {
            try
            {
                DataTable tempStock = new DataTable("BorisGlobalTraderImports.dbo.tbTempStockData");
                // Copy the DataTable to SQL Server using SqlBulkCopy
                BLL.Stock.saveExcelBulkSave(tempStock, dtData, originalFilename, generatedFilename, SessionManager.LoginID ?? 0, SessionManager.ClientID ?? 0, SelectedclientId, clientType_con);
            }
            catch (Exception ex)
            {
                //var ai = new TelemetryClient();
                //ai.TrackTrace("Exception Header: saveExcelData_BulkSave");
                //ai.TrackException(ex);

                WriteError(ex);
            }



        }
        private string ConvertHeaderToJsonObj(DataTable dt)
        {
            StringBuilder JsonString = new StringBuilder();
            if (dt != null && dt.Rows.Count > 0)
            {
                //JsonString.Append("[");
                JsonString.Append("{");
                JsonString.Append("\"data\":");
                JsonString.Append("[");
                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    JsonString.Append("{");
                    for (int j = 0; j < dt.Columns.Count; j++)
                    {
                        if (j < dt.Columns.Count - 1)
                        {
                            JsonString.Append("\"" + dt.Columns[j].ColumnName.ToString() + "\":" + "\"" + dt.Rows[i][j].ToString().Replace(@"\r", "").Replace(@"\n", "").Replace(@"\", "") + "\",");
                        }
                        else if (j == dt.Columns.Count - 1)
                        {
                            JsonString.Append("\"" + dt.Columns[j].ColumnName.ToString() + "\":" + "\"" + dt.Rows[i][j].ToString().Replace(@"\r", "").Replace(@"\n", "").Replace(@"\", "") + "\"");
                        }
                    }
                    if (i == dt.Rows.Count - 1)
                    {
                        JsonString.Append("}");
                    }
                    else
                    {
                        JsonString.Append("},");
                    }
                }
                JsonString.Append("]");
                JsonString.Append("}");
                //JsonString.Append("]");

                return Regex.Replace(JsonString.ToString(), @"\r\n?|\n", "");
            }
            else
            {
                return null;
            }
        }
        private string DataTableToJsonObj(DataTable dt, int iTotalRecords, int iTotalDisplayRecords)
        {
            StringBuilder JsonString = new StringBuilder();
            if (dt != null && dt.Rows.Count > 0)
            {
                //JsonString.Append("[");
                JsonString.Append("{\"iTotalRecords\":" + iTotalRecords + ",");
                JsonString.Append("\"iTotalDisplayRecords\":" + iTotalDisplayRecords + ",");
                JsonString.Append("\"data\":");
                JsonString.Append("[");
                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    JsonString.Append("{");
                    for (int j = 0; j < dt.Columns.Count; j++)
                    {
                        if (j < dt.Columns.Count - 1)
                        {
                            //JsonString.Append("\"" + dt.Columns[j].ColumnName.ToString() + "\":" + "\"" + dt.Rows[i][j].ToString().Replace(@"\r", "").Replace(@"\n", "").Replace(@"\", "") + "\",");
                            JsonString.Append("\"" + dt.Columns[j].ColumnName.ToString() + "\":" + "\"" + dt.Rows[i][j].ToString() + "\",");
                        }
                        else if (j == dt.Columns.Count - 1)
                        {
                            //JsonString.Append("\"" + dt.Columns[j].ColumnName.ToString() + "\":" + "\"" + dt.Rows[i][j].ToString().Replace(@"\r", "").Replace(@"\n", "").Replace(@"\", "") + "\"");
                            JsonString.Append("\"" + dt.Columns[j].ColumnName.ToString() + "\":" + "\"" + dt.Rows[i][j].ToString() + "\"");
                        }
                    }
                    if (i == dt.Rows.Count - 1)
                    {
                        JsonString.Append("}");
                    }
                    else
                    {
                        JsonString.Append("},");
                    }
                }
                JsonString.Append("]");
                JsonString.Append("}");
                //JsonString.Append("]");


            }
            else
            {
                JsonString.Append("{\"iTotalRecords\":" + iTotalRecords + ",");
                JsonString.Append("\"iTotalDisplayRecords\":" + iTotalDisplayRecords + ",");
                JsonString.Append("\"data\":");
                JsonString.Append("[]}");
            }
            //return Regex.Replace(JsonString.ToString(), @"\r\n?|\n", "");
            return Regex.Replace(JsonString.ToString(), @"\t|\n|\r", "");
        }
        private void GenrateSupplierData(HttpContext context)
        {
            string Column_Lable = "";
            string insertDataList = string.Empty;
            string Column_Name = "";

            DataTable dtGenrateImport = new DataTable();
            dtGenrateImport.Clear();

            try
            {
                #region Stock checkbox paramete
                new Errorlog().LogMessage("Generate method started" + DateTime.UtcNow.ToString());
                int clientType_con = string.IsNullOrEmpty(context.Request.QueryString["clientType"]) ? 0 : int.Parse(context.Request.QueryString["clientType"]);
                int SupplierId = string.IsNullOrEmpty(context.Request.QueryString["SupplierId"]) ? 0 : int.Parse(context.Request.QueryString["SupplierId"]);
                string SupplierNameText = string.IsNullOrEmpty(context.Request.QueryString["SupplierNameText"]) ? "" : Convert.ToString((context.Request.QueryString["SupplierNameText"]));
                int recordType = string.IsNullOrEmpty(context.Request.QueryString["recordType"]) ? 0 : int.Parse(context.Request.QueryString["recordType"]);
                int SelectedclientId = string.IsNullOrEmpty(context.Request.QueryString["SelectedclientId"]) ? 0 : int.Parse(context.Request.QueryString["SelectedclientId"]);

                //checkbox parameter
                #endregion

                #region Stock  Dropdwon parameter
                string ddlCurrency = string.IsNullOrEmpty(context.Request.QueryString["ddlCurrency"]) ? "" : Convert.ToString((context.Request.QueryString["ddlCurrency"]));
                string btnImport = string.IsNullOrEmpty(context.Request.QueryString["btnImport"]) ? "" : Convert.ToString((context.Request.QueryString["btnImport"]));
                //mapped selected column list
                insertDataList = string.IsNullOrEmpty(context.Request.QueryString["insertDataList"]) ? "" : Convert.ToString((context.Request.QueryString["insertDataList"]));
                Column_Lable = string.IsNullOrEmpty(context.Request.QueryString["Column_Lable"]) ? "" : Convert.ToString((context.Request.QueryString["Column_Lable"]));
                Column_Name = string.IsNullOrEmpty(context.Request.QueryString["Column_Name"]) ? "" : Convert.ToString((context.Request.QueryString["Column_Name"]));


                string FixedCurrency = string.Empty;
                if (string.IsNullOrEmpty(ddlCurrency))
                { ddlCurrency = "GBP"; }
                else
                {
                    if (ddlCurrency == "< Not Imported >")
                    { ddlCurrency = "GBP"; }
                    else { FixedCurrency = ddlCurrency; }
                }

                #endregion
                #region Stock Import Genrate JSON Table


                int displayLength = 20;//string.IsNullOrEmpty(context.Request.Params["Length"]) ? 20 : int.Parse(context.Request.Params["Length"]);//int.Parse(context.Request.Params["Length"]);
                int displayStart = 0;//string.IsNullOrEmpty(context.Request.Params["Start"]) ? 0 : int.Parse(context.Request.Params["Start"]);//int.Parse(context.Request.Params["Start"]);
                int sortCol = 0;//string.IsNullOrEmpty(context.Request.Params["order[0][column]"]) ? 0 : int.Parse(context.Request.Params["order[0][column]"]);// int.Parse(context.Request.Params["order[0][column]"]);
                string sortDir = "";//"asc";// context.Request.Params["order[0][dir]"];
                string search = "";//context.Request.Params["search[value]"];
                dtGenrateImport = Stock.GetStockGenrateTempData(displayLength, displayStart, sortCol, sortDir, search, SessionManager.LoginID ?? 0, SessionManager.ClientID ?? 0, SelectedclientId, insertDataList.TrimEnd(','), ddlCurrency, SupplierNameText, clientType_con, Column_Lable.TrimEnd(','), Column_Name.TrimEnd(','));
                int total = 20;//Convert.ToInt32(dtGenrateImport.Rows.Count == 0 ? "0" : dtGenrateImport.Rows[0]["TotalCount"].ToString());
                dtGenrateImport.Columns.Remove("TotalCount");
                dtGenrateImport.Columns.Remove("RowNum");
                var serializer = new System.Web.Script.Serialization.JavaScriptSerializer();

                TestData t = new TestData();
                TestData1 t1 = new TestData1();
                List<columnsinfo> _col = new List<columnsinfo>();

                for (int i = 0; i <= dtGenrateImport.Columns.Count - 1; i++)
                {
                    _col.Add(new columnsinfo { title = dtGenrateImport.Columns[i].ColumnName, data = dtGenrateImport.Columns[i].ColumnName });
                }
                t1.columns = _col;
                string col = (string)serializer.Serialize(_col);
                t.columns = col;
                var lst = dtGenrateImport.AsEnumerable().Select(r => r.Table.Columns.Cast<DataColumn>().Select(c => new KeyValuePair<string, object>(c.ColumnName, r[c.Ordinal])).ToDictionary(z => z.Key, z => z.Value)).ToList();
                t1.data = lst;
                string data = serializer.Serialize(lst);
                t.data = data;
                string strNewDate = serializer.Serialize(t1);
                string teststring = "{\"iTotalRecords\":" + total + "," + "\"iTotalDisplayRecords\":" + total + "," + strNewDate.TrimStart('{');
                new Errorlog().LogMessage("Generate method finish" + DateTime.UtcNow.ToString());
                context.Response.Write(teststring);

                #endregion


            }
            catch (Exception ex)
            {
                //var ai = new TelemetryClient();
                //ai.TrackTrace("Exception Header: GenrateSupplierData");
                //ai.TrackException(ex);

                //WriteError(ex);

                TestData1 err = new TestData1();
                err.IsError = true;
                err.ErrorMessage = ex.InnerException.Message;
                //err.ErrorMessage += "<br />" + ex.StackTrace.Replace(System.Environment.NewLine, "<br />");
                new Errorlog().LogMessage("Inside Offer GenrateSupplierData : " + (ex.InnerException == null ? ex.Message : ex.InnerException.Message));
                context.Response.Write(new JavaScriptSerializer().Serialize(err));
            }


        }
        public class columnsinfo
        {
            public string title { get; set; }
            public string data { get; set; }
        }
        public class TestData
        {
            public string jsondata { get; set; }
            public string columns { get; set; }
            public string data { get; set; }
        }
        public class TestData1
        {
            public List<columnsinfo> columns { get; set; }
            public List<Dictionary<string, object>> data { get; set; }

            public bool IsError { get; set; }

            public string ErrorMessage { get; set; }
        }
        private void DeleteRecord(HttpContext context)
        {
            try
            {
                int clientType_con = string.IsNullOrEmpty(context.Request.QueryString["clientType"]) ? 0 : int.Parse(context.Request.QueryString["clientType"]);
                int SelectedclientId = int.Parse(context.Request.QueryString["SelectedclientId"].ToString());
                Stock.DeleteRecord(SelectedclientId, SessionManager.LoginID ?? 0, SessionManager.ClientID ?? 0, clientType_con);
            }
            catch (Exception)
            {

            }
        }
        private void DeleteTempMapping(HttpContext context)
        {
            try
            {
                int clientType_con = string.IsNullOrEmpty(context.Request.QueryString["clientType"]) ? 0 : int.Parse(context.Request.QueryString["clientType"]);
                int SupplierId = string.IsNullOrEmpty(context.Request.QueryString["SupplierId"]) ? 0 : int.Parse(context.Request.QueryString["SupplierId"]);
                Stock.DeleteTempMapping(SupplierId, clientType_con);
            }
            catch (Exception)
            {

            }
        }
        private void SaveSupplierColumnMapping(HttpContext context)
        {
            try
            {
                string InsertMeppedColumnList = string.Empty;
                int clientType_con = string.IsNullOrEmpty(context.Request.QueryString["clientType"]) ? 0 : int.Parse(context.Request.QueryString["clientType"]);
                int SupplierId = string.IsNullOrEmpty(context.Request.QueryString["SupplierId"]) ? 0 : int.Parse(context.Request.QueryString["SupplierId"]);
                InsertMeppedColumnList = string.IsNullOrEmpty(context.Request.QueryString["SaveMappingColumnlist"]) ? "" : Convert.ToString((context.Request.QueryString["SaveMappingColumnlist"]));
                Stock.SaveSupplierColumnMapping(SupplierId, InsertMeppedColumnList.TrimEnd(','), clientType_con);
            }
            catch (Exception ex)
            {
                new Errorlog().LogMessage("Error at method SaveSupplierColumnMapping : " + ex.Message);
            }
        }
        public void GetSupplierMappedColumn(HttpContext context)
        {
            try
            {
                int clientType_con = string.IsNullOrEmpty(context.Request.QueryString["clientType"]) ? 0 : int.Parse(context.Request.QueryString["clientType"]);
                int SupplierId = string.IsNullOrEmpty(context.Request.QueryString["SupplierId"]) ? 0 : int.Parse(context.Request.QueryString["SupplierId"]);
                DataTable dtSpResult = Stock.GetSupplierMappedColumn(SupplierId, clientType_con);
                context.Response.Write(ConvertHeaderToJsonObj(dtSpResult));

            }
            catch (Exception ex)
            {
                new Errorlog().LogMessage("Inside Offer GetSupplierMappedColumn : " + (ex.InnerException == null ? ex.Message : ex.InnerException.Message));
                WriteErrorDataNotFound();
            }
        }
        private void ImportStockData(HttpContext context)
        {

            string Column_Lable = "";
            string insertDataList = "";
            string Column_Name = "";


            try
            {
                #region Stock checkbox paramete
                new Errorlog().LogMessage("Import method started" + DateTime.UtcNow.ToString());
                int clientType_con = string.IsNullOrEmpty(context.Request.QueryString["clientType"]) ? 0 : int.Parse(context.Request.QueryString["clientType"]);
                int SupplierId = string.IsNullOrEmpty(context.Request.QueryString["SupplierId"]) ? 0 : int.Parse(context.Request.QueryString["SupplierId"]);
                int recordType = string.IsNullOrEmpty(context.Request.QueryString["recordType"]) ? 0 : int.Parse(context.Request.QueryString["recordType"]);
                int SelectedclientId = string.IsNullOrEmpty(context.Request.QueryString["SelectedclientId"]) ? 0 : int.Parse(context.Request.QueryString["SelectedclientId"]);
                string ActionPerform = string.IsNullOrEmpty(context.Request.QueryString["ActionPerform"]) ? "" : Convert.ToString((context.Request.QueryString["ActionPerform"]));
                //checkbox parameter
                #endregion

                #region Stock  Dropdwon parameter
                string ddlCurrency = string.IsNullOrEmpty(context.Request.QueryString["ddlCurrency"]) ? "" : Convert.ToString((context.Request.QueryString["ddlCurrency"]));
                string btnImport = string.IsNullOrEmpty(context.Request.QueryString["btnImport"]) ? "" : Convert.ToString((context.Request.QueryString["btnImport"]));
                //mapped selected column list
                Column_Lable = string.IsNullOrEmpty(context.Request.QueryString["Column_Lable"]) ? "" : Convert.ToString((context.Request.QueryString["Column_Lable"]));
                insertDataList = string.IsNullOrEmpty(context.Request.QueryString["insertDataList"]) ? "" : Convert.ToString((context.Request.QueryString["insertDataList"]));
                Column_Name = string.IsNullOrEmpty(context.Request.QueryString["Column_Name"]) ? "" : Convert.ToString((context.Request.QueryString["Column_Name"]));


                #endregion
                DataTable dtcount = new DataTable();
                dtcount.Clear();
                int displayLength = 11;//int.Parse(context.Request.Params["Length"]);
                int displayStart = 0;//int.Parse(context.Request.Params["Start"]);
                int sortCol = 0;// int.Parse(context.Request.Params["order[0][column]"]);
                string sortDir = "asc";// context.Request.Params["order[0][dir]"];
                string search = context.Request.Params["search[value]"];
                dtcount = Stock.GetStockDetailFromTemp(displayLength, displayStart, sortCol, sortDir, search, SessionManager.LoginID ?? 0, SessionManager.ClientID ?? 0, SelectedclientId, clientType_con);
                string TotalCount = dtcount.Rows[0]["TotalCount"].ToString();
                string OriginalFilename = dtcount.Rows[0]["OriginalFilename"].ToString();

                #region Stock Import Save data in SQL Table
                if (ActionPerform == "ImportData")
                {

                    string fileColName = recordType == 1 ? "OfferName" : recordType == 2 ? "ExcessName" : "StockInfoName";
                    string FixedCurrency = string.Empty;
                    if (string.IsNullOrEmpty(ddlCurrency))
                    { ddlCurrency = "GBP"; }
                    else
                    {
                        if (ddlCurrency == "< Not Imported >")
                        { ddlCurrency = "GBP"; }
                        else { FixedCurrency = ddlCurrency; }
                    }

                    string insertcolumndata = SupplierId + " as SupplierNo " + "," + insertDataList.TrimEnd(',');
                    string errorMessage = "";
                    int recordCount = BLL.Stock.InsertUpdateStockImportData(SessionManager.LoginID ?? 0, SessionManager.ClientID ?? 0, SelectedclientId, Column_Lable.TrimEnd(','), Column_Name.TrimEnd(','), insertcolumndata, fileColName, SupplierId, ddlCurrency, recordType, clientType_con, out errorMessage);
                    new Errorlog().LogMessage("records imported " + TotalCount.ToString());
                    new Errorlog().LogMessage("Import method finish" + DateTime.UtcNow.ToString());
                    //context.Response.Write(TotalCount+","+OriginalFilename);
                    context.Response.Write(recordCount + "," + OriginalFilename + "," + errorMessage);
                }
                #endregion

            }
            catch (Exception ex)
            {
                //var ai = new TelemetryClient();
                //ai.TrackTrace("Exception Header: ImportStockData");
                //ai.TrackException(ex);

                WriteError(ex);
            }


        }
        public void GetImportActivity(HttpContext context)
        {

            try
            {
                int clientType_con = string.IsNullOrEmpty(context.Request.QueryString["clientType"]) ? 0 : int.Parse(context.Request.QueryString["clientType"]);
                int SelectedclientId = string.IsNullOrEmpty(context.Request.QueryString["SelectedclientId"]) ? 0 : int.Parse(context.Request.QueryString["SelectedclientId"]);
                int displayLength = string.IsNullOrEmpty(context.Request.Params["Length"]) ? 0 : int.Parse(context.Request.Params["Length"]); //int.Parse(context.Request.Params["Length"]);
                int displayStart = string.IsNullOrEmpty(context.Request.Params["Start"]) ? 0 : int.Parse(context.Request.Params["Start"]); //int.Parse(context.Request.Params["Start"]);
                int sortCol = string.IsNullOrEmpty(context.Request.Params["Start"]) ? 0 : int.Parse(context.Request.Params["order[0][column]"]);// int.Parse(context.Request.Params["order[0][column]"]);
                string sortDir = string.IsNullOrEmpty(context.Request.Params["order[0][dir]"]) ? "" : Convert.ToString((context.Request.Params["order[0][dir]"])); //context.Request.Params["order[0][dir]"];
                string search = string.IsNullOrEmpty(context.Request.Params["search[value]"]) ? "" : Convert.ToString((context.Request.Params["search[value]"])); //context.Request.Params["search[value]"];

                if (clientType_con == 1)
                {
                    DataTable dtStcokResult = Stock.GetStockImportactivity(displayLength, displayStart, sortCol, sortDir, search, SessionManager.ClientID ?? 0, clientType_con, SelectedclientId, SessionManager.LoginID ?? 0);
                    int total = Convert.ToInt32(dtStcokResult.Rows.Count == 0 ? "0" : dtStcokResult.Rows[0]["totalcount"].ToString());
                    context.Response.Write(DataTableToJsonObj(dtStcokResult, total, total));
                }
                if (clientType_con == 2)
                {

                    DataTable dtStcokResult = Stock.GetStockImportactivity(displayLength, displayStart, sortCol, sortDir, search, SessionManager.ClientID ?? 0, clientType_con, SelectedclientId, SessionManager.LoginID ?? 0);
                    int total = Convert.ToInt32(dtStcokResult.Rows.Count == 0 ? "0" : dtStcokResult.Rows[0]["totalcount"].ToString());
                    context.Response.Write(DataTableToJsonObj(dtStcokResult, total, total));
                }
                if (clientType_con == 3)
                {

                    DataTable dtStcokResult = Stock.GetStockImportactivity(displayLength, displayStart, sortCol, sortDir, search, SessionManager.ClientID ?? 0, clientType_con, SelectedclientId, SessionManager.LoginID ?? 0);
                    int total = Convert.ToInt32(dtStcokResult.Rows.Count == 0 ? "0" : dtStcokResult.Rows[0]["totalcount"].ToString());
                    context.Response.Write(DataTableToJsonObj(dtStcokResult, total, total));
                }
            }
            catch (Exception ex)
            {
                new Errorlog().LogMessage("Error at method GetImportActivity : " + ex.Message);
                WriteError(ex);
            }
        }
        private void SaveImportActivity(HttpContext context)
        {


            int userid = 0;
            try
            {
                int clientType_con = string.IsNullOrEmpty(context.Request.QueryString["clientType"]) ? 0 : int.Parse(context.Request.QueryString["clientType"]);
                int SelectedclientId = string.IsNullOrEmpty(context.Request.QueryString["SelectedclientId"]) ? 0 : int.Parse(context.Request.QueryString["SelectedclientId"]);
                string Suppliertext = string.IsNullOrEmpty(context.Request.QueryString["SupplierNameText"]) ? "" : Convert.ToString((context.Request.QueryString["SupplierNameText"]));
                int SupplierId = string.IsNullOrEmpty(context.Request.QueryString["SupplierId"]) ? 0 : int.Parse(context.Request.QueryString["SupplierId"]);
                string Orignalfilename = string.IsNullOrEmpty(context.Request.QueryString["Orignalfilename"]) ? "" : Convert.ToString((context.Request.QueryString["Orignalfilename"]));
                int totalrowEffected = string.IsNullOrEmpty(context.Request.QueryString["totalrowEffected"]) ? 0 : int.Parse(context.Request.QueryString["totalrowEffected"]);
                int recordType = string.IsNullOrEmpty(context.Request.QueryString["recordType"]) ? 0 : int.Parse(context.Request.QueryString["recordType"]);

                string fileColName = recordType == 1 ? "Offer" : recordType == 2 ? "Excess" : "StockInfo";

                //string strSupplier = Suppliertext + "  " + SupplierId;
                string strSupplier = Suppliertext;
                strSupplier = strSupplier.Length > 50 ? strSupplier.Substring(0, 50) : strSupplier;
                userid = SessionManager.LoginID ?? 0;
                string strInsertSummary = "('" + strSupplier + "','" + Orignalfilename + "','" + totalrowEffected + "','" + fileColName + "','" + SelectedclientId + "','" + userid + "','" + clientType_con + "')";
                BLL.Stock.SaveImportActivity(strInsertSummary, clientType_con);


            }
            catch (Exception ex)
            {
                new Errorlog().LogMessage("Error at method SaveImportActivity : " + ex.Message);
                WriteError(ex);
            }


        }
        #endregion

    }
}
