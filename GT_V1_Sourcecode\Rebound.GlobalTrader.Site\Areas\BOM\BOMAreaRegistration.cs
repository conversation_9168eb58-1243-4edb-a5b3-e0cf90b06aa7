﻿using System.Web.Mvc;

namespace Rebound.GlobalTrader.Site.Areas.BOM
{
    public class BOMAreaRegistration : AreaRegistration 
    {
        public override string AreaName 
        {
            get 
            {
                return "BOM";
            }
        }

        public override void RegisterArea(AreaRegistrationContext context) 
        {
            context.MapRoute(
                "BOM_default",
                "BOM/{controller}/{action}/{id}",
                new { action = "Index", id = UrlParameter.Optional }
            );
        }
    }
}