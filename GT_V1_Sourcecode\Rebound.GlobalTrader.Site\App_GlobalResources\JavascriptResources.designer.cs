//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Resources {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option or rebuild the Visual Studio project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.VisualStudio.Web.Application.StronglyTypedResourceProxyBuilder", "********")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class JavascriptResources {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal JavascriptResources() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Resources.JavascriptResources", global::System.Reflection.Assembly.Load("App_GlobalResources"));
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to I have added a &lt;a href=&quot;[#HYPERLINK#]&quot;&gt;new Customer Requirement&lt;/a&gt;:
        ///
        ///Number: [#CUSREQ_NUMBER#]
        ///Customer: [#CUSTOMER#]
        ///Quantity: [#QUANTITY#]
        ///Part No: [#PART#]
        ///Cust Part No: [#CUSTOMERPART#]
        ///Manufacturer:	[#MANUFACTURER#]
        ///DateCode: [#DATECODE#]
        ///Product: [#PRODUCT#]
        ///Package: [#PACKAGE#]
        ///Target Price: [#PRICE#]
        ///Date Required: [#DATEREQUIRED#].
        /// </summary>
        internal static string AddNewCustomerRequirement {
            get {
                return ResourceManager.GetString("AddNewCustomerRequirement", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to An address with that type has already been entered.
        /// </summary>
        internal static string AddressTypeAlreadyEntered {
            get {
                return ResourceManager.GetString("AddressTypeAlreadyEntered", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Alternate part number already exist.
        /// </summary>
        internal static string AlternatePartMessage {
            get {
                return ResourceManager.GetString("AlternatePartMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, there was a problem.&lt;br /&gt;The following details have been noted in your Computer&apos;s event log and emailed to Rebound staff for attention..
        /// </summary>
        internal static string ApplicationError {
            get {
                return ResourceManager.GetString("ApplicationError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Rebound Global:Trader.
        /// </summary>
        internal static string AppTitle {
            get {
                return ResourceManager.GetString("AppTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You have selected AS9120 required. Please select a product source for each line to release this document.
        /// </summary>
        internal static string AS9120AllLineMessage {
            get {
                return ResourceManager.GetString("AS9120AllLineMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You have selected AS9120 required. Please select a product source for line to release this document.
        /// </summary>
        internal static string AS9120LineMessage {
            get {
                return ResourceManager.GetString("AS9120LineMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invoice value exceeded the credit limit.
        /// </summary>
        internal static string AvailableCreditBalanceToShipSO {
            get {
                return ResourceManager.GetString("AvailableCreditBalanceToShipSO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bank fee required if apply bank fee checked.
        /// </summary>
        internal static string BankFeeMessage {
            get {
                return ResourceManager.GetString("BankFeeMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PO bank fee required if apply po bank fee checked.
        /// </summary>
        internal static string BankPOFeeMessage {
            get {
                return ResourceManager.GetString("BankPOFeeMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to HUBRFQ notification sent successfully.
        /// </summary>
        internal static string BOMNotificationSentSuccess {
            get {
                return ResourceManager.GetString("BOMNotificationSentSuccess", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Your changes were saved successfully.
        /// </summary>
        internal static string ChangesSavedSuccessfully {
            get {
                return ResourceManager.GetString("ChangesSavedSuccessfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This will close the line.
        ///Any allocation will be deleted.
        ///The ordered line quantity will be set to the received quantity.
        ///Please ensure all related documentation is dealt with..
        /// </summary>
        internal static string CloseCRMALine {
            get {
                return ResourceManager.GetString("CloseCRMALine", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This will close the header and all lines.
        ///All allocations will be deleted.
        ///All ordered line quantities will be set to the received quantity.
        ///The related Sales Order will not be deleted.
        ///Please ensure all related documentation is dealt with..
        /// </summary>
        internal static string ClosePO {
            get {
                return ResourceManager.GetString("ClosePO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This will close the line.
        ///Any allocation will be deleted.
        ///The ordered line quantity will be set to the received quantity.
        ///The related Sales Order line will not be deleted.
        ///Please ensure all related documentation is dealt with..
        /// </summary>
        internal static string ClosePOLine {
            get {
                return ResourceManager.GetString("ClosePOLine", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This will close the header and all lines.
        ///All allocations will be deleted.
        ///The related Purchase Order will not be deleted.
        ///Please ensure all related documentation is dealt with..
        /// </summary>
        internal static string CloseSO {
            get {
                return ResourceManager.GetString("CloseSO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This will close the header and all lines.
        ///All line quantities will be reset to as much as has been shipped.
        ///All allocations will be deleted.
        ///The related Purchase Order will not be deleted.
        ///Please ensure all related documentation is dealt with..
        /// </summary>
        internal static string CloseSOAndResetLines {
            get {
                return ResourceManager.GetString("CloseSOAndResetLines", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This will close the line.
        ///Any allocation will be deleted.
        ///The ordered line quantity can be set to the shipped quantity if indicated.
        ///The related Purchase Order line will not be deleted.
        ///Please ensure all related documentation is dealt with..
        /// </summary>
        internal static string CloseSOLine {
            get {
                return ResourceManager.GetString("CloseSOLine", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to COMPANY.
        /// </summary>
        internal static string COMPANY {
            get {
                return ResourceManager.GetString("COMPANY", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Company has been approved.
        /// </summary>
        internal static string CompanyApproveSubject {
            get {
                return ResourceManager.GetString("CompanyApproveSubject", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The quantity ordered is greater than the quantity required.
        ///Do you want to retain this excessive quantity?.
        /// </summary>
        internal static string ConfirmQuantityOrderedGreaterThanRequired {
            get {
                return ResourceManager.GetString("ConfirmQuantityOrderedGreaterThanRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Click OK button to email consolidate sales order on fields Price,Part,Manufacturer,Package,Product,
        ///DatePromised,CustomerPart,Date Code,ROHS and ProductSource
        ///Click Cancel to email sales order without consolidate.
        /// </summary>
        internal static string ConsolidateMailSOConfirmMSG {
            get {
                return ResourceManager.GetString("ConsolidateMailSOConfirmMSG", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Click OK button to print consolidate sales order on fields Price,Part,Manufacturer,Package,Product,
        ///DatePromised,CustomerPart,Date Code,ROHS and ProductSource
        ///Click Cancel to print sales order without consolidate.
        /// </summary>
        internal static string ConsolidateSOConfirmMSG {
            get {
                return ResourceManager.GetString("ConsolidateSOConfirmMSG", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Email address required if finance contact checked.
        /// </summary>
        internal static string ContactEmailMessage {
            get {
                return ResourceManager.GetString("ContactEmailMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Contacts for {0}.
        /// </summary>
        internal static string ContactsForCompany {
            get {
                return ResourceManager.GetString("ContactsForCompany", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Finance contact is not found in the following credit notes:.
        /// </summary>
        internal static string CreditFinanceContactNotFoundMessage {
            get {
                return ResourceManager.GetString("CreditFinanceContactNotFoundMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Freight should be less than credit balance of the company.
        /// </summary>
        internal static string CreditLimitMessage {
            get {
                return ResourceManager.GetString("CreditLimitMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Selected Credit Note is in progress to send..
        /// </summary>
        internal static string CreditProgressMessage {
            get {
                return ResourceManager.GetString("CreditProgressMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer/Supplier name cannot exceed more than 30 characters. Please edit the customer/supplier name manually to proceed..
        /// </summary>
        internal static string CustomerMessage {
            get {
                return ResourceManager.GetString("CustomerMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to If approved Customer No. required.
        /// </summary>
        internal static string CustomerNoMessage {
            get {
                return ResourceManager.GetString("CustomerNoMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer Part cannot exceed more than 30 characters. Please edit the Customer Part manually to proceed..
        /// </summary>
        internal static string CustomerPart {
            get {
                return ResourceManager.GetString("CustomerPart", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to There was a problem with the database.
        /// </summary>
        internal static string DatabaseError {
            get {
                return ResourceManager.GetString("DatabaseError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, the database call has timed out.
        /// </summary>
        internal static string DatabaseTimeout {
            get {
                return ResourceManager.GetString("DatabaseTimeout", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error: No data found.
        /// </summary>
        internal static string DataNotFound {
            get {
                return ResourceManager.GetString("DataNotFound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date cannot exceed more than 10 characters. Please edit the date manually to proceed..
        /// </summary>
        internal static string Date {
            get {
                return ResourceManager.GetString("Date", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date Code cannot exceed more than 9 characters. Please edit the date code manually to proceed..
        /// </summary>
        internal static string DateCode {
            get {
                return ResourceManager.GetString("DateCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter a date in the future.
        /// </summary>
        internal static string DateMustBeInFuture {
            get {
                return ResourceManager.GetString("DateMustBeInFuture", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date picked cannot exceed more than 8 characters. Please edit the date picked manually to proceed..
        /// </summary>
        internal static string DatePicked {
            get {
                return ResourceManager.GetString("DatePicked", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter a date and time in the future.
        /// </summary>
        internal static string DateTimeMustBeInFuture {
            get {
                return ResourceManager.GetString("DateTimeMustBeInFuture", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Finance contact is not found in the following debit notes:.
        /// </summary>
        internal static string DebitFinanceContactNotFoundMessage {
            get {
                return ResourceManager.GetString("DebitFinanceContactNotFoundMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Debit Note Ref should be less than 16 characters.
        /// </summary>
        internal static string DebitNoteRefMessage {
            get {
                return ResourceManager.GetString("DebitNoteRefMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Selected Debit Note is in progress to send..
        /// </summary>
        internal static string DebitProgressMessage {
            get {
                return ResourceManager.GetString("DebitProgressMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to DIVISION.
        /// </summary>
        internal static string DIVISION {
            get {
                return ResourceManager.GetString("DIVISION", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, that document cannot be found or you do not have the permissions to see it..
        /// </summary>
        internal static string Document {
            get {
                return ResourceManager.GetString("Document", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to A currency with that code already exists on the database..
        /// </summary>
        internal static string DuplicateCurrencyCode {
            get {
                return ResourceManager.GetString("DuplicateCurrencyCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The Login Name you entered has already been taken.
        /// </summary>
        internal static string DuplicateLoginName {
            get {
                return ResourceManager.GetString("DuplicateLoginName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ERAI Reported.
        /// </summary>
        internal static string EARIReported {
            get {
                return ResourceManager.GetString("EARIReported", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter a valid email address.
        /// </summary>
        internal static string EmailInvalidMessage {
            get {
                return ResourceManager.GetString("EmailInvalidMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to To.
        /// </summary>
        internal static string EmailTo {
            get {
                return ResourceManager.GetString("EmailTo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter a value.
        /// </summary>
        internal static string EnterFieldGeneric {
            get {
                return ResourceManager.GetString("EnterFieldGeneric", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error.
        /// </summary>
        internal static string Error {
            get {
                return ResourceManager.GetString("Error", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Your feedback has been sent to Rebound, thank you for your time.&lt;br/&gt;Press &lt;b&gt;Continue&lt;/b&gt; to return to your previous page..
        /// </summary>
        internal static string FeedbackSent {
            get {
                return ResourceManager.GetString("FeedbackSent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The file upload failed.
        /// </summary>
        internal static string FileUploadFailed {
            get {
                return ResourceManager.GetString("FileUploadFailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The file you have selected is not an allowed type.
        /// </summary>
        internal static string FileUploadNotAllowedType {
            get {
                return ResourceManager.GetString("FileUploadNotAllowedType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Finance contact is not found in the following invoices: .
        /// </summary>
        internal static string FinanceContactNotFoundMessage {
            get {
                return ResourceManager.GetString("FinanceContactNotFoundMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Your new folder has been added.
        /// </summary>
        internal static string FolderAdded {
            get {
                return ResourceManager.GetString("FolderAdded", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Your folder has been deleted.
        /// </summary>
        internal static string FolderDeleted {
            get {
                return ResourceManager.GetString("FolderDeleted", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Freight charge and shipping cost changed.
        ///Related Division header, Tax and Incoterms also changed..
        /// </summary>
        internal static string FreightChargeAndShippingChanged {
            get {
                return ResourceManager.GetString("FreightChargeAndShippingChanged", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Freight charge will be left as is..
        /// </summary>
        internal static string FreightChargeLeft {
            get {
                return ResourceManager.GetString("FreightChargeLeft", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Freight charge will be changed. .
        /// </summary>
        internal static string FreightChargeWillBeChanged {
            get {
                return ResourceManager.GetString("FreightChargeWillBeChanged", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to GIN cannot exceed more than 9 characters. Please edit the GIN manually to proceed..
        /// </summary>
        internal static string GIN {
            get {
                return ResourceManager.GetString("GIN", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invoice Amount value does not match with the selected GI value and Ship value, do you want to continue saving..
        /// </summary>
        internal static string GIPluseShipValueMessage {
            get {
                return ResourceManager.GetString("GIPluseShipValueMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to GoodsInNo cannot exceed more than 8 characters. Please edit the GoodsInNo manually to proceed..
        /// </summary>
        internal static string GoodsInNo {
            get {
                return ResourceManager.GetString("GoodsInNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This product may have a dangerous Goods classification when transported. The consignment may need to be accompanied by transport documents, declaring the description and nature of the goods.
        /// </summary>
        internal static string HazardousMessage {
            get {
                return ResourceManager.GetString("HazardousMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please click on refresh button to view record.
        /// </summary>
        internal static string HistoryMessage {
            get {
                return ResourceManager.GetString("HistoryMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select hold reason.
        /// </summary>
        internal static string HoldReasonMessage {
            get {
                return ResourceManager.GetString("HoldReasonMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Selected HUBRFQ has been assigned.
        /// </summary>
        internal static string HUBRFQAssigned {
            get {
                return ResourceManager.GetString("HUBRFQAssigned", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Imp !.
        /// </summary>
        internal static string Important {
            get {
                return ResourceManager.GetString("Important", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Inactive.
        /// </summary>
        internal static string Inactive {
            get {
                return ResourceManager.GetString("Inactive", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Inbox.
        /// </summary>
        internal static string Inbox {
            get {
                return ResourceManager.GetString("Inbox", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Inspected by cannot exceed more than 12 characters. Please edit the inspected by manually to proceed..
        /// </summary>
        internal static string InspectedBy {
            get {
                return ResourceManager.GetString("InspectedBy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select a Insured  amount currency.
        /// </summary>
        internal static string InsuredCurrencyAmount {
            get {
                return ResourceManager.GetString("InsuredCurrencyAmount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invoice amount is not correct.
        /// </summary>
        internal static string InvoiceAmountMessage {
            get {
                return ResourceManager.GetString("InvoiceAmountMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invoice Amount value not equal to Sum of : Goods Value , Tax , Delivery Charge , Bank Fee , Credit Card Fee , do you want to continue!.
        /// </summary>
        internal static string InvoiceAmountnotEqualToGoodsValueMessage {
            get {
                return ResourceManager.GetString("InvoiceAmountnotEqualToGoodsValueMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invoice Amount value not equal to Sum of : Goods Value, Tax, Delivery Charge, Bank Fee. Do you want to continue!.
        /// </summary>
        internal static string InvoiceAmountnotEqualToGoodsValueMessageV1 {
            get {
                return ResourceManager.GetString("InvoiceAmountnotEqualToGoodsValueMessageV1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Selected Invoices is in progress to send. You can check the status under Report =&gt; Invoicing =&gt; Invoices.
        /// </summary>
        internal static string InvoiceProgressMessage {
            get {
                return ResourceManager.GetString("InvoiceProgressMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You are trying to allocate a non IPO stock line to an IPO sales order. If you continue, this sales order line will be marked as a non IPO sales order line which cannot be undone. Would you like to proceed ?.
        /// </summary>
        internal static string IPOAllocateMessage {
            get {
                return ResourceManager.GetString("IPOAllocateMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Thank you for your order. Please note your order will not be processed until your Sales Order is checked.
        /// </summary>
        internal static string IPOCreationMsg {
            get {
                return ResourceManager.GetString("IPOCreationMsg", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Item {0} of {1}.
        /// </summary>
        internal static string ItemXOfY {
            get {
                return ResourceManager.GetString("ItemXOfY", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Your label has been sent for printing.
        /// </summary>
        internal static string LabelPrintMessage {
            get {
                return ResourceManager.GetString("LabelPrintMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter a valid landed cost.
        /// </summary>
        internal static string landedCostValidation1 {
            get {
                return ResourceManager.GetString("landedCostValidation1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter or calculate new landed cost.
        /// </summary>
        internal static string landedCostValidation2 {
            get {
                return ResourceManager.GetString("landedCostValidation2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to New landed cost should be greater than or equal to 0.
        /// </summary>
        internal static string landedCostValidation3 {
            get {
                return ResourceManager.GetString("landedCostValidation3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to New landed cost should be less than current landed cost.
        /// </summary>
        internal static string landedCostValidation4 {
            get {
                return ResourceManager.GetString("landedCostValidation4", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Line {0} of {1}.
        /// </summary>
        internal static string LineXOfY {
            get {
                return ResourceManager.GetString("LineXOfY", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Loading.
        /// </summary>
        internal static string Loading {
            get {
                return ResourceManager.GetString("Loading", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Currency already added.
        /// </summary>
        internal static string LocalCurrencyMessage {
            get {
                return ResourceManager.GetString("LocalCurrencyMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Location cannot exceed more than 8 characters. Please edit the location manually to proceed..
        /// </summary>
        internal static string Location {
            get {
                return ResourceManager.GetString("Location", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please calculate new stock provision.
        /// </summary>
        internal static string LotStockProvisionMessage {
            get {
                return ResourceManager.GetString("LotStockProvisionMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Manufacturer cannot exceed more than 30 characters. Please edit the manufacturer manually to proceed..
        /// </summary>
        internal static string Manufacturer {
            get {
                return ResourceManager.GetString("Manufacturer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Your messages have been deleted.
        /// </summary>
        internal static string MessageDeleted {
            get {
                return ResourceManager.GetString("MessageDeleted", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Your messages have been moved.
        /// </summary>
        internal static string MessageMoved {
            get {
                return ResourceManager.GetString("MessageMoved", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Your message has been sent.
        /// </summary>
        internal static string MessageSent {
            get {
                return ResourceManager.GetString("MessageSent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to MSL Level cannot exceed more than 6 characters. Please edit the MSL Level manually to proceed..
        /// </summary>
        internal static string MSLLevel {
            get {
                return ResourceManager.GetString("MSLLevel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to MY.
        /// </summary>
        internal static string MY {
            get {
                return ResourceManager.GetString("MY", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Narrative should be less than 41 characters.
        /// </summary>
        internal static string NarrativeMessage {
            get {
                return ResourceManager.GetString("NarrativeMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to New Credit Note Added.
        /// </summary>
        internal static string NewCreditNoteAdded {
            get {
                return ResourceManager.GetString("NewCreditNoteAdded", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to New Customer Requirement Added.
        /// </summary>
        internal static string NewCustomerRequirementAdded {
            get {
                return ResourceManager.GetString("NewCustomerRequirementAdded", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to New Customer RMA Added.
        /// </summary>
        internal static string NewCustomerRMAAdded {
            get {
                return ResourceManager.GetString("NewCustomerRMAAdded", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to New Debit Note Added.
        /// </summary>
        internal static string NewDebitNoteAdded {
            get {
                return ResourceManager.GetString("NewDebitNoteAdded", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to New Goods In Note Added.
        /// </summary>
        internal static string NewGoodsInAdded {
            get {
                return ResourceManager.GetString("NewGoodsInAdded", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to New Invoice Added.
        /// </summary>
        internal static string NewInvoiceAdded {
            get {
                return ResourceManager.GetString("NewInvoiceAdded", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to New Purchase Order Added.
        /// </summary>
        internal static string NewPurchaseOrderAdded {
            get {
                return ResourceManager.GetString("NewPurchaseOrderAdded", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to New Quote Added.
        /// </summary>
        internal static string NewQuoteAdded {
            get {
                return ResourceManager.GetString("NewQuoteAdded", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to New Sales Order Added.
        /// </summary>
        internal static string NewSalesOrderAdded {
            get {
                return ResourceManager.GetString("NewSalesOrderAdded", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to New Supplier Invoice Added.
        /// </summary>
        internal static string NewSupplierInvoiceAdded {
            get {
                return ResourceManager.GetString("NewSupplierInvoiceAdded", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to New Supplier RMA Added.
        /// </summary>
        internal static string NewSupplierRMAAdded {
            get {
                return ResourceManager.GetString("NewSupplierRMAAdded", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No.
        /// </summary>
        internal static string No {
            get {
                return ResourceManager.GetString("No", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, no data was found.
        /// </summary>
        internal static string NoDataFound {
            get {
                return ResourceManager.GetString("NoDataFound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to None.
        /// </summary>
        internal static string None {
            get {
                return ResourceManager.GetString("None", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Non Preferred Source.
        /// </summary>
        internal static string NonPreferred {
            get {
                return ResourceManager.GetString("NonPreferred", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to EPR  {0} Notification.
        /// </summary>
        internal static string NotifyEPR {
            get {
                return ResourceManager.GetString("NotifyEPR", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Goods In Note {0} Notification.
        /// </summary>
        internal static string NotifyGoodsIn {
            get {
                return ResourceManager.GetString("NotifyGoodsIn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to NPR  {0} Notification.
        /// </summary>
        internal static string NotifyNPR {
            get {
                return ResourceManager.GetString("NotifyNPR", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Purchase Order {0} Notification.
        /// </summary>
        internal static string NotifyPurchaseOrder {
            get {
                return ResourceManager.GetString("NotifyPurchaseOrder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sales Order {0} Notification.
        /// </summary>
        internal static string NotifySalesOrder {
            get {
                return ResourceManager.GetString("NotifySalesOrder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier Invoice {0} Notification.
        /// </summary>
        internal static string NotifySupplierInvoice {
            get {
                return ResourceManager.GetString("NotifySupplierInvoice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to NPRNo cannot exceed more than 6 characters. Please edit the NPRNo manually to proceed..
        /// </summary>
        internal static string NPRNo {
            get {
                return ResourceManager.GetString("NPRNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter a valid numeric value.
        /// </summary>
        internal static string NumericFieldError {
            get {
                return ResourceManager.GetString("NumericFieldError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter a numeric value greater than {0}.
        /// </summary>
        internal static string NumericFieldGreaterThanError {
            get {
                return ResourceManager.GetString("NumericFieldGreaterThanError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter a numeric value greater than or equal to {0}.
        /// </summary>
        internal static string NumericFieldGreaterThanOrEqualToError {
            get {
                return ResourceManager.GetString("NumericFieldGreaterThanOrEqualToError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter a numeric value less than {0}.
        /// </summary>
        internal static string NumericFieldLessThanError {
            get {
                return ResourceManager.GetString("NumericFieldLessThanError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter a numeric value less than or equal to {0}.
        /// </summary>
        internal static string NumericFieldLessThanOrEqualToError {
            get {
                return ResourceManager.GetString("NumericFieldLessThanOrEqualToError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The password you entered is incorrect..
        /// </summary>
        internal static string OldPasswordIncorrect {
            get {
                return ResourceManager.GetString("OldPasswordIncorrect", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to On Stop.
        /// </summary>
        internal static string OnStop {
            get {
                return ResourceManager.GetString("OnStop", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This product is known to have potential purchasing risks associated to it and as such you are requested to purchase directly from the IPO team only..
        /// </summary>
        internal static string OrderViaIPOonlyMessage {
            get {
                return ResourceManager.GetString("OrderViaIPOonlyMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The total of open orders takes the company over its credit limit, this Sales Order cannot be authorised.
        /// </summary>
        internal static string OverCreditLimitOfCompanyMessage {
            get {
                return ResourceManager.GetString("OverCreditLimitOfCompanyMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Overshipment(s) cancelled.
        /// </summary>
        internal static string OverShipmentCancelled {
            get {
                return ResourceManager.GetString("OverShipmentCancelled", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Overshipment(s) confirmed.
        /// </summary>
        internal static string OverShipmentConfirmed {
            get {
                return ResourceManager.GetString("OverShipmentConfirmed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to A quantity shipped is greater than the quantity ordered.
        ///Press OK to accept the overshipment or Cancel to reset the quantity to the amount ordered..
        /// </summary>
        internal static string OverShipmentMessage {
            get {
                return ResourceManager.GetString("OverShipmentMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Part number cannot exceed more than 30 characters. Please edit the part number manually to proceed..
        /// </summary>
        internal static string Part {
            get {
                return ResourceManager.GetString("Part", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Price Request notification sent successfully.
        /// </summary>
        internal static string PONotificationSentSuccess {
            get {
                return ResourceManager.GetString("PONotificationSentSuccess", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You are no longer logged in, this window will close.
        /// </summary>
        internal static string PopupLoggedOut {
            get {
                return ResourceManager.GetString("PopupLoggedOut", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select product source.
        /// </summary>
        internal static string ProductSourceMessage {
            get {
                return ResourceManager.GetString("ProductSourceMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quality approval already exist.
        /// </summary>
        internal static string QualityApproval {
            get {
                return ResourceManager.GetString("QualityApproval", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} allocated, {1} in stock.
        /// </summary>
        internal static string QuantitiesAllocatedAndInStock {
            get {
                return ResourceManager.GetString("QuantitiesAllocatedAndInStock", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quantity cannot exceed more than 10 characters. Please edit the quantity manually to proceed..
        /// </summary>
        internal static string Quantity {
            get {
                return ResourceManager.GetString("Quantity", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} allocated.
        /// </summary>
        internal static string QuantityAllocated {
            get {
                return ResourceManager.GetString("QuantityAllocated", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quantity ordered will be as entered..
        /// </summary>
        internal static string QuantityOrderedAsEntered {
            get {
                return ResourceManager.GetString("QuantityOrderedAsEntered", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quantity ordered has been re-set to the original value..
        /// </summary>
        internal static string QuantityOrderedResetToOriginal {
            get {
                return ResourceManager.GetString("QuantityOrderedResetToOriginal", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Some lines missing MSL please check and try again.
        /// </summary>
        internal static string QuoteAllMSL {
            get {
                return ResourceManager.GetString("QuoteAllMSL", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Some lines missing the product type please check and try again.
        /// </summary>
        internal static string QuoteAllProduct {
            get {
                return ResourceManager.GetString("QuoteAllProduct", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quoted {0}.
        /// </summary>
        internal static string QuotedValue {
            get {
                return ResourceManager.GetString("QuotedValue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please set the Reminder Date to be 3 days after the Offered Date for pending Offered Quotes.
        /// </summary>
        internal static string QuoteMinReminderDate {
            get {
                return ResourceManager.GetString("QuoteMinReminderDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select a product type.
        /// </summary>
        internal static string QuoteProduct {
            get {
                return ResourceManager.GetString("QuoteProduct", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Received Purchase Order.
        /// </summary>
        internal static string ReceivedPurchaseOrder {
            get {
                return ResourceManager.GetString("ReceivedPurchaseOrder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to HUBRFQ has been sent for Price Request successfully..
        /// </summary>
        internal static string RequestToPurchaseHubSavedSuccessfully {
            get {
                return ResourceManager.GetString("RequestToPurchaseHubSavedSuccessfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter a value.
        /// </summary>
        internal static string RequiredFieldMissingMessage {
            get {
                return ResourceManager.GetString("RequiredFieldMissingMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select Reason1 Value..
        /// </summary>
        internal static string ResReason1Value {
            get {
                return ResourceManager.GetString("ResReason1Value", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The Administrators Group must retain at least one member..
        /// </summary>
        internal static string RetainOneAdmin {
            get {
                return ResourceManager.GetString("RetainOneAdmin", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ROHS Status cannot exceed more than 14 characters. Please edit the ROHS Status manually to proceed..
        /// </summary>
        internal static string ROHS {
            get {
                return ResourceManager.GetString("ROHS", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ROHS2.
        /// </summary>
        internal static string ROHS2 {
            get {
                return ResourceManager.GetString("ROHS2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to RoHS 5/6.
        /// </summary>
        internal static string ROHS56 {
            get {
                return ResourceManager.GetString("ROHS56", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to RoHS 6/6.
        /// </summary>
        internal static string ROHS66 {
            get {
                return ResourceManager.GetString("ROHS66", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to RoHS Compliant.
        /// </summary>
        internal static string ROHSCompliant {
            get {
                return ResourceManager.GetString("ROHSCompliant", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to RoHS Exempt.
        /// </summary>
        internal static string ROHSExempt {
            get {
                return ResourceManager.GetString("ROHSExempt", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to RoHS Non-compliant.
        /// </summary>
        internal static string ROHSNonCompliant {
            get {
                return ResourceManager.GetString("ROHSNonCompliant", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to RoHS Not Applicable.
        /// </summary>
        internal static string ROHSNotApplicable {
            get {
                return ResourceManager.GetString("ROHSNotApplicable", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to RoHS Unknown.
        /// </summary>
        internal static string ROHSUnknown {
            get {
                return ResourceManager.GetString("ROHSUnknown", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select additional salesperson.
        /// </summary>
        internal static string Salesman2Message {
            get {
                return ResourceManager.GetString("Salesman2Message", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter additional salesperson %.
        /// </summary>
        internal static string Salesman2PercentMessage {
            get {
                return ResourceManager.GetString("Salesman2PercentMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SalesOrderNo cannot exceed more than 6 characters. Please edit the sales order number manually to proceed..
        /// </summary>
        internal static string SalesOrder {
            get {
                return ResourceManager.GetString("SalesOrder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Salesperson and Additional Salesperson can not be same.
        /// </summary>
        internal static string SalesPersonCompare {
            get {
                return ResourceManager.GetString("SalesPersonCompare", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Saving.
        /// </summary>
        internal static string Saving {
            get {
                return ResourceManager.GetString("Saving", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Your search was cancelled.
        /// </summary>
        internal static string SearchCancelled {
            get {
                return ResourceManager.GetString("SearchCancelled", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SecondRef should be less than 16 characters.
        /// </summary>
        internal static string SecondRefMessage {
            get {
                return ResourceManager.GetString("SecondRefMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SECTION NAME.
        /// </summary>
        internal static string SECTION_NAME {
            get {
                return ResourceManager.GetString("SECTION_NAME", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sending.
        /// </summary>
        internal static string Sending {
            get {
                return ResourceManager.GetString("Sending", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sent.
        /// </summary>
        internal static string SentMessages {
            get {
                return ResourceManager.GetString("SentMessages", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cannot insert Serial Numbers beyond Quantity limit.
        /// </summary>
        internal static string SerialNoLimit {
            get {
                return ResourceManager.GetString("SerialNoLimit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Freight charge and shipping cost changed.
        ///Related Division header, Tax and Incoterms also changed..
        /// </summary>
        internal static string ShipFreightChargeAndShippingChanged {
            get {
                return ResourceManager.GetString("ShipFreightChargeAndShippingChanged", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Freight charge will be changed..
        ///Related Division header, Tax and Incoterms also changed..
        /// </summary>
        internal static string ShipFreightChargeWillBeChanged {
            get {
                return ResourceManager.GetString("ShipFreightChargeWillBeChanged", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ship-In cost changed..
        /// </summary>
        internal static string ShipInCostChanged {
            get {
                return ResourceManager.GetString("ShipInCostChanged", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Order quantity cannot be less than shipped quantity.
        /// </summary>
        internal static string ShippedQuantityMessage {
            get {
                return ResourceManager.GetString("ShippedQuantityMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shipping normally waived for this Customer.
        /// </summary>
        internal static string ShippingWaived {
            get {
                return ResourceManager.GetString("ShippingWaived", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This line cannot be released until all serial numbers are recorded.
        /// </summary>
        internal static string ShowInspect {
            get {
                return ResourceManager.GetString("ShowInspect", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Press OK to set the default freight charge for this new shipping method or press Cancel to leave the freight charge field empty..
        /// </summary>
        internal static string SOFreightChargeOption {
            get {
                return ResourceManager.GetString("SOFreightChargeOption", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Finish.
        /// </summary>
        internal static string SourcingFinish {
            get {
                return ResourceManager.GetString("SourcingFinish", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sourcing.
        /// </summary>
        internal static string SourcingNuggetTitle {
            get {
                return ResourceManager.GetString("SourcingNuggetTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Further 6 Months.
        /// </summary>
        internal static string SourcingSixMonth {
            get {
                return ResourceManager.GetString("SourcingSixMonth", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to More than 12 Months.
        /// </summary>
        internal static string SourcingTwelveMonth {
            get {
                return ResourceManager.GetString("SourcingTwelveMonth", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Document.
        /// </summary>
        internal static string String {
            get {
                return ResourceManager.GetString("String", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dear Supplier
        ///Please find attached a copy of Rebound terms and conditions and ensure that all online orders are processed in accordance with them.
        ///
        ///Kind regards.
        /// </summary>
        internal static string SUpplierApprovalMailMessage {
            get {
                return ResourceManager.GetString("SUpplierApprovalMailMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Terms And Conditions.
        /// </summary>
        internal static string SUpplierApprovalMailSubject {
            get {
                return ResourceManager.GetString("SUpplierApprovalMailSubject", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please provide supplier code.
        /// </summary>
        internal static string SupplierCodeMessage {
            get {
                return ResourceManager.GetString("SupplierCodeMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select atleast one line.
        /// </summary>
        internal static string SupplierInvoiceLineAddMessage {
            get {
                return ResourceManager.GetString("SupplierInvoiceLineAddMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter supplier warranty.
        /// </summary>
        internal static string SupplierWarranty {
            get {
                return ResourceManager.GetString("SupplierWarranty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tax value does not match with the Tax type selected, do you want to continue saving..
        /// </summary>
        internal static string TaxValueMessage {
            get {
                return ResourceManager.GetString("TaxValueMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to TEAM.
        /// </summary>
        internal static string TEAM {
            get {
                return ResourceManager.GetString("TEAM", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Today.
        /// </summary>
        internal static string Today {
            get {
                return ResourceManager.GetString("Today", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You have allocated more stock than is available.
        /// </summary>
        internal static string TooMuchStockAllocated {
            get {
                return ResourceManager.GetString("TooMuchStockAllocated", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Traceable Source.
        /// </summary>
        internal static string Traceable {
            get {
                return ResourceManager.GetString("Traceable", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Trusted Source.
        /// </summary>
        internal static string Trusted {
            get {
                return ResourceManager.GetString("Trusted", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unfiltered.
        /// </summary>
        internal static string Unfiltered {
            get {
                return ResourceManager.GetString("Unfiltered", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to kg.
        /// </summary>
        internal static string Units_Kg {
            get {
                return ResourceManager.GetString("Units_Kg", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to lb.
        /// </summary>
        internal static string Units_Pounds {
            get {
                return ResourceManager.GetString("Units_Pounds", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter a valid web address.
        /// </summary>
        internal static string URLInvalidMessage {
            get {
                return ResourceManager.GetString("URLInvalidMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yes.
        /// </summary>
        internal static string Yes {
            get {
                return ResourceManager.GetString("Yes", resourceCulture);
            }
        }
    }
}
