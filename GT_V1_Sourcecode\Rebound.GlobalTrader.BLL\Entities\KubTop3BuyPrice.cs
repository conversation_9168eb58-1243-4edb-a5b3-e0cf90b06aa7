﻿using Rebound.GlobalTrader.DAL.SQLClient;
using Rebound.GlobalTrader.DAL;
using System;
using System.Collections.Generic;

namespace Rebound.GlobalTrader.BLL
{
    //Anuj
    public partial class KubTop3BuyPrice : BizObject
    {
        #region Properties
        public System.String POID { get; set; }
        public System.String PONo { get; set; }
        public System.String Date { get; set; }
        public System.String Price { get; set; }
        public System.Boolean? IsClientPrice { get; set; }
        #endregion


        #region Methods
        /// <summary>
        /// getKubPO Details
        /// Calls [sp_Kub]
        /// </summary>
        public static List<KubTop3BuyPrice> GetKubTop3BuyPriceDetails(System.String PartNo, System.Int32 ClientID)
        {
            List<KubTop3BuyPrice> lts = new List<KubTop3BuyPrice>();
            List<KubTop3BuyPriceDetails> lstkubPODetails = new List<KubTop3BuyPriceDetails>();
            SQLKubProvider objSQLKubProvider = new SQLKubProvider();
            try
            {
                lstkubPODetails = objSQLKubProvider.ListKubTop3BuyPriceDetails(PartNo, ClientID);
                if (lstkubPODetails == null)
                {
                    return new List<KubTop3BuyPrice>();
                }
                else
                {
                    foreach (KubTop3BuyPriceDetails objDetails in lstkubPODetails)
                    {
                        KubTop3BuyPrice obj = new KubTop3BuyPrice
                        {
                            POID = objDetails.POID,
                            PONo = objDetails.PONo,
                            Date = objDetails.Date,
                            Price = objDetails.Price,
                            IsClientPrice = objDetails.IsClientPrice
                        };
                        lts.Add(obj);
                    }
                }
                return lts;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        public static List<KubTop3BuyPrice> GetKubTop3BuyPriceDetailsForHUB(System.String PartNo, System.Int32 ClientID)
        {
            List<KubTop3BuyPrice> lts = new List<KubTop3BuyPrice>();
            List<KubTop3BuyPriceDetails> lstkubPODetails = new List<KubTop3BuyPriceDetails>();
            SQLKubProvider objSQLKubProvider = new SQLKubProvider();
            try
            {
                lstkubPODetails = objSQLKubProvider.ListKubTop3BuyPriceDetailsForHUB(PartNo, ClientID);
                if (lstkubPODetails == null)
                {
                    return new List<KubTop3BuyPrice>();
                }
                else
                {
                    foreach (KubTop3BuyPriceDetails objDetails in lstkubPODetails)
                    {
                        KubTop3BuyPrice obj = new KubTop3BuyPrice
                        {
                            POID = objDetails.POID,
                            PONo = objDetails.PONo,
                            Date = objDetails.Date,
                            Price = objDetails.Price,
                            IsClientPrice = objDetails.IsClientPrice
                        };
                        lts.Add(obj);
                    }
                }
                return lts;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        #endregion
    }
}
