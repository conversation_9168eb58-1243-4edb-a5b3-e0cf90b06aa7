Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.GILines_AddShortShipment=function(n){Rebound.GlobalTrader.Site.Controls.Forms.GILines_AddShortShipment.initializeBase(this,[n]);this._intGIID=0;this._intLineID=-1;this._intPOQuantityOrdered=0;this._intGlobalClientNo=-1;this._intSupplierNo=-1;this._intBuyerNo=-1;this._intManufacturerNo=-1;this._intSalesPersonId=-1;this._intRaisedById=-1;this._IsPoHubLogin=0;this._intInternalPurchaseOrderId=0;this._intIPOSupplier=0;this._IPOSupplierName="";this._FirstLineCustomer="";this._PurchaseOrderNo=-1;this._intSupportTeamMemberNo=-1};Rebound.GlobalTrader.Site.Controls.Forms.GILines_AddShortShipment.prototype={get_intGIID:function(){return this._intGIID},set_intGIID:function(n){this._intGIID!==n&&(this._intGIID=n)},get_intPOQuantityOrdered:function(){return this._intPOQuantityOrdered},set_intPOQuantityOrdered:function(n){this._intPOQuantityOrdered!==n&&(this._intPOQuantityOrdered=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Forms.GILines_AddShortShipment.callBaseMethod(this,"initialize");this.addShown(Function.createDelegate(this,this.formShown));this.addSave(Function.createDelegate(this,this.saveClicked))},formShown:function(){this.storeOriginalFieldValues()},dispose:function(){this.isDisposed||(this._intGIID=null,this._intPOQuantityOrdered=null,this._intSupplierNo=null,this._intBuyerNo=null,this._intManufacturerNo=null,this._intSalesPersonId=null,this._intRaisedById=null,this._IsPoHubLogin=null,this._intInternalPurchaseOrderId=null,this._intIPOSupplier=null,this._IPOSupplierName=null,Rebound.GlobalTrader.Site.Controls.Forms.GILines_AddShortShipment.callBaseMethod(this,"dispose"))},saveClicked:function(){if(this.validateForm()){var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/GILines");n.set_DataObject("GILines");n.set_DataAction("SaveShortShipment");n.addParameter("id",this._intLineID);n.addParameter("PurchaseOrderNo",this._PurchaseOrderNo);n.addParameter("SalesContact",this._intSalesPersonId);n.addParameter("Reference",this.getFieldValue("ctlReferenceDetail"));n.addParameter("GoodsIn",this._intGIID);n.addParameter("ReceivedDate",this.getFieldValue("ctlReceivedDate"));n.addParameter("RaisedBy",this._intRaisedById);n.addParameter("Buyer",this._intBuyerNo);n.addParameter("PartNo",this.getFieldValue("ctlPartNo"));n.addParameter("Manufacturer",this._intManufacturerNo);n.addParameter("QuantityOrder",this.getFieldValue("ctlQuantityOrder"));n.addParameter("QuantityAdvised",this.getFieldValue("ctlQuantityAdvised"));n.addParameter("QuantityReceived",this.getFieldValue("ctlQuantityReceived"));n.addParameter("ShortageQuantity",this.getFieldValue("ctlShortageQuantity"));n.addParameter("ShortageValue",this.getFieldValue("ctlShortageValue"));n.addParameter("GoodsInNo",this.getFieldValue("ctlGoodsIn"));n.addParameter("PONumber",this.getFieldValue("ctlPurchaseOrder"));this._IsPoHubLogin==0&&this._intInternalPurchaseOrderId>0?(n.addParameter("IsPoHub",!0),n.addParameter("Supplier",this._intIPOSupplier)):(n.addParameter("IsPoHub",!1),n.addParameter("Supplier",this._intSupplierNo));n.addParameter("Buyername",this.getFieldValue("ctlBuyer"));n.addParameter("Suppliername",this.getFieldValue("ctlSupplier"));n.addParameter("Customername",this._FirstLineCustomer);n.addParameter("SupportTeamMemberNo",this._intSupportTeamMemberNo);n.addDataOK(Function.createDelegate(this,this.saveShortShipmentComplete));n.addError(Function.createDelegate(this,this.saveShortShipmentError));n.addTimeout(Function.createDelegate(this,this.saveShortShipmentError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null}},saveShortShipmentError:function(n){this._strErrorMessage=n._errorMessage;this.onSaveError()},saveShortShipmentComplete:function(n){if(n._result.Result==!0)this.onSaveComplete();else{var t=n._result.Message==""?n._errorMessage:n._result.Message;return this.showMessage(!0,t),!1}},validateForm:function(){this.onValidate();return this.autoValidateFields()},showMessage:function(n,t){$R_FN.showElement(this._pnlValidateError,n);n&&($R_FN.showElement(this._pnlSaving,!1),$R_FN.showElement(this._pnlSavedOK,!1),$R_FN.showElement(this._pnlLoading,!1),$R_FN.showElement(this._pnlContentInner,!0),$R_FN.showElement(this._pnlLinksHolder,!0),$R_FN.showElement(this._pnlFooterLinksHolder,!0),this._ctlRelatedNugget&&(this._ctlRelatedNugget.control.showLoading(!1),this._ctlRelatedNugget.control.showRefresh(!0)),t||(t=""),this._pnlValidateErrorText.innerHTML=t)}};Rebound.GlobalTrader.Site.Controls.Forms.GILines_AddShortShipment.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.GILines_AddShortShipment",Rebound.GlobalTrader.Site.Controls.Forms.Base,Sys.IDisposable);