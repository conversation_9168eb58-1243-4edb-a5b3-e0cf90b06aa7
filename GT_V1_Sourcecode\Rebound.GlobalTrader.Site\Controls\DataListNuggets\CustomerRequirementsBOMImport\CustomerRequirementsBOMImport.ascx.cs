//Marker     changed by      date         Remarks
//[001]      <PERSON><PERSON>   18-Mar-2019  Showing Records Processed and Records Remaining.

using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site.Controls;
using Rebound.GlobalTrader.Site.Controls.DropDowns;

namespace Rebound.GlobalTrader.Site.Controls.DataListNuggets {
    public partial class CustomerRequirementsBOMImport : Base
    {

        Combo _cmb;
        ContactsForCompany _ddlContact;
        
        #region Overrides

        /// <summary>
        /// OnInit
        /// </summary>
        /// <param name="e"></param>
        protected override void OnInit(EventArgs e) {
            SetDataListNuggetType("CustomerRequirementsBOMImport");
            base.OnInit(e);
            WireUpControls();
            TitleText = Functions.GetGlobalResource("Nuggets", "CustomerRequirementsBOMImport");
            AddScriptReference("Controls.DataListNuggets.CustomerRequirementsBOMImport.CustomerRequirementsBOMImport.js");
            SetupTable();
        }

        protected override void OnLoad(EventArgs e) {
            _scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.DataListNuggets.CustomerRequirementsBOMImport", ctlDesignBase.ClientID);
            _scScriptControlDescriptor.AddProperty("intCompanyID", _objQSManager.CompanyID);
            _scScriptControlDescriptor.AddProperty("strCompanyName",HttpContext.Current.Server.UrlDecode(_objQSManager.CompanyName));
           // _scScriptControlDescriptor.AddComponentProperty("ctlBOMName", _cmb.ClientID);
           // _scScriptControlDescriptor.AddComponentProperty("ddlContact", _ddlContact.ClientID);
            _scScriptControlDescriptor.AddProperty("intContactID", _objQSManager.ContactID);

			base.OnLoad(e);
        }
      

        protected override void RenderAdditionalState()
        {
            //string strViewLevel = this.GetSavedStateValue("ViewLevel");
            //if (!string.IsNullOrEmpty(strViewLevel))
            //{
            //    ((Pages.Content)Page).CurrentTab = Convert.ToInt32(strViewLevel);
            //    _enmViewLevel = (ViewLevelList)Convert.ToInt32(strViewLevel);
            //    this.OnAskPageToChangeTab();
            //}
            base.RenderAdditionalState();
        }

        #endregion

        private void SetupTable() {
            _tbl.AllowSelection = true;
            _tbl.AllowMultipleSelection = true;
            _tbl.SortColumnDirection = Rebound.GlobalTrader.Site.Enumerations.SortColumnDirection.DESC;
            _tbl.Columns.Add(new FlexiDataColumn("ClientBOMNo", "ClientBOMName"));
            _tbl.Columns.Add(new FlexiDataColumn("Company", "Contact", WidthManager.GetWidth(WidthManager.ColumnWidth.CompanyName), true));
            _tbl.Columns.Add(new FlexiDataColumn("HUBRFQName",   Unit.Pixel(150), true));
            _tbl.Columns.Add(new FlexiDataColumn("Salesman", WidthManager.GetWidth(WidthManager.ColumnWidth.ContactName), true));
            _tbl.Columns.Add(new FlexiDataColumn("ImportDate",  WidthManager.GetWidth(WidthManager.ColumnWidth.ImportDate), true));
            //_tbl.Columns.Add(new FlexiDataColumn("NoOfRequirement", WidthManager.GetWidth(WidthManager.ColumnWidth.NoOfRequirement), true));
            _tbl.Columns.Add(new FlexiDataColumn("RecordsProcessed", "RecordsRemaining", WidthManager.GetWidth(WidthManager.ColumnWidth.BOMRecordsRemProcess), true));
        }

        private void WireUpControls()
        {
            _cmb =(Combo) FindFilterControl("cmbCustomer");
            _ddlContact = (ContactsForCompany)FindFilterControl("ddlContact");

            
        }

    }
}