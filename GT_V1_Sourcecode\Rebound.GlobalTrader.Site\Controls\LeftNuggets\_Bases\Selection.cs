using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;

namespace Rebound.GlobalTrader.Site.Controls.LeftNuggets {

	public class Selection : Base {

		protected PlaceHolder _plhItems;

		protected override void OnInit(EventArgs e) {
			TitleResource = "Selection";
			IconCssType = "Selection";
			base.OnInit(e);
			_plhItems = (PlaceHolder)ctlDesignBase.FindContentControl("plhItems");
		}

		protected HtmlControl AddItem(String strPage, String strOverrideTitle) {
			Rebound.GlobalTrader.Site.SitePage pg = _objSite.GetPage(strPage);
			HtmlControl li = new HtmlGenericControl("li");
			HyperLink hyp = new HyperLink();
			hyp.NavigateUrl = pg.Url;
			if (string.IsNullOrEmpty(strOverrideTitle)) {
				hyp.Text = Functions.GetGlobalResource("PageTitles", pg.Name);
			} else {
				hyp.Text = strOverrideTitle;
			}
			li.Controls.Add(hyp);
			return li;
		}
		protected HtmlControl AddItem(String strPage) {
			return AddItem(strPage, "");
		}
		protected HtmlControl AddItem(BLL.SitePage.List enmPage, String strOverrideTitle) {
			return AddItem(enmPage.ToString(), strOverrideTitle);
		}
		protected HtmlControl AddItem(BLL.SitePage.List enmPage) {
			return AddItem(enmPage.ToString(), "");
		}

		protected HtmlControl AddHeading(string strHeading) {
			HtmlControl li = new HtmlGenericControl("li");
			li.Attributes["class"] = "heading";
			ControlBuilders.CreateLiteralInsideParent(li, strHeading);
			return li;
		}


	}
}