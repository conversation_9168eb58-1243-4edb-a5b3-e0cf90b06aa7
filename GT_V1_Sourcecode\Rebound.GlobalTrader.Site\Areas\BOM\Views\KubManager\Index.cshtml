﻿<!DOCTYPE html>

<html lang="en" xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta charset="utf-8" />
    <title>Kub Manager</title>
    <link href="~/Areas/BOM/css/libs/bootstrap.min.css" rel="stylesheet" />

    <script src="~/Areas/BOM/js/po-hub-libs/3.4.1-jquery.min.js"></script>
    <script src="~/Areas/BOM/js/po-hub-libs/jquery-2.2.4.min.js"></script>
    <script src="~/Areas/BOM/js/po-hub-libs/2.2.4-dist-jquery.js"></script>
    <script src="~/Areas/BOM/js/libs/bootstrap.min.js"></script>
    <script src="~/Areas/BOM/js/po-hub-libs/1.11.4-jquery-ui.min.js"></script>

    <link href="~/Areas/BOM/css/kub-libs/chatBot.css" rel="stylesheet" />
    <link href="~/Areas/BOM/css/bom-manager-kub.css" rel="stylesheet" />
</head>
<body style="background-color: transparent;">
    <div class="main_container">
        <!-- Chat bot UI start -->

        <div class="chat-screen" id="close_modal">
            <div class="chat-header">
                <div class="chat-header-title">
                    <span class="head_text">KUB Assistant</span>
                </div>
                <div class="close_icon" id="close_icon_model"> </div>
            </div>

            <div class="chat-body">
                <div class="table_content">
                    <table>
                        <thead>
                            <tr>
                                <th colspan="2">
                                    Part Number: <span id="spPartNo"> </span>
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td colspan="2" class="text_left"><label>Customer Quotes</label></td>
                            </tr>
                            <tr>
                                <td class="text_left">Latest Quote Price to Customer</td>
                                <td class="text_right"><span id="lblLatestQuotePrice"></span></td> <!--0.09240 GBP [0.09800 USD](07-11-2023) (3297018-hyperlink) -->
                            </tr>
                            <tr>
                                <td>
                                    <button class="accordion" onclick="return false;" id="btn10MostOffers">
                                        <span class="text_left" style=" width:51.4%; font-size:10px;">
                                            10 Most recent Quotes in the last 12 months (Rolling)
                                        </span>
                                    </button>
                                    
                                    <div class="panel">
                                        <table class="data_tb">
                                            <thead>
                                                <tr>
                                                    <th>Quote Number</th>
                                                    <th>Quantity</th>
                                                    <th>Offer Amount (Quote's Unit Price & Original HUB Offer)</th>
                                                    <th>Converted to SO?</th>
                                                </tr>
                                            </thead>
                                            <tbody id="tb10MostOffers" class="data-Breakdown-GI"></tbody>
                                        </table>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td class="text_left">Latest Offer shared by HUB</td>
                                <td class="text_right">
                                    <span id="lblLatestQuotePriceByHub"></span><!--0.09240 GBP [0.09800 USD](07-11-2023) (3297018-hyperlink) -->
                                </td>
                            </tr>
                            <tr>
                                <td class="text_left">Win/Loss Ratio of selected part for selected customer in last 12 months (Rolling)</td>
                                <td class="text_right">
                                    <span id="lblWinLossRatio"></span>&nbsp; Quotes Won
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <button class="accordion" onclick="return false;" id="btnTop3BuyPrice">
                                        <span class="text_left" style=" width:51.4%; font-size:10px;">
                                            Top 3 Buy Price in last 6 months
                                        </span>
                                    </button>

                                    <div class="panel">
                                        <table class="data_tb">
                                            <thead>
                                                <tr>
                                                    <th>PO/IPO</th>
                                                    <th>Date</th>
                                                    <th>Price</th>
                                                </tr>
                                            </thead>
                                            <tbody id="tbTop3BuyPrice" class="data-Breakdown-GI"></tbody>
                                        </table>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                    <span id="dots"> </span>
                    <table id="more">
                        <tbody>
                            <tr style="border-bottom: 1px solid #dcdcdc">
                            </tr>

                            <tr>
                                <td colspan="2" class="text_left"><label>Requirements</label></td>
                            </tr>
                            <tr>
                                <td class="text_left">Number of Customer Requirements for this part in the last 12 months</td>
                                <td class="text_right"><span id="lblNumberCusReqIn12Month"></span>&nbsp; QTY</td>
                            </tr>
                            <tr>
                                <td>
                                    <button class="accordion" id="btnLast20CusReqs">
                                        <span class="text_left" style=" width:51.4%; font-size:10px;">
                                            Last 20 HUBRFQ for the selected part
                                        </span>
                                    </button>

                                    <div class="panel">
                                        <table class="data_tb">
                                            <thead>
                                                <tr>
                                                    <th>HUBRFQ Number</th>
                                                    <th>Quantity</th>
                                                    <th>Customer Target Price</th>
                                                    <th>Quoted?</th>
                                                </tr>
                                            </thead>
                                            <tbody id="tbLast20CusReqs" class="data-Breakdown-GI"></tbody>
                                        </table>
                                    </div>
                                </td>
                            </tr>

                            <tr>
                                <td class="text_left">Lastest HUBRFQ for the selected part</td>
                                <td class="text_right"><span id="lblLatestRequirement"></span></td> <!--16-04-2024 (3297018-hyperlink) -->
                            </tr>
                            

                            <tr style="border-bottom: 1px solid #dcdcdc">
                            </tr>

                            <tr>
                                <td colspan="2" class="text_left"><label>Sales</label></td>
                            </tr>
                            <tr>
                                <td class="text_left">
                                    Price last invoiced to this Customer for the selected part in the last 12 months (Rolling)
                                </td>
                                <td class="text_right"><span id="lblPriceLastInvoiced"></span></td>
                            </tr>
                            <tr>
                                <td class="text_left">
                                    Highest sales in price in the last 12 months (Rolling)
                                </td>
                                <td class="text_right"><span id="lblHighestSales"></span></td>
                            </tr>
                            <tr>
                                <td class="text_left">
                                    Lowest sales price in the last 12 months (Rolling)
                                </td>
                                <td class="text_right"><span id="lblLowestSales"></span></td>
                            </tr>
                            <tr>
                                <td class="text_left">
                                    Number parts invoiced for the last 12 months
                                </td>
                                <td class="text_right"><span id="lblNumberPartsInvoiced"></span>&nbsp; QTY</td>
                            </tr>

                            <tr style="border-bottom: 1px solid #dcdcdc">
                            </tr>

                            <tr>
                                <td colspan="2" class="text_left"><label>API Data</label></td>
                            </tr>
                            <tr>
                                <td class="text_left">
                                    IHS Data
                                </td>
                                <td class="text_right"><span id="lblIHSData"></span></td>
                                <!--AV Price: 0.11 M/Leading: 0.9 Target: 0.10 P/Status: Obselete -->
                            </tr>
                            <tr>
                                <td class="text_left">
                                    Lytica Data
                                </td>
                                <td class="text_right"><span id="lblLyticaData"></span></td>
                                <!--AV Price: 0.11 M/Leading: 0.9 Target: 0.10 P/Status: Obselete -->
                            </tr>
                        </tbody>
                    </table>

                    <div class="clearing" style="clear:both;"></div>
                    <div>
                        <button onclick="return false" id="myBtn" class="read_btn">+ Read more</button>
                    </div>
                </div>
            </div>
            <span class="LastUpdate" id="spnLastUpdatedAdd"></span>
        </div>

        <div class="chat-bot-icon" id="show" style="display:none;">
            <div class="container">
                <div id="chatbot">
                    <div class="dot"></div>
                    <div class="dot"></div>
                    <div class="dot"></div>
                </div>
                <div id="antenna">
                    <div id="beam-pulsar"></div>
                </div>
            </div>
        </div>
        <div class="chat-bot-icon1" id="fixicon" style="display:none;">
            <div class="container">
                <div>
                    <div class="dot1"></div>
                    <div class="dot1"></div>
                    <div class="dot1"></div>
                </div>
            </div>
        </div>
    </div>

    <script>
        var _KubFlagConfiguration = false;
        var _BOMManagerItem;
        var _BOMManagerId;
        $(document).ready(function () {
            var acc = document.getElementsByClassName("accordion");
            var i;
            for (i = 0; i < acc.length; i++) {
                acc[i].addEventListener("click", function (event) {
                    this.classList.toggle("active");
                    var panel = this.nextElementSibling;
                    if (panel.style.maxHeight != "0px" && panel.style.maxHeight != "") {
                        panel.style.maxHeight = "0px";
                    } else {
                        panel.style.maxHeight = "330px";
                    }
                    event.preventDefault();
                    event.stopPropagation();
                });
            }
            GetKubConfig();
        });

        $(".chat-bot-icon").click(function (e) {
            var iframe = window.parent.frames['ctl00_cphMain_kubIframe'];
            if (iframe.style.height == '70px') {
                iframe.style.height = '520px';
            } else {
                iframe.style.height = '70px';
                ShowReadMoreKub();
            }

            $(this).children('img').toggleClass('close_icon_model');
            $(this).children('svg').toggleClass('animate');
            $('.chat-screen').toggleClass('show-chat');
            $("#close_modal").show();
            ResetViewDetails();
        });

        $("#close_icon_model").click(function () {
            CloseKubPopup();
            ResetViewDetails();
        });

        $("#myBtn").on("click", function () {
            var dots = document.getElementById("dots");
            if (dots.style.display === "none") {
                ShowReadMoreKub();
            } else {
                ShowReadLessKub();
            }
        });

        function CloseKubPopup() {
            var iframe = window.parent.frames['ctl00_cphMain_kubIframe'];
            if (iframe.style.height == '520px') {
                iframe.style.height = '70px';
                ShowReadMoreKub();
            }
            $(".chat-bot-icon").children('img').toggleClass('close_icon_model');
            $(".chat-bot-icon").children('svg').toggleClass('animate');
            $('.chat-screen').toggleClass('show-chat');
            $("#close_modal").show();
        }

        function DisplayHubIcon() {
            $('.chat-bot-icon').css('display', 'block');
            $('.chat-bot-icon1').css('display', 'none');
        }

        function ResetKubDetail() {
            var iframe = window.parent.frames['ctl00_cphMain_kubIframe'];
            if (iframe.style.height == '520px') {
                iframe.style.height = '70px';
                ShowReadMoreKub();
            }

            $('.chat-bot-icon').css('display', 'none');
            $('.chat-bot-icon1').css('display', 'block');
            $(".chat-bot-icon").children('img').removeClass('close_icon_model');
            $(".chat-bot-icon").children('svg').removeClass('animate');
            $('.chat-screen').removeClass('show-chat');

            $('#spPartNo').text('');
            $('#lblLatestQuotePrice').text('');
            $('#lblLatestQuotePriceByHub').text('');
            $('#lblNumberCusReqIn12Month').text('');
            $('#lblLatestRequirement').text('');
            $('#lblPriceLastInvoiced').text('');
            $('#lblHighestSales').text('');
            $('#lblLowestSales').text('');
            $('#lblNumberPartsInvoiced').text('');
            $('#lblWinLossRatio').text('');
            $('#lblIHSData').text('');
            $('#lblLyticaData').text('');
            $('#tbLast20CusReqs').empty();
            $('#tb10MostOffers').empty();
            $('#tbTop3BuyPrice').empty();
            $('#spnLastUpdatedAdd').text('');
            ResetViewDetails();
        }

        function ResetViewDetails() {
            var acc = document.getElementsByClassName("accordion");
            for (var i = 0; i < acc.length; i++) {
                if (acc[i].id) {
                    $('#' + acc[i].id).removeClass("active");
                    var panel = acc[i].nextElementSibling;
                    panel.style.maxHeight = "0px";
                }
            }
        }

        function ShowReadMoreKub() {
            var dots = document.getElementById("dots");
            var moreText = document.getElementById("more");
            var btnText = document.getElementById("myBtn");

            dots.style.display = "inline";
            btnText.innerHTML = "+ Read more";
            moreText.style.display = "none";
        }

        function ShowReadLessKub() {
            var dots = document.getElementById("dots");
            var moreText = document.getElementById("more");
            var btnText = document.getElementById("myBtn");

            dots.style.display = "none";
            btnText.innerHTML = "- Read less";
            moreText.style.display = "table";
        }

        function LoadPartNumber(BOMManagerItem, BOMManagerId) {
            if (!_KubFlagConfiguration) {
                return;
            }

            ResetKubDetail();

            _BOMManagerItem = BOMManagerItem;
            _BOMManagerId = BOMManagerId;
            var partNo = _BOMManagerItem.Part;
            var manufacturer = _BOMManagerItem.ManufacturerName;
            var manufacturerNo = _BOMManagerItem.ManufacturerNo;
            $('#spPartNo').text(partNo);

            ShowKubAssistanceDetails(partNo, manufacturerNo, manufacturer);
        }

        function GetKubConfig() {
            $.ajax({
                type: 'GET',
                contentType: 'application/json',
                url: 'GetKubConfigData',
                async: true,
                success: function (data) {
                    _KubFlagConfiguration = data.IsEnalbe;
                    if (_KubFlagConfiguration) {
                        $('.chat-bot-icon1').css('display', 'block');
                    } else {
                        $('.chat-bot-icon1').css('display', 'none');
                    }
                },
                error: function () { /*alert("something went wrong");*/ }
            });
        }

        function ShowKubAssistanceDetails(partNo, manufacturerNo, manufacturer) {
            var payload = {
                partNo: partNo,
                bomManagerId: _BOMManagerId,
                manufactuerId: manufacturerNo,
                manufacturerName: manufacturer
            };
            $.ajax({
                type: "POST",
                url: "ShowKubAssistance",
                data: JSON.stringify(payload),
                dataType: "json",
                contentType: "application/json",
                success: function (data) {
                    if (data.IsEnalbe == false || data.PartNo != $('#spPartNo').text()) {
                        return;
                    }
                    var fullPart = data.FullPartNo;
                    GetListKubTop10QuoteForBOM(fullPart);
                    GetListKubTop3BuyPrice(fullPart);
                    GetListKubTop20CusReqForBOM(fullPart);
                    DisplayKUBDetails(data);
                    DisplayHubIcon();
                }
            });
        }

        function DisplayKUBDetails(data) {
            var noDataText = 'N/A';
            $('#lblLatestQuotePrice').html("");
            $('#lblLatestQuotePriceByHub').html("");
            $('#lblNumberCusReqIn12Month').html("");
            $('#lblLatestRequirement').html("");
            $('#lblPriceLastInvoiced').html("");
            $('#lblHighestSales').html("");
            $('#lblNumberPartsInvoiced').html("");
            $('#lblWinLossRatio').html("");
            $('#spnLastUpdatedAdd').html("");

            $('#lblLatestQuotePrice').html(data.LastQuotedPrice ? data.LastQuotedPrice : noDataText);
            $('#lblLatestQuotePriceByHub').html(data.LastHubprice ? data.LastHubprice : noDataText);
            $('#lblNumberCusReqIn12Month').html(data.NumberOfRequirement);

            var latestRequirement = "";

            if (!data.LastestHubRFQId) {
                latestRequirement = '<span>N/A</span>';
            } else {
                latestRequirement = data.LastestHubNumberDate + '<a class="documentachor" href="../../Ord_BOMDetail.aspx?BOM='
                    + data.LastestHubRFQId + '" target="_blank">(' + data.LastestHubRFQName + ')</a>'
            }

            $('#lblLatestRequirement').html(latestRequirement);

            $('#lblPriceLastInvoiced').html(data.LastSoldPrice ? data.LastSoldPrice : noDataText);
            $('#lblHighestSales').html(data.LastHighestSoldPrice ? data.LastHighestSoldPrice: noDataText);
            $('#lblLowestSales').html(data.LastLowestSoldPrice ? data.LastLowestSoldPrice: noDataText);
            $('#lblNumberPartsInvoiced').html(data.NumberOfInvoice ? data.NumberOfInvoice : noDataText);
            $('#lblWinLossRatio').html(data.NumberQuoteToSalesOrder + '/' + data.NumberOfQuote);
            $('#spnLastUpdatedAdd').html(data.LastUpdatedDate);
            $('#lblIHSData').html(data.IHSResultForPartNo);
            $('#lblLyticaData').html(data.LyticaResultForPartNo);
        }

        function GetListKubTop20CusReqForBOM(fullPartNo) {
            $.ajax({
                type: "GET",
                url: "GetListKubTop20CusReqForBOM?fullPartNo=" + fullPartNo,
                dataType: "json",
                contentType: "application/json",
                success: function (data) {
                    $('#tbLast20CusReqs').html("");
                    if (data.length <= 0) {
                        var tbody = '<tr>' +
                            '<td style="border-right:none;text-align: center;" colspan="4">No HUBRFQ found</td >' +
                            '</tr>'
                        $('#tbLast20CusReqs').html(tbody);
                    } else {
                        for (var i = 0; i < data.length; i++) {
                            var row = data[i];
                            var tr = '<tr>' +
                                '<td>' + row.BOMName + '</td>' +
                                '<td>' + row.Quantity + '</td>' +
                                '<td>' + row.Price + '</td>' +
                                '<td>' + row.Quoted + '</td>' +
                                '</tr>';
                            $('#tbLast20CusReqs').append(tr);
                        }
                    }
                }
            });
        }

        function GetListKubTop10QuoteForBOM(fullPartNo) {
            $.ajax({
                type: "GET",
                url: "GetListKubTop10QuoteForBOM?fullPartNo=" + fullPartNo,
                dataType: "json",
                contentType: "application/json",
                success: function (data) {
                    $('#tb10MostOffers').html("");
                    if (data.length <= 0) {
                        var tbody = '<tr>' +
                            '<td style="border-right:none;text-align: center;" colspan="4">No Quote found</td >' +
                            '</tr>'
                        $('#tb10MostOffers').html(tbody);
                    } else {
                        for (var i = 0; i < data.length; i++) {
                            var row = data[i];
                            var tr = '<tr>' +
                                '<td>' + row.QuoteNumber + '</td>' +
                                '<td>' + row.Quantity + '</td>' +
                                '<td>' + row.Price + '</td>' +
                                '<td>' + row.ConvertedToSO + '</td>' +
                                '</tr>';
                            $('#tb10MostOffers').append(tr);
                        }
                    }                 
                }
            });
        }

        function GetListKubTop3BuyPrice(fullPartNo) {
            $.ajax({
                type: "GET",
                url: "GetListKubTop3BuyPrice?fullPartNo=" + fullPartNo,
                dataType: "json",
                contentType: "application/json",
                success: function (data) {
                    $('#tbTop3BuyPrice').html("");
                    for (var i = 0; i < data.length; i++) {
                        var row = data[i];
                        if (row.POID == "No purchase found") {
                            var tbody = '<tr>' +
                                '<td colspan="3" style="border-right:none; text-align: center;">' + row.POID + '</td>' +
                                '</tr>'
                            $('#tbTop3BuyPrice').html(tbody);
                        } else {
                            var POName = "";
                            if (row.IsClientPrice == true) {
                                reqDetailURL = "../../Ord_InternalPODetail.aspx?ipo=" + row.POID;
                                POName = row.PONo + " (From IPO)";
                            } else {
                                reqDetailURL = "../../Ord_PODetail.aspx?po=" + row.POID;
                                POName = row.PONo;
                            }

                            var hyperLink = ' <a class="documentachor" href="' + reqDetailURL + '" target="_blank">' + POName + '</a>';
                            var tr = '<tr>' +
                                '<td>' + hyperLink + '</td>' +
                                '<td>' + row.Date + '</td>' +
                                '<td>' + row.Price + '</td>' +
                                '</tr>';
                            $('#tbTop3BuyPrice').append(tr);
                        }
                    }
                }
            });
        }
    </script>
</body>
</html>