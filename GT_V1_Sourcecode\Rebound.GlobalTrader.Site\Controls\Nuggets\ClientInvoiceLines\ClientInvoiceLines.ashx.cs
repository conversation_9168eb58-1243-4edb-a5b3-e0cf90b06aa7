//-----------------------------------------------------------------------------------------
// RP 12.10.2009:
// - retrofitting changes from v3.0.34 - allow selection of whether to update Stock
//   and Shipments too (task 322)
//-----------------------------------------------------------------------------------------
using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;
using System.Net;


namespace Rebound.GlobalTrader.Site.Controls.Nuggets.Data {
	[WebService(Namespace = "http://tempuri.org/")]
	[WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    public class ClientInvoiceLines : Rebound.GlobalTrader.Site.Data.Base
    {

		public override void ProcessRequest(HttpContext context) {
			if (base.init(context)) {
				switch (Action) {
					case "GetLines": GetLines(); break;
					case "Delete": Delete(); break;
					default: WriteErrorActionNotFound(); break;
				}
			}
		}

		/// <summary>
		/// Get all Client invoice line for specified Client invoice
		/// </summary>
		private void GetLines() {
            List<ClientInvoiceLine> lstLines = null;
			try {
                lstLines = ClientInvoiceLine.GetListForClientInvoiceLine(ID);
                if (lstLines == null)
                {
					WriteErrorDataNotFound();
				} else {
					JsonObject jsn = new JsonObject();
					JsonObject jsnItems = new JsonObject(true);
                    double total = 0;
                    string strCurrencyCode = "";
                    foreach (ClientInvoiceLine ln in lstLines)
                    {
                        double lineTotal = ((double)((ln.UnitPrice.HasValue) ? ln.UnitPrice.Value : 0)) * ((double)((ln.QtyReceived.HasValue) ? ln.QtyReceived.Value : 0));
                        total += lineTotal;
						JsonObject jsnItem = new JsonObject();
                        jsnItem.AddVariable("UnitPrice", Functions.FormatCurrency(ln.UnitPrice, ln.CurrencyCode));
                        jsnItem.AddVariable("ClientInvoiceLineId", ln.ClientInvoiceLineId);
                        jsnItem.AddVariable("ClientInvoiceNo", ln.ClientInvoiceNo);
                        jsnItem.AddVariable("GoodsInNo", ln.GoodsInNo);
                        jsnItem.AddVariable("GoodsInLineNo", ln.GoodsInLineNo);
                        jsnItem.AddVariable("GoodsInNumber", ln.GoodsInNumber);
                        jsnItem.AddVariable("Part", ln.Part);
                        jsnItem.AddVariable("SupplierPart", ln.SupplierPart);
                        //jsnItem.AddVariable("GoodsInLineValue", ln.GoodsInLineValue);
                        jsnItem.AddVariable("QtyReceived", Functions.FormatNumeric(ln.QtyReceived));
                        jsnItem.AddVariable("LineTotal", Functions.FormatCurrency(lineTotal, ln.CurrencyCode));
                        jsnItem.AddVariable("Landedcost", Functions.FormatCurrency(ln.Landedcost,SessionManager.ClientCurrencyCode));
                        jsnItem.AddVariable("DateReceived",Functions.FormatDate(ln.DateReceived));
                        jsnItem.AddVariable("StockNo", ln.StockNo);
                        jsnItem.AddVariable("ROHS", ln.ROHS);
                        jsnItem.AddVariable("UpdatedBy", ln.UpdatedBy);
                        jsnItem.AddVariable("DLUP", ln.DLUP);

                        jsnItem.AddVariable("MFR", ln.CountryOfManufacture);
                        jsnItem.AddVariable("DateCode", ln.DateCode);
                        jsnItem.AddVariable("ProductNo", ln.ProductNo);
                        jsnItem.AddVariable("Product", ln.ProductName);
                        jsnItem.AddVariable("PackageNo", ln.PackageNo);
                        jsnItem.AddVariable("PackageName", ln.PackageName);
                        jsnItem.AddVariable("IPO", ln.InternalPurchaseOrderNumber);
                        jsnItem.AddVariable("IPONo", ln.InternalPurchaseOrderNo);
                        jsnItem.AddVariable("PO", ln.PurchaseOrderNumber);
                        jsnItem.AddVariable("PONo", ln.PurchaseOrderNo);
                        jsnItem.AddVariable("ManufacturerNo", ln.ManufacturerNo);
                        jsnItem.AddVariable("Manufacturer", ln.ManufacturerCode);
						jsnItems.AddVariable(jsnItem);
						jsnItem.Dispose();
						jsnItem = null;
                        strCurrencyCode = ln.CurrencyCode;
					}
					jsn.AddVariable("Lines", jsnItems);
                    jsn.AddVariable("Total", Functions.FormatCurrency(total,strCurrencyCode, 5));
                    jsn.AddVariable("TotalValue", Functions.FormatCurrency(total, 5));
                    jsn.AddVariable("IsPOHub", SessionManager.IsPOHub);
					OutputResult(jsn);
					jsn.Dispose(); jsn = null;
				}
			} catch (Exception e) {
				WriteError(e);
			} finally {
                lstLines = null;
			}
		}
		

		/// <summary>
		/// Delete
		/// </summary>
		public void Delete() {
			try {
				JsonObject jsn = new JsonObject();
                jsn.AddVariable("Result", ClientInvoiceLine.Delete(ID, LoginID));
				OutputResult(jsn);
				jsn.Dispose(); jsn = null;
			} catch (Exception e) {
				WriteError(e);
			}
		}

		

	}
}
