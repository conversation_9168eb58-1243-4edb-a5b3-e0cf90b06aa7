using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;
using Rebound.GlobalTrader.Site.Enumerations;

namespace Rebound.GlobalTrader.Site.Controls.DataListNuggets.Data {

    public class CustomerRequirementsBOMImport : Base
    {

        /// <summary>
        /// Gets the main data
        /// </summary>
        protected override void GetData() {
            JsonObject jsn = new JsonObject();
            int? getPageLimit = GetFormValue_NullableInt("PageLimit") > 0 ? GetFormValue_NullableInt("PageLimit") : 50;
            if (GetFormValue_Boolean("IsGet"))
            {
                List<CustomerRequirement> lst = CustomerRequirement.DataListNuggetImport(
                    SessionManager.ClientID
                    , (int?)LoginID
                    , GetFormValue_NullableInt("SortIndex", 0)
                    , GetFormValue_NullableInt("SortDir", SortColumnDirection.ASC)
                    , GetFormValue_NullableInt("PageIndex", 0)
                    //, GetFormValue_NullableInt("PageSize", 50)
                    , GetFormValue_NullableInt("NewPageSize", getPageLimit)
                    , GetFormValue_StringForNameSearchNew("ClientBOMName")
                    , GetFormValue_NullableInt("Salesman")
                    , GetFormValue_StringForNameSearchDecode("CMName")
                    , GetFormValue_NullableDateTime("ImportDateFrom")
                    , GetFormValue_NullableDateTime("ImportDateTo")
                    , GetFormValue_NullableInt("Status")
                );

                //check counts
                jsn.AddVariable("TotalRecords", (lst.Count > 0) ? lst[0].RowCnt : 0);

                //format data
                JsonObject jsnRowsArray = new JsonObject(true);
                for (int i = 0; i < lst.Count; i++)
                {
                    if (i < lst.Count)
                    {
                        JsonObject jsnRow = new JsonObject();
                        jsnRow.AddVariable("ID", lst[i].ClientBOMId);
                        jsnRow.AddVariable("ClientBOMNo", lst[i].ClientBOMCode);
                        jsnRow.AddVariable("ClientBOMName", lst[i].ClientBOMName);
                        jsnRow.AddVariable("Company", lst[i].CompanyName);
                        jsnRow.AddVariable("Contact", lst[i].ContactNo);
                        jsnRow.AddVariable("ContactName", lst[i].ContactName);
                        jsnRow.AddVariable("Salesman", lst[i].SalesmanName);
                        jsnRow.AddVariable("ImportDate", Functions.FormatDate(lst[i].ImportDate));
                        jsnRow.AddVariable("NoOfRequirements", lst[i].NoOfRequirements);
                        jsnRow.AddVariable("RecordsProcessed", lst[i].RecordsProcessed);
                        jsnRow.AddVariable("RecordsRemaining", lst[i].RecordsRemaining);
                        jsnRow.AddVariable("BomId", lst[i].BOMNo);
                        jsnRow.AddVariable("BOMName", lst[i].BOMName);
                        jsnRowsArray.AddVariable(jsnRow);
                        jsnRow.Dispose();
                        jsnRow = null;
                    }
                }
                jsn.AddVariable("Results", jsnRowsArray);
                OutputResult(jsn);
                jsnRowsArray.Dispose();
                jsnRowsArray = null;
                jsn.Dispose();
                jsn = null;
                base.GetData();
            }
            else
            {
                jsn.AddVariable("TotalRecords",  0);
                JsonObject jsnRowsArray = new JsonObject(true);
                jsn.AddVariable("Results", jsnRowsArray);
                OutputResult(jsn);
                jsnRowsArray.Dispose();
                jsnRowsArray = null;
                jsn.Dispose();
                jsn = null;
                base.GetData();
            }
        }

        protected override void AddFilterStates()
        {
           //  AddFilterState("CMName");
            // AddFilterState("Salesman");
            //base.AddFilterStates();
        }
    }
}
