﻿<!DOCTYPE html>

<html lang="en" xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta charset="utf-8" />
    @*<link rel='stylesheet' href="~/Areas/BOM/css/BOMManagerCustomTemplate.css" />*@
    <link rel='stylesheet' href='https://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/4.1.3/css/bootstrap.min.css'>
    <script src='https://cdnjs.cloudflare.com/ajax/libs/jquery/3.4.1/jquery.min.js'></script>
    <script src='https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.15.0/popper.min.js'></script>
    <script src='https://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/4.3.1/js/bootstrap.min.js'></script>
    <link rel="stylesheet" type="text/css" href="//cdn.datatables.net/1.10.12/css/jquery.dataTables.min.css" />
    <script src="//cdn.datatables.net/1.10.12/js/jquery.dataTables.min.js"></script>

    <title></title>
    @*<style>
            body {
                font-family: Tahoma;
                background-color: #56954e;
            }
        </style>*@
    <style>

        body {
            background-color: #56954E;
            font-family: Tahoma;
        }

        .container_green {
            background-color: #56954E;
            background-repeat: repeat-x;
            background-position: bottom;
            color: #ffffff;
            font-family: Tahoma;
            font-size: 11px;
            font-style: normal;
        }

        .name_label {
            width: 30%;
            padding-right: 5px;
            padding-bottom: 7px;
            text-align: left;
            font-size: 11px;
            color: #009900;
            float: left;
        }

        .form {
            width: 100%;
            float: left;
            padding: 10px 10px;
            background-color: #BBF2B3
        }

        .right_tbl {
            width: 100%;
            float: left;
        }

        .form h5 {
            font-size: 12px;
            font-family: Lucida Sans, Arial;
            text-transform: uppercase;
            font-weight: bold;
            margin: 10px 0px;
            padding-bottom: 3px;
            border-bottom: dotted 1px #90db89;
            color: #009900;
        }

        .right_tbl tr {
            width: 25%;
            float: left;
        }

        .sel_data {
            width: 68%;
            float: left;
        }

            .sel_data select {
                width: 70%;
                float: left;
                border-radius: 2px;
                font-size: 11px;
                padding: 2px 2px;
                border: none !important;
                background-color: white;
            }



            .sel_data input[type=checkbox] {
                float: left;
                margin-top: 4px;
                margin-right: 5px;
            }

            .sel_data input[type=button] {
                -moz-box-shadow: inset 0px 1px 0px 0px #a4e271;
                -webkit-box-shadow: inset 0px 1px 0px 0px #a4e271;
                box-shadow: inset 0px 1px 0px 0px #a4e271;
                background: -webkit-gradient( linear, left top, left bottom, color-stop(0.05, #89c403), color-stop(1, #77a809) );
                background: -moz-linear-gradient( center top, #89c403 5%, #77a809 100% );
                filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#89c403', endColorstr='#77a809');
                background-color: #89c403;
                text-decoration: none;
                text-shadow: 1px 1px 0px #528009;
                -moz-border-radius: 4px;
                -webkit-border-radius: 4px;
                border-radius: 4px;
                display: inline-block;
                color: #fff;
                font-family: Verdana;
                font-size: 10px;
                font-weight: bold;
                padding: 6px 10px;
                text-decoration: none;
                margin-top: 10px;
                border: none;
                cursor: pointer;
                opacity: 0.5;
            }


        .grid_area {
            width: 98.5%;
            float: left;
            padding: 1%;
            border: 1px #6cab63 solid;
            margin-top: 20px;
            margin-left: 10px;
        }

        .grid_table {
            width: 100%;
            float: left;
        }

            .grid_table table {
                border-collapse: collapse;
                border: 1px solid #89c403;
                width: 100%;
            }

                .grid_table table thead {
                    background-image: url(../../../../App_Themes/Original/images/FlexiDataTable/th_bg.png) !important;
                    background-position: bottom !important;
                    background-repeat: repeat-x !important;
                    background-color: #eeeeee;
                    color: #999999;
                }

                    .grid_table table thead tr th {
                        padding: 5px 20px 5px 5px;
                        /*  background-color: #808080;*/
                        border-right: 1px solid #afafaf;
                        text-align: left;
                        border-bottom: none;
                    }

                .grid_table table tbody tr td {
                    font-weight: normal;
                    border-right: 1px solid #dbdbdb;
                    padding: 5px;
                    text-align: left;
                    border-bottom: 1px solid #dbdbdb;
                    background-color: white;
                    color: #000;
                }


        .btn_area {
            float: right;
            width: 100%;
            margin-top: 10px;
            text-align: right;
        }

            .btn_area input[type="button"] {
                background: #cacaca;
                color: #040404;
                padding: 8px 11px;
                border: 1px #427f3a solid;
                width: 11%;
                white-space: normal;
                float: right;
                font-weight: bold;
                margin-top: 4px;
                opacity: 0.5;
                border-radius: 4px;
                cursor: pointer;
            }

        #table1_length, #table1_info {
            padding: 10px;
            /*background: #3a6c34;*/
            color: #d3fFcC;
        }

        #table1_paginate {
            margin-top: 5px;
        }

            #table1_paginate a {
                padding: 2px 7px;
                margin: 5px;
                background: #3a6c34;
                color: #d3fFcC !important;
            }

        #table1 tbody tr:hover {
            background-color: #BBF2B3 !important;
        }

        .tbl_data {
            display: block;
            overflow: scroll;
        }

            .tbl_data tr th {
                width: 20% !important;
            }

        .dataTables_length label {
            color: #d3fFcC;
            font-family: Tahoma;
        }

            .dataTables_length label input {
                border-radius: 3px;
            }


        .btn_tbl {
            width: 70.5% !important;
        }

            .btn_tbl tr {
                text-align: right;
                width: 100%
            }

        .btn_bottom {
            float: right !important;
            width: 100% !important;
        }

        .note_area {
            width: 100%;
            float: left;
            background-color: #666;
            color: #fff;
            font-weight: bold;
            padding: 6px 6px;
            border-radius: 4px;
        }
    </style>
    <script type="text/javascript">
        var defaultOption = '<option value="0">&lt; Select &gt;</option>';
        var refershOption = '<option selected="true" value="Loading">loading</option>';
        var optionStart = "<option value='";
        var optionEnd = "</option>";

        $(document).ready(function () {
            //LoadCategories();
            GetColumnList();

            $('#btnGenerateData').click(function () {
                $('#gridtable').css('display', 'block');
                $('#btnGenerateData').prop('disabled', true).css('opacity', 0.5);
                GetColumnMapping();
                GetGeneratedCustomTemplateDate();
            });
            $('#btnExportData').click(function () {
                ExportCustomTemplateDate();
            });
            $('#btnSaveMapping').click(function () {

                SaveMapping();
            });
            $('#btnRetrieveMapping').click(function () {

                GetMapping();
            });


        });
        function GetMapping() {
            var columnmapping = GetColumnMapping();
            var qrystrObj = getUrlVars();
            //console.log(qrystrObj);
            //console.log(qrystrObj.id);
            $.ajax({
                processData: true,
                contentType: 'application/json',
                type: 'POST',
                url: 'GetCustomTemplateMapping?QuoteID=' + qrystrObj.id,
                dataType: "json",

                success: function (data) {
                    //Convert Base64 string to Byte Array.
                    //debugger;
                    //console.log(data);
                    /* $('#ddlPart').empty();*/
                    if (data.status == 'Success')
                        alert('Customer Template Mappings Retreived')
                    var mainkeyval = data.Message.split('|');
                    mainkeyval = mainkeyval.slice(0, -1);
                    $.each(mainkeyval, function (index, value) {
                        //console.log(index + '--' + value);
                        var keyval = value;
                        var keyvalarr = value.split('=');
                        keyvalarr = keyvalarr.slice(0, -1);
                        //debugger;
                        for (let i = 0; i < keyvalarr.length; i++) {
                            $(".ddlclass").each(function (index, element) {
                                //console.log(('ddl' + keyvalarr[0]) + '~~~~~~~~~~~~' + element.id);
                                if (('ddl' + keyvalarr[0]) == element.id) {
                                    $('#' + element.id).val(keyvalarr[1]).change();
                                    //console.log(element.id + '=val =' + keyvalarr[0] + '=' + keyvalarr[1])
                                }
                                //$('#' + element.id).append('<option value=' + value.columnId + '>' + value.ColumnName + '</option>');

                            });
                        }
                        //$.each(keyval.split('='), function (indx, valstr) {
                        //    //console.log(index + '++' + valstr)
                        //    $(".ddlclass").each(function (index, element) {
                        //        //console.log(('ddl' + valstr) + '~~~~~~~~~~~~' + element.id);
                        //        if (('ddl' + valstr) == element.id) {
                        //            $('#' + element.id).val(valstr);
                        //            //console.log(element.id+'=val =' + valstr)
                        //        }
                        //        //$('#' + element.id).append('<option value=' + value.columnId + '>' + value.ColumnName + '</option>');

                        //    });
                        //});
                    });


                },
                error: function (err) {
                    /*alert(err);*/
                    //console.log(err);
                }
            });

        }
        function GetBOMManagerUploadMapping() {
            var columnmapping = GetColumnMapping();
            var qrystrObj = getUrlVars();
            //console.log(qrystrObj);
            //console.log(qrystrObj.id);
            $.ajax({
                processData: true,
                contentType: 'application/json',
                type: 'POST',
                url: 'GetBOMManagerUploadMapping?QuoteID=' + qrystrObj.id,
                dataType: "json",

                success: function (data) {
                    //Convert Base64 string to Byte Array.
                   // debugger;
                    //console.log(data);
                    /* $('#ddlPart').empty();*/
                    if (data.status == 'Success')
                        alert('Uploaded File Mappings Retreived')
                    //debugger;
                    var mainkeyval = data.Message.split('|');
                    //mainkeyval = mainkeyval.slice(0, -1);
                    $.each(mainkeyval, function (index, value) {
                        //console.log(index + '--' + value);
                        var keyval = value;
                        var keyvalarr = value.split('=');
                        //keyvalarr = keyvalarr.slice(0, -1);
                        //debugger;
                        for (let i = 0; i < keyvalarr.length; i++) {
                            $(".ddlclass").each(function (index, element) {
                                //console.log(('ddl' + keyvalarr[0]) + '~~~~~~~~~~~~' + element.id);
                                keyvalarr[1] = keyvalarr[1].replace('Column', '');
                                keyvalarr[0] = keyvalarr[0].replace('StockCode', 'CustomerPart');
                                keyvalarr[0] = keyvalarr[0].replace('Description', 'Mfr');
                                keyvalarr[0] = keyvalarr[0].replace('RFQ', 'QuotedQuantity');
                                //console.log('ddl' + keyvalarr[0] + '**********' + element.id);
                                if (('ddl' + keyvalarr[0]) == element.id) {
                                    $('#' + element.id).val(keyvalarr[1]).change();
                                  //  console.log(element.id + '=val =' + keyvalarr[0] + '=' + keyvalarr[1])

                                }
                                //$('#' + element.id).append('<option value=' + value.columnId + '>' + value.ColumnName + '</option>');

                            });
                        }
                        //$.each(keyval.split('='), function (indx, valstr) {
                        //    //console.log(index + '++' + valstr)
                        //    $(".ddlclass").each(function (index, element) {
                        //        //console.log(('ddl' + valstr) + '~~~~~~~~~~~~' + element.id);
                        //        if (('ddl' + valstr) == element.id) {
                        //            $('#' + element.id).val(valstr);
                        //            //console.log(element.id+'=val =' + valstr)
                        //        }
                        //        //$('#' + element.id).append('<option value=' + value.columnId + '>' + value.ColumnName + '</option>');

                        //    });
                        //});
                    });


                },
                error: function (err) {
                    /*alert(err);*/
                    //console.log(err);
                }
            });

        }
        function SaveMapping() {
            var columnmapping = GetColumnMapping();
            var qrystrObj = getUrlVars();
            //console.log(qrystrObj);
            //console.log(qrystrObj.id);
            $.ajax({
                processData: true,
                contentType: 'application/json',
                type: 'POST',
                url: 'SaveCustomTemplateMapping?QuoteID=' + qrystrObj.id + "&ColumnString=" + columnmapping,
                dataType: "json",

                success: function (data) {
                    //Convert Base64 string to Byte Array.
                    //debugger;
                    //console.log(data);
                    /* $('#ddlPart').empty();*/
                    if (data.status == 'Success')
                        alert('Customer Template Mappings Saved')


                },
                error: function (err) {
                    /*alert(err);*/
                    //console.log(err);
                }
            });

        }
        function GetColumnList() {


            var qrystrObj = getUrlVars();
            //console.log(qrystrObj);
            //console.log(qrystrObj.id);
            $.ajax({
                processData: true,
                contentType: 'application/json',
                type: 'POST',
                url: 'GetColumnList?QuoteID=' + qrystrObj.id,
                dataType: "json",

                success: function (data) {
                    //Convert Base64 string to Byte Array.
                   // debugger;
                    ////console.log(data);
                    /* $('#ddlPart').empty();*/
                    //debugger;
                    if (data == null) {
                        $("#btnSaveMapping").prop('disabled', true).css('opacity', 0.5);
                        $("#btnRetrieveMapping").prop('disabled', true).css('opacity', 0.5);
                        $("#btnGenerateData").prop('disabled', true).css('opacity', 0.5);
                        alert('Mappings not found for this Quote.');
                        return;
                    }

                    $(".ddlclass").each(function (index, element) {
                        //console.log(element.id);
                        $('#' + element.id).empty();
                        $('#' + element.id).append('<option value=0>Select</option>');
                        //binding change event on all ddl controls
                        $('#' + element.id).on("change", function () {
                            //console.log(element.id + ' clicked');
                            //console.log($('#' + element.id).val() + '---' + $('#' + element.id).find(":selected").text());
                            //console.log(element.id + 'Chk');
                            //checking if some value is selected
                            if ($('#' + element.id).val() > 0) {
                                $('#' + element.id + 'Chk').prop('checked', true);
                            }
                            else {
                                $('#' + element.id + 'Chk').prop('checked', false);
                            }
                        });
                    });
                    //to populate data in drop downs
                    $.each(data, function (index, value) {
                        //$('#ddlPart').append('<option value=' + value.columnId + '>' + value.ColumnName + '</option>');
                        $(".ddlclass").each(function (index, element) {
                            $('#' + element.id).append('<option value=' + value.columnId + '>' + value.ColumnName + '</option>');

                        });
                        //listItems += optionStart + jsonColumnList.data[i].ExcelHeaderid + "'>" + jsonColumnList.data[i].ColumnHeading + optionEnd;
                    });
                    //console.log('column loaded');
                    GetBOMManagerUploadMapping();
                    $("#btnSaveMapping").prop('disabled', false).css('opacity', 5.5);
                    $("#btnRetrieveMapping").prop('disabled', false).css('opacity', 5.5);
                    $("#btnGenerateData").prop('disabled', false).css('opacity', 5.5);

                },
                error: function (err) {
                    /*alert(err);*/
                    //console.log(err);
                }
            });

        }

        function GetColumnMapping() {
            var colmapping = ''

            $(".ddlclass").each(function (index, element) {
                ////console.log(element.id);
                if ($('#' + element.id).val() > 0) {
                    /* $('#' + element.id + 'Chk').prop('checked', true);*/
                    colmapping = colmapping + element.id.replace('ddl', '') + '=' + $('#' + element.id).val() + '=' + $('#' + element.id).find(":selected").text() + '|';
                    //colmapping = colmapping + element.id.replace('ddl', '') + '=' + $('#' + element.id).find(":selected").text() + '|';
                }



            });
            //console.log(colmapping);
            return colmapping;
        }

        function GetGeneratedCustomTemplateDate() {
            var qrystrObj = getUrlVars();
            //console.log(qrystrObj);
            //console.log(qrystrObj.id);
            if ($('#ddlPart').val() < 1 || $('#ddlMfr').val() < 1) {
                window.confirm('Please select Pat and Manufacturer.')
                return;
            }
            var strColumnMapping = GetColumnMapping();
            var blnDefaultCheck = false;
            if ($('#DefaultColumnChk').is(":checked") == true)
                blnDefaultCheck = true;
            $.ajax({
                processData: true,
                contentType: 'application/json',
                type: 'POST',
                url: 'GenerateCustomTemplateData?QuoteID=' + qrystrObj.id + '&ColumnString=' + strColumnMapping + '&DefaulCheck=' + blnDefaultCheck,
                dataType: "json",

                success: function (data) {
                    //Convert Base64 string to Byte Array.
                    //debugger;
                    //console.log(data);
                    var jsonData = JSON.parse(data);
                    loadfirstgrid(jsonData);
                    /* $('#ddlPart').empty();*/
                    $("#btnExportData").prop('disabled', false).css('opacity', 5.5);
                    $('#btnGenerateData').prop('disabled', false).css('opacity', 5.5);

                },
                error: function (err) {
                    $('#btnGenerateData').prop('disabled', false).css('opacity', 5.5);
                    /*alert(err);*/
                    //console.log(err);
                }
            });
        }

        function ExportCustomTemplateDate() {
            var qrystrObj = getUrlVars();
            //console.log(qrystrObj);
            //console.log(qrystrObj.id);
            $("#btnExportData").prop('disabled', true).css('opacity', 0.5);
            if ($('#ddlPart').val() < 1 || $('#ddlMfr').val() < 1) {
                window.confirm('Please select Pat and Manufacturer.')
                return;
            }
            var strColumnMapping = GetColumnMapping();
            $.ajax({
                processData: true,
                contentType: 'application/json',
                type: 'POST',
                url: 'GetCustomTemplateFile?QuoteID=' + qrystrObj.id + '&ColumnString=' + strColumnMapping,
                dataType: "json",

                success: function (r) {
                    //Convert Base64 string to Byte Array.
                    //debugger;
                    //console.log(r);
                    //var jsonData = JSON.parse(r);
                    //loadfirstgrid(jsonData);
                    /* $('#ddlPart').empty();*/
                    //console.log(r.Base64String);
                    var bytes = Base64ToBytes(r.Base64String);

                    //Convert Byte Array to BLOB.
                    var blob = new Blob([bytes], { type: "application/octetstream" });

                    //Check the Browser type and download the File.
                    var isIE = false || !!document.documentMode;
                    if (isIE) {
                        window.navigator.msSaveBlob(blob, r.fileName);
                    } else {
                        var url = window.URL || window.webkitURL;
                        link = url.createObjectURL(blob);
                        var a = $("<a />");
                        a.attr("download", r.fileName);
                        a.attr("href", link);
                        $("body").append(a);
                        a[0].click();
                        $("body").remove(a);


                    }
                    $("#btnExportData").prop('disabled', false).css('opacity', 5.5);
                },
                error: function (err) {
                    alert('Something went wrong.');
                    $("#btnExportData").prop('disabled', false).css('opacity', 5.5);
                    /*alert(err);*/
                    //console.log(err);
                }
            });
        }

        function moveRight(leftValue, rightValue) {
            //alert("Elft value is t : "+leftValue);
            var leftSelect = document.forms["form1"].elements[leftValue];
            var rightSelect = document.forms["form1"].elements[rightValue];
            //alert("test : " + document.forms["form1"].elements[myLeftId].options[selItem].value);
            if (leftSelect.selectedIndex == -1) {
                window.alert("Please select an item Before Moving.")
            } else {
                var option = leftSelect.options[leftSelect.selectedIndex];
                rightSelect.appendChild(option);
            }
        }

        $('#BtnPrint').click(function () {
            SaveDocument();
        });

        function getUrlVars() {
            var vars = [], hash;
            var hashes = window.location.search.slice(window.location.search.indexOf('?') + 1).split('&');
            for (var i = 0; i < hashes.length; i++) {
                hash = hashes[i].split('=');
                vars.push(hash[0]);
                vars[hash[0]] = hash[1];
            }
            return vars;
        }

        function SaveDocument() {
            var qrystrObj = getUrlVars();
            //console.log(qrystrObj);
            //console.log(qrystrObj.id);
            $.ajax({
                processData: true,
                contentType: 'application/json',
                type: 'POST',
                url: 'SaveDocument?QuoteID=' + qrystrObj.id + '&documentFormat=' + $('input[name="ExportType"]:checked').val(),
                dataType: "json",

                success: function (r) {
                    //Convert Base64 string to Byte Array.
                    //console.log(r.Base64String);
                    var bytes = Base64ToBytes(r.Base64String);

                    //Convert Byte Array to BLOB.
                    var blob = new Blob([bytes], { type: "application/octetstream" });

                    //Check the Browser type and download the File.
                    var isIE = false || !!document.documentMode;
                    if (isIE) {
                        window.navigator.msSaveBlob(blob, r.fileName);
                    } else {
                        var url = window.URL || window.webkitURL;
                        link = url.createObjectURL(blob);
                        var a = $("<a />");
                        a.attr("download", r.fileName);
                        a.attr("href", link);
                        $("body").append(a);
                        a[0].click();
                        $("body").remove(a);
                    }
                },
                error: function (err) {
                    /*alert(err);*/
                    //console.log(err);
                }
            });
        }
        function Base64ToBytes(base64) {
            var s = window.atob(base64);
            var bytes = new Uint8Array(s.length);
            for (var i = 0; i < s.length; i++) {
                bytes[i] = s.charCodeAt(i);
            }
            return bytes;
        };
        function getdistorytable1() {
            var dataTableObject1 = $('#table1').DataTable();

            if (dataTableObject1) { // Check if table object exists and needs to be flushed
                dataTableObject1.destroy(); // For new version use table.destroy();
                $('#table1').empty(); // empty in case the columns change

            }
        }
        function loadfirstgrid(data) {
            ////debugger;
            //listItems = '';
            //listItems = defaultSelectdrop;
            //var collist = '';
            //$.each(data.columns, function (j, header) {
            //    collist = collist + header.title;
            //    listItems += optionStart + jsonColumnList.data[i].ExcelHeaderid + "'>" + jsonColumnList.data[i].ColumnHeading + optionEnd;
            //});
            //$('#hdnColumnList').val(data.columns);
            //var jsonColumnList = JSON.parse(data);
            //for (var i = 0; i < jsonColumnList.data.length; i++) {
            //    listItems += optionStart + jsonColumnList.data[i].ExcelHeaderid + "'>" + jsonColumnList.data[i].ColumnHeading + optionEnd;
            //}
            //$("#ddlCustomerPart").html(listItems);
            rowCount1 = $('#table1 tr').length;
            //console.log("table1 count" + rowCount1);
            if (rowCount1 != 0) {
                getdistorytable1();
            }

            $('#table1').DataTable({
                //serverSide: true,
                //processing: true,
                //ordering: false,
                //destroy: true,
                //autoWidth: true,
                searching: false,
                //bFilter: false,
                paging: true,
                pageLength: 10,
                // pageLength: 10,
                data: data.data,
                columns: data.columns
            });
            $("#divLoadershowdata").css('display', "none");
            if ($('input[name="fileType"]:checked').val() != 0) {
                $('#btnGenerateData').prop('disabled', false).css('opacity', 5.5);
                //code mapping
                $("#btnGetMapping").prop('disabled', false).css('opacity', 5.5);
                $("#btnSaveMapping").prop('disabled', false).css('opacity', 5.5);

            }
        }

        function getDataWithPagingsNew() {
            var responsedata;
            $.ajax({
                processData: true,
                contentType: 'application/json',
                type: 'POST',
                url: handlerUrl + '?action=GetData&ClientId=' + $("#ddlClient option:selected").val() + '&FileHeaderCheck=' + $('#chkFileCCH').prop('checked'),
                dataType: 'json',
                async: false,

                success: function (data) {
                    responsedata = data;
                    loadfirstgrid(data);
                    //var jsonObject = data;
                    //if (jsonObject.IsError) {
                    //    alert(jsonObject.ErrorMessage);
                    //    return;
                }
            }
            );
        }
    </script>
</head>
<body>
    <div class="container_green">

        <div class="form">
            <h5>Custom Template Mapping</h5>
            <table class="right_tbl">
                <tbody>
                    <tr>
                        <td class="name_label">
                            <label> Part No. *</label>

                        </td>
                        <td class="sel_data">
                            <input type="checkbox" id="ddlPartChk" />
                            <select class="ddlclass" id="ddlPart">
                                <option>Select</option>
                            </select>
                        </td>
                    </tr>
                    <tr>
                        <td class="name_label">
                            <label> Manufacturer *</label>

                        </td>
                        <td class="sel_data">
                            <input type="checkbox" id="ddlMfrChk" />
                            <select class="ddlclass" id="ddlMfr">
                                <option>Select</option>
                            </select>
                        </td>
                    </tr>
                    <tr>
                        <td class="name_label">
                            <label> Product *</label>

                        </td>
                        <td class="sel_data">
                            <input type="checkbox" id="ddlProductChk" />
                            <select class="ddlclass" id="ddlProduct">
                                <option>Select</option>
                            </select>

                        </td>
                    </tr>

                </tbody>

            </table>
            <table class="right_tbl">
                <tbody>

                    <tr>
                        <td class="name_label">
                            <label> Pack *</label>

                        </td>
                        <td class="sel_data">
                            <input type="checkbox" id="ddlPackChk" />
                            <select class="ddlclass" id="ddlPack">
                                <option>Select</option>
                            </select>
                        </td>
                    </tr>
                    <tr>
                        <td class="name_label">
                            <label> Notes *</label>

                        </td>
                        <td class="sel_data">
                            <input type="checkbox" id="ddlNotesChk" />

                            <select class="ddlclass" id="ddlNotes">
                                <option>Select</option>
                            </select>
                        </td>
                    </tr>
                    <tr>
                        <td class="name_label">
                            <label> Total Price *</label>

                        </td>
                        <td class="sel_data">
                            <input type="checkbox" id="ddlTotalPriceChk" />

                            <select class="ddlclass" id="ddlTotalPrice">
                                <option>Select</option>
                            </select>
                        </td>
                    </tr>
                </tbody>

            </table>
            <table class="right_tbl">
                <tbody>
                    <tr>
                        <td class="name_label">
                            <label> Unit Price *</label>

                        </td>
                        <td class="sel_data">
                            <input type="checkbox" id="ddlUnitPriceChk" />

                            <select class="ddlclass" id="ddlUnitPrice">
                                <option>Select</option>
                            </select>
                        </td>
                    </tr>
                    <tr>
                        <td class="name_label">
                            <label>Quantity</label>

                        </td>
                        <td class="sel_data">
                            <input type="checkbox" id="ddlQuotedQuantityChk" />

                            <select class="ddlclass" id="ddlQuotedQuantity">
                                <option>Select</option>
                            </select>
                        </td>
                    </tr>
                    <tr>
                        <td class="name_label">
                            <label>Customer Part</label>

                        </td>
                        <td class="sel_data">
                            <input type="checkbox" id="ddlCustomerPartChk" />

                            <select class="ddlclass" id="ddlCustomerPart">
                                <option>Select</option>
                            </select>
                        </td>
                    </tr>
                </tbody>

            </table>

            <table class="right_tbl">
                <tbody>


                    <tr>
                        <td class="name_label">
                            <label>DC</label>

                        </td>
                        <td class="sel_data">
                            <input type="checkbox" id="ddlDCChk" />

                            <select class="ddlclass" id="ddlDC">
                                <option>Select</option>
                            </select>
                        </td>
                    </tr>
                    <tr>
                        <td class="name_label">
                            <label>Duty Code</label>

                        </td>
                        <td class="sel_data">
                            <input type="checkbox" id="ddlDutyCodeChk" />

                            <select class="ddlclass" id="ddlDutyCode">
                                <option>Select</option>
                            </select>
                        </td>
                    </tr>
                    <tr>
                        <td class="name_label">
                            <label>ETA</label>

                        </td>
                        <td class="sel_data">
                            <input type="checkbox" id="ddlETAChk" />

                            <select class="ddlclass" id="ddlETA">
                                <option>Select</option>
                            </select>
                        </td>
                    </tr>
                </tbody>

            </table>
            <table class="right_tbl">
                <tbody>

                    <tr>
                        <td class="name_label">
                            <label>MOQ</label>

                        </td>
                        <td class="sel_data">
                            <input type="checkbox" id="ddlMOQChk" />

                            <select class="ddlclass" id="ddlMOQ">
                                <option>Select</option>
                            </select>
                        </td>
                    </tr>
                    <tr>
                        <td class="name_label">
                            <label>SPQ</label>

                        </td>
                        <td class="sel_data">
                            <input type="checkbox" id="ddlSPQChk" />

                            <select class="ddlclass" id="ddlSPQ">
                                <option>Select</option>

                            </select>
                        </td>
                    </tr>
                </tbody>

            </table>
            <table class="right_tbl btn_tbl">
                <tbody>
                    <tr>

                        <td class="sel_data btn_bottom">
                            <input type="button" id="btnSaveMapping" value="Save Mapping" style="margin-right: 10px; margin-left:10px" />

                            <input type="button" id="btnRetrieveMapping" value="Retrieve Mapping" />


                        </td>

                    </tr>
                </tbody>

            </table>


        </div>

        <div class="grid_area">

            <div class="note_area">Note :Quoted Unit Price and Quoted Total Price will be added by default in below grid at last position. </div>

            <div class="btn_area">
                <span style="display:none;"><input type="checkbox" id="DefaultColumnChk" checked="checked" /> Include Default Columns.</span>
                <input type="button" id="btnGenerateData" value="Generate Data" />
                <input type="button" id="btnExportData" value="Export Data" />

            </div>
            <div id="gridtable" class="grid_table" style="display:none">

                <table id="table1" class="table table-striped table-bordered nowrap  tbl_data" cellspacing="0" cellpadding="0" border="0" style="width: 100%; table-layout: auto; border-style: None; border-collapse: collapse;"></table>

            </div>

        </div>


    </div>

</body>
</html>
