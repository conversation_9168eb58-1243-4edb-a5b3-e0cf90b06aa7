///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference name="~/Controls/IconButton/IconButton.js" />
///<reference name="~/Controls/ReboundTextBox/ReboundTextBox.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.QuoteMainInfo_Close = function(element) {
	Rebound.GlobalTrader.Site.Controls.Forms.QuoteMainInfo_Close.initializeBase(this, [element]);
	this._intQuoteID = -1;
};

Rebound.GlobalTrader.Site.Controls.Forms.QuoteMainInfo_Close.prototype = {

	get_intQuoteID: function () { return this._intQuoteID; }, set_intQuoteID: function (value) { if (this._intQuoteID !== value) this._intQuoteID = value; },

	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.Forms.QuoteMainInfo_Close.callBaseMethod(this, "initialize");
		this.addShown(Function.createDelegate(this, this.formShown));
	},

	dispose: function() { 
		if (this.isDisposed) return;
		if (this._ctlConfirm) this._ctlConfirm.dispose();
		this._intQuoteID = null;
		Rebound.GlobalTrader.Site.Controls.Forms.QuoteMainInfo_Close.callBaseMethod(this, "dispose");
	},

	formShown: function() {
		if (this._blnFirstTimeShown) {
			this._ctlConfirm = this.getFieldComponent("ctlConfirm");
			this._ctlConfirm.addClickYesEvent(Function.createDelegate(this, this.yesClicked));
			this._ctlConfirm.addClickNoEvent(Function.createDelegate(this, this.noClicked));
		}
		//RP-991
		this.getFieldDropDownData("ctlReason");
	},

	yesClicked: function () {
		this.resetFormFields();
		if (!this.validateForm()) return;
		this.showSaving(true);
		var obj = new Rebound.GlobalTrader.Site.Data();
		obj.set_PathToData("Controls/Nuggets/QuoteMainInfo");
		obj.set_DataObject("QuoteMainInfo");
		obj.set_DataAction("CloseAllLines");
		obj.addParameter("id", this._intQuoteID);
		obj.addParameter("Reason", this.getFieldValue("ctlReason"));
		obj.addParameter("txtReason", this.getFieldValue("ctlTxtReasons"));
		obj.addDataOK(Function.createDelegate(this, this.saveComplete));
		obj.addError(Function.createDelegate(this, this.saveError));
		obj.addTimeout(Function.createDelegate(this, this.saveError));
		$R_DQ.addToQueue(obj);
		$R_DQ.processQueue();
		obj = null;
		this.onSave();
	},

	noClicked: function() {
		this.showSaving(false);
		this.onNotConfirmed();
	},

	saveError: function(args) {
		this.showSaving(false);
		this._strErrorMessage = args._errorMessage;
		this.onSaveError();
	},

	saveComplete: function(args) {
		this.showSaving(false);
		if (args._result.Result == true) {
			this.onSaveComplete();
		} else {
			this._strErrorMessage = args._errorMessage;
			this.onSaveError();
		}
	},
	
	validateForm: function() {
		var blnOK = this.autoValidateFields();
		return blnOK;
	}
	
};

Rebound.GlobalTrader.Site.Controls.Forms.QuoteMainInfo_Close.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.QuoteMainInfo_Close", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
