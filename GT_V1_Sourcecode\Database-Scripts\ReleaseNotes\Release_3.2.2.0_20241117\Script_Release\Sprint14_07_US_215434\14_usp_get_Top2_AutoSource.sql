﻿
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


/* 
===========================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-208621]		An.TranTan			08-Oct-2024		CREATE			Get auto sourcing in HUBRFQ(DMCC side)
[US-208621]		An.TranTan			15-Oct-2024		UPDATE			Remove quantity in conditions, add new column AddedToRequirement
[US-208621]		An.TranTan			15-Oct-2024		UPDATE			Maximum to 10 results
[US-215434]		Phuc Hoang			06-Nov-2024		Update		Lytica Price should apply fuzzy logic for inserting & displaying
===========================================================================================
*/
CREATE OR ALTER PROCEDURE [dbo].[usp_get_Top2_AutoSource]
	@ClientNo INT = 114	--DMCC
	,@PartSearch NVARCHAR(50)
	,@CustomerRequirementId INT
WITH RECOMPILE
AS
BEGIN
	SET NOCOUNT ON
	--Declare variables
	DECLARE @TargetManufacturerNo INT,
			@TargetManufacturerName NVARCHAR(256) = NULL,
			@IsRestrictMfr INT = 0,
			@TargetQuantity INT,
			@MaxRecordRequired INT = 10,	--get max 10 records
			@FromDate DATE = dbo.ufn_get_date_from_datetime(DATEADD(month, -12, GETDATE())),
			@ToDate DATE = GETDATE(),
			@CurrencyCode NVARCHAR(5) = NULL;
	DECLARE @IHSAveragePrice FLOAT = NULL,   
			@IHSPartStatus NVARCHAR(60) = NULL,  
			@IHSResult NVARCHAR(MAX) = NULL;  
	DECLARE @LyticaAveragePrice FLOAT = NULL,   
			@LyticaMarketLeading FLOAT = NULL,  
			@LyticaTargetPrice FLOAT = NULL,  
			@LyticaStatus VARCHAR(200) = NULL,  
			@LyticaResult NVARCHAR(MAX) = NULL;
	
	SELECT @CurrencyCode = cr.CurrencyCode
	FROM tbCurrency cr WITH(NOLOCK)
	JOIN tbClient cl WITH(NOLOCK) ON cl.CurrencyNo = cr.CurrencyId
	WHERE cl.ClientId = @ClientNo;

	IF OBJECT_ID('tempdb..#finalResults') IS NOT NULL
		DROP TABLE #finalResults
	CREATE TABLE #finalResults
	(
		ID INT,
		ClientNo INT,
		PartNo NVARCHAR(30),
		ManufacturerNo INT,
		ManufacturerCode NVARCHAR(10),
		ManufacturerName NVARCHAR(256),
		ProductNo INT,
		ProductName NVARCHAR(100),
		ProductDescription NVARCHAR(256),
		DateOrdered DATETIME,
		Quantity INT,
		BuyPrice FLOAT,
		SellPrice FLOAT,
		CurrencyCode NVARCHAR(5),
		DivisionStatus INT,	--for stock
		IsSourcingHub BIT,	--for strategic stock/Reverse Logistic Stock/ Offer
		ROHS TINYINT,
		SourcingType VARCHAR(5),
		SourcingTypeDescription VARCHAR(50), --Stock/ Strategic Stock/ Reverse Logistic Stock/ Offer
		SourcingTypeOrder INT
	);

	--get the target manufacturer and quantity, 
	SELECT 
		@TargetManufacturerNo = ISNULL(cr.ManufacturerNo, 0),
		@TargetManufacturerName = ISNULL(mf.ManufacturerName, '')
		--@TargetQuantity = Quantity
	FROM tbCustomerRequirement cr WITH(NOLOCK)
	LEFT JOIN tbManufacturer mf WITH(NOLOCK) on mf.ManufacturerId = cr.ManufacturerNo
	WHERE cr.CustomerRequirementId = @CustomerRequirementId;

	--check if manufacturer is restricted (for add to requirement action)
	IF EXISTS (SELECT TOP 1 1 FROM tbRestrictedManufacturer WHERE ManufacturerNo = @TargetManufacturerNo)
		SET @IsRestrictMfr = CAST(1 AS BIT);

	--get stocks matching part, manufacturer within 12 months (refer usp_source_Stock)
	;WITH cteMatchingStock AS (
		SELECT 
			st.StockId,
			st.Part,
			st.StockDate,
			cu.CurrencyId,
			st.ManufacturerNo,
			st.ProductNo,
			st.ClientNo,
			st.ROHS,
			(CASE st.Unavailable WHEN 0
				THEN (
						st.QuantityInStock + st.QuantityOnOrder - isnull((
								SELECT sum(QuantityAllocated)
								FROM tbAllocation al
								WHERE al.StockNo = st.StockId
								), 0)
						)
				ELSE 0 END
			) AS QuantityAvailable,
			dbo.ufn_convert_to_HUB_currency(st.ResalePrice, cu.CurrencyId, st.StockDate) AS ConvertedBuyPrice,
			CASE WHEN isnull(st.ClientUpLiftPrice, 0) = 0 THEN isnull(st.landedCost, 0)
				ELSE st.ClientUpLiftPrice
			END AS ClientUpLiftPrice
		FROM tbStock st WITH(NOLOCK)
		JOIN dbo.tbClient cl WITH(NOLOCK) ON cl.ClientId = st.ClientNo
		JOIN dbo.tbCurrency cu WITH(NOLOCK) ON cu.CurrencyId = cl.CurrencyNo
		WHERE 
			(
				st.ClientNo = @ClientNo 
				OR (cl.OwnDataVisibleToOthers = 1 AND st.ClientNo <> 109)
				OR (isnull(st.ClientUpLiftPrice, 0) > 0)
			)
			AND (st.FullPart LIKE @PartSearch OR st.FullSupplierPart LIKE @PartSearch)
			AND (st.QuantityInStock > 0 OR st.QuantityOnOrder > 0)
			AND (@TargetManufacturerNo = 0 OR ISNULL(st.ManufacturerNo, 0) = @TargetManufacturerNo)
			AND (dbo.ufn_get_date_from_datetime(st.StockDate) between @FromDate AND @ToDate)
	)
	INSERT INTO #finalResults
	(
		ID,
		ClientNo,
		PartNo,
		ManufacturerNo,
		ManufacturerCode,
		ManufacturerName,
		ProductNo,
		ProductName,
		ProductDescription,
		DateOrdered,
		Quantity,
		BuyPrice,
		SellPrice,
		CurrencyCode,
		DivisionStatus,
		ROHS,
		SourcingType,
		SourcingTypeDescription,
		SourcingTypeOrder
	)
	SELECT TOP (@MaxRecordRequired)
		cte.StockId,
		cte.ClientNo,
		cte.Part,
		mf.ManufacturerId,
		mf.ManufacturerCode,
		mf.ManufacturerName,
		pr.ProductId,
		pr.ProductName,
		pr.ProductDescription,
		cte.StockDate, --DateOrdered
		cte.QuantityAvailable,
		cte.ConvertedBuyPrice,
		dbo.ufn_convert_to_HUB_currency(cte.ClientUpLiftPrice, cte.CurrencyId, cte.StockDate), --sale price
		@CurrencyCode,
		CASE WHEN cte.ClientNo = @ClientNo AND cte.QuantityAvailable > 0 THEN 1
			ELSE 0
		END AS DivisionStatus, --Division Status
		cte.ROHS,
		'ST',
		'Stock',
		1 AS SourcingTypeOrder
	FROM cteMatchingStock cte
	LEFT JOIN dbo.tbProduct pr WITH(NOLOCK) ON cte.ProductNo = pr.ProductId
	LEFT JOIN dbo.tbManufacturer mf WITH(NOLOCK) ON cte.ManufacturerNo = mf.ManufacturerId
	--WHERE cte.QuantityAvailable = @TargetQuantity
	ORDER BY cte.ConvertedBuyPrice ASC,
		DivisionStatus DESC;

	--get matching strategic stock: part, manufacturer, quantity within 12 months (refer: usp_ipobom_source_Epo)
	IF (SELECT COUNT(*) FROM #finalResults) < @MaxRecordRequired
	BEGIN
		;WITH cteMatchingStrategicStock AS(
			SELECT 
				o.EpoId,
				o.Part,
				o.OriginalEntryDate,
				o.Quantity,
				o.CurrencyNo,
				o.ManufacturerNo,
				o.ProductNo,
				o.ClientNo,
				o.ROHS,
				CASE WHEN o.ClientNo = @ClientNo THEN 1 ELSE 0
				END AS IsCurrentClient,
				dbo.ufn_convert_to_HUB_currency(o.Price, o.CurrencyNo, o.OriginalEntryDate) AS ConvertedBuyPrice,
				dbo.ufn_convert_to_HUB_currency(o.UpliftPrice, o.CurrencyNo, o.OriginalEntryDate) AS ConvertedSellPrice
			FROM [BorisGlobalTraderImports].dbo.tbEpo o WITH (NOLOCK)
			JOIN tbClient cl WITH (NOLOCK)
		            ON o.ClientNo = cl.ClientId
			WHERE (o.ClientNo = @ClientNo OR cl.OwnDataVisibleToOthers = 1)
				AND o.FullPart LIKE @PartSearch
				AND (@TargetManufacturerNo = 0 OR ISNULL(o.ManufacturerNo, 0) = @TargetManufacturerNo) 
				--AND o.Quantity = @TargetQuantity
				AND (dbo.ufn_get_date_from_datetime(o.OriginalEntryDate) between @FromDate AND @ToDate)
		)
		INSERT INTO #finalResults
		(
			ID,
			ClientNo,
			PartNo,
			ManufacturerNo,
			ManufacturerCode,
			ManufacturerName,
			ProductNo,
			ProductName,
			ProductDescription,
			DateOrdered,
			Quantity,
			BuyPrice,
			SellPrice,
			CurrencyCode,
			IsSourcingHub,
			ROHS,
			SourcingType, 
			SourcingTypeDescription, --Stock/ Strategic Stock/ Reverse Logistic Stock/ Offer
			SourcingTypeOrder
		)
		SELECT TOP (@MaxRecordRequired)
			cte.EpoId,
			cte.ClientNo,
			cte.Part,
			mf.ManufacturerId,
			mf.ManufacturerCode,
			mf.ManufacturerName,
			pr.ProductId,
			pr.ProductName,
			pr.ProductDescription,
			cte.OriginalEntryDate,
			cte.Quantity,
			cte.ConvertedBuyPrice,
			cte.ConvertedSellPrice,
			@CurrencyCode,
			CAST(0 AS BIT),
			cte.ROHS,
			'SS',
			'Strategic Stock',
			2
		FROM cteMatchingStrategicStock cte
		LEFT JOIN dbo.tbManufacturer mf WITH (NOLOCK)
			ON cte.ManufacturerNo = mf.ManufacturerId
		LEFT JOIN dbo.tbProduct pr WITH(NOLOCK) ON cte.ProductNo = pr.ProductId
		ORDER BY cte.ConvertedBuyPrice ASC,
			cte.IsCurrentClient DESC; 
	END

	--get matching Reverse Logistic Stock: part, manufacturer, quantity within 12 months
	--refer: [usp_ipobom_source_ReverseLogistic]
	IF (SELECT COUNT(*) FROM #finalResults) < @MaxRecordRequired
	BEGIN
		;WITH cteMatchingRL AS(
			SELECT 
				o.ReverseLogisticId,
				o.Part,
				o.OriginalEntryDate,
				o.Quantity,
				o.CurrencyNo,
				o.ManufacturerNo,
				o.ProductNo,
				o.ClientNo,
				o.ROHS,
				CASE WHEN o.ClientNo = @ClientNo THEN 1 ELSE 0
				END AS IsCurrentClient,
				dbo.ufn_convert_to_HUB_currency(o.Price, o.CurrencyNo, o.OriginalEntryDate) AS ConvertedBuyPrice,
				dbo.ufn_convert_to_HUB_currency(o.UpliftPrice, o.CurrencyNo, o.OriginalEntryDate) AS ConvertedSellPrice
			FROM [BorisGlobalTraderImports].dbo.tbReverseLogistic o WITH (NOLOCK)
			JOIN tbClient cl WITH (NOLOCK) 
				ON o.ClientNo = cl.ClientId
			WHERE (o.ClientNo = @ClientNo OR cl.OwnDataVisibleToOthers = 1)
				AND o.FullPart LIKE @PartSearch
				AND (@TargetManufacturerNo = 0 OR ISNULL(o.ManufacturerNo, 0) = @TargetManufacturerNo)
				--AND o.Quantity = @TargetQuantity
				AND (dbo.ufn_get_date_from_datetime(o.OriginalEntryDate) between @FromDate AND @ToDate)
		)
		INSERT INTO #finalResults
		(
			ID,
			ClientNo,
			PartNo,
			ManufacturerNo,
			ManufacturerCode,
			ManufacturerName,
			ProductNo,
			ProductName,
			ProductDescription,
			DateOrdered,
			Quantity,
			BuyPrice,
			SellPrice,
			CurrencyCode,
			IsSourcingHub,
			ROHS,
			SourcingType,
			SourcingTypeDescription, --Stock/ Strategic Stock/ Reverse Logistic Stock/ Offer
			SourcingTypeOrder
		)
		SELECT TOP (@MaxRecordRequired)
			cte.ReverseLogisticId,
			cte.ClientNo,
			cte.Part,
			mf.ManufacturerId,
			mf.ManufacturerCode,
			mf.ManufacturerName,
			pr.ProductId,
			pr.ProductName,
			pr.ProductDescription,
			cte.OriginalEntryDate,
			cte.Quantity,
			cte.ConvertedBuyPrice,
			cte.ConvertedSellPrice,
			@CurrencyCode,
			CAST(0 AS BIT),
			cte.ROHS,
			'RL',
			'Reverse Logistic',
			3
		FROM cteMatchingRL cte
		LEFT JOIN dbo.tbManufacturer mf WITH (NOLOCK)
			ON cte.ManufacturerNo = mf.ManufacturerId
		LEFT JOIN dbo.tbProduct pr WITH(NOLOCK) ON cte.ProductNo = pr.ProductId
		ORDER BY cte.ConvertedBuyPrice ASC, cte.IsCurrentClient DESC;
	END

	--get matching offer: part, manufacturer, quantity within 12 months
	--refer usp_IPOBOM_Source_OfferPH
	IF (SELECT COUNT(*) FROM #finalResults) < @MaxRecordRequired
	BEGIN
		;WITH cteMatchingOffer AS(
			SELECT 
				o.OfferId,
				o.Part,
				dbo.ufn_convert_to_HUB_currency(o.Price, o.CurrencyNo, o.OriginalEntryDate) AS ConvertedBuyPrice,
				o.ManufacturerNo,
				o.ProductNo,
				o.OriginalEntryDate,
				o.Quantity,
				o.ClientNo,
				CAST(0 AS BIT) AS IsHub,
				o.ROHS,
				CASE WHEN o.ClientNo = @ClientNo THEN 1 ELSE 0 
				END AS IsCurrentClient
			FROM [BorisGlobalTraderImports].dbo.tbOffer o WITH (NOLOCK)
			WHERE 
				o.FullPart LIKE @PartSearch
				AND (@TargetManufacturerNo = 0 OR ISNULL(o.ManufacturerNo, 0) = @TargetManufacturerNo)
				--AND o.Quantity = @TargetQuantity
				AND (dbo.ufn_get_date_from_datetime(o.OriginalEntryDate) between @FromDate AND @ToDate)
			UNION ALL
			SELECT 
				vw.OfferId,
				vw.Part,
				dbo.ufn_convert_to_HUB_currency(vw.Price, vw.CurrencyNo, vw.OriginalEntryDate) AS ConvertedBuyPrice,
				vw.ManufacturerNo,
				vw.ProductNo,
				vw.OriginalEntryDate,
				vw.Quantity,
				vw.ClientNo,
				vw.ishub AS IsHub,
				vw.ROHS,
				CASE WHEN vw.ClientNo = @ClientNo THEN 1 ELSE 0 
				END AS IsCurrentClient
			FROM [vwSourcingData] vw
			WHERE
				vw.FullPart LIKE @PartSearch
				AND (@TargetManufacturerNo = 0 OR ISNULL(vw.ManufacturerNo, 0) = @TargetManufacturerNo)
				--AND vw.Quantity = @TargetQuantity
				AND (dbo.ufn_get_date_from_datetime(vw.OriginalEntryDate) between @FromDate AND @ToDate)
		)
		INSERT INTO #finalResults
		(
			ID,
			ClientNo,
			PartNo,
			ManufacturerNo,
			ManufacturerCode,
			ManufacturerName,
			ProductNo,
			ProductName,
			ProductDescription,
			DateOrdered,
			Quantity,
			BuyPrice,
			SellPrice,
			CurrencyCode,
			IsSourcingHub,
			ROHS,
			SourcingType,
			SourcingTypeDescription, --Stock/ Strategic Stock/ Reverse Logistic Stock/ Offer
			SourcingTypeOrder
		)
		SELECT TOP (@MaxRecordRequired)
			cte.OfferId,
			cte.ClientNo,
			cte.Part,
			mf.ManufacturerId,
			mf.ManufacturerCode,
			mf.ManufacturerName,
			pr.ProductId,
			pr.ProductName,
			pr.ProductDescription,
			cte.OriginalEntryDate,
			cte.Quantity,
			cte.ConvertedBuyPrice,
			0,
			@CurrencyCode,
			cte.IsHub,
			cte.ROHS,
			'OF',
			'Offer',
			4
		FROM cteMatchingOffer cte
		LEFT JOIN tbManufacturer mf WITH(NOLOCK) ON mf.ManufacturerId = cte.ManufacturerNo
		LEFT JOIN tbProduct pr WITH(NOLOCK) ON pr.ProductId = cte.ProductNo
		ORDER BY cte.ConvertedBuyPrice ASC, cte.IsCurrentClient DESC;
	END

	--IHS
	SELECT TOP 1 
		@IHSAveragePrice = ISNULL(AveragePrice, dbo.ufn_extract_IHS_AvgPrice(Descriptions)),  
		@IHSPartStatus = PartStatus  
	FROM tbIHSparts WITH (NOLOCK)  
	WHERE FullPart LIKE @PartSearch  
	 AND (@TargetManufacturerNo = 0 OR ISNULL(ManufacturerNo, 0) = @TargetManufacturerNo)  
	 AND ISNULL(Inactive, 0) = 0  
	ORDER BY DLUP DESC

	SET @IHSResult = 'AV Price: ' + CASE WHEN @IHSAveragePrice IS NULL THEN '0.00'  
         ELSE CAST(@IHSAveragePrice AS NVARCHAR(25))  
        END  
    + '&nbsp;&nbsp; P/Status: ' + ISNULL(@IHSPartStatus, 'N/A'); 

	--Lytica
	SELECT TOP 1 
		@LyticaAveragePrice = AveragePrice,  
	    @LyticaMarketLeading = MarketLeading,  
	    @LyticaTargetPrice = TargetPrice,  
	    @LyticaStatus = lifeCycleStatus  
	FROM tbLyticaAPI WITH (NOLOCK)  
	WHERE dbo.ufn_get_fullpart(OriginalPartSearched) LIKE @PartSearch  
	 AND ISNULL(Inactive, 0) = 0 
	 AND (ISNULL(AveragePrice, 0) + ISNULL(TargetPrice, 0) + ISNULL(MarketLeading, 0)) > 0
	 AND (
			Manufacturer = ISNULL(@TargetManufacturerName, '') 
			OR Manufacturer LIKE ISNULL(@TargetManufacturerName, '')  + '%'
			OR @TargetManufacturerName LIKE ISNULL(Manufacturer, '') + '%' 
			OR Manufacturer LIKE ISNULL([dbo].[ufn_GetFirstWord](@TargetManufacturerName), '') + '%'
		);

	SET @LyticaResult = 'AV Price: ' + CASE WHEN @LyticaAveragePrice IS NULL THEN '0.00'  
          ELSE CAST(@LyticaAveragePrice AS NVARCHAR(25))  
         END  
     + '&nbsp;&nbsp; M/Leading: ' + CASE WHEN @LyticaMarketLeading IS NULL THEN '0.00'  
              ELSE CAST(@LyticaMarketLeading AS NVARCHAR(25))  
               END  
     + '&nbsp;&nbsp; Target: ' + CASE WHEN @LyticaTargetPrice IS NULL THEN '0.00'  
             ELSE CAST(@LyticaTargetPrice AS NVARCHAR(25))  
            END  
     + '&nbsp;&nbsp; P/Status: ' + ISNULL(@LyticaStatus, 'N/A')
	 
	--final results
	SELECT TOP (@MaxRecordRequired)
		ID,
		ClientNo,
		PartNo,
		ManufacturerNo,
		ManufacturerCode,
		ManufacturerName,
		ProductNo,
		ProductName,
		ProductDescription,
		DateOrdered,
		Quantity,
		CONVERT(DECIMAL(16,5),ISNULL(BuyPrice, 0)) AS BuyPrice,
		CONVERT(DECIMAL(16,5),ISNULL(SellPrice, 0)) AS SellPrice,
		CurrencyCode,
		ISNULL(DivisionStatus, 0) AS DivisionStatus,
		@IsRestrictMfr AS IsRestrictMfr,
		ISNULL(IsSourcingHub, 0) AS IsSourcingHub,
		CAST(ISNULL(ROHS, 0) AS TINYINT) AS ROHS,
		SourcingType,
		SourcingTypeDescription,
		CASE 
			WHEN SourcingType = 'ST' 
				AND EXISTS(SELECT 1 FROM dbo.tbSourcingResult sr WITH(NOLOCK)
							WHERE sr.CustomerRequirementNo = @CustomerRequirementId 
								AND sr.SourcingTableItemNo = ID 
								AND sr.SourcingTable = 'HUBSTK'
				) THEN CAST(1 AS BIT)
			WHEN SourcingType = 'SS'
				AND EXISTS(SELECT 1 FROM dbo.tbSourcingResult sr WITH(NOLOCK)
							WHERE sr.CustomerRequirementNo = @CustomerRequirementId
								AND sr.SourcingTableItemNo = ID 
								AND sr.SourcingTable = 'EPPH'
				) THEN CAST(1 AS BIT)
			WHEN SourcingType = 'RL'
				AND EXISTS(SELECT 1 FROM dbo.tbSourcingResult sr WITH(NOLOCK)
							WHERE sr.CustomerRequirementNo = @CustomerRequirementId 
								AND sr.SourcingTableItemNo = ID 
								AND sr.SourcingTable = 'RLPH'
				) THEN CAST(1 AS BIT)
			WHEN SourcingType = 'OF'
				AND EXISTS(SELECT 1 FROM dbo.tbSourcingResult sr WITH(NOLOCK)
							WHERE sr.CustomerRequirementNo = @CustomerRequirementId 
								AND sr.SourcingTableItemNo = ID 
								AND sr.SourcingTable = 'OFPH'
				) THEN CAST(1 AS BIT)
			ELSE CAST(0 AS BIT)
		END AS AddedToRequirement
	FROM #finalResults
	ORDER BY SourcingTypeOrder ASC, 
			BuyPrice ASC;

	SELECT @IHSResult AS IHSResult;
	SELECT @LyticaResult AS LyticaResult;

	DROP TABLE #finalResults
	SET NOCOUNT OFF
END

/*test script
exec usp_get_Top2_AutoSource
	@ClientNo  = 114	
	,@PartSearch = 'STM32F030K6T6%'
	,@CustomerRequirementId  = 5859979
*/
GO


