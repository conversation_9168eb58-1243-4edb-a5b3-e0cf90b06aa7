Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");Rebound.GlobalTrader.Site.Controls.DropDowns.SellShipMethod=function(n){Rebound.GlobalTrader.Site.Controls.DropDowns.SellShipMethod.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.DropDowns.SellShipMethod.prototype={get_intGlobalLoginClientNo:function(){return this._intGlobalLoginClientNo},set_intGlobalLoginClientNo:function(n){this._intGlobalLoginClientNo!==n&&(this._intGlobalLoginClientNo=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.DropDowns.SellShipMethod.callBaseMethod(this,"initialize");this.addSetupDataCall(Function.createDelegate(this,this.setupDataCall));this.addGetDataOK(Function.createDelegate(this,this.dataCallOK))},dispose:function(){this.isDisposed||(this._intGlobalLoginClientNo=null,Rebound.GlobalTrader.Site.Controls.DropDowns.SellShipMethod.callBaseMethod(this,"dispose"))},setupDataCall:function(){this._objData.set_PathToData("controls/DropDowns/SellShipMethod");this._objData.set_DataObject("SellShipMethod");this._objData.set_DataAction("GetData");this._objData.addParameter("GlobalLoginClientNo",this._intGlobalLoginClientNo)},dataCallOK:function(){var t=this._objData._result,n;if(t.ShipVias)for(n=0;n<t.ShipVias.length;n++)this.addOption(t.ShipVias[n].Name,t.ShipVias[n].ID)}};Rebound.GlobalTrader.Site.Controls.DropDowns.SellShipMethod.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.SellShipMethod",Rebound.GlobalTrader.Site.Controls.DropDowns.Base);