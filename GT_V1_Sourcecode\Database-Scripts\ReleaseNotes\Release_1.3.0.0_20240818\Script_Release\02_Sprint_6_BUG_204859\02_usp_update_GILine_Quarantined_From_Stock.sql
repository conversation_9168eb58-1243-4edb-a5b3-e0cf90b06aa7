﻿SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

-- =============================================
-- Author:		Cuongdx
-- Create date: 12 Jul 2024
-- Description:	Update quarantine for the GI from Stock
-- =============================================
CREATE OR ALTER PROCEDURE usp_update_GILine_Quarantined_From_Stock
	-- Add the parameters for the stored procedure here
	@GoodsInLineId int,
    @UpdatedBy int,
	@ClientNo INT = 101
AS
BEGIN
	BEGIN TRANSACTION
	/*---- BUG-204859 start ---------*/
	/* unallocate all stock relate to that goods in line */
	declare @allocationId int

	DECLARE db_cursorGIAllocation CURSOR FOR
	select AllocationId
	from tbAllocation a
		inner join tbStock sk
			ON sk.StockId = a.StockNo
		inner join tbPurchaseOrderLine pol
			ON sk.PurchaseOrderLineNo = pol.PurchaseOrderLineId
		inner join tbGoodsInLine gil
			on gil.PurchaseOrderLineNo = pol.PurchaseOrderLineId
	where gil.GoodsInLineId = @GoodsInLineId

	OPEN db_cursorGIAllocation

	FETCH next FROM db_cursorGIAllocation
	INTO @allocationId
	WHILE @@FETCH_STATUS = 0
	BEGIN
		EXEC usp_delete_Allocation @allocationId,@UpdatedBy

		FETCH NEXT FROM db_cursorGIAllocation INTO @allocationId;
	END
	CLOSE db_cursorGIAllocation
	DEALLOCATE db_cursorGIAllocation

	/*-- send message response to decline all query --*/
	--1. get all query from a goods in line
	declare 
			@GI_QueryId INT,
			@GoodInNo INT,
			@GILineNo INT,
			@LoginId INT,
			@QueryMessage NVARCHAR(3000),
			@SalesApprovalStatus INT = 0,
			@QualityApprovalStatus INT = 0,
			@PurchasingApprovalStatus INT = 0,
			@ApproverHtml NVARCHAR(MAX) = NULL,
			@TotalCheckBoxcount INT = 0,
			@GetEnableCheckBoxIds NVARCHAR(1000) = ''

	DECLARE db_cursorGIQueryMessage CURSOR FOR
	SELECT  GI_QueryId,GoodInNo,GILineNo,SalesApprovalStatus,PurchasingApprovalStatus,QualityApprovalStatus
	FROM tbGI_QueryMessageApprovals WHERE GILineNo = @GoodsInLineId
	
	--get checkbox ids
	SELECT @GetEnableCheckBoxIds = STUFF((
    SELECT ',' + col
    FROM (
        SELECT CASE WHEN C1 = 1 THEN '1' ELSE NULL END AS col  from tbGI_QueryApprovalResponse where GoodsInLineNo = @GoodsInLineId
        UNION ALL
        SELECT CASE WHEN C2 = 1 THEN '2' ELSE NULL END AS col  from tbGI_QueryApprovalResponse where GoodsInLineNo = @GoodsInLineId
        UNION ALL
        SELECT CASE WHEN C3 = 1 THEN '3' ELSE NULL END AS col from tbGI_QueryApprovalResponse where GoodsInLineNo = @GoodsInLineId
        UNION ALL
        SELECT CASE WHEN C4 = 1 THEN '4' ELSE NULL END AS col from tbGI_QueryApprovalResponse where GoodsInLineNo = @GoodsInLineId
        UNION ALL
        SELECT CASE WHEN C5 = 1 THEN '5' ELSE NULL END AS col from tbGI_QueryApprovalResponse where GoodsInLineNo = @GoodsInLineId
        UNION ALL
        SELECT CASE WHEN C6 = 1 THEN '6' ELSE NULL END AS col from tbGI_QueryApprovalResponse where GoodsInLineNo = @GoodsInLineId
        UNION ALL
        SELECT CASE WHEN C7 = 1 THEN '7' ELSE NULL END AS col from tbGI_QueryApprovalResponse where GoodsInLineNo = @GoodsInLineId
        UNION ALL
        SELECT CASE WHEN C8 = 1 THEN '8' ELSE NULL END AS col from tbGI_QueryApprovalResponse where GoodsInLineNo = @GoodsInLineId
        UNION ALL
        SELECT CASE WHEN C9 = 1 THEN '9' ELSE NULL END AS col from tbGI_QueryApprovalResponse where GoodsInLineNo = @GoodsInLineId
        UNION ALL
        SELECT CASE WHEN C10 = 1 THEN '10' ELSE NULL END AS col from tbGI_QueryApprovalResponse where GoodsInLineNo = @GoodsInLineId
    ) AS sub
    WHERE col IS NOT NULL
    FOR XML PATH('')), 1, 1, '');

	--count total checkbox count
	SELECT @TotalCheckBoxcount = 
		(CASE WHEN C1 = 1 THEN 1 ELSE 0 END +
		 CASE WHEN C2 = 1 THEN 1 ELSE 0 END +
		 CASE WHEN C3 = 1 THEN 1 ELSE 0 END +
		 CASE WHEN C4 = 1 THEN 1 ELSE 0 END +
		 CASE WHEN C5 = 1 THEN 1 ELSE 0 END +
		 CASE WHEN C6 = 1 THEN 1 ELSE 0 END +
		 CASE WHEN C7 = 1 THEN 1 ELSE 0 END +
		 CASE WHEN C8 = 1 THEN 1 ELSE 0 END +
		 CASE WHEN C9 = 1 THEN 1 ELSE 0 END +
		 CASE WHEN C10 = 1 THEN 1 ELSE 0 END)
	FROM tbGI_QueryApprovalResponse 
	WHERE GoodsInLineNo = @GoodsInLineId

	OPEN db_cursorGIQueryMessage

	FETCH next FROM db_cursorGIQueryMessage
	INTO @GI_QueryId,@GoodInNo,@GILineNo,@SalesApprovalStatus,@PurchasingApprovalStatus,@QualityApprovalStatus
	WHILE @@FETCH_STATUS = 0
	BEGIN
		--2. send all query set as status = 2 means declined
		SET @SalesApprovalStatus =
		(
			SELECT CASE WHEN @SalesApprovalStatus is not null THEN 2 ELSE NULL END
		)
		SET @QualityApprovalStatus =
		(
			SELECT CASE WHEN @QualityApprovalStatus is not null THEN 2 ELSE NULL END
		)
		SET @PurchasingApprovalStatus =
		(
			SELECT CASE WHEN @PurchasingApprovalStatus is not null THEN 2 ELSE NULL END
		)
		EXEC usp_Insert_GILineQueryApprovalResponce
										   @GI_QueryId = @GI_QueryId
										  ,@GoodInNo = @GoodInNo
										  ,@GILineNo = @GILineNo
										  ,@LoginId = @UpdatedBy
										  ,@QueryMessage = N'Query is closed (declined) since this GI is quarantined'
										  ,@SalesApprovalStatus = @SalesApprovalStatus
										  ,@QualityApprovalStatus = @QualityApprovalStatus
										  ,@PurchasingApprovalStatus = @PurchasingApprovalStatus
										  ,@ClientNo = @ClientNo
										  ,@TotalCheckBoxcount = @TotalCheckBoxcount
										  ,@CheckedTotalCheckBoxcount =  @TotalCheckBoxcount
										  ,@GetEnableCheckBoxIds = @GetEnableCheckBoxIds
		FETCH NEXT FROM db_cursorGIQueryMessage
		INTO @GI_QueryId,@GoodInNo,@GILineNo,@SalesApprovalStatus,@PurchasingApprovalStatus,@QualityApprovalStatus
	END
	CLOSE db_cursorGIQueryMessage
	DEALLOCATE db_cursorGIQueryMessage

	/*--release the GI line--*/
	EXEC usp_update_GoodsInLine_Inspect @GoodsInLineId,@UpdatedBy
    COMMIT TRANSACTION
END	

GO
