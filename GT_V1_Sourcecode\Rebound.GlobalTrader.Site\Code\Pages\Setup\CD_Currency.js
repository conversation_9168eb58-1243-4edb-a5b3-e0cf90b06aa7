Type.registerNamespace("Rebound.GlobalTrader.Site.Pages.Setup");Rebound.GlobalTrader.Site.Pages.Setup.CD_Currency=function(n){Rebound.GlobalTrader.Site.Pages.Setup.CD_Currency.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Pages.Setup.CD_Currency.prototype={get_ctlCurrency:function(){return this._ctlCurrency},set_ctlCurrency:function(n){this._ctlCurrency!==n&&(this._ctlCurrency=n)},get_ctlCurrencyRates:function(){return this._ctlCurrencyRates},set_ctlCurrencyRates:function(n){this._ctlCurrencyRates!==n&&(this._ctlCurrencyRates=n)},initialize:function(){Rebound.GlobalTrader.Site.Pages.Setup.CD_Currency.callBaseMethod(this,"initialize")},goInit:function(){this._ctlCurrency&&this._ctlCurrency.addSelectCurrency(Function.createDelegate(this,this.ctlCurrency_SelectCurrency));this._ctlCurrencyRates&&this._ctlCurrencyRates.addChangedData(Function.createDelegate(this,this.ctlCurrencyRates_ChangedData));Rebound.GlobalTrader.Site.Pages.Setup.CD_Currency.callBaseMethod(this,"goInit")},dispose:function(){this.isDisposed||(this._ctlCurrency&&this._ctlCurrency.dispose(),this._ctlCurrencyRates&&this._ctlCurrencyRates.dispose(),this._ctlCurrency=null,this._ctlCurrencyRates=null,Rebound.GlobalTrader.Site.Pages.Setup.CD_Currency.callBaseMethod(this,"dispose"))},ctlCurrency_SelectCurrency:function(){this._ctlCurrencyRates._intCurrencyID=this._ctlCurrency._intCurrencyID;this._ctlCurrency._tbl.resizeColumns();this._ctlCurrencyRates._strCurrency=this._ctlCurrency._strCurrency;this._ctlCurrencyRates.show(!this._ctlCurrency._blnCurrencyIsBase);this._ctlCurrencyRates.refresh()},ctlCurrencyRates_ChangedData:function(){this._ctlCurrency.getData()}};Rebound.GlobalTrader.Site.Pages.Setup.CD_Currency.registerClass("Rebound.GlobalTrader.Site.Pages.Setup.CD_Currency",Rebound.GlobalTrader.Site.Pages.Content,Sys.IDisposable);