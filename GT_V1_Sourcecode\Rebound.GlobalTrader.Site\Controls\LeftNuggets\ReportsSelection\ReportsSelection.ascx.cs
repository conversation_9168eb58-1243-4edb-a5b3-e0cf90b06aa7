using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site;

namespace Rebound.GlobalTrader.Site.Controls.LeftNuggets {
	public partial class ReportsSelection : Selection {

		private int _intReportID;

		protected override void OnInit(EventArgs e) {
			SetLeftNuggetType("ReportsSelection");
			base.OnInit(e);
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.LeftNuggets.Base", ctlDesignBase.ClientID);
			_intReportID = _objQSManager.ReportID;
			AddItems();
		}

		private void AddItems() {
			HtmlControl ul = new HtmlGenericControl("ul");
			string strPrevCategory = "";
			foreach (BLL.Report rpt in BLL.Report.GetListInSameGroupForLogin(SessionManager.LoginID, _intReportID)) {
				if (rpt.ReportCategoryName != strPrevCategory) {
					ul.Controls.Add(AddHeading(Functions.GetGlobalResource("Reports", rpt.ReportCategoryName)));
				}
				ul.Controls.Add(AddReportItem((BLL.Report.List)rpt.ReportId));
				strPrevCategory = rpt.ReportCategoryName;
			}
			_plhItems.Controls.Add(ul);
		}

		private HtmlControl AddReportItem(BLL.Report.List enmReport) {
			HtmlControl li = new HtmlGenericControl("li");
			HyperLink hyp = ControlBuilders.CreateHyperLinkInsideParent(li);
			hyp.NavigateUrl = string.Format("~/{0}", PageManager.GotoURL_Report((int)enmReport));
			hyp.Text = Functions.GetGlobalResource("Reports", enmReport);
			return li;
		}
	}
}