Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.HomeNuggets");Rebound.GlobalTrader.Site.Controls.HomeNuggets.UnProcessSalesOrders=function(n){Rebound.GlobalTrader.Site.Controls.HomeNuggets.UnProcessSalesOrders.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.HomeNuggets.UnProcessSalesOrders.prototype={get_pnlOpen:function(){return this._pnlOpen},set_pnlOpen:function(n){this._pnlOpen!==n&&(this._pnlOpen=n)},get_pnlOverdue:function(){return this._pnlOverdue},set_pnlOverdue:function(n){this._pnlOverdue!==n&&(this._pnlOverdue=n)},get_tblOpen:function(){return this._tblOpen},set_tblOpen:function(n){this._tblOpen!==n&&(this._tblOpen=n)},get_tblOverdue:function(){return this._tblOverdue},set_tblOverdue:function(n){this._tblOverdue!==n&&(this._tblOverdue=n)},get_pnlMore:function(){return this._pnlMore},set_pnlMore:function(n){this._pnlMore!==n&&(this._pnlMore=n)},get_myLoginID:function(){return this.myLoginID},set_myLoginID:function(n){this.myLoginID!==n&&(this.myLoginID=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.HomeNuggets.UnProcessSalesOrders.callBaseMethod(this,"initialize");this.getData();this.addRefreshEvent(Function.createDelegate(this,this.getData))},dispose:function(){this.isDisposed||(this._tblOpen&&this._tblOpen.dispose(),this._tblOverdue&&this._tblOverdue.dispose(),this._pnlOpen=null,this._pnlOverdue=null,this._tblOpen=null,this._tblOverdue=null,this._pnlMore=null,Rebound.GlobalTrader.Site.Controls.HomeNuggets.UnProcessSalesOrders.callBaseMethod(this,"dispose"))},setupLoadingState:function(){$R_FN.showElement(this._pnlMore,!1);$R_FN.showElement(this._pnlOpen,!1);$R_FN.showElement(this._pnlOverdue,!1);Rebound.GlobalTrader.Site.Controls.HomeNuggets.UnProcessSalesOrders.callBaseMethod(this,"setupLoadingState")},showNoData:function(n){this.showContent(!0);$R_FN.showElement(this._pnlNoData,n)},getData:function(){this.setupLoadingState();var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/HomeNuggets/UnProcessSalesOrders");n.set_DataObject("UnProcessSalesOrders");n.set_DataAction("GetData");n.addParameter("rowcount",this._intRowCount);n.addParameter("OtherLoginID",this._intLoginID_Other);n.addDataOK(Function.createDelegate(this,this.getDataComplete));n.addError(Function.createDelegate(this,this.homeNuggetDataError));n.addTimeout(Function.createDelegate(this,this.homeNuggetDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getDataComplete:function(n){var i,u,t,r;for(this.showNoData(n._result.Count==0),$R_FN.showElement(this._pnlMore,!0),i=n._result,this._tblOpen.clearTable(),r=0;r<i.OpenSO.length;r++)t=i.OpenSO[r],u=[$RGT_nubButton_SalesOrder(t.ID,t.No),$RGT_nubButton_Company(t.CMNo,t.CM),t.Due],this._tblOpen.addRow(u,null),t=null;$R_FN.showElement(this._pnlOpen,i.OpenSO.length>0);this.hideLoading()}};Rebound.GlobalTrader.Site.Controls.HomeNuggets.UnProcessSalesOrders.registerClass("Rebound.GlobalTrader.Site.Controls.HomeNuggets.UnProcessSalesOrders",Rebound.GlobalTrader.Site.Controls.HomeNuggets.Base,Sys.IDisposable);