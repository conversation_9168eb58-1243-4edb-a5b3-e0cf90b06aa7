///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//----------------------------------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//----------------------------------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.ItemSearch");

Rebound.GlobalTrader.Site.Controls.ItemSearch.SalesOrderLinesAuthorised = function(element) {
	Rebound.GlobalTrader.Site.Controls.ItemSearch.SalesOrderLinesAuthorised.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.ItemSearch.SalesOrderLinesAuthorised.prototype = {

	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.ItemSearch.SalesOrderLinesAuthorised.callBaseMethod(this, "initialize");
		this.addSetupData(Function.createDelegate(this, this.doSetupData));
		this.addGetDataComplete(Function.createDelegate(this, this.doGetDataComplete));	

		//var ddlSalesOrderByIvoice = $find('ctl00_cphMain_ctlManufacturersSupplied_ctlDB_ctl13_ddlMfrGroupBySupplier');
		//if (ddlSalesOrderByIvoice) {
		//	ddlSalesOrderByIvoice._intInvoiceID = this.GetParameterValues("inv");
		//}
	},

	dispose: function() { 
		if (this.isDisposed) return;
		Rebound.GlobalTrader.Site.Controls.ItemSearch.SalesOrderLinesAuthorised.callBaseMethod(this, "dispose");
	},
	
	doSetupData: function() {
		this._objData.set_PathToData("controls/ItemSearch/SalesOrderLinesAuthorised");
		this._objData.set_DataObject("SalesOrderLinesAuthorised");
		this._objData.set_DataAction("GetData");
		this._objData.addParameter("Part", this.getFieldValue("ctlPartNo"));
		this._objData.addParameter("CMName", this.getFieldValue("ctlCompany"));
		this._objData.addParameter("IncludeClosed", this.getFieldValue("ctlIncludeClosed"));
		this._objData.addParameter("CustomerPO", this.getFieldValue("ctlCustomerPO"));
		this._objData.addParameter("SalesOrderNo", this.getFieldValue("ctlSalesOrderNo"));
		this._objData.addParameter("DateOrderedFrom", this.getFieldValue("ctlDateOrderedFrom"));
		this._objData.addParameter("DateOrderedTo", this.getFieldValue("ctlDateOrderedTo"));
		this._objData.addParameter("DatePromisedFrom", this.getFieldValue("ctlDatePromisedFrom"));
		this._objData.addParameter("DatePromisedTo", this.getFieldValue("ctlDatePromisedTo"));
		this._objData.addParameter("Salesman", this.getFieldValue("ctlSalesman"));

		var baseUrl = (window.location).href;
		var url = new URL(baseUrl);
		var invoiceID = url.searchParams.get("inv");

		this._objData.addParameter("InvoiceNo", invoiceID);
	},
	
	doGetDataComplete: function() {
		for (var i = 0, l = this._objResult.Results.length; i < l; i++) {
			var row = this._objResult.Results[i];
			var aryData = [
				row.No,
				$R_FN.setCleanTextValue(row.CMName),
				$R_FN.writePartNo(row.Part, row.ROHS),
				$R_FN.setCleanTextValue(row.Date),
				row.Price,
				row.Quantity,
				$R_FN.setCleanTextValue(row.Salesman),
				$R_FN.setCleanTextValue(row.CustomerPO),
				row.Cost,
				
			];
			this._tblResults.addRow(aryData, row.ID, false);
			aryData = null; row = null;
		}
	},
	GetParameterValues: function (param) {
		var url = window.location.href.slice(window.location.href.indexOf('?') + 1).split('&');
		for (var i = 0; i < url.length; i++) {
			var urlparam = url[i].split('=');
			if (urlparam[0] == param) {
				return urlparam[1];
			}
		}
	}
};

Rebound.GlobalTrader.Site.Controls.ItemSearch.SalesOrderLinesAuthorised.registerClass("Rebound.GlobalTrader.Site.Controls.ItemSearch.SalesOrderLinesAuthorised", Rebound.GlobalTrader.Site.Controls.ItemSearch.Base, Sys.IDisposable);
