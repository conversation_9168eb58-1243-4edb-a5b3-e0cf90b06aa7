<%@ Control Language="C#" CodeBehind="CompanyCertificate_Add.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Forms.CompanyCertificate_Add" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>

<ReboundUI_Form:DesignBase ID="ctlDB" runat="server" ShowRequiredFieldsExplanation="true" ShowQuickHelp="false" NumberOfSteps="3" MultiStepControlID="ctlMultiStep">

	<Links>
		<ReboundUI:IconButton ID="ibtnSave" runat="server" IconGroup="Nugget" IconTitleResource="Save" IconCSSType="Save" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
		<ReboundUI:IconButton ID="ibtnCancel" runat="server" IconGroup="Nugget" IconTitleResource="Cancel" IconCSSType="Cancel" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
	</Links>

	<Explanation><%=Functions.GetGlobalResource("FormExplanations", "CompanyCertificate_Add")%></Explanation>
	
	
	<Content>
	
	
		<ReboundUI_Table:Form id="frmCertificate" runat="server">
		
		    <ReboundUI_Form:FormField id="ctlCategory" runat="server" FieldID="ddlCategory" ResourceTitle="CertificateCategoryName" IsRequiredField="true">
				<Field><ReboundDropDown:CertificateCategory ID="ddlCategory" runat="server" /></Field>
			</ReboundUI_Form:FormField>
			
			<ReboundUI_Form:FormField id="ctlCertificate" runat="server" FieldID="ddlCertificate" ResourceTitle="CertificateName" IsRequiredField="true">
				<Field><ReboundDropDown:Certificate ID="ddlCertificate" runat="server" /></Field>
			</ReboundUI_Form:FormField>
			
			<ReboundUI_Form:FormField id="ctlCertificateNumbre" runat="server" FieldID="txtCertificateNumber" ResourceTitle="CertificateNumber" IsRequiredField="true">
				<Field><ReboundUI:ReboundTextBox ID="txtCertificateNumber" runat="server" Width="140"  MaxLength="20" /></Field>
			</ReboundUI_Form:FormField>
	
			<ReboundUI_Form:FormField id="ctlStartDate" runat="server" FieldID="txtStartDate" ResourceTitle="StartDate" >
				<Field>
					<ReboundUI:ReboundTextBox ID="txtStartDate" runat="server" Width="140" />
					<ReboundUI:Calendar ID="calStartDate" runat="server" RelatedTextBoxID="txtStartDate" />
				</Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlExpiryDate" runat="server" FieldID="txtExpiryDate" ResourceTitle="ExpiryDate" IsRequiredField="true">
				<Field>
					<ReboundUI:ReboundTextBox ID="txtExpiryDate" runat="server" Width="140" />
					<ReboundUI:Calendar ID="calExpiryDate" runat="server" RelatedTextBoxID="txtExpiryDate" />
				</Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlInActive" runat="server" FieldID="chkInActive" ResourceTitle="IsInactive">
				<Field><ReboundUI:ImageCheckBox ID="chkInActive" runat="server" Enabled="true" /></Field>
			</ReboundUI_Form:FormField>
			
				
			<ReboundUI_Form:FormField id="ctlDescription" runat="server" FieldID="txtDescription" ResourceTitle="Description">
				<Field><ReboundUI:ReboundTextBox ID="txtDescription" runat="server" Width="400" TextMode="multiLine" Rows="2" /></Field>
			</ReboundUI_Form:FormField>

		</ReboundUI_Table:Form>

	</Content>
	
</ReboundUI_Form:DesignBase>
