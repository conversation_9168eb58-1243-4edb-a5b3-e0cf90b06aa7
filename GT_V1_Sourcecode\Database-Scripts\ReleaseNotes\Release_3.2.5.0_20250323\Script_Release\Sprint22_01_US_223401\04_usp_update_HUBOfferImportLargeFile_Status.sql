﻿GO
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


/*
=========================================================================================================================================================
TASK			UPDATED BY       DATE				ACTION		DESCRIPTION  
[US-223401]     Phuc Hoang		 20-Feb-2025		CREATE		IPO - Bulk Offer Import tool - Apply fuzzy matching and rationalize data before importing
=========================================================================================================================================================
*/
CREATE OR ALTER PROC [dbo].[usp_update_HUBOfferImportLargeFile_Status] (
	@FileId int,
	@Status NVARCHAR(100),
	@UpdatedBy INT,
	@RecordCount INT OUTPUT
)
AS

BEGIN
	BEGIN TRANSACTION;
		UPDATE BorisGlobalTraderImports.dbo.tbHUBOfferImportLargeFile 
		SET 
		[status] = @Status,
		UpdatedBy = @UpdatedBy
		WHERE ID = @FileId

		SET @RecordCount = @@ROWCOUNT
	COMMIT TRANSACTION;
END

GO