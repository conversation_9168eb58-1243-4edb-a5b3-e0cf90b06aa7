﻿
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

/*
--============================================================================================================================
TASK			UPDATED BY       DATE				ACTION		DESCRIPTION  
[US-229094]     An.TranTan		 21-Jan-2025		UPDATE		Add filter option and Get more data: category, quote
[US-229094]     An.TranTan		 22-Jan-2025		UPDATE		Search quote number by like search
[US-229094]     An.TranTan		 24-Jan-2025		UPDATE		Get Customer name, sales person for task type Quote
[US-229094]     An.TranTan		 07-Feb-2025		UPDATE		Remove restrict filter TODO task base on login user
==============================================================================================================================  
*/
CREATE OR ALTER PROCEDURE [dbo].[usp_selectall_toDoListTask] 
@OrderBy int = 1,                                                                        
@SortDir int = 1,                                                                        
@PageIndex int = 0,                                                                        
@PageSize int = 10,                                        
@CreatedDateFrom datetime = NULL,                                         
@CreatedDateTo datetime = NULL,                                         
@TaskDateFrom datetime = NULL,                                         
@TaskDateTo datetime = NULL,                                         
@TaskType INT = null,                                        
@TaskStatus NVARCHAR(50) = null,                                        
@CustomerName NVARCHAR(50) = null,                                        
@SalesPerson INT = null,                                        
@LoginId int = null,                                        
@ClientId int = null ,                
@TaskReminderDate datetime = NULL,                            
@ReviewOnly BIT=NULL,     
@ToDoID int = 0,
@CategoryNo INT = null,
@QuoteNumber NVARCHAR(100) = null
WITH RECOMPILE                                                       
AS                        
  IF @ReviewOnly=0                     
  SET @ReviewOnly=null                                                                    
  DECLARE @StartPage int,                                                                        
          @EndPage int                                                                                                                 
  SET @StartPage = (@PageIndex * @PageSize + 1)                                                                        
  SET @EndPage = ((@PageIndex + 1) * @PageSize)  
  
  ---[0001]
  if(@SalesPerson is not null or @CustomerName is not NULL)
	SET @LoginId = null
  
  --category number 2: Quote
  IF(ISNULL(@CategoryNo,0) <> 2)
	SET @QuoteNumber = null;

  ;                                                                        
  WITH cteSearch                                                                        
  AS (SELECT                                                                        
    *,                                                                        
    ROW_NUMBER() OVER (ORDER BY --                                                                                                                  
    CASE                                                                        
      WHEN @OrderBy = 1 AND  @SortDir = 2 THEN ToDoListId END DESC, CASE                                                                        
      WHEN @OrderBy = 1 THEN ToDoListId  END, CASE                                                                        
      WHEN @OrderBy = 2 AND @SortDir = 2 THEN CreatedDateFrom END DESC, CASE                                                                        
      WHEN @OrderBy = 2 THEN CreatedDateFrom END, CASE                                                                        
      WHEN @OrderBy = 3 AND  @SortDir = 2 THEN CreatedDateTo END DESC, CASE                                                                        
      WHEN @OrderBy = 3 THEN CreatedDateTo    END,  CASE                                                                        
      WHEN @OrderBy = 5 AND @SortDir = 2 THEN TaskDateFrom  END DESC, CASE                                                                        
      WHEN @OrderBy = 5 THEN TaskDateFrom    END, CASE                                        
      WHEN @OrderBy = 6 AND @SortDir = 2 THEN TaskDateTo END DESC, CASE            
      WHEN @OrderBy = 6 THEN TaskDateTo END, CASE                                                                        
      WHEN @OrderBy = 7 AND  @SortDir = 2 THEN TaskType   END DESC, CASE                                                                        
      WHEN @OrderBy = 7 THEN TaskType                                                                        
    END) AS RowNum FROM (      
         
SELECT                                       
td.ToDoId as ToDoListId,                                      
td.DateAdded as CreatedDateFrom,                                      
td.DateAdded as CreatedDateTo,                                      
td.DateAdded as TaskDateFrom,                                      
td.DueDate as TaskDateTo,                                      
tdlst.ToDoListTypeName as TaskType,                                      
CASE WHEN td.IsComplete=1 THEN 'Completed' WHEN CONVERT(DATETIME, CONVERT(VARCHAR(20), td.DueDate, 120)) < CONVERT(DATETIME, CONVERT(VARCHAR(20), GETDATE(), 120)) THEN 'Overdue' ELSE 'Open' end  as TaskStatus,                                     
--ISNULL(tbLogin.EmployeeName,'') as SalesPersonName,                                      
CASE WHEN (td.ToDoCategoryNo = 2 AND td.QuoteNo IS NOT NULL) THEN ISNULL(qsm.EmployeeName,'')
	ELSE ISNULL(tbLogin.EmployeeName,'')
END AS SalesPersonName,
CASE WHEN (td.ToDoCategoryNo = 2 AND td.QuoteNo IS NOT NULL) THEN qc.CompanyName 
	ELSE tc.CompanyName 
END as CustomerName ,                                   
td.IsComplete,                                  
td.ReminderDate,                          
td.Subject as TaskTitle,            
td.ReminderDate as TaskReminderDate
,td.ToDoCategoryNo
,tdc.ToDoCategoryName
,td.QuoteNo
,q.QuoteNumber
FROM vwToDo td                                      
left join tbCompany tc on tc.CompanyId=td.CompanyNo                                    
left outer join tbLogin on tbLogin.LoginId = tc.Salesman    
--left outer join tbLogin tl1 on tl1.LoginId =  @LoginId                                         
left outer join tbToDoListType tdlst on tdlst.ToDoListTypeId= td.TypeNo  
left join tbToDoCategory tdc WITH(NOLOCK) on tdc.ToDoCategoryId = td.ToDoCategoryNo
left join tbQuote q WITH(NOLOCK) on q.QuoteId = td.QuoteNo
left join tbCompany qc WITH(NOLOCK) on qc.CompanyId = q.CompanyNo
left join tbLogin qsm WITH(NOLOCK) on qsm.LoginId = qc.Salesman
                                               
WHERE     
--td.LoginNo = @LoginId
--- [0001]
--((@LoginId IS NULL) OR (NOT @LoginId IS NULL AND td.LoginNo = @LoginId))    
--AND 
--((@ClientId IS NULL) OR (NOT @ClientId IS NULL AND tl1.ClientNo = @ClientId))                                                                  
--AND ((@CreatedDateFrom IS NULL) OR (NOT @CreatedDateFrom IS NULL AND CAST(td.DateAdded as DATE) = CAST(@CreatedDateFrom AS DATE)))                         
--AND 
((@TaskReminderDate IS NULL) OR (NOT @TaskReminderDate IS NULL  AND CAST(td.ReminderDate as DATE) = CAST(@TaskReminderDate AS DATE)))                                                                            
--AND ((@CreatedDateTo IS NULL)OR (NOT @CreatedDateTo IS NULL AND isnull(td.DateAdded,0) LIKE @CreatedDateTo))                                                                                                            
AND ((@TaskDateFrom IS NULL) OR (NOT @TaskDateFrom IS NULL AND CAST(td.DateAdded as DATE) >= CAST(@TaskDateFrom AS DATE)))                                                                                                         
AND ((@TaskDateTo IS NULL) OR (NOT @TaskDateTo IS NULL AND CAST(td.DueDate as DATE) <= CAST(@TaskDateTo AS DATE)))                         
                                                                                                       
AND ((@TaskType IS NULL) OR (NOT @TaskType IS NULL AND td.TypeNo Like @TaskType))                                                           
--AND ((@TaskStatus IS NULL)                                                                          
--OR (NOT @TaskStatus IS NULL                                                           
--AND td.Task LIKE @TaskStatus))       
 
--AND tbLogin.LoginId = CASE       
--                          WHEN @SalesPerson IS NULL       
--                               THEN @LoginId       
--                          ELSE @SalesPerson       
--                     END       
                                                                    
AND ((@SalesPerson IS NULL) OR (NOT @SalesPerson IS NULL AND tbLogin.LoginId = @SalesPerson))          
----        
--AND ((@LoginId IS NULL) OR (NOT @LoginId IS NULL AND sp.LoginId = @LoginId))         
----                              
AND ((@CustomerName IS NULL) OR (NOT @CustomerName IS NULL AND tc.CompanyName Like @CustomerName))                                                   
--     AND (td.HasReview IS NULL                                          
--OR ISNULL(td.HasReview,0) =@ReviewOnly)                        
AND ((@ReviewOnly IS NULL) OR (NOT @ReviewOnly IS NULL AND ISNULL(td.HasReview,0) = @ReviewOnly))      
---- ToDoID provision                                            
AND ((@ToDoID = 0) OR (NOT @ToDoID = 0 AND td.ToDoId = @ToDoID))                     
--AND td.ToDoId like (case when @ToDoID > 0 then @ToDoID else NULL end)      
AND ((@CategoryNo IS NULL) OR (td.ToDoCategoryNo = @CategoryNo))
AND ((@QuoteNumber IS NULL) OR (q.QuoteNumber LIKE @QuoteNumber))
  ) BASE)                           
  SELECT                                                                        
    *,                                                                        
    (SELECT                                                                        
COUNT(*)                                            
    FROM cteSearch)                                                                        
    AS RowCnt                                                                        
  FROM cteSearch                                                                        
  WHERE RowNum BETWEEN @StartPage AND @EndPage                       
  AND ((@TaskStatus IS NULL) OR (NOT @TaskStatus IS NULL                                                                      
  AND TaskStatus Like @TaskStatus))                                                            
  ORDER BY CreatedDateFrom desc 
GO


