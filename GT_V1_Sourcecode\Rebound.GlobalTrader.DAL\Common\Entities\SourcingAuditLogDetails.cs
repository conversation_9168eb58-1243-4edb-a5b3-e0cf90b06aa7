﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Rebound.GlobalTrader.DAL
{
    public class SourcingAuditLogDetails
    {
        public int SourcingAuditLogId { get; set; }
        public int SourcingId { get; set; }
        public string SourcingType { get; set; }
        public int BatchId { get; set; }
        public string PartNo { get; set; }
        public string Action { get; set; }
        public string OldValue { get; set; }
        public string UpdatedBy { get; set; }
        public DateTime UpdatedDate { get; set; }
        public int TotalCount { get; set; }
    }
}
