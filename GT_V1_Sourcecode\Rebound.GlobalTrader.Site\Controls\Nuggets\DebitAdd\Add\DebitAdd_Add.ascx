<%--
Marker     Changed by      Date         Remarks
[001]      Vinay           26/07/2012   Add compulsory incoterms field when create Credit and debit note. :ESMS No:- 105
--%>
<%@ Control Language="C#" CodeBehind="DebitAdd_Add.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Forms.DebitAdd_Add" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>

<ReboundUI_Form:DesignBase ID="ctlDB" runat="server" ShowRequiredFieldsExplanation="true" ShowQuickHelp="false">

	<Links>
		<ReboundUI:IconButton ID="ibtnSave" runat="server" IconGroup="Nugget" IconTitleResource="Save" IconCSSType="Save" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
		<ReboundUI:IconButton ID="ibtnCancel" runat="server" IconGroup="Nugget" IconTitleResource="Cancel" IconCSSType="Cancel" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
		<ReboundUI:IconButton ID="ibtnSend" runat="server" IconGroup="Nugget" IconTitleResource="Send" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
		<ReboundUI:IconButton ID="ibtnContinue" runat="server" IconGroup="Nugget" IconTitleResource="Continue" IconCSSType="Continue" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
	</Links>

	<Explanation>
	
		<ReboundUI:MultiStep ID="ctlMultiStep" runat="server">
			<Items>
				<ReboundUI:MultiStepItem ID="ctlItem1" runat="server" ResourceTitle="DebitAdd_SelectPO" />
				<ReboundUI:MultiStepItem ID="ctlItem2" runat="server" ResourceTitle="DebitAdd_EnterDetail" />
				<ReboundUI:MultiStepItem ID="ctlItem3" runat="server" ResourceTitle="DebitAdd_Notify" />
			</Items>
		</ReboundUI:MultiStep>
		
	</Explanation>

	<Content>
		<!-- Step 1 ------------------------------------------------------------------->
		
		<ReboundUI_Table:Form id="frmStep1" runat="server">
		
			<asp:TableRow id="trSelectPO" runat="server">
				<asp:TableCell id="tdSelectPO" runat="server">
					<ReboundItemSearch:PurchaseOrders id="ctlSelectPO" runat="server" />
					<asp:Panel id="pnlLines" runat="server" CssClass="itemSearch invisible">
						<h5><%=Functions.GetGlobalResource("Misc", "PurchaseOrders")%></h5>
						<asp:Panel id="pnlLinesError" runat="server" CssClass="itemSearchError invisible"><asp:Label id="lblLinesError" runat="server" /></asp:Panel>
						<asp:Panel id="pnlLinesLoading" runat="server" CssClass="loading invisible"><%=Functions.GetGlobalResource("Misc", "Loading")%></asp:Panel>
						<asp:Panel id="pnlLinesNoneFound" runat="server" CssClass="noneFound invisible"><%=Functions.GetGlobalResource("NotFound", "Generic")%></asp:Panel>
						<ReboundUI:FlexiDataTable ID="tblLines" runat="server" AllowMultipleSelection="true" />
					</asp:Panel>
				</asp:TableCell>
			</asp:TableRow>
			
		</ReboundUI_Table:Form>

		<!-- Step 2 ------------------------------------------------------------------->
		
		<ReboundUI_Table:Form id="frmStep2" runat="server">
		
			<ReboundUI_Form:FormField id="ctlCompany" runat="server" FieldID="lblCompany" ResourceTitle="Supplier">
				<Field><asp:Label ID="lblCompany" runat="server" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlContact" runat="server" FieldID="lblContact" ResourceTitle="Contact">
				<Field><asp:Label ID="lblContact" runat="server" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlBuyer" runat="server" FieldID="lblBuyer" ResourceTitle="Buyer">
				<Field><asp:Label ID="lblBuyer" runat="server" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlPONumber" runat="server" FieldID="lblPONumber" ResourceTitle="PurchaseOrderNo">
				<Field><asp:Label ID="lblPONumber" runat="server" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlDivision" runat="server" FieldID="lblDivision" ResourceTitle="Division">
				<Field><asp:Label ID="lblDivision" runat="server" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlCurrency" runat="server" FieldID="lblCurrency" ResourceTitle="Currency">
				<Field><asp:Label ID="lblCurrency" runat="server" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlTax" runat="server" FieldID="lblTax" ResourceTitle="Tax">
				<Field><asp:Label ID="lblTax" runat="server" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlFreight" runat="server" FieldID="txtFreight" ResourceTitle="Freight">
				<Field><ReboundUI:ReboundTextBox ID="txtFreight" runat="server" TextBoxMode="currency" FormatDecimalPlaces="true" Width="100" /> <asp:Label ID="lblCurrency_Freight" runat="server" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlRaisedBy" runat="server" FieldID="ddlRaisedBy" ResourceTitle="RaisedBy" IsRequiredField="true">
				<Field><ReboundDropDown:Employee ID="ddlRaisedBy" runat="server" /></Field>
			</ReboundUI_Form:FormField>

			<%--<ReboundUI_Form:FormField id="ctlReferenceDate" runat="server" FieldID="txtReferenceDate" ResourceTitle="ReferenceDate" IsRequiredField="true">
				<Field>
					<ReboundUI:ReboundTextBox ID="txtReferenceDate" runat="server" Width="150" />
					<ReboundUI:Calendar ID="calReferenceDate" runat="server" RelatedTextBoxID="txtReferenceDate" />
				</Field>
			</ReboundUI_Form:FormField>--%>
			
			<ReboundUI_Form:FormField id="ctlReferenceDate" runat="server" FieldID="lblReferenceDate" ResourceTitle="ReferenceDate">
				<Field><asp:Label ID="lblReferenceDate" runat="server" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlSupplierInvoice" runat="server" FieldID="txtSupplierInvoice" ResourceTitle="SupplierInvoice">
				<Field><ReboundUI:ReboundTextBox ID="txtSupplierInvoice" runat="server" Width="200" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlSupplierReturn" runat="server" FieldID="txtSupplierReturn" ResourceTitle="SupplierReturn">
				<Field><ReboundUI:ReboundTextBox ID="txtSupplierReturn" runat="server" Width="200" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlSupplierCredit" runat="server" FieldID="txtSupplierCredit" ResourceTitle="SupplierCredit">
				<Field><ReboundUI:ReboundTextBox ID="txtSupplierCredit" runat="server" Width="200" /></Field>
			</ReboundUI_Form:FormField>
			
			<%--[001] code start--%>
			<ReboundUI_Form:FormField id="ctlIncoterm" runat="server" FieldID="ddlIncoterm" ResourceTitle="Incoterm" IsRequiredField="true">
			<Field><ReboundDropDown:Incoterm ID="ddlIncoterm" runat="server" /></Field>
			</ReboundUI_Form:FormField>
			<%--[001] code end--%>

			<ReboundUI_Form:FormField id="ctlNotes" runat="server" FieldID="txtNotes" ResourceTitle="SupplierNotes">
				<Field><ReboundUI:ReboundTextBox ID="txtNotes" runat="server" Width="450" Rows="2" TextMode="MultiLine" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlInstructions" runat="server" FieldID="txtInstructions" ResourceTitle="Instructions">
				<Field><ReboundUI:ReboundTextBox ID="txtInstructions" runat="server" Width="450" Rows="2" TextMode="MultiLine" /></Field>
			</ReboundUI_Form:FormField>
			
		</ReboundUI_Table:Form>
		
		
		<!-- Step 3 ------------------------------------------------------------------->
		
		<ReboundUI_Table:Form id="frmStep3" runat="server">
		
			<ReboundUI_Form:FormField id="ctlSendMail" runat="server" FieldID="chkSendMail" ResourceTitle="ShouldMailBeSent">
				<Field><ReboundUI:ImageCheckBox ID="chkSendMail" runat="server" Enabled="true" Checked="true" /></Field>
			</ReboundUI_Form:FormField>
			
			<ReboundFormFieldCollection:SendMailMessage id="ctlSendMailMessage" runat="server" />
			
		</ReboundUI_Table:Form>

	</Content>
	
</ReboundUI_Form:DesignBase>
