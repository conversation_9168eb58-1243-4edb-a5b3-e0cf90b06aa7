Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DataListNuggets");Rebound.GlobalTrader.Site.Controls.DataListNuggets.CreditBulk=function(n){Rebound.GlobalTrader.Site.Controls.DataListNuggets.CreditBulk.initializeBase(this,[n]);this._format=null};Rebound.GlobalTrader.Site.Controls.DataListNuggets.CreditBulk.prototype={get_blnPOHub:function(){return this._blnPOHub},set_blnPOHub:function(n){this._blnPOHub!==n&&(this._blnPOHub=n)},get_blnHubCredit:function(){return this._blnHubCredit},set_blnHubCredit:function(n){this._blnHubCredit!==n&&(this._blnHubCredit=n)},get_IsGSA:function(){return this._IsGSA},set_IsGSA:function(n){this._IsGSA!==n&&(this._IsGSA=n)},get_ClientNo:function(){return this._ClientNo},set_ClientNo:function(n){this._ClientNo!==n&&(this._ClientNo=n)},get_AllowGenerateXml:function(){return this._AllowGenerateXml},set_AllowGenerateXml:function(n){this._AllowGenerateXml!==n&&(this._AllowGenerateXml=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.DataListNuggets.CreditBulk.callBaseMethod(this,"initialize");this.addSetupDataCallEvent(Function.createDelegate(this,this.setupDataCall));this.addGetDataOKEvent(Function.createDelegate(this,this.getDataOK));this.addInitCompleteEvent(Function.createDelegate(this,this.initAfterBaseIsReady));this._ibtnPrint=$get(this._aryButtonIDs[0]);this._ibtnEmail=$get(this._aryButtonIDs[1]);this._frmConfirm=$find(this._aryFormIDs[0]);this._frmConfirm.addCancel(Function.createDelegate(this,this.hideConfirmForm));this._frmConfirm.addSaveComplete(Function.createDelegate(this,this.saveCeaseComplete));this._frmConfirm.addNotConfirmed(Function.createDelegate(this,this.hideConfirmForm));this._ibtnEmail&&$R_IBTN.addClick(this._ibtnEmail,Function.createDelegate(this,this.showConfirmForm));this._ibtnPrint&&$R_IBTN.addClick(this._ibtnPrint,Function.createDelegate(this,this.showFormatPopup));document.getElementById("btnFormatAction").addEventListener("click",Function.createDelegate(this,this.selectFormatAndPrint));document.getElementById("btnFormatCancel").addEventListener("click",Function.createDelegate(this,this.closeFormatPopup));this.enableBulkButtons(!1);this._table.addMultipleSelectionChanged(Function.createDelegate(this,this.selectionMade));this._strPathToData="controls/DataListNuggets/CreditBulk";this._strDataObject="CreditBulk";this.getData()},enableBulkButtons:function(n){this._ibtnPrint&&$R_IBTN.enableButton(this._ibtnPrint,n);this._ibtnEmail&&$R_IBTN.enableButton(this._ibtnEmail,n)},selectionMade:function(){this.enableBulkButtons(this._table._arySelectedIndexes.length>0)},initAfterBaseIsReady:function(){this.addPageTabChangedEvent(Function.createDelegate(this,this.pageTabChanged));this.updateFilterVisibility();this.hubCreditFilterVisibility();this.getData()},dispose:function(){this.isDisposed||(this._blnHubCredit=null,this._blnPOHub=null,this._IsGSA=null,this._ClientNo=null,this._format=null,this.closeFormatPopup(),Rebound.GlobalTrader.Site.Controls.DataListNuggets.CreditBulk.callBaseMethod(this,"dispose"))},pageTabChanged:function(){this._table._intCurrentPage=1;this._enmViewLevel=this._intCurrentTab;this.updateFilterVisibility();this.hubCreditFilterVisibility();this.getData()},setupDataCall:function(){this._objData.addParameter("ViewLevel",this._enmViewLevel);this._objData.addParameter("blnHubCredit",this._blnHubCredit);this._objData.addParameter("PageSizeLimit",$("#ctl00_cphMain_ctlCredits_ctlDB_txtLimitResults").val())},getDataOK:function(){for(var n,i,t=0,r=this._objResult.Results.length;t<r;t++)n=this._objResult.Results[t],i=[$RGT_nubButton_CreditNote(n.ID,n.No),$R_FN.writeDoubleCellValue($R_FN.writePartNo(n.Part,n.ROHS),$RGT_nubButton_Manufacturer(n.MfrNo,n.Mfr)),$R_FN.writeDoubleCellValue(n.blnMakeYellow==!0?'<span style="background-color:yellow;">'+$RGT_nubButton_Company(n.CMNo,n.CM)+"<\/span>":$RGT_nubButton_Company(n.CMNo,n.CM),$RGT_nubButton_Contact(n.ContactNo,n.Contact)),$R_FN.setCleanTextValue(n.Date),$R_FN.writeDoubleCellValue($RGT_nubButton_Invoice(n.InvNo,n.Invoice),$RGT_nubButton_ClientInvoice(n.ClientInvoiceNo,n.ClientInvoiceNumber)),$R_FN.setCleanTextValue(n.CustPO),$R_FN.setCleanTextValue(n.Total)],this._table.addRow(i,n.ID,!1),i=null,n=null},updateFilterVisibility:function(){this.getFilterField("ctlSalesman").show(this._enmViewLevel!=0);this.getFilterField("ctlPohubOnly").show(this._blnPOHub);this.getFilterField("ctlPohubOnly").enableField(this._blnPOHub);this.getFilterField("ctlClientInvNo").show(!1);this.getFilterField("ctlClientInvNo").enableField(!1);this.getFilterField("ctlClientName").show(this._IsGSA)},hubCreditFilterVisibility:function(){this.getFilterField("ctlSalesman").show(this._enmViewLevel!=0&&!this._blnHubCredit);this.getFilterField("ctlCreditNotes").show(!this._blnHubCredit);this.getFilterField("ctlCompanyName").show(!this._blnHubCredit);this.getFilterField("ctlContactName").show(!this._blnHubCredit);this.getFilterField("ctlCRMANo").show(!this._blnHubCredit);this.getFilterField("ctlClientInvNo").show(!0)},showConfirmForm:function(){this._frmConfirm._strCredits=this._table._aryCurrentValues;this._frmConfirm._ClientNo=this._ClientNo;this._frmConfirm._AllowGenerateXml=this._AllowGenerateXml;this.showForm(this._frmConfirm,!0)},hideConfirmForm:function(){this.showForm(this._frmConfirm,!1)},saveCeaseComplete:function(){this.hideConfirmForm()},printCredit:function(){var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/CreditMainInfo");n.set_DataObject("CreditMainInfo");n.set_DataAction("SaveCreditPrint");n.addParameter("CreditsPrintId",this._table._aryCurrentValues);n.addDataOK(Function.createDelegate(this,this.printCreditsaveComplete));n.addError(Function.createDelegate(this,this.printCreditError));n.addTimeout(Function.createDelegate(this,this.printCreditError));$R_DQ.addToQueue(n);$R_DQ.processQueue()},printCreditError:function(n){this._strErrorMessage=n._errorMessage;this.onSaveError()},printCreditsaveComplete:function(n){var t=n._result.Result,i=n._result.Url;this._format=="XML"?window.open("Print.aspx?pro=64&id="+t,"winPrintBulkCredit","left=20,top=20,width=870,height=674,toolbar=no,resizable=yes,status=no,directories=no,location=no,menubar=yes,scrollbars=yes"):window.open("Print.aspx?pro=60&id="+t,"winPrintBulkCredit","left=20,top=20,width=870,height=674,toolbar=no,resizable=yes,status=no,directories=no,location=no,menubar=yes,scrollbars=yes")},showFormatPopup:function(){let n=this._AllowGenerateXml&&this._ClientNo==108?"XML":"PDF";$("#overlay").css("display","block");$("input:radio[name=rdFormat]").val([n]);$("#optionXML").css("display",this._AllowGenerateXml?"block":"none");$("#formatModal").dialog("open")},selectFormatAndPrint:function(){this._format=$('input[name="rdFormat"]:checked').val();this.printCredit();this.closeFormatPopup()},closeFormatPopup:function(){$("#overlay").css("display","none");$("#formatModal").dialog("close")}};Rebound.GlobalTrader.Site.Controls.DataListNuggets.CreditBulk.registerClass("Rebound.GlobalTrader.Site.Controls.DataListNuggets.CreditBulk",Rebound.GlobalTrader.Site.Controls.DataListNuggets.Base,Sys.IDisposable);