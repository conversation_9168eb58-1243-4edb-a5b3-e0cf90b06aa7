///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference name="~/Controls/IconButton/IconButton.js" />
///<reference name="~/Controls/ReboundTextBox/ReboundTextBox.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------

Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.BOMItems_Add = function(element) {
    Rebound.GlobalTrader.Site.Controls.Forms.BOMItems_Add.initializeBase(this, [element]);
    //this._intPOQuoteID = 0;
    this._intLineID = 0;
    this._intRequirementLineID = 0;
    this._strCompanyName = "";
    this._intBOMID = 0;

};

Rebound.GlobalTrader.Site.Controls.Forms.BOMItems_Add.prototype = {
    get_intBOMID: function() { return this._intBOMID; }, set_intBOMID: function(value) { if (this._intBOMID !== value) this._intBOMID = value; },
    //get_intPOQuoteID: function() { return this._intPOQuoteID; }, set_intPOQuoteID: function(v) { if (this._intPOQuoteID !== v) this._intPOQuoteID = v; },
    // get_trSourceFromRequirement: function() { return this._trSourceFromRequirement; }, set_trSourceFromRequirement: function(v) { if (this._trSourceFromRequirement !== v) this._trSourceFromRequirement = v; },
    get_ctlReqsWithBOM: function() { return this._ctlReqsWithBOM; }, set_ctlReqsWithBOM: function(v) { if (this._ctlReqsWithBOM !== v) this._ctlReqsWithBOM = v; },
    initialize: function() {
        Rebound.GlobalTrader.Site.Controls.Forms.BOMItems_Add.callBaseMethod(this, "initialize");
        this.addShown(Function.createDelegate(this, this.formShown));
    },

    dispose: function() {
        if (this.isDisposed) return;
        if (this._ctlReqsWithBOM) this._ctlReqsWithBOM.dispose();
        //  this._intPOQuoteID = null;
        this._trSourceFromRequirement = null;
        this._ctlReqsWithBOM = null;

        Rebound.GlobalTrader.Site.Controls.Forms.BOMItems_Add.callBaseMethod(this, "dispose");
    },

    formShown: function() {
        if (this._blnFirstTimeShown) {
            //form actions
            this.addSave(Function.createDelegate(this, this.saveAdd));
            this._ctlReqsWithBOM.addItemSelected(Function.createDelegate(this, this.selectRequirementItem));
            this._ctlReqsWithBOM._intBOMID = this._intBOMID;
            this._strPathToData = "controls/Nuggets/BOMItems";
            this._strDataObject = "BOMItems";
        }
        this._ctlReqsWithBOM._tblResults.clearTable();
        this._ctlReqsWithBOM.resizeColumns();
       //   this._ctlReqsWithBOM.setFieldValue("ctlReqNo",true);
       // this._ctlReqsWithBOM.searchClicked();



        this._ctlReqsWithBOM.setFieldValue("ctlDateReceivedFrom", $R_FN.oneWeekAgo());

        this._ctlReqsWithBOM.setFieldValue("ctlDateReceivedTo", $R_FN.shortDate());
        this._ctlReqsWithBOM.searchClicked();

    },


    loadDropDowns: function() {
       // this.getFieldDropDownData("ctlProduct");
        //this.getFieldDropDownData("ctlPackage");
        this.getFieldDropDownData("ctlROHS");

    },

    selectRequirementItem: function() {
        this._intRequirementLineID = this._ctlReqsWithBOM.getSelectedID();
        this.continueClicked();
    },

    validateForm: function() {
        this.onValidate();
        var blnOK = true;
        if (this._ctlReqsWithBOM._tblResults._aryCurrentValues.length == 0) {
            blnOK = false;
            this.showError(true, "Please select any line");
        }
        if (!blnOK) this.showError(true);
        return blnOK;
    },

    saveAdd: function() {
        if (!this.validateForm()) return;
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData(this._strPathToData);
        obj.set_DataObject(this._strDataObject);
        obj.set_DataAction("Update");
        //obj.addParameter("id", this._intPOQuoteID);
        obj.addParameter("BomId", this._intBOMID);
        obj.addParameter("ReqIds", $R_FN.arrayToSingleString(this._ctlReqsWithBOM._tblResults._aryCurrentValues, ","));
        obj.addDataOK(Function.createDelegate(this, this.saveAddOK));
        obj.addError(Function.createDelegate(this, this.saveAddError));
        obj.addTimeout(Function.createDelegate(this, this.saveAddError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    saveAddError: function(args) {
        this._strErrorMessage = args._errorMessage;
        this.onSaveError();
    },

    saveAddOK: function(args) {
        if (args._result.Result == true) {
            this._intLineID = args._result.NewID;
            this.onSaveComplete();
        } else {
            this.saveEditError(args);
        }
    }

};

Rebound.GlobalTrader.Site.Controls.Forms.BOMItems_Add.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.BOMItems_Add", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
