﻿/* Marker     changed by      date         Remarks  
[001]      A<PERSON><PERSON><PERSON>     25-Aug-2021   Add for Supplier Po Approval.
 */
using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;
using System.Data.Common;

namespace Rebound.GlobalTrader.DAL
{
    public abstract class PowerAppProvider : DataAccess
    {
        static private PowerAppProvider _instance = null;

        static public PowerAppProvider Instance
        {
            get
            {
                if (_instance == null) _instance = (PowerAppProvider)Activator.CreateInstance(Type.GetType(Globals.Settings.PowerApps.ProviderType));
                return _instance;
            }
        }
        public PowerAppProvider()
        {
            this.ConnectionString = Globals.Settings.PowerApps.ConnectionString;
            this.GTConnectionString = Globals.Settings.PowerApps.GTConnectionString;
        }

        #region Method Registrations



        /// <summary>
        /// Line Manager Approvals.
        /// Calls [usp_PowerApp_update_SO_Authorise]
        /// </summary>
        public abstract List<PowerAppDetails> SOCheckedUnchecked(System.Int32? SOId, System.String AuthorisedByEmail, System.Boolean? Authorise, System.String ApproverNote, System.Int32? RequestId, System.String TokenValue);

        /// <summary>
        /// calls [usp_select_PowerBIActivity]
        /// </summary>
        /// <param name="loginId"></param>
        /// <param name="clientId"></param>
        /// <returns></returns>
        public abstract List<PowerAppDetails> GetPowerBIActivity(System.Int32? loginId, System.Int32? clientId);
        public abstract string LastVisitedBI(int? LoginId,int? clientId);

        /// <summary>
        /// Export Approvals.
        /// Calls [usp_PowerAppExportApproveRejectById]
        /// </summary>
        public abstract List<PowerAppDetails> ApproveRejectExportApproval(System.Int32? ExportApprovalId, System.String AuthorisedByEmail, System.String ApproverNote, System.Int32? RequestId, System.String TokenValue, System.Int32? ApprovalOption, System.Int32? OgelNumber);

        /// <summary>
        /// Notify changes of RL Stock.
        /// Calls [usp_PowerApp_notify_RL_Stock]
        /// </summary>
        public abstract PowerAppDetails GetFlowUrlByFlowName(System.String flowName);
        #endregion
    }
}
