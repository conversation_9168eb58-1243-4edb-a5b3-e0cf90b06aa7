﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


/*   
===========================================================================================  
TASK         UPDATED BY   DATE			ACTION		DESCRIPTION  
[US-210037]  An.TranTan   23-Oct-2024	create		Get Manufacturer advisory notes  
===========================================================================================  
*/  
CREATE OR ALTER   PROCEDURE  [dbo].[usp_select_ManufacturerAdvisoryNotes]      
  @ManufacturerId INT
AS  
BEGIN  
	SELECT CASE WHEN ISNULL(IsDisplayAdvisory, 0) = 1 THEN AdvisoryNotes 
								ELSE '' 
							END AS AdvisoryNotes   
	FROM tbManufacturer WITH(NOLOCK)  
	WHERE ManufacturerId = @ManufacturerId;  
END
GO


