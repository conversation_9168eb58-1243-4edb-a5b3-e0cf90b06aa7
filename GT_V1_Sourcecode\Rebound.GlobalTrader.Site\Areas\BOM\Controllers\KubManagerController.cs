﻿using Rebound.GlobalTrader.BLL;
using Rebound.GlobalTrader.Site.Areas.BOM.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Mvc;

namespace Rebound.GlobalTrader.Site.Areas.BOM.Controllers
{
    public class KubManagerController : Controller
    {
        // GET: BOM/KubManger
        public ActionResult Index()
        {
            return View();
        }

        //For client side
        public ActionResult BOMManagerClientKUB()
        {
            ViewBag.IsPoHub = SessionManager.IsPOHub;
            return View();
        }

        public ActionResult GetKubConfigData()
        {
            List<Setting> settings = new List<Setting>();

            try
            {
                settings = BLL.Setting.GetListValues(null);
                var setting = settings.FirstOrDefault(x => x.SettingItemID == 17);

                var kubConfig = new KubConfigDataModel()
                {
                    IsEnalbe = setting != null && setting.SettingValue.Equals("true", StringComparison.InvariantCultureIgnoreCase)
                };
                return Json(kubConfig, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                new Errorlog().LogMessage("Inside KubManagerController class, Method name : GetKubConfigData. Exception details:" + (ex.InnerException == null ? ex.Message : ex.InnerException.Message));
                return Json(settings, JsonRequestBehavior.AllowGet);
            }
        }

        [HttpPost]
        public ActionResult ShowKubAssistance(string partNo, int bomManagerId, int manufactuerId, string manufacturerName)
        {
            BLL.KubAssistance obj = null;
            try
            {
                var fullPartNo = Functions.GetFullPart(partNo);
                var enabledObj = BLL.AllowedEnabledForHUB.IsAllowedEnabledForHUB(fullPartNo, 0, bomManagerId, manufactuerId, manufacturerName, false);
                if (enabledObj == null || enabledObj.IsAllowedEnable == false)
                {
                    return Json(new { IsEnalbe = false }, JsonRequestBehavior.AllowGet);
                }

                obj = BLL.KubAssistance.LoadKubAssistanceForBOMManager(fullPartNo, 0, bomManagerId, manufactuerId, manufacturerName);
                var result = new KubAssistanceForBOM
                {
                    PartNo = partNo,
                    FullPartNo = fullPartNo,
                    NumberOfRequirement = obj.NumberOfRequirement,
                    LastQuotedPrice = obj.LastQuotedPrice,//Format 0.00001 for Price
                    LastHubprice = obj.LastHubprice, //Format 0.00001 for Price
                    NumberOfInvoice = obj.NumberOfInvoice,
                    LastestHubRFQName = obj.LastestHubRFQName,
                    LastestHubNumberDate = Functions.FormatUSDate(obj.LastestHubNumberDate),
                    LastestHubRFQId = obj.LastestHubRFQId,
                    LastSoldPrice = obj.LastSoldPrice, //Format 0.00001 for Price
                    LastHighestSoldPrice = obj.LastHighestSoldPrice, //Format 0.00001 for Price
                    LastLowestSoldPrice = obj.LastLowestSoldPrice, //Format 0.00001 for Price
                    NumberOfQuote = obj.NumberOfQuote,
                    NumberQuoteToSalesOrder = obj.NumberQuoteToSalesOrder,
                    LastUpdatedDate = obj.LastUpdatedDate,
                    IHSResultForPartNo = obj.IHSResultForPartNo,
                    LyticaResultForPartNo = obj.LyticaResultForPartNo,
                    IsEnalbe = true
                };
                return Json(result, JsonRequestBehavior.AllowGet);
            }
            catch (Exception e)
            {
                new Errorlog().LogMessage("Error at ShowKubAssistance() in KubManagerController : " + e.Message);
                return Json(obj, JsonRequestBehavior.AllowGet);
            }
        }

        [HttpPost]
        public ActionResult ShowKubAssistanceForClient(string partNo, int bomManagerId, int manufactuerId, string manufacturerName)
        {
            try
            {
                string fullPartNo = Functions.GetFullPart(partNo);
                int clientId = Convert.ToInt32(SessionManager.ClientID);

                var enabledObj = BLL.AllowedEnabledForHUB.IsAllowedEnabledForHUB(fullPartNo, clientId, bomManagerId, manufactuerId, manufacturerName, false);
                if (enabledObj == null || enabledObj.IsAllowedEnable == false)
                {
                    return Json(new { IsEnalbe = false }, JsonRequestBehavior.AllowGet);
                }

                KubAssistance obj = BLL.KubAssistance.LoadKubAssistanceForBOMManager(fullPartNo, clientId, bomManagerId, manufactuerId, manufacturerName);
                var result = new KubAssistanceForBOM
                {
                    PartNo = partNo,
                    FullPartNo = fullPartNo,
                    NumberOfRequirement = obj.NumberOfRequirement,
                    LastQuotedPrice = obj.LastQuotedPrice,//Format 0.00001 for Price
                    LastHubprice = obj.LastHubprice, //Format 0.00001 for Price
                    NumberOfInvoice = obj.NumberOfInvoice,
                    LastestHubRFQName = obj.LastestHubRFQName,
                    LastestHubNumberDate = Functions.FormatUSDate(obj.LastestHubNumberDate),
                    LastestHubRFQId = obj.LastestHubRFQId,
                    LastSoldPrice = obj.LastSoldPrice, //Format 0.00001 for Price
                    LastHighestSoldPrice = obj.LastHighestSoldPrice, //Format 0.00001 for Price
                    LastLowestSoldPrice = obj.LastLowestSoldPrice, //Format 0.00001 for Price
                    NumberOfQuote = obj.NumberOfQuote,
                    NumberQuoteToSalesOrder = obj.NumberQuoteToSalesOrder,
                    LastUpdatedDate = obj.LastUpdatedDate,
                    LastDatePartSoldToBomCustomer = obj.LastDatePartSoldToBomCustomer,
                    IHSResultForPartNo = obj.IHSResultForPartNo,
                    LyticaResultForPartNo = obj.LyticaResultForPartNo,
                    IsEnalbe = true
                };

                return Json(result, JsonRequestBehavior.AllowGet);
            }
            catch (Exception e)
            {
                var errorMessage = "Error at ShowKubAssistanceForClient() in KubManagerController : " + e.Message;
                new Errorlog().LogMessage(errorMessage);
                return Json(new { IsEnalbe = false }, JsonRequestBehavior.AllowGet);
            }
        }

        public ActionResult GetListKubTop20CusReqForBOM(string fullPartNo)
        {
            List<BLL.KubTop20CusReqForBOM> lst = null;
            try
            {
                lst = BLL.KubTop20CusReqForBOM.GetListKubTop20CusReqForBOM(fullPartNo, Convert.ToInt32(SessionManager.ClientID));

                return Json(lst, JsonRequestBehavior.AllowGet);
            }
            catch (Exception e)
            {
                new Errorlog().LogMessage("Error at GetListKubTop20CusReqForBOM() in KubManagerController : " + e.Message);
                return Json(lst, JsonRequestBehavior.AllowGet);
            }
        }

        public ActionResult GetListKubTop10QuoteForBOM(string fullPartNo)
        {
            List<BLL.KubTop10QuoteForBOM> lst = null;
            try
            {
                lst = BLL.KubTop10QuoteForBOM.GetListKubTop10QuoteForBOM(fullPartNo, Convert.ToInt32(SessionManager.ClientID));
                return Json(lst, JsonRequestBehavior.AllowGet);
            }
            catch (Exception e)
            {
                new Errorlog().LogMessage("Error at GetListKubTop10QuoteForBOM() in KubManagerController : " + e.Message);
                return Json(lst, JsonRequestBehavior.AllowGet);
            }
        }

        public ActionResult GetListKubTop3BuyPrice(string fullPartNo)
        {
            List<BLL.KubTop3BuyPrice> lst = null;
            try
            {
                lst = BLL.KubTop3BuyPrice.GetKubTop3BuyPriceDetailsForHUB(fullPartNo, Convert.ToInt32(SessionManager.ClientID));
                return Json(lst, JsonRequestBehavior.AllowGet);
            }
            catch (Exception e)
            {
                new Errorlog().LogMessage("Error at GetListKubTop3BuyPrice() in KubManagerController : " + e.Message);
                return Json(lst, JsonRequestBehavior.AllowGet);
            }
        }

        public ActionResult GetStockDetailsForBOMPart(string fullPartNo, int BOMManagerId)
        {
            KubStockDetailsForBOM stockDetails = null;
            try
            {
                stockDetails = BLL.KubStockDetailsForBOM.GetStockDetailsForBOMPart(fullPartNo, Convert.ToInt32(SessionManager.ClientID), BOMManagerId);
                return Json(stockDetails, JsonRequestBehavior.AllowGet);
            }
            catch (Exception e)
            {
                new Errorlog().LogMessage("Error at GetStockDetailsForBOMPart() in KubManagerController : " + e.Message);
                return Json(stockDetails, JsonRequestBehavior.AllowGet);
            }
        }
    }
}
