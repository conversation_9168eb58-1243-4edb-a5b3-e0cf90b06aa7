///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.HomeNuggets");

Rebound.GlobalTrader.Site.Controls.HomeNuggets.Base = function(element) { 
	Rebound.GlobalTrader.Site.Controls.HomeNuggets.Base.initializeBase(this, [element]);
	this._intCompanyID = -1;
	this._intLoginID_Other = 0;
	this._strNoData_My = "";
	this._strNoData_Other = "";
	this._isIncludeCredit = false;
};

Rebound.GlobalTrader.Site.Controls.HomeNuggets.Base.prototype = {

	get_intCompanyID: function() { return this._intCompanyID; }, 	set_intCompanyID: function(v) { if (this._intCompanyID !== v)  this._intCompanyID = v; }, 
	get_intRowCount: function() { return this._intRowCount; }, 	set_intRowCount: function(v) { if (this._intRowCount !== v)  this._intRowCount = v; }, 
	get_intLoginID_My: function() { return this._intLoginID_My; }, 	set_intLoginID_My: function(v) { if (this._intLoginID_My !== v)  this._intLoginID_My = v; }, 
	get_strTitle_My: function() { return this._strTitle_My; }, 	set_strTitle_My: function(v) { if (this._strTitle_My !== v)  this._strTitle_My = v; }, 
	get_strTitle_Other: function() { return this._strTitle_Other; }, 	set_strTitle_Other: function(v) { if (this._strTitle_Other !== v)  this._strTitle_Other = v; }, 
	get_strNoData_My: function() { return this._strNoData_My; }, 	set_strNoData_My: function(v) { if (this._strNoData_My !== v)  this._strNoData_My = v; }, 
	get_strNoData_Other: function() { return this._strNoData_Other; }, 	set_strNoData_Other: function(v) { if (this._strNoData_Other !== v)  this._strNoData_Other = v; }, 
	get_blnCanShowForAnotherUser: function() { return this._blnCanShowForAnotherUser; }, 	set_blnCanShowForAnotherUser: function(v) { if (this._blnCanShowForAnotherUser !== v)  this._blnCanShowForAnotherUser = v; }, 
	get_isIncludeCredit: function() { return this._isIncludeCredit; }, 	set_isIncludeCredit: function(v) { if (this._isIncludeCredit !== v)  this._isIncludeCredit = v; }, 

	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.HomeNuggets.Base.callBaseMethod(this, "initialize");
	},
	
	dispose: function() { 
		if (this.isDisposed) return;
		this._intCompanyID = null;
		this._intRowCount = null;
		this._intLoginID_My = null;
		this._strTitle_My = null;
		this._strTitle_Other = null;
		this._strNoData_My = null;
		this._strNoData_Other = null;
		this._blnCanShowForAnotherUser = null;
		this._intLoginID_Other = null;
		this._isIncludeCredit = null;

		Rebound.GlobalTrader.Site.Controls.HomeNuggets.Base.callBaseMethod(this, "dispose");
	},
	
	setupLoadingState: function() {
		this.showLoading(true);
		this.showContentLoading(true);
		this.showContent(false);
		this.showNoData(false);
	},

	showNoneFoundOrContent: function(intCount) {
		if (intCount > 0) {
			this.showHomeNuggetContent(true);
		} else {
			this.showNoData(true);
		}
	},

	hideLoading: function() {
		this.showLoading(false);
		this.showContentLoading(false);
	},
	
	showHomeNuggetContent: function(blnShow) {
		$R_FN.showElement(this._pnlContent, blnShow);
		if (blnShow) {
			$R_FN.showElement(this._pnlError, false);
			$R_FN.showElement(this._pnlLoading, false);
		}
	},
	
	addHTMLContentItem: function(strHTML, el) {
		var div = document.createElement("div");
		div.className = "contentItem";
		$R_FN.setInnerHTML(div, strHTML);
		var elToAddTo = (el) ? el : this._pnlContent;
		elToAddTo.appendChild(div);
		elToAddTo = null;
		el = null;
		div = null;
	},
	
	homeNuggetDataError: function(args) {
		this.showNoData(false);
		this.showError(true, args._errorMessage);
	},
	
	changeUser: function(intNewLoginID, strNewLoginName,isIncludeCredit = false) {
		if (!this._blnCanShowForAnotherUser) return;
		this._intLoginID_Other = intNewLoginID;
		this.setTitle(String.format(this._strTitle_Other, strNewLoginName));
		this.setNoDataMessage(String.format(this._strNoData_Other, strNewLoginName));
		this._isIncludeCredit = isIncludeCredit;
	},
	
	revertUserToCurrentLogin: function(intNewLoginID) {
		if (!this._blnCanShowForAnotherUser) return;
		this._intLoginID_Other = 0;
		this.setTitle(this._strTitle_My);
		this.setNoDataMessage(this._strNoData_My);
	}
	
};

Rebound.GlobalTrader.Site.Controls.HomeNuggets.Base.registerClass("Rebound.GlobalTrader.Site.Controls.HomeNuggets.Base", Rebound.GlobalTrader.Site.Controls.Nuggets.Base, Sys.IDisposable);
