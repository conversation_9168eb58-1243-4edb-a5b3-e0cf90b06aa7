Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");Rebound.GlobalTrader.Site.Controls.DropDowns.Country=function(n){Rebound.GlobalTrader.Site.Controls.DropDowns.Country.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.DropDowns.Country.prototype={get_intPOHubClientNo:function(){return this._intPOHubClientNo},set_intPOHubClientNo:function(n){this._intPOHubClientNo!==n&&(this._intPOHubClientNo=n)},get_intGlobalLoginClientNo:function(){return this._intGlobalLoginClientNo},set_intGlobalLoginClientNo:function(n){this._intGlobalLoginClientNo!==n&&(this._intGlobalLoginClientNo=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.DropDowns.Country.callBaseMethod(this,"initialize");this.addSetupDataCall(Function.createDelegate(this,this.setupDataCall));this.addGetDataOK(Function.createDelegate(this,this.dataCallOK))},dispose:function(){this.isDisposed||(this._intPOHubClientNo=null,this._intGlobalLoginClientNo=null,Rebound.GlobalTrader.Site.Controls.DropDowns.Country.callBaseMethod(this,"dispose"))},setupDataCall:function(){this._objData.set_PathToData("controls/DropDowns/Country");this._objData.set_DataObject("Country");this._objData.set_DataAction("GetData");this._objData.addParameter("POHubClientNo",this._intPOHubClientNo);this._objData.addParameter("GlobalLoginClientNo",this._intGlobalLoginClientNo)},dataCallOK:function(){var t=this._objData._result,n;if(t.Countries)for(n=0;n<t.Countries.length;n++)this.addOption(t.Countries[n].Name,t.Countries[n].ID)}};Rebound.GlobalTrader.Site.Controls.DropDowns.Country.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.Country",Rebound.GlobalTrader.Site.Controls.DropDowns.Base);