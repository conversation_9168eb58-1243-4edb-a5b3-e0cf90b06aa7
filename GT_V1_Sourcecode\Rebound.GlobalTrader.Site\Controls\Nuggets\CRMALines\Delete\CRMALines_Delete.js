Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.CRMALines_Delete=function(n){Rebound.GlobalTrader.Site.Controls.Forms.CRMALines_Delete.initializeBase(this,[n]);this._intLineID=-1};Rebound.GlobalTrader.Site.Controls.Forms.CRMALines_Delete.prototype={get_intLineID:function(){return this._intLineID},set_intLineID:function(n){this._intLineID!==n&&(this._intLineID=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Forms.CRMALines_Delete.callBaseMethod(this,"initialize");this.addShown(Function.createDelegate(this,this.formShown))},dispose:function(){this.isDisposed||(this._ctlConfirm&&this._ctlConfirm.dispose(),this._ctlConfirm=null,this._intLineID=null,Rebound.GlobalTrader.Site.Controls.Forms.CRMALines_Delete.callBaseMethod(this,"dispose"))},formShown:function(){this._blnFirstTimeShown&&(this._ctlConfirm=this.getFieldComponent("ctlConfirm"),this._ctlConfirm.addClickYesEvent(Function.createDelegate(this,this.yesClicked)),this._ctlConfirm.addClickNoEvent(Function.createDelegate(this,this.noClicked)))},setFieldsFromHeader:function(n,t){this.setFieldValue("ctlCustomerRMA",n);this.setFieldValue("ctlCustomer",t)},yesClicked:function(){this.showSaving(!0);var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("Controls/Nuggets/CRMALines");n.set_DataObject("CRMALines");n.set_DataAction("Delete");n.addParameter("id",this._intLineID);n.addDataOK(Function.createDelegate(this,this.saveComplete));n.addError(Function.createDelegate(this,this.saveError));n.addTimeout(Function.createDelegate(this,this.saveError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null;this.onSave()},noClicked:function(){this.showSaving(!1);this.onNotConfirmed()},saveError:function(n){this.showSaving(!1);this._strErrorMessage=n._errorMessage;this.onSaveError()},saveComplete:function(n){this.showSaving(!1);n._result.Result==!0?this.onSaveComplete():(this._strErrorMessage=n._errorMessage,this.onSaveError())}};Rebound.GlobalTrader.Site.Controls.Forms.CRMALines_Delete.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.CRMALines_Delete",Rebound.GlobalTrader.Site.Controls.Forms.Base,Sys.IDisposable);