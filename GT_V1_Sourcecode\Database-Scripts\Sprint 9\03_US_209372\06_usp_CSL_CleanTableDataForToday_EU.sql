﻿/* 
===========================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[BUG-209372]	Cuong.DoXaun		19-Aug-2024		Create			Clear data for importing
===========================================================================================
*/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

IF OBJECT_ID('dbo.usp_CSL_CleanTableDataForToday_EU', 'P') IS NOT NULL
BEGIN
    DROP PROCEDURE dbo.usp_CSL_CleanTableDataForToday_EU
END
GO

CREATE procedure usp_CSL_CleanTableDataForToday_EU  
AS  
BEGIn  
  
 delete from tbCSVImportEU  
 where cast(CreatedOn as date) = cast(getdate() as date)  
  
END  
  