Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Nuggets");Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyProspects=function(n){Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyProspects.initializeBase(this,[n]);this._intCRMProspectID=-1;this._inactive=!1};Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyProspects.prototype={get_intCustomerID:function(){return this._intCustomerID},set_intCustomerID:function(n){this._intCustomerID!==n&&(this._intCustomerID=n)},get_ibtnAdd:function(){return this._ibtnAdd},set_ibtnAdd:function(n){this._ibtnAdd!==n&&(this._ibtnAdd=n)},get_ibtnEdit:function(){return this._ibtnEdit},set_ibtnEdit:function(n){this._ibtnEdit!==n&&(this._ibtnEdit=n)},get_CanEditActive:function(){return this._CanEditActive},set_CanEditActive:function(n){this._CanEditActive!==n&&(this._CanEditActive=n)},get_ibtnViewTask:function(){return this._ibtnViewTask},set_ibtnViewTask:function(n){this._ibtnViewTask!==n&&(this._ibtnViewTask=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyProspects.callBaseMethod(this,"initialize");this.addRefreshEvent(Function.createDelegate(this,this.getData));this._ibtnEdit&&($R_IBTN.addClick(this._ibtnEdit,Function.createDelegate(this,this.showEditForm)),this._frmEdit=$find(this._aryFormIDs[0]),this._frmEdit.addCancel(Function.createDelegate(this,this.cancelEdit)),this._frmEdit.addSaveComplete(Function.createDelegate(this,this.saveEditComplete)),this._frmEdit.addSaveError(Function.createDelegate(this,this.saveEditError)));this._ibtnAdd&&($R_IBTN.addClick(this._ibtnAdd,Function.createDelegate(this,this.showAddForm)),this._frmAdd=$find(this._aryFormIDs[1]),this._frmAdd.addCancel(Function.createDelegate(this,this.cancelAdd)),this._frmAdd.addSaveComplete(Function.createDelegate(this,this.saveAddComplete)),this._frmAdd.addSaveError(Function.createDelegate(this,this.saveAddError)));this._blnIsNoDataFound||this._blnHasInitialData||this.getData();this._ibtnViewTask&&$R_IBTN.addClick(this._ibtnViewTask,Function.createDelegate(this,this.redirectToDetails));this.getCompanyInactive()},dispose:function(){this.isDisposed||(this._ibtnEdit&&$R_IBTN.clearHandlers(this._ibtnEdit),this._ibtnAdd&&$R_IBTN.clearHandlers(this._ibtnAdd),this._frmEdit&&this._frmEdit.dispose(),this._frmAdd&&this._frmAdd.dispose(),this._intCustomerID=null,this._ibtnAdd=null,this._frmEdit=null,this._frmAdd=null,this._inactive=null,this._ibtnViewTask&&$R_IBTN.clearHandlers(this._ibtnViewTask),this._ibtnViewTask=null,Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyProspects.callBaseMethod(this,"dispose"))},getData:function(){this.getData_Start();var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/CompanyProspects");n.set_DataObject("CompanyProspects");n.set_DataAction("GetData");n.addParameter("id",this._intCustomerID);n.addDataOK(Function.createDelegate(this,this.getDataOK));n.addError(Function.createDelegate(this,this.getDataError));n.addTimeout(Function.createDelegate(this,this.getDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getDataOK:function(n){var t=n._result,i,f,u,c,r;this.setFieldValue("ctlProspectType",$R_FN.setCleanTextValue(t.ProspectTypeName));this.setFieldValue("ctlMFRBoardLevel",t.IscrmProspectBoardLevel==!0?"Yes":t.IscrmProspectBoardLevel==!1?"No":"");this.setFieldValue("ctlFinalAssembly",t.IsFinalAssembly==!0?"Yes":t.IsFinalAssembly==!1?"No":"");this.setFieldValue("ctlEndCustomer",$R_FN.setCleanTextValue(t.EndCustomer));this.setFieldValue("ctlCreditInfo","Credit Limit Potential: "+$R_FN.setCleanTextValue(t.LimitedEstimate)+"<br/> Health Rating %: "+$R_FN.setCleanTextValue(t.HealthRating));this.setFieldValue("ctlElectronicSpend",$R_FN.setCleanTextValue(t.ElectronicSpendName));this.setFieldValue("ctlFrequencyOfPurchase",$R_FN.setCleanTextValue(t.FrequencyOfPurchaseName));this.setFieldValue("ctlCommodities",$R_FN.setCleanTextValue(t.CommoditiesName));this.setFieldValue("ctlTurnover",$R_FN.setCleanTextValue(t.TurnoverName));this.setFieldValue("hidProspectTypeId",t.ProspectTypeId);this.setFieldValue("hidIsIndustry",t.IsIndustry);this.setFieldValue("hidElectronicSpendId",t.ElectronicSpendId);this.setFieldValue("hidFrequencyOfPurchaseId",t.FrequencyOfPurchaseId);this.setFieldValue("hidCommoditiesId",t.CommoditiesId);this.setFieldValue("hidTurnoverId",t.TurnoverId);this.setFieldValue("hidLimitedEstimate",t.LimitedEstimate);this.setFieldValue("hidHealthRating",t.HealthRating);this.setFieldValue("hidMFRBoardLevel",t.IscrmProspectBoardLevel);this.setFieldValue("hidFinalAssembly",t.IsFinalAssembly);this._intCRMProspectID=t.ID;var l=!1,e="",o="",s="",h="";if(t.IndustryTypes)for(r=0;r<t.IndustryTypes.length;r++)t.IndustryTypes[r].Selected&&(e.length>0&&(e+=", "),e+=t.IndustryTypes[r].Name,o.length>0&&(o+="||"),o+=t.IndustryTypes[r].ID,l=!0),s.length>0&&(s+="||"),s+=t.IndustryTypes[r].Name,h.length>0&&(h+="||"),h+=t.IndustryTypes[r].ID;for(this.setFieldValue("ctlIndustryType",e),this.setFieldValue("hidIndustryTypeIDs",o),this.setFieldValue("hidAllIndustryTypeNames",s),this.setFieldValue("hidAllIndustryTypeIDs",h),i=0,f=0,t.ProspectTypeId>0&&(i=i+1),t.IscrmProspectBoardLevel!=null&&(i=i+1),t.IsFinalAssembly!=null&&(i=i+1),t.EndCustomer!=""&&(i=i+1),l==!0&&(i=i+1),t.ElectronicSpendId>0&&(i=i+1),t.FrequencyOfPurchaseId>0&&(i=i+1),t.CommoditiesName.length>0&&(i=i+1),t.TurnoverId>0&&(i=i+1),t.LimitedEstimate!=""&&t.LimitedEstimate!=undefined&&t.HealthRating!=""&&t.HealthRating!=undefined?i=i+1:(t.LimitedEstimate!=""&&t.LimitedEstimate!=undefined&&(f=f+.5),t.HealthRating!=""&&t.HealthRating!=undefined&&(f=f+.5)),u="",c="",r=0;r<=10;r++)r<=i?u=u+" <img src='../../../../images/crmicons/"+r+".png'>":c=c+" <img src='../../../../images/crmicons/"+r+"-disable.png'>";f>0&&(u=u+" <img src='../../../../images/crmicons/"+(i+f)+".png'>");u=u+c;u=u+'<br/><span style=" margin - left: 235px;color: green;"><b>Percentage complete<\/b><\/span> <br/><br/> <span style=" margin - left: 235px;color: red;"><b>"Credit Limit Potential" and "Health Rating" is calculated as 5% each and the rest is 10%<\/b><\/span><br/><br/>';$("#dvCompletePecent").html(u);this.getDataOK_End()},getDataError:function(n){this.showError(!0,n.get_ErrorMessage())},getCompanyInactive:function(){var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/CompanyProspects");n.set_DataObject("CompanyProspects");n.set_DataAction("GetCompanyDetailInactive");n.addParameter("id",this._intCustomerID);n.addDataOK(Function.createDelegate(this,this.getCompanyInactiveOK));n.addError(Function.createDelegate(this,this.getCompanyInactiveError));n.addTimeout(Function.createDelegate(this,this.getCompanyInactiveError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getCompanyInactiveOK:function(n){var t=n._result;this._inactive=t.Inactive;this._ibtnAdd&&$R_IBTN.enableButton(this._ibtnAdd,!this._inactive);this._ibtnEdit&&$R_IBTN.enableButton(this._ibtnEdit,!this._inactive);this._ibtnViewTask&&$R_IBTN.enableButton(this._ibtnViewTask,!this._inactive)},getCompanyInactiveError:function(n){this.showError(!0,n.get_ErrorMessage())},showEditForm:function(){var n;this.showForm(this._frmEdit,!0);this._frmEdit._intCRMProspectID=this._intCRMProspectID;this._frmEdit._intCustomerID=this._intCustomerID;this._frmEdit.setFieldValue("ctlProspectType",this.getFieldValue("hidProspectTypeId"));this._frmEdit.setFieldValue("ctlEndCustomer",this.getFieldValue("ctlEndCustomer"));this._frmEdit.setFieldValue("ctlLimitedEstimate",this.getFieldValue("hidLimitedEstimate"));this._frmEdit.setFieldValue("ctlHealthRating",this.getFieldValue("hidHealthRating"));this._frmEdit.setFieldValue("ctlElectronicSpend",this.getFieldValue("hidElectronicSpendId"));this._frmEdit.setFieldValue("ctlFrequencyOfPurchase",this.getFieldValue("hidFrequencyOfPurchaseId"));this._frmEdit.setFieldValue("ctlCommodities",this.getFieldValue("ctlCommodities"));this._frmEdit.setFieldValue("ctlTurnover",this.getFieldValue("hidTurnoverId"));this.getFieldValue("hidMFRBoardLevel")=="true"?$get("ctl00_cphMain_ctlCRMProspects_ctlDB_ctl14_ctlCompanyProspects_Edit_ctlDB_ctlMFRBoardLevel_ctl03_radMFRBoardLevel_0").checked=!0:this.getFieldValue("hidMFRBoardLevel")=="false"&&($get("ctl00_cphMain_ctlCRMProspects_ctlDB_ctl14_ctlCompanyProspects_Edit_ctlDB_ctlMFRBoardLevel_ctl03_radMFRBoardLevel_1").checked=!0);this.getFieldValue("hidFinalAssembly")=="true"?$get("ctl00_cphMain_ctlCRMProspects_ctlDB_ctl14_ctlCompanyProspects_Edit_ctlDB_ctlFinalAssembly_ctl03_radFinalAssembly_0").checked=!0:this.getFieldValue("hidFinalAssembly")=="false"&&($get("ctl00_cphMain_ctlCRMProspects_ctlDB_ctl14_ctlCompanyProspects_Edit_ctlDB_ctlFinalAssembly_ctl03_radFinalAssembly_1").checked=!0);this._frmEdit._ctlSelectIndustryType.clearData();var r=$R_FN.singleStringToArray(this.getFieldValue("hidIndustryTypeIDs")),t=$R_FN.singleStringToArray(this.getFieldValue("hidAllIndustryTypeNames")),i=$R_FN.singleStringToArray(this.getFieldValue("hidAllIndustryTypeIDs"));for(n=0;n<t.length;n++)this._frmEdit._ctlSelectIndustryType.addRow(Array.contains(r,i[n]),[$R_FN.setCleanTextValue(t[n])],i[n]);this._frmEdit._ctlSelectIndustryType.populateTables();this._frmEdit.showInnerContent(!0)},cancelEdit:function(){this.showForm(this._frmEdit,!1)},saveEditComplete:function(){this.showForm(this._frmEdit,!1);this.showContentLoading(!1);this.getData();this.showSavedOK(!0,$R_RES.ChangesSavedSuccessfully);this.onSaveEditComplete()},saveEditError:function(){this.showError(!0,this._frmEdit._strErrorMessage)},showAddForm:function(){this._frmAdd.setFormFieldsToDefaults();this._frmAdd.setFieldValue("ctlDueTime","09:00");this._frmAdd.setFieldValue("ctlReminderTime","09:00");this.showForm(this._frmAdd,!0);this._frmAdd.setFieldValue("ctlCompanyNew",this._intCustomerID,null,$R_FN.setCleanTextValue($("#ctl00_cphMain_ctlMainCompanyInfo_ctlDB_ctl13_ctlCompanyName_lbl").text()));this._frmAdd.setFieldValue("ctlCompany",'<a style="color:white;" href="Con_CompanyDetail.aspx?cm='+this._intCustomerID+'">'+$R_FN.setCleanTextValue($("#ctl00_cphMain_ctlMainCompanyInfo_ctlDB_ctl13_ctlCompanyName_lbl").text())+"<\/a>");this._frmAdd.showField("ctlCompanyNew",!1)},cancelAdd:function(){this.showForm(this._frmAdd,!1)},saveAddComplete:function(){this.showForm(this._frmAdd,!1);this.showContentLoading(!1);this.getData();this.showSavedOK(!0,$R_RES.ChangesSavedSuccessfully);this.onSaveEditComplete()},saveAddError:function(){this.showError(!0,this._frmAdd._strErrorMessage)},redirectToDetails:function(){location.href="Prf_ToDo.aspx?cm="+this._intCustomerID+"&cmn="+$R_FN.setCleanTextValue($("#ctl00_cphMain_ctlMainCompanyInfo_ctlDB_ctl13_ctlCompanyName_lbl").text());"_blank"}};Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyProspects.registerClass("Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyProspects",Rebound.GlobalTrader.Site.Controls.Nuggets.Base);