Type.registerNamespace("Rebound.GlobalTrader.Site.Controls");Rebound.GlobalTrader.Site.Controls.PageTitle=function(n){Rebound.GlobalTrader.Site.Controls.PageTitle.initializeBase(this,[n]);this._aryTabIDs=[];this._intCurrentTab=0};Rebound.GlobalTrader.Site.Controls.PageTitle.prototype={get_ctlH3:function(){return this._ctlH3},set_ctlH3:function(n){this._ctlH3!==n&&(this._ctlH3=n)},get_aryTabIDs:function(){return this._aryTabIDs},set_aryTabIDs:function(n){this._aryTabIDs!==n&&(this._aryTabIDs=n)},get_intCurrentTab:function(){return this._intCurrentTab},set_intCurrentTab:function(n){this._intCurrentTab!==n&&(this._intCurrentTab=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.PageTitle.callBaseMethod(this,"initialize")},dispose:function(){this.isDisposed||(this._element&&$clearHandlers(this._element),this._aryTabIDs=null,this._ctlH3=null,this._intCurrentTab=null,Rebound.GlobalTrader.Site.Controls.PageTitle.callBaseMethod(this,"dispose"),this.isDisposed=!0)},updateTitle:function(n){n&&this._ctlH3&&($R_FN.setInnerHTML(this._ctlH3,n),document.title=String.format("{0} - {1}",$R_RES.AppTitle,n))},updateTitleColor:function(n,t){n&&this._ctlH3&&(t?Sys.UI.DomElement.addCssClass(this._ctlH3,n):Sys.UI.DomElement.removeCssClass(this._ctlH3,n))},selectTab:function(n){var t,r,i;if(!(n<0)&&!(n>=this._aryTabIDs.length)){for(t=0,r=this._aryTabIDs.length;t<r;t++)i=$get(String.format("{0}_tab",this._aryTabIDs[t])),i.className=t==n?"tabSelected":"tab",i=null;this._intCurrentTab=n}},getTitle:function(){return this._ctlH3.innerHTML},showTab:function(n,t){var i=$get(String.format("{0}_tab",this._aryTabIDs[n]));i&&$R_FN.showElement(i,t);i=null}};Rebound.GlobalTrader.Site.Controls.PageTitle.registerClass("Rebound.GlobalTrader.Site.Controls.PageTitle",Sys.UI.Control,Sys.IDisposable);