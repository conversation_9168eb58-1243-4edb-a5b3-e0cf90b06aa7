Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.GILines_CloseInspection=function(n){Rebound.GlobalTrader.Site.Controls.Forms.GILines_CloseInspection.initializeBase(this,[n]);this._intLineID=-1;this._intGIID=-1;this._strStartInspection=null;this._lblExplainStartInspection=null;this._aryUnpostedLineIDs=[];this._aryPostedLineIDs=[];this._intPurchaseOrderID=0;this._ctlConfirm=null;this._intValidationCount=0};Rebound.GlobalTrader.Site.Controls.Forms.GILines_CloseInspection.prototype={get_intPurchaseOrderID:function(){return this._intPurchaseOrderID},set_intPurchaseOrderID:function(n){this._intPurchaseOrderID!==n&&(this._intPurchaseOrderID=n)},get_intLineID:function(){return this._intLineID},set_intLineID:function(n){this._intLineID!==n&&(this._intLineID=n)},get_strStartInspection:function(){return this._strStartInspection},set_strStartInspection:function(n){this._strStartInspection!==n&&(this._strStartInspection=n)},get_lblExplainStartInspection:function(){return this._lblExplainStartInspection},set_lblExplainStartInspection:function(n){this._lblExplainStartInspection!==n&&(this._lblExplainStartInspection=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Forms.GILines_CloseInspection.callBaseMethod(this,"initialize");this.addShown(Function.createDelegate(this,this.formShown))},formShown:function(){this._blnFirstTimeShown&&(this._ctlConfirm=this.getFieldComponent("ctlConfirm"),this._ctlConfirm.addClickYesEvent(Function.createDelegate(this,this.yesClicked)),this._ctlConfirm.addClickNoEvent(Function.createDelegate(this,this.noClicked)));this._intValidationCount=0;this.GetCloseInspectionData();this.checkMode()},GetCloseInspectionData:function(){$R_FN.showElement(this._pnlLines,!1);var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/GILines");n.set_DataObject("GILines");n.set_DataAction("GetCloseInspectionData");n.addParameter("ID",this._intLineID);n.addDataOK(Function.createDelegate(this,this.GetCloseInspectionDataOK));n.addError(Function.createDelegate(this,this.GetCloseInspectionDataError));n.addTimeout(Function.createDelegate(this,this.GetCloseInspectionDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},GetCloseInspectionDataOK:function(n){var t=n._result;$("#lblPartTicked").text(t.PartNumberFilled);$("#lblPBComplete").text(t.PackageBreakdownComplete);$("#lblPhotosAttached").text(t.PhotosAttached);$("#lblBarCodeScan").text(t.BarcodeScannedTicked);$("#lblQueryFormat").text(t.QueryFormatUse)},GetCloseInspectionDataError:function(n){this._strErrorMessage=n._errorMessage;this.onSaveError()},dispose:function(){this.isDisposed||(this._ctlConfirm&&this._ctlConfirm.dispose(),this._ctlConfirm=null,this._intLineID=null,this._strStartInspection=null,this._lblExplainStartInspection=null,this._aryUnpostedLineIDs=null,this._aryPostedLineIDs=null,this._intPurchaseOrderID=null,this._intValidationCount=null,Rebound.GlobalTrader.Site.Controls.Forms.POLines_Post.callBaseMethod(this,"dispose"))},yesClicked:function(){this.showSaving(!0);var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("Controls/Nuggets/GILines");n.set_DataObject("GILines");n.set_DataAction("CloseInspection");n.addParameter("id",this._intLineID);n.addParameter("Comment",this.getFieldValue("ctlComment"));n.addDataOK(Function.createDelegate(this,this.saveComplete));n.addError(Function.createDelegate(this,this.saveError));n.addTimeout(Function.createDelegate(this,this.saveError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null;this.onSave()},noClicked:function(){this.showSaving(!1);this.onNotConfirmed();this.setFieldValue("ctlComment","")},saveError:function(n){this.showSaving(!1);this._strErrorMessage=n._errorMessage;this.onSaveError()},saveComplete:function(n){this.showSaving(!1);n._result.Result==!0?(this.onSaveComplete(),this.setFieldValue("ctlComment",""),setTimeout(function(){$("#ctl00_cphMain_ctlLines_ctlDB_imgRefresh").trigger("click")},900)):(this._strErrorMessage=n._errorMessage,this.onSaveError())},checkMode:function(){this.changeTitle(this._strStartInspection);$R_FN.showElement(this._lblExplainStartInspection,!0)},EnableYesButtonPart:function(){var n=this.getControlValue("ctl00_cphMain_ctlLines_ctlDB_ctl14_GILines_CloseInspection_ctlDB_chkPartTicked","CheckBox");n==!0?this._intValidationCount+=1:this._intValidationCount-=1;this._intValidationCount==5?$R_IBTN.enableButton(this._ctlConfirm._ibtnYes,!0):$R_IBTN.enableButton(this._ctlConfirm._ibtnYes,!1)},EnableYesButtonPB:function(){var n=this.getControlValue("ctl00_cphMain_ctlLines_ctlDB_ctl14_GILines_CloseInspection_ctlDB_chkPBComplete","CheckBox");n==!0?this._intValidationCount+=1:this._intValidationCount-=1;this._intValidationCount==5?$R_IBTN.enableButton(this._ctlConfirm._ibtnYes,!0):$R_IBTN.enableButton(this._ctlConfirm._ibtnYes,!1)},EnableYesButtonPhotoAttached:function(){var n=this.getControlValue("ctl00_cphMain_ctlLines_ctlDB_ctl14_GILines_CloseInspection_ctlDB_chkPhotosAttached","CheckBox");n==!0?this._intValidationCount+=1:this._intValidationCount-=1;this._intValidationCount==5?$R_IBTN.enableButton(this._ctlConfirm._ibtnYes,!0):$R_IBTN.enableButton(this._ctlConfirm._ibtnYes,!1)},EnableYesButtonBarCode:function(){var n=this.getControlValue("ctl00_cphMain_ctlLines_ctlDB_ctl14_GILines_CloseInspection_ctlDB_chkBarCodeScan","CheckBox");n==!0?this._intValidationCount+=1:this._intValidationCount-=1;this._intValidationCount==5?$R_IBTN.enableButton(this._ctlConfirm._ibtnYes,!0):$R_IBTN.enableButton(this._ctlConfirm._ibtnYes,!1)},EnableYesButtonQueryFormat:function(){var n=this.getControlValue("ctl00_cphMain_ctlLines_ctlDB_ctl14_GILines_CloseInspection_ctlDB_chkQueryFormat","CheckBox");n==!0?this._intValidationCount+=1:this._intValidationCount-=1;this._intValidationCount==5?$R_IBTN.enableButton(this._ctlConfirm._ibtnYes,!0):$R_IBTN.enableButton(this._ctlConfirm._ibtnYes,!1)}};Rebound.GlobalTrader.Site.Controls.Forms.GILines_CloseInspection.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.GILines_CloseInspection",Rebound.GlobalTrader.Site.Controls.Forms.Base,Sys.IDisposable);