<%@ Control Language="C#" CodeBehind="UncheckedIPO.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.HomeNuggets.UncheckedIPO" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Nugget:DesignBase id="ctlDB" runat="server">
	<Content>
		<div class="homepageNugget">
		    <asp:Panel ID="pnlPartsOrdered" runat="server" CssClass="overdue">
			    <ReboundUI:SimpleDataTable ID="tblPartsOrdered" runat="server" AllowSelection="false" />
		    </asp:Panel>
		    <asp:Panel ID="pnlMore" runat="server" CssClass="homeNuggetMoreLink">
				<ReboundUI:PageHyperLink id="lnkMore" runat="server" PageType="Orders_InternalPurchaseOrderBrowse" OverrideTextResource="MoreOpenInternalPurchaseOrders" CssClass="nubButton nubButtonAlignLeft" />
			</asp:Panel>
		</div>
	</Content>
</ReboundUI_Nugget:DesignBase>
