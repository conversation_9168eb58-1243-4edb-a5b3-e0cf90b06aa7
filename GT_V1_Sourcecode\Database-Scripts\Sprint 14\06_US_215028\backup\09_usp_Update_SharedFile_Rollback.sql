﻿SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
/*   
===========================================================================================  
TASK			UPDATED BY			DATE			ACTION		DESCRIPTION  
[US-211441]		Trung Pham Van		24-Sep-2024		CREATE		Update date when offer is sent
===========================================================================================  
*/
CREATE OR ALTER PROCEDURE [dbo].[usp_Update_SharedFile]
	@ProspectiveOfferId INT,
	@ProspectiveOfferLineIds VARCHAR(MAX)
AS
BEGIN
	UPDATE tbProspectiveOfferLines
	SET SentDate = GETDATE()
	WHERE ProspectiveOfferNo = @ProspectiveOfferId 
	AND ProspectiveOfferLineId IN (SELECT CAST(value AS INT) FROM STRING_SPLIT(@ProspectiveOfferLineIds, ','))
END
