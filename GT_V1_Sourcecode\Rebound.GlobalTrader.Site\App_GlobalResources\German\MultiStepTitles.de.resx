﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="CountryAdd_EnterDetail" xml:space="preserve">
    <value>Tragen Sie Details ein</value>
  </data>
  <data name="CountryAdd_SelectSource" xml:space="preserve">
    <value>Wählen Sie Quelle vor</value>
  </data>
  <data name="CreditAdd_EnterDetail" xml:space="preserve">
    <value>Tragen Sie Details ein</value>
  </data>
  <data name="CreditAdd_Notify" xml:space="preserve">
    <value>Teilen Sie mit</value>
  </data>
  <data name="CreditAdd_SelectCRMA" xml:space="preserve">
    <value>Wählen Sie Kunden RMA vor</value>
  </data>
  <data name="CreditAdd_SelectInvoice" xml:space="preserve">
    <value>Wählen Sie Rechnung vor</value>
  </data>
  <data name="CreditAdd_SelectItem" xml:space="preserve">
    <value>Wählen Sie Einzelteil vor</value>
  </data>
  <data name="CreditAdd_SelectSource" xml:space="preserve">
    <value>Wählen Sie Quelle vor</value>
  </data>
  <data name="CreditLine_Add_EditDetails" xml:space="preserve">
    <value>Redigieren Sie Details</value>
  </data>
  <data name="CreditLine_Add_SelectItem" xml:space="preserve">
    <value>Wählen Sie Einzelteil vor</value>
  </data>
  <data name="CreditLine_Add_SelectSource" xml:space="preserve">
    <value>Wählen Sie Quelle vor</value>
  </data>
  <data name="CRMAAdd_EnterDetail" xml:space="preserve">
    <value>Tragen Sie Details ein</value>
  </data>
  <data name="CRMAAdd_Notify" xml:space="preserve">
    <value>Teilen Sie mit</value>
  </data>
  <data name="CRMAAdd_SelectInvoice" xml:space="preserve">
    <value>Wählen Sie Rechnung vor</value>
  </data>
  <data name="CRMALines_Add_EnterDetail" xml:space="preserve">
    <value>Redigieren Sie Details</value>
  </data>
  <data name="CRMALines_Add_SelectItem" xml:space="preserve">
    <value>Wählen Sie Rechnungs-Linie vor</value>
  </data>
  <data name="CRMAReceivingLines_Receive_Detail" xml:space="preserve">
    <value>Tragen Sie Details ein</value>
  </data>
  <data name="CRMAReceivingLines_Receive_Header" xml:space="preserve">
    <value>Überschrift-Details</value>
  </data>
  <data name="CRMAReceivingLines_Receive_Notify" xml:space="preserve">
    <value>Teilen Sie mit</value>
  </data>
  <data name="CRMAReceivingLines_Receive_SelectItem" xml:space="preserve">
    <value>Wählen Sie Einzelteil vor</value>
  </data>
  <data name="CRMAReceivingLines_Receive_Source" xml:space="preserve">
    <value>Wählen Sie Quelle vor</value>
  </data>
  <data name="CurrencyAdd_EnterDetail" xml:space="preserve">
    <value>Tragen Sie Details ein</value>
  </data>
  <data name="CurrencyAdd_SelectSource" xml:space="preserve">
    <value>Wählen Sie Quelle vor</value>
  </data>
  <data name="CusReqAdd_EnterDetail" xml:space="preserve">
    <value>Tragen Sie Details ein</value>
  </data>
  <data name="CusReqAdd_Notify" xml:space="preserve">
    <value>Teilen Sie mit</value>
  </data>
  <data name="CusReqAdd_SelectCompany" xml:space="preserve">
    <value>Wählen Sie Firma vor</value>
  </data>
  <data name="DebitAdd_EnterDetail" xml:space="preserve">
    <value>Tragen Sie Details ein</value>
  </data>
  <data name="DebitAdd_Notify" xml:space="preserve">
    <value>Teilen Sie mit</value>
  </data>
  <data name="DebitAdd_SelectPO" xml:space="preserve">
    <value>Wählen Sie Kaufauftrag vor</value>
  </data>
  <data name="DebitLine_Add_EditDetails" xml:space="preserve">
    <value>Redigieren Sie Details</value>
  </data>
  <data name="DebitLine_Add_SelectItem" xml:space="preserve">
    <value>Wählen Sie Einzelteil vor</value>
  </data>
  <data name="DebitLine_Add_SelectSource" xml:space="preserve">
    <value>Wählen Sie Quelle vor</value>
  </data>
  <data name="GIAdd_EnterDetail" xml:space="preserve">
    <value>Tragen Sie Details ein</value>
  </data>
  <data name="GIAdd_Notify" xml:space="preserve">
    <value>Teilen Sie mit</value>
  </data>
  <data name="GIAdd_SelectPO" xml:space="preserve">
    <value>Wählen Sie Kaufauftrag vor</value>
  </data>
  <data name="InvoiceAdd_EnterDetail" xml:space="preserve">
    <value>Tragen Sie Details ein</value>
  </data>
  <data name="InvoiceAdd_Notify" xml:space="preserve">
    <value>Teilen Sie mit</value>
  </data>
  <data name="InvoiceAdd_SelectSO" xml:space="preserve">
    <value>Wählen Sie Verkaufs-Auftrag vor</value>
  </data>
  <data name="InvoiceLines_Add_EnterDetail" xml:space="preserve">
    <value>Bestätigen Sie </value>
  </data>
  <data name="InvoiceLines_Add_SelectItem" xml:space="preserve">
    <value>Wählen Sie Hausanschlussleitung vor</value>
  </data>
  <data name="InvoiceLines_Add_SelectSource" xml:space="preserve">
    <value>Wählen Sie Quelle vor</value>
  </data>
  <data name="Notify" xml:space="preserve">
    <value>Teilen Sie mit</value>
  </data>
  <data name="POAdd_EnterDetail" xml:space="preserve">
    <value>Tragen Sie Details ein</value>
  </data>
  <data name="POAdd_Notify" xml:space="preserve">
    <value>Teilen Sie mit</value>
  </data>
  <data name="POAdd_SelectCompany" xml:space="preserve">
    <value>Wählen Sie Firma vor</value>
  </data>
  <data name="POLine_Add_EditDetails" xml:space="preserve">
    <value>Redigieren Sie Details</value>
  </data>
  <data name="POLine_Add_SelectItem" xml:space="preserve">
    <value>Wählen Sie Einzelteil vor</value>
  </data>
  <data name="POLine_Add_SelectSource" xml:space="preserve">
    <value>Wählen Sie Quelle vor</value>
  </data>
  <data name="POReceivingLines_Receive_Detail" xml:space="preserve">
    <value>Tragen Sie Linie Details ein</value>
  </data>
  <data name="POReceivingLines_Receive_Header" xml:space="preserve">
    <value>Überschrift-Details</value>
  </data>
  <data name="POReceivingLines_Receive_NewOrExisting" xml:space="preserve">
    <value>Wählen Sie neue oder vorhandene Überschrift vor</value>
  </data>
  <data name="POReceivingLines_Receive_Notify" xml:space="preserve">
    <value>Teilen Sie mit</value>
  </data>
  <data name="POReceivingLines_Receive_Target" xml:space="preserve">
    <value>Wählen Sie Ziel vor</value>
  </data>
  <data name="QuoteAdd_EnterDetail" xml:space="preserve">
    <value>Tragen Sie Details ein</value>
  </data>
  <data name="QuoteAdd_Notify" xml:space="preserve">
    <value>Teilen Sie mit</value>
  </data>
  <data name="QuoteAdd_SelectItem" xml:space="preserve">
    <value>Wählen Sie Einzelteil vor</value>
  </data>
  <data name="QuoteAdd_SelectSource" xml:space="preserve">
    <value>Wählen Sie Quelle vor</value>
  </data>
  <data name="QuoteLine_Add_EditDetails" xml:space="preserve">
    <value>Tragen Sie Details ein</value>
  </data>
  <data name="QuoteLine_Add_SelectItem" xml:space="preserve">
    <value>Wählen Sie Einzelteil vor</value>
  </data>
  <data name="QuoteLine_Add_SelectSource" xml:space="preserve">
    <value>Wählen Sie Quelle vor</value>
  </data>
  <data name="SOAdd_EnterDetail" xml:space="preserve">
    <value>Tragen Sie Details ein</value>
  </data>
  <data name="SOAdd_Notify" xml:space="preserve">
    <value>Teilen Sie mit</value>
  </data>
  <data name="SOAdd_SelectCompany" xml:space="preserve">
    <value>Wählen Sie Firma vor</value>
  </data>
  <data name="SOLine_Add_EditDetails" xml:space="preserve">
    <value>Redigieren Sie Details</value>
  </data>
  <data name="SOLine_Add_SelectItem" xml:space="preserve">
    <value>Wählen Sie Einzelteil vor</value>
  </data>
  <data name="SOLine_Add_SelectSource" xml:space="preserve">
    <value>Wählen Sie Quelle vor</value>
  </data>
  <data name="SOLine_Allocate_SelectStock" xml:space="preserve">
    <value>Wählen Sie auf lagereinzelteil vor</value>
  </data>
  <data name="SOLine_Alocate_EditDetails" xml:space="preserve">
    <value>Redigieren Sie Details</value>
  </data>
  <data name="SOShippingLines_Ship_Detail" xml:space="preserve">
    <value>Tragen Sie Linie Details ein</value>
  </data>
  <data name="SOShippingLines_Ship_Header" xml:space="preserve">
    <value>Überschrift-Details</value>
  </data>
  <data name="SOShippingLines_Ship_Notify" xml:space="preserve">
    <value>Teilen Sie mit</value>
  </data>
  <data name="SOShippingLines_Ship_Target" xml:space="preserve">
    <value>Wählen Sie Ziel vor</value>
  </data>
  <data name="Sourcing_AddToReq_Confirm" xml:space="preserve">
    <value>Bestätigen Sie </value>
  </data>
  <data name="Sourcing_AddToReq_SelectCusReq" xml:space="preserve">
    <value>Wählen Sie Kunden-Anforderung vor</value>
  </data>
  <data name="SRMAAdd_EnterDetail" xml:space="preserve">
    <value>Tragen Sie Details ein</value>
  </data>
  <data name="SRMAAdd_Notify" xml:space="preserve">
    <value>Teilen Sie mit</value>
  </data>
  <data name="SRMAAdd_SelectPO" xml:space="preserve">
    <value>Wählen Sie Kaufauftrag vor</value>
  </data>
  <data name="SRMALine_Allocate_EditDetails" xml:space="preserve">
    <value>Redigieren Sie Details</value>
  </data>
  <data name="SRMALine_Allocate_SelectStock" xml:space="preserve">
    <value>Wählen Sie Quelle vor</value>
  </data>
  <data name="SRMALines_Add_EnterDetail" xml:space="preserve">
    <value>Redigieren Sie Details</value>
  </data>
  <data name="SRMALines_Add_SelectItem" xml:space="preserve">
    <value>Wählen Sie Kaufauftrag-Linie vor</value>
  </data>
  <data name="SRMAShippingLines_Ship_Detail" xml:space="preserve">
    <value>Tragen Sie Linie Details ein</value>
  </data>
  <data name="SRMAShippingLines_Ship_Header" xml:space="preserve">
    <value>Überschrift-Details</value>
  </data>
  <data name="SRMAShippingLines_Ship_Notify" xml:space="preserve">
    <value>Teilen Sie mit</value>
  </data>
  <data name="SRMAShippingLines_Ship_SelectSource" xml:space="preserve">
    <value>Wählen Sie Quelle vor</value>
  </data>
</root>