﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Web;
using System.Web.Helpers;

namespace Rebound.GlobalTrader.Site.Code.Common
{
    public class IHSManager
    {
        public class PartDetail
        {
            public string msl { get; set; }
            public string pckMethod { get; set; }
            public string pckCd { get; set; }

        }
        public class PartInfo
        {
            public string avgPrice { get; set; }
        }

        public class MfrPart
        {
            public string mfrFullName { get; set; }
            public string coo { get; set; }
        }
        public class ExpCompliance
        {
            public string htsCd { get; set; }
            public string eccn { get; set; }
        }

        public class Attributes
        {
            public MfrPart mfrPart { get; set; }
            public ExpCompliance expCompliance { get; set; }
            public PartDetail partDetail { get; set; }
            public PartInfo partInfo { get; set; }
            // public Collections collections { get; set; }

        }
        public class Price
        {
            public string source { get; set; }
            public double price { get; set; }
            public string currency { get; set; }
            public string telephone { get; set; }
            public string email { get; set; }
            public string priceAvailabilityLink { get; set; }
        }
        //public Price prices { get; set; }
        public class Collections
        {
            public List<Price> prices { get; set; }
        }

        public class Root
        {


            public Int64 id { get; set; }
            public string prtNbr { get; set; }
            public string mfrName { get; set; }
            public string prtStatus { get; set; }
            public string prtDesc { get; set; }
            public Attributes attributes { get; set; }
            public Collections collections { get; set; }



            public Nullable<int> ManufacturerNo { get; set; }
            public string Descriptions { get; set; }
            public string CountryOfOrigin { get; set; }
            public Nullable<int> CountryOfOriginNo { get; set; }
            public string LifeCycleStage { get; set; }

            public string MSL { get; set; }
            public Nullable<int> MSLNo { get; set; }
            public string HTSCode { get; set; }
            public Nullable<double> AveragePrice { get; set; }
            public string Packaging { get; set; }
            public Nullable<int> PackagingSize { get; set; }
            public Nullable<int> UpdatedBy { get; set; }
            public Nullable<System.DateTime> OriginalEntryDate { get; set; }
            public Nullable<System.DateTime> DLUP { get; set; }
            public Nullable<bool> Inactive { get; set; }
        }

        public class TokenRespose
        {
            public string authToken { get; set; }
        }

        public static string ListToXml(List<Root> data)
        {
            StringBuilder strBuilder = new StringBuilder();
            string strinAvgPrice = string.Empty;
            strBuilder.Append("<IHSResults>");
            foreach (IHSManager.Root item in data)
            {
                strBuilder.Append("<IHSResult>");
                strBuilder.Append("<IHSID>");
                strBuilder.Append(item.id);
                strBuilder.Append("</IHSID>");
                // Part
                strBuilder.Append("<prtNbr>");
                //strBuilder.Append(item.prtNbr);
                if (!string.IsNullOrEmpty(item.prtNbr))
                    strBuilder.Append(item.prtNbr.Replace("&", "&amp;"));
                else
                    strBuilder.Append("");
                strBuilder.Append("</prtNbr>");
                //Manuf Name
                strBuilder.Append("<mfrName>");
                strBuilder.Append(item.mfrName);
                strBuilder.Append("</mfrName>");

                strBuilder.Append("<prtStatus>");
                strBuilder.Append(item.prtStatus);
                strBuilder.Append("</prtStatus>");

                strBuilder.Append("<prtDesc>");
                if (!string.IsNullOrEmpty(item.prtDesc))
                {
                    strinAvgPrice = "";
                    if (item.attributes.partInfo != null)
                    {
                        strinAvgPrice = Convert.ToString(item.attributes.partInfo.avgPrice);
                        strBuilder.Append(item.prtDesc.Replace("&", "&amp;") + " , Avg Price : " + strinAvgPrice + " (Date Updated : " + DateTime.Now.ToString() + ")");
                    }
                    else
                    {
                        strBuilder.Append(item.prtDesc.Replace("&", "&amp;"));
                    }
                }
                else
                {
                    strBuilder.Append("");
                }
                strBuilder.Append("</prtDesc>");


                //attributes
                if (item.attributes.mfrPart != null)
                {
                    strBuilder.Append("<mfrFullName>");
                    if (!string.IsNullOrEmpty(item.attributes.mfrPart.mfrFullName))
                        strBuilder.Append(item.attributes.mfrPart.mfrFullName.Replace("&", "&amp;"));
                    else
                        strBuilder.Append("");
                    strBuilder.Append("</mfrFullName>");

                    strBuilder.Append("<coo>");
                    strBuilder.Append(item.attributes.mfrPart.coo);
                    strBuilder.Append("</coo>");
                }
                else
                {
                    strBuilder.Append("<mfrFullName>");
                    strBuilder.Append("");
                    strBuilder.Append("</mfrFullName>");

                    strBuilder.Append("<coo>");
                    strBuilder.Append("");
                    strBuilder.Append("</coo>");
                }
                //expCompliance
                if (item.attributes.expCompliance != null)
                {
                    strBuilder.Append("<htsCd>");
                    strBuilder.Append(item.attributes.expCompliance.htsCd);
                    strBuilder.Append("</htsCd>");

                    strBuilder.Append("<eccn>");
                    strBuilder.Append(item.attributes.expCompliance.eccn);
                    strBuilder.Append("</eccn>");
                }
                else
                {
                    strBuilder.Append("<htsCd>");
                    strBuilder.Append(" ");
                    strBuilder.Append("</htsCd>");

                    strBuilder.Append("<eccn>");
                    strBuilder.Append(" ");
                    strBuilder.Append("</eccn>");
                }
                //Attribute part details
                if (item.attributes.partDetail != null)
                {
                    strBuilder.Append("<msl>");
                    strBuilder.Append(item.attributes.partDetail.msl);
                    strBuilder.Append("</msl>");

                    strBuilder.Append("<pckMethod>");
                    strBuilder.Append(item.attributes.partDetail.pckMethod);
                    strBuilder.Append("</pckMethod>");

                    strBuilder.Append("<pckCd>");
                    strBuilder.Append(item.attributes.partDetail.pckCd);
                    strBuilder.Append("</pckCd>");
                }
                else
                {
                    strBuilder.Append("<msl>");
                    strBuilder.Append(" ");
                    strBuilder.Append("</msl>");

                    strBuilder.Append("<pckMethod>");
                    strBuilder.Append(" ");
                    strBuilder.Append("</pckMethod>");

                    strBuilder.Append("<pckCd>");
                    strBuilder.Append(" ");
                    strBuilder.Append("</pckCd>");

                }
                //Collections Price
                if (item.collections != null)
                {
                    strBuilder.Append("<source>");
                    strBuilder.Append(item.collections.prices[0].source);
                    strBuilder.Append("</source>");

                    strBuilder.Append("<price>");
                    strBuilder.Append(item.collections.prices[0].price);
                    strBuilder.Append("</price>");

                    strBuilder.Append("<currency>");
                    strBuilder.Append(item.collections.prices[0].currency);
                    strBuilder.Append("</currency>");

                    strBuilder.Append("<telephone>");
                    strBuilder.Append(item.collections.prices[0].telephone);
                    strBuilder.Append("</telephone>");

                    strBuilder.Append("<email>");
                    strBuilder.Append(item.collections.prices[0].email);
                    strBuilder.Append("</email>");

                    strBuilder.Append("<priceAvailabilityLink>");
                    strBuilder.Append(item.collections.prices[0].priceAvailabilityLink);
                    strBuilder.Append("</priceAvailabilityLink>");

                }
                else
                {
                    strBuilder.Append("<source>");
                    strBuilder.Append(" ");
                    strBuilder.Append("</source>");

                    strBuilder.Append("<price>");
                    strBuilder.Append("0");
                    strBuilder.Append("</price>");

                    strBuilder.Append("<currency>");
                    strBuilder.Append(" ");
                    strBuilder.Append("</currency>");

                    strBuilder.Append("<telephone>");
                    strBuilder.Append(" ");
                    strBuilder.Append("</telephone>");

                    strBuilder.Append("<email>");
                    strBuilder.Append(" ");
                    strBuilder.Append("</email>");

                    strBuilder.Append("<priceAvailabilityLink>");
                    strBuilder.Append(" ");
                    strBuilder.Append("</priceAvailabilityLink>");
                }
                strBuilder.Append("</IHSResult>");
            }
            strBuilder.Append("</IHSResults>");

            return strBuilder.ToString();
        }
    }
}