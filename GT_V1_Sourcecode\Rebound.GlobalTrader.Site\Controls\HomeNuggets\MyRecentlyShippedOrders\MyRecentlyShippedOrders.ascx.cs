//----------------------------------------------------------------------------------------
// RP 21.12.2009:
//- use SimpleDataTable
//----------------------------------------------------------------------------------------
using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site.Controls;

namespace Rebound.GlobalTrader.Site.Controls.HomeNuggets {
	public partial class MyRecentlyShippedOrders : Base {

		protected SimpleDataTable _tblShipped;

		/// <summary>
		/// On init
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
			this.HomePageNuggetType = "MyRecentlyShippedOrders";
			base.OnInit(e);
			WireUpControls();
			SetupTables();
			AddScriptReference("Controls.HomeNuggets.MyRecentlyShippedOrders.MyRecentlyShippedOrders.js");
		}

		protected override void OnLoad(EventArgs e) {
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyRecentlyShippedOrders", this.ctlDesignBase.ClientID);
			_scScriptControlDescriptor.AddComponentProperty("tblShipped", _tblShipped.ClientID);
			base.OnLoad(e);
		}

		private void SetupTables() {
			_tblShipped.Columns.Add(new SimpleDataColumn("SalesOrder", Unit.Pixel(65)));
			_tblShipped.Columns.Add(new SimpleDataColumn("Customer"));
			_tblShipped.Columns.Add(new SimpleDataColumn("DatePromised","DateShipped", Unit.Pixel(75)));
            
		}

		private void WireUpControls() {
			_tblShipped = (SimpleDataTable)FindContentControl("tblShipped");
		}
	}
}