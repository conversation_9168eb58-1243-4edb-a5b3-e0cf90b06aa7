using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site.Controls;

namespace Rebound.GlobalTrader.Site.Controls.HomeNuggets {
	public partial class MyQualityGIQueries : Base {

		#region Controls
		SimpleDataTable _tblMyQualityGIQueries;
		#endregion

		protected override void OnInit(EventArgs e) {
			this.HomePageNuggetType = "MyQualityGIQueries";
			base.OnInit(e);
            _tblMyQualityGIQueries = (SimpleDataTable)FindContentControl("tblMyQualityGIQueries");
			AddScriptReference("Controls.HomeNuggets.MyQualityGIQueries.MyQualityGIQueries.js");
		}

		protected override void OnLoad(EventArgs e) {
			//table headings
			_tblMyQualityGIQueries.Columns.Add(new SimpleDataColumn("GINumber"));
            _tblMyQualityGIQueries.Columns.Add(new SimpleDataColumn("SONumber"));
            _tblMyQualityGIQueries.Columns.Add(new SimpleDataColumn("QueryDate"));
            _tblMyQualityGIQueries.Columns.Add(new SimpleDataColumn("GIStatus"));

            //setup javascript
            _scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyQualityGIQueries", this.ctlDesignBase.ClientID);
			_scScriptControlDescriptor.AddComponentProperty("tblMyQualityGIQueries", _tblMyQualityGIQueries.ClientID);
			base.OnLoad(e);
		}


	}
}