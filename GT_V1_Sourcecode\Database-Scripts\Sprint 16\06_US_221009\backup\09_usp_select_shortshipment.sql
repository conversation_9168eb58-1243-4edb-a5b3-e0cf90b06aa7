﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

/*   
===========================================================================================  
TASK         UPDATED BY   DATE          ACTION    DESCRIPTION  
[US-210037]  An.TranTan   23-Oct-2024	UPDATE    Get Manufacturer/Supplier advisory notes  
===========================================================================================  
*/ 
CREATE OR ALTER   Procedure [dbo].[usp_select_shortshipment] --107  
(                                                    
@ShortShipmentId int                                                      
)                                                    
AS                                                    
BEGIN        
--DECLARE @CurrencyNo INT=NULL;      
--SET   @CurrencyNo =(select top 1 CurrencyId from tbCurrency where ClientNo=116 and CurrencyCode='JPY')       
    
 declare @vDebitIds Varchar(1000) = NULL;         
 declare @vDebitNumbers Varchar(1000) = NULL;        
 Select @vDebitIds = COALESCE(@vDebitIds + ',','') + COALESCE(Cast(tdb.DebitId As Varchar),'')            
                  , @vDebitNumbers = COALESCE(@vDebitNumbers + ',','') + COALESCE(Cast(tdb.DebitNumber AS varchar),'')          
    From tbDebit tdb        
 join tbShortShipmentDebitNoteLog ssdl on tdb.DebitId=ssdl.DebitNoteNo    
    Where ssdl.ShortShipmentId=@ShortShipmentId      
                                                 
                                                   
Select   distinct                                                  
tss.ShortShipmentId,                                                    
tc.CompanyName as Supplier,                                                    
po.PurchaseOrderNumber as PurchaseOrderNo,                     
po.PurchaseOrderId as PurchaseOrderId,                                               
tl1.EmployeeName AS Salesman,                                      
tss.Salesman as SalesmanId,                                
tss.Reference,                                                    
vGI.GoodsInNumber as  GoodsInNo,                   
vGI.GoodsInId,                                                 
tss.DateReceived,                                                    
tl2.EmployeeName AS Raisedby,                                      
tss.Raisedby as RaisedbyId,                                  
tl3.EmployeeName AS Buyer,                           
tss.Buyer as BuyerId,                          
tss.PartNo,                                                    
tm.ManufacturerName as ManufacturerName,                         
tm.ManufacturerId,                                           
tss.QuantityOrdered,                                                    
tss.QuantityAdvised,                                                    
tss.QuantityReceived,                                                    
tss.ShortageQuantity,                                                    
tss.ShortageValue,                                                                          
tss.IsShortageRefundIssue,                                                    
tss.ShortageRefundIssue,                                                                      
tss.Completedby,                                                    
tss.CompletedDate  ,                                                  
tss.[Status] as StatusId,                                          
ISNULL(tss.IsStageTwoUpdated,0) as IsStageTwoUpdated,                      
CAST((CASE WHEN ISNULL(tss.Status,0)=4 THEN 1 ELSE 0 END) as BIT) as IsCancel,                  
ISNULL(tss.IsDebitNoteExists,0) as IsDebitNoteExists,                  
tss.IsPOHub,                  
CAST((CASE WHEN ISNULL(tss.Status,0)=5 THEN 1 ELSE 0 END) as BIT) as IsClosed,            
vGI.ClientNo,            
tss.DebitNoteNo,          
dbt.DebitNumber as DebitNumber,              
tss.SupportTeamMemberNo,      
tsss.[Status],      
vGI.CurrencyNo as CurrencyNo, -- currency code for JPY client 116 to match price above to 5      
@vDebitIds As 'DebitIds',                              
@vDebitNumbers As 'DebitNumbers',  
vGI.InternalPurchaseOrderId,      
vGI.InternalPurchaseOrderNumber,  
vGI.ClientCurrencyCode,  
vGI.CurrencyCode
, CASE WHEN ISNULL(tc.IsDisplayAdvisory, 0) = 1 THEN tc.AdvisoryNotes ELSE '' END AS CompanyAdvisoryNotes
, CASE WHEN ISNULL(tm.IsDisplayAdvisory, 0) = 1 THEN tm.AdvisoryNotes ELSE '' END AS MfrAdvisoryNotes
 from [tbShortShipment] as tss                             
 --join tbGoodsIn gi on gi.GoodsInId=tss.GoodsInNo                            
 join tbPurchaseOrder po on po.PurchaseOrderId=tss.PurchaseOrderNo     
 JOIN vwGoodsIn vGI on vGI.GoodsInId=tss.GoodsInNo                                        
 left join tbCompany as tc  ON tc.CompanyId = tss.Supplier                                              
 LEFT JOIN tbManufacturer AS tm                                       
    ON tm.ManufacturerId = tss.ManufacturerNo                                              
 LEFT JOIN tbLogin AS tl1                                                
    ON tl1.LoginId = tss.Salesman                                                
  LEFT JOIN tbLogin AS tl2                                                
    ON tl2.LoginId = tss.Raisedby                                                
  LEFT JOIN tbLogin AS tl3                                                
    ON tl3.LoginId = tss.Buyer              
 left join tbDebit dbt on   dbt.DebitId=tss.DebitNoteNo      
  LEFT JOIN tbShortShipmentStatus AS tsss                                    
    ON tsss.StatusId = tss.[Status]            
 where                                                     
ShortShipmentId = @ShortShipmentId     
 end 

GO


