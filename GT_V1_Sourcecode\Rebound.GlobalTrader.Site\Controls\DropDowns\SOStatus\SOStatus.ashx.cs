//--------------------------------------------------------------------------------------------------------
// RP 11.12.2009:
// - cache data
//
// RP 12.10.2009:
// - retrofitted changes from v3.0.34 to get Name from resource file
//-----------------------------------------------------------------------------------------
using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;

namespace Rebound.GlobalTrader.Site.Controls.DropDowns.Data {
    [WebService(Namespace = "http://tempuri.org/")]
    [WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    public class SOStatus : Rebound.GlobalTrader.Site.Data.DropDowns.Base {

        public override void ProcessRequest(HttpContext context) {
            SetDropDownType("SOStatus");
            base.ProcessRequest(context);
        }
        
        protected override void GetData()
        {
            string strCacheOptions = CacheManager.SerializeOptions(new object[] { SessionManager.Culture });
            string strCachedData = CacheManager.GetDropDownData(_objDropDown.ID, strCacheOptions);
            if (string.IsNullOrEmpty(strCachedData))
            {

                //JsonObject jsn = new JsonObject();
                //JsonObject jsnList = new JsonObject(true);    
                //JsonObject jsnItem = new JsonObject();
                //jsnItem.AddVariable("ID", "0");
                //jsnItem.AddVariable("Name", Functions.GetGlobalResource("misc", "Select"));
                //jsnList.AddVariable(jsnItem);
                //jsnItem = new JsonObject();
                //jsnItem.AddVariable("ID", "1");
                //jsnItem.AddVariable("Name", Functions.GetGlobalResource("misc", "New"));
                //jsnList.AddVariable(jsnItem);
                //jsnItem = new JsonObject();
                //jsnItem.AddVariable("ID", "2");
                //jsnItem.AddVariable("Name", Functions.GetGlobalResource("misc", "Unposted"));
                //jsnList.AddVariable(jsnItem);
                //jsnItem = new JsonObject();
                //jsnItem.AddVariable("ID", "3");
                //jsnItem.AddVariable("Name", Functions.GetGlobalResource("misc", "PartPosted"));
                //jsnList.AddVariable(jsnItem);
                //jsnItem = new JsonObject();
                //jsnItem.AddVariable("ID", "4");
                //jsnItem.AddVariable("Name", Functions.GetGlobalResource("misc", "Posted"));
                //jsnList.AddVariable(jsnItem);
                //jsnItem = new JsonObject();
                //jsnItem.AddVariable("ID", "5");
                //jsnItem.AddVariable("Name", Functions.GetGlobalResource("misc", "PartAllocated"));
                //jsnList.AddVariable(jsnItem);
                //jsnItem = new JsonObject();
                //jsnItem.AddVariable("ID", "6");
                //jsnItem.AddVariable("Name", Functions.GetGlobalResource("misc", "Allocated"));
                //jsnList.AddVariable(jsnItem);
                //jsnItem = new JsonObject();
                //jsnItem.AddVariable("ID", "7");
                //jsnItem.AddVariable("Name", Functions.GetGlobalResource("misc", "AuthorisedPartAllocated"));
                //jsnList.AddVariable(jsnItem);
                //jsnItem = new JsonObject();
                //jsnItem.AddVariable("ID", "8");
                //jsnItem.AddVariable("Name", Functions.GetGlobalResource("misc", "Authorised"));
                //jsnList.AddVariable(jsnItem);
                //jsnItem = new JsonObject();
                //jsnItem.AddVariable("ID", "9");
                //jsnItem.AddVariable("Name", Functions.GetGlobalResource("misc", "PartShipped"));
                //jsnList.AddVariable(jsnItem);
                //jsnItem = new JsonObject();
                //jsnItem.AddVariable("ID", "10");
                //jsnItem.AddVariable("Name", Functions.GetGlobalResource("misc", "Complete"));
                //jsnList.AddVariable(jsnItem);
                //jsn.AddVariable("Types", jsnList);
                //jsnItem.Dispose(); jsnItem = null;
                //jsnList.Dispose(); jsnList = null;
                //CacheManager.StoreDropDown(_objDropDown.ID, strCacheOptions, jsn.Result, CacheManager.CacheExpiryType.OneWorkingDay);
                //OutputResult(jsn);
                //jsn.Dispose(); jsn = null;
                JsonObject jsn = new JsonObject();
                JsonObject jsnList = new JsonObject(true);
                JsonObject jsnItem = new JsonObject();
                jsnItem.AddVariable("ID", "");
                jsnItem.AddVariable("Name", Functions.GetGlobalResource("misc", "Select"));
                jsnList.AddVariable(jsnItem);
                jsnItem = new JsonObject();
                jsnItem.AddVariable("ID", "1");
                jsnItem.AddVariable("Name", Functions.GetGlobalResource("misc", "Approve"));
                jsnList.AddVariable(jsnItem);
                jsnItem = new JsonObject();
                jsnItem.AddVariable("ID", "2");
                jsnItem.AddVariable("Name", Functions.GetGlobalResource("misc", "Unapprove"));
                jsnList.AddVariable(jsnItem);
                jsnItem = new JsonObject();
                jsnItem.AddVariable("ID", "3");
                jsnItem.AddVariable("Name", Functions.GetGlobalResource("misc", "Bothoptions"));
                jsnList.AddVariable(jsnItem);
                jsn.AddVariable("Types", jsnList);
                jsnItem.Dispose(); jsnItem = null;
                jsnList.Dispose(); jsnList = null;
                CacheManager.StoreDropDown(_objDropDown.ID, strCacheOptions, jsn.Result, CacheManager.CacheExpiryType.OneWorkingDay);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            else
            {
                _context.Response.Write(strCachedData);
            }
            strCachedData = null;
        }
        
    }


}
