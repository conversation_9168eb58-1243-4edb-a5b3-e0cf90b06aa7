//-----------------------------------------------------------------------------------------
// RP 22.12.2009:
// - allow hiding of virtual warehouses
//
// RP 11.12.2009:
// - cache data
//-----------------------------------------------------------------------------------------
using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;

namespace Rebound.GlobalTrader.Site.Controls.DropDowns.Data {

	[WebService(Namespace = "http://tempuri.org/")]
	[WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
	public class Warehouse : Rebound.GlobalTrader.Site.Data.DropDowns.Base {

		public override void ProcessRequest(HttpContext context) {
			SetDropDownType("Warehouse");
			base.ProcessRequest(context);
		}

		protected override void GetData() {
            int? intPOHubClientNo;
            intPOHubClientNo = GetFormValue_NullableInt("POHubClientNo");
            int? intGlobalLoginClientNo;
            intGlobalLoginClientNo = GetFormValue_NullableInt("GlobalLoginClientNo");

            int? IncludeAll = GetFormValue_NullableInt("IncludeAll")??0;
            string strCacheOptions = CacheManager.SerializeOptions(new object[] { intGlobalLoginClientNo.HasValue ? intGlobalLoginClientNo.Value : SessionManager.ClientID, GetFormValue_Boolean("IncludeVirtual"), (intPOHubClientNo.HasValue) ? intPOHubClientNo.Value : 0 });
			string strCachedData = CacheManager.GetDropDownData(_objDropDown.ID, strCacheOptions);
			if (string.IsNullOrEmpty(strCachedData)) {
				JsonObject jsn = new JsonObject();
				JsonObject jsnList = new JsonObject(true);
                List<BLL.Warehouse> lst;
                //if (intPOHubClientNo.HasValue && intPOHubClientNo.Value > 0)
                //    lst = BLL.Warehouse.DropDownForClient(intPOHubClientNo, GetFormValue_Boolean("IncludeVirtual"));
                //else
                lst = BLL.Warehouse.DropDownForClient((intGlobalLoginClientNo.HasValue && intGlobalLoginClientNo.Value > 0) ? intGlobalLoginClientNo.Value : ((intPOHubClientNo.HasValue && intPOHubClientNo.Value > 0) ? intPOHubClientNo : SessionManager.ClientID), GetFormValue_Boolean("IncludeVirtual"), IncludeAll>0);

				for (int i = 0; i < lst.Count; i++) {
                   // lst = null;
                    JsonObject jsnItem = new JsonObject();
					jsnItem.AddVariable("ID", lst[i].WarehouseId);
					jsnItem.AddVariable("Name", lst[i].WarehouseName);
					jsnList.AddVariable(jsnItem);
					jsnItem.Dispose(); jsnItem = null;
				}
				lst.Clear(); lst = null;
				jsn.AddVariable("Warehouses", jsnList);
				jsnList.Dispose(); jsnList = null;
				CacheManager.StoreDropDown(_objDropDown.ID, strCacheOptions, jsn.Result, CacheManager.CacheExpiryType.OneWorkingDay);
				OutputResult(jsn);
				jsn.Dispose(); jsn = null;
			} else {
				_context.Response.Write(strCachedData);
			}
			strCachedData = null;
			strCacheOptions = null;
		}
	}
}
