﻿/* Marker     changed by      date         Remarks  
   [001]      A<PERSON><PERSON><PERSON>  25-08-2021    Add for supplier PO approval.
   [002]      A<PERSON><PERSON><PERSON>  22-Sep-2021   Add new property for TypeNo
   [003]      Ab<PERSON>av <PERSON>  26-Oct-2021   Add columns for QualityApprovalDate & LineManagerApprovalDate.
   [004]      A<PERSON><PERSON><PERSON>  04-Jan-2022   Add columns for QualityApprovalDate & LineManagerApprovalDate.
   [005]      A<PERSON><PERSON><PERSON>  20-Jan-2022   Add new column IsEscalate.
   [006]      Ab<PERSON>av <PERSON>  27-Jan-2022   Add Line Manager Snapshot.
   [007]      Abhinav <PERSON>a  14-Feb-2022   Add new notes field on Approval popup.
   [008]      Ab<PERSON>av <PERSON>  15-Mar-2022   Add new fields for permissions.
*/
using System;
using System.Data;
using System.Data.SqlClient;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;
using System.Web.Caching;
using System.Data.Common;

namespace Rebound.GlobalTrader.DAL.SqlClient
{
    public class SqlSupplierPoApprovalProvider : SupplierPoApprovalProvider
    {

        /// <summary>
        /// Line Manager Approvals.
        /// Calls [usp_PoSupplierLineManagerApproval]
        /// </summary>
        public override List<SupplierPoApprovalDetails> LineManagerApprovalDeclineIndependentTest(System.Int32? SupplierApprovalId, System.Int32? Status, System.Int32? UpdatedBy, System.Int32? clientId, System.String Notes)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            int RowAffected = 0;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_PoSupplierLineManagerApproval", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@SupplierApprovalId", SqlDbType.Int).Value = SupplierApprovalId;
                cmd.Parameters.Add("@Status", SqlDbType.Int).Value = Status;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = UpdatedBy;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@Notes", SqlDbType.NVarChar).Value = Notes;
                cn.Open();
                SqlDataReader reader = cmd.ExecuteReader();
                List<SupplierPoApprovalDetails> lst = new List<SupplierPoApprovalDetails>();

                while (reader.Read())
                {
                    SupplierPoApprovalDetails obj = new SupplierPoApprovalDetails();
                    obj.PurchaseOrderId = GetReaderValue_Int32(reader, "PurchaseOrderId", 0);
                    obj.PurchaseOrderNumber = GetReaderValue_Int32(reader, "PurchaseOrderNumber", 0);
                    obj.BuyerId = GetReaderValue_Int32(reader, "Buyer", 0);
                    obj.BuyerName = GetReaderValue_String(reader, "BuyerName", "");
                    obj.QualityGroupId = GetReaderValue_Int32(reader, "QualityGroupId", 0);
                    obj.SupplierId = GetReaderValue_Int32(reader, "SupplierId", 0);
                    obj.CommentText = GetReaderValue_String(reader, "RequesterNotes", "");
                    RowAffected = GetReaderValue_Int32(reader, "RowAffected", 0);
                    obj.Result = RowAffected > 0 ? true : false;
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to update line manager approval", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// Get selected data for update screen. 
        /// Calls [usp_select_TradeRefForSupplier]
        /// </summary>
        public override SupplierPoApprovalDetails Get(System.Int32? SupplierApprovalId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_select_TradeRefForSupplier", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@SupplierApprovalId", SqlDbType.Int).Value = SupplierApprovalId;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd, CommandBehavior.SingleRow);
                if (reader.Read())
                {
                    SupplierPoApprovalDetails obj = new SupplierPoApprovalDetails();
                    obj.SupplierApprovalId = GetReaderValue_Int32(reader, "SupplierApprovalId", 0);
                    obj.SupplierId = GetReaderValue_Int32(reader, "SupplierId", 0);
                    obj.SupplierName = GetReaderValue_String(reader, "SupplierName", "");
                    obj.FranchiseweblinkOrEvidence = GetReaderValue_String(reader, "FranchiseweblinkOrEvidence", "");
                    obj.TradeReferenceOne = GetReaderValue_String(reader, "TradeReferenceOne", "");
                    obj.IsPDFAvalableOne = GetReaderValue_Boolean(reader, "IsPDFAvalableOne", false);
                    obj.TradeReferenceTwo = GetReaderValue_String(reader, "TradeReferenceTwo", "");
                    obj.IsPDFAvalableTwo = GetReaderValue_Boolean(reader, "IsPDFAvalableTwo", false);
                    obj.TradeRefrenceThree = GetReaderValue_String(reader, "TrandeReferenceThree", "");
                    obj.IsPDFAvalableThree = GetReaderValue_Boolean(reader, "IsPDFAvalableThree", false);
                    obj.PurchasingMethodNo = GetReaderValue_Int32(reader, "PurchasingMethodNo", 0);
                    obj.PrecogsSupplierNo = GetReaderValue_Int32(reader, "PrecogsSupplierNo", 0);
                    obj.TypeNo = GetReaderValue_String(reader, "TypeNo", "");
                    obj.InDraftMode = GetReaderValue_Boolean(reader, "InDraftMode", false);
                    obj.EvidenceCount = GetReaderValue_Int32(reader, "EvidenceCount", 0);
                    obj.TradeRefOneCount = GetReaderValue_Int32(reader, "TradeRefOneCount", 0);
                    obj.TRadeRefTwoCount = GetReaderValue_Int32(reader, "TRadeRefTwoCount", 0);
                    obj.TradeRefThreeCount = GetReaderValue_Int32(reader, "TradeRefThreeCount", 0);
                    obj.DevicePictureCount = GetReaderValue_Int32(reader, "DevicePictureCount", 0);
                    obj.ManufacturerPictureCount = GetReaderValue_Int32(reader, "ManufacturerPictureCount", 0);
                    obj.TraceblityPictureCount = GetReaderValue_Int32(reader, "TraceblityPictureCount", 0);
                    obj.LineManagerId = GetReaderValue_Int32(reader, "LineManagerId", 0);
                    obj.PurchaseOrderId = GetReaderValue_Int32(reader, "PurchaseOrderId", 0);
                    obj.PurchaseOrderNumber = GetReaderValue_Int32(reader, "PurchaseOrderNumber", 0);
                    obj.IsSendToLineManager = GetReaderValue_Boolean(reader, "IsSendToLineManager", false);
                    obj.SendToLineManagerDLUP = GetReaderValue_DateTime(reader, "SendToLineManagerDLUP", DateTime.MinValue);
                    obj.SendToLineManagerUpdatedBy = GetReaderValue_String(reader, "SendToLineManagerUpdatedBy", "");
                    obj.IsSendToSupplier = GetReaderValue_Boolean(reader, "IsSendToSupplier", false);
                    obj.SendToSupplierDLUP = GetReaderValue_DateTime(reader, "SendToSupplierDLUP", DateTime.MinValue);
                    obj.SendToSupplierUpdatedBy = GetReaderValue_String(reader, "SendToSupplierUpdatedBy", "");
                    obj.IsSendToQuality = GetReaderValue_Boolean(reader, "IsSendToQuality", false);
                    obj.SendToQualityDLUP = GetReaderValue_DateTime(reader, "SendToQualityDLUP", DateTime.MinValue);
                    obj.SendToQualityUpdatedBy = GetReaderValue_String(reader, "SendToQualityUpdatedBy", "");
                    obj.CommentText = GetReaderValue_String(reader, "TradeRefComment", "");
                    obj.IsLineManagerApproved = GetReaderValue_Boolean(reader, "IsLineManagerApproved", false);
                    return obj;
                }
                else
                {
                    return null;
                }
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get PurchaseOrderLine", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// Get selected data for update screen. 
        /// Calls [usp_update_LineManagerDetails]
        /// </summary>
        public override SupplierPoApprovalDetails UpdateLineManagerDetails(System.Int32? SupplierApprovalId, System.Int32? LineManagerId, System.Boolean? IsSendToSupplier, System.Boolean? IsSendToLineManager, System.Int32? LoginId, System.Boolean? isSendToQuality, System.Int32? ClientId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_update_LineManagerDetails", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@SupplierApprovalId", SqlDbType.Int).Value = SupplierApprovalId;
                cmd.Parameters.Add("@LineManagerId", SqlDbType.Int).Value = LineManagerId;
                cmd.Parameters.Add("@IsSendToSupplier", SqlDbType.Bit).Value = IsSendToSupplier;
                cmd.Parameters.Add("@IsSendToLineManager", SqlDbType.Bit).Value = IsSendToLineManager;
                cmd.Parameters.Add("@LoginId", SqlDbType.Int).Value = LoginId;
                cmd.Parameters.Add("@IsSendToQuality", SqlDbType.Bit).Value = isSendToQuality;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = ClientId;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd, CommandBehavior.SingleRow);
                if (reader.Read())
                {
                    SupplierPoApprovalDetails obj = new SupplierPoApprovalDetails();
                    obj.LineManagerId = GetReaderValue_Int32(reader, "LineManagerId", 0);
                    obj.LineManagerName = GetReaderValue_String(reader, "LineManagerName", "");
                    obj.LineManagerEmail = GetReaderValue_String(reader, "LineManagerEmail", "");
                    obj.QualityGroupId = GetReaderValue_Int32(reader, "QualityGroupId", 0);
                    return obj;
                }
                else
                {
                    return null;
                }
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get PurchaseOrderLine", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// Get selected data for update screen. 
        /// Calls [usp_Change_LineManager]
        /// </summary>
        public override SupplierPoApprovalDetails ChangeLineManager(System.Int32? SupplierApprovalId, System.Int32? LineManagerId, System.Int32? LoginId, System.Int32? ClientId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_Change_LineManager", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@SupplierApprovalId", SqlDbType.Int).Value = SupplierApprovalId;
                cmd.Parameters.Add("@LineManagerId", SqlDbType.Int).Value = LineManagerId;
                cmd.Parameters.Add("@LoginId", SqlDbType.Int).Value = LoginId;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = ClientId;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd, CommandBehavior.SingleRow);
                if (reader.Read())
                {
                    SupplierPoApprovalDetails obj = new SupplierPoApprovalDetails();
                    obj.LineManagerId = GetReaderValue_Int32(reader, "LineManagerId", 0);
                    obj.LineManagerName = GetReaderValue_String(reader, "LineManagerName", "");
                    obj.LineManagerEmail = GetReaderValue_String(reader, "LineManagerEmail", "");
                    return obj;
                }
                else
                {
                    return null;
                }
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get PurchaseOrderLine", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        /// <summary>
        /// Get data for Supplier Approval History tab. 
        /// Calls [usp_selectSupplierApprovalPOHistory]
        /// </summary>
        public override List<SupplierPoApprovalDetails> GetData_SupplierApprovalHistory(System.Int32? purchaseOrderId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_selectSupplierApprovalPOHistory", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@PurchaseOrderId", SqlDbType.Int).Value = purchaseOrderId;

                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<SupplierPoApprovalDetails> lst = new List<SupplierPoApprovalDetails>();
                while (reader.Read())
                {
                    SupplierPoApprovalDetails obj = new SupplierPoApprovalDetails();
                    obj.SupplierApprovalId = GetReaderValue_Int32(reader, "SupplierApprovalId", 0);
                    obj.SupplierId = GetReaderValue_Int32(reader, "SupplierId", 0);
                    obj.SupplierName = GetReaderValue_String(reader, "SupplierName", "");
                    obj.ApprovalStatus = GetReaderValue_String(reader, "ApprovalStatus", "");
                    obj.ApprovedDated = GetReaderValue_DateTime(reader, "ApprovedDated", DateTime.MinValue);
                    obj.ApprovedBy = GetReaderValue_String(reader, "ApprovedBy", "");

                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get PurchaseOrderLines", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// Get data for Approval Status tab. 
        /// Calls [usp_selectSupplierPoApproval]
        /// </summary>
        public override List<SupplierPoApprovalDetails> GetData_ApprovalStatus(System.Int32? purchaseOrderId, System.Int32? LoginId, System.Int32? ClientNo)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_selectSupplierPoApproval", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@PurchaseOrderId", SqlDbType.Int).Value = purchaseOrderId;
                cmd.Parameters.Add("@LoginId", SqlDbType.Int).Value = LoginId;
                //cmd.Parameters.Add("@ClientNo", SqlDbType.Int).Value = ClientNo;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<SupplierPoApprovalDetails> lst = new List<SupplierPoApprovalDetails>();
                while (reader.Read())
                {
                    SupplierPoApprovalDetails obj = new SupplierPoApprovalDetails();
                    obj.SupplierApprovalId = GetReaderValue_Int32(reader, "SupplierApprovalId", 0);
                    obj.SupplierId = GetReaderValue_Int32(reader, "SupplierId", 0);
                    obj.SupplierName = GetReaderValue_String(reader, "SupplierName", "");
                    obj.ApprovedOrdersCount = GetReaderValue_Int32(reader, "ApprovedOrdersCount", 0);
                    obj.SupplierRMAsCount = GetReaderValue_Int32(reader, "SupplierRMAsCount", 0);
                    obj.PurchasingMethod = GetReaderValue_String(reader, "PurchasingMethod", "");
                    obj.QualityApproval = GetReaderValue_String(reader, "QualityApproval", "");
                    obj.QualityApprovedBy = GetReaderValue_String(reader, "QualityApprovedBy", "");
                    obj.LineManagerApproval = GetReaderValue_String(reader, "LineManagerApproval", "");
                    obj.LineManagerApprovedBy = GetReaderValue_String(reader, "LineManagerApprovedBy", "");
                    obj.SupplierERAIReported = GetReaderValue_Boolean(reader, "SupplierERAIReported", false);
                    obj.PartIsERAIReported = GetReaderValue_Boolean(reader, "PartIsERAIReported", false);
                    obj.IsQualityApproved = GetReaderValue_Boolean(reader, "IsQualityApproved", false);
                    obj.IsLineManagerApproved = GetReaderValue_Boolean(reader, "IsLineManagerApproved", false);
                    obj.QualityApproveDate = GetReaderValue_DateTime(reader, "QualityDLUP", DateTime.MinValue);
                    obj.LineManagerApproveDate = GetReaderValue_DateTime(reader, "LineManagerDLUP", DateTime.MinValue);
                    //[005] Code start
                    obj.IsEscalate = GetReaderValue_Boolean(reader, "IsEscalate", false);
                    //[005] code end

                    //[006] code start
                    obj.PartNo = GetReaderValue_String(reader, "PartNo", "");
                    obj.Quantity = GetReaderValue_Int32(reader, "Quantity", 0);
                    obj.PaymentTerms = GetReaderValue_String(reader, "PaymentTerms", "");
                    obj.Incoterms = GetReaderValue_String(reader, "Incoterms", "");
                    obj.ShipVia = GetReaderValue_String(reader, "ShipVia", "");
                    obj.ShipFromCountry = GetReaderValue_String(reader, "ShipFromCountry", "");
                    obj.TotalValueOfPOCurrency = GetReaderValue_String(reader, "TotalValueOfPOCurrency", "");
                    obj.Margin = GetReaderValue_String(reader, "Margin", "");
                    obj.RepeatOrder = GetReaderValue_String(reader, "RepeatOrder", "");
                    obj.ReboundPurchaserDivision = GetReaderValue_String(reader, "ReboundPurchaserDivision", "");
                    obj.GTClinetForPO = GetReaderValue_String(reader, "GTClinetForPO", "");
                    obj.Warehouse = GetReaderValue_String(reader, "Warehouse", "");
                    obj.CustomerDefinedVendor = GetReaderValue_String(reader, "CustomerDefinedVendor", "");
                    //[006] code end
                    obj.IsSendToQuality = GetReaderValue_Boolean(reader, "IsSendToQuality", false);
                    obj.SupplierApprovalStatus = GetReaderValue_Int32(reader, "SupplierApprovalStatus", 0);
                    obj.ApproverPartERAIReported = GetReaderValue_Boolean(reader, "ApproverPartERAIReported", false);
                    obj.SupplierType = GetReaderValue_String(reader, "CompanyType", "");
                    obj.IsPOApproved = GetReaderValue_Boolean(reader, "POApproved", false);
                    obj.InDraftMode = GetReaderValue_Boolean(reader, "InDraftMode", false);

                    //[008] code start
                    obj.IsLineManagerApprovalPermission = GetReaderValue_Boolean(reader, "IsLineManagerApprovalPermission", false);
                    obj.IsQualityTeamApprovalPermission = GetReaderValue_Boolean(reader, "IsQualityTeamApprovalPermission", false);
                    obj.ISEscalationApprovalPermission = GetReaderValue_Boolean(reader, "ISEscalationApprovalPermission", false);
                    obj.CommentText = GetReaderValue_String(reader, "TradeRefComment", "");
                    obj.LineManagerComment = GetReaderValue_String(reader, "LineManagerComment", "");
                    obj.QualityComment = GetReaderValue_String(reader, "QualityComment", "");
                    obj.WarrantyPeriod = GetReaderValue_Int32(reader, "WarrantyPeriod", 0);
                    obj.CountryOnHighRisk = GetReaderValue_Int32(reader, "CountryOnHighRisk", 0);
                    //[008] code end
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get PurchaseOrderLines", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// Get data for Trade Reference tab. 
        /// Calls [usp_selectSupplierApprovalTradeReferences]
        /// </summary>
        public override List<SupplierPoApprovalDetails> GetData_TradeReference(System.Int32? purchaseOrderId, System.Int32? ClientNo)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_selectSupplierApprovalTradeReferences", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@PurchaseOrderId", SqlDbType.Int).Value = purchaseOrderId;
                //cmd.Parameters.Add("@ClientNo", SqlDbType.Int).Value = ClientNo;

                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<SupplierPoApprovalDetails> lst = new List<SupplierPoApprovalDetails>();
                while (reader.Read())
                {
                    SupplierPoApprovalDetails obj = new SupplierPoApprovalDetails();
                    obj.SupplierApprovalId = GetReaderValue_Int32(reader, "SupplierApprovalId", 0);
                    obj.SupplierId = GetReaderValue_Int32(reader, "SupplierId", 0);
                    obj.SupplierName = GetReaderValue_String(reader, "SupplierName", "");
                    obj.FranchiseweblinkOrEvidence = GetReaderValue_String(reader, "FranchiseweblinkOrEvidence", "");
                    obj.TradeReferenceOne = GetReaderValue_String(reader, "TradeReferenceOne", "");
                    obj.IsPDFAvalableOne = GetReaderValue_Boolean(reader, "IsPDFAvalableOne", false);
                    obj.TradeReferenceTwo = GetReaderValue_String(reader, "TradeReferenceTwo", "");
                    obj.IsPDFAvalableTwo = GetReaderValue_Boolean(reader, "IsPDFAvalableTwo", false);
                    obj.TradeRefrenceThree = GetReaderValue_String(reader, "TrandeReferenceThree", "");
                    obj.IsPDFAvalableThree = GetReaderValue_Boolean(reader, "IsPDFAvalableThree", false);
                    obj.Status = GetReaderValue_String(reader, "Status", "");
                    obj.EvidenceCount = GetReaderValue_Int32(reader, "EvidenceCount", 0);
                    obj.TradeRefOneCount = GetReaderValue_Int32(reader, "TradeRefOneCount", 0);
                    obj.TRadeRefTwoCount = GetReaderValue_Int32(reader, "TRadeRefTwoCount", 0);
                    obj.TradeRefThreeCount = GetReaderValue_Int32(reader, "TradeRefThreeCount", 0);
                    obj.DevicePictureCount = GetReaderValue_Int32(reader, "DevicePictureCount", 0);
                    obj.ManufacturerPictureCount = GetReaderValue_Int32(reader, "ManufacturerPictureCount", 0);
                    obj.TraceblityPictureCount = GetReaderValue_Int32(reader, "TraceblityPictureCount", 0);
                    obj.BuyerId = GetReaderValue_Int32(reader, "BuyerId", 0);
                    obj.SupplierApprovalStatus = GetReaderValue_Int32(reader, "SupplierApprovalStatus", 0);
                    obj.SupplierType = GetReaderValue_String(reader, "CompanyType", "");
                    obj.IsPOApproved = GetReaderValue_Boolean(reader, "POApproved", false);
                    obj.CountryOnHighRisk = GetReaderValue_Int32(reader, "CountryOnHighRisk", 0);
                    
                    
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get PurchaseOrderLines", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// Update Trade reference data.
        /// Calls [usp_update_TradeReferenceDetails]
        /// </summary>
        public override bool Update(System.Int32? SupplierApprovalId, System.String FranchiseweblinkOrEvidence, System.String TradeReferenceOne, System.String TradeReferenceTwo, System.String TradeRefrenceThree, System.Int32? PurchasingMethodNo, System.Int32? PrecogsSupplierNo, System.Int32? UpdatedBy, System.Boolean? InDraftMode, System.String CommentText)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_update_TradeReferenceDetails", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@SupplierApprovalId", SqlDbType.Int).Value = SupplierApprovalId;
                cmd.Parameters.Add("@FranchiseweblinkOrEvidence", SqlDbType.NVarChar, 100).Value = FranchiseweblinkOrEvidence;
                cmd.Parameters.Add("@TradeReferenceOne", SqlDbType.NVarChar, 100).Value = TradeReferenceOne;
                cmd.Parameters.Add("@TradeReferenceTwo", SqlDbType.NVarChar, 100).Value = TradeReferenceTwo;
                cmd.Parameters.Add("@TradeRefrenceThree", SqlDbType.NVarChar, 100).Value = TradeRefrenceThree;
                cmd.Parameters.Add("@PurchasingMethodNo", SqlDbType.Int).Value = PurchasingMethodNo;
                cmd.Parameters.Add("@PrecogsSupplierNo", SqlDbType.Int).Value = PrecogsSupplierNo;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = UpdatedBy;
                cmd.Parameters.Add("@InDraftMode", SqlDbType.Bit).Value = InDraftMode;
                cmd.Parameters.Add("@CommentText", SqlDbType.NVarChar).Value = CommentText;
                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to update Trade reference data.", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// Quality Team Approvals.
        /// Calls [usp_PoSupplierQualityApproval]
        /// </summary>
        public override List<SupplierPoApprovalDetails> QualityApproveDecline(System.Int32? purchaseOrderLineId, System.Int32? Approved, System.Int32? updatedBy, System.String Notes, System.Boolean? PartERAI)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            int RowAffected = 0;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_PoSupplierQualityApproval", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@SupplierApprovalId", SqlDbType.Int).Value = purchaseOrderLineId;
                cmd.Parameters.Add("@Approved", SqlDbType.Int).Value = Approved;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@Notes", SqlDbType.NVarChar).Value = Notes;
                cmd.Parameters.Add("@PartERAI", SqlDbType.Bit).Value = PartERAI;
                cn.Open();
                SqlDataReader reader = cmd.ExecuteReader();
                List<SupplierPoApprovalDetails> lst = new List<SupplierPoApprovalDetails>();
                while (reader.Read())
                {
                    SupplierPoApprovalDetails obj = new SupplierPoApprovalDetails();
                    obj.PurchaseOrderId = GetReaderValue_Int32(reader, "PurchaseOrderId", 0);
                    obj.PurchaseOrderNumber = GetReaderValue_Int32(reader, "PurchaseOrderNumber", 0);
                    obj.BuyerId = GetReaderValue_Int32(reader, "Buyer", 0);
                    obj.BuyerName = GetReaderValue_String(reader, "BuyerName", "");
                    obj.LineManagerId = GetReaderValue_Int32(reader, "Manager", 0);
                    obj.LineManagerName = GetReaderValue_String(reader, "ManagerName", "");
                    obj.SupplierId = GetReaderValue_Int32(reader, "SupplierId", 0);
                    RowAffected = GetReaderValue_Int32(reader, "RowAffected", 0);
                    obj.Result = RowAffected > 0 ? true : false;
                    lst.Add(obj);
                    obj = null;
                }
                return lst;

            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to update quality approval", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        /// <summary>
        /// Get Email Data.
        /// Calls [usp_GetSupplierApprovalEmailData]
        /// </summary>
        public override List<SupplierPoApprovalDetails> GetEmailData(System.Int32? PurchaseOrderNo)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            //int RowAffected = 0;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_GetSupplierApprovalEmailData", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@PurchaseOrderId", SqlDbType.Int).Value = PurchaseOrderNo;
                cn.Open();
                SqlDataReader reader = cmd.ExecuteReader();
                List<SupplierPoApprovalDetails> lst = new List<SupplierPoApprovalDetails>();
                while (reader.Read())
                {
                    SupplierPoApprovalDetails obj = new SupplierPoApprovalDetails();
                    obj.PartNo = GetReaderValue_String(reader, "PartNo", "");
                    obj.Quantity = GetReaderValue_Int32(reader, "Quantity", 0);
                    obj.PaymentTerms = GetReaderValue_String(reader, "PaymentTerms", "");
                    obj.Incoterms = GetReaderValue_String(reader, "Incoterms", "");
                    obj.ShipVia = GetReaderValue_String(reader, "ShipVia", "");
                    obj.ShipFromCountry = GetReaderValue_String(reader, "ShipFromCountry", "");
                    obj.TotalValueOfPOCurrency = GetReaderValue_String(reader, "TotalValueOfPOCurrency", "");
                    obj.Margin = GetReaderValue_String(reader, "Margin", "");
                    obj.RepeatOrder = GetReaderValue_String(reader, "RepeatOrder", "");
                    obj.ReboundPurchaserDivision = GetReaderValue_String(reader, "ReboundPurchaserDivision", "");
                    obj.GTClinetForPO = GetReaderValue_String(reader, "GTClinetForPO", "");
                    obj.Warehouse = GetReaderValue_String(reader, "Warehouse", "");
                    obj.CustomerDefinedVendor = GetReaderValue_String(reader, "CustomerDefinedVendor", "");
                    obj.SupplierName= GetReaderValue_String(reader, "SupplierName", "");
                    obj.SupplierType= GetReaderValue_String(reader, "SupplierType", "");
                    obj.ApprovedOrdersCount= GetReaderValue_Int32(reader, "ApprovedOrdersCount", 0);
                    obj.SupplierRMAsCount= GetReaderValue_Int32(reader, "SupplierRMAsCount", 0);
                    obj.WarrantyPeriod= GetReaderValue_Int32(reader, "WarrantyPeriod", 0);
                    lst.Add(obj);
                    obj = null;
                }
                return lst;

            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to update quality approval", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override int InsertTradeRefPDF(System.Int32? ID, System.String caption, System.String tempFile, System.Int32 LoginID, System.String UploadType)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_PO_SA_InsertTradeRefPDF", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@SupplierApprovalNo", SqlDbType.Int).Value = ID;
                cmd.Parameters.Add("@Caption", SqlDbType.NVarChar, 1000).Value = caption;
                cmd.Parameters.Add("@FileName", SqlDbType.NVarChar, 1000).Value = tempFile;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = LoginID;
                cmd.Parameters.Add("@UpdatedType", SqlDbType.NVarChar, 100).Value = UploadType;
                cmd.Parameters.Add("@NewId", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (Int32)cmd.Parameters["@NewId"].Value;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to insert supplier approval pdf", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        /// <summary>
        /// GetPDFListForSupplierApproval 
        /// Calls [usp_selectAll_PDF_for_SupplierApproval]
        /// </summary>
        public override List<PDFDocumentDetails> GetPDFListForSupplierApproval(System.Int32? SupplierApprovalId, System.String UploadType)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_selectAll_PDF_for_SupplierApproval", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@SupplierApprovalNo", SqlDbType.Int).Value = SupplierApprovalId;
                cmd.Parameters.Add("@UploadType", SqlDbType.NVarChar, 100).Value = UploadType;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<PDFDocumentDetails> lstPDF = new List<PDFDocumentDetails>();
                while (reader.Read())
                {
                    PDFDocumentDetails obj = new PDFDocumentDetails();
                    obj.PDFDocumentId = GetReaderValue_Int32(reader, "SATradeReferencePDFID", 0);
                    obj.PDFDocumentRefNo = GetReaderValue_Int32(reader, "SupplierApprovalNo", 0);
                    obj.Caption = GetReaderValue_String(reader, "Caption", "");
                    obj.FileName = GetReaderValue_String(reader, "FileName", "");
                    obj.UpdatedBy = GetReaderValue_NullableInt32(reader, "UpdatedBy", null);
                    obj.DLUP = GetReaderValue_NullableDateTime(reader, "DLUP", null);
                    lstPDF.Add(obj);
                    obj = null;
                }
                return lstPDF;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get PDF list for supplier approval", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// Delete purchase order pdf
        /// Calls[usp_delete_PurchaseOrderPDF]
        /// </summary>
        public override bool DeleteSupplierApprovalPDF(System.Int32? SATradeReferencePDFID)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_delete_SupplierApprovalPDF", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@SATradeReferencePDFID", SqlDbType.Int).Value = SATradeReferencePDFID;
                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to delete purchase order pdf", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override int InsertSupplierAprrovalImage(System.Int32? ID, System.String caption, System.String tempFile, System.Int32 LoginID, System.String UploadType)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_PO_SA_InsertImage", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@SupplierApprovalNo", SqlDbType.Int).Value = ID;
                cmd.Parameters.Add("@Caption", SqlDbType.NVarChar, 1000).Value = caption;
                cmd.Parameters.Add("@ImageName", SqlDbType.NVarChar, 1000).Value = tempFile;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = LoginID;
                cmd.Parameters.Add("@UpdatedType", SqlDbType.NVarChar, 100).Value = UploadType;
                cmd.Parameters.Add("@NewId", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (Int32)cmd.Parameters["@NewId"].Value;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to insert supplier approval image", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override List<SupplierPoApprovalDetails> GetImageListForSupplierApproval(System.Int32? SupplierApprovalId, System.String UploadType)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_selectAll_Image_for_SupplierApproval", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@SupplierApprovalNo", SqlDbType.Int).Value = SupplierApprovalId;
                cmd.Parameters.Add("@UploadType", SqlDbType.NVarChar, 100).Value = UploadType;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<SupplierPoApprovalDetails> lstPDF = new List<SupplierPoApprovalDetails>();
                while (reader.Read())
                {
                    SupplierPoApprovalDetails obj = new SupplierPoApprovalDetails();
                    obj.ImageId = GetReaderValue_Int32(reader, "SAImageID", 0);
                    obj.Caption = GetReaderValue_String(reader, "Caption", "");
                    obj.ImageName = GetReaderValue_String(reader, "ImageName", "");
                    obj.UpdatedBy = GetReaderValue_NullableInt32(reader, "UpdatedBy", null);
                    obj.DLUP = GetReaderValue_NullableDateTime(reader, "DLUP", null);
                    lstPDF.Add(obj);
                    obj = null;
                }
                return lstPDF;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get PDF list for supplier approval", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override bool DeleteSupplierApprovalImage(System.Int32? SAImageID)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_delete_SupplierApprovalImage", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@SAImageID", SqlDbType.Int).Value = SAImageID;
                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to delete supplier Approval Image", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        /// <summary>
        /// Insert Term and Condetion Email Log.
        /// Calls [usp_Insert_SupplierApprovalTnCEmailLog]
        /// </summary>
        public override int InsertTermCondetionEmailLog(System.Int32? SupplierApprovalNo, System.Int32? SendFromId, System.String SendToEmail, System.Int32? SupplierNo, System.String Subject, System.Boolean? IsNotTCEmail, System.Int32? SendToId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_Insert_SupplierApprovalTnCEmailLog", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@SupplierApprovalNo", SqlDbType.Int).Value = SupplierApprovalNo;
                cmd.Parameters.Add("@SendFromId", SqlDbType.Int).Value = SendFromId;
                cmd.Parameters.Add("@SupplierNo", SqlDbType.Int).Value = SupplierNo;
                cmd.Parameters.Add("@SendToEmailId", SqlDbType.NVarChar, 2000).Value = SendToEmail;
                cmd.Parameters.Add("@Subject", SqlDbType.NVarChar, 2000).Value = Subject;
                cmd.Parameters.Add("@IsNotTCEmail", SqlDbType.Bit).Value = IsNotTCEmail;
                cmd.Parameters.Add("@SendToId", SqlDbType.Int).Value = SendToId;
                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (Int32)cmd.Parameters["@RowsAffected"].Value;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to insert email log.", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        /// <summary>
        /// Quality Team Escalate.
        /// Calls [usp_PoSupplierQualityEscalate]
        /// </summary>
        public override List<SupplierPoApprovalDetails> QualityEscalate(System.Int32? SupplierApprovalId, System.Int32? updatedBy, System.Int32? ClientId, System.String Notes, System.Boolean? IsPartERAI)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            int RowAffected = 0;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_PoSupplierQualityEscalate", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@SupplierApprovalId", SqlDbType.Int).Value = SupplierApprovalId;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = ClientId;
                cmd.Parameters.Add("@Notes", SqlDbType.NVarChar).Value = Notes;
                cmd.Parameters.Add("@PartERAI", SqlDbType.Bit).Value = IsPartERAI;
                cn.Open();
                SqlDataReader reader = cmd.ExecuteReader();
                List<SupplierPoApprovalDetails> lst = new List<SupplierPoApprovalDetails>();
                while (reader.Read())
                {
                    SupplierPoApprovalDetails obj = new SupplierPoApprovalDetails();
                    obj.PurchaseOrderId = GetReaderValue_Int32(reader, "PurchaseOrderId", 0);
                    obj.PurchaseOrderNumber = GetReaderValue_Int32(reader, "PurchaseOrderNumber", 0);
                    obj.POEscalationGroupId = GetReaderValue_Int32(reader, "POEscalationGroupId", 0);
                    obj.SupplierId = GetReaderValue_Int32(reader, "SupplierId", 0);
                    RowAffected = GetReaderValue_Int32(reader, "RowAffected", 0);
                    obj.Result = RowAffected > 0 ? true : false;
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to update quality escalation.", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
    }
}
