using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;

namespace Rebound.GlobalTrader.Site.Controls.HomeNuggets.Data {
	[WebService(Namespace = "http://tempuri.org/")]
	[WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
	public class MyOpenQuotes : Rebound.GlobalTrader.Site.Data.Base {

		public override void ProcessRequest(HttpContext context) {
			if (base.init(context)) {
				switch (Action) {
					case "GetData": GetData(); break;
					default: WriteErrorActionNotFound(); break;
				}
			}
		}

		/// <summary>
		/// Gets the main data
		/// </summary>
		private void GetData() {
            try
            {
                int intLoginID = LoginID;
                if (GetFormValue_Int("OtherLoginID") > 0) intLoginID = GetFormValue_Int("OtherLoginID");
                List<Quote> lstOpen = Quote.GetListOpenForLogin(intLoginID, 20);
                List<Quote> lstRecent = Quote.GetListRecentForLogin(intLoginID, RowCount);
                if (lstOpen == null && lstRecent == null)
                {
                    WriteErrorDataNotFound();
                }
                else
                {
                    //open
                    JsonObject jsn = new JsonObject();
                    JsonObject jsnItems = new JsonObject(true);
                    for (int i = 0; i < lstOpen.Count; i++)
                    {
                        JsonObject jsnItem = new JsonObject();
                        jsnItem.AddVariable("ID", lstOpen[i].QuoteId);
                        jsnItem.AddVariable("No", lstOpen[i].QuoteNumber);
                        jsnItem.AddVariable("Quote", Functions.FormatDate(lstOpen[i].DateQuoted));
                        jsnItem.AddVariable("CM", lstOpen[i].CompanyName);
                        jsnItem.AddVariable("CMNo", lstOpen[i].CompanyNo);
                        jsnItem.AddVariable("IsImportant", lstOpen[i].IsImportant);
                        jsnItems.AddVariable(jsnItem);
                        jsnItem.Dispose();
                        jsnItem = null;
                    }
                    jsn.AddVariable("OpenQ", jsnItems);
                    jsnItems.Dispose();
                    jsnItems = null;

                    //recent
                    jsnItems = new JsonObject(true);
                    for (int i = 0; i < lstRecent.Count; i++)
                    {
                        JsonObject jsnItem = new JsonObject();
                        jsnItem.AddVariable("ID", lstRecent[i].QuoteId);
                        jsnItem.AddVariable("No", lstRecent[i].QuoteNumber);
                        jsnItem.AddVariable("Quote", Functions.FormatDate(lstRecent[i].DateQuoted));
                        jsnItem.AddVariable("CM", lstRecent[i].CompanyName);
                        jsnItem.AddVariable("CMNo", lstRecent[i].CompanyNo);
                        jsnItem.AddVariable("IsImportant", lstRecent[i].IsImportant);
                        jsnItems.AddVariable(jsnItem);
                        jsnItem.Dispose();
                        jsnItem = null;
                    }
                    jsn.AddVariable("RecentQ", jsnItems);
                    jsn.AddVariable("Count", lstOpen.Count + lstRecent.Count);
                    jsnItems.Dispose();
                    jsnItems = null;

                    OutputResult(jsn);
                    jsn.Dispose();
                    jsn = null;
                }
                lstOpen = null;
                lstRecent = null;
            }
            catch (Exception ex)
            {
                Errorlog obj = new Errorlog();
                obj.LogMessage(Convert.ToString("Exception- " + ex.Message + " " + ex.StackTrace));
            }
        }


	}
}
