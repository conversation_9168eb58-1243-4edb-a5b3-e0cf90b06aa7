<%@ Control Language="C#" CodeBehind="CompanyGlobalSalesPDetails_AddEdit.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Forms.CompanyGlobalSalesPDetails_AddEdit" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Form:DesignBase ID="ctlDB" runat="server" ShowRequiredFieldsExplanation="true" ShowQuickHelp="false">
	<Links>
		<ReboundUI:IconButton ID="ibtnSave" runat="server" IconGroup="Nugget" IconTitleResource="Save" IconCSSType="Save" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
		<ReboundUI:IconButton ID="ibtnCancel" runat="server" IconGroup="Nugget" IconTitleResource="Cancel" IconCSSType="Cancel" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
	</Links>
	<Explanation><%=Functions.GetGlobalResource("FormExplanations", "CompanyManufacturers_AddEdit")%></Explanation>
	<Content>
		<ReboundUI_Table:Form id="frm" runat="server">
			<ReboundUI_Form:FormField id="ctlManufacturer" runat="server" FieldID="txtManufacturer" ResourceTitle="Manufacturer" IsRequiredField="true">
				<Field>
					<ReboundUI:ReboundTextBox ID="txtManufacturer" runat="server" Width="250" />
					<ReboundAutoSearch:Manufacturers ID="autManufacturers" runat="server" RelatedTextBoxID="txtManufacturer" Width="250" ResultsHeight="100" ResultsActionType="RaiseEvent" CharactersToEnterBeforeSearch="2" />
				</Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField ID="ctlManufacturerSelected" FieldID="lblManufacturerSelected" runat="server" ResourceTitle="Manufacturer" >
				<Field>
					<asp:Label ID="lblManufacturerSelected" runat="server" />
				</Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlRating" runat="server" FieldID="ctlStarRating" ResourceTitle="Rating">
				<Field><ReboundUI:StarRating ID="ctlStarRating" runat="server" /></Field>
			</ReboundUI_Form:FormField>
			
		</ReboundUI_Table:Form>
	</Content>
</ReboundUI_Form:DesignBase>
