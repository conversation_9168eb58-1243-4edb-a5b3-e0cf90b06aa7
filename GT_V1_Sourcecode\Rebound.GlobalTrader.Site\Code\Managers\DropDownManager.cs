using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Reflection;

namespace Rebound.GlobalTrader.Site {
	public class DropDownManager {

		public static Controls.DropDowns.Base GetDropDown(string strAssembly, string strDropDown) {
			string strFullAssemblyDropDownType = string.Format("{0}.Controls.DropDowns.{1}", strAssembly, strDropDown);
			try {
				Type typ = Assembly.Load(strAssembly).GetType(strFullAssemblyDropDownType);
				return (Rebound.GlobalTrader.Site.Controls.DropDowns.Base)Assembly.GetAssembly(typ).CreateInstance(strFullAssemblyDropDownType);
			} catch {
				throw new Exception(string.Format(@"Type '{0}' cannot be found", strFullAssemblyDropDownType));
			}
		}

		public static Controls.DropDowns.Base GetDropDown(string strAssembly, int intDropDownID) {
			return GetDropDown(strAssembly, Site.GetInstance().GetDropDown(intDropDownID).Name);
		}

	}
}
