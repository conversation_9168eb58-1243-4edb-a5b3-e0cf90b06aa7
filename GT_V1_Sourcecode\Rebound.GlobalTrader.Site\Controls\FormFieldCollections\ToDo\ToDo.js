Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.FormFieldCollections");Rebound.GlobalTrader.Site.Controls.FormFieldCollections.ToDo=function(n){Rebound.GlobalTrader.Site.Controls.FormFieldCollections.ToDo.initializeBase(this,[n]);this._intToDoID=null;this._intMessageID=null;this._intCategoryID=null;this._blnCreateReminder=!1;this._blnFirstTimeReminderShown=!0;this._quoteMinReminderDate=null;this._quoteStatus=null};Rebound.GlobalTrader.Site.Controls.FormFieldCollections.ToDo.prototype={initialize:function(){Rebound.GlobalTrader.Site.Controls.FormFieldCollections.ToDo.callBaseMethod(this,"initialize")},dispose:function(){this.isDisposed||(this._intToDoID=null,this._intMessageID=null,this._blnCreateReminder=null,this._blnFirstTimeReminderShown=null,this._intCategoryID=null,this._quoteStatus=null,Rebound.GlobalTrader.Site.Controls.FormFieldCollections.ToDo.callBaseMethod(this,"dispose"))},setupReminderClick:function(){this._ctlRelatedForm.getFieldControl("ctlReminder").addClick(Function.createDelegate(this,this.selectReminder))},selectReminder:function(){this._blnCreateReminder=this._ctlRelatedForm.getFieldValue("ctlReminder");this._ctlRelatedForm.showField("ctlReminderDate",this._blnCreateReminder);this._ctlRelatedForm.showField("ctlReminderTime",this._blnCreateReminder);this._ctlRelatedForm.showField("ctlReminderText",this._blnCreateReminder);this._ctlRelatedForm.showField("ctlDailyReminder",this._blnCreateReminder&&this._intCategoryID==2&&(this._quoteStatus=="Offered"||this._quoteStatus=="Partially Offered"));this._blnCreateReminder&&this._blnFirstTimeReminderShown&&this._intCategoryID!=2&&(this._ctlRelatedForm.setFieldValue("ctlReminderDate",this._ctlRelatedForm.getFieldValue("ctlDueDate")),this._ctlRelatedForm.setFieldValue("ctlReminderTime",this._ctlRelatedForm.getFieldValue("ctlDueTime")),this._ctlRelatedForm.setFieldValue("ctlReminderText",this._ctlRelatedForm.getFieldValue("ctlText")),this._blnFirstTimeReminderShown=!1)},validateFields:function(){var n=!0,t,i=new Date;return this._ctlRelatedForm.checkFieldEntered("ctlSubject")||(n=!1),this._ctlRelatedForm.checkFieldEntered("ctlText")||(n=!1),this._ctlRelatedForm.getFieldValue("ctlDueDate").length>0&&(t=$R_FN.getDateFromDateAndTimeFields("ctlDueDate","ctlDueTime",this._ctlRelatedForm),t<i&&(this._ctlRelatedForm.setFieldInError("ctlDueDate",!0,$R_RES.DateTimeMustBeInFuture),this._ctlRelatedForm.setFieldInError("ctlDueTime",!0,$R_RES.DateTimeMustBeInFuture),n=!1)),this._blnCreateReminder&&(this._ctlRelatedForm.checkFieldEntered("ctlReminderDate")||(n=!1),this._ctlRelatedForm.checkFieldEntered("ctlReminderText")||(n=!1),this._ctlRelatedForm.getFieldValue("ctlReminderDate").length>0&&(t=$R_FN.getDateFromDateAndTimeFields("ctlReminderDate","ctlReminderTime",this._ctlRelatedForm),t<i&&(this._ctlRelatedForm.setFieldInError("ctlReminderDate",!0,$R_RES.DateTimeMustBeInFuture),this._ctlRelatedForm.setFieldInError("ctlReminderTime",!0,$R_RES.DateTimeMustBeInFuture),n=!1))),n},addFieldsToDataObject:function(n){n.addParameter("ID",this._intToDoID);n.addParameter("Subject",this._ctlRelatedForm.getFieldValue("ctlSubject"));n.addParameter("DueDate",$R_FN.getDateFromDateAndTimeFields("ctlDueDate","ctlDueTime",this._ctlRelatedForm,!0));n.addParameter("Text",this._ctlRelatedForm.getFieldValue("ctlText"));n.addParameter("MessageID",this._intMessageID);n.addParameter("HasReminder",this._blnCreateReminder);n.addParameter("HasReview",this._ctlRelatedForm.getFieldValue("ctlReview"));n.addParameter("ToDoListTypeId",this._ctlRelatedForm.getFieldValue("ctlToDoListType"));this._blnCreateReminder&&(n.addParameter("ReminderDate",$R_FN.getDateFromDateAndTimeFields("ctlReminderDate","ctlReminderTime",this._ctlRelatedForm,!0)),n.addParameter("DailyReminder",this._ctlRelatedForm.getFieldValue("ctlDailyReminder")),n.addParameter("ReminderText",this._ctlRelatedForm.getFieldValue("ctlReminderText")))}};Rebound.GlobalTrader.Site.Controls.FormFieldCollections.ToDo.registerClass("Rebound.GlobalTrader.Site.Controls.FormFieldCollections.ToDo",Rebound.GlobalTrader.Site.Controls.Forms.LabelFormField,Sys.IDisposable);