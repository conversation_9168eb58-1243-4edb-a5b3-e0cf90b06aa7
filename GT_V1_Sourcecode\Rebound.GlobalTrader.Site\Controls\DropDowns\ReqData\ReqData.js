Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");Rebound.GlobalTrader.Site.Controls.DropDowns.ReqData=function(n){Rebound.GlobalTrader.Site.Controls.DropDowns.ReqData.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.DropDowns.ReqData.prototype={get_SType:function(){return this._SType},set_SType:function(n){this._SType!==n&&(this._SType=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.DropDowns.ReqData.callBaseMethod(this,"initialize");this.addSetupDataCall(Function.createDelegate(this,this.setupDataCall));this.addGetDataOK(Function.createDelegate(this,this.dataCallOK))},dispose:function(){this.isDisposed||Rebound.GlobalTrader.Site.Controls.DropDowns.ReqData.callBaseMethod(this,"dispose")},setupDataCall:function(){this._objData.set_PathToData("controls/DropDowns/ReqData");this._objData.set_DataObject("ReqData");this._objData.set_DataAction("GetData");this._objData.addParameter("SType",this._SType)},dataCallOK:function(){var t=this._objData._result,n;if(t.Types)for(n=0;n<t.Types.length;n++)this.addOption(t.Types[n].Name,t.Types[n].ID)}};Rebound.GlobalTrader.Site.Controls.DropDowns.ReqData.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.ReqData",Rebound.GlobalTrader.Site.Controls.DropDowns.Base);