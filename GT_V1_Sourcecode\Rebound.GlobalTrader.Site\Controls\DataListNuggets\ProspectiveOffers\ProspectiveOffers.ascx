﻿<%@ Control Language="C#" CodeBehind="ProspectiveOffers.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.DataListNuggets.ProspectiveOffers" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Nugget:DesignBase ID="ctlDB" runat="server" BoxType="List">
    <Filters>
        <ReboundUI_DataListNugget:Filter ID="ctlFilter" runat="server">
            <FieldsLeft>
                <ReboundUI_FilterDataItemRow:TextBox ID="ctlSourceFileName" runat="server" ResourceTitle="SourceFileName" FilterField="SourceFileName" />
                <ReboundUI_FilterDataItemRow:TextBox ID="ctlPart" runat="server" ResourceTitle="PartNo" FilterField="Part" />
                <ReboundUI_FilterDataItemRow:DateSelect ID="ctlDateUploadFrom" runat="server" ResourceTitle="UploadFromDate" FilterField="DateUploadFrom" />
                <ReboundUI_FilterDataItemRow:DateSelect ID="ctlDateUploadTo" runat="server" ResourceTitle="UploadToDate" FilterField="DateUploadTo" />
            </FieldsLeft>
            <FieldsRight>
                <ReboundUI_FilterDataItemRow:DropDown ID="ctlImportedBy" runat="server" ResourceTitle="ImportedBy" DropDownType="Employee" DropDownAssembly="Rebound.GlobalTrader.Site" FilterField="ImportedBy" />
                <ReboundUI_FilterDataItemRow:TextBox ID="ctlSupplierName" runat="server" ResourceTitle="Supplier" FilterField="SupplierName" />
            </FieldsRight>
        </ReboundUI_DataListNugget:Filter>
    </Filters>
</ReboundUI_Nugget:DesignBase>

