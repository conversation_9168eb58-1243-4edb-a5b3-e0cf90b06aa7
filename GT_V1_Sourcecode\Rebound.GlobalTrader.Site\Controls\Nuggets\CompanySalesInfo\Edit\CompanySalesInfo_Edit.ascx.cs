//-----------------------------------------------------------------------------------------
// RP 17.05.2010:
// - [168]: Add new default setting for rating
//Marker     Changed by      Date         Remarks
// [001]      Suhail          02/05/2018   Added Credit Limit2
//-----------------------------------------------------------------------------------------
using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site;
using Rebound.GlobalTrader.Site.Controls;

namespace Rebound.GlobalTrader.Site.Controls.Forms {
	public partial class CompanySalesInfo_Edit : Base {

		#region Locals

		#endregion

		#region Properties

		/// <summary>
		/// CompanyID
		/// </summary>
		private int _intCompanyID = -1;
		public int CompanyID {
			get { return _intCompanyID; }
			set { _intCompanyID = value; }
		}

		#endregion

		#region Overrides

		protected override void OnInit(EventArgs e) {
			base.OnInit(e);
			TitleText = Functions.GetGlobalResource("FormTitles", "CompanySalesInfo_Edit");
			AddScriptReference("Controls.Nuggets.CompanySalesInfo.Edit.CompanySalesInfo_Edit.js");
			if (_intCompanyID == -1) _intCompanyID = _objQSManager.CompanyID;
		}

		protected override void OnPreRender(EventArgs e) {
			SetupScriptDescriptors();
			base.OnPreRender(e);
		}

		#endregion

		private void SetupScriptDescriptors() {
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.Forms.CompanySalesInfo_Edit", ctlDesignBase.ClientID);
			_scScriptControlDescriptor.AddProperty("intCompanyID", _intCompanyID);
			_scScriptControlDescriptor.AddElementProperty("lblCurrency_CreditLimit", FindFieldControl("ctlCreditLimit", "lblCurrency_CreditLimit").ClientID);
			_scScriptControlDescriptor.AddProperty("intDefaultRating", SettingsManager.GetSetting_Int(BLL.SettingItem.List.DefaultSORating));
            //_scScriptControlDescriptor.AddProperty("intMailGroupNo", BLL.MailGroup.GetQualityMailGroupNo(SessionManager.ClientID));
            _scScriptControlDescriptor.AddProperty("intMailGroupNo", BLL.MailGroup.GetNewCustomerApprovalNotificationMailGroupNo(SessionManager.ClientID));
            //[001] code start
            _scScriptControlDescriptor.AddElementProperty("lblCurrency_ActualCreditLimit", FindFieldControl("ctlActualCreditLimit", "lblCurrency_ActualCreditLimit").ClientID);
            //[001] code end
		}

	}
}