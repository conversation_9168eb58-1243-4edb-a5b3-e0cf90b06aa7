﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO
/* 
===========================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-223239]		An.TranTan			12-Dev-2024		Update			Insert bulk edit history
===========================================================================================
*/
CREATE OR ALTER PROCEDURE [dbo].[usp_ipobom_update_ReverseLogisticBulk]
--****************************************************************************************                            
--*  23.01.2024:                 
--*  Marker     Changed by           Date         Remarks               
--*  [001]      Devendra Sikarwar    23-01-2024  [RP-2603]                 
--*  EXEC  [usp_ipobom_update_ReverseLogisticBulk]  '55745,55743,55741',0,4763              
--****************************************************************************************                            
	@ReverseLogisticId NVARCHAR(300)
	,@isBulk BIT
	,@updatedBy INT
	,@RowsAffected INT = NULL OUTPUT
AS
BEGIN
BEGIN TRANSACTION
	DECLARE @tempRL TABLE(ID INT);
	INSERT INTO @tempRL(ID)
	SELECT CAST(VALUE AS INT)
	FROM STRING_SPLIT(@ReverseLogisticId, ',')
	WHERE TRY_CAST(VALUE AS INT) IS NOT NULL;

	--save data to temp before update
	SELECT 
		rl.ReverseLogisticId
		,rl.Quantity
		,rl.Inactive
		,rl.Part
		,rl.FullPart
	INTO #tempOriginalRL
	FROM [BorisGlobalTraderImports].dbo.tbReverseLogistic rl WITH(NOLOCK)
	JOIN @tempRL t on t.ID = rl.ReverseLogisticId; 

	UPDATE [BorisGlobalTraderImports].dbo.tbReverseLogistic
	SET Quantity = IIF(@isBulk = 0, 0, Quantity)
		,Inactive = IIF(@isBulk = 1, 1, Inactive)
		,UpdatedBy = @updatedBy
		,UpdatedOn = GETDATE()
	WHERE ReverseLogisticId IN (SELECT ID FROM @tempRL)

	SELECT @RowsAffected = @@ROWCOUNT;

	/*********************insert history*********************/
	DECLARE @BatchID INT;
	SELECT @BatchID = ISNULL(MAX(BatchNo),0) + 1 
	FROM BorisGlobalTraderImports.dbo.tbBulkRLAuditLog;

	INSERT INTO BorisGlobalTraderImports.dbo.tbBulkRLAuditLog
	(
		BatchNo
		,ReverseLogisticNo
		,Part
		,FullPart
		,[Action]
		,OldValue
		,CreatedBy
		,CreatedByName
		,DLUP
	)
	SELECT 
		@BatchID
		,t.ReverseLogisticId
		,t.Part
		,t.FullPart
		,IIF(@isBulk = 1, 'Remove', 'Set Qty to Zero')	--action
		,IIF(@isBulk = 1, 'Active', CAST(t.Quantity AS NVARCHAR(20))) --old value
		,@updatedBy
		,CAST((l.FirstName + ' ' + l.LastName) AS NVARCHAR(256))
		,GETDATE()
	FROM #tempOriginalRL t
	JOIN tbLogin l WITH(NOLOCK) ON l.LoginId = @updatedBy

	IF @@ERROR = 0
		COMMIT TRANSACTION
	ELSE BEGIN
		SET @RowsAffected = -1;
		ROLLBACK TRANSACTION
	END
	DROP TABLE #tempOriginalRL;
END
GO


