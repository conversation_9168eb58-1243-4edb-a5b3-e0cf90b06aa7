﻿
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE OR ALTER PROCEDURE [dbo].[Usp_GetSupplierAPIData]                        
		--**************************************************************************************                                      
		--* - add search on SupplierPart                                     
		--*Marker     Changed by      Date         Remarks                                      
		--*[001]      Manoj           15/05/2023   FutureElectronics                                    
		--**************************************************************************************                                      
		  @ClientId INT                                      
		, @PartSearch NVARCHAR(50)     ,    
		@ApiURLKeyId int    
		WITH RECOMPILE         
		AS                 
		Begin                
		--Declare @SupType varchar(300);                
		--set @SupType=(select top 1 isnull(cotype.Name,'')                 
		--FROM [BorisGlobalTraderArchive].dbo.tbOffer_arc o                                                
		--    LEFT JOIN dbo.tbCompany s ON o.SupplierNo = s.CompanyId                                                
		--    JOIN tbClient cl ON o.ClientNo = cl.ClientId                                               
		--    LEFT JOIN dbo.tbCompanyType cotype ON s.TypeNo=cotype.CompanyTypeId                                                
		--    WHERE ((o.ClientNo = @ClientId) OR (o.ClientNo <> @ClientId                                            
		--    AND (case when o.ClientNo=114 then cast(1 as bit) else cl.OwnDataVisibleToOthers end) = 1))                     
		--     AND o.Part LIKE @PartSearch)   
  
		Declare @SupType varchar(300)                  
		select  @SupType=coty.Name from  tbCompany co join tbCompanyType coty on co.TypeNo = coty.CompanyTypeId   
		  where co.clientno = 114 and  co.FullName ='FutureElectronics' and co.IsSupplier = 1 and co.POApproved = 1   
                
		 SELECT tf.SupplierAPIID                                       
				  , tf.QuantityAvailable QuantityInSupplier                                      
				  , tf.QuantityAvailable QuantityOnOrder                
				  --, 1 ManufacturerNo  --st.ManufacturerNo              
			 ,(select top 1 ManufacturerId from dbo.tbManufacturer where partid.[manufacturerName] = ManufacturerName) ManufacturerNo            
				  --, tf.pr ProductName --pr.ProductName                
				  --, case when ipo.InternalPurchaseOrderId is null then po.CompanyNo else ipo.CompanyNo end  AS SupplierNo                                      
				  --, case when ipo.InternalPurchaseOrderId is null then ISNULL(co.CompanyName, '') else ISNULL(coc.CompanyName, '') end AS SupplierName                       
				  , 0 SupplierNo                
				  , null ResalePrice --st.ResalePrice                                      
				  , null ROHS --st.ROHS  iif(partid.rohs='Y',1,0)                                    
				  , 'WarehouseName' WarehouseName --wh.WarehouseName                                      
				  , 'Location' Location --st.Location                                      
				  , partid.packageType PackageName --pk.PackageName                
				  , 'SupplierPart' SupplierPart --st.SupplierPart                
				  , tf.clientno ClientNo --st.ClientNo                                      
				  , cl.ClientName ClientName  --cl.ClientName                                      
				  , 'ClientDataVisibleToOthers' ClientDataVisibleToOthers --cl.OwnDataVisibleToOthers AS ClientDataVisibleToOthers                                                
				  , 'N' SupplierLTB                   
			, 'TR' SupplierMOQ                               
				  , tf.QuantityAvailable QuantityAvailable                
				  , tf.currencyCode ClientBaseCurrencyCode --cu.CurrencyCode AS ClientBaseCurrencyCode                         
				  , cl.ClientCode ClientCode --cl.ClientCode                    
				  , '' ClientDataVisibleToOthers --ClientDataVisibleToOthers     
				  , 'TR' SPQ                    
				  , 'N' LTB                
				  , tf.QuantityAvailable UpliftPrice                
			, tf.DLUP DLUP                
			, tf.QuantityAvailable Quantity                
			, tf.PartNumber Part               
			 , tf.DLUP OriginalEntryDate                
			 , tf.DLUP GTDate                
		   , partid.[description (en)] [Description]                
			, partid.manufacturerName ManufacturerCode   --mf.ManufacturerCode                        
			 , partid.dateCode DateCode   --st.DateCode              
			 , tf.DatasourceName SupplierName                
			 --, isnull(cotype.Name,'') SupplierType --isnull(cotype.Name,'') as SupplierType                 
			-- , (select top 1 isnull(cotype.Name,'') from [BorisGlobalTraderArchive].dbo.tbOffer_arc o with(nolock) --on o.fullpart=tf.Part_number                
			--LEFT JOIN dbo.tbCompany s with(nolock) ON o.SupplierNo = s.CompanyId                
			--LEFT JOIN dbo.tbCompanyType cotype with(nolock) ON s.TypeNo=cotype.CompanyTypeId                 
			--where o.fullpart=tf.Part_number order by cotype.CompanyTypeId desc) SupplierType                
		   , @SupType SupplierType                
			 , tf.WebUrl Reference                
			 --, tf.offers_categories_subcategory_name ProductName    
		  ,'' ProductName  
			  , partid.packageType PackageType --pk.PackageName                
		   , partid.ECCN ECCN                
			--, tf.offers_documents_publish_date PublishDate   
		 , Null PublishDate  
			 , tf.QuantityMinimum MOQ                
			, tf.QuantityMinimum VirtualCostPrice                
			 , (select top 1 CONCAT('$', UnitPrice) from tbSupplierAPIPricing where SupplierAPIID = tf.SupplierAPIID) as UnitCostPrice           
		  , (select top 1 ApiName from tbApiURLKey apik where apik.ApiURLKeyId = tf.ApiURLKeyNo) as ApiSourceName  
				  FROM dbo.tbSupplierAPI tf                     
		   Left Join(SELECT * FROM (SELECT [name],[value],[SupplierAPINo]                    
				  FROM tbSupplierAPIPartAttributes) PartAttributes                    
				  PIVOT (min([value])                 
			 FOR [name] IN ([packageType], [manufacturerName], [dateCode], [rohs], [leadFree], [description (en)], ECCN)                    
		   ) AS PivotTable) partid on tf.SupplierAPIID=partid.SupplierAPINo             
				  JOIN dbo.tbClient cl ON cl.ClientId = tf.ClientNo                                      
				  WHERE ((tf.ClientNo = @ClientId) and tf.ClientNo <> 109 AND (tf.PartNumber LIKE @PartSearch))    
			and tf.ApiURLKeyNo = @ApiURLKeyId  
			ORDER BY tf.DLUP Desc  
				  --ORDER BY part_number ASC                                      
				  --, QuantityInFutureElectronics DESC                      
		 End   
  
  
GO
