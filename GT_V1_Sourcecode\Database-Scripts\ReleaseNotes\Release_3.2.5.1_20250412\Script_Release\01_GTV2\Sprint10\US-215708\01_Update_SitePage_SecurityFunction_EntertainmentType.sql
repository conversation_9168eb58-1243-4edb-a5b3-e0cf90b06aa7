﻿/*  
===========================================================================================  
TASK              UPDATED BY                  DATE            ACTION      DESCRIPTION  
[US-215708]       Thinh.NguyenHung            28-Feb-2025     Update      Setting permission for add/edit button on Entertainment Type
===========================================================================================  
*/

DECLARE @SitePageNo INT = '3000936'

--Add button
UPDATE tbSecurityFunction
SET SitePageNo = @SitePageNo
WHERE tbSecurityFunction.SecurityFunctionId  = '3000937'

--Edit button 
UPDATE tbSecurityFunction
SET SitePageNo = @SitePageNo
WHERE tbSecurityFunction.SecurityFunctionId  = '3000938'

DECLARE @SiteSectionNo INT = 6
DECLARE @ShortName NVARCHAR(100) = N'Setup_GlobalSettings_EntertainmentType'
DECLARE @PageUrl NVARCHAR(100) = N'Set_GS_EntertainmentType.aspx'

IF NOT EXISTS (
      SELECT 1
      FROM tbSitePage
      WHERE [SitePageId] = @SitePageNo
            AND [ShortName] = @ShortName
            AND [URL] =  @PageUrl
            AND [SiteSectionNo] = @SiteSectionNo
      )
BEGIN
	INSERT INTO tbSitePage ([SitePageId], [ShortName], [Description], [URL] , [SiteSectionNo], [UpdatedBy]) VALUES
	(@SitePageNo, @ShortName, NULL, @PageUrl, @SiteSectionNo, NULL);
END
ELSE
BEGIN
	PRINT 'Site Page ID: ' + CAST(@SitePageNo AS VARCHAR(10)) + ', Site Section ID: ' + CAST(@SiteSectionNo AS VARCHAR(10)) + ', ShortName: ' + @ShortName + ' is existing'
END
