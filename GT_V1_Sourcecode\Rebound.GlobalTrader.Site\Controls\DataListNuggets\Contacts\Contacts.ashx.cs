/* Marker     changed by      date         Remarks  
  [001]      <PERSON><PERSON><PERSON>      13-Sep2018   [REB-12820]:Provision to add Global Security on Contact Section
  [002]      Soorya          03/03/2023    RP-1048 Remove AI code
 
 */
using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using System.Text;
using System.Web.UI;
using System.Collections.Generic;
using Rebound.GlobalTrader.Site.Controls.DropDowns.Data;
using Rebound.GlobalTrader.BLL;
using Rebound.GlobalTrader.Site.Enumerations;
//using Microsoft.ApplicationInsights; // [002] 

namespace Rebound.GlobalTrader.Site.Controls.DataListNuggets.Data
{

    public class Contacts : Base
    {

        protected override void GetData()
        {
            try
            {


                ViewLevelList enmViewLevel = (ViewLevelList)GetFormValue_Int("ViewLevel");
                int ClientId = GetFormValue_Boolean("IsGlobalLogin") == true ? GetFormValue_NullableInt("Client") ?? 0 : SessionManager.ClientID ?? 0;
                //get data	
                List<BLL.Contact> lst = BLL.Contact.DataListNugget(
                    SessionManager.ClientID
                    , (enmViewLevel == ViewLevelList.Team) ? (int?)SessionManager.LoginTeamID : null
                    , (enmViewLevel == ViewLevelList.Division) ? (int?)SessionManager.LoginDivisionID : null
                    , (enmViewLevel == ViewLevelList.My) ? (int?)LoginID : null
                    , GetFormValue_NullableInt("SortIndex", 1)
                    , GetFormValue_NullableInt("SortDir", SortColumnDirection.ASC)
                    , GetFormValue_NullableInt("PageIndex", 0)
                    , GetFormValue_NullableInt("PageSize", 10)
                    //, GetFormValue_StringForNameSearch("FirstName")
                    , GetFormValue_StringForNameSearchDecode("FirstName")
                    //, GetFormValue_StringForNameSearch("LastName")
                    , GetFormValue_StringForNameSearchDecode("LastName")

                    //, GetFormValue_StringForNameSearch("Company")
                    , GetFormValue_StringForNameSearchDecode("Company")
                    , GetFormValue_NullableInt("Salesman")
                    , GetFormValue_StringForSearch("TelNo")
                    //[001] start
                    , GetFormValue_Boolean("IsGlobalLogin")
                    , ClientId
                    //[001] end
                    , GetFormValue_String("Email")
                    );
                JsonObject jsn = new JsonObject();
                jsn.AddVariable("TotalRecords", (lst.Count > 0) ? lst[0].RowCnt : 0);
                JsonObject jsnRowsArray = new JsonObject(true);
                foreach (BLL.Contact it in lst)
                {
                    JsonObject jsnRow = new JsonObject();
                    jsnRow.AddVariable("ID", it.ContactId);
                    jsnRow.AddVariable("Name", it.ContactName);
                    jsnRow.AddVariable("Title", it.Title);
                    jsnRow.AddVariable("Co", it.CompanyName);
                    jsnRow.AddVariable("CoID", it.CompanyNo);
                    jsnRow.AddVariable("Tel", it.Telephone);
                    jsnRow.AddVariable("SMan", it.SalesmanName);
                    //[001] start
                    jsnRow.AddVariable("ClientName", it.ClientName);
                    //[001] end
                    jsnRow.AddVariable("Email", it.EMail);
                    jsnRowsArray.AddVariable(jsnRow);
                    jsnRow.Dispose(); jsnRow = null;
                }
                jsn.AddVariable("Results", jsnRowsArray);
                OutputResult(jsn);
                jsnRowsArray.Dispose(); jsnRowsArray = null;
                jsn.Dispose(); jsn = null;
                lst = null;
                base.GetData();
            }
            catch (Exception e)
            {
                //[002]
                //var ai = new TelemetryClient();
                //ai.TrackTrace("Contacts: GetData");
                //ai.TrackException(e);
                new Errorlog().LogMessage("Error at GetData in Contacts.ashx.cs : " + e.Message);
                WriteError(e);
            }
        }

        protected override void AddFilterStates()
        {
            //Prevent filter state for Tab
            //AddExplicitFilterState("ViewLevel", GetFormValue_Int("ViewLevel"));
            AddFilterState("FirstName");
            AddFilterState("LastName");
            AddFilterState("Company");
            AddFilterState("Salesman");
            AddFilterState("TelNo");
            AddFilterStateWithHttpEncode("Email");
            base.AddFilterStates();
        }
    }
}
