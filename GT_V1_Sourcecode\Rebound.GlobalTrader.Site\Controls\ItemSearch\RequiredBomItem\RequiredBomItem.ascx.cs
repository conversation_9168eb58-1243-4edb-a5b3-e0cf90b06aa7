using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site;
using Rebound.GlobalTrader.Site.Controls;

namespace Rebound.GlobalTrader.Site.Controls.ItemSearch {
    public partial class RequiredBomItem : Base
    {

		protected override void OnInit(EventArgs e) {
			base.OnInit(e);
            SetItemSearchType("ReqsWithBOM");
            AddScriptReference("Controls.ItemSearch.RequiredBomItem.RequiredBomItem.js");
		}

		protected override void OnLoad(EventArgs e) {
            _scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.ItemSearch.RequiredBomItem", ctlDesignBase.ClientID);
			base.OnLoad(e);
		}

		protected override void OnPreRender(EventArgs e) {
			ctlDesignBase.MakeChildControls();
            ctlDesignBase.tblResults.AllowMultipleSelection = true;
			ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("Requirement", WidthManager.GetWidth(WidthManager.ColumnWidth.SystemDocumentNumber), true));
			ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("Company", Unit.Empty, true));
          //  ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("BOM", WidthManager.GetWidth(WidthManager.ColumnWidth.CompanyName), true));
			ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("PartNo", WidthManager.GetWidth(WidthManager.ColumnWidth.PartNo), true));
			ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("Date", WidthManager.GetWidth(WidthManager.ColumnWidth.Date), true));
			ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("Quantity", WidthManager.GetWidth(WidthManager.ColumnWidth.Quantity), true));
			ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("Price", WidthManager.GetWidth(WidthManager.ColumnWidth.CurrencyValue), true));
            ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("ReqBOMName", WidthManager.GetWidth(WidthManager.ColumnWidth.CompanyName), true));
			base.OnPreRender(e);
		}

	}
}