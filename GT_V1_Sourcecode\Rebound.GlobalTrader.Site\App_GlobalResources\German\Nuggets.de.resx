<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Addresses" xml:space="preserve">
    <value>Adressen</value>
  </data>
  <data name="CommunicationLogType" xml:space="preserve">
    <value>Kommunikations-Maschinenbordbuch-Art</value>
  </data>
  <data name="CompanyAdd" xml:space="preserve">
    <value>Addieren Sie New Company</value>
  </data>
  <data name="CompanyContactLog" xml:space="preserve">
    <value>Kontakt-Maschinenbordbuch</value>
  </data>
  <data name="CompanyMainInfo" xml:space="preserve">
    <value>Informationen Maincompany</value>
  </data>
  <data name="CompanyManufacturers" xml:space="preserve">
    <value>Hersteller lieferten</value>
  </data>
  <data name="CompanyPurchasingInfo" xml:space="preserve">
    <value>Kauf von Informationen</value>
  </data>
  <data name="CompanySalesInfo" xml:space="preserve">
    <value>Verkaufs-Informationen</value>
  </data>
  <data name="CompanyType" xml:space="preserve">
    <value>Firma-Art</value>
  </data>
  <data name="ContactContactLog" xml:space="preserve">
    <value>Kontakt-Maschinenbordbuch</value>
  </data>
  <data name="ContactExtendedInfo" xml:space="preserve">
    <value>Ausgedehnte Kontakt-Informationen</value>
  </data>
  <data name="ContactMainInfo" xml:space="preserve">
    <value>Hauptkontakt-Informationen</value>
  </data>
  <data name="ContactsForCompany" xml:space="preserve">
    <value>Kontakte für {0}</value>
  </data>
  <data name="CountingMethod" xml:space="preserve">
    <value>Zählung-Methode</value>
  </data>
  <data name="Country" xml:space="preserve">
    <value>Land</value>
  </data>
  <data name="CreditAdd" xml:space="preserve">
    <value>Addieren Sie neue Kreditnote</value>
  </data>
  <data name="CreditLines" xml:space="preserve">
    <value>Linien</value>
  </data>
  <data name="CreditMainInfo" xml:space="preserve">
    <value>Hauptinformationen</value>
  </data>
  <data name="Credits" xml:space="preserve">
    <value>Gutschrift</value>
  </data>
  <data name="CRMAAdd" xml:space="preserve">
    <value>Addieren Sie neuen Kunden RMA</value>
  </data>
  <data name="CRMALines" xml:space="preserve">
    <value>Linien</value>
  </data>
  <data name="CRMAMainInfo" xml:space="preserve">
    <value>Hauptinformationen</value>
  </data>
  <data name="CRMAReceivingInfo" xml:space="preserve">
    <value>Erhalten von Informationen</value>
  </data>
  <data name="CRMAReceivingLines" xml:space="preserve">
    <value>Linien für das Empfangen</value>
  </data>
  <data name="CRMAS" xml:space="preserve">
    <value>Kunde RMAs</value>
  </data>
  <data name="CRMAsReceive" xml:space="preserve">
    <value>Empfangen Sie Kunden RMAs</value>
  </data>
  <data name="Currency" xml:space="preserve">
    <value>Währung</value>
  </data>
  <data name="CurrencyRateHistory" xml:space="preserve">
    <value>Verbrauchssteuer-Geschichte</value>
  </data>
  <data name="CustomerRequirementAdd" xml:space="preserve">
    <value>Addieren Sie neue Anforderung</value>
  </data>
  <data name="CustomerRequirementMainInfo" xml:space="preserve">
    <value>Teile erfordert</value>
  </data>
  <data name="CustomerRequirements" xml:space="preserve">
    <value>Kunden-Anforderungen</value>
  </data>
  <data name="CustomerRequirementSourcingResults" xml:space="preserve">
    <value>Auftreten-Resultate</value>
  </data>
  <data name="DebitAdd" xml:space="preserve">
    <value>Addieren Sie neue Belastungsanzeige</value>
  </data>
  <data name="DebitLines" xml:space="preserve">
    <value>Linien</value>
  </data>
  <data name="DebitMainInfo" xml:space="preserve">
    <value>Hauptinformationen</value>
  </data>
  <data name="Debits" xml:space="preserve">
    <value>Schuldposten</value>
  </data>
  <data name="DivisionMembers" xml:space="preserve">
    <value>Abteilungs-Mitglieder</value>
  </data>
  <data name="Divisions" xml:space="preserve">
    <value>Abteilung</value>
  </data>
  <data name="DocFooters" xml:space="preserve">
    <value>menten-Seitenende-Mitteilungen</value>
  </data>
  <data name="DocHeaderImage" xml:space="preserve">
    <value>Dokumenten-Überschrift-Bild</value>
  </data>
  <data name="Feedback" xml:space="preserve">
    <value>Rückgespräch</value>
  </data>
  <data name="GIAdd" xml:space="preserve">
    <value>Fügen Sie neue Waren in der Anmerkung hinzu</value>
  </data>
  <data name="GILines" xml:space="preserve">
    <value>Linien</value>
  </data>
  <data name="GIMainInfo" xml:space="preserve">
    <value>Hauptinformationen</value>
  </data>
  <data name="GlobalCountryList" xml:space="preserve">
    <value>Vorlagenland-Liste</value>
  </data>
  <data name="GlobalCurrencyList" xml:space="preserve">
    <value>Vorlagenwährung-Liste</value>
  </data>
  <data name="GoodsIn" xml:space="preserve">
    <value>Waren innen</value>
  </data>
  <data name="IndustryType" xml:space="preserve">
    <value>Industrie-Art</value>
  </data>
  <data name="InvoiceAdd" xml:space="preserve">
    <value>Addieren Sie neue Rechnung</value>
  </data>
  <data name="InvoiceLines" xml:space="preserve">
    <value>Linien</value>
  </data>
  <data name="InvoiceLinesDeleted" xml:space="preserve">
    <value>Gelöschte Linien</value>
  </data>
  <data name="InvoiceMainInfo" xml:space="preserve">
    <value>Hauptinformationen</value>
  </data>
  <data name="Invoices" xml:space="preserve">
    <value>Rechnungen</value>
  </data>
  <data name="LotAdd" xml:space="preserve">
    <value>Addieren Sie neues Los</value>
  </data>
  <data name="LotItems" xml:space="preserve">
    <value>Los-Einzelteile</value>
  </data>
  <data name="LotMainInfo" xml:space="preserve">
    <value>Hauptinformationen</value>
  </data>
  <data name="Lots" xml:space="preserve">
    <value>Lose</value>
  </data>
  <data name="MailMessageGroupMembers" xml:space="preserve">
    <value>Post-Gruppen-Mitglieder</value>
  </data>
  <data name="MailMessageGroups" xml:space="preserve">
    <value>Post-Gruppen</value>
  </data>
  <data name="MailMessages" xml:space="preserve">
    <value>Post-Mitteilungen</value>
  </data>
  <data name="ManufacturerAdd" xml:space="preserve">
    <value>Addieren Sie neuen Hersteller</value>
  </data>
  <data name="ManufacturerCompanies" xml:space="preserve">
    <value>In Verbindung stehende Firmen</value>
  </data>
  <data name="ManufacturerMainInfo" xml:space="preserve">
    <value>Hauptinformationen</value>
  </data>
  <data name="ManufacturerSuppliers" xml:space="preserve">
    <value>Lieferanten, die sich verteilen</value>
  </data>
  <data name="Notes" xml:space="preserve">
    <value>Anmerkungen</value>
  </data>
  <data name="Package" xml:space="preserve">
    <value>Paket</value>
  </data>
  <data name="POAdd" xml:space="preserve">
    <value>Addieren Sie neuen Kaufauftrag</value>
  </data>
  <data name="POLines" xml:space="preserve">
    <value>Linien</value>
  </data>
  <data name="POMainInfo" xml:space="preserve">
    <value>Hauptinformationen</value>
  </data>
  <data name="POReceivingInfo" xml:space="preserve">
    <value>Erhalten von Informationen</value>
  </data>
  <data name="POReceivingLines" xml:space="preserve">
    <value>Linien für das Empfangen</value>
  </data>
  <data name="Product" xml:space="preserve">
    <value>Produkt</value>
  </data>
  <data name="ProductDutyRateHistory" xml:space="preserve">
    <value>Steuersatz-Geschichte</value>
  </data>
  <data name="PurchaseOrders" xml:space="preserve">
    <value>Kaufaufträge</value>
  </data>
  <data name="PurchaseOrdersReceive" xml:space="preserve">
    <value>Empfangen Sie Kaufaufträge</value>
  </data>
  <data name="PurchaseRequisitions" xml:space="preserve">
    <value>Kauf-Forderungen</value>
  </data>
  <data name="PurReqMainInfo" xml:space="preserve">
    <value>Hauptinformationen</value>
  </data>
  <data name="QuoteAdd" xml:space="preserve">
    <value>Addieren Sie neuen Preisangabe</value>
  </data>
  <data name="QuoteLines" xml:space="preserve">
    <value>Linien</value>
  </data>
  <data name="QuoteMainInfo" xml:space="preserve">
    <value>Hauptinformationen</value>
  </data>
  <data name="Quotes" xml:space="preserve">
    <value>Preisangaben</value>
  </data>
  <data name="Reason" xml:space="preserve">
    <value>Nahe Gründe</value>
  </data>
  <data name="Reports" xml:space="preserve">
    <value>Reports</value>
  </data>
  <data name="ReportVariables" xml:space="preserve">
    <value>Berichten Sie über Parameter</value>
  </data>
  <data name="SalesOrders" xml:space="preserve">
    <value>Verkaufs-Aufträge</value>
  </data>
  <data name="SalesOrdersShip" xml:space="preserve">
    <value>Schiffs-Verkaufs-Aufträge</value>
  </data>
  <data name="SecurityGroupMembers" xml:space="preserve">
    <value>Gruppen-Mitglieder</value>
  </data>
  <data name="SecurityGroupPermissionsGeneral" xml:space="preserve">
    <value>Allgemeine Erlaubnis</value>
  </data>
  <data name="SecurityGroupPermissionsPages" xml:space="preserve">
    <value>Seiten-Erlaubnis</value>
  </data>
  <data name="SecurityGroupPermissionsReports" xml:space="preserve">
    <value>Berichten Sie über Erlaubnis</value>
  </data>
  <data name="SecurityGroups" xml:space="preserve">
    <value>Sicherheits-Gruppen</value>
  </data>
  <data name="SecurityUserGroups" xml:space="preserve">
    <value>Sicherheits-Gruppen</value>
  </data>
  <data name="SecurityUsers" xml:space="preserve">
    <value>Sicherheits-Benutzer</value>
  </data>
  <data name="SelectPart" xml:space="preserve">
    <value>Wählen Sie Teil vor</value>
  </data>
  <data name="Sequencer" xml:space="preserve">
    <value>Folgenummern</value>
  </data>
  <data name="ServiceAdd" xml:space="preserve">
    <value>Addieren Sie neuen Service</value>
  </data>
  <data name="ServiceAllocations" xml:space="preserve">
    <value>Verteilungen</value>
  </data>
  <data name="ServiceMainInfo" xml:space="preserve">
    <value>Hauptinformationen</value>
  </data>
  <data name="Services" xml:space="preserve">
    <value>Dienstleistungen</value>
  </data>
  <data name="ServicesForLots" xml:space="preserve">
    <value>Dienstleistungen fur {0}</value>
  </data>
  <data name="Shipping" xml:space="preserve">
    <value>Verschiffen</value>
  </data>
  <data name="ShipVia" xml:space="preserve">
    <value>Verschiffen-Methoden</value>
  </data>
  <data name="SOAdd" xml:space="preserve">
    <value>Addieren Sie neuen Verkaufs-Auftrag</value>
  </data>
  <data name="SOAuthorisation" xml:space="preserve">
    <value>Ermächtigungs-Informationen</value>
  </data>
  <data name="SOLines" xml:space="preserve">
    <value>Linien</value>
  </data>
  <data name="SOMainInfo" xml:space="preserve">
    <value>Hauptinformationen</value>
  </data>
  <data name="SOShippingInfo" xml:space="preserve">
    <value>Verschiffen-Informationen</value>
  </data>
  <data name="SOShippingLines" xml:space="preserve">
    <value>Linien für Verschiffen</value>
  </data>
  <data name="Sourcing" xml:space="preserve">
    <value>Auftreten</value>
  </data>
  <data name="SourcingLinks" xml:space="preserve">
    <value>Auftreten-Verbindungen</value>
  </data>
  <data name="SRMAAdd" xml:space="preserve">
    <value>Addieren Sie neuen Lieferanten RMA</value>
  </data>
  <data name="SRMALines" xml:space="preserve">
    <value>Linien</value>
  </data>
  <data name="SRMAMainInfo" xml:space="preserve">
    <value>Hauptinformationen</value>
  </data>
  <data name="SRMAS" xml:space="preserve">
    <value>Lieferanten RMAs</value>
  </data>
  <data name="SRMAShippingInfo" xml:space="preserve">
    <value>Verschiffen-Informationen</value>
  </data>
  <data name="SRMAShippingLines" xml:space="preserve">
    <value>Linien für Verschiffen</value>
  </data>
  <data name="SRMAsShip" xml:space="preserve">
    <value>Schiffs-Lieferant RMAs</value>
  </data>
  <data name="Stock" xml:space="preserve">
    <value>Vorrat</value>
  </data>
  <data name="StockAdd" xml:space="preserve">
    <value>Addieren Sie neues auf lagereinzelteil</value>
  </data>
  <data name="StockAllocations" xml:space="preserve">
    <value>Verteilungen</value>
  </data>
  <data name="StockForLots" xml:space="preserve">
    <value>Vorrat für {0}</value>
  </data>
  <data name="StockImages" xml:space="preserve">
    <value>Bilder</value>
  </data>
  <data name="StockLog" xml:space="preserve">
    <value>Maschinenbordbuch</value>
  </data>
  <data name="StockLogReason" xml:space="preserve">
    <value>Auf lagermaschinenbordbuch-Grund</value>
  </data>
  <data name="StockMainInfo" xml:space="preserve">
    <value>Hauptinformationen</value>
  </data>
  <data name="StockRelatedStock" xml:space="preserve">
    <value>In Verbindung stehender Vorrat</value>
  </data>
  <data name="StockTransactions" xml:space="preserve">
    <value>In Verbindung stehende Verhandlungen</value>
  </data>
  <data name="TableActivity" xml:space="preserve">
    <value>Neue Tätigkeit</value>
  </data>
  <data name="Tax" xml:space="preserve">
    <value>Steuer</value>
  </data>
  <data name="TaxRateHistory" xml:space="preserve">
    <value>Raten-Geschichte</value>
  </data>
  <data name="Team" xml:space="preserve">
    <value>Mannschaft</value>
  </data>
  <data name="TeamMembers" xml:space="preserve">
    <value>Teammitglieder</value>
  </data>
  <data name="Terms" xml:space="preserve">
    <value>Ausdrücke</value>
  </data>
  <data name="ToDo" xml:space="preserve">
    <value>Zu Liste tun</value>
  </data>
  <data name="Transactions" xml:space="preserve">
    <value>Öffnen Sie Verhandlungen</value>
  </data>
  <data name="User" xml:space="preserve">
    <value>Benutzer</value>
  </data>
  <data name="UserPreferences" xml:space="preserve">
    <value>Präferenzen</value>
  </data>
  <data name="UserProfile" xml:space="preserve">
    <value>Profil</value>
  </data>
  <data name="Warehouse" xml:space="preserve">
    <value>Lager</value>
  </data>
  <data name="Incoterm" xml:space="preserve">
    <value>Incoterms</value>
  </data>
</root>