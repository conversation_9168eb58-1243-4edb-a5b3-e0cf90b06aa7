Type.registerNamespace("Rebound.GlobalTrader.Site.Pages.Contact");Rebound.GlobalTrader.Site.Pages.Contact.Contact=function(n){Rebound.GlobalTrader.Site.Pages.Contact.Contact.initializeBase(this,[n]);this._strPathToData="Code/Pages/Contact";this._strDataObject="Contact"};Rebound.GlobalTrader.Site.Pages.Contact.Contact.prototype={get_ctlEllipses_AllCompanies:function(){return this._ctlEllipses_AllCompanies},set_ctlEllipses_AllCompanies:function(n){this._ctlEllipses_AllCompanies!==n&&(this._ctlEllipses_AllCompanies=n)},get_ctlEllipses_Manufacturers:function(){return this._ctlEllipses_Manufacturers},set_ctlEllipses_Manufacturers:function(n){this._ctlEllipses_Manufacturers!==n&&(this._ctlEllipses_Manufacturers=n)},get_ctlEllipses_Prospects:function(){return this._ctlEllipses_Prospects},set_ctlEllipses_Prospects:function(n){this._ctlEllipses_Prospects!==n&&(this._ctlEllipses_Prospects=n)},get_ctlEllipses_Customers:function(){return this._ctlEllipses_Customers},set_ctlEllipses_Customers:function(n){this._ctlEllipses_Customers!==n&&(this._ctlEllipses_Customers=n)},get_ctlEllipses_Suppliers:function(){return this._ctlEllipses_Suppliers},set_ctlEllipses_Suppliers:function(n){this._ctlEllipses_Suppliers!==n&&(this._ctlEllipses_Suppliers=n)},get_ctlEllipses_Contacts:function(){return this._ctlEllipses_Contacts},set_ctlEllipses_Contacts:function(n){this._ctlEllipses_Contacts!==n&&(this._ctlEllipses_Contacts=n)},initialize:function(){Rebound.GlobalTrader.Site.Pages.Contact.Contact.callBaseMethod(this,"initialize")},goInit:function(){Rebound.GlobalTrader.Site.Pages.Contact.Contact.callBaseMethod(this,"goInit")},dispose:function(){this.isDisposed||(this._ctlEllipses_AllCompanies&&this._ctlEllipses_AllCompanies.dispose(),this._ctlEllipses_Manufacturers&&this._ctlEllipses_Manufacturers.dispose(),this._ctlEllipses_Prospects&&this._ctlEllipses_Prospects.dispose(),this._ctlEllipses_Customers&&this._ctlEllipses_Customers.dispose(),this._ctlEllipses_Suppliers&&this._ctlEllipses_Suppliers.dispose(),this._ctlEllipses_Contacts&&this._ctlEllipses_Contacts.dispose(),this._ctlEllipses_AllCompanies=null,this._ctlEllipses_Manufacturers=null,this._ctlEllipses_Prospects=null,this._ctlEllipses_Customers=null,this._ctlEllipses_Suppliers=null,this._ctlEllipses_Contacts=null,this._strPathToData=null,this._strDataObject=null,Rebound.GlobalTrader.Site.Pages.Contact.Contact.callBaseMethod(this,"dispose"))},countAllCompanies:function(){var n=this._ctlEllipses_AllCompanies._objData;n.set_PathToData(this._strPathToData);n.set_DataObject(this._strDataObject);n.set_DataAction("CountAllCompanies")},countManufacturers:function(){var n=this._ctlEllipses_Manufacturers._objData;n.set_PathToData(this._strPathToData);n.set_DataObject(this._strDataObject);n.set_DataAction("CountManufacturers")},countProspects:function(){var n=this._ctlEllipses_Prospects._objData;n.set_PathToData(this._strPathToData);n.set_DataObject(this._strDataObject);n.set_DataAction("CountProspects")},countCustomers:function(){var n=this._ctlEllipses_Customers._objData;n.set_PathToData(this._strPathToData);n.set_DataObject(this._strDataObject);n.set_DataAction("CountCustomers")},countSuppliers:function(){var n=this._ctlEllipses_Suppliers._objData;n.set_PathToData(this._strPathToData);n.set_DataObject(this._strDataObject);n.set_DataAction("CountSuppliers")},countContacts:function(){var n=this._ctlEllipses_Contacts._objData;n.set_PathToData(this._strPathToData);n.set_DataObject(this._strDataObject);n.set_DataAction("CountContacts")}};Rebound.GlobalTrader.Site.Pages.Contact.Contact.registerClass("Rebound.GlobalTrader.Site.Pages.Contact.Contact",Rebound.GlobalTrader.Site.Pages.Content,Sys.IDisposable);