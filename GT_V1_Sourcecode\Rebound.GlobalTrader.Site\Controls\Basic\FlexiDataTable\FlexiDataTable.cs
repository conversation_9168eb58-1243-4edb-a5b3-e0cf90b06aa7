//-----------------------------------------------------------------------------------------
// RP 06.11.2009:
// - begin adding ability to sort data in table without returning to the database
//-----------------------------------------------------------------------------------------
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using System.Collections;
using System.Threading;
using AjaxControlToolkit;
using Rebound.GlobalTrader.Site.Enumerations;

namespace Rebound.GlobalTrader.Site.Controls {
	[DefaultProperty("")]
	[ToolboxData("<{0}:FlexiDataTable runat=server></{0}:FlexiDataTable>")]
	public class FlexiDataTable : Panel, IScriptControl, INamingContainer {

		#region Locals

		protected ScriptManager _sm;
		protected Panel _pnlScroll;
		protected Table _tbl;
		protected Table _tblHeader;
		protected Panel _pnlHeader;
		protected Panel _pnlHandleFooter;
		protected AjaxControlToolkit.ResizableControlExtender _rceResizing;
		private bool _blnIsScrollable = false;

		#endregion

		#region Properties

		/// <summary>
		/// Columns
		/// </summary>
		private List<FlexiDataColumn> _lstColumns = new List<FlexiDataColumn>();
		public List<FlexiDataColumn> Columns {
			get { return _lstColumns; }
			set { _lstColumns = value; }
		}

		/// <summary>
		/// Current Page
		/// </summary>
		private int _intCurrentPage = 1;
		public int CurrentPage {
			get { return _intCurrentPage; }
			set { _intCurrentPage = value; }
		}

		/// <summary>
		/// Page Size
		/// </summary>
		private int _intPageSize = 10;
		public int PageSize {
			get { return _intPageSize; }
			set { _intPageSize = value; }
		}

		/// <summary>
		/// Sort field index
		/// </summary>
		private int _intSortColumnIndex = 0;
		public int SortColumnIndex {
			get { return _intSortColumnIndex; }
			set { _intSortColumnIndex = value; }
		}

		/// <summary>
		/// Sort field direction
		/// </summary>
		private SortColumnDirection _enmSortColumnDirection = SortColumnDirection.ASC;
		public SortColumnDirection SortColumnDirection {
			get { return _enmSortColumnDirection; }
			set { _enmSortColumnDirection = value; }
		}

		/// <summary>
		/// Are the filters on?
		/// </summary>
		private bool _blnFiltersOn = false;
		public bool FiltersOn {
			get { return _blnFiltersOn; }
			set { _blnFiltersOn = value; }
		}

		/// <summary>
		/// height of panel (for scrolling)
		/// </summary>
		private Unit _untPanelHeight = Unit.Empty;
		public Unit PanelHeight {
			get { return _untPanelHeight; }
			set { _untPanelHeight = value; }
		}

		/// <summary>
		/// Allow this box to have a selection
		/// </summary>
		private bool _blnAllowSelection;
		public bool AllowSelection {
			get { return _blnAllowSelection; }
			set { _blnAllowSelection = value; }
		}

		/// <summary>
		/// Allow more than one selection
		/// </summary>
		private bool _blnAllowMultipleSelection;
		public bool AllowMultipleSelection {
			get { return _blnAllowMultipleSelection; }
			set { _blnAllowMultipleSelection = value; }
		}

		/// <summary>
		/// Initial values - if this is a selectable box
		/// </summary>
		private List<string> _lstInitialValues = new List<string>();
		public List<string> InitialValues {
			get { return _lstInitialValues; }
			set { _lstInitialValues = value; }
		}

		/// <summary>
		/// Is this table inside a Data List Nugget
		/// </summary>
		private bool _blnInsideDataListNugget = false;
		public bool InsideDataListNugget {
			get { return _blnInsideDataListNugget; }
			set { _blnInsideDataListNugget = value; }
		}

		/// <summary>
		/// Should header be shown
		/// </summary>
		private bool _blnShowHeader = true;
		public bool ShowHeader {
			get { return _blnShowHeader; }
			set { _blnShowHeader = value; }
		}

		/// <summary>
		/// Is the table visible on first load of the screen
		/// </summary>
		private bool _blnIsInitiallyVisible = true;
		public bool IsInitiallyVisible {
			get { return _blnIsInitiallyVisible; }
			set { _blnIsInitiallyVisible = value; }
		}

		/// <summary>
		/// Can the table raise a double-clicked event?
		/// </summary>
		private bool _blnAllowDoubleClick = false;
		public bool AllowDoubleClick {
			get { return _blnAllowDoubleClick; }
			set { _blnAllowDoubleClick = value; }
		}

		/// <summary>
		/// Can the table raise a double-clicked event?
		/// </summary>
		private bool _blnAllowResizeOnFixedHeight = true;
		public bool AllowResizeOnFixedHeight {
			get { return _blnAllowResizeOnFixedHeight; }
			set { _blnAllowResizeOnFixedHeight = value; }
		}

		/// <summary>
		/// Can the table be sorted using javascript (without going back to the database?)
		/// </summary>
		private bool _blnAllowClientSideSorting = false;
		public bool AllowClientSideSorting {
			get { return _blnAllowClientSideSorting; }
			set { _blnAllowClientSideSorting = value; }
		}

		#endregion

		#region Constructors

		public FlexiDataTable() { }

		#endregion

		#region Overrides

		protected override void OnInit(EventArgs e) {
			((Pages.Base)Page).AddCSSFile("FlexiDataTable.css");
			this.EnableViewState = false;
			if (_blnAllowMultipleSelection) _blnAllowSelection = true;
			if (_untPanelHeight == Unit.Empty) _blnAllowResizeOnFixedHeight = false;
			base.OnInit(e);
		}

		protected override void CreateChildControls() {
			CssClass = "dataTableOuter";

			//header panel and table
			_pnlHeader = ControlBuilders.CreatePanelInsideParent(this, "dataTableHeader");
			if (!_blnShowHeader) _pnlHeader.CssClass += " invisible";
			_tblHeader = ControlBuilders.CreateTable("dataTable");
			_tblHeader.ID = "tblHeader";
			_tblHeader.Width = Unit.Percentage(100);
			_pnlHeader.Controls.Add(_tblHeader);

			//col headings
			TableHeaderRow trHeading = new TableHeaderRow();
			trHeading.VerticalAlign = VerticalAlign.Top;
			for (int i = 0; i < _lstColumns.Count; i++) {
				//set id and css class
				_lstColumns[i].ID = string.Format("th_{0}", i);
				_lstColumns[i].CssClass = (i == _lstColumns.Count - 1) ? "last" : "";
				_lstColumns[i].ColumnIndex = i;
				_lstColumns[i].ParentFlexiDataTable = this;
				if (i == SortColumnIndex) {
					_lstColumns[i].IsCurrentSort = true;
					_lstColumns[i].SortDirection = this.SortColumnDirection;
				}
				trHeading.Cells.Add(_lstColumns[i]);
			}
			_tblHeader.Rows.Add(trHeading);
			trHeading.Dispose(); trHeading = null;

			//scroll panel
			_pnlScroll = ControlBuilders.CreatePanelInsideParent(this);
			_pnlScroll.ID = "pnlScroll";
			if (_untPanelHeight != Unit.Empty) {
				_pnlScroll.CssClass = "dataTableScroll";
				_pnlScroll.Height = _untPanelHeight;
				_pnlScroll.EnableViewState = false;
				_blnIsScrollable = true;
			}

			//table
			_tbl = ControlBuilders.CreateTable("dataTable");
			_tbl.ID = "tbl";
			if (_blnAllowSelection) _tbl.CssClass += " dataTableSelectable";
			_pnlScroll.Controls.Add(_tbl);

			//resizing stuff
			if (_blnAllowResizeOnFixedHeight) {
				//resizeHandler
				_rceResizing = new AjaxControlToolkit.ResizableControlExtender();
				_rceResizing.ID = "rceResizing";
				_rceResizing.TargetControlID = _pnlScroll.ID;
				_rceResizing.HandleCssClass = "handle invisible";
				_rceResizing.ResizableCssClass = "resizing";
				_rceResizing.MinimumHeight = Convert.ToInt32(_untPanelHeight.Value);
				_rceResizing.EnableViewState = false;
				_pnlScroll.Controls.Add(_rceResizing);

				//resizeHandleFooter
				_pnlHandleFooter = ControlBuilders.CreatePanel("handleFooter");
				_pnlHandleFooter.ID = "pnlHandleFooter";
				Controls.Add(_pnlHandleFooter);
			}

			base.CreateChildControls();
		}


		/// <summary>
		/// OnPreRender
		/// </summary>
		/// <param name="e"></param>
		protected override void OnPreRender(EventArgs e) {
			CssClass = (_blnIsInitiallyVisible) ? "dataTableOuter" : "dataTableOuter invisible";

			//add sorting script if needed
			if (_blnAllowClientSideSorting) {
				((Pages.Base)Page).AddScriptReference("Rebound.GlobalTrader.Site.Controls.Basic.FlexiDataTable.FlexiDataTableSorting");
			}

			//add script reference
			if (!this.DesignMode) {
				_sm = ScriptManager.GetCurrent(Page);
				if (_sm == null) throw new HttpException("A ScriptManager control must exist on the current page.");
				_sm.RegisterScriptControl(this);
			}
			base.OnPreRender(e);
		}

		/// <summary>
		/// Render
		/// </summary>
		/// <param name="writer"></param>
		protected override void Render(HtmlTextWriter writer) {
			if (!this.DesignMode) _sm.RegisterScriptDescriptors(this);
			base.Render(writer);
		}

		#endregion

		#region IScriptControl Members

		protected virtual IEnumerable<ScriptReference> GetScriptReferences() {
			bool blnDebug = false;
#if (DEBUG)
			blnDebug = true;
#endif
			return new ScriptReference[] { Functions.GetScriptReference(blnDebug, "Rebound.GlobalTrader.Site", "Controls.Basic.FlexiDataTable.FlexiDataTable", true) };
		}

		protected virtual IEnumerable<ScriptDescriptor> GetScriptDescriptors() {
			ScriptControlDescriptor descriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.FlexiDataTable", this.ClientID);
			descriptor.AddElementProperty("tbl", _tbl.ClientID);
			descriptor.AddElementProperty("tblHeader", _tblHeader.ClientID);
			descriptor.AddProperty("intCurrentPageSize", PageSize);
			descriptor.AddProperty("intCurrentPage", CurrentPage);
			descriptor.AddProperty("blnFiltersOn", FiltersOn);
			descriptor.AddProperty("intSortColumnIndex", _intSortColumnIndex);
			descriptor.AddProperty("enmSortDirection", (int)_enmSortColumnDirection);
			descriptor.AddProperty("blnAllowSelection", _blnAllowSelection);
			descriptor.AddProperty("blnAllowMultipleSelection", _blnAllowMultipleSelection);
			descriptor.AddProperty("aryCurrentValues", _lstInitialValues);
			descriptor.AddProperty("blnInsideDataListNugget", _blnInsideDataListNugget);
			descriptor.AddProperty("blnShowHeader", _blnShowHeader);
			descriptor.AddProperty("blnIsScrollable", _blnIsScrollable);
			descriptor.AddProperty("blnAllowDoubleClick", _blnAllowDoubleClick);
			descriptor.AddElementProperty("pnlScroll", _pnlScroll.ClientID);
			descriptor.AddProperty("blnAllowResize", _blnAllowResizeOnFixedHeight);
			descriptor.AddProperty("blnAllowClientSideSorting", _blnAllowClientSideSorting);
			if (_blnAllowResizeOnFixedHeight) {
				descriptor.AddElementProperty("pnlHandleFooter", _pnlHandleFooter.ClientID);
				descriptor.AddComponentProperty("rceResizing", _rceResizing.ClientID);
			}

			//columns
			List<string> lstHeaderCellClientIDs = new List<string>();
			List<bool> lstColumnIsSortable = new List<bool>();
			List<int> lstColumnAlignment = new List<int>();
			List<string> lstColumnWidth = new List<string>();
			List<int> lstColumnClientSortFormat = new List<int>();
			for (int i = 0; i < _lstColumns.Count; i++) {
				lstHeaderCellClientIDs.Add(_tblHeader.Rows[0].Cells[i].ClientID);
				lstColumnIsSortable.Add(_lstColumns[i].IsSortable);
				lstColumnAlignment.Add((int)_lstColumns[i].HorizontalAlign);
				lstColumnWidth.Add(_lstColumns[i].Width.ToString());
				lstColumnClientSortFormat.Add((int)_lstColumns[i].FormatForClientSort);
			}
			descriptor.AddProperty("aryHeaderCellIDs", lstHeaderCellClientIDs);
			descriptor.AddProperty("aryColumnIsSortable", lstColumnIsSortable);
			descriptor.AddProperty("aryColumnAlignment", lstColumnAlignment);
			descriptor.AddProperty("aryColumnWidth", lstColumnWidth);
			descriptor.AddProperty("aryColumnClientSortFormat", lstColumnClientSortFormat);
			return new ScriptDescriptor[] { descriptor };
		}

		IEnumerable<ScriptReference> IScriptControl.GetScriptReferences() {
			return GetScriptReferences();
		}

		IEnumerable<ScriptDescriptor> IScriptControl.GetScriptDescriptors() {
			return GetScriptDescriptors();
		}

		#endregion
	}
}