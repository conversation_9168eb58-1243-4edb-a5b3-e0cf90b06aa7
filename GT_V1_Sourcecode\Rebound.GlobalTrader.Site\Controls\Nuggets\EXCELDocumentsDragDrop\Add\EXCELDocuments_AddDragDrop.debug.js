﻿
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.EXCELDocuments_AddDragDrop = function(element) {
    Rebound.GlobalTrader.Site.Controls.Forms.EXCELDocuments_AddDragDrop.initializeBase(this, [element]);
    this._intSectionID = -1;
    //this._ctlFileUpload = null;
    this._strSectionName = null;
    this._strExcel = null;
    this._strFileName = "";
    this._dragobj = null;
    this._strDBFileName = "";
};

Rebound.GlobalTrader.Site.Controls.Forms.EXCELDocuments_AddDragDrop.prototype = {

    initialize: function() {

    Rebound.GlobalTrader.Site.Controls.Forms.EXCELDocuments_AddDragDrop.callBaseMethod(this, "initialize");
        this.addShown(Function.createDelegate(this, this.formShown));
    },

    dispose: function() {
        if (this.isDisposed) return;
        if (this._ctlFileUpload) this._ctlFileUpload.dispose();
        this._intSectionID = -1;
        this._ctlFileUpload = null;
        this._strSectionName = null;
        this._strFileName = null;
        this._dragobj = null;
        this._strDBFileName = null;
        Rebound.GlobalTrader.Site.Controls.Forms.EXCELDocuments_AddDragDrop.callBaseMethod(this, "dispose");
    },

    formShown: function() {
    if (this._blnFirstTimeShown) {
        this.addSave(Function.createDelegate(this, this.saveClicked));
        //this.setFieldValue("ctlFile", "");
    }
    this.setFormFieldsToDefaults();

    this.showInnerContent(true);
    },

    saveClicked: function() {

        if (!this.validateForm()) return;
        this.showSaving(true);
        if (this._dragobj) this._dragobj.startUpload();
        //this._ctlFileUpload.doUpload();

    },

    uploadError: function() {
        this.showError(true);
        this.showInnerContent(true);
        //this.setFieldInError("ctlFile", true, this._ctlFileUpload._strErrorMessage);
    },

    uploadFatalError: function() {
        //this.showNuggetError(true, this._ctlFileUpload._strErrorMessage);
        this.showInnerContent(true);
       // this._ctlFileUpload.reset();
    },

    uploadComplete: function() {
        //this.saveEdit();
    },

    saveEdit: function() {

        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/EXCELDocumentsDragDrop");
        obj.set_DataObject("EXCELDocumentsDragDrop");
        obj.set_DataAction("AddNew");
        obj._intTimeoutMilliseconds = 30000;
        obj.addParameter("id", this._intSectionID);
        obj.addParameter("Caption", this.getFieldValue("ctlCaption"));
        obj.addParameter("TempFile", this._strDBFileName);
        obj.addParameter("Section", this._strSectionName);
        obj.addDataOK(Function.createDelegate(this, this.saveEditComplete));
        obj.addError(Function.createDelegate(this, this.saveEditError));
        obj.addTimeout(Function.createDelegate(this, this.saveEditError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    saveEditError: function(args) {
        this._strErrorMessage = args._errorMessage;
        this.showNuggetError(true, this._strErrorMessage);
        this.onSaveError();
    },

    saveEditComplete: function(args) {
        if (args._result.Result == true) {
            this.onSaveComplete();
        } else {
            if (args._result.Message) this._strErrorMessage = args._result.Message;
            this.onSaveError();
        }
    },

    validateForm: function() {
        this.onValidate();
        var blnOK = this.autoValidateFields();
        return blnOK;
    }

};

Rebound.GlobalTrader.Site.Controls.Forms.EXCELDocuments_AddDragDrop.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.EXCELDocuments_AddDragDrop", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
