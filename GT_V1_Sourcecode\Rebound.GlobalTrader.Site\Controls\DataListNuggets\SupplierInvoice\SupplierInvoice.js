Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DataListNuggets");Rebound.GlobalTrader.Site.Controls.DataListNuggets.SupplierInvoice=function(n){Rebound.GlobalTrader.Site.Controls.DataListNuggets.SupplierInvoice.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.DataListNuggets.SupplierInvoice.prototype={get_blnShowCanNotBeExported:function(){return this._blnShowCanNotBeExported},set_blnShowCanNotBeExported:function(n){this._blnShowCanNotBeExported!==n&&(this._blnShowCanNotBeExported=n)},initialize:function(){this.addSetupDataCallEvent(Function.createDelegate(this,this.setupDataCall));this.addGetDataOKEvent(Function.createDelegate(this,this.getDataOK));this.addInitCompleteEvent(Function.createDelegate(this,this.initAfterBaseIsReady));this._strPathToData="controls/DataListNuggets/SupplierInvoice";this._strDataObject="SupplierInvoice";this.showFilterField("ctlStatus",!this._blnShowCanNotBeExported);this.showFilterField("ctlStatusReason",this._blnShowCanNotBeExported);Rebound.GlobalTrader.Site.Controls.DataListNuggets.SupplierInvoice.callBaseMethod(this,"initialize")},initAfterBaseIsReady:function(){this.addPageTabChangedEvent(Function.createDelegate(this,this.pageTabChanged));this.getData()},dispose:function(){this.isDisposed||(this._blnShowCanNotBeExported=null,Rebound.GlobalTrader.Site.Controls.DataListNuggets.SupplierInvoice.callBaseMethod(this,"dispose"))},pageTabChanged:function(){this._table._intCurrentPage=1;this._blnShowCanNotBeExported=this._intCurrentTab==1;this.showFilterField("ctlStatus",this._intCurrentTab==0);this.showFilterField("ctlStatusReason",this._intCurrentTab==1);this.getData()},setupDataCall:function(){this._objData.addParameter("CanNotBeExported",this._blnShowCanNotBeExported)},getDataOK:function(){for(var n,i,t=0,r=this._objResult.Results.length;t<r;t++)n=this._objResult.Results[t],i=[n.CanBeExported?"<input class='SupplierInvoiceCheckbox' type='checkbox' name='SelectSupplierInvoice"+n.ID+"'value='"+n.ID+"' checked disabled>":n.MatchPercentage!=null&&parseInt(n.MatchPercentage)==100?"<input class='SupplierInvoiceCheckbox' type='checkbox' name='SelectSupplierInvoice"+n.ID+"'value='"+n.ID+"'>":"<input class='SupplierInvoiceCheckbox' type='checkbox' name='SelectSupplierInvoice"+n.ID+"'value='"+n.ID+"' disabled>",$RGT_nubButton_SupplierInvoice(n.ID,n.No),$RGT_nubButton_Company(n.CompanyNo,n.Name),$R_FN.writeDoubleCellValue($RGT_nubButton_GoodsIn(n.GI,n.GINo),n.HasIPO?$RGT_nubButton_InternalPurchaseOrder(n.PONo,n.PO):$RGT_nubButton_PurchaseOrder(n.PONo,n.PO)),$R_FN.setCleanTextValue(n.URNNumber),$R_FN.setCleanTextValue(n.INVDate),$R_FN.writePartNo(n.Part,""),n.Value,$R_FN.writeDoubleCellValue(n.ItemCount,n.MatchPercentage!=null?"Matched: "+n.MatchPercentage+"%":""),"<a class='SupplierInvoiceViewDetail' data-value='"+n.ID+"'>View Detail<\/a>"],this._table.addRow(i,n.ID,!1),i=null,n=null;$(".SupplierInvoiceCheckbox").change(function(){$(this).prop("checked")?$("[value='"+$(this).prop("value")+"']").prop("checked",!0):$("[value='"+$(this).prop("value")+"']").prop("checked",!1)});$(".SupplierInvoiceViewDetail").click(function(){var n=$(this).data("value");$R_FN.openSupplierInvoiceDetailWindow(n)})}};Rebound.GlobalTrader.Site.Controls.DataListNuggets.SupplierInvoice.registerClass("Rebound.GlobalTrader.Site.Controls.DataListNuggets.SupplierInvoice",Rebound.GlobalTrader.Site.Controls.DataListNuggets.Base,Sys.IDisposable);