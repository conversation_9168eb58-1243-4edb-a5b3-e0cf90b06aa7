using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using System.Collections;
using Rebound.GlobalTrader.Site.Controls.Forms;
using Rebound.GlobalTrader.Site.Controls;

namespace Rebound.GlobalTrader.Site.Controls.FormFieldCollections {
	[DefaultProperty("")]
	[ToolboxData("<{0}:RestrictedManufacturerList runat=server></{0}:RestrictedManufacturerList>")]
	public class RestrictedManufacturerList : Base, INamingContainer {

		#region Locals

		private Table _tbl;
		private FormField _ctlRestrictedManufacturer;
		private Panel _pnlSelected;
		private Label _lblSelected;
		private AutoSearch.Manufacturers _aut;

		#endregion

		protected override void OnInit(EventArgs e) {
			base.OnInit(e);
			AddScriptReference("Controls.FormFieldCollections.RestrictedManufacturerList.RestrictedManufacturerList.js");
			RemoveCSSClass = true;
		}

		protected override void OnLoad(EventArgs e) {
			_tbl = ControlBuilders.CreateTable();
			_tbl.Width = Unit.Percentage(100);

			//To
			_ctlRestrictedManufacturer = new FormField();
			_ctlRestrictedManufacturer.ID = "ctlRestrictedManufacturer";
			_ctlRestrictedManufacturer.ResourceTitle = "Manufacturer";
			_ctlRestrictedManufacturer.FieldID = "lblSelected";
			_ctlRestrictedManufacturer.IsRequiredField = true;
			_pnlSelected = new Panel();
			_pnlSelected.ID = "pnlSelected";
			_lblSelected = ControlBuilders.CreateLabelInsideParent(_pnlSelected);
			_lblSelected.ID = "lblSelected";
			_ctlRestrictedManufacturer.AddFieldControl(_pnlSelected);
			 ReboundTextBox txtManufacturer = new ReboundTextBox();
			txtManufacturer.ID = "txtManufacturer";
			txtManufacturer.Width = 255;
			_ctlRestrictedManufacturer.AddFieldControl(txtManufacturer);
			_aut = new AutoSearch.Manufacturers();
			_aut.ID = "autRestrictedManufacturer";
			_aut.RelatedTextBoxID = "txtManufacturer";
			_aut.CharactersToEnterBeforeSearch = 1;
			_aut.ResultsActionType = Rebound.GlobalTrader.Site.Controls.AutoSearch.Base.AutoSearchResultsActionType.RaiseEvent;
			_aut.Width = 255;
			_aut.ResultsHeight = 200;
			_ctlRestrictedManufacturer.AddFieldControl(_aut);
			_tbl.Rows.Add(_ctlRestrictedManufacturer);

			AddControl(_tbl);
			SetupScriptDescriptors();
			base.OnLoad(e);
		}

		private void SetupScriptDescriptors() {
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.FormFieldCollections.RestrictedManufacturerList", this.ClientID);
			_scScriptControlDescriptor.AddElementProperty("pnlSelected", _pnlSelected.ClientID);
			_scScriptControlDescriptor.AddElementProperty("lblSelected", _lblSelected.ClientID);
			_scScriptControlDescriptor.AddComponentProperty("autLoginOrGroup", _aut.ClientID);
		}

	}
}
