﻿
body {
    font-size: 12px;
    font-family: Tahoma;
}

.container {
    width: 100%;
    padding: 0px;
}

.panel-title {
    font-size: 12px;
    font-weight: normal;
    padding: 2px 0px 0px 8px;
    float: left;
    line-height: 11px;
    font-family: Lucida Sans Unicode, Arial;
    color: #000000;
}

.panel-default {
    border: none !important;
}

.pq-grid-center-o {
    padding: 5px 5px 0px 5px;
}

.panel-default > .panel-heading {
    color: #333;
    background-color: #9fe994;
    border-color: #e4e5e7;
    padding: 0;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    background-image: url(/Areas/BOM/Images/tab_bg.jpg);
    background-repeat: repeat-x;
    float: left;
    width: 100%;
    padding: 0px 0px;
}

    .panel-default > .panel-heading a {
        padding: 10px 0px 5px 15px;
        right: 5px;
    }

.panel-collapse {
    float: left !important;
}

.panel {
    position: relative;
    float: left;
    width: 100%;
}

.panel-default > .panel-heading a:after {
    content: "";
    position: relative;
    top: 1px;
    display: inline-block;
    font-family: 'Glyphicons Halflings';
    font-style: normal;
    font-weight: 400;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    float: right;
    transition: transform .25s linear;
    -webkit-transition: -webkit-transform .25s linear;
}



.panel-default .minus_icon {
    /*content: "\2212";
                    -webkit-transform: rotate(180deg);
                    transform: rotate(180deg);*/
    background-image: url(/Areas/BOM/Images/hide.gif);
    background-repeat: no-repeat;
    position: absolute;
    right: 0px;
}

.sec_tab {
    /*content: "\2212";
                    -webkit-transform: rotate(180deg);
                    transform: rotate(180deg);*/
    background-image: url(/Areas/BOM/Images/hide.gif);
    background-repeat: no-repeat;
    position: absolute;
    right: 0px;
    padding: 7px;
}

.panel-default > .panel-heading a[aria-expanded="false"]:after {
    /*content: "\002b";
                -webkit-transform: rotate(90deg);
                transform: rotate(90deg);*/

    background-image: url(/Areas/BOM/Images/show.gif);
    background-repeat: no-repeat;
    position: absolute;
    padding: 7px;
    right: 0px;
    height: 30px;
}


    .panel-default > .panel-heading a[aria-expanded="false"]:after > .boxlink {
        /*content: "\002b";
                -webkit-transform: rotate(90deg);
                transform: rotate(90deg);*/

        visibility: hidden;
    }



.boxlink {
    padding: 10px 5px 0px 5px;
    color: #009900;
    font-size: 11px;
    line-height: 0px;
    width: 100%;
    float: left;
}


    .boxlink a {
        color: #009900;
        float: left;
        padding: 6px 10px 5px 0px !important;
    }

.linkbuttoncss {
    background-position: left top;
    padding: 3px 5px 0px 21px;
    background-repeat: no-repeat;
}

.edit {
    background-image: url(/Areas/BOM/Images/edit_x.gif);
}

.export {
    background-image: url(/Areas/BOM/Images/export.gif);
}

.purchase_hub {
    background-image: url(/Areas/BOM/Images/trusted_rfq.gif);
}

.supplier {
    background-image: url(/Areas/BOM/Images/notify.gif);
}

.close_icon {
    background-image: url(/Areas/BOM/Images/close.gif);
}

.note {
    background-image: url(/Areas/BOM/Images/add.gif);
}

.release {
    background-image: url(/Areas/BOM/Images/release.gif);
}

.delete {
    background-image: url(/Areas/BOM/Images/delete.gif);
}


.item_details .desc {
    width: 104px !important;
}

.desc {
    width: 95px; /*changes for version.3*/
    font-size: 11px; /*changes for version.3*/
    font-weight: bold;
    color: #000;
    padding-right: 15px;
    padding-bottom: 5px;
}

table tr td {
    font-size: 11px; /*changes for version.3*/
}


.top_tbl tr {
    vertical-align: top;
    width: 100%;
    float: left;
}


.first_tbl tr:hover {
    background-color: #e0ffe0;
}

.item label {
    font-weight: normal;
    font-family: Tahoma !important;
    font-size: 11px !important;
    color: #000 !important;
}

table {
    padding: 6px;
}

.comm_tb thead {
    background-image: url(/Areas/BOM/Images/th_bg.gif);
    background-position: bottom;
    background-repeat: no-repeat;
    background-color: #eeeeee;
    font-weight: bold;
    border-bottom: solid 2px #e0e0e0;
    color: #999999;
}

    .comm_tb thead th {
        width: 100vh;
        border-right: solid 1px #bbbbbb;
        padding: 2px 5px 8px 2px;
    }

.sec_panel {
    width: 100%;
    background-color: #ffffff;
    background-image: url(./images/bg.jpg);
    background-repeat: repeat-x;
    background-position: bottom;
    padding: 8px 5px 4px;
    font-size: 11px;
}

.nav-tabs li {
    color: #009900;
    font-weight: bold;
    padding-left: 10px;
    padding-right: 10px;
    line-height: 20px;
}

.nav-tabs {
    border-bottom: 1px solid #ddd !important;
}

.doubleHeaderTop {
    font-size: 11px;
    border-bottom: dotted 1px #e0e0e0;
    margin-bottom: 2px;
    padding-bottom: 6px;
}

.tab-pane thead th {
    width: 100vh;
    border-right: solid 1px #bbbbbb;
    padding: 2px 5px 8px 2px;
    font-weight: bold;
    border-bottom: solid 2px #e0e0e0;
    border-right: solid 1px #bbbbbb;
    background-color: #eeeeee;
    color: #999999;
    background-image: url(./images/th_bg.png);
    background-position: bottom;
    background-repeat: repeat-x;
    font-size: 11px;
}

.bomitem_tbl {
    margin-top: 20px;
}

.nav > li > a {
    padding: 5px;
    color: #009900 !important;
    font-size: 10px;
}

.check_img {
    background-image: url(./images/ready.gif);
    background-repeat: no-repeat;
    background-position: 2px 2px;
    padding-left: 50px;
}

    .check_img input {
        margin-top: 2px;
    }


.doubleValueTop {
    font-size: 11px;
    padding-bottom: 4px;
}

.rohsCompliant {
    background-image: url(./images/compliant.gif);
}

.rohs {
    display: inline;
    background-repeat: no-repeat;
    background-position: right 2px;
    padding-right: 28px;
    font-size: 11px;
    white-space: nowrap;
}


.last_tbl tr td {
    vertical-align: top;
    padding-bottom: 8px;
    padding-right: 2px;
    border-right: solid 1px #bbbbbb;
    border-top: dotted 1px #e0e0e0;
}

/*POPupModalCSS*/
/* The Modal (background) */
.modal {
    display: none; /* Hidden by default */
    position: fixed; /* Stay in place */
    z-index: 1; /* Sit on top */
    padding-top: 100px; /* Location of the box */
    left: 0;
    top: 0;
    width: 100%; /* Full width */
    height: 100%; /* Full height */
    overflow: auto; /* Enable scroll if needed */

    background-color: rgba(0,0,0,0.85); /* Black w/ opacity */
}

/* Modal Content */
.modal-content {
    background-color: #fefefe;
    margin: auto;
    /*padding: 20px;*/ /*padding remover for popup*/
    border: 1px solid #888;
    width: 99%;
}

/* The Close Button */
.close {
    color: #aaaaaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
}

    .close:hover,
    .close:focus {
        color: #000;
        text-decoration: none;
        cursor: pointer;
    }
/*POPupModalCSS*/


.item_details {
    border-top: 4px solid #E0E0E0;
    background-image: url(../../../App_Themes/Original/images/Nuggets/standard/bg.jpg);
    background-position: bottom left;
    background-repeat: repeat-x;
    border-left: 1px solid #AAE2A0 !important;
    border-right: 1px solid #AAE2A0 !important;
    border-bottom: 1px solid #aae2a0 !important;
    padding: 5px;
}

.details tr:hover {
    background-color: #e0ffe0;
}

.icon {
    background-image: url(/Areas/BOM/Images/nub.gif);
    background-position: left center;
    background-repeat: no-repeat;
    padding-left: 13px;
}

.details tr {
    font-size: 10px !important;
}

.larger_font {
    background-repeat: no-repeat;
    font-size: 20px;
    color: blue;
}

.blackbox {
    left: 52%;
    position: absolute;
    width: 20px;
    height: 17px;
    background-repeat: no-repeat;
    background-position: center center;
}

    .blackbox img {
        width: 29px;
        height: 29px;
    }

/*changes css starts here for version.3*/

.BtnSubmitEdit, .BtnCloseEdit {
    background-image: url(../../../App_Themes/Original/images/IconButton/nuggets/save.gif);
    background-repeat: no-repeat;
    background-position: left center;
    padding: 2px 5px 0px 21px;
    color: #009900 !important;
    cursor: pointer;
}

.BtnCloseEdit {
    background-image: url(../../../App_Themes/Original/images/IconButton/nuggets/cancel.gif) !important;
}


.main_header {
    background-image: url(../../../App_Themes/Original/images/Nuggets/standard/bg_top.gif);
    background-repeat: repeat-x;
    background-position: bottom;
    background-color: #b9f0b2;
    padding: 5px;
}

.footer_area {
    background-color: #b9f0b2;
    padding: 5px;
    border-radius: 0px 0px 4px 4px;
}

.main_header h6 {
    padding: 5px 0px 8px 0px;
    font-weight: bold;
}

.main_header {
    border-radius: 4px 4px 0px 0px;
}

    .main_header h6 {
        margin: 0px !important;
    }

.modal_table {
    background-color: #56954E;
}

.form_container {
    padding: 5px 5px !important;
    background-color: #56954E;
}

.modal_table tr td {
    width: 180px;
    padding-right: 10px;
    padding-bottom: 7px;
    text-align: left;
    font-weight: bold;
    font-size: 11px;
    color: #d3fFcC;
}

    .modal_table tr td input, .modal_table tr td select {
        color: #000;
        font-weight: normal;
        border-radius: 2px;
        border: none;
    }


.refresh_icon {
    background-image: url(../../../App_Themes/Original/images/DropDowns/refresh.png);
    position: absolute;
    width: 20px;
    height: 17px;
    background-repeat: no-repeat;
    background-position: center center;
}

.input_feild {
    width: 250px;
}

.header_text h4 {
    border-bottom: dotted 1px #cccccc;
    padding-bottom: 2px;
    margin-bottom: 5px;
    color: #FFFFFF;
    font-size: 11px;
    margin: 0px 0px 2px;
    text-transform: uppercase;
    padding-top: 5px;
    font-family: Tahoma;
    font-weight: bold;
}

.formInstructions {
    position: relative;
    margin: 0px 0px;
    color: #fff;
    margin-bottom: 10px;
    font-size: 12px;
}

.modal_table #textNotes {
    width: 450px !important;
    border-radius: 4px;
}

/*changes css ends here for version.3*/


/*changes css starts here for version.3*/

.pq-cont-inner span a {
    color: #0000ff !important;
    background-image: url(../../../../App_Themes/Original/images/nubs/nub.gif);
    background-repeat: no-repeat;
    background-position: left 2px;
    padding-left: 13px;
    height: 12px;
}


.pq-grid-row:hover {
    background-color: #e0ffe0;
}

.pq-grid-header-table {
    background-image: url(../../../App_Themes/Original/images/FlexiDataTable/th_bg.png);
    background-position: bottom;
    background-repeat: repeat-x;
    border-bottom: solid 2px #e0e0e0;
    background-color: #eeeeee;
}

.pq-grid-row.pq-striped {
    background: #fff;
}

.pq-grid-col:hover {
    background-color: #e0e0e0;
    background-image: url(../../../App_Themes/Original/images/FlexiDataTable/th_bg.png);
    background-position: bottom;
    background-repeat: repeat-x;
}

.pq-grid-row.pq-striped:hover {
    background-color: #e0ffe0 !important;
    color: #000 !important;
}

.pq-grid-header-table span {
    font-weight: bold;
    font-size: 12px;
    color: #999999 !important;
}

.pq-grid-row {
    font-size: 11px !important;
    line-height: 1.6px;
    border-bottom: 1px dotted #bbb !important;
    background-color: #fff;
}

.pq-grid-cell {
    border-right: 1px solid #bbb;
}

.pq-grid-row:first-child > .pq-grid-col {
    border-right: 1px solid #bbb !important;
}
/*changes css ends here for version.3*/


/*changes css starts here for version.3*/

.ui-datepicker-calendar tr {
    float: none !important;
}

    .ui-datepicker-calendar tr td {
        float: none !important;
    }

.ui-datepicker {
    width: 19em;
    padding: 0px !important;
}

    .ui-datepicker td span, .ui-datepicker td a {
        text-align: center;
    }

.ui-state-default {
    color: #575779 !important;
    border: none !important;
    background: none !important;
}

.ui-widget.ui-widget-content {
    border: solid 2px #50508e;
    background-repeat: no-repeat;
    background-position: left center;
    font-size: 10px;
    background: #ffffff;
    background-image: url(../../../../App_Themes/Original/images/calendar/bg.jpg);
    background-size: cover;
}

.ui-datepicker table {
    font-size: 10px;
    font-weight: bold;
}

.pq-grid-bottom {
    border-bottom: solid 1px #50508e;
    background-image: url(../../../App_Themes/Original/images/Nuggets/standard/bg_top.gif) !important;
    background-position: left top !important;
    background-repeat: repeat-x !important;
}

.ui-widget-header {
    border-bottom: solid 1px #50508e;
    background: #9fe994;
}

.ui-corner-all, .ui-corner-bottom, .ui-corner-left, .ui-corner-bl {
    border-bottom-left-radius: none !important;
}

.ui-datepicker-title {
    font-size: 12px;
}

.ui-datepicker-week-end {
    color: #8888bb;
}

/*changes css ends here for version.3*/


/*changes css starts for modal 2 here for version.3*/


.BtnBackPH {
    background-image: url(../../../App_Themes/Original/images/IconButton/nuggets/back.gif);
    background-position: left center;
    background-repeat: no-repeat;
    padding-left: 23px;
    color: #009900;
    cursor: pointer;
}

.container {
    margin-top: 10px
}



.yes_icon {
    background-image: url(../../../App_Themes/Original/images/IconButton/formbody/yes.gif);
    background-position: left center;
    background-repeat: no-repeat;
    color: #fff;
    font-size: 16px;
    padding: 2px 20px 5px 21px;
    font-weight: normal;
    cursor: pointer;
}

.n_icon {
    background-image: url(../../../App_Themes/Original/images/IconButton/formbody/no.gif);
    background-position: left center;
    background-repeat: no-repeat;
    color: #fff;
    font-size: 16px;
    padding: 2px 20px 5px 21px;
    font-weight: normal;
    cursor: pointer;
}

    .yes_icon:hover, .n_icon:hover {
        color: #fff;
    }

.topTL {
    background-image: url(../../../App_Themes/Original/images/Nuggets/list/top.gif);
    margin-right: 6px;
    height: 6px;
    font-size: 2px;
}

.topTR {
    background-image: url(../../../App_Themes/Original/images/Nuggets/list/top.gif);
    margin-top: -6px;
    margin-left: 6px;
    background-position: 100% 0px;
    height: 6px;
    font-size: 2px;
}


.head_in {
    height: 42px;
    border-width: 0px;
    border-style: solid;
    border-color: #bbbbbb;
    position: relative;
    background-color: #ffffff;
    position: relative;
    background-image: url(../../../App_Themes/Original/images/Nuggets/standard/bg_top.gif);
    background-repeat: repeat-x;
}


.panel-body {
    padding: 8px 5px 4px !important;
    border-left: 1px solid #AAE2A0 !important;
    border-right: 1px solid #AAE2A0 !important;
    border-bottom: 1px solid #aae2a0 !important;
    border-radius: 0px 0px 4px 4px;
    background-image: url(../../../App_Themes/Original/images/Nuggets/standard/bg.jpg);
    background-position: bottom left;
    background-repeat: repeat-x;
}

.boxBL {
    margin-right: 6px;
    background-image: url(../../../App_Themes/Original/images/Nuggets/allwhite/bottom.gif);
    background-position: 0 -6px;
    height: 6px;
    font-size: 2px;
}

.boxBR {
    margin-top: -6px;
    margin-left: 6px;
    background-image: url(../../../App_Themes/Original/images/Nuggets/standard/bottom_collapsed.gif);
    background-position: 100% -6px;
    height: 6px;
    font-size: 2px;
}

#grid_md, #grid_xm, #grid_AutoSource, #grid_xmSourcing, .api_section, #grid_IHS, #grid_md_notes {
    background-color: #fff !important;
    border-left: 1px solid #AAE2A0 !important;
    border-right: 1px solid #AAE2A0 !important;
    border-top: none !important;
    border-bottom: 1px solid #aae2a0 !important;
    border-radius: 0px 0px 5px 5px;
}


#grid_API {
    border: none !important;
}

/*.pq-grid-center {
    height: 418px !important;
}*/



.pq-grid-row.pq-striped {
    font-size: 11px;
    border-bottom: 1px dotted #bbb !important;
}

.pq-grid-row {
    font-size: 11px !important;
    line-height: 1.6px;
    border-bottom: 1px dotted #bbb !important;
}

.pq-pager {
    padding: 1px 5px;
}

#lblItemManufacturer {
    color: #0000ff !important;
}


.pq-state-select.ui-state-highlight {
    background: #000066 !important;
    color: #fff !important;
}

    .pq-state-select.ui-state-highlight .ui-button-text {
        color: #66ccFF !important;
    }

.red {
    background-color: #000066 !important;
    color: white !important;
}


    .red .ui-button-text {
        color: #66ccFF !important;
    }




.ui-button-text {
    color: #0000ff !important;
}

    .ui-button-text:hover {
        text-decoration: underline;
    }


.ui-state-highlight a, .ui-widget-content .ui-state-highlight a, .ui-widget-header .ui-state-highlight a {
    color: #66ccFF !important;
    background-image: url(../../../App_Themes/Original/images/FlexiDataTable/nub_tb_selected.gif);
}

.page_tittle {
    width: 100%;
    float: left;
    font-family: 'Lucida Sans', 'Lucida Sans Regular', 'Lucida Grande', 'Lucida Sans Unicode', Geneva, Verdana, sans-serif;
    display: contents;
}

    .page_tittle h3 {
        font-size: 22px;
        font-weight: normal;
        margin-top: 2px;
        margin-bottom: 0px;
    }

.head_content {
    background-image: url(../../../App_Themes/Original/images/Nuggets/titlebar/bg.png);
    background-repeat: repeat-x;
    background-position: bottom;
    padding: 15px 5px 5px;
    border-bottom: solid 1px #bbbbbb;
    margin-bottom: 15px;
}

.abovePageTitle {
    color: #009900;
    font-family: Lucida Sans Unicode, Arial;
    font-size: 10px;
    font-weight: normal;
    margin: 0px;
    padding: 0px;
    text-transform: uppercase;
}

.pageTitleItem {
    padding: 2px 0px;
    color: #218610;
    font-size: 11px;
}

    .pageTitleItem .itemTitle {
        font-weight: bold;
        padding-right: 3px;
    }

.noneFound {
    color: #999999;
    font-size: 11px;
    font-style: italic;
    font-weight: normal;
    padding: 10px 0px;
}

.modal_table tr .txt {
    width: 100% !important;
    color: #fff;
    border-top: dotted 1px #ccc;
    border-bottom: dotted 1px #ccc;
    padding-top: 4px;
    margin-bottom: 7px;
    padding-bottom: 4px !important;
    display: block;
}

.pq-grid-center .pq-grid-top {
    border: none !important;
    background: none !important;
    border-bottom: 1px solid #bbb !important;
}

#grid_md .pq-grid-top, #grid_AutoSource .pq-grid-top, #grid_xmSourcing .pq-grid-top, #grid_API .pq-grid-top, #grid_IHS .pq-grid-top, #grid_md_notes .pq-grid-top {
    background: none !important;
    border-bottom: none !important;
}


/*Css added starts version1.0*/

#grid_EmsOffer .pq-grid-top {
    background: none !important;
    border-bottom: none !important;
}

/*Css added ends version1.0*/

#grid_xm .pq-grid-top {
    background: none !important;
    border-bottom: none !important;
}

/*css for tabs*/
body {
    font-family: Arial;
}

/* Style the tab */

/*Css changes starts version1.0*/
.tab {
    overflow: hidden;
    background-color: #eceae8;
}
    /*Css changes ends version1.0*/

    /* Style the buttons inside the tab */


    /*Css changes starts version1.0*/
    .tab button {
        background-color: inherit;
        float: left;
        border: none;
        outline: none;
        cursor: pointer;
        padding: .5rem 1rem;
        transition: 0.3s;
        font-size: 12px;
        width: 25%;
        font-family: 'Lucida Sans', 'Lucida Sans Regular', 'Lucida Grande', 'Lucida Sans Unicode', Geneva, Verdana, sans-serif;
        font-weight: bold;
    }
        /*Css changes ends version1.0*/

        /* Change background color of buttons on hover */
        .tab button:hover {
            background-color: #ddd;
        }

        /* Create an active/current tablink class */

        /*Css changes starts version1.0*/
        .tab button.active {
            background-color: #0c8f3f;
            color: #fff;
        }
/*Css changes ends version1.0*/


/* Style the tab content */
.tabcontent {
    display: none;
    padding: 6px 12px;
    border: 1px solid #ccc;
    border-top: none;
}
/*css form tabs*/
#dvLoading {
    background: #000 url(images/loader.gif) no-repeat center center;
    height: 100px;
    width: 100px;
    position: fixed;
    z-index: 1000;
    left: 50%;
    top: 50%;
    margin: -25px 0 0 -25px;
}

/*Css Needs to include starts version1.0*/
.top_inner h4 {
    font-family: Lucida Sans Unicode, Arial;
    font-weight: normal;
    font-size: 12px;
    line-height: 12px;
    color: #000000;
    margin: 0px;
    padding: 2px 0px 0px 5px;
}

.pq-grid {
    background-image: none !important;
}

.nested_tab button.active {
    background-color: #ffb923 !important;
}

.nested_tab .tablinks {
    background-color: #6fb320;
    width: 33.33%;
}

#grid_EmsOffer {
    border-left: 1px solid #AAE2A0 !important;
    border-right: 1px solid #AAE2A0 !important;
    border-top: none !important;
    border-bottom: 1px solid #aae2a0 !important;
}

.pq-header-outer {
    border-bottom: none !important;
    background: none !important;
}

.pq-body-outer {
    color: #000;
    font-family: Tahoma !important;
    /*height: 389px !important;*/
}

#grid_xmSourcing .pq-body-outer, #grid_API .pq-body-outer, #grid_xm .pq-body-outer {
    /*height: 382px !important;*/
}

.pq-td-border-right > .pq-grid-row > .pq-grid-cell {
    border-right-color: #bbb !important;
    font-family: Tahoma !important;
}

.refreshButton {
    display: block;
    visibility: visible;
    background-image: url(../../../App_Themes/Original/images/Nuggets/standard/refresh.gif);
}

.refreshButton {
    background-repeat: no-repeat;
    height: 10px;
    width: 11px;
    position: absolute;
    top: 3px;
    right: 24px;
    margin: 0px;
    padding: 0px;
    cursor: pointer;
}


.showHide {
    background-image: url(../../../App_Themes/Original/images/Nuggets/list/hide.gif);
    position: absolute;
    top: 0px;
    right: 5px;
    margin: 0px;
    padding: 0px;
    height: 15px;
    width: 16px;
    cursor: pointer;
    background-repeat: no-repeat;
    background-position: center center;
}

/*Css Needs to include ends version1.0*/

.modal-body {
    padding: 0px !important;
}

    .modal-body tr {
        vertical-align: top;
        height: 22px !important;
        width: 100%;
        float: left;
    }

    .modal-body .leftsec {
        width: 70% !important;
        float: left;
    }

    .modal-body tr td input {
        border-radius: 2px;
        color: #000;
        border-style: none;
        font-weight: normal;
    }

    .modal-body tr td select {
        border-radius: 2px;
        color: #000;
        padding: 1px 0px;
        border-style: none;
        font-weight: normal;
        float: left;
    }

.col label {
    float: left;
    margin-right: 15px;
    color: #fff;
    font-weight: normal;
}

.col a {
    color: #000;
    float: left;
    font-weight: normal;
}

.tabcontent2 {
    background-image: url(../../../App_Themes/Original/images/Nuggets/standard/bg.jpg);
    background-position: center bottom;
    background-repeat: repeat-x;
    float: left;
    width: 100%;
}

input[type=number]::-webkit-inner-spin-button,
input[type=number]::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

input[type=number] {
    -moz-appearance: textfield;
}

.errorSummary {
    background-color: #EE0000;
    background-image: url(../../../App_Themes/Original/images/Forms/form_error.gif);
    background-position: 6px 6px;
    background-repeat: no-repeat;
    border: 1px solid #990000;
    color: #FFFFFF;
    font-weight: bold;
    margin: 10px 5px 0px;
    padding: 5px 5px 5px 40px;
}

.section_container {
    margin-bottom: 15px;
}

.main_header h6 {
    font-family: 'Lucida Sans', 'Lucida Sans Regular', 'Lucida Grande', 'Lucida Sans Unicode', Geneva, Verdana, sans-serif;
    font-size: 12px;
    font-weight: normal;
}



.ui-menu-item {
    padding: 0px !important;
}

    .ui-menu-item div {
        padding: 0px 0px !important;
        font-size: 11px !important;
        background-color: #eeffee !important;
        color: #006600 !important;
        word-break: break-all;
    }

        .ui-menu-item div:hover {
            background-color: #eeffee;
            padding: 0px !important;
        }

.add_text {
    width: 100%;
    padding: 10px;
    background-color: #C2FFB8;
    margin: 0 auto;
}

    .add_text a {
        color: #0000ff !important;
        padding: 5px;
    }

.mandetory {
    color: #FFFFFF;
    font-family: Tahoma;
    font-size: 11px;
    font-weight: bold;
    padding-left: 2px;
}

.formNotes {
    border-top: 1px dotted #79b870;
    color: #B6F2AC;
    font-size: 10px;
    padding: 5px 5px 8px;
    text-align: right;
    background-color: #56954E !important;
}

#grid_AutoSource .ui-button, #grid_xmSourcing .ui-button, #grid_API .ui-button {
    padding: 0px !important;
}

#grid_AutoSource .ui-button-text-only .ui-button-text,
#grid_xmSourcing .ui-button-text-only .ui-button-text,
#grid_API .ui-button-text-only .ui-button-text {
    padding: 0px !important;
    text-align: left;
}



.boxContent {
    border-width: 0px;
    padding: 15px 5px 5px;
    border-bottom: solid 1px #bbbbbb;
    background-image: url(../../../App_Themes/Original/images/Nuggets/titlebar/bg.png);
    background-repeat: repeat-x;
    background-position: bottom;
    position: relative;
    border-style: solid;
    border-color: #AAE2A0;
    margin-bottom: 15px;
}

.pageTitle h3 {
    margin: 0px;
    padding: 0px;
    font-size: 22px;
    font-weight: normal;
    font-family: Lucida Sans Unicode, Arial;
    color: #000000;
}

.pageTitleItem {
    padding: 2px 0px;
    color: #218610;
    font-size: 11px;
}

    .pageTitleItem .itemTitle {
        font-weight: bold;
        padding-right: 3px;
    }

.pageTitleTopRight {
    position: absolute;
    top: 2px;
    right: 5px;
    font-size: 11px;
}

.nubButton {
    background-image: url(../../../../App_Themes/Original/images/nubs/nub.gif);
    background-repeat: no-repeat;
    background-position: left 2px;
    margin-left: 20px;
    padding-left: 13px;
    height: 12px;
    text-decoration: none;
    color: #0000ff;
}

#grid_md, #grid_xm, #grid_AutoSource, #grid_xmSourcing, .api_section, #grid_IHS, #grid_md_notes {
    border-left: 1px solid #AAE2A0 !important;
    border-right: 1px solid #AAE2A0 !important;
    border-top: none !important;
    border-bottom: 1px solid #aae2a0 !important;
    border-radius: 0px 0px 5px 5px;
    background-image: url(../../../App_Themes/Original/images/Nuggets/standard/bg.jpg) !important;
    background-position: left bottom;
    background-repeat: repeat-x;
    background-size: auto !important;
}

td .pq-ui-button ui-widget-header pq-page-first disabled {
    width: auto !important;
}

.ui-button-text-only .ui-button-text {
    padding: 0px !important;
}




.pq-last-frozen-col {
    border-bottom: 1px dotted #bbb !important;
}



.pq-body-outer .pq-cont-left {
    border-right: none !important;
}

.edit_modal {
    height: auto !important;
}

.button_disabled {
    pointer-events: none;
    cursor: default;
}

.pq-slider-icon {
    display: none !important;
}
