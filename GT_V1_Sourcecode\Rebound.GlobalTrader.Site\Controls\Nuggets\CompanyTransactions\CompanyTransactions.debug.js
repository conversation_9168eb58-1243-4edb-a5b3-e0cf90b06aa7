///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//
// RP 17.11.2009:
// - don't get data straight away because the Company Detail page now switches tabs
//   with javascript
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Nuggets");

Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyTransactions = function(element) { 
	Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyTransactions.initializeBase(this, [element]);
	this._intCompanyID = -1;
	this._aryAlreadyGotTabData = [];
	this._blnCancelAddingData = false;
	this._objTabIndexes = { 
		Requirements: 0
		, BOMs: 1
		, Quotes: 2
		, SalesOrders: 3
		, Invoices: 4
		, PurchaseOrders: 5
		, CRMAs: 6
		, SRMAs: 7
		, CreditNotes: 8
		, DebitNotes: 9 
	};
};

Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyTransactions.prototype = {

	get_intCompanyID: function() { return this._intCompanyID; }, set_intCompanyID: function(value) { if (this._intCompanyID !== value) this._intCompanyID = value; },
	get_ctlTabStrip: function() { return this._ctlTabStrip; }, 	set_ctlTabStrip: function(value) { if (this._ctlTabStrip !== value)  this._ctlTabStrip = value; }, 
	get_tblRequirements: function () { return this._tblRequirements; }, set_tblRequirements: function (value) { if (this._tblRequirements !== value) this._tblRequirements = value; },
	get_tblBOMs: function () { return this._tblBOMs; }, set_tblBOMs: function (value) { if (this._tblBOMs !== value) this._tblBOMs = value; },
	get_tblQuotes: function() { return this._tblQuotes; }, 	set_tblQuotes: function(value) { if (this._tblQuotes !== value)  this._tblQuotes = value; }, 
	get_tblSOs: function() { return this._tblSOs; }, set_tblSOs: function(value) { if (this._tblSOs !== value)  this._tblSOs = value; }, 
	get_tblPOs: function() { return this._tblPOs; }, set_tblPOs: function(value) { if (this._tblPOs !== value)  this._tblPOs = value; }, 
	get_tblInvoices: function() { return this._tblInvoices; }, 	set_tblInvoices: function(value) { if (this._tblInvoices !== value)  this._tblInvoices = value; }, 
	get_tblSRMAs: function() { return this._tblSRMAs; }, set_tblSRMAs: function(value) { if (this._tblSRMAs !== value)  this._tblSRMAs = value; }, 
	get_tblCRMAs: function() { return this._tblCRMAs; }, set_tblCRMAs: function(value) { if (this._tblCRMAs !== value)  this._tblCRMAs = value; }, 
	get_tblCreditNotes: function() { return this._tblCreditNotes; }, set_tblCreditNotes: function(value) { if (this._tblCreditNotes !== value)  this._tblCreditNotes = value; }, 
	get_tblDebitNotes: function() { return this._tblDebitNotes; }, 	set_tblDebitNotes: function(value) { if (this._tblDebitNotes !== value)  this._tblDebitNotes = value; }, 
	get_blnIncludeClosed: function() { return this._blnIncludeClosed; }, set_blnIncludeClosed: function(value) { if (this._blnIncludeClosed !== value)  this._blnIncludeClosed = value; }, 
	get_chkIncludeClosed: function() { return this._chkIncludeClosed; }, set_chkIncludeClosed: function(value) { if (this._chkIncludeClosed !== value)  this._chkIncludeClosed = value; }, 

	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyTransactions.callBaseMethod(this, "initialize");	
		
		//nugget events
		this.addRefreshEvent(Function.createDelegate(this, this.doRefresh));
		
		//data
		this._strPathToData = "controls/Nuggets/CompanyTransactions";
		this._strDataObject = "CompanyTransactions";
		
		//tab strip
		this._ctlTabStrip.addTabIndexChanged(Function.createDelegate(this, this.tabChanged));
		this._ctlTabStrip.showContent(false);
		
		//include closed checkbox
		this._chkIncludeClosed.addClick(Function.createDelegate(this, this.includeClosedChanged));

		$("#ctl00_cphMain_ctlTransactions_ctlDB_ctl06").append("<br/><br/><span style='color:red;background-color: yellow;'>Requirement tab showing upto 2 years of data only.</span>");
	},

	dispose: function() { 
		if (this.isDisposed) return;
		if (this._ctlTabStrip) this._ctlTabStrip.dispose();
		if (this._chkIncludeClosed) this._chkIncludeClosed.dispose();
		this._intCompanyID = null;
		this._aryAlreadyGotTabData = null;
		this._blnCancelAddingData = null;
		this._ctlTabStrip = null;
		this._chkIncludeClosed = null;
		this._objTabIndexes = null;
		Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyTransactions.callBaseMethod(this, "dispose");
	},
	
	initialGetData: function() {
		this.showContentLoading(false);
		this.showContent(true);
		this.tabChanged();
		this.getCounts();
	},
	
	doRefresh: function() {
		this.getCounts();
		this.getTabData();
	},
	
	getCounts: function() {
		this.showLoading(true);
		this._ctlTabStrip.resetTabTitles();
		var obj = new Rebound.GlobalTrader.Site.Data();
		obj.set_PathToData(this._strPathToData);
		obj.set_DataObject(this._strDataObject);
		obj.set_DataAction("GetCounts");
		obj.addParameter("id", this._intCompanyID);
		obj.addParameter("IncludeClosed", this._blnIncludeClosed);
		obj.addDataOK(Function.createDelegate(this, this.getCountsOK));
		obj.addError(Function.createDelegate(this, this.dataCallError));
		obj.addTimeout(Function.createDelegate(this, this.dataCallError));
		$R_DQ.addToQueue(obj);
		$R_DQ.processQueue();
		obj = null;
	},
	
	getCountsOK: function(args) {
		this.showLoading(false);
		var result = args._result;
		for (var i = 0, l = this._ctlTabStrip._aryTabIDs.length; i < l; i++) {
			var tab = $find(this._ctlTabStrip._aryTabIDs[i]);
			if (tab) {
				switch (i) {
					case this._objTabIndexes.Requirements: tab.addCountToTitle(result.Requirements); break;
					case this._objTabIndexes.BOMs: tab.addCountToTitle(result.BOMs); break;
					case this._objTabIndexes.Quotes: tab.addCountToTitle(result.Quotes); break;
					case this._objTabIndexes.SalesOrders: tab.addCountToTitle(result.SalesOrders); break;
					case this._objTabIndexes.Invoices: tab.addCountToTitle(result.Invoices); break;
					case this._objTabIndexes.PurchaseOrders: tab.addCountToTitle(result.PurchaseOrders); break;
					case this._objTabIndexes.CRMAs: tab.addCountToTitle(result.CustomerRMAs); break;
					case this._objTabIndexes.SRMAs: tab.addCountToTitle(result.SupplierRMAs); break;
					case this._objTabIndexes.CreditNotes: tab.addCountToTitle(result.CreditNotes); break;
					case this._objTabIndexes.DebitNotes: tab.addCountToTitle(result.DebitNotes); break;
				}
			}
			tab = null;
		}
	},
	
	getCurrentTable: function() {
		switch (this._ctlTabStrip._selectedTabIndex) {
			case this._objTabIndexes.Requirements: return this._tblRequirements; break;
			case this._objTabIndexes.BOMs: return this._tblBOMs; break;
			case this._objTabIndexes.Quotes: return this._tblQuotes; break;
			case this._objTabIndexes.SalesOrders: return this._tblSOs; break;
			case this._objTabIndexes.Invoices: return this._tblInvoices; break;
			case this._objTabIndexes.PurchaseOrders: return this._tblPOs; break;
			case this._objTabIndexes.CRMAs: return this._tblCRMAs; break;
			case this._objTabIndexes.SRMAs: return this._tblSRMAs; break;
			case this._objTabIndexes.CreditNotes: return this._tblCreditNotes; break;
			case this._objTabIndexes.DebitNotes: return this._tblDebitNotes; break;
		}
	},

	clearAllTables: function() {
		this._tblRequirements.clearTable();
		this._tblBOMs.clearTable();
		this._tblQuotes.clearTable();
		this._tblSOs.clearTable();
		this._tblInvoices.clearTable();
		this._tblPOs.clearTable();
		this._tblCRMAs.clearTable();
		this._tblSRMAs.clearTable();
		this._tblCreditNotes.clearTable();
		this._tblDebitNotes.clearTable();
	},

	tabChanged: function() {
		if (!this._aryAlreadyGotTabData[this._ctlTabStrip._selectedTabIndex]) this.getTabData();
		this.getCurrentTable().resizeColumns();
	},
	
	includeClosedChanged: function() {
		this._blnCancelAddingData = true; //stop any data getting added to tables
		this._blnIncludeClosed = this._chkIncludeClosed._blnChecked;
		this._aryAlreadyGotTabData = [];
		this.getCounts();
		this.clearAllTables();
		this.tabChanged();
	},
	
	getTabData: function() {
		this.showContent(true);
		switch (this._ctlTabStrip._selectedTabIndex) {
			case this._objTabIndexes.Requirements: this.getTabData_Requirements(); break;
			case this._objTabIndexes.BOMs: this.getTabData_BOMs(); break;
			case this._objTabIndexes.Quotes: this.getTabData_Quotes(); break;
			case this._objTabIndexes.SalesOrders: this.getTabData_SalesOrder(); break;
			case this._objTabIndexes.Invoices: this.getTabData_Invoice(); break;
			case this._objTabIndexes.PurchaseOrders: this.getTabData_PurchaseOrder(); break;
			case this._objTabIndexes.CRMAs: this.getTabData_CRMAs(); break;
			case this._objTabIndexes.SRMAs: this.getTabData_SRMAs(); break;
			case this._objTabIndexes.CreditNotes: this.getTabData_CreditNote(); break;
			case this._objTabIndexes.DebitNotes: this.getTabData_DebitNote(); break;
		}
	},
	
	doDataCall: function(strAction, callbackFunction) {
		this.showLoading(true);
		this._ctlTabStrip.showLoading(true);
		if (this._objData) this._objData.cancel();
		this._objData = new Rebound.GlobalTrader.Site.Data();
		this._objData.set_PathToData(this._strPathToData);
		this._objData.set_DataObject(this._strDataObject);
		this._objData.set_DataAction(strAction);
		this._objData.addParameter("id", this._intCompanyID);
		this._objData.addParameter("IncludeClosed", this._blnIncludeClosed);
		this._objData.addDataOK(callbackFunction);
		this._objData.addError(Function.createDelegate(this, this.dataCallError));
		this._objData.addTimeout(Function.createDelegate(this, this.dataCallError));
		$R_DQ.addToQueue(this._objData);
		$R_DQ.processQueue();		
	},
	
	dataCallError: function(args) {
		this.showError(true, args.get_ErrorMessage());
	},
	
	dataCallFinished: function() {
		this.showLoading(false);
		this._ctlTabStrip.showContent(true);
	},
	
	getTabData_Requirements: function() {
		this.doDataCall("GetCustomerRequirementData", Function.createDelegate(this, this.getTabDataComplete_Requirements));
	},

	getTabDataComplete_Requirements: function(args) {
		var result = args._result;
		this._tblRequirements.clearTable();
		this._blnCancelAddingData = false;
		if (result != null) {
			if (result.Items) this.addData_Requirements(result, 0);
		}
		this._tblRequirements.resizeColumns();
		this.dataCallFinished();
	},

	addData_Requirements: function (res, i) {
		if (this._blnCancelAddingData) return;
		if (i < res.Items.length) {
			var row = res.Items[i];
			var aryData = [ 
				$RGT_nubButton_CustomerRequirement(row.ID, row.No)
				, row.ReceivedDate
				, $R_FN.setCleanTextValue(row.Part)
				, row.Quantity
				, row.Price 
				];
			this._tblRequirements.addRow(aryData, row.ID, false);
			row = null;
			i += 1;
			var strID = this._element.id;
			var fn = function() { $find(strID).addData_Requirements(res, i); };
			setTimeout(fn, 0);
		} else {
			this._aryAlreadyGotTabData[this._objTabIndexes.Requirements] = true;
			this._tblRequirements.resizeColumns();
		}
	},

	getTabData_BOMs: function () {
		this.doDataCall("GetCustomerBOMsData", Function.createDelegate(this, this.getTabDataComplete_BOMs));
	},

	getTabDataComplete_BOMs: function (args) {
		var result = args._result;
		this._tblBOMs.clearTable();
		this._blnCancelAddingData = false;
		if (result != null) {
			if (result.Items) this.addData_BOMs(result, 0);
		}
		this._tblBOMs.resizeColumns();
		this.dataCallFinished();
	},

	addData_BOMs: function (res, i) {
		if (this._blnCancelAddingData) return;
		if (i < res.Items.length) {
			var row = res.Items[i];
			var aryData = [
				$RGT_nubButton_BMM(row.ID, row.BOMCode)
				, row.BOMName
				, row.ReceivedDate
				, row.Salesperson
				, row.Status
				, row.TotalValue
			];
			this._tblBOMs.addRow(aryData, row.ID, false);
			row = null;
			i += 1;
			var strID = this._element.id;
			var fn = function () { $find(strID).addData_BOMs(res, i); };
			setTimeout(fn, 0);
		} else {
			this._aryAlreadyGotTabData[this._objTabIndexes.BOMs] = true;
			this._tblBOMs.resizeColumns();
		}
	},

	getTabData_Quotes: function() {
		this.doDataCall("GetQuotesData", Function.createDelegate(this, this.getTabDataComplete_Quotes));
	},

	getTabDataComplete_Quotes: function(args) {
		var result = args._result;
		this._tblQuotes.clearTable();
		this._blnCancelAddingData = false;
		if (result != null) {
			if (result.Items) this.addData_Quotes(result, 0);
		}
		this._tblQuotes.resizeColumns();
		this.dataCallFinished();
	},

	addData_Quotes: function(res, i) {
		if (this._blnCancelAddingData) return;
		if (i < res.Items.length) {
			var row = res.Items[i];
			var aryData = [ 
				$RGT_nubButton_Quote(row.ID, row.No)
				, row.Date
				, row.Value
				, $R_FN.setCleanTextValue(row.Salesman) 
				];
			this._tblQuotes.addRow(aryData, row.ID, false);
			row = null;
			i += 1;
			var strID = this._element.id;
			var fn = function() { $find(strID).addData_Quotes(res, i); };
			setTimeout(fn, 0);
		} else {
			this._aryAlreadyGotTabData[this._objTabIndexes.Quotes] = true;
			this._tblQuotes.resizeColumns();
		}
	},
	
	getTabData_SalesOrder: function() {
		this.doDataCall("GetSalesOrderData", Function.createDelegate(this, this.getTabDataComplete_SalesOrder));
	},

	getTabDataComplete_SalesOrder: function(args) {
		var result = args._result;
		this._tblSOs.clearTable();
		this._blnCancelAddingData = false;
		if (result != null) {
			if (result.Items) this.addData_SalesOrder(result, 0);
		}
		this._tblSOs.resizeColumns();
		this.dataCallFinished();
	},

	addData_SalesOrder: function(res, i) {
		if (this._blnCancelAddingData) return;
		if (i < res.Items.length) {
			var row = res.Items[i];
			var aryData = [ 
				$RGT_nubButton_SalesOrder(row.ID, row.No)
				, row.Date
				, row.Value
				, $R_FN.setCleanTextValue(row.CustomerPO)
				, $R_FN.setCleanTextValue(row.Salesman)
				];
			this._tblSOs.addRow(aryData, row.ID, false);
			row = null;
			i += 1;
			var strID = this._element.id;
			var fn = function() { $find(strID).addData_SalesOrder(res, i); };
			setTimeout(fn, 0);
		} else {
			this._aryAlreadyGotTabData[this._objTabIndexes.SalesOrders] = true;
			this._tblSOs.resizeColumns();
		}
	},
	
	getTabData_PurchaseOrder: function() {
		this.doDataCall("GetPurchaseOrderData", Function.createDelegate(this, this.getTabDataComplete_PurchaseOrder));
	},
	
	getTabDataComplete_PurchaseOrder: function(args) {
		var result = args._result;
		this._tblPOs.clearTable();
		this._blnCancelAddingData = false;
		if (result != null) {
			if (result.Items) this.addData_PurchaseOrder(result, 0);
		}
		this._tblPOs.resizeColumns();
		this.dataCallFinished();
	},

	addData_PurchaseOrder: function(res, i) {
		if (this._blnCancelAddingData) return;
		if (i < res.Items.length) {
			var row = res.Items[i];
			var aryData = [ 
				$RGT_nubButton_PurchaseOrder(row.ID, row.No)
				, row.Date
				, row.Value
				, $R_FN.setCleanTextValue(row.Buyer) 
				];
			this._tblPOs.addRow(aryData, row.ID, false);
			row = null;
			i += 1;
			var strID = this._element.id;
			var fn = function() { $find(strID).addData_PurchaseOrder(res, i); };
			setTimeout(fn, 0);
		} else {
			this._aryAlreadyGotTabData[this._objTabIndexes.PurchaseOrders] = true;
			this._tblPOs.resizeColumns();
		}
	},
	getTabData_CRMAs: function() {
		this.doDataCall("GetCustomerRMAData", Function.createDelegate(this, this.getTabDataComplete_CRMAs));
	},

	getTabDataComplete_CRMAs: function(args) {
		var result = args._result;
		this._tblCRMAs.clearTable();
		this._blnCancelAddingData = false;
		if (result != null) {
			if (result.Items) this.addData_CRMAs(result, 0);
		}
		this._tblCRMAs.resizeColumns();
		this.dataCallFinished();
	},

	addData_CRMAs: function(res, i) {
		if (this._blnCancelAddingData) return;
		if (i < res.Items.length) {
			var row = res.Items[i];
			var aryData = [ 
				$RGT_nubButton_CRMA(row.ID, row.No)
				, row.Date
				, $RGT_nubButton_Invoice(row.InvoiceNo, row.InvoiceNumber)
				, $R_FN.setCleanTextValue(row.Authoriser)
				, $RGT_nubButton_Contact(row.ContactNo, row.Contact)
				];
			this._tblCRMAs.addRow(aryData, row.ID, false);
			row = null;
			i += 1;
			var strID = this._element.id;
			var fn = function() { $find(strID).addData_CRMAs(res, i); };
			setTimeout(fn, 0);
		} else {
			this._aryAlreadyGotTabData[this._objTabIndexes.CRMAs] = true;
			this._tblCRMAs.resizeColumns();
		}
	},

	getTabData_SRMAs: function() {
		this.doDataCall("GetSupplierRMAData", Function.createDelegate(this, this.getTabDataComplete_SRMAs));
	},

	getTabDataComplete_SRMAs: function(args) {
		var result = args._result;
		this._tblSRMAs.clearTable();
		this._blnCancelAddingData = false;
		if (result!= null) {
			if (result.Items) this.addData_SRMAs(result, 0);
		}
		this._tblSRMAs.resizeColumns();
		this.dataCallFinished();
	},

	addData_SRMAs: function(res, i) {
		if (this._blnCancelAddingData) return;
		if (i < res.Items.length) {
			var row = res.Items[i];
			var aryData = [ 
				$RGT_nubButton_SRMA(row.ID, row.No)
				, row.Date
				, $RGT_nubButton_PurchaseOrder(row.PONo, row.PONumber)
				, $R_FN.setCleanTextValue(row.Authoriser)
				, $RGT_nubButton_Contact(row.ContactNo, row.Contact)
				];
			this._tblSRMAs.addRow(aryData, row.ID, false);
			row = null;
			i += 1;
			var strID = this._element.id;
			var fn = function() { $find(strID).addData_SRMAs(res, i); };
			setTimeout(fn, 0);
		} else {
			this._aryAlreadyGotTabData[this._objTabIndexes.SRMAs] = true;
			this._tblSRMAs.resizeColumns();
		}
	},
	
	getTabData_Invoice: function() {
		this.doDataCall("GetInvoiceData", Function.createDelegate(this, this.getTabDataComplete_Invoice));
	},

	getTabDataComplete_Invoice: function(args) {
		var result = args._result;
		this._tblInvoices.clearTable();
		this._blnCancelAddingData = false;
		if (result != null) {
			if (result.Items) this.addData_Invoice(result, 0);
		}
		this._tblInvoices.resizeColumns();
		this.dataCallFinished();
	},

	addData_Invoice: function(res, i) {
		if (this._blnCancelAddingData) return;
		if (i < res.Items.length) {
			var row = res.Items[i];
			var aryData = [ 
				$RGT_nubButton_Invoice(row.ID, row.No)
				, row.Date
				, row.Value
				, $RGT_nubButton_SalesOrder(row.SONo, row.SO)
				, $R_FN.setCleanTextValue(row.CustomerPO)
				];
			this._tblInvoices.addRow(aryData, row.ID, false);
			row = null;
			i += 1;
			var strID = this._element.id;
			var fn = function() { $find(strID).addData_Invoice(res, i); };
			setTimeout(fn, 0);
		} else {
			this._aryAlreadyGotTabData[this._objTabIndexes.Invoices] = true;
			this._tblInvoices.resizeColumns();
		}
	},

	getTabData_CreditNote: function() {
		this.doDataCall("GetCreditNoteDate", Function.createDelegate(this, this.getTabDataComplete_CreditNote));
	},
	
	getTabDataComplete_CreditNote: function(args) {
		var result = args._result;
		this._tblCreditNotes.clearTable();
		this._blnCancelAddingData = false;
		if (result != null) {
			if (result.Items) this.addData_CreditNote(result, 0);
		}
		this._tblCreditNotes.resizeColumns();
		this.dataCallFinished();
	},

	addData_CreditNote: function(res, i) {
		if (this._blnCancelAddingData) return;
		if (i < res.Items.length) {
			var row = res.Items[i];
			var aryData = [ 
				$RGT_nubButton_CreditNote(row.ID, row.No)
				, row.Date
				, $RGT_nubButton_Contact(row.ContactNo, row.Contact)
				, row.Value
				, $R_FN.setCleanTextValue(row.Raiser)
				, $RGT_nubButton_SalesOrder(row.SONo, row.SO)
				];
			this._tblCreditNotes.addRow(aryData, row.ID, false);
			row = null;
			i += 1;
			var strID = this._element.id;
			var fn = function() { $find(strID).addData_CreditNote(res, i); };
			setTimeout(fn, 0);
		} else {
			this._aryAlreadyGotTabData[this._objTabIndexes.CreditNotes] = true;
			this._tblCreditNotes.resizeColumns();
		}
	},

	getTabData_DebitNote: function() {
		this.doDataCall("GetDebitNoteData", Function.createDelegate(this, this.getTabDataComplete_DebitNote));
	},
	
	getTabDataComplete_DebitNote: function(args) {
		var result = args._result;
		this._tblDebitNotes.clearTable();
		this._blnCancelAddingData = false;
		if (result != null) {
			if (result.Items) this.addData_DebitNote(result, 0);
		}
		this._tblDebitNotes.resizeColumns();
		this.dataCallFinished();
	},

	addData_DebitNote: function(res, i) {
		if (this._blnCancelAddingData) return;
		if (i < res.Items.length) {
			var row = res.Items[i];
			var aryData = [ 
				$RGT_nubButton_DebitNote(row.ID, row.No)
				, $RGT_nubButton_Contact(row.ContactNo, row.Contact)
				, row.Date
				, row.Value
				, $R_FN.setCleanTextValue(row.Raiser)
				, $RGT_nubButton_PurchaseOrder(row.PONo, row.PO)
				];
			this._tblDebitNotes.addRow(aryData, row.ID, false);
			row = null;
			i += 1;
			var strID = this._element.id;
			var fn = function() { $find(strID).addData_DebitNote(res, i); };
			setTimeout(fn, 0);
		} else {
			this._aryAlreadyGotTabData[this._objTabIndexes.DebitNotes] = true;
			this._tblDebitNotes.resizeColumns();
		}
	}

};

Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyTransactions.registerClass("Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyTransactions", Rebound.GlobalTrader.Site.Controls.Nuggets.Base);
