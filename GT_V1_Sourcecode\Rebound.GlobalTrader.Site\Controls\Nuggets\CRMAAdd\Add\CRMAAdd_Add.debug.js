///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//----------------------------------------------------------------------------------------------------------------
// RP 24.05.2010:
// - Use new default warehouse setting
//
// RP 06.01.2010:
// - fully dispose everything
//
// RP 18.12.2009:
// - allow passing a company name to initially search for (task 357)
//Marker     Changed by      Date         Remarks
//[001]      Vinay           29/05/2012   This need to implement Incoterms field is requird.
//[002]      Vinay           11/06/2012   This need to Add Incoterms field in company section
//[003]      Vinay           30/10/2012   Add link in the Invoice section to create CRMA and Credit
//[RP-2339]	Ravi		11-10-2023		AS6081 GT Documents - Show AS6081 on detail screens
//----------------------------------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.CRMAAdd_Add = function(element) { 
	Rebound.GlobalTrader.Site.Controls.Forms.CRMAAdd_Add.initializeBase(this, [element]);
	this._intNewID = 0;
	this._intCompanyID = 0;
	this._intLoginID = 0;
	this._intContactID = 0;
	this._intDivisionID = 0;
	this._strCompanyName = "";
	this._strContactName = "";
	this._strSearchCompanyName = "";
};

Rebound.GlobalTrader.Site.Controls.Forms.CRMAAdd_Add.prototype = {

    get_ctlSelectInvoice: function() { return this._ctlSelectInvoice; }, set_ctlSelectInvoice: function(v) { if (this._ctlSelectInvoice !== v) this._ctlSelectInvoice = v; },
    get_intLoginID: function() { return this._intLoginID; }, set_intLoginID: function(v) { if (this._intLoginID !== v) this._intLoginID = v; },
    get_intCompanyID: function() { return this._intCompanyID; }, set_intCompanyID: function(v) { if (this._intCompanyID !== v) this._intCompanyID = v; },
    get_strCompanyName: function() { return this._strCompanyName; }, set_strCompanyName: function(v) { if (this._strCompanyName !== v) this._strCompanyName = v; },
    get_intContactID: function() { return this._intContactID; }, set_intContactID: function(v) { if (this._intContactID !== v) this._intContactID = v; },
    get_strContactName: function() { return this._strContactName; }, set_strContactName: function(v) { if (this._strContactName !== v) this._strContactName = v; },
    get_ibtnSend: function() { return this._ibtnSend; }, set_ibtnSend: function(v) { if (this._ibtnSend !== v) this._ibtnSend = v; },
    get_ibtnSend_Footer: function() { return this._ibtnSend_Footer; }, set_ibtnSend_Footer: function(v) { if (this._ibtnSend_Footer !== v) this._ibtnSend_Footer = v; },
    get_ibtnContinue: function() { return this._ibtnContinue; }, set_ibtnContinue: function(v) { if (this._ibtnContinue !== v) this._ibtnContinue = v; },
    get_ibtnContinue_Footer: function() { return this._ibtnContinue_Footer; }, set_ibtnContinue_Footer: function(v) { if (this._ibtnContinue_Footer !== v) this._ibtnContinue_Footer = v; },
    get_strSearchCompanyName: function() { return this._strSearchCompanyName; }, set_strSearchCompanyName: function(v) { if (this._strSearchCompanyName !== v) this._strSearchCompanyName = v; },
    get_intDefaultWarehouseID: function() { return this._intDefaultWarehouseID; }, set_intDefaultWarehouseID: function(v) { if (this._intDefaultWarehouseID !== v) this._intDefaultWarehouseID = v; },
    //[003] code start
    get_intQSInvoiceID: function() { return this._intQSInvoiceID; }, set_intQSInvoiceID: function(v) { if (this._intQSInvoiceID !== v) this._intQSInvoiceID = v; },
    //[003] code end

    initialize: function() {
        Rebound.GlobalTrader.Site.Controls.Forms.CRMAAdd_Add.callBaseMethod(this, "initialize");
        this._ctlMail = $find(this.getField("ctlSendMailMessage").ID);
        this._ctlMail._ctlRelatedForm = this;
        this.addCancel(Function.createDelegate(this, this.cancelClicked));
        this.addSave(Function.createDelegate(this, this.saveClicked));
        this.addShown(Function.createDelegate(this, this.formShown));
        this._ctlMultiStep.addStepChanged(Function.createDelegate(this, this.stepChanged));
        this._ctlSelectInvoice.addItemSelected(Function.createDelegate(this, this.selectInvoice));
        this.addFieldCheckBoxClickEvent("ctlSendMail", Function.createDelegate(this, this.chooseIfSendMail));
        var fnContinue = Function.createDelegate(this, this.finishedForm);
        $R_IBTN.addClick(this._ibtnContinue, fnContinue);
        $R_IBTN.addClick(this._ibtnContinue_Footer, fnContinue);
        var fnSend = Function.createDelegate(this, this.sendMail);
        $R_IBTN.addClick(this._ibtnSend, fnSend);
        $R_IBTN.addClick(this._ibtnSend_Footer, fnSend);
        //[003] code start
        if (this._intQSInvoiceID != null && this._intQSInvoiceID > 0) {
            this.getInvoiceFromInvoiceDetail();
        }
        //[003] code end
    },

    dispose: function() {
        if (this.isDisposed) return;
        if (this._ibtnContinue) $R_IBTN.clearHandlers(this._ibtnContinue);
        if (this._ibtnContinue_Footer) $R_IBTN.clearHandlers(this._ibtnContinue_Footer);
        if (this._ibtnSend) $R_IBTN.clearHandlers(this._ibtnSend);
        if (this._ibtnSend_Footer) $R_IBTN.clearHandlers(this._ibtnSend_Footer);
        if (this._ctlMail) this._ctlMail.dispose();
        if (this._ctlSelectInvoice) this._ctlSelectInvoice.dispose();
        if (this._ctlMultiStep) this._ctlMultiStep.dispose();
        this._ctlMail = null;
        this._ctlSelectInvoice = null;
        this._ctlMultiStep = null;
        this._intLoginID = null;
        this._intCompanyID = null;
        this._strCompanyName = null;
        this._intContactID = null;
        this._strContactName = null;
        this._ibtnSend = null;
        this._ibtnSend_Footer = null;
        this._ibtnContinue = null;
        this._ibtnContinue_Footer = null;
        this._strSearchCompanyName = null;
        //[003] code start
        this._intQSInvoiceID = null;
        //[003] code end
        Rebound.GlobalTrader.Site.Controls.Forms.CRMAAdd_Add.callBaseMethod(this, "dispose");
    },

    formShown: function() {
        this.resetSteps();
        if (this._strSearchCompanyName) {
            this._ctlSelectInvoice.setFieldValue("ctlCompany", this._strSearchCompanyName);
            this._ctlSelectInvoice.searchClicked();
        } else if (this._intCompanyID > 0) {
            this.doInitialCompanySearch();
        }
        //[003] code start
        if (this._intQSInvoiceID != null && this._intQSInvoiceID > 0) {
            this.gotoStep(2);
        }
        else {
            this.gotoStep(1);
        }
        //[003] code end
    },

    doInitialCompanySearch: function() {
        if (!this._ctlSelectInvoice._initialized) setTimeout(Function.createDelegate(this, this.doInitialCompanySearch), 100);
        if (this._strCompanyName != "") this._ctlSelectInvoice.setFieldValue("ctlCompany", this._strCompanyName);
        if (this._strContactName != "") this._ctlSelectInvoice.setFieldValue("ctlContact", this._strContactName);
        if (this._strCompanyName != "" && this._strContactName != "") this._ctlSelectInvoice.getData();
    },
   
    getInvoice: function() {
        this.showInvoiceUpdateFieldsLoading(true);
        $R_FN.showElement(this._pnlLines, false);
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/InvoiceMainInfo");
        obj.set_DataObject("InvoiceMainInfo");
        obj.set_DataAction("GetData");
        obj.addParameter("ID", this._intInvoiceID);
        obj.addDataOK(Function.createDelegate(this, this.getInvoiceOK));
        obj.addError(Function.createDelegate(this, this.getInvoiceError));
        obj.addTimeout(Function.createDelegate(this, this.getInvoiceError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    getInvoiceOK: function(args) {
        var res = args._result;
        this.setFieldValue("ctlCompany", $R_FN.setCleanTextValue(res.Customer));
        this.setFieldValue("ctlShipToAddress", $R_FN.setCleanTextValue(res.BillToAddress));
        this.setFieldValue("ctlInvoiceNumber", res.InvoiceNumber);
        this.setFieldValue("ctlContact", $R_FN.setCleanTextValue(res.Contact));
        this.setFieldValue("ctlDivision", $R_FN.setCleanTextValue(res.DivisionName));
        this.setFieldValue("ctlRMADate", $R_FN.shortDate());
        this.getFieldDropDownData("ctlWarehouse");
        this.getFieldDropDownData("ctlShipVia");
        this.getFieldDropDownData("ctlAuthorisedBy");
        this.getFieldDropDownData("ctlIncoterm");
        this._intCompanyID = res.CustomerNo;
        this._intContactID = res.ContactNo;
        this._intDivisionID = res.DivisionNo;
        this.setFieldValue("ctlAuthorisedBy", this._intLoginID);
        //[002] code start
        this.setFieldValue("ctlIncoterm", res.DefaultIncotermNo);
        //[002] code end
        this.showInvoiceUpdateFieldsLoading(false);
        this.setFieldValue("ctlAS6081", res.AS6081 == true ? "Yes" : "No"); //[RP-2339]
    },

    getInvoiceError: function(args) {
        this.showInvoiceUpdateFieldsLoading(false);
        this._strErrorMessage = args._errorMessage;
        this.onSaveError();
    },

    showInvoiceUpdateFieldsLoading: function(bln) {
        this.showFieldLoading("ctlCompany", bln);
        this.showFieldLoading("ctlShipToAddress", bln);
        this.showFieldLoading("ctlInvoiceNumber", bln);
        this.showFieldLoading("ctlContact", bln);
        this.showFieldLoading("ctlDivision", bln);
        this.showFieldLoading("ctlRMADate", bln);
        this.showFieldLoading("ctlAuthorisedBy", bln);
    },

    cancelClicked: function() {
        $R_FN.navigateBack();
    },

    stepChanged: function() {
        var intStep = this._ctlMultiStep._intCurrentStep;
        $R_IBTN.showButton(this._ibtnSend, intStep == 3);
        $R_IBTN.showButton(this._ibtnSend_Footer, intStep == 3);
        $R_IBTN.enableButton(this._ibtnSave, intStep == 2);
        $R_IBTN.enableButton(this._ibtnSave_Footer, intStep == 2);
        $R_IBTN.showButton(this._ibtnSave, intStep != 3);
        $R_IBTN.showButton(this._ibtnSave_Footer, intStep != 3);
        $R_IBTN.showButton(this._ibtnCancel, intStep != 3);
        $R_IBTN.showButton(this._ibtnCancel_Footer, intStep != 3);
        $R_IBTN.showButton(this._ibtnContinue, intStep == 3);
        $R_IBTN.showButton(this._ibtnContinue_Footer, intStep == 3);
        this._ctlMultiStep.showSteps(intStep != 3);
        if (intStep == 1) this._ctlSelectInvoice.resizeColumns();
        if (intStep == 2) this.setFieldValue("ctlWarehouse", this._intDefaultWarehouseID);
        if (intStep == 3) {
            this.getMessageText();
            this.setFieldValue("ctlSendMail", false);
            this.showMailButtons();
        }
    },

    selectInvoice: function() {
        this._intInvoiceID = this._ctlSelectInvoice.getSelectedID();
        this.getInvoice();
        this.nextStep();
    },

    saveClicked: function() {
        if (!this.validateForm()) return;
        this.showSaving(true);
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/CRMAAdd");
        obj.set_DataObject("CRMAAdd");
        obj.set_DataAction("AddNew");
        obj.addParameter("AuthorisedBy", this.getFieldValue("ctlAuthorisedBy"));
        obj.addParameter("ShipViaNo", this.getFieldValue("ctlShipVia"));
        obj.addParameter("Notes", this.getFieldValue("ctlNotes"));
        obj.addParameter("Instructions", this.getFieldValue("ctlInstructions"));
        obj.addParameter("Account", this.getFieldValue("ctlAccount"));
        obj.addParameter("RMADate", this.getFieldValue("ctlRMADate"));
        obj.addParameter("InvoiceNo", this._intInvoiceID);
        obj.addParameter("WarehouseNo", this.getFieldValue("ctlWarehouse"));
        obj.addParameter("CMNo", this._intCompanyID);
        obj.addParameter("ContactNo", this._intContactID);
        obj.addParameter("DivisionNo", this._intDivisionID);
        obj.addParameter("IncotermNo", this.getFieldValue("ctlIncoterm"));
        obj.addParameter("CustomerRejectionNo", this.getFieldValue("ctlCustomerRejectionNo"));
        obj.addParameter("AS6081", this.connvertAs6081ToBoolean()); //[RP-2339]
        obj.addDataOK(Function.createDelegate(this, this.saveEditComplete));
        obj.addError(Function.createDelegate(this, this.saveEditError));
        obj.addTimeout(Function.createDelegate(this, this.saveEditError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    saveEditError: function(args) {
        this._strErrorMessage = args._errorMessage;
        this.onSaveError();
    },

    saveEditComplete: function(args) {
        if (args._result.NewID > 0) {
            this._intNewID = args._result.NewID;
            this.showSaving(false);
            this.showInnerContent(true);
            this.nextStep();
        } else {
            this._strErrorMessage = args._errorMessage;
            this.onSaveError();
        }
    },

    validateForm: function() {
        this.onValidate();
        var blnOK = true;
        if (this._ctlMultiStep._intCurrentStep == 2) {
            if (!this.checkFieldEntered("ctlWarehouse")) blnOK = false;
            if (!this.checkFieldEntered("ctlShipVia")) blnOK = false;
            if (!this.checkFieldEntered("ctlAuthorisedBy")) blnOK = false;
            if (!this.checkFieldEntered("ctlRMADate")) blnOK = false;
            //[001] code start
            if (!this.checkFieldEntered("ctlIncoterm")) blnOK = false;
            //[001] code end
        }
        if (this._ctlMultiStep._intCurrentStep == 3) {
            if (!this._ctlMail.validateFields()) blnOK = false;
        }
        if (!blnOK) this.showError(true);
        return blnOK;
    },

    showMailButtons: function() {
        var bln = this.getFieldValue("ctlSendMail");
        this.showField("ctlSendMailMessage", bln);
        $R_IBTN.showButton(this._ibtnSend, bln);
        $R_IBTN.showButton(this._ibtnSend_Footer, bln);
        $R_IBTN.showButton(this._ibtnContinue, !bln);
        $R_IBTN.showButton(this._ibtnContinue_Footer, !bln);
    },

    chooseIfSendMail: function() {
        this.showMailButtons();
    },

    getMessageText: function() {
        Rebound.GlobalTrader.Site.WebServices.GetMailMessage_NewCustomerRMA(this._intNewID, Function.createDelegate(this, this.getMessageTextComplete));
    },

    getMessageTextComplete: function(strMsg) {
        this._ctlMail.setValue_Body(strMsg);
        this._ctlMail.setValue_Subject($R_RES.NewCustomerRMAAdded);
    },

    validateMailForm: function() {
        var blnOK = this._ctlMail.validateFields();
        if (!blnOK) this.showError(true);
        return blnOK;
    },

    sendMail: function() {
        if (!this.validateMailForm()) return;
        Rebound.GlobalTrader.Site.WebServices.SendMessage($R_FN.arrayToSingleString(this._ctlMail._aryRecipientLoginIDs), $R_FN.arrayToSingleString(this._ctlMail._aryRecipientGroupIDs), this._ctlMail.getValue_Subject(), this._ctlMail.getValue_Body(), this._intNewID, Function.createDelegate(this, this.sendMailComplete));
        $R_IBTN.showButton(this._ibtnSave, false);
        $R_IBTN.showButton(this._ibtnSave_Footer, false);
        $R_IBTN.showButton(this._ibtnSend, false);
        $R_IBTN.showButton(this._ibtnSend_Footer, false);
    },

    sendMailComplete: function() {
        this.finishedForm();
    },

    finishedForm: function() {
        this._ctlMultiStep.showExplainLabel(false);
        this._ctlMultiStep.showSteps(false);
        $R_IBTN.showButton(this._ibtnSave, false);
        $R_IBTN.showButton(this._ibtnSave_Footer, false);
        $R_IBTN.showButton(this._ibtnSend, false);
        $R_IBTN.showButton(this._ibtnSend_Footer, false);
        this.showSavedOK(true);
        this.onSaveComplete();
    },
     //[003] code start
    getInvoiceFromInvoiceDetail: function() {
        this._intInvoiceID = this._intQSInvoiceID;
        this.getInvoice();
        this.nextStep();
    }
    //[003] code end
    //[RP-2339] start
    , connvertAs6081ToBoolean: function () {
        selectedvalue = this.getFieldValue("ctlAS6081");
        this._AS6081 = selectedvalue == "Yes" ? true : false;
        return this._AS6081;
    }
    //[RP-2339] end
};

Rebound.GlobalTrader.Site.Controls.Forms.CRMAAdd_Add.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.CRMAAdd_Add", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
