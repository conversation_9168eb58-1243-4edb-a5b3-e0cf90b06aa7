/*
 [RP-2339]	Ravi		11-10-2023		AS6081 GT Documents - Show AS6081 on detail screens
 */

using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;

namespace Rebound.GlobalTrader.Site.Controls.Nuggets.Data {
	[WebService(Namespace = "http://tempuri.org/")]
	[WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
	public class CRMAAdd : Rebound.GlobalTrader.Site.Data.Base {
		public override void ProcessRequest(HttpContext context) {
			if (base.init(context)) {
				switch (Action) {
					case "AddNew":AddNew();
						break;
					default:
						WriteErrorActionNotFound();
						break;
				}
			}
		}

		/// <summary>
		/// Add new customerRMA
		/// </summary>
		public void AddNew() {
			try {
				int intResult = CustomerRma.Insert(
					SessionManager.ClientID,
					GetFormValue_NullableInt("InvoiceNo"),
					GetFormValue_NullableInt("AuthorisedBy"),
					GetFormValue_NullableDateTime("RMADate", DateTime.Now),
					GetFormValue_String("Notes"),
					GetFormValue_String("Instructions"),
					GetFormValue_NullableInt("ShipViaNo"),
					GetFormValue_String("Account"),
					GetFormValue_NullableInt("WarehouseNo"),
					GetFormValue_NullableInt("CMNo"),
					GetFormValue_NullableInt("ContactNo"),
					GetFormValue_NullableInt("DivisionNo"),
                    GetFormValue_NullableInt("IncotermNo"),
                    LoginID,
                    GetFormValue_String("CustomerRejectionNo"),
					FileUploadManager.GetDocumentHeaderImageNameForAdd(GetFormValue_Int("DivisionNo"), true, (int)SessionManager.ClientID)
					, GetFormValue_NullableBoolean("AS6081",false)//[RP - 2339]
				);
				if (intResult > 0) {
					//return result
					JsonObject jsn = new JsonObject();
					jsn.AddVariable("NewID", intResult);
					OutputResult(jsn);
					jsn.Dispose();
					jsn = null;
				} else {
					WriteErrorSQLActionFailed("Insert");
				}
			} catch (Exception e) {
				WriteError(e);
			}
		}
	}
}