﻿
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE OR ALTER PROCEDURE [dbo].[usp_selectCusReqForMail]        
@BOMNo int ,                                      
@ClientID int                                                
AS            
BEGIN         
select         
cr.CustomerRequirement<PERSON><PERSON>ber,        
cr.<PERSON>erRequirementId,        
cr.Quantity,        
cr.<PERSON>er<PERSON>,        
cr.Part,        
cr.Date<PERSON>ode,        
cr.ManufacturerCode,        
cr.PackageName,        
cr.ProductName,        
cr.CompanyName,        
cr.ClientName,        
cr.DatePromised,        
dbo.ufn_convert_currency_value(cr.<PERSON>, cr.<PERSON>urrency<PERSON>o, cr.<PERSON>OM<PERSON>ur<PERSON>cyNo, cr.BOMDate) AS ConvertedTargetValue,        
cr.CurrencyNo,        
cu.CurrencyCode as BOMCurrencyCode ,        
cr.SalesmanName,        
case when cr.Obsolete=1 then 'Obsolete : Yes <br/>'  +ISNULL(cr.Instructions,'') else  'Obsolete : No <br/>' + ISNULL(cr.Instructions,'') end as Instructions,        
cr.MSL,        
cr.Factory<PERSON>ealed,        
TRD.ServiceName AS ReqTypeText,    
TRDT.ServiceName AS ReqForTraceability,
cr.AlternativesAccepted,
cr.RepeatBusiness  ,     
cr.AlternateStatus  ,
cr.Alternate      
 FROM  dbo.vwCustomerRequirement cr         
 LEFT JOIN DBO.[tbRequirementDropDownData] TRD ON cr.ReqType=TRD.Id       
 LEFT JOIN DBO.[tbRequirementDropDownData] TRDT ON cr.ReqForTraceability=TRDT.Id      
 LEFT JOIN dbo.tbCurrency cu  ON cr.CurrencyNo = cu.CurrencyId        
     
 where            
BOMNo = @BOMNo 
--and cr.ClientNo= @ClientID   
-- AND (@ClientID IS NULL        
--                                 OR (NOT @ClientID IS NULL        
--                                     AND ClientNo = @ClientID))   
end  






GO