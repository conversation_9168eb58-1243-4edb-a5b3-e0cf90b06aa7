﻿/* 
===========================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-226355]		Ngai To				25-Dec-2024		Update			226355: Quote - Apply new status matrix for the existing Quote
===========================================================================================
*/
WITH QueryQuotes
AS (
	SELECT qt.QuoteId,
		qt.QuoteNumber,
		qt.QuoteStatus,
		qs.Name AS QuoteStatusName,
		isnull(qt.closed, 0) IsQuoteClosed,
		(
			SELECT count(*)
			FROM dbo.tbQuoteLine ql
			WHERE QuoteNo = qt.QuoteId
			) AS NumOfQuoteLines,
		(
			SELECT count(*)
			FROM dbo.tbQuoteLine ql
			WHERE QuoteNo = qt.QuoteId
				AND EXISTS (
					SELECT SalesOrderLineId
					FROM [dbo].[tbSalesOrderLine]
					WHERE QuoteLineNo = ql.QuoteLineId
					)
			) AS NumOfQuoteLinesAccepted,
		(
			SELECT count(*)
			FROM dbo.tbQuoteLine ql
			WHERE QuoteNo = qt.QuoteId
				AND NOT EXISTS (
					SELECT SalesOrderLineId
					FROM [dbo].[tbSalesOrderLine]
					WHERE QuoteLineNo = ql.QuoteLineId
					)
				AND isnull(ql.SourcingResultNo, 0) > 0
			) AS NumOfQuoteLinesOffered,
		(
			SELECT count(*)
			FROM dbo.tbQuoteLine ql
			WHERE QuoteNo = qt.QuoteId
				AND isnull(ql.Closed, 0) > 0
			) AS NumOfQuoteLinesClosed
	FROM dbo.tbQuote qt
	LEFT JOIN tbQuoteStatus qs ON qt.QuoteStatus = qs.QuoteStatusId
	)
--SELECT TOP 1000 * FROM QueryQuotes ORDER BY 1 DESC
UPDATE QueryQuotes
SET QuoteStatus = CASE 
		WHEN NumOfQuoteLinesAccepted > 0
			AND NumOfQuoteLines = NumOfQuoteLinesAccepted
			THEN 2 --'Accepted'
		WHEN NumOfQuoteLinesAccepted > 0
			AND NumOfQuoteLines > NumOfQuoteLinesAccepted
			THEN 7 --'Partially Accepted'
		WHEN NumOfQuoteLinesClosed > 0
				AND NumOfQuoteLinesClosed = NumOfQuoteLines
			THEN 3 --'Declined'
		WHEN NumOfQuoteLinesClosed > 0
			AND NumOfQuoteLines > NumOfQuoteLinesClosed
			THEN 8 --'Partially Declined'
		WHEN NumOfQuoteLinesOffered > 0
			AND NumOfQuoteLines = NumOfQuoteLinesOffered
			THEN 1 --'Offered'
		WHEN NumOfQuoteLinesOffered > 0
			AND NumOfQuoteLines > NumOfQuoteLinesOffered
			THEN 6 --'Partially Offered'
		WHEN QuoteStatusName = 'Pending'
			OR QuoteStatus IS NULL
			OR QuoteStatus = 0
			THEN 5 --'New'
		ELSE QuoteStatus
		END

--Verify data:
SELECT DISTINCT qt.QuoteStatus_old, qs.Name AS QuoteStatusName, count(1) AS Row_Count
FROM tbQuote qt
LEFT JOIN tbQuoteStatus qs ON qt.QuoteStatus_old= qs.QuoteStatusId
GROUP BY qt.QuoteStatus_old, qs.Name
ORDER BY 1

SELECT DISTINCT qt.QuoteStatus, qs.Name AS QuoteStatusName, count(1) AS Row_Count
FROM tbQuote qt
LEFT JOIN tbQuoteStatus qs ON qt.QuoteStatus= qs.QuoteStatusId
GROUP BY qt.QuoteStatus, qs.Name
ORDER BY 1