using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;

namespace Rebound.GlobalTrader.Site.Controls.DropDowns.Data {
	[WebService(Namespace = "http://tempuri.org/")]
	[WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
	public class EmployeeAllClient : Rebound.GlobalTrader.Site.Data.DropDowns.Base {

		public override void ProcessRequest(HttpContext context) {
			SetDropDownType("EmployeeAllClient");
			base.ProcessRequest(context);
		}

		protected override void GetData() {
            int? intGlobalLoginClientNo;
            intGlobalLoginClientNo = GetFormValue_NullableInt("GlobalLoginClientNo");
			int intTeamNo = 0;
			int intDivisionNo = 0;
			int intExcludeLoginNo = 0;
			if (GetFormValue_Boolean("LimitToCurrentUsersTeam")) intTeamNo = (int)SessionManager.LoginTeamID;
			if (GetFormValue_Boolean("LimitToCurrentUsersDivision")) intDivisionNo = (int)SessionManager.LoginDivisionID;
			if (GetFormValue_Boolean("ExcludeCurrentUser")) intExcludeLoginNo = (int)SessionManager.LoginID;
            string strOptions = CacheManager.SerializeOptions(new object[] { (intGlobalLoginClientNo.HasValue && intGlobalLoginClientNo.Value > 0) ? intGlobalLoginClientNo.Value : SessionManager.ClientID, intTeamNo, intDivisionNo, intExcludeLoginNo });
			string strCachedData = CacheManager.GetDropDownData(_objDropDown.ID, strOptions);
			if (string.IsNullOrEmpty(strCachedData)) {
				JsonObject jsn = new JsonObject();
				JsonObject jsnList = new JsonObject(true);
                List<Login> lst = Login.DropDownForAllClient((intGlobalLoginClientNo.HasValue && intGlobalLoginClientNo.Value > 0) ? intGlobalLoginClientNo.Value : SessionManager.ClientID, intTeamNo, intDivisionNo, intExcludeLoginNo);
				for (int i = 0; i < lst.Count; i++) {
					JsonObject jsnItem = new JsonObject();
					jsnItem.AddVariable("ID", lst[i].LoginId);
					jsnItem.AddVariable("Name", lst[i].EmployeeName);
					jsnList.AddVariable(jsnItem);
					jsnItem.Dispose(); jsnItem = null;
				}
				lst.Clear(); lst = null;
				jsn.AddVariable("Employees", jsnList);
				jsnList.Dispose(); jsnList = null;
				//[GTDP-303]
				//CacheManager.StoreDropDown(_objDropDown.ID, strOptions, jsn.Result, CacheManager.CacheExpiryType.OneWorkingDay);
				OutputResult(jsn);
				jsn.Dispose(); jsn = null;
			} else {
				_context.Response.Write(strCachedData);
			}
			strCachedData = null;
		}
	}
}
