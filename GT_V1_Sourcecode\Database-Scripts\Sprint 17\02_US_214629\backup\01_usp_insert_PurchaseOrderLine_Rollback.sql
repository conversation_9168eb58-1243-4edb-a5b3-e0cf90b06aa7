﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE OR ALTER PROCEDURE [dbo].[usp_insert_PurchaseOrderLine]                                                
--******************************************************************************************                                                
--* SK 29.10.2009:                                                
--* - allow for new column - FullSupplierPart - used for searching                                                
--*                                                
--* SK 29/07/2009:                                                
--* - allow for Notes                                               
--* Marker     changed by      date         Remarks                                              
--* [001]      <PERSON><PERSON><PERSON>     19/12/2011   ESMS Ref:17 - Update manufacture with suppliername                         
--* [002]      Anand     26/08/2020   Added for pass ihs column                    
--* [003]      Abhinav <PERSON> 29/07/2021  Added new field for repeat order.    
--* [004]  <PERSON> 13/09/2023 RP-2340 (AS6081)    
--******************************************************************************************                                                
@PurchaseOrderNo  int ,                                                 
@Part     nvarchar(30) ,                                                
@ManufacturerNo   int    = Null ,                                                
@DateCode    nvarchar(5)  = Null ,                                                
@PackageNo    int    = Null ,                                                
@Quantity    int ,                                                
@Price     float ,                                                
@DeliveryDate   datetime ,                                                
@ReceivingNotes   nvarchar(MAX) = Null ,                                                
@Taxable    bit ,                                                
@ProductNo    int    = Null ,                                                
@Posted     bit ,                                                
@ShipInCost    float   = Null ,                                                
@SupplierPart   nvarchar(30) = Null ,                                                
@ROHS     tinyint =NULL,                                                
@Notes     nvarchar(2000) = Null ,                                      
@PromiseDate   datetime ,                                                          
@UpdatedBy    int    = Null ,                             
@ReqSerialNo    bit =0,                                  
@MSLLevel nvarchar(100) = null ,                           
@SupplierWarranty int = null,                                        
@PrintHazardous    bit = NULL ,                         
--[002] code start                        
--ihs data passing start                        
--@CountryOfOrigin nvarchar(50)=null,                              
@CountryOfOriginNo int=null,                              
@LifeCycleStage nvarchar(50)=null,                              
@HTSCode varchar(20)=null,                              
@AveragePrice  float=null,                              
@Packing varchar(60)=null,                              
@PackagingSize varchar(100)=null  ,                          
@Descriptions nvarchar(max)=null  ,                    
@IHSProduct nvarchar(100)=null,                     
@ECCNCode varchar(100)=null ,                       
--[002] code end              
--[003] code start             
@RepeatOrder bit=0              
--[003] code end        
, @AS6081 bit = 0 --[004]    
,@PurchaseOrderLineId int Output                                                
                                                
AS                                                
                                                
BEGIN                                                
INSERT               
INTO dbo.tbPurchaseOrderLine                                                
 (                              
  PurchaseOrderNo                                                  
 , FullPart                       
 , Part                                                    
 , ManufacturerNo            
 , DateCode                                                   
 , PackageNo                                                  
 , Quantity                                                   
 , Price                                                    
 , DeliveryDate                                       
 , ReceivingNotes                                             
 , Taxable                                                   
 , ProductNo                                                
 , Posted                                                   
 , ShipInCost                                
 , SupplierPart                                                  
 , ROHS                                                
 , Notes                                      
 , PromiseDate                                            
 , UpdatedBy                                                 
 , FullSupplierPart                             
 , ReqSerialNo                                
 , MSLLevel                             
 ,SupplierWarranty                         
 --[002] code start                        
 --ihs data passing start                      
 --,CountryOfOrigin                              
 , CountryOfOriginNo                              
 , LifeCycleStage                              
 , HTSCode                              
 , AveragePrice                              
 , Packing                              
 , PackagingSize                     
 , Descriptions                     
 ,IHSProduct                    
 ,ECCNCode                       
 --[002] code  end                             
 , PrintHazardous             
 --[003] start code               
 , RepeatOrder              
 --[003] end code      
 , AS6081 --[004]    
 )                                                
VALUES                                                
 (                                                
  @PurchaseOrderNo                                            
 , dbo.ufn_get_fullpart(@Part)                                                  
 , @Part                                                    
 , @ManufacturerNo                                                   
 , @DateCode                                        
 , @PackageNo                                                  
 , @Quantity                                                   
 , @Price                                                    
 , @DeliveryDate                                                   
 , @ReceivingNotes                                                  
 , @Taxable                                                   
 , @ProductNo                                                
 , @Posted                                                   
 , @ShipInCost                                                   
 , @SupplierPart                                                   
 , @ROHS                                                
 , @Notes                                    
 , @PromiseDate                                              
 , @UpdatedBy                                                
 , dbo.ufn_get_fullpart(@SupplierPart)                              
 , @ReqSerialNo                             
 , @MSLLevel                              
 ,@SupplierWarranty                         
 --[002] code start                        
 --ihs data passing start                        
--  ,@CountryOfOrigin                              
 , @CountryOfOriginNo                              
 , @LifeCycleStage                              
 , @HTSCode             
 , @AveragePrice             
 , @Packing                              
 , @PackagingSize                          
 , @Descriptions                      
 ,@IHSProduct                     
 ,@ECCNCode      
 --[002] code end                         
 , @PrintHazardous              
 --[003] code start              
 , @RepeatOrder             
 --[003] code end      
 , ISNULL(@AS6081,0) --[004]    
 )                                                 
                         
 SET @PurchaseOrderLineId = SCOPE_IDENTITY()                                          
--[RP-2464]  start   
    IF(SELECT COUNT(1)  
    FROM tbPurchaseOrderLine  
    WHERE PurchaseOrderNo = @PurchaseOrderNo and ISNULL(AS6081,0) = 1) > 0   
   BEGIN  
        UPDATE tbPurchaseOrder set AS6081 = 1 WHERE PurchaseOrderId = @PurchaseOrderNo;  
    END  
--[RP-2464] END                                
  --Reset the PurchaseOrderLine serial Number:                                
   EXEC usp_Update_PurchaseOrderLine_SerialNo @PurchaseOrderNo                                         
                                       
END; 
GO


