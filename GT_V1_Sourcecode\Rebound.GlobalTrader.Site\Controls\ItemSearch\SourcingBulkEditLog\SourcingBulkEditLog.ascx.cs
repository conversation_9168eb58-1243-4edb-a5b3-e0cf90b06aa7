﻿using System;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace Rebound.GlobalTrader.Site.Controls.ItemSearch
{
    public partial class SourcingBulkEditLog : Base
    {
		protected override void OnInit(EventArgs e)
		{
			base.OnInit(e);
			SetItemSearchType("SourcingBulkEditLog");
			AddScriptReference("Controls.ItemSearch.SourcingBulkEditLog.SourcingBulkEditLog.js");
		}

		protected override void OnLoad(EventArgs e)
		{
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.ItemSearch.SourcingBulkEditLog", ctlDesignBase.ClientID);
			base.OnLoad(e);
		}

		protected override void OnPreRender(EventArgs e)
		{
			ctlDesignBase.MakeChildControls();
			ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("BatchNo", Unit.Percentage(10), true));
			ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("PartNo", Unit.Percentage(20), true));
			ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("Action", Unit.Percentage(20), true));
			ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("OldValue", Unit.Percentage(10), true));
			ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("By", Unit.Percentage(20), true));
			ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("Date", Unit.Percentage(20), true));
			base.OnPreRender(e);
		}
	}
}