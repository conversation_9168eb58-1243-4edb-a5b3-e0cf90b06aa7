﻿/*
Marker     Changed by      Date          Remarks
[001]      <PERSON><PERSON><PERSON>          13/09/2014   NPR Search
 */

using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site.Controls;
namespace Rebound.GlobalTrader.Site.Controls.DataListNuggets.NPR
{
    public partial class NPR : Base
    {



        protected override void OnInit(EventArgs e)
        {
            SetDataListNuggetType("NPR");
            base.OnInit(e);
            TitleText = Functions.GetGlobalResource("Nuggets", "Npr");
            AddScriptReference("Controls.DataListNuggets.NPR.NPR");
            SetupTable();
        }


        protected override void OnLoad(EventArgs e)
        {
            _scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.DataListNuggets.NPR", ctlDesignBase.ClientID);
            base.OnLoad(e);
        }

        protected override void RenderAdditionalState()
        {
            base.RenderAdditionalState();
        }





        private void SetupTable()
        {
            _tbl.AllowSelection = false;
            _tbl.SortColumnDirection = Rebound.GlobalTrader.Site.Enumerations.SortColumnDirection.DESC;
            _tbl.Columns.Add(new FlexiDataColumn("NprNo", "PurchaseOrder", WidthManager.GetWidth(WidthManager.ColumnWidth.PartNo), true));
            _tbl.Columns.Add(new FlexiDataColumn("PartNo", "Location", WidthManager.GetWidth(WidthManager.ColumnWidth.PartNo), true));
            _tbl.Columns.Add(new FlexiDataColumn("QuantityRejected", "Price", WidthManager.GetWidth(WidthManager.ColumnWidth.CompanyName), true));
            _tbl.Columns.Add(new FlexiDataColumn("Company", WidthManager.GetWidth(WidthManager.ColumnWidth.CompanyName), true));
            _tbl.Columns.Add(new FlexiDataColumn("AuthorisedBy", "CompletedBy", WidthManager.GetWidth(WidthManager.ColumnWidth.Date), true));
            _tbl.Columns.Add(new FlexiDataColumn("NprRaisedDateFrom", "Action", Unit.Empty, true));
        }




    }
}
