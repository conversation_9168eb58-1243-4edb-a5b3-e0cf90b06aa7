
/*
Marker     changed by      date         Remarks
[001]      Abhinav       17/11/20011   ESMS Ref:25 & 34  - Virtual Stock Update & Closeing of line CRMA
[002]      Vinay           21/11/2012   Please make Rosh as a non compulsory field on the following:- Requirements,Quotes,PO,SO
[003]       Arpit           42/06/2022  For IHS Wild card symbol removal
[004]      Soorya          03/03/2023   RP-1048 Remove AI code
[005]      Soorya          03/03/2023   RP-1021 ThreadAbortException
 */
using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using System.Text;
using System.Web.Script.Serialization;
using System.Web.SessionState;
using System.Globalization;
//using Microsoft.ApplicationInsights; //[004] 

namespace Rebound.GlobalTrader.Site.Data
{
    [WebService(Namespace = "http://tempuri.org/")]
    [WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    public class Base : <PERSON><PERSON>ttp<PERSON><PERSON><PERSON>, IReadOnlySessionState
    {

        protected HttpContext _context;
        protected Site _objSite = Site.GetInstance();

        private string _strAction;
        public string Action
        {
            get { return _strAction; }
            set { _strAction = value; }
        }

        private string _strMode;
        public string Mode
        {
            get { return _strMode; }
            set { _strMode = value; }
        }

        private int _intID;
        public int ID
        {
            get { return _intID; }
            set { _intID = value; }
        }
        //[001] code start
        //this._intInvoiceLineNo
        private int _intInvoiceLineNo;
        public int InvoiceLineNo
        {
            get { return _intInvoiceLineNo; }
            set { _intInvoiceLineNo = value; }
        }
        //[001] code end
        private int _intRowCount;
        public int RowCount
        {
            get { return _intRowCount; }
            set { _intRowCount = value; }
        }

        private int _intStartRow;
        public int StartRow
        {
            get { return _intStartRow; }
            set { _intStartRow = value; }
        }

        private int _intEndRow;
        public int EndRow
        {
            get { return _intEndRow; }
            set { _intEndRow = value; }
        }

        private bool _blnCheckIfLoggedIn = true;
        public bool CheckIfLoggedIn
        {
            get { return _blnCheckIfLoggedIn; }
            set { _blnCheckIfLoggedIn = value; }
        }

        private int _intLoginID;
        public int LoginID
        {
            get { return _intLoginID; }
            set { _intLoginID = value; }
        }



        public virtual bool init(HttpContext context)
        {
            _context = context;

            //if (SessionManager.LoginID == null || SessionManager.LoginID <= 0)
            //    HttpContext.Current.Response.Redirect("ADLogin.aspx");


            //check we have a logged in session
            if (_blnCheckIfLoggedIn)
            {
                Boolean blnNoSession = false;
                if (context.Session == null)
                {
                    blnNoSession = true;
                }
                else
                {
                    blnNoSession = !SessionManager.CheckLoggedIn();
                }
                if (blnNoSession)
                {
                    WriteErrorNoSession();
                    //return false;
                }
            }

            int _intcLoginID = GetFormValue_Int("cLoginUser");
            //Log out, if user login from diferent login in other tab
            //Comment the code due to logout from system
            if (_intcLoginID > 0 && _intcLoginID != SessionManager.LoginID)
            {
                WriteErrorNoSession();
                return false;
            }

            //get some standard variables into properties for easy access
            _strAction = GetFormValue_String("action");
            _intID = GetFormValue_Int("id");
            //[001] code start
            _intInvoiceLineNo = GetFormValue_Int("invoiceLineID");
            //[001] code end
            _intStartRow = GetFormValue_Int("startrow");
            _intEndRow = GetFormValue_Int("endrow");
            _intRowCount = GetFormValue_Int("rowcount");
            _strMode = GetFormValue_String("Mode");
            _intLoginID = (int)SessionManager.LoginID;
            return true;
        }
        public virtual void ProcessRequest(HttpContext context)
        {
            //do nothing
        }

        public virtual bool IsReusable
        {
            get
            {
                return false;
            }
        }


        protected void WriteErrorActionNotFound()
        {
            WriteError(string.Format("Sorry, there was a problem with the database: Action not found - '{0}'", Action));
        }

        protected void WriteErrorDataNotFound()
        {
            WriteError("Sorry, there was a problem with the database: No data found");
        }

        protected void WriteErrorNoSession()
        {
            WriteError("LOGGED_OUT");
        }

        protected void WriteError(string strError)
        {
            JsonObject jsn = new JsonObject();
            jsn.AddVariable("Error", true);
            jsn.AddVariable("Message", strError);
            _context.Response.Write(jsn.Result);
            //[005] Start
            //_context.Response.End(); 
            this._context.ApplicationInstance.CompleteRequest();
            //[005] End
            jsn.Dispose(); jsn = null;
        }

        protected void WriteError(Exception e)
        {
            //[004] 
            //var ai = new TelemetryClient(); // or re-use an existing instance
            //ai.TrackException(e);


            string strError = e.Message;
            if (e.InnerException != null) strError += "<br/>" + e.InnerException.Message;
#if (DEBUG)
            strError += "<br />" + e.StackTrace.Replace(System.Environment.NewLine, "<br />");
#endif
            new Errorlog().LogMessage("Error occured in application " + strError.ToString());
            WriteError(strError.Replace("\r","").Replace("\n","").ToString());
        }

        protected void WriteErrorSQLActionFailed(string strType)
        {
            WriteError(String.Format("Sorry, there was a problem with the database: {0} failed", strType));
        }

        private object GetFormValue(string strIndex)
        {
            return GetFormValue(strIndex, "");
        }

        private object GetFormValue(string strIndex, object objIfNull)
        {
            object obj = objIfNull;
            if (_context.Request.Form[strIndex] != null) obj = Functions.FormatStringForDatabase(_context.Request.Form[strIndex]);
            return obj;
        }
        //[003]
        private object GetFormValue_IHS(string strIndex, object objIfNull)
        {
            object obj = objIfNull;
            if (_context.Request.Form[strIndex] != null) obj = Functions.FormatStringForDatabase_IHS(_context.Request.Form[strIndex]);
            return obj;
        }

        private object GetFormValueCrossMatch(string strIndex, object objIfNull)
        {
            object obj = objIfNull;
            if (_context.Request.Form[strIndex] != null) obj = Functions.FormatStringForDatabase(_context.Request.QueryString[strIndex]);
            return obj;
        }

        private object GetFormValueQueryint(string strIndex, object objIfNull)
        {
            object obj = objIfNull;
            if (_context.Request.QueryString[strIndex] != null) obj = Functions.FormatStringForDatabase(_context.Request.QueryString[strIndex]);
            return obj;
        }

        private object GetFormValueByte(string strIndex, object objIfNull)
        {
            object obj = objIfNull;
            if (_context.Request.QueryString[strIndex] != null) obj = Functions.FormatStringForDatabase(_context.Request.QueryString[strIndex]);
            return obj;
        }

        private object GetFormValueDubal(string strIndex, object objIfNull)
        {
            object obj = objIfNull;
            if (_context.Request.QueryString[strIndex] != null) obj = Functions.FormatStringForDatabase(_context.Request.QueryString[strIndex]);
            return obj;
        }

        private object GetQueryStringValue(string strIndex, object objIfNull)
        {
            object obj = objIfNull;
            if (_context.Request.QueryString[strIndex] != null) obj = Functions.FormatStringForDatabase(_context.Request.QueryString[strIndex]);
            return obj;
        }
        protected string GetFormValue_String(string strIndex, object objIfNull)
        {
            return GetFormValue(strIndex, objIfNull).ToString();
        }

        protected string GetFormValueCrossMatch_String(string strIndex, object objIfNull)
        {
            return GetFormValueCrossMatch(strIndex, objIfNull).ToString();
        }

        protected string GetFormValue_String(string strIndex)
        {
            object obj = GetFormValue(strIndex, null);
            if (obj == null)
            {
                return null;
            }
            else
            {
                return obj.ToString().Trim();
            }
        }
        //[003]
        protected string GetFormValueIHS_String(string strIndex)
        {
            object obj = GetFormValue_IHS(strIndex, null);
            if (obj == null)
            {
                return null;
            }
            else
            {
                return obj.ToString().Trim();
            }
        }

        protected string GetQueryStringValue_String(string strIndex)
        {
            object obj = GetQueryStringValue(strIndex, null);
            if (obj == null)
            {
                return null;
            }
            else
            {
                return obj.ToString().Trim();
            }
        }

        protected string GetFormValue_HTMLDecodedString(string strIndex)
        {
            string strVal = GetFormValue(strIndex, null) as string;
            if (string.IsNullOrEmpty(strVal))
            {
                return null;
            }
            else
            {
                return HttpUtility.HtmlDecode(strVal).Trim();
            }
        }

        protected string GetFormValue_StringForSearch(string strIndex)
        {
            string strIn = HttpUtility.UrlDecode(GetFormValue_String(strIndex));
            string strOut = strIn;
            if (strIn != null)
            {
                strOut = strIn.Replace("%", "");
                if (strIn.StartsWith("%")) strOut = string.Format("%{0}", strOut);
                if (strIn.EndsWith("%")) strOut = string.Format("{0}%", strOut);
            }
            return strOut;
        }

        protected string GetFormValue_StringNewForSearch(string strIndex)
        {
            string strIn = GetFormValue_String(strIndex);
            string strOut = null;

            if (!string.IsNullOrEmpty(strIn))
            {

                if (strIn.StartsWith("%") && strIn.EndsWith("%"))
                {
                    strIn = HttpUtility.UrlDecode(GetFormValue_String(strIndex));
                    strIn = strIn.Replace("'", "");
                    strOut = strIn.TrimStart('%').TrimEnd('%');
                    strOut = string.Format("%{0}%", strOut);
                }
                else if (strIn.StartsWith("%"))
                {
                    strIn = HttpUtility.UrlDecode(GetFormValue_String(strIndex));
                    strIn = strIn.Replace("'", "");
                    strOut = strIn.TrimStart('%');
                    strOut = string.Format("%{0}", strOut);
                }
                else if (strIn.EndsWith("%"))
                {
                    strIn = HttpUtility.UrlDecode(GetFormValue_String(strIndex));
                    strIn = strIn.Replace("'", "");
                    strOut = strIn.TrimEnd('%');
                    strOut = string.Format("{0}%", strOut);
                }
            }
            return strOut;
        }
        //protected string GetFormValue_StringForLikeSearchCompany(string strIndex)
        //{
        //    string strIn = GetFormValue_String(strIndex, "");
        //    string strOut = null;

        //    if (!string.IsNullOrEmpty(strIn))
        //    {
        //        if (strIn.StartsWith("%") && strIn.EndsWith("%"))
        //        {
        //            strOut = strIn.TrimStart('%').TrimEnd('%');
        //            strOut = Functions.RemovePunctuation(HttpUtility.UrlDecode(strOut));
        //            strOut = string.Format("%{0}%", strOut);
        //        }
        //        else if (strIn.StartsWith("%"))
        //        {
        //            strOut = strIn.TrimStart('%');
        //            strOut = Functions.RemovePunctuation(HttpUtility.UrlDecode(strOut));
        //            strOut = string.Format("%{0}", strOut);
        //        }
        //        else if (strIn.EndsWith("%"))
        //        {
        //            strOut = strIn.TrimEnd('%');
        //            strOut = Functions.RemovePunctuation(HttpUtility.UrlDecode(strOut));
        //            strOut = string.Format("{0}%", strOut);
        //        }
        //    }
        //    return strOut;
        //}
        protected string GetFormValue_StringForLikeSearch(string strIndex)
        {
            string strIn = GetFormValue_String(strIndex);
            string strOut = null;

            if (!string.IsNullOrEmpty(strIn))
            {
                strIn.Trim();
                if (strIn.StartsWith("%") && strIn.EndsWith("%"))
                {
                    strOut = strIn.TrimStart('%').TrimEnd('%');
                    strOut = HttpUtility.UrlDecode(strOut);
                    strOut = string.Format("%{0}%", strOut);
                }
                else if (strIn.StartsWith("%"))
                {
                    strOut = strIn.TrimStart('%');
                    strOut = HttpUtility.UrlDecode(strOut);
                    strOut = string.Format("%{0}", strOut);
                }
                else if (strIn.EndsWith("%"))
                {
                    strOut = strIn.TrimEnd('%');
                    strOut = HttpUtility.UrlDecode(strOut);
                    strOut = string.Format("{0}%", strOut);
                }
            }
            return strOut;
        }
        protected string GetFormValue_StringForLikeSearchCompany(string strIndex)
        {
            string strIn = GetFormValue_String(strIndex, "");
            string strOut = null;

            if (!string.IsNullOrEmpty(strIn))
            {
                if (strIn.StartsWith("%") && strIn.EndsWith("%"))
                {
                    strOut = strIn.TrimStart('%').TrimEnd('%');
                    strOut = Functions.RemovePunctuation(HttpUtility.UrlDecode(strOut));
                    strOut = string.Format("%{0}%", strOut);
                }
                else if (strIn.StartsWith("%"))
                {
                    strOut = strIn.TrimStart('%');
                    strOut = Functions.RemovePunctuation(HttpUtility.UrlDecode(strOut));
                    strOut = string.Format("%{0}", strOut);
                }
                else if (strIn.EndsWith("%"))
                {
                    strOut = strIn.TrimEnd('%');
                    strOut = Functions.RemovePunctuation(HttpUtility.UrlDecode(strOut));
                    strOut = string.Format("{0}%", strOut);
                }
            }
            return strOut;
        }
        protected string GetFormValue_StringForLikeSearch(string strIndex, bool Decode)
        {
            string strIn = GetFormValue_String(strIndex);
            string strOut = null;

            if (!string.IsNullOrEmpty(strIn))
            {
                if (strIn.StartsWith("%") && strIn.EndsWith("%"))
                {
                    strOut = strIn.TrimStart('%').TrimEnd('%');
                    strOut = HttpUtility.UrlDecode(strOut, System.Text.Encoding.Default);
                    strOut = string.Format("%{0}%", strOut);
                }
                else if (strIn.StartsWith("%"))
                {
                    strOut = strIn.TrimStart('%');
                    strOut = HttpUtility.UrlDecode(strOut, System.Text.Encoding.Default);
                    strOut = string.Format("%{0}", strOut);
                }
                else if (strIn.EndsWith("%"))
                {
                    strOut = strIn.TrimEnd('%');
                    strOut = HttpUtility.UrlDecode(strOut, System.Text.Encoding.Default);
                    strOut = string.Format("{0}%", strOut);
                }
            }
            return strOut;
        }

        protected string GetFormValue_PartForLikeSearch(string strIndex)
        {
            string strIn = GetFormValue_String(strIndex, "");
            string strOut = null;

            if (!string.IsNullOrEmpty(strIn))
            {
                if (strIn.StartsWith("%") && strIn.EndsWith("%"))
                {
                    strOut = strIn.TrimStart('%').TrimEnd('%');
                    strOut = Functions.RemovePunctuation(HttpUtility.UrlDecode(strOut));
                    strOut = string.Format("%{0}%", strOut);
                }
                else if (strIn.StartsWith("%"))
                {
                    strOut = strIn.TrimStart('%');
                    strOut = Functions.RemovePunctuation(HttpUtility.UrlDecode(strOut));
                    strOut = string.Format("%{0}", strOut);
                }
                else if (strIn.EndsWith("%"))
                {
                    strOut = strIn.TrimEnd('%');
                    strOut = Functions.RemovePunctuation(HttpUtility.UrlDecode(strOut));
                    strOut = string.Format("{0}%", strOut);
                }
            }
            return strOut;
        }


        protected string GetFormValue_PartForLikeSearchNpr(string strIndex)
        {
            string strIn = GetFormValue_String(strIndex, "");
            string strOut = null;

            if (!string.IsNullOrEmpty(strIn))
            {
                if (strIn.StartsWith("%") && strIn.EndsWith("%"))
                {
                    strOut = strIn.TrimStart('%').TrimEnd('%');
                    strOut = Functions.RemovePunctuation(HttpUtility.UrlDecode(strOut));
                    strOut = string.Format("%{0}%", strOut);
                }
                else if (strIn.StartsWith("%"))
                {
                    strOut = strIn.TrimStart('%');
                    strOut = Functions.RemovePunctuation(HttpUtility.UrlDecode(strOut));
                    strOut = string.Format("%{0}", strOut);
                }
                else if (strIn.EndsWith("%"))
                {
                    strOut = strIn.TrimEnd('%');
                    strOut = HttpUtility.UrlDecode(strOut);
                    strOut = string.Format("{0}%", strOut);
                }
            }
            return strOut;
        }

        protected int GetFormValue_Int(string strIndex)
        {
            CultureInfo ci = new CultureInfo(SessionManager.Culture);
            return Convert.ToInt32(GetFormValue(strIndex, 0), ci);
        }

        protected int GetFormValueQuery_Int(string strIndex)
        {
            CultureInfo ci = new CultureInfo(SessionManager.Culture);
            return Convert.ToInt32(GetFormValueQueryint(strIndex, 0), ci);
        }

        protected double GetFormValue_Double(string strIndex)
        {
            CultureInfo ci = new CultureInfo(SessionManager.Culture);
            return Convert.ToDouble(GetFormValue(strIndex, 0), ci);
        }

        protected double GetFormValueQuery_Double(string strIndex)
        {
            CultureInfo ci = new CultureInfo(SessionManager.Culture);
            return Convert.ToDouble(GetFormValueDubal(strIndex, 0), ci);
        }


        protected byte GetFormValue_Byte(string strIndex)
        {
            CultureInfo ci = new CultureInfo(SessionManager.Culture);
            return Convert.ToByte(GetFormValue(strIndex, 0), ci);
        }

        protected byte GetFormValueQuery_Byte(string strIndex)
        {
            CultureInfo ci = new CultureInfo(SessionManager.Culture);
            return Convert.ToByte(GetFormValueByte(strIndex, 0), ci);
        }

        protected bool GetFormValue_Boolean(string strIndex)
        {
            CultureInfo ci = new CultureInfo(SessionManager.Culture);
            return Convert.ToBoolean(GetFormValue(strIndex, false), ci);
        }

        protected DateTime GetFormValue_DateTime(string strIndex)
        {
            CultureInfo ci = new CultureInfo(SessionManager.Culture);
            return Convert.ToDateTime(GetFormValue(strIndex, DateTime.Now), ci);
        }

        protected int? GetFormValue_NullableInt(string strIndex, object objValueIfNull)
        {
            try
            {
                object obj = GetFormValue(strIndex, objValueIfNull);
                if (obj == null)
                {
                    return null;
                }
                else
                {
                    CultureInfo ci = new CultureInfo(SessionManager.Culture);
                    return (int?)Convert.ToInt32(obj, ci);
                }
            }
            catch (Exception ex)
            {
                var log = ex.Message;
                return null;
            }
            
           
            //object obj = GetFormValue(strIndex, objValueIfNull);
            //if (obj == null)
            //{
            //    return null;
            //}
            //else
            //{
            //    CultureInfo ci = new CultureInfo(SessionManager.Culture);
            //    if (obj.ToString().Length <= 10)
            //    {
            //        return (int?)Convert.ToInt32(obj, ci);
            //    }
            //    else
            //    {
            //        return (int?)Convert.ToInt64(obj, ci);
            //    }
            //}
        }

        protected int? GetQueryStringValue_NullableInt(string strIndex, object objValueIfNull)
        {
            object obj = GetQueryStringValue(strIndex, objValueIfNull);
            if (obj == null || obj.Equals(0))
            {
                return null;
            }
            else
            {
                CultureInfo ci = new CultureInfo(SessionManager.Culture);
                return (int?)Convert.ToInt32(obj, ci);
            }
        }

        protected int? GetQueryStringValue_NullableInt(string strIndex, object objValueIfNull, bool RetNullIfZero)
        {
            object obj = GetQueryStringValue(strIndex, objValueIfNull);
            if (obj == null)
            {
                return null;
            }
            else
            {
                CultureInfo ci = new CultureInfo(SessionManager.Culture);
                if (RetNullIfZero & (int?)Convert.ToInt32(obj, ci) <= 0)
                    return null;
                else
                    return (int?)Convert.ToInt32(obj, ci);
            }
        }

        protected int? GetFormValue_NullableInt(string strIndex)
        {
            return GetFormValue_NullableInt(strIndex, null);
        }

        protected int? GetQueryStringValue_NullableInt(string strIndex)
        {
            return GetQueryStringValue_NullableInt(strIndex, null);
        }

        protected double? GetFormValue_NullableDouble(string strIndex, object objValueIfNull)
        {
            object obj = GetFormValue(strIndex, objValueIfNull);
            if (obj == null)
            {
                return null;
            }
            else
            {
                CultureInfo ci = new CultureInfo(SessionManager.Culture);
                return (double?)Convert.ToDouble(obj, ci);
            }
        }

        protected double? GetFormValue_NullableDouble(string strIndex)
        {
            return GetFormValue_NullableDouble(strIndex, null);
        }

        protected DateTime? GetFormValue_NullableDateTime(string strIndex, object objValueIfNull)
        {
            object obj = GetFormValue(strIndex, objValueIfNull);
            if (obj == null)
            {
                return null;
            }
            else
            {
                CultureInfo ci = new CultureInfo(SessionManager.Culture);
                return (DateTime?)Convert.ToDateTime(obj, ci);
            }
        }

        protected DateTime? GetFormValue_NullableDateTime(string strIndex)
        {
            return GetFormValue_NullableDateTime(strIndex, null);
        }

        protected Boolean? GetFormValue_NullableBoolean(string strIndex, object objValueIfNull)
        {
            object obj = GetFormValue(strIndex, objValueIfNull);
            if (obj == null)
            {
                return null;
            }
            else
            {
                CultureInfo ci = new CultureInfo(SessionManager.Culture);
                return (Boolean?)Convert.ToBoolean(obj, ci);
            }
        }

        protected Boolean? GetQueryStringValue_NullableBoolean(string strIndex, object objValueIfNull)
        {
            object obj = GetQueryStringValue(strIndex, objValueIfNull);
            if (obj == null)
            {
                return null;
            }
            else
            {
                CultureInfo ci = new CultureInfo(SessionManager.Culture);
                return (Boolean?)Convert.ToBoolean(obj, ci);
            }
        }

        protected Boolean? GetFormValue_NullableBoolean(string strIndex)
        {
            return GetFormValue_NullableBoolean(strIndex, null);
        }

        protected string GetFormValue_StringForPartSearch(string strIndex)
        {
            string strOut = Functions.RemovePunctuationRetainingPercentSigns(GetFormValue_String(strIndex, ""));
            if (strOut == "") strOut = null;
            return strOut;
        }

        protected string GetFormValue_StringForFullPartSearch(string strIndex)
        {
            string strOut = Functions.RemovePunctuationRetainingPercentSigns(Functions.GetFullPart(HttpUtility.UrlDecode(GetFormValue_String(strIndex, ""))));
            if (strOut == "") strOut = null;
            return strOut;
        }
        protected string GetFormValue_StringForPartSearchFE(string strIndex)
        {
            string strOut = (GetFormValue_String(strIndex, ""));
            if (strOut == "") strOut = null;
            return strOut;
        }
        protected string GetFormValue_StringForNameSearch(string strIndex)
        {
            string strOut = Functions.RemovePunctuationRetainingPercentSigns(HttpUtility.UrlDecode(GetFormValue_String(strIndex, "")));
            strOut = strOut.Replace(" ", "");
            if (strOut == "") strOut = null;
            return strOut;
        }
        protected string GetFormValue_StringForNameSearchNew(string strIndex)
        {
            string strOut = Functions.RemovePunctuationRetainingPercentSignsNew(HttpUtility.UrlDecode(GetFormValue_String(strIndex, "")));
            //strOut = strOut.Replace(" ", "");
            if (strOut == "") strOut = null;
            return strOut;
        }
        protected string GetFormValueQuery_StringForNameSearch(string strIndex)
        {
            string strOut = Functions.RemovePunctuationRetainingPercentSigns(HttpUtility.UrlDecode(GetFormValue_String(strIndex, "")));
            strOut = strOut.Replace(" ", "");
            if (strOut == "") strOut = null;
            return strOut;
        }
        protected string GetFormValueQueryCrossMatch_StringForNameSearch(string strIndex)
        {
            string strOut = Functions.RemovePunctuationRetainingPercentSigns(HttpUtility.UrlDecode(GetFormValueCrossMatch_String(strIndex, "")));
            strOut = strOut.Replace(" ", "");
            if (strOut == "") strOut = null;
            return strOut;
        }


        protected string GetFormValue_StringForNameSearchDecode(string strIndex)
        {
            string strOut = Functions.RemovePunctuationRetainingPercentSigns(HttpUtility.UrlDecode(GetFormValue_String(strIndex, ""), System.Text.Encoding.Default));
            //strOut = strOut.Replace(" ", "");
            if (strOut == "") strOut = null;
            return strOut;
        }


        protected string GetFormValue_StringForNameSearchByCompanyNameDecode(string strIndex)
        {
            string strOut = Functions.RemovePunctuationRetainingPercentSignsCompanyName(HttpUtility.UrlDecode(GetFormValue_String(strIndex, ""), System.Text.Encoding.Default));
            //strOut = strOut.Replace(" ", "");
            if (strOut == "") strOut = null;
            return strOut;
        }




        protected void OutputCount(int intResult)
        {
            JsonObject jsn = new JsonObject();
            jsn.AddVariable("Value", intResult);
            OutputResult(jsn);
        }

        protected void OutputResult(JsonObject jsn)
        {
            if (jsn == null)
            {
                WriteErrorDataNotFound();
            }
            else
            {
                _context.Response.Write(jsn.Result);
            }
        }
        //[002] code start
        //Add nullable byte in database
        protected Byte? GetFormValue_NullableByte(string strIndex)
        {
            return GetFormValue_NullableByte(strIndex, null);
        }
        protected Byte? GetFormValue_NullableByte(string strIndex, object objValueIfNull)
        {
            object obj = GetFormValue(strIndex, objValueIfNull);
            if (obj == null)
            {
                return null;
            }
            else
            {
                CultureInfo ci = new CultureInfo(SessionManager.Culture);
                return (Byte?)Convert.ToByte(obj, ci);
            }
        }
        //[002] code end

        //add nullable byte for querystring
        protected Byte? GetFormValueQyery_NullableByte(string strIndex)
        {
            return GetFormValueQyery_NullableByte(strIndex, null);
        }
        protected Byte? GetFormValueQyery_NullableByte(string strIndex, object objValueIfNull)
        {
            object obj = GetFormValueByte(strIndex, objValueIfNull);
            if (obj == null)
            {
                return null;
            }
            else
            {
                CultureInfo ci = new CultureInfo(SessionManager.Culture);
                return (Byte?)Convert.ToByte(obj, ci);
            }
        }
        //end

        //[003] code start
        protected Int64? GetFormValue_NullableInt64(string strIndex, object objValueIfNull)
        {
            object obj = GetFormValue(strIndex, objValueIfNull);
            if (obj == null)
            {
                return null;
            }
            else
            {
                CultureInfo ci = new CultureInfo(SessionManager.Culture);
                return (Int64?)Convert.ToInt64(obj, ci);
            }
        }
        /// <summary>
        /// GetFormValue_ForLikeSearchWithSpecialChar -- This search method added because of searching data like 123+321 
        /// </summary>
        /// <param name="strIndex"></param>
        /// <returns></returns>
        protected string GetFormValue_ForLikeSearchWithSpecialChar(string strIndex)
        {
            string strIn = GetFormValue_String(strIndex, "");
            string strOut = null;

            if (!string.IsNullOrEmpty(strIn))
            {
                if (strIn.StartsWith("%") && strIn.EndsWith("%"))
                {
                    strOut = strIn.TrimStart('%').TrimEnd('%');
                    //strOut = Functions.RemovePunctuation(HttpUtility.UrlDecode(strOut));
                    strOut = string.Format("%{0}%", strOut);
                }
                else if (strIn.StartsWith("%"))
                {
                    strOut = strIn.TrimStart('%');
                    //strOut = Functions.RemovePunctuation(HttpUtility.UrlDecode(strOut));
                    strOut = string.Format("%{0}", strOut);
                }
                else if (strIn.EndsWith("%"))
                {
                    strOut = strIn.TrimEnd('%');
                    //strOut = Functions.RemovePunctuation(HttpUtility.UrlDecode(strOut));
                    strOut = string.Format("{0}%", strOut);
                }
            }
            return strOut;
        }
    }
}
