﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO
/* 
===========================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-222518]		An.TranTan			04-Mar-2025		Create			Insert bom csv/ bom csv log when create/update hubrfq
===========================================================================================
*/
CREATE OR ALTER PROCEDURE [dbo].[usp_insert_BOMCSV_ForHUBRFQCreation]
	@LoginID INT = 0
	,@BOMID INT = 0
	,@Status BIT = 0
	,@ImportType NVARCHAR(50) = NULL
	,@OriginalFilename NVARCHAR(1000) = NULL
	,@GeneratedFilename NVARCHAR(1000) = NULL
	,@OutputMessage NVARCHAR(2000) OUTPUT
AS
BEGIN
BEGIN TRY
	--BEGIN TRANSACTION [Tran]
	INSERT INTO dbo.tbBOMCSV
	(
		BOMNo        
		, Caption         
		, [FileName]         
		, UpdatedBy          
		, DLUP
		, ImportType
	)VALUES(@BOMId, @OriginalFilename, @GeneratedFilename, @LoginID, GETDATE(), @ImportType);
	--insert log
	DECLARE @LogMessage NVARCHAR(1000)
		, @UpdatedByName NVARCHAR(500)
		, @Action NVARCHAR(50);
	SELECT @UpdatedByName = CONCAT(FirstName, ' ', LastName) FROM dbo.tbLogin WITH(NOLOCK) WHERE LoginId = @LoginID;
	SET @Action = IIF(@ImportType = 'CreateHUBRFQ', 'create HUBRFQ', 'update HUBRFQ');

	SET @LogMessage = CONCAT('BOM Import when ', @Action, ' | File Name: ' , @OriginalFilename, ' | Imported By: ', @UpdatedByName);
	INSERT INTO dbo.[tbBomCsvLog]
	(
		[BOMNo]
		,[FileName]
		,[Status]
		,[Message]
		,[DLUP]
	)VALUES(@BOMId, @GeneratedFilename, @Status, @LogMessage, GETDATE());
	--COMMIT TRANSACTION [Tran]
	SET @OutputMessage = 'Succeed';
END TRY
BEGIN CATCH
	--ROLLBACK TRANSACTION [Tran]
	SET @OutputMessage = error_message()
END CATCH
END
GO