<%@ Control Language="C#" CodeBehind="CustomerRequirementsPrint.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.DataListNuggets.CustomerRequirementsPrint" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Nugget:DesignBase ID="ctlDB" runat="server"  BoxType="List">
	<Filters>
		<ReboundUI_DataListNugget:Filter ID="ctlFilter" runat="server" >
			<FieldsLeft>
				<%--<ReboundUI_FilterDataItemRow:Numerical id="ctlCReqNo" runat="server" ResourceTitle="CusReqNo" FilterField="CReqNo" />
				<ReboundUI_FilterDataItemRow:TextBox id="ctlPart" runat="server" ResourceTitle="PartNo" FilterField="Part" />
		        <ReboundUI_FilterDataItemRow:CheckBox id="ctlIncludeClosed" runat="server" ResourceTitle="IncludeClosed" FilterField="IncludeClosed" />
		        <ReboundUI_FilterDataItemRow:CheckBox id="ctlRecentOnly" runat="server" ResourceTitle="RecentOnly" FilterField="RecentOnly" DefaultValue="true" />--%>
		      <ReboundUI_Form:FormField id="ctlCustomer" runat="server" FieldID="cmbCustomer" ResourceTitle="Customer" IsRequiredField="true">
				<Field><ReboundUI:Combo ID="cmbCustomer" runat="server" AutoSearchControlAssembly="Rebound.GlobalTrader.Site" AutoSearchControlType="Customers" /></Field>
			</ReboundUI_Form:FormField>
		        
				<ReboundUI_FilterDataItemRow:DateSelect id="ctlReceivedDateFrom" runat="server" ResourceTitle="ReceivedDateFrom" FilterField="ReceivedDateFrom" />
				<ReboundUI_FilterDataItemRow:DateSelect id="ctlReceivedDateTo" runat="server" ResourceTitle="ReceivedDateTo" FilterField="ReceivedDateTo" />
			</FieldsLeft>
			<FieldsRight>
		     <ReboundUI_Form:FormField id="ctlContact" runat="server" FieldID="ddlContact" ResourceTitle="Contact" IsRequiredField="true">
				<Field><ReboundDropDown:ContactsForCompany ID="ddlContact" runat="server" /></Field>
			</ReboundUI_Form:FormField>
				<ReboundUI_FilterDataItemRow:DropDown id="ctlSalesman" runat="server" ResourceTitle="Salesman" DropDownType="Employee"  DropDownAssembly="Rebound.GlobalTrader.Site" FilterField="Salesman" />
			
				<ReboundUI_FilterDataItemRow:DateSelect id="ctlDatePromisedFrom" runat="server" ResourceTitle="DatePromisedFrom" FilterField="DatePromisedFrom" />
				<ReboundUI_FilterDataItemRow:DateSelect id="ctlDatePromisedTo" runat="server" ResourceTitle="DatePromisedTo" FilterField="DatePromisedTo" />
				<%--<ReboundUI_FilterDataItemRow:DateSelect id="ctlDatePromisedFrom" runat="server" ResourceTitle="DatePromisedFrom" FilterField="DatePromisedFrom" />
				<ReboundUI_FilterDataItemRow:DateSelect id="ctlDatePromisedTo" runat="server" ResourceTitle="DatePromisedTo" FilterField="DatePromisedTo" />
				<ReboundUI_FilterDataItemRow:DateSelect id="ctlReceivedDateFrom" runat="server" ResourceTitle="ReceivedDateFrom" FilterField="ReceivedDateFrom" />
				<ReboundUI_FilterDataItemRow:DateSelect id="ctlReceivedDateTo" runat="server" ResourceTitle="ReceivedDateTo" FilterField="ReceivedDateTo" />
		        <ReboundUI_FilterDataItemRow:CheckBox id="ctlPartWatch" runat="server" ResourceTitle="PartWatch" FilterField="PartWatch" />--%>
			</FieldsRight>
		</ReboundUI_DataListNugget:Filter>
	</Filters>
</ReboundUI_Nugget:DesignBase>
 