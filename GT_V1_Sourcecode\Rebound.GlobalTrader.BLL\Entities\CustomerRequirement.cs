﻿
/*

Marker     changed by      date         Remarks

[001]      Abhinav       31/05/2012   ESMS Ref:92 - Requirement Error - Urgent
[002]      <PERSON><PERSON><PERSON>   10/07/2018   [REB-12515]: show the HUBRFQ link below the customer requirement number in the customer requirement nugget of the sourcing results
[003]      <PERSON><PERSON><PERSON>   15-Oct-2018  Export all result instead of single page on HUBRFQ.
[004]      <PERSON><PERSON><PERSON>   27-Nov-2018  Show customer requirement all info in tree view.
[004]      <PERSON><PERSON><PERSON>   04-Dec-2018  [REB-13584]: Link Requirement to SO Line
[005]      <PERSON><PERSON> 17-Dec-2018  Customer Requirement Import functionality.
[006]      <PERSON><PERSON> 07-Jan-2019  Client BOM Items Details functionality.
[007]      <PERSON><PERSON> 18-Mar-2019  Showing Records Processed and Records Remaining.
[008]      <PERSON><PERSON> 20-Mar-2019  Update BomNo with HUBRFQ BomId
[009]      Ab<PERSON><PERSON> 14-July-2021 Add new function for finding part mach 
[010]      <PERSON>     17/08/2021   (GTDP-198) Controlling specific product groups for purchasing
[011]      <PERSON>     11/01/2022   (GTDP-240)  Add function to add warning message to show on all screens REQ - Quote - SO - PO related to ECCN field
[012]      Tanbirakhtar    13/06/2023    RP-37  Filter Added for industry type and Also added industry type on grid of UI
[013]      Ravi Bhushan    29-08-2023   RP-2227 AS6081  (Counterfeit Electronic Part) 
[014]      Ravi Bhushan    19-09-2023      RP-2338  AS6081 Search/Filter functionality on different pages 
[015]      Cuong DO        14-05-2024      202948  Average, Target Price, Market Leading should be reflected correctly in HUBRFQ visbility & export
 */
using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;
using Rebound.GlobalTrader.DAL;
using Rebound.GlobalTrader.BLL;

namespace Rebound.GlobalTrader.BLL
{
    public partial class CustomerRequirement : BizObject
    {

        #region Properties

        protected static DAL.CustomerRequirementElement Settings
        {
            get { return Globals.Settings.CustomerRequirements; }
        }
        public System.Boolean isIncludeAltPart { get; set; }
        /// <summary>
        /// CustomerRequirementId
        /// </summary>
        public System.Int32 CustomerRequirementId { get; set; }

        // <summary>
        /// CustomerRequirementNumber
        /// </summary>
        public System.Int32 CustomerRequirementNumber { get; set; }
        /// <summary>
        /// SupportTeamMemberNo
        /// </summary>
        public System.Int32? SupportTeamMemberNo { get; set; }
        /// <summary>
        /// SupportTeamMemberName
        /// </summary>
        public System.String SupportTeamMemberName { get; set; }
        /// <summary>
        /// ClientNo
        /// </summary>
        public System.Int32 ClientNo { get; set; }
        /// <summary>
        /// FullPart
        /// </summary>
        public System.String FullPart { get; set; }
        /// <summary>
        /// Part
        /// </summary>
        public System.String Part { get; set; }
        /// <summary>
        /// ManufacturerNo
        /// </summary>
        public System.DateTime? RequiredDate { get; set; }

        public System.String RequiredDateStatus { get; set; }


        public System.String ExpediteNotes { get; set; }
        public System.Int32? ManufacturerNo { get; set; }
        /// <summary>
        /// DateCode
        /// </summary>
        public System.String DateCode { get; set; }
        /// <summary>
        /// PackageNo
        /// </summary>
        public System.Int32? PackageNo { get; set; }
        /// <summary>
        /// Quantity
        /// </summary>
        public System.Int32 Quantity { get; set; }
        /// <summary>
        /// Price
        /// </summary>
        public System.Double Price { get; set; }
        /// <summary>
        /// CurrencyNo
        /// </summary>
        public System.Int32? CurrencyNo { get; set; }
        /// <summary>
        /// ReceivedDate
        /// </summary>
        public System.DateTime ReceivedDate { get; set; }
        /// <summary>
        /// Salesman
        /// </summary>
        public System.Int32 Salesman { get; set; }
        /// <summary>
        /// DatePromised
        /// </summary>
        public System.DateTime DatePromised { get; set; }
        /// <summary>
        /// Notes
        /// </summary>
        public System.String Notes { get; set; }
        /// <summary>
        /// Instructions
        /// </summary>
        public System.String Instructions { get; set; }
        /// <summary>
        /// Shortage
        /// </summary>
        public System.Boolean Shortage { get; set; }
        /// <summary>
        /// CompanyNo
        /// </summary>
        public System.Int32 CompanyNo { get; set; }
        /// <summary>
        /// ContactNo
        /// </summary>
        public System.Int32 ContactNo { get; set; }
        /// <summary>
        /// Alternate
        /// </summary>
        public System.Boolean Alternate { get; set; }
        /// <summary>
        /// OriginalCustomerRequirementNo
        /// </summary>
        public System.Int32? OriginalCustomerRequirementNo { get; set; }
        /// <summary>
        /// ReasonNo
        /// </summary>
        public System.Int32? ReasonNo { get; set; }
        /// <summary>
        /// ProductNo
        /// </summary>
        public System.Int32? ProductNo { get; set; }
        /// <summary>
        /// CustomerPart
        /// </summary>
        public System.String CustomerPart { get; set; }
        /// <summary>
        /// Closed
        /// </summary>
        public System.Boolean Closed { get; set; }
        /// <summary>
        /// ROHS
        /// </summary>
        public System.Byte? ROHS { get; set; }
        /// <summary>
        /// UpdatedBy
        /// </summary>
        public System.Int32? UpdatedBy { get; set; }
        /// <summary>
        /// DLUP
        /// </summary>
        public System.DateTime DLUP { get; set; }
        /// <summary>
        /// UsageNo
        /// </summary>
        public System.Int32? UsageNo { get; set; }
        /// <summary>
        /// FullCustomerPart
        /// </summary>
        public System.String FullCustomerPart { get; set; }
        /// <summary>
        /// BOM
        /// </summary>
        public System.Boolean? BOM { get; set; }
        /// <summary>
        /// BOMName
        /// </summary>
        public System.String BOMName { get; set; }
        /// <summary>
        /// PartWatch
        /// </summary>
        public System.Boolean? PartWatch { get; set; }
        /// <summary>
        /// SalesmanName
        /// </summary>
        public System.String SalesmanName { get; set; }
        /// <summary>
        /// ManufacturerCode
        /// </summary>
        public System.String ManufacturerCode { get; set; }
        /// <summary>
        /// CompanyName
        /// </summary>
        public System.String CompanyName { get; set; }
        /// <summary>
        /// ContactName
        /// </summary>
        public System.String ContactName { get; set; }
        /// <summary>
        /// RowNum
        /// </summary>
        public System.Int64? RowNum { get; set; }
        /// <summary>
        /// RowCnt
        /// </summary>
        public System.Int32? RowCnt { get; set; }
        /// <summary>
        /// CurrencyCode
        /// </summary>
        public System.String CurrencyCode { get; set; }
        /// <summary>
        /// DisplayStatus
        /// </summary>
        public System.String DisplayStatus { get; set; }
        /// <summary>
        /// DivisionNo
        /// </summary>
        public System.Int32? DivisionNo { get; set; }
        /// <summary>
        /// TeamNo
        /// </summary>
        public System.Int32? TeamNo { get; set; }
        /// <summary>
        /// CompanyOnStop
        /// </summary>
        public System.Boolean? CompanyOnStop { get; set; }
        /// <summary>
        /// CurrencyDescription
        /// </summary>
        public System.String CurrencyDescription { get; set; }
        /// <summary>
        /// ProductName
        /// </summary>
        public System.String ProductName { get; set; }
        /// <summary>
        /// ProductDescription
        /// </summary>
        public System.String ProductDescription { get; set; }
        /// <summary>
        /// ManufacturerName
        /// </summary>
        public System.String ManufacturerName { get; set; }
        /// <summary>
        /// PackageName
        /// </summary>
        public System.String PackageName { get; set; }
        /// <summary>
        /// PackageDescription
        /// </summary>
        public System.String PackageDescription { get; set; }
        /// <summary>
        /// UsageName
        /// </summary>
        public System.String UsageName { get; set; }
        /// <summary>
        /// CustomerRequirementValue
        /// </summary>
        public System.Double CustomerRequirementValue { get; set; }
        /// <summary>
        /// ClosedReason
        /// </summary>
        public System.String ClosedReason { get; set; }
        /// <summary>
        /// DivisionName
        /// </summary>
        public System.String DivisionName { get; set; }
        /// <summary>
        /// Status
        /// </summary>
        public System.String Status { get; set; }
        /// <summary>
        /// CreditLimit
        /// </summary>
        public System.Double? CreditLimit { get; set; }
        /// <summary>
        /// Balance
        /// </summary>
        public System.Double? Balance { get; set; }
        /// <summary>
        /// DaysOverdue
        /// </summary>
        public System.Int32? DaysOverdue { get; set; }
        /// <summary>
        /// ClientName
        /// </summary>
        public System.String ClientName { get; set; }
        /// <summary>
        /// Traceability
        /// </summary>
        public System.Boolean? Traceability { get; set; }

        /// <summary>
        /// BOMNo
        /// </summary>
        public System.Int32 BOMNo { get; set; }
        /// <summary>
        /// BOMHeader
        /// </summary>
        public System.String BOMHeader { get; set; }
        /// <summary>
        /// BOMCode
        /// </summary>
        public System.String BOMCode { get; set; }
        public string BOMFullName { get; set; }
        public System.String IsAs6081Required { get; set; }
        public System.String AssignedTo { get; set; }
        public System.String AssigneeId { get; set; }
        public System.Int32? POHubReleaseBy { get; set; }
        public System.Int32? RequestToPOHubBy { get; set; }
        public System.Boolean? IsPDFAvailable { get; set; }
        public System.String BOMStatus { get; set; }
        public int? SourcingResultId { get; set; }
        /// <summary>
        /// Price (from Table)
        /// </summary>
        public System.Double ConvertedTargetValue { get; set; }
        public System.String BOMCurrencyCode { get; set; }
        public int? PurchaseQuoteNumber { get; set; }
        public int? PurchaseQuoteId { get; set; }
        public System.Double PHPrice { get; set; }
        public System.String PHCurrencyCode { get; set; }
        public int? POHubCompany { get; set; }
        public System.Boolean? FactorySealed { get; set; }
        public System.String MSL { get; set; }
        public System.Int32 AllSorcingHasDelDate { get; set; }
        public int AllSorcingHasProduct { get; set; }
        public System.Boolean? AS9120 { get; set; }
        public int? SourcingResultNo { get; set; }
        public System.Int32 SourcingResult { get; set; }

        public System.Boolean? PQA { get; set; }
        public System.Boolean? Obsolete { get; set; }
        public System.Boolean? LastTimeBuy { get; set; }
        public System.Boolean? RefirbsAcceptable { get; set; }
        public System.Boolean? TestingRequired { get; set; }
        public System.Double? TargetSellPrice { get; set; }
        public System.Double? CompetitorBestOffer { get; set; }
        public System.DateTime? CustomerDecisionDate { get; set; }
        public System.DateTime? RFQClosingDate { get; set; }
        public System.Int32? QuoteValidityRequired { get; set; }
        public System.Int32? Type { get; set; }
        public System.Boolean? OrderToPlace { get; set; }
        public System.Int32? RequirementforTraceability { get; set; }
        public System.String QuoteValidityText { get; set; }
        public System.String ReqTypeText { get; set; }
        public System.String ReqForTraceabilityText { get; set; }
        public System.Boolean? IsGlobalCurrencySame { get; set; }
        public System.Boolean? HasClientSourcingResult { get; set; }
        public System.Boolean? HasHubSourcingResult { get; set; }
        public System.String EAU { get; set; }
        public System.Int32? ClientGlobalCurrencyNo { get; set; }
        public System.Int32? ReqGlobalCurrencyNo { get; set; }
        public System.String ClientCurrencyCode { get; set; }
        public System.Int32? ClientCurrencyNo { get; set; }
        public System.String ReqNotes { get; set; }
        public System.String ClientCode { get; set; }
        public Boolean? IsNoBid { get; set; }
        public System.String NoBidNotes { get; set; }
        public System.Boolean? IsCurrencyInSameFaimly { get; set; }
        public System.Boolean? AlternativesAccepted { get; set; }
        public System.Boolean? RepeatBusiness { get; set; }
        public System.DateTime? DateRequestToPOHub { get; set; }
        public System.Int32 POCurrencyNo { get; set; }
        public System.DateTime? ExpeditDate { get; set; }
        public System.Int32 UpdateByPH { get; set; }
        public System.String DutyCode { get; set; }
        public System.Double? DutyRate { get; set; }
        public System.Int32? MSLLevelNo { get; set; }
        public System.Boolean? IsProdHaz { get; set; }
        public System.Double? TotalValue { get; set; }
        public System.Double? TotalInBase { get; set; }
        /// <summary>
        /// AlternateStatus (from Table)
        /// </summary>
        public System.Byte? AlternateStatus { get; set; }
        //[004] start
        public System.Int32 ID { get; set; }
        public System.String Number { get; set; }
        public System.String ResultType { get; set; }
        //[004] end
        //[004] start
        public string SalesOrderNumber { get; set; }
        //[004] end
        //[005] start
        public System.Int32 ClientBOMId { get; set; }
        public System.String ClientBOMCode { get; set; }
        public string ClientBOMName { get; set; }
        public System.DateTime? ImportDateFrom { get; set; }
        public System.DateTime? ImportDateTo { get; set; }
        public System.DateTime? ImportDate { get; set; }
        public System.Int32 NoOfRequirements { get; set; }
        //[005] end
        //[010] start
        public System.Int32? RecordsProcessed { get; set; }
        public System.Int32? RecordsRemaining { get; set; }
        //[010] end
        public System.Int32? TotalCount { get; set; }
        /// <summary>
        /// CountryOfOrigin
        /// </summary>
        public System.String CountryOfOrigin { get; set; }
        /// <summary>
        /// CountryOfOriginNo
        /// </summary>
        public System.Int32? CountryOfOriginNo { get; set; }
        /// <summary>
        /// LifeCycleStage
        /// </summary>
        public System.String LifeCycleStage { get; set; }
        public System.String IHSProduct { get; set; }
        public System.String IHSHTSCode { get; set; }
        public System.Int32? IHSProductNo { get; set; }
        public System.String IHSProductName { get; set; }
        public System.String IHSDutyCode { get; set; }
        public System.String PurchaseRequestId { get; set; }
        public System.String PurchaseRequestNumber { get; set; }
        public System.String ECCNCode { get; set; }
        public System.Int32? ParentRequirementNo { get; set; }
        public System.Int32? ParentRequirementId { get; set; }
        public System.String IHSECCNCodeDefination { get; set; }

        /// <summary>
        /// HTSCode
        /// </summary>
        public System.String HTSCode { get; set; }
        /// <summary>
        /// AveragePrice
        /// </summary>
        public System.Double? AveragePrice { get; set; }
        /// <summary>
        /// Target Price from Lytica
        /// </summary>
        public System.Double? TargetPrice { get; set; }
        /// <summary>
        /// market leading from lytica
        /// </summary>
        public System.Double? MarketLeading { get; set; }
        /// <summary>
        /// Packaging
        /// </summary>
        public System.String Packaging { get; set; }
        /// <summary>
        /// PackagingSize
        /// </summary>
        public System.String PackagingSize { get; set; }

        public System.String Descriptions { get; set; }
        public System.Int32? IHSPartsId { get; set; }
        public System.String IHSCurrencyCode { get; set; }
        public System.String REQStatusName { get; set; }
        /// <summary>
		/// BomStatus (from Table)
		/// </summary>
		public System.String BomStatus { get; set; }
        public System.Boolean? IsOrderViaIPOonly { get; set; }
        public System.Boolean? IsRestManufaturer { get; set; }
        public System.Int32? PartEditStatus { get; set; }
        public System.Int32 IHSEditCount { get; set; }
        public System.Int32? ECCNNo { get; set; }
        public System.Boolean? AS6081 { get; set; }

        public System.Boolean? PriceIssueBuyAndSell { get; set; }
        /// <summary>
        /// IsRestrictedProduct
        /// </summary>
        public System.Boolean? IsRestrictedProduct { get; set; }
        /// <summary>
        /// ECCNNotify
        /// </summary>
        public System.Boolean? ECCNNotify { get; set; }
        /// <summary>
        /// EccnSubject
        /// </summary>
        public System.String EccnSubject { get; set; }
        /// <summary>
        /// EccnMessage
        /// </summary>
        /// 
        public System.String EccnMessage { get; set; }
        public System.String BlankECCNCode { get; set; }
        public System.String StockAvailableDetail { get; set; }
        public System.String WarningMessage { get; set; }

        public System.String StockAlerturl { get; set; }
        public System.Int32? InStock { get; set; }
        public System.Int32? OnOrder { get; set; }
        public System.Int32? Allocated { get; set; }
        public System.Int32? Available { get; set; }
        public System.String UserName { get; set; }
        public System.String ToEmail { get; set; }
        public System.Int32? stockId { get; set; }

        public System.Boolean? PartWatchHUBIPO { get; set; }

        /// <summary>
        /// ReverseLogisticid
        /// </summary>
        public System.Int32 ReverseLogisticid { get; set; }
        /// <summary>
        /// Partcount
        /// </summary>
        public System.Int32 Partcount { get; set; }

        //CodeStart[[012]] 
        /// <summary>
        /// IndustryName
        /// </summary>
        public System.String IndustryName { get; set; }

        /// <summary>
        /// IndustryType 
        /// </summary>
        public System.Int32? IndustryType { get; set; }
        //CodeEnd[[012]] 

        /// <summary>
		/// PurchasingNotes
		/// </summary>
		public System.String PurchasingNotes { get; set; }

        /// <summary>
        /// Allow Enable Kub
        /// </summary>
        public System.Boolean? IsAllowedEnableKub { get; set; }
        public System.String LyticaManufacturerRef { get; set; }
        public System.Double LyticaAveragePrice { get; set; }
        public System.Double LyticaTargetPrice { get; set; }
        public System.Double LyticaMarketLeading { get; set; }
        public System.Double IHSAveragePrice { get; set; }
        public string CompanyAdvisoryNotes { get; set; }
        public string MfrAdvisoryNotes { get; set; }
        public double? NewOfferPriceFromProspective { get; set; }
        public string CustomerRefNo { get; set; }
        #endregion


        /// <summary>
        /// CountForClient
        /// Calls [usp_count_CustomerRequirement_for_Client]
        /// </summary>
        public static Int32 CountForClient(System.Int32? clientId)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.CountForClient(clientId);
        }
        /// <summary>
        /// CountForCompany
        /// Calls [usp_count_CustomerRequirement_for_Company]
        /// </summary>
        public static Int32 CountForCompany(System.Int32? companyId, System.Boolean? includeClosed)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.CountForCompany(companyId, includeClosed);
        }       /// <summary>
                /// CountOpenForCompany
                /// Calls [usp_count_CustomerRequirement_open_for_Company]
                /// </summary>
        public static Int32 CountOpenForCompany(System.Int32? companyId)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.CountOpenForCompany(companyId);
        }       /// <summary>
                /// DataListNugget
                /// Calls [usp_datalistnugget_CustomerRequirement]

        public static List<List<object>> GetBOMListForCRList(System.Int32? BOMNo, System.Int32? clientID, System.Int32? CompanyNo)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.GetBOMListForCRList(BOMNo, clientID, CompanyNo);
        }
        /// </summary>
        //CodeStart[[012]] 
        public static List<CustomerRequirement> DataListNugget(System.Int32? clientId, System.Int32? teamId, System.Int32? divisionId, System.Int32? loginId, System.Int32? orderBy, System.Int32? sortDir, System.Int32? pageIndex, System.Int32? pageSize, System.String partSearch, System.String contactSearch, System.String cmSearch, System.Int32? salesmanNo, System.Boolean? recentOnly, System.Boolean? includeClosed, System.Int32? customerRequirementNoLo, System.Int32? customerRequirementNoHi, System.DateTime? receivedDateFrom, System.DateTime? receivedDateTo, System.DateTime? datePromisedFrom, System.DateTime? datePromisedTo, System.Boolean? partWatch, System.String bomNameSearch, System.String BOMCode, System.Double? totalLo, System.Double? totalHi, System.Int32? REQStatus, System.Int32? IndustryType, System.Boolean? AS6081, System.Int32? SelectedClientNo = null, System.Int32? SelectedLoginNo = null)
        {
            List<CustomerRequirementDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.DataListNugget(clientId, teamId, divisionId, loginId, orderBy, sortDir, pageIndex, pageSize, partSearch, contactSearch, cmSearch, salesmanNo, recentOnly, includeClosed, customerRequirementNoLo, customerRequirementNoHi, receivedDateFrom, receivedDateTo, datePromisedFrom, datePromisedTo, partWatch, bomNameSearch, BOMCode, totalLo, totalHi, REQStatus, IndustryType, AS6081, SelectedClientNo, SelectedLoginNo);
            if (lstDetails == null)
            {
                return new List<CustomerRequirement>();
            }
            else
            {
                List<CustomerRequirement> lst = new List<CustomerRequirement>();
                foreach (CustomerRequirementDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.CustomerRequirement obj = new Rebound.GlobalTrader.BLL.CustomerRequirement();
                    obj.CustomerRequirementId = objDetails.CustomerRequirementId;
                    obj.CustomerRequirementNumber = objDetails.CustomerRequirementNumber;
                    obj.Salesman = objDetails.Salesman;
                    obj.Part = objDetails.Part;
                    obj.ROHS = objDetails.ROHS;
                    obj.SalesmanName = objDetails.SalesmanName;
                    obj.ManufacturerNo = objDetails.ManufacturerNo;
                    obj.ManufacturerCode = objDetails.ManufacturerCode;
                    obj.Quantity = objDetails.Quantity;
                    obj.CompanyNo = objDetails.CompanyNo;
                    obj.CompanyName = objDetails.CompanyName;
                    obj.ContactNo = objDetails.ContactNo;
                    obj.ContactName = objDetails.ContactName;
                    obj.ReceivedDate = objDetails.ReceivedDate;
                    obj.DatePromised = objDetails.DatePromised;
                    obj.RowNum = objDetails.RowNum;
                    obj.RowCnt = objDetails.RowCnt;
                    obj.BOMCode = objDetails.BOMCode;
                    obj.BOMNo = (int)objDetails.BOMNo;
                    obj.BOMName = objDetails.BOMName;
                    obj.TotalValue = objDetails.TotalValue;
                    obj.TotalInBase = objDetails.TotalInBase;
                    obj.CurrencyNo = objDetails.CurrencyNo;
                    obj.CurrencyCode = objDetails.CurrencyCode;
                    obj.REQStatusName = objDetails.REQStatusName;
                    obj.BomStatus = objDetails.BomStatus;
                    obj.IndustryName = objDetails.IndustryName;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }

        /// Calls [usp_datalistnugget_CustomerRequirementForHUBRFQ]
        /// add start date and end date  for searching by umendra
        /// </summary>
        public static List<CustomerRequirement> DataListNuggetHUBRFQ(System.Int32? clientId, System.Int32? teamId, System.Int32? divisionId, System.Int32? loginId, System.Int32? orderBy, System.Int32? sortDir, System.Int32? pageIndex, System.Int32? pageSize, System.String BOMCode, System.String bomName, System.Boolean? isPOHub, System.Int32? selectedClientNo, int? bomStatus, int? isAssignToMe, int? assignedUser, System.String Manufacturer, System.String Part, System.Int32? intDivision, System.DateTime? startdate, System.DateTime? enddate, System.DateTime? RequiredStartdate, System.DateTime? RequiredEndDate, System.Int32? salesPerson, System.Int32? AS6081Required = 0, System.Int32? SelectedLoginId = null)
        {
            List<CustomerRequirementDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.DataListNuggetHUBRFQ(clientId, teamId, divisionId, loginId, orderBy, sortDir, pageIndex, pageSize, BOMCode, bomName, isPOHub, selectedClientNo, bomStatus, isAssignToMe, assignedUser, Manufacturer, Part, intDivision, startdate, enddate, RequiredStartdate, RequiredEndDate, salesPerson, AS6081Required, SelectedLoginId);
            if (lstDetails == null)
            {
                return new List<CustomerRequirement>();
            }
            else
            {
                List<CustomerRequirement> lst = new List<CustomerRequirement>();
                foreach (CustomerRequirementDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.CustomerRequirement obj = new Rebound.GlobalTrader.BLL.CustomerRequirement();
                    obj.CustomerRequirementId = objDetails.CustomerRequirementId;
                    obj.CustomerRequirementNumber = objDetails.CustomerRequirementNumber;
                    obj.Salesman = objDetails.Salesman;
                    obj.Part = objDetails.Part;
                    obj.ROHS = objDetails.ROHS;
                    obj.SalesmanName = objDetails.SalesmanName;
                    obj.ManufacturerNo = objDetails.ManufacturerNo;
                    obj.ManufacturerCode = objDetails.ManufacturerCode;
                    obj.Quantity = objDetails.Quantity;
                    obj.CompanyNo = objDetails.CompanyNo;
                    obj.CompanyName = objDetails.CompanyName;
                    obj.ContactNo = objDetails.ContactNo;
                    obj.ContactName = objDetails.ContactName;
                    obj.ReceivedDate = objDetails.ReceivedDate;
                    obj.DatePromised = objDetails.DatePromised;
                    obj.RowNum = objDetails.RowNum;
                    obj.RowCnt = objDetails.RowCnt;
                    obj.BOMCode = objDetails.BOMCode;
                    obj.BOMNo = (int)objDetails.BOMNo;
                    obj.BOMName = objDetails.BOMName;
                    obj.BOMStatus = objDetails.BOMStatus;
                    obj.Price = objDetails.Price;
                    obj.PHPrice = objDetails.PHPrice;
                    obj.CurrencyNo = objDetails.CurrencyNo;
                    obj.CurrencyCode = objDetails.CurrencyCode;
                    obj.DLUP = objDetails.DLUP;
                    obj.DivisionName = objDetails.DivisionName;
                    obj.AlternateStatus = objDetails.AlternateStatus;
                    obj.Alternate = objDetails.Alternate;
                    obj.POCurrencyNo = objDetails.POCurrencyNo;
                    obj.SupportTeamMemberNo = objDetails.SupportTeamMemberNo;
                    obj.SupportTeamMemberName = objDetails.SupportTeamMemberName;
                    obj.AssignedTo = objDetails.AssignedTo;
                    obj.RequiredDate = objDetails.RequiredDate;
                    obj.RequiredDateStatus = objDetails.RequiredDateStatus;
                    obj.ExpediteNotes = objDetails.ExpediteNotes;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }

        /// <summary>
        /// 
        /// [001] code start
        /// <summary>
        /// GetListForCustomerRequirement
        /// Calls [usp_selectAll_CustomerRequirement_for_BOM]
        /// </summary>
        public static List<CustomerRequirement> GetBOMListForCustomerRequirement(System.Int32? BOMNo, System.Int32? clientID, System.Boolean isPOHub = false)
        {
            List<CustomerRequirementDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.GetBOMListForCustomerRequirement(BOMNo, clientID, isPOHub);
            if (lstDetails == null)
            {
                return new List<CustomerRequirement>();
            }
            else
            {
                List<CustomerRequirement> lst = new List<CustomerRequirement>();
                foreach (CustomerRequirementDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.CustomerRequirement obj = new Rebound.GlobalTrader.BLL.CustomerRequirement();
                    obj.CustomerRequirementId = objDetails.CustomerRequirementId;
                    obj.CustomerRequirementNumber = objDetails.CustomerRequirementNumber;
                    obj.ClientNo = objDetails.ClientNo;
                    obj.FullPart = objDetails.FullPart;
                    obj.Part = objDetails.Part;
                    obj.ManufacturerNo = objDetails.ManufacturerNo;
                    obj.DateCode = objDetails.DateCode;
                    obj.PackageNo = objDetails.PackageNo;
                    obj.Quantity = objDetails.Quantity;
                    obj.Price = objDetails.Price;
                    obj.CurrencyNo = objDetails.CurrencyNo;
                    obj.ReceivedDate = objDetails.ReceivedDate;
                    obj.Salesman = objDetails.Salesman;
                    obj.DatePromised = objDetails.DatePromised;
                    obj.Notes = objDetails.Notes;
                    obj.Instructions = objDetails.Instructions;
                    obj.Shortage = objDetails.Shortage;
                    obj.CompanyNo = objDetails.CompanyNo;
                    obj.ContactNo = objDetails.ContactNo;
                    obj.Alternate = objDetails.Alternate;
                    obj.OriginalCustomerRequirementNo = objDetails.OriginalCustomerRequirementNo;
                    obj.ReasonNo = objDetails.ReasonNo;
                    obj.ProductNo = objDetails.ProductNo;
                    obj.CustomerPart = objDetails.CustomerPart;
                    obj.Closed = objDetails.Closed;
                    obj.ROHS = objDetails.ROHS;
                    obj.UpdatedBy = objDetails.UpdatedBy;
                    obj.DLUP = objDetails.DLUP;
                    obj.UsageNo = objDetails.UsageNo;
                    obj.DisplayStatus = objDetails.DisplayStatus;
                    obj.SalesmanName = objDetails.SalesmanName;
                    obj.DivisionNo = objDetails.DivisionNo;
                    obj.TeamNo = objDetails.TeamNo;
                    obj.CompanyName = objDetails.CompanyName;
                    obj.CompanyOnStop = objDetails.CompanyOnStop;
                    obj.ContactName = objDetails.ContactName;
                    obj.CurrencyCode = objDetails.CurrencyCode;
                    obj.CurrencyDescription = objDetails.CurrencyDescription;
                    obj.ProductName = objDetails.ProductName;
                    obj.ProductDescription = objDetails.ProductDescription;
                    obj.ManufacturerName = objDetails.ManufacturerName;
                    obj.ManufacturerCode = objDetails.ManufacturerCode;
                    obj.PackageName = objDetails.PackageName;
                    obj.PackageDescription = objDetails.PackageDescription;
                    obj.UsageName = objDetails.UsageName;
                    obj.CustomerRequirementValue = objDetails.CustomerRequirementValue;
                    obj.ClosedReason = objDetails.ClosedReason;
                    obj.PartWatch = objDetails.PartWatch;
                    obj.BOM = objDetails.BOM;
                    obj.BOMName = objDetails.BOMName;
                    obj.DivisionName = objDetails.DivisionName;
                    obj.BOMHeader = objDetails.BOMHeader;
                    obj.BOMNo = (int)objDetails.BOMNo;
                    obj.POHubReleaseBy = objDetails.POHubReleaseBy;
                    obj.SourcingResultId = objDetails.SourcingResultId;
                    obj.RequestToPOHubBy = objDetails.RequestToPOHubBy;
                    obj.BOMCode = objDetails.BOMCode;
                    obj.BOMFullName = objDetails.BOMFullName;
                    obj.BOMCurrencyCode = objDetails.BOMCurrencyCode;
                    obj.ConvertedTargetValue = objDetails.ConvertedTargetValue;
                    obj.PurchaseQuoteId = objDetails.PurchaseQuoteId;
                    obj.PurchaseQuoteNumber = objDetails.PurchaseQuoteNumber;
                    obj.ClientName = objDetails.ClientName;
                    obj.POHubCompany = objDetails.POHubCompany;
                    obj.FactorySealed = objDetails.FactorySealed;
                    obj.MSL = objDetails.MSL;
                    obj.AllSorcingHasDelDate = objDetails.AllSorcingHasDelDate;
                    obj.AllSorcingHasProduct = objDetails.AllSorcingHasProduct;
                    obj.SourcingResult = objDetails.SourcingResult;
                    obj.BOMStatus = objDetails.BOMStatus;
                    obj.HasClientSourcingResult = objDetails.HasClientSourcingResult;
                    obj.HasHubSourcingResult = objDetails.HasHubSourcingResult;
                    obj.IsNoBid = objDetails.IsNoBid;
                    obj.ExpeditDate = objDetails.ExpeditDate;
                    obj.UpdateByPH = objDetails.UpdateByPH;
                    obj.AlternateStatus = objDetails.AlternateStatus;
                    obj.SupportTeamMemberNo = objDetails.SupportTeamMemberNo;
                    obj.SupportTeamMemberName = objDetails.SupportTeamMemberName;
                    obj.PriceIssueBuyAndSell = objDetails.PriceIssueBuyAndSell;
                    obj.PartWatchHUBIPO = objDetails.PartWatchHUBIPO;
                    obj.IsAs6081Required = objDetails.IsAs6081Required;
                    obj.AssignedTo = objDetails.AssignedTo;
                    obj.AssigneeId = objDetails.AssigneeId;
                    obj.MfrAdvisoryNotes = objDetails.MfrAdvisoryNotes;
                    obj.CompanyAdvisoryNotes = objDetails.CompanyAdvisoryNotes;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }

        //by anand
        /// <summary>
        /// 
        /// [000] code start
        /// <summary>
        /// GetListForCustomerRequirement Cross match
        /// Calls [[usp_selectAll_CustomerRequirement_for_BOM_CorssMatchOffer]]
        /// </summary>
        public static List<CustomerRequirement> GetBOMListForCustomerRequirement_Offer(System.Int32? BOMNo, System.Int32 customerReqID, System.Int32? clientID)
        {
            List<CustomerRequirementDetails> lstDetails = SiteProvider.CustomerRequirement.GetBOMListForCustomerRequirement_Offer(BOMNo, customerReqID, clientID);
            if (lstDetails == null)
            {
                return new List<CustomerRequirement>();
            }
            else
            {
                List<CustomerRequirement> lst = new List<CustomerRequirement>();
                foreach (CustomerRequirementDetails objDetails in lstDetails)
                {
                    CustomerRequirement obj = new CustomerRequirement();
                    obj.CustomerRequirementId = objDetails.CustomerRequirementId;
                    obj.CustomerRequirementNumber = objDetails.CustomerRequirementNumber;
                    obj.ClientNo = objDetails.ClientNo;
                    obj.FullPart = objDetails.FullPart;
                    obj.Part = objDetails.Part;
                    obj.ManufacturerNo = objDetails.ManufacturerNo;
                    obj.DateCode = objDetails.DateCode;
                    obj.PackageNo = objDetails.PackageNo;
                    obj.Quantity = objDetails.Quantity;
                    obj.Price = objDetails.Price;
                    obj.CurrencyNo = objDetails.CurrencyNo;
                    obj.ReceivedDate = objDetails.ReceivedDate;
                    obj.Salesman = objDetails.Salesman;
                    obj.DatePromised = objDetails.DatePromised;
                    obj.Notes = objDetails.Notes;
                    obj.Instructions = objDetails.Instructions;
                    obj.Shortage = objDetails.Shortage;
                    obj.CompanyNo = objDetails.CompanyNo;
                    obj.ContactNo = objDetails.ContactNo;
                    obj.Alternate = objDetails.Alternate;
                    obj.OriginalCustomerRequirementNo = objDetails.OriginalCustomerRequirementNo;
                    obj.ReasonNo = objDetails.ReasonNo;
                    obj.ProductNo = objDetails.ProductNo;
                    obj.CustomerPart = objDetails.CustomerPart;
                    obj.Closed = objDetails.Closed;
                    obj.ROHS = objDetails.ROHS;
                    obj.UpdatedBy = objDetails.UpdatedBy;
                    obj.DLUP = objDetails.DLUP;
                    obj.UsageNo = objDetails.UsageNo;
                    obj.DisplayStatus = objDetails.DisplayStatus;
                    obj.SalesmanName = objDetails.SalesmanName;
                    obj.DivisionNo = objDetails.DivisionNo;
                    obj.TeamNo = objDetails.TeamNo;
                    obj.CompanyName = objDetails.CompanyName;
                    obj.CompanyOnStop = objDetails.CompanyOnStop;
                    obj.ContactName = objDetails.ContactName;
                    obj.CurrencyCode = objDetails.CurrencyCode;
                    obj.CurrencyDescription = objDetails.CurrencyDescription;
                    obj.ProductName = objDetails.ProductName;
                    obj.ProductDescription = objDetails.ProductDescription;
                    obj.ManufacturerName = objDetails.ManufacturerName;
                    obj.ManufacturerCode = objDetails.ManufacturerCode;
                    obj.PackageName = objDetails.PackageName;
                    obj.PackageDescription = objDetails.PackageDescription;
                    obj.UsageName = objDetails.UsageName;
                    obj.CustomerRequirementValue = objDetails.CustomerRequirementValue;
                    obj.ClosedReason = objDetails.ClosedReason;
                    obj.PartWatch = objDetails.PartWatch;
                    obj.BOM = objDetails.BOM;
                    obj.BOMName = objDetails.BOMName;
                    obj.DivisionName = objDetails.DivisionName;
                    obj.BOMHeader = objDetails.BOMHeader;
                    obj.BOMNo = (int)objDetails.BOMNo;
                    obj.POHubReleaseBy = objDetails.POHubReleaseBy;
                    obj.SourcingResultId = objDetails.SourcingResultId;
                    obj.RequestToPOHubBy = objDetails.RequestToPOHubBy;
                    obj.BOMCode = objDetails.BOMCode;
                    obj.BOMFullName = objDetails.BOMFullName;
                    obj.BOMCurrencyCode = objDetails.BOMCurrencyCode;
                    obj.ConvertedTargetValue = objDetails.ConvertedTargetValue;
                    obj.PurchaseQuoteId = objDetails.PurchaseQuoteId;
                    obj.PurchaseQuoteNumber = objDetails.PurchaseQuoteNumber;
                    obj.ClientName = objDetails.ClientName;
                    obj.POHubCompany = objDetails.POHubCompany;
                    obj.FactorySealed = objDetails.FactorySealed;
                    obj.MSL = objDetails.MSL;
                    obj.AllSorcingHasDelDate = objDetails.AllSorcingHasDelDate;
                    obj.AllSorcingHasProduct = objDetails.AllSorcingHasProduct;
                    obj.SourcingResult = objDetails.SourcingResult;
                    obj.BOMStatus = objDetails.BOMStatus;
                    obj.HasClientSourcingResult = objDetails.HasClientSourcingResult;
                    obj.HasHubSourcingResult = objDetails.HasHubSourcingResult;
                    obj.IsNoBid = objDetails.IsNoBid;
                    obj.ExpeditDate = objDetails.ExpeditDate;
                    obj.UpdateByPH = objDetails.UpdateByPH;
                    obj.AlternateStatus = objDetails.AlternateStatus;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }
        //end by anand
        /// <summary>

        /// BOM Release

        /// Calls [usp_update_BOM_Release]

        /// </summary>

        public static bool BOMReleaseRequirement(System.Int32? bomID, System.Int32? updatedBy)

        {

            return Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.BOMReleaseRequirement(bomID, updatedBy);

        }



        /// <summary>

        /// BOM NoBid

        /// Calls [usp_update_BOM_NoBid]

        /// </summary>

        public static bool BOMNoBidRequirement(System.Int32? bomID, System.Int32? updatedBy, string Notes)

        {

            return Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.BOMNoBidRequirement(bomID, updatedBy, Notes);

        }



        public static List<CustomerRequirement> GetHUBRFQForMail(System.Int32? BOMNo, System.Int32? clientID)

        {

            List<CustomerRequirementDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.GetHUBRFQForMail(BOMNo, clientID);

            if (lstDetails == null)

            {

                return new List<CustomerRequirement>();

            }

            else

            {

                List<CustomerRequirement> lst = new List<CustomerRequirement>();

                foreach (CustomerRequirementDetails objDetails in lstDetails)

                {

                    Rebound.GlobalTrader.BLL.CustomerRequirement obj = new Rebound.GlobalTrader.BLL.CustomerRequirement();



                    obj.CustomerRequirementId = objDetails.CustomerRequirementId;

                    obj.CustomerRequirementNumber = objDetails.CustomerRequirementNumber;

                    obj.PackageName = objDetails.PackageName;

                    obj.ProductName = objDetails.ProductName;

                    obj.Quantity = objDetails.Quantity;

                    obj.CustomerPart = objDetails.CustomerPart;

                    obj.Part = objDetails.Part;

                    obj.DateCode = objDetails.DateCode;

                    obj.ManufacturerCode = objDetails.ManufacturerCode;

                    obj.CompanyName = objDetails.CompanyName;

                    obj.SalesmanName = objDetails.SalesmanName;

                    obj.ClientName = objDetails.ClientName;

                    obj.DatePromised = objDetails.DatePromised;

                    obj.ConvertedTargetValue = objDetails.ConvertedTargetValue;

                    obj.CurrencyNo = objDetails.CurrencyNo;

                    obj.BOMCurrencyCode = objDetails.BOMCurrencyCode;

                    obj.Instructions = objDetails.Instructions;

                    obj.MSL = objDetails.MSL;

                    obj.FactorySealed = objDetails.FactorySealed;

                    obj.ReqTypeText = objDetails.ReqTypeText;

                    obj.ReqForTraceabilityText = objDetails.ReqForTraceabilityText;

                    obj.RepeatBusiness = objDetails.RepeatBusiness;

                    obj.AlternativesAccepted = objDetails.AlternativesAccepted;

                    obj.AlternateStatus = objDetails.AlternateStatus;

                    obj.Alternate = objDetails.Alternate;

                    obj.LyticaAveragePrice = objDetails.LyticaAveragePrice;

                    obj.LyticaTargetPrice = objDetails.LyticaTargetPrice;

                    obj.LyticaMarketLeading = objDetails.LyticaMarketLeading;
                    obj.LyticaManufacturerRef = objDetails.LyticaManufacturerRef;

                    lst.Add(obj);

                    obj = null;

                }

                lstDetails = null;

                return lst;

            }

        }
        //[003] start
        public static DataTable DataListNuggetHUBRFQ_Export(System.Int32? clientId, System.Int32? teamId, System.Int32? divisionId, System.Int32? loginId, System.Int32? orderBy, System.Int32? sortDir, System.String BOMCode, System.String bomName, System.Boolean? isPOHub, System.Int32? selectedClientNo, int? bomStatus, int? isAssignToMe, int? assignedUser, System.String Manufacturer, System.String Part, System.Int32? intDivision, System.DateTime? startdate, System.DateTime? enddate, System.Int32? salesPerson)
        {
            DataTable dtResult = Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.DataListNuggetHUBRFQ_Export(clientId, teamId, divisionId, loginId, orderBy, sortDir, BOMCode, bomName, isPOHub, selectedClientNo, bomStatus, isAssignToMe, assignedUser, Manufacturer, Part, intDivision, startdate, enddate, salesPerson);
            //List<CustomerRequirementDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.DataListNuggetHUBRFQ_Export(clientId, teamId, divisionId, loginId, orderBy, sortDir, BOMCode, bomName, isPOHub, selectedClientNo, bomStatus, isAssignToMe, assignedUser, Manufacturer, Part, intDivision, startdate, enddate, salesPerson);
            //if (lstDetails == null)
            //{
            //    return new List<CustomerRequirement>();
            //}
            //else
            //{
            //    List<CustomerRequirement> lst = new List<CustomerRequirement>();
            //    foreach (CustomerRequirementDetails objDetails in lstDetails)
            //    {
            //        Rebound.GlobalTrader.BLL.CustomerRequirement obj = new Rebound.GlobalTrader.BLL.CustomerRequirement();
            //        obj.CustomerRequirementId = objDetails.CustomerRequirementId;
            //        obj.CustomerRequirementNumber = objDetails.CustomerRequirementNumber;
            //        obj.Salesman = objDetails.Salesman;
            //        obj.Part = objDetails.Part;
            //        obj.ROHS = objDetails.ROHS;
            //        obj.SalesmanName = objDetails.SalesmanName;
            //        obj.ManufacturerNo = objDetails.ManufacturerNo;
            //        obj.ManufacturerCode = objDetails.ManufacturerCode;
            //        obj.Quantity = objDetails.Quantity;
            //        obj.CompanyNo = objDetails.CompanyNo;
            //        obj.CompanyName = objDetails.CompanyName;
            //        obj.ContactNo = objDetails.ContactNo;
            //        obj.ContactName = objDetails.ContactName;
            //        obj.ReceivedDate = objDetails.ReceivedDate;
            //        obj.DatePromised = objDetails.DatePromised;
            //        obj.RowNum = objDetails.RowNum;
            //        obj.RowCnt = objDetails.RowCnt;
            //        obj.BOMCode = objDetails.BOMCode;
            //        obj.BOMNo = (int)objDetails.BOMNo;
            //        obj.BOMName = objDetails.BOMName;
            //        obj.BOMStatus = objDetails.BOMStatus;
            //        obj.Price = objDetails.Price;
            //        obj.PHPrice = objDetails.PHPrice;
            //        obj.CurrencyNo = objDetails.CurrencyNo;
            //        obj.CurrencyCode = objDetails.CurrencyCode;
            //        obj.DLUP = objDetails.DLUP;
            //        obj.DivisionName = objDetails.DivisionName;
            //        lst.Add(obj);
            //        obj = null;
            //    }
            //    lstDetails = null;
            //    return lst;
            //}
            return dtResult;
        }


        /// DataListNugget for HURFQ
        /// Calls [usp_datalistnugget_CustomerRequirementForHUBRFQBOMManager]
        /// add start date and end date  for searching by umendra
        /// </summary>
        public static List<CustomerRequirement> DataListNuggetHUBRFQBOMManager(System.Int32? clientId, System.Int32? teamId, System.Int32? divisionId, System.Int32? loginId, System.Int32? orderBy, System.Int32? sortDir, System.Int32? pageIndex, System.Int32? pageSize, System.String BOMCode, System.String bomName, System.Boolean? isPOHub, System.Int32? selectedClientNo, int? bomStatus, int? isAssignToMe, int? assignedUser, System.String Manufacturer, System.String Part, System.Int32? intDivision, System.DateTime? startdate, System.DateTime? enddate, System.Int32? salesPerson, System.Int32? MailGroupId)
        {
            List<CustomerRequirementDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.DataListNuggetHUBRFQBOMManager(clientId, teamId, divisionId, loginId, orderBy, sortDir, pageIndex, pageSize, BOMCode, bomName, isPOHub, selectedClientNo, bomStatus, isAssignToMe, assignedUser, Manufacturer, Part, intDivision, startdate, enddate, salesPerson, MailGroupId);
            if (lstDetails == null)
            {
                return new List<CustomerRequirement>();
            }
            else
            {
                List<CustomerRequirement> lst = new List<CustomerRequirement>();
                foreach (CustomerRequirementDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.CustomerRequirement obj = new Rebound.GlobalTrader.BLL.CustomerRequirement();
                    obj.CustomerRequirementId = objDetails.CustomerRequirementId;
                    obj.CustomerRequirementNumber = objDetails.CustomerRequirementNumber;
                    obj.Salesman = objDetails.Salesman;
                    obj.Part = objDetails.Part;
                    obj.ROHS = objDetails.ROHS;
                    obj.SalesmanName = objDetails.SalesmanName;
                    obj.ManufacturerNo = objDetails.ManufacturerNo;
                    obj.ManufacturerCode = objDetails.ManufacturerCode;
                    obj.Quantity = objDetails.Quantity;
                    obj.CompanyNo = objDetails.CompanyNo;
                    obj.CompanyName = objDetails.CompanyName;
                    obj.ContactNo = objDetails.ContactNo;
                    obj.ContactName = objDetails.ContactName;
                    obj.ReceivedDate = objDetails.ReceivedDate;
                    obj.DatePromised = objDetails.DatePromised;
                    obj.RowNum = objDetails.RowNum;
                    obj.RowCnt = objDetails.RowCnt;
                    obj.BOMCode = objDetails.BOMCode;
                    obj.BOMNo = (int)objDetails.BOMNo;
                    obj.BOMName = objDetails.BOMName;
                    obj.BOMStatus = objDetails.BOMStatus;
                    obj.Price = objDetails.Price;
                    obj.PHPrice = objDetails.PHPrice;
                    obj.CurrencyNo = objDetails.CurrencyNo;
                    obj.CurrencyCode = objDetails.CurrencyCode;
                    obj.DLUP = objDetails.DLUP;
                    obj.DivisionName = objDetails.DivisionName;
                    obj.AlternateStatus = objDetails.AlternateStatus;
                    obj.Alternate = objDetails.Alternate;
                    obj.POCurrencyNo = objDetails.POCurrencyNo;
                    obj.SupportTeamMemberNo = objDetails.SupportTeamMemberNo;
                    obj.SupportTeamMemberName = objDetails.SupportTeamMemberName;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }

        /// <summary>
        /// 
        /// [001] code start
        /// <summary>
        /// GetListForCustomerRequirement
        /// Calls [usp_selectAll_CustomerRequirement_for_BOM]
        /// </summary>
        public static List<CustomerRequirement> GetHUBRFQReqNos(String ReqIds, System.Int32? clientID)
        {
            List<CustomerRequirementDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.GetHUBRFQReqNos(ReqIds, clientID);
            if (lstDetails == null)
            {
                return new List<CustomerRequirement>();
            }
            else
            {
                List<CustomerRequirement> lst = new List<CustomerRequirement>();
                foreach (CustomerRequirementDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.CustomerRequirement obj = new Rebound.GlobalTrader.BLL.CustomerRequirement();
                    obj.CustomerRequirementId = objDetails.CustomerRequirementId;
                    obj.CustomerRequirementNumber = objDetails.CustomerRequirementNumber;
                    obj.BOMHeader = objDetails.BOMHeader;


                    obj.RequestToPOHubBy = objDetails.RequestToPOHubBy;
                    obj.BOMFullName = objDetails.BOMFullName;
                    obj.BOMCode = objDetails.BOMCode;

                    obj.FullPart = objDetails.FullPart;
                    obj.Part = objDetails.Part;
                    obj.ManufacturerNo = objDetails.ManufacturerNo;
                    obj.Salesman = objDetails.Salesman;




                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }
        /// NoBid 
        /// Calls [usp_update_CustomerRequirement_NoBid]
        /// </summary>
        public static bool NoBidRequirement(System.Int32? customerRequirementId, System.Int32? updatedBy, System.Int32? bomID, string Notes)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.NoBidRequirement(customerRequirementId, updatedBy, bomID, Notes);
        }

        /// <summary>
        /// Release 
        /// Calls [usp_update_CustomerRequirement_Release]
        /// </summary>
        public static bool ReleaseRequirement(System.Int32? customerRequirementId, System.Int32? updatedBy, System.Int32? bomID)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.ReleaseRequirement(customerRequirementId, updatedBy, bomID);
        }

        /// <summary>
        /// delete 
        /// Calls [usp_delete_CustomerRequirement_Bom]
        /// </summary>
        public static bool DeleteBomItem(int? bomId, int? requirementId, System.Int32? LoginID, System.Int32? clientId)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.DeleteBomItem(bomId, requirementId, LoginID, clientId);
        }

        /// <summary>
        /// delete 
        /// Calls [usp_UnRelease_CustomerRequirement_Bom]
        /// </summary>
        public static bool UnReleaseBomItem(int? bomId, int? requirementId)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.UnReleaseBomItem(bomId, requirementId);
        }

        ///Recall NoBid 
        /// Calls [usp_update_CustomerRequirement_RecallNoBid]
        /// </summary>
        public static bool RecallNoBidRequirement(System.Int32? customerRequirementId, System.Int32? updatedBy)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.RecallNoBidRequirement(customerRequirementId, updatedBy);
        }
        public static void SaveExcelHeader(string columnList, string insertColumnList, int bomId, int loginId)
        {
            Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.SaveExcelHeader(columnList, insertColumnList, bomId, loginId);
        }

        public static void UpdateCell(DataTable dt, int userid)
        {
            Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.UpdateCell(dt, userid);
        }

        public static bool IsCompanyMappingExists(int bomId)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.IsCompanyMappingExists(bomId);
        }

        public static void UpdateCompanyMapping(int bomId, int userId)
        {
            Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.UpdateCompanyMapping(bomId, userId);
        }

        public static void ReArrangeCompanyMapping(int columnIndex, int columnId, int ClientBOMId, int userId)
        {
            Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.ReArrangeCompanyMapping(columnIndex, columnId, ClientBOMId, userId);
        }

        public static void RearrangeColumnData(int bomId, int userId, string insertColumnList, string selectColumnList)
        {
            Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.RearrangeColumnData(bomId, userId, insertColumnList, selectColumnList);
        }
        public static List<PDFDocument> GetBomUploadedFiles(int bomId)
        {
            List<PDFDocumentDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.GetBomUploadedFiles(bomId);
            if (lstDetails == null)
            {
                return new List<PDFDocument>();
            }
            else
            {
                List<PDFDocument> lst = new List<PDFDocument>();
                foreach (PDFDocumentDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.PDFDocument obj = new Rebound.GlobalTrader.BLL.PDFDocument();
                    obj.PDFDocumentId = objDetails.PDFDocumentId;
                    //obj.PDFDocumentRefNo = objDetails.PDFDocumentRefNo;
                    obj.GeneratedFileName = objDetails.GeneratedFileName;
                    obj.FileName = objDetails.FileName;
                    obj.UpdatedBy = objDetails.UpdatedBy;
                    obj.DLUP = objDetails.DLUP;
                    obj.Caption = objDetails.Caption;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }
        public static DataTable DataListNuggetIHS_Export(System.Int32? clientId, System.Int32? orderBy, System.Int32? sortDir, System.Int32? pageIndex, System.Int32? pageSize, System.String partSearch, System.String MfrSearch, System.Int32? countryOforigin, System.String MSL, System.String HtcCode, System.String Description, System.Boolean? recentOnly, int? IsPoHub)
        {
            DataTable dtResult = Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.DataListNuggetIHS_Export(clientId, orderBy, sortDir, pageIndex, pageSize, partSearch, MfrSearch, countryOforigin, MSL, HtcCode, Description, recentOnly, IsPoHub);
            return dtResult;
        }

        /// <summary>
        /// 
        /// [006] code start
        /// <summary>
        /// GetListForCustomerRequirement
        /// Calls [usp_selectAll_CustomerRequirement_for_ClientBOM]
        /// </summary>
        public static List<CustomerRequirement> GetClientBOMListForCustomerRequirement(System.Int32? BOMNo, System.Int32? clientID)
        {
            List<CustomerRequirementDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.GetClientBOMListForCustomerRequirement(BOMNo, clientID);
            if (lstDetails == null)
            {
                return new List<CustomerRequirement>();
            }
            else
            {
                List<CustomerRequirement> lst = new List<CustomerRequirement>();
                foreach (CustomerRequirementDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.CustomerRequirement obj = new Rebound.GlobalTrader.BLL.CustomerRequirement();
                    obj.CustomerRequirementId = objDetails.CustomerRequirementId;
                    obj.CustomerRequirementNumber = objDetails.CustomerRequirementNumber;
                    obj.ClientNo = objDetails.ClientNo;
                    obj.FullPart = objDetails.FullPart;
                    obj.Part = objDetails.Part;
                    obj.ManufacturerNo = objDetails.ManufacturerNo;
                    obj.DateCode = objDetails.DateCode;
                    obj.PackageNo = objDetails.PackageNo;
                    obj.Quantity = objDetails.Quantity;
                    obj.Price = objDetails.Price;
                    obj.CurrencyNo = objDetails.CurrencyNo;
                    obj.ReceivedDate = objDetails.ReceivedDate;
                    obj.Salesman = objDetails.Salesman;
                    obj.DatePromised = objDetails.DatePromised;
                    obj.Notes = objDetails.Notes;
                    obj.Instructions = objDetails.Instructions;
                    obj.Shortage = objDetails.Shortage;
                    obj.CompanyNo = objDetails.CompanyNo;
                    obj.ContactNo = objDetails.ContactNo;
                    obj.Alternate = objDetails.Alternate;
                    obj.OriginalCustomerRequirementNo = objDetails.OriginalCustomerRequirementNo;
                    obj.ReasonNo = objDetails.ReasonNo;
                    obj.ProductNo = objDetails.ProductNo;
                    obj.CustomerPart = objDetails.CustomerPart;
                    obj.Closed = objDetails.Closed;
                    obj.ROHS = objDetails.ROHS;
                    obj.UpdatedBy = objDetails.UpdatedBy;
                    obj.DLUP = objDetails.DLUP;
                    obj.UsageNo = objDetails.UsageNo;
                    obj.DisplayStatus = objDetails.DisplayStatus;
                    obj.SalesmanName = objDetails.SalesmanName;
                    obj.DivisionNo = objDetails.DivisionNo;
                    obj.TeamNo = objDetails.TeamNo;
                    obj.CompanyName = objDetails.CompanyName;
                    obj.CompanyOnStop = objDetails.CompanyOnStop;
                    obj.ContactName = objDetails.ContactName;
                    obj.CurrencyCode = objDetails.CurrencyCode;
                    obj.CurrencyDescription = objDetails.CurrencyDescription;
                    obj.ProductName = objDetails.ProductName;
                    obj.ProductDescription = objDetails.ProductDescription;
                    obj.ManufacturerName = objDetails.ManufacturerName;
                    obj.ManufacturerCode = objDetails.ManufacturerCode;
                    obj.PackageName = objDetails.PackageName;
                    obj.PackageDescription = objDetails.PackageDescription;
                    obj.UsageName = objDetails.UsageName;
                    obj.CustomerRequirementValue = objDetails.CustomerRequirementValue;
                    obj.ClosedReason = objDetails.ClosedReason;
                    obj.PartWatch = objDetails.PartWatch;
                    obj.BOM = objDetails.BOM;
                    obj.BOMName = objDetails.BOMName;
                    obj.DivisionName = objDetails.DivisionName;
                    obj.BOMHeader = objDetails.BOMHeader;
                    obj.BOMNo = (int)objDetails.BOMNo;
                    obj.POHubReleaseBy = objDetails.POHubReleaseBy;
                    obj.SourcingResultId = objDetails.SourcingResultId;
                    obj.RequestToPOHubBy = objDetails.RequestToPOHubBy;
                    obj.BOMCode = objDetails.BOMCode;
                    obj.BOMFullName = objDetails.BOMFullName;
                    obj.BOMCurrencyCode = objDetails.BOMCurrencyCode;
                    obj.ConvertedTargetValue = objDetails.ConvertedTargetValue;

                    obj.PurchaseQuoteId = objDetails.PurchaseQuoteId;
                    obj.PurchaseQuoteNumber = objDetails.PurchaseQuoteNumber;
                    obj.ClientName = objDetails.ClientName;
                    obj.POHubCompany = objDetails.POHubCompany;
                    obj.FactorySealed = objDetails.FactorySealed;
                    obj.MSL = objDetails.MSL;
                    obj.AllSorcingHasDelDate = objDetails.AllSorcingHasDelDate;
                    obj.AllSorcingHasProduct = objDetails.AllSorcingHasProduct;
                    obj.SourcingResult = objDetails.SourcingResult;
                    obj.BOMStatus = objDetails.BOMStatus;
                    obj.HasClientSourcingResult = objDetails.HasClientSourcingResult;
                    obj.HasHubSourcingResult = objDetails.HasHubSourcingResult;
                    obj.IsNoBid = objDetails.IsNoBid;
                    obj.ExpeditDate = objDetails.ExpeditDate;
                    obj.UpdateByPH = objDetails.UpdateByPH;
                    obj.AlternateStatus = objDetails.AlternateStatus;

                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }

        public static DataTable GetMappedColumn(int clientBomId, int loginId)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.GetMappedColumn(clientBomId, loginId);
        }

        public static void SaveMappingDetail(int columnIndex, int columnId, int ClientBOMId, int userId)
        {
            Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.SaveMappingDetail(columnIndex, columnId, ClientBOMId, userId);
        }

        public static void DeleteTempMapping(int bomId, int userId)
        {
            Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.DeleteTempMapping(bomId, userId);
        }

        public static int DeleteRecord(int recordId)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.DeleteRecord(recordId);
        }

        public static int ProcessRecord(int bomId, int userId, int currentIndex, int pageSize, string recordIds)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.ProcessRecord(bomId, userId, currentIndex, pageSize, recordIds);
        }

        public static bool ValidateBOMData(string RecordIds, int bomId, int userId)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.ValidateBOMData(RecordIds, bomId, userId);
        }

        public static DataTable GetCustTableAllColumn()
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.GetCustTableAllColumn();
        }

        public static void saveExcelData(DataTable dt, int bomId)
        {
            Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.saveExcelData(dt, bomId);
        }

        public static Int32 saveBomFileInfo(int bomId, string caption, string filename, int loginId)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.saveBomFileInfo(bomId, caption, filename, loginId);
        }
        public static DataTable GetBOMInfo(int BOMId)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.GetBOMInfo(BOMId);
        }
        public static DataTable GetAllColumn(int bomId, int userId)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.GetAllColumn(bomId, userId);
        }
        public static DataTable GetCompanyAndOtherMasterData(int CompanyId)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.GetCompanyAndOtherMasterData(CompanyId);
        }

        public static void SaveUpdateRecord(DataTable dt, int userid)
        {
            Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.SaveUpdateRecord(dt, userid);
        }

        public static DataTable GetBOMDetailFromTemp(int displayLength, int displayStart, int sortCol, string sortDir, string search, int userid, int bomId)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.GetBOMDetailFromTemp(displayLength, displayStart, sortCol, sortDir, search, userid, bomId);
        }

        public static DataTable GetValidationError(int bomId, int userId, string RecordIds)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.GetValidationError(bomId, userId, RecordIds);
        }

        public static DataTable GetSalespersonList(int clientId)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.GetSalespersonList(clientId);
        }

        public static void ResetBomData(int bomId, int userId, int fileLogId)
        {
            Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.ResetBomData(bomId, userId, fileLogId);
        }

        /// <summary>
        /// ItemSearch
        /// Calls [usp_itemsearch_CustomerReqsWithBOM]
        /// </summary>
        public static List<CustomerRequirement> ItemSearchWithBOM(System.Int32? clientId, System.Int32? teamId, System.Int32? divisionId, System.Int32? loginId, System.Int32? orderBy, System.Int32? sortDir, System.Int32? pageIndex, System.Int32? pageSize, System.String partSearch, System.String cmSearch, System.Boolean? includeClosed, System.Int32? customerRequirementNoLo, System.Int32? customerRequirementNoHi, System.DateTime? receivedDateFrom, System.DateTime? receivedDateTo, System.Int32? client, System.String bomName)
        {
            List<CustomerRequirementDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.ItemSearchWithBOM(clientId, teamId, divisionId, loginId, orderBy, sortDir, pageIndex, pageSize, partSearch, cmSearch, includeClosed, customerRequirementNoLo, customerRequirementNoHi, receivedDateFrom, receivedDateTo, client, bomName);
            if (lstDetails == null)
            {
                return new List<CustomerRequirement>();
            }
            else
            {
                List<CustomerRequirement> lst = new List<CustomerRequirement>();
                foreach (CustomerRequirementDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.CustomerRequirement obj = new Rebound.GlobalTrader.BLL.CustomerRequirement();
                    obj.CustomerRequirementId = objDetails.CustomerRequirementId;
                    obj.CustomerRequirementNumber = objDetails.CustomerRequirementNumber;
                    obj.CompanyName = objDetails.CompanyName;
                    obj.Part = objDetails.Part;
                    obj.ROHS = objDetails.ROHS;
                    obj.ReceivedDate = objDetails.ReceivedDate;
                    obj.Quantity = objDetails.Quantity;
                    obj.Price = objDetails.Price;
                    obj.CurrencyCode = objDetails.CurrencyCode;
                    obj.RowNum = objDetails.RowNum;
                    obj.RowCnt = objDetails.RowCnt;
                    obj.BOMHeader = objDetails.BOMHeader;
                    obj.PHPrice = objDetails.PHPrice;
                    obj.PHCurrencyCode = objDetails.PHCurrencyCode;
                    obj.AS9120 = objDetails.AS9120;
                    obj.IsGlobalCurrencySame = objDetails.IsGlobalCurrencySame;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }

        public static void AutoMapRemainingColumn(int ClientBOMId, int userId)
        {
            Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.AutoMapRemainingColumn(ClientBOMId, userId);
        }

        public static int SaveCompanyColumnMapping(int ClientBOMId)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.SaveCompanyColumnMapping(ClientBOMId);
        }
        public static DataTable GetRecordDetail(int bomId)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.GetRecordDetail(bomId);
        }
        /// <summary>
        /// ItemSearch
        /// Calls [usp_itemsearch_CustRequirementWithoutBOM]
        /// </summary>
        public static List<CustomerRequirement> ItemSearchWithoutBOM(System.Int32? clientId, System.Int32? orderBy, System.Int32? sortDir, System.Int32? pageIndex, System.Int32? pageSize, System.String partSearch, System.String cmSearch, System.Boolean? includeClosed, System.Int32? customerRequirementNoLo, System.Int32? customerRequirementNoHi, System.DateTime? receivedDateFrom, System.DateTime? receivedDateTo, System.Int32? BOM, System.String BOMName)
        {
            List<CustomerRequirementDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.ItemSearchWithoutBOM(clientId, orderBy, sortDir, pageIndex, pageSize, partSearch, cmSearch, includeClosed, customerRequirementNoLo, customerRequirementNoHi, receivedDateFrom, receivedDateTo, BOM, BOMName);
            if (lstDetails == null)
            {
                return new List<CustomerRequirement>();
            }
            else
            {
                List<CustomerRequirement> lst = new List<CustomerRequirement>();
                foreach (CustomerRequirementDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.CustomerRequirement obj = new Rebound.GlobalTrader.BLL.CustomerRequirement();
                    obj.CustomerRequirementId = objDetails.CustomerRequirementId;
                    obj.CustomerRequirementNumber = objDetails.CustomerRequirementNumber;
                    obj.CompanyName = objDetails.CompanyName;
                    obj.Part = objDetails.Part;
                    obj.ROHS = objDetails.ROHS;
                    obj.ReceivedDate = objDetails.ReceivedDate;
                    obj.Quantity = objDetails.Quantity;
                    obj.Price = objDetails.Price;
                    obj.CurrencyCode = objDetails.CurrencyCode;
                    obj.RowNum = objDetails.RowNum;
                    obj.RowCnt = objDetails.RowCnt;
                    obj.BOMHeader = objDetails.BOMHeader;
                    obj.BOMName = objDetails.BOMName;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }
        /// <summary>
        /// ItemSearch
        /// Calls [usp_itemsearch_CustRequirementWithoutClientBOM]
        /// </summary>
        public static List<CustomerRequirement> ItemSearchWithoutClientBOM(System.Int32? clientId, System.Int32? orderBy, System.Int32? sortDir, System.Int32? pageIndex, System.Int32? pageSize, System.String partSearch, System.String cmSearch, System.Boolean? includeClosed, System.Int32? customerRequirementNoLo, System.Int32? customerRequirementNoHi, System.DateTime? receivedDateFrom, System.DateTime? receivedDateTo, System.Int32? BOM, System.String BOMName)
        {
            List<CustomerRequirementDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.ItemSearchWithoutClientBOM(clientId, orderBy, sortDir, pageIndex, pageSize, partSearch, cmSearch, includeClosed, customerRequirementNoLo, customerRequirementNoHi, receivedDateFrom, receivedDateTo, BOM, BOMName);
            if (lstDetails == null)
            {
                return new List<CustomerRequirement>();
            }
            else
            {
                List<CustomerRequirement> lst = new List<CustomerRequirement>();
                foreach (CustomerRequirementDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.CustomerRequirement obj = new Rebound.GlobalTrader.BLL.CustomerRequirement();
                    obj.CustomerRequirementId = objDetails.CustomerRequirementId;
                    obj.CustomerRequirementNumber = objDetails.CustomerRequirementNumber;
                    obj.CompanyName = objDetails.CompanyName;
                    obj.Part = objDetails.Part;
                    obj.ROHS = objDetails.ROHS;
                    obj.ReceivedDate = objDetails.ReceivedDate;
                    obj.Quantity = objDetails.Quantity;
                    obj.Price = objDetails.Price;
                    obj.CurrencyCode = objDetails.CurrencyCode;
                    obj.RowNum = objDetails.RowNum;
                    obj.RowCnt = objDetails.RowCnt;
                    obj.BOMHeader = objDetails.BOMHeader;
                    obj.BOMName = objDetails.BOMName;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }


        //CodeEnd[[012]] 
        /// <summary>
        /// Delete
        /// Calls [usp_delete_CustomerRequirement]
        /// </summary>
        public static bool Delete(System.Int32? customerRequirementId)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.Delete(customerRequirementId);
        }
        /// <summary>
        /// Insert
        /// Calls [usp_insert_CustomerRequirement]
        /// </summary>
        public static Int32 Insert(System.Int32? clientNo, System.String part, System.Int32? manufacturerNo, System.String dateCode, System.Int32? packageNo,
            System.Int32? quantity,
            System.Double? price, System.Int32? currencyNo, System.DateTime? receivedDate, System.Int32? salesman, System.DateTime? datePromised,
            System.String notes, System.String instructions, System.Boolean? shortage, System.Int32? companyNo, System.Int32? contactNo, System.Int32?
            usageNo, System.Boolean? alternate, System.Int32? originalCustomerRequirementNo, System.Int32? reasonNo,
            System.Int32? productNo, System.String customerPart, System.Boolean? closed, System.Byte? rohs, System.Int32? updatedBy,
            System.Boolean? partWatch, System.Boolean? bom, System.String bomName, System.Int32? BOMNo, System.Boolean? FactorySealed,
            System.String MSL, System.Boolean? PQA, System.Boolean? ObsoleteChk, System.Boolean? LastTimeBuyChk, System.Boolean? RefirbsAcceptableChk,
            System.Boolean? TestingRequiredChk, System.Double? TargetSellPrice, System.Double? CompetitorBestOffer, System.DateTime? CustomerDecisionDate, System.DateTime? RFQClosingDate, System.Int32? QuoteValidityRequired, System.Int32? Type, System.Boolean? OrderToPlace, System.Int32? RequirementforTraceability,
            System.String EAU, System.Boolean? AlternativesAccepted, System.Boolean? RepeatBusiness, System.Int32? SupportTeamMemberNo,
            System.String CountryOfOrigin, System.Int32? CountryOfOriginNo, System.String LifeCycleStage, System.String HTSCode, System.Double? AveragePrice, System.String Packaging, System.String PackagingSize, System.String Descriptions, System.Int32? IHSPartsId, System.String IHSCurrencyCode, System.String IHSProduct, System.String ECCNCode, System.Int32? ECCNNo, System.Boolean? AS6081)

        {
            Int32 objReturn = Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.Insert(clientNo, part, manufacturerNo, dateCode,
                packageNo, quantity, price, currencyNo, receivedDate, salesman, datePromised, notes, instructions, shortage, companyNo,
                contactNo, usageNo, alternate, originalCustomerRequirementNo, reasonNo, productNo, customerPart, closed, rohs, updatedBy,
                partWatch, bom, bomName, BOMNo, FactorySealed, MSL, PQA, ObsoleteChk, LastTimeBuyChk, RefirbsAcceptableChk, TestingRequiredChk,
                TargetSellPrice, CompetitorBestOffer, CustomerDecisionDate, RFQClosingDate, QuoteValidityRequired, Type, OrderToPlace, RequirementforTraceability,
                EAU, AlternativesAccepted, RepeatBusiness, SupportTeamMemberNo, CountryOfOrigin, CountryOfOriginNo, LifeCycleStage, HTSCode, AveragePrice, Packaging, PackagingSize, Descriptions, IHSPartsId, IHSCurrencyCode, IHSProduct, ECCNCode, ECCNNo, AS6081);
            return objReturn;
        }
        /// <summary>
        /// Insert (without parameters)
        /// Calls [usp_insert_CustomerRequirement]
        /// </summary>
        public Int32 Insert()
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.Insert(ClientNo, Part, ManufacturerNo, DateCode, PackageNo, Quantity,
                Price, CurrencyNo, ReceivedDate, Salesman, DatePromised, Notes, Instructions, Shortage, CompanyNo, ContactNo, UsageNo, Alternate,
                OriginalCustomerRequirementNo, ReasonNo, ProductNo, CustomerPart, Closed, ROHS, UpdatedBy, PartWatch, BOM, BOMName, BOMNo, FactorySealed,
                MSL, PQA, Obsolete, LastTimeBuy, RefirbsAcceptable, TestingRequired, TargetSellPrice, CompetitorBestOffer, CustomerDecisionDate, RFQClosingDate,
                QuoteValidityRequired, Type, OrderToPlace, RequirementforTraceability, EAU, AlternativesAccepted, RepeatBusiness, SupportTeamMemberNo,
                CountryOfOrigin, CountryOfOriginNo, LifeCycleStage, HTSCode, AveragePrice, Packaging, PackagingSize, Descriptions, IHSPartsId, IHSCurrencyCode, IHSProduct, ECCNCode, ECCNNo, AS6081);
        }

        //
        //IHS API Data Insert code start

        /// <summary>
        /// Insert
        /// Calls [usp_insert_APITokenNumber]
        /// </summary>
        public static Int32 InsertTokenNumberFromAPI(System.String TokenNumberInsert)
        {
            Int32 objReturn = Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.InsertTokenNumberFromAPI(TokenNumberInsert);
            return objReturn;
        }

        /// <summary>
        /// Insert
        /// Calls [usp_insert_InsertFromIHSApi]
        /// </summary>
        public static Int32 InsertFromIHSApi(System.Int32? clientNo, System.Int64? PartID, System.String prtNbr, System.String mfrName, System.String prtStatus, System.String prtDesc, System.String mfrFullName, System.String coo, System.String htsCd, System.String msl, System.String pckMethod, System.String pckCd, System.String source, System.Double price, System.String currency, System.String telephone, System.String email, System.String priceAvailabilityLink)
        {
            Int32 objReturn = Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.InsertFromIHSApi(clientNo, PartID, prtNbr, mfrName, prtStatus, prtDesc, mfrFullName, coo, htsCd, msl, pckMethod, pckCd, source, price, currency, telephone, email, priceAvailabilityLink);
            return objReturn;
        }

        /// <summary>
        /// Insert
        /// Calls [usp_insert_IHSApiXML]
        /// </summary>
        public static Int32 InsertIHSApiXML(System.Int32? clientNo, System.Int32? updatedBy, System.String strXMLData)
        {
            Int32 objReturn = Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.InsertIHSApiXML(clientNo, updatedBy, strXMLData);
            return objReturn;
        }

        /// <summary>
        /// Insert
        /// Calls [usp_insert_IHSApiXML_BOMManager]
        /// </summary>
        public static List<Part> InsertIHSApiXML_BOMManager(System.Int32? clientNo, System.Int32? updatedBy, System.String strXMLData)
        {
            List<PartDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.InsertIHSApiXML_BOMManager(clientNo, updatedBy, strXMLData);

            if (lstDetails == null)
            {
                return new List<Part>();
            }
            else
            {
                List<Part> lst = new List<Part>();
                foreach (PartDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.Part obj = new Rebound.GlobalTrader.BLL.Part();
                    obj.PartName = objDetails.PartName;
                    obj.ManufacturerNo = objDetails.ManufacturerNo;
                    obj.PartNameWithManufacture = objDetails.PartNameWithManufacture;
                    obj.ManufacturerName = objDetails.ManufacturerName;
                    obj.ManufacturerFullName = objDetails.ManufacturerFullName;
                    obj.ROHSNo = objDetails.ROHSNo;
                    obj.IHSPartsId = objDetails.IHSPartsId;
                    obj.CountryOfOrigin = objDetails.CountryOfOrigin;
                    obj.CountryOfOriginNo = objDetails.CountryOfOriginNo;
                    obj.HTSCode = objDetails.HTSCode;
                    obj.AveragePrice = objDetails.AveragePrice;
                    obj.Packaging = objDetails.Packaging;
                    obj.PackagingSize = objDetails.PackagingSize;
                    obj.ROHSName = objDetails.ROHSName;
                    obj.Descriptions = objDetails.Descriptions;
                    obj.ResultType = objDetails.ResultType;
                    obj.PartStatus = objDetails.PartStatus;
                    obj.IHSCurrencyCode = objDetails.IHSCurrencyCode;
                    obj.ProductDescription = objDetails.ProductDescription;
                    obj.ProductNo = objDetails.ProductNo;
                    obj.IHSProductDescription = objDetails.IHSProductDescription;
                    obj.IHSDutyCode = objDetails.IHSDutyCode;
                    obj.RowNum = objDetails.RowNum;
                    obj.RowCnt = objDetails.RowCnt;
                    obj.IHSProduct = objDetails.IHSProduct;
                    obj.ECCNCode = objDetails.ECCNCode;

                    //[001] end
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }



        ////IHS API Data Insert code start
        /// <summary>
        /// InsertAsAlternate
        /// Calls [usp_insert_CustomerRequirement_as_Alternate]
        /// </summary>
        //public static Int32 InsertAsAlternate(System.String customerRequirementName, System.Int32? customerRequirementNumber, System.Int32? clientNo, System.String part, System.Int32? manufacturerNo, System.String dateCode, System.Int32? packageNo, System.Int32? quantity, System.Double? price, System.Int32? currencyNo, System.DateTime? receivedDate, System.Int32? salesman, System.DateTime? datePromised, System.String notes, System.String instructions, System.Boolean? shortage, System.Int32? companyNo, System.Int32? contactNo, System.Int32? usageNo, System.Int32? originalCustomerRequirementNo, System.Int32? reasonNo, System.Int32? productNo, System.String customerPart, System.Boolean? closed, System.Byte? rohs, System.Int32? updatedBy, System.Boolean? partWatch, System.Boolean? bom, System.String bomName,
        //    System.Int32? salesmanno, System.Boolean? PQA, System.Boolean? ObsoleteChk, System.Boolean? LastTimeBuyChk, System.Boolean? RefirbsAcceptableChk, System.Boolean? TestingRequiredChk, System.Boolean? AlternativesAccepted, System.Boolean? RepeatBusiness, System.Double? CompetitorBestOffer, System.DateTime? CustomerDecisionDate, System.DateTime? RFQClosingDate, System.Int32? QuoteValidityRequired, System.Int32? Type, System.Boolean? OrderToPlace, System.Boolean? FactorySealed, System.String MSL, System.Int32? BOMNo, System.Int32? RequirementforTraceability, System.String EAU)
        //{
        //    Int32 objReturn = Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.InsertAsAlternate(customerRequirementName, customerRequirementNumber, clientNo, part, manufacturerNo, dateCode, packageNo, quantity, price, currencyNo, receivedDate, salesman, datePromised, notes, instructions, shortage, companyNo, contactNo, usageNo, originalCustomerRequirementNo, reasonNo, productNo, customerPart, closed, rohs, updatedBy, partWatch, bom, bomName,
        //        salesmanno, PQA, ObsoleteChk, LastTimeBuyChk, RefirbsAcceptableChk, TestingRequiredChk, AlternativesAccepted, RepeatBusiness, CompetitorBestOffer, CustomerDecisionDate, RFQClosingDate, QuoteValidityRequired, Type, OrderToPlace, FactorySealed, MSL, BOMNo, RequirementforTraceability, EAU);
        //    return objReturn;
        //}
        /// <summary>
        /// InsertAsAllAlternate
        /// Calls [usp_insert_CustomerRequirement_as_AllAlternate]
        /// </summary>
        public static Int32 InsertAsAllAlternate(System.Int32? clientNo, System.String part, System.Int32? CustRequirementId)
        {
            Int32 objReturn = Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.InsertAsAllAlternate(clientNo, part, CustRequirementId);
            return objReturn;
        }


        /// <summary>
        /// ItemSearch
        /// Calls [usp_itemsearch_CustomerRequirement]
        /// </summary>
        public static List<CustomerRequirement> ItemSearch(System.Int32? clientId, System.Int32? orderBy, System.Int32? sortDir, System.Int32? pageIndex, System.Int32? pageSize, System.String partSearch, System.String cmSearch, System.Boolean? includeClosed, System.Int32? customerRequirementNoLo, System.Int32? customerRequirementNoHi, System.DateTime? receivedDateFrom, System.DateTime? receivedDateTo)
        {
            List<CustomerRequirementDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.ItemSearch(clientId, orderBy, sortDir, pageIndex, pageSize, partSearch, cmSearch, includeClosed, customerRequirementNoLo, customerRequirementNoHi, receivedDateFrom, receivedDateTo);
            if (lstDetails == null)
            {
                return new List<CustomerRequirement>();
            }
            else
            {
                List<CustomerRequirement> lst = new List<CustomerRequirement>();
                foreach (CustomerRequirementDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.CustomerRequirement obj = new Rebound.GlobalTrader.BLL.CustomerRequirement();
                    obj.CustomerRequirementId = objDetails.CustomerRequirementId;
                    obj.CustomerRequirementNumber = objDetails.CustomerRequirementNumber;
                    obj.CompanyName = objDetails.CompanyName;
                    obj.Part = objDetails.Part;
                    obj.ROHS = objDetails.ROHS;
                    obj.ReceivedDate = objDetails.ReceivedDate;
                    obj.Quantity = objDetails.Quantity;
                    obj.Price = objDetails.Price;
                    obj.CurrencyCode = objDetails.CurrencyCode;
                    obj.RowNum = objDetails.RowNum;
                    obj.RowCnt = objDetails.RowCnt;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }
        /// <summary>
        /// Get
        /// Calls [usp_select_CustomerRequirement]
        /// </summary>
        public static CustomerRequirement Get(System.Int32? customerRequirementId)
        {
            Rebound.GlobalTrader.DAL.CustomerRequirementDetails objDetails = Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.Get(customerRequirementId);
            if (objDetails == null)
            {
                return null;
            }
            else
            {
                CustomerRequirement obj = new CustomerRequirement();
                obj.CustomerRequirementId = objDetails.CustomerRequirementId;
                obj.CustomerRequirementNumber = objDetails.CustomerRequirementNumber;
                obj.ClientNo = objDetails.ClientNo;
                obj.FullPart = objDetails.FullPart;
                obj.Part = objDetails.Part;
                obj.ManufacturerNo = objDetails.ManufacturerNo;
                obj.DateCode = objDetails.DateCode;
                obj.PackageNo = objDetails.PackageNo;
                obj.Quantity = objDetails.Quantity;
                obj.Price = objDetails.Price;
                obj.CurrencyNo = objDetails.CurrencyNo;
                obj.ReceivedDate = objDetails.ReceivedDate;
                obj.Salesman = objDetails.Salesman;
                obj.DatePromised = objDetails.DatePromised;
                obj.Notes = objDetails.Notes;
                obj.Instructions = objDetails.Instructions;
                obj.Shortage = objDetails.Shortage;
                obj.CompanyNo = objDetails.CompanyNo;
                obj.ContactNo = objDetails.ContactNo;
                obj.Alternate = objDetails.Alternate;
                obj.OriginalCustomerRequirementNo = objDetails.OriginalCustomerRequirementNo;
                obj.ReasonNo = objDetails.ReasonNo;
                obj.ProductNo = objDetails.ProductNo;
                obj.CustomerPart = objDetails.CustomerPart;
                obj.Closed = objDetails.Closed;
                obj.ROHS = objDetails.ROHS;
                obj.UpdatedBy = objDetails.UpdatedBy;
                obj.DLUP = objDetails.DLUP;
                obj.UsageNo = objDetails.UsageNo;
                obj.DisplayStatus = objDetails.DisplayStatus;
                obj.SalesmanName = objDetails.SalesmanName;
                obj.DivisionNo = objDetails.DivisionNo;
                obj.TeamNo = objDetails.TeamNo;
                obj.CompanyName = objDetails.CompanyName;
                obj.CompanyOnStop = objDetails.CompanyOnStop;
                obj.ContactName = objDetails.ContactName;
                obj.CurrencyCode = objDetails.CurrencyCode;
                obj.CurrencyDescription = objDetails.CurrencyDescription;
                obj.ProductName = objDetails.ProductName;
                obj.ProductDescription = objDetails.ProductDescription;
                obj.ManufacturerName = objDetails.ManufacturerName;
                obj.ManufacturerCode = objDetails.ManufacturerCode;
                obj.PackageName = objDetails.PackageName;
                obj.PackageDescription = objDetails.PackageDescription;
                obj.UsageName = objDetails.UsageName;
                obj.CustomerRequirementValue = objDetails.CustomerRequirementValue;
                obj.ClosedReason = objDetails.ClosedReason;
                obj.PartWatch = objDetails.PartWatch;
                obj.BOM = objDetails.BOM;
                obj.BOMName = objDetails.BOMName;
                obj.DivisionName = objDetails.DivisionName;
                obj.Traceability = objDetails.Traceability;
                //obj.BOMHeader = objDetails.BOMNo + "-" + objDetails.BOMHeader;
                //obj.BOMHeader = objDetails.BOMHeader + " (" + objDetails.BOMStatus+")";
                obj.BOMHeader = !string.IsNullOrEmpty(objDetails.BOMHeader) ? objDetails.BOMHeader + " (" + objDetails.BOMStatus + ")" : "";//NEW code
                obj.BOMNo = objDetails.BOMNo == null ? 0 : (int)objDetails.BOMNo;
                obj.RequestToPOHubBy = objDetails.RequestToPOHubBy;
                obj.FactorySealed = objDetails.FactorySealed;
                obj.MSL = objDetails.MSL;
                obj.SourcingResultNo = objDetails.SourcingResultNo;
                obj.PQA = objDetails.PQA;
                obj.Obsolete = objDetails.Obsolete;
                obj.LastTimeBuy = objDetails.LastTimeBuy;
                obj.RefirbsAcceptable = objDetails.RefirbsAcceptable;
                obj.TestingRequired = objDetails.TestingRequired;
                obj.TargetSellPrice = objDetails.TargetSellPrice;
                obj.CompetitorBestOffer = objDetails.CompetitorBestOffer;
                obj.CustomerDecisionDate = objDetails.CustomerDecisionDate;
                obj.RFQClosingDate = objDetails.RFQClosingDate;
                obj.QuoteValidityRequired = objDetails.QuoteValidityRequired;
                obj.Type = objDetails.Type;
                obj.OrderToPlace = objDetails.OrderToPlace;
                obj.RequirementforTraceability = objDetails.RequirementforTraceability;
                obj.QuoteValidityText = objDetails.QuoteValidityText;
                obj.ReqTypeText = objDetails.ReqTypeText;
                obj.ReqForTraceabilityText = objDetails.ReqForTraceabilityText;
                obj.EAU = objDetails.EAU;
                obj.ReqNotes = objDetails.ReqNotes;
                obj.POHubReleaseBy = objDetails.POHubReleaseBy;
                obj.ClientGlobalCurrencyNo = objDetails.ClientGlobalCurrencyNo;
                obj.IsCurrencyInSameFaimly = objDetails.IsCurrencyInSameFaimly;
                obj.AlternativesAccepted = objDetails.AlternativesAccepted;
                obj.RepeatBusiness = objDetails.RepeatBusiness;
                obj.ProductInactive = objDetails.ProductInactive;
                obj.DutyCode = objDetails.DutyCode;
                obj.DutyRate = objDetails.DutyRate;
                obj.MSLLevelNo = objDetails.MSLLevelNo;
                obj.IsProdHaz = objDetails.IsProdHaz;
                obj.AlternateStatus = objDetails.AlternateStatus;
                //[004] start
                obj.SalesOrderNumber = objDetails.SalesOrderNumber;
                //[004] end
                obj.SupportTeamMemberNo = objDetails.SupportTeamMemberNo;
                obj.SupportTeamMemberName = objDetails.SupportTeamMemberName;
                //ihs code start
                obj.CountryOfOrigin = objDetails.CountryOfOrigin;
                obj.CountryOfOriginNo = objDetails.CountryOfOriginNo;
                obj.LifeCycleStage = objDetails.LifeCycleStage;
                obj.HTSCode = objDetails.HTSCode;
                obj.AveragePrice = objDetails.AveragePrice;
                obj.Packaging = objDetails.Packaging;
                obj.PackagingSize = objDetails.PackagingSize;
                obj.Descriptions = objDetails.Descriptions;
                obj.IHSProduct = objDetails.IHSProduct;
                //
                //obj.IHSHTSCode = objDetails.IHSHTSCode;
                //obj.IHSProductNo = objDetails.IHSProductNo;
                //obj.IHSProductName = objDetails.IHSProductName;
                obj.IHSDutyCode = objDetails.IHSDutyCode;
                obj.ECCNCode = objDetails.ECCNCode;
                obj.ParentRequirementId = objDetails.ParentRequirementId;
                obj.ParentRequirementNo = objDetails.ParentRequirementNo;
                //[010] code start
                obj.IsOrderViaIPOonly = objDetails.IsOrderViaIPOonly;
                //[010] code end
                obj.IsRestManufaturer = objDetails.IsRestManufaturer;
                obj.PartEditStatus = objDetails.PartEditStatus;

                obj.SupportTeamMemberName = objDetails.SupportTeamMemberName;
                obj.SupportTeamMemberNo = objDetails.SupportTeamMemberNo;
                //[011] code start
                obj.IHSECCNCodeDefination = objDetails.IHSECCNCodeDefination;
                //[011] code end
                //ihs code end
                obj.IsRestrictedProduct = objDetails.IsRestrictedProduct;
                obj.ECCNNotify = objDetails.ECCNNotify;
                obj.EccnSubject = objDetails.EccnSubject;
                obj.EccnMessage = objDetails.EccnMessage;
                obj.StockAvailableDetail = objDetails.StockAvailableDetail;
                obj.WarningMessage = objDetails.WarningMessage;
                obj.BlankECCNCode = objDetails.BlankECCNCode;
                obj.AS6081 = objDetails.AS6081; //[013]
                obj.PurchasingNotes = objDetails.PurchasingNotes;
                obj.IsPDFAvailable = objDetails.IsPDFAvailable;
                
                objDetails = null;
                return obj;
            }
        }

        /// <summary>
        /// Get
        /// Calls [usp_select_CustomerRequirementBOM]
        /// </summary>
        public static CustomerRequirement GetReqBOM(System.Int32? customerRequirementId, System.Int32? clientNo)
        {
            Rebound.GlobalTrader.DAL.CustomerRequirementDetails objDetails = Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.GetReqBOM(customerRequirementId, clientNo);
            if (objDetails == null)
            {
                return null;
            }
            else
            {
                CustomerRequirement obj = new CustomerRequirement();
                obj.CustomerRequirementId = objDetails.CustomerRequirementId;
                obj.CustomerRequirementNumber = objDetails.CustomerRequirementNumber;
                obj.ClientNo = objDetails.ClientNo;
                obj.FullPart = objDetails.FullPart;
                obj.Part = objDetails.Part;
                obj.ManufacturerNo = objDetails.ManufacturerNo;
                obj.DateCode = objDetails.DateCode;
                obj.PackageNo = objDetails.PackageNo;
                obj.Quantity = objDetails.Quantity;
                obj.Price = objDetails.Price;
                obj.CurrencyNo = objDetails.CurrencyNo;
                obj.ReceivedDate = objDetails.ReceivedDate;
                obj.Salesman = objDetails.Salesman;
                obj.DatePromised = objDetails.DatePromised;
                obj.Notes = objDetails.Notes;
                obj.Instructions = objDetails.Instructions;
                obj.Shortage = objDetails.Shortage;
                obj.CompanyNo = objDetails.CompanyNo;
                obj.ContactNo = objDetails.ContactNo;
                obj.Alternate = objDetails.Alternate;
                obj.OriginalCustomerRequirementNo = objDetails.OriginalCustomerRequirementNo;
                obj.ReasonNo = objDetails.ReasonNo;
                obj.ProductNo = objDetails.ProductNo;
                obj.CustomerPart = objDetails.CustomerPart;
                obj.Closed = objDetails.Closed;
                obj.ROHS = objDetails.ROHS;
                obj.UpdatedBy = objDetails.UpdatedBy;
                obj.DLUP = objDetails.DLUP;
                obj.UsageNo = objDetails.UsageNo;
                obj.DisplayStatus = objDetails.DisplayStatus;
                obj.SalesmanName = objDetails.SalesmanName;
                obj.DivisionNo = objDetails.DivisionNo;
                obj.TeamNo = objDetails.TeamNo;
                obj.CompanyName = objDetails.CompanyName;
                obj.CompanyOnStop = objDetails.CompanyOnStop;
                obj.ContactName = objDetails.ContactName;
                obj.CurrencyCode = objDetails.CurrencyCode;
                obj.CurrencyDescription = objDetails.CurrencyDescription;
                obj.ProductName = objDetails.ProductName;
                obj.ProductDescription = objDetails.ProductDescription;
                obj.ManufacturerName = objDetails.ManufacturerName;
                obj.ManufacturerCode = objDetails.ManufacturerCode;
                obj.PackageName = objDetails.PackageName;
                obj.PackageDescription = objDetails.PackageDescription;
                obj.UsageName = objDetails.UsageName;
                obj.CustomerRequirementValue = objDetails.CustomerRequirementValue;
                obj.ClosedReason = objDetails.ClosedReason;
                obj.PartWatch = objDetails.PartWatch;
                obj.BOM = objDetails.BOM;
                obj.BOMName = objDetails.BOMName;
                obj.DivisionName = objDetails.DivisionName;
                obj.Traceability = objDetails.Traceability;
                //obj.BOMHeader = objDetails.BOMNo + "-" + objDetails.BOMHeader;
                //obj.BOMHeader = objDetails.BOMHeader + " (" + objDetails.BOMStatus+")";
                obj.BOMHeader = !string.IsNullOrEmpty(objDetails.BOMHeader) ? objDetails.BOMHeader + " (" + objDetails.BOMStatus + ")" : "";//NEW code
                obj.BOMNo = objDetails.BOMNo == null ? 0 : (int)objDetails.BOMNo;
                obj.RequestToPOHubBy = objDetails.RequestToPOHubBy;
                obj.FactorySealed = objDetails.FactorySealed;
                obj.MSL = objDetails.MSL;
                obj.SourcingResultNo = objDetails.SourcingResultNo;
                obj.PQA = objDetails.PQA;
                obj.Obsolete = objDetails.Obsolete;
                obj.LastTimeBuy = objDetails.LastTimeBuy;
                obj.RefirbsAcceptable = objDetails.RefirbsAcceptable;
                obj.TestingRequired = objDetails.TestingRequired;
                obj.TargetSellPrice = objDetails.TargetSellPrice;
                obj.CompetitorBestOffer = objDetails.CompetitorBestOffer;
                obj.CustomerDecisionDate = objDetails.CustomerDecisionDate;
                obj.RFQClosingDate = objDetails.RFQClosingDate;
                obj.QuoteValidityRequired = objDetails.QuoteValidityRequired;
                obj.Type = objDetails.Type;
                obj.OrderToPlace = objDetails.OrderToPlace;
                obj.RequirementforTraceability = objDetails.RequirementforTraceability;
                obj.QuoteValidityText = objDetails.QuoteValidityText;
                obj.ReqTypeText = objDetails.ReqTypeText;
                obj.ReqForTraceabilityText = objDetails.ReqForTraceabilityText;
                obj.SourcingResult = objDetails.SourcingResult;
                obj.EAU = objDetails.EAU;
                obj.ClientGlobalCurrencyNo = objDetails.ClientGlobalCurrencyNo;
                obj.ReqGlobalCurrencyNo = objDetails.ReqGlobalCurrencyNo;
                obj.ClientCurrencyCode = objDetails.ClientCurrencyCode;
                obj.ClientCurrencyNo = objDetails.ClientCurrencyNo;
                obj.IsNoBid = objDetails.IsNoBid;
                obj.NoBidNotes = objDetails.NoBidNotes;
                obj.AlternativesAccepted = objDetails.AlternativesAccepted;
                obj.RepeatBusiness = objDetails.RepeatBusiness;
                obj.DutyCode = objDetails.DutyCode;
                obj.DutyRate = objDetails.DutyRate;
                obj.MSLLevelNo = objDetails.MSLLevelNo;
                obj.SupportTeamMemberNo = objDetails.SupportTeamMemberNo;
                //ihs code start
                obj.CountryOfOrigin = objDetails.CountryOfOrigin;
                obj.CountryOfOriginNo = objDetails.CountryOfOriginNo;
                obj.LifeCycleStage = objDetails.LifeCycleStage;
                obj.HTSCode = objDetails.HTSCode;
                obj.AveragePrice = objDetails.AveragePrice;
                obj.Packaging = objDetails.Packaging;
                obj.PackagingSize = objDetails.PackagingSize;
                obj.Descriptions = objDetails.Descriptions;
                obj.IHSCurrencyCode = objDetails.IHSCurrencyCode;
                obj.IHSProduct = objDetails.IHSProduct;
                obj.IHSHTSCode = objDetails.IHSHTSCode;
                obj.IHSProductNo = objDetails.IHSProductNo;
                obj.IHSProductName = objDetails.IHSProductName;
                obj.IHSDutyCode = objDetails.IHSDutyCode;
                obj.PurchaseRequestId = objDetails.PurchaseRequestId;
                obj.PurchaseRequestNumber = objDetails.PurchaseRequestNumber;
                obj.ECCNCode = objDetails.ECCNCode;
                obj.IHSECCNCodeDefination = objDetails.IHSECCNCodeDefination;
                obj.IsAs6081Required = objDetails.IsAs6081Required;

                obj.IsPDFAvailable = objDetails.IsPDFAvailable;
                obj.IHSPartsId = objDetails.IHSPartsId;
                obj.StockAvailableDetail = objDetails.StockAvailableDetail;

                obj.LyticaManufacturerRef = objDetails.LyticaManufacturerRef;
                obj.LyticaAveragePrice = objDetails.LyticaAveragePrice;
                obj.LyticaTargetPrice = objDetails.LyticaTargetPrice;
                obj.LyticaMarketLeading = objDetails.LyticaMarketLeading;
                obj.CustomerRefNo = objDetails.CustomerRefNo;
                //ihs code end
                objDetails = null;
                return obj;
            }
        }
        /// <summary>
        /// GetByNumber
        /// Calls [usp_select_CustomerRequirement_by_Number]
        /// </summary>
        public static CustomerRequirement GetByNumber(System.Int32? customerRequirementNumber, System.Int32? clientNo)
        {
            Rebound.GlobalTrader.DAL.CustomerRequirementDetails objDetails = Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.GetByNumber(customerRequirementNumber, clientNo);
            if (objDetails == null)
            {
                return null;
            }
            else
            {
                CustomerRequirement obj = new CustomerRequirement();
                obj.CustomerRequirementId = objDetails.CustomerRequirementId;
                obj.CustomerRequirementNumber = objDetails.CustomerRequirementNumber;
                obj.ClientNo = objDetails.ClientNo;
                obj.FullPart = objDetails.FullPart;
                obj.Part = objDetails.Part;
                obj.ManufacturerNo = objDetails.ManufacturerNo;
                obj.DateCode = objDetails.DateCode;
                obj.PackageNo = objDetails.PackageNo;
                obj.Quantity = objDetails.Quantity;
                obj.Price = objDetails.Price;
                obj.CurrencyNo = objDetails.CurrencyNo;
                obj.ReceivedDate = objDetails.ReceivedDate;
                obj.Salesman = objDetails.Salesman;
                obj.DatePromised = objDetails.DatePromised;
                obj.Notes = objDetails.Notes;
                obj.Instructions = objDetails.Instructions;
                obj.Shortage = objDetails.Shortage;
                obj.CompanyNo = objDetails.CompanyNo;
                obj.ContactNo = objDetails.ContactNo;
                obj.Alternate = objDetails.Alternate;
                obj.OriginalCustomerRequirementNo = objDetails.OriginalCustomerRequirementNo;
                obj.ReasonNo = objDetails.ReasonNo;
                obj.ProductNo = objDetails.ProductNo;
                obj.CustomerPart = objDetails.CustomerPart;
                obj.Closed = objDetails.Closed;
                obj.ROHS = objDetails.ROHS;
                obj.UpdatedBy = objDetails.UpdatedBy;
                obj.DLUP = objDetails.DLUP;
                obj.UsageNo = objDetails.UsageNo;
                obj.DisplayStatus = objDetails.DisplayStatus;
                obj.SalesmanName = objDetails.SalesmanName;
                obj.DivisionNo = objDetails.DivisionNo;
                obj.TeamNo = objDetails.TeamNo;
                obj.CompanyName = objDetails.CompanyName;
                obj.CompanyOnStop = objDetails.CompanyOnStop;
                obj.ContactName = objDetails.ContactName;
                obj.CurrencyCode = objDetails.CurrencyCode;
                obj.CurrencyDescription = objDetails.CurrencyDescription;
                obj.ProductName = objDetails.ProductName;
                obj.ProductDescription = objDetails.ProductDescription;
                obj.ManufacturerName = objDetails.ManufacturerName;
                obj.ManufacturerCode = objDetails.ManufacturerCode;
                obj.PackageName = objDetails.PackageName;
                obj.PackageDescription = objDetails.PackageDescription;
                obj.UsageName = objDetails.UsageName;
                obj.CustomerRequirementValue = objDetails.CustomerRequirementValue;
                obj.ClosedReason = objDetails.ClosedReason;
                obj.PartWatch = objDetails.PartWatch;
                obj.BOM = objDetails.BOM;
                obj.BOMName = objDetails.BOMName;
                obj.DivisionName = objDetails.DivisionName;
                objDetails = null;
                return obj;
            }
        }
        /// <summary>
        /// GetForPage
        /// Calls [usp_select_CustomerRequirement_for_Page]
        /// </summary>
        public static CustomerRequirement GetForPage(System.Int32? customerRequirementId)
        {
            Rebound.GlobalTrader.DAL.CustomerRequirementDetails objDetails = Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.GetForPage(customerRequirementId);
            if (objDetails == null)
            {
                return null;
            }
            else
            {
                CustomerRequirement obj = new CustomerRequirement();
                obj.CustomerRequirementId = objDetails.CustomerRequirementId;
                obj.CustomerRequirementNumber = objDetails.CustomerRequirementNumber;
                obj.ClientNo = objDetails.ClientNo;
                obj.CompanyNo = objDetails.CompanyNo;
                obj.CompanyName = objDetails.CompanyName;
                obj.DisplayStatus = objDetails.DisplayStatus;
                obj.TeamNo = objDetails.TeamNo;
                obj.DivisionNo = objDetails.DivisionNo;
                obj.Salesman = objDetails.Salesman;
                obj.ContactNo = objDetails.ContactNo;
                obj.ClientName = objDetails.ClientName;
                objDetails = null;
                return obj;
            }
        }
        /// <summary>
        /// GetIdByNumber
        /// Calls [usp_select_CustomerRequirement_Id_by_Number]
        /// </summary>
        public static CustomerRequirement GetIdByNumber(System.Int32? customerRequirementNumber, System.Int32? clientNo)
        {
            Rebound.GlobalTrader.DAL.CustomerRequirementDetails objDetails = Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.GetIdByNumber(customerRequirementNumber, clientNo);
            if (objDetails == null)
            {
                return null;
            }
            else
            {
                CustomerRequirement obj = new CustomerRequirement();
                obj.CustomerRequirementId = objDetails.CustomerRequirementId;
                objDetails = null;
                return obj;
            }
        }
        /// <summary>
        /// GetNextNumber
        /// Calls [usp_select_CustomerRequirement_NextNumber]
        /// </summary>
        public static CustomerRequirement GetNextNumber(System.Int32? clientNo, System.Int32? updatedBy)
        {
            Rebound.GlobalTrader.DAL.CustomerRequirementDetails objDetails = Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.GetNextNumber(clientNo, updatedBy);
            if (objDetails == null)
            {
                return null;
            }
            else
            {
                CustomerRequirement obj = new CustomerRequirement();
                obj.CustomerRequirementNumber = objDetails.CustomerRequirementNumber;
                objDetails = null;
                return obj;
            }
        }
        /// <summary>
        /// GetNumberById
        /// Calls [usp_select_CustomerRequirement_Number_by_Id]
        /// </summary>
        public static CustomerRequirement GetNumberById(System.Int32? customerRequirementId)
        {
            Rebound.GlobalTrader.DAL.CustomerRequirementDetails objDetails = Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.GetNumberById(customerRequirementId);
            if (objDetails == null)
            {
                return null;
            }
            else
            {
                CustomerRequirement obj = new CustomerRequirement();
                obj.CustomerRequirementNumber = objDetails.CustomerRequirementNumber;
                objDetails = null;
                return obj;
            }
        }
        /// <summary>
        /// GetListForCompany
        /// Calls [usp_selectAll_CustomerRequirement_for_Company]
        /// </summary>
        public static List<CustomerRequirement> GetListForCompany(System.Int32? companyId, System.Boolean? includeClosed)
        {
            List<CustomerRequirementDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.GetListForCompany(companyId, includeClosed);
            if (lstDetails == null)
            {
                return new List<CustomerRequirement>();
            }
            else
            {
                List<CustomerRequirement> lst = new List<CustomerRequirement>();
                foreach (CustomerRequirementDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.CustomerRequirement obj = new Rebound.GlobalTrader.BLL.CustomerRequirement();
                    obj.CustomerRequirementId = objDetails.CustomerRequirementId;
                    obj.CustomerRequirementNumber = objDetails.CustomerRequirementNumber;
                    obj.ClientNo = objDetails.ClientNo;
                    obj.FullPart = objDetails.FullPart;
                    obj.Part = objDetails.Part;
                    obj.ManufacturerNo = objDetails.ManufacturerNo;
                    obj.DateCode = objDetails.DateCode;
                    obj.PackageNo = objDetails.PackageNo;
                    obj.Quantity = objDetails.Quantity;
                    obj.Price = objDetails.Price;
                    obj.CurrencyNo = objDetails.CurrencyNo;
                    obj.ReceivedDate = objDetails.ReceivedDate;
                    obj.Salesman = objDetails.Salesman;
                    obj.DatePromised = objDetails.DatePromised;
                    obj.Notes = objDetails.Notes;
                    obj.Instructions = objDetails.Instructions;
                    obj.Shortage = objDetails.Shortage;
                    obj.CompanyNo = objDetails.CompanyNo;
                    obj.ContactNo = objDetails.ContactNo;
                    obj.Alternate = objDetails.Alternate;
                    obj.OriginalCustomerRequirementNo = objDetails.OriginalCustomerRequirementNo;
                    obj.ReasonNo = objDetails.ReasonNo;
                    obj.ProductNo = objDetails.ProductNo;
                    obj.CustomerPart = objDetails.CustomerPart;
                    obj.Closed = objDetails.Closed;
                    obj.ROHS = objDetails.ROHS;
                    obj.UpdatedBy = objDetails.UpdatedBy;
                    obj.DLUP = objDetails.DLUP;
                    obj.UsageNo = objDetails.UsageNo;
                    obj.DisplayStatus = objDetails.DisplayStatus;
                    obj.SalesmanName = objDetails.SalesmanName;
                    obj.DivisionNo = objDetails.DivisionNo;
                    obj.TeamNo = objDetails.TeamNo;
                    obj.CompanyName = objDetails.CompanyName;
                    obj.CompanyOnStop = objDetails.CompanyOnStop;
                    obj.ContactName = objDetails.ContactName;
                    obj.CurrencyCode = objDetails.CurrencyCode;
                    obj.CurrencyDescription = objDetails.CurrencyDescription;
                    obj.ProductName = objDetails.ProductName;
                    obj.ProductDescription = objDetails.ProductDescription;
                    obj.ManufacturerName = objDetails.ManufacturerName;
                    obj.ManufacturerCode = objDetails.ManufacturerCode;
                    obj.PackageName = objDetails.PackageName;
                    obj.PackageDescription = objDetails.PackageDescription;
                    obj.UsageName = objDetails.UsageName;
                    obj.CustomerRequirementValue = objDetails.CustomerRequirementValue;
                    obj.ClosedReason = objDetails.ClosedReason;
                    obj.PartWatch = objDetails.PartWatch;
                    obj.BOM = objDetails.BOM;
                    obj.BOMName = objDetails.BOMName;
                    obj.DivisionName = objDetails.DivisionName;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }
        /// <summary>
        /// GetListForCustomerRequirementNumber
        /// Calls [usp_selectAll_CustomerRequirement_for_CustomerRequirementNumber]
        /// </summary>
        public static List<CustomerRequirement> GetListForCustomerRequirementNumber(System.Int32? customerRequirementNumber, System.Int32? clientNo)
        {
            List<CustomerRequirementDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.GetListForCustomerRequirementNumber(customerRequirementNumber, clientNo);
            if (lstDetails == null)
            {
                return new List<CustomerRequirement>();
            }
            else
            {
                List<CustomerRequirement> lst = new List<CustomerRequirement>();
                foreach (CustomerRequirementDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.CustomerRequirement obj = new Rebound.GlobalTrader.BLL.CustomerRequirement();
                    obj.CustomerRequirementId = objDetails.CustomerRequirementId;
                    obj.CustomerRequirementNumber = objDetails.CustomerRequirementNumber;
                    obj.ClientNo = objDetails.ClientNo;
                    obj.FullPart = objDetails.FullPart;
                    obj.Part = objDetails.Part;
                    obj.ManufacturerNo = objDetails.ManufacturerNo;
                    obj.DateCode = objDetails.DateCode;
                    obj.PackageNo = objDetails.PackageNo;
                    obj.Quantity = objDetails.Quantity;
                    obj.Price = objDetails.Price;
                    obj.CurrencyNo = objDetails.CurrencyNo;
                    obj.ReceivedDate = objDetails.ReceivedDate;
                    obj.Salesman = objDetails.Salesman;
                    obj.DatePromised = objDetails.DatePromised;
                    obj.Notes = objDetails.Notes;
                    obj.Instructions = objDetails.Instructions;
                    obj.Shortage = objDetails.Shortage;
                    obj.CompanyNo = objDetails.CompanyNo;
                    obj.ContactNo = objDetails.ContactNo;
                    obj.Alternate = objDetails.Alternate;
                    obj.OriginalCustomerRequirementNo = objDetails.OriginalCustomerRequirementNo;
                    obj.ReasonNo = objDetails.ReasonNo;
                    obj.ProductNo = objDetails.ProductNo;
                    obj.CustomerPart = objDetails.CustomerPart;
                    obj.Closed = objDetails.Closed;
                    obj.ROHS = objDetails.ROHS;
                    obj.UpdatedBy = objDetails.UpdatedBy;
                    obj.DLUP = objDetails.DLUP;
                    obj.UsageNo = objDetails.UsageNo;
                    obj.DisplayStatus = objDetails.DisplayStatus;
                    obj.SalesmanName = objDetails.SalesmanName;
                    obj.DivisionNo = objDetails.DivisionNo;
                    obj.TeamNo = objDetails.TeamNo;
                    obj.CompanyName = objDetails.CompanyName;
                    obj.CompanyOnStop = objDetails.CompanyOnStop;
                    obj.ContactName = objDetails.ContactName;
                    obj.CurrencyCode = objDetails.CurrencyCode;
                    obj.CurrencyDescription = objDetails.CurrencyDescription;
                    obj.ProductName = objDetails.ProductName;
                    obj.ProductDescription = objDetails.ProductDescription;
                    obj.ManufacturerName = objDetails.ManufacturerName;
                    obj.ManufacturerCode = objDetails.ManufacturerCode;
                    obj.PackageName = objDetails.PackageName;
                    obj.PackageDescription = objDetails.PackageDescription;
                    obj.UsageName = objDetails.UsageName;
                    obj.CustomerRequirementValue = objDetails.CustomerRequirementValue;
                    obj.ClosedReason = objDetails.ClosedReason;
                    obj.PartWatch = objDetails.PartWatch;
                    obj.BOM = objDetails.BOM;
                    obj.BOMName = objDetails.BOMName;
                    obj.DivisionName = objDetails.DivisionName;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }
        /// <summary>
        /// 
        /// [001] code start
        /// <summary>
        /// GetListForCustomerRequirement
        /// Calls [usp_selectAll_CustomerRequirement_for_CustomerRequirement]
        /// </summary>
        public static List<CustomerRequirement> GetListForCustomerRequirement(System.Int32? customerRequirementNo, System.Int32? clientID, System.Boolean? IsGSA = null)
        {
            List<CustomerRequirementDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.GetListForCustomerRequirement(customerRequirementNo, clientID, IsGSA);
            if (lstDetails == null)
            {
                return new List<CustomerRequirement>();
            }
            else
            {
                List<CustomerRequirement> lst = new List<CustomerRequirement>();
                foreach (CustomerRequirementDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.CustomerRequirement obj = new Rebound.GlobalTrader.BLL.CustomerRequirement();
                    obj.CustomerRequirementId = objDetails.CustomerRequirementId;
                    obj.CustomerRequirementNumber = objDetails.CustomerRequirementNumber;
                    obj.ClientNo = objDetails.ClientNo;
                    obj.FullPart = objDetails.FullPart;
                    obj.Part = objDetails.Part;
                    obj.ManufacturerNo = objDetails.ManufacturerNo;
                    obj.DateCode = objDetails.DateCode;
                    obj.PackageNo = objDetails.PackageNo;
                    obj.Quantity = objDetails.Quantity;
                    obj.Price = objDetails.Price;
                    obj.CurrencyNo = objDetails.CurrencyNo;
                    obj.ReceivedDate = objDetails.ReceivedDate;
                    obj.Salesman = objDetails.Salesman;
                    obj.DatePromised = objDetails.DatePromised;
                    obj.Notes = objDetails.Notes;
                    obj.Instructions = objDetails.Instructions;
                    obj.Shortage = objDetails.Shortage;
                    obj.CompanyNo = objDetails.CompanyNo;
                    obj.ContactNo = objDetails.ContactNo;
                    obj.Alternate = objDetails.Alternate;
                    obj.OriginalCustomerRequirementNo = objDetails.OriginalCustomerRequirementNo;
                    obj.ReasonNo = objDetails.ReasonNo;
                    obj.ProductNo = objDetails.ProductNo;
                    obj.CustomerPart = objDetails.CustomerPart;
                    obj.Closed = objDetails.Closed;
                    obj.ROHS = objDetails.ROHS;
                    obj.UpdatedBy = objDetails.UpdatedBy;
                    obj.DLUP = objDetails.DLUP;
                    obj.UsageNo = objDetails.UsageNo;
                    obj.DisplayStatus = objDetails.DisplayStatus;
                    obj.SalesmanName = objDetails.SalesmanName;
                    obj.DivisionNo = objDetails.DivisionNo;
                    obj.TeamNo = objDetails.TeamNo;
                    obj.CompanyName = objDetails.CompanyName;
                    obj.CompanyOnStop = objDetails.CompanyOnStop;
                    obj.ContactName = objDetails.ContactName;
                    obj.CurrencyCode = objDetails.CurrencyCode;
                    obj.CurrencyDescription = objDetails.CurrencyDescription;
                    obj.ProductName = objDetails.ProductName;
                    obj.ProductDescription = objDetails.ProductDescription;
                    obj.ManufacturerName = objDetails.ManufacturerName;
                    obj.ManufacturerCode = objDetails.ManufacturerCode;
                    obj.PackageName = objDetails.PackageName;
                    obj.PackageDescription = objDetails.PackageDescription;
                    obj.UsageName = objDetails.UsageName;
                    obj.CustomerRequirementValue = objDetails.CustomerRequirementValue;
                    obj.ClosedReason = objDetails.ClosedReason;
                    obj.PartWatch = objDetails.PartWatch;
                    obj.BOM = objDetails.BOM;
                    obj.BOMName = objDetails.BOMName;
                    obj.DivisionName = objDetails.DivisionName;
                    obj.BOMHeader = objDetails.BOMHeader;
                    obj.BOMNo = objDetails.BOMNo == null ? 0 : (int)objDetails.BOMNo;
                    obj.RequestToPOHubBy = objDetails.RequestToPOHubBy;
                    obj.FactorySealed = objDetails.FactorySealed;
                    obj.MSL = objDetails.MSL;
                    obj.PQA = objDetails.PQA;
                    obj.Obsolete = objDetails.Obsolete;
                    obj.LastTimeBuy = objDetails.LastTimeBuy;
                    obj.RefirbsAcceptable = objDetails.RefirbsAcceptable;
                    obj.TestingRequired = objDetails.TestingRequired;
                    obj.TargetSellPrice = objDetails.TargetSellPrice;
                    obj.CompetitorBestOffer = objDetails.CompetitorBestOffer;
                    obj.CustomerDecisionDate = objDetails.CustomerDecisionDate;
                    obj.RFQClosingDate = objDetails.RFQClosingDate;
                    obj.QuoteValidityRequired = objDetails.QuoteValidityRequired;
                    obj.Type = objDetails.Type;
                    obj.OrderToPlace = objDetails.OrderToPlace;
                    obj.RequirementforTraceability = objDetails.RequirementforTraceability;
                    obj.AlternateStatus = objDetails.AlternateStatus;
                    obj.SupportTeamMemberNo = objDetails.SupportTeamMemberNo;
                    obj.SupportTeamMemberName = objDetails.SupportTeamMemberName;

                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }
        /// <summary>
        // [001] code end

        /// GetListOpenForCompany
        /// Calls [usp_selectAll_CustomerRequirement_open_for_Company]
        /// </summary>
        public static List<CustomerRequirement> GetListOpenForCompany(System.Int32? companyId)
        {
            List<CustomerRequirementDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.GetListOpenForCompany(companyId);
            if (lstDetails == null)
            {
                return new List<CustomerRequirement>();
            }
            else
            {
                List<CustomerRequirement> lst = new List<CustomerRequirement>();
                foreach (CustomerRequirementDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.CustomerRequirement obj = new Rebound.GlobalTrader.BLL.CustomerRequirement();
                    obj.CustomerRequirementId = objDetails.CustomerRequirementId;
                    obj.CustomerRequirementNumber = objDetails.CustomerRequirementNumber;
                    obj.ClientNo = objDetails.ClientNo;
                    obj.FullPart = objDetails.FullPart;
                    obj.Part = objDetails.Part;
                    obj.ManufacturerNo = objDetails.ManufacturerNo;
                    obj.DateCode = objDetails.DateCode;
                    obj.PackageNo = objDetails.PackageNo;
                    obj.Quantity = objDetails.Quantity;
                    obj.Price = objDetails.Price;
                    obj.CurrencyNo = objDetails.CurrencyNo;
                    obj.ReceivedDate = objDetails.ReceivedDate;
                    obj.Salesman = objDetails.Salesman;
                    obj.DatePromised = objDetails.DatePromised;
                    obj.Notes = objDetails.Notes;
                    obj.Instructions = objDetails.Instructions;
                    obj.Shortage = objDetails.Shortage;
                    obj.CompanyNo = objDetails.CompanyNo;
                    obj.ContactNo = objDetails.ContactNo;
                    obj.Alternate = objDetails.Alternate;
                    obj.OriginalCustomerRequirementNo = objDetails.OriginalCustomerRequirementNo;
                    obj.ReasonNo = objDetails.ReasonNo;
                    obj.ProductNo = objDetails.ProductNo;
                    obj.CustomerPart = objDetails.CustomerPart;
                    obj.Closed = objDetails.Closed;
                    obj.ROHS = objDetails.ROHS;
                    obj.UpdatedBy = objDetails.UpdatedBy;
                    obj.DLUP = objDetails.DLUP;
                    obj.UsageNo = objDetails.UsageNo;
                    obj.DisplayStatus = objDetails.DisplayStatus;
                    obj.SalesmanName = objDetails.SalesmanName;
                    obj.DivisionNo = objDetails.DivisionNo;
                    obj.TeamNo = objDetails.TeamNo;
                    obj.CompanyName = objDetails.CompanyName;
                    obj.CompanyOnStop = objDetails.CompanyOnStop;
                    obj.ContactName = objDetails.ContactName;
                    obj.CurrencyCode = objDetails.CurrencyCode;
                    obj.CurrencyDescription = objDetails.CurrencyDescription;
                    obj.ProductName = objDetails.ProductName;
                    obj.ProductDescription = objDetails.ProductDescription;
                    obj.ManufacturerName = objDetails.ManufacturerName;
                    obj.ManufacturerCode = objDetails.ManufacturerCode;
                    obj.PackageName = objDetails.PackageName;
                    obj.PackageDescription = objDetails.PackageDescription;
                    obj.UsageName = objDetails.UsageName;
                    obj.CustomerRequirementValue = objDetails.CustomerRequirementValue;
                    obj.ClosedReason = objDetails.ClosedReason;
                    obj.PartWatch = objDetails.PartWatch;
                    obj.BOM = objDetails.BOM;
                    obj.BOMName = objDetails.BOMName;
                    obj.DivisionName = objDetails.DivisionName;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }
        /// <summary>
        /// GetListOpenForLogin
        /// Calls [usp_selectAll_CustomerRequirement_open_for_Login]
        /// </summary>
        public static List<CustomerRequirement> GetListOpenForLogin(System.Int32? loginId, System.Int32? topToSelect)
        {
            List<CustomerRequirementDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.GetListOpenForLogin(loginId, topToSelect);
            if (lstDetails == null)
            {
                return new List<CustomerRequirement>();
            }
            else
            {
                List<CustomerRequirement> lst = new List<CustomerRequirement>();
                foreach (CustomerRequirementDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.CustomerRequirement obj = new Rebound.GlobalTrader.BLL.CustomerRequirement();
                    obj.Status = objDetails.Status;
                    obj.CustomerRequirementNumber = objDetails.CustomerRequirementNumber;
                    obj.ReceivedDate = objDetails.ReceivedDate;
                    obj.CompanyNo = objDetails.CompanyNo;
                    obj.CompanyName = objDetails.CompanyName;
                    obj.Quantity = objDetails.Quantity;
                    obj.Part = objDetails.Part;
                    obj.ROHS = objDetails.ROHS;
                    obj.CreditLimit = objDetails.CreditLimit;
                    obj.Balance = objDetails.Balance;
                    obj.Salesman = objDetails.Salesman;
                    obj.DatePromised = objDetails.DatePromised;
                    obj.DaysOverdue = objDetails.DaysOverdue;
                    obj.CustomerRequirementId = objDetails.CustomerRequirementId;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }
        /// <summary>
        /// GetListOverdueForLogin
        /// Calls [usp_selectAll_CustomerRequirement_overdue_for_Login]
        /// </summary>
        public static List<CustomerRequirement> GetListOverdueForLogin(System.Int32? loginId, System.Int32? topToSelect)
        {
            List<CustomerRequirementDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.GetListOverdueForLogin(loginId, topToSelect);
            if (lstDetails == null)
            {
                return new List<CustomerRequirement>();
            }
            else
            {
                List<CustomerRequirement> lst = new List<CustomerRequirement>();
                foreach (CustomerRequirementDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.CustomerRequirement obj = new Rebound.GlobalTrader.BLL.CustomerRequirement();
                    obj.Status = objDetails.Status;
                    obj.CustomerRequirementNumber = objDetails.CustomerRequirementNumber;
                    obj.ReceivedDate = objDetails.ReceivedDate;
                    obj.CompanyNo = objDetails.CompanyNo;
                    obj.CompanyName = objDetails.CompanyName;
                    obj.Quantity = objDetails.Quantity;
                    obj.Part = objDetails.Part;
                    obj.ROHS = objDetails.ROHS;
                    obj.CreditLimit = objDetails.CreditLimit;
                    obj.Balance = objDetails.Balance;
                    obj.Salesman = objDetails.Salesman;
                    obj.DatePromised = objDetails.DatePromised;
                    obj.DaysOverdue = objDetails.DaysOverdue;
                    obj.CustomerRequirementId = objDetails.CustomerRequirementId;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }
        /// <summary>
        /// Source
        /// Calls [usp_source_CustomerRequirement]
        /// </summary>
        public static List<CustomerRequirement> Source(System.Int32? clientId, System.String partSearch, System.Int32? index, DateTime? maxDate, out DateTime? outDate, System.Boolean? blnReferesh, bool IsServerLocal)
        {
            DateTime? StartDate = null;
            DateTime? EndDate = null;
            //maxDate = DateTime.Today;
            //if (index == 2 && maxDate.HasValue)
            //{
            //    StartDate = (!blnReferesh.Value) ? maxDate.Value.AddMonths(-6) : maxDate.Value.AddMonths(-12);
            //    EndDate = maxDate.Value;
            //}
            //else if (index == 3 && maxDate.HasValue)
            //{
            //    StartDate = maxDate.Value.AddMonths(-36);
            //    EndDate = maxDate.Value;
            //}
            List<CustomerRequirementDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.Source(clientId, partSearch, index, StartDate, EndDate, out outDate, IsServerLocal);
            if (lstDetails == null)
            {
                return new List<CustomerRequirement>();
            }
            else
            {
                List<CustomerRequirement> lst = new List<CustomerRequirement>();
                foreach (CustomerRequirementDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.CustomerRequirement obj = new Rebound.GlobalTrader.BLL.CustomerRequirement();
                    obj.ClientNo = objDetails.ClientNo;
                    obj.ClientName = objDetails.ClientName;
                    obj.CustomerRequirementId = objDetails.CustomerRequirementId;
                    obj.CustomerRequirementNumber = objDetails.CustomerRequirementNumber;
                    obj.Part = objDetails.Part;
                    obj.Quantity = objDetails.Quantity;
                    obj.ROHS = objDetails.ROHS;
                    obj.CompanyNo = objDetails.CompanyNo;
                    obj.CompanyName = objDetails.CompanyName;
                    obj.ReceivedDate = objDetails.ReceivedDate;
                    obj.Price = objDetails.Price;
                    obj.CurrencyNo = objDetails.CurrencyNo;
                    obj.CurrencyCode = objDetails.CurrencyCode;
                    obj.ManufacturerNo = objDetails.ManufacturerNo;
                    obj.ManufacturerCode = objDetails.ManufacturerCode;
                    obj.ProductName = objDetails.ProductName;
                    obj.PackageName = objDetails.PackageName;
                    obj.CustomerPart = objDetails.CustomerPart;
                    obj.DateCode = objDetails.DateCode;
                    obj.Salesman = objDetails.Salesman;
                    obj.SalesmanName = objDetails.SalesmanName;
                    obj.ClientCode = objDetails.ClientCode;
                    //[002] start
                    obj.BOMNo = objDetails.BOMNo ?? 0;
                    obj.BOMName = objDetails.BOMName;
                    //[002] end
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }

        //code aded by anand for Customer Requirement table
        /// <summary>
        /// Source
        /// Calls [usp_CrossMatch_CustomerRequirement]
        /// </summary>
        public static List<CustomerRequirement> SourceCustomerRequirement(System.Int32? clientId, System.Int32? pageIndex, System.Int32? pageSize, System.Int32? orderBy, System.String sortDir, System.String partSearch, System.String PartMatch, System.String months, System.Int32? monthTime, System.Int32? vendorNo, System.Int32? currencyNo, System.Boolean? isManufaurer, System.Int32? NoOfTopRecord, bool IsServerLocal, System.Boolean? isPohub, System.Int32? BomID, System.Boolean? IncludeAltPart, System.Int32? ReqId)
        {

            List<CustomerRequirementDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.SourceCustomerRequirement(clientId, pageIndex, pageSize, orderBy, sortDir, partSearch, PartMatch, months, monthTime, vendorNo, currencyNo, isManufaurer, NoOfTopRecord, IsServerLocal, isPohub, BomID, IncludeAltPart, ReqId);
            if (lstDetails == null)
            {
                return new List<CustomerRequirement>();
            }
            else
            {
                List<CustomerRequirement> lst = new List<CustomerRequirement>();
                foreach (CustomerRequirementDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.CustomerRequirement obj = new Rebound.GlobalTrader.BLL.CustomerRequirement();
                    obj.ClientNo = objDetails.ClientNo;
                    obj.ClientName = objDetails.ClientName;
                    obj.CustomerRequirementId = objDetails.CustomerRequirementId;
                    obj.CustomerRequirementNumber = objDetails.CustomerRequirementNumber;
                    obj.Part = objDetails.Part;
                    obj.Quantity = objDetails.Quantity;
                    obj.ROHS = objDetails.ROHS;
                    obj.CompanyNo = objDetails.CompanyNo;
                    obj.CompanyName = objDetails.CompanyName;
                    obj.ReceivedDate = objDetails.ReceivedDate;
                    obj.Price = objDetails.Price;
                    obj.CurrencyNo = objDetails.CurrencyNo;
                    obj.CurrencyCode = objDetails.CurrencyCode;
                    obj.ManufacturerNo = objDetails.ManufacturerNo;
                    obj.ManufacturerCode = objDetails.ManufacturerCode;
                    obj.ProductName = objDetails.ProductName;
                    obj.PackageName = objDetails.PackageName;
                    obj.CustomerPart = objDetails.CustomerPart;
                    obj.DateCode = objDetails.DateCode;
                    obj.Salesman = objDetails.Salesman;
                    obj.SalesmanName = objDetails.SalesmanName;
                    obj.ClientCode = objDetails.ClientCode;
                    //[002] start
                    obj.BOMNo = objDetails.BOMNo ?? 0;
                    obj.BOMName = objDetails.BOMName;
                    obj.isIncludeAltPart = objDetails.isIncludeAltPart;
                    obj.RowNum = objDetails.RowNum;
                    obj.TotalCount = objDetails.TotalCount;
                    //[002] end
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }
        //code end
        public static Int32 InsertExpedite(System.Int32? HUBRFQId, System.String expediteNotes, System.Int32? UpdatedBy, string ReqIds, System.Int32? emailSendTo, System.String CCUserID, System.String SendToGroup)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.InsertExpedite(HUBRFQId, expediteNotes, UpdatedBy, ReqIds, emailSendTo, CCUserID, SendToGroup);
        }

        public static Int32 InsertBOMExpediteNote(System.Int32? BOMId, System.String expediteNotes, System.Int32? UpdatedBy, string ReqIds, System.Int32? emailSendTo, System.String CCUserID, System.String SendToGroup)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.InsertBOMExpediteNote(BOMId, expediteNotes, UpdatedBy, ReqIds, emailSendTo, CCUserID, SendToGroup);
        }

        public static Int32 InsertHUBRFQExpedite(System.Int32? HUBRFQId, System.String expediteNotes, System.Int32? UpdatedBy, System.Int32? emailSendTo)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.InsertHUBRFQExpedite(HUBRFQId, expediteNotes, UpdatedBy, emailSendTo);
        }

        /// <summary>
        /// Update
        /// Calls [usp_update_CustomerRequirement]
        /// </summary>
        public static bool Update(System.Int32? customerRequirementId, System.String part, System.Int32? manufacturerNo, System.String dateCode, System.Int32? packageNo, System.Int32? quantity, System.Double? price, System.Int32? currencyNo, System.DateTime? receivedDate, System.Int32? salesman, System.DateTime? datePromised, System.String notes, System.String instructions, System.Boolean? shortage, System.Int32? companyNo, System.Int32? contactNo, System.Int32? usageNo, System.Boolean? alternate, System.Int32? originalCustomerRequirementNo, System.Int32? reasonNo, System.Int32? productNo, System.String customerPart, System.Boolean? closed, System.Byte? rohs, System.Int32? updatedBy, System.Boolean? partWatch, System.Boolean? bom, System.String bomName, System.Int32? BOMNo, System.Boolean? FactorySealed, System.String MSL, System.Boolean? PQA, System.Boolean? ObsoleteChk, System.Boolean? LastTimeBuyChk, System.Boolean? RefirbsAcceptableChk, System.Boolean? TestingRequiredChk, System.Double? TargetSellPrice, System.Double? CompetitorBestOffer, System.DateTime? CustomerDecisionDate, System.DateTime? RFQClosingDate, System.Int32? QuoteValidityRequired, System.Int32? Type, System.Boolean? OrderToPlace, System.Int32? RequirementforTraceability, System.String EAU, System.Boolean? AlternativesAccepted, System.Boolean? RepeatBusiness, System.Int32? SupportTeamMemberNo, System.String CountryOfOrigin, System.Int32? CountryOfOriginNo, System.String LifeCycleStage, System.String HTSCode, System.Double? AveragePrice, System.String Packaging, System.String PackagingSize, System.String Descriptions, System.Int32? IHSPartsId, System.String IHSCurrencyCode, System.String IHSProduct, System.String ECCNCode, System.Int32? ECCNNo, System.Boolean? AS6081)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.Update(customerRequirementId, part, manufacturerNo, dateCode, packageNo, quantity, price, currencyNo, receivedDate, salesman, datePromised, notes, instructions, shortage, companyNo, contactNo, usageNo, alternate, originalCustomerRequirementNo, reasonNo, productNo, customerPart, closed, rohs, updatedBy, partWatch, bom, bomName, BOMNo, FactorySealed, MSL, PQA, ObsoleteChk, LastTimeBuyChk, RefirbsAcceptableChk, TestingRequiredChk, TargetSellPrice, CompetitorBestOffer, CustomerDecisionDate, RFQClosingDate, QuoteValidityRequired, Type, OrderToPlace, RequirementforTraceability, EAU, AlternativesAccepted, RepeatBusiness, SupportTeamMemberNo, CountryOfOrigin, CountryOfOriginNo, LifeCycleStage, HTSCode, AveragePrice, Packaging, PackagingSize, Descriptions, IHSPartsId, IHSCurrencyCode, IHSProduct, ECCNCode, ECCNNo, AS6081);
        }
        /// <summary>
        /// Update (without parameters)
        /// Calls [usp_update_CustomerRequirement]
        /// </summary>
        public bool Update()
        {
            //return Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.Update(CustomerRequirementId, Part, ManufacturerNo, DateCode, PackageNo, Quantity, Price, CurrencyNo, ReceivedDate, Salesman, DatePromised, Notes, Instructions, Shortage, CompanyNo, ContactNo, UsageNo, Alternate, OriginalCustomerRequirementNo, ReasonNo, ProductNo, CustomerPart, Closed, ROHS, UpdatedBy, PartWatch, BOM, BOMName, BOMNo, FactorySealed, MSL, PQA, Obsolete, LastTimeBuy, RefirbsAcceptable, TestingRequired, TargetSellPrice, CompetitorBestOffer, CustomerDecisionDate, RFQClosingDate, QuoteValidityRequired, Type, OrderToPlace, RequirementforTraceability, EAU, AlternativesAccepted, RepeatBusiness, SupportTeamMemberNo);
            return Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.Update(CustomerRequirementId, Part, ManufacturerNo, DateCode, PackageNo, Quantity, Price, CurrencyNo, ReceivedDate, Salesman, DatePromised, Notes, Instructions, Shortage, CompanyNo, ContactNo, UsageNo, Alternate, OriginalCustomerRequirementNo, ReasonNo, ProductNo, CustomerPart, Closed, ROHS, UpdatedBy, PartWatch, BOM, BOMName, BOMNo, FactorySealed, MSL, PQA, Obsolete, LastTimeBuy, RefirbsAcceptable, TestingRequired, TargetSellPrice, CompetitorBestOffer, CustomerDecisionDate, RFQClosingDate, QuoteValidityRequired, Type, OrderToPlace, RequirementforTraceability, EAU, AlternativesAccepted, RepeatBusiness, SupportTeamMemberNo,
                CountryOfOrigin, CountryOfOriginNo, LifeCycleStage, HTSCode, AveragePrice, Packaging, PackagingSize, Descriptions, IHSPartsId, IHSCurrencyCode, IHSProduct, ECCNCode, ECCNNo, AS6081);
        }
        //public static bool Update(System.Int32? customerRequirementId, System.String part, System.Int32? manufacturerNo, System.String dateCode, System.Int32? packageNo, System.Int32? quantity, System.Double? price, System.Int32? currencyNo, System.DateTime? receivedDate, System.Int32? salesman, System.DateTime? datePromised, System.String notes, System.String instructions, System.Boolean? shortage, System.Int32? companyNo, System.Int32? contactNo, System.Int32? usageNo, System.Boolean? alternate, System.Int32? originalCustomerRequirementNo, System.Int32? reasonNo, System.Int32? productNo, System.String customerPart, System.Boolean? closed, System.Byte? rohs, System.Int32? updatedBy, System.Boolean? partWatch, System.Boolean? bom, System.String bomName, System.Int32? BOMNo, System.Boolean? FactorySealed, System.String MSL, System.Boolean? PQA, System.Boolean? ObsoleteChk, System.Boolean? LastTimeBuyChk, System.Boolean? RefirbsAcceptableChk, System.Boolean? TestingRequiredChk, System.Double? TargetSellPrice, System.Double? CompetitorBestOffer, System.DateTime? CustomerDecisionDate, System.DateTime? RFQClosingDate, System.Int32? QuoteValidityRequired, System.Int32? Type, System.Boolean? OrderToPlace, System.Int32? RequirementforTraceability, System.String EAU, System.Boolean? AlternativesAccepted, System.Boolean? RepeatBusiness, System.Int32? SupportTeamMemberNo, System.String CountryOfOrigin, System.Int32? CountryOfOriginNo, System.String LifeCycleStage, System.String HTSCode, System.Double? AveragePrice, System.String Packaging, System.String PackagingSize)
        //{
        //    return Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.Update(customerRequirementId, part, manufacturerNo, dateCode, packageNo, quantity, price, currencyNo, receivedDate, salesman, datePromised, notes, instructions, shortage, companyNo, contactNo, usageNo, alternate, originalCustomerRequirementNo, reasonNo, productNo, customerPart, closed, rohs, updatedBy, partWatch, bom, bomName, BOMNo, FactorySealed, MSL, PQA, ObsoleteChk, LastTimeBuyChk, RefirbsAcceptableChk, TestingRequiredChk, TargetSellPrice, CompetitorBestOffer, CustomerDecisionDate, RFQClosingDate, QuoteValidityRequired, Type, OrderToPlace, RequirementforTraceability, EAU, AlternativesAccepted, RepeatBusiness, SupportTeamMemberNo, CountryOfOrigin, CountryOfOriginNo, LifeCycleStage, HTSCode, AveragePrice, Packaging, PackagingSize);
        //}

        /// <summary>
        /// Update Bomid in Customer Requirements
        /// Calls [usp_update_CustRequirementByBomID]
        /// </summary>
        public static bool Update(System.Int32? bomID, System.Int32? updatedBy, System.Int32? clientNo, System.String ReqsId, System.Int32? bomStatus)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.Update(bomID, updatedBy, clientNo, ReqsId, bomStatus);
        }

        /// <summary>
        /// ChangeAltStatus
        /// Calls [usp_update_CustReqAlternate_Status]
        /// </summary>
        public static bool ChangeAltStatus(System.Int32? customerRequirementId, System.Byte? altStatus, System.Int32? updatedBy)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.ChangeAltStatus(customerRequirementId, altStatus, updatedBy);
        }

        public static bool UpdateClose(System.Int32? customerRequirementId, System.Boolean? includeAllRelatedAlternates, System.Int32? reasonNo, System.Int32? updatedBy)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.UpdateClose(customerRequirementId, includeAllRelatedAlternates, reasonNo, updatedBy);
        }

        /// <summary>
        /// Delete
        /// Calls [[usp_delete_CustomerReq_AlternatePart]]
        /// </summary>
        public static bool DeleteAlternateParts(System.Int32? AlternatePartid)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.DeleteAlternateParts(AlternatePartid);
        }


        /// <summary>
        /// Update ClientBomid in Customer Requirements
        /// Calls [usp_update_CustRequirementByClientBomID]
        /// </summary>
        public static bool ClientBOMItemsUpdate(System.Int32? bomID, System.Int32? updatedBy, System.Int32? clientNo, System.String ReqsId, System.Int32? bomStatus)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.ClientBOMItemsUpdate(bomID, updatedBy, clientNo, ReqsId, bomStatus);
        }
        private static CustomerRequirement PopulateFromDBDetailsObject(CustomerRequirementDetails obj)
        {
            CustomerRequirement objNew = new CustomerRequirement();
            objNew.CustomerRequirementId = obj.CustomerRequirementId;
            objNew.CustomerRequirementNumber = obj.CustomerRequirementNumber;
            objNew.ClientNo = obj.ClientNo;
            objNew.FullPart = obj.FullPart;
            objNew.Part = obj.Part;
            objNew.ManufacturerNo = obj.ManufacturerNo;
            objNew.DateCode = obj.DateCode;
            objNew.PackageNo = obj.PackageNo;
            objNew.Quantity = obj.Quantity;
            objNew.Price = obj.Price;
            objNew.CurrencyNo = obj.CurrencyNo;
            objNew.ReceivedDate = obj.ReceivedDate;
            objNew.Salesman = obj.Salesman;
            objNew.DatePromised = obj.DatePromised;
            objNew.Notes = obj.Notes;
            objNew.Instructions = obj.Instructions;
            objNew.Shortage = obj.Shortage;
            objNew.CompanyNo = obj.CompanyNo;
            objNew.ContactNo = obj.ContactNo;
            objNew.Alternate = obj.Alternate;
            objNew.OriginalCustomerRequirementNo = obj.OriginalCustomerRequirementNo;
            objNew.ReasonNo = obj.ReasonNo;
            objNew.ProductNo = obj.ProductNo;
            objNew.CustomerPart = obj.CustomerPart;
            objNew.Closed = obj.Closed;
            objNew.ROHS = obj.ROHS;
            objNew.UpdatedBy = obj.UpdatedBy;
            objNew.DLUP = obj.DLUP;
            objNew.UsageNo = obj.UsageNo;
            objNew.FullCustomerPart = obj.FullCustomerPart;
            objNew.BOM = obj.BOM;
            objNew.BOMName = obj.BOMName;
            objNew.PartWatch = obj.PartWatch;
            objNew.SalesmanName = obj.SalesmanName;
            objNew.ManufacturerCode = obj.ManufacturerCode;
            objNew.CompanyName = obj.CompanyName;
            objNew.ContactName = obj.ContactName;
            objNew.RowNum = obj.RowNum;
            objNew.RowCnt = obj.RowCnt;
            objNew.CurrencyCode = obj.CurrencyCode;
            objNew.DisplayStatus = obj.DisplayStatus;
            objNew.DivisionNo = obj.DivisionNo;
            objNew.TeamNo = obj.TeamNo;
            objNew.CompanyOnStop = obj.CompanyOnStop;
            objNew.CurrencyDescription = obj.CurrencyDescription;
            objNew.ProductName = obj.ProductName;
            objNew.ProductDescription = obj.ProductDescription;
            objNew.ManufacturerName = obj.ManufacturerName;
            objNew.PackageName = obj.PackageName;
            objNew.PackageDescription = obj.PackageDescription;
            objNew.UsageName = obj.UsageName;
            objNew.CustomerRequirementValue = obj.CustomerRequirementValue;
            objNew.ClosedReason = obj.ClosedReason;
            objNew.DivisionName = obj.DivisionName;
            objNew.Status = obj.Status;
            objNew.CreditLimit = obj.CreditLimit;
            objNew.Balance = obj.Balance;
            objNew.DaysOverdue = obj.DaysOverdue;
            objNew.ClientName = obj.ClientName;
            return objNew;
        }

        public static List<CustomerRequirement> DataListNuggetPrint(System.Int32? clientId, System.Int32? loginId, System.Int32? orderBy, System.Int32? sortDir, System.Int32? pageIndex, System.Int32? pageSize, System.String cmSearch, System.Int32? salesmanNo, System.Int32? companyNo, System.DateTime? receivedDateFrom, System.DateTime? receivedDateTo, System.DateTime? datePromisedFrom, System.DateTime? datePromisedTo, System.Int32? contactNo)
        {
            List<CustomerRequirementDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.DataListNuggetPrint(clientId, loginId, orderBy, sortDir, pageIndex, pageSize, cmSearch, salesmanNo, companyNo, receivedDateFrom, receivedDateTo, datePromisedFrom, datePromisedTo, contactNo);
            if (lstDetails == null)
            {
                return new List<CustomerRequirement>();
            }
            else
            {
                List<CustomerRequirement> lst = new List<CustomerRequirement>();
                foreach (CustomerRequirementDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.CustomerRequirement obj = new Rebound.GlobalTrader.BLL.CustomerRequirement();
                    obj.CustomerRequirementId = objDetails.CustomerRequirementId;
                    obj.CustomerRequirementNumber = objDetails.CustomerRequirementNumber;
                    obj.Salesman = objDetails.Salesman;
                    obj.Part = objDetails.Part;
                    obj.ROHS = objDetails.ROHS;
                    obj.SalesmanName = objDetails.SalesmanName;
                    obj.ManufacturerNo = objDetails.ManufacturerNo;
                    obj.ManufacturerCode = objDetails.ManufacturerCode;
                    obj.Quantity = objDetails.Quantity;
                    obj.CompanyNo = objDetails.CompanyNo;
                    obj.CompanyName = objDetails.CompanyName;
                    obj.ContactNo = objDetails.ContactNo;
                    obj.ContactName = objDetails.ContactName;
                    obj.ReceivedDate = objDetails.ReceivedDate;
                    obj.DatePromised = objDetails.DatePromised;
                    obj.RowNum = objDetails.RowNum;
                    obj.RowCnt = objDetails.RowCnt;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }
        //start [005]
        public static List<CustomerRequirement> DataListNuggetImport(System.Int32? clientId, System.Int32? loginId, System.Int32? orderBy, System.Int32? sortDir, System.Int32? pageIndex, System.Int32? pageSize, System.String clientBOMName, System.Int32? salesmanNo, System.String companyNo, System.DateTime? importDateFrom, System.DateTime? importDateTo, System.Int32? status)
        {
            List<CustomerRequirementDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.DataListNuggetImport(clientId, loginId, orderBy, sortDir, pageIndex, pageSize, clientBOMName, salesmanNo, companyNo, importDateFrom, importDateTo, status);
            if (lstDetails == null)
            {
                return new List<CustomerRequirement>();
            }
            else
            {
                List<CustomerRequirement> lst = new List<CustomerRequirement>();
                foreach (CustomerRequirementDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.CustomerRequirement obj = new Rebound.GlobalTrader.BLL.CustomerRequirement();
                    obj.ClientBOMId = objDetails.ClientBOMId;
                    obj.ClientBOMCode = objDetails.ClientBOMCode;
                    obj.ClientBOMName = objDetails.ClientBOMName;
                    obj.SalesmanName = objDetails.SalesmanName;
                    obj.CompanyName = objDetails.CompanyName;
                    obj.ContactNo = objDetails.ContactNo;
                    obj.ContactName = objDetails.ContactName;
                    obj.ImportDate = objDetails.ImportDate;
                    obj.NoOfRequirements = objDetails.NoOfRequirements;
                    obj.Status = objDetails.Status;
                    obj.RowNum = objDetails.RowNum;
                    obj.RowCnt = objDetails.RowCnt;
                    obj.RecordsProcessed = objDetails.RecordsProcessed;
                    obj.RecordsRemaining = objDetails.RecordsRemaining;
                    obj.BOMNo = objDetails.BOMNo ?? 0;
                    obj.BOMName = objDetails.BOMName;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }
        //end [005]
        /// <summary>
        /// usp_Print_CustomerRequirement_Enquiry_Form
        /// </summary>
        /// <param name="xmlReqNo"></param>
        /// <returns></returns>
        public static List<CustomerRequirement> GetForPrint(System.String xmlReqNo)
        {
            List<CustomerRequirementDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.GetForPrint(xmlReqNo);
            if (lstDetails == null)
            {
                return new List<CustomerRequirement>();
            }
            else
            {
                List<CustomerRequirement> lst = new List<CustomerRequirement>();
                foreach (CustomerRequirementDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.CustomerRequirement obj = new Rebound.GlobalTrader.BLL.CustomerRequirement();
                    obj.CustomerRequirementId = objDetails.CustomerRequirementId;
                    obj.CustomerRequirementNumber = objDetails.CustomerRequirementNumber;
                    obj.ManufacturerNo = objDetails.ManufacturerNo;
                    obj.ManufacturerCode = objDetails.ManufacturerCode;
                    obj.Quantity = objDetails.Quantity;
                    obj.ReceivedDate = objDetails.ReceivedDate;
                    obj.DatePromised = objDetails.DatePromised;
                    obj.CompanyNo = objDetails.CompanyNo;
                    obj.ContactNo = objDetails.ContactNo;
                    obj.SalesmanName = objDetails.SalesmanName;
                    obj.ManufacturerCode = objDetails.ManufacturerCode;
                    obj.CompanyName = objDetails.CompanyName;
                    obj.ContactName = objDetails.ContactName;
                    obj.ManufacturerName = objDetails.ManufacturerName;
                    obj.PackageName = objDetails.PackageName;
                    obj.ProductName = objDetails.ProductName;
                    obj.DateCode = objDetails.DateCode;
                    obj.Price = objDetails.Price;
                    obj.PackageDescription = objDetails.PackageDescription;
                    obj.ProductDescription = objDetails.ProductDescription;
                    obj.Alternate = objDetails.Alternate;
                    obj.Part = objDetails.Part;
                    obj.RowCnt = objDetails.RowCnt;
                    obj.CustomerPart = objDetails.CustomerPart;
                    obj.AlternateStatus = objDetails.AlternateStatus;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }


        }


        /// <summary>[008]
        /// Update BomNo with HUBRFQ in Customer Requirements
        /// Calls [usp_update_CustomerRequirementWithHUBRFQ]
        /// </summary>
        public static bool ClientBOMCustomerRequirementWithHUBRFQUpdate(System.Int32? bomID, System.Int32? CompanyNo, System.Int32? CustomerRequirementNumber, System.Int32? clientNo)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.ClientBOMCustomerRequirementWithHUBRFQUpdate(bomID, CompanyNo, CustomerRequirementNumber, clientNo);
        }



        public static List<CustomerRequirement> GetListCustomerAllInfo(System.String customerRequirementNo, System.String actionType)
        {
            List<CustomerRequirementDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.GetListCustomerAllInfo(customerRequirementNo, actionType);
            if (lstDetails == null)
            {
                return new List<CustomerRequirement>();
            }
            else
            {
                List<CustomerRequirement> lst = new List<CustomerRequirement>();
                foreach (CustomerRequirementDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.CustomerRequirement obj = new Rebound.GlobalTrader.BLL.CustomerRequirement();
                    obj.ID = objDetails.ID;
                    obj.Number = objDetails.Number;
                    obj.ResultType = objDetails.ResultType;

                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }
        //[004] end
        public static DataTable GetExcelHeader(System.Int32 clientBomId, System.Int32 userId)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.GetExcelHeader(clientBomId, userId);
        }
        public static DataTable GetCurrencyCode(string currencyCode)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.GetCurrencyCode(currencyCode);
        }
        //start code by Manish
        public static DataTable GetClientCurrencyCode(string currencyCode, System.Int32? ClientNo)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.GetClientCurrencyCode(currencyCode, ClientNo);
        }
        //start code by umendra
        public static void SaveUpdateTempCusReqData(int tempid, int bomid, int clientid, string manufacturename)
        {
            Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.SaveUpdateTempCusReqData(tempid, bomid, clientid, manufacturename);
        }
        public static void SaveUpdateTempSourcingData(int sourcingresultid, string suppliername, int clientid, string manufacturename)
        {
            Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.SaveUpdateTempSourcingData(sourcingresultid, suppliername, clientid, manufacturename);
        }
        //end code by umendra
        /// <summary>
        /// Clone the existing customer requirement and also add existing HUBRFQ
        /// Calls [usp_CloneRequirementDataHUBRFQ]
        /// </summary>
        /// <param name="customerRequirementNumber"></param>
        /// <param name="hubrfqId"></param>
        /// <param name="clientNo"></param>
        /// <param name="loginId"></param>
        /// <returns></returns>
        //public static Int32 CloneRequirementDataHUBRFQ(System.Int32 customerRequirementNumber, System.Int32 hubrfqId, System.Int32? clientNo, System.Int32 loginId)
        //{
        //    Int32 objReturn = Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.CloneRequirementDataHUBRFQ(customerRequirementNumber, hubrfqId, clientNo, loginId);
        //    return objReturn;
        //}
        /// <summary>
        /// Calls [usp_CloneRequirementDataHUB]
        /// </summary>
        /// <param name="customerRequirementNumber"></param>
        /// <param name="clientNo"></param>
        /// <param name="loginId"></param>
        /// <returns></returns>
        //public static Int32 CloneRequirementDataHUB(System.Int32 customerRequirementNumber, System.Int32? clientNo, System.Int32 loginId)
        //{
        //    Int32 objReturn = Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.CloneRequirementDataHUB(customerRequirementNumber, clientNo, loginId);
        //    return objReturn;
        //}

        /// <summary>
        /// Insert
        /// Calls [usp_insert_CloneRequirementDataHUBRFQ]
        /// </summary>
        public static Int32 CloneRequirementDataHUBRFQ(System.Int32? clientNo, System.String part, System.Int32? manufacturerNo, System.String dateCode, System.Int32? packageNo,
            System.Int32? quantity,
            System.Double? price, System.Int32? currencyNo, System.DateTime? receivedDate, System.Int32? salesman, System.DateTime? datePromised,
            System.String notes, System.String instructions, System.Boolean? shortage, System.Int32? companyNo, System.Int32? contactNo, System.Int32?
            usageNo, System.Boolean? alternate, System.Int32? originalCustomerRequirementNo, System.Int32? reasonNo,
            System.Int32? productNo, System.String customerPart, System.Boolean? closed, System.Byte? rohs, System.Int32? updatedBy,
            System.Boolean? partWatch, System.Boolean? bom, System.String bomName, System.Int32? BOMNo, System.Boolean? FactorySealed,
            System.String MSL, System.Boolean? PQA, System.Boolean? ObsoleteChk, System.Boolean? LastTimeBuyChk, System.Boolean? RefirbsAcceptableChk,
            System.Boolean? TestingRequiredChk, System.Double? TargetSellPrice, System.Double? CompetitorBestOffer, System.DateTime? CustomerDecisionDate, System.DateTime? RFQClosingDate, System.Int32? QuoteValidityRequired, System.Int32? Type, System.Boolean? OrderToPlace, System.Int32? RequirementforTraceability,
            System.String EAU, System.Boolean? AlternativesAccepted, System.Boolean? RepeatBusiness, System.Int32? SupportTeamMemberNo, System.Int32? CustReqNo,
            System.String CountryOfOrigin, System.Int32? CountryOfOriginNo, System.String LifeCycleStage, System.String HTSCode, System.Double? AveragePrice, System.String Packaging, System.String PackagingSize, System.String Descriptions, System.Int32? IHSPartsId, System.String IHSCurrencyCode, System.String IHSProduct, System.String ECCNCode, System.Int32? ECCNNo)

        {
            Int32 objReturn = Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.CloneRequirementDataHUBRFQ(clientNo, part, manufacturerNo, dateCode,
                packageNo, quantity, price, currencyNo, receivedDate, salesman, datePromised, notes, instructions, shortage, companyNo,
                contactNo, usageNo, alternate, originalCustomerRequirementNo, reasonNo, productNo, customerPart, closed, rohs, updatedBy,
                partWatch, bom, bomName, BOMNo, FactorySealed, MSL, PQA, ObsoleteChk, LastTimeBuyChk, RefirbsAcceptableChk, TestingRequiredChk,
                TargetSellPrice, CompetitorBestOffer, CustomerDecisionDate, RFQClosingDate, QuoteValidityRequired, Type, OrderToPlace, RequirementforTraceability,
                EAU, AlternativesAccepted, RepeatBusiness, SupportTeamMemberNo, CustReqNo, CountryOfOrigin, CountryOfOriginNo, LifeCycleStage, HTSCode, AveragePrice, Packaging, PackagingSize, Descriptions, IHSPartsId, IHSCurrencyCode, IHSProduct, ECCNCode, ECCNNo);
            return objReturn;
        }

        /// <summary>
        /// Insert
        /// Calls [usp_insert_CloneRequirementDataHUB]
        /// </summary>
        public static Int32 CloneRequirementDataHUB(System.Int32? clientNo, System.String part, System.Int32? manufacturerNo, System.String dateCode, System.Int32? packageNo,
            System.Int32? quantity,
            System.Double? price, System.Int32? currencyNo, System.DateTime? receivedDate, System.Int32? salesman, System.DateTime? datePromised,
            System.String notes, System.String instructions, System.Boolean? shortage, System.Int32? companyNo, System.Int32? contactNo, System.Int32?
            usageNo, System.Boolean? alternate, System.Int32? originalCustomerRequirementNo, System.Int32? reasonNo,
            System.Int32? productNo, System.String customerPart, System.Boolean? closed, System.Byte? rohs, System.Int32? updatedBy,
            System.Boolean? partWatch, System.Boolean? bom, System.String bomName, System.Int32? BOMNo, System.Boolean? FactorySealed,
            System.String MSL, System.Boolean? PQA, System.Boolean? ObsoleteChk, System.Boolean? LastTimeBuyChk, System.Boolean? RefirbsAcceptableChk,
            System.Boolean? TestingRequiredChk, System.Double? TargetSellPrice, System.Double? CompetitorBestOffer, System.DateTime? CustomerDecisionDate, System.DateTime? RFQClosingDate, System.Int32? QuoteValidityRequired, System.Int32? Type, System.Boolean? OrderToPlace, System.Int32? RequirementforTraceability,
            System.String EAU, System.Boolean? AlternativesAccepted, System.Boolean? RepeatBusiness, System.Int32? SupportTeamMemberNo, System.Int32? CustReqNo,
            System.String CountryOfOrigin, System.Int32? CountryOfOriginNo, System.String LifeCycleStage, System.String HTSCode, System.Double? AveragePrice, System.String Packaging, System.String PackagingSize, System.String Descriptions, System.Int32? IHSPartsId, System.String IHSCurrencyCode, System.String IHSProduct, System.String ECCNCode, System.Int32? ECCNNo)

        {
            Int32 objReturn = Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.CloneRequirementDataHUB(clientNo, part, manufacturerNo, dateCode,
                packageNo, quantity, price, currencyNo, receivedDate, salesman, datePromised, notes, instructions, shortage, companyNo,
                contactNo, usageNo, alternate, originalCustomerRequirementNo, reasonNo, productNo, customerPart, closed, rohs, updatedBy,
                partWatch, bom, bomName, BOMNo, FactorySealed, MSL, PQA, ObsoleteChk, LastTimeBuyChk, RefirbsAcceptableChk, TestingRequiredChk,
                TargetSellPrice, CompetitorBestOffer, CustomerDecisionDate, RFQClosingDate, QuoteValidityRequired, Type, OrderToPlace, RequirementforTraceability,
                EAU, AlternativesAccepted, RepeatBusiness, SupportTeamMemberNo, CustReqNo, CountryOfOrigin, CountryOfOriginNo, LifeCycleStage, HTSCode, AveragePrice, Packaging, PackagingSize, Descriptions, IHSPartsId, IHSCurrencyCode, IHSProduct, ECCNCode, ECCNNo);
            return objReturn;
        }

        public static DataTable GetPartMachInfo(System.Int32? clientId, System.String partSearch, System.Int32? CustomerRequirementId)
        {
            DataTable dtOfferData;
            dtOfferData = Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.GetPartMachInfo(clientId, partSearch, CustomerRequirementId);
            return dtOfferData;
        }
        public static DataTable GetPartMachHUBIPOInfo(System.Int32? clientId, System.Int32? updatedBy, System.Int32? CustomerRequirementId)
        {
            DataTable dtOfferData;
            dtOfferData = Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.GetPartMachHUBIPOInfo(clientId, updatedBy, CustomerRequirementId);
            return dtOfferData;
        }

        //public static void AddSourcingResultsforMatchedOffers(System.Int32? CustomerRequirementId, DataTable dtOffer)
        //{
        //    Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.AddSourcingResultsforMatchedOffers(CustomerRequirementId, dtOffer);   
        //}
        /// <summary>
        /// Calls [usp_insert_CustomerRequirement_as_Alternate]
        /// </summary>
        /// <returns></returns>
        public Int32 AddAlternateNew()
        {
            //return Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.Update(CustomerRequirementId, Part, ManufacturerNo, DateCode, PackageNo, Quantity, Price, CurrencyNo, ReceivedDate, Salesman, DatePromised, Notes, Instructions, Shortage, CompanyNo, ContactNo, UsageNo, Alternate, OriginalCustomerRequirementNo, ReasonNo, ProductNo, CustomerPart, Closed, ROHS, UpdatedBy, PartWatch, BOM, BOMName, BOMNo, FactorySealed, MSL, PQA, Obsolete, LastTimeBuy, RefirbsAcceptable, TestingRequired, TargetSellPrice, CompetitorBestOffer, CustomerDecisionDate, RFQClosingDate, QuoteValidityRequired, Type, OrderToPlace, RequirementforTraceability, EAU, AlternativesAccepted, RepeatBusiness, SupportTeamMemberNo);
            return Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.AddAlternateNew(CustomerRequirementId, Part, ManufacturerNo, DateCode, PackageNo, Quantity, Price, CurrencyNo, ReceivedDate, Salesman, DatePromised, Notes, Instructions, Shortage, CompanyNo, ContactNo, UsageNo, Alternate, OriginalCustomerRequirementNo, ReasonNo, ProductNo, CustomerPart, Closed, ROHS, UpdatedBy, PartWatch, BOM, BOMName, BOMNo, FactorySealed, MSL, PQA, Obsolete, LastTimeBuy, RefirbsAcceptable, TestingRequired, TargetSellPrice, CompetitorBestOffer, CustomerDecisionDate, RFQClosingDate, QuoteValidityRequired, Type, OrderToPlace, RequirementforTraceability, EAU, AlternativesAccepted, RepeatBusiness, SupportTeamMemberNo,
                CountryOfOrigin, CountryOfOriginNo, LifeCycleStage, HTSCode, AveragePrice, Packaging, PackagingSize, Descriptions, IHSPartsId, IHSCurrencyCode, IHSProduct, ECCNCode, ECCNNo);
        }


        /// <summary>
        /// Get
        /// Calls [usp_select_PartFoundInReverseLogistics]
        /// </summary>
        public static CustomerRequirement GetRLPart(System.String PartNo)
        {
            Rebound.GlobalTrader.DAL.CustomerRequirementDetails objDetails = Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.GetRLPart(PartNo);
            if (objDetails == null)
            {
                return null;
            }
            else
            {
                CustomerRequirement obj = new CustomerRequirement();
                obj.ReverseLogisticid = objDetails.ReverseLogisticid;
                obj.Partcount = objDetails.Partcount;
                obj.ClientNo = objDetails.ClientNo;
                obj.FullPart = objDetails.FullPart;
                obj.Part = objDetails.Part;
                objDetails = null;
                return obj;
            }
        }


        public static string GetPartLinesRL(string strpartNo)
        {
            string PartLinesRL = "";
            PartLinesRL = Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.GetPartLinesRL(strpartNo);

            return PartLinesRL;
        }

        public static void UpsertLyticaAPI(string APIResponseJson, int? UpdatedBy)
        {
            Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.UpsertLyticaAPIData(APIResponseJson, UpdatedBy);
        }

        public static bool RefreshLyticaDataAfter3days(string partNo, string mfrCode, int mfrNo)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.IsExistLyticaAPIDataOver3Days(partNo, mfrCode, mfrNo);
        }

        public static LyticaAPI GetLyticaDataByPartMfr(System.String partNo, System.String manufacturerCode, System.String manufacturerName)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.GetLyticaDataByPartMfr(partNo, manufacturerCode, manufacturerName);
        }

        public static LyticaAPI GetLyticaDataOnHUBRFQByPartMfr(System.Int32 cusReqNo, string mfrName)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.GetLyticaDataOnHUBRFQByPartMfr(cusReqNo, mfrName);
        }

        public static string GetManufacturerNameByCode(string manufacturerCode, string mfrNo)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.GetManufacturerNameByCode(manufacturerCode, mfrNo);
        }

        /// <summary>
        /// Get List CustomerRequirement
        /// Calls [usp_Get_List_CusReq_PowerApp_By_Ids]
        /// </summary>
        public static List<ProspectiveOfferForPowerApp> ListEmailPowerApp(List<int> customerRequirementIds, string flowName)
        {
            List<ProspectiveOfferForPowerApp> objDetails = Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.ListEmailPowerApp(customerRequirementIds, flowName);
            if (objDetails == null)
            {
                return null;
            }
            else
            {
                return objDetails;
            }
        }

        /// <summary>
        /// Get
        /// Calls [usp_Get_List_CustomerRequirement_Details_By_Ids]
        /// </summary>
        public static List<CustomerRequirement> List(List<int> customerRequirementIds)
        {
            List<CustomerRequirementDetails> objDetails = Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.List(customerRequirementIds);
            if (objDetails == null)
            {
                return null;
            }
            else
            {
                List<CustomerRequirement> result = new List<CustomerRequirement>();
                foreach (CustomerRequirementDetails item in objDetails)
                {
                    CustomerRequirement obj = new CustomerRequirement();
                    obj.BOMNo = item.BOMNo == null ? 0 : (int)item.BOMNo;
                    obj.CustomerRequirementId = item.CustomerRequirementId;
                    obj.ReceivedDate = item.ReceivedDate;
                    obj.CompanyName = item.CompanyName;
                    obj.Part = item.Part;
                    obj.CustomerPart = item.CustomerPart;
                    obj.ManufacturerName = item.ManufacturerName;
                    obj.DateCode = item.DateCode;
                    obj.ProductName = item.ProductName;
                    obj.PackageName = item.PackageName;
                    obj.Price = item.Price;
                    obj.Quantity = item.Quantity;
                    obj.LyticaAveragePrice = item.LyticaAveragePrice;
                    obj.IHSAveragePrice = item.IHSAveragePrice;
                    obj.CurrencyCode = item.CurrencyCode;
                    obj.Salesman = item.Salesman;
                    obj.SalesmanName = item.SalesmanName;
                    obj.CurrencyNo = item.CurrencyNo;
                    obj.NewOfferPriceFromProspective = item.NewOfferPriceFromProspective;
                    obj.ClientCurrencyNo = item.ClientCurrencyNo;
                    result.Add(obj);
                }
                return result;
            }
        }

        /// <summary>
        /// Get
        /// Calls [usp_Get_CusReq_Having_Available_RLStock]
        /// </summary>
        public static List<CustomerRequirement> GetHUBRFQHasRLStock(System.Int32? BOMId)
        {
            List<CustomerRequirementDetails> details = Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.GetHUBRFQHasRLStock(BOMId);
            if (details == null)
            {
                return null;
            }
            else
            {
                List<CustomerRequirement> result = new List<CustomerRequirement>();
                foreach (CustomerRequirementDetails item in details)
                {
                    CustomerRequirement obj = new CustomerRequirement();
                    obj.BOMNo = item.BOMNo == null ? 0 : (int)item.BOMNo;
                    obj.CompanyName = item.CompanyName;
                    obj.Part = item.Part;
                    obj.ManufacturerName = item.ManufacturerName;
                    obj.ProductName = item.ProductName;
                    obj.PackageName = item.PackageName;
                    obj.DateCode = item.DateCode;
                    obj.Quantity = item.Quantity;
                    obj.Price = item.Price;
                    obj.ClientNo = item.ClientNo; // This is clientNo of Warehouse in stock
                    obj.BOMCode = item.BOMCode;
                    obj.CurrencyNo = item.CurrencyNo;
                    obj.CurrencyCode = item.CurrencyCode;
                    obj.ClientGlobalCurrencyNo = item.ClientGlobalCurrencyNo;
                    obj.ClientCurrencyCode = item.ClientCurrencyCode;
                    obj.ReqGlobalCurrencyNo = item.ReqGlobalCurrencyNo;
                    obj.ClientCurrencyNo = item.ClientCurrencyNo;
                    result.Add(obj);
                }
                return result;
            }
        }

        /// <summary>
        /// Get
        /// Calls [usp_Get_CusReq_Having_Available_RLStock_By_Part]
        /// </summary>
        public static List<CustomerRequirement> GetHUBRFQHasRLStockByPart(System.String part)
        {
            List<CustomerRequirementDetails> details = Rebound.GlobalTrader.DAL.SiteProvider.CustomerRequirement.GetHUBRFQHasRLStockByPart(part);
            if (details == null)
            {
                return null;
            }
            else
            {
                List<CustomerRequirement> result = new List<CustomerRequirement>();
                foreach (CustomerRequirementDetails item in details)
                {
                    CustomerRequirement obj = new CustomerRequirement();
                    obj.BOMNo = item.BOMNo == null ? 0 : (int)item.BOMNo;
                    obj.CompanyName = item.CompanyName;
                    obj.Part = item.Part;
                    obj.ManufacturerName = item.ManufacturerName;
                    obj.ProductName = item.ProductName;
                    obj.PackageName = item.PackageName;
                    obj.DateCode = item.DateCode;
                    obj.Quantity = item.Quantity;
                    obj.Price = item.Price;
                    obj.ClientNo = item.ClientNo; // This is clientNo of Warehouse in stock
                    obj.BOMCode = item.BOMCode;
                    obj.CurrencyNo = item.CurrencyNo;
                    obj.CurrencyCode = item.CurrencyCode;
                    obj.ClientGlobalCurrencyNo = item.ClientGlobalCurrencyNo;
                    obj.ClientCurrencyCode = item.ClientCurrencyCode;
                    obj.ReqGlobalCurrencyNo = item.ReqGlobalCurrencyNo;
                    obj.ClientCurrencyNo = item.ClientCurrencyNo;
                    result.Add(obj);
                }
                return result;
            }
        }
    }
}

