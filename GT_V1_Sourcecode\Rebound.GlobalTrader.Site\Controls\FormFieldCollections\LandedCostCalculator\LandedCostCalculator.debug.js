///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.FormFieldCollections");

Rebound.GlobalTrader.Site.Controls.FormFieldCollections.LandedCostCalculator = function(element) { 
	Rebound.GlobalTrader.Site.Controls.FormFieldCollections.LandedCostCalculator.initializeBase(this, [element]);
	this._intCurrencyID = -1;
};

Rebound.GlobalTrader.Site.Controls.FormFieldCollections.LandedCostCalculator.prototype = {

	get_lblCurrency: function() { return this._lblCurrency; }, 	set_lblCurrency: function(value) { if (this._lblCurrency !== value)  this._lblCurrency = value; }, 
	get_lblCurrency2: function() { return this._lblCurrency2; }, 	set_lblCurrency2: function(value) { if (this._lblCurrency2 !== value)  this._lblCurrency2 = value; }, 
	get_ibtnApply: function() { return this._ibtnApply; }, 	set_ibtnApply: function(value) { if (this._ibtnApply !== value)  this._ibtnApply = value; }, 
	get_ibtnCalculate: function() { return this._ibtnCalculate; }, 	set_ibtnCalculate: function(value) { if (this._ibtnCalculate !== value)  this._ibtnCalculate = value; }, 
	get_ibtnCancel: function() { return this._ibtnCancel; }, 	set_ibtnCancel: function(value) { if (this._ibtnCancel !== value)  this._ibtnCancel = value; }, 
	get_trLoading: function() { return this._trLoading; }, 	set_trLoading: function(value) { if (this._trLoading !== value)  this._trLoading = value; }, 
	get_trError: function() { return this._trError; }, 	set_trError: function(value) { if (this._trError !== value)  this._trError = value; }, 
	get_trApply: function() { return this._trApply; }, 	set_trApply: function(value) { if (this._trApply !== value)  this._trApply = value; }, 
	get_trCalculate: function() { return this._trCalculate; }, 	set_trCalculate: function(value) { if (this._trCalculate !== value)  this._trCalculate = value; }, 
	
	addApply: function(handler) { this.get_events().addHandler("Apply", handler); },
	removeApply: function(handler) { this.get_events().removeHandler("Apply", handler); },
	onApply: function() { 
		var handler = this.get_events().getHandler("Apply");
		if (handler) handler(this, Sys.EventArgs.Empty); 
	},
	
	addCancel: function(handler) { this.get_events().addHandler("Cancel", handler); },
	removeCancel: function(handler) { this.get_events().removeHandler("Cancel", handler); },
	onCancel: function() { 
		var handler = this.get_events().getHandler("Cancel");
		if (handler) handler(this, Sys.EventArgs.Empty); 
	},

	initialize: function() {
		$R_IBTN.addClick(this._ibtnCalculate, Function.createDelegate(this, this.doCalculate));
		$R_IBTN.addClick(this._ibtnCancel, Function.createDelegate(this, this.doCancel));
		$R_IBTN.addClick(this._ibtnApply, Function.createDelegate(this, this.doApply));
		Rebound.GlobalTrader.Site.Controls.FormFieldCollections.LandedCostCalculator.callBaseMethod(this, "initialize");
	},

	dispose: function() { 
		if (this.isDisposed) return;
		$R_IBTN.clearHandlers(this._ibtnCalculate);
		$R_IBTN.clearHandlers(this._ibtnCancel);
		$R_IBTN.clearHandlers(this._ibtnApply);
		this._lblCurrency = null;
		this._lblCurrency2 = null;
		this._ibtnApply = null;
		this._ibtnCalculate = null;
		this._ibtnCancel = null;
		this._trLoading = null;
		this._trError = null;
		this._trApply = null;
		this._trCalculate = null;
		Rebound.GlobalTrader.Site.Controls.FormFieldCollections.LandedCostCalculator.callBaseMethod(this, "dispose");
	},
	
	show: function(bln) {
		$R_FN.showElement(this._element, bln);
		if (bln) {
			this._ctlRelatedForm.getFieldDropDownData("ctlLCC_Funds");
			this._ctlRelatedForm.getFieldDropDownData("ctlLCC_Product");
			this.resetFields();
			this.showResult(false);
			this._ctlRelatedForm.showField("ctlLCC_LandedCost", false);
			$R_FN.showElement(this._trApply, false);
		}
	},
	
	setCurrency: function(intID, strName) {
		this._intCurrencyID = intID;
		$R_FN.setInnerHTML(this._lblCurrency, strName);
		$R_FN.setInnerHTML(this._lblCurrency2, strName);
	},
	
	showLoading: function(bln) {
		$R_FN.showElement(this._trLoading, bln);
		if (bln) {
			this._ctlRelatedForm.showField("ctlLCC_LandedCost", false);
			$R_FN.showElement(this._trError, false);
			$R_FN.showElement(this._trApply, false);
			$R_FN.showElement(this._trCalculate, false);
		}
	},
	
	showError: function(bln) {
		$R_FN.showElement(this._trError, bln);
		if (bln) {
			this._ctlRelatedForm.showField("ctlLCC_LandedCost", false);
			$R_FN.showElement(this._trLoading, false);
			$R_FN.showElement(this._trApply, false);
			$R_FN.showElement(this._trCalculate, true);
		}
	},
	
	showResult: function(bln) {
		this._ctlRelatedForm.showField("ctlLCC_LandedCost", bln);
		$R_FN.showElement(this._trApply, bln);
		if (bln) {
			$R_FN.showElement(this._trError, false);
			$R_FN.showElement(this._trLoading, false);
			$R_FN.showElement(this._trCalculate, true);
		}
	},
	
	validateForm: function() {
		var bln = true;
		this.resetFields();
		if (!this._ctlRelatedForm.checkFieldEntered("ctlLCC_Quantity")) bln = false;
		if (!this._ctlRelatedForm.checkFieldNumeric("ctlLCC_Quantity")) bln = false;
		if (!this._ctlRelatedForm.checkFieldEntered("ctlLCC_Cost")) bln = false;
		if (!this._ctlRelatedForm.checkFieldNumeric("ctlLCC_Cost")) bln = false;
		if (!this._ctlRelatedForm.checkFieldEntered("ctlLCC_Funds")) bln = false;
		if (!this._ctlRelatedForm.checkFieldEntered("ctlLCC_Date")) bln = false;
		if (!this._ctlRelatedForm.checkFieldEntered("ctlLCC_Product")) bln = false;
		if (!this._ctlRelatedForm.checkFieldEntered("ctlLCC_ShippingCost")) bln = false;
		if (!this._ctlRelatedForm.checkFieldNumeric("ctlLCC_ShippingCost")) bln = false;
		return bln;
	},
	
	resetFields: function() {
		this._ctlRelatedForm.resetFieldError("ctlLCC_Quantity");
		this._ctlRelatedForm.resetFieldError("ctlLCC_Cost");
		this._ctlRelatedForm.resetFieldError("ctlLCC_Funds");
		this._ctlRelatedForm.resetFieldError("ctlLCC_Date");
		this._ctlRelatedForm.resetFieldError("ctlLCC_Product");
		this._ctlRelatedForm.resetFieldError("ctlLCC_ShippingCost");
	},
	
	doCancel: function() {
		this.show(false);
		this.onCancel();
	},
	
	doCalculate: function() {
		if (!this.validateForm()) return;
		this.showLoading(true);
		Rebound.GlobalTrader.Site.WebServices.CalculateLandedcost(
			this._ctlRelatedForm.getFieldValue("ctlLCC_Quantity")
			, this._ctlRelatedForm.getFieldValue("ctlLCC_Cost")
			, this._ctlRelatedForm.getFieldValue("ctlLCC_Funds")
			, this._ctlRelatedForm.getFieldValue("ctlLCC_Date")
			, this._ctlRelatedForm.getFieldValue("ctlLCC_ApplyDuty")
			, this._ctlRelatedForm.getFieldValue("ctlLCC_Product")
			, this._ctlRelatedForm.getFieldValue("ctlLCC_ShippingCost")
			, Function.createDelegate(this, this.doCalculateComplete)
			, Function.createDelegate(this, this.doCalculateError)
		);
	},

	doCalculateComplete: function(v) {
		this.showResult(true);
		this._ctlRelatedForm.setFieldValue("ctlLCC_LandedCost", v);
		this._varValue = v;
	},

	doCalculateError: function() {
		this.showError(true);
	},
	
	doApply: function() {
		this.onApply();
	}

};

Rebound.GlobalTrader.Site.Controls.FormFieldCollections.LandedCostCalculator.registerClass("Rebound.GlobalTrader.Site.Controls.FormFieldCollections.LandedCostCalculator", Rebound.GlobalTrader.Site.Controls.Forms.LabelFormField, Sys.IDisposable);
