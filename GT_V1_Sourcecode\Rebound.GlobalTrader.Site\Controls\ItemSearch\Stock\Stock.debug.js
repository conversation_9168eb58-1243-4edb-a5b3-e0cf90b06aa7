///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//----------------------------------------------------------------------------------------------------------------
// RP 27.01.2010:
// - pass through SupplierRMANo to data call
//
// RP 06.01.2010:
// - fully dispose everything
//
// RP 08.12.2009:
// - add more columns
//Marker      ChangedBy     Date            Remarks
//[001]       <PERSON><PERSON><PERSON>   30-Aug-2018     apply global security login on allocate and ipoallocate.
//----------------------------------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.ItemSearch");

Rebound.GlobalTrader.Site.Controls.ItemSearch.Stock = function(element) {
    Rebound.GlobalTrader.Site.Controls.ItemSearch.Stock.initializeBase(this, [element]);
    this._blnForRMAs = false;
    this._blnIncludeQuarantined = false;
    this._blnIncludeLotsOnHold = false;
    this._IncludeLockLotCustNo = "";
};

Rebound.GlobalTrader.Site.Controls.ItemSearch.Stock.prototype = {

    get_blnForRMAs: function() { return this._blnForRMAs; }, set_blnForRMAs: function(v) { if (this._blnForRMAs !== v) this._blnForRMAs = v; },
    get_blnIncludeQuarantined: function() { return this._blnIncludeQuarantined; }, set_blnIncludeQuarantined: function(v) { if (this._blnIncludeQuarantined !== v) this._blnIncludeQuarantined = v; },
    get_blnIncludeLotsOnHold: function() { return this._blnIncludeLotsOnHold; }, set_blnIncludeLotsOnHold: function(v) { if (this._blnIncludeLotsOnHold !== v) this._blnIncludeLotsOnHold = v; },
    get_intSupplierRMANo: function() { return this._intSupplierRMANo; }, set_intSupplierRMANo: function(v) { if (this._intSupplierRMANo !== v) this._intSupplierRMANo = v; },

    initialize: function() {
        Rebound.GlobalTrader.Site.Controls.ItemSearch.Stock.callBaseMethod(this, "initialize");
        this.addSetupData(Function.createDelegate(this, this.doSetupData));
        this.addGetDataComplete(Function.createDelegate(this, this.doGetDataComplete));
        $R_FN.showElement(this.getField("ctlPurchaseOrderNo")._element, !this._blnForRMAs);
        $R_FN.showElement(this.getField("ctlCRMANo")._element, this._blnForRMAs);
    },

    dispose: function() {
        if (this.isDisposed) return;
        this._blnForRMAs = null;
        this._blnIncludeQuarantined = null;
        this._blnIncludeLotsOnHold = null;
        this._intSupplierRMANo = null;
        this._IncludeLockLotCustNo = null;
        Rebound.GlobalTrader.Site.Controls.ItemSearch.Stock.callBaseMethod(this, "dispose");
    },

    doSetupData: function () {
        this._objData.set_PathToData("controls/ItemSearch/Stock");
        this._objData.set_DataObject("Stock");
        this._objData.set_DataAction("GetData");
        this._objData.addParameter("ForRMAs", this._blnForRMAs);
        this._objData.addParameter("SupplierRMANo", this._intSupplierRMANo);
        this._objData.addParameter("IncludeQuarantined", this._blnIncludeQuarantined);
        this._objData.addParameter("IncludeLotsOnHold", this._blnIncludeLotsOnHold);
        this._objData.addParameter("PartNo", this.getFieldValue("ctlPartNo"));
        this._objData.addParameter("WarehouseNo", this.getFieldValue("ctlWarehouse"));
        this._objData.addParameter("Location", this.getFieldValue("ctlLocation"));
        this._objData.addParameter("PONoLo", this.getFieldValue_Min("ctlPurchaseOrderNo"));
        this._objData.addParameter("PONoHi", this.getFieldValue_Max("ctlPurchaseOrderNo"));
        this._objData.addParameter("CRMANoLo", this.getFieldValue_Min("ctlCRMANo"));
        this._objData.addParameter("CRMANoHi", this.getFieldValue_Max("ctlCRMANo"));
        this._objData.addParameter("IncLockCust", this._IncludeLockLotCustNo);
        //[001] start
        this._objData.addParameter("GlobalLoginClientNo", this._intGlobalClientNo);
        //[001] end
    },

    doGetDataComplete: function() {
        for (var i = 0, l = this._objResult.Results.length; i < l; i++) {
            var row = this._objResult.Results[i];
            var aryData = [
				$R_FN.writeDoubleCellValue($R_FN.writePartNo(row.Part, row.ROHS),$R_FN.setCleanTextValue(row.SupplierPart))
				, $R_FN.writeDoubleCellValue(row.QtyStock, row.QtyOrder)
				, $R_FN.writeDoubleCellValue(row.QtyAllocated, row.QtyAvailable)
				, $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.Warehouse), $R_FN.setCleanTextValue(row.Location))
				, $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.Supplier), $R_FN.setCleanTextValue(row.Landed))
				, (this._blnForRMAs) ? $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.CRMA), $R_FN.setCleanTextValue(row.CRMADate)) : $R_FN.writeDoubleCellValue($R_FN.showSerialNumber($R_FN.setCleanTextValue(row.PO), row.LineNo), $R_FN.setCleanTextValue(row.PODelivDate))
			];
            if (this._blnIncludeQuarantined) Array.add(aryData, $R_FN.setCleanTextValue(row.Unavailable));
            this._tblResults.addRow(aryData, row.ID, false);
            aryData = null; row = null;
        }
        this._tblResults.resizeColumns();
    }

};

Rebound.GlobalTrader.Site.Controls.ItemSearch.Stock.registerClass("Rebound.GlobalTrader.Site.Controls.ItemSearch.Stock", Rebound.GlobalTrader.Site.Controls.ItemSearch.Base, Sys.IDisposable);
