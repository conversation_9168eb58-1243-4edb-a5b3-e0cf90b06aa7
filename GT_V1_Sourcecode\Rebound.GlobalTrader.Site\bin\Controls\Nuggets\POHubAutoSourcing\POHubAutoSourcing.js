Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Nuggets");Rebound.GlobalTrader.Site.Controls.Nuggets.POHubAutoSourcing=function(n){Rebound.GlobalTrader.Site.Controls.Nuggets.POHubAutoSourcing.initializeBase(this,[n]);this._cusReqID=-1;this._partNo="";this._blnRequirementClosed=!1};Rebound.GlobalTrader.Site.Controls.Nuggets.POHubAutoSourcing.prototype={get_cusReqID:function(){return this._cusReqID},set_cusReqID:function(n){this._cusReqID!==n&&(this._cusReqID=n)},get_tblAutoSourcing:function(){return this._tblAutoSourcing},set_tblAutoSourcing:function(n){this._tblAutoSourcing!==n&&(this._tblAutoSourcing=n)},updateSourcingResultComplete:function(n){this.get_events().addHandler("SourcingResultUpdated",n)},removeSourcingResultComplete:function(n){this.get_events().removeHandler("SourcingResultUpdated",n)},onSourcingResultUpdated:function(){var n=this.get_events().getHandler("SourcingResultUpdated");n&&n(this,Sys.EventArgs.Empty)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Nuggets.POHubAutoSourcing.callBaseMethod(this,"initialize");this._strPathToData="controls/Nuggets/POHubSourcing";this._strDataObject="POHubSourcing";this.addRefreshEvent(Function.createDelegate(this,this.getAutoSourcing));this._tblAutoSourcing.addSortDataEvent(Function.createDelegate(this,this.getAutoSourcing));this.showLoading(!1);this.showContent(!0);this.showContentLoading(!1);this.showValidateMessage(!1)},dispose:function(){this.isDisposed||(this._tblAutoSourcing&&this._tblAutoSourcing.dispose(),this._tblAutoSourcing=null,this._cusReqID=null,this._partNo=null,this._blnRequirementClosed=null,Rebound.GlobalTrader.Site.Controls.Nuggets.POHubAutoSourcing.callBaseMethod(this,"dispose"))},getAutoSourcing:function(){this.showValidateMessage(!1);this.getData_Start();var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData(this._strPathToData);n.set_DataObject(this._strDataObject);n.set_DataAction("GetAutoSourcing");n.addParameter("CRID",this._cusReqID);n.addParameter("PartNo",this._partNo);n.addParameter("SortIndex",this._tblAutoSourcing._intSortColumnIndex);n.addParameter("SortDir",this._tblAutoSourcing._enmSortDirection);n.addDataOK(Function.createDelegate(this,this.getAutoSourcingOK));n.addError(Function.createDelegate(this,this.getAutoSourcingError));n.addTimeout(Function.createDelegate(this,this.getAutoSourcingError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getAutoSourcingOK:function(n){var t=n._result;this._tblAutoSourcing.clearTable();this.renderTable(this._tblAutoSourcing,t);this._tblAutoSourcing.resizeColumns();this.getDataOK_End();this.showNoData(t.Count==0)},getAutoSourcingError:function(n){this.showError(!0,n.get_ErrorMessage())},renderTable:function(n,t){var r,u,i,f,e;if(t.Items){for(r=0;r<t.Items.length;r++)u="<span class='disabled'>Add Offer<\/span>",i=t.Items[r],i.AddedToReq?u=i.AllowRemoveOffer?String.format("<a href=\"javascript:void(0);\" class=\"link-button nubButtonAlignLeft\" title='Remove Offer' onclick=\"$find('{0}').removeOffer('{1}');\">Remove Offer<\/a>",this._element.id,i.SourcingResultId):"<span class='disabled'>Remove Offer<\/span>":this.checkEnableAddToOffer(i)&&(u=String.format("<a href=\"javascript:void(0);\" class=\"link-button nubButtonAlignLeft\" title='Add Offer' onclick=\"$find('{0}').addOffer('{1}');\">Add Offer<\/a>",this._element.id,i.ID)),f=[u,$R_FN.writePartNo(i.PartNo,i.ROHS),$R_FN.setCleanTextValue(i.SourcingTypeDescription),$R_FN.setCleanTextValue(i.Supplier),$RGT_nubButton_Manufacturer(i.MfrNo,i.MfrCode),$R_FN.setCleanTextValue(i.Product),$R_FN.setCleanTextValue(i.DateOrdered),$R_FN.setCleanTextValue(i.Quantity),$R_FN.setCleanTextValue(i.BuyPrice),$R_FN.setCleanTextValue(i.SellPrice)],e={SourcingType:i.SourcingType,DivisionStatus:i.DivisionStatus,IsCurrentClient:i.IsCurrentClient,IsSourcingHub:i.IsSourcingHub,IsRestrictMfr:i.IsRestrictMfr},n.addRow(f,i.ID,!1,e),i=null,f=null;t.LyticaResult&&$("#lytica-data").html($R_FN.setCleanTextValue(t.LyticaResult));t.IHSResult&&$("#ihs-data").html($R_FN.setCleanTextValue(t.IHSResult))}},checkEnableAddToOffer:function(n){var t=!0;switch(n.SourcingType){case"HUBSTK":t=n.DivisionStatus==1;break;case"EPPH":case"RLPH":t=!this._blnRequirementClosed&&n.IsCurrentClient&&!n.IsSourcingHub;break;case"OFPH":t=!this._blnRequirementClosed&&n.IsCurrentClient&&!n.IsSourcingHub&&!n.IsRestrictMfr}return t},addOffer:function(n){var i=confirm("Are you sure you want to add"),t;i&&(this.showLoading(!0),t=new Rebound.GlobalTrader.Site.Data,t.set_PathToData(this._strPathToData),t.set_DataObject(this._strDataObject),t.set_DataAction("AddAutoSourcing"),t.addParameter("ID",this._cusReqID),t.addParameter("AutoSourceID",n),t.addDataOK(Function.createDelegate(this,this.addOfferOK)),t.addError(Function.createDelegate(this,this.addOfferError)),t.addTimeout(Function.createDelegate(this,this.addOfferError)),$R_DQ.addToQueue(t),$R_DQ.processQueue(),t=null)},addOfferOK:function(n){if(n._result.Result){this.showSavedOK(!0,$R_RES.ChangesSavedSuccessfully);this.onSourcingResultUpdated();return}if(n._result.Msg!="undefined"&&n._result.Msg.length>0){this.showLoading(!1);this.showValidateMessage(!0,n._result.Msg);return}this.showError(!0,n.get_ErrorMessage())},addOfferError:function(n){this.showError(!0,n.get_ErrorMessage())},removeOffer:function(n){var i=confirm("Are you sure you want to remove"),t;i&&(this.showLoading(!0),t=new Rebound.GlobalTrader.Site.Data,t.set_PathToData("controls/Nuggets/BOMCusReqSourcingResults"),t.set_DataObject("BOMCusReqSourcingResults"),t.set_DataAction("DeleteItem"),t.addParameter("ListOfIds",n),t.addDataOK(Function.createDelegate(this,this.removeOfferOK)),t.addError(Function.createDelegate(this,this.removeOfferError)),t.addTimeout(Function.createDelegate(this,this.removeOfferError)),$R_DQ.addToQueue(t),$R_DQ.processQueue(),t=null)},removeOfferOK:function(n){n._result.Result?(this.showSavedOK(!0,$R_RES.ChangesSavedSuccessfully),this.onSourcingResultUpdated()):this.showError(!0,n.get_ErrorMessage())},removeOfferError:function(n){this.showError(!0,n.get_ErrorMessage())},showValidateMessage:function(n,t){n?($("#error-detail").html(t),$(".error-summary").show()):($("#error-detail").html(""),$(".error-summary").hide())}};Rebound.GlobalTrader.Site.Controls.Nuggets.POHubAutoSourcing.registerClass("Rebound.GlobalTrader.Site.Controls.Nuggets.POHubAutoSourcing",Rebound.GlobalTrader.Site.Controls.Nuggets.Base);