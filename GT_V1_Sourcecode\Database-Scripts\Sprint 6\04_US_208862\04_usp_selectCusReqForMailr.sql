﻿-- Drop the procedure if it already exists
IF OBJECT_ID('dbo.usp_selectCusReqForMail', 'P') IS NOT NULL
BEGIN
    DROP PROCEDURE dbo.usp_selectCusReqForMail;
END

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

/* 
===========================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-208862]		Phuc Hoang			16-Jul-2024		Create			Manual Trigger: Refreshing Lytica API
===========================================================================================
*/

CREATE PROCEDURE [dbo].[usp_selectCusReqForMail]        
@BOMNo int ,                                      
@ClientID int                                                
AS            
BEGIN         
select         
cr.CustomerRequirementNumber,        
cr.CustomerRequirementId,        
cr.Quantity,        
cr.CustomerPart,        
cr.Part,        
cr.DateCode,        
cr.ManufacturerCode,        
cr.PackageName,        
cr.ProductName,        
cr.CompanyName,        
cr.ClientName,        
cr.DatePromised,        
dbo.ufn_convert_currency_value(cr.Price, cr.CurrencyNo, cr.BOMCurrencyNo, cr.BOMDate) AS ConvertedTargetValue,        
cr.CurrencyNo,        
cu.CurrencyCode as BOMCurrencyCode ,        
cr.SalesmanName,        
case when cr.Obsolete=1 then 'Obsolete : Yes <br/>'  +ISNULL(cr.Instructions,'') else  'Obsolete : No <br/>' + ISNULL(cr.Instructions,'') end as Instructions,        
cr.MSL,        
cr.FactorySealed,        
TRD.ServiceName AS ReqTypeText,    
TRDT.ServiceName AS ReqForTraceability,
cr.AlternativesAccepted,
cr.RepeatBusiness  ,     
cr.AlternateStatus  ,
cr.Alternate,
ly.AveragePrice AS LyticaAveragePrice,
ly.TargetPrice AS LyticaTargetPrice,
ly.MarketLeading AS LyticaMarketLeading

 FROM  dbo.vwCustomerRequirement cr         
 LEFT JOIN DBO.[tbRequirementDropDownData] TRD ON cr.ReqType=TRD.Id       
 LEFT JOIN DBO.[tbRequirementDropDownData] TRDT ON cr.ReqForTraceability=TRDT.Id      
 LEFT JOIN dbo.tbCurrency cu  ON cr.CurrencyNo = cu.CurrencyId
 LEFT JOIN tbLyticaAPI ly ON (ly.Manufacturer = cr.ManufacturerName AND ly.OriginalPartSearched = cr.Part)
 
     
 where            
BOMNo = @BOMNo 
--and cr.ClientNo= @ClientID   
-- AND (@ClientID IS NULL        
--                                 OR (NOT @ClientID IS NULL        
--                                     AND ClientNo = @ClientID))   
end  

GO
