﻿
GO


CREATE OR ALTER PROCEDURE [dbo].[usp_GetXMAtchDynamicData]   
@DisplayLength int = 0,    
@DisplayStart int = 0,    
@SortCol int = 0,    
@SortDir nvarchar(10),    
@Search nvarchar(255) = NULL,    
@list_Label_name nvarchar(max) = NULL,    
@list_column_name nvarchar(max) = NULL,    
@SelectColumns varchar(3000),    
@ClientId int,    
@UserId int    
AS    
BEGIN    
  
BEGIN TRY                    
    BEGIN TRANSACTION GenerateXMAtchDynamicData;    
  
  
  DECLARE @FirstRec int,    
          @LastRec int, @counterVar int  = 1     
  SET @FirstRec = @DisplayStart;    
  SET @LastRec = @DisplayStart + @DisplayLength;    
    
  --- added RP-25 error handling                
  truncate table tbTmpUtilityTable    
  
  DECLARE @columnlist nvarchar(max)    
  DECLARE @DynamicQuery nvarchar(max)    
  DECLARE @Label_name varchar(max),    
          @column_name varchar(max)    
  DECLARE @strScript nvarchar(max)    
  SET @strScript = ''    
  -------------------------                                          
  DECLARE cursor_GenXMatchData CURSOR FOR    
  WITH T1 AS (SELECT    
    val AS Label_name,    
    ROW_NUMBER() OVER (ORDER BY (SELECT    
      1)    
    ) AS ID    
  FROM BorisGlobalTrader.dbo.[SplitString](@list_Label_name, ',')),    
  T2 AS (SELECT    
    val AS Column_name,    
    ROW_NUMBER() OVER (ORDER BY (SELECT    
      1)    
    ) AS ID    
  FROM BorisGlobalTrader.dbo.[SplitString](@list_column_name, ','))    
  SELECT    
    T1.Label_name,    
    T2.Column_name    
  FROM T1    
  FULL JOIN T2    
    ON (T1.ID = T2.ID)    
  OPEN cursor_GenXMatchData;    
  FETCH NEXT FROM cursor_GenXMatchData INTO    
  @Label_name, @column_name    
  WHILE @@FETCH_STATUS = 0    
  BEGIN    
    
 --- RP-25  
 insert into tbTmpUtilityTable(Value1,Value2,rwStatus,lineNumber,insertedby,clientid,utilityname)                    
    select @Label_name,@column_name,0,@counterVar,@UserId,@ClientId,'XMatch'   
  
    IF @Label_name = 'XPN'    
    BEGIN    
      SET @strScript = @strScript + ',' + '(case   when len( ' + @column_name + ')>50 then dbo.stripAlphahnumeric(SUBSTRING(' + @column_name + ',0, 50))                             
     else dbo.stripAlphahnumeric( ' + @column_name + ' ) end )as XPartNumber' + ''   
    
  exec ('select dbo.stripAlphahnumeric( ' + @column_name + ' ) from BorisGlobalTraderimports.dbo.tbTempXMatchData  where       
   ClientId= CONVERT(nvarchar(10),' + @ClientId + ') and CreatedBy= CONVERT(nvarchar(10),' + @UserId +')');   
     
    END    
    IF @Label_name = 'XQty'    
    BEGIN    
      SET @strScript = @strScript + ',' + '(case   when len( ' + @column_name + ')>50 then dbo.stripAlphahnumeric(SUBSTRING(' + @column_name + ',0, 50))          
	  else dbo.stripAlphahnumeric( ' + @column_name + ' ) end )as XQty' + ''   
    
  exec ('select cast(dbo.stripSpecialCharacterFromNumeric(IsNUll(' + @column_name + ',0)) as numeric) from BorisGlobalTraderimports.dbo.tbTempXMatchData where        
   ClientId= CONVERT(nvarchar(10),' + @ClientId + ') and CreatedBy= CONVERT(nvarchar(10),' + @UserId +')');     
     
    END    
    --Part--                            
    IF @Label_name = 'XDC'    
    BEGIN    
      SET @strScript = @strScript + ',' + '(case   when len( ' + @column_name + ')>50 then dbo.stripAlphahnumeric(SUBSTRING(' + @column_name + ',0, 30))                             
     else dbo.stripAlphahnumeric( ' + @column_name + ' ) end )as XDC' + ''   
    
  exec ('select dbo.stripAlphahnumeric(SUBSTRING(' + @column_name + ',0, 30)) from BorisGlobalTraderimports.dbo.tbTempXMatchData where         
   ClientId= CONVERT(nvarchar(10),' + @ClientId + ') and CreatedBy= CONVERT(nvarchar(10),' + @UserId +')');     
  
     
    END    
    
  update tbTmpUtilityTable set rwStatus = 1 where Value1 = @Label_name and Value2 = @column_name and clientid = @ClientId and insertedby = @UserId                
  and utilityname = 'XMatch'                
                
        --- increment @counterVar to +1 to set the line number to match with the row data of excel uploaded                  
  SET @counterVar = @counterVar + 1;       
  
  
    FETCH NEXT FROM cursor_GenXMatchData INTO    
    @Label_name, @column_name    
  END;    
    
  CLOSE cursor_GenXMatchData;    
  DEALLOCATE cursor_GenXMatchData;    
    
  DECLARE @setSelctCommand nvarchar(max)    
  SET @setSelctCommand = @strScript    
    
  SET @DynamicQuery = ';WITH TempResult as                                    
   ( select top 20 ROW_NUMBER() over (order by  id ) as  RowNum ,COUNT(*) over() as TotalCount ' +    
  '' + @setSelctCommand + '' + ' from BorisGlobalTraderimports.dbo.tbTempXMatchData ' + 'WHERE  ClientId=' + CONVERT(nvarchar(10), @ClientId) + 'and CreatedBy=' + CONVERT(nvarchar(10), @UserId) + ' )                                  
   SELECT *  from TempResult where RowNum > ' + CAST(@FirstRec AS varchar(20)) + ' and  RowNum <= ' + CAST(@LastRec AS varchar(20))    
  EXEC (@DynamicQuery)    
    
 truncate table tbTmpUtilityTable;                    
 COMMIT TRANSACTION GenerateXMAtchDynamicData;    
                     
END TRY                    
BEGIN CATCH        
 DECLARE @LabelText1 varchar(100);                             
 DECLARE @ColumnText1 varchar(100);                             
                            
                             
 Select @LabelText1 = Value1,@ColumnText1 = Value2 from tbTmpUtilityTable where RwID = (Select min(RwID) from tbTmpUtilityTable where rwStatus = 0                
 and insertedby = @UserId and utilityname = 'XMatch');                            
                                                       
 DECLARE @msg NVARCHAR(2048)  
   
 if(@LabelText1 = 'XQty')  
 BEGIN  
 --SET @msg = Replace(FORMATMESSAGE('%s column accpets numeric value, kindly verify file column mapping for %s column',@LabelText1, @LabelText1),'.',','); 
 SET @msg = FORMATMESSAGE('%s field only accepts numeric values. Please verify source data and %s column mappings and try again',@LabelText1, @LabelText1);   
 END  
 --else if(@LabelText1 = 'Price')  
 --BEGIN  
 --SET @msg = Replace(FORMATMESSAGE('%s column accpets numeric value, kindly verify file column mapping for %s column',@LabelText1, @LabelText1),'.',',');   
 --END                    
 ELSE  
 BEGIN          
 SET @msg = Replace(FORMATMESSAGE('Please check column %s for data format issues', @LabelText1),'.',',');           
 END                                  
                    
 Rollback TRANSACTION GenerateXMAtchDynamicData;                     
 truncate table tbTmpUtilityTable;                    
                    
 THROW 60000, @msg, 1;                    
                    
END CATCH   
  
END  
  
GO


