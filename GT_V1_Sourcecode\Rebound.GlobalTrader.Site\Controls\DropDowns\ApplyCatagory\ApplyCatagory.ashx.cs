//Marker     Changed by         Date          Remarks
//[001]      <PERSON><PERSON><PERSON><PERSON>     10/09/2021    Added hanlder for purchase method.
using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;

namespace Rebound.GlobalTrader.Site.Controls.DropDowns.Data {
    [WebService(Namespace = "http://tempuri.org/")]
    [WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    public class ApplyCatagory : Rebound.GlobalTrader.Site.Data.DropDowns.Base {

        public override void ProcessRequest(HttpContext context) {
            SetDropDownType("ApplyCatagory");
            base.ProcessRequest(context);
        }

        protected override void GetData() {
            string strCacheOptions = CacheManager.SerializeOptions(new object[] { SessionManager.Culture });
            //string strCachedData = CacheManager.GetDropDownData(_objDropDown.ID, strCacheOptions);
            
                JsonObject jsn = new JsonObject();
                JsonObject jsnList = new JsonObject(true);
                List<BLL.PurchaseMethod> lst = new List<BLL.PurchaseMethod>();
                
                
                    JsonObject jsnItem1 = new JsonObject();
                    jsnItem1.AddVariable("ID", 1);
                    jsnItem1.AddVariable("Name", "Manufacturer");
                    jsnList.AddVariable(jsnItem1);
                    jsnItem1.Dispose(); jsnItem1 = null;

                    JsonObject jsnItem2 = new JsonObject();
                    jsnItem2.AddVariable("ID", 2);
                    jsnItem2.AddVariable("Name", "Product");
                    jsnList.AddVariable(jsnItem2);
                    jsnItem2.Dispose(); jsnItem2 = null;

                    JsonObject jsnItem3 = new JsonObject();
                    jsnItem3.AddVariable("ID", 3);
                    jsnItem3.AddVariable("Name", "Vendor");
                    jsnList.AddVariable(jsnItem3);
                    jsnItem3.Dispose(); jsnItem3 = null;
           
              JsonObject jsnItem4 = new JsonObject();
            jsnItem4.AddVariable("ID", 4);
            jsnItem4.AddVariable("Name", "Terms");
            jsnList.AddVariable(jsnItem4);
            jsnItem4.Dispose(); jsnItem4= null;

            lst.Clear(); lst = null;
                jsn.AddVariable("Types", jsnList);
                jsnList.Dispose(); jsnList = null;
                CacheManager.StoreDropDown(_objDropDown.ID, strCacheOptions, jsn.Result, CacheManager.CacheExpiryType.OneWorkingDay);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;      
        }
    }
}
