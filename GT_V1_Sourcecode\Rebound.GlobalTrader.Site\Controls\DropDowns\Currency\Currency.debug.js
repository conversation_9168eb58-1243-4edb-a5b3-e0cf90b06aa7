///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//
// RP 06.10.2009:
// - Changes due to changes in base class
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");

Rebound.GlobalTrader.Site.Controls.DropDowns.Currency = function(element) { 
	Rebound.GlobalTrader.Site.Controls.DropDowns.Currency.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.DropDowns.Currency.prototype = {
    get_intGlobalLoginClientNo: function () { return this._intGlobalLoginClientNo; }, set_intGlobalLoginClientNo: function (v) { if (this._intGlobalLoginClientNo !== v) this._intGlobalLoginClientNo = v; },
	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.DropDowns.Currency.callBaseMethod(this, "initialize");
		this.addSetupDataCall(Function.createDelegate(this, this.setupDataCall));
		this.addGetDataOK(Function.createDelegate(this, this.dataCallOK));
	},
	
	dispose: function() { 
	    if (this.isDisposed) return;
	    this._intGlobalLoginClientNo=null;
		Rebound.GlobalTrader.Site.Controls.DropDowns.Currency.callBaseMethod(this, "dispose");
	},
	
	setupDataCall: function() { 
		this._objData.set_PathToData(this._strDataPathModification + "controls/DropDowns/Currency");
		this._objData.set_DataObject("Currency");
		this._objData.set_DataAction("GetData");
		this._objData.addParameter("GlobalLoginClientNo", this._intGlobalLoginClientNo);
	},
	
	dataCallOK: function() { 
		var result = this._objData._result;
		if (result.Currencies) {
			for (var i = 0; i < result.Currencies.length; i++) {
				this.addOption(result.Currencies[i].Name, result.Currencies[i].ID, result.Currencies[i].Code);
			}
		}
	}	
	
};

Rebound.GlobalTrader.Site.Controls.DropDowns.Currency.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.Currency", Rebound.GlobalTrader.Site.Controls.DropDowns.Base);
