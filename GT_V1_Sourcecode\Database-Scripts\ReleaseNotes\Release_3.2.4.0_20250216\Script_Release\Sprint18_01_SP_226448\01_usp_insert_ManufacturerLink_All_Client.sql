﻿/*   
===========================================================================================  
TASK         UPDATED BY   DATE          ACTION    DESCRIPTION  
[US-226448]  Cuong Do   23-Dec-2024		Upsert    Renew add relationship manually flow and calculate Star Rating after that
===========================================================================================  
*/ 
CREATE OR ALTER PROCEDURE [dbo].[usp_insert_ManufacturerLink_All_Client] 
--
AS
--
BEGIN
	DECLARE @LatestNumOfPO INT;
		SELECT top 1 @LatestNumOfPO = NumOfPO 
		FROM tbStarRatingConfig 
		ORDER BY CreatedDate DESC

	MERGE INTO tbManufacturerLink AS ml
	USING (
		SELECT 
			pol.ManufacturerNo,
			po.CompanyNo AS SupplierCompanyNo,
			COUNT(pol.PurchaseOrderLineId) AS TotalPOLine
		FROM 
			tbPurchaseOrder po
		JOIN 
			tbPurchaseOrderLine pol
			ON po.PurchaseOrderId = pol.PurchaseOrderNo
		WHERE 
			pol.ManufacturerNo IS NOT NULL
			AND po.CompanyNo IS NOT NULL
		GROUP BY 
			pol.ManufacturerNo, 
			po.CompanyNo
	) AS source
	ON ml.ManufacturerNo = source.ManufacturerNo 
	   AND ml.SupplierCompanyNo = source.SupplierCompanyNo
	WHEN MATCHED THEN 
		UPDATE SET 
			ml.StarRating =
			CASE 
				WHEN source.TotalPOLine / @LatestNumOfPO >= 5 THEN 5
				ELSE FLOOR(source.TotalPOLine / @LatestNumOfPO) 
			END
	WHEN NOT MATCHED THEN 
		INSERT (ManufacturerNo, SupplierCompanyNo, StarRating)
		VALUES (source.ManufacturerNo, source.SupplierCompanyNo, 
		CASE 
			WHEN source.TotalPOLine / @LatestNumOfPO >= 5 THEN 5
			ELSE FLOOR(source.TotalPOLine / @LatestNumOfPO)
		END);
	UPDATE tbManufacturerLink SET starrating = 0 WHERE starrating is null
END