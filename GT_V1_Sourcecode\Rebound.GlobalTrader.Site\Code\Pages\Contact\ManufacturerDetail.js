Type.registerNamespace("Rebound.GlobalTrader.Site.Pages.Contact");Rebound.GlobalTrader.Site.Pages.Contact.ManufacturerDetail=function(n){Rebound.GlobalTrader.Site.Pages.Contact.ManufacturerDetail.initializeBase(this,[n]);this._intManufacturerID=0};Rebound.GlobalTrader.Site.Pages.Contact.ManufacturerDetail.prototype={get_ctlPageTitle:function(){return this._ctlPageTitle},set_ctlPageTitle:function(n){this._ctlPageTitle!==n&&(this._ctlPageTitle=n)},get_ctlMainInfo:function(){return this._ctlMainInfo},set_ctlMainInfo:function(n){this._ctlMainInfo!==n&&(this._ctlMainInfo=n)},get_lblAbbreviation:function(){return this._lblAbbreviation},set_lblAbbreviation:function(n){this._lblAbbreviation!==n&&(this._lblAbbreviation=n)},get_ctlManufacturerPDF:function(){return this._ctlManufacturerPDF},set_ctlManufacturerPDF:function(n){this._ctlManufacturerPDF!==n&&(this._ctlManufacturerPDF=n)},get_ctlManufacturerEXCEL:function(){return this._ctlManufacturerEXCEL},set_ctlManufacturerEXCEL:function(n){this._ctlManufacturerEXCEL!==n&&(this._ctlManufacturerEXCEL=n)},get_ctlManufacturerPDFDragDrop:function(){return this._ctlManufacturerPDFDragDrop},set_ctlManufacturerPDFDragDrop:function(n){this._ctlManufacturerPDFDragDrop!==n&&(this._ctlManufacturerPDFDragDrop=n)},get_ctlManufacturerEXCELDragDrop:function(){return this._ctlManufacturerEXCELDragDrop},set_ctlManufacturerEXCELDragDrop:function(n){this._ctlManufacturerEXCELDragDrop!==n&&(this._ctlManufacturerEXCELDragDrop=n)},initialize:function(){Rebound.GlobalTrader.Site.Pages.Contact.ManufacturerDetail.callBaseMethod(this,"initialize")},goInit:function(){this._ctlMainInfo&&this._ctlMainInfo.addSaveEditComplete(Function.createDelegate(this,this.ctlMainInfo_EditComplete));this._ctlManufacturerPDF&&this._ctlManufacturerPDF.getData();this._ctlManufacturerEXCEL&&this._ctlManufacturerEXCEL.getData();this._ctlManufacturerPDFDragDrop&&this._ctlManufacturerPDFDragDrop.getData();this._ctlManufacturerEXCELDragDrop&&this._ctlManufacturerEXCELDragDrop.getData();Rebound.GlobalTrader.Site.Pages.Contact.ManufacturerDetail.callBaseMethod(this,"goInit")},dispose:function(){this.isDisposed||(this._ctlPageTitle&&this._ctlPageTitle.dispose(),this._ctlMainInfo&&this._ctlMainInfo.dispose(),this._ctlManufacturerPDF&&this._ctlManufacturerPDF.dispose(),this._ctlManufacturerEXCEL&&this._ctlManufacturerEXCEL.dispose(),this._ctlManufacturerEXCELDragDrop&&this._ctlManufacturerEXCELDragDrop.dispose(),this._intManufacturerID=null,this._ctlPageTitle=null,this._ctlMainInfo=null,this._lblAbbreviation=null,this._ctlManufacturerPDF=null,this._ctlManufacturerEXCEL=null,this._ctlManufacturerEXCELDragDrop=null,Rebound.GlobalTrader.Site.Pages.Contact.ManufacturerDetail.callBaseMethod(this,"dispose"))},ctlMainInfo_EditComplete:function(){this._ctlPageTitle.updateTitle(this._ctlMainInfo._frmEdit.getFieldValue("ctlName"));$R_FN.setInnerHTML(this._lblAbbreviation,this._ctlMainInfo._frmEdit.getFieldValue("ctlAbbreviation"))}};Rebound.GlobalTrader.Site.Pages.Contact.ManufacturerDetail.registerClass("Rebound.GlobalTrader.Site.Pages.Contact.ManufacturerDetail",Rebound.GlobalTrader.Site.Pages.Content,Sys.IDisposable);