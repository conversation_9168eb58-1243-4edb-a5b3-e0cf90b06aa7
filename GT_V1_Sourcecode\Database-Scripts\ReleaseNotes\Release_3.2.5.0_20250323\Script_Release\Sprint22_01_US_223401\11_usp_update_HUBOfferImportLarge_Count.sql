﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

/*
=========================================================================================================================================================
TASK			UPDATED BY       DATE				ACTION		DESCRIPTION  
[US-225093]     An.TranTan		 20-Feb-2025		CREATE		Update import rows count after upload success
=========================================================================================================================================================
*/
CREATE OR ALTER   PROC [dbo].[usp_update_HUBOfferImportLarge_Count] (  
	@LoginID INT,
	@ImportID INT,
	@Total INT
)  
AS  
BEGIN  
	UPDATE BorisGlobalTraderImports.dbo.tbHUBOfferImportLargeFile
	SET UploadedRowCount = @Total,
		 UpdatedBy = @LoginId
	WHERE ID = @ImportID
END  
GO


