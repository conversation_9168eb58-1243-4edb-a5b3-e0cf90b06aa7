Type.registerNamespace("Rebound.GlobalTrader.Site.Pages.Warehouse");Rebound.GlobalTrader.Site.Pages.Warehouse.SupplierInvoiceDetail=function(n){Rebound.GlobalTrader.Site.Pages.Warehouse.SupplierInvoiceDetail.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Pages.Warehouse.SupplierInvoiceDetail.prototype={get_intSupplierInvoiceID:function(){return this._intSupplierInvoiceID},set_intSupplierInvoiceID:function(n){this._intSupplierInvoiceID!==n&&(this._intSupplierInvoiceID=n)},get_ctlMainInfo:function(){return this._ctlMainInfo},set_ctlMainInfo:function(n){this._ctlMainInfo!==n&&(this._ctlMainInfo=n)},get_ctlLines:function(){return this._ctlLines},set_ctlLines:function(n){this._ctlLines!==n&&(this._ctlLines=n)},get_ctlSIPDFDragDrop:function(){return this._ctlSIPDFDragDrop},set_ctlSIPDFDragDrop:function(n){this._ctlSIPDFDragDrop!==n&&(this._ctlSIPDFDragDrop=n)},initialize:function(){Rebound.GlobalTrader.Site.Pages.Warehouse.SupplierInvoiceDetail.callBaseMethod(this,"initialize")},goInit:function(){this._ctlMainInfo&&this._ctlMainInfo.addGetDataComplete(Function.createDelegate(this,this.ctlMainInfo_GetDataComplete));this._ctlSIPDFDragDrop&&this._ctlSIPDFDragDrop.getData();Rebound.GlobalTrader.Site.Pages.Warehouse.SupplierInvoiceDetail.callBaseMethod(this,"goInit")},dispose:function(){this.isDisposed||(this._ctlMainInfo&&this._ctlMainInfo.dispose(),this._ctlLines&&this._ctlLines.dispose(),this._ctlMainInfo=null,this._ctlLines=null,this._ctlSIPDFDragDrop=null,Rebound.GlobalTrader.Site.Pages.Warehouse.SupplierInvoiceDetail.callBaseMethod(this,"dispose"))},ctlMainInfo_GetDataComplete:function(){this._ctlLines&&(this._ctlLines._blnExported=this._ctlMainInfo.getFieldValue("ctlExported"),this._ctlLines.getData())}};Rebound.GlobalTrader.Site.Pages.Warehouse.SupplierInvoiceDetail.registerClass("Rebound.GlobalTrader.Site.Pages.Warehouse.SupplierInvoiceDetail",Rebound.GlobalTrader.Site.Pages.Content,Sys.IDisposable);