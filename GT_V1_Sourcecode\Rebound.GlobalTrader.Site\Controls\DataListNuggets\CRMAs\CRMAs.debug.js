///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 09.09.2010:
// - hide Salesperson filter on "My" tab
//
// RP 06.01.2010:
// - fully dispose everything
//
// RP 23.12.2009:
// - render state on server
//
// RP 16.10.2009:
// - Changes due to changes in base class
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DataListNuggets");

Rebound.GlobalTrader.Site.Controls.DataListNuggets.CRMAs = function(element) { 
	Rebound.GlobalTrader.Site.Controls.DataListNuggets.CRMAs.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.DataListNuggets.CRMAs.prototype = {
 get_blnPOHub: function() { return this._blnPOHub; }, set_blnPOHub: function(v) { if (this._blnPOHub !== v) this._blnPOHub = v; },

    initialize: function() {
        this.addSetupDataCallEvent(Function.createDelegate(this, this.setupDataCall));
        this.addGetDataOKEvent(Function.createDelegate(this, this.getDataOK));
        this.addInitCompleteEvent(Function.createDelegate(this, this.initAfterBaseIsReady));
        this._strPathToData = "controls/DataListNuggets/CRMAs";
        this._strDataObject = "CRMAs";
        Rebound.GlobalTrader.Site.Controls.DataListNuggets.CRMAs.callBaseMethod(this, "initialize");
    },

    initAfterBaseIsReady: function() {
        this.addPageTabChangedEvent(Function.createDelegate(this, this.pageTabChanged));
        this.updateFilterVisibility();
        this.getData();
    },

    dispose: function() {
        if (this.isDisposed) return;
        Rebound.GlobalTrader.Site.Controls.DataListNuggets.CRMAs.callBaseMethod(this, "dispose");
    },

    pageTabChanged: function() {
        this._table._intCurrentPage = 1;
        this._enmViewLevel = this._intCurrentTab;
        this.updateFilterVisibility();
        this.getData();
    },

    setupDataCall: function() {
        this._objData.addParameter("ViewLevel", this._enmViewLevel);
    },

    getDataOK: function(args) {
        for (var i = 0, l = this._objResult.Results.length; i < l; i++) {
            var row = this._objResult.Results[i];
            var aryData = [
				$RGT_nubButton_CRMA(row.ID, row.No)
				, $R_FN.writeDoubleCellValue($R_FN.writePartNo(row.Part, row.ROHS), $RGT_nubButton_Manufacturer(row.MfrNo, row.Mfr))
				, row.Quantity
				, $R_FN.writeDoubleCellValue($RGT_nubButton_Company(row.CMNo, row.CM), $RGT_nubButton_Contact(row.ContactNo, row.Contact))
				, $R_FN.setCleanTextValue(row.Date)
				, $RGT_nubButton_Invoice(row.InvNo, row.Invoice)
			];
            this._table.addRow(aryData, row.ID, false);
            aryData = null; row = null;
        }
    },

    updateFilterVisibility: function() {
        this.getFilterField("ctlSalesmanName").show(this._enmViewLevel != 0);
         this.getFilterField("ctlPohubOnly").show(this._blnPOHub);
         this.getFilterField("ctlPohubOnly").enableField(this._blnPOHub);
        
    }

};
Rebound.GlobalTrader.Site.Controls.DataListNuggets.CRMAs.registerClass("Rebound.GlobalTrader.Site.Controls.DataListNuggets.CRMAs", Rebound.GlobalTrader.Site.Controls.DataListNuggets.Base, Sys.IDisposable);
