Type.registerNamespace("Rebound.GlobalTrader.Site.Pages.Orders");Rebound.GlobalTrader.Site.Pages.Orders.ClientInvoiceDetail=function(n){Rebound.GlobalTrader.Site.Pages.Orders.ClientInvoiceDetail.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Pages.Orders.ClientInvoiceDetail.prototype={get_intClientInvoiceID:function(){return this._intClientInvoiceID},set_intClientInvoiceID:function(n){this._intClientInvoiceID!==n&&(this._intClientInvoiceID=n)},get_btnPrint:function(){return this._btnPrint},set_btnPrint:function(n){this._btnPrint!==n&&(this._btnPrint=n)},get_ctlMainInfo:function(){return this._ctlMainInfo},set_ctlMainInfo:function(n){this._ctlMainInfo!==n&&(this._ctlMainInfo=n)},get_ctlLines:function(){return this._ctlLines},set_ctlLines:function(n){this._ctlLines!==n&&(this._ctlLines=n)},get_ctlLinesDeleted:function(){return this._ctlLinesDeleted},set_ctlLinesDeleted:function(n){this._ctlLinesDeleted!==n&&(this._ctlLinesDeleted=n)},get_lblStatus:function(){return this._lblStatus},set_lblStatus:function(n){this._lblStatus!==n&&(this._lblStatus=n)},get_pnlStatus:function(){return this._pnlStatus},set_pnlStatus:function(n){this._pnlStatus!==n&&(this._pnlStatus=n)},get_ctlInvoicePDF:function(){return this._ctlInvoicePDF},set_ctlInvoicePDF:function(n){this._ctlInvoicePDF!==n&&(this._ctlInvoicePDF=n)},get_ctlInvoicePDFDragDrop:function(){return this._ctlInvoicePDFDragDrop},set_ctlInvoicePDFDragDrop:function(n){this._ctlInvoicePDFDragDrop!==n&&(this._ctlInvoicePDFDragDrop=n)},initialize:function(){Rebound.GlobalTrader.Site.Pages.Orders.ClientInvoiceDetail.callBaseMethod(this,"initialize")},goInit:function(){this._btnPrint&&this._btnPrint.addPrint(Function.createDelegate(this,this.printInvoice));this._btnPrint&&this._btnPrint.addEmail(Function.createDelegate(this,this.emailInvoice));this._btnPrint&&this._btnPrint.addExtraButtonClick(Function.createDelegate(this,this.printOtherDocs));this._ctlMainInfo&&this._ctlMainInfo.addGetDataComplete(Function.createDelegate(this,this.ctlMainInfo_GetDataComplete));this._ctlInvoicePDF&&this._ctlInvoicePDF.getData();this._ctlInvoicePDFDragDrop&&this._ctlInvoicePDFDragDrop.getData();Rebound.GlobalTrader.Site.Pages.Orders.ClientInvoiceDetail.callBaseMethod(this,"goInit")},dispose:function(){this.isDisposed||(this._ctlMainInfo&&this._ctlMainInfo.dispose(),this._ctlLines&&this._ctlLines.dispose(),this._ctlLinesDeleted&&this._ctlLinesDeleted.dispose(),this._btnPrint&&this._btnPrint.dispose(),this._btnPrint=null,this._ctlMainInfo=null,this._ctlLines=null,this._ctlLinesDeleted=null,this._pnlStatus=null,this._lblStatus=null,this._intClientInvoiceID=null,this._ctlInvoicePDF=null,Rebound.GlobalTrader.Site.Pages.Orders.ClientInvoiceDetail.callBaseMethod(this,"dispose"))},printInvoice:function(){$R_FN.openPrintWindow($R_ENUM$PrintObject.ClientInvoice,this._intClientInvoiceID)},emailInvoice:function(){$R_FN.openPrintWindow($R_ENUM$PrintObject.ClientInvoice,this._intClientInvoiceID,!0)},printOtherDocs:function(){this._btnPrint._strExtraButtonClickCommand=="PrintLog"&&$R_FN.openPrintLogWindow($R_ENUM$PrintObject.PrintLog,this._intClientInvoiceID,!1,"ClientInvoice")},ctlMainInfo_GetDataComplete:function(){this.setLineFieldsFromHeader();this._ctlLines.getData()},ctlLines_GetDeletedData:function(){this._ctlLinesDeleted.getData()},setLineFieldsFromHeader:function(){this._ctlLines&&(this._ctlLines._blnExported=this._ctlMainInfo._blnExported,this._ctlLines._intInvoiceClientNo=this._ctlMainInfo._intInvoiceClientNo)}};Rebound.GlobalTrader.Site.Pages.Orders.ClientInvoiceDetail.registerClass("Rebound.GlobalTrader.Site.Pages.Orders.ClientInvoiceDetail",Rebound.GlobalTrader.Site.Pages.Content,Sys.IDisposable);