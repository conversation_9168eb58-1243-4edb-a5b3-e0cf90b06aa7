Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.CloneHUB=function(n){Rebound.GlobalTrader.Site.Controls.Forms.CloneHUB.initializeBase(this,[n]);this._intCustomerRequirementID=-1;this._intCurrencyID=-1;this._intCompanyID=-1;this._blnReqValidated=!0;this._ctlCompany="";this._strCustomerRequirementNumber=""};Rebound.GlobalTrader.Site.Controls.Forms.CloneHUB.prototype={get_intCustomerRequirementID:function(){return this._intCustomerRequirementID},set_intCustomerRequirementID:function(n){this._intCustomerRequirementID!==n&&(this._intCustomerRequirementID=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Forms.CloneHUB.callBaseMethod(this,"initialize");this.addShown(Function.createDelegate(this,this.formShown))},dispose:function(){this.isDisposed||(this._intCustomerRequirementID=null,this._intCurrencyID=-0,this._intCompanyID=null,this._ctlCompany="",this._strCustomerRequirementNumber="",Rebound.GlobalTrader.Site.Controls.Forms.CloneHUB.callBaseMethod(this,"dispose"))},formShown:function(){this._blnFirstTimeShown&&(this._ctlConfirm=this.getFieldComponent("ctlConfirm"),this._ctlConfirm.addClickYesEvent(Function.createDelegate(this,this.yesClicked)),this._ctlConfirm.addClickNoEvent(Function.createDelegate(this,this.noClicked)));$("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_lblCustomer").text(this._ctlCompany);$("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_lblCustReqNo").text(this._strCustomerRequirementNumber);$addHandler(document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl25_ibtnBack_hyp"),"click",Function.createDelegate(this,this.noClicked1));$addHandler(document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl26_ibtnBack_hyp"),"click",Function.createDelegate(this,this.noClicked1))},noClicked1:function(){this.onNotConfirmed()},yesClicked:function(){if(this.validateForm()){this.showSaving(!0);var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("Controls/Nuggets/CusReqMainInfo");n.set_DataObject("CusReqMainInfo");n.set_DataAction("CloneRequirementDataHUB");n.addParameter("id",this._intCustomerRequirementID);n.addDataOK(Function.createDelegate(this,this.saveCloneHUBComplete));n.addError(Function.createDelegate(this,this.saveError));n.addTimeout(Function.createDelegate(this,this.saveError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null;this.onSave()}},noClicked:function(){this.showSaving(!1);this.onNotConfirmed()},validateForm:function(){this.onValidate();return!0},saveError:function(n){this.showSaving(!1);this._strErrorMessage=n._errorMessage;this.onSaveError()},saveCloneHUBComplete:function(n){var t=n._result;n._result.Result>0?(this.showSavedOK(!0),location.href=$RGT_gotoURL_CustomerRequirement(n._result.Result)):this.showError(!0,n._result.ValidationMessage)}};Rebound.GlobalTrader.Site.Controls.Forms.CloneHUB.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.CloneHUB",Rebound.GlobalTrader.Site.Controls.Forms.Base,Sys.IDisposable);