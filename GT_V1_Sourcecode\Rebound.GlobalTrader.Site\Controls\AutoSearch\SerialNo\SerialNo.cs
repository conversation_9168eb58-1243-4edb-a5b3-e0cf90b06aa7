using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using System.Collections;

namespace Rebound.GlobalTrader.Site.Controls.AutoSearch {
	[DefaultProperty("")]
    [ToolboxData("<{0}:SerialNo runat=server></{0}:SerialNo>")]
    public class SerialNo : Base
    {


		#region Properties

        /// <summary>
        /// Manufacturer Dropdown list show active and inactive data
        /// </summary>
        //private bool _showInActive = true;
        //public bool ShowInactive
        //{
        //    get { return _showInActive; }
        //    set { _showInActive = value; }
        //}

		#endregion

		#region Overrides
		/// <summary>
		/// OnInit
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
			base.OnInit(e);
            AddScriptReference("Controls.AutoSearch.SerialNo.SerialNo.js");
            SetAutoSearchType("SerialNo");
		}

		protected override void OnLoad(EventArgs e) {
			CharactersToEnterBeforeSearch = 2;
            //IncludeInactive = false;
			base.OnLoad(e);
		}

		/// <summary>
		/// OnPreRender
		/// </summary>
		/// <param name="e"></param>
		protected override void OnPreRender(EventArgs e) {
			SetupScriptDescriptors();
			base.OnPreRender(e);
		}
		#endregion

		/// <summary>
		/// SetupScriptDescriptors
		/// </summary>
		private void SetupScriptDescriptors() {
            _scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.AutoSearch.SerialNo", ClientID);
            //_scScriptControlDescriptor.AddProperty("intPOHubClientNo", POHubClientNo);
		}
	}
}