﻿//-----------------------------------------------------------------------------------------
// RP 06.09.2010:
// - include virtual warehouses by default
//
// RP 22.12.2009:
// - Allow for Virtual Warehouses
//-----------------------------------------------------------------------------------------
using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;

namespace Rebound.GlobalTrader.Site.Controls.DropDowns {
    public partial class PreferredWarehouse : Base
    {

		private bool _blnIncludeVirtual = true;
		public bool IncludeVirtual {
			get { return _blnIncludeVirtual; }
			set { _blnIncludeVirtual = value; }
		}

		protected override void OnLoad(EventArgs e) {
			SetDropDownType("Warehouse");
            AddScriptReference("Controls.DropDowns.PreferredWarehouse.PreferredWarehouse");
            _scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.DropDowns.PreferredWarehouse", ClientID);
			base.OnLoad(e);
		}

		protected override void OnPreRender(EventArgs e) {
			_scScriptControlDescriptor.AddProperty("blnIncludeVirtual", _blnIncludeVirtual);
			base.OnPreRender(e);
		}

	}
}