Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");Rebound.GlobalTrader.Site.Controls.DropDowns.LCurrency=function(n){Rebound.GlobalTrader.Site.Controls.DropDowns.LCurrency.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.DropDowns.LCurrency.prototype={get_intGlobalLoginClientNo:function(){return this._intGlobalLoginClientNo},set_intGlobalLoginClientNo:function(n){this._intGlobalLoginClientNo!==n&&(this._intGlobalLoginClientNo=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.DropDowns.LCurrency.callBaseMethod(this,"initialize");this.addSetupDataCall(Function.createDelegate(this,this.setupDataCall));this.addGetDataOK(Function.createDelegate(this,this.dataCallOK))},dispose:function(){this.isDisposed||(this._intGlobalLoginClientNo=null,Rebound.GlobalTrader.Site.Controls.DropDowns.LCurrency.callBaseMethod(this,"dispose"))},setupDataCall:function(){this._objData.set_PathToData("controls/DropDowns/LCurrency");this._objData.set_DataObject("LCurrency");this._objData.set_DataAction("GetData");this._objData.addParameter("GlobalLoginClientNo",this._intGlobalLoginClientNo)},dataCallOK:function(){var t=this._objData._result,n;if(t.Currencies)for(n=0;n<t.Currencies.length;n++)this.addOption(t.Currencies[n].Name,t.Currencies[n].ID,t.Currencies[n].Code)}};Rebound.GlobalTrader.Site.Controls.DropDowns.LCurrency.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.LCurrency",Rebound.GlobalTrader.Site.Controls.DropDowns.Base);