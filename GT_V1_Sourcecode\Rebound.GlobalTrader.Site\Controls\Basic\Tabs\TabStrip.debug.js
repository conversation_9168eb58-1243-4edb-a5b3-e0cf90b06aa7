///<reference name="MicrosoftAjax.js" />
///<reference path="~/Common/Data/Data.js" />
///<reference path="~/Common/Functions/Functions.js" />
///<reference path="~/Controls/IconButton/IconButton.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - dispose everything fully
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls");

Rebound.GlobalTrader.Site.Controls.TabStrip = function(element) { 
	Rebound.GlobalTrader.Site.Controls.TabStrip.initializeBase(this, [element]);
	this._aryTabIDs = [];
	this._selectedTabIndex = -1;
};

Rebound.GlobalTrader.Site.Controls.TabStrip.prototype = {

	get_aryTabIDs: function() { return this._aryTabIDs; }, 	set_aryTabIDs: function(value) { if (this._aryTabIDs !== value)  this._aryTabIDs = value; }, 
	get_SelectedTabIndex: function() { return this._selectedTabIndex; }, 	set_SelectedTabIndex: function(value) { if (this._selectedTabIndex !== value)  this._selectedTabIndex = value; }, 
	get_pnlContent: function() { return this._pnlContent; }, 	set_pnlContent: function(value) { if (this._pnlContent !== value)  this._pnlContent = value; }, 
	get_pnlLoading: function() { return this._pnlLoading; }, 	set_pnlLoading: function(value) { if (this._pnlLoading !== value)  this._pnlLoading = value; }, 
	get_currentTab: function() { return this._currentTab; }, 	set_currentTab: function(value) { if (this._currentTab !== value)  this._currentTab = value; }, 

	addTabIndexChanged: function(handler) { this.get_events().addHandler("TabIndexChanged", handler); },
	removeTabIndexChanged: function(handler) { this.get_events().removeHandler("TabIndexChanged", handler); },
	onTabIndexChanged: function() {
		var handler = this.get_events().getHandler("TabIndexChanged");
		if (handler) handler(this, new Rebound.GlobalTrader.Site.DataEventArgs(this._statusCode, this._errorMessage, this._result, this._url));
	},	

	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.TabStrip.callBaseMethod(this, "initialize");
	},

	dispose: function() { 
		if (this.isDisposed) return;
		if (this.get_element()) $clearHandlers(this.get_element());
		this._selectedTabIndex = null;
		this._aryTabIDs = null;
		this._pnlContent = null;
		this._pnlLoading = null;
		this._currentTab = null;
		Rebound.GlobalTrader.Site.Controls.TabStrip.callBaseMethod(this, "dispose");
		this.isDisposed = true;
	},
	
	selectTab: function(intTabIndex) {
		if (intTabIndex == this._selectedTabIndex) return;
		for (var i = 0, l = this._aryTabIDs.length; i < l; i++) {
			var tab = $find(this._aryTabIDs[i]);
			tab.selectTab(tab._tabIndex == intTabIndex);
			$R_FN.showElement(tab._pnlRelatedContent, (tab._tabIndex == intTabIndex));
			tab = null;
		}
		
		this._selectedTabIndex = intTabIndex;
		this._currentTab = $find(this._aryTabIDs[this._selectedTabIndex]);
		this.onTabIndexChanged();
	},
	
	showContent: function(blnShow) {
		$R_FN.showElement(this._pnlContent, blnShow);
		if (blnShow) $R_FN.showElement(this._pnlLoading, false);
	},
	
	showLoading: function(blnShow) {
		$R_FN.showElement(this._pnlLoading, blnShow);
		if (blnShow) $R_FN.showElement(this._pnlContent, false);
	},
	
	resetTabTitles: function() {
		for (var i = 0, l = this._aryTabIDs.length; i < l; i++) {
			this.resetTabTitle(i);
		}
	},
	
	setTabCount: function(i, intCount) {
		var tab = $find(this._aryTabIDs[i]);
		if (!tab) return;
		tab.addCountToTitle(intCount);
		tab = null;
	},
	
	resetTabTitle: function(i) {
		var tab = $find(this._aryTabIDs[i]);
		if (tab) tab.resetTitle();
		tab = null;
	}
	
};

Rebound.GlobalTrader.Site.Controls.TabStrip.registerClass("Rebound.GlobalTrader.Site.Controls.TabStrip", Sys.UI.Control, Sys.IDisposable);