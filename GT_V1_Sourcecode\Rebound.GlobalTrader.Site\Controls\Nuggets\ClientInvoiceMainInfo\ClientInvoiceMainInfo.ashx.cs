//Marker     Changed by      Date               Remarks
//[005]      Prakash           11/04/2014         Add Client Invoice
using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;

namespace Rebound.GlobalTrader.Site.Controls.Nuggets.Data {
	[WebService(Namespace = "http://tempuri.org/")]
	[WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    public class ClientInvoiceMainInfo : Rebound.GlobalTrader.Site.Data.Base
    {
		public override void ProcessRequest(HttpContext context) {
			if (base.init(context)) {
				switch (Action) {
					case "GetData": GetData(); break;
					case "SaveEdit": SaveEdit(); break;
                    case "GetTaxRate": GetTaxRate(); break;
                    case "GetDataItem": GetDataItem(); break;
					default: WriteErrorActionNotFound(); break;
				}
			}
		}
		/// <summary>
		/// get specific goodsIn by key
		/// </summary>
        public JsonObject GetData(ClientInvoice ci)
        {
			JsonObject jsn = null;
            if (ci != null)
            {
				jsn = new JsonObject();
                jsn.AddVariable("ClientInvoiceID", ci.ClientInvoiceID);
                jsn.AddVariable("ClientInvoiceNumber", ci.ClientInvoiceNumber);
                jsn.AddVariable("CompanyNo", ci.CompanyNo);
                jsn.AddVariable("ClientInvoiceDate",Functions.FormatDate(ci.ClientInvoiceDate));
                jsn.AddVariable("SupplierCode", ci.SupplierCode);
                jsn.AddVariable("SupplierName", ci.Clientname);
                jsn.AddVariable("CurrencyNo", ci.CurrencyNo);
                jsn.AddVariable("CurrencyCode", ci.CurrencyCode);
                jsn.AddVariable("InvoiceAmount", Functions.FormatCurrency(ci.InvoiceAmount, ci.CurrencyCode));
                jsn.AddVariable("GoodsValue", Functions.FormatCurrency(ci.GoodsValue, ci.CurrencyCode));
                jsn.AddVariable("Tax", Functions.FormatCurrency(ci.Tax, ci.CurrencyCode));
                jsn.AddVariable("TaxName", Functions.ReplaceLineBreaks(ci.TaxName));
                jsn.AddVariable("DeliveryCharge", Functions.FormatCurrency(ci.DeliveryCharge, ci.CurrencyCode));
                jsn.AddVariable("BankFee", Functions.FormatCurrency(ci.BankFee, ci.CurrencyCode));
                jsn.AddVariable("CreditCardFee", Functions.FormatCurrency(ci.CreditCardFee, ci.CurrencyCode));
                jsn.AddVariable("Notes", Functions.ReplaceLineBreaks(ci.Notes));
                jsn.AddVariable("SecondRef", Functions.ReplaceLineBreaks(ci.SecondRef));
                jsn.AddVariable("Narrative", Functions.ReplaceLineBreaks(ci.Narrative));
                jsn.AddVariable("CanbeExported",Convert.ToBoolean(ci.CanbeExported));
                jsn.AddVariable("Exported",Convert.ToBoolean(ci.Exported));
                jsn.AddVariable("URNNumber", (ci.URNNumber.HasValue) ? Convert.ToString(ci.URNNumber.Value) : "");
                jsn.AddVariable("DLUP", Functions.FormatDLUP(ci.DLUP, ci.UpdatedBy));
                jsn.AddVariable("PO", ci.PurchaseOrderNumber);
                jsn.AddVariable("PONo", ci.PurchaseOrderNo);
                jsn.AddVariable("InvoiceAmountValue", Functions.FormatCurrency(ci.InvoiceAmount));
                jsn.AddVariable("GoodsValueValue", Functions.FormatCurrency(ci.GoodsValue));
                jsn.AddVariable("TaxValue", Functions.FormatCurrency(ci.Tax));
                jsn.AddVariable("DeliveryChargeValue", Functions.FormatCurrency(ci.DeliveryCharge));
                jsn.AddVariable("BankFeeValue", Functions.FormatCurrency(ci.BankFee));
                jsn.AddVariable("CreditCardFeeValue", Functions.FormatCurrency(ci.CreditCardFee));
                jsn.AddVariable("TaxNo", ci.TaxNo);
                jsn.AddVariable("InternalPurchaseOrderNo", ci.InternalPurchaseOrderNo);
                jsn.AddVariable("InternalPurchaseOrderNumber", ci.InternalPurchaseOrderNumber);
                jsn.AddVariable("DivisionNo", ci.DivisionNo);
                jsn.AddVariable("Salesman", ci.SalesmanName);
                jsn.AddVariable("SalesmanNo", ci.SalesmanNo);
                jsn.AddVariable("DivisionName", ci.DivisionName);
                jsn.AddVariable("ClientNo", ci.ClientNo);
                jsn.AddVariable("InvoiceClientNo", ci.InvoiceClientNo);
                jsn.AddVariable("IsPOHub", SessionManager.IsPOHub);
                
			}
			ci = null;
			return jsn;
		}
		public void GetData() {
            ClientInvoice clientInvoice = ClientInvoice.Get(ID);
            OutputResult(GetData(clientInvoice));
            clientInvoice = null;
		}

        public void GetDataItem()
        {
            ClientInvoice clientInvoice = ClientInvoice.GetClientInvoiceByLineNo(ID, GetFormValue_Int("LineNo"));
            OutputResult(GetDataItem(clientInvoice));
            clientInvoice = null;
        }

        /// <summary>
        /// get specific goodsIn by key
        /// </summary>
        public JsonObject GetDataItem(ClientInvoice ci)
        {
            JsonObject jsn = null;
            if (ci != null)
            {
                jsn = new JsonObject();
                jsn.AddVariable("ClientInvoiceID", ci.ClientInvoiceID);
                jsn.AddVariable("ClientInvoiceNumber", ci.ClientInvoiceNumber);
                jsn.AddVariable("CompanyNo", ci.CompanyNo);
                jsn.AddVariable("ClientInvoiceDate", Functions.FormatDate(ci.ClientInvoiceDate));
                jsn.AddVariable("SupplierCode", ci.SupplierCode);
                jsn.AddVariable("SupplierName", ci.Clientname);
                jsn.AddVariable("CurrencyNo", ci.CurrencyNo);
                jsn.AddVariable("CurrencyCode", ci.CurrencyCode);
                jsn.AddVariable("InvoiceAmount", Functions.FormatCurrency(ci.InvoiceAmount, ci.CurrencyCode));
                jsn.AddVariable("GoodsValue", Functions.FormatCurrency(ci.GoodsValue, ci.CurrencyCode));
                jsn.AddVariable("Tax", Functions.FormatCurrency(ci.Tax, ci.CurrencyCode));
                jsn.AddVariable("TaxName", Functions.ReplaceLineBreaks(ci.TaxName));
                jsn.AddVariable("DeliveryCharge", Functions.FormatCurrency(ci.DeliveryCharge, ci.CurrencyCode));
                jsn.AddVariable("BankFee", Functions.FormatCurrency(ci.BankFee, ci.CurrencyCode));
                jsn.AddVariable("CreditCardFee", Functions.FormatCurrency(ci.CreditCardFee, ci.CurrencyCode));
                jsn.AddVariable("Notes", Functions.ReplaceLineBreaks(ci.Notes));
                jsn.AddVariable("SecondRef", Functions.ReplaceLineBreaks(ci.SecondRef));
                jsn.AddVariable("Narrative", Functions.ReplaceLineBreaks(ci.Narrative));
                jsn.AddVariable("CanbeExported", Convert.ToBoolean(ci.CanbeExported));
                jsn.AddVariable("Exported", Convert.ToBoolean(ci.Exported));
                jsn.AddVariable("URNNumber", (ci.URNNumber.HasValue) ? Convert.ToString(ci.URNNumber.Value) : "");
                jsn.AddVariable("DLUP", Functions.FormatDLUP(ci.DLUP, ci.UpdatedBy));
                jsn.AddVariable("PO", ci.PurchaseOrderNumber);
                jsn.AddVariable("PONo", ci.PurchaseOrderNo);
                jsn.AddVariable("InvoiceAmountValue", Functions.FormatCurrency(ci.InvoiceAmount));
                jsn.AddVariable("GoodsValueValue", Functions.FormatCurrency(ci.GoodsValue));
                jsn.AddVariable("TaxValue", Functions.FormatCurrency(ci.Tax));
                jsn.AddVariable("DeliveryChargeValue", Functions.FormatCurrency(ci.DeliveryCharge));
                jsn.AddVariable("BankFeeValue", Functions.FormatCurrency(ci.BankFee));
                jsn.AddVariable("CreditCardFeeValue", Functions.FormatCurrency(ci.CreditCardFee));
                jsn.AddVariable("TaxNo", ci.TaxNo);
                jsn.AddVariable("InternalPurchaseOrderNo", ci.InternalPurchaseOrderNo);
                jsn.AddVariable("InternalPurchaseOrderNumber", ci.InternalPurchaseOrderNumber);
                jsn.AddVariable("DivisionNo", ci.DivisionNo);
                jsn.AddVariable("Salesman", ci.SalesmanName);
                jsn.AddVariable("SalesmanNo", ci.SalesmanNo);
                jsn.AddVariable("DivisionName", ci.DivisionName);
                jsn.AddVariable("DivisionHeaderNo", ci.DivisionHeaderNo);
                jsn.AddVariable("DivisionHeaderName", ci.DivisionHeaderName);

            }
            ci = null;
            return jsn;
        }

		/// <summary>
		/// Update main Client invoice
		/// </summary>
		public void SaveEdit() {
			try {
                bool blnResult = ClientInvoice.Update(
                    ID,
                    "" ,
                   // GetFormValue_String("clientInvoiceNumber"),
                    GetFormValue_NullableDateTime("ClientInvoiceDate", DateTime.Now),
                   GetFormValue_NullableInt("CurrencyNo"),
                    // GetFormValue_NullableInt("CurrencyNo"),
                    GetFormValue_NullableDouble("Amount", null),
                    GetFormValue_NullableDouble("GoodsValue", null),
                    GetFormValue_NullableDouble("Tax", null),
                    GetFormValue_NullableDouble("DeliveryCharge", null),
                    GetFormValue_NullableDouble("BankFee", null),
                    GetFormValue_NullableDouble("CreditCardFee", null),
                    false ,
                   // GetFormValue_NullableBoolean("CanBeExported", 0),
                    GetFormValue_String("Notes"),
                    GetFormValue_NullableInt("TaxNo", null),
                   // GetFormValue_NullableInt("TaxNo",null),
                   GetFormValue_String("TaxCode"),
                  //  GetFormValue_String("TaxCode"),
                   // GetFormValue_String("CurrencyCode",SessionManager.ClientCurrencyCode),
                   GetFormValue_String("CurrencyCode"),
                    GetFormValue_String("SecondRef"),
                    GetFormValue_String("Narrative"),
                    LoginID,
                   // GetFormValue_NullableInt64("URNNumber",null)
                   0
                );
				JsonObject jsn = new JsonObject();
				jsn.AddVariable("Result", blnResult);
				OutputResult(jsn);
				jsn.Dispose();
				jsn = null;
			} catch (Exception e) {
				WriteError(e);
			}
		}

        public void GetTaxRate()
        {
            try
            {
                JsonObject jsn = new JsonObject();
                jsn.AddVariable("Rate", Functions.FormatPercentage(TaxManager.GetTaxRate(GetFormValue_Int("TaxNo"), DateTime.Now) * 100, 5, false));
                OutputResult(jsn);
                jsn.Dispose();
                jsn = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }

	}
}