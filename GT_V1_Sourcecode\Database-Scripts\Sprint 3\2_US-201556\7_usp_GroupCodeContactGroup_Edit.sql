-- Drop the procedure if it already exists
IF OBJECT_ID('dbo.usp_GroupCodeContactGroup_Edit', 'P') IS NOT NULL
BEGIN
    DROP PROCEDURE dbo.usp_GroupCodeContactGroup_Edit;
END

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

/* 
===========================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-201556]		Hau Nguyen			31-May-2024		CREATE			Update Group code contact group
===========================================================================================
*/

-- Create the new procedure
CREATE PROCEDURE [dbo].[usp_GroupCodeContactGroup_Edit]    
(    
    @ItemId int,
    @ContactName varchar(100),    
    @Code varchar(50),    
    @ContactGroupType varchar(50),    
    @UpdatedBy int,
    @RowsAffected int OUTPUT    
)    
AS    
BEGIN    
    SET NOCOUNT ON;

    DECLARE @ErrInSaving int = 0;

    -- Check for existing group with the same name but different code (excluding the current group being edited)
    IF EXISTS (SELECT 1 FROM tbContactGroup    
               WHERE ContactName = LTRIM(RTRIM(@ContactName))
               AND Code != LTRIM(RTRIM(@Code)) 
               AND ContactGroupType = @ContactGroupType
               AND ItemId != @ItemId)
    BEGIN
        SET @ErrInSaving = -1;
    END

    -- Check for existing group with the same code but different name (excluding the current group being edited)
    IF EXISTS (SELECT 1 FROM tbContactGroup    
               WHERE ContactName != LTRIM(RTRIM(@ContactName))
               AND Code = LTRIM(RTRIM(@Code)) 
               AND ContactGroupType = @ContactGroupType
               AND ItemId != @ItemId)
    BEGIN
        SET @ErrInSaving = -1;
    END

	IF EXISTS (SELECT 1 FROM tbContactGroup
               WHERE ContactName = LTRIM(RTRIM(@ContactName))
               AND Code = LTRIM(RTRIM(@Code))
               AND ContactGroupType = @ContactGroupType
               AND ItemId != @ItemId)
    BEGIN
        SET @ErrInSaving = -1;
    END

    IF @ErrInSaving = -1
    BEGIN
        SET @RowsAffected = -1;  
    END
    ELSE
    BEGIN
        -- Update the existing group
        UPDATE tbContactGroup
        SET ContactName = @ContactName,
            Code = @Code,
            ContactGroupType = @ContactGroupType,
            UpdatedBy = @UpdatedBy,
            DLUP = GETDATE()
        WHERE ItemId = @ItemId;

        SET @RowsAffected = @@ROWCOUNT;  
    END

    SELECT @RowsAffected;
END;
