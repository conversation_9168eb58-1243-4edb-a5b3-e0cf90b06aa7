Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");Rebound.GlobalTrader.Site.Controls.DropDowns.Printer=function(n){Rebound.GlobalTrader.Site.Controls.DropDowns.Printer.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.DropDowns.Printer.prototype={initialize:function(){Rebound.GlobalTrader.Site.Controls.DropDowns.Printer.callBaseMethod(this,"initialize");this.addSetupDataCall(Function.createDelegate(this,this.setupDataCall));this.addGetDataOK(Function.createDelegate(this,this.dataCallOK))},dispose:function(){this.isDisposed||Rebound.GlobalTrader.Site.Controls.DropDowns.Printer.callBaseMethod(this,"dispose")},setupDataCall:function(){this._objData.set_PathToData("controls/DropDowns/Printer");this._objData.set_DataObject("Printer");this._objData.set_DataAction("GetData")},dataCallOK:function(){var t=this._objData._result,n;if(t.Printers)for(n=0;n<t.Printers.length;n++)this.addOption(t.Printers[n].Name,t.Printers[n].ID)}};Rebound.GlobalTrader.Site.Controls.DropDowns.Printer.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.Printer",Rebound.GlobalTrader.Site.Controls.DropDowns.Base);