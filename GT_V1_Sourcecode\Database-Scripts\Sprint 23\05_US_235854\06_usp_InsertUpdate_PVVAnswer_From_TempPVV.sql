﻿/*
============================================================================================================================ 
TASK			UPDATED BY       DATE				ACTION		DESCRIPTION  
[US-235854]     CuongDox		 12-Mar-2025		CREATE		IPO- Simplified HUBRFQ Creation - Addition of PPV/ Bom Qualification at creation stage (Client Side)
============================================================================================================================  
*/
CREATE OR ALTER PROCEDURE [dbo].[usp_InsertUpdate_PVVAnswer_From_TempPVV]    
      @BomIdGenerated NVARCHAR(100)    
    , @BOMId int    
    , @RowsAffected int OUTPUT    
AS     
      BEGIN     
     INSERT INTO tbHUBRFQPVVAnswer (
		PVVQuestionNo,
		BomNo,
		HUBRFQNo,
		PVVAnswerName,
		ClientNo,
		UpdatedBy
	)
	SELECT 
		PVVQuestionNo,
		@BOMId,
		HUBRFQNo,
		PVVAnswerName,
		ClientNo,
		UpdatedBy
	FROM tbHUBRFQPVVAnswerTemp
	WHERE BomNoGenerated = @BomIdGenerated

	--delete after inserted
	DELETE FROM tbHUBRFQPVVAnswerTemp WHERE BomNoGenerated = @BomIdGenerated
           
      SET @RowsAffected = @@rowcount    
    
 END    