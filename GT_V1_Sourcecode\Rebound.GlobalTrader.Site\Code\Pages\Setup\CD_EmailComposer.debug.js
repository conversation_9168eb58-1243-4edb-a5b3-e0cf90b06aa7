///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Pages.Setup");

Rebound.GlobalTrader.Site.Pages.Setup.CD_EmailComposer = function(el) { 
	Rebound.GlobalTrader.Site.Pages.Setup.CD_EmailComposer.initializeBase(this, [el]);
};

Rebound.GlobalTrader.Site.Pages.Setup.CD_EmailComposer.prototype = {

    get_ctlEmailComposer: function() { return this._ctlEmailComposer; }, set_ctlEmailComposer: function(v) { if (this._ctlEmailComposer !== v) this._ctlEmailComposer = v; },
    initialize: function() {
        Rebound.GlobalTrader.Site.Pages.Setup.CD_EmailComposer.callBaseMethod(this, "initialize");
    },

    goInit: function() {
        Rebound.GlobalTrader.Site.Pages.Setup.CD_EmailComposer.callBaseMethod(this, "goInit");
      //  this._ctlEmailComposer.getData();
    },

    dispose: function() {
        if (this.isDisposed) return;
        if (this._ctlEmailComposer) this._ctlEmailComposer.dispose();
        this._ctlEmailComposer = null;
        Rebound.GlobalTrader.Site.Pages.Setup.CD_EmailComposer.callBaseMethod(this, "dispose");
    },


    showNuggets: function(bln) {
        this._ctlEmailComposer.show(bln);
    }

};

Rebound.GlobalTrader.Site.Pages.Setup.CD_EmailComposer.registerClass("Rebound.GlobalTrader.Site.Pages.Setup.CD_EmailComposer", Rebound.GlobalTrader.Site.Pages.Content, Sys.IDisposable);
