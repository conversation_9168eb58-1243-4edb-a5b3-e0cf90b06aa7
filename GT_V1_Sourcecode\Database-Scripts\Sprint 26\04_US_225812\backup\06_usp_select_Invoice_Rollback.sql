﻿  
  
--Marker     Changed by      Date         Remarks    
--[001]      Vinay           01/11/2012   Add comma(,) seprated credit notes and CustomerRMA in invoice section    
CREATE OR ALTER PROCEDURE [dbo].[usp_select_Invoice]      
    @InvoiceId int        
AS        
BEGIN      
      
    Declare @vSalesOrderNos Varchar(1000)      
    Declare @vSalesOrderNumbers Varchar(1000)      
        
    Declare @vCRMAIDs Varchar(1000)    
    Declare @vCRMANumbers Varchar(1000)    
        
    Declare @vCreditIDs Varchar(1000)    
    Declare @vCreditNumbers Varchar(1000)   
   
   
                      
    Execute usp_select_SalesOrderNos_By_Invoice @InvoiceId, @vSalesOrderNos Out, @vSalesOrderNumbers Out     
        
    Execute usp_select_CustomerRMANos_By_Invoice @InvoiceId,@vCRMAIDs Out,@vCRMANumbers Out    
        
    Execute usp_select_CreditNos_By_Invoice @InvoiceId,@vCreditIDs Out,@vCreditNumbers Out  
   --------------changes------------------  
    declare @ExchangeRate1 float   
    declare @TotalValue float  declare @ShipSurChargeValue float  declare @ShippingSurchargePercent float  select @ExchangeRate1 = dbo.ufn_get_exchange_rate(CurrencyNo, isnull(InvoiceDate,GETDATE())),@ShippingSurchargePercent=ShippingSurchargePercent from
  
  
 tbInvoice where InvoiceId = @InvoiceId   select @TotalValue = dbo.ufn_calculate_InvoicePriceForSurcharge(@InvoiceId)/@ExchangeRate1     set @ShipSurChargeValue =round( ((@TotalValue*isnull(@ShippingSurchargePercent,0))/100),2)  
 --change end------------  
  
    SELECT  *      
    , @vSalesOrderNos As 'SalesOrderNos', @vSalesOrderNumbers As 'SalesOrderNumbers'     
    , @vCRMAIDs As 'CRMAIds',@vCRMANumbers As 'CRMANumbers'      
    , @vCreditIDs As 'CreditIds',@vCreditNumbers As 'CreditNumbers'    
    , (case when isnull(vi.IsAppShippingSurcharge,0)=0 then   @ShipSurChargeValue else vi.ShippingSurchargeValue end) as  AppliedShippingSurcharge  
    FROM    dbo.vwInvoice  vi  
    WHERE   InvoiceId = @InvoiceId        
END    
    
  
  