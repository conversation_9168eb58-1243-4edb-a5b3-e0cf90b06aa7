﻿//Marker     Changed by       Date         Remarks
//[001]      <PERSON><PERSON><PERSON><PERSON>   05/10/2021   Add new dropdown for Ship SO Status
using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;
using Rebound.GlobalTrader.DAL;
using Rebound.GlobalTrader.BLL;

namespace Rebound.GlobalTrader.BLL
{
    public partial class ShipSOStatus : BizObject
    {
        #region Properties

        /// <summary>
        /// StatusId
        /// </summary>
        public System.Int32 StatusId { get; set; }

        /// <summary>
        /// StatusName
        /// </summary>
        public System.String StatusName { get; set; }
       
        #endregion

        #region Methods

        /// <summary>
        /// DropDownForClient
        /// Calls [usp_dropdown_Login_for_Client]
        /// </summary>
        public static List<ShipSOStatus> DropDownForClient()
        {
            List<ShipSOStatusDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.ShipSOStatus.DropDownForClient();
            if (lstDetails == null)
            {
                return new List<ShipSOStatus>();
            }
            else
            {
                List<ShipSOStatus> lst = new List<ShipSOStatus>();
                foreach (ShipSOStatusDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.ShipSOStatus obj = new Rebound.GlobalTrader.BLL.ShipSOStatus();
                    obj.StatusId = objDetails.StatusId;
                    obj.StatusName = objDetails.StatusName;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }
        public static List<ShipSOStatus> DropDownForReadyStatus()
        {
            List<ShipSOStatusDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.ShipSOStatus.DropDownForReadyStatus();
            if (lstDetails == null)
            {
                return new List<ShipSOStatus>();
            }
            else
            {
                List<ShipSOStatus> lst = new List<ShipSOStatus>();
                foreach (ShipSOStatusDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.ShipSOStatus obj = new Rebound.GlobalTrader.BLL.ShipSOStatus();
                    obj.StatusId = objDetails.StatusId;
                    obj.StatusName = objDetails.StatusName;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }
        #endregion
    }
}
