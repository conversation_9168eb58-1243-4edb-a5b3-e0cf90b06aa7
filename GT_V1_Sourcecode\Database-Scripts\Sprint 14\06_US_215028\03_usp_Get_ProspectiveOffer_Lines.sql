﻿SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
/*   
===========================================================================================  
TASK			UPDATED BY			DATE			ACTION		DESCRIPTION  
[US-211441]		Trung Pham Van		24-Sep-2024		CREATE		Get Prospective Offer Lines By Id
[US-211441]		Trung Pham Van		09-Oct-2024		UPDATE		Refactor script and convert value follow currency
[US-211441]		Trung Pham Van		11-Oct-2024		UPDATE		Fix issue when return missing line
[US-211441]		Trung Pham Van		14-Oct-2024		UPDATE		Remove Round, Add SentDate
[US-215028]		Trung Pham Van		29-Oct-2024		UPDATE		Add Notes columns, Get ManufacturerNo
[US-215028]		Trung Pham Van		12-Nov-2024		UPDATE		Add FullPart columns, Change get Lytica value
[US-215028]		Trung Pham Van		12-Nov-2024		UPDATE		Remove SentDate
===========================================================================================
*/  
CREATE OR ALTER PROCEDURE [dbo].[usp_Get_ProspectiveOffer_Lines]
	@ProspectiveOfferId INT = NULL,
	@CurPage INT = 1,
	@Rpp INT = 10
AS
BEGIN
	DECLARE @TotalRecords INT, @Skip INT
    SELECT @TotalRecords = COUNT(prol.ProspectiveOfferLineId)
    FROM tbProspectiveOfferLines prol
    WHERE prol.ProspectiveOfferNo = @ProspectiveOfferId

    SET @Skip = (@Rpp * (@CurPage - 1))
    IF (@Skip >= @TotalRecords AND @TotalRecords > 0)
    BEGIN
        SET @CurPage = CAST(@TotalRecords / @Rpp AS INT)
        SET @Skip = CAST((@Rpp * (@CurPage - 1)) AS INT)
    END

    DECLARE @ClientCurrencyNo INT, @CurrencyCode VARCHAR(MAX), @GlobalClientCurrencyNo INT
    SELECT @ClientCurrencyNo = CurrencyNo FROM tbClient WHERE ClientId = 114
	SELECT @CurrencyCode = CurrencyCode,  @GlobalClientCurrencyNo = GlobalCurrencyNo FROM tbCurrency WHERE CurrencyId = @ClientCurrencyNo
	
    ;WITH OfferPrices AS (
        SELECT 
            prol.Part,
			prol.FullPart,
            MAX(cr.Price) AS Highest,
            MIN(cr.Price) AS Lowest,
			COUNT(cr.CustomerRequirementId) AS NumOfReq,
			c.GlobalCurrencyNo
        FROM tbProspectiveOfferLines prol
        JOIN tbCustomerRequirement cr ON cr.FullPart = prol.FullPart
		JOIN tbCurrency c ON c.CurrencyId = prol.CurrencyNo
        WHERE prol.ProspectiveOfferNo = @ProspectiveOfferId 
          AND DATEDIFF(MONTH, cr.ReceivedDate, GETDATE()) <= 12 
          AND cr.BOMNo IS NOT NULL
        GROUP BY prol.Part, prol.FullPart, c.GlobalCurrencyNo
    )
    SELECT DISTINCT 
        prol.ProspectiveOfferLineId,
        prol.Part AS PartNo,
        '' AS HUBRFQCustomer,
        prol.Quantity AS QuantityOffered,
        prol.Price AS UploadedOfferPrice,
		op.GlobalCurrencyNo AS ReqGlobalCurrencyNo,
        op.Lowest AS GTLowestPrice,
        op.Highest AS GTHighestPrice,
        @CurrencyCode AS Currency,
        @GlobalClientCurrencyNo AS CurrencyNo,
        ISNULL(ihs.AveragePrice, ISNULL(dbo.ufn_extract_IHS_AvgPrice(ihs.Descriptions), 0)) AS IHSAvgPrice,
        ISNULL(lytica.AveragePrice, 0) AS LyticaAvgPrice,
        op.NumOfReq,
        NULL AS QuotePrice,
        NULL AS QuoteQTY,
        NULL AS SOPrice,
        NULL AS SOQTY,
        NULL AS SOTaxable,
        NULL AS SODate,
        NULL AS SOTaxRate,
        mfr.ManufacturerName AS Manufacturer,
        NULL AS FileShared,
		0 AS IsFromGT,
		prol.Notes,
		prol.ManufacturerNo
    FROM tbProspectiveOffers pro
    JOIN tbProspectiveOfferLines prol ON pro.ProspectiveOfferId = prol.ProspectiveOfferNo
    LEFT JOIN OfferPrices op ON op.FullPart = prol.FullPart
    LEFT JOIN tbManufacturer mfr ON mfr.ManufacturerId = prol.ManufacturerNo
    LEFT JOIN tbIHSparts ihs ON ihs.FullPart = prol.FullPart AND ihs.ManufacturerFullName = mfr.ManufacturerName
    --LEFT JOIN tbLyticaAPI lytica ON dbo.ufn_get_fullpart(OriginalPartSearched) = prol.FullPart AND lytica.Manufacturer = mfr.ManufacturerName
	OUTER APPLY (
		SELECT  TOP 1 *
			FROM tbLyticaAPI
			WHERE OriginalPartSearched = prol.Part 
			AND ISNULL(Inactive, 0) = 0
			AND (Manufacturer = mfr.ManufacturerName OR Manufacturer LIKE mfr.ManufacturerName + '%' OR Manufacturer LIKE [dbo].[ufn_GetFirstWord](mfr.ManufacturerName) + '%')
	) lytica
    WHERE ISNULL(ihs.Inactive, 0) = 0 
      AND ISNULL(lytica.Inactive, 0) = 0
	  AND prol.ProspectiveOfferNo = @ProspectiveOfferId
    GROUP BY prol.ProspectiveOfferLineId,
        prol.Part,
		prol.FullPart,
        prol.Quantity,
        prol.Price,
		op.GlobalCurrencyNo,
        op.Lowest,
        op.Highest,
		op.NumOfReq,
        ihs.AveragePrice,
        ihs.Descriptions,
        lytica.AveragePrice,
        mfr.ManufacturerName,
		prol.Notes,
		prol.ManufacturerNo
    ORDER BY prol.ProspectiveOfferLineId
END
