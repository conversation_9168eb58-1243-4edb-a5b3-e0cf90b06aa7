<%@ Control Language="C#" CodeBehind="ExportApprovalStatus_Approvals.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Forms.ExportApprovalStatus_Approvals" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<script src="../../../../js/PowerBIToken.js" type="text/javascript"></script>
<%@ Register TagPrefix="cc1" Namespace="Rebound.GlobalTrader.Site" Assembly="Rebound.GlobalTrader.Site" %>
<ReboundUI_Form:DesignBase ID="ctlDB" runat="server" ShowRequiredFieldsExplanation="false" AllowQuickHelp="false">

	<Explanation>	
		<asp:Label ID="lblExplainExportApprove" runat="server" CssClass="invisible"><%=Functions.GetGlobalResource("FormExplanations", "OGELApproveExplntn")%></asp:Label>
	</Explanation>
	
	<Content>
	
		<ReboundUI_Table:Form id="frm" runat="server">
            
            <ReboundUI_Form:FormField id="ctlSalesPerson" runat="server" FieldID="lblSalesPerson" ResourceTitle="OGELSalesPerson">
	            <Field><asp:Label ID="lblSalesPerson" runat="server" /></Field>
            </ReboundUI_Form:FormField>
            <ReboundUI_Form:FormField id="ctlSalesOrder" runat="server" FieldID="lblSalesOrder" ResourceTitle="SalesOrder">
	            <Field><asp:Label ID="lblSalesOrder" runat="server" /></Field>
            </ReboundUI_Form:FormField>
            <ReboundUI_Form:FormField id="ctlSOLineNo" runat="server" FieldID="lblSOLineNo" ResourceTitle="OGELSOLine">
	            <Field><asp:Label ID="lblSOLineNo" runat="server" /></Field>
            </ReboundUI_Form:FormField>
			<ReboundUI_Form:FormField id="ctlCustomer" runat="server" FieldID="lblCustomer" ResourceTitle="Customer">
	            <Field><asp:Label ID="lblCustomer" runat="server" /></Field>
            </ReboundUI_Form:FormField>
            <ReboundUI_Form:FormField id="ctlPartNumber" runat="server" FieldID="lblPartNumber" ResourceTitle="PartNumber">
	            <Field><asp:Label ID="lblPartNumber" runat="server" /></Field>
            </ReboundUI_Form:FormField>
            <ReboundUI_Form:FormField id="ctlDestinationCountry" runat="server" FieldID="lblDestinationCountry" ResourceTitle="OGELDestinationCountry">
	            <Field><asp:Label ID="lblDestinationCountry" runat="server" /></Field>
            </ReboundUI_Form:FormField>
            <ReboundUI_Form:FormField id="ctlOGELNumber" runat="server" FieldID="ddlOGELNumber" ResourceTitle="OgelNumber" >
                <Field><ReboundDropDown:OGELLicenseNumber ID="ddlOGELNumber" runat="server" IncludeNoValue="true" NoValue_Value="" InitialValue="" /></Field>
            </ReboundUI_Form:FormField>
<%--            <ReboundUI_Form:FormField id="ctlMilitaryuse" runat="server" FieldID="ddlMilitaryuse" ResourceTitle="OgelMilitaryUse" >
                <Field><ReboundDropDown:Military ID="ddlMilitaryuse" runat="server" IncludeNoValue="true" NoValue_Value="" InitialValue="" /></Field>
            </ReboundUI_Form:FormField>--%>
            <ReboundUI_Form:FormField id="ctlMilitaryuseName" runat="server" FieldID="lbllmilitaryUseName" ResourceTitle="OgelMilitaryUse" >
                <Field><asp:Label ID="lbllmilitaryUseName" runat="server" /></Field>
            </ReboundUI_Form:FormField>
            <ReboundUI_Form:FormField id="ctlEndUser" runat="server" FieldID="txtEndUser" ResourceTitle="OgelEndUser">
				<Field><ReboundUI:ReboundTextBox ID="txtEndUser" runat="server" Style="height:49px; width:310px" Width="310" TextMode="MultiLine" Rows="2" CountChar="true" Enabled="false"/></Field>          
			</ReboundUI_Form:FormField>
            <ReboundUI_Form:FormField id="ctlEUUForm" runat="server" FieldID="ibtnGeneratePDF" ResourceTitle="OGELEUUForm">
                <Field><ReboundUI:IconButton ID="ibtnGeneratePDF" runat="server" IconGroup="Nugget" IconTitleResource="OGELEUUForm" IconButtonMode="HyperLink" IconCSSType="CreatePDF" Href="javascript:void(0);" /></Field>
            </ReboundUI_Form:FormField>
            <ReboundUI_Form:FormField id="ctlEUUFormLbl" runat="server" FieldID="lbllNoFileFound" ResourceTitle="OGELEUUForm">
                <Field><asp:Label ID="lbllNoFileFound" runat="server" />File Not Uploaded</Field>
            </ReboundUI_Form:FormField>
            <ReboundUI_Form:FormField id="ctlComment" runat="server" FieldID="txtComment" ResourceTitle="Comment">
				<Field><ReboundUI:ReboundTextBox ID="txtComment" runat="server" Style="height:49px; width:310px" Width="310" TextMode="MultiLine" Rows="2" CountChar="true" /></Field>          
			</ReboundUI_Form:FormField>
            <ReboundUI_Form:FormField id="ctlSendMail" runat="server" FieldID="chkSendMail" ResourceTitle="NotifySales">
				<Field><ReboundUI:ImageCheckBox ID="chkSendMail" runat="server" Enabled="true" Checked="true" /></Field>
			</ReboundUI_Form:FormField>
			<ReboundUI_Form:FormField id="ctlConfirm" runat="server" FieldID="ctlConfirmation" ResourceTitle="">
				<Field><ReboundUI:ConfirmationExport ID="ctlConfirmation" runat="server" /></Field>
			</ReboundUI_Form:FormField>
			
		</ReboundUI_Table:Form>
		
	</Content>
	
</ReboundUI_Form:DesignBase>
