﻿//Marker     changed by      date          Remarks
//[001]      Ab<PERSON><PERSON>   28-Jul-2021   Add new property for repeat order
//[002]      Ab<PERSON>av <PERSON>   22-Sep-2021   Add new property for TypeNo
//[003]      Abhinav <PERSON>a   26-Oct-2021   Add new properties named QualityApproveDate & LineManagerApproveDate.
//[004]      Abhinav <PERSON>   05-Jan-2021   Add new property for Line manager Email.
//[005]      Ab<PERSON>av <PERSON>   20-Jan-2021   Add new property IsEscalate.
//[006]      Abhinav <PERSON>   27-Jan-2022   Add Line Manager Snapshot.
//[007]      Abhinav <PERSON>xena   15-Mar-2022   Add new props for approval permissions.
using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;

namespace Rebound.GlobalTrader.DAL
{
    public class SupplierPoApprovalDetails
    {
        #region Constructors

        public SupplierPoApprovalDetails() { }

        #endregion



        #region Properties
        public System.Int32? PurchaseOrderId { get; set; }
        public System.Int32? PurchaseOrderNumber { get; set; }
        public System.Int32? POEscalationGroupId { get; set; }
        public System.Int32? SupplierApprovalId { get; set; }
        public System.Int32? SupplierId { get; set; }
        public System.String SupplierName { get; set; }
        public System.Int32? ApprovedOrdersCount { get; set; }
        public System.Int32? SupplierRMAsCount { get; set; }
        public System.Int32? PurchasingMethodNo { get; set; }
        public System.String PurchasingMethod { get; set; }
        public System.String QualityApproval { get; set; }
        public System.String QualityApprovedBy { get; set; }
        public System.String LineManagerApproval { get; set; }
        public System.String LineManagerApprovedBy { get; set; }
        public System.Boolean? SupplierERAIReported { get; set; }
        public System.Boolean? PartIsERAIReported { get; set; }
        public System.Boolean? IsQualityApproved { get; set; }
        //[005] code start
        public System.Boolean? IsEscalate { get; set; }
        //[005] code end

        //[006] code start
        public System.String PartNo { get; set; }
        public System.Int32? Quantity { get; set; }
        public System.String PaymentTerms { get; set; }
        public System.String Incoterms { get; set; }
        public System.String ShipVia { get; set; }
        public System.String ShipFromCountry { get; set; }
        public System.DateTime? DeliveryDateToRebound { get; set; }
        public System.DateTime? DeliveryDateToCustomer { get; set; }
        public System.String TotalValueOfPOCurrency { get; set; }
        public System.String Margin { get; set; }
        public System.String HUBRFQQuoteNo { get; set; }
        public System.String RepeatOrder { get; set; }
        public System.String ReboundPurchaserDivision { get; set; }
        public System.String GTClinetForPO { get; set; }
        public System.String Warehouse { get; set; }
        public System.String CustomerDefinedVendor { get; set; }
        //[006] code end
        public System.Boolean? IsLineManagerApproved { get; set; }
        public System.String TradeReferenceOne { get; set; }
        public System.Boolean? IsPDFAvalableOne { get; set; }
        public System.String TradeReferenceTwo { get; set; }
        public System.Boolean? IsPDFAvalableTwo { get; set; }
        public System.String TradeRefrenceThree { get; set; }
        public System.Boolean? IsPDFAvalableThree { get; set; }
        public System.String ApprovalStatus { get; set; }
        public System.DateTime? ApprovedDated { get; set; }
        public System.String ApprovedBy { get; set; }
        public System.Int32? BuyerId { get; set; }
        public System.String BuyerName { get; set; }
        public System.Int32? LineManagerId { get; set; }
        public System.String LineManagerName { get; set; }
        public System.String LineManagerEmail { get; set; }
        public System.Boolean? Result { get; set; }
        public System.Int32? QualityGroupId { get; set; }
        public System.String FranchiseweblinkOrEvidence { get; set; }
        public System.Int32? PrecogsSupplierNo { get; set; }
        public System.Int32? ImageId { get; set; }
        public System.DateTime? DLUP { get; set; }
        public System.String Caption { get; set; }
        public System.String FullCaption { get; set; }
        public System.String ImageName { get; set; }
        public System.Int32? UpdatedBy { get; set; }
        public System.String TypeNo { get; set; }
        public System.DateTime? QualityApproveDate { get; set; }
        public System.DateTime? LineManagerApproveDate { get; set; }
        public System.Boolean? InDraftMode { get; set; }
        public System.String Status { get; set; }
        public System.Int32? EvidenceCount { get; set; }
        public System.Int32? TradeRefOneCount { get; set; }
        public System.Int32? TRadeRefTwoCount { get; set; }
        public System.Int32? TradeRefThreeCount { get; set; }
        public System.Int32? DevicePictureCount { get; set; }
        public System.Int32? ManufacturerPictureCount { get; set; }
        public System.Int32? TraceblityPictureCount { get; set; }
        public System.Boolean? IsSendToLineManager { get; set; }
        public System.DateTime? SendToLineManagerDLUP { get; set; }
        public System.String SendToLineManagerUpdatedBy { get; set; }
        public System.Boolean? IsSendToSupplier { get; set; }
        public System.DateTime? SendToSupplierDLUP { get; set; }
        public System.String SendToSupplierUpdatedBy { get; set; }
        public System.Boolean? IsSendToQuality { get; set; }
        public System.DateTime? SendToQualityDLUP { get; set; }
        public System.String SendToQualityUpdatedBy { get; set; }
        public System.Int32? SupplierApprovalStatus { get; set; }
        public System.String CommentText { get; set; }
        public System.Boolean? ApproverPartERAIReported { get; set; }
        public System.String SupplierType { get; set; }
        public System.Boolean? IsPOApproved { get; set; }
        //[007] code start
        public System.Boolean? IsLineManagerApprovalPermission { get; set; }
        public System.Boolean? IsQualityTeamApprovalPermission { get; set; }
        public System.Boolean? ISEscalationApprovalPermission { get; set; }
        public System.String LineManagerComment { get; set; }
        public System.String QualityComment { get; set; }
        public System.Int32? WarrantyPeriod { get; set; }
        public System.Int32? CountryOnHighRisk { get; set; }
        public System.Int32? ClientNo { get; set; }
        //[007] code end
        #endregion
    }
}
