﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Services;
using Rebound.GlobalTrader.BLL;

namespace Rebound.GlobalTrader.Site.Controls.ItemSearch.Data
{
    [WebService(Namespace = "http://tempuri.org/")]
    [WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    public class SIDebitNotes : Rebound.GlobalTrader.Site.Data.ItemSearch.Base
    {
        protected override void GetData()
        {
            List<Debit> lst;
            try
            {
                List<string> listAddedDebit = new List<string>();
                var addedDebits = GetFormValue_String("AddedDebit");
                if (!string.IsNullOrEmpty(addedDebits))
                {
                    listAddedDebit = addedDebits.Split('/').ToList();
                }
                var addedDebit = GetFormValue_String("AddedDebit");
                lst = BLL.Debit.SupplierInvoiceItemSearch(
                    Convert.ToInt32(SessionManager.ClientID)
                    , GetFormValue_Int("CompanyNo")
                    , GetFormValue_NullableInt("PurchaseOrderNumber",null)
                    , GetFormValue_NullableDateTime("DebitDateFrom", null)
                    , GetFormValue_NullableDateTime("DebitDateTo", null)
                    );

                JsonObject jsn = new JsonObject();
                JsonObject jsnItems = new JsonObject(true);
                for (int i = 0; i < lst.Count; i++)
                {
                    JsonObject jsnItem = new JsonObject();
                    jsnItem.AddVariable("ID", lst[i].DebitId);
                    jsnItem.AddVariable("DebitNoteNumber", lst[i].DebitNumber);
                    jsnItem.AddVariable("Date", Functions.FormatDate(lst[i].DebitDate));
                    jsnItem.AddVariable("PurchaseOrderNumber", lst[i].PurchaseOrderNumber);
                    jsnItem.AddVariable("IPONumber", lst[i].InternalPurchaseOrderNumber);
                    jsnItem.AddVariable("SupplierRMANo", lst[i].SupplierRMANumber);
                    jsnItem.AddVariable("SupplierNotes", Functions.ReplaceLineBreaks(lst[i].Notes));
                    jsnItem.AddVariable("IsDebitNoteSelected", listAddedDebit.Contains(lst[i].DebitNumber.ToString()));
                    jsnItem.AddVariable("DebitAmount", Functions.FormatCurrency(lst[i].DebitValue, lst[i].CurrencyCode, 2, true));
                    jsnItems.AddVariable(jsnItem);
                    jsnItem.Dispose();
                    jsnItem = null;
                }
                jsn.AddVariable("Results", jsnItems);
                OutputResult(jsn);
                jsnItems.Dispose();
                jsnItems = null;
                jsn.Dispose();
                jsn = null;
            }
            catch (Exception ex)
            {
                WriteError(ex);
            }
            finally
            {
                lst = null;
            }
            base.GetData();
        }
    }
}