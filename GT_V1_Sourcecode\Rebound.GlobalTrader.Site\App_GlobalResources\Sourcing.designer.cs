//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Resources {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option or rebuild the Visual Studio project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.VisualStudio.Web.Application.StronglyTypedResourceProxyBuilder", "16.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class Sourcing {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Sourcing() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Resources.Sourcing", global::System.Reflection.Assembly.Load("App_GlobalResources"));
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Alternative Parts.
        /// </summary>
        internal static string AltPartInfo {
            get {
                return ResourceManager.GetString("AltPartInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier Live Data Feeds.
        /// </summary>
        internal static string APIExternalLinks {
            get {
                return ResourceManager.GetString("APIExternalLinks", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to EPO.
        /// </summary>
        internal static string EPO {
            get {
                return ResourceManager.GetString("EPO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Strategic Offers.
        /// </summary>
        internal static string ExcessAvailability {
            get {
                return ResourceManager.GetString("ExcessAvailability", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Offers History.
        /// </summary>
        internal static string History {
            get {
                return ResourceManager.GetString("History", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to IPO.
        /// </summary>
        internal static string IPO {
            get {
                return ResourceManager.GetString("IPO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select a part to source above.
        /// </summary>
        internal static string NoPartSelected {
            get {
                return ResourceManager.GetString("NoPartSelected", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Offers.
        /// </summary>
        internal static string Offers {
            get {
                return ResourceManager.GetString("Offers", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to HUB Offer.
        /// </summary>
        internal static string OffersAll {
            get {
                return ResourceManager.GetString("OffersAll", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sourcing.
        /// </summary>
        internal static string POHubSourcing {
            get {
                return ResourceManager.GetString("POHubSourcing", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Price Request.
        /// </summary>
        internal static string POQuotes {
            get {
                return ResourceManager.GetString("POQuotes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Purchases.
        /// </summary>
        internal static string Purchases {
            get {
                return ResourceManager.GetString("Purchases", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quotes.
        /// </summary>
        internal static string Quotes {
            get {
                return ResourceManager.GetString("Quotes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer Requirements.
        /// </summary>
        internal static string Requirements {
            get {
                return ResourceManager.GetString("Requirements", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reverse Logistics.
        /// </summary>
        internal static string ReverseLogisticHUBRFQ {
            get {
                return ResourceManager.GetString("ReverseLogisticHUBRFQ", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reverse Logistics.
        /// </summary>
        internal static string ReverseLogistics {
            get {
                return ResourceManager.GetString("ReverseLogistics", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sales.
        /// </summary>
        internal static string Sales {
            get {
                return ResourceManager.GetString("Sales", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Previous HUBRFQ Offer.
        /// </summary>
        internal static string SourcingIPO {
            get {
                return ResourceManager.GetString("SourcingIPO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Last-3 Months StockOfferTrusted.
        /// </summary>
        internal static string SourcingStockOfferTrusted {
            get {
                return ResourceManager.GetString("SourcingStockOfferTrusted", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Stock /On Order.
        /// </summary>
        internal static string Stock {
            get {
                return ResourceManager.GetString("Stock", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sourcing Info.
        /// </summary>
        internal static string StockInfo {
            get {
                return ResourceManager.GetString("StockInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Strategic Offers.
        /// </summary>
        internal static string StrategicOffers {
            get {
                return ResourceManager.GetString("StrategicOffers", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Trusted.
        /// </summary>
        internal static string Trusted {
            get {
                return ResourceManager.GetString("Trusted", resourceCulture);
            }
        }
    }
}
