Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DataListNuggets");Rebound.GlobalTrader.Site.Controls.DataListNuggets.Lots=function(n){Rebound.GlobalTrader.Site.Controls.DataListNuggets.Lots.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.DataListNuggets.Lots.prototype={initialize:function(){this.addGetDataOKEvent(Function.createDelegate(this,this.getDataOK));this.addInitCompleteEvent(Function.createDelegate(this,this.initAfterBaseIsReady));this._strPathToData="controls/DataListNuggets/Lots";this._strDataObject="Lots";Rebound.GlobalTrader.Site.Controls.DataListNuggets.Lots.callBaseMethod(this,"initialize")},initAfterBaseIsReady:function(){this.getData()},dispose:function(){this.isDisposed||Rebound.GlobalTrader.Site.Controls.DataListNuggets.Lots.callBaseMethod(this,"dispose")},getDataOK:function(){for(var t=0,i=this._objResult.Results.length;t<i;t++){var n=this._objResult.Results[t],r=[$RGT_nubButton_Lot(n.ID,n.Name),$R_FN.setCleanTextValue(n.Code),n.StockCount,n.Consignment,n.OnHold],u=n.Inactive?"ceased":"";this._table.addRow(r,n.ID,!1,null,u);r=null;n=null}}};Rebound.GlobalTrader.Site.Controls.DataListNuggets.Lots.registerClass("Rebound.GlobalTrader.Site.Controls.DataListNuggets.Lots",Rebound.GlobalTrader.Site.Controls.DataListNuggets.Base,Sys.IDisposable);