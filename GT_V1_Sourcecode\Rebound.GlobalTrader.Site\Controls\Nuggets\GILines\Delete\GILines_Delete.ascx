<%@ Control Language="C#" CodeBehind="GILines_Delete.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Forms.GILines_Delete" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>

<ReboundUI_Form:DesignBase ID="ctlDB" runat="server" ShowRequiredFieldsExplanation="false" AllowQuickHelp="false">

	<Explanation><%=Functions.GetGlobalResource("FormExplanations", "GILines_Delete")%></Explanation>
	
	<Content>
	
		<ReboundUI_Table:Form id="frm" runat="server">
		
            <ReboundUI_Form:FormField id="ctlGoodsIn" runat="server" FieldID="lblGoodsIn" ResourceTitle="GoodsInNo" >
	            <Field><asp:Label ID="lblGoodsIn" runat="server" /></Field>
            </ReboundUI_Form:FormField>
            
            <ReboundUI_Form:FormField id="ctlSupplier" runat="server" FieldID="lblSupplier" ResourceTitle="Supplier">
	            <Field><asp:Label ID="lblSupplier" runat="server" /></Field>
            </ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlPartNo" runat="server" FieldID="lblPart" ResourceTitle="PartNo">
				<Field><asp:Label ID="lblPart" runat="server" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlQuantity" runat="server" FieldID="lblQuantity" ResourceTitle="Quantity">
				<Field><asp:Label ID="lblQuantity" runat="server" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlConfirm" runat="server" FieldID="ctlConfirmation" ResourceTitle="Confirm">
				<Field><ReboundUI:Confirmation ID="ctlConfirmation" runat="server" /></Field>
			</ReboundUI_Form:FormField>
			
		</ReboundUI_Table:Form>
		
	</Content>
	
</ReboundUI_Form:DesignBase>
