﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

/* 
===========================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-210540]		Phuc Hoang			07-Aug-2024		UPDATE			[PROD Bug] DHL Interface - Label Print Issue
[US-210981]		Phuc Hoang			14-Aug-2024		UPDATE			[PROD BUG] UPS Exporter only showing 3 decimal places for unit price
[US-209534]		NgaiTo		 		17-Sep-2024		UPDATE			209534: OGEL approval dropdown to be moved out of code to the setup screen
===========================================================================================
*/

      
  -----OGEL change SP-------------                  
CREATE OR ALTER PROC [dbo].[usp_upsGetCustomInvoice]                                          
(                                          
  @InvoiceNumbers NVARCHAR(MAX) = NULL ,                                          
  @ClientNo INT = NULL                                          
)                                          
AS                                          
BEGIN
DECLARE @OGELNUMBER  NVARCHAR(1000)= NULL;


    SELECT                                           
    inv.InvoiceNumber,                                          
    --'Electronic Components' as DescriptionOfGoods,                   
 case when ((isnull(tbCompany.EORINumber,'')='') and (isnull(ts.OGEL_Required,0)=0 OR (isnull(es.OGELNumber,0)<=0 and isnull(ts.OGEL_Required,0)=1))) then 'Electronic Components'           
 when ((isnull(tbCompany.EORINumber,'')!='') and (isnull(ts.OGEL_Required,0)=0 OR (isnull(es.OGELNumber,0)<=0 and isnull(ts.OGEL_Required,0)=1))) then ('Electronic Components EORI: ' +tbCompany.EORINumber)                  
 when ( isnull(tbCompany.EORINumber,'')=''   and isnull(es.OGELNumber,0)>0  and isnull(ts.OGEL_Required,0)=1) then 'Electronic Components '+ 'OGEL: '+ (
		CASE 
			WHEN es.OGELNumber > 0
				AND ts.OGEL_Required = 1
				THEN (
						--SELECT ct.OGELNumber
						--FROM tbClient ct
						--WHERE ct.ClientId = so.ClientNO
						SELECT ISNULL((SELECT OgelNumber FROM tbOGELLicense WHERE OgelId = es.OGELNumber and Inactive = 0),'GBOGE2020/00615')
						--CASE 
						--	WHEN es.OGELNumber = 1
						--		THEN 'GBOGE2020/00615'
						--	WHEN es.OGELNumber = 2
						--		THEN 'GBOGE2024/00532 - OGEL MIL GMST'
						--	WHEN es.OGELNumber = 3
						--		THEN 'GBOGE2024/00756 - OGEL PCB Comp MIL'
						--	ELSE 'GBOGE2020/00615'
						--	END
						)
			ELSE ''
			END
	)                 
 when ((isnull(tbCompany.EORINumber,'')!='') and isnull(es.OGELNumber,0)>0 and isnull(ts.OGEL_Required,0)=1 ) then 'Electronic Components'+ ' EORI: ' + tbCompany.EORINumber +' OGEL: '+ (
		CASE 
			WHEN es.OGELNumber > 0
				AND ts.OGEL_Required = 1
				THEN (
						--SELECT ct.OGELNumber
						--FROM tbClient ct
						--WHERE ct.ClientId = so.ClientNO
						SELECT ISNULL((SELECT OgelNumber FROM tbOGELLicense WHERE OgelId = es.OGELNumber and Inactive = 0),'GBOGE2020/00615')
						--CASE 
						--	WHEN es.OGELNumber = 1
						--		THEN 'GBOGE2020/00615'
						--	WHEN es.OGELNumber = 2
						--		THEN 'GBOGE2024/00532 - OGEL MIL GMST'
						--	WHEN es.OGELNumber = 3
						--		THEN 'GBOGE2024/00756 - OGEL PCB Comp MIL'
						--	ELSE 'GBOGE2020/00615'
						--	END
						)
			ELSE ''
			END
	) 
 end as DescriptionOfGoods,                  
    InvLine.Part,                                          
    replace(ISNULL(pr.DutyCode,''),' ','') as TaiffCode ,               
    --InvLine.Quantity ,                                 
    isnull((SELECT sum(ila.Quantity)                                        
    FROM dbo.tbInvoiceLineAllocation ila                                        
    WHERE ila.InvoiceLineNo = InvLine.InvoiceLineId), 0) AS Quantity,                                         
    tpck.PackageName as UnitsOfMeasure,                                          
    --InvLine.Price,  
 Round(InvLine.Price,5) as Price,  
    Inv.CurrencyNo ,                                        
    cu.CurrencyCode ,                                        
    --ISNULL(ups.CountryCode,'') As CountryCode ,                                    
  isnull((SELECT Top 1(ups.CountryCode)                                        
  FROM dbo.tbInvoiceLineAllocation ila                                        
  LEFT JOIN dbo.tbGlobalCountryList gc  ON ila.CountryOfManufactureNo = gc.GlobalCountryId                                      
  LEFT JOIN UPSCountryList ups On ups.GTCountryID = gc.GlobalCountryId                                       
   WHERE ila.InvoiceLineNo = InvLine.InvoiceLineId),'') AS CountryCode,                            
    isnull((SELECT Top 1(gc.GlobalCountryId)                                        
  FROM dbo.tbInvoiceLineAllocation ila                                        
  LEFT JOIN dbo.tbGlobalCountryList gc  ON ila.CountryOfManufactureNo = gc.GlobalCountryId                                      
  --LEFT JOIN UPSCountryList ups On ups.GTCountryID = gc.GlobalCountryId                                       
   WHERE ila.InvoiceLineNo = InvLine.InvoiceLineId),'') AS GTCountryNo,                            
   pr.ProductDescription as ProductDescription ,                          
   tbCompany.EORINumber,                        
   tsl.ECCNCode,                        
   --REPLACE(tihscode.HTSCode,'.','') HTSCode                   
   (select top 1 REPLACE(tihscode.HTSCode ,'.','')  from tbihsparts tihscode where tihscode.Part = InvLine.Part)  as HTSCode        
   --,iif(isnull(ts.OGEL_Required,0)=1,tcl.OGELNumber,null) OGELNumber ---Added By Manoj Kumar 29-03-2023--------
   ,(
		CASE 
			WHEN es.OGELNumber > 0
				AND ts.OGEL_Required = 1
				THEN (
						--SELECT ct.OGELNumber
						--FROM tbClient ct
						--WHERE ct.ClientId = so.ClientNO
						SELECT ISNULL((SELECT OgelNumber FROM tbOGELLicense WHERE OgelId = es.OGELNumber and Inactive = 0),'GBOGE2020/00615')
						--CASE 
						--	WHEN es.OGELNumber = 1
						--		THEN 'GBOGE2020/00615'
						--	WHEN es.OGELNumber = 2
						--		THEN 'GBOGE2024/00532 - OGEL MIL GMST'
						--	WHEN es.OGELNumber = 3
						--		THEN 'GBOGE2024/00756 - OGEL PCB Comp MIL'
						--	ELSE 'GBOGE2020/00615'
						--	END
						)
			ELSE ''
			END
	) AS OGELNumber

   --, tit.Code IncotermCode      
  FROM tbInvoiceLine InvLine                         
  JOIN tbInvoice inv on InvLine.InvoiceNo=inv.InvoiceId                    
  Left Outer Join tbCompany On tbCompany.CompanyId = Inv.CompanyNo                    
  LEFT JOIN tbCurrency cu on cu.CurrencyId = inv.CurrencyNo                                        
  LEFT JOIN tbProduct pr ON pr.ProductId = InvLine.ProductNo                    
  Left Join dbo.tbInvoiceLineAllocation tila on tila.InvoiceLineNo = InvLine.InvoiceLineId                        
  Left Join tbSalesOrderLine tsl on tila.SalesOrderLineNo=tsl.SalesOrderNo                   
  --Left Join tbSalesOrder ts on tsl.SalesOrderLineId=ts.SalesOrderId                  
  --Left Join tbSalesOrder ts on tsl.SalesOrderLineId=ts.SalesOrderNumber                  
  Left Join tbSalesOrder ts on ts.SalesOrderId=Inv.SalesOrderNo
  LEFT JOIN tbSO_ExportApprovalStatusOGEL es ON isnull(InvLine.SalesOrderLineNo, 0) = isnull(es.SalesOrderLineNo, 0)
  left join tbPackage tpck on tpck.PackageId=InvLine.PackageNo      
  --left join tbIncoterm tit on tit.IncotermId=inv.IncotermNo      
  --left join tbihsparts tihscode on tihscode.Part=InvLine.Part                     
  --Left Outer join tbSO_ExportApprovalStatusOGEL tse on tse.SalesOrderLineNo=InvLine.SalesOrderLineNo               
  Left join tbclient tcl on tbCompany.ClientNo=tcl.ClientId                  
  Inner Join tbAddress Adrs On Adrs.AddressId = Inv.ShipToAddressNo                         
  Left Outer Join tbCountry On tbCountry.CountryId = Adrs.CountryNo                  
  WHERE ISNULL(InvLine.Inactive,0) = 0 AND inv.ClientNo = @ClientNo                                   
  AND inv.InvoiceNumber In (Select String From dbo.[ufn_splitString](@InvoiceNumbers,','))                    
  And lower(InvLine.Part) not Like 'enhanced inspection%'                
  order by inv.InvoiceNumber                    
END 
GO


