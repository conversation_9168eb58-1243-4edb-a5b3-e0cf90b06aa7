Type.registerNamespace("Rebound.GlobalTrader.Site.Controls");Rebound.GlobalTrader.Site.Controls.TabStrip=function(n){Rebound.GlobalTrader.Site.Controls.TabStrip.initializeBase(this,[n]);this._aryTabIDs=[];this._selectedTabIndex=-1};Rebound.GlobalTrader.Site.Controls.TabStrip.prototype={get_aryTabIDs:function(){return this._aryTabIDs},set_aryTabIDs:function(n){this._aryTabIDs!==n&&(this._aryTabIDs=n)},get_SelectedTabIndex:function(){return this._selectedTabIndex},set_SelectedTabIndex:function(n){this._selectedTabIndex!==n&&(this._selectedTabIndex=n)},get_pnlContent:function(){return this._pnlContent},set_pnlContent:function(n){this._pnlContent!==n&&(this._pnlContent=n)},get_pnlLoading:function(){return this._pnlLoading},set_pnlLoading:function(n){this._pnlLoading!==n&&(this._pnlLoading=n)},get_currentTab:function(){return this._currentTab},set_currentTab:function(n){this._currentTab!==n&&(this._currentTab=n)},addTabIndexChanged:function(n){this.get_events().addHandler("TabIndexChanged",n)},removeTabIndexChanged:function(n){this.get_events().removeHandler("TabIndexChanged",n)},onTabIndexChanged:function(){var n=this.get_events().getHandler("TabIndexChanged");n&&n(this,new Rebound.GlobalTrader.Site.DataEventArgs(this._statusCode,this._errorMessage,this._result,this._url))},initialize:function(){Rebound.GlobalTrader.Site.Controls.TabStrip.callBaseMethod(this,"initialize")},dispose:function(){this.isDisposed||(this.get_element()&&$clearHandlers(this.get_element()),this._selectedTabIndex=null,this._aryTabIDs=null,this._pnlContent=null,this._pnlLoading=null,this._currentTab=null,Rebound.GlobalTrader.Site.Controls.TabStrip.callBaseMethod(this,"dispose"),this.isDisposed=!0)},selectTab:function(n){var i,r,t;if(n!=this._selectedTabIndex){for(i=0,r=this._aryTabIDs.length;i<r;i++)t=$find(this._aryTabIDs[i]),t.selectTab(t._tabIndex==n),$R_FN.showElement(t._pnlRelatedContent,t._tabIndex==n),t=null;this._selectedTabIndex=n;this._currentTab=$find(this._aryTabIDs[this._selectedTabIndex]);this.onTabIndexChanged()}},showContent:function(n){$R_FN.showElement(this._pnlContent,n);n&&$R_FN.showElement(this._pnlLoading,!1)},showLoading:function(n){$R_FN.showElement(this._pnlLoading,n);n&&$R_FN.showElement(this._pnlContent,!1)},resetTabTitles:function(){for(var n=0,t=this._aryTabIDs.length;n<t;n++)this.resetTabTitle(n)},setTabCount:function(n,t){var i=$find(this._aryTabIDs[n]);i&&(i.addCountToTitle(t),i=null)},resetTabTitle:function(n){var t=$find(this._aryTabIDs[n]);t&&t.resetTitle();t=null}};Rebound.GlobalTrader.Site.Controls.TabStrip.registerClass("Rebound.GlobalTrader.Site.Controls.TabStrip",Sys.UI.Control,Sys.IDisposable);