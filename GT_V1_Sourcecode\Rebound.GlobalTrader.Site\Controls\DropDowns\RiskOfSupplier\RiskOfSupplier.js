Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");Rebound.GlobalTrader.Site.Controls.DropDowns.RiskOfSupplier=function(n){Rebound.GlobalTrader.Site.Controls.DropDowns.RiskOfSupplier.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.DropDowns.RiskOfSupplier.prototype={initialize:function(){Rebound.GlobalTrader.Site.Controls.DropDowns.RiskOfSupplier.callBaseMethod(this,"initialize");this.addSetupDataCall(Function.createDelegate(this,this.setupDataCall));this.addGetDataOK(Function.createDelegate(this,this.dataCallOK))},dispose:function(){this.isDisposed||Rebound.GlobalTrader.Site.Controls.DropDowns.RiskOfSupplier.callBaseMethod(this,"dispose")},setupDataCall:function(){this._objData.set_PathToData("controls/DropDowns/RiskOfSupplier");this._objData.set_DataObject("RiskOfSupplier");this._objData.set_DataAction("GetData")},dataCallOK:function(){var t=this._objData._result,n;if(t.Types)for(n=0;n<t.Types.length;n++)this.addOption(t.Types[n].Name,t.Types[n].ID)}};Rebound.GlobalTrader.Site.Controls.DropDowns.RiskOfSupplier.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.RiskOfSupplier",Rebound.GlobalTrader.Site.Controls.DropDowns.Base);