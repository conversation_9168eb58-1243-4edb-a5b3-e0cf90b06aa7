///<reference name="MicrosoftAjax.js" />
///<reference path="~/Common/Data/Data.js" />
///<reference path="~/Common/Functions/Functions.js" />
///<reference path="~/Controls/IconButton/IconButton.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - dispose everything fully
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.CheckBoxFormField = function(element) { 
	Rebound.GlobalTrader.Site.Controls.Forms.CheckBoxFormField.initializeBase(this, [element]);
	this._blnVisible = true;
};

Rebound.GlobalTrader.Site.Controls.Forms.CheckBoxFormField.prototype = {

	get_tdLeft: function() { return this._tdLeft; }, 	set_tdLeft: function(value) { if (this._tdLeft !== value)  this._tdLeft = value; }, 
	get_chk: function() { return this._chk; }, 	set_chk: function(value) { if (this._chk !== value)  this._chk = value; }, 

	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.Forms.CheckBoxFormField.callBaseMethod(this, "initialize");
	},

	dispose: function() { 
		if (this.isDisposed) return;
		if (this.get_element()) $clearHandlers(this.get_element());
		if (this._chk) this._chk.dispose();
		this._tdLeft = null;
		this._chk = null;
		Rebound.GlobalTrader.Site.Controls.Forms.CheckBoxFormField.callBaseMethod(this, "dispose");
		this.isDisposed = true;
	},
	
	setText: function(str) {
		$R_FN.setInnerHTML(this._tdLeft, str);
	},
	
	setValue: function(bln) {
		this._chk.setValue(bln);	
	},
	
	getValue: function() {
		return this._chk.getValue();
	},
	
	show: function(blnShow) {
		this._blnVisible = blnShow;
		$R_FN.showElement(this.get_element(), blnShow);
	},
	
	resetField: function() {
		//do nothing - needed when iterating all form controls
	},
	
	enableCheckBox: function(bln) {
		this.enableButton(bln);
	}
	
};

Rebound.GlobalTrader.Site.Controls.Forms.CheckBoxFormField.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.CheckBoxFormField", Sys.UI.Control, Sys.IDisposable);
