Type.registerNamespace("Rebound.GlobalTrader.Site.Pages.Setup");Rebound.GlobalTrader.Site.Pages.Setup.CD_Tax=function(n){Rebound.GlobalTrader.Site.Pages.Setup.CD_Tax.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Pages.Setup.CD_Tax.prototype={get_ctlRates:function(){return this._ctlRates},set_ctlRates:function(n){this._ctlRates!==n&&(this._ctlRates=n)},get_ctlTax:function(){return this._ctlTax},set_ctlTax:function(n){this._ctlTax!==n&&(this._ctlTax=n)},initialize:function(){Rebound.GlobalTrader.Site.Pages.Setup.CD_Tax.callBaseMethod(this,"initialize")},goInit:function(){this._ctlTax&&this._ctlTax.addSelectTax(Function.createDelegate(this,this.ctlTax_SelectTax));Rebound.GlobalTrader.Site.Pages.Setup.CD_Tax.callBaseMethod(this,"goInit")},dispose:function(){this.isDisposed||(this._ctlRates&&this._ctlRates.dispose(),this._ctlTax&&this._ctlTax.dispose(),this._ctlRates=null,this._ctlTax=null,Rebound.GlobalTrader.Site.Pages.Setup.CD_Tax.callBaseMethod(this,"dispose"))},ctlTax_SelectTax:function(){this._ctlRates._intTaxID=this._ctlTax._intTaxID;this._ctlRates.show(!0);this._ctlTax._tbl.resizeColumns();this._ctlRates.refresh()}};Rebound.GlobalTrader.Site.Pages.Setup.CD_Tax.registerClass("Rebound.GlobalTrader.Site.Pages.Setup.CD_Tax",Rebound.GlobalTrader.Site.Pages.Content,Sys.IDisposable);