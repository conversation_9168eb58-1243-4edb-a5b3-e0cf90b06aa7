﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

/* 
===========================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-210540]		Phuc Hoang			07-Aug-2024		UPDATE			[PROD Bug] DHL Interface - Label Print Issue
[US-223082]		An.TranTan			03-Dec-2024		UPDATE			Update VatNo and EORINumber for Importer in DHL
===========================================================================================
*/
CREATE OR ALTER PROCEDURE [dbo].[usp_upsUpdateShippingAccount_DHL]            
(    
  --Vinay: Add billing currency  
  --Date: 16 March 2015            
  @DHLAccount varchar(100) ,            
  @CompanyName varchar(200),            
  @Attention varchar(200),            
  @Address1  nvarchar(500),            
  @Address2  nvarchar(500),            
  @Address3  nvarchar(500),            
  @Country  varchar(10),            
  @PostalCode nvarchar(100),            
  @City   nvarchar(100),            
  @State  nvarchar(100),            
  @Telephone nvarchar(30),            
  @Fax   nvarchar(200)=NULL,            
  @TaxIDNumber nvarchar(100)=NULL,            
  @TaxIDType nvarchar(50),            
  @Active  bit,    
  @LoadDefault bit ,  
  @BillingCurrency varchar(30) = NULL ,  
   @CountryName varchar(100)=null,  
  @Email nvarchar(128)=NULL,
  @ShipType nvarchar(100) = NULL,
  @VatNo nvarchar(200) = NULL,
  @EORINumber nvarchar(200) = NULL
)            
As            
BEGIN            
BEGIN TRY        
  UPDATE   tbupsShippingAccount SET            
    CompanyName =  @CompanyName,            
    Attention   =  @Attention,             
    Address1    =  @Address1,             
    Address2    =  @Address2,             
    Address3    =  @Address3,             
    Country     =  @Country,            
    PostalCode  =  @PostalCode,            
    City        =  @City,              
    [State]     =  @State,            
    Telephone   =  @Telephone,            
    Fax         =  @Fax,             
    TaxIDNumber =  @TaxIDNumber,            
    TaxIDType   =  @TaxIDType,            
    Active      =  @Active ,    
    LoadDefault =  @LoadDefault ,  
    BillingCurrency = @BillingCurrency,  
	CountryName=@CountryName,  
	Email= @Email,
	ShipType = @ShipType,
	VatNo = @VatNo,
	EORINumber = @EORINumber
  WHERE UPSAccount =@DHLAccount            
END TRY        
BEGIN CATCH        
  RAISERROR('Please enter unique dhl account number.',16,1)        
END CATCH        
END   
  
  
GO


