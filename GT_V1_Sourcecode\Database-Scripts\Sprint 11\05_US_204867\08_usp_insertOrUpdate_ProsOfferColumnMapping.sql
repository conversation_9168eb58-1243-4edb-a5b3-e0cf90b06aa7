﻿ /*   
===========================================================================================  
TASK			UPDATED BY      DATE			ACTION		DESCRIPTION  
[US-204867]		An.TranTan		25-Sep-2024		Create		insert or update column mapping for supplier
===========================================================================================  
*/ 
CREATE OR ALTER PROCEDURE [dbo].[usp_insertOrUpdate_ProsOfferColumnMapping]            
	@supplierNo INT,
	@manufacturer nvarchar(10) = null,
	@part nvarchar(10) = null,
	@quantity nvarchar(10) = null,
	@price nvarchar(10) = null,
	@description nvarchar(10) = null,
	@alterPart nvarchar(10) = null,
	@datecode nvarchar(10) = null,
	@product nvarchar(10) = null,
	@package nvarchar(10) = null,
	@rohs nvarchar(10) = null,
	@supplierPart nvarchar(10) = null,
	@currencyNo int = null,
	@loginNo int
AS              
BEGIN
	IF EXISTS (SELECT 1 FROM BorisGlobalTraderimports.dbo.tbProspectiveOffer_ColumnMapping WHERE SupplierNo = @supplierNo)
	BEGIN
		UPDATE BorisGlobalTraderimports.dbo.tbProspectiveOffer_ColumnMapping
		SET Manufacturer = @manufacturer,
			Part = @part,
			Quantity = @quantity,
			Price = @price,
			[Description] = @description,
			AlterPart = @alterPart,
			DateCode = @datecode,
			Product = @product,
			Package = @package,
			ROHS = @rohs,
			SupplierPart = @supplierPart,
			FixedCurrencyNo  = @currencyNo,
			UpdatedBy = @loginNo,
			UpdatedDate = GETDATE()
		WHERE SupplierNo = @supplierNo
	END
	ELSE
	BEGIN
		INSERT INTO BorisGlobalTraderimports.dbo.tbProspectiveOffer_ColumnMapping
		(
			SupplierNo,
			Manufacturer,
			Part,
			Quantity,
			Price,
			[Description],
			AlterPart,
			DateCode,
			Product,
			Package,
			ROHS,
			SupplierPart,
			FixedCurrencyNo,
			CreatedBy,
			CreatedDate
		)VALUES(
			@supplierNo,
			@manufacturer,
			@part,
			@quantity,
			@price,
			@description,
			@alterPart,
			@datecode,
			@product,
			@package,
			@rohs,
			@supplierPart,
			@currencyNo,
			@loginNo,
			getdate()
		)
	END
END   
  