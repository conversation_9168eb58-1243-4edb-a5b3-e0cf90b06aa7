using System;
using System.Collections.Generic;
using System.Text;
using System.Web;
using System.Web.Caching;
using Rebound.GlobalTrader.Site.Enumerations;
using System.Collections;

namespace Rebound.GlobalTrader.Site {
    public class CacheManager {

        public CacheKeys CacheKeys;

        public static void ClearCache() {
            //clear out cache
            IEnumerator en = HttpContext.Current.Cache.GetEnumerator();
            while (en.MoveNext()) {
                HttpContext.Current.Cache.Remove(((DictionaryEntry)en.Current).Key.ToString());
            }
            en = null;
        }

        public static object GetItem(CacheKey ck) {
            return GetItem(ck.Name);
        }

        public static object GetItem(string strKey) {
            return HttpContext.Current.Cache[strKey];
        }

        public static void StoreItem(CacheKey ck, object objValue) {
            StoreItem(ck.Name, objValue, CacheExpiryType.OneHour, null, CacheItemPriority.Normal);
        }

        public static void StoreItem(CacheKey ck, object objValue, CacheExpiryType enmCacheExpiryType) {
            StoreItem(ck.Name, objValue, enmCacheExpiryType, null, CacheItemPriority.Normal);
        }

        public static void StoreItem(CacheKey ck, object objValue, CacheExpiryType enmCacheExpiryType, string[] strDependentOnFiles) {
            StoreItem(ck.Name, objValue, enmCacheExpiryType, strDependentOnFiles, CacheItemPriority.Normal);
        }

        public static void StoreItem(string strKey, object objValue, CacheExpiryType enmCacheExpiryType, string[] strDependentOnFiles, CacheItemPriority enmCacheItemPriority) {
            HttpContext.Current.Cache.Add(
                strKey
                , objValue
                , GetCacheDependency(enmCacheExpiryType, strDependentOnFiles)
                , Cache.NoAbsoluteExpiration
                , GetSlidingExpiration(enmCacheExpiryType)
                , enmCacheItemPriority
                , null
            );
        }

        public static void ClearItem(CacheKey ck) {
            ClearItem(ck.Name);
        }

        public static void ClearItem(string strKey) {
            HttpContext.Current.Cache.Remove(strKey);
        }

        private static CacheDependency GetCacheDependency(CacheExpiryType enmCacheExpiryType, string[] strDependentOnFiles) {
            CacheDependency cdCacheDependency = null;
            if (enmCacheExpiryType == CacheExpiryType.DependentOnConfigFile) cdCacheDependency = new CacheDependency(strDependentOnFiles);
            return cdCacheDependency;
        }

        private static TimeSpan GetSlidingExpiration(CacheExpiryType enmCacheExpiryType) {
            TimeSpan tsSlidingExpiration = Cache.NoSlidingExpiration;
            switch (enmCacheExpiryType) {
                case CacheExpiryType.Never: tsSlidingExpiration = Cache.NoSlidingExpiration; break;
                case CacheExpiryType.OneDay: tsSlidingExpiration = new TimeSpan(24, 0, 0); break;
                case CacheExpiryType.OneWorkingDay: tsSlidingExpiration = new TimeSpan(7, 0, 0); break;
                case CacheExpiryType.ThreeHours: tsSlidingExpiration = new TimeSpan(3, 0, 0); break;
                case CacheExpiryType.OneHour: tsSlidingExpiration = new TimeSpan(1, 0, 0); break;
                case CacheExpiryType.TenMinutes: tsSlidingExpiration = new TimeSpan(0, 10, 0); break;
                case CacheExpiryType.OneMinute: tsSlidingExpiration = new TimeSpan(0, 1, 0); break;
                case CacheExpiryType.DependentOnConfigFile: tsSlidingExpiration = Cache.NoSlidingExpiration; break;
            }
            return tsSlidingExpiration;
        }

        public static string SerializeOptions(object[] aryParameters) {
            string strOut = "";
            for (int i = 0; i < aryParameters.Length; i++) {
                if (strOut.Length > 0) strOut += "||";
                string strValue = aryParameters[i].ToString();
                if (aryParameters[i] is bool) strValue = Convert.ToInt32(aryParameters[i]).ToString();
                if (aryParameters[i] is DateTime) strValue = ((DateTime)aryParameters[i]).Ticks.ToString();
                if (aryParameters[i] is string) strValue = Functions.RemovePunctuation(aryParameters[i].ToString());
                strOut += strValue;
                strValue = null;
            }
            return strOut;
        }

        #region DropDowns

        public static void StoreDropDown(int intDropDownID, string strOptions, string strResult, CacheExpiryType enmCacheExpiryType, string[] strDependentOnFiles) {
            StoreItem(
                GetDropDownCacheKey(intDropDownID, strOptions)
                , strResult
                , enmCacheExpiryType
                , strDependentOnFiles
                , CacheItemPriority.Normal
            );
        }

        public static void StoreDropDown(int intDropDownID, string strOptions, string strResult, CacheExpiryType enmCacheExpiryType) {
            StoreDropDown(intDropDownID, strOptions, strResult, enmCacheExpiryType, null);
        }

        public static void StoreDropDown(int intDropDownID, string strResult, CacheExpiryType enmCacheExpiryType) {
            StoreDropDown(intDropDownID, "", strResult, enmCacheExpiryType, null);
        }

        public static string GetDropDownData(int intDropDownID, string strOptions) {
            object obj = GetItem(GetDropDownCacheKey(intDropDownID, strOptions));
            return (obj == null) ? "" : obj.ToString();
        }
        public static string GetDropDownData(int intDropDownID) {
            return GetDropDownData(intDropDownID, "");
        }

        public static void ClearStoredDropDown(int intDropDownID) {
            string strDropDownBaseID = GetDropDownCacheKey(intDropDownID, "");
            IDictionaryEnumerator en = HttpContext.Current.Cache.GetEnumerator();
            while (en.MoveNext()) {
                if (en.Key.ToString().StartsWith(strDropDownBaseID)) ClearItem(en.Key.ToString());
            }
            en = null;
        }
        public static void ClearStoredDropDown(string strDropDownName) {
            ClearStoredDropDown(Rebound.GlobalTrader.Site.Site.GetInstance().GetDropDown(strDropDownName).ID);
        }
        public static void ClearStoredDropDown(int intDropDownID, string strOptions) {
            ClearItem(GetDropDownCacheKey(intDropDownID, strOptions));
        }
        public static void ClearStoredDropDown(int intDropDownID, object[] objOptions) {
            ClearStoredDropDown(intDropDownID, SerializeOptions(objOptions));
        }
        public static void ClearStoredDropDown(string strDropDownName, string strOptions) {
            ClearStoredDropDown(Rebound.GlobalTrader.Site.Site.GetInstance().GetDropDown(strDropDownName).ID, strOptions);
        }
        public static void ClearStoredDropDown(string strDropDownName, object[] objOptions) {
            ClearStoredDropDown(strDropDownName, SerializeOptions(objOptions));
        }

        private static string GetDropDownCacheKey(int intDropDownID, string strOptions) {
            return string.Format("ddl{0}_{1}", intDropDownID, strOptions);
        }

        #endregion

        public enum CacheExpiryType {
            Never,
            OneDay,
            OneWorkingDay,
            ThreeHours,
            OneHour,
            TenMinutes,
            OneMinute,
            DependentOnConfigFile
        }

    }

    public class CacheKeys {
        public static readonly CacheKey FooterText = new CacheKey(0, "ftt");
        public static readonly CacheKey BackgroundImages = new CacheKey(1, "bgs");
        public static readonly CacheKey SiteObject = new CacheKey(2, "sto");
    }

    public class CacheKey : ExtendableEnum {
        public CacheKey(int intValue, string strName) : base(intValue, strName) { }
    }
}
