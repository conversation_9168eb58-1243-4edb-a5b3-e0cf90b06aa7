Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.FilterDataItemRows");Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.TextBox=function(n){Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.TextBox.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.TextBox.prototype={get_txt:function(){return this._txt},set_txt:function(n){this._txt!==n&&(this._txt=n)},get_hypSearchType:function(){return this._hypSearchType},set_hypSearchType:function(n){this._hypSearchType!==n&&(this._hypSearchType=n)},get_enmSearchType:function(){return this._enmSearchType},set_enmSearchType:function(n){this._enmSearchType!==n&&(this._enmSearchType=n)},get_strSearchType_StartsWith:function(){return this._strSearchType_StartsWith},set_strSearchType_StartsWith:function(n){this._strSearchType_StartsWith!==n&&(this._strSearchType_StartsWith=n)},get_strSearchType_EndsWith:function(){return this._strSearchType_EndsWith},set_strSearchType_EndsWith:function(n){this._strSearchType_EndsWith!==n&&(this._strSearchType_EndsWith=n)},get_strSearchType_Contains:function(){return this._strSearchType_Contains},set_strSearchType_Contains:function(n){this._strSearchType_Contains!==n&&(this._strSearchType_Contains=n)},addEnterPressed:function(n){$R_TXTBOX.addEnterPressedEvent(this._txt,n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.TextBox.callBaseMethod(this,"initialize");$addHandler(this._txt,"focus",Function.createDelegate(this,this.textBoxFocus));$addHandler(this._txt,"blur",Function.createDelegate(this,this.textBoxBlur));$addHandler(this._txt,"keyup",Function.createDelegate(this,this.textBoxKeyup));$addHandler(this._hypSearchType,"click",Function.createDelegate(this,this.changeSearchType))},dispose:function(){this.isDisposed||(this.get_element()&&$clearHandlers(this.get_element()),this._txt&&$clearHandlers(this._txt),this._hypSearchType&&$clearHandlers(this._hypSearchType),this._txt=null,this._hypSearchType=null,this._enmSearchType=null,this._strSearchType_StartsWith=null,this._strSearchType_EndsWith=null,this._strSearchType_Contains=null,Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.TextBox.callBaseMethod(this,"dispose"))},textBoxFocus:function(){this.enableField(!0)},textBoxBlur:function(){this.enableField($R_FN.isEntered(this._txt.value))},textBoxKeyup:function(){this.enableField($R_FN.isEntered(this._txt.value))},getValue:function(){var n=encodeURIComponent(this._txt.value.trim());switch(this._enmSearchType){case $R_ENUM$TextFilterSearchType.StartsWith:n=n+"%";break;case $R_ENUM$TextFilterSearchType.Contains:n="%"+n+"%";break;case $R_ENUM$TextFilterSearchType.EndsWith:n="%"+n}return encodeURIComponent(n)},setValue:function(n){(typeof n=="undefined"||n==null)&&(n="");n=n.toString().trim();this._txt.value=n;this.enableField(n.length>0)},reset:function(){this._txt.value="";this.enableField(!1)},changeSearchType:function(){this.setSearchType(this._enmSearchType+1)},setSearchType:function(n){this._enmSearchType=n;this._enmSearchType>2&&(this._enmSearchType=0);this.showSearchType()},showSearchType:function(){switch(this._enmSearchType){case $R_ENUM$TextFilterSearchType.StartsWith:this._hypSearchType.className="searchType_StartsWith";this._hypSearchType.setAttribute("title",this._strSearchType_StartsWith);this._hypSearchType.setAttribute("alt",this._strSearchType_StartsWith);break;case $R_ENUM$TextFilterSearchType.Contains:this._hypSearchType.className="searchType_Contains";this._hypSearchType.setAttribute("title",this._strSearchType_Contains);this._hypSearchType.setAttribute("alt",this._strSearchType_Contains);break;case $R_ENUM$TextFilterSearchType.EndsWith:this._hypSearchType.className="searchType_EndsWith";this._hypSearchType.setAttribute("title",this._strSearchType_EndsWith);this._hypSearchType.setAttribute("alt",this._strSearchType_EndsWith)}}};Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.TextBox.registerClass("Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.TextBox",Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.Base,Sys.IDisposable);