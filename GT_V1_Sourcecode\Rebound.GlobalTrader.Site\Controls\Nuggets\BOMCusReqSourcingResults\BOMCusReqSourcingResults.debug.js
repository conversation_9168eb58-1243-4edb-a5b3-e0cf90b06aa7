///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//
// RP 05.01.2010:
// - add links back to related Quotes
//Marker     changed by      date         Remarks
//[001]      Aashu          08/06/2018     Added supplier warranty field
//[002]      Aashu Singh    02/07/2018     validation for msl.
//[003]      Aashu Singh    16/08/2018     REB-12322 : A tick box to recomond test the parts from HUB side.
//[004]      Aashu Singh    18-Sep-2018    REB-13101:Show the quote number on the sourcing results on the HUB side.
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Nuggets");

Rebound.GlobalTrader.Site.Controls.Nuggets.BOMCustomerRequirementSourcingResults = function(element) {
    Rebound.GlobalTrader.Site.Controls.Nuggets.BOMCustomerRequirementSourcingResults.initializeBase(this, [element]);
    this._intCustomerRequirementID = -1;
    this._intCompanyID = -1;
    this._blnRequirementClosed = false;
    this._hasSourcingResult = false;
    this._hasReleasedItem = false;
    this._strUpLiftPrice = "";
    this._blnSourcingRelease=false;
    this._blnAllHasDelDate = false;
    this._blnAllHasProduct = false;
    this._HasQuote=false;
    this._PackageT="";
    this._ProductT="";
    this._ClientNo=-1;
    this._SupplierManufacturerName="";
    this._supDateCode = "";
    this._IsSoCreated = false;
    this._IsClosed = false;
    this._SourceRef = false;
    this._intActBuyCurrencyNo = -1;
    this._IsSourcingReleasedCount = false;

    this._intRequirementLineID = -1;
    this._intBOMID = -1;
    this._BomCode = -1;
    this._BomName = null;
    this._BomCompanyName = null;
    this._BomCompanyNo = null;
    this._SalesManNo = null;
    this._SalesManName = null;
    this._CustReqNo = null;
    this._blnCanRelease = false;
    this._Quantity = -1;
    this._PackageID = -1;
    this._ProductID = -1;
    this._ManufacturerNo = -1;
    this._Manufacturer = null;
    this._DateCode = null;
    this._ROHS = -1;
    this._Product = null;
    this._IHSCountryOfOriginName = null;
    //[001] start
    this._SupplierWarranty = null;
    
    //[001] end
    //[002] start
    this._msl = true;
    //[002] end
    //[003] start
    this._isTestingRecommended = false;
    //[003] end
    this._SourcingResultNo = -1;
    this._selectedRow = -1;
    this._blnImageAvail = false;
    this._ReqSalesman = -1;
    this._SupportTeamMemberNo = null;
    this._UnitBuyPrice= null;
    this._UnitSellPrice=null;
    this._UnitBuyPriceWithCurrany= null;
    this._UnitSellPriceWithCurrany = null;
    this._blnISAS6081Required = false;
    this._IsAssignToMe = false;
    this._mfrNotes = null;
};

Rebound.GlobalTrader.Site.Controls.Nuggets.BOMCustomerRequirementSourcingResults.prototype = {

    get_intCustomerRequirementID: function() { return this._intCustomerRequirementID; }, set_intCustomerRequirementID: function(v) { if (this._intCustomerRequirementID !== v) this._intCustomerRequirementID = v; },
    get_tbl: function() { return this._tbl; }, set_tbl: function(value) { if (this._tbl !== value) this._tbl = value; },
    get_ibtnAdd: function() { return this._ibtnAdd; }, set_ibtnAdd: function(v) { if (this._ibtnAdd !== v) this._ibtnAdd = v; },
    get_ibtnEdit: function() { return this._ibtnEdit; }, set_ibtnEdit: function(v) { if (this._ibtnEdit !== v) this._ibtnEdit = v; },
    get_ibtnQuote: function() { return this._ibtnQuote; }, set_ibtnQuote: function(v) { if (this._ibtnQuote !== v) this._ibtnQuote = v; },
    get_ctlMultiSelectionCount: function() { return this._ctlMultiSelectionCount; }, set_ctlMultiSelectionCount: function(value) { if (this._ctlMultiSelectionCount !== value) this._ctlMultiSelectionCount = value; },
    get_blnPOHub: function() { return this._blnPOHub; }, set_blnPOHub: function(v) { if (this._blnPOHub !== v) this._blnPOHub = v; },

    get_pnlLineDetail: function() { return this._pnlLineDetail; }, set_pnlLineDetail: function(v) { if (this._pnlLineDetail !== v) this._pnlLineDetail = v; },
    get_pnlLoadingLineDetail: function() { return this._pnlLoadingLineDetail; }, set_pnlLoadingLineDetail: function(v) { if (this._pnlLoadingLineDetail !== v) this._pnlLoadingLineDetail = v; },
    get_pnlLineDetailError: function() { return this._pnlLineDetailError; }, set_pnlLineDetailError: function(v) { if (this._pnlLineDetailError !== v) this._pnlLineDetailError = v; },
    get_ibtnDelete: function () { return this._ibtnDelete; }, set_ibtnDelete: function (v) { if (this._ibtnDelete !== v) this._ibtnDelete = v; },
    get_ibtnDeletePartwatch: function () { return this._ibtnDeletePartwatch; }, set_ibtnDeletePartwatch: function (v) { if (this._ibtnDeletePartwatch !== v) this._ibtnDeletePartwatch = v; },
    get_ibtnConfirm: function () { return this._ibtnConfirm; }, set_ibtnConfirm: function (v) { if (this._ibtnConfirm !== v) this._ibtnConfirm = v; },
    get_ibtnApproval: function () { return this._ibtnApproval; }, set_ibtnApproval: function (v) { if (this._ibtnApproval !== v) this._ibtnApproval = v; },

    addAddFormShown: function(handler) { this.get_events().addHandler("AddFormShown", handler); },
    removeAddFormShown: function(handler) { this.get_events().removeHandler("AddFormShown", handler); },
    onAddFormShown: function() {
        var handler = this.get_events().getHandler("AddFormShown");
        if (handler) handler(this, Sys.EventArgs.Empty);
    },

    addAddCustFormShown: function (handler) { this.get_events().addHandler("AddCustFormShown", handler); },
    removeAddCustFormShown: function (handler) { this.get_events().removeHandler("AddCustFormShown", handler); },
    onAddCustFormShown: function () {
        var handler = this.get_events().getHandler("AddCustFormShown");
        if (handler) handler(this, Sys.EventArgs.Empty);
    },

    addSourcingResultAdded: function(handler) { this.get_events().addHandler("SourcingResultAdded", handler); },
    removeSourcingResultAdded: function(handler) { this.get_events().removeHandler("SourcingResultAdded", handler); },
    onSourcingResultAdded: function() {
        var handler = this.get_events().getHandler("SourcingResultAdded");
        if (handler) handler(this, Sys.EventArgs.Empty);
    },
     addGotDataOK: function(handler) { this.get_events().addHandler("GotDataOK", handler); },
    removeGotDataOK: function(handler) { this.get_events().removeHandler("GotDataOK", handler); },
    onGotDataOK: function() {
        var handler = this.get_events().getHandler("GotDataOK");
        if (handler) handler(this, Sys.EventArgs.Empty);
    },

    addCallBeforeRelease: function (handler) { this.get_events().addHandler("CallBeforeRelease", handler); },
    removeCallBeforeRelease: function (handler) { this.get_events().removeHandler("CallBeforeRelease", handler); },
    onCallBeforeRelease: function () {
        var handler = this.get_events().getHandler("CallBeforeRelease");
        if (handler) handler(this, Sys.EventArgs.Empty);
    },

    addSourcingResultDeleted: function(handler) { this.get_events().addHandler("SourcingResultDeleted", handler); },
    removeSourcingResultDeleted: function(handler) { this.get_events().removeHandler("SourcingResultDeleted", handler); },
    onSourcingResultDeleted: function() {
        var handler = this.get_events().getHandler("SourcingResultDeleted");
        if (handler) handler(this, Sys.EventArgs.Empty);
    },

    addSourcingResultSelect: function (handler) { this.get_events().addHandler("SourcingResultSelect", handler); },
    removeSourcingResultSelect: function (handler) { this.get_events().removeHandler("SourcingResultSelect", handler); },
    onSourcingResultSelect: function () {
        var handler = this.get_events().getHandler("SourcingResultSelect");
        if (handler) handler(this, Sys.EventArgs.Empty);
    },

    initialize: function() {
        Rebound.GlobalTrader.Site.Controls.Nuggets.BOMCustomerRequirementSourcingResults.callBaseMethod(this, "initialize");

        //$("#ctl00_cphMain_ctlMainInfo_ctlDB_BuyAndPriceMerginesourcing").hide();
        this.addRefreshEvent(Function.createDelegate(this, this.getData));
        this._tbl.addMultipleSelectionChanged(Function.createDelegate(this, this.selectResult));
        this._ctlMultiSelectionCount.registerTable(this._tbl);

        if (this._ibtnQuote) $R_IBTN.addClick(this._ibtnQuote, Function.createDelegate(this, this.doQuote));

        //Add form
        if (this._ibtnAdd) {
            if (this._blnPOHub) {
                $R_IBTN.addClick(this._ibtnAdd, Function.createDelegate(this, this.showAddForm));
            } else {

                $R_IBTN.addClick(this._ibtnAdd, Function.createDelegate(this, this.showAddCustForm));
            }
            this._frmAdd = (this._blnPOHub) ? $find(this._aryFormIDs[0]) : $find(this._aryFormIDs[4]);
            this._frmAdd.addCancel(Function.createDelegate(this, this.cancelAdd));
            this._frmAdd.addSaveComplete(Function.createDelegate(this, this.saveAddComplete));
        }

        //Edit form
        if (this._ibtnEdit) {
            $R_IBTN.addClick(this._ibtnEdit, Function.createDelegate(this, this.showEditForm));
            this._frmEdit = (this._blnPOHub) ? $find(this._aryFormIDs[1]) : $find(this._aryFormIDs[5]);
            this._frmEdit.addCancel(Function.createDelegate(this, this.cancelEdit));
            this._frmEdit.addSaveComplete(Function.createDelegate(this, this.saveEditComplete));
        }
        
         //Edit form
        if (this._ibtnDelete) {
            $R_IBTN.addClick(this._ibtnDelete, Function.createDelegate(this, this.showDeleteForm));
            this._frmDelete = $find(this._aryFormIDs[2]);
            this._frmDelete.addCancel(Function.createDelegate(this, this.cancelDelete));
            this._frmDelete.addSaveComplete(Function.createDelegate(this, this.saveDeleteComplete));
            this._frmDelete.addNotConfirmed(Function.createDelegate(this, this.cancelDelete));
        }
        //Delete Partwatch
        if (this._ibtnDeletePartwatch) {
            $R_IBTN.addClick(this._ibtnDeletePartwatch, Function.createDelegate(this, this.showDeletePartWatchForm));
            this._frmDeletePartWatch = $find(this._aryFormIDs[7]);
            this._frmDeletePartWatch.addCancel(Function.createDelegate(this, this.cancelDeletePartWatch));
            this._frmDeletePartWatch.addSaveComplete(Function.createDelegate(this, this.saveDeletePartWatchComplete));
            this._frmDeletePartWatch.addNotConfirmed(Function.createDelegate(this, this.cancelDeletePartWatch));
        }

        if (this._ibtnConfirm) {
            $R_IBTN.addClick(this._ibtnConfirm, Function.createDelegate(this, this.showConfirmForm));
            this._ctlConfirm = $find(this._aryFormIDs[3]);
            this._ctlConfirm.addCancel(Function.createDelegate(this, this.cancelDelete));   
            this._ctlConfirm.addSaveComplete(Function.createDelegate(this, this.saveDeleteComplete));
            this._ctlConfirm.addNotConfirmed(Function.createDelegate(this, this.cancelDelete));
        }

        if (this._ibtnApproval) {
            $R_IBTN.addClick(this._ibtnApproval, Function.createDelegate(this, this.showApprovalForm));
            this._ctlApproval = $find(this._aryFormIDs[6]);
            this._ctlApproval.addCancel(Function.createDelegate(this, this.cancelApproval));
            this._ctlApproval.addSaveComplete(Function.createDelegate(this, this.saveApprovalComplete));
            this._ctlApproval.addNotConfirmed(Function.createDelegate(this, this.cancelApproval));
        }

        //add quote task
        this._frmAddTask = $find(this._aryFormIDs[8]);
        this._frmAddTask.addCancel(Function.createDelegate(this, this.hideAddTaskForm));
        this._frmAddTask.addSaveComplete(Function.createDelegate(this, this.addTaskComplete));

        this.getData();
    },

    dispose: function() {
        if (this.isDisposed) return;
        if (this._ibtnAdd) $R_IBTN.clearHandlers(this._ibtnAdd);
        if (this._ibtnEdit) $R_IBTN.clearHandlers(this._ibtnEdit);
        if (this._ibtnQuote) $R_IBTN.clearHandlers(this._ibtnQuote);
        if (this._tbl) this._tbl.dispose();
        if (this._ctlMultiSelectionCount) this._ctlMultiSelectionCount.dispose();
        this._intCustomerRequirementID = null;
        this._intCompanyID = null;
        this._tbl = null;
        this._ibtnAdd = null;
        this._ibtnEdit = null;
        this._ibtnQuote = null;
        this._ctlMultiSelectionCount = null;
        this._blnRequirementClosed = null;
        this._blnPOHub = null;
        this._pnlLineDetail = null;
        this._pnlLoadingLineDetail = null;
        this._pnlLineDetailError = null;
        this._ibtnDelete = null;
        this._ibtnDeletePartwatch = null;
        this._blnSourcingRelease = null;
        this._blnAllHasDelDate = null;
        this._blnAllHasProduct = null;
        this._intActBuyCurrencyNo = null;
        this._ibtnConfirm = null;
        this._SourcingResultNo = null;
        this._selectedRow = null;
        this._ReqSalesman = null;
        this._SupportTeamMemberNo = null;
        this._ibtnApproval = null;
        this._UnitBuyPrice= null;
        this._UnitSellPrice=null;
        this._UnitBuyPriceWithCurrany= null;
        this._UnitSellPriceWithCurrany = null;
        this._mfrNotes = null;
        Rebound.GlobalTrader.Site.Controls.Nuggets.BOMCustomerRequirementSourcingResults.callBaseMethod(this, "dispose");
    },

    getData: function () {
        this._ctlMultiSelectionCount.clearAll();
        this.getData_Start();
        this.enableButtons(false);
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/BOMCusReqSourcingResults");
        obj.set_DataObject("BOMCusReqSourcingResults");
        obj.set_DataAction("GetData");
        obj.addParameter("id", this._intCustomerRequirementID);
        obj.addDataOK(Function.createDelegate(this, this.getDataOK));
        obj.addError(Function.createDelegate(this, this.getDataError));
        obj.addTimeout(Function.createDelegate(this, this.getDataError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    getDataOK: function (args) {
        this._hasSourcingResult = false;
        var res = args._result;
        $R_FN.showElement(this._pnlLineDetail, false);
        $R_FN.showElement(this._pnlLoadingLineDetail, false);
        this._tbl.clearTable();
        for (var i = 0, l = res.Results.length; i < l; i++) {
            var row = res.Results[i];

            var aryData;
            var strQuotes = "";
            var addTaskHtml = "";
            var viewTaskHtml = "";
            if (row.Quotes.length > 0) {
                var quote = row.Quotes[0];
                addTaskHtml = String.format("<a href=\"javascript:void(0);\" style=\"color: #1b55e2;\" title='Add task' onclick=\"$find('{0}').showAddTaskForm({1},'{2}','{3}','{4}');\">" + "Add Task" + "</a>",
                    this._element.id, quote.ID, quote.No, quote.QuoteStatus, quote.CustomerName,
                ) + "&nbsp;&nbsp;&nbsp;";
                viewTaskHtml = String.format("<a href=\"javascript:void(0);\" title='View task' onclick=\"$find('{0}').redirectToTaskDetails({1});\" style=\"color: {2};\">" + (quote.TaskCount + " Task") + "</a>",
                    this._element.id, quote.No, quote.HasUnFinishedTask ? 'red' : '#1b55e2');
            }

            if (this._blnPOHub == true) {
                //[004] start
                for (var q = 0, lq = row.Quotes.length; q < lq; q++) {
                        strQuotes += row.Quotes[q].No;
                }
                //[004] end
                aryData = [
                    $R_FN.writeDoubleCellValue($RGT_nubButton_CompanySourcing(row.SupplierNo, row.Supplier, null, null, false, true, row.MslSpqFactorySealed, row.SupplierAdvisoryNotes) + " (" + row.SupplierPercentage + ")" + "<br />" + $R_FN.setCleanTextValue(row.SupplierType), strQuotes)
				    , $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.Part), $R_FN.setCleanTextValue(row.Notes))
                    , $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.ManufacturerName) + $R_FN.createAdvisoryNotesIcon(row.MfrAdvisoryNotes, 'margin-left-10'), $R_FN.setCleanTextValue(row.SupplierDateCode))
				    , $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.Product), $R_FN.setCleanTextValue(row.Package))
				    , $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.Date), $R_FN.setCleanTextValue(row.By))
				    , $R_FN.writeDoubleCellValue(row.Qty,row.DeliveryDate)
				    , $R_FN.writeDoubleCellValue(row.BuyPrice,row.ActualPrice)
				    , $R_FN.writeDoubleCellValue(row.Price, row.PriceInBase)
				    //, $R_FN.setCleanTextValue(row.PFPQ)
				    , $R_FN.writeDoubleCellValue(row.PFPQ, $R_FN.setCleanTextValue(row.RegionName))
                    , $R_FN.writeDoubleCellValue(row.EstimatedShippingCost, row.EstimatedShippingCostInBase)
                    , $R_FN.writeDoubleCellValue(row.PartWatchMatchHUBIPO == true ? "<input type='checkbox' checked onclick='return false'/>" : "<input type='checkbox' onclick='return false'/>", row.SourceClient)
                    , addTaskHtml + viewTaskHtml
                ];
            }
            else {
                //[004] start
                for (var q = 0, lq = row.Quotes.length; q < lq; q++) {
                    strQuotes += $RGT_nubButton_Quote(row.Quotes[q].ID, row.Quotes[q].No);
                }
                //[004] end
                aryData = [
                    $R_FN.writeDoubleCellValue($RGT_nubButton_CompanySourcing(row.SupplierNo, row.Supplier, null, null, null, true, row.MslSpqFactorySealed, row.SupplierAdvisoryNotes) + "<br />" + $R_FN.setCleanTextValue(row.SupplierType), strQuotes)
				    , $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.Part),"<span style='color:red!important;'>"+$R_FN.setCleanTextValue(row.Notes)+"</span>")
                    , $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.ManufacturerName) + $R_FN.createAdvisoryNotesIcon(row.MfrAdvisoryNotes, 'margin-left-10'), $R_FN.setCleanTextValue(row.DC))
				    , $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.Product), $R_FN.setCleanTextValue(row.Package))
				    , $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.Date), $R_FN.setCleanTextValue(row.By))
				    , $R_FN.writeDoubleCellValue(row.Qty, row.DeliveryDate)
				    , $R_FN.writeDoubleCellValue(row.Price, row.PriceInBase)
				    , $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.RegionName), $R_FN.setCleanTextValue(row.TermsName))
                    , $R_FN.writeDoubleCellValue(row.EstimatedShippingCost, row.EstimatedShippingCostInBase)
               // , $R_FN.setCleanTextValue(row.PartWatchMatchHUBIPO == true ? "<input type='checkbox' checked onclick='return false'/>" : "<input type='checkbox' onclick='return false'/>")
                    , addTaskHtml + viewTaskHtml
                ];
            }
            var objExtra = {
                SupplierManufacturerName: row.SupplierManufacturerName,
                SupplierDateCode: row.SupplierDateCode,
                SupplierPackageType: row.SupplierPackageType,
                SupplierProductType: row.SupplierProductType,
                SupplierMOQ: row.SupplierMOQ,
                SupplierTotalQSA: row.SupplierTotalQSA,
                PriorityId: row.PriorityId,
                IHSCountryOfOriginName: row.IHSCountryOfOriginName,
                CountryOfOriginName:row.CountryOfOriginName,
                SupplierLTB: row.SupplierLTB,
                SupplierNotes:$R_FN.setCleanTextValue(row.Notes),
                IsFromPQ: row.PFPQ,
                IsRelease: row.SourcRelease,
                SPQ: row.SPQ,
                LeadTime: row.LeadTime,
                ROHSStatus: row.ROHSStatus,
                FactorySealed: row.FactorySealed,
                MSL: row.MSL,
                HasQuote:row.Quotes.length>0?false:true,
                ClientNo: row.ClientNo,
                IsClosed: row.IsClosed,
                IsSoCreated: row.IsSoCreated,
                SourceRef: row.SourceRef,
                ActBuyCurrencyNo: row.ActBuyCurrencyNo,
                MSLLevelNo: row.MSLLevelNo,
                MSLLevelText: row.MSLLevelText,
                SupplierWarranty: row.SupplierWarranty,
                IsTestingRecommended: row.IsTestingRecommended,
                RowId: row.ID,
                IsImageAvail: row.IsImageAvail,
                UnitBuyPrice: row.UnitBuyPrice,
                UnitSellPrice: row.UnitSellPrice,
                UnitBuyPriceWithCurrany: row.BuyPrice,
                UnitSellPriceWithCurrany: row.Price,
                ROHSDescription: row.ROHSDescription,
                IsPartWatchMatch: row.PartWatchMatchHUBIPO,
                IsPartWatchMatchClient: row.IsPartWatchMatchClient,
                bln_ISAS6081Required: row.ISAS6081Required,
                strTypeOfSupplierName: row.TypeOfSupplier,
                strReasonForSupplierName: row.ReasonForSupplier,
                strRiskOfSupplierName: row.RiskOfSupplier,
                mfrAdvisoryNotes: row.MfrAdvisoryNotes
            };
            this._blnISAS6081Required = row.ISAS6081Required;
            this._IsAssignToMe = row.IsAssignToMe;

            if (row.IsSourcingReleasedCount) this._IsSourcingReleasedCount = row.IsSourcingReleasedCount;

            this._tbl.addRow(aryData, row.ID, false, objExtra, null);
            row = null; aryData = null; objExtra = null;
           
            strQuotes = null;
        }
        this._hasSourcingResult = (res.Results != null && res.Results.length > 0) ? true : false;
        this._blnAllHasDelDate = res.AllHasDelDate;
        this._blnAllHasProduct = res.AllHasProduct;
        this._tbl.resizeColumns();
        this.getDataOK_End();
        this.showNoData(res.Results.length == 0);
        this.onGotDataOK();
    },

    getDataError: function(args) {
        this.showError(true, args.get_ErrorMessage());
    },

    selectResult: function(currentObj) {

        $R_FN.showElement(this._pnlLineDetail, false);
        $R_FN.showElement(this._pnlLoadingLineDetail, false);
        this._selectedRow = this._tbl._aryCurrentValues.length;
        if (this._selectedRow == 1) {
            //$("#ctl00_cphMain_ctlMainInfo_ctlDB_BuyAndPriceMerginesourcing").hide();
            var objExtraData = this._tbl._aryExtraData[currentObj._arySelectedIndexes];
            //if (objExtraData.IsFromPQ == "YES") {
            var manufacturerName = objExtraData.SupplierManufacturerName;
            var dateCode = objExtraData.SupplierDateCode;
            var packageType = objExtraData.SupplierPackageType;
            var productType = objExtraData.SupplierProductType;
            var mOQ = objExtraData.SupplierMOQ;
            var totalQSA = objExtraData.SupplierTotalQSA;
            var priority = objExtraData.PriorityId;
            var ihsCountryOfOriginName = objExtraData.IHSCountryOfOriginName;
            var CountryOfOriginName = objExtraData.CountryOfOriginName;
            var lTB = objExtraData.SupplierLTB;
            var notes = objExtraData.SupplierNotes;

            var SPQ = objExtraData.SPQ;
            var leadTime = objExtraData.LeadTime;
            var rohsStatus = objExtraData.ROHSStatus;
            var factorySealed = objExtraData.FactorySealed;
            var MSL = objExtraData.MSL;
            //[001] start
            var supplierWarranty = objExtraData.SupplierWarranty;
            //[001] end
            //[003] start
            var testingRecommended = objExtraData.IsTestingRecommended;
            this._blnImageAvail = objExtraData.IsImageAvail;
            //[003] end
            var ROHSDescription = objExtraData.ROHSDescription;
            this._blnISAS6081Required = objExtraData.bln_ISAS6081Required;

            this.fillAndShowOnLineSelect(manufacturerName, dateCode, packageType, productType, mOQ, totalQSA, lTB, notes, SPQ, leadTime, rohsStatus, factorySealed, MSL, objExtraData.MSLLevelNo, objExtraData.MSLLevelText, supplierWarranty, testingRecommended, this._blnImageAvail, priority, ihsCountryOfOriginName, CountryOfOriginName, ROHSDescription, objExtraData.strTypeOfSupplierName, objExtraData.strReasonForSupplierName, objExtraData.strRiskOfSupplierName, objExtraData.mfrAdvisoryNotes);
            // }
            this._HasQuote = objExtraData.HasQuote;
            this._PackageT = objExtraData.SupplierPackageType;
            this._ProductT = objExtraData.SupplierProductType;
            this._blnSourcingRelease = objExtraData.IsRelease;
            this._ClientNo = objExtraData.ClientNo;
            this._SupplierManufacturerName = objExtraData.SupplierManufacturerName;
            this._supDateCode = objExtraData.SupplierDateCode;
            this._IsSoCreated = objExtraData.IsSoCreated;
            this._IsClosed = objExtraData.IsClosed;
            this._SourceRef = objExtraData.SourceRef;
            this._intActBuyCurrencyNo = objExtraData.ActBuyCurrencyNo;
            this._SourcingResultNo = objExtraData.RowId;
            this._UnitBuyPrice = objExtraData.UnitBuyPrice;
            this._UnitSellPrice = objExtraData.UnitSellPrice;
            this._UnitBuyPriceWithCurrany = objExtraData.UnitBuyPriceWithCurrany;
            this._UnitSellPriceWithCurrany = objExtraData.UnitSellPriceWithCurrany;
            this._mfrNotes = objExtraData.mfrAdvisoryNotes;
        }
        
        this.onSourcingResultSelect();
       // alert(this._SourceRef);
      //  alert(this._IsClosed);
        this._IsAssignToMe = GetAssignToMe();
        this._blnISAS6081Required = GetAS6081Required();
         this.enableButtons(true);
    },
    fillAndShowOnLineSelect: function (manufacturerName, dateCode, packageType, productType, mOQ, totalQSA, lTB, notes, SPQ, leadTime, rohsStatus, factorySealed, MSL, MSLLevelNo, MSLLevelText, supplierWarranty, isTestingRecommended, isImageAvail, priority, ihsCountryOfOriginName, CountryOfOriginName, ROHSDescription, TypeOfSupplierName, ReasonForSupplierName, RiskOfSupplierName, mfrAdvisoryNotes) {
       // alert(ROHSDescription);
        this.setFieldValue("ctlManufacturerName", (manufacturerName == "") ? "" : (manufacturerName + $R_FN.createAdvisoryNotesIcon(mfrAdvisoryNotes, 'margin-left-10')));
        this.setFieldValue("ctlDateCode", dateCode);
        this.setFieldValue("ctlPackageType", packageType);
        this.setFieldValue("ctlProductType", productType);
        this.setFieldValue("ctlMOQ",mOQ );
        this.setFieldValue("ctlTotalQSA", totalQSA);
        this.setFieldValue("ctlPriority", priority);
        this.setFieldValue("ctlLTB", lTB);
        this.setFieldValue("ctlNotes", $R_FN.setCleanTextValue(notes));
        
        this.setFieldValue("ctlSPQ", SPQ);
        this.setFieldValue("ctlLeadTime", leadTime);
        //this.setFieldValue("ctlROHSStatus", rohsStatus); 
        this.setFieldValue("ctlROHSStatus", ROHSDescription);
        this.setFieldValue("ctlFactorySealed", factorySealed);
        this.setFieldValue("ctlMSL", (MSLLevelNo > 0) ? MSLLevelText : MSL);
        //this.setFieldValue("ctlMSL", MSL);
        //[001] start
        this.setFieldValue("ctlSupplierWarranty", ((supplierWarranty > 0) ? supplierWarranty : "") + ((supplierWarranty > 0) ? " days" : ""));
        //[001] end
        //[003] start ctlTestingRecommended
        this.setFieldValue("ctlTestingRecommended", isTestingRecommended == true ? "Yes" : "No");
        this.setFieldValue("ctlIsImage", isImageAvail == true ? "Yes" : "No");
        this.setFieldValue("ctlCountryOfOrigin", CountryOfOriginName);
        this._IHSCountryOfOriginName = ihsCountryOfOriginName;
        
        //[003] end
        this.showField("ctlPriority", false);
        this.setFieldValue("ctlTypeOfSupplierName", TypeOfSupplierName);
        this.setFieldValue("ctlReasonForSupplierName", ReasonForSupplierName);
        this.setFieldValue("ctlRiskOfSupplierName", RiskOfSupplierName);
        $R_FN.showElement(this._pnlLineDetail, true);
        $R_FN.showElement(this._pnlLoadingLineDetail, false);
        this.changeBackGroundColor(isTestingRecommended);
    },

    changeBackGroundColor: function (bln) {
        var cols = document.getElementsByClassName('YellowBackground');
        for (i = 0; i < cols.length; i++) {
            (bln) ? cols[i].style.backgroundColor = 'yellow' : cols[i].style.backgroundColor = '';
        }
    },
    enableButtons: function (bln) {
        if (bln) {
            if (this._ibtnAdd) $R_IBTN.enableButton(this._ibtnAdd, (!this._blnRequirementClosed && this._IsAssignToMe));
            if (this._ibtnEdit) $R_IBTN.enableButton(this._ibtnEdit, (!this._blnRequirementClosed && this._tbl._aryCurrentValues.length == 1 && !this._blnSourcingRelease && this._IsAssignToMe));
            //if (this._ibtnQuote) $R_IBTN.enableButton(this._ibtnQuote, (!this._blnRequirementClosed && this._tbl._aryCurrentValues.length > 0 && !this._blnPOHub && !(this._IsSoCreated || this._IsClosed) && this._IsAssignToMe));
            if (this._ibtnQuote) $R_IBTN.enableButton(this._ibtnQuote, (!this._blnRequirementClosed && this._tbl._aryCurrentValues.length > 0 && !this._blnPOHub && !(this._IsSoCreated || this._IsClosed)));
            if (this._ibtnConfirm) $R_IBTN.enableButton(this._ibtnConfirm, (this._IsSourcingReleasedCount && !this._blnSourcingRelease && this._IsAssignToMe));

            //Delete Button Enable feature
            var partwatchClient = false;
            for (var i = 0; i < this._tbl._aryCurrentValues.length; i++) {
                if (this._tbl.getSelectedExtraData(this._tbl._arySelectedIndexes[i]).IsPartWatchMatchClient == true) {
                    partwatchClient = true;
                    break;
                }
                else {
                    partwatchClient = false;

                }
            }

            var partwatchhub = false;
            for (var i = 0; i < this._tbl._aryCurrentValues.length; i++) {
                if (this._tbl.getSelectedExtraData(this._tbl._arySelectedIndexes[i]).IsPartWatchMatch == true) {
                    partwatchhub = true;
                    break;
                }
                else {
                    partwatchhub = false;
                    
                }
            }

            if (this._ibtnDelete) $R_IBTN.enableButton(this._ibtnDelete, (!this._blnSourcingRelease && this._SourceRef && partwatchhub == false && this._SourceRef && this._IsAssignToMe));
            //END

            //PartWatch Match Delete
            var partwatch = false;
            for (var i = 0; i < this._tbl._aryCurrentValues.length; i++) {
                if (this._tbl.getSelectedExtraData(this._tbl._arySelectedIndexes[i]).IsPartWatchMatch == true) {
                    partwatch = true;
                }
                else {
                    partwatch = false;
                    break;
                }
            }
            /*if (this._ibtnDeletePartwatch) $R_IBTN.enableButton(this._ibtnDeletePartwatch, this._tbl._aryCurrentValues.length > 0 && !this._blnRequirementClosed && partwatch == true);*/ 
            if (this._ibtnDeletePartwatch) $R_IBTN.enableButton(this._ibtnDeletePartwatch, (this._tbl._aryCurrentValues.length > 0 && !this._blnSourcingRelease && partwatch == true) && this._IsAssignToMe);
            // END
            
        } else {
            if (this._ibtnEdit) $R_IBTN.enableButton(this._ibtnEdit, false);
            if (this._ibtnQuote) $R_IBTN.enableButton(this._ibtnQuote, false);
            if (this._ibtnDelete) $R_IBTN.enableButton(this._ibtnDelete, false);
            if (this._ibtnDeletePartwatch) $R_IBTN.enableButton(this._ibtnDeletePartwatch, false);
            if (this._ibtnConfirm) $R_IBTN.enableButton(this._ibtnConfirm, false);
        }
    },

    doQuote: function () {
        this.clearMessages();

        //if (this.getFieldValue("ctlMSL") === "") {
        if(!this.checkMSLValue()){
            this.addMessage("Kindly update MSL", $R_ENUM$MessageTypeList.Error);
            return false;
        }
        else
        location.href = $RGT_gotoURL_QuoteAdd(this._intCompanyID, null, this._intCustomerRequirementID, $R_FN.arrayToSingleString(this._tbl._aryCurrentValues));
    },

    showAddForm: function () {
        //alert(this._IHSCountryOfOriginName);
        this._frmAdd.setFormFieldsToDefaults();
        this.onAddFormShown();
        this._frmAdd._intCustomerRequirementID = this._intCustomerRequirementID;
        this._frmAdd._strPartNo = this._strPartNo;
        this._frmAdd._blnPOHub = this._blnPOHub;
        this._frmAdd._intSupplierId = -1;
        this._frmAdd._ClientNo = this._ClientNo; 
        this._frmAdd._Quantity = this._Quantity;
        this._frmAdd._PackageID = this._PackageID;
        this._frmAdd._ProductID = this._ProductID;
        this._frmAdd._ManufacturerNo = this._ManufacturerNo;
        this._frmAdd._Manufacturer = this._Manufacturer;
        this._frmAdd._DateCode = this._DateCode;
        this._frmAdd._ROHS = this._ROHS;
        this._frmAdd._Product = this._Product;
        this._frmAdd._IHSCountryOfOriginName = this._IHSCountryOfOriginName;
        this._frmAdd._blnISAS6081Required = this._blnISAS6081Required; 
       
        this.showForm(this._frmAdd, true);
    },

    showAddForm: function () {
       
        //alert(this._IHSCountryOfOriginName);
        this._frmAdd.setFormFieldsToDefaults();
        this.onAddFormShown();
        this._frmAdd._intCustomerRequirementID = this._intCustomerRequirementID;
        this._frmAdd._strPartNo = this._strPartNo;
        this._frmAdd._blnPOHub = this._blnPOHub;
        this._frmAdd._intSupplierId = -1;
        this._frmAdd._ClientNo = this._ClientNo;
        this._frmAdd._Quantity = this._Quantity;
        this._frmAdd._PackageID = this._PackageID;
        this._frmAdd._ProductID = this._ProductID;
        this._frmAdd._ManufacturerNo = this._ManufacturerNo;
        this._frmAdd._Manufacturer = this._Manufacturer;
        this._frmAdd._MfrAdvisoryNotes = this._MfrAdvisoryNotes;
        this._frmAdd._DateCode = this._DateCode;
        this._frmAdd._ROHS = this._ROHS;
        this._frmAdd._Product = this._Product;
        this._frmAdd._IHSCountryOfOriginName = this._IHSCountryOfOriginName;
        this._frmAdd._blnISAS6081Required = this._blnISAS6081Required;

        this.showForm(this._frmAdd, true);
    },
    showAddCustForm: function () {
        this._frmAdd.setFormFieldsToDefaults();
        this.onAddCustFormShown();
        this._frmAdd._intCustomerRequirementID = this._intCustomerRequirementID;
        this._frmAdd._strPartNo = this._strPartNo;
        this._frmAdd._intSupplierId = -1;
        this.showForm(this._frmAdd, true);
    },


    hideAddForm: function() {
        this.showForm(this._frmAdd, false);
        this._tbl.resizeColumns();
    },

    cancelAdd: function() {
        this.hideAddForm();
    },

    saveAddComplete: function() {
        this.getData();
        this.hideAddForm();
        this.showSavedOK(true, $R_RES.ChangesSavedSuccessfully);
        //this.onSourcingResultAdded();
        this.onSaveEditComplete();
    },

    viewTask: function () {
        location.href = ('Prf_ToDo.aspx?Category=2'), '_blank';
    },
    showAddTaskForm: function (quoteId, quoteNumber, status, customerName) { 
        this._frmAddTask.setFormFieldsToDefaults();
        this._frmAddTask._intQuoteID = quoteId;
        this._frmAddTask._quoteStatus = status;
        this._frmAddTask.setFieldValue("ctlDueTime", "09:00");
        this._frmAddTask.setFieldValue("ctlReminderTime", "09:00");
        this._frmAddTask.setFieldValue("ctlQuoteNumber", quoteNumber);
        this._frmAddTask.setFieldValue("ctlCustomerName", $R_FN.setCleanTextValue(customerName));
        this._frmAddTask.setFieldValue("ctlToDoCategory", "Quote");
        this.showForm(this._frmAddTask, true);
    },
    hideAddTaskForm: function () {
        this.showForm(this._frmAddTask, false);
    },

    addTaskComplete: function () {
        this.hideAddForm();
        this.showSavedOK(true, $R_RES.ChangesSavedSuccessfully);
        this.getData();
    },
    redirectToTaskDetails: function (quoteNumber) {
        location.href = ('Prf_ToDo.aspx?qn=' + quoteNumber + '&Category=2'), '_blank';
    },

    showEditForm: function () {
        this._frmEdit._intSourcingResultID = this._tbl._aryCurrentValues[0];
        this._frmEdit._ProductT = this._ProductT;
        this._frmEdit._PackageT = this._PackageT;
        this._frmEdit._blnPOHub = this._blnPOHub;
        this._frmEdit._ClientNo = this._ClientNo;
        this._frmEdit._SupplierManufacturerName = this._SupplierManufacturerName;
        this._frmEdit._supDateCode = this._supDateCode;
        this._frmEdit._ActBuyCurrencyNo = this._intActBuyCurrencyNo;
        this._frmEdit._OfferQuantity = this._Quantity;
        //[003] start
        this._frmEdit._isTestingRecommended = this._isTestingRecommended;
        this._frmEdit._IHSCountryOfOriginName = this._IHSCountryOfOriginName;
        this._frmEdit._blnISAS6081Required = this._blnISAS6081Required;
        //[003] end
        this.showForm(this._frmEdit, true);
    },

    hideEditForm: function() {
        this.showForm(this._frmEdit, false);
        this._tbl.resizeColumns();
    },

    cancelEdit: function() {
        this.hideEditForm();
    },

    saveEditComplete: function() {
        this.getData();
        this.hideEditForm();
        this.showSavedOK(true, $R_RES.ChangesSavedSuccessfully);
        this.onSaveEditComplete();
    },
       showDeleteForm: function() {
           this._frmDelete._intSourcingResultID = this._tbl._aryCurrentValues[0];
           this._frmDelete._blnIsPOHub = this._blnPOHub;
           this._frmDelete._aryCurrentValues = $R_FN.arrayToSingleString(this._tbl._aryCurrentValues);
        this.showForm(this._frmDelete, true);
    },
       saveDeleteComplete: function() {
        this.cancelDelete();
        this.showSavedOK(true, $R_RES.ChangesSavedSuccessfully);
        //this.getData();
        this.onSourcingResultDeleted();
    },
     cancelDelete: function() {
        this.showForm(this._frmDelete, false);
     },

//Delete PartWatch

    showDeletePartWatchForm: function () {
        this._frmDeletePartWatch._intSourcingResultID = this._tbl._aryCurrentValues[0];
        this._frmDeletePartWatch._aryCurrentValues = $R_FN.arrayToSingleString(this._tbl._aryCurrentValues);
        this.showForm(this._frmDeletePartWatch, true);
    },
    saveDeletePartWatchComplete: function () {
        this.cancelDelete();
        this.showSavedOK(true, $R_RES.ChangesSavedSuccessfully);
        //this.getData();
        this.onSourcingResultDeleted();
    },
    cancelDeletePartWatch: function () {
        this.showForm(this._frmDeletePartWatch, false);
    },

// END
     showConfirmForm: function () {
         this.onCallBeforeRelease();
         //alert(this._UnitBuyPrice + " " + this._UnitSellPrice);
         if (this._blnCanRelease) {
             this._ctlConfirm._intSourcingResultID = this._tbl._aryCurrentValues[0];
             this._ctlConfirm._intRequirementLineID = this._intRequirementLineID;
             this._ctlConfirm._intBOMID = this._intBOMID;
             this._ctlConfirm._BomCode = this._BomCode;
             this._ctlConfirm._BomName = this._BomName;
             this._ctlConfirm._BomCompanyName = this._BomCompanyName;
             this._ctlConfirm._BomCompanyNo = this._BomCompanyNo;
             this._ctlConfirm._SalesManNo = this._SalesManNo;
             this._ctlConfirm._SalesManName = this._SalesManName;
             this._ctlConfirm._CustReqNo = this._intCustomerRequirementID;
             this._ctlConfirm._ReqSalesman = this._ReqSalesman;
             this._ctlConfirm._SupportTeamMemberNo = this._SupportTeamMemberNo;
             this._ctlConfirm._UnitBuyPrice =this._UnitBuyPrice;
             this._ctlConfirm._UnitSellPrice = this._UnitSellPrice;
             this._ctlConfirm._UnitBuyPriceWithCurrany =this._UnitBuyPriceWithCurrany;
             this._ctlConfirm._UnitSellPriceWithCurrany = this._UnitSellPriceWithCurrany;
             this.showForm(this._ctlConfirm, true);

         }
     },
     saveConfirmComplete: function () {
         this.cancelConfirm();
         this.showSavedOK(true, $R_RES.ChangesSavedSuccessfully);
     },

     cancelConfirm: function () {
         this.showForm(this._ctlConfirm, false);
     },
    //[002] start
     checkMSLValue: function () {
         this._msl = true;
         var selectedRows = this._tbl._aryCurrentValues;
         var allData = this._tbl._aryExtraData;
         for (i = 0; i < selectedRows.length; i++) {
             for (j = 0; j < allData.length; j++) {
                 if (allData[j].RowId === selectedRows[i] && allData[j].MSLLevelNo === 0)
                     this._msl = false;
             }
         }
         return this._msl;
    },
    showApprovalForm: function () {
       // this.onCallBeforeRelease();
       // if (this._blnCanRelease) {
            this._ctlApproval._intSourcingResultID = this._tbl._aryCurrentValues[0];
            this._ctlApproval._intRequirementLineID = this._intRequirementLineID;
            this._ctlApproval._intBOMID = this._intBOMID;
            this._ctlApproval._BomCode = this._BomCode;
            this._ctlApproval._BomName = this._BomName;
            this._ctlApproval._BomCompanyName = this._BomCompanyName;
            this._ctlApproval._BomCompanyNo = this._BomCompanyNo;
            this._ctlApproval._SalesManNo = this._SalesManNo;
            this._ctlApproval._SalesManName = this._SalesManName;
            this._ctlApproval._CustReqNo = this._intCustomerRequirementID;
            this._ctlApproval._ReqSalesman = this._ReqSalesman;
            this._ctlConfirm._SupportTeamMemberNo = this._SupportTeamMemberNo;
            this.showForm(this._ctlApproval, true);
      //  }
    },
    saveApprovalComplete: function () {
        this.cancelApproval();
        this.showSavedOK(true, $R_RES.ChangesSavedSuccessfully);
    },

    cancelApproval: function () {
        this.showForm(this._ctlApproval, false);
    }

};

Rebound.GlobalTrader.Site.Controls.Nuggets.BOMCustomerRequirementSourcingResults.registerClass("Rebound.GlobalTrader.Site.Controls.Nuggets.BOMCustomerRequirementSourcingResults", Rebound.GlobalTrader.Site.Controls.Nuggets.Base);
