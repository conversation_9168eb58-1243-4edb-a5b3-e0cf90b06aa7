///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------

Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DataListNuggets");

Rebound.GlobalTrader.Site.Controls.DataListNuggets.CustomerRequirementsPrint = function(element) {
    Rebound.GlobalTrader.Site.Controls.DataListNuggets.CustomerRequirementsPrint.initializeBase(this, [element]);
    this._blnGet = true;
    this._intCompanyNo = -1;
    this._intContactNo = -1;

};

Rebound.GlobalTrader.Site.Controls.DataListNuggets.CustomerRequirementsPrint.prototype = {

    get_intCompanyID: function() { return this._intCompanyID; }, set_intCompanyID: function(v) { if (this._intCompanyID !== v) this._intCompanyID = v; },
    get_strCompanyName: function() { return this._strCompanyName; }, set_strCompanyName: function(v) { if (this._strCompanyName !== v) this._strCompanyName = v; },
    get_cmbCustomer: function() { return this._cmbCustomer; }, set_cmbCustomer: function(v) { if (this._cmbCustomer !== v) this._cmbCustomer = v; },
    get_ddlContact: function() { return this._ddlContact; }, set_ddlContact: function(v) { if (this._ddlContact !== v) this._ddlContact = v; },
    get_intContactID: function() { return this._intContactID; }, set_intContactID: function(v) { if (this._intContactID !== v) this._intContactID = v; },



    initialize: function() {

        this.addSetupDataCallEvent(Function.createDelegate(this, this.setupDataCall));
        this.addGetDataOKEvent(Function.createDelegate(this, this.getDataOK));

        this.addInitCompleteEvent(Function.createDelegate(this, this.initAfterBaseIsReady));
        this.addSortDataEvent(Function.createDelegate(this, this.getSorting));
        this._ibtnPrint = $get(this._aryButtonIDs[0]);
        this._strPathToData = "controls/DataListNuggets/CustomerRequirementsPrint";
        this._strDataObject = "CustomerRequirementsPrint";
        this.enableBulkButtons(false);
        this._table.addMultipleSelectionChanged(Function.createDelegate(this, this.selectionMade));
        this._strCK = "Req";
        this._strCKExp = 1;
        this._lnsSeperator = "|";
        //alert(this._intCompanyID);
        //

        if (this._ibtnPrint) $R_IBTN.addClick(this._ibtnPrint, Function.createDelegate(this, this.printCustReq));
        //this.getData();
        //this.showLoading(false);


        Rebound.GlobalTrader.Site.Controls.DataListNuggets.CustomerRequirementsPrint.callBaseMethod(this, "initialize");
    },

    initAfterBaseIsReady: function() {
        if (this._cmbCustomer) {
            this._cmbCustomer.setValue(this._intCompanyID, this._strCompanyName);
            this._cmbCustomer._aut.addSelectionMadeEvent(Function.createDelegate(this, this.selectedCustomer));
            $addHandler($get("ctl00_cphMain_ctlCustReqPrint_ctlDB_ctl19_ctlFilter_ctlCustomer_ctl04_aut_ctl04"), "click", Function.createDelegate(this, this.reselectData));
        }
        if ((this._intCompanyID && this._intCompanyID != -1) && (this._intContactID && this._intContactID != -1)) {
            this._intCompanyNo = this._intCompanyID;
            this._intContactNo = this._intContactID;
            this.selectedCustomer();
            this._ddlContact.setValue(this._intContactNo);
            this.getData();
        }
        else {
            this.showLoading(false);
        }
    },

    dispose: function() {
        if (this.isDisposed) return;
        this._intCompanyID = null;
        this._strCompanyName = null;
        this._cmbCustomer = null;
        this._ddlContact = null;
        this._intContactID = null;
        this._blnGet = null;
        this._intCompanyNo = null;
        this._intContactNo = null;
        if (this._ibtnPrint) this._ibtnPrint = null;
        Rebound.GlobalTrader.Site.Controls.DataListNuggets.CustomerRequirementsPrint.callBaseMethod(this, "dispose");
    },

    enableBulkButtons: function(bln) {
        if (this._ibtnPrint) $R_IBTN.enableButton(this._ibtnPrint, bln);
    },
    setupDataCall: function() {
        this._objData.addParameter("CmpID", this._intCompanyNo);
        this._objData.addParameter("ConID", this._intContactNo);
        this._objData.addParameter("IsGet", this._blnGet);
        this._blnGet = false;
        this._objData.addParameter("PageLimit", this._txtLimitResults.value);
        //alert(this._txtLimitResults.value);
    },

    printCustReq: function() {
   
        if (this._cmbCustomer)
            this._intCompanyID = this._cmbCustomer.getValue();
        if (!this._intCompanyID) {
            alert("Please select customer");
            return;
        }
        if (!this._ddlContact.getValue()) {
            alert("Please select contact.");
            return;
        }
        var strIDs = $R_FN.arrayToSingleString(this._table._aryCurrentValues, this._lnsSeperator);
        $R_FN.setCookie(this._strCK, strIDs, this._strCKExp); strIDs = "";
        $R_FN.openPrintWindowCustReqWithMultiples($R_ENUM$PrintObject.CustomerRequirement, this._intCompanyID);
        strIDs = null;
    },
    selectionMade: function() {
        this.enableBulkButtons(this._table._arySelectedIndexes.length > 0);
    },

    getDataOK: function(args) {          
        // this._cmbCustomer.setValue("83161", "ASAN ELEKTRONIK");
        for (var i = 0, l = this._objResult.Results.length; i < l; i++) {
            var row = this._objResult.Results[i];
            var aryData = [
				$RGT_nubButton_CustomerRequirement(row.ID, row.No)
				, $R_FN.writeDoubleCellValue($R_FN.writePartNo(row.Part, row.ROHS), $RGT_nubButton_Manufacturer(row.MfrNo, row.Mfr))
				, row.Quantity
				, $R_FN.writeDoubleCellValue($RGT_nubButton_Company(row.CMNo, row.CM), $RGT_nubButton_Contact(row.ContactNo, row.Contact))
				, $R_FN.setCleanTextValue(row.Salesman)
				, $R_FN.writeDoubleCellValue(row.Received, row.Promised)
			];
            this._table.addRow(aryData, row.ID, false);
            aryData = null; row = null;
        }
    },

    enableApplyFilter: function(bln) {
        if (this._ibtnApply) $R_IBTN.enableButton(this._ibtnApply, bln);
    },
    applyFilter: function() {
        if (this._cmbCustomer)
            this._intCompanyNo = this._cmbCustomer.getValue();
        if (!this._intCompanyNo) {
            alert("Please select customer.");
            return;
        }
        if (this._ddlContact)
            this._intContactNo = this._ddlContact.getValue();
        if (!this._intContactNo) {
            alert("Please select contact.");
            return;
        }
        this._blnGet = true;
        if (this._blnGettingData) return;
        this.onFilterData();
    },
    selectedCustomer: function() {
        this._table.clearTable();
        this._table.resizeColumns();
        this._table.clearSelection(true);
        //this.getData();

        if (this._cmbCustomer)
            this._intCompanyNo = this._cmbCustomer.getValue();
        if (!this._intCompanyNo) {
            this._intCompanyNo = 0;
        }
        this._ddlContact._intCompanyID = this._intCompanyNo;
        this._ddlContact.getData();
        // alert(this._ddlContact._intCompanyID);
        this.selectionMade();
    },
    resetFilter: function() {
        for (var i = 0, l = this._aryFilterFieldIDs.length; i < l; i++) {
            var fld = $find(this._aryFilterFieldIDs[i]);
            fld.reset();
            fld.resetToDefault();
            fld = null;
        }
        if (this._blnAllowSelection) {
            this._txtLimitResults.value = 50;
            this._intResultsLimit = 50;
        }
        this._cmbCustomer.setValue("", "");
    },
    reselectData: function() {

        //        this.showResultsPanels(true);
        //        //$R_FN.showElement(this._ctlPagingButtonsTop._element, false);
        //        // $R_FN.showElement(this._ctlPagingButtonsBottom._element, false);
        //        this.showNoData();
        this._table.clearTable();
        this._table.resizeColumns();
        this._table.clearSelection(true)
        this._blnGet = false;
        this._ddlContact._intCompanyID = -1;
        this._ddlContact.getData();
        this.getData();
    },
    getSorting: function() {
        this._blnGet = true;
    }

};
Rebound.GlobalTrader.Site.Controls.DataListNuggets.CustomerRequirementsPrint.registerClass("Rebound.GlobalTrader.Site.Controls.DataListNuggets.CustomerRequirementsPrint", Rebound.GlobalTrader.Site.Controls.DataListNuggets.Base, Sys.IDisposable);
