//Marker     Changed by      Date         Remarks
//[001]      Vinay           14/09/2012   Add airwaybill search
//[002]     <PERSON>     07/02/2020    Add WareHouse Search filter
//[003]     A<PERSON><PERSON><PERSON>  02/12/2021    Add new filter named "notexportedinvoices".
//[004]		Ravi			19-09-2023    RP - 2338  AS6081 Search/Filter functionality on different pages 
using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;
using Rebound.GlobalTrader.Site.Enumerations;

namespace Rebound.GlobalTrader.Site.Controls.DataListNuggets.Data {

	public class InvoiceLines : Base {

		/// <summary>
		/// Gets the main data
		/// </summary>
		protected override void GetData() {
			JsonObject jsn = new JsonObject();
            string AS6081 = GetFormValue_String("AS6081"); //[004]
            //check view level
            ViewLevelList enmViewLevel = (ViewLevelList)GetFormValue_Int("ViewLevel");
			int? SelectedclientNo =null;
			int? SessionClientNo = SessionManager.ClientID;
			bool? blnMakeYellow = false;
			if(SessionManager.IsGSA==true && SessionManager.IsGlobalUser==false)
            {
				SelectedclientNo = GetFormValue_NullableInt("Client");
				if(SelectedclientNo !=null)
                {
					blnMakeYellow = true;
				}
                else
                {
					blnMakeYellow = false;
				}

			}
            else
            {
				blnMakeYellow = false;
			}
			//get data	
			List<InvoiceLine> lst = InvoiceLine.DataListNugget(
				SessionManager.ClientID
				, (enmViewLevel == ViewLevelList.Team) ? (int?)SessionManager.LoginTeamID : null
				, (enmViewLevel == ViewLevelList.Division) ? (int?)SessionManager.LoginDivisionID : null
				, (enmViewLevel == ViewLevelList.My) ? (int?)LoginID : null
				, GetFormValue_NullableInt("PageIndex", 0)
				, GetFormValue_NullableInt("PageSize", 10)
				, GetFormValue_NullableInt("SortIndex")
				, GetFormValue_NullableInt("SortDir", SortColumnDirection.ASC)
                , GetFormValue_PartForLikeSearch("Part")
                //, GetFormValue_StringForNameSearch("Contact")
                 , GetFormValue_StringForNameSearchDecode("Contact")
               // , GetFormValue_StringForNameSearch("CMName")
                 , GetFormValue_StringForNameSearchDecode("CMName")
				, GetFormValue_NullableInt("Salesman")
				, GetFormValue_StringForSearch("CustPO")
				, GetFormValue_Boolean("IncludePaid")
				, GetFormValue_NullableInt("InvoiceNoLo")
				, GetFormValue_NullableInt("InvoiceNoHi")
				, GetFormValue_NullableInt("SONoLo")
				, GetFormValue_NullableInt("SONoHi")
				, GetFormValue_NullableDateTime("InvoiceDateFrom")
				, GetFormValue_NullableDateTime("InvoiceDateTo")
				, GetFormValue_Boolean("RecentOnly")
                //[001] code start
                , GetFormValue_StringForLikeSearch("AirWayBill")
                //[001] code end
                , GetFormValue_Boolean("IsGlobalLogin")
                , GetFormValue_NullableInt("Client")
                //[002]code start | Add wharehouse Search [Warehouse]
                , GetFormValue_NullableInt("Warehouse")
				, GetFormValue_NullableInt("CountryNo")
				//[002] code end
				, GetFormValue_Boolean("InvoiceHold")
                , GetFormValue_Boolean("notexportedinvoices")
                , (AS6081 == "1" ? true : (AS6081 == "2" ? false : (bool?)null)) //[004]
				, SessionManager.LoginID
			);

			//check counts
			jsn.AddVariable("TotalRecords", (lst.Count > 0) ? lst[0].RowCnt : 0);

			//format data
			JsonObject jsnRowsArray = new JsonObject(true);
			for (int i = 0; i < lst.Count; i++) {
				JsonObject jsnRow = new JsonObject();
				jsnRow.AddVariable("ID", lst[i].InvoiceId);
				jsnRow.AddVariable("No", lst[i].InvoiceNumber);
				jsnRow.AddVariable("Part", lst[i].Part);
				jsnRow.AddVariable("Price", Functions.FormatCurrency(lst[i].Price, lst[i].CurrencyCode, 5 ,true));
				jsnRow.AddVariable("Quantity", Functions.FormatNumeric(lst[i].Quantity));
				jsnRow.AddVariable("Date", Functions.FormatDate(lst[i].InvoiceDate));
				jsnRow.AddVariable("CM", lst[i].CompanyName);
				jsnRow.AddVariable("CMNo", lst[i].CompanyNo);
				jsnRow.AddVariable("CustPONO", lst[i].CustomerPO);
				jsnRow.AddVariable("ROHS", lst[i].ROHS);
                jsnRow.AddVariable("ClientName", lst[i].ClientName);
                jsnRow.AddVariable("AS6081", lst[i].AS6081); //[004]
				jsnRow.AddVariable("blnMakeYellow", blnMakeYellow);

				jsnRowsArray.AddVariable(jsnRow);
				jsnRow.Dispose();
				jsnRow = null;
			}
			jsn.AddVariable("Results", jsnRowsArray);
			OutputResult(jsn);
			jsnRowsArray.Dispose();
			jsnRowsArray = null;
			jsn.Dispose();
			jsn = null;
			base.GetData();
		}

		protected override void AddFilterStates() {
            //Prevent filter state for Tab
			//AddExplicitFilterState("ViewLevel", GetFormValue_Int("ViewLevel"));
			AddFilterState("Part");
			AddFilterState("Contact");
			AddFilterState("CMName");
			AddFilterState("Salesman");
			AddFilterState("CustPO");
			AddFilterState("IncludePaid");
			AddFilterState("InvoiceNo");
			AddFilterState("SONo");
			AddFilterState("InvoiceDateFrom");
			AddFilterState("InvoiceDateTo");
			AddFilterState("RecentOnly");
            //[001] code start
            AddFilterState("AirWayBill");
            //[001] code end
            //[002]code start | Add wharehouse Search [Warehouse]
            AddFilterState("Warehouse");
            //[002] code end

            //[003] code start
            AddFilterState("notexportedinvoices");
			AddFilterState("AS6081"); //[004]
            //[003] code end
            base.AddFilterStates();
		}

	}
}
