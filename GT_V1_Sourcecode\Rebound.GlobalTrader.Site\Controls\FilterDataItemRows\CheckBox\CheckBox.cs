using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using System.Collections;
using Rebound.GlobalTrader.Site.Controls.Nuggets;

namespace Rebound.GlobalTrader.Site.Controls.FilterDataItemRows {
	[DefaultProperty("")]
	[ToolboxData("<{0}:CheckBox runat=server></{0}:CheckBox>")]
	public class CheckBox : Base {

		#region Locals

		#endregion

		#region Properties

		#endregion

		#region Overrides

		/// <summary>
		/// OnInit
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
			FieldType = Type.CheckBox;
			base.OnInit(e);
			bool blnDebug = false;
#if (DEBUG)
			blnDebug = true;
#endif
			AddScriptReference(Functions.GetScriptReference(blnDebug, "Rebound.GlobalTrader.Site", "Controls.FilterDataItemRows.CheckBox.CheckBox", true));
		}

		/// <summary>
		/// OnLoad
		/// </summary>
		/// <param name="e"></param>
		protected override void OnLoad(EventArgs e) {
			EnsureChildControls();
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.CheckBox", ClientID);
			base.OnLoad(e);
		}

		public override void SetDefaultValue() {
			if (DefaultValue == null) DefaultValue = "false";
			bool blnDefault;
			if (Boolean.TryParse(DefaultValue, out blnDefault)) SetInitialValue(blnDefault);
			base.SetDefaultValue();
		}

		public override void Reset() {
			EnsureChildControls();
			Enable(false);
			base.Reset();
		}

		#endregion

		#region Methods

		public void SetInitialValue(bool blnValue) {
			EnsureChildControls();
			Enable(blnValue);
		}

		#endregion
	}

}