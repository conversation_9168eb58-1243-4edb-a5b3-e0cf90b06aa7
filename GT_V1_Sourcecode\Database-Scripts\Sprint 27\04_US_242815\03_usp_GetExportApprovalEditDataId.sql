﻿/*  
===========================================================================================  
TASK			UPDATED BY      DATE			ACTION		DESCRIPTION  
[US-242815]		Trung Pham		05-May-2025		UPDATE		Get more fields  
===========================================================================================  
*/
CREATE OR ALTER PROCEDURE usp_GetExportApprovalEditDataId        
@ExportApprovalId  INT        
AS        
/*        
 *[001]  Created  Abhinav Saxena  18-04-2023  Add new procedure to get the data of export approval Edit Screen.           
 */        
BEGIN        
SET NOCOUNT ON        
DECLARE @ExportPowerUrl NVARCHAR(MAX)='';    
DECLARE @EUUFormUploadName NVARCHAR(MAX)=NULL;    
  
CREATE TABLE #tempEditExportApproval        
(        
ExportApprovalId   INT,     
ExportApprovalDetailsId  INT,     
SalesPersonName    NVARCHAR(MAX),        
SalesOrderNumber   INT,        
LineNumber     INT,        
CustomerName    NVARCHAR(MAX),        
PART      NVARCHAR(MAX),        
DestinationCountryId  INT,      
MilitaryUseId    INT,      
EndUserText        NVARCHAR(MAX),  
ExportApprovalStatusId  INT,
ECCN NVARCHAR(MAX),
PartApplication NVARCHAR(100),
ExportControl BIT,
AerospaceUse BIT,
PartTested NVARCHAR(200),
CommodityCode NVARCHAR(MAX),
ClientNo INT,
ShipToCustomerCountry NVARCHAR(MAX),
ShipToCustomerName NVARCHAR(MAX),
ShipFromCountry NVARCHAR(MAX),
ShipFromWarehouse NVARCHAR(MAX)
)        
        
INSERT INTO #tempEditExportApproval        
SELECT          
ISNULL(easg.ExportApprovalId,0),   
ISNULL(ead.ExportApprovalDetailsId,0),       
lgnsp.EmployeeName,        
so.SalesOrderNumber,        
sol.SOSerialNo,        
co.CompanyName,        
sol.FullPart,  
ISNULL(ead.EndDestinationCountryNo,0),  
ISNULL(ead.MilitaryUseNo,0),  
ISNULL(ead.ENDUserText,''),  
easg.ApprovalStatusId,
sol.ECCNCode,
ead.PartApplication,
ead.ExportControl,
ead.AerospaceUse,
ead.PartTested,
pr.DutyCode,
so.ClientNo,
cy.CountryName,
co.CompanyName,
ct.CountryName,
wh.WarehouseName
FROM  tbSO_ExportApprovalStatusOGEL easg  
LEFT OUTER JOIN  tbSO_ExportApprovalDetails ead   
ON ead.ExportApprovalNo=easg.ExportApprovalId     
LEFT OUTER JOIN tbSalesOrder so ON easg.SalesOrderNo=so.SalesOrderId        
LEFT OUTER JOIN tbLogin lgnsp ON so.Salesman=lgnsp.LoginId        
LEFT OUTER JOIN tbSalesOrderLine sol ON easg.SalesOrderLineNo=sol.SalesOrderLineId        
LEFT OUTER JOIN tbCompany co ON so.CompanyNo=co.CompanyId
LEFT OUTER JOIN tbProduct pr ON pr.ProductId=sol.ProductNo
LEFT OUTER JOIN tbAddress ad ON ad.AddressId=so.ShipToAddressNo
LEFT OUTER JOIN tbCountry cy ON cy.CountryId=ad.CountryNo
LEFT OUTER JOIN vwAllocation al ON al.CustomerPO=so.CustomerPO AND sol.Part = al.Part
LEFT OUTER JOIN tbPurchaseOrder po ON al.PurchaseOrderNo=po.PurchaseOrderId
LEFT OUTER JOIN tbCountry ct ON ct.CountryId=po.ImportCountryNo
LEFT OUTER JOIN tbWareHouse wh ON wh.WarehouseId=po.WarehouseNo
WHERE easg.ExportApprovalId=@ExportApprovalId        
        
SELECT        
 *        
  From #tempEditExportApproval        
        
DROP TABLE #tempEditExportApproval        
SET NOCOUNT OFF        
END  