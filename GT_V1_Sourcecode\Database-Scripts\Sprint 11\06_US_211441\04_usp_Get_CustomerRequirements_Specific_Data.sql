﻿SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
/*   
===========================================================================================  
TASK			UPDATED BY			DATE			ACTION		DESCRIPTION  
[US-211441]		Trung Pham Van		24-Sep-2024		CREATE		Get specific columns by CustomerRequirementId
===========================================================================================  
*/
CREATE OR ALTER PROCEDURE usp_Get_CustomerRequirements_Specific_Data
	@CustomerRequirementId INT
AS
BEGIN
	SELECT cr.CustomerRequirementId
	,cr.<PERSON><PERSON><PERSON>o
	,cr.Company<PERSON>o
	,ISNULL(c.Salesman, cr.Contact<PERSON>o) AS Salesman
	,cr.CurrencyNo
	FROM tbCustomerRequirement cr
	LEFT JOIN tbCompany c ON c.CompanyId = cr.CompanyNo
	WHERE CustomerRequirementId = @CustomerRequirementId
END
GO