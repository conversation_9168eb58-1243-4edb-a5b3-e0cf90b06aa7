using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.BLL;
using Rebound.GlobalTrader.Site.Controls;

namespace Rebound.GlobalTrader.Site.Controls.Nuggets {
    public partial class ClientImportBOMMainInfo : Base
    {

		#region Locals
		protected IconButton _ibtnEdit;
        protected IconButton _ibtnExportCSV;
        protected IconButton _ibtnExportPurchaseHUB;
		protected IconButton _ibtnDelete;
        protected IconButton _ibtnNotify;
        protected IconButton _ibtnRelease;
        protected IconButton _ibtnClose;
        protected IconButton _ibtnNoBid;
        protected IconButton _ibtnNote;

        protected IconButton _ibtnComplete;
		#endregion

		#region Properties

		private int _intBOMID = -1;
		public int BOMID {
			get { return _intBOMID; }
			set { _intBOMID = value; }
		}

		private bool _blnCanEdit = true;
		public bool CanEdit {
			get { return _blnCanEdit; }
			set { _blnCanEdit = value; }
		}

		private bool _blnCanDelete = true;
		public bool CanDelete {
			get { return _blnCanDelete; }
			set { _blnCanDelete = value; }
		}

        private bool _blnCanNotify = true;
        public bool CanNotify
        {
            get { return _blnCanNotify; }
            set { _blnCanNotify = value; }
        }

        private bool _blnCanRelease = true;
        public bool CanRelease
        {
            get { return _blnCanRelease; }
            set { _blnCanRelease = value; }
        }

        private bool _blnCanClosed = true;
        public bool CanClosed
        {
            get { return _blnCanClosed; }
            set { _blnCanClosed = value; }
        }
        private bool _blnCanNobid = true;
        public bool CanNoBid
        {
            get { return _blnCanNobid; }
            set { _blnCanNobid = value; }
        }
        private bool _blnCanNote = true;
        public bool CanNote
        {
            get { return _blnCanNote; }
            set { _blnCanNote = value; }
        }
      
		#endregion

		#region Overrides

		/// <summary>
		/// Oninit
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
			base.OnInit(e);
			WireUpControls();
			AddScriptReference("Controls.Nuggets.ClientImportBOMMainInfo.ClientImportBOMMainInfo.js");
			TitleText = Functions.GetGlobalResource("Nuggets", "ClientImportBOMMainInfo");
            if (_objQSManager.ClientBOMID > 0) _intBOMID = _objQSManager.ClientBOMID;
		}

		protected override void OnLoad(EventArgs e) {
			SetupScriptDescriptors();
			base.OnLoad(e);
		}

        protected override void OnPreRender(EventArgs e)
        {
            _ibtnEdit.Visible = _blnCanEdit;
            //_ibtnDelete.Visible = _blnCanDelete;
           // _ibtnExportPurchaseHUB.Visible = !Convert.ToBoolean(SessionManager.IsPOHub);
            //_ibtnRelease.Visible = Convert.ToBoolean(SessionManager.IsPOHub);
           // _ibtnClose.Visible = !Convert.ToBoolean(SessionManager.IsPOHub);// false;
           // _ibtnNoBid.Visible = Convert.ToBoolean(SessionManager.IsPOHub);
          
           // _ibtnRelease.Enabled = false;
            //_ibtnRelease.Visible = _blnCanRelease;
            base.OnPreRender(e);
        }

		#endregion

		/// <summary>
		/// Sets up AJAX script descriptors
		/// </summary>
		private void SetupScriptDescriptors() {
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.Nuggets.ClientImportBOMMainInfo", ctlDesignBase.ClientID);
			if (_blnCanEdit) _scScriptControlDescriptor.AddElementProperty("ibtnEdit", _ibtnEdit.ClientID);
            _scScriptControlDescriptor.AddElementProperty("ibtnComplete", _ibtnComplete.ClientID);
			_scScriptControlDescriptor.AddProperty("intBOMID", _intBOMID);
           _scScriptControlDescriptor.AddProperty("blnPOHub", Convert.ToBoolean(SessionManager.IsPOHub));
           _scScriptControlDescriptor.AddElementProperty("ibtnExportPurchaseHUB", _ibtnExportPurchaseHUB.ClientID);
		}

		/// <summary>
		/// Wire up controls from the ascx
		/// </summary>
		private void WireUpControls() {
			_ibtnEdit = FindIconButton("ibtnEdit");
            _ibtnComplete = FindIconButton("ibtnComplete");
            _ibtnExportPurchaseHUB = FindIconButton("ibtnExportPurchaseHUB");
		}

	}
}
