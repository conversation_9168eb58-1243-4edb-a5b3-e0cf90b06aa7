﻿CREATE OR ALTER PROCEDURE [dbo].[usp_selectAll_SecurityFunction_by_SitePage]
    --*******************************************************************************************  
    --* RP 15.08.09:  
    --* - added ordering by new field DisplaySortOrder  
    --*******************************************************************************************  
    @SitePageNo int
AS
SELECT sf.SecurityFunctionId,
       sf.FunctionName,
       sf.SitePageNo
FROM tbSecurityFunction sf
    JOIN tbSitePage sp
        ON sp.SitePageId = sf.SitePageNo
WHERE sf.SitePageNo = @SitePageNo
      AND NOT sf.SitePageNo IS NULL
      --and sf.SitePageNo in (3000102,3000502,3000802)  
      AND sf.ReportNo IS NULL
ORDER BY sf.DisplaySortOrder


