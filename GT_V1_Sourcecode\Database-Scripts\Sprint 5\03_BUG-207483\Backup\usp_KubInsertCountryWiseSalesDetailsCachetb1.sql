
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE PROCEDURE [dbo].[usp_KubInsertCountryWiseSalesDetailsCachetb1]              
@Part  NVARCHAR(100)='',            
@ClientNo INT=0               
AS                
/*            
 *Action: Created  By:<PERSON><PERSON><PERSON><PERSON>  Date:31-07-2023  Comment: Proc is used for cache the sales data.            
 */              
BEGIN                  
SET NOCOUNT ON                  
DECLARE @DefaultCurrency INT = 0;          
DECLARE @DefaultCurrencyCode NVARCHAR(max);          
CREATE TABLE #tbTemp_UnShippedSODetails    
(    
ClientNo  INT,    
PartNo   NVARCHAR(100),    
Countries  NVARCHAR(100),    
ReSale   DECIMAL(16,5)    
)     
CREATE TABLE #tbTemp_UnShippedSODetailsFinal    
(    
ClientNo  INT,    
PartNo   NVARCHAR(100),    
Countries  NVARCHAR(100),    
ReSale   NVARCHAR(200),    
NoOfSales       NVARCHAR(100)    
)           
SELECT @DefaultCurrency= CurrencyNo, @DefaultCurrencyCode= cr.CurrencyCode FROM tbClient c           
left join tbCurrency cr on c.CurrencyNo = cr.CurrencyId          
WHERE ClientId = @ClientNo;          
          
IF((SELECT COUNT(1) FROM tbKubCountryWiseSaleDetailsCache WHERE ClientID=@ClientNo AND PartNo=@Part             
AND DLUP>DATEAdd(DAY,-3,GETDATE()))=0)             
BEGIN             
DELETE FROM tbKubCountryWiseSaleDetailsCache WHERE ClientID=@ClientNo AND PartNo=@Part             
    
-----Shipped Invoices Data----          
INSERT INTO [dbo].[tbKubCountryWiseSaleDetailsCache]              
(              
ClientID,              
PartNo,              
Countries,              
NoOfSales,              
ReSale,           
DLUP          
)          
           
SELECT iv.ClientNo, ivl.FullPart as PartNo, ct.CountryName as Countries, COUNT(iv.InvoiceId) as NoOfSales,              
CAST(          
 CONVERT(DECIMAL(16,2)          
  ,dbo.ufn_convert_currency_value(SUM(CONVERT(DECIMAL(16,5),ivl.Price)*ila.Quantity),iv.CurrencyNo,@DefaultCurrency,GETDATE())          
  )        
 AS NVARCHAR(100))  + ' ' + @DefaultCurrencyCode as ReSale              
  , GETDATE()          
FROM  tbInvoiceLine AS ivl     
INNER JOIN tbInvoice AS iv ON  ivl.InvoiceNo= iv.InvoiceId       
INNER JOIN tbAddress AS ad ON ad.AddressId = iv.ShipToAddressNo              
INNER JOIN tbCountry AS ct ON ct.CountryId= ad.CountryNo    
LEFT OUTER JOIN dbo.tbInvoiceLineAllocation ila ON  
ivl.InvoiceLineId =ila.InvoiceLineNo             
WHERE CAST(ivl.ShippedDate AS DATE)>CAST(DATEADD(MONTH,-12,GETDATE()) AS DATE)            
AND ivl.FullPart=@Part   AND iv.ClientNo=@ClientNo              
GROUP BY iv.ClientNo,ct.CountryName,ivl.FullPart, iv.CurrencyNo            
ORDER BY NoOfSales,ivl.FullPart ASC       
-------END-----     
    
    
----For Unshipped SO details----    
INSERT INTO #tbTemp_UnShippedSODetails    
SELECT so.ClientNo, sol.FullPart as PartNo, ct.CountryName as Countries,              
          
 CAST(CONVERT(DECIMAL(16,5)          
  ,dbo.ufn_convert_currency_value(SUM(CONVERT(DECIMAL(16,5),sol.Price)*sol.Quantity),so.CurrencyNo,@DefaultCurrency,GETDATE())          
  )  
    
   AS NVARCHAR(100))        
   as ReSale                       
FROM  tbSalesOrderLine AS sol     
INNER JOIN tbSalesOrder AS so ON  sol.SalesOrderNo= so.SalesOrderId       
INNER JOIN tbAddress AS ad ON ad.AddressId = so.ShipToAddressNo              
INNER JOIN tbCountry AS ct ON ct.CountryId= ad.CountryNo             
WHERE CAST(sol.RequiredDate AS DATE)>CAST(DATEADD(MONTH,-12,GETDATE()) AS DATE)            
AND sol.FullPart=@Part   AND so.ClientNo=@ClientNo              
GROUP BY so.ClientNo,ct.CountryName,sol.FullPart, so.CurrencyNo,so.SalesOrderId          
ORDER BY sol.FullPart ASC     
    
INSERT INTO #tbTemp_UnShippedSODetailsFinal    
SELECT ClientNo,PartNo,Countries,SUM(ReSale),COUNT(PartNo)    
FROM #tbTemp_UnShippedSODetails Group By PartNo,Countries,ClientNo    
    
DELETE FROM tbKubCountryWiseUnShippedSaleDetailsCache WHERE PartNo=@Part AND clientId=@ClientNo    
INSERT INTO tbKubCountryWiseUnShippedSaleDetailsCache    
(clientId,    
PartNo,    
Countries,    
ReSale,    
NoOfSales,    
DLUP)    
SELECT    
ClientNo,    
PartNo,    
Countries,    
CAST(CONVERT(DECIMAL(16,2),ReSale) AS NVARCHAR(100))+' '+@DefaultCurrencyCode,    
NoOfSales,    
GETDATE()    
FROM #tbTemp_UnShippedSODetailsFinal    
-------END----------------    
          
END    
DROP TABLE #tbTemp_UnShippedSODetails       
DROP TABLE #tbTemp_UnShippedSODetailsFinal            
SET NOCOUNT OFF                  
END 
GO


