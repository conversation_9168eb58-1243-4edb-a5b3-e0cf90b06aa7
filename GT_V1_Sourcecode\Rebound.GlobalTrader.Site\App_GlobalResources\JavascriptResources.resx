﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="AddressTypeAlreadyEntered" xml:space="preserve">
    <value>An address with that type has already been entered</value>
  </data>
  <data name="ApplicationError" xml:space="preserve">
    <value>Sorry, there was a problem.&lt;br /&gt;The following details have been noted in your Computer's event log and emailed to Rebound staff for attention.</value>
    <comment>Copied from Messages.resx</comment>
  </data>
  <data name="ContactsForCompany" xml:space="preserve">
    <value>Contacts for {0}</value>
  </data>
  <data name="DatabaseError" xml:space="preserve">
    <value>There was a problem with the database</value>
  </data>
  <data name="DatabaseTimeout" xml:space="preserve">
    <value>Sorry, the database call has timed out</value>
  </data>
  <data name="DataNotFound" xml:space="preserve">
    <value>Error: No data found</value>
  </data>
  <data name="DateTimeMustBeInFuture" xml:space="preserve">
    <value>Please enter a date and time in the future</value>
  </data>
  <data name="DuplicateCurrencyCode" xml:space="preserve">
    <value>A currency with that code already exists on the database.</value>
  </data>
  <data name="DuplicateLoginName" xml:space="preserve">
    <value>The Login Name you entered has already been taken</value>
  </data>
  <data name="EmailInvalidMessage" xml:space="preserve">
    <value>Please enter a valid email address</value>
  </data>
  <data name="EnterFieldGeneric" xml:space="preserve">
    <value>Please enter a value</value>
  </data>
  <data name="Error" xml:space="preserve">
    <value>Error</value>
  </data>
  <data name="FeedbackSent" xml:space="preserve">
    <value>Your feedback has been sent to Rebound, thank you for your time.&lt;br/&gt;Press &lt;b&gt;Continue&lt;/b&gt; to return to your previous page.</value>
  </data>
  <data name="FolderAdded" xml:space="preserve">
    <value>Your new folder has been added</value>
  </data>
  <data name="FolderDeleted" xml:space="preserve">
    <value>Your folder has been deleted</value>
  </data>
  <data name="Inactive" xml:space="preserve">
    <value>Inactive</value>
  </data>
  <data name="Inbox" xml:space="preserve">
    <value>Inbox</value>
  </data>
  <data name="ItemXOfY" xml:space="preserve">
    <value>Item {0} of {1}</value>
  </data>
  <data name="LineXOfY" xml:space="preserve">
    <value>Line {0} of {1}</value>
  </data>
  <data name="Loading" xml:space="preserve">
    <value>Loading</value>
  </data>
  <data name="MessageDeleted" xml:space="preserve">
    <value>Your messages have been deleted</value>
  </data>
  <data name="MessageMoved" xml:space="preserve">
    <value>Your messages have been moved</value>
  </data>
  <data name="MessageSent" xml:space="preserve">
    <value>Your message has been sent</value>
  </data>
  <data name="NewCustomerRequirementAdded" xml:space="preserve">
    <value>New Customer Requirement Added</value>
    <comment>Used in Mail Message Subjects</comment>
  </data>
  <data name="NewCustomerRMAAdded" xml:space="preserve">
    <value>New Customer RMA Added</value>
    <comment>Used in Mail Message Subjects</comment>
  </data>
  <data name="NewCreditNoteAdded" xml:space="preserve">
    <value>New Credit Note Added</value>
    <comment>Used in Mail Message Subjects</comment>
  </data>
  <data name="NewDebitNoteAdded" xml:space="preserve">
    <value>New Debit Note Added</value>
    <comment>Used in Mail Message Subjects</comment>
  </data>
  <data name="NewGoodsInAdded" xml:space="preserve">
    <value>New Goods In Note Added</value>
    <comment>Used in Mail Message Subjects</comment>
  </data>
  <data name="NewInvoiceAdded" xml:space="preserve">
    <value>New Invoice Added</value>
    <comment>Used in Mail Message Subjects</comment>
  </data>
  <data name="NewPurchaseOrderAdded" xml:space="preserve">
    <value>New Purchase Order Added</value>
    <comment>Used in Mail Message Subjects</comment>
  </data>
  <data name="NewQuoteAdded" xml:space="preserve">
    <value>New Quote Added</value>
    <comment>Used in Mail Message Subjects</comment>
  </data>
  <data name="NewSalesOrderAdded" xml:space="preserve">
    <value>New Sales Order Added</value>
    <comment>Used in Mail Message Subjects</comment>
  </data>
  <data name="NewSupplierRMAAdded" xml:space="preserve">
    <value>New Supplier RMA Added</value>
    <comment>Used in Mail Message Subjects</comment>
  </data>
  <data name="No" xml:space="preserve">
    <value>No</value>
  </data>
  <data name="None" xml:space="preserve">
    <value>None</value>
  </data>
  <data name="NumericFieldError" xml:space="preserve">
    <value>Please enter a valid numeric value</value>
  </data>
  <data name="OldPasswordIncorrect" xml:space="preserve">
    <value>The password you entered is incorrect.</value>
  </data>
  <data name="RequiredFieldMissingMessage" xml:space="preserve">
    <value>Please enter a value</value>
  </data>
  <data name="RetainOneAdmin" xml:space="preserve">
    <value>The Administrators Group must retain at least one member.</value>
  </data>
  <data name="ROHSCompliant" xml:space="preserve">
    <value>RoHS Compliant</value>
  </data>
  <data name="ROHSExempt" xml:space="preserve">
    <value>RoHS Exempt</value>
  </data>
  <data name="ROHSNonCompliant" xml:space="preserve">
    <value>RoHS Non-compliant</value>
  </data>
  <data name="ROHSUnknown" xml:space="preserve">
    <value>RoHS Unknown</value>
  </data>
  <data name="SentMessages" xml:space="preserve">
    <value>Sent</value>
  </data>
  <data name="SourcingNuggetTitle" xml:space="preserve">
    <value>Sourcing</value>
  </data>
  <data name="Today" xml:space="preserve">
    <value>Today</value>
  </data>
  <data name="Unfiltered" xml:space="preserve">
    <value>Unfiltered</value>
  </data>
  <data name="URLInvalidMessage" xml:space="preserve">
    <value>Please enter a valid web address</value>
  </data>
  <data name="Yes" xml:space="preserve">
    <value>Yes</value>
  </data>
  <data name="EmailTo" xml:space="preserve">
    <value>To</value>
  </data>
  <data name="TooMuchStockAllocated" xml:space="preserve">
    <value>You have allocated more stock than is available</value>
  </data>
  <data name="ROHSNotApplicable" xml:space="preserve">
    <value>RoHS Not Applicable</value>
  </data>
  <data name="FileUploadFailed" xml:space="preserve">
    <value>The file upload failed</value>
  </data>
  <data name="FileUploadNotAllowedType" xml:space="preserve">
    <value>The file you have selected is not an allowed type</value>
  </data>
  <data name="AppTitle" xml:space="preserve">
    <value>Rebound Global:Trader</value>
  </data>
  <data name="NumericFieldGreaterThanError" xml:space="preserve">
    <value>Please enter a numeric value greater than {0}</value>
  </data>
  <data name="NumericFieldLessThanError" xml:space="preserve">
    <value>Please enter a numeric value less than {0}</value>
  </data>
  <data name="NumericFieldGreaterThanOrEqualToError" xml:space="preserve">
    <value>Please enter a numeric value greater than or equal to {0}</value>
  </data>
  <data name="NumericFieldLessThanOrEqualToError" xml:space="preserve">
    <value>Please enter a numeric value less than or equal to {0}</value>
  </data>
  <data name="NotifySalesOrder" xml:space="preserve">
    <value>Sales Order {0} Notification</value>
    <comment>Used in Mail Message Subjects</comment>
  </data>
  <data name="Sending" xml:space="preserve">
    <value>Sending</value>
  </data>
  <data name="Saving" xml:space="preserve">
    <value>Saving</value>
  </data>
  <data name="ReceivedPurchaseOrder" xml:space="preserve">
    <value>Received Purchase Order</value>
  </data>
  <data name="PopupLoggedOut" xml:space="preserve">
    <value>You are no longer logged in, this window will close</value>
  </data>
  <data name="NotifyGoodsIn" xml:space="preserve">
    <value>Goods In Note {0} Notification</value>
    <comment>Used in Mail Message Subjects</comment>
  </data>
  <data name="OnStop" xml:space="preserve">
    <value>On Stop</value>
  </data>
  <data name="Units_Kg" xml:space="preserve">
    <value>kg</value>
  </data>
  <data name="Units_Pounds" xml:space="preserve">
    <value>lb</value>
  </data>
  <data name="ClosePO" xml:space="preserve">
    <value>This will close the header and all lines.
All allocations will be deleted.
All ordered line quantities will be set to the received quantity.
The related Sales Order will not be deleted.
Please ensure all related documentation is dealt with.</value>
  </data>
  <data name="CloseSO" xml:space="preserve">
    <value>This will close the header and all lines.
All allocations will be deleted.
The related Purchase Order will not be deleted.
Please ensure all related documentation is dealt with.</value>
  </data>
  <data name="ConfirmQuantityOrderedGreaterThanRequired" xml:space="preserve">
    <value>The quantity ordered is greater than the quantity required.
Do you want to retain this excessive quantity?</value>
  </data>
  <data name="FreightChargeAndShippingChanged" xml:space="preserve">
    <value>Freight charge and shipping cost changed.
Related Division header, Tax and Incoterms also changed.</value>
  </data>
  <data name="FreightChargeLeft" xml:space="preserve">
    <value>Freight charge will be left as is.</value>
  </data>
  <data name="FreightChargeWillBeChanged" xml:space="preserve">
    <value>Freight charge will be changed. </value>
  </data>
  <data name="QuantityOrderedAsEntered" xml:space="preserve">
    <value>Quantity ordered will be as entered.</value>
  </data>
  <data name="QuantityOrderedResetToOriginal" xml:space="preserve">
    <value>Quantity ordered has been re-set to the original value.</value>
  </data>
  <data name="ShipInCostChanged" xml:space="preserve">
    <value>Ship-In cost changed.</value>
  </data>
  <data name="ShippingWaived" xml:space="preserve">
    <value>Shipping normally waived for this Customer</value>
  </data>
  <data name="SOFreightChargeOption" xml:space="preserve">
    <value>Press OK to set the default freight charge for this new shipping method or press Cancel to leave the freight charge field empty.</value>
  </data>
  <data name="ClosePOLine" xml:space="preserve">
    <value>This will close the line.
Any allocation will be deleted.
The ordered line quantity will be set to the received quantity.
The related Sales Order line will not be deleted.
Please ensure all related documentation is dealt with.</value>
  </data>
  <data name="CloseSOLine" xml:space="preserve">
    <value>This will close the line.
Any allocation will be deleted.
The ordered line quantity can be set to the shipped quantity if indicated.
The related Purchase Order line will not be deleted.
Please ensure all related documentation is dealt with.</value>
  </data>
  <data name="SearchCancelled" xml:space="preserve">
    <value>Your search was cancelled</value>
  </data>
  <data name="CloseSOAndResetLines" xml:space="preserve">
    <value>This will close the header and all lines.
All line quantities will be reset to as much as has been shipped.
All allocations will be deleted.
The related Purchase Order will not be deleted.
Please ensure all related documentation is dealt with.</value>
  </data>
  <data name="QuotedValue" xml:space="preserve">
    <value>Quoted {0}</value>
  </data>
  <data name="NotifyPurchaseOrder" xml:space="preserve">
    <value>Purchase Order {0} Notification</value>
    <comment>Used in Mail Message Subjects</comment>
  </data>
  <data name="OverShipmentCancelled" xml:space="preserve">
    <value>Overshipment(s) cancelled</value>
  </data>
  <data name="OverShipmentConfirmed" xml:space="preserve">
    <value>Overshipment(s) confirmed</value>
  </data>
  <data name="OverShipmentMessage" xml:space="preserve">
    <value>A quantity shipped is greater than the quantity ordered.
Press OK to accept the overshipment or Cancel to reset the quantity to the amount ordered.</value>
  </data>
  <data name="QuantitiesAllocatedAndInStock" xml:space="preserve">
    <value>{0} allocated, {1} in stock</value>
  </data>
  <data name="QuantityAllocated" xml:space="preserve">
    <value>{0} allocated</value>
  </data>
  <data name="DateMustBeInFuture" xml:space="preserve">
    <value>Please enter a date in the future</value>
  </data>
  <data name="CloseCRMALine" xml:space="preserve">
    <value>This will close the line.
Any allocation will be deleted.
The ordered line quantity will be set to the received quantity.
Please ensure all related documentation is dealt with.</value>
  </data>
  <data name="ContactEmailMessage" xml:space="preserve">
    <value>Email address required if finance contact checked</value>
  </data>
  <data name="InvoiceProgressMessage" xml:space="preserve">
    <value>Selected Invoices is in progress to send. You can check the status under Report =&gt; Invoicing =&gt; Invoices</value>
  </data>
  <data name="CustomerNoMessage" xml:space="preserve">
    <value>If approved Customer No. required</value>
  </data>
  <data name="FinanceContactNotFoundMessage" xml:space="preserve">
    <value>Finance contact is not found in the following invoices: </value>
  </data>
  <data name="BankFeeMessage" xml:space="preserve">
    <value>Bank fee required if apply bank fee checked</value>
  </data>
  <data name="AlternatePartMessage" xml:space="preserve">
    <value>Alternate part number already exist</value>
  </data>
  <data name="HistoryMessage" xml:space="preserve">
    <value>Please click on refresh button to view record</value>
  </data>
  <data name="NoDataFound" xml:space="preserve">
    <value>Sorry, no data was found</value>
  </data>
  <data name="SourcingFinish" xml:space="preserve">
    <value>Finish</value>
  </data>
  <data name="SourcingSixMonth" xml:space="preserve">
    <value>Further 6 Months</value>
  </data>
  <data name="SourcingTwelveMonth" xml:space="preserve">
    <value>More than 12 Months</value>
  </data>
  <data name="CreditLimitMessage" xml:space="preserve">
    <value>Freight should be less than credit balance of the company</value>
  </data>
  <data name="COMPANY" xml:space="preserve">
    <value>COMPANY</value>
  </data>
  <data name="DIVISION" xml:space="preserve">
    <value>DIVISION</value>
  </data>
  <data name="MY" xml:space="preserve">
    <value>MY</value>
  </data>
  <data name="SECTION_NAME" xml:space="preserve">
    <value>SECTION NAME</value>
  </data>
  <data name="TEAM" xml:space="preserve">
    <value>TEAM</value>
  </data>
  <data name="AvailableCreditBalanceToShipSO" xml:space="preserve">
    <value>Invoice value exceeded the credit limit</value>
  </data>
  <data name="OverCreditLimitOfCompanyMessage" xml:space="preserve">
    <value>The total of open orders takes the company over its credit limit, this Sales Order cannot be authorised</value>
  </data>
  <data name="LocalCurrencyMessage" xml:space="preserve">
    <value>Currency already added</value>
  </data>
  <data name="NewSupplierInvoiceAdded" xml:space="preserve">
    <value>New Supplier Invoice Added</value>
    <comment>Used in Mail Message Subjects</comment>
  </data>
  <data name="SupplierInvoiceLineAddMessage" xml:space="preserve">
    <value>Please select atleast one line</value>
  </data>
  <data name="NotifySupplierInvoice" xml:space="preserve">
    <value>Supplier Invoice {0} Notification</value>
  </data>
  <data name="Document" xml:space="preserve">
    <value>Sorry, that document cannot be found or you do not have the permissions to see it.</value>
  </data>
  <data name="String" xml:space="preserve">
    <value>Document</value>
  </data>
  <data name="InvoiceAmountMessage" xml:space="preserve">
    <value>Invoice amount is not correct</value>
  </data>
  <data name="NarrativeMessage" xml:space="preserve">
    <value>Narrative should be less than 41 characters</value>
  </data>
  <data name="SecondRefMessage" xml:space="preserve">
    <value>SecondRef should be less than 16 characters</value>
  </data>
  <data name="SupplierCodeMessage" xml:space="preserve">
    <value>Please provide supplier code</value>
  </data>
  <data name="TaxValueMessage" xml:space="preserve">
    <value>Tax value does not match with the Tax type selected, do you want to continue saving.</value>
  </data>
  <data name="NotifyNPR" xml:space="preserve">
    <value>NPR  {0} Notification</value>
  </data>
  <data name="Salesman2Message" xml:space="preserve">
    <value>Please select additional salesperson</value>
  </data>
  <data name="NonPreferred" xml:space="preserve">
    <value>Non Preferred Source</value>
  </data>
  <data name="Salesman2PercentMessage" xml:space="preserve">
    <value>Please enter additional salesperson %</value>
  </data>
  <data name="ProductSourceMessage" xml:space="preserve">
    <value>Please select product source</value>
  </data>
  <data name="SalesPersonCompare" xml:space="preserve">
    <value>Salesperson and Additional Salesperson can not be same</value>
  </data>
  <data name="Traceable" xml:space="preserve">
    <value>Traceable Source</value>
  </data>
  <data name="ShippedQuantityMessage" xml:space="preserve">
    <value>Order quantity cannot be less than shipped quantity</value>
  </data>
  <data name="Trusted" xml:space="preserve">
    <value>Trusted Source</value>
  </data>
  <data name="AS9120AllLineMessage" xml:space="preserve">
    <value>You have selected AS9120 required. Please select a product source for each line to release this document</value>
  </data>
  <data name="AS9120LineMessage" xml:space="preserve">
    <value>You have selected AS9120 required. Please select a product source for line to release this document</value>
  </data>
  <data name="CustomerMessage" xml:space="preserve">
    <value>Customer/Supplier name cannot exceed more than 30 characters. Please edit the customer/supplier name manually to proceed.</value>
  </data>
  <data name="LabelPrintMessage" xml:space="preserve">
    <value>Your label has been sent for printing</value>
  </data>
  <data name="EARIReported" xml:space="preserve">
    <value>ERAI Reported</value>
  </data>
  <data name="ROHS2" xml:space="preserve">
    <value>ROHS2</value>
  </data>
  <data name="ROHS56" xml:space="preserve">
    <value>RoHS 5/6</value>
  </data>
  <data name="ROHS66" xml:space="preserve">
    <value>RoHS 6/6</value>
  </data>
  <data name="QuoteAllProduct" xml:space="preserve">
    <value>Some lines missing the product type please check and try again</value>
  </data>
  <data name="QuoteProduct" xml:space="preserve">
    <value>Please select a product type</value>
  </data>
  <data name="CompanyApproveSubject" xml:space="preserve">
    <value>Company has been approved</value>
  </data>
  <data name="QualityApproval" xml:space="preserve">
    <value>Quality approval already exist</value>
  </data>
  <data name="landedCostValidation1" xml:space="preserve">
    <value>Please enter a valid landed cost</value>
  </data>
  <data name="landedCostValidation2" xml:space="preserve">
    <value>Please enter or calculate new landed cost</value>
  </data>
  <data name="landedCostValidation3" xml:space="preserve">
    <value>New landed cost should be greater than or equal to 0</value>
  </data>
  <data name="landedCostValidation4" xml:space="preserve">
    <value>New landed cost should be less than current landed cost</value>
  </data>
  <data name="LotStockProvisionMessage" xml:space="preserve">
    <value>Please calculate new stock provision</value>
  </data>
  <data name="ResReason1Value" xml:space="preserve">
    <value>Please select Reason1 Value.</value>
  </data>
  <data name="NotifyEPR" xml:space="preserve">
    <value>EPR  {0} Notification</value>
  </data>
  <data name="RequestToPurchaseHubSavedSuccessfully" xml:space="preserve">
    <value>HUBRFQ has been sent for Price Request successfully.</value>
  </data>
  <data name="ChangesSavedSuccessfully" xml:space="preserve">
    <value>Your changes were saved successfully</value>
  </data>
  <data name="BOMNotificationSentSuccess" xml:space="preserve">
    <value>HUBRFQ notification sent successfully</value>
  </data>
  <data name="PONotificationSentSuccess" xml:space="preserve">
    <value>Price Request notification sent successfully</value>
  </data>
  <data name="HUBRFQAssigned" xml:space="preserve">
    <value>Selected HUBRFQ has been assigned</value>
  </data>
  <data name="BankPOFeeMessage" xml:space="preserve">
    <value>PO bank fee required if apply po bank fee checked</value>
  </data>
  <data name="CustomerPart" xml:space="preserve">
    <value>Customer Part cannot exceed more than 30 characters. Please edit the Customer Part manually to proceed.</value>
  </data>
  <data name="DateCode" xml:space="preserve">
    <value>Date Code cannot exceed more than 9 characters. Please edit the date code manually to proceed.</value>
  </data>
  <data name="DatePicked" xml:space="preserve">
    <value>Date picked cannot exceed more than 8 characters. Please edit the date picked manually to proceed.</value>
  </data>
  <data name="InspectedBy" xml:space="preserve">
    <value>Inspected by cannot exceed more than 12 characters. Please edit the inspected by manually to proceed.</value>
  </data>
  <data name="Manufacturer" xml:space="preserve">
    <value>Manufacturer cannot exceed more than 30 characters. Please edit the manufacturer manually to proceed.</value>
  </data>
  <data name="MSLLevel" xml:space="preserve">
    <value>MSL Level cannot exceed more than 6 characters. Please edit the MSL Level manually to proceed.</value>
  </data>
  <data name="Part" xml:space="preserve">
    <value>Part number cannot exceed more than 30 characters. Please edit the part number manually to proceed.</value>
  </data>
  <data name="Quantity" xml:space="preserve">
    <value>Quantity cannot exceed more than 10 characters. Please edit the quantity manually to proceed.</value>
  </data>
  <data name="SalesOrder" xml:space="preserve">
    <value>SalesOrderNo cannot exceed more than 6 characters. Please edit the sales order number manually to proceed.</value>
  </data>
  <data name="ROHS" xml:space="preserve">
    <value>ROHS Status cannot exceed more than 14 characters. Please edit the ROHS Status manually to proceed.</value>
  </data>
  <data name="Date" xml:space="preserve">
    <value>Date cannot exceed more than 10 characters. Please edit the date manually to proceed.</value>
  </data>
  <data name="GIN" xml:space="preserve">
    <value>GIN cannot exceed more than 9 characters. Please edit the GIN manually to proceed.</value>
  </data>
  <data name="GoodsInNo" xml:space="preserve">
    <value>GoodsInNo cannot exceed more than 8 characters. Please edit the GoodsInNo manually to proceed.</value>
  </data>
  <data name="Location" xml:space="preserve">
    <value>Location cannot exceed more than 8 characters. Please edit the location manually to proceed.</value>
  </data>
  <data name="NPRNo" xml:space="preserve">
    <value>NPRNo cannot exceed more than 6 characters. Please edit the NPRNo manually to proceed.</value>
  </data>
  <data name="IPOAllocateMessage" xml:space="preserve">
    <value>You are trying to allocate a non IPO stock line to an IPO sales order. If you continue, this sales order line will be marked as a non IPO sales order line which cannot be undone. Would you like to proceed ?</value>
  </data>
  <data name="ConsolidateSOConfirmMSG" xml:space="preserve">
    <value>Click OK button to print consolidate sales order on fields Price,Part,Manufacturer,Package,Product,
DatePromised,CustomerPart,Date Code,ROHS and ProductSource
Click Cancel to print sales order without consolidate</value>
  </data>
  <data name="ConsolidateMailSOConfirmMSG" xml:space="preserve">
    <value>Click OK button to email consolidate sales order on fields Price,Part,Manufacturer,Package,Product,
DatePromised,CustomerPart,Date Code,ROHS and ProductSource
Click Cancel to email sales order without consolidate</value>
  </data>
  <data name="IPOCreationMsg" xml:space="preserve">
    <value>Thank you for your order. Please note your order will not be processed until your Sales Order is checked</value>
  </data>
  <data name="ShowInspect" xml:space="preserve">
    <value>This line cannot be released until all serial numbers are recorded</value>
  </data>
  <data name="SerialNoLimit" xml:space="preserve">
    <value>Cannot insert Serial Numbers beyond Quantity limit</value>
  </data>
  <data name="Important" xml:space="preserve">
    <value>Imp !</value>
  </data>
  <data name="SupplierWarranty" xml:space="preserve">
    <value>Please enter supplier warranty</value>
  </data>
  <data name="QuoteAllMSL" xml:space="preserve">
    <value>Some lines missing MSL please check and try again</value>
  </data>
  <data name="HazardousMessage" xml:space="preserve">
    <value>This product may have a dangerous Goods classification when transported. The consignment may need to be accompanied by transport documents, declaring the description and nature of the goods</value>
  </data>
  <data name="HoldReasonMessage" xml:space="preserve">
    <value>Please select hold reason</value>
  </data>
  <data name="AddNewCustomerRequirement" xml:space="preserve">
    <value>I have added a &lt;a href="[#HYPERLINK#]"&gt;new Customer Requirement&lt;/a&gt;:

Number: [#CUSREQ_NUMBER#]
Customer: [#CUSTOMER#]
Quantity: [#QUANTITY#]
Part No: [#PART#]
Cust Part No: [#CUSTOMERPART#]
Manufacturer:	[#MANUFACTURER#]
DateCode: [#DATECODE#]
Product: [#PRODUCT#]
Package: [#PACKAGE#]
Target Price: [#PRICE#]
Date Required: [#DATEREQUIRED#]</value>
    <comment>Used in Mail Message Body</comment>
  </data>
  <data name="InsuredCurrencyAmount" xml:space="preserve">
    <value>Please select a Insured  amount currency</value>
  </data>
  <data name="OrderViaIPOonlyMessage" xml:space="preserve">
    <value>This product is known to have potential purchasing risks associated to it and as such you are requested to purchase directly from the IPO team only.</value>
  </data>
  <data name="SUpplierApprovalMailMessage" xml:space="preserve">
    <value>Dear Supplier
Please find attached a copy of Rebound terms and conditions and ensure that all online orders are processed in accordance with them.

Kind regards</value>
  </data>
  <data name="SUpplierApprovalMailSubject" xml:space="preserve">
    <value>Terms And Conditions</value>
  </data>
  <data name="ShipFreightChargeAndShippingChanged" xml:space="preserve">
    <value>Freight charge and shipping cost changed.
Related Division header, Tax and Incoterms also changed.</value>
  </data>
  <data name="ShipFreightChargeWillBeChanged" xml:space="preserve">
    <value>Freight charge will be changed..
Related Division header, Tax and Incoterms also changed.</value>
  </data>
  <data name="GIPluseShipValueMessage" xml:space="preserve">
    <value>Invoice Amount value does not match with the selected GI value and Ship value, do you want to continue saving.</value>
  </data>
  <data name="InvoiceAmountnotEqualToGoodsValueMessage" xml:space="preserve">
    <value>Invoice Amount value not equal to Sum of : Goods Value , Tax , Delivery Charge , Bank Fee , Credit Card Fee , do you want to continue!</value>
  </data>
  <data name="CreditProgressMessage" xml:space="preserve">
    <value>Selected Credit Note is in progress to send.</value>
  </data>
  <data name="DebitProgressMessage" xml:space="preserve">
    <value>Selected Debit Note is in progress to send.</value>
  </data>
  <data name="CreditFinanceContactNotFoundMessage" xml:space="preserve">
    <value>Finance contact is not found in the following credit notes:</value>
  </data>
  <data name="DebitFinanceContactNotFoundMessage" xml:space="preserve">
    <value>Finance contact is not found in the following debit notes:</value>
  </data>
  <data name="InvoiceAmountnotEqualToGoodsValueMessageV1" xml:space="preserve">
    <value>Invoice Amount value not equal to Sum of : Goods Value, Tax, Delivery Charge, Bank Fee. Do you want to continue!</value>
  </data>
  <data name="DebitNoteRefMessage" xml:space="preserve">
    <value>Debit Note Ref should be less than 16 characters</value>
  </data>
  <data name="QuoteMinReminderDate" xml:space="preserve">
    <value>Please set the Reminder Date to be 3 days after the Offered Date for pending Offered Quotes</value>
  </data>
</root>