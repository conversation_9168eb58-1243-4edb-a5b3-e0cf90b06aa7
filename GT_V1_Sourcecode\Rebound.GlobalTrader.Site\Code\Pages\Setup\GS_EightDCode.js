Type.registerNamespace("Rebound.GlobalTrader.Site.Pages.Setup");Rebound.GlobalTrader.Site.Pages.Setup.GS_EightDCode=function(n){Rebound.GlobalTrader.Site.Pages.Setup.GS_EightDCode.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Pages.Setup.GS_EightDCode.prototype={get_ctlEightDCode:function(){return this._ctlEightDCode},set_ctlEightDCode:function(n){this._ctlEightDCode!==n&&(this._ctlEightDCode=n)},get_ctlEightDSubCategory:function(){return this._ctlEightDSubCategory},set_ctlEightDSubCategory:function(n){this._ctlEightDSubCategory!==n&&(this._ctlEightDSubCategory=n)},initialize:function(){Rebound.GlobalTrader.Site.Pages.Setup.GS_EightDCode.callBaseMethod(this,"initialize")},goInit:function(){this._ctlEightDCode.addSelectCategory(Function.createDelegate(this,this.ctlEightDCode_SelectCategory));Rebound.GlobalTrader.Site.Pages.Setup.GS_EightDCode.callBaseMethod(this,"goInit")},dispose:function(){this.isDisposed||(this._ctlEightDCode&&this._ctlEightDCode.dispose(),this._ctlEightDSubCategory&&this._ctlEightDSubCategory.dispose(),this._ctlEightDCode=null,this._ctlEightDSubCategory=null,Rebound.GlobalTrader.Site.Pages.Setup.GS_EightDCode.callBaseMethod(this,"dispose"))},ctlEightDCode_SelectCategory:function(){this._ctlEightDSubCategory._intcategoryID=this._ctlEightDCode._intCategoryID;this._ctlEightDSubCategory._tbl.resizeColumns();this._ctlEightDSubCategory.show(!0);this._ctlEightDSubCategory.refresh()}};Rebound.GlobalTrader.Site.Pages.Setup.GS_EightDCode.registerClass("Rebound.GlobalTrader.Site.Pages.Setup.GS_EightDCode",Rebound.GlobalTrader.Site.Pages.Content,Sys.IDisposable);