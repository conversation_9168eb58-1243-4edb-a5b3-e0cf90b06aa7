using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;

namespace Rebound.GlobalTrader.Site.Controls.DropDowns.Data
{
    [WebService(Namespace = "http://tempuri.org/")]
    [WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    public class BomStatus : Rebound.GlobalTrader.Site.Data.DropDowns.Base
    {

        public override void ProcessRequest(HttpContext context)
        {
            SetDropDownType("BomStatus");
            base.ProcessRequest(context);
        }

        protected override void GetData()
        {
            JsonObject jsn = new JsonObject();
            JsonObject jsnList = new JsonObject(true);
            JsonObject jsnItem = null;

            jsnItem = new JsonObject();
            jsnItem.AddVariable("ID", (int)BOMStatus.List.New);
            jsnItem.AddVariable("Name", Functions.GetGlobalResource("misc", "New"));
            jsnList.AddVariable(jsnItem);

            jsnItem = new JsonObject();
            jsnItem.AddVariable("ID", (int)BOMStatus.List.Open);
            jsnItem.AddVariable("Name", Functions.GetGlobalResource("misc", "Open"));
            jsnList.AddVariable(jsnItem);

            jsnItem = new JsonObject();
            jsnItem.AddVariable("ID", (int)BOMStatus.List.RPQ);
            jsnItem.AddVariable("Name", Functions.GetGlobalResource("misc", "RPQ"));
            jsnList.AddVariable(jsnItem);

            jsnItem = new JsonObject();
            jsnItem.AddVariable("ID", (int)BOMStatus.List.PartialReleased);
            jsnItem.AddVariable("Name", Functions.GetGlobalResource("misc", "PartialReleased"));
            jsnList.AddVariable(jsnItem);

            jsnItem = new JsonObject();
            jsnItem.AddVariable("ID", (int)BOMStatus.List.Released);
            jsnItem.AddVariable("Name", Functions.GetGlobalResource("misc", "Released"));
            jsnList.AddVariable(jsnItem);
            //Add new code for Closed Status by Beer Singh on 25-08-2023
            jsnItem = new JsonObject();
            jsnItem.AddVariable("ID", (int)BOMStatus.List.CustomerQuoted);
            jsnItem.AddVariable("Name", Functions.GetGlobalResource("misc", "CustomerQuoted"));
            jsnList.AddVariable(jsnItem);
            //Add new code for Closed Status by Beer Singh on 25-08-2023
            jsnItem = new JsonObject();
            jsnItem.AddVariable("ID", (int)BOMStatus.List.OrderGenerated);
            jsnItem.AddVariable("Name", Functions.GetGlobalResource("misc", "OrderGenerated"));
            jsnList.AddVariable(jsnItem);
            //Add new code for Closed Status by Prakash on 07-06-2016
            jsnItem = new JsonObject();
            jsnItem.AddVariable("ID", (int)BOMStatus.List.Closed);
            jsnItem.AddVariable("Name", Functions.GetGlobalResource("misc", "Closed"));
            jsnList.AddVariable(jsnItem); 

            jsnItem.Dispose(); jsnItem = null;

            jsn.AddVariable("BomStatus", jsnList);
            jsnList.Dispose(); jsnList = null;
            OutputResult(jsn);
            jsn.Dispose(); jsn = null;
        }
    }
}
