﻿/*========================================================================================================
Task			UPDATED BY			DATE			ACTION		DESCRIPTION
[US-215876]		cuongnguyenm3		3-Jan-2025		INSERT		Insert data into table tbApplicationSetting
===========================================================================================================
*/
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[tbApplicationSetting] WHERE [Key] = 'SuccessfulSaveMessageTime')
BEGIN
	INSERT INTO [dbo].[tbApplicationSetting]
		([Key]
		,[Value]
		,[UpdatedBy]
		,[DLUP])
	VALUES
		('SuccessfulSaveMessageTime'
		,'1'
		,null
		,GETDATE())
END
GO

IF NOT EXISTS (SELECT 1 FROM [dbo].[tbApplicationSetting] WHERE [Key] = 'MessageCheckTimeout')
BEGIN
	INSERT INTO [dbo].[tbApplicationSetting]
		([Key]
		,[Value]
		,[UpdatedBy]
		,[DLUP])
	VALUES
		('MessageCheckTimeout'
		,'2.0'
		,null
		,GETDATE())
END

