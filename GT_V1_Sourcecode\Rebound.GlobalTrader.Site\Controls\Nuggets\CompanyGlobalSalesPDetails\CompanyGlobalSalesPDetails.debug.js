///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//
// RP 17.11.2009:
// - don't get data straight away because the Company Detail page now switches tabs
//   with javascript
//-------------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Nuggets");

Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyGlobalSalesPDetails = function(element) {
	Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyGlobalSalesPDetails.initializeBase(this, [element]);
	this._intSupplierID = -1;  
	this._blnNoData = false;
};

Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyGlobalSalesPDetails.prototype = {

	get_intSupplierID: function() { return this._intSupplierID; }, set_intSupplierID: function(v) { if (this._intSupplierID !== v) this._intSupplierID = v; },
	get_tbl: function() { return this._tbl; }, 	set_tbl: function(v) { if (this._tbl !== v)  this._tbl = v; }, 
	get_ibtnEdit: function() { return this._ibtnEdit; }, 	set_ibtnEdit: function(v) { if (this._ibtnEdit !== v)  this._ibtnEdit = v; }, 
	get_ibtnAdd: function() { return this._ibtnAdd; }, 	set_ibtnAdd: function(v) { if (this._ibtnAdd !== v)  this._ibtnAdd = v; }, 
	get_ibtnDelete: function() { return this._ibtnDelete; }, 	set_ibtnDelete: function(v) { if (this._ibtnDelete !== v)  this._ibtnDelete = v; }, 

	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyGlobalSalesPDetails.callBaseMethod(this, "initialize");
		
		//data
		this._strPathToData = "controls/Nuggets/CompanyGlobalSalesPDetails";
		this._strDataObject = "CompanyGlobalSalesPDetails";
		
		//nugget events
		this.addRefreshEvent(Function.createDelegate(this, this.getData));
		
		//add and edit form
		if (this._ibtnEdit || this._ibtnAdd) {
			if (this._ibtnEdit) $R_IBTN.addClick(this._ibtnEdit, Function.createDelegate(this, this.showEditForm));
			if (this._ibtnAdd) $R_IBTN.addClick(this._ibtnAdd, Function.createDelegate(this, this.showAddForm));
			this._frmAddEdit = $find(this._aryFormIDs[0]);
			this._frmAddEdit._intSupplierID = this._intSupplierID;
			this._frmAddEdit.addShown(Function.createDelegate(this, this.addEditFormShown));
			this._frmAddEdit.addCancel(Function.createDelegate(this, this.hideAddEditForm));
			this._frmAddEdit.addSaveComplete(Function.createDelegate(this, this.saveAddEditOK));
			this._frmAddEdit.addSaveError(Function.createDelegate(this, this.saveAddEditError));
		}
		
		//delete form
		if (this._ibtnDelete) {
			$R_IBTN.addClick(this._ibtnDelete, Function.createDelegate(this, this.showDeleteForm));
			this._frmDelete = $find(this._aryFormIDs[1]);
			this._frmDelete.addShown(Function.createDelegate(this, this.deleteFormShown));
			this._frmDelete.addNotConfirmed(Function.createDelegate(this, this.hideDeleteForm));
			this._frmDelete.addSaveComplete(Function.createDelegate(this, this.saveDeleteOK));
			this._frmDelete.addSaveError(Function.createDelegate(this, this.saveDeleteError));
		}
		
		//control events
		this._tbl.addSelectedIndexChanged(Function.createDelegate(this, this.tbl_SelectedIndexChanged));
	},

	dispose: function() { 
		if (this.isDisposed) return;
		if (this._ibtnAdd) $R_IBTN.clearHandlers(this._ibtnAdd);
		if (this._ibtnEdit) $R_IBTN.clearHandlers(this._ibtnEdit);
		if (this._ibtnDelete) $R_IBTN.clearHandlers(this._ibtnDelete);
		if (this._frmDelete) this._frmDelete.dispose();
		if (this._frmAddEdit) this._frmAddEdit.dispose();
		if (this._tbl) this._tbl.dispose();
		this._frmDelete = null;
		this._frmAddEdit = null;
		this._tbl = null;
		this._ibtnAdd = null;
		this._ibtnEdit = null;
		this._ibtnDelete = null;
		this._intSupplierID = null;
		this._blnNoData = null;
		Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyGlobalSalesPDetails.callBaseMethod(this, "dispose");
	},

	getData: function() { 
		Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyGlobalSalesPDetails.callBaseMethod(this, "getData_Start");
		this.updateButtonsEnabledState(false);	
		var obj = new Rebound.GlobalTrader.Site.Data();
		obj.set_PathToData(this._strPathToData);
		obj.set_DataObject(this._strDataObject);
		obj.set_DataAction("GetGlobalSalesPersonsDetails");
		obj.addParameter("id", this._intSupplierID);
		obj.addDataOK(Function.createDelegate(this, this.getDataComplete));
		obj.addError(Function.createDelegate(this, this.getDataError));
		obj.addTimeout(Function.createDelegate(this, this.getDataError));
		$R_DQ.addToQueue(obj);
		$R_DQ.processQueue();
		obj = null;
	},

	getDataError: function(args) {
		this.showError(true, args.get_ErrorMessage());
	},

	getDataComplete: function(args) { 
		var res = args._result;
		this._tbl.clearTable();
		this._blnNoData = true;
		if (res.Items) {
			for (var i = 0; i < res.Items.length; i++) {
				var row = res.Items[i];
				var aryData = [
					$R_FN.setCleanTextValue(row.SalesPersonName),
					$R_FN.setCleanTextValue(row.ClientName)
				];
				var objExtraData = {
					MfrName: row.CompanyNo
				};
				this._tbl.addRow(aryData, row.ID, false, objExtraData);
				row = null; aryData = null;
			}
			this._blnNoData = (res.Items.length == 0);			
		}		
		this._tbl.resizeColumns();
		this.getDataOK_End();
		this.showNoData(this._blnNoData);
	},
	
	updateButtonsEnabledState: function(blnEnable) {
		if (this._ibtnEdit) $R_IBTN.enableButton(this._ibtnEdit, blnEnable);
		if (this._ibtnDelete) $R_IBTN.enableButton(this._ibtnDelete, blnEnable);	
	},
	
	tbl_SelectedIndexChanged: function() {
		this.updateButtonsEnabledState(true);
		this._frmAddEdit._intManufacturerLinkID = this._tbl._varSelectedValue;
		this._frmDelete._intManufacturerLinkID = this._tbl._varSelectedValue;
	},
	
	showAddForm: function() {
		this._frmAddEdit.changeMode("ADD");
		this.showForm(this._frmAddEdit, true);
	},

	showEditForm: function() {
		this._frmAddEdit.changeMode("EDIT");
		this.showForm(this._frmAddEdit, true);
	},
	
	addEditFormShown: function() {
		if (this._frmAddEdit._mode == "ADD") {
			this._frmAddEdit._autManufacturers.reselect();
			this._frmAddEdit.setFieldValue("ctlRating", 0);
		} else {
			this._frmAddEdit.setFieldValue("ctlManufacturerSelected", this._tbl.getSelectedExtraData().MfrName);
			this._frmAddEdit.setFieldValue("ctlRating", this._tbl.getSelectedExtraData().Rating);
		}
	},

	hideAddEditForm: function() {
		this.showForm(this._frmAddEdit, false);
		this.showNoData(this._blnNoData);
	},

	saveAddEditOK: function() {
		this.hideAddEditForm();
		this.showSavedOK(true, $R_RES.ChangesSavedSuccessfully);
		this.getData();
	},

	saveAddEditError: function(args) {
		this.showError(true, this._frmAddEdit._strErrorMessage);
	},
	
	showDeleteForm: function() {
		this.showForm(this._frmDelete, true);
	},
	
	hideDeleteForm: function() {
		this.showForm(this._frmDelete, false);
		this.showNoData(this._blnNoData);
	},
	
	saveDeleteOK: function() {
		this.hideDeleteForm();
		this.showSavedOK(true, $R_RES.ChangesSavedSuccessfully);
		this.getData();
	},
	
	saveDeleteError: function(args) {
		this.showError(true, this._frmDelete._strErrorMessage);
	},
	
	deleteFormShown: function() {
		this._frmDelete.setFieldValue("ctlManufacturerSelected", this._tbl.getSelectedExtraData().MfrName);
	}

};

Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyGlobalSalesPDetails.registerClass("Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyGlobalSalesPDetails", Rebound.GlobalTrader.Site.Controls.Nuggets.Base);
