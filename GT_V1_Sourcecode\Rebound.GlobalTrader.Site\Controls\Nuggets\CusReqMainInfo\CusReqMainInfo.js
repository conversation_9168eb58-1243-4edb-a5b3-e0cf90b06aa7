Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Nuggets");Rebound.GlobalTrader.Site.Controls.Nuggets.CustomerRequirementMainInfo=function(n){Rebound.GlobalTrader.Site.Controls.Nuggets.CustomerRequirementMainInfo.initializeBase(this,[n]);this._intCustomerRequirementID=-1;this._intOriginalCustomerRequirementID=-1;this._strCustomerRequirementNumber="";this._intLineCount=0;this._blnLineLoaded=!1;this._blnRequirementClosed=!1;this._intCompanyID=0;this._isPoRequest=!1;this._blnProdInactive=!1;this._salesmanNo=-1;this._altStatus=0;this._changeAltStatus=0;this._aryCheckedLineIDs=[];this._AlternateStatus=0;this._ctlCompany="";this._hidCompanyName="";this._hidCompanyID=0;this._ctlContact=-1;this._hidContactID=-1;this._ctlQuantity=0;this._ctlPartNo="";this._ctlAS6081=!1;this._ctlCustomerPart="";this._ctlManufacturer="";this._hidManufacturer="";this._hidManufacturerNo="";this._ctlDateCode="";this._ctlProduct="";this._ctlProductDis=!1;this._ctlPrdDutyCodeRate="";this._hidProductID=0;this._ctlPackage="";this._hidPackageID=-1;this._ctlTargetPrice=0;this._hidPrice=0;this._ctlCurrency="";this._hidCurrencyID=0;this._ctlDateRequired="";this._ctlUsage="";this._hidUsageID=-1;this._ctlNotes="";this._ctlInstructions="";this._ctlROHS="";this._hidROHS=0;this._ctlClosed="";this._ctlClosedReason="";this._hidDisplayStatus="";this._ctlPartWatch="";this._ctlBOM=!1;this._ctlBOMName="";this._ctlBOMHeader="";this._hidBOMID=0;this._hidBOMHeaderDisplayStatus=!1;this._ctlMSL=-1;this._ctlFactorySealed=!1;this._ctlPQA=!1;this._ctlObsolete=!1;this._ctlLastTimeBuy=!1;this._ctlRefirbsAcceptable=!1;this._ctlTestingRequired=!1;this._ctlTargetSellPrice=0;this._ctlCompetitorBestoffer="";this._ctlCustomerDecisionDate="";this._ctlRFQClosingDate="";this._ctlQuoteValidityRequiredHid="";this._ctlQuoteValidityRequired="";this._ctlTypeHid=-1;this._ctlType="";this._ctlOrderToPlace=!1;this._ctlRequirementforTraceability="";this._ctlRequirementforTraceabilityHid="";this._ctlTargetSellPriceHidden=0;this._ctlCompetitorBestofferHidden="";this._ctlEAU="";this._hidCustGCNo=null;this._ctlAlternativesAccepted=!1;this._ctlRepeatBusiness=!1;this._blnProdInactive=!1;this._strhidMSL=0;this._ctlSalespersion=null;this._hidSalesPersion=null;this._hidCountryOfOrigin="";this._hidCountryOfOriginNo=0;this._hidLifeCycleStage="";this._hidHTSCode="";this._hidAveragePrice=0;this._hidPackaging="";this._hidPackagingSize="";this._IsPOHub=!1;this._IHSProductNo=0;this._IHSProduct="";this._IHSHTSCode="";this._IHSDutyCode="";this._ECCNCode="";this._blnIsRestMFR=!1;this._PartEditStatus=0;this._ibtnReqEccnPrint=null;this._ctlAS6081="0";this.handlerUrl=window.location.origin+"/controls/Nuggets/CusReqAdd/CusReqAdd.ashx"};Rebound.GlobalTrader.Site.Controls.Nuggets.CustomerRequirementMainInfo.prototype={get_intCustomerRequirementID:function(){return this._intCustomerRequirementID},set_intCustomerRequirementID:function(n){this._intCustomerRequirementID!==n&&(this._intCustomerRequirementID=n)},get_intOriginalCustomerRequirementID:function(){return this._intOriginalCustomerRequirementID},set_intOriginalCustomerRequirementID:function(n){this._intOriginalCustomerRequirementID!==n&&(this._intOriginalCustomerRequirementID=n)},get_tbl:function(){return this._tbl},set_tbl:function(n){this._tbl!==n&&(this._tbl=n)},get_strCustomerRequirementNumber:function(){return this._strCustomerRequirementNumber},set_strCustomerRequirementNumber:function(n){this._strCustomerRequirementNumber!==n&&(this._strCustomerRequirementNumber=n)},get_ibtnAdd:function(){return this._ibtnAdd},set_ibtnAdd:function(n){this._ibtnAdd!==n&&(this._ibtnAdd=n)},get_ibtnAddAll:function(){return this._ibtnAddAll},set_ibtnAddAll:function(n){this._ibtnAddAll!==n&&(this._ibtnAddAll=n)},get_ibtnEdit:function(){return this._ibtnEdit},set_ibtnEdit:function(n){this._ibtnEdit!==n&&(this._ibtnEdit=n)},get_ibtnQuote:function(){return this._ibtnQuote},set_ibtnQuote:function(n){this._ibtnQuote!==n&&(this._ibtnQuote=n)},get_ibtnClose:function(){return this._ibtnClose},set_ibtnClose:function(n){this._ibtnClose!==n&&(this._ibtnClose=n)},get_hypPrev:function(){return this._hypPrev},set_hypPrev:function(n){this._hypPrev!==n&&(this._hypPrev=n)},get_hypNext:function(){return this._hypNext},set_hypNext:function(n){this._hypNext!==n&&(this._hypNext=n)},get_lblLineTitle:function(){return this._lblLineTitle},set_lblLineTitle:function(n){this._lblLineTitle!==n&&(this._lblLineTitle=n)},get_pnlLineDetail:function(){return this._pnlLineDetail},set_pnlLineDetail:function(n){this._pnlLineDetail!==n&&(this._pnlLineDetail=n)},get_pnlLoadingLineDetail:function(){return this._pnlLoadingLineDetail},set_pnlLoadingLineDetail:function(n){this._pnlLoadingLineDetail!==n&&(this._pnlLoadingLineDetail=n)},get_pnlLineDetailError:function(){return this._pnlLineDetailError},set_pnlLineDetailError:function(n){this._pnlLineDetailError!==n&&(this._pnlLineDetailError=n)},get_ibtnReqPrint:function(){return this._ibtnReqPrint},set_ibtnReqPrint:function(n){this._ibtnReqPrint!==n&&(this._ibtnReqPrint=n)},get_ibtnReqEccnPrint:function(){return this._ibtnReqEccnPrint},set_ibtnReqEccnPrint:function(n){this._ibtnReqEccnPrint!==n&&(this._ibtnReqEccnPrint=n)},get_ibtnExportPurchaseHUB:function(){return this._ibtnExportPurchaseHUB},set_ibtnExportPurchaseHUB:function(n){this._ibtnExportPurchaseHUB!==n&&(this._ibtnExportPurchaseHUB=n)},get_ibtnViewTree:function(){return this._ibtnViewTree},set_ibtnViewTree:function(n){this._ibtnViewTree!==n&&(this._ibtnViewTree=n)},get_intCompanyID:function(){return this._intCompanyID},set_intCompanyID:function(n){this._intCompanyID!==n&&(this._intCompanyID=n)},get_ibtnMarkPossible:function(){return this._ibtnMarkPossible},set_ibtnMarkPossible:function(n){this._ibtnMarkPossible!==n&&(this._ibtnMarkPossible=n)},get_ibtnDelete:function(){return this._ibtnDelete},set_ibtnDelete:function(n){this._ibtnDelete!==n&&(this._ibtnDelete=n)},get_ibtnPrintLabel:function(){return this._ibtnPrintLabel},set_ibtnPrintLabel:function(n){this._ibtnPrintLabel!==n&&(this._ibtnPrintLabel=n)},get_pnlLabelTootTip:function(){return this._pnlLabelTootTip},set_pnlLabelTootTip:function(n){this._pnlLabelTootTip!==n&&(this._pnlLabelTootTip=n)},get_pnlLabel:function(){return this._pnlLabel},set_pnlLabel:function(n){this._pnlLabel!==n&&(this._pnlLabel=n)},get_hypCloneHUBRFQ:function(){return this._hypCloneHUBRFQ},set_hypCloneHUBRFQ:function(n){this._hypCloneHUBRFQ!==n&&(this._hypCloneHUBRFQ=n)},get_hypCloneHUB:function(){return this._hypCloneHUB},set_hypCloneHUB:function(n){this._hypCloneHUB!==n&&(this._hypCloneHUB=n)},get_IsDiffrentClient:function(){return this._IsDiffrentClient},set_IsDiffrentClient:function(n){this._IsDiffrentClient!==n&&(this._IsDiffrentClient=n)},get_IsGSAEditPermission:function(){return this._IsGSAEditPermission},set_IsGSAEditPermission:function(n){this._IsGSAEditPermission!==n&&(this._IsGSAEditPermission=n)},get_IsGSA:function(){return this._IsGSA},set_IsGSA:function(n){this._IsGSA!==n&&(this._IsGSA=n)},addStartGetData:function(n){this.get_events().addHandler("StartGetData",n)},removeStartGetData:function(n){this.get_events().removeHandler("StartGetData",n)},onStartGetData:function(){var n=this.get_events().getHandler("StartGetData");n&&n(this,Sys.EventArgs.Empty)},addGotDataOK:function(n){this.get_events().addHandler("GotDataOK",n)},removeGotDataOK:function(n){this.get_events().removeHandler("GotDataOK",n)},onGotDataOK:function(){var n=this.get_events().getHandler("GotDataOK");n&&n(this,Sys.EventArgs.Empty)},addPartSelected:function(n){this.get_events().addHandler("PartSelected",n)},removePartSelected:function(n){this.get_events().removeHandler("PartSelected",n)},onPartSelected:function(){var n=this.get_events().getHandler("PartSelected");n&&n(this,Sys.EventArgs.Empty)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Nuggets.CustomerRequirementMainInfo.callBaseMethod(this,"initialize");this.addRefreshEvent(Function.createDelegate(this,this.getData));this._tbl.addSelectedIndexChanged(Function.createDelegate(this,this.selectPart));this._IsDiffrentClient==!0?this._IsGSA==!0?this._IsGSAEditPermission==!0?($("#ctl00_cphMain_ctlSourcingResults_ctlDB_pnlLinks").show(),$("#ctl00_cphMain_ctlSourcing_ctlDB_ctl13_ibtnSearch").show(),$("#ctl00_cphMain_ctlSourcing_ctlDB_ctl13_ibtnAddStockInfo").show(),$("#ctl00_cphMain_ctlSourcing_ctlDB_ctl13_ibtnAddOffer").show(),$("#ctl00_cphMain_ctlSourcing_ctlDB_ctl13_ibtnAddTrusted").show(),$("#ctl00_cphMain_ctlSourcing_ctlDB_ctl13_hypNewNPR").show()):($("#ctl00_cphMain_ctlSourcingResults_ctlDB_pnlLinks").hide(),$("#ctl00_cphMain_ctlSourcing_ctlDB_ctl13_ibtnSearch").hide(),$("#ctl00_cphMain_ctlSourcing_ctlDB_ctl13_ibtnAddStockInfo").hide(),$("#ctl00_cphMain_ctlSourcing_ctlDB_ctl13_ibtnAddOffer").hide(),$("#ctl00_cphMain_ctlSourcing_ctlDB_ctl13_ibtnAddTrusted").hide(),$("#ctl00_cphMain_ctlSourcing_ctlDB_ctl13_hypNewNPR").hide()):($("#ctl00_cphMain_ctlSourcingResults_ctlDB_pnlLinks").show(),$("#ctl00_cphMain_ctlSourcing_ctlDB_ctl13_ibtnSearch").show(),$("#ctl00_cphMain_ctlSourcing_ctlDB_ctl13_ibtnAddStockInfo").show(),$("#ctl00_cphMain_ctlSourcing_ctlDB_ctl13_ibtnAddOffer").show(),$("#ctl00_cphMain_ctlSourcing_ctlDB_ctl13_ibtnAddTrusted").show(),$("#ctl00_cphMain_ctlSourcing_ctlDB_ctl13_hypNewNPR").show()):($("#ctl00_cphMain_ctlSourcingResults_ctlDB_pnlLinks").show(),$("#ctl00_cphMain_ctlSourcing_ctlDB_ctl13_ibtnSearch").show(),$("#ctl00_cphMain_ctlSourcing_ctlDB_ctl13_ibtnAddStockInfo").show(),$("#ctl00_cphMain_ctlSourcing_ctlDB_ctl13_ibtnAddOffer").show(),$("#ctl00_cphMain_ctlSourcing_ctlDB_ctl13_ibtnAddTrusted").show(),$("#ctl00_cphMain_ctlSourcing_ctlDB_ctl13_hypNewNPR").show());$addHandler(this._hypPrev,"click",Function.createDelegate(this,this.prevLine));$addHandler(this._hypNext,"click",Function.createDelegate(this,this.nextLine));this._ibtnEdit&&($R_IBTN.addClick(this._ibtnEdit,Function.createDelegate(this,this.showEditForm)),this._frmEdit=$find(this._aryFormIDs[0]),this._frmEdit._intCustomerRequirementID=this._intCustomerRequirementID,this._frmEdit.addCancel(Function.createDelegate(this,this.cancelEdit)),this._frmEdit.addSaveComplete(Function.createDelegate(this,this.saveEditComplete)));this._ibtnAdd&&($R_IBTN.addClick(this._ibtnAdd,Function.createDelegate(this,this.showAddForm)),this._frmAdd=$find(this._aryFormIDs[1]),this._frmAdd._intCustomerRequirementID=this._intCustomerRequirementID,this._frmAdd.addCancel(Function.createDelegate(this,this.cancelAdd)),this._frmAdd.addSaveComplete(Function.createDelegate(this,this.saveAddComplete)));this._ibtnAddAll&&$R_IBTN.addClick(this._ibtnAddAll,Function.createDelegate(this,this.saveAllAlternate));this._ibtnQuote&&$R_IBTN.addClick(this._ibtnQuote,Function.createDelegate(this,this.doQuote));this._ibtnClose&&($R_IBTN.addClick(this._ibtnClose,Function.createDelegate(this,this.showCloseForm)),this._frmClose=$find(this._aryFormIDs[2]),this._frmClose._intCustomerRequirementID=this._intCustomerRequirementID,this._frmClose.addCancel(Function.createDelegate(this,this.cancelClose)),this._frmClose.addNotConfirmed(Function.createDelegate(this,this.cancelClose)),this._frmClose.addSaveComplete(Function.createDelegate(this,this.saveCloseComplete)));this._ibtnExportPurchaseHUB&&($R_IBTN.addClick(this._ibtnExportPurchaseHUB,Function.createDelegate(this,this.showConfirmForm)),this._frmConfirm=$find(this._aryFormIDs[3]),this._frmConfirm._intCustomerRequirementID=this._intCustomerRequirementID,this._frmConfirm.addNotConfirmed(Function.createDelegate(this,this.cancelConfirm)),this._frmConfirm.addSaveComplete(Function.createDelegate(this,this.saveConfirmComplete)),this._frmConfirm.addCancel(Function.createDelegate(this,this.cancelConfirm)));this._ibtnDelete&&($R_IBTN.addClick(this._ibtnDelete,Function.createDelegate(this,this.showAltDeleteForm)),this._frmDelete=$find(this._aryFormIDs[4]),this._frmDelete.addCancel(Function.createDelegate(this,this.cancelAltDelete)),this._frmDelete.addNotConfirmed(Function.createDelegate(this,this.cancelAltDelete)),this._frmDelete.addSaveComplete(Function.createDelegate(this,this.saveAltDeleteComplete)));this._ibtnReqPrint&&$R_IBTN.addClick(this._ibtnReqPrint,Function.createDelegate(this,this.printCustRequirement));this._ibtnMarkPossible&&$R_IBTN.addClick(this._ibtnMarkPossible,Function.createDelegate(this,this.markedFirmAlt));this._ibtnViewTree&&$R_IBTN.addClick(this._ibtnViewTree,Function.createDelegate(this,this.OpenTree));this._ibtnReqEccnPrint&&$R_IBTN.addClick(this._ibtnReqEccnPrint,Function.createDelegate(this,this.printCustRequirementEccn));this._ibtnPrintLabel&&this._hypCloneHUBRFQ&&($addHandler(this._hypCloneHUBRFQ,"click",Function.createDelegate(this,this.showEditCloneHUBRFQForm)),this._frmEditCloneHUBRFQ=$find(this._aryFormIDs[5]),this._frmEditCloneHUBRFQ._intCustomerRequirementID=this._intCustomerRequirementID,this._frmEditCloneHUBRFQ.addCancel(Function.createDelegate(this,this.cancelEditCloneHUBRFQ)),this._frmEditCloneHUBRFQ.addSaveComplete(Function.createDelegate(this,this.saveEditCloneHUBRFQComplete)));this._ibtnPrintLabel&&this._hypCloneHUB&&($addHandler(this._hypCloneHUB,"click",Function.createDelegate(this,this.showEditCloneHUBForm)),this._frmEditCloneHUB=$find(this._aryFormIDs[6]),this._frmEditCloneHUB._intCustomerRequirementID=this._intCustomerRequirementID,this._frmEditCloneHUB.addCancel(Function.createDelegate(this,this.cancelEditCloneHUB)),this._frmEditCloneHUB.addSaveComplete(Function.createDelegate(this,this.saveEditCloneHUBComplete)));this.getData()},dispose:function(){this.isDisposed||(this._ibtnEdit&&$R_IBTN.clearHandlers(this._ibtnEdit),this._ibtnQuote&&$R_IBTN.clearHandlers(this._ibtnQuote),this._ibtnClose&&$R_IBTN.clearHandlers(this._ibtnClose),this._ibtnAdd&&$R_IBTN.clearHandlers(this._ibtnAdd),this._ibtnAddAll&&$R_IBTN.clearHandlers(this._ibtnAddAll),this._hypPrev&&$clearHandlers(this._hypPrev),this._hypNext&&$clearHandlers(this._hypNext),this._frmEdit&&this._frmEdit.dispose(),this._frmAdd&&this._frmAdd.dispose(),this._frmClose&&this._frmClose.dispose(),this._ibtnDelete&&$R_IBTN.clearHandlers(this._ibtnDelete),this._frmDelete&&this._frmDelete.dispose(),this._tbl&&this._tbl.dispose(),this._ibtnPrintLabel&&$R_IBTN.clearHandlers(this._ibtnPrintLabel),this._aryCheckedLineIDs=null,this._AlternateStatus=null,this._frmDelete=null,this._ibtnDelete=null,this._IsDiffrentClient=null,this._IsGSAEditPermission=null,this._IsGSA=null,this._frmEdit=null,this._frmAdd=null,this._frmClose=null,this._intCustomerRequirementID=null,this._strCompanyName=null,this._strContact=null,this._strddlROHS=null,this._strddlType=null,this._strQuantity=null,this._strPart=null,this._intOriginalCustomerRequirementID=null,this._tbl=null,this._strCustomerRequirementNumber=null,this._ibtnAdd=null,this._ibtnAddAll=null,this._ibtnEdit=null,this._ibtnQuote=null,this._ibtnClose=null,this._hypPrev=null,this._hypNext=null,this._lblLineTitle=null,this._pnlLineDetail=null,this._pnlLoadingLineDetail=null,this._pnlLineDetailError=null,this._blnRequirementClosed=null,this._ibtnReqPrint=null,this._blnProdInactive=null,this._salesmanNo=null,this._ibtnMarkPossible=null,this._altStatus=null,this._changeAltStatus=null,this._ctlCompany=null,this._hidCompanyName=null,this._hidCompanyID=null,this._ctlContact=null,this._hidContactID=null,this._ctlQuantity=null,this._ctlPartNo=null,this._ctlAS6081=null,this._ctlCustomerPart=null,this._ctlManufacturer=null,this._hidManufacturer=null,this._hidManufacturerNo=null,this._ctlDateCode=null,this._ctlProduct=null,this._ctlProductDis=null,this._ctlPrdDutyCodeRate=null,this._hidProductID=null,this._ctlPackage=null,this._hidPackageID=null,this._ctlTargetPrice=null,this._hidPrice=null,this._ctlCurrency=null,this._hidCurrencyID=null,this._ctlDateRequired=null,this._ctlUsage=null,this._hidUsageID=null,this._ctlNotes=null,this._ctlInstructions=null,this._ctlROHS=null,this._hidROHS=null,this._ctlClosed=null,this._ctlClosedReason=null,this._hidDisplayStatus=null,this._ctlPartWatch=null,this._ctlBOM=null,this._ctlBOMName=null,this._ctlBOMHeader=null,this._hidBOMID=null,this._hidBOMHeaderDisplayStatus=null,this._ctlMSL=null,this._ctlFactorySealed=null,this._ctlPQA=null,this._ctlObsolete=null,this._ctlLastTimeBuy=null,this._ctlRefirbsAcceptable=null,this._ctlTestingRequired=null,this._ctlTargetSellPrice=null,this._ctlCompetitorBestoffer=null,this._ctlCustomerDecisionDate=null,this._ctlRFQClosingDate=null,this._ctlQuoteValidityRequiredHid=null,this._ctlQuoteValidityRequired=null,this._ctlTypeHid=null,this._ctlType=null,this._ctlOrderToPlace=null,this._ctlRequirementforTraceability=null,this._ctlRequirementforTraceabilityHid=null,this._ctlTargetSellPriceHidden=null,this._ctlCompetitorBestofferHidden=null,this._ctlEAU=null,this._hidCustGCNo=null,this._ctlAlternativesAccepted=null,this._ctlRepeatBusiness=null,this._strhidMSL=null,this._ctlSalespersion=null,this._hidSalesPersion=null,this._hidCountryOfOrigin=null,this._hidCountryOfOriginNo=null,this._hidLifeCycleStage=null,this._hidHTSCode=null,this._hidAveragePrice=null,this._hidPackaging=null,this._hidPackagingSize=null,this._IsPOHub=null,this._IHSProductNo=null,this._IHSProduct=null,this._IHSHTSCode=null,this._IHSDutyCode=null,this._ECCNCode=null,this._ibtnPrintLabel=null,this._pnlLabelTootTip=null,this._pnlLabel=null,this._hypCloneHUBRFQ=null,this._hypCloneHUB=null,this._blnIsRestMFR=null,this._PartEditStatus=null,this._ibtnReqEccnPrint=null,Rebound.GlobalTrader.Site.Controls.Nuggets.CustomerRequirementMainInfo.callBaseMethod(this,"dispose"))},getData:function(){this.enableButtons(!1);this.getData_Start();var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/CusReqMainInfo");n.set_DataObject("CusReqMainInfo");n.set_DataAction("GetData");n.addParameter("id",this._intCustomerRequirementID);n.addParameter("CustomerRequirementNumber",this._strCustomerRequirementNumber);n.addDataOK(Function.createDelegate(this,this.getDataOK));n.addError(Function.createDelegate(this,this.getDataError));n.addTimeout(Function.createDelegate(this,this.getDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null;this.onStartGetData()},getDataOK:function(n){var r,i,f,t,e,o,u;for(this._ibtnDelete&&$R_IBTN.enableButton(this._ibtnDelete,!1),r=n._result,this._tbl.clearTable(),i=0,f=r.Items.length;i<f;i++)t=r.Items[i],this._AlternateStatus=t.AlternateStatus,e=[this.writeCheckbox(t.ID,i,this._tbl),$R_FN.writeDoubleCellValue($R_FN.writePartNo(t.Part,t.ROHS),$R_FN.setCleanTextValue(t.CustomerPart)),$R_FN.writeDoubleCellValue($RGT_nubButton_Manufacturer(t.MfrNo,t.Mfr,t.MfrAdvisoryNotes),$R_FN.setCleanTextValue(t.DC)),$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(t.Product),$R_FN.setCleanTextValue(t.Package)),$R_FN.writeDoubleCellValue(t.Quantity,$R_FN.setCleanTextValue(t.Date)),$R_FN.writeDoubleCellValue(t.Price,t.PriceInBase)],t.Alt||(this._intOriginalCustomerRequirementID=t.ID),o={Part:$R_FN.setCleanTextValue(t.PartNo),Closed:t.Closed},this._tbl.addRow(e,t.ID,t.ID==this._intCustomerRequirementID,o,t.Alt?"cusReqAlternatePart":"cusReqMainPart"),this.registerCheckBox(t.ID,i,!1,!0,this._tbl),u=this.getCheckBox(i,this._tbl),u._element.setAttribute("onClick",String.format('$find("{0}").getCheckedCellValue({1},{2});',this._element.id,i,t.ID)),$("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl13_tbl_chkImg0").removeClass("on"),$("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl13_tbl_chkImg0").removeClass("off"),$("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl13_tbl_chkImg0").hide(),this.StartRefreshLylicaAPIData(t.Part,t.Mfr,t.MfrNo),u=null,t=null;this._intLineCount=r.Items.length;this._tbl.resizeColumns();this.setFieldValue("hidDisplayStatus",$R_FN.setCleanTextValue(r.DisplayStatus));this.enableButtons(!0);this.getDataOK_End();this._AlternateStatus!="undefined"&&this._AlternateStatus>0||this._ibtnDelete&&$R_IBTN.enableButton(this._ibtnDelete,!1)},getDataError:function(n){this.showError(!0,n.get_ErrorMessage())},getCheckedCellValue:function(n,t){var i=this.getCheckBox(n,this._tbl),r=i._blnChecked,u=this._tbl._tbl.rows[n];u&&(r==!0?Array.add(this._aryCheckedLineIDs,t):Array.remove(this._aryCheckedLineIDs,t),this._aryCheckedLineIDs.length>0?$R_IBTN.enableButton(this._ibtnDelete,!0):$R_IBTN.enableButton(this._ibtnDelete,!1))},writeCheckbox:function(n,t,i){var r=this.getControlID("chk",t,i),u=this.getControlID("chkImg",t,i);return String.format('<div class="imageCheckBoxDisabled"   name="select_altranativepart" id="{0}" ><img id="{1}" class="{2}" src="images/x.gif" style="border-width: 0px;cursor: alias;" /> <\/div>',r,u,"off")},getControlID:function(n,t,i){return String.format("{0}_{1}{2}",i._element.id,n,t)},getCheckBox:function(n,t){return $find(this.getControlID("chk",n,t))},registerCheckBox:function(n,t,i,r,u){var e=this.getControlID("chk",t,u),o=this.getControlID("chkImg",t,u),f=this.getCheckBox(t,u);f&&(f.dispose(),f=null);eval($R_FN.getCreateStatement("Rebound.GlobalTrader.Site.Controls.ImageCheckBox",[["blnChecked",i],["blnEnabled",r],["img",String.format('$get("{0}")',o)]],e))},doQuote:function(){if(this.clearMessages(),this.getFieldValue("ctlMSL")==="")return this.addMessage("Kindly update MSL",$R_ENUM$MessageTypeList.Error),!1;location.href=$RGT_gotoURL_QuoteAdd(null,null,this._intCustomerRequirementID,null)},setToolTipLabelLocation:function(){this._pnlLabelTootTip&&(this._ibtnPrintLabel&&(this._pnlLabelTootTip.style.top=String.format("{0}px",Sys.UI.DomElement.getBounds(this._ibtnPrintLabel).y-Sys.UI.DomElement.getBounds(this._pnlLabelTootTip).y+15)),this._ibtnPrintLabel&&(this._pnlLabelTootTip.style.left=String.format("{0}px",Sys.UI.DomElement.getBounds(this._ibtnPrintLabel).x-Sys.UI.DomElement.getBounds(this._pnlLabelTootTip).x)))},showLabelTopIcons:function(){clearTimeout(this._intTimeout);$R_FN.showElement(this._pnlLabelTootTip,!0);this._pnlLabelTootTip.style.top="0px";this._pnlLabelTootTip.style.left="0px";this.setToolTipLabelLocation()},hideLabelTopIcons:function(){clearTimeout(this._intTimeout);this._intTimeout=setTimeout(Function.createDelegate(this,this.finishHideLabelTopIcons),100)},finishHideLabelTopIcons:function(){$R_FN.showElement(this._pnlLabelTootTip,!1)},enableButtons:function(n){n?(this._ibtnClose&&$R_IBTN.enableButton(this._ibtnClose,!this._blnRequirementClosed&&this._blnLineLoaded),this._ibtnAdd&&$R_IBTN.enableButton(this._ibtnAdd,!this._blnRequirementClosed&&this._blnLineLoaded),this._ibtnAddAll&&$R_IBTN.enableButton(this._ibtnAddAll,!this._blnRequirementClosed&&this._blnLineLoaded),this._ibtnEdit&&$R_IBTN.enableButton(this._ibtnEdit,!this._blnRequirementClosed&&this._blnLineLoaded),this._ibtnQuote&&$R_IBTN.enableButton(this._ibtnQuote,!this._blnRequirementClosed&&!this._blnIsRestMFR&&this._blnLineLoaded),this._ibtnQuote&&$R_IBTN.enableButton(this._ibtnMarkPossible,!this._blnRequirementClosed&&this._blnLineLoaded&&(this._altStatus==2||this._altStatus==3)),this._ibtnPrintLabel&&($R_IBTN.enableButton(this._ibtnPrintLabel,!this._blnRequirementClosed&&this._blnLineLoaded),!this._blnRequirementClosed&&this._blnLineLoaded&&$addHandler(this._ibtnPrintLabel,"mouseover",Function.createDelegate(this,this.showLabelTopIcons)),!this._blnRequirementClosed&&this._blnLineLoaded&&$addHandler(this._pnlLabelTootTip,"mouseover",Function.createDelegate(this,this.showLabelTopIcons)),!this._blnRequirementClosed&&this._blnLineLoaded&&$addHandler(this._ibtnPrintLabel,"mouseout",Function.createDelegate(this,this.hideLabelTopIcons)),!this._blnRequirementClosed&&this._blnLineLoaded&&$addHandler(this._pnlLabelTootTip,"mouseout",Function.createDelegate(this,this.hideLabelTopIcons)))):(this._ibtnAdd&&$R_IBTN.enableButton(this._ibtnAdd,!1),this._ibtnAddAll&&$R_IBTN.enableButton(this._ibtnAddAll,!1),this._ibtnEdit&&$R_IBTN.enableButton(this._ibtnEdit,!1),this._ibtnQuote&&$R_IBTN.enableButton(this._ibtnQuote,!1),this._ibtnClose&&$R_IBTN.enableButton(this._ibtnClose,!1),this._ibtnQuote&&$R_IBTN.enableButton(this._ibtnMarkPossible,!1),this._ibtnPrintLabel&&$R_IBTN.enableButton(this._ibtnPrintLabel,!1))},getBomNo:function(){var n=new Rebound.GlobalTrader.Site.Data;this.showLoading(!0);$R_FN.showElement(this._pnlLineDetail,!1);$R_FN.showElement(this._pnlLoadingLineDetail,!0);$R_FN.showElement(this._pnlLineDetailError,!1);n.set_PathToData("controls/Nuggets/CusReqMainInfo");n.set_DataObject("CusReqMainInfo");n.set_DataAction("GetBomID");n.addParameter("id",this._intCustomerRequirementID);n.addParameter("CustomerRequirementNumber",this._strCustomerRequirementNumber);n.addDataOK(Function.createDelegate(this,this.getBomDataOK));n.addError(Function.createDelegate(this,this.getLineDataError));n.addTimeout(Function.createDelegate(this,this.getLineDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getFormControlID:function(n,t){return String.format("{0}_{1}",n,t)},getBomDataOK:function(n){var t=n._result;this._frmEdit._intBOMID=t.BOMNo;this._frmEdit._isPoRequest=this._isPoRequest;this._frmEdit._ctlCompany=this.getFieldValue("hidCompanyName");this._frmEdit._ctlContact=this.getFieldValue("hidContactName");this._frmEdit._salesmanNo=this.getFieldValue("hidSalesman");this._frmEdit._ctlROHS=this.getFieldValue("hidROHS");this._frmEdit._ctlQuantity=this.getFieldValue("ctlQuantity");this._frmEdit._ctlPartNo=this.getFieldValue("ctlPartNo");this._frmEdit._ctlAS6081=this._ctlAS6081=="0"?"2":"1";this._frmEdit._ctlCustomerPart=this.getFieldValue("ctlCustomerPart");this._frmEdit._ctlManufacturer=this._ctlManufacturer;this._frmEdit._hidManufacturer=this._hidManufacturer;this._frmEdit._hidManufacturerNo=this._hidManufacturerNo;this._frmEdit._ctlDateCode=this.getFieldValue("ctlDateCode");this._frmEdit._ctlProduct=this.getFieldValue("ctlProduct");this._frmEdit._hidProductID=this.getFieldValue("hidProductID");this._frmEdit._ctlProductDis=this._ctlProductDis;this._frmEdit._ctlPrdDutyCodeRate=this._ctlPrdDutyCodeRate;this._frmEdit._ctlPackage=this._ctlPackage;this._frmEdit._hidPackageID=this.getFieldValue("hidPackageID");this._frmEdit._ctlTargetPrice=this._ctlTargetPrice;this._frmEdit._hidPrice=this.getFieldValue("hidPrice");this._frmEdit._ctlCurrency=this._ctlCurrency;this._frmEdit._hidCurrencyID=this.getFieldValue("hidCurrencyID");this._frmEdit._ctlDateRequired=this.getFieldValue("ctlDateRequired");this._frmEdit._ctlUsage=this._ctlUsage;this._frmEdit._hidUsageID=this.getFieldValue("hidUsageID");this._frmEdit._ctlNotes=this.getFieldValue("ctlNotes");this._frmEdit._ctlInstructions=this.getFieldValue("ctlInstructions");this._frmEdit._hidROHS=this._hidROHS;this._frmEdit._ctlClosed=this._ctlClosed;this._frmEdit._ctlClosedReason=this._ctlClosedReason;this._frmEdit._hidDisplayStatus=this._hidDisplayStatus;this._frmEdit._ctlPartWatch=this.getFieldValue("ctlPartWatch");this._frmEdit._ctlBOM=this.getFieldValue("ctlBOM");this._frmEdit._ctlBOMName=this.getFieldValue("ctlBOMName");this._frmEdit._ctlBOMHeader=this._ctlBOMHeader;this._frmEdit._hidBOMID=this._hidBOMID;this._frmEdit._ctlMSL=this._ctlMSL;this._frmEdit._ctlFactorySealed=this.getFieldValue("ctlFactorySealed");this._frmEdit._ctlPQA=this.getFieldValue("ctlPQA");this._frmEdit._ctlTargetSellPrice=this._ctlTargetSellPrice;this._frmEdit._ctlCompetitorBestoffer=this._ctlCompetitorBestoffer;this._frmEdit._ctlCustomerDecisionDate=this._ctlCustomerDecisionDate;this._frmEdit._ctlRFQClosingDate=this._ctlRFQClosingDate;this._frmEdit._ctlQuoteValidityRequiredHid=this._ctlQuoteValidityRequiredHid;this._frmEdit._ctlQuoteValidityRequired=this._ctlQuoteValidityRequired;this._frmEdit._ctlTypeHid=this.getFieldValue("ctlTypeHid");this._frmEdit._ctlOrderToPlace=this._ctlOrderToPlace;this._frmEdit._ctlRequirementforTraceabilityHid=this.getFieldValue("ctlRequirementforTraceabilityHid");this._frmEdit._ctlTargetSellPriceHidden=this.getFieldValue("ctlTargetSellPriceHidden");this._frmEdit._ctlCompetitorBestofferHidden=this.getFieldValue("ctlCompetitorBestofferHidden");this._frmEdit._ctlEAU=this.getFieldValue("ctlEAU");this._frmEdit._hidCustGCNo=this.getFieldValue("hidCustGCNo");this._frmEdit._blnProdInactive=this._blnProdInactive;this._frmEdit._ctlSalespersion=this.getFieldValue("ctlSalespersion");this._frmEdit._hidSalesPersion=this.getFieldValue("hidSalesPersion");this._frmEdit._hidCountryOfOrigin=this._hidCountryOfOrigin;this._frmEdit._hidCountryOfOriginNo=this._hidCountryOfOriginNo;this._frmEdit._hidLifeCycleStage=this._hidLifeCycleStage;this._frmEdit._hidHTSCode=this._hidHTSCode;this._frmEdit._hidAveragePrice=this._hidAveragePrice;this._frmEdit._hidPackaging=this._hidPackaging;this._frmEdit._hidPackagingSize=this._hidPackagingSize;this._frmEdit._intCompanyID=this.getFieldValue("hidCompanyID");this._frmEdit._radioCheck=this.getFieldValue("ctlFactorySealed");this._frmEdit._radObsoleteChk=this.getFieldValue("ctlObsolete");this._frmEdit._radLastTimeBuyChk=this.getFieldValue("ctlLastTimeBuy");this._frmEdit._radRefirbsAcceptableChk=this.getFieldValue("ctlRefirbsAcceptable");this._frmEdit._radTestingRequiredChk=this.getFieldValue("ctlTestingRequired");this._frmEdit._radAlternativesAcceptedChK=this.getFieldValue("ctlAlternativesAccepted");this._frmEdit._radRepeatBusinessChk=this.getFieldValue("ctlRepeatBusiness");this._frmEdit._intCustomerRequirementID=this._intCustomerRequirementID;this._intBOMID=t.BOMNo;this._frmEdit._BOMHeaderDisplayStatus=Boolean.parse(this.getFieldValue("hidBOMHeaderDisplayStatus"));this._frmEdit._blnCurInSameFaimly=t.IsSameCurFam;this._frmEdit._intCurrencyNo=this.getFieldValue("hidCurrencyID");this._frmEdit._IHSProductNo=this._IHSProductNo;this._frmEdit._IHSProduct=this._IHSProduct;this._frmEdit._IHSHTSCode=this._IHSHTSCode;this._frmEdit._IHSDutyCode=this._IHSDutyCode;this._frmEdit._AlternateStatus=this._AlternateStatus;this._frmEdit._ECCNCode=this._ECCNCode;this._frmEdit._PartEditStatus=this._PartEditStatus;this.showLoading(!1);this.showForm(this._frmEdit,!0);this.onGotDataOK()},showEditForm:function(){this.getBomNo()},hideEditForm:function(){this.showForm(this._frmEdit,!1)},cancelEdit:function(){this.hideEditForm();this.getData()},saveEditComplete:function(){this.hideEditForm();this.showSavedOK(!0,$R_RES.ChangesSavedSuccessfully);this.onSaveEditComplete();this.getData()},selectPart:function(){this._intCustomerRequirementID=this._tbl._varSelectedValue;this._blnRequirementClosed=this._tbl.getSelectedExtraData().Closed;this.enableButtons(!0);this.getLineData();this.onPartSelected()},printCustRequirementEccn:function(){$R_FN.openPrintLogWindow($R_ENUM$PrintObject.PrintLog,this._intCustomerRequirementID,!1,"CustomerRequirementECCN")},getSelectedPartNo:function(){return this._tbl.getSelectedExtraData().Part},prevLine:function(){var n=this._tbl._intSelectedIndex-1;n<0||this._tbl.selectRow(n,!0)},nextLine:function(){var n=this._tbl._intSelectedIndex+1;n>=this._intLineCount||this._tbl.selectRow(n,!0)},getLineData:function(){this._blnLineLoaded=!1;this._altStatus=0;this.enableButtons(!1);var n=new Rebound.GlobalTrader.Site.Data;this.showLoading(!0);$R_FN.showElement(this._pnlLineDetail,!1);$R_FN.showElement(this._pnlLoadingLineDetail,!0);$R_FN.showElement(this._pnlLineDetailError,!1);n.set_PathToData("controls/Nuggets/CusReqMainInfo");n.set_DataObject("CusReqMainInfo");n.set_DataAction("GetItem");n.addParameter("id",this._intCustomerRequirementID);n.addDataOK(Function.createDelegate(this,this.getLineDataOK));n.addError(Function.createDelegate(this,this.getLineDataError));n.addTimeout(Function.createDelegate(this,this.getLineDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getLineDataOK:function(n){var t=n._result,u,f;if(this._ctlCompany=t.CustomerName,this._hidCompanyName=t.CustomerName,this._hidCompanyID=t.CustomerNo,this._ctlContact=t.Contact,this._hidContactID=t.ContactNo,this._ctlQuantity=t.Quantity,this._ctlPartNo=t.Part,this._ctlAS6081=t.AS6081,this._ctlCustomerPart=t.CustomerPart,this._ctlManufacturer=t.ManufacturerNo,this._hidManufacturer=t.ManufacturerNo,this._hidManufacturerNo=t.Manufacturer,this._ctlDateCode=t.DateCode,this._ctlProduct=t.Product,this._ctlProductDis=t.IsProdHaz,this._ctlPrdDutyCodeRate=t.DutyCodeAndRate,this._hidProductID=t.ProductNo,this._ctlPackage=t.Package,this._hidPackageID=t.PackageNo,this._ctlTargetPrice=t.Price,this._hidPrice=t.PriceRaw,this._ctlCurrency=t.Currency,this._hidCurrencyID=t.CurrencyNo,this._ctlDateRequired=t.DatePromised,this._ctlUsage=t.Usage,this._hidUsageID=t.UsageNo,this._ctlNotes=t.Notes,this._ctlInstructions=t.Instructions,this._ctlROHS=t.ROHS,this._hidROHS=t.ROHS,this._ctlClosed=t.Closed,this._ctlClosedReason=t.ClosedReason,this._hidDisplayStatus=t.DisplayStatus,this._ctlPartWatch=t.PartWatch,this._ctlBOM=t.BOM,this._ctlBOMName=t.BOMName,this._ctlBOMHeader=t.BOMHeader,this._hidBOMID=t.BOMId,this._hidBOMHeaderDisplayStatus=t.RequestToPOHubBy==null?!0:!1,this._ctlMSL=t.MSL,this._ctlFactorySealed=t.FactorySealed,this._ctlPQA=t.PQA,this._ctlObsolete=t.Obsolete,this._ctlLastTimeBuy=t.LastTimeBuy,this._ctlRefirbsAcceptable=t.RefirbsAcceptable,this._ctlTestingRequired=t.TestingRequired,this._ctlTargetSellPrice=t.TargetSellPrice,this._ctlCompetitorBestoffer=t.CompetitorBestOffer,this._ctlCustomerDecisionDate=t.CustomerDecisionDate,this._ctlRFQClosingDate=t.RFQClosingDate,this._ctlQuoteValidityRequiredHid=t.QuoteValidityRequired,this._ctlQuoteValidityRequired=t.QuoteValidityText,this._ctlTypeHid=t.Type,this._ctlType=t.ReqTypeText,this._ctlOrderToPlace=t.OrderToPlace,this._ctlRequirementforTraceability=t.ReqForTraceabilityText,this._ctlRequirementforTraceabilityHid=t.RequirementforTraceability,this._ctlTargetSellPriceHidden=t.hidTargetSellPrice,this._ctlCompetitorBestofferHidden=t.hidCompetitorBestOffer,this._ctlEAU=t.EAU,this._hidCustGCNo=t.CustGCNo,this._ctlAlternativesAccepted=t.AlternativesAccepted,this._ctlRepeatBusiness=t.RepeatBusiness,this._strhidMSL=t.MSLLevelNo,this._hidCountryOfOrigin=t.CountryOfOrigin,this._hidCountryOfOriginNo=t.CountryOfOriginNo,this._hidLifeCycleStage=t.LifeCycleStage,this._hidHTSCode=t.HTSCode,this._hidAveragePrice=t.AveragePrice,this._hidPackaging=t.Packaging,this._hidPackagingSize=t.PackagingSize,this.setFieldValue("ctlCompany",$RGT_nubButton_Company(t.CustomerNo,t.CustomerName,null,null,t.CustomerOnStop,t.CompanyAdvisoryNotes)),this.setFieldValue("ctlContact",$RGT_nubButton_Contact(t.ContactNo,t.Contact)),this.setFieldValue("hidCompanyID",t.CustomerNo),this.setFieldValue("hidCompanyName",$R_FN.setCleanTextValue(t.CustomerName)),this.setFieldValue("hidContactID",t.ContactNo),this.setFieldValue("hidContactName",$R_FN.setCleanTextValue(t.Contact)),this.setFieldValue("ctlQuantity",t.Quantity),this.setFieldValue("ctlPartNo",t.Part),this.setFieldValue("ctlAS6081",t.AS6081==!1?"No":"Yes"),t.StockAvailableDetail!=null&&t.StockAvailableDetail!=""){var r=t.StockAvailableDetail.split("-"),o=r[0],s=r[1],h=r[2],c=r[3],l=r[4],a="Whs_StockDetail.aspx?stk="+l+"";this.setFieldValue("ctlPartNoDis",$R_FN.showStockAvailableNew(t.Part,o,s,h,c,a,""))}else this.setFieldValue("ctlPartNoDis",t.Part);if(this.setFieldValue("ctlCustomerPart",$R_FN.setCleanTextValue(t.CustomerPart)),this.setFieldValue("ctlManufacturer",$RGT_nubButton_Manufacturer(t.ManufacturerNo,t.Manufacturer,t.MfrAdvisoryNotes)),this.setFieldValue("hidManufacturer",$R_FN.setCleanTextValue(t.Manufacturer)),this.setFieldValue("hidManufacturerNo",t.ManufacturerNo),this.setFieldValue("hidMfrNotes",t.MfrAdvisoryNotes),this.setFieldValue("ctlDateCode",t.DateCode),this.setFieldValue("ctlProduct",$R_FN.setCleanTextValue(t.Product)),this.setFieldValue("ctlProductDis",$R_FN.showProductWarningIndividual(t.Product,t.IsProdHaz,t.IsOrderViaIPOonly,t.IsRestrictedProduct,$R_FN.setCleanTextValue(t.MsgHazardous),$R_FN.setCleanTextValue(t.MsgIPO),$R_FN.setCleanTextValue(t.MsgRestricted))),this.setFieldValue("ctlPrdDutyCodeRate",t.DutyCodeAndRate),this.setFieldValue("hidProductID",t.ProductNo),this.setFieldValue("ctlPackage",$R_FN.setCleanTextValue(t.Package)),this.setFieldValue("hidPackageID",t.PackageNo),this.setFieldValue("ctlTargetPrice",t.Price),this.setFieldValue("hidPrice",t.PriceRaw),this.setFieldValue("ctlCurrency",$R_FN.setCleanTextValue(t.Currency)),this.setFieldValue("hidCurrencyID",t.CurrencyNo),this.setFieldValue("ctlDateRequired",t.DatePromised),this.setFieldValue("ctlUsage",$R_FN.setCleanTextValue(t.Usage)),this.setFieldValue("hidUsageID",$R_FN.setCleanTextValue(t.UsageNo)),this.setFieldValue("ctlNotes",$R_FN.setCleanTextValue(t.Notes)),this.setFieldValue("ctlInstructions",$R_FN.setCleanTextValue(t.Instructions)),this.setFieldValue("ctlROHS",$R_FN.writeROHS(t.ROHS)),this.setFieldValue("hidROHS",t.ROHS),this.setFieldValue("ctlClosed",t.Closed),this.setFieldValue("ctlClosedReason",t.ClosedReason),this.setFieldValue("hidDisplayStatus",$R_FN.setCleanTextValue(t.DisplayStatus)),this.setFieldValue("ctlPartWatch",t.PartWatch),this.setFieldValue("ctlBOM",t.BOM),this.setFieldValue("ctlBOMName",t.BOMName),this.setFieldValue("ctlBOMHeader",$RGT_nubButton_BOM(t.BOMId,$R_FN.setCleanTextValue(t.BOMHeader))),this.setFieldValue("hidBOMID",t.BOMId),this.setFieldValue("hidBOMHeaderDisplayStatus",t.RequestToPOHubBy==null?!0:!1),this.setFieldValue("ctlMSL",t.MSL),this.setFieldValue("ctlFactorySealed",t.FactorySealed),this.setFieldValue("ctlPQA",t.PQA),this.setFieldValue("ctlObsolete",t.Obsolete),this.setFieldValue("ctlLastTimeBuy",t.LastTimeBuy),this.setFieldValue("ctlRefirbsAcceptable",t.RefirbsAcceptable),this.setFieldValue("ctlTestingRequired",t.TestingRequired),this.setFieldValue("ctlTargetSellPrice",t.TargetSellPrice),this.setFieldValue("ctlCompetitorBestoffer",t.CompetitorBestOffer),this.setFieldValue("ctlCustomerDecisionDate",t.CustomerDecisionDate),this.setFieldValue("ctlRFQClosingDate",t.RFQClosingDate),this.setFieldValue("ctlQuoteValidityRequiredHid",t.QuoteValidityRequired),this.setFieldValue("ctlQuoteValidityRequired",t.QuoteValidityText),this.setFieldValue("ctlTypeHid",t.Type),this.setFieldValue("ctlType",t.ReqTypeText),this.setFieldValue("ctlOrderToPlace",t.OrderToPlace),this.setFieldValue("ctlRequirementforTraceability",t.ReqForTraceabilityText),this.setFieldValue("ctlRequirementforTraceabilityHid",t.RequirementforTraceability),this.setFieldValue("ctlTargetSellPriceHidden",t.hidTargetSellPrice),this.setFieldValue("ctlCompetitorBestofferHidden",t.hidCompetitorBestOffer),this.setFieldValue("ctlEAU",t.EAU),this.setFieldValue("hidCustGCNo",t.CustGCNo),this.setFieldValue("ctlAlternativesAccepted",t.AlternativesAccepted),this.setFieldValue("ctlRepeatBusiness",t.RepeatBusiness),this._blnProdInactive=t.ProdInactive,this.setFieldValue("hidSalesman",t.SalesmanNo),this.setFieldValue("hidMSL",t.MSLLevelNo),this.setFieldValue("ctlCountryOfOrigin",t.CountryOfOrigin),this.setFieldValue("ctlLifeCycleStage",$R_FN.showIHSstatusDefi(t.LifeCycleStage,t.IHSStatusDefination)),this.setFieldValue("ctlPackagingSize",t.PackagingSize),this.setFieldValue("ctlDescriptions",t.Descriptions),this.setFieldValue("ctlIHSProduct",t.IHSProduct),t.IHSHTSCode!=""?(this.setFieldValue("ctlHTSCode",t.HTSCode),$("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl13_ctlHTSCode_lbl").css("color","")):this.setFieldValue("ctlHTSCode",t.HTSCode),t.IHSDutyCode!=null,this._IHSProduct=t.IHSProduct,this._IHSHTSCode=t.IHSHTSCode,this._IHSDutyCode=t.IHSDutyCode,this._IsPOHub=t.IsPOHub,this._IsPOHub==!0?(this.showField("ctlAveragePrice",!1),this.setFieldValue("ctlAveragePrice",t.AveragePrice)):this.showField("ctlAveragePrice",!1),t.IHSECCNSCodeDefination!=null&&t.IHSECCNSCodeDefination!=""?this.setFieldValue("ctlECCNCode",$R_FN.showIHSECCNCodeDefi(t.ECCNCode,t.IHSECCNSCodeDefination)):this.setFieldValue("ctlECCNCode",t.ECCNCode),this._ECCNCode=t.ECCNCode,this._salesmanNo=t.SalesmanNo,this.setDLUP(t.DLUP),this.setFieldValue("ctlParentRequirementNo",$RGT_nubButton_CustomerRequirement(t.ParentRequirementId,t.ParentRequirementNo)),t.PurchasingNotes!=null&&t.PurchasingNotes!=""?this.setFieldValue("ctlPurchasingNotes",$R_FN.setCleanTextValue($R_FN.showYellowText(t.PurchasingNotes))):this.setFieldValue("ctlPurchasingNotes",$R_FN.setCleanTextValue(t.PurchasingNotes)),$R_FN.setInnerHTML(this._lblLineTitle,this._tbl.getSelectedExtraData().Part),$R_FN.showElement(this._pnlLineDetail,!0),$R_FN.showElement(this._pnlLoadingLineDetail,!1),this.showLoading(!1),this._blnLineLoaded=!0,t.AltStatus==2?(this._ibtnMarkPossible&&$R_IBTN.updateText(this._ibtnMarkPossible,"Mark as Firm Alternate"),this._changeAltStatus=3):t.AltStatus==3?(this._ibtnMarkPossible&&$R_IBTN.updateText(this._ibtnMarkPossible,"Mark as Possible Alternate"),this._changeAltStatus=2):(this._ibtnMarkPossible&&$R_IBTN.updateText(this._ibtnMarkPossible,"Mark as Firm Alternate"),this._changeAltStatus=0),this._altStatus=t.AltStatus,this._blnIsRestMFR=t.IsRestMFR,this._PartEditStatus=t.PartEditStatus,this.enableButtons(!0),this.onGotDataOK(),this.getFieldValue("hidBOMID")>0?(this._isPoRequest=Number(t.RequestToPOHubBy)>0?!0:!1,this._ibtnExportPurchaseHUB&&$R_IBTN.enableButton(this._ibtnExportPurchaseHUB,!1),this._ibtnQuote&&$R_IBTN.enableButton(this._ibtnQuote,!1),this._ibtnAdd&&$R_IBTN.enableButton(this._ibtnAdd,!t.IsPOHubReleased)):this._ibtnExportPurchaseHUB&&$R_IBTN.enableButton(this._ibtnExportPurchaseHUB,!0),(this.getFieldValue("ctlBOMHeader").indexOf("NEW")!=-1||this.getFieldValue("ctlBOMHeader").indexOf("OPEN")!=-1)&&($R_IBTN.enableButton(this._ibtnEdit,!0),$R_IBTN.enableButton(this._ibtnAdd,!0)),u="",t.SalesOrderNumber!="")for(f=t.SalesOrderNumber.split(","),i=0;i<f.length;i++){var e=f[i].split(":"),v=e[0],y=e[1];u=u+$RGT_nubButton_SalesOrder(v,y)+" "}this.setFieldValue("ctlSalesOrderNo",u);this.setFieldValue("ctlSalespersion",$R_FN.setCleanTextValue(t.SupportTeamMember));this.setFieldValue("hidSalesPersion",t.SupportTeamMemberNo);$R_FN.highlightBackgroundColorOfText("ctl00_cphMain_ctlMainInfo_ctlDB_ctl13_ctlAS6081_lbl",t.AS6081);t.AS6081?($("#ctl00_cphMain_ctlPageTitle_ctl20_lblAS6081Status").text("This part requires AS6081 compliance, please ensure the appropriate supplier is chosen to fulfil this requirement."),$R_FN.highlightBackgroundColorOfText("ctl00_cphMain_ctlPageTitle_ctl20_lblAS6081Status",t.AS6081)):$("#ctl00_cphMain_ctlPageTitle_ctl20_lblAS6081Status").text("")},getLineDataError:function(n){this.showLoading(!1);$R_FN.showElement(this._pnlLoadingLineDetail,!1);$R_FN.showElement(this._pnlLineDetailError,!0);$R_FN.setInnerHTML(this._pnlLineDetailError,n.get_ErrorMessage())},showAddForm:function(){this.getBomNoForAdd()},getBomNoForAdd:function(){var n=new Rebound.GlobalTrader.Site.Data;this.showLoading(!0);$R_FN.showElement(this._pnlLineDetail,!1);$R_FN.showElement(this._pnlLoadingLineDetail,!0);$R_FN.showElement(this._pnlLineDetailError,!1);n.set_PathToData("controls/Nuggets/CusReqMainInfo");n.set_DataObject("CusReqMainInfo");n.set_DataAction("GetBomID");n.addParameter("id",this._intCustomerRequirementID);n.addParameter("CustomerRequirementNumber",this._strCustomerRequirementNumber);n.addDataOK(Function.createDelegate(this,this.getBomNoForAddOK));n.addError(Function.createDelegate(this,this.getLineDataError));n.addTimeout(Function.createDelegate(this,this.getLineDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getBomNoForAddOK:function(n){var t=n._result;this._frmAdd._intBOMID=t.BOMNo;this._frmAdd._intOriginalCustomerRequirementID=this._intOriginalCustomerRequirementID;this._frmAdd._isPoRequest=this._isPoRequest;this._frmAdd._ctlCompany=this.getFieldValue("hidCompanyName");this._frmAdd._ctlContact=this.getFieldValue("hidContactName");this._frmAdd._salesmanNo=this.getFieldValue("hidSalesman");this._frmAdd._ctlROHS=this.getFieldValue("hidROHS");this._frmAdd._ctlQuantity=this.getFieldValue("ctlQuantity");this._frmAdd._ctlPartNo=this.getFieldValue("ctlPartNo");this._frmAdd._ctlCustomerPart=this.getFieldValue("ctlCustomerPart");this._frmAdd._ctlManufacturer=this._ctlManufacturer;this._frmAdd._hidManufacturer=this._hidManufacturer;this._frmAdd._hidManufacturerNo=this._hidManufacturerNo;this._frmAdd._ctlDateCode=this.getFieldValue("ctlDateCode");this._frmAdd._ctlProduct=this.getFieldValue("ctlProduct");this._frmAdd._hidProductID=this.getFieldValue("hidProductID");this._frmAdd._ctlProductDis=this._ctlProductDis;this._frmAdd._ctlPrdDutyCodeRate=this._ctlPrdDutyCodeRate;this._frmAdd._ctlPackage=this._ctlPackage;this._frmAdd._hidPackageID=this.getFieldValue("hidPackageID");this._frmAdd._ctlTargetPrice=this._ctlTargetPrice;this._frmAdd._hidPrice=this.getFieldValue("hidPrice");this._frmAdd._ctlCurrency=this._ctlCurrency;this._frmAdd._hidCurrencyID=this.getFieldValue("hidCurrencyID");this._frmAdd._ctlDateRequired=this.getFieldValue("ctlDateRequired");this._frmAdd._ctlUsage=this._ctlUsage;this._frmAdd._hidUsageID=this.getFieldValue("hidUsageID");this._frmAdd._ctlNotes=this.getFieldValue("ctlNotes");this._frmAdd._ctlInstructions=this.getFieldValue("ctlInstructions");this._frmAdd._hidROHS=this._hidROHS;this._frmAdd._ctlClosed=this._ctlClosed;this._frmAdd._ctlClosedReason=this._ctlClosedReason;this._frmAdd._hidDisplayStatus=this._hidDisplayStatus;this._frmAdd._ctlPartWatch=this.getFieldValue("ctlPartWatch");this._frmAdd._ctlBOM=this.getFieldValue("ctlBOM");this._frmAdd._ctlBOMName=this.getFieldValue("ctlBOMName");this._frmAdd._ctlBOMHeader=this._ctlBOMHeader;this._frmAdd._hidBOMID=this._hidBOMID;this._frmAdd._ctlMSL=this._ctlMSL;this._frmAdd._ctlFactorySealed=this.getFieldValue("ctlFactorySealed");this._frmAdd._ctlPQA=this.getFieldValue("ctlPQA");this._frmAdd._ctlTargetSellPrice=this._ctlTargetSellPrice;this._frmAdd._ctlCompetitorBestoffer=this._ctlCompetitorBestoffer;this._frmAdd._ctlCustomerDecisionDate=this._ctlCustomerDecisionDate;this._frmAdd._ctlRFQClosingDate=this._ctlRFQClosingDate;this._frmAdd._ctlQuoteValidityRequiredHid=this._ctlQuoteValidityRequiredHid;this._frmAdd._ctlQuoteValidityRequired=this._ctlQuoteValidityRequired;this._frmAdd._ctlTypeHid=this.getFieldValue("ctlTypeHid");this._frmAdd._ctlOrderToPlace=this._ctlOrderToPlace;this._frmAdd._ctlRequirementforTraceabilityHid=this.getFieldValue("ctlRequirementforTraceabilityHid");this._frmAdd._ctlTargetSellPriceHidden=this.getFieldValue("ctlTargetSellPriceHidden");this._frmAdd._ctlCompetitorBestofferHidden=this.getFieldValue("ctlCompetitorBestofferHidden");this._frmAdd._ctlEAU=this.getFieldValue("ctlEAU");this._frmAdd._hidCustGCNo=this.getFieldValue("hidCustGCNo");this._frmAdd._blnProdInactive=this._blnProdInactive;this._frmAdd._ctlSalespersion=this.getFieldValue("ctlSalespersion");this._frmAdd._hidSalesPersion=this.getFieldValue("hidSalesPersion");this._frmAdd._hidCountryOfOrigin=this._hidCountryOfOrigin;this._frmAdd._hidCountryOfOriginNo=this._hidCountryOfOriginNo;this._frmAdd._hidLifeCycleStage=this._hidLifeCycleStage;this._frmAdd._hidHTSCode=this._hidHTSCode;this._frmAdd._hidAveragePrice=this._hidAveragePrice;this._frmAdd._hidPackaging=this._hidPackaging;this._frmAdd._hidPackagingSize=this._hidPackagingSize;this._frmAdd._intCompanyID=this.getFieldValue("hidCompanyID");this._frmAdd._radioCheck=this.getFieldValue("ctlFactorySealed");this._frmAdd._radObsoleteChk=this.getFieldValue("ctlObsolete");this._frmAdd._radLastTimeBuyChk=this.getFieldValue("ctlLastTimeBuy");this._frmAdd._radRefirbsAcceptableChk=this.getFieldValue("ctlRefirbsAcceptable");this._frmAdd._radTestingRequiredChk=this.getFieldValue("ctlTestingRequired");this._frmAdd._radAlternativesAcceptedChK=this.getFieldValue("ctlAlternativesAccepted");this._frmAdd._radRepeatBusinessChk=this.getFieldValue("ctlRepeatBusiness");this._frmAdd._intCustomerRequirementID=this._intCustomerRequirementID;this._intBOMID=t.BOMNo;this._frmAdd._BOMHeaderDisplayStatus=Boolean.parse(this.getFieldValue("hidBOMHeaderDisplayStatus"));this._frmAdd._blnCurInSameFaimly=t.IsSameCurFam;this._frmAdd._intCurrencyNo=this.getFieldValue("hidCurrencyID");this._frmAdd._IHSProductNo=this._IHSProductNo;this._frmAdd._IHSProduct=this._IHSProduct;this._frmAdd._IHSHTSCode=this._IHSHTSCode;this._frmAdd._IHSDutyCode=this._IHSDutyCode;this._frmAdd._AlternateStatus=this._AlternateStatus;this._frmAdd._ECCNCode=this._ECCNCode;this._frmAdd._PartEditStatus=1;this.showLoading(!1);this.showForm(this._frmAdd,!0);this.onGotDataOK()},saveAllAlternate:function(){this.showLoading(!0);var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/CusReqMainInfo");n.set_DataObject("CusReqMainInfo");n.set_DataAction("AddAlternateAll");n.addParameter("Part",this.getFieldValue("ctlPartNo"));n.addParameter("id",this._intCustomerRequirementID);n.addDataOK(Function.createDelegate(this,this.saveAllAlternateAddComplete));n.addError(Function.createDelegate(this,this.saveAllAlternateEditError));n.addTimeout(Function.createDelegate(this,this.saveAllAlternateEditError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},saveAllAlternateAddComplete:function(n){this.showLoading(!1);n._result.NewID>0?(this.showSavedOK(!0,String.format("{0} alternate(s) added successfully in this requirement",n._result.NewID)),this.getData(),this.showSaving(!1),this.showError(!1)):alert("This part doesn't have any Alternate.")},saveAllAlternateEditError:function(n){this._strErrorMessage=n._errorMessage;this.showLoading(!1);this.showError(!1);this.showNuggetError(!0,this._strErrorMessage);var t=this.get_events().getHandler("SaveError");t&&t(this,Sys.EventArgs.Empty)},saveEditError:function(n){this._strErrorMessage=n._errorMessage;this.onSaveError()},cancelAdd:function(){this.showForm(this._frmAdd,!1);this.getData()},saveAddComplete:function(){this.showForm(this._frmAdd,!1);this._intCustomerRequirementID=this._frmAdd._intNewID;this.showSavedOK(!0,$R_RES.ChangesSavedSuccessfully);this.getData()},showCloseForm:function(){this._frmClose._intCustomerRequirementID=this._intCustomerRequirementID;this.showForm(this._frmClose,!0)},cancelClose:function(){this.showForm(this._frmClose,!1);this._tbl.resizeColumns()},saveCloseComplete:function(){this.showForm(this._frmClose,!1);this.showSavedOK(!0,$R_RES.ChangesSavedSuccessfully);this.getData()},showAltDeleteForm:function(){this._aryCheckedLineIDs!=""&&this._aryCheckedLineIDs!=null?(this._frmDelete._aryCheckedLineIDs=this._aryCheckedLineIDs,this.showForm(this._frmDelete,!0)):alert("please select alternate part for delete!")},cancelAltDelete:function(){this.showForm(this._frmDelete,!1);this._tbl.resizeColumns()},saveAltDeleteComplete:function(){this._intCustomerRequirementID=this._intOriginalCustomerRequirementID;this.showForm(this._frmDelete,!1);this._tbl.addRow(this._intCustomerRequirementID);this.showSavedOK(!0,$R_RES.ChangesSavedSuccessfully);this.getData()},printCustRequirement:function(){var n=this._strCustomerRequirementNumber;$R_FN.setCookie("Req",n,1);n="";$R_FN.openPrintWindowCustReqWithMultiples($R_ENUM$PrintObject.SingleRequirement,this.getFieldValue("hidCompanyID"));n=null},showConfirmForm:function(){this._frmConfirm._intCustomerRequirementID=this._intCustomerRequirementID;this._frmConfirm._intCurrencyID=this.getFieldValue("hidCurrencyID");this._frmConfirm._intCompanyID=this.getFieldValue("hidCompanyID");var n=!0;(this.getFieldValue("hidManufacturerNo")==null||this.getFieldValue("hidManufacturerNo")<=0)&&(n=!1);this.getFieldValue("ctlQuantity")<0&&(n=!1);this.getFieldValue("hidContactID")<=0&&(n=!1);(this.getFieldValue("ctlPartNo")==null||this.getFieldValue("ctlPartNo").length<=0)&&(n=!1);(this.getFieldValue("hidCurrencyID")==null||this.getFieldValue("hidCurrencyID")<=0)&&(n=!1);(this.getFieldValue("hidProductID")==null||this.getFieldValue("hidProductID")<=0)&&(n=!1);(this.getFieldValue("ctlRequirementforTraceabilityHid")==null||this.getFieldValue("ctlRequirementforTraceabilityHid")<=0)&&(n=!1);(this.getFieldValue("ctlTypeHid")===""||this.getFieldValue("ctlTypeHid")==null)&&(n=!1);this._frmConfirm._ctlPartNo=this.getFieldValue("ctlPartNo");this._frmConfirm._blnReqValidated=n;this.showForm(this._frmConfirm,!0)},hideConfirmForm:function(){this._frmConfirm.setFieldValue("ctlSalesperson","");this.showForm(this._frmConfirm,!1)},cancelConfirm:function(){this.hideConfirmForm()},saveConfirmComplete:function(){this.hideConfirmForm();this.showSavedOK(!0,"HUBRFQ has been sent for Price Request successfully.");this.getData()},saveRequirementData:function(){this.getData_Start();var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/CusReqMainInfo");n.set_DataObject("CusReqMainInfo");n.set_DataAction("SaveRequirementData");n.addParameter("id",this._intCustomerRequirementID);n.addParameter("CompanyNo",this.getFieldValue("hidCompanyID"));n.addParameter("ContactNo",this.getFieldValue("hidContactID"));n.addDataOK(Function.createDelegate(this,this.getDataOK));n.addError(Function.createDelegate(this,this.getDataError));n.addTimeout(Function.createDelegate(this,this.getDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},markedFirmAlt:function(){this.showLoading(!0);var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/CusReqMainInfo");n.set_DataObject("CusReqMainInfo");n.set_DataAction("ChangeAlternateStatus");n.addParameter("id",this._intCustomerRequirementID);n.addParameter("ChangeStatus",this._changeAltStatus);n.addDataOK(Function.createDelegate(this,this.altStatusComplete));n.addError(Function.createDelegate(this,this.altStatusError));n.addTimeout(Function.createDelegate(this,this.altStatusError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},altStatusError:function(n){this.showLoading(!1);this._strErrorMessage=n._errorMessage;this.onSaveError()},altStatusComplete:function(n){this.showLoading(!1);n._result.Result==!0?(this.showSavedOK(!0,"Alternate status has been changed successfully."),this.getData(),this.showError(!1)):(this._strErrorMessage=n._errorMessage,this.onSaveError())},OpenTree:function(){$R_FN.openDocumentTree(this._intCustomerRequirementID,"REQ",this._strCustomerRequirementNumber)},getEditCloneHUBRFQBomDataOK:function(n){var t=n._result;this._frmEditCloneHUBRFQ._intBOMID=t.BOMNo;this._frmEditCloneHUBRFQ._isPoRequest=this._isPoRequest;this._frmEditCloneHUBRFQ._ctlCompany=this.getFieldValue("hidCompanyName");this._frmEditCloneHUBRFQ._ctlContact=this.getFieldValue("hidContactName");this._frmEditCloneHUBRFQ._salesmanNo=this.getFieldValue("hidSalesman");this._frmEditCloneHUBRFQ._ctlROHS=this.getFieldValue("hidROHS");this._frmEditCloneHUBRFQ._ctlQuantity=this.getFieldValue("ctlQuantity");this._frmEditCloneHUBRFQ._ctlPartNo=this.getFieldValue("ctlPartNo");this._frmEditCloneHUBRFQ._ctlCustomerPart=this.getFieldValue("ctlCustomerPart");this._frmEditCloneHUBRFQ._ctlManufacturer=this._ctlManufacturer;this._frmEditCloneHUBRFQ._hidManufacturer=this._hidManufacturer;this._frmEditCloneHUBRFQ._hidManufacturerNo=this._hidManufacturerNo;this._frmEditCloneHUBRFQ._ctlDateCode=this.getFieldValue("ctlDateCode");this._frmEditCloneHUBRFQ._ctlProduct=this.getFieldValue("ctlProduct");this._frmEditCloneHUBRFQ._hidProductID=this.getFieldValue("hidProductID");this._frmEditCloneHUBRFQ._ctlProductDis=this._ctlProductDis;this._frmEditCloneHUBRFQ._ctlPrdDutyCodeRate=this._ctlPrdDutyCodeRate;this._frmEditCloneHUBRFQ._ctlPackage=this._ctlPackage;this._frmEditCloneHUBRFQ._hidPackageID=this.getFieldValue("hidPackageID");this._frmEditCloneHUBRFQ._ctlTargetPrice=this._ctlTargetPrice;this._frmEditCloneHUBRFQ._hidPrice=this.getFieldValue("hidPrice");this._frmEditCloneHUBRFQ._ctlCurrency=this._ctlCurrency;this._frmEditCloneHUBRFQ._hidCurrencyID=this.getFieldValue("hidCurrencyID");this._frmEditCloneHUBRFQ._ctlDateRequired=this.getFieldValue("ctlDateRequired");this._frmEditCloneHUBRFQ._ctlUsage=this._ctlUsage;this._frmEditCloneHUBRFQ._hidUsageID=this.getFieldValue("hidUsageID");this._frmEditCloneHUBRFQ._ctlNotes=this.getFieldValue("ctlNotes");this._frmEditCloneHUBRFQ._ctlInstructions=this.getFieldValue("ctlInstructions");this._frmEditCloneHUBRFQ._hidROHS=this._hidROHS;this._frmEditCloneHUBRFQ._ctlClosed=this._ctlClosed;this._frmEditCloneHUBRFQ._ctlClosedReason=this._ctlClosedReason;this._frmEditCloneHUBRFQ._hidDisplayStatus=this._hidDisplayStatus;this._frmEditCloneHUBRFQ._ctlPartWatch=this.getFieldValue("ctlPartWatch");this._frmEditCloneHUBRFQ._ctlBOM=this.getFieldValue("ctlBOM");this._frmEditCloneHUBRFQ._ctlBOMName=this.getFieldValue("ctlBOMName");this._frmEditCloneHUBRFQ._ctlBOMHeader=this._ctlBOMHeader;this._frmEditCloneHUBRFQ._hidBOMID=this._hidBOMID;this._frmEditCloneHUBRFQ._ctlMSL=this._ctlMSL;this._frmEditCloneHUBRFQ._ctlFactorySealed=this.getFieldValue("ctlFactorySealed");this._frmEditCloneHUBRFQ._ctlPQA=this.getFieldValue("ctlPQA");this._frmEditCloneHUBRFQ._ctlTargetSellPrice=this._ctlTargetSellPrice;this._frmEditCloneHUBRFQ._ctlCompetitorBestoffer=this._ctlCompetitorBestoffer;this._frmEditCloneHUBRFQ._ctlCustomerDecisionDate=this._ctlCustomerDecisionDate;this._frmEditCloneHUBRFQ._ctlRFQClosingDate=this._ctlRFQClosingDate;this._frmEditCloneHUBRFQ._ctlQuoteValidityRequiredHid=this._ctlQuoteValidityRequiredHid;this._frmEditCloneHUBRFQ._ctlQuoteValidityRequired=this._ctlQuoteValidityRequired;this._frmEditCloneHUBRFQ._ctlTypeHid=this.getFieldValue("ctlTypeHid");this._frmEditCloneHUBRFQ._ctlOrderToPlace=this._ctlOrderToPlace;this._frmEditCloneHUBRFQ._ctlRequirementforTraceabilityHid=this.getFieldValue("ctlRequirementforTraceabilityHid");this._frmEditCloneHUBRFQ._ctlTargetSellPriceHidden=this.getFieldValue("ctlTargetSellPriceHidden");this._frmEditCloneHUBRFQ._ctlCompetitorBestofferHidden=this.getFieldValue("ctlCompetitorBestofferHidden");this._frmEditCloneHUBRFQ._ctlEAU=this.getFieldValue("ctlEAU");this._frmEditCloneHUBRFQ._hidCustGCNo=this.getFieldValue("hidCustGCNo");this._frmEditCloneHUBRFQ._blnProdInactive=this._blnProdInactive;this._frmEditCloneHUBRFQ._ctlSalespersion=this.getFieldValue("ctlSalespersion");this._frmEditCloneHUBRFQ._hidSalesPersion=this.getFieldValue("hidSalesPersion");this._frmEditCloneHUBRFQ._hidCountryOfOrigin=this._hidCountryOfOrigin;this._frmEditCloneHUBRFQ._hidCountryOfOriginNo=this._hidCountryOfOriginNo;this._frmEditCloneHUBRFQ._hidLifeCycleStage=this._hidLifeCycleStage;this._frmEditCloneHUBRFQ._hidHTSCode=this._hidHTSCode;this._frmEditCloneHUBRFQ._hidAveragePrice=this._hidAveragePrice;this._frmEditCloneHUBRFQ._hidPackaging=this._hidPackaging;this._frmEditCloneHUBRFQ._hidPackagingSize=this._hidPackagingSize;this._frmEditCloneHUBRFQ._intCompanyID=this.getFieldValue("hidCompanyID");this._frmEditCloneHUBRFQ._radioCheck=this.getFieldValue("ctlFactorySealed");this._frmEditCloneHUBRFQ._radObsoleteChk=this.getFieldValue("ctlObsolete");this._frmEditCloneHUBRFQ._radLastTimeBuyChk=this.getFieldValue("ctlLastTimeBuy");this._frmEditCloneHUBRFQ._radRefirbsAcceptableChk=this.getFieldValue("ctlRefirbsAcceptable");this._frmEditCloneHUBRFQ._radTestingRequiredChk=this.getFieldValue("ctlTestingRequired");this._frmEditCloneHUBRFQ._radAlternativesAcceptedChK=this.getFieldValue("ctlAlternativesAccepted");this._frmEditCloneHUBRFQ._radRepeatBusinessChk=this.getFieldValue("ctlRepeatBusiness");this._frmEditCloneHUBRFQ._intCustomerRequirementID=this._intCustomerRequirementID;this._intBOMID=t.BOMNo;this._frmEditCloneHUBRFQ._BOMHeaderDisplayStatus=Boolean.parse(this.getFieldValue("hidBOMHeaderDisplayStatus"));this._frmEditCloneHUBRFQ._blnCurInSameFaimly=t.IsSameCurFam;this._frmEditCloneHUBRFQ._intCurrencyNo=this.getFieldValue("hidCurrencyID");this._frmEditCloneHUBRFQ._IHSProductNo=this._IHSProductNo;this._frmEditCloneHUBRFQ._IHSProduct=this._IHSProduct;this._frmEditCloneHUBRFQ._IHSHTSCode=this._IHSHTSCode;this._frmEditCloneHUBRFQ._IHSDutyCode=this._IHSDutyCode;this._frmEditCloneHUBRFQ._AlternateStatus=this._AlternateStatus;this._frmEditCloneHUBRFQ._ECCNCode=this._ECCNCode;this._frmEditCloneHUBRFQ._PartEditStatus=this._PartEditStatus;this.showLoading(!1);this.showForm(this._frmEditCloneHUBRFQ,!0);this.onGotDataOK()},getCloneHUBRFQBomNo:function(){var n=new Rebound.GlobalTrader.Site.Data;this.showLoading(!0);$R_FN.showElement(this._pnlLineDetail,!1);$R_FN.showElement(this._pnlLoadingLineDetail,!0);$R_FN.showElement(this._pnlLineDetailError,!1);n.set_PathToData("controls/Nuggets/CusReqMainInfo");n.set_DataObject("CusReqMainInfo");n.set_DataAction("GetBomID");n.addParameter("id",this._intCustomerRequirementID);n.addParameter("CustomerRequirementNumber",this._strCustomerRequirementNumber);n.addDataOK(Function.createDelegate(this,this.getEditCloneHUBRFQBomDataOK));n.addError(Function.createDelegate(this,this.getLineDataError));n.addTimeout(Function.createDelegate(this,this.getLineDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},showEditCloneHUBRFQForm:function(){this.getCloneHUBRFQBomNo()},hideEditCloneHUBRFQForm:function(){this.showForm(this._frmEditCloneHUBRFQ,!1)},cancelEditCloneHUBRFQ:function(){this.hideEditCloneHUBRFQForm();this.getData()},saveEditCloneHUBRFQComplete:function(){this.hideEditCloneHUBRFQForm();this.showSavedOK(!0,"HUBRFQ has been added and clone successfully.");this.onSaveEditComplete();this.getData()},getEditCloneHUBBomDataOK:function(n){var t=n._result;this._frmEditCloneHUB._intBOMID=t.BOMNo;this._frmEditCloneHUB._isPoRequest=this._isPoRequest;this._frmEditCloneHUB._ctlCompany=this.getFieldValue("hidCompanyName");this._frmEditCloneHUB._ctlContact=this.getFieldValue("hidContactName");this._frmEditCloneHUB._salesmanNo=this.getFieldValue("hidSalesman");this._frmEditCloneHUB._ctlROHS=this.getFieldValue("hidROHS");this._frmEditCloneHUB._ctlQuantity=this.getFieldValue("ctlQuantity");this._frmEditCloneHUB._ctlPartNo=this.getFieldValue("ctlPartNo");this._frmEditCloneHUB._ctlCustomerPart=this.getFieldValue("ctlCustomerPart");this._frmEditCloneHUB._ctlManufacturer=this._ctlManufacturer;this._frmEditCloneHUB._hidManufacturer=this._hidManufacturer;this._frmEditCloneHUB._hidManufacturerNo=this._hidManufacturerNo;this._frmEditCloneHUB._ctlDateCode=this.getFieldValue("ctlDateCode");this._frmEditCloneHUB._ctlProduct=this.getFieldValue("ctlProduct");this._frmEditCloneHUB._hidProductID=this.getFieldValue("hidProductID");this._frmEditCloneHUB._ctlProductDis=this._ctlProductDis;this._frmEditCloneHUB._ctlPrdDutyCodeRate=this._ctlPrdDutyCodeRate;this._frmEditCloneHUB._ctlPackage=this._ctlPackage;this._frmEditCloneHUB._hidPackageID=this.getFieldValue("hidPackageID");this._frmEditCloneHUB._ctlTargetPrice=this._ctlTargetPrice;this._frmEditCloneHUB._hidPrice=this.getFieldValue("hidPrice");this._frmEditCloneHUB._ctlCurrency=this._ctlCurrency;this._frmEditCloneHUB._hidCurrencyID=this.getFieldValue("hidCurrencyID");this._frmEditCloneHUB._ctlDateRequired=this.getFieldValue("ctlDateRequired");this._frmEditCloneHUB._ctlUsage=this._ctlUsage;this._frmEditCloneHUB._hidUsageID=this.getFieldValue("hidUsageID");this._frmEditCloneHUB._ctlNotes=this.getFieldValue("ctlNotes");this._frmEditCloneHUB._ctlInstructions=this.getFieldValue("ctlInstructions");this._frmEditCloneHUB._hidROHS=this._hidROHS;this._frmEditCloneHUB._ctlClosed=this._ctlClosed;this._frmEditCloneHUB._ctlClosedReason=this._ctlClosedReason;this._frmEditCloneHUB._hidDisplayStatus=this._hidDisplayStatus;this._frmEditCloneHUB._ctlPartWatch=this.getFieldValue("ctlPartWatch");this._frmEditCloneHUB._ctlBOM=this.getFieldValue("ctlBOM");this._frmEditCloneHUB._ctlBOMName=this.getFieldValue("ctlBOMName");this._frmEditCloneHUB._ctlBOMHeader=this._ctlBOMHeader;this._frmEditCloneHUB._hidBOMID=this._hidBOMID;this._frmEditCloneHUB._ctlMSL=this._ctlMSL;this._frmEditCloneHUB._ctlFactorySealed=this.getFieldValue("ctlFactorySealed");this._frmEditCloneHUB._ctlPQA=this.getFieldValue("ctlPQA");this._frmEditCloneHUB._ctlTargetSellPrice=this._ctlTargetSellPrice;this._frmEditCloneHUB._ctlCompetitorBestoffer=this._ctlCompetitorBestoffer;this._frmEditCloneHUB._ctlCustomerDecisionDate=this._ctlCustomerDecisionDate;this._frmEditCloneHUB._ctlRFQClosingDate=this._ctlRFQClosingDate;this._frmEditCloneHUB._ctlQuoteValidityRequiredHid=this._ctlQuoteValidityRequiredHid;this._frmEditCloneHUB._ctlQuoteValidityRequired=this._ctlQuoteValidityRequired;this._frmEditCloneHUB._ctlTypeHid=this.getFieldValue("ctlTypeHid");this._frmEditCloneHUB._ctlOrderToPlace=this._ctlOrderToPlace;this._frmEditCloneHUB._ctlRequirementforTraceabilityHid=this.getFieldValue("ctlRequirementforTraceabilityHid");this._frmEditCloneHUB._ctlTargetSellPriceHidden=this.getFieldValue("ctlTargetSellPriceHidden");this._frmEditCloneHUB._ctlCompetitorBestofferHidden=this.getFieldValue("ctlCompetitorBestofferHidden");this._frmEditCloneHUB._ctlEAU=this.getFieldValue("ctlEAU");this._frmEditCloneHUB._hidCustGCNo=this.getFieldValue("hidCustGCNo");this._frmEditCloneHUB._blnProdInactive=this._blnProdInactive;this._frmEditCloneHUB._ctlSalespersion=this.getFieldValue("ctlSalespersion");this._frmEditCloneHUB._hidSalesPersion=this.getFieldValue("hidSalesPersion");this._frmEditCloneHUB._hidCountryOfOrigin=this._hidCountryOfOrigin;this._frmEditCloneHUB._hidCountryOfOriginNo=this._hidCountryOfOriginNo;this._frmEditCloneHUB._hidLifeCycleStage=this._hidLifeCycleStage;this._frmEditCloneHUB._hidHTSCode=this._hidHTSCode;this._frmEditCloneHUB._hidAveragePrice=this._hidAveragePrice;this._frmEditCloneHUB._hidPackaging=this._hidPackaging;this._frmEditCloneHUB._hidPackagingSize=this._hidPackagingSize;this._frmEditCloneHUB._intCompanyID=this.getFieldValue("hidCompanyID");this._frmEditCloneHUB._radioCheck=this.getFieldValue("ctlFactorySealed");this._frmEditCloneHUB._radObsoleteChk=this.getFieldValue("ctlObsolete");this._frmEditCloneHUB._radLastTimeBuyChk=this.getFieldValue("ctlLastTimeBuy");this._frmEditCloneHUB._radRefirbsAcceptableChk=this.getFieldValue("ctlRefirbsAcceptable");this._frmEditCloneHUB._radTestingRequiredChk=this.getFieldValue("ctlTestingRequired");this._frmEditCloneHUB._radAlternativesAcceptedChK=this.getFieldValue("ctlAlternativesAccepted");this._frmEditCloneHUB._radRepeatBusinessChk=this.getFieldValue("ctlRepeatBusiness");this._frmEditCloneHUB._intCustomerRequirementID=this._intCustomerRequirementID;this._intBOMID=t.BOMNo;this._frmEditCloneHUB._BOMHeaderDisplayStatus=Boolean.parse(this.getFieldValue("hidBOMHeaderDisplayStatus"));this._frmEditCloneHUB._blnCurInSameFaimly=t.IsSameCurFam;this._frmEditCloneHUB._intCurrencyNo=this.getFieldValue("hidCurrencyID");this._frmEditCloneHUB._IHSProductNo=this._IHSProductNo;this._frmEditCloneHUB._IHSProduct=this._IHSProduct;this._frmEditCloneHUB._IHSHTSCode=this._IHSHTSCode;this._frmEditCloneHUB._IHSDutyCode=this._IHSDutyCode;this._frmEditCloneHUB._AlternateStatus=this._AlternateStatus;this._frmEditCloneHUB._ECCNCode=this._ECCNCode;this._frmEditCloneHUB._PartEditStatus=this._PartEditStatus;this.showLoading(!1);this.showForm(this._frmEditCloneHUB,!0);this.onGotDataOK()},getCloneHUBBomNo:function(){var n=new Rebound.GlobalTrader.Site.Data;this.showLoading(!0);$R_FN.showElement(this._pnlLineDetail,!1);$R_FN.showElement(this._pnlLoadingLineDetail,!0);$R_FN.showElement(this._pnlLineDetailError,!1);n.set_PathToData("controls/Nuggets/CusReqMainInfo");n.set_DataObject("CusReqMainInfo");n.set_DataAction("GetBomID");n.addParameter("id",this._intCustomerRequirementID);n.addParameter("CustomerRequirementNumber",this._strCustomerRequirementNumber);n.addDataOK(Function.createDelegate(this,this.getEditCloneHUBBomDataOK));n.addError(Function.createDelegate(this,this.getLineDataError));n.addTimeout(Function.createDelegate(this,this.getLineDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},showEditCloneHUBForm:function(){this.getCloneHUBBomNo()},hideEditCloneHUBForm:function(){this.showForm(this._frmEditCloneHUB,!1)},cancelEditCloneHUB:function(){this.hideEditCloneHUBForm();this.getData()},saveEditCloneHUBComplete:function(){this.hideEditCloneHUBForm();this.showSavedOK(!0,"Clone successfully and send to HUB.");this.onSaveEditComplete();this.getData()},getAS6081BannerMessage:function(){var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("Controls/SetupNuggets/AS6081");n.set_DataObject("AS6081");n.set_DataAction("GetAlertMessageByOperationType");n.addParameter("operationType","ReceivePoBanner");n.addDataOK(Function.createDelegate(this,this.getAS6081BannerMessageOK));n.addError(Function.createDelegate(this,this.getAS6081BannerMessageError));n.addTimeout(Function.createDelegate(this,this.getAS6081BannerMessageError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getAS6081BannerMessageOK:function(n){var t=n._result;this.clearMessages();this.addMessage(t.Message,$R_ENUM$MessageTypeList.Warning)},getAS6081BannerMessageError:function(){console.error(`error occured while trying to fetch 'banner for AS6081'`)},StartRefreshLylicaAPIData:function(n,t,i){n=this.beautifyPartNumber(n);$.ajax({type:"POST",contentType:"application/json",url:this.handlerUrl+"?action=RefreshLyticaAPIAfter3Days&PartNumber="+n+"&mfr="+t+"&mfrNo="+i,async:!0,error:function(){}})},beautifyPartNumber:function(n){return n=n.replace(" (Alternate)",""),n=n.replace("&","_AMPERSAND_"),n=n.replace("#","_HASH_"),n.replace("=","_EQUALS_")}};Rebound.GlobalTrader.Site.Controls.Nuggets.CustomerRequirementMainInfo.registerClass("Rebound.GlobalTrader.Site.Controls.Nuggets.CustomerRequirementMainInfo",Rebound.GlobalTrader.Site.Controls.Nuggets.Base);