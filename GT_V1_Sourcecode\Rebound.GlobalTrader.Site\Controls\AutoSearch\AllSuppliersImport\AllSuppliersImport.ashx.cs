using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;

namespace Rebound.GlobalTrader.Site.Controls.AutoSearch.Data {

	[WebService(Namespace = "http://tempuri.org/")]
	[WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
	public class AllSuppliersImport : Rebound.GlobalTrader.Site.Controls.AutoSearch.Data.Base {

		protected override void GetData() {

            int? intGlobalLoginClientNo;
            intGlobalLoginClientNo = GetFormValue_NullableInt("GlobalLoginClientNo");

            //string txtGroup = null;
            //txtGroup = GetFormValue_String("txtGroup");
            int? txtGroup;
            txtGroup = GetFormValue_NullableInt("txtGroup");


            List<Company> lst = null;
			try {
                lst = Company.AutoSearchForAllSuppliersImport(intGlobalLoginClientNo, GetFormValue_StringForNameSearch("search"), txtGroup);
				JsonObject jsn = new JsonObject();
				jsn.AddVariable("TotalRecords", lst.Count);
				JsonObject jsnRows = new JsonObject(true);
				for (int i = 0; i < lst.Count; i++) {
					if (i < lst.Count) {
						JsonObject jsnRow = new JsonObject();
						jsnRow.AddVariable("ID", lst[i].CompanyId);
						jsnRow.AddVariable("Name", lst[i].CompanyName);
						jsnRow.AddVariable("SupNo", lst[i].CompanyId);
                        jsnRow.AddVariable("Email", lst[i].EMail);
						jsnRows.AddVariable(jsnRow);
						jsnRow.Dispose();
						jsnRow = null;
					}
				}
				jsn.AddVariable("Results", jsnRows);
				OutputResult(jsn);
				jsnRows.Dispose(); jsnRows = null;
				jsn.Dispose(); jsn = null;
			} catch (Exception e) {
				WriteError(e);
			} finally {
				lst = null;
			}
			base.GetData();
		}
	}
}