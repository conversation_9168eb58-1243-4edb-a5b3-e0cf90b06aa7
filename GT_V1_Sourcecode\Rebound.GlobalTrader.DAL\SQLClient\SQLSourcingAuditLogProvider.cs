﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Data.SqlClient;

namespace Rebound.GlobalTrader.DAL.SqlClient
{
    public class SqlSourcingAuditLogProvider : SourcingAuditLogProvider
    {
        public override List<SourcingAuditLogDetails> GetBulkEditLog(int? order,
                                                                     int? sortDir,
                                                                     int? pageIndex,
                                                                     int? pageSize,
                                                                     string sourcingType,
                                                                     string partSearch,
                                                                     int? userId,
                                                                     DateTime? updatedDateFrom,
                                                                     DateTime? updatedDateTo)
        {
			SqlConnection cn = null;
			SqlCommand cmd = null;
			try
			{
				string spName = sourcingType.Equals("StrategicStock") ? "usp_get_BulkEditLog_Epo" : "usp_get_BulkEditLog_RL";
				cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand(spName, cn)
                {
                    CommandType = CommandType.StoredProcedure,
                    CommandTimeout = 30
                };
                cmd.Parameters.Add("@OrderBy", SqlDbType.Int).Value = order;
                cmd.Parameters.Add("@SortDir", SqlDbType.Int).Value = sortDir;
                cmd.Parameters.Add("@PageIndex", SqlDbType.Int).Value = pageIndex;
                cmd.Parameters.Add("@PageSize", SqlDbType.Int).Value = pageSize;
                cmd.Parameters.Add("@PartSearch", SqlDbType.NVarChar).Value = partSearch;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = userId;
                cmd.Parameters.Add("@DateFrom", SqlDbType.DateTime).Value = updatedDateFrom;
                cmd.Parameters.Add("@DateTo", SqlDbType.DateTime).Value = updatedDateTo;
                cn.Open();
				DbDataReader reader = ExecuteReader(cmd);
				List<SourcingAuditLogDetails> lst = new List<SourcingAuditLogDetails>();
				while (reader.Read())
				{
                    SourcingAuditLogDetails obj = new SourcingAuditLogDetails
                    {
                        SourcingAuditLogId = GetReaderValue_Int32(reader, "SourcingAuditLogId", 0),
                        SourcingId = GetReaderValue_Int32(reader, "SourcingId", 0),
                        BatchId = GetReaderValue_Int32(reader, "BatchNo", 0),
                        SourcingType = GetReaderValue_String(reader, "SourcingType", ""),
                        PartNo = GetReaderValue_String(reader, "PartNo", ""),
                        Action = GetReaderValue_String(reader, "Action", ""),
                        OldValue = GetReaderValue_String(reader, "OldValue", ""),
                        UpdatedBy = GetReaderValue_String(reader, "UpdatedBy", ""),
                        UpdatedDate = GetReaderValue_DateTime(reader, "UpdatedDate", DateTime.MinValue),
                        TotalCount = GetReaderValue_Int32(reader, "RowCnt", 0)
                    };
                    lst.Add(obj);
					obj = null;
				}
				return lst;
			}
			catch (SqlException sqlex)
			{
				throw new Exception("Failed to get sourcing bulk edit log", sqlex);
			}
			finally
			{
				cmd.Dispose();
				cn.Close();
				cn.Dispose();
			}
		}
    }
}
