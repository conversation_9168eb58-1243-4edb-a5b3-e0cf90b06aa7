/* Marker     changed by      date         Remarks
/* [0001]      A<PERSON><PERSON><PERSON>   14/07/2014  ESMS Ref:172 - Issue in Part Searching in whole GT */

using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;
using Rebound.GlobalTrader.Site.Enumerations;

namespace Rebound.GlobalTrader.Site.Controls.DataListNuggets.Data {

	public class SRMAsShip : Base {

		private string _strCallType;

		public override void ProcessRequest(HttpContext context) {
			base.ProcessRequest(context);
			if (Action == "GetData_All") GetData_All();
		}

		protected override void GetData() {
			_strCallType = "READY";
			List<SupplierRmaLine> lst = SupplierRmaLine.DataListNuggetReadyToShip(
				SessionManager.ClientID
				, GetFormValue_NullableInt("PageIndex", 0)
				, GetFormValue_NullableInt("PageSize", 10)
				, GetFormValue_NullableInt("SortIndex")
				, GetFormValue_NullableInt("SortDir", SortColumnDirection.ASC)
                //[0001] start code
                //, GetFormValue_StringForPartSearch("Part")
                  , GetFormValue_PartForLikeSearch("Part")
                //[0001] end code
               // , GetFormValue_StringForNameSearch("Contact")
                 , GetFormValue_StringForNameSearchDecode("Contact")
               // , GetFormValue_StringForNameSearch("CMName")
                 , GetFormValue_StringForNameSearchDecode("CMName")
				, GetFormValue_NullableInt("Buyer")
				, GetFormValue_StringForSearch("SRMANotes")
				, GetFormValue_NullableInt("PONoLo")
				, GetFormValue_NullableInt("PONoHi")
				, GetFormValue_NullableInt("SRMANoLo")
				, GetFormValue_NullableInt("SRMANoHi")
				, GetFormValue_NullableDateTime("SRMADateFrom")
				, GetFormValue_NullableDateTime("SRMADateTo")
                 , GetFormValue_NullableInt("Client")
                , GetFormValue_Boolean("IsGlobalLogin")
				);
			FormatData(lst);
			base.GetData();
		}

		private void GetData_All() {
			_strCallType = "ALL";
			List<SupplierRmaLine> lst = SupplierRmaLine.DataListNugget(
				SessionManager.ClientID
				, null
				, null
				, null
				, GetFormValue_NullableInt("PageIndex", 0)
				, GetFormValue_NullableInt("PageSize", 10)
				, GetFormValue_NullableInt("SortIndex")
				, GetFormValue_NullableInt("SortDir", SortColumnDirection.ASC)
                //[0001] start code
                //, GetFormValue_StringForPartSearch("Part")
                  , GetFormValue_PartForLikeSearch("Part")
                //[0001] end code
                // , GetFormValue_StringForNameSearch("Contact")
                 , GetFormValue_StringForNameSearchDecode("Contact")
                // , GetFormValue_StringForNameSearch("CMName")
                 , GetFormValue_StringForNameSearchDecode("CMName")
				, GetFormValue_NullableInt("Buyer")
				, GetFormValue_StringForSearch("SRMANotes")
				, GetFormValue_NullableInt("PONoLo")
				, GetFormValue_NullableInt("PONoHi")
				, GetFormValue_NullableInt("SRMANoLo")
				, GetFormValue_NullableInt("SRMANoHi")
				, GetFormValue_NullableDateTime("SRMADateFrom")
				, GetFormValue_NullableDateTime("SRMADateTo")
                , null
                , null
                , false
                 , GetFormValue_NullableInt("Client")
                , GetFormValue_Boolean("IsGlobalLogin")
                );

			FormatData(lst);
			SaveState();
		}

		private void FormatData(List<SupplierRmaLine> lst) {

			JsonObject jsn = new JsonObject();
			jsn.AddVariable("TotalRecords", (lst.Count > 0) ? lst[0].RowCnt : 0);
			JsonObject jsnRowsArray = new JsonObject(true);
			for (int i = 0; i < lst.Count; i++) {
				if (i < lst.Count) {
					JsonObject jsnRow = new JsonObject();
					jsnRow.AddVariable("ID", lst[i].SupplierRMAId);
					jsnRow.AddVariable("No", lst[i].SupplierRMANumber);
					jsnRow.AddVariable("Part", lst[i].Part);
					jsnRow.AddVariable("Quantity", Functions.FormatNumeric(lst[i].Quantity));
					jsnRow.AddVariable("QuantityAllocated", Functions.FormatNumeric(lst[i].AllocatedQuantity));
					jsnRow.AddVariable("Date", Functions.FormatDate(lst[i].SupplierRMADate));
					jsnRow.AddVariable("CM", lst[i].CompanyName);
					jsnRow.AddVariable("CMNo", lst[i].CompanyNo);
					jsnRow.AddVariable("PO", lst[i].PurchaseOrderNumber);
					jsnRow.AddVariable("PONo", lst[i].PurchaseOrderNo);
					jsnRow.AddVariable("Contact", lst[i].ContactName);
					jsnRow.AddVariable("ContactNo", lst[i].ContactNo);
					jsnRow.AddVariable("ROHS", lst[i].ROHS);
					jsnRow.AddVariable("Mfr", lst[i].ManufacturerCode);
					jsnRow.AddVariable("MfrNo", lst[i].ManufacturerNo);
                    jsnRow.AddVariable("ClientName", lst[i].ClientName);
					jsnRowsArray.AddVariable(jsnRow);
					jsnRow.Dispose();
					jsnRow = null;
				}
			}
			jsn.AddVariable("Results", jsnRowsArray);
			OutputResult(jsn);
			jsnRowsArray.Dispose();
			jsnRowsArray = null;
			jsn.Dispose();
			jsn = null;
			lst = null;
		}

		protected override void AddFilterStates() {
			AddExplicitFilterState("CallType", _strCallType);
			AddFilterState("Part");
			AddFilterState("Contact");
			AddFilterState("CMName");
			AddFilterState("Buyer");
			AddFilterState("SRMANotes");
			AddFilterState("PONo");
			AddFilterState("SRMANo");
			AddFilterState("SRMADateFrom");
			AddFilterState("SRMADateTo");
			base.AddFilterStates();
		}
	}
}
