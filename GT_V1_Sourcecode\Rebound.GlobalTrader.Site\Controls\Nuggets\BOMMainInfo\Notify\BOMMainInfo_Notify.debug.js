///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//Marker     Changed by      Date         Remarks
//[001]      Vinay           04/07/2012   This need to notify the user by email.
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

    Rebound.GlobalTrader.Site.Controls.Forms.BOMMainInfo_Notify = function(element) {
    Rebound.GlobalTrader.Site.Controls.Forms.BOMMainInfo_Notify.initializeBase(this, [element]);
	this._strPONumber = "";
};

Rebound.GlobalTrader.Site.Controls.Forms.BOMMainInfo_Notify.prototype = {
    get_intBOMID: function() { return this._intBOMID; }, set_intBOMID: function(v) { if (this._intBOMID !== v) this._intBOMID = v; },
    get_intCompanyID: function() { return this._intCompanyID; }, set_intCompanyID: function(v) { if (this._intCompanyID !== v) this._intCompanyID = v; },
    get_ibtnSend: function() { return this._ibtnSend; }, set_ibtnSend: function(value) { if (this._ibtnSend !== value) this._ibtnSend = value; },
    get_ibtnSend_Footer: function() { return this._ibtnSend_Footer; }, set_ibtnSend_Footer: function(v) { if (this._ibtnSend_Footer !== v) this._ibtnSend_Footer = v; },
    get_stringCurrency: function() { return this._stringCurrency; }, set_stringCurrency: function(v) { if (this._stringCurrencyD !== v) this._stringCurrency = v; },
    get_BomCode: function() { return this._BomCode; }, set_BomCode: function(value) { if (this._BomCode !== value) this._BomCode = value; },
    get_BomName: function() { return this._BomName; }, set_BomName: function(value) { if (this._BomName !== value) this._BomName = value; },
    get_BomCompanyName: function() { return this._BomCompanyName; }, set_BomCompanyName: function(value) { if (this._BomCompanyName !== value) this._BomCompanyName = value; },
    get_BomCompanyNo: function() { return this._BomCompanyNo; }, set_BomCompanyNo: function(value) { if (this._BomCompanyNo !== value) this._BomCompanyNo = value; },
    initialize: function() {
        Rebound.GlobalTrader.Site.Controls.Forms.BOMMainInfo_Notify.callBaseMethod(this, "initialize");
        this.addShown(Function.createDelegate(this, this.formShown));
    },

    formShown: function() {
        //alert(this._BomCode);
        if (this._blnFirstTimeShown) {
            $R_IBTN.addClick(this._ibtnSend, Function.createDelegate(this, this.sendMail));
            $R_IBTN.addClick(this._ibtnSend_Footer, Function.createDelegate(this, this.sendMail));
            this._ctlMail = $find(this.getField("ctlSendMailMessage").ID);
            this._ctlMail._ctlRelatedForm = this;

            this.getMessage();
        }
    },

    dispose: function() {
        if (this.isDisposed) return;
        if (this._ibtnSend) $R_IBTN.clearHandlers(this._ibtnSend);
        if (this._ibtnSend_Footer) $R_IBTN.clearHandlers(this._ibtnSend_Footer);
        if (this._ctlMail) this._ctlMail.dispose();
        this._ctlMail = null;
        this._strPONumber = null;
        this._intCompanyID = null;
        this._intPurchaseOrderID = null;
        this._ibtnSend = null;
        this._ibtnSend_Footer = null;
        Rebound.GlobalTrader.Site.Controls.Forms.BOMMainInfo_Notify.callBaseMethod(this, "dispose");
    },

    getMessage: function() {
    this._ctlMail.setValue_Body("Kindly find the attached HUBRFQ regarding our requirements.<br/>Please update the price column of the sheet & send us back for further processing.");
    this._ctlMail.setValue_Subject("HUBRFQ " + this._BomCode + " Notification");
    },
    //[001] code start
    sendMail: function() {
        //
       // alert($R_FN.arrayToSingleString(this._ctlMail._aryRecipientEmail));
    if (!this.validateForm()) return;
    this.showSaving(true);
   // Rebound.GlobalTrader.Site.WebServices.NotifyMessageSupplier($R_FN.arrayToSingleString(this._ctlMail._aryRecipientEmail), this._ctlMail.getValue_Subject(), this._ctlMail.getValue_Body(), 114, this._intBOMID, this._intCompanyID, $R_FN.arrayToSingleString(this._ctlMail._aryCompanyIDs), this._stringCurrency, $R_FN.arrayToSingleString(this._ctlMail._aryRecipientLoginNames), Function.createDelegate(this, this.sendMailComplete));
    Rebound.GlobalTrader.Site.WebServices.NotifyMessageSupplier($R_FN.arrayToSingleString(this._ctlMail._aryRecipientEmail), this._ctlMail.getValue_Subject(), this._ctlMail.getValue_Body(), 114, this._intBOMID, this._intCompanyID, $R_FN.arrayToSingleString(this._ctlMail._aryCompanyIDs), this._stringCurrency, $R_FN.arrayToSingleString(this._ctlMail._aryRecipientLoginNames), Function.createDelegate(this, this.sendMailComplete));
    },
    //[001] code end
    validateForm: function() {
    this._ctlMail.setKeyValueArray();
        var blnOK = this._ctlMail.validateFields();
        if (!blnOK) this.showError(true);
        return blnOK;
    },

    sendMailComplete: function() {
    this.showSaving(false);
        this.showSavedOK(true);
        this.onSaveComplete();
    }

};

Rebound.GlobalTrader.Site.Controls.Forms.BOMMainInfo_Notify.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.BOMMainInfo_Notify", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
