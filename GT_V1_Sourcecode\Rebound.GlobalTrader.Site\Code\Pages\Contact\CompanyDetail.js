Type.registerNamespace("Rebound.GlobalTrader.Site.Pages.Contact");Rebound.GlobalTrader.Site.Pages.Contact.CompanyDetail=function(n){Rebound.GlobalTrader.Site.Pages.Contact.CompanyDetail.initializeBase(this,[n]);this._aryGotTabData=[]};Rebound.GlobalTrader.Site.Pages.Contact.CompanyDetail.prototype={get_ctlPageTitle:function(){return this._ctlPageTitle},set_ctlPageTitle:function(n){this._ctlPageTitle!==n&&(this._ctlPageTitle=n)},get_intCurrentTab:function(){return this._intCurrentTab},set_intCurrentTab:function(n){this._intCurrentTab!==n&&(this._intCurrentTab=n)},get_pnlMain:function(){return this._pnlMain},set_pnlMain:function(n){this._pnlMain!==n&&(this._pnlMain=n)},get_pnlDetail:function(){return this._pnlDetail},set_pnlDetail:function(n){this._pnlDetail!==n&&(this._pnlDetail=n)},get_ctlMainCompanyInfo:function(){return this._ctlMainCompanyInfo},set_ctlMainCompanyInfo:function(n){this._ctlMainCompanyInfo!==n&&(this._ctlMainCompanyInfo=n)},get_ctlContactLog:function(){return this._ctlContactLog},set_ctlContactLog:function(n){this._ctlContactLog!==n&&(this._ctlContactLog=n)},get_ctlTransactions:function(){return this._ctlTransactions},set_ctlTransactions:function(n){this._ctlTransactions!==n&&(this._ctlTransactions=n)},get_ctlAddresses:function(){return this._ctlAddresses},set_ctlAddresses:function(n){this._ctlAddresses!==n&&(this._ctlAddresses=n)},get_ctlPurchasingInfo:function(){return this._ctlPurchasingInfo},set_ctlPurchasingInfo:function(n){this._ctlPurchasingInfo!==n&&(this._ctlPurchasingInfo=n)},get_ctlSalesInfo:function(){return this._ctlSalesInfo},set_ctlSalesInfo:function(n){this._ctlSalesInfo!==n&&(this._ctlSalesInfo=n)},get_ctlManufacturersSupplied:function(){return this._ctlManufacturersSupplied},set_ctlManufacturersSupplied:function(n){this._ctlManufacturersSupplied!==n&&(this._ctlManufacturersSupplied=n)},get_intCompanyID:function(){return this._intCompanyID},set_intCompanyID:function(n){this._intCompanyID!==n&&(this._intCompanyID=n)},get_pnlAddress:function(){return this._pnlAddress},set_pnlAddress:function(n){this._pnlAddress!==n&&(this._pnlAddress=n)},get_lblAddress:function(){return this._lblAddress},set_lblAddress:function(n){this._lblAddress!==n&&(this._lblAddress=n)},get_pnlParentCompany:function(){return this._pnlParentCompany},set_pnlParentCompany:function(n){this._pnlParentCompany!==n&&(this._pnlParentCompany=n)},get_lblCompanyType:function(){return this._lblCompanyType},set_lblCompanyType:function(n){this._lblCompanyType!==n&&(this._lblCompanyType=n)},get_lblSupplierRating:function(){return this._lblSupplierRating},set_lblSupplierRating:function(n){this._lblSupplierRating!==n&&(this._lblSupplierRating=n)},get_ctlRatingCustomer:function(){return this._ctlRatingCustomer},set_ctlRatingCustomer:function(n){this._ctlRatingCustomer!==n&&(this._ctlRatingCustomer=n)},get_lblCustomerRating:function(){return this._lblCustomerRating},set_lblCustomerRating:function(n){this._lblCustomerRating!==n&&(this._lblCustomerRating=n)},get_ctlRatingSupplier:function(){return this._ctlRatingSupplier},set_ctlRatingSupplier:function(n){this._ctlRatingSupplier!==n&&(this._ctlRatingSupplier=n)},get_pnlImportantNotes:function(){return this._pnlImportantNotes},set_pnlImportantNotes:function(n){this._pnlImportantNotes!==n&&(this._pnlImportantNotes=n)},get_lblImportantNotes:function(){return this._lblImportantNotes},set_lblImportantNotes:function(n){this._lblImportantNotes!==n&&(this._lblImportantNotes=n)},get_pnlOnStop:function(){return this._pnlOnStop},set_pnlOnStop:function(n){this._pnlOnStop!==n&&(this._pnlOnStop=n)},get_lblCompanyIsSanctioned:function(){return this._lblCompanyIsSanctioned},set_lblCompanyIsSanctioned:function(n){this._lblCompanyIsSanctioned!==n&&(this._lblCompanyIsSanctioned=n)},get_lblCompanyOnStop:function(){return this._lblCompanyOnStop},set_lblCompanyOnStop:function(n){this._lblCompanyOnStop!==n&&(this._lblCompanyOnStop=n)},get_btnTransactions:function(){return this._btnTransactions},set_btnTransactions:function(n){this._btnTransactions!==n&&(this._btnTransactions=n)},get_ctlCMPDocuments:function(){return this._ctlCMPDocuments},set_ctlCMPDocuments:function(n){this._ctlCMPDocuments!==n&&(this._ctlCMPDocuments=n)},get_ctlCMPDocumentsNew:function(){return this._ctlCMPDocumentsNew},set_ctlCMPDocumentsNew:function(n){this._ctlCMPDocumentsNew!==n&&(this._ctlCMPDocumentsNew=n)},get_pnlCertificate:function(){return this._pnlCertificate},set_pnlCertificate:function(n){this._pnlCertificate!==n&&(this._pnlCertificate=n)},get_ctlCompanyCertificate:function(){return this._ctlCompanyCertificate},set_ctlCompanyCertificate:function(n){this._ctlCompanyCertificate!==n&&(this._ctlCompanyCertificate=n)},get_lblInsurance:function(){return this._lblInsurance},set_lblInsurance:function(n){this._lblInsurance!==n&&(this._lblInsurance=n)},get_pnlInsurance:function(){return this._pnlInsurance},set_pnlInsurance:function(n){this._pnlInsurance!==n&&(this._pnlInsurance=n)},get_lblInsuranceFileNo:function(){return this._lblInsuranceFileNo},set_lblInsuranceFileNo:function(n){this._lblInsuranceFileNo!==n&&(this._lblInsuranceFileNo=n)},get_strStopStatus:function(){return this._strStopStatus},set_strStopStatus:function(n){this._strStopStatus!==n&&(this._strStopStatus=n)},get_blnEditHubSupplier:function(){return this._blnEditHubSupplier},set_blnEditHubSupplier:function(n){this._blnEditHubSupplier!==n&&(this._blnEditHubSupplier=n)},get_IsGlobalLogin:function(){return this._IsGlobalLogin},set_IsGlobalLogin:function(n){this._IsGlobalLogin!==n&&(this._IsGlobalLogin=n)},get_pnlAPICustomer:function(){return this._pnlAPICustomer},set_pnlAPICustomer:function(n){this._pnlAPICustomer!==n&&(this._pnlAPICustomer=n)},get_ctlCompanyAPICustomer:function(){return this._ctlCompanyAPICustomer},set_ctlCompanyAPICustomer:function(n){this._ctlCompanyAPICustomer!==n&&(this._ctlCompanyAPICustomer=n)},get_pnlOnPremierCustomer:function(){return this._pnlOnPremierCustomer},set_pnlOnPremierCustomer:function(n){this._pnlOnPremierCustomer!==n&&(this._pnlOnPremierCustomer=n)},get_pnlOnTier2PremierCustomer:function(){return this._pnlOnTier2PremierCustomer},set_pnlOnTier2PremierCustomer:function(n){this._pnlOnTier2PremierCustomer!==n&&(this._pnlOnTier2PremierCustomer=n)},get_ctlCRMProspects:function(){return this._ctlCRMProspects},set_ctlCRMProspects:function(n){this._ctlCRMProspects!==n&&(this._ctlCRMProspects=n)},get_ctlGSA:function(){return this._ctlGSA},set_ctlGSA:function(n){this._ctlGSA!==n&&(this._ctlGSA=n)},get_ctlFinanceInfo:function(){return this._ctlFinanceInfo},set_ctlFinanceInfo:function(n){this._ctlFinanceInfo!==n&&(this._ctlFinanceInfo=n)},initialize:function(){Rebound.GlobalTrader.Site.Pages.Contact.CompanyDetail.callBaseMethod(this,"initialize")},goInit:function(){this._ctlMainCompanyInfo&&this._ctlMainCompanyInfo.addGetDataComplete(Function.createDelegate(this,this.ctlMainCompanyInfo_GetDataComplete));this._ctlAddresses&&this._ctlAddresses.addGetDataComplete(Function.createDelegate(this,this.ctlAddresses_GetDataComplete));this._ctlPurchasingInfo&&this._ctlPurchasingInfo.addGetDataComplete(Function.createDelegate(this,this.ctlPurchasingInfo_GetDataComplete));this._ctlSalesInfo&&this._ctlSalesInfo.addGetDataComplete(Function.createDelegate(this,this.ctlSalesInfo_GetDataComplete));this._ctlMainCompanyInfo&&this._ctlMainCompanyInfo.addInactiveCompanyComplete(Function.createDelegate(this,this._ctlMainCompanyInfo_InactiveCompanyComplete));this._ctlCMPDocuments&&this._ctlCMPDocuments.getData();this._ctlCMPDocumentsNew&&this._ctlCMPDocumentsNew.getData();this._ctlMainCompanyInfo?this.changeTab(this._intCurrentTab):($R_FN.showElement(this._pnlParentCompany,!1),$R_FN.showElement(this._pnlAddress,!1),$R_FN.showElement(this._pnlCompanyType,!1),$R_FN.showElement(this._pnlRatings,!1),$R_FN.showElement(this._pnlImportantNotes,!1),$R_FN.showElement(this._pnlOnStop,!1),$R_FN.showElement(this._pnlOnPremierCustomer,!1),$R_FN.showElement(this._pnlOnTier2PremierCustomer,!1),$R_FN.showElement(this._lblCompanyIsSanctioned,!1),$R_FN.showElement(this._lblCompanyOnStop,!1));Rebound.GlobalTrader.Site.Pages.Contact.CompanyDetail.callBaseMethod(this,"goInit")},dispose:function(){this.isDisposed||(this._element&&$clearHandlers(this._element),this._ctlPageTitle&&this._ctlPageTitle.dispose(),this._ctlMainCompanyInfo&&this._ctlMainCompanyInfo.dispose(),this._ctlContactLog&&this._ctlContactLog.dispose(),this._ctlTransactions&&this._ctlTransactions.dispose(),this._ctlAddresses&&this._ctlAddresses.dispose(),this._ctlPurchasingInfo&&this._ctlPurchasingInfo.dispose(),this._ctlSalesInfo&&this._ctlSalesInfo.dispose(),this._ctlManufacturersSupplied&&this._ctlManufacturersSupplied.dispose(),this._ctlRatingCustomer&&this._ctlRatingCustomer.dispose(),this._ctlRatingSupplier&&this._ctlRatingSupplier.dispose(),this._btnTransactions&&this._btnTransactions.dispose(),this._ctlCMPDocuments&&this._ctlCMPDocuments.dispose(),this._ctlCMPDocumentsNew&&this._ctlCMPDocumentsNew.dispose(),this._ctlCompanyCertificate&&this._ctlCompanyCertificate.dispose(),this._ctlCompanyAPICustomer&&this._ctlCompanyAPICustomer.dispose(),this._ctlPageTitle=null,this._pnlMain=null,this._pnlDetail=null,this._ctlMainCompanyInfo=null,this._ctlContactLog=null,this._ctlTransactions=null,this._ctlAddresses=null,this._ctlPurchasingInfo=null,this._ctlSalesInfo=null,this._ctlManufacturersSupplied=null,this._pnlAddress=null,this._lblAddress=null,this._pnlParentCompany=null,this._lblCompanyType=null,this._lblSupplierRating=null,this._ctlRatingCustomer=null,this._lblCustomerRating=null,this._ctlRatingSupplier=null,this._pnlImportantNotes=null,this._lblImportantNotes=null,this._pnlOnStop=null,this._lblCompanyOnStop=null,this._lblCompanyIsSanctioned=null,this._pnlOnPremierCustomer=null,this._pnlOnTier2PremierCustomer=null,this._btnTransactions=null,this._aryGotTabData=null,this._ctlCMPDocuments=null,this._ctlCMPDocumentsNew=null,this._pnlCertificate=null,this._pnlAPICustomer=null,this._ctlCompanyCertificate=null,this.ctlCompanyAPICustomer=null,this._lblInsurance=null,this._pnlInsurance=null,this.lblInsuranceFileNo=null,this._strStopStatus=null,this._blnEditHubSupplier=null,this._IsGlobalLogin=null,Rebound.GlobalTrader.Site.Pages.Contact.CompanyDetail.callBaseMethod(this,"dispose"))},changeTab:function(n){this._intCurrentTab=n;this._ctlPageTitle.selectTab(n);$R_FN.showElement(this._pnlMain,n==0);$R_FN.showElement(this._pnlDetail,n==1);$R_FN.showElement(this._pnlCertificate,n==2);$R_FN.showElement(this._pnlAPICustomer,n==3);this._aryGotTabData[n]||(n==0?(this._ctlMainCompanyInfo.getData(),this._ctlContactLog.getData(),this._ctlAddresses.initialGetData(),this._ctlSalesInfo.getData(),this._ctlPurchasingInfo.getData()):n==1?(this._ctlSalesInfo.getData(),this._ctlPurchasingInfo.getData(),this._ctlTransactions.initialGetData(),this._ctlManufacturersSupplied.getData()):(this._ctlCompanyCertificate.getData(),this._ctlCompanyAPICustomer.getData()),this._aryGotTabData[n]=!0)},ctlContactList_SelectContact:function(){this._intContactID=this._ctlContactList._intContactID},ctlMainCompanyInfo_GetDataComplete:function(){var n=this._ctlMainCompanyInfo.getFieldValue("hidGlobalClientNo");this._ctlAddresses._globalLoginClientNo=n;this._ctlSalesInfo._globalLoginClientNo=n;this._ctlPurchasingInfo._globalLoginClientNo=n;this._ctlManufacturersSupplied._globalLoginClientNo=n;this._ctlTransactions._globalLoginClientNo=n;this._ctlCompanyCertificate._globalLoginClientNo=n;this._ctlCompanyAPICustomer._globalLoginClientNo=n;this.updateCompanyName();this.updateParentCo();this.updateCompanyType();this.updateImportantNotes();this.updateOnPremierCustomer();this.updateTier2OnPremierCustomer();this._ctlMainCompanyInfo.enableButtons(this._blnEditHubSupplier);this.updateOnStop();this._btnTransactions&&(this._btnTransactions._blnApprovedForPOs=Boolean.parse(this._ctlMainCompanyInfo.getFieldValue("ctlhidSupplier"))&&!Boolean.parse(this._ctlMainCompanyInfo.getFieldValue("hidSupplierOnStop")),this._btnTransactions.updateApproval())},ctlSalesInfo_GetDataComplete:function(){this.updateInsuranceDetails();this.updateCustomerRating();this.updateRatingsPanelVisibility();this.updateOnStop();this._btnTransactions&&(this._btnTransactions._blnApprovedForSOs=Boolean.parse(this._ctlSalesInfo.getFieldValue("hidIsApproved")),this._btnTransactions._blnApprovedForPOs=Boolean.parse(this._ctlMainCompanyInfo.getFieldValue("ctlhidSupplier"))&&!Boolean.parse(this._ctlMainCompanyInfo.getFieldValue("hidSupplierOnStop")),this._ctlPurchasingInfo.getFieldValue("hidSupOnStop")&&(this._btnTransactions._blnOnStop=Boolean.parse(this._ctlPurchasingInfo.getFieldValue("hidSupOnStop"))),this._btnTransactions.updateApproval())},ctlPurchasingInfo_GetDataComplete:function(){this.updateSupplierRating();this.updateRatingsPanelVisibility();this._btnTransactions&&(this._btnTransactions._blnOnStop=Boolean.parse(this._ctlPurchasingInfo.getFieldValue("hidSupOnStop")),this._btnTransactions._blnApprovedForPOs=Boolean.parse(this._ctlMainCompanyInfo.getFieldValue("ctlhidSupplier"))&&!Boolean.parse(this._ctlMainCompanyInfo.getFieldValue("hidSupplierOnStop")),this._btnTransactions.updateApproval());this._ctlPurchasingInfo&&this._ctlPurchasingInfo.enableButtons(this._blnEditHubSupplier)},ctlAddresses_GetDataComplete:function(){this.updateAddress();this._ctlAddresses&&(this._ctlAddresses._blnEditHubSupplier=this._blnEditHubSupplier,this._ctlAddresses.enableEditButtons(!0))},_ctlMainCompanyInfo_InactiveCompanyComplete:function(){this._ctlMainCompanyInfo&&this._ctlMainCompanyInfo.getData();this._ctlManufacturersSupplied&&this._ctlManufacturersSupplied.getCompanyInactive();this._ctlAddresses&&this._ctlAddresses.getCompanyInactive();this._ctlCRMProspects&&this._ctlCRMProspects.getCompanyInactive();this._ctlContactLog&&this._ctlContactLog.getCompanyInactive();this._ctlGSA&&this._ctlGSA.getCompanyInactive();this._ctlSalesInfo&&this._ctlSalesInfo.getData();this._ctlPurchasingInfo&&this._ctlPurchasingInfo.getData();this._ctlFinanceInfo&&this._ctlFinanceInfo.getCompanyInactive();this._ctlCompanyCertificate&&this._ctlCompanyCertificate.getCompanyInactive();this._ctlCompanyCertificate&&this._ctlCompanyCertificate.getData();this._ctlCompanyAPICustomer&&this._ctlCompanyAPICustomer.getCompanyInactive()},updateCompanyName:function(){var n=$R_FN.setCleanTextValue(this._ctlMainCompanyInfo.getFieldValue("ctlCompanyName"));n=n+" ("+this._ctlMainCompanyInfo._intCompanyID+")  "+this._strStopStatus;this._ctlPageTitle.updateTitle(n);this._ctlAddresses._strCompanyName=n;this._ctlPageTitle.updateTitleColor("pageTitleEARI",this._ctlMainCompanyInfo.getFieldValue("ctlEARIReported"))},updateParentCo:function(){var t=$R_FN.setCleanTextValue(this._ctlMainCompanyInfo.getFieldValue("hidParentCompany")),n=this._ctlMainCompanyInfo.getFieldValue("hidParentCompanyID");$R_FN.showElement(this._pnlParentCompany,n>0);$R_FN.setInnerHTML(this._hypParentCompany,t);this._hypParentCompany&&(this._hypParentCompany.href=$RGT_gotoURL_Company(n))},updateAddress:function(){var n=$R_FN.setCleanTextValue(this._ctlAddresses._strDefaultAddress);$R_FN.showElement(this._pnlAddress,n.length>0);$R_FN.setInnerHTML(this._lblAddress,n)},updateCompanyType:function(){var n=$R_FN.setCleanTextValue(this._ctlMainCompanyInfo.getFieldValue("ctlCompanyType"));$R_FN.showElement(this._pnlCompanyType,n.length>0);$R_FN.setInnerHTML(this._lblCompanyType,n)},updateSupplierRating:function(){this._ctlRatingSupplier.setRating(this._ctlPurchasingInfo.getFieldValue("ctlRating"));$R_FN.showElement(this._lblSupplierRating,Boolean.parse(this._ctlPurchasingInfo.getFieldValue("hidIsApproved")))},updateCustomerRating:function(){this._ctlRatingCustomer.setRating(this._ctlSalesInfo.getFieldValue("ctlRating"));$R_FN.showElement(this._lblCustomerRating,Boolean.parse(this._ctlSalesInfo.getFieldValue("hidIsApproved")))},updateRatingsPanelVisibility:function(){$R_FN.showElement(this._pnlRatings,$R_FN.isElementVisible(this._lblSupplierRating)||$R_FN.isElementVisible(this._lblCustomerRating))},updateOnStop:function(){$R_FN.showElement(this._pnlOnStop,this._ctlSalesInfo.getFieldValue("ctlIsOnStop")||this._ctlMainCompanyInfo.getFieldValue("ctlSanction"))},updateOnPremierCustomer:function(){$R_FN.showElement(this._pnlOnPremierCustomer,this._ctlMainCompanyInfo.getFieldValue("ctlPremierCustomer"))},updateTier2OnPremierCustomer:function(){$R_FN.showElement(this._pnlOnTier2PremierCustomer,this._ctlMainCompanyInfo.getFieldValue("ctlTier2PremierCustomer"))},updateImportantNotes:function(){var n=MainInfoCompanyImportantNotes;n||(n="");$R_FN.showElement(this._pnlImportantNotes,n.length>0);$R_FN.setInnerHTML(this._lblImportantNotes,n)},updateInsuranceDetails:function(){var t=this._ctlSalesInfo.getFieldValue("ctlInsuranceFileNo"),i=this._ctlSalesInfo.getFieldValue("ctlInsuredAmount"),n;$R_FN.showElement(this._pnlInsurance,t.length>0);$R_FN.setInnerHTML(this._lblInsurance,i);$R_FN.setInnerHTML(this.lblInsuranceFileNo,t);n=$R_FN.setCleanTextValue(this._ctlMainCompanyInfo.getFieldValue("ctlCompanyName"));n=n+" ("+this._ctlMainCompanyInfo._intCompanyID+")  "+this._strStopStatus;this._ctlPageTitle.updateTitle(n)}};Rebound.GlobalTrader.Site.Pages.Contact.CompanyDetail.registerClass("Rebound.GlobalTrader.Site.Pages.Contact.CompanyDetail",Rebound.GlobalTrader.Site.Pages.Content,Sys.IDisposable);