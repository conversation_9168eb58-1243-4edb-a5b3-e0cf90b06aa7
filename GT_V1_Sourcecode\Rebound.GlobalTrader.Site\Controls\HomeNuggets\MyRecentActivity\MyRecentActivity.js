Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.HomeNuggets");Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyRecentActivity=function(n){Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyRecentActivity.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyRecentActivity.prototype={get_pnlCR:function(){return this._pnlCR},set_pnlCR:function(n){this._pnlCR!==n&&(this._pnlCR=n)},get_pnlGI:function(){return this._pnlGI},set_pnlGI:function(n){this._pnlGI!==n&&(this._pnlGI=n)},get_pnlQU:function(){return this._pnlQU},set_pnlQU:function(n){this._pnlQU!==n&&(this._pnlQU=n)},get_pnlPO:function(){return this._pnlPO},set_pnlPO:function(n){this._pnlPO!==n&&(this._pnlPO=n)},get_pnlSO:function(){return this._pnlSO},set_pnlSO:function(n){this._pnlSO!==n&&(this._pnlSO=n)},get_pnlCRMA:function(){return this._pnlCRMA},set_pnlCRMA:function(n){this._pnlCRMA!==n&&(this._pnlCRMA=n)},get_pnlSRMA:function(){return this._pnlSRMA},set_pnlSRMA:function(n){this._pnlSRMA!==n&&(this._pnlSRMA=n)},get_pnlCredit:function(){return this._pnlCredit},set_pnlCredit:function(n){this._pnlCredit!==n&&(this._pnlCredit=n)},get_pnlDebit:function(){return this._pnlDebit},set_pnlDebit:function(n){this._pnlDebit!==n&&(this._pnlDebit=n)},get_pnlInv:function(){return this._pnlInv},set_pnlInv:function(n){this._pnlInv!==n&&(this._pnlInv=n)},get_pnlOsPoApproval:function(){return this._pnlOsPoApproval},set_pnlOsPoApproval:function(n){this._pnlOsPoApproval!==n&&(this._pnlOsPoApproval=n)},get_tblCR:function(){return this._tblCR},set_tblCR:function(n){this._tblCR!==n&&(this._tblCR=n)},get_tblGI:function(){return this._tblGI},set_tblGI:function(n){this._tblGI!==n&&(this._tblGI=n)},get_tblQU:function(){return this._tblQU},set_tblQU:function(n){this._tblQU!==n&&(this._tblQU=n)},get_tblPO:function(){return this._tblPO},set_tblPO:function(n){this._tblPO!==n&&(this._tblPO=n)},get_tblSO:function(){return this._tblSO},set_tblSO:function(n){this._tblSO!==n&&(this._tblSO=n)},get_tblCRMA:function(){return this._tblCRMA},set_tblCRMA:function(n){this._tblCRMA!==n&&(this._tblCRMA=n)},get_tblSRMA:function(){return this._tblSRMA},set_tblSRMA:function(n){this._tblSRMA!==n&&(this._tblSRMA=n)},get_tblCredit:function(){return this._tblCredit},set_tblCredit:function(n){this._tblCredit!==n&&(this._tblCredit=n)},get_tblDebit:function(){return this._tblDebit},set_tblDebit:function(n){this._tblDebit!==n&&(this._tblDebit=n)},get_tblInv:function(){return this._tblInv},set_tblInv:function(n){this._tblInv!==n&&(this._tblInv=n)},get_tblOsPoApproval:function(){return this._tblOsPoApproval},set_tblOsPoApproval:function(n){this._tblOsPoApproval!==n&&(this._tblOsPoApproval=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyRecentActivity.callBaseMethod(this,"initialize");this.addRefreshEvent(Function.createDelegate(this,this.getData))},dispose:function(){this.isDisposed||(this._tblCR&&this._tblCR.dispose(),this._tblGI&&this._tblGI.dispose(),this._tblQU&&this._tblQU.dispose(),this._tblPO&&this._tblPO.dispose(),this._tblSO&&this._tblSO.dispose(),this._tblCRMA&&this._tblCRMA.dispose(),this._tblSRMA&&this._tblSRMA.dispose(),this._tblCredit&&this._tblCredit.dispose(),this._tblDebit&&this._tblDebit.dispose(),this._tblInv&&this._tblInv.dispose(),this._tblOsPoApproval&&this._tblOsPoApproval.dispose(),this._pnlCR=null,this._pnlGI=null,this._pnlQU=null,this._pnlPO=null,this._pnlSO=null,this._pnlCRMA=null,this._pnlSRMA=null,this._pnlCredit=null,this._pnlDebit=null,this._pnlInv=null,this._pnlOsPoApproval=null,this._tblCR=null,this._tblGI=null,this._tblQU=null,this._tblPO=null,this._tblSO=null,this._tblCRMA=null,this._tblSRMA=null,this._tblCredit=null,this._tblDebit=null,this._tblInv=null,this._tblOsPoApproval=null,Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyRecentActivity.callBaseMethod(this,"dispose"))},setupLoadingState:function(){$R_FN.showElement(this._pnlCR,!1);$R_FN.showElement(this._pnlGI,!1);$R_FN.showElement(this._pnlQU,!1);$R_FN.showElement(this._pnlPO,!1);$R_FN.showElement(this._pnlSO,!1);$R_FN.showElement(this._pnlCRMA,!1);$R_FN.showElement(this._pnlSRMA,!1);$R_FN.showElement(this._pnlCredit,!1);$R_FN.showElement(this._pnlDebit,!1);$R_FN.showElement(this._pnlInv,!1);$R_FN.showElement(this._pnlOsPoApproval,!1);Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyRecentActivity.callBaseMethod(this,"setupLoadingState")},getData:function(){this.setupLoadingState();var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/HomeNuggets/MyRecentActivity");n.set_DataObject("MyRecentActivity");n.set_DataAction("GetData");n.addParameter("rowcount",this._intRowCount);n.addParameter("OtherLoginID",this._intLoginID_Other);n.addDataOK(Function.createDelegate(this,this.getDataComplete));n.addError(Function.createDelegate(this,this.homeNuggetDataError));n.addTimeout(Function.createDelegate(this,this.homeNuggetDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getDataComplete:function(n){var r=n._result,u,t,i;for(this.showNoneFoundOrContent(r.Count),this._tblCR.clearTable(),i=0;i<r.CRActivity.length;i++)t=r.CRActivity[i],u=[$RGT_nubButton_CustomerRequirement(t.ID,t.No),$RGT_nubButton_Company(t.CMNo,t.CM),t.Date],this._tblCR.addRow(u,null),t=null;for($R_FN.showElement(this._pnlCR,r.CRActivity.length>0),this._tblGI.clearTable(),i=0;i<r.GIActivity.length;i++)t=r.GIActivity[i],u=[$RGT_nubButton_GoodsIn(t.ID,t.No),$RGT_nubButton_Company(t.CMNo,t.CM),t.Date],this._tblGI.addRow(u,null),t=null;for($R_FN.showElement(this._pnlGI,r.GIActivity.length>0),this._tblQU.clearTable(),i=0;i<r.QUActivity.length;i++)t=r.QUActivity[i],u=[$RGT_nubButton_Quote(t.ID,t.No),$RGT_nubButton_Company(t.CMNo,t.CM),t.Date],this._tblQU.addRow(u,null),t=null;for($R_FN.showElement(this._pnlQU,r.QUActivity.length>0),this._tblPO.clearTable(),i=0;i<r.POActivity.length;i++)t=r.POActivity[i],u=[$RGT_nubButton_PurchaseOrder(t.ID,t.No),$RGT_nubButton_Company(t.CMNo,t.CM),t.Date],this._tblPO.addRow(u,null),t=null;for($R_FN.showElement(this._pnlPO,r.POActivity.length>0),this._tblSO.clearTable(),i=0;i<r.SOActivity.length;i++)t=r.SOActivity[i],u=[$RGT_nubButton_SalesOrder(t.ID,t.No),$RGT_nubButton_Company(t.CMNo,t.CM),t.Date],this._tblSO.addRow(u,null),t=null;for($R_FN.showElement(this._pnlSO,r.SOActivity.length>0),this._tblCRMA.clearTable(),i=0;i<r.CRMAActivity.length;i++)t=r.CRMAActivity[i],u=[$RGT_nubButton_CRMA(t.ID,t.No),$RGT_nubButton_Company(t.CMNo,t.CM),t.Date],this._tblCRMA.addRow(u,null),t=null;for($R_FN.showElement(this._pnlCRMA,r.CRMAActivity.length>0),this._tblSRMA.clearTable(),i=0;i<r.SRMAActivity.length;i++)t=r.SRMAActivity[i],u=[$RGT_nubButton_SRMA(t.ID,t.No),$RGT_nubButton_Company(t.CMNo,t.CM),t.Date],this._tblSRMA.addRow(u,null),t=null;for($R_FN.showElement(this._pnlSRMA,r.SRMAActivity.length>0),this._tblCredit.clearTable(),i=0;i<r.CreditActivity.length;i++)t=r.CreditActivity[i],u=[$RGT_nubButton_CreditNote(t.ID,t.No),$RGT_nubButton_Company(t.CMNo,t.CM),t.Date],this._tblCredit.addRow(u,null),t=null;for($R_FN.showElement(this._pnlCredit,r.CreditActivity.length>0),this._tblDebit.clearTable(),i=0;i<r.DebitActivity.length;i++)t=r.DebitActivity[i],u=[$RGT_nubButton_DebitNote(t.ID,t.No),$RGT_nubButton_Company(t.CMNo,t.CM),t.Date],this._tblDebit.addRow(u,null),t=null;for($R_FN.showElement(this._pnlDebit,r.DebitActivity.length>0),this._tblInv.clearTable(),i=0;i<r.InvoiceActivity.length;i++)t=r.InvoiceActivity[i],u=[$RGT_nubButton_Invoice(t.ID,t.No),$RGT_nubButton_Company(t.CMNo,t.CM),t.Date],this._tblInv.addRow(u,null),t=null;for($R_FN.showElement(this._pnlInv,r.InvoiceActivity.length>0),this._tblOsPoApproval.clearTable(),i=0;i<r.POSupplierApprovalActivity.length;i++)t=r.POSupplierApprovalActivity[i],u=[$RGT_nubButton_PurchaseOrder(t.ID,t.No),$RGT_nubButton_Company(t.CMNo,t.CM),t.Date],this._tblOsPoApproval.addRow(u,null),t=null;$R_FN.showElement(this._pnlOsPoApproval,r.POSupplierApprovalActivity.length>0);this.hideLoading()}};Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyRecentActivity.registerClass("Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyRecentActivity",Rebound.GlobalTrader.Site.Controls.HomeNuggets.Base,Sys.IDisposable);