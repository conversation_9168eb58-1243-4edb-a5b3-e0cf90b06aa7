using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site.Controls;

namespace Rebound.GlobalTrader.Site.Controls.ItemSearch {
	public partial class Service : Base {

		private int _intMaxResults = 50;
		public int MaxResults {
			get { return _intMaxResults; }
			set { _intMaxResults = value; }
		}

		protected override void OnInit(EventArgs e) {
			base.OnInit(e);
			SetItemSearchType("Service");
			AddScriptReference("Controls.ItemSearch.Service.Service.js");
		}

		protected override void OnLoad(EventArgs e) {
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.ItemSearch.Service", ctlDesignBase.ClientID);
			_scScriptControlDescriptor.AddProperty("intMaxResults", _intMaxResults);
			base.OnLoad(e);
		}

		protected override void OnPreRender(EventArgs e) {
			ctlDesignBase.MakeChildControls();
			ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("Service", WidthManager.GetWidth(WidthManager.ColumnWidth.ListName), true));
			ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("Description", Unit.Empty, true));
			ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("Cost", WidthManager.GetWidth(WidthManager.ColumnWidth.CurrencyValue), true));
			ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("Price", WidthManager.GetWidth(WidthManager.ColumnWidth.CurrencyValue), true));
			base.OnPreRender(e);
		}

	}
}