Type.registerNamespace("Rebound.GlobalTrader.Site.Pages.Setup");Rebound.GlobalTrader.Site.Pages.Setup.GS_Product=function(n){Rebound.GlobalTrader.Site.Pages.Setup.GS_Product.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Pages.Setup.GS_Product.prototype={get_ctlProduct:function(){return this._ctlProduct},set_ctlProduct:function(n){this._ctlProduct!==n&&(this._ctlProduct=n)},get_ctlRateHistory:function(){return this._ctlRateHistory},set_ctlRateHistory:function(n){this._ctlRateHistory!==n&&(this._ctlRateHistory=n)},get_ctlProductName:function(){return this._ctlProductName},set_ctlProductName:function(n){this._ctlProductName!==n&&(this._ctlProductName=n)},get_ctlGlobalProductMainCategory:function(){return this._ctlGlobalProductMainCategory},set_ctlGlobalProductMainCategory:function(n){this._ctlGlobalProductMainCategory!==n&&(this._ctlGlobalProductMainCategory=n)},initialize:function(){Rebound.GlobalTrader.Site.Pages.Setup.GS_Product.callBaseMethod(this,"initialize")},goInit:function(){this._ctlProduct.addSelectProduct(Function.createDelegate(this,this.ctlProduct_SelectProduct));this._ctlProductName.addSelectProduct(Function.createDelegate(this,this.ctlProduct_SelectProductName));this._ctlProductName.addSearchClick(Function.createDelegate(this,this.ctlProduct_Search));this._ctlGlobalProductMainCategory.addSelectProduct(Function.createDelegate(this,this.ctlGlobalProductMainCategory_SelectProductName));this._ctlGlobalProductMainCategory.addSearchClick(Function.createDelegate(this,this.ctlGlobalProductMainCategory_Search));Rebound.GlobalTrader.Site.Pages.Setup.GS_Product.callBaseMethod(this,"goInit")},dispose:function(){this.isDisposed||(this._ctlProduct&&this._ctlProduct.dispose(),this._ctlRateHistory&&this._ctlRateHistory.dispose(),this._ctlProductName&&this._ctlProductName.dispose(),this._ctlProduct=null,this._ctlRateHistory=null,this._ctlProductName=null,Rebound.GlobalTrader.Site.Pages.Setup.GS_Product.callBaseMethod(this,"dispose"))},ctlProduct_SelectProduct:function(){this._ctlRateHistory._intProductID=this._ctlProduct._intProductID;this._ctlRateHistory.show(!0);this._ctlProduct._tbl.resizeColumns();this._ctlRateHistory.refresh()},ctlProduct_SelectProductName:function(){this._ctlProduct._intProductNameID=this._ctlProductName._intProductNameEditID;this._ctlProduct.show(!0);this._ctlRateHistory.show(!1);this._ctlProductName._tbl.resizeColumns();this._ctlProduct.refresh()},ctlProduct_Search:function(){this._ctlProductName.ctlProduct=this._ctlProduct;this._ctlProductName.getData();this._ctlProduct.show(!1)},ctlGlobalProductMainCategory_SelectProductName:function(){this._ctlProductName._intProductNameID=this._ctlGlobalProductMainCategory._intProductNameID;this._ctlProductName._stringProductType=this._ctlGlobalProductMainCategory._stringProductType;this._ctlGlobalProductMainCategory.show(!0);this._ctlProductName.show(!0);this._ctlRateHistory.show(!1);this._ctlProduct.show(!1);this._ctlGlobalProductMainCategory._tbl.resizeColumns();this._ctlProductName.refresh()},ctlGlobalProductMainCategory_Search:function(){this._ctlGlobalProductMainCategory.getData();this._ctlGlobalProductMainCategory.show(!0)}};Rebound.GlobalTrader.Site.Pages.Setup.GS_Product.registerClass("Rebound.GlobalTrader.Site.Pages.Setup.GS_Product",Rebound.GlobalTrader.Site.Pages.Content,Sys.IDisposable);