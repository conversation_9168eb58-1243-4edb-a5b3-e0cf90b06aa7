﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

/* 
===========================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-229093]		Ngai To				21-Jan-2024		Update			229093: Quote - Add traffic light colouring coding based on date offered
===========================================================================================
*/
CREATE OR ALTER PROCEDURE [dbo].[usp_select_Quote_for_Print]
	--  
	@QuoteId INT
	--  
AS
BEGIN
	DECLARE @QuoteStatusNo INT = NULL;

	SELECT TOP 1 @QuoteStatusNo = CASE 
			WHEN isnull(QuoteStatus, 0) = 4
				THEN 1
			ELSE QuoteStatus
			END
	FROM tbQuote
	WHERE QuoteId = @QuoteId

	-- Update Quote Status Pending to Offer:  
	UPDATE tbQuote
	SET QuoteStatus = @QuoteStatusNo
	WHERE QuoteId = @QuoteId

	IF @QuoteStatusNo = 1 OR @QuoteStatusNo = 6 -- Offered - Partially Offered
	BEGIN
		UPDATE tbQuote
		SET QuoteOfferedDate = CURRENT_TIMESTAMP
		WHERE QuoteId = @QuoteId
	END

	SELECT  a.*  
		, b.EMail AS ContactEmail 
		,(select FooterText from tbSystemDocumentFooterHistory FTH where FTH.SystemDocumentFooterHistoryId=a.SysDocAS9120HistoryNo)  as SysDocAS9120HistoryText
		,(select FooterText from tbSystemDocumentFooterHistory FTH where FTH.SystemDocumentFooterHistoryId=a.SysDocHazardousHistoryNo)  as SysDocHazardousHistoryText
		,(select FooterText from tbSystemDocumentFooterHistory FTH where FTH.SystemDocumentFooterHistoryId=a.SysDocCOOHistoryNo)  as SysDocCOOHistoryText 
	FROM    dbo.vwQuote a  
	JOIN    dbo.tbContact b ON a.ContactNo = b.ContactId  
	WHERE   a.QuoteId = @QuoteId  
END
GO


