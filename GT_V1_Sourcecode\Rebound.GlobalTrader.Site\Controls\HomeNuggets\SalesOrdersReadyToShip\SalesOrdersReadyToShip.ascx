<%@ Control Language="C#" CodeBehind="SalesOrdersReadyToShip.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.HomeNuggets.SalesOrdersReadyToShip" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Nugget:DesignBase id="ctlDB" runat="server">
	<content>
		<div class="homepageNugget">
		    <asp:Panel ID="pnlReady" runat="server">
			    <h5><%=Functions.GetGlobalResource("misc", "Ready")%></h5>
			    <ReboundUI:SimpleDataTable ID="tblReady" runat="server" AllowSelection="false" />
		    </asp:Panel>
		    <asp:Panel ID="pnlMore" runat="server" CssClass="homeNuggetMoreLink">
				<ReboundUI:PageHyperLink id="lnkMore" runat="server" PageType="Warehouse_ShipSalesOrderBrowse" OverrideTextResource="MoreSalesOrdersReadyToShip" CssClass="nubButton nubButtonAlignLeft" />
			</asp:Panel>
		</div>
	</content>
</ReboundUI_Nugget:DesignBase>
