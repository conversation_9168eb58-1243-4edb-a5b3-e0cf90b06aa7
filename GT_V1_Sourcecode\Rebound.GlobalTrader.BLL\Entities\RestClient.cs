﻿using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;
using Rebound.GlobalTrader.DAL;
using Rebound.GlobalTrader.BLL;
using System.IO;
using System.Net;
using System.Text;
using IHSPart;

public enum HttpVerb
{
    GET,
    POST,
    PUT,
    DELETE
}

namespace HttpUtils
{
    public class RestClient
    {
        public string EndPoint { get; set; }
        public HttpVerb Method { get; set; }
        public string ContentType { get; set; }
        public string PostData { get; set; }
        public string tokenRespose { get; set; }
        string errormessage = string.Empty;
        string errormessage1 = string.Empty;

        public RestClient()
        {
            EndPoint = "";
            Method = HttpVerb.GET;
            ContentType = "application/json";
            PostData = "";
        }
        public RestClient(string endpoint)
        {
            EndPoint = endpoint;
            Method = HttpVerb.GET;
            ContentType = "application/json";
            PostData = "";
        }
        public RestClient(string endpoint, HttpVerb method)
        {
            EndPoint = endpoint;
            Method = method;
            ContentType = "application/json";
            PostData = "";
        }

        public RestClient(string endpoint, HttpVerb method, string postData)
        {
            EndPoint = endpoint;
            Method = method;
            ContentType = "application/json";
            PostData = postData;
        }


        public string MakeRequest()
        {
            return MakeRequest("");
        }

        public string MakeRequest(string parameters)
        {
            
            var request = (HttpWebRequest)WebRequest.Create(EndPoint + parameters);
            request.Method = Method.ToString();
            request.ContentLength = 0;
            request.MaximumResponseHeadersLength = 65536;
            request.ContentType = ContentType;

            if (!string.IsNullOrEmpty(PostData) && Method == HttpVerb.POST)
            {
                var encoding = new UTF8Encoding();
                var bytes = Encoding.GetEncoding("iso-8859-1").GetBytes(PostData);
                request.ContentLength = bytes.Length;
                if (!string.IsNullOrEmpty(tokenRespose))
                {
                    request.Headers.Add("Authorization", "Bearer " + tokenRespose);
                }
                //else
                //{
                //    tokenRespose= HttpContext.Current.Session["token"].ToString();
                //}

                using (var writeStream = request.GetRequestStream())
                {
                    writeStream.Write(bytes, 0, bytes.Length);
                }
            }
            try
            {
                
                using (var response = (HttpWebResponse)request.GetResponse())
                {
                    var responseValue = string.Empty;

                    if (response.StatusCode != HttpStatusCode.OK)
                    {
                        var message = String.Format("Request failed. Received HTTP {0}", response.StatusCode);
                        throw new ApplicationException(message);
                    }

                    // grab the response
                    using (var responseStream = response.GetResponseStream())
                    {
                        if (responseStream != null)
                            using (var reader = new StreamReader(responseStream))
                            {
                                responseValue = reader.ReadToEnd();
                            }
                    }
                    return responseValue;
                }
            }
            catch (WebException e)
            {
                
                if (e.Status == WebExceptionStatus.ProtocolError)
                {
                   // errormessage = String.Format("Status Code : {0}", ((HttpWebResponse)e.Response).StatusCode);
                    //errormessage1 = String.Format("Status Code : {0}", ((HttpWebResponse)e.Response).StatusDescription);
                    // failed...
                    using (StreamReader r = new StreamReader(
                        e.Response.GetResponseStream()))
                    {
                        errormessage = r.ReadToEnd();
                        // ... do whatever ...
                    }
                    
                }
                return null;
            }
            catch (Exception e)
            {
                errormessage = e.Message;
                return null;
            }
            
        }

    } // class

}