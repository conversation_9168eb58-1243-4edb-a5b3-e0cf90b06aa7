﻿using Rebound.GlobalTrader.DAL;
using System;
using System.Collections.Generic;
using System.Data;
using System.Globalization;
//using Microsoft.ApplicationInsights;

namespace Rebound.GlobalTrader.BLL
{
    public partial class BOMManagerContract : BizObject
    {
        protected static DAL.BOMManagerElement Settings
        {
            get { return Globals.Settings.BOMManager; }
        }

        #region Properties
        public System.Int32 BOMManagerId { get; set; }
        public System.Int32 Line { get; set; }
        public System.Double LineValue { get; set; }
        public System.Boolean isIncludeAltPart { get; set; }
        /// <summary>
        /// CustomerRequirementId (from Table)
        /// </summary>
        public System.Int32 CustomerRequirementId { get; set; }
        /// <summary>
        /// CustomerRequirementNumber (from Table)
        /// </summary>
        public System.Int32 CustomerRequirementNumber { get; set; }
        public System.String IHSProduct { get; set; }
        public System.String IHSHTSCode { get; set; }
        public System.Int32? IHSProductNo { get; set; }
        public System.String IHSProductName { get; set; }
        public System.String IHSDutyCode { get; set; }
        public System.String PurchaseRequestId { get; set; }
        public System.String PurchaseRequestNumber { get; set; }
        public System.String ECCNCode { get; set; }
        public System.String REQStatusName { get; set; }
        public System.Int32? ParentRequirementNo { get; set; }
        public System.Int32? ParentRequirementId { get; set; }

        /// <summary>
        /// ClientNo (from Table)
        /// </summary>
        public System.Int32 ClientNo { get; set; }
        /// <summary>
        /// SupportTeamMemberNo
        /// </summary>
        public System.Int32? SupportTeamMemberNo { get; set; }
        /// <summary>
        /// SupportTeamMemberName
        /// </summary>
        public System.String SupportTeamMemberName { get; set; }
        /// <summary>
        /// FullPart (from Table)
        /// </summary>
        public System.String FullPart { get; set; }
        /// <summary>
        /// Part (from Table)
        /// </summary>
        public System.String Part { get; set; }
        /// <summary>
        /// ManufacturerNo (from Table)
        /// </summary>
        public System.Int32? ManufacturerNo { get; set; }
        /// <summary>
        /// DateCode (from Table)
        /// </summary>
        public System.String DateCode { get; set; }
        /// <summary>
        /// PackageNo (from Table)
        /// </summary>
        public System.Int32? PackageNo { get; set; }
        /// <summary>
        /// Quantity (from Table)
        /// </summary>
        public System.Int32 Quantity { get; set; }
        /// <summary>
        /// Price (from Table)
        /// </summary>
        public System.Double Price { get; set; }
        /// <summary>
        /// CurrencyNo (from Table)
        /// </summary>
        public double TotalBomManagerLinePrice { get; set; }
        public System.Int32? CurrencyNo { get; set; }
        /// <summary>
        /// ReceivedDate (from Table)
        /// </summary>
        public System.DateTime ReceivedDate { get; set; }
        /// <summary>
        /// Salesman (from Table)
        /// </summary>
        public System.Int32 Salesman { get; set; }
        /// <summary>
        /// DatePromised (from Table)
        /// </summary>
        public System.DateTime DatePromised { get; set; }
        /// <summary>
        /// Notes (from Table)
        /// </summary>
        public System.String Notes { get; set; }
        /// <summary>
        /// Instructions (from Table)
        /// </summary>
        public System.String Instructions { get; set; }
        /// <summary>
        /// Shortage (from Table)
        /// </summary>
        public System.Boolean Shortage { get; set; }
        /// <summary>
        /// CompanyNo (from Table)
        /// </summary>
        public System.Int32? CompanyNo { get; set; }
        /// <summary>
        /// ContactNo (from Table)
        /// </summary>
        public System.Int32? ContactNo { get; set; }
        /// <summary>
        /// Alternate (from Table)
        /// </summary>
        public System.Boolean Alternate { get; set; }
        /// <summary>
        /// OriginalCustomerRequirementNo (from Table)
        /// </summary>
        public System.Int32? OriginalCustomerRequirementNo { get; set; }
        /// <summary>
        /// ReasonNo (from Table)
        /// </summary>
        public System.Int32? ReasonNo { get; set; }
        /// <summary>
        /// ProductNo (from Table)
        /// </summary>
        public System.Int32? ProductNo { get; set; }
        /// <summary>
        /// CustomerPart (from Table)
        /// </summary>
        public System.String CustomerPart { get; set; }
        /// <summary>
        /// Closed (from Table)
        /// </summary>
        public System.Boolean Closed { get; set; }
        /// <summary>
        /// ROHS (from Table)
        /// </summary>
        public System.Byte? ROHS { get; set; }
        /// <summary>
        /// UpdatedBy (from Table)
        /// </summary>
        public System.Int32? UpdatedBy { get; set; }
        /// <summary>
        /// Updated by Name (from Table)
        /// </summary>
        public System.String UpdateByName { get; set; }
        /// <summary>
        /// DLUP (from Table)
        /// </summary>
        public System.DateTime DLUP { get; set; }
        /// <summary>
        /// DLUPstr (from Table)
        /// </summary>
        public System.String DLUPstr { get; set; }
        /// <summary>
        /// UsageNo (from Table)
        /// </summary>
        public System.Int32? UsageNo { get; set; }
        /// <summary>
        /// FullCustomerPart (from Table)
        /// </summary>
        public System.String FullCustomerPart { get; set; }
        /// <summary>
        /// BOM (from Table)
        /// </summary>
        public System.Boolean? BOMManagerflag { get; set; }
        /// <summary>
        /// BOMName (from Table)
        /// </summary>
        public System.String BOMManagerName { get; set; }
        /// <summary>
		/// BomStatus (from Table)
		/// </summary>
		public System.String BomManagerStatus { get; set; }

        /// <summary>
        /// PartWatch (from Table)
        /// </summary>
        public System.Boolean? PartWatch { get; set; }
        /// <summary>
        /// SalesmanName (from usp_select_Credit)
        /// </summary>
        public System.String SalesmanName { get; set; }
        /// <summary>
        /// ManufacturerCode (from usp_datalistnugget_CustomerRequirement)
        /// </summary>
        public System.String ManufacturerCode { get; set; }
        /// <summary>
        /// CompanyName (from usp_select_Credit)
        /// </summary>
        public System.String CompanyName { get; set; }
        /// <summary>
        /// ContactName (from usp_select_Credit)
        /// </summary>
        public System.String ContactName { get; set; }
        /// <summary>
        /// RowNum (from usp_datalistnugget_CustomerRequirement)
        /// </summary>
        public System.Int64? RowNum { get; set; }
        /// <summary>
        /// RowCnt (from usp_datalistnugget_CustomerRequirement)
        /// </summary>
        public System.Int32? RowCnt { get; set; }
        /// <summary>
        /// CurrencyCode (from usp_select_Credit)
        /// </summary>
        public System.String CurrencyCode { get; set; }
        /// <summary>
        /// DisplayStatus (from usp_select_CustomerRequirement)
        /// </summary>
        public System.String DisplayStatus { get; set; }
        /// <summary>
        /// DivisionNo (from Table)
        /// </summary>
        public System.Int32? DivisionNo { get; set; }
        /// <summary>
        /// TeamNo (from usp_select_Credit)
        /// </summary>
        public System.Int32? TeamNo { get; set; }
        /// <summary>
        /// CompanyOnStop (from usp_select_CustomerRequirement)
        /// </summary>
        public System.Boolean? CompanyOnStop { get; set; }
        /// <summary>
        /// CurrencyDescription (from usp_select_Credit)
        /// </summary>
        public System.String CurrencyDescription { get; set; }
        /// <summary>
        /// ProductName (from usp_select_CustomerRequirement)
        /// </summary>
        public System.String ProductName { get; set; }
        /// <summary>
        /// ProductDescription (from usp_select_CustomerRequirement)
        /// </summary>
        public System.String ProductDescription { get; set; }
        /// <summary>
        /// ManufacturerName (from usp_select_CustomerRequirement)
        /// </summary>
        public System.String ManufacturerName { get; set; }
        /// <summary>
        /// PackageName (from usp_select_CustomerRequirement)
        /// </summary>
        public System.String PackageName { get; set; }
        /// <summary>
        /// PackageDescription (from usp_select_CustomerRequirement)
        /// </summary>
        public System.String PackageDescription { get; set; }
        /// <summary>
        /// UsageName (from usp_select_CustomerRequirement)
        /// </summary>
        public System.String UsageName { get; set; }
        /// <summary>
        /// CustomerRequirementValue (from usp_select_CustomerRequirement)
        /// </summary>
        public System.Double CustomerRequirementValue { get; set; }
        /// <summary>
        /// ClosedReason (from usp_select_CustomerRequirement)
        /// </summary>
        public System.String ClosedReason { get; set; }
        /// <summary>
        /// DivisionName (from usp_select_Credit)
        /// </summary>
        public System.String DivisionName { get; set; }
        /// <summary>
        /// Status (from usp_selectAll_CustomerRequirement_open_for_Login)
        /// </summary>
        public System.String Status { get; set; }
        /// <summary>
        /// CreditLimit (from usp_selectAll_CustomerRequirement_open_for_Login)
        /// </summary>
        public System.Double? CreditLimit { get; set; }
        /// <summary>
        /// Balance (from usp_selectAll_CustomerRequirement_open_for_Login)
        /// </summary>
        public System.Double? Balance { get; set; }
        /// <summary>
        /// DaysOverdue (from usp_selectAll_CustomerRequirement_open_for_Login)
        /// </summary>
        public System.Int32? DaysOverdue { get; set; }
        /// <summary>
        /// ClientName (from usp_source_CustomerRequirement)
        /// </summary>
        public System.String ClientName { get; set; }
        /// <summary>
        /// Traceability
        /// </summary>
        public System.Boolean? Traceability { get; set; }

        /// <summary>
        /// BOMNo (from Table)
        /// </summary>
        public System.Int32? BOMManagerNo { get; set; }
        /// <summary>
        /// BOMHeader
        /// </summary>
        public System.String BOMManagerHeader { get; set; }
        /// <summary>
        /// BOMCode
        /// </summary>
        public System.String BOMManagerCode { get; set; }
        public string BOMManagerFullName { get; set; }
        public System.Int32? POHubReleaseBy { get; set; }
        public System.Int32? RequestToPOHubBy { get; set; }
        public int? SourcingResultId { get; set; }
        /// <summary>
        /// Price (from Table)
        /// </summary>
        public System.Double ConvertedTargetValue { get; set; }
        public System.String BOMManagerCurrencyCode { get; set; }
        public int? PurchaseQuoteNumber { get; set; }
        public int? PurchaseQuoteId { get; set; }
        public System.String BOMManagerStatus { get; set; }
        public System.Double PHPrice { get; set; }
        public System.String PHCurrencyCode { get; set; }
        public int? POHubCompany { get; set; }
        public System.Boolean? FactorySealed { get; set; }
        public System.String MSL { get; set; }
        public System.Int32 AllSorcingHasDelDate { get; set; }
        public int AllSorcingHasProduct { get; set; }
        public System.Boolean? AS9120 { get; set; }
        public System.Int32 SourcingResult { get; set; }
        public int? SourcingResultNo { get; set; }

        public System.Boolean? PQA { get; set; }
        public System.Boolean? Obsolete { get; set; }
        public System.Boolean? LastTimeBuy { get; set; }
        public System.Boolean? RefirbsAcceptable { get; set; }
        public System.Boolean? TestingRequired { get; set; }
        public System.Double? TargetSellPrice { get; set; }
        public System.Double? CompetitorBestOffer { get; set; }
        public System.DateTime? CustomerDecisionDate { get; set; }
        public System.DateTime? RFQClosingDate { get; set; }
        public System.Int32? QuoteValidityRequired { get; set; }
        public System.Int32? Type { get; set; }
        public System.Boolean? OrderToPlace { get; set; }
        public System.Int32? RequirementforTraceability { get; set; }
        public System.String QuoteValidityText { get; set; }
        public System.String ReqTypeText { get; set; }
        public System.String ReqForTraceabilityText { get; set; }
        public System.Boolean? IsGlobalCurrencySame { get; set; }
        public System.Boolean? HasClientSourcingResult { get; set; }
        public System.Boolean? HasHubSourcingResult { get; set; }
        public System.String EAU { get; set; }
        public System.Int32? ClientGlobalCurrencyNo { get; set; }
        public System.Int32? ReqGlobalCurrencyNo { get; set; }
        public System.String ClientCurrencyCode { get; set; }
        public System.Int32? ClientCurrencyNo { get; set; }
        public System.String ReqNotes { get; set; }
        /// ClientCode
        /// </summary>
        public System.String ClientCode { get; set; }
        /// <summary>
        public Boolean? IsNoBid { get; set; }
        public System.String NoBidNotes { get; set; }
        /// <summary>
        /// IsCurrencyInSameFaimly
        /// </summary>
        public System.Boolean? IsCurrencyInSameFaimly { get; set; }
        public System.Boolean? AlternativesAccepted { get; set; }
        public System.Boolean? RepeatBusiness { get; set; }
        public System.DateTime? DateRequestToPOHub { get; set; }
        public System.Int32 POCurrencyNo { get; set; }

        public System.DateTime? ExpeditDate { get; set; }
        public System.Int32? UpdateByPH { get; set; }
        public System.Boolean? ProductInactive { get; set; }
        public System.String DutyCode { get; set; }
        public System.Double? DutyRate { get; set; }
        public System.String ValidateMessage { get; set; }
        public System.Int32? MSLLevelNo { get; set; }
        public System.Boolean? IsProdHaz { get; set; }
        public System.Double? TotalValue { get; set; }
        public System.String TotalValueStr { get; set; }
        public System.Double? TotalInBase { get; set; }
        public System.String TotalInBaseStr { get; set; }
        /// <summary>
        /// AlternateStatus (from Table)
        /// </summary>
        public System.Byte? AlternateStatus { get; set; }
        //[001] start
        public string SalesOrderNumber { get; set; }
        //[001] end

        //[002] start
        public System.Int32 ClientBOMManagerId { get; set; }
        public System.String ClientBOMManagerCode { get; set; }
        public string ClientBOMManagerName { get; set; }
        public System.DateTime? ImportDateFrom { get; set; }
        public System.DateTime? ImportDateTo { get; set; }
        public System.DateTime? ImportDate { get; set; }
        public System.Int32 NoOfRequirements { get; set; }
        //[002] end

        //[003] start
        public System.Int32? RecordsProcessed { get; set; }
        public System.Int32? RecordsRemaining { get; set; }
        //[003] end

        //[001] start
        public System.Int32 ID { get; set; }
        public System.String Number { get; set; }
        public System.String ResultType { get; set; }
        //[001] end
        public System.Int32? TotalCount { get; set; }

        /// <summary>
        /// CountryOfOrigin
        /// </summary>
        public System.String CountryOfOrigin { get; set; }
        /// <summary>
        /// CountryOfOriginNo
        /// </summary>
        public System.Int32? CountryOfOriginNo { get; set; }
        /// <summary>
        /// LifeCycleStage
        /// </summary>
        public System.String LifeCycleStage { get; set; }
        /// <summary>
        /// HTSCode
        /// </summary>
        public System.String HTSCode { get; set; }
        /// <summary>
        /// AveragePrice
        /// </summary>
        public System.Double? AveragePrice { get; set; }
        /// <summary>
        /// Packaging
        /// </summary>
        public System.String Packaging { get; set; }
        /// <summary>
        /// PackagingSize
        /// </summary>
        public System.String PackagingSize { get; set; }
        public System.String Descriptions { get; set; }
        public System.String IHSCurrencyCode { get; set; }
        public System.Boolean? IsOrderViaIPOonly { get; set; }
        public System.Boolean? IsRestManufaturer { get; set; }
        public System.Int32? PartEditStatus { get; set; }
        public System.String IHSECCNCodeDefination { get; set; }


        public System.Boolean? PriceIssueBuyAndSell { get; set; }
        /// <summary>
        /// IsRestrictedProduct
        /// </summary>
        public System.Boolean? IsRestrictedProduct { get; set; }
        /// <summary>
        /// ECCNNotify
        /// </summary>
        public System.Boolean? ECCNNotify { get; set; }
        /// <summary>
        /// EccnSubject
        /// </summary>
        public System.String EccnSubject { get; set; }
        /// <summary>
        /// EccnMessage
        /// </summary>
        public System.String EccnMessage { get; set; }

        public System.String StockAvailableDetail { get; set; }
        public System.String WarningMessage { get; set; }

        public System.String StockAlerturl { get; set; }
        public System.Int32? InStock { get; set; }
        public System.Int32? OnOrder { get; set; }
        public System.Int32? Allocated { get; set; }
        public System.Int32? Available { get; set; }
        public System.String UserName { get; set; }
        public System.String ToEmail { get; set; }
        public System.Int32? stockId { get; set; }
        public System.Boolean Inactive { get; set; }
        public System.Int32? ReleaseBy { get; set; }
        public System.DateTime? DateRelease { get; set; }
        //public System.String BOMStatus { get; set; }
        public System.Int32? BomManagerCount { get; set; }
        public int? StatusValue { get; set; }
        public System.String Currency_Code { get; set; }
        public System.String CurrentSupplier { get; set; }
        public System.DateTime? QuoteRequired { get; set; }
        public System.Int32? AllItemHasSourcing { get; set; }
        public string Releasedby { get; set; }
        public string Requestedby { get; set; }
        public string AssignedUser { get; set; }
        public int NoBidCount { get; set; }
        public System.Int32? Contact2Id { get; set; }
        public System.String Contact2Name { get; set; }
        public System.String ValidationMessage { get; set; }
        public System.Boolean IsReqInValid { get; set; }
        public System.String ReqSalesPerson { get; set; }
        public System.String ReqSalesPersonName { get; set; }
        public System.String SupportTeamMemberNoAsString { get; set; }
        public System.Boolean? BOM { get; set; }
        public int BOMItemsCount { get; set; }
        public System.Boolean? ISPrimarySourceActual { get; set; }
        public System.Boolean? ISPrimarySource { get; set; }
        public System.Boolean? QuoteGenerated { get; set; }
        public System.Int32? QuoteId { get; set; }
        public System.Int32? QuoteNumber { get; set; }
        public System.Int32? ReqStatus { get; set; }
        public System.Boolean? AutosourcingStatus { get; set; }
        public System.String ReqStatusText { get; set; }
        public int curpage { get; set; }
        public int OfferCount { get; set; }
        public System.String GeneratedFilename { get; set; }
        /// <summary>
        /// Release note by BOM
        /// </summary>
        public System.String ReleaseNote { get; set; }
        public System.String MfrAdvisoryNotes { get; set; }
        public System.String CompanyAdvisoryNotes { get; set; }

        #endregion

        /// <summary>
        /// GetListReadyForClient
        /// Calls [usp_selectAll_BOM]
        /// </summary>
        public static List<BOMManagerContract> GetBomManagerList(System.Int32? clientId, System.Boolean? isPoHUB, System.Int32? topToSelect, System.Int32? bomStatus, System.Int32? updatedBy)
        {
            List<BOMManager> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.BOMManager.GetBomManagerList(clientId, isPoHUB, topToSelect, bomStatus, updatedBy);
            if (lstDetails == null)
            {
                return new List<BOMManagerContract>();
            }
            else
            {
                List<BOMManagerContract> lst = new List<BOMManagerContract>();
                foreach (BOMManager objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.BOMManagerContract obj = new Rebound.GlobalTrader.BLL.BOMManagerContract();
                    obj.BOMManagerId = objDetails.BOMManagerId;
                    obj.CompanyName = objDetails.CompanyName;
                    obj.ClientName = objDetails.ClientName;
                    obj.BOMManagerCode = objDetails.BOMManagerCode;
                    obj.BOMManagerName = objDetails.BOMManagerName;
                    obj.DLUP = objDetails.DLUP;
                    obj.StatusValue = objDetails.StatusValue;
                    obj.UpdatedBy = objDetails.UpdatedBy;
                    obj.QuoteRequired = objDetails.QuoteRequired;
                    obj.RequestToPOHubBy = objDetails.RequestToPOHubBy;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }

        public static List<BOMManagerContract> BOMSearch(int BOMId, int? clientId, DateTime? FromDate, DateTime? ToDate, int? stageId, bool? isLock, string ClientCurrencyCode, string strCulture, System.Int32? teamId, System.Int32? divisionId, System.Int32? loginId)
        {

            List<BOMManager> BOMMgrLst = Rebound.GlobalTrader.DAL.SiteProvider.BOMManager.BOMSearch(BOMId, clientId, FromDate, ToDate, stageId, isLock, teamId, divisionId, loginId);

            if (BOMMgrLst == null)
            {
                return new List<BOMManagerContract>();
            }
            else
            {
                List<BOMManagerContract> bomlist = new List<BOMManagerContract>();
                foreach (BOMManager bomItem in BOMMgrLst)
                {
                    BOMManagerContract BOMMgr = new BOMManagerContract();

                    BOMMgr.CustomerRequirementId = bomItem.CustomerRequirementId;
                    BOMMgr.CustomerRequirementNumber = bomItem.CustomerRequirementNumber;
                    BOMMgr.Part = bomItem.Part;
                    BOMMgr.ROHS = bomItem.ROHS;
                    BOMMgr.ManufacturerNo = bomItem.ManufacturerNo;
                    BOMMgr.ManufacturerCode = bomItem.ManufacturerCode;
                    BOMMgr.Quantity = bomItem.Quantity;
                    BOMMgr.CompanyName = bomItem.CompanyName;
                    BOMMgr.CompanyNo = bomItem.CompanyNo;
                    BOMMgr.ContactName = bomItem.ContactName;
                    BOMMgr.ContactNo = bomItem.ContactNo;
                    BOMMgr.SalesmanName = bomItem.SalesmanName;
                    BOMMgr.ReceivedDate = bomItem.ReceivedDate;
                    BOMMgr.DatePromised = bomItem.DatePromised;
                    BOMMgr.BOMManagerCode = bomItem.BOMManagerCode;
                    BOMMgr.BOMManagerNo = bomItem.BOMManagerNo;
                    BOMMgr.BOMManagerName = bomItem.BOMManagerName;
                    //BOMMgr.TotalInBase = Functions.FormatCurrency( bomItem.TotalInBase SessionManager.ClientCurrencyCode, 2);
                    //BOMMgr.TotalInBase = bomItem.TotalInBase;
                    BOMMgr.TotalInBaseStr = BOMManagerContract.FormatCurrency(bomItem.TotalInBase, ClientCurrencyCode, 2, false, strCulture);
                    // need to concatenate client currency in site project
                    //BOMMgr.TotalValue = Functions.FormatCurrency(bomItem.TotalValue, bomItem.CurrencyCode, 2);
                    //BOMMgr.TotalValue = bomItem.TotalValue;
                    BOMMgr.TotalValueStr = BOMManagerContract.FormatCurrency(bomItem.TotalValue, bomItem.CurrencyCode, 2, false, strCulture);
                    // need to concatenate bomItem currency in site project
                    BOMMgr.CurrencyCode = bomItem.CurrencyCode;// need to concatenate bomItem currency in site project
                    BOMMgr.REQStatusName = bomItem.REQStatusName;
                    BOMMgr.BOMManagerStatus = bomItem.BOMManagerStatus;
                    //BOMMgr.SaleBOMManagerImportId = bomItem.SaleBOMManagerImportId;
                    //BOMMgr.StockCode = bomItem.StockCode;
                    //BOMMgr.Description = bomItem.Description;
                    //BOMMgr.Part = bomItem.Part;
                    //BOMMgr.RFQ = bomItem.RFQ;
                    //BOMMgr.UnitPrice = bomItem.UnitPrice;
                    //BOMMgr.LineTotal = bomItem.LineTotal;
                    //BOMMgr.Clientid = bomItem.Clientid;
                    //BOMMgr.ClientName = bomItem.ClientName;
                    //BOMMgr.StatusId = bomItem.StatusId;
                    //BOMMgr.Islock = bomItem.Islock;
                    bomlist.Add(BOMMgr);
                    BOMMgr = null;
                }
                BOMMgrLst = null;
                return bomlist;
            }
        }

        public static string FormatCurrency(object objAmount, string strCurrency, int intFigures, bool blnCommaThousands, string strCulture)
        {
            blnCommaThousands = false;
            if (objAmount == null) objAmount = 0;
            CultureInfo ci = new CultureInfo(strCulture);
            Double dbl = Convert.ToDouble(objAmount);
            string strReturn = dbl.ToString(string.Format("{0}{1}", (blnCommaThousands) ? "n" : "f", intFigures), ci);
            if (!String.IsNullOrEmpty(strCurrency)) strReturn += string.Format(" {0}", strCurrency.Trim());
            return strReturn.Trim();
        }

        /// <summary>
        /// Get
        /// Calls [usp_select_BOM]
        /// </summary>
        public static BOMManagerContract Get(System.Int32? bomManagerId)
        {
            Rebound.GlobalTrader.DAL.BOMManager objDetails = Rebound.GlobalTrader.DAL.SiteProvider.BOMManager.Get(bomManagerId);
            if (objDetails == null)
            {
                return null;
            }
            else
            {
                BOMManagerContract obj = new BOMManagerContract();
                obj.BOMManagerId = objDetails.BOMManagerId;
                //obj.ClientNo = objDetails.ClientNo;
                obj.BOMManagerName = objDetails.BOMManagerName;
                obj.CompanyNo = objDetails.CompanyNo;
                obj.CompanyName = objDetails.CompanyName;
                obj.Notes = objDetails.Notes;
                obj.BOMManagerCode = objDetails.BOMManagerCode;
                obj.Inactive = objDetails.Inactive;
                obj.ContactNo = objDetails.ContactNo;
                obj.ContactName = objDetails.ContactName;
                obj.RequestToPOHubBy = objDetails.RequestToPOHubBy;
                obj.DateRequestToPOHub = objDetails.DateRequestToPOHub;
                obj.ReleaseBy = objDetails.ReleaseBy;
                obj.DateRelease = objDetails.DateRelease;
                obj.UpdatedBy = objDetails.UpdatedBy;
                obj.UpdateByName = objDetails.UpdatedByName;
                obj.DLUP = objDetails.DLUP;
                obj.DLUPstr = FormatBSTDate(objDetails.DLUP);
                obj.BOMManagerStatus = objDetails.BOMManagerStatus;
                obj.BomManagerCount = objDetails.BomManagerCount;
                obj.StatusValue = objDetails.StatusValue;
                obj.CurrencyCode = objDetails.CurrencyCode;
                obj.CurrencyNo = objDetails.CurrencyNo;
                obj.Currency_Code = objDetails.Currency_Code;
                obj.CurrentSupplier = objDetails.CurrentSupplier;
                obj.QuoteRequired = objDetails.QuoteRequired;
                obj.AllItemHasSourcing = objDetails.AllItemHasSourcing;
                obj.AS9120 = objDetails.AS9120;
                obj.Requestedby = objDetails.Requestedby;
                obj.Releasedby = objDetails.Releasedby;
                obj.NoBidCount = objDetails.NoBidCount;
                obj.UpdateByPH = objDetails.UpdateByPH;
                obj.AssignedUser = objDetails.AssignedUser;
                obj.Contact2Id = objDetails.Contact2Id;
                obj.Contact2Name = objDetails.Contact2Name;
                obj.ValidationMessage = objDetails.ValidationMessage;
                obj.IsReqInValid = objDetails.IsReqInValid;
                obj.ReqSalesPerson = objDetails.ReqSalesPerson;
                obj.ReqSalesPersonName = objDetails.ReqSalesPersonName;
                obj.SupportTeamMemberNoAsString = objDetails.SupportTeamMemberNoAsString;
                obj.ClientNo = objDetails.ClientNo;
                obj.BOMItemsCount = objDetails.BOMItemsCount;
                objDetails = null;
                return obj;
            }
        }

        /// <summary>
        /// GetListForCustomerRequirement
        /// Calls [usp_selectAll_CustomerRequirement_for_BOM]
        /// </summary>
        public static List<BOMManagerContract> GetBOMListForCustomerRequirement(System.Int32? BOMNo, System.Int32? clientID, int curPage = 1, int Rpp = 5, System.String Part = "", System.Int32? ReqType = 0)
        {
            List<BOMManager> lstDetails = SiteProvider.BOMManager.GetBOMListForCustomerRequirement(BOMNo, clientID, curPage, Rpp, Part,ReqType);
            if (lstDetails == null)
            {
                return new List<BOMManagerContract>();
            }
            else
            {
                List<BOMManagerContract> lst = new List<BOMManagerContract>();
                foreach (BOMManager objDetails in lstDetails)
                {
                    BOMManagerContract obj = new BOMManagerContract();
                    obj.CustomerRequirementId = objDetails.CustomerRequirementId;
                    obj.CustomerRequirementNumber = objDetails.CustomerRequirementNumber;
                    obj.ClientNo = objDetails.ClientNo;
                    obj.FullPart = objDetails.FullPart;
                    obj.Part = objDetails.Part;
                    obj.ManufacturerNo = objDetails.ManufacturerNo;
                    obj.DateCode = objDetails.DateCode;
                    obj.PackageNo = objDetails.PackageNo;
                    obj.Quantity = objDetails.Quantity;
                    obj.Price = objDetails.Price;
                    obj.CurrencyNo = objDetails.CurrencyNo;
                    obj.ReceivedDate = objDetails.ReceivedDate;
                    obj.Salesman = objDetails.Salesman;
                    obj.DatePromised = objDetails.DatePromised;
                    obj.Notes = objDetails.Notes;
                    obj.Instructions = objDetails.Instructions;
                    obj.Shortage = objDetails.Shortage;
                    obj.CompanyNo = objDetails.CompanyNo;
                    obj.ContactNo = objDetails.ContactNo;
                    obj.Alternate = objDetails.Alternate;
                    obj.OriginalCustomerRequirementNo = objDetails.OriginalCustomerRequirementNo;
                    obj.ReasonNo = objDetails.ReasonNo;
                    obj.ProductNo = objDetails.ProductNo;
                    obj.ProductDescription = objDetails.ProductDescription;
                    obj.CustomerPart = objDetails.CustomerPart;
                    obj.Closed = objDetails.Closed;
                    obj.ROHS = objDetails.ROHS;
                    obj.UpdatedBy = objDetails.UpdatedBy;
                    obj.DLUP = objDetails.DLUP;
                    obj.UsageNo = objDetails.UsageNo;
                    obj.DisplayStatus = objDetails.DisplayStatus;
                    obj.SalesmanName = objDetails.SalesmanName;
                    obj.DivisionNo = objDetails.DivisionNo;
                    obj.TeamNo = objDetails.TeamNo;
                    obj.CompanyName = objDetails.CompanyName;
                    obj.CompanyOnStop = objDetails.CompanyOnStop;
                    obj.ContactName = objDetails.ContactName;
                    obj.CurrencyCode = objDetails.CurrencyCode;
                    obj.CurrencyDescription = objDetails.CurrencyDescription;
                    obj.ProductName = objDetails.ProductName;
                    obj.ProductDescription = objDetails.ProductDescription;
                    obj.ManufacturerName = objDetails.ManufacturerName;
                    obj.TargetSellPrice = objDetails.TargetSellPrice;
                    obj.Line = objDetails.Line;
                    obj.LineValue = objDetails.LineValue;
                    obj.ManufacturerCode = objDetails.ManufacturerCode;
                    obj.PackageName = objDetails.PackageName;
                    obj.PackageDescription = objDetails.PackageDescription;
                    obj.UsageName = objDetails.UsageName;
                    obj.CustomerRequirementValue = objDetails.CustomerRequirementValue;
                    obj.ClosedReason = objDetails.ClosedReason;
                    obj.PartWatch = objDetails.PartWatch;
                    obj.BOM = objDetails.BOM;
                    obj.BOMManagerName = objDetails.BOMManagerName;
                    obj.DivisionName = objDetails.DivisionName;
                    obj.BOMManagerHeader = objDetails.BOMManagerHeader;
                    obj.BOMManagerNo = (int)objDetails.BOMManagerNo;
                    obj.POHubReleaseBy = objDetails.POHubReleaseBy;
                    obj.SourcingResultId = objDetails.SourcingResultId;
                    obj.RequestToPOHubBy = objDetails.RequestToPOHubBy;
                    obj.BOMManagerCode = objDetails.BOMManagerCode;
                    obj.BOMManagerFullName = objDetails.BOMManagerFullName;
                    obj.BOMManagerCurrencyCode = objDetails.BOMManagerCurrencyCode;
                    obj.ConvertedTargetValue = objDetails.ConvertedTargetValue;

                    obj.PurchaseQuoteId = objDetails.PurchaseQuoteId;
                    obj.PurchaseQuoteNumber = objDetails.PurchaseQuoteNumber;
                    obj.ClientName = objDetails.ClientName;
                    obj.POHubCompany = objDetails.POHubCompany;
                    obj.FactorySealed = objDetails.FactorySealed;
                    obj.MSL = objDetails.MSL;
                    obj.AllSorcingHasDelDate = objDetails.AllSorcingHasDelDate;
                    obj.AllSorcingHasProduct = objDetails.AllSorcingHasProduct;
                    obj.SourcingResult = objDetails.SourcingResult;
                    obj.BOMManagerStatus = objDetails.BOMManagerStatus;
                    obj.HasClientSourcingResult = objDetails.HasClientSourcingResult;
                    obj.HasHubSourcingResult = objDetails.HasHubSourcingResult;
                    obj.IsNoBid = objDetails.IsNoBid;
                    obj.ExpeditDate = objDetails.ExpeditDate;
                    obj.UpdateByPH = objDetails.UpdateByPH;
                    obj.AlternateStatus = objDetails.AlternateStatus;
                    obj.SupportTeamMemberNo = objDetails.SupportTeamMemberNo;
                    obj.SupportTeamMemberName = objDetails.SupportTeamMemberName;
                    obj.ISPrimarySource = objDetails.IsPrimarySource;
                    obj.ISPrimarySourceActual = objDetails.IsPrimarySourceActual;
                    obj.QuoteGenerated = objDetails.QuoteGenerated;
                    obj.QuoteId = objDetails.QuoteId;
                    obj.QuoteNumber = objDetails.QuoteNumber;
                    obj.ReqStatus = objDetails.ReqStatus;
                    obj.AutosourcingStatus = objDetails.AutosourcingStatus;
                    obj.ReqStatusText = objDetails.ReqStatusText;
                    obj.IsNoBid = objDetails.IsNoBid;
                    obj.TotalCount = objDetails.TotalCount;
                    obj.OfferCount = objDetails.OfferCount;
                    obj.GeneratedFilename = objDetails.GeneratedFilename;
                    obj.Releasedby = objDetails.Releasedby;
                    obj.ReleaseNote = objDetails.ReleaseNote;
                    obj.MfrAdvisoryNotes = objDetails.MfrAdvisoryNotes;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }

        /// <summary>
        /// Update
        /// Calls [usp_update_BOM]
        /// </summary>
        public static bool UpdateBOMManager(System.Int32? bomId, System.Int32? clientNo, System.String bomName, System.String notes, System.String bomCode, System.Boolean? inactive, System.Int32? updatedBy, System.Int32? companyId, System.Int32? contactId, System.Int32? currencyNo, System.String currentSupplier, System.DateTime? quoteRequired, System.Boolean? AS9120, System.Int32? contact2Id, System.Int32? salespersonId)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.BOMManager.UpdateBOMManager(bomId, clientNo, bomName, notes, bomCode, inactive, updatedBy, companyId, contactId, currencyNo, currentSupplier, quoteRequired, AS9120, contact2Id, salespersonId);
        }

        /// <summary>
        /// Update
        /// Calls [usp_update_BOM_POHubQuote]
        /// </summary>
        public static bool UpdatePurchaseQuote(System.Int32? BOMId, System.Int32? updatedBy, System.Int32? bomStatus, System.Int32 AssignUserNo, out System.String ValidateMessage, string AssignedUserType)
        {
            string ValidationMessage = null;
            bool Isupdated = Rebound.GlobalTrader.DAL.SiteProvider.BOMManager.UpdatePurchaseQuote(BOMId, updatedBy, bomStatus, AssignUserNo, out ValidateMessage, AssignedUserType);
            ValidationMessage = ValidateMessage;
            return Isupdated;
        }

        /// <summary>
        /// Update
        /// 
        /// </summary>
        public static DataTable UpdateBOMStatusToClosed(System.Int32? BOMId, System.Int32? updatedBy, System.Int32? bomStatus)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.BOMManager.UpdateBOMStatusToClosed(BOMId, updatedBy, bomStatus);
        }
        /// DataListNugget
        /// Calls [usp_datalistnugget_BOM]
        /// </summary>
        //public static List<BOM> DataListNugget(System.Int32? clientId, System.Int32? orderBy, System.Int32? sortDir, System.Int32? pageIndex, System.Int32? pageSize, System.Int32? BOMIdLo, System.Int32? BOMIdHi, System.String bomName, System.Boolean? isPOHub, System.Int32? selectedClientNo)
        public static List<BOMManagerContract> DataListNuggetOLD(System.Int32? clientId, System.Int32? teamId, System.Int32? divisionId, System.Int32? loginId, System.Int32? orderBy, System.Int32? sortDir, System.Int32? pageIndex, System.Int32? pageSize, System.String BOMCode, System.String bomName, System.Boolean? isPOHub, System.Int32? selectedClientNo, int? bomStatus, int? isAssignToMe, int? assignedUser, System.Int32? intDivisionNo, System.Int32? salesPerson, System.DateTime? startdate, System.DateTime? enddate, string CompanyNo, System.Int32? MailGroupId)
        {
            List<BOMManager> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.BOMManager.DataListNuggetOld(clientId, teamId, divisionId, loginId, orderBy, sortDir, pageIndex, pageSize, BOMCode, bomName, isPOHub, selectedClientNo, bomStatus, isAssignToMe, assignedUser, intDivisionNo, salesPerson, startdate, enddate, CompanyNo, MailGroupId);
            if (lstDetails == null)
            {
                return new List<BOMManagerContract>();
            }
            else
            {
                List<BOMManagerContract> lst = new List<BOMManagerContract>();
                foreach (BOMManager objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.BOMManagerContract obj = new Rebound.GlobalTrader.BLL.BOMManagerContract();
                    obj.BOMManagerId = objDetails.BOMManagerId;
                    obj.BOMManagerCode = objDetails.BOMManagerCode.TrimEnd();
                    obj.BOMManagerName = objDetails.BOMManagerName;
                    obj.RowNum = objDetails.RowNum;
                    obj.RowCnt = objDetails.RowCnt;
                    obj.CompanyName = objDetails.CompanyName;
                    obj.CompanyNo = objDetails.CompanyNo;
                    obj.Inactive = objDetails.Inactive;
                    obj.BOMManagerStatus = objDetails.BOMManagerStatus;
                    obj.DLUP = objDetails.DLUP;
                    obj.QuoteRequired = objDetails.QuoteRequired;
                    obj.UpdatedBy = objDetails.UpdatedBy;
                    obj.CurrencyNo = objDetails.CurrencyNo;
                    obj.TotalBomManagerLinePrice = objDetails.TotalBomManagerLinePrice;
                    obj.DateRequestToPOHub = objDetails.DateRequestToPOHub;
                    obj.POCurrencyNo = objDetails.POCurrencyNo;
                    obj.AssignedUser = objDetails.AssignedUser;
                    obj.DivisionName = objDetails.DivisionName;
                    obj.ClientCode = objDetails.ClientCode;//[002]
                    obj.Requestedby = objDetails.Requestedby;//[002]
                    obj.SalesmanName = objDetails.SalesmanName;
                    obj.ContactName = objDetails.ContactName;
                    obj.ContactNo = objDetails.ContactNo;
                    obj.ReceivedDate = objDetails.ReceivedDate;
                    obj.DatePromised = objDetails.DatePromised;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }

        /// DataListNugget
        /// Calls [usp_datalistnugget_BOM]
        /// </summary>
        //public static List<BOM> DataListNugget(System.Int32? clientId, System.Int32? orderBy, System.Int32? sortDir, System.Int32? pageIndex, System.Int32? pageSize, System.Int32? BOMIdLo, System.Int32? BOMIdHi, System.String bomName, System.Boolean? isPOHub, System.Int32? selectedClientNo)
        public static List<BOMManagerContract> DataListNuggetOLD_ForAssign(System.Int32? clientId, System.Int32? teamId, System.Int32? divisionId, System.Int32? loginId, System.Int32? orderBy, System.Int32? sortDir, System.Int32? pageIndex, System.Int32? pageSize, System.String BOMCode, System.String bomName, System.Boolean? isPOHub, System.Int32? selectedClientNo, int? bomStatus, int? isAssignToMe, int? assignedUser, System.Int32? intDivisionNo, System.Int32? salesPerson, System.DateTime? startdate, System.DateTime? enddate, string CompanyNo)
        {
            List<BOMManager> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.BOMManager.DataListNuggetOld_ForAssign(clientId, teamId, divisionId, loginId, orderBy, sortDir, pageIndex, pageSize, BOMCode, bomName, isPOHub, selectedClientNo, bomStatus, isAssignToMe, assignedUser, intDivisionNo, salesPerson, startdate, enddate, CompanyNo);
            if (lstDetails == null)
            {
                return new List<BOMManagerContract>();
            }
            else
            {
                List<BOMManagerContract> lst = new List<BOMManagerContract>();
                foreach (BOMManager objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.BOMManagerContract obj = new Rebound.GlobalTrader.BLL.BOMManagerContract();
                    obj.BOMManagerId = objDetails.BOMManagerId;
                    obj.BOMManagerCode = objDetails.BOMManagerCode.TrimEnd();
                    obj.BOMManagerName = objDetails.BOMManagerName;
                    obj.RowNum = objDetails.RowNum;
                    obj.RowCnt = objDetails.RowCnt;
                    obj.CompanyName = objDetails.CompanyName;
                    obj.CompanyNo = objDetails.CompanyNo;
                    obj.Inactive = objDetails.Inactive;
                    obj.BOMManagerStatus = objDetails.BOMManagerStatus;
                    obj.DLUP = objDetails.DLUP;
                    obj.QuoteRequired = objDetails.QuoteRequired;
                    obj.UpdatedBy = objDetails.UpdatedBy;
                    obj.CurrencyNo = objDetails.CurrencyNo;
                    obj.TotalBomManagerLinePrice = objDetails.TotalBomManagerLinePrice;
                    obj.DateRequestToPOHub = objDetails.DateRequestToPOHub;
                    obj.POCurrencyNo = objDetails.POCurrencyNo;
                    obj.AssignedUser = objDetails.AssignedUser;
                    obj.DivisionName = objDetails.DivisionName;
                    obj.ClientCode = objDetails.ClientCode;//[002]
                    obj.Requestedby = objDetails.Requestedby;//[002]
                    obj.SalesmanName = objDetails.SalesmanName;
                    obj.ContactName = objDetails.ContactName;
                    obj.ContactNo = objDetails.ContactNo;
                    obj.ReceivedDate = objDetails.ReceivedDate;
                    obj.DatePromised = objDetails.DatePromised;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }

        /// DataListNugget for HURFQ
        /// Calls [usp_datalistnugget_CustomerRequirementForHUBRFQ]
        /// add start date and end date  for searching by umendra
        /// </summary>
        public static List<CustomerRequirement> DataListNuggetHUBRFQBOMManager(System.Int32? clientId, System.Int32? teamId, System.Int32? divisionId, System.Int32? loginId, System.Int32? orderBy, System.Int32? sortDir, System.Int32? pageIndex, System.Int32? pageSize, System.String BOMCode, System.String bomName, System.Boolean? isPOHub, System.Int32? selectedClientNo, int? bomStatus, int? isAssignToMe, int? assignedUser, System.String Manufacturer, System.String Part, System.Int32? intDivision, System.DateTime? startdate, System.DateTime? enddate, System.Int32? salesPerson)
        {
            List<CustomerRequirementDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.BOMManager.DataListNuggetHUBRFQBOMManager(clientId, teamId, divisionId, loginId, orderBy, sortDir, pageIndex, pageSize, BOMCode, bomName, isPOHub, selectedClientNo, bomStatus, isAssignToMe, assignedUser, Manufacturer, Part, intDivision, startdate, enddate, salesPerson);
            if (lstDetails == null)
            {
                return new List<CustomerRequirement>();
            }
            else
            {
                List<CustomerRequirement> lst = new List<CustomerRequirement>();
                foreach (CustomerRequirementDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.CustomerRequirement obj = new Rebound.GlobalTrader.BLL.CustomerRequirement();
                    obj.CustomerRequirementId = objDetails.CustomerRequirementId;
                    obj.CustomerRequirementNumber = objDetails.CustomerRequirementNumber;
                    obj.Salesman = objDetails.Salesman;
                    obj.Part = objDetails.Part;
                    obj.ROHS = objDetails.ROHS;
                    obj.SalesmanName = objDetails.SalesmanName;
                    obj.ManufacturerNo = objDetails.ManufacturerNo;
                    obj.ManufacturerCode = objDetails.ManufacturerCode;
                    obj.Quantity = objDetails.Quantity;
                    obj.CompanyNo = objDetails.CompanyNo;
                    obj.CompanyName = objDetails.CompanyName;
                    obj.ContactNo = objDetails.ContactNo;
                    obj.ContactName = objDetails.ContactName;
                    obj.ReceivedDate = objDetails.ReceivedDate;
                    obj.DatePromised = objDetails.DatePromised;
                    obj.RowNum = objDetails.RowNum;
                    obj.RowCnt = objDetails.RowCnt;
                    obj.BOMCode = objDetails.BOMCode;
                    obj.BOMNo = (int)objDetails.BOMNo;
                    obj.BOMName = objDetails.BOMName;
                    obj.BOMStatus = objDetails.BOMStatus;
                    obj.Price = objDetails.Price;
                    obj.PHPrice = objDetails.PHPrice;
                    obj.CurrencyNo = objDetails.CurrencyNo;
                    obj.CurrencyCode = objDetails.CurrencyCode;
                    obj.DLUP = objDetails.DLUP;
                    obj.DivisionName = objDetails.DivisionName;
                    obj.AlternateStatus = objDetails.AlternateStatus;
                    obj.Alternate = objDetails.Alternate;
                    obj.POCurrencyNo = objDetails.POCurrencyNo;
                    obj.SupportTeamMemberNo = objDetails.SupportTeamMemberNo;
                    obj.SupportTeamMemberName = objDetails.SupportTeamMemberName;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }

        public static List<BOMManagerContract> DataListNugget(System.Int32? clientId, System.Int32? teamId, System.Int32? divisionId, System.Int32? loginId, System.Int32? orderBy, System.Int32? sortDir, System.Int32? pageIndex, System.Int32? pageSize, System.String BOMCode, System.String bomName, System.Boolean? isPOHub, System.Int32? selectedClientNo, int? bomStatus, int? isAssignToMe, int? assignedUser, System.Int32? intDivisionNo, System.Int32? salesPerson, System.DateTime? startdate, System.DateTime? enddate, System.String CompanyNo)

        {
            List<BOMManager> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.BOMManager.DataListNugget(clientId, teamId, divisionId, loginId, orderBy, sortDir, pageIndex, pageSize, BOMCode, bomName, isPOHub, selectedClientNo, bomStatus, isAssignToMe, assignedUser, intDivisionNo, salesPerson, startdate, enddate, CompanyNo);
            if (lstDetails == null)
            {
                return new List<BOMManagerContract>();
            }
            else
            {
                List<BOMManagerContract> lst = new List<BOMManagerContract>();
                foreach (BOMManager objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.BOMManagerContract obj = new Rebound.GlobalTrader.BLL.BOMManagerContract();
                    obj.BOMManagerId = objDetails.BOMManagerId;
                    obj.BOMManagerCode = objDetails.BOMManagerCode.TrimEnd();
                    obj.BOMManagerName = objDetails.BOMManagerName;
                    obj.RowNum = objDetails.RowNum;
                    obj.RowCnt = objDetails.RowCnt;
                    obj.CompanyName = objDetails.CompanyName;
                    obj.CompanyNo = objDetails.CompanyNo;
                    obj.Inactive = objDetails.Inactive;
                    obj.BOMManagerStatus = objDetails.BOMManagerStatus;
                    obj.DLUP = objDetails.DLUP;
                    obj.QuoteRequired = objDetails.QuoteRequired;
                    obj.UpdatedBy = objDetails.UpdatedBy;
                    obj.CurrencyNo = objDetails.CurrencyNo;
                    obj.TotalBomManagerLinePrice = objDetails.TotalBomManagerLinePrice;
                    obj.DateRequestToPOHub = objDetails.DateRequestToPOHub;
                    obj.POCurrencyNo = objDetails.POCurrencyNo;
                    obj.AssignedUser = objDetails.AssignedUser;
                    obj.DivisionName = objDetails.DivisionName;
                    obj.ClientCode = objDetails.ClientCode;//[002]
                    obj.Requestedby = objDetails.Requestedby;//[002]
                    obj.SalesmanName = objDetails.SalesmanName;
                    obj.ContactName = objDetails.ContactName;
                    obj.ContactNo = objDetails.ContactNo;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }
        public static DataTable GetBOMManagerDetailFromTemp(int displayLength, int displayStart, int sortCol, string sortDir, string search, int userid, int ClientId, int SelectedclientId, int clientType_con)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.BOMManager.GetBOMManagerDetailFromTemp(displayLength, displayStart, sortCol, sortDir, search, userid, ClientId, SelectedclientId, clientType_con);
        }
        public static DataTable GenerateCustomTemplateData(int QuoteId, string ColumnString)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.BOMManager.GenerateCustomTemplateData(QuoteId, ColumnString);
        }
        public static DataTable SaveCustomTemplateMapping(int QuoteId, string MappingDetails, int? clientNo, int CompanyNo, int? updatedBy)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.BOMManager.SaveCustomTemplateMapping(QuoteId, MappingDetails, clientNo, CompanyNo, updatedBy);
        }
        public static DataTable GetBOMManagerUploadMapping(int QuoteId, int? clientNo)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.BOMManager.GetBOMManagerUploadMapping(QuoteId, clientNo);
        }
        
        public static DataTable GetCustomTemplateMapping(int QuoteId, int? clientNo)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.BOMManager.GetCustomTemplateMapping(QuoteId,  clientNo);
        }
        
        public static DataTable GetBOMManagerGenrateTempData(int displayLength, int displayStart, int sortCol, string sortDir, string search, int userid, int ClientId, int SelectedclientId, string ColumnList, string ddlCurrency, string CompanyNameText, int clientType_con, string Column_Lable, string Column_Name, string ContactText, bool OverRideCurrency, string DefaultCurrencyName, string CurrencyColumnName, int DefaultCurrencyId)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.BOMManager.GetBOMManagerGenrateTempData(displayLength, displayStart, sortCol, sortDir, search, userid, ClientId, SelectedclientId, ColumnList, ddlCurrency, CompanyNameText, clientType_con, Column_Lable, Column_Name, ContactText, OverRideCurrency, DefaultCurrencyName, CurrencyColumnName, DefaultCurrencyId);
        }
        public static void SaveBOMManagerExcelBulkSave(DataTable tempStock, DataTable dtData, string originalFilename, string generatedFilename, System.Int32 userId, System.Int32 clientId, int? SelectedclientId, int clientType_con, string DefaultCurrency)
        {
            Rebound.GlobalTrader.DAL.SiteProvider.BOMManager.saveBOMManagerExcelBulkSave(tempStock, dtData, originalFilename, generatedFilename, userId, clientId, SelectedclientId, clientType_con, DefaultCurrency);
        }


        public static void SaveBOMManagerExcelHeader(string columnList, string insertColumnList, System.Int32? clientId, int? SelectedclientId, int? loginID, int clientType_con)
        {
            Rebound.GlobalTrader.DAL.SiteProvider.BOMManager.SaveBOMManagerExcelHeader(columnList, insertColumnList, clientId, SelectedclientId, loginID, clientType_con);
        }
        public static string SaveBOMManagerImportData(int userId, int ClientId, int SelectedclientId, string Column_Lable, string Column_Name, string insertDataList, string fileColName, string ddlCurrency, out System.String errorMessage, string BomName, string CompanyName, string ContactName, int SalesmanId, int CompanyId, int ContactId, bool PartWatch, int DefaultCurrencyId, bool OverRideCurrency, string SaveImportOrHubRFQ, out System.String NewBomCode, out System.Int32 NewBomid, System.Int32? ReqforTraceabilityId, System.Int32? TypeId, System.DateTime DateRequired)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.BOMManager.SaveBOMManagerImportData(userId, ClientId, SelectedclientId, Column_Lable, Column_Name, insertDataList, fileColName, ddlCurrency, out errorMessage, BomName, CompanyName, ContactName, SalesmanId, CompanyId, ContactId, PartWatch, DefaultCurrencyId, OverRideCurrency, SaveImportOrHubRFQ, out NewBomCode, out NewBomid, ReqforTraceabilityId, TypeId, DateRequired);
        }

        public static List<Offer> IPOBOMAutoSource(int? clientId, string partSearch, int? index, DateTime? maxDate, DateTime? endDate, out DateTime? outDate, bool IsServerLocal, int? BOM, int? CallType, int? customerRequirementId, int curPage = 1, int Rpp = 5)
        {
            try
            {
                List<OfferDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.BOMManager.IPOBOMAutoSource(
                    clientId,
                    GetFormValue_StringForPartSearch(partSearch),
                    index,
                    maxDate,
                    endDate,
                    out outDate,
                    IsServerLocal,
                    BOM,
                    CallType,
                    customerRequirementId,
                    curPage,
                    Rpp
                );

                List<Offer> lst = new List<Offer>();

                foreach (OfferDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.Offer obj = new Rebound.GlobalTrader.BLL.Offer
                    {
                        OfferId = objDetails.OfferId,
                        FullPart = objDetails.FullPart,
                        CustomerRequirementId = objDetails.CustomerRequirementId,
                        Part = objDetails.Part,
                        ManufacturerNo = objDetails.ManufacturerNo,
                        DateCode = objDetails.DateCode,
                        ProductNo = objDetails.ProductNo,
                        PackageNo = objDetails.PackageNo,
                        Quantity = objDetails.Quantity,
                        Price = objDetails.Price,
                        OriginalEntryDate = objDetails.OriginalEntryDate,
                        Salesman = objDetails.Salesman,
                        SupplierNo = objDetails.SupplierNo,
                        CurrencyNo = objDetails.CurrencyNo,
                        ROHS = objDetails.ROHS,
                        UpdatedBy = objDetails.UpdatedBy,
                        DLUP = objDetails.DLUP,
                        OfferStatusNo = objDetails.OfferStatusNo,
                        OfferStatusChangeDate = objDetails.OfferStatusChangeDate,
                        OfferStatusChangeLoginNo = objDetails.OfferStatusChangeLoginNo,
                        ManufacturerCode = objDetails.ManufacturerCode,
                        ProductName = objDetails.ProductName,
                        CurrencyCode = objDetails.CurrencyCode,
                        CurrencyDescription = objDetails.CurrencyDescription,
                        SupplierName = objDetails.SupplierName,
                        ManufacturerName = objDetails.ManufacturerName,
                        SupplierEmail = objDetails.SupplierEmail,
                        SalesmanName = objDetails.SalesmanName,
                        OfferStatusChangeEmployeeName = objDetails.OfferStatusChangeEmployeeName,
                        PackageName = objDetails.PackageName,
                        Notes = objDetails.Notes,
                        ClientNo = objDetails.ClientNo,
                        ClientId = objDetails.ClientId,
                        ClientName = objDetails.ClientName,
                        ClientDataVisibleToOthers = objDetails.ClientDataVisibleToOthers,
                        SupplierType = objDetails.SupplierType,
                        ClientCode = objDetails.ClientCode,
                        MSL = objDetails.MSL,
                        SPQ = objDetails.SPQ,
                        LeadTime = objDetails.LeadTime,
                        RoHSStatus = objDetails.RoHSStatus,
                        FactorySealed = objDetails.FactorySealed,
                        IPOBOMNo = objDetails.IPOBOMNo,
                        SupplierTotalQSA = objDetails.SupplierTotalQSA,
                        SupplierLTB = objDetails.SupplierLTB,
                        SupplierMOQ = objDetails.SupplierMOQ,
                        IsSourcingHub = objDetails.IsSourcingHub,
                        SupplierMessage = objDetails.SupplierMessage,
                        TotalCount = objDetails.TotalCount,
                        REQStatus = objDetails.REQStatus,
                        OfferAddFlag = objDetails.OfferAddFlag
                    };

                    lst.Add(obj);
                }

                return lst;
            }
            catch (Exception ex)
            {
                throw new Exception("Error executing IPOBOMAutoSource", ex);
            }
        }

        public static List<AutoSourcing> GetAutoSourcingResult(System.Int32 BOM, int? CustomerReqID, int curPage = 1, int Rpp = 5)
        {
            List<AutoSourcing> listSourcing = Rebound.GlobalTrader.DAL.SiteProvider.BOMManager.GetAutoSourcingResult(BOM, CustomerReqID, curPage, Rpp);
            return listSourcing;
        }

        public static DataTable UpdateAutoSourcing(int BOM,
                                                  int SourceId,
                                                  int? SPQ,
                                                  double ReSell,
                                                  double Cost,
                                                  int EditMfrId,
                                                  int EditProdId,
                                                  string Reason,
                                                  string part,
                                                  int? rohs,
                                                  int? countryOfOrigin,
                                                  string dateCode,
                                                  int? packageNo,
                                                  int quantity,
                                                  int? offerStatusNo,
                                                  string factorySealed,
                                                  int? mslNo,
                                                  string totalQSA,
                                                  string moq,
                                                  string ltb,
                                                  int currencyNo,
                                                  double? shippingCost,
                                                  string leadTime,
                                                  int? regionNo,
                                                  DateTime? deliveryDate,
                                                  int? supplierWarranty,
                                                  string rohsStatus,
                                                  string notes,
                                                  bool? isTestingRecommened,
                                                  int? UpdatedBy)
        {
            DataTable dt = Rebound.GlobalTrader.DAL.SiteProvider.BOMManager.UpdateAutoSourcing(BOM,
                                                                                               SourceId,
                                                                                               SPQ,
                                                                                               ReSell,
                                                                                               Cost,
                                                                                               EditMfrId,
                                                                                               EditProdId,
                                                                                               Reason,
                                                                                               part,
                                                                                               rohs,
                                                                                               countryOfOrigin,
                                                                                               dateCode,
                                                                                               packageNo,
                                                                                               quantity,
                                                                                               offerStatusNo,
                                                                                               factorySealed,
                                                                                               mslNo,
                                                                                               totalQSA,
                                                                                               moq,
                                                                                               ltb,
                                                                                               currencyNo,
                                                                                               shippingCost,
                                                                                               leadTime,
                                                                                               regionNo,
                                                                                               deliveryDate,
                                                                                               supplierWarranty,
                                                                                               rohsStatus,
                                                                                               notes,
                                                                                               isTestingRecommened,
                                                                                               UpdatedBy);
            return dt;
        }

        public static DataTable UpdateReasonForAutoSourcing(System.Int32? BOM, System.Int32? SourceId,  string Reason, int? UpdatedBy)
        {
            DataTable dt = Rebound.GlobalTrader.DAL.SiteProvider.BOMManager.UpdateReasonForAutoSourcing(BOM, SourceId, Reason, UpdatedBy);
            return dt;
        }

        public static DataTable ReplaceSourcingBOMManager(System.Int32 BOM, System.Int32 CustomerRequirementId, System.Int32 UpdatedBy, System.Int32 ReplaceSourceType)
        {
            DataTable dt = Rebound.GlobalTrader.DAL.SiteProvider.BOMManager.ReplaceSourcingBOMManager(BOM, CustomerRequirementId, UpdatedBy, ReplaceSourceType);
            return dt;
        }
        public static DataTable RemoveOfferBOMManager(System.Int32 BOM, System.Int32 SourceId, System.Int32 UpdatedBy)
        {
            DataTable dt = Rebound.GlobalTrader.DAL.SiteProvider.BOMManager.RemoveOfferBOMManager(BOM, SourceId, UpdatedBy);
            return dt;
        }
        
        public static DataTable AddNewOffer(System.Int32 BOM, int sourceId, System.Int32 CustomerRequirementId, System.Int32 UpdatedBy, System.Int32 ReplaceSourceType, string OfferSource)
        {
            DataTable dt = Rebound.GlobalTrader.DAL.SiteProvider.BOMManager.AddNewOffer(BOM, sourceId, CustomerRequirementId, UpdatedBy, ReplaceSourceType, OfferSource);
            return dt;
        }
        public static DataTable Save_PrimarySourcing(System.Int32 ClientId, System.Int32 SourcingResultId, System.Int32 CustomerRequirementId, System.Int32 UpdatedBy)
        {
            DataTable dt = Rebound.GlobalTrader.DAL.SiteProvider.BOMManager.Save_PrimarySourcing(ClientId, SourcingResultId, CustomerRequirementId, UpdatedBy);
            return dt;
        }
        public static DataTable Reset_PrimarySourcing(System.Int32 ClientId, System.Int32 SourcingResultId, System.Int32 CustomerRequirementId, System.Int32 UpdatedBy)
        {
            DataTable dt = Rebound.GlobalTrader.DAL.SiteProvider.BOMManager.Reset_PrimarySourcing(ClientId, SourcingResultId, CustomerRequirementId, UpdatedBy);
            return dt;
        }
        public static DataTable AssignUser(string BOMManagerids, System.Int32 AssignUserId)
        {
            DataTable dt = Rebound.GlobalTrader.DAL.SiteProvider.BOMManager.AssignUser(BOMManagerids, AssignUserId);
            return dt;
        }
        
        public static DataTable GetBOMManagerStatus(System.Int32 BOM)
        {
            DataTable dt = Rebound.GlobalTrader.DAL.SiteProvider.BOMManager.GetBOMManagerStatus(BOM);
            return dt;
        }

        public static List<SourcingResult> GetListForBOMManagerReleaseAll(System.Int32? BOMManagerID, System.Boolean isPOHub, int reqStatus)
        {
            List<SourcingResultDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.BOMManager.GetListForBOMManagerReleaseAll(BOMManagerID, isPOHub,reqStatus);
            if (lstDetails == null)
            {
                return new List<SourcingResult>();
            }
            else
            {
                List<SourcingResult> lst = new List<SourcingResult>();
                foreach (SourcingResultDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.SourcingResult obj = new Rebound.GlobalTrader.BLL.SourcingResult();
                    obj.SourcingResultId = objDetails.SourcingResultId;
                    obj.CustomerRequirementNo = objDetails.CustomerRequirementNo;
                    obj.FullPart = objDetails.FullPart;
                    obj.Part = objDetails.Part;
                    obj.Price = objDetails.Price;
                    obj.CurrencyCode = objDetails.CurrencyCode;
                    obj.DeliveryDate = objDetails.DeliveryDate;
                    obj.OriginalPrice = objDetails.OriginalPrice;
                    obj.ActualCurrencyCode = objDetails.ActualCurrencyCode;
                    obj.ActualCurrencyNo = objDetails.ActualCurrencyNo;
                    obj.Status1 = objDetails.Status1;
                    obj.Status2 = objDetails.Status2;
                    obj.CustomerRequirementNumber = objDetails.CustomerRequirementNumber;
                    obj.SupplierName = objDetails.SupplierName;
                    obj.HeaderFlag = objDetails.HeaderFlag;
                    obj.LineNumber = objDetails.LineNumber;
                    obj.SourceId = objDetails.SourceId;
                    obj.MissingRequiredFeilds = objDetails.MissingRequiredFeilds;
                    obj.ReleaseNote = objDetails.ReleaseNote;
                    obj.Reason = objDetails.Reason;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }
        public static List<String> GetUnsourcedParts(System.Int32? BOMManagerID)
        {
            List<String> lst = Rebound.GlobalTrader.DAL.SiteProvider.BOMManager.GetUnsourcedParts(BOMManagerID);
            if (lst == null)
            {
                return new List<String>();
            }
            else
                return lst;
        }

        public static DataTable BOMManagerReleaseRequirement(System.Int32? BOMManagerID, System.Int32? updatedBy, System.String CIds, System.Int32? ReqType, System.String NoBidNotes, System.String ASIds)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.BOMManager.BOMManagerReleaseRequirement(BOMManagerID, updatedBy,CIds,ReqType,NoBidNotes,ASIds);
        }
        /// <summary>
        /// Update Release note to Customer requirement
        /// </summary>
        /// <param name="lstReleaseNotes">list customer requirement release notes</param>
        /// <returns>list of data table with message</returns>
        public static List<DataTable> BOMManagerUpdateCustomerRequirementReleaseNotes(List<CustomerRReleaseNote> lstReleaseNotes)
        {
            List<DataTable> returnTables = new List<DataTable>();
            foreach (CustomerRReleaseNote item in lstReleaseNotes)
            {
                DataTable returnTable = Rebound.GlobalTrader.DAL.SiteProvider.BOMManager.BOMManagerUpdateCustomerRequirementReleaseNotes(item.CustomerRequirementID, item.ReleaseNote);
                returnTables.Add(returnTable);
            }
            return returnTables;
        }

        public static List<AutoSourcing> GetBOMManagerAutoSourcing(System.Int32 BOMManagerId, bool IsPOHUB, int? CustomerReqID, int curPage = 1, int Rpp = 5)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.BOMManager.GetBOMManagerAutoSourcing(BOMManagerId, IsPOHUB, CustomerReqID, curPage, Rpp);
        }
        public static bool EditSourcingResultsSalesman(System.Int32 CRID, Double UnitPrice, string Notes, System.Int32? updatedBy)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.BOMManager.EditSourcingResultsSalesman(CRID, UnitPrice, Notes, updatedBy);
        }
        public static QuoteDetails LoadQuoteGenerationDataBOMManager(int CustomerReqID, int BOMManagerNo)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.BOMManager.LoadQuoteGenerationDataBOMManager(CustomerReqID, BOMManagerNo);
        }
        public static Int32 InsertFromSourcingResultBOMManager(System.Int32? sourcingResultId, System.Int32? quoteNo, System.DateTime? dateQuoted, int BOMManagerID)
        {
            Int32 objReturn = Rebound.GlobalTrader.DAL.SiteProvider.BOMManager.InsertFromSourcingResultBOMManager(sourcingResultId, quoteNo, dateQuoted, BOMManagerID);
            return objReturn;
        }
        public static DataTable GetCustomTemplateData(int QuoteID)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.BOMManager.GetCustomTemplateData(QuoteID);
        }
        public static bool DeleteBOMManagerItem(int BOMManagerID, string CustReqIDs, int? UpdatedBy)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.BOMManager.DeleteBOMManagerItem(BOMManagerID, CustReqIDs, UpdatedBy); ;
        }
        public static bool SaveEditBOMItemData(int customerRequirementId, int RequirementforTraceability, int salesman, int quantity, System.Int32? Usage, int Type, System.String EAU, int manufacturerNo, string customerPart, Double TargetSellPrice, int currencyNo, System.Byte? rohs, string dateCode, int productNo, System.Int32? PackageNo, string MSL, DateTime DatePromised, System.DateTime? CustomerDecisionDate, System.DateTime? RFQClosingDate, System.Int32? QuoteValidityRequired, string BOMManagerName, System.String notes, System.String instructions, System.Int32? SupportTeamMemberNo, int BOMManagerID, System.Int32? UpdatedBy)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.BOMManager.SaveEditBOMItemData(customerRequirementId, RequirementforTraceability, salesman, quantity, Usage, Type, EAU, manufacturerNo, customerPart, TargetSellPrice, currencyNo, rohs, dateCode, productNo, PackageNo, MSL, DatePromised, CustomerDecisionDate, RFQClosingDate, QuoteValidityRequired, BOMManagerName, notes, instructions, SupportTeamMemberNo, BOMManagerID, UpdatedBy); ;
        }
        public static bool SaveUpliftAllPrice(int BOMManagerID, float UpliftPercentage, System.Int32? UpdatedBy)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.BOMManager.SaveUpliftAllPrice(BOMManagerID, UpliftPercentage, UpdatedBy); ;
        }
        public static DataTable GetUpliftPercentageAll(System.Int32 BOM)
        {
            DataTable dt = Rebound.GlobalTrader.DAL.SiteProvider.BOMManager.GetUpliftPercentageAll(BOM);
            return dt;
        }
        public static bool RemoveUpliftPriceAll(int BOMManagerID, System.Int32? UpdatedBy)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.BOMManager.RemoveUpliftPriceAll(BOMManagerID, UpdatedBy); ;
        }
        public static List<AutoSourcing> GetBOMManagerSourcingForUplift(System.Int32 BOMManagerId, System.String CustomerRequirementIds = "")
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.BOMManager.GetBOMManagerSourcingForUplift(BOMManagerId,CustomerRequirementIds);
        }
        public static bool SaveBOMItemNoBid(int BOMManagerID, int CustomerRequirementId, int? UpdatedBy, string NoBidReason)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.BOMManager.SaveBOMItemNoBid(BOMManagerID,CustomerRequirementId,UpdatedBy, NoBidReason); ;
        }
        public static bool RecallNoBidRequirement(System.Int32? customerRequirementId, System.Int32? updatedBy)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.BOMManager.RecallNoBidRequirement(customerRequirementId, updatedBy);
        }
        public static bool ResetUpliftPriceAll(int BOMManagerID, int? ClientID)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.BOMManager.ResetUpliftPriceAll(BOMManagerID, ClientID); ;
        }
        public static bool PHSaveEditBOMItemData(int customerRequirementId, int manufacturerNo, int productNo, int BOMManagerID, System.Int32? UpdatedBy)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.BOMManager.PHSaveEditBOMItemData(customerRequirementId, manufacturerNo, productNo, BOMManagerID, UpdatedBy); ;
        }

        /// <summary>
        /// GetBOMManagerListForCompany
        /// Calls [usp_selectAll_BOMManager_for_Company]
        /// </summary>
        public static List<BOMManagerContract> GetListForCompany(System.Int32? clientId, System.Int32? companyId, System.Boolean? includeClosed)
        {
            List<BOMManager> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.BOMManager.GetListForCompany(clientId, companyId, includeClosed);
            if (lstDetails == null)
            {
                return new List<BOMManagerContract>();
            }
            else
            {
                List<BOMManagerContract> lst = new List<BOMManagerContract>();
                foreach (BOMManager objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.BOMManagerContract obj = new Rebound.GlobalTrader.BLL.BOMManagerContract();
                    obj.BOMManagerId = objDetails.BOMManagerId;
                    obj.BOMManagerCode = objDetails.BOMManagerCode;
                    obj.BOMManagerName = objDetails.BOMManagerName;
                    obj.ReceivedDate = objDetails.ReceivedDate;
                    obj.SalesmanName = objDetails.SalesmanName;
                    obj.BOMManagerStatus = objDetails.BOMManagerStatus;
                    obj.TotalBomManagerLinePrice = objDetails.TotalBomManagerLinePrice;
                    obj.DateRequestToPOHub = objDetails.DateRequestToPOHub;
                    obj.DLUP = objDetails.DLUP;
                    obj.POCurrencyNo = objDetails.POCurrencyNo;

                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }

        /// <summary>
        /// CountForCompany
        /// Calls [usp_count_BOMManager_for_Company]
        /// </summary>
        public static Int32 CountForCompany(System.Int32? companyId, System.Boolean? includeClosed)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.BOMManager.CountForCompany(companyId, includeClosed);
        }
    }
}
