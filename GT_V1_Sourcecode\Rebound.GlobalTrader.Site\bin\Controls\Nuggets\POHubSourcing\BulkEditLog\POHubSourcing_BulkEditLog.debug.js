﻿///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />

Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.POHubSourcing_BulkEditLog = function (element) {
    Rebound.GlobalTrader.Site.Controls.Forms.POHubSourcing_BulkEditLog.initializeBase(this, [element]);
    this._sourcingType = "";
    this._partNo = "";
};

Rebound.GlobalTrader.Site.Controls.Forms.POHubSourcing_BulkEditLog.prototype = {
    get_ctlBulkEditLog: function () { return this._ctlBulkEditLog; }, set_ctlBulkEditLog: function (v) { if (this._ctlBulkEditLog !== v) this._ctlBulkEditLog = v; },

    initialize: function () {
        Rebound.GlobalTrader.Site.Controls.Forms.POHubSourcing_BulkEditLog.callBaseMethod(this, "initialize");
        this.addShown(Function.createDelegate(this, this.formShown));
    },
    formShown: function () {
        this._ctlBulkEditLog._sourcingType = this._sourcingType;
        this._ctlBulkEditLog.setFieldValue("ctlPartNo", this._partNo);
        this._ctlBulkEditLog.searchClicked();
    },
    clearFilterInput: function () {
        this._ctlBulkEditLog.clearFilterInput();
    },
    dispose: function () {
        if (this.isDisposed) return;
        if (this._ctlBulkEditLog) this._ctlBulkEditLog.dispose();
        this._ctlBulkEditLog = null;
        this._sourcingType = null;
        this._partNo = null;

        Rebound.GlobalTrader.Site.Controls.Forms.POHubSourcing_BulkEditLog.callBaseMethod(this, "dispose");
    }
};

Rebound.GlobalTrader.Site.Controls.Forms.POHubSourcing_BulkEditLog.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.POHubSourcing_BulkEditLog", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
