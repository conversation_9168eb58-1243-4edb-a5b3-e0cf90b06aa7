using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;

namespace Rebound.GlobalTrader.Site.Controls.DropDowns.Data
{
    [WebService(Namespace = "http://tempuri.org/")]
    [WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    public class GlobalCountryList : Rebound.GlobalTrader.Site.Data.DropDowns.Base
    {

        public override void ProcessRequest(HttpContext context)
        {
            SetDropDownType("GlobalCountryList");
            base.ProcessRequest(context);
        }

        protected override void GetData()
        {
            int? intGlobalLoginClientNo;
            intGlobalLoginClientNo = GetFormValue_NullableInt("GlobalLoginClientNo");

            JsonObject jsn = new JsonObject();
            JsonObject jsnCountries = new JsonObject(true);
            foreach (BLL.GlobalCountryList gcl in BLL.GlobalCountryList.DropDown(GetFormValue_Boolean("IncludeSelected"), (intGlobalLoginClientNo.HasValue && intGlobalLoginClientNo.Value > 0) ? intGlobalLoginClientNo.Value : SessionManager.ClientID))
            {
                JsonObject jsnCountry = new JsonObject();
                jsnCountry.AddVariable("ID", gcl.GlobalCountryId);
                jsnCountry.AddVariable("Name", gcl.GlobalCountryName);
                //jsnCountry.AddVariable("HighRiskContent", gcl.HighRisk == true ? Functions.GetGlobalResource("Status", "HighRiskCountryStatus") : "");
                jsnCountry.AddVariable("HighRiskContent", gcl.HighRisk == true ? "" : "");
                jsnCountries.AddVariable(jsnCountry);
                jsnCountry.Dispose(); jsnCountry = null;
            }
            jsn.AddVariable("GlobalCountryLists", jsnCountries);
            jsnCountries.Dispose();
            jsnCountries = null;
            OutputResult(jsn);
            jsn.Dispose(); jsn = null;
        }
    }
}
