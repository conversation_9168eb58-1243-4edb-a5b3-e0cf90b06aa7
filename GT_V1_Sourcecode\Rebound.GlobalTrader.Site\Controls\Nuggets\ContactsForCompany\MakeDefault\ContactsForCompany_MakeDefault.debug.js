///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//Marker     Changed by      Date         Remarks
//[001]      Vinay           09/10/2012   Degete Ref:#26#  - Add two more columns contact to identify Default Purchase ledger and Default Sales ledger
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.ContactsForCompany_MakeDefault = function(element) { 
	Rebound.GlobalTrader.Site.Controls.Forms.ContactsForCompany_MakeDefault.initializeBase(this, [element]);
	this._intContactID = -1;
	this._strContactName = "";
};

Rebound.GlobalTrader.Site.Controls.Forms.ContactsForCompany_MakeDefault.prototype = {

    get_strExplain_PO: function() { return this._strExplain_PO; }, set_strExplain_PO: function(v) { if (this._strExplain_PO !== v) this._strExplain_PO = v; },
    get_strTitle_PO: function() { return this._strTitle_PO; }, set_strTitle_PO: function(v) { if (this._strTitle_PO !== v) this._strTitle_PO = v; },
    get_strExplain_SO: function() { return this._strExplain_SO; }, set_strExplain_SO: function(v) { if (this._strExplain_SO !== v) this._strExplain_SO = v; },
    get_strTitle_SO: function() { return this._strTitle_SO; }, set_strTitle_SO: function(v) { if (this._strTitle_SO !== v) this._strTitle_SO = v; },

    //[001] code start
    get_strExplain_POLedger: function() { return this._strExplain_POLedger; }, set_strExplain_POLedger: function(v) { if (this._strExplain_POLedger !== v) this._strExplain_POLedger = v; },
    get_strTitle_POLedger: function() { return this._strTitle_POLedger; }, set_strTitle_POLedger: function(v) { if (this._strTitle_POLedger !== v) this._strTitle_POLedger = v; },
    get_strExplain_SOLedger: function() { return this._strExplain_SOLedger; }, set_strExplain_SOLedger: function(v) { if (this._strExplain_SOLedger !== v) this._strExplain_SOLedger = v; },
    get_strTitle_SOLedger: function() { return this._strTitle_SOLedger; }, set_strTitle_SOLedger: function(v) { if (this._strTitle_SOLedger !== v) this._strTitle_SOLedger = v; },
    //[001] code end

    initialize: function() {
        Rebound.GlobalTrader.Site.Controls.Forms.ContactsForCompany_MakeDefault.callBaseMethod(this, "initialize");
        this.addShown(Function.createDelegate(this, this.formShown));
        this.addModeChanged(Function.createDelegate(this, this.modeChanged));
    },

    dispose: function() {
        if (this.isDisposed) return;
        if (this._ctlConfirm) this._ctlConfirm.dispose();
        this._ctlConfirm = null;
        this._intContactID = null;
        this._strContactName = null;
        this._strExplain_PO = null;
        this._strTitle_PO = null;
        this._strExplain_SO = null;
        this._strTitle_SO = null;
        //[001] code start
        this._strExplain_POLedger = null;
        this._strTitle_POLedger = null;
        this._strExplain_SOLedger = null;
        this._strTitle_SOLedger = null;
        //[001] code end
        Rebound.GlobalTrader.Site.Controls.Forms.ContactsForCompany_MakeDefault.callBaseMethod(this, "dispose");
    },

    formShown: function() {
        if (this._blnFirstTimeShown) {
            this._ctlConfirm = this.getFieldComponent("ctlConfirm");
            this._ctlConfirm.addClickYesEvent(Function.createDelegate(this, this.yesClicked));
            this._ctlConfirm.addClickNoEvent(Function.createDelegate(this, this.noClicked));
        }
    },
    //[001] code start
    modeChanged: function() {
        if (this._mode == "SO") {
            this.changeTitle(this._strTitle_SO);
            this.changeExplanation(this._strExplain_SO);
        } else if (this._mode == "PO") {
            this.changeTitle(this._strTitle_PO);
            this.changeExplanation(this._strExplain_PO);
        }
        else if (this._mode == "POLedger") {
            this.changeTitle(this._strTitle_POLedger);
            this.changeExplanation(this._strExplain_POLedger);
        }
        else {
            this.changeTitle(this._strTitle_SOLedger);
            this.changeExplanation(this._strExplain_SOLedger);
        }
    },
    //[001] code end
    yesClicked: function() {
        this.showSaving(true);
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/ContactsForCompany");
        obj.set_DataObject("ContactsForCompany");
        if (this._mode == "SO") {
            obj.set_DataAction("MakeDefaultSO");
        } else if (this._mode == "PO") {
            obj.set_DataAction("MakeDefaultPO");
        }
        else if (this._mode == "POLedger") {
            obj.set_DataAction("MakeDefaultPOLedger");
        }
        else {
            obj.set_DataAction("MakeDefaultSOLedger");
        }
        obj.addParameter("ID", this._intCompanyID);
        obj.addParameter("ContactID", this._intContactID);
        obj.addDataOK(Function.createDelegate(this, this.saveDeleteComplete));
        obj.addError(Function.createDelegate(this, this.saveDeleteError));
        obj.addTimeout(Function.createDelegate(this, this.saveDeleteError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    noClicked: function() {
        this.onNotConfirmed();
    },

    saveDeleteError: function(args) {
        this._strErrorMessage = args._errorMessage;
        this.onSaveError();
    },

    saveDeleteComplete: function(args) {
        this.showSaving(false);
        if (args._result.Result == true) {
            this.onSaveComplete();
        } else {
            this._strErrorMessage = args._errorMessage;
            this.onSaveError();
        }
    }

};

Rebound.GlobalTrader.Site.Controls.Forms.ContactsForCompany_MakeDefault.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.ContactsForCompany_MakeDefault", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
