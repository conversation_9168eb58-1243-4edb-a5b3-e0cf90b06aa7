﻿/*     
===========================================================================================    
TASK   UPDATED BY   DATE   ACTION  DESCRIPTION    
[US-216162]  CuongDox  1-Sep-2024  CREATE  Update logs 
===========================================================================================    
*/  
CREATE OR ALTER PROCEDURE usp_Get_ProspectiveOffer_Lines_ByID    
 @ProspectiveOfferLineId INT
AS  
BEGIN  
WITH CTE_OfferLines AS (
    SELECT 
        offer.SourceFileName,
        line.Part,
        line.ProspectiveOfferLineId,
        ROW_NUMBER() OVER (ORDER BY line.ProspectiveOfferLineId) AS RowIndex
    FROM 
        tbProspectiveOfferLines line
    INNER JOIN 
        tbProspectiveOffers offer ON offer.ProspectiveOfferId = line.ProspectiveOfferNo AND offer.ProspectiveOfferId IN(select ProspectiveOfferNo from tbProspectiveOfferLines l where l.ProspectiveOfferLineId = @ProspectiveOfferLineId)
)
-- Select the row index for the specific ProspectiveOfferLineId
SELECT 
    RowIndex as ImportRowCount,
	SourceFileName,
    Part,
    ProspectiveOfferLineId
FROM 
    CTE_OfferLines
WHERE 
    ProspectiveOfferLineId = @ProspectiveOfferLineId;
END  