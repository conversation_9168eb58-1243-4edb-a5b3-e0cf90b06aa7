///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.AutoSearch");

Rebound.GlobalTrader.Site.Controls.AutoSearch.SerialNo = function (element) {
    Rebound.GlobalTrader.Site.Controls.AutoSearch.SerialNo.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.AutoSearch.SerialNo.prototype = {

get_ShowInactive: function() { return this._ShowInactive; }, set_ShowInactive: function(value) { if (this._ShowInactive !== value)  this._ShowInactive = value; }, 
//get_intPOHubClientNo: function () { return this._intPOHubClientNo; }, set_intPOHubClientNo: function (v) { if (this._intPOHubClientNo !== v) this._intPOHubClientNo = v; },

	initialize: function(){
	    Rebound.GlobalTrader.Site.Controls.AutoSearch.SerialNo.callBaseMethod(this, "initialize");
		this.addDataReturnedEvent(Function.createDelegate(this, this.dataReturned));
    	//this.addSetupParametersEvent(Function.createDelegate(this, this.setupParameters));
	
		this.setupDataObject("SerialNo");
	},
	
	dispose: function(){
		if (this.isDisposed) return;
		this._ShowInactive = null;
		//this._intPOHubClientNo = null;
		Rebound.GlobalTrader.Site.Controls.AutoSearch.SerialNo.callBaseMethod(this, "dispose");
	},
	
	//setupParameters: function() {
//	alert("hi");
//	//this.addParameter("blnShowInactive", this._ShowInactive);
    //   this.addDataParameter("intPOHubClientNo", this._intPOHubClientNo);

	//},
	
	dataReturned: function(){
	

	    //if (this._objData._result.Results.length > 0) {
	    //    for (var i = 0; i < this._objData._result.Results.length; i++) {
	    //        this.addOption(this._objData._result.Results[i].Group, this._objData._result.Results[i].Group);
	    //    }
	    //}
	    if (!this._result.Results) return;
	    if (this._result.Results.length > 0) {
			for (var i = 0, l = this._result.Results.length; i < l; i++) {
				var res = this._result.Results[i];
				var strHTML = "";
				if (this._enmResultsActionType == $R_ENUM$AutoSearchResultsActionType.Navigate) {
				    strHTML = $RGT_nubButton_Manufacturer(res.ID, res.SerialNo);
				} else {
				    strHTML = $R_FN.setCleanTextValue(res.SerialNo);
				}
				this.addResultItem(strHTML, $R_FN.setCleanTextValue(res.SerialNo), res.ID);
				strHTML = null;
				res = null;
			}
		}
	}
};
Rebound.GlobalTrader.Site.Controls.AutoSearch.SerialNo.registerClass("Rebound.GlobalTrader.Site.Controls.AutoSearch.SerialNo", Rebound.GlobalTrader.Site.Controls.AutoSearch.Base, Sys.IDisposable);
