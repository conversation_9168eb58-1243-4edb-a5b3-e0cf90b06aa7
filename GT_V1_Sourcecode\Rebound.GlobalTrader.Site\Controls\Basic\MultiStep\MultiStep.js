Type.registerNamespace("Rebound.GlobalTrader.Site.Controls");Rebound.GlobalTrader.Site.Controls.MultiStep=function(n){Rebound.GlobalTrader.Site.Controls.MultiStep.initializeBase(this,[n]);this._intTotalSteps=0};Rebound.GlobalTrader.Site.Controls.MultiStep.prototype={get_aryItemIDs:function(){return this._aryItemIDs},set_aryItemIDs:function(n){this._aryItemIDs!==n&&(this._aryItemIDs=n)},get_aryContentFormIDs:function(){return this._aryContentFormIDs},set_aryContentFormIDs:function(n){this._aryContentFormIDs!==n&&(this._aryContentFormIDs=n)},get_aryItemLinkIDs:function(){return this._aryItemLinkIDs},set_aryItemLinkIDs:function(n){this._aryItemLinkIDs!==n&&(this._aryItemLinkIDs=n)},get_arySeparatorIDs:function(){return this._arySeparatorIDs},set_arySeparatorIDs:function(n){this._arySeparatorIDs!==n&&(this._arySeparatorIDs=n)},get_aryExplainLabelIDs:function(){return this._aryExplainLabelIDs},set_aryExplainLabelIDs:function(n){this._aryExplainLabelIDs!==n&&(this._aryExplainLabelIDs=n)},get_pnlMultiStep:function(){return this._pnlMultiStep},set_pnlMultiStep:function(n){this._pnlMultiStep!==n&&(this._pnlMultiStep=n)},get_intCurrentStep:function(){return this._intCurrentStep},set_intCurrentStep:function(n){this._intCurrentStep!==n&&(this._intCurrentStep=n)},addStepChanged:function(n){this.get_events().addHandler("StepChanged",n)},removeStepChanged:function(n){this.get_events().removeHandler("StepChanged",n)},onStepChanged:function(){var n=this.get_events().getHandler("StepChanged");n&&n(this,new Rebound.GlobalTrader.Site.DataEventArgs(this._statusCode,this._errorMessage,this._result,this._url))},initialize:function(){Rebound.GlobalTrader.Site.Controls.MultiStep.callBaseMethod(this,"initialize");this._intTotalSteps=this._aryContentFormIDs.length},dispose:function(){this.isDisposed||(this.get_element()&&$clearHandlers(this.get_element()),this._aryItemIDs=null,this._aryContentFormIDs=null,this._aryItemLinkIDs=null,this._arySeparatorIDs=null,this._aryExplainLabelIDs=null,this._pnlMultiStep=null,this._intTotalSteps=null,Rebound.GlobalTrader.Site.Controls.MultiStep.callBaseMethod(this,"dispose"),this.isDisposed=!0)},gotoStep:function(n){var t,e,i,r,u,f;for(this._intCurrentStep=n,t=1,e=this._intTotalSteps;t<=e;t++)$R_FN.showElement($get(this._aryContentFormIDs[t-1]),t==this._intCurrentStep),$R_FN.showElement($get(this._aryExplainLabelIDs[t-1]),t==this._intCurrentStep),i=this.currentStepTimeString(t),r=$get(this._aryItemIDs[t-1]),r&&(r.className=String.format("step step{0}",i)),u=$get(this._arySeparatorIDs[t-1]),u&&(u.className=String.format("sep sep_{0}_{1}",i,this.nextStepTimeString(t))),f=$get(this._aryItemLinkIDs[t-1]),f.setAttribute("onclick","void(0);"),i=="Past"&&f.setAttribute("onclick",String.format("$find('{0}').gotoStep({1});",this.get_element().id,t)),r=null,u=null,f=null;$R_FN.isElementVisible(this._pnlMultiStep)&&$R_FN.scrollPageToElement(this._pnlMultiStep,-95);this.onStepChanged()},nextStep:function(){var n=this._intCurrentStep+1;n>this._intTotalSteps&&(n=this._intTotalSteps);this.gotoStep(n)},prevStep:function(){var n=this._intCurrentStep-1;n<1&&(n=1);this.gotoStep(n)},currentStepTimeString:function(n){var t="Future";return n==this._intCurrentStep&&(t="Current"),n<this._intCurrentStep&&(t="Past"),t},nextStepTimeString:function(n){return n+=1,n>this._intTotalSteps?"End":this.currentStepTimeString(n)},disableStep:function(n){var i=$get(this._aryItemLinkIDs[n-1]),t;i&&i.setAttribute("onclick","void(0);");t=$get(this._aryItemIDs[n-1]);t&&(t.className+=" stepDisabled");i=null;t=null},showSteps:function(n){$R_FN.showElement(this._pnlMultiStep,n)},showExplainLabel:function(n){$R_FN.showElement($get(this._aryExplainLabelIDs[this._intCurrentStep-1]),n)}};Rebound.GlobalTrader.Site.Controls.MultiStep.registerClass("Rebound.GlobalTrader.Site.Controls.MultiStep",Sys.UI.Control,Sys.IDisposable);