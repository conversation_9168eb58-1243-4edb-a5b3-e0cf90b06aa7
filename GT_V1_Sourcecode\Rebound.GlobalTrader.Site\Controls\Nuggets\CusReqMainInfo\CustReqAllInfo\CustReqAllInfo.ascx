<%@ Control Language="C#" CodeBehind="CustReqAllInfo.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Nuggets.CustReqAllInfo" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Nugget:DesignBase ID="ctlDB" runat="server" TitleText="Main Information" BoxType="InPageSelection" ShowRefreshButton="false">
    <Content>
    <asp:Panel ID="pnlLoadingLineDetail" runat="server" CssClass="subInfoLoading invisible"><%=Functions.GetGlobalResource("Misc", "Loading")%></asp:Panel>
    <asp:Panel ID="pnlCustReqAllDoc" runat="server"></asp:Panel>
    </Content>
</ReboundUI_Nugget:DesignBase>
