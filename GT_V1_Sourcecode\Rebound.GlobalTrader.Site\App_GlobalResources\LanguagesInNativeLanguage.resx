﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Afrikaans" xml:space="preserve">
    <value>Afrikaans</value>
  </data>
  <data name="Albanian" xml:space="preserve">
    <value>shqipe</value>
  </data>
  <data name="Armenian" xml:space="preserve">
    <value>Հայերեն</value>
  </data>
  <data name="Basque" xml:space="preserve">
    <value>euskara</value>
  </data>
  <data name="Belarusian" xml:space="preserve">
    <value>Беларускі</value>
  </data>
  <data name="Bulgarian" xml:space="preserve">
    <value>български</value>
  </data>
  <data name="Chinese" xml:space="preserve">
    <value>中文</value>
  </data>
  <data name="Chinese_HongKong" xml:space="preserve">
    <value>中文(香港特别行政區)</value>
  </data>
  <data name="Chinese_Macau" xml:space="preserve">
    <value>中文(澳門特别行政區)</value>
  </data>
  <data name="Chinese_Singapore" xml:space="preserve">
    <value>中文(新加坡)</value>
  </data>
  <data name="Chinese_Taiwan" xml:space="preserve">
    <value>中文(台灣)</value>
  </data>
  <data name="Croatian" xml:space="preserve">
    <value>hrvatski</value>
  </data>
  <data name="Czech" xml:space="preserve">
    <value>čeština</value>
  </data>
  <data name="Danish" xml:space="preserve">
    <value>dansk</value>
  </data>
  <data name="Dutch" xml:space="preserve">
    <value>Nederlands</value>
  </data>
  <data name="Dutch_Belgium" xml:space="preserve">
    <value>Nederlands (België)</value>
  </data>
  <data name="English" xml:space="preserve">
    <value>English</value>
  </data>
  <data name="English_Australia" xml:space="preserve">
    <value>English (Australia)</value>
  </data>
  <data name="English_Canada" xml:space="preserve">
    <value>English (Canada)</value>
  </data>
  <data name="English_Ireland" xml:space="preserve">
    <value>English (Eire)</value>
  </data>
  <data name="English_NewZealand" xml:space="preserve">
    <value>English (New Zealand)</value>
  </data>
  <data name="English_SouthAfrica" xml:space="preserve">
    <value>English (South Africa)</value>
  </data>
  <data name="English_UnitedStates" xml:space="preserve">
    <value>English (United States)</value>
  </data>
  <data name="English_Zimbabwe" xml:space="preserve">
    <value>English (Zimbabwe)</value>
  </data>
  <data name="Estonian" xml:space="preserve">
    <value>eesti</value>
  </data>
  <data name="Finnish" xml:space="preserve">
    <value>suomi</value>
  </data>
  <data name="French" xml:space="preserve">
    <value>français</value>
  </data>
  <data name="French_Belgium" xml:space="preserve">
    <value>français (Belgique)</value>
  </data>
  <data name="French_Canada" xml:space="preserve">
    <value>français (Canada)</value>
  </data>
  <data name="French_Luxembourg" xml:space="preserve">
    <value>français (Luxembourg)</value>
  </data>
  <data name="French_Monaco" xml:space="preserve">
    <value>français (Principauté de Monaco)</value>
  </data>
  <data name="French_Switzerland" xml:space="preserve">
    <value>français (Suisse)</value>
  </data>
  <data name="Georgian" xml:space="preserve">
    <value>ქართული</value>
  </data>
  <data name="German" xml:space="preserve">
    <value>Deutsche</value>
  </data>
  <data name="German_Austria" xml:space="preserve">
    <value>Deutsch (Österreich)</value>
  </data>
  <data name="German_Switzerland" xml:space="preserve">
    <value>Deutsch (Schweiz)</value>
  </data>
  <data name="Greek" xml:space="preserve">
    <value>ελληνικά</value>
  </data>
  <data name="Gujarati" xml:space="preserve">
    <value>ગુજરાતી</value>
  </data>
  <data name="Hebrew" xml:space="preserve">
    <value>עברית</value>
  </data>
  <data name="Hindi" xml:space="preserve">
    <value>हिंदी</value>
  </data>
  <data name="hrvatski_BosnaiHercegovina" xml:space="preserve">
    <value>hrvatski (Bosna i Hercegovina)</value>
  </data>
  <data name="Hungarian" xml:space="preserve">
    <value>magyar</value>
  </data>
  <data name="Icelandic" xml:space="preserve">
    <value>íslenska</value>
  </data>
  <data name="Indonesian" xml:space="preserve">
    <value>Bahasa Indonesia</value>
  </data>
  <data name="Italian" xml:space="preserve">
    <value>Italiano</value>
  </data>
  <data name="Italian_Switzerland" xml:space="preserve">
    <value>italiano (Svizzera)</value>
  </data>
  <data name="Japanese" xml:space="preserve">
    <value>日本語</value>
  </data>
  <data name="Kannada" xml:space="preserve">
    <value>ಕನ್ನಡ</value>
  </data>
  <data name="Kazakh" xml:space="preserve">
    <value>Қазащb</value>
  </data>
  <data name="Kiswahili_Kenya" xml:space="preserve">
    <value>Kiswahili</value>
  </data>
  <data name="Konkani" xml:space="preserve">
    <value>कोंकणी</value>
  </data>
  <data name="Korean" xml:space="preserve">
    <value>한국어</value>
  </data>
  <data name="Kyrgyz" xml:space="preserve">
    <value>Кыргыз</value>
  </data>
  <data name="Latvian" xml:space="preserve">
    <value>latviešu</value>
  </data>
  <data name="Lithuanian" xml:space="preserve">
    <value>lietuvių</value>
  </data>
  <data name="Macedonian" xml:space="preserve">
    <value>македонски јазик</value>
  </data>
  <data name="Malay" xml:space="preserve">
    <value>Bahasa Malaysia</value>
  </data>
  <data name="Maltese" xml:space="preserve">
    <value>Malti (Malta)</value>
  </data>
  <data name="Marathi" xml:space="preserve">
    <value>मराठी</value>
  </data>
  <data name="Mongolian" xml:space="preserve">
    <value>Монгол хэл</value>
  </data>
  <data name="Norwegian" xml:space="preserve">
    <value>norsk</value>
  </data>
  <data name="Polish" xml:space="preserve">
    <value>polski</value>
  </data>
  <data name="Portuguese" xml:space="preserve">
    <value>Português</value>
  </data>
  <data name="Portuguese_Brazil" xml:space="preserve">
    <value>Português (Brasil)</value>
  </data>
  <data name="Punjabi" xml:space="preserve">
    <value>ਪੰਜਾਬੀ</value>
  </data>
  <data name="Romanian" xml:space="preserve">
    <value>română</value>
  </data>
  <data name="Russian" xml:space="preserve">
    <value>русский</value>
  </data>
  <data name="Sanskrit" xml:space="preserve">
    <value>संस्कृत</value>
  </data>
  <data name="Slovak" xml:space="preserve">
    <value>slovenčina</value>
  </data>
  <data name="Slovenian" xml:space="preserve">
    <value>slovenski</value>
  </data>
  <data name="Spanish" xml:space="preserve">
    <value>Español</value>
  </data>
  <data name="Spanish_Argentina" xml:space="preserve">
    <value>Español (Argentina)</value>
  </data>
  <data name="Spanish_Bolivia" xml:space="preserve">
    <value>Español (Bolivia)</value>
  </data>
  <data name="Spanish_Chile" xml:space="preserve">
    <value>Español (Chile)</value>
  </data>
  <data name="Spanish_Colombia" xml:space="preserve">
    <value>Español (Colombia)</value>
  </data>
  <data name="Spanish_CostaRica" xml:space="preserve">
    <value>Español (Costa Rica)</value>
  </data>
  <data name="Spanish_DominicanRepublic" xml:space="preserve">
    <value>Español (República Dominicana)</value>
  </data>
  <data name="Spanish_Ecuador" xml:space="preserve">
    <value>Español (Ecuador)</value>
  </data>
  <data name="Spanish_ElSalvador" xml:space="preserve">
    <value>Español (El Salvador)</value>
  </data>
  <data name="Spanish_Guatemala" xml:space="preserve">
    <value>Español (Guatemala)</value>
  </data>
  <data name="Spanish_Honduras" xml:space="preserve">
    <value>Español (Honduras)</value>
  </data>
  <data name="Spanish_Mexico" xml:space="preserve">
    <value>Español (México)</value>
  </data>
  <data name="Spanish_Nicaragua" xml:space="preserve">
    <value>Español (Nicaragua)</value>
  </data>
  <data name="Spanish_Panama" xml:space="preserve">
    <value>Español (Panamá)</value>
  </data>
  <data name="Spanish_Paraguay" xml:space="preserve">
    <value>Español (Paraguay)</value>
  </data>
  <data name="Spanish_Peru" xml:space="preserve">
    <value>Español (Perú)</value>
  </data>
  <data name="Spanish_PuertoRico" xml:space="preserve">
    <value>Español (Puerto Rico)</value>
  </data>
  <data name="Spanish_Uruguay" xml:space="preserve">
    <value>Español (Uruguay)</value>
  </data>
  <data name="Spanish_Venezuela" xml:space="preserve">
    <value>Español (Republica Bolivariana de Venezuela)</value>
  </data>
  <data name="Swedish" xml:space="preserve">
    <value>svenska</value>
  </data>
  <data name="Syriac" xml:space="preserve">
    <value>ܣܘܪܝܝܐ</value>
  </data>
  <data name="Tamil" xml:space="preserve">
    <value>தமிழ்</value>
  </data>
  <data name="Tatar" xml:space="preserve">
    <value>Татар</value>
  </data>
  <data name="Telugu" xml:space="preserve">
    <value>తెలుగు</value>
  </data>
  <data name="Thai" xml:space="preserve">
    <value>ไทย</value>
  </data>
  <data name="Turkish" xml:space="preserve">
    <value>Türkçe</value>
  </data>
  <data name="Ukrainian" xml:space="preserve">
    <value>україньска</value>
  </data>
  <data name="Urdu" xml:space="preserve">
    <value>اُردو</value>
  </data>
  <data name="Vietnamese" xml:space="preserve">
    <value>Tiếng Việt</value>
  </data>
  <data name="Welsh" xml:space="preserve">
    <value>Cymraeg (y Deyrnas Unedig)</value>
  </data>
</root>