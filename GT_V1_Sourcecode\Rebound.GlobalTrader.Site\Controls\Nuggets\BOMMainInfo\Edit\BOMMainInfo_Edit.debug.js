///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference name="~/Controls/IconButton/IconButton.js" />
///<reference name="~/Controls/ReboundTextBox/ReboundTextBox.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//Marker     Changed by      Date         Remarks
//[001]      Vinay           12/06/2014   EMS Ticket No:	165
//[002]      Vinay           28/07/2015   EMS Ticket No:	254
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.BOMMainInfo_Edit = function(element) { 
	Rebound.GlobalTrader.Site.Controls.Forms.BOMMainInfo_Edit.initializeBase(this, [element]);
	this._intBOMID = -1;
    this._blnRequestedToPoHub = false;
    //this._BomName = "";
	
};

Rebound.GlobalTrader.Site.Controls.Forms.BOMMainInfo_Edit.prototype = {

    get_intBOMID: function() { return this._intBOMID; }, set_intBOMID: function(value) { if (this._intBOMID !== value) this._intBOMID = value; },
    get_BomCode: function() { return this._BomCode; }, set_BomCode: function(value) { if (this._BomCode !== value) this._BomCode = value; },
    get_BomName: function() { return this._BomName; }, set_BomName: function(value) { if (this._BomName !== value) this._BomName = value; },
    get_BomCompanyName: function() { return this._BomCompanyName; }, set_BomCompanyName: function(value) { if (this._BomCompanyName !== value) this._BomCompanyName = value; },
    get_BomCompanyNo: function () { return this._BomCompanyNo; }, set_BomCompanyNo: function (value) { if (this._BomCompanyNo !== value) this._BomCompanyNo = value; },
   
    initialize: function() {
        Rebound.GlobalTrader.Site.Controls.Forms.BOMMainInfo_Edit.callBaseMethod(this, "initialize");
        this.addShown(Function.createDelegate(this, this.formShown));
    },

    formShown: function() {
        if (this._blnFirstTimeShown) {
            this.addSave(Function.createDelegate(this, this.saveClicked));
        }
        //this.getFieldDropDownData("ctlCompany");
        //this.getFieldDropDownData("ctlContact");
        
        if (this._blnRequestedToPoHub) {
            this.showField("ctlCode", false);
            this.showField("ctlName", false);
            this.showField("ctlCompany", false);
            this.showField("ctlContact", false);
            this.showField("ctlInActive", false);
            this.showField("ctlNotes", false);
            this.showField("ctlCurrency", false);
            this.showField("ctlCurrentSupplier", false);
            this.showField("ctlQuoteRequired", false);
            this.showField("ctlAS9120", false);
            
        }
    },

    dispose: function() {
        if (this.isDisposed) return;
        this._intBOMID = null;
        
        Rebound.GlobalTrader.Site.Controls.Forms.BOMMainInfo_Edit.callBaseMethod(this, "dispose");
    },

    saveClicked: function () {
        if (this._blnRequestedToPoHub==false) {
            if (!this.validateForm()) return;
        }
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/BOMMainInfo");
        obj.set_DataObject("BOMMainInfo");
        obj.set_DataAction("SaveEdit");
        obj.addParameter("id", this._intBOMID);
        obj.addParameter("Code", this.getFieldValue("ctlCode"));
        obj.addParameter("Name", this.getFieldValue("ctlName"));
        obj.addParameter("Company", this.getFieldValue("ctlCompany"));
        obj.addParameter("Contact", this.getFieldValue("ctlContact"));
        obj.addParameter("InActive", this.getFieldValue("ctlInActive"));
        obj.addParameter("Notes", this.getFieldValue("ctlNotes"));
        obj.addParameter("Currency", this.getFieldValue("ctlCurrency"));

        obj.addParameter("CurrentSupplier", this.getFieldValue("ctlCurrentSupplier"));
        obj.addParameter("QuoteRequired", this.getFieldValue("ctlQuoteRequired"));
        obj.addParameter("AS9120", this.getFieldValue("ctlAS9120"));
        obj.addParameter("Contact2No", this.getFieldValue("ctlSalesman"));
        //[002] code end
        obj.addDataOK(Function.createDelegate(this, this.saveEditComplete));
        obj.addError(Function.createDelegate(this, this.saveEditError));
        obj.addTimeout(Function.createDelegate(this, this.saveEditError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    saveEditError: function(args) {
        this._strErrorMessage = args._errorMessage;
        this.onSaveError();
    },

    saveEditComplete: function(args) {
        if (args._result.Result == true) {
            this.showSavedOK(true);
            this.onSaveComplete();
            $("#ctl00_cphMain_ctlMainInfo_ctlDB_BuyAndPriceMergine").hide();
        } else {
            this._strErrorMessage = args._errorMessage;
            this.onSaveError();
        }
    },

    validateForm: function() {
        this.onValidate();
        var blnOK = this.autoValidateFields();
        
        return blnOK;
    },
    showFieldsLoading: function(bln) {
        this.showFieldLoading("ctlCompany", bln);
    }

};

Rebound.GlobalTrader.Site.Controls.Forms.BOMMainInfo_Edit.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.BOMMainInfo_Edit", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
