///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-------------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//
// RP 17.11.2009:
// - get the YTD / last year values in one hit
// - don't get data straight away because the Company Detail page now switches tabs
//   with javascript


/*

Marker     Changed By         Date          Remarks

[001]      Pankaj Kumar       27/10/20011   ESMS Ref:10 - Data drop out since Thursday
[002]      Shashi Keshar      20/01/2016    Added Insurance History in Detail Section
[003]      Aashu Singh        14-Sep-2018   [REB-12820]:Provision to add Global Security on Contact Section

*/
//-------------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Nuggets");

Rebound.GlobalTrader.Site.Controls.Nuggets.CompanySalesInfo = function(element) { 
	Rebound.GlobalTrader.Site.Controls.Nuggets.CompanySalesInfo.initializeBase(this, [element]);
	this._intCompanyID = -1;
	this._warehouseNo = -1;
};

Rebound.GlobalTrader.Site.Controls.Nuggets.CompanySalesInfo.prototype = {

    get_intCompanyID: function() { return this._intCompanyID; }, set_intCompanyID: function(value) { if (this._intCompanyID !== value) this._intCompanyID = value; },
    get_ibtnEdit: function() { return this._ibtnEdit; }, set_ibtnEdit: function(value) { if (this._ibtnEdit !== value) this._ibtnEdit = value; },
    get_ibtnAdd: function() { return this._ibtnAdd; }, set_ibtnAdd: function(value) { if (this._ibtnAdd !== value) this._ibtnAdd = value; },
    get_tblOpenSOs: function() { return this._tblOpenSOs; }, set_tblOpenSOs: function(value) { if (this._tblOpenSOs !== value) this._tblOpenSOs = value; },
    get_pnlOpenSOs: function() { return this._pnlOpenSOs; }, set_pnlOpenSOs: function(value) { if (this._pnlOpenSOs !== value) this._pnlOpenSOs = value; },
    get_pnlGetOpenSOs: function() { return this._pnlGetOpenSOs; }, set_pnlGetOpenSOs: function(value) { if (this._pnlGetOpenSOs !== value) this._pnlGetOpenSOs = value; },
    get_hypGetOpenSOs: function() { return this._hypGetOpenSOs; }, set_hypGetOpenSOs: function(value) { if (this._hypGetOpenSOs !== value) this._hypGetOpenSOs = value; },
    get_pnlLoadingOpenSOs: function() { return this._pnlLoadingOpenSOs; }, set_pnlLoadingOpenSOs: function(value) { if (this._pnlLoadingOpenSOs !== value) this._pnlLoadingOpenSOs = value; },
    get_pnlOpenSOsError: function() { return this._pnlOpenSOsError; }, set_pnlOpenSOsError: function(value) { if (this._pnlOpenSOsError !== value) this._pnlOpenSOsError = value; },
    get_tblOverdueSOs: function() { return this._tblOverdueSOs; }, set_tblOverdueSOs: function(value) { if (this._tblOverdueSOs !== value) this._tblOverdueSOs = value; },
    get_pnlOverdueSOs: function() { return this._pnlOverdueSOs; }, set_pnlOverdueSOs: function(value) { if (this._pnlOverdueSOs !== value) this._pnlOverdueSOs = value; },
    get_pnlLoadingOverdueSOs: function() { return this._pnlLoadingOverdueSOs; }, set_pnlLoadingOverdueSOs: function(value) { if (this._pnlLoadingOverdueSOs !== value) this._pnlLoadingOverdueSOs = value; },
    get_pnlOverdueSOsError: function() { return this._pnlOverdueSOsError; }, set_pnlOverdueSOsError: function(value) { if (this._pnlOverdueSOsError !== value) this._pnlOverdueSOsError = value; },
    get_pnlGetOverdueSOs: function() { return this._pnlGetOverdueSOs; }, set_pnlGetOverdueSOs: function(value) { if (this._pnlGetOverdueSOs !== value) this._pnlGetOverdueSOs = value; },
    get_hypGetOverdueSOs: function() { return this._hypGetOverdueSOs; }, set_hypGetOverdueSOs: function(value) { if (this._hypGetOverdueSOs !== value) this._hypGetOverdueSOs = value; },
    get_tblCreditHistory: function() { return this._tblCreditHistory; }, set_tblCreditHistory: function(value) { if (this._tblCreditHistory !== value) this._tblCreditHistory = value; },
    get_pnlCreditHistory: function() { return this._pnlCreditHistory; }, set_pnlCreditHistory: function(value) { if (this._pnlCreditHistory !== value) this._pnlCreditHistory = value; },
    get_pnlLoadingCreditHistory: function() { return this._pnlLoadingCreditHistory; }, set_pnlLoadingCreditHistory: function(value) { if (this._pnlLoadingCreditHistory !== value) this._pnlLoadingCreditHistory = value; },
    get_pnlCreditHistoryError: function() { return this._pnlCreditHistoryError; }, set_pnlCreditHistoryError: function(value) { if (this._pnlCreditHistoryError !== value) this._pnlCreditHistoryError = value; },
    get_pnlGetCreditHistory: function() { return this._pnlGetCreditHistory; }, set_pnlGetCreditHistory: function(value) { if (this._pnlGetCreditHistory !== value) this._pnlGetCreditHistory = value; },
    get_hypGetCreditHistory: function() { return this._hypGetCreditHistory; }, set_hypGetCreditHistory: function(value) { if (this._hypGetCreditHistory !== value) this._hypGetCreditHistory = value; },
// [002] Start Here
    get_tblInsuranceHistory: function() { return this._tblInsuranceHistory; }, set_tblInsuranceHistory: function(value) { if (this._tblInsuranceHistory !== value) this._tblInsuranceHistory = value; },
    get_pnlInsuranceHistory: function() { return this._pnlInsuranceHistory; }, set_pnlInsuranceHistory: function(value) { if (this._pnlInsuranceHistory !== value) this._pnlInsuranceHistory = value; },
    get_pnlLoadingInsuranceHistory: function() { return this._pnlLoadingInsuranceHistory; }, set_pnlLoadingInsuranceHistory: function(value) { if (this._pnlLoadingInsuranceHistory !== value) this._pnlLoadingInsuranceHistory = value; },
    get_pnlInsuranceHistoryError: function() { return this._pnlInsuranceHistoryError; }, set_pnlInsuranceHistoryError: function(value) { if (this._pnlInsuranceHistoryError !== value) this._pnlInsuranceHistoryError = value; },
    get_pnlGetInsuranceHistory: function() { return this._pnlGetInsuranceHistory; }, set_pnlGetInsuranceHistory: function(value) { if (this._pnlGetInsuranceHistory !== value) this._pnlGetInsuranceHistory = value; },
    get_hypGetInsuranceHistory: function() { return this._hypGetInsuranceHistory; }, set_hypGetInsuranceHistory: function(value) { if (this._hypGetInsuranceHistory !== value) this._hypGetInsuranceHistory = value; },
// [002] End Here
   get_Status: function() { return this._Status; }, set_Status: function(value) { if (this._Status !== value) this._Status = value; },

    initialize: function() {
        Rebound.GlobalTrader.Site.Controls.Nuggets.CompanySalesInfo.callBaseMethod(this, "initialize");

        //data
        this._strPathToData = "controls/Nuggets/CompanySalesInfo";
        this._strDataObject = "CompanySalesInfo";

        //events
        this.addRefreshEvent(Function.createDelegate(this, this.getData));

        //ellipses clicks
        this._fldYearToDate = this.getEllipsesControl("ctlYearToDateNew");
        this._fldYearToDate.addSetupData(Function.createDelegate(this, this.getYearToDate));
        this._fldLastYear = this.getEllipsesControl("ctlLastYearNew");
        this._fldLastYear.addSetupData(Function.createDelegate(this, this.getLastYear));

        //form
        if (this._ibtnEdit) {
            $R_IBTN.addClick(this._ibtnEdit, Function.createDelegate(this, this.showEditForm));
            this._frmEdit = $find(this._aryFormIDs[0]);
            this._frmEdit.addCancel(Function.createDelegate(this, this.cancelEdit));
            this._frmEdit.addSaveComplete(Function.createDelegate(this, this.saveEditComplete));
            this._frmEdit.addSaveError(Function.createDelegate(this, this.saveEditError));
        }

        //SO table get data
        $addHandler(this._hypGetOpenSOs, "click", Function.createDelegate(this, this.getOpenSOs));
        $addHandler(this._hypGetOverdueSOs, "click", Function.createDelegate(this, this.getOverdueSOs));
        $addHandler(this._hypGetCreditHistory, "click", Function.createDelegate(this, this.getCreditHistory));
          $addHandler(this._hypGetInsuranceHistory, "click", Function.createDelegate(this, this.getInsuranceHistory));
    },

    dispose: function() {
        if (this.isDisposed) return;
        if (this._hypGetOpenSOs) $clearHandlers(this._hypGetOpenSOs);
        if (this._hypGetOverdueSOs) $clearHandlers(this._hypGetOverdueSOs);
        if (this._hypGetCreditHistory) $clearHandlers(this._hypGetCreditHistory);
        if (this._ibtnEdit) $R_IBTN.clearHandlers(this._ibtnEdit);
        if (this._frmEdit) this._frmEdit.dispose();
        if (this._tblOpenSOs) this._tblOpenSOs.dispose();
        if (this._tblOverdueSOs) this._tblOverdueSOs.dispose();
        if (this._tblCreditHistory) this._tblCreditHistory.dispose();
        this._intCompanyID = null;
        this._ibtnEdit = null;
        this._ibtnAdd = null;
        this._tblOpenSOs = null;
        this._pnlOpenSOs = null;
        this._pnlGetOpenSOs = null;
        this._hypGetOpenSOs = null;
        this._pnlLoadingOpenSOs = null;
        this._pnlOpenSOsError = null;
        this._tblOverdueSOs = null;
        this._pnlOverdueSOs = null;
        this._pnlLoadingOverdueSOs = null;
        this._pnlOverdueSOsError = null;
        this._pnlGetOverdueSOs = null;
        this._hypGetOverdueSOs = null;
        this._tblCreditHistory = null;
        this._pnlCreditHistory = null;
        this._pnlLoadingCreditHistory = null;
        this._pnlCreditHistoryError = null;
        this._pnlGetCreditHistory = null;
        this._hypGetCreditHistory = null;
        
        this._tblInsuranceHistory = null;
        this._pnlInsuranceHistory = null;
        this._pnlLoadingInsuranceHistory = null;
        this._pnlInsuranceHistoryError = null;
        this._pnlGetInsuranceHistory = null;
        this._hypGetInsuranceHistory = null;
        
        Rebound.GlobalTrader.Site.Controls.Nuggets.CompanySalesInfo.callBaseMethod(this, "dispose");
    },

    getData: function() {

        this.getData_Start();
        this.showOpenSOsGetData(true);
        this.showOpenSOsError(false);
        this.showOverdueSOsGetData(true);
        this.showOverdueSOsError(false);
        this.showCreditHistoryGetData(true);
        this.showCreditHistoryError(false);
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData(this._strPathToData);
        obj.set_DataObject(this._strDataObject);
        obj.set_DataAction("GetData");
        obj.addParameter("id", this._intCompanyID);
        obj.addDataOK(Function.createDelegate(this, this.getDataOK));
        obj.addError(Function.createDelegate(this, this.getDataError));
        obj.addTimeout(Function.createDelegate(this, this.getDataError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        this.getOpenSOs();
        this.getOverdueSOs();
        this.getCreditHistory();
        this.getInsuranceHistory();
        obj = null;
    },

    getDataOK: function (args) {       
        var res = args._result;
        if (!res) {
            return;
        }
        this.setFieldValue("ctlSalesperson", res.Salesman);
        this.setFieldValue("hidSalespersonNo", res.SalesmanNo);
        this.setFieldValue("ctlIsApproved", $R_FN.getApprovedByAndDate(res.IsApproved, res.ApprovedByAndDate));
        this.setFieldValue("hidIsApproved", res.IsApproved);
        this.setFieldValue("ctlCurrency", res.Currency);
        this.setFieldValue("hidCurrencyNo", res.CurrencyNo);
        this.setFieldValue("hidCurrencyCode", res.CurrencyCode);
        this.setFieldValue("ctlCustomerNo", res.CustomerNo);
        this.setFieldValue("ctlTerms", res.Terms);
        this.setFieldValue("hidTermsNo", res.TermsNo);
        //ESMS #14
        //this.setFieldValue("ctlTax", res.Tax);
        this.setFieldValue("hidTaxNo", res.TaxNo);
        this.setFieldValue("ctlRating", res.Rating);
        this.setFieldValue("ctlIsOnStop", res.OnStop);
        this.setFieldValue("ctlIsShippingWaived", res.IsShippingWaived);
        this.setFieldValue("ctlShipVia", res.ShipVia);
        this.setFieldValue("hidShipViaNo", res.ShipViaNo);
        this.setFieldValue("ctlShippingAccountNo", res.ShippingAccountNo);
        this.setFieldValue("hidContactNo", res.ContactNo);
        this.setFieldValue("ctlContact", $RGT_nubButton_Contact(res.ContactNo, res.ContactName));
        this.setFieldValue("ctlCreditLimit", res.CreditLimit);
        this.setFieldValue("hidCreditLimit", res.CreditLimitRaw);
        this.setFieldValue("ctlYearToDate", res.YearToDate);
        this.setFieldValue("ctlLastYear", res.LastYear);
        this.setFieldValue("ctlBalance", res.Balance);
        this.setFieldValue("ctlCurrent", res.Current);
        this.setFieldValue("ctlDays30", res.Days30);
        this.setFieldValue("ctlDays60", res.Days60);
        this.setFieldValue("ctlDays90", res.Days90);
        this.setFieldValue("ctlDays120", res.Days120);
        this.setFieldValue("ctlYearToDate", res.ThisYearValue);
        this.setFieldValue("ctlLastYear", res.LastYearValue);
        this.setFieldValue("ctlBalanceWithOpenOrders", res.BalanceWithOpenSalesOrders);
        this.setFieldValue("ctlInvoiceNotExport", res.InvoiceNotExport);
        this.setFieldValue("ctlInsuranceFileNo", res.InsuranceFileNo);
        this.setFieldValue("ctlInsuredAmount", res.InsuredAmount);
        this.setFieldValue("hidInsuredAmount", res.InsuredAmountRaw);
        this.setFieldValue("ctlStopStatus", res.StopStatus);

        this.setFieldValue("ctlNotesToInvoice", $R_FN.setCleanTextValue(res.NotesToInvoice));
        this.setFieldValue("ctlCreditLimit2", res.ActualCreditLimit);
        this.setFieldValue("hidActCreditLimit", res.ActualCreditLimitRaw);
        this.setFieldValue("ctlDays1", res.Days1);
        this.setFieldValue("ctlPreferredWarehouse", res.WarehouseName);
        this._warehouseNo = res.WarehouseNo;
        this.setFieldValue("hidInsuredAmountCurrencyNo", res.InsuredAmountCurrencyNo);

        if (this._ibtnAdd) $R_IBTN.enableButton(this._ibtnAdd, !res.Inactive);
        if (this._ibtnEdit) $R_IBTN.enableButton(this._ibtnEdit, !res.Inactive);

        this.setDLUP(res.DLUP);
        this.getDataOK_End();
    },

    getDataError: function(args) {
        this.showError(true, args.get_ErrorMessage());
    },

    getOpenSOs: function() {
        this.showLoadingOpenSOs(true);
        this.showOpenSOsError(false);
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData(this._strPathToData);
        obj.set_DataObject(this._strDataObject);
        obj.set_DataAction("GetOpenSOs");
        obj.addParameter("id", this._intCompanyID);
        obj.addDataOK(Function.createDelegate(this, this.getOpenSOsOK));
        obj.addError(Function.createDelegate(this, this.getOpenSOsError));
        obj.addTimeout(Function.createDelegate(this, this.getOpenSOsError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    getOpenSOsOK: function(args) {
        res = args._result;
        if (!res) {
            return;
        }
        this.showLoadingOpenSOs(false);
        this.showOpenSOsError(false);
        this._tblOpenSOs.clearTable();
        this.processSOList(this._tblOpenSOs);
        this._tblOpenSOs.resizeColumns();
    },

    getOpenSOsError: function(args) {
        this.showLoadingOpenSOs(false);
        this.showOpenSOsError(true, args.get_ErrorMessage());
    },

    showLoadingOpenSOs: function(blnShow) {
        $R_FN.showElement(this._pnlLoadingOpenSOs, blnShow);
        $R_FN.showElement(this._pnlOpenSOs, !blnShow);
        this.showLoading(blnShow);
        if (blnShow) $R_FN.showElement(this._pnlGetOpenSOs, false);
    },

    showOpenSOsError: function(blnShow, strMessage) {
        $R_FN.showElement(this._pnlOpenSOsError, blnShow);
        if (blnShow) {
            this.showLoading(false);
            $R_FN.showElement(this._pnlOpenSOs, false);
            $R_FN.showElement(this._pnlGetOpenSOs, false);
            $R_FN.setInnerHTML(this._pnlOpenSOsError, strMessage);
        }
    },

    showOpenSOsGetData: function(blnShow) {
        $R_FN.showElement(this._pnlGetOpenSOs, blnShow);
        if (blnShow) {
            this.showLoading(false);
            $R_FN.showElement(this._pnlLoadingOpenSOs, false);
            $R_FN.showElement(this._pnlOpenSOs, false);
            $R_FN.setInnerHTML(this._pnlOpenSOsError, false);
        }
    },

    processSOList: function(tbl) {
        if (res.Items) {
            for (var i = 0; i < res.Items.length; i++) {
                var row = res.Items[i];
                var aryData = [
					$RGT_nubButton_SalesOrder(row.ID, row.No),
					row.Date,
					row.Amount
				];
                tbl.addRow(aryData, row.ID, false);
                row = null; aryData = null;
            }
        }
    },

    getOverdueSOs: function() {
        this.showLoadingOverdueSOs(true);
        this.showOverdueSOsError(false);
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData(this._strPathToData);
        obj.set_DataObject(this._strDataObject);
        obj.set_DataAction("GetOverdueSOs");
        obj.addParameter("id", this._intCompanyID);
        obj.addDataOK(Function.createDelegate(this, this.getOverdueSOsOK));
        obj.addError(Function.createDelegate(this, this.getOverdueSOsError));
        obj.addTimeout(Function.createDelegate(this, this.getOverdueSOsError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    getOverdueSOsOK: function(args) {
        res = args._result;
        if (!res) {
            return;
        }
        this.showLoadingOverdueSOs(false);
        this.showOverdueSOsError(false);
        this._tblOverdueSOs.clearTable();
        this.processSOList(this._tblOverdueSOs);
        this._tblOverdueSOs.resizeColumns();
    },

    getOverdueSOsError: function(args) {
        this.showLoadingOverdueSOs(false);
        this.showOverdueSOsError(true, args.get_ErrorMessage());
    },

    showLoadingOverdueSOs: function(blnShow) {
        $R_FN.showElement(this._pnlLoadingOverdueSOs, blnShow);
        $R_FN.showElement(this._pnlOverdueSOs, !blnShow);
        this.showLoading(blnShow);
        if (blnShow) $R_FN.showElement(this._pnlGetOverdueSOs, false);
    },

    showOverdueSOsError: function(blnShow, strMessage) {
        $R_FN.showElement(this._pnlOverdueSOsError, blnShow);
        if (blnShow) {
            this.showLoading(false);
            $R_FN.showElement(this._pnlOverdueSOs, false);
            $R_FN.showElement(this._pnlGetOverdueSOs, false);
            $R_FN.setInnerHTML(this._pnlOverdueSOsError, strMessage);
        }
    },

    showOverdueSOsGetData: function(blnShow) {
        $R_FN.showElement(this._pnlGetOverdueSOs, blnShow);
        if (blnShow) {
            this.showLoading(false);
            $R_FN.showElement(this._pnlLoadingOverdueSOs, false);
            $R_FN.showElement(this._pnlOverdueSOs, false);
            $R_FN.setInnerHTML(this._pnlOverdueSOsError, false);
        }
    },

    getCreditHistory: function() {
        this.showLoadingCreditHistory(true);
        this.showCreditHistoryError(false);
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData(this._strPathToData);
        obj.set_DataObject(this._strDataObject);
        obj.set_DataAction("GetCreditHistory");
        obj.addParameter("id", this._intCompanyID);
        obj.addDataOK(Function.createDelegate(this, this.getCreditHistoryOK));
        obj.addError(Function.createDelegate(this, this.getCreditHistoryError));
        obj.addTimeout(Function.createDelegate(this, this.getCreditHistoryError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    getCreditHistoryOK: function(args) {
        res = args._result;
        if (!res) {
            return;
        }
        this.showLoadingCreditHistory(false);
        this.showCreditHistoryError(false);
        this._tblCreditHistory.clearTable();
        this.processCreditHistory(this._tblCreditHistory);
        this._tblCreditHistory.resizeColumns();
    },

    getCreditHistoryError: function(args) {
        this.showLoadingCreditHistory(false);
        this.showCreditHistoryError(true, args.get_ErrorMessage());
    },

    showLoadingCreditHistory: function(blnShow) {
        $R_FN.showElement(this._pnlLoadingCreditHistory, blnShow);
        $R_FN.showElement(this._pnlCreditHistory, !blnShow);
        this.showLoading(blnShow);
        if (blnShow) $R_FN.showElement(this._pnlGetCreditHistory, false);
    },

    showCreditHistoryError: function(blnShow, strMessage) {
        $R_FN.showElement(this._pnlCreditHistoryError, blnShow);
        if (blnShow) {
            this.showLoading(false);
            $R_FN.showElement(this._pnlCreditHistory, false);
            $R_FN.showElement(this._pnlGetCreditHistory, false);
            $R_FN.setInnerHTML(this._pnlCreditHistoryError, strMessage);
        }
    },

    showCreditHistoryGetData: function(blnShow) {
        $R_FN.showElement(this._pnlGetCreditHistory, blnShow);
        if (blnShow) {
            this.showLoading(false);
            $R_FN.showElement(this._pnlLoadingCreditHistory, false);
            $R_FN.showElement(this._pnlCreditHistory, false);
            $R_FN.setInnerHTML(this._pnlCreditHistoryError, false);
        }
    },

    processCreditHistory: function(tbl) {
        if (res.CreditHist) {
            for (var i = 0; i < res.CreditHist.length; i++) {
                var row = res.CreditHist[i];
                var aryData = [
					row.From,
					row.To,
					row.Date + " " + row.Time,
					row.By
				];
                tbl.addRow(aryData, row.ID, false);
                row = null; aryData = null;
            }
        }
    },

 getInsuranceHistory: function() {
        this.showLoadingInsuranceHistory(true);
        this.showInsuranceHistoryError(false);
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData(this._strPathToData);
        obj.set_DataObject(this._strDataObject);
        obj.set_DataAction("GetInsuranceHistory");
        obj.addParameter("id", this._intCompanyID);
        obj.addDataOK(Function.createDelegate(this, this.getInsuranceHistoryOK));
        obj.addError(Function.createDelegate(this, this.getInsuranceHistoryError));
        obj.addTimeout(Function.createDelegate(this, this.getInsuranceHistoryError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    getInsuranceHistoryOK: function(args) {
        res = args._result;
        if (!res) {
            return;
        }
        this.showLoadingInsuranceHistory(false);
        this.showInsuranceHistoryError(false);
        this._tblInsuranceHistory.clearTable();
        this.processInsuranceHistory(this._tblInsuranceHistory);
        this._tblInsuranceHistory.resizeColumns();
    },

    getInsuranceHistoryError: function(args) {
        this.showLoadingInsuranceHistory(false);
        this.showInsuranceHistoryError(true, args.get_ErrorMessage());
    },

    showLoadingInsuranceHistory: function(blnShow) {
        $R_FN.showElement(this._pnlLoadingInsuranceHistory, blnShow);
        $R_FN.showElement(this._pnlInsuranceHistory, !blnShow);
        this.showLoading(blnShow);
        if (blnShow) $R_FN.showElement(this._pnlGetInsuranceHistory, false);
    },

    showInsuranceHistoryError: function(blnShow, strMessage) {
        $R_FN.showElement(this._pnlInsuranceHistoryError, blnShow);
        if (blnShow) {
            this.showLoading(false);
            $R_FN.showElement(this._pnlInsuranceHistory, false);
            $R_FN.showElement(this._pnlGetInsuranceHistory, false);
            $R_FN.setInnerHTML(this._pnlInsuranceHistoryError, strMessage);
        }
    },

    showInsuranceHistoryGetData: function(blnShow) {
        $R_FN.showElement(this._pnlGetInsuranceHistory, blnShow);
        if (blnShow) {
            this.showLoading(false);
            $R_FN.showElement(this._pnlLoadingInsuranceHistory, false);
            $R_FN.showElement(this._pnlInsuranceHistory, false);
            $R_FN.setInnerHTML(this._pnlInsuranceHistoryError, false);
        }
    },

    processInsuranceHistory: function(tbl) {
        if (res.InsuranceHist) {
            for (var i = 0; i < res.InsuranceHist.length; i++) {
                var row = res.InsuranceHist[i];
                var aryData = [
					row.From,
					row.To,
					row.Date + " " + row.Time,
					row.By
				];
                tbl.addRow(aryData, row.ID, false);
                row = null; aryData = null;
            }
        }
    },
    showEditForm: function () {
        //[003] start
        this._frmEdit._globalLoginClientNo = this._globalLoginClientNo;
        //[003] end
        //[001] Code Start
        $R_FN.showElement(this._ibtnEdit, false);
        //[001] Code End
        var intCMID = this._intCompanyID;
        this._frmEdit.getFieldComponent("ctlContact")._intCompanyID = intCMID;
        this._frmEdit.setFieldValue("ctlSalesperson", this.getFieldValue("hidSalespersonNo"));
        this._frmEdit.setFieldValue("ctlIsApproved", Boolean.parse(this.getFieldValue("hidIsApproved")));
        this._frmEdit.setFieldValue("ctlCurrency", this.getFieldValue("hidCurrencyNo"));
        this._frmEdit.setFieldValue("ctlCustomerNo", this.getFieldValue("ctlCustomerNo"));
        this._frmEdit.setFieldValue("ctlTerms", this.getFieldValue("hidTermsNo"));
        //   this._frmEdit.setFieldValue("ctlTax", this.getFieldValue("hidTaxNo"));
        this._frmEdit.setFieldValue("ctlRating", this.getFieldValue("ctlRating"));
        this._frmEdit.setFieldValue("ctlIsOnStop", this.getFieldValue("ctlIsOnStop"));
        this._frmEdit.setFieldValue("ctlIsShippingWaived", this.getFieldValue("ctlIsShippingWaived"));
        this._frmEdit.setFieldValue("ctlShipVia", this.getFieldValue("hidShipViaNo"));
        this._frmEdit.setFieldValue("ctlShippingAccountNo", this.getFieldValue("ctlShippingAccountNo"));
        this._frmEdit.setFieldValue("ctlContact", this.getFieldValue("hidContactNo"));
        this._frmEdit.setFieldValue("ctlCreditLimit", this.getFieldValue("hidCreditLimit"));
        this._frmEdit.setCurrencyLabel(this.getFieldValue("hidCurrencyCode"));
        
          this._frmEdit.setFieldValue("ctlInsuranceFileNo", this.getFieldValue("ctlInsuranceFileNo"));
          this._frmEdit.setFieldValue("ctlInsuredAmount", this.getFieldValue("hidInsuredAmount"));
            this._frmEdit.setFieldValue("ctlStopStatus", this.getFieldValue("ctlStopStatus"));
          this._frmEdit.showField("ctlStopStatus",this._Status);
        this.showForm(this._frmEdit, true); 
        this._frmEdit.style = 'left:10%;top:10%';
        this._frmEdit.setFieldValue("ctlNotesToInvoice", this.getFieldValue("ctlNotesToInvoice"));
        this._frmEdit.setFieldValue("ctlActualCreditLimit", this.getFieldValue("hidActCreditLimit"));
        //[001] Code Start
        $R_FN.showElement(this._ibtnEdit, true);
        //[001] Code End
       
        this._frmEdit.setFieldValue("ctlPreferredWarehouse", this._warehouseNo);
        this._frmEdit.setFieldValue("ctlInsuredAmountCurrency", this.getFieldValue("hidInsuredAmountCurrencyNo"));
        
        
    },

    cancelEdit: function() {
        this.showContent(true);
        this.showForm(this._frmEdit, false);
    },

    saveEditComplete: function() {
        this.showForm(this._frmEdit, false);
        this.getData();
        this.showSavedOK(true, $R_RES.ChangesSavedSuccessfully);
        this.onSaveEditComplete();
    },

    saveEditError: function() {
        this.showForm(this._frmEdit, false);
        this.showError(true, this._frmEdit._strErrorMessage);
    },
    getYearToDate: function () {
        var obj = this._fldYearToDate._objData;
        obj.set_PathToData(this._strPathToData);
        obj.set_DataObject(this._strDataObject);
        obj.set_DataAction("GetYearToDateNew");
        obj.addParameter("id", this._intCompanyID);
    },

    getLastYear: function () {
        var obj = this._fldLastYear._objData;
        obj.set_PathToData(this._strPathToData);
        obj.set_DataObject(this._strDataObject);
        obj.set_DataAction("GetLastYearNew");
        obj.addParameter("id", this._intCompanyID);
    }

};

Rebound.GlobalTrader.Site.Controls.Nuggets.CompanySalesInfo.registerClass("Rebound.GlobalTrader.Site.Controls.Nuggets.CompanySalesInfo", Rebound.GlobalTrader.Site.Controls.Nuggets.Base);
