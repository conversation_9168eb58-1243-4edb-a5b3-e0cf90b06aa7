Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");Rebound.GlobalTrader.Site.Controls.DropDowns.GILineQueryProgressStatus=function(n){Rebound.GlobalTrader.Site.Controls.DropDowns.GILineQueryProgressStatus.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.DropDowns.GILineQueryProgressStatus.prototype={get_IsPartialGIQueryStatus:function(){return this._IsPartialGIQueryStatus},set_IsPartialGIQueryStatus:function(n){this._IsPartialGIQueryStatus!==n&&(this._IsPartialGIQueryStatus=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.DropDowns.GILineQueryProgressStatus.callBaseMethod(this,"initialize");this.addSetupDataCall(Function.createDelegate(this,this.setupDataCall));this.addGetDataOK(Function.createDelegate(this,this.dataCallOK))},dispose:function(){this.isDisposed||(this._IsPartialGIQueryStatus=null,Rebound.GlobalTrader.Site.Controls.DropDowns.GILineQueryProgressStatus.callBaseMethod(this,"dispose"))},setupDataCall:function(){this._objData.set_PathToData("controls/DropDowns/GILineQueryProgressStatus");this._objData.set_DataObject("GILineQueryProgressStatus");this._objData.set_DataAction("GetData")},dataCallOK:function(){var n=this._objData._result,t;if(n!=null&&n.Types)for(t=0;t<n.Types.length;t++)this.addOption(n.Types[t].Name,n.Types[t].ID)}};Rebound.GlobalTrader.Site.Controls.DropDowns.GILineQueryProgressStatus.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.GILineQueryProgressStatus",Rebound.GlobalTrader.Site.Controls.DropDowns.Base);