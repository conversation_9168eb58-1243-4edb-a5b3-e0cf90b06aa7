﻿
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

/*  
========================================================================================================================================   
TASK           UPDATED BY      DATE            ACTION        DESCRIPTION  
[US-246240]    Phuc Hoang      15-May-2025     CREATE        Invoice - Line from 'Service' source Function on Client/ DMCC side (Part 2)
========================================================================================================================================  
*/ 

CREATE OR ALTER PROCEDURE [dbo].[usp_itemsearch_AuthorisedSalesOrderLine] (                        
    @ClientId int 
  , @OrderBy int = 1 
  , @SortDir int = 1 
  , @PageIndex int = 0 
  , @PageSize int = 10 
  , @PartSearch nvarchar(50) = NULL 
  , @CMSearch nvarchar(50) = NULL 
  , @SalesmanSearch int = NULL 
  , @CustomerPOSearch nvarchar(50) = NULL 
  , @IncludeClosed bit = NULL 
  , @SalesOrderNo NVARCHAR(500) = NULL          
  , @DateOrderedFrom datetime = NULL 
  , @DateOrderedTo datetime = NULL 
  , @DatePromisedFrom datetime = NULL 
  , @DatePromisedTo datetime = NULL 
  , @InvoiceNo INT = NULL 
)               
WITH RECOMPILE               
AS                  
	IF OBJECT_ID(N'tempdb..#tempClosedSalesOrders') IS NOT NULL    
	BEGIN    
		DROP TABLE #tempClosedSalesOrders    
	END    
       
    DECLARE @StartPage int , @EndPage int;               
    SET @StartPage = (@PageIndex * @PageSize + 1)                
    SET @EndPage = ((@PageIndex + 1) * @PageSize)                
                
    IF (NOT @DateOrderedFrom IS NULL)                 
        SET @DateOrderedFrom = dbo.ufn_get_start_of_day_for_date(@DateOrderedFrom)                
                        
    IF (NOT @DateOrderedTo IS NULL)                 
        SET @DateOrderedTo = dbo.ufn_get_end_of_day_for_date(@DateOrderedTo)                
                
    IF (NOT @DatePromisedFrom IS NULL)                 
        SET @DatePromisedFrom = dbo.ufn_get_start_of_day_for_date(@DatePromisedFrom)                
                        
    IF (NOT @DatePromisedTo IS NULL)                 
        SET @DatePromisedTo = dbo.ufn_get_end_of_day_for_date(@DatePromisedTo)   
		
	IF (ISNULL(@SalesOrderNo, '') = '' OR ISNULL(@SalesOrderNo, '') = '0') 
	BEGIN
		SELECT @SalesOrderNo = coalesce(@SalesOrderNo + ',', '') +  convert(varchar(12),so.SalesOrderId)
		FROM [dbo].[tbInvoice] inv
			LEFT JOIN [dbo].[tbInvoiceLine] inl ON inl.InvoiceNo = inv.InvoiceId
			LEFT JOIN [dbo].[tbSalesOrderLine] sol ON sol.SalesOrderLineId = inl.SalesOrderLineNo
			LEFT JOIN [dbo].[tbSalesOrder] so ON so.SalesOrderId = sol.SalesOrderNo
			LEFT JOIN [dbo].[tbSalesOrderLine] solService ON so.SalesOrderId = solService.SalesOrderNo 
		WHERE inv.InvoiceId = @InvoiceNo AND inv.ClientNo = @ClientId		
			AND ISNULL(so.AuthorisedBy, 0) > 0 AND so.DateAuthorised IS NOT NULL 
			AND ISNULL(solService.ServiceNo, 0) > 0;

		IF @SalesOrderNo IS NULL 
			SET @SalesOrderNo = ''
	END
    
	SELECT DISTINCT sol.SalesOrderLineId 
        , so.SalesOrderNumber 
        , sol.Quantity  
        , so.CompanyNo 
        , c.CompanyName 
        , dbo.ufn_get_date_from_datetime(so.DateOrdered) AS DateOrdered   
        , so.ContactNo      
        , isnull(cn.ContactName, '') AS ContactName             
        , ISNULL(lg.EmployeeName, '') AS SalesmanName               
        , so.CustomerPO 
        , sol.Price 
        , cu.CurrencyCode 
        , sol.Part 
        , sol.ROHS 
        , sol.FullPart 
		, sol.ServiceNo
		, sv.ServiceName
		, ISNULL(sol.ServiceCostRef, sv.Cost) AS Cost                		
		INTO #tempClosedSalesOrders 
        FROM dbo.tbSalesOrder so
			LEFT JOIN [dbo].[tbSalesOrderLine] sol ON so.SalesOrderId = sol.SalesOrderNo 
			LEFT JOIN dbo.tbCompany c ON so.CompanyNo = c.CompanyId 
			LEFT JOIN dbo.tbContact cn ON so.ContactNo = cn.ContactId 
			LEFT JOIN dbo.tbLogin lg ON lg.LoginId = so.Salesman 
			LEFT JOIN dbo.tbCurrency cu ON so.CurrencyNo = cu.CurrencyId 
			LEFT JOIN dbo.tbService sv ON sol.ServiceNo = sv.ServiceId 

        WHERE so.ClientNo = @ClientId  
			AND (ISNULL(@IncludeClosed, 0) = 0 OR ISNULL(so.Closed, 0) IN (0, 1))
			AND ISNULL(so.AuthorisedBy, 0) > 0 AND so.DateAuthorised IS NOT NULL 
            AND so.SalesOrderId IN (SELECT String FROM dbo.[ufn_splitString](@SalesOrderNo,','))
			AND ISNULL(sol.ServiceNo, 0) > 0
			AND sol.SalesOrderLineId NOT IN (SELECT SalesOrderLineNo FROM tbInvoiceLine WHERE InvoiceNo = @InvoiceNo AND ISNULL(Inactive, 0) = 0 AND ISNULL(SalesOrderLineNo, 0) != 0)
            AND ((@DateOrderedFrom IS NULL) 
                    OR (NOT @DateOrderedFrom IS NULL 
                        AND so.DateOrdered >= @DateOrderedFrom)) 
            AND ((@DateOrderedTo IS NULL) 
                    OR (NOT @DateOrderedTo IS NULL 
                        AND so.DateOrdered <= @DateOrderedTo)) 
            AND ((@DatePromisedFrom IS NULL)  
                    OR (NOT @DatePromisedFrom IS NULL 
                        AND sol.DatePromised >= @DatePromisedFrom)) 
            AND ((@DatePromisedTo IS NULL) 
                    OR (NOT @DatePromisedTo IS NULL  
					AND sol.DatePromised <= @DatePromisedTo)) 
            AND (@CMSearch IS NULL 
                    OR (NOT @CMSearch IS NULL 
                        AND c.FullName LIKE @CMSearch)) 
            AND ((@SalesmanSearch IS NULL)                
                    OR (NOT @SalesmanSearch IS NULL  
                        AND (so.Salesman = @SalesmanSearch 
                            OR (so.Salesman2 = @SalesmanSearch  
								AND so.Salesman2Percent > 0))))  
            AND (@CustomerPOSearch IS NULL 
                    OR (NOT @CustomerPOSearch IS NULL 
                        AND ISNULL(so.CustomerPO, '') LIKE @CustomerPOSearch)) 
            AND (@PartSearch IS NULL 
                    OR (NOT @PartSearch IS NULL 
                        AND (sol.FullPart LIKE @PartSearch 
                OR sol.FullCustomerPart LIKE @PartSearch))) 
	; WITH cteSearch                
		AS (SELECT * ,              
            ROW_NUMBER() OVER (ORDER BY                
				case WHEN @OrderBy = 1 AND @SortDir = 2 THEN sol.SalesOrderNumber END DESC                 
                , case WHEN @OrderBy = 1 THEN sol.SalesOrderNumber END                
                , case WHEN @OrderBy = 2 AND @SortDir = 2 THEN sol.CompanyName END DESC                
                , case WHEN @OrderBy = 2 THEN sol.CompanyName END                
                , case WHEN @OrderBy = 3 AND @SortDir = 2 THEN sol.FullPart END DESC                
                , case WHEN @OrderBy = 3 THEN sol.FullPart END                
                , case WHEN @OrderBy = 3 AND @SortDir = 2 THEN sol.Part END DESC                
                , case WHEN @OrderBy = 3 THEN sol.Part END                
                , case WHEN @OrderBy = 4 AND @SortDir = 2 THEN DateOrdered END DESC                
                , case WHEN @OrderBy = 4 THEN DateOrdered END                
                , case WHEN @OrderBy = 5 AND @SortDir = 2 THEN sol.Price END DESC                
                , case WHEN @OrderBy = 5 THEN sol.Price END         
				, case WHEN @OrderBy = 6 AND @SortDir = 2 THEN ISNULL(SalesmanName, '') END DESC                
                , case WHEN @OrderBy = 6 THEN ISNULL(SalesmanName, '') END                
                , case WHEN @OrderBy = 7 AND @SortDir = 2 THEN CustomerPO END DESC                
                , case WHEN @OrderBy = 7 THEN CustomerPO END
				) AS RowNum  
             FROM  #tempClosedSalesOrders sol           
             ) 
        SELECT  * , (SELECT count(*) FROM cteSearch) AS RowCnt 
        FROM cteSearch                
        WHERE RowNum BETWEEN @StartPage AND @EndPage And SalesOrderLineId != 0;
                      
	DROP TABLE #tempClosedSalesOrders;  

GO


