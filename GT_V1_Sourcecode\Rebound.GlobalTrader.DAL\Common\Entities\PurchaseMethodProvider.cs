﻿//Marker     Changed by         Date         Remarks
//[001]      <PERSON><PERSON><PERSON><PERSON>     10/09/2021    Added class for purchase method.
using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;
using System.Data.Common;

namespace Rebound.GlobalTrader.DAL { 
    public abstract class PurchaseMethodProvider : DataAccess
    {
        static private PurchaseMethodProvider _instance = null;
        /// <summary>
        /// Returns an instance of the provider type specified in the config file
        /// </summary>       
        static public PurchaseMethodProvider Instance
        {
            get
            {
                if (_instance == null) _instance = (PurchaseMethodProvider)Activator.CreateInstance(Type.GetType(Globals.Settings.PurchaseMethods.ProviderType));
                return _instance;
            }
        }
        public PurchaseMethodProvider()
        {
            this.ConnectionString = Globals.Settings.RohsStatuss.ConnectionString;
        }

        #region Method Registrations
        
        /// <summary>
        /// DropDown
        /// Calls [usp_dropdown_ROHSStatus]
        /// </summary>
        public abstract List<PurchaseMethodDetails> DropDown();





        #endregion



    }
}
