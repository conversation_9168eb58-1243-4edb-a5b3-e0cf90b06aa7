<%--
Marker     Changed by      Date         Remarks
[001]      Vinay           29/05/2012   This need to implement Incoterms field is requird.
--%>
<%@ Control Language="C#" CodeBehind="CreditMainInfo_Edit.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Forms.CreditMainInfo_Edit" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>

<ReboundUI_Form:DesignBase ID="ctlDB" runat="server" ShowRequiredFieldsExplanation="true" ShowQuickHelp="false">
	<Links>
		<ReboundUI:IconButton ID="ibtnSave" runat="server" IconGroup="Nugget" IconTitleResource="Save" IconCSSType="Save" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
		<ReboundUI:IconButton ID="ibtnCancel" runat="server" IconGroup="Nugget" IconTitleResource="Cancel" IconCSSType="Cancel" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
	</Links>

	<Explanation><%=Functions.GetGlobalResource("FormExplanations", "CreditMainInfo_Edit")%></Explanation>

	<Content>
		<ReboundUI_Table:Form id="frm" runat="server">
		
			<ReboundUI_Form:FormField id="ctlCustomer" runat="server" FieldID="lblCustomer" ResourceTitle="Customer">
				<Field><asp:Label ID="lblCustomer" runat="server" /></Field>
			</ReboundUI_Form:FormField>
		
			<ReboundUI_Form:FormField id="ctlContact" runat="server" FieldID="lblContact" ResourceTitle="Contact">
				<Field><asp:Label ID="lblContact" runat="server" /></Field>
			</ReboundUI_Form:FormField>
		
			<ReboundUI_Form:FormField id="ctlDivision" runat="server" FieldID="ddlDivision" ResourceTitle="Division" IsRequiredField="true">
				<Field><ReboundDropDown:Division ID="ddlDivision" runat="server" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlDivisionHeader" runat="server" FieldID="ddlDivisionHeader" ResourceTitle="DivisionHeader" IsRequiredField="true">
				<Field><ReboundDropDown:Division ID="ddlDivisionHeader" runat="server" /></Field>
			</ReboundUI_Form:FormField>
		
			<ReboundUI_Form:FormField id="ctlSalesman" runat="server" FieldID="ddlSalesman" ResourceTitle="Salesperson" IsRequiredField="true">
				<Field><ReboundDropDown:Employee ID="ddlSalesman" runat="server" /></Field>
			</ReboundUI_Form:FormField>
		
			<ReboundUI_Form:FormField id="ctlSalesman2" runat="server" FieldID="ddlSalesman2" ResourceTitle="Salesperson2">
				<Field><ReboundDropDown:Employee ID="ddlSalesman2" runat="server" /></Field>
			</ReboundUI_Form:FormField>

           <ReboundUI_Form:FormField id="ctlSalesman2Percent" runat="server" FieldID="txtSalesman2Percent" ResourceTitle="Salesman2Percent">
                <Field><ReboundUI:ReboundTextBox ID="txtSalesman2Percent" runat="server" Width="50" TextBoxMode="Numeric" /> %</Field>
            </ReboundUI_Form:FormField>
            
			<ReboundUI_Form:FormField id="ctlRaisedBy" runat="server" FieldID="ddlRaisedBy" ResourceTitle="RaisedBy" >
				<Field><ReboundDropDown:Employee ID="ddlRaisedBy" runat="server" /></Field>
			</ReboundUI_Form:FormField>
			
			<ReboundUI_Form:FormField id="ctlRaisedByLbl" runat="server" FieldID="lblRaisedBy" ResourceTitle="RaisedBy" >
				<Field><asp:Label ID="lblRaisedBy" runat="server" /></Field>
			</ReboundUI_Form:FormField>

            <ReboundUI_Form:FormField id="ctlCreditDate" runat="server" FieldID="txtCreditDate" ResourceTitle="CreditDate" IsRequiredField="true">
	            <Field>
		            <ReboundUI:ReboundTextBox ID="txtCreditDate" runat="server" Width="150" />
		            <ReboundUI:Calendar ID="calCreditDate" runat="server" RelatedTextBoxID="txtCreditDate" />
	            </Field>
            </ReboundUI_Form:FormField>

            <ReboundUI_Form:FormField id="ctlReferenceDate" runat="server" FieldID="txtReferenceDate" ResourceTitle="ReferenceDate" IsRequiredField="true">
	            <Field>
		            <ReboundUI:ReboundTextBox ID="txtReferenceDate" runat="server" Width="150" />
		            <ReboundUI:Calendar ID="calReferenceDate" runat="server" RelatedTextBoxID="txtReferenceDate" />
	            </Field>
            </ReboundUI_Form:FormField>
		
			<ReboundUI_Form:FormField id="ctlInvoice" runat="server" FieldID="lblInvoice" ResourceTitle="InvoiceNo">
				<Field><asp:Label ID="lblInvoice" runat="server" /></Field>
			</ReboundUI_Form:FormField>
		
			<ReboundUI_Form:FormField id="ctlSalesOrder" runat="server" FieldID="lblSalesOrder" ResourceTitle="SalesOrderNo">
				<Field><asp:Label ID="lblSalesOrder" runat="server" /></Field>
			</ReboundUI_Form:FormField>
		
			<ReboundUI_Form:FormField id="ctlCustomerRMA" runat="server" FieldID="lblCustomerRMA" ResourceTitle="CustomerRMANo">
				<Field><asp:Label ID="lblCustomerRMA" runat="server" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlCustomerPO" runat="server" FieldID="txtCustomerPO" ResourceTitle="CustomerPO">
				<Field><ReboundUI:ReboundTextBox ID="txtCustomerPO" runat="server" Width="200" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlCustomerReturn" runat="server" FieldID="txtCustomerReturn" ResourceTitle="CustomerReturn">
				<Field><ReboundUI:ReboundTextBox ID="txtCustomerReturn" runat="server" Width="200" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlCustomerDebit" runat="server" FieldID="txtCustomerDebit" ResourceTitle="CustomerDebit">
				<Field><ReboundUI:ReboundTextBox ID="txtCustomerDebit" runat="server" Width="200" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlTax" runat="server" FieldID="ddlTax" ResourceTitle="Tax" IsRequiredField="true">
				<Field><ReboundDropDown:Tax ID="ddlTax" runat="server" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlCurrency" runat="server" FieldID="ddlCurrency" ResourceTitle="Currency" IsRequiredField="true">
				<Field><ReboundDropDown:SellCurrency ID="ddlCurrency" runat="server" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlShipVia" runat="server" FieldID="ddlShipVia" ResourceTitle="ShipVia">
				<Field><ReboundDropDown:SellShipMethod ID="ddlShipVia" runat="server" /></Field>
			</ReboundUI_Form:FormField>
			
			<ReboundUI_Form:FormField id="ctlShipViaLbl" runat="server" FieldID="lblShipVia" ResourceTitle="ShipVia" >
				<Field><asp:Label ID="lblShipVia" runat="server" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlShippingAccount" runat="server" FieldID="txtShippingAccount" ResourceTitle="ShippingAccountNo">
				<Field><ReboundUI:ReboundTextBox ID="txtShippingAccount" runat="server" Width="200" /></Field>
			</ReboundUI_Form:FormField>

		  <%--[001] code start--%>
			<ReboundUI_Form:FormField id="ctlIncoterm" runat="server" FieldID="ddlIncoterm" ResourceTitle="Incoterm" IsRequiredField="true">
				<Field><ReboundDropDown:Incoterm ID="ddlIncoterm" runat="server" /></Field>
			</ReboundUI_Form:FormField>
			<%--[001] code end--%>
			
			<ReboundUI_Form:FormField id="ctlShippingCost" runat="server" FieldID="txtShippingCost" ResourceTitle="ShippingCost" IsRequiredField="true">
				<Field><ReboundUI:ReboundTextBox ID="txtShippingCost" runat="server" Width="100" TextBoxMode="currency" FormatDecimalPlaces="true" /> <%=Rebound.GlobalTrader.Site.SessionManager.ClientCurrencyCode%></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlFreight" runat="server" FieldID="txtFreight" ResourceTitle="Freight" IsRequiredField="true">
				<Field><ReboundUI:ReboundTextBox ID="txtFreight" runat="server" Width="100" TextBoxMode="currency" FormatDecimalPlaces="true" /> <asp:Label id="lblFreight_Currency" runat="server" /></Field>
			</ReboundUI_Form:FormField>
		<%--[002] code start--%>
			<ReboundUI_Form:FormField id="ctlCreditNoteBankFee" runat="server" FieldID="txtCreditNoteBankFee" ResourceTitle="BankFee">
				<Field><ReboundUI:ReboundTextBox ID="txtCreditNoteBankFee" runat="server" TextBoxMode="currency" FormatDecimalPlaces="true" Width="100" /></Field>
			</ReboundUI_Form:FormField>
	        <%--[002] code end--%>
			<ReboundUI_Form:FormField id="ctlInstructions" runat="server" FieldID="txtInstructions" ResourceTitle="Instructions">
				<Field><ReboundUI:ReboundTextBox ID="txtInstructions" runat="server" Width="400" Rows="4" TextMode="multiLine" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlNotes" runat="server" FieldID="txtNotes" ResourceTitle="CustomerNotes">
				<Field><ReboundUI:ReboundTextBox ID="txtNotes" runat="server" Width="400" Rows="4" TextMode="multiLine" /></Field>
			</ReboundUI_Form:FormField>

        	<ReboundUI_Form:FormField id="ctlExchangeRate" runat="server" FieldID="txtExchangeRate" ResourceTitle="ExchangeRate">
                <Field><ReboundUI:ReboundTextBox ID="txtExchangeRate" runat="server" TextBoxMode="currency" DecimalPlaces="5" Width="100" /></Field>
			</ReboundUI_Form:FormField>
			
		
		</ReboundUI_Table:Form>

	</Content>
	
</ReboundUI_Form:DesignBase>
