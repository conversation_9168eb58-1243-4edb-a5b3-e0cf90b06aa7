﻿    
/*       
===========================================================================================      
TASK   UPDATED BY   DATE   ACTION      DESCRIPTION     
[US-232285]    CuongDox   18-Apr-2025  CREATE   PO - Add ability to modify ECCN  
===========================================================================================      
*/ 
CREATE OR ALTER PROCEDURE GetEMailForGroupMembersOGELECCN       
    @SecurityGroupNo INT      
AS      
BEGIN      
declare    
@groupno int =0    
SELECT top 1 @groupno= MailGroupId FROM tbMailGroup WHERE Name LIKE '%ECCN Approvals%'and ClientNO=@SecurityGroupNo;    
    
print @groupno    
    SELECT mgm.LoginNo, lg.EMail      
    FROM dbo.tbMailGroupMember mgm      
    LEFT JOIN tbLogin lg ON mgm.LoginNo = lg.LoginId      
    WHERE mgm.MailGroupNo = @groupno       
END  