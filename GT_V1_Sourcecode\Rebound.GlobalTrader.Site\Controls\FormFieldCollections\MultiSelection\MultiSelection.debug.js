///<reference name="MicrosoftAjax.js" />
///<reference path="~/Common/Data/Data.js" />
///<reference path="~/Common/Functions/Functions.js" />
///<reference path="~/Controls/IconButton/IconButton.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - dispose everything fully
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.FormFieldCollections");

Rebound.GlobalTrader.Site.Controls.FormFieldCollections.MultiSelection = function(element) { 
	Rebound.GlobalTrader.Site.Controls.FormFieldCollections.MultiSelection.initializeBase(this, [element]);
	this._arySelected = [];
	this._aryUnselected = [];
};

Rebound.GlobalTrader.Site.Controls.FormFieldCollections.MultiSelection.prototype = {

	get_ibtnSelect: function() { return this._ibtnSelect; }, 	set_ibtnSelect: function(v) { if (this._ibtnSelect !== v)  this._ibtnSelect = v; }, 
	get_ibtnDeselect: function() { return this._ibtnDeselect; }, 	set_ibtnDeselect: function(v) { if (this._ibtnDeselect !== v)  this._ibtnDeselect = v; }, 
	get_tblSelected: function() { return this._tblSelected; }, 	set_tblSelected: function(v) { if (this._tblSelected !== v)  this._tblSelected = v; }, 
	get_tblUnselected: function() { return this._tblUnselected; }, 	set_tblUnselected: function(v) { if (this._tblUnselected !== v)  this._tblUnselected = v; }, 
	
	addItemSelected: function(handler) { this.get_events().addHandler("ItemSelected", handler); },
	removeItemSelected: function(handler) { this.get_events().removeHandler("ItemSelected", handler); },
	onItemSelected: function() { 
		var handler = this.get_events().getHandler("ItemSelected");
		if (handler) handler(this, Sys.EventArgs.Empty); 
	},

	addItemDeselected: function(handler) { this.get_events().addHandler("ItemDeselected", handler); },
	removeItemDeselected: function(handler) { this.get_events().removeHandler("ItemDeselected", handler); },
	onItemDeselected: function() { 
		var handler = this.get_events().getHandler("ItemDeselected");
		if (handler) handler(this, Sys.EventArgs.Empty); 
	},

	initialize: function() {
		//buttons
		$R_IBTN.addClick(this._ibtnSelect, Function.createDelegate(this, this.doSelect));
		$R_IBTN.addClick(this._ibtnDeselect, Function.createDelegate(this, this.doDeselect));
		this._tblSelected.addMultipleSelectionChanged(Function.createDelegate(this, this.updateButtons));
		this._tblUnselected.addMultipleSelectionChanged(Function.createDelegate(this, this.updateButtons));
		Rebound.GlobalTrader.Site.Controls.FormFieldCollections.MultiSelection.callBaseMethod(this, "initialize");
	},

	dispose: function() { 
		if (this.isDisposed) return;
		if (this.get_element()) $clearHandlers(this.get_element());
		if (this._tblSelected) this._tblSelected.dispose();
		if (this._tblUnselected) this._tblUnselected.dispose();
		this._ibtnSelect = null;
		this._ibtnDeselect = null;
		this._tblSelected = null;
		this._tblUnselected = null;
		this._arySelected = null;
		this._aryUnselected = null;
		Rebound.GlobalTrader.Site.Controls.FormFieldCollections.MultiSelection.callBaseMethod(this, "dispose");
	},
	
	addRow: function(blnSelected, aryText, varValue) {
		if (blnSelected) {
			Array.add(this._arySelected, {Text:aryText, Value:varValue});
		} else {
			Array.add(this._aryUnselected, {Text:aryText, Value:varValue});
		}
	},
	
	clearData: function() {
		Array.clear(this._arySelected);
		Array.clear(this._aryUnselected);
	},
	
	populateTables: function() {
		$R_IBTN.enableButton(this._ibtnDeselect, false);
		$R_IBTN.enableButton(this._ibtnSelect, false);
		this._tblSelected.clearTable();
		for (var i = 0, l = this._arySelected.length; i < l; i++) {
			this._tblSelected.addRow(this._arySelected[i].Text, this._arySelected[i].Value, false);
		}
		this._tblUnselected.clearTable();
		for (i = 0, l = this._aryUnselected.length; i < l; i++) {
			this._tblUnselected.addRow(this._aryUnselected[i].Text, this._aryUnselected[i].Value, false);
		}
	},
	
	doSelect: function() {
		var bln = false;
		for (var i = this._aryUnselected.length - 1; i >= 0; i--) {
			if (Array.contains(this._tblUnselected._aryCurrentValues, this._aryUnselected[i].Value)) {
				bln = true;
				Array.add(this._arySelected, this._aryUnselected[i]);
				Array.removeAt(this._aryUnselected, i);
			}
		}
		if (bln) this.populateTables();
		this.onItemSelected();
	},
	
	doDeselect: function() {
		var bln = false;
		for (var i = this._arySelected.length - 1; i >= 0; i--) {
			if (Array.contains(this._tblSelected._aryCurrentValues, this._arySelected[i].Value)) {
				bln = true;
				Array.add(this._aryUnselected, this._arySelected[i]);
				Array.removeAt(this._arySelected, i);
			}
		}
		if (bln) this.populateTables();
		this.onItemDeselected();
	},
	
	updateButtons: function() {
		$R_IBTN.enableButton(this._ibtnDeselect, this._tblSelected._aryCurrentValues.length > 0);
		$R_IBTN.enableButton(this._ibtnSelect, this._tblUnselected._aryCurrentValues.length > 0);
	},
	
	getValues: function(bln) {
		var ary = [];
		if (bln) {
			for (var i = 0, l = this._arySelected.length; i < l; i++) {
				Array.add(ary, this._arySelected[i].Value);
			}
		} else {
			for (i = 0, l = this._aryUnselected.length; i < l; i++) {
				Array.add(ary, this._aryUnselected[i].Value);
			}
		}
		return ary;
	},

	getValuesAsString: function(bln) {
		return $R_FN.arrayToSingleString(this.getValues(bln));
	}
	
};

Rebound.GlobalTrader.Site.Controls.FormFieldCollections.MultiSelection.registerClass("Rebound.GlobalTrader.Site.Controls.FormFieldCollections.MultiSelection", Rebound.GlobalTrader.Site.Controls.Forms.LabelFormField, Sys.IDisposable);