﻿/* 
===========================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-205174]		An.TranTan			28-Jun-2024		Update			Add 1 more column to table, correct CONSTRAINTS after added
===========================================================================================
*/

ALTER TABLE [BorisGlobalTraderImports].[dbo].[tbPriceQuoteImport_tempData] DROP CONSTRAINT [DF_tbPriceQuoteImport_tempData_ImportDate]
GO

ALTER TABLE [BorisGlobalTraderImports].[dbo].[tbPriceQuoteImport_tempData] DROP CONSTRAINT [DF_tbPriceQuoteImport_tempData_ROHS]
GO

ALTER TABLE [BorisGlobalTraderImports].[dbo].[tbPriceQuoteImport_tempData] DROP CONSTRAINT [DF_tbPriceQuoteImport_tempData__SupplierCost]
GO

ALTER TABLE [BorisGlobalTraderImports].[dbo].[tbPriceQuoteImport_tempData] DROP CONSTRAINT [DF_tbPriceQuoteImport_tempData_Quantity]
GO

DROP TABLE [BorisGlobalTraderImports].[dbo].[tbPriceQuoteImport_tempData]
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [BorisGlobalTraderImports].[dbo].[tbPriceQuoteImport_tempData](
	[PriceQuoteImportId] [int] IDENTITY(1,1) NOT NULL,
	[Column1] [nvarchar](max) NULL,
	[Column2] [nvarchar](max) NULL,
	[Column3] [nvarchar](max) NULL,
	[Column4] [nvarchar](max) NULL,
	[Column5] [nvarchar](max) NULL,
	[Column6] [nvarchar](max) NULL,
	[Column7] [nvarchar](max) NULL,
	[Column8] [nvarchar](max) NULL,
	[Column9] [nvarchar](max) NULL,
	[Column10] [nvarchar](max) NULL,
	[Column11] [nvarchar](max) NULL,
	[Column12] [nvarchar](max) NULL,
	[Column13] [nvarchar](max) NULL,
	[Column14] [nvarchar](max) NULL,
	[Column15] [nvarchar](max) NULL,
	[Column16] [nvarchar](max) NULL,
	[Column17] [nvarchar](max) NULL,
	[Column18] [nvarchar](max) NULL,
	[Column19] [nvarchar](max) NULL,
	[Column20] [nvarchar](max) NULL,
	[Column21] [nvarchar](max) NULL,
	[Column22] [nvarchar](max) NULL,
	[Column23] [nvarchar](max) NULL,
	[Column24] [nvarchar](max) NULL,
	[Column25] [nvarchar](max) NULL,
	[OriginalFilename] [nvarchar](100) NULL,
	[GeneratedFilename] [nvarchar](100) NULL,
	[ClientId] [int] NULL,
	[SelectedClientId] [int] NULL,
	[CreatedBy] [int] NULL,
	[ImportDate] [datetime] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

ALTER TABLE [BorisGlobalTraderImports].[dbo].[tbPriceQuoteImport_tempData] ADD  CONSTRAINT [DF_tbPriceQuoteImport_tempData_Quantity]  DEFAULT ('0') FOR [Column10]
GO

ALTER TABLE [BorisGlobalTraderImports].[dbo].[tbPriceQuoteImport_tempData] ADD  CONSTRAINT [DF_tbPriceQuoteImport_tempData__SupplierCost]  DEFAULT ('0') FOR [Column5]
GO

ALTER TABLE [BorisGlobalTraderImports].[dbo].[tbPriceQuoteImport_tempData] ADD  CONSTRAINT [DF_tbPriceQuoteImport_tempData_ROHS]  DEFAULT ('N') FOR [Column6]
GO

ALTER TABLE [BorisGlobalTraderImports].[dbo].[tbPriceQuoteImport_tempData] ADD  CONSTRAINT [DF_tbPriceQuoteImport_tempData_ImportDate]  DEFAULT (getdate()) FOR [ImportDate]
GO


