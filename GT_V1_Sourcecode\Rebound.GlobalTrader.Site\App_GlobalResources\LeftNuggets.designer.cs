//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Resources {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option or rebuild the Visual Studio project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.VisualStudio.Web.Application.StronglyTypedResourceProxyBuilder", "16.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class LeftNuggets {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal LeftNuggets() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Resources.LeftNuggets", global::System.Reflection.Assembly.Load("App_GlobalResources"));
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Company List.
        /// </summary>
        internal static string CompanyList {
            get {
                return ResourceManager.GetString("CompanyList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to QuickBrowse.
        /// </summary>
        internal static string QuickBrowse {
            get {
                return ResourceManager.GetString("QuickBrowse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to QuickJump.
        /// </summary>
        internal static string QuickJump {
            get {
                return ResourceManager.GetString("QuickJump", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Recently Viewed.
        /// </summary>
        internal static string RecentlyViewed {
            get {
                return ResourceManager.GetString("RecentlyViewed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Search.
        /// </summary>
        internal static string Search {
            get {
                return ResourceManager.GetString("Search", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Selection.
        /// </summary>
        internal static string Selection {
            get {
                return ResourceManager.GetString("Selection", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sourcing Links.
        /// </summary>
        internal static string SourcingLinks {
            get {
                return ResourceManager.GetString("SourcingLinks", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to View Options.
        /// </summary>
        internal static string ViewOptions {
            get {
                return ResourceManager.GetString("ViewOptions", resourceCulture);
            }
        }
    }
}
