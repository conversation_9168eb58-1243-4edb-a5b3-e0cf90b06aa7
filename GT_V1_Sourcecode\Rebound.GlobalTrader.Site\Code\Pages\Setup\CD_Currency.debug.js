///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Pages.Setup");

Rebound.GlobalTrader.Site.Pages.Setup.CD_Currency = function(el) { 
	Rebound.GlobalTrader.Site.Pages.Setup.CD_Currency.initializeBase(this, [el]);
};

Rebound.GlobalTrader.Site.Pages.Setup.CD_Currency.prototype = {

	get_ctlCurrency: function() { return this._ctlCurrency; }, 	set_ctlCurrency: function(v) { if (this._ctlCurrency !== v)  this._ctlCurrency = v; }, 
	get_ctlCurrencyRates: function() { return this._ctlCurrencyRates; }, 	set_ctlCurrencyRates: function(v) { if (this._ctlCurrencyRates !== v)  this._ctlCurrencyRates = v; }, 

	initialize: function() {
		Rebound.GlobalTrader.Site.Pages.Setup.CD_Currency.callBaseMethod(this, "initialize");
	},
	
	goInit: function() {
		if (this._ctlCurrency) this._ctlCurrency.addSelectCurrency(Function.createDelegate(this, this.ctlCurrency_SelectCurrency));
		if (this._ctlCurrencyRates) this._ctlCurrencyRates.addChangedData(Function.createDelegate(this, this.ctlCurrencyRates_ChangedData));
		Rebound.GlobalTrader.Site.Pages.Setup.CD_Currency.callBaseMethod(this, "goInit");
	},

	dispose: function() { 
		if (this.isDisposed) return;
		if (this._ctlCurrency) this._ctlCurrency.dispose();
		if (this._ctlCurrencyRates) this._ctlCurrencyRates.dispose();
		this._ctlCurrency = null;
		this._ctlCurrencyRates = null;
		Rebound.GlobalTrader.Site.Pages.Setup.CD_Currency.callBaseMethod(this, "dispose");
	},
	
	ctlCurrency_SelectCurrency: function() {
		this._ctlCurrencyRates._intCurrencyID = this._ctlCurrency._intCurrencyID;
		this._ctlCurrency._tbl.resizeColumns();
		this._ctlCurrencyRates._strCurrency = this._ctlCurrency._strCurrency;
		this._ctlCurrencyRates.show(!this._ctlCurrency._blnCurrencyIsBase);
		this._ctlCurrencyRates.refresh();
	},
	
	ctlCurrencyRates_ChangedData: function() {
		this._ctlCurrency.getData();
	}
	
};

Rebound.GlobalTrader.Site.Pages.Setup.CD_Currency.registerClass("Rebound.GlobalTrader.Site.Pages.Setup.CD_Currency", Rebound.GlobalTrader.Site.Pages.Content, Sys.IDisposable);
