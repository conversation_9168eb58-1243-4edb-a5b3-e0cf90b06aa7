Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.EditEpoBulk=function(n){Rebound.GlobalTrader.Site.Controls.Forms.EditEpoBulk.initializeBase(this,[n]);this._ID="";this._availableID="";this._offerAddFlag=!1};Rebound.GlobalTrader.Site.Controls.Forms.EditEpoBulk.prototype={initialize:function(){Rebound.GlobalTrader.Site.Controls.Forms.EditEpoBulk.callBaseMethod(this,"initialize");this.addShown(Function.createDelegate(this,this.formShown))},formShown:function(){this._blnFirstTimeShown&&this.addSave(Function.createDelegate(this,this.saveClicked));this.checkWarningStock();$("input[type=radio][name=BulkEditOptions]").change(function(){$(".remove-warning").css("display",this.value=="Remove"&&$("#offerAddFlag").val()=="true"?"":"none")})},dispose:function(){this.isDisposed||(this._ID=null,this._availableID=null,this._offerAddFlag=null,Rebound.GlobalTrader.Site.Controls.Forms.EditEpoBulk.callBaseMethod(this,"dispose"))},saveClicked:function(){var t=$("[name='BulkEditOptions'][type='radio']:checked").val(),n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("Controls/Nuggets/POHubSourcing");n.set_DataObject("POHubSourcing");n.set_DataAction("EditEpoBulk");n.addParameter("EpoIDs",t=="Remove"?this._availableID:this._ID);n.addParameter("Option",t);n.addDataOK(Function.createDelegate(this,this.saveEditComplete));n.addError(Function.createDelegate(this,this.saveEditError));n.addTimeout(Function.createDelegate(this,this.saveEditError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},saveEditError:function(n){this._strErrorMessage=n._errorMessage;this.onSaveError()},saveEditComplete:function(n){n._result.Result==!0?this.onSaveComplete():(this._strErrorMessage=n._errorMessage,this.onSaveError())},checkWarningStock:function(){$("#offerAddFlag").val(this._offerAddFlag);var n=$("[name='BulkEditOptions'][type='radio']:checked").val();$(".remove-warning").css("display",n=="Remove"&&this._offerAddFlag==!0?"":"none")}};Rebound.GlobalTrader.Site.Controls.Forms.EditEpoBulk.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.EditEpoBulk",Rebound.GlobalTrader.Site.Controls.Forms.Base,Sys.IDisposable);