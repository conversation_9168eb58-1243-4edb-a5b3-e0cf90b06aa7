Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.CusReqSourcingResults_Delete=function(n){Rebound.GlobalTrader.Site.Controls.Forms.CusReqSourcingResults_Delete.initializeBase(this,[n]);this._intSourcingResultID=-1;this._aryCurrentValues=""};Rebound.GlobalTrader.Site.Controls.Forms.CusReqSourcingResults_Delete.prototype={initialize:function(){Rebound.GlobalTrader.Site.Controls.Forms.CusReqSourcingResults_Delete.callBaseMethod(this,"initialize");this.addShown(Function.createDelegate(this,this.formShown))},dispose:function(){this.isDisposed||(this._ctlConfirm&&this._ctlConfirm.dispose(),this._ctlConfirm=null,Rebound.GlobalTrader.Site.Controls.Forms.CusReqSourcingResults_Delete.callBaseMethod(this,"dispose"))},formShown:function(){this._blnFirstTimeShown&&(this._ctlConfirm=this.getFieldComponent("ctlConfirm"),this._ctlConfirm.addClickYesEvent(Function.createDelegate(this,this.yesClicked)),this._ctlConfirm.addClickNoEvent(Function.createDelegate(this,this.noClicked)))},yesClicked:function(){this.showSaving(!0);var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/CusReqSourcingResults");n.set_DataObject("CusReqSourcingResults");n.set_DataAction("DeletePartWatchMatch");n.addParameter("SourcingResultIDs",this._aryCurrentValues);n.addDataOK(Function.createDelegate(this,this.saveComplete));n.addError(Function.createDelegate(this,this.saveError));n.addTimeout(Function.createDelegate(this,this.saveError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null;this.onSave()},noClicked:function(){this.showSaving(!1);this.onNotConfirmed()},saveError:function(n){this.showSaving(!1);this._strErrorMessage=n._errorMessage;this.onSaveError()},saveComplete:function(n){n._result.Result?this.onSaveComplete():(this._strErrorMessage=n._errorMessage,this.onSaveError())}};Rebound.GlobalTrader.Site.Controls.Forms.CusReqSourcingResults_Delete.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.CusReqSourcingResults_Delete",Rebound.GlobalTrader.Site.Controls.Forms.Base,Sys.IDisposable);