<%@ Control Language="C#" CodeBehind="SourcingLinks.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.LeftNuggets.SourcingLinks" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_LeftNugget:DesignBase ID="ctlDB" runat="server" Visible="true" Title="Sourcing Links">
	<Content>
		<ul id="ulLinks" runat="server" />
		<asp:Panel ID="pnlNoneFound" runat="server" CssClass="noneFound"><%=Functions.GetGlobalResource("NotFound", "SourcingLinks") %></asp:Panel>
	</Content>
</ReboundUI_LeftNugget:DesignBase>
