Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DataListNuggets");Rebound.GlobalTrader.Site.Controls.DataListNuggets.BOMLines=function(n){Rebound.GlobalTrader.Site.Controls.DataListNuggets.BOMLines.initializeBase(this,[n]);this._frmConfirm=null;this._intSalesPersonId=null};Rebound.GlobalTrader.Site.Controls.DataListNuggets.BOMLines.prototype={get_blnPOHub:function(){return this._blnPOHub},set_blnPOHub:function(n){this._blnPOHub!==n&&(this._blnPOHub=n)},get_ibtnExportCSV:function(){return this._ibtnExportCSV},set_ibtnExportCSV:function(n){this._ibtnExportCSV!==n&&(this._ibtnExportCSV=n)},get_sortIndex:function(){return this._sortIndex},set_sortIndex:function(n){this._sortIndex!==n&&(this._sortIndex=n)},get_sortDir:function(){return this._sortDir},set_sortDir:function(n){this._sortDir!==n&&(this._sortDir=n)},get_pageIndex:function(){return this._pageIndex},set_pageIndex:function(n){this._pageIndex!==n&&(this._pageIndex=n)},get_pageSize:function(){return this._pageSize},set_pageSize:function(n){this._pageSize!==n&&(this._pageSize=n)},get_code:function(){return this._code},set_code:function(n){this._code!==n&&(this._code=n)},get_name:function(){return this._name},set_name:function(n){this._name!==n&&(this._name=n)},get_intSalesPersonId:function(){return this._intSalesPersonId},set_intSalesPersonId:function(n){this._intSalesPersonId!==n&&(this._intSalesPersonId=n)},get_IsGSA:function(){return this._IsGSA},set_IsGSA:function(n){this._IsGSA!==n&&(this._IsGSA=n)},initialize:function(){this.addSetupDataCallEvent(Function.createDelegate(this,this.setupDataCall));this.addGetDataOKEvent(Function.createDelegate(this,this.getDataOK));this.addInitCompleteEvent(Function.createDelegate(this,this.initAfterBaseIsReady));this._strPathToData="controls/DataListNuggets/BOMLines";this._strDataObject="BOMLines";Rebound.GlobalTrader.Site.Controls.DataListNuggets.BOMLines.callBaseMethod(this,"initialize");this._ibtnExportCSV&&$R_IBTN.addClick(this._ibtnExportCSV,Function.createDelegate(this,this.exportCSV));$find(this.getFilterField("ctlClient").get_id())._element.setAttribute("onchange",String.format('$find("{0}").onClientChange()',this._element.id));$("#ctl00_cphMain_ctlHUBRFQResults_ctlDB_ctl16_ctlFilter_ctlAS6081Required_ddl_ddl").css("width","144px")},initAfterBaseIsReady:function(){this.addPageTabChangedEvent(Function.createDelegate(this,this.pageTabChanged));this.getData()},dispose:function(){this.isDisposed||(this._blnPOHub=null,this._ibtnExportCSV&&$R_IBTN.clearHandlers(this._ibtnExportCSV),this._ibtnExportCSV=null,this._IsGSA=null,Rebound.GlobalTrader.Site.Controls.DataListNuggets.BOMLines.callBaseMethod(this,"dispose"))},pageTabChanged:function(){this._table._intCurrentPage=1;this._enmViewLevel=this._intCurrentTab;this.getData()},setupDataCall:function(){this._objData.addParameter("SelectedRadio",this.getRadioButtonValue());this._objData.addParameter("ViewLevel",this._enmViewLevel)},getRadioButtonValue:function(){for(var i="",u=document.getElementById("ctl00_cphMain_ctlHUBRFQResults_ctlDB_ctl16_ctlFilter_ctl11_ctl02_radHeaderDetail"),t=u.getElementsByTagName("input"),r,n=0;n<t.length;n++)if(t[n].checked){r=t[n];i=r.value;break}return i},getDataOK:function(){var e=this._objResult.isSearchFromRequirements,t,i,f,n,u,r;if(this._sortIndex=this._objResult.SortIndex,this._sortDir=this._objResult.SortDir,this._pageIndex=this._objResult.PageIndex,this._pageSize=this._objResult.PageSize,this._code=this._objResult.Code,this._name=this._objResult.Name,this._intSalesPersonId=this._objResult.SalesPerson,this._intcompanytypeid=this._objResult.ctlcomType,!e)for(t="",i=0,f=this._objResult.Results.length;i<f;i++)n=this._objResult.Results[i],u=$R_FN.replaceBRTags(n.ExpediteNotes),n.RequiredDateStatus=="Green"?t="green":n.RequiredDateStatus=="Yellow"?t="#FFBF00":n.RequiredDateStatus=="Red"?t="Red":n.RequiredDateStatus=="White"&&(t="White"),r=[$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(String.format("<span style='text-decoration:none;' title=\""+$R_FN.setCleanTextValue(u)+'">'+$RGT_nubButton_BOM(n.ID,n.Name)+"<\/span>")),""),$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(n.AssignedUser),""),$R_FN.writeDoubleCellValue("",""),$R_FN.writeTriCellValue($R_FN.setCleanTextValue(n.Code),$R_FN.setCleanTextValue(n.Salesman),$R_FN.setCleanTextValue("")),$R_FN.writeDoubleCellValue(n.blnMakeYellow==!0?'<span style="background-color:yellow;">'+$RGT_nubButton_Company(n.CompanyNo,n.Company+" ("+n.CompanyType+")")+"<\/span>":$RGT_nubButton_Company(n.CompanyNo,n.Company+" ("+n.CompanyType+")"),$RGT_nubButton_Contact(n.ContactNo,n.Contact)),$R_FN.writeDoubleCellValue(n.BOMStatus,$R_FN.setCleanTextValue(n.DivName)),$R_FN.writeDoubleCellValue(n.Date,""),$R_FN.writeDoubleCellValue(n.RequiredDate,"<span style='background-color:"+t+"!important;float: right;margin-top: -17px;height: 20px;width: 20px;'><\/span>",""),n.TotalValue],this._table.addRow(r,n.ID,!1),r=null,n=null;if(e)for(t="",i=0,f=this._objResult.Results.length;i<f;i++)n=this._objResult.Results[i],u=$R_FN.replaceBRTags(n.ExpediteNotes),n.RequiredDateStatus=="Green"?t="green":n.RequiredDateStatus=="Yellow"?t="#FFBF00":n.RequiredDateStatus=="Red"?t="Red":n.RequiredDateStatus=="White"&&(t="White"),r=[$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(String.format("<span style='text-decoration:none;' title=\""+$R_FN.setCleanTextValue(u)+'">'+$RGT_nubButton_BOM(n.BOMNo,n.BOMName)+"<\/span>")),n.isPoHUB==!0?n.No:$R_FN.setCleanTextValue(String.format("<span style='text-decoration:none;' title=\""+$R_FN.setCleanTextValue(u)+'">'+$RGT_nubButton_CustomerRequirement(n.ID,n.No))+"<\/span>")),$R_FN.writeDoubleCellValue(n.AssignedTo,n.Quantity),$R_FN.writeDoubleCellValue($R_FN.writePartNo(n.Part,n.ROHS),$RGT_nubButton_Manufacturer(n.MfrNo,n.Mfr)),$R_FN.writeTriCellValue($R_FN.setCleanTextValue(n.BOMCode),$R_FN.setCleanTextValue(n.Salesman),$R_FN.setCleanTextValue($R_FN.showYellowText(n.SupportTeamMember))),$R_FN.writeDoubleCellValue(n.blnMakeYellow==!0?'<span style="background-color:yellow;">'+$RGT_nubButton_Company(n.CMNo,n.CM)+"<\/span>":$RGT_nubButton_Company(n.CMNo,n.CM),$RGT_nubButton_Contact(n.ContactNo,n.Contact)),$R_FN.writeDoubleCellValue(n.BOMStatus,$R_FN.setCleanTextValue(n.DivName)),$R_FN.writeDoubleCellValue(n.Received,n.Promised),$R_FN.writeDoubleCellValue(n.RequiredDate,"<span style='background-color:"+t+"!important;float: right;margin-top: -17px;height: 20px;width: 20px;'><\/span>",""),n.TotalValue],this._table.addRow(r,n.ID,!1),r=null,n=null},updateFilterVisibility:function(){this.getFilterField("ctlDivision")._ddl._intSelClientNo=9999;this.getFilterField("ctlDivision")._ddl.getData();this.getFilterField("ctlClient").show(this._blnPOHub||this._IsGSA);this.getFilterField("ctlDivision").show(this._blnPOHub)},getDivision:function(){this.getFilterField("ctlClient").getValue()==null||this.getFilterField("ctlClient").getValue()==-1?this.getFilterField("ctlDivision")._ddl._intSelClientNo!=0:this.getFilterField("ctlDivision")._ddl._intSelClientNo=this.getFilterField("ctlClient").getValue();this.getFilterField("ctlDivision")._ddl.getData()},exportCSV:function(){this.getData_Start();var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/DataListNuggets/BOMLines");n.set_DataObject("BOMLines");n.addParameter("ViewLevel",this._enmViewLevel);n.set_DataAction("ExportToCSV");n._intTimeoutMilliseconds=5e5;n.addParameter("SortIndex",this._sortIndex);n.addParameter("SortDir",this._sortDir);n.addParameter("PageIndex",this._pageIndex);n.addParameter("PageSize",this._pageSize);n.addParameter("Code",this._code);n.addParameter("Name",this._name);n.addParameter("SalesPerson",this._intSalesPersonId);n.addParameter("SelectedRadio",this.getRadioButtonValue());n.addParameter("BomStatus",this.getFilterFieldValue("ctlStatus"));n.addParameter("PoHubBuyer",this.getFilterFieldValue("ctlClient"));n.addParameter("Manufacturer",this.getFilterFieldValue("ctlManufacturer"));n.addParameter("Part",this.getFilterFieldValue("ctlPartNumber"));n.addParameter("PoHubBuyer",this.getFilterFieldValue("ctlSalesperson"));n.addParameter("StartDate",this.getFilterFieldValue("ctlStartDate"));n.addParameter("EndDate",this.getFilterFieldValue("ctlEndDate"));n.addDataOK(Function.createDelegate(this,this.exportCSV_OK));n.addError(Function.createDelegate(this,this.exportCSV_Error));n.addTimeout(Function.createDelegate(this,this.exportCSV_Error));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},exportCSV_OK:function(n){var i=n._result,t;i.Filename&&(t=new Date,location.href=String.format("{0}?t={1}",i.Filename,t.getTime()),t=null);this.getDataOK_End()},exportCSV_Error:function(n){this.showError(!0,n.get_ErrorMessage())},onClientChange:function(){this.getDivision();this.getSalesPersonByClient()},getSalesPersonByClient:function(){var n=999;this.getFilterField("ctlClient").getValue()!=null&&(n=this.getFilterField("ctlClient").getValue());this.getFilterField("ctlClientSalesperson")._ddl._intGlobalLoginClientNo=n;this.getFilterField("ctlClientSalesperson")._ddl.getData()}};Rebound.GlobalTrader.Site.Controls.DataListNuggets.BOMLines.registerClass("Rebound.GlobalTrader.Site.Controls.DataListNuggets.BOMLines",Rebound.GlobalTrader.Site.Controls.DataListNuggets.Base,Sys.IDisposable);