﻿  CREATE OR ALTER PROCEDURE [dbo].[usp_update_SupplierInvoice]                        
--******************************************************************************************                            
--* Created By :- <PERSON><PERSON>ra                          
--* Created Date :- 13-06-2013                          
--* Description :- Update Supplier Invoice  
/*   
===========================================================================================  
TASK			UPDATED BY      DATE			ACTION		DESCRIPTION  
[BUG-207603]	Trung Pham		01-Jul-2024		FIX			207603: [PROD Bug] Set InvoiceAmount = Good Value + Tax + DeliveryCharge + BankFee + CreditCardFee  
[US-204678]		An.TranTan		15-Aug-2024		Update		Set InvoiceAmount = Good Value + Tax + DeliveryCharge + BankFee + DebitAmount  
[US-204678]		An.TranTan		21-Aug-2024		Update		Set InvoiceAmount = value user input  
===========================================================================================  
*/  
--******************************************************************************************                        
  @SupplierInvoiceId INT                       
 ,@SupplierInvoiceNumber nvarchar(100)                         
 ,@SupplierInvoiceDate DATETIME                          
 ,@CurrencyNo INT = NULL                          
 ,@InvoiceAmount FLOAT = NULL                          
 ,@GoodsValue FLOAT = NULL                          
 ,@Tax FLOAT = NULL                          
 ,@DeliveryCharge FLOAT = NULL                          
 ,@BankFee FLOAT = NULL                          
 ,@CreditCardFee FLOAT = NULL                       
 ,@CanBeExported BIT = NULL                          
 ,@Notes NVARCHAR(512) = NULL                  
 ,@TaxNo INT  = NULL              
 ,@TaxCode  NVARCHAR(10) = NULL              
 ,@CurrencyCode  NVARCHAR(10)                
 ,@SecondRef NVARCHAR(32) = NULL                    
 ,@Narrative NVARCHAR(82) = NULL                
 ,@UpdatedBy INT        
 ,@URNNumber BIGINT = NULL                          
 ,@StatusReason INT  = NULL     
 ,@AuthoriseNotes NVARCHAR(500) = NULL
 ,@DebitAmount FLOAT = NULL
 ,@DebitNoteRef NVARCHAR(32) = NULL
 ,@RowsAffected   int = NULL Output                        
                        
AS                        
                 
UPDATE dbo.tbSupplierInvoice            
                  
SET   SupplierInvoiceNumber   = @SupplierInvoiceNumber                       
 , SupplierInvoiceDate   = @SupplierInvoiceDate                         
 , CurrencyNo    = @CurrencyNo                         
 --, InvoiceAmount   = ISNULL(@GoodsValue,0) + ISNULL(@Tax,0) + ISNULL(@DeliveryCharge,0) + ISNULL(@BankFee,0)  
 --, InvoiceAmount   = ISNULL(@GoodsValue,0) + ISNULL(@Tax,0) + ISNULL(@DeliveryCharge,0) + ISNULL(@BankFee,0) + ISNULL(@DebitAmount,0)  
 , InvoiceAmount   = ISNULL(@InvoiceAmount,0)  
 , GoodsValue    = @GoodsValue                         
 , Tax  = @Tax                         
 , DeliveryCharge  = @DeliveryCharge                         
 , BankFee  = @BankFee                         
 , CreditCardFee   = @CreditCardFee                        
 , Notes    = @Notes                        
 , CanbeExported=@CanBeExported                     
 , UpdatedBy   = @UpdatedBy                
 , TaxNo = @TaxNo                
 , TaxCode = @TaxCode                
 , CurrencyCode=@CurrencyCode              
 , SecondRef=@SecondRef              
 , Narrative=@Narrative                      
 , DLUP    = current_timestamp           
 , URNNumber =  @URNNumber        
 , StatusReasonNo = @StatusReason
 , DebitAmount = @DebitAmount
 , DebitNoteRef = @DebitNoteRef
 WHERE SupplierInvoiceId  = @SupplierInvoiceId          
       
  --Get 'Automatically exported for supplier invoice setting' from tbsetting table      
        DECLARE @SettingValue nvarchar(5)      
        DECLARE @ClientNo INT      
              
        SELECT @ClientNo=ClientNo FROM tbSupplierInvoice WHERE SupplierInvoiceId  = @SupplierInvoiceId          
              
        SELECT TOP 1 @SettingValue=SettingValue FROM tbsetting WHERE ClientID=@ClientNo AND SettingItemID=9      
              
        IF UPPER(ISNULL(@SettingValue,''))='TRUE' AND ISNULL(@CanbeExported,0)=1      
        BEGIN      
           UPDATE tbSupplierInvoice SET Exported=1 WHERE SupplierInvoiceId  = @SupplierInvoiceId             
        END      
    --End                                
                         
SELECT @RowsAffected  = @@ROWCOUNT      
if(@AuthoriseNotes is not null)    
 begin    
 --Reset the SupplierInvoice:                            
   EXEC usp_insert_SupplierAuthorized @SupplierInvoiceId ,@AuthoriseNotes,@UpdatedBy     
   end    
      
      
  
  