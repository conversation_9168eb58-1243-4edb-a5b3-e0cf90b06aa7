//--------------------------------------------------------------------------------------------------------
// RP 10.12.2009:
// - cache data
//--------------------------------------------------------------------------------------------------------
using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;

namespace Rebound.GlobalTrader.Site.Controls.DropDowns.Data {

    [WebService(Namespace = "http://tempuri.org/")]
    [WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    public class AddressesForCompany : Rebound.GlobalTrader.Site.Data.DropDowns.Base {

        protected override void GetData() {
            string strOptions = CacheManager.SerializeOptions(new object[] { ID });
            string strCachedData = CacheManager.GetDropDownData(_objDropDown.ID, strOptions);
            if (string.IsNullOrEmpty(strCachedData)) {
                JsonObject jsn = new JsonObject();
                JsonObject jsnList = new JsonObject(true);
                List<BLL.Address> lst = BLL.Address.DropDownForCompany(ID);
                for (int i = 0; i < lst.Count; i++) {
                    JsonObject jsnItem = new JsonObject();
                    jsnItem.AddVariable("ID", lst[i].AddressId);
                    jsnItem.AddVariable("Address", Functions.ReplaceLineBreaks(AddressManager.ToLongString(lst[i])));
                    jsnList.AddVariable(jsnItem);
                    jsnItem.Dispose();
                    jsnItem = null;
                }
                lst.Clear();
                lst = null;
                jsn.AddVariable("Addresses", jsnList);
                jsnList.Dispose();
                jsnList = null;
                CacheManager.StoreDropDown(_objDropDown.ID, strOptions, jsn.Result, CacheManager.CacheExpiryType.OneWorkingDay);
                OutputResult(jsn);
                jsn.Dispose();
                jsn = null;
            } else {
                _context.Response.Write(strCachedData);
            }
        }
    }
}
