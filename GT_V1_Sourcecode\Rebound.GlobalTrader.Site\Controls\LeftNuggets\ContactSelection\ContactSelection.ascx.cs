using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site;

namespace Rebound.GlobalTrader.Site.Controls.LeftNuggets {
	public partial class ContactSelection : Selection {

		protected override void OnInit(EventArgs e) {
			SetLeftNuggetType("ContactSelection");
			base.OnInit(e);
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.LeftNuggets.Base", ctlDesignBase.ClientID);
			AddItems();
		}

		private void AddItems() {
			HtmlControl ul = new HtmlGenericControl("ul");
			string strStandardHref = string.Format("{0}?{1}=", _objSite.GetPage("Contact_CompanyBrowse").Url, QueryStringManager.GetVariableQSName(QueryStringManager.QueryStringVariables.CompanyListType));
			ul.Controls.Add(AddContactItem(DataListNuggets.CompanyListType.AllCompanies, string.Format("{0}{1}", strStandardHref, (int)DataListNuggets.CompanyListType.AllCompanies)));
			ul.Controls.Add(AddContactItem(DataListNuggets.CompanyListType.Customers, string.Format("{0}{1}", strStandardHref, (int)DataListNuggets.CompanyListType.Customers)));
			ul.Controls.Add(AddContactItem(DataListNuggets.CompanyListType.Manufacturers, _objSite.GetPage("Contact_ManufacturerBrowse").Url));
			ul.Controls.Add(AddContactItem(DataListNuggets.CompanyListType.Suppliers, string.Format("{0}{1}", strStandardHref, (int)DataListNuggets.CompanyListType.Suppliers)));
			ul.Controls.Add(AddContactItem(DataListNuggets.CompanyListType.Prospects, string.Format("{0}{1}", strStandardHref, (int)DataListNuggets.CompanyListType.Prospects)));
			ul.Controls.Add(AddContactItem(DataListNuggets.CompanyListType.Contacts, _objSite.GetPage("Contact_ContactBrowse").Url));
			_plhItems.Controls.Add(ul);
		}

		private HtmlControl AddContactItem(Controls.DataListNuggets.CompanyListType enmCompanyListType, string strHref) {
			HtmlControl li = new HtmlGenericControl("li");
			HyperLink hyp = new HyperLink();
			hyp.NavigateUrl = strHref;
			hyp.Text = Functions.GetGlobalResource("CompanyListType", enmCompanyListType.ToString());
			li.Controls.Add(hyp);
			return li;
		}
	}
}