﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;
using DocumentFormat.OpenXml;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Spreadsheet;


namespace Rebound.GlobalTrader.Site
{
    public class ExcelAdapterLarge
    {
        public static DataTable ReadExcel(string filepathtempfolder, string sheetName, string GeneratedFileName, int ClientNo, int fileId, int updatedBy)
        {
            var data = new DataTable();
            using (SpreadsheetDocument spreadsheetDocument = SpreadsheetDocument.Open(filepathtempfolder, false))
            {
                // Get the worksheet we are working with
                var sheets = spreadsheetDocument.WorkbookPart.Workbook.Descendants<Sheet>().Where(s => s.Name == sheetName);
                var worksheetPart = (WorksheetPart)spreadsheetDocument.WorkbookPart.GetPartById(sheets.First().Id);
                var worksheet = worksheetPart.Worksheet;
                var sstPart = spreadsheetDocument.WorkbookPart.GetPartsOfType<SharedStringTablePart>().First();
                var ssTable = sstPart.SharedStringTable;
                // Get the CellFormats for cells without defined data types
                var workbookStylesPart = spreadsheetDocument.WorkbookPart.GetPartsOfType<WorkbookStylesPart>().First();
                var stylesheet = workbookStylesPart.Stylesheet;
                var cellFormats = workbookStylesPart.Stylesheet.CellFormats;
                data = ExtractRowsData(worksheet, ssTable, cellFormats, GeneratedFileName, ClientNo, fileId, updatedBy);
            }
            return data;
        }

        private static DataTable ExtractRowsData(Worksheet worksheet, SharedStringTable ssTable, CellFormats cellFormats, string GeneratedFileName, int ClientNo, int fileId, int updatedBy)
        {
            int R = 0;
            var data = new DataTable();

            uint dateTimeFormatId = (uint)(164); // Start with IDs over 163, reserved for custom formats

            NumberingFormat dateTimeFormat = new NumberingFormat()
            {
                NumberFormatId = dateTimeFormatId,
                FormatCode = StringValue.FromString("MM/DD/YYYY HH:MM")
            };

            // Create a new cell format that uses the datetime numbering format

            CellFormat dateTimeCellFormat = new CellFormat()
            {
                NumberFormatId = dateTimeFormatId,
                ApplyNumberFormat = true
            };

            try
            {
                var validateData = new DataTable();
                var columnHeaders = worksheet.Descendants<Row>().First().Descendants<Cell>().Select(c => Convert.ToString(ProcessCellValue(c, ssTable, cellFormats)).Replace(" ", "")).ToArray();
                var columnHeadersCellReference = worksheet.Descendants<Row>().First().Descendants<Cell>().Select(c => c.CellReference.InnerText.Replace("1", string.Empty)).ToArray();
                var spreadsheetData = from row in worksheet.Descendants<Row>()
                                      where row.RowIndex > 1
                                      select row;
                validateData.Columns.Add("Index");
                foreach (string columnHeader in columnHeaders)
                {
                    data.Columns.Add(columnHeader);
                    validateData.Columns.Add(columnHeader);
                }
                data.Columns.Add("GeneratedFileName");
                data.Columns.Add("ClientNo");
                data.Columns.Add("HUBOfferImportLargeFileID");
                data.Columns.Add("GTMFR");
                data.Columns.Add("GTVendor");
                data.Columns.Add("UpdatedBy");
                foreach (var dataRow in spreadsheetData)
                {
                    R++;
                    //if (R > Convert.ToInt32(ConfigurationManager.AppSettings["MaxUploadRowCount"]) + 10)
                    //{
                    //    break;
                    //}
                    var newRow = data.NewRow();
                    for (int i = 0; i < columnHeaders.Length; i++)
                    {
                        // Find and add the correct cell to the row object
                        var cell = dataRow.Descendants<Cell>().Where(c => c.CellReference == columnHeadersCellReference[i] + dataRow.RowIndex).FirstOrDefault();
                        string cellValue = "";
                        if (cell != null)
                        {
                            object cellValueObj = ProcessCellValueWithColumn(cell, ssTable, cellFormats, i);
                            cellValue = cellValueObj != null ? cellValueObj.ToString().Trim() : "";
                            
                            if (columnHeaders[i].Equals("MOQ", StringComparison.InvariantCultureIgnoreCase) 
                                || columnHeaders[i].Equals("SPQ", StringComparison.InvariantCultureIgnoreCase)
                                || columnHeaders[i].Equals("COST", StringComparison.InvariantCultureIgnoreCase))
                            {
                                cellValue = Regex.Replace(cellValue, @"\s+", "").Replace(",", "");
                            }
                        }

                        if (columnHeaders[i].Equals("MOQ", StringComparison.InvariantCultureIgnoreCase)
                            || columnHeaders[i].Equals("SPQ", StringComparison.InvariantCultureIgnoreCase))
                        {
                            cellValue = !string.IsNullOrEmpty(cellValue) ? cellValue : "0";
                        }

                        newRow[columnHeaders[i]] = cellValue;
                    }
                    if (!newRow.ItemArray.All(field =>
                    {
                        string s = null;
                        if (field != null)
                            s = field.ToString();
                        return string.IsNullOrEmpty(s);
                    }))
                    {
                        newRow["GeneratedFileName"] = GeneratedFileName;
                        newRow["ClientNo"] = ClientNo;
                        newRow["HUBOfferImportLargeFileID"] = fileId;
                        newRow["GTMFR"] = "";
                        newRow["GTVendor"] = "";
                        newRow["UpdatedBy"] = updatedBy;
                        data.Rows.Add(newRow);
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine(string.Format("Eror Extracting data row:{0} message:{1}", R.ToString(), ex.Message));
                throw ex;
            }
            return data;
        }

        // Process the valus of a cell and return a .NET value
        private static Func<Cell, SharedStringTable, CellFormats, int, Object> ProcessCellValueWithColumn =
            (c, ssTable, cellFormats, columnIndex) =>
            {
                if (c.CellValue == null) return null;
                // If there is no data type, this must be a string that has been formatted as a number

                if (c.DataType == null)
                {
                    if (c.StyleIndex == null) return c.CellValue.Text.Trim();
                    var cf =
                        cellFormats.Descendants<CellFormat>()
                                   .ElementAt<CellFormat>(Convert.ToInt32(c.StyleIndex.Value));
                    if (cf.NumberFormatId >= 0 && cf.NumberFormatId <= 13) // This is a number
                        return double.Parse(c.CellValue.Text, NumberStyles.Any);
                    if (cf.NumberFormatId >= 14 && cf.NumberFormatId <= 22) // This is a date
                        return DateTime.FromOADate(Convert.ToDouble(c.CellValue.Text)).ToString("dd/MM/yyyy");
                    int dateNumber = 0;
                    if (columnIndex == 7 && int.TryParse(c.CellValue.Text.Trim(), out dateNumber))
                    {
                        return DateTime.FromOADate(dateNumber).ToString("dd/MM/yyyy");
                    }//offeredDate)
                    return c.CellValue.Text.Trim();
                }
                switch (c.DataType.Value)
                {
                    case CellValues.SharedString:

                        //[0001] regex to make sure that \t,\n,\r values is replaced with blank as these create problem in json parsing
                        return Regex.Replace(ssTable.ChildElements[Convert.ToInt32(c.CellValue.Text)].InnerText.Trim(), @"\t|\n|\r", "");
                    case CellValues.Boolean:
                        return c.CellValue.Text == "1";
                    case CellValues.Date:
                        return DateTime.FromOADate(Convert.ToDouble(c.CellValue.Text));
                    case CellValues.Number:
                        if (c.StyleIndex != null)
                        {
                            var cf =
                        cellFormats.Descendants<CellFormat>()
                                   .ElementAt<CellFormat>(Convert.ToInt32(c.StyleIndex.Value));
                            if ((cf.NumberFormatId >= 14 && cf.NumberFormatId <= 22) || cf.NumberFormatId == 168 || columnIndex == 7) // This is a date
                                return DateTime.FromOADate(Convert.ToDouble(c.CellValue.Text)).ToString("dd/MM/yyyy");
                        }

                        return double.Parse(c.CellValue.Text, NumberStyles.Any);
                    default:
                        return c.CellValue != null ? c.CellValue.Text : string.Empty;
                }
            };

        private static Func<Cell, SharedStringTable, CellFormats, Object> ProcessCellValue =
            (c, ssTable, cellFormats) =>
            {
                if (c.CellValue == null) return null;
                // If there is no data type, this must be a string that has been formatted as a number

                if (c.DataType == null)
                {
                    if (c.StyleIndex == null) return c.CellValue.Text.Trim();
                    var cf =
                        cellFormats.Descendants<CellFormat>()
                                   .ElementAt<CellFormat>(Convert.ToInt32(c.StyleIndex.Value));
                    if (cf.NumberFormatId >= 0 && cf.NumberFormatId <= 13) // This is a number
                        return double.Parse(c.CellValue.Text, NumberStyles.Any);
                    if (cf.NumberFormatId >= 14 && cf.NumberFormatId <= 22) // This is a date
                        return DateTime.FromOADate(Convert.ToDouble(c.CellValue.Text)).ToString("dd/MM/yyyy");
                    return c.CellValue.Text.Trim();
                }
                switch (c.DataType.Value)
                {
                    case CellValues.SharedString:

                        //[0001] regex to make sure that \t,\n,\r values is replaced with blank as these create problem in json parsing
                        return Regex.Replace(ssTable.ChildElements[Convert.ToInt32(c.CellValue.Text)].InnerText.Trim(), @"\t|\n|\r", "");
                    case CellValues.Boolean:
                        return c.CellValue.Text == "1";
                    case CellValues.Date:
                        return DateTime.FromOADate(Convert.ToDouble(c.CellValue.Text));
                    case CellValues.Number:
                        if (c.StyleIndex != null)
                        {
                            var cf =
                        cellFormats.Descendants<CellFormat>()
                                   .ElementAt<CellFormat>(Convert.ToInt32(c.StyleIndex.Value));
                            if ((cf.NumberFormatId >= 14 && cf.NumberFormatId <= 22) || cf.NumberFormatId == 168) // This is a date
                                return DateTime.FromOADate(Convert.ToDouble(c.CellValue.Text)).ToString("dd/MM/yyyy");
                        }

                        return double.Parse(c.CellValue.Text, NumberStyles.Any);
                    default:
                        return c.CellValue != null ? c.CellValue.Text : string.Empty;
                }
            };
        public static List<string> GetSheet(string filename)
        {
            DataTable dataTable = new DataTable();
            List<string> list = new List<string>();
            using (SpreadsheetDocument spreadsheetDocument = SpreadsheetDocument.Open(filename, false))
            {
                IEnumerable<Sheet> enumerable = spreadsheetDocument.WorkbookPart.Workbook.Descendants<Sheet>();
                foreach (Sheet current in enumerable)
                {
                    list.Add(current.Name);
                }
            }
            return list;
        }

        public static List<string> GetSheet(MemoryStream stream)
        {
            DataTable dataTable = new DataTable();
            List<string> list = new List<string>();
            using (SpreadsheetDocument spreadsheetDocument = SpreadsheetDocument.Open(stream, false))
            {
                IEnumerable<Sheet> enumerable = spreadsheetDocument.WorkbookPart.Workbook.Descendants<Sheet>();
                foreach (Sheet current in enumerable)
                {
                    list.Add(current.Name);
                }
            }
            return list;
        }

        public static DataTable ReadExcel(string path, string sheetName, bool isHeaderCheck)
        {
            var data = new DataTable();
            using (SpreadsheetDocument spreadsheetDocument = SpreadsheetDocument.Open(path, false))
            {
                // Get the worksheet we are working with
                var sheets = spreadsheetDocument.WorkbookPart.Workbook.Descendants<Sheet>().Where(s => s.Name == sheetName);
                var worksheetPart = (WorksheetPart)spreadsheetDocument.WorkbookPart.GetPartById(sheets.First().Id);
                var worksheet = worksheetPart.Worksheet;
                var sstPart = spreadsheetDocument.WorkbookPart.GetPartsOfType<SharedStringTablePart>().First();
                var ssTable = sstPart.SharedStringTable;
                // Get the CellFormats for cells without defined data types
                var workbookStylesPart = spreadsheetDocument.WorkbookPart.GetPartsOfType<WorkbookStylesPart>().First();
                var cellFormats = workbookStylesPart.Stylesheet.CellFormats;

                ExtractRowsData(data, worksheet, ssTable, cellFormats, isHeaderCheck);
            }
            return data;
        }

        private static void ExtractRowsData(DataTable data, Worksheet worksheet, SharedStringTable ssTable, CellFormats cellFormats, bool isHeaderCheck)
        {
            var columnHeaders = worksheet.Descendants<Row>().First().Descendants<Cell>().Select(c => Convert.ToString(ProcessCellValue(c, ssTable, cellFormats))).ToArray();
            var columnHeadersCellReference = worksheet.Descendants<Row>().First().Descendants<Cell>().Select(c => c.CellReference.InnerText.Replace("1", string.Empty)).ToArray();

            var spreadsheetData = isHeaderCheck == true ? from row in worksheet.Descendants<Row>()
                                                          where row.RowIndex > 1
                                                          select row : from row in worksheet.Descendants<Row>()
                                                                       where row.RowIndex > 0
                                                                       select row;
            int ColumnStartIndex = 1;
            foreach (string columnHeader in columnHeaders)
            {
                data.Columns.Add(isHeaderCheck == true ? columnHeader : ("F" + ColumnStartIndex.ToString()));
                if (isHeaderCheck == false)
                {
                    columnHeaders[ColumnStartIndex - 1] = ("F" + ColumnStartIndex.ToString());
                }
                ColumnStartIndex = ColumnStartIndex + 1;
            }
            foreach (var dataRow in spreadsheetData)
            {
                var newRow = data.NewRow();
                for (int i = 0; i < columnHeaders.Length; i++)
                {
                    // Find and add the correct cell to the row object
                    var cell = dataRow.Descendants<Cell>().Where(c => c.CellReference == columnHeadersCellReference[i] + dataRow.RowIndex).FirstOrDefault();
                    if (cell != null)
                        newRow[columnHeaders[i]] = ProcessCellValue(cell, ssTable, cellFormats);
                }
                if (!newRow.ItemArray.All(field =>
                {
                    string s = null;
                    if (field != null)
                        s = field.ToString();
                    return string.IsNullOrEmpty(s);
                }))
                    data.Rows.Add(newRow);
            }
        }

    }
}