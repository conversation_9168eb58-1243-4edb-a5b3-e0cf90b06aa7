using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;
using Rebound.GlobalTrader.Site.Enumerations;

namespace Rebound.GlobalTrader.Site.Controls.ItemSearch.Data {
	[WebService(Namespace = "http://tempuri.org/")]
	[WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    public class ReqsWithBOM : Rebound.GlobalTrader.Site.Data.ItemSearch.Base
    {

		protected override void GetData() {
			List<CustomerRequirement> lst = null;

            //check view level
            ViewLevelList enmViewLevel = (ViewLevelList)GetFormValue_Int("ViewLevel");

            try
            {
                lst = CustomerRequirement.ItemSearchWithBOM(
                    SessionManager.ClientID
                    , (enmViewLevel == ViewLevelList.Team) ? (int?)SessionManager.LoginTeamID : null
                , (enmViewLevel == ViewLevelList.Division) ? (int?)SessionManager.LoginDivisionID : null
                , (enmViewLevel == ViewLevelList.My) ? (int?)LoginID : null
                    , GetFormValue_NullableInt("Order", 0)
                    , GetFormValue_NullableInt("SortDir", SortColumnDirection.ASC)
                    , GetFormValue_NullableInt("PageIndex", 0)
                    , GetFormValue_NullableInt("PageSize", 10)
                    , GetFormValue_PartForLikeSearch("Part")
                    , ""
                    , null
                    , GetFormValue_NullableInt("ReqNoLo")
                    , GetFormValue_NullableInt("ReqNoHi")
                    , GetFormValue_NullableDateTime("DateReceivedFrom")
                    , GetFormValue_NullableDateTime("DateReceivedTo")
                    , GetFormValue_NullableInt("Client", null)
                    , GetFormValue_StringForNameSearchDecode("BOM")
                );
                JsonObject jsn = new JsonObject();
                JsonObject jsnItems = new JsonObject(true);
                for (int i = 0; i < lst.Count; i++)
                {
                    JsonObject jsnItem = new JsonObject();
                    string strPrice = Functions.FormatCurrency(lst[i].Price, lst[i].CurrencyCode);
                    if (!lst[i].IsGlobalCurrencySame.Value)
                        strPrice += String.Format(" ({0})", Functions.FormatCurrency(lst[i].PHPrice, lst[i].PHCurrencyCode));
                    jsnItem.AddVariable("ID", lst[i].CustomerRequirementId);
                    jsnItem.AddVariable("No", lst[i].CustomerRequirementNumber);
                    jsnItem.AddVariable("Price", strPrice);
                    jsnItem.AddVariable("Quantity", lst[i].Quantity);
                    jsnItem.AddVariable("CMNo", lst[i].CompanyNo);
                    jsnItem.AddVariable("CMName", lst[i].CompanyName);
                    jsnItem.AddVariable("Date", Functions.FormatDate(lst[i].ReceivedDate));
                    jsnItem.AddVariable("Part", lst[i].Part);
                    jsnItem.AddVariable("ROHS", lst[i].ROHS);
                    jsnItem.AddVariable("BMName", lst[i].BOMHeader);
                    jsnItem.AddVariable("AS9120", lst[i].AS9120.Value);
                    jsnItems.AddVariable(jsnItem);
                    jsnItem.Dispose(); jsnItem = null;
                }
                jsn.AddVariable("Count", (lst.Count > 0) ? lst[0].RowCnt : 0);
                jsn.AddVariable("Results", jsnItems);
                OutputResult(jsn);
                jsnItems.Dispose(); jsnItems = null;
                jsn.Dispose(); jsn = null;
            }
            catch (Exception ex)
            {
                WriteError(ex);
            }
            finally
            {
				lst = null;
			}
			base.GetData();
		}

	}
}
