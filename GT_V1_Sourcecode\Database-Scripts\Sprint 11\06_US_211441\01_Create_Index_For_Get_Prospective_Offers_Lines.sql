﻿/*   
===========================================================================================  
TASK			UPDATED BY			DATE			ACTION		DESCRIPTION  
[US-211441]		Trung Pham Van		09-Oct-2024		CREATE		Create index to increase performance
===========================================================================================
*/
-- CREATE INDEX on tbProspectiveOfferLines if it not exists
IF NOT EXISTS (SELECT 1 FROM sys.indexes WHERE name = 'IX_tbProspectiveOfferLines_ProspectiveOfferNo_Part_ProspectiveOfferLineId' AND object_id = OBJECT_ID('tbProspectiveOfferLines'))
BEGIN
    CREATE INDEX IX_tbProspectiveOfferLines_ProspectiveOfferNo_Part_ProspectiveOfferLineId ON tbProspectiveOfferLines (ProspectiveOfferNo, Part, ProspectiveOfferLineId);
END
IF NOT EXISTS (SELECT 1 FROM sys.indexes WHERE name = 'IX_tbProspectiveOfferLines_ProspectiveOfferNo_Part_ManufacturerNo' AND object_id = OBJECT_ID('tbProspectiveOfferLines'))
BEGIN
    CREATE INDEX IX_tbProspectiveOfferLines_ProspectiveOfferNo_Part_ManufacturerNo ON tbProspectiveOfferLines (ProspectiveOfferNo, Part, ManufacturerNo);
END

-- CREATE INDEX on tbClient if it not exists
IF NOT EXISTS (SELECT 1 FROM sys.indexes WHERE name = 'IX_tbClient_ClientId' AND object_id = OBJECT_ID('tbClient'))
BEGIN
    CREATE INDEX IX_tbClient_ClientId ON tbClient (ClientId);
END

-- CREATE INDEX on tbManufacturer if it not exists
IF NOT EXISTS (SELECT 1 FROM sys.indexes WHERE name = 'IX_tbManufacturer_ManufacturerId' AND object_id = OBJECT_ID('tbManufacturer'))
BEGIN
    CREATE INDEX IX_tbManufacturer_ManufacturerId ON tbManufacturer (ManufacturerId);
END

-- CREATE INDEX on tbLyticaAPI if it not exists
IF NOT EXISTS (SELECT 1 FROM sys.indexes WHERE name = 'IX_tbLyticaAPI_OriginalPartSearched_Manufacturer' AND object_id = OBJECT_ID('tbLyticaAPI'))
BEGIN
    CREATE INDEX IX_tbLyticaAPI_OriginalPartSearched_Manufacturer ON tbLyticaAPI (OriginalPartSearched, Manufacturer);
END

-- CREATE INDEX on tbIHSparts if it not exists
IF NOT EXISTS (SELECT 1 FROM sys.indexes WHERE name = 'IX_tbIHSparts_FullPart_ManufacturerFullName' AND object_id = OBJECT_ID('tbIHSparts'))
BEGIN
    CREATE INDEX IX_tbIHSparts_FullPart_ManufacturerFullName ON tbIHSparts (FullPart, ManufacturerFullName);
END

