﻿using System;
using System.Collections;
using System.Configuration;
using System.Data;
using System.Linq;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.HtmlControls;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Xml.Linq;

namespace Rebound.GlobalTrader.Site.Controls.Forms
{
    public partial class EXCELDocuments_AddDragDrop : Base
    {
        protected override void OnInit(EventArgs e)
        {
            base.OnInit(e);
            TitleText = Functions.GetGlobalResource("FormTitles", "EXCEL_Add");
            AddScriptReference("Controls.Nuggets.EXCELDocumentsDragDrop.Add.EXCELDocuments_AddDragDrop.js");
        }

        protected override void OnPreRender(EventArgs e)
        {
            SetupScriptDescriptors();
            base.OnPreRender(e);
        }

        private void SetupScriptDescriptors()
        {
            _scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.Forms.EXCELDocuments_AddDragDrop", ctlDesignBase.ClientID);
        }
    }
}