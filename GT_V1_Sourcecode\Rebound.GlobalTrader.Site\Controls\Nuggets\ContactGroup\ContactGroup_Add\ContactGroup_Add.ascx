﻿<%@ Control Language="C#" AutoEventWireup="true" CodeBehind="ContactGroup_Add.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Forms.ContactGroup_Add" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<script src="js/jquery.dataTables.min.js"></script>
<script src="js/ContactGroup.js"></script>
<%--<link href="css/jquery-ui.css.css" rel="stylesheet" />--%>
<%--<script src="js/jquery.min.js"></script>--%>
<script src="js/jquery-ui.js"></script>
<style>
    .disable-click {
        pointer-events: none;
    }

    div.quickSearch {
        width: 320px;
        font-weight: normal;
    }

    .quickSearchWrapper {
        background-color: #fff;
        border-radius: 2px;
    }

    #ctl00_cphMain_ctlNotify_ctlDB_ctl14_ctlNotify_ctlDB_ctlLotNo_ctl03_aut_ctl03, #ctl00_cphMain_ctlNotify_ctlDB_ctl14_ctlNotify_ctlDB_ctlSalespersion_ctl03_aut_ctl03 {
        color: #000;
        font-weight: normal;
        padding: 3px;
        padding-bottom: 3px;
        display: inline-block;
        border-bottom: 1px #85d279 solid;
        padding-bottom: 5px;
        min-height: 25px;
        width: 100%;
    }

    a.quickSearchReselect {
        margin-left: 0;
        padding-bottom: 5px;
        display: block;
        width: 100%;
        background: #a3e898;
        padding-top: 5px;
        text-align: center;
    }

    .dataTables_wrapper .dataTables_paginate .paginate_button {
        width: 88%;
        text-align: right;
        height: 60px;
        border-radius: 10%;
        background-color: #C0C0C0;
        vertical-align: central;
        color: black;
        margin: 0 5px;
        border: 0 !important;
        line-height: 17px;
        box-shadow: none !important;
    }

    .LoaderPopup {
        background: rgba(0,0,0,.4);
        cursor: pointer;
        display: none;
        /* height: auto; */
        height: 100%;
        position: absolute;
        text-align: center;
        top: -20px;
        width: 100%;
        z-index: 10000;
    }

    .wdt120 {
        width: 120px !important;
        float: left;
    }

    .quickSearchOuter {
        width: 100% !important;
    }

    * {
        box-sizing: border-box;
    }
</style>
<style>
    .ajax-file-upload {
        padding: 7px 25px !important;
        width: auto !important;
        background-color: #158684 !important;
        color: #fff !important;
        margin: 0 !important;
    }

    .ajax-file-upload {
        height: 24px !important;
    }



    #table1_length, #table1_info {
        padding: 10px;
        /*background: #3a6c34;*/
        color: #666;
    }

    #table1_paginate {
        margin-top: 5px;
    }

        #table1_paginate a {
            padding: 5px;
            margin: 5px;
            background: #3a6c34;
            color: #fff;
        }
</style>
<style>
    .container-greenbase {
        background-color: #56954e;
        color: #fff;
        font-size: 11px;
        font-family: tahoma;
        padding: 10px;
    }

    /*fieldset {
			border: 1px #6cab63 solid;
			margin-bottom:96px;
		}*/

    legend {
        display: block;
        padding-left: 1px;
        padding-right: 5px;
        color: #fff;
        font-family: Tahoma;
    }

    table {
        width: 100%
    }

        table td {
            vertical-align: top;
        }

            table td.firstcol {
                width: 90%;
            }

    .radio-option {
        margin-bottom: 10px;
    }

    select {
        min-width: 110px;
        margin-right: 20px;
        border: 0px #fff solid;
        font-size: 11px;
        padding: 2px;
    }

    label {
        margin-right: 10px;
    }

    .space {
        padding: 10px;
    }

    .btn {
        border: none;
        padding: 5px 8px;
        border-radius: 2px;
        width: 100px;
    }

    .show {
        background-color: #009491;
        color: #fff;
        margin-right: 10px;
        border: 1px #009491 solid;
    }

    .reset {
        background-color: #85d279;
        color: #2c6e23;
        border: 1px #2c6e23 solid;
        padding: 20px 0;
        margin-top: 5px;
    }

    .exlsfile {
        width: 100%;
        background: #d5d5d5;
        /*height: 110px;*/
        margin-top: 5px;
    }

        .exlsfile .header {
            background: #434343;
            padding: 5px 10px;
        }

    .clientblock .row {
        float: left;
        width: 100%;
        padding: 5px 0;
    }

        .clientblock .row .label {
            display: block;
            float: left;
            width: 13%;
        }

    .row .label {
        display: block;
        float: left;
        width: 4%;
    }

    .clientblock .row .radio-option {
        float: left;
    }

    .radio-option select {
        min-width: 240px;
        margin-right: 20px;
    }

    input[type='radio'] {
        vertical-align: bottom;
        margin: 0 5px 0 5px;
    }

    .gfg {
        font-size: 40px;
        color: green;
        font-weight: bold;
    }

    .col2 {
        width: 48%;
        margin: 0% 1%;
        float: left;
    }

    .file {
        position: relative;
        display: inline-block;
        cursor: pointer;
        margin-right: 105px;
    }

        .file input {
            min-width: 14rem;
            margin: 0;
            filter: alpha(opacity=0);
            opacity: 0;
        }

    .file-custom {
        position: absolute;
        top: 0;
        right: 0;
        left: 0;
        z-index: 5;
        line-height: 1.5;
        color: #555;
        background-color: #fff;
        border: .075rem solid #ddd;
    }

        .file-custom::before {
            position: absolute;
            top: -.075rem;
            right: -2.075rem;
            bottom: -.075rem;
            z-index: 6;
            display: block;
            content: "";
            padding: .1rem 0.9rem;
            line-height: 1.5;
            background-color: #85ce7a;
            border: 1px #93c58b solid;
        }

        .file-custom::after {
            content: "Choose file...";
        }

    .colright {
        float: right;
    }

    .btn {
        cursor: pointer;
    }

        .btn.right {
            background: #cacaca;
            color: #040404;
            padding: 10px 18px;
            border: 1px #427f3a solid;
            /*width: 40px;*/
            white-space: normal;
            /*float: right;*/
            font-weight: bold;
            /* margin-top: 4px;*/
        }

    .spanBorder {
        margin: 0px 0 0 3px;
        color: #c1cbce;
    }

    table.formRows td.title {
        padding-right: 0px;
        color: #fff;
    }

    #ctl00_cphMain_ctlNotify_ctlDB_ctl14_ctlNotify_ctlDB_cmbSalesPersion {
        display: flex;
    }

    #ddlClient, #ctl00_cphMain_ctlNotify_ctlDB_ctl14_ctlNotify_ctlDB_txt, #ddlMainCurrency, #txtBomName, #ddlContact {
        width: 170px !important;
    }

    /*#ctl00_cphMain_ctlNotify_ctlDB_ctl14_ctlNotify_ctlDB_ctlCompany_ctl04_txt, #ctl00_cphMain_ctlNotify_ctlDB_ctl14_ctlNotify_ctlDB_ctlSalespersion_ctl04_txt{width:170px!important;}*/
    .btnfull {
        width: 100% !important;
    }

    .three-col td {
        width: 31%;
    }

    .col3 {
        display: flex;
    }

        .col3 label {
            min-width: 100px;
            display: inline-block;
            text-align: right;
            margin-right: 4px;
        }

        .col3 input, .col3 select {
            margin: 0 0 3px 0;
        }

            .col3 input[type='checkbox'] {
                margin-right: 10px;
                vertical-align: middle;
            }

            .col3 input[type='text'] {
                width: 111px;
                vertical-align: middle;
            }

    .mainArea_Off .mainRightInner {
        margin-left: 11px;
        max-width: 1045px;
        margin: 0 auto;
    }

    .mainArea_On .mainRightInner {
        width: 1045px;
    }

    #ctl00_cphMain_ctlNotify_ctlDB_ctl14_ctlNotify_ctlDB_ctlLotNo_ctl03_aut_ctl05 {
        width: auto !important;
    }

    #ctl00_cphMain_ctlNotify_ctlDB_ctl14_ctlNotify_ctlDB_ctlLotNo_tdTitle {
        width: 50px;
    }

    #ctl00_cphMain_ctlNotify_ctlDB_ctl14_ctlNotify_ctlDB_ctlSalespersion_tdTitle {
        width: 106px;
    }


    table.dataTable tr th {
        background-color: #424241 !important;
        color: #fff;
        border-bottom: solid 1px #515050 !important;
    }

    .boxForms table.dataTable td {
        color: #000;
        font-weight: normal;
        white-space: nowrap;
    }
</style>
<style>
    /* The Modal (background) */
    .modal {
        display: none;
        /* Hidden by default */
        position: absolute;
        /* Stay in place */
        z-index: 1;
        /* Sit on top */
        padding-top: 100px;
        /* Location of the box */
        left: 0;
        top: 0;
        width: 100%;
        /* Full width */
        height: 100%;
        /* Full height */
        overflow: auto;
        /* Enable scroll if needed */
        background-color: rgb(0, 0, 0);
        /* Fallback color */
        background-color: rgba(0, 0, 0, 0.4);
        /* Black w/ opacity */
    }

    /* Modal Content */
    .modal-content {
        background-color: #66a75e;
        margin: auto;
        padding: 20px;
        border: 1px solid #66a75e;
        width: 50%;
        position: relative;
        overflow: hidden;
    }

        .modal-content table tr td,
        .modal-content table tr th {
            text-align: left;
        }

    /* The Close Button */
    .close {
        color: #aaaaaa;
        float: right;
        font-size: 28px;
        font-weight: bold;
    }

    span.PartDetailsGridGoBtn2 {
        padding: 2px 0px;
        margin-left: 0px;
    }

    .headertitle {
        background: #5f9553;
        padding: 12px;
    }

    .LoaderMain {
        background: rgba(0, 0, 0, .4);
        cursor: pointer;
        display: none;
        /* height: auto; */
        height: 100%;
        position: absolute;
        text-align: center;
        top: 30px;
        width: 95%;
        z-index: 10000;
    }

    .cssloadIHSloader {
        width: 244px;
        height: 49px;
        line-height: 49px;
        text-align: center;
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        -o-transform: translate(-50%, -50%);
        -ms-transform: translate(-50%, -50%);
        -webkit-transform: translate(-50%, -50%);
        -moz-transform: translate(-50%, -50%);
        font-family: helvetica, arial, sans-serif;
        text-transform: uppercase;
        font-weight: 900;
        font-size: 18px;
        color: rgb(206, 66, 51);
        letter-spacing: 0.2em;
        background-color: aliceblue;
    }

    #ui-id-1 {
        width: 150px;
        position: absolute;
        top: 435px;
        left: 646px;
        z-index: 30000;
        background-color: white;
        border: solid 1px black;
        cursor: pointer;
    }

    ul {
        list-style-type: none !important;
        padding: 1px !important;
        margin-left: 1px !important;
    }

    .ui-helper-hidden-accessible {
        display: none;
    }

    .ui-state-active, 
    .ui-widget-content .ui-state-active, 
    .ui-widget-header .ui-state-active, 
    a.ui-button:active, 
    .ui-button:active, 
    .ui-state-active.ui-button:hover {
        border: 1px solid #fff;
        background:Grey;
    }
</style>
<ReboundUI_Form:DesignBase ID="ctlDB" runat="server" ShowRequiredFieldsExplanation="true" ShowQuickHelp="false">
    <Links>
        <%--<ReboundUI:IconButton ID="ibtnSave" runat="server" IconGroup="Nugget" IconTitleResource="Save" IconCSSType="Save" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />--%>
        <ReboundUI:IconButton ID="ibtnCancel" runat="server" IconGroup="Nugget" IconTitleResource="Cancel" IconCSSType="Cancel" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
    </Links>
    <Content>
        <ReboundUI_Table:Form ID="frm" runat="server">
            <asp:TableRow>
                <asp:TableCell class="title" RowSpan="2" Style="width: 100%">
                    <table>
                        <tr>
                            <td>
                                <label>Manufacturer Name</label>
                            </td>
                            <td>
                                <input type="text" id="txtName" runat="server" />
                            </td>
                            <td>
                                <label>Manufacturer Code</label>
                            </td>
                            <td>
                                <input type="text" id="txtCode" runat="server" />
                            </td>
                        </tr>
                        <tr>
                            <td colspan="4">
                                <input type="button" class="btn right" id="btnSearch" value="Search">

                                <input type="button" class="btn right" id="btnAddInGroup" style="width: 250px" value="Add in manufacturer group">
                            </td>
                        </tr>
                    </table>
                    <table style="width: 100%;">
                        <tr>
                            <td class="firstcol">
                                <div id="ShowDataID" style="width: 940px; overflow: scroll; border: solid 1px; overflow-x: scroll; overflow-y: scroll; height: 300px; border-style: ridge;" class="display dataTables_wrapper dataTables_paginate paginate_button overflow: auto">
                                    <table id="table1" class="table table-striped table-bordered nowrap " cellspacing="0" cellpadding="0" border="0" style="width: 100%; table-layout: auto; border-style: None; border-collapse: collapse;">
                                        <thead>
                                        </thead>
                                    </table>
                                </div>
                            </td>
                        </tr>
                    </table>
                    <div id="myModal" class="modal">
                        <!-- Modal content -->
                        <div class="modal-content" style="height: 140px; 0px 0px 15px 0px #000; width: 350px;">
                            <div class="LoaderMain" id="divLoader">
                                <div>
                                    <div class="cssloadIHSloader">Loading..</div>
                                </div>
                            </div>
                            <asp:Label ID="btn6" Text="Cancel" runat="server" Style="cursor: pointer;"
                                CssClass="PartDetailsGridGoBtn2 close">&times;</asp:Label>
                            <div class="tophead">
                                <div class="bgbase" style="text-align:center">
                                    <span class="headertitle">Add in manufacturer group</span>
                                </div>
                                &nbsp;
                                <span class="close" id="ctl00_cphMain_ctlAddGroupCodeCompany_ctlDB_ctl14_ctlAdd_ctlDB_btn6">&times;</span>
                                <div class="bgbase">
                                    <table>
                                        <tr>
                                            <td>
                                                <label>Group Name<span>*</span> :</label><input type="text" id="txtContactName" />
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <label>Group Code&nbsp;<span>*</span> :</label><input type="text" id="txtContactCode" />
                                            </td>
                                        </tr
                                        <tr>
                                            <td style="text-align: right">
                                                <input type="button" class="btn right" id="btnSave" value="Save">
                                            </td>
                                        </tr>

                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </asp:TableCell>
            </asp:TableRow>

        </ReboundUI_Table:Form>
    </Content>
</ReboundUI_Form:DesignBase>
