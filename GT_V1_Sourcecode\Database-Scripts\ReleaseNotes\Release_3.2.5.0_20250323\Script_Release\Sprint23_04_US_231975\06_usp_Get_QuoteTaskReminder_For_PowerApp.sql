﻿
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO



/*
--===============================================================================================================================  
TASK            UPDATED BY         DATE                ACTION        DESCRIPTION  
[US-229094]     Phuc Hoang         21-Jan-2025         CREATE        Quote - Add automatic Task Reminder based on Quote Status
[US-231975]     Phuc Hoang	       05-Mar-2025	       UPDATE	     IPO - MFR Group Code Franchised Tick Box Further Enhancement
=================================================================================================================================  
*/


CREATE OR ALTER PROCEDURE [dbo].[usp_Get_QuoteTaskReminder_For_PowerApp]
    
AS
BEGIN
SET NOCOUNT ON

Declare @PowerProspectUrl NVARCHAR(MAX)=''

-----------Power App url---------
SELECT
@PowerProspectUrl=FlowUrl
FROM tbPowerApp_urls WHERE FlowName='Quote_TaskReminder_Notification'
--------------------------------------------------
SELECT
    @PowerProspectUrl AS PowerProspectUrl
    ,td.ToDoId
    ,td.LoginNo
    ,td.[Subject]
    ,td.DateAdded
    ,td.DueDate
    ,td.ToDoText
    ,td.IsComplete
    ,td.ReminderDate
    ,td.ReminderText
    ,td.QuoteNo
	,qt.QuoteNumber
    ,td.ReminderHasBeenNotified
    ,td.UpdatedBy
    ,td.DLUP
    ,td.HasReview
    ,td.[Name]
    ,td.TypeNo
	,td.DailyReminder
    ,lg.EmployeeName AS CreatorName
    ,lg.EMail AS ToCreatorEmail
	,qt.Salesman AS SalesmanNo
	,lgSale.EmployeeName AS SalesmanName
    ,lgSale.EMail As SalesmanEmail

  FROM  dbo.tbToDo td 
  JOIN dbo.tbQuote qt ON td.QuoteNo = qt.QuoteId
  JOIN dbo.tbLogin lg ON td.UpdatedBy = lg.LoginId
  JOIN dbo.tbLogin lgSale ON qt.Salesman = lgSale.LoginId
  WHERE td.ToDoCategoryNo = 2 --Only query Quote category
  AND ISNULL(td.ReminderText, '') <> '' 
  AND 
  (
	(ISNULL(td.DailyReminder, 0) = 0 AND CAST(td.ReminderDate AS DATE) = CAST(GETDATE() AS DATE))
	OR (ISNULL(td.DailyReminder, 0) = 1 AND CAST(td.ReminderDate AS DATE) <= CAST(GETDATE() AS DATE))
  )
  AND DATEPART(HOUR, td.ReminderDate) = DATEPART(HOUR, GETDATE())
  AND DATEPART(MINUTE, td.ReminderDate) >= DATEPART(MINUTE, GETDATE()) AND DATEPART(MINUTE, td.ReminderDate) <= DATEPART(MINUTE, GETDATE()) + 14
  
SET NOCOUNT OFF

END
GO


