﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER   PROCEDURE [dbo].[usp_insert_PurchaseOrderLine]                                                
--******************************************************************************************                                                
--* SK 29.10.2009:                                                
--* - allow for new column - FullSupplierPart - used for searching                                                
--*                                                
--* SK 29/07/2009:                                                
--* - allow for Notes                                               
--* Marker     changed by      date         Remarks                                              
--* [001]      V<PERSON><PERSON>     19/12/2011   ESMS Ref:17 - Update manufacture with suppliername                         
--* [002]      Anand     26/08/2020   Added for pass ihs column                    
--* [003]      Abhinav <PERSON>xena 29/07/2021  Added new field for repeat order.    
--* [004]  Ravi Bhushan 13/09/2023 RP-2340 (AS6081)    
--******************************************************************************************         
/*   
===========================================================================================  
TASK         UPDATED BY   DATE          ACTION    DESCRIPTION  
[US-214629]  Trung Pham   16-Dec-2024	update    Renew add relationship manually flow and calculate Star Rating after that
[US-214629]  Trung Pham   02-Jan-2025	update    Remove check supplier
===========================================================================================  
*/ 
@PurchaseOrderNo  int ,                                                 
@Part     nvarchar(30) ,                                                
@ManufacturerNo   int    = Null ,                                                
@DateCode    nvarchar(5)  = Null ,                                                
@PackageNo    int    = Null ,                                                
@Quantity    int ,                                                
@Price     float ,                                                
@DeliveryDate   datetime ,                                                
@ReceivingNotes   nvarchar(MAX) = Null ,                                                
@Taxable    bit ,                                                
@ProductNo    int    = Null ,                                                
@Posted     bit ,                                                
@ShipInCost    float   = Null ,                                                
@SupplierPart   nvarchar(30) = Null ,                                                
@ROHS     tinyint =NULL,                                                
@Notes     nvarchar(2000) = Null ,                                      
@PromiseDate   datetime ,                                                          
@UpdatedBy    int    = Null ,                             
@ReqSerialNo    bit =0,                                  
@MSLLevel nvarchar(100) = null ,                           
@SupplierWarranty int = null,                                        
@PrintHazardous    bit = NULL ,                         
--[002] code start                        
--ihs data passing start                        
--@CountryOfOrigin nvarchar(50)=null,                              
@CountryOfOriginNo int=null,                              
@LifeCycleStage nvarchar(50)=null,                              
@HTSCode varchar(20)=null,                              
@AveragePrice  float=null,                              
@Packing varchar(60)=null,                              
@PackagingSize varchar(100)=null  ,                          
@Descriptions nvarchar(max)=null  ,                    
@IHSProduct nvarchar(100)=null,                     
@ECCNCode varchar(100)=null ,                       
--[002] code end              
--[003] code start             
@RepeatOrder bit=0              
--[003] code end        
, @AS6081 bit = 0 --[004]    
,@PurchaseOrderLineId int Output                                                
                                                
AS                                                
                                                
BEGIN                                                
INSERT               
INTO dbo.tbPurchaseOrderLine                                                
 (                              
  PurchaseOrderNo                                                  
 , FullPart                       
 , Part                                                    
 , ManufacturerNo            
 , DateCode                                                   
 , PackageNo                                                  
 , Quantity                                                   
 , Price                                                    
 , DeliveryDate                                       
 , ReceivingNotes                                             
 , Taxable                                                   
 , ProductNo                                                
 , Posted                                                   
 , ShipInCost                                
 , SupplierPart                                                  
 , ROHS                                                
 , Notes                                      
 , PromiseDate                                            
 , UpdatedBy                                                 
 , FullSupplierPart                             
 , ReqSerialNo                                
 , MSLLevel                             
 ,SupplierWarranty                         
 --[002] code start                        
 --ihs data passing start                      
 --,CountryOfOrigin                              
 , CountryOfOriginNo                              
 , LifeCycleStage                              
 , HTSCode                              
 , AveragePrice                              
 , Packing                              
 , PackagingSize                     
 , Descriptions                     
 ,IHSProduct                    
 ,ECCNCode                       
 --[002] code  end                             
 , PrintHazardous             
 --[003] start code               
 , RepeatOrder              
 --[003] end code      
 , AS6081 --[004]    
 )                                                
VALUES                                                
 (                                                
  @PurchaseOrderNo                                            
 , dbo.ufn_get_fullpart(@Part)                                                  
 , @Part                                                    
 , @ManufacturerNo                                                   
 , @DateCode                                        
 , @PackageNo                                                  
 , @Quantity                                                   
 , @Price                                                    
 , @DeliveryDate                                                   
 , @ReceivingNotes                                                  
 , @Taxable                                                   
 , @ProductNo                                                
 , @Posted                                                   
 , @ShipInCost                                                   
 , @SupplierPart                                                   
 , @ROHS                                                
 , @Notes                                    
 , @PromiseDate                                              
 , @UpdatedBy                                                
 , dbo.ufn_get_fullpart(@SupplierPart)                              
 , @ReqSerialNo                             
 , @MSLLevel                              
 ,@SupplierWarranty                         
 --[002] code start                        
 --ihs data passing start                        
--  ,@CountryOfOrigin                              
 , @CountryOfOriginNo                              
 , @LifeCycleStage                              
 , @HTSCode             
 , @AveragePrice             
 , @Packing                              
 , @PackagingSize                          
 , @Descriptions                      
 ,@IHSProduct                     
 ,@ECCNCode      
 --[002] code end                         
 , @PrintHazardous              
 --[003] code start              
 , @RepeatOrder             
 --[003] code end      
 , ISNULL(@AS6081,0) --[004]    
 )                                                 
                         
 SET @PurchaseOrderLineId = SCOPE_IDENTITY()                                          
--[RP-2464]  start   
    IF(SELECT COUNT(1)  
    FROM tbPurchaseOrderLine  
    WHERE PurchaseOrderNo = @PurchaseOrderNo and ISNULL(AS6081,0) = 1) > 0   
   BEGIN  
        UPDATE tbPurchaseOrder set AS6081 = 1 WHERE PurchaseOrderId = @PurchaseOrderNo;  
    END  
--[RP-2464] END                                
	DECLARE @SupplierMfrNo INT;
	SELECT @SupplierMfrNo = CompanyNo FROM tbPurchaseOrder WHERE PurchaseOrderId = @PurchaseOrderNo

	IF NOT EXISTS (SELECT 1 FROM tbManufacturerLink WHERE ManufacturerNo = @ManufacturerNo AND SupplierCompanyNo = @SupplierMfrNo)
	BEGIN
		INSERT INTO tbManufacturerLink (ManufacturerNo, SupplierCompanyNo, ManufacturerRating, SupplierRating, UpdatedBy, DLUP, StarRating)
		VALUES (@ManufacturerNo, @SupplierMfrNo, 0, 0, @UpdatedBy, GETDATE(), 0)
	END
	ELSE
	BEGIN
		UPDATE tbManufacturerLink
		SET StarRating = 0
		WHERE ManufacturerNo = @ManufacturerNo AND SupplierCompanyNo = @SupplierMfrNo
	END

	-- Recalculate the Star for all Supplier - Manufacturer relationship based on Purchase Order every time a new PO Line is created
	DECLARE @LatestNumOfPO INT;
	SELECT TOP 1 @LatestNumOfPO = NumOfPO FROM tbStarRatingConfig ORDER BY CreatedDate DESC
	UPDATE tbManufacturerLink
	SET StarRating = CASE 
	    WHEN POLineCount / @LatestNumOfPO >= 5 THEN 5
	    ELSE FLOOR(POLineCount / @LatestNumOfPO) 
	END
	FROM tbManufacturerLink l
	CROSS APPLY (
	    SELECT COUNT(DISTINCT pol.PurchaseOrderLineId) AS POLineCount
	    FROM tbCompany c
	    JOIN tbPurchaseOrder po ON po.CompanyNo = l.SupplierCompanyNo
	    JOIN tbPurchaseOrderLine pol ON pol.ManufacturerNo = l.ManufacturerNo
	    WHERE c.CompanyId = l.SupplierCompanyNo
	      AND pol.PurchaseOrderNo = po.PurchaseOrderId
	) AS SubQuery
	WHERE l.StarRating IS NOT NULL AND l.ManufacturerNo = @ManufacturerNo AND l.SupplierCompanyNo = @SupplierMfrNo;

  --Reset the PurchaseOrderLine serial Number:                                
   EXEC usp_Update_PurchaseOrderLine_SerialNo @PurchaseOrderNo                                         
                                       
END; 
GO


