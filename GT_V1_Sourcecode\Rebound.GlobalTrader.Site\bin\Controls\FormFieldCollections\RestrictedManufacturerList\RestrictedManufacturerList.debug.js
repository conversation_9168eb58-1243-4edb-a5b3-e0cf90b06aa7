///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.FormFieldCollections");

Rebound.GlobalTrader.Site.Controls.FormFieldCollections.RestrictedManufacturerList = function(element) {
    Rebound.GlobalTrader.Site.Controls.FormFieldCollections.RestrictedManufacturerList.initializeBase(this, [element]);
	this._aryRecipientLoginIDs = [];
	this._aryRecipientLoginNames = [];
    this._intNumberRecipients = 0;
    this._manufacturerNameArr = [];
};

Rebound.GlobalTrader.Site.Controls.FormFieldCollections.RestrictedManufacturerList.prototype = {

    get_pnlSelected: function() { return this._pnlSelected; }, set_pnlSelected: function(value) { if (this._pnlSelected !== value) this._pnlSelected = value; },
    get_lblSelected: function() { return this._lblSelected; }, set_lblSelected: function(value) { if (this._lblSelected !== value) this._lblSelected = value; },
    get_autLoginOrGroup: function() { return this._autLoginOrGroup; }, set_autLoginOrGroup: function(value) { if (this._autLoginOrGroup !== value) this._autLoginOrGroup = value; },

    initialize: function () {
        this._autLoginOrGroup._intGlobalLoginClientNo =  this._intGlobalClientNo;
        this._autLoginOrGroup.addSelectionMadeEvent(Function.createDelegate(this, this.newRecipientSelected));
        this.showSelected();
        Rebound.GlobalTrader.Site.Controls.FormFieldCollections.RestrictedManufacturerList.callBaseMethod(this, "initialize");
    },

    dispose: function() {
        if (this.isDisposed) return;
        if (this._autLoginOrGroup) this._autLoginOrGroup.dispose();
        this._pnlSelected = null;
        this._lblSelected = null;
        this._autLoginOrGroup = null;
        this._aryRecipientLoginIDs = null;
        this._aryRecipientLoginNames = null;
        this._intNumberRecipients = null;
        this._manufacturerNameArr = null;
        Rebound.GlobalTrader.Site.Controls.FormFieldCollections.RestrictedManufacturerList.callBaseMethod(this, "dispose");
    },

    newRecipientSelected: function (blnShow) {
        this.addNewLoginRecipient(this._autLoginOrGroup._varSelectedID, this._autLoginOrGroup._varSelectedValue);
    },

    addNewLoginRecipient: function(intID, strName) {
        if (!Array.contains(this._aryRecipientLoginIDs, intID)) {
            Array.add(this._aryRecipientLoginIDs, intID);
            Array.add(this._aryRecipientLoginNames, strName);
            this._intNumberRecipients += 1;
        }
        this.showSelected();
        this._autLoginOrGroup.reselect();
    },

    showSelected: function() {
        $R_FN.showElement(this._pnlSelected, (this._intNumberRecipients > 0));
        var strFN = "";
        var strHTML = "";
        for (i = 0, l = this._aryRecipientLoginIDs.length; i < l; i++) {
            if (this._manufacturerNameArr.length > 0 && this._manufacturerNameArr.includes(this._aryRecipientLoginNames[i])) {
                strHTML += '<div class="mailRecipient" style="color: #EE0000;">';
            }
            else {
                strHTML += '<div class="mailRecipient">';
            }
            strHTML += this._aryRecipientLoginNames[i];
            strFN = String.format("$find('{0}').removeLoginRecipient({1});", this._element.id, i);
            strHTML += String.format('&nbsp;&nbsp;&nbsp;<a href="javascript:void(0);" class="quickSearchReselect" onclick="{0}">[x]</a>', strFN);
            strHTML += "</div>";
        }
        $R_FN.setInnerHTML(this._lblSelected, strHTML);
    },

    removeLoginRecipient: function (i) {
        Array.removeAt(this._aryRecipientLoginIDs, i);
        Array.removeAt(this._aryRecipientLoginNames, i);
        this._intNumberRecipients -= 1;
        this.showSelected();
    },

    setValue_To: function(strValue) {
        this._ctlRelatedForm.setFieldValue("ctlRestrictedManufacturer", strValue);
    },
    getValue_To: function() {
        return this._ctlRelatedForm.getFieldValue("ctlRestrictedManufacturer");
    },

    validateFields: function() {
        var blnOK = true;
        this._ctlRelatedForm.resetFormFields();
        if (this._intNumberRecipients == 0) {
            this._ctlRelatedForm.setFieldInError("ctlRestrictedManufacturer", true, $R_RES.RequiredFieldMissingMessage);
            blnOK = false;
        }
        return blnOK;
    },

    resetFields: function() {
        Array.clear(this._aryRecipientLoginIDs);
        Array.clear(this._aryRecipientLoginNames);
        Array.clear(this._manufacturerNameArr);
        this._intNumberRecipients = 0;
        this.showSelected();
    }
};

Rebound.GlobalTrader.Site.Controls.FormFieldCollections.RestrictedManufacturerList.registerClass("Rebound.GlobalTrader.Site.Controls.FormFieldCollections.RestrictedManufacturerList", Rebound.GlobalTrader.Site.Controls.Forms.LabelFormField, Sys.IDisposable);
