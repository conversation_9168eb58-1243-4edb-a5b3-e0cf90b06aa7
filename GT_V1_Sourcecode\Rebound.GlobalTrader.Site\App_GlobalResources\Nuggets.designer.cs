//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Resources {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option or rebuild the Visual Studio project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.VisualStudio.Web.Application.StronglyTypedResourceProxyBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class Nuggets {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Nuggets() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Resources.Nuggets", global::System.Reflection.Assembly.Load("App_GlobalResources"));
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Addresses.
        /// </summary>
        internal static string Addresses {
            get {
                return ResourceManager.GetString("Addresses", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to All Document Detail.
        /// </summary>
        internal static string AllDocumentInfo {
            get {
                return ResourceManager.GetString("AllDocumentInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Application Settings.
        /// </summary>
        internal static string AppSettings {
            get {
                return ResourceManager.GetString("AppSettings", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Type Of Suppliers.
        /// </summary>
        internal static string AS6081 {
            get {
                return ResourceManager.GetString("AS6081", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reason For Chosen Supplier.
        /// </summary>
        internal static string AS6081_RCS {
            get {
                return ResourceManager.GetString("AS6081_RCS", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Risk Of Supplier.
        /// </summary>
        internal static string AS6081_ROS {
            get {
                return ResourceManager.GetString("AS6081_ROS", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to HUBRFQ.
        /// </summary>
        internal static string BillOfMaterial {
            get {
                return ResourceManager.GetString("BillOfMaterial", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New HUBRFQ.
        /// </summary>
        internal static string BOMAdd {
            get {
                return ResourceManager.GetString("BOMAdd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to BOM Import.
        /// </summary>
        internal static string BOMImport {
            get {
                return ResourceManager.GetString("BOMImport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to BOM Import Form.
        /// </summary>
        internal static string BOMImport_Form {
            get {
                return ResourceManager.GetString("BOMImport_Form", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to HUBRFQ Items.
        /// </summary>
        internal static string BOMItems {
            get {
                return ResourceManager.GetString("BOMItems", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Main Information.
        /// </summary>
        internal static string BOMMainInfo {
            get {
                return ResourceManager.GetString("BOMMainInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to BOM Manager.
        /// </summary>
        internal static string BOMManager {
            get {
                return ResourceManager.GetString("BOMManager", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PPV/ BOM Qualification.
        /// </summary>
        internal static string BOMPVV {
            get {
                return ResourceManager.GetString("BOMPVV", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Assign BOM.
        /// </summary>
        internal static string BOMSearchAssign {
            get {
                return ResourceManager.GetString("BOMSearchAssign", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Certificate.
        /// </summary>
        internal static string Certificate {
            get {
                return ResourceManager.GetString("Certificate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Certificate Category.
        /// </summary>
        internal static string CertificateCategory {
            get {
                return ResourceManager.GetString("CertificateCategory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Certificate PDF.
        /// </summary>
        internal static string CIPPODPDFTitle {
            get {
                return ResourceManager.GetString("CIPPODPDFTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Client.
        /// </summary>
        internal static string Client {
            get {
                return ResourceManager.GetString("Client", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New BOM.
        /// </summary>
        internal static string ClientBOMAdd {
            get {
                return ResourceManager.GetString("ClientBOMAdd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Client BOM Items.
        /// </summary>
        internal static string ClientBOMItems {
            get {
                return ResourceManager.GetString("ClientBOMItems", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Main Information.
        /// </summary>
        internal static string ClientImportBOMMainInfo {
            get {
                return ResourceManager.GetString("ClientImportBOMMainInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Client Invoice.
        /// </summary>
        internal static string ClientInvoiceAdd {
            get {
                return ResourceManager.GetString("ClientInvoiceAdd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Client Invoice Header.
        /// </summary>
        internal static string ClientInvoiceHeader {
            get {
                return ResourceManager.GetString("ClientInvoiceHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Client Invoice Header Image.
        /// </summary>
        internal static string ClientInvoiceHeaderImage {
            get {
                return ResourceManager.GetString("ClientInvoiceHeaderImage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lines.
        /// </summary>
        internal static string ClientInvoiceLines {
            get {
                return ResourceManager.GetString("ClientInvoiceLines", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Main Information.
        /// </summary>
        internal static string ClientInvoiceMainInfo {
            get {
                return ResourceManager.GetString("ClientInvoiceMainInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Client Invoices.
        /// </summary>
        internal static string ClientInvoices {
            get {
                return ResourceManager.GetString("ClientInvoices", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Contact Log.
        /// </summary>
        internal static string CommunicationLog {
            get {
                return ResourceManager.GetString("CommunicationLog", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Communication Log Type.
        /// </summary>
        internal static string CommunicationLogType {
            get {
                return ResourceManager.GetString("CommunicationLogType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Communication Note.
        /// </summary>
        internal static string CommunicationNotes {
            get {
                return ResourceManager.GetString("CommunicationNotes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Company.
        /// </summary>
        internal static string CompanyAdd {
            get {
                return ResourceManager.GetString("CompanyAdd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer API.
        /// </summary>
        internal static string CompanyApiCustomer {
            get {
                return ResourceManager.GetString("CompanyApiCustomer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Certificate.
        /// </summary>
        internal static string CompanyCertificate {
            get {
                return ResourceManager.GetString("CompanyCertificate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Finance.
        /// </summary>
        internal static string CompanyFinanceInfo {
            get {
                return ResourceManager.GetString("CompanyFinanceInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Global Sales Person Details.
        /// </summary>
        internal static string CompanyGlobalSalesPDetails {
            get {
                return ResourceManager.GetString("CompanyGlobalSalesPDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Company Insurance Certificate.
        /// </summary>
        internal static string CompanyInsuranceCertificate {
            get {
                return ResourceManager.GetString("CompanyInsuranceCertificate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Main Company Information.
        /// </summary>
        internal static string CompanyMainInfo {
            get {
                return ResourceManager.GetString("CompanyMainInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Manufacturers Supplied.
        /// </summary>
        internal static string CompanyManufacturers {
            get {
                return ResourceManager.GetString("CompanyManufacturers", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Prospects Qualification.
        /// </summary>
        internal static string CompanyProspects {
            get {
                return ResourceManager.GetString("CompanyProspects", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Purchasing Information.
        /// </summary>
        internal static string CompanyPurchasingInfo {
            get {
                return ResourceManager.GetString("CompanyPurchasingInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sales Information.
        /// </summary>
        internal static string CompanySalesInfo {
            get {
                return ResourceManager.GetString("CompanySalesInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Company Type.
        /// </summary>
        internal static string CompanyType {
            get {
                return ResourceManager.GetString("CompanyType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Extended Contact Information.
        /// </summary>
        internal static string ContactExtendedInfo {
            get {
                return ResourceManager.GetString("ContactExtendedInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add in Manufacturer Group Code.
        /// </summary>
        internal static string CONTACTGROUP {
            get {
                return ResourceManager.GetString("CONTACTGROUP", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Main Contact Information.
        /// </summary>
        internal static string ContactMainInfo {
            get {
                return ResourceManager.GetString("ContactMainInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Contacts for {0}.
        /// </summary>
        internal static string ContactsForCompany {
            get {
                return ResourceManager.GetString("ContactsForCompany", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Counting Method.
        /// </summary>
        internal static string CountingMethod {
            get {
                return ResourceManager.GetString("CountingMethod", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Country.
        /// </summary>
        internal static string Country {
            get {
                return ResourceManager.GetString("Country", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Credit Note.
        /// </summary>
        internal static string CreditAdd {
            get {
                return ResourceManager.GetString("CreditAdd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lines.
        /// </summary>
        internal static string CreditLines {
            get {
                return ResourceManager.GetString("CreditLines", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Main Information.
        /// </summary>
        internal static string CreditMainInfo {
            get {
                return ResourceManager.GetString("CreditMainInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Credit.
        /// </summary>
        internal static string Credits {
            get {
                return ResourceManager.GetString("Credits", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Customer RMA.
        /// </summary>
        internal static string CRMAAdd {
            get {
                return ResourceManager.GetString("CRMAAdd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lines.
        /// </summary>
        internal static string CRMALines {
            get {
                return ResourceManager.GetString("CRMALines", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Main Information.
        /// </summary>
        internal static string CRMAMainInfo {
            get {
                return ResourceManager.GetString("CRMAMainInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Receiving Information.
        /// </summary>
        internal static string CRMAReceivingInfo {
            get {
                return ResourceManager.GetString("CRMAReceivingInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lines for Receiving.
        /// </summary>
        internal static string CRMAReceivingLines {
            get {
                return ResourceManager.GetString("CRMAReceivingLines", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer RMAs.
        /// </summary>
        internal static string CRMAS {
            get {
                return ResourceManager.GetString("CRMAS", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Receive CRMAs.
        /// </summary>
        internal static string CRMAsReceive {
            get {
                return ResourceManager.GetString("CRMAsReceive", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Log.
        /// </summary>
        internal static string CsvExportHistory {
            get {
                return ResourceManager.GetString("CsvExportHistory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to File Upload History.
        /// </summary>
        internal static string CsvUploadHistory {
            get {
                return ResourceManager.GetString("CsvUploadHistory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to CSV File.
        /// </summary>
        internal static string CSV_Import {
            get {
                return ResourceManager.GetString("CSV_Import", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Currency.
        /// </summary>
        internal static string Currency {
            get {
                return ResourceManager.GetString("Currency", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Exchange Rate History.
        /// </summary>
        internal static string CurrencyRateHistory {
            get {
                return ResourceManager.GetString("CurrencyRateHistory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Requirement.
        /// </summary>
        internal static string CustomerRequirementAdd {
            get {
                return ResourceManager.GetString("CustomerRequirementAdd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Parts Required.
        /// </summary>
        internal static string CustomerRequirementMainInfo {
            get {
                return ResourceManager.GetString("CustomerRequirementMainInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer Requirements.
        /// </summary>
        internal static string CustomerRequirements {
            get {
                return ResourceManager.GetString("CustomerRequirements", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to BOM Import.
        /// </summary>
        internal static string CustomerRequirementsBOMImport {
            get {
                return ResourceManager.GetString("CustomerRequirementsBOMImport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sourcing Results.
        /// </summary>
        internal static string CustomerRequirementSourcingResults {
            get {
                return ResourceManager.GetString("CustomerRequirementSourcingResults", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quote to Client.
        /// </summary>
        internal static string CustomerRequirementSourcingResultsHub {
            get {
                return ResourceManager.GetString("CustomerRequirementSourcingResultsHub", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Print Customer Requirements Enquiry Form.
        /// </summary>
        internal static string CustomerRequirementsPrint {
            get {
                return ResourceManager.GetString("CustomerRequirementsPrint", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Debit Note.
        /// </summary>
        internal static string DebitAdd {
            get {
                return ResourceManager.GetString("DebitAdd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lines.
        /// </summary>
        internal static string DebitLines {
            get {
                return ResourceManager.GetString("DebitLines", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Main Information.
        /// </summary>
        internal static string DebitMainInfo {
            get {
                return ResourceManager.GetString("DebitMainInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Debit.
        /// </summary>
        internal static string Debits {
            get {
                return ResourceManager.GetString("Debits", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Division Members.
        /// </summary>
        internal static string DivisionMembers {
            get {
                return ResourceManager.GetString("DivisionMembers", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Division.
        /// </summary>
        internal static string Divisions {
            get {
                return ResourceManager.GetString("Divisions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Document Footer Messages.
        /// </summary>
        internal static string DocFooters {
            get {
                return ResourceManager.GetString("DocFooters", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Document Header Image.
        /// </summary>
        internal static string DocHeaderImage {
            get {
                return ResourceManager.GetString("DocHeaderImage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ECCN.
        /// </summary>
        internal static string ECCN {
            get {
                return ResourceManager.GetString("ECCN", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Root Cause Category.
        /// </summary>
        internal static string EightDCode {
            get {
                return ResourceManager.GetString("EightDCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Root Cause Sub Category.
        /// </summary>
        internal static string EightDSubCategory {
            get {
                return ResourceManager.GetString("EightDSubCategory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Email Composer.
        /// </summary>
        internal static string EmailComposer {
            get {
                return ResourceManager.GetString("EmailComposer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to End User Undertaking Form.
        /// </summary>
        internal static string EndUserUndertakingForm {
            get {
                return ResourceManager.GetString("EndUserUndertakingForm", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Entertainment Type.
        /// </summary>
        internal static string EntertainmentType {
            get {
                return ResourceManager.GetString("EntertainmentType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to EPR Notify.
        /// </summary>
        internal static string EPRNotify {
            get {
                return ResourceManager.GetString("EPRNotify", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PDF Document.
        /// </summary>
        internal static string EUUPDFTitle {
            get {
                return ResourceManager.GetString("EUUPDFTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Uploaded Documents.
        /// </summary>
        internal static string EXCELTitle {
            get {
                return ResourceManager.GetString("EXCELTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Exchange Rate History.
        /// </summary>
        internal static string ExchangeRateHistory {
            get {
                return ResourceManager.GetString("ExchangeRateHistory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Expedite Notes.
        /// </summary>
        internal static string ExpediteHistory {
            get {
                return ResourceManager.GetString("ExpediteHistory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Export Approval Status.
        /// </summary>
        internal static string ExportApprovalStatus {
            get {
                return ResourceManager.GetString("ExportApprovalStatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Feedback.
        /// </summary>
        internal static string Feedback {
            get {
                return ResourceManager.GetString("Feedback", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Goods In Note.
        /// </summary>
        internal static string GIAdd {
            get {
                return ResourceManager.GetString("GIAdd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Goods In Line Notify.
        /// </summary>
        internal static string GILineNotify {
            get {
                return ResourceManager.GetString("GILineNotify", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lines.
        /// </summary>
        internal static string GILines {
            get {
                return ResourceManager.GetString("GILines", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Main Information.
        /// </summary>
        internal static string GIMainInfo {
            get {
                return ResourceManager.GetString("GIMainInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Master Country List.
        /// </summary>
        internal static string GlobalCountryList {
            get {
                return ResourceManager.GetString("GlobalCountryList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Master Currency List.
        /// </summary>
        internal static string GlobalCurrencyList {
            get {
                return ResourceManager.GetString("GlobalCurrencyList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Global Product Group.
        /// </summary>
        internal static string GlobalProduct {
            get {
                return ResourceManager.GetString("GlobalProduct", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Client Duty Rate.
        /// </summary>
        internal static string GlobalProductDutyRateHistory {
            get {
                return ResourceManager.GetString("GlobalProductDutyRateHistory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Global Product Main Category.
        /// </summary>
        internal static string GlobalProductMainCategory {
            get {
                return ResourceManager.GetString("GlobalProductMainCategory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Global Product Category.
        /// </summary>
        internal static string GlobalProductName {
            get {
                return ResourceManager.GetString("GlobalProductName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Global Group Members.
        /// </summary>
        internal static string GlobalSecurityGroupMembers {
            get {
                return ResourceManager.GetString("GlobalSecurityGroupMembers", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Master Taxes List.
        /// </summary>
        internal static string GlobalTax {
            get {
                return ResourceManager.GetString("GlobalTax", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Master Tax Rate History.
        /// </summary>
        internal static string GlobalTaxRateHistory {
            get {
                return ResourceManager.GetString("GlobalTaxRateHistory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Goods In.
        /// </summary>
        internal static string GoodsIn {
            get {
                return ResourceManager.GetString("GoodsIn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to GROUP CODE MANAGEMENT.
        /// </summary>
        internal static string GroupCodeCompanyAdd {
            get {
                return ResourceManager.GetString("GroupCodeCompanyAdd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Global Sales Access.
        /// </summary>
        internal static string GSA {
            get {
                return ResourceManager.GetString("GSA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to GT.
        /// </summary>
        internal static string GT {
            get {
                return ResourceManager.GetString("GT", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to GT Update Notification.
        /// </summary>
        internal static string GTUpdate {
            get {
                return ResourceManager.GetString("GTUpdate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to RFQ Assignment History.
        /// </summary>
        internal static string HubRFQAssignmentHistory {
            get {
                return ResourceManager.GetString("HubRFQAssignmentHistory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to HUBRFQ Lines.
        /// </summary>
        internal static string HUBRFQLines {
            get {
                return ResourceManager.GetString("HUBRFQLines", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Part Information.
        /// </summary>
        internal static string IHSAdd {
            get {
                return ResourceManager.GetString("IHSAdd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Product Catalogue.
        /// </summary>
        internal static string IHSCatalogue {
            get {
                return ResourceManager.GetString("IHSCatalogue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Images Attached.
        /// </summary>
        internal static string ImagesAttached {
            get {
                return ResourceManager.GetString("ImagesAttached", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Incoterms.
        /// </summary>
        internal static string Incoterm {
            get {
                return ResourceManager.GetString("Incoterm", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Industry Type.
        /// </summary>
        internal static string IndustryType {
            get {
                return ResourceManager.GetString("IndustryType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Internal Purchase Order.
        /// </summary>
        internal static string InternalPOAdd {
            get {
                return ResourceManager.GetString("InternalPOAdd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lines.
        /// </summary>
        internal static string InternalPOLines {
            get {
                return ResourceManager.GetString("InternalPOLines", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Main Information.
        /// </summary>
        internal static string InternalPOMainInfo {
            get {
                return ResourceManager.GetString("InternalPOMainInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Internal Purchase Orders.
        /// </summary>
        internal static string InternalPurchaseOrders {
            get {
                return ResourceManager.GetString("InternalPurchaseOrders", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Invoice.
        /// </summary>
        internal static string InvoiceAdd {
            get {
                return ResourceManager.GetString("InvoiceAdd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invoice Export History.
        /// </summary>
        internal static string InvoiceExportHistory {
            get {
                return ResourceManager.GetString("InvoiceExportHistory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lines.
        /// </summary>
        internal static string InvoiceLines {
            get {
                return ResourceManager.GetString("InvoiceLines", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Deleted Lines.
        /// </summary>
        internal static string InvoiceLinesDeleted {
            get {
                return ResourceManager.GetString("InvoiceLinesDeleted", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Main Information.
        /// </summary>
        internal static string InvoiceMainInfo {
            get {
                return ResourceManager.GetString("InvoiceMainInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invoices.
        /// </summary>
        internal static string Invoices {
            get {
                return ResourceManager.GetString("Invoices", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invoice.
        /// </summary>
        internal static string InvoiceSetting {
            get {
                return ResourceManager.GetString("InvoiceSetting", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Nice Label Path.
        /// </summary>
        internal static string LabelFullPath {
            get {
                return ResourceManager.GetString("LabelFullPath", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Master Status.
        /// </summary>
        internal static string LabelSetup {
            get {
                return ResourceManager.GetString("LabelSetup", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Master Status List.
        /// </summary>
        internal static string LabelSetupItem {
            get {
                return ResourceManager.GetString("LabelSetupItem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Local Currency.
        /// </summary>
        internal static string LocalCurrency {
            get {
                return ResourceManager.GetString("LocalCurrency", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Log History.
        /// </summary>
        internal static string LogHistory {
            get {
                return ResourceManager.GetString("LogHistory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Lot.
        /// </summary>
        internal static string LotAdd {
            get {
                return ResourceManager.GetString("LotAdd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lot Items.
        /// </summary>
        internal static string LotItems {
            get {
                return ResourceManager.GetString("LotItems", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Main Information.
        /// </summary>
        internal static string LotMainInfo {
            get {
                return ResourceManager.GetString("LotMainInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lots.
        /// </summary>
        internal static string Lots {
            get {
                return ResourceManager.GetString("Lots", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mail Group Members.
        /// </summary>
        internal static string MailMessageGroupMembers {
            get {
                return ResourceManager.GetString("MailMessageGroupMembers", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mail Groups.
        /// </summary>
        internal static string MailMessageGroups {
            get {
                return ResourceManager.GetString("MailMessageGroups", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mail Messages.
        /// </summary>
        internal static string MailMessages {
            get {
                return ResourceManager.GetString("MailMessages", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Manufacturer.
        /// </summary>
        internal static string ManufacturerAdd {
            get {
                return ResourceManager.GetString("ManufacturerAdd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Related Companies.
        /// </summary>
        internal static string ManufacturerCompanies {
            get {
                return ResourceManager.GetString("ManufacturerCompanies", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Main Information.
        /// </summary>
        internal static string ManufacturerMainInfo {
            get {
                return ResourceManager.GetString("ManufacturerMainInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Suppliers Who Distribute.
        /// </summary>
        internal static string ManufacturerSuppliers {
            get {
                return ResourceManager.GetString("ManufacturerSuppliers", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to MasterLogin.
        /// </summary>
        internal static string MasterLogin {
            get {
                return ResourceManager.GetString("MasterLogin", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Notes.
        /// </summary>
        internal static string Notes {
            get {
                return ResourceManager.GetString("Notes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to NPR.
        /// </summary>
        internal static string Npr {
            get {
                return ResourceManager.GetString("Npr", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Notify NPR.
        /// </summary>
        internal static string NPRNotify {
            get {
                return ResourceManager.GetString("NPRNotify", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to OGELLicenses.
        /// </summary>
        internal static string OGELLicenses {
            get {
                return ResourceManager.GetString("OGELLicenses", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to OGEL Lines.
        /// </summary>
        internal static string OGELLinesExport {
            get {
                return ResourceManager.GetString("OGELLinesExport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Prospective Cross Selling.
        /// </summary>
        internal static string Orders_ProspectiveCrossSelling {
            get {
                return ResourceManager.GetString("Orders_ProspectiveCrossSelling", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Package.
        /// </summary>
        internal static string Package {
            get {
                return ResourceManager.GetString("Package", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Document File Size.
        /// </summary>
        internal static string PDFDocumentFileSize {
            get {
                return ResourceManager.GetString("PDFDocumentFileSize", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PDF Documents.
        /// </summary>
        internal static string PDFTitle {
            get {
                return ResourceManager.GetString("PDFTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Purchase Order.
        /// </summary>
        internal static string POAdd {
            get {
                return ResourceManager.GetString("POAdd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier Approvals.
        /// </summary>
        internal static string POApprovals {
            get {
                return ResourceManager.GetString("POApprovals", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to POD PDF.
        /// </summary>
        internal static string PODPDFTitle {
            get {
                return ResourceManager.GetString("PODPDFTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sourcing.
        /// </summary>
        internal static string POHubSourcing {
            get {
                return ResourceManager.GetString("POHubSourcing", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lines.
        /// </summary>
        internal static string POLines {
            get {
                return ResourceManager.GetString("POLines", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Main Information.
        /// </summary>
        internal static string POMainInfo {
            get {
                return ResourceManager.GetString("POMainInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Price Request.
        /// </summary>
        internal static string POQuoteAdd {
            get {
                return ResourceManager.GetString("POQuoteAdd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lines.
        /// </summary>
        internal static string POQuoteLines {
            get {
                return ResourceManager.GetString("POQuoteLines", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Main Information.
        /// </summary>
        internal static string POQuoteMainInfo {
            get {
                return ResourceManager.GetString("POQuoteMainInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Receiving Information.
        /// </summary>
        internal static string POReceivingInfo {
            get {
                return ResourceManager.GetString("POReceivingInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lines for Receiving.
        /// </summary>
        internal static string POReceivingLines {
            get {
                return ResourceManager.GetString("POReceivingLines", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to POR.
        /// </summary>
        internal static string PORPDFTitleNew {
            get {
                return ResourceManager.GetString("PORPDFTitleNew", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PPV/ BOM Qualification.
        /// </summary>
        internal static string PPVBOM {
            get {
                return ResourceManager.GetString("PPVBOM", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Print Enquiry Form.
        /// </summary>
        internal static string PrintEnqForm {
            get {
                return ResourceManager.GetString("PrintEnqForm", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Printer.
        /// </summary>
        internal static string Printer {
            get {
                return ResourceManager.GetString("Printer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Product.
        /// </summary>
        internal static string Product {
            get {
                return ResourceManager.GetString("Product", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Duty Rate History.
        /// </summary>
        internal static string ProductDutyRateHistory {
            get {
                return ResourceManager.GetString("ProductDutyRateHistory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Prospective Cross Selling Import Tool.
        /// </summary>
        internal static string ProsCrossSellImportTool {
            get {
                return ResourceManager.GetString("ProsCrossSellImportTool", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Price Request Quotes.
        /// </summary>
        internal static string PRQ {
            get {
                return ResourceManager.GetString("PRQ", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Purchase Orders.
        /// </summary>
        internal static string PurchaseOrders {
            get {
                return ResourceManager.GetString("PurchaseOrders", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Receive Purchase Orders.
        /// </summary>
        internal static string PurchaseOrdersReceive {
            get {
                return ResourceManager.GetString("PurchaseOrdersReceive", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Price Request.
        /// </summary>
        internal static string PurchaseQuotes {
            get {
                return ResourceManager.GetString("PurchaseQuotes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Price Request Line Detail.
        /// </summary>
        internal static string PurchaseRequestLineDetail {
            get {
                return ResourceManager.GetString("PurchaseRequestLineDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Purchase Requisitions.
        /// </summary>
        internal static string PurchaseRequisitions {
            get {
                return ResourceManager.GetString("PurchaseRequisitions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Main Information.
        /// </summary>
        internal static string PurReqMainInfo {
            get {
                return ResourceManager.GetString("PurReqMainInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Quote.
        /// </summary>
        internal static string QuoteAdd {
            get {
                return ResourceManager.GetString("QuoteAdd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lines.
        /// </summary>
        internal static string QuoteLines {
            get {
                return ResourceManager.GetString("QuoteLines", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Main Information.
        /// </summary>
        internal static string QuoteMainInfo {
            get {
                return ResourceManager.GetString("QuoteMainInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quotes.
        /// </summary>
        internal static string Quotes {
            get {
                return ResourceManager.GetString("Quotes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Close Reasons.
        /// </summary>
        internal static string Reason {
            get {
                return ResourceManager.GetString("Reason", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reports.
        /// </summary>
        internal static string Reports {
            get {
                return ResourceManager.GetString("Reports", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Report Parameters.
        /// </summary>
        internal static string ReportVariables {
            get {
                return ResourceManager.GetString("ReportVariables", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Restricted Manufacturer.
        /// </summary>
        internal static string RestrictedManufacture {
            get {
                return ResourceManager.GetString("RestrictedManufacture", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sales Orders.
        /// </summary>
        internal static string SalesOrders {
            get {
                return ResourceManager.GetString("SalesOrders", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ship Sales Orders.
        /// </summary>
        internal static string SalesOrdersShip {
            get {
                return ResourceManager.GetString("SalesOrdersShip", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Group Members.
        /// </summary>
        internal static string SecurityGroupMembers {
            get {
                return ResourceManager.GetString("SecurityGroupMembers", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to General Permissions.
        /// </summary>
        internal static string SecurityGroupPermissionsGeneral {
            get {
                return ResourceManager.GetString("SecurityGroupPermissionsGeneral", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Page Permissions.
        /// </summary>
        internal static string SecurityGroupPermissionsPages {
            get {
                return ResourceManager.GetString("SecurityGroupPermissionsPages", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Report Permissions.
        /// </summary>
        internal static string SecurityGroupPermissionsReports {
            get {
                return ResourceManager.GetString("SecurityGroupPermissionsReports", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Security Groups.
        /// </summary>
        internal static string SecurityGroups {
            get {
                return ResourceManager.GetString("SecurityGroups", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Security Groups.
        /// </summary>
        internal static string SecurityUserGroups {
            get {
                return ResourceManager.GetString("SecurityUserGroups", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Security Users.
        /// </summary>
        internal static string SecurityUsers {
            get {
                return ResourceManager.GetString("SecurityUsers", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select Part.
        /// </summary>
        internal static string SelectPart {
            get {
                return ResourceManager.GetString("SelectPart", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sequence Numbers.
        /// </summary>
        internal static string Sequencer {
            get {
                return ResourceManager.GetString("Sequencer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Service.
        /// </summary>
        internal static string ServiceAdd {
            get {
                return ResourceManager.GetString("ServiceAdd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allocations.
        /// </summary>
        internal static string ServiceAllocations {
            get {
                return ResourceManager.GetString("ServiceAllocations", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Main Information.
        /// </summary>
        internal static string ServiceMainInfo {
            get {
                return ResourceManager.GetString("ServiceMainInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Services.
        /// </summary>
        internal static string Services {
            get {
                return ResourceManager.GetString("Services", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Services for {0}.
        /// </summary>
        internal static string ServicesForLots {
            get {
                return ResourceManager.GetString("ServicesForLots", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shipping.
        /// </summary>
        internal static string Shipping {
            get {
                return ResourceManager.GetString("Shipping", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shipping Methods.
        /// </summary>
        internal static string ShipVia {
            get {
                return ResourceManager.GetString("ShipVia", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Short Shipment.
        /// </summary>
        internal static string ShortShipment {
            get {
                return ResourceManager.GetString("ShortShipment", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Short Shipment Notify.
        /// </summary>
        internal static string ShortShipmentNotify {
            get {
                return ResourceManager.GetString("ShortShipmentNotify", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Sales Order.
        /// </summary>
        internal static string SOAdd {
            get {
                return ResourceManager.GetString("SOAdd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Authorisation Information.
        /// </summary>
        internal static string SOAuthorisation {
            get {
                return ResourceManager.GetString("SOAuthorisation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lines.
        /// </summary>
        internal static string SOLines {
            get {
                return ResourceManager.GetString("SOLines", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Main Information.
        /// </summary>
        internal static string SOMainInfo {
            get {
                return ResourceManager.GetString("SOMainInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SO Payment Files.
        /// </summary>
        internal static string SOPaymentFileList {
            get {
                return ResourceManager.GetString("SOPaymentFileList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer PO Only.
        /// </summary>
        internal static string SORPDFTitle {
            get {
                return ResourceManager.GetString("SORPDFTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SOR.
        /// </summary>
        internal static string SORPDFTitleNew {
            get {
                return ResourceManager.GetString("SORPDFTitleNew", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shipping Information.
        /// </summary>
        internal static string SOShippingInfo {
            get {
                return ResourceManager.GetString("SOShippingInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lines for Shipping.
        /// </summary>
        internal static string SOShippingLines {
            get {
                return ResourceManager.GetString("SOShippingLines", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sourcing.
        /// </summary>
        internal static string Sourcing {
            get {
                return ResourceManager.GetString("Sourcing", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Image (Max {0}) .
        /// </summary>
        internal static string SourcingImages {
            get {
                return ResourceManager.GetString("SourcingImages", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sourcing Links.
        /// </summary>
        internal static string SourcingLinks {
            get {
                return ResourceManager.GetString("SourcingLinks", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Supplier RMA.
        /// </summary>
        internal static string SRMAAdd {
            get {
                return ResourceManager.GetString("SRMAAdd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lines.
        /// </summary>
        internal static string SRMALines {
            get {
                return ResourceManager.GetString("SRMALines", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Main Information.
        /// </summary>
        internal static string SRMAMainInfo {
            get {
                return ResourceManager.GetString("SRMAMainInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier RMAs.
        /// </summary>
        internal static string SRMAS {
            get {
                return ResourceManager.GetString("SRMAS", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shipping Information.
        /// </summary>
        internal static string SRMAShippingInfo {
            get {
                return ResourceManager.GetString("SRMAShippingInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lines for Shipping.
        /// </summary>
        internal static string SRMAShippingLines {
            get {
                return ResourceManager.GetString("SRMAShippingLines", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ship SRMAs.
        /// </summary>
        internal static string SRMAsShip {
            get {
                return ResourceManager.GetString("SRMAsShip", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Main Information.
        /// </summary>
        internal static string SSMainInfo {
            get {
                return ResourceManager.GetString("SSMainInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Star Rating.
        /// </summary>
        internal static string StarRating {
            get {
                return ResourceManager.GetString("StarRating", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Stock.
        /// </summary>
        internal static string Stock {
            get {
                return ResourceManager.GetString("Stock", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Stock Item.
        /// </summary>
        internal static string StockAdd {
            get {
                return ResourceManager.GetString("StockAdd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allocations.
        /// </summary>
        internal static string StockAllocations {
            get {
                return ResourceManager.GetString("StockAllocations", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Stock for {0}.
        /// </summary>
        internal static string StockForLots {
            get {
                return ResourceManager.GetString("StockForLots", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Images.
        /// </summary>
        internal static string StockImages {
            get {
                return ResourceManager.GetString("StockImages", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Log.
        /// </summary>
        internal static string StockLog {
            get {
                return ResourceManager.GetString("StockLog", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Stock Log Reason.
        /// </summary>
        internal static string StockLogReason {
            get {
                return ResourceManager.GetString("StockLogReason", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Main Information.
        /// </summary>
        internal static string StockMainInfo {
            get {
                return ResourceManager.GetString("StockMainInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Related Stock.
        /// </summary>
        internal static string StockRelatedStock {
            get {
                return ResourceManager.GetString("StockRelatedStock", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Related Transactions.
        /// </summary>
        internal static string StockTransactions {
            get {
                return ResourceManager.GetString("StockTransactions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        internal static string String1 {
            get {
                return ResourceManager.GetString("String1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier Invoice.
        /// </summary>
        internal static string SupplierInvoice {
            get {
                return ResourceManager.GetString("SupplierInvoice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Supplier Invoice.
        /// </summary>
        internal static string SupplierInvoiceAdd {
            get {
                return ResourceManager.GetString("SupplierInvoiceAdd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lines.
        /// </summary>
        internal static string SupplierInvoiceLines {
            get {
                return ResourceManager.GetString("SupplierInvoiceLines", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Main Information.
        /// </summary>
        internal static string SupplierInvoiceMainInfo {
            get {
                return ResourceManager.GetString("SupplierInvoiceMainInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Recent Activity.
        /// </summary>
        internal static string TableActivity {
            get {
                return ResourceManager.GetString("TableActivity", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tax.
        /// </summary>
        internal static string Tax {
            get {
                return ResourceManager.GetString("Tax", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Rate History.
        /// </summary>
        internal static string TaxRateHistory {
            get {
                return ResourceManager.GetString("TaxRateHistory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Team.
        /// </summary>
        internal static string Team {
            get {
                return ResourceManager.GetString("Team", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Team Members.
        /// </summary>
        internal static string TeamMembers {
            get {
                return ResourceManager.GetString("TeamMembers", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Terms.
        /// </summary>
        internal static string Terms {
            get {
                return ResourceManager.GetString("Terms", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Warning Messages.
        /// </summary>
        internal static string TextWarningMessage {
            get {
                return ResourceManager.GetString("TextWarningMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to To Do List.
        /// </summary>
        internal static string ToDo {
            get {
                return ResourceManager.GetString("ToDo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to To Do List.
        /// </summary>
        internal static string ToDoList {
            get {
                return ResourceManager.GetString("ToDoList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to To Do List Type.
        /// </summary>
        internal static string ToDoListType {
            get {
                return ResourceManager.GetString("ToDoListType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Transactions.
        /// </summary>
        internal static string Transactions {
            get {
                return ResourceManager.GetString("Transactions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Uploaded Excel Files.
        /// </summary>
        internal static string UploadedExcelFiles {
            get {
                return ResourceManager.GetString("UploadedExcelFiles", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to User.
        /// </summary>
        internal static string User {
            get {
                return ResourceManager.GetString("User", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Preferences.
        /// </summary>
        internal static string UserPreferences {
            get {
                return ResourceManager.GetString("UserPreferences", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Profile.
        /// </summary>
        internal static string UserProfile {
            get {
                return ResourceManager.GetString("UserProfile", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Utility Alternative Import.
        /// </summary>
        internal static string UtilityAlternativeImport {
            get {
                return ResourceManager.GetString("UtilityAlternativeImport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Utility BOM Import.
        /// </summary>
        internal static string UtilityBOMImport {
            get {
                return ResourceManager.GetString("UtilityBOMImport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to BOM Manager Import.
        /// </summary>
        internal static string UtilityBOMManagerImport {
            get {
                return ResourceManager.GetString("UtilityBOMManagerImport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Strategic Offers Import Tool.
        /// </summary>
        internal static string UtilityHUBOfferImport {
            get {
                return ResourceManager.GetString("UtilityHUBOfferImport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bulk Offer Scheduled Import.
        /// </summary>
        internal static string UtilityHUBOfferImportLarge {
            get {
                return ResourceManager.GetString("UtilityHUBOfferImportLarge", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Utility Log.
        /// </summary>
        internal static string UtilityLog {
            get {
                return ResourceManager.GetString("UtilityLog", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Offers Import Tool.
        /// </summary>
        internal static string UtilityOfferImport {
            get {
                return ResourceManager.GetString("UtilityOfferImport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Price Quote Import Tool.
        /// </summary>
        internal static string UtilityPriceQuoteImport {
            get {
                return ResourceManager.GetString("UtilityPriceQuoteImport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Prospective Offer Import Tool.
        /// </summary>
        internal static string UtilityProspectiveOfferImport {
            get {
                return ResourceManager.GetString("UtilityProspectiveOfferImport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Utility Stock Import.
        /// </summary>
        internal static string UtilityStockImport {
            get {
                return ResourceManager.GetString("UtilityStockImport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to X-Match Import.
        /// </summary>
        internal static string UtilityXMatchImport {
            get {
                return ResourceManager.GetString("UtilityXMatchImport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Prospective Offers.
        /// </summary>
        internal static string Utility_ProspectiveOffers {
            get {
                return ResourceManager.GetString("Utility_ProspectiveOffers", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reverse Logistics Import Tool.
        /// </summary>
        internal static string Utility_ReverseLogisticsImport {
            get {
                return ResourceManager.GetString("Utility_ReverseLogisticsImport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Warehouse.
        /// </summary>
        internal static string Warehouse {
            get {
                return ResourceManager.GetString("Warehouse", resourceCulture);
            }
        }
    }
}
