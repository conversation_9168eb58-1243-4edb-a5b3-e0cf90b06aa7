//--------------------------------------------------------------------------------------------------------
// RP 10.12.2009:
// - clear cache of related dropdown after any inserts / edits
//Marker     Changed by      Date         Remarks
//[001]      Vinay           11/06/2012   This need to Add Incoterms field in company section
//[002]      Vinay           13/03/2014   EMS Ref No: 104
//[003]      <PERSON>    31-03-2023     [RP-1224] edit fields 
//--------------------------------------------------------------------------------------------------------
using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;

namespace Rebound.GlobalTrader.Site.Controls.Nuggets.Data
{
    [WebService(Namespace = "http://tempuri.org/")]
    [WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    public class CompanyAddresses : Rebound.GlobalTrader.Site.Data.Base
    {

        public override void ProcessRequest(HttpContext context)
        {
            if (base.init(context))
            {
                switch (Action)
                {
                    case "GetAddressList": GetAddressList(); break;
                    case "GetAddress": GetAddress(); break;
                    case "AddNew": AddNew(); break;
                    case "SaveEdit": SaveEdit(); break;
                    case "CeaseAddress": CeaseAddress(); break;
                    case "MakeDefaultShip": MakeDefaultShip(); break;
                    case "MakeDefaultBill": MakeDefaultBill(); break;
                    case "GetCompanyDetailInactive": GetCompanyDetailInactive(); break;
                    default: WriteErrorActionNotFound(); break;
                }
            }
        }

        /// <summary>
        /// Gets the address list
        /// </summary>
        private void GetAddressList()
        {
            List<BLL.Address> lst = BLL.Address.GetListForCompany(ID);
            JsonObject jsn = new JsonObject();
            JsonObject jsnAddresses = new JsonObject(true);
            foreach (BLL.Address ad in lst)
            {
                if (ad.CeaseDate == null)
                {
                    JsonObject jsnAddress = new JsonObject();
                    jsnAddress.AddVariable("ID", ad.CompanyAddressId);
                    jsnAddress.AddVariable("Name", ad.AddressName);
                    jsnAddress.AddVariable("Long", Functions.ReplaceLineBreaks(AddressManager.ToLongString(ad, true)));
                    jsnAddress.AddVariable("DefaultBill", ad.DefaultBilling);
                    jsnAddress.AddVariable("DefaultShip", ad.DefaultShipping);
                    jsnAddresses.AddVariable(jsnAddress);
                    jsnAddress.Dispose(); jsnAddress = null;
                }
            }
            jsn.AddVariable("Addresses", jsnAddresses);
            jsn.AddVariable("DefaultAddress", Functions.ReplaceLineBreaks(AddressManager.ToLongString(Company.GetDefaultBillingAddress(ID))));
            OutputResult(jsn);
            jsn.Dispose();
            jsn = null;
        }

        private void GetCompanyDetailInactive()
        {
            Company cm = Company.GetCompanyDetailInactive(ID);
            if (cm == null)
            {
                WriteErrorDataNotFound();
            }
            else
            {
                JsonObject jsn = null;
                if (cm != null)
                {
                    jsn = new JsonObject();
                    jsn.AddVariable("CompanyId", cm.CompanyId);
                    jsn.AddVariable("Inactive", cm.Inactive);
                }
                cm = null;
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            cm = null;
        }

        /// <summary>
        /// Get specific address
        /// </summary>
        private void GetAddress()
        {
            Address ad = Address.GetForCompany(ID);
            if (ad == null)
            {
                WriteErrorDataNotFound();
            }
            else
            {
                JsonObject jsn = new JsonObject();
                jsn.AddVariable("Name", ad.AddressName);
                jsn.AddVariable("Long", Functions.ReplaceLineBreaks(AddressManager.ToLongString(ad, ("<br />"))));
                jsn.AddVariable("Address1", ad.Line1);
                jsn.AddVariable("Address2", ad.Line2);
                jsn.AddVariable("Address3", ad.Line3);
                jsn.AddVariable("City", ad.City);
                jsn.AddVariable("County", ad.County);
                jsn.AddVariable("CountryID", ad.CountryNo);
                jsn.AddVariable("Country", ad.CountryName);
                jsn.AddVariable("ZIP", ad.ZIP);
                jsn.AddVariable("State", ad.State);
                jsn.AddVariable("ShipVia", ad.ShipViaName);
                jsn.AddVariable("ShipViaNo", ad.ShipViaNo);
                jsn.AddVariable("ShipViaAccount", ad.ShipViaAccount);
                jsn.AddVariable("DefaultBill", ad.DefaultBilling);
                jsn.AddVariable("DefaultShip", ad.DefaultShipping);
                jsn.AddVariable("CeaseDate", ad.CeaseDate);
                jsn.AddVariable("Notes", Functions.ReplaceLineBreaks(ad.Notes));
                /// /// ESMS #14
                jsn.AddVariable("TaxbyAddress", ad.TaxbyAddress);
                jsn.AddVariable("TaxValue", ad.TaxValue);
                // END
                //[001] code start
                jsn.AddVariable("Incoterm", ad.IncotermName);
                jsn.AddVariable("IncotermNo", ad.IncotermNo);
                //[001] code end
                jsn.AddVariable("ShippingNotes", Functions.ReplaceLineBreaks(ad.ShippingNotes));
                jsn.AddVariable("VatNo", ad.VatNo);
                jsn.AddVariable("RNotes", Functions.ReplaceLineBreaks(ad.RecievingNotes));
                jsn.AddVariable("Region", ad.RegionName);
                jsn.AddVariable("RegionNo", ad.RegionNo);
                jsn.AddVariable("DivisionHeaderNo", ad.DivisionHeaderNo);
                jsn.AddVariable("DivisionHeaderName", ad.DivisionHeaderName);


                //[003] start
                jsn.AddVariable("CompanyRegNo", ad.CompanyRegNo);
                jsn.AddVariable("EORINo", ad.EORINo);
                jsn.AddVariable("TelephoneNo", ad.TelephoneNo);
                jsn.AddVariable("LabelTypeNo", ad.LabelTypeNo);
                jsn.AddVariable("LabelTypeName", ad.LabelTypeName);
                //[003] end
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
                ad = null;
            }
        }

        /// <summary>
        /// Add New Address
        /// </summary>
        public void AddNew()
        {
            try
            {
                ClearCompanyAddressDropDownCache();
                int intNewID = Address.Insert(
                    GetFormValue_String("Name")
                    , GetFormValue_String("Line1")
                    , GetFormValue_String("Line2")
                    , GetFormValue_String("Line3")
                    , GetFormValue_String("City")
                    , GetFormValue_String("County")
                    , GetFormValue_String("State")
                    , GetFormValue_Int("Country")
                    , GetFormValue_String("Zip")
                    , LoginID
                    , GetFormValue_NullableInt("Region")
                    , GetFormValue_NullableInt("DivisionHeaderNo")
                );
                if (intNewID > 0)
                {
                    CompanyAddress.Insert(
                        GetFormValue_Int("CMNo")
                        , intNewID
                        , GetFormValue_NullableInt("ShipViaNo")
                        , GetFormValue_String("ShipViaAccount")
                        , GetFormValue_String("Notes")
                        , GetFormValue_String("ShippingNotes")
                        , GetFormValue_NullableInt("TaxbyAddress")
                        , LoginID
                        //[001] code start
                        , GetFormValue_NullableInt("IncotermNo")
                        //[001] code end
                        //[002] code start
                        , GetFormValue_String("VatNo")
                        //[002] code end
                        , GetFormValue_String("RNotes")
                        , GetFormValue_NullableInt("LabelTypeNo")
                    );
                }

                JsonObject jsn = new JsonObject();
                jsn.AddVariable("Result", (intNewID > 0));
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }

        /// <summary>
        /// Save Edit
        /// </summary>
        public void SaveEdit()
        {
            Address ad = null;
            try
            {
                ClearCompanyAddressDropDownCache();
                ad = Address.GetForCompany(ID);
                ad.AddressName = GetFormValue_String("Name");
                ad.Line1 = GetFormValue_String("Line1");
                ad.Line2 = GetFormValue_String("Line2");
                ad.Line3 = GetFormValue_String("Line3");
                ad.County = GetFormValue_String("County");
                ad.City = GetFormValue_String("City");
                ad.State = GetFormValue_String("State");
                ad.CountryNo = GetFormValue_NullableInt("Country", 0);
                ad.ZIP = GetFormValue_String("Zip");
                ad.UpdatedBy = LoginID;
                ad.RegionNo = GetFormValue_NullableInt("Region");
                ad.DivisionHeaderNo = GetFormValue_NullableInt("DivisionHeaderNo");

                //update notes on Company address table
                CompanyAddress ca = CompanyAddress.GetByAddress(ad.AddressId);
                ca.Notes = GetFormValue_String("Notes");
                ca.ShippingNotes = GetFormValue_String("ShippingNotes");
                ca.ShipViaNo = GetFormValue_NullableInt("ShipViaNo");
                ca.ShipViaAccount = GetFormValue_String("ShipViaAccount");
                // ESMS #14
                ca.TaxbyAddress = GetFormValue_NullableInt("TaxbyAddress");
                // End
                //[001] code start
                ca.IncotermNo = GetFormValue_NullableInt("IncotermNo");
                //[001] code end
                ca.UpdatedBy = LoginID;
                ca.VatNo = GetFormValue_String("VatNo");
                ca.RecievingNotes = GetFormValue_String("RNotes");


                //[003] start
                ca.EORINo = GetFormValue_String("EORINo");
                ca.TelephoneNumber = GetFormValue_String("TelephoneNo");
                //[003] end
                ca.LabelTypeNo=  GetFormValue_NullableInt("LabelTypeNo");
                JsonObject jsn = new JsonObject();
                jsn.AddVariable("Result", ad.Update() && ca.Update());
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
            finally
            {
                ad = null;
            }
        }

        /// <summary>
        /// Delete
        /// </summary>
        public void CeaseAddress()
        {
            try
            {
                ClearCompanyAddressDropDownCache();
                JsonObject jsn = new JsonObject();
                bool blnResult = BLL.CompanyAddress.UpdateCeaseDate(ID, LoginID);
                jsn.AddVariable("Result", blnResult);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }

        /// <summary>
        /// Make default shipping
        /// </summary>
        private void MakeDefaultShip()
        {
            try
            {
                CacheManager.ClearStoredDropDown("AddressesForCompany", new object[] { ID });
                JsonObject jsn = new JsonObject();
                bool blnResult = BLL.CompanyAddress.UpdateDefaultShipping(ID, LoginID);
                jsn.AddVariable("Result", blnResult);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }

        /// <summary>
        /// Make default billing
        /// </summary>
        private void MakeDefaultBill()
        {
            try
            {
                ClearCompanyAddressDropDownCache();
                JsonObject jsn = new JsonObject();
                bool blnResult = BLL.CompanyAddress.UpdateDefaultBilling(ID, LoginID);
                jsn.AddVariable("Result", blnResult);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }

        private void ClearCompanyAddressDropDownCache()
        {
            CacheManager.ClearStoredDropDown("AddressesForCompany", new object[] { GetFormValue_Int("CMNo") });
            //Functions.ClearFormCache("1_29917");
            //Functions.ClearFormCache("1");
        }

    }
}