Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.ItemSearch");Rebound.GlobalTrader.Site.Controls.ItemSearch.GoodsIn=function(n){Rebound.GlobalTrader.Site.Controls.ItemSearch.GoodsIn.initializeBase(this,[n]);this._intGlobalClientNo=-1};Rebound.GlobalTrader.Site.Controls.ItemSearch.GoodsIn.prototype={initialize:function(){Rebound.GlobalTrader.Site.Controls.ItemSearch.GoodsIn.callBaseMethod(this,"initialize");this.addSetupData(Function.createDelegate(this,this.doSetupData));this.addGetDataComplete(Function.createDelegate(this,this.doGetDataComplete))},dispose:function(){this.isDisposed||(this._intGlobalClientNo=null,Rebound.GlobalTrader.Site.Controls.ItemSearch.GoodsIn.callBaseMethod(this,"dispose"))},doSetupData:function(){this._objData.set_PathToData("controls/ItemSearch/GoodsIn");this._objData.set_DataObject("GoodsIn");this._objData.set_DataAction("GetData");this._objData.addParameter("AirWayBill",this.getFieldValue("ctlAirWayBill"));this._objData.addParameter("CMName",this.getFieldValue("ctlCompanyName"));this._objData.addParameter("IncludeInvoiced",this.getFieldValue("ctlIncludeInvoiced"));this._objData.addParameter("ReceivedBy",this.getFieldValue("ctlReceivedBy"));this._objData.addParameter("PONoLo",this.getFieldValue_Min("ctlPONo"));this._objData.addParameter("PONoHi",this.getFieldValue_Max("ctlPONo"));this._objData.addParameter("CRMANoLo",this.getFieldValue_Min("ctlCRMANo"));this._objData.addParameter("CRMANoHi",this.getFieldValue_Max("ctlCRMANo"));this._objData.addParameter("GINoLo",this.getFieldValue_Min("ctlGINo"));this._objData.addParameter("GINoHi",this.getFieldValue_Max("ctlGINo"));this._objData.addParameter("DateReceivedFrom",this.getFieldValue("ctlDateReceivedFrom"));this._objData.addParameter("DateReceivedTo",this.getFieldValue("ctlDateReceivedTo"));this._objData.addParameter("GlobalClientNo",this._intGlobalClientNo)},doGetDataComplete:function(){for(var n,i,t=0,r=this._objResult.Results.length;t<r;t++)n=this._objResult.Results[t],i=[n.No,$R_FN.setCleanTextValue(n.CMName),$R_FN.setCleanTextValue(n.AirWayBill),$R_FN.setCleanTextValue(n.Date),n.PONo,n.CRMANo,$R_FN.setCleanTextValue(n.ReceivedBy)],this._tblResults.addRow(i,n.ID,!1),i=null,n=null}};Rebound.GlobalTrader.Site.Controls.ItemSearch.GoodsIn.registerClass("Rebound.GlobalTrader.Site.Controls.ItemSearch.GoodsIn",Rebound.GlobalTrader.Site.Controls.ItemSearch.Base,Sys.IDisposable);