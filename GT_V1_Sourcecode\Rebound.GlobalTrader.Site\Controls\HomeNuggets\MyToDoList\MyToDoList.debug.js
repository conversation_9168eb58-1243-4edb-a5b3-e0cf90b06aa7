///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.HomeNuggets");

Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyToDoList = function(element) { 
	Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyToDoList.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyToDoList.prototype = {
	get_tblMessages: function() { return this._tblMessages; }, 	set_tblMessages: function(v) { if (this._tblMessages !== v)  this._tblMessages = v; }, 

	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyToDoList.callBaseMethod(this, "initialize");
		this.addRefreshEvent(Function.createDelegate(this, this.getData));
	},

	dispose: function() { 
		if (this.isDisposed) return;
		if (this._tblMessages) this._tblMessages.dispose();
		this._tblMessages = null;
		Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyToDoList.callBaseMethod(this, "dispose");
	},
	
	setupLoadingState: function() {
		this._tblMessages.show(false);
		Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyToDoList.callBaseMethod(this, "setupLoadingState");
	},
	
	getData: function() {
		this.setupLoadingState();
		this._tblMessages.clearTable();
		var obj = new Rebound.GlobalTrader.Site.Data();
		obj.set_PathToData("controls/HomeNuggets/MyToDoList");
		obj.set_DataObject("MyToDoList");
		obj.set_DataAction("GetData");
		obj.addParameter("rowcount", this._intRowCount);
		obj.addDataOK(Function.createDelegate(this, this.getDataComplete));
		obj.addError(Function.createDelegate(this, this.homeNuggetDataError));
		obj.addTimeout(Function.createDelegate(this, this.homeNuggetDataError));
		$R_DQ.addToQueue(obj);
		$R_DQ.processQueue();
		obj = null;
	},
	
	getDataComplete: function(args) {
		var result = args._result;
		this.showNoneFoundOrContent(result.Count);
		for (var i = 0; i < result.Items.length; i++) {
			var row = result.Items[i];
			var aryData = [
                $R_FN.setCleanTextValue(row.Due),
				row.IsCompleted ? $RGT_nubButton_ToDo_CusClass(row.ID, row.Item, "") : $RGT_nubButton_ToDo_CusClass(row.ID, row.Item, "todo_red"),
                $RGT_nubButton_Company(row.CompanyNo, row.CompanyName)
			];
			this._tblMessages.addRow(aryData, null);
		}
		this._tblMessages.show(result.Count > 0);
		this.hideLoading();
	}
};

Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyToDoList.registerClass("Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyToDoList", Rebound.GlobalTrader.Site.Controls.HomeNuggets.Base, Sys.IDisposable);
