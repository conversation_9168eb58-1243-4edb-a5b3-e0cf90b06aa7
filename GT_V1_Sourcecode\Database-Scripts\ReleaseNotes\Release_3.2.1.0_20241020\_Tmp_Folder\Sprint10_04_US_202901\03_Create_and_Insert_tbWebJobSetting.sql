﻿
GO
/*===========================================================================================  
TASK			UPDATED BY       DATE				ACTION		DESCRIPTION  
[US-202901]     Phuc Hoang		 11-Sep-2024		CREATE		German Invoice Part 1-Implement monthly Germany exchange rates published by tax authorities
===========================================================================================  
*/
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[tbWebJobSetting]') AND type in (N'U'))
BEGIN
	CREATE TABLE [dbo].[tbWebJobSetting](
		[WebJobSettingID] [int] NOT NULL,
		[SettingItemName] [nvarchar](255) NOT NULL,
		[DefaultValue] [nvarchar](500) NULL,
	 CONSTRAINT [PK_tbWebJobSetting] PRIMARY KEY CLUSTERED 
	(
		[WebJobSettingID] ASC
	)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
	) ON [PRIMARY]

END

GO

IF NOT EXISTS (SELECT 1 FROM dbo.tbWebJobSetting WHERE SettingItemName = 'ExhangeRatePdfFileUrl')
BEGIN
	-- Insert value --
	INSERT INTO dbo.tbWebJobSetting (WebJobSettingID, SettingItemName, DefaultValue) 
	VALUES (1, 'ExhangeRatePdfFileUrl', '')
END
GO