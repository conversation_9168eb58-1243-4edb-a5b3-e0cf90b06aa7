using System;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Reflection;
using System.ComponentModel;
using System.Resources;
using System.Collections.Generic;
using System.Text.RegularExpressions;
using System.Text;
using System.IO;
using System.Web.UI.WebControls;
using System.Collections;
using System.Threading;
using System.Globalization;
using System.Diagnostics;


/// <summary>
/// Functions, static class
/// </summary>
namespace Rebound.GlobalTrader.Site {
	public static partial class Functions {

		/// <summary>
		/// Takes a List and converts it into a javscript array string
		/// </summary>
		/// <param name="lst"></param>
		/// <returns></returns>
		public static string ArrayToJavascriptString(List<string> lst, bool blnUseQuotes) {
			StringBuilder sb = new StringBuilder();
			for (int i = 0; i < lst.Count; i++) {
				if (sb.Length > 0) sb.Append(",");
				sb.Append(string.Format("{0}{1}{0}", (blnUseQuotes) ? @"""" : "", lst[i]));
			}
			return string.Format("[{0}]", sb.ToString());
		}
		public static string ArrayToJavascriptString(List<string> lst) {
			return ArrayToJavascriptString(lst, false);
		}

		/// <summary>
		/// Takes a List and converts it into a javscript array string
		/// </summary>
		/// <param name="lst"></param>
		/// <returns></returns>
		public static string ArrayToSingleString(List<string> lst, string strSeparator) {
			StringBuilder sb = new StringBuilder();
			for (int i = 0; i < lst.Count; i++) {
				if (sb.Length > 0) sb.Append(strSeparator);
				sb.Append(lst[i]);
			}
			return sb.ToString();
		}
		public static string ArrayToSingleString(List<string> lst) {
			return ArrayToSingleString(lst, "||");
		}

		/// <summary>
		/// Converts a string (from javascript) into an array list
		/// </summary>
		/// <param name="strIn"></param>
		/// <param name="strSeparator"></param>
		/// <returns></returns>
		public static Array JavascriptStringToArray(string strIn, string[] strSeparator) {
			if (String.IsNullOrEmpty(strIn)) return new string[] { };
			return strIn.Split(strSeparator, StringSplitOptions.None);
		}
		public static Array JavascriptStringToArray(string strIn) {
			return JavascriptStringToArray(strIn, new string[] { "||" });
		}

        public static string GetPartWithAlternate(string strPart, System.Byte? intAltStatus,System.Boolean IsAlternate)
        {
            string strOut = string.Empty;
            if (!string.IsNullOrEmpty(strPart) && intAltStatus > 0 && IsAlternate==true)
            {
                CustReqAlternative enumDisplayStatus = (CustReqAlternative)intAltStatus;
                strOut += string.Format("{0} ({1})", strPart, Functions.GetGlobalResource("Misc", enumDisplayStatus.ToString()));
            }
            else
            {
              strOut = strPart;
            }
            return strOut;
        }
	}

}