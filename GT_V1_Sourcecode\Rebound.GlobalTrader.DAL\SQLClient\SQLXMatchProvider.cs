﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Rebound.GlobalTrader.DAL.SQLClient
{
    public class SQLXMatchProvider : DatabaseSettingsElement
    {
        public int Login(string UserName, string Password, out string ErrorMessage)
        {
            int ret = 0;
            ErrorMessage = string.Empty;
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_XMatchLogin", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@UserName", SqlDbType.NVarChar).Value = UserName;
                cmd.Parameters.Add("@Password", SqlDbType.NVarChar).Value = Password;
                cmd.Parameters.Add("@ErrorMessage", SqlDbType.NVarChar, 500).Direction = ParameterDirection.Output;
                cn.Open();
                cmd.ExecuteNonQuery();
                ErrorMessage = (string)cmd.Parameters["@ErrorMessage"].Value;
                return ret;
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to login XMatch", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public DataTable GetXMatchMatchingData(DateTime FromDate, DateTime ToDate, string MatchType, int MatchFirstValue, bool ExcludeZero, int numberReturn, string supplierType, int LoginID, int ClinetId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            DataTable dtResult = new DataTable();
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_GetXMatchMatchingData", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@FromDate", SqlDbType.Date).Value = FromDate;
                cmd.Parameters.Add("@ToDate", SqlDbType.Date).Value = ToDate;
                cmd.Parameters.Add("@MatchType", SqlDbType.VarChar, 500).Value = MatchType;
                cmd.Parameters.Add("@MatchFirstValue", SqlDbType.Int).Value = MatchFirstValue;
                cmd.Parameters.Add("@ExcludeZero", SqlDbType.Bit).Value = ExcludeZero;
                //cmd.Parameters.Add("@NumberReturn", SqlDbType.Int).Value = numberReturn;
                //cmd.Parameters.Add("@SupplierType", SqlDbType.NVarChar, 500).Value = supplierType;
                cmd.Parameters.Add("@LoginID", SqlDbType.Int).Value = LoginID;
                cmd.Parameters.Add("@ClinetId", SqlDbType.Int).Value = ClinetId;
                cmd.CommandTimeout = 600;
                cn.Open();
                SqlDataAdapter da = new SqlDataAdapter();
                da.SelectCommand = cmd;
                da.Fill(dtResult);
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to load GetXMatchMatchingData in SQLXMatchProvider", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
            return dtResult;
        }
        public DataTable GetXMatchMatchingDataBOMManager(DateTime FromDate, DateTime ToDate, string MatchType, int MatchFirstValue, int LoginID, int ClinetId, int BOMManagerID, int? CustomerRequirementID, int curPage, int Rpp)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            DataTable dtResult = new DataTable();
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_GetXMatchMatchingDataBOMManager", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@FromDate", SqlDbType.Date).Value = FromDate;
                cmd.Parameters.Add("@ToDate", SqlDbType.Date).Value = ToDate;
                cmd.Parameters.Add("@MatchType", SqlDbType.VarChar, 500).Value = MatchType;
                cmd.Parameters.Add("@MatchFirstValue", SqlDbType.Int).Value = MatchFirstValue;
                cmd.Parameters.Add("@LoginID", SqlDbType.Int).Value = LoginID;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = ClinetId;
                cmd.Parameters.Add("@BOMManagerID", SqlDbType.Int).Value = BOMManagerID;
                cmd.Parameters.Add("@CustomerRequirementID", SqlDbType.Int).Value = CustomerRequirementID;
                cmd.Parameters.Add("@curPage", SqlDbType.Int).Value = curPage;
                cmd.Parameters.Add("@Rpp", SqlDbType.Int).Value = Rpp;
                cmd.CommandTimeout = 0;
                cn.Open();
                SqlDataAdapter da = new SqlDataAdapter();
                da.SelectCommand = cmd;
                da.Fill(dtResult);
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to load GetXMatchMatchingData in SQLXMatchProvider", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
            return dtResult;
        }
        public DataTable GetUserXMatchData(int BOMManagerID, int? SalesXMatchID)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            DataTable dtResult = new DataTable();
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_GetUserXMatchDataBOMManager", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@BOMManagerID", SqlDbType.Int).Value = BOMManagerID;
                cmd.Parameters.Add("@SalesXMatchID", SqlDbType.Int).Value = SalesXMatchID;
                cmd.CommandTimeout = 0;
                cn.Open();
                SqlDataAdapter da = new SqlDataAdapter();
                da.SelectCommand = cmd;
                da.Fill(dtResult);
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to load GetUserXMatchData in SQLXMatchProvider", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
            return dtResult;
        }
        public void SaveUserXmatchData(int? MOQ, int SPQ, int? StockQTY, string DateCode, string LeadTime, int BOMManagerID, int SalesXMatchID)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_SaveUserXmatchData", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 600;
                cmd.Parameters.Add("@MOQ", SqlDbType.Int).Value = MOQ;
                cmd.Parameters.Add("@SPQ", SqlDbType.Int).Value = SPQ;
                cmd.Parameters.Add("@StockQTY", SqlDbType.Int).Value = StockQTY;
                cmd.Parameters.Add("@BOMManagerID", SqlDbType.Int).Value = BOMManagerID;
                cmd.Parameters.Add("@SalesXMatchID", SqlDbType.Int).Value = SalesXMatchID;
                cmd.Parameters.Add("@LeadTime", SqlDbType.NVarChar).Value = LeadTime;
                cmd.Parameters.Add("@DateCode", SqlDbType.NVarChar).Value = DateCode;
                cn.Open();
                cmd.ExecuteNonQuery();

            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to save SaveXMatchHeader", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public DataSet GetOfferAndExcess(int LoginId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            DataSet dsResult = new DataSet();
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_GetOfferAndExcess", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@LoginId", SqlDbType.NVarChar).Value = LoginId;
                cn.Open();
                SqlDataAdapter da = new SqlDataAdapter();
                da.SelectCommand = cmd;
                da.Fill(dsResult);
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to load GetOfferAndExcess in SQLXMatchProvider", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
            return dsResult;
        }

        public DataTable GetXMatchGenrateData(int displayLength, int displayStart, int sortCol, string sortDir, string search, int userid, int clientId, string columnList, string column_Lable, string column_Name)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_GetXMAtchDynamicData", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@displayLength", SqlDbType.Int).Value = displayLength;
                cmd.Parameters.Add("@displayStart", SqlDbType.Int).Value = displayStart;
                cmd.Parameters.Add("@sortCol", SqlDbType.Int).Value = sortCol;
                cmd.Parameters.Add("@sortDir", SqlDbType.NVarChar, 10).Value = sortDir;//[004]  
                cmd.Parameters.Add("@search", SqlDbType.NVarChar, 255).Value = search;//[004]  
                cmd.Parameters.Add("@userid", SqlDbType.Int).Value = userid;//[003]
                cmd.Parameters.Add("@list_Label_name", SqlDbType.NVarChar).Value = column_Lable;
                cmd.Parameters.Add("@list_column_name", SqlDbType.NVarChar).Value = column_Name;
                cmd.Parameters.Add("@SelectColumns", SqlDbType.NVarChar).Value = columnList;
                cmd.Parameters.Add("@clientId", SqlDbType.Int).Value = clientId;//[003]

                //cmd.CommandTimeout = 60;
                cmd.CommandTimeout = 600;

                cn.Open();
                DataSet ds = new DataSet();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);
                //return ds.Tables[0];
                // changes done to catch exception from SP
                if (ds.Tables.Count == 1)   // if exception is thrown
                    return ds.Tables[0];
                else
                    return ds.Tables[ds.Tables.Count - 1];   // if no exception then multiple tables collection return so picking last table
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get GetXMatchGenrateData", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public DataTable GetXMatchAdSearchData(DateTime FromDate, DateTime ToDate, string MatchType, string SearchText, int UserId, int ClientId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            DataTable dtResult = new DataTable();
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_GetXMatchAdSearchData", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@FromDate", SqlDbType.Date).Value = FromDate;
                cmd.Parameters.Add("@ToDate", SqlDbType.Date).Value = ToDate;
                cmd.Parameters.Add("@MatchType", SqlDbType.VarChar, 500).Value = MatchType;
                cmd.Parameters.Add("@SearchText", SqlDbType.VarChar, 500).Value = SearchText;
                cmd.Parameters.Add("@UserId", SqlDbType.NVarChar).Value = UserId;
                cmd.Parameters.Add("@ClientId", SqlDbType.NVarChar).Value = ClientId;
                cmd.CommandTimeout = 0;
                cn.Open();
                SqlDataAdapter da = new SqlDataAdapter();
                da.SelectCommand = cmd;
                da.Fill(dtResult);
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to load GetXMatchAdSearchData in SQLXMatchProvider", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
            return dtResult;
        }
        public void SaveXMatchHeader(string columnList, string insertColumnList, System.Int32? clientId, int? loginId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_SaveXMatchHeader", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 600;
                cmd.Parameters.Add("@SelectColumns", SqlDbType.NVarChar, 3000).Value = columnList;
                cmd.Parameters.Add("@InserColumnList", SqlDbType.NVarChar, 3000).Value = insertColumnList;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@UserId", SqlDbType.Int).Value = loginId;
                cn.Open();
                cmd.ExecuteNonQuery();

            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to save SaveXMatchHeader", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public void saveXMatchImportData(DataTable tempXMatch, DataTable dtData, string originalFilename, string generatedFilename, System.Int32 userId, System.Int32 clientId)
        {
            SqlConnection cn = null;
            SqlCommand cmdcur = null;
            try
            {
                //using (SqlConnection dbConnection = new SqlConnection(this.ConnectionString))
                //{
                //    dbConnection.Open();
                //    if (dbConnection.State == ConnectionState.Open)
                //    {
                //        using (var tran = dbConnection.BeginTransaction(IsolationLevel.ReadCommitted))
                //        {
                //            using (SqlBulkCopy s = new SqlBulkCopy(dbConnection, SqlBulkCopyOptions.Default, tran))
                //            {
                //                s.DestinationTableName = tempXMatch.TableName;
                //                int i = 1;
                //                foreach (var column in dtData.Columns)
                //                {

                //                    if (i <= 15)
                //                    {
                //                        s.ColumnMappings.Add(column.ToString(), "Column" + i.ToString());

                //                    }
                //                    i++;
                //                }

                //                DataColumn ClientId = new DataColumn();
                //                ClientId.DataType = typeof(int);
                //                ClientId.ColumnName = "ClientId";

                //                DataColumn CreatedBy = new DataColumn();
                //                CreatedBy.DataType = typeof(int);
                //                CreatedBy.ColumnName = "CreatedBy";

                //                DataColumn orgFilename = new DataColumn();
                //                orgFilename.DataType = typeof(string);
                //                orgFilename.ColumnName = "OriginalFilename";

                //                DataColumn genFilename = new DataColumn();
                //                genFilename.DataType = typeof(string);
                //                genFilename.ColumnName = "GeneratedFilename";

                //                // Add the columns to the tempStock DataTable
                //                dtData.Columns.Add(ClientId);
                //                dtData.Columns.Add(CreatedBy);
                //                dtData.Columns.Add(orgFilename);
                //                dtData.Columns.Add(genFilename);
                //                foreach (DataRow dr in dtData.Rows)
                //                {
                //                    //  DataRow appendStockfiled = dtData.NewRow();
                //                    dr["ClientId"] = clientId;
                //                    dr["CreatedBy"] = userId;
                //                    dr["OriginalFilename"] = originalFilename;
                //                    dr["GeneratedFilename"] = generatedFilename;

                //                }

                //                dtData.AcceptChanges();

                //                s.ColumnMappings.Add("ClientId", "ClientId");
                //                s.ColumnMappings.Add("CreatedBy", "CreatedBy");
                //                s.ColumnMappings.Add("OriginalFilename", "OriginalFilename");
                //                s.ColumnMappings.Add("GeneratedFilename", "GeneratedFilename");
                //                s.WriteToServer(dtData);
                //                tran.Commit();

                //            }
                //        }

                //    }
                //}
                int fixedColumn = 15;
                //int totalColumn = 25;
                int columnCount = dtData.Columns.Count;
                // rename actual column according to excel file uploaded
                for (int i = 0; i < columnCount; i++)
                {
                    dtData.Columns[i].ColumnName = "Column" + (i + 1).ToString();
                }
                // add rest column to datatable to match fixed column to 15 range
                int leftOver = fixedColumn - columnCount;
                for (int j = 0; j < leftOver; j++)
                {
                    columnCount = columnCount + 1;
                    dtData.Columns.Add("Column" + (columnCount).ToString());
                }

                cn = new SqlConnection(System.Configuration.ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmdcur = new SqlCommand("usp_XMatchBulkUpload", cn);
                cmdcur.CommandType = CommandType.StoredProcedure;
                cmdcur.CommandTimeout = 0;
                cmdcur.Parameters.AddWithValue("@UploadedData", dtData);
                cmdcur.Parameters.AddWithValue("@originalFilename", originalFilename);
                cmdcur.Parameters.AddWithValue("@generatedFilename", generatedFilename);
                cmdcur.Parameters.AddWithValue("@userId", userId);
                cmdcur.Parameters.AddWithValue("@clientId", clientId);
                //cmdcur.Parameters.AddWithValue("@SelectedclientId", SelectedclientId);
                cmdcur.Parameters.AddWithValue("@Inactive", 0);
                //cmdcur.Parameters.AddWithValue("@clientType_con", clientType_con);
                cn.Open();
                cmdcur.ExecuteNonQuery();
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to insert XMatchImport data", sqlex);
            }
            finally
            {
                cmdcur.Dispose();
                cn.Close();
                cn.Dispose();
            }

        }

        public DataTable GetXMatchDetailFromTemp(int displayLength, int displayStart, int sortCol, string sortDir, string search, int userid, int Clientid)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_GetXMatchData", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@displayLength", SqlDbType.Int).Value = displayLength;
                cmd.Parameters.Add("@displayStart", SqlDbType.Int).Value = displayStart;
                cmd.Parameters.Add("@sortCol", SqlDbType.Int).Value = sortCol;
                cmd.Parameters.Add("@sortDir", SqlDbType.NVarChar, 10).Value = sortDir;//[004]  
                cmd.Parameters.Add("@search", SqlDbType.NVarChar, 255).Value = search;//[004]  
                cmd.Parameters.Add("@userid", SqlDbType.Int).Value = userid;//[003]
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = Clientid;//[003]
                //cmd.CommandTimeout = 60;
                cmd.CommandTimeout = 900;
                cn.Open();
                DataSet ds = new DataSet();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);
                return ds.Tables[0];
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get GetXMatchDetailFromTemp", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public DataTable GetXMatchHeader(System.Int32 clientId, System.Int32 userId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                // cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_GetXMatchHeader", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                //cmd.CommandTimeout = 60;
                cmd.CommandTimeout = 600;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@UserId", SqlDbType.Int).Value = userId;
                cn.Open();
                DataSet ds = new DataSet();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);
                return ds.Tables[0];
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get GetXMatchHeader", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public DataTable GetExcelHeaderTemp(System.Int32 clientId, System.Int32 userId)//GetExcelHeaderFrom
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_GetXMatchHeaderColumn", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                //cmd.CommandTimeout = 60;
                cmd.CommandTimeout = 600;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@UserId", SqlDbType.Int).Value = userId;
                cn.Open();
                DataSet ds = new DataSet();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);
                return ds.Tables[0];
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get GetExcelHeaderTemp", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public int SaveXMatchImportData(int userId, int clientId, string column_Lable, string column_Name, string insertDataList, string fileColName, out string errorMessage, bool IsDelExistingData)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            errorMessage = string.Empty;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_SaveXMatchImportData", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 600;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@UserId", SqlDbType.Int).Value = userId;
                cmd.Parameters.Add("@list_Label_name", SqlDbType.NVarChar).Value = column_Lable;
                cmd.Parameters.Add("@list_column_name", SqlDbType.NVarChar).Value = column_Name;
                cmd.Parameters.Add("@insertDataList", SqlDbType.NVarChar).Value = insertDataList;
                cmd.Parameters.Add("@fileColName", SqlDbType.NVarChar).Value = fileColName;
                cmd.Parameters.Add("@RecordCount", SqlDbType.Int).Direction = ParameterDirection.Output;
                cmd.Parameters.Add("@ErrorMessage", SqlDbType.VarChar, 400).Direction = ParameterDirection.Output;
                cmd.Parameters.Add("@IsDelExistingData", SqlDbType.Bit).Value = IsDelExistingData;
                cn.Open();
                cmd.ExecuteNonQuery();
                errorMessage = Convert.ToString(cmd.Parameters["@ErrorMessage"].Value);
                return (Int32)cmd.Parameters["@RecordCount"].Value;


            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to insert mapping Column detail", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public int DeleteXMatchRecord(int userId, int clientId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_deleteTempXMatchData", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 600;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@UserId", SqlDbType.Int).Value = userId;
                cn.Open();
                return cmd.ExecuteNonQuery();

            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to DeleteXMatchRecord", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public DataTable GetXMatchExcessData(int loginID, int clientID)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_GetXMatchExcessData", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                //cmd.CommandTimeout = 60;
                cmd.CommandTimeout = 600;
                cmd.Parameters.Add("@UserId", SqlDbType.Int).Value = loginID;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientID;
                cn.Open();
                DataSet ds = new DataSet();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);
                return ds.Tables[0];
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get GetXMatchExcessData", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public int DeleteExcessRecord(int LoginId, int ClientId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_DeleteXMatchExcessRecord", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 600;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = ClientId;
                cmd.Parameters.Add("@UserId", SqlDbType.Int).Value = LoginId;
                cn.Open();
                return cmd.ExecuteNonQuery();

            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to DeleteExcelRecord", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public int GetExcessRecord(int UserId, int ClientId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_GetXMathExcessRecord", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 600;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = ClientId;
                cmd.Parameters.Add("@UserId", SqlDbType.Int).Value = UserId;
                cn.Open();
                return (int)cmd.ExecuteScalar();

            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to GetExcessRecord", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public DataTable GetXMatchClient(int ClientId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_GetXMatchClient", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                //cmd.CommandTimeout = 60;
                cmd.CommandTimeout = 600;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = ClientId;
                cn.Open();
                DataSet ds = new DataSet();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);
                return ds.Tables[0];
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to get GetXMatchClient", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public DataTable GetSupplierTrustedOfferXMatch(int ClientType, int ClientId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_GetSupplierTrustedOfferXMatch", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                //cmd.CommandTimeout = 60;
                cmd.CommandTimeout = 600;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = ClientId;
                cmd.Parameters.Add("@ClientType", SqlDbType.Int).Value = ClientType;
                cn.Open();
                DataSet ds = new DataSet();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);
                return ds.Tables[0];
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to get GetSupplierTrustedOfferXMatch", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public DataTable ProcessGTMatchData(string ExComp, string OfComp, string DocType, DateTime FromDate, int ClientId, int Months)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_ProcessGTMatchData", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                //cmd.CommandTimeout = 60;
                cmd.CommandTimeout = 600;
                cmd.Parameters.Add("@ExComp", SqlDbType.NVarChar, 500).Value = ExComp;
                cmd.Parameters.Add("@OfComp", SqlDbType.NVarChar, 500).Value = OfComp;
                cmd.Parameters.Add("@DocType", SqlDbType.NVarChar, 500).Value = DocType;
                cmd.Parameters.Add("@FromDate", SqlDbType.DateTime).Value = FromDate;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = ClientId;
                cmd.Parameters.Add("@Months", SqlDbType.Int).Value = Months;
                cn.Open();
                DataSet ds = new DataSet();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);
                return ds.Tables[0];
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to ProcessGTMatchData", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
    }
}
