﻿<%@ Control Language="C#" CodeBehind="SourcingBulkEditLog.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.ItemSearch.SourcingBulkEditLog" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_ItemSearch:DesignBase id="ctlDB" runat="server" InitialSortDirection="DESC">
    <FieldsLeft>
        <ReboundUI_FilterDataItemRow:TextBox id="ctlPartNo" runat="server" ResourceTitle="PartNo" />
		<ReboundUI_FilterDataItemRow:DropDown id="ctlEditedBy" runat="server" ResourceTitle="Salesman" DropDownType="Employee" DropDownAssembly="Rebound.GlobalTrader.Site" />
    </FieldsLeft>
    <FieldsRight>
        <ReboundUI_FilterDataItemRow:DateSelect id="ctlEditedDateFrom" runat="server"  ResourceTitle="EditedDateFrom" />
		<ReboundUI_FilterDataItemRow:DateSelect id="ctlEditedDateTo" runat="server" ResourceTitle="EditedDateTo" />
    </FieldsRight>
</ReboundUI_ItemSearch:DesignBase>