﻿/*
=========================================================================================================================================================
TASK			UPDATED BY       DATE				ACTION		DESCRIPTION  
[US-223401]     Phuc Hoang		 20-Feb-2025		CREATE		IPO - Bulk Offer Import tool - Apply fuzzy matching and rationalize data before importing
=========================================================================================================================================================
*/
CREATE OR ALTER  PROCEDURE [dbo].usp_select_GTVendorMFR_tbOfferImportByExcelTemp    
 @OfferedIds NVARCHAR(255)
AS  
BEGIN  
	SELECT OfferTempId
			,MPN
			,MFR
			,COST
			,LeadTime
			,SPQ
			,MOQ
			,Remarks
			,OfferedDate
			,Vendor
			,ClientNo
			,DLUP
			,GeneratedFileName
			,HUBOfferImportLargeFileID
	,COALESCE(
		(SELECT TOP 1 ManufacturerName FROM tbManufacturer manu WHERE MFR = manu.ManufacturerName AND manu.Inactive = 0),
		(SELECT TOP 1 ManufacturerName FROM tbManufacturer manu WHERE [BorisGlobalTrader].[dbo].[ufn_GetFirstWord](manu.ManufacturerName) = [BorisGlobalTrader].[dbo].[ufn_GetFirstWord](MFR) AND manu.Inactive = 0)
	)as GTMFR,
	COALESCE(
		(SELECT TOP 1 CompanyName FROM tbCompany cmp WHERE Vendor = cmp.CompanyName AND  cmp.ClientNo = 114 AND cmp.IsSupplier = 1),
		(SELECT TOP 1 CompanyName FROM tbCompany cmp WHERE [BorisGlobalTrader].[dbo].[ufn_GetFirstWord](cmp.CompanyName) = [BorisGlobalTrader].[dbo].[ufn_GetFirstWord](Vendor) AND  cmp.ClientNo = 114 AND cmp.IsSupplier = 1)
	) as GTVendor
	from BorisGlobalTraderimports.dbo.tbOfferImportByExcelTemp
	WHERE OfferTempId IN (SELECT value FROM STRING_SPLIT(@OfferedIds, ','));
	
END