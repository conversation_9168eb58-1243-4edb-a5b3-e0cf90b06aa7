﻿using Rebound.GlobalTrader.DAL;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;

namespace Rebound.GlobalTrader.BLL
{
    public partial class ProspectiveOffer: BizObject
    {
        #region Properties
        protected static DAL.ProspectiveOfferElement Settings
        {
            get { return Globals.Settings.ProspectiveOffers; }
        }

        public int ProspectiveOfferId { get; set; }
        public int SupplierId { get; set; }
        public string SupplierName { get; set; }
        public string SourceFileName { get; set; }
        public int ImportRowCount { get; set; }
        public string ImportStatus { get; set; }
        public string ImportedBy { get; set; }
        public DateTime ImportDate { get; set; }  
        public int RowCnt { get; set; }

        #endregion
        #region Methods

        public static List<ProspectiveOffer> DataListNugget(int? teamId,
                                                            int? divisionId,
                                                            int? loginId,
                                                            int? orderBy,
                                                            int? sortDir,
                                                            int? pageIndex,
                                                            int? pageSize,
                                                            string fileNameSearch,
                                                            string partSearch,
                                                            DateTime? dateUploadForm,
                                                            DateTime? dateUploadTo,
                                                            int? importedBy,
                                                            string supplierSearch)
        {
            List<ProspectiveOfferDetails> listDetails = SiteProvider.ProspectiveOffer.DataListNugget(teamId, divisionId, loginId, orderBy, sortDir, pageIndex, pageSize, fileNameSearch, partSearch, dateUploadForm, dateUploadTo, importedBy, supplierSearch);
            if(listDetails == null)
            {
                return new List<ProspectiveOffer>();
            }
            List<ProspectiveOffer> list = new List<ProspectiveOffer>();
            foreach (var offerDetails in listDetails)
            {
                var offer = new ProspectiveOffer
                {
                    ProspectiveOfferId = offerDetails.ProspectiveOfferId,
                    SupplierId = offerDetails.SupplierId,
                    SupplierName = offerDetails.SupplierName,
                    SourceFileName = offerDetails.SourceFileName,
                    ImportRowCount = offerDetails.ImportRowCount,
                    ImportStatus = offerDetails.ImportStatus,
                    ImportedBy = offerDetails.ImportedBy,
                    ImportDate = offerDetails.ImportDate,
                    RowCnt = offerDetails.RowCnt
                };
                list.Add(offer);
            }
            return list;
        }

        public static void SaveImportColumnHeader(string columnList, string insertColumnList, int loginId)
        {
            SiteProvider.ProspectiveOffer.SaveImportColumnHeader(columnList, insertColumnList, loginId);
        }

        public static void SaveBulkImportData(DataTable dtData, string originalFilename, string generatedFilename, int loginId)
        {
            SiteProvider.ProspectiveOffer.SaveBulkImportData(dtData, originalFilename, generatedFilename, loginId);
        }

        public static DataTable GetRawDataHeader(int loginId)
        {
            return SiteProvider.ProspectiveOffer.GetRawDataHeader(loginId);
        }

        public static DataTable GetProspectiveOfferRawData(int displayLength, int displayStart, int sortCol, string sortDir, int userId)
        {
            return SiteProvider.ProspectiveOffer.GetProspectiveOfferRawData(displayLength, displayStart, sortCol, sortDir, userId);
        }

        public static DataTable GetSupplierMappedColumn(int supplierId)
        {
            return SiteProvider.ProspectiveOffer.GetSupplierMappedColumn(supplierId);
        }

        public static void SaveSupplierColumnMapping(int supplierId,
                                               string manufacturer,
                                               string part,
                                               string quantity,
                                               string price,
                                               string description,
                                               string alterPart,
                                               string datecode,
                                               string product,
                                               string package,
                                               string rohs,
                                               string supplierPart,
                                               int currencyNo, 
                                               int loginNo)
        {
            SiteProvider.ProspectiveOffer.SaveSupplierColumnMapping(supplierId,
                                                                    manufacturer,
                                                                    part,
                                                                    quantity,
                                                                    price,
                                                                    description,
                                                                    alterPart,
                                                                    datecode,
                                                                    product,
                                                                    package,
                                                                    rohs,
                                                                    supplierPart,
                                                                    currencyNo,
                                                                    loginNo);
        }

        public static DataTable ValidateImportData(int supplierId, int loginId)
        {
            return SiteProvider.ProspectiveOffer.ValidateImportData(supplierId, loginId);
        }

        public static int ImportProspectiveOffers(int supplierId, int loginId, out string outputMessage)
        {
            return SiteProvider.ProspectiveOffer.ImportProspectiveOffers(supplierId, loginId, out outputMessage);
        }

        public static ProspectiveOfferDetails GetStatus(int proId)
        {
            return SiteProvider.ProspectiveOffer.GetStatus(proId);
        }

        public static List<ProspectiveOffersLogs> GetProspectiveOffersLogs(int prospectiveOfferLineId)
        {
            return SiteProvider.ProspectiveOffer.GetProspectiveOffersLogs(prospectiveOfferLineId);
        }
        
            public static ProspectiveOfferLinesOffer GetProspectiveOfferLineByID(int prospectiveOfferLineId)
        {
            return SiteProvider.ProspectiveOffer.GetProspectiveOfferLineByID(prospectiveOfferLineId);
        }
        public static List<ProspectiveOffersLogs> GetProspectiveOffersLogsSentDate(int prospectiveOfferLineId, List<int> customerRequirementId)
        {
            return SiteProvider.ProspectiveOffer.GetProspectiveOffersLogsSentDate(prospectiveOfferLineId, customerRequirementId);
        }
        public static List<ProspectiveOfferLines> GetProspectiveOfferLines(int proId, int curPage, int rpp)
        {
            var data = SiteProvider.ProspectiveOffer.GetProspectiveOfferLines(proId, curPage, rpp);
            return data.OrderBy(x => x.ProspectiveOfferLineId).ToList();
        }

        public static List<ProspectiveOfferLines> GetGTOffersDetail(int proId, List<int> proLineIds, int monthRange, List<string> mfrs, int minOfferQty, int minSOVal, int curPage, int rpp)
        {
            var data = SiteProvider.ProspectiveOffer.GetGTOffersDetail(proId, proLineIds, monthRange, minOfferQty, curPage, rpp);

            foreach (var ln in data)
            {
                double dblOrderValueInBaseCurrency = 0;
                DateTime currencyDate = ln.SODate != null ? (DateTime)ln.SODate : DateTime.Now;
                ln.GTReqCount = data.Count(x => x.IsFromGT == 1 && x.PartNo == ln.PartNo);
                double dblLineTotal = (double)ln.SOQTY * (double)ln.SOPrice;
                double dblLineTax = ln.IsLineTaxable ? dblLineTotal * ((double)ln.SOTaxRate / 100) : 0;
                dblOrderValueInBaseCurrency += BLL.Currency.ConvertValueBetweenTwoCurrencies((dblLineTotal + dblLineTax), ln.CurrencyNo, ln.CurrencyNo, currencyDate);
                ln.SOLineValue = dblOrderValueInBaseCurrency;
            }
            data = data.Where(x => x.SOLineValue >= minSOVal).ToList();
            if (mfrs != null && mfrs.Count() > 0)
            {
                data = data.Where(x => mfrs.Contains(x.Manufacturer.ToUpper())).ToList();
            }
            return data.OrderBy(x => x.ProspectiveOfferLineId).ToList();
        }

        public static int? CloneOffer(int proId, int proLineId, int gtOfferId, int bomId, int? clientId, int currencyNo)
        {
            int newExistingHUBRFQ = SiteProvider.ProspectiveOffer.GetNewHUBRFQ(proId, proLineId, gtOfferId, bomId, clientId);
            if (newExistingHUBRFQ != 0)
            {
                return newExistingHUBRFQ;
            }
            else
            {
                ProspectiveOfferCustomerRequirement cusReq = SiteProvider.ProspectiveOffer.GetExistingHUBRFQ(gtOfferId);
                ProspectiveOfferLines line = SiteProvider.ProspectiveOffer.GetProspectiveOfferLineById(proLineId);
                int newCusReqId = SiteProvider.ProspectiveOffer.CloneCustomerRequirement(cusReq.CustomerRequirementId, currencyNo);

                var bom = new BOM();
                bom.ClientNo = cusReq.ClientNo;
                bom.BOMName = "HUBRFQ Clone " + newCusReqId + "-" + cusReq.ClientNo.ToString();
                bom.CompanyNo = cusReq.CompanyNo;
                bom.ContactNo = cusReq.Salesman;
                bom.Inactive = false;
                bom.Notes = "";

                bom.UpdateRequirement = 0;
                bom.Status = (int)Rebound.GlobalTrader.BLL.BOMStatus.List.New;
                bom.CurrencyNo = cusReq.CurrencyNo;
                bom.CurrentSupplier = "";
                bom.QuoteRequired = DateTime.Now;
                bom.UpdatedBy = cusReq.Salesman;
                bom.AS9120 = true;
                bom.Contact2Id = null;
                bom.BOMId = bom.Insert();

                if (bom.BOMId > 0)
                {
                    SiteProvider.ProspectiveOffer.CloneHUBRFQOffer(proId, bom.BOMId, gtOfferId, newCusReqId);
                    CustomerRequirement newCusReq = CustomerRequirement.Get(newCusReqId);
                    System.Int32? offerID = -1;

                    BLL.SourcingResult.InsertProspectiveSourcingResult(newCusReqId, proLineId, line.PartNo, line.ManufacturerNo, line.DateCode, line.ProductNo, line.PackageNo, line.QuantityOffered,
                         newCusReq.Salesman, line.SupplierNo, line.CurrencyNo, newCusReq.POHubCompany, cusReq.ProspectiveOfferPrice, out offerID);

                    return bom.BOMId;
                }
                else
                {
                    return null;
                }
            }
        }
        public static void UpdateProspectiveOfferLine(int proId, List<int> proLineIds, List<ProspectiveOfferLines> gtOfferIds)
        {
            SiteProvider.ProspectiveOffer.UpdateProspectiveOfferLine(proId, proLineIds, gtOfferIds);
        }

        public static void InsertProspectiveOfferLogs(int proLineId, int customerRequirementId)
        {
            SiteProvider.ProspectiveOffer.InsertProspectiveOfferLogs(proLineId, customerRequirementId);
        }

        public static void UpdateProspectiveOfferManufacturer(int proId, int proLineId, int manufacturerNo)
        {
            SiteProvider.ProspectiveOffer.UpdateProspectiveOfferManufacturer(proId, proLineId, manufacturerNo);
        }
        #endregion
    }
}
