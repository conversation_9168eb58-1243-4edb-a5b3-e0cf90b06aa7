using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;
using Rebound.GlobalTrader.Site.Enumerations;

namespace Rebound.GlobalTrader.Site.Controls.ItemSearch.Data {
    [WebService(Namespace = "http://tempuri.org/")]
    [WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    public class GoodsIn : Rebound.GlobalTrader.Site.Data.ItemSearch.Base {
	
		protected override void GetData() {
			List<BLL.GoodsIn> lst = null;
            try {
                int? intGlobalClientNo;
                intGlobalClientNo = GetFormValue_NullableInt("GlobalClientNo");
                lst = BLL.GoodsIn.ItemSearch(
                    (intGlobalClientNo.HasValue && intGlobalClientNo.Value > 0) ? intGlobalClientNo.Value : SessionManager.ClientID
                    , GetFormValue_NullableInt("Order", 0)
                    , GetFormValue_NullableInt("SortDir", SortColumnDirection.ASC)
                    , GetFormValue_NullableInt("PageIndex", 0)
                    , GetFormValue_NullableInt("PageSize", 10)
                    , GetFormValue_String("AirWayBill")
                    //, GetFormValue_StringForNameSearch("CMName")
                    , GetFormValue_StringForNameSearchDecode("CMName")
                    , GetFormValue_NullableInt("ReceivedBy")
                    , GetFormValue_Boolean("IncludeInvoiced")
                    , GetFormValue_NullableInt("PONoLo")
                    , GetFormValue_NullableInt("PONoHi")
                    , GetFormValue_NullableInt("CRMANoLo")
                    , GetFormValue_NullableInt("CRMANoHi")
                    , GetFormValue_NullableInt("GINoLo")
                    , GetFormValue_NullableInt("GINoHi")
                    , GetFormValue_NullableDateTime("DateReceivedFrom")
                    , GetFormValue_NullableDateTime("DateReceivedTo")
                    ,(intGlobalClientNo.HasValue && intGlobalClientNo.Value > 0) ? true :false
                    );
                JsonObject jsn = new JsonObject();
                JsonObject jsnItems = new JsonObject(true);
                for (int i = 0; i < lst.Count; i++) {
                    JsonObject jsnItem = new JsonObject();
                    jsnItem.AddVariable("ID", lst[i].GoodsInId);
                    jsnItem.AddVariable("No", lst[i].GoodsInNumber);
                    jsnItem.AddVariable("CMNo", lst[i].CompanyNo);
                    jsnItem.AddVariable("CMName", lst[i].CompanyName);
                    jsnItem.AddVariable("Date", Functions.FormatDate(lst[i].DateReceived));
                    jsnItem.AddVariable("PONo", lst[i].PurchaseOrderNumber);
                    jsnItem.AddVariable("CRMANo", lst[i].CustomerRMANumber);
                    jsnItem.AddVariable("AirWayBill", lst[i].AirWayBill);
                    jsnItem.AddVariable("ReceivedBy", lst[i].ReceiverName);
                    jsnItem.AddVariable("Reference", lst[i].Reference);
                    jsnItems.AddVariable(jsnItem);
                    jsnItem.Dispose();
                    jsnItem = null;
                }
                jsn.AddVariable("Count", (lst.Count > 0) ? lst[0].RowCnt : 0);
                jsn.AddVariable("Results", jsnItems);
                OutputResult(jsn);
                jsnItems.Dispose();
                jsnItems = null;
                jsn.Dispose();
                jsn = null;
            } catch (Exception ex) {
                WriteError(ex);
            } finally {
                lst = null;
            }
			base.GetData();
        }
    }
}
