///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//----------------------------------------------------------------------------------------------------------------
// RP 11.03.2011:
// - only set Contact and Company name if there is something to set
//
// RP 06.01.2010:
// - fully dispose everything
//
// RP 18.12.2009:
// - allow passing a company name to initially search for (task 357)
//
// RP 04.12.2009:
// - allow sources for new lines to be set on permissions
//
// RP 16.10.2009:
// - retrofit changes from v3.0.34
//Marker     Changed by      Date         Remarks
//[001]      Vinay           26/07/2012   Add compulsory incoterms field when create Credit and debit note. :ESMS No:- 105
//[002]      Vinay           29/08/2012   Get only exported invoice, when create credit note from invoice
//[003]      Vinay           30/10/2012   Add link in the Invoice section to create CRMA and Credit
//----------------------------------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.CreditAdd_Add = function (element) {
    Rebound.GlobalTrader.Site.Controls.Forms.CreditAdd_Add.initializeBase(this, [element]);
    this._intNewID = 0;
    this._intCompanyID = 0;
    this._intLoginID = 0;
    this._intContactID = 0;
    this._intDivisionID = 0;
    this._intInvoiceID = 0;
    this._intCRMAID = 0;
    this._intSalesmanID = 0;
    this._intCurrencyID = 0;
    this._intTaxID = 0;
    this._strCompanyName = "";
    this._strReferenceDate = "";
    this._strSearchCompanyName = "";
    this._intClientInvoiceID = 0;
    this._intClientInvoiceLineNo = 0;
    this._isClientInvoice = false;
    this._intDivisionHeaderNo = 0;
    

};

Rebound.GlobalTrader.Site.Controls.Forms.CreditAdd_Add.prototype = {

    get_intCompanyID: function () { return this._intCompanyID; }, set_intCompanyID: function (v) { if (this._intCompanyID !== v) this._intCompanyID = v; },
    get_strCompanyName: function () { return this._strCompanyName; }, set_strCompanyName: function (v) { if (this._strCompanyName !== v) this._strCompanyName = v; },
    get_intLoginID: function () { return this._intLoginID; }, set_intLoginID: function (v) { if (this._intLoginID !== v) this._intLoginID = v; },
    get_intContactID: function () { return this._intContactID; }, set_intContactID: function (v) { if (this._intContactID !== v) this._intContactID = v; },
    get_strContactName: function () { return this._strContactName; }, set_strContactName: function (v) { if (this._strContactName !== v) this._strContactName = v; },
    get_ibtnSend: function () { return this._ibtnSend; }, set_ibtnSend: function (v) { if (this._ibtnSend !== v) this._ibtnSend = v; },
    get_ibtnSend_Footer: function () { return this._ibtnSend_Footer; }, set_ibtnSend_Footer: function (v) { if (this._ibtnSend_Footer !== v) this._ibtnSend_Footer = v; },
    get_ibtnContinue: function () { return this._ibtnContinue; }, set_ibtnContinue: function (v) { if (this._ibtnContinue !== v) this._ibtnContinue = v; },
    get_ibtnContinue_Footer: function () { return this._ibtnContinue_Footer; }, set_ibtnContinue_Footer: function (v) { if (this._ibtnContinue_Footer !== v) this._ibtnContinue_Footer = v; },
    get_radSelectSource: function () { return this._radSelectSource; }, set_radSelectSource: function (v) { if (this._radSelectSource !== v) this._radSelectSource = v; },
    get_ctlSelectFromInvoice: function () { return this._ctlSelectFromInvoice; }, set_ctlSelectFromInvoice: function (v) { if (this._ctlSelectFromInvoice !== v) this._ctlSelectFromInvoice = v; },
    get_trSelectFromInvoice: function () { return this._trSelectFromInvoice; }, set_trSelectFromInvoice: function (v) { if (this._trSelectFromInvoice !== v) this._trSelectFromInvoice = v; },
    get_ctlSelectFromCRMA: function () { return this._ctlSelectFromCRMA; }, set_ctlSelectFromCRMA: function (v) { if (this._ctlSelectFromCRMA !== v) this._ctlSelectFromCRMA = v; },
    get_trSelectFromCRMA: function () { return this._trSelectFromCRMA; }, set_trSelectFromCRMA: function (v) { if (this._trSelectFromCRMA !== v) this._trSelectFromCRMA = v; },
    get_lblCurrency_Freight: function () { return this._lblCurrency_Freight; }, set_lblCurrency_Freight: function (v) { if (this._lblCurrency_Freight !== v) this._lblCurrency_Freight = v; },
    get_arySources: function () { return this._arySources; }, set_arySources: function (v) { if (this._arySources !== v) this._arySources = v; },
    get_strSearchCompanyName: function () { return this._strSearchCompanyName; }, set_strSearchCompanyName: function (v) { if (this._strSearchCompanyName !== v) this._strSearchCompanyName = v; },
    //[003] code start
    get_intQSInvoiceID: function () { return this._intQSInvoiceID; }, set_intQSInvoiceID: function (v) { if (this._intQSInvoiceID !== v) this._intQSInvoiceID = v; },
    //[003] code end
    get_ctlSelectFromClientInvoice: function () { return this._ctlSelectFromClientInvoice; }, set_ctlSelectFromClientInvoice: function (v) { if (this._ctlSelectFromClientInvoice !== v) this._ctlSelectFromClientInvoice = v; },
    get_trSelectFromClientInvoice: function () { return this._trSelectFromClientInvoice; }, set_trSelectFromClientInvoice: function (v) { if (this._trSelectFromClientInvoice !== v) this._trSelectFromClientInvoice = v; },

    initialize: function () {
        Rebound.GlobalTrader.Site.Controls.Forms.CreditAdd_Add.callBaseMethod(this, "initialize");
        this.addShown(Function.createDelegate(this, this.formShown));
    },

    dispose: function () {
        if (this.isDisposed) return;
        if (this._ibtnContinue) $R_IBTN.clearHandlers(this._ibtnContinue);
        if (this._ibtnContinue_Footer) $R_IBTN.clearHandlers(this._ibtnContinue_Footer);
        if (this._ibtnSend) $R_IBTN.clearHandlers(this._ibtnSend);
        if (this._ibtnSend_Footer) $R_IBTN.clearHandlers(this._ibtnSend_Footer);
        if (this._ctlSelectFromInvoice) this._ctlSelectFromInvoice.dispose();
        if (this._ctlSelectFromCRMA) this._ctlSelectFromCRMA.dispose();
        if (this._ctlMultiStep) this._ctlMultiStep.dispose();
        if (this._ctlMail) this._ctlMail.dispose();
        if (this._ctlSelectFromClientInvoice) this._ctlSelectFromClientInvoice.dispose();
        this._intCompanyID = null;
        this._strCompanyName = null;
        this._intLoginID = null;
        this._intContactID = null;
        this._strContactName = null;
        this._ibtnSend = null;
        this._ibtnSend_Footer = null;
        this._ibtnContinue = null;
        this._ibtnContinue_Footer = null;
        this._radSelectSource = null;
        this._ctlSelectFromInvoice = null;
        this._trSelectFromInvoice = null;
        this._ctlSelectFromCRMA = null;
        this._trSelectFromCRMA = null;
        this._lblCurrency_Freight = null;
        this._arySources = null;
        this._intNewID = null;
        this._intCompanyID = null;
        this._intLoginID = null;
        this._intContactID = null;
        this._intDivisionID = null;
        this._intDivisionHeaderNo = null;
        this._intInvoiceID = null;
        this._intCRMAID = null;
        this._intSalesmanID = null;
        this._intCurrencyID = null;
        this._intTaxID = null;
        this._strCompanyName = null;
        this._strReferenceDate = null;
        this._strSearchCompanyName = null;
        this._ctlMail = null;
        //[003] code start
        this._intQSInvoiceID = null;
        //[003] code end
        this._isClientInvoice = null;
        this._ctlSelectFromClientInvoice = null;
        this._trSelectFromClientInvoice = null;
        this._intClientInvoiceLineNo = null;
        Rebound.GlobalTrader.Site.Controls.Forms.CreditAdd_Add.callBaseMethod(this, "dispose");
    },

    formShown: function () {
        if (this._blnFirstTimeShown) {
            $find(this.getField("ctlShipVia").ControlID).addChanged(Function.createDelegate(this, this.getShipVia));
            this._ctlMail = $find(this.getField("ctlSendMailMessage").ID);
            this._ctlMail._ctlRelatedForm = this;
            this.addCancel(Function.createDelegate(this, this.cancelClicked));
            this.addSave(Function.createDelegate(this, this.saveClicked));
            this._ctlMultiStep.addStepChanged(Function.createDelegate(this, this.stepChanged));
            this._ctlSelectFromInvoice.addItemSelected(Function.createDelegate(this, this.selectInvoice));
            this._ctlSelectFromClientInvoice.addItemSelected(Function.createDelegate(this, this.selectClientInvoice));
            this._ctlSelectFromCRMA.addItemSelected(Function.createDelegate(this, this.selectCRMA));
            this.addFieldCheckBoxClickEvent("ctlSendMail", Function.createDelegate(this, this.chooseIfSendMail));
            var fnContinue = Function.createDelegate(this, this.continueClicked);
            $R_IBTN.addClick(this._ibtnContinue, fnContinue);
            $R_IBTN.addClick(this._ibtnContinue_Footer, fnContinue);
            var fnSend = Function.createDelegate(this, this.sendMail);
            $R_IBTN.addClick(this._ibtnSend, fnSend);
            $R_IBTN.addClick(this._ibtnSend_Footer, fnSend);
            $find(this.getField("ctlSalesman2").ControlID).addChanged(Function.createDelegate(this, this.changedSalesman2));
            //[002] code start
            this._ctlSelectFromInvoice._ShipExported = 1;
            //[002] code end
        }
        if (this._strSearchCompanyName) {
            this._ctlSelectFromInvoice.setFieldValue("ctlCompany", this._strSearchCompanyName);
            this._ctlSelectFromCRMA.setFieldValue("ctlCompany", this._strSearchCompanyName);
            // this._ctlSelectFromClientInvoice.setFieldValue("ctlCompany", this._strSearchCompanyName);
        }
        this.resetSteps();
        //[003] code start
        if (this._intQSInvoiceID != null && this._intQSInvoiceID > 0) {
            this.getInvoiceFromInvoiceDetail();
        }
        else {
            this.gotoStep(1);
        }
        //[003] code end
    },

    continueClicked: function () {
        switch (this._ctlMultiStep._intCurrentStep) {
            case 1:
                this._strSourceSelected = this.findWhichTypeSelected();
                this.gotoStep(2);
                break;
            case 4:
                this.finishedForm();
                break;
        }
    },

    findWhichTypeSelected: function () {
        for (var i = 0; i < this._arySources.length; i++) {
            var rad = $get(String.format("{0}_{1}", this._radSelectSource.id, i));
            if (rad.checked) return this._arySources[i];
        }
    },

    setSourceSelectRadio: function () {
        for (var i = 0; i < 2; i++) {
            var rad = $get(String.format("{0}_{1}", this._radSelectSource.id, i));
            switch (i) {
                case 0: rad.checked = (this._strSourceSelected == "INVOICE"); break;
                case 1: rad.checked = (this._strSourceSelected == "CRMA"); break;
                case 2: rad.checked = (this._strSourceSelected == "CLIENTINVOICE"); break;
            }
        }
    },

    cancelClicked: function () {
        $R_FN.navigateBack();
    },

    stepChanged: function () {
        var intStep = this._ctlMultiStep._intCurrentStep;
        $R_IBTN.showButton(this._ibtnSend, intStep == 4);
        $R_IBTN.showButton(this._ibtnSend_Footer, intStep == 4);
        $R_IBTN.enableButton(this._ibtnSave, intStep == 3);
        $R_IBTN.enableButton(this._ibtnSave_Footer, intStep == 3);
        $R_IBTN.showButton(this._ibtnSave, intStep != 4);
        $R_IBTN.showButton(this._ibtnSave_Footer, intStep != 4);
        $R_IBTN.showButton(this._ibtnCancel, intStep != 4);
        $R_IBTN.showButton(this._ibtnCancel_Footer, intStep != 4);
        this._ctlMultiStep.showSteps(intStep != 4);
        this._ctlMultiStep.showExplainLabel(intStep != 2);
        var blnShowContinue = (intStep == 1);
        $R_IBTN.showButton(this._ibtnContinue, blnShowContinue);
        $R_IBTN.showButton(this._ibtnContinue_Footer, blnShowContinue);
        $R_FN.showElement(this._trSelectFromInvoice, (intStep == 2 && this._strSourceSelected == "INVOICE"));
        $R_FN.showElement(this._trSelectFromCRMA, (intStep == 2 && this._strSourceSelected == "CRMA"));
        $R_FN.showElement(this._trSelectFromClientInvoice, (intStep == 2 && this._strSourceSelected == "CLIENTINVOICE"));
        if (intStep == 2) {
            //[001] code start
            this.getFieldDropDownData("ctlIncoterm");
            //[001] code end
            //[004] code start
            this.getFieldDropDownData("ctlCreditNoteBankFee");
            //[004] code end
            if (this._strSourceSelected == "INVOICE") this._ctlSelectFromInvoice.resizeColumns();
            if (this._strSourceSelected == "CRMA") this._ctlSelectFromCRMA.resizeColumns();
            if (this._strSourceSelected == "CLIENTINVOICE") this._ctlSelectFromClientInvoice.resizeColumns();
            //do preselect on Company ID if we have one
            if (this._intCompanyID > 0) {
                if (this._strSourceSelected == "INVOICE") {
                    if (this._strCompanyName) this._ctlSelectFromInvoice.setFieldValue("ctlCompany", $R_FN.setCleanTextValue(this._strCompanyName));
                    if (this._strContactName) this._ctlSelectFromInvoice.setFieldValue("ctlContact", $R_FN.setCleanTextValue(this._strContactName));

                    this._ctlSelectFromInvoice.getData();
                }
                else if (this._strSourceSelected == "CLIENTINVOICE") {
                    // if (this._strCompanyName) this._ctlSelectFromClientInvoice.setFieldValue("ctlCompany", $R_FN.setCleanTextValue(this._strCompanyName));
                    if (this._strContactName) this._ctlSelectFromClientInvoice.setFieldValue("ctlContact", $R_FN.setCleanTextValue(this._strContactName));
                    this._isClientInvoice = true;
                    this._ctlSelectFromClientInvoice.getData();
                }
                else {
                    if (this._strCompanyName) this._ctlSelectFromCRMA.setFieldValue("ctlCompany", $R_FN.setCleanTextValue(this._strCompanyName));
                    if (this._strContactName) this._ctlSelectFromCRMA.setFieldValue("ctlContact", $R_FN.setCleanTextValue(this._strContactName));
                    this._ctlSelectFromCRMA.getData();
                }
            }

            this.showFormField("ctlAuthorisedBy", this._strSourceSelected == "CRMA");
            this.showFormField("ctlCRMANumber", this._strSourceSelected == "CRMA");
        }
        if (intStep == 4) {
            this.getMessageText();
            this.setFieldValue("ctlSendMail", false);
            this.showMailButtons();
        }
    },

    selectClientInvoice: function () {
        this._intInvoiceID = this._ctlSelectFromClientInvoice.getSelectedID();
        this._intClientInvoiceLineNo = this._ctlSelectFromClientInvoice._tblResults.getSelectedExtraData().ClientInvLineNo;
       // alert(this._intClientInvoiceLineNo);
        this.getClientInvoice();
        this.gotoStep(3);
    },

    getClientInvoice: function () {
        this._isClientInvoice = true;
        this.showClientInvoiceFieldsLoading(true);
        $R_FN.showElement(this._pnlLines, false);
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/ClientInvoiceMainInfo");
        obj.set_DataObject("ClientInvoiceMainInfo");
        obj.set_DataAction("GetDataItem");
        obj.addParameter("ID", this._intInvoiceID);
        obj.addParameter("LineNo", this._intClientInvoiceLineNo);
        obj.addDataOK(Function.createDelegate(this, this.getClientInvoiceOK));
        obj.addError(Function.createDelegate(this, this.getClientInvoiceError));
        obj.addTimeout(Function.createDelegate(this, this.getClientInvoiceError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    getClientInvoiceOK: function (args) {
        var res = args._result;
        if (!res) return;
        //[003] code start
        this.checkCreateCreditNote(res.Exported, res.ClientInvoiceNumber)
        //[003] code end
        this.setFieldValue("ctlCompany", $R_FN.setCleanTextValue(res.SupplierName));
        this.setFieldValue("ctlClientInvoiceNumber", res.ClientInvoiceNumber);
        this.setFieldValue("ctlContact", res.Contact);
        var strSalesman = $R_FN.setCleanTextValue(res.Salesman);
        if (res.Salesman2) strSalesman += ", " + $R_FN.setCleanTextValue(res.Salesman2);
        this.setFieldValue("ctlSalesman", $R_FN.setCleanTextValue(res.Salesman));
        this.setFieldValue("ctlSalesman2", res.Salesman2No);
        this.setFieldValue("ctlSalesman2Percent", res.Salesman2Percent);
        this.setFieldValue("ctlCurrency", $R_FN.setCleanTextValue(res.CurrencyCode));
        this.setFieldValue("ctlTax", $R_FN.setCleanTextValue(res.TaxName));
        this.setFieldValue("ctlDivision", $R_FN.setCleanTextValue(res.DivisionName));
        this.setFieldValue("ctlDivisionHeader", $R_FN.setCleanTextValue(res.DivisionHeaderName));
        this.setFieldValue("ctlShippingCost", res.ShippingCostVal);
        this.setFieldValue("ctlFreight", res.FreightVal);
        this.setFieldValue("ctlCreditDate", $R_FN.shortDate());
        this.setFieldValue("ctlReferenceDate", res.ClientInvoiceDate);
        this.setFieldValue("ctlRaisedBy", this._intLoginID);
        this.setFieldValue("ctlShipVia", res.ShipViaNo);
        //  this.setFieldValue("ctlCustomerPO", res.CustomerPO);
        this.setFieldValue("ctlAccount", res.ShippingAccountNo);
        //[001] code start
        this.setFieldValue("ctlIncoterm", res.IncotermNo);
        //[001] code end
        //[004] code start
        this.setFieldValue("ctlCreditNoteBankFee", res.CreditNoteBankFee);
        //[004] code end
        this.getFieldDropDownData("ctlSalesman2");
        this.getFieldDropDownData("ctlShipVia");
        this.getFieldDropDownData("ctlRaisedBy");
        this._intCompanyID = res.CustomerNo;
        this._intContactID = res.ContactNo;
        this._intDivisionID = res.DivisionNo;
        this._intTaxID = res.TaxNo;
        this._intSalesmanID = res.SalesmanNo;
        this._intCurrencyID = res.CurrencyNo;
        this._strReferenceDate = res.InvoiceDateRaw;
        this._intDivisionHeaderNo = res.DivisionHeaderNo;
        $R_FN.setInnerHTML(this._lblCurrency_Freight, res.CurrencyCode);
        this.showClientInvoiceFieldsLoading(false);

    },

    getClientInvoiceError: function (args) {
        this.showClientInvoiceFieldsLoading(false);
        this._strErrorMessage = args._errorMessage;
        this.onSaveError();
    },

    showClientInvoiceFieldsLoading: function (bln) {

        this.showField("ctlContact", false);
        this.showField("ctlSalesman2", false);
        this.showField("ctlSalesman2Percent", false);
        this.showField("ctlShippingCost", false);
        this.showField("ctlShipVia", false);
        this.showField("ctlAccount", false);
        this.showField("ctlClientInvoiceNumber", true);
        this.showField("ctlInvoiceNumber", false);

        this.showFieldLoading("ctlCompany", bln);
        this.showFieldLoading("ctlInvoiceNumber", bln);
        this.showFieldLoading("ctlContact", bln);
        this.showFieldLoading("ctlSalesman", bln);
        this.showFieldLoading("ctlSalesman2", bln);
        this.showFieldLoading("ctlCurrency", bln);
        this.showFieldLoading("ctlTax", bln);
        this.showFieldLoading("ctlDivision", bln);
        this.showFieldLoading("ctlAccount", bln);
        this.showFieldLoading("ctlShippingCost", bln);
        this.showFieldLoading("ctlFreight", bln);
        this.showFieldLoading("ctlRaisedBy", bln);
        this.showFieldLoading("ctlShipVia", bln);
        this.showFieldLoading("ctlCustomerPO", bln);
        this.showFieldLoading("ctlAccount", bln);
    },

    selectInvoice: function () {
        this._intInvoiceID = this._ctlSelectFromInvoice.getSelectedID();
        this.getInvoice();
        this.gotoStep(3);
    },

    getInvoice: function () {
        this.showInvoiceFieldsLoading(true);
        $R_FN.showElement(this._pnlLines, false);
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/InvoiceMainInfo");
        obj.set_DataObject("InvoiceMainInfo");
        obj.set_DataAction("GetData");
        obj.addParameter("ID", this._intInvoiceID);
        obj.addDataOK(Function.createDelegate(this, this.getInvoiceOK));
        obj.addError(Function.createDelegate(this, this.getInvoiceError));
        obj.addTimeout(Function.createDelegate(this, this.getInvoiceError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    getInvoiceOK: function (args) {
        var res = args._result;
        if (!res) return;
        //[003] code start
        this.checkCreateCreditNote(res.Exported, res.InvoiceNumber)
        //[003] code end
        this.setFieldValue("ctlCompany", $R_FN.setCleanTextValue(res.Customer));
        this.setFieldValue("ctlInvoiceNumber", res.InvoiceNumber);
        this.setFieldValue("ctlContact", res.Contact);
        var strSalesman = $R_FN.setCleanTextValue(res.Salesman);
        if (res.Salesman2) strSalesman += ", " + $R_FN.setCleanTextValue(res.Salesman2);
        this.setFieldValue("ctlSalesman", $R_FN.setCleanTextValue(res.Salesman));
        this.setFieldValue("ctlSalesman2", res.Salesman2No);
        this.setFieldValue("ctlSalesman2Percent", res.Salesman2Percent);
        this.setFieldValue("ctlCurrency", $R_FN.setCleanTextValue(res.Currency));
        this.setFieldValue("ctlTax", $R_FN.setCleanTextValue(res.TaxName));
        this.setFieldValue("ctlDivision", $R_FN.setCleanTextValue(res.DivisionName));
        this.setFieldValue("ctlDivisionHeader", $R_FN.setCleanTextValue(res.DivisionHeaderName));
        this.setFieldValue("ctlShippingCost", res.ShippingCostVal);
        this.setFieldValue("ctlFreight", res.FreightVal);
        this.setFieldValue("ctlCreditDate", $R_FN.shortDate());
        this.setFieldValue("ctlReferenceDate", res.InvoiceDate);
        this.setFieldValue("ctlRaisedBy", this._intLoginID);
        this.setFieldValue("ctlShipVia", res.ShipViaNo);
        this.setFieldValue("ctlCustomerPO", res.CustomerPO);
        this.setFieldValue("ctlAccount", res.ShippingAccountNo);
        //[001] code start
        this.setFieldValue("ctlIncoterm", res.IncotermNo);
        //[001] code end
        //[004] code start
        this.setFieldValue("ctlCreditNoteBankFee", res.CreditNoteBankFee);
        //[004] code end
        this.getFieldDropDownData("ctlSalesman2");
        this.getFieldDropDownData("ctlShipVia");
        this.getFieldDropDownData("ctlRaisedBy");
        this._intCompanyID = res.CustomerNo;
        this._intContactID = res.ContactNo;
        this._intDivisionID = res.DivisionNo;
        this._intTaxID = res.TaxNo;
        this._intSalesmanID = res.SalesmanNo;
        this._intCurrencyID = res.CurrencyNo;
        this._strReferenceDate = res.InvoiceDateRaw;
        this._intDivisionHeaderNo = res.DivisionHeaderNo;
        $R_FN.setInnerHTML(this._lblCurrency_Freight, res.CurrencyCode);
        this.showInvoiceFieldsLoading(false);

    },

    getInvoiceError: function (args) {
        this.showInvoiceFieldsLoading(false);
        this._strErrorMessage = args._errorMessage;
        this.onSaveError();
    },

    showInvoiceFieldsLoading: function (bln) {
        this.showFieldLoading("ctlCompany", bln);
        this.showFieldLoading("ctlInvoiceNumber", bln);
        this.showFieldLoading("ctlContact", bln);
        this.showFieldLoading("ctlSalesman", bln);
        this.showFieldLoading("ctlSalesman2", bln);
        this.showFieldLoading("ctlCurrency", bln);
        this.showFieldLoading("ctlTax", bln);
        this.showFieldLoading("ctlDivision", bln);
        this.showFieldLoading("ctlAccount", bln);
        this.showFieldLoading("ctlShippingCost", bln);
        this.showFieldLoading("ctlFreight", bln);
        this.showFieldLoading("ctlRaisedBy", bln);
        this.showFieldLoading("ctlShipVia", bln);
        this.showFieldLoading("ctlCustomerPO", bln);
        this.showFieldLoading("ctlAccount", bln);
        this.showField("ctlClientInvoiceNumber", false);
        this.showField("ctlInvoiceNumber", true);
    },

    selectCRMA: function () {
        this._intCRMAID = this._ctlSelectFromCRMA.getSelectedID();
        this.getCRMA();
        this.gotoStep(3);
    },

    getCRMA: function () {
        this.showCRMAFieldsLoading(true);
        $R_FN.showElement(this._pnlLines, false);
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/CreditAdd");
        obj.set_DataObject("CreditAdd");
        obj.set_DataAction("GetCRMA");
        obj.addParameter("ID", this._intCRMAID);
        obj.addDataOK(Function.createDelegate(this, this.getCRMAOK));
        obj.addError(Function.createDelegate(this, this.getCRMAError));
        obj.addTimeout(Function.createDelegate(this, this.getCRMAError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    getCRMAOK: function (args) {
        var res = args._result;
        this._intCompanyID = res.CustomerNo;
        this._intContactID = res.ContactNo;
        this._intDivisionID = res.DivisionNo;
        this._intDivisionHeaderNo = res.DivisionHeaderNo;
        this._intTaxID = res.TaxNo;
        this._intSalesmanID = res.SalesmanNo;
        this._intCurrencyID = res.CurrencyNo;
        this._intInvoiceID = res.InvoiceNo;
        this._strReferenceDate = res.InvoiceDateRaw;
        this.setFieldValue("ctlCompany", $R_FN.setCleanTextValue(res.Customer));
        this.setFieldValue("ctlInvoiceNumber", res.Invoice);
        this.setFieldValue("ctlCRMANumber", res.CRMANumber);
        this.setFieldValue("ctlContact", $R_FN.setCleanTextValue(res.Contact));
        this.setFieldValue("ctlSalesman", $R_FN.setCleanTextValue(res.Salesman));
        this.setFieldValue("ctlSalesman2", res.Salesman2);
        this.setFieldValue("ctlSalesman2Percent", "");
        if (res.Salesman2Percent > 0) this.setFieldValue("ctlSalesman2Percent", res.Salesman2Percent);
        this.setFieldValue("ctlAuthorisedBy", $R_FN.setCleanTextValue(res.Authoriser));
        this.setFieldValue("ctlCurrency", $R_FN.setCleanTextValue(res.Currency));
        this.setFieldValue("ctlTax", $R_FN.setCleanTextValue(res.Tax));
        this.setFieldValue("ctlDivision", $R_FN.setCleanTextValue(res.Division));
        this.setFieldValue("ctlDivisionHeader", $R_FN.setCleanTextValue(res.DivisionHeaderName));
        this.setFieldValue("ctlAccount", $R_FN.setCleanTextValue(res.ShippingAccountNo));
        this.setFieldValue("ctlCreditDate", $R_FN.shortDate());
        this.setFieldValue("ctlReferenceDate", res.InvoiceDate);
        this.setFieldValue("ctlRaisedBy", this._intLoginID);
        this.setFieldValue("ctlCustomerPO", $R_FN.setCleanTextValue(res.CustomerPO));
        this.setFieldValue("ctlShippingCost", res.ShippingCostVal);
        this.setFieldValue("ctlFreight", res.FreightVal);
        this.setFieldValue("ctlShipVia", res.ShipViaNo);
        //[001] code start
        this.setFieldValue("ctlIncoterm", res.IncotermNo);
        //[001] code end
        //[004] code start
        this.setFieldValue("ctlCreditNoteBankFee", res.CreditNoteBankFee);
        //[004] code end
        this.showCRMAFieldsLoading(false);
        this.getFieldDropDownData("ctlShipVia");
        this.getFieldDropDownData("ctlRaisedBy");
        this.getFieldDropDownData("ctlSalesman2");
        $R_FN.setInnerHTML(this._lblCurrency_Freight, res.CurrencyCode);
    },

    getCRMAError: function (args) {
        this.showCRMAFieldsLoading(false);
        this._strErrorMessage = args._errorMessage;
        this.onSaveError();
    },

    showCRMAFieldsLoading: function (bln) {
        this.showFieldLoading("ctlCompany", bln);
        this.showFieldLoading("ctlInvoiceNumber", bln);
        this.showFieldLoading("ctlCRMANumber", bln);
        this.showFieldLoading("ctlContact", bln);
        this.showFieldLoading("ctlSalesman", bln);
        this.showFieldLoading("ctlSalesman2", bln);
        this.showFieldLoading("ctlAuthorisedBy", bln);
        this.showFieldLoading("ctlCurrency", bln);
        this.showFieldLoading("ctlTax", bln);
        this.showFieldLoading("ctlDivision", bln);
        this.showFieldLoading("ctlAccount", bln);
        this.showFieldLoading("ctlShippingCost", bln);
        this.showFieldLoading("ctlFreight", bln);
        this.showFieldLoading("ctlReferenceDate", bln);
        this.showFieldLoading("ctlRaisedBy", bln);
        this.showFieldLoading("ctlShipVia", bln);
        this.showFieldLoading("ctlCustomerPO", bln);
        this.showField("ctlClientInvoiceNumber", false);
        this.showField("ctlInvoiceNumber", true);
    },

    saveClicked: function () {
        if (!this.validateForm()) return;
        this.showSaving(true);
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/CreditAdd");
        obj.set_DataObject("CreditAdd");
        obj.set_DataAction("AddNew");
        obj.addParameter("RaisedBy", this.getFieldValue("ctlRaisedBy"));
        obj.addParameter("ShipViaNo", this.getFieldValue("ctlShipVia"));
        obj.addParameter("Notes", this.getFieldValue("ctlNotes"));
        obj.addParameter("Instructions", this.getFieldValue("ctlInstructions"));
        obj.addParameter("Account", this.getFieldValue("ctlAccount"));
        obj.addParameter("ReferenceDate", this._strReferenceDate);
        obj.addParameter("CreditDate", this.getFieldValue("ctlCreditDate"));
        obj.addParameter("ShippingCost", this.getFieldValue("ctlShippingCost"));
        obj.addParameter("Freight", this.getFieldValue("ctlFreight"));
        obj.addParameter("TaxNo", this._intTaxID);
        obj.addParameter("InvoiceNo", this._intInvoiceID);
        obj.addParameter("SalesOrderNo", this._intSalesOrderID);
        obj.addParameter("CustomerRMANo", this._intCRMAID);
        obj.addParameter("Salesman", this._intSalesmanID);
        obj.addParameter("CurrencyNo", this._intCurrencyID);
        obj.addParameter("CMNo", this._intCompanyID);
        obj.addParameter("ContactNo", this._intContactID);
        obj.addParameter("DivisionNo", this._intDivisionID);
        obj.addParameter("CustomerPO", this.getFieldValue("ctlCustomerPO"));
        obj.addParameter("CustomerReturn", this.getFieldValue("ctlCustomerReturn"));
        obj.addParameter("CustomerDebit", this.getFieldValue("ctlCustomerDebit"));
        obj.addParameter("Salesman2No", this.getFieldValue("ctlSalesman2"));
        obj.addParameter("Salesman2Percent", this.getFieldValue("ctlSalesman2Percent"));
        //[001] code start
        obj.addParameter("Incoterm", this.getFieldValue("ctlIncoterm"));
        //[001] code end
        //[004] code start
        obj.addParameter("CreditNoteBankFee", this.getFieldValue("ctlCreditNoteBankFee"));
        obj.addParameter("isClientInvoice", this._isClientInvoice);
        obj.addParameter("ClientInvLineNo", this._intClientInvoiceLineNo);
        obj.addParameter("DivisionHeaderNo", this._intDivisionHeaderNo);
        
        

        //[004] code end
        obj.addDataOK(Function.createDelegate(this, this.saveEditComplete));
        obj.addError(Function.createDelegate(this, this.saveEditError));
        obj.addTimeout(Function.createDelegate(this, this.saveEditError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    saveEditError: function (args) {
        this._strErrorMessage = args._errorMessage;
        this.onSaveError();
    },

    saveEditComplete: function (args) {
        if (args._result.NewID > 0) {
            this._intNewID = args._result.NewID;
            this.showSaving(false);
            this.showInnerContent(true);
            this.nextStep();
        } else {
            this._strErrorMessage = args._errorMessage;
            this.onSaveError();
        }
    },

    validateForm: function () {
        this.onValidate();
        var blnOK = true;
        if (this._ctlMultiStep._intCurrentStep == 3) {
            if (!this.checkFieldEntered("ctlRaisedBy")) blnOK = false;
            if (!this.checkFieldEntered("ctlCreditDate")) blnOK = false;
            //[001] code start
            if (!this.checkFieldEntered("ctlIncoterm")) blnOK = false;
            //[001] code end

        }
        if (this._ctlMultiStep._intCurrentStep == 4) {
            if (!this._ctlMail.validateFields()) blnOK = false;
        }
        if (!blnOK) this.showError(true);
        return blnOK;
    },

    getShipVia: function () {
        this.showShipViaFieldsLoading(true);
        if (!this.checkFieldEntered("ctlShipVia")) {
            this.getShipViaError();
            return;
        }
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/CreditAdd");
        obj.set_DataObject("CreditAdd");
        obj.set_DataAction("GetShipVia");
        obj.addParameter("ID", this.getFieldValue("ctlShipVia"));
        obj.addParameter("CreditCurrencyNo", this._intCurrencyID);
        obj.addParameter("CreditDate", this.getFieldValue("ctlReferenceDate"));
        obj.addDataOK(Function.createDelegate(this, this.getShipViaOK));
        obj.addError(Function.createDelegate(this, this.getShipViaError));
        obj.addTimeout(Function.createDelegate(this, this.getShipViaError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    getShipViaOK: function (args) {
        var res = args._result;
        this.setFieldValue("ctlShippingCost", res.Cost);
        this.setFieldValue("ctlFreight", res.Charge);
        this.showShipViaFieldsLoading(false);
    },

    getShipViaError: function () {
        this.setFieldValue("ctlShippingCost", $R_FN.formatCurrency(0, "", 2));
        this.setFieldValue("ctlFreight", $R_FN.formatCurrency(0, "", 2));
        this.showShipViaFieldsLoading(false);
    },

    showShipViaFieldsLoading: function (bln) {
        this.showFieldLoading("ctlShippingCost", bln);
        this.showFieldLoading("ctlFreight", bln);
    },

    changedSalesman2: function () {
        if ($find(this.getField("ctlSalesman2").ControlID).isSetAsNoValue()) {
            this.setFieldValue("ctlSalesman2Percent", "");
        } else {
            if (this.getFieldValue("ctlSalesman2Percent") == 0) this.setFieldValue("ctlSalesman2Percent", 50);
        }
    },

    showMailButtons: function () {
        var bln = this.getFieldValue("ctlSendMail");
        this.showField("ctlSendMailMessage", bln);
        $R_IBTN.showButton(this._ibtnSend, bln);
        $R_IBTN.showButton(this._ibtnSend_Footer, bln);
        $R_IBTN.showButton(this._ibtnContinue, !bln);
        $R_IBTN.showButton(this._ibtnContinue_Footer, !bln);
    },

    chooseIfSendMail: function () {
        this.showMailButtons();
    },

    getMessageText: function () {
        Rebound.GlobalTrader.Site.WebServices.GetMailMessage_NewCreditNote(this._intNewID, Function.createDelegate(this, this.getMessageTextComplete));
    },

    getMessageTextComplete: function (strMsg) {
        this._ctlMail.setValue_Body(strMsg);
        this._ctlMail.setValue_Subject($R_RES.NewCreditNoteAdded);
    },

    validateMailForm: function () {
        var blnOK = this._ctlMail.validateFields();
        if (!blnOK) this.showError(true);
        return blnOK;
    },

    sendMail: function () {
        if (!this.validateMailForm()) return;
        Rebound.GlobalTrader.Site.WebServices.SendMessage($R_FN.arrayToSingleString(this._ctlMail._aryRecipientLoginIDs), $R_FN.arrayToSingleString(this._ctlMail._aryRecipientGroupIDs), this._ctlMail.getValue_Subject(), this._ctlMail.getValue_Body(), this._intNewID, Function.createDelegate(this, this.sendMailComplete));
        $R_IBTN.showButton(this._ibtnSave, false);
        $R_IBTN.showButton(this._ibtnSave_Footer, false);
        $R_IBTN.showButton(this._ibtnSend, false);
        $R_IBTN.showButton(this._ibtnSend_Footer, false);
    },

    sendMailComplete: function () {
        this.finishedForm();
    },

    finishedForm: function () {
        this._ctlMultiStep.showExplainLabel(false);
        this._ctlMultiStep.showSteps(false);
        $R_IBTN.showButton(this._ibtnSave, false);
        $R_IBTN.showButton(this._ibtnSave_Footer, false);
        $R_IBTN.showButton(this._ibtnSend, false);
        $R_IBTN.showButton(this._ibtnSend_Footer, false);
        this.showSavedOK(true);
        this.onSaveComplete();
    },
    //[003] code start
    getInvoiceFromInvoiceDetail: function () {
        this._intInvoiceID = this._intQSInvoiceID;
        this.getInvoice();
    },
    checkCreateCreditNote: function (exported, intInvoiceNo) {
        this.getFieldDropDownData("ctlIncoterm");
        if ((this._intQSInvoiceID != null) && (this._intQSInvoiceID > 0)) {
            if (!exported) {
                this.gotoStep(2);
                $R_FN.showElement(this._trSelectFromInvoice, true);
                $R_FN.showElement(this._trSelectFromCRMA, false);
                this._ctlSelectFromInvoice.setFieldValue("ctlInvoiceNo", intInvoiceNo);
                this._ctlSelectFromInvoice.getData();
            }
            else {
                this.gotoStep(3);
            }
        }
    }
    //[003] code end
};

Rebound.GlobalTrader.Site.Controls.Forms.CreditAdd_Add.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.CreditAdd_Add", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
