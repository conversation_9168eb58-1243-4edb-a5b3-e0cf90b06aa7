﻿/*===========================================================================================  
TASK			UPDATED BY       DATE				ACTION		DESCRIPTION  
[US-230712]     An.TranTan		 20-Jan-2025		UPDATE		Add new permissions for HUBRFQ - Export/ Import Sourcing function permission
===========================================================================================  
*/
DECLARE @NewSecurityFunction TABLE
(
	SecurityFunctionId INT
	,FunctionName NVARCHAR(300)
	,Description NVARCHAR(600)
	,SitePageNo INT
	,SiteSectionNo INT
	,ReportNo INT
	,UpdatedBy INT 
	,DLUP DATETIME 
	,InitiallyProhibitedForNewLogins BIT
	,DisplaySortOrder INT
)
INSERT INTO @NewSecurityFunction
(
	SecurityFunctionId,
    FunctionName,
    Description,
    SitePageNo,
    SiteSectionNo,
    ReportNo,
    UpdatedBy,
    DLUP,
    InitiallyProhibitedForNewLogins,
    DisplaySortOrder
)VALUES
(
	20010034
	,'Orders_BOMDetail_Import_Export_SourcingResult'
	,'Allow exporting HUBRFQ and importing Sourcing'
	,2001107		--SitePageNo
	,2				--SiteSectionNo
	,NULL			--ReportNo
	,1				--UpdatedBy
	,GETDATE()		--DLUP
	,1				--InitiallyProhibitedForNewLogins
	,(SELECT (ISNULL(MAX(DisplaySortOrder),0) + 1) FROM tbSecurityFunction WHERE SiteSectionNo = 2 AND SitePageNo = 2000101)
)


INSERT INTO tbSecurityFunction (
	SecurityFunctionId,
    FunctionName,
    Description,
    SitePageNo,
    SiteSectionNo,
    ReportNo,
    UpdatedBy,
    DLUP,
    InitiallyProhibitedForNewLogins,
    DisplaySortOrder
)
SELECT 
	nsf.SecurityFunctionId,
    nsf.FunctionName,
    nsf.Description,
    nsf.SitePageNo,
    nsf.SiteSectionNo,
    nsf.ReportNo,
    nsf.UpdatedBy,
    nsf.DLUP,
    nsf.InitiallyProhibitedForNewLogins,
    nsf.DisplaySortOrder
FROM @NewSecurityFunction nsf
LEFT JOIN tbSecurityFunction sf ON sf.SecurityFunctionId = nsf.SecurityFunctionId
WHERE sf.SecurityFunctionId IS NULL	--insert if not exist ID

--set default value for new permissions
DELETE tbSecurityGroupSecurityFunctionPermission
WHERE SecurityFunctionNo IN (SELECT SecurityFunctionId FROM @NewSecurityFunction);

;WITH cte AS(
	SELECT	sg.SecurityGroupId,
			sf.SecurityFunctionId,
			CASE WHEN sg.Administrator = 1 AND sg.ClientNo = 114 THEN CAST(1 AS BIT) --allow admin for DMCC client
				ELSE CAST(0 AS BIT) 
			END AS IsAllowed
	FROM tbSecurityGroup sg, @NewSecurityFunction sf
)
INSERT INTO tbSecurityGroupSecurityFunctionPermission 
(  
	SecurityGroupNo  
    ,SecurityFunctionNo  
    ,IsAllowed  
    ,DLUP
) 
SELECT 
	SecurityGroupId,
	SecurityFunctionId,	
	IsAllowed,
	GETDATE()
FROM cte 
