﻿GO
/*
--===========================================================================================  
TASK			UPDATED BY       DATE				ACTION		DESCRIPTION  
[US-229094]     Phuc Hoang		 21-Jan-2025		CREATE		Quote - Add automatic Task Reminder based on Quote Status
=============================================================================================  
*/

IF NOT EXISTS(SELECT 1 FROM dbo.tbPowerApp_urls WITH(NOLOCK) WHERE FlowName = 'Quote_TaskReminder_Notification')
    BEGIN
        INSERT INTO [dbo].[tbPowerApp_urls]
           ([FlowName]
           ,[FlowUrl])
		VALUES
           ('Quote_TaskReminder_Notification'
           ,'https://gt-uat2-webapp-001.azurewebsites.net')
    END
GO
