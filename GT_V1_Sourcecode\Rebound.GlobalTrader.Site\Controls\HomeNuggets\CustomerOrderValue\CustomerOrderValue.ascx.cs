//----------------------------------------------------------------------------------------
//Marker  Date          Changed By     Remarks
//[001]   03/07/2018    <PERSON><PERSON><PERSON>    Add customer order value nugget on broker and sales tab
//----------------------------------------------------------------------------------------
using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site.Controls;

namespace Rebound.GlobalTrader.Site.Controls.HomeNuggets {
    public partial class CustomerOrderValue : Base
    {

        protected SimpleDataTable _tblCustomerOrder;

		/// <summary>
		/// On init
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
            this.HomePageNuggetType = "OrdersDueOut";
			base.OnInit(e);
			WireUpControls();
			SetupTables();
            AddScriptReference("Controls.HomeNuggets.CustomerOrderValue.CustomerOrderValue.js");
		}

		protected override void OnLoad(EventArgs e) {
            _scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.HomeNuggets.CustomerOrderValue", this.ctlDesignBase.ClientID);
            _scScriptControlDescriptor.AddComponentProperty("tblCustomerOrder", _tblCustomerOrder.ClientID);
			base.OnLoad(e);
		}

		private void SetupTables() {
            _tblCustomerOrder.Columns.Add(new SimpleDataColumn("CustomerName"));
            _tblCustomerOrder.Columns.Add(new SimpleDataColumn("TotalValue",Unit.Pixel(65)));
            _tblCustomerOrder.Columns.Add(new SimpleDataColumn("AvailableCredit", Unit.Pixel(75)));
		}

		private void WireUpControls() {
            _tblCustomerOrder = (SimpleDataTable)FindContentControl("tblCustomerOrder");
		}
	}
}