///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.CRMAReceiveLines_Confirm = function (element) {
    Rebound.GlobalTrader.Site.Controls.Forms.CRMAReceiveLines_Confirm.initializeBase(this, [element]);
    this._intLineID = -1;
    //locals
    this._intSelectedLineID = 0;
    this._intSelectedGoodsLineID = 0;
    this._ctlConfirm = null;
    this._intAllocationID = -1;
    this._intInvoiceLineID = -1;
};

Rebound.GlobalTrader.Site.Controls.Forms.CRMAReceiveLines_Confirm.prototype = {

    get_intGIID: function () { return this._intGIID; }, set_intGIID: function (v) { if (this._intGIID !== v) this._intGIID = v; },
    get_intLineID: function () { return this._intLineID; }, set_intLineID: function (value) { if (this._intLineID !== value) this._intLineID = value; },

    initialize: function () {
        Rebound.GlobalTrader.Site.Controls.Forms.CRMAReceiveLines_Confirm.callBaseMethod(this, "initialize");
        this.addShown(Function.createDelegate(this, this.formShown));      
    },

    dispose: function () {
        if (this.isDisposed) return;
        if (this._ctlConfirm) this._ctlConfirm.dispose();
        this._intSelectedGoodsLineID = null;
        this._intSelectedLineID = null;
        this._ctlConfirm = null;
        this._intAllocationID = null;
        Rebound.GlobalTrader.Site.Controls.Forms.CRMAReceiveLines_Confirm.callBaseMethod(this, "dispose");
    },

    formShown: function () {
        if (this._blnFirstTimeShown) {
            this._ctlConfirm = this.getFieldComponent("ctlConfirm");
            this._ctlConfirm.addClickYesEvent(Function.createDelegate(this, this.yesClicked));
            this._ctlConfirm.addClickNoEvent(Function.createDelegate(this, this.noClicked));
        }
    }, 

    yesClicked: function () {      
        this.deleteSerialBySOLine();
        this.onSaveComplete();
    },

    deleteSerialBySOLine: function () {
        // alert("sdasd");
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/CRMAReceivingLines");
        obj.set_DataObject("CRMAReceivingLines");
        obj.set_DataAction("DeattachCRMASerial");
        obj.addParameter("InvoiceLineNo", this._intInvoiceLineID);
        obj.addDataOK(Function.createDelegate(this, this.deleteSerialBySOLineOK));
        obj.addError(Function.createDelegate(this, this.deleteSerialBySOLineError));
        obj.addTimeout(Function.createDelegate(this, this.deleteSerialBySOLineError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },


    deleteSerialBySOLineOK: function (args) {
        this.showSaving(false);
        if (args._result.Result == true) {
            this.showSavedOK(true);
            this.onSaveComplete();
        }  
    },

    deleteSerialBySOLineError: function (args) {
        this.showSaving(false);
        this._strErrorMessage = args._errorMessage;
        this.onSaveError();
    },

    noClicked: function () {
        //this.onSaveComplete();
        this.onNotConfirmed();
    }

};

Rebound.GlobalTrader.Site.Controls.Forms.CRMAReceiveLines_Confirm.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.CRMAReceiveLines_Confirm", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
